	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc30160a -c99 --dep-file=mcal_src\\.Gtm_Irq.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Gtm_Irq.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Gtm_Irq.src ..\\mcal_src\\Gtm_Irq.c"
	.compiler_name		"ctc"
	.name	"Gtm_Irq"

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	37773
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_src\\Gtm_Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	176
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	182
	.byte	5,1,3
	.word	206
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	208
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	231
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	262
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	299
	.byte	4
	.byte	'boolean',0,2,105,29
	.word	231
	.byte	7
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,3,45,16,4,6
	.byte	'unsigned int',0,4,7,8
	.byte	'EN0',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'EN1',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'EN2',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'EN3',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'EN4',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'EN5',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'EN6',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'EN7',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'EN8',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'EN9',0,4
	.word	377
	.byte	1,22,2,35,0,8
	.byte	'EN10',0,4
	.word	377
	.byte	1,21,2,35,0,8
	.byte	'EN11',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'EN12',0,4
	.word	377
	.byte	1,19,2,35,0,8
	.byte	'EN13',0,4
	.word	377
	.byte	1,18,2,35,0,8
	.byte	'EN14',0,4
	.word	377
	.byte	1,17,2,35,0,8
	.byte	'EN15',0,4
	.word	377
	.byte	1,16,2,35,0,8
	.byte	'EN16',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'EN17',0,4
	.word	377
	.byte	1,14,2,35,0,8
	.byte	'EN18',0,4
	.word	377
	.byte	1,13,2,35,0,8
	.byte	'EN19',0,4
	.word	377
	.byte	1,12,2,35,0,8
	.byte	'EN20',0,4
	.word	377
	.byte	1,11,2,35,0,8
	.byte	'EN21',0,4
	.word	377
	.byte	1,10,2,35,0,8
	.byte	'EN22',0,4
	.word	377
	.byte	1,9,2,35,0,8
	.byte	'EN23',0,4
	.word	377
	.byte	1,8,2,35,0,8
	.byte	'EN24',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'EN25',0,4
	.word	377
	.byte	1,6,2,35,0,8
	.byte	'EN26',0,4
	.word	377
	.byte	1,5,2,35,0,8
	.byte	'EN27',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'EN28',0,4
	.word	377
	.byte	1,3,2,35,0,8
	.byte	'EN29',0,4
	.word	377
	.byte	1,2,2,35,0,8
	.byte	'EN30',0,4
	.word	377
	.byte	1,1,2,35,0,8
	.byte	'EN31',0,4
	.word	377
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN0_Bits',0,3,79,3
	.word	351
	.byte	7
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,3,82,16,4,8
	.byte	'reserved_0',0,4
	.word	377
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN1_Bits',0,3,85,3
	.word	924
	.byte	7
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,3,88,16,4,8
	.byte	'SEL0',0,4
	.word	377
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	377
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	377
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	377
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,3,95,3
	.word	1001
	.byte	7
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,3,98,16,4,8
	.byte	'SEL0',0,4
	.word	377
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	377
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	377
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	377
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,3,105,3
	.word	1155
	.byte	7
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,3,108,16,4,8
	.byte	'TO_ADDR',0,4
	.word	377
	.byte	20,12,2,35,0,8
	.byte	'TO_W1R0',0,4
	.word	377
	.byte	1,11,2,35,0,8
	.byte	'reserved_21',0,4
	.word	377
	.byte	11,0,2,35,0,0,4
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,3,113,3
	.word	1309
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,3,116,16,4,8
	.byte	'BRG_MODE',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'MSK_WR_RSP',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	6,24,2,35,0,8
	.byte	'MODE_UP_PGR',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'BUFF_OVL',0,4
	.word	377
	.byte	1,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'SYNC_INPUT_REG',0,4
	.word	377
	.byte	1,19,2,35,0,8
	.byte	'reserved_13',0,4
	.word	377
	.byte	3,16,2,35,0,8
	.byte	'BRG_RST',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'reserved_17',0,4
	.word	377
	.byte	7,8,2,35,0,8
	.byte	'BUFF_DPT',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,3,129,1,3
	.word	1437
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,3,132,1,16,4,8
	.byte	'NEW_TRAN_PTR',0,4
	.word	377
	.byte	5,27,2,35,0,8
	.byte	'FIRST_RSP_PTR',0,4
	.word	377
	.byte	5,22,2,35,0,8
	.byte	'TRAN_IN_PGR',0,4
	.word	377
	.byte	5,17,2,35,0,8
	.byte	'ABT_TRAN_PGR',0,4
	.word	377
	.byte	5,12,2,35,0,8
	.byte	'FBC',0,4
	.word	377
	.byte	6,6,2,35,0,8
	.byte	'RSP_TRAN_RDY',0,4
	.word	377
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,3,140,1,3
	.word	1744
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,3,143,1,16,4,8
	.byte	'TRAN_IN_PGR2',0,4
	.word	377
	.byte	5,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	377
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,3,147,1,3
	.word	1946
	.byte	7
	.byte	'_Ifx_GTM_CLC_Bits',0,3,150,1,16,4,8
	.byte	'DISR',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'DISS',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'EDIS',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_CLC_Bits',0,3,157,1,3
	.word	2059
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,3,160,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,3,164,1,3
	.word	2202
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,3,167,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'CLK6_SEL',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	377
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,3,172,1,3
	.word	2319
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,3,175,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'CLK7_SEL',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	377
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,3,180,1,3
	.word	2454
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,3,183,1,16,4,8
	.byte	'EN_CLK0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'EN_CLK1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'EN_CLK2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'EN_CLK3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'EN_CLK4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'EN_CLK5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'EN_CLK6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'EN_CLK7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'EN_ECLK0',0,4
	.word	377
	.byte	2,14,2,35,0,8
	.byte	'EN_ECLK1',0,4
	.word	377
	.byte	2,12,2,35,0,8
	.byte	'EN_ECLK2',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'EN_FXCLK',0,4
	.word	377
	.byte	2,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,3,198,1,3
	.word	2589
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,3,201,1,16,4,8
	.byte	'ECLK_DEN',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,3,205,1,3
	.word	2909
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,3,208,1,16,4,8
	.byte	'ECLK_NUM',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,3,212,1,3
	.word	3021
	.byte	7
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,3,215,1,16,4,8
	.byte	'FXCLK_SEL',0,4
	.word	377
	.byte	4,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,3,219,1,3
	.word	3133
	.byte	7
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,3,222,1,16,4,8
	.byte	'GCLK_DEN',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,3,226,1,3
	.word	3249
	.byte	7
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,3,229,1,16,4,8
	.byte	'GCLK_NUM',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,3,233,1,3
	.word	3361
	.byte	7
	.byte	'_Ifx_GTM_CTRL_Bits',0,3,236,1,16,4,8
	.byte	'RF_PROT',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'TO_MODE',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'TO_VAL',0,4
	.word	377
	.byte	5,23,2,35,0,8
	.byte	'reserved_9',0,4
	.word	377
	.byte	23,0,2,35,0,0,4
	.byte	'Ifx_GTM_CTRL_Bits',0,3,243,1,3
	.word	3473
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,3,246,1,16,4,8
	.byte	'O1SEL_0',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	377
	.byte	2,29,2,35,0,8
	.byte	'SWAP_0',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'O1F_0',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'O1SEL_1',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'I1SEL_1',0,4
	.word	377
	.byte	1,22,2,35,0,8
	.byte	'SH_EN_1',0,4
	.word	377
	.byte	1,21,2,35,0,8
	.byte	'SWAP_1',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'O1F_1',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'O1SEL_2',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'I1SEL_2',0,4
	.word	377
	.byte	1,14,2,35,0,8
	.byte	'SH_EN_2',0,4
	.word	377
	.byte	1,13,2,35,0,8
	.byte	'SWAP_2',0,4
	.word	377
	.byte	1,12,2,35,0,8
	.byte	'O1F_2',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'reserved_22',0,4
	.word	377
	.byte	2,8,2,35,0,8
	.byte	'O1SEL_3',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'I1SEL_3',0,4
	.word	377
	.byte	1,6,2,35,0,8
	.byte	'SH_EN_3',0,4
	.word	377
	.byte	1,5,2,35,0,8
	.byte	'SWAP_3',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'O1F_3',0,4
	.word	377
	.byte	2,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,3,143,2,3
	.word	3626
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,3,146,2,16,4,8
	.byte	'POL0_0',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'OC0_0',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'SL0_0',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'DT0_0',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'POL1_0',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'OC1_0',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'SL1_0',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'DT1_0',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'POL0_1',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'OC0_1',0,4
	.word	377
	.byte	1,22,2,35,0,8
	.byte	'SL0_1',0,4
	.word	377
	.byte	1,21,2,35,0,8
	.byte	'DT0_1',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'POL1_1',0,4
	.word	377
	.byte	1,19,2,35,0,8
	.byte	'OC1_1',0,4
	.word	377
	.byte	1,18,2,35,0,8
	.byte	'SL1_1',0,4
	.word	377
	.byte	1,17,2,35,0,8
	.byte	'DT1_1',0,4
	.word	377
	.byte	1,16,2,35,0,8
	.byte	'POL0_2',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'OC0_2',0,4
	.word	377
	.byte	1,14,2,35,0,8
	.byte	'SL0_2',0,4
	.word	377
	.byte	1,13,2,35,0,8
	.byte	'DT0_2',0,4
	.word	377
	.byte	1,12,2,35,0,8
	.byte	'POL1_2',0,4
	.word	377
	.byte	1,11,2,35,0,8
	.byte	'OC1_2',0,4
	.word	377
	.byte	1,10,2,35,0,8
	.byte	'SL1_2',0,4
	.word	377
	.byte	1,9,2,35,0,8
	.byte	'DT1_2',0,4
	.word	377
	.byte	1,8,2,35,0,8
	.byte	'POL0_3',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'OC0_3',0,4
	.word	377
	.byte	1,6,2,35,0,8
	.byte	'SL0_3',0,4
	.word	377
	.byte	1,5,2,35,0,8
	.byte	'DT0_3',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'POL1_3',0,4
	.word	377
	.byte	1,3,2,35,0,8
	.byte	'OC1_3',0,4
	.word	377
	.byte	1,2,2,35,0,8
	.byte	'SL1_3',0,4
	.word	377
	.byte	1,1,2,35,0,8
	.byte	'DT1_3',0,4
	.word	377
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,3,180,2,3
	.word	4138
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,3,183,2,16,4,8
	.byte	'POL0_0_SR',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'OC0_0_SR',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'SL0_0_SR',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'DT0_0_SR',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'POL1_0_SR',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'OC1_0_SR',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'SL1_0_SR',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'DT1_0_SR',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'POL0_1_SR',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'OC0_1_SR',0,4
	.word	377
	.byte	1,22,2,35,0,8
	.byte	'SL0_1_SR',0,4
	.word	377
	.byte	1,21,2,35,0,8
	.byte	'DT0_1_SR',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'POL1_1_SR',0,4
	.word	377
	.byte	1,19,2,35,0,8
	.byte	'OC1_1_SR',0,4
	.word	377
	.byte	1,18,2,35,0,8
	.byte	'SL1_1_SR',0,4
	.word	377
	.byte	1,17,2,35,0,8
	.byte	'DT1_1_SR',0,4
	.word	377
	.byte	1,16,2,35,0,8
	.byte	'POL0_2_SR',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'OC0_2_SR',0,4
	.word	377
	.byte	1,14,2,35,0,8
	.byte	'SL0_2_SR',0,4
	.word	377
	.byte	1,13,2,35,0,8
	.byte	'DT0_2_SR',0,4
	.word	377
	.byte	1,12,2,35,0,8
	.byte	'POL1_2_SR',0,4
	.word	377
	.byte	1,11,2,35,0,8
	.byte	'OC1_2_SR',0,4
	.word	377
	.byte	1,10,2,35,0,8
	.byte	'SL1_2_SR',0,4
	.word	377
	.byte	1,9,2,35,0,8
	.byte	'DT1_2_SR',0,4
	.word	377
	.byte	1,8,2,35,0,8
	.byte	'POL0_3_SR',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'OC0_3_SR',0,4
	.word	377
	.byte	1,6,2,35,0,8
	.byte	'SL0_3_SR',0,4
	.word	377
	.byte	1,5,2,35,0,8
	.byte	'DT0_3_SR',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'POL1_3_SR',0,4
	.word	377
	.byte	1,3,2,35,0,8
	.byte	'OC1_3_SR',0,4
	.word	377
	.byte	1,2,2,35,0,8
	.byte	'SL1_3_SR',0,4
	.word	377
	.byte	1,1,2,35,0,8
	.byte	'DT1_3_SR',0,4
	.word	377
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,3,217,2,3
	.word	4759
	.byte	7
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,3,220,2,16,4,8
	.byte	'CLK_SEL',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'UPD_MODE',0,4
	.word	377
	.byte	3,25,2,35,0,8
	.byte	'reserved_7',0,4
	.word	377
	.byte	25,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,3,226,2,3
	.word	5482
	.byte	7
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,3,229,2,16,4,8
	.byte	'RELRISE',0,4
	.word	377
	.byte	10,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	377
	.byte	6,16,2,35,0,8
	.byte	'RELFALL',0,4
	.word	377
	.byte	10,6,2,35,0,8
	.byte	'reserved_26',0,4
	.word	377
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,3,235,2,3
	.word	5626
	.byte	7
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,3,238,2,16,4,8
	.byte	'RELBLK',0,4
	.word	377
	.byte	10,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	377
	.byte	6,16,2,35,0,8
	.byte	'PSU_IN_SEL',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'IN_POL',0,4
	.word	377
	.byte	1,14,2,35,0,8
	.byte	'reserved_18',0,4
	.word	377
	.byte	2,12,2,35,0,8
	.byte	'SHIFT_SEL',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'reserved_22',0,4
	.word	377
	.byte	10,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,3,247,2,3
	.word	5775
	.byte	7
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,3,250,2,16,4,8
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,3,129,3,3
	.word	5990
	.byte	7
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,3,132,3,16,4,8
	.byte	'GRSTEN',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'BRIDGE_MODE_RST',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'AEI_IN',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	377
	.byte	5,24,2,35,0,8
	.byte	'TOM_OUT_RST',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	377
	.byte	3,20,2,35,0,8
	.byte	'reserved_12',0,4
	.word	377
	.byte	4,16,2,35,0,8
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'IRQ_MODE_PULSE',0,4
	.word	377
	.byte	1,14,2,35,0,8
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	377
	.byte	1,13,2,35,0,8
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	377
	.byte	1,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	377
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_GTM_HW_CONF_Bits',0,3,146,3,3
	.word	6194
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,3,149,3,16,4,8
	.byte	'reserved_0',0,4
	.word	377
	.byte	4,28,2,35,0,8
	.byte	'AEI_IRQ',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	377
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,3,154,3,3
	.word	6551
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,3,157,3,16,4,8
	.byte	'TIM0_CH0_IRQ',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'TIM0_CH1_IRQ',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'TIM0_CH2_IRQ',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'TIM0_CH3_IRQ',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'TIM0_CH4_IRQ',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'TIM0_CH5_IRQ',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'TIM0_CH6_IRQ',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'TIM0_CH7_IRQ',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	377
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,3,168,3,3
	.word	6679
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,3,171,3,16,4,8
	.byte	'TOM0_CH0_IRQ',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'TOM0_CH1_IRQ',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'TOM0_CH2_IRQ',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'TOM0_CH3_IRQ',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'TOM0_CH4_IRQ',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'TOM0_CH5_IRQ',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'TOM0_CH6_IRQ',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'TOM0_CH7_IRQ',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'TOM0_CH8_IRQ',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'TOM0_CH9_IRQ',0,4
	.word	377
	.byte	1,22,2,35,0,8
	.byte	'TOM0_CH10_IRQ',0,4
	.word	377
	.byte	1,21,2,35,0,8
	.byte	'TOM0_CH11_IRQ',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'TOM0_CH12_IRQ',0,4
	.word	377
	.byte	1,19,2,35,0,8
	.byte	'TOM0_CH13_IRQ',0,4
	.word	377
	.byte	1,18,2,35,0,8
	.byte	'TOM0_CH14_IRQ',0,4
	.word	377
	.byte	1,17,2,35,0,8
	.byte	'TOM0_CH15_IRQ',0,4
	.word	377
	.byte	1,16,2,35,0,8
	.byte	'TOM1_CH0_IRQ',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'TOM1_CH1_IRQ',0,4
	.word	377
	.byte	1,14,2,35,0,8
	.byte	'TOM1_CH2_IRQ',0,4
	.word	377
	.byte	1,13,2,35,0,8
	.byte	'TOM1_CH3_IRQ',0,4
	.word	377
	.byte	1,12,2,35,0,8
	.byte	'TOM1_CH4_IRQ',0,4
	.word	377
	.byte	1,11,2,35,0,8
	.byte	'TOM1_CH5_IRQ',0,4
	.word	377
	.byte	1,10,2,35,0,8
	.byte	'TOM1_CH6_IRQ',0,4
	.word	377
	.byte	1,9,2,35,0,8
	.byte	'TOM1_CH7_IRQ',0,4
	.word	377
	.byte	1,8,2,35,0,8
	.byte	'TOM1_CH8_IRQ',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'TOM1_CH9_IRQ',0,4
	.word	377
	.byte	1,6,2,35,0,8
	.byte	'TOM1_CH10_IRQ',0,4
	.word	377
	.byte	1,5,2,35,0,8
	.byte	'TOM1_CH11_IRQ',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'TOM1_CH12_IRQ',0,4
	.word	377
	.byte	1,3,2,35,0,8
	.byte	'TOM1_CH13_IRQ',0,4
	.word	377
	.byte	1,2,2,35,0,8
	.byte	'TOM1_CH14_IRQ',0,4
	.word	377
	.byte	1,1,2,35,0,8
	.byte	'TOM1_CH15_IRQ',0,4
	.word	377
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,3,205,3,3
	.word	6958
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,3,208,3,16,4,8
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	377
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,3,219,3,3
	.word	7803
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,3,222,3,16,4,8
	.byte	'GTM_EIRQ',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	377
	.byte	3,28,2,35,0,8
	.byte	'TIM0_EIRQ',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	377
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,3,228,3,3
	.word	8096
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,3,231,3,16,4,8
	.byte	'SEL0',0,4
	.word	377
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	377
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	377
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	377
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,3,238,3,3
	.word	8250
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,3,241,3,16,4,8
	.byte	'SEL0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'SEL1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'SEL2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'SEL3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'SEL4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'SEL5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'SEL6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'SEL7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'SEL8',0,4
	.word	377
	.byte	2,14,2,35,0,8
	.byte	'SEL9',0,4
	.word	377
	.byte	2,12,2,35,0,8
	.byte	'SEL10',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'SEL11',0,4
	.word	377
	.byte	2,8,2,35,0,8
	.byte	'SEL12',0,4
	.word	377
	.byte	2,6,2,35,0,8
	.byte	'SEL13',0,4
	.word	377
	.byte	2,4,2,35,0,8
	.byte	'SEL14',0,4
	.word	377
	.byte	2,2,2,35,0,8
	.byte	'SEL15',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,3,131,4,3
	.word	8420
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,3,134,4,16,4,8
	.byte	'CH0SEL',0,4
	.word	377
	.byte	4,28,2,35,0,8
	.byte	'CH1SEL',0,4
	.word	377
	.byte	4,24,2,35,0,8
	.byte	'CH2SEL',0,4
	.word	377
	.byte	4,20,2,35,0,8
	.byte	'CH3SEL',0,4
	.word	377
	.byte	4,16,2,35,0,8
	.byte	'CH4SEL',0,4
	.word	377
	.byte	4,12,2,35,0,8
	.byte	'CH5SEL',0,4
	.word	377
	.byte	4,8,2,35,0,8
	.byte	'CH6SEL',0,4
	.word	377
	.byte	4,4,2,35,0,8
	.byte	'CH7SEL',0,4
	.word	377
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,3,144,4,3
	.word	8761
	.byte	7
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,3,147,4,16,4,8
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,3,154,4,3
	.word	8986
	.byte	7
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,3,157,4,16,4,8
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'TRG_AEI_USP_BE',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,3,164,4,3
	.word	9184
	.byte	7
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,3,167,4,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,3,171,4,3
	.word	9380
	.byte	7
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,3,174,4,16,4,8
	.byte	'AEI_TO_XPT',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,3,181,4,3
	.word	9483
	.byte	7
	.byte	'_Ifx_GTM_KRST0_Bits',0,3,184,4,16,4,8
	.byte	'RST',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'RSTSTAT',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRST0_Bits',0,3,189,4,3
	.word	9661
	.byte	7
	.byte	'_Ifx_GTM_KRST1_Bits',0,3,192,4,16,4,8
	.byte	'RST',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	377
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRST1_Bits',0,3,196,4,3
	.word	9772
	.byte	7
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,3,199,4,16,4,8
	.byte	'CLR',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	377
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,3,203,4,3
	.word	9864
	.byte	7
	.byte	'_Ifx_GTM_OCS_Bits',0,3,206,4,16,4,8
	.byte	'reserved_0',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'SUS',0,4
	.word	377
	.byte	4,4,2,35,0,8
	.byte	'SUS_P',0,4
	.word	377
	.byte	1,3,2,35,0,8
	.byte	'SUSSTA',0,4
	.word	377
	.byte	1,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_OCS_Bits',0,3,213,4,3
	.word	9960
	.byte	7
	.byte	'_Ifx_GTM_ODA_Bits',0,3,216,4,16,4,8
	.byte	'DDREN',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'DREN',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_ODA_Bits',0,3,221,4,3
	.word	10106
	.byte	7
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,3,224,4,16,4,8
	.byte	'CV',0,4
	.word	377
	.byte	27,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'CM',0,4
	.word	377
	.byte	2,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU0T_Bits',0,3,230,4,3
	.word	10212
	.byte	7
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,3,233,4,16,4,8
	.byte	'CV',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	4,4,2,35,0,8
	.byte	'EN',0,4
	.word	377
	.byte	1,3,2,35,0,8
	.byte	'reserved_29',0,4
	.word	377
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU1T_Bits',0,3,239,4,3
	.word	10343
	.byte	7
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,3,242,4,16,4,8
	.byte	'CV',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	4,4,2,35,0,8
	.byte	'EN',0,4
	.word	377
	.byte	1,3,2,35,0,8
	.byte	'reserved_29',0,4
	.word	377
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU2T_Bits',0,3,248,4,3
	.word	10474
	.byte	7
	.byte	'_Ifx_GTM_OTSC0_Bits',0,3,251,4,16,4,8
	.byte	'B0LMT',0,4
	.word	377
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'B0LMI',0,4
	.word	377
	.byte	4,24,2,35,0,8
	.byte	'B0HMT',0,4
	.word	377
	.byte	3,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'B0HMI',0,4
	.word	377
	.byte	4,16,2,35,0,8
	.byte	'B1LMT',0,4
	.word	377
	.byte	3,13,2,35,0,8
	.byte	'reserved_19',0,4
	.word	377
	.byte	1,12,2,35,0,8
	.byte	'B1LMI',0,4
	.word	377
	.byte	4,8,2,35,0,8
	.byte	'B1HMT',0,4
	.word	377
	.byte	3,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'B1HMI',0,4
	.word	377
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTSC0_Bits',0,3,137,5,3
	.word	10605
	.byte	7
	.byte	'_Ifx_GTM_OTSS_Bits',0,3,140,5,16,4,8
	.byte	'OTGB0',0,4
	.word	377
	.byte	4,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	4,24,2,35,0,8
	.byte	'OTGB1',0,4
	.word	377
	.byte	4,20,2,35,0,8
	.byte	'reserved_12',0,4
	.word	377
	.byte	4,16,2,35,0,8
	.byte	'OTGB2',0,4
	.word	377
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	377
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTSS_Bits',0,3,148,5,3
	.word	10887
	.byte	7
	.byte	'_Ifx_GTM_REV_Bits',0,3,151,5,16,4,8
	.byte	'STEP',0,4
	.word	377
	.byte	8,24,2,35,0,8
	.byte	'NO',0,4
	.word	377
	.byte	4,20,2,35,0,8
	.byte	'MINOR',0,4
	.word	377
	.byte	4,16,2,35,0,8
	.byte	'MAJOR',0,4
	.word	377
	.byte	4,12,2,35,0,8
	.byte	'DEV_CODE0',0,4
	.word	377
	.byte	4,8,2,35,0,8
	.byte	'DEV_CODE1',0,4
	.word	377
	.byte	4,4,2,35,0,8
	.byte	'DEV_CODE2',0,4
	.word	377
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_REV_Bits',0,3,160,5,3
	.word	11059
	.byte	7
	.byte	'_Ifx_GTM_RST_Bits',0,3,163,5,16,4,8
	.byte	'RST',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	377
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_RST_Bits',0,3,167,5,3
	.word	11237
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,3,170,5,16,4,8
	.byte	'BASE',0,4
	.word	377
	.byte	27,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	377
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,3,174,5,3
	.word	11325
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,3,177,5,16,4,8
	.byte	'LOW_RES',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	377
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,3,182,5,3
	.word	11433
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,3,185,5,16,4,8
	.byte	'BASE',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,3,189,5,3
	.word	11565
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,3,192,5,16,4,8
	.byte	'CH_MODE',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	377
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,3,197,5,3
	.word	11673
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,3,200,5,16,4,8
	.byte	'BASE',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,3,204,5,3
	.word	11805
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,3,207,5,16,4,8
	.byte	'CH_MODE',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	377
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	377
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,3,212,5,3
	.word	11913
	.byte	7
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,3,215,5,16,4,8
	.byte	'ENDIS_CH0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CH1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CH2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	377
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,3,221,5,3
	.word	12045
	.byte	7
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,3,224,5,16,4,8
	.byte	'SRC_CH0',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'SRC_CH1',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'SRC_CH2',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'SRC_CH3',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'SRC_CH4',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'SRC_CH5',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'SRC_CH6',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'SRC_CH7',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	377
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,3,235,5,3
	.word	12191
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,3,238,5,16,4,8
	.byte	'CNT',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,3,242,5,3
	.word	12438
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,3,245,5,16,4,8
	.byte	'CNTS',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,3,249,5,3
	.word	12541
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,3,252,5,16,4,8
	.byte	'TIM_EN',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'TIM_MODE',0,4
	.word	377
	.byte	3,28,2,35,0,8
	.byte	'OSM',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'CICTRL',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'TBU0x_SEL',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'GPR0_SEL',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'GPR1_SEL',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'CNTS_SEL',0,4
	.word	377
	.byte	1,19,2,35,0,8
	.byte	'DSL',0,4
	.word	377
	.byte	1,18,2,35,0,8
	.byte	'ISL',0,4
	.word	377
	.byte	1,17,2,35,0,8
	.byte	'ECNT_RESET',0,4
	.word	377
	.byte	1,16,2,35,0,8
	.byte	'FLT_EN',0,4
	.word	377
	.byte	1,15,2,35,0,8
	.byte	'FLT_CNT_FRQ',0,4
	.word	377
	.byte	2,13,2,35,0,8
	.byte	'EXT_CAP_EN',0,4
	.word	377
	.byte	1,12,2,35,0,8
	.byte	'FLT_MODE_RE',0,4
	.word	377
	.byte	1,11,2,35,0,8
	.byte	'FLT_CTR_RE',0,4
	.word	377
	.byte	1,10,2,35,0,8
	.byte	'FLT_MODE_FE',0,4
	.word	377
	.byte	1,9,2,35,0,8
	.byte	'FLT_CTR_FE',0,4
	.word	377
	.byte	1,8,2,35,0,8
	.byte	'CLK_SEL',0,4
	.word	377
	.byte	3,5,2,35,0,8
	.byte	'FR_ECNT_OFL',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'EGPR0_SEL',0,4
	.word	377
	.byte	1,3,2,35,0,8
	.byte	'EGPR1_SEL',0,4
	.word	377
	.byte	1,2,2,35,0,8
	.byte	'TOCTRL',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,3,150,6,3
	.word	12640
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,3,153,6,16,4,8
	.byte	'ECNT',0,4
	.word	377
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,3,157,6,3
	.word	13188
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,3,160,6,16,4,8
	.byte	'EXT_CAP_SRC',0,4
	.word	377
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	377
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,3,164,6,3
	.word	13294
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,3,167,6,16,4,8
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'TODET_EIRQ_EN',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	377
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,3,176,6,3
	.word	13408
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,3,179,6,16,4,8
	.byte	'FLT_FE',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,3,183,6,3
	.word	13663
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,3,186,6,16,4,8
	.byte	'FLT_RE',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,3,190,6,3
	.word	13775
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,3,193,6,16,4,8
	.byte	'GPR0',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,3,197,6,3
	.word	13887
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,3,200,6,16,4,8
	.byte	'GPR1',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,3,204,6,3
	.word	13986
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,3,207,6,16,4,8
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'TODET_IRQ_EN',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	377
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,3,216,6,3
	.word	14085
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,3,219,6,16,4,8
	.byte	'TRG_NEWVAL',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'TRG_ECNTOFL',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'TRG_CNTOFL',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'TRG_GPRzOFL',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'TRG_TODET',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'TRG_GLITCHDET',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	377
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,3,228,6,3
	.word	14332
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,3,231,6,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,3,235,6,3
	.word	14571
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,3,238,6,16,4,8
	.byte	'NEWVAL',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'TODET',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	377
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,3,247,6,3
	.word	14688
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,3,250,6,16,4,8
	.byte	'TO_CNT',0,4
	.word	377
	.byte	8,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	377
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,3,254,6,3
	.word	14901
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,3,129,7,16,4,8
	.byte	'TOV',0,4
	.word	377
	.byte	8,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	377
	.byte	20,4,2,35,0,8
	.byte	'TCS',0,4
	.word	377
	.byte	3,1,2,35,0,8
	.byte	'reserved_31',0,4
	.word	377
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,3,135,7,3
	.word	15008
	.byte	7
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,3,138,7,16,4,8
	.byte	'VAL_0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'MODE_0',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'VAL_1',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'MODE_1',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'VAL_2',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'MODE_2',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'VAL_3',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'MODE_3',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'VAL_4',0,4
	.word	377
	.byte	2,14,2,35,0,8
	.byte	'MODE_4',0,4
	.word	377
	.byte	2,12,2,35,0,8
	.byte	'VAL_5',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'MODE_5',0,4
	.word	377
	.byte	2,8,2,35,0,8
	.byte	'VAL_6',0,4
	.word	377
	.byte	2,6,2,35,0,8
	.byte	'MODE_6',0,4
	.word	377
	.byte	2,4,2,35,0,8
	.byte	'VAL_7',0,4
	.word	377
	.byte	2,2,2,35,0,8
	.byte	'MODE_7',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,3,156,7,3
	.word	15150
	.byte	7
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,3,159,7,16,4,8
	.byte	'F_OUT',0,4
	.word	377
	.byte	8,24,2,35,0,8
	.byte	'F_IN',0,4
	.word	377
	.byte	8,16,2,35,0,8
	.byte	'TIM_IN',0,4
	.word	377
	.byte	8,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	377
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,3,165,7,3
	.word	15495
	.byte	7
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,3,168,7,16,4,8
	.byte	'RST_CH0',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	377
	.byte	1,29,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	377
	.byte	1,28,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	377
	.byte	1,27,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	377
	.byte	1,26,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	377
	.byte	1,25,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	377
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	377
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_RST_Bits',0,3,179,7,3
	.word	15636
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,3,182,7,16,4,8
	.byte	'CM0',0,4
	.word	377
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,3,186,7,3
	.word	15869
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,3,189,7,16,4,8
	.byte	'CM1',0,4
	.word	377
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,3,193,7,3
	.word	15972
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,3,196,7,16,4,8
	.byte	'CN0',0,4
	.word	377
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,3,200,7,3
	.word	16075
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,3,203,7,16,4,8
	.byte	'reserved_0',0,4
	.word	377
	.byte	11,21,2,35,0,8
	.byte	'SL',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'CLK_SRC_SR',0,4
	.word	377
	.byte	3,17,2,35,0,8
	.byte	'reserved_15',0,4
	.word	377
	.byte	5,12,2,35,0,8
	.byte	'RST_CCU0',0,4
	.word	377
	.byte	1,11,2,35,0,8
	.byte	'OSM_TRIG',0,4
	.word	377
	.byte	1,10,2,35,0,8
	.byte	'EXT_TRIG',0,4
	.word	377
	.byte	1,9,2,35,0,8
	.byte	'EXTTRIGOUT',0,4
	.word	377
	.byte	1,8,2,35,0,8
	.byte	'TRIGOUT',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	377
	.byte	1,6,2,35,0,8
	.byte	'OSM',0,4
	.word	377
	.byte	1,5,2,35,0,8
	.byte	'BITREV',0,4
	.word	377
	.byte	1,4,2,35,0,8
	.byte	'reserved_28',0,4
	.word	377
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,3,218,7,3
	.word	16178
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,3,221,7,16,4,8
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,3,226,7,3
	.word	16506
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,3,229,7,16,4,8
	.byte	'TRG_CCU0TC0',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'TRG_CCU1TC0',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,3,234,7,3
	.word	16649
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,3,237,7,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,3,241,7,3
	.word	16798
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,3,244,7,16,4,8
	.byte	'CCU0TC',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'CCU1TC',0,4
	.word	377
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	377
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,3,249,7,3
	.word	16915
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,3,252,7,16,4,8
	.byte	'SR0',0,4
	.word	377
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,3,128,8,3
	.word	17052
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,3,131,8,16,4,8
	.byte	'SR1',0,4
	.word	377
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,3,135,8,3
	.word	17155
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,3,138,8,16,4,8
	.byte	'OL',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	377
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,3,142,8,3
	.word	17258
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,3,145,8,16,4,8
	.byte	'ACT_TB',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'TB_TRIG',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'TBU_SEL',0,4
	.word	377
	.byte	2,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	377
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,3,151,8,3
	.word	17361
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,3,154,8,16,4,8
	.byte	'ENDIS_CTRL0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CTRL1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CTRL2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_CTRL3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_CTRL4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_CTRL5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_CTRL6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_CTRL7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,3,165,8,3
	.word	17515
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,3,168,8,16,4,8
	.byte	'ENDIS_STAT0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_STAT1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_STAT2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_STAT3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_STAT4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_STAT5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_STAT6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_STAT7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,3,179,8,3
	.word	17805
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,3,182,8,16,4,8
	.byte	'FUPD_CTRL0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'FUPD_CTRL1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'FUPD_CTRL2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'FUPD_CTRL3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'FUPD_CTRL4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'FUPD_CTRL5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'FUPD_CTRL6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'FUPD_CTRL7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'RSTCN0_CH0',0,4
	.word	377
	.byte	2,14,2,35,0,8
	.byte	'RSTCN0_CH1',0,4
	.word	377
	.byte	2,12,2,35,0,8
	.byte	'RSTCN0_CH2',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'RSTCN0_CH3',0,4
	.word	377
	.byte	2,8,2,35,0,8
	.byte	'RSTCN0_CH4',0,4
	.word	377
	.byte	2,6,2,35,0,8
	.byte	'RSTCN0_CH5',0,4
	.word	377
	.byte	2,4,2,35,0,8
	.byte	'RSTCN0_CH6',0,4
	.word	377
	.byte	2,2,2,35,0,8
	.byte	'RSTCN0_CH7',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,3,200,8,3
	.word	18095
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,3,203,8,16,4,8
	.byte	'HOST_TRIG',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	377
	.byte	7,24,2,35,0,8
	.byte	'RST_CH0',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	377
	.byte	1,22,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	377
	.byte	1,21,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	377
	.byte	1,19,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	377
	.byte	1,18,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	377
	.byte	1,17,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	377
	.byte	1,16,2,35,0,8
	.byte	'UPEN_CTRL0',0,4
	.word	377
	.byte	2,14,2,35,0,8
	.byte	'UPEN_CTRL1',0,4
	.word	377
	.byte	2,12,2,35,0,8
	.byte	'UPEN_CTRL2',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'UPEN_CTRL3',0,4
	.word	377
	.byte	2,8,2,35,0,8
	.byte	'UPEN_CTRL4',0,4
	.word	377
	.byte	2,6,2,35,0,8
	.byte	'UPEN_CTRL5',0,4
	.word	377
	.byte	2,4,2,35,0,8
	.byte	'UPEN_CTRL6',0,4
	.word	377
	.byte	2,2,2,35,0,8
	.byte	'UPEN_CTRL7',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,3,223,8,3
	.word	18528
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,3,226,8,16,4,8
	.byte	'INT_TRIG0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'INT_TRIG1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'INT_TRIG2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'INT_TRIG3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'INT_TRIG4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'INT_TRIG5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'INT_TRIG6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'INT_TRIG7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,3,237,8,3
	.word	18978
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,3,240,8,16,4,8
	.byte	'OUTEN_CTRL0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_CTRL1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_CTRL2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_CTRL3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_CTRL4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_CTRL5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_CTRL6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_CTRL7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,3,251,8,3
	.word	19248
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,3,254,8,16,4,8
	.byte	'OUTEN_STAT0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_STAT1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_STAT2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_STAT3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_STAT4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_STAT5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_STAT6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_STAT7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,3,137,9,3
	.word	19538
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,3,140,9,16,4,8
	.byte	'ACT_TB',0,4
	.word	377
	.byte	24,8,2,35,0,8
	.byte	'TB_TRIG',0,4
	.word	377
	.byte	1,7,2,35,0,8
	.byte	'TBU_SEL',0,4
	.word	377
	.byte	2,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	377
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,3,146,9,3
	.word	19828
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,3,149,9,16,4,8
	.byte	'ENDIS_CTRL0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CTRL1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CTRL2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_CTRL3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_CTRL4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_CTRL5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_CTRL6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_CTRL7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,3,160,9,3
	.word	19982
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,3,163,9,16,4,8
	.byte	'ENDIS_STAT0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_STAT1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_STAT2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_STAT3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_STAT4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_STAT5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_STAT6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_STAT7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,3,174,9,3
	.word	20272
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,3,177,9,16,4,8
	.byte	'FUPD_CTRL0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'FUPD_CTRL1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'FUPD_CTRL2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'FUPD_CTRL3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'FUPD_CTRL4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'FUPD_CTRL5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'FUPD_CTRL6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'FUPD_CTRL7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'RSTCN0_CH0',0,4
	.word	377
	.byte	2,14,2,35,0,8
	.byte	'RSTCN0_CH1',0,4
	.word	377
	.byte	2,12,2,35,0,8
	.byte	'RSTCN0_CH2',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'RSTCN0_CH3',0,4
	.word	377
	.byte	2,8,2,35,0,8
	.byte	'RSTCN0_CH4',0,4
	.word	377
	.byte	2,6,2,35,0,8
	.byte	'RSTCN0_CH5',0,4
	.word	377
	.byte	2,4,2,35,0,8
	.byte	'RSTCN0_CH6',0,4
	.word	377
	.byte	2,2,2,35,0,8
	.byte	'RSTCN0_CH7',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,3,195,9,3
	.word	20562
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,3,198,9,16,4,8
	.byte	'HOST_TRIG',0,4
	.word	377
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	377
	.byte	7,24,2,35,0,8
	.byte	'RST_CH0',0,4
	.word	377
	.byte	1,23,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	377
	.byte	1,22,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	377
	.byte	1,21,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	377
	.byte	1,20,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	377
	.byte	1,19,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	377
	.byte	1,18,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	377
	.byte	1,17,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	377
	.byte	1,16,2,35,0,8
	.byte	'UPEN_CTRL0',0,4
	.word	377
	.byte	2,14,2,35,0,8
	.byte	'UPEN_CTRL1',0,4
	.word	377
	.byte	2,12,2,35,0,8
	.byte	'UPEN_CTRL2',0,4
	.word	377
	.byte	2,10,2,35,0,8
	.byte	'UPEN_CTRL3',0,4
	.word	377
	.byte	2,8,2,35,0,8
	.byte	'UPEN_CTRL4',0,4
	.word	377
	.byte	2,6,2,35,0,8
	.byte	'UPEN_CTRL5',0,4
	.word	377
	.byte	2,4,2,35,0,8
	.byte	'UPEN_CTRL6',0,4
	.word	377
	.byte	2,2,2,35,0,8
	.byte	'UPEN_CTRL7',0,4
	.word	377
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,3,218,9,3
	.word	20995
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,3,221,9,16,4,8
	.byte	'INT_TRIG0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'INT_TRIG1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'INT_TRIG2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'INT_TRIG3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'INT_TRIG4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'INT_TRIG5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'INT_TRIG6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'INT_TRIG7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,3,232,9,3
	.word	21445
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,3,235,9,16,4,8
	.byte	'OUTEN_CTRL0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_CTRL1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_CTRL2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_CTRL3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_CTRL4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_CTRL5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_CTRL6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_CTRL7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,3,246,9,3
	.word	21715
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,3,249,9,16,4,8
	.byte	'OUTEN_STAT0',0,4
	.word	377
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_STAT1',0,4
	.word	377
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_STAT2',0,4
	.word	377
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_STAT3',0,4
	.word	377
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_STAT4',0,4
	.word	377
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_STAT5',0,4
	.word	377
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_STAT6',0,4
	.word	377
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_STAT7',0,4
	.word	377
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	377
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,3,132,10,3
	.word	22005
	.byte	9,3,140,10,9,4,6
	.byte	'unsigned int',0,4,7,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,6
	.byte	'int',0,4,5,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	351
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN0',0,3,145,10,3
	.word	22295
	.byte	9,3,148,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	924
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN1',0,3,153,10,3
	.word	22382
	.byte	9,3,156,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1001
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,3,161,10,3
	.word	22446
	.byte	9,3,164,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1155
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,3,169,10,3
	.word	22516
	.byte	9,3,172,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1309
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,3,177,10,3
	.word	22586
	.byte	9,3,180,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1437
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_MODE',0,3,185,10,3
	.word	22656
	.byte	9,3,188,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1744
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,3,193,10,3
	.word	22725
	.byte	9,3,196,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1946
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,3,201,10,3
	.word	22794
	.byte	9,3,204,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2059
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CLC',0,3,209,10,3
	.word	22863
	.byte	9,3,212,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2202
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,3,217,10,3
	.word	22924
	.byte	9,3,220,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2319
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,3,225,10,3
	.word	22997
	.byte	9,3,228,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2454
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,3,233,10,3
	.word	23069
	.byte	9,3,236,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2589
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_EN',0,3,241,10,3
	.word	23141
	.byte	9,3,244,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2909
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,3,249,10,3
	.word	23209
	.byte	9,3,252,10,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3021
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,3,129,11,3
	.word	23279
	.byte	9,3,132,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3133
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,3,137,11,3
	.word	23349
	.byte	9,3,140,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3249
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,3,145,11,3
	.word	23421
	.byte	9,3,148,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3361
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,3,153,11,3
	.word	23491
	.byte	9,3,156,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3473
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CTRL',0,3,161,11,3
	.word	23561
	.byte	9,3,164,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3626
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,3,169,11,3
	.word	23623
	.byte	9,3,172,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4138
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,3,177,11,3
	.word	23693
	.byte	9,3,180,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4759
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,3,185,11,3
	.word	23763
	.byte	9,3,188,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5482
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CTRL',0,3,193,11,3
	.word	23836
	.byte	9,3,196,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5626
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_DTV_CH',0,3,201,11,3
	.word	23902
	.byte	9,3,204,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5775
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,3,209,11,3
	.word	23970
	.byte	9,3,212,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5990
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_EIRQ_EN',0,3,217,11,3
	.word	24039
	.byte	9,3,220,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6194
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_HW_CONF',0,3,225,11,3
	.word	24104
	.byte	9,3,228,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6551
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_0',0,3,233,11,3
	.word	24169
	.byte	9,3,236,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6679
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_2',0,3,241,11,3
	.word	24237
	.byte	9,3,244,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6958
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_6',0,3,249,11,3
	.word	24305
	.byte	9,3,252,11,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7803
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,3,129,12,3
	.word	24373
	.byte	9,3,132,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8096
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,3,137,12,3
	.word	24444
	.byte	9,3,140,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8250
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,3,145,12,3
	.word	24514
	.byte	9,3,148,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8420
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,3,153,12,3
	.word	24591
	.byte	9,3,156,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8761
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,3,161,12,3
	.word	24666
	.byte	9,3,164,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8986
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_EN',0,3,169,12,3
	.word	24742
	.byte	9,3,172,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9184
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_FORCINT',0,3,177,12,3
	.word	24806
	.byte	9,3,180,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9380
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_MODE',0,3,185,12,3
	.word	24875
	.byte	9,3,188,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9483
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,3,193,12,3
	.word	24941
	.byte	9,3,196,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9661
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRST0',0,3,201,12,3
	.word	25009
	.byte	9,3,204,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9772
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRST1',0,3,209,12,3
	.word	25072
	.byte	9,3,212,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9864
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRSTCLR',0,3,217,12,3
	.word	25135
	.byte	9,3,220,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9960
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OCS',0,3,225,12,3
	.word	25200
	.byte	9,3,228,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10106
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ODA',0,3,233,12,3
	.word	25261
	.byte	9,3,236,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10212
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU0T',0,3,241,12,3
	.word	25322
	.byte	9,3,244,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10343
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU1T',0,3,249,12,3
	.word	25386
	.byte	9,3,252,12,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10474
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU2T',0,3,129,13,3
	.word	25450
	.byte	9,3,132,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10605
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTSC0',0,3,137,13,3
	.word	25514
	.byte	9,3,140,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10887
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTSS',0,3,145,13,3
	.word	25577
	.byte	9,3,148,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11059
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_REV',0,3,153,13,3
	.word	25639
	.byte	9,3,156,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11237
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_RST',0,3,161,13,3
	.word	25700
	.byte	9,3,164,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11325
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,3,169,13,3
	.word	25761
	.byte	9,3,172,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11433
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,3,177,13,3
	.word	25831
	.byte	9,3,180,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11565
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,3,185,13,3
	.word	25901
	.byte	9,3,188,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11673
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,3,193,13,3
	.word	25971
	.byte	9,3,196,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11805
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,3,201,13,3
	.word	26041
	.byte	9,3,204,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11913
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,3,209,13,3
	.word	26111
	.byte	9,3,212,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12045
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CHEN',0,3,217,13,3
	.word	26181
	.byte	9,3,220,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12191
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,3,225,13,3
	.word	26247
	.byte	9,3,228,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12438
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNT',0,3,233,13,3
	.word	26319
	.byte	9,3,236,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12541
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,3,241,13,3
	.word	26387
	.byte	9,3,244,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12640
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,3,249,13,3
	.word	26456
	.byte	9,3,252,13,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13188
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,3,129,14,3
	.word	26525
	.byte	9,3,132,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13294
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,3,137,14,3
	.word	26594
	.byte	9,3,140,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13408
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,3,145,14,3
	.word	26664
	.byte	9,3,148,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13663
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,3,153,14,3
	.word	26736
	.byte	9,3,156,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13775
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,3,161,14,3
	.word	26807
	.byte	9,3,164,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13887
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,3,169,14,3
	.word	26878
	.byte	9,3,172,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13986
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,3,177,14,3
	.word	26947
	.byte	9,3,180,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14085
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,3,185,14,3
	.word	27016
	.byte	9,3,188,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14332
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,3,193,14,3
	.word	27087
	.byte	9,3,196,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14571
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,3,201,14,3
	.word	27163
	.byte	9,3,204,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14688
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,3,209,14,3
	.word	27236
	.byte	9,3,212,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14901
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,3,217,14,3
	.word	27311
	.byte	9,3,220,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15008
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,3,225,14,3
	.word	27380
	.byte	9,3,228,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15150
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_IN_SRC',0,3,233,14,3
	.word	27449
	.byte	9,3,236,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15495
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_INP_VAL',0,3,241,14,3
	.word	27517
	.byte	9,3,244,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15636
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_RST',0,3,249,14,3
	.word	27586
	.byte	9,3,252,14,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15869
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM0',0,3,129,15,3
	.word	27651
	.byte	9,3,132,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15972
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM1',0,3,137,15,3
	.word	27719
	.byte	9,3,140,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16075
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CN0',0,3,145,15,3
	.word	27787
	.byte	9,3,148,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16178
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,3,153,15,3
	.word	27855
	.byte	9,3,156,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16506
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,3,161,15,3
	.word	27924
	.byte	9,3,164,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16649
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,3,169,15,3
	.word	27995
	.byte	9,3,172,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16798
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,3,177,15,3
	.word	28071
	.byte	9,3,180,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16915
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,3,185,15,3
	.word	28144
	.byte	9,3,188,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17052
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR0',0,3,193,15,3
	.word	28219
	.byte	9,3,196,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17155
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR1',0,3,201,15,3
	.word	28287
	.byte	9,3,204,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17258
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_STAT',0,3,209,15,3
	.word	28355
	.byte	9,3,212,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17361
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,3,217,15,3
	.word	28424
	.byte	9,3,220,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17515
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,3,225,15,3
	.word	28497
	.byte	9,3,228,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17805
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,3,233,15,3
	.word	28574
	.byte	9,3,236,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18095
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,3,241,15,3
	.word	28651
	.byte	9,3,244,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18528
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,3,249,15,3
	.word	28727
	.byte	9,3,252,15,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18978
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,3,129,16,3
	.word	28802
	.byte	9,3,132,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19248
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,3,137,16,3
	.word	28877
	.byte	9,3,140,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19538
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,3,145,16,3
	.word	28954
	.byte	9,3,148,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19828
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,3,153,16,3
	.word	29031
	.byte	9,3,156,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19982
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,3,161,16,3
	.word	29104
	.byte	9,3,164,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20272
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,3,169,16,3
	.word	29181
	.byte	9,3,172,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20562
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,3,177,16,3
	.word	29258
	.byte	9,3,180,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20995
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,3,185,16,3
	.word	29334
	.byte	9,3,188,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21445
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,3,193,16,3
	.word	29409
	.byte	9,3,196,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21715
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,3,201,16,3
	.word	29484
	.byte	9,3,204,16,9,4,10
	.byte	'U',0,4
	.word	22301
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22328
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22005
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,3,209,16,3
	.word	29561
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,3,220,16,25,4,10
	.byte	'CTRL',0,4
	.word	22924
	.byte	2,35,0,0,11
	.word	29638
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK0_5',0,3,223,16,3
	.word	29679
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_6',0,3,226,16,25,4,10
	.byte	'CTRL',0,4
	.word	22997
	.byte	2,35,0,0,11
	.word	29712
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK_6',0,3,229,16,3
	.word	29752
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_7',0,3,232,16,25,4,10
	.byte	'CTRL',0,4
	.word	23069
	.byte	2,35,0,0,11
	.word	29784
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK_7',0,3,235,16,3
	.word	29824
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK',0,3,238,16,25,8,10
	.byte	'NUM',0,4
	.word	23279
	.byte	2,35,0,10
	.byte	'DEN',0,4
	.word	23209
	.byte	2,35,4,0,11
	.word	29856
	.byte	4
	.byte	'Ifx_GTM_CMU_ECLK',0,3,242,16,3
	.word	29907
	.byte	7
	.byte	'_Ifx_GTM_CMU_FXCLK',0,3,245,16,25,4,10
	.byte	'CTRL',0,4
	.word	23349
	.byte	2,35,0,0,11
	.word	29938
	.byte	4
	.byte	'Ifx_GTM_CMU_FXCLK',0,3,248,16,3
	.word	29978
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,3,251,16,25,4,10
	.byte	'OUTSEL',0,4
	.word	24514
	.byte	2,35,0,0,11
	.word	30010
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,3,254,16,3
	.word	30055
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_T',0,3,129,17,25,32,12,32
	.word	24591
	.byte	13,7,0,10
	.byte	'OUTSEL',0,32
	.word	30116
	.byte	2,35,0,0,11
	.word	30090
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_T',0,3,132,17,3
	.word	30142
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,3,135,17,25,32,10
	.byte	'INSEL',0,4
	.word	24666
	.byte	2,35,0,12,28
	.word	231
	.byte	13,27,0,10
	.byte	'reserved_4',0,28
	.word	30218
	.byte	2,35,4,0,11
	.word	30175
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,3,139,17,3
	.word	30248
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH',0,3,142,17,25,116,10
	.byte	'GPR0',0,4
	.word	26878
	.byte	2,35,0,10
	.byte	'GPR1',0,4
	.word	26947
	.byte	2,35,4,10
	.byte	'CNT',0,4
	.word	26319
	.byte	2,35,8,10
	.byte	'ECNT',0,4
	.word	26525
	.byte	2,35,12,10
	.byte	'CNTS',0,4
	.word	26387
	.byte	2,35,16,10
	.byte	'TDUC',0,4
	.word	27311
	.byte	2,35,20,10
	.byte	'TDUV',0,4
	.word	27380
	.byte	2,35,24,10
	.byte	'FLT_RE',0,4
	.word	26807
	.byte	2,35,28,10
	.byte	'FLT_FE',0,4
	.word	26736
	.byte	2,35,32,10
	.byte	'CTRL',0,4
	.word	26456
	.byte	2,35,36,10
	.byte	'ECTRL',0,4
	.word	26594
	.byte	2,35,40,10
	.byte	'IRQ_NOTIFY',0,4
	.word	27236
	.byte	2,35,44,10
	.byte	'IRQ_EN',0,4
	.word	27016
	.byte	2,35,48,10
	.byte	'IRQ_FORCINT',0,4
	.word	27087
	.byte	2,35,52,10
	.byte	'IRQ_MODE',0,4
	.word	27163
	.byte	2,35,56,10
	.byte	'EIRQ_EN',0,4
	.word	26664
	.byte	2,35,60,12,52
	.word	231
	.byte	13,51,0,10
	.byte	'reserved_40',0,52
	.word	30555
	.byte	2,35,64,0,11
	.word	30283
	.byte	4
	.byte	'Ifx_GTM_TIM_CH',0,3,161,17,3
	.word	30586
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH',0,3,164,17,25,48,10
	.byte	'CTRL',0,4
	.word	27855
	.byte	2,35,0,10
	.byte	'SR0',0,4
	.word	28219
	.byte	2,35,4,10
	.byte	'SR1',0,4
	.word	28287
	.byte	2,35,8,10
	.byte	'CM0',0,4
	.word	27651
	.byte	2,35,12,10
	.byte	'CM1',0,4
	.word	27719
	.byte	2,35,16,10
	.byte	'CN0',0,4
	.word	27787
	.byte	2,35,20,10
	.byte	'STAT',0,4
	.word	28355
	.byte	2,35,24,10
	.byte	'IRQ_NOTIFY',0,4
	.word	28144
	.byte	2,35,28,10
	.byte	'IRQ_EN',0,4
	.word	27924
	.byte	2,35,32,10
	.byte	'IRQ_FORCINT',0,4
	.word	27995
	.byte	2,35,36,10
	.byte	'IRQ_MODE',0,4
	.word	28071
	.byte	2,35,40,12,4
	.word	231
	.byte	13,3,0,10
	.byte	'reserved_2C',0,4
	.word	30805
	.byte	2,35,44,0,11
	.word	30615
	.byte	4
	.byte	'Ifx_GTM_TOM_CH',0,3,178,17,3
	.word	30836
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE',0,3,191,17,25,12,10
	.byte	'MODE',0,4
	.word	22656
	.byte	2,35,0,10
	.byte	'PTR1',0,4
	.word	22725
	.byte	2,35,4,10
	.byte	'PTR2',0,4
	.word	22794
	.byte	2,35,8,0,11
	.word	30865
	.byte	4
	.byte	'Ifx_GTM_BRIDGE',0,3,196,17,3
	.word	30930
	.byte	7
	.byte	'_Ifx_GTM_CMU',0,3,199,17,25,72,10
	.byte	'CLK_EN',0,4
	.word	23141
	.byte	2,35,0,10
	.byte	'GCLK_NUM',0,4
	.word	23491
	.byte	2,35,4,10
	.byte	'GCLK_DEN',0,4
	.word	23421
	.byte	2,35,8,12,24
	.word	29638
	.byte	13,5,0,11
	.word	31030
	.byte	10
	.byte	'CLK0_5',0,24
	.word	31039
	.byte	2,35,12,11
	.word	29712
	.byte	10
	.byte	'CLK_6',0,4
	.word	31060
	.byte	2,35,36,11
	.word	29784
	.byte	10
	.byte	'CLK_7',0,4
	.word	31080
	.byte	2,35,40,12,24
	.word	29856
	.byte	13,2,0,11
	.word	31100
	.byte	10
	.byte	'ECLK',0,24
	.word	31109
	.byte	2,35,44,11
	.word	29938
	.byte	10
	.byte	'FXCLK',0,4
	.word	31128
	.byte	2,35,68,0,11
	.word	30959
	.byte	4
	.byte	'Ifx_GTM_CMU',0,3,209,17,3
	.word	31149
	.byte	7
	.byte	'_Ifx_GTM_DTM',0,3,212,17,25,36,10
	.byte	'CTRL',0,4
	.word	23836
	.byte	2,35,0,10
	.byte	'CH_CTRL1',0,4
	.word	23623
	.byte	2,35,4,10
	.byte	'CH_CTRL2',0,4
	.word	23693
	.byte	2,35,8,10
	.byte	'CH_CTRL2_SR',0,4
	.word	23763
	.byte	2,35,12,10
	.byte	'PS_CTRL',0,4
	.word	23970
	.byte	2,35,16,12,16
	.word	23902
	.byte	13,3,0,10
	.byte	'DTV_CH',0,16
	.word	31282
	.byte	2,35,20,0,11
	.word	31175
	.byte	4
	.byte	'Ifx_GTM_DTM',0,3,220,17,3
	.word	31308
	.byte	7
	.byte	'_Ifx_GTM_ICM',0,3,223,17,25,60,10
	.byte	'IRQG_0',0,4
	.word	24169
	.byte	2,35,0,10
	.byte	'reserved_4',0,4
	.word	30805
	.byte	2,35,4,10
	.byte	'IRQG_2',0,4
	.word	24237
	.byte	2,35,8,12,12
	.word	231
	.byte	13,11,0,10
	.byte	'reserved_C',0,12
	.word	31405
	.byte	2,35,12,10
	.byte	'IRQG_6',0,4
	.word	24305
	.byte	2,35,24,12,20
	.word	231
	.byte	13,19,0,10
	.byte	'reserved_1C',0,20
	.word	31450
	.byte	2,35,28,10
	.byte	'IRQG_MEI',0,4
	.word	24444
	.byte	2,35,48,10
	.byte	'reserved_34',0,4
	.word	30805
	.byte	2,35,52,10
	.byte	'IRQG_CEI1',0,4
	.word	24373
	.byte	2,35,56,0,11
	.word	31334
	.byte	4
	.byte	'Ifx_GTM_ICM',0,3,234,17,3
	.word	31539
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL',0,3,237,17,25,148,1,12,32
	.word	30175
	.byte	13,0,0,11
	.word	31590
	.byte	10
	.byte	'TIM',0,32
	.word	31599
	.byte	2,35,0,11
	.word	30090
	.byte	10
	.byte	'T',0,32
	.word	31617
	.byte	2,35,32,12,80
	.word	231
	.byte	13,79,0,10
	.byte	'reserved_40',0,80
	.word	31633
	.byte	2,35,64,11
	.word	30010
	.byte	10
	.byte	'CAN',0,4
	.word	31663
	.byte	3,35,144,1,0,11
	.word	31565
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL',0,3,243,17,3
	.word	31683
	.byte	7
	.byte	'_Ifx_GTM_TBU',0,3,246,17,25,28,10
	.byte	'CHEN',0,4
	.word	26181
	.byte	2,35,0,10
	.byte	'CH0_CTRL',0,4
	.word	25831
	.byte	2,35,4,10
	.byte	'CH0_BASE',0,4
	.word	25761
	.byte	2,35,8,10
	.byte	'CH1_CTRL',0,4
	.word	25971
	.byte	2,35,12,10
	.byte	'CH1_BASE',0,4
	.word	25901
	.byte	2,35,16,10
	.byte	'CH2_CTRL',0,4
	.word	26111
	.byte	2,35,20,10
	.byte	'CH2_BASE',0,4
	.word	26041
	.byte	2,35,24,0,11
	.word	31714
	.byte	4
	.byte	'Ifx_GTM_TBU',0,3,255,17,3
	.word	31856
	.byte	7
	.byte	'_Ifx_GTM_TIM',0,3,130,18,25,128,8,11
	.word	30283
	.byte	10
	.byte	'CH0',0,116
	.word	31902
	.byte	2,35,0,10
	.byte	'INP_VAL',0,4
	.word	27517
	.byte	2,35,116,10
	.byte	'IN_SRC',0,4
	.word	27449
	.byte	2,35,120,10
	.byte	'RST',0,4
	.word	27586
	.byte	2,35,124,11
	.word	30283
	.byte	10
	.byte	'CH1',0,116
	.word	31966
	.byte	3,35,128,1,10
	.byte	'reserved_F4',0,12
	.word	31405
	.byte	3,35,244,1,11
	.word	30283
	.byte	10
	.byte	'CH2',0,116
	.word	32007
	.byte	3,35,128,2,10
	.byte	'reserved_174',0,12
	.word	31405
	.byte	3,35,244,2,11
	.word	30283
	.byte	10
	.byte	'CH3',0,116
	.word	32049
	.byte	3,35,128,3,10
	.byte	'reserved_1F4',0,12
	.word	31405
	.byte	3,35,244,3,11
	.word	30283
	.byte	10
	.byte	'CH4',0,116
	.word	32091
	.byte	3,35,128,4,10
	.byte	'reserved_274',0,12
	.word	31405
	.byte	3,35,244,4,11
	.word	30283
	.byte	10
	.byte	'CH5',0,116
	.word	32133
	.byte	3,35,128,5,10
	.byte	'reserved_2F4',0,12
	.word	31405
	.byte	3,35,244,5,11
	.word	30283
	.byte	10
	.byte	'CH6',0,116
	.word	32175
	.byte	3,35,128,6,10
	.byte	'reserved_374',0,12
	.word	31405
	.byte	3,35,244,6,11
	.word	30283
	.byte	10
	.byte	'CH7',0,116
	.word	32217
	.byte	3,35,128,7,10
	.byte	'reserved_3F4',0,12
	.word	31405
	.byte	3,35,244,7,0,11
	.word	31882
	.byte	4
	.byte	'Ifx_GTM_TIM',0,3,150,18,3
	.word	32260
	.byte	7
	.byte	'_Ifx_GTM_TOM',0,3,153,18,25,128,16,11
	.word	30615
	.byte	10
	.byte	'CH0',0,48
	.word	32306
	.byte	2,35,0,10
	.byte	'TGC0_GLB_CTRL',0,4
	.word	28727
	.byte	2,35,48,10
	.byte	'TGC0_ACT_TB',0,4
	.word	28424
	.byte	2,35,52,10
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	28651
	.byte	2,35,56,10
	.byte	'TGC0_INT_TRIG',0,4
	.word	28802
	.byte	2,35,60,11
	.word	30615
	.byte	10
	.byte	'CH1',0,48
	.word	32415
	.byte	2,35,64,10
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	28497
	.byte	2,35,112,10
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	28574
	.byte	2,35,116,10
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	28877
	.byte	2,35,120,10
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	28954
	.byte	2,35,124,11
	.word	30615
	.byte	10
	.byte	'CH2',0,48
	.word	32533
	.byte	3,35,128,1,12,16
	.word	231
	.byte	13,15,0,10
	.byte	'reserved_B0',0,16
	.word	32552
	.byte	3,35,176,1,11
	.word	30615
	.byte	10
	.byte	'CH3',0,48
	.word	32583
	.byte	3,35,192,1,10
	.byte	'reserved_F0',0,16
	.word	32552
	.byte	3,35,240,1,11
	.word	30615
	.byte	10
	.byte	'CH4',0,48
	.word	32624
	.byte	3,35,128,2,10
	.byte	'reserved_130',0,16
	.word	32552
	.byte	3,35,176,2,11
	.word	30615
	.byte	10
	.byte	'CH5',0,48
	.word	32666
	.byte	3,35,192,2,10
	.byte	'reserved_170',0,16
	.word	32552
	.byte	3,35,240,2,11
	.word	30615
	.byte	10
	.byte	'CH6',0,48
	.word	32708
	.byte	3,35,128,3,10
	.byte	'reserved_1B0',0,16
	.word	32552
	.byte	3,35,176,3,11
	.word	30615
	.byte	10
	.byte	'CH7',0,48
	.word	32750
	.byte	3,35,192,3,10
	.byte	'reserved_1F0',0,16
	.word	32552
	.byte	3,35,240,3,11
	.word	30615
	.byte	10
	.byte	'CH8',0,48
	.word	32792
	.byte	3,35,128,4,10
	.byte	'TGC1_GLB_CTRL',0,4
	.word	29334
	.byte	3,35,176,4,10
	.byte	'TGC1_ACT_TB',0,4
	.word	29031
	.byte	3,35,180,4,10
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	29258
	.byte	3,35,184,4,10
	.byte	'TGC1_INT_TRIG',0,4
	.word	29409
	.byte	3,35,188,4,11
	.word	30615
	.byte	10
	.byte	'CH9',0,48
	.word	32906
	.byte	3,35,192,4,10
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	29104
	.byte	3,35,240,4,10
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	29181
	.byte	3,35,244,4,10
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	29484
	.byte	3,35,248,4,10
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	29561
	.byte	3,35,252,4,11
	.word	30615
	.byte	10
	.byte	'CH10',0,48
	.word	33029
	.byte	3,35,128,5,10
	.byte	'reserved_2B0',0,16
	.word	32552
	.byte	3,35,176,5,11
	.word	30615
	.byte	10
	.byte	'CH11',0,48
	.word	33072
	.byte	3,35,192,5,10
	.byte	'reserved_2F0',0,16
	.word	32552
	.byte	3,35,240,5,11
	.word	30615
	.byte	10
	.byte	'CH12',0,48
	.word	33115
	.byte	3,35,128,6,10
	.byte	'reserved_330',0,16
	.word	32552
	.byte	3,35,176,6,11
	.word	30615
	.byte	10
	.byte	'CH13',0,48
	.word	33158
	.byte	3,35,192,6,10
	.byte	'reserved_370',0,16
	.word	32552
	.byte	3,35,240,6,11
	.word	30615
	.byte	10
	.byte	'CH14',0,48
	.word	33201
	.byte	3,35,128,7,10
	.byte	'reserved_3B0',0,16
	.word	32552
	.byte	3,35,176,7,11
	.word	30615
	.byte	10
	.byte	'CH15',0,48
	.word	33244
	.byte	3,35,192,7,12,144,8
	.word	231
	.byte	13,143,8,0,10
	.byte	'reserved_3F0',0,144,8
	.word	33264
	.byte	3,35,240,7,0,11
	.word	32286
	.byte	4
	.byte	'Ifx_GTM_TOM',0,3,199,18,3
	.word	33300
	.byte	14,4,130,4,20,64,10
	.byte	'CTRL',0,4
	.word	27855
	.byte	2,35,0,10
	.byte	'SR0',0,4
	.word	28219
	.byte	2,35,4,10
	.byte	'SR1',0,4
	.word	28287
	.byte	2,35,8,10
	.byte	'CM0',0,4
	.word	27651
	.byte	2,35,12,10
	.byte	'CM1',0,4
	.word	27719
	.byte	2,35,16,10
	.byte	'CN0',0,4
	.word	27787
	.byte	2,35,20,10
	.byte	'STAT',0,4
	.word	28355
	.byte	2,35,24,10
	.byte	'IRQ_NOTIFY',0,4
	.word	28144
	.byte	2,35,28,10
	.byte	'IRQ_EN',0,4
	.word	27924
	.byte	2,35,32,10
	.byte	'IRQ_FORCINT',0,4
	.word	27995
	.byte	2,35,36,10
	.byte	'IRQ_MODE',0,4
	.word	28071
	.byte	2,35,40,12,20
	.word	231
	.byte	13,19,0,10
	.byte	'reserved_2C',0,20
	.word	33500
	.byte	2,35,44,0,11
	.word	33326
	.byte	4
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,4,155,4,4
	.word	33531
	.byte	14,4,157,4,20,128,4,10
	.byte	'GLB_CTRL',0,4
	.word	28727
	.byte	2,35,0,10
	.byte	'ACT_TB',0,4
	.word	28424
	.byte	2,35,4,10
	.byte	'FUPD_CTRL',0,4
	.word	28651
	.byte	2,35,8,10
	.byte	'INT_TRIG',0,4
	.word	28802
	.byte	2,35,12,12,48
	.word	231
	.byte	13,47,0,10
	.byte	'reserved_tgc0',0,48
	.word	33643
	.byte	2,35,16,10
	.byte	'ENDIS_CTRL',0,4
	.word	28497
	.byte	2,35,64,10
	.byte	'ENDIS_STAT',0,4
	.word	28574
	.byte	2,35,68,10
	.byte	'OUTEN_CTRL',0,4
	.word	28877
	.byte	2,35,72,10
	.byte	'OUTEN_STAT',0,4
	.word	28954
	.byte	2,35,76,12,176,3
	.word	231
	.byte	13,175,3,0,10
	.byte	'reserved_tgc1',0,176,3
	.word	33755
	.byte	2,35,80,0,11
	.word	33565
	.byte	4
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,4,177,4,5
	.word	33791
	.byte	14,4,179,4,20,128,16,10
	.byte	'reserved_tom0',0,48
	.word	33643
	.byte	2,35,0,12,128,8
	.word	33565
	.byte	13,1,0,11
	.word	33856
	.byte	10
	.byte	'TGC',0,128,8
	.word	33866
	.byte	2,35,48,12,208,7
	.word	231
	.byte	13,207,7,0,10
	.byte	'reserved_tgc2',0,208,7
	.word	33885
	.byte	3,35,176,8,0,11
	.word	33826
	.byte	4
	.byte	'Ifx_GTM_TOM_TGCx',0,4,184,4,5
	.word	33922
	.byte	14,4,187,4,20,128,16,12,128,8
	.word	33326
	.byte	13,15,0,11
	.word	33960
	.byte	10
	.byte	'CH',0,128,8
	.word	33970
	.byte	2,35,0,12,128,8
	.word	231
	.byte	13,255,7,0,10
	.byte	'reserved_tom1',0,128,8
	.word	33988
	.byte	3,35,128,8,0,11
	.word	33953
	.byte	4
	.byte	'Ifx_GTM_TOM_CHx',0,4,191,4,5
	.word	34025
	.byte	14,4,212,4,20,128,1,10
	.byte	'CH_GPR0',0,4
	.word	26878
	.byte	2,35,0,10
	.byte	'CH_GPR1',0,4
	.word	26947
	.byte	2,35,4,10
	.byte	'CH_CNT',0,4
	.word	26319
	.byte	2,35,8,10
	.byte	'CH_ECNT',0,4
	.word	26525
	.byte	2,35,12,10
	.byte	'CH_CNTS',0,4
	.word	26387
	.byte	2,35,16,10
	.byte	'CH_TDUC',0,4
	.word	27311
	.byte	2,35,20,10
	.byte	'CH_TDUV',0,4
	.word	27380
	.byte	2,35,24,10
	.byte	'CH_FLT_RE',0,4
	.word	26807
	.byte	2,35,28,10
	.byte	'CH_FLT_FE',0,4
	.word	26736
	.byte	2,35,32,10
	.byte	'CH_CTRL',0,4
	.word	26456
	.byte	2,35,36,10
	.byte	'CH_ECTRL',0,4
	.word	26594
	.byte	2,35,40,10
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	27236
	.byte	2,35,44,10
	.byte	'CH_IRQ_EN',0,4
	.word	27016
	.byte	2,35,48,10
	.byte	'CH_IRQ_FORCINT',0,4
	.word	27087
	.byte	2,35,52,10
	.byte	'CH_IRQ_MODE',0,4
	.word	27163
	.byte	2,35,56,10
	.byte	'CH_EIRQ_EN',0,4
	.word	26664
	.byte	2,35,60,12,64
	.word	231
	.byte	13,63,0,10
	.byte	'reserved_40',0,64
	.word	34360
	.byte	2,35,64,0,11
	.word	34055
	.byte	4
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,4,248,4,4
	.word	34391
	.byte	14,4,250,4,20,8,10
	.byte	'IN_SRC',0,4
	.word	27449
	.byte	2,35,0,10
	.byte	'RST',0,4
	.word	27586
	.byte	2,35,4,0,11
	.word	34425
	.byte	4
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,4,255,4,4
	.word	34461
	.byte	14,4,129,5,21,128,16,12,128,8
	.word	34055
	.byte	13,7,0,11
	.word	34512
	.byte	10
	.byte	'CH',0,128,8
	.word	34522
	.byte	2,35,0,10
	.byte	'reserved_tim1',0,128,8
	.word	33988
	.byte	3,35,128,8,0,11
	.word	34505
	.byte	4
	.byte	'Ifx_GTM_TIM_CHx',0,4,133,5,4
	.word	34566
	.byte	14,4,135,5,20,128,16,12,120
	.word	231
	.byte	13,119,0,10
	.byte	'reserved_tim2',0,120
	.word	34603
	.byte	2,35,0,11
	.word	34425
	.byte	10
	.byte	'IN_SRC_RESET',0,8
	.word	34635
	.byte	2,35,120,12,128,15
	.word	231
	.byte	13,255,14,0,10
	.byte	'reserved_tim3',0,128,15
	.word	34662
	.byte	3,35,128,1,0,11
	.word	34596
	.byte	4
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,4,140,5,4
	.word	34699
	.byte	15,4,174,5,11,1,16
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,16
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,16
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,16
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,16
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,16
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,16
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,16
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,4
	.byte	'Gtm_ConfigurableClockType',0,4,184,5,4
	.word	34737
	.byte	15,4,188,5,11,1,16
	.byte	'GTM_LOW',0,0,16
	.byte	'GTM_HIGH',0,1,0,4
	.byte	'Gtm_OutputLevelType',0,4,192,5,4
	.word	34971
	.byte	15,4,195,5,11,1,16
	.byte	'TOM_GLB_CTRL',0,0,16
	.byte	'TOM_ACT_TB',0,1,16
	.byte	'TOM_FUPD_CTRL',0,2,16
	.byte	'TOM_INT_TRIG',0,3,16
	.byte	'TOM_RESERVED_0',0,4,16
	.byte	'TOM_RESERVED_1',0,5,16
	.byte	'TOM_RESERVED_2',0,6,16
	.byte	'TOM_RESERVED_3',0,7,16
	.byte	'TOM_RESERVED_4',0,8,16
	.byte	'TOM_RESERVED_5',0,9,16
	.byte	'TOM_RESERVED_6',0,10,16
	.byte	'TOM_RESERVED_7',0,11,16
	.byte	'TOM_RESERVED_8',0,12,16
	.byte	'TOM_RESERVED_9',0,13,16
	.byte	'TOM_RESERVED_10',0,14,16
	.byte	'TOM_RESERVED_11',0,15,16
	.byte	'TOM_ENDIS_CTRL',0,16,16
	.byte	'TOM_ENDIS_STAT',0,17,16
	.byte	'TOM_OUTEN_CTRL',0,18,16
	.byte	'TOM_OUTEN_STAT',0,19,0,4
	.byte	'Gtm_TomTimerRegistersType',0,4,217,5,4
	.word	35028
	.byte	14,4,221,5,11,8,10
	.byte	'FltRisingEdge',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'FltFallingEdge',0,4
	.word	299
	.byte	2,35,4,0,4
	.byte	'Gtm_TimFilterType',0,4,225,5,4
	.word	35403
	.byte	4
	.byte	'Gtm_TbuChCtrlType',0,4,230,5,32
	.word	25831
	.byte	4
	.byte	'Gtm_TbuChBaseType',0,4,231,5,32
	.word	25761
	.byte	14,4,233,5,11,8,10
	.byte	'CH_CTRL',0,4
	.word	25831
	.byte	2,35,0,10
	.byte	'CH_BASE',0,4
	.word	25761
	.byte	2,35,4,0,4
	.byte	'Gtm_TbuChType',0,4,237,5,4
	.word	35538
	.byte	14,4,249,5,9,36,12,4
	.word	299
	.byte	13,0,0,10
	.byte	'TimInSel',0,4
	.word	35608
	.byte	2,35,0,12,32
	.word	299
	.byte	13,7,0,10
	.byte	'ToutSel',0,32
	.word	35635
	.byte	2,35,4,0,4
	.byte	'Gtm_PortConfigType',0,4,253,5,2
	.word	35602
	.byte	14,4,129,6,9,8,10
	.byte	'TimRisingEdgeFilter',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'TimFallingEdgeFilter',0,4
	.word	299
	.byte	2,35,4,0,4
	.byte	'Gtm_TimFltType',0,4,134,6,2
	.word	35690
	.byte	14,4,138,6,11,24,10
	.byte	'TimUsage',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'TimIrqEn',0,1
	.word	231
	.byte	2,35,1,10
	.byte	'TimErrIrqEn',0,1
	.word	231
	.byte	2,35,2,10
	.byte	'TimExtCapSrc',0,1
	.word	231
	.byte	2,35,3,10
	.byte	'TimCtrlValue',0,4
	.word	299
	.byte	2,35,4,17
	.word	35690
	.byte	3
	.word	35887
	.byte	10
	.byte	'GtmTimFltPtr',0,4
	.word	35892
	.byte	2,35,8,10
	.byte	'TimCntsValue',0,4
	.word	299
	.byte	2,35,12,10
	.byte	'TimTduValue',0,4
	.word	299
	.byte	2,35,16,10
	.byte	'TimInSrcSel',0,4
	.word	299
	.byte	2,35,20,0,4
	.byte	'Gtm_TimConfigType',0,4,151,6,4
	.word	35780
	.byte	14,4,154,6,11,40,12,8
	.word	231
	.byte	13,7,0,10
	.byte	'Gtm_TimUsage',0,8
	.word	36017
	.byte	2,35,0,12,16
	.word	231
	.byte	13,15,0,12,32
	.word	36048
	.byte	13,1,0,10
	.byte	'Gtm_TomUsage',0,32
	.word	36057
	.byte	2,35,8,0,4
	.byte	'Gtm_ModUsageConfigType',0,4,163,6,4
	.word	36011
	.byte	14,4,177,6,9,16,10
	.byte	'GtmTomUpdateEn',0,2
	.word	262
	.byte	2,35,0,10
	.byte	'GtmTomEndisCtrl',0,2
	.word	262
	.byte	2,35,2,10
	.byte	'GtmTomEndisStat',0,2
	.word	262
	.byte	2,35,4,10
	.byte	'GtmTomOutenCtrl',0,2
	.word	262
	.byte	2,35,6,10
	.byte	'GtmTomOutenStat',0,2
	.word	262
	.byte	2,35,8,10
	.byte	'GtmTomFupd',0,4
	.word	299
	.byte	2,35,10,0,4
	.byte	'Gtm_TomTgcConfigGroupType',0,4,185,6,2
	.word	36121
	.byte	14,4,189,6,9,12,10
	.byte	'GtmTomIntTrig',0,2
	.word	262
	.byte	2,35,0,10
	.byte	'GtmTomActTb',0,4
	.word	299
	.byte	2,35,2,17
	.word	36121
	.byte	3
	.word	36357
	.byte	10
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	36362
	.byte	2,35,8,0,4
	.byte	'Gtm_TomTgcConfigType',0,4,196,6,2
	.word	36307
	.byte	14,4,199,6,9,12,10
	.byte	'GtmTomIrqEn',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'GtmTomCn0Value',0,2
	.word	262
	.byte	2,35,2,10
	.byte	'GtmTomCm0Value',0,2
	.word	262
	.byte	2,35,4,10
	.byte	'GtmTomCm1Value',0,2
	.word	262
	.byte	2,35,6,10
	.byte	'GtmTomSr0Value',0,2
	.word	262
	.byte	2,35,8,10
	.byte	'GtmTomSr1Value',0,2
	.word	262
	.byte	2,35,10,0,4
	.byte	'Gtm_TomChannelConfigType',0,4,207,6,2
	.word	36429
	.byte	14,4,211,6,9,12,10
	.byte	'TomUsage',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'GtmTomIrqMode',0,1
	.word	231
	.byte	2,35,1,10
	.byte	'GtmTomControlWord',0,4
	.word	299
	.byte	2,35,2,17
	.word	36429
	.byte	3
	.word	36685
	.byte	10
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	36690
	.byte	2,35,8,0,4
	.byte	'Gtm_TomConfigType',0,4,219,6,2
	.word	36611
	.byte	14,4,223,6,9,8,10
	.byte	'CmuEclkNum',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'CmuEclkDen',0,4
	.word	299
	.byte	2,35,4,0,4
	.byte	'Gtm_ExtClkType',0,4,227,6,2
	.word	36752
	.byte	14,4,230,6,9,64,10
	.byte	'GtmClockEnable',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'GtmCmuClkCnt',0,32
	.word	35635
	.byte	2,35,4,10
	.byte	'GtmFxdClkControl',0,4
	.word	299
	.byte	2,35,36,12,24
	.word	36752
	.byte	13,2,0,10
	.byte	'GtmEclk',0,24
	.word	36901
	.byte	2,35,40,0,4
	.byte	'Gtm_ClockSettingType',0,4,236,6,2
	.word	36823
	.byte	14,4,240,6,9,4,10
	.byte	'GtmCtrlValue',0,2
	.word	262
	.byte	2,35,0,10
	.byte	'GtmIrqEnable',0,2
	.word	262
	.byte	2,35,2,0,4
	.byte	'Gtm_GeneralConfigType',0,4,245,6,2
	.word	36958
	.byte	14,4,249,6,9,6,10
	.byte	'TbuChannelCtrl',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'TbuBaseValue',0,4
	.word	299
	.byte	2,35,2,0,4
	.byte	'Gtm_TbuConfigType',0,4,253,6,2
	.word	37040
	.byte	14,4,129,7,9,72,10
	.byte	'GtmModuleSleepEnable',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'GtmGclkNum',0,4
	.word	299
	.byte	2,35,2,10
	.byte	'GtmGclkDen',0,4
	.word	299
	.byte	2,35,6,10
	.byte	'GtmAccessEnable0',0,4
	.word	299
	.byte	2,35,10,10
	.byte	'GtmAccessEnable1',0,4
	.word	299
	.byte	2,35,14,12,2
	.word	262
	.byte	13,0,0,10
	.byte	'GtmTimModuleUsage',0,2
	.word	37248
	.byte	2,35,18,12,1
	.word	231
	.byte	13,0,0,10
	.byte	'GtmTimUsage',0,1
	.word	37284
	.byte	2,35,20,17
	.word	35780
	.byte	3
	.word	37314
	.byte	10
	.byte	'GtmTimConfigPtr',0,4
	.word	37319
	.byte	2,35,24,10
	.byte	'GtmTomTgcUsage',0,1
	.word	37284
	.byte	2,35,28,17
	.word	36307
	.byte	3
	.word	37373
	.byte	10
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	37378
	.byte	2,35,32,12,8
	.word	299
	.byte	13,1,0,10
	.byte	'GtmTomModuleUsage',0,8
	.word	37411
	.byte	2,35,36,10
	.byte	'GtmTomUsage',0,4
	.word	35608
	.byte	2,35,44,17
	.word	36611
	.byte	3
	.word	37468
	.byte	10
	.byte	'GtmTomConfigPtr',0,4
	.word	37473
	.byte	2,35,48,17
	.word	36011
	.byte	3
	.word	37503
	.byte	10
	.byte	'GtmModUsageConfigPtr',0,4
	.word	37508
	.byte	2,35,52,17
	.word	36958
	.byte	3
	.word	37543
	.byte	10
	.byte	'GtmGeneralConfigPtr',0,4
	.word	37548
	.byte	2,35,56,17
	.word	37040
	.byte	3
	.word	37582
	.byte	10
	.byte	'GtmTbuConfigPtr',0,4
	.word	37587
	.byte	2,35,60,17
	.word	231
	.byte	3
	.word	37617
	.byte	10
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	37622
	.byte	2,35,64,10
	.byte	'GtmTtcanTriggers',0,2
	.word	37248
	.byte	2,35,68,0,4
	.byte	'Gtm_ModuleConfigType',0,4,163,7,2
	.word	37120
	.byte	18,1,1,19
	.word	231
	.byte	19
	.word	231
	.byte	19
	.word	231
	.byte	19
	.word	262
	.byte	0,3
	.word	37714
	.byte	4
	.byte	'Gtm_NotificationPtrType',0,4,172,7,16
	.word	37738
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,23,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0
	.byte	0,11,53,0,73,19,0,0,12,1,1,11,15,73,19,0,0,13,33,0,47,15,0,0,14,19,1,58,15,59,15,57,15,11,15,0,0,15,4
	.byte	1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0,17,38,0,73,19,0,0,18,21,1,54,15,39,12,0,0,19,5,0,73
	.byte	19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Irq.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxGtm_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Gtm.h',0,0,0,0,0
.L9:
.L7:

; ..\mcal_src\Gtm_Irq.c	     1  /******************************************************************************
; ..\mcal_src\Gtm_Irq.c	     2  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_src\Gtm_Irq.c	     4  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Gtm_Irq.c	     6  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Gtm_Irq.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Gtm_Irq.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Gtm_Irq.c	    10  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    11  *******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    12  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    13  **  $FILENAME   : Gtm_Irq.c $                                                **
; ..\mcal_src\Gtm_Irq.c	    14  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    15  **  $CC VERSION : \main\17 $                                                 **
; ..\mcal_src\Gtm_Irq.c	    16  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    17  **  $DATE       : 2016-06-27 $                                               **
; ..\mcal_src\Gtm_Irq.c	    18  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Gtm_Irq.c	    20  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Gtm_Irq.c	    22  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    23  **  DESCRIPTION : This file contains the interrupt frames for the GTM. This  **
; ..\mcal_src\Gtm_Irq.c	    24  **                file is given for evaluation purpose only.                 **
; ..\mcal_src\Gtm_Irq.c	    25  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    26  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\Gtm_Irq.c	    27  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    28  ******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    29  
; ..\mcal_src\Gtm_Irq.c	    30  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    31  **                      Includes                                              **
; ..\mcal_src\Gtm_Irq.c	    32  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    33  /* Include GTM header file */
; ..\mcal_src\Gtm_Irq.c	    34  #include "Gtm.h"
; ..\mcal_src\Gtm_Irq.c	    35  
; ..\mcal_src\Gtm_Irq.c	    36  /*Include Irq Module*/
; ..\mcal_src\Gtm_Irq.c	    37  #include "Irq.h"
; ..\mcal_src\Gtm_Irq.c	    38  
; ..\mcal_src\Gtm_Irq.c	    39  #include "Irq_Cfg.h"
; ..\mcal_src\Gtm_Irq.c	    40  
; ..\mcal_src\Gtm_Irq.c	    41  /* Include Mcal.h to import the library functions */ 
; ..\mcal_src\Gtm_Irq.c	    42  #include "Mcal.h"
; ..\mcal_src\Gtm_Irq.c	    43  
; ..\mcal_src\Gtm_Irq.c	    44  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    45  **                      Imported Compiler Switch Checks                       **
; ..\mcal_src\Gtm_Irq.c	    46  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    47  
; ..\mcal_src\Gtm_Irq.c	    48  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    49  **                      Private Macro Definitions                             **
; ..\mcal_src\Gtm_Irq.c	    50  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    51  
; ..\mcal_src\Gtm_Irq.c	    52  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    53  **                      Private Type Definitions                              **
; ..\mcal_src\Gtm_Irq.c	    54  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    55  
; ..\mcal_src\Gtm_Irq.c	    56  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    57  **                      Private Function Declarations                         **
; ..\mcal_src\Gtm_Irq.c	    58  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    59  
; ..\mcal_src\Gtm_Irq.c	    60  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    61  **                      Global Constant Definitions                           **
; ..\mcal_src\Gtm_Irq.c	    62  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    63  
; ..\mcal_src\Gtm_Irq.c	    64  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    65  **                      Global Variable Definitions                           **
; ..\mcal_src\Gtm_Irq.c	    66  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    67  
; ..\mcal_src\Gtm_Irq.c	    68  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    69  **                      Private Constant Definitions                          **
; ..\mcal_src\Gtm_Irq.c	    70  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    71  
; ..\mcal_src\Gtm_Irq.c	    72  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    73  **                      Private Variable Definitions                          **
; ..\mcal_src\Gtm_Irq.c	    74  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    75  
; ..\mcal_src\Gtm_Irq.c	    76  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    77  **                      Private Function Definitions                          **
; ..\mcal_src\Gtm_Irq.c	    78  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    79  
; ..\mcal_src\Gtm_Irq.c	    80  /*******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    81  **                      Global Function Definitions                           **
; ..\mcal_src\Gtm_Irq.c	    82  *******************************************************************************/
; ..\mcal_src\Gtm_Irq.c	    83  #define IRQ_START_SEC_CODE
; ..\mcal_src\Gtm_Irq.c	    84  #include "MemMap.h"
; ..\mcal_src\Gtm_Irq.c	    85  
; ..\mcal_src\Gtm_Irq.c	    86  #if (IRQ_GTM_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	    87  /******************************************************************************
; ..\mcal_src\Gtm_Irq.c	    88  ** Syntax : void GTM_ISR_<MOD>[x]_SRy(void)                                  **
; ..\mcal_src\Gtm_Irq.c	    89  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    90  ** Service ID:       NA                                                      **
; ..\mcal_src\Gtm_Irq.c	    91  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    92  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Gtm_Irq.c	    93  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    94  ** Reentrancy:       reentrant                                               **
; ..\mcal_src\Gtm_Irq.c	    95  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    96  ** Parameters (in):  none                                                    **
; ..\mcal_src\Gtm_Irq.c	    97  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	    98  ** Parameters (out): none                                                    **
; ..\mcal_src\Gtm_Irq.c	    99  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	   100  ** Return value:     none                                                    **
; ..\mcal_src\Gtm_Irq.c	   101  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	   102  ** Description : Service for GTM Service request for Sub Modules             **
; ..\mcal_src\Gtm_Irq.c	   103  **                                                                           **
; ..\mcal_src\Gtm_Irq.c	   104  *****************************************************************************/
; ..\mcal_src\Gtm_Irq.c	   105  #if(IRQ_GTM_TOM0_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   106  #if((IRQ_GTM_TOM0_SR0_PRIO > 0) || (IRQ_GTM_TOM0_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   107  #if((IRQ_GTM_TOM0_SR0_PRIO > 0) && (IRQ_GTM_TOM0_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   108  IFX_INTERRUPT(GTMTOM0SR0_ISR, 0, IRQ_GTM_TOM0_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   109  #elif IRQ_GTM_TOM0_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   110  ISR(GTMTOM0SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   111  #endif
; ..\mcal_src\Gtm_Irq.c	   112  {
; ..\mcal_src\Gtm_Irq.c	   113    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   114  #if (IRQ_GTM_TOM0_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   115    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   116  #endif
; ..\mcal_src\Gtm_Irq.c	   117    
; ..\mcal_src\Gtm_Irq.c	   118    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   119    Gtm_IsrTomModule(0, 0);
; ..\mcal_src\Gtm_Irq.c	   120  }
; ..\mcal_src\Gtm_Irq.c	   121  #endif
; ..\mcal_src\Gtm_Irq.c	   122  
; ..\mcal_src\Gtm_Irq.c	   123  #if((IRQ_GTM_TOM0_SR1_PRIO > 0) || (IRQ_GTM_TOM0_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   124  #if((IRQ_GTM_TOM0_SR1_PRIO > 0) && (IRQ_GTM_TOM0_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   125  IFX_INTERRUPT(GTMTOM0SR1_ISR, 0, IRQ_GTM_TOM0_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	   126  #elif IRQ_GTM_TOM0_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   127  ISR(GTMTOM0SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	   128  #endif
; ..\mcal_src\Gtm_Irq.c	   129  {
; ..\mcal_src\Gtm_Irq.c	   130    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   131  #if (IRQ_GTM_TOM0_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   132    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   133  #endif
; ..\mcal_src\Gtm_Irq.c	   134    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   135    Gtm_IsrTomModule(0, 2);
; ..\mcal_src\Gtm_Irq.c	   136  }
; ..\mcal_src\Gtm_Irq.c	   137  #endif
; ..\mcal_src\Gtm_Irq.c	   138  
; ..\mcal_src\Gtm_Irq.c	   139  
; ..\mcal_src\Gtm_Irq.c	   140  #if((IRQ_GTM_TOM0_SR2_PRIO > 0) || (IRQ_GTM_TOM0_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   141  #if((IRQ_GTM_TOM0_SR2_PRIO > 0) && (IRQ_GTM_TOM0_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   142  IFX_INTERRUPT(GTMTOM0SR2_ISR, 0, IRQ_GTM_TOM0_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	   143  #elif IRQ_GTM_TOM0_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   144  ISR(GTMTOM0SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	   145  #endif
; ..\mcal_src\Gtm_Irq.c	   146  {
; ..\mcal_src\Gtm_Irq.c	   147    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   148  #if (IRQ_GTM_TOM0_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   149    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   150  #endif
; ..\mcal_src\Gtm_Irq.c	   151    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   152    Gtm_IsrTomModule(0, 4);
; ..\mcal_src\Gtm_Irq.c	   153  }
; ..\mcal_src\Gtm_Irq.c	   154  #endif
; ..\mcal_src\Gtm_Irq.c	   155  
; ..\mcal_src\Gtm_Irq.c	   156  #if((IRQ_GTM_TOM0_SR3_PRIO > 0) || (IRQ_GTM_TOM0_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   157  #if((IRQ_GTM_TOM0_SR3_PRIO > 0) && (IRQ_GTM_TOM0_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   158  IFX_INTERRUPT(GTMTOM0SR3_ISR, 0, IRQ_GTM_TOM0_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	   159  #elif IRQ_GTM_TOM0_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   160  ISR(GTMTOM0SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	   161  #endif
; ..\mcal_src\Gtm_Irq.c	   162  {
; ..\mcal_src\Gtm_Irq.c	   163    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   164  #if (IRQ_GTM_TOM0_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   165    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   166  #endif
; ..\mcal_src\Gtm_Irq.c	   167    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   168    Gtm_IsrTomModule(0, 6);
; ..\mcal_src\Gtm_Irq.c	   169  }
; ..\mcal_src\Gtm_Irq.c	   170  #endif
; ..\mcal_src\Gtm_Irq.c	   171  
; ..\mcal_src\Gtm_Irq.c	   172  #if((IRQ_GTM_TOM0_SR4_PRIO > 0) || (IRQ_GTM_TOM0_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   173  #if((IRQ_GTM_TOM0_SR4_PRIO > 0) && (IRQ_GTM_TOM0_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   174  IFX_INTERRUPT(GTMTOM0SR4_ISR, 0, IRQ_GTM_TOM0_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	   175  #elif IRQ_GTM_TOM0_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   176  ISR(GTMTOM0SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	   177  #endif
; ..\mcal_src\Gtm_Irq.c	   178  {
; ..\mcal_src\Gtm_Irq.c	   179    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   180  #if (IRQ_GTM_TOM0_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   181    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   182  #endif
; ..\mcal_src\Gtm_Irq.c	   183    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   184    Gtm_IsrTomModule(0, 8);
; ..\mcal_src\Gtm_Irq.c	   185  }
; ..\mcal_src\Gtm_Irq.c	   186  #endif
; ..\mcal_src\Gtm_Irq.c	   187  
; ..\mcal_src\Gtm_Irq.c	   188  #if((IRQ_GTM_TOM0_SR5_PRIO > 0) || (IRQ_GTM_TOM0_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   189  #if((IRQ_GTM_TOM0_SR5_PRIO > 0) && (IRQ_GTM_TOM0_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   190  IFX_INTERRUPT(GTMTOM0SR5_ISR, 0, IRQ_GTM_TOM0_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	   191  #elif IRQ_GTM_TOM0_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   192  ISR(GTMTOM0SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	   193  #endif
; ..\mcal_src\Gtm_Irq.c	   194  {
; ..\mcal_src\Gtm_Irq.c	   195    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   196  #if (IRQ_GTM_TOM0_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   197    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   198  #endif
; ..\mcal_src\Gtm_Irq.c	   199    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   200    Gtm_IsrTomModule(0, 10);
; ..\mcal_src\Gtm_Irq.c	   201  }
; ..\mcal_src\Gtm_Irq.c	   202  #endif
; ..\mcal_src\Gtm_Irq.c	   203  
; ..\mcal_src\Gtm_Irq.c	   204  #if((IRQ_GTM_TOM0_SR6_PRIO > 0) || (IRQ_GTM_TOM0_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   205  #if((IRQ_GTM_TOM0_SR6_PRIO > 0) && (IRQ_GTM_TOM0_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   206  IFX_INTERRUPT(GTMTOM0SR6_ISR, 0, IRQ_GTM_TOM0_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	   207  #elif IRQ_GTM_TOM0_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   208  ISR(GTMTOM0SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	   209  #endif
; ..\mcal_src\Gtm_Irq.c	   210  {
; ..\mcal_src\Gtm_Irq.c	   211    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   212  #if (IRQ_GTM_TOM0_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   213    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   214  #endif
; ..\mcal_src\Gtm_Irq.c	   215    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   216    Gtm_IsrTomModule(0, 12);
; ..\mcal_src\Gtm_Irq.c	   217  }
; ..\mcal_src\Gtm_Irq.c	   218  #endif
; ..\mcal_src\Gtm_Irq.c	   219  
; ..\mcal_src\Gtm_Irq.c	   220  #if((IRQ_GTM_TOM0_SR7_PRIO > 0) || (IRQ_GTM_TOM0_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   221  #if((IRQ_GTM_TOM0_SR7_PRIO > 0) && (IRQ_GTM_TOM0_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   222  IFX_INTERRUPT(GTMTOM0SR7_ISR, 0, IRQ_GTM_TOM0_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	   223  #elif IRQ_GTM_TOM0_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   224  ISR(GTMTOM0SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	   225  #endif
; ..\mcal_src\Gtm_Irq.c	   226  {
; ..\mcal_src\Gtm_Irq.c	   227    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   228  #if (IRQ_GTM_TOM0_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   229    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   230  #endif
; ..\mcal_src\Gtm_Irq.c	   231    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   232    Gtm_IsrTomModule(0, 14);
; ..\mcal_src\Gtm_Irq.c	   233  }
; ..\mcal_src\Gtm_Irq.c	   234  #endif
; ..\mcal_src\Gtm_Irq.c	   235  
; ..\mcal_src\Gtm_Irq.c	   236  
; ..\mcal_src\Gtm_Irq.c	   237  #endif /* #if(IRQ_GTM_TOM0_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	   238  
; ..\mcal_src\Gtm_Irq.c	   239  
; ..\mcal_src\Gtm_Irq.c	   240  #if(IRQ_GTM_TOM1_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   241  
; ..\mcal_src\Gtm_Irq.c	   242  #if((IRQ_GTM_TOM1_SR0_PRIO > 0) || (IRQ_GTM_TOM1_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   243  #if((IRQ_GTM_TOM1_SR0_PRIO > 0) && (IRQ_GTM_TOM1_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   244  IFX_INTERRUPT(GTMTOM1SR0_ISR, 0, IRQ_GTM_TOM1_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   245  #elif IRQ_GTM_TOM1_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   246  ISR(GTMTOM1SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   247  #endif
; ..\mcal_src\Gtm_Irq.c	   248  {
; ..\mcal_src\Gtm_Irq.c	   249    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   250  #if (IRQ_GTM_TOM1_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   251    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   252  #endif
; ..\mcal_src\Gtm_Irq.c	   253    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   254    Gtm_IsrTomModule(1, 0);
; ..\mcal_src\Gtm_Irq.c	   255  }
; ..\mcal_src\Gtm_Irq.c	   256  #endif
; ..\mcal_src\Gtm_Irq.c	   257  
; ..\mcal_src\Gtm_Irq.c	   258  #if((IRQ_GTM_TOM1_SR1_PRIO > 0) || (IRQ_GTM_TOM1_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   259  #if((IRQ_GTM_TOM1_SR1_PRIO > 0) && (IRQ_GTM_TOM1_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   260  IFX_INTERRUPT(GTMTOM1SR1_ISR, 0, IRQ_GTM_TOM1_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	   261  #elif IRQ_GTM_TOM1_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   262  ISR(GTMTOM1SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	   263  #endif
; ..\mcal_src\Gtm_Irq.c	   264  {
; ..\mcal_src\Gtm_Irq.c	   265    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   266  #if (IRQ_GTM_TOM1_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   267    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   268  #endif
; ..\mcal_src\Gtm_Irq.c	   269    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   270    Gtm_IsrTomModule(1, 2);
; ..\mcal_src\Gtm_Irq.c	   271  }
; ..\mcal_src\Gtm_Irq.c	   272  #endif
; ..\mcal_src\Gtm_Irq.c	   273  
; ..\mcal_src\Gtm_Irq.c	   274  
; ..\mcal_src\Gtm_Irq.c	   275  #if((IRQ_GTM_TOM1_SR2_PRIO > 0) || (IRQ_GTM_TOM1_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   276  #if((IRQ_GTM_TOM1_SR2_PRIO > 0) && (IRQ_GTM_TOM1_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   277  IFX_INTERRUPT(GTMTOM1SR2_ISR, 0, IRQ_GTM_TOM1_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	   278  #elif IRQ_GTM_TOM1_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   279  ISR(GTMTOM1SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	   280  #endif
; ..\mcal_src\Gtm_Irq.c	   281  {
; ..\mcal_src\Gtm_Irq.c	   282    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   283  #if (IRQ_GTM_TOM1_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   284    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   285  #endif
; ..\mcal_src\Gtm_Irq.c	   286    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   287    Gtm_IsrTomModule(1, 4);
; ..\mcal_src\Gtm_Irq.c	   288  }
; ..\mcal_src\Gtm_Irq.c	   289  #endif
; ..\mcal_src\Gtm_Irq.c	   290  
; ..\mcal_src\Gtm_Irq.c	   291  #if((IRQ_GTM_TOM1_SR3_PRIO > 0) || (IRQ_GTM_TOM1_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   292  #if((IRQ_GTM_TOM1_SR3_PRIO > 0) && (IRQ_GTM_TOM1_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   293  IFX_INTERRUPT(GTMTOM1SR3_ISR, 0, IRQ_GTM_TOM1_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	   294  #elif IRQ_GTM_TOM1_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   295  ISR(GTMTOM1SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	   296  #endif
; ..\mcal_src\Gtm_Irq.c	   297  {
; ..\mcal_src\Gtm_Irq.c	   298    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   299  #if (IRQ_GTM_TOM1_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   300    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   301  #endif
; ..\mcal_src\Gtm_Irq.c	   302    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   303    Gtm_IsrTomModule(1, 6);
; ..\mcal_src\Gtm_Irq.c	   304  }
; ..\mcal_src\Gtm_Irq.c	   305  #endif
; ..\mcal_src\Gtm_Irq.c	   306  
; ..\mcal_src\Gtm_Irq.c	   307  #if((IRQ_GTM_TOM1_SR4_PRIO > 0) || (IRQ_GTM_TOM1_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   308  #if((IRQ_GTM_TOM1_SR4_PRIO > 0) && (IRQ_GTM_TOM1_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   309  IFX_INTERRUPT(GTMTOM1SR4_ISR, 0, IRQ_GTM_TOM1_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	   310  #elif IRQ_GTM_TOM1_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   311  ISR(GTMTOM1SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	   312  #endif
; ..\mcal_src\Gtm_Irq.c	   313  {
; ..\mcal_src\Gtm_Irq.c	   314    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   315  #if (IRQ_GTM_TOM1_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   316    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   317  #endif
; ..\mcal_src\Gtm_Irq.c	   318    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   319    Gtm_IsrTomModule(1, 8);
; ..\mcal_src\Gtm_Irq.c	   320  }
; ..\mcal_src\Gtm_Irq.c	   321  #endif
; ..\mcal_src\Gtm_Irq.c	   322  
; ..\mcal_src\Gtm_Irq.c	   323  #if((IRQ_GTM_TOM1_SR5_PRIO > 0) || (IRQ_GTM_TOM1_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   324  #if((IRQ_GTM_TOM1_SR5_PRIO > 0) && (IRQ_GTM_TOM1_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   325  IFX_INTERRUPT(GTMTOM1SR5_ISR, 0, IRQ_GTM_TOM1_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	   326  #elif IRQ_GTM_TOM1_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   327  ISR(GTMTOM1SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	   328  #endif
; ..\mcal_src\Gtm_Irq.c	   329  {
; ..\mcal_src\Gtm_Irq.c	   330    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   331  #if (IRQ_GTM_TOM1_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   332    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   333  #endif
; ..\mcal_src\Gtm_Irq.c	   334    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   335    Gtm_IsrTomModule(1, 10);
; ..\mcal_src\Gtm_Irq.c	   336  }
; ..\mcal_src\Gtm_Irq.c	   337  #endif
; ..\mcal_src\Gtm_Irq.c	   338  
; ..\mcal_src\Gtm_Irq.c	   339  #if((IRQ_GTM_TOM1_SR6_PRIO > 0) || (IRQ_GTM_TOM1_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   340  #if((IRQ_GTM_TOM1_SR6_PRIO > 0) && (IRQ_GTM_TOM1_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   341  IFX_INTERRUPT(GTMTOM1SR6_ISR, 0, IRQ_GTM_TOM1_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	   342  #elif IRQ_GTM_TOM1_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   343  ISR(GTMTOM1SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	   344  #endif
; ..\mcal_src\Gtm_Irq.c	   345  {
; ..\mcal_src\Gtm_Irq.c	   346    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   347  #if (IRQ_GTM_TOM1_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   348    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   349  #endif
; ..\mcal_src\Gtm_Irq.c	   350    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   351    Gtm_IsrTomModule(1, 12);
; ..\mcal_src\Gtm_Irq.c	   352  }
; ..\mcal_src\Gtm_Irq.c	   353  #endif
; ..\mcal_src\Gtm_Irq.c	   354  
; ..\mcal_src\Gtm_Irq.c	   355  #if((IRQ_GTM_TOM1_SR7_PRIO > 0) || (IRQ_GTM_TOM1_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   356  #if((IRQ_GTM_TOM1_SR7_PRIO > 0) && (IRQ_GTM_TOM1_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   357  IFX_INTERRUPT(GTMTOM1SR7_ISR, 0, IRQ_GTM_TOM1_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	   358  #elif IRQ_GTM_TOM1_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   359  ISR(GTMTOM1SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	   360  #endif
; ..\mcal_src\Gtm_Irq.c	   361  {
; ..\mcal_src\Gtm_Irq.c	   362    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   363  #if (IRQ_GTM_TOM1_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   364    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   365  #endif
; ..\mcal_src\Gtm_Irq.c	   366    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   367    Gtm_IsrTomModule(1, 14);
; ..\mcal_src\Gtm_Irq.c	   368  }
; ..\mcal_src\Gtm_Irq.c	   369  #endif
; ..\mcal_src\Gtm_Irq.c	   370  
; ..\mcal_src\Gtm_Irq.c	   371  #endif /* #if(IRQ_GTM_TOM1_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	   372  
; ..\mcal_src\Gtm_Irq.c	   373  #if(IRQ_GTM_TOM2_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   374  
; ..\mcal_src\Gtm_Irq.c	   375  #if((IRQ_GTM_TOM2_SR0_PRIO > 0) || (IRQ_GTM_TOM2_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   376  #if((IRQ_GTM_TOM2_SR0_PRIO > 0) && (IRQ_GTM_TOM2_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   377  IFX_INTERRUPT(GTMTOM2SR0_ISR, 0, IRQ_GTM_TOM2_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   378  #elif IRQ_GTM_TOM2_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   379  ISR(GTMTOM2SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   380  #endif
; ..\mcal_src\Gtm_Irq.c	   381  {
; ..\mcal_src\Gtm_Irq.c	   382    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   383  #if (IRQ_GTM_TOM2_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   384    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   385  #endif
; ..\mcal_src\Gtm_Irq.c	   386    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   387    Gtm_IsrTomModule(2, 0);
; ..\mcal_src\Gtm_Irq.c	   388  }
; ..\mcal_src\Gtm_Irq.c	   389  #endif
; ..\mcal_src\Gtm_Irq.c	   390  
; ..\mcal_src\Gtm_Irq.c	   391  #if((IRQ_GTM_TOM2_SR1_PRIO > 0) || (IRQ_GTM_TOM2_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   392  #if((IRQ_GTM_TOM2_SR1_PRIO > 0) && (IRQ_GTM_TOM2_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   393  IFX_INTERRUPT(GTMTOM2SR1_ISR, 0, IRQ_GTM_TOM2_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	   394  #elif IRQ_GTM_TOM2_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   395  ISR(GTMTOM2SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	   396  #endif
; ..\mcal_src\Gtm_Irq.c	   397  {
; ..\mcal_src\Gtm_Irq.c	   398    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   399  #if (IRQ_GTM_TOM2_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   400    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   401  #endif
; ..\mcal_src\Gtm_Irq.c	   402    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   403    Gtm_IsrTomModule(2, 2);
; ..\mcal_src\Gtm_Irq.c	   404  }
; ..\mcal_src\Gtm_Irq.c	   405  #endif
; ..\mcal_src\Gtm_Irq.c	   406  
; ..\mcal_src\Gtm_Irq.c	   407  #if((IRQ_GTM_TOM2_SR2_PRIO > 0) || (IRQ_GTM_TOM2_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   408  #if((IRQ_GTM_TOM2_SR2_PRIO > 0) && (IRQ_GTM_TOM2_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   409  IFX_INTERRUPT(GTMTOM2SR2_ISR, 0, IRQ_GTM_TOM2_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	   410  #elif IRQ_GTM_TOM2_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   411  ISR(GTMTOM2SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	   412  #endif
; ..\mcal_src\Gtm_Irq.c	   413  {
; ..\mcal_src\Gtm_Irq.c	   414    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   415  #if (IRQ_GTM_TOM2_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   416    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   417  #endif
; ..\mcal_src\Gtm_Irq.c	   418    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   419    Gtm_IsrTomModule(2, 4);
; ..\mcal_src\Gtm_Irq.c	   420  }
; ..\mcal_src\Gtm_Irq.c	   421  #endif
; ..\mcal_src\Gtm_Irq.c	   422  
; ..\mcal_src\Gtm_Irq.c	   423  #if((IRQ_GTM_TOM2_SR3_PRIO > 0) || (IRQ_GTM_TOM2_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   424  #if((IRQ_GTM_TOM2_SR3_PRIO > 0) && (IRQ_GTM_TOM2_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   425  IFX_INTERRUPT(GTMTOM2SR3_ISR, 0, IRQ_GTM_TOM2_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	   426  #elif IRQ_GTM_TOM2_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   427  ISR(GTMTOM2SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	   428  #endif
; ..\mcal_src\Gtm_Irq.c	   429  {
; ..\mcal_src\Gtm_Irq.c	   430    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   431  #if (IRQ_GTM_TOM2_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   432    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   433  #endif
; ..\mcal_src\Gtm_Irq.c	   434    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   435    Gtm_IsrTomModule(2, 6);
; ..\mcal_src\Gtm_Irq.c	   436  }
; ..\mcal_src\Gtm_Irq.c	   437  #endif
; ..\mcal_src\Gtm_Irq.c	   438  
; ..\mcal_src\Gtm_Irq.c	   439  #if((IRQ_GTM_TOM2_SR4_PRIO > 0) || (IRQ_GTM_TOM2_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   440  #if((IRQ_GTM_TOM2_SR4_PRIO > 0) && (IRQ_GTM_TOM2_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   441  IFX_INTERRUPT(GTMTOM2SR4_ISR, 0, IRQ_GTM_TOM2_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	   442  #elif IRQ_GTM_TOM2_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   443  ISR(GTMTOM2SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	   444  #endif
; ..\mcal_src\Gtm_Irq.c	   445  {
; ..\mcal_src\Gtm_Irq.c	   446    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   447  #if (IRQ_GTM_TOM2_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   448    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   449  #endif
; ..\mcal_src\Gtm_Irq.c	   450    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   451    Gtm_IsrTomModule(2, 8);
; ..\mcal_src\Gtm_Irq.c	   452  }
; ..\mcal_src\Gtm_Irq.c	   453  #endif
; ..\mcal_src\Gtm_Irq.c	   454  
; ..\mcal_src\Gtm_Irq.c	   455  #if((IRQ_GTM_TOM2_SR5_PRIO > 0) || (IRQ_GTM_TOM2_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   456  #if((IRQ_GTM_TOM2_SR5_PRIO > 0) && (IRQ_GTM_TOM2_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   457  IFX_INTERRUPT(GTMTOM2SR5_ISR, 0, IRQ_GTM_TOM2_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	   458  #elif IRQ_GTM_TOM2_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   459  ISR(GTMTOM2SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	   460  #endif
; ..\mcal_src\Gtm_Irq.c	   461  {
; ..\mcal_src\Gtm_Irq.c	   462    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   463  #if (IRQ_GTM_TOM2_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   464    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   465  #endif
; ..\mcal_src\Gtm_Irq.c	   466    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   467    Gtm_IsrTomModule(2, 10);
; ..\mcal_src\Gtm_Irq.c	   468  }
; ..\mcal_src\Gtm_Irq.c	   469  #endif
; ..\mcal_src\Gtm_Irq.c	   470  
; ..\mcal_src\Gtm_Irq.c	   471  #if((IRQ_GTM_TOM2_SR6_PRIO > 0) || (IRQ_GTM_TOM2_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   472  #if((IRQ_GTM_TOM2_SR6_PRIO > 0) && (IRQ_GTM_TOM2_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   473  IFX_INTERRUPT(GTMTOM2SR6_ISR, 0, IRQ_GTM_TOM2_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	   474  #elif IRQ_GTM_TOM2_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   475  ISR(GTMTOM2SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	   476  #endif
; ..\mcal_src\Gtm_Irq.c	   477  {
; ..\mcal_src\Gtm_Irq.c	   478    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   479  #if (IRQ_GTM_TOM2_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   480    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   481  #endif
; ..\mcal_src\Gtm_Irq.c	   482    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   483    Gtm_IsrTomModule(2, 12);
; ..\mcal_src\Gtm_Irq.c	   484  }
; ..\mcal_src\Gtm_Irq.c	   485  #endif
; ..\mcal_src\Gtm_Irq.c	   486  
; ..\mcal_src\Gtm_Irq.c	   487  #if((IRQ_GTM_TOM2_SR7_PRIO > 0) || (IRQ_GTM_TOM2_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   488  #if((IRQ_GTM_TOM2_SR7_PRIO > 0) && (IRQ_GTM_TOM2_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   489  IFX_INTERRUPT(GTMTOM2SR7_ISR, 0, IRQ_GTM_TOM2_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	   490  #elif IRQ_GTM_TOM2_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   491  ISR(GTMTOM2SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	   492  #endif
; ..\mcal_src\Gtm_Irq.c	   493  {
; ..\mcal_src\Gtm_Irq.c	   494    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   495  #if (IRQ_GTM_TOM2_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   496    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   497  #endif
; ..\mcal_src\Gtm_Irq.c	   498    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   499    Gtm_IsrTomModule(2, 14);
; ..\mcal_src\Gtm_Irq.c	   500  }
; ..\mcal_src\Gtm_Irq.c	   501  #endif
; ..\mcal_src\Gtm_Irq.c	   502  
; ..\mcal_src\Gtm_Irq.c	   503  #endif /* #if(IRQ_GTM_TOM2_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	   504  
; ..\mcal_src\Gtm_Irq.c	   505  
; ..\mcal_src\Gtm_Irq.c	   506  
; ..\mcal_src\Gtm_Irq.c	   507  #if(IRQ_GTM_TOM3_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   508  
; ..\mcal_src\Gtm_Irq.c	   509  #if((IRQ_GTM_TOM3_SR0_PRIO > 0) || (IRQ_GTM_TOM3_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   510  #if((IRQ_GTM_TOM3_SR0_PRIO > 0) && (IRQ_GTM_TOM3_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   511  IFX_INTERRUPT(GTMTOM3SR0_ISR, 0, IRQ_GTM_TOM3_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   512  #elif IRQ_GTM_TOM3_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   513  ISR(GTMTOM3SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   514  #endif
; ..\mcal_src\Gtm_Irq.c	   515  {
; ..\mcal_src\Gtm_Irq.c	   516    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   517  #if (IRQ_GTM_TOM3_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   518    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   519  #endif
; ..\mcal_src\Gtm_Irq.c	   520    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   521    Gtm_IsrTomModule(3, 0);
; ..\mcal_src\Gtm_Irq.c	   522  }
; ..\mcal_src\Gtm_Irq.c	   523  #endif
; ..\mcal_src\Gtm_Irq.c	   524  
; ..\mcal_src\Gtm_Irq.c	   525  #if((IRQ_GTM_TOM3_SR1_PRIO > 0) || (IRQ_GTM_TOM3_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   526  #if((IRQ_GTM_TOM3_SR1_PRIO > 0) && (IRQ_GTM_TOM3_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   527  IFX_INTERRUPT(GTMTOM3SR1_ISR, 0, IRQ_GTM_TOM3_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	   528  #elif IRQ_GTM_TOM3_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   529  ISR(GTMTOM3SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	   530  #endif
; ..\mcal_src\Gtm_Irq.c	   531  {
; ..\mcal_src\Gtm_Irq.c	   532    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   533  #if (IRQ_GTM_TOM3_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   534    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   535  #endif
; ..\mcal_src\Gtm_Irq.c	   536    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   537    Gtm_IsrTomModule(3, 2);
; ..\mcal_src\Gtm_Irq.c	   538  }
; ..\mcal_src\Gtm_Irq.c	   539  #endif
; ..\mcal_src\Gtm_Irq.c	   540  
; ..\mcal_src\Gtm_Irq.c	   541  #if((IRQ_GTM_TOM3_SR2_PRIO > 0) || (IRQ_GTM_TOM3_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   542  #if((IRQ_GTM_TOM3_SR2_PRIO > 0) && (IRQ_GTM_TOM3_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   543  IFX_INTERRUPT(GTMTOM3SR2_ISR, 0, IRQ_GTM_TOM3_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	   544  #elif IRQ_GTM_TOM3_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   545  ISR(GTMTOM3SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	   546  #endif
; ..\mcal_src\Gtm_Irq.c	   547  {
; ..\mcal_src\Gtm_Irq.c	   548    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   549  #if (IRQ_GTM_TOM3_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   550    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   551  #endif
; ..\mcal_src\Gtm_Irq.c	   552    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   553    Gtm_IsrTomModule(3, 4);
; ..\mcal_src\Gtm_Irq.c	   554  }
; ..\mcal_src\Gtm_Irq.c	   555  #endif
; ..\mcal_src\Gtm_Irq.c	   556  
; ..\mcal_src\Gtm_Irq.c	   557  #if((IRQ_GTM_TOM3_SR3_PRIO > 0) || (IRQ_GTM_TOM3_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   558  #if((IRQ_GTM_TOM3_SR3_PRIO > 0) && (IRQ_GTM_TOM3_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   559  IFX_INTERRUPT(GTMTOM3SR3_ISR, 0, IRQ_GTM_TOM3_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	   560  #elif IRQ_GTM_TOM3_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   561  ISR(GTMTOM3SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	   562  #endif
; ..\mcal_src\Gtm_Irq.c	   563  {
; ..\mcal_src\Gtm_Irq.c	   564    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   565  #if (IRQ_GTM_TOM3_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   566    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   567  #endif
; ..\mcal_src\Gtm_Irq.c	   568    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   569    Gtm_IsrTomModule(3, 6);
; ..\mcal_src\Gtm_Irq.c	   570  }
; ..\mcal_src\Gtm_Irq.c	   571  #endif
; ..\mcal_src\Gtm_Irq.c	   572  
; ..\mcal_src\Gtm_Irq.c	   573  #if((IRQ_GTM_TOM3_SR4_PRIO > 0) || (IRQ_GTM_TOM3_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   574  #if((IRQ_GTM_TOM3_SR4_PRIO > 0) && (IRQ_GTM_TOM3_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   575  IFX_INTERRUPT(GTMTOM3SR4_ISR, 0, IRQ_GTM_TOM3_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	   576  #elif IRQ_GTM_TOM3_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   577  ISR(GTMTOM3SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	   578  #endif
; ..\mcal_src\Gtm_Irq.c	   579  {
; ..\mcal_src\Gtm_Irq.c	   580    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   581  #if (IRQ_GTM_TOM3_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   582    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   583  #endif
; ..\mcal_src\Gtm_Irq.c	   584    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   585    Gtm_IsrTomModule(3, 8);
; ..\mcal_src\Gtm_Irq.c	   586  }
; ..\mcal_src\Gtm_Irq.c	   587  #endif
; ..\mcal_src\Gtm_Irq.c	   588  
; ..\mcal_src\Gtm_Irq.c	   589  #if((IRQ_GTM_TOM3_SR5_PRIO > 0) || (IRQ_GTM_TOM3_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   590  #if((IRQ_GTM_TOM3_SR5_PRIO > 0) && (IRQ_GTM_TOM3_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   591  IFX_INTERRUPT(GTMTOM3SR5_ISR, 0, IRQ_GTM_TOM3_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	   592  #elif IRQ_GTM_TOM3_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   593  ISR(GTMTOM3SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	   594  #endif
; ..\mcal_src\Gtm_Irq.c	   595  {
; ..\mcal_src\Gtm_Irq.c	   596    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   597  #if (IRQ_GTM_TOM3_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   598    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   599  #endif
; ..\mcal_src\Gtm_Irq.c	   600    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   601    Gtm_IsrTomModule(3, 10);
; ..\mcal_src\Gtm_Irq.c	   602  }
; ..\mcal_src\Gtm_Irq.c	   603  #endif
; ..\mcal_src\Gtm_Irq.c	   604  
; ..\mcal_src\Gtm_Irq.c	   605  #if((IRQ_GTM_TOM3_SR6_PRIO > 0) || (IRQ_GTM_TOM3_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   606  #if((IRQ_GTM_TOM3_SR6_PRIO > 0) && (IRQ_GTM_TOM3_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   607  IFX_INTERRUPT(GTMTOM3SR6_ISR, 0, IRQ_GTM_TOM3_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	   608  #elif IRQ_GTM_TOM3_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   609  ISR(GTMTOM3SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	   610  #endif
; ..\mcal_src\Gtm_Irq.c	   611  {
; ..\mcal_src\Gtm_Irq.c	   612    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   613  #if (IRQ_GTM_TOM3_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   614    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   615  #endif
; ..\mcal_src\Gtm_Irq.c	   616    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   617    Gtm_IsrTomModule(3, 12);
; ..\mcal_src\Gtm_Irq.c	   618  }
; ..\mcal_src\Gtm_Irq.c	   619  #endif
; ..\mcal_src\Gtm_Irq.c	   620  
; ..\mcal_src\Gtm_Irq.c	   621  #if((IRQ_GTM_TOM3_SR7_PRIO > 0) || (IRQ_GTM_TOM3_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   622  #if((IRQ_GTM_TOM3_SR7_PRIO > 0) && (IRQ_GTM_TOM3_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   623  IFX_INTERRUPT(GTMTOM3SR7_ISR, 0, IRQ_GTM_TOM3_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	   624  #elif IRQ_GTM_TOM3_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   625  ISR(GTMTOM3SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	   626  #endif
; ..\mcal_src\Gtm_Irq.c	   627  {
; ..\mcal_src\Gtm_Irq.c	   628    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   629  #if (IRQ_GTM_TOM3_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   630    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   631  #endif
; ..\mcal_src\Gtm_Irq.c	   632    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   633    Gtm_IsrTomModule(3, 14);
; ..\mcal_src\Gtm_Irq.c	   634  }
; ..\mcal_src\Gtm_Irq.c	   635  #endif
; ..\mcal_src\Gtm_Irq.c	   636  
; ..\mcal_src\Gtm_Irq.c	   637  #endif /* #if(IRQ_GTM_TOM3_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	   638  
; ..\mcal_src\Gtm_Irq.c	   639  
; ..\mcal_src\Gtm_Irq.c	   640  
; ..\mcal_src\Gtm_Irq.c	   641  #if(IRQ_GTM_TOM4_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   642  
; ..\mcal_src\Gtm_Irq.c	   643  #if((IRQ_GTM_TOM4_SR0_PRIO > 0) || (IRQ_GTM_TOM4_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   644  #if((IRQ_GTM_TOM4_SR0_PRIO > 0) && (IRQ_GTM_TOM4_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   645  IFX_INTERRUPT(GTMTOM4SR0_ISR, 0, IRQ_GTM_TOM4_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   646  #elif IRQ_GTM_TOM4_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   647  ISR(GTMTOM4SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   648  #endif
; ..\mcal_src\Gtm_Irq.c	   649  {
; ..\mcal_src\Gtm_Irq.c	   650    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   651  #if (IRQ_GTM_TOM4_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   652    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   653  #endif
; ..\mcal_src\Gtm_Irq.c	   654    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   655    Gtm_IsrTomModule(4, 0);
; ..\mcal_src\Gtm_Irq.c	   656  }
; ..\mcal_src\Gtm_Irq.c	   657  #endif
; ..\mcal_src\Gtm_Irq.c	   658  
; ..\mcal_src\Gtm_Irq.c	   659  #if((IRQ_GTM_TOM4_SR1_PRIO > 0) || (IRQ_GTM_TOM4_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   660  #if((IRQ_GTM_TOM4_SR1_PRIO > 0) && (IRQ_GTM_TOM4_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   661  IFX_INTERRUPT(GTMTOM4SR1_ISR, 0, IRQ_GTM_TOM4_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	   662  #elif IRQ_GTM_TOM4_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   663  ISR(GTMTOM4SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	   664  #endif
; ..\mcal_src\Gtm_Irq.c	   665  {
; ..\mcal_src\Gtm_Irq.c	   666    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   667  #if (IRQ_GTM_TOM4_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   668    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   669  #endif
; ..\mcal_src\Gtm_Irq.c	   670    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   671    Gtm_IsrTomModule(4, 2);
; ..\mcal_src\Gtm_Irq.c	   672  }
; ..\mcal_src\Gtm_Irq.c	   673  #endif
; ..\mcal_src\Gtm_Irq.c	   674  
; ..\mcal_src\Gtm_Irq.c	   675  #if((IRQ_GTM_TOM4_SR2_PRIO > 0) || (IRQ_GTM_TOM4_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   676  #if((IRQ_GTM_TOM4_SR2_PRIO > 0) && (IRQ_GTM_TOM4_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   677  IFX_INTERRUPT(GTMTOM4SR2_ISR, 0, IRQ_GTM_TOM4_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	   678  #elif IRQ_GTM_TOM4_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   679  ISR(GTMTOM4SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	   680  #endif
; ..\mcal_src\Gtm_Irq.c	   681  {
; ..\mcal_src\Gtm_Irq.c	   682    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   683  #if (IRQ_GTM_TOM4_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   684    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   685  #endif
; ..\mcal_src\Gtm_Irq.c	   686    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   687    Gtm_IsrTomModule(4, 4);
; ..\mcal_src\Gtm_Irq.c	   688  }
; ..\mcal_src\Gtm_Irq.c	   689  #endif
; ..\mcal_src\Gtm_Irq.c	   690  
; ..\mcal_src\Gtm_Irq.c	   691  #if((IRQ_GTM_TOM4_SR3_PRIO > 0) || (IRQ_GTM_TOM4_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   692  #if((IRQ_GTM_TOM4_SR3_PRIO > 0) && (IRQ_GTM_TOM4_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   693  IFX_INTERRUPT(GTMTOM4SR3_ISR, 0, IRQ_GTM_TOM4_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	   694  #elif IRQ_GTM_TOM4_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   695  ISR(GTMTOM4SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	   696  #endif
; ..\mcal_src\Gtm_Irq.c	   697  {
; ..\mcal_src\Gtm_Irq.c	   698    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   699  #if (IRQ_GTM_TOM4_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   700    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   701  #endif
; ..\mcal_src\Gtm_Irq.c	   702    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   703    Gtm_IsrTomModule(4, 6);
; ..\mcal_src\Gtm_Irq.c	   704  }
; ..\mcal_src\Gtm_Irq.c	   705  #endif
; ..\mcal_src\Gtm_Irq.c	   706  
; ..\mcal_src\Gtm_Irq.c	   707  #if((IRQ_GTM_TOM4_SR4_PRIO > 0) || (IRQ_GTM_TOM4_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   708  #if((IRQ_GTM_TOM4_SR4_PRIO > 0) && (IRQ_GTM_TOM4_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   709  IFX_INTERRUPT(GTMTOM4SR4_ISR, 0, IRQ_GTM_TOM4_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	   710  #elif IRQ_GTM_TOM4_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   711  ISR(GTMTOM4SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	   712  #endif
; ..\mcal_src\Gtm_Irq.c	   713  {
; ..\mcal_src\Gtm_Irq.c	   714    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   715  #if (IRQ_GTM_TOM4_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   716    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   717  #endif
; ..\mcal_src\Gtm_Irq.c	   718    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   719    Gtm_IsrTomModule(4, 8);
; ..\mcal_src\Gtm_Irq.c	   720  }
; ..\mcal_src\Gtm_Irq.c	   721  #endif
; ..\mcal_src\Gtm_Irq.c	   722  
; ..\mcal_src\Gtm_Irq.c	   723  #if((IRQ_GTM_TOM4_SR5_PRIO > 0) || (IRQ_GTM_TOM4_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   724  #if((IRQ_GTM_TOM4_SR5_PRIO > 0) && (IRQ_GTM_TOM4_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   725  IFX_INTERRUPT(GTMTOM4SR5_ISR, 0, IRQ_GTM_TOM4_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	   726  #elif IRQ_GTM_TOM4_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   727  ISR(GTMTOM4SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	   728  #endif
; ..\mcal_src\Gtm_Irq.c	   729  {
; ..\mcal_src\Gtm_Irq.c	   730    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   731  #if (IRQ_GTM_TOM4_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   732    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   733  #endif
; ..\mcal_src\Gtm_Irq.c	   734    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   735    Gtm_IsrTomModule(4, 10);
; ..\mcal_src\Gtm_Irq.c	   736  }
; ..\mcal_src\Gtm_Irq.c	   737  #endif
; ..\mcal_src\Gtm_Irq.c	   738  
; ..\mcal_src\Gtm_Irq.c	   739  #if((IRQ_GTM_TOM4_SR6_PRIO > 0) || (IRQ_GTM_TOM4_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   740  #if((IRQ_GTM_TOM4_SR6_PRIO > 0) && (IRQ_GTM_TOM4_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   741  IFX_INTERRUPT(GTMTOM4SR6_ISR, 0, IRQ_GTM_TOM4_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	   742  #elif IRQ_GTM_TOM4_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   743  ISR(GTMTOM4SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	   744  #endif
; ..\mcal_src\Gtm_Irq.c	   745  {
; ..\mcal_src\Gtm_Irq.c	   746    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   747  #if (IRQ_GTM_TOM4_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   748    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   749  #endif
; ..\mcal_src\Gtm_Irq.c	   750    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   751    Gtm_IsrTomModule(4, 12);
; ..\mcal_src\Gtm_Irq.c	   752  }
; ..\mcal_src\Gtm_Irq.c	   753  #endif
; ..\mcal_src\Gtm_Irq.c	   754  
; ..\mcal_src\Gtm_Irq.c	   755  #if((IRQ_GTM_TOM4_SR7_PRIO > 0) || (IRQ_GTM_TOM4_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   756  #if((IRQ_GTM_TOM4_SR7_PRIO > 0) && (IRQ_GTM_TOM4_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   757  IFX_INTERRUPT(GTMTOM4SR7_ISR, 0, IRQ_GTM_TOM4_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	   758  #elif IRQ_GTM_TOM4_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   759  ISR(GTMTOM4SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	   760  #endif
; ..\mcal_src\Gtm_Irq.c	   761  {
; ..\mcal_src\Gtm_Irq.c	   762    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   763  #if (IRQ_GTM_TOM4_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   764    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   765  #endif
; ..\mcal_src\Gtm_Irq.c	   766    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   767    Gtm_IsrTomModule(4, 14);
; ..\mcal_src\Gtm_Irq.c	   768  }
; ..\mcal_src\Gtm_Irq.c	   769  #endif
; ..\mcal_src\Gtm_Irq.c	   770  
; ..\mcal_src\Gtm_Irq.c	   771  #endif /* #if(IRQ_GTM_TOM4_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	   772  
; ..\mcal_src\Gtm_Irq.c	   773  
; ..\mcal_src\Gtm_Irq.c	   774  
; ..\mcal_src\Gtm_Irq.c	   775  
; ..\mcal_src\Gtm_Irq.c	   776  
; ..\mcal_src\Gtm_Irq.c	   777  #if(IRQ_GTM_ATOM0_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   778  
; ..\mcal_src\Gtm_Irq.c	   779  #if((IRQ_GTM_ATOM0_SR0_PRIO > 0) || (IRQ_GTM_ATOM0_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   780  #if((IRQ_GTM_ATOM0_SR0_PRIO > 0) && (IRQ_GTM_ATOM0_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   781  IFX_INTERRUPT(GTMATOM0SR0_ISR, 0, IRQ_GTM_ATOM0_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   782  #elif IRQ_GTM_ATOM0_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   783  ISR(GTMATOM0SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   784  #endif
; ..\mcal_src\Gtm_Irq.c	   785  {
; ..\mcal_src\Gtm_Irq.c	   786    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   787  #if (IRQ_GTM_ATOM0_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   788    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   789  #endif
; ..\mcal_src\Gtm_Irq.c	   790    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   791    Gtm_IsrAtomModule(0, 0);
; ..\mcal_src\Gtm_Irq.c	   792  }
; ..\mcal_src\Gtm_Irq.c	   793  #endif
; ..\mcal_src\Gtm_Irq.c	   794  
; ..\mcal_src\Gtm_Irq.c	   795  #if((IRQ_GTM_ATOM0_SR1_PRIO > 0) || (IRQ_GTM_ATOM0_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   796  #if((IRQ_GTM_ATOM0_SR1_PRIO > 0) && (IRQ_GTM_ATOM0_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   797  IFX_INTERRUPT(GTMATOM0SR1_ISR, 0, IRQ_GTM_ATOM0_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	   798  #elif IRQ_GTM_ATOM0_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   799  ISR(GTMATOM0SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	   800  #endif
; ..\mcal_src\Gtm_Irq.c	   801  {
; ..\mcal_src\Gtm_Irq.c	   802    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   803  #if (IRQ_GTM_ATOM0_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   804    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   805  #endif
; ..\mcal_src\Gtm_Irq.c	   806    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   807    Gtm_IsrAtomModule(0, 2);
; ..\mcal_src\Gtm_Irq.c	   808  }
; ..\mcal_src\Gtm_Irq.c	   809  #endif
; ..\mcal_src\Gtm_Irq.c	   810  
; ..\mcal_src\Gtm_Irq.c	   811  #if((IRQ_GTM_ATOM0_SR2_PRIO > 0) || (IRQ_GTM_ATOM0_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   812  #if((IRQ_GTM_ATOM0_SR2_PRIO > 0) && (IRQ_GTM_ATOM0_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   813  IFX_INTERRUPT(GTMATOM0SR2_ISR, 0, IRQ_GTM_ATOM0_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	   814  #elif IRQ_GTM_ATOM0_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   815  ISR(GTMATOM0SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	   816  #endif
; ..\mcal_src\Gtm_Irq.c	   817  {
; ..\mcal_src\Gtm_Irq.c	   818    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   819  #if (IRQ_GTM_ATOM0_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   820    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   821  #endif
; ..\mcal_src\Gtm_Irq.c	   822    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   823    Gtm_IsrAtomModule(0, 4);
; ..\mcal_src\Gtm_Irq.c	   824  }
; ..\mcal_src\Gtm_Irq.c	   825  #endif
; ..\mcal_src\Gtm_Irq.c	   826  
; ..\mcal_src\Gtm_Irq.c	   827  #if((IRQ_GTM_ATOM0_SR3_PRIO > 0) || (IRQ_GTM_ATOM0_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   828  #if((IRQ_GTM_ATOM0_SR3_PRIO > 0) && (IRQ_GTM_ATOM0_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   829  IFX_INTERRUPT(GTMATOM0SR3_ISR, 0, IRQ_GTM_ATOM0_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	   830  #elif IRQ_GTM_ATOM0_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   831  ISR(GTMATOM0SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	   832  #endif
; ..\mcal_src\Gtm_Irq.c	   833  {
; ..\mcal_src\Gtm_Irq.c	   834    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   835  #if (IRQ_GTM_ATOM0_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   836    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   837  #endif
; ..\mcal_src\Gtm_Irq.c	   838    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   839    Gtm_IsrAtomModule(0, 6);
; ..\mcal_src\Gtm_Irq.c	   840  }
; ..\mcal_src\Gtm_Irq.c	   841  #endif
; ..\mcal_src\Gtm_Irq.c	   842  
; ..\mcal_src\Gtm_Irq.c	   843  
; ..\mcal_src\Gtm_Irq.c	   844  #endif /* #if(IRQ_GTM_ATOM0_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	   845  
; ..\mcal_src\Gtm_Irq.c	   846  #if(IRQ_GTM_ATOM1_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   847  
; ..\mcal_src\Gtm_Irq.c	   848  #if((IRQ_GTM_ATOM1_SR0_PRIO > 0) || (IRQ_GTM_ATOM1_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   849  #if((IRQ_GTM_ATOM1_SR0_PRIO > 0) && (IRQ_GTM_ATOM1_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   850  IFX_INTERRUPT(GTMATOM1SR0_ISR, 0, IRQ_GTM_ATOM1_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   851  #elif IRQ_GTM_ATOM1_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   852  ISR(GTMATOM1SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   853  #endif
; ..\mcal_src\Gtm_Irq.c	   854  {
; ..\mcal_src\Gtm_Irq.c	   855    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   856  #if (IRQ_GTM_ATOM1_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   857    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   858  #endif
; ..\mcal_src\Gtm_Irq.c	   859    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   860    Gtm_IsrAtomModule(1, 0);
; ..\mcal_src\Gtm_Irq.c	   861  }
; ..\mcal_src\Gtm_Irq.c	   862  #endif
; ..\mcal_src\Gtm_Irq.c	   863  
; ..\mcal_src\Gtm_Irq.c	   864  #if((IRQ_GTM_ATOM1_SR1_PRIO > 0) || (IRQ_GTM_ATOM1_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   865  #if((IRQ_GTM_ATOM1_SR1_PRIO > 0) && (IRQ_GTM_ATOM1_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   866  IFX_INTERRUPT(GTMATOM1SR1_ISR, 0, IRQ_GTM_ATOM1_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	   867  #elif IRQ_GTM_ATOM1_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   868  ISR(GTMATOM1SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	   869  #endif
; ..\mcal_src\Gtm_Irq.c	   870  {
; ..\mcal_src\Gtm_Irq.c	   871    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   872  #if (IRQ_GTM_ATOM1_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   873    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   874  #endif
; ..\mcal_src\Gtm_Irq.c	   875    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   876    Gtm_IsrAtomModule(1, 2);
; ..\mcal_src\Gtm_Irq.c	   877  }
; ..\mcal_src\Gtm_Irq.c	   878  #endif
; ..\mcal_src\Gtm_Irq.c	   879  
; ..\mcal_src\Gtm_Irq.c	   880  #if((IRQ_GTM_ATOM1_SR2_PRIO > 0) || (IRQ_GTM_ATOM1_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   881  #if((IRQ_GTM_ATOM1_SR2_PRIO > 0) && (IRQ_GTM_ATOM1_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   882  IFX_INTERRUPT(GTMATOM1SR2_ISR, 0, IRQ_GTM_ATOM1_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	   883  #elif IRQ_GTM_ATOM1_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   884  ISR(GTMATOM1SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	   885  #endif
; ..\mcal_src\Gtm_Irq.c	   886  {
; ..\mcal_src\Gtm_Irq.c	   887    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   888  #if (IRQ_GTM_ATOM1_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   889    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   890  #endif
; ..\mcal_src\Gtm_Irq.c	   891    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   892    Gtm_IsrAtomModule(1, 4);
; ..\mcal_src\Gtm_Irq.c	   893  }
; ..\mcal_src\Gtm_Irq.c	   894  #endif
; ..\mcal_src\Gtm_Irq.c	   895  
; ..\mcal_src\Gtm_Irq.c	   896  #if((IRQ_GTM_ATOM1_SR3_PRIO > 0) || (IRQ_GTM_ATOM1_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   897  #if((IRQ_GTM_ATOM1_SR3_PRIO > 0) && (IRQ_GTM_ATOM1_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   898  IFX_INTERRUPT(GTMATOM1SR3_ISR, 0, IRQ_GTM_ATOM1_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	   899  #elif IRQ_GTM_ATOM1_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   900  ISR(GTMATOM1SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	   901  #endif
; ..\mcal_src\Gtm_Irq.c	   902  {
; ..\mcal_src\Gtm_Irq.c	   903    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   904  #if (IRQ_GTM_ATOM1_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   905    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   906  #endif
; ..\mcal_src\Gtm_Irq.c	   907    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   908    Gtm_IsrAtomModule(1, 6);
; ..\mcal_src\Gtm_Irq.c	   909  }
; ..\mcal_src\Gtm_Irq.c	   910  #endif
; ..\mcal_src\Gtm_Irq.c	   911  
; ..\mcal_src\Gtm_Irq.c	   912  #endif /* #if(IRQ_GTM_ATOM1_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	   913  #if(IRQ_GTM_ATOM2_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   914  
; ..\mcal_src\Gtm_Irq.c	   915  
; ..\mcal_src\Gtm_Irq.c	   916  #if((IRQ_GTM_ATOM2_SR0_PRIO > 0) || (IRQ_GTM_ATOM2_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   917  #if((IRQ_GTM_ATOM2_SR0_PRIO > 0) && (IRQ_GTM_ATOM2_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   918  IFX_INTERRUPT(GTMATOM2SR0_ISR, 0, IRQ_GTM_ATOM2_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   919  #elif IRQ_GTM_ATOM2_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   920  ISR(GTMATOM2SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   921  #endif
; ..\mcal_src\Gtm_Irq.c	   922  {
; ..\mcal_src\Gtm_Irq.c	   923    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   924  #if (IRQ_GTM_ATOM2_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   925    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   926  #endif
; ..\mcal_src\Gtm_Irq.c	   927    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   928    Gtm_IsrAtomModule(2, 0);
; ..\mcal_src\Gtm_Irq.c	   929  }
; ..\mcal_src\Gtm_Irq.c	   930  #endif
; ..\mcal_src\Gtm_Irq.c	   931  
; ..\mcal_src\Gtm_Irq.c	   932  #if((IRQ_GTM_ATOM2_SR1_PRIO > 0) || (IRQ_GTM_ATOM2_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   933  #if((IRQ_GTM_ATOM2_SR1_PRIO > 0) && (IRQ_GTM_ATOM2_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   934  IFX_INTERRUPT(GTMATOM2SR1_ISR, 0, IRQ_GTM_ATOM2_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	   935  #elif IRQ_GTM_ATOM2_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   936  ISR(GTMATOM2SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	   937  #endif
; ..\mcal_src\Gtm_Irq.c	   938  {
; ..\mcal_src\Gtm_Irq.c	   939    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   940  #if (IRQ_GTM_ATOM2_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   941    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   942  #endif
; ..\mcal_src\Gtm_Irq.c	   943    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   944    Gtm_IsrAtomModule(2, 2);
; ..\mcal_src\Gtm_Irq.c	   945  }
; ..\mcal_src\Gtm_Irq.c	   946  #endif
; ..\mcal_src\Gtm_Irq.c	   947  
; ..\mcal_src\Gtm_Irq.c	   948  #if((IRQ_GTM_ATOM2_SR2_PRIO > 0) || (IRQ_GTM_ATOM2_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   949  #if((IRQ_GTM_ATOM2_SR2_PRIO > 0) && (IRQ_GTM_ATOM2_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   950  IFX_INTERRUPT(GTMATOM2SR2_ISR, 0, IRQ_GTM_ATOM2_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	   951  #elif IRQ_GTM_ATOM2_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   952  ISR(GTMATOM2SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	   953  #endif
; ..\mcal_src\Gtm_Irq.c	   954  {
; ..\mcal_src\Gtm_Irq.c	   955    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   956  #if (IRQ_GTM_ATOM2_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   957    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   958  #endif
; ..\mcal_src\Gtm_Irq.c	   959    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   960    Gtm_IsrAtomModule(2, 4);
; ..\mcal_src\Gtm_Irq.c	   961  }
; ..\mcal_src\Gtm_Irq.c	   962  #endif
; ..\mcal_src\Gtm_Irq.c	   963  
; ..\mcal_src\Gtm_Irq.c	   964  #if((IRQ_GTM_ATOM2_SR3_PRIO > 0) || (IRQ_GTM_ATOM2_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   965  #if((IRQ_GTM_ATOM2_SR3_PRIO > 0) && (IRQ_GTM_ATOM2_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   966  IFX_INTERRUPT(GTMATOM2SR3_ISR, 0, IRQ_GTM_ATOM2_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	   967  #elif IRQ_GTM_ATOM2_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   968  ISR(GTMATOM2SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	   969  #endif
; ..\mcal_src\Gtm_Irq.c	   970  {
; ..\mcal_src\Gtm_Irq.c	   971    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   972  #if (IRQ_GTM_ATOM2_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   973    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   974  #endif
; ..\mcal_src\Gtm_Irq.c	   975    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   976    Gtm_IsrAtomModule(2, 6);
; ..\mcal_src\Gtm_Irq.c	   977  }
; ..\mcal_src\Gtm_Irq.c	   978  #endif
; ..\mcal_src\Gtm_Irq.c	   979  
; ..\mcal_src\Gtm_Irq.c	   980  #endif /* #if(IRQ_GTM_ATOM2_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	   981  
; ..\mcal_src\Gtm_Irq.c	   982  #if(IRQ_GTM_ATOM3_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	   983  
; ..\mcal_src\Gtm_Irq.c	   984  #if((IRQ_GTM_ATOM3_SR0_PRIO > 0) || (IRQ_GTM_ATOM3_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	   985  #if((IRQ_GTM_ATOM3_SR0_PRIO > 0) && (IRQ_GTM_ATOM3_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	   986  IFX_INTERRUPT(GTMATOM3SR0_ISR, 0, IRQ_GTM_ATOM3_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	   987  #elif IRQ_GTM_ATOM3_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	   988  ISR(GTMATOM3SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	   989  #endif
; ..\mcal_src\Gtm_Irq.c	   990  {
; ..\mcal_src\Gtm_Irq.c	   991    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	   992  #if (IRQ_GTM_ATOM3_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	   993    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	   994  #endif
; ..\mcal_src\Gtm_Irq.c	   995    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	   996    Gtm_IsrAtomModule(3, 0);
; ..\mcal_src\Gtm_Irq.c	   997  }
; ..\mcal_src\Gtm_Irq.c	   998  #endif
; ..\mcal_src\Gtm_Irq.c	   999  
; ..\mcal_src\Gtm_Irq.c	  1000  #if((IRQ_GTM_ATOM3_SR1_PRIO > 0) || (IRQ_GTM_ATOM3_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1001  #if((IRQ_GTM_ATOM3_SR1_PRIO > 0) && (IRQ_GTM_ATOM3_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1002  IFX_INTERRUPT(GTMATOM3SR1_ISR, 0, IRQ_GTM_ATOM3_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1003  #elif IRQ_GTM_ATOM3_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1004  ISR(GTMATOM3SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1005  #endif
; ..\mcal_src\Gtm_Irq.c	  1006  {
; ..\mcal_src\Gtm_Irq.c	  1007    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1008  #if (IRQ_GTM_ATOM3_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1009    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1010  #endif
; ..\mcal_src\Gtm_Irq.c	  1011    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1012    Gtm_IsrAtomModule(3, 2);
; ..\mcal_src\Gtm_Irq.c	  1013  }
; ..\mcal_src\Gtm_Irq.c	  1014  #endif
; ..\mcal_src\Gtm_Irq.c	  1015  
; ..\mcal_src\Gtm_Irq.c	  1016  #if((IRQ_GTM_ATOM3_SR2_PRIO > 0) || (IRQ_GTM_ATOM3_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1017  #if((IRQ_GTM_ATOM3_SR2_PRIO > 0) && (IRQ_GTM_ATOM3_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1018  IFX_INTERRUPT(GTMATOM3SR2_ISR, 0, IRQ_GTM_ATOM3_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1019  #elif IRQ_GTM_ATOM3_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1020  ISR(GTMATOM3SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1021  #endif
; ..\mcal_src\Gtm_Irq.c	  1022  {
; ..\mcal_src\Gtm_Irq.c	  1023    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1024  #if (IRQ_GTM_ATOM3_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1025    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1026  #endif
; ..\mcal_src\Gtm_Irq.c	  1027    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1028    Gtm_IsrAtomModule(3, 4);
; ..\mcal_src\Gtm_Irq.c	  1029  }
; ..\mcal_src\Gtm_Irq.c	  1030  #endif
; ..\mcal_src\Gtm_Irq.c	  1031  
; ..\mcal_src\Gtm_Irq.c	  1032  #if((IRQ_GTM_ATOM3_SR3_PRIO > 0) || (IRQ_GTM_ATOM3_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1033  #if((IRQ_GTM_ATOM3_SR3_PRIO > 0) && (IRQ_GTM_ATOM3_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1034  IFX_INTERRUPT(GTMATOM3SR3_ISR, 0, IRQ_GTM_ATOM3_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1035  #elif IRQ_GTM_ATOM3_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1036  ISR(GTMATOM3SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1037  #endif
; ..\mcal_src\Gtm_Irq.c	  1038  {
; ..\mcal_src\Gtm_Irq.c	  1039    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1040  #if (IRQ_GTM_ATOM3_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1041    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1042  #endif
; ..\mcal_src\Gtm_Irq.c	  1043    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1044    Gtm_IsrAtomModule(3, 6);
; ..\mcal_src\Gtm_Irq.c	  1045  }
; ..\mcal_src\Gtm_Irq.c	  1046  #endif
; ..\mcal_src\Gtm_Irq.c	  1047  
; ..\mcal_src\Gtm_Irq.c	  1048  #endif /* #if(IRQ_GTM_ATOM3_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1049  
; ..\mcal_src\Gtm_Irq.c	  1050  #if(IRQ_GTM_ATOM4_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1051  
; ..\mcal_src\Gtm_Irq.c	  1052  #if((IRQ_GTM_ATOM4_SR0_PRIO > 0) || (IRQ_GTM_ATOM4_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1053  #if((IRQ_GTM_ATOM4_SR0_PRIO > 0) && (IRQ_GTM_ATOM4_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1054  IFX_INTERRUPT(GTMATOM4SR0_ISR, 0, IRQ_GTM_ATOM4_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1055  #elif IRQ_GTM_ATOM4_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1056  ISR(GTMATOM4SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1057  #endif
; ..\mcal_src\Gtm_Irq.c	  1058  {
; ..\mcal_src\Gtm_Irq.c	  1059    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1060  #if (IRQ_GTM_ATOM4_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1061    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1062  #endif
; ..\mcal_src\Gtm_Irq.c	  1063    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1064    Gtm_IsrAtomModule(4, 0);
; ..\mcal_src\Gtm_Irq.c	  1065  }
; ..\mcal_src\Gtm_Irq.c	  1066  #endif
; ..\mcal_src\Gtm_Irq.c	  1067  
; ..\mcal_src\Gtm_Irq.c	  1068  #if((IRQ_GTM_ATOM4_SR1_PRIO > 0) || (IRQ_GTM_ATOM4_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1069  #if((IRQ_GTM_ATOM4_SR1_PRIO > 0) && (IRQ_GTM_ATOM4_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1070  IFX_INTERRUPT(GTMATOM4SR1_ISR, 0, IRQ_GTM_ATOM4_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1071  #elif IRQ_GTM_ATOM4_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1072  ISR(GTMATOM4SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1073  #endif
; ..\mcal_src\Gtm_Irq.c	  1074  {
; ..\mcal_src\Gtm_Irq.c	  1075    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1076  #if (IRQ_GTM_ATOM4_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1077    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1078  #endif
; ..\mcal_src\Gtm_Irq.c	  1079    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1080    Gtm_IsrAtomModule(4, 2);
; ..\mcal_src\Gtm_Irq.c	  1081  }
; ..\mcal_src\Gtm_Irq.c	  1082  #endif
; ..\mcal_src\Gtm_Irq.c	  1083  
; ..\mcal_src\Gtm_Irq.c	  1084  #if((IRQ_GTM_ATOM4_SR2_PRIO > 0) || (IRQ_GTM_ATOM4_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1085  #if((IRQ_GTM_ATOM4_SR2_PRIO > 0) && (IRQ_GTM_ATOM4_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1086  IFX_INTERRUPT(GTMATOM4SR2_ISR, 0, IRQ_GTM_ATOM4_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1087  #elif IRQ_GTM_ATOM4_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1088  ISR(GTMATOM4SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1089  #endif
; ..\mcal_src\Gtm_Irq.c	  1090  {
; ..\mcal_src\Gtm_Irq.c	  1091    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1092  #if (IRQ_GTM_ATOM4_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1093    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1094  #endif
; ..\mcal_src\Gtm_Irq.c	  1095    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1096    Gtm_IsrAtomModule(4, 4);
; ..\mcal_src\Gtm_Irq.c	  1097  }
; ..\mcal_src\Gtm_Irq.c	  1098  #endif
; ..\mcal_src\Gtm_Irq.c	  1099  
; ..\mcal_src\Gtm_Irq.c	  1100  #if((IRQ_GTM_ATOM4_SR3_PRIO > 0) || (IRQ_GTM_ATOM4_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1101  #if((IRQ_GTM_ATOM4_SR3_PRIO > 0) && (IRQ_GTM_ATOM4_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1102  IFX_INTERRUPT(GTMATOM4SR3_ISR, 0, IRQ_GTM_ATOM4_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1103  #elif IRQ_GTM_ATOM4_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1104  ISR(GTMATOM4SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1105  #endif
; ..\mcal_src\Gtm_Irq.c	  1106  {
; ..\mcal_src\Gtm_Irq.c	  1107    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1108  #if (IRQ_GTM_ATOM4_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1109    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1110  #endif
; ..\mcal_src\Gtm_Irq.c	  1111    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1112    Gtm_IsrAtomModule(4, 6);
; ..\mcal_src\Gtm_Irq.c	  1113  }
; ..\mcal_src\Gtm_Irq.c	  1114  #endif
; ..\mcal_src\Gtm_Irq.c	  1115  
; ..\mcal_src\Gtm_Irq.c	  1116  #endif /* #if(IRQ_GTM_ATOM4_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1117  
; ..\mcal_src\Gtm_Irq.c	  1118  
; ..\mcal_src\Gtm_Irq.c	  1119  
; ..\mcal_src\Gtm_Irq.c	  1120  #if(IRQ_GTM_ATOM5_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1121  
; ..\mcal_src\Gtm_Irq.c	  1122  #if((IRQ_GTM_ATOM5_SR0_PRIO > 0) || (IRQ_GTM_ATOM5_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1123  #if((IRQ_GTM_ATOM5_SR0_PRIO > 0) && (IRQ_GTM_ATOM5_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1124  IFX_INTERRUPT(GTMATOM5SR0_ISR, 0, IRQ_GTM_ATOM5_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1125  #elif IRQ_GTM_ATOM5_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1126  ISR(GTMATOM5SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1127  #endif
; ..\mcal_src\Gtm_Irq.c	  1128  {
; ..\mcal_src\Gtm_Irq.c	  1129    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1130  #if (IRQ_GTM_ATOM5_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1131    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1132  #endif
; ..\mcal_src\Gtm_Irq.c	  1133    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1134    Gtm_IsrAtomModule(5, 0);
; ..\mcal_src\Gtm_Irq.c	  1135  }
; ..\mcal_src\Gtm_Irq.c	  1136  #endif
; ..\mcal_src\Gtm_Irq.c	  1137  
; ..\mcal_src\Gtm_Irq.c	  1138  #if((IRQ_GTM_ATOM5_SR1_PRIO > 0) || (IRQ_GTM_ATOM5_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1139  #if((IRQ_GTM_ATOM5_SR1_PRIO > 0) && (IRQ_GTM_ATOM5_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1140  IFX_INTERRUPT(GTMATOM5SR1_ISR, 0, IRQ_GTM_ATOM5_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1141  #elif IRQ_GTM_ATOM5_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1142  ISR(GTMATOM5SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1143  #endif
; ..\mcal_src\Gtm_Irq.c	  1144  {
; ..\mcal_src\Gtm_Irq.c	  1145    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1146  #if (IRQ_GTM_ATOM5_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1147    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1148  #endif
; ..\mcal_src\Gtm_Irq.c	  1149    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1150    Gtm_IsrAtomModule(5, 2);
; ..\mcal_src\Gtm_Irq.c	  1151  }
; ..\mcal_src\Gtm_Irq.c	  1152  #endif
; ..\mcal_src\Gtm_Irq.c	  1153  
; ..\mcal_src\Gtm_Irq.c	  1154  #if((IRQ_GTM_ATOM5_SR2_PRIO > 0) || (IRQ_GTM_ATOM5_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1155  #if((IRQ_GTM_ATOM5_SR2_PRIO > 0) && (IRQ_GTM_ATOM5_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1156  IFX_INTERRUPT(GTMATOM5SR2_ISR, 0, IRQ_GTM_ATOM5_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1157  #elif IRQ_GTM_ATOM5_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1158  ISR(GTMATOM5SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1159  #endif
; ..\mcal_src\Gtm_Irq.c	  1160  {
; ..\mcal_src\Gtm_Irq.c	  1161    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1162  #if (IRQ_GTM_ATOM5_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1163    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1164  #endif
; ..\mcal_src\Gtm_Irq.c	  1165    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1166    Gtm_IsrAtomModule(5, 4);
; ..\mcal_src\Gtm_Irq.c	  1167  }
; ..\mcal_src\Gtm_Irq.c	  1168  #endif
; ..\mcal_src\Gtm_Irq.c	  1169  
; ..\mcal_src\Gtm_Irq.c	  1170  #if((IRQ_GTM_ATOM5_SR3_PRIO > 0) || (IRQ_GTM_ATOM5_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1171  #if((IRQ_GTM_ATOM5_SR3_PRIO > 0) && (IRQ_GTM_ATOM5_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1172  IFX_INTERRUPT(GTMATOM5SR3_ISR, 0, IRQ_GTM_ATOM5_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1173  #elif IRQ_GTM_ATOM5_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1174  ISR(GTMATOM5SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1175  #endif
; ..\mcal_src\Gtm_Irq.c	  1176  {
; ..\mcal_src\Gtm_Irq.c	  1177    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1178  #if (IRQ_GTM_ATOM5_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1179    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1180  #endif
; ..\mcal_src\Gtm_Irq.c	  1181    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1182    Gtm_IsrAtomModule(5, 6);
; ..\mcal_src\Gtm_Irq.c	  1183  }
; ..\mcal_src\Gtm_Irq.c	  1184  #endif
; ..\mcal_src\Gtm_Irq.c	  1185  
; ..\mcal_src\Gtm_Irq.c	  1186  #endif /* #if(IRQ_GTM_ATOM5_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1187  
; ..\mcal_src\Gtm_Irq.c	  1188  
; ..\mcal_src\Gtm_Irq.c	  1189  #if(IRQ_GTM_ATOM6_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1190  
; ..\mcal_src\Gtm_Irq.c	  1191  #if((IRQ_GTM_ATOM6_SR0_PRIO > 0) || (IRQ_GTM_ATOM6_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1192  #if((IRQ_GTM_ATOM6_SR0_PRIO > 0) && (IRQ_GTM_ATOM6_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1193  IFX_INTERRUPT(GTMATOM6SR0_ISR, 0, IRQ_GTM_ATOM6_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1194  #elif IRQ_GTM_ATOM6_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1195  ISR(GTMATOM6SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1196  #endif
; ..\mcal_src\Gtm_Irq.c	  1197  {
; ..\mcal_src\Gtm_Irq.c	  1198    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1199  #if (IRQ_GTM_ATOM6_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1200    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1201  #endif
; ..\mcal_src\Gtm_Irq.c	  1202    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1203    Gtm_IsrAtomModule(6, 0);
; ..\mcal_src\Gtm_Irq.c	  1204  }
; ..\mcal_src\Gtm_Irq.c	  1205  #endif
; ..\mcal_src\Gtm_Irq.c	  1206  
; ..\mcal_src\Gtm_Irq.c	  1207  #if((IRQ_GTM_ATOM6_SR1_PRIO > 0) || (IRQ_GTM_ATOM6_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1208  #if((IRQ_GTM_ATOM6_SR1_PRIO > 0) && (IRQ_GTM_ATOM6_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1209  IFX_INTERRUPT(GTMATOM6SR1_ISR, 0, IRQ_GTM_ATOM6_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1210  #elif IRQ_GTM_ATOM6_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1211  ISR(GTMATOM6SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1212  #endif
; ..\mcal_src\Gtm_Irq.c	  1213  {
; ..\mcal_src\Gtm_Irq.c	  1214    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1215  #if (IRQ_GTM_ATOM6_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1216    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1217  #endif
; ..\mcal_src\Gtm_Irq.c	  1218    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1219    Gtm_IsrAtomModule(6, 2);
; ..\mcal_src\Gtm_Irq.c	  1220  }
; ..\mcal_src\Gtm_Irq.c	  1221  #endif
; ..\mcal_src\Gtm_Irq.c	  1222  
; ..\mcal_src\Gtm_Irq.c	  1223  #if((IRQ_GTM_ATOM6_SR2_PRIO > 0) || (IRQ_GTM_ATOM6_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1224  #if((IRQ_GTM_ATOM6_SR2_PRIO > 0) && (IRQ_GTM_ATOM6_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1225  IFX_INTERRUPT(GTMATOM6SR2_ISR, 0, IRQ_GTM_ATOM6_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1226  #elif IRQ_GTM_ATOM6_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1227  ISR(GTMATOM6SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1228  #endif
; ..\mcal_src\Gtm_Irq.c	  1229  {
; ..\mcal_src\Gtm_Irq.c	  1230    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1231  #if (IRQ_GTM_ATOM6_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1232    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1233  #endif
; ..\mcal_src\Gtm_Irq.c	  1234    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1235    Gtm_IsrAtomModule(6, 4);
; ..\mcal_src\Gtm_Irq.c	  1236  }
; ..\mcal_src\Gtm_Irq.c	  1237  #endif
; ..\mcal_src\Gtm_Irq.c	  1238  
; ..\mcal_src\Gtm_Irq.c	  1239  #if((IRQ_GTM_ATOM6_SR3_PRIO > 0) || (IRQ_GTM_ATOM6_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1240  #if((IRQ_GTM_ATOM6_SR3_PRIO > 0) && (IRQ_GTM_ATOM6_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1241  IFX_INTERRUPT(GTMATOM6SR3_ISR, 0, IRQ_GTM_ATOM6_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1242  #elif IRQ_GTM_ATOM6_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1243  ISR(GTMATOM6SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1244  #endif
; ..\mcal_src\Gtm_Irq.c	  1245  {
; ..\mcal_src\Gtm_Irq.c	  1246    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1247  #if (IRQ_GTM_ATOM6_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1248    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1249  #endif
; ..\mcal_src\Gtm_Irq.c	  1250    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1251    Gtm_IsrAtomModule(6, 6);
; ..\mcal_src\Gtm_Irq.c	  1252  }
; ..\mcal_src\Gtm_Irq.c	  1253  #endif
; ..\mcal_src\Gtm_Irq.c	  1254  
; ..\mcal_src\Gtm_Irq.c	  1255  #endif /* #if(IRQ_GTM_ATOM6_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1256  
; ..\mcal_src\Gtm_Irq.c	  1257  
; ..\mcal_src\Gtm_Irq.c	  1258  #if(IRQ_GTM_ATOM7_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1259  
; ..\mcal_src\Gtm_Irq.c	  1260  #if((IRQ_GTM_ATOM7_SR0_PRIO > 0) || (IRQ_GTM_ATOM7_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1261  #if((IRQ_GTM_ATOM7_SR0_PRIO > 0) && (IRQ_GTM_ATOM7_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1262  IFX_INTERRUPT(GTMATOM7SR0_ISR, 0, IRQ_GTM_ATOM7_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1263  #elif IRQ_GTM_ATOM7_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1264  ISR(GTMATOM7SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1265  #endif
; ..\mcal_src\Gtm_Irq.c	  1266  {
; ..\mcal_src\Gtm_Irq.c	  1267    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1268  #if (IRQ_GTM_ATOM7_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1269    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1270  #endif
; ..\mcal_src\Gtm_Irq.c	  1271    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1272    Gtm_IsrAtomModule(7, 0);
; ..\mcal_src\Gtm_Irq.c	  1273  }
; ..\mcal_src\Gtm_Irq.c	  1274  #endif
; ..\mcal_src\Gtm_Irq.c	  1275  
; ..\mcal_src\Gtm_Irq.c	  1276  #if((IRQ_GTM_ATOM7_SR1_PRIO > 0) || (IRQ_GTM_ATOM7_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1277  #if((IRQ_GTM_ATOM7_SR1_PRIO > 0) && (IRQ_GTM_ATOM7_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1278  IFX_INTERRUPT(GTMATOM7SR1_ISR, 0, IRQ_GTM_ATOM7_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1279  #elif IRQ_GTM_ATOM7_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1280  ISR(GTMATOM7SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1281  #endif
; ..\mcal_src\Gtm_Irq.c	  1282  {
; ..\mcal_src\Gtm_Irq.c	  1283    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1284  #if (IRQ_GTM_ATOM7_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1285    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1286  #endif
; ..\mcal_src\Gtm_Irq.c	  1287    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1288    Gtm_IsrAtomModule(7, 2);
; ..\mcal_src\Gtm_Irq.c	  1289  }
; ..\mcal_src\Gtm_Irq.c	  1290  #endif
; ..\mcal_src\Gtm_Irq.c	  1291  
; ..\mcal_src\Gtm_Irq.c	  1292  #if((IRQ_GTM_ATOM7_SR2_PRIO > 0) || (IRQ_GTM_ATOM7_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1293  #if((IRQ_GTM_ATOM7_SR2_PRIO > 0) && (IRQ_GTM_ATOM7_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1294  IFX_INTERRUPT(GTMATOM7SR2_ISR, 0, IRQ_GTM_ATOM7_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1295  #elif IRQ_GTM_ATOM7_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1296  ISR(GTMATOM7SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1297  #endif
; ..\mcal_src\Gtm_Irq.c	  1298  {
; ..\mcal_src\Gtm_Irq.c	  1299    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1300  #if (IRQ_GTM_ATOM7_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1301    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1302  #endif
; ..\mcal_src\Gtm_Irq.c	  1303    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1304    Gtm_IsrAtomModule(7, 4);
; ..\mcal_src\Gtm_Irq.c	  1305  }
; ..\mcal_src\Gtm_Irq.c	  1306  #endif
; ..\mcal_src\Gtm_Irq.c	  1307  
; ..\mcal_src\Gtm_Irq.c	  1308  #if((IRQ_GTM_ATOM7_SR3_PRIO > 0) || (IRQ_GTM_ATOM7_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1309  #if((IRQ_GTM_ATOM7_SR3_PRIO > 0) && (IRQ_GTM_ATOM7_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1310  IFX_INTERRUPT(GTMATOM7SR3_ISR, 0, IRQ_GTM_ATOM7_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1311  #elif IRQ_GTM_ATOM7_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1312  ISR(GTMATOM7SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1313  #endif
; ..\mcal_src\Gtm_Irq.c	  1314  {
; ..\mcal_src\Gtm_Irq.c	  1315    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1316  #if (IRQ_GTM_ATOM7_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1317    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1318  #endif
; ..\mcal_src\Gtm_Irq.c	  1319    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1320    Gtm_IsrAtomModule(7, 6);
; ..\mcal_src\Gtm_Irq.c	  1321  }
; ..\mcal_src\Gtm_Irq.c	  1322  #endif
; ..\mcal_src\Gtm_Irq.c	  1323  
; ..\mcal_src\Gtm_Irq.c	  1324  #endif /* #if(IRQ_GTM_ATOM7_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1325  
; ..\mcal_src\Gtm_Irq.c	  1326  
; ..\mcal_src\Gtm_Irq.c	  1327  
; ..\mcal_src\Gtm_Irq.c	  1328  #if(IRQ_GTM_ATOM8_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1329  
; ..\mcal_src\Gtm_Irq.c	  1330  #if((IRQ_GTM_ATOM8_SR0_PRIO > 0) || (IRQ_GTM_ATOM8_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1331  #if((IRQ_GTM_ATOM8_SR0_PRIO > 0) && (IRQ_GTM_ATOM8_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1332  IFX_INTERRUPT(GTMATOM8SR0_ISR, 0, IRQ_GTM_ATOM8_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1333  #elif IRQ_GTM_ATOM8_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1334  ISR(GTMATOM8SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1335  #endif
; ..\mcal_src\Gtm_Irq.c	  1336  {
; ..\mcal_src\Gtm_Irq.c	  1337    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1338  #if (IRQ_GTM_ATOM8_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1339    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1340  #endif
; ..\mcal_src\Gtm_Irq.c	  1341    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1342    Gtm_IsrAtomModule(8, 0);
; ..\mcal_src\Gtm_Irq.c	  1343  }
; ..\mcal_src\Gtm_Irq.c	  1344  #endif
; ..\mcal_src\Gtm_Irq.c	  1345  
; ..\mcal_src\Gtm_Irq.c	  1346  #if((IRQ_GTM_ATOM8_SR1_PRIO > 0) || (IRQ_GTM_ATOM8_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1347  #if((IRQ_GTM_ATOM8_SR1_PRIO > 0) && (IRQ_GTM_ATOM8_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1348  IFX_INTERRUPT(GTMATOM8SR1_ISR, 0, IRQ_GTM_ATOM8_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1349  #elif IRQ_GTM_ATOM8_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1350  ISR(GTMATOM8SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1351  #endif
; ..\mcal_src\Gtm_Irq.c	  1352  {
; ..\mcal_src\Gtm_Irq.c	  1353    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1354  #if (IRQ_GTM_ATOM8_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1355    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1356  #endif
; ..\mcal_src\Gtm_Irq.c	  1357    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1358    Gtm_IsrAtomModule(8, 2);
; ..\mcal_src\Gtm_Irq.c	  1359  }
; ..\mcal_src\Gtm_Irq.c	  1360  #endif
; ..\mcal_src\Gtm_Irq.c	  1361  
; ..\mcal_src\Gtm_Irq.c	  1362  #if((IRQ_GTM_ATOM8_SR2_PRIO > 0) || (IRQ_GTM_ATOM8_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1363  #if((IRQ_GTM_ATOM8_SR2_PRIO > 0) && (IRQ_GTM_ATOM8_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1364  IFX_INTERRUPT(GTMATOM8SR2_ISR, 0, IRQ_GTM_ATOM8_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1365  #elif IRQ_GTM_ATOM8_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1366  ISR(GTMATOM8SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1367  #endif
; ..\mcal_src\Gtm_Irq.c	  1368  {
; ..\mcal_src\Gtm_Irq.c	  1369    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1370  #if (IRQ_GTM_ATOM8_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1371    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1372  #endif
; ..\mcal_src\Gtm_Irq.c	  1373    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1374    Gtm_IsrAtomModule(8, 4);
; ..\mcal_src\Gtm_Irq.c	  1375  }
; ..\mcal_src\Gtm_Irq.c	  1376  #endif
; ..\mcal_src\Gtm_Irq.c	  1377  
; ..\mcal_src\Gtm_Irq.c	  1378  #if((IRQ_GTM_ATOM8_SR3_PRIO > 0) || (IRQ_GTM_ATOM8_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1379  #if((IRQ_GTM_ATOM8_SR3_PRIO > 0) && (IRQ_GTM_ATOM8_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1380  IFX_INTERRUPT(GTMATOM8SR3_ISR, 0, IRQ_GTM_ATOM8_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1381  #elif IRQ_GTM_ATOM8_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1382  ISR(GTMATOM8SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1383  #endif
; ..\mcal_src\Gtm_Irq.c	  1384  {
; ..\mcal_src\Gtm_Irq.c	  1385    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1386  #if (IRQ_GTM_ATOM8_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1387    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1388  #endif
; ..\mcal_src\Gtm_Irq.c	  1389    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1390    Gtm_IsrAtomModule(8, 6);
; ..\mcal_src\Gtm_Irq.c	  1391  }
; ..\mcal_src\Gtm_Irq.c	  1392  #endif
; ..\mcal_src\Gtm_Irq.c	  1393  
; ..\mcal_src\Gtm_Irq.c	  1394  #endif /* #if(IRQ_GTM_ATOM8_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1395  
; ..\mcal_src\Gtm_Irq.c	  1396  
; ..\mcal_src\Gtm_Irq.c	  1397  
; ..\mcal_src\Gtm_Irq.c	  1398  #if(IRQ_GTM_TIM0_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1399  /******************** TIM 0 *************************************/
; ..\mcal_src\Gtm_Irq.c	  1400  #if((IRQ_GTM_TIM0_SR0_PRIO > 0) || (IRQ_GTM_TIM0_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1401  #if((IRQ_GTM_TIM0_SR0_PRIO > 0) && (IRQ_GTM_TIM0_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1402  IFX_INTERRUPT(GTMTIM0SR0_ISR, 0, IRQ_GTM_TIM0_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1403  #elif IRQ_GTM_TIM0_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1404  ISR(GTMTIM0SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1405  #endif
; ..\mcal_src\Gtm_Irq.c	  1406  {
; ..\mcal_src\Gtm_Irq.c	  1407    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1408  #if (IRQ_GTM_TIM0_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1409    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1410  #endif
; ..\mcal_src\Gtm_Irq.c	  1411    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1412    Gtm_IsrTimModule(0, 0);
; ..\mcal_src\Gtm_Irq.c	  1413  }
; ..\mcal_src\Gtm_Irq.c	  1414  #endif
; ..\mcal_src\Gtm_Irq.c	  1415  
; ..\mcal_src\Gtm_Irq.c	  1416  #if((IRQ_GTM_TIM0_SR1_PRIO > 0) || (IRQ_GTM_TIM0_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1417  #if((IRQ_GTM_TIM0_SR1_PRIO > 0) && (IRQ_GTM_TIM0_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1418  IFX_INTERRUPT(GTMTIM0SR1_ISR, 0, IRQ_GTM_TIM0_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1419  #elif IRQ_GTM_TIM0_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1420  ISR(GTMTIM0SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1421  #endif
; ..\mcal_src\Gtm_Irq.c	  1422  {
; ..\mcal_src\Gtm_Irq.c	  1423    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1424  #if (IRQ_GTM_TIM0_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1425    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1426  #endif
; ..\mcal_src\Gtm_Irq.c	  1427    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1428    Gtm_IsrTimModule(0, 1);
; ..\mcal_src\Gtm_Irq.c	  1429  }
; ..\mcal_src\Gtm_Irq.c	  1430  #endif
; ..\mcal_src\Gtm_Irq.c	  1431  
; ..\mcal_src\Gtm_Irq.c	  1432  #if((IRQ_GTM_TIM0_SR2_PRIO > 0) || (IRQ_GTM_TIM0_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1433  #if((IRQ_GTM_TIM0_SR2_PRIO > 0) && (IRQ_GTM_TIM0_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1434  IFX_INTERRUPT(GTMTIM0SR2_ISR, 0, IRQ_GTM_TIM0_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1435  #elif IRQ_GTM_TIM0_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1436  ISR(GTMTIM0SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1437  #endif
; ..\mcal_src\Gtm_Irq.c	  1438  {
; ..\mcal_src\Gtm_Irq.c	  1439    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1440  #if (IRQ_GTM_TIM0_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1441    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1442  #endif
; ..\mcal_src\Gtm_Irq.c	  1443    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1444    Gtm_IsrTimModule(0, 2);
; ..\mcal_src\Gtm_Irq.c	  1445  }
; ..\mcal_src\Gtm_Irq.c	  1446  #endif
; ..\mcal_src\Gtm_Irq.c	  1447  
; ..\mcal_src\Gtm_Irq.c	  1448  #if((IRQ_GTM_TIM0_SR3_PRIO > 0) || (IRQ_GTM_TIM0_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1449  #if((IRQ_GTM_TIM0_SR3_PRIO > 0) && (IRQ_GTM_TIM0_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1450  IFX_INTERRUPT(GTMTIM0SR3_ISR, 0, IRQ_GTM_TIM0_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1451  #elif IRQ_GTM_TIM0_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1452  ISR(GTMTIM0SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1453  #endif
; ..\mcal_src\Gtm_Irq.c	  1454  {
; ..\mcal_src\Gtm_Irq.c	  1455    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1456  #if (IRQ_GTM_TIM0_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1457    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1458  #endif
; ..\mcal_src\Gtm_Irq.c	  1459    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1460    Gtm_IsrTimModule(0, 3);
; ..\mcal_src\Gtm_Irq.c	  1461  }
; ..\mcal_src\Gtm_Irq.c	  1462  #endif
; ..\mcal_src\Gtm_Irq.c	  1463  
; ..\mcal_src\Gtm_Irq.c	  1464  #if((IRQ_GTM_TIM0_SR4_PRIO > 0) || (IRQ_GTM_TIM0_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1465  #if((IRQ_GTM_TIM0_SR4_PRIO > 0) && (IRQ_GTM_TIM0_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1466  IFX_INTERRUPT(GTMTIM0SR4_ISR, 0, IRQ_GTM_TIM0_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1467  #elif IRQ_GTM_TIM0_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1468  ISR(GTMTIM0SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  1469  #endif
; ..\mcal_src\Gtm_Irq.c	  1470  {
; ..\mcal_src\Gtm_Irq.c	  1471    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1472  #if (IRQ_GTM_TIM0_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1473    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1474  #endif
; ..\mcal_src\Gtm_Irq.c	  1475    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1476    Gtm_IsrTimModule(0, 4);
; ..\mcal_src\Gtm_Irq.c	  1477  }
; ..\mcal_src\Gtm_Irq.c	  1478  #endif
; ..\mcal_src\Gtm_Irq.c	  1479  
; ..\mcal_src\Gtm_Irq.c	  1480  #if((IRQ_GTM_TIM0_SR5_PRIO > 0) || (IRQ_GTM_TIM0_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1481  #if((IRQ_GTM_TIM0_SR5_PRIO > 0) && (IRQ_GTM_TIM0_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1482  IFX_INTERRUPT(GTMTIM0SR5_ISR, 0, IRQ_GTM_TIM0_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1483  #elif IRQ_GTM_TIM0_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1484  ISR(GTMTIM0SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  1485  #endif
; ..\mcal_src\Gtm_Irq.c	  1486  {
; ..\mcal_src\Gtm_Irq.c	  1487    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1488  #if (IRQ_GTM_TIM0_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1489    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1490  #endif
; ..\mcal_src\Gtm_Irq.c	  1491    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1492    Gtm_IsrTimModule(0, 5);
; ..\mcal_src\Gtm_Irq.c	  1493  }
; ..\mcal_src\Gtm_Irq.c	  1494  #endif
; ..\mcal_src\Gtm_Irq.c	  1495  
; ..\mcal_src\Gtm_Irq.c	  1496  #if((IRQ_GTM_TIM0_SR6_PRIO > 0) || (IRQ_GTM_TIM0_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1497  #if((IRQ_GTM_TIM0_SR6_PRIO > 0) && (IRQ_GTM_TIM0_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1498  IFX_INTERRUPT(GTMTIM0SR6_ISR, 0, IRQ_GTM_TIM0_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1499  #elif IRQ_GTM_TIM0_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1500  ISR(GTMTIM0SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  1501  #endif
; ..\mcal_src\Gtm_Irq.c	  1502  {
; ..\mcal_src\Gtm_Irq.c	  1503    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1504  #if (IRQ_GTM_TIM0_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1505    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1506  #endif
; ..\mcal_src\Gtm_Irq.c	  1507    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1508    Gtm_IsrTimModule(0, 6);
; ..\mcal_src\Gtm_Irq.c	  1509  }
; ..\mcal_src\Gtm_Irq.c	  1510  #endif
; ..\mcal_src\Gtm_Irq.c	  1511  
; ..\mcal_src\Gtm_Irq.c	  1512  #if((IRQ_GTM_TIM0_SR7_PRIO > 0) || (IRQ_GTM_TIM0_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1513  #if((IRQ_GTM_TIM0_SR7_PRIO > 0) && (IRQ_GTM_TIM0_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1514  IFX_INTERRUPT(GTMTIM0SR7_ISR, 0, IRQ_GTM_TIM0_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1515  #elif IRQ_GTM_TIM0_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1516  ISR(GTMTIM0SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  1517  #endif
; ..\mcal_src\Gtm_Irq.c	  1518  {
; ..\mcal_src\Gtm_Irq.c	  1519    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1520  #if (IRQ_GTM_TIM0_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1521    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1522  #endif
; ..\mcal_src\Gtm_Irq.c	  1523    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1524    Gtm_IsrTimModule(0, 7);
; ..\mcal_src\Gtm_Irq.c	  1525  }
; ..\mcal_src\Gtm_Irq.c	  1526  #endif
; ..\mcal_src\Gtm_Irq.c	  1527  
; ..\mcal_src\Gtm_Irq.c	  1528  #endif /* #if(IRQ_GTM_TIM0_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1529  
; ..\mcal_src\Gtm_Irq.c	  1530  #if(IRQ_GTM_TIM1_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1531  
; ..\mcal_src\Gtm_Irq.c	  1532  /******************** TIM 1 *************************************/
; ..\mcal_src\Gtm_Irq.c	  1533  #if((IRQ_GTM_TIM1_SR0_PRIO > 0) || (IRQ_GTM_TIM1_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1534  #if((IRQ_GTM_TIM1_SR0_PRIO > 0) && (IRQ_GTM_TIM1_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1535  IFX_INTERRUPT(GTMTIM1SR0_ISR, 0, IRQ_GTM_TIM1_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1536  #elif IRQ_GTM_TIM1_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1537  ISR(GTMTIM1SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1538  #endif
; ..\mcal_src\Gtm_Irq.c	  1539  {
; ..\mcal_src\Gtm_Irq.c	  1540    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1541  #if (IRQ_GTM_TIM1_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1542    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1543  #endif
; ..\mcal_src\Gtm_Irq.c	  1544    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1545    Gtm_IsrTimModule(1, 0);
; ..\mcal_src\Gtm_Irq.c	  1546  }
; ..\mcal_src\Gtm_Irq.c	  1547  #endif
; ..\mcal_src\Gtm_Irq.c	  1548  
; ..\mcal_src\Gtm_Irq.c	  1549  #if((IRQ_GTM_TIM1_SR1_PRIO > 0) || (IRQ_GTM_TIM1_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1550  #if((IRQ_GTM_TIM1_SR1_PRIO > 0) && (IRQ_GTM_TIM1_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1551  IFX_INTERRUPT(GTMTIM1SR1_ISR, 0, IRQ_GTM_TIM1_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1552  #elif IRQ_GTM_TIM1_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1553  ISR(GTMTIM1SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1554  #endif
; ..\mcal_src\Gtm_Irq.c	  1555  {
; ..\mcal_src\Gtm_Irq.c	  1556    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1557  #if (IRQ_GTM_TIM1_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1558    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1559  #endif
; ..\mcal_src\Gtm_Irq.c	  1560    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1561    Gtm_IsrTimModule(1, 1);
; ..\mcal_src\Gtm_Irq.c	  1562  }
; ..\mcal_src\Gtm_Irq.c	  1563  #endif
; ..\mcal_src\Gtm_Irq.c	  1564  
; ..\mcal_src\Gtm_Irq.c	  1565  #if((IRQ_GTM_TIM1_SR2_PRIO > 0) || (IRQ_GTM_TIM1_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1566  #if((IRQ_GTM_TIM1_SR2_PRIO > 0) && (IRQ_GTM_TIM1_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1567  IFX_INTERRUPT(GTMTIM1SR2_ISR, 0, IRQ_GTM_TIM1_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1568  #elif IRQ_GTM_TIM1_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1569  ISR(GTMTIM1SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1570  #endif
; ..\mcal_src\Gtm_Irq.c	  1571  {
; ..\mcal_src\Gtm_Irq.c	  1572    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1573  #if (IRQ_GTM_TIM1_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1574    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1575  #endif
; ..\mcal_src\Gtm_Irq.c	  1576    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1577    Gtm_IsrTimModule(1, 2);
; ..\mcal_src\Gtm_Irq.c	  1578  }
; ..\mcal_src\Gtm_Irq.c	  1579  #endif
; ..\mcal_src\Gtm_Irq.c	  1580  
; ..\mcal_src\Gtm_Irq.c	  1581  #if((IRQ_GTM_TIM1_SR3_PRIO > 0) || (IRQ_GTM_TIM1_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1582  #if((IRQ_GTM_TIM1_SR3_PRIO > 0) && (IRQ_GTM_TIM1_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1583  IFX_INTERRUPT(GTMTIM1SR3_ISR, 0, IRQ_GTM_TIM1_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1584  #elif IRQ_GTM_TIM1_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1585  ISR(GTMTIM1SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1586  #endif
; ..\mcal_src\Gtm_Irq.c	  1587  {
; ..\mcal_src\Gtm_Irq.c	  1588    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1589  #if (IRQ_GTM_TIM1_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1590    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1591  #endif
; ..\mcal_src\Gtm_Irq.c	  1592    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1593    Gtm_IsrTimModule(1, 3);
; ..\mcal_src\Gtm_Irq.c	  1594  }
; ..\mcal_src\Gtm_Irq.c	  1595  #endif
; ..\mcal_src\Gtm_Irq.c	  1596  
; ..\mcal_src\Gtm_Irq.c	  1597  #if((IRQ_GTM_TIM1_SR4_PRIO > 0) || (IRQ_GTM_TIM1_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1598  #if((IRQ_GTM_TIM1_SR4_PRIO > 0) && (IRQ_GTM_TIM1_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1599  IFX_INTERRUPT(GTMTIM1SR4_ISR, 0, IRQ_GTM_TIM1_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1600  #elif IRQ_GTM_TIM1_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1601  ISR(GTMTIM1SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  1602  #endif
; ..\mcal_src\Gtm_Irq.c	  1603  {
; ..\mcal_src\Gtm_Irq.c	  1604    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1605  #if (IRQ_GTM_TIM1_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1606    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1607  #endif
; ..\mcal_src\Gtm_Irq.c	  1608    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1609    Gtm_IsrTimModule(1, 4);
; ..\mcal_src\Gtm_Irq.c	  1610  }
; ..\mcal_src\Gtm_Irq.c	  1611  #endif
; ..\mcal_src\Gtm_Irq.c	  1612  
; ..\mcal_src\Gtm_Irq.c	  1613  
; ..\mcal_src\Gtm_Irq.c	  1614  #if((IRQ_GTM_TIM1_SR5_PRIO > 0) || (IRQ_GTM_TIM1_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1615  #if((IRQ_GTM_TIM1_SR5_PRIO > 0) && (IRQ_GTM_TIM1_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1616  IFX_INTERRUPT(GTMTIM1SR5_ISR, 0, IRQ_GTM_TIM1_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1617  #elif IRQ_GTM_TIM1_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1618  ISR(GTMTIM1SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  1619  #endif
; ..\mcal_src\Gtm_Irq.c	  1620  {
; ..\mcal_src\Gtm_Irq.c	  1621    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1622  #if (IRQ_GTM_TIM1_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1623    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1624  #endif
; ..\mcal_src\Gtm_Irq.c	  1625    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1626    Gtm_IsrTimModule(1, 5);
; ..\mcal_src\Gtm_Irq.c	  1627  }
; ..\mcal_src\Gtm_Irq.c	  1628  #endif
; ..\mcal_src\Gtm_Irq.c	  1629  
; ..\mcal_src\Gtm_Irq.c	  1630  #if((IRQ_GTM_TIM1_SR6_PRIO > 0) || (IRQ_GTM_TIM1_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1631  #if((IRQ_GTM_TIM1_SR6_PRIO > 0) && (IRQ_GTM_TIM1_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1632  IFX_INTERRUPT(GTMTIM1SR6_ISR, 0, IRQ_GTM_TIM1_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1633  #elif IRQ_GTM_TIM1_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1634  ISR(GTMTIM1SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  1635  #endif
; ..\mcal_src\Gtm_Irq.c	  1636  {
; ..\mcal_src\Gtm_Irq.c	  1637    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1638  #if (IRQ_GTM_TIM1_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1639    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1640  #endif
; ..\mcal_src\Gtm_Irq.c	  1641    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1642    Gtm_IsrTimModule(1, 6);
; ..\mcal_src\Gtm_Irq.c	  1643  }
; ..\mcal_src\Gtm_Irq.c	  1644  #endif
; ..\mcal_src\Gtm_Irq.c	  1645  
; ..\mcal_src\Gtm_Irq.c	  1646  #if((IRQ_GTM_TIM1_SR7_PRIO > 0) || (IRQ_GTM_TIM1_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1647  #if((IRQ_GTM_TIM1_SR7_PRIO > 0) && (IRQ_GTM_TIM1_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1648  IFX_INTERRUPT(GTMTIM1SR7_ISR, 0, IRQ_GTM_TIM1_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1649  #elif IRQ_GTM_TIM1_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1650  ISR(GTMTIM1SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  1651  #endif
; ..\mcal_src\Gtm_Irq.c	  1652  {
; ..\mcal_src\Gtm_Irq.c	  1653    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1654  #if (IRQ_GTM_TIM1_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1655    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1656  #endif
; ..\mcal_src\Gtm_Irq.c	  1657    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1658    Gtm_IsrTimModule(1, 7);
; ..\mcal_src\Gtm_Irq.c	  1659  }
; ..\mcal_src\Gtm_Irq.c	  1660  #endif
; ..\mcal_src\Gtm_Irq.c	  1661  
; ..\mcal_src\Gtm_Irq.c	  1662  #endif /* #if(IRQ_GTM_TIM1_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1663  
; ..\mcal_src\Gtm_Irq.c	  1664  #if(IRQ_GTM_TIM2_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1665  /******************** TIM 2 *************************************/
; ..\mcal_src\Gtm_Irq.c	  1666  
; ..\mcal_src\Gtm_Irq.c	  1667  #if((IRQ_GTM_TIM2_SR0_PRIO > 0) || (IRQ_GTM_TIM2_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1668  #if((IRQ_GTM_TIM2_SR0_PRIO > 0) && (IRQ_GTM_TIM2_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1669  IFX_INTERRUPT(GTMTIM2SR0_ISR, 0, IRQ_GTM_TIM2_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1670  #elif IRQ_GTM_TIM2_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1671  ISR(GTMTIM2SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1672  #endif
; ..\mcal_src\Gtm_Irq.c	  1673  {
; ..\mcal_src\Gtm_Irq.c	  1674    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1675  #if (IRQ_GTM_TIM2_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1676    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1677  #endif
; ..\mcal_src\Gtm_Irq.c	  1678    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1679    Gtm_IsrTimModule(2, 0);
; ..\mcal_src\Gtm_Irq.c	  1680  }
; ..\mcal_src\Gtm_Irq.c	  1681  #endif
; ..\mcal_src\Gtm_Irq.c	  1682  
; ..\mcal_src\Gtm_Irq.c	  1683  #if((IRQ_GTM_TIM2_SR1_PRIO > 0) || (IRQ_GTM_TIM2_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1684  #if((IRQ_GTM_TIM2_SR1_PRIO > 0) && (IRQ_GTM_TIM2_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1685  IFX_INTERRUPT(GTMTIM2SR1_ISR, 0, IRQ_GTM_TIM2_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1686  #elif IRQ_GTM_TIM2_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1687  ISR(GTMTIM2SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1688  #endif
; ..\mcal_src\Gtm_Irq.c	  1689  {
; ..\mcal_src\Gtm_Irq.c	  1690    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1691  #if (IRQ_GTM_TIM2_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1692    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1693  #endif
; ..\mcal_src\Gtm_Irq.c	  1694    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1695    Gtm_IsrTimModule(2, 1);
; ..\mcal_src\Gtm_Irq.c	  1696  }
; ..\mcal_src\Gtm_Irq.c	  1697  #endif
; ..\mcal_src\Gtm_Irq.c	  1698  
; ..\mcal_src\Gtm_Irq.c	  1699  
; ..\mcal_src\Gtm_Irq.c	  1700  #if((IRQ_GTM_TIM2_SR2_PRIO > 0) || (IRQ_GTM_TIM2_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1701  #if((IRQ_GTM_TIM2_SR2_PRIO > 0) && (IRQ_GTM_TIM2_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1702  IFX_INTERRUPT(GTMTIM2SR2_ISR, 0, IRQ_GTM_TIM2_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1703  #elif IRQ_GTM_TIM2_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1704  ISR(GTMTIM2SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1705  #endif
; ..\mcal_src\Gtm_Irq.c	  1706  {
; ..\mcal_src\Gtm_Irq.c	  1707    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1708  #if (IRQ_GTM_TIM2_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1709    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1710  #endif
; ..\mcal_src\Gtm_Irq.c	  1711    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1712    Gtm_IsrTimModule(2, 2);
; ..\mcal_src\Gtm_Irq.c	  1713  }
; ..\mcal_src\Gtm_Irq.c	  1714  #endif
; ..\mcal_src\Gtm_Irq.c	  1715  
; ..\mcal_src\Gtm_Irq.c	  1716  #if((IRQ_GTM_TIM2_SR3_PRIO > 0) || (IRQ_GTM_TIM2_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1717  #if((IRQ_GTM_TIM2_SR3_PRIO > 0) && (IRQ_GTM_TIM2_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1718  IFX_INTERRUPT(GTMTIM2SR3_ISR, 0, IRQ_GTM_TIM2_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1719  #elif IRQ_GTM_TIM2_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1720  ISR(GTMTIM2SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1721  #endif
; ..\mcal_src\Gtm_Irq.c	  1722  {
; ..\mcal_src\Gtm_Irq.c	  1723    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1724  #if (IRQ_GTM_TIM2_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1725    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1726  #endif
; ..\mcal_src\Gtm_Irq.c	  1727    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1728    Gtm_IsrTimModule(2, 3);
; ..\mcal_src\Gtm_Irq.c	  1729  }
; ..\mcal_src\Gtm_Irq.c	  1730  #endif
; ..\mcal_src\Gtm_Irq.c	  1731  
; ..\mcal_src\Gtm_Irq.c	  1732  
; ..\mcal_src\Gtm_Irq.c	  1733  #if((IRQ_GTM_TIM2_SR4_PRIO > 0) || (IRQ_GTM_TIM2_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1734  #if((IRQ_GTM_TIM2_SR4_PRIO > 0) && (IRQ_GTM_TIM2_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1735  IFX_INTERRUPT(GTMTIM2SR4_ISR, 0, IRQ_GTM_TIM2_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1736  #elif IRQ_GTM_TIM2_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1737  ISR(GTMTIM2SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  1738  #endif
; ..\mcal_src\Gtm_Irq.c	  1739  {
; ..\mcal_src\Gtm_Irq.c	  1740    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1741  #if (IRQ_GTM_TIM2_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1742    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1743  #endif
; ..\mcal_src\Gtm_Irq.c	  1744    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1745    Gtm_IsrTimModule(2, 4);
; ..\mcal_src\Gtm_Irq.c	  1746  }
; ..\mcal_src\Gtm_Irq.c	  1747  #endif
; ..\mcal_src\Gtm_Irq.c	  1748  
; ..\mcal_src\Gtm_Irq.c	  1749  #if((IRQ_GTM_TIM2_SR5_PRIO > 0) || (IRQ_GTM_TIM2_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1750  #if((IRQ_GTM_TIM2_SR5_PRIO > 0) && (IRQ_GTM_TIM2_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1751  IFX_INTERRUPT(GTMTIM2SR5_ISR, 0, IRQ_GTM_TIM2_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1752  #elif IRQ_GTM_TIM2_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1753  ISR(GTMTIM2SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  1754  #endif
; ..\mcal_src\Gtm_Irq.c	  1755  {
; ..\mcal_src\Gtm_Irq.c	  1756    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1757  #if (IRQ_GTM_TIM2_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1758    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1759  #endif
; ..\mcal_src\Gtm_Irq.c	  1760    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1761    Gtm_IsrTimModule(2, 5);
; ..\mcal_src\Gtm_Irq.c	  1762  }
; ..\mcal_src\Gtm_Irq.c	  1763  #endif
; ..\mcal_src\Gtm_Irq.c	  1764  
; ..\mcal_src\Gtm_Irq.c	  1765  
; ..\mcal_src\Gtm_Irq.c	  1766  #if((IRQ_GTM_TIM2_SR6_PRIO > 0) || (IRQ_GTM_TIM2_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1767  #if((IRQ_GTM_TIM2_SR6_PRIO > 0) && (IRQ_GTM_TIM2_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1768  IFX_INTERRUPT(GTMTIM2SR6_ISR, 0, IRQ_GTM_TIM2_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1769  #elif IRQ_GTM_TIM2_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1770  ISR(GTMTIM2SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  1771  #endif
; ..\mcal_src\Gtm_Irq.c	  1772  {
; ..\mcal_src\Gtm_Irq.c	  1773    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1774  #if (IRQ_GTM_TIM2_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1775    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1776  #endif
; ..\mcal_src\Gtm_Irq.c	  1777    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1778    Gtm_IsrTimModule(2, 6);
; ..\mcal_src\Gtm_Irq.c	  1779  }
; ..\mcal_src\Gtm_Irq.c	  1780  #endif
; ..\mcal_src\Gtm_Irq.c	  1781  
; ..\mcal_src\Gtm_Irq.c	  1782  #if((IRQ_GTM_TIM2_SR7_PRIO > 0) || (IRQ_GTM_TIM2_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1783  #if((IRQ_GTM_TIM2_SR7_PRIO > 0) && (IRQ_GTM_TIM2_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1784  IFX_INTERRUPT(GTMTIM2SR7_ISR, 0, IRQ_GTM_TIM2_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1785  #elif IRQ_GTM_TIM2_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1786  ISR(GTMTIM2SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  1787  #endif
; ..\mcal_src\Gtm_Irq.c	  1788  {
; ..\mcal_src\Gtm_Irq.c	  1789    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1790  #if (IRQ_GTM_TIM2_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1791    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1792  #endif
; ..\mcal_src\Gtm_Irq.c	  1793    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1794    Gtm_IsrTimModule(2, 7);
; ..\mcal_src\Gtm_Irq.c	  1795  }
; ..\mcal_src\Gtm_Irq.c	  1796  #endif
; ..\mcal_src\Gtm_Irq.c	  1797  
; ..\mcal_src\Gtm_Irq.c	  1798  #endif /* #if(IRQ_GTM_TIM2_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1799  
; ..\mcal_src\Gtm_Irq.c	  1800  #if(IRQ_GTM_TIM3_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1801  /******************** TIM 3 *************************************/
; ..\mcal_src\Gtm_Irq.c	  1802  
; ..\mcal_src\Gtm_Irq.c	  1803  #if((IRQ_GTM_TIM3_SR0_PRIO > 0) || (IRQ_GTM_TIM3_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1804  #if((IRQ_GTM_TIM3_SR0_PRIO > 0) && (IRQ_GTM_TIM3_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1805  IFX_INTERRUPT(GTMTIM3SR0_ISR, 0, IRQ_GTM_TIM3_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1806  #elif IRQ_GTM_TIM3_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1807  ISR(GTMTIM3SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1808  #endif
; ..\mcal_src\Gtm_Irq.c	  1809  {
; ..\mcal_src\Gtm_Irq.c	  1810    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1811  #if (IRQ_GTM_TIM3_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1812    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1813  #endif
; ..\mcal_src\Gtm_Irq.c	  1814    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1815    Gtm_IsrTimModule(3, 0);
; ..\mcal_src\Gtm_Irq.c	  1816  }
; ..\mcal_src\Gtm_Irq.c	  1817  #endif
; ..\mcal_src\Gtm_Irq.c	  1818  
; ..\mcal_src\Gtm_Irq.c	  1819  #if((IRQ_GTM_TIM3_SR1_PRIO > 0) || (IRQ_GTM_TIM3_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1820  #if((IRQ_GTM_TIM3_SR1_PRIO > 0) && (IRQ_GTM_TIM3_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1821  IFX_INTERRUPT(GTMTIM3SR1_ISR, 0, IRQ_GTM_TIM3_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1822  #elif IRQ_GTM_TIM3_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1823  ISR(GTMTIM3SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1824  #endif
; ..\mcal_src\Gtm_Irq.c	  1825  {
; ..\mcal_src\Gtm_Irq.c	  1826    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1827  #if (IRQ_GTM_TIM3_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1828    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1829  #endif
; ..\mcal_src\Gtm_Irq.c	  1830    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1831    Gtm_IsrTimModule(3, 1);
; ..\mcal_src\Gtm_Irq.c	  1832  }
; ..\mcal_src\Gtm_Irq.c	  1833  #endif
; ..\mcal_src\Gtm_Irq.c	  1834  
; ..\mcal_src\Gtm_Irq.c	  1835  
; ..\mcal_src\Gtm_Irq.c	  1836  #if((IRQ_GTM_TIM3_SR2_PRIO > 0) || (IRQ_GTM_TIM3_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1837  #if((IRQ_GTM_TIM3_SR2_PRIO > 0) && (IRQ_GTM_TIM3_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1838  IFX_INTERRUPT(GTMTIM3SR2_ISR, 0, IRQ_GTM_TIM3_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1839  #elif IRQ_GTM_TIM3_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1840  ISR(GTMTIM3SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1841  #endif
; ..\mcal_src\Gtm_Irq.c	  1842  {
; ..\mcal_src\Gtm_Irq.c	  1843    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1844  #if (IRQ_GTM_TIM3_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1845    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1846  #endif
; ..\mcal_src\Gtm_Irq.c	  1847    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1848    Gtm_IsrTimModule(3, 2);
; ..\mcal_src\Gtm_Irq.c	  1849  }
; ..\mcal_src\Gtm_Irq.c	  1850  #endif
; ..\mcal_src\Gtm_Irq.c	  1851  
; ..\mcal_src\Gtm_Irq.c	  1852  #if((IRQ_GTM_TIM3_SR3_PRIO > 0) || (IRQ_GTM_TIM3_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1853  #if((IRQ_GTM_TIM3_SR3_PRIO > 0) && (IRQ_GTM_TIM3_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1854  IFX_INTERRUPT(GTMTIM3SR3_ISR, 0, IRQ_GTM_TIM3_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1855  #elif IRQ_GTM_TIM3_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1856  ISR(GTMTIM3SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1857  #endif
; ..\mcal_src\Gtm_Irq.c	  1858  {
; ..\mcal_src\Gtm_Irq.c	  1859    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1860  #if (IRQ_GTM_TIM3_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1861    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1862  #endif
; ..\mcal_src\Gtm_Irq.c	  1863    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1864    Gtm_IsrTimModule(3, 3);
; ..\mcal_src\Gtm_Irq.c	  1865  }
; ..\mcal_src\Gtm_Irq.c	  1866  #endif
; ..\mcal_src\Gtm_Irq.c	  1867  
; ..\mcal_src\Gtm_Irq.c	  1868  
; ..\mcal_src\Gtm_Irq.c	  1869  #if((IRQ_GTM_TIM3_SR4_PRIO > 0) || (IRQ_GTM_TIM3_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1870  #if((IRQ_GTM_TIM3_SR4_PRIO > 0) && (IRQ_GTM_TIM3_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1871  IFX_INTERRUPT(GTMTIM3SR4_ISR, 0, IRQ_GTM_TIM3_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1872  #elif IRQ_GTM_TIM3_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1873  ISR(GTMTIM3SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  1874  #endif
; ..\mcal_src\Gtm_Irq.c	  1875  {
; ..\mcal_src\Gtm_Irq.c	  1876    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1877  #if (IRQ_GTM_TIM3_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1878    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1879  #endif
; ..\mcal_src\Gtm_Irq.c	  1880    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1881    Gtm_IsrTimModule(3, 4);
; ..\mcal_src\Gtm_Irq.c	  1882  }
; ..\mcal_src\Gtm_Irq.c	  1883  #endif
; ..\mcal_src\Gtm_Irq.c	  1884  
; ..\mcal_src\Gtm_Irq.c	  1885  #if((IRQ_GTM_TIM3_SR5_PRIO > 0) || (IRQ_GTM_TIM3_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1886  #if((IRQ_GTM_TIM3_SR5_PRIO > 0) && (IRQ_GTM_TIM3_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1887  IFX_INTERRUPT(GTMTIM3SR5_ISR, 0, IRQ_GTM_TIM3_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1888  #elif IRQ_GTM_TIM3_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1889  ISR(GTMTIM3SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  1890  #endif
; ..\mcal_src\Gtm_Irq.c	  1891  {
; ..\mcal_src\Gtm_Irq.c	  1892    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1893  #if (IRQ_GTM_TIM3_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1894    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1895  #endif
; ..\mcal_src\Gtm_Irq.c	  1896    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1897    Gtm_IsrTimModule(3, 5);
; ..\mcal_src\Gtm_Irq.c	  1898  }
; ..\mcal_src\Gtm_Irq.c	  1899  #endif
; ..\mcal_src\Gtm_Irq.c	  1900  
; ..\mcal_src\Gtm_Irq.c	  1901  
; ..\mcal_src\Gtm_Irq.c	  1902  #if((IRQ_GTM_TIM3_SR6_PRIO > 0) || (IRQ_GTM_TIM3_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1903  #if((IRQ_GTM_TIM3_SR6_PRIO > 0) && (IRQ_GTM_TIM3_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1904  IFX_INTERRUPT(GTMTIM3SR6_ISR, 0, IRQ_GTM_TIM3_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1905  #elif IRQ_GTM_TIM3_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1906  ISR(GTMTIM3SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  1907  #endif
; ..\mcal_src\Gtm_Irq.c	  1908  {
; ..\mcal_src\Gtm_Irq.c	  1909    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1910  #if (IRQ_GTM_TIM3_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1911    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1912  #endif
; ..\mcal_src\Gtm_Irq.c	  1913    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1914    Gtm_IsrTimModule(3, 6);
; ..\mcal_src\Gtm_Irq.c	  1915  }
; ..\mcal_src\Gtm_Irq.c	  1916  #endif
; ..\mcal_src\Gtm_Irq.c	  1917  
; ..\mcal_src\Gtm_Irq.c	  1918  #if((IRQ_GTM_TIM3_SR7_PRIO > 0) || (IRQ_GTM_TIM3_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1919  #if((IRQ_GTM_TIM3_SR7_PRIO > 0) && (IRQ_GTM_TIM3_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1920  IFX_INTERRUPT(GTMTIM3SR7_ISR, 0, IRQ_GTM_TIM3_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1921  #elif IRQ_GTM_TIM3_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1922  ISR(GTMTIM3SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  1923  #endif
; ..\mcal_src\Gtm_Irq.c	  1924  {
; ..\mcal_src\Gtm_Irq.c	  1925    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1926  #if (IRQ_GTM_TIM3_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1927    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1928  #endif
; ..\mcal_src\Gtm_Irq.c	  1929    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1930    Gtm_IsrTimModule(3, 7);
; ..\mcal_src\Gtm_Irq.c	  1931  }
; ..\mcal_src\Gtm_Irq.c	  1932  #endif
; ..\mcal_src\Gtm_Irq.c	  1933  
; ..\mcal_src\Gtm_Irq.c	  1934  #endif /* #if(IRQ_GTM_TIM3_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  1935  
; ..\mcal_src\Gtm_Irq.c	  1936  
; ..\mcal_src\Gtm_Irq.c	  1937  
; ..\mcal_src\Gtm_Irq.c	  1938  #if(IRQ_GTM_TIM4_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  1939  /******************** TIM 4 *************************************/
; ..\mcal_src\Gtm_Irq.c	  1940  
; ..\mcal_src\Gtm_Irq.c	  1941  #if((IRQ_GTM_TIM4_SR0_PRIO > 0) || (IRQ_GTM_TIM4_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1942  #if((IRQ_GTM_TIM4_SR0_PRIO > 0) && (IRQ_GTM_TIM4_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1943  IFX_INTERRUPT(GTMTIM4SR0_ISR, 0, IRQ_GTM_TIM4_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1944  #elif IRQ_GTM_TIM4_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1945  ISR(GTMTIM4SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  1946  #endif
; ..\mcal_src\Gtm_Irq.c	  1947  {
; ..\mcal_src\Gtm_Irq.c	  1948    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1949  #if (IRQ_GTM_TIM4_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1950    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1951  #endif
; ..\mcal_src\Gtm_Irq.c	  1952    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1953    Gtm_IsrTimModule(4, 0);
; ..\mcal_src\Gtm_Irq.c	  1954  }
; ..\mcal_src\Gtm_Irq.c	  1955  #endif
; ..\mcal_src\Gtm_Irq.c	  1956  
; ..\mcal_src\Gtm_Irq.c	  1957  #if((IRQ_GTM_TIM4_SR1_PRIO > 0) || (IRQ_GTM_TIM4_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1958  #if((IRQ_GTM_TIM4_SR1_PRIO > 0) && (IRQ_GTM_TIM4_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1959  IFX_INTERRUPT(GTMTIM4SR1_ISR, 0, IRQ_GTM_TIM4_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1960  #elif IRQ_GTM_TIM4_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1961  ISR(GTMTIM4SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  1962  #endif
; ..\mcal_src\Gtm_Irq.c	  1963  {
; ..\mcal_src\Gtm_Irq.c	  1964    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1965  #if (IRQ_GTM_TIM4_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1966    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1967  #endif
; ..\mcal_src\Gtm_Irq.c	  1968    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1969    Gtm_IsrTimModule(4, 1);
; ..\mcal_src\Gtm_Irq.c	  1970  }
; ..\mcal_src\Gtm_Irq.c	  1971  #endif
; ..\mcal_src\Gtm_Irq.c	  1972  
; ..\mcal_src\Gtm_Irq.c	  1973  
; ..\mcal_src\Gtm_Irq.c	  1974  #if((IRQ_GTM_TIM4_SR2_PRIO > 0) || (IRQ_GTM_TIM4_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1975  #if((IRQ_GTM_TIM4_SR2_PRIO > 0) && (IRQ_GTM_TIM4_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1976  IFX_INTERRUPT(GTMTIM4SR2_ISR, 0, IRQ_GTM_TIM4_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1977  #elif IRQ_GTM_TIM4_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1978  ISR(GTMTIM4SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  1979  #endif
; ..\mcal_src\Gtm_Irq.c	  1980  {
; ..\mcal_src\Gtm_Irq.c	  1981    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1982  #if (IRQ_GTM_TIM4_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1983    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  1984  #endif
; ..\mcal_src\Gtm_Irq.c	  1985    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  1986    Gtm_IsrTimModule(4, 2);
; ..\mcal_src\Gtm_Irq.c	  1987  }
; ..\mcal_src\Gtm_Irq.c	  1988  #endif
; ..\mcal_src\Gtm_Irq.c	  1989  
; ..\mcal_src\Gtm_Irq.c	  1990  #if((IRQ_GTM_TIM4_SR3_PRIO > 0) || (IRQ_GTM_TIM4_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  1991  #if((IRQ_GTM_TIM4_SR3_PRIO > 0) && (IRQ_GTM_TIM4_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  1992  IFX_INTERRUPT(GTMTIM4SR3_ISR, 0, IRQ_GTM_TIM4_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  1993  #elif IRQ_GTM_TIM4_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  1994  ISR(GTMTIM4SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  1995  #endif
; ..\mcal_src\Gtm_Irq.c	  1996  {
; ..\mcal_src\Gtm_Irq.c	  1997    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  1998  #if (IRQ_GTM_TIM4_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  1999    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2000  #endif
; ..\mcal_src\Gtm_Irq.c	  2001    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2002    Gtm_IsrTimModule(4, 3);
; ..\mcal_src\Gtm_Irq.c	  2003  }
; ..\mcal_src\Gtm_Irq.c	  2004  #endif
; ..\mcal_src\Gtm_Irq.c	  2005  
; ..\mcal_src\Gtm_Irq.c	  2006  
; ..\mcal_src\Gtm_Irq.c	  2007  #if((IRQ_GTM_TIM4_SR4_PRIO > 0) || (IRQ_GTM_TIM4_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2008  #if((IRQ_GTM_TIM4_SR4_PRIO > 0) && (IRQ_GTM_TIM4_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2009  IFX_INTERRUPT(GTMTIM4SR4_ISR, 0, IRQ_GTM_TIM4_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2010  #elif IRQ_GTM_TIM4_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2011  ISR(GTMTIM4SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  2012  #endif
; ..\mcal_src\Gtm_Irq.c	  2013  {
; ..\mcal_src\Gtm_Irq.c	  2014    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2015  #if (IRQ_GTM_TIM4_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2016    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2017  #endif
; ..\mcal_src\Gtm_Irq.c	  2018    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2019    Gtm_IsrTimModule(4, 4);
; ..\mcal_src\Gtm_Irq.c	  2020  }
; ..\mcal_src\Gtm_Irq.c	  2021  #endif
; ..\mcal_src\Gtm_Irq.c	  2022  
; ..\mcal_src\Gtm_Irq.c	  2023  #if((IRQ_GTM_TIM4_SR5_PRIO > 0) || (IRQ_GTM_TIM4_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2024  #if((IRQ_GTM_TIM4_SR5_PRIO > 0) && (IRQ_GTM_TIM4_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2025  IFX_INTERRUPT(GTMTIM4SR5_ISR, 0, IRQ_GTM_TIM4_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2026  #elif IRQ_GTM_TIM4_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2027  ISR(GTMTIM4SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  2028  #endif
; ..\mcal_src\Gtm_Irq.c	  2029  {
; ..\mcal_src\Gtm_Irq.c	  2030    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2031  #if (IRQ_GTM_TIM4_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2032    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2033  #endif
; ..\mcal_src\Gtm_Irq.c	  2034    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2035    Gtm_IsrTimModule(4, 5);
; ..\mcal_src\Gtm_Irq.c	  2036  }
; ..\mcal_src\Gtm_Irq.c	  2037  #endif
; ..\mcal_src\Gtm_Irq.c	  2038  
; ..\mcal_src\Gtm_Irq.c	  2039  
; ..\mcal_src\Gtm_Irq.c	  2040  #if((IRQ_GTM_TIM4_SR6_PRIO > 0) || (IRQ_GTM_TIM4_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2041  #if((IRQ_GTM_TIM4_SR6_PRIO > 0) && (IRQ_GTM_TIM4_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2042  IFX_INTERRUPT(GTMTIM4SR6_ISR, 0, IRQ_GTM_TIM4_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2043  #elif IRQ_GTM_TIM4_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2044  ISR(GTMTIM4SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  2045  #endif
; ..\mcal_src\Gtm_Irq.c	  2046  {
; ..\mcal_src\Gtm_Irq.c	  2047    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2048  #if (IRQ_GTM_TIM4_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2049    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2050  #endif
; ..\mcal_src\Gtm_Irq.c	  2051    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2052    Gtm_IsrTimModule(4, 6);
; ..\mcal_src\Gtm_Irq.c	  2053  }
; ..\mcal_src\Gtm_Irq.c	  2054  #endif
; ..\mcal_src\Gtm_Irq.c	  2055  
; ..\mcal_src\Gtm_Irq.c	  2056  #if((IRQ_GTM_TIM4_SR7_PRIO > 0) || (IRQ_GTM_TIM4_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2057  #if((IRQ_GTM_TIM4_SR7_PRIO > 0) && (IRQ_GTM_TIM4_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2058  IFX_INTERRUPT(GTMTIM4SR7_ISR, 0, IRQ_GTM_TIM4_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2059  #elif IRQ_GTM_TIM4_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2060  ISR(GTMTIM4SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  2061  #endif
; ..\mcal_src\Gtm_Irq.c	  2062  {
; ..\mcal_src\Gtm_Irq.c	  2063    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2064  #if (IRQ_GTM_TIM4_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2065    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2066  #endif
; ..\mcal_src\Gtm_Irq.c	  2067    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2068    Gtm_IsrTimModule(4, 7);
; ..\mcal_src\Gtm_Irq.c	  2069  }
; ..\mcal_src\Gtm_Irq.c	  2070  #endif
; ..\mcal_src\Gtm_Irq.c	  2071  
; ..\mcal_src\Gtm_Irq.c	  2072  #endif /* #if(IRQ_GTM_TIM4_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  2073  
; ..\mcal_src\Gtm_Irq.c	  2074  
; ..\mcal_src\Gtm_Irq.c	  2075  
; ..\mcal_src\Gtm_Irq.c	  2076  #if(IRQ_GTM_TIM5_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2077  /******************** TIM 5 *************************************/
; ..\mcal_src\Gtm_Irq.c	  2078  
; ..\mcal_src\Gtm_Irq.c	  2079  #if((IRQ_GTM_TIM5_SR0_PRIO > 0) || (IRQ_GTM_TIM5_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2080  #if((IRQ_GTM_TIM5_SR0_PRIO > 0) && (IRQ_GTM_TIM5_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2081  IFX_INTERRUPT(GTMTIM5SR0_ISR, 0, IRQ_GTM_TIM5_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2082  #elif IRQ_GTM_TIM5_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2083  ISR(GTMTIM5SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  2084  #endif
; ..\mcal_src\Gtm_Irq.c	  2085  {
; ..\mcal_src\Gtm_Irq.c	  2086    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2087  #if (IRQ_GTM_TIM5_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2088    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2089  #endif
; ..\mcal_src\Gtm_Irq.c	  2090    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2091    Gtm_IsrTimModule(5, 0);
; ..\mcal_src\Gtm_Irq.c	  2092  }
; ..\mcal_src\Gtm_Irq.c	  2093  #endif
; ..\mcal_src\Gtm_Irq.c	  2094  
; ..\mcal_src\Gtm_Irq.c	  2095  #if((IRQ_GTM_TIM5_SR1_PRIO > 0) || (IRQ_GTM_TIM5_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2096  #if((IRQ_GTM_TIM5_SR1_PRIO > 0) && (IRQ_GTM_TIM5_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2097  IFX_INTERRUPT(GTMTIM5SR1_ISR, 0, IRQ_GTM_TIM5_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2098  #elif IRQ_GTM_TIM5_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2099  ISR(GTMTIM5SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  2100  #endif
; ..\mcal_src\Gtm_Irq.c	  2101  {
; ..\mcal_src\Gtm_Irq.c	  2102    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2103  #if (IRQ_GTM_TIM5_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2104    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2105  #endif
; ..\mcal_src\Gtm_Irq.c	  2106    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2107    Gtm_IsrTimModule(5, 1);
; ..\mcal_src\Gtm_Irq.c	  2108  }
; ..\mcal_src\Gtm_Irq.c	  2109  #endif
; ..\mcal_src\Gtm_Irq.c	  2110  
; ..\mcal_src\Gtm_Irq.c	  2111  
; ..\mcal_src\Gtm_Irq.c	  2112  #if((IRQ_GTM_TIM5_SR2_PRIO > 0) || (IRQ_GTM_TIM5_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2113  #if((IRQ_GTM_TIM5_SR2_PRIO > 0) && (IRQ_GTM_TIM5_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2114  IFX_INTERRUPT(GTMTIM5SR2_ISR, 0, IRQ_GTM_TIM5_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2115  #elif IRQ_GTM_TIM5_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2116  ISR(GTMTIM5SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  2117  #endif
; ..\mcal_src\Gtm_Irq.c	  2118  {
; ..\mcal_src\Gtm_Irq.c	  2119    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2120  #if (IRQ_GTM_TIM5_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2121    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2122  #endif
; ..\mcal_src\Gtm_Irq.c	  2123    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2124    Gtm_IsrTimModule(5, 2);
; ..\mcal_src\Gtm_Irq.c	  2125  }
; ..\mcal_src\Gtm_Irq.c	  2126  #endif
; ..\mcal_src\Gtm_Irq.c	  2127  
; ..\mcal_src\Gtm_Irq.c	  2128  #if((IRQ_GTM_TIM5_SR3_PRIO > 0) || (IRQ_GTM_TIM5_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2129  #if((IRQ_GTM_TIM5_SR3_PRIO > 0) && (IRQ_GTM_TIM5_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2130  IFX_INTERRUPT(GTMTIM5SR3_ISR, 0, IRQ_GTM_TIM5_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2131  #elif IRQ_GTM_TIM5_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2132  ISR(GTMTIM5SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  2133  #endif
; ..\mcal_src\Gtm_Irq.c	  2134  {
; ..\mcal_src\Gtm_Irq.c	  2135    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2136  #if (IRQ_GTM_TIM5_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2137    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2138  #endif
; ..\mcal_src\Gtm_Irq.c	  2139    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2140    Gtm_IsrTimModule(5, 3);
; ..\mcal_src\Gtm_Irq.c	  2141  }
; ..\mcal_src\Gtm_Irq.c	  2142  #endif
; ..\mcal_src\Gtm_Irq.c	  2143  
; ..\mcal_src\Gtm_Irq.c	  2144  
; ..\mcal_src\Gtm_Irq.c	  2145  #if((IRQ_GTM_TIM5_SR4_PRIO > 0) || (IRQ_GTM_TIM5_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2146  #if((IRQ_GTM_TIM5_SR4_PRIO > 0) && (IRQ_GTM_TIM5_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2147  IFX_INTERRUPT(GTMTIM5SR4_ISR, 0, IRQ_GTM_TIM5_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2148  #elif IRQ_GTM_TIM5_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2149  ISR(GTMTIM5SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  2150  #endif
; ..\mcal_src\Gtm_Irq.c	  2151  {
; ..\mcal_src\Gtm_Irq.c	  2152    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2153  #if (IRQ_GTM_TIM5_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2154    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2155  #endif
; ..\mcal_src\Gtm_Irq.c	  2156    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2157    Gtm_IsrTimModule(5, 4);
; ..\mcal_src\Gtm_Irq.c	  2158  }
; ..\mcal_src\Gtm_Irq.c	  2159  #endif
; ..\mcal_src\Gtm_Irq.c	  2160  
; ..\mcal_src\Gtm_Irq.c	  2161  #if((IRQ_GTM_TIM5_SR5_PRIO > 0) || (IRQ_GTM_TIM5_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2162  #if((IRQ_GTM_TIM5_SR5_PRIO > 0) && (IRQ_GTM_TIM5_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2163  IFX_INTERRUPT(GTMTIM5SR5_ISR, 0, IRQ_GTM_TIM5_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2164  #elif IRQ_GTM_TIM5_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2165  ISR(GTMTIM5SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  2166  #endif
; ..\mcal_src\Gtm_Irq.c	  2167  {
; ..\mcal_src\Gtm_Irq.c	  2168    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2169  #if (IRQ_GTM_TIM5_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2170    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2171  #endif
; ..\mcal_src\Gtm_Irq.c	  2172    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2173    Gtm_IsrTimModule(5, 5);
; ..\mcal_src\Gtm_Irq.c	  2174  }
; ..\mcal_src\Gtm_Irq.c	  2175  #endif
; ..\mcal_src\Gtm_Irq.c	  2176  
; ..\mcal_src\Gtm_Irq.c	  2177  
; ..\mcal_src\Gtm_Irq.c	  2178  #if((IRQ_GTM_TIM5_SR6_PRIO > 0) || (IRQ_GTM_TIM5_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2179  #if((IRQ_GTM_TIM5_SR6_PRIO > 0) && (IRQ_GTM_TIM5_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2180  IFX_INTERRUPT(GTMTIM5SR6_ISR, 0, IRQ_GTM_TIM5_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2181  #elif IRQ_GTM_TIM5_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2182  ISR(GTMTIM5SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  2183  #endif
; ..\mcal_src\Gtm_Irq.c	  2184  {
; ..\mcal_src\Gtm_Irq.c	  2185    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2186  #if (IRQ_GTM_TIM5_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2187    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2188  #endif
; ..\mcal_src\Gtm_Irq.c	  2189    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2190    Gtm_IsrTimModule(5, 6);
; ..\mcal_src\Gtm_Irq.c	  2191  }
; ..\mcal_src\Gtm_Irq.c	  2192  #endif
; ..\mcal_src\Gtm_Irq.c	  2193  
; ..\mcal_src\Gtm_Irq.c	  2194  #if((IRQ_GTM_TIM5_SR7_PRIO > 0) || (IRQ_GTM_TIM5_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2195  #if((IRQ_GTM_TIM5_SR7_PRIO > 0) && (IRQ_GTM_TIM5_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2196  IFX_INTERRUPT(GTMTIM5SR7_ISR, 0, IRQ_GTM_TIM5_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2197  #elif IRQ_GTM_TIM5_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2198  ISR(GTMTIM5SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  2199  #endif
; ..\mcal_src\Gtm_Irq.c	  2200  {
; ..\mcal_src\Gtm_Irq.c	  2201    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2202  #if (IRQ_GTM_TIM5_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2203    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2204  #endif
; ..\mcal_src\Gtm_Irq.c	  2205    /* Parameter is Channel Number */
; ..\mcal_src\Gtm_Irq.c	  2206    Gtm_IsrTimModule(5, 7);
; ..\mcal_src\Gtm_Irq.c	  2207  }
; ..\mcal_src\Gtm_Irq.c	  2208  #endif
; ..\mcal_src\Gtm_Irq.c	  2209  #endif /* #if(IRQ_GTM_TIM5_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  2210  
; ..\mcal_src\Gtm_Irq.c	  2211  
; ..\mcal_src\Gtm_Irq.c	  2212  #if(IRQ_GTM_AEI_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2213  
; ..\mcal_src\Gtm_Irq.c	  2214  #if((IRQ_GTM_AEI_PRIO > 0) || (IRQ_GTM_AEI_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2215  #if((IRQ_GTM_AEI_PRIO > 0) && (IRQ_GTM_AEI_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2216  IFX_INTERRUPT(GTMAEI_ISR, 0, IRQ_GTM_AEI_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2217  #elif IRQ_GTM_AEI_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2218  ISR(GTMAEI_ISR)
; ..\mcal_src\Gtm_Irq.c	  2219  #endif
; ..\mcal_src\Gtm_Irq.c	  2220  {
; ..\mcal_src\Gtm_Irq.c	  2221    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2222  #if (IRQ_GTM_AEI_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2223    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2224  #endif
; ..\mcal_src\Gtm_Irq.c	  2225  
; ..\mcal_src\Gtm_Irq.c	  2226  
; ..\mcal_src\Gtm_Irq.c	  2227  }
; ..\mcal_src\Gtm_Irq.c	  2228  #endif
; ..\mcal_src\Gtm_Irq.c	  2229  
; ..\mcal_src\Gtm_Irq.c	  2230  #endif /* #if(IRQ_GTM_AEI_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  2231  
; ..\mcal_src\Gtm_Irq.c	  2232  #if(IRQ_GTM_ARU_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2233  #if((IRQ_GTM_ARU_SR0_PRIO > 0) || (IRQ_GTM_ARU_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2234  #if((IRQ_GTM_ARU_SR0_PRIO > 0) && (IRQ_GTM_ARU_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2235  IFX_INTERRUPT(GTMARUSR0_ISR, 0, IRQ_GTM_ARU_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2236  #elif IRQ_GTM_ARU_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2237  ISR(GTMARUSR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  2238  #endif
; ..\mcal_src\Gtm_Irq.c	  2239  {
; ..\mcal_src\Gtm_Irq.c	  2240    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2241  #if (IRQ_GTM_ARU_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2242    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2243  #endif
; ..\mcal_src\Gtm_Irq.c	  2244  
; ..\mcal_src\Gtm_Irq.c	  2245  }
; ..\mcal_src\Gtm_Irq.c	  2246  #endif
; ..\mcal_src\Gtm_Irq.c	  2247  
; ..\mcal_src\Gtm_Irq.c	  2248  
; ..\mcal_src\Gtm_Irq.c	  2249  #if((IRQ_GTM_ARU_SR1_PRIO > 0) || (IRQ_GTM_ARU_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2250  #if((IRQ_GTM_ARU_SR1_PRIO > 0) && (IRQ_GTM_ARU_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2251  IFX_INTERRUPT(GTMARUSR1_ISR, 0, IRQ_GTM_ARU_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2252  #elif IRQ_GTM_ARU_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2253  ISR(GTMARUSR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  2254  #endif
; ..\mcal_src\Gtm_Irq.c	  2255  {
; ..\mcal_src\Gtm_Irq.c	  2256    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2257  #if (IRQ_GTM_ARU_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2258    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2259  #endif
; ..\mcal_src\Gtm_Irq.c	  2260  
; ..\mcal_src\Gtm_Irq.c	  2261  }
; ..\mcal_src\Gtm_Irq.c	  2262  #endif
; ..\mcal_src\Gtm_Irq.c	  2263  
; ..\mcal_src\Gtm_Irq.c	  2264  
; ..\mcal_src\Gtm_Irq.c	  2265  #if((IRQ_GTM_ARU_SR2_PRIO > 0) || (IRQ_GTM_ARU_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2266  #if((IRQ_GTM_ARU_SR2_PRIO > 0) && (IRQ_GTM_ARU_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2267  IFX_INTERRUPT(GTMARUSR2_ISR, 0, IRQ_GTM_ARU_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2268  #elif IRQ_GTM_ARU_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2269  ISR(GTMARUSR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  2270  #endif
; ..\mcal_src\Gtm_Irq.c	  2271  {
; ..\mcal_src\Gtm_Irq.c	  2272    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2273  #if (IRQ_GTM_ARU_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2274    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2275  #endif
; ..\mcal_src\Gtm_Irq.c	  2276  
; ..\mcal_src\Gtm_Irq.c	  2277  }
; ..\mcal_src\Gtm_Irq.c	  2278  #endif
; ..\mcal_src\Gtm_Irq.c	  2279  
; ..\mcal_src\Gtm_Irq.c	  2280  #endif /* #if(IRQ_GTM_ARU_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  2281  
; ..\mcal_src\Gtm_Irq.c	  2282  #if (IRQ_GTM_BRC_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2283  
; ..\mcal_src\Gtm_Irq.c	  2284  #if((IRQ_GTM_BRC_PRIO > 0) || (IRQ_GTM_BRC_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2285  #if((IRQ_GTM_BRC_PRIO > 0) && (IRQ_GTM_BRC_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2286  IFX_INTERRUPT(GTMBRC_ISR, 0, IRQ_GTM_BRC_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2287  #elif IRQ_GTM_BRC_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2288  ISR(GTMBRC_ISR)
; ..\mcal_src\Gtm_Irq.c	  2289  #endif
; ..\mcal_src\Gtm_Irq.c	  2290  {
; ..\mcal_src\Gtm_Irq.c	  2291    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2292  #if (IRQ_GTM_BRC_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2293    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2294  #endif
; ..\mcal_src\Gtm_Irq.c	  2295  
; ..\mcal_src\Gtm_Irq.c	  2296  }
; ..\mcal_src\Gtm_Irq.c	  2297  #endif
; ..\mcal_src\Gtm_Irq.c	  2298  #endif /* #if (IRQ_GTM_BRC_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  2299  
; ..\mcal_src\Gtm_Irq.c	  2300  
; ..\mcal_src\Gtm_Irq.c	  2301  #if(IRQ_GTM_CMP_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2302  #if((IRQ_GTM_CMP_PRIO > 0) || (IRQ_GTM_CMP_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2303  #if((IRQ_GTM_CMP_PRIO > 0) && (IRQ_GTM_CMP_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2304  IFX_INTERRUPT(GTMCMP_ISR, 0, IRQ_GTM_CMP_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2305  #elif IRQ_GTM_CMP_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2306  ISR(GTMCMP_ISR)
; ..\mcal_src\Gtm_Irq.c	  2307  #endif
; ..\mcal_src\Gtm_Irq.c	  2308  {
; ..\mcal_src\Gtm_Irq.c	  2309    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2310  #if (IRQ_GTM_CMP_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2311    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2312  #endif
; ..\mcal_src\Gtm_Irq.c	  2313  
; ..\mcal_src\Gtm_Irq.c	  2314  }
; ..\mcal_src\Gtm_Irq.c	  2315  #endif
; ..\mcal_src\Gtm_Irq.c	  2316  
; ..\mcal_src\Gtm_Irq.c	  2317  #endif /* #if(IRQ_GTM_CMP_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  2318  
; ..\mcal_src\Gtm_Irq.c	  2319  #if(IRQ_GTM_SPE_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2320  
; ..\mcal_src\Gtm_Irq.c	  2321  #if((IRQ_GTM_SPE0_PRIO > 0) || (IRQ_GTM_SPE0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2322  #if((IRQ_GTM_SPE0_PRIO > 0) && (IRQ_GTM_SPE0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2323  IFX_INTERRUPT(GTMSPE0_ISR, 0, IRQ_GTM_SPE0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2324  #elif IRQ_GTM_SPE0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2325  ISR(GTMSPE0_ISR)
; ..\mcal_src\Gtm_Irq.c	  2326  #endif
; ..\mcal_src\Gtm_Irq.c	  2327  {
; ..\mcal_src\Gtm_Irq.c	  2328    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2329  #if (IRQ_GTM_SPE0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2330    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2331  #endif
; ..\mcal_src\Gtm_Irq.c	  2332  
; ..\mcal_src\Gtm_Irq.c	  2333  }
; ..\mcal_src\Gtm_Irq.c	  2334  #endif
; ..\mcal_src\Gtm_Irq.c	  2335  
; ..\mcal_src\Gtm_Irq.c	  2336  #if((IRQ_GTM_SPE1_PRIO > 0) || (IRQ_GTM_SPE1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2337  #if((IRQ_GTM_SPE1_PRIO > 0) && (IRQ_GTM_SPE1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2338  IFX_INTERRUPT(GTMSPE1_ISR, 0, IRQ_GTM_SPE1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2339  #elif IRQ_GTM_SPE1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2340  ISR(GTMSPE1_ISR)
; ..\mcal_src\Gtm_Irq.c	  2341  #endif
; ..\mcal_src\Gtm_Irq.c	  2342  {
; ..\mcal_src\Gtm_Irq.c	  2343    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2344  #if (IRQ_GTM_SPE1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2345    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2346  #endif
; ..\mcal_src\Gtm_Irq.c	  2347  
; ..\mcal_src\Gtm_Irq.c	  2348  }
; ..\mcal_src\Gtm_Irq.c	  2349  #endif
; ..\mcal_src\Gtm_Irq.c	  2350  
; ..\mcal_src\Gtm_Irq.c	  2351  #endif /* #if(IRQ_GTM_SPE_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  2352  
; ..\mcal_src\Gtm_Irq.c	  2353  #if(IRQ_GTM_PSM0_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2354  
; ..\mcal_src\Gtm_Irq.c	  2355  #if((IRQ_GTM_PSM0_SR0_PRIO > 0) || (IRQ_GTM_PSM0_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2356  #if((IRQ_GTM_PSM0_SR0_PRIO > 0) && (IRQ_GTM_PSM0_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2357  IFX_INTERRUPT(GTMPSM0SR0_ISR, 0, IRQ_GTM_PSM0_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2358  #elif IRQ_GTM_PSM0_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2359  ISR(GTMPSM0SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  2360  #endif
; ..\mcal_src\Gtm_Irq.c	  2361  {
; ..\mcal_src\Gtm_Irq.c	  2362    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2363  #if (IRQ_GTM_PSM0_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2364    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2365  #endif
; ..\mcal_src\Gtm_Irq.c	  2366  
; ..\mcal_src\Gtm_Irq.c	  2367  }
; ..\mcal_src\Gtm_Irq.c	  2368  #endif
; ..\mcal_src\Gtm_Irq.c	  2369  
; ..\mcal_src\Gtm_Irq.c	  2370  #if((IRQ_GTM_PSM0_SR1_PRIO > 0) || (IRQ_GTM_PSM0_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2371  #if((IRQ_GTM_PSM0_SR1_PRIO > 0) && (IRQ_GTM_PSM0_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2372  IFX_INTERRUPT(GTMPSM0SR1_ISR, 0, IRQ_GTM_PSM0_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2373  #elif IRQ_GTM_PSM0_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2374  ISR(GTMPSM0SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  2375  #endif
; ..\mcal_src\Gtm_Irq.c	  2376  {
; ..\mcal_src\Gtm_Irq.c	  2377    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2378  #if (IRQ_GTM_PSM0_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2379    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2380  #endif
; ..\mcal_src\Gtm_Irq.c	  2381  
; ..\mcal_src\Gtm_Irq.c	  2382  }
; ..\mcal_src\Gtm_Irq.c	  2383  #endif
; ..\mcal_src\Gtm_Irq.c	  2384  
; ..\mcal_src\Gtm_Irq.c	  2385  
; ..\mcal_src\Gtm_Irq.c	  2386  #if((IRQ_GTM_PSM0_SR2_PRIO > 0) || (IRQ_GTM_PSM0_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2387  #if((IRQ_GTM_PSM0_SR2_PRIO > 0) && (IRQ_GTM_PSM0_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2388  IFX_INTERRUPT(GTMPSM0SR2_ISR, 0, IRQ_GTM_PSM0_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2389  #elif IRQ_GTM_PSM0_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2390  ISR(GTMPSM0SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  2391  #endif
; ..\mcal_src\Gtm_Irq.c	  2392  {
; ..\mcal_src\Gtm_Irq.c	  2393    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2394  #if (IRQ_GTM_PSM0_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2395    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2396  #endif
; ..\mcal_src\Gtm_Irq.c	  2397  
; ..\mcal_src\Gtm_Irq.c	  2398  }
; ..\mcal_src\Gtm_Irq.c	  2399  #endif
; ..\mcal_src\Gtm_Irq.c	  2400  
; ..\mcal_src\Gtm_Irq.c	  2401  #if((IRQ_GTM_PSM0_SR3_PRIO > 0) || (IRQ_GTM_PSM0_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2402  #if((IRQ_GTM_PSM0_SR3_PRIO > 0) && (IRQ_GTM_PSM0_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2403  IFX_INTERRUPT(GTMPSM0SR3_ISR, 0, IRQ_GTM_PSM0_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2404  #elif IRQ_GTM_PSM0_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2405  ISR(GTMPSM0SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  2406  #endif
; ..\mcal_src\Gtm_Irq.c	  2407  {
; ..\mcal_src\Gtm_Irq.c	  2408    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2409  #if (IRQ_GTM_PSM0_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2410    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2411  #endif
; ..\mcal_src\Gtm_Irq.c	  2412  
; ..\mcal_src\Gtm_Irq.c	  2413  }
; ..\mcal_src\Gtm_Irq.c	  2414  #endif
; ..\mcal_src\Gtm_Irq.c	  2415  
; ..\mcal_src\Gtm_Irq.c	  2416  
; ..\mcal_src\Gtm_Irq.c	  2417  #if((IRQ_GTM_PSM0_SR4_PRIO > 0) || (IRQ_GTM_PSM0_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2418  #if((IRQ_GTM_PSM0_SR4_PRIO > 0) && (IRQ_GTM_PSM0_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2419  IFX_INTERRUPT(GTMPSM0SR4_ISR, 0, IRQ_GTM_PSM0_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2420  #elif IRQ_GTM_PSM0_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2421  ISR(GTMPSM0SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  2422  #endif
; ..\mcal_src\Gtm_Irq.c	  2423  {
; ..\mcal_src\Gtm_Irq.c	  2424    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2425  #if (IRQ_GTM_PSM0_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2426    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2427  #endif
; ..\mcal_src\Gtm_Irq.c	  2428  
; ..\mcal_src\Gtm_Irq.c	  2429  }
; ..\mcal_src\Gtm_Irq.c	  2430  #endif
; ..\mcal_src\Gtm_Irq.c	  2431  
; ..\mcal_src\Gtm_Irq.c	  2432  #if((IRQ_GTM_PSM0_SR5_PRIO > 0) || (IRQ_GTM_PSM0_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2433  #if((IRQ_GTM_PSM0_SR5_PRIO > 0) && (IRQ_GTM_PSM0_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2434  IFX_INTERRUPT(GTMPSM0SR5_ISR, 0, IRQ_GTM_PSM0_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2435  #elif IRQ_GTM_PSM0_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2436  ISR(GTMPSM0SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  2437  #endif
; ..\mcal_src\Gtm_Irq.c	  2438  {
; ..\mcal_src\Gtm_Irq.c	  2439    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2440  #if (IRQ_GTM_PSM0_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2441    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2442  #endif
; ..\mcal_src\Gtm_Irq.c	  2443  
; ..\mcal_src\Gtm_Irq.c	  2444  }
; ..\mcal_src\Gtm_Irq.c	  2445  #endif
; ..\mcal_src\Gtm_Irq.c	  2446  
; ..\mcal_src\Gtm_Irq.c	  2447  
; ..\mcal_src\Gtm_Irq.c	  2448  #if((IRQ_GTM_PSM0_SR6_PRIO > 0) || (IRQ_GTM_PSM0_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2449  #if((IRQ_GTM_PSM0_SR6_PRIO > 0) && (IRQ_GTM_PSM0_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2450  IFX_INTERRUPT(GTMPSM0SR6_ISR, 0, IRQ_GTM_PSM0_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2451  #elif IRQ_GTM_PSM0_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2452  ISR(GTMPSM0SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  2453  #endif
; ..\mcal_src\Gtm_Irq.c	  2454  {
; ..\mcal_src\Gtm_Irq.c	  2455    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2456  #if (IRQ_GTM_PSM0_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2457    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2458  #endif
; ..\mcal_src\Gtm_Irq.c	  2459  
; ..\mcal_src\Gtm_Irq.c	  2460  }
; ..\mcal_src\Gtm_Irq.c	  2461  #endif
; ..\mcal_src\Gtm_Irq.c	  2462  
; ..\mcal_src\Gtm_Irq.c	  2463  #if((IRQ_GTM_PSM0_SR7_PRIO > 0) || (IRQ_GTM_PSM0_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2464  #if((IRQ_GTM_PSM0_SR7_PRIO > 0) && (IRQ_GTM_PSM0_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2465  IFX_INTERRUPT(GTMPSM0SR7_ISR, 0, IRQ_GTM_PSM0_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2466  #elif IRQ_GTM_PSM0_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2467  ISR(GTMPSM0SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  2468  #endif
; ..\mcal_src\Gtm_Irq.c	  2469  {
; ..\mcal_src\Gtm_Irq.c	  2470    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2471  #if (IRQ_GTM_PSM0_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2472    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2473  #endif
; ..\mcal_src\Gtm_Irq.c	  2474  
; ..\mcal_src\Gtm_Irq.c	  2475  }
; ..\mcal_src\Gtm_Irq.c	  2476  #endif
; ..\mcal_src\Gtm_Irq.c	  2477  
; ..\mcal_src\Gtm_Irq.c	  2478  
; ..\mcal_src\Gtm_Irq.c	  2479  #endif /* #if(IRQ_GTM_PSM0_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  2480  
; ..\mcal_src\Gtm_Irq.c	  2481  #if(IRQ_GTM_DPLL_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2482  
; ..\mcal_src\Gtm_Irq.c	  2483  #if((IRQ_GTM_DPLL_SR0_PRIO > 0) || (IRQ_GTM_DPLL_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2484  #if((IRQ_GTM_DPLL_SR0_PRIO > 0) && (IRQ_GTM_DPLL_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2485  IFX_INTERRUPT(GTMDPLLSR0_ISR, 0, IRQ_GTM_DPLL_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2486  #elif IRQ_GTM_DPLL_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2487  ISR(GTMDPLLSR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  2488  #endif
; ..\mcal_src\Gtm_Irq.c	  2489  {
; ..\mcal_src\Gtm_Irq.c	  2490    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2491  #if (IRQ_GTM_DPLL_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2492    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2493  #endif
; ..\mcal_src\Gtm_Irq.c	  2494  
; ..\mcal_src\Gtm_Irq.c	  2495  }
; ..\mcal_src\Gtm_Irq.c	  2496  #endif
; ..\mcal_src\Gtm_Irq.c	  2497  
; ..\mcal_src\Gtm_Irq.c	  2498  
; ..\mcal_src\Gtm_Irq.c	  2499  #if((IRQ_GTM_DPLL_SR1_PRIO > 0) || (IRQ_GTM_DPLL_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2500  #if((IRQ_GTM_DPLL_SR1_PRIO > 0) && (IRQ_GTM_DPLL_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2501  IFX_INTERRUPT(GTMDPLLSR1_ISR, 0, IRQ_GTM_DPLL_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2502  #elif IRQ_GTM_DPLL_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2503  ISR(GTMDPLLSR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  2504  #endif
; ..\mcal_src\Gtm_Irq.c	  2505  {
; ..\mcal_src\Gtm_Irq.c	  2506    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2507  #if (IRQ_GTM_DPLL_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2508    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2509  #endif
; ..\mcal_src\Gtm_Irq.c	  2510  
; ..\mcal_src\Gtm_Irq.c	  2511  }
; ..\mcal_src\Gtm_Irq.c	  2512  #endif
; ..\mcal_src\Gtm_Irq.c	  2513  
; ..\mcal_src\Gtm_Irq.c	  2514  #if((IRQ_GTM_DPLL_SR2_PRIO > 0) || (IRQ_GTM_DPLL_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2515  #if((IRQ_GTM_DPLL_SR2_PRIO > 0) && (IRQ_GTM_DPLL_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2516  IFX_INTERRUPT(GTMDPLLSR2_ISR, 0, IRQ_GTM_DPLL_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2517  #elif IRQ_GTM_DPLL_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2518  ISR(GTMDPLLSR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  2519  #endif
; ..\mcal_src\Gtm_Irq.c	  2520  {
; ..\mcal_src\Gtm_Irq.c	  2521    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2522  #if (IRQ_GTM_DPLL_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2523    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2524  #endif
; ..\mcal_src\Gtm_Irq.c	  2525  
; ..\mcal_src\Gtm_Irq.c	  2526  }
; ..\mcal_src\Gtm_Irq.c	  2527  #endif
; ..\mcal_src\Gtm_Irq.c	  2528  
; ..\mcal_src\Gtm_Irq.c	  2529  
; ..\mcal_src\Gtm_Irq.c	  2530  
; ..\mcal_src\Gtm_Irq.c	  2531  #if((IRQ_GTM_DPLL_SR3_PRIO > 0) || (IRQ_GTM_DPLL_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2532  #if((IRQ_GTM_DPLL_SR3_PRIO > 0) && (IRQ_GTM_DPLL_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2533  IFX_INTERRUPT(GTMDPLLSR3_ISR, 0, IRQ_GTM_DPLL_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2534  #elif IRQ_GTM_DPLL_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2535  ISR(GTMDPLLSR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  2536  #endif
; ..\mcal_src\Gtm_Irq.c	  2537  {
; ..\mcal_src\Gtm_Irq.c	  2538    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2539  #if (IRQ_GTM_DPLL_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2540    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2541  #endif
; ..\mcal_src\Gtm_Irq.c	  2542  
; ..\mcal_src\Gtm_Irq.c	  2543  }
; ..\mcal_src\Gtm_Irq.c	  2544  #endif
; ..\mcal_src\Gtm_Irq.c	  2545  
; ..\mcal_src\Gtm_Irq.c	  2546  #if((IRQ_GTM_DPLL_SR4_PRIO > 0) || (IRQ_GTM_DPLL_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2547  #if((IRQ_GTM_DPLL_SR4_PRIO > 0) && (IRQ_GTM_DPLL_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2548  IFX_INTERRUPT(GTMDPLLSR4_ISR, 0, IRQ_GTM_DPLL_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2549  #elif IRQ_GTM_DPLL_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2550  ISR(GTMDPLLSR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  2551  #endif
; ..\mcal_src\Gtm_Irq.c	  2552  {
; ..\mcal_src\Gtm_Irq.c	  2553    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2554  #if (IRQ_GTM_DPLL_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2555    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2556  #endif
; ..\mcal_src\Gtm_Irq.c	  2557  
; ..\mcal_src\Gtm_Irq.c	  2558  }
; ..\mcal_src\Gtm_Irq.c	  2559  #endif
; ..\mcal_src\Gtm_Irq.c	  2560  
; ..\mcal_src\Gtm_Irq.c	  2561  
; ..\mcal_src\Gtm_Irq.c	  2562  #if((IRQ_GTM_DPLL_SR5_PRIO > 0) || (IRQ_GTM_DPLL_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2563  #if((IRQ_GTM_DPLL_SR5_PRIO > 0) && (IRQ_GTM_DPLL_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2564  IFX_INTERRUPT(GTMDPLLSR5_ISR, 0, IRQ_GTM_DPLL_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2565  #elif IRQ_GTM_DPLL_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2566  ISR(GTMDPLLSR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  2567  #endif
; ..\mcal_src\Gtm_Irq.c	  2568  {
; ..\mcal_src\Gtm_Irq.c	  2569    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2570  #if (IRQ_GTM_DPLL_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2571    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2572  #endif
; ..\mcal_src\Gtm_Irq.c	  2573  
; ..\mcal_src\Gtm_Irq.c	  2574  }
; ..\mcal_src\Gtm_Irq.c	  2575  #endif
; ..\mcal_src\Gtm_Irq.c	  2576  
; ..\mcal_src\Gtm_Irq.c	  2577  #if((IRQ_GTM_DPLL_SR6_PRIO > 0) || (IRQ_GTM_DPLL_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2578  #if((IRQ_GTM_DPLL_SR6_PRIO > 0) && (IRQ_GTM_DPLL_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2579  IFX_INTERRUPT(GTMDPLLSR6_ISR, 0, IRQ_GTM_DPLL_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2580  #elif IRQ_GTM_DPLL_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2581  ISR(GTMDPLLSR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  2582  #endif
; ..\mcal_src\Gtm_Irq.c	  2583  {
; ..\mcal_src\Gtm_Irq.c	  2584    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2585  #if (IRQ_GTM_DPLL_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2586    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2587  #endif
; ..\mcal_src\Gtm_Irq.c	  2588  
; ..\mcal_src\Gtm_Irq.c	  2589  }
; ..\mcal_src\Gtm_Irq.c	  2590  #endif
; ..\mcal_src\Gtm_Irq.c	  2591  
; ..\mcal_src\Gtm_Irq.c	  2592  
; ..\mcal_src\Gtm_Irq.c	  2593  
; ..\mcal_src\Gtm_Irq.c	  2594  #if((IRQ_GTM_DPLL_SR7_PRIO > 0) || (IRQ_GTM_DPLL_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2595  #if((IRQ_GTM_DPLL_SR7_PRIO > 0) && (IRQ_GTM_DPLL_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2596  IFX_INTERRUPT(GTMDPLLSR7_ISR, 0, IRQ_GTM_DPLL_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2597  #elif IRQ_GTM_DPLL_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2598  ISR(GTMDPLLSR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  2599  #endif
; ..\mcal_src\Gtm_Irq.c	  2600  {
; ..\mcal_src\Gtm_Irq.c	  2601    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2602  #if (IRQ_GTM_DPLL_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2603    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2604  #endif
; ..\mcal_src\Gtm_Irq.c	  2605  
; ..\mcal_src\Gtm_Irq.c	  2606  }
; ..\mcal_src\Gtm_Irq.c	  2607  #endif
; ..\mcal_src\Gtm_Irq.c	  2608  
; ..\mcal_src\Gtm_Irq.c	  2609  
; ..\mcal_src\Gtm_Irq.c	  2610  #if((IRQ_GTM_DPLL_SR8_PRIO > 0) || (IRQ_GTM_DPLL_SR8_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2611  #if((IRQ_GTM_DPLL_SR8_PRIO > 0) && (IRQ_GTM_DPLL_SR8_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2612  IFX_INTERRUPT(GTMDPLLSR8_ISR, 0, IRQ_GTM_DPLL_SR8_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2613  #elif IRQ_GTM_DPLL_SR8_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2614  ISR(GTMDPLLSR8_ISR)
; ..\mcal_src\Gtm_Irq.c	  2615  #endif
; ..\mcal_src\Gtm_Irq.c	  2616  {
; ..\mcal_src\Gtm_Irq.c	  2617    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2618  #if (IRQ_GTM_DPLL_SR8_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2619    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2620  #endif
; ..\mcal_src\Gtm_Irq.c	  2621  
; ..\mcal_src\Gtm_Irq.c	  2622  }
; ..\mcal_src\Gtm_Irq.c	  2623  #endif
; ..\mcal_src\Gtm_Irq.c	  2624  
; ..\mcal_src\Gtm_Irq.c	  2625  
; ..\mcal_src\Gtm_Irq.c	  2626  
; ..\mcal_src\Gtm_Irq.c	  2627  #if((IRQ_GTM_DPLL_SR9_PRIO > 0) || (IRQ_GTM_DPLL_SR9_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2628  #if((IRQ_GTM_DPLL_SR9_PRIO > 0) && (IRQ_GTM_DPLL_SR9_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2629  IFX_INTERRUPT(GTMDPLLSR9_ISR, 0, IRQ_GTM_DPLL_SR9_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2630  #elif IRQ_GTM_DPLL_SR9_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2631  ISR(GTMDPLLSR9_ISR)
; ..\mcal_src\Gtm_Irq.c	  2632  #endif
; ..\mcal_src\Gtm_Irq.c	  2633  {
; ..\mcal_src\Gtm_Irq.c	  2634    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2635  #if (IRQ_GTM_DPLL_SR9_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2636    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2637  #endif
; ..\mcal_src\Gtm_Irq.c	  2638  
; ..\mcal_src\Gtm_Irq.c	  2639  }
; ..\mcal_src\Gtm_Irq.c	  2640  #endif
; ..\mcal_src\Gtm_Irq.c	  2641  
; ..\mcal_src\Gtm_Irq.c	  2642  
; ..\mcal_src\Gtm_Irq.c	  2643  #if((IRQ_GTM_DPLL_SR10_PRIO > 0) || (IRQ_GTM_DPLL_SR10_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2644  #if((IRQ_GTM_DPLL_SR10_PRIO > 0) && (IRQ_GTM_DPLL_SR10_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2645  IFX_INTERRUPT(GTMDPLLSR10_ISR, 0, IRQ_GTM_DPLL_SR10_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2646  #elif IRQ_GTM_DPLL_SR10_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2647  ISR(GTMDPLLSR10_ISR)
; ..\mcal_src\Gtm_Irq.c	  2648  #endif
; ..\mcal_src\Gtm_Irq.c	  2649  {
; ..\mcal_src\Gtm_Irq.c	  2650    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2651  #if (IRQ_GTM_DPLL_SR10_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2652    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2653  #endif
; ..\mcal_src\Gtm_Irq.c	  2654  
; ..\mcal_src\Gtm_Irq.c	  2655  }
; ..\mcal_src\Gtm_Irq.c	  2656  #endif
; ..\mcal_src\Gtm_Irq.c	  2657  
; ..\mcal_src\Gtm_Irq.c	  2658  
; ..\mcal_src\Gtm_Irq.c	  2659  #if((IRQ_GTM_DPLL_SR11_PRIO > 0) || (IRQ_GTM_DPLL_SR11_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2660  #if((IRQ_GTM_DPLL_SR11_PRIO > 0) && (IRQ_GTM_DPLL_SR11_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2661  IFX_INTERRUPT(GTMDPLLSR11_ISR, 0, IRQ_GTM_DPLL_SR11_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2662  #elif IRQ_GTM_DPLL_SR11_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2663  ISR(GTMDPLLSR11_ISR)
; ..\mcal_src\Gtm_Irq.c	  2664  #endif
; ..\mcal_src\Gtm_Irq.c	  2665  {
; ..\mcal_src\Gtm_Irq.c	  2666    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2667  #if (IRQ_GTM_DPLL_SR11_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2668    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2669  #endif
; ..\mcal_src\Gtm_Irq.c	  2670  
; ..\mcal_src\Gtm_Irq.c	  2671  }
; ..\mcal_src\Gtm_Irq.c	  2672  #endif
; ..\mcal_src\Gtm_Irq.c	  2673  
; ..\mcal_src\Gtm_Irq.c	  2674  #if((IRQ_GTM_DPLL_SR12_PRIO > 0) || (IRQ_GTM_DPLL_SR12_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2675  #if((IRQ_GTM_DPLL_SR12_PRIO > 0) && (IRQ_GTM_DPLL_SR12_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2676  IFX_INTERRUPT(GTMDPLLSR12_ISR, 0, IRQ_GTM_DPLL_SR12_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2677  #elif IRQ_GTM_DPLL_SR12_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2678  ISR(GTMDPLLSR12_ISR)
; ..\mcal_src\Gtm_Irq.c	  2679  #endif
; ..\mcal_src\Gtm_Irq.c	  2680  {
; ..\mcal_src\Gtm_Irq.c	  2681    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2682  #if (IRQ_GTM_DPLL_SR12_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2683    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2684  #endif
; ..\mcal_src\Gtm_Irq.c	  2685  
; ..\mcal_src\Gtm_Irq.c	  2686  }
; ..\mcal_src\Gtm_Irq.c	  2687  #endif
; ..\mcal_src\Gtm_Irq.c	  2688  
; ..\mcal_src\Gtm_Irq.c	  2689  #if((IRQ_GTM_DPLL_SR13_PRIO > 0) || (IRQ_GTM_DPLL_SR13_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2690  #if((IRQ_GTM_DPLL_SR13_PRIO > 0) && (IRQ_GTM_DPLL_SR13_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2691  IFX_INTERRUPT(GTMDPLLSR13_ISR, 0, IRQ_GTM_DPLL_SR13_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2692  #elif IRQ_GTM_DPLL_SR13_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2693  ISR(GTMDPLLSR13_ISR)
; ..\mcal_src\Gtm_Irq.c	  2694  #endif
; ..\mcal_src\Gtm_Irq.c	  2695  {
; ..\mcal_src\Gtm_Irq.c	  2696    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2697  #if (IRQ_GTM_DPLL_SR13_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2698    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2699  #endif
; ..\mcal_src\Gtm_Irq.c	  2700  
; ..\mcal_src\Gtm_Irq.c	  2701  }
; ..\mcal_src\Gtm_Irq.c	  2702  #endif
; ..\mcal_src\Gtm_Irq.c	  2703  
; ..\mcal_src\Gtm_Irq.c	  2704  #if((IRQ_GTM_DPLL_SR14_PRIO > 0) || (IRQ_GTM_DPLL_SR14_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2705  #if((IRQ_GTM_DPLL_SR14_PRIO > 0) && (IRQ_GTM_DPLL_SR14_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2706  IFX_INTERRUPT(GTMDPLLSR14_ISR, 0, IRQ_GTM_DPLL_SR14_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2707  #elif IRQ_GTM_DPLL_SR14_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2708  ISR(GTMDPLLSR14_ISR)
; ..\mcal_src\Gtm_Irq.c	  2709  #endif
; ..\mcal_src\Gtm_Irq.c	  2710  {
; ..\mcal_src\Gtm_Irq.c	  2711    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2712  #if (IRQ_GTM_DPLL_SR14_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2713    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2714  #endif
; ..\mcal_src\Gtm_Irq.c	  2715  
; ..\mcal_src\Gtm_Irq.c	  2716  }
; ..\mcal_src\Gtm_Irq.c	  2717  #endif
; ..\mcal_src\Gtm_Irq.c	  2718  
; ..\mcal_src\Gtm_Irq.c	  2719  #if((IRQ_GTM_DPLL_SR15_PRIO > 0) || (IRQ_GTM_DPLL_SR15_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2720  #if((IRQ_GTM_DPLL_SR15_PRIO > 0) && (IRQ_GTM_DPLL_SR15_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2721  IFX_INTERRUPT(GTMDPLLSR15_ISR, 0, IRQ_GTM_DPLL_SR15_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2722  #elif IRQ_GTM_DPLL_SR15_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2723  ISR(GTMDPLLSR15_ISR)
; ..\mcal_src\Gtm_Irq.c	  2724  #endif
; ..\mcal_src\Gtm_Irq.c	  2725  {
; ..\mcal_src\Gtm_Irq.c	  2726    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2727  #if (IRQ_GTM_DPLL_SR15_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2728    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2729  #endif
; ..\mcal_src\Gtm_Irq.c	  2730  
; ..\mcal_src\Gtm_Irq.c	  2731  }
; ..\mcal_src\Gtm_Irq.c	  2732  #endif
; ..\mcal_src\Gtm_Irq.c	  2733  
; ..\mcal_src\Gtm_Irq.c	  2734  
; ..\mcal_src\Gtm_Irq.c	  2735  #if((IRQ_GTM_DPLL_SR16_PRIO > 0) || (IRQ_GTM_DPLL_SR16_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2736  #if((IRQ_GTM_DPLL_SR16_PRIO > 0) && (IRQ_GTM_DPLL_SR16_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2737  IFX_INTERRUPT(GTMDPLLSR16_ISR, 0, IRQ_GTM_DPLL_SR16_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2738  #elif IRQ_GTM_DPLL_SR16_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2739  ISR(GTMDPLLSR16_ISR)
; ..\mcal_src\Gtm_Irq.c	  2740  #endif
; ..\mcal_src\Gtm_Irq.c	  2741  {
; ..\mcal_src\Gtm_Irq.c	  2742    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2743  #if (IRQ_GTM_DPLL_SR16_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2744    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2745  #endif
; ..\mcal_src\Gtm_Irq.c	  2746  
; ..\mcal_src\Gtm_Irq.c	  2747  }
; ..\mcal_src\Gtm_Irq.c	  2748  #endif
; ..\mcal_src\Gtm_Irq.c	  2749  
; ..\mcal_src\Gtm_Irq.c	  2750  
; ..\mcal_src\Gtm_Irq.c	  2751  #if((IRQ_GTM_DPLL_SR17_PRIO > 0) || (IRQ_GTM_DPLL_SR17_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2752  #if((IRQ_GTM_DPLL_SR17_PRIO > 0) && (IRQ_GTM_DPLL_SR17_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2753  IFX_INTERRUPT(GTMDPLLSR17_ISR, 0, IRQ_GTM_DPLL_SR17_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2754  #elif IRQ_GTM_DPLL_SR17_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2755  ISR(GTMDPLLSR17_ISR)
; ..\mcal_src\Gtm_Irq.c	  2756  #endif
; ..\mcal_src\Gtm_Irq.c	  2757  {
; ..\mcal_src\Gtm_Irq.c	  2758    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2759  #if (IRQ_GTM_DPLL_SR17_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2760    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2761  #endif
; ..\mcal_src\Gtm_Irq.c	  2762  
; ..\mcal_src\Gtm_Irq.c	  2763  }
; ..\mcal_src\Gtm_Irq.c	  2764  #endif
; ..\mcal_src\Gtm_Irq.c	  2765  
; ..\mcal_src\Gtm_Irq.c	  2766  #if((IRQ_GTM_DPLL_SR18_PRIO > 0) || (IRQ_GTM_DPLL_SR18_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2767  #if((IRQ_GTM_DPLL_SR18_PRIO > 0) && (IRQ_GTM_DPLL_SR18_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2768  IFX_INTERRUPT(GTMDPLLSR18_ISR, 0, IRQ_GTM_DPLL_SR18_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2769  #elif IRQ_GTM_DPLL_SR18_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2770  ISR(GTMDPLLSR18_ISR)
; ..\mcal_src\Gtm_Irq.c	  2771  #endif
; ..\mcal_src\Gtm_Irq.c	  2772  {
; ..\mcal_src\Gtm_Irq.c	  2773    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2774  #if (IRQ_GTM_DPLL_SR18_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2775    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2776  #endif
; ..\mcal_src\Gtm_Irq.c	  2777  
; ..\mcal_src\Gtm_Irq.c	  2778  }
; ..\mcal_src\Gtm_Irq.c	  2779  #endif
; ..\mcal_src\Gtm_Irq.c	  2780  
; ..\mcal_src\Gtm_Irq.c	  2781  
; ..\mcal_src\Gtm_Irq.c	  2782  #if((IRQ_GTM_DPLL_SR19_PRIO > 0) || (IRQ_GTM_DPLL_SR19_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2783  #if((IRQ_GTM_DPLL_SR19_PRIO > 0) && (IRQ_GTM_DPLL_SR19_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2784  IFX_INTERRUPT(GTMDPLLSR19_ISR, 0, IRQ_GTM_DPLL_SR19_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2785  #elif IRQ_GTM_DPLL_SR19_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2786  ISR(GTMDPLLSR19_ISR)
; ..\mcal_src\Gtm_Irq.c	  2787  #endif
; ..\mcal_src\Gtm_Irq.c	  2788  {
; ..\mcal_src\Gtm_Irq.c	  2789    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2790  #if (IRQ_GTM_DPLL_SR19_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2791    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2792  #endif
; ..\mcal_src\Gtm_Irq.c	  2793  
; ..\mcal_src\Gtm_Irq.c	  2794  }
; ..\mcal_src\Gtm_Irq.c	  2795  #endif
; ..\mcal_src\Gtm_Irq.c	  2796  
; ..\mcal_src\Gtm_Irq.c	  2797  
; ..\mcal_src\Gtm_Irq.c	  2798  #if((IRQ_GTM_DPLL_SR20_PRIO > 0) || (IRQ_GTM_DPLL_SR20_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2799  #if((IRQ_GTM_DPLL_SR20_PRIO > 0) && (IRQ_GTM_DPLL_SR20_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2800  IFX_INTERRUPT(GTMDPLLSR20_ISR, 0, IRQ_GTM_DPLL_SR20_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2801  #elif IRQ_GTM_DPLL_SR20_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2802  ISR(GTMDPLLSR20_ISR)
; ..\mcal_src\Gtm_Irq.c	  2803  #endif
; ..\mcal_src\Gtm_Irq.c	  2804  {
; ..\mcal_src\Gtm_Irq.c	  2805    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2806  #if (IRQ_GTM_DPLL_SR20_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2807    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2808  #endif
; ..\mcal_src\Gtm_Irq.c	  2809  
; ..\mcal_src\Gtm_Irq.c	  2810  }
; ..\mcal_src\Gtm_Irq.c	  2811  #endif
; ..\mcal_src\Gtm_Irq.c	  2812  
; ..\mcal_src\Gtm_Irq.c	  2813  
; ..\mcal_src\Gtm_Irq.c	  2814  #if((IRQ_GTM_DPLL_SR21_PRIO > 0) || (IRQ_GTM_DPLL_SR21_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2815  #if((IRQ_GTM_DPLL_SR21_PRIO > 0) && (IRQ_GTM_DPLL_SR21_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2816  IFX_INTERRUPT(GTMDPLLSR21_ISR, 0, IRQ_GTM_DPLL_SR21_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2817  #elif IRQ_GTM_DPLL_SR21_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2818  ISR(GTMDPLLSR21_ISR)
; ..\mcal_src\Gtm_Irq.c	  2819  #endif
; ..\mcal_src\Gtm_Irq.c	  2820  {
; ..\mcal_src\Gtm_Irq.c	  2821    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2822  #if (IRQ_GTM_DPLL_SR21_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2823    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2824  #endif
; ..\mcal_src\Gtm_Irq.c	  2825  
; ..\mcal_src\Gtm_Irq.c	  2826  }
; ..\mcal_src\Gtm_Irq.c	  2827  #endif
; ..\mcal_src\Gtm_Irq.c	  2828  
; ..\mcal_src\Gtm_Irq.c	  2829  #if((IRQ_GTM_DPLL_SR22_PRIO > 0) || (IRQ_GTM_DPLL_SR22_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2830  #if((IRQ_GTM_DPLL_SR22_PRIO > 0) && (IRQ_GTM_DPLL_SR22_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2831  IFX_INTERRUPT(GTMDPLLSR22_ISR, 0, IRQ_GTM_DPLL_SR22_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2832  #elif IRQ_GTM_DPLL_SR22_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2833  ISR(GTMDPLLSR22_ISR)
; ..\mcal_src\Gtm_Irq.c	  2834  #endif
; ..\mcal_src\Gtm_Irq.c	  2835  {
; ..\mcal_src\Gtm_Irq.c	  2836    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2837  #if (IRQ_GTM_DPLL_SR22_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2838    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2839  #endif
; ..\mcal_src\Gtm_Irq.c	  2840  
; ..\mcal_src\Gtm_Irq.c	  2841  }
; ..\mcal_src\Gtm_Irq.c	  2842  #endif
; ..\mcal_src\Gtm_Irq.c	  2843  
; ..\mcal_src\Gtm_Irq.c	  2844  
; ..\mcal_src\Gtm_Irq.c	  2845  #if((IRQ_GTM_DPLL_SR23_PRIO > 0) || (IRQ_GTM_DPLL_SR23_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2846  #if((IRQ_GTM_DPLL_SR23_PRIO > 0) && (IRQ_GTM_DPLL_SR23_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2847  IFX_INTERRUPT(GTMDPLLSR23_ISR, 0, IRQ_GTM_DPLL_SR23_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2848  #elif IRQ_GTM_DPLL_SR23_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2849  ISR(GTMDPLLSR23_ISR)
; ..\mcal_src\Gtm_Irq.c	  2850  #endif
; ..\mcal_src\Gtm_Irq.c	  2851  {
; ..\mcal_src\Gtm_Irq.c	  2852    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2853  #if (IRQ_GTM_DPLL_SR23_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2854    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2855  #endif
; ..\mcal_src\Gtm_Irq.c	  2856  
; ..\mcal_src\Gtm_Irq.c	  2857  }
; ..\mcal_src\Gtm_Irq.c	  2858  #endif
; ..\mcal_src\Gtm_Irq.c	  2859  
; ..\mcal_src\Gtm_Irq.c	  2860  
; ..\mcal_src\Gtm_Irq.c	  2861  #if((IRQ_GTM_DPLL_SR24_PRIO > 0) || (IRQ_GTM_DPLL_SR24_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2862  #if((IRQ_GTM_DPLL_SR24_PRIO > 0) && (IRQ_GTM_DPLL_SR24_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2863  IFX_INTERRUPT(GTMDPLLSR24_ISR, 0, IRQ_GTM_DPLL_SR24_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2864  #elif IRQ_GTM_DPLL_SR24_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2865  ISR(GTMDPLLSR24_ISR)
; ..\mcal_src\Gtm_Irq.c	  2866  #endif
; ..\mcal_src\Gtm_Irq.c	  2867  {
; ..\mcal_src\Gtm_Irq.c	  2868    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2869  #if (IRQ_GTM_DPLL_SR24_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2870    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2871  #endif
; ..\mcal_src\Gtm_Irq.c	  2872  
; ..\mcal_src\Gtm_Irq.c	  2873  }
; ..\mcal_src\Gtm_Irq.c	  2874  #endif
; ..\mcal_src\Gtm_Irq.c	  2875  
; ..\mcal_src\Gtm_Irq.c	  2876  #if((IRQ_GTM_DPLL_SR25_PRIO > 0) || (IRQ_GTM_DPLL_SR25_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2877  #if((IRQ_GTM_DPLL_SR25_PRIO > 0) && (IRQ_GTM_DPLL_SR25_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2878  IFX_INTERRUPT(GTMDPLLSR25_ISR, 0, IRQ_GTM_DPLL_SR25_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2879  #elif IRQ_GTM_DPLL_SR25_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2880  ISR(GTMDPLLSR25_ISR)
; ..\mcal_src\Gtm_Irq.c	  2881  #endif
; ..\mcal_src\Gtm_Irq.c	  2882  {
; ..\mcal_src\Gtm_Irq.c	  2883    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2884  #if (IRQ_GTM_DPLL_SR25_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2885    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2886  #endif
; ..\mcal_src\Gtm_Irq.c	  2887  
; ..\mcal_src\Gtm_Irq.c	  2888  }
; ..\mcal_src\Gtm_Irq.c	  2889  #endif
; ..\mcal_src\Gtm_Irq.c	  2890  
; ..\mcal_src\Gtm_Irq.c	  2891  
; ..\mcal_src\Gtm_Irq.c	  2892  #if((IRQ_GTM_DPLL_SR26_PRIO > 0) || (IRQ_GTM_DPLL_SR26_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2893  #if((IRQ_GTM_DPLL_SR26_PRIO > 0) && (IRQ_GTM_DPLL_SR26_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2894  IFX_INTERRUPT(GTMDPLLSR26_ISR, 0, IRQ_GTM_DPLL_SR26_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2895  #elif IRQ_GTM_DPLL_SR26_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2896  ISR(GTMDPLLSR26_ISR)
; ..\mcal_src\Gtm_Irq.c	  2897  #endif
; ..\mcal_src\Gtm_Irq.c	  2898  {
; ..\mcal_src\Gtm_Irq.c	  2899    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2900  #if (IRQ_GTM_DPLL_SR26_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2901    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2902  #endif
; ..\mcal_src\Gtm_Irq.c	  2903  
; ..\mcal_src\Gtm_Irq.c	  2904  }
; ..\mcal_src\Gtm_Irq.c	  2905  #endif
; ..\mcal_src\Gtm_Irq.c	  2906  
; ..\mcal_src\Gtm_Irq.c	  2907  #endif /*#if(IRQ_GTM_DPLL_EXIST == (STD_ON))*/
; ..\mcal_src\Gtm_Irq.c	  2908  
; ..\mcal_src\Gtm_Irq.c	  2909  
; ..\mcal_src\Gtm_Irq.c	  2910  #if(IRQ_GTM_MCS0_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  2911  /****************** MCS0 *******************************************/
; ..\mcal_src\Gtm_Irq.c	  2912  #if((IRQ_GTM_MCS0_SR0_PRIO > 0) || (IRQ_GTM_MCS0_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2913  #if((IRQ_GTM_MCS0_SR0_PRIO > 0) && (IRQ_GTM_MCS0_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2914  IFX_INTERRUPT(GTMMCS0SR0_ISR, 0, IRQ_GTM_MCS0_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2915  #elif IRQ_GTM_MCS0_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2916  ISR(GTMMCS0SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  2917  #endif
; ..\mcal_src\Gtm_Irq.c	  2918  {
; ..\mcal_src\Gtm_Irq.c	  2919    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2920  #if (IRQ_GTM_MCS0_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2921    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2922  #endif
; ..\mcal_src\Gtm_Irq.c	  2923  
; ..\mcal_src\Gtm_Irq.c	  2924  }
; ..\mcal_src\Gtm_Irq.c	  2925  #endif
; ..\mcal_src\Gtm_Irq.c	  2926  
; ..\mcal_src\Gtm_Irq.c	  2927  #if((IRQ_GTM_MCS0_SR1_PRIO > 0) || (IRQ_GTM_MCS0_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2928  #if((IRQ_GTM_MCS0_SR1_PRIO > 0) && (IRQ_GTM_MCS0_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2929  IFX_INTERRUPT(GTMMCS0SR1_ISR, 0, IRQ_GTM_MCS0_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2930  #elif IRQ_GTM_MCS0_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2931  ISR(GTMMCS0SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  2932  #endif
; ..\mcal_src\Gtm_Irq.c	  2933  {
; ..\mcal_src\Gtm_Irq.c	  2934    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2935  #if (IRQ_GTM_MCS0_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2936    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2937  #endif
; ..\mcal_src\Gtm_Irq.c	  2938  
; ..\mcal_src\Gtm_Irq.c	  2939  }
; ..\mcal_src\Gtm_Irq.c	  2940  #endif
; ..\mcal_src\Gtm_Irq.c	  2941  
; ..\mcal_src\Gtm_Irq.c	  2942  #if((IRQ_GTM_MCS0_SR2_PRIO > 0) || (IRQ_GTM_MCS0_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2943  #if((IRQ_GTM_MCS0_SR2_PRIO > 0) && (IRQ_GTM_MCS0_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2944  IFX_INTERRUPT(GTMMCS0SR2_ISR, 0, IRQ_GTM_MCS0_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2945  #elif IRQ_GTM_MCS0_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2946  ISR(GTMMCS0SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  2947  #endif
; ..\mcal_src\Gtm_Irq.c	  2948  {
; ..\mcal_src\Gtm_Irq.c	  2949    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2950  #if (IRQ_GTM_MCS0_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2951    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2952  #endif
; ..\mcal_src\Gtm_Irq.c	  2953  
; ..\mcal_src\Gtm_Irq.c	  2954  }
; ..\mcal_src\Gtm_Irq.c	  2955  #endif
; ..\mcal_src\Gtm_Irq.c	  2956  
; ..\mcal_src\Gtm_Irq.c	  2957  
; ..\mcal_src\Gtm_Irq.c	  2958  #if((IRQ_GTM_MCS0_SR3_PRIO > 0) || (IRQ_GTM_MCS0_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2959  #if((IRQ_GTM_MCS0_SR3_PRIO > 0) && (IRQ_GTM_MCS0_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2960  IFX_INTERRUPT(GTMMCS0SR3_ISR, 0, IRQ_GTM_MCS0_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2961  #elif IRQ_GTM_MCS0_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2962  ISR(GTMMCS0SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  2963  #endif
; ..\mcal_src\Gtm_Irq.c	  2964  {
; ..\mcal_src\Gtm_Irq.c	  2965    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2966  #if (IRQ_GTM_MCS0_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2967    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2968  #endif
; ..\mcal_src\Gtm_Irq.c	  2969  
; ..\mcal_src\Gtm_Irq.c	  2970  }
; ..\mcal_src\Gtm_Irq.c	  2971  #endif
; ..\mcal_src\Gtm_Irq.c	  2972  
; ..\mcal_src\Gtm_Irq.c	  2973  #if((IRQ_GTM_MCS0_SR4_PRIO > 0) || (IRQ_GTM_MCS0_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2974  #if((IRQ_GTM_MCS0_SR4_PRIO > 0) && (IRQ_GTM_MCS0_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2975  IFX_INTERRUPT(GTMMCS0SR4_ISR, 0, IRQ_GTM_MCS0_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2976  #elif IRQ_GTM_MCS0_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2977  ISR(GTMMCS0SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  2978  #endif
; ..\mcal_src\Gtm_Irq.c	  2979  {
; ..\mcal_src\Gtm_Irq.c	  2980    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2981  #if (IRQ_GTM_MCS0_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2982    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2983  #endif
; ..\mcal_src\Gtm_Irq.c	  2984  
; ..\mcal_src\Gtm_Irq.c	  2985  }
; ..\mcal_src\Gtm_Irq.c	  2986  #endif
; ..\mcal_src\Gtm_Irq.c	  2987  
; ..\mcal_src\Gtm_Irq.c	  2988  #if((IRQ_GTM_MCS0_SR5_PRIO > 0) || (IRQ_GTM_MCS0_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  2989  #if((IRQ_GTM_MCS0_SR5_PRIO > 0) && (IRQ_GTM_MCS0_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  2990  IFX_INTERRUPT(GTMMCS0SR5_ISR, 0, IRQ_GTM_MCS0_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  2991  #elif IRQ_GTM_MCS0_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  2992  ISR(GTMMCS0SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  2993  #endif
; ..\mcal_src\Gtm_Irq.c	  2994  {
; ..\mcal_src\Gtm_Irq.c	  2995    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  2996  #if (IRQ_GTM_MCS0_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  2997    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  2998  #endif
; ..\mcal_src\Gtm_Irq.c	  2999  
; ..\mcal_src\Gtm_Irq.c	  3000  }
; ..\mcal_src\Gtm_Irq.c	  3001  #endif
; ..\mcal_src\Gtm_Irq.c	  3002  
; ..\mcal_src\Gtm_Irq.c	  3003  #if((IRQ_GTM_MCS0_SR6_PRIO > 0) || (IRQ_GTM_MCS0_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3004  #if((IRQ_GTM_MCS0_SR6_PRIO > 0) && (IRQ_GTM_MCS0_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3005  IFX_INTERRUPT(GTMMCS0SR6_ISR, 0, IRQ_GTM_MCS0_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3006  #elif IRQ_GTM_MCS0_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3007  ISR(GTMMCS0SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  3008  #endif
; ..\mcal_src\Gtm_Irq.c	  3009  {
; ..\mcal_src\Gtm_Irq.c	  3010    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3011  #if (IRQ_GTM_MCS0_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3012    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3013  #endif
; ..\mcal_src\Gtm_Irq.c	  3014  
; ..\mcal_src\Gtm_Irq.c	  3015  }
; ..\mcal_src\Gtm_Irq.c	  3016  #endif
; ..\mcal_src\Gtm_Irq.c	  3017  
; ..\mcal_src\Gtm_Irq.c	  3018  #if((IRQ_GTM_MCS0_SR7_PRIO > 0) || (IRQ_GTM_MCS0_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3019  #if((IRQ_GTM_MCS0_SR7_PRIO > 0) && (IRQ_GTM_MCS0_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3020  IFX_INTERRUPT(GTMMCS0SR7_ISR, 0, IRQ_GTM_MCS0_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3021  #elif IRQ_GTM_MCS0_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3022  ISR(GTMMCS0SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  3023  #endif
; ..\mcal_src\Gtm_Irq.c	  3024  {
; ..\mcal_src\Gtm_Irq.c	  3025    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3026  #if (IRQ_GTM_MCS0_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3027    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3028  #endif
; ..\mcal_src\Gtm_Irq.c	  3029  
; ..\mcal_src\Gtm_Irq.c	  3030  }
; ..\mcal_src\Gtm_Irq.c	  3031  #endif
; ..\mcal_src\Gtm_Irq.c	  3032  
; ..\mcal_src\Gtm_Irq.c	  3033  #endif /* #if(IRQ_GTM_MCS0_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  3034  
; ..\mcal_src\Gtm_Irq.c	  3035  #if(IRQ_GTM_MCS1_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  3036  /****************** MCS1 *******************************************/
; ..\mcal_src\Gtm_Irq.c	  3037  
; ..\mcal_src\Gtm_Irq.c	  3038  #if((IRQ_GTM_MCS1_SR0_PRIO > 0) || (IRQ_GTM_MCS1_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3039  #if((IRQ_GTM_MCS1_SR0_PRIO > 0) && (IRQ_GTM_MCS1_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3040  IFX_INTERRUPT(GTMMCS1SR0_ISR, 0, IRQ_GTM_MCS1_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3041  #elif IRQ_GTM_MCS1_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3042  ISR(GTMMCS1SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  3043  #endif
; ..\mcal_src\Gtm_Irq.c	  3044  {
; ..\mcal_src\Gtm_Irq.c	  3045    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3046  #if (IRQ_GTM_MCS1_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3047    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3048  #endif
; ..\mcal_src\Gtm_Irq.c	  3049  
; ..\mcal_src\Gtm_Irq.c	  3050  }
; ..\mcal_src\Gtm_Irq.c	  3051  #endif
; ..\mcal_src\Gtm_Irq.c	  3052  
; ..\mcal_src\Gtm_Irq.c	  3053  #if((IRQ_GTM_MCS1_SR1_PRIO > 0) || (IRQ_GTM_MCS1_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3054  #if((IRQ_GTM_MCS1_SR1_PRIO > 0) && (IRQ_GTM_MCS1_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3055  IFX_INTERRUPT(GTMMCS1SR1_ISR, 0, IRQ_GTM_MCS1_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3056  #elif IRQ_GTM_MCS1_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3057  ISR(GTMMCS1SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  3058  #endif
; ..\mcal_src\Gtm_Irq.c	  3059  {
; ..\mcal_src\Gtm_Irq.c	  3060    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3061  #if (IRQ_GTM_MCS1_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3062    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3063  #endif
; ..\mcal_src\Gtm_Irq.c	  3064  
; ..\mcal_src\Gtm_Irq.c	  3065  }
; ..\mcal_src\Gtm_Irq.c	  3066  #endif
; ..\mcal_src\Gtm_Irq.c	  3067  
; ..\mcal_src\Gtm_Irq.c	  3068  #if((IRQ_GTM_MCS1_SR2_PRIO > 0) || (IRQ_GTM_MCS1_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3069  #if((IRQ_GTM_MCS1_SR2_PRIO > 0) && (IRQ_GTM_MCS1_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3070  IFX_INTERRUPT(GTMMCS1SR2_ISR, 0, IRQ_GTM_MCS1_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3071  #elif IRQ_GTM_MCS1_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3072  ISR(GTMMCS1SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  3073  #endif
; ..\mcal_src\Gtm_Irq.c	  3074  {
; ..\mcal_src\Gtm_Irq.c	  3075    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3076  #if (IRQ_GTM_MCS1_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3077    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3078  #endif
; ..\mcal_src\Gtm_Irq.c	  3079  
; ..\mcal_src\Gtm_Irq.c	  3080  }
; ..\mcal_src\Gtm_Irq.c	  3081  #endif
; ..\mcal_src\Gtm_Irq.c	  3082  
; ..\mcal_src\Gtm_Irq.c	  3083  
; ..\mcal_src\Gtm_Irq.c	  3084  #if((IRQ_GTM_MCS1_SR3_PRIO > 0) || (IRQ_GTM_MCS1_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3085  #if((IRQ_GTM_MCS1_SR3_PRIO > 0) && (IRQ_GTM_MCS1_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3086  IFX_INTERRUPT(GTMMCS1SR3_ISR, 0, IRQ_GTM_MCS1_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3087  #elif IRQ_GTM_MCS1_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3088  ISR(GTMMCS1SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  3089  #endif
; ..\mcal_src\Gtm_Irq.c	  3090  {
; ..\mcal_src\Gtm_Irq.c	  3091    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3092  #if (IRQ_GTM_MCS1_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3093    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3094  #endif
; ..\mcal_src\Gtm_Irq.c	  3095  
; ..\mcal_src\Gtm_Irq.c	  3096  }
; ..\mcal_src\Gtm_Irq.c	  3097  #endif
; ..\mcal_src\Gtm_Irq.c	  3098  
; ..\mcal_src\Gtm_Irq.c	  3099  #if((IRQ_GTM_MCS1_SR4_PRIO > 0) || (IRQ_GTM_MCS1_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3100  #if((IRQ_GTM_MCS1_SR4_PRIO > 0) && (IRQ_GTM_MCS1_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3101  IFX_INTERRUPT(GTMMCS1SR4_ISR, 0, IRQ_GTM_MCS1_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3102  #elif IRQ_GTM_MCS1_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3103  ISR(GTMMCS1SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  3104  #endif
; ..\mcal_src\Gtm_Irq.c	  3105  {
; ..\mcal_src\Gtm_Irq.c	  3106    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3107  #if (IRQ_GTM_MCS1_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3108    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3109  #endif
; ..\mcal_src\Gtm_Irq.c	  3110  
; ..\mcal_src\Gtm_Irq.c	  3111  }
; ..\mcal_src\Gtm_Irq.c	  3112  #endif
; ..\mcal_src\Gtm_Irq.c	  3113  
; ..\mcal_src\Gtm_Irq.c	  3114  #if((IRQ_GTM_MCS1_SR5_PRIO > 0) || (IRQ_GTM_MCS1_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3115  #if((IRQ_GTM_MCS1_SR5_PRIO > 0) && (IRQ_GTM_MCS1_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3116  IFX_INTERRUPT(GTMMCS1SR5_ISR, 0, IRQ_GTM_MCS1_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3117  #elif IRQ_GTM_MCS1_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3118  ISR(GTMMCS1SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  3119  #endif
; ..\mcal_src\Gtm_Irq.c	  3120  {
; ..\mcal_src\Gtm_Irq.c	  3121    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3122  #if (IRQ_GTM_MCS1_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3123    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3124  #endif
; ..\mcal_src\Gtm_Irq.c	  3125  
; ..\mcal_src\Gtm_Irq.c	  3126  }
; ..\mcal_src\Gtm_Irq.c	  3127  #endif
; ..\mcal_src\Gtm_Irq.c	  3128  
; ..\mcal_src\Gtm_Irq.c	  3129  #if((IRQ_GTM_MCS1_SR6_PRIO > 0) || (IRQ_GTM_MCS1_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3130  #if((IRQ_GTM_MCS1_SR6_PRIO > 0) && (IRQ_GTM_MCS1_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3131  IFX_INTERRUPT(GTMMCS1SR6_ISR, 0, IRQ_GTM_MCS1_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3132  #elif IRQ_GTM_MCS1_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3133  ISR(GTMMCS1SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  3134  #endif
; ..\mcal_src\Gtm_Irq.c	  3135  {
; ..\mcal_src\Gtm_Irq.c	  3136    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3137  #if (IRQ_GTM_MCS1_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3138    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3139  #endif
; ..\mcal_src\Gtm_Irq.c	  3140  
; ..\mcal_src\Gtm_Irq.c	  3141  }
; ..\mcal_src\Gtm_Irq.c	  3142  #endif
; ..\mcal_src\Gtm_Irq.c	  3143  
; ..\mcal_src\Gtm_Irq.c	  3144  #if((IRQ_GTM_MCS1_SR7_PRIO > 0) || (IRQ_GTM_MCS1_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3145  #if((IRQ_GTM_MCS1_SR7_PRIO > 0) && (IRQ_GTM_MCS1_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3146  IFX_INTERRUPT(GTMMCS1SR7_ISR, 0, IRQ_GTM_MCS1_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3147  #elif IRQ_GTM_MCS1_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3148  ISR(GTMMCS1SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  3149  #endif
; ..\mcal_src\Gtm_Irq.c	  3150  {
; ..\mcal_src\Gtm_Irq.c	  3151    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3152  #if (IRQ_GTM_MCS1_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3153    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3154  #endif
; ..\mcal_src\Gtm_Irq.c	  3155  
; ..\mcal_src\Gtm_Irq.c	  3156  }
; ..\mcal_src\Gtm_Irq.c	  3157  #endif
; ..\mcal_src\Gtm_Irq.c	  3158  
; ..\mcal_src\Gtm_Irq.c	  3159  #endif /* #if(IRQ_GTM_MCS1_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  3160  
; ..\mcal_src\Gtm_Irq.c	  3161  #if(IRQ_GTM_MCS2_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  3162  /****************** MCS2 *******************************************/
; ..\mcal_src\Gtm_Irq.c	  3163  
; ..\mcal_src\Gtm_Irq.c	  3164  #if((IRQ_GTM_MCS2_SR0_PRIO > 0) || (IRQ_GTM_MCS2_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3165  #if((IRQ_GTM_MCS2_SR0_PRIO > 0) && (IRQ_GTM_MCS2_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3166  IFX_INTERRUPT(GTMMCS2SR0_ISR, 0, IRQ_GTM_MCS2_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3167  #elif IRQ_GTM_MCS2_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3168  ISR(GTMMCS2SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  3169  #endif
; ..\mcal_src\Gtm_Irq.c	  3170  {
; ..\mcal_src\Gtm_Irq.c	  3171    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3172  #if (IRQ_GTM_MCS2_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3173    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3174  #endif
; ..\mcal_src\Gtm_Irq.c	  3175  
; ..\mcal_src\Gtm_Irq.c	  3176  }
; ..\mcal_src\Gtm_Irq.c	  3177  #endif
; ..\mcal_src\Gtm_Irq.c	  3178  
; ..\mcal_src\Gtm_Irq.c	  3179  #if((IRQ_GTM_MCS2_SR1_PRIO > 0) || (IRQ_GTM_MCS2_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3180  #if((IRQ_GTM_MCS2_SR1_PRIO > 0) && (IRQ_GTM_MCS2_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3181  IFX_INTERRUPT(GTMMCS2SR1_ISR, 0, IRQ_GTM_MCS2_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3182  #elif IRQ_GTM_MCS2_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3183  ISR(GTMMCS2SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  3184  #endif
; ..\mcal_src\Gtm_Irq.c	  3185  {
; ..\mcal_src\Gtm_Irq.c	  3186    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3187  #if (IRQ_GTM_MCS2_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3188    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3189  #endif
; ..\mcal_src\Gtm_Irq.c	  3190  
; ..\mcal_src\Gtm_Irq.c	  3191  }
; ..\mcal_src\Gtm_Irq.c	  3192  #endif
; ..\mcal_src\Gtm_Irq.c	  3193  
; ..\mcal_src\Gtm_Irq.c	  3194  #if((IRQ_GTM_MCS2_SR2_PRIO > 0) || (IRQ_GTM_MCS2_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3195  #if((IRQ_GTM_MCS2_SR2_PRIO > 0) && (IRQ_GTM_MCS2_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3196  IFX_INTERRUPT(GTMMCS2SR2_ISR, 0, IRQ_GTM_MCS2_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3197  #elif IRQ_GTM_MCS2_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3198  ISR(GTMMCS2SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  3199  #endif
; ..\mcal_src\Gtm_Irq.c	  3200  {
; ..\mcal_src\Gtm_Irq.c	  3201    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3202  #if (IRQ_GTM_MCS2_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3203    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3204  #endif
; ..\mcal_src\Gtm_Irq.c	  3205  
; ..\mcal_src\Gtm_Irq.c	  3206  }
; ..\mcal_src\Gtm_Irq.c	  3207  #endif
; ..\mcal_src\Gtm_Irq.c	  3208  
; ..\mcal_src\Gtm_Irq.c	  3209  
; ..\mcal_src\Gtm_Irq.c	  3210  #if((IRQ_GTM_MCS2_SR3_PRIO > 0) || (IRQ_GTM_MCS2_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3211  #if((IRQ_GTM_MCS2_SR3_PRIO > 0) && (IRQ_GTM_MCS2_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3212  IFX_INTERRUPT(GTMMCS2SR3_ISR, 0, IRQ_GTM_MCS2_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3213  #elif IRQ_GTM_MCS2_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3214  ISR(GTMMCS2SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  3215  #endif
; ..\mcal_src\Gtm_Irq.c	  3216  {
; ..\mcal_src\Gtm_Irq.c	  3217    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3218  #if (IRQ_GTM_MCS2_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3219    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3220  #endif
; ..\mcal_src\Gtm_Irq.c	  3221  
; ..\mcal_src\Gtm_Irq.c	  3222  }
; ..\mcal_src\Gtm_Irq.c	  3223  #endif
; ..\mcal_src\Gtm_Irq.c	  3224  
; ..\mcal_src\Gtm_Irq.c	  3225  #if((IRQ_GTM_MCS2_SR4_PRIO > 0) || (IRQ_GTM_MCS2_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3226  #if((IRQ_GTM_MCS2_SR4_PRIO > 0) && (IRQ_GTM_MCS2_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3227  IFX_INTERRUPT(GTMMCS2SR4_ISR, 0, IRQ_GTM_MCS2_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3228  #elif IRQ_GTM_MCS2_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3229  ISR(GTMMCS2SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  3230  #endif
; ..\mcal_src\Gtm_Irq.c	  3231  {
; ..\mcal_src\Gtm_Irq.c	  3232    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3233  #if (IRQ_GTM_MCS2_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3234    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3235  #endif
; ..\mcal_src\Gtm_Irq.c	  3236  
; ..\mcal_src\Gtm_Irq.c	  3237  }
; ..\mcal_src\Gtm_Irq.c	  3238  #endif
; ..\mcal_src\Gtm_Irq.c	  3239  
; ..\mcal_src\Gtm_Irq.c	  3240  #if((IRQ_GTM_MCS2_SR5_PRIO > 0) || (IRQ_GTM_MCS2_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3241  #if((IRQ_GTM_MCS2_SR5_PRIO > 0) && (IRQ_GTM_MCS2_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3242  IFX_INTERRUPT(GTMMCS2SR5_ISR, 0, IRQ_GTM_MCS2_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3243  #elif IRQ_GTM_MCS2_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3244  ISR(GTMMCS2SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  3245  #endif
; ..\mcal_src\Gtm_Irq.c	  3246  {
; ..\mcal_src\Gtm_Irq.c	  3247    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3248  #if (IRQ_GTM_MCS2_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3249    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3250  #endif
; ..\mcal_src\Gtm_Irq.c	  3251  
; ..\mcal_src\Gtm_Irq.c	  3252  }
; ..\mcal_src\Gtm_Irq.c	  3253  #endif
; ..\mcal_src\Gtm_Irq.c	  3254  
; ..\mcal_src\Gtm_Irq.c	  3255  #if((IRQ_GTM_MCS2_SR6_PRIO > 0) || (IRQ_GTM_MCS2_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3256  #if((IRQ_GTM_MCS2_SR6_PRIO > 0) && (IRQ_GTM_MCS2_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3257  IFX_INTERRUPT(GTMMCS2SR6_ISR, 0, IRQ_GTM_MCS2_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3258  #elif IRQ_GTM_MCS2_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3259  ISR(GTMMCS2SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  3260  #endif
; ..\mcal_src\Gtm_Irq.c	  3261  {
; ..\mcal_src\Gtm_Irq.c	  3262    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3263  #if (IRQ_GTM_MCS2_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3264    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3265  #endif
; ..\mcal_src\Gtm_Irq.c	  3266  
; ..\mcal_src\Gtm_Irq.c	  3267  }
; ..\mcal_src\Gtm_Irq.c	  3268  #endif
; ..\mcal_src\Gtm_Irq.c	  3269  
; ..\mcal_src\Gtm_Irq.c	  3270  #if((IRQ_GTM_MCS2_SR7_PRIO > 0) || (IRQ_GTM_MCS2_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3271  #if((IRQ_GTM_MCS2_SR7_PRIO > 0) && (IRQ_GTM_MCS2_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3272  IFX_INTERRUPT(GTMMCS2SR7_ISR, 0, IRQ_GTM_MCS2_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3273  #elif IRQ_GTM_MCS2_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3274  ISR(GTMMCS2SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  3275  #endif
; ..\mcal_src\Gtm_Irq.c	  3276  {
; ..\mcal_src\Gtm_Irq.c	  3277    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3278  #if (IRQ_GTM_MCS2_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3279    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3280  #endif
; ..\mcal_src\Gtm_Irq.c	  3281  
; ..\mcal_src\Gtm_Irq.c	  3282  }
; ..\mcal_src\Gtm_Irq.c	  3283  #endif
; ..\mcal_src\Gtm_Irq.c	  3284  
; ..\mcal_src\Gtm_Irq.c	  3285  #endif /* #if(IRQ_GTM_MCS2_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  3286  
; ..\mcal_src\Gtm_Irq.c	  3287  #if(IRQ_GTM_MCS3_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  3288  /****************** MCS3 *******************************************/
; ..\mcal_src\Gtm_Irq.c	  3289  
; ..\mcal_src\Gtm_Irq.c	  3290  #if((IRQ_GTM_MCS3_SR0_PRIO > 0) || (IRQ_GTM_MCS3_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3291  #if((IRQ_GTM_MCS3_SR0_PRIO > 0) && (IRQ_GTM_MCS3_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3292  IFX_INTERRUPT(GTMMCS3SR0_ISR, 0, IRQ_GTM_MCS3_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3293  #elif IRQ_GTM_MCS3_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3294  ISR(GTMMCS3SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  3295  #endif
; ..\mcal_src\Gtm_Irq.c	  3296  {
; ..\mcal_src\Gtm_Irq.c	  3297    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3298  #if (IRQ_GTM_MCS3_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3299    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3300  #endif
; ..\mcal_src\Gtm_Irq.c	  3301  
; ..\mcal_src\Gtm_Irq.c	  3302  }
; ..\mcal_src\Gtm_Irq.c	  3303  #endif
; ..\mcal_src\Gtm_Irq.c	  3304  
; ..\mcal_src\Gtm_Irq.c	  3305  #if((IRQ_GTM_MCS3_SR1_PRIO > 0) || (IRQ_GTM_MCS3_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3306  #if((IRQ_GTM_MCS3_SR1_PRIO > 0) && (IRQ_GTM_MCS3_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3307  IFX_INTERRUPT(GTMMCS3SR1_ISR, 0, IRQ_GTM_MCS3_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3308  #elif IRQ_GTM_MCS3_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3309  ISR(GTMMCS3SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  3310  #endif
; ..\mcal_src\Gtm_Irq.c	  3311  {
; ..\mcal_src\Gtm_Irq.c	  3312    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3313  #if (IRQ_GTM_MCS3_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3314    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3315  #endif
; ..\mcal_src\Gtm_Irq.c	  3316  
; ..\mcal_src\Gtm_Irq.c	  3317  }
; ..\mcal_src\Gtm_Irq.c	  3318  #endif
; ..\mcal_src\Gtm_Irq.c	  3319  
; ..\mcal_src\Gtm_Irq.c	  3320  #if((IRQ_GTM_MCS3_SR2_PRIO > 0) || (IRQ_GTM_MCS3_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3321  #if((IRQ_GTM_MCS3_SR2_PRIO > 0) && (IRQ_GTM_MCS3_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3322  IFX_INTERRUPT(GTMMCS3SR2_ISR, 0, IRQ_GTM_MCS3_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3323  #elif IRQ_GTM_MCS3_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3324  ISR(GTMMCS3SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  3325  #endif
; ..\mcal_src\Gtm_Irq.c	  3326  {
; ..\mcal_src\Gtm_Irq.c	  3327    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3328  #if (IRQ_GTM_MCS3_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3329    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3330  #endif
; ..\mcal_src\Gtm_Irq.c	  3331  
; ..\mcal_src\Gtm_Irq.c	  3332  }
; ..\mcal_src\Gtm_Irq.c	  3333  #endif
; ..\mcal_src\Gtm_Irq.c	  3334  
; ..\mcal_src\Gtm_Irq.c	  3335  
; ..\mcal_src\Gtm_Irq.c	  3336  #if((IRQ_GTM_MCS3_SR3_PRIO > 0) || (IRQ_GTM_MCS3_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3337  #if((IRQ_GTM_MCS3_SR3_PRIO > 0) && (IRQ_GTM_MCS3_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3338  IFX_INTERRUPT(GTMMCS3SR3_ISR, 0, IRQ_GTM_MCS3_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3339  #elif IRQ_GTM_MCS3_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3340  ISR(GTMMCS3SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  3341  #endif
; ..\mcal_src\Gtm_Irq.c	  3342  {
; ..\mcal_src\Gtm_Irq.c	  3343    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3344  #if (IRQ_GTM_MCS3_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3345    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3346  #endif
; ..\mcal_src\Gtm_Irq.c	  3347  
; ..\mcal_src\Gtm_Irq.c	  3348  }
; ..\mcal_src\Gtm_Irq.c	  3349  #endif
; ..\mcal_src\Gtm_Irq.c	  3350  
; ..\mcal_src\Gtm_Irq.c	  3351  #if((IRQ_GTM_MCS3_SR4_PRIO > 0) || (IRQ_GTM_MCS3_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3352  #if((IRQ_GTM_MCS3_SR4_PRIO > 0) && (IRQ_GTM_MCS3_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3353  IFX_INTERRUPT(GTMMCS3SR4_ISR, 0, IRQ_GTM_MCS3_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3354  #elif IRQ_GTM_MCS3_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3355  ISR(GTMMCS3SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  3356  #endif
; ..\mcal_src\Gtm_Irq.c	  3357  {
; ..\mcal_src\Gtm_Irq.c	  3358    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3359  #if (IRQ_GTM_MCS3_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3360    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3361  #endif
; ..\mcal_src\Gtm_Irq.c	  3362  
; ..\mcal_src\Gtm_Irq.c	  3363  }
; ..\mcal_src\Gtm_Irq.c	  3364  #endif
; ..\mcal_src\Gtm_Irq.c	  3365  
; ..\mcal_src\Gtm_Irq.c	  3366  #if((IRQ_GTM_MCS3_SR5_PRIO > 0) || (IRQ_GTM_MCS3_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3367  #if((IRQ_GTM_MCS3_SR5_PRIO > 0) && (IRQ_GTM_MCS3_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3368  IFX_INTERRUPT(GTMMCS3SR5_ISR, 0, IRQ_GTM_MCS3_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3369  #elif IRQ_GTM_MCS3_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3370  ISR(GTMMCS3SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  3371  #endif
; ..\mcal_src\Gtm_Irq.c	  3372  {
; ..\mcal_src\Gtm_Irq.c	  3373    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3374  #if (IRQ_GTM_MCS3_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3375    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3376  #endif
; ..\mcal_src\Gtm_Irq.c	  3377  
; ..\mcal_src\Gtm_Irq.c	  3378  }
; ..\mcal_src\Gtm_Irq.c	  3379  #endif
; ..\mcal_src\Gtm_Irq.c	  3380  
; ..\mcal_src\Gtm_Irq.c	  3381  #if((IRQ_GTM_MCS3_SR6_PRIO > 0) || (IRQ_GTM_MCS3_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3382  #if((IRQ_GTM_MCS3_SR6_PRIO > 0) && (IRQ_GTM_MCS3_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3383  IFX_INTERRUPT(GTMMCS3SR6_ISR, 0, IRQ_GTM_MCS3_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3384  #elif IRQ_GTM_MCS3_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3385  ISR(GTMMCS3SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  3386  #endif
; ..\mcal_src\Gtm_Irq.c	  3387  {
; ..\mcal_src\Gtm_Irq.c	  3388    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3389  #if (IRQ_GTM_MCS3_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3390    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3391  #endif
; ..\mcal_src\Gtm_Irq.c	  3392  
; ..\mcal_src\Gtm_Irq.c	  3393  }
; ..\mcal_src\Gtm_Irq.c	  3394  #endif
; ..\mcal_src\Gtm_Irq.c	  3395  
; ..\mcal_src\Gtm_Irq.c	  3396  #if((IRQ_GTM_MCS3_SR7_PRIO > 0) || (IRQ_GTM_MCS3_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3397  #if((IRQ_GTM_MCS3_SR7_PRIO > 0) && (IRQ_GTM_MCS3_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3398  IFX_INTERRUPT(GTMMCS3SR7_ISR, 0, IRQ_GTM_MCS3_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3399  #elif IRQ_GTM_MCS3_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3400  ISR(GTMMCS3SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  3401  #endif
; ..\mcal_src\Gtm_Irq.c	  3402  {
; ..\mcal_src\Gtm_Irq.c	  3403    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3404  #if (IRQ_GTM_MCS3_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3405    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3406  #endif
; ..\mcal_src\Gtm_Irq.c	  3407  
; ..\mcal_src\Gtm_Irq.c	  3408  }
; ..\mcal_src\Gtm_Irq.c	  3409  #endif
; ..\mcal_src\Gtm_Irq.c	  3410  
; ..\mcal_src\Gtm_Irq.c	  3411  #endif /* #if(IRQ_GTM_MCS3_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  3412  
; ..\mcal_src\Gtm_Irq.c	  3413  
; ..\mcal_src\Gtm_Irq.c	  3414  #if(IRQ_GTM_MCS4_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  3415  /****************** MCS4 *******************************************/
; ..\mcal_src\Gtm_Irq.c	  3416  
; ..\mcal_src\Gtm_Irq.c	  3417  #if((IRQ_GTM_MCS4_SR0_PRIO > 0) || (IRQ_GTM_MCS4_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3418  #if((IRQ_GTM_MCS4_SR0_PRIO > 0) && (IRQ_GTM_MCS4_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3419  IFX_INTERRUPT(GTMMCS4SR0_ISR, 0, IRQ_GTM_MCS4_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3420  #elif IRQ_GTM_MCS4_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3421  ISR(GTMMCS4SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  3422  #endif
; ..\mcal_src\Gtm_Irq.c	  3423  {
; ..\mcal_src\Gtm_Irq.c	  3424    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3425  #if (IRQ_GTM_MCS4_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3426    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3427  #endif
; ..\mcal_src\Gtm_Irq.c	  3428  
; ..\mcal_src\Gtm_Irq.c	  3429  }
; ..\mcal_src\Gtm_Irq.c	  3430  #endif
; ..\mcal_src\Gtm_Irq.c	  3431  
; ..\mcal_src\Gtm_Irq.c	  3432  #if((IRQ_GTM_MCS4_SR1_PRIO > 0) || (IRQ_GTM_MCS4_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3433  #if((IRQ_GTM_MCS4_SR1_PRIO > 0) && (IRQ_GTM_MCS4_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3434  IFX_INTERRUPT(GTMMCS4SR1_ISR, 0, IRQ_GTM_MCS4_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3435  #elif IRQ_GTM_MCS4_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3436  ISR(GTMMCS4SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  3437  #endif
; ..\mcal_src\Gtm_Irq.c	  3438  {
; ..\mcal_src\Gtm_Irq.c	  3439    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3440  #if (IRQ_GTM_MCS4_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3441    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3442  #endif
; ..\mcal_src\Gtm_Irq.c	  3443  
; ..\mcal_src\Gtm_Irq.c	  3444  }
; ..\mcal_src\Gtm_Irq.c	  3445  #endif
; ..\mcal_src\Gtm_Irq.c	  3446  
; ..\mcal_src\Gtm_Irq.c	  3447  #if((IRQ_GTM_MCS4_SR2_PRIO > 0) || (IRQ_GTM_MCS4_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3448  #if((IRQ_GTM_MCS4_SR2_PRIO > 0) && (IRQ_GTM_MCS4_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3449  IFX_INTERRUPT(GTMMCS4SR2_ISR, 0, IRQ_GTM_MCS4_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3450  #elif IRQ_GTM_MCS4_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3451  ISR(GTMMCS4SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  3452  #endif
; ..\mcal_src\Gtm_Irq.c	  3453  {
; ..\mcal_src\Gtm_Irq.c	  3454    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3455  #if (IRQ_GTM_MCS4_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3456    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3457  #endif
; ..\mcal_src\Gtm_Irq.c	  3458  
; ..\mcal_src\Gtm_Irq.c	  3459  }
; ..\mcal_src\Gtm_Irq.c	  3460  #endif
; ..\mcal_src\Gtm_Irq.c	  3461  
; ..\mcal_src\Gtm_Irq.c	  3462  
; ..\mcal_src\Gtm_Irq.c	  3463  #if((IRQ_GTM_MCS4_SR3_PRIO > 0) || (IRQ_GTM_MCS4_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3464  #if((IRQ_GTM_MCS4_SR3_PRIO > 0) && (IRQ_GTM_MCS4_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3465  IFX_INTERRUPT(GTMMCS4SR3_ISR, 0, IRQ_GTM_MCS4_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3466  #elif IRQ_GTM_MCS4_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3467  ISR(GTMMCS4SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  3468  #endif
; ..\mcal_src\Gtm_Irq.c	  3469  {
; ..\mcal_src\Gtm_Irq.c	  3470    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3471  #if (IRQ_GTM_MCS4_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3472    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3473  #endif
; ..\mcal_src\Gtm_Irq.c	  3474  
; ..\mcal_src\Gtm_Irq.c	  3475  }
; ..\mcal_src\Gtm_Irq.c	  3476  #endif
; ..\mcal_src\Gtm_Irq.c	  3477  
; ..\mcal_src\Gtm_Irq.c	  3478  #if((IRQ_GTM_MCS4_SR4_PRIO > 0) || (IRQ_GTM_MCS4_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3479  #if((IRQ_GTM_MCS4_SR4_PRIO > 0) && (IRQ_GTM_MCS4_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3480  IFX_INTERRUPT(GTMMCS4SR4_ISR, 0, IRQ_GTM_MCS4_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3481  #elif IRQ_GTM_MCS4_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3482  ISR(GTMMCS4SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  3483  #endif
; ..\mcal_src\Gtm_Irq.c	  3484  {
; ..\mcal_src\Gtm_Irq.c	  3485    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3486  #if (IRQ_GTM_MCS4_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3487    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3488  #endif
; ..\mcal_src\Gtm_Irq.c	  3489  
; ..\mcal_src\Gtm_Irq.c	  3490  }
; ..\mcal_src\Gtm_Irq.c	  3491  #endif
; ..\mcal_src\Gtm_Irq.c	  3492  
; ..\mcal_src\Gtm_Irq.c	  3493  #if((IRQ_GTM_MCS4_SR5_PRIO > 0) || (IRQ_GTM_MCS4_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3494  #if((IRQ_GTM_MCS4_SR5_PRIO > 0) && (IRQ_GTM_MCS4_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3495  IFX_INTERRUPT(GTMMCS4SR5_ISR, 0, IRQ_GTM_MCS4_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3496  #elif IRQ_GTM_MCS4_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3497  ISR(GTMMCS4SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  3498  #endif
; ..\mcal_src\Gtm_Irq.c	  3499  {
; ..\mcal_src\Gtm_Irq.c	  3500    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3501  #if (IRQ_GTM_MCS4_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3502    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3503  #endif
; ..\mcal_src\Gtm_Irq.c	  3504  
; ..\mcal_src\Gtm_Irq.c	  3505  }
; ..\mcal_src\Gtm_Irq.c	  3506  #endif
; ..\mcal_src\Gtm_Irq.c	  3507  
; ..\mcal_src\Gtm_Irq.c	  3508  #if((IRQ_GTM_MCS4_SR6_PRIO > 0) || (IRQ_GTM_MCS4_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3509  #if((IRQ_GTM_MCS4_SR6_PRIO > 0) && (IRQ_GTM_MCS4_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3510  IFX_INTERRUPT(GTMMCS4SR6_ISR, 0, IRQ_GTM_MCS4_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3511  #elif IRQ_GTM_MCS4_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3512  ISR(GTMMCS4SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  3513  #endif
; ..\mcal_src\Gtm_Irq.c	  3514  {
; ..\mcal_src\Gtm_Irq.c	  3515    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3516  #if (IRQ_GTM_MCS4_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3517    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3518  #endif
; ..\mcal_src\Gtm_Irq.c	  3519  
; ..\mcal_src\Gtm_Irq.c	  3520  }
; ..\mcal_src\Gtm_Irq.c	  3521  #endif
; ..\mcal_src\Gtm_Irq.c	  3522  
; ..\mcal_src\Gtm_Irq.c	  3523  #if((IRQ_GTM_MCS4_SR7_PRIO > 0) || (IRQ_GTM_MCS4_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3524  #if((IRQ_GTM_MCS4_SR7_PRIO > 0) && (IRQ_GTM_MCS4_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3525  IFX_INTERRUPT(GTMMCS4SR7_ISR, 0, IRQ_GTM_MCS4_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3526  #elif IRQ_GTM_MCS4_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3527  ISR(GTMMCS4SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  3528  #endif
; ..\mcal_src\Gtm_Irq.c	  3529  {
; ..\mcal_src\Gtm_Irq.c	  3530    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3531  #if (IRQ_GTM_MCS4_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3532    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3533  #endif
; ..\mcal_src\Gtm_Irq.c	  3534  
; ..\mcal_src\Gtm_Irq.c	  3535  }
; ..\mcal_src\Gtm_Irq.c	  3536  #endif
; ..\mcal_src\Gtm_Irq.c	  3537  
; ..\mcal_src\Gtm_Irq.c	  3538  #endif /* #if(IRQ_GTM_MCS4_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  3539  
; ..\mcal_src\Gtm_Irq.c	  3540  
; ..\mcal_src\Gtm_Irq.c	  3541  #if(IRQ_GTM_MCS5_EXIST == (STD_ON))
; ..\mcal_src\Gtm_Irq.c	  3542  /****************** MCS5 *******************************************/
; ..\mcal_src\Gtm_Irq.c	  3543  
; ..\mcal_src\Gtm_Irq.c	  3544  #if((IRQ_GTM_MCS5_SR0_PRIO > 0) || (IRQ_GTM_MCS5_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3545  #if((IRQ_GTM_MCS5_SR0_PRIO > 0) && (IRQ_GTM_MCS5_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3546  IFX_INTERRUPT(GTMMCS5SR0_ISR, 0, IRQ_GTM_MCS5_SR0_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3547  #elif IRQ_GTM_MCS5_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3548  ISR(GTMMCS5SR0_ISR)
; ..\mcal_src\Gtm_Irq.c	  3549  #endif
; ..\mcal_src\Gtm_Irq.c	  3550  {
; ..\mcal_src\Gtm_Irq.c	  3551    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3552  #if (IRQ_GTM_MCS5_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3553    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3554  #endif
; ..\mcal_src\Gtm_Irq.c	  3555  
; ..\mcal_src\Gtm_Irq.c	  3556  }
; ..\mcal_src\Gtm_Irq.c	  3557  #endif
; ..\mcal_src\Gtm_Irq.c	  3558  
; ..\mcal_src\Gtm_Irq.c	  3559  #if((IRQ_GTM_MCS5_SR1_PRIO > 0) || (IRQ_GTM_MCS5_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3560  #if((IRQ_GTM_MCS5_SR1_PRIO > 0) && (IRQ_GTM_MCS5_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3561  IFX_INTERRUPT(GTMMCS5SR1_ISR, 0, IRQ_GTM_MCS5_SR1_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3562  #elif IRQ_GTM_MCS5_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3563  ISR(GTMMCS5SR1_ISR)
; ..\mcal_src\Gtm_Irq.c	  3564  #endif
; ..\mcal_src\Gtm_Irq.c	  3565  {
; ..\mcal_src\Gtm_Irq.c	  3566    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3567  #if (IRQ_GTM_MCS5_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3568    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3569  #endif
; ..\mcal_src\Gtm_Irq.c	  3570  
; ..\mcal_src\Gtm_Irq.c	  3571  }
; ..\mcal_src\Gtm_Irq.c	  3572  #endif
; ..\mcal_src\Gtm_Irq.c	  3573  
; ..\mcal_src\Gtm_Irq.c	  3574  #if((IRQ_GTM_MCS5_SR2_PRIO > 0) || (IRQ_GTM_MCS5_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3575  #if((IRQ_GTM_MCS5_SR2_PRIO > 0) && (IRQ_GTM_MCS5_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3576  IFX_INTERRUPT(GTMMCS5SR2_ISR, 0, IRQ_GTM_MCS5_SR2_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3577  #elif IRQ_GTM_MCS5_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3578  ISR(GTMMCS5SR2_ISR)
; ..\mcal_src\Gtm_Irq.c	  3579  #endif
; ..\mcal_src\Gtm_Irq.c	  3580  {
; ..\mcal_src\Gtm_Irq.c	  3581    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3582  #if (IRQ_GTM_MCS5_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3583    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3584  #endif
; ..\mcal_src\Gtm_Irq.c	  3585  
; ..\mcal_src\Gtm_Irq.c	  3586  }
; ..\mcal_src\Gtm_Irq.c	  3587  #endif
; ..\mcal_src\Gtm_Irq.c	  3588  
; ..\mcal_src\Gtm_Irq.c	  3589  
; ..\mcal_src\Gtm_Irq.c	  3590  #if((IRQ_GTM_MCS5_SR3_PRIO > 0) || (IRQ_GTM_MCS5_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3591  #if((IRQ_GTM_MCS5_SR3_PRIO > 0) && (IRQ_GTM_MCS5_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3592  IFX_INTERRUPT(GTMMCS5SR3_ISR, 0, IRQ_GTM_MCS5_SR3_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3593  #elif IRQ_GTM_MCS5_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3594  ISR(GTMMCS5SR3_ISR)
; ..\mcal_src\Gtm_Irq.c	  3595  #endif
; ..\mcal_src\Gtm_Irq.c	  3596  {
; ..\mcal_src\Gtm_Irq.c	  3597    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3598  #if (IRQ_GTM_MCS5_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3599    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3600  #endif
; ..\mcal_src\Gtm_Irq.c	  3601  
; ..\mcal_src\Gtm_Irq.c	  3602  }
; ..\mcal_src\Gtm_Irq.c	  3603  #endif
; ..\mcal_src\Gtm_Irq.c	  3604  
; ..\mcal_src\Gtm_Irq.c	  3605  #if((IRQ_GTM_MCS5_SR4_PRIO > 0) || (IRQ_GTM_MCS5_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3606  #if((IRQ_GTM_MCS5_SR4_PRIO > 0) && (IRQ_GTM_MCS5_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3607  IFX_INTERRUPT(GTMMCS5SR4_ISR, 0, IRQ_GTM_MCS5_SR4_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3608  #elif IRQ_GTM_MCS5_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3609  ISR(GTMMCS5SR4_ISR)
; ..\mcal_src\Gtm_Irq.c	  3610  #endif
; ..\mcal_src\Gtm_Irq.c	  3611  {
; ..\mcal_src\Gtm_Irq.c	  3612    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3613  #if (IRQ_GTM_MCS5_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3614    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3615  #endif
; ..\mcal_src\Gtm_Irq.c	  3616  
; ..\mcal_src\Gtm_Irq.c	  3617  }
; ..\mcal_src\Gtm_Irq.c	  3618  #endif
; ..\mcal_src\Gtm_Irq.c	  3619  
; ..\mcal_src\Gtm_Irq.c	  3620  #if((IRQ_GTM_MCS5_SR5_PRIO > 0) || (IRQ_GTM_MCS5_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3621  #if((IRQ_GTM_MCS5_SR5_PRIO > 0) && (IRQ_GTM_MCS5_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3622  IFX_INTERRUPT(GTMMCS5SR5_ISR, 0, IRQ_GTM_MCS5_SR5_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3623  #elif IRQ_GTM_MCS5_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3624  ISR(GTMMCS5SR5_ISR)
; ..\mcal_src\Gtm_Irq.c	  3625  #endif
; ..\mcal_src\Gtm_Irq.c	  3626  {
; ..\mcal_src\Gtm_Irq.c	  3627    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3628  #if (IRQ_GTM_MCS5_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3629    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3630  #endif
; ..\mcal_src\Gtm_Irq.c	  3631  
; ..\mcal_src\Gtm_Irq.c	  3632  }
; ..\mcal_src\Gtm_Irq.c	  3633  #endif
; ..\mcal_src\Gtm_Irq.c	  3634  
; ..\mcal_src\Gtm_Irq.c	  3635  #if((IRQ_GTM_MCS5_SR6_PRIO > 0) || (IRQ_GTM_MCS5_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3636  #if((IRQ_GTM_MCS5_SR6_PRIO > 0) && (IRQ_GTM_MCS5_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3637  IFX_INTERRUPT(GTMMCS5SR6_ISR, 0, IRQ_GTM_MCS5_SR6_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3638  #elif IRQ_GTM_MCS5_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3639  ISR(GTMMCS5SR6_ISR)
; ..\mcal_src\Gtm_Irq.c	  3640  #endif
; ..\mcal_src\Gtm_Irq.c	  3641  {
; ..\mcal_src\Gtm_Irq.c	  3642    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3643  #if (IRQ_GTM_MCS5_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3644    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3645  #endif
; ..\mcal_src\Gtm_Irq.c	  3646  
; ..\mcal_src\Gtm_Irq.c	  3647  }
; ..\mcal_src\Gtm_Irq.c	  3648  #endif
; ..\mcal_src\Gtm_Irq.c	  3649  
; ..\mcal_src\Gtm_Irq.c	  3650  #if((IRQ_GTM_MCS5_SR7_PRIO > 0) || (IRQ_GTM_MCS5_SR7_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3651  #if((IRQ_GTM_MCS5_SR7_PRIO > 0) && (IRQ_GTM_MCS5_SR7_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3652  IFX_INTERRUPT(GTMMCS5SR7_ISR, 0, IRQ_GTM_MCS5_SR7_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3653  #elif IRQ_GTM_MCS5_SR7_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3654  ISR(GTMMCS5SR7_ISR)
; ..\mcal_src\Gtm_Irq.c	  3655  #endif
; ..\mcal_src\Gtm_Irq.c	  3656  {
; ..\mcal_src\Gtm_Irq.c	  3657    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3658  #if (IRQ_GTM_MCS5_SR7_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3659    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3660  #endif
; ..\mcal_src\Gtm_Irq.c	  3661  
; ..\mcal_src\Gtm_Irq.c	  3662  }
; ..\mcal_src\Gtm_Irq.c	  3663  #endif
; ..\mcal_src\Gtm_Irq.c	  3664  
; ..\mcal_src\Gtm_Irq.c	  3665  #endif /* #if(IRQ_GTM_MCS5_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  3666  
; ..\mcal_src\Gtm_Irq.c	  3667  #if((IRQ_GTM_ERR_SR_PRIO > 0) || (IRQ_GTM_ERR_SR_CAT == IRQ_CAT23))
; ..\mcal_src\Gtm_Irq.c	  3668  #if((IRQ_GTM_ERR_SR_PRIO > 0) && (IRQ_GTM_ERR_SR_CAT == IRQ_CAT1))
; ..\mcal_src\Gtm_Irq.c	  3669  IFX_INTERRUPT(GTMERRSR_ISR, 0, IRQ_GTM_ERR_SR_PRIO)
; ..\mcal_src\Gtm_Irq.c	  3670  #elif IRQ_GTM_ERR_SR_CAT == IRQ_CAT23
; ..\mcal_src\Gtm_Irq.c	  3671  ISR(GTMERRSR_ISR)
; ..\mcal_src\Gtm_Irq.c	  3672  #endif
; ..\mcal_src\Gtm_Irq.c	  3673  {
; ..\mcal_src\Gtm_Irq.c	  3674    /* Enable Global Interrupts */
; ..\mcal_src\Gtm_Irq.c	  3675  #if (IRQ_GTM_ERR_SR_CAT == IRQ_CAT1)
; ..\mcal_src\Gtm_Irq.c	  3676    Mcal_EnableAllInterrupts();
; ..\mcal_src\Gtm_Irq.c	  3677  #endif
; ..\mcal_src\Gtm_Irq.c	  3678  
; ..\mcal_src\Gtm_Irq.c	  3679  }
; ..\mcal_src\Gtm_Irq.c	  3680  #endif
; ..\mcal_src\Gtm_Irq.c	  3681  
; ..\mcal_src\Gtm_Irq.c	  3682  #endif /* #if (IRQ_GTM_EXIST == (STD_ON)) */
; ..\mcal_src\Gtm_Irq.c	  3683  
; ..\mcal_src\Gtm_Irq.c	  3684  #define IRQ_STOP_SEC_CODE
; ..\mcal_src\Gtm_Irq.c	  3685  #include "MemMap.h"

	; Module end
