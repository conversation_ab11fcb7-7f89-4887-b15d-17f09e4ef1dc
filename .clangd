# clangd 配置文件 - 嵌入式C语言 + MISRA C 2012
CompileFlags:
  Add:
    - -std=c99
    - -Wall
    - -Wextra
    - -Wpedantic
    - -Wconversion
    - -Wsign-conversion
    - -Wcast-qual
    - -Wcast-align
    - -Wpointer-arith
    - -Wredundant-decls
    - -Wswitch-default
    - -Wswitch-enum
    - -Wunused-parameter
    - -Wunused-variable
    - -Wunused-function
    - -Wmissing-prototypes
    - -Wstrict-prototypes
    - -Wold-style-definition
    - -Wmissing-declarations
    - -Wnested-externs
    - -Wbad-function-cast
    - -Wwrite-strings
    - -Wfloat-equal
    - -Wundef
    - -Wshadow
    - -Wlogical-op
    - -Wformat=2
    - -Winit-self
    - -Wmissing-include-dirs
    - -Wstrict-overflow=5
    - -Wno-unused-parameter
    # === Tasking 编译器内置函数支持 ===
    - -Wno-implicit-function-declaration  # 允许隐式函数声明（用于编译器内置函数）
    - -Wno-main-return-type              # 嵌入式系统main函数通常返回void
    - -ffreestanding                    # 指示这是独立环境（嵌入式）
    - -Dmain=main                        # 确保main函数被正确识别
    - -Wno-gnu-anonymous-struct          # 允许匿名结构体
    - -Wno-nested-anon-types             # 允许嵌套匿名类型
    # Tasking TriCore 内置函数声明
    - -D__nop()=
    - -D__isync()=
    - -D__dsync()=
    - -D__enable()=
    - -D__disable()=
    - -D__mfcr(x)=0
    - -D__mtcr(x,y)=
    - -D__clz(x)=0
    - -D__crc32(x,y)=0
    - -D__noinline=
    - -D__inline=inline
    # Infineon TriCore 寄存器类型支持
    - -D__sfrbit16=unsigned
    - -D__sfrbit32=unsigned
    - -DIfx_Strict_16Bit=unsigned
    - -DIfx_Strict_32Bit=unsigned

Diagnostics:
  ClangTidy:
    Add:
      # === 内存安全相关 (MISRA C 2012: Rule 18.x, 21.x) ===
      - bugprone-sizeof-expression
      - bugprone-sizeof-container
      - bugprone-suspicious-memset-usage
      - bugprone-not-null-terminated-result
      - bugprone-dangling-handle
      - bugprone-use-after-move
      - clang-analyzer-core.*
      - clang-analyzer-security.*
      - clang-analyzer-alpha.security.*

      # === 类型安全相关 (MISRA C 2012: Rule 10.x, 11.x) ===
      - bugprone-implicit-widening-of-multiplication-result
      - bugprone-integer-division
      - bugprone-misplaced-widening-cast
      - bugprone-narrowing-conversions
      - bugprone-signed-char-misuse
      - misc-misplaced-const

      # === 控制流相关 (MISRA C 2012: Rule 9.x, 15.x) ===
      - bugprone-branch-clone
      - bugprone-infinite-loop
      - bugprone-suspicious-semicolon
      - bugprone-swapped-arguments
      - clang-analyzer-deadcode.*

      # === 函数相关 (MISRA C 2012: Rule 8.x, 17.x) ===
      - bugprone-unused-return-value
      - cert-dcl03-c
      - cert-dcl37-c
      - cert-dcl50-cpp
      - misc-unused-parameters

      # === MISRA C 2012 核心规则对应 ===
      - cert-err33-c              # Rule 22.8: 检查库函数返回值
      - cert-err34-c              # Rule 21.7: 检查字符串转换函数
      - misc-static-assert        # Rule 20.9: 编译时断言

    Remove:
      # === 不适合嵌入式环境的规则 ===
      - readability-magic-numbers                    # 嵌入式常用寄存器地址等魔法数字
      - cppcoreguidelines-avoid-magic-numbers        # 同上
      - cppcoreguidelines-avoid-non-const-global-variables  # 嵌入式需要全局状态变量
      - cppcoreguidelines-avoid-c-arrays             # C语言项目需要使用数组
      - modernize-*                                   # C++现代化规则，不适用于C
      - readability-function-cognitive-complexity    # 嵌入式状态机可能较复杂
      - readability-identifier-length                # 嵌入式常用短变量名
      - bugprone-easily-swappable-parameters         # 嵌入式API常有相似参数
      - cert-dcl51-cpp                               # C++规则
      - hicpp-multiway-paths-covered                 # 过于严格的switch覆盖要求
      - readability-identifier-naming                # 命名风格检查

      # === Tasking 编译器特有功能支持 ===
      - misc-definitions-in-headers                   # 允许头文件中的内联汇编定义
      - readability-function-size                     # 嵌入式main函数可能很大
      - bugprone-reserved-identifier                  # 允许编译器保留标识符
      - misc-non-private-member-variables-in-classes  # 允许公共成员变量

      # === 代码风格偏好相关规则 (非安全性问题) ===
      - readability-uppercase-literal-suffix         # 字面量后缀大小写偏好
      - readability-braces-around-statements         # 大括号风格偏好
      - readability-else-after-return                # else-after-return风格偏好
      - readability-function-size                    # 函数大小限制偏好
      - readability-inconsistent-declaration-parameter-name  # 参数命名一致性偏好
      - readability-const-return-type                # const返回类型风格偏好
      - readability-redundant-control-flow           # 冗余控制流风格偏好
      - readability-redundant-declaration            # 冗余声明风格偏好
      - readability-redundant-function-ptr-dereference  # 函数指针解引用风格偏好
      - readability-redundant-smartptr-get           # 智能指针风格偏好(C++相关)
      - readability-simplify-boolean-expr            # 布尔表达式简化风格偏好
      - readability-static-accessed-through-instance # 静态成员访问风格偏好
      - readability-string-compare                   # 字符串比较风格偏好
      - readability-uniqueptr-delete-release         # 智能指针风格偏好(C++相关)
      - readability-misleading-indentation           # 缩进风格偏好
      - readability-delete-null-pointer              # 空指针删除风格偏好
      - readability-non-const-parameter              # 参数const风格偏好

Index:
  Background: Build
