	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc4304a -c99 --dep-file=eeprom\\.eeprom.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\eeprom.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\eeprom.src ..\\eeprom\\eeprom.c"
	.compiler_name		"ctc"
	.name	"eeprom"

	
$TC16X
	
	.sdecl	'.text.eeprom.EEP_DID_Init',code,cluster('EEP_DID_Init')
	.sect	'.text.eeprom.EEP_DID_Init'
	.align	2
	

; ..\eeprom\eeprom.c	     1  /*
; ..\eeprom\eeprom.c	     2   * eeprom.c
; ..\eeprom\eeprom.c	     3   *
; ..\eeprom\eeprom.c	     4   *  Created on: 2019-9-16
; ..\eeprom\eeprom.c	     5   *      Author: fanghongqing
; ..\eeprom\eeprom.c	     6   */
; ..\eeprom\eeprom.c	     7  
; ..\eeprom\eeprom.c	     8  #include "Platform_Types.h"
; ..\eeprom\eeprom.c	     9  #include "Did_Cfg.h"
; ..\eeprom\eeprom.c	    10  #include "FL.h"
; ..\eeprom\eeprom.c	    11  #include "eeprom_Cfg.h"
; ..\eeprom\eeprom.c	    12  #include "Fls.h"
; ..\eeprom\eeprom.c	    13  #include "Fls_17_Pmu.h"
; ..\eeprom\eeprom.c	    14  #include "Fee.h"
; ..\eeprom\eeprom.c	    15  #include "NvM.h"
; ..\eeprom\eeprom.c	    16  #define EEPROM_OK   FL_OK
; ..\eeprom\eeprom.c	    17  #define EEPROM_NOK  FL_FAILED
; ..\eeprom\eeprom.c	    18  #define EEPROM_OUTOFRANGE FL_ERR_SEQUENCE
; ..\eeprom\eeprom.c	    19  #define EEPROM_WRITE_MORE_TIME FL_WRITEMOREONETIME
; ..\eeprom\eeprom.c	    20  
; ..\eeprom\eeprom.c	    21  #define LOG_BUFFER_SIZE 40
; ..\eeprom\eeprom.c	    22  
; ..\eeprom\eeprom.c	    23  #define ENTRY_SIZE 8
; ..\eeprom\eeprom.c	    24  
; ..\eeprom\eeprom.c	    25  #define MAX_ENTRIES 5
; ..\eeprom\eeprom.c	    26  
; ..\eeprom\eeprom.c	    27  static uint8 log_buffer[LOG_BUFFER_SIZE];
; ..\eeprom\eeprom.c	    28  
; ..\eeprom\eeprom.c	    29  uint32 NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
; ..\eeprom\eeprom.c	    30  uint16 test_int=0;
; ..\eeprom\eeprom.c	    31  uint8 SecurityErrorFlag = 0;
; ..\eeprom\eeprom.c	    32  uint8 blsDIDF187WriteOnlyOnceFlag = 0;
; ..\eeprom\eeprom.c	    33  uint8 ReadCurrent_session=2;
; ..\eeprom\eeprom.c	    34  extern uint8 appblkIntDefault;
; ..\eeprom\eeprom.c	    35  extern uint8 appblkCpbDefault;
; ..\eeprom\eeprom.c	    36  extern uint8 FailReason;
; ..\eeprom\eeprom.c	    37  extern uint8 SecurityLog_trigger;
; ..\eeprom\eeprom.c	    38  static void EEP_DID_Init(void)
; Function EEP_DID_Init
.L89:
EEP_DID_Init:	.type	func

; ..\eeprom\eeprom.c	    39  {
; ..\eeprom\eeprom.c	    40  	uint16 i,cnt;
; ..\eeprom\eeprom.c	    41  	 for(i=0;i<Nvm_Did_Cfg_Size;i++)
	movh.a	a12,#@his(Nvm_Did_Cfg_Size)
	lea	a12,[a12]@los(Nvm_Did_Cfg_Size)
	ld.bu	d15,[a12]
.L712:
	mov	d8,#0
.L366:
	jeq	d15,#0,.L2
.L713:

; ..\eeprom\eeprom.c	    42       {
; ..\eeprom\eeprom.c	    43  		 /*if did exsist in flash*/
; ..\eeprom\eeprom.c	    44  	     if(Nvm_Did_Cfg[i].u8t_datatype==FLASH_TYPE)
; ..\eeprom\eeprom.c	    45  	     {
; ..\eeprom\eeprom.c	    46  	    	 FlashReadMemory(Nvm_Did_Cfg[i].u8t_buffer,Nvm_Did_Cfg[i].u32t_addr, Nvm_Did_Cfg[i].len);
; ..\eeprom\eeprom.c	    47  
; ..\eeprom\eeprom.c	    48  	     }
; ..\eeprom\eeprom.c	    49  	     else if(Nvm_Did_Cfg[i].u8t_datatype==EEPROM_TYPE)
; ..\eeprom\eeprom.c	    50  	     {
; ..\eeprom\eeprom.c	    51  	    	 cnt=0;
; ..\eeprom\eeprom.c	    52  	    	 NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_BUZY;
	mov	d9,#1
	movh.a	a13,#@his(NvM_JobFinished_Flag)
	lea	a13,[a13]@los(NvM_JobFinished_Flag)
.L3:
	mul	d15,d8,#24
.L714:
	fcall	.cocofun_4
.L715:
	mov.aa	a2,a15
	ld.bu	d15,[+a2]2
.L716:
	jne	d15,#2,.L4
.L717:
	ld.a	a4,[a15]8
.L718:
	ld.w	d4,[a15]4
.L719:
	ld.hu	d5,[a15]12
	call	FlashReadMemory
.L720:
	j	.L5
.L4:
	ld.bu	d15,[a2]
.L721:
	jne	d15,#1,.L6
.L722:
	mov	d10,#0
	st.w	[a13],d9
.L367:

; ..\eeprom\eeprom.c	    53  	    	 NvM_ReadBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
	fcall	.cocofun_2
.L723:
	call	NvM_ReadBlock
.L368:

; ..\eeprom\eeprom.c	    54  	    	 while((cnt<=20)&&(NVM_JOB_STATUS_READ_BUZY==NvM_JobFinished_Flag))
; ..\eeprom\eeprom.c	    55  	    	 {
; ..\eeprom\eeprom.c	    56  	    		 cnt++;
; ..\eeprom\eeprom.c	    57  	    		 test_int=i;
	movh.a	a15,#@his(test_int)
.L724:
	j	.L7
.L8:
	add	d10,#1
	st.h	[a15]@los(test_int),d8
.L725:

; ..\eeprom\eeprom.c	    58  	    		 EEP_MainFunction();
	call	EEP_MainFunction
.L7:
	mov	d15,#20
.L726:
	jlt.u	d15,d10,.L9
.L727:
	ld.w	d15,[a13]
.L728:
	jeq	d15,#1,.L8
.L9:
.L6:
.L5:
	add	d8,#1
.L369:
	extr.u	d8,d8,#0,#16
	ld.bu	d15,[a12]
.L370:
	jlt.u	d8,d15,.L3
.L2:
	mov	d15,#1
	movh.a	a15,#@his(NvM_JobFinished_Flag)
	lea	a15,[a15]@los(NvM_JobFinished_Flag)
.L729:

; ..\eeprom\eeprom.c	    59  	    	 }
; ..\eeprom\eeprom.c	    60  
; ..\eeprom\eeprom.c	    61  	     }
; ..\eeprom\eeprom.c	    62       }
; ..\eeprom\eeprom.c	    63  	 NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_BUZY;
	st.w	[a15],d15
.L730:

; ..\eeprom\eeprom.c	    64  	 NvM_ReadBlock(EEP_SecErrFlag_BLOCK_ID,NULL);
	mov	d4,#84
	mov.a	a4,#0
	call	NvM_ReadBlock
.L731:

; ..\eeprom\eeprom.c	    65  	 while((cnt<=20)&&(NVM_JOB_STATUS_READ_BUZY==NvM_JobFinished_Flag))
	j	.L10
.L11:

; ..\eeprom\eeprom.c	    66  	 {
; ..\eeprom\eeprom.c	    67  		 cnt++;
	add	d10,#1
.L732:

; ..\eeprom\eeprom.c	    68  		 EEP_MainFunction();
	extr.u	d10,d10,#0,#16
	call	EEP_MainFunction
.L10:
	mov	d15,#20
.L371:
	jlt.u	d15,d10,.L12
.L372:
	ld.w	d15,[a15]
.L733:
	jeq	d15,#1,.L11
.L12:

; ..\eeprom\eeprom.c	    69  	 }
; ..\eeprom\eeprom.c	    70  	 SecurityErrorFlag = NvM_SECERR_DATA[0];
	movh.a	a15,#@his(SecurityErrorFlag)
.L734:
	movh.a	a2,#@his(NvM_SECERR_DATA)
.L735:
	ld.bu	d15,[a2]@los(NvM_SECERR_DATA)
.L736:
	st.b	[a15]@los(SecurityErrorFlag),d15
.L737:

; ..\eeprom\eeprom.c	    71  }
	ret
.L350:
	
__EEP_DID_Init_function_end:
	.size	EEP_DID_Init,__EEP_DID_Init_function_end-EEP_DID_Init
.L226:
	; End of function
	
	.sdecl	'.text.eeprom..cocofun_4',code,cluster('.cocofun_4')
	.sect	'.text.eeprom..cocofun_4'
	.align	2
; Function .cocofun_4
.L91:
.cocofun_4:	.type	func
; Function body .cocofun_4, coco_iter:0
	movh.a	a15,#@his(Nvm_Did_Cfg)
	lea	a15,[a15]@los(Nvm_Did_Cfg)
.L781:
	addsc.a	a15,a15,d15,#0
.L782:
	fret
.L246:
	; End of function
	.sdecl	'.text.eeprom..cocofun_2',code,cluster('.cocofun_2')
	.sect	'.text.eeprom..cocofun_2'
	.align	2
; Function .cocofun_2
.L93:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	ld.w	d15,[a15]4
.L419:
	mov.a	a4,#0
.L764:
	extr.u	d4,d15,#0,#16
	fret
.L236:
	; End of function
	.sdecl	'.text.eeprom.EEP_Init',code,cluster('EEP_Init')
	.sect	'.text.eeprom.EEP_Init'
	.align	2
	
	.global	EEP_Init

; ..\eeprom\eeprom.c	    72  
; ..\eeprom\eeprom.c	    73  void EEP_Init(void)
; Function EEP_Init
.L95:
EEP_Init:	.type	func

; ..\eeprom\eeprom.c	    74  {
; ..\eeprom\eeprom.c	    75  	uint8 RequestResultPtr = 0;
; ..\eeprom\eeprom.c	    76  	Fls_17_Pmu_Init(&Fls_17_Pmu_ConfigRoot[0]);
	movh.a	a4,#@his(Fls_17_Pmu_ConfigRoot)
	lea	a4,[a4]@los(Fls_17_Pmu_ConfigRoot)
	call	Fls_17_Pmu_Init
.L530:

; ..\eeprom\eeprom.c	    77  	Fee_Init();
	call	Fee_Init
.L531:

; ..\eeprom\eeprom.c	    78  	NvM_Init();
	call	NvM_Init
.L532:

; ..\eeprom\eeprom.c	    79  	EEP_DID_Init();
	j	EEP_DID_Init
.L298:
	
__EEP_Init_function_end:
	.size	EEP_Init,__EEP_Init_function_end-EEP_Init
.L161:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_MainFunction',code,cluster('EEP_MainFunction')
	.sect	'.text.eeprom.EEP_MainFunction'
	.align	2
	
	.global	EEP_MainFunction

; ..\eeprom\eeprom.c	    80  
; ..\eeprom\eeprom.c	    81  }
; ..\eeprom\eeprom.c	    82  
; ..\eeprom\eeprom.c	    83  void EEP_MainFunction(void)
; Function EEP_MainFunction
.L97:
EEP_MainFunction:	.type	func

; ..\eeprom\eeprom.c	    84  {    
; ..\eeprom\eeprom.c	    85  	if(0!=(NvM_JobFinished_Flag&NVM_JOB_STATUS_BUSY_MASK))
	mov	d0,#16881
	movh.a	a15,#@his(NvM_JobFinished_Flag)
.L537:
	addih	d0,d0,#1
	ld.w	d15,[a15]@los(NvM_JobFinished_Flag)
.L538:
	and	d15,d0
.L539:
	jeq	d15,#0,.L13
.L540:

; ..\eeprom\eeprom.c	    86  	{
; ..\eeprom\eeprom.c	    87  		Fls_17_Pmu_MainFunction();
	call	Fls_17_Pmu_MainFunction
.L541:

; ..\eeprom\eeprom.c	    88  		Fee_MainFunction();
	call	Fee_MainFunction
.L542:

; ..\eeprom\eeprom.c	    89  		NvM_MainFunction();
	j	NvM_MainFunction
.L13:

; ..\eeprom\eeprom.c	    90  	}
; ..\eeprom\eeprom.c	    91  
; ..\eeprom\eeprom.c	    92  }
	ret
.L299:
	
__EEP_MainFunction_function_end:
	.size	EEP_MainFunction,__EEP_MainFunction_function_end-EEP_MainFunction
.L166:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_ReadDID',code,cluster('EEP_ReadDID')
	.sect	'.text.eeprom.EEP_ReadDID'
	.align	2
	
	.global	EEP_ReadDID

; ..\eeprom\eeprom.c	    93  
; ..\eeprom\eeprom.c	    94  uint16 EEP_ReadDID(uint8 *readData,uint16 did)
; Function EEP_ReadDID
.L99:
EEP_ReadDID:	.type	func

; ..\eeprom\eeprom.c	    95  {
; ..\eeprom\eeprom.c	    96  	uint16 length,i,j;
; ..\eeprom\eeprom.c	    97  	for(i=0;i<Nvm_Did_Cfg_Size;i++)
	mov	d0,#0
	fcall	.cocofun_6
	ld.bu	d15,[a15]
.L466:
	jeq	d15,#0,.L14
.L15:

; ..\eeprom\eeprom.c	    98  	{
; ..\eeprom\eeprom.c	    99  	   if(Nvm_Did_Cfg[i].u16t_did==did)
	mul	d15,d0,#24
	fcall	.cocofun_7
.L467:
	addsc.a	a2,a2,d15,#0
.L468:
	ld.hu	d15,[a2]0
.L469:
	jeq	d15,d4,.L16
.L470:
	add	d0,#1
.L375:
	extr.u	d0,d0,#0,#16
	ld.bu	d15,[a15]
.L377:
	jlt.u	d0,d15,.L15
.L14:

; ..\eeprom\eeprom.c	   100  	   {
; ..\eeprom\eeprom.c	   101  		   break;
; ..\eeprom\eeprom.c	   102  	   }
; ..\eeprom\eeprom.c	   103  	}
; ..\eeprom\eeprom.c	   104  
; ..\eeprom\eeprom.c	   105  	if(i>=Nvm_Did_Cfg_Size)
.L16:
	ld.bu	d15,[a15]
.L471:
	jlt.u	d0,d15,.L17
.L472:

; ..\eeprom\eeprom.c	   106  	{
; ..\eeprom\eeprom.c	   107  	   return 0;
; ..\eeprom\eeprom.c	   108  	}
; ..\eeprom\eeprom.c	   109  	else
; ..\eeprom\eeprom.c	   110  	{
; ..\eeprom\eeprom.c	   111  	   length=Nvm_Did_Cfg[i].len;
; ..\eeprom\eeprom.c	   112  	   if(NULL!=Nvm_Did_Cfg[i].readeeprom)
; ..\eeprom\eeprom.c	   113  	   {
; ..\eeprom\eeprom.c	   114  		   Nvm_Did_Cfg[i].readeeprom((uint8)i,readData);
; ..\eeprom\eeprom.c	   115  	   }
; ..\eeprom\eeprom.c	   116  	   else
; ..\eeprom\eeprom.c	   117  	   {
; ..\eeprom\eeprom.c	   118  		   for(j=0;j<length;j++)
; ..\eeprom\eeprom.c	   119  		   {
; ..\eeprom\eeprom.c	   120  			   readData[j]=Nvm_Did_Cfg[i].u8t_buffer[j];
; ..\eeprom\eeprom.c	   121  		   }
; ..\eeprom\eeprom.c	   122  	   }
; ..\eeprom\eeprom.c	   123  	   return length;
; ..\eeprom\eeprom.c	   124  	}
; ..\eeprom\eeprom.c	   125  }
	mov	d2,#0
	ret
.L17:
	mul	d15,d0,#24
.L473:
	fcall	.cocofun_4
.L474:
	lea	a2,[a15]20
	ld.a	a6,[a2]
.L475:
	mov.a	a5,#0
.L476:
	ld.hu	d8,[a15]12
.L379:
	jeq.a	a5,a6,.L19
.L477:
	ld.a	a15,[a2]
.L478:
	extr.u	d4,d0,#0,#8
.L376:
	calli	a15
.L378:
	j	.L20
.L19:
	mov	d15,#0
	j	.L21
.L22:
	ld.a	a2,[a15]8
.L479:
	addsc.a	a2,a2,d15,#0
.L480:
	add	d15,#1
.L380:
	ld.bu	d0,[a2]
.L481:
	extr.u	d15,d15,#0,#16
	st.b	[a4+],d0
.L21:
	jlt.u	d15,d8,.L22
.L20:
	mov	d2,d8
	ret
.L280:
	
__EEP_ReadDID_function_end:
	.size	EEP_ReadDID,__EEP_ReadDID_function_end-EEP_ReadDID
.L146:
	; End of function
	
	.sdecl	'.text.eeprom..cocofun_7',code,cluster('.cocofun_7')
	.sect	'.text.eeprom..cocofun_7'
	.align	2
; Function .cocofun_7
.L101:
.cocofun_7:	.type	func
; Function body .cocofun_7, coco_iter:0
	movh.a	a2,#@his(Nvm_Did_Cfg)
.L374:
	lea	a2,[a2]@los(Nvm_Did_Cfg)
.L797:
	fret
.L261:
	; End of function
	.sdecl	'.text.eeprom..cocofun_6',code,cluster('.cocofun_6')
	.sect	'.text.eeprom..cocofun_6'
	.align	2
; Function .cocofun_6
.L103:
.cocofun_6:	.type	func
; Function body .cocofun_6, coco_iter:0
	movh.a	a15,#@his(Nvm_Did_Cfg_Size)
.L373:
	lea	a15,[a15]@los(Nvm_Did_Cfg_Size)
.L792:
	fret
.L256:
	; End of function
	.sdecl	'.text.eeprom.EEP_ReadCurrent_session',code,cluster('EEP_ReadCurrent_session')
	.sect	'.text.eeprom.EEP_ReadCurrent_session'
	.align	2
	
	.global	EEP_ReadCurrent_session

; ..\eeprom\eeprom.c	   126  //extern Dcm_RunTimeType dcmRunTime;
; ..\eeprom\eeprom.c	   127  uint16 EEP_ReadCurrent_session(uint8 *readData)
; Function EEP_ReadCurrent_session
.L105:
EEP_ReadCurrent_session:	.type	func

; ..\eeprom\eeprom.c	   128  {
; ..\eeprom\eeprom.c	   129  	uint16 length=1;
; ..\eeprom\eeprom.c	   130  	*readData = ReadCurrent_session;
	movh.a	a15,#@his(ReadCurrent_session)
	ld.bu	d15,[a15]@los(ReadCurrent_session)
.L574:
	st.b	[a4],d15
.L575:

; ..\eeprom\eeprom.c	   131  	if(*readData==0x04)
	jne	d15,#4,.L24
.L576:

; ..\eeprom\eeprom.c	   132  	{
; ..\eeprom\eeprom.c	   133  		*readData=3;
	mov	d15,#3
	st.b	[a4],d15
.L24:

; ..\eeprom\eeprom.c	   134  	}
; ..\eeprom\eeprom.c	   135  	return length;
; ..\eeprom\eeprom.c	   136  }
	mov	d2,#1
	ret
.L304:
	
__EEP_ReadCurrent_session_function_end:
	.size	EEP_ReadCurrent_session,__EEP_ReadCurrent_session_function_end-EEP_ReadCurrent_session
.L181:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_WriteDID',code,cluster('EEP_WriteDID')
	.sect	'.text.eeprom.EEP_WriteDID'
	.align	2
	
	.global	EEP_WriteDID

; ..\eeprom\eeprom.c	   137  uint16 EEP_WriteDID(uint8 *writeData,uint16 datalength)
; Function EEP_WriteDID
.L107:
EEP_WriteDID:	.type	func
	mov.aa	a12,a4
.L395:

; ..\eeprom\eeprom.c	   138  {
; ..\eeprom\eeprom.c	   139  	uint16 length,i,j,did;
; ..\eeprom\eeprom.c	   140  	// static boolean blsDIDF187WriteOnlyOnceFlag = FALSE;
; ..\eeprom\eeprom.c	   141  
; ..\eeprom\eeprom.c	   142  	did=(((uint16)writeData[0])<<8)+(uint16)writeData[1];
	ld.bu	d0,[a12]
.L486:
	ld.bu	d15,[a12]1
.L487:

; ..\eeprom\eeprom.c	   143  	for(i=0;i<Nvm_Did_Cfg_Size;i++)
	sha	d0,d0,#8
	movh.a	a2,#@his(Nvm_Did_Cfg_Size)
.L384:
	add	d0,d15
	lea	a2,[a2]@los(Nvm_Did_Cfg_Size)
	ld.bu	d15,[a2]
.L488:
	mov	d1,#0
.L381:
	jeq	d15,#0,.L26
.L27:

; ..\eeprom\eeprom.c	   144  	{
; ..\eeprom\eeprom.c	   145  		if(Nvm_Did_Cfg[i].u16t_did==did)
	mul	d15,d1,#24
	movh.a	a4,#@his(Nvm_Did_Cfg)
	lea	a4,[a4]@los(Nvm_Did_Cfg)
.L489:
	addsc.a	a4,a4,d15,#0
.L490:
	ld.hu	d15,[a4]0
.L491:
	jeq	d15,d0,.L28
.L492:
	add	d1,#1
.L382:
	extr.u	d1,d1,#0,#16
	ld.bu	d15,[a2]
.L386:
	jlt.u	d1,d15,.L27
.L26:

; ..\eeprom\eeprom.c	   146  		{
; ..\eeprom\eeprom.c	   147  		   break;
; ..\eeprom\eeprom.c	   148  		}
; ..\eeprom\eeprom.c	   149  	}
; ..\eeprom\eeprom.c	   150  
; ..\eeprom\eeprom.c	   151  	if(i>=Nvm_Did_Cfg_Size)
.L28:
	ld.bu	d15,[a2]
.L493:
	jlt.u	d1,d15,.L29
.L494:

; ..\eeprom\eeprom.c	   152  	{
; ..\eeprom\eeprom.c	   153  		return EEPROM_OUTOFRANGE;
; ..\eeprom\eeprom.c	   154  	}
; ..\eeprom\eeprom.c	   155  	else
; ..\eeprom\eeprom.c	   156  	{
; ..\eeprom\eeprom.c	   157  		if(FLASH_TYPE==Nvm_Did_Cfg[i].u8t_datatype)
; ..\eeprom\eeprom.c	   158  		{
; ..\eeprom\eeprom.c	   159  			return EEPROM_NOK;
; ..\eeprom\eeprom.c	   160  		}
; ..\eeprom\eeprom.c	   161  		else
; ..\eeprom\eeprom.c	   162  		{
; ..\eeprom\eeprom.c	   163  			length=Nvm_Did_Cfg[i].len;
; ..\eeprom\eeprom.c	   164  			if(datalength>length)
; ..\eeprom\eeprom.c	   165  			{
; ..\eeprom\eeprom.c	   166  				return EEPROM_NOK;
; ..\eeprom\eeprom.c	   167  			}
; ..\eeprom\eeprom.c	   168  			else
; ..\eeprom\eeprom.c	   169  			{
; ..\eeprom\eeprom.c	   170  				if(NULL!=Nvm_Did_Cfg[i].writeeeprom)
; ..\eeprom\eeprom.c	   171  				{
; ..\eeprom\eeprom.c	   172  					Nvm_Did_Cfg[i].writeeeprom((uint8)i,writeData+2);
; ..\eeprom\eeprom.c	   173  				}
; ..\eeprom\eeprom.c	   174  				else
; ..\eeprom\eeprom.c	   175  				{
; ..\eeprom\eeprom.c	   176  					if(0xF187==Nvm_Did_Cfg[i].u16t_did)
; ..\eeprom\eeprom.c	   177  					{
; ..\eeprom\eeprom.c	   178  						if( 03 == blsDIDF187WriteOnlyOnceFlag )
; ..\eeprom\eeprom.c	   179  						{
; ..\eeprom\eeprom.c	   180  							NvM_WriteBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
; ..\eeprom\eeprom.c	   181  							NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F187;
; ..\eeprom\eeprom.c	   182  							blsDIDF187WriteOnlyOnceFlag = 0;
; ..\eeprom\eeprom.c	   183  						}
; ..\eeprom\eeprom.c	   184  						else
; ..\eeprom\eeprom.c	   185  						{
; ..\eeprom\eeprom.c	   186  							return EEPROM_WRITE_MORE_TIME;
; ..\eeprom\eeprom.c	   187  						}
; ..\eeprom\eeprom.c	   188  					}
; ..\eeprom\eeprom.c	   189  					else if(0xF1AA==Nvm_Did_Cfg[i].u16t_did)
; ..\eeprom\eeprom.c	   190  					{
; ..\eeprom\eeprom.c	   191  						NvM_WriteBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
; ..\eeprom\eeprom.c	   192  						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F1AA;
; ..\eeprom\eeprom.c	   193  					}
; ..\eeprom\eeprom.c	   194  					else if(0xF121==Nvm_Did_Cfg[i].u16t_did)
; ..\eeprom\eeprom.c	   195  					{
; ..\eeprom\eeprom.c	   196  						NvM_WriteBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
; ..\eeprom\eeprom.c	   197  						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F121;
; ..\eeprom\eeprom.c	   198  					}
; ..\eeprom\eeprom.c	   199  					else
; ..\eeprom\eeprom.c	   200  					{}
; ..\eeprom\eeprom.c	   201  					for(j=0;j<length;j++)
; ..\eeprom\eeprom.c	   202  					{
; ..\eeprom\eeprom.c	   203  						Nvm_Did_Cfg[i].u8t_buffer[j]=writeData[j+2];
; ..\eeprom\eeprom.c	   204  					}
; ..\eeprom\eeprom.c	   205  				}
; ..\eeprom\eeprom.c	   206  				return EEPROM_OK;
; ..\eeprom\eeprom.c	   207  			}
; ..\eeprom\eeprom.c	   208  		}
; ..\eeprom\eeprom.c	   209  	}
; ..\eeprom\eeprom.c	   210  }
	mov	d2,#2
	ret
.L29:
	mul	d15,d1,#24
.L495:
	fcall	.cocofun_4
.L496:
	ld.bu	d15,[a15]2
.L497:
	jeq	d15,#2,.L31
.L498:
	ld.hu	d8,[a15]12
.L387:
	jge.u	d8,d4,.L32
.L31:
	mov	d2,#1
	ret
.L32:
	lea	a2,[a15]16
	ld.a	a5,[a2]
.L499:
	mov.a	a4,#0
.L500:
	jeq.a	a4,a5,.L34
.L501:
	ld.a	a15,[a2]
.L502:
	extr.u	d4,d1,#0,#8
	lea	a4,[a12]2
.L383:
	calli	a15
.L385:
	j	.L35
.L34:
	ld.hu	d15,[a15]0
.L503:
	mov.u	d0,#61831
.L389:
	jne	d15,d0,.L36
.L504:
	movh.a	a13,#@his(blsDIDF187WriteOnlyOnceFlag)
	lea	a13,[a13]@los(blsDIDF187WriteOnlyOnceFlag)
	ld.bu	d15,[a13]
.L505:
	jne	d15,#3,.L37
.L506:
	fcall	.cocofun_2
.L388:
	call	NvM_WriteBlock
.L507:
	fcall	.cocofun_5
.L390:
	or	d15,#64
	st.w	[a2]@los(NvM_JobFinished_Flag),d15
.L508:
	mov	d15,#0
	st.b	[a13],d15
.L509:
	j	.L38
.L37:
	mov	d2,#254
	ret
.L36:
	ld.hu	d15,[a15]0
.L510:
	mov.u	d0,#61866
.L511:
	jne	d15,d0,.L40
.L512:
	fcall	.cocofun_2
.L391:
	call	NvM_WriteBlock
.L513:
	fcall	.cocofun_5
.L392:
	or	d15,#32
	j	.L41
.L40:
	ld.hu	d15,[a15]0
.L514:
	mov.u	d0,#61729
.L515:
	jne	d15,d0,.L42
.L516:
	fcall	.cocofun_2
.L393:
	call	NvM_WriteBlock
.L517:
	fcall	.cocofun_5
.L394:
	or	d15,#16
.L41:
	st.w	[a2]@los(NvM_JobFinished_Flag),d15
.L42:
.L38:
	mov	d15,#0
	add.a	a12,#2
.L396:
	j	.L43
.L44:
	ld.a	a2,[a15]8
.L518:
	ld.bu	d0,[a12+]
.L519:
	addsc.a	a2,a2,d15,#0
.L520:
	add	d15,#1
.L397:
	st.b	[a2],d0
.L521:
	extr.u	d15,d15,#0,#16
.L43:
	jlt.u	d15,d8,.L44
.L35:
	mov	d2,#0
	ret
.L288:
	
__EEP_WriteDID_function_end:
	.size	EEP_WriteDID,__EEP_WriteDID_function_end-EEP_WriteDID
.L151:
	; End of function
	
	.sdecl	'.text.eeprom..cocofun_5',code,cluster('.cocofun_5')
	.sect	'.text.eeprom..cocofun_5'
	.align	2
; Function .cocofun_5
.L109:
.cocofun_5:	.type	func
; Function body .cocofun_5, coco_iter:0
	movh.a	a2,#@his(NvM_JobFinished_Flag)
	ld.w	d15,[a2]@los(NvM_JobFinished_Flag)
.L787:
	fret
.L251:
	; End of function
	.sdecl	'.text.eeprom.EEP_GetLastSecurityAttemptResult',code,cluster('EEP_GetLastSecurityAttemptResult')
	.sect	'.text.eeprom.EEP_GetLastSecurityAttemptResult'
	.align	2
	
	.global	EEP_GetLastSecurityAttemptResult

; ..\eeprom\eeprom.c	   211  
; ..\eeprom\eeprom.c	   212  uint8 EEP_GetLastSecurityAttemptResult(void)
; Function EEP_GetLastSecurityAttemptResult
.L111:
EEP_GetLastSecurityAttemptResult:	.type	func

; ..\eeprom\eeprom.c	   213  {
; ..\eeprom\eeprom.c	   214  
; ..\eeprom\eeprom.c	   215  	return EEPROM_OK;
; ..\eeprom\eeprom.c	   216  }
	mov	d2,#0
	ret
.L297:
	
__EEP_GetLastSecurityAttemptResult_function_end:
	.size	EEP_GetLastSecurityAttemptResult,__EEP_GetLastSecurityAttemptResult_function_end-EEP_GetLastSecurityAttemptResult
.L156:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_GetPIF_F110',code,cluster('EEP_GetPIF_F110')
	.sect	'.text.eeprom.EEP_GetPIF_F110'
	.align	2
	
	.global	EEP_GetPIF_F110

; ..\eeprom\eeprom.c	   217  /**/
; ..\eeprom\eeprom.c	   218  uint16 EEP_GetPIF_F110(uint8 f110index,uint8* data)
; Function EEP_GetPIF_F110
.L113:
EEP_GetPIF_F110:	.type	func

; ..\eeprom\eeprom.c	   219  {
; ..\eeprom\eeprom.c	   220  
; ..\eeprom\eeprom.c	   221  	uint32 writecounter=(((uint32)Nvm_Did_Cfg[f110index].u8t_buffer[0])<<24)+(((uint32)Nvm_Did_Cfg[f110index].u8t_buffer[1])<<16)+(((uint32)Nvm_Did_Cfg[f110index].u8t_buffer[2])<<8)+(uint32)Nvm_Did_Cfg[f110index].u8t_buffer[3];
	fcall	.cocofun_7
.L402:
	mul	d15,d4,#24
	addsc.a	a15,a2,d15,#0
.L581:
	lea	a5,[a15]8
	fcall	.cocofun_3
.L582:
	ld.a	a15,[a5]
.L583:
	ld.bu	d2,[a15]
.L584:
	sh	d2,d2,#24
.L585:
	add	d0,d2
.L586:
	add	d0,d1
.L398:
	add	d15,d0
.L587:

; ..\eeprom\eeprom.c	   222  	uint16 did;
; ..\eeprom\eeprom.c	   223  	uint8 defaultvalue=0x00;
; ..\eeprom\eeprom.c	   224  	uint16 i,j,length;
; ..\eeprom\eeprom.c	   225  	/*first turn from F111 to F11F,then always from F112 to F11F*/
; ..\eeprom\eeprom.c	   226  	if(writecounter==0)
	jne	d15,#0,.L47
.L588:

; ..\eeprom\eeprom.c	   227  	{
; ..\eeprom\eeprom.c	   228  		for(i=0;i<16;i++)
	mov.a	a15,#15
.L589:

; ..\eeprom\eeprom.c	   229  		{
; ..\eeprom\eeprom.c	   230  			data[i]=defaultvalue;
	mov	d15,#0
.L48:
	st.b	[a4+],d15
	loop	a15,.L48
.L590:
	j	.L49
.L47:

; ..\eeprom\eeprom.c	   231  		}
; ..\eeprom\eeprom.c	   232  	}
; ..\eeprom\eeprom.c	   233  	else
; ..\eeprom\eeprom.c	   234  	{
; ..\eeprom\eeprom.c	   235  		if(writecounter<16)
	mov	d0,#16
.L591:
	jge.u	d15,d0,.L50
.L592:

; ..\eeprom\eeprom.c	   236  		{
; ..\eeprom\eeprom.c	   237  			did=0xF110+(uint16)writecounter;
	mov.u	d0,#61712
.L399:
	add	d15,d0
	j	.L51
.L50:

; ..\eeprom\eeprom.c	   238  		}
; ..\eeprom\eeprom.c	   239  		else
; ..\eeprom\eeprom.c	   240  		{
; ..\eeprom\eeprom.c	   241  			did=0xF112+((writecounter-16)%14);
	add	d0,d15,#-16
	fcall	.cocofun_8
.L51:

; ..\eeprom\eeprom.c	   242  		}
; ..\eeprom\eeprom.c	   243  
; ..\eeprom\eeprom.c	   244  		/*find the did to read from*/
; ..\eeprom\eeprom.c	   245  		for(i=0;i<Nvm_Did_Cfg_Size;i++)
	mov	d0,#0
	fcall	.cocofun_6
	ld.bu	d1,[a15]
.L593:
	jeq	d1,#0,.L52
.L53:

; ..\eeprom\eeprom.c	   246  		{
; ..\eeprom\eeprom.c	   247  		   if(Nvm_Did_Cfg[i].u16t_did==did)
	mul	d1,d0,#24
	addsc.a	a5,a2,d1,#0
.L594:
	ld.hu	d1,[a5]0
.L595:
	jeq	d15,d1,.L54
.L596:
	add	d0,#1
.L404:
	extr.u	d0,d0,#0,#16
	ld.bu	d1,[a15]
.L405:
	jlt.u	d0,d1,.L53
.L52:

; ..\eeprom\eeprom.c	   248  		   {
; ..\eeprom\eeprom.c	   249  			   break;
; ..\eeprom\eeprom.c	   250  		   }
; ..\eeprom\eeprom.c	   251  		}
; ..\eeprom\eeprom.c	   252  
; ..\eeprom\eeprom.c	   253  		/*did not found*/
; ..\eeprom\eeprom.c	   254  	   if(i>=Nvm_Did_Cfg_Size) return EEPROM_NOK;
.L54:
	ld.bu	d15,[a15]
.L403:
	jlt.u	d0,d15,.L55
.L597:

; ..\eeprom\eeprom.c	   255  
; ..\eeprom\eeprom.c	   256  	   /*did found,read data*/
; ..\eeprom\eeprom.c	   257  	   length=Nvm_Did_Cfg[i].len;
; ..\eeprom\eeprom.c	   258  	   for(j=0;j<length;j++)
; ..\eeprom\eeprom.c	   259  	   {
; ..\eeprom\eeprom.c	   260  		   data[j] = Nvm_Did_Cfg[i].u8t_buffer[j];
; ..\eeprom\eeprom.c	   261  	   }
; ..\eeprom\eeprom.c	   262  	}
; ..\eeprom\eeprom.c	   263  	return EEPROM_OK;
; ..\eeprom\eeprom.c	   264  }
	mov	d2,#1
	ret
.L55:
	mul	d15,d0,#24
	addsc.a	a15,a2,d15,#0
.L598:
	mov	d15,#0
.L407:
	ld.hu	d0,[a15]12
.L406:
	j	.L57
.L58:
	ld.a	a2,[a15]8
.L599:
	addsc.a	a2,a2,d15,#0
.L600:
	add	d15,#1
.L408:
	ld.bu	d1,[a2]
.L601:
	extr.u	d15,d15,#0,#16
	st.b	[a4+],d1
.L57:
	jlt.u	d15,d0,.L58
.L49:
	mov	d2,#0
	ret
.L306:
	
__EEP_GetPIF_F110_function_end:
	.size	EEP_GetPIF_F110,__EEP_GetPIF_F110_function_end-EEP_GetPIF_F110
.L186:
	; End of function
	
	.sdecl	'.text.eeprom..cocofun_8',code,cluster('.cocofun_8')
	.sect	'.text.eeprom..cocofun_8'
	.align	2
; Function .cocofun_8
.L115:
.cocofun_8:	.type	func
; Function body .cocofun_8, coco_iter:0
	mov	d15,#14
.L400:
	div.u	e0,d0,d15
.L802:
	mov.u	d15,#61714
.L401:
	add	d15,d1
	fret
.L266:
	; End of function
	.sdecl	'.text.eeprom..cocofun_3',code,cluster('.cocofun_3')
	.sect	'.text.eeprom..cocofun_3'
	.align	2
; Function .cocofun_3
.L117:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:0
	ld.a	a15,[a5]
.L769:
	ld.bu	d15,[a15]1
.L770:
	ld.a	a15,[a5]
.L771:
	sh	d0,d15,#16
.L772:
	ld.bu	d15,[a15]2
.L773:
	ld.a	a15,[a5]
.L774:
	sh	d1,d15,#8
.L775:
	ld.bu	d15,[a15]3
.L776:
	fret
.L241:
	; End of function
	.sdecl	'.text.eeprom.EEP_SetPIF_F110',code,cluster('EEP_SetPIF_F110')
	.sect	'.text.eeprom.EEP_SetPIF_F110'
	.align	2
	
	.global	EEP_SetPIF_F110

; ..\eeprom\eeprom.c	   265  
; ..\eeprom\eeprom.c	   266  uint16 EEP_SetPIF_F110(uint8 f110index,uint8* data)
; Function EEP_SetPIF_F110
.L119:
EEP_SetPIF_F110:	.type	func

; ..\eeprom\eeprom.c	   267  {
; ..\eeprom\eeprom.c	   268  	uint8 pifcntindex=f110index;/*EEP_PIFCNT_BLOCK_ID¶ÔÓ¦µÄindex*/
; ..\eeprom\eeprom.c	   269  	uint32 writecounter=(((uint32)Nvm_Did_Cfg[pifcntindex].u8t_buffer[0])<<24)+(((uint32)Nvm_Did_Cfg[pifcntindex].u8t_buffer[1])<<16)+(((uint32)Nvm_Did_Cfg[pifcntindex].u8t_buffer[2])<<8)+(uint32)Nvm_Did_Cfg[pifcntindex].u8t_buffer[3];
	mul	d15,d4,#24
	movh.a	a13,#@his(Nvm_Did_Cfg)
	lea	a13,[a13]@los(Nvm_Did_Cfg)
.L606:
	mov.aa	a12,a4
.L420:
	addsc.a	a4,a13,d15,#0
.L409:
	lea	a5,[a4]8
	fcall	.cocofun_3
.L607:
	ld.a	a2,[a5]
.L608:
	ld.bu	d2,[a2]
.L609:

; ..\eeprom\eeprom.c	   270  	uint16 did;
; ..\eeprom\eeprom.c	   271  	uint8 defaultvalue=0xFF;
; ..\eeprom\eeprom.c	   272  	uint16 i,j,length;
; ..\eeprom\eeprom.c	   273  	/*first turn from F111 to F11F,then always from F112 to F11F*/
; ..\eeprom\eeprom.c	   274  
; ..\eeprom\eeprom.c	   275  	/*increase the counter saved in F110,then store back to f110*/
; ..\eeprom\eeprom.c	   276  	writecounter++;
; ..\eeprom\eeprom.c	   277  	Nvm_Did_Cfg[pifcntindex].u8t_buffer[0]=(writecounter&0xFF000000)>>24;
	ld.a	a2,[a5]
.L610:
	sh	d2,d2,#24
.L611:
	add	d0,d2
.L612:
	add	d0,d1
.L613:
	add	d0,d15
.L614:
	add	d8,d0,#1
.L422:
	sh	d0,d8,#-24
	st.b	[a2],d0
.L615:

; ..\eeprom\eeprom.c	   278  	Nvm_Did_Cfg[pifcntindex].u8t_buffer[1]=(writecounter&0x00FF0000)>>16;
	sh	d0,d8,#-16
.L616:
	ld.a	a2,[a5]
.L617:
	st.b	[+a2]1,d0
.L618:

; ..\eeprom\eeprom.c	   279  	Nvm_Did_Cfg[pifcntindex].u8t_buffer[2]=(writecounter&0x0000FF00)>>8;
	sh	d0,d8,#-8
.L619:
	ld.a	a15,[a5]
.L620:
	st.b	[+a15]2,d0
.L621:

; ..\eeprom\eeprom.c	   280  	Nvm_Did_Cfg[pifcntindex].u8t_buffer[3]=writecounter&0x000000FF;
	ld.a	a15,[a5]
.L622:
	st.b	[+a15]3,d8
.L623:

; ..\eeprom\eeprom.c	   281  	/*store nvm if exsist*/
; ..\eeprom\eeprom.c	   282  	NvM_WriteBlock((uint16)Nvm_Did_Cfg[pifcntindex].u32t_addr,NULL);
	ld.w	d15,[a4]4
.L624:
	mov.a	a4,#0
.L625:
	extr.u	d4,d15,#0,#16
	call	NvM_WriteBlock
.L410:

; ..\eeprom\eeprom.c	   283  	NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F110;
	movh.a	a14,#@his(NvM_JobFinished_Flag)
	lea	a14,[a14]@los(NvM_JobFinished_Flag)
	ld.w	d15,[a14]
.L626:
	or	d15,#128
	st.w	[a14],d15
.L627:

; ..\eeprom\eeprom.c	   284  		if(writecounter<16)
	mov	d15,#16
.L628:
	jge.u	d8,d15,.L60
.L629:

; ..\eeprom\eeprom.c	   285  		{
; ..\eeprom\eeprom.c	   286  			did=0xF110+(uint16)writecounter;
	mov.u	d15,#61712
.L411:
	add	d15,d8
	j	.L61
.L60:

; ..\eeprom\eeprom.c	   287  		}
; ..\eeprom\eeprom.c	   288  		else
; ..\eeprom\eeprom.c	   289  		{
; ..\eeprom\eeprom.c	   290  			did=0xF112+((writecounter-16)%14);
	add	d0,d8,#-16
	fcall	.cocofun_8
.L61:

; ..\eeprom\eeprom.c	   291  		}
; ..\eeprom\eeprom.c	   292  
; ..\eeprom\eeprom.c	   293  		/*find the did to write to*/
; ..\eeprom\eeprom.c	   294  		for(i=0;i<Nvm_Did_Cfg_Size;i++)
	mov	d0,#0
	fcall	.cocofun_6
	ld.bu	d1,[a15]
.L630:
	jeq	d1,#0,.L62
.L63:

; ..\eeprom\eeprom.c	   295  		{
; ..\eeprom\eeprom.c	   296  		   if(Nvm_Did_Cfg[i].u16t_did==did)
	mul	d1,d0,#24
	addsc.a	a2,a13,d1,#0
.L631:
	ld.hu	d1,[a2]0
.L632:
	jeq	d15,d1,.L64
.L633:
	add	d0,#1
.L413:
	extr.u	d0,d0,#0,#16
	ld.bu	d1,[a15]
.L414:
	jlt.u	d0,d1,.L63
.L62:

; ..\eeprom\eeprom.c	   297  		   {
; ..\eeprom\eeprom.c	   298  			   break;
; ..\eeprom\eeprom.c	   299  		   }
; ..\eeprom\eeprom.c	   300  		}
; ..\eeprom\eeprom.c	   301  
; ..\eeprom\eeprom.c	   302  		/*did not found*/
; ..\eeprom\eeprom.c	   303  	   if(i>=Nvm_Did_Cfg_Size) return EEPROM_NOK;
.L64:
	ld.bu	d15,[a15]
.L412:
	jlt.u	d0,d15,.L65
.L634:

; ..\eeprom\eeprom.c	   304  
; ..\eeprom\eeprom.c	   305  	   /*did found,read data*/
; ..\eeprom\eeprom.c	   306  	   length=Nvm_Did_Cfg[i].len;
; ..\eeprom\eeprom.c	   307  	   for(j=0;j<length;j++)
; ..\eeprom\eeprom.c	   308  	   {
; ..\eeprom\eeprom.c	   309  		   Nvm_Did_Cfg[i].u8t_buffer[j] = data[j];
; ..\eeprom\eeprom.c	   310  	   }
; ..\eeprom\eeprom.c	   311  	   /*store nvm if exsist*/
; ..\eeprom\eeprom.c	   312  	   NvM_WriteBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
; ..\eeprom\eeprom.c	   313  
; ..\eeprom\eeprom.c	   314  	   NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF;
; ..\eeprom\eeprom.c	   315  	return EEPROM_OK;
; ..\eeprom\eeprom.c	   316  }
	mov	d2,#1
	ret
.L65:
	mul	d15,d0,#24
	addsc.a	a15,a13,d15,#0
.L635:
	mov	d15,#0
.L416:
	ld.hu	d0,[a15]12
.L415:
	j	.L67
.L68:
	ld.a	a2,[a15]8
.L636:
	ld.bu	d1,[a12+]
.L637:
	addsc.a	a2,a2,d15,#0
.L638:
	add	d15,#1
.L417:
	st.b	[a2],d1
.L639:
	extr.u	d15,d15,#0,#16
.L67:
	jlt.u	d15,d0,.L68
.L640:
	fcall	.cocofun_2
.L418:
	call	NvM_WriteBlock
.L421:
	ld.w	d15,[a14]
.L641:
	mov	d2,#0
.L642:
	or	d15,d15,#256
	st.w	[a14],d15
.L643:
	ret
.L316:
	
__EEP_SetPIF_F110_function_end:
	.size	EEP_SetPIF_F110,__EEP_SetPIF_F110_function_end-EEP_SetPIF_F110
.L191:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_GetSWVerification_F100',code,cluster('EEP_GetSWVerification_F100')
	.sect	'.text.eeprom.EEP_GetSWVerification_F100'
	.align	2
	
	.global	EEP_GetSWVerification_F100

; ..\eeprom\eeprom.c	   317  
; ..\eeprom\eeprom.c	   318  uint16 EEP_GetSWVerification_F100(uint8 f100index,uint8* data)
; Function EEP_GetSWVerification_F100
.L121:
EEP_GetSWVerification_F100:	.type	func
	mov	d8,d4
	mov.aa	a12,a4
.L424:

; ..\eeprom\eeprom.c	   319  {
; ..\eeprom\eeprom.c	   320      uint8 i;
; ..\eeprom\eeprom.c	   321  	FL_CheckSWVerification(data);
	mov.aa	a4,a12
	call	FL_CheckSWVerification
.L423:

; ..\eeprom\eeprom.c	   322  
; ..\eeprom\eeprom.c	   323  	for(i=0;i<Nvm_Did_Cfg[f100index].len;i++)
; ..\eeprom\eeprom.c	   324  	{
; ..\eeprom\eeprom.c	   325  		Nvm_Did_Cfg[f100index].u8t_buffer[i]=data[i];
	mul	d0,d8,#24
	movh.a	a15,#@his(Nvm_Did_Cfg)
	lea	a15,[a15]@los(Nvm_Did_Cfg)
.L648:
	addsc.a	a15,a15,d0,#0
.L649:
	mov	d15,#0
.L425:
	ld.hu	d0,[a15]12
.L650:
	jeq	d0,#0,.L70
.L71:
	addsc.a	a4,a12,d15,#0
.L651:
	ld.a	a2,[a15]8
.L652:
	ld.bu	d0,[a4]
.L653:
	addsc.a	a2,a2,d15,#0
.L654:
	add	d15,#1
.L426:
	st.b	[a2],d0
.L655:
	extr.u	d15,d15,#0,#8
.L427:
	ld.hu	d0,[a15]12
.L656:
	jlt.u	d15,d0,.L71
.L70:

; ..\eeprom\eeprom.c	   326  	}
; ..\eeprom\eeprom.c	   327  	return EEPROM_OK;
; ..\eeprom\eeprom.c	   328  }
	mov	d2,#0
	ret
.L324:
	
__EEP_GetSWVerification_F100_function_end:
	.size	EEP_GetSWVerification_F100,__EEP_GetSWVerification_F100_function_end-EEP_GetSWVerification_F100
.L196:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_GetECUProgramState_F1A3',code,cluster('EEP_GetECUProgramState_F1A3')
	.sect	'.text.eeprom.EEP_GetECUProgramState_F1A3'
	.align	2
	
	.global	EEP_GetECUProgramState_F1A3

; ..\eeprom\eeprom.c	   329  /*byte0 bit7 is app present                       reference to integrity bit0
; ..\eeprom\eeprom.c	   330   *byte0 bit6 is cal present                       reference to integrity bit2
; ..\eeprom\eeprom.c	   331   *byte1 bit7 is hardware/app compatible           not definite yet by 2020-12-5
; ..\eeprom\eeprom.c	   332   *byte1 bit6 is cal/app compatible                not definite yet by 2020-12-5
; ..\eeprom\eeprom.c	   333   *byte1 bit5 is eyeq/app compatible               not definite yet by 2020-12-5
; ..\eeprom\eeprom.c	   334   *byte2 bit2 is NVRAM Memory fail                 not definite yet by 2020-12-5
; ..\eeprom\eeprom.c	   335   * */
; ..\eeprom\eeprom.c	   336  uint16 EEP_GetECUProgramState_F1A3(uint8 f1a3index,uint8* data)
; Function EEP_GetECUProgramState_F1A3
.L123:
EEP_GetECUProgramState_F1A3:	.type	func
	sub.a	a10,#8
.L428:
	mov	d9,d4
	mov.aa	a15,a4
.L431:

; ..\eeprom\eeprom.c	   337  {
; ..\eeprom\eeprom.c	   338      uint8 i;
; ..\eeprom\eeprom.c	   339      uint8 isPresent=0;
; ..\eeprom\eeprom.c	   340      uint8 isCompatible=0;
	mov	d15,#0
	st.b	[a10],d15
.L661:

; ..\eeprom\eeprom.c	   341      uint8 isMemoryFail=0;
; ..\eeprom\eeprom.c	   342  
; ..\eeprom\eeprom.c	   343      isPresent=FL_CheckProgramIntegrity();
	call	FL_CheckProgramIntegrity
.L429:

; ..\eeprom\eeprom.c	   344      data[0]=0;
; ..\eeprom\eeprom.c	   345      data[0]|=((isPresent&APP_INTEGRITY_ERR)<<7);
; ..\eeprom\eeprom.c	   346      data[0]|=((isPresent&CAL_INTEGRITY_ERR)<<5);
; ..\eeprom\eeprom.c	   347      data[1]=0;
	mov	d15,#0
	st.b	[a15]1,d15
.L662:

; ..\eeprom\eeprom.c	   348      FL_CheckProgramDependencies(&isCompatible);
	and	d15,d2,#4
	mov.aa	a4,a10
.L663:
	sha	d8,d15,#5
.L664:
	and	d15,d2,#1
.L665:
	sha	d15,#7
.L666:
	or	d15,d8
	st.b	[a15],d15
.L667:
	call	FL_CheckProgramDependencies
.L432:

; ..\eeprom\eeprom.c	   349      data[1]|=((isPresent&CAL_APP_CPB_ERR)<<5);
	ld.bu	d15,[a15]1
.L668:

; ..\eeprom\eeprom.c	   350      data[2]=0;
; ..\eeprom\eeprom.c	   351  	for(i=0;i<Nvm_Did_Cfg[f1a3index].len;i++)
; ..\eeprom\eeprom.c	   352  	{
; ..\eeprom\eeprom.c	   353  		Nvm_Did_Cfg[f1a3index].u8t_buffer[i]=data[i];
	mul	d0,d9,#24
.L669:
	or	d15,d8
	st.b	[a15]1,d15
.L670:
	mov	d15,#0
	st.b	[a15]2,d15
.L430:
	fcall	.cocofun_7
.L433:
	addsc.a	a2,a2,d0,#0
.L671:
	ld.hu	d0,[a2]12
.L672:
	jeq	d0,#0,.L73
.L74:
	addsc.a	a5,a15,d15,#0
.L673:
	ld.a	a4,[a2]8
.L674:
	ld.bu	d0,[a5]
.L675:
	addsc.a	a4,a4,d15,#0
.L676:
	add	d15,#1
.L434:
	st.b	[a4],d0
.L677:
	extr.u	d15,d15,#0,#8
.L435:
	ld.hu	d0,[a2]12
.L678:
	jlt.u	d15,d0,.L74
.L73:

; ..\eeprom\eeprom.c	   354  	}
; ..\eeprom\eeprom.c	   355  	return EEPROM_OK;
; ..\eeprom\eeprom.c	   356  }
	mov	d2,#0
	ret
.L328:
	
__EEP_GetECUProgramState_F1A3_function_end:
	.size	EEP_GetECUProgramState_F1A3,__EEP_GetECUProgramState_F1A3_function_end-EEP_GetECUProgramState_F1A3
.L201:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_GetSoftwareValidFlag_AFFF',code,cluster('EEP_GetSoftwareValidFlag_AFFF')
	.sect	'.text.eeprom.EEP_GetSoftwareValidFlag_AFFF'
	.align	2
	
	.global	EEP_GetSoftwareValidFlag_AFFF

; ..\eeprom\eeprom.c	   357  
; ..\eeprom\eeprom.c	   358  uint16 EEP_GetSoftwareValidFlag_AFFF(uint8 afffindex,uint8* data)
; Function EEP_GetSoftwareValidFlag_AFFF
.L125:
EEP_GetSoftwareValidFlag_AFFF:	.type	func
	sub.a	a10,#8
.L436:
	mov.aa	a15,a4
.L439:

; ..\eeprom\eeprom.c	   359  {
; ..\eeprom\eeprom.c	   360  	uint8 isCompatible=0;
	mov	d15,#0
	st.b	[a10],d15
.L683:

; ..\eeprom\eeprom.c	   361  	FL_CheckProgramDependencies(&isCompatible);
	mov.aa	a4,a10
.L438:
	call	FL_CheckProgramDependencies
.L437:

; ..\eeprom\eeprom.c	   362  	data[0]=(isCompatible>0? 0:1);
	ld.bu	d15,[a10]
.L684:

; ..\eeprom\eeprom.c	   363  	return EEPROM_OK;
	mov	d2,#0
.L685:
	eq	d15,d15,#0
	st.b	[a15],d15
.L686:

; ..\eeprom\eeprom.c	   364  }
	ret
.L334:
	
__EEP_GetSoftwareValidFlag_AFFF_function_end:
	.size	EEP_GetSoftwareValidFlag_AFFF,__EEP_GetSoftwareValidFlag_AFFF_function_end-EEP_GetSoftwareValidFlag_AFFF
.L206:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_GetSoftwareIntegrityStatus_AFFD',code,cluster('EEP_GetSoftwareIntegrityStatus_AFFD')
	.sect	'.text.eeprom.EEP_GetSoftwareIntegrityStatus_AFFD'
	.align	2
	
	.global	EEP_GetSoftwareIntegrityStatus_AFFD

; ..\eeprom\eeprom.c	   365  
; ..\eeprom\eeprom.c	   366  uint16 EEP_GetSoftwareIntegrityStatus_AFFD(uint8 affdindex,uint8* data)
; Function EEP_GetSoftwareIntegrityStatus_AFFD
.L127:
EEP_GetSoftwareIntegrityStatus_AFFD:	.type	func
	mov.aa	a15,a4
.L441:

; ..\eeprom\eeprom.c	   367  {
; ..\eeprom\eeprom.c	   368  	uint8 isPresent=0;
; ..\eeprom\eeprom.c	   369  	
; ..\eeprom\eeprom.c	   370  	isPresent=FL_CheckProgramIntegrity();
	call	FL_CheckProgramIntegrity
.L440:

; ..\eeprom\eeprom.c	   371  	// if( 0x00u != isPresent )
; ..\eeprom\eeprom.c	   372  	if(appblkIntDefault)
	movh.a	a2,#@his(appblkIntDefault)
	ld.bu	d15,[a2]@los(appblkIntDefault)
.L691:

; ..\eeprom\eeprom.c	   373  	{
; ..\eeprom\eeprom.c	   374  		isPresent = 0xFFu;
; ..\eeprom\eeprom.c	   375  	}
; ..\eeprom\eeprom.c	   376  	data[0]=isPresent;
	seln	d2,d15,d2,#255
	st.b	[a15],d2
.L692:

; ..\eeprom\eeprom.c	   377  	
; ..\eeprom\eeprom.c	   378  	return EEPROM_OK;
; ..\eeprom\eeprom.c	   379  }
	mov	d2,#0
	ret
.L338:
	
__EEP_GetSoftwareIntegrityStatus_AFFD_function_end:
	.size	EEP_GetSoftwareIntegrityStatus_AFFD,__EEP_GetSoftwareIntegrityStatus_AFFD_function_end-EEP_GetSoftwareIntegrityStatus_AFFD
.L211:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_GetSoftwareCompatibilityStatus_AFFE',code,cluster('EEP_GetSoftwareCompatibilityStatus_AFFE')
	.sect	'.text.eeprom.EEP_GetSoftwareCompatibilityStatus_AFFE'
	.align	2
	
	.global	EEP_GetSoftwareCompatibilityStatus_AFFE

; ..\eeprom\eeprom.c	   380  
; ..\eeprom\eeprom.c	   381  uint16 EEP_GetSoftwareCompatibilityStatus_AFFE(uint8 affeindex,uint8* data)
; Function EEP_GetSoftwareCompatibilityStatus_AFFE
.L129:
EEP_GetSoftwareCompatibilityStatus_AFFE:	.type	func
	sub.a	a10,#8
.L442:
	mov.aa	a15,a4
.L445:

; ..\eeprom\eeprom.c	   382  {
; ..\eeprom\eeprom.c	   383  	uint8 isCompatible=0;
	mov	d15,#0
	st.b	[a10],d15
.L697:

; ..\eeprom\eeprom.c	   384  	
; ..\eeprom\eeprom.c	   385  	FL_CheckProgramDependencies(&isCompatible);
	mov.aa	a4,a10
.L444:
	call	FL_CheckProgramDependencies
.L443:

; ..\eeprom\eeprom.c	   386  	// if( 0x00u != isCompatible )
; ..\eeprom\eeprom.c	   387  	if(appblkCpbDefault)
	movh.a	a2,#@his(appblkCpbDefault)
	ld.bu	d15,[a2]@los(appblkCpbDefault)
.L698:
	jeq	d15,#0,.L79
.L699:

; ..\eeprom\eeprom.c	   388  	{
; ..\eeprom\eeprom.c	   389  		isCompatible = 0xFFu;
	mov	d15,#255
	st.b	[a10],d15
.L79:

; ..\eeprom\eeprom.c	   390  	}
; ..\eeprom\eeprom.c	   391  	data[0]=isCompatible;
	ld.bu	d15,[a10]
.L700:
	st.b	[a15],d15
.L701:

; ..\eeprom\eeprom.c	   392  	
; ..\eeprom\eeprom.c	   393  	return EEPROM_OK;
; ..\eeprom\eeprom.c	   394  }
	mov	d2,#0
	ret
.L342:
	
__EEP_GetSoftwareCompatibilityStatus_AFFE_function_end:
	.size	EEP_GetSoftwareCompatibilityStatus_AFFE,__EEP_GetSoftwareCompatibilityStatus_AFFE_function_end-EEP_GetSoftwareCompatibilityStatus_AFFE
.L216:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_GetProgrammingCounter_AFFC',code,cluster('EEP_GetProgrammingCounter_AFFC')
	.sect	'.text.eeprom.EEP_GetProgrammingCounter_AFFC'
	.align	2
	
	.global	EEP_GetProgrammingCounter_AFFC

; ..\eeprom\eeprom.c	   395  uint16 EEP_GetProgrammingCounter_AFFC(uint8 affcindex,uint8* data)
; Function EEP_GetProgrammingCounter_AFFC
.L131:
EEP_GetProgrammingCounter_AFFC:	.type	func
	mov.aa	a15,a4
.L447:

; ..\eeprom\eeprom.c	   396  {
; ..\eeprom\eeprom.c	   397  	uint16 counter=0;
; ..\eeprom\eeprom.c	   398  	counter=FL_CheckProgramCounter();
	call	FL_CheckProgramCounter
.L446:

; ..\eeprom\eeprom.c	   399  	data[0]=(uint8)(counter>>8);
	sha	d15,d2,#-8
	st.b	[a15],d15
.L706:

; ..\eeprom\eeprom.c	   400  	data[1]=(uint8)(counter&0xFF);
	st.b	[a15]1,d2
.L707:

; ..\eeprom\eeprom.c	   401  	return EEPROM_OK;
; ..\eeprom\eeprom.c	   402  }
	mov	d2,#0
	ret
.L346:
	
__EEP_GetProgrammingCounter_AFFC_function_end:
	.size	EEP_GetProgrammingCounter_AFFC,__EEP_GetProgrammingCounter_AFFC_function_end-EEP_GetProgrammingCounter_AFFC
.L221:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_SetSecErrFlag',code,cluster('EEP_SetSecErrFlag')
	.sect	'.text.eeprom.EEP_SetSecErrFlag'
	.align	2
	
	.global	EEP_SetSecErrFlag

; ..\eeprom\eeprom.c	   403  
; ..\eeprom\eeprom.c	   404  uint16 EEP_SetSecErrFlag(uint8* data)
; Function EEP_SetSecErrFlag
.L133:
EEP_SetSecErrFlag:	.type	func

; ..\eeprom\eeprom.c	   405  {
; ..\eeprom\eeprom.c	   406  	NvM_SECERR_DATA[0] = *data;
	movh.a	a15,#@his(NvM_SECERR_DATA)
.L547:
	ld.bu	d15,[a4]
.L548:
	st.b	[a15]@los(NvM_SECERR_DATA),d15
.L549:

; ..\eeprom\eeprom.c	   407  	NvM_WriteBlock(EEP_SecErrFlag_BLOCK_ID,NULL);
	mov	d4,#84
	mov.a	a4,#0
.L448:
	call	NvM_WriteBlock
.L550:

; ..\eeprom\eeprom.c	   408  	NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_SECERR;
	movh.a	a15,#@his(NvM_JobFinished_Flag)
	ld.w	d15,[a15]@los(NvM_JobFinished_Flag)
.L551:

; ..\eeprom\eeprom.c	   409   	return EEPROM_OK;
	mov	d2,#0
.L552:
	insert	d15,d15,#1,#14,#1
	st.w	[a15]@los(NvM_JobFinished_Flag),d15
.L553:

; ..\eeprom\eeprom.c	   410  }
	ret
.L300:
	
__EEP_SetSecErrFlag_function_end:
	.size	EEP_SetSecErrFlag,__EEP_SetSecErrFlag_function_end-EEP_SetSecErrFlag
.L171:
	; End of function
	
	.sdecl	'.text.eeprom.log_failure',code,cluster('log_failure')
	.sect	'.text.eeprom.log_failure'
	.align	2
	
	.global	log_failure

; ..\eeprom\eeprom.c	   411  
; ..\eeprom\eeprom.c	   412  
; ..\eeprom\eeprom.c	   413  void log_failure(uint8 year, uint8 month, uint8 day,uint8 hour, uint8 minute, uint8 second,uint8 reason)
; Function log_failure
.L135:
log_failure:	.type	func
	sub.a	a10,#8
.L449:
	ld.bu	d0,[a10]8
.L452:
	ld.bu	d1,[a10]12
.L454:
	ld.bu	d2,[a10]16
.L455:

; ..\eeprom\eeprom.c	   414  {
; ..\eeprom\eeprom.c	   415  	uint8 cnt = 0;
; ..\eeprom\eeprom.c	   416  	uint8 new_entry[ENTRY_SIZE] = {year, month, day, hour, minute, second, reason, 0x00 };//0x00->Reserved
	mov	d15,#0
	st.b	[a10]1,d15
.L456:
	st.b	[a10]2,d15
	st.b	[a10]3,d15
	st.b	[a10]4,d15
	st.b	[a10]5,d15
	st.b	[a10]6,d15
	st.b	[a10]7,d15
.L742:
	st.b	[a10]1,d5
.L743:
	st.b	[a10]2,d6
.L744:
	st.b	[a10]3,d7
.L745:
	st.b	[a10]4,d0
.L746:
	st.b	[a10]5,d1
.L747:
	st.b	[a10]6,d2
.L748:

; ..\eeprom\eeprom.c	   417  
; ..\eeprom\eeprom.c	   418  	NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_BUZY;
	movh.a	a15,#@his(NvM_JobFinished_Flag)
	lea	a15,[a15]@los(NvM_JobFinished_Flag)
.L749:
	mov	d0,#1
	st.w	[a15],d0
.L453:

; ..\eeprom\eeprom.c	   419  	NvM_ReadBlock(EEP_SecurityLog_BLOCK_ID,NULL);
	st.b	[a10],d4
.L750:
	mov	d4,#98
	mov.a	a4,#0
.L451:
	call	NvM_ReadBlock
.L450:

; ..\eeprom\eeprom.c	   420  	while((cnt<=20)&&(NVM_JOB_STATUS_READ_BUZY==NvM_JobFinished_Flag))
	j	.L83
.L84:

; ..\eeprom\eeprom.c	   421  	{
; ..\eeprom\eeprom.c	   422  		cnt++;
; ..\eeprom\eeprom.c	   423  		EEP_MainFunction();
	add	d15,#1
	call	EEP_MainFunction
.L83:
	mov	d0,#20
.L751:
	jlt.u	d0,d15,.L85
.L752:
	ld.w	d0,[a15]
.L753:
	jeq	d0,#1,.L84
.L85:

; ..\eeprom\eeprom.c	   424  	}
; ..\eeprom\eeprom.c	   425  
; ..\eeprom\eeprom.c	   426  	memcpy(log_buffer, NvM_SECLOG_DATA, LOG_BUFFER_SIZE);
	movh.a	a15,#@his(log_buffer)
	lea	a15,[a15]@los(log_buffer)
.L754:
	movh.a	a5,#@his(NvM_SECLOG_DATA)
	lea	a5,[a5]@los(NvM_SECLOG_DATA)
.L755:
	mov	d4,#40
	mov.aa	a4,a15
	call	memcpy
.L756:

; ..\eeprom\eeprom.c	   427  
; ..\eeprom\eeprom.c	   428  	memmove(log_buffer + ENTRY_SIZE, log_buffer, LOG_BUFFER_SIZE - ENTRY_SIZE);
	lea	a4,[a15]8
.L757:
	mov	d4,#32
	mov.aa	a5,a15
	call	memmove
.L758:

; ..\eeprom\eeprom.c	   429  
; ..\eeprom\eeprom.c	   430  	memcpy(log_buffer, new_entry, ENTRY_SIZE);
	mov.aa	a5,a10
.L759:
	mov	d4,#8
	mov.aa	a4,a15
	j	memcpy
.L354:
	
__log_failure_function_end:
	.size	log_failure,__log_failure_function_end-log_failure
.L231:
	; End of function
	
	.sdecl	'.text.eeprom.EEP_SaveSecLog',code,cluster('EEP_SaveSecLog')
	.sect	'.text.eeprom.EEP_SaveSecLog'
	.align	2
	
	.global	EEP_SaveSecLog

; ..\eeprom\eeprom.c	   431  
; ..\eeprom\eeprom.c	   432  }
; ..\eeprom\eeprom.c	   433  
; ..\eeprom\eeprom.c	   434  uint16 EEP_SaveSecLog(void)
; Function EEP_SaveSecLog
.L137:
EEP_SaveSecLog:	.type	func

; ..\eeprom\eeprom.c	   435  {
; ..\eeprom\eeprom.c	   436  	uint8 year =0;
; ..\eeprom\eeprom.c	   437  	uint8 month =0;
; ..\eeprom\eeprom.c	   438  	uint8 day = 0;
; ..\eeprom\eeprom.c	   439  	uint8 hour = 0; 
; ..\eeprom\eeprom.c	   440  	uint8 minute = 0;
; ..\eeprom\eeprom.c	   441  	uint8 second=0;
; ..\eeprom\eeprom.c	   442  	uint8 reason =0;
; ..\eeprom\eeprom.c	   443  
; ..\eeprom\eeprom.c	   444  	if(0x1==SecurityLog_trigger)
	movh.a	a15,#@his(SecurityLog_trigger)
	lea	a15,[a15]@los(SecurityLog_trigger)
	ld.bu	d15,[a15]
.L558:
	sub.a	a10,#16
.L457:
	jne	d15,#1,.L86
.L559:

; ..\eeprom\eeprom.c	   445  	{   
; ..\eeprom\eeprom.c	   446  		log_failure(year,month,day,hour,minute,second,FailReason); 
	mov	d4,#0
	st.b	[a10],d4
.L560:
	movh.a	a2,#@his(FailReason)
.L561:
	mov	d5,d4
	st.b	[a10]4,d4
.L562:
	mov	e6,d4,d4
	ld.bu	d15,[a2]@los(FailReason)
	st.b	[a10]8,d15
.L563:
	call	log_failure
.L303:

; ..\eeprom\eeprom.c	   447  		memcpy(NvM_SECLOG_DATA, log_buffer, LOG_BUFFER_SIZE);
	movh.a	a4,#@his(NvM_SECLOG_DATA)
	lea	a4,[a4]@los(NvM_SECLOG_DATA)
.L564:
	movh.a	a5,#@his(log_buffer)
	lea	a5,[a5]@los(log_buffer)
.L565:
	mov	d4,#40
	call	memcpy
.L566:

; ..\eeprom\eeprom.c	   448  
; ..\eeprom\eeprom.c	   449  		NvM_WriteBlock(EEP_SecurityLog_BLOCK_ID,NULL);
	mov	d4,#98
	mov.a	a4,#0
	call	NvM_WriteBlock
.L567:

; ..\eeprom\eeprom.c	   450  		NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_SECLOG;
	fcall	.cocofun_5
.L568:
	insert	d15,d15,#1,#16,#1
	st.w	[a2]@los(NvM_JobFinished_Flag),d15
.L569:

; ..\eeprom\eeprom.c	   451  		SecurityLog_trigger =0;
	mov	d15,#0
	st.b	[a15],d15
.L86:

; ..\eeprom\eeprom.c	   452  	
; ..\eeprom\eeprom.c	   453  	}
; ..\eeprom\eeprom.c	   454   	return EEPROM_OK;
; ..\eeprom\eeprom.c	   455  }
	mov	d2,#0
	ret
.L302:
	
__EEP_SaveSecLog_function_end:
	.size	EEP_SaveSecLog,__EEP_SaveSecLog_function_end-EEP_SaveSecLog
.L176:
	; End of function
	
	.sdecl	'.bss.eeprom.log_buffer',data,cluster('log_buffer')
	.sect	'.bss.eeprom.log_buffer'
	.align	4
log_buffer:	.type	object
	.size	log_buffer,40
	.space	40
	.sdecl	'.data.eeprom.NvM_JobFinished_Flag',data,cluster('NvM_JobFinished_Flag')
	.sect	'.data.eeprom.NvM_JobFinished_Flag'
	.global	NvM_JobFinished_Flag
	.align	4
NvM_JobFinished_Flag:	.type	object
	.size	NvM_JobFinished_Flag,4
	.space	4
	.sdecl	'.data.eeprom.test_int',data,cluster('test_int')
	.sect	'.data.eeprom.test_int'
	.global	test_int
	.align	2
test_int:	.type	object
	.size	test_int,2
	.space	2
	.sdecl	'.data.eeprom.SecurityErrorFlag',data,cluster('SecurityErrorFlag')
	.sect	'.data.eeprom.SecurityErrorFlag'
	.global	SecurityErrorFlag
SecurityErrorFlag:	.type	object
	.size	SecurityErrorFlag,1
	.space	1
	.sdecl	'.data.eeprom.blsDIDF187WriteOnlyOnceFlag',data,cluster('blsDIDF187WriteOnlyOnceFlag')
	.sect	'.data.eeprom.blsDIDF187WriteOnlyOnceFlag'
	.global	blsDIDF187WriteOnlyOnceFlag
blsDIDF187WriteOnlyOnceFlag:	.type	object
	.size	blsDIDF187WriteOnlyOnceFlag,1
	.space	1
	.sdecl	'.data.eeprom.ReadCurrent_session',data,cluster('ReadCurrent_session')
	.sect	'.data.eeprom.ReadCurrent_session'
	.global	ReadCurrent_session
ReadCurrent_session:	.type	object
	.size	ReadCurrent_session,1
	.byte	2
	.calls	'EEP_DID_Init','FlashReadMemory'
	.calls	'EEP_DID_Init','NvM_ReadBlock'
	.calls	'EEP_DID_Init','EEP_MainFunction'
	.calls	'EEP_Init','Fls_17_Pmu_Init'
	.calls	'EEP_Init','Fee_Init'
	.calls	'EEP_Init','NvM_Init'
	.calls	'EEP_Init','EEP_DID_Init'
	.calls	'EEP_MainFunction','Fls_17_Pmu_MainFunction'
	.calls	'EEP_MainFunction','Fee_MainFunction'
	.calls	'EEP_MainFunction','NvM_MainFunction'
	.calls	'EEP_ReadDID','__INDIRECT__'
	.calls	'EEP_WriteDID','__INDIRECT__'
	.calls	'EEP_WriteDID','NvM_WriteBlock'
	.calls	'EEP_SetPIF_F110','NvM_WriteBlock'
	.calls	'EEP_GetSWVerification_F100','FL_CheckSWVerification'
	.calls	'EEP_GetECUProgramState_F1A3','FL_CheckProgramIntegrity'
	.calls	'EEP_GetECUProgramState_F1A3','FL_CheckProgramDependencies'
	.calls	'EEP_GetSoftwareValidFlag_AFFF','FL_CheckProgramDependencies'
	.calls	'EEP_GetSoftwareIntegrityStatus_AFFD','FL_CheckProgramIntegrity'
	.calls	'EEP_GetSoftwareCompatibilityStatus_AFFE','FL_CheckProgramDependencies'
	.calls	'EEP_GetProgrammingCounter_AFFC','FL_CheckProgramCounter'
	.calls	'EEP_SetSecErrFlag','NvM_WriteBlock'
	.calls	'log_failure','NvM_ReadBlock'
	.calls	'log_failure','EEP_MainFunction'
	.calls	'log_failure','memcpy'
	.calls	'log_failure','memmove'
	.calls	'EEP_SaveSecLog','log_failure'
	.calls	'EEP_SaveSecLog','memcpy'
	.calls	'EEP_SaveSecLog','NvM_WriteBlock'
	.calls	'EEP_DID_Init','.cocofun_4'
	.calls	'EEP_DID_Init','.cocofun_2'
	.calls	'EEP_ReadDID','.cocofun_6'
	.calls	'EEP_ReadDID','.cocofun_7'
	.calls	'EEP_ReadDID','.cocofun_4'
	.calls	'EEP_WriteDID','.cocofun_4'
	.calls	'EEP_WriteDID','.cocofun_2'
	.calls	'EEP_WriteDID','.cocofun_5'
	.calls	'EEP_GetPIF_F110','.cocofun_7'
	.calls	'EEP_GetPIF_F110','.cocofun_3'
	.calls	'EEP_GetPIF_F110','.cocofun_8'
	.calls	'EEP_GetPIF_F110','.cocofun_6'
	.calls	'EEP_SetPIF_F110','.cocofun_3'
	.calls	'EEP_SetPIF_F110','.cocofun_8'
	.calls	'EEP_SetPIF_F110','.cocofun_6'
	.calls	'EEP_SetPIF_F110','.cocofun_2'
	.calls	'EEP_GetECUProgramState_F1A3','.cocofun_7'
	.calls	'EEP_SaveSecLog','.cocofun_5'
	.calls	'EEP_DID_Init','',0
	.calls	'.cocofun_4','',0
	.calls	'.cocofun_2','',0
	.calls	'EEP_Init','',0
	.calls	'EEP_MainFunction','',0
	.calls	'EEP_ReadDID','',0
	.calls	'.cocofun_7','',0
	.calls	'.cocofun_6','',0
	.calls	'EEP_ReadCurrent_session','',0
	.calls	'EEP_WriteDID','',0
	.calls	'.cocofun_5','',0
	.calls	'EEP_GetLastSecurityAttemptResult','',0
	.calls	'EEP_GetPIF_F110','',0
	.calls	'.cocofun_8','',0
	.calls	'.cocofun_3','',0
	.calls	'EEP_SetPIF_F110','',0
	.calls	'EEP_GetSWVerification_F100','',0
	.calls	'EEP_GetECUProgramState_F1A3','',8
	.calls	'EEP_GetSoftwareValidFlag_AFFF','',8
	.calls	'EEP_GetSoftwareIntegrityStatus_AFFD','',0
	.calls	'EEP_GetSoftwareCompatibilityStatus_AFFE','',8
	.calls	'EEP_GetProgrammingCounter_AFFC','',0
	.calls	'EEP_SetSecErrFlag','',0
	.calls	'log_failure','',8
	.extern	FL_CheckProgramIntegrity
	.extern	FL_CheckProgramDependencies
	.extern	FL_CheckSWVerification
	.extern	FL_CheckProgramCounter
	.extern	NvM_SECERR_DATA
	.extern	NvM_SECLOG_DATA
	.extern	Nvm_Did_Cfg
	.extern	Nvm_Did_Cfg_Size
	.extern	FlashReadMemory
	.extern	Fls_17_Pmu_ConfigRoot
	.extern	Fls_17_Pmu_Init
	.extern	Fls_17_Pmu_MainFunction
	.extern	Fee_Init
	.extern	Fee_MainFunction
	.extern	NvM_Init
	.extern	NvM_ReadBlock
	.extern	NvM_WriteBlock
	.extern	NvM_MainFunction
	.extern	appblkIntDefault
	.extern	appblkCpbDefault
	.extern	FailReason
	.extern	SecurityLog_trigger
	.extern	memcpy
	.extern	memmove
	.extern	__INDIRECT__
	.calls	'EEP_SaveSecLog','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L139:
	.word	9084
	.half	3
	.word	.L140
	.byte	4
.L138:
	.byte	1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L141
.L296:
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'FL_CheckProgramIntegrity',0,1,205,1,14
	.word	173
	.byte	1,1,1,1,4
	.byte	'FL_CheckProgramDependencies',0,1,206,1,22
	.word	173
	.byte	1,1,1,1
.L281:
	.byte	5
	.word	173
	.byte	6
	.byte	'errorvalue',0,1,206,1,57
	.word	269
	.byte	0,4
	.byte	'FL_CheckSWVerification',0,1,207,1,22
	.word	173
	.byte	1,1,1,1,6
	.byte	'checkdata',0,1,207,1,52
	.word	269
	.byte	0
.L279:
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'FL_CheckProgramCounter',0,1,208,1,15
	.word	351
	.byte	1,1,1,1
.L310:
	.byte	2
	.byte	'unsigned long int',0,4,7,7
	.byte	'FlashReadMemory',0,2,139,1,13,1,1,1,1,6
	.byte	'DataBuf',0,2,139,1,36
	.word	269
	.byte	6
	.byte	'Addr',0,2,139,1,51
	.word	409
	.byte	6
	.byte	'Length',0,2,139,1,64
	.word	409
	.byte	0,7
	.byte	'Fls_17_Pmu_Init',0,3,134,6,13,1,1,1,1,8
	.byte	'Fls_17_Pmu_ConfigType',0,3,153,4,16,40,8
	.byte	'Fls_17_Pmu_StateType',0,3,202,3,16,36,9
	.byte	'FlsReadAddress',0,4
	.word	409
	.byte	2,35,0,9
	.byte	'FlsWriteAddress',0,4
	.word	409
	.byte	2,35,4,9
	.byte	'FlsReadLength',0,4
	.word	409
	.byte	2,35,8,9
	.byte	'FlsWriteLength',0,4
	.word	409
	.byte	2,35,12,9
	.byte	'FlsReadBufferPtr',0,4
	.word	269
	.byte	2,35,16,10
	.word	173
	.byte	5
	.word	705
	.byte	9
	.byte	'FlsWriteBufferPtr',0,4
	.word	710
	.byte	2,35,20,11,4,72,9,1,12
	.byte	'MEMIF_JOB_OK',0,0,12
	.byte	'MEMIF_JOB_FAILED',0,1,12
	.byte	'MEMIF_JOB_PENDING',0,2,12
	.byte	'MEMIF_JOB_CANCELED',0,3,12
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,12
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,9
	.byte	'FlsJobResult',0,1
	.word	742
	.byte	2,35,24,11,4,88,9,1,12
	.byte	'MEMIF_MODE_SLOW',0,0,12
	.byte	'MEMIF_MODE_FAST',0,1,0,9
	.byte	'FlsMode',0,1
	.word	894
	.byte	2,35,25,9
	.byte	'NotifCaller',0,1
	.word	173
	.byte	2,35,26,8
	.byte	'Fls_JobStartType',0,3,179,3,16,1,13
	.byte	'Reserved1',0,1
	.word	173
	.byte	1,7,2,35,0,13
	.byte	'Write',0,1
	.word	173
	.byte	1,6,2,35,0,13
	.byte	'Erase',0,1
	.word	173
	.byte	1,5,2,35,0,13
	.byte	'Read',0,1
	.word	173
	.byte	1,4,2,35,0,13
	.byte	'Compare',0,1
	.word	173
	.byte	1,3,2,35,0,13
	.byte	'Reserved2',0,1
	.word	173
	.byte	3,0,2,35,0,0,9
	.byte	'JobStarted',0,1
	.word	974
	.byte	2,35,27,14,2
	.word	173
	.byte	15,1,0,9
	.byte	'FlsJobType',0,2
	.word	1129
	.byte	2,35,28,9
	.byte	'FlsPver',0,1
	.word	173
	.byte	2,35,30,9
	.byte	'FlsOper',0,1
	.word	173
	.byte	2,35,31,9
	.byte	'FlsTimeoutErr',0,1
	.word	173
	.byte	2,35,32,0,5
	.word	556
	.byte	9
	.byte	'FlsStateVarPtr',0,4
	.word	1216
	.byte	2,35,0,9
	.byte	'FlsFastRead',0,4
	.word	409
	.byte	2,35,4,9
	.byte	'FlsSlowRead',0,4
	.word	409
	.byte	2,35,8,16,1,1,5
	.word	1287
	.byte	17
	.byte	'Fls_NotifFunctionPtrType',0,3,141,4,16
	.word	1290
	.byte	9
	.byte	'FlsJobEndNotificationPtr',0,4
	.word	1295
	.byte	2,35,12,9
	.byte	'FlsJobErrorNotificationPtr',0,4
	.word	1295
	.byte	2,35,16,9
	.byte	'FlsIllegalStateNotificationPtr',0,4
	.word	1295
	.byte	2,35,20,9
	.byte	'FlsWaitStates',0,4
	.word	409
	.byte	2,35,24,18,1,1,19
	.word	409
	.byte	19
	.word	409
	.byte	10
	.word	409
	.byte	5
	.word	1475
	.byte	19
	.word	1480
	.byte	19
	.word	173
	.byte	0,5
	.word	1462
	.byte	17
	.byte	'Fls_WriteCmdPtrType',0,3,143,4,16
	.word	1496
	.byte	9
	.byte	'FlsAccessCodeWritePtr',0,4
	.word	1501
	.byte	2,35,28,18,1,1,19
	.word	409
	.byte	0,5
	.word	1561
	.byte	17
	.byte	'Fls_EraseCmdPtrType',0,3,148,4,16
	.word	1570
	.byte	9
	.byte	'FlsAccessCodeErasePtr',0,4
	.word	1575
	.byte	2,35,32,9
	.byte	'FlsDefaultMode',0,1
	.word	894
	.byte	2,35,36,0,10
	.word	528
	.byte	5
	.word	1660
	.byte	6
	.byte	'ConfigPtr',0,3,134,6,58
	.word	1665
	.byte	0,5
	.word	1287
	.byte	5
	.word	1462
	.byte	5
	.word	1561
	.byte	20
	.byte	'Fls_17_Pmu_MainFunction',0,3,138,8,13,1,1,1,1,20
	.byte	'Fee_Init',0,5,218,5,13,1,1,1,1,20
	.byte	'Fee_MainFunction',0,5,131,8,13,1,1,1,1,20
	.byte	'NvM_Init',0,6,190,2,36,1,1,1,1,4
	.byte	'NvM_ReadBlock',0,6,223,3,46
	.word	173
	.byte	1,1,1,1,6
	.byte	'BlockId',0,6,225,3,25
	.word	351
	.byte	21
	.byte	'void',0,5
	.word	1844
	.byte	6
	.byte	'NvM_DstPtr',0,6,226,3,47
	.word	1850
	.byte	0,4
	.byte	'NvM_WriteBlock',0,6,247,3,46
	.word	173
	.byte	1,1,1,1,6
	.byte	'BlockId',0,6,249,3,25
	.word	351
	.byte	10
	.word	1844
	.byte	5
	.word	1921
	.byte	6
	.byte	'NvM_SrcPtr',0,6,250,3,49
	.word	1926
	.byte	0,20
	.byte	'NvM_MainFunction',0,6,186,5,36,1,1,1,1
.L363:
	.byte	14,8
	.word	173
	.byte	15,7,0,2
	.byte	'int',0,4,5,22
	.byte	'memcpy',0,7,170,3,2
	.word	1987
	.byte	1,1,1,22
	.byte	'memmove',0,7,172,3,2
	.word	1987
	.byte	1,1,1,23
	.byte	'__INDIRECT__',0,7,1,1,1,1,1,17
	.byte	'__prof_adm',0,7,1,1
	.word	1850
	.byte	24,1,5
	.word	2072
	.byte	17
	.byte	'__codeptr',0,7,1,1
	.word	2074
	.byte	17
	.byte	'uint8',0,8,90,29
	.word	173
	.byte	2
	.byte	'short int',0,2,5,17
	.byte	'sint16',0,8,91,29
	.word	2111
	.byte	17
	.byte	'uint16',0,8,92,29
	.word	351
	.byte	17
	.byte	'uint32',0,8,94,29
	.word	409
	.byte	17
	.byte	'boolean',0,8,105,29
	.word	173
	.byte	2
	.byte	'unsigned long long int',0,8,7,17
	.byte	'uint64',0,8,130,1,30
	.word	2185
	.byte	17
	.byte	'Std_ReturnType',0,9,113,15
	.word	173
	.byte	25,10,52,9,164,1,14,164,1
	.word	173
	.byte	15,163,1,0,9
	.byte	'datas',0,164,1
	.word	2256
	.byte	2,35,0,26,10,55,5,164,1,9
	.byte	'certFmt',0,1
	.word	173
	.byte	2,35,0,9
	.byte	'pModNum',0,8
	.word	1978
	.byte	2,35,1,14,16
	.word	173
	.byte	15,15,0,9
	.byte	'customPars',0,16
	.word	2323
	.byte	2,35,9,14,3
	.word	173
	.byte	15,2,0,9
	.byte	'certFailDate',0,3
	.word	2352
	.byte	2,35,25,14,4
	.word	173
	.byte	15,3,0,9
	.byte	'certSequenceNum',0,4
	.word	2383
	.byte	2,35,28,9
	.byte	'signAlgoFlg',0,1
	.word	173
	.byte	2,35,32,9
	.byte	'pubKeyCurPar',0,1
	.word	173
	.byte	2,35,33,9
	.byte	'hashAlgoFlg',0,1
	.word	173
	.byte	2,35,34,9
	.byte	'pubKeyIdx',0,1
	.word	173
	.byte	2,35,35,14,64
	.word	173
	.byte	15,63,0,9
	.byte	'certPubKey',0,64
	.word	2500
	.byte	2,35,36,9
	.byte	'certSigner',0,64
	.word	2500
	.byte	2,35,100,0,9
	.byte	'parameters',0,164,1
	.word	2283
	.byte	2,35,0,0,17
	.byte	'Secure_SignerInfoType',0,10,68,3
	.word	2250
	.byte	17
	.byte	'_iob_flag_t',0,11,75,25
	.word	351
	.byte	17
	.byte	'FL_ResultType',0,1,69,15
	.word	173
	.byte	11,1,72,9,1,12
	.byte	'INTERNAL_FLS',0,0,12
	.byte	'EXTERNAL_FLS',0,1,0,17
	.byte	'FL_FlashType',0,1,76,2
	.word	2644
	.byte	11,1,78,9,1,12
	.byte	'NO_CRC',0,0,12
	.byte	'LAST_ADDR',0,1,12
	.byte	'HEAD_ADDR',0,2,0,17
	.byte	'FL_CrcAddrType',0,1,83,2
	.word	2701
	.byte	26,1,108,9,8,9
	.byte	'address',0,4
	.word	409
	.byte	2,35,0,9
	.byte	'length',0,4
	.word	409
	.byte	2,35,4,0,17
	.byte	'FL_SegmentInfoType',0,1,116,3
	.word	2763
	.byte	26,1,129,1,9,20,9
	.byte	'blkValid',0,1
	.word	173
	.byte	2,35,0,9
	.byte	'blkProgAttempt',0,2
	.word	351
	.byte	2,35,2,9
	.byte	'blkChecksum',0,4
	.word	409
	.byte	2,35,4,14,9
	.word	173
	.byte	15,8,0,9
	.byte	'fingerPrint',0,9
	.word	2898
	.byte	2,35,8,0,17
	.byte	'FL_blockInfoType',0,1,139,1,3
	.word	2829
	.byte	27
	.word	351
	.byte	1,1,19
	.word	173
	.byte	5
	.word	173
	.byte	19
	.word	2967
	.byte	0,5
	.word	2955
	.byte	17
	.byte	'Eep_WriteFct',0,12,42,18
	.word	2978
	.byte	17
	.byte	'Eep_ReadFct',0,12,43,18
	.word	2978
	.byte	26,12,45,9,24,10
	.word	351
	.byte	9
	.byte	'u16t_did',0,2
	.word	3029
	.byte	2,35,0,9
	.byte	'u8t_datatype',0,1
	.word	173
	.byte	2,35,2,9
	.byte	'u32t_addr',0,4
	.word	409
	.byte	2,35,4,9
	.byte	'u8t_buffer',0,4
	.word	269
	.byte	2,35,8,10
	.word	351
	.byte	9
	.byte	'len',0,2
	.word	3113
	.byte	2,35,12,10
	.word	2983
	.byte	9
	.byte	'writeeeprom',0,4
	.word	3131
	.byte	2,35,16,10
	.word	3004
	.byte	9
	.byte	'readeeprom',0,4
	.word	3157
	.byte	2,35,20,0,17
	.byte	'ST_NVM_DID_TYPE',0,12,53,2
	.word	3024
	.byte	5
	.word	2955
	.byte	5
	.word	2955
	.byte	17
	.byte	'PduLengthType',0,13,76,22
	.word	351
	.byte	17
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,14,112,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,14,115,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,14,118,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,14,121,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,14,124,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,14,136,1,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,14,139,1,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,14,148,1,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,14,151,1,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,14,141,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,14,144,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,14,147,3,16
	.word	351
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,14,150,3,16
	.word	351
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,14,153,3,16
	.word	351
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,14,156,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,14,159,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,14,162,3,16
	.word	351
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,14,165,3,16
	.word	351
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,14,180,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,14,183,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,14,186,3,16
	.word	409
	.byte	17
	.byte	'IdtAppCom_MainPwrFltRsn',0,14,192,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGMDiags',0,14,195,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGMFltRsn',0,14,198,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGMSts',0,14,201,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGMSwSts',0,14,207,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,14,210,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,14,213,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,14,216,3,16
	.word	351
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,14,219,3,16
	.word	351
	.byte	17
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,14,225,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,14,228,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_RednPwrFltRsn',0,14,231,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,14,234,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_SHWAIndSts',0,14,237,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_SHWASysFltSts',0,14,240,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_SHWASysMsg',0,14,243,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,14,246,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_SHWASysSts',0,14,249,3,15
	.word	173
	.byte	17
	.byte	'IdtAppCom_SHWASysTakeOver',0,14,252,3,15
	.word	173
	.byte	17
	.byte	'NvM_BlockIdType',0,14,227,5,16
	.word	351
	.byte	2
	.byte	'unsigned int',0,4,7,17
	.byte	'Rte_BitType',0,14,230,7,22
	.word	4821
	.byte	28
	.byte	'NvM_SECERR_DATA',0,15,88,14
	.word	2500
	.byte	1,1
.L365:
	.byte	14,40
	.word	173
	.byte	15,39,0,28
	.byte	'NvM_SECLOG_DATA',0,15,89,14
	.word	4884
	.byte	1,1,29
	.word	3024
	.byte	30,0,31
	.word	4919
	.byte	10
	.word	4926
	.byte	28
	.byte	'Nvm_Did_Cfg',0,15,91,39
	.word	4931
	.byte	1,1,31
	.word	173
	.byte	10
	.word	4958
	.byte	28
	.byte	'Nvm_Did_Cfg_Size',0,15,92,29
	.word	4963
	.byte	1,1,17
	.byte	'tMajorVersion',0,2,35,15
	.word	173
	.byte	17
	.byte	'tMinorVersion',0,2,38,15
	.word	173
	.byte	17
	.byte	'tBugfixVersion',0,2,41,15
	.word	173
	.byte	17
	.byte	'tFlashResult',0,2,44,15
	.word	173
	.byte	17
	.byte	'tFlashAddress',0,2,47,16
	.word	409
	.byte	17
	.byte	'tFlashLength',0,2,50,16
	.word	409
	.byte	17
	.byte	'tFlashData',0,2,53,15
	.word	173
	.byte	17
	.byte	'tWDTriggerFct',0,2,56,16
	.word	1290
	.byte	18,1,1,26,2,59,9,24,9
	.byte	'patchLevel',0,1
	.word	173
	.byte	2,35,0,9
	.byte	'minorNumber',0,1
	.word	173
	.byte	2,35,1,9
	.byte	'majorNumber',0,1
	.word	173
	.byte	2,35,2,9
	.byte	'reserved1',0,1
	.word	173
	.byte	2,35,3,9
	.byte	'errorCode',0,1
	.word	173
	.byte	2,35,4,9
	.byte	'reserved2',0,2
	.word	351
	.byte	2,35,6,9
	.byte	'address',0,4
	.word	409
	.byte	2,35,8,9
	.byte	'length',0,4
	.word	409
	.byte	2,35,12,10
	.word	173
	.byte	5
	.word	5327
	.byte	9
	.byte	'data',0,4
	.word	5332
	.byte	2,35,16,9
	.byte	'wdTriggerFct',0,4
	.word	5145
	.byte	2,35,20,0,5
	.word	5170
	.byte	19
	.word	5374
	.byte	0,5
	.word	5167
	.byte	17
	.byte	'tFlashFct',0,2,95,16
	.word	5385
	.byte	5
	.word	1287
	.byte	17
	.byte	'unsigned_int',0,16,121,22
	.word	4821
	.byte	17
	.byte	'MemIf_JobResultType',0,4,80,3
	.word	742
	.byte	17
	.byte	'MemIf_ModeType',0,4,92,3
	.word	894
	.byte	17
	.byte	'Fls_LengthType',0,3,177,3,16
	.word	409
	.byte	17
	.byte	'Fls_JobStartType',0,3,187,3,3
	.word	974
	.byte	17
	.byte	'Fls_17_Pmu_Job_Type',0,3,191,3,15
	.word	173
	.byte	17
	.byte	'Fls_17_Pmu_StateType',0,3,134,4,3
	.word	556
	.byte	17
	.byte	'Fls_17_Pmu_ConfigType',0,3,222,4,3
	.word	528
	.byte	14,40
	.word	528
	.byte	15,0,0,10
	.word	5625
	.byte	28
	.byte	'Fls_17_Pmu_ConfigRoot',0,3,129,5,25
	.word	5634
	.byte	1,1,17
	.byte	'Fee_PageType',0,5,136,1,17
	.word	351
	.byte	17
	.byte	'Fee_NotifFunctionPtrType',0,5,146,1,16
	.word	1290
	.byte	8
	.byte	'Fee_Block',0,5,148,1,16,8,13
	.byte	'CycleCountLimit',0,4
	.word	4821
	.byte	24,8,2,35,2,13
	.byte	'FeeImmediateData',0,1
	.word	173
	.byte	8,0,2,35,3,13
	.byte	'BlockNumber',0,2
	.word	351
	.byte	16,0,2,35,4,13
	.byte	'Size',0,2
	.word	351
	.byte	16,0,2,35,6,0,17
	.byte	'Fee_BlockType',0,5,162,1,3
	.word	5728
	.byte	8
	.byte	'Fee_CacheStatus',0,5,170,1,16,2,13
	.byte	'Valid',0,1
	.word	173
	.byte	1,7,2,35,0,13
	.byte	'Consistent',0,1
	.word	173
	.byte	1,6,2,35,0,13
	.byte	'Copied',0,1
	.word	173
	.byte	1,5,2,35,0,13
	.byte	'PrevCopyValid',0,1
	.word	173
	.byte	1,4,2,35,0,13
	.byte	'PrevCopyConsistent',0,1
	.word	173
	.byte	1,3,2,35,0,13
	.byte	'PrevCopyCopied',0,1
	.word	173
	.byte	1,2,2,35,0,13
	.byte	'Reserved',0,2
	.word	351
	.byte	10,0,2,35,0,0,17
	.byte	'Fee_CacheStatusType',0,5,187,1,3
	.word	5862
	.byte	8
	.byte	'Fee_Cache',0,5,189,1,16,8,9
	.byte	'Address',0,4
	.word	409
	.byte	2,35,0,9
	.byte	'BlockNumber',0,2
	.word	351
	.byte	2,35,4,9
	.byte	'Status',0,2
	.word	5862
	.byte	2,35,6,0,17
	.byte	'Fee_CacheType',0,5,204,1,3
	.word	6072
	.byte	8
	.byte	'FeePendReqInfo_Buf',0,5,210,1,16,12,9
	.byte	'DataBufferPtr',0,4
	.word	269
	.byte	2,35,0,9
	.byte	'BlockNumber',0,2
	.word	351
	.byte	2,35,4,9
	.byte	'BlockOffset',0,2
	.word	351
	.byte	2,35,6,9
	.byte	'Length',0,2
	.word	351
	.byte	2,35,8,0,17
	.byte	'Fee_PendReqBufType',0,5,219,1,3
	.word	6166
	.byte	8
	.byte	'FeeStatusFlags_t',0,5,226,1,17,1,13
	.byte	'FeeBlkModified',0,1
	.word	173
	.byte	1,7,2,35,0,13
	.byte	'FeeStartInitGC',0,1
	.word	173
	.byte	1,6,2,35,0,13
	.byte	'FeeCurrSector',0,1
	.word	173
	.byte	1,5,2,35,0,13
	.byte	'FeeInitAPICalled',0,1
	.word	173
	.byte	1,4,2,35,0,13
	.byte	'FeeBlkInvalidStatus',0,1
	.word	173
	.byte	1,3,2,35,0,13
	.byte	'FeeWriteInvldAPICalled',0,1
	.word	173
	.byte	1,2,2,35,0,13
	.byte	'unused',0,1
	.word	173
	.byte	2,0,2,35,0,0,17
	.byte	'Fee_StatusFlagsType',0,5,254,1,3
	.word	6301
	.byte	8
	.byte	'Fee_SectorStatus_t',0,5,133,2,17,1,13
	.byte	'Dirty',0,1
	.word	173
	.byte	1,7,2,35,0,13
	.byte	'Used',0,1
	.word	173
	.byte	1,6,2,35,0,13
	.byte	'unused',0,1
	.word	173
	.byte	6,0,2,35,0,0,17
	.byte	'Fee_SectorStatusType',0,5,142,2,3
	.word	6542
	.byte	8
	.byte	'Fee_SectorInfo_t',0,5,147,2,16,32,9
	.byte	'StateCount',0,4
	.word	409
	.byte	2,35,0,9
	.byte	'UnerasableWLAddr',0,4
	.word	409
	.byte	2,35,4,14,8
	.word	409
	.byte	15,1,0,9
	.byte	'NonZeroWLAddr',0,8
	.word	6718
	.byte	2,35,8,9
	.byte	'NonZeroWLCount',0,4
	.word	409
	.byte	2,35,16,9
	.byte	'StatePageAddr',0,4
	.word	409
	.byte	2,35,20,9
	.byte	'NextFreeWLAddr',0,4
	.word	409
	.byte	2,35,24,9
	.byte	'UnerasableWLCount',0,1
	.word	173
	.byte	2,35,28,9
	.byte	'State',0,1
	.word	173
	.byte	2,35,29,9
	.byte	'Status',0,1
	.word	6542
	.byte	2,35,30,0,17
	.byte	'Fee_SectorInfoType',0,5,177,2,3
	.word	6649
	.byte	8
	.byte	'Fee_LastWrittenBlkInfo_t',0,5,180,2,16,12,9
	.byte	'Addr',0,4
	.word	409
	.byte	2,35,0,9
	.byte	'PageCount',0,2
	.word	351
	.byte	2,35,4,9
	.byte	'BlockNumber',0,2
	.word	351
	.byte	2,35,6,9
	.byte	'Status',0,2
	.word	5862
	.byte	2,35,8,0,17
	.byte	'Fee_LastWrittenBlkInfoType',0,5,190,2,3
	.word	6908
	.byte	8
	.byte	'Fee_GcBlkInfo_t',0,5,192,2,16,12,9
	.byte	'Addr',0,4
	.word	409
	.byte	2,35,0,9
	.byte	'PageCount',0,2
	.word	351
	.byte	2,35,4,9
	.byte	'BlockNumber',0,2
	.word	351
	.byte	2,35,6,9
	.byte	'Consistent',0,1
	.word	173
	.byte	2,35,8,0,17
	.byte	'Fee_GcBlkInfoType',0,5,202,2,3
	.word	7046
	.byte	8
	.byte	'Fee_State_Data_t',0,5,206,2,16,192,21,14,64
	.word	6649
	.byte	15,1,0,9
	.byte	'FeeSectorInfo',0,64
	.word	7194
	.byte	2,35,0,14,128,7
	.word	6072
	.byte	15,111,0,9
	.byte	'FeeBlockInfo',0,128,7
	.word	7226
	.byte	2,35,64,9
	.byte	'FeeLastWrittenBlkInfo',0,12
	.word	6908
	.byte	3,35,192,7,9
	.byte	'FeeGcCurrBlkInfo',0,12
	.word	7046
	.byte	3,35,204,7,9
	.byte	'FeePendReqInfo',0,12
	.word	6166
	.byte	3,35,216,7,14,128,1
	.word	409
	.byte	15,31,0,9
	.byte	'FeeGcLWBGcSrcAddr',0,128,1
	.word	7343
	.byte	3,35,228,7,9
	.byte	'FeeTempArray',0,8
	.word	6718
	.byte	3,35,228,8,9
	.byte	'FeeStateCount',0,4
	.word	409
	.byte	3,35,236,8,14,128,4
	.word	173
	.byte	15,255,3,0,9
	.byte	'FeeReadWriteBuffer',0,128,4
	.word	7429
	.byte	3,35,240,8,9
	.byte	'FeeGcReadWriteBuffer',0,128,4
	.word	7429
	.byte	3,35,240,12,14,248,3
	.word	173
	.byte	15,247,3,0,9
	.byte	'FeeLastWrittenBlkBuffer',0,248,3
	.word	7502
	.byte	3,35,240,16,9
	.byte	'FeeGcDestAddr',0,4
	.word	409
	.byte	3,35,232,20,9
	.byte	'FeeGcSrcAddr',0,4
	.word	409
	.byte	3,35,236,20,9
	.byte	'FeeNextFreePageAddr',0,4
	.word	409
	.byte	3,35,240,20,9
	.byte	'FeeWriteAffectedAddr',0,4
	.word	409
	.byte	3,35,244,20,9
	.byte	'FeeBlockStartAddr',0,4
	.word	409
	.byte	3,35,248,20,9
	.byte	'FeeCurrSectSrcAddr',0,4
	.word	409
	.byte	3,35,252,20,9
	.byte	'FeeUnErasableWLAddrTemp',0,4
	.word	409
	.byte	3,35,128,21,9
	.byte	'FeeUserReadDestPtr',0,4
	.word	269
	.byte	3,35,132,21,9
	.byte	'FeeJobResult',0,1
	.word	742
	.byte	3,35,136,21,9
	.byte	'FeeLastWriteSize',0,4
	.word	409
	.byte	3,35,138,21,9
	.byte	'FeeLastReadSize',0,4
	.word	409
	.byte	3,35,142,21,9
	.byte	'FeeComparedLen',0,2
	.word	351
	.byte	3,35,146,21,9
	.byte	'FeeReadLen',0,2
	.word	351
	.byte	3,35,148,21,9
	.byte	'FeeBlkPageCount',0,2
	.word	351
	.byte	3,35,150,21,9
	.byte	'FeeUserWriteBytesCount',0,2
	.word	351
	.byte	3,35,152,21,9
	.byte	'FeeCurrReqBlockNum',0,2
	.word	351
	.byte	3,35,154,21,9
	.byte	'FeeIntrCurrReqPageCount',0,2
	.word	351
	.byte	3,35,156,21,9
	.byte	'FeeGCCopyIndex',0,2
	.word	351
	.byte	3,35,158,21,9
	.byte	'FeeGCUnconfigBlkCopyIndex',0,2
	.word	351
	.byte	3,35,160,21,9
	.byte	'FeeUnConfigBlockCount',0,2
	.word	351
	.byte	3,35,162,21,9
	.byte	'FeeGcPrevBlockNumber',0,2
	.word	351
	.byte	3,35,164,21,9
	.byte	'FeeGcFirstBlkNumInWL',0,2
	.word	351
	.byte	3,35,166,21,9
	.byte	'FeeStatusFlags',0,1
	.word	6301
	.byte	3,35,168,21,9
	.byte	'FeeLastWrittenBlockDirty',0,1
	.word	173
	.byte	3,35,169,21,9
	.byte	'FeePendReqStatus',0,1
	.word	173
	.byte	3,35,170,21,9
	.byte	'FeeGcState',0,1
	.word	173
	.byte	3,35,171,21,9
	.byte	'FeeGcResumeState',0,1
	.word	173
	.byte	3,35,172,21,9
	.byte	'FeeGcBlkIndexInWL',0,1
	.word	173
	.byte	3,35,173,21,9
	.byte	'FeeInitGCState',0,1
	.word	173
	.byte	3,35,174,21,9
	.byte	'FeePrepDFLASHState',0,1
	.word	173
	.byte	3,35,175,21,9
	.byte	'FeeCacheState',0,1
	.word	173
	.byte	3,35,176,21,9
	.byte	'FeeRepairStep',0,1
	.word	173
	.byte	3,35,177,21,9
	.byte	'FeeWLAffectedType',0,1
	.word	173
	.byte	3,35,178,21,9
	.byte	'FeeIntrJob',0,1
	.word	173
	.byte	3,35,179,21,9
	.byte	'FeeIntrJobStatus',0,1
	.word	173
	.byte	3,35,180,21,9
	.byte	'FeeUserJobStatus',0,1
	.word	173
	.byte	3,35,181,21,9
	.byte	'FeeIntrJobResult',0,1
	.word	173
	.byte	3,35,182,21,9
	.byte	'FeeUserJobResult',0,1
	.word	173
	.byte	3,35,183,21,9
	.byte	'FeeMainJob',0,1
	.word	173
	.byte	3,35,184,21,9
	.byte	'FeeUserJobFailCount',0,1
	.word	173
	.byte	3,35,185,21,9
	.byte	'FeeIntrJobFailCount',0,1
	.word	173
	.byte	3,35,186,21,9
	.byte	'FeeUncfgBlksExceeded',0,1
	.word	173
	.byte	3,35,187,21,9
	.byte	'FeeUnErasableWLCountTemp',0,1
	.word	173
	.byte	3,35,188,21,9
	.byte	'FeeSectorCount',0,1
	.word	173
	.byte	3,35,189,21,9
	.byte	'FeeDisableGCStart',0,1
	.word	173
	.byte	3,35,190,21,0,17
	.byte	'Fee_StateDataType',0,5,155,4,3
	.word	7170
	.byte	8
	.byte	'Fee_Other_Config_t',0,5,157,4,16,1,13
	.byte	'FeeUnconfigBlock',0,1
	.word	173
	.byte	1,7,2,35,0,13
	.byte	'FeeGcRestartPoint',0,1
	.word	173
	.byte	1,6,2,35,0,13
	.byte	'FeeUseEraseSuspend',0,1
	.word	173
	.byte	1,5,2,35,0,13
	.byte	'unused',0,1
	.word	173
	.byte	5,0,2,35,0,0,17
	.byte	'Fee_GCConfigType',0,5,174,4,3
	.word	8825
	.byte	28
	.byte	'appblkIntDefault',0,7,34,14
	.word	173
	.byte	1,1,28
	.byte	'appblkCpbDefault',0,7,35,14
	.word	173
	.byte	1,1,28
	.byte	'FailReason',0,7,36,14
	.word	173
	.byte	1,1,28
	.byte	'SecurityLog_trigger',0,7,37,14
	.word	173
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L140:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,0,3,8,58,15,59,15,57,15
	.byte	73,19,54,15,39,12,63,12,60,12,0,0,4,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,5,15
	.byte	0,73,19,0,0,6,5,0,3,8,58,15,59,15,57,15,73,19,0,0,7,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,8,19,1,3,8,58,15,59,15,57,15,11,15,0,0,9,13,0,3,8,11,15,73,19,56,9,0,0,10,38,0,73,19,0,0,11,4,1,58
	.byte	15,59,15,57,15,11,15,0,0,12,40,0,3,8,28,13,0,0,13,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,14,1,1,11
	.byte	15,73,19,0,0,15,33,0,47,15,0,0,16,21,0,54,15,39,12,0,0,17,22,0,3,8,58,15,59,15,57,15,73,19,0,0,18,21,1
	.byte	54,15,39,12,0,0,19,5,0,73,19,0,0,20,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,21,59,0,3,8
	.byte	0,0,22,46,0,3,8,58,15,59,15,57,15,73,19,54,15,63,12,60,12,0,0,23,46,0,3,8,58,15,59,15,57,15,54,15,63,12
	.byte	60,12,0,0,24,21,0,54,15,0,0,25,23,1,58,15,59,15,57,15,11,15,0,0,26,19,1,58,15,59,15,57,15,11,15,0,0,27
	.byte	21,1,73,19,54,15,39,12,0,0,28,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,29,1,1,73,19,0,0,30,33
	.byte	0,0,0,31,53,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L141:
	.word	.L459-.L458
.L458:
	.half	3
	.word	.L461-.L460
.L460:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0,0
	.byte	'FL.h',0,1,0,0
	.byte	'Fls.h',0,1,0,0
	.byte	'Fls_17_Pmu.h',0,2,0,0
	.byte	'MemIf_Types.h',0,3,0,0
	.byte	'Fee.h',0,4,0,0
	.byte	'NvM.h',0,5,0,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'Std_Types.h',0,6,0,0
	.byte	'Secure_Types.h',0,7,0,0
	.byte	'stdio.h',0,8,0,0
	.byte	'..\\eeprom\\eeprom.h',0,0,0,0
	.byte	'ComStack_Types.h',0,6,0,0
	.byte	'..\\eeprom\\Rte_Type.h',0,0,0,0
	.byte	'..\\eeprom\\eeprom_Cfg.h',0,0,0,0
	.byte	'Mcal_TcLib.h',0,6,0,0,0
.L461:
.L459:
	.sdecl	'.debug_info',debug,cluster('EEP_ReadDID')
	.sect	'.debug_info'
.L142:
	.word	305
	.half	3
	.word	.L143
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L145,.L144
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_ReadDID',0,1,94,8
	.word	.L279
	.byte	1,1,1
	.word	.L99,.L280,.L98
	.byte	4
	.byte	'readData',0,1,94,27
	.word	.L281,.L282
	.byte	4
	.byte	'did',0,1,94,43
	.word	.L279,.L283
	.byte	5
	.word	.L284
	.byte	6
	.byte	'length',0,1,96,9
	.word	.L279,.L285
	.byte	6
	.byte	'i',0,1,96,16
	.word	.L279,.L286
	.byte	6
	.byte	'j',0,1,96,18
	.word	.L279,.L287
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_ReadDID')
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_ReadDID')
	.sect	'.debug_line'
.L144:
	.word	.L463-.L462
.L462:
	.half	3
	.word	.L465-.L464
.L464:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L465:
	.byte	5,7,7,0,5,2
	.word	.L99
	.byte	3,224,0,1,5,12,1,5,28,9
	.half	.L466-.L99
	.byte	1,5,19,7,9
	.half	.L15-.L466
	.byte	3,2,1,5,8,1,5,19,9
	.half	.L467-.L15
	.byte	1,5,22,9
	.half	.L468-.L467
	.byte	1,5,5,9
	.half	.L469-.L468
	.byte	1,5,30,7,9
	.half	.L470-.L469
	.byte	3,126,1,5,12,9
	.half	.L375-.L470
	.byte	1,5,28,9
	.half	.L377-.L375
	.byte	1,5,8,7,9
	.half	.L16-.L377
	.byte	3,8,1,5,2,9
	.half	.L471-.L16
	.byte	1,5,12,7,9
	.half	.L472-.L471
	.byte	3,2,1,5,1,3,18,1,5,23,7,9
	.half	.L17-.L472
	.byte	3,114,1,5,12,9
	.half	.L473-.L17
	.byte	1,5,28,9
	.half	.L474-.L473
	.byte	3,1,1,5,8,9
	.half	.L475-.L474
	.byte	1,5,26,9
	.half	.L476-.L475
	.byte	3,127,1,5,5,9
	.half	.L379-.L476
	.byte	3,1,1,5,20,7,9
	.half	.L477-.L379
	.byte	3,2,1,5,32,9
	.half	.L478-.L477
	.byte	1,5,41,9
	.half	.L376-.L478
	.byte	1,5,20,9
	.half	.L378-.L376
	.byte	1,5,11,9
	.half	.L19-.L378
	.byte	3,4,1,5,22,1,5,33,9
	.half	.L22-.L19
	.byte	3,2,1,5,44,9
	.half	.L479-.L22
	.byte	1,5,24,9
	.half	.L480-.L479
	.byte	3,126,1,5,44,9
	.half	.L380-.L480
	.byte	3,2,1,5,24,9
	.half	.L481-.L380
	.byte	3,126,1,5,18,3,2,1,5,22,9
	.half	.L21-.L481
	.byte	3,126,1,5,5,7,9
	.half	.L20-.L21
	.byte	3,5,1,5,1,3,2,1,7,9
	.half	.L146-.L20
	.byte	0,1,1
.L463:
	.sdecl	'.debug_ranges',debug,cluster('EEP_ReadDID')
	.sect	'.debug_ranges'
.L145:
	.word	-1,.L99,0,.L146-.L99,0,0
.L284:
	.word	-1,.L99,0,.L280-.L99,-1,.L101,0,.L261-.L101,-1,.L103,0,.L256-.L103,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_WriteDID')
	.sect	'.debug_info'
.L147:
	.word	337
	.half	3
	.word	.L148
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L150,.L149
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_WriteDID',0,1,137,1,8
	.word	.L279
	.byte	1,1,1
	.word	.L107,.L288,.L106
	.byte	4
	.byte	'writeData',0,1,137,1,28
	.word	.L281,.L289
	.byte	4
	.byte	'datalength',0,1,137,1,45
	.word	.L279,.L290
	.byte	5
	.word	.L291
	.byte	6
	.byte	'length',0,1,139,1,9
	.word	.L279,.L292
	.byte	6
	.byte	'i',0,1,139,1,16
	.word	.L279,.L293
	.byte	6
	.byte	'j',0,1,139,1,18
	.word	.L279,.L294
	.byte	6
	.byte	'did',0,1,139,1,20
	.word	.L279,.L295
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_WriteDID')
	.sect	'.debug_abbrev'
.L148:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_WriteDID')
	.sect	'.debug_line'
.L149:
	.word	.L483-.L482
.L482:
	.half	3
	.word	.L485-.L484
.L484:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L485:
	.byte	5,8,7,0,5,2
	.word	.L107
	.byte	3,136,1,1,5,25,9
	.half	.L395-.L107
	.byte	3,5,1,5,51,9
	.half	.L486-.L395
	.byte	1,5,29,9
	.half	.L487-.L486
	.byte	1,5,12,3,1,1,5,33,9
	.half	.L384-.L487
	.byte	3,127,1,5,12,3,1,1,5,7,9
	.half	.L488-.L384
	.byte	1,5,28,9
	.half	.L381-.L488
	.byte	1,5,17,7,9
	.half	.L27-.L381
	.byte	3,2,1,5,6,1,5,17,9
	.half	.L489-.L27
	.byte	1,5,20,9
	.half	.L490-.L489
	.byte	1,5,3,9
	.half	.L491-.L490
	.byte	1,5,30,7,9
	.half	.L492-.L491
	.byte	3,126,1,5,12,9
	.half	.L382-.L492
	.byte	1,5,28,9
	.half	.L386-.L382
	.byte	1,5,8,7,9
	.half	.L28-.L386
	.byte	3,8,1,5,2,9
	.half	.L493-.L28
	.byte	1,5,10,7,9
	.half	.L494-.L493
	.byte	3,2,1,5,1,3,57,1,5,29,7,9
	.half	.L29-.L494
	.byte	3,75,1,5,18,9
	.half	.L495-.L29
	.byte	1,5,32,9
	.half	.L496-.L495
	.byte	1,5,3,9
	.half	.L497-.L496
	.byte	1,5,25,7,9
	.half	.L498-.L497
	.byte	3,6,1,5,4,9
	.half	.L387-.L498
	.byte	3,1,1,5,12,7,9
	.half	.L31-.L387
	.byte	3,2,1,5,1,3,44,1,5,28,7,9
	.half	.L32-.L31
	.byte	3,88,1,5,8,9
	.half	.L499-.L32
	.byte	1,5,5,9
	.half	.L500-.L499
	.byte	1,5,20,7,9
	.half	.L501-.L500
	.byte	3,2,1,5,33,9
	.half	.L502-.L501
	.byte	1,5,52,1,5,51,9
	.half	.L383-.L502
	.byte	1,5,20,9
	.half	.L385-.L383
	.byte	1,5,31,9
	.half	.L34-.L385
	.byte	3,4,1,5,9,9
	.half	.L503-.L34
	.byte	1,5,6,9
	.half	.L389-.L503
	.byte	1,5,17,7,9
	.half	.L504-.L389
	.byte	3,2,1,5,7,9
	.half	.L505-.L504
	.byte	1,5,45,7,9
	.half	.L506-.L505
	.byte	3,2,1,5,56,9
	.half	.L388-.L506
	.byte	1,5,8,9
	.half	.L507-.L388
	.byte	3,1,1,5,28,9
	.half	.L390-.L507
	.byte	1,5,38,9
	.half	.L508-.L390
	.byte	3,1,1,5,36,1,5,7,9
	.half	.L509-.L508
	.byte	3,2,1,5,15,9
	.half	.L37-.L509
	.byte	3,2,1,5,1,3,24,1,5,36,7,9
	.half	.L36-.L37
	.byte	3,107,1,5,14,9
	.half	.L510-.L36
	.byte	1,5,11,9
	.half	.L511-.L510
	.byte	1,5,44,7,9
	.half	.L512-.L511
	.byte	3,2,1,5,55,9
	.half	.L391-.L512
	.byte	1,5,7,9
	.half	.L513-.L391
	.byte	3,1,1,5,27,9
	.half	.L392-.L513
	.byte	1,5,22,3,127,1,5,36,9
	.half	.L40-.L392
	.byte	3,3,1,5,14,9
	.half	.L514-.L40
	.byte	1,5,11,9
	.half	.L515-.L514
	.byte	1,5,44,7,9
	.half	.L516-.L515
	.byte	3,2,1,5,55,9
	.half	.L393-.L516
	.byte	1,5,7,9
	.half	.L517-.L393
	.byte	3,1,1,5,27,9
	.half	.L394-.L517
	.byte	1,5,11,9
	.half	.L38-.L394
	.byte	3,4,1,5,45,3,2,1,5,22,9
	.half	.L396-.L38
	.byte	3,126,1,5,21,9
	.half	.L44-.L396
	.byte	3,2,1,5,45,9
	.half	.L518-.L44
	.byte	1,5,32,9
	.half	.L519-.L518
	.byte	1,5,24,9
	.half	.L520-.L519
	.byte	3,126,1,5,35,9
	.half	.L397-.L520
	.byte	3,2,1,5,24,9
	.half	.L521-.L397
	.byte	3,126,1,5,22,9
	.half	.L43-.L521
	.byte	1,5,12,7,9
	.half	.L35-.L43
	.byte	3,5,1,5,1,3,4,1,7,9
	.half	.L151-.L35
	.byte	0,1,1
.L483:
	.sdecl	'.debug_ranges',debug,cluster('EEP_WriteDID')
	.sect	'.debug_ranges'
.L150:
	.word	-1,.L107,0,.L151-.L107,0,0
.L291:
	.word	-1,.L107,0,.L288-.L107,-1,.L109,0,.L251-.L109,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_GetLastSecurityAttemptResult')
	.sect	'.debug_info'
.L152:
	.word	246
	.half	3
	.word	.L153
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L155,.L154
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_GetLastSecurityAttemptResult',0,1,212,1,7
	.word	.L296
	.byte	1,1,1
	.word	.L111,.L297,.L110
	.byte	4
	.word	.L111,.L297
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_GetLastSecurityAttemptResult')
	.sect	'.debug_abbrev'
.L153:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_GetLastSecurityAttemptResult')
	.sect	'.debug_line'
.L154:
	.word	.L523-.L522
.L522:
	.half	3
	.word	.L525-.L524
.L524:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L525:
	.byte	5,9,7,0,5,2
	.word	.L111
	.byte	3,214,1,1,5,1,3,1,1,7,9
	.half	.L156-.L111
	.byte	0,1,1
.L523:
	.sdecl	'.debug_ranges',debug,cluster('EEP_GetLastSecurityAttemptResult')
	.sect	'.debug_ranges'
.L155:
	.word	-1,.L111,0,.L156-.L111,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_Init')
	.sect	'.debug_info'
.L157:
	.word	217
	.half	3
	.word	.L158
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L160,.L159
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_Init',0,1,73,6,1,1,1
	.word	.L95,.L298,.L94
	.byte	4
	.word	.L95,.L298
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_Init')
	.sect	'.debug_abbrev'
.L158:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_Init')
	.sect	'.debug_line'
.L159:
	.word	.L527-.L526
.L526:
	.half	3
	.word	.L529-.L528
.L528:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L529:
	.byte	5,19,7,0,5,2
	.word	.L95
	.byte	3,203,0,1,5,10,9
	.half	.L530-.L95
	.byte	3,1,1,9
	.half	.L531-.L530
	.byte	3,1,1,5,14,9
	.half	.L532-.L531
	.byte	3,1,1,5,1,7,9
	.half	.L161-.L532
	.byte	3,2,0,1,1
.L527:
	.sdecl	'.debug_ranges',debug,cluster('EEP_Init')
	.sect	'.debug_ranges'
.L160:
	.word	-1,.L95,0,.L161-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_MainFunction')
	.sect	'.debug_info'
.L162:
	.word	225
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L165,.L164
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_MainFunction',0,1,83,6,1,1,1
	.word	.L97,.L299,.L96
	.byte	4
	.word	.L97,.L299
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_MainFunction')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_MainFunction')
	.sect	'.debug_line'
.L164:
	.word	.L534-.L533
.L533:
	.half	3
	.word	.L536-.L535
.L535:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L536:
	.byte	5,30,7,0,5,2
	.word	.L97
	.byte	3,212,0,1,5,9,1,5,30,9
	.half	.L537-.L97
	.byte	1,5,9,1,5,29,9
	.half	.L538-.L537
	.byte	1,5,2,9
	.half	.L539-.L538
	.byte	1,5,26,7,9
	.half	.L540-.L539
	.byte	3,2,1,5,19,9
	.half	.L541-.L540
	.byte	3,1,1,9
	.half	.L542-.L541
	.byte	3,1,1,5,1,7,9
	.half	.L13-.L542
	.byte	3,3,1,7,9
	.half	.L166-.L13
	.byte	0,1,1
.L534:
	.sdecl	'.debug_ranges',debug,cluster('EEP_MainFunction')
	.sect	'.debug_ranges'
.L165:
	.word	-1,.L97,0,.L166-.L97,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_SetSecErrFlag')
	.sect	'.debug_info'
.L167:
	.word	249
	.half	3
	.word	.L168
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L170,.L169
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_SetSecErrFlag',0,1,148,3,8
	.word	.L279
	.byte	1,1,1
	.word	.L133,.L300,.L132
	.byte	4
	.byte	'data',0,1,148,3,33
	.word	.L281,.L301
	.byte	5
	.word	.L133,.L300
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_SetSecErrFlag')
	.sect	'.debug_abbrev'
.L168:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_SetSecErrFlag')
	.sect	'.debug_line'
.L169:
	.word	.L544-.L543
.L543:
	.half	3
	.word	.L546-.L545
.L545:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L546:
	.byte	5,2,7,0,5,2
	.word	.L133
	.byte	3,149,3,1,5,23,9
	.half	.L547-.L133
	.byte	1,5,21,9
	.half	.L548-.L547
	.byte	1,5,17,9
	.half	.L549-.L548
	.byte	3,1,1,5,41,1,5,2,9
	.half	.L550-.L549
	.byte	3,1,1,5,10,9
	.half	.L551-.L550
	.byte	3,1,1,5,22,9
	.half	.L552-.L551
	.byte	3,127,1,5,1,9
	.half	.L553-.L552
	.byte	3,2,1,7,9
	.half	.L171-.L553
	.byte	0,1,1
.L544:
	.sdecl	'.debug_ranges',debug,cluster('EEP_SetSecErrFlag')
	.sect	'.debug_ranges'
.L170:
	.word	-1,.L133,0,.L171-.L133,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_SaveSecLog')
	.sect	'.debug_info'
.L172:
	.word	238
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L175,.L174
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_SaveSecLog',0,1,178,3,8
	.word	.L279
	.byte	1,1,1
	.word	.L137,.L302,.L136
	.byte	4
	.word	.L137,.L302
	.byte	5
	.word	.L303,.L86
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_SaveSecLog')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_SaveSecLog')
	.sect	'.debug_line'
.L174:
	.word	.L555-.L554
.L554:
	.half	3
	.word	.L557-.L556
.L556:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L557:
	.byte	5,10,7,0,5,2
	.word	.L137
	.byte	3,187,3,1,5,8,9
	.half	.L558-.L137
	.byte	3,118,1,5,2,9
	.half	.L457-.L558
	.byte	3,10,1,5,35,7,9
	.half	.L559-.L457
	.byte	3,2,1,5,49,9
	.half	.L560-.L559
	.byte	1,5,20,9
	.half	.L561-.L560
	.byte	1,5,42,1,5,26,9
	.half	.L562-.L561
	.byte	1,5,49,1,5,30,9
	.half	.L563-.L562
	.byte	1,5,10,9
	.half	.L303-.L563
	.byte	3,1,1,5,27,9
	.half	.L564-.L303
	.byte	1,5,39,9
	.half	.L565-.L564
	.byte	1,5,18,9
	.half	.L566-.L565
	.byte	3,2,1,5,43,1,5,3,9
	.half	.L567-.L566
	.byte	3,1,1,5,23,9
	.half	.L568-.L567
	.byte	1,5,24,9
	.half	.L569-.L568
	.byte	3,1,1,5,23,1,5,10,9
	.half	.L86-.L569
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L176-.L86
	.byte	0,1,1
.L555:
	.sdecl	'.debug_ranges',debug,cluster('EEP_SaveSecLog')
	.sect	'.debug_ranges'
.L175:
	.word	-1,.L137,0,.L176-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_ReadCurrent_session')
	.sect	'.debug_info'
.L177:
	.word	257
	.half	3
	.word	.L178
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L180,.L179
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_ReadCurrent_session',0,1,127,8
	.word	.L279
	.byte	1,1,1
	.word	.L105,.L304,.L104
	.byte	4
	.byte	'readData',0,1,127,39
	.word	.L281,.L305
	.byte	5
	.word	.L105,.L304
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_ReadCurrent_session')
	.sect	'.debug_abbrev'
.L178:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_ReadCurrent_session')
	.sect	'.debug_line'
.L179:
	.word	.L571-.L570
.L570:
	.half	3
	.word	.L573-.L572
.L572:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L573:
	.byte	5,14,7,0,5,2
	.word	.L105
	.byte	3,129,1,1,5,12,9
	.half	.L574-.L105
	.byte	1,5,2,9
	.half	.L575-.L574
	.byte	3,1,1,5,13,7,9
	.half	.L576-.L575
	.byte	3,2,1,5,12,1,5,9,9
	.half	.L24-.L576
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L181-.L24
	.byte	0,1,1
.L571:
	.sdecl	'.debug_ranges',debug,cluster('EEP_ReadCurrent_session')
	.sect	'.debug_ranges'
.L180:
	.word	-1,.L105,0,.L181-.L105,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_GetPIF_F110')
	.sect	'.debug_info'
.L182:
	.word	360
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L185,.L184
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_GetPIF_F110',0,1,218,1,8
	.word	.L279
	.byte	1,1,1
	.word	.L113,.L306,.L112
	.byte	4
	.byte	'f110index',0,1,218,1,30
	.word	.L296,.L307
	.byte	4
	.byte	'data',0,1,218,1,47
	.word	.L281,.L308
	.byte	5
	.word	.L309
	.byte	6
	.byte	'writecounter',0,1,221,1,9
	.word	.L310,.L311
	.byte	6
	.byte	'did',0,1,222,1,9
	.word	.L279,.L312
	.byte	6
	.byte	'i',0,1,224,1,9
	.word	.L279,.L313
	.byte	6
	.byte	'j',0,1,224,1,11
	.word	.L279,.L314
	.byte	6
	.byte	'length',0,1,224,1,13
	.word	.L279,.L315
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_GetPIF_F110')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_GetPIF_F110')
	.sect	'.debug_line'
.L184:
	.word	.L578-.L577
.L577:
	.half	3
	.word	.L580-.L579
.L579:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L580:
	.byte	5,85,7,0,5,2
	.word	.L113
	.byte	3,220,1,1,5,96,9
	.half	.L402-.L113
	.byte	1,5,107,9
	.half	.L581-.L402
	.byte	1,5,54,9
	.half	.L582-.L581
	.byte	1,5,65,9
	.half	.L583-.L582
	.byte	1,5,69,9
	.half	.L584-.L583
	.byte	1,5,74,9
	.half	.L585-.L584
	.byte	1,5,127,9
	.half	.L586-.L585
	.byte	1,5,179,1,9
	.half	.L398-.L586
	.byte	1,5,2,9
	.half	.L587-.L398
	.byte	3,5,1,5,15,7,9
	.half	.L588-.L587
	.byte	3,2,1,5,12,9
	.half	.L589-.L588
	.byte	3,2,1,5,11,9
	.half	.L48-.L589
	.byte	1,5,15,3,126,1,7,9
	.half	.L590-.L48
	.byte	1,5,19,9
	.half	.L47-.L590
	.byte	3,7,1,5,3,9
	.half	.L591-.L47
	.byte	1,5,8,7,9
	.half	.L592-.L591
	.byte	3,2,1,5,14,9
	.half	.L399-.L592
	.byte	1,5,35,1,5,29,9
	.half	.L50-.L399
	.byte	3,4,1,5,34,1,5,8,9
	.half	.L51-.L50
	.byte	3,4,1,5,13,1,5,29,9
	.half	.L593-.L51
	.byte	1,5,20,7,9
	.half	.L53-.L593
	.byte	3,2,1,5,23,9
	.half	.L594-.L53
	.byte	1,5,6,9
	.half	.L595-.L594
	.byte	1,5,31,7,9
	.half	.L596-.L595
	.byte	3,126,1,5,13,9
	.half	.L404-.L596
	.byte	1,5,29,9
	.half	.L405-.L404
	.byte	1,5,11,7,9
	.half	.L54-.L405
	.byte	3,9,1,5,5,9
	.half	.L403-.L54
	.byte	1,5,36,7,9
	.half	.L597-.L403
	.byte	1,5,1,3,10,1,5,23,7,9
	.half	.L55-.L597
	.byte	3,121,1,5,10,9
	.half	.L598-.L55
	.byte	3,1,1,5,26,9
	.half	.L407-.L598
	.byte	3,127,1,5,21,9
	.half	.L406-.L407
	.byte	3,1,1,5,30,9
	.half	.L58-.L406
	.byte	3,2,1,5,41,9
	.half	.L599-.L58
	.byte	1,5,23,9
	.half	.L600-.L599
	.byte	3,126,1,5,41,9
	.half	.L408-.L600
	.byte	3,2,1,5,23,9
	.half	.L601-.L408
	.byte	3,126,1,5,14,3,2,1,5,21,9
	.half	.L57-.L601
	.byte	3,126,1,5,9,7,9
	.half	.L49-.L57
	.byte	3,5,1,5,1,3,1,1,7,9
	.half	.L186-.L49
	.byte	0,1,1
.L578:
	.sdecl	'.debug_ranges',debug,cluster('EEP_GetPIF_F110')
	.sect	'.debug_ranges'
.L185:
	.word	-1,.L113,0,.L186-.L113,0,0
.L309:
	.word	-1,.L113,0,.L306-.L113,-1,.L115,0,.L266-.L115,-1,.L117,0,.L241-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_SetPIF_F110')
	.sect	'.debug_info'
.L187:
	.word	364
	.half	3
	.word	.L188
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L190,.L189
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_SetPIF_F110',0,1,138,2,8
	.word	.L279
	.byte	1,1,1
	.word	.L119,.L316,.L118
	.byte	4
	.byte	'f110index',0,1,138,2,30
	.word	.L296,.L317
	.byte	4
	.byte	'data',0,1,138,2,47
	.word	.L281,.L318
	.byte	5
	.word	.L119,.L316
	.byte	6
	.byte	'writecounter',0,1,141,2,9
	.word	.L310,.L319
	.byte	6
	.byte	'did',0,1,142,2,9
	.word	.L279,.L320
	.byte	6
	.byte	'i',0,1,144,2,9
	.word	.L279,.L321
	.byte	6
	.byte	'j',0,1,144,2,11
	.word	.L279,.L322
	.byte	6
	.byte	'length',0,1,144,2,13
	.word	.L279,.L323
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_SetPIF_F110')
	.sect	'.debug_abbrev'
.L188:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_SetPIF_F110')
	.sect	'.debug_line'
.L189:
	.word	.L603-.L602
.L602:
	.half	3
	.word	.L605-.L604
.L604:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L605:
	.byte	5,98,7,0,5,2
	.word	.L119
	.byte	3,140,2,1,5,87,1,5,8,9
	.half	.L606-.L119
	.byte	3,125,1,5,98,9
	.half	.L420-.L606
	.byte	3,3,1,5,111,9
	.half	.L409-.L420
	.byte	1,5,56,9
	.half	.L607-.L409
	.byte	1,5,67,9
	.half	.L608-.L607
	.byte	1,5,26,9
	.half	.L609-.L608
	.byte	3,8,1,5,71,9
	.half	.L610-.L609
	.byte	3,120,1,5,76,9
	.half	.L611-.L610
	.byte	1,5,131,1,9
	.half	.L612-.L611
	.byte	1,5,185,1,9
	.half	.L613-.L612
	.byte	1,5,14,9
	.half	.L614-.L613
	.byte	3,7,1,5,66,9
	.half	.L422-.L614
	.byte	3,1,1,5,40,1,5,66,9
	.half	.L615-.L422
	.byte	3,1,1,5,26,9
	.half	.L616-.L615
	.byte	1,5,40,9
	.half	.L617-.L616
	.byte	1,5,66,9
	.half	.L618-.L617
	.byte	3,1,1,5,26,9
	.half	.L619-.L618
	.byte	1,5,40,9
	.half	.L620-.L619
	.byte	1,5,26,9
	.half	.L621-.L620
	.byte	3,1,1,5,40,9
	.half	.L622-.L621
	.byte	1,5,49,9
	.half	.L623-.L622
	.byte	3,2,1,5,60,9
	.half	.L624-.L623
	.byte	1,5,17,9
	.half	.L625-.L624
	.byte	1,5,60,1,5,2,9
	.half	.L410-.L625
	.byte	3,1,1,5,22,9
	.half	.L626-.L410
	.byte	1,5,19,9
	.half	.L627-.L626
	.byte	3,1,1,5,3,9
	.half	.L628-.L627
	.byte	1,5,8,7,9
	.half	.L629-.L628
	.byte	3,2,1,5,14,9
	.half	.L411-.L629
	.byte	1,5,35,1,5,29,9
	.half	.L60-.L411
	.byte	3,4,1,5,34,1,5,8,9
	.half	.L61-.L60
	.byte	3,4,1,5,13,1,5,29,9
	.half	.L630-.L61
	.byte	1,5,20,7,9
	.half	.L63-.L630
	.byte	3,2,1,5,23,9
	.half	.L631-.L63
	.byte	1,5,6,9
	.half	.L632-.L631
	.byte	1,5,31,7,9
	.half	.L633-.L632
	.byte	3,126,1,5,13,9
	.half	.L413-.L633
	.byte	1,5,29,9
	.half	.L414-.L413
	.byte	1,5,11,7,9
	.half	.L64-.L414
	.byte	3,9,1,5,5,9
	.half	.L412-.L64
	.byte	1,5,36,7,9
	.half	.L634-.L412
	.byte	1,5,1,3,13,1,5,23,7,9
	.half	.L65-.L634
	.byte	3,118,1,5,10,9
	.half	.L635-.L65
	.byte	3,1,1,5,26,9
	.half	.L416-.L635
	.byte	3,127,1,5,21,9
	.half	.L415-.L416
	.byte	3,1,1,5,20,9
	.half	.L68-.L415
	.byte	3,2,1,5,41,9
	.half	.L636-.L68
	.byte	1,5,31,9
	.half	.L637-.L636
	.byte	1,5,23,9
	.half	.L638-.L637
	.byte	3,126,1,5,35,9
	.half	.L417-.L638
	.byte	3,2,1,5,23,9
	.half	.L639-.L417
	.byte	3,126,1,5,21,9
	.half	.L67-.L639
	.byte	1,5,42,7,9
	.half	.L640-.L67
	.byte	3,5,1,5,53,9
	.half	.L418-.L640
	.byte	1,5,5,9
	.half	.L421-.L418
	.byte	3,2,1,5,9,9
	.half	.L641-.L421
	.byte	3,1,1,5,25,9
	.half	.L642-.L641
	.byte	3,127,1,5,1,9
	.half	.L643-.L642
	.byte	3,2,1,7,9
	.half	.L191-.L643
	.byte	0,1,1
.L603:
	.sdecl	'.debug_ranges',debug,cluster('EEP_SetPIF_F110')
	.sect	'.debug_ranges'
.L190:
	.word	-1,.L119,0,.L191-.L119,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_GetSWVerification_F100')
	.sect	'.debug_info'
.L192:
	.word	297
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L195,.L194
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_GetSWVerification_F100',0,1,190,2,8
	.word	.L279
	.byte	1,1,1
	.word	.L121,.L324,.L120
	.byte	4
	.byte	'f100index',0,1,190,2,41
	.word	.L296,.L325
	.byte	4
	.byte	'data',0,1,190,2,58
	.word	.L281,.L326
	.byte	5
	.word	.L121,.L324
	.byte	6
	.byte	'i',0,1,192,2,11
	.word	.L296,.L327
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_GetSWVerification_F100')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_GetSWVerification_F100')
	.sect	'.debug_line'
.L194:
	.word	.L645-.L644
.L644:
	.half	3
	.word	.L647-.L646
.L646:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L647:
	.byte	5,8,7,0,5,2
	.word	.L121
	.byte	3,189,2,1,5,25,9
	.half	.L424-.L121
	.byte	3,3,1,5,14,9
	.half	.L423-.L424
	.byte	3,4,1,5,3,1,5,14,9
	.half	.L648-.L423
	.byte	1,5,7,9
	.half	.L649-.L648
	.byte	3,126,1,5,34,9
	.half	.L425-.L649
	.byte	1,5,38,9
	.half	.L650-.L425
	.byte	1,5,44,7,9
	.half	.L71-.L650
	.byte	3,2,1,5,25,9
	.half	.L651-.L71
	.byte	1,5,44,9
	.half	.L652-.L651
	.byte	1,5,36,9
	.half	.L653-.L652
	.byte	1,5,40,9
	.half	.L654-.L653
	.byte	3,126,1,5,39,9
	.half	.L426-.L654
	.byte	3,2,1,5,40,9
	.half	.L655-.L426
	.byte	3,126,1,5,34,9
	.half	.L427-.L655
	.byte	1,5,38,9
	.half	.L656-.L427
	.byte	1,5,9,7,9
	.half	.L70-.L656
	.byte	3,4,1,5,1,3,1,1,7,9
	.half	.L196-.L70
	.byte	0,1,1
.L645:
	.sdecl	'.debug_ranges',debug,cluster('EEP_GetSWVerification_F100')
	.sect	'.debug_ranges'
.L195:
	.word	-1,.L121,0,.L196-.L121,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_GetECUProgramState_F1A3')
	.sect	'.debug_info'
.L197:
	.word	347
	.half	3
	.word	.L198
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L200,.L199
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_GetECUProgramState_F1A3',0,1,208,2,8
	.word	.L279
	.byte	1,1,1
	.word	.L123,.L328,.L122
	.byte	4
	.byte	'f1a3index',0,1,208,2,42
	.word	.L296,.L329
	.byte	4
	.byte	'data',0,1,208,2,59
	.word	.L281,.L330
	.byte	5
	.word	.L123,.L328
	.byte	6
	.byte	'i',0,1,210,2,11
	.word	.L296,.L331
	.byte	6
	.byte	'isPresent',0,1,211,2,11
	.word	.L296,.L332
	.byte	6
	.byte	'isCompatible',0,1,212,2,11
	.word	.L296,.L333
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_GetECUProgramState_F1A3')
	.sect	'.debug_abbrev'
.L198:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_GetECUProgramState_F1A3')
	.sect	'.debug_line'
.L199:
	.word	.L658-.L657
.L657:
	.half	3
	.word	.L660-.L659
.L659:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L660:
	.byte	5,8,7,0,5,2
	.word	.L123
	.byte	3,207,2,1,5,24,9
	.half	.L431-.L123
	.byte	3,4,1,5,23,1,5,39,9
	.half	.L661-.L431
	.byte	3,3,1,5,13,9
	.half	.L429-.L661
	.byte	3,4,1,5,12,1,5,25,9
	.half	.L662-.L429
	.byte	3,127,1,5,34,3,2,1,5,44,9
	.half	.L663-.L662
	.byte	3,126,1,5,25,9
	.half	.L664-.L663
	.byte	3,127,1,5,44,9
	.half	.L665-.L664
	.byte	1,5,12,9
	.half	.L666-.L665
	.byte	3,1,1,5,47,3,2,1,5,34,9
	.half	.L667-.L666
	.byte	1,5,9,9
	.half	.L432-.L667
	.byte	3,1,1,5,14,9
	.half	.L668-.L432
	.byte	3,4,1,5,12,9
	.half	.L669-.L668
	.byte	3,124,1,5,13,9
	.half	.L670-.L669
	.byte	3,1,1,5,12,1,5,3,9
	.half	.L430-.L670
	.byte	3,3,1,5,14,9
	.half	.L433-.L430
	.byte	1,5,34,9
	.half	.L671-.L433
	.byte	3,126,1,5,38,9
	.half	.L672-.L671
	.byte	1,5,44,7,9
	.half	.L74-.L672
	.byte	3,2,1,5,25,9
	.half	.L673-.L74
	.byte	1,5,44,9
	.half	.L674-.L673
	.byte	1,5,36,9
	.half	.L675-.L674
	.byte	1,5,40,9
	.half	.L676-.L675
	.byte	3,126,1,5,39,9
	.half	.L434-.L676
	.byte	3,2,1,5,40,9
	.half	.L677-.L434
	.byte	3,126,1,5,34,9
	.half	.L435-.L677
	.byte	1,5,38,9
	.half	.L678-.L435
	.byte	1,5,9,7,9
	.half	.L73-.L678
	.byte	3,4,1,5,1,3,1,1,7,9
	.half	.L201-.L73
	.byte	0,1,1
.L658:
	.sdecl	'.debug_ranges',debug,cluster('EEP_GetECUProgramState_F1A3')
	.sect	'.debug_ranges'
.L200:
	.word	-1,.L123,0,.L201-.L123,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_GetSoftwareValidFlag_AFFF')
	.sect	'.debug_info'
.L202:
	.word	311
	.half	3
	.word	.L203
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L205,.L204
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_GetSoftwareValidFlag_AFFF',0,1,230,2,8
	.word	.L279
	.byte	1,1,1
	.word	.L125,.L334,.L124
	.byte	4
	.byte	'afffindex',0,1,230,2,44
	.word	.L296,.L335
	.byte	4
	.byte	'data',0,1,230,2,61
	.word	.L281,.L336
	.byte	5
	.word	.L125,.L334
	.byte	6
	.byte	'isCompatible',0,1,232,2,8
	.word	.L296,.L337
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_GetSoftwareValidFlag_AFFF')
	.sect	'.debug_abbrev'
.L203:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_GetSoftwareValidFlag_AFFF')
	.sect	'.debug_line'
.L204:
	.word	.L680-.L679
.L679:
	.half	3
	.word	.L682-.L681
.L681:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L682:
	.byte	5,8,7,0,5,2
	.word	.L125
	.byte	3,229,2,1,5,21,9
	.half	.L439-.L125
	.byte	3,2,1,5,20,1,5,31,9
	.half	.L683-.L439
	.byte	3,1,1,5,11,9
	.half	.L437-.L683
	.byte	3,1,1,5,9,9
	.half	.L684-.L437
	.byte	3,1,1,5,11,9
	.half	.L685-.L684
	.byte	3,127,1,5,9,1,5,1,9
	.half	.L686-.L685
	.byte	3,2,1,7,9
	.half	.L206-.L686
	.byte	0,1,1
.L680:
	.sdecl	'.debug_ranges',debug,cluster('EEP_GetSoftwareValidFlag_AFFF')
	.sect	'.debug_ranges'
.L205:
	.word	-1,.L125,0,.L206-.L125,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_GetSoftwareIntegrityStatus_AFFD')
	.sect	'.debug_info'
.L207:
	.word	314
	.half	3
	.word	.L208
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L210,.L209
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_GetSoftwareIntegrityStatus_AFFD',0,1,238,2,8
	.word	.L279
	.byte	1,1,1
	.word	.L127,.L338,.L126
	.byte	4
	.byte	'affdindex',0,1,238,2,50
	.word	.L296,.L339
	.byte	4
	.byte	'data',0,1,238,2,67
	.word	.L281,.L340
	.byte	5
	.word	.L127,.L338
	.byte	6
	.byte	'isPresent',0,1,240,2,8
	.word	.L296,.L341
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_GetSoftwareIntegrityStatus_AFFD')
	.sect	'.debug_abbrev'
.L208:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_GetSoftwareIntegrityStatus_AFFD')
	.sect	'.debug_line'
.L209:
	.word	.L688-.L687
.L687:
	.half	3
	.word	.L690-.L689
.L689:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L690:
	.byte	5,8,7,0,5,2
	.word	.L127
	.byte	3,237,2,1,5,36,9
	.half	.L441-.L127
	.byte	3,4,1,5,5,9
	.half	.L440-.L441
	.byte	3,2,1,5,13,9
	.half	.L691-.L440
	.byte	3,2,1,5,9,3,2,1,9
	.half	.L692-.L691
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L211-.L692
	.byte	0,1,1
.L688:
	.sdecl	'.debug_ranges',debug,cluster('EEP_GetSoftwareIntegrityStatus_AFFD')
	.sect	'.debug_ranges'
.L210:
	.word	-1,.L127,0,.L211-.L127,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_GetSoftwareCompatibilityStatus_AFFE')
	.sect	'.debug_info'
.L212:
	.word	321
	.half	3
	.word	.L213
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L215,.L214
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_GetSoftwareCompatibilityStatus_AFFE',0,1,253,2,8
	.word	.L279
	.byte	1,1,1
	.word	.L129,.L342,.L128
	.byte	4
	.byte	'affeindex',0,1,253,2,54
	.word	.L296,.L343
	.byte	4
	.byte	'data',0,1,253,2,71
	.word	.L281,.L344
	.byte	5
	.word	.L129,.L342
	.byte	6
	.byte	'isCompatible',0,1,255,2,8
	.word	.L296,.L345
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_GetSoftwareCompatibilityStatus_AFFE')
	.sect	'.debug_abbrev'
.L213:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_GetSoftwareCompatibilityStatus_AFFE')
	.sect	'.debug_line'
.L214:
	.word	.L694-.L693
.L693:
	.half	3
	.word	.L696-.L695
.L695:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L696:
	.byte	5,8,7,0,5,2
	.word	.L129
	.byte	3,252,2,1,5,21,9
	.half	.L445-.L129
	.byte	3,2,1,5,20,1,5,31,9
	.half	.L697-.L445
	.byte	3,2,1,5,5,9
	.half	.L443-.L697
	.byte	3,2,1,5,2,9
	.half	.L698-.L443
	.byte	1,5,18,7,9
	.half	.L699-.L698
	.byte	3,2,1,5,16,1,5,10,9
	.half	.L79-.L699
	.byte	3,2,1,5,9,9
	.half	.L700-.L79
	.byte	1,9
	.half	.L701-.L700
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L216-.L701
	.byte	0,1,1
.L694:
	.sdecl	'.debug_ranges',debug,cluster('EEP_GetSoftwareCompatibilityStatus_AFFE')
	.sect	'.debug_ranges'
.L215:
	.word	-1,.L129,0,.L216-.L129,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_GetProgrammingCounter_AFFC')
	.sect	'.debug_info'
.L217:
	.word	307
	.half	3
	.word	.L218
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L220,.L219
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_GetProgrammingCounter_AFFC',0,1,139,3,8
	.word	.L279
	.byte	1,1,1
	.word	.L131,.L346,.L130
	.byte	4
	.byte	'affcindex',0,1,139,3,45
	.word	.L296,.L347
	.byte	4
	.byte	'data',0,1,139,3,62
	.word	.L281,.L348
	.byte	5
	.word	.L131,.L346
	.byte	6
	.byte	'counter',0,1,141,3,9
	.word	.L279,.L349
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_GetProgrammingCounter_AFFC')
	.sect	'.debug_abbrev'
.L218:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_GetProgrammingCounter_AFFC')
	.sect	'.debug_line'
.L219:
	.word	.L703-.L702
.L702:
	.half	3
	.word	.L705-.L704
.L704:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L705:
	.byte	5,8,7,0,5,2
	.word	.L131
	.byte	3,138,3,1,5,32,9
	.half	.L447-.L131
	.byte	3,3,1,5,25,9
	.half	.L446-.L447
	.byte	3,1,1,5,9,1,9
	.half	.L706-.L446
	.byte	3,1,1,9
	.half	.L707-.L706
	.byte	3,1,1,5,1,3,1,1,7,9
	.half	.L221-.L707
	.byte	0,1,1
.L703:
	.sdecl	'.debug_ranges',debug,cluster('EEP_GetProgrammingCounter_AFFC')
	.sect	'.debug_ranges'
.L220:
	.word	-1,.L131,0,.L221-.L131,0,0
	.sdecl	'.debug_info',debug,cluster('EEP_DID_Init')
	.sect	'.debug_info'
.L222:
	.word	247
	.half	3
	.word	.L223
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L225,.L224
	.byte	2
	.word	.L138
	.byte	3
	.byte	'EEP_DID_Init',0,1,38,13,1,1
	.word	.L89,.L350,.L88
	.byte	4
	.word	.L351
	.byte	5
	.byte	'i',0,1,40,9
	.word	.L279,.L352
	.byte	5
	.byte	'cnt',0,1,40,11
	.word	.L279,.L353
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EEP_DID_Init')
	.sect	'.debug_abbrev'
.L223:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EEP_DID_Init')
	.sect	'.debug_line'
.L224:
	.word	.L709-.L708
.L708:
	.half	3
	.word	.L711-.L710
.L710:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L711:
	.byte	5,13,7,0,5,2
	.word	.L89
	.byte	3,40,1,5,8,9
	.half	.L712-.L89
	.byte	1,5,29,9
	.half	.L366-.L712
	.byte	1,5,10,7,9
	.half	.L713-.L366
	.byte	1,5,8,3,11,1,5,21,9
	.half	.L3-.L713
	.byte	3,120,1,5,10,9
	.half	.L714-.L3
	.byte	1,5,24,9
	.half	.L715-.L714
	.byte	1,5,7,9
	.half	.L716-.L715
	.byte	1,5,38,7,9
	.half	.L717-.L716
	.byte	3,2,1,5,64,9
	.half	.L718-.L717
	.byte	1,5,90,9
	.half	.L719-.L718
	.byte	1,5,38,9
	.half	.L720-.L719
	.byte	1,5,29,9
	.half	.L4-.L720
	.byte	3,3,1,5,12,9
	.half	.L721-.L4
	.byte	1,5,11,7,9
	.half	.L722-.L721
	.byte	3,2,1,5,28,3,1,1,5,44,9
	.half	.L367-.L722
	.byte	3,1,1,5,55,9
	.half	.L723-.L367
	.byte	1,5,9,9
	.half	.L368-.L723
	.byte	3,4,1,5,73,9
	.half	.L724-.L368
	.byte	3,125,1,5,12,9
	.half	.L8-.L724
	.byte	3,2,1,5,17,3,1,1,5,25,9
	.half	.L725-.L8
	.byte	3,1,1,5,20,9
	.half	.L7-.L725
	.byte	3,124,1,5,14,9
	.half	.L726-.L7
	.byte	1,5,52,7,9
	.half	.L727-.L726
	.byte	1,5,50,9
	.half	.L728-.L727
	.byte	1,5,31,7,9
	.half	.L5-.L728
	.byte	3,115,1,5,13,9
	.half	.L369-.L5
	.byte	1,5,29,9
	.half	.L370-.L369
	.byte	1,7,9
	.half	.L2-.L370
	.byte	1,5,8,3,11,1,5,23,9
	.half	.L729-.L2
	.byte	3,11,1,5,17,9
	.half	.L730-.L729
	.byte	3,1,1,5,41,1,5,68,9
	.half	.L731-.L730
	.byte	3,1,1,5,7,9
	.half	.L11-.L731
	.byte	3,2,1,5,20,9
	.half	.L732-.L11
	.byte	3,1,1,5,15,9
	.half	.L10-.L732
	.byte	3,125,1,5,9,9
	.half	.L371-.L10
	.byte	1,5,47,7,9
	.half	.L372-.L371
	.byte	1,5,45,9
	.half	.L733-.L372
	.byte	1,5,3,7,9
	.half	.L12-.L733
	.byte	3,5,1,5,23,9
	.half	.L734-.L12
	.byte	1,5,38,9
	.half	.L735-.L734
	.byte	1,5,21,9
	.half	.L736-.L735
	.byte	1,5,1,9
	.half	.L737-.L736
	.byte	3,1,1,7,9
	.half	.L226-.L737
	.byte	0,1,1
.L709:
	.sdecl	'.debug_ranges',debug,cluster('EEP_DID_Init')
	.sect	'.debug_ranges'
.L225:
	.word	-1,.L89,0,.L226-.L89,0,0
.L351:
	.word	-1,.L89,0,.L350-.L89,-1,.L91,0,.L246-.L91,-1,.L93,0,.L236-.L93,0,0
	.sdecl	'.debug_info',debug,cluster('log_failure')
	.sect	'.debug_info'
.L227:
	.word	394
	.half	3
	.word	.L228
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L230,.L229
	.byte	2
	.word	.L138
	.byte	3
	.byte	'log_failure',0,1,157,3,6,1,1,1
	.word	.L135,.L354,.L134
	.byte	4
	.byte	'year',0,1,157,3,24
	.word	.L296,.L355
	.byte	4
	.byte	'month',0,1,157,3,36
	.word	.L296,.L356
	.byte	4
	.byte	'day',0,1,157,3,49
	.word	.L296,.L357
	.byte	4
	.byte	'hour',0,1,157,3,59
	.word	.L296,.L358
	.byte	4
	.byte	'minute',0,1,157,3,71
	.word	.L296,.L359
	.byte	4
	.byte	'second',0,1,157,3,85
	.word	.L296,.L360
	.byte	4
	.byte	'reason',0,1,157,3,98
	.word	.L296,.L361
	.byte	5
	.word	.L135,.L354
	.byte	6
	.byte	'cnt',0,1,159,3,8
	.word	.L296,.L362
	.byte	6
	.byte	'new_entry',0,1,160,3,8
	.word	.L363,.L364
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('log_failure')
	.sect	'.debug_abbrev'
.L228:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('log_failure')
	.sect	'.debug_line'
.L229:
	.word	.L739-.L738
.L738:
	.half	3
	.word	.L741-.L740
.L740:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L741:
	.byte	5,6,7,0,5,2
	.word	.L135
	.byte	3,156,3,1,5,12,9
	.half	.L455-.L135
	.byte	3,2,1,5,8,3,1,1,5,39,9
	.half	.L742-.L455
	.byte	1,5,46,9
	.half	.L743-.L742
	.byte	1,5,51,9
	.half	.L744-.L743
	.byte	1,5,57,9
	.half	.L745-.L744
	.byte	1,5,65,9
	.half	.L746-.L745
	.byte	1,5,73,9
	.half	.L747-.L746
	.byte	1,5,2,9
	.half	.L748-.L747
	.byte	3,2,1,5,23,9
	.half	.L749-.L748
	.byte	1,5,22,1,5,46,9
	.half	.L453-.L749
	.byte	3,1,1,5,16,9
	.half	.L750-.L453
	.byte	1,5,41,1,5,67,9
	.half	.L450-.L750
	.byte	3,1,1,5,6,9
	.half	.L84-.L450
	.byte	3,2,1,5,19,3,1,1,5,14,9
	.half	.L83-.L84
	.byte	3,125,1,5,8,9
	.half	.L751-.L83
	.byte	1,5,46,7,9
	.half	.L752-.L751
	.byte	1,5,44,9
	.half	.L753-.L752
	.byte	1,5,9,7,9
	.half	.L85-.L753
	.byte	3,6,1,5,21,9
	.half	.L754-.L85
	.byte	1,5,38,9
	.half	.L755-.L754
	.byte	1,5,21,9
	.half	.L756-.L755
	.byte	3,2,1,5,63,9
	.half	.L757-.L756
	.byte	1,5,21,9
	.half	.L758-.L757
	.byte	3,2,1,5,32,9
	.half	.L759-.L758
	.byte	1,5,1,9
	.half	.L231-.L759
	.byte	3,2,0,1,1
.L739:
	.sdecl	'.debug_ranges',debug,cluster('log_failure')
	.sect	'.debug_ranges'
.L230:
	.word	-1,.L135,0,.L231-.L135,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L232:
	.word	207
	.half	3
	.word	.L233
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L235,.L234
	.byte	2
	.word	.L138
	.byte	3
	.byte	'.cocofun_2',0,1,38,13,1
	.word	.L93,.L236,.L92
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L233:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L234:
	.word	.L761-.L760
.L760:
	.half	3
	.word	.L763-.L762
.L762:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L763:
	.byte	5,44,7,0,5,2
	.word	.L93
	.byte	3,52,1,5,55,9
	.half	.L419-.L93
	.byte	1,5,22,9
	.half	.L764-.L419
	.byte	1,5,55,1,7,9
	.half	.L236-.L764
	.byte	0,1,1,5,45,0,5,2
	.word	.L93
	.byte	3,179,1,1,5,56,9
	.half	.L419-.L93
	.byte	1,5,23,9
	.half	.L764-.L419
	.byte	1,5,55,3,129,127,1,7,9
	.half	.L236-.L764
	.byte	0,1,1,5,44,0,5,2
	.word	.L93
	.byte	3,190,1,1,5,55,9
	.half	.L419-.L93
	.byte	1,5,22,9
	.half	.L764-.L419
	.byte	1,5,55,3,246,126,1,7,9
	.half	.L236-.L764
	.byte	0,1,1,5,44,0,5,2
	.word	.L93
	.byte	3,195,1,1,5,55,9
	.half	.L419-.L93
	.byte	1,5,22,9
	.half	.L764-.L419
	.byte	1,5,55,3,241,126,1,7,9
	.half	.L236-.L764
	.byte	0,1,1,5,42,0,5,2
	.word	.L93
	.byte	3,183,2,1,5,53,9
	.half	.L419-.L93
	.byte	1,5,20,9
	.half	.L764-.L419
	.byte	1,5,55,3,253,125,1,7,9
	.half	.L236-.L764
	.byte	0,1,1
.L761:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L235:
	.word	-1,.L93,0,.L236-.L93,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L237:
	.word	208
	.half	3
	.word	.L238
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L240,.L239
	.byte	2
	.word	.L138
	.byte	3
	.byte	'.cocofun_3',0,1,218,1,8,1
	.word	.L117,.L241,.L116
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L238:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L239:
	.word	.L766-.L765
.L765:
	.half	3
	.word	.L768-.L767
.L767:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L768:
	.byte	5,107,7,0,5,2
	.word	.L117
	.byte	3,220,1,1,5,118,9
	.half	.L769-.L117
	.byte	1,5,160,1,9
	.half	.L770-.L769
	.byte	1,5,122,9
	.half	.L771-.L770
	.byte	1,5,171,1,9
	.half	.L772-.L771
	.byte	1,5,210,1,9
	.half	.L773-.L772
	.byte	1,5,175,1,9
	.half	.L774-.L773
	.byte	1,5,221,1,9
	.half	.L775-.L774
	.byte	1,9
	.half	.L241-.L775
	.byte	0,1,1,5,111,0,5,2
	.word	.L117
	.byte	3,140,2,1,5,122,9
	.half	.L769-.L117
	.byte	1,5,166,1,9
	.half	.L770-.L769
	.byte	1,5,126,9
	.half	.L771-.L770
	.byte	1,5,177,1,9
	.half	.L772-.L771
	.byte	1,5,218,1,9
	.half	.L773-.L772
	.byte	1,5,181,1,9
	.half	.L774-.L773
	.byte	1,5,229,1,9
	.half	.L775-.L774
	.byte	1,5,221,1,9
	.half	.L776-.L775
	.byte	3,80,1,7,9
	.half	.L241-.L776
	.byte	0,1,1
.L766:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L240:
	.word	-1,.L117,0,.L241-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_4')
	.sect	'.debug_info'
.L242:
	.word	207
	.half	3
	.word	.L243
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L245,.L244
	.byte	2
	.word	.L138
	.byte	3
	.byte	'.cocofun_4',0,1,38,13,1
	.word	.L91,.L246,.L90
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_4')
	.sect	'.debug_abbrev'
.L243:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_4')
	.sect	'.debug_line'
.L244:
	.word	.L778-.L777
.L777:
	.half	3
	.word	.L780-.L779
.L779:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L780:
	.byte	5,10,7,0,5,2
	.word	.L91
	.byte	3,43,1,5,21,9
	.half	.L781-.L91
	.byte	1,9
	.half	.L246-.L781
	.byte	0,1,1,5,12,0,5,2
	.word	.L91
	.byte	3,238,0,1,5,23,9
	.half	.L781-.L91
	.byte	1,5,21,9
	.half	.L782-.L781
	.byte	3,189,127,1,7,9
	.half	.L246-.L782
	.byte	0,1,1,5,18,0,5,2
	.word	.L91
	.byte	3,156,1,1,5,29,9
	.half	.L781-.L91
	.byte	1,5,21,9
	.half	.L782-.L781
	.byte	3,143,127,1,7,9
	.half	.L246-.L782
	.byte	0,1,1
.L778:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_4')
	.sect	'.debug_ranges'
.L245:
	.word	-1,.L91,0,.L246-.L91,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_5')
	.sect	'.debug_info'
.L247:
	.word	208
	.half	3
	.word	.L248
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L250,.L249
	.byte	2
	.word	.L138
	.byte	3
	.byte	'.cocofun_5',0,1,137,1,8,1
	.word	.L109,.L251,.L108
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_5')
	.sect	'.debug_abbrev'
.L248:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_5')
	.sect	'.debug_line'
.L249:
	.word	.L784-.L783
.L783:
	.half	3
	.word	.L786-.L785
.L785:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L786:
	.byte	5,8,7,0,5,2
	.word	.L109
	.byte	3,180,1,1,9
	.half	.L251-.L109
	.byte	0,1,1,5,7,0,5,2
	.word	.L109
	.byte	3,191,1,1,5,8,9
	.half	.L787-.L109
	.byte	3,117,1,7,9
	.half	.L251-.L787
	.byte	0,1,1,5,7,0,5,2
	.word	.L109
	.byte	3,196,1,1,5,8,9
	.half	.L787-.L109
	.byte	3,112,1,7,9
	.half	.L251-.L787
	.byte	0,1,1,5,3,0,5,2
	.word	.L109
	.byte	3,193,3,1,5,8,9
	.half	.L787-.L109
	.byte	3,243,125,1,7,9
	.half	.L251-.L787
	.byte	0,1,1
.L784:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_5')
	.sect	'.debug_ranges'
.L250:
	.word	-1,.L109,0,.L251-.L109,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_6')
	.sect	'.debug_info'
.L252:
	.word	207
	.half	3
	.word	.L253
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L255,.L254
	.byte	2
	.word	.L138
	.byte	3
	.byte	'.cocofun_6',0,1,94,8,1
	.word	.L103,.L256,.L102
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_6')
	.sect	'.debug_abbrev'
.L253:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_6')
	.sect	'.debug_line'
.L254:
	.word	.L789-.L788
.L788:
	.half	3
	.word	.L791-.L790
.L790:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L791:
	.byte	5,12,7,0,5,2
	.word	.L103
	.byte	3,224,0,1,9
	.half	.L256-.L103
	.byte	0,1,1,5,13,0,5,2
	.word	.L103
	.byte	3,244,1,1,5,12,9
	.half	.L792-.L103
	.byte	3,236,126,1,7,9
	.half	.L256-.L792
	.byte	0,1,1,5,13,0,5,2
	.word	.L103
	.byte	3,165,2,1,5,12,9
	.half	.L792-.L103
	.byte	3,187,126,1,7,9
	.half	.L256-.L792
	.byte	0,1,1
.L789:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_6')
	.sect	'.debug_ranges'
.L255:
	.word	-1,.L103,0,.L256-.L103,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_7')
	.sect	'.debug_info'
.L257:
	.word	207
	.half	3
	.word	.L258
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L260,.L259
	.byte	2
	.word	.L138
	.byte	3
	.byte	'.cocofun_7',0,1,94,8,1
	.word	.L101,.L261,.L100
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_7')
	.sect	'.debug_abbrev'
.L258:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_7')
	.sect	'.debug_line'
.L259:
	.word	.L794-.L793
.L793:
	.half	3
	.word	.L796-.L795
.L795:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L796:
	.byte	5,8,7,0,5,2
	.word	.L101
	.byte	3,226,0,1,9
	.half	.L261-.L101
	.byte	0,1,1,5,85,0,5,2
	.word	.L101
	.byte	3,220,1,1,5,8,9
	.half	.L797-.L101
	.byte	3,134,127,1,7,9
	.half	.L261-.L797
	.byte	0,1,1,5,3,0,5,2
	.word	.L101
	.byte	3,224,2,1,5,8,9
	.half	.L797-.L101
	.byte	3,130,126,1,7,9
	.half	.L261-.L797
	.byte	0,1,1
.L794:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_7')
	.sect	'.debug_ranges'
.L260:
	.word	-1,.L101,0,.L261-.L101,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_8')
	.sect	'.debug_info'
.L262:
	.word	208
	.half	3
	.word	.L263
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L265,.L264
	.byte	2
	.word	.L138
	.byte	3
	.byte	'.cocofun_8',0,1,218,1,8,1
	.word	.L115,.L266,.L114
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_8')
	.sect	'.debug_abbrev'
.L263:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_8')
	.sect	'.debug_line'
.L264:
	.word	.L799-.L798
.L798:
	.half	3
	.word	.L801-.L800
.L800:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\eeprom.c',0,0,0,0,0
.L801:
	.byte	5,34,7,0,5,2
	.word	.L115
	.byte	3,240,1,1,5,33,9
	.half	.L400-.L115
	.byte	1,5,8,9
	.half	.L802-.L400
	.byte	1,5,14,9
	.half	.L401-.L802
	.byte	1,9
	.half	.L266-.L401
	.byte	0,1,1,5,34,0,5,2
	.word	.L115
	.byte	3,161,2,1,5,33,9
	.half	.L400-.L115
	.byte	1,5,8,9
	.half	.L802-.L400
	.byte	1,5,14,9
	.half	.L401-.L802
	.byte	1,3,79,1,7,9
	.half	.L266-.L401
	.byte	0,1,1
.L799:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_8')
	.sect	'.debug_ranges'
.L265:
	.word	-1,.L115,0,.L266-.L115,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_JobFinished_Flag')
	.sect	'.debug_info'
.L267:
	.word	207
	.half	3
	.word	.L268
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L138
	.byte	3
	.byte	'NvM_JobFinished_Flag',0,7,29,8
	.word	.L310
	.byte	1,5,3
	.word	NvM_JobFinished_Flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_JobFinished_Flag')
	.sect	'.debug_abbrev'
.L268:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('log_buffer')
	.sect	'.debug_info'
.L269:
	.word	196
	.half	3
	.word	.L270
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L138
	.byte	3
	.byte	'log_buffer',0,7,27,14
	.word	.L365
	.byte	5,3
	.word	log_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('log_buffer')
	.sect	'.debug_abbrev'
.L270:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('test_int')
	.sect	'.debug_info'
.L271:
	.word	195
	.half	3
	.word	.L272
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L138
	.byte	3
	.byte	'test_int',0,7,30,8
	.word	.L279
	.byte	1,5,3
	.word	test_int
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('test_int')
	.sect	'.debug_abbrev'
.L272:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('SecurityErrorFlag')
	.sect	'.debug_info'
.L273:
	.word	204
	.half	3
	.word	.L274
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L138
	.byte	3
	.byte	'SecurityErrorFlag',0,7,31,7
	.word	.L296
	.byte	1,5,3
	.word	SecurityErrorFlag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('SecurityErrorFlag')
	.sect	'.debug_abbrev'
.L274:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('blsDIDF187WriteOnlyOnceFlag')
	.sect	'.debug_info'
.L275:
	.word	214
	.half	3
	.word	.L276
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L138
	.byte	3
	.byte	'blsDIDF187WriteOnlyOnceFlag',0,7,32,7
	.word	.L296
	.byte	1,5,3
	.word	blsDIDF187WriteOnlyOnceFlag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('blsDIDF187WriteOnlyOnceFlag')
	.sect	'.debug_abbrev'
.L276:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ReadCurrent_session')
	.sect	'.debug_info'
.L277:
	.word	206
	.half	3
	.word	.L278
	.byte	4,1
	.byte	'..\\eeprom\\eeprom.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L138
	.byte	3
	.byte	'ReadCurrent_session',0,7,33,7
	.word	.L296
	.byte	1,5,3
	.word	ReadCurrent_session
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ReadCurrent_session')
	.sect	'.debug_abbrev'
.L278:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L92:
	.word	-1,.L93,0,.L236-.L93
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L116:
	.word	-1,.L117,0,.L241-.L117
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_4')
	.sect	'.debug_loc'
.L90:
	.word	-1,.L91,0,.L246-.L91
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_5')
	.sect	'.debug_loc'
.L108:
	.word	-1,.L109,0,.L251-.L109
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_6')
	.sect	'.debug_loc'
.L102:
	.word	-1,.L103,0,.L256-.L103
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_7')
	.sect	'.debug_loc'
.L100:
	.word	-1,.L101,0,.L261-.L101
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_8')
	.sect	'.debug_loc'
.L114:
	.word	-1,.L115,0,.L266-.L115
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_DID_Init')
	.sect	'.debug_loc'
.L88:
	.word	-1,.L89,0,.L350-.L89
	.half	2
	.byte	138,0
	.word	0,0
.L353:
	.word	-1,.L89,.L93-.L89,.L236-.L89
	.half	5
	.byte	144,37,157,32,0
	.word	.L368-.L89,.L5-.L89
	.half	5
	.byte	144,37,157,32,0
	.word	.L371-.L89,.L372-.L89
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L352:
	.word	-1,.L89,.L91-.L89,.L246-.L89
	.half	5
	.byte	144,36,157,32,0
	.word	.L366-.L89,.L367-.L89
	.half	5
	.byte	144,36,157,32,0
	.word	.L93-.L89,.L236-.L89
	.half	5
	.byte	144,36,157,32,0
	.word	.L368-.L89,.L369-.L89
	.half	5
	.byte	144,36,157,32,0
	.word	.L370-.L89,.L350-.L89
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_GetECUProgramState_F1A3')
	.sect	'.debug_loc'
.L122:
	.word	-1,.L123,0,.L428-.L123
	.half	2
	.byte	138,0
	.word	.L428-.L123,.L328-.L123
	.half	2
	.byte	138,8
	.word	.L328-.L123,.L328-.L123
	.half	2
	.byte	138,0
	.word	0,0
.L330:
	.word	-1,.L123,0,.L429-.L123
	.half	1
	.byte	100
	.word	.L431-.L123,.L430-.L123
	.half	1
	.byte	111
	.word	.L101-.L123,.L261-.L123
	.half	1
	.byte	111
	.word	.L433-.L123,.L328-.L123
	.half	1
	.byte	111
	.word	0,0
.L329:
	.word	-1,.L123,0,.L429-.L123
	.half	5
	.byte	144,34,157,32,0
	.word	.L431-.L123,.L430-.L123
	.half	5
	.byte	144,36,157,32,32
	.word	.L101-.L123,.L261-.L123
	.half	5
	.byte	144,36,157,32,32
	.word	.L433-.L123,.L328-.L123
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L331:
	.word	-1,.L123,.L101-.L123,.L261-.L123
	.half	5
	.byte	144,39,157,32,32
	.word	.L433-.L123,.L434-.L123
	.half	5
	.byte	144,39,157,32,32
	.word	.L435-.L123,.L328-.L123
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L333:
	.word	-1,.L123,0,.L430-.L123
	.half	2
	.byte	145,120
	.word	.L101-.L123,.L261-.L123
	.half	2
	.byte	145,120
	.word	.L433-.L123,.L328-.L123
	.half	2
	.byte	145,120
	.word	0,0
.L332:
	.word	-1,.L123,.L429-.L123,.L432-.L123
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_GetLastSecurityAttemptResult')
	.sect	'.debug_loc'
.L110:
	.word	-1,.L111,0,.L297-.L111
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_GetPIF_F110')
	.sect	'.debug_loc'
.L112:
	.word	-1,.L113,0,.L306-.L113
	.half	2
	.byte	138,0
	.word	0,0
.L308:
	.word	-1,.L113,.L101-.L113,.L261-.L113
	.half	1
	.byte	100
	.word	.L117-.L113,.L241-.L113
	.half	1
	.byte	100
	.word	.L400-.L113,.L266-.L113
	.half	1
	.byte	100
	.word	.L373-.L113,.L256-.L113
	.half	1
	.byte	100
	.word	.L402-.L113,.L306-.L113
	.half	1
	.byte	100
	.word	0,0
.L312:
	.word	-1,.L113,.L399-.L113,.L50-.L113
	.half	5
	.byte	144,39,157,32,32
	.word	.L401-.L113,.L266-.L113
	.half	5
	.byte	144,39,157,32,32
	.word	.L373-.L113,.L256-.L113
	.half	5
	.byte	144,39,157,32,32
	.word	.L51-.L113,.L403-.L113
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L307:
	.word	-1,.L113,.L101-.L113,.L261-.L113
	.half	5
	.byte	144,34,157,32,0
	.word	.L117-.L113,.L241-.L113
	.half	5
	.byte	144,34,157,32,0
	.word	.L400-.L113,.L266-.L113
	.half	5
	.byte	144,34,157,32,0
	.word	.L373-.L113,.L256-.L113
	.half	5
	.byte	144,34,157,32,0
	.word	.L402-.L113,.L306-.L113
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L313:
	.word	-1,.L113,.L373-.L113,.L256-.L113
	.half	5
	.byte	144,32,157,32,0
	.word	.L51-.L113,.L404-.L113
	.half	5
	.byte	144,32,157,32,0
	.word	.L405-.L113,.L406-.L113
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L314:
	.word	-1,.L113,.L407-.L113,.L408-.L113
	.half	5
	.byte	144,39,157,32,32
	.word	.L57-.L113,.L49-.L113
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L315:
	.word	-1,.L113,.L406-.L113,.L49-.L113
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L311:
	.word	-1,.L113,.L398-.L113,.L48-.L113
	.half	5
	.byte	144,39,157,32,32
	.word	.L47-.L113,.L51-.L113
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_GetProgrammingCounter_AFFC')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L131,0,.L346-.L131
	.half	2
	.byte	138,0
	.word	0,0
.L347:
	.word	-1,.L131,0,.L446-.L131
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L349:
	.word	-1,.L131,.L446-.L131,.L346-.L131
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L348:
	.word	-1,.L131,0,.L446-.L131
	.half	1
	.byte	100
	.word	.L447-.L131,.L346-.L131
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_GetSWVerification_F100')
	.sect	'.debug_loc'
.L120:
	.word	-1,.L121,0,.L324-.L121
	.half	2
	.byte	138,0
	.word	0,0
.L326:
	.word	-1,.L121,0,.L423-.L121
	.half	1
	.byte	100
	.word	.L424-.L121,.L324-.L121
	.half	1
	.byte	108
	.word	0,0
.L325:
	.word	-1,.L121,0,.L423-.L121
	.half	5
	.byte	144,34,157,32,0
	.word	.L424-.L121,.L324-.L121
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L327:
	.word	-1,.L121,.L425-.L121,.L426-.L121
	.half	5
	.byte	144,39,157,32,32
	.word	.L427-.L121,.L324-.L121
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_GetSoftwareCompatibilityStatus_AFFE')
	.sect	'.debug_loc'
.L128:
	.word	-1,.L129,0,.L442-.L129
	.half	2
	.byte	138,0
	.word	.L442-.L129,.L342-.L129
	.half	2
	.byte	138,8
	.word	.L342-.L129,.L342-.L129
	.half	2
	.byte	138,0
	.word	0,0
.L343:
	.word	-1,.L129,0,.L443-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L344:
	.word	-1,.L129,0,.L444-.L129
	.half	1
	.byte	100
	.word	.L445-.L129,.L342-.L129
	.half	1
	.byte	111
	.word	0,0
.L345:
	.word	-1,.L129,0,.L342-.L129
	.half	2
	.byte	145,120
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_GetSoftwareIntegrityStatus_AFFD')
	.sect	'.debug_loc'
.L126:
	.word	-1,.L127,0,.L338-.L127
	.half	2
	.byte	138,0
	.word	0,0
.L339:
	.word	-1,.L127,0,.L440-.L127
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L340:
	.word	-1,.L127,0,.L440-.L127
	.half	1
	.byte	100
	.word	.L441-.L127,.L338-.L127
	.half	1
	.byte	111
	.word	0,0
.L341:
	.word	-1,.L127,.L440-.L127,.L338-.L127
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_GetSoftwareValidFlag_AFFF')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L125,0,.L436-.L125
	.half	2
	.byte	138,0
	.word	.L436-.L125,.L334-.L125
	.half	2
	.byte	138,8
	.word	.L334-.L125,.L334-.L125
	.half	2
	.byte	138,0
	.word	0,0
.L335:
	.word	-1,.L125,0,.L437-.L125
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L336:
	.word	-1,.L125,0,.L438-.L125
	.half	1
	.byte	100
	.word	.L439-.L125,.L334-.L125
	.half	1
	.byte	111
	.word	0,0
.L337:
	.word	-1,.L125,0,.L334-.L125
	.half	2
	.byte	145,120
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_Init')
	.sect	'.debug_loc'
.L94:
	.word	-1,.L95,0,.L298-.L95
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_MainFunction')
	.sect	'.debug_loc'
.L96:
	.word	-1,.L97,0,.L299-.L97
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_ReadCurrent_session')
	.sect	'.debug_loc'
.L104:
	.word	-1,.L105,0,.L304-.L105
	.half	2
	.byte	138,0
	.word	0,0
.L305:
	.word	-1,.L105,0,.L304-.L105
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_ReadDID')
	.sect	'.debug_loc'
.L98:
	.word	-1,.L99,0,.L280-.L99
	.half	2
	.byte	138,0
	.word	0,0
.L283:
	.word	-1,.L99,.L373-.L99,.L256-.L99
	.half	5
	.byte	144,34,157,32,0
	.word	.L374-.L99,.L261-.L99
	.half	5
	.byte	144,34,157,32,0
	.word	.L91-.L99,.L246-.L99
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L376-.L99
	.half	5
	.byte	144,34,157,32,0
	.word	.L19-.L99,.L20-.L99
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L286:
	.word	-1,.L99,.L373-.L99,.L256-.L99
	.half	5
	.byte	144,32,157,32,0
	.word	.L374-.L99,.L261-.L99
	.half	5
	.byte	144,32,157,32,0
	.word	0,.L375-.L99
	.half	5
	.byte	144,32,157,32,0
	.word	.L91-.L99,.L246-.L99
	.half	5
	.byte	144,32,157,32,0
	.word	.L377-.L99,.L378-.L99
	.half	5
	.byte	144,32,157,32,0
	.word	.L19-.L99,.L22-.L99
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L287:
	.word	-1,.L99,.L22-.L99,.L380-.L99
	.half	5
	.byte	144,39,157,32,32
	.word	.L21-.L99,.L20-.L99
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L285:
	.word	-1,.L99,.L379-.L99,.L280-.L99
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L282:
	.word	-1,.L99,.L373-.L99,.L256-.L99
	.half	1
	.byte	100
	.word	.L374-.L99,.L261-.L99
	.half	1
	.byte	100
	.word	.L91-.L99,.L246-.L99
	.half	1
	.byte	100
	.word	0,.L378-.L99
	.half	1
	.byte	100
	.word	.L19-.L99,.L20-.L99
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_SaveSecLog')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L137,0,.L457-.L137
	.half	2
	.byte	138,0
	.word	.L457-.L137,.L302-.L137
	.half	2
	.byte	138,16
	.word	.L302-.L137,.L302-.L137
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_SetPIF_F110')
	.sect	'.debug_loc'
.L118:
	.word	-1,.L119,0,.L316-.L119
	.half	2
	.byte	138,0
	.word	0,0
.L318:
	.word	-1,.L119,0,.L409-.L119
	.half	1
	.byte	100
	.word	.L117-.L119,.L241-.L119
	.half	1
	.byte	108
	.word	.L400-.L119,.L266-.L119
	.half	1
	.byte	108
	.word	.L373-.L119,.L256-.L119
	.half	1
	.byte	108
	.word	.L93-.L119,.L236-.L119
	.half	1
	.byte	108
	.word	.L420-.L119,.L316-.L119
	.half	1
	.byte	108
	.word	0,0
.L320:
	.word	-1,.L119,.L411-.L119,.L60-.L119
	.half	5
	.byte	144,39,157,32,32
	.word	.L401-.L119,.L266-.L119
	.half	5
	.byte	144,39,157,32,32
	.word	.L373-.L119,.L256-.L119
	.half	5
	.byte	144,39,157,32,32
	.word	.L61-.L119,.L412-.L119
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L317:
	.word	-1,.L119,.L117-.L119,.L241-.L119
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L410-.L119
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L321:
	.word	-1,.L119,.L373-.L119,.L256-.L119
	.half	5
	.byte	144,32,157,32,0
	.word	.L61-.L119,.L413-.L119
	.half	5
	.byte	144,32,157,32,0
	.word	.L414-.L119,.L415-.L119
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L322:
	.word	-1,.L119,.L416-.L119,.L417-.L119
	.half	5
	.byte	144,39,157,32,32
	.word	.L67-.L119,.L418-.L119
	.half	5
	.byte	144,39,157,32,32
	.word	.L93-.L119,.L419-.L119
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L323:
	.word	-1,.L119,.L93-.L119,.L236-.L119
	.half	5
	.byte	144,32,157,32,0
	.word	.L415-.L119,.L421-.L119
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L319:
	.word	-1,.L119,.L400-.L119,.L266-.L119
	.half	5
	.byte	144,36,157,32,0
	.word	.L373-.L119,.L256-.L119
	.half	5
	.byte	144,36,157,32,0
	.word	.L93-.L119,.L236-.L119
	.half	5
	.byte	144,36,157,32,0
	.word	.L422-.L119,.L316-.L119
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_SetSecErrFlag')
	.sect	'.debug_loc'
.L132:
	.word	-1,.L133,0,.L300-.L133
	.half	2
	.byte	138,0
	.word	0,0
.L301:
	.word	-1,.L133,0,.L448-.L133
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EEP_WriteDID')
	.sect	'.debug_loc'
.L106:
	.word	-1,.L107,0,.L288-.L107
	.half	2
	.byte	138,0
	.word	0,0
.L290:
	.word	-1,.L107,.L91-.L107,.L246-.L107
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L383-.L107
	.half	5
	.byte	144,34,157,32,0
	.word	.L34-.L107,.L388-.L107
	.half	5
	.byte	144,34,157,32,0
	.word	.L93-.L107,.L236-.L107
	.half	5
	.byte	144,34,157,32,0
	.word	.L37-.L107,.L391-.L107
	.half	5
	.byte	144,34,157,32,0
	.word	.L40-.L107,.L393-.L107
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L295:
	.word	-1,.L107,.L91-.L107,.L246-.L107
	.half	5
	.byte	144,32,157,32,0
	.word	.L384-.L107,.L385-.L107
	.half	5
	.byte	144,32,157,32,0
	.word	.L34-.L107,.L389-.L107
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L293:
	.word	-1,.L107,.L381-.L107,.L382-.L107
	.half	5
	.byte	144,32,157,32,32
	.word	.L91-.L107,.L246-.L107
	.half	5
	.byte	144,32,157,32,32
	.word	.L386-.L107,.L385-.L107
	.half	5
	.byte	144,32,157,32,32
	.word	.L93-.L107,.L236-.L107
	.half	5
	.byte	144,32,157,32,32
	.word	.L34-.L107,.L390-.L107
	.half	5
	.byte	144,32,157,32,32
	.word	.L37-.L107,.L392-.L107
	.half	5
	.byte	144,32,157,32,32
	.word	.L40-.L107,.L394-.L107
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L294:
	.word	-1,.L107,.L396-.L107,.L397-.L107
	.half	5
	.byte	144,39,157,32,32
	.word	.L43-.L107,.L35-.L107
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L292:
	.word	-1,.L107,.L387-.L107,.L31-.L107
	.half	5
	.byte	144,36,157,32,0
	.word	.L93-.L107,.L236-.L107
	.half	5
	.byte	144,36,157,32,0
	.word	.L109-.L107,.L251-.L107
	.half	5
	.byte	144,36,157,32,0
	.word	.L32-.L107,.L288-.L107
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L289:
	.word	-1,.L107,0,.L27-.L107
	.half	1
	.byte	100
	.word	.L91-.L107,.L246-.L107
	.half	1
	.byte	108
	.word	.L93-.L107,.L236-.L107
	.half	1
	.byte	108
	.word	.L109-.L107,.L251-.L107
	.half	1
	.byte	108
	.word	.L395-.L107,.L288-.L107
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('log_failure')
	.sect	'.debug_loc'
.L362:
	.word	-1,.L135,.L456-.L135,.L354-.L135
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L357:
	.word	-1,.L135,0,.L450-.L135
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L358:
	.word	-1,.L135,0,.L450-.L135
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
.L134:
	.word	-1,.L135,0,.L449-.L135
	.half	2
	.byte	138,0
	.word	.L449-.L135,.L354-.L135
	.half	2
	.byte	138,8
	.word	.L354-.L135,.L354-.L135
	.half	2
	.byte	138,0
	.word	0,0
.L359:
	.word	-1,.L135,0,.L354-.L135
	.half	2
	.byte	145,0
	.word	.L452-.L135,.L453-.L135
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L356:
	.word	-1,.L135,0,.L450-.L135
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L364:
	.word	-1,.L135,0,.L354-.L135
	.half	2
	.byte	145,120
	.word	0,0
.L361:
	.word	-1,.L135,0,.L354-.L135
	.half	2
	.byte	145,8
	.word	.L455-.L135,.L450-.L135
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L360:
	.word	-1,.L135,0,.L354-.L135
	.half	2
	.byte	145,4
	.word	.L454-.L135,.L450-.L135
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L355:
	.word	-1,.L135,0,.L451-.L135
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L803:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('EEP_DID_Init')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L89,.L350-.L89
	.sdecl	'.debug_frame',debug,cluster('EEP_Init')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L95,.L298-.L95
	.sdecl	'.debug_frame',debug,cluster('EEP_MainFunction')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L97,.L299-.L97
	.sdecl	'.debug_frame',debug,cluster('EEP_ReadDID')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L99,.L280-.L99
	.sdecl	'.debug_frame',debug,cluster('EEP_ReadCurrent_session')
	.sect	'.debug_frame'
	.word	24
	.word	.L803,.L105,.L304-.L105
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('EEP_WriteDID')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L107,.L288-.L107
	.sdecl	'.debug_frame',debug,cluster('EEP_GetLastSecurityAttemptResult')
	.sect	'.debug_frame'
	.word	24
	.word	.L803,.L111,.L297-.L111
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('EEP_GetPIF_F110')
	.sect	'.debug_frame'
	.word	20
	.word	.L803,.L113,.L306-.L113
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('EEP_SetPIF_F110')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L119,.L316-.L119
	.sdecl	'.debug_frame',debug,cluster('EEP_GetSWVerification_F100')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L121,.L324-.L121
	.sdecl	'.debug_frame',debug,cluster('EEP_GetECUProgramState_F1A3')
	.sect	'.debug_frame'
	.word	36
	.word	.L803,.L123,.L328-.L123
	.byte	4
	.word	(.L428-.L123)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L328-.L428)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('EEP_GetSoftwareValidFlag_AFFF')
	.sect	'.debug_frame'
	.word	36
	.word	.L803,.L125,.L334-.L125
	.byte	4
	.word	(.L436-.L125)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L334-.L436)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('EEP_GetSoftwareIntegrityStatus_AFFD')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L127,.L338-.L127
	.sdecl	'.debug_frame',debug,cluster('EEP_GetSoftwareCompatibilityStatus_AFFE')
	.sect	'.debug_frame'
	.word	36
	.word	.L803,.L129,.L342-.L129
	.byte	4
	.word	(.L442-.L129)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L342-.L442)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('EEP_GetProgrammingCounter_AFFC')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L131,.L346-.L131
	.sdecl	'.debug_frame',debug,cluster('EEP_SetSecErrFlag')
	.sect	'.debug_frame'
	.word	12
	.word	.L803,.L133,.L300-.L133
	.sdecl	'.debug_frame',debug,cluster('log_failure')
	.sect	'.debug_frame'
	.word	36
	.word	.L803,.L135,.L354-.L135
	.byte	4
	.word	(.L449-.L135)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L354-.L449)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('EEP_SaveSecLog')
	.sect	'.debug_frame'
	.word	36
	.word	.L803,.L137,.L302-.L137
	.byte	4
	.word	(.L457-.L137)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L302-.L457)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L804:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_4')
	.sect	'.debug_frame'
	.word	24
	.word	.L804,.L91,.L246-.L91
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L804,.L93,.L236-.L93
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_7')
	.sect	'.debug_frame'
	.word	24
	.word	.L804,.L101,.L261-.L101
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_6')
	.sect	'.debug_frame'
	.word	24
	.word	.L804,.L103,.L256-.L103
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_5')
	.sect	'.debug_frame'
	.word	24
	.word	.L804,.L109,.L251-.L109
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_8')
	.sect	'.debug_frame'
	.word	24
	.word	.L804,.L115,.L266-.L115
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L804,.L117,.L241-.L117
	.byte	8,18,8,19,8,20,8,21,8,22,8,23


	; Module end
