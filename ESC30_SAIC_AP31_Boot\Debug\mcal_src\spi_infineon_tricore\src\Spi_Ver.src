	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc17812a -c99 --dep-file=mcal_src\\spi_infineon_tricore\\src\\.Spi_Ver.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\spi_infineon_tricore\\src\\Spi_Ver.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\spi_infineon_tricore\\src\\Spi_Ver.src ..\\mcal_src\\spi_infineon_tricore\\src\\Spi_Ver.c"
	.compiler_name		"ctc"
	.name	"Spi_Ver"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Spi_lGetJobStatus')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Spi_lGetJobStatus

; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     1  /*******************************************************************************
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     2  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     3  ** Copyright (C) Infineon Technologies (2015)                                 **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     4  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     5  ** All rights reserved.                                                       **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     6  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    10  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    11  ********************************************************************************
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    12  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    13  **   $FILENAME   : Spi_Ver.c $                                                **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    14  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    15  **   $CC VERSION : \main\30 $                                                 **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    16  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    17  **   $DATE       : 2018-01-06 $                                               **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    18  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    19  **   AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    20  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    21  **   VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    22  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    23  **   DESCRIPTION : This file contains AUTOSAR version specific functionality  **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    24  **                 of SPI Handler driver.                                     **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    25  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    26  **   MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    27  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    28  *******************************************************************************/
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    29  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    30  /*******************************************************************************
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    31  **                      Includes                                              **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    32  *******************************************************************************/
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    33  /* Own header file, this includes own configuration file also */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    34  #include "Spi.h"
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    35  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    36  #include "Spi_Local.h"
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    37  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    38  #if(SPI_SLAVE_ENABLE == STD_ON)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    39  #include "SpiSlave.h"
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    40  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    41  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    42  /* Include AS version specific header file */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    43  #include "Spi_Ver.h"
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    44  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    45  /* Inclusion from Diagnostic Error Manager File */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    46  /* The module shall include the Dem.h file.*/
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    47  /* Fix for AI00252645*/
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    48  #if ( (SPI_HW_ERROR_DEM_REPORT == ENABLE_DEM_REPORT) ||  \ 
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    49        (SPI_DMA_ERROR_DEM_REPORT == ENABLE_DEM_REPORT) )
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    50  #include "Dem.h"
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    51  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    52  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    53  /*******************************************************************************
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    54  **                      Imported Compiler Switch Checks                       **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    55  *******************************************************************************/
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    56  #ifndef SPI_SW_MAJOR_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    57    #error "SPI_SW_MAJOR_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    58  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    59  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    60  #ifndef SPI_SW_MINOR_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    61    #error "SPI_SW_MINOR_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    62  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    63  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    64  #ifndef SPI_SW_PATCH_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    65    #error "SPI_SW_PATCH_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    66  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    67  /* [cover parentID=DS_AS_SPI069] //ASW:1530 */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    68  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    69  /* [cover parentID=DS_AS_SPI069] //AUTOSAR:11117 */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    70  #if (SPI_SW_MAJOR_VERSION != 4U)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    71    #error "SPI_SW_MAJOR_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    72  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    73  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    74  #if (SPI_SW_MINOR_VERSION != 9U)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    75    #error "SPI_SW_MINOR_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    76  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    77  /* [/cover] */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    78  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    79  /* AUTOSAR Secification File Version Check */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    80  #ifndef SPI_AR_RELEASE_MAJOR_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    81    #error "SPI_AR_RELEASE_MAJOR_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    82  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    83  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    84  #ifndef SPI_AR_RELEASE_MINOR_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    85    #error "SPI_AR_RELEASE_MINOR_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    86  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    87  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    88  #ifndef SPI_AR_RELEASE_REVISION_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    89    #error "SPI_AR_RELEASE_REVISION_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    90  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    91  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    92  #if (SPI_AR_RELEASE_MAJOR_VERSION != 4U)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    93    #error "SPI_AR_RELEASE_MAJOR_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    94  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    95  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    96  #if (SPI_AR_RELEASE_MINOR_VERSION != 0U)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    97    #error "SPI_AR_RELEASE_MINOR_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    98  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	    99  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   100  #if (SPI_AR_RELEASE_REVISION_VERSION != 3U)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   101    #error "SPI_AR_RELEASE_REVISION_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   102  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   103  /* [/cover] */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   104  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   105  /* Inter Module Check */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   106  /* Check for the correct version usage in the used modules */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   107  /* [cover parentID=DS_AS403_SPI369] */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   108  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   109  /*
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   110    VERSION CHECK FOR DET MODULE INCLUSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   111  */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   112  #if ( SPI_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   113  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   114  #ifndef DET_AR_RELEASE_MAJOR_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   115    #error "DET_AR_RELEASE_MAJOR_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   116  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   117  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   118  #ifndef DET_AR_RELEASE_MINOR_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   119    #error "DET_AR_RELEASE_MINOR_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   120  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   121  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   122  /* Updated for AI00251674 IFX_DET_VERSION_CHECK and IFX_DEM_VERSION_CHECK
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   123     are defined in Mcal_Options.h */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   124  #if (IFX_DET_VERSION_CHECK == STD_ON)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   125  #if ( DET_AR_RELEASE_MAJOR_VERSION != SPI_AR_RELEASE_MAJOR_VERSION )
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   126    #error "DET_AR_RELEASE_MAJOR_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   127  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   128  #if ( DET_AR_RELEASE_MINOR_VERSION != SPI_AR_RELEASE_MINOR_VERSION )
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   129    #error "DET_AR_RELEASE_MINOR_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   130  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   131  #endif /* ( SPI_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   132  /* End Of SPI_DEV_ERROR_DETECT */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   133  #endif /* (IFX_DET_VERSION_CHECK  == STD_ON) */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   134  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   135  #if ( (SPI_HW_ERROR_DEM_REPORT == ENABLE_DEM_REPORT) ||  \ 
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   136        (SPI_DMA_ERROR_DEM_REPORT == ENABLE_DEM_REPORT) )
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   137  #ifndef DEM_AR_RELEASE_MAJOR_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   138    #error "DEM_AR_RELEASE_MAJOR_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   139  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   140  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   141  #ifndef DEM_AR_RELEASE_MINOR_VERSION
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   142    #error "DEM_AR_RELEASE_MINOR_VERSION is not defined. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   143  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   144  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   145  #if (IFX_DEM_VERSION_CHECK == STD_ON)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   146  #if ( DEM_AR_RELEASE_MAJOR_VERSION != SPI_AR_RELEASE_MAJOR_VERSION )
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   147    #error "DEM_AR_RELEASE_MAJOR_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   148  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   149  #if ( DEM_AR_RELEASE_MINOR_VERSION != SPI_AR_RELEASE_MINOR_VERSION )
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   150    #error "DEM_AR_RELEASE_MINOR_VERSION does not match. "
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   151  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   152  #endif /* (IFX_DEM_VERSION_CHECK  == STD_ON) */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   153  #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   154  /* [/cover] */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   155  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   156  #define SPI_START_SEC_CODE
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   157  #include "MemMap.h"
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   158  /*******************************************************************************
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   159  ** Syntax           :  Spi_JobResultType Spi_lGetJobStatus                    **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   160  ** (                                                                          **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   161  **   Spi_JobType Job                                                          **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   162  ** )                                                                          **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   163  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   164  ** Service ID       : NA                                                      **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   165  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   166  ** Sync/Async       : Asynchronous                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   167  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   168  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   169  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   170  ** Parameters (in)  : Job - Job Id                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   171  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   172  ** Parameters (out) : None                                                    **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   173  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   174  ** Return value     : Job result.                                             **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   175  **                                                                            **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   176  ** Description      : Get the Job Result                                      **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   177  ** Traceability : [cover parentID=DS_AS403_SPI038]                 [/cover]   **
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   178  *******************************************************************************/
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   179  Spi_JobResultType Spi_lGetJobStatus(Spi_JobType Job)
; Function Spi_lGetJobStatus
.L4:
Spi_lGetJobStatus:	.type	func

; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   180  {
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   181    uint16 Index;
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   182    uint32 Temp = 0U;
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   183    Spi_JobResultType RetVal;
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   184  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   185    #if (SPI_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   186    {
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   187      /* ASIL or QM Job index from map configuration*/
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   188      Index = (uint16)(Spi_kConfigPtr->SpiMapConfigPtr->JobIndex[Job]
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   189              >> SPI_JOB_INDEX);
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   190    }
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   191    #else
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   192    {
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   193      /* if safety is OFF then map config is absent */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   194      Index = (uint16)(Job
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   195              >> SPI_JOB_INDEX);
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   196    }
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   197    #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   198  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   199    #if (SPI_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   200      if( SPI_ASIL_JOB == Spi_lGetJobKind(Job) )
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   201      {
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   202        Temp = Spi_AsilJobResult[Index] >>
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   203                     (SPI_JOB_STATUS_BITS * ((uint32)Job & SPI_JOB_BIT_POSITION));
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   204      }
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   205      else
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   206      {
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   207        #if (SPI_QM_MASTER_MODULES_USED != 0U)
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   208        Temp = Spi_QmJobResult[Index] >>
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   209                     (SPI_JOB_STATUS_BITS * ((uint32)Job & SPI_JOB_BIT_POSITION));
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   210        #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   211        /* Do nothing QM modules are NOT Used */
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   212      }
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   213    #else
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   214    {
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   215        Temp = Spi_QmJobResult[Index] >>
	sha	d15,d4,#-4
	movh.a	a15,#@his(Spi_QmJobResult)
	lea	a15,[a15]@los(Spi_QmJobResult)
.L26:
	addsc.a	a15,a15,d15,#2
.L27:

; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   216                     (SPI_JOB_STATUS_BITS * ((uint32)Job & SPI_JOB_BIT_POSITION));
	and	d15,d4,#15
.L28:
	ld.w	d0,[a15]
.L29:
	sh	d15,#1
.L30:
	rsub	d15,#0
	sh	d0,d0,d15
	extr.u	d15,d0,#0,#8
.L31:

; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   217    }
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   218    #endif
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   219  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   220    RetVal = (Spi_JobResultType)(Temp & (uint32)SPI_JOB_STATUS_EXTRACT);
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   221  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   222    return (RetVal);
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   223  }
	and	d2,d15,#3
	ret
.L15:
	
__Spi_lGetJobStatus_function_end:
	.size	Spi_lGetJobStatus,__Spi_lGetJobStatus_function_end-Spi_lGetJobStatus
.L13:
	; End of function
	
	.extern	Spi_QmJobResult
	.calls	'Spi_lGetJobStatus','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L6:
	.word	58048
	.half	3
	.word	.L7
	.byte	4
.L5:
	.byte	1
	.byte	'..\\mcal_src\\spi_infineon_tricore\\src\\Spi_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L8
.L14:
	.byte	2,1,250,6,9,1,3
	.byte	'SPI_JOB_OK',0,0,3
	.byte	'SPI_JOB_PENDING',0,1,3
	.byte	'SPI_JOB_FAILED',0,2,3
	.byte	'SPI_JOB_QUEUED',0,3,0
.L16:
	.byte	4
	.byte	'unsigned short int',0,2,7,5
	.byte	'void',0,6
	.word	295
	.byte	7
	.byte	'__prof_adm',0,2,1,1
	.word	301
	.byte	8,1,6
	.word	325
	.byte	7
	.byte	'__codeptr',0,2,1,1
	.word	327
	.byte	4
	.byte	'unsigned char',0,1,8,7
	.byte	'uint8',0,3,90,29
	.word	350
	.byte	7
	.byte	'uint16',0,3,92,29
	.word	273
	.byte	4
	.byte	'unsigned long int',0,4,7,7
	.byte	'uint32',0,3,94,29
	.word	396
	.byte	9
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,4,45,16,4,10
	.byte	'EN0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	350
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	350
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_DMA_ACCEN00_Bits',0,4,79,3
	.word	432
	.byte	9
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,4,82,16,4,4
	.byte	'unsigned int',0,4,7,10
	.byte	'reserved_0',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_ACCEN01_Bits',0,4,85,3
	.word	991
	.byte	9
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,4,88,16,4,10
	.byte	'EN0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	350
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	350
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_DMA_ACCEN10_Bits',0,4,122,3
	.word	1086
	.byte	9
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,4,125,16,4,10
	.byte	'reserved_0',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_ACCEN11_Bits',0,4,128,1,3
	.word	1645
	.byte	9
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,4,131,1,16,4,10
	.byte	'EN0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	350
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	350
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_DMA_ACCEN20_Bits',0,4,165,1,3
	.word	1725
	.byte	9
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,4,168,1,16,4,10
	.byte	'reserved_0',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_ACCEN21_Bits',0,4,171,1,3
	.word	2286
	.byte	9
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,4,174,1,16,4,10
	.byte	'EN0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	350
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	350
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_DMA_ACCEN30_Bits',0,4,208,1,3
	.word	2367
	.byte	9
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,4,211,1,16,4,10
	.byte	'reserved_0',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_ACCEN31_Bits',0,4,214,1,3
	.word	2928
	.byte	9
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,4,217,1,16,4,10
	.byte	'reserved_0',0,2
	.word	273
	.byte	16,0,2,35,0,10
	.byte	'CSER',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'CDER',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	350
	.byte	2,4,2,35,2,10
	.byte	'CSPBER',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'CSRIER',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'reserved_22',0,1
	.word	350
	.byte	2,0,2,35,2,10
	.byte	'CRAMER',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'CSLLER',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'CDLLER',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	350
	.byte	5,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,4,230,1,3
	.word	3009
	.byte	9
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,4,233,1,16,4,10
	.byte	'reserved_0',0,2
	.word	273
	.byte	16,0,2,35,0,10
	.byte	'ESER',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'EDER',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	350
	.byte	6,0,2,35,2,10
	.byte	'ERER',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'reserved_25',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'ELER',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	350
	.byte	5,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_EER_Bits',0,4,243,1,3
	.word	3283
	.byte	9
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,4,246,1,16,4,10
	.byte	'LEC',0,1
	.word	350
	.byte	7,1,2,35,0,10
	.byte	'reserved_7',0,2
	.word	273
	.byte	9,0,2,35,0,10
	.byte	'SER',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'DER',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	350
	.byte	2,4,2,35,2,10
	.byte	'SPBER',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'SRIER',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'reserved_22',0,1
	.word	350
	.byte	2,0,2,35,2,10
	.byte	'RAMER',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'SLLER',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'DLLER',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	350
	.byte	5,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,4,132,2,3
	.word	3497
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,4,135,2,16,4,10
	.byte	'SMF',0,1
	.word	350
	.byte	3,5,2,35,0,10
	.byte	'INCS',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'DMF',0,1
	.word	350
	.byte	3,1,2,35,0,10
	.byte	'INCD',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'CBLS',0,1
	.word	350
	.byte	4,4,2,35,1,10
	.byte	'CBLD',0,1
	.word	350
	.byte	4,0,2,35,1,10
	.byte	'SHCT',0,1
	.word	350
	.byte	4,4,2,35,2,10
	.byte	'SCBE',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'DCBE',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'STAMP',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'ETRL',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'WRPSE',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'WRPDE',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'INTCT',0,1
	.word	350
	.byte	2,4,2,35,3,10
	.byte	'IRDV',0,1
	.word	350
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,4,152,2,3
	.word	3781
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,4,155,2,16,4,10
	.byte	'TREL',0,2
	.word	273
	.byte	14,2,2,35,0,10
	.byte	'reserved_14',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'BLKM',0,1
	.word	350
	.byte	3,5,2,35,2,10
	.byte	'RROAT',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'CHMODE',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'CHDW',0,1
	.word	350
	.byte	3,0,2,35,2,10
	.byte	'PATSEL',0,1
	.word	350
	.byte	3,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'PRSEL',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'reserved_29',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'DMAPRIO',0,1
	.word	350
	.byte	2,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,4,168,2,3
	.word	4092
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,4,171,2,16,4,10
	.byte	'TCOUNT',0,2
	.word	273
	.byte	14,2,2,35,0,10
	.byte	'reserved_14',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'LXO',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'WRPS',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'WRPD',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'ICH',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'IPM',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'reserved_20',0,1
	.word	350
	.byte	2,2,2,35,2,10
	.byte	'BUFFER',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'FROZEN',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,4,184,2,3
	.word	4365
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,4,187,2,16,4,10
	.byte	'DADR',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,4,190,2,3
	.word	4632
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,4,193,2,16,4,10
	.byte	'RD00',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'RD01',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'RD02',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'RD03',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,4,199,2,3
	.word	4715
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,4,202,2,16,4,10
	.byte	'RD10',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'RD11',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'RD12',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'RD13',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,4,208,2,3
	.word	4842
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,4,211,2,16,4,10
	.byte	'RD20',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'RD21',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'RD22',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'RD23',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,4,217,2,3
	.word	4969
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,4,220,2,16,4,10
	.byte	'RD30',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'RD31',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'RD32',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'RD33',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,4,226,2,3
	.word	5096
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,4,229,2,16,4,10
	.byte	'RD40',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'RD41',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'RD42',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'RD43',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,4,235,2,3
	.word	5223
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,4,238,2,16,4,10
	.byte	'RD50',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'RD51',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'RD52',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'RD53',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,4,244,2,3
	.word	5350
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,4,247,2,16,4,10
	.byte	'RD60',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'RD61',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'RD62',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'RD63',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,4,253,2,3
	.word	5477
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,4,128,3,16,4,10
	.byte	'RD70',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'RD71',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'RD72',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'RD73',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,4,134,3,3
	.word	5604
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,4,137,3,16,4,10
	.byte	'RDCRC',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,4,140,3,3
	.word	5731
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,4,143,3,16,4,10
	.byte	'SADR',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,4,146,3,3
	.word	5817
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,4,149,3,16,4,10
	.byte	'SDCRC',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,4,152,3,3
	.word	5900
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,4,155,3,16,4,10
	.byte	'SHADR',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,4,158,3,3
	.word	5986
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,4,161,3,16,4,10
	.byte	'RS',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	350
	.byte	3,4,2,35,0,10
	.byte	'WS',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'reserved_5',0,2
	.word	273
	.byte	11,0,2,35,0,10
	.byte	'CH',0,1
	.word	350
	.byte	7,1,2,35,2,10
	.byte	'reserved_23',0,2
	.word	273
	.byte	9,0,2,35,2,0,7
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,4,169,3,3
	.word	6072
	.byte	9
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,4,172,3,16,4,10
	.byte	'SMF',0,1
	.word	350
	.byte	3,5,2,35,0,10
	.byte	'INCS',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'DMF',0,1
	.word	350
	.byte	3,1,2,35,0,10
	.byte	'INCD',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'CBLS',0,1
	.word	350
	.byte	4,4,2,35,1,10
	.byte	'CBLD',0,1
	.word	350
	.byte	4,0,2,35,1,10
	.byte	'SHCT',0,1
	.word	350
	.byte	4,4,2,35,2,10
	.byte	'SCBE',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'DCBE',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'STAMP',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'ETRL',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'WRPSE',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'WRPDE',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'INTCT',0,1
	.word	350
	.byte	2,4,2,35,3,10
	.byte	'IRDV',0,1
	.word	350
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,4,189,3,3
	.word	6244
	.byte	9
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,4,192,3,16,4,10
	.byte	'TREL',0,2
	.word	273
	.byte	14,2,2,35,0,10
	.byte	'reserved_14',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'BLKM',0,1
	.word	350
	.byte	3,5,2,35,2,10
	.byte	'RROAT',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'CHMODE',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'CHDW',0,1
	.word	350
	.byte	3,0,2,35,2,10
	.byte	'PATSEL',0,1
	.word	350
	.byte	3,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'PRSEL',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'reserved_29',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'DMAPRIO',0,1
	.word	350
	.byte	2,0,2,35,3,0,7
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,4,205,3,3
	.word	6547
	.byte	9
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,4,208,3,16,4,10
	.byte	'TCOUNT',0,2
	.word	273
	.byte	14,2,2,35,0,10
	.byte	'reserved_14',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'LXO',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'WRPS',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'WRPD',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'ICH',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'IPM',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'reserved_20',0,1
	.word	350
	.byte	2,2,2,35,2,10
	.byte	'BUFFER',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'FROZEN',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'SWB',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'CWRP',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'CICH',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'SIT',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	350
	.byte	3,1,2,35,3,10
	.byte	'SCH',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,4,226,3,3
	.word	6816
	.byte	9
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,4,229,3,16,4,10
	.byte	'DADR',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_CH_DADR_Bits',0,4,232,3,3
	.word	7154
	.byte	9
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,4,235,3,16,4,10
	.byte	'RDCRC',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,4,238,3,3
	.word	7229
	.byte	9
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,4,241,3,16,4,10
	.byte	'SADR',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_CH_SADR_Bits',0,4,244,3,3
	.word	7309
	.byte	9
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,4,247,3,16,4,10
	.byte	'SDCRC',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,4,250,3,3
	.word	7384
	.byte	9
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,4,253,3,16,4,10
	.byte	'SHADR',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,4,128,4,3
	.word	7464
	.byte	9
	.byte	'_Ifx_DMA_CLC_Bits',0,4,131,4,16,4,10
	.byte	'DISR',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'DISS',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'EDIS',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	1018
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_DMA_CLC_Bits',0,4,138,4,3
	.word	7542
	.byte	9
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,4,141,4,16,4,10
	.byte	'SIT',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	1018
	.byte	31,0,2,35,2,0,7
	.byte	'Ifx_DMA_ERRINTR_Bits',0,4,145,4,3
	.word	7685
	.byte	9
	.byte	'_Ifx_DMA_HRR_Bits',0,4,148,4,16,4,10
	.byte	'HRP',0,1
	.word	350
	.byte	2,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	1018
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_DMA_HRR_Bits',0,4,152,4,3
	.word	7781
	.byte	9
	.byte	'_Ifx_DMA_ID_Bits',0,4,155,4,16,4,10
	.byte	'MODREV',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'MODTYPE',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'MODNUMBER',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_DMA_ID_Bits',0,4,160,4,3
	.word	7869
	.byte	9
	.byte	'_Ifx_DMA_MEMCON_Bits',0,4,163,4,16,4,4
	.byte	'unsigned int',0,4,7,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	2,30,2,35,0,10
	.byte	'INTERR',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'RMWERR',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	8003
	.byte	1,26,2,35,0,10
	.byte	'DATAERR',0,4
	.word	8003
	.byte	1,25,2,35,0,10
	.byte	'reserved_7',0,4
	.word	8003
	.byte	1,24,2,35,0,10
	.byte	'PMIC',0,4
	.word	8003
	.byte	1,23,2,35,0,10
	.byte	'ERRDIS',0,4
	.word	8003
	.byte	1,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	8003
	.byte	22,0,2,35,0,0,7
	.byte	'Ifx_DMA_MEMCON_Bits',0,4,175,4,3
	.word	7976
	.byte	9
	.byte	'_Ifx_DMA_MODE_Bits',0,4,178,4,16,4,10
	.byte	'MODE',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	1018
	.byte	31,0,2,35,2,0,7
	.byte	'Ifx_DMA_MODE_Bits',0,4,182,4,3
	.word	8249
	.byte	9
	.byte	'_Ifx_DMA_OTSS_Bits',0,4,185,4,16,4,10
	.byte	'TGS',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	350
	.byte	3,1,2,35,0,10
	.byte	'BS',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	1018
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_DMA_OTSS_Bits',0,4,191,4,3
	.word	8340
	.byte	9
	.byte	'_Ifx_DMA_PRR0_Bits',0,4,194,4,16,4,10
	.byte	'PAT00',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'PAT01',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'PAT02',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'PAT03',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_PRR0_Bits',0,4,200,4,3
	.word	8466
	.byte	9
	.byte	'_Ifx_DMA_PRR1_Bits',0,4,203,4,16,4,10
	.byte	'PAT10',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'PAT11',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'PAT12',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'PAT13',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_DMA_PRR1_Bits',0,4,209,4,3
	.word	8587
	.byte	9
	.byte	'_Ifx_DMA_SUSACR_Bits',0,4,212,4,16,4,10
	.byte	'SUSAC',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	1018
	.byte	31,0,2,35,2,0,7
	.byte	'Ifx_DMA_SUSACR_Bits',0,4,216,4,3
	.word	8708
	.byte	9
	.byte	'_Ifx_DMA_SUSENR_Bits',0,4,219,4,16,4,10
	.byte	'SUSEN',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	1018
	.byte	31,0,2,35,2,0,7
	.byte	'Ifx_DMA_SUSENR_Bits',0,4,223,4,3
	.word	8804
	.byte	9
	.byte	'_Ifx_DMA_TIME_Bits',0,4,226,4,16,4,10
	.byte	'COUNT',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_DMA_TIME_Bits',0,4,229,4,3
	.word	8900
	.byte	9
	.byte	'_Ifx_DMA_TSR_Bits',0,4,232,4,16,4,10
	.byte	'RST',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'HTRE',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'TRL',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'CH',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	350
	.byte	4,0,2,35,0,10
	.byte	'HLTREQ',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'HLTACK',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'reserved_10',0,1
	.word	350
	.byte	6,0,2,35,1,10
	.byte	'ECH',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'DCH',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'CTL',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	350
	.byte	5,0,2,35,2,10
	.byte	'HLTCLR',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'reserved_25',0,1
	.word	350
	.byte	7,0,2,35,3,0,7
	.byte	'Ifx_DMA_TSR_Bits',0,4,248,4,3
	.word	8970
	.byte	11,4,128,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,4
	.byte	'int',0,4,5,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	432
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ACCEN00',0,4,133,5,3
	.word	9271
	.byte	11,4,136,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	991
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ACCEN01',0,4,141,5,3
	.word	9343
	.byte	11,4,144,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	1086
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ACCEN10',0,4,149,5,3
	.word	9408
	.byte	11,4,152,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	1645
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ACCEN11',0,4,157,5,3
	.word	9473
	.byte	11,4,160,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	1725
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ACCEN20',0,4,165,5,3
	.word	9538
	.byte	11,4,168,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	2286
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ACCEN21',0,4,173,5,3
	.word	9603
	.byte	11,4,176,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	2367
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ACCEN30',0,4,181,5,3
	.word	9668
	.byte	11,4,184,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	2928
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ACCEN31',0,4,189,5,3
	.word	9733
	.byte	11,4,192,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3009
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_CLRE',0,4,197,5,3
	.word	9798
	.byte	11,4,200,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3283
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_EER',0,4,205,5,3
	.word	9864
	.byte	11,4,208,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3497
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ERRSR',0,4,213,5,3
	.word	9929
	.byte	11,4,216,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3781
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,4,221,5,3
	.word	9996
	.byte	11,4,224,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	4092
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,4,229,5,3
	.word	10066
	.byte	11,4,232,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	4365
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,4,237,5,3
	.word	10135
	.byte	11,4,240,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	4632
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_DADR',0,4,245,5,3
	.word	10204
	.byte	11,4,248,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	4715
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_R0',0,4,253,5,3
	.word	10273
	.byte	11,4,128,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	4842
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_R1',0,4,133,6,3
	.word	10340
	.byte	11,4,136,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	4969
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_R2',0,4,141,6,3
	.word	10407
	.byte	11,4,144,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5096
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_R3',0,4,149,6,3
	.word	10474
	.byte	11,4,152,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5223
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_R4',0,4,157,6,3
	.word	10541
	.byte	11,4,160,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5350
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_R5',0,4,165,6,3
	.word	10608
	.byte	11,4,168,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5477
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_R6',0,4,173,6,3
	.word	10675
	.byte	11,4,176,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5604
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_R7',0,4,181,6,3
	.word	10742
	.byte	11,4,184,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5731
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,4,189,6,3
	.word	10809
	.byte	11,4,192,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5817
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_SADR',0,4,197,6,3
	.word	10879
	.byte	11,4,200,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5900
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,4,205,6,3
	.word	10948
	.byte	11,4,208,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5986
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,4,213,6,3
	.word	11018
	.byte	11,4,216,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	6072
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_BLK_ME_SR',0,4,221,6,3
	.word	11088
	.byte	11,4,224,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	6244
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CH_ADICR',0,4,229,6,3
	.word	11155
	.byte	11,4,232,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	6547
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CH_CHCFGR',0,4,237,6,3
	.word	11221
	.byte	11,4,240,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	6816
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CH_CHCSR',0,4,245,6,3
	.word	11288
	.byte	11,4,248,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7154
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CH_DADR',0,4,253,6,3
	.word	11354
	.byte	11,4,128,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7229
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CH_RDCRCR',0,4,133,7,3
	.word	11419
	.byte	11,4,136,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7309
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CH_SADR',0,4,141,7,3
	.word	11486
	.byte	11,4,144,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7384
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CH_SDCRCR',0,4,149,7,3
	.word	11551
	.byte	11,4,152,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7464
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CH_SHADR',0,4,157,7,3
	.word	11618
	.byte	11,4,160,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7542
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_CLC',0,4,165,7,3
	.word	11684
	.byte	11,4,168,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7685
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ERRINTR',0,4,173,7,3
	.word	11745
	.byte	11,4,176,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7781
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_HRR',0,4,181,7,3
	.word	11810
	.byte	11,4,184,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7869
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_ID',0,4,189,7,3
	.word	11871
	.byte	11,4,192,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	7976
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_MEMCON',0,4,197,7,3
	.word	11931
	.byte	11,4,200,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	8249
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_MODE',0,4,205,7,3
	.word	11995
	.byte	11,4,208,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	8340
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_OTSS',0,4,213,7,3
	.word	12057
	.byte	11,4,216,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	8466
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_PRR0',0,4,221,7,3
	.word	12119
	.byte	11,4,224,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	8587
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_PRR1',0,4,229,7,3
	.word	12181
	.byte	11,4,232,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	8708
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_SUSACR',0,4,237,7,3
	.word	12243
	.byte	11,4,240,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	8804
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_SUSENR',0,4,245,7,3
	.word	12307
	.byte	11,4,248,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	8900
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_TIME',0,4,253,7,3
	.word	12371
	.byte	11,4,128,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	8970
	.byte	2,35,0,0,7
	.byte	'Ifx_DMA_TSR',0,4,133,8,3
	.word	12433
	.byte	9
	.byte	'_Ifx_DMA_BLK_ME',0,4,144,8,25,112,12
	.byte	'SR',0,4
	.word	11088
	.byte	2,35,0,13,12
	.word	350
	.byte	14,11,0,12
	.byte	'reserved_4',0,12
	.word	12528
	.byte	2,35,4,12
	.byte	'R0',0,4
	.word	10273
	.byte	2,35,16,12
	.byte	'R1',0,4
	.word	10340
	.byte	2,35,20,12
	.byte	'R2',0,4
	.word	10407
	.byte	2,35,24,12
	.byte	'R3',0,4
	.word	10474
	.byte	2,35,28,12
	.byte	'R4',0,4
	.word	10541
	.byte	2,35,32,12
	.byte	'R5',0,4
	.word	10608
	.byte	2,35,36,12
	.byte	'R6',0,4
	.word	10675
	.byte	2,35,40,12
	.byte	'R7',0,4
	.word	10742
	.byte	2,35,44,13,32
	.word	350
	.byte	14,31,0,12
	.byte	'reserved_30',0,32
	.word	12653
	.byte	2,35,48,12
	.byte	'RDCRC',0,4
	.word	10809
	.byte	2,35,80,12
	.byte	'SDCRC',0,4
	.word	10948
	.byte	2,35,84,12
	.byte	'SADR',0,4
	.word	10879
	.byte	2,35,88,12
	.byte	'DADR',0,4
	.word	10204
	.byte	2,35,92,12
	.byte	'ADICR',0,4
	.word	9996
	.byte	2,35,96,12
	.byte	'CHCR',0,4
	.word	10066
	.byte	2,35,100,12
	.byte	'SHADR',0,4
	.word	11018
	.byte	2,35,104,12
	.byte	'CHSR',0,4
	.word	10135
	.byte	2,35,108,0,15
	.word	12494
	.byte	7
	.byte	'Ifx_DMA_BLK_ME',0,4,165,8,3
	.word	12800
	.byte	9
	.byte	'_Ifx_DMA_BLK',0,4,178,8,25,128,1,12
	.byte	'EER',0,4
	.word	9864
	.byte	2,35,0,12
	.byte	'ERRSR',0,4
	.word	9929
	.byte	2,35,4,12
	.byte	'CLRE',0,4
	.word	9798
	.byte	2,35,8,13,4
	.word	350
	.byte	14,3,0,12
	.byte	'reserved_C',0,4
	.word	12891
	.byte	2,35,12,15
	.word	12494
	.byte	12
	.byte	'ME',0,112
	.word	12920
	.byte	2,35,16,0,15
	.word	12829
	.byte	7
	.byte	'Ifx_DMA_BLK',0,4,185,8,3
	.word	12938
	.byte	9
	.byte	'_Ifx_DMA_CH',0,4,188,8,25,32,12
	.byte	'RDCRCR',0,4
	.word	11419
	.byte	2,35,0,12
	.byte	'SDCRCR',0,4
	.word	11551
	.byte	2,35,4,12
	.byte	'SADR',0,4
	.word	11486
	.byte	2,35,8,12
	.byte	'DADR',0,4
	.word	11354
	.byte	2,35,12,12
	.byte	'ADICR',0,4
	.word	11155
	.byte	2,35,16,12
	.byte	'CHCFGR',0,4
	.word	11221
	.byte	2,35,20,12
	.byte	'SHADR',0,4
	.word	11618
	.byte	2,35,24,12
	.byte	'CHCSR',0,4
	.word	11288
	.byte	2,35,28,0,15
	.word	12964
	.byte	7
	.byte	'Ifx_DMA_CH',0,4,198,8,3
	.word	13104
	.byte	9
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,5,45,16,4,10
	.byte	'EN0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	350
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	350
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_ACCEN0_Bits',0,5,79,3
	.word	13129
	.byte	9
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,5,82,16,4,10
	.byte	'reserved_0',0,4
	.word	1018
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_SCU_ACCEN1_Bits',0,5,85,3
	.word	13686
	.byte	9
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,5,88,16,4,10
	.byte	'STM0DIS',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'STM1DIS',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'STM2DIS',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,4
	.word	1018
	.byte	29,0,2,35,2,0,7
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,5,94,3
	.word	13763
	.byte	9
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,5,97,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'BAUD2DIV',0,1
	.word	350
	.byte	4,0,2,35,0,10
	.byte	'SRIDIV',0,1
	.word	350
	.byte	4,4,2,35,1,10
	.byte	'LPDIV',0,1
	.word	350
	.byte	4,0,2,35,1,10
	.byte	'SPBDIV',0,1
	.word	350
	.byte	4,4,2,35,2,10
	.byte	'FSI2DIV',0,1
	.word	350
	.byte	2,2,2,35,2,10
	.byte	'reserved_22',0,1
	.word	350
	.byte	2,0,2,35,2,10
	.byte	'FSIDIV',0,1
	.word	350
	.byte	2,6,2,35,3,10
	.byte	'reserved_26',0,1
	.word	350
	.byte	2,4,2,35,3,10
	.byte	'CLKSEL',0,1
	.word	350
	.byte	2,2,2,35,3,10
	.byte	'UP',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON0_Bits',0,5,111,3
	.word	13899
	.byte	9
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,5,114,16,4,10
	.byte	'CANDIV',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'ERAYDIV',0,1
	.word	350
	.byte	4,0,2,35,0,10
	.byte	'STMDIV',0,1
	.word	350
	.byte	4,4,2,35,1,10
	.byte	'GTMDIV',0,1
	.word	350
	.byte	4,0,2,35,1,10
	.byte	'ETHDIV',0,1
	.word	350
	.byte	4,4,2,35,2,10
	.byte	'ASCLINFDIV',0,1
	.word	350
	.byte	4,0,2,35,2,10
	.byte	'ASCLINSDIV',0,1
	.word	350
	.byte	4,4,2,35,3,10
	.byte	'INSEL',0,1
	.word	350
	.byte	2,2,2,35,3,10
	.byte	'UP',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON1_Bits',0,5,126,3
	.word	14181
	.byte	9
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,5,129,1,16,4,10
	.byte	'BBBDIV',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	1018
	.byte	26,2,2,35,2,10
	.byte	'UP',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON2_Bits',0,5,135,1,3
	.word	14419
	.byte	9
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,5,138,1,16,4,10
	.byte	'PLLDIV',0,1
	.word	350
	.byte	6,2,2,35,0,10
	.byte	'PLLSEL',0,1
	.word	350
	.byte	2,0,2,35,0,10
	.byte	'PLLERAYDIV',0,1
	.word	350
	.byte	6,2,2,35,1,10
	.byte	'PLLERAYSEL',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'SRIDIV',0,1
	.word	350
	.byte	6,2,2,35,2,10
	.byte	'SRISEL',0,1
	.word	350
	.byte	2,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	6,2,2,35,3,10
	.byte	'UP',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON3_Bits',0,5,149,1,3
	.word	14547
	.byte	9
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,5,152,1,16,4,10
	.byte	'SPBDIV',0,1
	.word	350
	.byte	6,2,2,35,0,10
	.byte	'SPBSEL',0,1
	.word	350
	.byte	2,0,2,35,0,10
	.byte	'GTMDIV',0,1
	.word	350
	.byte	6,2,2,35,1,10
	.byte	'GTMSEL',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'STMDIV',0,1
	.word	350
	.byte	6,2,2,35,2,10
	.byte	'STMSEL',0,1
	.word	350
	.byte	2,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	6,2,2,35,3,10
	.byte	'UP',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON4_Bits',0,5,163,1,3
	.word	14774
	.byte	9
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,5,166,1,16,4,10
	.byte	'MAXDIV',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	1018
	.byte	26,2,2,35,2,10
	.byte	'UP',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON5_Bits',0,5,172,1,3
	.word	14993
	.byte	9
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,5,175,1,16,4,10
	.byte	'CPU0DIV',0,1
	.word	350
	.byte	6,2,2,35,0,10
	.byte	'reserved_6',0,4
	.word	1018
	.byte	26,0,2,35,2,0,7
	.byte	'Ifx_SCU_CCUCON6_Bits',0,5,179,1,3
	.word	15121
	.byte	9
	.byte	'_Ifx_SCU_CHIPID_Bits',0,5,182,1,16,4,10
	.byte	'CHREV',0,1
	.word	350
	.byte	6,2,2,35,0,10
	.byte	'CHTEC',0,1
	.word	350
	.byte	2,0,2,35,0,10
	.byte	'CHID',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'EEA',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'UCODE',0,1
	.word	350
	.byte	7,0,2,35,2,10
	.byte	'FSIZE',0,1
	.word	350
	.byte	4,4,2,35,3,10
	.byte	'SP',0,1
	.word	350
	.byte	2,2,2,35,3,10
	.byte	'SEC',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CHIPID_Bits',0,5,193,1,3
	.word	15221
	.byte	9
	.byte	'_Ifx_SCU_DTSCON_Bits',0,5,196,1,16,4,10
	.byte	'PWD',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'START',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	2,4,2,35,0,10
	.byte	'CAL',0,4
	.word	1018
	.byte	22,6,2,35,2,10
	.byte	'reserved_26',0,1
	.word	350
	.byte	5,1,2,35,3,10
	.byte	'SLCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_DTSCON_Bits',0,5,204,1,3
	.word	15429
	.byte	9
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,5,207,1,16,4,10
	.byte	'LOWER',0,2
	.word	273
	.byte	10,6,2,35,0,10
	.byte	'reserved_10',0,1
	.word	350
	.byte	5,1,2,35,1,10
	.byte	'LLU',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'UPPER',0,2
	.word	273
	.byte	10,6,2,35,2,10
	.byte	'reserved_26',0,1
	.word	350
	.byte	4,2,2,35,3,10
	.byte	'SLCK',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'UOF',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_DTSLIM_Bits',0,5,216,1,3
	.word	15594
	.byte	9
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,5,219,1,16,4,10
	.byte	'RESULT',0,2
	.word	273
	.byte	10,6,2,35,0,10
	.byte	'reserved_10',0,1
	.word	350
	.byte	4,2,2,35,1,10
	.byte	'RDY',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'BUSY',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,5,226,1,3
	.word	15777
	.byte	9
	.byte	'_Ifx_SCU_EICR_Bits',0,5,229,1,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'EXIS0',0,1
	.word	350
	.byte	3,1,2,35,0,10
	.byte	'reserved_7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'FEN0',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'REN0',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'LDEN0',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'EIEN0',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'INP0',0,1
	.word	350
	.byte	3,1,2,35,1,10
	.byte	'reserved_15',0,4
	.word	1018
	.byte	5,12,2,35,2,10
	.byte	'EXIS1',0,1
	.word	350
	.byte	3,1,2,35,2,10
	.byte	'reserved_23',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'FEN1',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'REN1',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'LDEN1',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'EIEN1',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'INP1',0,1
	.word	350
	.byte	3,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EICR_Bits',0,5,248,1,3
	.word	15931
	.byte	9
	.byte	'_Ifx_SCU_EIFR_Bits',0,5,251,1,16,4,10
	.byte	'INTF0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'INTF1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'INTF2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'INTF3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'INTF4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'INTF5',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'INTF6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'INTF7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	1018
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_SCU_EIFR_Bits',0,5,134,2,3
	.word	16295
	.byte	9
	.byte	'_Ifx_SCU_EMSR_Bits',0,5,137,2,16,4,10
	.byte	'POL',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'MODE',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'ENON',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'PSEL',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,2
	.word	273
	.byte	12,0,2,35,0,10
	.byte	'EMSF',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'SEMSF',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	350
	.byte	6,0,2,35,2,10
	.byte	'EMSFM',0,1
	.word	350
	.byte	2,6,2,35,3,10
	.byte	'SEMSFM',0,1
	.word	350
	.byte	2,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	350
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_SCU_EMSR_Bits',0,5,150,2,3
	.word	16506
	.byte	9
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,5,153,2,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	7,1,2,35,0,10
	.byte	'EDCON',0,2
	.word	273
	.byte	2,7,2,35,0,10
	.byte	'reserved_9',0,4
	.word	1018
	.byte	23,0,2,35,2,0,7
	.byte	'Ifx_SCU_ESRCFG_Bits',0,5,158,2,3
	.word	16758
	.byte	9
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,5,161,2,16,4,10
	.byte	'ARI',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'ARC',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	1018
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_ESROCFG_Bits',0,5,166,2,3
	.word	16876
	.byte	9
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,5,169,2,16,4,10
	.byte	'reserved_0',0,4
	.word	1018
	.byte	28,4,2,35,2,10
	.byte	'EVR13OFF',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'BPEVR13OFF',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'reserved_30',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVR13CON_Bits',0,5,176,2,3
	.word	16987
	.byte	9
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,5,179,2,16,4,10
	.byte	'ADC13V',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'ADCSWDV',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	7,1,2,35,3,10
	.byte	'VAL',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,5,186,2,3
	.word	17150
	.byte	9
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,5,189,2,16,4,10
	.byte	'EVR13OVMOD',0,1
	.word	350
	.byte	2,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	2,4,2,35,0,10
	.byte	'EVR13UVMOD',0,1
	.word	350
	.byte	2,2,2,35,0,10
	.byte	'reserved_6',0,2
	.word	273
	.byte	10,0,2,35,0,10
	.byte	'SWDOVMOD',0,1
	.word	350
	.byte	2,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	350
	.byte	2,4,2,35,2,10
	.byte	'SWDUVMOD',0,1
	.word	350
	.byte	2,2,2,35,2,10
	.byte	'reserved_22',0,2
	.word	273
	.byte	8,2,2,35,2,10
	.byte	'SLCK',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,5,201,2,3
	.word	17312
	.byte	9
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,5,204,2,16,4,10
	.byte	'EVR13OVVAL',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'SWDOVVAL',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	6,2,2,35,3,10
	.byte	'SLCK',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVROVMON_Bits',0,5,212,2,3
	.word	17590
	.byte	9
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,5,215,2,16,4,10
	.byte	'reserved_0',0,4
	.word	1018
	.byte	28,4,2,35,2,10
	.byte	'RSTSWDOFF',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'BPRSTSWDOFF',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'SLCK',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,5,222,2,3
	.word	17769
	.byte	9
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,5,225,2,16,4,10
	.byte	'SD33P',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	350
	.byte	4,0,2,35,0,10
	.byte	'SD33I',0,1
	.word	350
	.byte	4,4,2,35,1,10
	.byte	'reserved_12',0,4
	.word	1018
	.byte	19,1,2,35,2,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,5,232,2,3
	.word	17929
	.byte	9
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,5,235,2,16,4,10
	.byte	'SDFREQSPRD',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	350
	.byte	4,0,2,35,0,10
	.byte	'TON',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'TOFF',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'SDSTEP',0,1
	.word	350
	.byte	4,4,2,35,3,10
	.byte	'SYNCDIV',0,1
	.word	350
	.byte	3,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,5,244,2,3
	.word	18090
	.byte	9
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,5,247,2,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'STBS',0,1
	.word	350
	.byte	2,6,2,35,1,10
	.byte	'STSP',0,1
	.word	350
	.byte	2,4,2,35,1,10
	.byte	'NS',0,1
	.word	350
	.byte	2,2,2,35,1,10
	.byte	'OL',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'PIAD',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'ADCMODE',0,1
	.word	350
	.byte	4,4,2,35,2,10
	.byte	'ADCLPF',0,1
	.word	350
	.byte	2,2,2,35,2,10
	.byte	'ADCLSB',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'reserved_23',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'SDLUT',0,1
	.word	350
	.byte	6,2,2,35,3,10
	.byte	'reserved_30',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,5,134,3,3
	.word	18282
	.byte	9
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,5,137,3,16,4,10
	.byte	'SDOLCON',0,1
	.word	350
	.byte	7,1,2,35,0,10
	.byte	'MODSEL',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'MODLOW',0,1
	.word	350
	.byte	7,1,2,35,1,10
	.byte	'reserved_15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'SDVOKLVL',0,1
	.word	350
	.byte	6,2,2,35,2,10
	.byte	'MODMAN',0,1
	.word	350
	.byte	2,0,2,35,2,10
	.byte	'MODHIGH',0,1
	.word	350
	.byte	7,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,5,147,3,3
	.word	18578
	.byte	9
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,5,150,3,16,4,10
	.byte	'EVR13',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'OV13',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	2,4,2,35,0,10
	.byte	'OVSWD',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'UV13',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'reserved_6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'UVSWD',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	2,6,2,35,1,10
	.byte	'BGPROK',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'reserved_11',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'SCMOD',0,1
	.word	350
	.byte	2,2,2,35,1,10
	.byte	'reserved_14',0,4
	.word	1018
	.byte	18,0,2,35,2,0,7
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,5,164,3,3
	.word	18793
	.byte	9
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,5,167,3,16,4,10
	.byte	'EVR13UVVAL',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'SWDUVVAL',0,1
	.word	350
	.byte	8,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	6,2,2,35,3,10
	.byte	'SLCK',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,5,175,3,3
	.word	19082
	.byte	9
	.byte	'_Ifx_SCU_EXTCON_Bits',0,5,178,3,16,4,10
	.byte	'EN0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'SEL0',0,1
	.word	350
	.byte	4,2,2,35,0,10
	.byte	'reserved_6',0,2
	.word	273
	.byte	10,0,2,35,0,10
	.byte	'EN1',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'NSEL',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'SEL1',0,1
	.word	350
	.byte	4,2,2,35,2,10
	.byte	'reserved_22',0,1
	.word	350
	.byte	2,0,2,35,2,10
	.byte	'DIV1',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_SCU_EXTCON_Bits',0,5,189,3,3
	.word	19261
	.byte	9
	.byte	'_Ifx_SCU_FDR_Bits',0,5,192,3,16,4,10
	.byte	'STEP',0,2
	.word	273
	.byte	10,6,2,35,0,10
	.byte	'reserved_10',0,1
	.word	350
	.byte	4,2,2,35,1,10
	.byte	'DM',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'RESULT',0,2
	.word	273
	.byte	10,6,2,35,2,10
	.byte	'reserved_26',0,1
	.word	350
	.byte	5,1,2,35,3,10
	.byte	'DISCLK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_FDR_Bits',0,5,200,3,3
	.word	19479
	.byte	9
	.byte	'_Ifx_SCU_FMR_Bits',0,5,203,3,16,4,10
	.byte	'FS0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'FS1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'FS2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'FS3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'FS4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'FS5',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'FS6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'FS7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'FC0',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'FC1',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'FC2',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'FC3',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'FC4',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'FC5',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'FC6',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'FC7',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_SCU_FMR_Bits',0,5,223,3,3
	.word	19642
	.byte	9
	.byte	'_Ifx_SCU_ID_Bits',0,5,226,3,16,4,10
	.byte	'MODREV',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'MODTYPE',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'MODNUMBER',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_ID_Bits',0,5,231,3,3
	.word	19978
	.byte	9
	.byte	'_Ifx_SCU_IGCR_Bits',0,5,234,3,16,4,10
	.byte	'IPEN00',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'IPEN01',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'IPEN02',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'IPEN03',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'IPEN04',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'IPEN05',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'IPEN06',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'IPEN07',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	5,3,2,35,1,10
	.byte	'GEEN0',0,1
	.word	350
	.byte	1,2,2,35,1,10
	.byte	'IGP0',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'IPEN10',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'IPEN11',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'IPEN12',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'IPEN13',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'IPEN14',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'IPEN15',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'IPEN16',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'IPEN17',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	5,3,2,35,3,10
	.byte	'GEEN1',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'IGP1',0,1
	.word	350
	.byte	2,0,2,35,3,0,7
	.byte	'Ifx_SCU_IGCR_Bits',0,5,130,4,3
	.word	20085
	.byte	9
	.byte	'_Ifx_SCU_IN_Bits',0,5,133,4,16,4,10
	.byte	'P0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'P1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	1018
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_IN_Bits',0,5,138,4,3
	.word	20537
	.byte	9
	.byte	'_Ifx_SCU_IOCR_Bits',0,5,141,4,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	4,4,2,35,0,10
	.byte	'PC0',0,1
	.word	350
	.byte	4,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	4,4,2,35,1,10
	.byte	'PC1',0,1
	.word	350
	.byte	4,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_IOCR_Bits',0,5,148,4,3
	.word	20636
	.byte	9
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,5,151,4,16,4,10
	.byte	'LBISTREQ',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'LBISTREQP',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'PATTERNS',0,2
	.word	273
	.byte	14,0,2,35,0,10
	.byte	'reserved_16',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,5,157,4,3
	.word	20786
	.byte	9
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,5,160,4,16,4,10
	.byte	'SEED',0,4
	.word	1018
	.byte	23,9,2,35,2,10
	.byte	'reserved_23',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'SPLITSH',0,1
	.word	350
	.byte	3,5,2,35,3,10
	.byte	'BODY',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'LBISTFREQU',0,1
	.word	350
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,5,167,4,3
	.word	20935
	.byte	9
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,5,170,4,16,4,10
	.byte	'SIGNATURE',0,4
	.word	1018
	.byte	24,8,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	7,1,2,35,3,10
	.byte	'LBISTDONE',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,5,175,4,3
	.word	21096
	.byte	9
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,5,178,4,16,4,10
	.byte	'reserved_0',0,2
	.word	273
	.byte	16,0,2,35,0,10
	.byte	'LS',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'reserved_17',0,2
	.word	273
	.byte	14,1,2,35,2,10
	.byte	'LSEN',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_LCLCON0_Bits',0,5,184,4,3
	.word	21226
	.byte	9
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,5,187,4,16,4,10
	.byte	'LCLT0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'LCLT1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	1018
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_LCLTEST_Bits',0,5,192,4,3
	.word	21360
	.byte	9
	.byte	'_Ifx_SCU_MANID_Bits',0,5,195,4,16,4,10
	.byte	'DEPT',0,1
	.word	350
	.byte	5,3,2,35,0,10
	.byte	'MANUF',0,2
	.word	273
	.byte	11,0,2,35,0,10
	.byte	'reserved_16',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_MANID_Bits',0,5,200,4,3
	.word	21475
	.byte	9
	.byte	'_Ifx_SCU_OMR_Bits',0,5,203,4,16,4,10
	.byte	'PS0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'PS1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,2
	.word	273
	.byte	14,0,2,35,0,10
	.byte	'PCL0',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'PCL1',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,2
	.word	273
	.byte	14,0,2,35,2,0,7
	.byte	'Ifx_SCU_OMR_Bits',0,5,211,4,3
	.word	21586
	.byte	9
	.byte	'_Ifx_SCU_OSCCON_Bits',0,5,214,4,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'PLLLV',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'OSCRES',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'GAINSEL',0,1
	.word	350
	.byte	2,3,2,35,0,10
	.byte	'MODE',0,1
	.word	350
	.byte	2,1,2,35,0,10
	.byte	'SHBY',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'PLLHV',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'reserved_9',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'X1D',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'X1DEN',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'reserved_12',0,1
	.word	350
	.byte	4,0,2,35,1,10
	.byte	'OSCVAL',0,1
	.word	350
	.byte	5,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	350
	.byte	2,1,2,35,2,10
	.byte	'APREN',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_SCU_OSCCON_Bits',0,5,231,4,3
	.word	21744
	.byte	9
	.byte	'_Ifx_SCU_OUT_Bits',0,5,234,4,16,4,10
	.byte	'P0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'P1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	1018
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_OUT_Bits',0,5,239,4,3
	.word	22084
	.byte	9
	.byte	'_Ifx_SCU_OVCCON_Bits',0,5,242,4,16,4,10
	.byte	'CSEL0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'CSEL1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'CSEL2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,2
	.word	273
	.byte	13,0,2,35,0,10
	.byte	'OVSTRT',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'OVSTP',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'DCINVAL',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	350
	.byte	5,0,2,35,2,10
	.byte	'OVCONF',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'POVCONF',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'reserved_26',0,1
	.word	350
	.byte	6,0,2,35,3,0,7
	.byte	'Ifx_SCU_OVCCON_Bits',0,5,255,4,3
	.word	22185
	.byte	9
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,5,130,5,16,4,10
	.byte	'OVEN0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'OVEN1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'OVEN2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,4
	.word	1018
	.byte	29,0,2,35,2,0,7
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,5,136,5,3
	.word	22452
	.byte	9
	.byte	'_Ifx_SCU_PDISC_Bits',0,5,139,5,16,4,10
	.byte	'PDIS0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'PDIS1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	1018
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_PDISC_Bits',0,5,144,5,3
	.word	22588
	.byte	9
	.byte	'_Ifx_SCU_PDR_Bits',0,5,147,5,16,4,10
	.byte	'PD0',0,1
	.word	350
	.byte	3,5,2,35,0,10
	.byte	'PL0',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'PD1',0,1
	.word	350
	.byte	3,1,2,35,0,10
	.byte	'PL1',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	1018
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_SCU_PDR_Bits',0,5,154,5,3
	.word	22699
	.byte	9
	.byte	'_Ifx_SCU_PDRR_Bits',0,5,157,5,16,4,10
	.byte	'PDR0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'PDR1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'PDR2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'PDR3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'PDR4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'PDR5',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'PDR6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'PDR7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	1018
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_SCU_PDRR_Bits',0,5,168,5,3
	.word	22832
	.byte	9
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,5,171,5,16,4,10
	.byte	'VCOBYP',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'VCOPWD',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'MODEN',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'SETFINDIS',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'CLRFINDIS',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'OSCDISCDIS',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'reserved_7',0,2
	.word	273
	.byte	2,7,2,35,0,10
	.byte	'NDIV',0,1
	.word	350
	.byte	7,0,2,35,1,10
	.byte	'PLLPWD',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'reserved_17',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'RESLD',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	350
	.byte	5,0,2,35,2,10
	.byte	'PDIV',0,1
	.word	350
	.byte	4,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	350
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_SCU_PLLCON0_Bits',0,5,188,5,3
	.word	23035
	.byte	9
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,5,191,5,16,4,10
	.byte	'K2DIV',0,1
	.word	350
	.byte	7,1,2,35,0,10
	.byte	'reserved_7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'K3DIV',0,1
	.word	350
	.byte	7,1,2,35,1,10
	.byte	'reserved_15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'K1DIV',0,1
	.word	350
	.byte	7,1,2,35,2,10
	.byte	'reserved_23',0,2
	.word	273
	.byte	9,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLCON1_Bits',0,5,199,5,3
	.word	23391
	.byte	9
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,5,202,5,16,4,10
	.byte	'MODCFG',0,2
	.word	273
	.byte	16,0,2,35,0,10
	.byte	'reserved_16',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLCON2_Bits',0,5,206,5,3
	.word	23569
	.byte	9
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,5,209,5,16,4,10
	.byte	'VCOBYP',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'VCOPWD',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	2,4,2,35,0,10
	.byte	'SETFINDIS',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'CLRFINDIS',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'OSCDISCDIS',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'reserved_7',0,2
	.word	273
	.byte	2,7,2,35,0,10
	.byte	'NDIV',0,1
	.word	350
	.byte	5,2,2,35,1,10
	.byte	'reserved_14',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'PLLPWD',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'reserved_17',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'RESLD',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	350
	.byte	5,0,2,35,2,10
	.byte	'PDIV',0,1
	.word	350
	.byte	4,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	350
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,5,226,5,3
	.word	23669
	.byte	9
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,5,229,5,16,4,10
	.byte	'K2DIV',0,1
	.word	350
	.byte	7,1,2,35,0,10
	.byte	'reserved_7',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'K3DIV',0,1
	.word	350
	.byte	4,4,2,35,1,10
	.byte	'reserved_12',0,1
	.word	350
	.byte	4,0,2,35,1,10
	.byte	'K1DIV',0,1
	.word	350
	.byte	7,1,2,35,2,10
	.byte	'reserved_23',0,2
	.word	273
	.byte	9,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,5,237,5,3
	.word	24039
	.byte	9
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,5,240,5,16,4,10
	.byte	'VCOBYST',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'PWDSTAT',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'VCOLOCK',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'FINDIS',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'K1RDY',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'K2RDY',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'reserved_6',0,4
	.word	1018
	.byte	26,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,5,249,5,3
	.word	24225
	.byte	9
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,5,252,5,16,4,10
	.byte	'VCOBYST',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'VCOLOCK',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'FINDIS',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'K1RDY',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'K2RDY',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'reserved_6',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'MODRUN',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	1018
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,5,135,6,3
	.word	24423
	.byte	9
	.byte	'_Ifx_SCU_PMCSR_Bits',0,5,138,6,16,4,10
	.byte	'REQSLP',0,1
	.word	350
	.byte	2,6,2,35,0,10
	.byte	'SMUSLP',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,1
	.word	350
	.byte	5,0,2,35,0,10
	.byte	'PMST',0,1
	.word	350
	.byte	3,5,2,35,1,10
	.byte	'reserved_11',0,4
	.word	1018
	.byte	21,0,2,35,2,0,7
	.byte	'Ifx_SCU_PMCSR_Bits',0,5,145,6,3
	.word	24656
	.byte	9
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,5,148,6,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'ESR1WKEN',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'PINAWKEN',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'PINBWKEN',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'ESR0DFEN',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'ESR0EDCON',0,1
	.word	350
	.byte	2,1,2,35,0,10
	.byte	'ESR1DFEN',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'ESR1EDCON',0,1
	.word	350
	.byte	2,6,2,35,1,10
	.byte	'PINADFEN',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'PINAEDCON',0,1
	.word	350
	.byte	2,3,2,35,1,10
	.byte	'PINBDFEN',0,1
	.word	350
	.byte	1,2,2,35,1,10
	.byte	'PINBEDCON',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'reserved_16',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'STBYRAMSEL',0,1
	.word	350
	.byte	2,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'WUTWKEN',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	350
	.byte	2,1,2,35,2,10
	.byte	'PORSTDF',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'DCDCSYNC',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'reserved_26',0,1
	.word	350
	.byte	3,3,2,35,3,10
	.byte	'ESR0TRIST',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'reserved_30',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,5,174,6,3
	.word	24808
	.byte	9
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,5,177,6,16,4,10
	.byte	'reserved_0',0,2
	.word	273
	.byte	12,4,2,35,0,10
	.byte	'IRADIS',0,1
	.word	350
	.byte	1,3,2,35,1,10
	.byte	'reserved_13',0,4
	.word	1018
	.byte	14,5,2,35,2,10
	.byte	'STBYEVEN',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'STBYEV',0,1
	.word	350
	.byte	3,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,5,185,6,3
	.word	25367
	.byte	9
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,5,188,6,16,4,10
	.byte	'WUTREL',0,4
	.word	1018
	.byte	24,8,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	4,4,2,35,3,10
	.byte	'WUTDIV',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'WUTEN',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'WUTMODE',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,5,196,6,3
	.word	25550
	.byte	9
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,5,199,6,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	2,6,2,35,0,10
	.byte	'ESR1WKP',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'ESR1OVRUN',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'PINAWKP',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'PINAOVRUN',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'PINBWKP',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'PINBOVRUN',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'PORSTDF',0,1
	.word	350
	.byte	1,6,2,35,1,10
	.byte	'HWCFGEVR',0,1
	.word	350
	.byte	3,3,2,35,1,10
	.byte	'STBYRAM',0,1
	.word	350
	.byte	2,1,2,35,1,10
	.byte	'reserved_15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'WUTWKP',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'WUTOVRUN',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'WUTWKEN',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'ESR1WKEN',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'PINAWKEN',0,1
	.word	350
	.byte	1,2,2,35,2,10
	.byte	'PINBWKEN',0,1
	.word	350
	.byte	1,1,2,35,2,10
	.byte	'reserved_23',0,2
	.word	273
	.byte	4,5,2,35,2,10
	.byte	'ESR0TRIST',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'WUTEN',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'WUTMODE',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'WUTRUN',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,5,226,6,3
	.word	25719
	.byte	9
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,5,229,6,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	2,6,2,35,0,10
	.byte	'ESR1WKPCLR',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'ESR1OVRUNCLR',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'PINAWKPCLR',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'PINAOVRUNCLR',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'PINBWKPCLR',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'PINBOVRUNCLR',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'WUTWKPCLR',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'WUTOVRUNCLR',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,2
	.word	273
	.byte	14,0,2,35,2,0,7
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,5,242,6,3
	.word	26286
	.byte	9
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,5,245,6,16,4,10
	.byte	'WUTCNT',0,4
	.word	1018
	.byte	24,8,2,35,2,10
	.byte	'reserved_24',0,1
	.word	350
	.byte	7,1,2,35,3,10
	.byte	'VAL',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,5,250,6,3
	.word	26602
	.byte	9
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,5,253,6,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'CLRC',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,2
	.word	273
	.byte	10,4,2,35,0,10
	.byte	'CSS0',0,1
	.word	350
	.byte	1,3,2,35,1,10
	.byte	'CSS1',0,1
	.word	350
	.byte	1,2,2,35,1,10
	.byte	'CSS2',0,1
	.word	350
	.byte	1,1,2,35,1,10
	.byte	'reserved_15',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'USRINFO',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_RSTCON2_Bits',0,5,135,7,3
	.word	26721
	.byte	9
	.byte	'_Ifx_SCU_RSTCON_Bits',0,5,138,7,16,4,10
	.byte	'ESR0',0,1
	.word	350
	.byte	2,6,2,35,0,10
	.byte	'ESR1',0,1
	.word	350
	.byte	2,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	350
	.byte	2,2,2,35,0,10
	.byte	'SMU',0,1
	.word	350
	.byte	2,0,2,35,0,10
	.byte	'SW',0,1
	.word	350
	.byte	2,6,2,35,1,10
	.byte	'STM0',0,1
	.word	350
	.byte	2,4,2,35,1,10
	.byte	'STM1',0,1
	.word	350
	.byte	2,2,2,35,1,10
	.byte	'STM2',0,1
	.word	350
	.byte	2,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_RSTCON_Bits',0,5,149,7,3
	.word	26930
	.byte	9
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,5,152,7,16,4,10
	.byte	'ESR0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'ESR1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'SMU',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'SW',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'STM0',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'STM1',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'STM2',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	8,0,2,35,1,10
	.byte	'PORST',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'reserved_17',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'CB0',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'CB1',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'CB3',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	350
	.byte	2,1,2,35,2,10
	.byte	'EVR13',0,1
	.word	350
	.byte	1,0,2,35,2,10
	.byte	'EVR33',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'SWD',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'reserved_26',0,1
	.word	350
	.byte	2,4,2,35,3,10
	.byte	'STBYR',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'reserved_29',0,1
	.word	350
	.byte	3,0,2,35,3,0,7
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,5,175,7,3
	.word	27141
	.byte	9
	.byte	'_Ifx_SCU_SAFECON_Bits',0,5,178,7,16,4,10
	.byte	'HBT',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	1018
	.byte	31,0,2,35,2,0,7
	.byte	'Ifx_SCU_SAFECON_Bits',0,5,182,7,3
	.word	27573
	.byte	9
	.byte	'_Ifx_SCU_STSTAT_Bits',0,5,185,7,16,4,10
	.byte	'HWCFG',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'FTM',0,1
	.word	350
	.byte	7,1,2,35,1,10
	.byte	'MODE',0,1
	.word	350
	.byte	1,0,2,35,1,10
	.byte	'FCBAE',0,1
	.word	350
	.byte	1,7,2,35,2,10
	.byte	'LUDIS',0,1
	.word	350
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	350
	.byte	1,5,2,35,2,10
	.byte	'TRSTL',0,1
	.word	350
	.byte	1,4,2,35,2,10
	.byte	'SPDEN',0,1
	.word	350
	.byte	1,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	350
	.byte	3,0,2,35,2,10
	.byte	'RAMINT',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'reserved_25',0,1
	.word	350
	.byte	7,0,2,35,3,0,7
	.byte	'Ifx_SCU_STSTAT_Bits',0,5,198,7,3
	.word	27669
	.byte	9
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,5,201,7,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'SWRSTREQ',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	1018
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,5,206,7,3
	.word	27929
	.byte	9
	.byte	'_Ifx_SCU_SYSCON_Bits',0,5,209,7,16,4,10
	.byte	'CCTRIG0',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'RAMINTM',0,1
	.word	350
	.byte	2,4,2,35,0,10
	.byte	'SETLUDIS',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'reserved_5',0,1
	.word	350
	.byte	3,0,2,35,0,10
	.byte	'DATM',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'reserved_9',0,4
	.word	1018
	.byte	23,0,2,35,2,0,7
	.byte	'Ifx_SCU_SYSCON_Bits',0,5,218,7,3
	.word	28054
	.byte	9
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,5,221,7,16,4,10
	.byte	'ESR0T',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'ESR1T',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'SMUT',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	1018
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,5,228,7,3
	.word	28251
	.byte	9
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,5,231,7,16,4,10
	.byte	'ESR0T',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'ESR1T',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'SMUT',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	1018
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,5,238,7,3
	.word	28404
	.byte	9
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,5,241,7,16,4,10
	.byte	'ESR0T',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'ESR1T',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'SMUT',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	1018
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_SCU_TRAPSET_Bits',0,5,248,7,3
	.word	28557
	.byte	9
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,5,251,7,16,4,10
	.byte	'ESR0T',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'ESR1T',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'SMUT',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	1018
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,5,130,8,3
	.word	28710
	.byte	9
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,5,133,8,16,4,10
	.byte	'ENDINIT',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'LCK',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'PW',0,4
	.word	8003
	.byte	14,16,2,35,0,10
	.byte	'REL',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,5,139,8,3
	.word	28865
	.byte	9
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,5,142,8,16,4,10
	.byte	'reserved_0',0,1
	.word	350
	.byte	2,6,2,35,0,10
	.byte	'IR0',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'DR',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'IR1',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'UR',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'PAR',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'TCR',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'TCTR',0,1
	.word	350
	.byte	7,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,5,154,8,3
	.word	28995
	.byte	9
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,5,157,8,16,4,10
	.byte	'AE',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'OE',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'IS0',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'DS',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'TO',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'IS1',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'US',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'PAS',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'TCS',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'TCT',0,1
	.word	350
	.byte	7,0,2,35,1,10
	.byte	'TIM',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,5,170,8,3
	.word	29233
	.byte	9
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,5,173,8,16,4,10
	.byte	'ENDINIT',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'LCK',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'PW',0,4
	.word	8003
	.byte	14,16,2,35,0,10
	.byte	'REL',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,5,179,8,3
	.word	29456
	.byte	9
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,5,182,8,16,4,10
	.byte	'CLRIRF',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'IR0',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'DR',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'IR1',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'UR',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'PAR',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'TCR',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'TCTR',0,1
	.word	350
	.byte	7,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,5,195,8,3
	.word	29582
	.byte	9
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,5,198,8,16,4,10
	.byte	'AE',0,1
	.word	350
	.byte	1,7,2,35,0,10
	.byte	'OE',0,1
	.word	350
	.byte	1,6,2,35,0,10
	.byte	'IS0',0,1
	.word	350
	.byte	1,5,2,35,0,10
	.byte	'DS',0,1
	.word	350
	.byte	1,4,2,35,0,10
	.byte	'TO',0,1
	.word	350
	.byte	1,3,2,35,0,10
	.byte	'IS1',0,1
	.word	350
	.byte	1,2,2,35,0,10
	.byte	'US',0,1
	.word	350
	.byte	1,1,2,35,0,10
	.byte	'PAS',0,1
	.word	350
	.byte	1,0,2,35,0,10
	.byte	'TCS',0,1
	.word	350
	.byte	1,7,2,35,1,10
	.byte	'TCT',0,1
	.word	350
	.byte	7,0,2,35,1,10
	.byte	'TIM',0,2
	.word	273
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,5,211,8,3
	.word	29834
	.byte	11,5,219,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13129
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ACCEN0',0,5,224,8,3
	.word	30053
	.byte	11,5,227,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13686
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ACCEN1',0,5,232,8,3
	.word	30117
	.byte	11,5,235,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13763
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ARSTDIS',0,5,240,8,3
	.word	30181
	.byte	11,5,243,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13899
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON0',0,5,248,8,3
	.word	30246
	.byte	11,5,251,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14181
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON1',0,5,128,9,3
	.word	30311
	.byte	11,5,131,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14419
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON2',0,5,136,9,3
	.word	30376
	.byte	11,5,139,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14547
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON3',0,5,144,9,3
	.word	30441
	.byte	11,5,147,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14774
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON4',0,5,152,9,3
	.word	30506
	.byte	11,5,155,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14993
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON5',0,5,160,9,3
	.word	30571
	.byte	11,5,163,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15121
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON6',0,5,168,9,3
	.word	30636
	.byte	11,5,171,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15221
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CHIPID',0,5,176,9,3
	.word	30701
	.byte	11,5,179,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15429
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_DTSCON',0,5,184,9,3
	.word	30765
	.byte	11,5,187,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15594
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_DTSLIM',0,5,192,9,3
	.word	30829
	.byte	11,5,195,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15777
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_DTSSTAT',0,5,200,9,3
	.word	30893
	.byte	11,5,203,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15931
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EICR',0,5,208,9,3
	.word	30958
	.byte	11,5,211,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16295
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EIFR',0,5,216,9,3
	.word	31020
	.byte	11,5,219,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16506
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EMSR',0,5,224,9,3
	.word	31082
	.byte	11,5,227,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16758
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ESRCFG',0,5,232,9,3
	.word	31144
	.byte	11,5,235,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16876
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ESROCFG',0,5,240,9,3
	.word	31208
	.byte	11,5,243,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16987
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVR13CON',0,5,248,9,3
	.word	31273
	.byte	11,5,251,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17150
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRADCSTAT',0,5,128,10,3
	.word	31339
	.byte	11,5,131,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17312
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRMONCTRL',0,5,136,10,3
	.word	31407
	.byte	11,5,139,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17590
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVROVMON',0,5,144,10,3
	.word	31475
	.byte	11,5,147,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17769
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRRSTCON',0,5,152,10,3
	.word	31541
	.byte	11,5,155,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17929
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,5,160,10,3
	.word	31608
	.byte	11,5,163,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18090
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSDCTRL1',0,5,168,10,3
	.word	31677
	.byte	11,5,171,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18282
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSDCTRL2',0,5,176,10,3
	.word	31745
	.byte	11,5,179,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18578
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSDCTRL3',0,5,184,10,3
	.word	31813
	.byte	11,5,187,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18793
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSTAT',0,5,192,10,3
	.word	31881
	.byte	11,5,195,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19082
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRUVMON',0,5,200,10,3
	.word	31946
	.byte	11,5,203,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19261
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EXTCON',0,5,208,10,3
	.word	32012
	.byte	11,5,211,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19479
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_FDR',0,5,216,10,3
	.word	32076
	.byte	11,5,219,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19642
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_FMR',0,5,224,10,3
	.word	32137
	.byte	11,5,227,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19978
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ID',0,5,232,10,3
	.word	32198
	.byte	11,5,235,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20085
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_IGCR',0,5,240,10,3
	.word	32258
	.byte	11,5,243,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20537
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_IN',0,5,248,10,3
	.word	32320
	.byte	11,5,251,10,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20636
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_IOCR',0,5,128,11,3
	.word	32380
	.byte	11,5,131,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20786
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LBISTCTRL0',0,5,136,11,3
	.word	32442
	.byte	11,5,139,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20935
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LBISTCTRL1',0,5,144,11,3
	.word	32510
	.byte	11,5,147,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21096
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LBISTCTRL2',0,5,152,11,3
	.word	32578
	.byte	11,5,155,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21226
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LCLCON0',0,5,160,11,3
	.word	32646
	.byte	11,5,163,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21360
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LCLTEST',0,5,168,11,3
	.word	32711
	.byte	11,5,171,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21475
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_MANID',0,5,176,11,3
	.word	32776
	.byte	11,5,179,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21586
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OMR',0,5,184,11,3
	.word	32839
	.byte	11,5,187,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21744
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OSCCON',0,5,192,11,3
	.word	32900
	.byte	11,5,195,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22084
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OUT',0,5,200,11,3
	.word	32964
	.byte	11,5,203,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22185
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OVCCON',0,5,208,11,3
	.word	33025
	.byte	11,5,211,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22452
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OVCENABLE',0,5,216,11,3
	.word	33089
	.byte	11,5,219,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22588
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PDISC',0,5,224,11,3
	.word	33156
	.byte	11,5,227,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22699
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PDR',0,5,232,11,3
	.word	33219
	.byte	11,5,235,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22832
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PDRR',0,5,240,11,3
	.word	33280
	.byte	11,5,243,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23035
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLCON0',0,5,248,11,3
	.word	33342
	.byte	11,5,251,11,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23391
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLCON1',0,5,128,12,3
	.word	33407
	.byte	11,5,131,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23569
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLCON2',0,5,136,12,3
	.word	33472
	.byte	11,5,139,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23669
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLERAYCON0',0,5,144,12,3
	.word	33537
	.byte	11,5,147,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24039
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLERAYCON1',0,5,152,12,3
	.word	33606
	.byte	11,5,155,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24225
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLERAYSTAT',0,5,160,12,3
	.word	33675
	.byte	11,5,163,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24423
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLSTAT',0,5,168,12,3
	.word	33744
	.byte	11,5,171,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24656
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMCSR',0,5,176,12,3
	.word	33809
	.byte	11,5,179,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24808
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWCR0',0,5,184,12,3
	.word	33872
	.byte	11,5,187,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	25367
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWCR1',0,5,192,12,3
	.word	33937
	.byte	11,5,195,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	25550
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWCR3',0,5,200,12,3
	.word	34002
	.byte	11,5,203,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	25719
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWSTAT',0,5,208,12,3
	.word	34067
	.byte	11,5,211,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	26286
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWSTATCLR',0,5,216,12,3
	.word	34133
	.byte	11,5,219,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	26602
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWUTCNT',0,5,224,12,3
	.word	34202
	.byte	11,5,227,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	26930
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_RSTCON',0,5,232,12,3
	.word	34269
	.byte	11,5,235,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	26721
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_RSTCON2',0,5,240,12,3
	.word	34333
	.byte	11,5,243,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	27141
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_RSTSTAT',0,5,248,12,3
	.word	34398
	.byte	11,5,251,12,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	27573
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_SAFECON',0,5,128,13,3
	.word	34463
	.byte	11,5,131,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	27669
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_STSTAT',0,5,136,13,3
	.word	34528
	.byte	11,5,139,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	27929
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_SWRSTCON',0,5,144,13,3
	.word	34592
	.byte	11,5,147,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	28054
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_SYSCON',0,5,152,13,3
	.word	34658
	.byte	11,5,155,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	28251
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_TRAPCLR',0,5,160,13,3
	.word	34722
	.byte	11,5,163,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	28404
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_TRAPDIS',0,5,168,13,3
	.word	34787
	.byte	11,5,171,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	28557
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_TRAPSET',0,5,176,13,3
	.word	34852
	.byte	11,5,179,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	28710
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_TRAPSTAT',0,5,184,13,3
	.word	34917
	.byte	11,5,187,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	28865
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTCPU_CON0',0,5,192,13,3
	.word	34983
	.byte	11,5,195,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	28995
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTCPU_CON1',0,5,200,13,3
	.word	35052
	.byte	11,5,203,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	29233
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTCPU_SR',0,5,208,13,3
	.word	35121
	.byte	11,5,211,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	29456
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTS_CON0',0,5,216,13,3
	.word	35188
	.byte	11,5,219,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	29582
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTS_CON1',0,5,224,13,3
	.word	35255
	.byte	11,5,227,13,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	29834
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTS_SR',0,5,232,13,3
	.word	35322
	.byte	9
	.byte	'_Ifx_SCU_WDTCPU',0,5,243,13,25,12,12
	.byte	'CON0',0,4
	.word	34983
	.byte	2,35,0,12
	.byte	'CON1',0,4
	.word	35052
	.byte	2,35,4,12
	.byte	'SR',0,4
	.word	35121
	.byte	2,35,8,0,15
	.word	35387
	.byte	7
	.byte	'Ifx_SCU_WDTCPU',0,5,248,13,3
	.word	35450
	.byte	9
	.byte	'_Ifx_SCU_WDTS',0,5,251,13,25,12,12
	.byte	'CON0',0,4
	.word	35188
	.byte	2,35,0,12
	.byte	'CON1',0,4
	.word	35255
	.byte	2,35,4,12
	.byte	'SR',0,4
	.word	35322
	.byte	2,35,8,0,15
	.word	35479
	.byte	7
	.byte	'Ifx_SCU_WDTS',0,5,128,14,3
	.word	35540
	.byte	9
	.byte	'_Ifx_CPU_A_Bits',0,6,45,16,4,10
	.byte	'ADDR',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_A_Bits',0,6,48,3
	.word	35567
	.byte	9
	.byte	'_Ifx_CPU_BIV_Bits',0,6,51,16,4,10
	.byte	'VSS',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'BIV',0,4
	.word	8003
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_BIV_Bits',0,6,55,3
	.word	35628
	.byte	9
	.byte	'_Ifx_CPU_BTV_Bits',0,6,58,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'BTV',0,4
	.word	8003
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_BTV_Bits',0,6,62,3
	.word	35707
	.byte	9
	.byte	'_Ifx_CPU_CCNT_Bits',0,6,65,16,4,10
	.byte	'CountValue',0,4
	.word	8003
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	8003
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_CCNT_Bits',0,6,69,3
	.word	35793
	.byte	9
	.byte	'_Ifx_CPU_CCTRL_Bits',0,6,72,16,4,10
	.byte	'CM',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'CE',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'M1',0,4
	.word	8003
	.byte	3,27,2,35,0,10
	.byte	'M2',0,4
	.word	8003
	.byte	3,24,2,35,0,10
	.byte	'M3',0,4
	.word	8003
	.byte	3,21,2,35,0,10
	.byte	'reserved_11',0,4
	.word	8003
	.byte	21,0,2,35,0,0,7
	.byte	'Ifx_CPU_CCTRL_Bits',0,6,80,3
	.word	35882
	.byte	9
	.byte	'_Ifx_CPU_COMPAT_Bits',0,6,83,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'RM',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'SP',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	8003
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_COMPAT_Bits',0,6,89,3
	.word	36028
	.byte	9
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,6,92,16,4,10
	.byte	'CORE_ID',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	8003
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_CORE_ID_Bits',0,6,96,3
	.word	36155
	.byte	9
	.byte	'_Ifx_CPU_CPR_L_Bits',0,6,99,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'LOWBND',0,4
	.word	8003
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_CPR_L_Bits',0,6,103,3
	.word	36253
	.byte	9
	.byte	'_Ifx_CPU_CPR_U_Bits',0,6,106,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'UPPBND',0,4
	.word	8003
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_CPR_U_Bits',0,6,110,3
	.word	36346
	.byte	9
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,6,113,16,4,10
	.byte	'MODREV',0,4
	.word	8003
	.byte	8,24,2,35,0,10
	.byte	'MOD_32B',0,4
	.word	8003
	.byte	8,16,2,35,0,10
	.byte	'MOD',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_CPU_ID_Bits',0,6,118,3
	.word	36439
	.byte	9
	.byte	'_Ifx_CPU_CPXE_Bits',0,6,121,16,4,10
	.byte	'XE',0,4
	.word	8003
	.byte	8,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	8003
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_CPXE_Bits',0,6,125,3
	.word	36546
	.byte	9
	.byte	'_Ifx_CPU_CREVT_Bits',0,6,128,1,16,4,10
	.byte	'EVTA',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'BBM',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'BOD',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'SUSP',0,4
	.word	8003
	.byte	1,26,2,35,0,10
	.byte	'CNT',0,4
	.word	8003
	.byte	2,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	8003
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_CREVT_Bits',0,6,136,1,3
	.word	36633
	.byte	9
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,6,139,1,16,4,10
	.byte	'CID',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	8003
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_CUS_ID_Bits',0,6,143,1,3
	.word	36787
	.byte	9
	.byte	'_Ifx_CPU_D_Bits',0,6,146,1,16,4,10
	.byte	'DATA',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_D_Bits',0,6,149,1,3
	.word	36881
	.byte	9
	.byte	'_Ifx_CPU_DATR_Bits',0,6,152,1,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'SBE',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	8003
	.byte	5,23,2,35,0,10
	.byte	'CWE',0,4
	.word	8003
	.byte	1,22,2,35,0,10
	.byte	'CFE',0,4
	.word	8003
	.byte	1,21,2,35,0,10
	.byte	'reserved_11',0,4
	.word	8003
	.byte	3,18,2,35,0,10
	.byte	'SOE',0,4
	.word	8003
	.byte	1,17,2,35,0,10
	.byte	'SME',0,4
	.word	8003
	.byte	1,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_DATR_Bits',0,6,163,1,3
	.word	36944
	.byte	9
	.byte	'_Ifx_CPU_DBGSR_Bits',0,6,166,1,16,4,10
	.byte	'DE',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'HALT',0,4
	.word	8003
	.byte	2,29,2,35,0,10
	.byte	'SIH',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'SUSP',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	8003
	.byte	1,26,2,35,0,10
	.byte	'PREVSUSP',0,4
	.word	8003
	.byte	1,25,2,35,0,10
	.byte	'PEVT',0,4
	.word	8003
	.byte	1,24,2,35,0,10
	.byte	'EVTSRC',0,4
	.word	8003
	.byte	5,19,2,35,0,10
	.byte	'reserved_13',0,4
	.word	8003
	.byte	19,0,2,35,0,0,7
	.byte	'Ifx_CPU_DBGSR_Bits',0,6,177,1,3
	.word	37162
	.byte	9
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,6,180,1,16,4,10
	.byte	'DTA',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	8003
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_DBGTCR_Bits',0,6,184,1,3
	.word	37377
	.byte	9
	.byte	'_Ifx_CPU_DCON0_Bits',0,6,187,1,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'DCBYP',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	8003
	.byte	30,0,2,35,0,0,7
	.byte	'Ifx_CPU_DCON0_Bits',0,6,192,1,3
	.word	37471
	.byte	9
	.byte	'_Ifx_CPU_DCON2_Bits',0,6,195,1,16,4,10
	.byte	'DCACHE_SZE',0,4
	.word	8003
	.byte	16,16,2,35,0,10
	.byte	'DSCRATCH_SZE',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_DCON2_Bits',0,6,199,1,3
	.word	37587
	.byte	9
	.byte	'_Ifx_CPU_DCX_Bits',0,6,202,1,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	6,26,2,35,0,10
	.byte	'DCXValue',0,4
	.word	8003
	.byte	26,0,2,35,0,0,7
	.byte	'Ifx_CPU_DCX_Bits',0,6,206,1,3
	.word	37688
	.byte	9
	.byte	'_Ifx_CPU_DEADD_Bits',0,6,209,1,16,4,10
	.byte	'ERROR_ADDRESS',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_DEADD_Bits',0,6,212,1,3
	.word	37781
	.byte	9
	.byte	'_Ifx_CPU_DIEAR_Bits',0,6,215,1,16,4,10
	.byte	'TA',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_DIEAR_Bits',0,6,218,1,3
	.word	37861
	.byte	9
	.byte	'_Ifx_CPU_DIETR_Bits',0,6,221,1,16,4,10
	.byte	'IED',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'IE_T',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'IE_C',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'IE_S',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'IE_BI',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'E_INFO',0,4
	.word	8003
	.byte	6,21,2,35,0,10
	.byte	'IE_DUAL',0,4
	.word	8003
	.byte	1,20,2,35,0,10
	.byte	'IE_SP',0,4
	.word	8003
	.byte	1,19,2,35,0,10
	.byte	'IE_BS',0,4
	.word	8003
	.byte	1,18,2,35,0,10
	.byte	'reserved_14',0,4
	.word	8003
	.byte	18,0,2,35,0,0,7
	.byte	'Ifx_CPU_DIETR_Bits',0,6,233,1,3
	.word	37930
	.byte	9
	.byte	'_Ifx_CPU_DMS_Bits',0,6,236,1,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'DMSValue',0,4
	.word	8003
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_DMS_Bits',0,6,240,1,3
	.word	38159
	.byte	9
	.byte	'_Ifx_CPU_DPR_L_Bits',0,6,243,1,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'LOWBND',0,4
	.word	8003
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_DPR_L_Bits',0,6,247,1,3
	.word	38252
	.byte	9
	.byte	'_Ifx_CPU_DPR_U_Bits',0,6,250,1,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'UPPBND',0,4
	.word	8003
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_DPR_U_Bits',0,6,254,1,3
	.word	38347
	.byte	9
	.byte	'_Ifx_CPU_DPRE_Bits',0,6,129,2,16,4,10
	.byte	'RE',0,4
	.word	8003
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_DPRE_Bits',0,6,133,2,3
	.word	38442
	.byte	9
	.byte	'_Ifx_CPU_DPWE_Bits',0,6,136,2,16,4,10
	.byte	'WE',0,4
	.word	8003
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_DPWE_Bits',0,6,140,2,3
	.word	38532
	.byte	9
	.byte	'_Ifx_CPU_DSTR_Bits',0,6,143,2,16,4,10
	.byte	'SRE',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'GAE',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'LBE',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	8003
	.byte	3,26,2,35,0,10
	.byte	'CRE',0,4
	.word	8003
	.byte	1,25,2,35,0,10
	.byte	'reserved_7',0,4
	.word	8003
	.byte	7,18,2,35,0,10
	.byte	'DTME',0,4
	.word	8003
	.byte	1,17,2,35,0,10
	.byte	'LOE',0,4
	.word	8003
	.byte	1,16,2,35,0,10
	.byte	'SDE',0,4
	.word	8003
	.byte	1,15,2,35,0,10
	.byte	'SCE',0,4
	.word	8003
	.byte	1,14,2,35,0,10
	.byte	'CAC',0,4
	.word	8003
	.byte	1,13,2,35,0,10
	.byte	'MPE',0,4
	.word	8003
	.byte	1,12,2,35,0,10
	.byte	'CLE',0,4
	.word	8003
	.byte	1,11,2,35,0,10
	.byte	'reserved_21',0,4
	.word	8003
	.byte	3,8,2,35,0,10
	.byte	'ALN',0,4
	.word	8003
	.byte	1,7,2,35,0,10
	.byte	'reserved_25',0,4
	.word	8003
	.byte	7,0,2,35,0,0,7
	.byte	'Ifx_CPU_DSTR_Bits',0,6,161,2,3
	.word	38622
	.byte	9
	.byte	'_Ifx_CPU_EXEVT_Bits',0,6,164,2,16,4,10
	.byte	'EVTA',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'BBM',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'BOD',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'SUSP',0,4
	.word	8003
	.byte	1,26,2,35,0,10
	.byte	'CNT',0,4
	.word	8003
	.byte	2,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	8003
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_EXEVT_Bits',0,6,172,2,3
	.word	38946
	.byte	9
	.byte	'_Ifx_CPU_FCX_Bits',0,6,175,2,16,4,10
	.byte	'FCXO',0,4
	.word	8003
	.byte	16,16,2,35,0,10
	.byte	'FCXS',0,4
	.word	8003
	.byte	4,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	8003
	.byte	12,0,2,35,0,0,7
	.byte	'Ifx_CPU_FCX_Bits',0,6,180,2,3
	.word	39100
	.byte	9
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,6,183,2,16,4,10
	.byte	'TST',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'TCL',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	8003
	.byte	6,24,2,35,0,10
	.byte	'RM',0,4
	.word	8003
	.byte	2,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	8003
	.byte	8,14,2,35,0,10
	.byte	'FXE',0,4
	.word	8003
	.byte	1,13,2,35,0,10
	.byte	'FUE',0,4
	.word	8003
	.byte	1,12,2,35,0,10
	.byte	'FZE',0,4
	.word	8003
	.byte	1,11,2,35,0,10
	.byte	'FVE',0,4
	.word	8003
	.byte	1,10,2,35,0,10
	.byte	'FIE',0,4
	.word	8003
	.byte	1,9,2,35,0,10
	.byte	'reserved_23',0,4
	.word	8003
	.byte	3,6,2,35,0,10
	.byte	'FX',0,4
	.word	8003
	.byte	1,5,2,35,0,10
	.byte	'FU',0,4
	.word	8003
	.byte	1,4,2,35,0,10
	.byte	'FZ',0,4
	.word	8003
	.byte	1,3,2,35,0,10
	.byte	'FV',0,4
	.word	8003
	.byte	1,2,2,35,0,10
	.byte	'FI',0,4
	.word	8003
	.byte	1,1,2,35,0,10
	.byte	'reserved_31',0,4
	.word	8003
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,6,202,2,3
	.word	39206
	.byte	9
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,205,2,16,4,10
	.byte	'OPC',0,4
	.word	8003
	.byte	8,24,2,35,0,10
	.byte	'FMT',0,4
	.word	8003
	.byte	1,23,2,35,0,10
	.byte	'reserved_9',0,4
	.word	8003
	.byte	7,16,2,35,0,10
	.byte	'DREG',0,4
	.word	8003
	.byte	4,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	8003
	.byte	12,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,212,2,3
	.word	39555
	.byte	9
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,6,215,2,16,4,10
	.byte	'PC',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,6,218,2,3
	.word	39715
	.byte	9
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,221,2,16,4,10
	.byte	'SRC1',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,224,2,3
	.word	39796
	.byte	9
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,227,2,16,4,10
	.byte	'SRC2',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,230,2,3
	.word	39883
	.byte	9
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,233,2,16,4,10
	.byte	'SRC3',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,236,2,3
	.word	39970
	.byte	9
	.byte	'_Ifx_CPU_ICNT_Bits',0,6,239,2,16,4,10
	.byte	'CountValue',0,4
	.word	8003
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	8003
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_ICNT_Bits',0,6,243,2,3
	.word	40057
	.byte	9
	.byte	'_Ifx_CPU_ICR_Bits',0,6,246,2,16,4,10
	.byte	'CCPN',0,4
	.word	8003
	.byte	10,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	8003
	.byte	5,17,2,35,0,10
	.byte	'IE',0,4
	.word	8003
	.byte	1,16,2,35,0,10
	.byte	'PIPN',0,4
	.word	8003
	.byte	10,6,2,35,0,10
	.byte	'reserved_26',0,4
	.word	8003
	.byte	6,0,2,35,0,0,7
	.byte	'Ifx_CPU_ICR_Bits',0,6,253,2,3
	.word	40148
	.byte	9
	.byte	'_Ifx_CPU_ISP_Bits',0,6,128,3,16,4,10
	.byte	'ISP',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_ISP_Bits',0,6,131,3,3
	.word	40291
	.byte	9
	.byte	'_Ifx_CPU_LCX_Bits',0,6,134,3,16,4,10
	.byte	'LCXO',0,4
	.word	8003
	.byte	16,16,2,35,0,10
	.byte	'LCXS',0,4
	.word	8003
	.byte	4,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	8003
	.byte	12,0,2,35,0,0,7
	.byte	'Ifx_CPU_LCX_Bits',0,6,139,3,3
	.word	40357
	.byte	9
	.byte	'_Ifx_CPU_M1CNT_Bits',0,6,142,3,16,4,10
	.byte	'CountValue',0,4
	.word	8003
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	8003
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_M1CNT_Bits',0,6,146,3,3
	.word	40463
	.byte	9
	.byte	'_Ifx_CPU_M2CNT_Bits',0,6,149,3,16,4,10
	.byte	'CountValue',0,4
	.word	8003
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	8003
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_M2CNT_Bits',0,6,153,3,3
	.word	40556
	.byte	9
	.byte	'_Ifx_CPU_M3CNT_Bits',0,6,156,3,16,4,10
	.byte	'CountValue',0,4
	.word	8003
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	8003
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_M3CNT_Bits',0,6,160,3,3
	.word	40649
	.byte	9
	.byte	'_Ifx_CPU_PC_Bits',0,6,163,3,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'PC',0,4
	.word	8003
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_PC_Bits',0,6,167,3,3
	.word	40742
	.byte	9
	.byte	'_Ifx_CPU_PCON0_Bits',0,6,170,3,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'PCBYP',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	8003
	.byte	30,0,2,35,0,0,7
	.byte	'Ifx_CPU_PCON0_Bits',0,6,175,3,3
	.word	40827
	.byte	9
	.byte	'_Ifx_CPU_PCON1_Bits',0,6,178,3,16,4,10
	.byte	'PCINV',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'PBINV',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	8003
	.byte	30,0,2,35,0,0,7
	.byte	'Ifx_CPU_PCON1_Bits',0,6,183,3,3
	.word	40943
	.byte	9
	.byte	'_Ifx_CPU_PCON2_Bits',0,6,186,3,16,4,10
	.byte	'PCACHE_SZE',0,4
	.word	8003
	.byte	16,16,2,35,0,10
	.byte	'PSCRATCH_SZE',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_PCON2_Bits',0,6,190,3,3
	.word	41054
	.byte	9
	.byte	'_Ifx_CPU_PCXI_Bits',0,6,193,3,16,4,10
	.byte	'PCXO',0,4
	.word	8003
	.byte	16,16,2,35,0,10
	.byte	'PCXS',0,4
	.word	8003
	.byte	4,12,2,35,0,10
	.byte	'UL',0,4
	.word	8003
	.byte	1,11,2,35,0,10
	.byte	'PIE',0,4
	.word	8003
	.byte	1,10,2,35,0,10
	.byte	'PCPN',0,4
	.word	8003
	.byte	10,0,2,35,0,0,7
	.byte	'Ifx_CPU_PCXI_Bits',0,6,200,3,3
	.word	41155
	.byte	9
	.byte	'_Ifx_CPU_PIEAR_Bits',0,6,203,3,16,4,10
	.byte	'TA',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_PIEAR_Bits',0,6,206,3,3
	.word	41285
	.byte	9
	.byte	'_Ifx_CPU_PIETR_Bits',0,6,209,3,16,4,10
	.byte	'IED',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'IE_T',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'IE_C',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'IE_S',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'IE_BI',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'E_INFO',0,4
	.word	8003
	.byte	6,21,2,35,0,10
	.byte	'IE_DUAL',0,4
	.word	8003
	.byte	1,20,2,35,0,10
	.byte	'IE_SP',0,4
	.word	8003
	.byte	1,19,2,35,0,10
	.byte	'IE_BS',0,4
	.word	8003
	.byte	1,18,2,35,0,10
	.byte	'reserved_14',0,4
	.word	8003
	.byte	18,0,2,35,0,0,7
	.byte	'Ifx_CPU_PIETR_Bits',0,6,221,3,3
	.word	41354
	.byte	9
	.byte	'_Ifx_CPU_PMA0_Bits',0,6,224,3,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	13,19,2,35,0,10
	.byte	'DAC',0,4
	.word	8003
	.byte	3,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_PMA0_Bits',0,6,229,3,3
	.word	41583
	.byte	9
	.byte	'_Ifx_CPU_PMA1_Bits',0,6,232,3,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	14,18,2,35,0,10
	.byte	'CAC',0,4
	.word	8003
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_PMA1_Bits',0,6,237,3,3
	.word	41696
	.byte	9
	.byte	'_Ifx_CPU_PMA2_Bits',0,6,240,3,16,4,10
	.byte	'PSI',0,4
	.word	8003
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	8003
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_PMA2_Bits',0,6,244,3,3
	.word	41809
	.byte	9
	.byte	'_Ifx_CPU_PSTR_Bits',0,6,247,3,16,4,10
	.byte	'FRE',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'FBE',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	8003
	.byte	9,20,2,35,0,10
	.byte	'FPE',0,4
	.word	8003
	.byte	1,19,2,35,0,10
	.byte	'reserved_13',0,4
	.word	8003
	.byte	1,18,2,35,0,10
	.byte	'FME',0,4
	.word	8003
	.byte	1,17,2,35,0,10
	.byte	'reserved_15',0,4
	.word	8003
	.byte	17,0,2,35,0,0,7
	.byte	'Ifx_CPU_PSTR_Bits',0,6,129,4,3
	.word	41900
	.byte	9
	.byte	'_Ifx_CPU_PSW_Bits',0,6,132,4,16,4,10
	.byte	'CDC',0,4
	.word	8003
	.byte	7,25,2,35,0,10
	.byte	'CDE',0,4
	.word	8003
	.byte	1,24,2,35,0,10
	.byte	'GW',0,4
	.word	8003
	.byte	1,23,2,35,0,10
	.byte	'IS',0,4
	.word	8003
	.byte	1,22,2,35,0,10
	.byte	'IO',0,4
	.word	8003
	.byte	2,20,2,35,0,10
	.byte	'PRS',0,4
	.word	8003
	.byte	2,18,2,35,0,10
	.byte	'S',0,4
	.word	8003
	.byte	1,17,2,35,0,10
	.byte	'reserved_15',0,4
	.word	8003
	.byte	12,5,2,35,0,10
	.byte	'SAV',0,4
	.word	8003
	.byte	1,4,2,35,0,10
	.byte	'AV',0,4
	.word	8003
	.byte	1,3,2,35,0,10
	.byte	'SV',0,4
	.word	8003
	.byte	1,2,2,35,0,10
	.byte	'V',0,4
	.word	8003
	.byte	1,1,2,35,0,10
	.byte	'C',0,4
	.word	8003
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_PSW_Bits',0,6,147,4,3
	.word	42103
	.byte	9
	.byte	'_Ifx_CPU_SEGEN_Bits',0,6,150,4,16,4,10
	.byte	'ADFLIP',0,4
	.word	8003
	.byte	8,24,2,35,0,10
	.byte	'ADTYPE',0,4
	.word	8003
	.byte	2,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	8003
	.byte	21,1,2,35,0,10
	.byte	'AE',0,4
	.word	8003
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_SEGEN_Bits',0,6,156,4,3
	.word	42346
	.byte	9
	.byte	'_Ifx_CPU_SMACON_Bits',0,6,159,4,16,4,10
	.byte	'PC',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'PT',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	8003
	.byte	5,24,2,35,0,10
	.byte	'DC',0,4
	.word	8003
	.byte	1,23,2,35,0,10
	.byte	'reserved_9',0,4
	.word	8003
	.byte	1,22,2,35,0,10
	.byte	'DT',0,4
	.word	8003
	.byte	1,21,2,35,0,10
	.byte	'reserved_11',0,4
	.word	8003
	.byte	13,8,2,35,0,10
	.byte	'IODT',0,4
	.word	8003
	.byte	1,7,2,35,0,10
	.byte	'reserved_25',0,4
	.word	8003
	.byte	7,0,2,35,0,0,7
	.byte	'Ifx_CPU_SMACON_Bits',0,6,171,4,3
	.word	42474
	.byte	9
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,6,174,4,16,4,10
	.byte	'EN',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,6,177,4,3
	.word	42715
	.byte	9
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,6,180,4,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,6,183,4,3
	.word	42798
	.byte	9
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,186,4,16,4,10
	.byte	'EN',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,189,4,3
	.word	42889
	.byte	9
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,192,4,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,195,4,3
	.word	42980
	.byte	9
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,6,198,4,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	5,27,2,35,0,10
	.byte	'ADDR',0,4
	.word	8003
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,6,202,4,3
	.word	43079
	.byte	9
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,6,205,4,16,4,10
	.byte	'reserved_0',0,4
	.word	8003
	.byte	5,27,2,35,0,10
	.byte	'ADDR',0,4
	.word	8003
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,6,209,4,3
	.word	43186
	.byte	9
	.byte	'_Ifx_CPU_SWEVT_Bits',0,6,212,4,16,4,10
	.byte	'EVTA',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'BBM',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'BOD',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'SUSP',0,4
	.word	8003
	.byte	1,26,2,35,0,10
	.byte	'CNT',0,4
	.word	8003
	.byte	2,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	8003
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_SWEVT_Bits',0,6,220,4,3
	.word	43293
	.byte	9
	.byte	'_Ifx_CPU_SYSCON_Bits',0,6,223,4,16,4,10
	.byte	'FCDSF',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'PROTEN',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'TPROTEN',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'IS',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'IT',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	8003
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_SYSCON_Bits',0,6,231,4,3
	.word	43447
	.byte	9
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,6,234,4,16,4,10
	.byte	'ASI',0,4
	.word	8003
	.byte	5,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	8003
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,6,238,4,3
	.word	43608
	.byte	9
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,6,241,4,16,4,10
	.byte	'TEXP0',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'TEXP1',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'TEXP2',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	8003
	.byte	13,16,2,35,0,10
	.byte	'TTRAP',0,4
	.word	8003
	.byte	1,15,2,35,0,10
	.byte	'reserved_17',0,4
	.word	8003
	.byte	15,0,2,35,0,0,7
	.byte	'Ifx_CPU_TPS_CON_Bits',0,6,249,4,3
	.word	43706
	.byte	9
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,6,252,4,16,4,10
	.byte	'Timer',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,6,255,4,3
	.word	43878
	.byte	9
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,6,130,5,16,4,10
	.byte	'ADDR',0,4
	.word	8003
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_TR_ADR_Bits',0,6,133,5,3
	.word	43958
	.byte	9
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,6,136,5,16,4,10
	.byte	'EVTA',0,4
	.word	8003
	.byte	3,29,2,35,0,10
	.byte	'BBM',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'BOD',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'SUSP',0,4
	.word	8003
	.byte	1,26,2,35,0,10
	.byte	'CNT',0,4
	.word	8003
	.byte	2,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	8003
	.byte	4,20,2,35,0,10
	.byte	'TYP',0,4
	.word	8003
	.byte	1,19,2,35,0,10
	.byte	'RNG',0,4
	.word	8003
	.byte	1,18,2,35,0,10
	.byte	'reserved_14',0,4
	.word	8003
	.byte	1,17,2,35,0,10
	.byte	'ASI_EN',0,4
	.word	8003
	.byte	1,16,2,35,0,10
	.byte	'ASI',0,4
	.word	8003
	.byte	5,11,2,35,0,10
	.byte	'reserved_21',0,4
	.word	8003
	.byte	6,5,2,35,0,10
	.byte	'AST',0,4
	.word	8003
	.byte	1,4,2,35,0,10
	.byte	'ALD',0,4
	.word	8003
	.byte	1,3,2,35,0,10
	.byte	'reserved_29',0,4
	.word	8003
	.byte	3,0,2,35,0,0,7
	.byte	'Ifx_CPU_TR_EVT_Bits',0,6,153,5,3
	.word	44031
	.byte	9
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,6,156,5,16,4,10
	.byte	'T0',0,4
	.word	8003
	.byte	1,31,2,35,0,10
	.byte	'T1',0,4
	.word	8003
	.byte	1,30,2,35,0,10
	.byte	'T2',0,4
	.word	8003
	.byte	1,29,2,35,0,10
	.byte	'T3',0,4
	.word	8003
	.byte	1,28,2,35,0,10
	.byte	'T4',0,4
	.word	8003
	.byte	1,27,2,35,0,10
	.byte	'T5',0,4
	.word	8003
	.byte	1,26,2,35,0,10
	.byte	'T6',0,4
	.word	8003
	.byte	1,25,2,35,0,10
	.byte	'T7',0,4
	.word	8003
	.byte	1,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	8003
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,6,167,5,3
	.word	44349
	.byte	11,6,175,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35567
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_A',0,6,180,5,3
	.word	44544
	.byte	11,6,183,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35628
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_BIV',0,6,188,5,3
	.word	44603
	.byte	11,6,191,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35707
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_BTV',0,6,196,5,3
	.word	44664
	.byte	11,6,199,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35793
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CCNT',0,6,204,5,3
	.word	44725
	.byte	11,6,207,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35882
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CCTRL',0,6,212,5,3
	.word	44787
	.byte	11,6,215,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36028
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_COMPAT',0,6,220,5,3
	.word	44850
	.byte	11,6,223,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36155
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CORE_ID',0,6,228,5,3
	.word	44914
	.byte	11,6,231,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36253
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CPR_L',0,6,236,5,3
	.word	44979
	.byte	11,6,239,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36346
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CPR_U',0,6,244,5,3
	.word	45042
	.byte	11,6,247,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36439
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CPU_ID',0,6,252,5,3
	.word	45105
	.byte	11,6,255,5,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36546
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CPXE',0,6,132,6,3
	.word	45169
	.byte	11,6,135,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36633
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CREVT',0,6,140,6,3
	.word	45231
	.byte	11,6,143,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36787
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CUS_ID',0,6,148,6,3
	.word	45294
	.byte	11,6,151,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36881
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_D',0,6,156,6,3
	.word	45358
	.byte	11,6,159,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36944
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DATR',0,6,164,6,3
	.word	45417
	.byte	11,6,167,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37162
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DBGSR',0,6,172,6,3
	.word	45479
	.byte	11,6,175,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37377
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DBGTCR',0,6,180,6,3
	.word	45542
	.byte	11,6,183,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37471
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DCON0',0,6,188,6,3
	.word	45606
	.byte	11,6,191,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37587
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DCON2',0,6,196,6,3
	.word	45669
	.byte	11,6,199,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37688
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DCX',0,6,204,6,3
	.word	45732
	.byte	11,6,207,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37781
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DEADD',0,6,212,6,3
	.word	45793
	.byte	11,6,215,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37861
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DIEAR',0,6,220,6,3
	.word	45856
	.byte	11,6,223,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37930
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DIETR',0,6,228,6,3
	.word	45919
	.byte	11,6,231,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38159
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DMS',0,6,236,6,3
	.word	45982
	.byte	11,6,239,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38252
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DPR_L',0,6,244,6,3
	.word	46043
	.byte	11,6,247,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38347
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DPR_U',0,6,252,6,3
	.word	46106
	.byte	11,6,255,6,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38442
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DPRE',0,6,132,7,3
	.word	46169
	.byte	11,6,135,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38532
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DPWE',0,6,140,7,3
	.word	46231
	.byte	11,6,143,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38622
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DSTR',0,6,148,7,3
	.word	46293
	.byte	11,6,151,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38946
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_EXEVT',0,6,156,7,3
	.word	46355
	.byte	11,6,159,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39100
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FCX',0,6,164,7,3
	.word	46418
	.byte	11,6,167,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39206
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,6,172,7,3
	.word	46479
	.byte	11,6,175,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39555
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,6,180,7,3
	.word	46549
	.byte	11,6,183,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39715
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,6,188,7,3
	.word	46619
	.byte	11,6,191,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39796
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,6,196,7,3
	.word	46688
	.byte	11,6,199,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39883
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,6,204,7,3
	.word	46759
	.byte	11,6,207,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39970
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,6,212,7,3
	.word	46830
	.byte	11,6,215,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40057
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_ICNT',0,6,220,7,3
	.word	46901
	.byte	11,6,223,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40148
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_ICR',0,6,228,7,3
	.word	46963
	.byte	11,6,231,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40291
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_ISP',0,6,236,7,3
	.word	47024
	.byte	11,6,239,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40357
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_LCX',0,6,244,7,3
	.word	47085
	.byte	11,6,247,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40463
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_M1CNT',0,6,252,7,3
	.word	47146
	.byte	11,6,255,7,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40556
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_M2CNT',0,6,132,8,3
	.word	47209
	.byte	11,6,135,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40649
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_M3CNT',0,6,140,8,3
	.word	47272
	.byte	11,6,143,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40742
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PC',0,6,148,8,3
	.word	47335
	.byte	11,6,151,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40827
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PCON0',0,6,156,8,3
	.word	47395
	.byte	11,6,159,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40943
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PCON1',0,6,164,8,3
	.word	47458
	.byte	11,6,167,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	41054
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PCON2',0,6,172,8,3
	.word	47521
	.byte	11,6,175,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	41155
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PCXI',0,6,180,8,3
	.word	47584
	.byte	11,6,183,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	41285
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PIEAR',0,6,188,8,3
	.word	47646
	.byte	11,6,191,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	41354
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PIETR',0,6,196,8,3
	.word	47709
	.byte	11,6,199,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	41583
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PMA0',0,6,204,8,3
	.word	47772
	.byte	11,6,207,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	41696
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PMA1',0,6,212,8,3
	.word	47834
	.byte	11,6,215,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	41809
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PMA2',0,6,220,8,3
	.word	47896
	.byte	11,6,223,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	41900
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PSTR',0,6,228,8,3
	.word	47958
	.byte	11,6,231,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	42103
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PSW',0,6,236,8,3
	.word	48020
	.byte	11,6,239,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	42346
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SEGEN',0,6,244,8,3
	.word	48081
	.byte	11,6,247,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	42474
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SMACON',0,6,252,8,3
	.word	48144
	.byte	11,6,255,8,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	42715
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_ACCENA',0,6,132,9,3
	.word	48208
	.byte	11,6,135,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	42798
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_ACCENB',0,6,140,9,3
	.word	48278
	.byte	11,6,143,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	42889
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,6,148,9,3
	.word	48348
	.byte	11,6,151,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	42980
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,6,156,9,3
	.word	48422
	.byte	11,6,159,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	43079
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,6,164,9,3
	.word	48496
	.byte	11,6,167,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	43186
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,6,172,9,3
	.word	48566
	.byte	11,6,175,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	43293
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SWEVT',0,6,180,9,3
	.word	48636
	.byte	11,6,183,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	43447
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SYSCON',0,6,188,9,3
	.word	48699
	.byte	11,6,191,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	43608
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TASK_ASI',0,6,196,9,3
	.word	48763
	.byte	11,6,199,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	43706
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TPS_CON',0,6,204,9,3
	.word	48829
	.byte	11,6,207,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	43878
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TPS_TIMER',0,6,212,9,3
	.word	48894
	.byte	11,6,215,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	43958
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TR_ADR',0,6,220,9,3
	.word	48961
	.byte	11,6,223,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	44031
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TR_EVT',0,6,228,9,3
	.word	49025
	.byte	11,6,231,9,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	44349
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TRIG_ACC',0,6,236,9,3
	.word	49089
	.byte	9
	.byte	'_Ifx_CPU_CPR',0,6,247,9,25,8,12
	.byte	'L',0,4
	.word	44979
	.byte	2,35,0,12
	.byte	'U',0,4
	.word	45042
	.byte	2,35,4,0,15
	.word	49155
	.byte	7
	.byte	'Ifx_CPU_CPR',0,6,251,9,3
	.word	49197
	.byte	9
	.byte	'_Ifx_CPU_DPR',0,6,254,9,25,8,12
	.byte	'L',0,4
	.word	46043
	.byte	2,35,0,12
	.byte	'U',0,4
	.word	46106
	.byte	2,35,4,0,15
	.word	49223
	.byte	7
	.byte	'Ifx_CPU_DPR',0,6,130,10,3
	.word	49265
	.byte	9
	.byte	'_Ifx_CPU_SPROT_RGN',0,6,133,10,25,16,12
	.byte	'LA',0,4
	.word	48496
	.byte	2,35,0,12
	.byte	'UA',0,4
	.word	48566
	.byte	2,35,4,12
	.byte	'ACCENA',0,4
	.word	48348
	.byte	2,35,8,12
	.byte	'ACCENB',0,4
	.word	48422
	.byte	2,35,12,0,15
	.word	49291
	.byte	7
	.byte	'Ifx_CPU_SPROT_RGN',0,6,139,10,3
	.word	49373
	.byte	9
	.byte	'_Ifx_CPU_TPS',0,6,142,10,25,16,12
	.byte	'CON',0,4
	.word	48829
	.byte	2,35,0,13,12
	.word	48894
	.byte	14,2,0,12
	.byte	'TIMER',0,12
	.word	49437
	.byte	2,35,4,0,15
	.word	49405
	.byte	7
	.byte	'Ifx_CPU_TPS',0,6,146,10,3
	.word	49462
	.byte	9
	.byte	'_Ifx_CPU_TR',0,6,149,10,25,8,12
	.byte	'EVT',0,4
	.word	49025
	.byte	2,35,0,12
	.byte	'ADR',0,4
	.word	48961
	.byte	2,35,4,0,15
	.word	49488
	.byte	7
	.byte	'Ifx_CPU_TR',0,6,153,10,3
	.word	49533
	.byte	9
	.byte	'_Ifx_SRC_SRCR_Bits',0,7,45,16,4,10
	.byte	'SRPN',0,1
	.word	350
	.byte	8,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	350
	.byte	2,6,2,35,1,10
	.byte	'SRE',0,1
	.word	350
	.byte	1,5,2,35,1,10
	.byte	'TOS',0,1
	.word	350
	.byte	1,4,2,35,1,10
	.byte	'reserved_12',0,1
	.word	350
	.byte	4,0,2,35,1,10
	.byte	'ECC',0,1
	.word	350
	.byte	5,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	350
	.byte	3,0,2,35,2,10
	.byte	'SRR',0,1
	.word	350
	.byte	1,7,2,35,3,10
	.byte	'CLRR',0,1
	.word	350
	.byte	1,6,2,35,3,10
	.byte	'SETR',0,1
	.word	350
	.byte	1,5,2,35,3,10
	.byte	'IOV',0,1
	.word	350
	.byte	1,4,2,35,3,10
	.byte	'IOVCLR',0,1
	.word	350
	.byte	1,3,2,35,3,10
	.byte	'SWS',0,1
	.word	350
	.byte	1,2,2,35,3,10
	.byte	'SWSCLR',0,1
	.word	350
	.byte	1,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	350
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SRC_SRCR_Bits',0,7,62,3
	.word	49558
	.byte	11,7,70,9,4,12
	.byte	'U',0,4
	.word	1018
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	9288
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	49558
	.byte	2,35,0,0,7
	.byte	'Ifx_SRC_SRCR',0,7,75,3
	.word	49874
	.byte	9
	.byte	'_Ifx_SRC_ASCLIN',0,7,86,25,12,12
	.byte	'TX',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'RX',0,4
	.word	49874
	.byte	2,35,4,12
	.byte	'ERR',0,4
	.word	49874
	.byte	2,35,8,0,15
	.word	49934
	.byte	7
	.byte	'Ifx_SRC_ASCLIN',0,7,91,3
	.word	49993
	.byte	9
	.byte	'_Ifx_SRC_BCUSPB',0,7,94,25,4,12
	.byte	'SBSRC',0,4
	.word	49874
	.byte	2,35,0,0,15
	.word	50021
	.byte	7
	.byte	'Ifx_SRC_BCUSPB',0,7,97,3
	.word	50058
	.byte	9
	.byte	'_Ifx_SRC_CAN',0,7,100,25,64,13,64
	.word	49874
	.byte	14,15,0,12
	.byte	'INT',0,64
	.word	50104
	.byte	2,35,0,0,15
	.word	50086
	.byte	7
	.byte	'Ifx_SRC_CAN',0,7,103,3
	.word	50127
	.byte	9
	.byte	'_Ifx_SRC_CAN1',0,7,106,25,32,13,32
	.word	49874
	.byte	14,7,0,12
	.byte	'INT',0,32
	.word	50171
	.byte	2,35,0,0,15
	.word	50152
	.byte	7
	.byte	'Ifx_SRC_CAN1',0,7,109,3
	.word	50194
	.byte	9
	.byte	'_Ifx_SRC_CCU6',0,7,112,25,16,12
	.byte	'SR0',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'SR1',0,4
	.word	49874
	.byte	2,35,4,12
	.byte	'SR2',0,4
	.word	49874
	.byte	2,35,8,12
	.byte	'SR3',0,4
	.word	49874
	.byte	2,35,12,0,15
	.word	50220
	.byte	7
	.byte	'Ifx_SRC_CCU6',0,7,118,3
	.word	50292
	.byte	9
	.byte	'_Ifx_SRC_CERBERUS',0,7,121,25,8,13,8
	.word	49874
	.byte	14,1,0,12
	.byte	'SR',0,8
	.word	50341
	.byte	2,35,0,0,15
	.word	50318
	.byte	7
	.byte	'Ifx_SRC_CERBERUS',0,7,124,3
	.word	50363
	.byte	9
	.byte	'_Ifx_SRC_CPU',0,7,127,25,32,12
	.byte	'SBSRC',0,4
	.word	49874
	.byte	2,35,0,13,28
	.word	350
	.byte	14,27,0,12
	.byte	'reserved_4',0,28
	.word	50426
	.byte	2,35,4,0,15
	.word	50393
	.byte	7
	.byte	'Ifx_SRC_CPU',0,7,131,1,3
	.word	50456
	.byte	9
	.byte	'_Ifx_SRC_DMA',0,7,134,1,25,80,12
	.byte	'ERR',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'reserved_4',0,12
	.word	12528
	.byte	2,35,4,12
	.byte	'CH',0,64
	.word	50104
	.byte	2,35,16,0,15
	.word	50482
	.byte	7
	.byte	'Ifx_SRC_DMA',0,7,139,1,3
	.word	50547
	.byte	9
	.byte	'_Ifx_SRC_EMEM',0,7,142,1,25,4,12
	.byte	'SR',0,4
	.word	49874
	.byte	2,35,0,0,15
	.word	50573
	.byte	7
	.byte	'Ifx_SRC_EMEM',0,7,145,1,3
	.word	50606
	.byte	9
	.byte	'_Ifx_SRC_ERAY',0,7,148,1,25,80,12
	.byte	'INT',0,8
	.word	50341
	.byte	2,35,0,12
	.byte	'TINT',0,8
	.word	50341
	.byte	2,35,8,12
	.byte	'NDAT',0,8
	.word	50341
	.byte	2,35,16,12
	.byte	'MBSC',0,8
	.word	50341
	.byte	2,35,24,12
	.byte	'OBUSY',0,4
	.word	49874
	.byte	2,35,32,12
	.byte	'IBUSY',0,4
	.word	49874
	.byte	2,35,36,13,40
	.word	350
	.byte	14,39,0,12
	.byte	'reserved_28',0,40
	.word	50738
	.byte	2,35,40,0,15
	.word	50633
	.byte	7
	.byte	'Ifx_SRC_ERAY',0,7,157,1,3
	.word	50769
	.byte	9
	.byte	'_Ifx_SRC_ETH',0,7,160,1,25,4,12
	.byte	'SR',0,4
	.word	49874
	.byte	2,35,0,0,15
	.word	50796
	.byte	7
	.byte	'Ifx_SRC_ETH',0,7,163,1,3
	.word	50828
	.byte	9
	.byte	'_Ifx_SRC_EVR',0,7,166,1,25,8,12
	.byte	'WUT',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'SCDC',0,4
	.word	49874
	.byte	2,35,4,0,15
	.word	50854
	.byte	7
	.byte	'Ifx_SRC_EVR',0,7,170,1,3
	.word	50901
	.byte	9
	.byte	'_Ifx_SRC_FFT',0,7,173,1,25,12,12
	.byte	'DONE',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'ERR',0,4
	.word	49874
	.byte	2,35,4,12
	.byte	'RFS',0,4
	.word	49874
	.byte	2,35,8,0,15
	.word	50927
	.byte	7
	.byte	'Ifx_SRC_FFT',0,7,178,1,3
	.word	50987
	.byte	9
	.byte	'_Ifx_SRC_GPSR',0,7,181,1,25,128,12,12
	.byte	'SR0',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'SR1',0,4
	.word	49874
	.byte	2,35,4,12
	.byte	'SR2',0,4
	.word	49874
	.byte	2,35,8,12
	.byte	'SR3',0,4
	.word	49874
	.byte	2,35,12,13,240,11
	.word	350
	.byte	14,239,11,0,12
	.byte	'reserved_10',0,240,11
	.word	51086
	.byte	2,35,16,0,15
	.word	51013
	.byte	7
	.byte	'Ifx_SRC_GPSR',0,7,188,1,3
	.word	51120
	.byte	9
	.byte	'_Ifx_SRC_GPT12',0,7,191,1,25,48,12
	.byte	'CIRQ',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'T2',0,4
	.word	49874
	.byte	2,35,4,12
	.byte	'T3',0,4
	.word	49874
	.byte	2,35,8,12
	.byte	'T4',0,4
	.word	49874
	.byte	2,35,12,12
	.byte	'T5',0,4
	.word	49874
	.byte	2,35,16,12
	.byte	'T6',0,4
	.word	49874
	.byte	2,35,20,13,24
	.word	350
	.byte	14,23,0,12
	.byte	'reserved_18',0,24
	.word	51242
	.byte	2,35,24,0,15
	.word	51147
	.byte	7
	.byte	'Ifx_SRC_GPT12',0,7,200,1,3
	.word	51273
	.byte	9
	.byte	'_Ifx_SRC_GTM',0,7,203,1,25,192,11,12
	.byte	'AEIIRQ',0,4
	.word	49874
	.byte	2,35,0,13,236,2
	.word	350
	.byte	14,235,2,0,12
	.byte	'reserved_4',0,236,2
	.word	51337
	.byte	2,35,4,12
	.byte	'ERR',0,4
	.word	49874
	.byte	3,35,240,2,12
	.byte	'reserved_174',0,12
	.word	12528
	.byte	3,35,244,2,13,32
	.word	50171
	.byte	14,0,0,12
	.byte	'TIM',0,32
	.word	51406
	.byte	3,35,128,3,13,224,7
	.word	350
	.byte	14,223,7,0,12
	.byte	'reserved_1A0',0,224,7
	.word	51429
	.byte	3,35,160,3,13,64
	.word	50171
	.byte	14,1,0,12
	.byte	'TOM',0,64
	.word	51464
	.byte	3,35,128,11,0,15
	.word	51301
	.byte	7
	.byte	'Ifx_SRC_GTM',0,7,212,1,3
	.word	51488
	.byte	9
	.byte	'_Ifx_SRC_HSM',0,7,215,1,25,8,12
	.byte	'HSM',0,8
	.word	50341
	.byte	2,35,0,0,15
	.word	51514
	.byte	7
	.byte	'Ifx_SRC_HSM',0,7,218,1,3
	.word	51547
	.byte	9
	.byte	'_Ifx_SRC_LMU',0,7,221,1,25,4,12
	.byte	'SR',0,4
	.word	49874
	.byte	2,35,0,0,15
	.word	51573
	.byte	7
	.byte	'Ifx_SRC_LMU',0,7,224,1,3
	.word	51605
	.byte	9
	.byte	'_Ifx_SRC_PMU',0,7,227,1,25,4,12
	.byte	'SR',0,4
	.word	49874
	.byte	2,35,0,0,15
	.word	51631
	.byte	7
	.byte	'Ifx_SRC_PMU',0,7,230,1,3
	.word	51663
	.byte	9
	.byte	'_Ifx_SRC_QSPI',0,7,233,1,25,24,12
	.byte	'TX',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'RX',0,4
	.word	49874
	.byte	2,35,4,12
	.byte	'ERR',0,4
	.word	49874
	.byte	2,35,8,12
	.byte	'PT',0,4
	.word	49874
	.byte	2,35,12,12
	.byte	'HC',0,4
	.word	49874
	.byte	2,35,16,12
	.byte	'U',0,4
	.word	49874
	.byte	2,35,20,0,15
	.word	51689
	.byte	7
	.byte	'Ifx_SRC_QSPI',0,7,241,1,3
	.word	51782
	.byte	9
	.byte	'_Ifx_SRC_SCU',0,7,244,1,25,20,12
	.byte	'DTS',0,4
	.word	49874
	.byte	2,35,0,13,16
	.word	49874
	.byte	14,3,0,12
	.byte	'ERU',0,16
	.word	51841
	.byte	2,35,4,0,15
	.word	51809
	.byte	7
	.byte	'Ifx_SRC_SCU',0,7,248,1,3
	.word	51864
	.byte	9
	.byte	'_Ifx_SRC_SENT',0,7,251,1,25,16,12
	.byte	'SR',0,16
	.word	51841
	.byte	2,35,0,0,15
	.word	51890
	.byte	7
	.byte	'Ifx_SRC_SENT',0,7,254,1,3
	.word	51923
	.byte	9
	.byte	'_Ifx_SRC_SMU',0,7,129,2,25,12,13,12
	.word	49874
	.byte	14,2,0,12
	.byte	'SR',0,12
	.word	51969
	.byte	2,35,0,0,15
	.word	51950
	.byte	7
	.byte	'Ifx_SRC_SMU',0,7,132,2,3
	.word	51991
	.byte	9
	.byte	'_Ifx_SRC_STM',0,7,135,2,25,96,12
	.byte	'SR0',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'SR1',0,4
	.word	49874
	.byte	2,35,4,13,88
	.word	350
	.byte	14,87,0,12
	.byte	'reserved_8',0,88
	.word	52062
	.byte	2,35,8,0,15
	.word	52017
	.byte	7
	.byte	'Ifx_SRC_STM',0,7,140,2,3
	.word	52092
	.byte	9
	.byte	'_Ifx_SRC_VADCCG',0,7,143,2,25,192,2,12
	.byte	'SR0',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'SR1',0,4
	.word	49874
	.byte	2,35,4,12
	.byte	'SR2',0,4
	.word	49874
	.byte	2,35,8,12
	.byte	'SR3',0,4
	.word	49874
	.byte	2,35,12,13,176,2
	.word	350
	.byte	14,175,2,0,12
	.byte	'reserved_10',0,176,2
	.word	52193
	.byte	2,35,16,0,15
	.word	52118
	.byte	7
	.byte	'Ifx_SRC_VADCCG',0,7,150,2,3
	.word	52227
	.byte	9
	.byte	'_Ifx_SRC_VADCG',0,7,153,2,25,16,12
	.byte	'SR0',0,4
	.word	49874
	.byte	2,35,0,12
	.byte	'SR1',0,4
	.word	49874
	.byte	2,35,4,12
	.byte	'SR2',0,4
	.word	49874
	.byte	2,35,8,12
	.byte	'SR3',0,4
	.word	49874
	.byte	2,35,12,0,15
	.word	52256
	.byte	7
	.byte	'Ifx_SRC_VADCG',0,7,159,2,3
	.word	52330
	.byte	9
	.byte	'_Ifx_SRC_XBAR',0,7,162,2,25,4,12
	.byte	'SRC',0,4
	.word	49874
	.byte	2,35,0,0,15
	.word	52358
	.byte	7
	.byte	'Ifx_SRC_XBAR',0,7,165,2,3
	.word	52392
	.byte	9
	.byte	'_Ifx_SRC_GASCLIN',0,7,178,2,25,24,13,24
	.word	49934
	.byte	14,1,0,15
	.word	52442
	.byte	12
	.byte	'ASCLIN',0,24
	.word	52451
	.byte	2,35,0,0,15
	.word	52419
	.byte	7
	.byte	'Ifx_SRC_GASCLIN',0,7,181,2,3
	.word	52473
	.byte	9
	.byte	'_Ifx_SRC_GBCU',0,7,184,2,25,4,15
	.word	50021
	.byte	12
	.byte	'SPB',0,4
	.word	52523
	.byte	2,35,0,0,15
	.word	52503
	.byte	7
	.byte	'Ifx_SRC_GBCU',0,7,187,2,3
	.word	52542
	.byte	9
	.byte	'_Ifx_SRC_GCAN',0,7,190,2,25,96,13,64
	.word	50086
	.byte	14,0,0,15
	.word	52589
	.byte	12
	.byte	'CAN',0,64
	.word	52598
	.byte	2,35,0,13,32
	.word	50152
	.byte	14,0,0,15
	.word	52616
	.byte	12
	.byte	'CAN1',0,32
	.word	52625
	.byte	2,35,64,0,15
	.word	52569
	.byte	7
	.byte	'Ifx_SRC_GCAN',0,7,194,2,3
	.word	52645
	.byte	9
	.byte	'_Ifx_SRC_GCCU6',0,7,197,2,25,32,13,32
	.word	50220
	.byte	14,1,0,15
	.word	52693
	.byte	12
	.byte	'CCU6',0,32
	.word	52702
	.byte	2,35,0,0,15
	.word	52672
	.byte	7
	.byte	'Ifx_SRC_GCCU6',0,7,200,2,3
	.word	52722
	.byte	9
	.byte	'_Ifx_SRC_GCERBERUS',0,7,203,2,25,8,15
	.word	50318
	.byte	12
	.byte	'CERBERUS',0,8
	.word	52775
	.byte	2,35,0,0,15
	.word	52750
	.byte	7
	.byte	'Ifx_SRC_GCERBERUS',0,7,206,2,3
	.word	52799
	.byte	9
	.byte	'_Ifx_SRC_GCPU',0,7,209,2,25,32,13,32
	.word	50393
	.byte	14,0,0,15
	.word	52851
	.byte	12
	.byte	'CPU',0,32
	.word	52860
	.byte	2,35,0,0,15
	.word	52831
	.byte	7
	.byte	'Ifx_SRC_GCPU',0,7,212,2,3
	.word	52879
	.byte	9
	.byte	'_Ifx_SRC_GDMA',0,7,215,2,25,80,13,80
	.word	50482
	.byte	14,0,0,15
	.word	52926
	.byte	12
	.byte	'DMA',0,80
	.word	52935
	.byte	2,35,0,0,15
	.word	52906
	.byte	7
	.byte	'Ifx_SRC_GDMA',0,7,218,2,3
	.word	52954
	.byte	9
	.byte	'_Ifx_SRC_GEMEM',0,7,221,2,25,4,13,4
	.word	50573
	.byte	14,0,0,15
	.word	53002
	.byte	12
	.byte	'EMEM',0,4
	.word	53011
	.byte	2,35,0,0,15
	.word	52981
	.byte	7
	.byte	'Ifx_SRC_GEMEM',0,7,224,2,3
	.word	53031
	.byte	9
	.byte	'_Ifx_SRC_GERAY',0,7,227,2,25,80,13,80
	.word	50633
	.byte	14,0,0,15
	.word	53080
	.byte	12
	.byte	'ERAY',0,80
	.word	53089
	.byte	2,35,0,0,15
	.word	53059
	.byte	7
	.byte	'Ifx_SRC_GERAY',0,7,230,2,3
	.word	53109
	.byte	9
	.byte	'_Ifx_SRC_GETH',0,7,233,2,25,4,13,4
	.word	50796
	.byte	14,0,0,15
	.word	53157
	.byte	12
	.byte	'ETH',0,4
	.word	53166
	.byte	2,35,0,0,15
	.word	53137
	.byte	7
	.byte	'Ifx_SRC_GETH',0,7,236,2,3
	.word	53185
	.byte	9
	.byte	'_Ifx_SRC_GEVR',0,7,239,2,25,8,13,8
	.word	50854
	.byte	14,0,0,15
	.word	53232
	.byte	12
	.byte	'EVR',0,8
	.word	53241
	.byte	2,35,0,0,15
	.word	53212
	.byte	7
	.byte	'Ifx_SRC_GEVR',0,7,242,2,3
	.word	53260
	.byte	9
	.byte	'_Ifx_SRC_GFFT',0,7,245,2,25,12,13,12
	.word	50927
	.byte	14,0,0,15
	.word	53307
	.byte	12
	.byte	'FFT',0,12
	.word	53316
	.byte	2,35,0,0,15
	.word	53287
	.byte	7
	.byte	'Ifx_SRC_GFFT',0,7,248,2,3
	.word	53335
	.byte	9
	.byte	'_Ifx_SRC_GGPSR',0,7,251,2,25,128,12,13,128,12
	.word	51013
	.byte	14,0,0,15
	.word	53384
	.byte	12
	.byte	'GPSR',0,128,12
	.word	53394
	.byte	2,35,0,0,15
	.word	53362
	.byte	7
	.byte	'Ifx_SRC_GGPSR',0,7,254,2,3
	.word	53415
	.byte	9
	.byte	'_Ifx_SRC_GGPT12',0,7,129,3,25,48,13,48
	.word	51147
	.byte	14,0,0,15
	.word	53465
	.byte	12
	.byte	'GPT12',0,48
	.word	53474
	.byte	2,35,0,0,15
	.word	53443
	.byte	7
	.byte	'Ifx_SRC_GGPT12',0,7,132,3,3
	.word	53495
	.byte	9
	.byte	'_Ifx_SRC_GGTM',0,7,135,3,25,192,11,13,192,11
	.word	51301
	.byte	14,0,0,15
	.word	53545
	.byte	12
	.byte	'GTM',0,192,11
	.word	53555
	.byte	2,35,0,0,15
	.word	53524
	.byte	7
	.byte	'Ifx_SRC_GGTM',0,7,138,3,3
	.word	53575
	.byte	9
	.byte	'_Ifx_SRC_GHSM',0,7,141,3,25,8,13,8
	.word	51514
	.byte	14,0,0,15
	.word	53622
	.byte	12
	.byte	'HSM',0,8
	.word	53631
	.byte	2,35,0,0,15
	.word	53602
	.byte	7
	.byte	'Ifx_SRC_GHSM',0,7,144,3,3
	.word	53650
	.byte	9
	.byte	'_Ifx_SRC_GLMU',0,7,147,3,25,4,13,4
	.word	51573
	.byte	14,0,0,15
	.word	53697
	.byte	12
	.byte	'LMU',0,4
	.word	53706
	.byte	2,35,0,0,15
	.word	53677
	.byte	7
	.byte	'Ifx_SRC_GLMU',0,7,150,3,3
	.word	53725
	.byte	9
	.byte	'_Ifx_SRC_GPMU',0,7,153,3,25,8,13,8
	.word	51631
	.byte	14,1,0,15
	.word	53772
	.byte	12
	.byte	'PMU',0,8
	.word	53781
	.byte	2,35,0,0,15
	.word	53752
	.byte	7
	.byte	'Ifx_SRC_GPMU',0,7,156,3,3
	.word	53800
	.byte	9
	.byte	'_Ifx_SRC_GQSPI',0,7,159,3,25,96,13,96
	.word	51689
	.byte	14,3,0,15
	.word	53848
	.byte	12
	.byte	'QSPI',0,96
	.word	53857
	.byte	2,35,0,0,15
	.word	53827
	.byte	7
	.byte	'Ifx_SRC_GQSPI',0,7,162,3,3
	.word	53877
	.byte	9
	.byte	'_Ifx_SRC_GSCU',0,7,165,3,25,20,15
	.word	51809
	.byte	12
	.byte	'SCU',0,20
	.word	53925
	.byte	2,35,0,0,15
	.word	53905
	.byte	7
	.byte	'Ifx_SRC_GSCU',0,7,168,3,3
	.word	53944
	.byte	9
	.byte	'_Ifx_SRC_GSENT',0,7,171,3,25,16,13,16
	.word	51890
	.byte	14,0,0,15
	.word	53992
	.byte	12
	.byte	'SENT',0,16
	.word	54001
	.byte	2,35,0,0,15
	.word	53971
	.byte	7
	.byte	'Ifx_SRC_GSENT',0,7,174,3,3
	.word	54021
	.byte	9
	.byte	'_Ifx_SRC_GSMU',0,7,177,3,25,12,13,12
	.word	51950
	.byte	14,0,0,15
	.word	54069
	.byte	12
	.byte	'SMU',0,12
	.word	54078
	.byte	2,35,0,0,15
	.word	54049
	.byte	7
	.byte	'Ifx_SRC_GSMU',0,7,180,3,3
	.word	54097
	.byte	9
	.byte	'_Ifx_SRC_GSTM',0,7,183,3,25,96,13,96
	.word	52017
	.byte	14,0,0,15
	.word	54144
	.byte	12
	.byte	'STM',0,96
	.word	54153
	.byte	2,35,0,0,15
	.word	54124
	.byte	7
	.byte	'Ifx_SRC_GSTM',0,7,186,3,3
	.word	54172
	.byte	9
	.byte	'_Ifx_SRC_GVADC',0,7,189,3,25,224,4,13,64
	.word	52256
	.byte	14,3,0,15
	.word	54221
	.byte	12
	.byte	'G',0,64
	.word	54230
	.byte	2,35,0,13,224,1
	.word	350
	.byte	14,223,1,0,12
	.byte	'reserved_40',0,224,1
	.word	54246
	.byte	2,35,64,13,192,2
	.word	52118
	.byte	14,0,0,15
	.word	54279
	.byte	12
	.byte	'CG',0,192,2
	.word	54289
	.byte	3,35,160,2,0,15
	.word	54199
	.byte	7
	.byte	'Ifx_SRC_GVADC',0,7,194,3,3
	.word	54309
	.byte	9
	.byte	'_Ifx_SRC_GXBAR',0,7,197,3,25,4,15
	.word	52358
	.byte	12
	.byte	'XBAR',0,4
	.word	54358
	.byte	2,35,0,0,15
	.word	54337
	.byte	7
	.byte	'Ifx_SRC_GXBAR',0,7,200,3,3
	.word	54378
	.byte	7
	.byte	'Dma_StatusType',0,8,121,22
	.word	1018
	.byte	7
	.byte	'Dma_ErrorStatusType',0,8,141,1,22
	.word	1018
	.byte	2,8,147,1,9,1,3
	.byte	'DMA_CHANNEL0',0,0,3
	.byte	'DMA_CHANNEL1',0,1,3
	.byte	'DMA_CHANNEL2',0,2,3
	.byte	'DMA_CHANNEL3',0,3,3
	.byte	'DMA_CHANNEL4',0,4,3
	.byte	'DMA_CHANNEL5',0,5,3
	.byte	'DMA_CHANNEL6',0,6,3
	.byte	'DMA_CHANNEL7',0,7,3
	.byte	'DMA_CHANNEL8',0,8,3
	.byte	'DMA_CHANNEL9',0,9,3
	.byte	'DMA_CHANNEL10',0,10,3
	.byte	'DMA_CHANNEL11',0,11,3
	.byte	'DMA_CHANNEL12',0,12,3
	.byte	'DMA_CHANNEL13',0,13,3
	.byte	'DMA_CHANNEL14',0,14,3
	.byte	'DMA_CHANNEL15',0,15,3
	.byte	'DMA_CHANNEL16',0,16,3
	.byte	'DMA_CHANNEL17',0,17,3
	.byte	'DMA_CHANNEL18',0,18,3
	.byte	'DMA_CHANNEL19',0,19,3
	.byte	'DMA_CHANNEL20',0,20,3
	.byte	'DMA_CHANNEL21',0,21,3
	.byte	'DMA_CHANNEL22',0,22,3
	.byte	'DMA_CHANNEL23',0,23,3
	.byte	'DMA_CHANNEL24',0,24,3
	.byte	'DMA_CHANNEL25',0,25,3
	.byte	'DMA_CHANNEL26',0,26,3
	.byte	'DMA_CHANNEL27',0,27,3
	.byte	'DMA_CHANNEL28',0,28,3
	.byte	'DMA_CHANNEL29',0,29,3
	.byte	'DMA_CHANNEL30',0,30,3
	.byte	'DMA_CHANNEL31',0,31,3
	.byte	'DMA_CHANNEL32',0,32,3
	.byte	'DMA_CHANNEL33',0,33,3
	.byte	'DMA_CHANNEL34',0,34,3
	.byte	'DMA_CHANNEL35',0,35,3
	.byte	'DMA_CHANNEL36',0,36,3
	.byte	'DMA_CHANNEL37',0,37,3
	.byte	'DMA_CHANNEL38',0,38,3
	.byte	'DMA_CHANNEL39',0,39,3
	.byte	'DMA_CHANNEL40',0,40,3
	.byte	'DMA_CHANNEL41',0,41,3
	.byte	'DMA_CHANNEL42',0,42,3
	.byte	'DMA_CHANNEL43',0,43,3
	.byte	'DMA_CHANNEL44',0,44,3
	.byte	'DMA_CHANNEL45',0,45,3
	.byte	'DMA_CHANNEL46',0,46,3
	.byte	'DMA_CHANNEL47',0,47,3
	.byte	'DMA_CHANNEL48',0,48,3
	.byte	'DMA_CHANNEL49',0,49,3
	.byte	'DMA_CHANNEL50',0,50,3
	.byte	'DMA_CHANNEL51',0,51,3
	.byte	'DMA_CHANNEL52',0,52,3
	.byte	'DMA_CHANNEL53',0,53,3
	.byte	'DMA_CHANNEL54',0,54,3
	.byte	'DMA_CHANNEL55',0,55,3
	.byte	'DMA_CHANNEL56',0,56,3
	.byte	'DMA_CHANNEL57',0,57,3
	.byte	'DMA_CHANNEL58',0,58,3
	.byte	'DMA_CHANNEL59',0,59,3
	.byte	'DMA_CHANNEL60',0,60,3
	.byte	'DMA_CHANNEL61',0,61,3
	.byte	'DMA_CHANNEL62',0,62,3
	.byte	'DMA_CHANNEL63',0,63,3
	.byte	'DMA_CHANNEL64',0,192,0,3
	.byte	'DMA_CHANNEL65',0,193,0,3
	.byte	'DMA_CHANNEL66',0,194,0,3
	.byte	'DMA_CHANNEL67',0,195,0,3
	.byte	'DMA_CHANNEL68',0,196,0,3
	.byte	'DMA_CHANNEL69',0,197,0,3
	.byte	'DMA_CHANNEL70',0,198,0,3
	.byte	'DMA_CHANNEL71',0,199,0,3
	.byte	'DMA_CHANNEL72',0,200,0,3
	.byte	'DMA_CHANNEL73',0,201,0,3
	.byte	'DMA_CHANNEL74',0,202,0,3
	.byte	'DMA_CHANNEL75',0,203,0,3
	.byte	'DMA_CHANNEL76',0,204,0,3
	.byte	'DMA_CHANNEL77',0,205,0,3
	.byte	'DMA_CHANNEL78',0,206,0,3
	.byte	'DMA_CHANNEL79',0,207,0,3
	.byte	'DMA_CHANNEL80',0,208,0,3
	.byte	'DMA_CHANNEL81',0,209,0,3
	.byte	'DMA_CHANNEL82',0,210,0,3
	.byte	'DMA_CHANNEL83',0,211,0,3
	.byte	'DMA_CHANNEL84',0,212,0,3
	.byte	'DMA_CHANNEL85',0,213,0,3
	.byte	'DMA_CHANNEL86',0,214,0,3
	.byte	'DMA_CHANNEL87',0,215,0,3
	.byte	'DMA_CHANNEL88',0,216,0,3
	.byte	'DMA_CHANNEL89',0,217,0,3
	.byte	'DMA_CHANNEL90',0,218,0,3
	.byte	'DMA_CHANNEL91',0,219,0,3
	.byte	'DMA_CHANNEL92',0,220,0,3
	.byte	'DMA_CHANNEL93',0,221,0,3
	.byte	'DMA_CHANNEL94',0,222,0,3
	.byte	'DMA_CHANNEL95',0,223,0,3
	.byte	'DMA_CHANNEL96',0,224,0,3
	.byte	'DMA_CHANNEL97',0,225,0,3
	.byte	'DMA_CHANNEL98',0,226,0,3
	.byte	'DMA_CHANNEL99',0,227,0,3
	.byte	'DMA_CHANNEL100',0,228,0,3
	.byte	'DMA_CHANNEL101',0,229,0,3
	.byte	'DMA_CHANNEL102',0,230,0,3
	.byte	'DMA_CHANNEL103',0,231,0,3
	.byte	'DMA_CHANNEL104',0,232,0,3
	.byte	'DMA_CHANNEL105',0,233,0,3
	.byte	'DMA_CHANNEL106',0,234,0,3
	.byte	'DMA_CHANNEL107',0,235,0,3
	.byte	'DMA_CHANNEL108',0,236,0,3
	.byte	'DMA_CHANNEL109',0,237,0,3
	.byte	'DMA_CHANNEL110',0,238,0,3
	.byte	'DMA_CHANNEL111',0,239,0,3
	.byte	'DMA_CHANNEL112',0,240,0,3
	.byte	'DMA_CHANNEL113',0,241,0,3
	.byte	'DMA_CHANNEL114',0,242,0,3
	.byte	'DMA_CHANNEL115',0,243,0,3
	.byte	'DMA_CHANNEL116',0,244,0,3
	.byte	'DMA_CHANNEL117',0,245,0,3
	.byte	'DMA_CHANNEL118',0,246,0,3
	.byte	'DMA_CHANNEL119',0,247,0,3
	.byte	'DMA_CHANNEL120',0,248,0,3
	.byte	'DMA_CHANNEL121',0,249,0,3
	.byte	'DMA_CHANNEL122',0,250,0,3
	.byte	'DMA_CHANNEL123',0,251,0,3
	.byte	'DMA_CHANNEL124',0,252,0,3
	.byte	'DMA_CHANNEL125',0,253,0,3
	.byte	'DMA_CHANNEL126',0,254,0,3
	.byte	'DMA_CHANNEL127',0,255,0,3
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0,7
	.byte	'Dma_ChannelType',0,8,149,2,2
	.word	54458
	.byte	7
	.byte	'Spi_JobResultType',0,1,128,7,2
	.word	201
	.byte	7
	.byte	'Spi_NumberOfDataType',0,1,169,7,16
	.word	273
	.byte	7
	.byte	'Spi_ChannelType',0,1,177,7,15
	.word	350
	.byte	7
	.byte	'Spi_JobType',0,1,185,7,16
	.word	273
	.byte	7
	.byte	'Spi_SequenceType',0,1,193,7,15
	.word	350
	.byte	16,1,1,6
	.word	56772
	.byte	7
	.byte	'Spi_NotifFunctionPtrType',0,1,238,7,15
	.word	56775
	.byte	9
	.byte	'Spi_ChannelConfig',0,1,247,7,16,12,12
	.byte	'DefaultData',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'DataConfig',0,2
	.word	273
	.byte	2,35,4,12
	.byte	'NoOfBuffers',0,2
	.word	273
	.byte	2,35,6,12
	.byte	'ChannelBufferType',0,1
	.word	350
	.byte	2,35,8,0,7
	.byte	'Spi_ChannelConfigType',0,1,144,8,3
	.word	56814
	.byte	7
	.byte	'Spi_DelayConfigType',0,1,162,8,16
	.word	396
	.byte	7
	.byte	'Spi_HWUnitType',0,1,170,8,15
	.word	350
	.byte	9
	.byte	'Spi_JobConfig',0,1,183,8,16,24,12
	.byte	'JobEndNotification',0,4
	.word	56780
	.byte	2,35,0,17
	.word	350
	.byte	6
	.word	57060
	.byte	12
	.byte	'ChannelLinkPtr',0,4
	.word	57065
	.byte	2,35,4,12
	.byte	'BaudRateConfig',0,4
	.word	396
	.byte	2,35,8,12
	.byte	'TimeDelayConfig',0,4
	.word	396
	.byte	2,35,12,12
	.byte	'CSPin',0,2
	.word	273
	.byte	2,35,16,12
	.byte	'CSPolarity',0,1
	.word	350
	.byte	2,35,18,12
	.byte	'ShiftClkConfig',0,1
	.word	350
	.byte	2,35,19,12
	.byte	'JobPriority',0,1
	.word	350
	.byte	2,35,20,12
	.byte	'HwUnit',0,1
	.word	350
	.byte	2,35,21,12
	.byte	'ChannelBasedChipSelect',0,1
	.word	350
	.byte	2,35,22,12
	.byte	'ParitySelection',0,1
	.word	350
	.byte	2,35,23,0,7
	.byte	'Spi_JobConfigType',0,1,241,8,2
	.word	57012
	.byte	6
	.word	56772
	.byte	9
	.byte	'Spi_SequenceConfig',0,1,252,8,16,16,12
	.byte	'SeqEndNotification',0,4
	.word	56780
	.byte	2,35,0,17
	.word	273
	.byte	6
	.word	57382
	.byte	12
	.byte	'JobLinkPtr',0,4
	.word	57387
	.byte	2,35,4,17
	.word	350
	.byte	6
	.word	57412
	.byte	12
	.byte	'SeqSharingJobs',0,4
	.word	57417
	.byte	2,35,8,12
	.byte	'JobsInParamSeq',0,2
	.word	273
	.byte	2,35,12,12
	.byte	'InterruptibleSequence',0,1
	.word	350
	.byte	2,35,14,0,7
	.byte	'Spi_SequenceConfigType',0,1,157,9,2
	.word	57329
	.byte	9
	.byte	'Spi_DmaConfigType',0,1,164,9,16,2,12
	.byte	'TxDmaChannel',0,1
	.word	54458
	.byte	2,35,0,12
	.byte	'RxDmaChannel',0,1
	.word	54458
	.byte	2,35,1,0,7
	.byte	'Spi_DmaConfigType',0,1,168,9,2
	.word	57534
	.byte	9
	.byte	'Spi_HWModuleConfig',0,1,174,9,16,16,17
	.word	396
	.byte	12
	.byte	'HWClkSetting',0,4
	.word	57655
	.byte	2,35,0,17
	.word	396
	.byte	12
	.byte	'HWCSPolaritySetting',0,4
	.word	57682
	.byte	2,35,4,17
	.word	396
	.byte	12
	.byte	'HWPinSetting',0,4
	.word	57716
	.byte	2,35,8,17
	.word	57534
	.byte	6
	.word	57743
	.byte	12
	.byte	'SpiDmaConfigPtr',0,4
	.word	57748
	.byte	2,35,12,0,7
	.byte	'Spi_HWModuleConfigType',0,1,187,9,2
	.word	57630
	.byte	9
	.byte	'Spi_BaudrateEconType',0,1,214,9,16,6,12
	.byte	'EconVal',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'QSPIHwUnit',0,1
	.word	350
	.byte	2,35,4,0,7
	.byte	'Spi_BaudrateEconType',0,1,222,9,2
	.word	57811
	.byte	9
	.byte	'Spi_LastChannelDataType',0,1,161,10,16,8,6
	.word	396
	.byte	12
	.byte	'LastDataPtr',0,4
	.word	57936
	.byte	2,35,0,12
	.byte	'DataWidth',0,2
	.word	273
	.byte	2,35,4,0,7
	.byte	'Spi_LastChannelDataType',0,1,165,10,2
	.word	57906
	.byte	13,4
	.word	396
	.byte	14,0,0,18
	.byte	'Spi_QmJobResult',0,9,129,1,15
	.word	58015
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,4,1,58,15,59,15,57,15,11,15,0,0,3,40,0,3,8,28,13,0,0,4
	.byte	36,0,3,8,11,15,62,15,0,0,5,59,0,3,8,0,0,6,15,0,73,19,0,0,7,22,0,3,8,58,15,59,15,57,15,73,19,0,0,8,21,0
	.byte	54,15,0,0,9,19,1,3,8,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,11,23,1
	.byte	58,15,59,15,57,15,11,15,0,0,12,13,0,3,8,11,15,73,19,56,9,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0
	.byte	15,53,0,73,19,0,0,16,21,0,54,15,39,12,0,0,17,38,0,73,19,0,0,18,52,0,3,8,58,15,59,15,57,15,73,19,63,12
	.byte	60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L8:
	.word	.L19-.L18
.L18:
	.half	3
	.word	.L21-.L20
.L20:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'Spi.h',0,1,0,0
	.byte	'..\\mcal_src\\spi_infineon_tricore\\src\\Spi_Ver.c',0,0,0,0
	.byte	'Platform_Types.h',0,2,0,0
	.byte	'IfxDma_regdef.h',0,2,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0
	.byte	'IfxCpu_regdef.h',0,2,0,0
	.byte	'IfxSrc_regdef.h',0,2,0,0
	.byte	'Mcal_DmaLib.h',0,2,0,0
	.byte	'Spi_Local.h',0,1,0,0,0
.L21:
.L19:
	.sdecl	'.debug_info',debug,cluster('Spi_lGetJobStatus')
	.sect	'.debug_info'
.L9:
	.word	276
	.half	3
	.word	.L10
	.byte	4,1
	.byte	'..\\mcal_src\\spi_infineon_tricore\\src\\Spi_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L12,.L11
	.byte	2
	.word	.L5
	.byte	3
	.byte	'Spi_lGetJobStatus',0,1,179,1,19
	.word	.L14
	.byte	1,1,1
	.word	.L4,.L15,.L3
	.byte	4
	.byte	'Job',0,1,179,1,49
	.word	.L16,.L17
	.byte	5
	.word	.L4,.L15
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Spi_lGetJobStatus')
	.sect	'.debug_abbrev'
.L10:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Spi_lGetJobStatus')
	.sect	'.debug_line'
.L11:
	.word	.L23-.L22
.L22:
	.half	3
	.word	.L25-.L24
.L24:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\spi_infineon_tricore\\src\\Spi_Ver.c',0,0,0,0,0
.L25:
	.byte	5,13,7,0,5,2
	.word	.L4
	.byte	3,194,1,1,5,14,3,20,1,5,29,9
	.half	.L26-.L4
	.byte	1,5,56,9
	.half	.L27-.L26
	.byte	3,1,1,5,29,9
	.half	.L28-.L27
	.byte	3,127,1,5,44,9
	.half	.L29-.L28
	.byte	3,1,1,5,37,9
	.half	.L30-.L29
	.byte	3,127,1,9
	.half	.L31-.L30
	.byte	3,5,1,5,1,3,3,1,7,9
	.half	.L13-.L31
	.byte	0,1,1
.L23:
	.sdecl	'.debug_ranges',debug,cluster('Spi_lGetJobStatus')
	.sect	'.debug_ranges'
.L12:
	.word	-1,.L4,0,.L13-.L4,0,0
	.sdecl	'.debug_loc',debug,cluster('Spi_lGetJobStatus')
	.sect	'.debug_loc'
.L17:
	.word	-1,.L4,0,.L15-.L4
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L3:
	.word	-1,.L4,0,.L15-.L4
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L32:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Spi_lGetJobStatus')
	.sect	'.debug_frame'
	.word	24
	.word	.L32,.L4,.L15-.L4
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   224  
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   225  #define SPI_STOP_SEC_CODE
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   226  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   227  allowed only for MemMap.h*/
; ..\mcal_src\spi_infineon_tricore\src\Spi_Ver.c	   228  #include "MemMap.h"

	; Module end
