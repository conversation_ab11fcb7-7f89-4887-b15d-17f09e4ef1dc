	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc17748a -c99 --dep-file=eeprom\\.Fee_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\Fee_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\Fee_PBCfg.src ..\\eeprom\\Fee_PBCfg.c"
	.compiler_name		"ctc"
	.name	"Fee_PBCfg"

	
$TC16X
	
	.sdecl	'.bss.Fee_PBCfg.Fee_StateVar',data,cluster('Fee_StateVar')
	.sect	'.bss.Fee_PBCfg.Fee_StateVar'
	.align	4
Fee_StateVar:	.type	object
	.size	Fee_StateVar,2752
	.space	2752
	.sdecl	'.rodata.Fee_PBCfg.Fee_BlockConfig',data,rom,cluster('Fee_BlockConfig')
	.sect	'.rodata.Fee_PBCfg.Fee_BlockConfig'
	.align	4
Fee_BlockConfig:	.type	object
	.size	Fee_BlockConfig,816
	.space	4
	.byte	16
	.space	1
	.byte	4
	.space	5
	.byte	17
	.space	1
	.byte	4
	.space	5
	.byte	32
	.space	1
	.byte	12
	.space	5
	.byte	48
	.space	1
	.byte	50
	.space	5
	.byte	64
	.space	1
	.byte	50
	.space	5
	.byte	80
	.space	1
	.byte	50
	.space	5
	.byte	96
	.space	1
	.byte	50
	.space	5
	.byte	112
	.space	1
	.byte	50
	.space	5
	.byte	128
	.space	1
	.byte	50
	.space	5
	.byte	144
	.space	1
	.byte	50
	.space	5
	.byte	160
	.space	1
	.byte	50
	.space	5
	.byte	176
	.space	1
	.byte	24
	.space	5
	.byte	192
	.space	1
	.byte	17
	.space	5
	.byte	208
	.space	1
	.byte	17
	.space	5
	.byte	224
	.space	1
	.byte	16
	.space	6
	.byte	6,4
	.space	5
	.byte	240
	.space	1
	.byte	4
	.space	6
	.byte	1,4
	.space	5
	.byte	16,1,4
	.space	5
	.byte	32,1,4
	.space	5
	.byte	48,1,4
	.space	5
	.byte	64,1,4
	.space	5
	.byte	80,1,4
	.space	5
	.byte	96,1,4
	.space	5
	.byte	112,1,4
	.space	5
	.byte	128,1,4
	.space	5
	.byte	144,1,4
	.space	5
	.byte	160,1,4
	.space	5
	.byte	176,1,4
	.space	5
	.byte	192,1,3
	.space	5
	.byte	208,1,4
	.space	5
	.byte	224,1,4
	.space	5
	.byte	240,1,4
	.space	6
	.byte	2,4
	.space	5
	.byte	16,2,3
	.space	5
	.byte	32,2,3
	.space	5
	.byte	48,2,4
	.space	5
	.byte	80,2,4
	.space	5
	.byte	96,2,4
	.space	5
	.byte	112,2,4
	.space	5
	.byte	128,2,3
	.space	5
	.byte	144,2,4
	.space	5
	.byte	160,2,4
	.space	5
	.byte	176,2,4
	.space	5
	.byte	192,2,6
	.space	5
	.byte	208,2,6
	.space	5
	.byte	224,2,6
	.space	5
	.byte	240,2,6
	.space	6
	.byte	3,6
	.space	5
	.byte	16,3,6
	.space	5
	.byte	32,3,6
	.space	5
	.byte	48,3,6
	.space	5
	.byte	64,3,18
	.space	5
	.byte	80,3,18
	.space	5
	.byte	96,3,18
	.space	5
	.byte	112,3,18
	.space	5
	.byte	128,3,18
	.space	5
	.byte	144,3,18
	.space	5
	.byte	160,3,18
	.space	5
	.byte	176,3,18
	.space	5
	.byte	192,3,18
	.space	5
	.byte	208,3,18
	.space	5
	.byte	224,3,18
	.space	5
	.byte	240,3,18
	.space	6
	.byte	4,18
	.space	5
	.byte	16,4,18
	.space	5
	.byte	32,4,18
	.space	5
	.byte	48,4,18
	.space	5
	.byte	64,4,18
	.space	5
	.byte	80,4,18
	.space	5
	.byte	96,4,7
	.space	5
	.byte	112,4,7
	.space	5
	.byte	128,4,5
	.space	5
	.byte	144,4,18
	.space	5
	.byte	160,4,19
	.space	5
	.byte	176,4,7
	.space	5
	.byte	192,4,12
	.space	5
	.byte	208,4,13
	.space	5
	.byte	224,4,5
	.space	5
	.byte	240,4,22
	.space	6
	.byte	5,17
	.space	5
	.byte	16,5,17
	.space	5
	.byte	32,5,16
	.space	5
	.byte	48,5,3
	.space	5
	.byte	64,5,3
	.space	5
	.byte	80,5,3
	.space	5
	.byte	96,5,66
	.space	5
	.byte	112,5,66
	.space	5
	.byte	128,5,10
	.space	5
	.byte	144,5,10
	.space	5
	.byte	160,5,4
	.space	5
	.byte	176,5,4
	.space	5
	.byte	192,5,4
	.space	5
	.byte	208,5,4
	.space	5
	.byte	224,5,4
	.space	5
	.byte	240,5,4
	.space	5
	.byte	208
	.space	1
	.byte	7
	.space	5
	.byte	192
	.space	1
	.byte	7
	.space	5
	.byte	224
	.space	1
	.byte	14
	.space	5
	.byte	16,6,112
	.space	5
	.byte	64,2,3
	.space	5
	.byte	32,6,42
	.space	1
	.sdecl	'.rodata.Fee_PBCfg.Fee_ConfigRoot',data,rom,cluster('Fee_ConfigRoot')
	.sect	'.rodata.Fee_PBCfg.Fee_ConfigRoot'
	.global	Fee_ConfigRoot
	.align	4
Fee_ConfigRoot:	.type	object
	.size	Fee_ConfigRoot,32
	.word	Fee_StateVar,Fee_BlockConfig,NvM_JobEndNotification,NvM_JobErrorNotification
	.word	2288
	.half	102
	.space	2
	.word	FeeIllegalStateNotification
	.byte	1
	.space	3
	.sdecl	'.data.Fee_PBCfg.Fee_CfgPtr',data,cluster('Fee_CfgPtr')
	.sect	'.data.Fee_PBCfg.Fee_CfgPtr'
	.global	Fee_CfgPtr
	.align	4
Fee_CfgPtr:	.type	object
	.size	Fee_CfgPtr,4
	.word	Fee_ConfigRoot
	.calls	'__INDIRECT__','NvM_JobEndNotification'
	.calls	'__INDIRECT__','NvM_JobErrorNotification'
	.extern	NvM_JobEndNotification
	.extern	NvM_JobErrorNotification
	.extern	FeeIllegalStateNotification
	.extern	__INDIRECT__
	.calls	'__INDIRECT__','FeeIllegalStateNotification'
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	5150
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\eeprom\\Fee_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'NvM_JobEndNotification',0,1,57,36,1,1,1,1,2
	.byte	'NvM_JobErrorNotification',0,1,69,36,1,1,1,1,2
	.byte	'FeeIllegalStateNotification',0,2,60,13,1,1,1,1,3
	.byte	'__INDIRECT__',0,2,1,1,1,1,1,4
	.byte	'void',0,5
	.word	296
	.byte	6
	.byte	'__prof_adm',0,2,1,1
	.word	302
	.byte	7,1,5
	.word	326
	.byte	6
	.byte	'__codeptr',0,2,1,1
	.word	328
	.byte	8
	.byte	'unsigned char',0,1,8,6
	.byte	'uint8',0,3,90,29
	.word	351
	.byte	8
	.byte	'unsigned short int',0,2,7,6
	.byte	'uint16',0,3,92,29
	.word	382
	.byte	8
	.byte	'unsigned long int',0,4,7,6
	.byte	'uint32',0,3,94,29
	.word	419
	.byte	6
	.byte	'boolean',0,3,105,29
	.word	351
	.byte	8
	.byte	'unsigned int',0,4,7,6
	.byte	'unsigned_int',0,4,121,22
	.word	471
	.byte	9,5,72,9,1,10
	.byte	'MEMIF_JOB_OK',0,0,10
	.byte	'MEMIF_JOB_FAILED',0,1,10
	.byte	'MEMIF_JOB_PENDING',0,2,10
	.byte	'MEMIF_JOB_CANCELED',0,3,10
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,10
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,6
	.byte	'MemIf_JobResultType',0,5,80,3
	.word	508
	.byte	9,5,88,9,1,10
	.byte	'MEMIF_MODE_SLOW',0,0,10
	.byte	'MEMIF_MODE_FAST',0,1,0,6
	.byte	'MemIf_ModeType',0,5,92,3
	.word	666
	.byte	6
	.byte	'Fls_LengthType',0,6,177,3,16
	.word	419
	.byte	11
	.byte	'Fls_JobStartType',0,6,179,3,16,1,12
	.byte	'Reserved1',0,1
	.word	351
	.byte	1,7,2,35,0,12
	.byte	'Write',0,1
	.word	351
	.byte	1,6,2,35,0,12
	.byte	'Erase',0,1
	.word	351
	.byte	1,5,2,35,0,12
	.byte	'Read',0,1
	.word	351
	.byte	1,4,2,35,0,12
	.byte	'Compare',0,1
	.word	351
	.byte	1,3,2,35,0,12
	.byte	'Reserved2',0,1
	.word	351
	.byte	3,0,2,35,0,0,6
	.byte	'Fls_JobStartType',0,6,187,3,3
	.word	755
	.byte	6
	.byte	'Fls_17_Pmu_Job_Type',0,6,191,3,15
	.word	351
	.byte	11
	.byte	'Fls_17_Pmu_StateType',0,6,202,3,16,36,13
	.byte	'FlsReadAddress',0,4
	.word	419
	.byte	2,35,0,13
	.byte	'FlsWriteAddress',0,4
	.word	419
	.byte	2,35,4,13
	.byte	'FlsReadLength',0,4
	.word	419
	.byte	2,35,8,13
	.byte	'FlsWriteLength',0,4
	.word	419
	.byte	2,35,12,5
	.word	351
	.byte	13
	.byte	'FlsReadBufferPtr',0,4
	.word	1068
	.byte	2,35,16,14
	.word	351
	.byte	5
	.word	1099
	.byte	13
	.byte	'FlsWriteBufferPtr',0,4
	.word	1104
	.byte	2,35,20,13
	.byte	'FlsJobResult',0,1
	.word	508
	.byte	2,35,24,13
	.byte	'FlsMode',0,1
	.word	666
	.byte	2,35,25,13
	.byte	'NotifCaller',0,1
	.word	351
	.byte	2,35,26,13
	.byte	'JobStarted',0,1
	.word	755
	.byte	2,35,27,15,2
	.word	351
	.byte	16,1,0,13
	.byte	'FlsJobType',0,2
	.word	1216
	.byte	2,35,28,13
	.byte	'FlsPver',0,1
	.word	351
	.byte	2,35,30,13
	.byte	'FlsOper',0,1
	.word	351
	.byte	2,35,31,13
	.byte	'FlsTimeoutErr',0,1
	.word	351
	.byte	2,35,32,0,6
	.byte	'Fls_17_Pmu_StateType',0,6,134,4,3
	.word	945
	.byte	17,1,1,5
	.word	1333
	.byte	6
	.byte	'Fls_NotifFunctionPtrType',0,6,141,4,16
	.word	1336
	.byte	18,1,1,19
	.word	419
	.byte	19
	.word	419
	.byte	14
	.word	419
	.byte	5
	.word	1388
	.byte	19
	.word	1393
	.byte	19
	.word	351
	.byte	0,5
	.word	1375
	.byte	6
	.byte	'Fls_WriteCmdPtrType',0,6,143,4,16
	.word	1409
	.byte	18,1,1,19
	.word	419
	.byte	0,5
	.word	1443
	.byte	6
	.byte	'Fls_EraseCmdPtrType',0,6,148,4,16
	.word	1452
	.byte	6
	.byte	'Fee_PageType',0,7,136,1,17
	.word	382
	.byte	6
	.byte	'Fee_NotifFunctionPtrType',0,7,146,1,16
	.word	1336
	.byte	11
	.byte	'Fee_Block',0,7,148,1,16,8,12
	.byte	'CycleCountLimit',0,4
	.word	471
	.byte	24,8,2,35,2,12
	.byte	'FeeImmediateData',0,1
	.word	351
	.byte	8,0,2,35,3,12
	.byte	'BlockNumber',0,2
	.word	382
	.byte	16,0,2,35,4,12
	.byte	'Size',0,2
	.word	382
	.byte	16,0,2,35,6,0,6
	.byte	'Fee_BlockType',0,7,162,1,3
	.word	1542
	.byte	11
	.byte	'Fee_CacheStatus',0,7,170,1,16,2,12
	.byte	'Valid',0,1
	.word	351
	.byte	1,7,2,35,0,12
	.byte	'Consistent',0,1
	.word	351
	.byte	1,6,2,35,0,12
	.byte	'Copied',0,1
	.word	351
	.byte	1,5,2,35,0,12
	.byte	'PrevCopyValid',0,1
	.word	351
	.byte	1,4,2,35,0,12
	.byte	'PrevCopyConsistent',0,1
	.word	351
	.byte	1,3,2,35,0,12
	.byte	'PrevCopyCopied',0,1
	.word	351
	.byte	1,2,2,35,0,12
	.byte	'Reserved',0,2
	.word	382
	.byte	10,0,2,35,0,0,6
	.byte	'Fee_CacheStatusType',0,7,187,1,3
	.word	1676
	.byte	11
	.byte	'Fee_Cache',0,7,189,1,16,8,13
	.byte	'Address',0,4
	.word	419
	.byte	2,35,0,13
	.byte	'BlockNumber',0,2
	.word	382
	.byte	2,35,4,13
	.byte	'Status',0,2
	.word	1676
	.byte	2,35,6,0,6
	.byte	'Fee_CacheType',0,7,204,1,3
	.word	1886
	.byte	11
	.byte	'FeePendReqInfo_Buf',0,7,210,1,16,12,13
	.byte	'DataBufferPtr',0,4
	.word	1068
	.byte	2,35,0,13
	.byte	'BlockNumber',0,2
	.word	382
	.byte	2,35,4,13
	.byte	'BlockOffset',0,2
	.word	382
	.byte	2,35,6,13
	.byte	'Length',0,2
	.word	382
	.byte	2,35,8,0,6
	.byte	'Fee_PendReqBufType',0,7,219,1,3
	.word	1980
	.byte	11
	.byte	'FeeStatusFlags_t',0,7,226,1,17,1,12
	.byte	'FeeBlkModified',0,1
	.word	351
	.byte	1,7,2,35,0,12
	.byte	'FeeStartInitGC',0,1
	.word	351
	.byte	1,6,2,35,0,12
	.byte	'FeeCurrSector',0,1
	.word	351
	.byte	1,5,2,35,0,12
	.byte	'FeeInitAPICalled',0,1
	.word	351
	.byte	1,4,2,35,0,12
	.byte	'FeeBlkInvalidStatus',0,1
	.word	351
	.byte	1,3,2,35,0,12
	.byte	'FeeWriteInvldAPICalled',0,1
	.word	351
	.byte	1,2,2,35,0,12
	.byte	'unused',0,1
	.word	351
	.byte	2,0,2,35,0,0,6
	.byte	'Fee_StatusFlagsType',0,7,254,1,3
	.word	2115
	.byte	11
	.byte	'Fee_SectorStatus_t',0,7,133,2,17,1,12
	.byte	'Dirty',0,1
	.word	351
	.byte	1,7,2,35,0,12
	.byte	'Used',0,1
	.word	351
	.byte	1,6,2,35,0,12
	.byte	'unused',0,1
	.word	351
	.byte	6,0,2,35,0,0,6
	.byte	'Fee_SectorStatusType',0,7,142,2,3
	.word	2356
	.byte	11
	.byte	'Fee_SectorInfo_t',0,7,147,2,16,32,13
	.byte	'StateCount',0,4
	.word	419
	.byte	2,35,0,13
	.byte	'UnerasableWLAddr',0,4
	.word	419
	.byte	2,35,4,15,8
	.word	419
	.byte	16,1,0,13
	.byte	'NonZeroWLAddr',0,8
	.word	2532
	.byte	2,35,8,13
	.byte	'NonZeroWLCount',0,4
	.word	419
	.byte	2,35,16,13
	.byte	'StatePageAddr',0,4
	.word	419
	.byte	2,35,20,13
	.byte	'NextFreeWLAddr',0,4
	.word	419
	.byte	2,35,24,13
	.byte	'UnerasableWLCount',0,1
	.word	351
	.byte	2,35,28,13
	.byte	'State',0,1
	.word	351
	.byte	2,35,29,13
	.byte	'Status',0,1
	.word	2356
	.byte	2,35,30,0,6
	.byte	'Fee_SectorInfoType',0,7,177,2,3
	.word	2463
	.byte	11
	.byte	'Fee_LastWrittenBlkInfo_t',0,7,180,2,16,12,13
	.byte	'Addr',0,4
	.word	419
	.byte	2,35,0,13
	.byte	'PageCount',0,2
	.word	382
	.byte	2,35,4,13
	.byte	'BlockNumber',0,2
	.word	382
	.byte	2,35,6,13
	.byte	'Status',0,2
	.word	1676
	.byte	2,35,8,0,6
	.byte	'Fee_LastWrittenBlkInfoType',0,7,190,2,3
	.word	2722
	.byte	11
	.byte	'Fee_GcBlkInfo_t',0,7,192,2,16,12,13
	.byte	'Addr',0,4
	.word	419
	.byte	2,35,0,13
	.byte	'PageCount',0,2
	.word	382
	.byte	2,35,4,13
	.byte	'BlockNumber',0,2
	.word	382
	.byte	2,35,6,13
	.byte	'Consistent',0,1
	.word	351
	.byte	2,35,8,0,6
	.byte	'Fee_GcBlkInfoType',0,7,202,2,3
	.word	2860
.L16:
	.byte	11
	.byte	'Fee_State_Data_t',0,7,206,2,16,192,21,15,64
	.word	2463
	.byte	16,1,0,13
	.byte	'FeeSectorInfo',0,64
	.word	3008
	.byte	2,35,0,15,128,7
	.word	1886
	.byte	16,111,0,13
	.byte	'FeeBlockInfo',0,128,7
	.word	3040
	.byte	2,35,64,13
	.byte	'FeeLastWrittenBlkInfo',0,12
	.word	2722
	.byte	3,35,192,7,13
	.byte	'FeeGcCurrBlkInfo',0,12
	.word	2860
	.byte	3,35,204,7,13
	.byte	'FeePendReqInfo',0,12
	.word	1980
	.byte	3,35,216,7,15,128,1
	.word	419
	.byte	16,31,0,13
	.byte	'FeeGcLWBGcSrcAddr',0,128,1
	.word	3157
	.byte	3,35,228,7,13
	.byte	'FeeTempArray',0,8
	.word	2532
	.byte	3,35,228,8,13
	.byte	'FeeStateCount',0,4
	.word	419
	.byte	3,35,236,8,15,128,4
	.word	351
	.byte	16,255,3,0,13
	.byte	'FeeReadWriteBuffer',0,128,4
	.word	3243
	.byte	3,35,240,8,13
	.byte	'FeeGcReadWriteBuffer',0,128,4
	.word	3243
	.byte	3,35,240,12,15,248,3
	.word	351
	.byte	16,247,3,0,13
	.byte	'FeeLastWrittenBlkBuffer',0,248,3
	.word	3316
	.byte	3,35,240,16,13
	.byte	'FeeGcDestAddr',0,4
	.word	419
	.byte	3,35,232,20,13
	.byte	'FeeGcSrcAddr',0,4
	.word	419
	.byte	3,35,236,20,13
	.byte	'FeeNextFreePageAddr',0,4
	.word	419
	.byte	3,35,240,20,13
	.byte	'FeeWriteAffectedAddr',0,4
	.word	419
	.byte	3,35,244,20,13
	.byte	'FeeBlockStartAddr',0,4
	.word	419
	.byte	3,35,248,20,13
	.byte	'FeeCurrSectSrcAddr',0,4
	.word	419
	.byte	3,35,252,20,13
	.byte	'FeeUnErasableWLAddrTemp',0,4
	.word	419
	.byte	3,35,128,21,13
	.byte	'FeeUserReadDestPtr',0,4
	.word	1068
	.byte	3,35,132,21,13
	.byte	'FeeJobResult',0,1
	.word	508
	.byte	3,35,136,21,13
	.byte	'FeeLastWriteSize',0,4
	.word	419
	.byte	3,35,138,21,13
	.byte	'FeeLastReadSize',0,4
	.word	419
	.byte	3,35,142,21,13
	.byte	'FeeComparedLen',0,2
	.word	382
	.byte	3,35,146,21,13
	.byte	'FeeReadLen',0,2
	.word	382
	.byte	3,35,148,21,13
	.byte	'FeeBlkPageCount',0,2
	.word	382
	.byte	3,35,150,21,13
	.byte	'FeeUserWriteBytesCount',0,2
	.word	382
	.byte	3,35,152,21,13
	.byte	'FeeCurrReqBlockNum',0,2
	.word	382
	.byte	3,35,154,21,13
	.byte	'FeeIntrCurrReqPageCount',0,2
	.word	382
	.byte	3,35,156,21,13
	.byte	'FeeGCCopyIndex',0,2
	.word	382
	.byte	3,35,158,21,13
	.byte	'FeeGCUnconfigBlkCopyIndex',0,2
	.word	382
	.byte	3,35,160,21,13
	.byte	'FeeUnConfigBlockCount',0,2
	.word	382
	.byte	3,35,162,21,13
	.byte	'FeeGcPrevBlockNumber',0,2
	.word	382
	.byte	3,35,164,21,13
	.byte	'FeeGcFirstBlkNumInWL',0,2
	.word	382
	.byte	3,35,166,21,13
	.byte	'FeeStatusFlags',0,1
	.word	2115
	.byte	3,35,168,21,13
	.byte	'FeeLastWrittenBlockDirty',0,1
	.word	351
	.byte	3,35,169,21,13
	.byte	'FeePendReqStatus',0,1
	.word	351
	.byte	3,35,170,21,13
	.byte	'FeeGcState',0,1
	.word	351
	.byte	3,35,171,21,13
	.byte	'FeeGcResumeState',0,1
	.word	351
	.byte	3,35,172,21,13
	.byte	'FeeGcBlkIndexInWL',0,1
	.word	351
	.byte	3,35,173,21,13
	.byte	'FeeInitGCState',0,1
	.word	351
	.byte	3,35,174,21,13
	.byte	'FeePrepDFLASHState',0,1
	.word	351
	.byte	3,35,175,21,13
	.byte	'FeeCacheState',0,1
	.word	351
	.byte	3,35,176,21,13
	.byte	'FeeRepairStep',0,1
	.word	351
	.byte	3,35,177,21,13
	.byte	'FeeWLAffectedType',0,1
	.word	351
	.byte	3,35,178,21,13
	.byte	'FeeIntrJob',0,1
	.word	351
	.byte	3,35,179,21,13
	.byte	'FeeIntrJobStatus',0,1
	.word	351
	.byte	3,35,180,21,13
	.byte	'FeeUserJobStatus',0,1
	.word	351
	.byte	3,35,181,21,13
	.byte	'FeeIntrJobResult',0,1
	.word	351
	.byte	3,35,182,21,13
	.byte	'FeeUserJobResult',0,1
	.word	351
	.byte	3,35,183,21,13
	.byte	'FeeMainJob',0,1
	.word	351
	.byte	3,35,184,21,13
	.byte	'FeeUserJobFailCount',0,1
	.word	351
	.byte	3,35,185,21,13
	.byte	'FeeIntrJobFailCount',0,1
	.word	351
	.byte	3,35,186,21,13
	.byte	'FeeUncfgBlksExceeded',0,1
	.word	351
	.byte	3,35,187,21,13
	.byte	'FeeUnErasableWLCountTemp',0,1
	.word	351
	.byte	3,35,188,21,13
	.byte	'FeeSectorCount',0,1
	.word	351
	.byte	3,35,189,21,13
	.byte	'FeeDisableGCStart',0,1
	.word	351
	.byte	3,35,190,21,0,6
	.byte	'Fee_StateDataType',0,7,155,4,3
	.word	2984
	.byte	11
	.byte	'Fee_Other_Config_t',0,7,157,4,16,1,12
	.byte	'FeeUnconfigBlock',0,1
	.word	351
	.byte	1,7,2,35,0,12
	.byte	'FeeGcRestartPoint',0,1
	.word	351
	.byte	1,6,2,35,0,12
	.byte	'FeeUseEraseSuspend',0,1
	.word	351
	.byte	1,5,2,35,0,12
	.byte	'unused',0,1
	.word	351
	.byte	5,0,2,35,0,0,6
	.byte	'Fee_GCConfigType',0,7,174,4,3
	.word	4639
	.byte	11
	.byte	'Fee_Config',0,7,180,4,16,32,5
	.word	2984
	.byte	13
	.byte	'FeeStatePtr',0,4
	.word	4813
	.byte	2,35,0,14
	.word	1542
	.byte	5
	.word	4839
	.byte	13
	.byte	'FeeBlockConfigPtr',0,4
	.word	4844
	.byte	2,35,4,13
	.byte	'FeeNvmJobEndNotification',0,4
	.word	1508
	.byte	2,35,8,13
	.byte	'FeeNvmJobErrorNotification',0,4
	.word	1508
	.byte	2,35,12,13
	.byte	'FeeThresholdLimit',0,4
	.word	419
	.byte	2,35,16,13
	.byte	'FeeBlkCnt',0,2
	.word	382
	.byte	2,35,20,13
	.byte	'FeeGCConfigSetting',0,1
	.word	4639
	.byte	2,35,22,13
	.byte	'FeeIllegalStateNotification',0,4
	.word	1508
	.byte	2,35,24,13
	.byte	'FeeEraseAllEnable',0,1
	.word	351
	.byte	2,35,28,0,6
	.byte	'Fee_ConfigType',0,7,209,4,3
	.word	4796
	.byte	5
	.word	1333
	.byte	15,32
	.word	4796
	.byte	16,0,0
.L14:
	.byte	14
	.word	5114
	.byte	14
	.word	4796
.L15:
	.byte	5
	.word	5128
	.byte	15,176,6
	.word	1542
	.byte	16,101,0
.L17:
	.byte	14
	.word	5138
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,4,59,0,3,8,0,0,5,15,0,73,19,0,0,6,22,0,3,8,58,15,59
	.byte	15,57,15,73,19,0,0,7,21,0,54,15,0,0,8,36,0,3,8,11,15,62,15,0,0,9,4,1,58,15,59,15,57,15,11,15,0,0,10,40
	.byte	0,3,8,28,13,0,0,11,19,1,3,8,58,15,59,15,57,15,11,15,0,0,12,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,13
	.byte	13,0,3,8,11,15,73,19,56,9,0,0,14,38,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,21,0,54,15
	.byte	39,12,0,0,18,21,1,54,15,39,12,0,0,19,5,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L19-.L18
.L18:
	.half	3
	.word	.L21-.L20
.L20:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee',0
	.byte	0
	.byte	'NvM_Cbk.h',0,1,0,0
	.byte	'..\\eeprom\\Fee_PBCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,2,0,0
	.byte	'Mcal_TcLib.h',0,2,0,0
	.byte	'MemIf_Types.h',0,3,0,0
	.byte	'Fls_17_Pmu.h',0,4,0,0
	.byte	'Fee.h',0,5,0,0,0
.L21:
.L19:
	.sdecl	'.debug_info',debug,cluster('Fee_ConfigRoot')
	.sect	'.debug_info'
.L6:
	.word	205
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\eeprom\\Fee_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Fee_ConfigRoot',0,2,192,5,22
	.word	.L14
	.byte	1,5,3
	.word	Fee_ConfigRoot
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Fee_ConfigRoot')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Fee_CfgPtr')
	.sect	'.debug_info'
.L8:
	.word	201
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\eeprom\\Fee_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Fee_CfgPtr',0,2,235,5,24
	.word	.L15
	.byte	1,5,3
	.word	Fee_CfgPtr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Fee_CfgPtr')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Fee_StateVar')
	.sect	'.debug_info'
.L10:
	.word	201
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\eeprom\\Fee_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Fee_StateVar',0,2,73,26
	.word	.L16
	.byte	5,3
	.word	Fee_StateVar
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Fee_StateVar')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Fee_BlockConfig')
	.sect	'.debug_info'
.L12:
	.word	204
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\eeprom\\Fee_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Fee_BlockConfig',0,2,87,28
	.word	.L17
	.byte	5,3
	.word	Fee_BlockConfig
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Fee_BlockConfig')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0

; ..\eeprom\Fee_PBCfg.c	     1  /******************************************************************************
; ..\eeprom\Fee_PBCfg.c	     2  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\eeprom\Fee_PBCfg.c	     4  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	     5  ** All rights reserved.                                                      **
; ..\eeprom\Fee_PBCfg.c	     6  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\eeprom\Fee_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\eeprom\Fee_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\eeprom\Fee_PBCfg.c	    10  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    11  *******************************************************************************
; ..\eeprom\Fee_PBCfg.c	    12  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    13  **  $FILENAME   : Fee_PBCfg.c $                                              **
; ..\eeprom\Fee_PBCfg.c	    14  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    15  **  $CC VERSION : \main\18 $                                                 **
; ..\eeprom\Fee_PBCfg.c	    16  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    17  **  DATE, TIME: 2021-10-28, 12:21:17                                         **
; ..\eeprom\Fee_PBCfg.c	    18  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    19  **  GENERATOR : Build b141014-0350                                           **
; ..\eeprom\Fee_PBCfg.c	    20  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    21  **  AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\eeprom\Fee_PBCfg.c	    22  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    23  **  VENDOR    : Infineon Technologies                                        **
; ..\eeprom\Fee_PBCfg.c	    24  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    25  **  DESCRIPTION  : FEE configuration generated out of ECU configuration      **
; ..\eeprom\Fee_PBCfg.c	    26  **                   file (Fee.bmd)                                          **
; ..\eeprom\Fee_PBCfg.c	    27  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    28  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\eeprom\Fee_PBCfg.c	    29  **                                                                           **
; ..\eeprom\Fee_PBCfg.c	    30  ******************************************************************************/
; ..\eeprom\Fee_PBCfg.c	    31  
; ..\eeprom\Fee_PBCfg.c	    32  /*******************************************************************************
; ..\eeprom\Fee_PBCfg.c	    33  **                      Includes                                              **
; ..\eeprom\Fee_PBCfg.c	    34  *******************************************************************************/
; ..\eeprom\Fee_PBCfg.c	    35  
; ..\eeprom\Fee_PBCfg.c	    36  /* Include Fee Module Header File */
; ..\eeprom\Fee_PBCfg.c	    37  #include "Fee.h"
; ..\eeprom\Fee_PBCfg.c	    38  
; ..\eeprom\Fee_PBCfg.c	    39  /*******************************************************************************
; ..\eeprom\Fee_PBCfg.c	    40  **                      Imported Compiler Switch Checks                       **
; ..\eeprom\Fee_PBCfg.c	    41  *******************************************************************************/
; ..\eeprom\Fee_PBCfg.c	    42  
; ..\eeprom\Fee_PBCfg.c	    43  /*******************************************************************************
; ..\eeprom\Fee_PBCfg.c	    44  **                      Private Macro Definitions                             **
; ..\eeprom\Fee_PBCfg.c	    45  *******************************************************************************/
; ..\eeprom\Fee_PBCfg.c	    46  
; ..\eeprom\Fee_PBCfg.c	    47  /*******************************************************************************
; ..\eeprom\Fee_PBCfg.c	    48  **                      Private Type Definitions                              **
; ..\eeprom\Fee_PBCfg.c	    49  *******************************************************************************/
; ..\eeprom\Fee_PBCfg.c	    50  
; ..\eeprom\Fee_PBCfg.c	    51  /*******************************************************************************
; ..\eeprom\Fee_PBCfg.c	    52  **                      Private Function Declarations                         **
; ..\eeprom\Fee_PBCfg.c	    53  *******************************************************************************/
; ..\eeprom\Fee_PBCfg.c	    54  
; ..\eeprom\Fee_PBCfg.c	    55  /*******************************************************************************
; ..\eeprom\Fee_PBCfg.c	    56  **                      Global Function Declarations                          **
; ..\eeprom\Fee_PBCfg.c	    57  *******************************************************************************/
; ..\eeprom\Fee_PBCfg.c	    58  
; ..\eeprom\Fee_PBCfg.c	    59  /* Illegal State Notification Function */
; ..\eeprom\Fee_PBCfg.c	    60  extern void FeeIllegalStateNotification(void);
; ..\eeprom\Fee_PBCfg.c	    61  
; ..\eeprom\Fee_PBCfg.c	    62  /*******************************************************************************
; ..\eeprom\Fee_PBCfg.c	    63  **                      Global Constant Definitions                           **
; ..\eeprom\Fee_PBCfg.c	    64  *******************************************************************************/
; ..\eeprom\Fee_PBCfg.c	    65  
; ..\eeprom\Fee_PBCfg.c	    66  /* Allocate space for state data variables in RAM */
; ..\eeprom\Fee_PBCfg.c	    67  #define FEE_START_SEC_VAR_FAST_UNSPECIFIED
; ..\eeprom\Fee_PBCfg.c	    68  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\eeprom\Fee_PBCfg.c	    69   allowed only for MemMap.h*/
; ..\eeprom\Fee_PBCfg.c	    70  //#include "MemMap.h"
; ..\eeprom\Fee_PBCfg.c	    71  
; ..\eeprom\Fee_PBCfg.c	    72  /* Fee State Variable structure */
; ..\eeprom\Fee_PBCfg.c	    73  static Fee_StateDataType Fee_StateVar;
; ..\eeprom\Fee_PBCfg.c	    74  
; ..\eeprom\Fee_PBCfg.c	    75  #define FEE_STOP_SEC_VAR_FAST_UNSPECIFIED
; ..\eeprom\Fee_PBCfg.c	    76  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\eeprom\Fee_PBCfg.c	    77   allowed only for MemMap.h*/
; ..\eeprom\Fee_PBCfg.c	    78  //#include "MemMap.h"
; ..\eeprom\Fee_PBCfg.c	    79  
; ..\eeprom\Fee_PBCfg.c	    80  
; ..\eeprom\Fee_PBCfg.c	    81  /* User configured logical block initialization structure */
; ..\eeprom\Fee_PBCfg.c	    82  #define FEE_START_SEC_CONST_UNSPECIFIED
; ..\eeprom\Fee_PBCfg.c	    83  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\eeprom\Fee_PBCfg.c	    84   allowed only for MemMap.h*/
; ..\eeprom\Fee_PBCfg.c	    85  //#include "MemMap.h"
; ..\eeprom\Fee_PBCfg.c	    86  
; ..\eeprom\Fee_PBCfg.c	    87  static const Fee_BlockType Fee_BlockConfig[] =
; ..\eeprom\Fee_PBCfg.c	    88  {
; ..\eeprom\Fee_PBCfg.c	    89    {
; ..\eeprom\Fee_PBCfg.c	    90      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	    91      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	    92      16U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	    93      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	    94    },
; ..\eeprom\Fee_PBCfg.c	    95    {
; ..\eeprom\Fee_PBCfg.c	    96      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	    97      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	    98      17U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	    99      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   100    },
; ..\eeprom\Fee_PBCfg.c	   101    {
; ..\eeprom\Fee_PBCfg.c	   102      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   103      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   104      32U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   105      12U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   106    },
; ..\eeprom\Fee_PBCfg.c	   107    {
; ..\eeprom\Fee_PBCfg.c	   108      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   109      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   110      48U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   111      50U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   112    },
; ..\eeprom\Fee_PBCfg.c	   113    {
; ..\eeprom\Fee_PBCfg.c	   114      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   115      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   116      64U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   117      50U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   118    },
; ..\eeprom\Fee_PBCfg.c	   119    {
; ..\eeprom\Fee_PBCfg.c	   120      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   121      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   122      80U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   123      50U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   124    },
; ..\eeprom\Fee_PBCfg.c	   125    {
; ..\eeprom\Fee_PBCfg.c	   126      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   127      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   128      96U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   129      50U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   130    },
; ..\eeprom\Fee_PBCfg.c	   131    {
; ..\eeprom\Fee_PBCfg.c	   132      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   133      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   134      112U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   135      50U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   136    },
; ..\eeprom\Fee_PBCfg.c	   137    {
; ..\eeprom\Fee_PBCfg.c	   138      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   139      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   140      128U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   141      50U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   142    },
; ..\eeprom\Fee_PBCfg.c	   143    {
; ..\eeprom\Fee_PBCfg.c	   144      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   145      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   146      144U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   147      50U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   148    },
; ..\eeprom\Fee_PBCfg.c	   149    {
; ..\eeprom\Fee_PBCfg.c	   150      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   151      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   152      160U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   153      50U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   154    },
; ..\eeprom\Fee_PBCfg.c	   155    {
; ..\eeprom\Fee_PBCfg.c	   156      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   157      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   158      176U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   159      24U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   160    },
; ..\eeprom\Fee_PBCfg.c	   161    {
; ..\eeprom\Fee_PBCfg.c	   162      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   163      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   164      192U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   165      17U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   166    },
; ..\eeprom\Fee_PBCfg.c	   167    {
; ..\eeprom\Fee_PBCfg.c	   168      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   169      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   170      208U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   171      17U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   172    },
; ..\eeprom\Fee_PBCfg.c	   173    {
; ..\eeprom\Fee_PBCfg.c	   174      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   175      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   176      224U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   177      16U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   178    },
; ..\eeprom\Fee_PBCfg.c	   179    {
; ..\eeprom\Fee_PBCfg.c	   180      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   181      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   182      1536U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   183      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   184    },
; ..\eeprom\Fee_PBCfg.c	   185    {
; ..\eeprom\Fee_PBCfg.c	   186      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   187      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   188      240U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   189      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   190    },
; ..\eeprom\Fee_PBCfg.c	   191    {
; ..\eeprom\Fee_PBCfg.c	   192      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   193      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   194      256U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   195      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   196    },
; ..\eeprom\Fee_PBCfg.c	   197    {
; ..\eeprom\Fee_PBCfg.c	   198      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   199      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   200      272U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   201      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   202    },
; ..\eeprom\Fee_PBCfg.c	   203    {
; ..\eeprom\Fee_PBCfg.c	   204      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   205      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   206      288U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   207      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   208    },
; ..\eeprom\Fee_PBCfg.c	   209    {
; ..\eeprom\Fee_PBCfg.c	   210      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   211      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   212      304U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   213      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   214    },
; ..\eeprom\Fee_PBCfg.c	   215    {
; ..\eeprom\Fee_PBCfg.c	   216      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   217      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   218      320U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   219      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   220    },
; ..\eeprom\Fee_PBCfg.c	   221    {
; ..\eeprom\Fee_PBCfg.c	   222      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   223      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   224      336U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   225      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   226    },
; ..\eeprom\Fee_PBCfg.c	   227    {
; ..\eeprom\Fee_PBCfg.c	   228      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   229      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   230      352U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   231      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   232    },
; ..\eeprom\Fee_PBCfg.c	   233    {
; ..\eeprom\Fee_PBCfg.c	   234      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   235      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   236      368U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   237      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   238    },
; ..\eeprom\Fee_PBCfg.c	   239    {
; ..\eeprom\Fee_PBCfg.c	   240      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   241      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   242      384U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   243      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   244    },
; ..\eeprom\Fee_PBCfg.c	   245    {
; ..\eeprom\Fee_PBCfg.c	   246      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   247      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   248      400U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   249      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   250    },
; ..\eeprom\Fee_PBCfg.c	   251    {
; ..\eeprom\Fee_PBCfg.c	   252      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   253      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   254      416U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   255      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   256    },
; ..\eeprom\Fee_PBCfg.c	   257    {
; ..\eeprom\Fee_PBCfg.c	   258      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   259      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   260      432U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   261      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   262    },
; ..\eeprom\Fee_PBCfg.c	   263    {
; ..\eeprom\Fee_PBCfg.c	   264      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   265      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   266      448U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   267      3U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   268    },
; ..\eeprom\Fee_PBCfg.c	   269    {
; ..\eeprom\Fee_PBCfg.c	   270      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   271      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   272      464U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   273      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   274    },
; ..\eeprom\Fee_PBCfg.c	   275    {
; ..\eeprom\Fee_PBCfg.c	   276      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   277      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   278      480U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   279      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   280    },
; ..\eeprom\Fee_PBCfg.c	   281    {
; ..\eeprom\Fee_PBCfg.c	   282      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   283      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   284      496U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   285      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   286    },
; ..\eeprom\Fee_PBCfg.c	   287    {
; ..\eeprom\Fee_PBCfg.c	   288      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   289      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   290      512U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   291      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   292    },
; ..\eeprom\Fee_PBCfg.c	   293    {
; ..\eeprom\Fee_PBCfg.c	   294      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   295      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   296      528U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   297      3U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   298    },
; ..\eeprom\Fee_PBCfg.c	   299    {
; ..\eeprom\Fee_PBCfg.c	   300      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   301      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   302      544U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   303      3U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   304    },
; ..\eeprom\Fee_PBCfg.c	   305    {
; ..\eeprom\Fee_PBCfg.c	   306      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   307      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   308      560U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   309      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   310    },
; ..\eeprom\Fee_PBCfg.c	   311    {
; ..\eeprom\Fee_PBCfg.c	   312      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   313      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   314      592U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   315      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   316    },
; ..\eeprom\Fee_PBCfg.c	   317    {
; ..\eeprom\Fee_PBCfg.c	   318      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   319      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   320      608U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   321      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   322    },
; ..\eeprom\Fee_PBCfg.c	   323    {
; ..\eeprom\Fee_PBCfg.c	   324      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   325      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   326      624U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   327      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   328    },
; ..\eeprom\Fee_PBCfg.c	   329    {
; ..\eeprom\Fee_PBCfg.c	   330      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   331      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   332      640U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   333      3U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   334    },
; ..\eeprom\Fee_PBCfg.c	   335    {
; ..\eeprom\Fee_PBCfg.c	   336      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   337      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   338      656U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   339      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   340    },
; ..\eeprom\Fee_PBCfg.c	   341    {
; ..\eeprom\Fee_PBCfg.c	   342      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   343      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   344      672U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   345      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   346    },
; ..\eeprom\Fee_PBCfg.c	   347    {
; ..\eeprom\Fee_PBCfg.c	   348      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   349      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   350      688U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   351      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   352    },
; ..\eeprom\Fee_PBCfg.c	   353    {
; ..\eeprom\Fee_PBCfg.c	   354      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   355      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   356      704U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   357      6U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   358    },
; ..\eeprom\Fee_PBCfg.c	   359    {
; ..\eeprom\Fee_PBCfg.c	   360      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   361      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   362      720U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   363      6U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   364    },
; ..\eeprom\Fee_PBCfg.c	   365    {
; ..\eeprom\Fee_PBCfg.c	   366      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   367      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   368      736U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   369      6U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   370    },
; ..\eeprom\Fee_PBCfg.c	   371    {
; ..\eeprom\Fee_PBCfg.c	   372      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   373      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   374      752U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   375      6U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   376    },
; ..\eeprom\Fee_PBCfg.c	   377    {
; ..\eeprom\Fee_PBCfg.c	   378      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   379      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   380      768U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   381      6U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   382    },
; ..\eeprom\Fee_PBCfg.c	   383    {
; ..\eeprom\Fee_PBCfg.c	   384      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   385      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   386      784U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   387      6U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   388    },
; ..\eeprom\Fee_PBCfg.c	   389    {
; ..\eeprom\Fee_PBCfg.c	   390      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   391      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   392      800U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   393      6U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   394    },
; ..\eeprom\Fee_PBCfg.c	   395    {
; ..\eeprom\Fee_PBCfg.c	   396      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   397      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   398      816U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   399      6U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   400    },
; ..\eeprom\Fee_PBCfg.c	   401    {
; ..\eeprom\Fee_PBCfg.c	   402      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   403      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   404      832U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   405      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   406    },
; ..\eeprom\Fee_PBCfg.c	   407    {
; ..\eeprom\Fee_PBCfg.c	   408      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   409      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   410      848U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   411      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   412    },
; ..\eeprom\Fee_PBCfg.c	   413    {
; ..\eeprom\Fee_PBCfg.c	   414      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   415      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   416      864U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   417      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   418    },
; ..\eeprom\Fee_PBCfg.c	   419    {
; ..\eeprom\Fee_PBCfg.c	   420      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   421      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   422      880U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   423      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   424    },
; ..\eeprom\Fee_PBCfg.c	   425    {
; ..\eeprom\Fee_PBCfg.c	   426      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   427      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   428      896U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   429      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   430    },
; ..\eeprom\Fee_PBCfg.c	   431    {
; ..\eeprom\Fee_PBCfg.c	   432      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   433      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   434      912U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   435      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   436    },
; ..\eeprom\Fee_PBCfg.c	   437    {
; ..\eeprom\Fee_PBCfg.c	   438      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   439      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   440      928U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   441      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   442    },
; ..\eeprom\Fee_PBCfg.c	   443    {
; ..\eeprom\Fee_PBCfg.c	   444      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   445      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   446      944U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   447      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   448    },
; ..\eeprom\Fee_PBCfg.c	   449    {
; ..\eeprom\Fee_PBCfg.c	   450      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   451      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   452      960U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   453      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   454    },
; ..\eeprom\Fee_PBCfg.c	   455    {
; ..\eeprom\Fee_PBCfg.c	   456      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   457      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   458      976U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   459      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   460    },
; ..\eeprom\Fee_PBCfg.c	   461    {
; ..\eeprom\Fee_PBCfg.c	   462      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   463      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   464      992U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   465      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   466    },
; ..\eeprom\Fee_PBCfg.c	   467    {
; ..\eeprom\Fee_PBCfg.c	   468      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   469      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   470      1008U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   471      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   472    },
; ..\eeprom\Fee_PBCfg.c	   473    {
; ..\eeprom\Fee_PBCfg.c	   474      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   475      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   476      1024U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   477      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   478    },
; ..\eeprom\Fee_PBCfg.c	   479    {
; ..\eeprom\Fee_PBCfg.c	   480      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   481      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   482      1040U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   483      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   484    },
; ..\eeprom\Fee_PBCfg.c	   485    {
; ..\eeprom\Fee_PBCfg.c	   486      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   487      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   488      1056U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   489      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   490    },
; ..\eeprom\Fee_PBCfg.c	   491    {
; ..\eeprom\Fee_PBCfg.c	   492      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   493      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   494      1072U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   495      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   496    },
; ..\eeprom\Fee_PBCfg.c	   497    {
; ..\eeprom\Fee_PBCfg.c	   498      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   499      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   500      1088U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   501      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   502    },
; ..\eeprom\Fee_PBCfg.c	   503    {
; ..\eeprom\Fee_PBCfg.c	   504      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   505      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   506      1104U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   507      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   508    },
; ..\eeprom\Fee_PBCfg.c	   509    {
; ..\eeprom\Fee_PBCfg.c	   510      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   511      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   512      1120U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   513      7U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   514    },
; ..\eeprom\Fee_PBCfg.c	   515    {
; ..\eeprom\Fee_PBCfg.c	   516      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   517      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   518      1136U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   519      7U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   520    },
; ..\eeprom\Fee_PBCfg.c	   521    {
; ..\eeprom\Fee_PBCfg.c	   522      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   523      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   524      1152U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   525      5U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   526    },
; ..\eeprom\Fee_PBCfg.c	   527    {
; ..\eeprom\Fee_PBCfg.c	   528      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   529      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   530      1168U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   531      18U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   532    },
; ..\eeprom\Fee_PBCfg.c	   533    {
; ..\eeprom\Fee_PBCfg.c	   534      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   535      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   536      1184U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   537      19U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   538    },
; ..\eeprom\Fee_PBCfg.c	   539    {
; ..\eeprom\Fee_PBCfg.c	   540      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   541      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   542      1200U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   543      7U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   544    },
; ..\eeprom\Fee_PBCfg.c	   545    {
; ..\eeprom\Fee_PBCfg.c	   546      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   547      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   548      1216U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   549      12U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   550    },
; ..\eeprom\Fee_PBCfg.c	   551    {
; ..\eeprom\Fee_PBCfg.c	   552      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   553      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   554      1232U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   555      13U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   556    },
; ..\eeprom\Fee_PBCfg.c	   557    {
; ..\eeprom\Fee_PBCfg.c	   558      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   559      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   560      1248U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   561      5U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   562    },
; ..\eeprom\Fee_PBCfg.c	   563    {
; ..\eeprom\Fee_PBCfg.c	   564      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   565      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   566      1264U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   567      22U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   568    },
; ..\eeprom\Fee_PBCfg.c	   569    {
; ..\eeprom\Fee_PBCfg.c	   570      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   571      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   572      1280U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   573      17U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   574    },
; ..\eeprom\Fee_PBCfg.c	   575    {
; ..\eeprom\Fee_PBCfg.c	   576      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   577      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   578      1296U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   579      17U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   580    },
; ..\eeprom\Fee_PBCfg.c	   581    {
; ..\eeprom\Fee_PBCfg.c	   582      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   583      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   584      1312U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   585      16U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   586    },
; ..\eeprom\Fee_PBCfg.c	   587    {
; ..\eeprom\Fee_PBCfg.c	   588      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   589      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   590      1328U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   591      3U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   592    },
; ..\eeprom\Fee_PBCfg.c	   593    {
; ..\eeprom\Fee_PBCfg.c	   594      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   595      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   596      1344U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   597      3U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   598    },
; ..\eeprom\Fee_PBCfg.c	   599    {
; ..\eeprom\Fee_PBCfg.c	   600      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   601      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   602      1360U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   603      3U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   604    },
; ..\eeprom\Fee_PBCfg.c	   605    {
; ..\eeprom\Fee_PBCfg.c	   606      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   607      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   608      1376U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   609      66U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   610    },
; ..\eeprom\Fee_PBCfg.c	   611    {
; ..\eeprom\Fee_PBCfg.c	   612      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   613      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   614      1392U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   615      66U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   616    },
; ..\eeprom\Fee_PBCfg.c	   617    {
; ..\eeprom\Fee_PBCfg.c	   618      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   619      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   620      1408U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   621      10U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   622    },
; ..\eeprom\Fee_PBCfg.c	   623    {
; ..\eeprom\Fee_PBCfg.c	   624      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   625      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   626      1424U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   627      10U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   628    },
; ..\eeprom\Fee_PBCfg.c	   629    {
; ..\eeprom\Fee_PBCfg.c	   630      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   631      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   632      1440U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   633      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   634    },
; ..\eeprom\Fee_PBCfg.c	   635    {
; ..\eeprom\Fee_PBCfg.c	   636      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   637      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   638      1456U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   639      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   640    },
; ..\eeprom\Fee_PBCfg.c	   641    {
; ..\eeprom\Fee_PBCfg.c	   642      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   643      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   644      1472U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   645      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   646    },
; ..\eeprom\Fee_PBCfg.c	   647    {
; ..\eeprom\Fee_PBCfg.c	   648      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   649      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   650      1488U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   651      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   652    },
; ..\eeprom\Fee_PBCfg.c	   653    {
; ..\eeprom\Fee_PBCfg.c	   654      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   655      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   656      1504U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   657      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   658    },
; ..\eeprom\Fee_PBCfg.c	   659    {
; ..\eeprom\Fee_PBCfg.c	   660      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   661      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   662      1520U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   663      4U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   664    },
; ..\eeprom\Fee_PBCfg.c	   665    {
; ..\eeprom\Fee_PBCfg.c	   666      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   667      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   668      208U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   669      7U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   670    },
; ..\eeprom\Fee_PBCfg.c	   671    {
; ..\eeprom\Fee_PBCfg.c	   672      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   673      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   674      192U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   675      7U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   676    },
; ..\eeprom\Fee_PBCfg.c	   677    {
; ..\eeprom\Fee_PBCfg.c	   678      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   679      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   680      224U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   681      14U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   682    },
; ..\eeprom\Fee_PBCfg.c	   683    {
; ..\eeprom\Fee_PBCfg.c	   684      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   685      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   686      1552U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   687      112U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   688    },
; ..\eeprom\Fee_PBCfg.c	   689    {
; ..\eeprom\Fee_PBCfg.c	   690      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   691      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   692      576U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   693      3U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   694    },
; ..\eeprom\Fee_PBCfg.c	   695    {
; ..\eeprom\Fee_PBCfg.c	   696      0U, /* Block Cycle Count */
; ..\eeprom\Fee_PBCfg.c	   697      (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
; ..\eeprom\Fee_PBCfg.c	   698      1568U, /* Block number */
; ..\eeprom\Fee_PBCfg.c	   699      42U /* Fee Block Size */
; ..\eeprom\Fee_PBCfg.c	   700    }
; ..\eeprom\Fee_PBCfg.c	   701  };
; ..\eeprom\Fee_PBCfg.c	   702  
; ..\eeprom\Fee_PBCfg.c	   703  /* Fee Global initialization structure. */
; ..\eeprom\Fee_PBCfg.c	   704  const Fee_ConfigType Fee_ConfigRoot[] =
; ..\eeprom\Fee_PBCfg.c	   705  {
; ..\eeprom\Fee_PBCfg.c	   706    {
; ..\eeprom\Fee_PBCfg.c	   707      /* Fee State Data Structure */
; ..\eeprom\Fee_PBCfg.c	   708      &Fee_StateVar,
; ..\eeprom\Fee_PBCfg.c	   709      /* Pointer to logical block configurations */
; ..\eeprom\Fee_PBCfg.c	   710      &Fee_BlockConfig[0],
; ..\eeprom\Fee_PBCfg.c	   711      /* Fee Job end notification API */
; ..\eeprom\Fee_PBCfg.c	   712      &NvM_JobEndNotification,
; ..\eeprom\Fee_PBCfg.c	   713      /* Fee Job error notification API */
; ..\eeprom\Fee_PBCfg.c	   714      &NvM_JobErrorNotification,
; ..\eeprom\Fee_PBCfg.c	   715      /* Fee threshold value */
; ..\eeprom\Fee_PBCfg.c	   716      2288U,
; ..\eeprom\Fee_PBCfg.c	   717      /* Number of blocks configured */
; ..\eeprom\Fee_PBCfg.c	   718      102U,
; ..\eeprom\Fee_PBCfg.c	   719  
; ..\eeprom\Fee_PBCfg.c	   720      {
; ..\eeprom\Fee_PBCfg.c	   721        /* Ignore the unconfigured blocks */
; ..\eeprom\Fee_PBCfg.c	   722        FEE_UNCONFIG_BLOCK_IGNORE,
; ..\eeprom\Fee_PBCfg.c	   723        /* Restart Garbage Collection during initialization */
; ..\eeprom\Fee_PBCfg.c	   724        FEE_GC_RESTART_INIT,
; ..\eeprom\Fee_PBCfg.c	   725        /* Erase Suspend feature is disabled */
; ..\eeprom\Fee_PBCfg.c	   726        FEE_ERASE_SUSPEND_DISABLED,
; ..\eeprom\Fee_PBCfg.c	   727        /* Reserved */
; ..\eeprom\Fee_PBCfg.c	   728        0U
; ..\eeprom\Fee_PBCfg.c	   729      },
; ..\eeprom\Fee_PBCfg.c	   730  
; ..\eeprom\Fee_PBCfg.c	   731      /* Fee Illegal State notification */
; ..\eeprom\Fee_PBCfg.c	   732      &FeeIllegalStateNotification,
; ..\eeprom\Fee_PBCfg.c	   733      /* Erase All feature is enabled */
; ..\eeprom\Fee_PBCfg.c	   734      (boolean)TRUE
; ..\eeprom\Fee_PBCfg.c	   735    }
; ..\eeprom\Fee_PBCfg.c	   736  };
; ..\eeprom\Fee_PBCfg.c	   737  
; ..\eeprom\Fee_PBCfg.c	   738  #define FEE_STOP_SEC_CONST_UNSPECIFIED
; ..\eeprom\Fee_PBCfg.c	   739  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\eeprom\Fee_PBCfg.c	   740   allowed only for MemMap.h*/
; ..\eeprom\Fee_PBCfg.c	   741  //#include "MemMap.h"
; ..\eeprom\Fee_PBCfg.c	   742  
; ..\eeprom\Fee_PBCfg.c	   743  #define FEE_START_SEC_SPL_VAR_32BIT
; ..\eeprom\Fee_PBCfg.c	   744  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\eeprom\Fee_PBCfg.c	   745   allowed only for MemMap.h*/
; ..\eeprom\Fee_PBCfg.c	   746  //#include "MemMap.h"
; ..\eeprom\Fee_PBCfg.c	   747  const Fee_ConfigType * Fee_CfgPtr = &Fee_ConfigRoot[0];
; ..\eeprom\Fee_PBCfg.c	   748  #define FEE_STOP_SEC_SPL_VAR_32BIT
; ..\eeprom\Fee_PBCfg.c	   749  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\eeprom\Fee_PBCfg.c	   750   allowed only for MemMap.h*/
; ..\eeprom\Fee_PBCfg.c	   751  //#include "MemMap.h"
; ..\eeprom\Fee_PBCfg.c	   752  

	; Module end
