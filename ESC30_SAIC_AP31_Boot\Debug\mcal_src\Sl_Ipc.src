	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc20568a -c99 --dep-file=mcal_src\\.Sl_Ipc.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Sl_Ipc.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Sl_Ipc.src ..\\mcal_src\\Sl_Ipc.c"
	.compiler_name		"ctc"
	.name	"Sl_Ipc"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Sl_SpinLockInit')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Sl_SpinLockInit

; ..\mcal_src\Sl_Ipc.c	     1  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	     2  ** Copyright (C) Infineon Technologies (2012)                                 **
; ..\mcal_src\Sl_Ipc.c	     3  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	     4  ** All rights reserved.                                                       **
; ..\mcal_src\Sl_Ipc.c	     5  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	     6  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Sl_Ipc.c	     7  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Sl_Ipc.c	     8  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Sl_Ipc.c	     9  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    10  ********************************************************************************
; ..\mcal_src\Sl_Ipc.c	    11  **  $FILENAME   : Sl_Ipc.c $                                                 **
; ..\mcal_src\Sl_Ipc.c	    12  **                                                                           **
; ..\mcal_src\Sl_Ipc.c	    13  **  $CC VERSION : \main\22 $                                                 **
; ..\mcal_src\Sl_Ipc.c	    14  **                                                                           **
; ..\mcal_src\Sl_Ipc.c	    15  **  $DATE       : 2016-02-16 $                                               **
; ..\mcal_src\Sl_Ipc.c	    16  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    17  **  VARIANT   : VariantPB                                                     **
; ..\mcal_src\Sl_Ipc.c	    18  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    19  **  PLATFORM  : Infineon AURIX                                                **
; ..\mcal_src\Sl_Ipc.c	    20  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    21  **  COMPILER  : Tasking                                                       **
; ..\mcal_src\Sl_Ipc.c	    22  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    23  **  AUTHOR    : SafeTlib Team                                                 **
; ..\mcal_src\Sl_Ipc.c	    24  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    25  **  VENDOR    : Infineon Technologies                                         **
; ..\mcal_src\Sl_Ipc.c	    26  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    27  **  TRACEABILITY:                                                             **
; ..\mcal_src\Sl_Ipc.c	    28  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    29  ** Description      : This file contains                                      **
; ..\mcal_src\Sl_Ipc.c	    30  **                 - Mcal library definitions                                 **
; ..\mcal_src\Sl_Ipc.c	    31  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    32  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    33  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    34  **  SPECIFICATION(S) :                                                        **
; ..\mcal_src\Sl_Ipc.c	    35  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    36  **  MAY BE CHANGED BY USER [Yes/No]: No                                       **
; ..\mcal_src\Sl_Ipc.c	    37  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	    38  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	    39  
; ..\mcal_src\Sl_Ipc.c	    40  /******************************************************************************
; ..\mcal_src\Sl_Ipc.c	    41  **                      Includes                                              *
; ..\mcal_src\Sl_Ipc.c	    42  ******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	    43  #include "Platform_Types.h"
; ..\mcal_src\Sl_Ipc.c	    44  #include "Compiler_Cfg.h"
; ..\mcal_src\Sl_Ipc.c	    45  #include "Mcal_TcLib.h"
; ..\mcal_src\Sl_Ipc.c	    46  #include "Sl_Ipc.h"
; ..\mcal_src\Sl_Ipc.c	    47  #include "Mcal_Options.h"
; ..\mcal_src\Sl_Ipc.c	    48  
; ..\mcal_src\Sl_Ipc.c	    49  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	    50  **                      Private Macro Definitions                             **
; ..\mcal_src\Sl_Ipc.c	    51  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	    52  #define SL_IPC_REDUNDANT_RESULT   (0xFFFFFFFFU)
; ..\mcal_src\Sl_Ipc.c	    53  
; ..\mcal_src\Sl_Ipc.c	    54  
; ..\mcal_src\Sl_Ipc.c	    55  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	    56  **                      Private Type Definitions                              **
; ..\mcal_src\Sl_Ipc.c	    57  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	    58  
; ..\mcal_src\Sl_Ipc.c	    59  
; ..\mcal_src\Sl_Ipc.c	    60  
; ..\mcal_src\Sl_Ipc.c	    61  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	    62  **                      Private Variable Definitions                          **
; ..\mcal_src\Sl_Ipc.c	    63  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	    64  
; ..\mcal_src\Sl_Ipc.c	    65  
; ..\mcal_src\Sl_Ipc.c	    66  
; ..\mcal_src\Sl_Ipc.c	    67  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	    68  **      Global Variable Definitions                                           **
; ..\mcal_src\Sl_Ipc.c	    69  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	    70  #if (IFX_SAFETLIB_USED == STD_ON)
; ..\mcal_src\Sl_Ipc.c	    71  #define IFX_APPL_START_SEC_VAR_32BIT_ASIL_B
; ..\mcal_src\Sl_Ipc.c	    72  #include "Ifx_MemMap.h"
; ..\mcal_src\Sl_Ipc.c	    73  #else
; ..\mcal_src\Sl_Ipc.c	    74  #define APPL_START_SEC_VAR_32BIT
; ..\mcal_src\Sl_Ipc.c	    75  #include "MemMap.h"
; ..\mcal_src\Sl_Ipc.c	    76  #endif
; ..\mcal_src\Sl_Ipc.c	    77  
; ..\mcal_src\Sl_Ipc.c	    78  /* All the spinlocks to be defined here */
; ..\mcal_src\Sl_Ipc.c	    79  #ifdef __TASKING__
; ..\mcal_src\Sl_Ipc.c	    80  __align(4) static uint32 Sl_SpinLockArr[SL_SPINLOCK_CNT];
; ..\mcal_src\Sl_Ipc.c	    81  __align(4) static uint32 Sl_SpinLockRednArr[SL_SPINLOCK_CNT];
; ..\mcal_src\Sl_Ipc.c	    82  #elif __GNUC__
; ..\mcal_src\Sl_Ipc.c	    83  static uint32 Sl_SpinLockArr[SL_SPINLOCK_CNT] __attribute__ ((aligned(4)));
; ..\mcal_src\Sl_Ipc.c	    84  static uint32 Sl_SpinLockRednArr[SL_SPINLOCK_CNT] __attribute__ ((aligned(4)));
; ..\mcal_src\Sl_Ipc.c	    85  #elif defined _DIABDATA_C_TRICORE_
; ..\mcal_src\Sl_Ipc.c	    86  #if (_DIABDATA_C_TRICORE_ == 1U)
; ..\mcal_src\Sl_Ipc.c	    87  static uint32 Sl_SpinLockArr[SL_SPINLOCK_CNT] __attribute__ ((aligned(4)));
; ..\mcal_src\Sl_Ipc.c	    88  static uint32 Sl_SpinLockRednArr[SL_SPINLOCK_CNT] __attribute__ ((aligned(4)));
; ..\mcal_src\Sl_Ipc.c	    89  #endif /* #if (_DIABDATA_C_TRICORE_ == 1U) */
; ..\mcal_src\Sl_Ipc.c	    90  #endif
; ..\mcal_src\Sl_Ipc.c	    91  
; ..\mcal_src\Sl_Ipc.c	    92  #if (IFX_SAFETLIB_USED == STD_ON)
; ..\mcal_src\Sl_Ipc.c	    93  #define IFX_APPL_STOP_SEC_VAR_32BIT_ASIL_B
; ..\mcal_src\Sl_Ipc.c	    94  #include "Ifx_MemMap.h"
; ..\mcal_src\Sl_Ipc.c	    95  #else
; ..\mcal_src\Sl_Ipc.c	    96  #define APPL_STOP_SEC_VAR_32BIT
; ..\mcal_src\Sl_Ipc.c	    97  #include "MemMap.h"
; ..\mcal_src\Sl_Ipc.c	    98  #endif
; ..\mcal_src\Sl_Ipc.c	    99  
; ..\mcal_src\Sl_Ipc.c	   100  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	   101  **                      Global Constant Definitions                           **
; ..\mcal_src\Sl_Ipc.c	   102  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	   103  
; ..\mcal_src\Sl_Ipc.c	   104  #if (IFX_SAFETLIB_USED == STD_ON)
; ..\mcal_src\Sl_Ipc.c	   105  #define IFX_APPL_START_SEC_CODE_ASIL_B
; ..\mcal_src\Sl_Ipc.c	   106  #include "Ifx_MemMap.h"
; ..\mcal_src\Sl_Ipc.c	   107  #else
; ..\mcal_src\Sl_Ipc.c	   108  #define APPL_START_SEC_CODE
; ..\mcal_src\Sl_Ipc.c	   109  #include "MemMap.h"
; ..\mcal_src\Sl_Ipc.c	   110  #endif
; ..\mcal_src\Sl_Ipc.c	   111  
; ..\mcal_src\Sl_Ipc.c	   112  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	   113  **                      Private Function Declarations                         **
; ..\mcal_src\Sl_Ipc.c	   114  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	   115  
; ..\mcal_src\Sl_Ipc.c	   116  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	   117  ** Traceability     : [cover parentID=]        [/cover]                       **
; ..\mcal_src\Sl_Ipc.c	   118  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   119  ** Syntax           : void Sl_SpinLockInit(void)                              **
; ..\mcal_src\Sl_Ipc.c	   120  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   121  ** Service ID       : None                                                    **
; ..\mcal_src\Sl_Ipc.c	   122  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   123  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Sl_Ipc.c	   124  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   125  ** Reentrancy       : Non-Reentrant                                           **
; ..\mcal_src\Sl_Ipc.c	   126  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   127  ** Parameters(in)   :  None                                                   **
; ..\mcal_src\Sl_Ipc.c	   128  ** Parameters (out) : None                                                    **
; ..\mcal_src\Sl_Ipc.c	   129  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   130  ** Return value     : None                                                    **
; ..\mcal_src\Sl_Ipc.c	   131  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   132  ** Description      :                                                         **
; ..\mcal_src\Sl_Ipc.c	   133  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   134  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	   135  void Sl_SpinLockInit()
; Function Sl_SpinLockInit
.L10:
Sl_SpinLockInit:	.type	func

; ..\mcal_src\Sl_Ipc.c	   136  {
; ..\mcal_src\Sl_Ipc.c	   137    uint32 Cnt;
; ..\mcal_src\Sl_Ipc.c	   138  
; ..\mcal_src\Sl_Ipc.c	   139    for(Cnt = 0U; Cnt < (uint32)SL_SPINLOCK_CNT; Cnt++)
; ..\mcal_src\Sl_Ipc.c	   140    {
; ..\mcal_src\Sl_Ipc.c	   141      Sl_SpinLockArr[Cnt] = 0U;
	mov	d15,#0
	fcall	.cocofun_1
.L74:

; ..\mcal_src\Sl_Ipc.c	   142      /* Redundant copy handling */
; ..\mcal_src\Sl_Ipc.c	   143      Sl_SpinLockRednArr[Cnt] = (uint32)0xFFFFFFFFU;
	mov	d0,#-1
	movh.a	a2,#@his(Sl_SpinLockRednArr)
	lea	a2,[a2]@los(Sl_SpinLockRednArr)
.L75:
	mov.a	a4,#9
.L2:
	st.w	[a15+],d15
.L76:
	st.w	[a2+],d0
	loop	a4,.L2
.L77:

; ..\mcal_src\Sl_Ipc.c	   144    }
; ..\mcal_src\Sl_Ipc.c	   145  }
	ret
.L45:
	
__Sl_SpinLockInit_function_end:
	.size	Sl_SpinLockInit,__Sl_SpinLockInit_function_end-Sl_SpinLockInit
.L25:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_1')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_1
.L12:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh.a	a15,#@his(Sl_SpinLockArr)
	lea	a15,[a15]@los(Sl_SpinLockArr)
.L103:
	fret
.L40:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Sl_GetSpinLock')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Sl_GetSpinLock

; ..\mcal_src\Sl_Ipc.c	   146  
; ..\mcal_src\Sl_Ipc.c	   147  
; ..\mcal_src\Sl_Ipc.c	   148  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	   149  ** Traceability     : [cover parentID=]        [/cover]                       **
; ..\mcal_src\Sl_Ipc.c	   150  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   151  ** Syntax           : Std_ReturnType Sl_GetSpinLock                           **
; ..\mcal_src\Sl_Ipc.c	   152  **                    (                                                       **
; ..\mcal_src\Sl_Ipc.c	   153  **                      SlSpnlockId SpinLckID,uint32 Timeout                  **
; ..\mcal_src\Sl_Ipc.c	   154  **                    )                                                       **
; ..\mcal_src\Sl_Ipc.c	   155  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   156  ** Service ID       : None                                                    **
; ..\mcal_src\Sl_Ipc.c	   157  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   158  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Sl_Ipc.c	   159  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   160  ** Reentrancy       : Non-Reentrant                                           **
; ..\mcal_src\Sl_Ipc.c	   161  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   162  ** Parameters(in)   : SpinLckID: Spinlock to be acquired                      **
; ..\mcal_src\Sl_Ipc.c	   163  **                    Timeout: Wait duration for acquisition of SpinLock      **
; ..\mcal_src\Sl_Ipc.c	   164  ** Parameters (out) : None                                                    **
; ..\mcal_src\Sl_Ipc.c	   165  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   166  ** Return value     : E_OK - Release acquired successfully                    **
; ..\mcal_src\Sl_Ipc.c	   167  **                  : E_NOT_OK - Release not acquired successfully due to     **
; ..\mcal_src\Sl_Ipc.c	   168  **                    timeout or wrong parameter                              **
; ..\mcal_src\Sl_Ipc.c	   169  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   170  ** Description      :                                                         **
; ..\mcal_src\Sl_Ipc.c	   171  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   172  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	   173  Std_ReturnType Sl_GetSpinLock(const SlSpnlockId SpinLckID,uint32 Timeout)
; Function Sl_GetSpinLock
.L14:
Sl_GetSpinLock:	.type	func
	mov	d15,d4
.L60:

; ..\mcal_src\Sl_Ipc.c	   174  {
; ..\mcal_src\Sl_Ipc.c	   175    Std_ReturnType RetVal;
; ..\mcal_src\Sl_Ipc.c	   176  
; ..\mcal_src\Sl_Ipc.c	   177    RetVal = E_NOT_OK;
	mov	d2,#1
.L59:

; ..\mcal_src\Sl_Ipc.c	   178  
; ..\mcal_src\Sl_Ipc.c	   179    if (SpinLckID < SL_SPINLOCK_CNT)
	jge.u	d15,#10,.L3
.L82:

; ..\mcal_src\Sl_Ipc.c	   180    {
; ..\mcal_src\Sl_Ipc.c	   181  
; ..\mcal_src\Sl_Ipc.c	   182  
; ..\mcal_src\Sl_Ipc.c	   183        RetVal = Mcal_GetSpinLock(&Sl_SpinLockArr[SpinLckID],Timeout);
	fcall	.cocofun_1
.L83:
	addsc.a	a15,a15,d15,#2
.L84:
	mov	d4,d5
.L62:
	mov.aa	a4,a15
	call	Mcal_GetSpinLock
.L63:

; ..\mcal_src\Sl_Ipc.c	   184        if (RetVal == E_OK)
	jne	d2,#0,.L4
.L85:

; ..\mcal_src\Sl_Ipc.c	   185        {
; ..\mcal_src\Sl_Ipc.c	   186            /* Verify against the redundant copy */
; ..\mcal_src\Sl_Ipc.c	   187           if (((Sl_SpinLockArr[SpinLckID]-1U) ^ Sl_SpinLockRednArr[SpinLckID])
	movh.a	a2,#@his(Sl_SpinLockRednArr)
	lea	a2,[a2]@los(Sl_SpinLockRednArr)
.L86:
	addsc.a	a2,a2,d15,#2
.L87:
	ld.w	d0,[a15]
.L88:
	ld.w	d15,[a2]
.L61:
	add	d0,#-1
.L89:
	xor	d0,d15
.L90:
	jne	d0,#-1,.L5
.L91:

; ..\mcal_src\Sl_Ipc.c	   188                            == SL_IPC_REDUNDANT_RESULT)
; ..\mcal_src\Sl_Ipc.c	   189             {
; ..\mcal_src\Sl_Ipc.c	   190                 Sl_SpinLockRednArr[SpinLckID] = 
; ..\mcal_src\Sl_Ipc.c	   191                  Sl_SpinLockRednArr[SpinLckID] - (uint32)1U;
	add	d15,#-1
	st.w	[a2],d15
.L4:
.L3:

; ..\mcal_src\Sl_Ipc.c	   192             }
; ..\mcal_src\Sl_Ipc.c	   193           else
; ..\mcal_src\Sl_Ipc.c	   194             {
; ..\mcal_src\Sl_Ipc.c	   195               RetVal = E_NOT_OK;
; ..\mcal_src\Sl_Ipc.c	   196             }
; ..\mcal_src\Sl_Ipc.c	   197  
; ..\mcal_src\Sl_Ipc.c	   198        }
; ..\mcal_src\Sl_Ipc.c	   199  
; ..\mcal_src\Sl_Ipc.c	   200    }
; ..\mcal_src\Sl_Ipc.c	   201  
; ..\mcal_src\Sl_Ipc.c	   202    return(RetVal);
; ..\mcal_src\Sl_Ipc.c	   203  }
	ret
.L5:
	mov	d2,#1
	ret
.L50:
	
__Sl_GetSpinLock_function_end:
	.size	Sl_GetSpinLock,__Sl_GetSpinLock_function_end-Sl_GetSpinLock
.L30:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Sl_ReleaseSpinLock')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Sl_ReleaseSpinLock

; ..\mcal_src\Sl_Ipc.c	   204  
; ..\mcal_src\Sl_Ipc.c	   205  /*******************************************************************************
; ..\mcal_src\Sl_Ipc.c	   206  ** Traceability     : [cover parentID=]        [/cover]                       **
; ..\mcal_src\Sl_Ipc.c	   207  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   208  ** Syntax           : void Sl_ReleaseSpinLock                                 **
; ..\mcal_src\Sl_Ipc.c	   209  **                    (                                                       **
; ..\mcal_src\Sl_Ipc.c	   210  **                      SlSpnlockId SpinLckID                                 **
; ..\mcal_src\Sl_Ipc.c	   211  **                    )                                                       **
; ..\mcal_src\Sl_Ipc.c	   212  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   213  ** Service ID       : None                                                    **
; ..\mcal_src\Sl_Ipc.c	   214  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   215  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Sl_Ipc.c	   216  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   217  ** Reentrancy       : Non-Reentrant                                           **
; ..\mcal_src\Sl_Ipc.c	   218  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   219  ** Parameters(in)   : SpinLckID: Spinlock to be released                      **
; ..\mcal_src\Sl_Ipc.c	   220  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   221  ** Parameters (out) : None                                                    **
; ..\mcal_src\Sl_Ipc.c	   222  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   223  ** Return value     : None                                                    **
; ..\mcal_src\Sl_Ipc.c	   224  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   225  ** Description      :                                                         **
; ..\mcal_src\Sl_Ipc.c	   226  **                                                                            **
; ..\mcal_src\Sl_Ipc.c	   227  *******************************************************************************/
; ..\mcal_src\Sl_Ipc.c	   228  void Sl_ReleaseSpinLock(SlSpnlockId SpinLckID)
; Function Sl_ReleaseSpinLock
.L16:
Sl_ReleaseSpinLock:	.type	func

; ..\mcal_src\Sl_Ipc.c	   229  {
; ..\mcal_src\Sl_Ipc.c	   230    if (SpinLckID < SL_SPINLOCK_CNT)
	jge.u	d4,#10,.L8
.L96:

; ..\mcal_src\Sl_Ipc.c	   231    {
; ..\mcal_src\Sl_Ipc.c	   232      /* no check for redundancy required, if corruption happens
; ..\mcal_src\Sl_Ipc.c	   233       * next acquire will catch the corruption
; ..\mcal_src\Sl_Ipc.c	   234       */
; ..\mcal_src\Sl_Ipc.c	   235      Sl_SpinLockRednArr[SpinLckID] = 
; ..\mcal_src\Sl_Ipc.c	   236                  Sl_SpinLockRednArr[SpinLckID] + (uint32)1U;
	movh.a	a15,#@his(Sl_SpinLockRednArr)
	lea	a15,[a15]@los(Sl_SpinLockRednArr)
.L97:
	addsc.a	a15,a15,d4,#2
	ld.w	d15,[a15]
.L98:
	add	d15,#1
	st.w	[a15],d15
.L64:

; ..\mcal_src\Sl_Ipc.c	   237      /* Reset the SpinLock*/
; ..\mcal_src\Sl_Ipc.c	   238      Mcal_ReleaseSpinLock(&Sl_SpinLockArr[SpinLckID]);
	fcall	.cocofun_1
.L65:
	addsc.a	a4,a15,d4,#2
	j	Mcal_ReleaseSpinLock
.L8:

; ..\mcal_src\Sl_Ipc.c	   239  
; ..\mcal_src\Sl_Ipc.c	   240    }
; ..\mcal_src\Sl_Ipc.c	   241  }
	ret
.L55:
	
__Sl_ReleaseSpinLock_function_end:
	.size	Sl_ReleaseSpinLock,__Sl_ReleaseSpinLock_function_end-Sl_ReleaseSpinLock
.L35:
	; End of function
	
	.sdecl	'.bss.CPU0.Private.DEFAULT_RAM_32BIT',data,cluster('Sl_SpinLockArr')
	.sect	'.bss.CPU0.Private.DEFAULT_RAM_32BIT'
	.align	4
Sl_SpinLockArr:	.type	object
	.size	Sl_SpinLockArr,40
	.space	40
	.sdecl	'.bss.CPU0.Private.DEFAULT_RAM_32BIT',data,cluster('Sl_SpinLockRednArr')
	.sect	'.bss.CPU0.Private.DEFAULT_RAM_32BIT'
	.align	4
Sl_SpinLockRednArr:	.type	object
	.size	Sl_SpinLockRednArr,40
	.space	40
	.calls	'Sl_GetSpinLock','Mcal_GetSpinLock'
	.calls	'Sl_ReleaseSpinLock','Mcal_ReleaseSpinLock'
	.calls	'Sl_SpinLockInit','.cocofun_1'
	.calls	'Sl_GetSpinLock','.cocofun_1'
	.calls	'Sl_ReleaseSpinLock','.cocofun_1'
	.calls	'Sl_SpinLockInit','',0
	.calls	'.cocofun_1','',0
	.calls	'Sl_GetSpinLock','',0
	.extern	Mcal_GetSpinLock
	.extern	Mcal_ReleaseSpinLock
	.calls	'Sl_ReleaseSpinLock','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L18:
	.word	806
	.half	3
	.word	.L19
	.byte	4
.L17:
	.byte	1
	.byte	'..\\mcal_src\\Sl_Ipc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L20
.L49:
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'Mcal_GetSpinLock',0,1,151,3,23
	.word	175
	.byte	1,1,1,1
.L47:
	.byte	2
	.byte	'unsigned long int',0,4,7,4
	.word	222
	.byte	5
	.byte	'SpinLckPtr',0,1,151,3,48
	.word	243
	.byte	5
	.byte	'Timeout',0,1,151,3,66
	.word	222
	.byte	0,6
	.byte	'Mcal_ReleaseSpinLock',0,1,176,3,13,1,1,1,1,5
	.byte	'SpinLckPtr',0,1,176,3,42
	.word	243
	.byte	0
.L56:
	.byte	7,2,57,9,1,8
	.byte	'SL_SMUALRMGRP3_SPNLCK_ID',0,0,8
	.byte	'SL_LMU_SPNLCK_ID',0,1,8
	.byte	'SL_DMA_SPNLCK_ID',0,2,8
	.byte	'SL_FCE_SPNLCK_ID',0,3,8
	.byte	'SL_SMUDRIVER_SPNLCK_ID',0,4,8
	.byte	'SL_SMUALRMGRP3_BIT30_SPNLCK_ID',0,5,8
	.byte	'SL_SMUALRMGRP5_ALM0_SPNLCK_ID',0,6,8
	.byte	'SL_SMUALRMGRP5_ALM1_SPNLCK_ID',0,7,8
	.byte	'SL_SMUALRMGRP5_ALM2_SPNLCK_ID',0,8,8
	.byte	'SL_SMUALRMGRP5_ALM3_SPNLCK_ID',0,9,8
	.byte	'SL_SPINLOCK_CNT',0,10,0
.L51:
	.byte	9
	.word	337
	.byte	10
	.byte	'void',0,4
	.word	636
	.byte	11
	.byte	'__prof_adm',0,3,1,1
	.word	642
	.byte	12,1,4
	.word	666
	.byte	11
	.byte	'__codeptr',0,3,1,1
	.word	668
	.byte	11
	.byte	'uint8',0,4,90,29
	.word	175
	.byte	2
	.byte	'unsigned short int',0,2,7,11
	.byte	'uint16',0,4,92,29
	.word	705
	.byte	11
	.byte	'uint32',0,4,94,29
	.word	222
	.byte	11
	.byte	'Std_ReturnType',0,5,113,15
	.word	175
	.byte	11
	.byte	'SlSpnlockId',0,2,72,2
	.word	337
.L58:
	.byte	13,40
	.word	222
	.byte	14,9,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,58,15,59,15,57,15
	.byte	73,19,54,15,39,12,63,12,60,12,0,0,4,15,0,73,19,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,46,1,3,8,58
	.byte	15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,7,4,1,58,15,59,15,57,15,11,15,0,0,8,40,0,3,8,28,13,0,0,9,38
	.byte	0,73,19,0,0,10,59,0,3,8,0,0,11,22,0,3,8,58,15,59,15,57,15,73,19,0,0,12,21,0,54,15,0,0,13,1,1,11,15,73
	.byte	19,0,0,14,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L20:
	.word	.L67-.L66
.L66:
	.half	3
	.word	.L69-.L68
.L68:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcal_TcLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Sl_Ipc.h',0,0,0,0
	.byte	'..\\mcal_src\\Sl_Ipc.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Std_Types.h',0,0,0,0,0
.L69:
.L67:
	.sdecl	'.debug_info',debug,cluster('Sl_SpinLockInit')
	.sect	'.debug_info'
.L21:
	.word	241
	.half	3
	.word	.L22
	.byte	4,1
	.byte	'..\\mcal_src\\Sl_Ipc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L24,.L23
	.byte	2
	.word	.L17
	.byte	3
	.byte	'Sl_SpinLockInit',0,1,135,1,6,1,1,1
	.word	.L10,.L45,.L9
	.byte	4
	.word	.L46
	.byte	5
	.byte	'Cnt',0,1,137,1,10
	.word	.L47,.L48
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Sl_SpinLockInit')
	.sect	'.debug_abbrev'
.L22:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Sl_SpinLockInit')
	.sect	'.debug_line'
.L23:
	.word	.L71-.L70
.L70:
	.half	3
	.word	.L73-.L72
.L72:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Sl_Ipc.c',0,0,0,0,0
.L73:
	.byte	5,13,7,0,5,2
	.word	.L10
	.byte	3,138,1,1,5,5,3,2,1,5,31,9
	.half	.L74-.L10
	.byte	3,2,1,5,5,1,5,46,9
	.half	.L75-.L74
	.byte	3,124,1,5,25,9
	.half	.L2-.L75
	.byte	3,2,1,5,29,9
	.half	.L76-.L2
	.byte	3,2,1,5,46,3,124,1,5,1,7,9
	.half	.L77-.L76
	.byte	3,6,1,7,9
	.half	.L25-.L77
	.byte	0,1,1
.L71:
	.sdecl	'.debug_ranges',debug,cluster('Sl_SpinLockInit')
	.sect	'.debug_ranges'
.L24:
	.word	-1,.L10,0,.L25-.L10,0,0
.L46:
	.word	-1,.L10,0,.L45-.L10,-1,.L12,0,.L40-.L12,0,0
	.sdecl	'.debug_info',debug,cluster('Sl_GetSpinLock')
	.sect	'.debug_info'
.L26:
	.word	295
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'..\\mcal_src\\Sl_Ipc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L29,.L28
	.byte	2
	.word	.L17
	.byte	3
	.byte	'Sl_GetSpinLock',0,1,173,1,16
	.word	.L49
	.byte	1,1,1
	.word	.L14,.L50,.L13
	.byte	4
	.byte	'SpinLckID',0,1,173,1,49
	.word	.L51,.L52
	.byte	4
	.byte	'Timeout',0,1,173,1,66
	.word	.L47,.L53
	.byte	5
	.word	.L14,.L50
	.byte	6
	.byte	'RetVal',0,1,175,1,18
	.word	.L49,.L54
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Sl_GetSpinLock')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Sl_GetSpinLock')
	.sect	'.debug_line'
.L28:
	.word	.L79-.L78
.L78:
	.half	3
	.word	.L81-.L80
.L80:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Sl_Ipc.c',0,0,0,0,0
.L81:
	.byte	5,16,7,0,5,2
	.word	.L14
	.byte	3,172,1,1,5,10,9
	.half	.L60-.L14
	.byte	3,4,1,5,3,9
	.half	.L59-.L60
	.byte	3,2,1,5,34,7,9
	.half	.L82-.L59
	.byte	3,4,1,5,48,9
	.half	.L83-.L82
	.byte	1,5,60,9
	.half	.L84-.L83
	.byte	1,5,7,9
	.half	.L63-.L84
	.byte	3,1,1,5,48,7,9
	.half	.L85-.L63
	.byte	3,3,1,5,66,9
	.half	.L86-.L85
	.byte	1,5,30,9
	.half	.L87-.L86
	.byte	1,5,66,9
	.half	.L88-.L87
	.byte	1,5,41,9
	.half	.L61-.L88
	.byte	1,5,46,9
	.half	.L89-.L61
	.byte	1,5,10,9
	.half	.L90-.L89
	.byte	1,5,47,7,9
	.half	.L91-.L90
	.byte	3,4,1,5,46,3,127,1,5,1,9
	.half	.L3-.L91
	.byte	3,13,1,5,21,7,9
	.half	.L5-.L3
	.byte	3,120,1,5,1,3,8,1,7,9
	.half	.L30-.L5
	.byte	0,1,1
.L79:
	.sdecl	'.debug_ranges',debug,cluster('Sl_GetSpinLock')
	.sect	'.debug_ranges'
.L29:
	.word	-1,.L14,0,.L30-.L14,0,0
	.sdecl	'.debug_info',debug,cluster('Sl_ReleaseSpinLock')
	.sect	'.debug_info'
.L31:
	.word	253
	.half	3
	.word	.L32
	.byte	4,1
	.byte	'..\\mcal_src\\Sl_Ipc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L34,.L33
	.byte	2
	.word	.L17
	.byte	3
	.byte	'Sl_ReleaseSpinLock',0,1,228,1,6,1,1,1
	.word	.L16,.L55,.L15
	.byte	4
	.byte	'SpinLckID',0,1,228,1,37
	.word	.L56,.L57
	.byte	5
	.word	.L16,.L55
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Sl_ReleaseSpinLock')
	.sect	'.debug_abbrev'
.L32:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Sl_ReleaseSpinLock')
	.sect	'.debug_line'
.L33:
	.word	.L93-.L92
.L92:
	.half	3
	.word	.L95-.L94
.L94:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Sl_Ipc.c',0,0,0,0,0
.L95:
	.byte	5,3,7,0,5,2
	.word	.L16
	.byte	3,229,1,1,5,17,7,9
	.half	.L96-.L16
	.byte	3,6,1,5,35,9
	.half	.L97-.L96
	.byte	1,5,47,9
	.half	.L98-.L97
	.byte	1,5,35,3,127,1,5,27,9
	.half	.L64-.L98
	.byte	3,3,1,5,41,9
	.half	.L65-.L64
	.byte	1,5,1,9
	.half	.L8-.L65
	.byte	3,3,1,7,9
	.half	.L35-.L8
	.byte	0,1,1
.L93:
	.sdecl	'.debug_ranges',debug,cluster('Sl_ReleaseSpinLock')
	.sect	'.debug_ranges'
.L34:
	.word	-1,.L16,0,.L35-.L16,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L36:
	.word	210
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'..\\mcal_src\\Sl_Ipc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L39,.L38
	.byte	2
	.word	.L17
	.byte	3
	.byte	'.cocofun_1',0,1,135,1,6,1
	.word	.L12,.L40,.L11
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L38:
	.word	.L100-.L99
.L99:
	.half	3
	.word	.L102-.L101
.L101:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Sl_Ipc.c',0,0,0,0,0
.L102:
	.byte	5,5,7,0,5,2
	.word	.L12
	.byte	3,140,1,1,9
	.half	.L40-.L12
	.byte	0,1,1,5,34,0,5,2
	.word	.L12
	.byte	3,182,1,1,5,5,9
	.half	.L103-.L12
	.byte	3,86,1,7,9
	.half	.L40-.L103
	.byte	0,1,1,5,27,0,5,2
	.word	.L12
	.byte	3,237,1,1,5,5,9
	.half	.L103-.L12
	.byte	3,159,127,1,7,9
	.half	.L40-.L103
	.byte	0,1,1
.L100:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L39:
	.word	-1,.L12,0,.L40-.L12,0,0
	.sdecl	'.debug_info',debug,cluster('Sl_SpinLockArr')
	.sect	'.debug_info'
.L41:
	.word	202
	.half	3
	.word	.L42
	.byte	4,1
	.byte	'..\\mcal_src\\Sl_Ipc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L17
	.byte	3
	.byte	'Sl_SpinLockArr',0,3,80,26
	.word	.L58
	.byte	5,3
	.word	Sl_SpinLockArr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Sl_SpinLockArr')
	.sect	'.debug_abbrev'
.L42:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Sl_SpinLockRednArr')
	.sect	'.debug_info'
.L43:
	.word	206
	.half	3
	.word	.L44
	.byte	4,1
	.byte	'..\\mcal_src\\Sl_Ipc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L17
	.byte	3
	.byte	'Sl_SpinLockRednArr',0,3,81,26
	.word	.L58
	.byte	5,3
	.word	Sl_SpinLockRednArr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Sl_SpinLockRednArr')
	.sect	'.debug_abbrev'
.L44:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L11:
	.word	-1,.L12,0,.L40-.L12
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Sl_GetSpinLock')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L14,.L12-.L14,.L40-.L14
	.half	5
	.byte	144,33,157,32,0
	.word	.L59-.L14,.L50-.L14
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L13:
	.word	-1,.L14,0,.L50-.L14
	.half	2
	.byte	138,0
	.word	0,0
.L52:
	.word	-1,.L14,.L12-.L14,.L40-.L14
	.half	5
	.byte	144,39,157,32,32
	.word	.L12-.L14,.L40-.L14
	.half	5
	.byte	144,34,157,32,0
	.word	.L60-.L14,.L61-.L14
	.half	5
	.byte	144,39,157,32,32
	.word	0,.L62-.L14
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L53:
	.word	-1,.L14,.L12-.L14,.L40-.L14
	.half	5
	.byte	144,34,157,32,32
	.word	0,.L63-.L14
	.half	5
	.byte	144,34,157,32,32
	.word	.L62-.L14,.L63-.L14
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Sl_ReleaseSpinLock')
	.sect	'.debug_loc'
.L15:
	.word	-1,.L16,0,.L55-.L16
	.half	2
	.byte	138,0
	.word	0,0
.L57:
	.word	-1,.L16,0,.L64-.L16
	.half	5
	.byte	144,34,157,32,0
	.word	.L12-.L16,.L40-.L16
	.half	5
	.byte	144,34,157,32,0
	.word	.L65-.L16,.L55-.L16
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Sl_SpinLockInit')
	.sect	'.debug_loc'
.L48:
	.word	0,0
.L9:
	.word	-1,.L10,0,.L45-.L10
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L104:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Sl_SpinLockInit')
	.sect	'.debug_frame'
	.word	20
	.word	.L104,.L10,.L45-.L10
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Sl_GetSpinLock')
	.sect	'.debug_frame'
	.word	12
	.word	.L104,.L14,.L50-.L14
	.sdecl	'.debug_frame',debug,cluster('Sl_ReleaseSpinLock')
	.sect	'.debug_frame'
	.word	12
	.word	.L104,.L16,.L55-.L16
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L105:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L105,.L12,.L40-.L12
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Sl_Ipc.c	   242  
; ..\mcal_src\Sl_Ipc.c	   243  #if (IFX_SAFETLIB_USED == STD_ON)
; ..\mcal_src\Sl_Ipc.c	   244  #define IFX_APPL_STOP_SEC_CODE_ASIL_B
; ..\mcal_src\Sl_Ipc.c	   245  #include "Ifx_MemMap.h"
; ..\mcal_src\Sl_Ipc.c	   246  #else
; ..\mcal_src\Sl_Ipc.c	   247  #define APPL_STOP_SEC_CODE
; ..\mcal_src\Sl_Ipc.c	   248  #include "MemMap.h"
; ..\mcal_src\Sl_Ipc.c	   249  #endif

	; Module end
