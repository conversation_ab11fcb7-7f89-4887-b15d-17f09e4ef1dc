	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc13952a -c99 --dep-file=mcal_src\\.EcuM_Callout_Stubs.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\EcuM_Callout_Stubs.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\EcuM_Callout_Stubs.src ..\\mcal_src\\EcuM_Callout_Stubs.c"
	.compiler_name		"ctc"
	.name	"EcuM_Callout_Stubs"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('EcuM_AL_DriverInitOne')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	EcuM_AL_DriverInitOne

; ..\mcal_src\EcuM_Callout_Stubs.c	     1  /******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	     2  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_src\EcuM_Callout_Stubs.c	     4  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\EcuM_Callout_Stubs.c	     6  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\EcuM_Callout_Stubs.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\EcuM_Callout_Stubs.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\EcuM_Callout_Stubs.c	    10  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    11  *******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	    12  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    13  **  $FILENAME   : EcuM_Callout_Stubs.c $                                     **
; ..\mcal_src\EcuM_Callout_Stubs.c	    14  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    15  **  $CC VERSION : \main\52 $                                                 **
; ..\mcal_src\EcuM_Callout_Stubs.c	    16  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    17  **  $DATE       : 2015-10-14 $                                               **
; ..\mcal_src\EcuM_Callout_Stubs.c	    18  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\EcuM_Callout_Stubs.c	    20  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\EcuM_Callout_Stubs.c	    22  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    23  **  DESCRIPTION  : Contains a simple example of ECU State Manager Code       **
; ..\mcal_src\EcuM_Callout_Stubs.c	    24  **                 This file is for Evaluation Purpose Only                  **
; ..\mcal_src\EcuM_Callout_Stubs.c	    25  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    26  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\EcuM_Callout_Stubs.c	    27  **                                                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	    28  ******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	    29  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	    30  **                      Includes                                              **
; ..\mcal_src\EcuM_Callout_Stubs.c	    31  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	    32  #include "EcuM.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    33  #include "EcuM_Cbk.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    34  /* Include Interrupt header file */
; ..\mcal_src\EcuM_Callout_Stubs.c	    35  #include "Irq.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    36  #include "Mcu.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    37  /* Inclusion of controller sfr file */
; ..\mcal_src\EcuM_Callout_Stubs.c	    38  #include "IfxSmu_reg.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    39  #include "IfxScu_reg.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    40  
; ..\mcal_src\EcuM_Callout_Stubs.c	    41  #define ECUM_USES_SPI
; ..\mcal_src\EcuM_Callout_Stubs.c	    42  #define ECUM_USES_GTM
; ..\mcal_src\EcuM_Callout_Stubs.c	    43  #define ECUM_USES_DMA
; ..\mcal_src\EcuM_Callout_Stubs.c	    44  
; ..\mcal_src\EcuM_Callout_Stubs.c	    45  
; ..\mcal_src\EcuM_Callout_Stubs.c	    46  #ifdef ECUM_USES_GPT
; ..\mcal_src\EcuM_Callout_Stubs.c	    47  #include "Gpt.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    48  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    49  #ifdef ECUM_USES_PORT
; ..\mcal_src\EcuM_Callout_Stubs.c	    50  #include "Port.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    51  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    52  #ifdef ECUM_USES_DIO
; ..\mcal_src\EcuM_Callout_Stubs.c	    53  #include "Dio.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    54  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    55  #ifdef ECUM_USES_GTM
; ..\mcal_src\EcuM_Callout_Stubs.c	    56  #include "Gtm.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    57  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    58  #ifdef ECUM_USES_DMA
; ..\mcal_src\EcuM_Callout_Stubs.c	    59  #include "Dma.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    60  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    61  #ifdef ECUM_USES_SPI
; ..\mcal_src\EcuM_Callout_Stubs.c	    62  #include "Spi.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    63  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    64  #ifdef ECUM_USES_ADC
; ..\mcal_src\EcuM_Callout_Stubs.c	    65  #include "Adc.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    66  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    67  #ifdef ECUM_USES_FLS
; ..\mcal_src\EcuM_Callout_Stubs.c	    68  #include "Fls_17_Pmu.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    69  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    70  #ifdef ECUM_USES_FEE
; ..\mcal_src\EcuM_Callout_Stubs.c	    71  #include "Fee.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    72  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    73  #ifdef ECUM_USES_CAN
; ..\mcal_src\EcuM_Callout_Stubs.c	    74  #include "Can_17_MCanP.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    75  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    76  #ifdef ECUM_USES_PWM
; ..\mcal_src\EcuM_Callout_Stubs.c	    77  #include "Pwm_17_Gtm.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    78  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    79  #ifdef ECUM_USES_FADC
; ..\mcal_src\EcuM_Callout_Stubs.c	    80  #include "Fadc.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    81  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    82  #ifdef ECUM_USES_ICU
; ..\mcal_src\EcuM_Callout_Stubs.c	    83  #include "Icu_17_GtmCcu6.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    84  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    85  #ifdef ECUM_USES_WDG
; ..\mcal_src\EcuM_Callout_Stubs.c	    86  #include "Wdg_17_Scu.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    87  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    88  #ifdef ECUM_USES_MLI
; ..\mcal_src\EcuM_Callout_Stubs.c	    89  #include "Mli.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    90  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    91  #ifdef ECUM_USES_SCI
; ..\mcal_src\EcuM_Callout_Stubs.c	    92  #include "Sci.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    93  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    94  #ifdef ECUM_USES_MCHK
; ..\mcal_src\EcuM_Callout_Stubs.c	    95  #include "Mchk.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    96  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	    97  #ifdef ECUM_USES_MSC
; ..\mcal_src\EcuM_Callout_Stubs.c	    98  #include "Msc.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	    99  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   100  
; ..\mcal_src\EcuM_Callout_Stubs.c	   101  #ifdef ECUM_USES_LIN
; ..\mcal_src\EcuM_Callout_Stubs.c	   102  #include "Lin_17_AscLin.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   103  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   104  
; ..\mcal_src\EcuM_Callout_Stubs.c	   105  #ifdef ECUM_USES_UART
; ..\mcal_src\EcuM_Callout_Stubs.c	   106  #include "Uart.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   107  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   108  
; ..\mcal_src\EcuM_Callout_Stubs.c	   109  #ifdef ECUM_USES_ETH
; ..\mcal_src\EcuM_Callout_Stubs.c	   110  #include "Eth_17_EthMac.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   111  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   112  
; ..\mcal_src\EcuM_Callout_Stubs.c	   113  #ifdef ECUM_USES_RAMTST
; ..\mcal_src\EcuM_Callout_Stubs.c	   114  #include "RamTst_Api.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   115  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   116  
; ..\mcal_src\EcuM_Callout_Stubs.c	   117  #ifdef ECUM_USES_FR_17_ERAY
; ..\mcal_src\EcuM_Callout_Stubs.c	   118  #include "Fr_17_Eray.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   119  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   120  
; ..\mcal_src\EcuM_Callout_Stubs.c	   121  #ifdef ECUM_USES_FLSLOADER
; ..\mcal_src\EcuM_Callout_Stubs.c	   122  #include "FlsLoader.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   123  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   124  
; ..\mcal_src\EcuM_Callout_Stubs.c	   125  #ifdef ECUM_USES_SENT
; ..\mcal_src\EcuM_Callout_Stubs.c	   126  #include "Sent.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   127  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   128  
; ..\mcal_src\EcuM_Callout_Stubs.c	   129  #ifdef ECUM_USES_IOM
; ..\mcal_src\EcuM_Callout_Stubs.c	   130  #include "Iom.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   131  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   132  
; ..\mcal_src\EcuM_Callout_Stubs.c	   133  #ifdef ECUM_USES_CANTRCV_17_6250GV33
; ..\mcal_src\EcuM_Callout_Stubs.c	   134  #include "CanTrcv_17_6250GV33.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   135  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   136  
; ..\mcal_src\EcuM_Callout_Stubs.c	   137  #ifdef ECUM_USES_CANTRCV_17_6251G
; ..\mcal_src\EcuM_Callout_Stubs.c	   138  #include "CanTrcv_17_6251G.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   139  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   140  
; ..\mcal_src\EcuM_Callout_Stubs.c	   141  #ifdef ECUM_USES_HSSL
; ..\mcal_src\EcuM_Callout_Stubs.c	   142  #include "Hssl.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   143  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   144  
; ..\mcal_src\EcuM_Callout_Stubs.c	   145  #ifdef ECUM_USES_DSADC
; ..\mcal_src\EcuM_Callout_Stubs.c	   146  #include "Dsadc.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   147  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   148  
; ..\mcal_src\EcuM_Callout_Stubs.c	   149  #ifdef ECUM_USES_SMU
; ..\mcal_src\EcuM_Callout_Stubs.c	   150  #include "SmuInt.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   151  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   152  
; ..\mcal_src\EcuM_Callout_Stubs.c	   153  #ifdef ECUM_USES_I2C
; ..\mcal_src\EcuM_Callout_Stubs.c	   154  #include "I2c.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   155  #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   156  
; ..\mcal_src\EcuM_Callout_Stubs.c	   157  /* Mcal Safety ENDINIT Timeout value */
; ..\mcal_src\EcuM_Callout_Stubs.c	   158  #define ECUM_ENDINIT_TIMEOUT   (150000U)
; ..\mcal_src\EcuM_Callout_Stubs.c	   159  
; ..\mcal_src\EcuM_Callout_Stubs.c	   160  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   161  **                      Imported Compiler Switch Check                        **
; ..\mcal_src\EcuM_Callout_Stubs.c	   162  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   163  
; ..\mcal_src\EcuM_Callout_Stubs.c	   164  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   165  **                      Private Macro Definitions                             **
; ..\mcal_src\EcuM_Callout_Stubs.c	   166  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   167  
; ..\mcal_src\EcuM_Callout_Stubs.c	   168  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   169  **                      Private Type Definitions                              **
; ..\mcal_src\EcuM_Callout_Stubs.c	   170  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   171  
; ..\mcal_src\EcuM_Callout_Stubs.c	   172  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   173  **                      Private Function Declarations                         **
; ..\mcal_src\EcuM_Callout_Stubs.c	   174  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   175  
; ..\mcal_src\EcuM_Callout_Stubs.c	   176  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   177  **                      Global Constant Definitions                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	   178  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   179  
; ..\mcal_src\EcuM_Callout_Stubs.c	   180  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   181  **                      Global Variable Definitions                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	   182  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   183  
; ..\mcal_src\EcuM_Callout_Stubs.c	   184  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   185  **                      Private Constant Definitions                          **
; ..\mcal_src\EcuM_Callout_Stubs.c	   186  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   187  
; ..\mcal_src\EcuM_Callout_Stubs.c	   188  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   189  **                      Private Variable Definitions                          **
; ..\mcal_src\EcuM_Callout_Stubs.c	   190  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   191  
; ..\mcal_src\EcuM_Callout_Stubs.c	   192  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   193  **                      Global Function Definitions                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	   194  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   195  #define ECUM_START_SEC_CODE
; ..\mcal_src\EcuM_Callout_Stubs.c	   196  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives 
; ..\mcal_src\EcuM_Callout_Stubs.c	   197    is allowed only for MemMap.h*/
; ..\mcal_src\EcuM_Callout_Stubs.c	   198  #include "MemMap.h"
; ..\mcal_src\EcuM_Callout_Stubs.c	   199  
; ..\mcal_src\EcuM_Callout_Stubs.c	   200  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   201  ** Syntax           : void EcuM_AL_DriverInitOne(const EcuM_ConfigType        **
; ..\mcal_src\EcuM_Callout_Stubs.c	   202                                                   *configptr)                  **
; ..\mcal_src\EcuM_Callout_Stubs.c	   203  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   204  ** Description      : initializes clock and interrupts                        **
; ..\mcal_src\EcuM_Callout_Stubs.c	   205  **                                                                            **                                                                                                                                 **
; ..\mcal_src\EcuM_Callout_Stubs.c	   206  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   207  void EcuM_AL_DriverInitOne(const EcuM_ConfigType *configptr) 
; Function EcuM_AL_DriverInitOne
.L4:
EcuM_AL_DriverInitOne:	.type	func
	mov.aa	a15,a4
.L43:

; ..\mcal_src\EcuM_Callout_Stubs.c	   208  {
; ..\mcal_src\EcuM_Callout_Stubs.c	   209    /* Initialize MCU Clock*/
; ..\mcal_src\EcuM_Callout_Stubs.c	   210    /* parameter 0 is chosen here by default, the first clock configuration */
; ..\mcal_src\EcuM_Callout_Stubs.c	   211    Mcu_InitClock(0);    
	mov	d4,#0
	call	Mcu_InitClock

; ..\mcal_src\EcuM_Callout_Stubs.c	   212    /* wait till PLL lock */
; ..\mcal_src\EcuM_Callout_Stubs.c	   213    while(Mcu_GetPllStatus() == 0);
.L2:
	call	Mcu_GetPllStatus
.L52:
	jeq	d2,#0,.L2
.L53:

; ..\mcal_src\EcuM_Callout_Stubs.c	   214    /* distribute the clock */
; ..\mcal_src\EcuM_Callout_Stubs.c	   215    Mcu_DistributePllClock();  
	call	Mcu_DistributePllClock
.L54:

; ..\mcal_src\EcuM_Callout_Stubs.c	   216    
; ..\mcal_src\EcuM_Callout_Stubs.c	   217    /* SMU is configured for Reset on WDG timeout */
; ..\mcal_src\EcuM_Callout_Stubs.c	   218  
; ..\mcal_src\EcuM_Callout_Stubs.c	   219    Mcal_ResetENDINIT();
	call	Mcal_ResetENDINIT
.L55:

; ..\mcal_src\EcuM_Callout_Stubs.c	   220    SCU_TRAPDIS.B.SMUT = 0;
	movh.a	a2,#61443
	ld.bu	d15,[a2]@los(0xf0036130)
.L56:
	insert	d15,d15,#0,#3,#1
	st.b	[a2]@los(0xf0036130),d15
.L57:

; ..\mcal_src\EcuM_Callout_Stubs.c	   221    Mcal_SetENDINIT();
	call	Mcal_SetENDINIT
.L58:

; ..\mcal_src\EcuM_Callout_Stubs.c	   222    #if (IFX_SAFETLIB_USED == STD_OFF)
; ..\mcal_src\EcuM_Callout_Stubs.c	   223    Mcal_ResetSafetyENDINIT_Timed(ECUM_ENDINIT_TIMEOUT);
	mov	d4,#9375
	sh	d4,#4
	call	Mcal_ResetSafetyENDINIT_Timed
.L59:

; ..\mcal_src\EcuM_Callout_Stubs.c	   224    SMU_KEYS.U = 0xbc; 
	mov	d15,#188
	movh.a	a2,#61443
	st.w	[a2]@los(0xf0036834),d15
.L60:

; ..\mcal_src\EcuM_Callout_Stubs.c	   225    SMU_CMD.U = 0x00;
	mov	d15,#0
	st.w	[a2]@los(0xf0036820),d15
.L61:

; ..\mcal_src\EcuM_Callout_Stubs.c	   226    SMU_AGCF3_0.U = 0x00000000;
	st.w	[a2]@los(0xf0036924),d15
.L62:

; ..\mcal_src\EcuM_Callout_Stubs.c	   227    SMU_AGCF3_1.U = 0x001E0000;
	movh	d15,#30
	st.w	[a2]@los(0xf0036928),d15
.L63:

; ..\mcal_src\EcuM_Callout_Stubs.c	   228    SMU_AGCF3_2.U = 0x001E0000;
	st.w	[a2]@los(0xf003692c),d15
.L64:

; ..\mcal_src\EcuM_Callout_Stubs.c	   229    Mcal_SetSafetyENDINIT_Timed();
	call	Mcal_SetSafetyENDINIT_Timed
.L65:

; ..\mcal_src\EcuM_Callout_Stubs.c	   230    #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   231  
; ..\mcal_src\EcuM_Callout_Stubs.c	   232    Mcal_EnableAllInterrupts();       /* Enable Global Interrupt Flag. */
	enable
.L66:

; ..\mcal_src\EcuM_Callout_Stubs.c	   233    /* Initialzie Interrupt Priority initialization*/
; ..\mcal_src\EcuM_Callout_Stubs.c	   234    #ifdef ECUM_USES_ADC
; ..\mcal_src\EcuM_Callout_Stubs.c	   235    IrqAdc_Init();    /* Initialise the interrupt priority for ADC */
	call	IrqAdc_Init
.L67:

; ..\mcal_src\EcuM_Callout_Stubs.c	   236    #endif  
; ..\mcal_src\EcuM_Callout_Stubs.c	   237    #ifdef ECUM_USES_SPI 
; ..\mcal_src\EcuM_Callout_Stubs.c	   238    IrqSpi_Init();    /* Assign priorities for SPI interrupts */
	call	IrqSpi_Init
.L68:

; ..\mcal_src\EcuM_Callout_Stubs.c	   239    #endif 
; ..\mcal_src\EcuM_Callout_Stubs.c	   240    IrqDma_Init();    /* Assign priorities for DMA interrupts */
	call	IrqDma_Init
.L69:

; ..\mcal_src\EcuM_Callout_Stubs.c	   241    #ifdef ECUM_USES_MLI
; ..\mcal_src\EcuM_Callout_Stubs.c	   242    IrqMli_Init();    /* Assign priorities for MLI interrupts */
; ..\mcal_src\EcuM_Callout_Stubs.c	   243    #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   244    #ifdef ECUM_USES_FADC
; ..\mcal_src\EcuM_Callout_Stubs.c	   245    IrqFadc_Init();   /* Assign priorities for FADC interrupts */
; ..\mcal_src\EcuM_Callout_Stubs.c	   246    #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   247    #ifdef ECUM_USES_CAN
; ..\mcal_src\EcuM_Callout_Stubs.c	   248    IrqCan_Init();    /* Assign priorities for CAN interrupts */
	call	IrqCan_Init
.L70:

; ..\mcal_src\EcuM_Callout_Stubs.c	   249    #endif  
; ..\mcal_src\EcuM_Callout_Stubs.c	   250    IrqStm_Init();    /* Assign priorities for STM interrupts */
	call	IrqStm_Init
.L71:

; ..\mcal_src\EcuM_Callout_Stubs.c	   251    #ifdef ECUM_USES_FLS
; ..\mcal_src\EcuM_Callout_Stubs.c	   252    IrqPmu_Init();
; ..\mcal_src\EcuM_Callout_Stubs.c	   253    #endif  
; ..\mcal_src\EcuM_Callout_Stubs.c	   254    #if( defined( ECUM_USES_LIN ) || defined( ECUM_USES_UART) )
; ..\mcal_src\EcuM_Callout_Stubs.c	   255    IrqAscLin_Init();    /* Assign priorities for SCI interrupts */
	call	IrqAscLin_Init
.L72:

; ..\mcal_src\EcuM_Callout_Stubs.c	   256    #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   257    #ifdef ECUM_USES_MSC
; ..\mcal_src\EcuM_Callout_Stubs.c	   258    IrqMsc_Init();    /* Assign priorities for MSC interrupts */
; ..\mcal_src\EcuM_Callout_Stubs.c	   259    #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   260    #ifdef ECUM_USES_ERU
; ..\mcal_src\EcuM_Callout_Stubs.c	   261    IrqScu_Init();     /* Assign priorities for ERU interrupts */
; ..\mcal_src\EcuM_Callout_Stubs.c	   262    #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   263    #ifdef ECUM_USES_GTM
; ..\mcal_src\EcuM_Callout_Stubs.c	   264    IrqGtm_Init();     /* Assign priorities for GTM interrupts */
	call	IrqGtm_Init
.L73:

; ..\mcal_src\EcuM_Callout_Stubs.c	   265    #endif  
; ..\mcal_src\EcuM_Callout_Stubs.c	   266    #ifdef ECUM_USES_ETH
; ..\mcal_src\EcuM_Callout_Stubs.c	   267    IrqEthernet_Init();     /* Assign priorities for GTM interrupts */
; ..\mcal_src\EcuM_Callout_Stubs.c	   268    #endif 
; ..\mcal_src\EcuM_Callout_Stubs.c	   269    #ifdef ECUM_USES_CCU
; ..\mcal_src\EcuM_Callout_Stubs.c	   270    IrqCcu6_Init();
; ..\mcal_src\EcuM_Callout_Stubs.c	   271    #endif 
; ..\mcal_src\EcuM_Callout_Stubs.c	   272    #ifdef ECUM_USES_SENT
; ..\mcal_src\EcuM_Callout_Stubs.c	   273    IrqSent_Init();
; ..\mcal_src\EcuM_Callout_Stubs.c	   274    #endif   
; ..\mcal_src\EcuM_Callout_Stubs.c	   275    #ifdef ECUM_USES_I2C
; ..\mcal_src\EcuM_Callout_Stubs.c	   276    IrqI2c_Init();
; ..\mcal_src\EcuM_Callout_Stubs.c	   277    #endif
; ..\mcal_src\EcuM_Callout_Stubs.c	   278    /* This is a macro defined in the EcuM_Cfg.h. The body of this call is 
; ..\mcal_src\EcuM_Callout_Stubs.c	   279       generated from the configuration tool*/
; ..\mcal_src\EcuM_Callout_Stubs.c	   280    EcuM_DriverInitListOneConfig(configptr);  
	ld.a	a4,[a15]12
	call	Dio_Init
	ld.a	a4,[a15]16
	call	Port_Init
	ld.a	a4,[a15]20
	call	Adc_Init
	ld.a	a4,[a15]24
	call	Uart_Init
	ld.a	a4,[a15]28
	call	Spi_Init
	ld.a	a4,[a15]32
	j	Dma_Init
.L35:
	
__EcuM_AL_DriverInitOne_function_end:
	.size	EcuM_AL_DriverInitOne,__EcuM_AL_DriverInitOne_function_end-EcuM_AL_DriverInitOne
.L19:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('EcuM_AL_DriverInitTwo')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	EcuM_AL_DriverInitTwo

; ..\mcal_src\EcuM_Callout_Stubs.c	   281  }
; ..\mcal_src\EcuM_Callout_Stubs.c	   282  
; ..\mcal_src\EcuM_Callout_Stubs.c	   283  
; ..\mcal_src\EcuM_Callout_Stubs.c	   284  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   285  ** Syntax           : void EcuM_AL_DriverInitTwo(const EcuM_ConfigType        **
; ..\mcal_src\EcuM_Callout_Stubs.c	   286  **                                               *configptr)                  **
; ..\mcal_src\EcuM_Callout_Stubs.c	   287  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   288  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   289  ** Description      : list two driver intialization                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	   290  **                                                                            **                                                                                                                                 **
; ..\mcal_src\EcuM_Callout_Stubs.c	   291  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   292  void EcuM_AL_DriverInitTwo(const EcuM_ConfigType *configptr) 
; Function EcuM_AL_DriverInitTwo
.L6:
EcuM_AL_DriverInitTwo:	.type	func

; ..\mcal_src\EcuM_Callout_Stubs.c	   293  {
; ..\mcal_src\EcuM_Callout_Stubs.c	   294    /* This is a macro defined in the EcuM_Cfg.h. The body of this call is 
; ..\mcal_src\EcuM_Callout_Stubs.c	   295       generated from the configuration tool*/
; ..\mcal_src\EcuM_Callout_Stubs.c	   296    EcuM_DriverInitListTwoConfig(configptr);
; ..\mcal_src\EcuM_Callout_Stubs.c	   297  	/* unused param warning removal for GNU */
; ..\mcal_src\EcuM_Callout_Stubs.c	   298  	UNUSED_PARAMETER(configptr)
; ..\mcal_src\EcuM_Callout_Stubs.c	   299  }
	ret
.L38:
	
__EcuM_AL_DriverInitTwo_function_end:
	.size	EcuM_AL_DriverInitTwo,__EcuM_AL_DriverInitTwo_function_end-EcuM_AL_DriverInitTwo
.L24:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('EcuM_AL_DriverInitThree')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	EcuM_AL_DriverInitThree

; ..\mcal_src\EcuM_Callout_Stubs.c	   300  
; ..\mcal_src\EcuM_Callout_Stubs.c	   301  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   302  ** Syntax           : void EcuM_AL_DriverInitThree(const EcuM_ConfigType      **
; ..\mcal_src\EcuM_Callout_Stubs.c	   303  **                                                 *configptr)                **
; ..\mcal_src\EcuM_Callout_Stubs.c	   304  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   305  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   306  ** Description      : List three intialization                                **
; ..\mcal_src\EcuM_Callout_Stubs.c	   307  **                                                                            **                                                                                                                                 **
; ..\mcal_src\EcuM_Callout_Stubs.c	   308  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   309  void EcuM_AL_DriverInitThree(const EcuM_ConfigType *configptr) 
; Function EcuM_AL_DriverInitThree
.L8:
EcuM_AL_DriverInitThree:	.type	func

; ..\mcal_src\EcuM_Callout_Stubs.c	   310  {
; ..\mcal_src\EcuM_Callout_Stubs.c	   311    /* This is a macro defined in the EcuM_Cfg.h. The body of this call is 
; ..\mcal_src\EcuM_Callout_Stubs.c	   312       generated from the configuration tool*/
; ..\mcal_src\EcuM_Callout_Stubs.c	   313    EcuM_DriverInitListThreeConfig(configptr);
; ..\mcal_src\EcuM_Callout_Stubs.c	   314  	/* unused param warning removal for GNU */
; ..\mcal_src\EcuM_Callout_Stubs.c	   315  	UNUSED_PARAMETER(configptr)
; ..\mcal_src\EcuM_Callout_Stubs.c	   316  }
	ret
.L40:
	
__EcuM_AL_DriverInitThree_function_end:
	.size	EcuM_AL_DriverInitThree,__EcuM_AL_DriverInitThree_function_end-EcuM_AL_DriverInitThree
.L29:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('EcuM_AL_DriverInitZero')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	EcuM_AL_DriverInitZero

; ..\mcal_src\EcuM_Callout_Stubs.c	   317  
; ..\mcal_src\EcuM_Callout_Stubs.c	   318  /*******************************************************************************
; ..\mcal_src\EcuM_Callout_Stubs.c	   319  ** Syntax           : void EcuM_AL_DriverInitZero()                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	   320  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   321  ** Service ID       : 0                                                       **
; ..\mcal_src\EcuM_Callout_Stubs.c	   322  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   323  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\EcuM_Callout_Stubs.c	   324  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   325  ** Reentrancy       : Non-reentrant                                           **
; ..\mcal_src\EcuM_Callout_Stubs.c	   326  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   327  ** Parameters(in)   : None                                                    **
; ..\mcal_src\EcuM_Callout_Stubs.c	   328  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   329  ** Parameters (out) : None                                                    **
; ..\mcal_src\EcuM_Callout_Stubs.c	   330  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   331  ** Return value     : None                                                    **
; ..\mcal_src\EcuM_Callout_Stubs.c	   332  **                                                                            **
; ..\mcal_src\EcuM_Callout_Stubs.c	   333  ** Description      : List zero, modules like DET can be initialized          **
; ..\mcal_src\EcuM_Callout_Stubs.c	   334  **                    post build modules cannot be loaded from here           **
; ..\mcal_src\EcuM_Callout_Stubs.c	   335  **                                                                            **                                                                                                                                 **
; ..\mcal_src\EcuM_Callout_Stubs.c	   336  *******************************************************************************/
; ..\mcal_src\EcuM_Callout_Stubs.c	   337  void EcuM_AL_DriverInitZero() 
; Function EcuM_AL_DriverInitZero
.L10:
EcuM_AL_DriverInitZero:	.type	func

; ..\mcal_src\EcuM_Callout_Stubs.c	   338  {
; ..\mcal_src\EcuM_Callout_Stubs.c	   339    /* This is a macro defined in the EcuM_Cfg.h. The body of this call is
; ..\mcal_src\EcuM_Callout_Stubs.c	   340       generated from the configuration tool */
; ..\mcal_src\EcuM_Callout_Stubs.c	   341    EcuM_DriverInitListZeroConfig();
; ..\mcal_src\EcuM_Callout_Stubs.c	   342  
; ..\mcal_src\EcuM_Callout_Stubs.c	   343  }
	ret
.L42:
	
__EcuM_AL_DriverInitZero_function_end:
	.size	EcuM_AL_DriverInitZero,__EcuM_AL_DriverInitZero_function_end-EcuM_AL_DriverInitZero
.L34:
	; End of function
	
	.calls	'EcuM_AL_DriverInitOne','Mcu_InitClock'
	.calls	'EcuM_AL_DriverInitOne','Mcu_GetPllStatus'
	.calls	'EcuM_AL_DriverInitOne','Mcu_DistributePllClock'
	.calls	'EcuM_AL_DriverInitOne','Mcal_ResetENDINIT'
	.calls	'EcuM_AL_DriverInitOne','Mcal_SetENDINIT'
	.calls	'EcuM_AL_DriverInitOne','Mcal_ResetSafetyENDINIT_Timed'
	.calls	'EcuM_AL_DriverInitOne','Mcal_SetSafetyENDINIT_Timed'
	.calls	'EcuM_AL_DriverInitOne','IrqAdc_Init'
	.calls	'EcuM_AL_DriverInitOne','IrqSpi_Init'
	.calls	'EcuM_AL_DriverInitOne','IrqDma_Init'
	.calls	'EcuM_AL_DriverInitOne','IrqCan_Init'
	.calls	'EcuM_AL_DriverInitOne','IrqStm_Init'
	.calls	'EcuM_AL_DriverInitOne','IrqAscLin_Init'
	.calls	'EcuM_AL_DriverInitOne','IrqGtm_Init'
	.calls	'EcuM_AL_DriverInitOne','Dio_Init'
	.calls	'EcuM_AL_DriverInitOne','Port_Init'
	.calls	'EcuM_AL_DriverInitOne','Adc_Init'
	.calls	'EcuM_AL_DriverInitOne','Uart_Init'
	.calls	'EcuM_AL_DriverInitOne','Spi_Init'
	.calls	'EcuM_AL_DriverInitOne','Dma_Init'
	.calls	'EcuM_AL_DriverInitOne','',0
	.calls	'EcuM_AL_DriverInitTwo','',0
	.calls	'EcuM_AL_DriverInitThree','',0
	.extern	Mcal_ResetENDINIT
	.extern	Mcal_SetENDINIT
	.extern	Mcal_ResetSafetyENDINIT_Timed
	.extern	Mcal_SetSafetyENDINIT_Timed
	.extern	IrqAscLin_Init
	.extern	IrqCan_Init
	.extern	IrqGtm_Init
	.extern	IrqSpi_Init
	.extern	IrqAdc_Init
	.extern	IrqDma_Init
	.extern	IrqStm_Init
	.extern	Mcu_InitClock
	.extern	Mcu_DistributePllClock
	.extern	Mcu_GetPllStatus
	.extern	Port_Init
	.extern	Dio_Init
	.extern	Dma_Init
	.extern	Spi_Init
	.extern	Adc_Init
	.extern	Uart_Init
	.calls	'EcuM_AL_DriverInitZero','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L12:
	.word	120302
	.half	3
	.word	.L13
	.byte	4
.L11:
	.byte	1
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L14
	.byte	2
	.byte	'__enable',0,1,1,1,1,3
	.byte	'EcuM_ConfigType_Tag',0,1,140,1,16,40,4
	.byte	'unsigned short int',0,2,7,5
	.word	227
	.byte	6
	.byte	'ConfigurationIdentifier',0,2
	.word	249
	.byte	2,35,0,4
	.byte	'unsigned long int',0,4,7,5
	.word	287
	.byte	6
	.byte	'PreCompileIdentifier',0,4
	.word	308
	.byte	2,35,2,3
	.byte	'Mcu_ConfigType',0,2,189,4,16,28,3
	.byte	'Mcu_ClockCfgType',0,2,236,3,16,80,4
	.byte	'unsigned char',0,1,8,7,8
	.word	387
	.byte	8,7,0,6
	.byte	'K2div',0,8
	.word	404
	.byte	2,35,0,7,32
	.word	287
	.byte	8,7,0,6
	.byte	'K2RampToPllDelayTicks',0,32
	.word	428
	.byte	2,35,8,9,2,247,3,3,4,10
	.byte	'K1div',0,1
	.word	387
	.byte	7,1,2,35,0,10
	.byte	'K3div',0,2
	.word	227
	.byte	7,2,2,35,0,4
	.byte	'unsigned int',0,4,7,10
	.byte	'Ndiv',0,4
	.word	508
	.byte	7,11,2,35,2,10
	.byte	'Pdiv',0,2
	.word	227
	.byte	4,7,2,35,2,10
	.byte	'K2steps',0,1
	.word	387
	.byte	4,3,2,35,3,10
	.byte	'PllMode',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'Reserved',0,1
	.word	387
	.byte	2,0,2,35,3,0,6
	.byte	'Mcu_ClockDivValues',0,4
	.word	468
	.byte	2,35,40,9,2,132,4,3,4,10
	.byte	'McuErayNDivider',0,1
	.word	387
	.byte	5,3,2,35,0,10
	.byte	'McuErayK2Divider',0,2
	.word	227
	.byte	7,4,2,35,0,10
	.byte	'McuErayK3Divider',0,4
	.word	508
	.byte	7,13,2,35,2,10
	.byte	'McuErayPDivider',0,1
	.word	387
	.byte	4,1,2,35,2,10
	.byte	'Reserved',0,2
	.word	227
	.byte	9,0,2,35,2,0,6
	.byte	'MCU_ErayPllDivValues',0,4
	.word	643
	.byte	2,35,44,6
	.byte	'Ccucon0',0,4
	.word	287
	.byte	2,35,48,6
	.byte	'Ccucon1',0,4
	.word	287
	.byte	2,35,52,6
	.byte	'Ccucon2',0,4
	.word	287
	.byte	2,35,56,6
	.byte	'Ccucon5',0,4
	.word	287
	.byte	2,35,60,6
	.byte	'Ccucon6',0,4
	.word	287
	.byte	2,35,64,6
	.byte	'Ccucon7',0,4
	.word	287
	.byte	2,35,68,6
	.byte	'Ccucon8',0,4
	.word	287
	.byte	2,35,72,6
	.byte	'K2RampToPllDelayConf',0,1
	.word	387
	.byte	2,35,76,0,5
	.word	364
	.byte	11
	.word	960
	.byte	6
	.byte	'ClockCfgPtr',0,4
	.word	965
	.byte	2,35,0,3
	.byte	'Gtm_ConfigType',0,3,192,7,16,12,9,3,230,6,9,64,6
	.byte	'GtmClockEnable',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'GtmCmuClkCnt',0,32
	.word	428
	.byte	2,35,4,6
	.byte	'GtmFxdClkControl',0,4
	.word	287
	.byte	2,35,36,9,3,223,6,9,8,6
	.byte	'CmuEclkNum',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'CmuEclkDen',0,4
	.word	287
	.byte	2,35,4,0,7,24
	.word	1090
	.byte	8,2,0,6
	.byte	'GtmEclk',0,24
	.word	1137
	.byte	2,35,40,0,5
	.word	1012
	.byte	11
	.word	1164
	.byte	6
	.byte	'GtmClockSettingPtr',0,4
	.word	1169
	.byte	2,35,0,9,3,249,5,9,36,7,4
	.word	287
	.byte	8,0,0,6
	.byte	'TimInSel',0,4
	.word	1208
	.byte	2,35,0,6
	.byte	'ToutSel',0,32
	.word	428
	.byte	2,35,4,0,5
	.word	1202
	.byte	11
	.word	1253
	.byte	6
	.byte	'GtmPortConfigPtr',0,4
	.word	1258
	.byte	2,35,4,9,3,129,7,9,72,6
	.byte	'GtmModuleSleepEnable',0,1
	.word	387
	.byte	2,35,0,6
	.byte	'GtmGclkNum',0,4
	.word	287
	.byte	2,35,2,6
	.byte	'GtmGclkDen',0,4
	.word	287
	.byte	2,35,6,6
	.byte	'GtmAccessEnable0',0,4
	.word	287
	.byte	2,35,10,6
	.byte	'GtmAccessEnable1',0,4
	.word	287
	.byte	2,35,14,7,2
	.word	227
	.byte	8,0,0,6
	.byte	'GtmTimModuleUsage',0,2
	.word	1417
	.byte	2,35,18,7,1
	.word	387
	.byte	8,0,0,6
	.byte	'GtmTimUsage',0,1
	.word	1453
	.byte	2,35,20,9,3,138,6,11,24,6
	.byte	'TimUsage',0,1
	.word	387
	.byte	2,35,0,6
	.byte	'TimIrqEn',0,1
	.word	387
	.byte	2,35,1,6
	.byte	'TimErrIrqEn',0,1
	.word	387
	.byte	2,35,2,6
	.byte	'TimExtCapSrc',0,1
	.word	387
	.byte	2,35,3,6
	.byte	'TimCtrlValue',0,4
	.word	287
	.byte	2,35,4,9,3,129,6,9,8,6
	.byte	'TimRisingEdgeFilter',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'TimFallingEdgeFilter',0,4
	.word	287
	.byte	2,35,4,0,5
	.word	1590
	.byte	11
	.word	1656
	.byte	6
	.byte	'GtmTimFltPtr',0,4
	.word	1661
	.byte	2,35,8,6
	.byte	'TimCntsValue',0,4
	.word	287
	.byte	2,35,12,6
	.byte	'TimTduValue',0,4
	.word	287
	.byte	2,35,16,6
	.byte	'TimInSrcSel',0,4
	.word	287
	.byte	2,35,20,0,5
	.word	1483
	.byte	11
	.word	1753
	.byte	6
	.byte	'GtmTimConfigPtr',0,4
	.word	1758
	.byte	2,35,24,6
	.byte	'GtmTomTgcUsage',0,1
	.word	1453
	.byte	2,35,28,9,3,189,6,9,12,6
	.byte	'GtmTomIntTrig',0,2
	.word	227
	.byte	2,35,0,6
	.byte	'GtmTomActTb',0,4
	.word	287
	.byte	2,35,2,9,3,177,6,9,16,6
	.byte	'GtmTomUpdateEn',0,2
	.word	227
	.byte	2,35,0,6
	.byte	'GtmTomEndisCtrl',0,2
	.word	227
	.byte	2,35,2,6
	.byte	'GtmTomEndisStat',0,2
	.word	227
	.byte	2,35,4,6
	.byte	'GtmTomOutenCtrl',0,2
	.word	227
	.byte	2,35,6,6
	.byte	'GtmTomOutenStat',0,2
	.word	227
	.byte	2,35,8,6
	.byte	'GtmTomFupd',0,4
	.word	287
	.byte	2,35,10,0,5
	.word	1862
	.byte	11
	.word	2013
	.byte	6
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	2018
	.byte	2,35,8,0,5
	.word	1812
	.byte	11
	.word	2055
	.byte	6
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	2060
	.byte	2,35,32,7,8
	.word	287
	.byte	8,1,0,6
	.byte	'GtmTomModuleUsage',0,8
	.word	2093
	.byte	2,35,36,6
	.byte	'GtmTomUsage',0,4
	.word	1208
	.byte	2,35,44,9,3,211,6,9,12,6
	.byte	'TomUsage',0,1
	.word	387
	.byte	2,35,0,6
	.byte	'GtmTomIrqMode',0,1
	.word	387
	.byte	2,35,1,6
	.byte	'GtmTomControlWord',0,4
	.word	287
	.byte	2,35,2,9,3,199,6,9,12,6
	.byte	'GtmTomIrqEn',0,1
	.word	387
	.byte	2,35,0,6
	.byte	'GtmTomCn0Value',0,2
	.word	227
	.byte	2,35,2,6
	.byte	'GtmTomCm0Value',0,2
	.word	227
	.byte	2,35,4,6
	.byte	'GtmTomCm1Value',0,2
	.word	227
	.byte	2,35,6,6
	.byte	'GtmTomSr0Value',0,2
	.word	227
	.byte	2,35,8,6
	.byte	'GtmTomSr1Value',0,2
	.word	227
	.byte	2,35,10,0,5
	.word	2224
	.byte	11
	.word	2372
	.byte	6
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	2377
	.byte	2,35,8,0,5
	.word	2150
	.byte	11
	.word	2412
	.byte	6
	.byte	'GtmTomConfigPtr',0,4
	.word	2417
	.byte	2,35,48,9,3,154,6,11,40,6
	.byte	'Gtm_TimUsage',0,8
	.word	404
	.byte	2,35,0,7,16
	.word	387
	.byte	8,15,0,7,32
	.word	2475
	.byte	8,1,0,6
	.byte	'Gtm_TomUsage',0,32
	.word	2484
	.byte	2,35,8,0,5
	.word	2447
	.byte	11
	.word	2516
	.byte	6
	.byte	'GtmModUsageConfigPtr',0,4
	.word	2521
	.byte	2,35,52,9,3,240,6,9,4,6
	.byte	'GtmCtrlValue',0,2
	.word	227
	.byte	2,35,0,6
	.byte	'GtmIrqEnable',0,2
	.word	227
	.byte	2,35,2,0,5
	.word	2556
	.byte	11
	.word	2607
	.byte	6
	.byte	'GtmGeneralConfigPtr',0,4
	.word	2612
	.byte	2,35,56,9,3,249,6,9,6,6
	.byte	'TbuChannelCtrl',0,1
	.word	387
	.byte	2,35,0,6
	.byte	'TbuBaseValue',0,4
	.word	287
	.byte	2,35,2,0,5
	.word	2646
	.byte	11
	.word	2699
	.byte	6
	.byte	'GtmTbuConfigPtr',0,4
	.word	2704
	.byte	2,35,60,5
	.word	387
	.byte	11
	.word	2734
	.byte	6
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	2739
	.byte	2,35,64,6
	.byte	'GtmTtcanTriggers',0,2
	.word	1417
	.byte	2,35,68,0,5
	.word	1289
	.byte	11
	.word	2801
	.byte	6
	.byte	'GtmModuleConfigPtr',0,4
	.word	2806
	.byte	2,35,8,0,5
	.word	991
	.byte	11
	.word	2840
	.byte	6
	.byte	'GtmConfigRootPtr',0,4
	.word	2845
	.byte	2,35,4,6
	.byte	'ResetCfg',0,4
	.word	287
	.byte	2,35,8,6
	.byte	'NoOfClockCfg',0,4
	.word	287
	.byte	2,35,12,6
	.byte	'NoOfRamCfg',0,4
	.word	287
	.byte	2,35,16,6
	.byte	'MaxMode',0,4
	.word	287
	.byte	2,35,20,3
	.byte	'Mcu_StandbyModeType',0,2,171,4,16,6,6
	.byte	'PMSWCR0',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'CrcCheckEnable',0,1
	.word	387
	.byte	2,35,4,0,5
	.word	2953
	.byte	11
	.word	3021
	.byte	6
	.byte	'StandbyCfgPtr',0,4
	.word	3026
	.byte	2,35,24,0,5
	.word	343
	.byte	11
	.word	3055
	.byte	6
	.byte	'Mcu_ConfigData',0,4
	.word	3060
	.byte	2,35,8,3
	.byte	'Dio_ConfigType',0,4,147,2,16,12,3
	.byte	'Dio_PortChannelIdType',0,4,139,2,16,8,6
	.byte	'Dio_PortIdConfig',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'Dio_ChannelConfig',0,4
	.word	287
	.byte	2,35,4,0,5
	.word	3110
	.byte	11
	.word	3192
	.byte	6
	.byte	'Dio_PortChannelConfigPtr',0,4
	.word	3197
	.byte	2,35,0,3
	.byte	'Dio_ChannelGroupType',0,4,129,2,16,4,6
	.byte	'mask',0,2
	.word	227
	.byte	2,35,0,6
	.byte	'offset',0,1
	.word	387
	.byte	2,35,2,6
	.byte	'port',0,1
	.word	387
	.byte	2,35,3,0,5
	.word	3236
	.byte	11
	.word	3308
	.byte	6
	.byte	'Dio_ChannelGroupConfigPtr',0,4
	.word	3313
	.byte	2,35,4,6
	.byte	'Dio_ChannelGroupConfigSize',0,4
	.word	287
	.byte	2,35,8,0,5
	.word	3089
	.byte	11
	.word	3390
	.byte	6
	.byte	'Dio_ConfigData',0,4
	.word	3395
	.byte	2,35,12,3
	.byte	'Port_ConfigType',0,5,176,3,16,12,3
	.byte	'Port_n_ConfigType',0,5,140,3,16,28,12
	.byte	'Port_n_ControlType',0,5,206,2,15,16,9,5,208,2,3,16,6
	.byte	'PC0',0,1
	.word	387
	.byte	2,35,0,6
	.byte	'PC1',0,1
	.word	387
	.byte	2,35,1,6
	.byte	'PC2',0,1
	.word	387
	.byte	2,35,2,6
	.byte	'PC3',0,1
	.word	387
	.byte	2,35,3,6
	.byte	'PC4',0,1
	.word	387
	.byte	2,35,4,6
	.byte	'PC5',0,1
	.word	387
	.byte	2,35,5,6
	.byte	'PC6',0,1
	.word	387
	.byte	2,35,6,6
	.byte	'PC7',0,1
	.word	387
	.byte	2,35,7,6
	.byte	'PC8',0,1
	.word	387
	.byte	2,35,8,6
	.byte	'PC9',0,1
	.word	387
	.byte	2,35,9,6
	.byte	'PC10',0,1
	.word	387
	.byte	2,35,10,6
	.byte	'PC11',0,1
	.word	387
	.byte	2,35,11,6
	.byte	'PC12',0,1
	.word	387
	.byte	2,35,12,6
	.byte	'PC13',0,1
	.word	387
	.byte	2,35,13,6
	.byte	'PC14',0,1
	.word	387
	.byte	2,35,14,6
	.byte	'PC15',0,1
	.word	387
	.byte	2,35,15,0,6
	.byte	'B',0,16
	.word	3495
	.byte	2,35,0,7,16
	.word	287
	.byte	8,3,0,6
	.byte	'U',0,16
	.word	3727
	.byte	2,35,0,0,6
	.byte	'PinControl',0,16
	.word	3470
	.byte	2,35,0,12
	.byte	'Port_n_PinType',0,5,176,2,15,4,9,5,178,2,3,2,10
	.byte	'P0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'P1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'P2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'P3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'P4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'P5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'P6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'P7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'P8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'P9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'P10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'P11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'P12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'P13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'P14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'P15',0,1
	.word	387
	.byte	1,0,2,35,1,0,6
	.byte	'B',0,2
	.word	3789
	.byte	2,35,0,6
	.byte	'U',0,4
	.word	287
	.byte	2,35,0,0,6
	.byte	'PinLevel',0,4
	.word	3768
	.byte	2,35,16,6
	.byte	'DriverStrength0',0,4
	.word	287
	.byte	2,35,20,6
	.byte	'DriverStrength1',0,4
	.word	287
	.byte	2,35,24,0,5
	.word	3446
	.byte	11
	.word	4118
	.byte	6
	.byte	'PortConfigSetPtr',0,4
	.word	4123
	.byte	2,35,0,5
	.word	227
	.byte	11
	.word	4154
	.byte	6
	.byte	'PDiscSet',0,4
	.word	4159
	.byte	2,35,4,5
	.word	287
	.byte	11
	.word	4182
	.byte	6
	.byte	'Port_PCSRConfigTypePtr',0,4
	.word	4187
	.byte	2,35,8,0,5
	.word	3424
	.byte	11
	.word	4225
	.byte	6
	.byte	'Port_ConfigData',0,4
	.word	4230
	.byte	2,35,16,3
	.byte	'Adc_ConfigType',0,6,247,7,17,16,3
	.byte	'Adc_KernelConfigType',0,6,163,7,17,16,3
	.byte	'Adc_HwUnitCfgType',0,6,138,6,16,20,6
	.byte	'ArbitrationLength',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'TriggSrcArbLevel',0,4
	.word	287
	.byte	2,35,4,6
	.byte	'KernelInputClass',0,8
	.word	2093
	.byte	2,35,8,6
	.byte	'SyncConvMode',0,1
	.word	387
	.byte	2,35,16,6
	.byte	'SlaveReady',0,1
	.word	387
	.byte	2,35,17,6
	.byte	'DmaChannel',0,1
	.word	387
	.byte	2,35,18,0,5
	.word	4308
	.byte	11
	.word	4474
	.byte	6
	.byte	'HwCfgPtr',0,4
	.word	4479
	.byte	2,35,0,3
	.byte	'Adc_ChannelCfgType',0,6,136,7,17,28,6
	.byte	'AdcChConfigValue',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'AdcChResAccumulation',0,4
	.word	287
	.byte	2,35,4,6
	.byte	'AdcIsChLimitChkEnabled',0,4
	.word	287
	.byte	2,35,8,6
	.byte	'AdcLimitChkRange',0,4
	.word	287
	.byte	2,35,12,6
	.byte	'AdcLimitChkBnd',0,4
	.word	287
	.byte	2,35,16,6
	.byte	'AdcChBndSelxValue',0,4
	.word	287
	.byte	2,35,20,6
	.byte	'AdcSyncChannel',0,1
	.word	387
	.byte	2,35,24,0,5
	.word	4502
	.byte	11
	.word	4717
	.byte	6
	.byte	'ChCfgPtr',0,4
	.word	4722
	.byte	2,35,4,3
	.byte	'Adc_GroupCfgType',0,6,182,6,16,12,5
	.word	387
	.byte	11
	.word	4768
	.byte	6
	.byte	'GroupDefinition',0,4
	.word	4773
	.byte	2,35,0,6
	.byte	'IntChMask',0,2
	.word	227
	.byte	2,35,4,6
	.byte	'TriggSrc',0,1
	.word	387
	.byte	2,35,6,6
	.byte	'GrpRequestSrc',0,1
	.word	387
	.byte	2,35,7,6
	.byte	'ConvMode',0,1
	.word	387
	.byte	2,35,8,6
	.byte	'NumSamples',0,1
	.word	387
	.byte	2,35,9,6
	.byte	'StreamBufferMode',0,1
	.word	387
	.byte	2,35,10,0,5
	.word	4745
	.byte	11
	.word	4928
	.byte	6
	.byte	'GrpCfgPtr',0,4
	.word	4933
	.byte	2,35,8,6
	.byte	'TotCfgGrps',0,1
	.word	387
	.byte	2,35,12,0,5
	.word	4281
	.byte	11
	.word	4978
	.byte	7,8
	.word	4983
	.byte	8,1,0,6
	.byte	'CfgPtr',0,8
	.word	4988
	.byte	2,35,0,3
	.byte	'Adc_GlobalCfgType',0,6,186,7,17,28,6
	.byte	'ClkPrescale',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'GlobInputClass',0,8
	.word	2093
	.byte	2,35,4,6
	.byte	'PostCalEnable',0,4
	.word	287
	.byte	2,35,12,6
	.byte	'LowPowerSupply',0,4
	.word	287
	.byte	2,35,16,6
	.byte	'RefPrechargeCtrl',0,4
	.word	287
	.byte	2,35,20,6
	.byte	'OperationMode',0,1
	.word	387
	.byte	2,35,24,0,5
	.word	5013
	.byte	11
	.word	5179
	.byte	6
	.byte	'GlobCfgPtr',0,4
	.word	5184
	.byte	2,35,8,6
	.byte	'SleepMode',0,1
	.word	387
	.byte	2,35,12,0,5
	.word	4260
	.byte	11
	.word	5229
	.byte	6
	.byte	'Adc_ConfigData',0,4
	.word	5234
	.byte	2,35,20,3
	.byte	'Uart_ConfigType',0,7,189,4,16,8,3
	.byte	'Uart_ChannelType',0,7,155,4,16,32,3
	.byte	'UartNotifType',0,7,142,4,16,16,13,1,1,14
	.byte	'Uart_ErrorIdType',0,7,253,3,14,1,15
	.byte	'UART_NO_ERR',0,0,15
	.byte	'UART_PARITY_ERR',0,1,15
	.byte	'UART_FRAME_ERR',0,2,15
	.byte	'UART_TXOVERFLOW_ERR',0,3,15
	.byte	'UART_RXOVERFLOW_ERR',0,4,15
	.byte	'UART_RXUNDERFLOW_ERR',0,5,0,16
	.word	5331
	.byte	0,11
	.word	5328
	.byte	17
	.byte	'Uart_NotificationPtrType',0,7,135,4,15
	.word	5477
	.byte	6
	.byte	'UartTransmitNotifPtr',0,4
	.word	5482
	.byte	2,35,0,6
	.byte	'UartReceiveNotifPtr',0,4
	.word	5482
	.byte	2,35,4,6
	.byte	'UartAbortTransmitNotifPtr',0,4
	.word	5482
	.byte	2,35,8,6
	.byte	'UartAbortReceiveNotifPtr',0,4
	.word	5482
	.byte	2,35,12,0,6
	.byte	'UartNotif',0,16
	.word	5308
	.byte	2,35,0,6
	.byte	'HwBrgNumerator',0,2
	.word	227
	.byte	2,35,16,6
	.byte	'HwBrgDenominator',0,2
	.word	227
	.byte	2,35,18,6
	.byte	'HwBitconPrescalar',0,2
	.word	227
	.byte	2,35,20,6
	.byte	'HwBitconOversampling',0,1
	.word	387
	.byte	2,35,22,6
	.byte	'HwModule',0,1
	.word	387
	.byte	2,35,23,6
	.byte	'StopBits',0,1
	.word	387
	.byte	2,35,24,6
	.byte	'DataLength',0,1
	.word	387
	.byte	2,35,25,6
	.byte	'RxPinSelection',0,1
	.word	387
	.byte	2,35,26,6
	.byte	'ParityEnable',0,1
	.word	387
	.byte	2,35,27,6
	.byte	'Parity',0,1
	.word	387
	.byte	2,35,28,6
	.byte	'CtsEnable',0,1
	.word	387
	.byte	2,35,29,6
	.byte	'CtsPolarity',0,1
	.word	387
	.byte	2,35,30,0,5
	.word	5285
	.byte	11
	.word	5930
	.byte	6
	.byte	'ChannelConfigPtr',0,4
	.word	5935
	.byte	2,35,0,6
	.byte	'NoOfChannels',0,1
	.word	387
	.byte	2,35,4,0,5
	.word	5263
	.byte	11
	.word	5989
	.byte	6
	.byte	'Uart_ConfigData',0,4
	.word	5994
	.byte	2,35,24,3
	.byte	'Spi_ConfigType',0,8,237,9,16,40,3
	.byte	'Spi_ChannelConfig',0,8,247,7,16,12,6
	.byte	'DefaultData',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'DataConfig',0,2
	.word	227
	.byte	2,35,4,6
	.byte	'NoOfBuffers',0,2
	.word	227
	.byte	2,35,6,6
	.byte	'ChannelBufferType',0,1
	.word	387
	.byte	2,35,8,0,5
	.word	6045
	.byte	11
	.word	6159
	.byte	6
	.byte	'SpiChannelConfigPtr',0,4
	.word	6164
	.byte	2,35,0,3
	.byte	'Spi_JobConfig',0,8,183,8,16,24,18,1,1,11
	.word	6218
	.byte	17
	.byte	'Spi_NotifFunctionPtrType',0,8,238,7,15
	.word	6221
	.byte	6
	.byte	'JobEndNotification',0,4
	.word	6226
	.byte	2,35,0,5
	.word	387
	.byte	11
	.word	6288
	.byte	6
	.byte	'ChannelLinkPtr',0,4
	.word	6293
	.byte	2,35,4,6
	.byte	'BaudRateConfig',0,4
	.word	287
	.byte	2,35,8,6
	.byte	'TimeDelayConfig',0,4
	.word	287
	.byte	2,35,12,6
	.byte	'CSPin',0,2
	.word	227
	.byte	2,35,16,6
	.byte	'CSPolarity',0,1
	.word	387
	.byte	2,35,18,6
	.byte	'ShiftClkConfig',0,1
	.word	387
	.byte	2,35,19,6
	.byte	'JobPriority',0,1
	.word	387
	.byte	2,35,20,6
	.byte	'HwUnit',0,1
	.word	387
	.byte	2,35,21,6
	.byte	'ChannelBasedChipSelect',0,1
	.word	387
	.byte	2,35,22,6
	.byte	'ParitySelection',0,1
	.word	387
	.byte	2,35,23,0,5
	.word	6198
	.byte	11
	.word	6525
	.byte	6
	.byte	'SpiJobConfigPtr',0,4
	.word	6530
	.byte	2,35,4,3
	.byte	'Spi_SequenceConfig',0,8,252,8,16,16,6
	.byte	'SeqEndNotification',0,4
	.word	6226
	.byte	2,35,0,6
	.byte	'JobLinkPtr',0,4
	.word	4159
	.byte	2,35,4,6
	.byte	'SeqSharingJobs',0,4
	.word	2739
	.byte	2,35,8,6
	.byte	'JobsInParamSeq',0,2
	.word	227
	.byte	2,35,12,6
	.byte	'InterruptibleSequence',0,1
	.word	387
	.byte	2,35,14,0,5
	.word	6560
	.byte	11
	.word	6713
	.byte	6
	.byte	'SpiSequenceConfigPtr',0,4
	.word	6718
	.byte	2,35,8,3
	.byte	'Spi_HWModuleConfig',0,8,174,9,16,16,5
	.word	287
	.byte	6
	.byte	'HWClkSetting',0,4
	.word	6778
	.byte	2,35,0,5
	.word	287
	.byte	6
	.byte	'HWCSPolaritySetting',0,4
	.word	6805
	.byte	2,35,4,5
	.word	287
	.byte	6
	.byte	'HWPinSetting',0,4
	.word	6839
	.byte	2,35,8,3
	.byte	'Spi_DmaConfigType',0,8,164,9,16,2,19,9,147,1,9,1,15
	.byte	'DMA_CHANNEL0',0,0,15
	.byte	'DMA_CHANNEL1',0,1,15
	.byte	'DMA_CHANNEL2',0,2,15
	.byte	'DMA_CHANNEL3',0,3,15
	.byte	'DMA_CHANNEL4',0,4,15
	.byte	'DMA_CHANNEL5',0,5,15
	.byte	'DMA_CHANNEL6',0,6,15
	.byte	'DMA_CHANNEL7',0,7,15
	.byte	'DMA_CHANNEL8',0,8,15
	.byte	'DMA_CHANNEL9',0,9,15
	.byte	'DMA_CHANNEL10',0,10,15
	.byte	'DMA_CHANNEL11',0,11,15
	.byte	'DMA_CHANNEL12',0,12,15
	.byte	'DMA_CHANNEL13',0,13,15
	.byte	'DMA_CHANNEL14',0,14,15
	.byte	'DMA_CHANNEL15',0,15,15
	.byte	'DMA_CHANNEL16',0,16,15
	.byte	'DMA_CHANNEL17',0,17,15
	.byte	'DMA_CHANNEL18',0,18,15
	.byte	'DMA_CHANNEL19',0,19,15
	.byte	'DMA_CHANNEL20',0,20,15
	.byte	'DMA_CHANNEL21',0,21,15
	.byte	'DMA_CHANNEL22',0,22,15
	.byte	'DMA_CHANNEL23',0,23,15
	.byte	'DMA_CHANNEL24',0,24,15
	.byte	'DMA_CHANNEL25',0,25,15
	.byte	'DMA_CHANNEL26',0,26,15
	.byte	'DMA_CHANNEL27',0,27,15
	.byte	'DMA_CHANNEL28',0,28,15
	.byte	'DMA_CHANNEL29',0,29,15
	.byte	'DMA_CHANNEL30',0,30,15
	.byte	'DMA_CHANNEL31',0,31,15
	.byte	'DMA_CHANNEL32',0,32,15
	.byte	'DMA_CHANNEL33',0,33,15
	.byte	'DMA_CHANNEL34',0,34,15
	.byte	'DMA_CHANNEL35',0,35,15
	.byte	'DMA_CHANNEL36',0,36,15
	.byte	'DMA_CHANNEL37',0,37,15
	.byte	'DMA_CHANNEL38',0,38,15
	.byte	'DMA_CHANNEL39',0,39,15
	.byte	'DMA_CHANNEL40',0,40,15
	.byte	'DMA_CHANNEL41',0,41,15
	.byte	'DMA_CHANNEL42',0,42,15
	.byte	'DMA_CHANNEL43',0,43,15
	.byte	'DMA_CHANNEL44',0,44,15
	.byte	'DMA_CHANNEL45',0,45,15
	.byte	'DMA_CHANNEL46',0,46,15
	.byte	'DMA_CHANNEL47',0,47,15
	.byte	'DMA_CHANNEL48',0,48,15
	.byte	'DMA_CHANNEL49',0,49,15
	.byte	'DMA_CHANNEL50',0,50,15
	.byte	'DMA_CHANNEL51',0,51,15
	.byte	'DMA_CHANNEL52',0,52,15
	.byte	'DMA_CHANNEL53',0,53,15
	.byte	'DMA_CHANNEL54',0,54,15
	.byte	'DMA_CHANNEL55',0,55,15
	.byte	'DMA_CHANNEL56',0,56,15
	.byte	'DMA_CHANNEL57',0,57,15
	.byte	'DMA_CHANNEL58',0,58,15
	.byte	'DMA_CHANNEL59',0,59,15
	.byte	'DMA_CHANNEL60',0,60,15
	.byte	'DMA_CHANNEL61',0,61,15
	.byte	'DMA_CHANNEL62',0,62,15
	.byte	'DMA_CHANNEL63',0,63,15
	.byte	'DMA_CHANNEL64',0,192,0,15
	.byte	'DMA_CHANNEL65',0,193,0,15
	.byte	'DMA_CHANNEL66',0,194,0,15
	.byte	'DMA_CHANNEL67',0,195,0,15
	.byte	'DMA_CHANNEL68',0,196,0,15
	.byte	'DMA_CHANNEL69',0,197,0,15
	.byte	'DMA_CHANNEL70',0,198,0,15
	.byte	'DMA_CHANNEL71',0,199,0,15
	.byte	'DMA_CHANNEL72',0,200,0,15
	.byte	'DMA_CHANNEL73',0,201,0,15
	.byte	'DMA_CHANNEL74',0,202,0,15
	.byte	'DMA_CHANNEL75',0,203,0,15
	.byte	'DMA_CHANNEL76',0,204,0,15
	.byte	'DMA_CHANNEL77',0,205,0,15
	.byte	'DMA_CHANNEL78',0,206,0,15
	.byte	'DMA_CHANNEL79',0,207,0,15
	.byte	'DMA_CHANNEL80',0,208,0,15
	.byte	'DMA_CHANNEL81',0,209,0,15
	.byte	'DMA_CHANNEL82',0,210,0,15
	.byte	'DMA_CHANNEL83',0,211,0,15
	.byte	'DMA_CHANNEL84',0,212,0,15
	.byte	'DMA_CHANNEL85',0,213,0,15
	.byte	'DMA_CHANNEL86',0,214,0,15
	.byte	'DMA_CHANNEL87',0,215,0,15
	.byte	'DMA_CHANNEL88',0,216,0,15
	.byte	'DMA_CHANNEL89',0,217,0,15
	.byte	'DMA_CHANNEL90',0,218,0,15
	.byte	'DMA_CHANNEL91',0,219,0,15
	.byte	'DMA_CHANNEL92',0,220,0,15
	.byte	'DMA_CHANNEL93',0,221,0,15
	.byte	'DMA_CHANNEL94',0,222,0,15
	.byte	'DMA_CHANNEL95',0,223,0,15
	.byte	'DMA_CHANNEL96',0,224,0,15
	.byte	'DMA_CHANNEL97',0,225,0,15
	.byte	'DMA_CHANNEL98',0,226,0,15
	.byte	'DMA_CHANNEL99',0,227,0,15
	.byte	'DMA_CHANNEL100',0,228,0,15
	.byte	'DMA_CHANNEL101',0,229,0,15
	.byte	'DMA_CHANNEL102',0,230,0,15
	.byte	'DMA_CHANNEL103',0,231,0,15
	.byte	'DMA_CHANNEL104',0,232,0,15
	.byte	'DMA_CHANNEL105',0,233,0,15
	.byte	'DMA_CHANNEL106',0,234,0,15
	.byte	'DMA_CHANNEL107',0,235,0,15
	.byte	'DMA_CHANNEL108',0,236,0,15
	.byte	'DMA_CHANNEL109',0,237,0,15
	.byte	'DMA_CHANNEL110',0,238,0,15
	.byte	'DMA_CHANNEL111',0,239,0,15
	.byte	'DMA_CHANNEL112',0,240,0,15
	.byte	'DMA_CHANNEL113',0,241,0,15
	.byte	'DMA_CHANNEL114',0,242,0,15
	.byte	'DMA_CHANNEL115',0,243,0,15
	.byte	'DMA_CHANNEL116',0,244,0,15
	.byte	'DMA_CHANNEL117',0,245,0,15
	.byte	'DMA_CHANNEL118',0,246,0,15
	.byte	'DMA_CHANNEL119',0,247,0,15
	.byte	'DMA_CHANNEL120',0,248,0,15
	.byte	'DMA_CHANNEL121',0,249,0,15
	.byte	'DMA_CHANNEL122',0,250,0,15
	.byte	'DMA_CHANNEL123',0,251,0,15
	.byte	'DMA_CHANNEL124',0,252,0,15
	.byte	'DMA_CHANNEL125',0,253,0,15
	.byte	'DMA_CHANNEL126',0,254,0,15
	.byte	'DMA_CHANNEL127',0,255,0,15
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0,6
	.byte	'TxDmaChannel',0,1
	.word	6890
	.byte	2,35,0,6
	.byte	'RxDmaChannel',0,1
	.word	6890
	.byte	2,35,1,0,5
	.word	6866
	.byte	11
	.word	9095
	.byte	6
	.byte	'SpiDmaConfigPtr',0,4
	.word	9100
	.byte	2,35,12,0,5
	.word	6753
	.byte	11
	.word	9131
	.byte	7,16
	.word	9136
	.byte	8,3,0,6
	.byte	'HWModuleConfigPtr',0,16
	.word	9141
	.byte	2,35,12,3
	.byte	'Spi_BaudrateEconType',0,8,214,9,16,6,6
	.byte	'EconVal',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'QSPIHwUnit',0,1
	.word	387
	.byte	2,35,4,0,5
	.word	9177
	.byte	11
	.word	9242
	.byte	6
	.byte	'SpiBaudrateEconPtr',0,4
	.word	9247
	.byte	2,35,28,6
	.byte	'NoOfJobs',0,2
	.word	227
	.byte	2,35,32,6
	.byte	'NoOfChannels',0,1
	.word	387
	.byte	2,35,34,6
	.byte	'NoOfSequences',0,1
	.word	387
	.byte	2,35,35,6
	.byte	'NoOfEconReg',0,1
	.word	387
	.byte	2,35,36,0,5
	.word	6024
	.byte	11
	.word	9365
	.byte	6
	.byte	'Spi_ConfigData',0,4
	.word	9370
	.byte	2,35,28,3
	.byte	'Dma_ConfigType',0,10,187,4,16,24,3
	.byte	'Dma_ChannelConfigType',0,10,174,4,16,12,6
	.byte	'DmaChannelConfig',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'DmaAddrIntControl',0,4
	.word	287
	.byte	2,35,4,6
	.byte	'DmaHwResourceMode',0,1
	.word	387
	.byte	2,35,8,6
	.byte	'DmaChannelHwPartitionConfig',0,1
	.word	387
	.byte	2,35,9,6
	.byte	'DmaChannelNumber',0,1
	.word	387
	.byte	2,35,10,0,5
	.word	9420
	.byte	11
	.word	9592
	.byte	6
	.byte	'ChannelCfgPtr',0,4
	.word	9597
	.byte	2,35,0,6
	.byte	'DmaPat0',0,4
	.word	287
	.byte	2,35,4,6
	.byte	'DmaPat1',0,4
	.word	287
	.byte	2,35,8,6
	.byte	'DmaMovEng0Err',0,4
	.word	287
	.byte	2,35,12,6
	.byte	'DmaMovEng1Err',0,4
	.word	287
	.byte	2,35,16,6
	.byte	'ChannelsConfigured',0,1
	.word	387
	.byte	2,35,20,0,5
	.word	9399
	.byte	11
	.word	9734
	.byte	6
	.byte	'Dma_ConfigData',0,4
	.word	9739
	.byte	2,35,32,5
	.word	387
	.byte	6
	.byte	'LocalConfigData',0,1
	.word	9768
	.byte	2,35,36,0,5
	.word	201
.L36:
	.byte	11
	.word	9799
	.byte	11
	.word	5328
	.byte	11
	.word	6218
	.byte	20
	.byte	'Mcal_ResetENDINIT',0,11,119,13,1,1,1,1,20
	.byte	'Mcal_SetENDINIT',0,11,146,1,13,1,1,1,1,21
	.byte	'Mcal_ResetSafetyENDINIT_Timed',0,11,190,2,13,1,1,1,1,22
	.byte	'TimeOut',0,11,190,2,50
	.word	287
	.byte	0,20
	.byte	'Mcal_SetSafetyENDINIT_Timed',0,11,214,2,13,1,1,1,1,20
	.byte	'IrqAscLin_Init',0,12,198,1,13,1,1,1,1,20
	.byte	'IrqCan_Init',0,12,240,1,13,1,1,1,1,20
	.byte	'IrqGtm_Init',0,12,175,2,13,1,1,1,1,20
	.byte	'IrqSpi_Init',0,12,238,2,13,1,1,1,1,20
	.byte	'IrqAdc_Init',0,12,131,3,13,1,1,1,1,20
	.byte	'IrqDma_Init',0,12,173,3,13,1,1,1,1,20
	.byte	'IrqStm_Init',0,12,155,4,13,1,1,1,1,23
	.byte	'Mcu_InitClock',0,2,245,5,23
	.word	387
	.byte	1,1,1,1,22
	.byte	'ClockSetting',0,2,245,5,51
	.word	287
	.byte	0,20
	.byte	'Mcu_DistributePllClock',0,2,146,6,13,1,1,1,1,14
	.byte	'Mcu_PllStatusType',0,2,202,3,14,1,15
	.byte	'MCU_PLL_UNLOCKED',0,0,15
	.byte	'MCU_PLL_LOCKED',0,1,15
	.byte	'MCU_PLL_STATUS_UNDEFINED',0,2,0,24
	.byte	'Mcu_GetPllStatus',0,2,167,6,26
	.word	10196
	.byte	1,1,1,1,21
	.byte	'Port_Init',0,5,255,3,13,1,1,1,1,5
	.word	3424
	.byte	11
	.word	10333
	.byte	22
	.byte	'ConfigPtr',0,5,129,4,27
	.word	10338
	.byte	0,21
	.byte	'Dio_Init',0,4,245,2,13,1,1,1,1,5
	.word	3089
	.byte	11
	.word	10381
	.byte	22
	.byte	'ConfigPtr',0,4,247,2,25
	.word	10386
	.byte	0,21
	.byte	'Dma_Init',0,10,238,4,13,1,1,1,1,5
	.word	9399
	.byte	11
	.word	10429
	.byte	22
	.byte	'ConfigPtr',0,10,238,4,44
	.word	10434
	.byte	0,21
	.byte	'Spi_Init',0,8,136,12,13,1,1,1,1,5
	.word	6024
	.byte	11
	.word	10477
	.byte	22
	.byte	'ConfigPtr',0,8,136,12,44
	.word	10482
	.byte	0,21
	.byte	'Adc_Init',0,6,184,8,13,1,1,1,1,5
	.word	4260
	.byte	11
	.word	10525
	.byte	22
	.byte	'ConfigPtr',0,6,186,8,25
	.word	10530
	.byte	0,21
	.byte	'Uart_Init',0,7,170,5,13,1,1,1,1,5
	.word	5263
	.byte	11
	.word	10574
	.byte	22
	.byte	'ConfigPtr',0,7,170,5,46
	.word	10579
	.byte	0,25
	.byte	'void',0,11
	.word	10604
	.byte	17
	.byte	'__prof_adm',0,13,1,1
	.word	10610
	.byte	26,1,11
	.word	10634
	.byte	17
	.byte	'__codeptr',0,13,1,1
	.word	10636
	.byte	17
	.byte	'uint8',0,14,90,29
	.word	387
	.byte	17
	.byte	'uint16',0,14,92,29
	.word	227
	.byte	17
	.byte	'uint32',0,14,94,29
	.word	287
	.byte	17
	.byte	'boolean',0,14,105,29
	.word	387
	.byte	17
	.byte	'Std_ReturnType',0,15,113,15
	.word	387
	.byte	17
	.byte	'EcuM_ConfigType',0,1,159,1,2
	.word	201
	.byte	17
	.byte	'unsigned_int',0,16,121,22
	.word	508
	.byte	3
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,17,45,16,4,10
	.byte	'EN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_ACCEN0_Bits',0,17,79,3
	.word	10788
	.byte	3
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,17,82,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_SCU_ACCEN1_Bits',0,17,85,3
	.word	11345
	.byte	3
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,17,88,16,4,10
	.byte	'STM0DIS',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'STM1DIS',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'STM2DIS',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,4
	.word	508
	.byte	29,0,2,35,2,0,17
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,17,94,3
	.word	11422
	.byte	3
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,17,97,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'BAUD2DIV',0,1
	.word	387
	.byte	4,0,2,35,0,10
	.byte	'SRIDIV',0,1
	.word	387
	.byte	4,4,2,35,1,10
	.byte	'LPDIV',0,1
	.word	387
	.byte	4,0,2,35,1,10
	.byte	'SPBDIV',0,1
	.word	387
	.byte	4,4,2,35,2,10
	.byte	'FSI2DIV',0,1
	.word	387
	.byte	2,2,2,35,2,10
	.byte	'reserved_22',0,1
	.word	387
	.byte	2,0,2,35,2,10
	.byte	'FSIDIV',0,1
	.word	387
	.byte	2,6,2,35,3,10
	.byte	'reserved_26',0,1
	.word	387
	.byte	2,4,2,35,3,10
	.byte	'CLKSEL',0,1
	.word	387
	.byte	2,2,2,35,3,10
	.byte	'UP',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON0_Bits',0,17,111,3
	.word	11558
	.byte	3
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,17,114,16,4,10
	.byte	'CANDIV',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'ERAYDIV',0,1
	.word	387
	.byte	4,0,2,35,0,10
	.byte	'STMDIV',0,1
	.word	387
	.byte	4,4,2,35,1,10
	.byte	'GTMDIV',0,1
	.word	387
	.byte	4,0,2,35,1,10
	.byte	'ETHDIV',0,1
	.word	387
	.byte	4,4,2,35,2,10
	.byte	'ASCLINFDIV',0,1
	.word	387
	.byte	4,0,2,35,2,10
	.byte	'ASCLINSDIV',0,1
	.word	387
	.byte	4,4,2,35,3,10
	.byte	'INSEL',0,1
	.word	387
	.byte	2,2,2,35,3,10
	.byte	'UP',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON1_Bits',0,17,126,3
	.word	11840
	.byte	3
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,17,129,1,16,4,10
	.byte	'BBBDIV',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	508
	.byte	26,2,2,35,2,10
	.byte	'UP',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON2_Bits',0,17,135,1,3
	.word	12078
	.byte	3
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,17,138,1,16,4,10
	.byte	'PLLDIV',0,1
	.word	387
	.byte	6,2,2,35,0,10
	.byte	'PLLSEL',0,1
	.word	387
	.byte	2,0,2,35,0,10
	.byte	'PLLERAYDIV',0,1
	.word	387
	.byte	6,2,2,35,1,10
	.byte	'PLLERAYSEL',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'SRIDIV',0,1
	.word	387
	.byte	6,2,2,35,2,10
	.byte	'SRISEL',0,1
	.word	387
	.byte	2,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	6,2,2,35,3,10
	.byte	'UP',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON3_Bits',0,17,149,1,3
	.word	12206
	.byte	3
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,17,152,1,16,4,10
	.byte	'SPBDIV',0,1
	.word	387
	.byte	6,2,2,35,0,10
	.byte	'SPBSEL',0,1
	.word	387
	.byte	2,0,2,35,0,10
	.byte	'GTMDIV',0,1
	.word	387
	.byte	6,2,2,35,1,10
	.byte	'GTMSEL',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'STMDIV',0,1
	.word	387
	.byte	6,2,2,35,2,10
	.byte	'STMSEL',0,1
	.word	387
	.byte	2,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	6,2,2,35,3,10
	.byte	'UP',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON4_Bits',0,17,163,1,3
	.word	12433
	.byte	3
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,17,166,1,16,4,10
	.byte	'MAXDIV',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	508
	.byte	26,2,2,35,2,10
	.byte	'UP',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON5_Bits',0,17,172,1,3
	.word	12652
	.byte	3
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,17,175,1,16,4,10
	.byte	'CPU0DIV',0,1
	.word	387
	.byte	6,2,2,35,0,10
	.byte	'reserved_6',0,4
	.word	508
	.byte	26,0,2,35,2,0,17
	.byte	'Ifx_SCU_CCUCON6_Bits',0,17,179,1,3
	.word	12780
	.byte	3
	.byte	'_Ifx_SCU_CHIPID_Bits',0,17,182,1,16,4,10
	.byte	'CHREV',0,1
	.word	387
	.byte	6,2,2,35,0,10
	.byte	'CHTEC',0,1
	.word	387
	.byte	2,0,2,35,0,10
	.byte	'CHID',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'EEA',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'UCODE',0,1
	.word	387
	.byte	7,0,2,35,2,10
	.byte	'FSIZE',0,1
	.word	387
	.byte	4,4,2,35,3,10
	.byte	'SP',0,1
	.word	387
	.byte	2,2,2,35,3,10
	.byte	'SEC',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CHIPID_Bits',0,17,193,1,3
	.word	12880
	.byte	3
	.byte	'_Ifx_SCU_DTSCON_Bits',0,17,196,1,16,4,10
	.byte	'PWD',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'START',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	2,4,2,35,0,10
	.byte	'CAL',0,4
	.word	508
	.byte	22,6,2,35,2,10
	.byte	'reserved_26',0,1
	.word	387
	.byte	5,1,2,35,3,10
	.byte	'SLCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_DTSCON_Bits',0,17,204,1,3
	.word	13088
	.byte	3
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,17,207,1,16,4,10
	.byte	'LOWER',0,2
	.word	227
	.byte	10,6,2,35,0,10
	.byte	'reserved_10',0,1
	.word	387
	.byte	5,1,2,35,1,10
	.byte	'LLU',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'UPPER',0,2
	.word	227
	.byte	10,6,2,35,2,10
	.byte	'reserved_26',0,1
	.word	387
	.byte	4,2,2,35,3,10
	.byte	'SLCK',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'UOF',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_DTSLIM_Bits',0,17,216,1,3
	.word	13253
	.byte	3
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,17,219,1,16,4,10
	.byte	'RESULT',0,2
	.word	227
	.byte	10,6,2,35,0,10
	.byte	'reserved_10',0,1
	.word	387
	.byte	4,2,2,35,1,10
	.byte	'RDY',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'BUSY',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,17,226,1,3
	.word	13436
	.byte	3
	.byte	'_Ifx_SCU_EICR_Bits',0,17,229,1,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'EXIS0',0,1
	.word	387
	.byte	3,1,2,35,0,10
	.byte	'reserved_7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'FEN0',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'REN0',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'LDEN0',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'EIEN0',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'INP0',0,1
	.word	387
	.byte	3,1,2,35,1,10
	.byte	'reserved_15',0,4
	.word	508
	.byte	5,12,2,35,2,10
	.byte	'EXIS1',0,1
	.word	387
	.byte	3,1,2,35,2,10
	.byte	'reserved_23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'FEN1',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'REN1',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'LDEN1',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'EIEN1',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'INP1',0,1
	.word	387
	.byte	3,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EICR_Bits',0,17,248,1,3
	.word	13590
	.byte	3
	.byte	'_Ifx_SCU_EIFR_Bits',0,17,251,1,16,4,10
	.byte	'INTF0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'INTF1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'INTF2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'INTF3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'INTF4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'INTF5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'INTF6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'INTF7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	508
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_SCU_EIFR_Bits',0,17,134,2,3
	.word	13954
	.byte	3
	.byte	'_Ifx_SCU_EMSR_Bits',0,17,137,2,16,4,10
	.byte	'POL',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'MODE',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'ENON',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'PSEL',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,2
	.word	227
	.byte	12,0,2,35,0,10
	.byte	'EMSF',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'SEMSF',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	387
	.byte	6,0,2,35,2,10
	.byte	'EMSFM',0,1
	.word	387
	.byte	2,6,2,35,3,10
	.byte	'SEMSFM',0,1
	.word	387
	.byte	2,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	387
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_SCU_EMSR_Bits',0,17,150,2,3
	.word	14165
	.byte	3
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,17,153,2,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	7,1,2,35,0,10
	.byte	'EDCON',0,2
	.word	227
	.byte	2,7,2,35,0,10
	.byte	'reserved_9',0,4
	.word	508
	.byte	23,0,2,35,2,0,17
	.byte	'Ifx_SCU_ESRCFG_Bits',0,17,158,2,3
	.word	14417
	.byte	3
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,17,161,2,16,4,10
	.byte	'ARI',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'ARC',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	508
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_ESROCFG_Bits',0,17,166,2,3
	.word	14535
	.byte	3
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,17,169,2,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	28,4,2,35,2,10
	.byte	'EVR13OFF',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'BPEVR13OFF',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'reserved_30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVR13CON_Bits',0,17,176,2,3
	.word	14646
	.byte	3
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,17,179,2,16,4,10
	.byte	'ADC13V',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'ADCSWDV',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	7,1,2,35,3,10
	.byte	'VAL',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,17,186,2,3
	.word	14809
	.byte	3
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,17,189,2,16,4,10
	.byte	'EVR13OVMOD',0,1
	.word	387
	.byte	2,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	2,4,2,35,0,10
	.byte	'EVR13UVMOD',0,1
	.word	387
	.byte	2,2,2,35,0,10
	.byte	'reserved_6',0,2
	.word	227
	.byte	10,0,2,35,0,10
	.byte	'SWDOVMOD',0,1
	.word	387
	.byte	2,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	387
	.byte	2,4,2,35,2,10
	.byte	'SWDUVMOD',0,1
	.word	387
	.byte	2,2,2,35,2,10
	.byte	'reserved_22',0,2
	.word	227
	.byte	8,2,2,35,2,10
	.byte	'SLCK',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,17,201,2,3
	.word	14971
	.byte	3
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,17,204,2,16,4,10
	.byte	'EVR13OVVAL',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'SWDOVVAL',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	6,2,2,35,3,10
	.byte	'SLCK',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVROVMON_Bits',0,17,212,2,3
	.word	15249
	.byte	3
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,17,215,2,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	28,4,2,35,2,10
	.byte	'RSTSWDOFF',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'BPRSTSWDOFF',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'SLCK',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,17,222,2,3
	.word	15428
	.byte	3
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,17,225,2,16,4,10
	.byte	'SD33P',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	387
	.byte	4,0,2,35,0,10
	.byte	'SD33I',0,1
	.word	387
	.byte	4,4,2,35,1,10
	.byte	'reserved_12',0,4
	.word	508
	.byte	19,1,2,35,2,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,17,232,2,3
	.word	15588
	.byte	3
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,17,235,2,16,4,10
	.byte	'SDFREQSPRD',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	387
	.byte	4,0,2,35,0,10
	.byte	'TON',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'TOFF',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'SDSTEP',0,1
	.word	387
	.byte	4,4,2,35,3,10
	.byte	'SYNCDIV',0,1
	.word	387
	.byte	3,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,17,244,2,3
	.word	15749
	.byte	3
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,17,247,2,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'STBS',0,1
	.word	387
	.byte	2,6,2,35,1,10
	.byte	'STSP',0,1
	.word	387
	.byte	2,4,2,35,1,10
	.byte	'NS',0,1
	.word	387
	.byte	2,2,2,35,1,10
	.byte	'OL',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'PIAD',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'ADCMODE',0,1
	.word	387
	.byte	4,4,2,35,2,10
	.byte	'ADCLPF',0,1
	.word	387
	.byte	2,2,2,35,2,10
	.byte	'ADCLSB',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'reserved_23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'SDLUT',0,1
	.word	387
	.byte	6,2,2,35,3,10
	.byte	'reserved_30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,17,134,3,3
	.word	15941
	.byte	3
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,17,137,3,16,4,10
	.byte	'SDOLCON',0,1
	.word	387
	.byte	7,1,2,35,0,10
	.byte	'MODSEL',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'MODLOW',0,1
	.word	387
	.byte	7,1,2,35,1,10
	.byte	'reserved_15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'SDVOKLVL',0,1
	.word	387
	.byte	6,2,2,35,2,10
	.byte	'MODMAN',0,1
	.word	387
	.byte	2,0,2,35,2,10
	.byte	'MODHIGH',0,1
	.word	387
	.byte	7,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,17,147,3,3
	.word	16237
	.byte	3
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,17,150,3,16,4,10
	.byte	'EVR13',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'OV13',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	2,4,2,35,0,10
	.byte	'OVSWD',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'UV13',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'reserved_6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'UVSWD',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	2,6,2,35,1,10
	.byte	'BGPROK',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'reserved_11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'SCMOD',0,1
	.word	387
	.byte	2,2,2,35,1,10
	.byte	'reserved_14',0,4
	.word	508
	.byte	18,0,2,35,2,0,17
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,17,164,3,3
	.word	16452
	.byte	3
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,17,167,3,16,4,10
	.byte	'EVR13UVVAL',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'SWDUVVAL',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	6,2,2,35,3,10
	.byte	'SLCK',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,17,175,3,3
	.word	16741
	.byte	3
	.byte	'_Ifx_SCU_EXTCON_Bits',0,17,178,3,16,4,10
	.byte	'EN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'SEL0',0,1
	.word	387
	.byte	4,2,2,35,0,10
	.byte	'reserved_6',0,2
	.word	227
	.byte	10,0,2,35,0,10
	.byte	'EN1',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'NSEL',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'SEL1',0,1
	.word	387
	.byte	4,2,2,35,2,10
	.byte	'reserved_22',0,1
	.word	387
	.byte	2,0,2,35,2,10
	.byte	'DIV1',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_SCU_EXTCON_Bits',0,17,189,3,3
	.word	16920
	.byte	3
	.byte	'_Ifx_SCU_FDR_Bits',0,17,192,3,16,4,10
	.byte	'STEP',0,2
	.word	227
	.byte	10,6,2,35,0,10
	.byte	'reserved_10',0,1
	.word	387
	.byte	4,2,2,35,1,10
	.byte	'DM',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'RESULT',0,2
	.word	227
	.byte	10,6,2,35,2,10
	.byte	'reserved_26',0,1
	.word	387
	.byte	5,1,2,35,3,10
	.byte	'DISCLK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_FDR_Bits',0,17,200,3,3
	.word	17138
	.byte	3
	.byte	'_Ifx_SCU_FMR_Bits',0,17,203,3,16,4,10
	.byte	'FS0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'FS1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'FS2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'FS3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'FS4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'FS5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'FS6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'FS7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'FC0',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'FC1',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'FC2',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'FC3',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'FC4',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'FC5',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'FC6',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'FC7',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_SCU_FMR_Bits',0,17,223,3,3
	.word	17301
	.byte	3
	.byte	'_Ifx_SCU_ID_Bits',0,17,226,3,16,4,10
	.byte	'MODREV',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'MODTYPE',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'MODNUMBER',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_ID_Bits',0,17,231,3,3
	.word	17637
	.byte	3
	.byte	'_Ifx_SCU_IGCR_Bits',0,17,234,3,16,4,10
	.byte	'IPEN00',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'IPEN01',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'IPEN02',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'IPEN03',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'IPEN04',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'IPEN05',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'IPEN06',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'IPEN07',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	5,3,2,35,1,10
	.byte	'GEEN0',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'IGP0',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'IPEN10',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'IPEN11',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'IPEN12',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'IPEN13',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'IPEN14',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'IPEN15',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'IPEN16',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'IPEN17',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	5,3,2,35,3,10
	.byte	'GEEN1',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'IGP1',0,1
	.word	387
	.byte	2,0,2,35,3,0,17
	.byte	'Ifx_SCU_IGCR_Bits',0,17,130,4,3
	.word	17744
	.byte	3
	.byte	'_Ifx_SCU_IN_Bits',0,17,133,4,16,4,10
	.byte	'P0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'P1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	508
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_IN_Bits',0,17,138,4,3
	.word	18196
	.byte	3
	.byte	'_Ifx_SCU_IOCR_Bits',0,17,141,4,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'PC0',0,1
	.word	387
	.byte	4,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	4,4,2,35,1,10
	.byte	'PC1',0,1
	.word	387
	.byte	4,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_IOCR_Bits',0,17,148,4,3
	.word	18295
	.byte	3
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,17,151,4,16,4,10
	.byte	'LBISTREQ',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'LBISTREQP',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'PATTERNS',0,2
	.word	227
	.byte	14,0,2,35,0,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,17,157,4,3
	.word	18445
	.byte	3
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,17,160,4,16,4,10
	.byte	'SEED',0,4
	.word	508
	.byte	23,9,2,35,2,10
	.byte	'reserved_23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'SPLITSH',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'BODY',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'LBISTFREQU',0,1
	.word	387
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,17,167,4,3
	.word	18594
	.byte	3
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,17,170,4,16,4,10
	.byte	'SIGNATURE',0,4
	.word	508
	.byte	24,8,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	7,1,2,35,3,10
	.byte	'LBISTDONE',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,17,175,4,3
	.word	18755
	.byte	3
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,17,178,4,16,4,10
	.byte	'reserved_0',0,2
	.word	227
	.byte	16,0,2,35,0,10
	.byte	'LS',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'reserved_17',0,2
	.word	227
	.byte	14,1,2,35,2,10
	.byte	'LSEN',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_LCLCON0_Bits',0,17,184,4,3
	.word	18885
	.byte	3
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,17,187,4,16,4,10
	.byte	'LCLT0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'LCLT1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	508
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_LCLTEST_Bits',0,17,192,4,3
	.word	19019
	.byte	3
	.byte	'_Ifx_SCU_MANID_Bits',0,17,195,4,16,4,10
	.byte	'DEPT',0,1
	.word	387
	.byte	5,3,2,35,0,10
	.byte	'MANUF',0,2
	.word	227
	.byte	11,0,2,35,0,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_MANID_Bits',0,17,200,4,3
	.word	19134
	.byte	3
	.byte	'_Ifx_SCU_OMR_Bits',0,17,203,4,16,4,10
	.byte	'PS0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PS1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,2
	.word	227
	.byte	14,0,2,35,0,10
	.byte	'PCL0',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'PCL1',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,2
	.word	227
	.byte	14,0,2,35,2,0,17
	.byte	'Ifx_SCU_OMR_Bits',0,17,211,4,3
	.word	19245
	.byte	3
	.byte	'_Ifx_SCU_OSCCON_Bits',0,17,214,4,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PLLLV',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'OSCRES',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'GAINSEL',0,1
	.word	387
	.byte	2,3,2,35,0,10
	.byte	'MODE',0,1
	.word	387
	.byte	2,1,2,35,0,10
	.byte	'SHBY',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'PLLHV',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'reserved_9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'X1D',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'X1DEN',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'reserved_12',0,1
	.word	387
	.byte	4,0,2,35,1,10
	.byte	'OSCVAL',0,1
	.word	387
	.byte	5,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	387
	.byte	2,1,2,35,2,10
	.byte	'APREN',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_SCU_OSCCON_Bits',0,17,231,4,3
	.word	19403
	.byte	3
	.byte	'_Ifx_SCU_OUT_Bits',0,17,234,4,16,4,10
	.byte	'P0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'P1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	508
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_OUT_Bits',0,17,239,4,3
	.word	19743
	.byte	3
	.byte	'_Ifx_SCU_OVCCON_Bits',0,17,242,4,16,4,10
	.byte	'CSEL0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'CSEL1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'CSEL2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,2
	.word	227
	.byte	13,0,2,35,0,10
	.byte	'OVSTRT',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'OVSTP',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'DCINVAL',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	387
	.byte	5,0,2,35,2,10
	.byte	'OVCONF',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'POVCONF',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'reserved_26',0,1
	.word	387
	.byte	6,0,2,35,3,0,17
	.byte	'Ifx_SCU_OVCCON_Bits',0,17,255,4,3
	.word	19844
	.byte	3
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,17,130,5,16,4,10
	.byte	'OVEN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'OVEN1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'OVEN2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,4
	.word	508
	.byte	29,0,2,35,2,0,17
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,17,136,5,3
	.word	20111
	.byte	3
	.byte	'_Ifx_SCU_PDISC_Bits',0,17,139,5,16,4,10
	.byte	'PDIS0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PDIS1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	508
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_PDISC_Bits',0,17,144,5,3
	.word	20247
	.byte	3
	.byte	'_Ifx_SCU_PDR_Bits',0,17,147,5,16,4,10
	.byte	'PD0',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'PL0',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PD1',0,1
	.word	387
	.byte	3,1,2,35,0,10
	.byte	'PL1',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	508
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_SCU_PDR_Bits',0,17,154,5,3
	.word	20358
	.byte	3
	.byte	'_Ifx_SCU_PDRR_Bits',0,17,157,5,16,4,10
	.byte	'PDR0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PDR1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'PDR2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'PDR3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PDR4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'PDR5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'PDR6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PDR7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	508
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_SCU_PDRR_Bits',0,17,168,5,3
	.word	20491
	.byte	3
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,17,171,5,16,4,10
	.byte	'VCOBYP',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'VCOPWD',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'MODEN',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'SETFINDIS',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'CLRFINDIS',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'OSCDISCDIS',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'reserved_7',0,2
	.word	227
	.byte	2,7,2,35,0,10
	.byte	'NDIV',0,1
	.word	387
	.byte	7,0,2,35,1,10
	.byte	'PLLPWD',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'reserved_17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'RESLD',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	387
	.byte	5,0,2,35,2,10
	.byte	'PDIV',0,1
	.word	387
	.byte	4,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	387
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_SCU_PLLCON0_Bits',0,17,188,5,3
	.word	20694
	.byte	3
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,17,191,5,16,4,10
	.byte	'K2DIV',0,1
	.word	387
	.byte	7,1,2,35,0,10
	.byte	'reserved_7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'K3DIV',0,1
	.word	387
	.byte	7,1,2,35,1,10
	.byte	'reserved_15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'K1DIV',0,1
	.word	387
	.byte	7,1,2,35,2,10
	.byte	'reserved_23',0,2
	.word	227
	.byte	9,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLCON1_Bits',0,17,199,5,3
	.word	21050
	.byte	3
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,17,202,5,16,4,10
	.byte	'MODCFG',0,2
	.word	227
	.byte	16,0,2,35,0,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLCON2_Bits',0,17,206,5,3
	.word	21228
	.byte	3
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,17,209,5,16,4,10
	.byte	'VCOBYP',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'VCOPWD',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	2,4,2,35,0,10
	.byte	'SETFINDIS',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'CLRFINDIS',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'OSCDISCDIS',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'reserved_7',0,2
	.word	227
	.byte	2,7,2,35,0,10
	.byte	'NDIV',0,1
	.word	387
	.byte	5,2,2,35,1,10
	.byte	'reserved_14',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'PLLPWD',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'reserved_17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'RESLD',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	387
	.byte	5,0,2,35,2,10
	.byte	'PDIV',0,1
	.word	387
	.byte	4,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	387
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,17,226,5,3
	.word	21328
	.byte	3
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,17,229,5,16,4,10
	.byte	'K2DIV',0,1
	.word	387
	.byte	7,1,2,35,0,10
	.byte	'reserved_7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'K3DIV',0,1
	.word	387
	.byte	4,4,2,35,1,10
	.byte	'reserved_12',0,1
	.word	387
	.byte	4,0,2,35,1,10
	.byte	'K1DIV',0,1
	.word	387
	.byte	7,1,2,35,2,10
	.byte	'reserved_23',0,2
	.word	227
	.byte	9,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,17,237,5,3
	.word	21698
	.byte	3
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,17,240,5,16,4,10
	.byte	'VCOBYST',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PWDSTAT',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'VCOLOCK',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'FINDIS',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'K1RDY',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'K2RDY',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'reserved_6',0,4
	.word	508
	.byte	26,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,17,249,5,3
	.word	21884
	.byte	3
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,17,252,5,16,4,10
	.byte	'VCOBYST',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'VCOLOCK',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'FINDIS',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'K1RDY',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'K2RDY',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'reserved_6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'MODRUN',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	508
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,17,135,6,3
	.word	22082
	.byte	3
	.byte	'_Ifx_SCU_PMCSR_Bits',0,17,138,6,16,4,10
	.byte	'REQSLP',0,1
	.word	387
	.byte	2,6,2,35,0,10
	.byte	'SMUSLP',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,1
	.word	387
	.byte	5,0,2,35,0,10
	.byte	'PMST',0,1
	.word	387
	.byte	3,5,2,35,1,10
	.byte	'reserved_11',0,4
	.word	508
	.byte	21,0,2,35,2,0,17
	.byte	'Ifx_SCU_PMCSR_Bits',0,17,145,6,3
	.word	22315
	.byte	3
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,17,148,6,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'ESR1WKEN',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'PINAWKEN',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'PINBWKEN',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'ESR0DFEN',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'ESR0EDCON',0,1
	.word	387
	.byte	2,1,2,35,0,10
	.byte	'ESR1DFEN',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'ESR1EDCON',0,1
	.word	387
	.byte	2,6,2,35,1,10
	.byte	'PINADFEN',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'PINAEDCON',0,1
	.word	387
	.byte	2,3,2,35,1,10
	.byte	'PINBDFEN',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'PINBEDCON',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'reserved_16',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'STBYRAMSEL',0,1
	.word	387
	.byte	2,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'WUTWKEN',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	387
	.byte	2,1,2,35,2,10
	.byte	'PORSTDF',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'DCDCSYNC',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'reserved_26',0,1
	.word	387
	.byte	3,3,2,35,3,10
	.byte	'ESR0TRIST',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'reserved_30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,17,174,6,3
	.word	22467
	.byte	3
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,17,177,6,16,4,10
	.byte	'reserved_0',0,2
	.word	227
	.byte	12,4,2,35,0,10
	.byte	'IRADIS',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'reserved_13',0,4
	.word	508
	.byte	14,5,2,35,2,10
	.byte	'STBYEVEN',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'STBYEV',0,1
	.word	387
	.byte	3,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,17,185,6,3
	.word	23026
	.byte	3
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,17,188,6,16,4,10
	.byte	'WUTREL',0,4
	.word	508
	.byte	24,8,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	4,4,2,35,3,10
	.byte	'WUTDIV',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'WUTEN',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'WUTMODE',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,17,196,6,3
	.word	23209
	.byte	3
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,17,199,6,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	2,6,2,35,0,10
	.byte	'ESR1WKP',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'ESR1OVRUN',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PINAWKP',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'PINAOVRUN',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'PINBWKP',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PINBOVRUN',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'PORSTDF',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'HWCFGEVR',0,1
	.word	387
	.byte	3,3,2,35,1,10
	.byte	'STBYRAM',0,1
	.word	387
	.byte	2,1,2,35,1,10
	.byte	'reserved_15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'WUTWKP',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'WUTOVRUN',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'WUTWKEN',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'ESR1WKEN',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'PINAWKEN',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'PINBWKEN',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'reserved_23',0,2
	.word	227
	.byte	4,5,2,35,2,10
	.byte	'ESR0TRIST',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'WUTEN',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'WUTMODE',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'WUTRUN',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,17,226,6,3
	.word	23378
	.byte	3
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,17,229,6,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	2,6,2,35,0,10
	.byte	'ESR1WKPCLR',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'ESR1OVRUNCLR',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PINAWKPCLR',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'PINAOVRUNCLR',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'PINBWKPCLR',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PINBOVRUNCLR',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'WUTWKPCLR',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'WUTOVRUNCLR',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,2
	.word	227
	.byte	14,0,2,35,2,0,17
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,17,242,6,3
	.word	23945
	.byte	3
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,17,245,6,16,4,10
	.byte	'WUTCNT',0,4
	.word	508
	.byte	24,8,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	7,1,2,35,3,10
	.byte	'VAL',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,17,250,6,3
	.word	24261
	.byte	3
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,17,253,6,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'CLRC',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,2
	.word	227
	.byte	10,4,2,35,0,10
	.byte	'CSS0',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'CSS1',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'CSS2',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'reserved_15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'USRINFO',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_RSTCON2_Bits',0,17,135,7,3
	.word	24380
	.byte	3
	.byte	'_Ifx_SCU_RSTCON_Bits',0,17,138,7,16,4,10
	.byte	'ESR0',0,1
	.word	387
	.byte	2,6,2,35,0,10
	.byte	'ESR1',0,1
	.word	387
	.byte	2,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	387
	.byte	2,2,2,35,0,10
	.byte	'SMU',0,1
	.word	387
	.byte	2,0,2,35,0,10
	.byte	'SW',0,1
	.word	387
	.byte	2,6,2,35,1,10
	.byte	'STM0',0,1
	.word	387
	.byte	2,4,2,35,1,10
	.byte	'STM1',0,1
	.word	387
	.byte	2,2,2,35,1,10
	.byte	'STM2',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_RSTCON_Bits',0,17,149,7,3
	.word	24589
	.byte	3
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,17,152,7,16,4,10
	.byte	'ESR0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'ESR1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'SMU',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'SW',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'STM0',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'STM1',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'STM2',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'PORST',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'reserved_17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'CB0',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'CB1',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'CB3',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	387
	.byte	2,1,2,35,2,10
	.byte	'EVR13',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'EVR33',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'SWD',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'reserved_26',0,1
	.word	387
	.byte	2,4,2,35,3,10
	.byte	'STBYR',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'reserved_29',0,1
	.word	387
	.byte	3,0,2,35,3,0,17
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,17,175,7,3
	.word	24800
	.byte	3
	.byte	'_Ifx_SCU_SAFECON_Bits',0,17,178,7,16,4,10
	.byte	'HBT',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	508
	.byte	31,0,2,35,2,0,17
	.byte	'Ifx_SCU_SAFECON_Bits',0,17,182,7,3
	.word	25232
	.byte	3
	.byte	'_Ifx_SCU_STSTAT_Bits',0,17,185,7,16,4,10
	.byte	'HWCFG',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'FTM',0,1
	.word	387
	.byte	7,1,2,35,1,10
	.byte	'MODE',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'FCBAE',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'LUDIS',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'TRSTL',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'SPDEN',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	387
	.byte	3,0,2,35,2,10
	.byte	'RAMINT',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'reserved_25',0,1
	.word	387
	.byte	7,0,2,35,3,0,17
	.byte	'Ifx_SCU_STSTAT_Bits',0,17,198,7,3
	.word	25328
	.byte	3
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,17,201,7,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'SWRSTREQ',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	508
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,17,206,7,3
	.word	25588
	.byte	3
	.byte	'_Ifx_SCU_SYSCON_Bits',0,17,209,7,16,4,10
	.byte	'CCTRIG0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'RAMINTM',0,1
	.word	387
	.byte	2,4,2,35,0,10
	.byte	'SETLUDIS',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'reserved_5',0,1
	.word	387
	.byte	3,0,2,35,0,10
	.byte	'DATM',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'reserved_9',0,4
	.word	508
	.byte	23,0,2,35,2,0,17
	.byte	'Ifx_SCU_SYSCON_Bits',0,17,218,7,3
	.word	25713
	.byte	3
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,17,221,7,16,4,10
	.byte	'ESR0T',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'ESR1T',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'SMUT',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	508
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,17,228,7,3
	.word	25910
	.byte	3
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,17,231,7,16,4,10
	.byte	'ESR0T',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'ESR1T',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'SMUT',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	508
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,17,238,7,3
	.word	26063
	.byte	3
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,17,241,7,16,4,10
	.byte	'ESR0T',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'ESR1T',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'SMUT',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	508
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_SCU_TRAPSET_Bits',0,17,248,7,3
	.word	26216
	.byte	3
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,17,251,7,16,4,10
	.byte	'ESR0T',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'ESR1T',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'SMUT',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	508
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,17,130,8,3
	.word	26369
	.byte	3
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,17,133,8,16,4,4
	.byte	'unsigned int',0,4,7,10
	.byte	'ENDINIT',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'LCK',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'PW',0,4
	.word	26556
	.byte	14,16,2,35,0,10
	.byte	'REL',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,17,139,8,3
	.word	26524
	.byte	3
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,17,142,8,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	2,6,2,35,0,10
	.byte	'IR0',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'DR',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'IR1',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'UR',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PAR',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'TCR',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'TCTR',0,1
	.word	387
	.byte	7,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,17,154,8,3
	.word	26670
	.byte	3
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,17,157,8,16,4,10
	.byte	'AE',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'OE',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'IS0',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'DS',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'TO',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'IS1',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'US',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PAS',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'TCS',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'TCT',0,1
	.word	387
	.byte	7,0,2,35,1,10
	.byte	'TIM',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,17,170,8,3
	.word	26908
	.byte	3
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,17,173,8,16,4,10
	.byte	'ENDINIT',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'LCK',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'PW',0,4
	.word	26556
	.byte	14,16,2,35,0,10
	.byte	'REL',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,17,179,8,3
	.word	27131
	.byte	3
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,17,182,8,16,4,10
	.byte	'CLRIRF',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'IR0',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'DR',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'IR1',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'UR',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PAR',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'TCR',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'TCTR',0,1
	.word	387
	.byte	7,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,17,195,8,3
	.word	27257
	.byte	3
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,17,198,8,16,4,10
	.byte	'AE',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'OE',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'IS0',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'DS',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'TO',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'IS1',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'US',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PAS',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'TCS',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'TCT',0,1
	.word	387
	.byte	7,0,2,35,1,10
	.byte	'TIM',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,17,211,8,3
	.word	27509
	.byte	27,17,219,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,4
	.byte	'int',0,4,5,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	10788
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ACCEN0',0,17,224,8,3
	.word	27728
	.byte	27,17,227,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	11345
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ACCEN1',0,17,232,8,3
	.word	27799
	.byte	27,17,235,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	11422
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ARSTDIS',0,17,240,8,3
	.word	27863
	.byte	27,17,243,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	11558
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON0',0,17,248,8,3
	.word	27928
	.byte	27,17,251,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	11840
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON1',0,17,128,9,3
	.word	27993
	.byte	27,17,131,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	12078
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON2',0,17,136,9,3
	.word	28058
	.byte	27,17,139,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	12206
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON3',0,17,144,9,3
	.word	28123
	.byte	27,17,147,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	12433
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON4',0,17,152,9,3
	.word	28188
	.byte	27,17,155,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	12652
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON5',0,17,160,9,3
	.word	28253
	.byte	27,17,163,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	12780
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON6',0,17,168,9,3
	.word	28318
	.byte	27,17,171,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	12880
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CHIPID',0,17,176,9,3
	.word	28383
	.byte	27,17,179,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	13088
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_DTSCON',0,17,184,9,3
	.word	28447
	.byte	27,17,187,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	13253
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_DTSLIM',0,17,192,9,3
	.word	28511
	.byte	27,17,195,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	13436
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_DTSSTAT',0,17,200,9,3
	.word	28575
	.byte	27,17,203,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	13590
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EICR',0,17,208,9,3
	.word	28640
	.byte	27,17,211,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	13954
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EIFR',0,17,216,9,3
	.word	28702
	.byte	27,17,219,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	14165
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EMSR',0,17,224,9,3
	.word	28764
	.byte	27,17,227,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	14417
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ESRCFG',0,17,232,9,3
	.word	28826
	.byte	27,17,235,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	14535
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ESROCFG',0,17,240,9,3
	.word	28890
	.byte	27,17,243,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	14646
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVR13CON',0,17,248,9,3
	.word	28955
	.byte	27,17,251,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	14809
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRADCSTAT',0,17,128,10,3
	.word	29021
	.byte	27,17,131,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	14971
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRMONCTRL',0,17,136,10,3
	.word	29089
	.byte	27,17,139,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	15249
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVROVMON',0,17,144,10,3
	.word	29157
	.byte	27,17,147,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	15428
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRRSTCON',0,17,152,10,3
	.word	29223
	.byte	27,17,155,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	15588
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,17,160,10,3
	.word	29290
	.byte	27,17,163,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	15749
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSDCTRL1',0,17,168,10,3
	.word	29359
	.byte	27,17,171,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	15941
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSDCTRL2',0,17,176,10,3
	.word	29427
	.byte	27,17,179,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	16237
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSDCTRL3',0,17,184,10,3
	.word	29495
	.byte	27,17,187,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	16452
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSTAT',0,17,192,10,3
	.word	29563
	.byte	27,17,195,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	16741
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRUVMON',0,17,200,10,3
	.word	29628
	.byte	27,17,203,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	16920
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EXTCON',0,17,208,10,3
	.word	29694
	.byte	27,17,211,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	17138
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_FDR',0,17,216,10,3
	.word	29758
	.byte	27,17,219,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	17301
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_FMR',0,17,224,10,3
	.word	29819
	.byte	27,17,227,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	17637
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ID',0,17,232,10,3
	.word	29880
	.byte	27,17,235,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	17744
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_IGCR',0,17,240,10,3
	.word	29940
	.byte	27,17,243,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	18196
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_IN',0,17,248,10,3
	.word	30002
	.byte	27,17,251,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	18295
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_IOCR',0,17,128,11,3
	.word	30062
	.byte	27,17,131,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	18445
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LBISTCTRL0',0,17,136,11,3
	.word	30124
	.byte	27,17,139,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	18594
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LBISTCTRL1',0,17,144,11,3
	.word	30192
	.byte	27,17,147,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	18755
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LBISTCTRL2',0,17,152,11,3
	.word	30260
	.byte	27,17,155,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	18885
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LCLCON0',0,17,160,11,3
	.word	30328
	.byte	27,17,163,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	19019
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LCLTEST',0,17,168,11,3
	.word	30393
	.byte	27,17,171,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	19134
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_MANID',0,17,176,11,3
	.word	30458
	.byte	27,17,179,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	19245
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OMR',0,17,184,11,3
	.word	30521
	.byte	27,17,187,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	19403
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OSCCON',0,17,192,11,3
	.word	30582
	.byte	27,17,195,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	19743
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OUT',0,17,200,11,3
	.word	30646
	.byte	27,17,203,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	19844
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OVCCON',0,17,208,11,3
	.word	30707
	.byte	27,17,211,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	20111
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OVCENABLE',0,17,216,11,3
	.word	30771
	.byte	27,17,219,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	20247
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PDISC',0,17,224,11,3
	.word	30838
	.byte	27,17,227,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	20358
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PDR',0,17,232,11,3
	.word	30901
	.byte	27,17,235,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	20491
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PDRR',0,17,240,11,3
	.word	30962
	.byte	27,17,243,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	20694
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLCON0',0,17,248,11,3
	.word	31024
	.byte	27,17,251,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	21050
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLCON1',0,17,128,12,3
	.word	31089
	.byte	27,17,131,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	21228
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLCON2',0,17,136,12,3
	.word	31154
	.byte	27,17,139,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	21328
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLERAYCON0',0,17,144,12,3
	.word	31219
	.byte	27,17,147,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	21698
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLERAYCON1',0,17,152,12,3
	.word	31288
	.byte	27,17,155,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	21884
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLERAYSTAT',0,17,160,12,3
	.word	31357
	.byte	27,17,163,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	22082
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLSTAT',0,17,168,12,3
	.word	31426
	.byte	27,17,171,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	22315
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMCSR',0,17,176,12,3
	.word	31491
	.byte	27,17,179,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	22467
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWCR0',0,17,184,12,3
	.word	31554
	.byte	27,17,187,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	23026
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWCR1',0,17,192,12,3
	.word	31619
	.byte	27,17,195,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	23209
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWCR3',0,17,200,12,3
	.word	31684
	.byte	27,17,203,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	23378
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWSTAT',0,17,208,12,3
	.word	31749
	.byte	27,17,211,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	23945
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWSTATCLR',0,17,216,12,3
	.word	31815
	.byte	27,17,219,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	24261
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWUTCNT',0,17,224,12,3
	.word	31884
	.byte	27,17,227,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	24589
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_RSTCON',0,17,232,12,3
	.word	31951
	.byte	27,17,235,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	24380
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_RSTCON2',0,17,240,12,3
	.word	32015
	.byte	27,17,243,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	24800
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_RSTSTAT',0,17,248,12,3
	.word	32080
	.byte	27,17,251,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	25232
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_SAFECON',0,17,128,13,3
	.word	32145
	.byte	27,17,131,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	25328
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_STSTAT',0,17,136,13,3
	.word	32210
	.byte	27,17,139,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	25588
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_SWRSTCON',0,17,144,13,3
	.word	32274
	.byte	27,17,147,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	25713
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_SYSCON',0,17,152,13,3
	.word	32340
	.byte	27,17,155,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	25910
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_TRAPCLR',0,17,160,13,3
	.word	32404
	.byte	27,17,163,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	26063
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_TRAPDIS',0,17,168,13,3
	.word	32469
	.byte	27,17,171,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	26216
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_TRAPSET',0,17,176,13,3
	.word	32534
	.byte	27,17,179,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	26369
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_TRAPSTAT',0,17,184,13,3
	.word	32599
	.byte	27,17,187,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	26524
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTCPU_CON0',0,17,192,13,3
	.word	32665
	.byte	27,17,195,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	26670
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTCPU_CON1',0,17,200,13,3
	.word	32734
	.byte	27,17,203,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	26908
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTCPU_SR',0,17,208,13,3
	.word	32803
	.byte	27,17,211,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	27131
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTS_CON0',0,17,216,13,3
	.word	32870
	.byte	27,17,219,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	27257
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTS_CON1',0,17,224,13,3
	.word	32937
	.byte	27,17,227,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	27509
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTS_SR',0,17,232,13,3
	.word	33004
	.byte	3
	.byte	'_Ifx_SCU_WDTCPU',0,17,243,13,25,12,6
	.byte	'CON0',0,4
	.word	32665
	.byte	2,35,0,6
	.byte	'CON1',0,4
	.word	32734
	.byte	2,35,4,6
	.byte	'SR',0,4
	.word	32803
	.byte	2,35,8,0,28
	.word	33069
	.byte	17
	.byte	'Ifx_SCU_WDTCPU',0,17,248,13,3
	.word	33132
	.byte	3
	.byte	'_Ifx_SCU_WDTS',0,17,251,13,25,12,6
	.byte	'CON0',0,4
	.word	32870
	.byte	2,35,0,6
	.byte	'CON1',0,4
	.word	32937
	.byte	2,35,4,6
	.byte	'SR',0,4
	.word	33004
	.byte	2,35,8,0,28
	.word	33161
	.byte	17
	.byte	'Ifx_SCU_WDTS',0,17,128,14,3
	.word	33222
	.byte	3
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,18,45,16,4,10
	.byte	'EN0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'EN1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'EN2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'EN3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'EN4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'EN5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'EN6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'EN7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'EN8',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'EN9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'EN10',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'EN11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'EN12',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'EN13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'EN14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'EN15',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'EN16',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'EN17',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'EN18',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'EN19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'EN20',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'EN21',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'EN22',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'EN23',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'EN24',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'EN25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'EN26',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'EN27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'EN28',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'EN29',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'EN30',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'EN31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_ACCEN0_Bits',0,18,79,3
	.word	33249
	.byte	3
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,18,82,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_GTM_ACCEN1_Bits',0,18,85,3
	.word	33806
	.byte	3
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,18,88,16,4,10
	.byte	'SEL0',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'SEL1',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'SEL2',0,4
	.word	26556
	.byte	4,20,2,35,0,10
	.byte	'SEL3',0,4
	.word	26556
	.byte	4,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,18,95,3
	.word	33883
	.byte	3
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,18,98,16,4,10
	.byte	'SEL0',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'SEL1',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'SEL2',0,4
	.word	26556
	.byte	4,20,2,35,0,10
	.byte	'SEL3',0,4
	.word	26556
	.byte	4,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,18,105,3
	.word	34037
	.byte	3
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,18,108,16,4,10
	.byte	'TO_ADDR',0,4
	.word	26556
	.byte	20,12,2,35,0,10
	.byte	'TO_W1R0',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'reserved_21',0,4
	.word	26556
	.byte	11,0,2,35,0,0,17
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,18,113,3
	.word	34191
	.byte	3
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,18,116,16,4,10
	.byte	'BRG_MODE',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'MSK_WR_RSP',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	6,24,2,35,0,10
	.byte	'MODE_UP_PGR',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'BUFF_OVL',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'SYNC_INPUT_REG',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'reserved_13',0,4
	.word	26556
	.byte	3,16,2,35,0,10
	.byte	'BRG_RST',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'reserved_17',0,4
	.word	26556
	.byte	7,8,2,35,0,10
	.byte	'BUFF_DPT',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,18,129,1,3
	.word	34319
	.byte	3
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,18,132,1,16,4,10
	.byte	'NEW_TRAN_PTR',0,4
	.word	26556
	.byte	5,27,2,35,0,10
	.byte	'FIRST_RSP_PTR',0,4
	.word	26556
	.byte	5,22,2,35,0,10
	.byte	'TRAN_IN_PGR',0,4
	.word	26556
	.byte	5,17,2,35,0,10
	.byte	'ABT_TRAN_PGR',0,4
	.word	26556
	.byte	5,12,2,35,0,10
	.byte	'FBC',0,4
	.word	26556
	.byte	6,6,2,35,0,10
	.byte	'RSP_TRAN_RDY',0,4
	.word	26556
	.byte	6,0,2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,18,140,1,3
	.word	34626
	.byte	3
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,18,143,1,16,4,10
	.byte	'TRAN_IN_PGR2',0,4
	.word	26556
	.byte	5,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,18,147,1,3
	.word	34828
	.byte	3
	.byte	'_Ifx_GTM_CLC_Bits',0,18,150,1,16,4,10
	.byte	'DISR',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'DISS',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'EDIS',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_CLC_Bits',0,18,157,1,3
	.word	34941
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,18,160,1,16,4,10
	.byte	'CLK_CNT',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,18,164,1,3
	.word	35084
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,18,167,1,16,4,10
	.byte	'CLK_CNT',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'CLK6_SEL',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'reserved_25',0,4
	.word	26556
	.byte	7,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,18,172,1,3
	.word	35201
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,18,175,1,16,4,10
	.byte	'CLK_CNT',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'CLK7_SEL',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'reserved_25',0,4
	.word	26556
	.byte	7,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,18,180,1,3
	.word	35336
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,18,183,1,16,4,10
	.byte	'EN_CLK0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'EN_CLK1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'EN_CLK2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'EN_CLK3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'EN_CLK4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'EN_CLK5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'EN_CLK6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'EN_CLK7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'EN_ECLK0',0,4
	.word	26556
	.byte	2,14,2,35,0,10
	.byte	'EN_ECLK1',0,4
	.word	26556
	.byte	2,12,2,35,0,10
	.byte	'EN_ECLK2',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'EN_FXCLK',0,4
	.word	26556
	.byte	2,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,18,198,1,3
	.word	35471
	.byte	3
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,18,201,1,16,4,10
	.byte	'ECLK_DEN',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,18,205,1,3
	.word	35791
	.byte	3
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,18,208,1,16,4,10
	.byte	'ECLK_NUM',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,18,212,1,3
	.word	35903
	.byte	3
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,18,215,1,16,4,10
	.byte	'FXCLK_SEL',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,18,219,1,3
	.word	36015
	.byte	3
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,18,222,1,16,4,10
	.byte	'GCLK_DEN',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,18,226,1,3
	.word	36131
	.byte	3
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,18,229,1,16,4,10
	.byte	'GCLK_NUM',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,18,233,1,3
	.word	36243
	.byte	3
	.byte	'_Ifx_GTM_CTRL_Bits',0,18,236,1,16,4,10
	.byte	'RF_PROT',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TO_MODE',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'TO_VAL',0,4
	.word	26556
	.byte	5,23,2,35,0,10
	.byte	'reserved_9',0,4
	.word	26556
	.byte	23,0,2,35,0,0,17
	.byte	'Ifx_GTM_CTRL_Bits',0,18,243,1,3
	.word	36355
	.byte	3
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,18,246,1,16,4,10
	.byte	'O1SEL_0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	2,29,2,35,0,10
	.byte	'SWAP_0',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'O1F_0',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'reserved_6',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'O1SEL_1',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'I1SEL_1',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'SH_EN_1',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'SWAP_1',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'O1F_1',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'reserved_14',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'O1SEL_2',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'I1SEL_2',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'SH_EN_2',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'SWAP_2',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'O1F_2',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'reserved_22',0,4
	.word	26556
	.byte	2,8,2,35,0,10
	.byte	'O1SEL_3',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'I1SEL_3',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'SH_EN_3',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'SWAP_3',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'O1F_3',0,4
	.word	26556
	.byte	2,2,2,35,0,10
	.byte	'reserved_30',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,18,143,2,3
	.word	36508
	.byte	3
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,18,146,2,16,4,10
	.byte	'POL0_0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'OC0_0',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'SL0_0',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'DT0_0',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'POL1_0',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'OC1_0',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'SL1_0',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'DT1_0',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'POL0_1',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'OC0_1',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'SL0_1',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'DT0_1',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'POL1_1',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'OC1_1',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'SL1_1',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'DT1_1',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'POL0_2',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'OC0_2',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'SL0_2',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'DT0_2',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'POL1_2',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'OC1_2',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'SL1_2',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'DT1_2',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'POL0_3',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'OC0_3',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'SL0_3',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'DT0_3',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'POL1_3',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'OC1_3',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'SL1_3',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'DT1_3',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,18,180,2,3
	.word	37020
	.byte	3
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,18,183,2,16,4,10
	.byte	'POL0_0_SR',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'OC0_0_SR',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'SL0_0_SR',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'DT0_0_SR',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'POL1_0_SR',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'OC1_0_SR',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'SL1_0_SR',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'DT1_0_SR',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'POL0_1_SR',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'OC0_1_SR',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'SL0_1_SR',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'DT0_1_SR',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'POL1_1_SR',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'OC1_1_SR',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'SL1_1_SR',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'DT1_1_SR',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'POL0_2_SR',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'OC0_2_SR',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'SL0_2_SR',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'DT0_2_SR',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'POL1_2_SR',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'OC1_2_SR',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'SL1_2_SR',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'DT1_2_SR',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'POL0_3_SR',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'OC0_3_SR',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'SL0_3_SR',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'DT0_3_SR',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'POL1_3_SR',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'OC1_3_SR',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'SL1_3_SR',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'DT1_3_SR',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,18,217,2,3
	.word	37641
	.byte	3
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,18,220,2,16,4,10
	.byte	'CLK_SEL',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'UPD_MODE',0,4
	.word	26556
	.byte	3,25,2,35,0,10
	.byte	'reserved_7',0,4
	.word	26556
	.byte	25,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,18,226,2,3
	.word	38364
	.byte	3
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,18,229,2,16,4,10
	.byte	'RELRISE',0,4
	.word	26556
	.byte	10,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	26556
	.byte	6,16,2,35,0,10
	.byte	'RELFALL',0,4
	.word	26556
	.byte	10,6,2,35,0,10
	.byte	'reserved_26',0,4
	.word	26556
	.byte	6,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,18,235,2,3
	.word	38508
	.byte	3
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,18,238,2,16,4,10
	.byte	'RELBLK',0,4
	.word	26556
	.byte	10,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	26556
	.byte	6,16,2,35,0,10
	.byte	'PSU_IN_SEL',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'IN_POL',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'reserved_18',0,4
	.word	26556
	.byte	2,12,2,35,0,10
	.byte	'SHIFT_SEL',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'reserved_22',0,4
	.word	26556
	.byte	10,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,18,247,2,3
	.word	38657
	.byte	3
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,18,250,2,16,4,10
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,18,129,3,3
	.word	38872
	.byte	3
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,18,132,3,16,4,10
	.byte	'GRSTEN',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'BRIDGE_MODE_RST',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'AEI_IN',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	5,24,2,35,0,10
	.byte	'TOM_OUT_RST',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	26556
	.byte	3,20,2,35,0,10
	.byte	'reserved_12',0,4
	.word	26556
	.byte	4,16,2,35,0,10
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'IRQ_MODE_PULSE',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	26556
	.byte	12,0,2,35,0,0,17
	.byte	'Ifx_GTM_HW_CONF_Bits',0,18,146,3,3
	.word	39076
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,18,149,3,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'AEI_IRQ',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,18,154,3,3
	.word	39433
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,18,157,3,16,4,10
	.byte	'TIM0_CH0_IRQ',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TIM0_CH1_IRQ',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'TIM0_CH2_IRQ',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'TIM0_CH3_IRQ',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'TIM0_CH4_IRQ',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'TIM0_CH5_IRQ',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'TIM0_CH6_IRQ',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'TIM0_CH7_IRQ',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,18,168,3,3
	.word	39561
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,18,171,3,16,4,10
	.byte	'TOM0_CH0_IRQ',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TOM0_CH1_IRQ',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'TOM0_CH2_IRQ',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'TOM0_CH3_IRQ',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'TOM0_CH4_IRQ',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'TOM0_CH5_IRQ',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'TOM0_CH6_IRQ',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'TOM0_CH7_IRQ',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'TOM0_CH8_IRQ',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'TOM0_CH9_IRQ',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'TOM0_CH10_IRQ',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'TOM0_CH11_IRQ',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'TOM0_CH12_IRQ',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'TOM0_CH13_IRQ',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'TOM0_CH14_IRQ',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'TOM0_CH15_IRQ',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'TOM1_CH0_IRQ',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'TOM1_CH1_IRQ',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'TOM1_CH2_IRQ',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'TOM1_CH3_IRQ',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'TOM1_CH4_IRQ',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'TOM1_CH5_IRQ',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'TOM1_CH6_IRQ',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'TOM1_CH7_IRQ',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'TOM1_CH8_IRQ',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'TOM1_CH9_IRQ',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'TOM1_CH10_IRQ',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'TOM1_CH11_IRQ',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'TOM1_CH12_IRQ',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'TOM1_CH13_IRQ',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'TOM1_CH14_IRQ',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'TOM1_CH15_IRQ',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,18,205,3,3
	.word	39840
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,18,208,3,16,4,10
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,18,219,3,3
	.word	40685
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,18,222,3,16,4,10
	.byte	'GTM_EIRQ',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	3,28,2,35,0,10
	.byte	'TIM0_EIRQ',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,18,228,3,3
	.word	40978
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,18,231,3,16,4,10
	.byte	'SEL0',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'SEL1',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'SEL2',0,4
	.word	26556
	.byte	4,20,2,35,0,10
	.byte	'SEL3',0,4
	.word	26556
	.byte	4,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,18,238,3,3
	.word	41132
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,18,241,3,16,4,10
	.byte	'SEL0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'SEL1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'SEL2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'SEL3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'SEL4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'SEL5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'SEL6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'SEL7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'SEL8',0,4
	.word	26556
	.byte	2,14,2,35,0,10
	.byte	'SEL9',0,4
	.word	26556
	.byte	2,12,2,35,0,10
	.byte	'SEL10',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'SEL11',0,4
	.word	26556
	.byte	2,8,2,35,0,10
	.byte	'SEL12',0,4
	.word	26556
	.byte	2,6,2,35,0,10
	.byte	'SEL13',0,4
	.word	26556
	.byte	2,4,2,35,0,10
	.byte	'SEL14',0,4
	.word	26556
	.byte	2,2,2,35,0,10
	.byte	'SEL15',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,18,131,4,3
	.word	41302
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,18,134,4,16,4,10
	.byte	'CH0SEL',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'CH1SEL',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'CH2SEL',0,4
	.word	26556
	.byte	4,20,2,35,0,10
	.byte	'CH3SEL',0,4
	.word	26556
	.byte	4,16,2,35,0,10
	.byte	'CH4SEL',0,4
	.word	26556
	.byte	4,12,2,35,0,10
	.byte	'CH5SEL',0,4
	.word	26556
	.byte	4,8,2,35,0,10
	.byte	'CH6SEL',0,4
	.word	26556
	.byte	4,4,2,35,0,10
	.byte	'CH7SEL',0,4
	.word	26556
	.byte	4,0,2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,18,144,4,3
	.word	41643
	.byte	3
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,18,147,4,16,4,10
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,18,154,4,3
	.word	41868
	.byte	3
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,18,157,4,16,4,10
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'TRG_AEI_USP_BE',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,18,164,4,3
	.word	42066
	.byte	3
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,18,167,4,16,4,10
	.byte	'IRQ_MODE',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,18,171,4,3
	.word	42262
	.byte	3
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,18,174,4,16,4,10
	.byte	'AEI_TO_XPT',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'AEI_USP_ADDR',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'AEI_IM_ADDR',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'AEI_USP_BE',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,18,181,4,3
	.word	42365
	.byte	3
	.byte	'_Ifx_GTM_KRST0_Bits',0,18,184,4,16,4,10
	.byte	'RST',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'RSTSTAT',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_KRST0_Bits',0,18,189,4,3
	.word	42543
	.byte	3
	.byte	'_Ifx_GTM_KRST1_Bits',0,18,192,4,16,4,10
	.byte	'RST',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_GTM_KRST1_Bits',0,18,196,4,3
	.word	42654
	.byte	3
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,18,199,4,16,4,10
	.byte	'CLR',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,18,203,4,3
	.word	42746
	.byte	3
	.byte	'_Ifx_GTM_OCS_Bits',0,18,206,4,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'SUS',0,4
	.word	26556
	.byte	4,4,2,35,0,10
	.byte	'SUS_P',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'SUSSTA',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'reserved_30',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_OCS_Bits',0,18,213,4,3
	.word	42842
	.byte	3
	.byte	'_Ifx_GTM_ODA_Bits',0,18,216,4,16,4,10
	.byte	'DDREN',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'DREN',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_ODA_Bits',0,18,221,4,3
	.word	42988
	.byte	3
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,18,224,4,16,4,10
	.byte	'CV',0,4
	.word	26556
	.byte	27,5,2,35,0,10
	.byte	'reserved_27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'CM',0,4
	.word	26556
	.byte	2,2,2,35,0,10
	.byte	'reserved_30',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTBU0T_Bits',0,18,230,4,3
	.word	43094
	.byte	3
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,18,233,4,16,4,10
	.byte	'CV',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	4,4,2,35,0,10
	.byte	'EN',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'reserved_29',0,4
	.word	26556
	.byte	3,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTBU1T_Bits',0,18,239,4,3
	.word	43225
	.byte	3
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,18,242,4,16,4,10
	.byte	'CV',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	4,4,2,35,0,10
	.byte	'EN',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'reserved_29',0,4
	.word	26556
	.byte	3,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTBU2T_Bits',0,18,248,4,3
	.word	43356
	.byte	3
	.byte	'_Ifx_GTM_OTSC0_Bits',0,18,251,4,16,4,10
	.byte	'B0LMT',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'B0LMI',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'B0HMT',0,4
	.word	26556
	.byte	3,21,2,35,0,10
	.byte	'reserved_11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'B0HMI',0,4
	.word	26556
	.byte	4,16,2,35,0,10
	.byte	'B1LMT',0,4
	.word	26556
	.byte	3,13,2,35,0,10
	.byte	'reserved_19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'B1LMI',0,4
	.word	26556
	.byte	4,8,2,35,0,10
	.byte	'B1HMT',0,4
	.word	26556
	.byte	3,5,2,35,0,10
	.byte	'reserved_27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'B1HMI',0,4
	.word	26556
	.byte	4,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTSC0_Bits',0,18,137,5,3
	.word	43487
	.byte	3
	.byte	'_Ifx_GTM_OTSS_Bits',0,18,140,5,16,4,10
	.byte	'OTGB0',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'OTGB1',0,4
	.word	26556
	.byte	4,20,2,35,0,10
	.byte	'reserved_12',0,4
	.word	26556
	.byte	4,16,2,35,0,10
	.byte	'OTGB2',0,4
	.word	26556
	.byte	4,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	26556
	.byte	12,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTSS_Bits',0,18,148,5,3
	.word	43769
	.byte	3
	.byte	'_Ifx_GTM_REV_Bits',0,18,151,5,16,4,10
	.byte	'STEP',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'NO',0,4
	.word	26556
	.byte	4,20,2,35,0,10
	.byte	'MINOR',0,4
	.word	26556
	.byte	4,16,2,35,0,10
	.byte	'MAJOR',0,4
	.word	26556
	.byte	4,12,2,35,0,10
	.byte	'DEV_CODE0',0,4
	.word	26556
	.byte	4,8,2,35,0,10
	.byte	'DEV_CODE1',0,4
	.word	26556
	.byte	4,4,2,35,0,10
	.byte	'DEV_CODE2',0,4
	.word	26556
	.byte	4,0,2,35,0,0,17
	.byte	'Ifx_GTM_REV_Bits',0,18,160,5,3
	.word	43941
	.byte	3
	.byte	'_Ifx_GTM_RST_Bits',0,18,163,5,16,4,10
	.byte	'RST',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_GTM_RST_Bits',0,18,167,5,3
	.word	44119
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,18,170,5,16,4,10
	.byte	'BASE',0,4
	.word	26556
	.byte	27,5,2,35,0,10
	.byte	'reserved_27',0,4
	.word	26556
	.byte	5,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,18,174,5,3
	.word	44207
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,18,177,5,16,4,10
	.byte	'LOW_RES',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'CH_CLK_SRC',0,4
	.word	26556
	.byte	3,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,18,182,5,3
	.word	44315
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,18,185,5,16,4,10
	.byte	'BASE',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,18,189,5,3
	.word	44447
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,18,192,5,16,4,10
	.byte	'CH_MODE',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'CH_CLK_SRC',0,4
	.word	26556
	.byte	3,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,18,197,5,3
	.word	44555
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,18,200,5,16,4,10
	.byte	'BASE',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,18,204,5,3
	.word	44687
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,18,207,5,16,4,10
	.byte	'CH_MODE',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'CH_CLK_SRC',0,4
	.word	26556
	.byte	3,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,18,212,5,3
	.word	44795
	.byte	3
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,18,215,5,16,4,10
	.byte	'ENDIS_CH0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'ENDIS_CH1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'ENDIS_CH2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'reserved_6',0,4
	.word	26556
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,18,221,5,3
	.word	44927
	.byte	3
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,18,224,5,16,4,10
	.byte	'SRC_CH0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'SRC_CH1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'SRC_CH2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'SRC_CH3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'SRC_CH4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'SRC_CH5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'SRC_CH6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'SRC_CH7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,18,235,5,3
	.word	45073
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,18,238,5,16,4,10
	.byte	'CNT',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,18,242,5,3
	.word	45320
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,18,245,5,16,4,10
	.byte	'CNTS',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'ECNT',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,18,249,5,3
	.word	45423
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,18,252,5,16,4,10
	.byte	'TIM_EN',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TIM_MODE',0,4
	.word	26556
	.byte	3,28,2,35,0,10
	.byte	'OSM',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'CICTRL',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'TBU0x_SEL',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'GPR0_SEL',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'GPR1_SEL',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'CNTS_SEL',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'DSL',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'ISL',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'ECNT_RESET',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'FLT_EN',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'FLT_CNT_FRQ',0,4
	.word	26556
	.byte	2,13,2,35,0,10
	.byte	'EXT_CAP_EN',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'FLT_MODE_RE',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'FLT_CTR_RE',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'FLT_MODE_FE',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'FLT_CTR_FE',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'CLK_SEL',0,4
	.word	26556
	.byte	3,5,2,35,0,10
	.byte	'FR_ECNT_OFL',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'EGPR0_SEL',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'EGPR1_SEL',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'TOCTRL',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,18,150,6,3
	.word	45522
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,18,153,6,16,4,10
	.byte	'ECNT',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,18,157,6,3
	.word	46070
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,18,160,6,16,4,10
	.byte	'EXT_CAP_SRC',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	29,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,18,164,6,3
	.word	46176
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,18,167,6,16,4,10
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'TODET_EIRQ_EN',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'reserved_6',0,4
	.word	26556
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,18,176,6,3
	.word	46290
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,18,179,6,16,4,10
	.byte	'FLT_FE',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,18,183,6,3
	.word	46545
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,18,186,6,16,4,10
	.byte	'FLT_RE',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,18,190,6,3
	.word	46657
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,18,193,6,16,4,10
	.byte	'GPR0',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'ECNT',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,18,197,6,3
	.word	46769
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,18,200,6,16,4,10
	.byte	'GPR1',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'ECNT',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,18,204,6,3
	.word	46868
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,18,207,6,16,4,10
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'TODET_IRQ_EN',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'reserved_6',0,4
	.word	26556
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,18,216,6,3
	.word	46967
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,18,219,6,16,4,10
	.byte	'TRG_NEWVAL',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TRG_ECNTOFL',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'TRG_CNTOFL',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'TRG_GPRzOFL',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'TRG_TODET',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'TRG_GLITCHDET',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'reserved_6',0,4
	.word	26556
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,18,228,6,3
	.word	47214
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,18,231,6,16,4,10
	.byte	'IRQ_MODE',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,18,235,6,3
	.word	47453
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,18,238,6,16,4,10
	.byte	'NEWVAL',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'ECNTOFL',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'CNTOFL',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'GPRzOFL',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'TODET',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'GLITCHDET',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'reserved_6',0,4
	.word	26556
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,18,247,6,3
	.word	47570
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,18,250,6,16,4,10
	.byte	'TO_CNT',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,18,254,6,3
	.word	47783
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,18,129,7,16,4,10
	.byte	'TOV',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	20,4,2,35,0,10
	.byte	'TCS',0,4
	.word	26556
	.byte	3,1,2,35,0,10
	.byte	'reserved_31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,18,135,7,3
	.word	47890
	.byte	3
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,18,138,7,16,4,10
	.byte	'VAL_0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'MODE_0',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'VAL_1',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'MODE_1',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'VAL_2',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'MODE_2',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'VAL_3',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'MODE_3',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'VAL_4',0,4
	.word	26556
	.byte	2,14,2,35,0,10
	.byte	'MODE_4',0,4
	.word	26556
	.byte	2,12,2,35,0,10
	.byte	'VAL_5',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'MODE_5',0,4
	.word	26556
	.byte	2,8,2,35,0,10
	.byte	'VAL_6',0,4
	.word	26556
	.byte	2,6,2,35,0,10
	.byte	'MODE_6',0,4
	.word	26556
	.byte	2,4,2,35,0,10
	.byte	'VAL_7',0,4
	.word	26556
	.byte	2,2,2,35,0,10
	.byte	'MODE_7',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,18,156,7,3
	.word	48032
	.byte	3
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,18,159,7,16,4,10
	.byte	'F_OUT',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'F_IN',0,4
	.word	26556
	.byte	8,16,2,35,0,10
	.byte	'TIM_IN',0,4
	.word	26556
	.byte	8,8,2,35,0,10
	.byte	'reserved_24',0,4
	.word	26556
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,18,165,7,3
	.word	48377
	.byte	3
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,18,168,7,16,4,10
	.byte	'RST_CH0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'RST_CH1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'RST_CH2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'RST_CH3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'RST_CH4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'RST_CH5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'RST_CH6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'RST_CH7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_RST_Bits',0,18,179,7,3
	.word	48518
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,18,182,7,16,4,10
	.byte	'CM0',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,18,186,7,3
	.word	48751
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,18,189,7,16,4,10
	.byte	'CM1',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,18,193,7,3
	.word	48854
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,18,196,7,16,4,10
	.byte	'CN0',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,18,200,7,3
	.word	48957
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,18,203,7,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	11,21,2,35,0,10
	.byte	'SL',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'CLK_SRC_SR',0,4
	.word	26556
	.byte	3,17,2,35,0,10
	.byte	'reserved_15',0,4
	.word	26556
	.byte	5,12,2,35,0,10
	.byte	'RST_CCU0',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'OSM_TRIG',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'EXT_TRIG',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'EXTTRIGOUT',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'TRIGOUT',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'reserved_25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'OSM',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'BITREV',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'reserved_28',0,4
	.word	26556
	.byte	4,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,18,218,7,3
	.word	49060
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,18,221,7,16,4,10
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,18,226,7,3
	.word	49388
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,18,229,7,16,4,10
	.byte	'TRG_CCU0TC0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TRG_CCU1TC0',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,18,234,7,3
	.word	49531
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,18,237,7,16,4,10
	.byte	'IRQ_MODE',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,18,241,7,3
	.word	49680
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,18,244,7,16,4,10
	.byte	'CCU0TC',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'CCU1TC',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,18,249,7,3
	.word	49797
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,18,252,7,16,4,10
	.byte	'SR0',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,18,128,8,3
	.word	49934
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,18,131,8,16,4,10
	.byte	'SR1',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,18,135,8,3
	.word	50037
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,18,138,8,16,4,10
	.byte	'OL',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,18,142,8,3
	.word	50140
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,18,145,8,16,4,10
	.byte	'ACT_TB',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'TB_TRIG',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'TBU_SEL',0,4
	.word	26556
	.byte	2,5,2,35,0,10
	.byte	'reserved_27',0,4
	.word	26556
	.byte	5,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,18,151,8,3
	.word	50243
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,18,154,8,16,4,10
	.byte	'ENDIS_CTRL0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'ENDIS_CTRL1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'ENDIS_CTRL2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'ENDIS_CTRL3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'ENDIS_CTRL4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'ENDIS_CTRL5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'ENDIS_CTRL6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'ENDIS_CTRL7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,18,165,8,3
	.word	50397
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,18,168,8,16,4,10
	.byte	'ENDIS_STAT0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'ENDIS_STAT1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'ENDIS_STAT2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'ENDIS_STAT3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'ENDIS_STAT4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'ENDIS_STAT5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'ENDIS_STAT6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'ENDIS_STAT7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,18,179,8,3
	.word	50687
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,18,182,8,16,4,10
	.byte	'FUPD_CTRL0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'FUPD_CTRL1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'FUPD_CTRL2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'FUPD_CTRL3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'FUPD_CTRL4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'FUPD_CTRL5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'FUPD_CTRL6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'FUPD_CTRL7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'RSTCN0_CH0',0,4
	.word	26556
	.byte	2,14,2,35,0,10
	.byte	'RSTCN0_CH1',0,4
	.word	26556
	.byte	2,12,2,35,0,10
	.byte	'RSTCN0_CH2',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'RSTCN0_CH3',0,4
	.word	26556
	.byte	2,8,2,35,0,10
	.byte	'RSTCN0_CH4',0,4
	.word	26556
	.byte	2,6,2,35,0,10
	.byte	'RSTCN0_CH5',0,4
	.word	26556
	.byte	2,4,2,35,0,10
	.byte	'RSTCN0_CH6',0,4
	.word	26556
	.byte	2,2,2,35,0,10
	.byte	'RSTCN0_CH7',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,18,200,8,3
	.word	50977
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,18,203,8,16,4,10
	.byte	'HOST_TRIG',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	7,24,2,35,0,10
	.byte	'RST_CH0',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'RST_CH1',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'RST_CH2',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'RST_CH3',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'RST_CH4',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'RST_CH5',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'RST_CH6',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'RST_CH7',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'UPEN_CTRL0',0,4
	.word	26556
	.byte	2,14,2,35,0,10
	.byte	'UPEN_CTRL1',0,4
	.word	26556
	.byte	2,12,2,35,0,10
	.byte	'UPEN_CTRL2',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'UPEN_CTRL3',0,4
	.word	26556
	.byte	2,8,2,35,0,10
	.byte	'UPEN_CTRL4',0,4
	.word	26556
	.byte	2,6,2,35,0,10
	.byte	'UPEN_CTRL5',0,4
	.word	26556
	.byte	2,4,2,35,0,10
	.byte	'UPEN_CTRL6',0,4
	.word	26556
	.byte	2,2,2,35,0,10
	.byte	'UPEN_CTRL7',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,18,223,8,3
	.word	51410
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,18,226,8,16,4,10
	.byte	'INT_TRIG0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'INT_TRIG1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'INT_TRIG2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'INT_TRIG3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'INT_TRIG4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'INT_TRIG5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'INT_TRIG6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'INT_TRIG7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,18,237,8,3
	.word	51860
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,18,240,8,16,4,10
	.byte	'OUTEN_CTRL0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'OUTEN_CTRL1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'OUTEN_CTRL2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'OUTEN_CTRL3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'OUTEN_CTRL4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'OUTEN_CTRL5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'OUTEN_CTRL6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'OUTEN_CTRL7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,18,251,8,3
	.word	52130
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,18,254,8,16,4,10
	.byte	'OUTEN_STAT0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'OUTEN_STAT1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'OUTEN_STAT2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'OUTEN_STAT3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'OUTEN_STAT4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'OUTEN_STAT5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'OUTEN_STAT6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'OUTEN_STAT7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,18,137,9,3
	.word	52420
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,18,140,9,16,4,10
	.byte	'ACT_TB',0,4
	.word	26556
	.byte	24,8,2,35,0,10
	.byte	'TB_TRIG',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'TBU_SEL',0,4
	.word	26556
	.byte	2,5,2,35,0,10
	.byte	'reserved_27',0,4
	.word	26556
	.byte	5,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,18,146,9,3
	.word	52710
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,18,149,9,16,4,10
	.byte	'ENDIS_CTRL0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'ENDIS_CTRL1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'ENDIS_CTRL2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'ENDIS_CTRL3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'ENDIS_CTRL4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'ENDIS_CTRL5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'ENDIS_CTRL6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'ENDIS_CTRL7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,18,160,9,3
	.word	52864
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,18,163,9,16,4,10
	.byte	'ENDIS_STAT0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'ENDIS_STAT1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'ENDIS_STAT2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'ENDIS_STAT3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'ENDIS_STAT4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'ENDIS_STAT5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'ENDIS_STAT6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'ENDIS_STAT7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,18,174,9,3
	.word	53154
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,18,177,9,16,4,10
	.byte	'FUPD_CTRL0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'FUPD_CTRL1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'FUPD_CTRL2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'FUPD_CTRL3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'FUPD_CTRL4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'FUPD_CTRL5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'FUPD_CTRL6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'FUPD_CTRL7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'RSTCN0_CH0',0,4
	.word	26556
	.byte	2,14,2,35,0,10
	.byte	'RSTCN0_CH1',0,4
	.word	26556
	.byte	2,12,2,35,0,10
	.byte	'RSTCN0_CH2',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'RSTCN0_CH3',0,4
	.word	26556
	.byte	2,8,2,35,0,10
	.byte	'RSTCN0_CH4',0,4
	.word	26556
	.byte	2,6,2,35,0,10
	.byte	'RSTCN0_CH5',0,4
	.word	26556
	.byte	2,4,2,35,0,10
	.byte	'RSTCN0_CH6',0,4
	.word	26556
	.byte	2,2,2,35,0,10
	.byte	'RSTCN0_CH7',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,18,195,9,3
	.word	53444
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,18,198,9,16,4,10
	.byte	'HOST_TRIG',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	7,24,2,35,0,10
	.byte	'RST_CH0',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'RST_CH1',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'RST_CH2',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'RST_CH3',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'RST_CH4',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'RST_CH5',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'RST_CH6',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'RST_CH7',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'UPEN_CTRL0',0,4
	.word	26556
	.byte	2,14,2,35,0,10
	.byte	'UPEN_CTRL1',0,4
	.word	26556
	.byte	2,12,2,35,0,10
	.byte	'UPEN_CTRL2',0,4
	.word	26556
	.byte	2,10,2,35,0,10
	.byte	'UPEN_CTRL3',0,4
	.word	26556
	.byte	2,8,2,35,0,10
	.byte	'UPEN_CTRL4',0,4
	.word	26556
	.byte	2,6,2,35,0,10
	.byte	'UPEN_CTRL5',0,4
	.word	26556
	.byte	2,4,2,35,0,10
	.byte	'UPEN_CTRL6',0,4
	.word	26556
	.byte	2,2,2,35,0,10
	.byte	'UPEN_CTRL7',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,18,218,9,3
	.word	53877
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,18,221,9,16,4,10
	.byte	'INT_TRIG0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'INT_TRIG1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'INT_TRIG2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'INT_TRIG3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'INT_TRIG4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'INT_TRIG5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'INT_TRIG6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'INT_TRIG7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,18,232,9,3
	.word	54327
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,18,235,9,16,4,10
	.byte	'OUTEN_CTRL0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'OUTEN_CTRL1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'OUTEN_CTRL2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'OUTEN_CTRL3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'OUTEN_CTRL4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'OUTEN_CTRL5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'OUTEN_CTRL6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'OUTEN_CTRL7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,18,246,9,3
	.word	54597
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,18,249,9,16,4,10
	.byte	'OUTEN_STAT0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'OUTEN_STAT1',0,4
	.word	26556
	.byte	2,28,2,35,0,10
	.byte	'OUTEN_STAT2',0,4
	.word	26556
	.byte	2,26,2,35,0,10
	.byte	'OUTEN_STAT3',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'OUTEN_STAT4',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'OUTEN_STAT5',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'OUTEN_STAT6',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'OUTEN_STAT7',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,18,132,10,3
	.word	54887
	.byte	27,18,140,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	33249
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ACCEN0',0,18,145,10,3
	.word	55177
	.byte	27,18,148,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	33806
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ACCEN1',0,18,153,10,3
	.word	55241
	.byte	27,18,156,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	33883
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,18,161,10,3
	.word	55305
	.byte	27,18,164,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	34037
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,18,169,10,3
	.word	55375
	.byte	27,18,172,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	34191
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,18,177,10,3
	.word	55445
	.byte	27,18,180,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	34319
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_MODE',0,18,185,10,3
	.word	55515
	.byte	27,18,188,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	34626
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,18,193,10,3
	.word	55584
	.byte	27,18,196,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	34828
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,18,201,10,3
	.word	55653
	.byte	27,18,204,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	34941
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CLC',0,18,209,10,3
	.word	55722
	.byte	27,18,212,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	35084
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,18,217,10,3
	.word	55783
	.byte	27,18,220,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	35201
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,18,225,10,3
	.word	55856
	.byte	27,18,228,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	35336
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,18,233,10,3
	.word	55928
	.byte	27,18,236,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	35471
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_EN',0,18,241,10,3
	.word	56000
	.byte	27,18,244,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	35791
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,18,249,10,3
	.word	56068
	.byte	27,18,252,10,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	35903
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,18,129,11,3
	.word	56138
	.byte	27,18,132,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	36015
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,18,137,11,3
	.word	56208
	.byte	27,18,140,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	36131
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,18,145,11,3
	.word	56280
	.byte	27,18,148,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	36243
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,18,153,11,3
	.word	56350
	.byte	27,18,156,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	36355
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CTRL',0,18,161,11,3
	.word	56420
	.byte	27,18,164,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	36508
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,18,169,11,3
	.word	56482
	.byte	27,18,172,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	37020
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,18,177,11,3
	.word	56552
	.byte	27,18,180,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	37641
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,18,185,11,3
	.word	56622
	.byte	27,18,188,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	38364
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CTRL',0,18,193,11,3
	.word	56695
	.byte	27,18,196,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	38508
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_DTV_CH',0,18,201,11,3
	.word	56761
	.byte	27,18,204,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	38657
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,18,209,11,3
	.word	56829
	.byte	27,18,212,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	38872
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_EIRQ_EN',0,18,217,11,3
	.word	56898
	.byte	27,18,220,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	39076
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_HW_CONF',0,18,225,11,3
	.word	56963
	.byte	27,18,228,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	39433
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_0',0,18,233,11,3
	.word	57028
	.byte	27,18,236,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	39561
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_2',0,18,241,11,3
	.word	57096
	.byte	27,18,244,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	39840
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_6',0,18,249,11,3
	.word	57164
	.byte	27,18,252,11,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	40685
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,18,129,12,3
	.word	57232
	.byte	27,18,132,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	40978
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,18,137,12,3
	.word	57303
	.byte	27,18,140,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	41132
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,18,145,12,3
	.word	57373
	.byte	27,18,148,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	41302
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,18,153,12,3
	.word	57450
	.byte	27,18,156,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	41643
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,18,161,12,3
	.word	57525
	.byte	27,18,164,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	41868
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_EN',0,18,169,12,3
	.word	57601
	.byte	27,18,172,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	42066
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_FORCINT',0,18,177,12,3
	.word	57665
	.byte	27,18,180,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	42262
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_MODE',0,18,185,12,3
	.word	57734
	.byte	27,18,188,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	42365
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,18,193,12,3
	.word	57800
	.byte	27,18,196,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	42543
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_KRST0',0,18,201,12,3
	.word	57868
	.byte	27,18,204,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	42654
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_KRST1',0,18,209,12,3
	.word	57931
	.byte	27,18,212,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	42746
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_KRSTCLR',0,18,217,12,3
	.word	57994
	.byte	27,18,220,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	42842
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OCS',0,18,225,12,3
	.word	58059
	.byte	27,18,228,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	42988
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ODA',0,18,233,12,3
	.word	58120
	.byte	27,18,236,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	43094
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTBU0T',0,18,241,12,3
	.word	58181
	.byte	27,18,244,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	43225
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTBU1T',0,18,249,12,3
	.word	58245
	.byte	27,18,252,12,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	43356
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTBU2T',0,18,129,13,3
	.word	58309
	.byte	27,18,132,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	43487
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTSC0',0,18,137,13,3
	.word	58373
	.byte	27,18,140,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	43769
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTSS',0,18,145,13,3
	.word	58436
	.byte	27,18,148,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	43941
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_REV',0,18,153,13,3
	.word	58498
	.byte	27,18,156,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	44119
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_RST',0,18,161,13,3
	.word	58559
	.byte	27,18,164,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	44207
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,18,169,13,3
	.word	58620
	.byte	27,18,172,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	44315
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,18,177,13,3
	.word	58690
	.byte	27,18,180,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	44447
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,18,185,13,3
	.word	58760
	.byte	27,18,188,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	44555
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,18,193,13,3
	.word	58830
	.byte	27,18,196,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	44687
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,18,201,13,3
	.word	58900
	.byte	27,18,204,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	44795
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,18,209,13,3
	.word	58970
	.byte	27,18,212,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	44927
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CHEN',0,18,217,13,3
	.word	59040
	.byte	27,18,220,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	45073
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,18,225,13,3
	.word	59106
	.byte	27,18,228,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	45320
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CNT',0,18,233,13,3
	.word	59178
	.byte	27,18,236,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	45423
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,18,241,13,3
	.word	59246
	.byte	27,18,244,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	45522
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,18,249,13,3
	.word	59315
	.byte	27,18,252,13,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	46070
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,18,129,14,3
	.word	59384
	.byte	27,18,132,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	46176
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,18,137,14,3
	.word	59453
	.byte	27,18,140,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	46290
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,18,145,14,3
	.word	59523
	.byte	27,18,148,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	46545
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,18,153,14,3
	.word	59595
	.byte	27,18,156,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	46657
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,18,161,14,3
	.word	59666
	.byte	27,18,164,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	46769
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,18,169,14,3
	.word	59737
	.byte	27,18,172,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	46868
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,18,177,14,3
	.word	59806
	.byte	27,18,180,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	46967
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,18,185,14,3
	.word	59875
	.byte	27,18,188,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	47214
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,18,193,14,3
	.word	59946
	.byte	27,18,196,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	47453
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,18,201,14,3
	.word	60022
	.byte	27,18,204,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	47570
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,18,209,14,3
	.word	60095
	.byte	27,18,212,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	47783
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,18,217,14,3
	.word	60170
	.byte	27,18,220,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	47890
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,18,225,14,3
	.word	60239
	.byte	27,18,228,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	48032
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_IN_SRC',0,18,233,14,3
	.word	60308
	.byte	27,18,236,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	48377
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_INP_VAL',0,18,241,14,3
	.word	60376
	.byte	27,18,244,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	48518
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_RST',0,18,249,14,3
	.word	60445
	.byte	27,18,252,14,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	48751
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CM0',0,18,129,15,3
	.word	60510
	.byte	27,18,132,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	48854
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CM1',0,18,137,15,3
	.word	60578
	.byte	27,18,140,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	48957
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CN0',0,18,145,15,3
	.word	60646
	.byte	27,18,148,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	49060
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,18,153,15,3
	.word	60714
	.byte	27,18,156,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	49388
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,18,161,15,3
	.word	60783
	.byte	27,18,164,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	49531
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,18,169,15,3
	.word	60854
	.byte	27,18,172,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	49680
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,18,177,15,3
	.word	60930
	.byte	27,18,180,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	49797
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,18,185,15,3
	.word	61003
	.byte	27,18,188,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	49934
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_SR0',0,18,193,15,3
	.word	61078
	.byte	27,18,196,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	50037
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_SR1',0,18,201,15,3
	.word	61146
	.byte	27,18,204,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	50140
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_STAT',0,18,209,15,3
	.word	61214
	.byte	27,18,212,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	50243
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,18,217,15,3
	.word	61283
	.byte	27,18,220,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	50397
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,18,225,15,3
	.word	61356
	.byte	27,18,228,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	50687
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,18,233,15,3
	.word	61433
	.byte	27,18,236,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	50977
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,18,241,15,3
	.word	61510
	.byte	27,18,244,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	51410
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,18,249,15,3
	.word	61586
	.byte	27,18,252,15,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	51860
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,18,129,16,3
	.word	61661
	.byte	27,18,132,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	52130
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,18,137,16,3
	.word	61736
	.byte	27,18,140,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	52420
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,18,145,16,3
	.word	61813
	.byte	27,18,148,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	52710
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,18,153,16,3
	.word	61890
	.byte	27,18,156,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	52864
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,18,161,16,3
	.word	61963
	.byte	27,18,164,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	53154
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,18,169,16,3
	.word	62040
	.byte	27,18,172,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	53444
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,18,177,16,3
	.word	62117
	.byte	27,18,180,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	53877
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,18,185,16,3
	.word	62193
	.byte	27,18,188,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	54327
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,18,193,16,3
	.word	62268
	.byte	27,18,196,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	54597
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,18,201,16,3
	.word	62343
	.byte	27,18,204,16,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	54887
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,18,209,16,3
	.word	62420
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,18,220,16,25,4,6
	.byte	'CTRL',0,4
	.word	55783
	.byte	2,35,0,0,28
	.word	62497
	.byte	17
	.byte	'Ifx_GTM_CMU_CLK0_5',0,18,223,16,3
	.word	62538
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_6',0,18,226,16,25,4,6
	.byte	'CTRL',0,4
	.word	55856
	.byte	2,35,0,0,28
	.word	62571
	.byte	17
	.byte	'Ifx_GTM_CMU_CLK_6',0,18,229,16,3
	.word	62611
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_7',0,18,232,16,25,4,6
	.byte	'CTRL',0,4
	.word	55928
	.byte	2,35,0,0,28
	.word	62643
	.byte	17
	.byte	'Ifx_GTM_CMU_CLK_7',0,18,235,16,3
	.word	62683
	.byte	3
	.byte	'_Ifx_GTM_CMU_ECLK',0,18,238,16,25,8,6
	.byte	'NUM',0,4
	.word	56138
	.byte	2,35,0,6
	.byte	'DEN',0,4
	.word	56068
	.byte	2,35,4,0,28
	.word	62715
	.byte	17
	.byte	'Ifx_GTM_CMU_ECLK',0,18,242,16,3
	.word	62766
	.byte	3
	.byte	'_Ifx_GTM_CMU_FXCLK',0,18,245,16,25,4,6
	.byte	'CTRL',0,4
	.word	56208
	.byte	2,35,0,0,28
	.word	62797
	.byte	17
	.byte	'Ifx_GTM_CMU_FXCLK',0,18,248,16,3
	.word	62837
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,18,251,16,25,4,6
	.byte	'OUTSEL',0,4
	.word	57373
	.byte	2,35,0,0,28
	.word	62869
	.byte	17
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,18,254,16,3
	.word	62914
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_T',0,18,129,17,25,32,7,32
	.word	57450
	.byte	8,7,0,6
	.byte	'OUTSEL',0,32
	.word	62975
	.byte	2,35,0,0,28
	.word	62949
	.byte	17
	.byte	'Ifx_GTM_INOUTSEL_T',0,18,132,17,3
	.word	63001
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,18,135,17,25,32,6
	.byte	'INSEL',0,4
	.word	57525
	.byte	2,35,0,7,28
	.word	387
	.byte	8,27,0,6
	.byte	'reserved_4',0,28
	.word	63077
	.byte	2,35,4,0,28
	.word	63034
	.byte	17
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,18,139,17,3
	.word	63107
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH',0,18,142,17,25,116,6
	.byte	'GPR0',0,4
	.word	59737
	.byte	2,35,0,6
	.byte	'GPR1',0,4
	.word	59806
	.byte	2,35,4,6
	.byte	'CNT',0,4
	.word	59178
	.byte	2,35,8,6
	.byte	'ECNT',0,4
	.word	59384
	.byte	2,35,12,6
	.byte	'CNTS',0,4
	.word	59246
	.byte	2,35,16,6
	.byte	'TDUC',0,4
	.word	60170
	.byte	2,35,20,6
	.byte	'TDUV',0,4
	.word	60239
	.byte	2,35,24,6
	.byte	'FLT_RE',0,4
	.word	59666
	.byte	2,35,28,6
	.byte	'FLT_FE',0,4
	.word	59595
	.byte	2,35,32,6
	.byte	'CTRL',0,4
	.word	59315
	.byte	2,35,36,6
	.byte	'ECTRL',0,4
	.word	59453
	.byte	2,35,40,6
	.byte	'IRQ_NOTIFY',0,4
	.word	60095
	.byte	2,35,44,6
	.byte	'IRQ_EN',0,4
	.word	59875
	.byte	2,35,48,6
	.byte	'IRQ_FORCINT',0,4
	.word	59946
	.byte	2,35,52,6
	.byte	'IRQ_MODE',0,4
	.word	60022
	.byte	2,35,56,6
	.byte	'EIRQ_EN',0,4
	.word	59523
	.byte	2,35,60,7,52
	.word	387
	.byte	8,51,0,6
	.byte	'reserved_40',0,52
	.word	63414
	.byte	2,35,64,0,28
	.word	63142
	.byte	17
	.byte	'Ifx_GTM_TIM_CH',0,18,161,17,3
	.word	63445
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH',0,18,164,17,25,48,6
	.byte	'CTRL',0,4
	.word	60714
	.byte	2,35,0,6
	.byte	'SR0',0,4
	.word	61078
	.byte	2,35,4,6
	.byte	'SR1',0,4
	.word	61146
	.byte	2,35,8,6
	.byte	'CM0',0,4
	.word	60510
	.byte	2,35,12,6
	.byte	'CM1',0,4
	.word	60578
	.byte	2,35,16,6
	.byte	'CN0',0,4
	.word	60646
	.byte	2,35,20,6
	.byte	'STAT',0,4
	.word	61214
	.byte	2,35,24,6
	.byte	'IRQ_NOTIFY',0,4
	.word	61003
	.byte	2,35,28,6
	.byte	'IRQ_EN',0,4
	.word	60783
	.byte	2,35,32,6
	.byte	'IRQ_FORCINT',0,4
	.word	60854
	.byte	2,35,36,6
	.byte	'IRQ_MODE',0,4
	.word	60930
	.byte	2,35,40,7,4
	.word	387
	.byte	8,3,0,6
	.byte	'reserved_2C',0,4
	.word	63664
	.byte	2,35,44,0,28
	.word	63474
	.byte	17
	.byte	'Ifx_GTM_TOM_CH',0,18,178,17,3
	.word	63695
	.byte	3
	.byte	'_Ifx_GTM_BRIDGE',0,18,191,17,25,12,6
	.byte	'MODE',0,4
	.word	55515
	.byte	2,35,0,6
	.byte	'PTR1',0,4
	.word	55584
	.byte	2,35,4,6
	.byte	'PTR2',0,4
	.word	55653
	.byte	2,35,8,0,28
	.word	63724
	.byte	17
	.byte	'Ifx_GTM_BRIDGE',0,18,196,17,3
	.word	63789
	.byte	3
	.byte	'_Ifx_GTM_CMU',0,18,199,17,25,72,6
	.byte	'CLK_EN',0,4
	.word	56000
	.byte	2,35,0,6
	.byte	'GCLK_NUM',0,4
	.word	56350
	.byte	2,35,4,6
	.byte	'GCLK_DEN',0,4
	.word	56280
	.byte	2,35,8,7,24
	.word	62497
	.byte	8,5,0,28
	.word	63889
	.byte	6
	.byte	'CLK0_5',0,24
	.word	63898
	.byte	2,35,12,28
	.word	62571
	.byte	6
	.byte	'CLK_6',0,4
	.word	63919
	.byte	2,35,36,28
	.word	62643
	.byte	6
	.byte	'CLK_7',0,4
	.word	63939
	.byte	2,35,40,7,24
	.word	62715
	.byte	8,2,0,28
	.word	63959
	.byte	6
	.byte	'ECLK',0,24
	.word	63968
	.byte	2,35,44,28
	.word	62797
	.byte	6
	.byte	'FXCLK',0,4
	.word	63987
	.byte	2,35,68,0,28
	.word	63818
	.byte	17
	.byte	'Ifx_GTM_CMU',0,18,209,17,3
	.word	64008
	.byte	3
	.byte	'_Ifx_GTM_DTM',0,18,212,17,25,36,6
	.byte	'CTRL',0,4
	.word	56695
	.byte	2,35,0,6
	.byte	'CH_CTRL1',0,4
	.word	56482
	.byte	2,35,4,6
	.byte	'CH_CTRL2',0,4
	.word	56552
	.byte	2,35,8,6
	.byte	'CH_CTRL2_SR',0,4
	.word	56622
	.byte	2,35,12,6
	.byte	'PS_CTRL',0,4
	.word	56829
	.byte	2,35,16,7,16
	.word	56761
	.byte	8,3,0,6
	.byte	'DTV_CH',0,16
	.word	64141
	.byte	2,35,20,0,28
	.word	64034
	.byte	17
	.byte	'Ifx_GTM_DTM',0,18,220,17,3
	.word	64167
	.byte	3
	.byte	'_Ifx_GTM_ICM',0,18,223,17,25,60,6
	.byte	'IRQG_0',0,4
	.word	57028
	.byte	2,35,0,6
	.byte	'reserved_4',0,4
	.word	63664
	.byte	2,35,4,6
	.byte	'IRQG_2',0,4
	.word	57096
	.byte	2,35,8,7,12
	.word	387
	.byte	8,11,0,6
	.byte	'reserved_C',0,12
	.word	64264
	.byte	2,35,12,6
	.byte	'IRQG_6',0,4
	.word	57164
	.byte	2,35,24,7,20
	.word	387
	.byte	8,19,0,6
	.byte	'reserved_1C',0,20
	.word	64309
	.byte	2,35,28,6
	.byte	'IRQG_MEI',0,4
	.word	57303
	.byte	2,35,48,6
	.byte	'reserved_34',0,4
	.word	63664
	.byte	2,35,52,6
	.byte	'IRQG_CEI1',0,4
	.word	57232
	.byte	2,35,56,0,28
	.word	64193
	.byte	17
	.byte	'Ifx_GTM_ICM',0,18,234,17,3
	.word	64398
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL',0,18,237,17,25,148,1,7,32
	.word	63034
	.byte	8,0,0,28
	.word	64449
	.byte	6
	.byte	'TIM',0,32
	.word	64458
	.byte	2,35,0,28
	.word	62949
	.byte	6
	.byte	'T',0,32
	.word	64476
	.byte	2,35,32,7,80
	.word	387
	.byte	8,79,0,6
	.byte	'reserved_40',0,80
	.word	64492
	.byte	2,35,64,28
	.word	62869
	.byte	6
	.byte	'CAN',0,4
	.word	64522
	.byte	3,35,144,1,0,28
	.word	64424
	.byte	17
	.byte	'Ifx_GTM_INOUTSEL',0,18,243,17,3
	.word	64542
	.byte	3
	.byte	'_Ifx_GTM_TBU',0,18,246,17,25,28,6
	.byte	'CHEN',0,4
	.word	59040
	.byte	2,35,0,6
	.byte	'CH0_CTRL',0,4
	.word	58690
	.byte	2,35,4,6
	.byte	'CH0_BASE',0,4
	.word	58620
	.byte	2,35,8,6
	.byte	'CH1_CTRL',0,4
	.word	58830
	.byte	2,35,12,6
	.byte	'CH1_BASE',0,4
	.word	58760
	.byte	2,35,16,6
	.byte	'CH2_CTRL',0,4
	.word	58970
	.byte	2,35,20,6
	.byte	'CH2_BASE',0,4
	.word	58900
	.byte	2,35,24,0,28
	.word	64573
	.byte	17
	.byte	'Ifx_GTM_TBU',0,18,255,17,3
	.word	64715
	.byte	3
	.byte	'_Ifx_GTM_TIM',0,18,130,18,25,128,8,28
	.word	63142
	.byte	6
	.byte	'CH0',0,116
	.word	64761
	.byte	2,35,0,6
	.byte	'INP_VAL',0,4
	.word	60376
	.byte	2,35,116,6
	.byte	'IN_SRC',0,4
	.word	60308
	.byte	2,35,120,6
	.byte	'RST',0,4
	.word	60445
	.byte	2,35,124,28
	.word	63142
	.byte	6
	.byte	'CH1',0,116
	.word	64825
	.byte	3,35,128,1,6
	.byte	'reserved_F4',0,12
	.word	64264
	.byte	3,35,244,1,28
	.word	63142
	.byte	6
	.byte	'CH2',0,116
	.word	64866
	.byte	3,35,128,2,6
	.byte	'reserved_174',0,12
	.word	64264
	.byte	3,35,244,2,28
	.word	63142
	.byte	6
	.byte	'CH3',0,116
	.word	64908
	.byte	3,35,128,3,6
	.byte	'reserved_1F4',0,12
	.word	64264
	.byte	3,35,244,3,28
	.word	63142
	.byte	6
	.byte	'CH4',0,116
	.word	64950
	.byte	3,35,128,4,6
	.byte	'reserved_274',0,12
	.word	64264
	.byte	3,35,244,4,28
	.word	63142
	.byte	6
	.byte	'CH5',0,116
	.word	64992
	.byte	3,35,128,5,6
	.byte	'reserved_2F4',0,12
	.word	64264
	.byte	3,35,244,5,28
	.word	63142
	.byte	6
	.byte	'CH6',0,116
	.word	65034
	.byte	3,35,128,6,6
	.byte	'reserved_374',0,12
	.word	64264
	.byte	3,35,244,6,28
	.word	63142
	.byte	6
	.byte	'CH7',0,116
	.word	65076
	.byte	3,35,128,7,6
	.byte	'reserved_3F4',0,12
	.word	64264
	.byte	3,35,244,7,0,28
	.word	64741
	.byte	17
	.byte	'Ifx_GTM_TIM',0,18,150,18,3
	.word	65119
	.byte	3
	.byte	'_Ifx_GTM_TOM',0,18,153,18,25,128,16,28
	.word	63474
	.byte	6
	.byte	'CH0',0,48
	.word	65165
	.byte	2,35,0,6
	.byte	'TGC0_GLB_CTRL',0,4
	.word	61586
	.byte	2,35,48,6
	.byte	'TGC0_ACT_TB',0,4
	.word	61283
	.byte	2,35,52,6
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	61510
	.byte	2,35,56,6
	.byte	'TGC0_INT_TRIG',0,4
	.word	61661
	.byte	2,35,60,28
	.word	63474
	.byte	6
	.byte	'CH1',0,48
	.word	65274
	.byte	2,35,64,6
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	61356
	.byte	2,35,112,6
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	61433
	.byte	2,35,116,6
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	61736
	.byte	2,35,120,6
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	61813
	.byte	2,35,124,28
	.word	63474
	.byte	6
	.byte	'CH2',0,48
	.word	65392
	.byte	3,35,128,1,7,16
	.word	387
	.byte	8,15,0,6
	.byte	'reserved_B0',0,16
	.word	65411
	.byte	3,35,176,1,28
	.word	63474
	.byte	6
	.byte	'CH3',0,48
	.word	65442
	.byte	3,35,192,1,6
	.byte	'reserved_F0',0,16
	.word	65411
	.byte	3,35,240,1,28
	.word	63474
	.byte	6
	.byte	'CH4',0,48
	.word	65483
	.byte	3,35,128,2,6
	.byte	'reserved_130',0,16
	.word	65411
	.byte	3,35,176,2,28
	.word	63474
	.byte	6
	.byte	'CH5',0,48
	.word	65525
	.byte	3,35,192,2,6
	.byte	'reserved_170',0,16
	.word	65411
	.byte	3,35,240,2,28
	.word	63474
	.byte	6
	.byte	'CH6',0,48
	.word	65567
	.byte	3,35,128,3,6
	.byte	'reserved_1B0',0,16
	.word	65411
	.byte	3,35,176,3,28
	.word	63474
	.byte	6
	.byte	'CH7',0,48
	.word	65609
	.byte	3,35,192,3,6
	.byte	'reserved_1F0',0,16
	.word	65411
	.byte	3,35,240,3,28
	.word	63474
	.byte	6
	.byte	'CH8',0,48
	.word	65651
	.byte	3,35,128,4,6
	.byte	'TGC1_GLB_CTRL',0,4
	.word	62193
	.byte	3,35,176,4,6
	.byte	'TGC1_ACT_TB',0,4
	.word	61890
	.byte	3,35,180,4,6
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	62117
	.byte	3,35,184,4,6
	.byte	'TGC1_INT_TRIG',0,4
	.word	62268
	.byte	3,35,188,4,28
	.word	63474
	.byte	6
	.byte	'CH9',0,48
	.word	65765
	.byte	3,35,192,4,6
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	61963
	.byte	3,35,240,4,6
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	62040
	.byte	3,35,244,4,6
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	62343
	.byte	3,35,248,4,6
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	62420
	.byte	3,35,252,4,28
	.word	63474
	.byte	6
	.byte	'CH10',0,48
	.word	65888
	.byte	3,35,128,5,6
	.byte	'reserved_2B0',0,16
	.word	65411
	.byte	3,35,176,5,28
	.word	63474
	.byte	6
	.byte	'CH11',0,48
	.word	65931
	.byte	3,35,192,5,6
	.byte	'reserved_2F0',0,16
	.word	65411
	.byte	3,35,240,5,28
	.word	63474
	.byte	6
	.byte	'CH12',0,48
	.word	65974
	.byte	3,35,128,6,6
	.byte	'reserved_330',0,16
	.word	65411
	.byte	3,35,176,6,28
	.word	63474
	.byte	6
	.byte	'CH13',0,48
	.word	66017
	.byte	3,35,192,6,6
	.byte	'reserved_370',0,16
	.word	65411
	.byte	3,35,240,6,28
	.word	63474
	.byte	6
	.byte	'CH14',0,48
	.word	66060
	.byte	3,35,128,7,6
	.byte	'reserved_3B0',0,16
	.word	65411
	.byte	3,35,176,7,28
	.word	63474
	.byte	6
	.byte	'CH15',0,48
	.word	66103
	.byte	3,35,192,7,7,144,8
	.word	387
	.byte	8,143,8,0,6
	.byte	'reserved_3F0',0,144,8
	.word	66123
	.byte	3,35,240,7,0,28
	.word	65145
	.byte	17
	.byte	'Ifx_GTM_TOM',0,18,199,18,3
	.word	66159
	.byte	9,3,130,4,20,64,6
	.byte	'CTRL',0,4
	.word	60714
	.byte	2,35,0,6
	.byte	'SR0',0,4
	.word	61078
	.byte	2,35,4,6
	.byte	'SR1',0,4
	.word	61146
	.byte	2,35,8,6
	.byte	'CM0',0,4
	.word	60510
	.byte	2,35,12,6
	.byte	'CM1',0,4
	.word	60578
	.byte	2,35,16,6
	.byte	'CN0',0,4
	.word	60646
	.byte	2,35,20,6
	.byte	'STAT',0,4
	.word	61214
	.byte	2,35,24,6
	.byte	'IRQ_NOTIFY',0,4
	.word	61003
	.byte	2,35,28,6
	.byte	'IRQ_EN',0,4
	.word	60783
	.byte	2,35,32,6
	.byte	'IRQ_FORCINT',0,4
	.word	60854
	.byte	2,35,36,6
	.byte	'IRQ_MODE',0,4
	.word	60930
	.byte	2,35,40,7,20
	.word	387
	.byte	8,19,0,6
	.byte	'reserved_2C',0,20
	.word	66359
	.byte	2,35,44,0,28
	.word	66185
	.byte	17
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,3,155,4,4
	.word	66390
	.byte	9,3,157,4,20,128,4,6
	.byte	'GLB_CTRL',0,4
	.word	61586
	.byte	2,35,0,6
	.byte	'ACT_TB',0,4
	.word	61283
	.byte	2,35,4,6
	.byte	'FUPD_CTRL',0,4
	.word	61510
	.byte	2,35,8,6
	.byte	'INT_TRIG',0,4
	.word	61661
	.byte	2,35,12,7,48
	.word	387
	.byte	8,47,0,6
	.byte	'reserved_tgc0',0,48
	.word	66502
	.byte	2,35,16,6
	.byte	'ENDIS_CTRL',0,4
	.word	61356
	.byte	2,35,64,6
	.byte	'ENDIS_STAT',0,4
	.word	61433
	.byte	2,35,68,6
	.byte	'OUTEN_CTRL',0,4
	.word	61736
	.byte	2,35,72,6
	.byte	'OUTEN_STAT',0,4
	.word	61813
	.byte	2,35,76,7,176,3
	.word	387
	.byte	8,175,3,0,6
	.byte	'reserved_tgc1',0,176,3
	.word	66614
	.byte	2,35,80,0,28
	.word	66424
	.byte	17
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,3,177,4,5
	.word	66650
	.byte	9,3,179,4,20,128,16,6
	.byte	'reserved_tom0',0,48
	.word	66502
	.byte	2,35,0,7,128,8
	.word	66424
	.byte	8,1,0,28
	.word	66715
	.byte	6
	.byte	'TGC',0,128,8
	.word	66725
	.byte	2,35,48,7,208,7
	.word	387
	.byte	8,207,7,0,6
	.byte	'reserved_tgc2',0,208,7
	.word	66744
	.byte	3,35,176,8,0,28
	.word	66685
	.byte	17
	.byte	'Ifx_GTM_TOM_TGCx',0,3,184,4,5
	.word	66781
	.byte	9,3,187,4,20,128,16,7,128,8
	.word	66185
	.byte	8,15,0,28
	.word	66819
	.byte	6
	.byte	'CH',0,128,8
	.word	66829
	.byte	2,35,0,7,128,8
	.word	387
	.byte	8,255,7,0,6
	.byte	'reserved_tom1',0,128,8
	.word	66847
	.byte	3,35,128,8,0,28
	.word	66812
	.byte	17
	.byte	'Ifx_GTM_TOM_CHx',0,3,191,4,5
	.word	66884
	.byte	9,3,212,4,20,128,1,6
	.byte	'CH_GPR0',0,4
	.word	59737
	.byte	2,35,0,6
	.byte	'CH_GPR1',0,4
	.word	59806
	.byte	2,35,4,6
	.byte	'CH_CNT',0,4
	.word	59178
	.byte	2,35,8,6
	.byte	'CH_ECNT',0,4
	.word	59384
	.byte	2,35,12,6
	.byte	'CH_CNTS',0,4
	.word	59246
	.byte	2,35,16,6
	.byte	'CH_TDUC',0,4
	.word	60170
	.byte	2,35,20,6
	.byte	'CH_TDUV',0,4
	.word	60239
	.byte	2,35,24,6
	.byte	'CH_FLT_RE',0,4
	.word	59666
	.byte	2,35,28,6
	.byte	'CH_FLT_FE',0,4
	.word	59595
	.byte	2,35,32,6
	.byte	'CH_CTRL',0,4
	.word	59315
	.byte	2,35,36,6
	.byte	'CH_ECTRL',0,4
	.word	59453
	.byte	2,35,40,6
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	60095
	.byte	2,35,44,6
	.byte	'CH_IRQ_EN',0,4
	.word	59875
	.byte	2,35,48,6
	.byte	'CH_IRQ_FORCINT',0,4
	.word	59946
	.byte	2,35,52,6
	.byte	'CH_IRQ_MODE',0,4
	.word	60022
	.byte	2,35,56,6
	.byte	'CH_EIRQ_EN',0,4
	.word	59523
	.byte	2,35,60,7,64
	.word	387
	.byte	8,63,0,6
	.byte	'reserved_40',0,64
	.word	67219
	.byte	2,35,64,0,28
	.word	66914
	.byte	17
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,3,248,4,4
	.word	67250
	.byte	9,3,250,4,20,8,6
	.byte	'IN_SRC',0,4
	.word	60308
	.byte	2,35,0,6
	.byte	'RST',0,4
	.word	60445
	.byte	2,35,4,0,28
	.word	67284
	.byte	17
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,3,255,4,4
	.word	67320
	.byte	9,3,129,5,21,128,16,7,128,8
	.word	66914
	.byte	8,7,0,28
	.word	67371
	.byte	6
	.byte	'CH',0,128,8
	.word	67381
	.byte	2,35,0,6
	.byte	'reserved_tim1',0,128,8
	.word	66847
	.byte	3,35,128,8,0,28
	.word	67364
	.byte	17
	.byte	'Ifx_GTM_TIM_CHx',0,3,133,5,4
	.word	67425
	.byte	9,3,135,5,20,128,16,7,120
	.word	387
	.byte	8,119,0,6
	.byte	'reserved_tim2',0,120
	.word	67462
	.byte	2,35,0,28
	.word	67284
	.byte	6
	.byte	'IN_SRC_RESET',0,8
	.word	67494
	.byte	2,35,120,7,128,15
	.word	387
	.byte	8,255,14,0,6
	.byte	'reserved_tim3',0,128,15
	.word	67521
	.byte	3,35,128,1,0,28
	.word	67455
	.byte	17
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,3,140,5,4
	.word	67558
	.byte	19,3,174,5,11,1,15
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,15
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,15
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,15
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,15
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,15
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,15
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,15
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,17
	.byte	'Gtm_ConfigurableClockType',0,3,184,5,4
	.word	67596
	.byte	19,3,188,5,11,1,15
	.byte	'GTM_LOW',0,0,15
	.byte	'GTM_HIGH',0,1,0,17
	.byte	'Gtm_OutputLevelType',0,3,192,5,4
	.word	67830
	.byte	19,3,195,5,11,1,15
	.byte	'TOM_GLB_CTRL',0,0,15
	.byte	'TOM_ACT_TB',0,1,15
	.byte	'TOM_FUPD_CTRL',0,2,15
	.byte	'TOM_INT_TRIG',0,3,15
	.byte	'TOM_RESERVED_0',0,4,15
	.byte	'TOM_RESERVED_1',0,5,15
	.byte	'TOM_RESERVED_2',0,6,15
	.byte	'TOM_RESERVED_3',0,7,15
	.byte	'TOM_RESERVED_4',0,8,15
	.byte	'TOM_RESERVED_5',0,9,15
	.byte	'TOM_RESERVED_6',0,10,15
	.byte	'TOM_RESERVED_7',0,11,15
	.byte	'TOM_RESERVED_8',0,12,15
	.byte	'TOM_RESERVED_9',0,13,15
	.byte	'TOM_RESERVED_10',0,14,15
	.byte	'TOM_RESERVED_11',0,15,15
	.byte	'TOM_ENDIS_CTRL',0,16,15
	.byte	'TOM_ENDIS_STAT',0,17,15
	.byte	'TOM_OUTEN_CTRL',0,18,15
	.byte	'TOM_OUTEN_STAT',0,19,0,17
	.byte	'Gtm_TomTimerRegistersType',0,3,217,5,4
	.word	67887
	.byte	9,3,221,5,11,8,6
	.byte	'FltRisingEdge',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'FltFallingEdge',0,4
	.word	287
	.byte	2,35,4,0,17
	.byte	'Gtm_TimFilterType',0,3,225,5,4
	.word	68262
	.byte	17
	.byte	'Gtm_TbuChCtrlType',0,3,230,5,32
	.word	58690
	.byte	17
	.byte	'Gtm_TbuChBaseType',0,3,231,5,32
	.word	58620
	.byte	9,3,233,5,11,8,6
	.byte	'CH_CTRL',0,4
	.word	58690
	.byte	2,35,0,6
	.byte	'CH_BASE',0,4
	.word	58620
	.byte	2,35,4,0,17
	.byte	'Gtm_TbuChType',0,3,237,5,4
	.word	68397
	.byte	17
	.byte	'Gtm_PortConfigType',0,3,253,5,2
	.word	1202
	.byte	17
	.byte	'Gtm_TimFltType',0,3,134,6,2
	.word	1590
	.byte	17
	.byte	'Gtm_TimConfigType',0,3,151,6,4
	.word	1483
	.byte	17
	.byte	'Gtm_ModUsageConfigType',0,3,163,6,4
	.word	2447
	.byte	17
	.byte	'Gtm_TomTgcConfigGroupType',0,3,185,6,2
	.word	1862
	.byte	17
	.byte	'Gtm_TomTgcConfigType',0,3,196,6,2
	.word	1812
	.byte	17
	.byte	'Gtm_TomChannelConfigType',0,3,207,6,2
	.word	2224
	.byte	17
	.byte	'Gtm_TomConfigType',0,3,219,6,2
	.word	2150
	.byte	17
	.byte	'Gtm_ExtClkType',0,3,227,6,2
	.word	1090
	.byte	17
	.byte	'Gtm_ClockSettingType',0,3,236,6,2
	.word	1012
	.byte	17
	.byte	'Gtm_GeneralConfigType',0,3,245,6,2
	.word	2556
	.byte	17
	.byte	'Gtm_TbuConfigType',0,3,253,6,2
	.word	2646
	.byte	17
	.byte	'Gtm_ModuleConfigType',0,3,163,7,2
	.word	1289
	.byte	13,1,1,16
	.word	387
	.byte	16
	.word	387
	.byte	16
	.word	387
	.byte	16
	.word	227
	.byte	0,11
	.word	68840
	.byte	17
	.byte	'Gtm_NotificationPtrType',0,3,172,7,16
	.word	68864
	.byte	17
	.byte	'Gtm_ConfigType',0,3,197,7,2
	.word	991
	.byte	17
	.byte	'Mcu_ClockType',0,2,156,3,18
	.word	287
	.byte	17
	.byte	'Mcu_ModeType',0,2,162,3,18
	.word	287
	.byte	17
	.byte	'Mcu_RamSectionType',0,2,168,3,18
	.word	287
	.byte	17
	.byte	'Mcu_RamBaseAdrType',0,2,178,3,18
	.word	10610
	.byte	17
	.byte	'Mcu_RamSizeType',0,2,181,3,17
	.word	287
	.byte	17
	.byte	'Mcu_RamPrstDatType',0,2,184,3,16
	.word	387
	.byte	17
	.byte	'Mcu_PllStatusType',0,2,207,3,2
	.word	10196
	.byte	17
	.byte	'Mcu_ClockCfgType',0,2,162,4,2
	.word	364
	.byte	17
	.byte	'Mcu_StandbyModeType',0,2,175,4,2
	.word	2953
	.byte	3
	.byte	'_Ifx_SMU_ACCEN0_Bits',0,19,45,16,4,10
	.byte	'EN0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'EN1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'EN2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'EN3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'EN4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'EN5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'EN6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'EN7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'EN8',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'EN9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'EN10',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'EN11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'EN12',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'EN13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'EN14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'EN15',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'EN16',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'EN17',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'EN18',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'EN19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'EN20',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'EN21',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'EN22',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'EN23',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'EN24',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'EN25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'EN26',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'EN27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'EN28',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'EN29',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'EN30',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'EN31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_SMU_ACCEN0_Bits',0,19,79,3
	.word	69162
	.byte	3
	.byte	'_Ifx_SMU_ACCEN1_Bits',0,19,82,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_SMU_ACCEN1_Bits',0,19,85,3
	.word	69719
	.byte	3
	.byte	'_Ifx_SMU_AD_Bits',0,19,88,16,4,10
	.byte	'DF0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'DF1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'DF2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'DF3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'DF4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'DF5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'DF6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'DF7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'DF8',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'DF9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'DF10',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'DF11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'DF12',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'DF13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'DF14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'DF15',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'DF16',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'DF17',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'DF18',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'DF19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'DF20',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'DF21',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'DF22',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'DF23',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'DF24',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'DF25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'DF26',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'DF27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'DF28',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'DF29',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'DF30',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'DF31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_SMU_AD_Bits',0,19,122,3
	.word	69796
	.byte	3
	.byte	'_Ifx_SMU_AFCNT_Bits',0,19,125,16,4,10
	.byte	'FCNT',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'ACNT',0,4
	.word	26556
	.byte	8,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	14,2,2,35,0,10
	.byte	'FCO',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'ACO',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_SMU_AFCNT_Bits',0,19,133,1,3
	.word	70345
	.byte	3
	.byte	'_Ifx_SMU_AG_Bits',0,19,136,1,16,4,10
	.byte	'SF0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'SF1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'SF2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'SF3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'SF4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'SF5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'SF6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'SF7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'SF8',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'SF9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'SF10',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'SF11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'SF12',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'SF13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'SF14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'SF15',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'SF16',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'SF17',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'SF18',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'SF19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'SF20',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'SF21',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'SF22',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'SF23',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'SF24',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'SF25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'SF26',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'SF27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'SF28',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'SF29',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'SF30',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'SF31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_SMU_AG_Bits',0,19,170,1,3
	.word	70506
	.byte	3
	.byte	'_Ifx_SMU_AGC_Bits',0,19,173,1,16,4,10
	.byte	'IGCS0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'IGCS1',0,4
	.word	26556
	.byte	3,25,2,35,0,10
	.byte	'reserved_7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'IGCS2',0,4
	.word	26556
	.byte	3,21,2,35,0,10
	.byte	'reserved_11',0,4
	.word	26556
	.byte	5,16,2,35,0,10
	.byte	'ICS',0,4
	.word	26556
	.byte	3,13,2,35,0,10
	.byte	'reserved_19',0,4
	.word	26556
	.byte	5,8,2,35,0,10
	.byte	'PES',0,4
	.word	26556
	.byte	5,3,2,35,0,10
	.byte	'EFRST',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'reserved_30',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_SMU_AGC_Bits',0,19,186,1,3
	.word	71057
	.byte	3
	.byte	'_Ifx_SMU_AGCF_Bits',0,19,189,1,16,4,10
	.byte	'CF0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'CF1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'CF2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'CF3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'CF4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'CF5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'CF6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'CF7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'CF8',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'CF9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'CF10',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'CF11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'CF12',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'CF13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'CF14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'CF15',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'CF16',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'CF17',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'CF18',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'CF19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'CF20',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'CF21',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'CF22',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'CF23',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'CF24',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'CF25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'CF26',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'CF27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'CF28',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'CF29',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'CF30',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'CF31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_SMU_AGCF_Bits',0,19,223,1,3
	.word	71319
	.byte	3
	.byte	'_Ifx_SMU_AGFSP_Bits',0,19,226,1,16,4,10
	.byte	'FE0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'FE1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'FE2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'FE3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'FE4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'FE5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'FE6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'FE7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'FE8',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'FE9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'FE10',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'FE11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'FE12',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'FE13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'FE14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'FE15',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'FE16',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'FE17',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'FE18',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'FE19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'FE20',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'FE21',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'FE22',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'FE23',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'FE24',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'FE25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'FE26',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'FE27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'FE28',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'FE29',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'FE30',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'FE31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_SMU_AGFSP_Bits',0,19,132,2,3
	.word	71874
	.byte	3
	.byte	'_Ifx_SMU_CLC_Bits',0,19,135,2,16,4,10
	.byte	'DISR',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'DISS',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'FDIS',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'EDIS',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_SMU_CLC_Bits',0,19,142,2,3
	.word	72431
	.byte	3
	.byte	'_Ifx_SMU_CMD_Bits',0,19,145,2,16,4,10
	.byte	'CMD',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'ARG',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_SMU_CMD_Bits',0,19,150,2,3
	.word	72568
	.byte	3
	.byte	'_Ifx_SMU_DBG_Bits',0,19,153,2,16,4,10
	.byte	'SSM',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_SMU_DBG_Bits',0,19,157,2,3
	.word	72671
	.byte	3
	.byte	'_Ifx_SMU_FSP_Bits',0,19,160,2,16,4,10
	.byte	'PRE1',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'PRE2',0,4
	.word	26556
	.byte	2,27,2,35,0,10
	.byte	'MODE',0,4
	.word	26556
	.byte	2,25,2,35,0,10
	.byte	'PES',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'TFSP_LOW',0,4
	.word	26556
	.byte	14,10,2,35,0,10
	.byte	'TFSP_HIGH',0,4
	.word	26556
	.byte	10,0,2,35,0,0,17
	.byte	'Ifx_SMU_FSP_Bits',0,19,168,2,3
	.word	72759
	.byte	3
	.byte	'_Ifx_SMU_ID_Bits',0,19,171,2,16,4,10
	.byte	'MODREV',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'MODTYPE',0,4
	.word	26556
	.byte	8,16,2,35,0,10
	.byte	'MODNUMBER',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_SMU_ID_Bits',0,19,176,2,3
	.word	72914
	.byte	3
	.byte	'_Ifx_SMU_KEYS_Bits',0,19,179,2,16,4,10
	.byte	'CFGLCK',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'PERLCK',0,4
	.word	26556
	.byte	8,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_SMU_KEYS_Bits',0,19,184,2,3
	.word	73021
	.byte	3
	.byte	'_Ifx_SMU_KRST0_Bits',0,19,187,2,16,4,10
	.byte	'RST',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'RSTSTAT',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_SMU_KRST0_Bits',0,19,192,2,3
	.word	73133
	.byte	3
	.byte	'_Ifx_SMU_KRST1_Bits',0,19,195,2,16,4,10
	.byte	'RST',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_SMU_KRST1_Bits',0,19,199,2,3
	.word	73244
	.byte	3
	.byte	'_Ifx_SMU_KRSTCLR_Bits',0,19,202,2,16,4,10
	.byte	'CLR',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_SMU_KRSTCLR_Bits',0,19,206,2,3
	.word	73336
	.byte	3
	.byte	'_Ifx_SMU_OCS_Bits',0,19,209,2,16,4,10
	.byte	'TGS',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'TGB',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'TG_P',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	20,8,2,35,0,10
	.byte	'SUS',0,4
	.word	26556
	.byte	4,4,2,35,0,10
	.byte	'SUS_P',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'SUSSTA',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'reserved_30',0,4
	.word	26556
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_SMU_OCS_Bits',0,19,219,2,3
	.word	73432
	.byte	3
	.byte	'_Ifx_SMU_PCTL_Bits',0,19,222,2,16,4,10
	.byte	'HWDIR',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'HWEN',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	5,25,2,35,0,10
	.byte	'PCS',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	8,16,2,35,0,10
	.byte	'PCFG',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_SMU_PCTL_Bits',0,19,230,2,3
	.word	73624
	.byte	3
	.byte	'_Ifx_SMU_RMCTL_Bits',0,19,233,2,16,4,10
	.byte	'TE',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_SMU_RMCTL_Bits',0,19,237,2,3
	.word	73785
	.byte	3
	.byte	'_Ifx_SMU_RMEF_Bits',0,19,240,2,16,4,10
	.byte	'EF0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'EF1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'EF2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'EF3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'EF4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'EF5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'EF6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'EF7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'EF8',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'EF9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'EF10',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'EF11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'EF12',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'EF13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'EF14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'EF15',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'EF16',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'EF17',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'EF18',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'EF19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'EF20',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'EF21',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'EF22',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'EF23',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'EF24',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'EF25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'EF26',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'EF27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'EF28',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'EF29',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'EF30',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'EF31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_SMU_RMEF_Bits',0,19,146,3,3
	.word	73876
	.byte	3
	.byte	'_Ifx_SMU_RMSTS_Bits',0,19,149,3,16,4,10
	.byte	'STS0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'STS1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'STS2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'STS3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'STS4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'STS5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'STS6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'STS7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'STS8',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'STS9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'STS10',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'STS11',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'STS12',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'STS13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'STS14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'STS15',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'STS16',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'STS17',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'STS18',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'STS19',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'STS20',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'STS21',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'STS22',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'STS23',0,4
	.word	26556
	.byte	1,8,2,35,0,10
	.byte	'STS24',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'STS25',0,4
	.word	26556
	.byte	1,6,2,35,0,10
	.byte	'STS26',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'STS27',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'STS28',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'STS29',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'STS30',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'STS31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_SMU_RMSTS_Bits',0,19,183,3,3
	.word	74431
	.byte	3
	.byte	'_Ifx_SMU_RTAC0_Bits',0,19,186,3,16,4,10
	.byte	'GID0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'ALID0',0,4
	.word	26556
	.byte	5,24,2,35,0,10
	.byte	'GID1',0,4
	.word	26556
	.byte	3,21,2,35,0,10
	.byte	'ALID1',0,4
	.word	26556
	.byte	5,16,2,35,0,10
	.byte	'GID2',0,4
	.word	26556
	.byte	3,13,2,35,0,10
	.byte	'ALID2',0,4
	.word	26556
	.byte	5,8,2,35,0,10
	.byte	'GID3',0,4
	.word	26556
	.byte	3,5,2,35,0,10
	.byte	'ALID3',0,4
	.word	26556
	.byte	5,0,2,35,0,0,17
	.byte	'Ifx_SMU_RTAC0_Bits',0,19,196,3,3
	.word	75020
	.byte	3
	.byte	'_Ifx_SMU_RTAC1_Bits',0,19,199,3,16,4,10
	.byte	'GID0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'ALID0',0,4
	.word	26556
	.byte	5,24,2,35,0,10
	.byte	'GID1',0,4
	.word	26556
	.byte	3,21,2,35,0,10
	.byte	'ALID1',0,4
	.word	26556
	.byte	5,16,2,35,0,10
	.byte	'GID2',0,4
	.word	26556
	.byte	3,13,2,35,0,10
	.byte	'ALID2',0,4
	.word	26556
	.byte	5,8,2,35,0,10
	.byte	'GID3',0,4
	.word	26556
	.byte	3,5,2,35,0,10
	.byte	'ALID3',0,4
	.word	26556
	.byte	5,0,2,35,0,0,17
	.byte	'Ifx_SMU_RTAC1_Bits',0,19,209,3,3
	.word	75207
	.byte	3
	.byte	'_Ifx_SMU_RTC_Bits',0,19,212,3,16,4,10
	.byte	'RT0E',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'RT1E',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	6,24,2,35,0,10
	.byte	'RTD',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_SMU_RTC_Bits',0,19,218,3,3
	.word	75394
	.byte	3
	.byte	'_Ifx_SMU_STS_Bits',0,19,221,3,16,4,10
	.byte	'CMD',0,4
	.word	26556
	.byte	4,28,2,35,0,10
	.byte	'ARG',0,4
	.word	26556
	.byte	4,24,2,35,0,10
	.byte	'RES',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'ASCE',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'FSP',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'FSTS',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'reserved_13',0,4
	.word	26556
	.byte	3,16,2,35,0,10
	.byte	'RTS0',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'RTME0',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'RTS1',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'RTME1',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	26556
	.byte	12,0,2,35,0,0,17
	.byte	'Ifx_SMU_STS_Bits',0,19,235,3,3
	.word	75514
	.byte	27,19,243,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	69162
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_ACCEN0',0,19,248,3,3
	.word	75769
	.byte	27,19,251,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	69719
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_ACCEN1',0,19,128,4,3
	.word	75833
	.byte	27,19,131,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	69796
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_AD',0,19,136,4,3
	.word	75897
	.byte	27,19,139,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	70345
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_AFCNT',0,19,144,4,3
	.word	75957
	.byte	27,19,147,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	70506
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_AG',0,19,152,4,3
	.word	76020
	.byte	27,19,155,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	71057
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_AGC',0,19,160,4,3
	.word	76080
	.byte	27,19,163,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	71319
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_AGCF',0,19,168,4,3
	.word	76141
	.byte	27,19,171,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	71874
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_AGFSP',0,19,176,4,3
	.word	76203
	.byte	27,19,179,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	72431
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_CLC',0,19,184,4,3
	.word	76266
	.byte	27,19,187,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	72568
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_CMD',0,19,192,4,3
	.word	76327
	.byte	27,19,195,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	72671
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_DBG',0,19,200,4,3
	.word	76388
	.byte	27,19,203,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	72759
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_FSP',0,19,208,4,3
	.word	76449
	.byte	27,19,211,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	72914
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_ID',0,19,216,4,3
	.word	76510
	.byte	27,19,219,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	73021
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_KEYS',0,19,224,4,3
	.word	76570
	.byte	27,19,227,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	73133
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_KRST0',0,19,232,4,3
	.word	76632
	.byte	27,19,235,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	73244
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_KRST1',0,19,240,4,3
	.word	76695
	.byte	27,19,243,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	73336
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_KRSTCLR',0,19,248,4,3
	.word	76758
	.byte	27,19,251,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	73432
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_OCS',0,19,128,5,3
	.word	76823
	.byte	27,19,131,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	73624
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_PCTL',0,19,136,5,3
	.word	76884
	.byte	27,19,139,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	73785
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_RMCTL',0,19,144,5,3
	.word	76946
	.byte	27,19,147,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	73876
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_RMEF',0,19,152,5,3
	.word	77009
	.byte	27,19,155,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	74431
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_RMSTS',0,19,160,5,3
	.word	77071
	.byte	27,19,163,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	75020
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_RTAC0',0,19,168,5,3
	.word	77134
	.byte	27,19,171,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	75207
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_RTAC1',0,19,176,5,3
	.word	77197
	.byte	27,19,179,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	75394
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_RTC',0,19,184,5,3
	.word	77260
	.byte	27,19,187,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	75514
	.byte	2,35,0,0,17
	.byte	'Ifx_SMU_STS',0,19,192,5,3
	.word	77321
	.byte	17
	.byte	'Port_n_PinType',0,5,203,2,3
	.word	3768
	.byte	17
	.byte	'Port_n_ControlType',0,5,233,2,2
	.word	3470
	.byte	17
	.byte	'Port_n_ConfigType',0,5,164,3,2
	.word	3446
	.byte	17
	.byte	'Port_n_PCSRConfigType',0,5,167,3,16
	.word	287
	.byte	17
	.byte	'Port_ConfigType',0,5,195,3,2
	.word	3424
	.byte	3
	.byte	'_Ifx_P_ACCEN0_Bits',0,20,45,16,4,10
	.byte	'EN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_P_ACCEN0_Bits',0,20,79,3
	.word	77517
	.byte	3
	.byte	'_Ifx_P_ACCEN1_Bits',0,20,82,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_P_ACCEN1_Bits',0,20,85,3
	.word	78070
	.byte	3
	.byte	'_Ifx_P_ESR_Bits',0,20,88,16,4,10
	.byte	'EN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_P_ESR_Bits',0,20,107,3
	.word	78143
	.byte	3
	.byte	'_Ifx_P_ID_Bits',0,20,110,16,4,10
	.byte	'MODREV',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'MODTYPE',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'MODNUMBER',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_P_ID_Bits',0,20,115,3
	.word	78457
	.byte	3
	.byte	'_Ifx_P_IN_Bits',0,20,118,16,4,10
	.byte	'P0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'P1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'P2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'P3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'P4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'P5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'P6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'P7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'P8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'P9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'P10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'P11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'P12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'P13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'P14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'P15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_P_IN_Bits',0,20,137,1,3
	.word	78558
	.byte	3
	.byte	'_Ifx_P_IOCR0_Bits',0,20,140,1,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'PC0',0,1
	.word	387
	.byte	5,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	3,5,2,35,1,10
	.byte	'PC1',0,1
	.word	387
	.byte	5,0,2,35,1,10
	.byte	'reserved_16',0,1
	.word	387
	.byte	3,5,2,35,2,10
	.byte	'PC2',0,1
	.word	387
	.byte	5,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'PC3',0,1
	.word	387
	.byte	5,0,2,35,3,0,17
	.byte	'Ifx_P_IOCR0_Bits',0,20,150,1,3
	.word	78855
	.byte	3
	.byte	'_Ifx_P_IOCR12_Bits',0,20,153,1,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'PC12',0,1
	.word	387
	.byte	5,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	3,5,2,35,1,10
	.byte	'PC13',0,1
	.word	387
	.byte	5,0,2,35,1,10
	.byte	'reserved_16',0,1
	.word	387
	.byte	3,5,2,35,2,10
	.byte	'PC14',0,1
	.word	387
	.byte	5,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'PC15',0,1
	.word	387
	.byte	5,0,2,35,3,0,17
	.byte	'Ifx_P_IOCR12_Bits',0,20,163,1,3
	.word	79056
	.byte	3
	.byte	'_Ifx_P_IOCR4_Bits',0,20,166,1,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'PC4',0,1
	.word	387
	.byte	5,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	3,5,2,35,1,10
	.byte	'PC5',0,1
	.word	387
	.byte	5,0,2,35,1,10
	.byte	'reserved_16',0,1
	.word	387
	.byte	3,5,2,35,2,10
	.byte	'PC6',0,1
	.word	387
	.byte	5,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'PC7',0,1
	.word	387
	.byte	5,0,2,35,3,0,17
	.byte	'Ifx_P_IOCR4_Bits',0,20,176,1,3
	.word	79263
	.byte	3
	.byte	'_Ifx_P_IOCR8_Bits',0,20,179,1,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'PC8',0,1
	.word	387
	.byte	5,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	3,5,2,35,1,10
	.byte	'PC9',0,1
	.word	387
	.byte	5,0,2,35,1,10
	.byte	'reserved_16',0,1
	.word	387
	.byte	3,5,2,35,2,10
	.byte	'PC10',0,1
	.word	387
	.byte	5,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'PC11',0,1
	.word	387
	.byte	5,0,2,35,3,0,17
	.byte	'Ifx_P_IOCR8_Bits',0,20,189,1,3
	.word	79464
	.byte	3
	.byte	'_Ifx_P_OMCR0_Bits',0,20,192,1,16,4,10
	.byte	'reserved_0',0,2
	.word	227
	.byte	16,0,2,35,0,10
	.byte	'PCL0',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'PCL1',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'PCL2',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'PCL3',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'reserved_20',0,2
	.word	227
	.byte	12,0,2,35,2,0,17
	.byte	'Ifx_P_OMCR0_Bits',0,20,200,1,3
	.word	79667
	.byte	3
	.byte	'_Ifx_P_OMCR12_Bits',0,20,203,1,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	28,4,2,35,2,10
	.byte	'PCL12',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'PCL13',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'PCL14',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'PCL15',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_P_OMCR12_Bits',0,20,210,1,3
	.word	79827
	.byte	3
	.byte	'_Ifx_P_OMCR4_Bits',0,20,213,1,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	20,12,2,35,2,10
	.byte	'PCL4',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'PCL5',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'PCL6',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'PCL7',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_P_OMCR4_Bits',0,20,221,1,3
	.word	79970
	.byte	3
	.byte	'_Ifx_P_OMCR8_Bits',0,20,224,1,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	24,8,2,35,2,10
	.byte	'PCL8',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'PCL9',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'PCL10',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'PCL11',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	387
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_P_OMCR8_Bits',0,20,232,1,3
	.word	80130
	.byte	3
	.byte	'_Ifx_P_OMCR_Bits',0,20,235,1,16,4,10
	.byte	'reserved_0',0,2
	.word	227
	.byte	16,0,2,35,0,10
	.byte	'PCL0',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'PCL1',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'PCL2',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'PCL3',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'PCL4',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'PCL5',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'PCL6',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'PCL7',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'PCL8',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'PCL9',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'PCL10',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'PCL11',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'PCL12',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'PCL13',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'PCL14',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'PCL15',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_P_OMCR_Bits',0,20,254,1,3
	.word	80292
	.byte	3
	.byte	'_Ifx_P_OMR_Bits',0,20,129,2,16,4,10
	.byte	'PS0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PS1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'PS2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'PS3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PS4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'PS5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'PS6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PS7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'PS8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'PS9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'PS10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'PS11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'PS12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'PS13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'PS14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'PS15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'PCL0',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'PCL1',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'PCL2',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'PCL3',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'PCL4',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'PCL5',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'PCL6',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'PCL7',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'PCL8',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'PCL9',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'PCL10',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'PCL11',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'PCL12',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'PCL13',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'PCL14',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'PCL15',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_P_OMR_Bits',0,20,163,2,3
	.word	80625
	.byte	3
	.byte	'_Ifx_P_OMSR0_Bits',0,20,166,2,16,4,10
	.byte	'PS0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PS1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'PS2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'PS3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	508
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_P_OMSR0_Bits',0,20,173,2,3
	.word	81180
	.byte	3
	.byte	'_Ifx_P_OMSR12_Bits',0,20,176,2,16,4,10
	.byte	'reserved_0',0,2
	.word	227
	.byte	12,4,2,35,0,10
	.byte	'PS12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'PS13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'PS14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'PS15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_P_OMSR12_Bits',0,20,184,2,3
	.word	81313
	.byte	3
	.byte	'_Ifx_P_OMSR4_Bits',0,20,187,2,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'PS4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'PS5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'PS6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PS7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	508
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_P_OMSR4_Bits',0,20,195,2,3
	.word	81475
	.byte	3
	.byte	'_Ifx_P_OMSR8_Bits',0,20,198,2,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'PS8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'PS9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'PS10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'PS11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'reserved_12',0,4
	.word	508
	.byte	20,0,2,35,2,0,17
	.byte	'Ifx_P_OMSR8_Bits',0,20,206,2,3
	.word	81630
	.byte	3
	.byte	'_Ifx_P_OMSR_Bits',0,20,209,2,16,4,10
	.byte	'PS0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PS1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'PS2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'PS3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PS4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'PS5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'PS6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PS7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'PS8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'PS9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'PS10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'PS11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'PS12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'PS13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'PS14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'PS15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_P_OMSR_Bits',0,20,228,2,3
	.word	81788
	.byte	3
	.byte	'_Ifx_P_OUT_Bits',0,20,231,2,16,4,10
	.byte	'P0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'P1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'P2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'P3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'P4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'P5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'P6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'P7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'P8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'P9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'P10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'P11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'P12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'P13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'P14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'P15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_P_OUT_Bits',0,20,250,2,3
	.word	82106
	.byte	3
	.byte	'_Ifx_P_PCSR_Bits',0,20,253,2,16,4,10
	.byte	'reserved_0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'SEL1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'SEL2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'reserved_3',0,2
	.word	227
	.byte	6,7,2,35,0,10
	.byte	'SEL9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'SEL10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'reserved_11',0,4
	.word	508
	.byte	20,1,2,35,2,10
	.byte	'LCK',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_P_PCSR_Bits',0,20,135,3,3
	.word	82406
	.byte	3
	.byte	'_Ifx_P_PDISC_Bits',0,20,138,3,16,4,10
	.byte	'PDIS0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'PDIS1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'PDIS2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'PDIS3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PDIS4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'PDIS5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'PDIS6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'PDIS7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'PDIS8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'PDIS9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'PDIS10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'PDIS11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'PDIS12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'PDIS13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'PDIS14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'PDIS15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'reserved_16',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_P_PDISC_Bits',0,20,157,3,3
	.word	82602
	.byte	3
	.byte	'_Ifx_P_PDR0_Bits',0,20,160,3,16,4,10
	.byte	'PD0',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'PL0',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PD1',0,1
	.word	387
	.byte	3,1,2,35,0,10
	.byte	'PL1',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'PD2',0,1
	.word	387
	.byte	3,5,2,35,1,10
	.byte	'PL2',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'PD3',0,1
	.word	387
	.byte	3,1,2,35,1,10
	.byte	'PL3',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'PD4',0,1
	.word	387
	.byte	3,5,2,35,2,10
	.byte	'PL4',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'PD5',0,1
	.word	387
	.byte	3,1,2,35,2,10
	.byte	'PL5',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'PD6',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'PL6',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'PD7',0,1
	.word	387
	.byte	3,1,2,35,3,10
	.byte	'PL7',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_P_PDR0_Bits',0,20,178,3,3
	.word	82954
	.byte	3
	.byte	'_Ifx_P_PDR1_Bits',0,20,181,3,16,4,10
	.byte	'PD8',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'PL8',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'PD9',0,1
	.word	387
	.byte	3,1,2,35,0,10
	.byte	'PL9',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'PD10',0,1
	.word	387
	.byte	3,5,2,35,1,10
	.byte	'PL10',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'PD11',0,1
	.word	387
	.byte	3,1,2,35,1,10
	.byte	'PL11',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'PD12',0,1
	.word	387
	.byte	3,5,2,35,2,10
	.byte	'PL12',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'PD13',0,1
	.word	387
	.byte	3,1,2,35,2,10
	.byte	'PL13',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'PD14',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'PL14',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'PD15',0,1
	.word	387
	.byte	3,1,2,35,3,10
	.byte	'PL15',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_P_PDR1_Bits',0,20,199,3,3
	.word	83243
	.byte	27,20,207,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	77517
	.byte	2,35,0,0,17
	.byte	'Ifx_P_ACCEN0',0,20,212,3,3
	.word	83544
	.byte	27,20,215,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	78070
	.byte	2,35,0,0,17
	.byte	'Ifx_P_ACCEN1',0,20,220,3,3
	.word	83606
	.byte	27,20,223,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	78143
	.byte	2,35,0,0,17
	.byte	'Ifx_P_ESR',0,20,228,3,3
	.word	83668
	.byte	27,20,231,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	78457
	.byte	2,35,0,0,17
	.byte	'Ifx_P_ID',0,20,236,3,3
	.word	83727
	.byte	27,20,239,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	78558
	.byte	2,35,0,0,17
	.byte	'Ifx_P_IN',0,20,244,3,3
	.word	83785
	.byte	27,20,247,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	78855
	.byte	2,35,0,0,17
	.byte	'Ifx_P_IOCR0',0,20,252,3,3
	.word	83843
	.byte	27,20,255,3,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	79056
	.byte	2,35,0,0,17
	.byte	'Ifx_P_IOCR12',0,20,132,4,3
	.word	83904
	.byte	27,20,135,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	79263
	.byte	2,35,0,0,17
	.byte	'Ifx_P_IOCR4',0,20,140,4,3
	.word	83966
	.byte	27,20,143,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	79464
	.byte	2,35,0,0,17
	.byte	'Ifx_P_IOCR8',0,20,148,4,3
	.word	84027
	.byte	27,20,151,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	80292
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMCR',0,20,156,4,3
	.word	84088
	.byte	27,20,159,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	79667
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMCR0',0,20,164,4,3
	.word	84148
	.byte	27,20,167,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	79827
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMCR12',0,20,172,4,3
	.word	84209
	.byte	27,20,175,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	79970
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMCR4',0,20,180,4,3
	.word	84271
	.byte	27,20,183,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	80130
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMCR8',0,20,188,4,3
	.word	84332
	.byte	27,20,191,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	80625
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMR',0,20,196,4,3
	.word	84393
	.byte	27,20,199,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	81788
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMSR',0,20,204,4,3
	.word	84452
	.byte	27,20,207,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	81180
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMSR0',0,20,212,4,3
	.word	84512
	.byte	27,20,215,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	81313
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMSR12',0,20,220,4,3
	.word	84573
	.byte	27,20,223,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	81475
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMSR4',0,20,228,4,3
	.word	84635
	.byte	27,20,231,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	81630
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OMSR8',0,20,236,4,3
	.word	84696
	.byte	27,20,239,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	82106
	.byte	2,35,0,0,17
	.byte	'Ifx_P_OUT',0,20,244,4,3
	.word	84757
	.byte	27,20,247,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	82406
	.byte	2,35,0,0,17
	.byte	'Ifx_P_PCSR',0,20,252,4,3
	.word	84816
	.byte	27,20,255,4,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	82602
	.byte	2,35,0,0,17
	.byte	'Ifx_P_PDISC',0,20,132,5,3
	.word	84876
	.byte	27,20,135,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	82954
	.byte	2,35,0,0,17
	.byte	'Ifx_P_PDR0',0,20,140,5,3
	.word	84937
	.byte	27,20,143,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	83243
	.byte	2,35,0,0,17
	.byte	'Ifx_P_PDR1',0,20,148,5,3
	.word	84997
	.byte	17
	.byte	'Dio_PortType',0,4,249,1,17
	.word	387
	.byte	17
	.byte	'Dio_PortLevelType',0,4,252,1,17
	.word	227
	.byte	17
	.byte	'Dio_ChannelGroupType',0,4,137,2,3
	.word	3236
	.byte	17
	.byte	'Dio_PortChannelIdType',0,4,145,2,2
	.word	3110
	.byte	17
	.byte	'Dio_ConfigType',0,4,179,2,2
	.word	3089
	.byte	19,10,215,3,9,1,15
	.byte	'DMA_CH_INCREMENT_DIR_NEG',0,0,15
	.byte	'DMA_CH_INCREMENT_DIR_POS',0,1,0,17
	.byte	'Dma_ChIncrementDirectionType',0,10,219,3,3
	.word	85191
	.byte	19,10,226,3,9,1,15
	.byte	'DMA_CH_ADDR_MOD_FACTOR_1',0,0,15
	.byte	'DMA_CH_ADDR_MOD_FACTOR_2',0,1,15
	.byte	'DMA_CH_ADDR_MOD_FACTOR_4',0,2,15
	.byte	'DMA_CH_ADDR_MOD_FACTOR_8',0,3,15
	.byte	'DMA_CH_ADDR_MOD_FACTOR_16',0,4,15
	.byte	'DMA_CH_ADDR_MOD_FACTOR_32',0,5,15
	.byte	'DMA_CH_ADDR_MOD_FACTOR_64',0,6,15
	.byte	'DMA_CH_ADDR_MOD_FACTOR_128',0,7,0,17
	.byte	'Dma_ChAddressModOffsetType',0,10,236,3,3
	.word	85290
	.byte	19,10,243,3,9,1,15
	.byte	'DMA_CH_CIRC_BUFF_DISABLE',0,0,15
	.byte	'DMA_CH_CIRC_BUFF_ENABLE',0,1,0,17
	.byte	'Dma_ChCircularBuffEnType',0,10,247,3,3
	.word	85554
	.byte	19,10,254,3,9,1,15
	.byte	'DMA_CH_WRAP_DISABLE',0,0,15
	.byte	'DMA_CH_WRAP_ENABLE',0,1,0,17
	.byte	'Dma_ChWrapEnType',0,10,130,4,3
	.word	85648
	.byte	19,10,133,4,9,1,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_1',0,0,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_2',0,1,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_4',0,2,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_8',0,3,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_16',0,4,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_32',0,5,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_64',0,6,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_128',0,7,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_256',0,8,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_512',0,9,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_1KB',0,10,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_2KB',0,11,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_4KB',0,12,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_8KB',0,13,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_16KB',0,14,15
	.byte	'DMA_CH_CIRC_BUFF_LEN_32KB',0,15,0,17
	.byte	'Dma_ChCircBuffSizeType',0,10,151,4,3
	.word	85724
	.byte	17
	.byte	'Dma_ChannelConfigType',0,10,181,4,2
	.word	9420
	.byte	17
	.byte	'Dma_ConfigType',0,10,195,4,2
	.word	9399
	.byte	3
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,21,45,16,4,10
	.byte	'EN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_DMA_ACCEN00_Bits',0,21,79,3
	.word	86241
	.byte	3
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,21,82,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_ACCEN01_Bits',0,21,85,3
	.word	86800
	.byte	3
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,21,88,16,4,10
	.byte	'EN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_DMA_ACCEN10_Bits',0,21,122,3
	.word	86879
	.byte	3
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,21,125,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_ACCEN11_Bits',0,21,128,1,3
	.word	87438
	.byte	3
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,21,131,1,16,4,10
	.byte	'EN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_DMA_ACCEN20_Bits',0,21,165,1,3
	.word	87518
	.byte	3
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,21,168,1,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_ACCEN21_Bits',0,21,171,1,3
	.word	88079
	.byte	3
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,21,174,1,16,4,10
	.byte	'EN0',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'EN1',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'EN2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'EN3',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'EN4',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'EN5',0,1
	.word	387
	.byte	1,2,2,35,0,10
	.byte	'EN6',0,1
	.word	387
	.byte	1,1,2,35,0,10
	.byte	'EN7',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'EN8',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'EN9',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'EN10',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'EN11',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'EN12',0,1
	.word	387
	.byte	1,3,2,35,1,10
	.byte	'EN13',0,1
	.word	387
	.byte	1,2,2,35,1,10
	.byte	'EN14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'EN15',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'EN16',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'EN17',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'EN18',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'EN19',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'EN20',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'EN21',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'EN22',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'EN23',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'EN24',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'EN25',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'EN26',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'EN27',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'EN28',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'EN29',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'EN30',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'EN31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_DMA_ACCEN30_Bits',0,21,208,1,3
	.word	88160
	.byte	3
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,21,211,1,16,4,10
	.byte	'reserved_0',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_ACCEN31_Bits',0,21,214,1,3
	.word	88721
	.byte	3
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,21,217,1,16,4,10
	.byte	'reserved_0',0,2
	.word	227
	.byte	16,0,2,35,0,10
	.byte	'CSER',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'CDER',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	387
	.byte	2,4,2,35,2,10
	.byte	'CSPBER',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'CSRIER',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'reserved_22',0,1
	.word	387
	.byte	2,0,2,35,2,10
	.byte	'CRAMER',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'CSLLER',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'CDLLER',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	387
	.byte	5,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,21,230,1,3
	.word	88802
	.byte	3
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,21,233,1,16,4,10
	.byte	'reserved_0',0,2
	.word	227
	.byte	16,0,2,35,0,10
	.byte	'ESER',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'EDER',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	387
	.byte	6,0,2,35,2,10
	.byte	'ERER',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'reserved_25',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'ELER',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	387
	.byte	5,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_EER_Bits',0,21,243,1,3
	.word	89076
	.byte	3
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,21,246,1,16,4,10
	.byte	'LEC',0,1
	.word	387
	.byte	7,1,2,35,0,10
	.byte	'reserved_7',0,2
	.word	227
	.byte	9,0,2,35,0,10
	.byte	'SER',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'DER',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'reserved_18',0,1
	.word	387
	.byte	2,4,2,35,2,10
	.byte	'SPBER',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'SRIER',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'reserved_22',0,1
	.word	387
	.byte	2,0,2,35,2,10
	.byte	'RAMER',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'SLLER',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'DLLER',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	387
	.byte	5,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,21,132,2,3
	.word	89290
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,21,135,2,16,4,10
	.byte	'SMF',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'INCS',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'DMF',0,1
	.word	387
	.byte	3,1,2,35,0,10
	.byte	'INCD',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'CBLS',0,1
	.word	387
	.byte	4,4,2,35,1,10
	.byte	'CBLD',0,1
	.word	387
	.byte	4,0,2,35,1,10
	.byte	'SHCT',0,1
	.word	387
	.byte	4,4,2,35,2,10
	.byte	'SCBE',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'DCBE',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'STAMP',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'ETRL',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'WRPSE',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'WRPDE',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'INTCT',0,1
	.word	387
	.byte	2,4,2,35,3,10
	.byte	'IRDV',0,1
	.word	387
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,21,152,2,3
	.word	89574
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,21,155,2,16,4,10
	.byte	'TREL',0,2
	.word	227
	.byte	14,2,2,35,0,10
	.byte	'reserved_14',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'BLKM',0,1
	.word	387
	.byte	3,5,2,35,2,10
	.byte	'RROAT',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'CHMODE',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'CHDW',0,1
	.word	387
	.byte	3,0,2,35,2,10
	.byte	'PATSEL',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'PRSEL',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'reserved_29',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'DMAPRIO',0,1
	.word	387
	.byte	2,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,21,168,2,3
	.word	89885
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,21,171,2,16,4,10
	.byte	'TCOUNT',0,2
	.word	227
	.byte	14,2,2,35,0,10
	.byte	'reserved_14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'LXO',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'WRPS',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'WRPD',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'ICH',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'IPM',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'reserved_20',0,1
	.word	387
	.byte	2,2,2,35,2,10
	.byte	'BUFFER',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'FROZEN',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'reserved_24',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,21,184,2,3
	.word	90158
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,21,187,2,16,4,10
	.byte	'DADR',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,21,190,2,3
	.word	90425
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,21,193,2,16,4,10
	.byte	'RD00',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'RD01',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'RD02',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'RD03',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,21,199,2,3
	.word	90508
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,21,202,2,16,4,10
	.byte	'RD10',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'RD11',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'RD12',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'RD13',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,21,208,2,3
	.word	90635
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,21,211,2,16,4,10
	.byte	'RD20',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'RD21',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'RD22',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'RD23',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,21,217,2,3
	.word	90762
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,21,220,2,16,4,10
	.byte	'RD30',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'RD31',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'RD32',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'RD33',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,21,226,2,3
	.word	90889
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,21,229,2,16,4,10
	.byte	'RD40',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'RD41',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'RD42',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'RD43',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,21,235,2,3
	.word	91016
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,21,238,2,16,4,10
	.byte	'RD50',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'RD51',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'RD52',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'RD53',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,21,244,2,3
	.word	91143
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,21,247,2,16,4,10
	.byte	'RD60',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'RD61',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'RD62',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'RD63',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,21,253,2,3
	.word	91270
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,21,128,3,16,4,10
	.byte	'RD70',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'RD71',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'RD72',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'RD73',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,21,134,3,3
	.word	91397
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,21,137,3,16,4,10
	.byte	'RDCRC',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,21,140,3,3
	.word	91524
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,21,143,3,16,4,10
	.byte	'SADR',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,21,146,3,3
	.word	91610
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,21,149,3,16,4,10
	.byte	'SDCRC',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,21,152,3,3
	.word	91693
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,21,155,3,16,4,10
	.byte	'SHADR',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,21,158,3,3
	.word	91779
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,21,161,3,16,4,10
	.byte	'RS',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,1
	.word	387
	.byte	3,4,2,35,0,10
	.byte	'WS',0,1
	.word	387
	.byte	1,3,2,35,0,10
	.byte	'reserved_5',0,2
	.word	227
	.byte	11,0,2,35,0,10
	.byte	'CH',0,1
	.word	387
	.byte	7,1,2,35,2,10
	.byte	'reserved_23',0,2
	.word	227
	.byte	9,0,2,35,2,0,17
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,21,169,3,3
	.word	91865
	.byte	3
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,21,172,3,16,4,10
	.byte	'SMF',0,1
	.word	387
	.byte	3,5,2,35,0,10
	.byte	'INCS',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'DMF',0,1
	.word	387
	.byte	3,1,2,35,0,10
	.byte	'INCD',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'CBLS',0,1
	.word	387
	.byte	4,4,2,35,1,10
	.byte	'CBLD',0,1
	.word	387
	.byte	4,0,2,35,1,10
	.byte	'SHCT',0,1
	.word	387
	.byte	4,4,2,35,2,10
	.byte	'SCBE',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'DCBE',0,1
	.word	387
	.byte	1,2,2,35,2,10
	.byte	'STAMP',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'ETRL',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'WRPSE',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'WRPDE',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'INTCT',0,1
	.word	387
	.byte	2,4,2,35,3,10
	.byte	'IRDV',0,1
	.word	387
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,21,189,3,3
	.word	92037
	.byte	3
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,21,192,3,16,4,10
	.byte	'TREL',0,2
	.word	227
	.byte	14,2,2,35,0,10
	.byte	'reserved_14',0,1
	.word	387
	.byte	2,0,2,35,1,10
	.byte	'BLKM',0,1
	.word	387
	.byte	3,5,2,35,2,10
	.byte	'RROAT',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'CHMODE',0,1
	.word	387
	.byte	1,3,2,35,2,10
	.byte	'CHDW',0,1
	.word	387
	.byte	3,0,2,35,2,10
	.byte	'PATSEL',0,1
	.word	387
	.byte	3,5,2,35,3,10
	.byte	'reserved_27',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'PRSEL',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'reserved_29',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'DMAPRIO',0,1
	.word	387
	.byte	2,0,2,35,3,0,17
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,21,205,3,3
	.word	92340
	.byte	3
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,21,208,3,16,4,10
	.byte	'TCOUNT',0,2
	.word	227
	.byte	14,2,2,35,0,10
	.byte	'reserved_14',0,1
	.word	387
	.byte	1,1,2,35,1,10
	.byte	'LXO',0,1
	.word	387
	.byte	1,0,2,35,1,10
	.byte	'WRPS',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'WRPD',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'ICH',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'IPM',0,1
	.word	387
	.byte	1,4,2,35,2,10
	.byte	'reserved_20',0,1
	.word	387
	.byte	2,2,2,35,2,10
	.byte	'BUFFER',0,1
	.word	387
	.byte	1,1,2,35,2,10
	.byte	'FROZEN',0,1
	.word	387
	.byte	1,0,2,35,2,10
	.byte	'SWB',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'CWRP',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'CICH',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'SIT',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'reserved_28',0,1
	.word	387
	.byte	3,1,2,35,3,10
	.byte	'SCH',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,21,226,3,3
	.word	92609
	.byte	3
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,21,229,3,16,4,10
	.byte	'DADR',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_CH_DADR_Bits',0,21,232,3,3
	.word	92947
	.byte	3
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,21,235,3,16,4,10
	.byte	'RDCRC',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,21,238,3,3
	.word	93022
	.byte	3
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,21,241,3,16,4,10
	.byte	'SADR',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_CH_SADR_Bits',0,21,244,3,3
	.word	93102
	.byte	3
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,21,247,3,16,4,10
	.byte	'SDCRC',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,21,250,3,3
	.word	93177
	.byte	3
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,21,253,3,16,4,10
	.byte	'SHADR',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,21,128,4,3
	.word	93257
	.byte	3
	.byte	'_Ifx_DMA_CLC_Bits',0,21,131,4,16,4,10
	.byte	'DISR',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'DISS',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'reserved_2',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'EDIS',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,4
	.word	508
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_DMA_CLC_Bits',0,21,138,4,3
	.word	93335
	.byte	3
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,21,141,4,16,4,10
	.byte	'SIT',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	508
	.byte	31,0,2,35,2,0,17
	.byte	'Ifx_DMA_ERRINTR_Bits',0,21,145,4,3
	.word	93478
	.byte	3
	.byte	'_Ifx_DMA_HRR_Bits',0,21,148,4,16,4,10
	.byte	'HRP',0,1
	.word	387
	.byte	2,6,2,35,0,10
	.byte	'reserved_2',0,4
	.word	508
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_DMA_HRR_Bits',0,21,152,4,3
	.word	93574
	.byte	3
	.byte	'_Ifx_DMA_ID_Bits',0,21,155,4,16,4,10
	.byte	'MODREV',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'MODTYPE',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'MODNUMBER',0,2
	.word	227
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_DMA_ID_Bits',0,21,160,4,3
	.word	93662
	.byte	3
	.byte	'_Ifx_DMA_MEMCON_Bits',0,21,163,4,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	2,30,2,35,0,10
	.byte	'INTERR',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'RMWERR',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'DATAERR',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'reserved_7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'PMIC',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'ERRDIS',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	26556
	.byte	22,0,2,35,0,0,17
	.byte	'Ifx_DMA_MEMCON_Bits',0,21,175,4,3
	.word	93769
	.byte	3
	.byte	'_Ifx_DMA_MODE_Bits',0,21,178,4,16,4,10
	.byte	'MODE',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	508
	.byte	31,0,2,35,2,0,17
	.byte	'Ifx_DMA_MODE_Bits',0,21,182,4,3
	.word	94026
	.byte	3
	.byte	'_Ifx_DMA_OTSS_Bits',0,21,185,4,16,4,10
	.byte	'TGS',0,1
	.word	387
	.byte	4,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	387
	.byte	3,1,2,35,0,10
	.byte	'BS',0,1
	.word	387
	.byte	1,0,2,35,0,10
	.byte	'reserved_8',0,4
	.word	508
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_DMA_OTSS_Bits',0,21,191,4,3
	.word	94117
	.byte	3
	.byte	'_Ifx_DMA_PRR0_Bits',0,21,194,4,16,4,10
	.byte	'PAT00',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'PAT01',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'PAT02',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'PAT03',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_PRR0_Bits',0,21,200,4,3
	.word	94243
	.byte	3
	.byte	'_Ifx_DMA_PRR1_Bits',0,21,203,4,16,4,10
	.byte	'PAT10',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'PAT11',0,1
	.word	387
	.byte	8,0,2,35,1,10
	.byte	'PAT12',0,1
	.word	387
	.byte	8,0,2,35,2,10
	.byte	'PAT13',0,1
	.word	387
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_DMA_PRR1_Bits',0,21,209,4,3
	.word	94364
	.byte	3
	.byte	'_Ifx_DMA_SUSACR_Bits',0,21,212,4,16,4,10
	.byte	'SUSAC',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	508
	.byte	31,0,2,35,2,0,17
	.byte	'Ifx_DMA_SUSACR_Bits',0,21,216,4,3
	.word	94485
	.byte	3
	.byte	'_Ifx_DMA_SUSENR_Bits',0,21,219,4,16,4,10
	.byte	'SUSEN',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'reserved_1',0,4
	.word	508
	.byte	31,0,2,35,2,0,17
	.byte	'Ifx_DMA_SUSENR_Bits',0,21,223,4,3
	.word	94581
	.byte	3
	.byte	'_Ifx_DMA_TIME_Bits',0,21,226,4,16,4,10
	.byte	'COUNT',0,4
	.word	508
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_DMA_TIME_Bits',0,21,229,4,3
	.word	94677
	.byte	3
	.byte	'_Ifx_DMA_TSR_Bits',0,21,232,4,16,4,10
	.byte	'RST',0,1
	.word	387
	.byte	1,7,2,35,0,10
	.byte	'HTRE',0,1
	.word	387
	.byte	1,6,2,35,0,10
	.byte	'TRL',0,1
	.word	387
	.byte	1,5,2,35,0,10
	.byte	'CH',0,1
	.word	387
	.byte	1,4,2,35,0,10
	.byte	'reserved_4',0,1
	.word	387
	.byte	4,0,2,35,0,10
	.byte	'HLTREQ',0,1
	.word	387
	.byte	1,7,2,35,1,10
	.byte	'HLTACK',0,1
	.word	387
	.byte	1,6,2,35,1,10
	.byte	'reserved_10',0,1
	.word	387
	.byte	6,0,2,35,1,10
	.byte	'ECH',0,1
	.word	387
	.byte	1,7,2,35,2,10
	.byte	'DCH',0,1
	.word	387
	.byte	1,6,2,35,2,10
	.byte	'CTL',0,1
	.word	387
	.byte	1,5,2,35,2,10
	.byte	'reserved_19',0,1
	.word	387
	.byte	5,0,2,35,2,10
	.byte	'HLTCLR',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'reserved_25',0,1
	.word	387
	.byte	7,0,2,35,3,0,17
	.byte	'Ifx_DMA_TSR_Bits',0,21,248,4,3
	.word	94747
	.byte	27,21,128,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	86241
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ACCEN00',0,21,133,5,3
	.word	95048
	.byte	27,21,136,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	86800
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ACCEN01',0,21,141,5,3
	.word	95113
	.byte	27,21,144,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	86879
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ACCEN10',0,21,149,5,3
	.word	95178
	.byte	27,21,152,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	87438
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ACCEN11',0,21,157,5,3
	.word	95243
	.byte	27,21,160,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	87518
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ACCEN20',0,21,165,5,3
	.word	95308
	.byte	27,21,168,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	88079
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ACCEN21',0,21,173,5,3
	.word	95373
	.byte	27,21,176,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	88160
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ACCEN30',0,21,181,5,3
	.word	95438
	.byte	27,21,184,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	88721
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ACCEN31',0,21,189,5,3
	.word	95503
	.byte	27,21,192,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	88802
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_CLRE',0,21,197,5,3
	.word	95568
	.byte	27,21,200,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	89076
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_EER',0,21,205,5,3
	.word	95634
	.byte	27,21,208,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	89290
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ERRSR',0,21,213,5,3
	.word	95699
	.byte	27,21,216,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	89574
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,21,221,5,3
	.word	95766
	.byte	27,21,224,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	89885
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,21,229,5,3
	.word	95836
	.byte	27,21,232,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	90158
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,21,237,5,3
	.word	95905
	.byte	27,21,240,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	90425
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_DADR',0,21,245,5,3
	.word	95974
	.byte	27,21,248,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	90508
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_R0',0,21,253,5,3
	.word	96043
	.byte	27,21,128,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	90635
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_R1',0,21,133,6,3
	.word	96110
	.byte	27,21,136,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	90762
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_R2',0,21,141,6,3
	.word	96177
	.byte	27,21,144,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	90889
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_R3',0,21,149,6,3
	.word	96244
	.byte	27,21,152,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91016
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_R4',0,21,157,6,3
	.word	96311
	.byte	27,21,160,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91143
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_R5',0,21,165,6,3
	.word	96378
	.byte	27,21,168,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91270
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_R6',0,21,173,6,3
	.word	96445
	.byte	27,21,176,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91397
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_R7',0,21,181,6,3
	.word	96512
	.byte	27,21,184,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91524
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,21,189,6,3
	.word	96579
	.byte	27,21,192,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91610
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_SADR',0,21,197,6,3
	.word	96649
	.byte	27,21,200,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91693
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,21,205,6,3
	.word	96718
	.byte	27,21,208,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91779
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,21,213,6,3
	.word	96788
	.byte	27,21,216,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	91865
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_BLK_ME_SR',0,21,221,6,3
	.word	96858
	.byte	27,21,224,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	92037
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CH_ADICR',0,21,229,6,3
	.word	96925
	.byte	27,21,232,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	92340
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CH_CHCFGR',0,21,237,6,3
	.word	96991
	.byte	27,21,240,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	92609
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CH_CHCSR',0,21,245,6,3
	.word	97058
	.byte	27,21,248,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	92947
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CH_DADR',0,21,253,6,3
	.word	97124
	.byte	27,21,128,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93022
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CH_RDCRCR',0,21,133,7,3
	.word	97189
	.byte	27,21,136,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93102
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CH_SADR',0,21,141,7,3
	.word	97256
	.byte	27,21,144,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93177
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CH_SDCRCR',0,21,149,7,3
	.word	97321
	.byte	27,21,152,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93257
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CH_SHADR',0,21,157,7,3
	.word	97388
	.byte	27,21,160,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93335
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_CLC',0,21,165,7,3
	.word	97454
	.byte	27,21,168,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93478
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ERRINTR',0,21,173,7,3
	.word	97515
	.byte	27,21,176,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93574
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_HRR',0,21,181,7,3
	.word	97580
	.byte	27,21,184,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93662
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_ID',0,21,189,7,3
	.word	97641
	.byte	27,21,192,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	93769
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_MEMCON',0,21,197,7,3
	.word	97701
	.byte	27,21,200,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	94026
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_MODE',0,21,205,7,3
	.word	97765
	.byte	27,21,208,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	94117
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_OTSS',0,21,213,7,3
	.word	97827
	.byte	27,21,216,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	94243
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_PRR0',0,21,221,7,3
	.word	97889
	.byte	27,21,224,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	94364
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_PRR1',0,21,229,7,3
	.word	97951
	.byte	27,21,232,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	94485
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_SUSACR',0,21,237,7,3
	.word	98013
	.byte	27,21,240,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	94581
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_SUSENR',0,21,245,7,3
	.word	98077
	.byte	27,21,248,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	94677
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_TIME',0,21,253,7,3
	.word	98141
	.byte	27,21,128,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	94747
	.byte	2,35,0,0,17
	.byte	'Ifx_DMA_TSR',0,21,133,8,3
	.word	98203
	.byte	3
	.byte	'_Ifx_DMA_BLK_ME',0,21,144,8,25,112,6
	.byte	'SR',0,4
	.word	96858
	.byte	2,35,0,6
	.byte	'reserved_4',0,12
	.word	64264
	.byte	2,35,4,6
	.byte	'R0',0,4
	.word	96043
	.byte	2,35,16,6
	.byte	'R1',0,4
	.word	96110
	.byte	2,35,20,6
	.byte	'R2',0,4
	.word	96177
	.byte	2,35,24,6
	.byte	'R3',0,4
	.word	96244
	.byte	2,35,28,6
	.byte	'R4',0,4
	.word	96311
	.byte	2,35,32,6
	.byte	'R5',0,4
	.word	96378
	.byte	2,35,36,6
	.byte	'R6',0,4
	.word	96445
	.byte	2,35,40,6
	.byte	'R7',0,4
	.word	96512
	.byte	2,35,44,7,32
	.word	387
	.byte	8,31,0,6
	.byte	'reserved_30',0,32
	.word	98414
	.byte	2,35,48,6
	.byte	'RDCRC',0,4
	.word	96579
	.byte	2,35,80,6
	.byte	'SDCRC',0,4
	.word	96718
	.byte	2,35,84,6
	.byte	'SADR',0,4
	.word	96649
	.byte	2,35,88,6
	.byte	'DADR',0,4
	.word	95974
	.byte	2,35,92,6
	.byte	'ADICR',0,4
	.word	95766
	.byte	2,35,96,6
	.byte	'CHCR',0,4
	.word	95836
	.byte	2,35,100,6
	.byte	'SHADR',0,4
	.word	96788
	.byte	2,35,104,6
	.byte	'CHSR',0,4
	.word	95905
	.byte	2,35,108,0,28
	.word	98264
	.byte	17
	.byte	'Ifx_DMA_BLK_ME',0,21,165,8,3
	.word	98561
	.byte	3
	.byte	'_Ifx_DMA_BLK',0,21,178,8,25,128,1,6
	.byte	'EER',0,4
	.word	95634
	.byte	2,35,0,6
	.byte	'ERRSR',0,4
	.word	95699
	.byte	2,35,4,6
	.byte	'CLRE',0,4
	.word	95568
	.byte	2,35,8,6
	.byte	'reserved_C',0,4
	.word	63664
	.byte	2,35,12,28
	.word	98264
	.byte	6
	.byte	'ME',0,112
	.word	98672
	.byte	2,35,16,0,28
	.word	98590
	.byte	17
	.byte	'Ifx_DMA_BLK',0,21,185,8,3
	.word	98690
	.byte	3
	.byte	'_Ifx_DMA_CH',0,21,188,8,25,32,6
	.byte	'RDCRCR',0,4
	.word	97189
	.byte	2,35,0,6
	.byte	'SDCRCR',0,4
	.word	97321
	.byte	2,35,4,6
	.byte	'SADR',0,4
	.word	97256
	.byte	2,35,8,6
	.byte	'DADR',0,4
	.word	97124
	.byte	2,35,12,6
	.byte	'ADICR',0,4
	.word	96925
	.byte	2,35,16,6
	.byte	'CHCFGR',0,4
	.word	96991
	.byte	2,35,20,6
	.byte	'SHADR',0,4
	.word	97388
	.byte	2,35,24,6
	.byte	'CHCSR',0,4
	.word	97058
	.byte	2,35,28,0,28
	.word	98716
	.byte	17
	.byte	'Ifx_DMA_CH',0,21,198,8,3
	.word	98856
	.byte	3
	.byte	'_Ifx_CPU_A_Bits',0,22,45,16,4,10
	.byte	'ADDR',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_A_Bits',0,22,48,3
	.word	98881
	.byte	3
	.byte	'_Ifx_CPU_BIV_Bits',0,22,51,16,4,10
	.byte	'VSS',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'BIV',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_CPU_BIV_Bits',0,22,55,3
	.word	98942
	.byte	3
	.byte	'_Ifx_CPU_BTV_Bits',0,22,58,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'BTV',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_CPU_BTV_Bits',0,22,62,3
	.word	99021
	.byte	3
	.byte	'_Ifx_CPU_CCNT_Bits',0,22,65,16,4,10
	.byte	'CountValue',0,4
	.word	26556
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_CPU_CCNT_Bits',0,22,69,3
	.word	99107
	.byte	3
	.byte	'_Ifx_CPU_CCTRL_Bits',0,22,72,16,4,10
	.byte	'CM',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'CE',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'M1',0,4
	.word	26556
	.byte	3,27,2,35,0,10
	.byte	'M2',0,4
	.word	26556
	.byte	3,24,2,35,0,10
	.byte	'M3',0,4
	.word	26556
	.byte	3,21,2,35,0,10
	.byte	'reserved_11',0,4
	.word	26556
	.byte	21,0,2,35,0,0,17
	.byte	'Ifx_CPU_CCTRL_Bits',0,22,80,3
	.word	99196
	.byte	3
	.byte	'_Ifx_CPU_COMPAT_Bits',0,22,83,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'RM',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'SP',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_CPU_COMPAT_Bits',0,22,89,3
	.word	99342
	.byte	3
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,22,92,16,4,10
	.byte	'CORE_ID',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	29,0,2,35,0,0,17
	.byte	'Ifx_CPU_CORE_ID_Bits',0,22,96,3
	.word	99469
	.byte	3
	.byte	'_Ifx_CPU_CPR_L_Bits',0,22,99,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'LOWBND',0,4
	.word	26556
	.byte	29,0,2,35,0,0,17
	.byte	'Ifx_CPU_CPR_L_Bits',0,22,103,3
	.word	99567
	.byte	3
	.byte	'_Ifx_CPU_CPR_U_Bits',0,22,106,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'UPPBND',0,4
	.word	26556
	.byte	29,0,2,35,0,0,17
	.byte	'Ifx_CPU_CPR_U_Bits',0,22,110,3
	.word	99660
	.byte	3
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,22,113,16,4,10
	.byte	'MODREV',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'MOD_32B',0,4
	.word	26556
	.byte	8,16,2,35,0,10
	.byte	'MOD',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_CPU_ID_Bits',0,22,118,3
	.word	99753
	.byte	3
	.byte	'_Ifx_CPU_CPXE_Bits',0,22,121,16,4,10
	.byte	'XE',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_CPU_CPXE_Bits',0,22,125,3
	.word	99860
	.byte	3
	.byte	'_Ifx_CPU_CREVT_Bits',0,22,128,1,16,4,10
	.byte	'EVTA',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'BBM',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'BOD',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'SUSP',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'CNT',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_CPU_CREVT_Bits',0,22,136,1,3
	.word	99947
	.byte	3
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,22,139,1,16,4,10
	.byte	'CID',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	29,0,2,35,0,0,17
	.byte	'Ifx_CPU_CUS_ID_Bits',0,22,143,1,3
	.word	100101
	.byte	3
	.byte	'_Ifx_CPU_D_Bits',0,22,146,1,16,4,10
	.byte	'DATA',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_D_Bits',0,22,149,1,3
	.word	100195
	.byte	3
	.byte	'_Ifx_CPU_DATR_Bits',0,22,152,1,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'SBE',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'reserved_4',0,4
	.word	26556
	.byte	5,23,2,35,0,10
	.byte	'CWE',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'CFE',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'reserved_11',0,4
	.word	26556
	.byte	3,18,2,35,0,10
	.byte	'SOE',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'SME',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_DATR_Bits',0,22,163,1,3
	.word	100258
	.byte	3
	.byte	'_Ifx_CPU_DBGSR_Bits',0,22,166,1,16,4,10
	.byte	'DE',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'HALT',0,4
	.word	26556
	.byte	2,29,2,35,0,10
	.byte	'SIH',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'SUSP',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'PREVSUSP',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'PEVT',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'EVTSRC',0,4
	.word	26556
	.byte	5,19,2,35,0,10
	.byte	'reserved_13',0,4
	.word	26556
	.byte	19,0,2,35,0,0,17
	.byte	'Ifx_CPU_DBGSR_Bits',0,22,177,1,3
	.word	100476
	.byte	3
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,22,180,1,16,4,10
	.byte	'DTA',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_CPU_DBGTCR_Bits',0,22,184,1,3
	.word	100691
	.byte	3
	.byte	'_Ifx_CPU_DCON0_Bits',0,22,187,1,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'DCBYP',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_CPU_DCON0_Bits',0,22,192,1,3
	.word	100785
	.byte	3
	.byte	'_Ifx_CPU_DCON2_Bits',0,22,195,1,16,4,10
	.byte	'DCACHE_SZE',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'DSCRATCH_SZE',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_DCON2_Bits',0,22,199,1,3
	.word	100901
	.byte	3
	.byte	'_Ifx_CPU_DCX_Bits',0,22,202,1,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	6,26,2,35,0,10
	.byte	'DCXValue',0,4
	.word	26556
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_CPU_DCX_Bits',0,22,206,1,3
	.word	101002
	.byte	3
	.byte	'_Ifx_CPU_DEADD_Bits',0,22,209,1,16,4,10
	.byte	'ERROR_ADDRESS',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_DEADD_Bits',0,22,212,1,3
	.word	101095
	.byte	3
	.byte	'_Ifx_CPU_DIEAR_Bits',0,22,215,1,16,4,10
	.byte	'TA',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_DIEAR_Bits',0,22,218,1,3
	.word	101175
	.byte	3
	.byte	'_Ifx_CPU_DIETR_Bits',0,22,221,1,16,4,10
	.byte	'IED',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'IE_T',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'IE_C',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'IE_S',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'IE_BI',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'E_INFO',0,4
	.word	26556
	.byte	6,21,2,35,0,10
	.byte	'IE_DUAL',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'IE_SP',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'IE_BS',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'reserved_14',0,4
	.word	26556
	.byte	18,0,2,35,0,0,17
	.byte	'Ifx_CPU_DIETR_Bits',0,22,233,1,3
	.word	101244
	.byte	3
	.byte	'_Ifx_CPU_DMS_Bits',0,22,236,1,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'DMSValue',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_CPU_DMS_Bits',0,22,240,1,3
	.word	101473
	.byte	3
	.byte	'_Ifx_CPU_DPR_L_Bits',0,22,243,1,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'LOWBND',0,4
	.word	26556
	.byte	29,0,2,35,0,0,17
	.byte	'Ifx_CPU_DPR_L_Bits',0,22,247,1,3
	.word	101566
	.byte	3
	.byte	'_Ifx_CPU_DPR_U_Bits',0,22,250,1,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'UPPBND',0,4
	.word	26556
	.byte	29,0,2,35,0,0,17
	.byte	'Ifx_CPU_DPR_U_Bits',0,22,254,1,3
	.word	101661
	.byte	3
	.byte	'_Ifx_CPU_DPRE_Bits',0,22,129,2,16,4,10
	.byte	'RE',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_DPRE_Bits',0,22,133,2,3
	.word	101756
	.byte	3
	.byte	'_Ifx_CPU_DPWE_Bits',0,22,136,2,16,4,10
	.byte	'WE',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_DPWE_Bits',0,22,140,2,3
	.word	101846
	.byte	3
	.byte	'_Ifx_CPU_DSTR_Bits',0,22,143,2,16,4,10
	.byte	'SRE',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'GAE',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'LBE',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	3,26,2,35,0,10
	.byte	'CRE',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'reserved_7',0,4
	.word	26556
	.byte	7,18,2,35,0,10
	.byte	'DTME',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'LOE',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'SDE',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'SCE',0,4
	.word	26556
	.byte	1,14,2,35,0,10
	.byte	'CAC',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'MPE',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'CLE',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'reserved_21',0,4
	.word	26556
	.byte	3,8,2,35,0,10
	.byte	'ALN',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'reserved_25',0,4
	.word	26556
	.byte	7,0,2,35,0,0,17
	.byte	'Ifx_CPU_DSTR_Bits',0,22,161,2,3
	.word	101936
	.byte	3
	.byte	'_Ifx_CPU_EXEVT_Bits',0,22,164,2,16,4,10
	.byte	'EVTA',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'BBM',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'BOD',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'SUSP',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'CNT',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_CPU_EXEVT_Bits',0,22,172,2,3
	.word	102260
	.byte	3
	.byte	'_Ifx_CPU_FCX_Bits',0,22,175,2,16,4,10
	.byte	'FCXO',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'FCXS',0,4
	.word	26556
	.byte	4,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	26556
	.byte	12,0,2,35,0,0,17
	.byte	'Ifx_CPU_FCX_Bits',0,22,180,2,3
	.word	102414
	.byte	3
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,22,183,2,16,4,10
	.byte	'TST',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TCL',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	6,24,2,35,0,10
	.byte	'RM',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	26556
	.byte	8,14,2,35,0,10
	.byte	'FXE',0,4
	.word	26556
	.byte	1,13,2,35,0,10
	.byte	'FUE',0,4
	.word	26556
	.byte	1,12,2,35,0,10
	.byte	'FZE',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'FVE',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'FIE',0,4
	.word	26556
	.byte	1,9,2,35,0,10
	.byte	'reserved_23',0,4
	.word	26556
	.byte	3,6,2,35,0,10
	.byte	'FX',0,4
	.word	26556
	.byte	1,5,2,35,0,10
	.byte	'FU',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'FZ',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'FV',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'FI',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'reserved_31',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,22,202,2,3
	.word	102520
	.byte	3
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,22,205,2,16,4,10
	.byte	'OPC',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'FMT',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'reserved_9',0,4
	.word	26556
	.byte	7,16,2,35,0,10
	.byte	'DREG',0,4
	.word	26556
	.byte	4,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	26556
	.byte	12,0,2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,22,212,2,3
	.word	102869
	.byte	3
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,22,215,2,16,4,10
	.byte	'PC',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,22,218,2,3
	.word	103029
	.byte	3
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,22,221,2,16,4,10
	.byte	'SRC1',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,22,224,2,3
	.word	103110
	.byte	3
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,22,227,2,16,4,10
	.byte	'SRC2',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,22,230,2,3
	.word	103197
	.byte	3
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,22,233,2,16,4,10
	.byte	'SRC3',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,22,236,2,3
	.word	103284
	.byte	3
	.byte	'_Ifx_CPU_ICNT_Bits',0,22,239,2,16,4,10
	.byte	'CountValue',0,4
	.word	26556
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_CPU_ICNT_Bits',0,22,243,2,3
	.word	103371
	.byte	3
	.byte	'_Ifx_CPU_ICR_Bits',0,22,246,2,16,4,10
	.byte	'CCPN',0,4
	.word	26556
	.byte	10,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	26556
	.byte	5,17,2,35,0,10
	.byte	'IE',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'PIPN',0,4
	.word	26556
	.byte	10,6,2,35,0,10
	.byte	'reserved_26',0,4
	.word	26556
	.byte	6,0,2,35,0,0,17
	.byte	'Ifx_CPU_ICR_Bits',0,22,253,2,3
	.word	103462
	.byte	3
	.byte	'_Ifx_CPU_ISP_Bits',0,22,128,3,16,4,10
	.byte	'ISP',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_ISP_Bits',0,22,131,3,3
	.word	103605
	.byte	3
	.byte	'_Ifx_CPU_LCX_Bits',0,22,134,3,16,4,10
	.byte	'LCXO',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'LCXS',0,4
	.word	26556
	.byte	4,12,2,35,0,10
	.byte	'reserved_20',0,4
	.word	26556
	.byte	12,0,2,35,0,0,17
	.byte	'Ifx_CPU_LCX_Bits',0,22,139,3,3
	.word	103671
	.byte	3
	.byte	'_Ifx_CPU_M1CNT_Bits',0,22,142,3,16,4,10
	.byte	'CountValue',0,4
	.word	26556
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_CPU_M1CNT_Bits',0,22,146,3,3
	.word	103777
	.byte	3
	.byte	'_Ifx_CPU_M2CNT_Bits',0,22,149,3,16,4,10
	.byte	'CountValue',0,4
	.word	26556
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_CPU_M2CNT_Bits',0,22,153,3,3
	.word	103870
	.byte	3
	.byte	'_Ifx_CPU_M3CNT_Bits',0,22,156,3,16,4,10
	.byte	'CountValue',0,4
	.word	26556
	.byte	31,1,2,35,0,10
	.byte	'SOvf',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_CPU_M3CNT_Bits',0,22,160,3,3
	.word	103963
	.byte	3
	.byte	'_Ifx_CPU_PC_Bits',0,22,163,3,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'PC',0,4
	.word	26556
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_CPU_PC_Bits',0,22,167,3,3
	.word	104056
	.byte	3
	.byte	'_Ifx_CPU_PCON0_Bits',0,22,170,3,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'PCBYP',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_CPU_PCON0_Bits',0,22,175,3,3
	.word	104141
	.byte	3
	.byte	'_Ifx_CPU_PCON1_Bits',0,22,178,3,16,4,10
	.byte	'PCINV',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'PBINV',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'reserved_2',0,4
	.word	26556
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_CPU_PCON1_Bits',0,22,183,3,3
	.word	104257
	.byte	3
	.byte	'_Ifx_CPU_PCON2_Bits',0,22,186,3,16,4,10
	.byte	'PCACHE_SZE',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'PSCRATCH_SZE',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_PCON2_Bits',0,22,190,3,3
	.word	104368
	.byte	3
	.byte	'_Ifx_CPU_PCXI_Bits',0,22,193,3,16,4,10
	.byte	'PCXO',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'PCXS',0,4
	.word	26556
	.byte	4,12,2,35,0,10
	.byte	'UL',0,4
	.word	26556
	.byte	1,11,2,35,0,10
	.byte	'PIE',0,4
	.word	26556
	.byte	1,10,2,35,0,10
	.byte	'PCPN',0,4
	.word	26556
	.byte	10,0,2,35,0,0,17
	.byte	'Ifx_CPU_PCXI_Bits',0,22,200,3,3
	.word	104469
	.byte	3
	.byte	'_Ifx_CPU_PIEAR_Bits',0,22,203,3,16,4,10
	.byte	'TA',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_PIEAR_Bits',0,22,206,3,3
	.word	104599
	.byte	3
	.byte	'_Ifx_CPU_PIETR_Bits',0,22,209,3,16,4,10
	.byte	'IED',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'IE_T',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'IE_C',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'IE_S',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'IE_BI',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'E_INFO',0,4
	.word	26556
	.byte	6,21,2,35,0,10
	.byte	'IE_DUAL',0,4
	.word	26556
	.byte	1,20,2,35,0,10
	.byte	'IE_SP',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'IE_BS',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'reserved_14',0,4
	.word	26556
	.byte	18,0,2,35,0,0,17
	.byte	'Ifx_CPU_PIETR_Bits',0,22,221,3,3
	.word	104668
	.byte	3
	.byte	'_Ifx_CPU_PMA0_Bits',0,22,224,3,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	13,19,2,35,0,10
	.byte	'DAC',0,4
	.word	26556
	.byte	3,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_PMA0_Bits',0,22,229,3,3
	.word	104897
	.byte	3
	.byte	'_Ifx_CPU_PMA1_Bits',0,22,232,3,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	14,18,2,35,0,10
	.byte	'CAC',0,4
	.word	26556
	.byte	2,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_PMA1_Bits',0,22,237,3,3
	.word	105010
	.byte	3
	.byte	'_Ifx_CPU_PMA2_Bits',0,22,240,3,16,4,10
	.byte	'PSI',0,4
	.word	26556
	.byte	16,16,2,35,0,10
	.byte	'reserved_16',0,4
	.word	26556
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_CPU_PMA2_Bits',0,22,244,3,3
	.word	105123
	.byte	3
	.byte	'_Ifx_CPU_PSTR_Bits',0,22,247,3,16,4,10
	.byte	'FRE',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'FBE',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	9,20,2,35,0,10
	.byte	'FPE',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'reserved_13',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'FME',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'reserved_15',0,4
	.word	26556
	.byte	17,0,2,35,0,0,17
	.byte	'Ifx_CPU_PSTR_Bits',0,22,129,4,3
	.word	105214
	.byte	3
	.byte	'_Ifx_CPU_PSW_Bits',0,22,132,4,16,4,10
	.byte	'CDC',0,4
	.word	26556
	.byte	7,25,2,35,0,10
	.byte	'CDE',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'GW',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'IS',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'IO',0,4
	.word	26556
	.byte	2,20,2,35,0,10
	.byte	'PRS',0,4
	.word	26556
	.byte	2,18,2,35,0,10
	.byte	'S',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'reserved_15',0,4
	.word	26556
	.byte	12,5,2,35,0,10
	.byte	'SAV',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'AV',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'SV',0,4
	.word	26556
	.byte	1,2,2,35,0,10
	.byte	'V',0,4
	.word	26556
	.byte	1,1,2,35,0,10
	.byte	'C',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_CPU_PSW_Bits',0,22,147,4,3
	.word	105417
	.byte	3
	.byte	'_Ifx_CPU_SEGEN_Bits',0,22,150,4,16,4,10
	.byte	'ADFLIP',0,4
	.word	26556
	.byte	8,24,2,35,0,10
	.byte	'ADTYPE',0,4
	.word	26556
	.byte	2,22,2,35,0,10
	.byte	'reserved_10',0,4
	.word	26556
	.byte	21,1,2,35,0,10
	.byte	'AE',0,4
	.word	26556
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_CPU_SEGEN_Bits',0,22,156,4,3
	.word	105660
	.byte	3
	.byte	'_Ifx_CPU_SMACON_Bits',0,22,159,4,16,4,10
	.byte	'PC',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'reserved_1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'PT',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	5,24,2,35,0,10
	.byte	'DC',0,4
	.word	26556
	.byte	1,23,2,35,0,10
	.byte	'reserved_9',0,4
	.word	26556
	.byte	1,22,2,35,0,10
	.byte	'DT',0,4
	.word	26556
	.byte	1,21,2,35,0,10
	.byte	'reserved_11',0,4
	.word	26556
	.byte	13,8,2,35,0,10
	.byte	'IODT',0,4
	.word	26556
	.byte	1,7,2,35,0,10
	.byte	'reserved_25',0,4
	.word	26556
	.byte	7,0,2,35,0,0,17
	.byte	'Ifx_CPU_SMACON_Bits',0,22,171,4,3
	.word	105788
	.byte	3
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,22,174,4,16,4,10
	.byte	'EN',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,22,177,4,3
	.word	106029
	.byte	3
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,22,180,4,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,22,183,4,3
	.word	106112
	.byte	3
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,22,186,4,16,4,10
	.byte	'EN',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,22,189,4,3
	.word	106203
	.byte	3
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,22,192,4,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,22,195,4,3
	.word	106294
	.byte	3
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,22,198,4,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	5,27,2,35,0,10
	.byte	'ADDR',0,4
	.word	26556
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,22,202,4,3
	.word	106393
	.byte	3
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,22,205,4,16,4,10
	.byte	'reserved_0',0,4
	.word	26556
	.byte	5,27,2,35,0,10
	.byte	'ADDR',0,4
	.word	26556
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,22,209,4,3
	.word	106500
	.byte	3
	.byte	'_Ifx_CPU_SWEVT_Bits',0,22,212,4,16,4,10
	.byte	'EVTA',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'BBM',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'BOD',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'SUSP',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'CNT',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_CPU_SWEVT_Bits',0,22,220,4,3
	.word	106607
	.byte	3
	.byte	'_Ifx_CPU_SYSCON_Bits',0,22,223,4,16,4,10
	.byte	'FCDSF',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'PROTEN',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'TPROTEN',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'IS',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'IT',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_CPU_SYSCON_Bits',0,22,231,4,3
	.word	106761
	.byte	3
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,22,234,4,16,4,10
	.byte	'ASI',0,4
	.word	26556
	.byte	5,27,2,35,0,10
	.byte	'reserved_5',0,4
	.word	26556
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,22,238,4,3
	.word	106922
	.byte	3
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,22,241,4,16,4,10
	.byte	'TEXP0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'TEXP1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'TEXP2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'reserved_3',0,4
	.word	26556
	.byte	13,16,2,35,0,10
	.byte	'TTRAP',0,4
	.word	26556
	.byte	1,15,2,35,0,10
	.byte	'reserved_17',0,4
	.word	26556
	.byte	15,0,2,35,0,0,17
	.byte	'Ifx_CPU_TPS_CON_Bits',0,22,249,4,3
	.word	107020
	.byte	3
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,22,252,4,16,4,10
	.byte	'Timer',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,22,255,4,3
	.word	107192
	.byte	3
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,22,130,5,16,4,10
	.byte	'ADDR',0,4
	.word	26556
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_CPU_TR_ADR_Bits',0,22,133,5,3
	.word	107272
	.byte	3
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,22,136,5,16,4,10
	.byte	'EVTA',0,4
	.word	26556
	.byte	3,29,2,35,0,10
	.byte	'BBM',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'BOD',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'SUSP',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'CNT',0,4
	.word	26556
	.byte	2,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	4,20,2,35,0,10
	.byte	'TYP',0,4
	.word	26556
	.byte	1,19,2,35,0,10
	.byte	'RNG',0,4
	.word	26556
	.byte	1,18,2,35,0,10
	.byte	'reserved_14',0,4
	.word	26556
	.byte	1,17,2,35,0,10
	.byte	'ASI_EN',0,4
	.word	26556
	.byte	1,16,2,35,0,10
	.byte	'ASI',0,4
	.word	26556
	.byte	5,11,2,35,0,10
	.byte	'reserved_21',0,4
	.word	26556
	.byte	6,5,2,35,0,10
	.byte	'AST',0,4
	.word	26556
	.byte	1,4,2,35,0,10
	.byte	'ALD',0,4
	.word	26556
	.byte	1,3,2,35,0,10
	.byte	'reserved_29',0,4
	.word	26556
	.byte	3,0,2,35,0,0,17
	.byte	'Ifx_CPU_TR_EVT_Bits',0,22,153,5,3
	.word	107345
	.byte	3
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,22,156,5,16,4,10
	.byte	'T0',0,4
	.word	26556
	.byte	1,31,2,35,0,10
	.byte	'T1',0,4
	.word	26556
	.byte	1,30,2,35,0,10
	.byte	'T2',0,4
	.word	26556
	.byte	1,29,2,35,0,10
	.byte	'T3',0,4
	.word	26556
	.byte	1,28,2,35,0,10
	.byte	'T4',0,4
	.word	26556
	.byte	1,27,2,35,0,10
	.byte	'T5',0,4
	.word	26556
	.byte	1,26,2,35,0,10
	.byte	'T6',0,4
	.word	26556
	.byte	1,25,2,35,0,10
	.byte	'T7',0,4
	.word	26556
	.byte	1,24,2,35,0,10
	.byte	'reserved_8',0,4
	.word	26556
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,22,167,5,3
	.word	107663
	.byte	27,22,175,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	98881
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_A',0,22,180,5,3
	.word	107858
	.byte	27,22,183,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	98942
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_BIV',0,22,188,5,3
	.word	107917
	.byte	27,22,191,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99021
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_BTV',0,22,196,5,3
	.word	107978
	.byte	27,22,199,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99107
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CCNT',0,22,204,5,3
	.word	108039
	.byte	27,22,207,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99196
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CCTRL',0,22,212,5,3
	.word	108101
	.byte	27,22,215,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99342
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_COMPAT',0,22,220,5,3
	.word	108164
	.byte	27,22,223,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99469
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CORE_ID',0,22,228,5,3
	.word	108228
	.byte	27,22,231,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99567
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CPR_L',0,22,236,5,3
	.word	108293
	.byte	27,22,239,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99660
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CPR_U',0,22,244,5,3
	.word	108356
	.byte	27,22,247,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99753
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CPU_ID',0,22,252,5,3
	.word	108419
	.byte	27,22,255,5,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99860
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CPXE',0,22,132,6,3
	.word	108483
	.byte	27,22,135,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	99947
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CREVT',0,22,140,6,3
	.word	108545
	.byte	27,22,143,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	100101
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_CUS_ID',0,22,148,6,3
	.word	108608
	.byte	27,22,151,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	100195
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_D',0,22,156,6,3
	.word	108672
	.byte	27,22,159,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	100258
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DATR',0,22,164,6,3
	.word	108731
	.byte	27,22,167,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	100476
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DBGSR',0,22,172,6,3
	.word	108793
	.byte	27,22,175,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	100691
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DBGTCR',0,22,180,6,3
	.word	108856
	.byte	27,22,183,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	100785
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DCON0',0,22,188,6,3
	.word	108920
	.byte	27,22,191,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	100901
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DCON2',0,22,196,6,3
	.word	108983
	.byte	27,22,199,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101002
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DCX',0,22,204,6,3
	.word	109046
	.byte	27,22,207,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101095
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DEADD',0,22,212,6,3
	.word	109107
	.byte	27,22,215,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101175
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DIEAR',0,22,220,6,3
	.word	109170
	.byte	27,22,223,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101244
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DIETR',0,22,228,6,3
	.word	109233
	.byte	27,22,231,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101473
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DMS',0,22,236,6,3
	.word	109296
	.byte	27,22,239,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101566
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DPR_L',0,22,244,6,3
	.word	109357
	.byte	27,22,247,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101661
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DPR_U',0,22,252,6,3
	.word	109420
	.byte	27,22,255,6,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101756
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DPRE',0,22,132,7,3
	.word	109483
	.byte	27,22,135,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101846
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DPWE',0,22,140,7,3
	.word	109545
	.byte	27,22,143,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	101936
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_DSTR',0,22,148,7,3
	.word	109607
	.byte	27,22,151,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	102260
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_EXEVT',0,22,156,7,3
	.word	109669
	.byte	27,22,159,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	102414
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_FCX',0,22,164,7,3
	.word	109732
	.byte	27,22,167,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	102520
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,22,172,7,3
	.word	109793
	.byte	27,22,175,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	102869
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,22,180,7,3
	.word	109863
	.byte	27,22,183,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103029
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,22,188,7,3
	.word	109933
	.byte	27,22,191,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103110
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,22,196,7,3
	.word	110002
	.byte	27,22,199,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103197
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,22,204,7,3
	.word	110073
	.byte	27,22,207,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103284
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,22,212,7,3
	.word	110144
	.byte	27,22,215,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103371
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_ICNT',0,22,220,7,3
	.word	110215
	.byte	27,22,223,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103462
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_ICR',0,22,228,7,3
	.word	110277
	.byte	27,22,231,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103605
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_ISP',0,22,236,7,3
	.word	110338
	.byte	27,22,239,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103671
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_LCX',0,22,244,7,3
	.word	110399
	.byte	27,22,247,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103777
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_M1CNT',0,22,252,7,3
	.word	110460
	.byte	27,22,255,7,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103870
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_M2CNT',0,22,132,8,3
	.word	110523
	.byte	27,22,135,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	103963
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_M3CNT',0,22,140,8,3
	.word	110586
	.byte	27,22,143,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	104056
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PC',0,22,148,8,3
	.word	110649
	.byte	27,22,151,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	104141
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PCON0',0,22,156,8,3
	.word	110709
	.byte	27,22,159,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	104257
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PCON1',0,22,164,8,3
	.word	110772
	.byte	27,22,167,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	104368
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PCON2',0,22,172,8,3
	.word	110835
	.byte	27,22,175,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	104469
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PCXI',0,22,180,8,3
	.word	110898
	.byte	27,22,183,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	104599
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PIEAR',0,22,188,8,3
	.word	110960
	.byte	27,22,191,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	104668
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PIETR',0,22,196,8,3
	.word	111023
	.byte	27,22,199,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	104897
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PMA0',0,22,204,8,3
	.word	111086
	.byte	27,22,207,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	105010
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PMA1',0,22,212,8,3
	.word	111148
	.byte	27,22,215,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	105123
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PMA2',0,22,220,8,3
	.word	111210
	.byte	27,22,223,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	105214
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PSTR',0,22,228,8,3
	.word	111272
	.byte	27,22,231,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	105417
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_PSW',0,22,236,8,3
	.word	111334
	.byte	27,22,239,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	105660
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SEGEN',0,22,244,8,3
	.word	111395
	.byte	27,22,247,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	105788
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SMACON',0,22,252,8,3
	.word	111458
	.byte	27,22,255,8,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106029
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_ACCENA',0,22,132,9,3
	.word	111522
	.byte	27,22,135,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106112
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_ACCENB',0,22,140,9,3
	.word	111592
	.byte	27,22,143,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106203
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,22,148,9,3
	.word	111662
	.byte	27,22,151,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106294
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,22,156,9,3
	.word	111736
	.byte	27,22,159,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106393
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,22,164,9,3
	.word	111810
	.byte	27,22,167,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106500
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,22,172,9,3
	.word	111880
	.byte	27,22,175,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106607
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SWEVT',0,22,180,9,3
	.word	111950
	.byte	27,22,183,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106761
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_SYSCON',0,22,188,9,3
	.word	112013
	.byte	27,22,191,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	106922
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_TASK_ASI',0,22,196,9,3
	.word	112077
	.byte	27,22,199,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	107020
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_TPS_CON',0,22,204,9,3
	.word	112143
	.byte	27,22,207,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	107192
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_TPS_TIMER',0,22,212,9,3
	.word	112208
	.byte	27,22,215,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	107272
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_TR_ADR',0,22,220,9,3
	.word	112275
	.byte	27,22,223,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	107345
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_TR_EVT',0,22,228,9,3
	.word	112339
	.byte	27,22,231,9,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	107663
	.byte	2,35,0,0,17
	.byte	'Ifx_CPU_TRIG_ACC',0,22,236,9,3
	.word	112403
	.byte	3
	.byte	'_Ifx_CPU_CPR',0,22,247,9,25,8,6
	.byte	'L',0,4
	.word	108293
	.byte	2,35,0,6
	.byte	'U',0,4
	.word	108356
	.byte	2,35,4,0,28
	.word	112469
	.byte	17
	.byte	'Ifx_CPU_CPR',0,22,251,9,3
	.word	112511
	.byte	3
	.byte	'_Ifx_CPU_DPR',0,22,254,9,25,8,6
	.byte	'L',0,4
	.word	109357
	.byte	2,35,0,6
	.byte	'U',0,4
	.word	109420
	.byte	2,35,4,0,28
	.word	112537
	.byte	17
	.byte	'Ifx_CPU_DPR',0,22,130,10,3
	.word	112579
	.byte	3
	.byte	'_Ifx_CPU_SPROT_RGN',0,22,133,10,25,16,6
	.byte	'LA',0,4
	.word	111810
	.byte	2,35,0,6
	.byte	'UA',0,4
	.word	111880
	.byte	2,35,4,6
	.byte	'ACCENA',0,4
	.word	111662
	.byte	2,35,8,6
	.byte	'ACCENB',0,4
	.word	111736
	.byte	2,35,12,0,28
	.word	112605
	.byte	17
	.byte	'Ifx_CPU_SPROT_RGN',0,22,139,10,3
	.word	112687
	.byte	3
	.byte	'_Ifx_CPU_TPS',0,22,142,10,25,16,6
	.byte	'CON',0,4
	.word	112143
	.byte	2,35,0,7,12
	.word	112208
	.byte	8,2,0,6
	.byte	'TIMER',0,12
	.word	112751
	.byte	2,35,4,0,28
	.word	112719
	.byte	17
	.byte	'Ifx_CPU_TPS',0,22,146,10,3
	.word	112776
	.byte	3
	.byte	'_Ifx_CPU_TR',0,22,149,10,25,8,6
	.byte	'EVT',0,4
	.word	112339
	.byte	2,35,0,6
	.byte	'ADR',0,4
	.word	112275
	.byte	2,35,4,0,28
	.word	112802
	.byte	17
	.byte	'Ifx_CPU_TR',0,22,153,10,3
	.word	112847
	.byte	3
	.byte	'_Ifx_SRC_SRCR_Bits',0,23,45,16,4,10
	.byte	'SRPN',0,1
	.word	387
	.byte	8,0,2,35,0,10
	.byte	'reserved_8',0,1
	.word	387
	.byte	2,6,2,35,1,10
	.byte	'SRE',0,1
	.word	387
	.byte	1,5,2,35,1,10
	.byte	'TOS',0,1
	.word	387
	.byte	1,4,2,35,1,10
	.byte	'reserved_12',0,1
	.word	387
	.byte	4,0,2,35,1,10
	.byte	'ECC',0,1
	.word	387
	.byte	5,3,2,35,2,10
	.byte	'reserved_21',0,1
	.word	387
	.byte	3,0,2,35,2,10
	.byte	'SRR',0,1
	.word	387
	.byte	1,7,2,35,3,10
	.byte	'CLRR',0,1
	.word	387
	.byte	1,6,2,35,3,10
	.byte	'SETR',0,1
	.word	387
	.byte	1,5,2,35,3,10
	.byte	'IOV',0,1
	.word	387
	.byte	1,4,2,35,3,10
	.byte	'IOVCLR',0,1
	.word	387
	.byte	1,3,2,35,3,10
	.byte	'SWS',0,1
	.word	387
	.byte	1,2,2,35,3,10
	.byte	'SWSCLR',0,1
	.word	387
	.byte	1,1,2,35,3,10
	.byte	'reserved_31',0,1
	.word	387
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SRC_SRCR_Bits',0,23,62,3
	.word	112872
	.byte	27,23,70,9,4,6
	.byte	'U',0,4
	.word	508
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	27745
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	112872
	.byte	2,35,0,0,17
	.byte	'Ifx_SRC_SRCR',0,23,75,3
	.word	113188
	.byte	3
	.byte	'_Ifx_SRC_ASCLIN',0,23,86,25,12,6
	.byte	'TX',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'RX',0,4
	.word	113188
	.byte	2,35,4,6
	.byte	'ERR',0,4
	.word	113188
	.byte	2,35,8,0,28
	.word	113248
	.byte	17
	.byte	'Ifx_SRC_ASCLIN',0,23,91,3
	.word	113307
	.byte	3
	.byte	'_Ifx_SRC_BCUSPB',0,23,94,25,4,6
	.byte	'SBSRC',0,4
	.word	113188
	.byte	2,35,0,0,28
	.word	113335
	.byte	17
	.byte	'Ifx_SRC_BCUSPB',0,23,97,3
	.word	113372
	.byte	3
	.byte	'_Ifx_SRC_CAN',0,23,100,25,64,7,64
	.word	113188
	.byte	8,15,0,6
	.byte	'INT',0,64
	.word	113418
	.byte	2,35,0,0,28
	.word	113400
	.byte	17
	.byte	'Ifx_SRC_CAN',0,23,103,3
	.word	113441
	.byte	3
	.byte	'_Ifx_SRC_CAN1',0,23,106,25,32,7,32
	.word	113188
	.byte	8,7,0,6
	.byte	'INT',0,32
	.word	113485
	.byte	2,35,0,0,28
	.word	113466
	.byte	17
	.byte	'Ifx_SRC_CAN1',0,23,109,3
	.word	113508
	.byte	3
	.byte	'_Ifx_SRC_CCU6',0,23,112,25,16,6
	.byte	'SR0',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'SR1',0,4
	.word	113188
	.byte	2,35,4,6
	.byte	'SR2',0,4
	.word	113188
	.byte	2,35,8,6
	.byte	'SR3',0,4
	.word	113188
	.byte	2,35,12,0,28
	.word	113534
	.byte	17
	.byte	'Ifx_SRC_CCU6',0,23,118,3
	.word	113606
	.byte	3
	.byte	'_Ifx_SRC_CERBERUS',0,23,121,25,8,7,8
	.word	113188
	.byte	8,1,0,6
	.byte	'SR',0,8
	.word	113655
	.byte	2,35,0,0,28
	.word	113632
	.byte	17
	.byte	'Ifx_SRC_CERBERUS',0,23,124,3
	.word	113677
	.byte	3
	.byte	'_Ifx_SRC_CPU',0,23,127,25,32,6
	.byte	'SBSRC',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'reserved_4',0,28
	.word	63077
	.byte	2,35,4,0,28
	.word	113707
	.byte	17
	.byte	'Ifx_SRC_CPU',0,23,131,1,3
	.word	113761
	.byte	3
	.byte	'_Ifx_SRC_DMA',0,23,134,1,25,80,6
	.byte	'ERR',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'reserved_4',0,12
	.word	64264
	.byte	2,35,4,6
	.byte	'CH',0,64
	.word	113418
	.byte	2,35,16,0,28
	.word	113787
	.byte	17
	.byte	'Ifx_SRC_DMA',0,23,139,1,3
	.word	113852
	.byte	3
	.byte	'_Ifx_SRC_EMEM',0,23,142,1,25,4,6
	.byte	'SR',0,4
	.word	113188
	.byte	2,35,0,0,28
	.word	113878
	.byte	17
	.byte	'Ifx_SRC_EMEM',0,23,145,1,3
	.word	113911
	.byte	3
	.byte	'_Ifx_SRC_ERAY',0,23,148,1,25,80,6
	.byte	'INT',0,8
	.word	113655
	.byte	2,35,0,6
	.byte	'TINT',0,8
	.word	113655
	.byte	2,35,8,6
	.byte	'NDAT',0,8
	.word	113655
	.byte	2,35,16,6
	.byte	'MBSC',0,8
	.word	113655
	.byte	2,35,24,6
	.byte	'OBUSY',0,4
	.word	113188
	.byte	2,35,32,6
	.byte	'IBUSY',0,4
	.word	113188
	.byte	2,35,36,7,40
	.word	387
	.byte	8,39,0,6
	.byte	'reserved_28',0,40
	.word	114043
	.byte	2,35,40,0,28
	.word	113938
	.byte	17
	.byte	'Ifx_SRC_ERAY',0,23,157,1,3
	.word	114074
	.byte	3
	.byte	'_Ifx_SRC_ETH',0,23,160,1,25,4,6
	.byte	'SR',0,4
	.word	113188
	.byte	2,35,0,0,28
	.word	114101
	.byte	17
	.byte	'Ifx_SRC_ETH',0,23,163,1,3
	.word	114133
	.byte	3
	.byte	'_Ifx_SRC_EVR',0,23,166,1,25,8,6
	.byte	'WUT',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'SCDC',0,4
	.word	113188
	.byte	2,35,4,0,28
	.word	114159
	.byte	17
	.byte	'Ifx_SRC_EVR',0,23,170,1,3
	.word	114206
	.byte	3
	.byte	'_Ifx_SRC_FFT',0,23,173,1,25,12,6
	.byte	'DONE',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'ERR',0,4
	.word	113188
	.byte	2,35,4,6
	.byte	'RFS',0,4
	.word	113188
	.byte	2,35,8,0,28
	.word	114232
	.byte	17
	.byte	'Ifx_SRC_FFT',0,23,178,1,3
	.word	114292
	.byte	3
	.byte	'_Ifx_SRC_GPSR',0,23,181,1,25,128,12,6
	.byte	'SR0',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'SR1',0,4
	.word	113188
	.byte	2,35,4,6
	.byte	'SR2',0,4
	.word	113188
	.byte	2,35,8,6
	.byte	'SR3',0,4
	.word	113188
	.byte	2,35,12,7,240,11
	.word	387
	.byte	8,239,11,0,6
	.byte	'reserved_10',0,240,11
	.word	114391
	.byte	2,35,16,0,28
	.word	114318
	.byte	17
	.byte	'Ifx_SRC_GPSR',0,23,188,1,3
	.word	114425
	.byte	3
	.byte	'_Ifx_SRC_GPT12',0,23,191,1,25,48,6
	.byte	'CIRQ',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'T2',0,4
	.word	113188
	.byte	2,35,4,6
	.byte	'T3',0,4
	.word	113188
	.byte	2,35,8,6
	.byte	'T4',0,4
	.word	113188
	.byte	2,35,12,6
	.byte	'T5',0,4
	.word	113188
	.byte	2,35,16,6
	.byte	'T6',0,4
	.word	113188
	.byte	2,35,20,7,24
	.word	387
	.byte	8,23,0,6
	.byte	'reserved_18',0,24
	.word	114547
	.byte	2,35,24,0,28
	.word	114452
	.byte	17
	.byte	'Ifx_SRC_GPT12',0,23,200,1,3
	.word	114578
	.byte	3
	.byte	'_Ifx_SRC_GTM',0,23,203,1,25,192,11,6
	.byte	'AEIIRQ',0,4
	.word	113188
	.byte	2,35,0,7,236,2
	.word	387
	.byte	8,235,2,0,6
	.byte	'reserved_4',0,236,2
	.word	114642
	.byte	2,35,4,6
	.byte	'ERR',0,4
	.word	113188
	.byte	3,35,240,2,6
	.byte	'reserved_174',0,12
	.word	64264
	.byte	3,35,244,2,7,32
	.word	113485
	.byte	8,0,0,6
	.byte	'TIM',0,32
	.word	114711
	.byte	3,35,128,3,7,224,7
	.word	387
	.byte	8,223,7,0,6
	.byte	'reserved_1A0',0,224,7
	.word	114734
	.byte	3,35,160,3,7,64
	.word	113485
	.byte	8,1,0,6
	.byte	'TOM',0,64
	.word	114769
	.byte	3,35,128,11,0,28
	.word	114606
	.byte	17
	.byte	'Ifx_SRC_GTM',0,23,212,1,3
	.word	114793
	.byte	3
	.byte	'_Ifx_SRC_HSM',0,23,215,1,25,8,6
	.byte	'HSM',0,8
	.word	113655
	.byte	2,35,0,0,28
	.word	114819
	.byte	17
	.byte	'Ifx_SRC_HSM',0,23,218,1,3
	.word	114852
	.byte	3
	.byte	'_Ifx_SRC_LMU',0,23,221,1,25,4,6
	.byte	'SR',0,4
	.word	113188
	.byte	2,35,0,0,28
	.word	114878
	.byte	17
	.byte	'Ifx_SRC_LMU',0,23,224,1,3
	.word	114910
	.byte	3
	.byte	'_Ifx_SRC_PMU',0,23,227,1,25,4,6
	.byte	'SR',0,4
	.word	113188
	.byte	2,35,0,0,28
	.word	114936
	.byte	17
	.byte	'Ifx_SRC_PMU',0,23,230,1,3
	.word	114968
	.byte	3
	.byte	'_Ifx_SRC_QSPI',0,23,233,1,25,24,6
	.byte	'TX',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'RX',0,4
	.word	113188
	.byte	2,35,4,6
	.byte	'ERR',0,4
	.word	113188
	.byte	2,35,8,6
	.byte	'PT',0,4
	.word	113188
	.byte	2,35,12,6
	.byte	'HC',0,4
	.word	113188
	.byte	2,35,16,6
	.byte	'U',0,4
	.word	113188
	.byte	2,35,20,0,28
	.word	114994
	.byte	17
	.byte	'Ifx_SRC_QSPI',0,23,241,1,3
	.word	115087
	.byte	3
	.byte	'_Ifx_SRC_SCU',0,23,244,1,25,20,6
	.byte	'DTS',0,4
	.word	113188
	.byte	2,35,0,7,16
	.word	113188
	.byte	8,3,0,6
	.byte	'ERU',0,16
	.word	115146
	.byte	2,35,4,0,28
	.word	115114
	.byte	17
	.byte	'Ifx_SRC_SCU',0,23,248,1,3
	.word	115169
	.byte	3
	.byte	'_Ifx_SRC_SENT',0,23,251,1,25,16,6
	.byte	'SR',0,16
	.word	115146
	.byte	2,35,0,0,28
	.word	115195
	.byte	17
	.byte	'Ifx_SRC_SENT',0,23,254,1,3
	.word	115228
	.byte	3
	.byte	'_Ifx_SRC_SMU',0,23,129,2,25,12,7,12
	.word	113188
	.byte	8,2,0,6
	.byte	'SR',0,12
	.word	115274
	.byte	2,35,0,0,28
	.word	115255
	.byte	17
	.byte	'Ifx_SRC_SMU',0,23,132,2,3
	.word	115296
	.byte	3
	.byte	'_Ifx_SRC_STM',0,23,135,2,25,96,6
	.byte	'SR0',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'SR1',0,4
	.word	113188
	.byte	2,35,4,7,88
	.word	387
	.byte	8,87,0,6
	.byte	'reserved_8',0,88
	.word	115367
	.byte	2,35,8,0,28
	.word	115322
	.byte	17
	.byte	'Ifx_SRC_STM',0,23,140,2,3
	.word	115397
	.byte	3
	.byte	'_Ifx_SRC_VADCCG',0,23,143,2,25,192,2,6
	.byte	'SR0',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'SR1',0,4
	.word	113188
	.byte	2,35,4,6
	.byte	'SR2',0,4
	.word	113188
	.byte	2,35,8,6
	.byte	'SR3',0,4
	.word	113188
	.byte	2,35,12,7,176,2
	.word	387
	.byte	8,175,2,0,6
	.byte	'reserved_10',0,176,2
	.word	115498
	.byte	2,35,16,0,28
	.word	115423
	.byte	17
	.byte	'Ifx_SRC_VADCCG',0,23,150,2,3
	.word	115532
	.byte	3
	.byte	'_Ifx_SRC_VADCG',0,23,153,2,25,16,6
	.byte	'SR0',0,4
	.word	113188
	.byte	2,35,0,6
	.byte	'SR1',0,4
	.word	113188
	.byte	2,35,4,6
	.byte	'SR2',0,4
	.word	113188
	.byte	2,35,8,6
	.byte	'SR3',0,4
	.word	113188
	.byte	2,35,12,0,28
	.word	115561
	.byte	17
	.byte	'Ifx_SRC_VADCG',0,23,159,2,3
	.word	115635
	.byte	3
	.byte	'_Ifx_SRC_XBAR',0,23,162,2,25,4,6
	.byte	'SRC',0,4
	.word	113188
	.byte	2,35,0,0,28
	.word	115663
	.byte	17
	.byte	'Ifx_SRC_XBAR',0,23,165,2,3
	.word	115697
	.byte	3
	.byte	'_Ifx_SRC_GASCLIN',0,23,178,2,25,24,7,24
	.word	113248
	.byte	8,1,0,28
	.word	115747
	.byte	6
	.byte	'ASCLIN',0,24
	.word	115756
	.byte	2,35,0,0,28
	.word	115724
	.byte	17
	.byte	'Ifx_SRC_GASCLIN',0,23,181,2,3
	.word	115778
	.byte	3
	.byte	'_Ifx_SRC_GBCU',0,23,184,2,25,4,28
	.word	113335
	.byte	6
	.byte	'SPB',0,4
	.word	115828
	.byte	2,35,0,0,28
	.word	115808
	.byte	17
	.byte	'Ifx_SRC_GBCU',0,23,187,2,3
	.word	115847
	.byte	3
	.byte	'_Ifx_SRC_GCAN',0,23,190,2,25,96,7,64
	.word	113400
	.byte	8,0,0,28
	.word	115894
	.byte	6
	.byte	'CAN',0,64
	.word	115903
	.byte	2,35,0,7,32
	.word	113466
	.byte	8,0,0,28
	.word	115921
	.byte	6
	.byte	'CAN1',0,32
	.word	115930
	.byte	2,35,64,0,28
	.word	115874
	.byte	17
	.byte	'Ifx_SRC_GCAN',0,23,194,2,3
	.word	115950
	.byte	3
	.byte	'_Ifx_SRC_GCCU6',0,23,197,2,25,32,7,32
	.word	113534
	.byte	8,1,0,28
	.word	115998
	.byte	6
	.byte	'CCU6',0,32
	.word	116007
	.byte	2,35,0,0,28
	.word	115977
	.byte	17
	.byte	'Ifx_SRC_GCCU6',0,23,200,2,3
	.word	116027
	.byte	3
	.byte	'_Ifx_SRC_GCERBERUS',0,23,203,2,25,8,28
	.word	113632
	.byte	6
	.byte	'CERBERUS',0,8
	.word	116080
	.byte	2,35,0,0,28
	.word	116055
	.byte	17
	.byte	'Ifx_SRC_GCERBERUS',0,23,206,2,3
	.word	116104
	.byte	3
	.byte	'_Ifx_SRC_GCPU',0,23,209,2,25,32,7,32
	.word	113707
	.byte	8,0,0,28
	.word	116156
	.byte	6
	.byte	'CPU',0,32
	.word	116165
	.byte	2,35,0,0,28
	.word	116136
	.byte	17
	.byte	'Ifx_SRC_GCPU',0,23,212,2,3
	.word	116184
	.byte	3
	.byte	'_Ifx_SRC_GDMA',0,23,215,2,25,80,7,80
	.word	113787
	.byte	8,0,0,28
	.word	116231
	.byte	6
	.byte	'DMA',0,80
	.word	116240
	.byte	2,35,0,0,28
	.word	116211
	.byte	17
	.byte	'Ifx_SRC_GDMA',0,23,218,2,3
	.word	116259
	.byte	3
	.byte	'_Ifx_SRC_GEMEM',0,23,221,2,25,4,7,4
	.word	113878
	.byte	8,0,0,28
	.word	116307
	.byte	6
	.byte	'EMEM',0,4
	.word	116316
	.byte	2,35,0,0,28
	.word	116286
	.byte	17
	.byte	'Ifx_SRC_GEMEM',0,23,224,2,3
	.word	116336
	.byte	3
	.byte	'_Ifx_SRC_GERAY',0,23,227,2,25,80,7,80
	.word	113938
	.byte	8,0,0,28
	.word	116385
	.byte	6
	.byte	'ERAY',0,80
	.word	116394
	.byte	2,35,0,0,28
	.word	116364
	.byte	17
	.byte	'Ifx_SRC_GERAY',0,23,230,2,3
	.word	116414
	.byte	3
	.byte	'_Ifx_SRC_GETH',0,23,233,2,25,4,7,4
	.word	114101
	.byte	8,0,0,28
	.word	116462
	.byte	6
	.byte	'ETH',0,4
	.word	116471
	.byte	2,35,0,0,28
	.word	116442
	.byte	17
	.byte	'Ifx_SRC_GETH',0,23,236,2,3
	.word	116490
	.byte	3
	.byte	'_Ifx_SRC_GEVR',0,23,239,2,25,8,7,8
	.word	114159
	.byte	8,0,0,28
	.word	116537
	.byte	6
	.byte	'EVR',0,8
	.word	116546
	.byte	2,35,0,0,28
	.word	116517
	.byte	17
	.byte	'Ifx_SRC_GEVR',0,23,242,2,3
	.word	116565
	.byte	3
	.byte	'_Ifx_SRC_GFFT',0,23,245,2,25,12,7,12
	.word	114232
	.byte	8,0,0,28
	.word	116612
	.byte	6
	.byte	'FFT',0,12
	.word	116621
	.byte	2,35,0,0,28
	.word	116592
	.byte	17
	.byte	'Ifx_SRC_GFFT',0,23,248,2,3
	.word	116640
	.byte	3
	.byte	'_Ifx_SRC_GGPSR',0,23,251,2,25,128,12,7,128,12
	.word	114318
	.byte	8,0,0,28
	.word	116689
	.byte	6
	.byte	'GPSR',0,128,12
	.word	116699
	.byte	2,35,0,0,28
	.word	116667
	.byte	17
	.byte	'Ifx_SRC_GGPSR',0,23,254,2,3
	.word	116720
	.byte	3
	.byte	'_Ifx_SRC_GGPT12',0,23,129,3,25,48,7,48
	.word	114452
	.byte	8,0,0,28
	.word	116770
	.byte	6
	.byte	'GPT12',0,48
	.word	116779
	.byte	2,35,0,0,28
	.word	116748
	.byte	17
	.byte	'Ifx_SRC_GGPT12',0,23,132,3,3
	.word	116800
	.byte	3
	.byte	'_Ifx_SRC_GGTM',0,23,135,3,25,192,11,7,192,11
	.word	114606
	.byte	8,0,0,28
	.word	116850
	.byte	6
	.byte	'GTM',0,192,11
	.word	116860
	.byte	2,35,0,0,28
	.word	116829
	.byte	17
	.byte	'Ifx_SRC_GGTM',0,23,138,3,3
	.word	116880
	.byte	3
	.byte	'_Ifx_SRC_GHSM',0,23,141,3,25,8,7,8
	.word	114819
	.byte	8,0,0,28
	.word	116927
	.byte	6
	.byte	'HSM',0,8
	.word	116936
	.byte	2,35,0,0,28
	.word	116907
	.byte	17
	.byte	'Ifx_SRC_GHSM',0,23,144,3,3
	.word	116955
	.byte	3
	.byte	'_Ifx_SRC_GLMU',0,23,147,3,25,4,7,4
	.word	114878
	.byte	8,0,0,28
	.word	117002
	.byte	6
	.byte	'LMU',0,4
	.word	117011
	.byte	2,35,0,0,28
	.word	116982
	.byte	17
	.byte	'Ifx_SRC_GLMU',0,23,150,3,3
	.word	117030
	.byte	3
	.byte	'_Ifx_SRC_GPMU',0,23,153,3,25,8,7,8
	.word	114936
	.byte	8,1,0,28
	.word	117077
	.byte	6
	.byte	'PMU',0,8
	.word	117086
	.byte	2,35,0,0,28
	.word	117057
	.byte	17
	.byte	'Ifx_SRC_GPMU',0,23,156,3,3
	.word	117105
	.byte	3
	.byte	'_Ifx_SRC_GQSPI',0,23,159,3,25,96,7,96
	.word	114994
	.byte	8,3,0,28
	.word	117153
	.byte	6
	.byte	'QSPI',0,96
	.word	117162
	.byte	2,35,0,0,28
	.word	117132
	.byte	17
	.byte	'Ifx_SRC_GQSPI',0,23,162,3,3
	.word	117182
	.byte	3
	.byte	'_Ifx_SRC_GSCU',0,23,165,3,25,20,28
	.word	115114
	.byte	6
	.byte	'SCU',0,20
	.word	117230
	.byte	2,35,0,0,28
	.word	117210
	.byte	17
	.byte	'Ifx_SRC_GSCU',0,23,168,3,3
	.word	117249
	.byte	3
	.byte	'_Ifx_SRC_GSENT',0,23,171,3,25,16,7,16
	.word	115195
	.byte	8,0,0,28
	.word	117297
	.byte	6
	.byte	'SENT',0,16
	.word	117306
	.byte	2,35,0,0,28
	.word	117276
	.byte	17
	.byte	'Ifx_SRC_GSENT',0,23,174,3,3
	.word	117326
	.byte	3
	.byte	'_Ifx_SRC_GSMU',0,23,177,3,25,12,7,12
	.word	115255
	.byte	8,0,0,28
	.word	117374
	.byte	6
	.byte	'SMU',0,12
	.word	117383
	.byte	2,35,0,0,28
	.word	117354
	.byte	17
	.byte	'Ifx_SRC_GSMU',0,23,180,3,3
	.word	117402
	.byte	3
	.byte	'_Ifx_SRC_GSTM',0,23,183,3,25,96,7,96
	.word	115322
	.byte	8,0,0,28
	.word	117449
	.byte	6
	.byte	'STM',0,96
	.word	117458
	.byte	2,35,0,0,28
	.word	117429
	.byte	17
	.byte	'Ifx_SRC_GSTM',0,23,186,3,3
	.word	117477
	.byte	3
	.byte	'_Ifx_SRC_GVADC',0,23,189,3,25,224,4,7,64
	.word	115561
	.byte	8,3,0,28
	.word	117526
	.byte	6
	.byte	'G',0,64
	.word	117535
	.byte	2,35,0,7,224,1
	.word	387
	.byte	8,223,1,0,6
	.byte	'reserved_40',0,224,1
	.word	117551
	.byte	2,35,64,7,192,2
	.word	115423
	.byte	8,0,0,28
	.word	117584
	.byte	6
	.byte	'CG',0,192,2
	.word	117594
	.byte	3,35,160,2,0,28
	.word	117504
	.byte	17
	.byte	'Ifx_SRC_GVADC',0,23,194,3,3
	.word	117614
	.byte	3
	.byte	'_Ifx_SRC_GXBAR',0,23,197,3,25,4,28
	.word	115663
	.byte	6
	.byte	'XBAR',0,4
	.word	117663
	.byte	2,35,0,0,28
	.word	117642
	.byte	17
	.byte	'Ifx_SRC_GXBAR',0,23,200,3,3
	.word	117683
	.byte	17
	.byte	'Dma_StatusType',0,9,121,22
	.word	508
	.byte	17
	.byte	'Dma_ErrorStatusType',0,9,141,1,22
	.word	508
	.byte	17
	.byte	'Dma_ChannelType',0,9,149,2,2
	.word	6890
	.byte	17
	.byte	'Spi_NumberOfDataType',0,8,169,7,16
	.word	227
	.byte	17
	.byte	'Spi_ChannelType',0,8,177,7,15
	.word	387
	.byte	17
	.byte	'Spi_JobType',0,8,185,7,16
	.word	227
	.byte	17
	.byte	'Spi_SequenceType',0,8,193,7,15
	.word	387
	.byte	17
	.byte	'Spi_ChannelConfigType',0,8,144,8,3
	.word	6045
	.byte	17
	.byte	'Spi_DelayConfigType',0,8,162,8,16
	.word	287
	.byte	17
	.byte	'Spi_HWUnitType',0,8,170,8,15
	.word	387
	.byte	17
	.byte	'Spi_JobConfigType',0,8,241,8,2
	.word	6198
	.byte	17
	.byte	'Spi_SequenceConfigType',0,8,157,9,2
	.word	6560
	.byte	17
	.byte	'Spi_DmaConfigType',0,8,168,9,2
	.word	6866
	.byte	17
	.byte	'Spi_HWModuleConfigType',0,8,187,9,2
	.word	6753
	.byte	17
	.byte	'Spi_BaudrateEconType',0,8,222,9,2
	.word	9177
	.byte	17
	.byte	'Spi_ConfigType',0,8,156,10,3
	.word	6024
	.byte	3
	.byte	'Spi_LastChannelDataType',0,8,161,10,16,8,11
	.word	287
	.byte	6
	.byte	'LastDataPtr',0,4
	.word	118176
	.byte	2,35,0,6
	.byte	'DataWidth',0,2
	.word	227
	.byte	2,35,4,0,17
	.byte	'Spi_LastChannelDataType',0,8,165,10,2
	.word	118146
	.byte	17
	.byte	'Adc_ChannelType',0,6,227,2,15
	.word	387
	.byte	17
	.byte	'Adc_GroupType',0,6,233,2,16
	.word	227
	.byte	17
	.byte	'Adc_ValueGroupType',0,6,247,2,16
	.word	227
	.byte	17
	.byte	'Adc_PrescaleType',0,6,144,3,16
	.word	287
	.byte	17
	.byte	'Adc_TriggerSourceType',0,6,200,3,15
	.word	387
	.byte	17
	.byte	'Adc_GroupConvModeType',0,6,208,3,15
	.word	387
	.byte	17
	.byte	'Adc_GroupPriorityType',0,6,215,3,15
	.word	387
	.byte	17
	.byte	'Adc_GroupDefType',0,6,130,4,25
	.word	387
	.byte	17
	.byte	'Adc_StreamNumSampleType',0,6,135,4,15
	.word	387
	.byte	17
	.byte	'Adc_StreamBufferModeType',0,6,148,4,15
	.word	387
	.byte	17
	.byte	'Adc_TriggSrcArbLevelType',0,6,179,4,16
	.word	287
	.byte	3
	.byte	'Adc_TriggSrcData',0,6,205,4,16,4,6
	.byte	'GrpId',0,2
	.word	227
	.byte	2,35,0,6
	.byte	'GrpPriority',0,1
	.word	387
	.byte	2,35,2,6
	.byte	'IsrDoNothing',0,1
	.word	387
	.byte	2,35,3,0,17
	.byte	'Adc_TriggSrcDataType',0,6,223,4,2
	.word	118577
	.byte	3
	.byte	'Adc_GroupData',0,6,232,4,16,32,7,16
	.word	387
	.byte	8,15,0,6
	.byte	'GrpChannels',0,16
	.word	118709
	.byte	2,35,0,6
	.byte	'GrpChannelRes',0,16
	.word	2475
	.byte	2,35,16,0,17
	.byte	'Adc_GroupDataType',0,6,250,4,2
	.word	118689
	.byte	17
	.byte	'Adc_HwUnitCfgType',0,6,179,6,2
	.word	4308
	.byte	17
	.byte	'Adc_GroupCfgType',0,6,129,7,3
	.word	4745
	.byte	17
	.byte	'Adc_ChannelCfgType',0,6,160,7,3
	.word	4502
	.byte	17
	.byte	'Adc_KernelConfigType',0,6,183,7,3
	.word	4281
	.byte	17
	.byte	'Adc_GlobalCfgType',0,6,244,7,3
	.word	5013
	.byte	17
	.byte	'Adc_ConfigType',0,6,134,8,3
	.word	4260
	.byte	17
	.byte	'PduIdType',0,24,72,22
	.word	387
	.byte	17
	.byte	'PduLengthType',0,24,76,22
	.word	227
	.byte	17
	.byte	'Can_IdType',0,25,46,16
	.word	287
	.byte	3
	.byte	'Can_TxHwObjectConfigType',0,26,218,3,16,2,6
	.byte	'MsgObjId',0,1
	.word	387
	.byte	2,35,0,6
	.byte	'HwControllerId',0,1
	.word	387
	.byte	2,35,1,0,17
	.byte	'Can_TxHwObjectConfigType',0,26,236,3,3
	.word	119011
	.byte	3
	.byte	'Can_RxHwObjectConfigType',0,26,241,3,16,12,6
	.byte	'MaskRef',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'MsgId',0,4
	.word	287
	.byte	2,35,4,6
	.byte	'MsgObjId',0,1
	.word	387
	.byte	2,35,8,6
	.byte	'HwControllerId',0,1
	.word	387
	.byte	2,35,9,0,17
	.byte	'Can_RxHwObjectConfigType',0,26,131,4,3
	.word	119119
	.byte	3
	.byte	'Can_ControllerMOMapConfigType',0,26,165,4,16,4,7,4
	.word	387
	.byte	8,3,0,6
	.byte	'ControllerMOMap',0,4
	.word	119295
	.byte	2,35,0,0,17
	.byte	'Can_ControllerMOMapConfigType',0,26,168,4,3
	.word	119259
	.byte	3
	.byte	'Can_NPCRValueType',0,26,172,4,16,2,6
	.byte	'Can_NPCRValue',0,2
	.word	227
	.byte	2,35,0,0,17
	.byte	'Can_NPCRValueType',0,26,175,4,3
	.word	119369
	.byte	3
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,26,178,4,16,6,6
	.byte	'CanControllerBaudrate',0,4
	.word	287
	.byte	2,35,0,6
	.byte	'CanControllerBaudrateCfg',0,2
	.word	227
	.byte	2,35,4,0,17
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,26,182,4,3
	.word	119444
	.byte	3
	.byte	'Can_BaudrateConfigPtrType',0,26,185,4,16,4,5
	.word	119444
	.byte	11
	.word	119641
	.byte	6
	.byte	'Can_kBaudrateConfigPtr',0,4
	.word	119646
	.byte	2,35,0,0,17
	.byte	'Can_BaudrateConfigPtrType',0,26,188,4,3
	.word	119609
	.byte	3
	.byte	'Can_FDConfigParamType',0,26,193,4,16,6,6
	.byte	'CanControllerFDBaudrate',0,2
	.word	227
	.byte	2,35,0,6
	.byte	'CanControllerTxDelayComp',0,2
	.word	227
	.byte	2,35,2,6
	.byte	'CanControllerTxBRS',0,1
	.word	387
	.byte	2,35,4,0,17
	.byte	'Can_FDConfigParamType',0,26,198,4,3
	.word	119719
	.byte	3
	.byte	'Can_FDConfigParamPtrType',0,26,200,4,16,4,5
	.word	119719
	.byte	11
	.word	119905
	.byte	6
	.byte	'Can_kFDConfigParamPtr',0,4
	.word	119910
	.byte	2,35,0,0,17
	.byte	'Can_FDConfigParamPtrType',0,26,203,4,3
	.word	119874
	.byte	3
	.byte	'Can_EventHandlingType',0,26,210,4,16,4,6
	.byte	'CanEventType',0,4
	.word	119295
	.byte	2,35,0,0,17
	.byte	'Can_EventHandlingType',0,26,213,4,3
	.word	119981
	.byte	11
	.word	387
	.byte	17
	.byte	'Uart_MemPtrType',0,7,231,3,16
	.word	120063
	.byte	17
	.byte	'Uart_SizeType',0,7,240,3,16
	.word	227
	.byte	17
	.byte	'UartNotifType',0,7,148,4,2
	.word	5308
	.byte	17
	.byte	'Uart_ChannelType',0,7,183,4,2
	.word	5285
	.byte	17
	.byte	'Uart_ConfigType',0,7,195,4,2
	.word	5263
	.byte	14
	.byte	'Uart_StateType',0,7,228,4,14,1,15
	.byte	'UART_UNINITIALISED',0,0,15
	.byte	'UART_INITIALISED',0,1,15
	.byte	'UART_OPERATION_IN_PROGRESS',0,2,0,17
	.byte	'Uart_StateType',0,7,233,4,2
	.word	120190
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,0,3,8,54,15,39,12,63,12,60,12,0,0,3,19,1,3,8,58,15
	.byte	59,15,57,15,11,15,0,0,4,36,0,3,8,11,15,62,15,0,0,5,38,0,73,19,0,0,6,13,0,3,8,11,15,73,19,56,9,0,0,7,1
	.byte	1,11,15,73,19,0,0,8,33,0,47,15,0,0,9,19,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,13,15,12
	.byte	15,56,9,0,0,11,15,0,73,19,0,0,12,23,1,3,8,58,15,59,15,57,15,11,15,0,0,13,21,1,54,15,39,12,0,0,14,4,1,3
	.byte	8,58,15,59,15,57,15,11,15,0,0,15,40,0,3,8,28,13,0,0,16,5,0,73,19,0,0,17,22,0,3,8,58,15,59,15,57,15,73
	.byte	19,0,0,18,21,0,54,15,39,12,0,0,19,4,1,58,15,59,15,57,15,11,15,0,0,20,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,60,12,0,0,21,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,22,5,0,3,8,58,15,59,15
	.byte	57,15,73,19,0,0,23,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,24,46,0,3,8,58,15,59,15
	.byte	57,15,73,19,54,15,39,12,63,12,60,12,0,0,25,59,0,3,8,0,0,26,21,0,54,15,0,0,27,23,1,58,15,59,15,57,15,11
	.byte	15,0,0,28,53,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L14:
	.word	.L45-.L44
.L44:
	.half	3
	.word	.L47-.L46
.L46:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc',0
	.byte	0
	.byte	'EcuM_Cfg.h',0,1,0,0
	.byte	'..\\mcal_src\\Mcu.h',0,0,0,0
	.byte	'..\\mcal_src\\Gtm.h',0,0,0,0
	.byte	'..\\mcal_src\\Dio.h',0,0,0,0
	.byte	'..\\mcal_src\\Port.h',0,0,0,0
	.byte	'..\\mcal_src\\Adc.h',0,0,0,0
	.byte	'..\\mcal_src\\Uart.h',0,0,0,0
	.byte	'Spi.h',0,2,0,0
	.byte	'Mcal_DmaLib.h',0,3,0,0
	.byte	'Dma.h',0,4,0,0
	.byte	'..\\mcal_src\\Mcal_WdgLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Irq.h',0,0,0,0
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Std_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_TcLib.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxGtm_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSmu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxPort_regdef.h',0,0,0,0
	.byte	'IfxDma_regdef.h',0,3,0,0
	.byte	'IfxCpu_regdef.h',0,3,0,0
	.byte	'IfxSrc_regdef.h',0,3,0,0
	.byte	'..\\mcal_src\\ComStack_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Can_GeneralTypes.h',0,0,0,0
	.byte	'..\\mcal_src\\Can_17_MCanP.h',0,0,0,0,0
.L47:
.L45:
	.sdecl	'.debug_info',debug,cluster('EcuM_AL_DriverInitOne')
	.sect	'.debug_info'
.L15:
	.word	268
	.half	3
	.word	.L16
	.byte	4,1
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L18,.L17
	.byte	2
	.word	.L11
	.byte	3
	.byte	'EcuM_AL_DriverInitOne',0,1,207,1,6,1,1,1
	.word	.L4,.L35,.L3
	.byte	4
	.byte	'configptr',0,1,207,1,51
	.word	.L36,.L37
	.byte	5
	.word	.L4,.L35
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_AL_DriverInitOne')
	.sect	'.debug_abbrev'
.L16:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EcuM_AL_DriverInitOne')
	.sect	'.debug_line'
.L17:
	.word	.L49-.L48
.L48:
	.half	3
	.word	.L51-.L50
.L50:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0,0,0,0,0
.L51:
	.byte	5,6,7,0,5,2
	.word	.L4
	.byte	3,206,1,1,5,17,9
	.half	.L43-.L4
	.byte	3,4,1,5,25,9
	.half	.L2-.L43
	.byte	3,2,1,5,32,9
	.half	.L52-.L2
	.byte	1,5,25,7,9
	.half	.L53-.L52
	.byte	3,2,1,5,20,9
	.half	.L54-.L53
	.byte	3,4,1,5,16,9
	.half	.L55-.L54
	.byte	3,1,1,5,22,9
	.half	.L56-.L55
	.byte	1,5,18,9
	.half	.L57-.L56
	.byte	3,1,1,5,33,9
	.half	.L58-.L57
	.byte	3,2,1,5,16,9
	.half	.L59-.L58
	.byte	3,1,1,5,14,1,5,15,9
	.half	.L60-.L59
	.byte	3,1,1,5,13,1,5,17,9
	.half	.L61-.L60
	.byte	3,1,1,5,19,9
	.half	.L62-.L61
	.byte	3,1,1,5,17,1,9
	.half	.L63-.L62
	.byte	3,1,1,5,30,9
	.half	.L64-.L63
	.byte	3,1,1,5,3,9
	.half	.L65-.L64
	.byte	3,3,1,5,14,9
	.half	.L66-.L65
	.byte	3,3,1,9
	.half	.L67-.L66
	.byte	3,3,1,9
	.half	.L68-.L67
	.byte	3,2,1,9
	.half	.L69-.L68
	.byte	3,8,1,9
	.half	.L70-.L69
	.byte	3,2,1,5,17,9
	.half	.L71-.L70
	.byte	3,5,1,5,14,9
	.half	.L72-.L71
	.byte	3,9,1,5,3,9
	.half	.L73-.L72
	.byte	3,16,1,5,1,9
	.half	.L19-.L73
	.byte	3,1,0,1,1
.L49:
	.sdecl	'.debug_ranges',debug,cluster('EcuM_AL_DriverInitOne')
	.sect	'.debug_ranges'
.L18:
	.word	-1,.L4,0,.L19-.L4,0,0
	.sdecl	'.debug_info',debug,cluster('EcuM_AL_DriverInitTwo')
	.sect	'.debug_info'
.L20:
	.word	268
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L23,.L22
	.byte	2
	.word	.L11
	.byte	3
	.byte	'EcuM_AL_DriverInitTwo',0,1,164,2,6,1,1,1
	.word	.L6,.L38,.L5
	.byte	4
	.byte	'configptr',0,1,164,2,51
	.word	.L36,.L39
	.byte	5
	.word	.L6,.L38
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_AL_DriverInitTwo')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EcuM_AL_DriverInitTwo')
	.sect	'.debug_line'
.L22:
	.word	.L75-.L74
.L74:
	.half	3
	.word	.L77-.L76
.L76:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0,0,0,0,0
.L77:
	.byte	5,1,7,0,5,2
	.word	.L6
	.byte	3,170,2,1,7,9
	.half	.L24-.L6
	.byte	0,1,1
.L75:
	.sdecl	'.debug_ranges',debug,cluster('EcuM_AL_DriverInitTwo')
	.sect	'.debug_ranges'
.L23:
	.word	-1,.L6,0,.L24-.L6,0,0
	.sdecl	'.debug_info',debug,cluster('EcuM_AL_DriverInitThree')
	.sect	'.debug_info'
.L25:
	.word	270
	.half	3
	.word	.L26
	.byte	4,1
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L28,.L27
	.byte	2
	.word	.L11
	.byte	3
	.byte	'EcuM_AL_DriverInitThree',0,1,181,2,6,1,1,1
	.word	.L8,.L40,.L7
	.byte	4
	.byte	'configptr',0,1,181,2,53
	.word	.L36,.L41
	.byte	5
	.word	.L8,.L40
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_AL_DriverInitThree')
	.sect	'.debug_abbrev'
.L26:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EcuM_AL_DriverInitThree')
	.sect	'.debug_line'
.L27:
	.word	.L79-.L78
.L78:
	.half	3
	.word	.L81-.L80
.L80:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0,0,0,0,0
.L81:
	.byte	5,1,7,0,5,2
	.word	.L8
	.byte	3,187,2,1,7,9
	.half	.L29-.L8
	.byte	0,1,1
.L79:
	.sdecl	'.debug_ranges',debug,cluster('EcuM_AL_DriverInitThree')
	.sect	'.debug_ranges'
.L28:
	.word	-1,.L8,0,.L29-.L8,0,0
	.sdecl	'.debug_info',debug,cluster('EcuM_AL_DriverInitZero')
	.sect	'.debug_info'
.L30:
	.word	246
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L33,.L32
	.byte	2
	.word	.L11
	.byte	3
	.byte	'EcuM_AL_DriverInitZero',0,1,209,2,6,1,1,1
	.word	.L10,.L42,.L9
	.byte	4
	.word	.L10,.L42
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_AL_DriverInitZero')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EcuM_AL_DriverInitZero')
	.sect	'.debug_line'
.L32:
	.word	.L83-.L82
.L82:
	.half	3
	.word	.L85-.L84
.L84:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\EcuM_Callout_Stubs.c',0,0,0,0,0
.L85:
	.byte	5,1,7,0,5,2
	.word	.L10
	.byte	3,214,2,1,7,9
	.half	.L34-.L10
	.byte	0,1,1
.L83:
	.sdecl	'.debug_ranges',debug,cluster('EcuM_AL_DriverInitZero')
	.sect	'.debug_ranges'
.L33:
	.word	-1,.L10,0,.L34-.L10,0,0
	.sdecl	'.debug_loc',debug,cluster('EcuM_AL_DriverInitOne')
	.sect	'.debug_loc'
.L3:
	.word	-1,.L4,0,.L35-.L4
	.half	2
	.byte	138,0
	.word	0,0
.L37:
	.word	-1,.L4,0,.L2-.L4
	.half	1
	.byte	100
	.word	.L43-.L4,.L35-.L4
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EcuM_AL_DriverInitThree')
	.sect	'.debug_loc'
.L7:
	.word	-1,.L8,0,.L40-.L8
	.half	2
	.byte	138,0
	.word	0,0
.L41:
	.word	-1,.L8,0,.L40-.L8
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EcuM_AL_DriverInitTwo')
	.sect	'.debug_loc'
.L5:
	.word	-1,.L6,0,.L38-.L6
	.half	2
	.byte	138,0
	.word	0,0
.L39:
	.word	-1,.L6,0,.L38-.L6
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EcuM_AL_DriverInitZero')
	.sect	'.debug_loc'
.L9:
	.word	-1,.L10,0,.L42-.L10
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L86:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('EcuM_AL_DriverInitOne')
	.sect	'.debug_frame'
	.word	12
	.word	.L86,.L4,.L35-.L4
	.sdecl	'.debug_frame',debug,cluster('EcuM_AL_DriverInitTwo')
	.sect	'.debug_frame'
	.word	24
	.word	.L86,.L6,.L38-.L6
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('EcuM_AL_DriverInitThree')
	.sect	'.debug_frame'
	.word	24
	.word	.L86,.L8,.L40-.L8
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('EcuM_AL_DriverInitZero')
	.sect	'.debug_frame'
	.word	24
	.word	.L86,.L10,.L42-.L10
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\EcuM_Callout_Stubs.c	   344  #define ECUM_STOP_SEC_CODE
; ..\mcal_src\EcuM_Callout_Stubs.c	   345  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives 
; ..\mcal_src\EcuM_Callout_Stubs.c	   346    is allowed only for MemMap.h*/
; ..\mcal_src\EcuM_Callout_Stubs.c	   347  #include "MemMap.h"

	; Module end
