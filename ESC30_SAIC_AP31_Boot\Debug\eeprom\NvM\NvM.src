	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc22300a -c99 --dep-file=eeprom\\NvM\\.NvM.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\NvM\\NvM.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\NvM\\NvM.src ..\\eeprom\\NvM\\NvM.c"
	.compiler_name		"ctc"
	.name	"NvM"

	
$TC16X
	
	.sdecl	'.text.NvM.NvM_Init',code,cluster('NvM_Init')
	.sect	'.text.NvM.NvM_Init'
	.align	2
	
	.global	NvM_Init

; ..\eeprom\NvM\NvM.c	     1  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	     2   *  COPYRIGHT
; ..\eeprom\NvM\NvM.c	     3   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM.c	     4   *  \verbatim
; ..\eeprom\NvM\NvM.c	     5   *  Copyright (c) 2019 by Vector Informatik GmbH.                                                  All rights reserved.
; ..\eeprom\NvM\NvM.c	     6   *
; ..\eeprom\NvM\NvM.c	     7   *                This software is copyright protected and proprietary to Vector Informatik GmbH.
; ..\eeprom\NvM\NvM.c	     8   *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
; ..\eeprom\NvM\NvM.c	     9   *                All other rights remain with Vector Informatik GmbH.
; ..\eeprom\NvM\NvM.c	    10   *  \endverbatim
; ..\eeprom\NvM\NvM.c	    11   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM.c	    12   *  FILE DESCRIPTION
; ..\eeprom\NvM\NvM.c	    13   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM.c	    14   *         File:  NvM.c
; ..\eeprom\NvM\NvM.c	    15   *      Project:  MemService_AsrNvM
; ..\eeprom\NvM\NvM.c	    16   *       Module:  NvM
; ..\eeprom\NvM\NvM.c	    17   *    Generator:  -
; ..\eeprom\NvM\NvM.c	    18   *
; ..\eeprom\NvM\NvM.c	    19   *  Description:  The NVRAM Manager ensure the data storage and maintenance of NV data.
; ..\eeprom\NvM\NvM.c	    20   *                The NVRAM Manager shall be able to administrate the NV data of an EEPROM
; ..\eeprom\NvM\NvM.c	    21   *                and/or a FLASH EEPROM emulation device.
; ..\eeprom\NvM\NvM.c	    22   *
; ..\eeprom\NvM\NvM.c	    23   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    24  
; ..\eeprom\NvM\NvM.c	    25  /* Do not modify this file! */
; ..\eeprom\NvM\NvM.c	    26  
; ..\eeprom\NvM\NvM.c	    27  
; ..\eeprom\NvM\NvM.c	    28  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    29   *  MODULE SWITCH
; ..\eeprom\NvM\NvM.c	    30   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    31  #define NVM_SOURCE
; ..\eeprom\NvM\NvM.c	    32  
; ..\eeprom\NvM\NvM.c	    33  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    34   *  INCLUDES
; ..\eeprom\NvM\NvM.c	    35   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    36  
; ..\eeprom\NvM\NvM.c	    37  #include "Std_Types.h"
; ..\eeprom\NvM\NvM.c	    38  #include "Mcal_Compiler.h"
; ..\eeprom\NvM\NvM.c	    39  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    40   *  MODULE HEADER INCLUDES (INCLUDE PUBLIC AND PART OF NvM_Cfg.h)
; ..\eeprom\NvM\NvM.c	    41   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    42  #include "NvM.h"
; ..\eeprom\NvM\NvM.c	    43  
; ..\eeprom\NvM\NvM.c	    44  #include "NvM_PrivateCfg.h"
; ..\eeprom\NvM\NvM.c	    45  
; ..\eeprom\NvM\NvM.c	    46  /* include the central internal include */
; ..\eeprom\NvM\NvM.c	    47  #include "NvM_JobProc.h"
; ..\eeprom\NvM\NvM.c	    48  
; ..\eeprom\NvM\NvM.c	    49  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    50   *  INCLUDES OF NVM SUBMODULES
; ..\eeprom\NvM\NvM.c	    51   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    52  #include "NvM_Act.h"
; ..\eeprom\NvM\NvM.c	    53  #include "NvM_Qry.h"
; ..\eeprom\NvM\NvM.c	    54  #include "NvM_Queue.h"
; ..\eeprom\NvM\NvM.c	    55  #include "NvM_Crc.h"
; ..\eeprom\NvM\NvM.c	    56  
; ..\eeprom\NvM\NvM.c	    57  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    58   *  CALLBACK ROUTINES DECLARATIONS OF THE OWN MODULE
; ..\eeprom\NvM\NvM.c	    59   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    60  #include "NvM_Cbk.h"
; ..\eeprom\NvM\NvM.c	    61  
; ..\eeprom\NvM\NvM.c	    62  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    63   *  VERSION CHECK
; ..\eeprom\NvM\NvM.c	    64   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    65  #if ((NVM_SW_MAJOR_VERSION != (5u)) \ 
; ..\eeprom\NvM\NvM.c	    66          || (NVM_SW_MINOR_VERSION != (13u)))
; ..\eeprom\NvM\NvM.c	    67  # error "Version numbers of NvM.c and NvM.h are inconsistent!"
; ..\eeprom\NvM\NvM.c	    68  #endif
; ..\eeprom\NvM\NvM.c	    69  
; ..\eeprom\NvM\NvM.c	    70  #if ((NVM_CFG_MAJOR_VERSION != (5u)) \ 
; ..\eeprom\NvM\NvM.c	    71          || (NVM_CFG_MINOR_VERSION != (13u)))
; ..\eeprom\NvM\NvM.c	    72  # error "Version numbers of NvM.c and NvM_Cfg.h are inconsistent!"
; ..\eeprom\NvM\NvM.c	    73  #endif
; ..\eeprom\NvM\NvM.c	    74  
; ..\eeprom\NvM\NvM.c	    75  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    76   *  INTERNAL MACROS
; ..\eeprom\NvM\NvM.c	    77   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    78  #ifndef NVM_LOCAL /* COV_NVM_COMPATIBILITY */
; ..\eeprom\NvM\NvM.c	    79  # define NVM_LOCAL static
; ..\eeprom\NvM\NvM.c	    80  #endif
; ..\eeprom\NvM\NvM.c	    81  
; ..\eeprom\NvM\NvM.c	    82  #ifndef NVM_LOCAL_INLINE /* COV_NVM_COMPATIBILITY */
; ..\eeprom\NvM\NvM.c	    83  # define NVM_LOCAL_INLINE LOCAL_INLINE
; ..\eeprom\NvM\NvM.c	    84  #endif
; ..\eeprom\NvM\NvM.c	    85  
; ..\eeprom\NvM\NvM.c	    86  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    87   *  INTERNAL TYPE DEFINITIONS
; ..\eeprom\NvM\NvM.c	    88   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    89  #if (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1)
; ..\eeprom\NvM\NvM.c	    90  typedef P2CONST(NvM_RamMngmtAreaType, AUTOMATIC, NVM_CONFIG_DATA) NvM_RamMngmtConstPtrType;
; ..\eeprom\NvM\NvM.c	    91  #endif /* (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1) */
; ..\eeprom\NvM\NvM.c	    92  
; ..\eeprom\NvM\NvM.c	    93  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	    94   *  INTERNAL FORWARD DECLARATIONS
; ..\eeprom\NvM\NvM.c	    95   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	    96  #define NVM_START_SEC_VAR_FAST_8
; ..\eeprom\NvM\NvM.c	    97    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM.c	    98  
; ..\eeprom\NvM\NvM.c	    99  /* API flags - which are accessible from API */
; ..\eeprom\NvM\NvM.c	   100  VAR(uint8, NVM_FAST_DATA) NvM_ApiFlags_u8;
; ..\eeprom\NvM\NvM.c	   101  
; ..\eeprom\NvM\NvM.c	   102  #define NVM_STOP_SEC_VAR_FAST_8
; ..\eeprom\NvM\NvM.c	   103    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM.c	   104  
; ..\eeprom\NvM\NvM.c	   105  #define NVM_START_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM.c	   106    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM.c	   107  
; ..\eeprom\NvM\NvM.c	   108  #if((NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) && (NVM_CALC_RAM_CRC_USED == STD_ON))
; ..\eeprom\NvM\NvM.c	   109  NVM_LOCAL VAR(struct NvM_CrcJobStruct, NVM_PRIVATE_DATA) NvM_AsyncCrcJob_t;
; ..\eeprom\NvM\NvM.c	   110  #endif
; ..\eeprom\NvM\NvM.c	   111  
; ..\eeprom\NvM\NvM.c	   112  #define NVM_STOP_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM.c	   113    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM.c	   114  
; ..\eeprom\NvM\NvM.c	   115  
; ..\eeprom\NvM\NvM.c	   116  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   117   *  IMPLEMENTATION
; ..\eeprom\NvM\NvM.c	   118   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   119  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM.c	   120    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM.c	   121  
; ..\eeprom\NvM\NvM.c	   122  #if (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1)
; ..\eeprom\NvM\NvM.c	   123  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   124   * NvM_WriteProtectionChecks
; ..\eeprom\NvM\NvM.c	   125   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   126  /*! \brief Checks whether the block referenced via parameter is write protected or not.
; ..\eeprom\NvM\NvM.c	   127   *  \details Checks whether the block referenced via parameter is write protected or not.
; ..\eeprom\NvM\NvM.c	   128   *  \param[in] MngmtPtr as a pointer to the management information structure of a NvM block. Must not be NULL_PTR.
; ..\eeprom\NvM\NvM.c	   129   *             Validity has to be ensured by called (used NvM internally).
; ..\eeprom\NvM\NvM.c	   130   *  \return FALSE block is write protected
; ..\eeprom\NvM\NvM.c	   131   *  \return TRUE otherwise
; ..\eeprom\NvM\NvM.c	   132   *  \context TASK
; ..\eeprom\NvM\NvM.c	   133   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   134   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   135   *  \config API configuration class > 1
; ..\eeprom\NvM\NvM.c	   136   *  \pre -
; ..\eeprom\NvM\NvM.c	   137   */
; ..\eeprom\NvM\NvM.c	   138  NVM_LOCAL_INLINE boolean NvM_WriteProtectionChecks(const NvM_RamMngmtConstPtrType MngmtPtr);
; ..\eeprom\NvM\NvM.c	   139  #endif
; ..\eeprom\NvM\NvM.c	   140  
; ..\eeprom\NvM\NvM.c	   141  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   142   * NvM_CheckBlockType
; ..\eeprom\NvM\NvM.c	   143   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   144  /*! \brief Checks whether the Block Id references a Dataset NvM block.
; ..\eeprom\NvM\NvM.c	   145   *  \details Checks whether the Block Id references a Dataset NvM block.
; ..\eeprom\NvM\NvM.c	   146   *  \param[in] BlockId in range [1, (number of blocks - 1)].
; ..\eeprom\NvM\NvM.c	   147   *  \return TRUE Block Id references a Dataset block
; ..\eeprom\NvM\NvM.c	   148   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   149   *  \context TASK
; ..\eeprom\NvM\NvM.c	   150   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   151   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   152   *  \pre -
; ..\eeprom\NvM\NvM.c	   153   */
; ..\eeprom\NvM\NvM.c	   154  NVM_LOCAL_INLINE boolean NvM_CheckBlockType(const NvM_BlockIdType BlockId);
; ..\eeprom\NvM\NvM.c	   155  
; ..\eeprom\NvM\NvM.c	   156  # if ((NVM_DEV_ERROR_DETECT == STD_ON) || (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON))
; ..\eeprom\NvM\NvM.c	   157  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   158   * NvM_CheckAddress
; ..\eeprom\NvM\NvM.c	   159   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   160  /*! \brief Checks whether the given pointer is valid for given NvM Block Id.
; ..\eeprom\NvM\NvM.c	   161   *  \details Checks whether the given pointer is valid for given NvM Block Id.
; ..\eeprom\NvM\NvM.c	   162   *  \param[in] BlockId in range [1, (number of blocks - 1)].
; ..\eeprom\NvM\NvM.c	   163   *  \param[in] RamPtr to be checked whether valid for the passed BlockId. May be NULL_PTR, depends on passed BlockId.
; ..\eeprom\NvM\NvM.c	   164   *  \return TRUE pointer is valid for NvM block references via BlockId
; ..\eeprom\NvM\NvM.c	   165   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   166   *  \context TASK
; ..\eeprom\NvM\NvM.c	   167   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   168   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   169   *  \config DET error detection and Param Pointer check are enabled
; ..\eeprom\NvM\NvM.c	   170   *  \pre -
; ..\eeprom\NvM\NvM.c	   171   */
; ..\eeprom\NvM\NvM.c	   172  NVM_LOCAL_INLINE boolean NvM_CheckAddress(const NvM_BlockIdType BlockId, const void * RamPtr);
; ..\eeprom\NvM\NvM.c	   173  #endif /* ((NVM_DEV_ERROR_DETECT == STD_ON) || (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)) */
; ..\eeprom\NvM\NvM.c	   174  
; ..\eeprom\NvM\NvM.c	   175  # if ((NVM_DEV_ERROR_DETECT == STD_ON) || (NVM_KILL_WRITEALL_API == STD_ON))
; ..\eeprom\NvM\NvM.c	   176  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   177   * NvM_CheckBlockId
; ..\eeprom\NvM\NvM.c	   178   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   179  /*! \brief Checks whether the Block Id is a valid NvM Block Id.
; ..\eeprom\NvM\NvM.c	   180   *  \details Checks whether the Block Id is a valid NvM Block Id.
; ..\eeprom\NvM\NvM.c	   181   *  \param[in] BlockId to be checked for validity. May exceed the number of configured blocks.
; ..\eeprom\NvM\NvM.c	   182   *  \return TRUE Block Id is valid
; ..\eeprom\NvM\NvM.c	   183   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   184   *  \context TASK
; ..\eeprom\NvM\NvM.c	   185   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   186   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   187   *  \config DET error detection and Param Block Id check are enabled
; ..\eeprom\NvM\NvM.c	   188   *  \pre -
; ..\eeprom\NvM\NvM.c	   189   */
; ..\eeprom\NvM\NvM.c	   190  NVM_LOCAL_INLINE boolean NvM_CheckBlockId(const NvM_BlockIdType BlockId);
; ..\eeprom\NvM\NvM.c	   191  # endif /* ((NVM_DEV_ERROR_DETECT == STD_ON) || (NVM_KILL_WRITEALL_API == STD_ON)) */
; ..\eeprom\NvM\NvM.c	   192  
; ..\eeprom\NvM\NvM.c	   193  #if(NVM_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\NvM\NvM.c	   194  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   195   * NvM_CheckCurrDataIndex
; ..\eeprom\NvM\NvM.c	   196   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   197  /*! \brief Checks the current Dataindex for given Block Id to be valid for the block.
; ..\eeprom\NvM\NvM.c	   198   *  \details Checks the current Dataindex for given Block Id to be valid for the block.
; ..\eeprom\NvM\NvM.c	   199   *  \param[in] BlockId in range [1, (number of blocks - 1)].
; ..\eeprom\NvM\NvM.c	   200   *  \return TRUE current Dataindex is valid
; ..\eeprom\NvM\NvM.c	   201   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   202   *  \context TASK
; ..\eeprom\NvM\NvM.c	   203   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   204   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   205   *  \config DET error detection and Param Dataindex check are enabled
; ..\eeprom\NvM\NvM.c	   206   *  \pre -
; ..\eeprom\NvM\NvM.c	   207   */
; ..\eeprom\NvM\NvM.c	   208  NVM_LOCAL_INLINE boolean NvM_CheckCurrDataIndex(const NvM_BlockIdType BlockId);
; ..\eeprom\NvM\NvM.c	   209  
; ..\eeprom\NvM\NvM.c	   210  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   211   * NvM_CheckDataIndex
; ..\eeprom\NvM\NvM.c	   212   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   213  /*! \brief Checks the given Dataindex to be valid for given Block Id.
; ..\eeprom\NvM\NvM.c	   214   *  \details Checks the given Dataindex to be valid for given Block Id.
; ..\eeprom\NvM\NvM.c	   215   *  \param[in] BlockId in range [1, (number of blocks - 1)].
; ..\eeprom\NvM\NvM.c	   216   *  \param[in] DataIndex to be checked for validity. May exceed the number of configured dataset.
; ..\eeprom\NvM\NvM.c	   217   *  \return TRUE Dataindex is valid
; ..\eeprom\NvM\NvM.c	   218   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   219   *  \context TASK
; ..\eeprom\NvM\NvM.c	   220   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   221   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   222   *  \config DET error detection and Param Dataindex check are enabled
; ..\eeprom\NvM\NvM.c	   223   *  \pre -
; ..\eeprom\NvM\NvM.c	   224   */
; ..\eeprom\NvM\NvM.c	   225  NVM_LOCAL_INLINE boolean NvM_CheckDataIndex(const NvM_BlockIdType BlockId, const uint8 DataIndex);
; ..\eeprom\NvM\NvM.c	   226  
; ..\eeprom\NvM\NvM.c	   227  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   228   * NvM_CheckNotNull
; ..\eeprom\NvM\NvM.c	   229   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   230  /*! \brief Checks whether the given pointer is null or not.
; ..\eeprom\NvM\NvM.c	   231   *  \details Checks whether the given pointer is null or not.
; ..\eeprom\NvM\NvM.c	   232   *  \param[in] Ptr to be checked != NULL_PTR. May be NULL_PTR.
; ..\eeprom\NvM\NvM.c	   233   *  \return TRUE pointer is not null
; ..\eeprom\NvM\NvM.c	   234   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   235   *  \context TASK
; ..\eeprom\NvM\NvM.c	   236   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   237   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   238   *  \config DET error detection and Param Pointer check are enabled
; ..\eeprom\NvM\NvM.c	   239   *  \pre -
; ..\eeprom\NvM\NvM.c	   240   */
; ..\eeprom\NvM\NvM.c	   241  NVM_LOCAL_INLINE boolean NvM_CheckNotNull(const uint8 * Ptr);
; ..\eeprom\NvM\NvM.c	   242  
; ..\eeprom\NvM\NvM.c	   243  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   244   * NvM_CheckInitialized
; ..\eeprom\NvM\NvM.c	   245   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   246  /*! \brief Checks whether the NvM is initialized or not.
; ..\eeprom\NvM\NvM.c	   247   *  \details Checks whether the NvM is initialized or not.
; ..\eeprom\NvM\NvM.c	   248   *  \return TRUE NvM is initialized
; ..\eeprom\NvM\NvM.c	   249   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   250   *  \context TASK
; ..\eeprom\NvM\NvM.c	   251   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   252   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   253   *  \config DET error detection and Status uninitialized check are enabled
; ..\eeprom\NvM\NvM.c	   254   *  \pre -
; ..\eeprom\NvM\NvM.c	   255   */
; ..\eeprom\NvM\NvM.c	   256  NVM_LOCAL_INLINE boolean NvM_CheckInitialized(void);
; ..\eeprom\NvM\NvM.c	   257  
; ..\eeprom\NvM\NvM.c	   258  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   259   * NvM_CheckMultiBlockPending
; ..\eeprom\NvM\NvM.c	   260   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   261  /*! \brief Checks whether the NvM Multi Block is pending.
; ..\eeprom\NvM\NvM.c	   262   *  \details Checks whether the NvM Multi Block is pending.
; ..\eeprom\NvM\NvM.c	   263   *  \return TRUE NvM Multi Block is pending
; ..\eeprom\NvM\NvM.c	   264   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   265   *  \context TASK
; ..\eeprom\NvM\NvM.c	   266   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   267   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   268   *  \config DET error detection and Block Pending check are enabled
; ..\eeprom\NvM\NvM.c	   269   *  \pre -
; ..\eeprom\NvM\NvM.c	   270   */
; ..\eeprom\NvM\NvM.c	   271  NVM_LOCAL_INLINE boolean NvM_CheckMultiBlockPending(void);
; ..\eeprom\NvM\NvM.c	   272  #endif /* (NVM_DEV_ERROR_DETECT == STD_ON) */
; ..\eeprom\NvM\NvM.c	   273  
; ..\eeprom\NvM\NvM.c	   274  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   275   * NvM_CheckBlockPending
; ..\eeprom\NvM\NvM.c	   276   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   277  /*! \brief Checks whether the NvM Block is pending.
; ..\eeprom\NvM\NvM.c	   278   *  \details Checks whether the NvM Block is pending.
; ..\eeprom\NvM\NvM.c	   279   *  \param[in] BlockId in range [1, (number of blocks - 1)].
; ..\eeprom\NvM\NvM.c	   280   *  \return TRUE referenced NvM Block is pending
; ..\eeprom\NvM\NvM.c	   281   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM.c	   282   *  \context TASK
; ..\eeprom\NvM\NvM.c	   283   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   284   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   285   *  \config DET error detection and Block Pending check are enabled
; ..\eeprom\NvM\NvM.c	   286   *  \pre -
; ..\eeprom\NvM\NvM.c	   287   */
; ..\eeprom\NvM\NvM.c	   288  NVM_LOCAL_INLINE boolean NvM_CheckBlockPending(const NvM_BlockIdType BlockId);
; ..\eeprom\NvM\NvM.c	   289  
; ..\eeprom\NvM\NvM.c	   290  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   291   * NvM_GetMngmtAreaPtr
; ..\eeprom\NvM\NvM.c	   292   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   293  /*! \brief Returns the pointer to management area.
; ..\eeprom\NvM\NvM.c	   294   *  \details The returned management area pointer depends on given Block Id - is it a DCM or normal NvM Block Id?
; ..\eeprom\NvM\NvM.c	   295   *           The given Block Id has to be valid - the function does not check the given parameter!
; ..\eeprom\NvM\NvM.c	   296   *  \param[in] BlockId in range [1, (number of blocks - 1)].
; ..\eeprom\NvM\NvM.c	   297   *  \return pointer to NvM Block's management area, if given Block Id is a NvM Block Id
; ..\eeprom\NvM\NvM.c	   298   *  \return pointer to the DCM Block management area, if the given Block Id is a DCM Block Id
; ..\eeprom\NvM\NvM.c	   299   *  \context TASK
; ..\eeprom\NvM\NvM.c	   300   *  \reentrant FALSE
; ..\eeprom\NvM\NvM.c	   301   *  \synchronous TRUE
; ..\eeprom\NvM\NvM.c	   302   *  \pre -
; ..\eeprom\NvM\NvM.c	   303   */
; ..\eeprom\NvM\NvM.c	   304  NVM_LOCAL_INLINE NvM_RamMngmtPtrType NvM_GetMngmtAreaPtr(const NvM_BlockIdType BlockId);
; ..\eeprom\NvM\NvM.c	   305  
; ..\eeprom\NvM\NvM.c	   306  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   307  *  NvM_Init
; ..\eeprom\NvM\NvM.c	   308  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   309  /*!
; ..\eeprom\NvM\NvM.c	   310   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   311   *
; ..\eeprom\NvM\NvM.c	   312   *
; ..\eeprom\NvM\NvM.c	   313   *
; ..\eeprom\NvM\NvM.c	   314   *
; ..\eeprom\NvM\NvM.c	   315   */
; ..\eeprom\NvM\NvM.c	   316  FUNC(void, NVM_PUBLIC_CODE) NvM_Init(void)
; Function NvM_Init
.L72:
NvM_Init:	.type	func

; ..\eeprom\NvM\NvM.c	   317  {
; ..\eeprom\NvM\NvM.c	   318      uint16_least NvM_BlockCount_u16loc;
; ..\eeprom\NvM\NvM.c	   319      /* #10 Initialize queue (only if API class > 1). */
; ..\eeprom\NvM\NvM.c	   320  #if (NVM_API_CONFIG_CLASS_1 != NVM_API_CONFIG_CLASS)
; ..\eeprom\NvM\NvM.c	   321      NvM_QueueInit();
	call	NvM_QueueInit
.L479:

; ..\eeprom\NvM\NvM.c	   322  #endif
; ..\eeprom\NvM\NvM.c	   323      /* #20 Initialize Crc queue (only if SetRamBlockStatusApi and CalcRamBlockCrc enabled). */
; ..\eeprom\NvM\NvM.c	   324  #if((NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) && (NVM_CALC_RAM_CRC_USED == STD_ON))
; ..\eeprom\NvM\NvM.c	   325      NvM_CrcQueueInit();
; ..\eeprom\NvM\NvM.c	   326      /* Initialize crc Job with "dummy job" -> CRC for Block 0. It will never be executed, due to its Block Descriptor values. */
; ..\eeprom\NvM\NvM.c	   327      NvM_CrcJob_Create(&NvM_AsyncCrcJob_t, (NvM_BlockIdType)0, NULL_PTR, (uint16)0); /* SBSW_NvM_FuncCall_PtrParam_CrcAsyncJob */
; ..\eeprom\NvM\NvM.c	   328  #endif
; ..\eeprom\NvM\NvM.c	   329      /* #30 Reset internal variables. */
; ..\eeprom\NvM\NvM.c	   330      /* No mutli block requested and the multi block result is OK. */
; ..\eeprom\NvM\NvM.c	   331      NvM_ApiFlags_u8 = 0u;
	mov	d15,#0
	movh.a	a15,#@his(NvM_ApiFlags_u8)
.L480:
	st.b	[a15]@los(NvM_ApiFlags_u8),d15
.L481:

; ..\eeprom\NvM\NvM.c	   332      NvM_BlockMngmtArea_at[0u].NvRamErrorStatus_u8 = NVM_REQ_OK; /* SBSW_NvM_AccessBlockManagementArea */
	fcall	.cocofun_2
.L482:
	st.b	[a15]1,d15
.L483:

; ..\eeprom\NvM\NvM.c	   333  
; ..\eeprom\NvM\NvM.c	   334      /* Set all data indices to zero. */
; ..\eeprom\NvM\NvM.c	   335      NvM_BlockCount_u16loc = NvM_NoOfBlockIds_t;
	movh.a	a2,#@his(NvM_NoOfBlockIds_t)
	ld.hu	d0,[a2]@los(NvM_NoOfBlockIds_t)
.L408:

; ..\eeprom\NvM\NvM.c	   336      do
; ..\eeprom\NvM\NvM.c	   337      {
; ..\eeprom\NvM\NvM.c	   338          --NvM_BlockCount_u16loc;
; ..\eeprom\NvM\NvM.c	   339          NvM_BlockMngmtArea_at[NvM_BlockCount_u16loc].NvDataIndex_t = 0u; /* SBSW_NvM_AccessBlockManagementArea */
	addsc.a	a15,a15,d0,#2

; ..\eeprom\NvM\NvM.c	   340      } while(NvM_BlockCount_u16loc > 0u);
.L2:
	add	d0,#-1
	st.b	[+a15]-4,d15
.L484:
	jne	d0,#0,.L2
.L485:

; ..\eeprom\NvM\NvM.c	   341  
; ..\eeprom\NvM\NvM.c	   342      /* Initialize DCM block Mngmt area. */
; ..\eeprom\NvM\NvM.c	   343      NvM_DcmBlockMngmt_t.NvDataIndex_t = 0u;
	movh.a	a15,#@his(NvM_DcmBlockMngmt_t)
	lea	a15,[a15]@los(NvM_DcmBlockMngmt_t)
.L486:
	st.b	[a15],d15
.L487:

; ..\eeprom\NvM\NvM.c	   344      NvM_DcmBlockMngmt_t.NvRamAttributes_u8 = 0u;
	st.b	[a15]2,d15
.L488:

; ..\eeprom\NvM\NvM.c	   345      NvM_DcmBlockMngmt_t.NvRamErrorStatus_u8 = 0u;
	st.b	[a15]1,d15
.L489:

; ..\eeprom\NvM\NvM.c	   346  
; ..\eeprom\NvM\NvM.c	   347      NvM_JobProcInit();
	j	NvM_JobProcInit
.L287:
	
__NvM_Init_function_end:
	.size	NvM_Init,__NvM_Init_function_end-NvM_Init
.L139:
	; End of function
	
	.sdecl	'.text.NvM..cocofun_2',code,cluster('.cocofun_2')
	.sect	'.text.NvM..cocofun_2'
	.align	2
; Function .cocofun_2
.L74:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	movh.a	a15,#@his(NvM_BlockMngmtArea_at)
	lea	a15,[a15]@los(NvM_BlockMngmtArea_at)
.L732:
	fret
.L279:
	; End of function
	.sdecl	'.text.NvM.NvM_SetDataIndex',code,cluster('NvM_SetDataIndex')
	.sect	'.text.NvM.NvM_SetDataIndex'
	.align	2
	
	.global	NvM_SetDataIndex

; ..\eeprom\NvM\NvM.c	   348  }
; ..\eeprom\NvM\NvM.c	   349  
; ..\eeprom\NvM\NvM.c	   350  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   351  *  NvM_SetDataIndex
; ..\eeprom\NvM\NvM.c	   352  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   353  /*!
; ..\eeprom\NvM\NvM.c	   354   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   355   *
; ..\eeprom\NvM\NvM.c	   356   *
; ..\eeprom\NvM\NvM.c	   357   *
; ..\eeprom\NvM\NvM.c	   358   *
; ..\eeprom\NvM\NvM.c	   359   *
; ..\eeprom\NvM\NvM.c	   360   *
; ..\eeprom\NvM\NvM.c	   361   *
; ..\eeprom\NvM\NvM.c	   362   *
; ..\eeprom\NvM\NvM.c	   363   *
; ..\eeprom\NvM\NvM.c	   364   *
; ..\eeprom\NvM\NvM.c	   365   *
; ..\eeprom\NvM\NvM.c	   366   */
; ..\eeprom\NvM\NvM.c	   367  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_SetDataIndex(NvM_BlockIdType BlockId, uint8 DataIndex)
; Function NvM_SetDataIndex
.L76:
NvM_SetDataIndex:	.type	func
	mov	d15,d4
.L410:
	mov	d8,d5
.L411:

; ..\eeprom\NvM\NvM.c	   368  {
; ..\eeprom\NvM\NvM.c	   369      Std_ReturnType returnValue = E_NOT_OK;
; ..\eeprom\NvM\NvM.c	   370      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   371  
; ..\eeprom\NvM\NvM.c	   372  #if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   373      if (NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	   374      {
; ..\eeprom\NvM\NvM.c	   375          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	   376      }
; ..\eeprom\NvM\NvM.c	   377      else if(NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   378      {
; ..\eeprom\NvM\NvM.c	   379          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	   380      }
; ..\eeprom\NvM\NvM.c	   381      else if(NvM_CheckBlockPending(BlockId) == TRUE)
; ..\eeprom\NvM\NvM.c	   382      {
; ..\eeprom\NvM\NvM.c	   383          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	   384      }
; ..\eeprom\NvM\NvM.c	   385      else if (NvM_CheckDataIndex(BlockId, DataIndex) == FALSE)
; ..\eeprom\NvM\NvM.c	   386      {
; ..\eeprom\NvM\NvM.c	   387          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	   388      }
; ..\eeprom\NvM\NvM.c	   389      else
; ..\eeprom\NvM\NvM.c	   390  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   391      {
; ..\eeprom\NvM\NvM.c	   392          if(NvM_CheckBlockType(BlockId) == TRUE)
	mov	d9,#1
	call	NvM_CheckBlockType
.L409:
	jeq	d2,#0,.L3
.L297:

; ..\eeprom\NvM\NvM.c	   393          {
; ..\eeprom\NvM\NvM.c	   394              const NvM_RamMngmtPtrType NvM_RamMngt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d4,d15
	call	NvM_GetMngmtAreaPtr
.L412:

; ..\eeprom\NvM\NvM.c	   395  
; ..\eeprom\NvM\NvM.c	   396              /* #21 set data index to given one */
; ..\eeprom\NvM\NvM.c	   397              NvM_RamMngt_ptloc->NvDataIndex_t = DataIndex; /* SBSW_NvM_AccessBlockManagementArea */
	st.b	[a2],d8
.L494:

; ..\eeprom\NvM\NvM.c	   398              /* #22 set return value to successful */
; ..\eeprom\NvM\NvM.c	   399              returnValue = E_OK;
	mov	d9,#0
.L3:

; ..\eeprom\NvM\NvM.c	   400          }
; ..\eeprom\NvM\NvM.c	   401          else
; ..\eeprom\NvM\NvM.c	   402          {
; ..\eeprom\NvM\NvM.c	   403  #if (NVM_DEV_ERROR_REPORT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   404              detErrorId = NVM_E_PARAM_BLOCK_TYPE;
; ..\eeprom\NvM\NvM.c	   405  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   406          }
; ..\eeprom\NvM\NvM.c	   407      }
; ..\eeprom\NvM\NvM.c	   408  
; ..\eeprom\NvM\NvM.c	   409  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   410      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	   411      {
; ..\eeprom\NvM\NvM.c	   412          ////NvM_Errorhook(NVM_SET_DATA_INDEX, detErrorId);
; ..\eeprom\NvM\NvM.c	   413      }
; ..\eeprom\NvM\NvM.c	   414  #else
; ..\eeprom\NvM\NvM.c	   415      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	   416  #endif
; ..\eeprom\NvM\NvM.c	   417  
; ..\eeprom\NvM\NvM.c	   418      return returnValue;
; ..\eeprom\NvM\NvM.c	   419  } /* PRQA S 6050, 6080 */ /* MD_MSR_STCAL */
	mov	d2,d9
	ret
.L292:
	
__NvM_SetDataIndex_function_end:
	.size	NvM_SetDataIndex,__NvM_SetDataIndex_function_end-NvM_SetDataIndex
.L144:
	; End of function
	
	.sdecl	'.text.NvM.NvM_GetDataIndex',code,cluster('NvM_GetDataIndex')
	.sect	'.text.NvM.NvM_GetDataIndex'
	.align	2
	
	.global	NvM_GetDataIndex

; ..\eeprom\NvM\NvM.c	   420  
; ..\eeprom\NvM\NvM.c	   421  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   422  *  NvM_GetDataIndex
; ..\eeprom\NvM\NvM.c	   423  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   424  /*!
; ..\eeprom\NvM\NvM.c	   425   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   426   *
; ..\eeprom\NvM\NvM.c	   427   *
; ..\eeprom\NvM\NvM.c	   428   *
; ..\eeprom\NvM\NvM.c	   429   *
; ..\eeprom\NvM\NvM.c	   430   *
; ..\eeprom\NvM\NvM.c	   431   *
; ..\eeprom\NvM\NvM.c	   432   *
; ..\eeprom\NvM\NvM.c	   433   *
; ..\eeprom\NvM\NvM.c	   434   *
; ..\eeprom\NvM\NvM.c	   435   *
; ..\eeprom\NvM\NvM.c	   436   */
; ..\eeprom\NvM\NvM.c	   437  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_GetDataIndex(NvM_BlockIdType BlockId, P2VAR(uint8, AUTOMATIC, NVM_APPL_DATA) DataIndexPtr)
; Function NvM_GetDataIndex
.L78:
NvM_GetDataIndex:	.type	func
	mov	d15,d4
	mov.aa	a15,a4
.L414:

; ..\eeprom\NvM\NvM.c	   438  {
; ..\eeprom\NvM\NvM.c	   439      Std_ReturnType returnValue = E_NOT_OK;
; ..\eeprom\NvM\NvM.c	   440      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   441  
; ..\eeprom\NvM\NvM.c	   442  #if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   443      /* check pointer first - for all other errors the pointed value shall be set to 0 */
; ..\eeprom\NvM\NvM.c	   444      if(NvM_CheckNotNull(DataIndexPtr) == FALSE) /* SBSW_NvM_FuncCall_PtrParam_ParamChecker */
; ..\eeprom\NvM\NvM.c	   445      {
; ..\eeprom\NvM\NvM.c	   446          detErrorId = NVM_E_PARAM_DATA;
; ..\eeprom\NvM\NvM.c	   447      }
; ..\eeprom\NvM\NvM.c	   448      else if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	   449      {
; ..\eeprom\NvM\NvM.c	   450          *DataIndexPtr = 0u; /* SBSW_NvM_PtrAccess_PublicAPI */
; ..\eeprom\NvM\NvM.c	   451  
; ..\eeprom\NvM\NvM.c	   452          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	   453      }
; ..\eeprom\NvM\NvM.c	   454      else if(NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   455      {
; ..\eeprom\NvM\NvM.c	   456          *DataIndexPtr = 0u; /* SBSW_NvM_PtrAccess_PublicAPI */
; ..\eeprom\NvM\NvM.c	   457  
; ..\eeprom\NvM\NvM.c	   458          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	   459      }
; ..\eeprom\NvM\NvM.c	   460      else
; ..\eeprom\NvM\NvM.c	   461  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   462      {
; ..\eeprom\NvM\NvM.c	   463          if(NvM_CheckBlockType(BlockId) == TRUE)
	mov	d8,#1
	call	NvM_CheckBlockType
.L413:
	jeq	d2,#0,.L5
.L305:

; ..\eeprom\NvM\NvM.c	   464          {
; ..\eeprom\NvM\NvM.c	   465              const NvM_RamMngmtPtrType NvM_RamMngt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d4,d15
	call	NvM_GetMngmtAreaPtr
.L416:

; ..\eeprom\NvM\NvM.c	   466  
; ..\eeprom\NvM\NvM.c	   467              *DataIndexPtr = NvM_RamMngt_ptloc->NvDataIndex_t; /* SBSW_NvM_PtrAccess_PublicAPI */
	ld.bu	d15,[a2]
.L415:

; ..\eeprom\NvM\NvM.c	   468  
; ..\eeprom\NvM\NvM.c	   469              returnValue = E_OK;
	mov	d8,#0
	j	.L6
.L5:

; ..\eeprom\NvM\NvM.c	   470          }
; ..\eeprom\NvM\NvM.c	   471          else
; ..\eeprom\NvM\NvM.c	   472          {
; ..\eeprom\NvM\NvM.c	   473              *DataIndexPtr = 0u; /* SBSW_NvM_PtrAccess_PublicAPI */
	mov	d15,#0
.L6:
	st.b	[a15],d15
.L499:

; ..\eeprom\NvM\NvM.c	   474  
; ..\eeprom\NvM\NvM.c	   475  #if (NVM_DEV_ERROR_REPORT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   476              detErrorId = NVM_E_PARAM_BLOCK_TYPE;
; ..\eeprom\NvM\NvM.c	   477  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   478          }
; ..\eeprom\NvM\NvM.c	   479      }
; ..\eeprom\NvM\NvM.c	   480  
; ..\eeprom\NvM\NvM.c	   481  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   482      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	   483      {
; ..\eeprom\NvM\NvM.c	   484          //NvM_Errorhook(NVM_GET_DATA_INDEX, detErrorId);
; ..\eeprom\NvM\NvM.c	   485      }
; ..\eeprom\NvM\NvM.c	   486  #else
; ..\eeprom\NvM\NvM.c	   487      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	   488  #endif
; ..\eeprom\NvM\NvM.c	   489  
; ..\eeprom\NvM\NvM.c	   490      return returnValue;
; ..\eeprom\NvM\NvM.c	   491  }
	mov	d2,d8
	ret
.L300:
	
__NvM_GetDataIndex_function_end:
	.size	NvM_GetDataIndex,__NvM_GetDataIndex_function_end-NvM_GetDataIndex
.L149:
	; End of function
	
	.sdecl	'.text.NvM.NvM_SetBlockProtection',code,cluster('NvM_SetBlockProtection')
	.sect	'.text.NvM.NvM_SetBlockProtection'
	.align	2
	
	.global	NvM_SetBlockProtection

; ..\eeprom\NvM\NvM.c	   492  
; ..\eeprom\NvM\NvM.c	   493  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM.c	   494  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   495  *  NvM_SetBlockProtection
; ..\eeprom\NvM\NvM.c	   496  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   497  /*!
; ..\eeprom\NvM\NvM.c	   498   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   499   *
; ..\eeprom\NvM\NvM.c	   500   *
; ..\eeprom\NvM\NvM.c	   501   *
; ..\eeprom\NvM\NvM.c	   502   *
; ..\eeprom\NvM\NvM.c	   503   *
; ..\eeprom\NvM\NvM.c	   504   *
; ..\eeprom\NvM\NvM.c	   505   *
; ..\eeprom\NvM\NvM.c	   506   *
; ..\eeprom\NvM\NvM.c	   507   *
; ..\eeprom\NvM\NvM.c	   508   *
; ..\eeprom\NvM\NvM.c	   509   *
; ..\eeprom\NvM\NvM.c	   510   *
; ..\eeprom\NvM\NvM.c	   511   */
; ..\eeprom\NvM\NvM.c	   512  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_SetBlockProtection(NvM_BlockIdType BlockId, boolean ProtectionEnabled)
; Function NvM_SetBlockProtection
.L80:
NvM_SetBlockProtection:	.type	func
	mov	e8,d5,d4
.L504:

; ..\eeprom\NvM\NvM.c	   513  {
; ..\eeprom\NvM\NvM.c	   514      Std_ReturnType returnValue = E_NOT_OK;
; ..\eeprom\NvM\NvM.c	   515      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   516  
; ..\eeprom\NvM\NvM.c	   517  # if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   518      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	   519      {
; ..\eeprom\NvM\NvM.c	   520          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	   521      }
; ..\eeprom\NvM\NvM.c	   522      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   523      {
; ..\eeprom\NvM\NvM.c	   524          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	   525      }
; ..\eeprom\NvM\NvM.c	   526      else if (NvM_CheckCurrDataIndex(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   527      {
; ..\eeprom\NvM\NvM.c	   528          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	   529      }
; ..\eeprom\NvM\NvM.c	   530      else if(NvM_CheckBlockPending(BlockId) == TRUE)
; ..\eeprom\NvM\NvM.c	   531      {
; ..\eeprom\NvM\NvM.c	   532          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	   533      }
; ..\eeprom\NvM\NvM.c	   534      else
; ..\eeprom\NvM\NvM.c	   535  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   536      {
; ..\eeprom\NvM\NvM.c	   537          const NvM_RamMngmtPtrType NvM_RamMngmt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d10,#1
	call	NvM_GetMngmtAreaPtr
.L312:

; ..\eeprom\NvM\NvM.c	   538  
; ..\eeprom\NvM\NvM.c	   539          /* always check block not to be write once!
; ..\eeprom\NvM\NvM.c	   540           * In case the Development error detecting or reporting is disabled, no error will be reported but the write protection wont be set */
; ..\eeprom\NvM\NvM.c	   541          if((NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].Flags_u8 & NVM_BLOCK_WRITE_BLOCK_ONCE_ON) == 0u)
	insert	d15,d8,#0,#15,#17
	mov.aa	a15,a2
.L417:
	movh.a	a2,#@his(NvM_BlockDescriptorTable_at)
.L418:
	lea	a2,[a2]@los(NvM_BlockDescriptorTable_at)
.L505:
	sha	d15,#6
.L506:
	addsc.a	a2,a2,d15,#0
.L507:
	ld.bu	d15,[a2]59
.L508:
	jnz.t	d15:2,.L8
.L509:

; ..\eeprom\NvM\NvM.c	   542          {
; ..\eeprom\NvM\NvM.c	   543              NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L510:

; ..\eeprom\NvM\NvM.c	   544  
; ..\eeprom\NvM\NvM.c	   545              if (ProtectionEnabled)
; ..\eeprom\NvM\NvM.c	   546              {
; ..\eeprom\NvM\NvM.c	   547                  NvM_RamMngmt_ptloc->NvRamAttributes_u8 |= NVM_WR_PROT_SET; /* SBSW_NvM_AccessBlockManagementArea */
	ld.bu	d15,[a15]2
.L419:
	jeq	d9,#0,.L9
.L420:
	or	d15,#128
	j	.L10
.L9:

; ..\eeprom\NvM\NvM.c	   548              }
; ..\eeprom\NvM\NvM.c	   549              else
; ..\eeprom\NvM\NvM.c	   550              {
; ..\eeprom\NvM\NvM.c	   551                  NvM_RamMngmt_ptloc->NvRamAttributes_u8 &= NVM_WR_PROT_CL; /* SBSW_NvM_AccessBlockManagementArea */
	and	d15,#127
.L10:
	st.b	[a15]2,d15
.L511:

; ..\eeprom\NvM\NvM.c	   552              }
; ..\eeprom\NvM\NvM.c	   553  
; ..\eeprom\NvM\NvM.c	   554              NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L512:

; ..\eeprom\NvM\NvM.c	   555  
; ..\eeprom\NvM\NvM.c	   556              returnValue = E_OK;
	mov	d10,#0
.L8:

; ..\eeprom\NvM\NvM.c	   557          }
; ..\eeprom\NvM\NvM.c	   558          else
; ..\eeprom\NvM\NvM.c	   559          {
; ..\eeprom\NvM\NvM.c	   560  # if (NVM_DEV_ERROR_REPORT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   561              detErrorId = NVM_E_BLOCK_CONFIG;
; ..\eeprom\NvM\NvM.c	   562  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   563          }
; ..\eeprom\NvM\NvM.c	   564      }
; ..\eeprom\NvM\NvM.c	   565  
; ..\eeprom\NvM\NvM.c	   566  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   567      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	   568      {
; ..\eeprom\NvM\NvM.c	   569          //NvM_Errorhook(NVM_SET_BLOCK_PROTECTION, detErrorId);
; ..\eeprom\NvM\NvM.c	   570      }
; ..\eeprom\NvM\NvM.c	   571  # else
; ..\eeprom\NvM\NvM.c	   572      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	   573  # endif
; ..\eeprom\NvM\NvM.c	   574  
; ..\eeprom\NvM\NvM.c	   575      return returnValue;
; ..\eeprom\NvM\NvM.c	   576  } /* PRQA S 6050, 6080 */ /* MD_MSR_STCAL, MD_MSR_STMIF */
	mov	d2,d10
	ret
.L308:
	
__NvM_SetBlockProtection_function_end:
	.size	NvM_SetBlockProtection,__NvM_SetBlockProtection_function_end-NvM_SetBlockProtection
.L154:
	; End of function
	
	.sdecl	'.text.NvM.NvM_GetErrorStatus',code,cluster('NvM_GetErrorStatus')
	.sect	'.text.NvM.NvM_GetErrorStatus'
	.align	2
	
	.global	NvM_GetErrorStatus

; ..\eeprom\NvM\NvM.c	   577  #endif /* (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3) */
; ..\eeprom\NvM\NvM.c	   578  
; ..\eeprom\NvM\NvM.c	   579  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   580  *  NvM_GetErrorStatus
; ..\eeprom\NvM\NvM.c	   581  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   582  /*!
; ..\eeprom\NvM\NvM.c	   583   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   584   *
; ..\eeprom\NvM\NvM.c	   585   *
; ..\eeprom\NvM\NvM.c	   586   *
; ..\eeprom\NvM\NvM.c	   587   *
; ..\eeprom\NvM\NvM.c	   588   *
; ..\eeprom\NvM\NvM.c	   589   *
; ..\eeprom\NvM\NvM.c	   590   *
; ..\eeprom\NvM\NvM.c	   591   *
; ..\eeprom\NvM\NvM.c	   592   *
; ..\eeprom\NvM\NvM.c	   593   */
; ..\eeprom\NvM\NvM.c	   594  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_GetErrorStatus(NvM_BlockIdType BlockId,
; Function NvM_GetErrorStatus
.L82:
NvM_GetErrorStatus:	.type	func
	mov	d15,d4
	mov.aa	a15,a4
.L422:

; ..\eeprom\NvM\NvM.c	   595      P2VAR(NvM_RequestResultType, AUTOMATIC, NVM_APPL_DATA) RequestResultPtr)
; ..\eeprom\NvM\NvM.c	   596  {
; ..\eeprom\NvM\NvM.c	   597      Std_ReturnType returnValue = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
; ..\eeprom\NvM\NvM.c	   598      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   599  
; ..\eeprom\NvM\NvM.c	   600  #if(NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   601      if (NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	   602      {
; ..\eeprom\NvM\NvM.c	   603          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	   604      }
; ..\eeprom\NvM\NvM.c	   605      /* we do not use the Det-function! GetErrorStatus is allowed for the multi block Id (0x0) */
; ..\eeprom\NvM\NvM.c	   606      else if (NVM_BLOCK_FROM_DCM_ID(BlockId) >= NvM_NoOfBlockIds_t)
; ..\eeprom\NvM\NvM.c	   607      {
; ..\eeprom\NvM\NvM.c	   608          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	   609      }
; ..\eeprom\NvM\NvM.c	   610      else if (NvM_CheckNotNull(RequestResultPtr) == FALSE) /* SBSW_NvM_FuncCall_PtrParam_ParamChecker */
; ..\eeprom\NvM\NvM.c	   611      {
; ..\eeprom\NvM\NvM.c	   612          detErrorId = NVM_E_PARAM_DATA;
; ..\eeprom\NvM\NvM.c	   613      }
; ..\eeprom\NvM\NvM.c	   614      else
; ..\eeprom\NvM\NvM.c	   615  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   616      {
; ..\eeprom\NvM\NvM.c	   617          if(NvM_CheckBlockPending(BlockId) == TRUE)
	call	NvM_CheckBlockPending
.L421:
	jeq	d2,#0,.L12
.L517:

; ..\eeprom\NvM\NvM.c	   618          {
; ..\eeprom\NvM\NvM.c	   619              *RequestResultPtr = NVM_REQ_PENDING; /* SBSW_NvM_PtrAccess_PublicAPI */
	mov	d15,#2
	j	.L13
.L12:

; ..\eeprom\NvM\NvM.c	   620          }
; ..\eeprom\NvM\NvM.c	   621          else
; ..\eeprom\NvM\NvM.c	   622          {
; ..\eeprom\NvM\NvM.c	   623              const NvM_RamMngmtPtrType NvM_RamMngmt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d4,d15
	call	NvM_GetMngmtAreaPtr
.L423:

; ..\eeprom\NvM\NvM.c	   624  
; ..\eeprom\NvM\NvM.c	   625              /* get the ErrorStatus */
; ..\eeprom\NvM\NvM.c	   626              *RequestResultPtr = NvM_RamMngmt_ptloc->NvRamErrorStatus_u8; /* SBSW_NvM_PtrAccess_PublicAPI */
	ld.bu	d15,[a2]1
.L13:
	st.b	[a15],d15
.L319:

; ..\eeprom\NvM\NvM.c	   627          }
; ..\eeprom\NvM\NvM.c	   628  
; ..\eeprom\NvM\NvM.c	   629          returnValue = E_OK;
; ..\eeprom\NvM\NvM.c	   630      }
; ..\eeprom\NvM\NvM.c	   631  
; ..\eeprom\NvM\NvM.c	   632  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   633      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	   634      {
; ..\eeprom\NvM\NvM.c	   635          //NvM_Errorhook(NVM_GET_ERROR_STATUS, detErrorId);
; ..\eeprom\NvM\NvM.c	   636      }
; ..\eeprom\NvM\NvM.c	   637  #else
; ..\eeprom\NvM\NvM.c	   638      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	   639  #endif
; ..\eeprom\NvM\NvM.c	   640  
; ..\eeprom\NvM\NvM.c	   641      return returnValue;
; ..\eeprom\NvM\NvM.c	   642  }
	mov	d2,#0
	ret
.L315:
	
__NvM_GetErrorStatus_function_end:
	.size	NvM_GetErrorStatus,__NvM_GetErrorStatus_function_end-NvM_GetErrorStatus
.L159:
	; End of function
	
	.sdecl	'.text.NvM.NvM_SetRamBlockStatus',code,cluster('NvM_SetRamBlockStatus')
	.sect	'.text.NvM.NvM_SetRamBlockStatus'
	.align	2
	
	.global	NvM_SetRamBlockStatus

; ..\eeprom\NvM\NvM.c	   643  
; ..\eeprom\NvM\NvM.c	   644  #if (NVM_VERSION_INFO_API == STD_ON)
; ..\eeprom\NvM\NvM.c	   645  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   646  *  NvM_GetVersionInfo
; ..\eeprom\NvM\NvM.c	   647  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   648  /*!
; ..\eeprom\NvM\NvM.c	   649   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   650   *
; ..\eeprom\NvM\NvM.c	   651   *
; ..\eeprom\NvM\NvM.c	   652   *
; ..\eeprom\NvM\NvM.c	   653   *
; ..\eeprom\NvM\NvM.c	   654   *
; ..\eeprom\NvM\NvM.c	   655   *
; ..\eeprom\NvM\NvM.c	   656   */
; ..\eeprom\NvM\NvM.c	   657  FUNC(void, NVM_PUBLIC_CODE) NvM_GetVersionInfo(P2VAR(Std_VersionInfoType, AUTOMATIC, NVM_APPL_DATA) Versioninfo)
; ..\eeprom\NvM\NvM.c	   658  {
; ..\eeprom\NvM\NvM.c	   659      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   660  
; ..\eeprom\NvM\NvM.c	   661  # if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   662      if (Versioninfo == NULL_PTR)
; ..\eeprom\NvM\NvM.c	   663      {
; ..\eeprom\NvM\NvM.c	   664          detErrorId = NVM_E_PARAM_DATA;
; ..\eeprom\NvM\NvM.c	   665      }
; ..\eeprom\NvM\NvM.c	   666      else
; ..\eeprom\NvM\NvM.c	   667  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   668      {
; ..\eeprom\NvM\NvM.c	   669          Versioninfo->vendorID         = NVM_VENDOR_ID; /* SBSW_NvM_PtrAccess_PublicAPI */
; ..\eeprom\NvM\NvM.c	   670          Versioninfo->moduleID         = NVM_MODULE_ID; /* SBSW_NvM_PtrAccess_PublicAPI */
; ..\eeprom\NvM\NvM.c	   671          Versioninfo->sw_major_version = NVM_SW_MAJOR_VERSION; /* SBSW_NvM_PtrAccess_PublicAPI */
; ..\eeprom\NvM\NvM.c	   672          Versioninfo->sw_minor_version = NVM_SW_MINOR_VERSION; /* SBSW_NvM_PtrAccess_PublicAPI */
; ..\eeprom\NvM\NvM.c	   673          Versioninfo->sw_patch_version = NVM_SW_PATCH_VERSION; /* SBSW_NvM_PtrAccess_PublicAPI */
; ..\eeprom\NvM\NvM.c	   674      }
; ..\eeprom\NvM\NvM.c	   675  
; ..\eeprom\NvM\NvM.c	   676  # if(NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   677      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	   678      {
; ..\eeprom\NvM\NvM.c	   679          //NvM_Errorhook(NVM_GET_VERSION_INFO, detErrorId);
; ..\eeprom\NvM\NvM.c	   680      }
; ..\eeprom\NvM\NvM.c	   681  # else
; ..\eeprom\NvM\NvM.c	   682      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	   683  # endif
; ..\eeprom\NvM\NvM.c	   684  }
; ..\eeprom\NvM\NvM.c	   685  #endif /* (NVM_VERSION_INFO_API == STD_ON) */
; ..\eeprom\NvM\NvM.c	   686  
; ..\eeprom\NvM\NvM.c	   687  #if (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)
; ..\eeprom\NvM\NvM.c	   688  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   689  *  NvM_SetRamBlockStatus
; ..\eeprom\NvM\NvM.c	   690  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   691  /*!
; ..\eeprom\NvM\NvM.c	   692   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   693   *
; ..\eeprom\NvM\NvM.c	   694   *
; ..\eeprom\NvM\NvM.c	   695   *
; ..\eeprom\NvM\NvM.c	   696   *
; ..\eeprom\NvM\NvM.c	   697   *
; ..\eeprom\NvM\NvM.c	   698   *
; ..\eeprom\NvM\NvM.c	   699   *
; ..\eeprom\NvM\NvM.c	   700   *
; ..\eeprom\NvM\NvM.c	   701   *
; ..\eeprom\NvM\NvM.c	   702   *
; ..\eeprom\NvM\NvM.c	   703   *
; ..\eeprom\NvM\NvM.c	   704   *
; ..\eeprom\NvM\NvM.c	   705   *
; ..\eeprom\NvM\NvM.c	   706   */
; ..\eeprom\NvM\NvM.c	   707  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_SetRamBlockStatus(NvM_BlockIdType BlockId, boolean BlockChanged)
; Function NvM_SetRamBlockStatus
.L84:
NvM_SetRamBlockStatus:	.type	func

; ..\eeprom\NvM\NvM.c	   708  {
; ..\eeprom\NvM\NvM.c	   709      Std_ReturnType returnValue = E_NOT_OK;
; ..\eeprom\NvM\NvM.c	   710      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   711  
; ..\eeprom\NvM\NvM.c	   712  # if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   713      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	   714      {
; ..\eeprom\NvM\NvM.c	   715          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	   716      }
; ..\eeprom\NvM\NvM.c	   717      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   718      {
; ..\eeprom\NvM\NvM.c	   719          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	   720      }
; ..\eeprom\NvM\NvM.c	   721      else if (NvM_CheckCurrDataIndex(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   722      {
; ..\eeprom\NvM\NvM.c	   723          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	   724      }
; ..\eeprom\NvM\NvM.c	   725      else if(NvM_CheckBlockPending(BlockId) == TRUE)
; ..\eeprom\NvM\NvM.c	   726      {
; ..\eeprom\NvM\NvM.c	   727          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	   728      }
; ..\eeprom\NvM\NvM.c	   729      else
; ..\eeprom\NvM\NvM.c	   730  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   731      {
; ..\eeprom\NvM\NvM.c	   732          /* SetRamBlockStatus API shall only work for origin NvM blocks with permanent RAM */
; ..\eeprom\NvM\NvM.c	   733          if(NvM_CheckAddress(BlockId, NULL_PTR) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_ParamChecker */
	mov	d15,#1
	mov.a	a4,#0
.L425:
	mov	e8,d5,d4
	call	NvM_CheckAddress
.L424:
	jeq	d2,#0,.L15
.L326:

; ..\eeprom\NvM\NvM.c	   734          {
; ..\eeprom\NvM\NvM.c	   735              const NvM_RamMngmtPtrType blockMngmtPtr = NvM_GetMngmtAreaPtr(BlockId);
	mov	d4,d8
	call	NvM_GetMngmtAreaPtr
.L427:
	mov.aa	a15,a2
.L429:

; ..\eeprom\NvM\NvM.c	   736  
; ..\eeprom\NvM\NvM.c	   737              NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L428:

; ..\eeprom\NvM\NvM.c	   738              if (BlockChanged)
; ..\eeprom\NvM\NvM.c	   739              {
; ..\eeprom\NvM\NvM.c	   740                  blockMngmtPtr->NvRamAttributes_u8 |= (NVM_STATE_CHANGED_SET | NVM_STATE_VALID_SET); /* SBSW_NvM_AccessBlockManagementArea */
	ld.bu	d15,[a15]2
.L426:
	jeq	d9,#0,.L16
.L430:
	or	d15,#3
	j	.L17
.L16:

; ..\eeprom\NvM\NvM.c	   741  
; ..\eeprom\NvM\NvM.c	   742                  NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM.c	   743  
; ..\eeprom\NvM\NvM.c	   744  # if (NVM_CALC_RAM_CRC_USED == STD_ON)
; ..\eeprom\NvM\NvM.c	   745                  if ((NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].Flags_u8 &
; ..\eeprom\NvM\NvM.c	   746                      (NvM_BitFieldType)NVM_CALC_RAM_BLOCK_CRC_ON) != 0u)
; ..\eeprom\NvM\NvM.c	   747                  {
; ..\eeprom\NvM\NvM.c	   748                      NvM_CrcQueueJob(NVM_BLOCK_FROM_DCM_ID(BlockId));
; ..\eeprom\NvM\NvM.c	   749                  }
; ..\eeprom\NvM\NvM.c	   750  # endif
; ..\eeprom\NvM\NvM.c	   751              }
; ..\eeprom\NvM\NvM.c	   752              else
; ..\eeprom\NvM\NvM.c	   753              {
; ..\eeprom\NvM\NvM.c	   754                  blockMngmtPtr->NvRamAttributes_u8 &= (NVM_STATE_CHANGED_CL & NVM_STATE_VALID_CL); /* SBSW_NvM_AccessBlockManagementArea */
	and	d15,#252
.L17:
	st.b	[a15]2,d15
.L522:

; ..\eeprom\NvM\NvM.c	   755  
; ..\eeprom\NvM\NvM.c	   756                  NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L523:

; ..\eeprom\NvM\NvM.c	   757              }
; ..\eeprom\NvM\NvM.c	   758  
; ..\eeprom\NvM\NvM.c	   759              returnValue = E_OK;
	mov	d15,#0
.L15:

; ..\eeprom\NvM\NvM.c	   760          }
; ..\eeprom\NvM\NvM.c	   761          else
; ..\eeprom\NvM\NvM.c	   762          {
; ..\eeprom\NvM\NvM.c	   763  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   764              detErrorId = NVM_E_PARAM_BLOCK_TYPE;
; ..\eeprom\NvM\NvM.c	   765  #  endif
; ..\eeprom\NvM\NvM.c	   766          }
; ..\eeprom\NvM\NvM.c	   767      }
; ..\eeprom\NvM\NvM.c	   768  
; ..\eeprom\NvM\NvM.c	   769  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   770      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	   771      {
; ..\eeprom\NvM\NvM.c	   772          //NvM_Errorhook(NVM_SET_RAM_BLOCK_STATUS, detErrorId);
; ..\eeprom\NvM\NvM.c	   773      }
; ..\eeprom\NvM\NvM.c	   774  # else
; ..\eeprom\NvM\NvM.c	   775      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	   776  # endif
; ..\eeprom\NvM\NvM.c	   777  
; ..\eeprom\NvM\NvM.c	   778      return returnValue;
; ..\eeprom\NvM\NvM.c	   779  } /* PRQA S 6050, 6080 */ /* MD_MSR_STCAL, MD_MSR_STMIF */
	mov	d2,d15
	ret
.L322:
	
__NvM_SetRamBlockStatus_function_end:
	.size	NvM_SetRamBlockStatus,__NvM_SetRamBlockStatus_function_end-NvM_SetRamBlockStatus
.L164:
	; End of function
	
	.sdecl	'.text.NvM.NvM_ReadBlock',code,cluster('NvM_ReadBlock')
	.sect	'.text.NvM.NvM_ReadBlock'
	.align	2
	
	.global	NvM_ReadBlock

; ..\eeprom\NvM\NvM.c	   780  #endif /* (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) */
; ..\eeprom\NvM\NvM.c	   781  
; ..\eeprom\NvM\NvM.c	   782  #if (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1)
; ..\eeprom\NvM\NvM.c	   783  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   784  *  NvM_ReadBlock
; ..\eeprom\NvM\NvM.c	   785  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   786  /*!
; ..\eeprom\NvM\NvM.c	   787   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   788   *
; ..\eeprom\NvM\NvM.c	   789   *
; ..\eeprom\NvM\NvM.c	   790   *
; ..\eeprom\NvM\NvM.c	   791   *
; ..\eeprom\NvM\NvM.c	   792   *
; ..\eeprom\NvM\NvM.c	   793   *
; ..\eeprom\NvM\NvM.c	   794   *
; ..\eeprom\NvM\NvM.c	   795   *
; ..\eeprom\NvM\NvM.c	   796   *
; ..\eeprom\NvM\NvM.c	   797   *
; ..\eeprom\NvM\NvM.c	   798   *
; ..\eeprom\NvM\NvM.c	   799   *
; ..\eeprom\NvM\NvM.c	   800   *
; ..\eeprom\NvM\NvM.c	   801   *
; ..\eeprom\NvM\NvM.c	   802   *
; ..\eeprom\NvM\NvM.c	   803   */
; ..\eeprom\NvM\NvM.c	   804  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_ReadBlock(NvM_BlockIdType BlockId, P2VAR(void, AUTOMATIC, NVM_APPL_DATA) NvM_DstPtr)
; Function NvM_ReadBlock
.L86:
NvM_ReadBlock:	.type	func

; ..\eeprom\NvM\NvM.c	   805  {
; ..\eeprom\NvM\NvM.c	   806      Std_ReturnType returnValue = E_NOT_OK;
	mov	d8,#1
	mov.aa	a15,a4
.L432:

; ..\eeprom\NvM\NvM.c	   807      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   808  
; ..\eeprom\NvM\NvM.c	   809  # if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   810      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	   811      {
; ..\eeprom\NvM\NvM.c	   812          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	   813      }
; ..\eeprom\NvM\NvM.c	   814      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   815      {
; ..\eeprom\NvM\NvM.c	   816          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	   817      }
; ..\eeprom\NvM\NvM.c	   818      else if (NvM_CheckCurrDataIndex(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   819      {
; ..\eeprom\NvM\NvM.c	   820          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	   821      }
; ..\eeprom\NvM\NvM.c	   822      else if (NvM_CheckAddress(BlockId, NvM_DstPtr) == FALSE) /* SBSW_NvM_FuncCall_PtrParam_ParamChecker */
; ..\eeprom\NvM\NvM.c	   823      {
; ..\eeprom\NvM\NvM.c	   824          detErrorId = NVM_E_PARAM_ADDRESS;
; ..\eeprom\NvM\NvM.c	   825      }
; ..\eeprom\NvM\NvM.c	   826      else if(NvM_CheckBlockPending(BlockId) == TRUE)
; ..\eeprom\NvM\NvM.c	   827      {
; ..\eeprom\NvM\NvM.c	   828          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	   829      }
; ..\eeprom\NvM\NvM.c	   830      else
; ..\eeprom\NvM\NvM.c	   831  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   832      {
; ..\eeprom\NvM\NvM.c	   833          /* PRQA S 0316 1 */ /* MD_NvM_11.5_CastVoidPtrToObjPtr*/
; ..\eeprom\NvM\NvM.c	   834          if (NvM_QueueJob(BlockId, NVM_INT_FID_READ_BLOCK, (NvM_RamAddressType)NvM_DstPtr)) /* SBSW_NvM_FuncCall_PtrParam_QueueJob */
	mov	d5,d8
	mov.aa	a4,a15
.L434:
	mov	d15,d4
	call	NvM_QueueJob
.L431:
	jeq	d2,#0,.L19
.L528:

; ..\eeprom\NvM\NvM.c	   835          {
; ..\eeprom\NvM\NvM.c	   836              if(NvM_DstPtr == NULL_PTR)
	jnz.a	a15,.L20
.L334:

; ..\eeprom\NvM\NvM.c	   837              {
; ..\eeprom\NvM\NvM.c	   838                  const NvM_RamMngmtPtrType NvM_RamMngmt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d4,d15
	call	NvM_GetMngmtAreaPtr
.L436:
	mov.aa	a15,a2
.L433:

; ..\eeprom\NvM\NvM.c	   839  
; ..\eeprom\NvM\NvM.c	   840                  NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L437:

; ..\eeprom\NvM\NvM.c	   841  
; ..\eeprom\NvM\NvM.c	   842                  NvM_RamMngmt_ptloc->NvRamAttributes_u8 &= (NVM_STATE_VALID_CL & NVM_STATE_CHANGED_CL); /* SBSW_NvM_AccessBlockManagementArea */
	ld.bu	d15,[a15]2
.L435:
	and	d15,#252
	st.b	[a15]2,d15
.L529:

; ..\eeprom\NvM\NvM.c	   843  
; ..\eeprom\NvM\NvM.c	   844                  NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L20:

; ..\eeprom\NvM\NvM.c	   845              }
; ..\eeprom\NvM\NvM.c	   846  
; ..\eeprom\NvM\NvM.c	   847              returnValue = E_OK;
	mov	d8,#0
.L19:

; ..\eeprom\NvM\NvM.c	   848          }
; ..\eeprom\NvM\NvM.c	   849      }
; ..\eeprom\NvM\NvM.c	   850  
; ..\eeprom\NvM\NvM.c	   851  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   852      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	   853      {
; ..\eeprom\NvM\NvM.c	   854          //NvM_Errorhook(NVM_READ_BLOCK, detErrorId);
; ..\eeprom\NvM\NvM.c	   855      }
; ..\eeprom\NvM\NvM.c	   856  # else
; ..\eeprom\NvM\NvM.c	   857      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	   858  # endif
; ..\eeprom\NvM\NvM.c	   859  
; ..\eeprom\NvM\NvM.c	   860      return returnValue;
; ..\eeprom\NvM\NvM.c	   861  } /* PRQA S 6050, 6080 */ /* MD_MSR_STCAL, MD_MSR_STMIF */
	mov	d2,d8
	ret
.L329:
	
__NvM_ReadBlock_function_end:
	.size	NvM_ReadBlock,__NvM_ReadBlock_function_end-NvM_ReadBlock
.L169:
	; End of function
	
	.sdecl	'.text.NvM.NvM_WriteBlock',code,cluster('NvM_WriteBlock')
	.sect	'.text.NvM.NvM_WriteBlock'
	.align	2
	
	.global	NvM_WriteBlock

; ..\eeprom\NvM\NvM.c	   862  
; ..\eeprom\NvM\NvM.c	   863  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   864  *  NvM_WriteBlock
; ..\eeprom\NvM\NvM.c	   865  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   866  /*!
; ..\eeprom\NvM\NvM.c	   867   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   868   *
; ..\eeprom\NvM\NvM.c	   869   *
; ..\eeprom\NvM\NvM.c	   870   *
; ..\eeprom\NvM\NvM.c	   871   *
; ..\eeprom\NvM\NvM.c	   872   *
; ..\eeprom\NvM\NvM.c	   873   *
; ..\eeprom\NvM\NvM.c	   874   *
; ..\eeprom\NvM\NvM.c	   875   *
; ..\eeprom\NvM\NvM.c	   876   *
; ..\eeprom\NvM\NvM.c	   877   *
; ..\eeprom\NvM\NvM.c	   878   *
; ..\eeprom\NvM\NvM.c	   879   *
; ..\eeprom\NvM\NvM.c	   880   *
; ..\eeprom\NvM\NvM.c	   881   *
; ..\eeprom\NvM\NvM.c	   882   */
; ..\eeprom\NvM\NvM.c	   883  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_WriteBlock(NvM_BlockIdType BlockId, P2CONST(void, AUTOMATIC, NVM_APPL_DATA) NvM_SrcPtr)
; Function NvM_WriteBlock
.L88:
NvM_WriteBlock:	.type	func
	mov	d15,d4
	mov.aa	a12,a4
.L438:

; ..\eeprom\NvM\NvM.c	   884  {
; ..\eeprom\NvM\NvM.c	   885      Std_ReturnType returnValue = E_NOT_OK;
; ..\eeprom\NvM\NvM.c	   886      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   887  
; ..\eeprom\NvM\NvM.c	   888  # if(NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   889      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	   890      {
; ..\eeprom\NvM\NvM.c	   891          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	   892      }
; ..\eeprom\NvM\NvM.c	   893      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   894      {
; ..\eeprom\NvM\NvM.c	   895          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	   896      }
; ..\eeprom\NvM\NvM.c	   897      else if (NvM_CheckCurrDataIndex(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   898      {
; ..\eeprom\NvM\NvM.c	   899          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	   900      }
; ..\eeprom\NvM\NvM.c	   901      else if (NvM_CheckAddress(BlockId, NvM_SrcPtr) == FALSE) /* SBSW_NvM_FuncCall_PtrParam_ParamChecker */
; ..\eeprom\NvM\NvM.c	   902      {
; ..\eeprom\NvM\NvM.c	   903          detErrorId = NVM_E_PARAM_ADDRESS;
; ..\eeprom\NvM\NvM.c	   904      }
; ..\eeprom\NvM\NvM.c	   905      else if(NvM_CheckBlockPending(BlockId) == TRUE)
; ..\eeprom\NvM\NvM.c	   906      {
; ..\eeprom\NvM\NvM.c	   907          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	   908      }
; ..\eeprom\NvM\NvM.c	   909      else
; ..\eeprom\NvM\NvM.c	   910  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   911      {
; ..\eeprom\NvM\NvM.c	   912          const NvM_RamMngmtPtrType NvM_RamMngmt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d8,#1
	call	NvM_GetMngmtAreaPtr
.L342:
	mov.aa	a15,a2
.L441:

; ..\eeprom\NvM\NvM.c	   913  
; ..\eeprom\NvM\NvM.c	   914          if(NvM_WriteProtectionChecks(NvM_RamMngmt_ptloc) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_BlockMngmtArea */
	mov.aa	a4,a15
.L440:
	call	NvM_WriteProtectionChecks
.L442:
	jeq	d2,#0,.L22
.L534:

; ..\eeprom\NvM\NvM.c	   915          {
; ..\eeprom\NvM\NvM.c	   916              /* PRQA S 0311, 0316 1 */ /* MD_NvM_11.5_CastLossOfConst, MD_NvM_11.5_CastVoidPtrToObjPtr*/
; ..\eeprom\NvM\NvM.c	   917              if (NvM_QueueJob(BlockId, NVM_INT_FID_WRITE_BLOCK, (NvM_RamAddressType)NvM_SrcPtr)) /* SBSW_NvM_FuncCall_PtrParam_QueueJob */
	mov	d4,d15
	mov.aa	a4,a12
.L443:
	mov	d5,#0
	call	NvM_QueueJob
.L444:
	jeq	d2,#0,.L23
.L535:

; ..\eeprom\NvM\NvM.c	   918              {
; ..\eeprom\NvM\NvM.c	   919                  if(NvM_SrcPtr == NULL_PTR)
	jnz.a	a12,.L24
.L536:

; ..\eeprom\NvM\NvM.c	   920                  {
; ..\eeprom\NvM\NvM.c	   921                      NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L537:

; ..\eeprom\NvM\NvM.c	   922  
; ..\eeprom\NvM\NvM.c	   923                      NvM_RamMngmt_ptloc->NvRamAttributes_u8 |= (NVM_STATE_VALID_SET | NVM_STATE_CHANGED_SET); /* SBSW_NvM_AccessBlockManagementArea */
	ld.bu	d15,[a15]2
.L439:
	or	d15,#3
	st.b	[a15]2,d15
.L538:

; ..\eeprom\NvM\NvM.c	   924  
; ..\eeprom\NvM\NvM.c	   925                      NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L24:

; ..\eeprom\NvM\NvM.c	   926                  }
; ..\eeprom\NvM\NvM.c	   927  
; ..\eeprom\NvM\NvM.c	   928                  returnValue = E_OK;
	mov	d8,#0
.L23:
.L22:

; ..\eeprom\NvM\NvM.c	   929              }
; ..\eeprom\NvM\NvM.c	   930          }
; ..\eeprom\NvM\NvM.c	   931      }
; ..\eeprom\NvM\NvM.c	   932  
; ..\eeprom\NvM\NvM.c	   933  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	   934      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	   935      {
; ..\eeprom\NvM\NvM.c	   936          //NvM_Errorhook(NVM_WRITE_BLOCK, detErrorId);
; ..\eeprom\NvM\NvM.c	   937      }
; ..\eeprom\NvM\NvM.c	   938  # else
; ..\eeprom\NvM\NvM.c	   939      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	   940  # endif
; ..\eeprom\NvM\NvM.c	   941  
; ..\eeprom\NvM\NvM.c	   942      return returnValue;
; ..\eeprom\NvM\NvM.c	   943  } /* PRQA S 6050, 6080 */ /* MD_MSR_STCAL, MD_MSR_STMIF */
	mov	d2,d8
	ret
.L337:
	
__NvM_WriteBlock_function_end:
	.size	NvM_WriteBlock,__NvM_WriteBlock_function_end-NvM_WriteBlock
.L174:
	; End of function
	
	.sdecl	'.text.NvM.NvM_RestoreBlockDefaults',code,cluster('NvM_RestoreBlockDefaults')
	.sect	'.text.NvM.NvM_RestoreBlockDefaults'
	.align	2
	
	.global	NvM_RestoreBlockDefaults

; ..\eeprom\NvM\NvM.c	   944  
; ..\eeprom\NvM\NvM.c	   945  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	   946  *  NvM_RestoreBlockDefaults
; ..\eeprom\NvM\NvM.c	   947  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	   948  /*!
; ..\eeprom\NvM\NvM.c	   949   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	   950   *
; ..\eeprom\NvM\NvM.c	   951   *
; ..\eeprom\NvM\NvM.c	   952   *
; ..\eeprom\NvM\NvM.c	   953   *
; ..\eeprom\NvM\NvM.c	   954   *
; ..\eeprom\NvM\NvM.c	   955   *
; ..\eeprom\NvM\NvM.c	   956   *
; ..\eeprom\NvM\NvM.c	   957   *
; ..\eeprom\NvM\NvM.c	   958   *
; ..\eeprom\NvM\NvM.c	   959   *
; ..\eeprom\NvM\NvM.c	   960   *
; ..\eeprom\NvM\NvM.c	   961   *
; ..\eeprom\NvM\NvM.c	   962   *
; ..\eeprom\NvM\NvM.c	   963   *
; ..\eeprom\NvM\NvM.c	   964   */
; ..\eeprom\NvM\NvM.c	   965  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_RestoreBlockDefaults(NvM_BlockIdType BlockId, P2VAR(void, AUTOMATIC, NVM_APPL_DATA) NvM_DstPtr)
; Function NvM_RestoreBlockDefaults
.L90:
NvM_RestoreBlockDefaults:	.type	func
	mov	d15,d4
	mov.aa	a15,a4
.L446:

; ..\eeprom\NvM\NvM.c	   966  {
; ..\eeprom\NvM\NvM.c	   967      Std_ReturnType returnValue = E_NOT_OK;
; ..\eeprom\NvM\NvM.c	   968      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	   969  
; ..\eeprom\NvM\NvM.c	   970  # if(NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   971      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	   972      {
; ..\eeprom\NvM\NvM.c	   973          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	   974      }
; ..\eeprom\NvM\NvM.c	   975      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   976      {
; ..\eeprom\NvM\NvM.c	   977          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	   978      }
; ..\eeprom\NvM\NvM.c	   979      else if (NvM_CheckCurrDataIndex(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	   980      {
; ..\eeprom\NvM\NvM.c	   981          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	   982      }
; ..\eeprom\NvM\NvM.c	   983      else if (NvM_CheckAddress(BlockId, NvM_DstPtr) == FALSE) /* SBSW_NvM_FuncCall_PtrParam_ParamChecker */
; ..\eeprom\NvM\NvM.c	   984      {
; ..\eeprom\NvM\NvM.c	   985          detErrorId = NVM_E_PARAM_ADDRESS;
; ..\eeprom\NvM\NvM.c	   986      }
; ..\eeprom\NvM\NvM.c	   987      else if ((NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].RomBlockDataAddr_pt == NULL_PTR) &&
; ..\eeprom\NvM\NvM.c	   988          (NvM_QryIsInitCallbackConfigured(&NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)]) == FALSE)) /* PRQA S 3415 */ /* MD_NvM_13.5 */ /* SBSW_NvM_FuncCall_PtrParam_ParamChecker */
; ..\eeprom\NvM\NvM.c	   989      {
; ..\eeprom\NvM\NvM.c	   990          detErrorId = NVM_E_BLOCK_CONFIG;
; ..\eeprom\NvM\NvM.c	   991      }
; ..\eeprom\NvM\NvM.c	   992      else if(NvM_CheckBlockPending(BlockId) == TRUE)
; ..\eeprom\NvM\NvM.c	   993      {
; ..\eeprom\NvM\NvM.c	   994          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	   995      }
; ..\eeprom\NvM\NvM.c	   996      else
; ..\eeprom\NvM\NvM.c	   997  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	   998      {
; ..\eeprom\NvM\NvM.c	   999          /* PRQA S 0316 1 */ /* MD_NvM_11.5_CastVoidPtrToObjPtr*/
; ..\eeprom\NvM\NvM.c	  1000          if (NvM_QueueJob(BlockId, NVM_INT_FID_RESTORE_DEFAULTS, (NvM_RamAddressType)NvM_DstPtr)) /* SBSW_NvM_FuncCall_PtrParam_QueueJob */
	mov	d8,#1
	mov.aa	a4,a15
.L449:
	mov	d5,#2
	call	NvM_QueueJob
.L445:
	jeq	d2,#0,.L26
.L543:

; ..\eeprom\NvM\NvM.c	  1001          {
; ..\eeprom\NvM\NvM.c	  1002              if(NvM_DstPtr == NULL_PTR)
	jnz.a	a15,.L27
.L349:

; ..\eeprom\NvM\NvM.c	  1003              {
; ..\eeprom\NvM\NvM.c	  1004                  const NvM_RamMngmtPtrType NvM_RamMngmt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d4,d15
	call	NvM_GetMngmtAreaPtr
.L450:
	mov.aa	a15,a2
.L448:

; ..\eeprom\NvM\NvM.c	  1005  
; ..\eeprom\NvM\NvM.c	  1006                  NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L451:

; ..\eeprom\NvM\NvM.c	  1007  
; ..\eeprom\NvM\NvM.c	  1008                  NvM_RamMngmt_ptloc->NvRamAttributes_u8 &= (NVM_STATE_VALID_CL & NVM_STATE_CHANGED_CL);  /* SBSW_NvM_AccessBlockManagementArea */
	ld.bu	d15,[a15]2
.L447:
	and	d15,#252
	st.b	[a15]2,d15
.L544:

; ..\eeprom\NvM\NvM.c	  1009  
; ..\eeprom\NvM\NvM.c	  1010                  NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L27:

; ..\eeprom\NvM\NvM.c	  1011              }
; ..\eeprom\NvM\NvM.c	  1012  
; ..\eeprom\NvM\NvM.c	  1013              returnValue = E_OK;
	mov	d8,#0
.L26:

; ..\eeprom\NvM\NvM.c	  1014          }
; ..\eeprom\NvM\NvM.c	  1015      }
; ..\eeprom\NvM\NvM.c	  1016  
; ..\eeprom\NvM\NvM.c	  1017  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1018      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1019      {
; ..\eeprom\NvM\NvM.c	  1020          //NvM_Errorhook(NVM_RESTORE_BLOCK_DEFAULTS, detErrorId);
; ..\eeprom\NvM\NvM.c	  1021      }
; ..\eeprom\NvM\NvM.c	  1022  # else
; ..\eeprom\NvM\NvM.c	  1023      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1024  # endif
; ..\eeprom\NvM\NvM.c	  1025  
; ..\eeprom\NvM\NvM.c	  1026      return returnValue;
; ..\eeprom\NvM\NvM.c	  1027  } /* PRQA S 6050, 6080 */ /* MD_MSR_STCAL, MD_MSR_STMIF */
	mov	d2,d8
	ret
.L345:
	
__NvM_RestoreBlockDefaults_function_end:
	.size	NvM_RestoreBlockDefaults,__NvM_RestoreBlockDefaults_function_end-NvM_RestoreBlockDefaults
.L179:
	; End of function
	
	.sdecl	'.text.NvM.NvM_EraseNvBlock',code,cluster('NvM_EraseNvBlock')
	.sect	'.text.NvM.NvM_EraseNvBlock'
	.align	2
	
	.global	NvM_EraseNvBlock

; ..\eeprom\NvM\NvM.c	  1028  #endif /* (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1) */
; ..\eeprom\NvM\NvM.c	  1029  
; ..\eeprom\NvM\NvM.c	  1030  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM.c	  1031  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1032  *  NvM_EraseNvBlock
; ..\eeprom\NvM\NvM.c	  1033  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1034  /*!
; ..\eeprom\NvM\NvM.c	  1035   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1036   *
; ..\eeprom\NvM\NvM.c	  1037   *
; ..\eeprom\NvM\NvM.c	  1038   *
; ..\eeprom\NvM\NvM.c	  1039   *
; ..\eeprom\NvM\NvM.c	  1040   *
; ..\eeprom\NvM\NvM.c	  1041   *
; ..\eeprom\NvM\NvM.c	  1042   *
; ..\eeprom\NvM\NvM.c	  1043   *
; ..\eeprom\NvM\NvM.c	  1044   *
; ..\eeprom\NvM\NvM.c	  1045   *
; ..\eeprom\NvM\NvM.c	  1046   *
; ..\eeprom\NvM\NvM.c	  1047   */
; ..\eeprom\NvM\NvM.c	  1048  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_EraseNvBlock(NvM_BlockIdType BlockId)
; Function NvM_EraseNvBlock
.L92:
NvM_EraseNvBlock:	.type	func
	mov	d8,d4
.L452:

; ..\eeprom\NvM\NvM.c	  1049  {
; ..\eeprom\NvM\NvM.c	  1050      Std_ReturnType returnValue = E_NOT_OK;
; ..\eeprom\NvM\NvM.c	  1051      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1052  
; ..\eeprom\NvM\NvM.c	  1053  # if(NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1054      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1055      {
; ..\eeprom\NvM\NvM.c	  1056          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1057      }
; ..\eeprom\NvM\NvM.c	  1058      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	  1059      {
; ..\eeprom\NvM\NvM.c	  1060          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	  1061      }
; ..\eeprom\NvM\NvM.c	  1062      else if (NvM_CheckCurrDataIndex(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	  1063      {
; ..\eeprom\NvM\NvM.c	  1064          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	  1065      }
; ..\eeprom\NvM\NvM.c	  1066      else if (NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].BlockPrio_u8 != 0u)
; ..\eeprom\NvM\NvM.c	  1067      {
; ..\eeprom\NvM\NvM.c	  1068          detErrorId = NVM_E_BLOCK_CONFIG;
; ..\eeprom\NvM\NvM.c	  1069      }
; ..\eeprom\NvM\NvM.c	  1070      else if(NvM_CheckBlockPending(BlockId) == TRUE)
; ..\eeprom\NvM\NvM.c	  1071      {
; ..\eeprom\NvM\NvM.c	  1072          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	  1073      }
; ..\eeprom\NvM\NvM.c	  1074      else
; ..\eeprom\NvM\NvM.c	  1075  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1076      {
; ..\eeprom\NvM\NvM.c	  1077          const NvM_RamMngmtPtrType NvM_RamMngmt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d15,#1
	call	NvM_GetMngmtAreaPtr
.L355:

; ..\eeprom\NvM\NvM.c	  1078  
; ..\eeprom\NvM\NvM.c	  1079          if(NvM_WriteProtectionChecks(NvM_RamMngmt_ptloc) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_BlockMngmtArea */
	mov.aa	a4,a2
.L454:
	call	NvM_WriteProtectionChecks
.L453:
	jeq	d2,#0,.L29
.L549:

; ..\eeprom\NvM\NvM.c	  1080          {
; ..\eeprom\NvM\NvM.c	  1081              returnValue = NvM_QueueJob(BlockId, NVM_INT_FID_ERASE_BLOCK, NULL_PTR) ? E_OK : E_NOT_OK; /* SBSW_NvM_FuncCall_PtrParam_QueueJob */
	mov	d5,#4
	mov.a	a4,#0
	mov	d4,d8
	call	NvM_QueueJob
.L550:
	eq	d15,d2,#0
.L29:

; ..\eeprom\NvM\NvM.c	  1082          }
; ..\eeprom\NvM\NvM.c	  1083      }
; ..\eeprom\NvM\NvM.c	  1084  
; ..\eeprom\NvM\NvM.c	  1085  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1086      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1087      {
; ..\eeprom\NvM\NvM.c	  1088          //NvM_Errorhook(NVM_ERASE_BLOCK, detErrorId);
; ..\eeprom\NvM\NvM.c	  1089      }
; ..\eeprom\NvM\NvM.c	  1090  # else
; ..\eeprom\NvM\NvM.c	  1091      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1092  # endif
; ..\eeprom\NvM\NvM.c	  1093  
; ..\eeprom\NvM\NvM.c	  1094      return returnValue;
; ..\eeprom\NvM\NvM.c	  1095  } /* PRQA S 6050, 6080 */ /* MD_MSR_STCAL, MD_MSR_STMIF */
	mov	d2,d15
	ret
.L352:
	
__NvM_EraseNvBlock_function_end:
	.size	NvM_EraseNvBlock,__NvM_EraseNvBlock_function_end-NvM_EraseNvBlock
.L184:
	; End of function
	
	.sdecl	'.text.NvM.NvM_InvalidateNvBlock',code,cluster('NvM_InvalidateNvBlock')
	.sect	'.text.NvM.NvM_InvalidateNvBlock'
	.align	2
	
	.global	NvM_InvalidateNvBlock

; ..\eeprom\NvM\NvM.c	  1096  
; ..\eeprom\NvM\NvM.c	  1097  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1098  *  NvM_InvalidateNvBlock
; ..\eeprom\NvM\NvM.c	  1099  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1100  /*!
; ..\eeprom\NvM\NvM.c	  1101   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1102   *
; ..\eeprom\NvM\NvM.c	  1103   *
; ..\eeprom\NvM\NvM.c	  1104   *
; ..\eeprom\NvM\NvM.c	  1105   *
; ..\eeprom\NvM\NvM.c	  1106   *
; ..\eeprom\NvM\NvM.c	  1107   *
; ..\eeprom\NvM\NvM.c	  1108   *
; ..\eeprom\NvM\NvM.c	  1109   *
; ..\eeprom\NvM\NvM.c	  1110   *
; ..\eeprom\NvM\NvM.c	  1111   *
; ..\eeprom\NvM\NvM.c	  1112   */
; ..\eeprom\NvM\NvM.c	  1113  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_InvalidateNvBlock(NvM_BlockIdType BlockId)
; Function NvM_InvalidateNvBlock
.L94:
NvM_InvalidateNvBlock:	.type	func
	mov	d8,d4
.L455:

; ..\eeprom\NvM\NvM.c	  1114  {
; ..\eeprom\NvM\NvM.c	  1115      Std_ReturnType returnValue = E_NOT_OK;
; ..\eeprom\NvM\NvM.c	  1116      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1117  
; ..\eeprom\NvM\NvM.c	  1118  # if(NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1119      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1120      {
; ..\eeprom\NvM\NvM.c	  1121          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1122      }
; ..\eeprom\NvM\NvM.c	  1123      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	  1124      {
; ..\eeprom\NvM\NvM.c	  1125          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	  1126      }
; ..\eeprom\NvM\NvM.c	  1127      else if (NvM_CheckCurrDataIndex(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	  1128      {
; ..\eeprom\NvM\NvM.c	  1129          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	  1130      }
; ..\eeprom\NvM\NvM.c	  1131      else if(NvM_CheckBlockPending(BlockId) == TRUE)
; ..\eeprom\NvM\NvM.c	  1132      {
; ..\eeprom\NvM\NvM.c	  1133          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	  1134      }
; ..\eeprom\NvM\NvM.c	  1135      else
; ..\eeprom\NvM\NvM.c	  1136  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1137      {
; ..\eeprom\NvM\NvM.c	  1138          const NvM_RamMngmtPtrType NvM_RamMngmt_ptloc = NvM_GetMngmtAreaPtr(BlockId);
	mov	d15,#1
	call	NvM_GetMngmtAreaPtr
.L361:

; ..\eeprom\NvM\NvM.c	  1139  
; ..\eeprom\NvM\NvM.c	  1140          if(NvM_WriteProtectionChecks(NvM_RamMngmt_ptloc) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_BlockMngmtArea */
	mov.aa	a4,a2
.L457:
	call	NvM_WriteProtectionChecks
.L456:
	jeq	d2,#0,.L31
.L555:

; ..\eeprom\NvM\NvM.c	  1141          {
; ..\eeprom\NvM\NvM.c	  1142              returnValue = NvM_QueueJob(BlockId, NVM_INT_FID_INVALIDATE_NV_BLOCK, NULL_PTR) ? E_OK : E_NOT_OK; /* SBSW_NvM_FuncCall_PtrParam_QueueJob */
	mov	d5,#3
	mov.a	a4,#0
	mov	d4,d8
	call	NvM_QueueJob
.L556:
	eq	d15,d2,#0
.L31:

; ..\eeprom\NvM\NvM.c	  1143          }
; ..\eeprom\NvM\NvM.c	  1144      }
; ..\eeprom\NvM\NvM.c	  1145  
; ..\eeprom\NvM\NvM.c	  1146  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1147      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1148      {
; ..\eeprom\NvM\NvM.c	  1149          //NvM_Errorhook(NVM_INVALIDATE_NV_BLOCK, detErrorId);
; ..\eeprom\NvM\NvM.c	  1150      }
; ..\eeprom\NvM\NvM.c	  1151  # else
; ..\eeprom\NvM\NvM.c	  1152      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1153  # endif
; ..\eeprom\NvM\NvM.c	  1154  
; ..\eeprom\NvM\NvM.c	  1155      return returnValue;
; ..\eeprom\NvM\NvM.c	  1156  } /* PRQA S 6050, 6080 */ /* MD_MSR_STCAL, MD_MSR_STMIF */
	mov	d2,d15
	ret
.L358:
	
__NvM_InvalidateNvBlock_function_end:
	.size	NvM_InvalidateNvBlock,__NvM_InvalidateNvBlock_function_end-NvM_InvalidateNvBlock
.L189:
	; End of function
	
	.sdecl	'.text.NvM.NvM_CancelJobs',code,cluster('NvM_CancelJobs')
	.sect	'.text.NvM.NvM_CancelJobs'
	.align	2
	
	.global	NvM_CancelJobs

; ..\eeprom\NvM\NvM.c	  1157  #endif /* (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3) */
; ..\eeprom\NvM\NvM.c	  1158  
; ..\eeprom\NvM\NvM.c	  1159  #if (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1)
; ..\eeprom\NvM\NvM.c	  1160  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1161  *  NvM_CancelJobs
; ..\eeprom\NvM\NvM.c	  1162  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1163  /*!
; ..\eeprom\NvM\NvM.c	  1164   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1165   *
; ..\eeprom\NvM\NvM.c	  1166   *
; ..\eeprom\NvM\NvM.c	  1167   *
; ..\eeprom\NvM\NvM.c	  1168   *
; ..\eeprom\NvM\NvM.c	  1169   *
; ..\eeprom\NvM\NvM.c	  1170   *
; ..\eeprom\NvM\NvM.c	  1171   *
; ..\eeprom\NvM\NvM.c	  1172   */
; ..\eeprom\NvM\NvM.c	  1173  FUNC(Std_ReturnType, NVM_PUBLIC_CODE) NvM_CancelJobs(NvM_BlockIdType BlockId)
; Function NvM_CancelJobs
.L96:
NvM_CancelJobs:	.type	func

; ..\eeprom\NvM\NvM.c	  1174  {
; ..\eeprom\NvM\NvM.c	  1175      Std_ReturnType returnValue = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
; ..\eeprom\NvM\NvM.c	  1176      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1177  
; ..\eeprom\NvM\NvM.c	  1178  # if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1179      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1180      {
; ..\eeprom\NvM\NvM.c	  1181          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1182      }
; ..\eeprom\NvM\NvM.c	  1183      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	  1184      {
; ..\eeprom\NvM\NvM.c	  1185          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	  1186      }
; ..\eeprom\NvM\NvM.c	  1187      else
; ..\eeprom\NvM\NvM.c	  1188  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1189      {
; ..\eeprom\NvM\NvM.c	  1190          returnValue = NvM_UnQueueJob(BlockId) ? E_OK : E_NOT_OK;
	call	NvM_UnQueueJob
.L458:

; ..\eeprom\NvM\NvM.c	  1191      }
; ..\eeprom\NvM\NvM.c	  1192  
; ..\eeprom\NvM\NvM.c	  1193  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1194      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1195      {
; ..\eeprom\NvM\NvM.c	  1196          //NvM_Errorhook(NVM_CANCEL_JOBS, detErrorId);
; ..\eeprom\NvM\NvM.c	  1197      }
; ..\eeprom\NvM\NvM.c	  1198  # else
; ..\eeprom\NvM\NvM.c	  1199      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1200  # endif
; ..\eeprom\NvM\NvM.c	  1201  
; ..\eeprom\NvM\NvM.c	  1202      return returnValue;
; ..\eeprom\NvM\NvM.c	  1203  }
	eq	d2,d2,#0
	ret
.L364:
	
__NvM_CancelJobs_function_end:
	.size	NvM_CancelJobs,__NvM_CancelJobs_function_end-NvM_CancelJobs
.L194:
	; End of function
	
	.sdecl	'.text.NvM.NvM_ReadAll',code,cluster('NvM_ReadAll')
	.sect	'.text.NvM.NvM_ReadAll'
	.align	2
	
	.global	NvM_ReadAll

; ..\eeprom\NvM\NvM.c	  1204  #endif /* (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1) */
; ..\eeprom\NvM\NvM.c	  1205  
; ..\eeprom\NvM\NvM.c	  1206  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1207  *  NvM_ReadAll
; ..\eeprom\NvM\NvM.c	  1208  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1209  /*!
; ..\eeprom\NvM\NvM.c	  1210   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1211   *
; ..\eeprom\NvM\NvM.c	  1212   *
; ..\eeprom\NvM\NvM.c	  1213   *
; ..\eeprom\NvM\NvM.c	  1214   *
; ..\eeprom\NvM\NvM.c	  1215   *
; ..\eeprom\NvM\NvM.c	  1216   *
; ..\eeprom\NvM\NvM.c	  1217   *
; ..\eeprom\NvM\NvM.c	  1218   *
; ..\eeprom\NvM\NvM.c	  1219   *
; ..\eeprom\NvM\NvM.c	  1220   *
; ..\eeprom\NvM\NvM.c	  1221   *
; ..\eeprom\NvM\NvM.c	  1222   *
; ..\eeprom\NvM\NvM.c	  1223   *
; ..\eeprom\NvM\NvM.c	  1224   */
; ..\eeprom\NvM\NvM.c	  1225  FUNC(void, NVM_PUBLIC_CODE) NvM_ReadAll(void)
; Function NvM_ReadAll
.L98:
NvM_ReadAll:	.type	func

; ..\eeprom\NvM\NvM.c	  1226  {
; ..\eeprom\NvM\NvM.c	  1227      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1228  
; ..\eeprom\NvM\NvM.c	  1229  #if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1230      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1231      {
; ..\eeprom\NvM\NvM.c	  1232          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1233      }
; ..\eeprom\NvM\NvM.c	  1234      else if (NvM_CheckMultiBlockPending() == TRUE)
; ..\eeprom\NvM\NvM.c	  1235      {
; ..\eeprom\NvM\NvM.c	  1236          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	  1237      }
; ..\eeprom\NvM\NvM.c	  1238      else
; ..\eeprom\NvM\NvM.c	  1239  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1240      {
; ..\eeprom\NvM\NvM.c	  1241          NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L565:

; ..\eeprom\NvM\NvM.c	  1242  
; ..\eeprom\NvM\NvM.c	  1243          NvM_BlockMngmtArea_at[0].NvRamErrorStatus_u8 = NVM_REQ_PENDING; /* SBSW_NvM_AccessBlockManagementArea */
	fcall	.cocofun_1
.L566:

; ..\eeprom\NvM\NvM.c	  1244          NvM_ApiFlags_u8 &= NVM_APIFLAG_KILL_READ_ALL_CL;
	and	d15,#191
.L567:

; ..\eeprom\NvM\NvM.c	  1245          NvM_ApiFlags_u8 |= NVM_APIFLAG_READ_ALL_SET;
	or	d15,#4
	st.b	[a15]@los(NvM_ApiFlags_u8),d15
.L568:

; ..\eeprom\NvM\NvM.c	  1246  
; ..\eeprom\NvM\NvM.c	  1247          NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L569:

; ..\eeprom\NvM\NvM.c	  1248  
; ..\eeprom\NvM\NvM.c	  1249          NvM_MultiBlockCbk(NVM_READ_ALL, NVM_REQ_PENDING);
	mov	d4,#12
.L570:
	mov	d5,#2
	j	NvM_MultiBlockCbk
.L366:
	
__NvM_ReadAll_function_end:
	.size	NvM_ReadAll,__NvM_ReadAll_function_end-NvM_ReadAll
.L199:
	; End of function
	
	.sdecl	'.text.NvM..cocofun_1',code,cluster('.cocofun_1')
	.sect	'.text.NvM..cocofun_1'
	.align	2
; Function .cocofun_1
.L100:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh.a	a15,#@his(NvM_BlockMngmtArea_at+1)
.L726:
	mov	d15,#2
	st.b	[a15]@los(NvM_BlockMngmtArea_at+1),d15
.L727:
	fcall	.cocofun_3
	fret
.L274:
	; End of function
	.sdecl	'.text.NvM..cocofun_3',code,cluster('.cocofun_3')
	.sect	'.text.NvM..cocofun_3'
	.align	2
; Function .cocofun_3
.L102:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:1
	movh.a	a15,#@his(NvM_ApiFlags_u8)
	ld.bu	d15,[a15]@los(NvM_ApiFlags_u8)
.L737:
	fret
.L284:
	; End of function
	.sdecl	'.text.NvM.NvM_KillReadAll',code,cluster('NvM_KillReadAll')
	.sect	'.text.NvM.NvM_KillReadAll'
	.align	2
	
	.global	NvM_KillReadAll

; ..\eeprom\NvM\NvM.c	  1250      }
; ..\eeprom\NvM\NvM.c	  1251  
; ..\eeprom\NvM\NvM.c	  1252  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1253      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1254      {
; ..\eeprom\NvM\NvM.c	  1255          //NvM_Errorhook(NVM_READ_ALL, detErrorId);
; ..\eeprom\NvM\NvM.c	  1256      }
; ..\eeprom\NvM\NvM.c	  1257  #else
; ..\eeprom\NvM\NvM.c	  1258      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1259  #endif
; ..\eeprom\NvM\NvM.c	  1260  }
; ..\eeprom\NvM\NvM.c	  1261  
; ..\eeprom\NvM\NvM.c	  1262  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1263  *  NvM_KillReadAll
; ..\eeprom\NvM\NvM.c	  1264  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1265  /*!
; ..\eeprom\NvM\NvM.c	  1266   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1267   *
; ..\eeprom\NvM\NvM.c	  1268   *
; ..\eeprom\NvM\NvM.c	  1269   *
; ..\eeprom\NvM\NvM.c	  1270   *
; ..\eeprom\NvM\NvM.c	  1271   *
; ..\eeprom\NvM\NvM.c	  1272   *
; ..\eeprom\NvM\NvM.c	  1273   *
; ..\eeprom\NvM\NvM.c	  1274   */
; ..\eeprom\NvM\NvM.c	  1275  FUNC(void, NVM_PUBLIC_CODE) NvM_KillReadAll(void)
; Function NvM_KillReadAll
.L104:
NvM_KillReadAll:	.type	func

; ..\eeprom\NvM\NvM.c	  1276  {
; ..\eeprom\NvM\NvM.c	  1277      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1278  
; ..\eeprom\NvM\NvM.c	  1279  #if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1280      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1281      {
; ..\eeprom\NvM\NvM.c	  1282          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1283      }
; ..\eeprom\NvM\NvM.c	  1284      else
; ..\eeprom\NvM\NvM.c	  1285  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1286      {
; ..\eeprom\NvM\NvM.c	  1287          if((NvM_ApiFlags_u8 & NVM_APIFLAG_READ_ALL_SET) == NVM_APIFLAG_READ_ALL_SET)
	movh.a	a15,#@his(NvM_ApiFlags_u8)
	lea	a15,[a15]@los(NvM_ApiFlags_u8)
	ld.bu	d15,[a15]
.L575:
	jz.t	d15:2,.L34
.L576:

; ..\eeprom\NvM\NvM.c	  1288          {
; ..\eeprom\NvM\NvM.c	  1289              NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L577:

; ..\eeprom\NvM\NvM.c	  1290              NvM_ApiFlags_u8 |= NVM_APIFLAG_KILL_READ_ALL_SET;
	ld.bu	d15,[a15]
.L578:
	or	d15,#64
	st.b	[a15],d15
.L579:

; ..\eeprom\NvM\NvM.c	  1291              NvM_ExitCriticalSection();
	j	NvM_ExitCriticalSection
.L34:

; ..\eeprom\NvM\NvM.c	  1292          }
; ..\eeprom\NvM\NvM.c	  1293      }
; ..\eeprom\NvM\NvM.c	  1294  
; ..\eeprom\NvM\NvM.c	  1295  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1296      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1297      {
; ..\eeprom\NvM\NvM.c	  1298          //NvM_Errorhook(NVM_KILL_READ_ALL, detErrorId);
; ..\eeprom\NvM\NvM.c	  1299      }
; ..\eeprom\NvM\NvM.c	  1300  #else
; ..\eeprom\NvM\NvM.c	  1301      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1302  #endif
; ..\eeprom\NvM\NvM.c	  1303  }
	ret
.L368:
	
__NvM_KillReadAll_function_end:
	.size	NvM_KillReadAll,__NvM_KillReadAll_function_end-NvM_KillReadAll
.L204:
	; End of function
	
	.sdecl	'.text.NvM.NvM_WriteAll',code,cluster('NvM_WriteAll')
	.sect	'.text.NvM.NvM_WriteAll'
	.align	2
	
	.global	NvM_WriteAll

; ..\eeprom\NvM\NvM.c	  1304  
; ..\eeprom\NvM\NvM.c	  1305  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1306  *  NvM_WriteAll
; ..\eeprom\NvM\NvM.c	  1307  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1308  /*!
; ..\eeprom\NvM\NvM.c	  1309   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1310   *
; ..\eeprom\NvM\NvM.c	  1311   *
; ..\eeprom\NvM\NvM.c	  1312   *
; ..\eeprom\NvM\NvM.c	  1313   *
; ..\eeprom\NvM\NvM.c	  1314   *
; ..\eeprom\NvM\NvM.c	  1315   *
; ..\eeprom\NvM\NvM.c	  1316   *
; ..\eeprom\NvM\NvM.c	  1317   *
; ..\eeprom\NvM\NvM.c	  1318   *
; ..\eeprom\NvM\NvM.c	  1319   *
; ..\eeprom\NvM\NvM.c	  1320   *
; ..\eeprom\NvM\NvM.c	  1321   *
; ..\eeprom\NvM\NvM.c	  1322   */
; ..\eeprom\NvM\NvM.c	  1323  FUNC(void, NVM_PUBLIC_CODE) NvM_WriteAll(void)
; Function NvM_WriteAll
.L106:
NvM_WriteAll:	.type	func

; ..\eeprom\NvM\NvM.c	  1324  {
; ..\eeprom\NvM\NvM.c	  1325      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1326  
; ..\eeprom\NvM\NvM.c	  1327  #if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1328      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1329      {
; ..\eeprom\NvM\NvM.c	  1330          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1331      }
; ..\eeprom\NvM\NvM.c	  1332      else if (NvM_CheckMultiBlockPending() == TRUE)
; ..\eeprom\NvM\NvM.c	  1333      {
; ..\eeprom\NvM\NvM.c	  1334          detErrorId = NVM_E_BLOCK_PENDING;
; ..\eeprom\NvM\NvM.c	  1335      }
; ..\eeprom\NvM\NvM.c	  1336      else
; ..\eeprom\NvM\NvM.c	  1337  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1338      {
; ..\eeprom\NvM\NvM.c	  1339          NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L584:

; ..\eeprom\NvM\NvM.c	  1340  
; ..\eeprom\NvM\NvM.c	  1341          NvM_BlockMngmtArea_at[0u].NvRamErrorStatus_u8 = NVM_REQ_PENDING; /* SBSW_NvM_AccessBlockManagementArea */
	fcall	.cocofun_1
.L585:

; ..\eeprom\NvM\NvM.c	  1342  
; ..\eeprom\NvM\NvM.c	  1343          NvM_ApiFlags_u8 &= (NVM_APIFLAG_KILL_WR_ALL_CL & NVM_APIFLAG_CANCEL_WR_ALL_CL);
	and	d15,#207
.L586:

; ..\eeprom\NvM\NvM.c	  1344          NvM_ApiFlags_u8 |= NVM_APIFLAG_WRITE_ALL_SET;
	or	d15,#2
	st.b	[a15]@los(NvM_ApiFlags_u8),d15
.L587:

; ..\eeprom\NvM\NvM.c	  1345  
; ..\eeprom\NvM\NvM.c	  1346          NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L588:

; ..\eeprom\NvM\NvM.c	  1347  
; ..\eeprom\NvM\NvM.c	  1348          NvM_MultiBlockCbk(NVM_WRITE_ALL, NVM_REQ_PENDING);
	mov	d4,#13
.L589:
	mov	d5,#2
	j	NvM_MultiBlockCbk
.L369:
	
__NvM_WriteAll_function_end:
	.size	NvM_WriteAll,__NvM_WriteAll_function_end-NvM_WriteAll
.L209:
	; End of function
	
	.sdecl	'.text.NvM.NvM_CancelWriteAll',code,cluster('NvM_CancelWriteAll')
	.sect	'.text.NvM.NvM_CancelWriteAll'
	.align	2
	
	.global	NvM_CancelWriteAll

; ..\eeprom\NvM\NvM.c	  1349      }
; ..\eeprom\NvM\NvM.c	  1350  
; ..\eeprom\NvM\NvM.c	  1351  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1352      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1353      {
; ..\eeprom\NvM\NvM.c	  1354          //NvM_Errorhook(NVM_WRITE_ALL, detErrorId);
; ..\eeprom\NvM\NvM.c	  1355      }
; ..\eeprom\NvM\NvM.c	  1356  #else
; ..\eeprom\NvM\NvM.c	  1357      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1358  #endif
; ..\eeprom\NvM\NvM.c	  1359  }
; ..\eeprom\NvM\NvM.c	  1360  
; ..\eeprom\NvM\NvM.c	  1361  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1362  *  NvM_CancelWriteAll
; ..\eeprom\NvM\NvM.c	  1363  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1364  /*!
; ..\eeprom\NvM\NvM.c	  1365   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1366   *
; ..\eeprom\NvM\NvM.c	  1367   *
; ..\eeprom\NvM\NvM.c	  1368   *
; ..\eeprom\NvM\NvM.c	  1369   *
; ..\eeprom\NvM\NvM.c	  1370   *
; ..\eeprom\NvM\NvM.c	  1371   *
; ..\eeprom\NvM\NvM.c	  1372   *
; ..\eeprom\NvM\NvM.c	  1373   */
; ..\eeprom\NvM\NvM.c	  1374  FUNC(void, NVM_PUBLIC_CODE) NvM_CancelWriteAll(void)
; Function NvM_CancelWriteAll
.L108:
NvM_CancelWriteAll:	.type	func

; ..\eeprom\NvM\NvM.c	  1375  {
; ..\eeprom\NvM\NvM.c	  1376      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1377  
; ..\eeprom\NvM\NvM.c	  1378  #if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1379      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1380      {
; ..\eeprom\NvM\NvM.c	  1381          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1382      }
; ..\eeprom\NvM\NvM.c	  1383      else
; ..\eeprom\NvM\NvM.c	  1384  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1385      {
; ..\eeprom\NvM\NvM.c	  1386          NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L594:

; ..\eeprom\NvM\NvM.c	  1387  
; ..\eeprom\NvM\NvM.c	  1388          NvM_ApiFlags_u8 |= NVM_APIFLAG_CANCEL_WR_ALL_SET;
	fcall	.cocofun_3
.L595:
	or	d15,#16
	st.b	[a15]@los(NvM_ApiFlags_u8),d15
.L596:

; ..\eeprom\NvM\NvM.c	  1389  
; ..\eeprom\NvM\NvM.c	  1390          NvM_ExitCriticalSection();
	j	NvM_ExitCriticalSection
.L370:
	
__NvM_CancelWriteAll_function_end:
	.size	NvM_CancelWriteAll,__NvM_CancelWriteAll_function_end-NvM_CancelWriteAll
.L214:
	; End of function
	
	.sdecl	'.text.NvM.NvM_KillWriteAll',code,cluster('NvM_KillWriteAll')
	.sect	'.text.NvM.NvM_KillWriteAll'
	.align	2
	
	.global	NvM_KillWriteAll

; ..\eeprom\NvM\NvM.c	  1391      }
; ..\eeprom\NvM\NvM.c	  1392  
; ..\eeprom\NvM\NvM.c	  1393  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1394      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1395      {
; ..\eeprom\NvM\NvM.c	  1396          //NvM_Errorhook(NVM_CANCEL_WRITE_ALL, detErrorId);
; ..\eeprom\NvM\NvM.c	  1397      }
; ..\eeprom\NvM\NvM.c	  1398  #else
; ..\eeprom\NvM\NvM.c	  1399      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1400  #endif
; ..\eeprom\NvM\NvM.c	  1401  }
; ..\eeprom\NvM\NvM.c	  1402  
; ..\eeprom\NvM\NvM.c	  1403  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM.c	  1404  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1405  *  NvM_KillWriteAll
; ..\eeprom\NvM\NvM.c	  1406  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1407  /*!
; ..\eeprom\NvM\NvM.c	  1408   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1409   *
; ..\eeprom\NvM\NvM.c	  1410   *
; ..\eeprom\NvM\NvM.c	  1411   *
; ..\eeprom\NvM\NvM.c	  1412   *
; ..\eeprom\NvM\NvM.c	  1413   *
; ..\eeprom\NvM\NvM.c	  1414   *
; ..\eeprom\NvM\NvM.c	  1415   *
; ..\eeprom\NvM\NvM.c	  1416   *
; ..\eeprom\NvM\NvM.c	  1417   */
; ..\eeprom\NvM\NvM.c	  1418  FUNC(void, NVM_PUBLIC_CODE) NvM_KillWriteAll(void)
; Function NvM_KillWriteAll
.L110:
NvM_KillWriteAll:	.type	func

; ..\eeprom\NvM\NvM.c	  1419  {
; ..\eeprom\NvM\NvM.c	  1420      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1421  
; ..\eeprom\NvM\NvM.c	  1422  # if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1423      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1424      {
; ..\eeprom\NvM\NvM.c	  1425          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1426      }
; ..\eeprom\NvM\NvM.c	  1427      else
; ..\eeprom\NvM\NvM.c	  1428  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1429      {
; ..\eeprom\NvM\NvM.c	  1430          /* Flags indicating, whether deferred function calls shall be done at the end.
; ..\eeprom\NvM\NvM.c	  1431             We don't want them being called from inside a critical section, on the other hand we'd like to keep number
; ..\eeprom\NvM\NvM.c	  1432             of paths (where the critical section must be explicitly left) at a minimum (see ESCAN00072502) */
; ..\eeprom\NvM\NvM.c	  1433          NvM_BlockIdType blockEndNotificationId = 0u; /* 0 (multiblock ID -> invalid in this context) -> no notification  */
	mov	d9,#0
.L459:

; ..\eeprom\NvM\NvM.c	  1434          boolean writeAllEndNotification = FALSE;
; ..\eeprom\NvM\NvM.c	  1435          /* since a NvM_KillWriteAll might be called from a task which preempted NvM_MainFunction,
; ..\eeprom\NvM\NvM.c	  1436             this is a critical section. */
; ..\eeprom\NvM\NvM.c	  1437          NvM_EnterCriticalSection();
	mov	d8,d9
	call	NvM_EnterCriticalSection
.L460:

; ..\eeprom\NvM\NvM.c	  1438  
; ..\eeprom\NvM\NvM.c	  1439          if((NvM_ApiFlags_u8 & NVM_APIFLAG_WRITE_ALL_SET) != 0u)
	movh.a	a15,#@his(NvM_ApiFlags_u8)
	lea	a15,[a15]@los(NvM_ApiFlags_u8)
	ld.bu	d15,[a15]
.L601:
	jz.t	d15:1,.L35
.L602:

; ..\eeprom\NvM\NvM.c	  1440          {
; ..\eeprom\NvM\NvM.c	  1441              /*  Clear WriteAll Flag -> if WriteAll was just queued, but not started yet, it will be simply discarded.
; ..\eeprom\NvM\NvM.c	  1442               *                      -> WriteAll may be requested immediately after we return.
; ..\eeprom\NvM\NvM.c	  1443               *  set both flags, "Cancel WriteAll" and "Kill WriteAll". The former one, makes sure, that
; ..\eeprom\NvM\NvM.c	  1444               *  No Block processing will be started, if WriteAll was not started until now               */
; ..\eeprom\NvM\NvM.c	  1445              NvM_ApiFlags_u8 &= NVM_APIFLAG_WRITE_ALL_CL;
	and	d15,#253
.L603:

; ..\eeprom\NvM\NvM.c	  1446              NvM_ApiFlags_u8 |= NVM_APIFLAG_KILL_WR_ALL_SET | NVM_APIFLAG_CANCEL_WR_ALL_SET;
	or	d15,#48
	st.b	[a15],d15
.L604:

; ..\eeprom\NvM\NvM.c	  1447              /* Check whether current job is actually a writeAll job, and if it actually processes an NVRAM Block */
; ..\eeprom\NvM\NvM.c	  1448              /* PRQA S 3415 1 */ /* MD_NvM_13.5 */
; ..\eeprom\NvM\NvM.c	  1449              if((NvM_CurrentJob_t.JobServiceId_t == NVM_INT_FID_WRITE_ALL) && (NvM_CheckBlockId(NvM_CurrentJob_t.JobBlockId_t))) /* COV_NVM_KILLWRITEALL */
	movh.a	a15,#@his(NvM_CurrentJob_t)
	lea	a15,[a15]@los(NvM_CurrentJob_t)
.L605:
	ld.bu	d15,[a15]2
.L606:
	jne	d15,#5,.L36
.L607:
	ld.hu	d8,[a15]0
.L461:
	mov	d4,d8
	call	NvM_CheckBlockId
.L608:
	jeq	d2,#0,.L37
.L374:

; ..\eeprom\NvM\NvM.c	  1450              {
; ..\eeprom\NvM\NvM.c	  1451                  const NvM_RamMngmtPtrType mngmt_pt = NvM_CurrentBlockInfo_t.Mngmt_pt;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+4)
.L609:
	ld.a	a15,[a15]@los(NvM_CurrentBlockInfo_t+4)
.L462:

; ..\eeprom\NvM\NvM.c	  1452                  blockEndNotificationId = NvM_CurrentJob_t.JobBlockId_t;
	mov	d9,d8
.L610:

; ..\eeprom\NvM\NvM.c	  1453                  /* immediately set it to NVM_REQ_OK, if it is VALID, otherwise set it to skipped */
; ..\eeprom\NvM\NvM.c	  1454                  mngmt_pt->NvRamErrorStatus_u8 = /* SBSW_NvM_Access_CurrBlockInfo */
; ..\eeprom\NvM\NvM.c	  1455                      ((mngmt_pt->NvRamAttributes_u8 & NVM_STATE_VALID_SET) != 0u) ? NVM_REQ_OK : NVM_REQ_BLOCK_SKIPPED;
	ld.bu	d15,[a15]2
.L611:
	and	d15,#1
	eq	d15,d15,#0
.L612:
	sh	d15,#2
	st.b	[a15]1,d15
.L37:
.L36:

; ..\eeprom\NvM\NvM.c	  1456              }
; ..\eeprom\NvM\NvM.c	  1457  
; ..\eeprom\NvM\NvM.c	  1458              /* Mark WriteAll as CANCELLED for the world (EcuM), however the internal handling does not finish immediately */
; ..\eeprom\NvM\NvM.c	  1459              NvM_BlockMngmtArea_at[0].NvRamErrorStatus_u8 = NVM_REQ_CANCELED; /* SBSW_NvM_AccessBlockManagementArea */
	mov	d15,#6
	movh.a	a15,#@his(NvM_BlockMngmtArea_at+1)
.L613:

; ..\eeprom\NvM\NvM.c	  1460  
; ..\eeprom\NvM\NvM.c	  1461              writeAllEndNotification = TRUE;
	mov	d8,#1
	st.b	[a15]@los(NvM_BlockMngmtArea_at+1),d15
.L35:

; ..\eeprom\NvM\NvM.c	  1462          }
; ..\eeprom\NvM\NvM.c	  1463  
; ..\eeprom\NvM\NvM.c	  1464          NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L614:

; ..\eeprom\NvM\NvM.c	  1465  
; ..\eeprom\NvM\NvM.c	  1466          if(blockEndNotificationId > 0u)
	jeq	d9,#0,.L38
.L615:

; ..\eeprom\NvM\NvM.c	  1467          {
; ..\eeprom\NvM\NvM.c	  1468              NvM_BlockNotification(blockEndNotificationId, NVM_WRITE_ALL, NVM_REQ_CANCELED);
	mov	d5,#13
.L616:
	mov	d6,#6
	mov	d4,d9
	call	NvM_BlockNotification
.L38:

; ..\eeprom\NvM\NvM.c	  1469          }
; ..\eeprom\NvM\NvM.c	  1470  
; ..\eeprom\NvM\NvM.c	  1471          if(writeAllEndNotification == TRUE)
	jeq	d8,#0,.L39
.L617:

; ..\eeprom\NvM\NvM.c	  1472          {
; ..\eeprom\NvM\NvM.c	  1473              NvM_MultiBlockCbk(NVM_WRITE_ALL, NVM_REQ_CANCELED);
	mov	d4,#13
.L618:
	mov	d5,#6
	j	NvM_MultiBlockCbk
.L39:

; ..\eeprom\NvM\NvM.c	  1474          }
; ..\eeprom\NvM\NvM.c	  1475      }
; ..\eeprom\NvM\NvM.c	  1476  
; ..\eeprom\NvM\NvM.c	  1477  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1478      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1479      {
; ..\eeprom\NvM\NvM.c	  1480          //NvM_Errorhook(NVM_KILL_WRITE_ALL, detErrorId);
; ..\eeprom\NvM\NvM.c	  1481      }
; ..\eeprom\NvM\NvM.c	  1482  # else
; ..\eeprom\NvM\NvM.c	  1483      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1484  # endif
; ..\eeprom\NvM\NvM.c	  1485  }
	ret
.L371:
	
__NvM_KillWriteAll_function_end:
	.size	NvM_KillWriteAll,__NvM_KillWriteAll_function_end-NvM_KillWriteAll
.L219:
	; End of function
	
	.sdecl	'.text.NvM.NvM_MainFunction',code,cluster('NvM_MainFunction')
	.sect	'.text.NvM.NvM_MainFunction'
	.align	2
	
	.global	NvM_MainFunction

; ..\eeprom\NvM\NvM.c	  1486  #endif /* (NVM_KILL_WRITEALL_API == STD_ON) */
; ..\eeprom\NvM\NvM.c	  1487  
; ..\eeprom\NvM\NvM.c	  1488  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM.c	  1489  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1490  *  NvM_RepairRedundantBlocks
; ..\eeprom\NvM\NvM.c	  1491  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1492  /*!
; ..\eeprom\NvM\NvM.c	  1493   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1494   *
; ..\eeprom\NvM\NvM.c	  1495   *
; ..\eeprom\NvM\NvM.c	  1496   *
; ..\eeprom\NvM\NvM.c	  1497   *
; ..\eeprom\NvM\NvM.c	  1498   *
; ..\eeprom\NvM\NvM.c	  1499   *
; ..\eeprom\NvM\NvM.c	  1500   *
; ..\eeprom\NvM\NvM.c	  1501   */
; ..\eeprom\NvM\NvM.c	  1502  FUNC(void, NVM_PUBLIC_CODE) NvM_RepairRedundantBlocks(void)
; ..\eeprom\NvM\NvM.c	  1503  {
; ..\eeprom\NvM\NvM.c	  1504      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1505  
; ..\eeprom\NvM\NvM.c	  1506  # if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1507      if (NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1508      {
; ..\eeprom\NvM\NvM.c	  1509          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1510      }
; ..\eeprom\NvM\NvM.c	  1511      else
; ..\eeprom\NvM\NvM.c	  1512  # endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1513      {
; ..\eeprom\NvM\NvM.c	  1514          NvM_EnterCriticalSection();
; ..\eeprom\NvM\NvM.c	  1515  
; ..\eeprom\NvM\NvM.c	  1516          NvM_ApiFlags_u8 |= NVM_APIFLAG_REPAIR_REDUNDANT_BLOCKS_SET;
; ..\eeprom\NvM\NvM.c	  1517  
; ..\eeprom\NvM\NvM.c	  1518          NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM.c	  1519      }
; ..\eeprom\NvM\NvM.c	  1520  
; ..\eeprom\NvM\NvM.c	  1521  # if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1522      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1523      {
; ..\eeprom\NvM\NvM.c	  1524          //NvM_Errorhook(NVM_REPAIR_REDUNDANT_BLOCKS, detErrorId);
; ..\eeprom\NvM\NvM.c	  1525      }
; ..\eeprom\NvM\NvM.c	  1526  # else
; ..\eeprom\NvM\NvM.c	  1527      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1528  # endif
; ..\eeprom\NvM\NvM.c	  1529  }
; ..\eeprom\NvM\NvM.c	  1530  #endif /* (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON) */
; ..\eeprom\NvM\NvM.c	  1531  
; ..\eeprom\NvM\NvM.c	  1532  
; ..\eeprom\NvM\NvM.c	  1533  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1534  *  NvM_MainFunction
; ..\eeprom\NvM\NvM.c	  1535  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1536  /*!
; ..\eeprom\NvM\NvM.c	  1537   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1538   *
; ..\eeprom\NvM\NvM.c	  1539   *
; ..\eeprom\NvM\NvM.c	  1540   *
; ..\eeprom\NvM\NvM.c	  1541   *
; ..\eeprom\NvM\NvM.c	  1542   *
; ..\eeprom\NvM\NvM.c	  1543   *
; ..\eeprom\NvM\NvM.c	  1544   *
; ..\eeprom\NvM\NvM.c	  1545   *
; ..\eeprom\NvM\NvM.c	  1546   *
; ..\eeprom\NvM\NvM.c	  1547   */
; ..\eeprom\NvM\NvM.c	  1548  FUNC(void, NVM_PUBLIC_CODE) NvM_MainFunction(void)
; Function NvM_MainFunction
.L112:
NvM_MainFunction:	.type	func

; ..\eeprom\NvM\NvM.c	  1549  {
; ..\eeprom\NvM\NvM.c	  1550      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1551  
; ..\eeprom\NvM\NvM.c	  1552  #if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1553      if (NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1554      {
; ..\eeprom\NvM\NvM.c	  1555          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1556      }
; ..\eeprom\NvM\NvM.c	  1557      else
; ..\eeprom\NvM\NvM.c	  1558  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1559      {
; ..\eeprom\NvM\NvM.c	  1560          /* reset the wait flag */
; ..\eeprom\NvM\NvM.c	  1561          NvM_CurrentBlockInfo_t.InternalFlags_u8 &= NVM_INTFLAG_WAIT_CL;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+42)
.L623:
	lea	a15,[a15]@los(NvM_CurrentBlockInfo_t+42)
	ld.bu	d15,[a15]
.L624:

; ..\eeprom\NvM\NvM.c	  1562  
; ..\eeprom\NvM\NvM.c	  1563  #if ((NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) && (NVM_CALC_RAM_CRC_USED == STD_ON))
; ..\eeprom\NvM\NvM.c	  1564          if(!NvM_CrcJob_isBusy(&NvM_AsyncCrcJob_t))
; ..\eeprom\NvM\NvM.c	  1565          {
; ..\eeprom\NvM\NvM.c	  1566              const NvM_BlockIdType newCrcBlockId = NvM_CrcGetQueuedBlockId();
; ..\eeprom\NvM\NvM.c	  1567              const NvM_BlockDescrPtrType descr_pt = &NvM_BlockDescriptorTable_at[newCrcBlockId];
; ..\eeprom\NvM\NvM.c	  1568              /* Copy CRC from previous CRC job to crc buffer, before we create a new one */
; ..\eeprom\NvM\NvM.c	  1569              NvM_CrcJob_CopyToBuffer(&NvM_AsyncCrcJob_t); /* SBSW_NvM_FuncCall_PtrParam_CrcAsyncJob */
; ..\eeprom\NvM\NvM.c	  1570              /* Create new Crc Job with next BlockId from CRC queue
; ..\eeprom\NvM\NvM.c	  1571               * Note: If the queue is empty we get 0.
; ..\eeprom\NvM\NvM.c	  1572               *       Due to block 0's Descriptor, it will result in an "empty" CRC job.
; ..\eeprom\NvM\NvM.c	  1573               *       We may omit a special check here                                    */
; ..\eeprom\NvM\NvM.c	  1574              NvM_CrcJob_Create(&NvM_AsyncCrcJob_t, newCrcBlockId, descr_pt->RamBlockDataAddr_t, descr_pt->NvBlockLength_u16); /* SBSW_NvM_FuncCall_PtrParam_CrcAsyncJob */
; ..\eeprom\NvM\NvM.c	  1575  # if (NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM.c	  1576              NvM_CrcJob_ReassignBuffer(&NvM_AsyncCrcJob_t, descr_pt->RamBlockCrcAddr_t); /* SBSW_NvM_AccessPtr_CrcReassignBuffer */
; ..\eeprom\NvM\NvM.c	  1577  # endif
; ..\eeprom\NvM\NvM.c	  1578          }
; ..\eeprom\NvM\NvM.c	  1579          /* call the cyclic crc processing function */
; ..\eeprom\NvM\NvM.c	  1580          NvM_CrcJob_Process(&NvM_AsyncCrcJob_t, NvM_NoOfCrcBytes_u16); /* SBSW_NvM_FuncCall_PtrParam_CrcAsyncJob */
; ..\eeprom\NvM\NvM.c	  1581  #endif
; ..\eeprom\NvM\NvM.c	  1582          /* #220 Loop: process state machine as long as the NvM does not have to wait (wait flag). */
; ..\eeprom\NvM\NvM.c	  1583          do
; ..\eeprom\NvM\NvM.c	  1584          {
; ..\eeprom\NvM\NvM.c	  1585              NvM_TaskState_t = NvM_Fsm(NvM_TaskState_t);
	movh.a	a12,#@his(NvM_TaskState_t)
.L625:
	and	d15,#254
	st.b	[a15],d15
.L626:

; ..\eeprom\NvM\NvM.c	  1586  
; ..\eeprom\NvM\NvM.c	  1587              NvM_JobMainState_t = NvM_Fsm(NvM_JobMainState_t);
	movh.a	a13,#@his(NvM_JobMainState_t)
.L627:

; ..\eeprom\NvM\NvM.c	  1588  
; ..\eeprom\NvM\NvM.c	  1589              NvM_JobSubState_t = NvM_Fsm(NvM_JobSubState_t);
	movh.a	a14,#@his(NvM_JobSubState_t)
.L628:
	lea	a12,[a12]@los(NvM_TaskState_t)
.L629:
	lea	a13,[a13]@los(NvM_JobMainState_t)
.L630:
	lea	a14,[a14]@los(NvM_JobSubState_t)

; ..\eeprom\NvM\NvM.c	  1590          }
; ..\eeprom\NvM\NvM.c	  1591          while ((NvM_CurrentBlockInfo_t.InternalFlags_u8 & NVM_INTFLAG_WAIT_SET) == 0u);
.L40:
	ld.bu	d4,[a12]
	call	NvM_Fsm
.L631:
	st.b	[a12],d2
.L632:
	ld.bu	d4,[a13]
	call	NvM_Fsm
.L633:
	st.b	[a13],d2
.L634:
	ld.bu	d4,[a14]
	call	NvM_Fsm
.L635:
	st.b	[a14],d2
.L636:
	ld.bu	d15,[a15]
.L637:
	jz.t	d15:0,.L40
.L638:

; ..\eeprom\NvM\NvM.c	  1592      }
; ..\eeprom\NvM\NvM.c	  1593  
; ..\eeprom\NvM\NvM.c	  1594  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1595      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1596      {
; ..\eeprom\NvM\NvM.c	  1597          //NvM_Errorhook(NVM_MAINFUNCTION, detErrorId);
; ..\eeprom\NvM\NvM.c	  1598      }
; ..\eeprom\NvM\NvM.c	  1599  #else
; ..\eeprom\NvM\NvM.c	  1600      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1601  #endif
; ..\eeprom\NvM\NvM.c	  1602  }
	ret
.L377:
	
__NvM_MainFunction_function_end:
	.size	NvM_MainFunction,__NvM_MainFunction_function_end-NvM_MainFunction
.L224:
	; End of function
	
	.sdecl	'.text.NvM.NvM_JobEndNotification',code,cluster('NvM_JobEndNotification')
	.sect	'.text.NvM.NvM_JobEndNotification'
	.align	2
	
	.global	NvM_JobEndNotification

; ..\eeprom\NvM\NvM.c	  1603  
; ..\eeprom\NvM\NvM.c	  1604  #if (NVM_POLLING_MODE == STD_OFF)
; ..\eeprom\NvM\NvM.c	  1605  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1606  *  NvM_JobEndNotification
; ..\eeprom\NvM\NvM.c	  1607  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1608  /*!
; ..\eeprom\NvM\NvM.c	  1609   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1610   *
; ..\eeprom\NvM\NvM.c	  1611   *
; ..\eeprom\NvM\NvM.c	  1612   */
; ..\eeprom\NvM\NvM.c	  1613  FUNC(void, NVM_PUBLIC_CODE) NvM_JobEndNotification(void)
; Function NvM_JobEndNotification
.L114:
NvM_JobEndNotification:	.type	func

; ..\eeprom\NvM\NvM.c	  1614  {
; ..\eeprom\NvM\NvM.c	  1615       NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_OK;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L649:
	mov	d15,#0
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+40),d15
.L650:

; ..\eeprom\NvM\NvM.c	  1616  }
	ret
.L381:
	
__NvM_JobEndNotification_function_end:
	.size	NvM_JobEndNotification,__NvM_JobEndNotification_function_end-NvM_JobEndNotification
.L234:
	; End of function
	
	.sdecl	'.text.NvM.NvM_JobErrorNotification',code,cluster('NvM_JobErrorNotification')
	.sect	'.text.NvM.NvM_JobErrorNotification'
	.align	2
	
	.global	NvM_JobErrorNotification

; ..\eeprom\NvM\NvM.c	  1617  
; ..\eeprom\NvM\NvM.c	  1618  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1619  *  NvM_JobErrorNotification
; ..\eeprom\NvM\NvM.c	  1620  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1621  /*!
; ..\eeprom\NvM\NvM.c	  1622   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1623   *
; ..\eeprom\NvM\NvM.c	  1624   *
; ..\eeprom\NvM\NvM.c	  1625   *
; ..\eeprom\NvM\NvM.c	  1626   */
; ..\eeprom\NvM\NvM.c	  1627  FUNC(void, NVM_PUBLIC_CODE) NvM_JobErrorNotification(void)
; Function NvM_JobErrorNotification
.L116:
NvM_JobErrorNotification:	.type	func

; ..\eeprom\NvM\NvM.c	  1628  {
; ..\eeprom\NvM\NvM.c	  1629      /* sets LastResult depending on JobResult of lower layer */
; ..\eeprom\NvM\NvM.c	  1630      switch(MemIf_GetJobResult((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8))
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t)
	lea	a15,[a15]@los(NvM_CurrentBlockInfo_t)
.L655:
	ld.a	a2,[a15]
.L656:
	ld.bu	d15,[a2]58
.L657:
	and	d4,d15,#15
	call	MemIf_GetJobResult
.L658:

; ..\eeprom\NvM\NvM.c	  1631      {       
; ..\eeprom\NvM\NvM.c	  1632  
; ..\eeprom\NvM\NvM.c	  1633          case MEMIF_BLOCK_INCONSISTENT:
	jeq	d2,#4,.L41
.L659:

; ..\eeprom\NvM\NvM.c	  1634              NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_INTEGRITY_FAILED;
; ..\eeprom\NvM\NvM.c	  1635              break;
; ..\eeprom\NvM\NvM.c	  1636  
; ..\eeprom\NvM\NvM.c	  1637          case MEMIF_BLOCK_INVALID:
	jeq	d2,#5,.L42
.L660:
	j	.L43
.L41:
	mov	d15,#3
	j	.L44
.L42:

; ..\eeprom\NvM\NvM.c	  1638              NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NV_INVALIDATED;
; ..\eeprom\NvM\NvM.c	  1639              break;
	mov	d15,#5
	j	.L45

; ..\eeprom\NvM\NvM.c	  1640  
; ..\eeprom\NvM\NvM.c	  1641          default:
.L43:

; ..\eeprom\NvM\NvM.c	  1642              NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
	mov	d15,#1

; ..\eeprom\NvM\NvM.c	  1643              break;
; ..\eeprom\NvM\NvM.c	  1644      }
; ..\eeprom\NvM\NvM.c	  1645  }
.L44:
.L45:
	st.b	[a15]40,d15
.L661:
	ret
.L382:
	
__NvM_JobErrorNotification_function_end:
	.size	NvM_JobErrorNotification,__NvM_JobErrorNotification_function_end-NvM_JobErrorNotification
.L239:
	; End of function
	
	.sdecl	'.text.NvM.NvM_SetBlockLockStatus',code,cluster('NvM_SetBlockLockStatus')
	.sect	'.text.NvM.NvM_SetBlockLockStatus'
	.align	2
	
	.global	NvM_SetBlockLockStatus

; ..\eeprom\NvM\NvM.c	  1646  #endif /* (NVM_POLLING_MODE == STD_OFF) */
; ..\eeprom\NvM\NvM.c	  1647  
; ..\eeprom\NvM\NvM.c	  1648  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1649  *  NvM_SetBlockLockStatus
; ..\eeprom\NvM\NvM.c	  1650  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1651  /*!
; ..\eeprom\NvM\NvM.c	  1652   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1653   *
; ..\eeprom\NvM\NvM.c	  1654   *
; ..\eeprom\NvM\NvM.c	  1655   *
; ..\eeprom\NvM\NvM.c	  1656   *
; ..\eeprom\NvM\NvM.c	  1657   *
; ..\eeprom\NvM\NvM.c	  1658   *
; ..\eeprom\NvM\NvM.c	  1659   *
; ..\eeprom\NvM\NvM.c	  1660   *
; ..\eeprom\NvM\NvM.c	  1661   *
; ..\eeprom\NvM\NvM.c	  1662   *
; ..\eeprom\NvM\NvM.c	  1663   */
; ..\eeprom\NvM\NvM.c	  1664  FUNC(void, NVM_PUBLIC_CODE) NvM_SetBlockLockStatus(NvM_BlockIdType BlockId, boolean BlockLocked)
; Function NvM_SetBlockLockStatus
.L118:
NvM_SetBlockLockStatus:	.type	func

; ..\eeprom\NvM\NvM.c	  1665  {
; ..\eeprom\NvM\NvM.c	  1666      uint8 detErrorId = NVM_E_NO_ERROR;
; ..\eeprom\NvM\NvM.c	  1667  
; ..\eeprom\NvM\NvM.c	  1668  #if (NVM_DEV_ERROR_DETECT == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1669      if(NvM_CheckInitialized() == FALSE)
; ..\eeprom\NvM\NvM.c	  1670      {
; ..\eeprom\NvM\NvM.c	  1671          detErrorId = NVM_E_NOT_INITIALIZED;
; ..\eeprom\NvM\NvM.c	  1672      }
; ..\eeprom\NvM\NvM.c	  1673      else if (NvM_CheckBlockId(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	  1674      {
; ..\eeprom\NvM\NvM.c	  1675          detErrorId = NVM_E_PARAM_BLOCK_ID;
; ..\eeprom\NvM\NvM.c	  1676      }
; ..\eeprom\NvM\NvM.c	  1677      else if (NvM_CheckCurrDataIndex(BlockId) == FALSE)
; ..\eeprom\NvM\NvM.c	  1678      {
; ..\eeprom\NvM\NvM.c	  1679          detErrorId = NVM_E_PARAM_BLOCK_DATA_IDX;
; ..\eeprom\NvM\NvM.c	  1680      }
; ..\eeprom\NvM\NvM.c	  1681      else
; ..\eeprom\NvM\NvM.c	  1682  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM.c	  1683      {
; ..\eeprom\NvM\NvM.c	  1684          NvM_EnterCriticalSection();
	mov	e8,d5,d4
	call	NvM_EnterCriticalSection
.L463:

; ..\eeprom\NvM\NvM.c	  1685  
; ..\eeprom\NvM\NvM.c	  1686          if (BlockLocked)
; ..\eeprom\NvM\NvM.c	  1687          {
; ..\eeprom\NvM\NvM.c	  1688              NvM_BlockMngmtArea_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].NvRamAttributes_u8 |= NVM_LOCK_STAT_SET; /* SBSW_NvM_AccessBlockManagementArea */
	insert	d15,d8,#0,#15,#17
	fcall	.cocofun_2
.L464:
	addsc.a	a15,a15,d15,#2
.L643:
	ld.bu	d15,[+a15]2
.L465:
	jeq	d9,#0,.L46
.L466:
	or	d15,#64
	j	.L47
.L46:

; ..\eeprom\NvM\NvM.c	  1689          }
; ..\eeprom\NvM\NvM.c	  1690          else
; ..\eeprom\NvM\NvM.c	  1691          {
; ..\eeprom\NvM\NvM.c	  1692              NvM_BlockMngmtArea_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].NvRamAttributes_u8 &= NVM_LOCK_STAT_CL; /* SBSW_NvM_AccessBlockManagementArea */
	and	d15,#191
.L47:
	st.b	[a15],d15
.L644:

; ..\eeprom\NvM\NvM.c	  1693          }
; ..\eeprom\NvM\NvM.c	  1694  
; ..\eeprom\NvM\NvM.c	  1695          NvM_ExitCriticalSection();
	j	NvM_ExitCriticalSection
.L378:
	
__NvM_SetBlockLockStatus_function_end:
	.size	NvM_SetBlockLockStatus,__NvM_SetBlockLockStatus_function_end-NvM_SetBlockLockStatus
.L229:
	; End of function
	
	.sdecl	'.text.NvM.NvM_WriteProtectionChecks',code,cluster('NvM_WriteProtectionChecks')
	.sect	'.text.NvM.NvM_WriteProtectionChecks'
	.align	2
	
	.global	NvM_WriteProtectionChecks

; ..\eeprom\NvM\NvM.c	  1696      }
; ..\eeprom\NvM\NvM.c	  1697  
; ..\eeprom\NvM\NvM.c	  1698  #if (NVM_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1699      if(detErrorId != NVM_E_NO_ERROR)
; ..\eeprom\NvM\NvM.c	  1700      {
; ..\eeprom\NvM\NvM.c	  1701          //NvM_Errorhook(NVM_SET_BLOCK_LOCK_STATUS, detErrorId);
; ..\eeprom\NvM\NvM.c	  1702      }
; ..\eeprom\NvM\NvM.c	  1703  #else
; ..\eeprom\NvM\NvM.c	  1704      NVM_DUMMY_STATEMENT(detErrorId); /* PRQA S 3112 */ /* MD_MSR_DummyStmt */
; ..\eeprom\NvM\NvM.c	  1705  #endif
; ..\eeprom\NvM\NvM.c	  1706  }
; ..\eeprom\NvM\NvM.c	  1707  
; ..\eeprom\NvM\NvM.c	  1708  #if (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1)
; ..\eeprom\NvM\NvM.c	  1709  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1710  *  NvM_WriteProtectionChecks
; ..\eeprom\NvM\NvM.c	  1711  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1712  /*!
; ..\eeprom\NvM\NvM.c	  1713   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1714   *
; ..\eeprom\NvM\NvM.c	  1715   *
; ..\eeprom\NvM\NvM.c	  1716   *
; ..\eeprom\NvM\NvM.c	  1717   *
; ..\eeprom\NvM\NvM.c	  1718   *
; ..\eeprom\NvM\NvM.c	  1719   *
; ..\eeprom\NvM\NvM.c	  1720   */
; ..\eeprom\NvM\NvM.c	  1721  NVM_LOCAL_INLINE boolean NvM_WriteProtectionChecks(const NvM_RamMngmtConstPtrType MngmtPtr)
; Function NvM_WriteProtectionChecks
.L120:
NvM_WriteProtectionChecks:	.type	func

; ..\eeprom\NvM\NvM.c	  1722  {
; ..\eeprom\NvM\NvM.c	  1723      boolean returnValue = FALSE;
; ..\eeprom\NvM\NvM.c	  1724  
; ..\eeprom\NvM\NvM.c	  1725      /* #10 check whether the block is write protected */
; ..\eeprom\NvM\NvM.c	  1726      if ((MngmtPtr->NvRamAttributes_u8 & NVM_WR_PROT_SET) != 0u)
	ld.bu	d15,[a4]2
.L666:
	mov	d2,#0
.L467:
	jnz.t	d15:7,.L48
.L667:

; ..\eeprom\NvM\NvM.c	  1727      {
; ..\eeprom\NvM\NvM.c	  1728          NvM_DemReportErrorWriteProtected();
; ..\eeprom\NvM\NvM.c	  1729      }
; ..\eeprom\NvM\NvM.c	  1730      /*  #20 block is not write protected and is an origin block (not a DCM block) */
; ..\eeprom\NvM\NvM.c	  1731      else if (MngmtPtr != &NvM_DcmBlockMngmt_t)
	movh.a	a15,#@his(NvM_DcmBlockMngmt_t)
	lea	a15,[a15]@los(NvM_DcmBlockMngmt_t)
.L668:
	jeq.a	a4,a15,.L49
.L669:

; ..\eeprom\NvM\NvM.c	  1732      {
; ..\eeprom\NvM\NvM.c	  1733          returnValue = (boolean)((MngmtPtr->NvRamAttributes_u8 & NVM_LOCK_STAT_SET) == 0u);
	and	d15,#64
.L670:
	eq	d2,d15,#0
.L48:

; ..\eeprom\NvM\NvM.c	  1734      }
; ..\eeprom\NvM\NvM.c	  1735      /* #30 all block is not write protected, check is ok */
; ..\eeprom\NvM\NvM.c	  1736      else
; ..\eeprom\NvM\NvM.c	  1737      {
; ..\eeprom\NvM\NvM.c	  1738          returnValue = TRUE;
; ..\eeprom\NvM\NvM.c	  1739      }
; ..\eeprom\NvM\NvM.c	  1740  
; ..\eeprom\NvM\NvM.c	  1741      return returnValue;
; ..\eeprom\NvM\NvM.c	  1742  }
	ret
.L49:
	mov	d2,#1
	ret
.L383:
	
__NvM_WriteProtectionChecks_function_end:
	.size	NvM_WriteProtectionChecks,__NvM_WriteProtectionChecks_function_end-NvM_WriteProtectionChecks
.L244:
	; End of function
	
	.sdecl	'.text.NvM.NvM_CheckBlockType',code,cluster('NvM_CheckBlockType')
	.sect	'.text.NvM.NvM_CheckBlockType'
	.align	2
	
	.global	NvM_CheckBlockType

; ..\eeprom\NvM\NvM.c	  1743  #endif /* (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1) */
; ..\eeprom\NvM\NvM.c	  1744  
; ..\eeprom\NvM\NvM.c	  1745  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1746  *  NvM_CheckBlockType
; ..\eeprom\NvM\NvM.c	  1747  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1748  /*!
; ..\eeprom\NvM\NvM.c	  1749  * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1750   *
; ..\eeprom\NvM\NvM.c	  1751   *
; ..\eeprom\NvM\NvM.c	  1752  */
; ..\eeprom\NvM\NvM.c	  1753  NVM_LOCAL_INLINE boolean NvM_CheckBlockType(const NvM_BlockIdType BlockId)
; Function NvM_CheckBlockType
.L122:
NvM_CheckBlockType:	.type	func

; ..\eeprom\NvM\NvM.c	  1754  {
; ..\eeprom\NvM\NvM.c	  1755    return (boolean)((NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].MngmtType_t & NVM_BLOCK_DATASET) != 0u);
	insert	d15,d4,#0,#15,#17
	movh.a	a15,#@his(NvM_BlockDescriptorTable_at)
	lea	a15,[a15]@los(NvM_BlockDescriptorTable_at)
.L675:
	sha	d15,#6
.L676:
	addsc.a	a15,a15,d15,#0
.L677:
	ld.bu	d15,[a15]58
	extr.u	d15,d15,#4,#2
.L678:
	and	d15,#2
.L679:

; ..\eeprom\NvM\NvM.c	  1756  }
	ne	d2,d15,#0
	ret
.L387:
	
__NvM_CheckBlockType_function_end:
	.size	NvM_CheckBlockType,__NvM_CheckBlockType_function_end-NvM_CheckBlockType
.L249:
	; End of function
	
	.sdecl	'.text.NvM.NvM_CheckAddress',code,cluster('NvM_CheckAddress')
	.sect	'.text.NvM.NvM_CheckAddress'
	.align	2
	
	.global	NvM_CheckAddress

; ..\eeprom\NvM\NvM.c	  1757  
; ..\eeprom\NvM\NvM.c	  1758  # if ((NVM_DEV_ERROR_DETECT == STD_ON) || (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON))
; ..\eeprom\NvM\NvM.c	  1759  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1760  *  NvM_CheckAddress
; ..\eeprom\NvM\NvM.c	  1761  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1762  /*!
; ..\eeprom\NvM\NvM.c	  1763   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1764   *
; ..\eeprom\NvM\NvM.c	  1765   *
; ..\eeprom\NvM\NvM.c	  1766   */
; ..\eeprom\NvM\NvM.c	  1767  NVM_LOCAL_INLINE boolean NvM_CheckAddress(const NvM_BlockIdType BlockId, const void * RamPtr)
; Function NvM_CheckAddress
.L124:
NvM_CheckAddress:	.type	func

; ..\eeprom\NvM\NvM.c	  1768  {
; ..\eeprom\NvM\NvM.c	  1769      NvM_BlockDescrPtrType descPtr = &NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)];
	insert	d0,d4,#0,#15,#17
	movh.a	a15,#@his(NvM_BlockDescriptorTable_at)
.L684:

; ..\eeprom\NvM\NvM.c	  1770      boolean returnValue = TRUE;
	mov	d2,#1
	lea	a15,[a15]@los(NvM_BlockDescriptorTable_at)
.L468:
	sha	d15,d0,#6
.L685:
	addsc.a	a15,a15,d15,#0
.L469:

; ..\eeprom\NvM\NvM.c	  1771  
; ..\eeprom\NvM\NvM.c	  1772      /* for a request setup with RamPtr != NULL_PTR:
; ..\eeprom\NvM\NvM.c	  1773       * - BlockId must be NvM Block Id AND
; ..\eeprom\NvM\NvM.c	  1774       * - permanent RAM OR explicit synchronization has to be configured */
; ..\eeprom\NvM\NvM.c	  1775      if(RamPtr == NULL_PTR) /* COV_NVM_COVEREDINOTHERCFG */
	jnz.a	a4,.L53
.L686:

; ..\eeprom\NvM\NvM.c	  1776      {
; ..\eeprom\NvM\NvM.c	  1777          returnValue = (boolean)((NVM_BLOCK_FROM_DCM_ID(BlockId) == BlockId) &&
	mov	d2,#0
.L687:
	jne	d0,d4,.L54
.L688:

; ..\eeprom\NvM\NvM.c	  1778              ((descPtr->RamBlockDataAddr_t != NULL_PTR) || (descPtr->CbkGetMirrorFunc_pt != NULL_PTR))); /* COV_NVM_COVEREDINOTHERCFG */
	ld.w	d15,[a15]
.L689:
	jne	d15,#0,.L55
.L690:
	ld.w	d15,[a15]24
.L691:
	jeq	d15,#0,.L56
.L55:
	mov	d2,#1
.L56:
.L54:
.L53:

; ..\eeprom\NvM\NvM.c	  1779      }
; ..\eeprom\NvM\NvM.c	  1780  
; ..\eeprom\NvM\NvM.c	  1781      return returnValue;
; ..\eeprom\NvM\NvM.c	  1782  }
	ret
.L390:
	
__NvM_CheckAddress_function_end:
	.size	NvM_CheckAddress,__NvM_CheckAddress_function_end-NvM_CheckAddress
.L254:
	; End of function
	
	.sdecl	'.text.NvM.NvM_CheckBlockId',code,cluster('NvM_CheckBlockId')
	.sect	'.text.NvM.NvM_CheckBlockId'
	.align	2
	
	.global	NvM_CheckBlockId

; ..\eeprom\NvM\NvM.c	  1783  #endif /* (((NVM_DEV_ERROR_DETECT == STD_ON) && (NVM_CFG_CHK_PARAM_POINTER == STD_ON)) || (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)) */
; ..\eeprom\NvM\NvM.c	  1784  
; ..\eeprom\NvM\NvM.c	  1785  # if (NVM_DEV_ERROR_DETECT == STD_ON) || (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM.c	  1786  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1787  *  NvM_CheckBlockId
; ..\eeprom\NvM\NvM.c	  1788  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1789  /*!
; ..\eeprom\NvM\NvM.c	  1790   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1791   *
; ..\eeprom\NvM\NvM.c	  1792   *
; ..\eeprom\NvM\NvM.c	  1793   */
; ..\eeprom\NvM\NvM.c	  1794  NVM_LOCAL_INLINE boolean NvM_CheckBlockId(const NvM_BlockIdType BlockId)
; Function NvM_CheckBlockId
.L126:
NvM_CheckBlockId:	.type	func

; ..\eeprom\NvM\NvM.c	  1795  {
; ..\eeprom\NvM\NvM.c	  1796      return (boolean)((NVM_BLOCK_FROM_DCM_ID(BlockId) > 0u) && (NVM_BLOCK_FROM_DCM_ID(BlockId) < NvM_NoOfBlockIds_t)); /* COV_NVM_COVEREDINOTHERCFG */
	insert	d15,d4,#0,#15,#17
.L696:
	mov	d2,#0
.L697:
	jeq	d15,#0,.L58
.L698:
	movh.a	a15,#@his(NvM_NoOfBlockIds_t)
	ld.hu	d0,[a15]@los(NvM_NoOfBlockIds_t)
.L699:
	ge.u	d15,d15,d0
.L700:
	cmovn	d2,d15,#1
.L58:

; ..\eeprom\NvM\NvM.c	  1797  }
	ret
.L397:
	
__NvM_CheckBlockId_function_end:
	.size	NvM_CheckBlockId,__NvM_CheckBlockId_function_end-NvM_CheckBlockId
.L259:
	; End of function
	
	.sdecl	'.text.NvM.NvM_CheckBlockPending',code,cluster('NvM_CheckBlockPending')
	.sect	'.text.NvM.NvM_CheckBlockPending'
	.align	2
	
	.global	NvM_CheckBlockPending

; ..\eeprom\NvM\NvM.c	  1798  # endif
; ..\eeprom\NvM\NvM.c	  1799  
; ..\eeprom\NvM\NvM.c	  1800  #if (NVM_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\NvM\NvM.c	  1801  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1802  *  NvM_CheckCurrDataIndex
; ..\eeprom\NvM\NvM.c	  1803  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1804  /*!
; ..\eeprom\NvM\NvM.c	  1805   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1806   *
; ..\eeprom\NvM\NvM.c	  1807   *
; ..\eeprom\NvM\NvM.c	  1808   */
; ..\eeprom\NvM\NvM.c	  1809  NVM_LOCAL_INLINE boolean NvM_CheckCurrDataIndex(const NvM_BlockIdType BlockId)
; ..\eeprom\NvM\NvM.c	  1810  {
; ..\eeprom\NvM\NvM.c	  1811      return (boolean)(NvM_BlockMngmtArea_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].NvDataIndex_t <
; ..\eeprom\NvM\NvM.c	  1812          NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].NvBlockCount_u8);
; ..\eeprom\NvM\NvM.c	  1813  }
; ..\eeprom\NvM\NvM.c	  1814  
; ..\eeprom\NvM\NvM.c	  1815  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1816  *  NvM_CheckDataIndex
; ..\eeprom\NvM\NvM.c	  1817  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1818  /*!
; ..\eeprom\NvM\NvM.c	  1819   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1820   *
; ..\eeprom\NvM\NvM.c	  1821   *
; ..\eeprom\NvM\NvM.c	  1822   */
; ..\eeprom\NvM\NvM.c	  1823  NVM_LOCAL_INLINE boolean NvM_CheckDataIndex(const NvM_BlockIdType BlockId, const uint8 DataIndex)
; ..\eeprom\NvM\NvM.c	  1824  {
; ..\eeprom\NvM\NvM.c	  1825      return (boolean)(DataIndex < NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].NvBlockCount_u8);
; ..\eeprom\NvM\NvM.c	  1826  }
; ..\eeprom\NvM\NvM.c	  1827  
; ..\eeprom\NvM\NvM.c	  1828  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1829  *  NvM_CheckNotNull
; ..\eeprom\NvM\NvM.c	  1830  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1831  /*!
; ..\eeprom\NvM\NvM.c	  1832   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1833   *
; ..\eeprom\NvM\NvM.c	  1834   *
; ..\eeprom\NvM\NvM.c	  1835   */
; ..\eeprom\NvM\NvM.c	  1836  NVM_LOCAL_INLINE boolean NvM_CheckNotNull(const uint8 * Ptr)
; ..\eeprom\NvM\NvM.c	  1837  {
; ..\eeprom\NvM\NvM.c	  1838      return (boolean)(Ptr != NULL_PTR);
; ..\eeprom\NvM\NvM.c	  1839  }
; ..\eeprom\NvM\NvM.c	  1840  
; ..\eeprom\NvM\NvM.c	  1841  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1842  *  NvM_CheckInitialized
; ..\eeprom\NvM\NvM.c	  1843  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1844  /*!
; ..\eeprom\NvM\NvM.c	  1845   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1846   *
; ..\eeprom\NvM\NvM.c	  1847   *
; ..\eeprom\NvM\NvM.c	  1848   */
; ..\eeprom\NvM\NvM.c	  1849  NVM_LOCAL_INLINE boolean NvM_CheckInitialized(void)
; ..\eeprom\NvM\NvM.c	  1850  {
; ..\eeprom\NvM\NvM.c	  1851      return (boolean)(NvM_TaskState_t != NVM_STATE_UNINIT);
; ..\eeprom\NvM\NvM.c	  1852  }
; ..\eeprom\NvM\NvM.c	  1853  
; ..\eeprom\NvM\NvM.c	  1854  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1855  *  NvM_CheckMultiBlockPending
; ..\eeprom\NvM\NvM.c	  1856  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1857  /*!
; ..\eeprom\NvM\NvM.c	  1858   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1859   *
; ..\eeprom\NvM\NvM.c	  1860   *
; ..\eeprom\NvM\NvM.c	  1861   */
; ..\eeprom\NvM\NvM.c	  1862  NVM_LOCAL_INLINE boolean NvM_CheckMultiBlockPending(void)
; ..\eeprom\NvM\NvM.c	  1863  {
; ..\eeprom\NvM\NvM.c	  1864      return (boolean)(NvM_BlockMngmtArea_at[0].NvRamErrorStatus_u8 == NVM_REQ_PENDING);
; ..\eeprom\NvM\NvM.c	  1865  }
; ..\eeprom\NvM\NvM.c	  1866  #endif /* (NVM_DEV_ERROR_DETECT == STD_ON) */
; ..\eeprom\NvM\NvM.c	  1867  
; ..\eeprom\NvM\NvM.c	  1868  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1869  *  NvM_CheckBlockPending
; ..\eeprom\NvM\NvM.c	  1870  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1871  /*!
; ..\eeprom\NvM\NvM.c	  1872   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1873   *
; ..\eeprom\NvM\NvM.c	  1874   *
; ..\eeprom\NvM\NvM.c	  1875   *
; ..\eeprom\NvM\NvM.c	  1876   *
; ..\eeprom\NvM\NvM.c	  1877   *
; ..\eeprom\NvM\NvM.c	  1878   *
; ..\eeprom\NvM\NvM.c	  1879   *
; ..\eeprom\NvM\NvM.c	  1880   *
; ..\eeprom\NvM\NvM.c	  1881   */
; ..\eeprom\NvM\NvM.c	  1882  NVM_LOCAL_INLINE boolean NvM_CheckBlockPending(const NvM_BlockIdType BlockId)
; Function NvM_CheckBlockPending
.L128:
NvM_CheckBlockPending:	.type	func

; ..\eeprom\NvM\NvM.c	  1883  {
; ..\eeprom\NvM\NvM.c	  1884      boolean blockPending;
; ..\eeprom\NvM\NvM.c	  1885  
; ..\eeprom\NvM\NvM.c	  1886      /* #100 required block Id is not the multi block itself and multi block request is pending */
; ..\eeprom\NvM\NvM.c	  1887      if((BlockId > 0u) && ((NvM_CurrentJob_t.JobServiceId_t == NVM_INT_FID_WRITE_ALL) || (NvM_CurrentJob_t.JobServiceId_t == NVM_INT_FID_READ_ALL)))
	jeq	d4,#0,.L61
.L705:
	movh.a	a15,#@his(NvM_CurrentJob_t)
	lea	a15,[a15]@los(NvM_CurrentJob_t)
.L706:
	ld.bu	d15,[a15]2
.L707:
	jeq	d15,#5,.L62
.L708:
	jne	d15,#6,.L63
.L62:

; ..\eeprom\NvM\NvM.c	  1888      {
; ..\eeprom\NvM\NvM.c	  1889          /* #110 NvM_WriteAll is pending */
; ..\eeprom\NvM\NvM.c	  1890          if((NvM_ApiFlags_u8 & NVM_APIFLAG_WRITE_ALL_SET) == NVM_APIFLAG_WRITE_ALL_SET)
; ..\eeprom\NvM\NvM.c	  1891          {
; ..\eeprom\NvM\NvM.c	  1892              /* WriteAll begins with last block and ends with configuration block -> higher block Id than current is done */
; ..\eeprom\NvM\NvM.c	  1893              blockPending = (boolean)(BlockId < NvM_CurrentJob_t.JobBlockId_t);
	ld.hu	d0,[a15]0
.L709:
	fcall	.cocofun_3
.L710:
	jz.t	d15:1,.L64
.L711:

; ..\eeprom\NvM\NvM.c	  1894          }
; ..\eeprom\NvM\NvM.c	  1895          /* #120 NvM_ReadAll is pending */
; ..\eeprom\NvM\NvM.c	  1896          else
; ..\eeprom\NvM\NvM.c	  1897          {
; ..\eeprom\NvM\NvM.c	  1898              /* ReadAll begins with configuration block and ends with last block -> lower block Id than current is done */
; ..\eeprom\NvM\NvM.c	  1899              blockPending = (boolean)(BlockId > NvM_CurrentJob_t.JobBlockId_t);
; ..\eeprom\NvM\NvM.c	  1900          }
; ..\eeprom\NvM\NvM.c	  1901      }
; ..\eeprom\NvM\NvM.c	  1902      /* #200 required block Id is the multi block or multi block request is not pending */
; ..\eeprom\NvM\NvM.c	  1903      else
; ..\eeprom\NvM\NvM.c	  1904      {
; ..\eeprom\NvM\NvM.c	  1905          /* #210 checks whether the given RAM management area describes a pending Block */
; ..\eeprom\NvM\NvM.c	  1906          blockPending = (boolean)(NvM_GetMngmtAreaPtr(BlockId)->NvRamErrorStatus_u8 == NVM_REQ_PENDING);
; ..\eeprom\NvM\NvM.c	  1907      }
; ..\eeprom\NvM\NvM.c	  1908  
; ..\eeprom\NvM\NvM.c	  1909      return blockPending;
; ..\eeprom\NvM\NvM.c	  1910  }
	lt.u	d2,d4,d0
	ret
.L64:
	lt.u	d2,d0,d4
	ret
.L63:
.L61:
	call	NvM_GetMngmtAreaPtr
.L470:
	ld.bu	d15,[a2]1
.L712:
	eq	d2,d15,#2
	ret
.L400:
	
__NvM_CheckBlockPending_function_end:
	.size	NvM_CheckBlockPending,__NvM_CheckBlockPending_function_end-NvM_CheckBlockPending
.L264:
	; End of function
	
	.sdecl	'.text.NvM.NvM_GetMngmtAreaPtr',code,cluster('NvM_GetMngmtAreaPtr')
	.sect	'.text.NvM.NvM_GetMngmtAreaPtr'
	.align	2
	
	.global	NvM_GetMngmtAreaPtr

; ..\eeprom\NvM\NvM.c	  1911  
; ..\eeprom\NvM\NvM.c	  1912  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM.c	  1913  *  NvM_GetMngmtAreaPtr
; ..\eeprom\NvM\NvM.c	  1914  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM.c	  1915  /*!
; ..\eeprom\NvM\NvM.c	  1916   * Internal comment removed.
; ..\eeprom\NvM\NvM.c	  1917   *
; ..\eeprom\NvM\NvM.c	  1918   *
; ..\eeprom\NvM\NvM.c	  1919   *
; ..\eeprom\NvM\NvM.c	  1920   *
; ..\eeprom\NvM\NvM.c	  1921   */
; ..\eeprom\NvM\NvM.c	  1922  NVM_LOCAL_INLINE NvM_RamMngmtPtrType NvM_GetMngmtAreaPtr(const NvM_BlockIdType BlockId)
; Function NvM_GetMngmtAreaPtr
.L130:
NvM_GetMngmtAreaPtr:	.type	func

; ..\eeprom\NvM\NvM.c	  1923  {
; ..\eeprom\NvM\NvM.c	  1924      return (BlockId == NVM_BLOCK_FROM_DCM_ID(BlockId)) ? (&NvM_BlockMngmtArea_at[BlockId]) : (&NvM_DcmBlockMngmt_t);
	insert	d15,d4,#0,#15,#17
.L717:
	jne	d15,d4,.L68
.L718:
	fcall	.cocofun_2
.L719:
	addsc.a	a2,a15,d4,#2
.L720:

; ..\eeprom\NvM\NvM.c	  1925  }
	ret
.L68:
	movh.a	a2,#@his(NvM_DcmBlockMngmt_t)
	lea	a2,[a2]@los(NvM_DcmBlockMngmt_t)
.L721:
	ret
.L405:
	
__NvM_GetMngmtAreaPtr_function_end:
	.size	NvM_GetMngmtAreaPtr,__NvM_GetMngmtAreaPtr_function_end-NvM_GetMngmtAreaPtr
.L269:
	; End of function
	
	.sdecl	'.bss.NvM.NvM_ApiFlags_u8',data,cluster('NvM_ApiFlags_u8')
	.sect	'.bss.NvM.NvM_ApiFlags_u8'
	.global	NvM_ApiFlags_u8
NvM_ApiFlags_u8:	.type	object
	.size	NvM_ApiFlags_u8,1
	.space	1
	.calls	'NvM_Init','NvM_QueueInit'
	.calls	'NvM_Init','NvM_JobProcInit'
	.calls	'NvM_SetDataIndex','NvM_CheckBlockType'
	.calls	'NvM_SetDataIndex','NvM_GetMngmtAreaPtr'
	.calls	'NvM_GetDataIndex','NvM_CheckBlockType'
	.calls	'NvM_GetDataIndex','NvM_GetMngmtAreaPtr'
	.calls	'NvM_SetBlockProtection','NvM_GetMngmtAreaPtr'
	.calls	'NvM_SetBlockProtection','NvM_EnterCriticalSection'
	.calls	'NvM_SetBlockProtection','NvM_ExitCriticalSection'
	.calls	'NvM_GetErrorStatus','NvM_CheckBlockPending'
	.calls	'NvM_GetErrorStatus','NvM_GetMngmtAreaPtr'
	.calls	'NvM_SetRamBlockStatus','NvM_CheckAddress'
	.calls	'NvM_SetRamBlockStatus','NvM_GetMngmtAreaPtr'
	.calls	'NvM_SetRamBlockStatus','NvM_EnterCriticalSection'
	.calls	'NvM_SetRamBlockStatus','NvM_ExitCriticalSection'
	.calls	'NvM_ReadBlock','NvM_QueueJob'
	.calls	'NvM_ReadBlock','NvM_GetMngmtAreaPtr'
	.calls	'NvM_ReadBlock','NvM_EnterCriticalSection'
	.calls	'NvM_ReadBlock','NvM_ExitCriticalSection'
	.calls	'NvM_WriteBlock','NvM_GetMngmtAreaPtr'
	.calls	'NvM_WriteBlock','NvM_WriteProtectionChecks'
	.calls	'NvM_WriteBlock','NvM_QueueJob'
	.calls	'NvM_WriteBlock','NvM_EnterCriticalSection'
	.calls	'NvM_WriteBlock','NvM_ExitCriticalSection'
	.calls	'NvM_RestoreBlockDefaults','NvM_QueueJob'
	.calls	'NvM_RestoreBlockDefaults','NvM_GetMngmtAreaPtr'
	.calls	'NvM_RestoreBlockDefaults','NvM_EnterCriticalSection'
	.calls	'NvM_RestoreBlockDefaults','NvM_ExitCriticalSection'
	.calls	'NvM_EraseNvBlock','NvM_GetMngmtAreaPtr'
	.calls	'NvM_EraseNvBlock','NvM_WriteProtectionChecks'
	.calls	'NvM_EraseNvBlock','NvM_QueueJob'
	.calls	'NvM_InvalidateNvBlock','NvM_GetMngmtAreaPtr'
	.calls	'NvM_InvalidateNvBlock','NvM_WriteProtectionChecks'
	.calls	'NvM_InvalidateNvBlock','NvM_QueueJob'
	.calls	'NvM_CancelJobs','NvM_UnQueueJob'
	.calls	'NvM_ReadAll','NvM_EnterCriticalSection'
	.calls	'NvM_ReadAll','NvM_ExitCriticalSection'
	.calls	'NvM_ReadAll','NvM_MultiBlockCbk'
	.calls	'NvM_KillReadAll','NvM_EnterCriticalSection'
	.calls	'NvM_KillReadAll','NvM_ExitCriticalSection'
	.calls	'NvM_WriteAll','NvM_EnterCriticalSection'
	.calls	'NvM_WriteAll','NvM_ExitCriticalSection'
	.calls	'NvM_WriteAll','NvM_MultiBlockCbk'
	.calls	'NvM_CancelWriteAll','NvM_EnterCriticalSection'
	.calls	'NvM_CancelWriteAll','NvM_ExitCriticalSection'
	.calls	'NvM_KillWriteAll','NvM_EnterCriticalSection'
	.calls	'NvM_KillWriteAll','NvM_CheckBlockId'
	.calls	'NvM_KillWriteAll','NvM_ExitCriticalSection'
	.calls	'NvM_KillWriteAll','NvM_BlockNotification'
	.calls	'NvM_KillWriteAll','NvM_MultiBlockCbk'
	.calls	'NvM_MainFunction','NvM_Fsm'
	.calls	'NvM_JobErrorNotification','MemIf_GetJobResult'
	.calls	'NvM_SetBlockLockStatus','NvM_EnterCriticalSection'
	.calls	'NvM_SetBlockLockStatus','NvM_ExitCriticalSection'
	.calls	'NvM_CheckBlockPending','NvM_GetMngmtAreaPtr'
	.calls	'NvM_Init','.cocofun_2'
	.calls	'NvM_ReadAll','.cocofun_1'
	.calls	'.cocofun_1','.cocofun_3'
	.calls	'NvM_WriteAll','.cocofun_1'
	.calls	'NvM_CancelWriteAll','.cocofun_3'
	.calls	'NvM_SetBlockLockStatus','.cocofun_2'
	.calls	'NvM_CheckBlockPending','.cocofun_3'
	.calls	'NvM_GetMngmtAreaPtr','.cocofun_2'
	.calls	'NvM_Init','',0
	.calls	'.cocofun_2','',0
	.calls	'NvM_SetDataIndex','',0
	.calls	'NvM_GetDataIndex','',0
	.calls	'NvM_SetBlockProtection','',0
	.calls	'NvM_GetErrorStatus','',0
	.calls	'NvM_SetRamBlockStatus','',0
	.calls	'NvM_ReadBlock','',0
	.calls	'NvM_WriteBlock','',0
	.calls	'NvM_RestoreBlockDefaults','',0
	.calls	'NvM_EraseNvBlock','',0
	.calls	'NvM_InvalidateNvBlock','',0
	.calls	'NvM_CancelJobs','',0
	.calls	'NvM_ReadAll','',0
	.calls	'.cocofun_1','',0
	.calls	'.cocofun_3','',0
	.calls	'NvM_KillReadAll','',0
	.calls	'NvM_WriteAll','',0
	.calls	'NvM_CancelWriteAll','',0
	.calls	'NvM_KillWriteAll','',0
	.calls	'NvM_MainFunction','',0
	.calls	'NvM_JobEndNotification','',0
	.calls	'NvM_JobErrorNotification','',0
	.calls	'NvM_SetBlockLockStatus','',0
	.calls	'NvM_WriteProtectionChecks','',0
	.calls	'NvM_CheckBlockType','',0
	.calls	'NvM_CheckAddress','',0
	.calls	'NvM_CheckBlockId','',0
	.calls	'NvM_CheckBlockPending','',0
	.extern	NvM_NoOfBlockIds_t
	.extern	MemIf_GetJobResult
	.extern	NvM_EnterCriticalSection
	.extern	NvM_ExitCriticalSection
	.extern	NvM_MultiBlockCbk
	.extern	NvM_BlockDescriptorTable_at
	.extern	NvM_BlockMngmtArea_at
	.extern	NvM_DcmBlockMngmt_t
	.extern	NvM_BlockNotification
	.extern	NvM_JobMainState_t
	.extern	NvM_JobSubState_t
	.extern	NvM_TaskState_t
	.extern	NvM_CurrentJob_t
	.extern	NvM_CurrentBlockInfo_t
	.extern	NvM_JobProcInit
	.extern	NvM_Fsm
	.extern	NvM_QueueInit
	.extern	NvM_QueueJob
	.extern	NvM_UnQueueJob
	.calls	'NvM_GetMngmtAreaPtr','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L132:
	.word	10078
	.half	3
	.word	.L133
	.byte	4
.L131:
	.byte	1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L134
.L289:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L291:
	.byte	2
	.byte	'unsigned char',0,1,8
.L293:
	.byte	2
	.byte	'unsigned short int',0,2,7,3,1,124,9,4,4
	.byte	'NvDataIndex_t',0,1
	.word	195
	.byte	2,35,0,4
	.byte	'NvRamErrorStatus_u8',0,1
	.word	195
	.byte	2,35,1,4
	.byte	'NvRamAttributes_u8',0,1
	.word	195
	.byte	2,35,2,0,5
	.word	234
.L404:
	.byte	6
	.byte	'NvM_RamMngmtPtrType',0,1,131,1,65
	.word	320
.L298:
	.byte	7
	.word	325
	.byte	5
	.word	234
.L302:
	.byte	5
	.word	195
.L306:
	.byte	7
	.word	325
.L313:
	.byte	7
	.word	325
.L317:
	.byte	5
	.word	195
.L320:
	.byte	7
	.word	325
.L327:
	.byte	7
	.word	325
	.byte	8
	.byte	'void',0
.L331:
	.byte	5
	.word	394
.L335:
	.byte	7
	.word	325
	.byte	7
	.word	394
.L339:
	.byte	5
	.word	410
.L343:
	.byte	7
	.word	325
.L350:
	.byte	7
	.word	325
.L356:
	.byte	7
	.word	325
.L362:
	.byte	7
	.word	325
.L375:
	.byte	7
	.word	325
	.byte	9,3,72,9,1,10
	.byte	'MEMIF_JOB_OK',0,0,10
	.byte	'MEMIF_JOB_FAILED',0,1,10
	.byte	'MEMIF_JOB_PENDING',0,2,10
	.byte	'MEMIF_JOB_CANCELED',0,3,10
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,10
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,11
	.byte	'MemIf_GetJobResult',0,2,249,1,39
	.word	445
	.byte	1,1,1,1,12
	.byte	'DeviceIndex',0,2,249,1,64
	.word	195
	.byte	0,13
	.byte	'NvM_EnterCriticalSection',0,1,192,2,37,1,1,1,1,13
	.byte	'NvM_ExitCriticalSection',0,1,204,2,37,1,1,1,1,14
	.byte	'NvM_MultiBlockCbk',0,1,218,2,37,1,1,1,1,12
	.byte	'ServiceId',0,1,218,2,73
	.word	195
	.byte	12
	.byte	'JobResult',0,1,218,2,106
	.word	195
	.byte	0,14
	.byte	'NvM_BlockNotification',0,4,192,1,37,1,1,1,1,12
	.byte	'BlockId',0,4,192,1,75
	.word	212
	.byte	12
	.byte	'ServiceId',0,4,192,1,102
	.word	195
	.byte	12
	.byte	'JobResult',0,4,192,1,135,1
	.word	195
	.byte	0,13
	.byte	'NvM_JobProcInit',0,5,196,2,37,1,1,1,1,9,5,96,9,1,10
	.byte	'NVM_STATE_UNINIT',0,0,10
	.byte	'NVM_STATE_IDLE',0,1,10
	.byte	'NVM_STATE_NORMAL_PRIO_JOB',0,2,10
	.byte	'NVM_STATE_MULTI_BLOCK_JOB',0,3,10
	.byte	'NVM_STATE_READ_READ_DATA',0,4,10
	.byte	'NVM_STATE_READ_DATA_VALIDATION',0,5,10
	.byte	'NVM_STATE_READ_CMP_CRC',0,6,10
	.byte	'NVM_STATE_READ_IMPL_RECOV',0,7,10
	.byte	'NVM_STATE_READ_LOAD_ROM',0,8,10
	.byte	'NVM_STATE_READ_FINALIZE',0,9,10
	.byte	'NVM_STATE_WRITE_INITIAL',0,10,10
	.byte	'NVM_STATE_WRITE_CRCCALC',0,11,10
	.byte	'NVM_STATE_WRITE_CRCCOMPMECHANISM',0,12,10
	.byte	'NVM_STATE_WRITE_TEST_PRI_READ',0,13,10
	.byte	'NVM_STATE_WRITE_TEST_SEC_READ',0,14,10
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_1',0,15,10
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_2',0,16,10
	.byte	'NVM_STATE_RESTORE_LOAD_ROM',0,17,10
	.byte	'NVM_STATE_INVALIDATING_BLOCK',0,18,10
	.byte	'NVM_STATE_ERASE_ERASE_BLOCK',0,19,10
	.byte	'NVM_STATE_READALL_PROC_CONFIG_ID',0,20,10
	.byte	'NVM_STATE_READALL_PROC_RAM_BLOCK',0,21,10
	.byte	'NVM_STATE_READALL_CHK_SKIP',0,22,10
	.byte	'NVM_STATE_READALL_KILLED',0,23,10
	.byte	'NVM_STATE_READALL_WR_ONCE_PROT',0,24,10
	.byte	'NVM_STATE_READALL_CHK_RAM_VALIDITY',0,25,10
	.byte	'NVM_STATE_READALL_READ_NV',0,26,10
	.byte	'NVM_STATE_READALL_LOAD_DEFAULTS',0,27,10
	.byte	'NVM_STATE_READALL_READABILITY_CHECK',0,28,10
	.byte	'NVM_STATE_WRITEALL_PROC_BLOCK',0,29,10
	.byte	'NVM_STATE_WRITEALL_WRITE_FSM',0,30,10
	.byte	'NVM_STATE_WRITEALL_WAIT_MEMHWA',0,31,10
	.byte	'NVM_STATE_FSM_FINISHED',0,32,0,11
	.byte	'NvM_Fsm',0,5,215,2,48
	.word	875
	.byte	1,1,1,1,12
	.byte	'NvM_CurrentState_t',0,5,215,2,72
	.word	875
	.byte	0,13
	.byte	'NvM_QueueInit',0,6,68,37,1,1,1,1,11
	.byte	'NvM_QueueJob',0,6,88,40
	.word	195
	.byte	1,1,1,1,12
	.byte	'BlockId',0,6,88,69
	.word	212
	.byte	9,1,243,1,9,1,10
	.byte	'NVM_INT_FID_WRITE_BLOCK',0,0,10
	.byte	'NVM_INT_FID_READ_BLOCK',0,1,10
	.byte	'NVM_INT_FID_RESTORE_DEFAULTS',0,2,10
	.byte	'NVM_INT_FID_INVALIDATE_NV_BLOCK',0,3,10
	.byte	'NVM_INT_FID_ERASE_BLOCK',0,4,10
	.byte	'NVM_INT_FID_WRITE_ALL',0,5,10
	.byte	'NVM_INT_FID_READ_ALL',0,6,10
	.byte	'NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS',0,7,10
	.byte	'NVM_INT_FID_NO_JOB_PENDING',0,8,0,12
	.byte	'ServiceId',0,6,88,104
	.word	1973
	.byte	6
	.byte	'NvM_RamAddressType',0,1,161,1,48
	.word	364
	.byte	12
	.byte	'RamAddress',0,6,88,134,1
	.word	2254
	.byte	0,5
	.word	195
	.byte	11
	.byte	'NvM_UnQueueJob',0,6,105,40
	.word	195
	.byte	1,1,1,1,12
	.byte	'BlockId',0,6,105,71
	.word	212
	.byte	0,7
	.word	234
	.byte	5
	.word	2352
	.byte	6
	.byte	'NvM_RamMngmtConstPtrType',0,7,90,67
	.word	2357
.L384:
	.byte	7
	.word	2362
	.byte	7
	.word	234
	.byte	5
	.word	2400
.L388:
	.byte	7
	.word	212
.L391:
	.byte	7
	.word	212
	.byte	3,1,196,1,9,64,4
	.byte	'RamBlockDataAddr_t',0,4
	.word	2254
	.byte	2,35,0,7
	.word	195
	.byte	5
	.word	2454
	.byte	6
	.byte	'NvM_RomAddressType',0,1,164,1,50
	.word	2459
	.byte	4
	.byte	'RomBlockDataAddr_pt',0,4
	.word	2464
	.byte	2,35,4,15
	.word	195
	.byte	1,1,5
	.word	2521
	.byte	6
	.byte	'NvM_InitCbkPtrType',0,1,142,1,9
	.word	2528
	.byte	4
	.byte	'InitCbkFunc_pt',0,4
	.word	2533
	.byte	2,35,8,16
	.word	195
	.byte	1,1,17
	.word	212
	.byte	17
	.word	400
	.byte	17
	.word	212
	.byte	0,5
	.word	2585
	.byte	6
	.byte	'NvM_InitCbkExtPtrType',0,1,145,1,9
	.word	2608
	.byte	4
	.byte	'InitCbkExtFunc_pt',0,4
	.word	2613
	.byte	2,35,12,16
	.word	195
	.byte	1,1,17
	.word	195
	.byte	17
	.word	195
	.byte	0,5
	.word	2671
	.byte	6
	.byte	'NvM_JobEndCbkPtrType',0,1,134,1,9
	.word	2689
	.byte	4
	.byte	'JobEndCbkFunc_pt',0,4
	.word	2694
	.byte	2,35,16,16
	.word	195
	.byte	1,1,17
	.word	212
	.byte	17
	.word	195
	.byte	17
	.word	195
	.byte	0,5
	.word	2750
	.byte	6
	.byte	'NvM_JobEndCbkExtPtrType',0,1,137,1,9
	.word	2773
	.byte	4
	.byte	'JobEndCbkExtFunc_pt',0,4
	.word	2778
	.byte	2,35,20,16
	.word	195
	.byte	1,1,17
	.word	415
	.byte	0,5
	.word	2840
	.byte	6
	.byte	'NvM_ReadRamFromNvMCbkPtrType',0,1,149,1,9
	.word	2853
	.byte	4
	.byte	'CbkGetMirrorFunc_pt',0,4
	.word	2858
	.byte	2,35,24,16
	.word	195
	.byte	1,1,17
	.word	400
	.byte	0,5
	.word	2925
	.byte	6
	.byte	'NvM_WriteRamToNvMCbkPtrType',0,1,148,1,9
	.word	2938
	.byte	4
	.byte	'CbkSetMirrorFunc_pt',0,4
	.word	2943
	.byte	2,35,28,18,1,1,17
	.word	212
	.byte	17
	.word	400
	.byte	17
	.word	212
	.byte	0,5
	.word	3009
	.byte	6
	.byte	'NvM_PreWriteTransformCbkPtrType',0,1,152,1,9
	.word	3028
	.byte	4
	.byte	'CbkPreWriteTransform',0,4
	.word	3033
	.byte	2,35,32,6
	.byte	'NvM_PostReadTransformCbkPtrType',0,1,153,1,9
	.word	2608
	.byte	4
	.byte	'CbkPostReadTransform',0,4
	.word	3104
	.byte	2,35,36,6
	.byte	'NvM_RamCrcAddressType',0,1,170,1,51
	.word	364
	.byte	4
	.byte	'RamBlockCrcAddr_t',0,4
	.word	3175
	.byte	2,35,40,4
	.byte	'CRCCompMechanismCrcAddr_t',0,4
	.word	3175
	.byte	2,35,44,4
	.byte	'NvIdentifier_u16',0,2
	.word	212
	.byte	2,35,48,4
	.byte	'NvBlockLength_u16',0,2
	.word	212
	.byte	2,35,50,4
	.byte	'NvCryptoReference',0,1
	.word	195
	.byte	2,35,52,4
	.byte	'NvBlockNVRAMDataLength',0,2
	.word	212
	.byte	2,35,54,19
	.byte	'NvBlockCount_u8',0,1
	.word	195
	.byte	8,0,2,35,56,19
	.byte	'BlockPrio_u8',0,1
	.word	195
	.byte	8,0,2,35,57,19
	.byte	'DeviceId_u8',0,1
	.word	195
	.byte	4,4,2,35,58,19
	.byte	'MngmtType_t',0,1
	.word	195
	.byte	2,2,2,35,58,19
	.byte	'CrcSettings',0,1
	.word	195
	.byte	2,0,2,35,58,19
	.byte	'Flags_u8',0,1
	.word	195
	.byte	8,0,2,35,59,19
	.byte	'NotifyBswM',0,1
	.word	195
	.byte	1,7,2,35,60,0,7
	.word	2420
	.byte	5
	.word	3543
	.byte	7
	.word	195
	.byte	5
	.word	3553
	.byte	5
	.word	2521
	.byte	5
	.word	2585
	.byte	5
	.word	2671
	.byte	5
	.word	2750
	.byte	5
	.word	2840
	.byte	5
	.word	2925
	.byte	5
	.word	3009
	.byte	5
	.word	2585
	.byte	5
	.word	195
	.byte	7
	.word	2420
	.byte	5
	.word	3608
.L394:
	.byte	6
	.byte	'NvM_BlockDescrPtrType',0,1,240,1,71
	.word	3613
.L398:
	.byte	7
	.word	212
.L401:
	.byte	7
	.word	212
.L406:
	.byte	7
	.word	212
	.byte	6
	.byte	'__prof_adm',0,7,1,1
	.word	400
	.byte	20,1,5
	.word	3683
	.byte	6
	.byte	'__codeptr',0,7,1,1
	.word	3685
	.byte	6
	.byte	'uint8',0,8,90,29
	.word	195
	.byte	2
	.byte	'short int',0,2,5,6
	.byte	'sint16',0,8,91,29
	.word	3722
	.byte	6
	.byte	'uint16',0,8,92,29
	.word	212
	.byte	6
	.byte	'uint32',0,8,94,29
	.word	174
	.byte	6
	.byte	'boolean',0,8,105,29
	.word	195
	.byte	2
	.byte	'unsigned long long int',0,8,7,6
	.byte	'uint64',0,8,130,1,30
	.word	3796
	.byte	6
	.byte	'Std_ReturnType',0,9,113,15
	.word	195
	.byte	6
	.byte	'PduLengthType',0,10,76,22
	.word	212
	.byte	6
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,11,112,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,11,115,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,11,118,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,11,121,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,11,124,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,11,136,1,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,11,139,1,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,11,148,1,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,11,151,1,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,11,141,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,11,144,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,11,147,3,16
	.word	212
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,11,150,3,16
	.word	212
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,11,153,3,16
	.word	212
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,11,156,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,11,159,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,11,162,3,16
	.word	212
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,11,165,3,16
	.word	212
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,11,180,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,11,183,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,11,186,3,16
	.word	174
	.byte	6
	.byte	'IdtAppCom_MainPwrFltRsn',0,11,192,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGMDiags',0,11,195,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGMFltRsn',0,11,198,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGMSts',0,11,201,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGMSwSts',0,11,207,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,11,210,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,11,213,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,11,216,3,16
	.word	212
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,11,219,3,16
	.word	212
	.byte	6
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,11,225,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,11,228,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_RednPwrFltRsn',0,11,231,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,11,234,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_SHWAIndSts',0,11,237,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_SHWASysFltSts',0,11,240,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_SHWASysMsg',0,11,243,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,11,246,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_SHWASysSts',0,11,249,3,15
	.word	195
	.byte	6
	.byte	'IdtAppCom_SHWASysTakeOver',0,11,252,3,15
	.word	195
	.byte	6
	.byte	'NvM_BlockIdType',0,11,227,5,16
	.word	212
	.byte	6
	.byte	'NvM_RequestResultType',0,11,207,6,15
	.word	195
	.byte	6
	.byte	'NvM_ServiceIdType',0,11,231,6,15
	.word	195
	.byte	2
	.byte	'unsigned int',0,4,7,6
	.byte	'Rte_BitType',0,11,230,7,22
	.word	5523
	.byte	7
	.word	212
	.byte	21
	.byte	'NvM_NoOfBlockIds_t',0,12,246,1,40
	.word	5560
	.byte	1,1,9,3,59,9,1,10
	.byte	'MEMIF_UNINIT',0,0,10
	.byte	'MEMIF_IDLE',0,1,10
	.byte	'MEMIF_BUSY',0,2,10
	.byte	'MEMIF_BUSY_INTERNAL',0,3,0,6
	.byte	'MemIf_StatusType',0,3,65,3
	.word	5595
	.byte	6
	.byte	'MemIf_JobResultType',0,3,80,3
	.word	445
	.byte	16
	.word	195
	.byte	1,1,17
	.word	212
	.byte	17
	.word	212
	.byte	5
	.word	195
	.byte	17
	.word	5734
	.byte	17
	.word	212
	.byte	0,5
	.word	5717
	.byte	6
	.byte	'MemIf_ApiReadType',0,13,120,9
	.word	5750
	.byte	16
	.word	195
	.byte	1,1,17
	.word	212
	.byte	17
	.word	5734
	.byte	0,5
	.word	5781
	.byte	6
	.byte	'MemIf_ApiWriteType',0,13,121,9
	.word	5799
	.byte	16
	.word	195
	.byte	1,1,17
	.word	212
	.byte	0,5
	.word	5831
	.byte	6
	.byte	'MemIf_ApiEraseImmediateBlockType',0,13,122,9
	.word	5844
	.byte	6
	.byte	'MemIf_ApiInvalidateBlockType',0,13,123,9
	.word	5844
	.byte	22,1,1,5
	.word	5927
	.byte	6
	.byte	'MemIf_ApiCancelType',0,13,124,9
	.word	5930
	.byte	15
	.word	5595
	.byte	1,1,5
	.word	5963
	.byte	6
	.byte	'MemIf_ApiGetStatusType',0,13,125,9
	.word	5970
	.byte	15
	.word	445
	.byte	1,1,5
	.word	6006
	.byte	6
	.byte	'MemIf_ApiGetJobResultType',0,13,126,9
	.word	6013
	.byte	18,1,1,9,3,88,9,1,10
	.byte	'MEMIF_MODE_SLOW',0,0,10
	.byte	'MEMIF_MODE_FAST',0,1,0,17
	.word	6055
	.byte	0,5
	.word	6052
	.byte	6
	.byte	'MemIf_ApiSetModeType',0,13,127,9
	.word	6103
	.byte	6
	.byte	'NvM_BitFieldType',0,1,113,22
	.word	5523
	.byte	6
	.byte	'NvM_CrcType',0,1,116,26
	.word	5523
	.byte	6
	.byte	'NvM_RamMngmtAreaType',0,1,129,1,3
	.word	234
	.byte	6
	.byte	'NvM_ConstRamAddressType',0,1,162,1,50
	.word	2459
	.byte	6
	.byte	'NvM_BlockDescriptorType',0,1,223,1,3
	.word	2420
	.byte	6
	.byte	'NvM_CsmJobIdType',0,1,231,1,16
	.word	174
	.byte	6
	.byte	'NvM_InternalServiceIdType',0,1,254,1,3
	.word	1973
	.byte	6
	.byte	'NvM_QueueEntryRefType',0,1,129,2,15
	.word	195
	.byte	23
	.word	2420
	.byte	24,0,7
	.word	6370
	.byte	21
	.byte	'NvM_BlockDescriptorTable_at',0,1,151,3,57
	.word	6377
	.byte	1,1,23
	.word	234
	.byte	24,0,21
	.byte	'NvM_BlockMngmtArea_at',0,1,160,3,51
	.word	6421
	.byte	1,1,21
	.byte	'NvM_DcmBlockMngmt_t',0,1,163,3,51
	.word	234
	.byte	1,1,9,4,42,9,1,10
	.byte	'NVM_ACT_ID_SetInitialAttr',0,0,10
	.byte	'NVM_ACT_ID_InitMainFsm',0,1,10
	.byte	'NVM_ACT_ID_InitBlock',0,2,10
	.byte	'NVM_ACT_ID_InitReadAll',0,3,10
	.byte	'NVM_ACT_ID_InitReadBlockSubFsm',0,4,10
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaultsSubFsm',0,5,10
	.byte	'NVM_ACT_ID_InitWriteAll',0,6,10
	.byte	'NVM_ACT_ID_InitWriteBlock',0,7,10
	.byte	'NVM_ACT_ID_InitWriteBlockFsm',0,8,10
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaults',0,9,10
	.byte	'NVM_ACT_ID_FinishMainJob',0,10,10
	.byte	'NVM_ACT_ID_KillWritAll',0,11,10
	.byte	'NVM_ACT_ID_FinishBlock',0,12,10
	.byte	'NVM_ACT_ID_InitNextBlockReadAll',0,13,10
	.byte	'NVM_ACT_ID_InitNextBlockWriteAll',0,14,10
	.byte	'NVM_ACT_ID_FinishCfgIdCheck',0,15,10
	.byte	'NVM_ACT_ID_FinishReadBlock',0,16,10
	.byte	'NVM_ACT_ID_FinishWriteBlock',0,17,10
	.byte	'NVM_ACT_ID_FinishEraseBlock',0,18,10
	.byte	'NVM_ACT_ID_EraseNvBlock',0,19,10
	.byte	'NVM_ACT_ID_InvalidateNvBlock',0,20,10
	.byte	'NVM_ACT_ID_ProcessCrc',0,21,10
	.byte	'NVM_ACT_ID_WriteNvBlock',0,22,10
	.byte	'NVM_ACT_ID_ReadNvBlock',0,23,10
	.byte	'NVM_ACT_ID_ProcessCrcRead',0,24,10
	.byte	'NVM_ACT_ID_ReadCopyData',0,25,10
	.byte	'NVM_ACT_ID_RestoreRomDefaults',0,26,10
	.byte	'NVM_ACT_ID_FinishRestoreRomDefaults',0,27,10
	.byte	'NVM_ACT_ID_TestBlockBlank',0,28,10
	.byte	'NVM_ACT_ID_ValidateRam',0,29,10
	.byte	'NVM_ACT_ID_SetupRedundant',0,30,10
	.byte	'NVM_ACT_ID_SetupOther',0,31,10
	.byte	'NVM_ACT_ID_UpdateNvState',0,32,10
	.byte	'NVM_ACT_ID_SetReqIntegrityFailed',0,33,10
	.byte	'NVM_ACT_ID_SetReqSkipped',0,34,10
	.byte	'NVM_ACT_ID_SetReqNotOk',0,35,10
	.byte	'NVM_ACT_ID_SetReqOk',0,36,10
	.byte	'NVM_ACT_ID_SetBlockPendingWriteAll',0,37,10
	.byte	'NVM_ACT_ID_CopyNvDataToBuf',0,38,10
	.byte	'NVM_ACT_ID_GetMultiBlockJob',0,39,10
	.byte	'NVM_ACT_ID_CancelNV',0,40,10
	.byte	'NVM_ACT_ID_KillSubFsm',0,41,10
	.byte	'NVM_ACT_ID_FinishReadBlockAndSetSkipped',0,42,10
	.byte	'NVM_ACT_ID_GetNormalPrioJob',0,43,10
	.byte	'NVM_ACT_ID_Wait',0,44,10
	.byte	'NVM_ACT_ID_Nop',0,45,0,6
	.byte	'NvM_StateActionIdType',0,4,110,3
	.word	6492
	.byte	9,14,48,9,1,10
	.byte	'NVM_QRY_ID_BLK_WRITE_ALL',0,0,10
	.byte	'NVM_QRY_ID_CANCEL_WRITE_ALL',0,1,10
	.byte	'NVM_QR_ID_WRITEALL_KILLED',0,2,10
	.byte	'NVM_QRY_ID_CRC_BUSY',0,3,10
	.byte	'NVM_QRY_ID_DATA_COPY_BUSY',0,4,10
	.byte	'NVM_QRY_ID_CRC_MATCH',0,5,10
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_READALL',0,6,10
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_WRITEALL',0,7,10
	.byte	'NVM_QRY_ID_LAST_RESULT_OK',0,8,10
	.byte	'NVM_QRY_ID_MAIN_FSM_RUNNING',0,9,10
	.byte	'NVM_QRY_ID_MULTI_BLK_JOB',0,10,10
	.byte	'NVM_QRY_ID_NORMAL_PRIO_JOB',0,11,10
	.byte	'NVM_QRY_ID_NV_BUSY',0,12,10
	.byte	'NVM_QRY_ID_MEMHWA_BUSY',0,13,10
	.byte	'NVM_QRY_ID_RAM_VALID',0,14,10
	.byte	'NVM_QRY_ID_REDUNDANT_BLOCK',0,15,10
	.byte	'NVM_QRY_ID_SKIP_BLOCK',0,16,10
	.byte	'NVM_QRY_ID_SUB_FSM_RUNNING',0,17,10
	.byte	'NVM_QRY_ID_WRITE_BLOCK_ONCE',0,18,10
	.byte	'NVM_QRY_ID_WRITE_RETRIES_EXCEEDED',0,19,10
	.byte	'NVM_QRY_ID_HAS_ROM',0,20,10
	.byte	'NVM_QRY_ID_EXT_RUNTIME',0,21,10
	.byte	'NvM_QRY_CRC_COMP_MECHANISM_SKIPWRITE',0,22,10
	.byte	'NVM_QRY_POST_READ_TRANSFORM',0,23,10
	.byte	'NVM_QRY_READALL_KILLED',0,24,10
	.byte	'NVM_QRY_SYNCDECRYPT',0,25,10
	.byte	'NVM_QRY_SYNCENCRYPT',0,26,10
	.byte	'NVM_QRY_CSM_RETRIES_NECESSARY',0,27,10
	.byte	'NVM_QRY_ID_TRUE',0,28,0,6
	.byte	'NvM_StateQueryIdType',0,14,93,3
	.word	7840
	.byte	6
	.byte	'NvM_CrcBufferPtrType',0,15,68,59
	.word	364
	.byte	18,1,1,7
	.word	195
	.byte	5
	.word	8705
	.byte	17
	.word	8710
	.byte	17
	.word	212
	.byte	5
	.word	174
	.byte	17
	.word	8725
	.byte	0,5
	.word	8702
	.byte	6
	.byte	'NvM_CrcCalculateFPtr',0,15,74,9
	.word	8736
	.byte	16
	.word	195
	.byte	1,1,17
	.word	8710
	.byte	17
	.word	8710
	.byte	0,5
	.word	8770
	.byte	6
	.byte	'NvM_CrcCompareFPtr',0,15,75,9
	.word	8788
	.byte	18,1,1,17
	.word	5734
	.byte	17
	.word	8710
	.byte	0,5
	.word	8820
	.byte	6
	.byte	'NvM_CrcCopyToBufferFPtr',0,15,76,9
	.word	8834
	.byte	25
	.byte	'NvM_CrcHandlerClass',0,15,79,8,20,4
	.byte	'calc',0,4
	.word	8741
	.byte	2,35,0,4
	.byte	'compare',0,4
	.word	8793
	.byte	2,35,4,4
	.byte	'copyToBuffer',0,4
	.word	8839
	.byte	2,35,8,4
	.byte	'initialCrcValue',0,4
	.word	174
	.byte	2,35,12,4
	.byte	'crcLength',0,1
	.word	195
	.byte	2,35,16,0,7
	.word	8871
	.byte	5
	.word	8994
	.byte	6
	.byte	'NvM_CrcHandlerClassConstPtr',0,15,88,75
	.word	8999
	.byte	5
	.word	8702
	.byte	5
	.word	8770
	.byte	5
	.word	8820
	.byte	25
	.byte	'NvM_CrcJobStruct',0,15,91,16,20,4
	.byte	'CurrentCrcValue',0,4
	.word	174
	.byte	2,35,0,4
	.byte	'RamData_pt',0,4
	.word	6212
	.byte	2,35,4,4
	.byte	'CrcBuffer',0,4
	.word	8673
	.byte	2,35,8,4
	.byte	'HandlerInstance_pt',0,4
	.word	9004
	.byte	2,35,12,4
	.byte	'RemainingLength_u16',0,2
	.word	212
	.byte	2,35,16,0,6
	.byte	'NvM_CrcJobType',0,15,98,3
	.word	9055
	.byte	7
	.word	195
	.byte	5
	.word	9222
	.byte	5
	.word	195
	.byte	7
	.word	8871
	.byte	5
	.word	9237
	.byte	3,5,57,9,8,4
	.byte	'JobBlockId_t',0,2
	.word	212
	.byte	2,35,0,4
	.byte	'JobServiceId_t',0,1
	.word	1973
	.byte	2,35,2,4
	.byte	'RamAddr_t',0,4
	.word	2254
	.byte	2,35,4,0,6
	.byte	'NvM_JobType',0,5,62,3
	.word	9247
	.byte	3,5,66,9,44,4
	.byte	'Descriptor_pt',0,4
	.word	3618
	.byte	2,35,0,4
	.byte	'Mngmt_pt',0,4
	.word	325
	.byte	2,35,4,4
	.byte	'RamAddr_t',0,4
	.word	2254
	.byte	2,35,8,4
	.byte	'NvRamAddr_t',0,4
	.word	2254
	.byte	2,35,12,4
	.byte	'BlockCrcJob_t',0,20
	.word	9055
	.byte	2,35,16,4
	.byte	'NvIdentifier_u16',0,2
	.word	212
	.byte	2,35,36,4
	.byte	'ByteCount_u16',0,2
	.word	212
	.byte	2,35,38,4
	.byte	'LastResult_t',0,1
	.word	195
	.byte	2,35,40,4
	.byte	'WriteRetryCounter_u8',0,1
	.word	195
	.byte	2,35,41,4
	.byte	'InternalFlags_u8',0,1
	.word	195
	.byte	2,35,42,4
	.byte	'NvState_u8',0,1
	.word	195
	.byte	2,35,43,0,6
	.byte	'NvM_BlockInfoType',0,5,83,3
	.word	9338
	.byte	6
	.byte	'NvM_StateIdType',0,5,215,1,3
	.word	875
	.byte	3,5,228,1,9,2,4
	.byte	'ExitHandler_t',0,1
	.word	6492
	.byte	2,35,0,4
	.byte	'EntryHandler_t',0,1
	.word	6492
	.byte	2,35,1,0,6
	.byte	'NvM_StateChangeActionsType',0,5,232,1,3
	.word	9646
	.byte	3,5,236,1,9,6,26,2
	.word	7840
	.byte	27,1,0,4
	.byte	'Queries_at',0,2
	.word	9742
	.byte	2,35,0,4
	.byte	'Actions_t',0,2
	.word	9646
	.byte	2,35,2,4
	.byte	'NextState_t',0,1
	.word	875
	.byte	2,35,4,0,6
	.byte	'NvM_StateChangeIfDescrType',0,5,241,1,3
	.word	9736
	.byte	3,5,243,1,9,4,4
	.byte	'Actions_t',0,2
	.word	9646
	.byte	2,35,0,4
	.byte	'NextState_t',0,1
	.word	875
	.byte	2,35,2,0,6
	.byte	'NvM_StateChangeElseDescrType',0,5,247,1,3
	.word	9848
	.byte	21
	.byte	'NvM_JobMainState_t',0,5,137,2,44
	.word	875
	.byte	1,1,21
	.byte	'NvM_JobSubState_t',0,5,138,2,44
	.word	875
	.byte	1,1,21
	.byte	'NvM_TaskState_t',0,5,152,2,47
	.word	875
	.byte	1,1,21
	.byte	'NvM_CurrentJob_t',0,5,160,2,43
	.word	9247
	.byte	1,1,21
	.byte	'NvM_CurrentBlockInfo_t',0,5,161,2,49
	.word	9338
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L133:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,11,15,73,19,56,9,0,0,5,15,0,73,19,0,0,6,22,0,3,8,58,15,59,15,57,15,73,19,0,0,7,38,0,73
	.byte	19,0,0,8,59,0,3,8,0,0,9,4,1,58,15,59,15,57,15,11,15,0,0,10,40,0,3,8,28,13,0,0,11,46,1,3,8,58,15,59,15
	.byte	57,15,73,19,54,15,39,12,63,12,60,12,0,0,12,5,0,3,8,58,15,59,15,57,15,73,19,0,0,13,46,0,3,8,58,15,59,15
	.byte	57,15,54,15,39,12,63,12,60,12,0,0,14,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,15,21,0,73
	.byte	19,54,15,39,12,0,0,16,21,1,73,19,54,15,39,12,0,0,17,5,0,73,19,0,0,18,21,1,54,15,39,12,0,0,19,13,0,3,8
	.byte	11,15,73,19,13,15,12,15,56,9,0,0,20,21,0,54,15,0,0,21,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0
	.byte	22,21,0,54,15,39,12,0,0,23,1,1,73,19,0,0,24,33,0,0,0,25,19,1,3,8,58,15,59,15,57,15,11,15,0,0,26,1,1,11
	.byte	15,73,19,0,0,27,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L134:
	.word	.L472-.L471
.L471:
	.half	3
	.word	.L474-.L473
.L473:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	0
	.byte	'_PrivateCfg.h',0,1,0,0
	.byte	'MemIf.h',0,2,0,0
	.byte	'MemIf_Types.h',0,2,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0
	.byte	'Platform_Types.h',0,3,0,0
	.byte	'Std_Types.h',0,3,0,0
	.byte	'ComStack_Types.h',0,3,0,0
	.byte	'Rte_Type.h',0,4,0,0
	.byte	'_Cfg.h',0,1,0,0
	.byte	'_Cfg.h',0,2,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.h',0,0,0,0,0
.L474:
.L472:
	.sdecl	'.debug_info',debug,cluster('NvM_Init')
	.sect	'.debug_info'
.L135:
	.word	251
	.half	3
	.word	.L136
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L138,.L137
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_Init',0,1,188,2,29,1,1,1
	.word	.L72,.L287,.L71
	.byte	4
	.word	.L288
	.byte	5
	.byte	'NvM_BlockCount_u16loc',0,1,190,2,18
	.word	.L289,.L290
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Init')
	.sect	'.debug_abbrev'
.L136:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_Init')
	.sect	'.debug_line'
.L137:
	.word	.L476-.L475
.L475:
	.half	3
	.word	.L478-.L477
.L477:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L478:
	.byte	5,18,7,0,5,2
	.word	.L72
	.byte	3,192,2,1,5,23,9
	.half	.L479-.L72
	.byte	3,10,1,5,5,1,5,21,9
	.half	.L480-.L479
	.byte	1,5,5,9
	.half	.L481-.L480
	.byte	3,1,1,5,51,9
	.half	.L482-.L481
	.byte	1,5,29,9
	.half	.L483-.L482
	.byte	3,3,1,5,30,9
	.half	.L408-.L483
	.byte	3,4,1,5,9,9
	.half	.L2-.L408
	.byte	3,127,1,5,68,3,1,1,5,40,9
	.half	.L484-.L2
	.byte	3,1,1,5,5,7,9
	.half	.L485-.L484
	.byte	3,3,1,5,39,9
	.half	.L486-.L485
	.byte	1,5,44,9
	.half	.L487-.L486
	.byte	3,1,1,5,45,9
	.half	.L488-.L487
	.byte	3,1,1,5,20,9
	.half	.L489-.L488
	.byte	3,2,1,5,1,7,9
	.half	.L139-.L489
	.byte	3,1,0,1,1
.L476:
	.sdecl	'.debug_ranges',debug,cluster('NvM_Init')
	.sect	'.debug_ranges'
.L138:
	.word	-1,.L72,0,.L139-.L72,0,0
.L288:
	.word	-1,.L72,0,.L287-.L72,-1,.L74,0,.L279-.L74,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_SetDataIndex')
	.sect	'.debug_info'
.L140:
	.word	342
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L143,.L142
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_SetDataIndex',0,1,239,2,39
	.word	.L291
	.byte	1,1,1
	.word	.L76,.L292,.L75
	.byte	4
	.byte	'BlockId',0,1,239,2,72
	.word	.L293,.L294
	.byte	4
	.byte	'DataIndex',0,1,239,2,87
	.word	.L291,.L295
	.byte	5
	.word	.L76,.L292
	.byte	6
	.byte	'returnValue',0,1,241,2,20
	.word	.L291,.L296
	.byte	5
	.word	.L297,.L3
	.byte	6
	.byte	'NvM_RamMngt_ptloc',0,1,138,3,39
	.word	.L298,.L299
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_SetDataIndex')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_SetDataIndex')
	.sect	'.debug_line'
.L142:
	.word	.L491-.L490
.L490:
	.half	3
	.word	.L493-.L492
.L492:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L493:
	.byte	5,39,7,0,5,2
	.word	.L76
	.byte	3,238,2,1,5,32,9
	.half	.L411-.L76
	.byte	3,2,1,5,31,3,23,1,5,9,9
	.half	.L409-.L411
	.byte	1,5,79,7,9
	.half	.L297-.L409
	.byte	3,2,1,5,46,9
	.half	.L412-.L297
	.byte	3,3,1,5,25,9
	.half	.L494-.L412
	.byte	3,2,1,5,5,9
	.half	.L3-.L494
	.byte	3,19,1,5,1,3,1,1,7,9
	.half	.L144-.L3
	.byte	0,1,1
.L491:
	.sdecl	'.debug_ranges',debug,cluster('NvM_SetDataIndex')
	.sect	'.debug_ranges'
.L143:
	.word	-1,.L76,0,.L144-.L76,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_GetDataIndex')
	.sect	'.debug_info'
.L145:
	.word	345
	.half	3
	.word	.L146
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L148,.L147
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_GetDataIndex',0,1,181,3,39
	.word	.L291
	.byte	1,1,1
	.word	.L78,.L300,.L77
	.byte	4
	.byte	'BlockId',0,1,181,3,72
	.word	.L293,.L301
	.byte	4
	.byte	'DataIndexPtr',0,1,181,3,120
	.word	.L302,.L303
	.byte	5
	.word	.L78,.L300
	.byte	6
	.byte	'returnValue',0,1,183,3,20
	.word	.L291,.L304
	.byte	5
	.word	.L305,.L5
	.byte	6
	.byte	'NvM_RamMngt_ptloc',0,1,209,3,39
	.word	.L306,.L307
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_GetDataIndex')
	.sect	'.debug_abbrev'
.L146:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_GetDataIndex')
	.sect	'.debug_line'
.L147:
	.word	.L496-.L495
.L495:
	.half	3
	.word	.L498-.L497
.L497:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L498:
	.byte	5,39,7,0,5,2
	.word	.L78
	.byte	3,180,3,1,5,32,9
	.half	.L414-.L78
	.byte	3,2,1,5,31,3,24,1,5,9,9
	.half	.L413-.L414
	.byte	1,5,79,7,9
	.half	.L305-.L413
	.byte	3,2,1,5,46,9
	.half	.L416-.L305
	.byte	3,2,1,5,25,9
	.half	.L415-.L416
	.byte	3,2,1,5,57,3,124,1,5,29,9
	.half	.L5-.L415
	.byte	3,8,1,5,27,9
	.half	.L6-.L5
	.byte	1,5,5,9
	.half	.L499-.L6
	.byte	3,17,1,5,1,3,1,1,7,9
	.half	.L149-.L499
	.byte	0,1,1
.L496:
	.sdecl	'.debug_ranges',debug,cluster('NvM_GetDataIndex')
	.sect	'.debug_ranges'
.L148:
	.word	-1,.L78,0,.L149-.L78,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_SetBlockProtection')
	.sect	'.debug_info'
.L150:
	.word	357
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L153,.L152
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_SetBlockProtection',0,1,128,4,39
	.word	.L291
	.byte	1,1,1
	.word	.L80,.L308,.L79
	.byte	4
	.byte	'BlockId',0,1,128,4,78
	.word	.L293,.L309
	.byte	4
	.byte	'ProtectionEnabled',0,1,128,4,95
	.word	.L291,.L310
	.byte	5
	.word	.L80,.L308
	.byte	6
	.byte	'returnValue',0,1,130,4,20
	.word	.L291,.L311
	.byte	5
	.word	.L312,.L8
	.byte	6
	.byte	'NvM_RamMngmt_ptloc',0,1,153,4,35
	.word	.L313,.L314
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_SetBlockProtection')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_SetBlockProtection')
	.sect	'.debug_line'
.L152:
	.word	.L501-.L500
.L500:
	.half	3
	.word	.L503-.L502
.L502:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L503:
	.byte	5,39,7,0,5,2
	.word	.L80
	.byte	3,255,3,1,5,32,9
	.half	.L504-.L80
	.byte	3,2,1,5,76,3,23,1,5,41,9
	.half	.L312-.L504
	.byte	3,4,1,5,54,3,124,1,5,13,9
	.half	.L417-.L312
	.byte	3,4,1,5,41,9
	.half	.L505-.L417
	.byte	1,5,40,9
	.half	.L506-.L505
	.byte	1,5,72,9
	.half	.L507-.L506
	.byte	1,5,9,9
	.half	.L508-.L507
	.byte	1,5,37,7,9
	.half	.L509-.L508
	.byte	3,2,1,5,35,9
	.half	.L510-.L509
	.byte	3,4,1,5,13,9
	.half	.L419-.L510
	.byte	3,126,1,5,56,7,9
	.half	.L420-.L419
	.byte	3,2,1,5,74,1,5,56,9
	.half	.L9-.L420
	.byte	3,4,1,5,36,9
	.half	.L511-.L9
	.byte	3,3,1,5,25,9
	.half	.L512-.L511
	.byte	3,2,1,5,5,9
	.half	.L8-.L512
	.byte	3,19,1,5,1,3,1,1,7,9
	.half	.L154-.L8
	.byte	0,1,1
.L501:
	.sdecl	'.debug_ranges',debug,cluster('NvM_SetBlockProtection')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L80,0,.L154-.L80,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_GetErrorStatus')
	.sect	'.debug_info'
.L155:
	.word	327
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L158,.L157
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_GetErrorStatus',0,1,210,4,39
	.word	.L291
	.byte	1,1,1
	.word	.L82,.L315,.L81
	.byte	4
	.byte	'BlockId',0,1,210,4,74
	.word	.L293,.L316
	.byte	4
	.byte	'RequestResultPtr',0,1,211,4,60
	.word	.L317,.L318
	.byte	5
	.word	.L82,.L315
	.byte	5
	.word	.L12,.L319
	.byte	6
	.byte	'NvM_RamMngmt_ptloc',0,1,239,4,39
	.word	.L320,.L321
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_GetErrorStatus')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_GetErrorStatus')
	.sect	'.debug_line'
.L157:
	.word	.L514-.L513
.L513:
	.half	3
	.word	.L516-.L515
.L515:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L516:
	.byte	5,39,7,0,5,2
	.word	.L82
	.byte	3,209,4,1,5,34,9
	.half	.L422-.L82
	.byte	3,23,1,5,9,9
	.half	.L421-.L422
	.byte	1,5,33,7,9
	.half	.L517-.L421
	.byte	3,2,1,5,48,1,5,80,9
	.half	.L12-.L517
	.byte	3,4,1,5,51,9
	.half	.L423-.L12
	.byte	3,3,1,5,31,9
	.half	.L13-.L423
	.byte	1,5,12,9
	.half	.L319-.L13
	.byte	3,15,1,5,1,3,1,1,7,9
	.half	.L159-.L319
	.byte	0,1,1
.L514:
	.sdecl	'.debug_ranges',debug,cluster('NvM_GetErrorStatus')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L82,0,.L159-.L82,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_SetRamBlockStatus')
	.sect	'.debug_info'
.L160:
	.word	346
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L163,.L162
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_SetRamBlockStatus',0,1,195,5,39
	.word	.L291
	.byte	1,1,1
	.word	.L84,.L322,.L83
	.byte	4
	.byte	'BlockId',0,1,195,5,77
	.word	.L293,.L323
	.byte	4
	.byte	'BlockChanged',0,1,195,5,94
	.word	.L291,.L324
	.byte	5
	.word	.L84,.L322
	.byte	6
	.byte	'returnValue',0,1,197,5,20
	.word	.L291,.L325
	.byte	5
	.word	.L326,.L15
	.byte	6
	.byte	'blockMngmtPtr',0,1,223,5,39
	.word	.L327,.L328
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_SetRamBlockStatus')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_SetRamBlockStatus')
	.sect	'.debug_line'
.L162:
	.word	.L519-.L518
.L518:
	.half	3
	.word	.L521-.L520
.L520:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L521:
	.byte	5,32,7,0,5,2
	.word	.L84
	.byte	3,196,5,1,5,38,3,24,1,5,39,9
	.half	.L425-.L84
	.byte	3,102,1,5,38,3,26,1,5,9,9
	.half	.L424-.L425
	.byte	1,5,75,7,9
	.half	.L326-.L424
	.byte	3,2,1,5,53,9
	.half	.L427-.L326
	.byte	1,5,37,9
	.half	.L429-.L427
	.byte	3,2,1,5,30,9
	.half	.L428-.L429
	.byte	3,3,1,5,13,9
	.half	.L426-.L428
	.byte	3,126,1,5,51,7,9
	.half	.L430-.L426
	.byte	3,2,1,5,99,1,5,51,9
	.half	.L16-.L430
	.byte	3,14,1,5,40,9
	.half	.L522-.L16
	.byte	3,2,1,5,25,9
	.half	.L523-.L522
	.byte	3,3,1,5,5,9
	.half	.L15-.L523
	.byte	3,19,1,5,1,3,1,1,7,9
	.half	.L164-.L15
	.byte	0,1,1
.L519:
	.sdecl	'.debug_ranges',debug,cluster('NvM_SetRamBlockStatus')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L84,0,.L164-.L84,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ReadBlock')
	.sect	'.debug_info'
.L165:
	.word	341
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L168,.L167
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_ReadBlock',0,1,164,6,39
	.word	.L291
	.byte	1,1,1
	.word	.L86,.L329,.L85
	.byte	4
	.byte	'BlockId',0,1,164,6,69
	.word	.L293,.L330
	.byte	4
	.byte	'NvM_DstPtr',0,1,164,6,116
	.word	.L331,.L332
	.byte	5
	.word	.L86,.L329
	.byte	6
	.byte	'returnValue',0,1,166,6,20
	.word	.L291,.L333
	.byte	5
	.word	.L334,.L20
	.byte	6
	.byte	'NvM_RamMngmt_ptloc',0,1,198,6,43
	.word	.L335,.L336
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ReadBlock')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ReadBlock')
	.sect	'.debug_line'
.L167:
	.word	.L525-.L524
.L524:
	.half	3
	.word	.L527-.L526
.L526:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L527:
	.byte	5,32,7,0,5,2
	.word	.L86
	.byte	3,165,6,1,5,39,3,126,1,5,35,9
	.half	.L432-.L86
	.byte	3,30,1,5,59,1,5,39,9
	.half	.L434-.L432
	.byte	3,98,1,5,59,3,30,1,5,9,9
	.half	.L431-.L434
	.byte	1,5,13,7,9
	.half	.L528-.L431
	.byte	3,2,1,5,84,7,9
	.half	.L334-.L528
	.byte	3,2,1,5,62,9
	.half	.L436-.L334
	.byte	1,5,41,9
	.half	.L433-.L436
	.byte	3,2,1,5,35,9
	.half	.L437-.L433
	.byte	3,2,1,5,56,9
	.half	.L435-.L437
	.byte	1,5,40,9
	.half	.L529-.L435
	.byte	3,2,1,5,25,9
	.half	.L20-.L529
	.byte	3,3,1,5,5,9
	.half	.L19-.L20
	.byte	3,13,1,5,1,3,1,1,7,9
	.half	.L169-.L19
	.byte	0,1,1
.L525:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ReadBlock')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L86,0,.L169-.L86,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_WriteBlock')
	.sect	'.debug_info'
.L170:
	.word	342
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L173,.L172
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_WriteBlock',0,1,243,6,39
	.word	.L291
	.byte	1,1,1
	.word	.L88,.L337,.L87
	.byte	4
	.byte	'BlockId',0,1,243,6,70
	.word	.L293,.L338
	.byte	4
	.byte	'NvM_SrcPtr',0,1,243,6,119
	.word	.L339,.L340
	.byte	5
	.word	.L88,.L337
	.byte	6
	.byte	'returnValue',0,1,245,6,20
	.word	.L291,.L341
	.byte	5
	.word	.L342,.L22
	.byte	6
	.byte	'NvM_RamMngmt_ptloc',0,1,144,7,35
	.word	.L343,.L344
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_WriteBlock')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_WriteBlock')
	.sect	'.debug_line'
.L172:
	.word	.L531-.L530
.L530:
	.half	3
	.word	.L533-.L532
.L532:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L533:
	.byte	5,39,7,0,5,2
	.word	.L88
	.byte	3,242,6,1,5,32,9
	.half	.L438-.L88
	.byte	3,2,1,5,76,3,27,1,5,54,9
	.half	.L342-.L438
	.byte	1,5,38,9
	.half	.L441-.L342
	.byte	3,2,1,5,9,9
	.half	.L442-.L441
	.byte	1,5,64,7,9
	.half	.L534-.L442
	.byte	3,3,1,5,39,9
	.half	.L443-.L534
	.byte	1,5,64,1,5,13,9
	.half	.L444-.L443
	.byte	1,5,17,7,9
	.half	.L535-.L444
	.byte	3,2,1,5,45,7,9
	.half	.L536-.L535
	.byte	3,2,1,5,39,9
	.half	.L537-.L536
	.byte	3,2,1,5,60,9
	.half	.L439-.L537
	.byte	1,5,44,9
	.half	.L538-.L439
	.byte	3,2,1,5,29,9
	.half	.L24-.L538
	.byte	3,3,1,5,5,9
	.half	.L22-.L24
	.byte	3,14,1,5,1,3,1,1,7,9
	.half	.L174-.L22
	.byte	0,1,1
.L531:
	.sdecl	'.debug_ranges',debug,cluster('NvM_WriteBlock')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L88,0,.L174-.L88,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_RestoreBlockDefaults')
	.sect	'.debug_info'
.L175:
	.word	352
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L178,.L177
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_RestoreBlockDefaults',0,1,197,7,39
	.word	.L291
	.byte	1,1,1
	.word	.L90,.L345,.L89
	.byte	4
	.byte	'BlockId',0,1,197,7,80
	.word	.L293,.L346
	.byte	4
	.byte	'NvM_DstPtr',0,1,197,7,127
	.word	.L331,.L347
	.byte	5
	.word	.L90,.L345
	.byte	6
	.byte	'returnValue',0,1,199,7,20
	.word	.L291,.L348
	.byte	5
	.word	.L349,.L27
	.byte	6
	.byte	'NvM_RamMngmt_ptloc',0,1,236,7,43
	.word	.L350,.L351
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_RestoreBlockDefaults')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_RestoreBlockDefaults')
	.sect	'.debug_line'
.L177:
	.word	.L540-.L539
.L539:
	.half	3
	.word	.L542-.L541
.L541:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L542:
	.byte	5,39,7,0,5,2
	.word	.L90
	.byte	3,196,7,1,5,32,9
	.half	.L446-.L90
	.byte	3,2,1,5,65,3,33,1,5,35,9
	.half	.L449-.L446
	.byte	1,5,65,1,5,9,9
	.half	.L445-.L449
	.byte	1,5,13,7,9
	.half	.L543-.L445
	.byte	3,2,1,5,84,7,9
	.half	.L349-.L543
	.byte	3,2,1,5,62,9
	.half	.L450-.L349
	.byte	1,5,41,9
	.half	.L448-.L450
	.byte	3,2,1,5,35,9
	.half	.L451-.L448
	.byte	3,2,1,5,56,9
	.half	.L447-.L451
	.byte	1,5,40,9
	.half	.L544-.L447
	.byte	3,2,1,5,25,9
	.half	.L27-.L544
	.byte	3,3,1,5,5,9
	.half	.L26-.L27
	.byte	3,13,1,5,1,3,1,1,7,9
	.half	.L179-.L26
	.byte	0,1,1
.L540:
	.sdecl	'.debug_ranges',debug,cluster('NvM_RestoreBlockDefaults')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L90,0,.L179-.L90,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_EraseNvBlock')
	.sect	'.debug_info'
.L180:
	.word	320
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L183,.L182
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_EraseNvBlock',0,1,152,8,39
	.word	.L291
	.byte	1,1,1
	.word	.L92,.L352,.L91
	.byte	4
	.byte	'BlockId',0,1,152,8,72
	.word	.L293,.L353
	.byte	5
	.word	.L92,.L352
	.byte	6
	.byte	'returnValue',0,1,154,8,20
	.word	.L291,.L354
	.byte	5
	.word	.L355,.L29
	.byte	6
	.byte	'NvM_RamMngmt_ptloc',0,1,181,8,35
	.word	.L356,.L357
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_EraseNvBlock')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_EraseNvBlock')
	.sect	'.debug_line'
.L182:
	.word	.L546-.L545
.L545:
	.half	3
	.word	.L548-.L547
.L547:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L548:
	.byte	5,39,7,0,5,2
	.word	.L92
	.byte	3,151,8,1,5,32,9
	.half	.L452-.L92
	.byte	3,2,1,5,76,3,27,1,5,38,9
	.half	.L355-.L452
	.byte	3,2,1,5,9,9
	.half	.L453-.L355
	.byte	1,5,49,7,9
	.half	.L549-.L453
	.byte	3,2,1,5,74,1,5,39,9
	.half	.L550-.L549
	.byte	1,5,5,9
	.half	.L29-.L550
	.byte	3,13,1,5,1,3,1,1,7,9
	.half	.L184-.L29
	.byte	0,1,1
.L546:
	.sdecl	'.debug_ranges',debug,cluster('NvM_EraseNvBlock')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L92,0,.L184-.L92,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_InvalidateNvBlock')
	.sect	'.debug_info'
.L185:
	.word	325
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L188,.L187
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_InvalidateNvBlock',0,1,217,8,39
	.word	.L291
	.byte	1,1,1
	.word	.L94,.L358,.L93
	.byte	4
	.byte	'BlockId',0,1,217,8,77
	.word	.L293,.L359
	.byte	5
	.word	.L94,.L358
	.byte	6
	.byte	'returnValue',0,1,219,8,20
	.word	.L291,.L360
	.byte	5
	.word	.L361,.L31
	.byte	6
	.byte	'NvM_RamMngmt_ptloc',0,1,242,8,35
	.word	.L362,.L363
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_InvalidateNvBlock')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_InvalidateNvBlock')
	.sect	'.debug_line'
.L187:
	.word	.L552-.L551
.L551:
	.half	3
	.word	.L554-.L553
.L553:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L554:
	.byte	5,39,7,0,5,2
	.word	.L94
	.byte	3,216,8,1,5,32,9
	.half	.L455-.L94
	.byte	3,2,1,5,76,3,23,1,5,38,9
	.half	.L361-.L455
	.byte	3,2,1,5,9,9
	.half	.L456-.L361
	.byte	1,5,49,7,9
	.half	.L555-.L456
	.byte	3,2,1,5,82,1,5,39,9
	.half	.L556-.L555
	.byte	1,5,5,9
	.half	.L31-.L556
	.byte	3,13,1,5,1,3,1,1,7,9
	.half	.L189-.L31
	.byte	0,1,1
.L552:
	.sdecl	'.debug_ranges',debug,cluster('NvM_InvalidateNvBlock')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L94,0,.L189-.L94,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CancelJobs')
	.sect	'.debug_info'
.L190:
	.word	250
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L193,.L192
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_CancelJobs',0,1,149,9,39
	.word	.L291
	.byte	1,1,1
	.word	.L96,.L364,.L95
	.byte	4
	.byte	'BlockId',0,1,149,9,70
	.word	.L293,.L365
	.byte	5
	.word	.L96,.L364
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CancelJobs')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CancelJobs')
	.sect	'.debug_line'
.L192:
	.word	.L558-.L557
.L557:
	.half	3
	.word	.L560-.L559
.L559:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L560:
	.byte	5,38,7,0,5,2
	.word	.L96
	.byte	3,165,9,1,5,37,9
	.half	.L458-.L96
	.byte	1,5,1,3,13,1,7,9
	.half	.L194-.L458
	.byte	0,1,1
.L558:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CancelJobs')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L96,0,.L194-.L96,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ReadAll')
	.sect	'.debug_info'
.L195:
	.word	218
	.half	3
	.word	.L196
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L198,.L197
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_ReadAll',0,1,201,9,29,1,1,1
	.word	.L98,.L366,.L97
	.byte	4
	.word	.L367
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ReadAll')
	.sect	'.debug_abbrev'
.L196:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ReadAll')
	.sect	'.debug_line'
.L197:
	.word	.L562-.L561
.L561:
	.half	3
	.word	.L564-.L563
.L563:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L564:
	.byte	5,33,7,0,5,2
	.word	.L98
	.byte	3,216,9,1,5,9,9
	.half	.L565-.L98
	.byte	3,2,1,5,25,9
	.half	.L566-.L565
	.byte	3,1,1,9
	.half	.L567-.L566
	.byte	3,1,1,5,32,9
	.half	.L568-.L567
	.byte	3,2,1,5,27,9
	.half	.L569-.L568
	.byte	3,2,1,5,41,9
	.half	.L570-.L569
	.byte	1,5,1,9
	.half	.L199-.L570
	.byte	3,11,0,1,1
.L562:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ReadAll')
	.sect	'.debug_ranges'
.L198:
	.word	-1,.L98,0,.L199-.L98,0,0
.L367:
	.word	-1,.L98,0,.L366-.L98,-1,.L100,0,.L274-.L100,-1,.L102,0,.L284-.L102,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_KillReadAll')
	.sect	'.debug_info'
.L200:
	.word	226
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L203,.L202
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_KillReadAll',0,1,251,9,29,1,1,1
	.word	.L104,.L368,.L103
	.byte	4
	.word	.L104,.L368
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_KillReadAll')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_KillReadAll')
	.sect	'.debug_line'
.L202:
	.word	.L572-.L571
.L571:
	.half	3
	.word	.L574-.L573
.L573:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L574:
	.byte	5,13,7,0,5,2
	.word	.L104
	.byte	3,134,10,1,5,9,9
	.half	.L575-.L104
	.byte	1,5,37,7,9
	.half	.L576-.L575
	.byte	3,2,1,5,13,9
	.half	.L577-.L576
	.byte	3,1,1,5,29,9
	.half	.L578-.L577
	.byte	1,5,36,9
	.half	.L579-.L578
	.byte	3,1,1,5,1,7,9
	.half	.L34-.L579
	.byte	3,12,1,7,9
	.half	.L204-.L34
	.byte	0,1,1
.L572:
	.sdecl	'.debug_ranges',debug,cluster('NvM_KillReadAll')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L104,0,.L204-.L104,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_WriteAll')
	.sect	'.debug_info'
.L205:
	.word	223
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L208,.L207
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_WriteAll',0,1,171,10,29,1,1,1
	.word	.L106,.L369,.L105
	.byte	4
	.word	.L106,.L369
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_WriteAll')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_WriteAll')
	.sect	'.debug_line'
.L207:
	.word	.L581-.L580
.L580:
	.half	3
	.word	.L583-.L582
.L582:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L583:
	.byte	5,33,7,0,5,2
	.word	.L106
	.byte	3,186,10,1,5,9,9
	.half	.L584-.L106
	.byte	3,2,1,5,25,9
	.half	.L585-.L584
	.byte	3,2,1,9
	.half	.L586-.L585
	.byte	3,1,1,5,32,9
	.half	.L587-.L586
	.byte	3,2,1,5,27,9
	.half	.L588-.L587
	.byte	3,2,1,5,42,9
	.half	.L589-.L588
	.byte	1,5,1,9
	.half	.L209-.L589
	.byte	3,11,0,1,1
.L581:
	.sdecl	'.debug_ranges',debug,cluster('NvM_WriteAll')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L106,0,.L209-.L106,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CancelWriteAll')
	.sect	'.debug_info'
.L210:
	.word	229
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L213,.L212
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_CancelWriteAll',0,1,222,10,29,1,1,1
	.word	.L108,.L370,.L107
	.byte	4
	.word	.L108,.L370
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CancelWriteAll')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CancelWriteAll')
	.sect	'.debug_line'
.L212:
	.word	.L591-.L590
.L590:
	.half	3
	.word	.L593-.L592
.L592:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L593:
	.byte	5,33,7,0,5,2
	.word	.L108
	.byte	3,233,10,1,5,9,9
	.half	.L594-.L108
	.byte	3,2,1,5,25,9
	.half	.L595-.L594
	.byte	1,5,32,9
	.half	.L596-.L595
	.byte	3,2,1,5,1,7,9
	.half	.L214-.L596
	.byte	3,11,0,1,1
.L591:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CancelWriteAll')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L108,0,.L214-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_KillWriteAll')
	.sect	'.debug_info'
.L215:
	.word	343
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L218,.L217
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_KillWriteAll',0,1,138,11,29,1,1,1
	.word	.L110,.L371,.L109
	.byte	4
	.word	.L110,.L371
	.byte	4
	.word	.L110,.L39
	.byte	5
	.byte	'blockEndNotificationId',0,1,153,11,25
	.word	.L293,.L372
	.byte	5
	.byte	'writeAllEndNotification',0,1,154,11,17
	.word	.L291,.L373
	.byte	4
	.word	.L374,.L36
	.byte	5
	.byte	'mngmt_pt',0,1,171,11,43
	.word	.L375,.L376
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_KillWriteAll')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_KillWriteAll')
	.sect	'.debug_line'
.L217:
	.word	.L598-.L597
.L597:
	.half	3
	.word	.L600-.L599
.L599:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L600:
	.byte	5,48,7,0,5,2
	.word	.L110
	.byte	3,152,11,1,5,41,9
	.half	.L459-.L110
	.byte	3,1,1,5,33,3,3,1,5,13,9
	.half	.L460-.L459
	.byte	3,2,1,5,9,9
	.half	.L601-.L460
	.byte	1,5,29,7,9
	.half	.L602-.L601
	.byte	3,6,1,9
	.half	.L603-.L602
	.byte	3,1,1,5,17,9
	.half	.L604-.L603
	.byte	3,3,1,5,33,9
	.half	.L605-.L604
	.byte	1,5,16,9
	.half	.L606-.L605
	.byte	1,5,112,7,9
	.half	.L607-.L606
	.byte	1,5,95,9
	.half	.L608-.L607
	.byte	1,5,54,7,9
	.half	.L374-.L608
	.byte	3,2,1,5,76,9
	.half	.L609-.L374
	.byte	1,5,40,9
	.half	.L462-.L609
	.byte	3,1,1,5,31,9
	.half	.L610-.L462
	.byte	3,3,1,5,52,9
	.half	.L611-.L610
	.byte	1,5,75,9
	.half	.L612-.L611
	.byte	1,5,47,3,127,1,5,60,9
	.half	.L36-.L612
	.byte	3,5,1,5,13,1,5,37,9
	.half	.L613-.L36
	.byte	3,2,1,5,58,3,126,1,5,32,9
	.half	.L35-.L613
	.byte	3,5,1,5,9,9
	.half	.L614-.L35
	.byte	3,2,1,5,59,7,9
	.half	.L615-.L614
	.byte	3,2,1,5,74,9
	.half	.L616-.L615
	.byte	1,5,9,9
	.half	.L38-.L616
	.byte	3,3,1,5,31,7,9
	.half	.L617-.L38
	.byte	3,2,1,5,46,9
	.half	.L618-.L617
	.byte	1,5,1,9
	.half	.L39-.L618
	.byte	3,12,1,7,9
	.half	.L219-.L39
	.byte	0,1,1
.L598:
	.sdecl	'.debug_ranges',debug,cluster('NvM_KillWriteAll')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L110,0,.L219-.L110,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_MainFunction')
	.sect	'.debug_info'
.L220:
	.word	227
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L223,.L222
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_MainFunction',0,1,140,12,29,1,1,1
	.word	.L112,.L377,.L111
	.byte	4
	.word	.L112,.L377
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_MainFunction')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_MainFunction')
	.sect	'.debug_line'
.L222:
	.word	.L620-.L619
.L619:
	.half	3
	.word	.L622-.L621
.L621:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L622:
	.byte	5,9,7,0,5,2
	.word	.L112
	.byte	3,152,12,1,5,31,9
	.half	.L623-.L112
	.byte	1,5,39,9
	.half	.L624-.L623
	.byte	3,24,1,5,49,9
	.half	.L625-.L624
	.byte	3,104,1,5,42,9
	.half	.L626-.L625
	.byte	3,26,1,5,41,9
	.half	.L627-.L626
	.byte	3,2,1,5,39,9
	.half	.L628-.L627
	.byte	3,124,1,5,42,9
	.half	.L629-.L628
	.byte	3,2,1,5,41,9
	.half	.L630-.L629
	.byte	3,2,1,5,39,9
	.half	.L40-.L630
	.byte	3,124,1,5,29,9
	.half	.L631-.L40
	.byte	1,5,42,9
	.half	.L632-.L631
	.byte	3,2,1,5,32,9
	.half	.L633-.L632
	.byte	1,5,41,9
	.half	.L634-.L633
	.byte	3,2,1,5,31,9
	.half	.L635-.L634
	.byte	1,5,39,9
	.half	.L636-.L635
	.byte	3,2,1,5,87,9
	.half	.L637-.L636
	.byte	1,5,1,7,9
	.half	.L638-.L637
	.byte	3,11,1,7,9
	.half	.L224-.L638
	.byte	0,1,1
.L620:
	.sdecl	'.debug_ranges',debug,cluster('NvM_MainFunction')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L112,0,.L224-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_SetBlockLockStatus')
	.sect	'.debug_info'
.L225:
	.word	279
	.half	3
	.word	.L226
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L228,.L227
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_SetBlockLockStatus',0,1,128,13,29,1,1,1
	.word	.L118,.L378,.L117
	.byte	4
	.byte	'BlockId',0,1,128,13,68
	.word	.L293,.L379
	.byte	4
	.byte	'BlockLocked',0,1,128,13,85
	.word	.L291,.L380
	.byte	5
	.word	.L118,.L378
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_SetBlockLockStatus')
	.sect	'.debug_abbrev'
.L226:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_SetBlockLockStatus')
	.sect	'.debug_line'
.L227:
	.word	.L640-.L639
.L639:
	.half	3
	.word	.L642-.L641
.L641:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L642:
	.byte	5,29,7,0,5,2
	.word	.L118
	.byte	3,255,12,1,5,33,3,20,1,5,35,9
	.half	.L463-.L118
	.byte	3,4,1,5,13,1,5,34,9
	.half	.L464-.L463
	.byte	1,5,66,9
	.half	.L643-.L464
	.byte	1,5,9,9
	.half	.L465-.L643
	.byte	3,126,1,5,86,7,9
	.half	.L466-.L465
	.byte	3,2,1,5,106,1,5,86,9
	.half	.L46-.L466
	.byte	3,4,1,5,32,9
	.half	.L644-.L46
	.byte	3,3,1,5,1,7,9
	.half	.L229-.L644
	.byte	3,11,0,1,1
.L640:
	.sdecl	'.debug_ranges',debug,cluster('NvM_SetBlockLockStatus')
	.sect	'.debug_ranges'
.L228:
	.word	-1,.L118,0,.L229-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_JobEndNotification')
	.sect	'.debug_info'
.L230:
	.word	233
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L233,.L232
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_JobEndNotification',0,1,205,12,29,1,1,1
	.word	.L114,.L381,.L113
	.byte	4
	.word	.L114,.L381
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_JobEndNotification')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_JobEndNotification')
	.sect	'.debug_line'
.L232:
	.word	.L646-.L645
.L645:
	.half	3
	.word	.L648-.L647
.L647:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L648:
	.byte	5,6,7,0,5,2
	.word	.L114
	.byte	3,206,12,1,5,44,9
	.half	.L649-.L114
	.byte	1,5,42,1,5,1,9
	.half	.L650-.L649
	.byte	3,1,1,7,9
	.half	.L234-.L650
	.byte	0,1,1
.L646:
	.sdecl	'.debug_ranges',debug,cluster('NvM_JobEndNotification')
	.sect	'.debug_ranges'
.L233:
	.word	-1,.L114,0,.L234-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_JobErrorNotification')
	.sect	'.debug_info'
.L235:
	.word	235
	.half	3
	.word	.L236
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L238,.L237
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_JobErrorNotification',0,1,219,12,29,1,1,1
	.word	.L116,.L382,.L115
	.byte	4
	.word	.L116,.L382
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_JobErrorNotification')
	.sect	'.debug_abbrev'
.L236:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_JobErrorNotification')
	.sect	'.debug_line'
.L237:
	.word	.L652-.L651
.L651:
	.half	3
	.word	.L654-.L653
.L653:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L654:
	.byte	5,38,7,0,5,2
	.word	.L116
	.byte	3,221,12,1,5,60,9
	.half	.L655-.L116
	.byte	1,5,74,9
	.half	.L656-.L655
	.byte	1,5,31,9
	.half	.L657-.L656
	.byte	1,5,14,9
	.half	.L658-.L657
	.byte	3,3,1,7,9
	.half	.L659-.L658
	.byte	3,4,1,7,9
	.half	.L660-.L659
	.byte	1,5,51,9
	.half	.L41-.L660
	.byte	3,125,1,5,13,3,1,1,5,51,9
	.half	.L42-.L41
	.byte	3,3,1,5,13,3,1,1,5,51,9
	.half	.L43-.L42
	.byte	3,3,1,5,49,9
	.half	.L45-.L43
	.byte	1,5,1,9
	.half	.L661-.L45
	.byte	3,3,1,7,9
	.half	.L239-.L661
	.byte	0,1,1
.L652:
	.sdecl	'.debug_ranges',debug,cluster('NvM_JobErrorNotification')
	.sect	'.debug_ranges'
.L238:
	.word	-1,.L116,0,.L239-.L116,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_WriteProtectionChecks')
	.sect	'.debug_info'
.L240:
	.word	288
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L243,.L242
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_WriteProtectionChecks',0,1,185,13,26
	.word	.L291
	.byte	1,1,1
	.word	.L120,.L383,.L119
	.byte	4
	.byte	'MngmtPtr',0,1,185,13,83
	.word	.L384,.L385
	.byte	5
	.word	.L120,.L383
	.byte	6
	.byte	'returnValue',0,1,187,13,13
	.word	.L291,.L386
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_WriteProtectionChecks')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_WriteProtectionChecks')
	.sect	'.debug_line'
.L242:
	.word	.L663-.L662
.L662:
	.half	3
	.word	.L665-.L664
.L664:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L665:
	.byte	5,18,7,0,5,2
	.word	.L120
	.byte	3,189,13,1,5,25,9
	.half	.L666-.L120
	.byte	3,125,1,5,5,9
	.half	.L467-.L666
	.byte	3,3,1,5,27,7,9
	.half	.L667-.L467
	.byte	3,5,1,5,10,9
	.half	.L668-.L667
	.byte	1,5,63,7,9
	.half	.L669-.L668
	.byte	3,2,1,5,84,9
	.half	.L670-.L669
	.byte	1,5,1,9
	.half	.L48-.L670
	.byte	3,9,1,5,21,7,9
	.half	.L49-.L48
	.byte	3,124,1,5,1,3,4,1,7,9
	.half	.L244-.L49
	.byte	0,1,1
.L663:
	.sdecl	'.debug_ranges',debug,cluster('NvM_WriteProtectionChecks')
	.sect	'.debug_ranges'
.L243:
	.word	-1,.L120,0,.L244-.L120,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CheckBlockType')
	.sect	'.debug_info'
.L245:
	.word	254
	.half	3
	.word	.L246
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L248,.L247
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_CheckBlockType',0,1,217,13,26
	.word	.L291
	.byte	1,1,1
	.word	.L122,.L387,.L121
	.byte	4
	.byte	'BlockId',0,1,217,13,67
	.word	.L388,.L389
	.byte	5
	.word	.L122,.L387
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CheckBlockType')
	.sect	'.debug_abbrev'
.L246:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CheckBlockType')
	.sect	'.debug_line'
.L247:
	.word	.L672-.L671
.L671:
	.half	3
	.word	.L674-.L673
.L673:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L674:
	.byte	5,49,7,0,5,2
	.word	.L122
	.byte	3,218,13,1,5,21,1,5,49,9
	.half	.L675-.L122
	.byte	1,5,48,9
	.half	.L676-.L675
	.byte	1,5,80,9
	.half	.L677-.L676
	.byte	1,5,93,9
	.half	.L678-.L677
	.byte	1,5,114,9
	.half	.L679-.L678
	.byte	1,5,1,3,1,1,7,9
	.half	.L249-.L679
	.byte	0,1,1
.L672:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CheckBlockType')
	.sect	'.debug_ranges'
.L248:
	.word	-1,.L122,0,.L249-.L122,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CheckAddress')
	.sect	'.debug_info'
.L250:
	.word	319
	.half	3
	.word	.L251
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L253,.L252
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_CheckAddress',0,1,231,13,26
	.word	.L291
	.byte	1,1,1
	.word	.L124,.L390,.L123
	.byte	4
	.byte	'BlockId',0,1,231,13,65
	.word	.L391,.L392
	.byte	4
	.byte	'RamPtr',0,1,231,13,87
	.word	.L339,.L393
	.byte	5
	.word	.L124,.L390
	.byte	6
	.byte	'descPtr',0,1,233,13,27
	.word	.L394,.L395
	.byte	6
	.byte	'returnValue',0,1,234,13,13
	.word	.L291,.L396
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CheckAddress')
	.sect	'.debug_abbrev'
.L251:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CheckAddress')
	.sect	'.debug_line'
.L252:
	.word	.L681-.L680
.L680:
	.half	3
	.word	.L683-.L682
.L682:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L683:
	.byte	5,66,7,0,5,2
	.word	.L124
	.byte	3,232,13,1,5,38,1,5,25,9
	.half	.L684-.L124
	.byte	3,1,1,5,38,3,127,1,5,66,9
	.half	.L468-.L684
	.byte	1,5,65,9
	.half	.L685-.L468
	.byte	1,5,5,9
	.half	.L469-.L685
	.byte	3,6,1,5,77,7,9
	.half	.L686-.L469
	.byte	3,2,1,5,33,9
	.half	.L687-.L686
	.byte	1,5,22,7,9
	.half	.L688-.L687
	.byte	3,1,1,5,14,9
	.half	.L689-.L688
	.byte	1,5,67,7,9
	.half	.L690-.L689
	.byte	1,5,89,9
	.half	.L691-.L690
	.byte	1,5,77,7,9
	.half	.L55-.L691
	.byte	3,127,1,5,1,9
	.half	.L53-.L55
	.byte	3,5,1,7,9
	.half	.L254-.L53
	.byte	0,1,1
.L681:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CheckAddress')
	.sect	'.debug_ranges'
.L253:
	.word	-1,.L124,0,.L254-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CheckBlockId')
	.sect	'.debug_info'
.L255:
	.word	252
	.half	3
	.word	.L256
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L258,.L257
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_CheckBlockId',0,1,130,14,26
	.word	.L291
	.byte	1,1,1
	.word	.L126,.L397,.L125
	.byte	4
	.byte	'BlockId',0,1,130,14,65
	.word	.L398,.L399
	.byte	5
	.word	.L126,.L397
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CheckBlockId')
	.sect	'.debug_abbrev'
.L256:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CheckBlockId')
	.sect	'.debug_line'
.L257:
	.word	.L693-.L692
.L692:
	.half	3
	.word	.L695-.L694
.L694:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L695:
	.byte	5,23,7,0,5,2
	.word	.L126
	.byte	3,131,14,1,5,60,9
	.half	.L696-.L126
	.byte	1,5,22,9
	.half	.L697-.L696
	.byte	1,5,97,7,9
	.half	.L698-.L697
	.byte	1,5,95,9
	.half	.L699-.L698
	.byte	1,5,60,9
	.half	.L700-.L699
	.byte	1,5,1,9
	.half	.L58-.L700
	.byte	3,1,1,7,9
	.half	.L259-.L58
	.byte	0,1,1
.L693:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CheckBlockId')
	.sect	'.debug_ranges'
.L258:
	.word	-1,.L126,0,.L259-.L126,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CheckBlockPending')
	.sect	'.debug_info'
.L260:
	.word	284
	.half	3
	.word	.L261
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L263,.L262
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_CheckBlockPending',0,1,218,14,26
	.word	.L291
	.byte	1,1,1
	.word	.L128,.L400,.L127
	.byte	4
	.byte	'BlockId',0,1,218,14,70
	.word	.L401,.L402
	.byte	5
	.word	.L128,.L400
	.byte	6
	.byte	'blockPending',0,1,220,14,13
	.word	.L291,.L403
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CheckBlockPending')
	.sect	'.debug_abbrev'
.L261:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CheckBlockPending')
	.sect	'.debug_line'
.L262:
	.word	.L702-.L701
.L701:
	.half	3
	.word	.L704-.L703
.L703:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L704:
	.byte	5,8,7,0,5,2
	.word	.L128
	.byte	3,222,14,1,5,28,7,9
	.half	.L705-.L128
	.byte	1,5,44,9
	.half	.L706-.L705
	.byte	1,5,27,9
	.half	.L707-.L706
	.byte	1,5,122,7,9
	.half	.L708-.L707
	.byte	1,5,64,7,9
	.half	.L62-.L708
	.byte	3,6,1,5,13,9
	.half	.L709-.L62
	.byte	3,125,1,5,9,9
	.half	.L710-.L709
	.byte	1,5,46,7,9
	.half	.L711-.L710
	.byte	3,3,1,5,1,3,17,1,5,46,7,9
	.half	.L64-.L711
	.byte	3,117,1,5,1,3,11,1,5,54,7,9
	.half	.L61-.L64
	.byte	3,124,1,5,62,9
	.half	.L470-.L61
	.byte	1,5,84,9
	.half	.L712-.L470
	.byte	1,5,1,3,4,1,7,9
	.half	.L264-.L712
	.byte	0,1,1
.L702:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CheckBlockPending')
	.sect	'.debug_ranges'
.L263:
	.word	-1,.L128,0,.L264-.L128,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_GetMngmtAreaPtr')
	.sect	'.debug_info'
.L265:
	.word	255
	.half	3
	.word	.L266
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L268,.L267
	.byte	2
	.word	.L131
	.byte	3
	.byte	'NvM_GetMngmtAreaPtr',0,1,130,15,38
	.word	.L404
	.byte	1,1,1
	.word	.L130,.L405,.L129
	.byte	4
	.byte	'BlockId',0,1,130,15,80
	.word	.L406,.L407
	.byte	5
	.word	.L130,.L405
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_GetMngmtAreaPtr')
	.sect	'.debug_abbrev'
.L266:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_GetMngmtAreaPtr')
	.sect	'.debug_line'
.L267:
	.word	.L714-.L713
.L713:
	.half	3
	.word	.L716-.L715
.L715:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L716:
	.byte	5,24,7,0,5,2
	.word	.L130
	.byte	3,131,15,1,5,12,9
	.half	.L717-.L130
	.byte	1,5,60,7,9
	.half	.L718-.L717
	.byte	1,5,81,9
	.half	.L719-.L718
	.byte	1,5,1,9
	.half	.L720-.L719
	.byte	3,1,1,5,96,7,9
	.half	.L68-.L720
	.byte	3,127,1,5,1,9
	.half	.L721-.L68
	.byte	3,1,1,7,9
	.half	.L269-.L721
	.byte	0,1,1
.L714:
	.sdecl	'.debug_ranges',debug,cluster('NvM_GetMngmtAreaPtr')
	.sect	'.debug_ranges'
.L268:
	.word	-1,.L130,0,.L269-.L130,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L270:
	.word	209
	.half	3
	.word	.L271
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L273,.L272
	.byte	2
	.word	.L131
	.byte	3
	.byte	'.cocofun_1',0,1,201,9,29,1
	.word	.L100,.L274,.L99
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L271:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L272:
	.word	.L723-.L722
.L722:
	.half	3
	.word	.L725-.L724
.L724:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L725:
	.byte	5,9,7,0,5,2
	.word	.L100
	.byte	3,218,9,1,5,56,9
	.half	.L726-.L100
	.byte	1,5,54,1,5,9,9
	.half	.L727-.L726
	.byte	3,1,1,9
	.half	.L274-.L727
	.byte	0,1,1,5,9,0,5,2
	.word	.L100
	.byte	3,188,10,1,5,57,9
	.half	.L726-.L100
	.byte	1,5,55,1,5,9,9
	.half	.L727-.L726
	.byte	3,159,127,1,9
	.half	.L274-.L727
	.byte	0,1,1
.L723:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L273:
	.word	-1,.L100,0,.L274-.L100,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L275:
	.word	209
	.half	3
	.word	.L276
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L278,.L277
	.byte	2
	.word	.L131
	.byte	3
	.byte	'.cocofun_2',0,1,188,2,29,1
	.word	.L74,.L279,.L73
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L276:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L277:
	.word	.L729-.L728
.L728:
	.half	3
	.word	.L731-.L730
.L730:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L731:
	.byte	5,5,7,0,5,2
	.word	.L74
	.byte	3,203,2,1,9
	.half	.L279-.L74
	.byte	0,1,1,5,13,0,5,2
	.word	.L74
	.byte	3,151,13,1,5,5,9
	.half	.L732-.L74
	.byte	3,180,117,1,7,9
	.half	.L279-.L732
	.byte	0,1,1,5,60,0,5,2
	.word	.L74
	.byte	3,131,15,1,5,5,9
	.half	.L732-.L74
	.byte	3,200,115,1,7,9
	.half	.L279-.L732
	.byte	0,1,1
.L729:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L278:
	.word	-1,.L74,0,.L279-.L74,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L280:
	.word	209
	.half	3
	.word	.L281
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L283,.L282
	.byte	2
	.word	.L131
	.byte	3
	.byte	'.cocofun_3',0,1,201,9,29,1
	.word	.L102,.L284,.L101
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L281:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L282:
	.word	.L734-.L733
.L733:
	.half	3
	.word	.L736-.L735
.L735:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM.c',0,0,0,0,0
.L736:
	.byte	5,9,7,0,5,2
	.word	.L102
	.byte	3,219,9,1,9
	.half	.L284-.L102
	.byte	0,1,1,5,9,0,5,2
	.word	.L102
	.byte	3,190,10,1,9
	.half	.L737-.L102
	.byte	3,157,127,1,7,9
	.half	.L284-.L737
	.byte	0,1,1,5,9,0,5,2
	.word	.L102
	.byte	3,235,10,1,9
	.half	.L737-.L102
	.byte	3,240,126,1,7,9
	.half	.L284-.L737
	.byte	0,1,1,5,13,0,5,2
	.word	.L102
	.byte	3,225,14,1,5,9,9
	.half	.L737-.L102
	.byte	3,250,122,1,7,9
	.half	.L284-.L737
	.byte	0,1,1
.L734:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L283:
	.word	-1,.L102,0,.L284-.L102,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ApiFlags_u8')
	.sect	'.debug_info'
.L285:
	.word	203
	.half	3
	.word	.L286
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L131
	.byte	3
	.byte	'NvM_ApiFlags_u8',0,7,100,27
	.word	.L291
	.byte	1,5,3
	.word	NvM_ApiFlags_u8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ApiFlags_u8')
	.sect	'.debug_abbrev'
.L286:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L99:
	.word	-1,.L100,0,.L274-.L100
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L73:
	.word	-1,.L74,0,.L279-.L74
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L101:
	.word	-1,.L102,0,.L284-.L102
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CancelJobs')
	.sect	'.debug_loc'
.L365:
	.word	-1,.L96,0,.L458-.L96
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L95:
	.word	-1,.L96,0,.L364-.L96
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CancelWriteAll')
	.sect	'.debug_loc'
.L107:
	.word	-1,.L108,0,.L370-.L108
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CheckAddress')
	.sect	'.debug_loc'
.L392:
	.word	-1,.L124,0,.L390-.L124
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L123:
	.word	-1,.L124,0,.L390-.L124
	.half	2
	.byte	138,0
	.word	0,0
.L393:
	.word	-1,.L124,0,.L390-.L124
	.half	1
	.byte	100
	.word	0,0
.L395:
	.word	-1,.L124,.L469-.L124,.L390-.L124
	.half	1
	.byte	111
	.word	0,0
.L396:
	.word	-1,.L124,.L468-.L124,.L390-.L124
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CheckBlockId')
	.sect	'.debug_loc'
.L399:
	.word	-1,.L126,0,.L397-.L126
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L125:
	.word	-1,.L126,0,.L397-.L126
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CheckBlockPending')
	.sect	'.debug_loc'
.L402:
	.word	-1,.L128,.L102-.L128,.L284-.L128
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L470-.L128
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L127:
	.word	-1,.L128,0,.L400-.L128
	.half	2
	.byte	138,0
	.word	0,0
.L403:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CheckBlockType')
	.sect	'.debug_loc'
.L389:
	.word	-1,.L122,0,.L387-.L122
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L121:
	.word	-1,.L122,0,.L387-.L122
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_EraseNvBlock')
	.sect	'.debug_loc'
.L353:
	.word	-1,.L92,0,.L355-.L92
	.half	5
	.byte	144,34,157,32,0
	.word	.L452-.L92,.L352-.L92
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L91:
	.word	-1,.L92,0,.L352-.L92
	.half	2
	.byte	138,0
	.word	0,0
.L357:
	.word	-1,.L92,.L355-.L92,.L453-.L92
	.half	1
	.byte	98
	.word	.L454-.L92,.L453-.L92
	.half	1
	.byte	100
	.word	0,0
.L354:
	.word	-1,.L92,.L355-.L92,.L352-.L92
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_GetDataIndex')
	.sect	'.debug_loc'
.L301:
	.word	-1,.L78,0,.L413-.L78
	.half	5
	.byte	144,34,157,32,0
	.word	.L414-.L78,.L415-.L78
	.half	5
	.byte	144,39,157,32,32
	.word	.L5-.L78,.L6-.L78
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L303:
	.word	-1,.L78,0,.L413-.L78
	.half	1
	.byte	100
	.word	.L414-.L78,.L300-.L78
	.half	1
	.byte	111
	.word	0,0
.L77:
	.word	-1,.L78,0,.L300-.L78
	.half	2
	.byte	138,0
	.word	0,0
.L307:
	.word	-1,.L78,.L416-.L78,.L5-.L78
	.half	1
	.byte	98
	.word	0,0
.L304:
	.word	-1,.L78,.L413-.L78,.L300-.L78
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_GetErrorStatus')
	.sect	'.debug_loc'
.L316:
	.word	-1,.L82,0,.L421-.L82
	.half	5
	.byte	144,34,157,32,0
	.word	.L422-.L82,.L13-.L82
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L81:
	.word	-1,.L82,0,.L315-.L82
	.half	2
	.byte	138,0
	.word	0,0
.L321:
	.word	-1,.L82,.L423-.L82,.L13-.L82
	.half	1
	.byte	98
	.word	0,0
.L318:
	.word	-1,.L82,0,.L421-.L82
	.half	1
	.byte	100
	.word	.L422-.L82,.L315-.L82
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_GetMngmtAreaPtr')
	.sect	'.debug_loc'
.L407:
	.word	-1,.L130,.L74-.L130,.L279-.L130
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L405-.L130
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L129:
	.word	-1,.L130,0,.L405-.L130
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_Init')
	.sect	'.debug_loc'
.L290:
	.word	-1,.L72,.L408-.L72,.L287-.L72
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L71:
	.word	-1,.L72,0,.L287-.L72
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_InvalidateNvBlock')
	.sect	'.debug_loc'
.L359:
	.word	-1,.L94,0,.L361-.L94
	.half	5
	.byte	144,34,157,32,0
	.word	.L455-.L94,.L358-.L94
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L93:
	.word	-1,.L94,0,.L358-.L94
	.half	2
	.byte	138,0
	.word	0,0
.L363:
	.word	-1,.L94,.L361-.L94,.L456-.L94
	.half	1
	.byte	98
	.word	.L457-.L94,.L456-.L94
	.half	1
	.byte	100
	.word	0,0
.L360:
	.word	-1,.L94,.L361-.L94,.L358-.L94
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_JobEndNotification')
	.sect	'.debug_loc'
.L113:
	.word	-1,.L114,0,.L381-.L114
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_JobErrorNotification')
	.sect	'.debug_loc'
.L115:
	.word	-1,.L116,0,.L382-.L116
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_KillReadAll')
	.sect	'.debug_loc'
.L103:
	.word	-1,.L104,0,.L368-.L104
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_KillWriteAll')
	.sect	'.debug_loc'
.L109:
	.word	-1,.L110,0,.L371-.L110
	.half	2
	.byte	138,0
	.word	0,0
.L372:
	.word	-1,.L110,.L459-.L110,.L371-.L110
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L376:
	.word	-1,.L110,.L462-.L110,.L36-.L110
	.half	1
	.byte	111
	.word	0,0
.L373:
	.word	-1,.L110,.L460-.L110,.L461-.L110
	.half	5
	.byte	144,36,157,32,0
	.word	.L35-.L110,.L371-.L110
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_MainFunction')
	.sect	'.debug_loc'
.L111:
	.word	-1,.L112,0,.L377-.L112
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ReadAll')
	.sect	'.debug_loc'
.L97:
	.word	-1,.L98,0,.L366-.L98
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ReadBlock')
	.sect	'.debug_loc'
.L330:
	.word	-1,.L86,0,.L431-.L86
	.half	5
	.byte	144,34,157,32,0
	.word	.L431-.L86,.L435-.L86
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L332:
	.word	-1,.L86,0,.L431-.L86
	.half	1
	.byte	100
	.word	.L432-.L86,.L433-.L86
	.half	1
	.byte	111
	.word	0,0
.L336:
	.word	-1,.L86,.L436-.L86,.L437-.L86
	.half	1
	.byte	98
	.word	.L433-.L86,.L20-.L86
	.half	1
	.byte	111
	.word	0,0
.L85:
	.word	-1,.L86,0,.L329-.L86
	.half	2
	.byte	138,0
	.word	0,0
.L333:
	.word	-1,.L86,.L432-.L86,.L329-.L86
	.half	5
	.byte	144,36,157,32,0
	.word	.L434-.L86,.L431-.L86
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_RestoreBlockDefaults')
	.sect	'.debug_loc'
.L346:
	.word	-1,.L90,0,.L445-.L90
	.half	5
	.byte	144,34,157,32,0
	.word	.L446-.L90,.L447-.L90
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L347:
	.word	-1,.L90,0,.L445-.L90
	.half	1
	.byte	100
	.word	.L446-.L90,.L448-.L90
	.half	1
	.byte	111
	.word	0,0
.L351:
	.word	-1,.L90,.L450-.L90,.L451-.L90
	.half	1
	.byte	98
	.word	.L448-.L90,.L27-.L90
	.half	1
	.byte	111
	.word	0,0
.L89:
	.word	-1,.L90,0,.L345-.L90
	.half	2
	.byte	138,0
	.word	0,0
.L348:
	.word	-1,.L90,.L449-.L90,.L345-.L90
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_SetBlockLockStatus')
	.sect	'.debug_loc'
.L379:
	.word	-1,.L118,0,.L463-.L118
	.half	5
	.byte	144,34,157,32,0
	.word	.L463-.L118,.L464-.L118
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L380:
	.word	-1,.L118,0,.L463-.L118
	.half	5
	.byte	144,34,157,32,32
	.word	.L465-.L118,.L466-.L118
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L117:
	.word	-1,.L118,0,.L378-.L118
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_SetBlockProtection')
	.sect	'.debug_loc'
.L309:
	.word	-1,.L80,0,.L312-.L80
	.half	5
	.byte	144,34,157,32,0
	.word	.L312-.L80,.L417-.L80
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L314:
	.word	-1,.L80,.L312-.L80,.L418-.L80
	.half	1
	.byte	98
	.word	.L417-.L80,.L308-.L80
	.half	1
	.byte	111
	.word	0,0
.L79:
	.word	-1,.L80,0,.L308-.L80
	.half	2
	.byte	138,0
	.word	0,0
.L310:
	.word	-1,.L80,0,.L312-.L80
	.half	5
	.byte	144,34,157,32,32
	.word	.L419-.L80,.L420-.L80
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L311:
	.word	-1,.L80,.L312-.L80,.L308-.L80
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_SetDataIndex')
	.sect	'.debug_loc'
.L294:
	.word	-1,.L76,0,.L409-.L76
	.half	5
	.byte	144,34,157,32,0
	.word	.L410-.L76,.L292-.L76
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L295:
	.word	-1,.L76,0,.L409-.L76
	.half	5
	.byte	144,34,157,32,32
	.word	.L411-.L76,.L292-.L76
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L299:
	.word	-1,.L76,.L412-.L76,.L3-.L76
	.half	1
	.byte	98
	.word	0,0
.L75:
	.word	-1,.L76,0,.L292-.L76
	.half	2
	.byte	138,0
	.word	0,0
.L296:
	.word	-1,.L76,.L409-.L76,.L292-.L76
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_SetRamBlockStatus')
	.sect	'.debug_loc'
.L324:
	.word	-1,.L84,0,.L424-.L84
	.half	5
	.byte	144,34,157,32,32
	.word	.L426-.L84,.L430-.L84
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L323:
	.word	-1,.L84,0,.L424-.L84
	.half	5
	.byte	144,34,157,32,0
	.word	.L326-.L84,.L427-.L84
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L83:
	.word	-1,.L84,0,.L322-.L84
	.half	2
	.byte	138,0
	.word	0,0
.L328:
	.word	-1,.L84,.L427-.L84,.L428-.L84
	.half	1
	.byte	98
	.word	.L429-.L84,.L15-.L84
	.half	1
	.byte	111
	.word	0,0
.L325:
	.word	-1,.L84,.L425-.L84,.L426-.L84
	.half	5
	.byte	144,39,157,32,32
	.word	.L15-.L84,.L322-.L84
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_WriteAll')
	.sect	'.debug_loc'
.L105:
	.word	-1,.L106,0,.L369-.L106
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_WriteBlock')
	.sect	'.debug_loc'
.L338:
	.word	-1,.L88,0,.L342-.L88
	.half	5
	.byte	144,34,157,32,0
	.word	.L438-.L88,.L439-.L88
	.half	5
	.byte	144,39,157,32,32
	.word	.L443-.L88,.L444-.L88
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L344:
	.word	-1,.L88,.L342-.L88,.L440-.L88
	.half	1
	.byte	98
	.word	.L441-.L88,.L337-.L88
	.half	1
	.byte	111
	.word	.L440-.L88,.L442-.L88
	.half	1
	.byte	100
	.word	0,0
.L340:
	.word	-1,.L88,0,.L342-.L88
	.half	1
	.byte	100
	.word	.L438-.L88,.L337-.L88
	.half	1
	.byte	108
	.word	.L443-.L88,.L444-.L88
	.half	1
	.byte	100
	.word	0,0
.L87:
	.word	-1,.L88,0,.L337-.L88
	.half	2
	.byte	138,0
	.word	0,0
.L341:
	.word	-1,.L88,.L342-.L88,.L337-.L88
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_WriteProtectionChecks')
	.sect	'.debug_loc'
.L385:
	.word	-1,.L120,0,.L383-.L120
	.half	1
	.byte	100
	.word	0,0
.L119:
	.word	-1,.L120,0,.L383-.L120
	.half	2
	.byte	138,0
	.word	0,0
.L386:
	.word	-1,.L120,.L467-.L120,.L383-.L120
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L738:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('NvM_Init')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L72,.L287-.L72
	.sdecl	'.debug_frame',debug,cluster('NvM_SetDataIndex')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L76,.L292-.L76
	.sdecl	'.debug_frame',debug,cluster('NvM_GetDataIndex')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L78,.L300-.L78
	.sdecl	'.debug_frame',debug,cluster('NvM_SetBlockProtection')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L80,.L308-.L80
	.sdecl	'.debug_frame',debug,cluster('NvM_GetErrorStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L82,.L315-.L82
	.sdecl	'.debug_frame',debug,cluster('NvM_SetRamBlockStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L84,.L322-.L84
	.sdecl	'.debug_frame',debug,cluster('NvM_ReadBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L86,.L329-.L86
	.sdecl	'.debug_frame',debug,cluster('NvM_WriteBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L88,.L337-.L88
	.sdecl	'.debug_frame',debug,cluster('NvM_RestoreBlockDefaults')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L90,.L345-.L90
	.sdecl	'.debug_frame',debug,cluster('NvM_EraseNvBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L92,.L352-.L92
	.sdecl	'.debug_frame',debug,cluster('NvM_InvalidateNvBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L94,.L358-.L94
	.sdecl	'.debug_frame',debug,cluster('NvM_CancelJobs')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L96,.L364-.L96
	.sdecl	'.debug_frame',debug,cluster('NvM_ReadAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L98,.L366-.L98
	.sdecl	'.debug_frame',debug,cluster('NvM_KillReadAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L104,.L368-.L104
	.sdecl	'.debug_frame',debug,cluster('NvM_WriteAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L106,.L369-.L106
	.sdecl	'.debug_frame',debug,cluster('NvM_CancelWriteAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L108,.L370-.L108
	.sdecl	'.debug_frame',debug,cluster('NvM_KillWriteAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L110,.L371-.L110
	.sdecl	'.debug_frame',debug,cluster('NvM_MainFunction')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L112,.L377-.L112
	.sdecl	'.debug_frame',debug,cluster('NvM_JobEndNotification')
	.sect	'.debug_frame'
	.word	24
	.word	.L738,.L114,.L381-.L114
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_JobErrorNotification')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L116,.L382-.L116
	.sdecl	'.debug_frame',debug,cluster('NvM_SetBlockLockStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L118,.L378-.L118
	.sdecl	'.debug_frame',debug,cluster('NvM_WriteProtectionChecks')
	.sect	'.debug_frame'
	.word	24
	.word	.L738,.L120,.L383-.L120
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_CheckBlockType')
	.sect	'.debug_frame'
	.word	24
	.word	.L738,.L122,.L387-.L122
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_CheckAddress')
	.sect	'.debug_frame'
	.word	24
	.word	.L738,.L124,.L390-.L124
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_CheckBlockId')
	.sect	'.debug_frame'
	.word	24
	.word	.L738,.L126,.L397-.L126
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_CheckBlockPending')
	.sect	'.debug_frame'
	.word	12
	.word	.L738,.L128,.L400-.L128
	.sdecl	'.debug_frame',debug,cluster('NvM_GetMngmtAreaPtr')
	.sect	'.debug_frame'
	.word	24
	.word	.L738,.L130,.L405-.L130
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L739:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L739,.L74,.L279-.L74
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L739,.L100,.L274-.L100
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L739,.L102,.L284-.L102
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\eeprom\NvM\NvM.c	  1926  
; ..\eeprom\NvM\NvM.c	  1927  
; ..\eeprom\NvM\NvM.c	  1928  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM.c	  1929    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM.c	  1930  
; ..\eeprom\NvM\NvM.c	  1931  /* Justification for module specific MISRA deviations: 
; ..\eeprom\NvM\NvM.c	  1932  
; ..\eeprom\NvM\NvM.c	  1933  MD_NvM_8.9:
; ..\eeprom\NvM\NvM.c	  1934    Reason: Query function table logically belongs to the NvM_Qry. Due to architectural decisions this table shall stay
; ..\eeprom\NvM\NvM.c	  1935            here for now.
; ..\eeprom\NvM\NvM.c	  1936    Risk: Components including the NvM_Qry.h file could directly call queries which then interfere with the intrinsic
; ..\eeprom\NvM\NvM.c	  1937          behavior of the component.
; ..\eeprom\NvM\NvM.c	  1938    Prevention: A code review should ensure that usage of this table is done correctly.
; ..\eeprom\NvM\NvM.c	  1939  
; ..\eeprom\NvM\NvM.c	  1940  MD_NvM_8.9_CrcHandlerTable
; ..\eeprom\NvM\NvM.c	  1941    Reason: The array NvM_CrcHandlerTable_at stores all possible CRC handlers, the implementation simply uses the configured
; ..\eeprom\NvM\NvM.c	  1942            CRC type as an index into the table to use the correct CRC handler.
; ..\eeprom\NvM\NvM.c	  1943            Instead of the table the CRC handler could be set within a if else if statements or similar, but the table avoids
; ..\eeprom\NvM\NvM.c	  1944            decisions within the code, but costs a bit memory.
; ..\eeprom\NvM\NvM.c	  1945    Risk: No risk.
; ..\eeprom\NvM\NvM.c	  1946    Prevention: No prevention, component tests and reviews ensure functionality.
; ..\eeprom\NvM\NvM.c	  1947  
; ..\eeprom\NvM\NvM.c	  1948  MD_NvM_8.9_TestBuffer
; ..\eeprom\NvM\NvM.c	  1949    Reason: To check the readability of a (redundant) NvM block, NvM uses a single byte NvM_TestBuffer_u8 - during a read and
; ..\eeprom\NvM\NvM.c	  1950            RepairRedundantBlocks. In case the RepairRedundantBlocks feature is disabled, the buffer will only be accessed once.
; ..\eeprom\NvM\NvM.c	  1951    Risk: No risk.
; ..\eeprom\NvM\NvM.c	  1952    Prevention: No prevention, component tests and reviews ensure functionality.
; ..\eeprom\NvM\NvM.c	  1953  
; ..\eeprom\NvM\NvM.c	  1954  MD_NvM_8.9_StateTable
; ..\eeprom\NvM\NvM.c	  1955    Reason: The state descriptor table holds the complete NvM state machine - all states. NvM_Fsm is the only function
; ..\eeprom\NvM\NvM.c	  1956            accessing the state table since the function is the one moving the NvM from one state to the next and so on.
; ..\eeprom\NvM\NvM.c	  1957            NvM is not allowed to access the table from another sub modules and/ or functions.
; ..\eeprom\NvM\NvM.c	  1958    Risk: No risk.
; ..\eeprom\NvM\NvM.c	  1959    Prevention: No prevention, component tests and reviews ensure functionality.
; ..\eeprom\NvM\NvM.c	  1960  
; ..\eeprom\NvM\NvM.c	  1961  MD_NvM_8.13_NoCrcHandler
; ..\eeprom\NvM\NvM.c	  1962    Reason: No Crc handler, used as dummies to avoid differing between CRC and no CRC, has to match the same signature like "real"
; ..\eeprom\NvM\NvM.c	  1963          Crc handlers. Because the No Crc handler does nothing, empty functions, the function parameters remain unused and could
; ..\eeprom\NvM\NvM.c	  1964          be const -> because of the signature the parameter remains non constant.
; ..\eeprom\NvM\NvM.c	  1965    Risk: No risk, the functions are empty, they do not use the parameters.
; ..\eeprom\NvM\NvM.c	  1966    Prevention: No prevention.
; ..\eeprom\NvM\NvM.c	  1967  
; ..\eeprom\NvM\NvM.c	  1968  MD_NvM_11.5_CastLossOfConst:
; ..\eeprom\NvM\NvM.c	  1969    Reason: Cast removing const qualifier is necessary.
; ..\eeprom\NvM\NvM.c	  1970    Risk: The component is able to modify the pointer content which could result in undefined behavior.
; ..\eeprom\NvM\NvM.c	  1971    Prevention: A code review should ensure that the parameters are used correctly.
; ..\eeprom\NvM\NvM.c	  1972  
; ..\eeprom\NvM\NvM.c	  1973  MD_NvM_11.5_CastVoidPtrToObjPtr:
; ..\eeprom\NvM\NvM.c	  1974    Reason: Cast is necessary to call the job-queue API that is processing the request.
; ..\eeprom\NvM\NvM.c	  1975    Risk: Cast into an inappropriate type can lead to unexpected behavior.
; ..\eeprom\NvM\NvM.c	  1976    Prevention: A code review should ensure that the parameters are used correctly.
; ..\eeprom\NvM\NvM.c	  1977  
; ..\eeprom\NvM\NvM.c	  1978  MD_NvM_13.5:
; ..\eeprom\NvM\NvM.c	  1979    Reason: Operands of '&&' or '||' is a helper function that is extracting information out of NvM_CurrentBlockInfo_t.
; ..\eeprom\NvM\NvM.c	  1980            This helps the structure and readability of the code a lot.
; ..\eeprom\NvM\NvM.c	  1981    Risk: None.
; ..\eeprom\NvM\NvM.c	  1982    Prevention: None.
; ..\eeprom\NvM\NvM.c	  1983  
; ..\eeprom\NvM\NvM.c	  1984  MD_NvM_13.5_ReadAllAndKillReadAll:
; ..\eeprom\NvM\NvM.c	  1985    Reason: Finishing the ReadAll request depends on KillReadAll request - if requested the job result shall be
; ..\eeprom\NvM\NvM.c	  1986            set to canceled, otherwise the result reflects the result of the finished ReadAll.
; ..\eeprom\NvM\NvM.c	  1987    Risk: No risk.
; ..\eeprom\NvM\NvM.c	  1988    Prevention: No prevention, reviews and component test ensure functionality.
; ..\eeprom\NvM\NvM.c	  1989  
; ..\eeprom\NvM\NvM.c	  1990  MD_NvM_Dir1.1_CastToVoidPtr:
; ..\eeprom\NvM\NvM.c	  1991    Reason: The used callback is defined with a void-pointer to a buffer. As the used buffer type is uint8 there is no
; ..\eeprom\NvM\NvM.c	  1992            chance to avoid this cast.
; ..\eeprom\NvM\NvM.c	  1993    Risk: None. A pointer-to-object can always be converted safely to a pointer-to-void.
; ..\eeprom\NvM\NvM.c	  1994    Prevention: None.
; ..\eeprom\NvM\NvM.c	  1995  
; ..\eeprom\NvM\NvM.c	  1996  */
; ..\eeprom\NvM\NvM.c	  1997  
; ..\eeprom\NvM\NvM.c	  1998  /* SBSW_JUSTIFICATION_BEGIN
; ..\eeprom\NvM\NvM.c	  1999  
; ..\eeprom\NvM\NvM.c	  2000  \ID SBSW_NvM_AccessBlockManagementArea
; ..\eeprom\NvM\NvM.c	  2001   \DESCRIPTION Write access to the array storing the NvM block management information about each configured block.
; ..\eeprom\NvM\NvM.c	  2002                The required array element is accessed via block Id passed to NvM - each block Id is an array index,
; ..\eeprom\NvM\NvM.c	  2003                or via internal block Id 0. NvM expects the given block Id to be valid.
; ..\eeprom\NvM\NvM.c	  2004   \COUNTERMEASURE \R Correctness of given parameter is checked by NvM only in case development mode is enabled.
; ..\eeprom\NvM\NvM.c	  2005  
; ..\eeprom\NvM\NvM.c	  2006  \ID SBSW_NvM_Access_CurrBlockInfo
; ..\eeprom\NvM\NvM.c	  2007   \DESCRIPTION NvM Write access to NvM internal variable storing information about current block and job. The variable has to be
; ..\eeprom\NvM\NvM.c	  2008                initialized correctly by NvM itself.
; ..\eeprom\NvM\NvM.c	  2009   \COUNTERMEASURE \N The NvM itself is responsible to initialize and access its internal structures correctly. Correct usage is
; ..\eeprom\NvM\NvM.c	  2010                   ensured by tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2011  
; ..\eeprom\NvM\NvM.c	  2012  \ID SBSW_NvM_PtrAccess_PublicAPI
; ..\eeprom\NvM\NvM.c	  2013   \DESCRIPTION Write access to a pointer, which is given to NvM function as parameter. NvM expects the pointer to be valid.
; ..\eeprom\NvM\NvM.c	  2014   \COUNTERMEASURE \R NvM checks the given pointer not to be NULL before using it, but only in case Development Error Detection is enabled.
; ..\eeprom\NvM\NvM.c	  2015  
; ..\eeprom\NvM\NvM.c	  2016  \ID SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo
; ..\eeprom\NvM\NvM.c	  2017   \DESCRIPTION NvM passes its current job parameter to internal functions as pointer. There is no explicit check whether the current job is valid,
; ..\eeprom\NvM\NvM.c	  2018                the internal function is invoked only during job processing.
; ..\eeprom\NvM\NvM.c	  2019   \COUNTERMEASURE \N Actions and queries which use the internal job parameter does not check whether the parameter are valid or not. They shall
; ..\eeprom\NvM\NvM.c	  2020                   only be invoked in case a valid job is currently running. Correct usage is ensured by tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2021  
; ..\eeprom\NvM\NvM.c	  2022  \ID SBSW_NvM_FuncPtrCall_QueryAction
; ..\eeprom\NvM\NvM.c	  2023   \DESCRIPTION All accesses to internal queries and actions are done via function pointer calls. There are no explicit checks whether the
; ..\eeprom\NvM\NvM.c	  2024                passed function pointer is valid or not, this is ensured via correct implementation.
; ..\eeprom\NvM\NvM.c	  2025   \COUNTERMEASURE \N The NvM itself is responsible to initialize and access its internal structures correctly. Correct usage is ensured by tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2026  
; ..\eeprom\NvM\NvM.c	  2027  \ID SBSW_NvM_FuncCall_PtrParam_ParamChecker
; ..\eeprom\NvM\NvM.c	  2028   \DESCRIPTION NvM uses helper functions to check pointer parameter to be valid (development error mode checks). Those functions only check the
; ..\eeprom\NvM\NvM.c	  2029                given pointer to be or not to be NULL and return, they do not use the pointer. Functions expects all other parameter to be valid (checked before invoking)
; ..\eeprom\NvM\NvM.c	  2030   \COUNTERMEASURE \N Functions are only used to check parameter, not to use them. Their correctness is ensured via component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2031  
; ..\eeprom\NvM\NvM.c	  2032  \ID SBSW_NvM_FuncCall_PtrParam_FsmQuery
; ..\eeprom\NvM\NvM.c	  2033   \DESCRIPTION Within the NvM state machine the next actions to execute are determined via queries. Invoked function gets two function pointers to queries
; ..\eeprom\NvM\NvM.c	  2034                stored in an array, checks their results and return accordingly. The function expects a valid parameter to be able to call both queries.
; ..\eeprom\NvM\NvM.c	  2035   \COUNTERMEASURE \N The code correctness is ensured via NvM state machine structure and is not tested explicitly. Current tests and
; ..\eeprom\NvM\NvM.c	  2036                   code reviews shall confirm the structure correctness.
; ..\eeprom\NvM\NvM.c	  2037  
; ..\eeprom\NvM\NvM.c	  2038  \ID SBSW_NvM_FuncCall_PtrParam_FsmAction
; ..\eeprom\NvM\NvM.c	  2039   \DESCRIPTION To execute actions corresponding function is invoked with an pointer parameter, pointing to an array storing both action pointers (function pointers).
; ..\eeprom\NvM\NvM.c	  2040                The function expects the given pointer and included function pointers to be valid and does not check them explicitly.
; ..\eeprom\NvM\NvM.c	  2041   \COUNTERMEASURE \N The code correctness is ensured via NvM state machine structure and is not tested explicitly. Current tests and
; ..\eeprom\NvM\NvM.c	  2042                   state machine reviews shall confirm the structure correctness.
; ..\eeprom\NvM\NvM.c	  2043  
; ..\eeprom\NvM\NvM.c	  2044  \ID SBSW_NvM_FuncCall_PtrParam_QueueJob
; ..\eeprom\NvM\NvM.c	  2045   \DESCRIPTION NvM uses a queue to store jobs to be processed later. The corresponding function is called from NvM's public APIs with parameter
; ..\eeprom\NvM\NvM.c	  2046                passed to the public function. The queue function expects the parameter are valid - the caller has to check them, which is done
; ..\eeprom\NvM\NvM.c	  2047                in development error mode.
; ..\eeprom\NvM\NvM.c	  2048   \COUNTERMEASURE \R NvM checks the parameter given to a public API to be valid, in case development error mode is enabled.
; ..\eeprom\NvM\NvM.c	  2049  
; ..\eeprom\NvM\NvM.c	  2050  \ID SBSW_NvM_FuncCall_PtrParam_BlockMngmtArea
; ..\eeprom\NvM\NvM.c	  2051   \DESCRIPTION NvM uses helper functions with pointer to according block management data as parameter. The function does not check the pointer correctness,
; ..\eeprom\NvM\NvM.c	  2052                it expects the pointer is valid and uses it. NvM functions which uses the helper function are responsible for invoking with valid parameter.
; ..\eeprom\NvM\NvM.c	  2053   \COUNTERMEASURE \R NvM check the parameter given to a public API to be valid, in case development error mode is enabled and invokes the helper function
; ..\eeprom\NvM\NvM.c	  2054                   only with valid parameter.
; ..\eeprom\NvM\NvM.c	  2055  
; ..\eeprom\NvM\NvM.c	  2056  \ID SBSW_NvM_FuncCall_PtrParam_CrcAsyncJob
; ..\eeprom\NvM\NvM.c	  2057   \DESCRIPTION To process queued CRC jobs with MainFunction calls NvM uses a local CRC job parameter. This parameter is passed to functions as pointer and
; ..\eeprom\NvM\NvM.c	  2058                will be initialized in one and used in another functions. NvM is responsible for invoking the function in correct order.
; ..\eeprom\NvM\NvM.c	  2059   \COUNTERMEASURE \N Functions do not check their parameter before using them, they expect the parameter are valid. Correct functionality shall be
; ..\eeprom\NvM\NvM.c	  2060                   checked within reviews and/or component tests.
; ..\eeprom\NvM\NvM.c	  2061  
; ..\eeprom\NvM\NvM.c	  2062  \ID SBSW_NvM_FuncCall_PtrParam_MemIf
; ..\eeprom\NvM\NvM.c	  2063   \DESCRIPTION NvM uses MemIf functions to read/ write data from/ to NV RAM. NvM is responsible to provide valid parameter as identifier or buffers.
; ..\eeprom\NvM\NvM.c	  2064   \COUNTERMEASURE \T Correct NvM behavior is checked during component tests. Also code reviews shall ensure correct invoking of the MemIf module.
; ..\eeprom\NvM\NvM.c	  2065  
; ..\eeprom\NvM\NvM.c	  2066  \ID SBSW_NvM_FuncCall_PtrParam_Csm
; ..\eeprom\NvM\NvM.c	  2067   \DESCRIPTION NvM invokes the CSM encrypt and decrypt APIs, of which the signature requires pointers. NvM is responsible to provide valid parameters/ buffers
; ..\eeprom\NvM\NvM.c	  2068                and pass them to the CSM.
; ..\eeprom\NvM\NvM.c	  2069   \COUNTERMEASURE \T The following test cases ensure the correct pointers: TCASE-1162566, TCASE-1162565, TCASE-1162561, TCASE-1162562, TCASE-1164959,
; ..\eeprom\NvM\NvM.c	  2070                   TCASE-1164960, TCASE-1164957, TCASE-1164958. Other test cases verify correct behavior too (implicitly or explicitly).
; ..\eeprom\NvM\NvM.c	  2071  
; ..\eeprom\NvM\NvM.c	  2072  \ID SBSW_NvM_AccessCrcQueue
; ..\eeprom\NvM\NvM.c	  2073   \DESCRIPTION NvM uses a CRC queue to store CRC calculation jobs to be processed later. The queue is only available if SetRamBlockStatus API is enabled
; ..\eeprom\NvM\NvM.c	  2074                and at least one block is configured with Calc RAM CRC. To access the queue NvM calculates element indexes.
; ..\eeprom\NvM\NvM.c	  2075   \COUNTERMEASURE \T Correct NvM CRC queue behavior is checked during component tests. Code reviews shall ensure correct behavior, too.
; ..\eeprom\NvM\NvM.c	  2076  
; ..\eeprom\NvM\NvM.c	  2077  \ID SBSW_NvM_AccessCrcJobPtr
; ..\eeprom\NvM\NvM.c	  2078   \DESCRIPTION To setup and process a CRC job NvM uses a job structure storing all required parameter. This structure is passed to different functions
; ..\eeprom\NvM\NvM.c	  2079                as pointer parameter - all functions assume the pointer is valid and uses it.
; ..\eeprom\NvM\NvM.c	  2080   \COUNTERMEASURE \T Correct NvM CRC job usage is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2081  
; ..\eeprom\NvM\NvM.c	  2082  \ID SBSW_NvM_FuncPtrCall_CrcHandler
; ..\eeprom\NvM\NvM.c	  2083   \DESCRIPTION To calculate, compare or copy CRC values NvM uses CRC helper functions - for each CRC type there are exactly 3 functions.
; ..\eeprom\NvM\NvM.c	  2084                These 3 functions are stored in one CRC specific handler, which is passed to different functions as pointer. Functions which get a CRC handler
; ..\eeprom\NvM\NvM.c	  2085                as pointer parameter assumes it is valid and work with it.
; ..\eeprom\NvM\NvM.c	  2086   \COUNTERMEASURE \T Correct NvM CRC job usage is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2087  
; ..\eeprom\NvM\NvM.c	  2088  \ID SBSW_NvM_AccessArray_BlockIdInCrc
; ..\eeprom\NvM\NvM.c	  2089   \DESCRIPTION There is a special feature NvM uses to ensure the data delivered by underlying modules fits to requested NvM block - it calculate the NvM block Id
; ..\eeprom\NvM\NvM.c	  2090                and current data index into the CRC. To do this the block Id and the data index are stored in an extra array and the NvM invokes the CRC module
; ..\eeprom\NvM\NvM.c	  2091                with this array to calculate CRC for the data. The array is exactly 3 bytes long and specific function local.
; ..\eeprom\NvM\NvM.c	  2092   \COUNTERMEASURE \T Correct buffer usage is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2093  
; ..\eeprom\NvM\NvM.c	  2094  \ID SBSW_NvM_AccessPtr_CrcReassignBuffer
; ..\eeprom\NvM\NvM.c	  2095   \DESCRIPTION In case the internal CRC buffer is enabled, NvM uses internal buffer to store the block CRC. During CRC job setup pointer to this buffer
; ..\eeprom\NvM\NvM.c	  2096                will be stored in CRC job to be able to fill it directly.
; ..\eeprom\NvM\NvM.c	  2097   \COUNTERMEASURE \T Correct buffer usage is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2098  
; ..\eeprom\NvM\NvM.c	  2099  \ID SBSW_NvM_AccessJobQueue
; ..\eeprom\NvM\NvM.c	  2100   \DESCRIPTION To store NvM jobs to be processed later asynchronously NvM uses a queue with configured length. The queue is an array storing all queued jobs. NvM accesses
; ..\eeprom\NvM\NvM.c	  2101                the queue via calculated indexes. The index calculations has to be correct to ensure correct array accesses.
; ..\eeprom\NvM\NvM.c	  2102   \COUNTERMEASURE \T Correct job queue behavior is checked during component tests and code review.
; ..\eeprom\NvM\NvM.c	  2103  
; ..\eeprom\NvM\NvM.c	  2104  \ID SBSW_NvM_FuncCall_PtrParam_Queue
; ..\eeprom\NvM\NvM.c	  2105   \DESCRIPTION NvM job queue invokes its internal helper functions with pointer parameter. These are always valid when passing as parameter,
; ..\eeprom\NvM\NvM.c	  2106                invoked functions does not check them.
; ..\eeprom\NvM\NvM.c	  2107   \COUNTERMEASURE \T Correct job queue behavior is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2108  
; ..\eeprom\NvM\NvM.c	  2109  \ID SBSW_NvM_AccessPtr_QueueIndex
; ..\eeprom\NvM\NvM.c	  2110   \DESCRIPTION Because the NvM job queue actually is an array NvM uses indexes to access the required queue element. Some functions
; ..\eeprom\NvM\NvM.c	  2111                require pointers to those indexes as parameter - in this case the caller is responsible for providing valid pointers
; ..\eeprom\NvM\NvM.c	  2112                storing valid indexes, functions does not check the given parameter.
; ..\eeprom\NvM\NvM.c	  2113   \COUNTERMEASURE \T Correct job queue behavior is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2114  
; ..\eeprom\NvM\NvM.c	  2115  \ID SBSW_NvM_AccessPtr_UsedQueue
; ..\eeprom\NvM\NvM.c	  2116   \DESCRIPTION In case job prioritization is enabled NvM uses two different queues: normal and high priority queue. In some situations we have to check
; ..\eeprom\NvM\NvM.c	  2117                both queues - this is done via queue pointer. Pointer is initialized with one queue, then the second queue is checked, whether it shall be used
; ..\eeprom\NvM\NvM.c	  2118                and the pointer will be reassigned or not. No matter how the pointer always points to a queue and can be used.
; ..\eeprom\NvM\NvM.c	  2119   \COUNTERMEASURE \T Correct job queue behavior is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2120  
; ..\eeprom\NvM\NvM.c	  2121  \ID SBSW_NvM_FuncPtrCall_UserCallbacks
; ..\eeprom\NvM\NvM.c	  2122   \DESCRIPTION NvM stores all user callbacks in function pointers and invokes them depending on callback type. NvM does not check whether the callback
; ..\eeprom\NvM\NvM.c	  2123                functions are valid, it assumes the configuration is correct.
; ..\eeprom\NvM\NvM.c	  2124   \COUNTERMEASURE \N User is responsible to provide valid functions to be called as callbacks. To avoid NULL function pointer there are checks within
; ..\eeprom\NvM\NvM.c	  2125                   configuration tool. NvM does not invoke disabled callbacks (function pointers are NULL and corresponding flags are disabled)
; ..\eeprom\NvM\NvM.c	  2126  
; ..\eeprom\NvM\NvM.c	  2127  \ID SBSW_NvM_FuncCall_CrcModule
; ..\eeprom\NvM\NvM.c	  2128   \DESCRIPTION To calculate a NvM block CRC NvM uses CRC module functions. They are always invoked with valid parameters from NvM CRC job.
; ..\eeprom\NvM\NvM.c	  2129   \COUNTERMEASURE \T Correct NvM behavior is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2130  
; ..\eeprom\NvM\NvM.c	  2131  \ID SBSW_NvM_AccessArray_CrcBuffers
; ..\eeprom\NvM\NvM.c	  2132   \DESCRIPTION NvM uses Crc buffers to store current Crc values to be able to compare against Crcs from NV RAM and/or to add the Crc to data.
; ..\eeprom\NvM\NvM.c	  2133                Since this is NvM internal handling, NvM is responsible for correct pointer and array usage.
; ..\eeprom\NvM\NvM.c	  2134   \COUNTERMEASURE \N The NvM itself is responsible to initialize and access its internal structures correctly. Correctness is ensured by
; ..\eeprom\NvM\NvM.c	  2135                   component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2136  
; ..\eeprom\NvM\NvM.c	  2137  \ID SBSW_NvM_AccessPtr_CrcValue
; ..\eeprom\NvM\NvM.c	  2138   \DESCRIPTION NvM internal CRC job includes a buffer to store the calculated CRC in. This buffer is always valid after the job was setup successfully
; ..\eeprom\NvM\NvM.c	  2139                and the calculated CRC can be store in it.
; ..\eeprom\NvM\NvM.c	  2140   \COUNTERMEASURE \T Correct NvM behavior is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2141  
; ..\eeprom\NvM\NvM.c	  2142  \ID SBSW_NvM_AccessArray_InternalCopyData
; ..\eeprom\NvM\NvM.c	  2143   \DESCRIPTION In some situations NvM has to copy data from one to another buffer. This is done internally using internal or job specific buffers
; ..\eeprom\NvM\NvM.c	  2144                and values as buffer length. Corresponding copy function assumes all parameter are valid and uses them. The caller is responsible
; ..\eeprom\NvM\NvM.c	  2145                for providing valid values.
; ..\eeprom\NvM\NvM.c	  2146   \COUNTERMEASURE \T Correct NvM behavior is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2147  
; ..\eeprom\NvM\NvM.c	  2148  \ID SBSW_NvM_FuncCall_PtrParam_InternalCopyData
; ..\eeprom\NvM\NvM.c	  2149   \DESCRIPTION To copy data NvM invokes the internal copy function with corresponding buffers and values. Before invoking the function it is checked whether
; ..\eeprom\NvM\NvM.c	  2150                copy is possible or not. Invoked copy function does not check parameter validity.
; ..\eeprom\NvM\NvM.c	  2151   \COUNTERMEASURE \T Correct NvM behavior is checked during component tests and code reviews.
; ..\eeprom\NvM\NvM.c	  2152  
; ..\eeprom\NvM\NvM.c	  2153  SBSW_JUSTIFICATION_END */
; ..\eeprom\NvM\NvM.c	  2154  
; ..\eeprom\NvM\NvM.c	  2155  /* COV_JUSTIFICATION_BEGIN
; ..\eeprom\NvM\NvM.c	  2156  
; ..\eeprom\NvM\NvM.c	  2157  CODE COVERAGE JUSTIFICATIONS
; ..\eeprom\NvM\NvM.c	  2158  
; ..\eeprom\NvM\NvM.c	  2159  \ID COV_NVM_MISRA
; ..\eeprom\NvM\NvM.c	  2160   \ACCEPT TX
; ..\eeprom\NvM\NvM.c	  2161   \REASON COV_MSR_MISRA
; ..\eeprom\NvM\NvM.c	  2162  
; ..\eeprom\NvM\NvM.c	  2163  \ID COV_NVM_COVEREDINOTHERCFG
; ..\eeprom\NvM\NvM.c	  2164   \ACCEPT TX
; ..\eeprom\NvM\NvM.c	  2165   \ACCEPT TX tx tx
; ..\eeprom\NvM\NvM.c	  2166   \ACCEPT TF tf tf xf
; ..\eeprom\NvM\NvM.c	  2167   \REASON Some configurations do not provide all functionalities required to go through all code branches. But the correctness of the affected code
; ..\eeprom\NvM\NvM.c	  2168           is ensured via tests in other configurations, which covers the code completely.
; ..\eeprom\NvM\NvM.c	  2169  
; ..\eeprom\NvM\NvM.c	  2170  \ID COV_NVM_NOCRCDUMMYHANDLER
; ..\eeprom\NvM\NvM.c	  2171   \ACCEPT X
; ..\eeprom\NvM\NvM.c	  2172   \REASON All NvM CRC handlers have the same structure: they provide a function to calculate, compare and copy data. For blocks
; ..\eeprom\NvM\NvM.c	  2173           without CRC the NvM uses a dummy CRC handler, which simply does nothing, but exists to simplify CRC handling: NvM does not have to
; ..\eeprom\NvM\NvM.c	  2174           check whether the block has a CRC each time, the assigned CRC handler does it. The uncovered function...
; ..\eeprom\NvM\NvM.c	  2175  
; ..\eeprom\NvM\NvM.c	  2176  \ID COV_NVM_KILLWRITEALL
; ..\eeprom\NvM\NvM.c	  2177   \ACCEPT TF tf tx
; ..\eeprom\NvM\NvM.c	  2178   \ACCEPT TX
; ..\eeprom\NvM\NvM.c	  2179   \ACCEPT XF
; ..\eeprom\NvM\NvM.c	  2180   \ACCEPT XF tf xf
; ..\eeprom\NvM\NvM.c	  2181   \REASON All affected code parts belong to NvM_KillWriteAll handling and are currently not testable (NvM processing is not interruptible on required
; ..\eeprom\NvM\NvM.c	  2182           points). There are two not covered situations:
; ..\eeprom\NvM\NvM.c	  2183           1. NvM isn't processing any block yet or any more (not started or nearly done NvM_WriteAll)
; ..\eeprom\NvM\NvM.c	  2184           2. NvM is processing a block and shall abort directly before or after passing the block to underlying modules
; ..\eeprom\NvM\NvM.c	  2185  
; ..\eeprom\NvM\NvM.c	  2186  \ID COV_NVM_APICFGCLASS
; ..\eeprom\NvM\NvM.c	  2187   \ACCEPT TX tf tx
; ..\eeprom\NvM\NvM.c	  2188   \ACCEPT TF tf tx
; ..\eeprom\NvM\NvM.c	  2189   \ACCEPT TF tx tf
; ..\eeprom\NvM\NvM.c	  2190   \ACCEPT TF tx tf tf
; ..\eeprom\NvM\NvM.c	  2191   \ACCEPT XF
; ..\eeprom\NvM\NvM.c	  2192   \REASON NvM supports three API configuration classes: 3 includes all functionality, 2 removes some functionality and 1 is the smallest
; ..\eeprom\NvM\NvM.c	  2193           NvM functionality with only multi block requests. Because of the missing functionalities it is not possible to cover all
; ..\eeprom\NvM\NvM.c	  2194           branches. Also some NvM block configuration does not make sense here - e.g. block without permanent RAM will not be processed
; ..\eeprom\NvM\NvM.c	  2195           and are responsible for uncovered code.
; ..\eeprom\NvM\NvM.c	  2196  
; ..\eeprom\NvM\NvM.c	  2197  \ID COV_NVM_CSM_RETRY_ZERO
; ..\eeprom\NvM\NvM.c	  2198   \ACCEPT XF tf xf xf
; ..\eeprom\NvM\NvM.c	  2199   \REASON NvM supports a configurable amount of retry mechanisms. It is possible to configure zero retries to have a
; ..\eeprom\NvM\NvM.c	  2200           'no-retry' behavior. It is necessary to test the implementation to work with this configuration.
; ..\eeprom\NvM\NvM.c	  2201           The actual configuration does not support ReadBlock() and WriteBlock() which would enable to cover the complete
; ..\eeprom\NvM\NvM.c	  2202           boolean expression.
; ..\eeprom\NvM\NvM.c	  2203           The justified code is ensured to have full coverage in CFG1.
; ..\eeprom\NvM\NvM.c	  2204  
; ..\eeprom\NvM\NvM.c	  2205  VARIANT COVERAGE JUSTIFICATIONS
; ..\eeprom\NvM\NvM.c	  2206  
; ..\eeprom\NvM\NvM.c	  2207  \ID COV_NVM_COMPATIBILITY
; ..\eeprom\NvM\NvM.c	  2208   \ACCEPT TX
; ..\eeprom\NvM\NvM.c	  2209   \REASON COV_MSR_COMPATIBILITY
; ..\eeprom\NvM\NvM.c	  2210  
; ..\eeprom\NvM\NvM.c	  2211  
; ..\eeprom\NvM\NvM.c	  2212  COV_JUSTIFICATION_END */
; ..\eeprom\NvM\NvM.c	  2213  
; ..\eeprom\NvM\NvM.c	  2214  
; ..\eeprom\NvM\NvM.c	  2215  /*---- End of File ---------------------------------------------------------*/

	; Module end
