	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc27696a -c99 --dep-file=mcal_src\\.Adc_Ver.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Adc_Ver.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Adc_Ver.src ..\\mcal_src\\Adc_Ver.c"
	.compiler_name		"ctc"
	.name	"Adc_Ver"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmEnterStartGroup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmEnterStartGroup

; ..\mcal_src\Adc_Ver.c	     1  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	     2  **                                                                            **
; ..\mcal_src\Adc_Ver.c	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_src\Adc_Ver.c	     4  **                                                                            **
; ..\mcal_src\Adc_Ver.c	     5  ** All rights reserved.                                                       **
; ..\mcal_src\Adc_Ver.c	     6  **                                                                            **
; ..\mcal_src\Adc_Ver.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Adc_Ver.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Adc_Ver.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Adc_Ver.c	    10  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    11  ********************************************************************************
; ..\mcal_src\Adc_Ver.c	    12  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    13  **   $FILENAME   : Adc_Ver.c $                                                **
; ..\mcal_src\Adc_Ver.c	    14  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    15  **   $CC VERSION : \main\9 $                                                  **
; ..\mcal_src\Adc_Ver.c	    16  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    17  **   $DATE       : 2018-06-08 $                                               **
; ..\mcal_src\Adc_Ver.c	    18  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    19  **   AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Adc_Ver.c	    20  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    21  **   VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Adc_Ver.c	    22  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    23  **   DESCRIPTION : This file contains                                         **
; ..\mcal_src\Adc_Ver.c	    24  **                 - Autosar specific functionality of Adc driver.            **
; ..\mcal_src\Adc_Ver.c	    25  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    26  **   MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Adc_Ver.c	    27  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    28  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	    29  
; ..\mcal_src\Adc_Ver.c	    30  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	    31  **  TRACEABILITY: [cover parentID=DS_NAS_ADC_PR730,DS_AS40X_ADC124]           **
; ..\mcal_src\Adc_Ver.c	    32  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    33  **  DESCRIPTION  : This file contains                                         **
; ..\mcal_src\Adc_Ver.c	    34  **                 - Autosar specific functionality of Adc driver.            **
; ..\mcal_src\Adc_Ver.c	    35  **                                                                            **
; ..\mcal_src\Adc_Ver.c	    36  **  [/cover]                                                                  **
; ..\mcal_src\Adc_Ver.c	    37  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	    38  
; ..\mcal_src\Adc_Ver.c	    39  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	    40  **                      Includes                                              **
; ..\mcal_src\Adc_Ver.c	    41  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	    42  /* this includes Adc.h file and declare the functions defined here  */
; ..\mcal_src\Adc_Ver.c	    43  #include "Adc_Utility.h"
; ..\mcal_src\Adc_Ver.c	    44  /* To use OS call in ADC functions */
; ..\mcal_src\Adc_Ver.c	    45  #include "SchM_Adc.h"
; ..\mcal_src\Adc_Ver.c	    46  
; ..\mcal_src\Adc_Ver.c	    47  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	    48  **                      Imported Compiler Switch Check                        **
; ..\mcal_src\Adc_Ver.c	    49  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	    50  /* Check for definition of the AS Version */
; ..\mcal_src\Adc_Ver.c	    51  #ifndef ADC_AR_RELEASE_MAJOR_VERSION
; ..\mcal_src\Adc_Ver.c	    52    #error "ADC_AR_RELEASE_MAJOR_VERSION is not defined. "
; ..\mcal_src\Adc_Ver.c	    53  #endif
; ..\mcal_src\Adc_Ver.c	    54  
; ..\mcal_src\Adc_Ver.c	    55  #ifndef ADC_AR_RELEASE_MINOR_VERSION
; ..\mcal_src\Adc_Ver.c	    56    #error "ADC_AR_RELEASE_MINOR_VERSION is not defined. "
; ..\mcal_src\Adc_Ver.c	    57  #endif
; ..\mcal_src\Adc_Ver.c	    58  
; ..\mcal_src\Adc_Ver.c	    59  #ifndef ADC_AR_RELEASE_REVISION_VERSION
; ..\mcal_src\Adc_Ver.c	    60    #error "ADC_AR_RELEASE_REVISION_VERSION is not defined. "
; ..\mcal_src\Adc_Ver.c	    61  #endif
; ..\mcal_src\Adc_Ver.c	    62  
; ..\mcal_src\Adc_Ver.c	    63  #if ( ADC_AR_RELEASE_MAJOR_VERSION != 4U)
; ..\mcal_src\Adc_Ver.c	    64    #error "ADC_AR_RELEASE_MAJOR_VERSION does not match. "
; ..\mcal_src\Adc_Ver.c	    65  #endif
; ..\mcal_src\Adc_Ver.c	    66  
; ..\mcal_src\Adc_Ver.c	    67  #if ( ADC_AR_RELEASE_MINOR_VERSION != 0U )
; ..\mcal_src\Adc_Ver.c	    68    #error "ADC_AR_RELEASE_MINOR_VERSION does not match. "
; ..\mcal_src\Adc_Ver.c	    69  #endif
; ..\mcal_src\Adc_Ver.c	    70  
; ..\mcal_src\Adc_Ver.c	    71  
; ..\mcal_src\Adc_Ver.c	    72  #ifndef ADC_SW_MAJOR_VERSION
; ..\mcal_src\Adc_Ver.c	    73    #error "ADC_SW_MAJOR_VERSION is not defined. "
; ..\mcal_src\Adc_Ver.c	    74  #endif
; ..\mcal_src\Adc_Ver.c	    75  
; ..\mcal_src\Adc_Ver.c	    76  #ifndef ADC_SW_MINOR_VERSION
; ..\mcal_src\Adc_Ver.c	    77    #error "ADC_SW_MINOR_VERSION is not defined. "
; ..\mcal_src\Adc_Ver.c	    78  #endif
; ..\mcal_src\Adc_Ver.c	    79  
; ..\mcal_src\Adc_Ver.c	    80  #ifndef ADC_SW_PATCH_VERSION
; ..\mcal_src\Adc_Ver.c	    81    #error "ADC_SW_PATCH_VERSION is not defined. "
; ..\mcal_src\Adc_Ver.c	    82  #endif
; ..\mcal_src\Adc_Ver.c	    83  
; ..\mcal_src\Adc_Ver.c	    84  /* Check for Correct inclusion of headers */
; ..\mcal_src\Adc_Ver.c	    85  #if ( ADC_SW_MAJOR_VERSION != 3U )
; ..\mcal_src\Adc_Ver.c	    86    #error "ADC_SW_MAJOR_VERSION does not match. "
; ..\mcal_src\Adc_Ver.c	    87  #endif
; ..\mcal_src\Adc_Ver.c	    88  
; ..\mcal_src\Adc_Ver.c	    89  #if ( ADC_SW_MINOR_VERSION != 3U )
; ..\mcal_src\Adc_Ver.c	    90    #error "ADC_SW_MINOR_VERSION does not match. "
; ..\mcal_src\Adc_Ver.c	    91  #endif
; ..\mcal_src\Adc_Ver.c	    92  
; ..\mcal_src\Adc_Ver.c	    93  /*
; ..\mcal_src\Adc_Ver.c	    94    ADC124: Inter Module Checks to avoid integration of incompatible files
; ..\mcal_src\Adc_Ver.c	    95    Its applicable and available for AS4.0 and higher versions
; ..\mcal_src\Adc_Ver.c	    96  */
; ..\mcal_src\Adc_Ver.c	    97  #if (ADC_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Adc_Ver.c	    98  
; ..\mcal_src\Adc_Ver.c	    99  #ifndef DET_AR_RELEASE_MAJOR_VERSION
; ..\mcal_src\Adc_Ver.c	   100    #error "DET_AR_RELEASE_MAJOR_VERSION is not defined. "
; ..\mcal_src\Adc_Ver.c	   101  #endif
; ..\mcal_src\Adc_Ver.c	   102  
; ..\mcal_src\Adc_Ver.c	   103  #ifndef DET_AR_RELEASE_MINOR_VERSION
; ..\mcal_src\Adc_Ver.c	   104    #error "DET_AR_RELEASE_MINOR_VERSION is not defined. "
; ..\mcal_src\Adc_Ver.c	   105  #endif
; ..\mcal_src\Adc_Ver.c	   106  
; ..\mcal_src\Adc_Ver.c	   107  #if (IFX_DET_VERSION_CHECK == STD_ON)
; ..\mcal_src\Adc_Ver.c	   108  
; ..\mcal_src\Adc_Ver.c	   109  #if ( DET_AR_RELEASE_MAJOR_VERSION != 4U )
; ..\mcal_src\Adc_Ver.c	   110    #error "DET_AR_RELEASE_MAJOR_VERSION does not match. "
; ..\mcal_src\Adc_Ver.c	   111  #endif
; ..\mcal_src\Adc_Ver.c	   112  
; ..\mcal_src\Adc_Ver.c	   113  #if ( DET_AR_RELEASE_MINOR_VERSION != 0U )
; ..\mcal_src\Adc_Ver.c	   114    #error "DET_AR_RELEASE_MINOR_VERSION does not match. "
; ..\mcal_src\Adc_Ver.c	   115  #endif
; ..\mcal_src\Adc_Ver.c	   116  
; ..\mcal_src\Adc_Ver.c	   117  #endif /* (IFX_DET_VERSION_CHECK == STD_ON) */
; ..\mcal_src\Adc_Ver.c	   118  
; ..\mcal_src\Adc_Ver.c	   119  #endif /* (ADC_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Adc_Ver.c	   120  
; ..\mcal_src\Adc_Ver.c	   121  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   122  **                      Private Macro Definitions                             **
; ..\mcal_src\Adc_Ver.c	   123  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   124  
; ..\mcal_src\Adc_Ver.c	   125  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   126  **                      Private Type Definitions                              **
; ..\mcal_src\Adc_Ver.c	   127  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   128  
; ..\mcal_src\Adc_Ver.c	   129  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   130  **                      Private Function Declarations                         **
; ..\mcal_src\Adc_Ver.c	   131  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   132  
; ..\mcal_src\Adc_Ver.c	   133  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   134  **                      Global Constant Definitions                           **
; ..\mcal_src\Adc_Ver.c	   135  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   136  
; ..\mcal_src\Adc_Ver.c	   137  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   138  **                      Global Variable Definitions                           **
; ..\mcal_src\Adc_Ver.c	   139  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   140  
; ..\mcal_src\Adc_Ver.c	   141  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   142  **                      Private Constant Definitions                          **
; ..\mcal_src\Adc_Ver.c	   143  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   144  
; ..\mcal_src\Adc_Ver.c	   145  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   146  **                      Private Variable Definitions                          **
; ..\mcal_src\Adc_Ver.c	   147  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   148  
; ..\mcal_src\Adc_Ver.c	   149  #define ADC_START_SEC_CODE
; ..\mcal_src\Adc_Ver.c	   150  #include "MemMap.h"
; ..\mcal_src\Adc_Ver.c	   151  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   152  **                      Global Function Definitions                           **
; ..\mcal_src\Adc_Ver.c	   153  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   154  /* Enable/Disable the use of the function */
; ..\mcal_src\Adc_Ver.c	   155  #if (ADC_ENABLE_START_STOP_GROUP_API == STD_ON)
; ..\mcal_src\Adc_Ver.c	   156  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   157  ** Syntax           : void Adc_lSchmEnterStartGroup                           **
; ..\mcal_src\Adc_Ver.c	   158  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   159  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   160  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   161  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   162  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   163  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   164  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   165  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   166  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   167  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   168  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   169  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   170  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   171  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   172  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   173  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   174  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   175  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   176  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   177  void Adc_lSchmEnterStartGroup(void)
; Function Adc_lSchmEnterStartGroup
.L3:
Adc_lSchmEnterStartGroup:	.type	func

; ..\mcal_src\Adc_Ver.c	   178  {
; ..\mcal_src\Adc_Ver.c	   179    SchM_Enter_Adc_StartGroup();
	j	SchM_Enter_Adc_StartGroup
.L76:
	
__Adc_lSchmEnterStartGroup_function_end:
	.size	Adc_lSchmEnterStartGroup,__Adc_lSchmEnterStartGroup_function_end-Adc_lSchmEnterStartGroup
.L30:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmExitStartGroup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmExitStartGroup

; ..\mcal_src\Adc_Ver.c	   180  }
; ..\mcal_src\Adc_Ver.c	   181  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   182  ** Syntax           : void Adc_lSchmExitStartGroup                            **
; ..\mcal_src\Adc_Ver.c	   183  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   184  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   185  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   186  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   187  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   188  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   189  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   190  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   191  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   192  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   193  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   194  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   195  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   196  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   197  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   198  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   199  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   200  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   201  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   202  void Adc_lSchmExitStartGroup(void)
; Function Adc_lSchmExitStartGroup
.L5:
Adc_lSchmExitStartGroup:	.type	func

; ..\mcal_src\Adc_Ver.c	   203  {
; ..\mcal_src\Adc_Ver.c	   204    SchM_Exit_Adc_StartGroup();
	j	SchM_Exit_Adc_StartGroup
.L77:
	
__Adc_lSchmExitStartGroup_function_end:
	.size	Adc_lSchmExitStartGroup,__Adc_lSchmExitStartGroup_function_end-Adc_lSchmExitStartGroup
.L35:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmEnterStopGroup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmEnterStopGroup

; ..\mcal_src\Adc_Ver.c	   205  }
; ..\mcal_src\Adc_Ver.c	   206  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   207  ** Syntax           : void Adc_lSchmEnterStopGroup                            **
; ..\mcal_src\Adc_Ver.c	   208  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   209  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   210  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   211  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   212  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   213  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   214  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   215  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   216  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   217  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   218  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   219  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   220  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   221  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   222  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   223  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   224  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   225  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   226  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   227  void Adc_lSchmEnterStopGroup(void)
; Function Adc_lSchmEnterStopGroup
.L7:
Adc_lSchmEnterStopGroup:	.type	func

; ..\mcal_src\Adc_Ver.c	   228  {
; ..\mcal_src\Adc_Ver.c	   229    SchM_Enter_Adc_StopGroup();
	j	SchM_Enter_Adc_StopGroup
.L78:
	
__Adc_lSchmEnterStopGroup_function_end:
	.size	Adc_lSchmEnterStopGroup,__Adc_lSchmEnterStopGroup_function_end-Adc_lSchmEnterStopGroup
.L40:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmExitStopGroup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmExitStopGroup

; ..\mcal_src\Adc_Ver.c	   230  }
; ..\mcal_src\Adc_Ver.c	   231  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   232  ** Syntax           : void Adc_lSchmExitStopGroup                             **
; ..\mcal_src\Adc_Ver.c	   233  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   234  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   235  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   236  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   237  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   238  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   239  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   240  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   241  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   242  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   243  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   244  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   245  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   246  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   247  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   248  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   249  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   250  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   251  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   252  void Adc_lSchmExitStopGroup(void)
; Function Adc_lSchmExitStopGroup
.L9:
Adc_lSchmExitStopGroup:	.type	func

; ..\mcal_src\Adc_Ver.c	   253  {
; ..\mcal_src\Adc_Ver.c	   254    SchM_Exit_Adc_StopGroup();
	j	SchM_Exit_Adc_StopGroup
.L79:
	
__Adc_lSchmExitStopGroup_function_end:
	.size	Adc_lSchmExitStopGroup,__Adc_lSchmExitStopGroup_function_end-Adc_lSchmExitStopGroup
.L45:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmEnterGetGrpStatus')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmEnterGetGrpStatus

; ..\mcal_src\Adc_Ver.c	   255  }
; ..\mcal_src\Adc_Ver.c	   256  #endif /* (ADC_ENABLE_START_STOP_GROUP_API == STD_ON) */
; ..\mcal_src\Adc_Ver.c	   257  
; ..\mcal_src\Adc_Ver.c	   258  /* Enable/Disable the use of the function */
; ..\mcal_src\Adc_Ver.c	   259  #if (ADC_HW_TRIGGER_API == STD_ON)
; ..\mcal_src\Adc_Ver.c	   260  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   261  ** Syntax           : void Adc_lSchmEnterEnableHwTrig                         **
; ..\mcal_src\Adc_Ver.c	   262  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   263  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   264  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   265  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   266  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   267  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   268  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   269  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   270  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   271  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   272  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   273  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   274  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   275  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   276  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   277  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   278  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   279  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   280  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   281  void Adc_lSchmEnterEnableHwTrig(void)
; ..\mcal_src\Adc_Ver.c	   282  {
; ..\mcal_src\Adc_Ver.c	   283    SchM_Enter_Adc_EnableHwTrig();
; ..\mcal_src\Adc_Ver.c	   284  }
; ..\mcal_src\Adc_Ver.c	   285  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   286  ** Syntax           : void Adc_lSchmExitEnableHwTrig                          **
; ..\mcal_src\Adc_Ver.c	   287  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   288  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   289  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   290  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   291  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   292  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   293  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   294  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   295  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   296  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   297  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   298  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   299  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   300  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   301  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   302  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   303  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   304  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   305  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   306  void Adc_lSchmExitEnableHwTrig(void)
; ..\mcal_src\Adc_Ver.c	   307  {
; ..\mcal_src\Adc_Ver.c	   308    SchM_Exit_Adc_EnableHwTrig();
; ..\mcal_src\Adc_Ver.c	   309  }
; ..\mcal_src\Adc_Ver.c	   310  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   311  ** Syntax           : void Adc_lSchmEnterDisableHwTrig                        **
; ..\mcal_src\Adc_Ver.c	   312  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   313  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   314  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   315  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   316  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   317  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   318  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   319  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   320  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   321  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   322  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   323  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   324  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   325  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   326  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   327  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   328  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   329  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   330  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   331  void Adc_lSchmEnterDisableHwTrig(void)
; ..\mcal_src\Adc_Ver.c	   332  {
; ..\mcal_src\Adc_Ver.c	   333    SchM_Enter_Adc_DisableHwTrig();
; ..\mcal_src\Adc_Ver.c	   334  }
; ..\mcal_src\Adc_Ver.c	   335  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   336  ** Syntax           : void Adc_lSchmExitDisableHwTrig                         **
; ..\mcal_src\Adc_Ver.c	   337  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   338  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   339  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   340  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   341  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   342  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   343  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   344  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   345  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   346  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   347  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   348  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   349  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   350  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   351  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   352  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   353  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   354  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   355  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   356  void Adc_lSchmExitDisableHwTrig(void)
; ..\mcal_src\Adc_Ver.c	   357  {
; ..\mcal_src\Adc_Ver.c	   358    SchM_Exit_Adc_DisableHwTrig();
; ..\mcal_src\Adc_Ver.c	   359  }
; ..\mcal_src\Adc_Ver.c	   360  #endif /* (ADC_HW_TRIGGER_API == STD_ON) */
; ..\mcal_src\Adc_Ver.c	   361  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   362  ** Syntax           : void Adc_lSchmEnterGetGrpStatus                         **
; ..\mcal_src\Adc_Ver.c	   363  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   364  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   365  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   366  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   367  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   368  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   369  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   370  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   371  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   372  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   373  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   374  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   375  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   376  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   377  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   378  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   379  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   380  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   381  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   382  void Adc_lSchmEnterGetGrpStatus(void)
; Function Adc_lSchmEnterGetGrpStatus
.L11:
Adc_lSchmEnterGetGrpStatus:	.type	func

; ..\mcal_src\Adc_Ver.c	   383  {
; ..\mcal_src\Adc_Ver.c	   384    SchM_Enter_Adc_GetGrpStatus();
	j	SchM_Enter_Adc_GetGrpStatus
.L80:
	
__Adc_lSchmEnterGetGrpStatus_function_end:
	.size	Adc_lSchmEnterGetGrpStatus,__Adc_lSchmEnterGetGrpStatus_function_end-Adc_lSchmEnterGetGrpStatus
.L50:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmExitGetGrpStatus')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmExitGetGrpStatus

; ..\mcal_src\Adc_Ver.c	   385  }
; ..\mcal_src\Adc_Ver.c	   386  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   387  ** Syntax           : void Adc_lSchmExitGetGrpStatus                          **
; ..\mcal_src\Adc_Ver.c	   388  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   389  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   390  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   391  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   392  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   393  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   394  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   395  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   396  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   397  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   398  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   399  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   400  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   401  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   402  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   403  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   404  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   405  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   406  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   407  void Adc_lSchmExitGetGrpStatus(void)
; Function Adc_lSchmExitGetGrpStatus
.L13:
Adc_lSchmExitGetGrpStatus:	.type	func

; ..\mcal_src\Adc_Ver.c	   408  {
; ..\mcal_src\Adc_Ver.c	   409    SchM_Exit_Adc_GetGrpStatus();
	j	SchM_Exit_Adc_GetGrpStatus
.L81:
	
__Adc_lSchmExitGetGrpStatus_function_end:
	.size	Adc_lSchmExitGetGrpStatus,__Adc_lSchmExitGetGrpStatus_function_end-Adc_lSchmExitGetGrpStatus
.L55:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmEnterGetStreamLastPtr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmEnterGetStreamLastPtr

; ..\mcal_src\Adc_Ver.c	   410  }
; ..\mcal_src\Adc_Ver.c	   411  
; ..\mcal_src\Adc_Ver.c	   412  #if (ADC_RESULT_HANDLING_MODE == ADC_AUTOSAR)
; ..\mcal_src\Adc_Ver.c	   413  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   414  ** Syntax           : void Adc_lSchmEnterGetStreamLastPtr                     **
; ..\mcal_src\Adc_Ver.c	   415  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   416  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   417  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   418  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   419  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   420  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   421  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   422  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   423  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   424  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   425  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   426  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   427  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   428  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   429  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   430  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   431  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   432  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   433  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   434  void Adc_lSchmEnterGetStreamLastPtr(void)
; Function Adc_lSchmEnterGetStreamLastPtr
.L15:
Adc_lSchmEnterGetStreamLastPtr:	.type	func

; ..\mcal_src\Adc_Ver.c	   435  {
; ..\mcal_src\Adc_Ver.c	   436    SchM_Enter_Adc_GetStreamLastPtr();
	j	SchM_Enter_Adc_GetStreamLastPtr
.L82:
	
__Adc_lSchmEnterGetStreamLastPtr_function_end:
	.size	Adc_lSchmEnterGetStreamLastPtr,__Adc_lSchmEnterGetStreamLastPtr_function_end-Adc_lSchmEnterGetStreamLastPtr
.L60:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmExitGetStreamLastPtr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmExitGetStreamLastPtr

; ..\mcal_src\Adc_Ver.c	   437  }
; ..\mcal_src\Adc_Ver.c	   438  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   439  ** Syntax           : void Adc_lSchmExitGetStreamLastPtr                      **
; ..\mcal_src\Adc_Ver.c	   440  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   441  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   442  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   443  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   444  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   445  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   446  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   447  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   448  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   449  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   450  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   451  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   452  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   453  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   454  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   455  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   456  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   457  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   458  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   459  void Adc_lSchmExitGetStreamLastPtr(void)
; Function Adc_lSchmExitGetStreamLastPtr
.L17:
Adc_lSchmExitGetStreamLastPtr:	.type	func

; ..\mcal_src\Adc_Ver.c	   460  {
; ..\mcal_src\Adc_Ver.c	   461    SchM_Exit_Adc_GetStreamLastPtr();
	j	SchM_Exit_Adc_GetStreamLastPtr
.L83:
	
__Adc_lSchmExitGetStreamLastPtr_function_end:
	.size	Adc_lSchmExitGetStreamLastPtr,__Adc_lSchmExitGetStreamLastPtr_function_end-Adc_lSchmExitGetStreamLastPtr
.L65:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmEnterReadGroup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmEnterReadGroup

; ..\mcal_src\Adc_Ver.c	   462  }
; ..\mcal_src\Adc_Ver.c	   463  
; ..\mcal_src\Adc_Ver.c	   464  #if (ADC_READ_GROUP_API == STD_ON)
; ..\mcal_src\Adc_Ver.c	   465  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   466  ** Syntax           : void Adc_lSchmEnterReadGroup                            **
; ..\mcal_src\Adc_Ver.c	   467  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   468  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   469  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   470  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   471  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   472  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   473  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   474  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   475  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   476  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   477  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   478  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   479  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   480  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   481  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   482  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   483  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   484  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   485  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   486  void Adc_lSchmEnterReadGroup(void)
; Function Adc_lSchmEnterReadGroup
.L19:
Adc_lSchmEnterReadGroup:	.type	func

; ..\mcal_src\Adc_Ver.c	   487  {
; ..\mcal_src\Adc_Ver.c	   488    SchM_Enter_Adc_ReadGroup();
	j	SchM_Enter_Adc_ReadGroup
.L84:
	
__Adc_lSchmEnterReadGroup_function_end:
	.size	Adc_lSchmEnterReadGroup,__Adc_lSchmEnterReadGroup_function_end-Adc_lSchmEnterReadGroup
.L70:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_lSchmExitReadGroup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_lSchmExitReadGroup

; ..\mcal_src\Adc_Ver.c	   489  }
; ..\mcal_src\Adc_Ver.c	   490  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   491  ** Syntax           : void Adc_lSchmExitReadGroup                             **
; ..\mcal_src\Adc_Ver.c	   492  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   493  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   494  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   495  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   496  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   497  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   498  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   499  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   500  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   501  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   502  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   503  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   504  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   505  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   506  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   507  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   508  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   509  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   510  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   511  void Adc_lSchmExitReadGroup(void)
; Function Adc_lSchmExitReadGroup
.L21:
Adc_lSchmExitReadGroup:	.type	func

; ..\mcal_src\Adc_Ver.c	   512  {
; ..\mcal_src\Adc_Ver.c	   513    SchM_Exit_Adc_ReadGroup();
	j	SchM_Exit_Adc_ReadGroup
.L85:
	
__Adc_lSchmExitReadGroup_function_end:
	.size	Adc_lSchmExitReadGroup,__Adc_lSchmExitReadGroup_function_end-Adc_lSchmExitReadGroup
.L75:
	; End of function
	
	.calls	'Adc_lSchmEnterStartGroup','SchM_Enter_Adc_StartGroup'
	.calls	'Adc_lSchmExitStartGroup','SchM_Exit_Adc_StartGroup'
	.calls	'Adc_lSchmEnterStopGroup','SchM_Enter_Adc_StopGroup'
	.calls	'Adc_lSchmExitStopGroup','SchM_Exit_Adc_StopGroup'
	.calls	'Adc_lSchmEnterGetGrpStatus','SchM_Enter_Adc_GetGrpStatus'
	.calls	'Adc_lSchmExitGetGrpStatus','SchM_Exit_Adc_GetGrpStatus'
	.calls	'Adc_lSchmEnterGetStreamLastPtr','SchM_Enter_Adc_GetStreamLastPtr'
	.calls	'Adc_lSchmExitGetStreamLastPtr','SchM_Exit_Adc_GetStreamLastPtr'
	.calls	'Adc_lSchmEnterReadGroup','SchM_Enter_Adc_ReadGroup'
	.calls	'Adc_lSchmExitReadGroup','SchM_Exit_Adc_ReadGroup'
	.calls	'Adc_lSchmEnterStartGroup','',0
	.calls	'Adc_lSchmExitStartGroup','',0
	.calls	'Adc_lSchmEnterStopGroup','',0
	.calls	'Adc_lSchmExitStopGroup','',0
	.calls	'Adc_lSchmEnterGetGrpStatus','',0
	.calls	'Adc_lSchmExitGetGrpStatus','',0
	.calls	'Adc_lSchmEnterGetStreamLastPtr','',0
	.calls	'Adc_lSchmExitGetStreamLastPtr','',0
	.calls	'Adc_lSchmEnterReadGroup','',0
	.extern	SchM_Enter_Adc_StartGroup
	.extern	SchM_Enter_Adc_StopGroup
	.extern	SchM_Enter_Adc_ReadGroup
	.extern	SchM_Enter_Adc_GetGrpStatus
	.extern	SchM_Enter_Adc_GetStreamLastPtr
	.extern	SchM_Exit_Adc_StartGroup
	.extern	SchM_Exit_Adc_StopGroup
	.extern	SchM_Exit_Adc_ReadGroup
	.extern	SchM_Exit_Adc_GetGrpStatus
	.extern	SchM_Exit_Adc_GetStreamLastPtr
	.calls	'Adc_lSchmExitReadGroup','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L23:
	.word	51765
	.half	3
	.word	.L24
	.byte	4
.L22:
	.byte	1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L25
	.byte	2
	.byte	'SchM_Enter_Adc_StartGroup',0,1,72,13,1,1,1,1,2
	.byte	'SchM_Enter_Adc_StopGroup',0,1,92,13,1,1,1,1,2
	.byte	'SchM_Enter_Adc_ReadGroup',0,1,112,13,1,1,1,1,2
	.byte	'SchM_Enter_Adc_GetGrpStatus',0,1,172,1,13,1,1,1,1,2
	.byte	'SchM_Enter_Adc_GetStreamLastPtr',0,1,192,1,13,1,1,1,1,2
	.byte	'SchM_Exit_Adc_StartGroup',0,1,184,2,13,1,1,1,1,2
	.byte	'SchM_Exit_Adc_StopGroup',0,1,204,2,13,1,1,1,1,2
	.byte	'SchM_Exit_Adc_ReadGroup',0,1,224,2,13,1,1,1,1,2
	.byte	'SchM_Exit_Adc_GetGrpStatus',0,1,156,3,13,1,1,1,1,2
	.byte	'SchM_Exit_Adc_GetStreamLastPtr',0,1,176,3,13,1,1,1,1,3
	.byte	'void',0,4
	.word	530
	.byte	5
	.byte	'__prof_adm',0,2,1,1
	.word	536
	.byte	6,1,4
	.word	560
	.byte	5
	.byte	'__codeptr',0,2,1,1
	.word	562
	.byte	7
	.byte	'_Ifx_VADC_ACCEN0_Bits',0,3,49,16,4,8
	.byte	'unsigned char',0,1,8,9
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	612
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	612
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	612
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_ACCEN0_Bits',0,3,83,3
	.word	585
	.byte	7
	.byte	'_Ifx_VADC_ACCPROT0_Bits',0,3,86,16,4,9
	.byte	'APC0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'APC1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'APC2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'APC3',0,1
	.word	612
	.byte	1,4,2,35,0,8
	.byte	'unsigned short int',0,2,7,9
	.byte	'reserved_4',0,2
	.word	1254
	.byte	11,1,2,35,0,9
	.byte	'APEM',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'API0',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'API1',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'API2',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'API3',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,2
	.word	1254
	.byte	11,1,2,35,2,9
	.byte	'APGC',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_ACCPROT0_Bits',0,3,100,3
	.word	1161
	.byte	7
	.byte	'_Ifx_VADC_ACCPROT1_Bits',0,3,103,16,4,9
	.byte	'APS0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'APS1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'APS2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'APS3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,2
	.word	1254
	.byte	11,1,2,35,0,9
	.byte	'APTF',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'APR0',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'APR1',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'APR2',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'APR3',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,2
	.word	1254
	.byte	12,0,2,35,2,0,5
	.byte	'Ifx_VADC_ACCPROT1_Bits',0,3,116,3
	.word	1449
	.byte	7
	.byte	'_Ifx_VADC_BRSCTRL_Bits',0,3,119,16,4,9
	.byte	'SRCRESREG',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'XTSEL',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'XTLVL',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'XTMODE',0,1
	.word	612
	.byte	2,1,2,35,1,9
	.byte	'XTWC',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'GTSEL',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'GTLVL',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,9
	.byte	'GTWC',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_VADC_BRSCTRL_Bits',0,3,132,1,3
	.word	1699
	.byte	7
	.byte	'_Ifx_VADC_BRSMR_Bits',0,3,135,1,16,4,9
	.byte	'ENGT',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'ENTR',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'ENSI',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'SCAN',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'LDM',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'REQGT',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'CLRPND',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'LDEV',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'reserved_10',0,1
	.word	612
	.byte	6,0,2,35,1,9
	.byte	'RPTDIS',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,2
	.word	1254
	.byte	15,0,2,35,2,0,5
	.byte	'Ifx_VADC_BRSMR_Bits',0,3,149,1,3
	.word	1966
	.byte	7
	.byte	'_Ifx_VADC_BRSPND_Bits',0,3,152,1,16,4,8
	.byte	'unsigned int',0,4,7,9
	.byte	'CHPNDGy',0,4
	.word	2267
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_VADC_BRSPND_Bits',0,3,155,1,3
	.word	2239
	.byte	7
	.byte	'_Ifx_VADC_BRSSEL_Bits',0,3,158,1,16,4,9
	.byte	'CHSELGy',0,4
	.word	2267
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_VADC_BRSSEL_Bits',0,3,161,1,3
	.word	2333
	.byte	7
	.byte	'_Ifx_VADC_CLC_Bits',0,3,164,1,16,4,9
	.byte	'DISR',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'DISS',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'EDIS',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_VADC_CLC_Bits',0,3,171,1,3
	.word	2411
	.byte	7
	.byte	'_Ifx_VADC_EMUXSEL_Bits',0,3,174,1,16,4,9
	.byte	'EMUXGRP0',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'EMUXGRP1',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	2267
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_VADC_EMUXSEL_Bits',0,3,179,1,3
	.word	2556
	.byte	7
	.byte	'_Ifx_VADC_G_ALIAS_Bits',0,3,182,1,16,4,9
	.byte	'ALIAS0',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	612
	.byte	3,0,2,35,0,9
	.byte	'ALIAS1',0,1
	.word	612
	.byte	5,3,2,35,1,9
	.byte	'reserved_13',0,4
	.word	2267
	.byte	19,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_ALIAS_Bits',0,3,188,1,3
	.word	2679
	.byte	7
	.byte	'_Ifx_VADC_G_ARBCFG_Bits',0,3,191,1,16,4,9
	.byte	'ANONC',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'ARBRND',0,1
	.word	612
	.byte	2,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'ARBM',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'ANONS',0,1
	.word	612
	.byte	2,6,2,35,2,9
	.byte	'CSRC',0,1
	.word	612
	.byte	2,4,2,35,2,9
	.byte	'CHNR',0,2
	.word	1254
	.byte	5,7,2,35,2,9
	.byte	'SYNRUN',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	2,4,2,35,3,9
	.byte	'CAL',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'CALS',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'BUSY',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'SAMPLE',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_ARBCFG_Bits',0,3,208,1,3
	.word	2821
	.byte	7
	.byte	'_Ifx_VADC_G_ARBPR_Bits',0,3,211,1,16,4,9
	.byte	'PRIO0',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'CSM0',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'PRIO1',0,1
	.word	612
	.byte	2,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'CSM1',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'PRIO2',0,1
	.word	612
	.byte	2,6,2,35,1,9
	.byte	'reserved_10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'CSM2',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'PRIO3',0,1
	.word	612
	.byte	2,2,2,35,1,9
	.byte	'reserved_14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'CSM3',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	612
	.byte	8,0,2,35,2,9
	.byte	'ASEN0',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'ASEN1',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'ASEN2',0,1
	.word	612
	.byte	1,5,2,35,3,9
	.byte	'ASEN3',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_ARBPR_Bits',0,3,231,1,3
	.word	3156
	.byte	7
	.byte	'_Ifx_VADC_G_ASCTRL_Bits',0,3,234,1,16,4,9
	.byte	'SRCRESREG',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'XTSEL',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'XTLVL',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'XTMODE',0,1
	.word	612
	.byte	2,1,2,35,1,9
	.byte	'XTWC',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'GTSEL',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'GTLVL',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,9
	.byte	'GTWC',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'TMEN',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	612
	.byte	2,1,2,35,3,9
	.byte	'TMWC',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_ASCTRL_Bits',0,3,250,1,3
	.word	3553
	.byte	7
	.byte	'_Ifx_VADC_G_ASMR_Bits',0,3,253,1,16,4,9
	.byte	'ENGT',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'ENTR',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'ENSI',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'SCAN',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'LDM',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'REQGT',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'CLRPND',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'LDEV',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'reserved_10',0,1
	.word	612
	.byte	6,0,2,35,1,9
	.byte	'RPTDIS',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,2
	.word	1254
	.byte	15,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_ASMR_Bits',0,3,139,2,3
	.word	3878
	.byte	7
	.byte	'_Ifx_VADC_G_ASPND_Bits',0,3,142,2,16,4,9
	.byte	'CHPND',0,4
	.word	2267
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_ASPND_Bits',0,3,145,2,3
	.word	4153
	.byte	7
	.byte	'_Ifx_VADC_G_ASSEL_Bits',0,3,148,2,16,4,9
	.byte	'CHSEL',0,4
	.word	2267
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_ASSEL_Bits',0,3,151,2,3
	.word	4231
	.byte	7
	.byte	'_Ifx_VADC_G_BFL_Bits',0,3,154,2,16,4,9
	.byte	'BFL0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'BFL1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'BFL2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'BFL3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'BFA0',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'BFA1',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'BFA2',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'BFA3',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'BFI0',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'BFI1',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'BFI2',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'BFI3',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,2
	.word	1254
	.byte	12,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_BFL_Bits',0,3,171,2,3
	.word	4309
	.byte	7
	.byte	'_Ifx_VADC_G_BFLC_Bits',0,3,174,2,16,4,9
	.byte	'BFM0',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'BFM1',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'BFM2',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'BFM3',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_BFLC_Bits',0,3,181,2,3
	.word	4626
	.byte	7
	.byte	'_Ifx_VADC_G_BFLNP_Bits',0,3,184,2,16,4,9
	.byte	'BFL0NP',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'BFL1NP',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'BFL2NP',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'BFL3NP',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_BFLNP_Bits',0,3,191,2,3
	.word	4772
	.byte	7
	.byte	'_Ifx_VADC_G_BFLS_Bits',0,3,194,2,16,4,9
	.byte	'BFC0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'BFC1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'BFC2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'BFC3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,2
	.word	1254
	.byte	12,0,2,35,0,9
	.byte	'BFS0',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'BFS1',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'BFS2',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'BFS3',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,2
	.word	1254
	.byte	12,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_BFLS_Bits',0,3,206,2,3
	.word	4928
	.byte	7
	.byte	'_Ifx_VADC_G_BOUND_Bits',0,3,209,2,16,4,9
	.byte	'BOUNDARY0',0,2
	.word	1254
	.byte	12,4,2,35,0,9
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'BOUNDARY1',0,2
	.word	1254
	.byte	12,4,2,35,2,9
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_BOUND_Bits',0,3,215,2,3
	.word	5160
	.byte	7
	.byte	'_Ifx_VADC_G_CEFCLR_Bits',0,3,218,2,16,4,9
	.byte	'CEV0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'CEV1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'CEV2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'CEV3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'CEV4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'CEV5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'CEV6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'CEV7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'CEV8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'CEV9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'CEV10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'CEV11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'CEV12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'CEV13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'CEV14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'CEV15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_CEFCLR_Bits',0,3,237,2,3
	.word	5309
	.byte	7
	.byte	'_Ifx_VADC_G_CEFLAG_Bits',0,3,240,2,16,4,9
	.byte	'CEV0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'CEV1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'CEV2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'CEV3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'CEV4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'CEV5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'CEV6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'CEV7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'CEV8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'CEV9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'CEV10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'CEV11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'CEV12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'CEV13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'CEV14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'CEV15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_CEFLAG_Bits',0,3,131,3,3
	.word	5657
	.byte	7
	.byte	'_Ifx_VADC_G_CEVNP0_Bits',0,3,134,3,16,4,9
	.byte	'CEV0NP',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'CEV1NP',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'CEV2NP',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'CEV3NP',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'CEV4NP',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'CEV5NP',0,1
	.word	612
	.byte	4,0,2,35,2,9
	.byte	'CEV6NP',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'CEV7NP',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_CEVNP0_Bits',0,3,144,3,3
	.word	6005
	.byte	7
	.byte	'_Ifx_VADC_G_CEVNP1_Bits',0,3,147,3,16,4,9
	.byte	'CEV8NP',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'CEV9NP',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'CEV10NP',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'CEV11NP',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'CEV12NP',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'CEV13NP',0,1
	.word	612
	.byte	4,0,2,35,2,9
	.byte	'CEV14NP',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'CEV15NP',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_CEVNP1_Bits',0,3,157,3,3
	.word	6212
	.byte	7
	.byte	'_Ifx_VADC_G_CHASS_Bits',0,3,160,3,16,4,9
	.byte	'ASSCH0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ASSCH1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'ASSCH2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'ASSCH3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'ASSCH4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'ASSCH5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'ASSCH6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'ASSCH7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'ASSCH8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'ASSCH9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'ASSCH10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'ASSCH11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'ASSCH12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'ASSCH13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'ASSCH14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'ASSCH15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'ASSCH16',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'ASSCH17',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'ASSCH18',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'ASSCH19',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'ASSCH20',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'ASSCH21',0,1
	.word	612
	.byte	1,2,2,35,2,9
	.byte	'ASSCH22',0,1
	.word	612
	.byte	1,1,2,35,2,9
	.byte	'ASSCH23',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'ASSCH24',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'ASSCH25',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'ASSCH26',0,1
	.word	612
	.byte	1,5,2,35,3,9
	.byte	'ASSCH27',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'ASSCH28',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'ASSCH29',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'ASSCH30',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'ASSCH31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_CHASS_Bits',0,3,194,3,3
	.word	6425
	.byte	7
	.byte	'_Ifx_VADC_G_CHCTR_Bits',0,3,197,3,16,4,9
	.byte	'ICLSEL',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'BNDSELL',0,1
	.word	612
	.byte	2,2,2,35,0,9
	.byte	'BNDSELU',0,1
	.word	612
	.byte	2,0,2,35,0,9
	.byte	'CHEVMODE',0,1
	.word	612
	.byte	2,6,2,35,1,9
	.byte	'SYNC',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'REFSEL',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'BNDSELX',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'RESREG',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'RESTBS',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'RESPOS',0,1
	.word	612
	.byte	1,2,2,35,2,9
	.byte	'reserved_22',0,2
	.word	1254
	.byte	6,4,2,35,2,9
	.byte	'BWDCH',0,1
	.word	612
	.byte	2,2,2,35,3,9
	.byte	'BWDEN',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_CHCTR_Bits',0,3,214,3,3
	.word	7084
	.byte	7
	.byte	'_Ifx_VADC_G_EMUXCTR_Bits',0,3,217,3,16,4,9
	.byte	'EMUXSET',0,1
	.word	612
	.byte	3,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	612
	.byte	5,0,2,35,0,9
	.byte	'EMUXACT',0,1
	.word	612
	.byte	3,5,2,35,1,9
	.byte	'reserved_11',0,1
	.word	612
	.byte	5,0,2,35,1,9
	.byte	'EMUXCH',0,2
	.word	1254
	.byte	10,6,2,35,2,9
	.byte	'EMUXMODE',0,1
	.word	612
	.byte	2,4,2,35,3,9
	.byte	'EMXCOD',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'EMXST',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'EMXCSS',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'EMXWC',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_EMUXCTR_Bits',0,3,229,3,3
	.word	7430
	.byte	7
	.byte	'_Ifx_VADC_G_Q0R0_Bits',0,3,232,3,16,4,9
	.byte	'REQCHNR',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'RF',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'ENSI',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'EXTR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'V',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,4
	.word	2267
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_Q0R0_Bits',0,3,240,3,3
	.word	7686
	.byte	7
	.byte	'_Ifx_VADC_G_Q0R3_Bits',0,3,243,3,16,4,9
	.byte	'REQCHNR',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'RF',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'ENSI',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'EXTR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'V',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'PDD',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'MDPD',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'MDPU',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'CDEN',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'CDSEL',0,1
	.word	612
	.byte	2,1,2,35,1,9
	.byte	'reserved_15',0,4
	.word	2267
	.byte	17,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_Q0R3_Bits',0,3,128,4,3
	.word	7845
	.byte	7
	.byte	'_Ifx_VADC_G_QBUR0_Bits',0,3,131,4,16,4,9
	.byte	'REQCHNR',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'RF',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'ENSI',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'EXTR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'V',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,4
	.word	2267
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_QBUR0_Bits',0,3,139,4,3
	.word	8085
	.byte	7
	.byte	'_Ifx_VADC_G_QBUR3_Bits',0,3,142,4,16,4,9
	.byte	'REQCHNR',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'RF',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'ENSI',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'EXTR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'V',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'PDD',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'MDPD',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'MDPU',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'CDEN',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'CDSEL',0,1
	.word	612
	.byte	2,1,2,35,1,9
	.byte	'reserved_15',0,4
	.word	2267
	.byte	17,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_QBUR3_Bits',0,3,155,4,3
	.word	8246
	.byte	7
	.byte	'_Ifx_VADC_G_QCTRL0_Bits',0,3,158,4,16,4,9
	.byte	'SRCRESREG',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'XTSEL',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'XTLVL',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'XTMODE',0,1
	.word	612
	.byte	2,1,2,35,1,9
	.byte	'XTWC',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'GTSEL',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'GTLVL',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,9
	.byte	'GTWC',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'TMEN',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	612
	.byte	2,1,2,35,3,9
	.byte	'TMWC',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_QCTRL0_Bits',0,3,174,4,3
	.word	8488
	.byte	7
	.byte	'_Ifx_VADC_G_QCTRL3_Bits',0,3,177,4,16,4,9
	.byte	'SRCRESREG',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'XTSEL',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'XTLVL',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'XTMODE',0,1
	.word	612
	.byte	2,1,2,35,1,9
	.byte	'XTWC',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'GTSEL',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'GTLVL',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,9
	.byte	'GTWC',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'TMEN',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	612
	.byte	2,1,2,35,3,9
	.byte	'TMWC',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_QCTRL3_Bits',0,3,193,4,3
	.word	8813
	.byte	7
	.byte	'_Ifx_VADC_G_QINR0_Bits',0,3,196,4,16,4,9
	.byte	'REQCHNR',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'RF',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'ENSI',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'EXTR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	2267
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_QINR0_Bits',0,3,203,4,3
	.word	9138
	.byte	7
	.byte	'_Ifx_VADC_G_QINR3_Bits',0,3,206,4,16,4,9
	.byte	'REQCHNR',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'RF',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'ENSI',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'EXTR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'PDD',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'MDPD',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'MDPU',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'CDEN',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'CDSEL',0,1
	.word	612
	.byte	2,1,2,35,1,9
	.byte	'reserved_15',0,4
	.word	2267
	.byte	17,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_QINR3_Bits',0,3,219,4,3
	.word	9286
	.byte	7
	.byte	'_Ifx_VADC_G_QMR0_Bits',0,3,222,4,16,4,9
	.byte	'ENGT',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'ENTR',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	612
	.byte	5,0,2,35,0,9
	.byte	'CLRV',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'TREV',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'FLUSH',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'CEV',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'RPTDIS',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,2
	.word	1254
	.byte	15,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_QMR0_Bits',0,3,234,4,3
	.word	9537
	.byte	7
	.byte	'_Ifx_VADC_G_QMR3_Bits',0,3,237,4,16,4,9
	.byte	'ENGT',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'ENTR',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	612
	.byte	5,0,2,35,0,9
	.byte	'CLRV',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'TREV',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'FLUSH',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'CEV',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'RPTDIS',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,2
	.word	1254
	.byte	15,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_QMR3_Bits',0,3,249,4,3
	.word	9778
	.byte	7
	.byte	'_Ifx_VADC_G_QSR0_Bits',0,3,252,4,16,4,9
	.byte	'FILL',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'EMPTY',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'REQGT',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'EV',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,4
	.word	2267
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_QSR0_Bits',0,3,133,5,3
	.word	10019
	.byte	7
	.byte	'_Ifx_VADC_G_QSR3_Bits',0,3,136,5,16,4,9
	.byte	'FILL',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'EMPTY',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'REQGT',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'EV',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,4
	.word	2267
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_QSR3_Bits',0,3,145,5,3
	.word	10208
	.byte	7
	.byte	'_Ifx_VADC_G_RCR_Bits',0,3,148,5,16,4,9
	.byte	'reserved_0',0,2
	.word	1254
	.byte	16,0,2,35,0,9
	.byte	'DRCTR',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'DMM',0,1
	.word	612
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	612
	.byte	2,0,2,35,2,9
	.byte	'WFR',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'FEN',0,1
	.word	612
	.byte	2,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	612
	.byte	4,1,2,35,3,9
	.byte	'SRGEN',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_RCR_Bits',0,3,158,5,3
	.word	10397
	.byte	7
	.byte	'_Ifx_VADC_G_REFCLR_Bits',0,3,161,5,16,4,9
	.byte	'REV0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'REV1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'REV2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'REV3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'REV4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'REV5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'REV6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'REV7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'REV8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'REV9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'REV10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'REV11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'REV12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'REV13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'REV14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'REV15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_REFCLR_Bits',0,3,180,5,3
	.word	10601
	.byte	7
	.byte	'_Ifx_VADC_G_REFLAG_Bits',0,3,183,5,16,4,9
	.byte	'REV0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'REV1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'REV2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'REV3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'REV4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'REV5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'REV6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'REV7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'REV8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'REV9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'REV10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'REV11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'REV12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'REV13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'REV14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'REV15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_REFLAG_Bits',0,3,202,5,3
	.word	10949
	.byte	7
	.byte	'_Ifx_VADC_G_RES_Bits',0,3,205,5,16,4,9
	.byte	'RESULT',0,2
	.word	1254
	.byte	16,0,2,35,0,9
	.byte	'DRC',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'CHNR',0,2
	.word	1254
	.byte	5,7,2,35,2,9
	.byte	'EMUX',0,1
	.word	612
	.byte	3,4,2,35,3,9
	.byte	'CRS',0,1
	.word	612
	.byte	2,2,2,35,3,9
	.byte	'FCR',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'VF',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_RES_Bits',0,3,214,5,3
	.word	11297
	.byte	7
	.byte	'_Ifx_VADC_G_RESD_Bits',0,3,217,5,16,4,9
	.byte	'RESULT',0,2
	.word	1254
	.byte	16,0,2,35,0,9
	.byte	'DRC',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'CHNR',0,2
	.word	1254
	.byte	5,7,2,35,2,9
	.byte	'EMUX',0,1
	.word	612
	.byte	3,4,2,35,3,9
	.byte	'CRS',0,1
	.word	612
	.byte	2,2,2,35,3,9
	.byte	'FCR',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'VF',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_RESD_Bits',0,3,226,5,3
	.word	11463
	.byte	7
	.byte	'_Ifx_VADC_G_REVNP0_Bits',0,3,229,5,16,4,9
	.byte	'REV0NP',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'REV1NP',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'REV2NP',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'REV3NP',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'REV4NP',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'REV5NP',0,1
	.word	612
	.byte	4,0,2,35,2,9
	.byte	'REV6NP',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'REV7NP',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_REVNP0_Bits',0,3,239,5,3
	.word	11631
	.byte	7
	.byte	'_Ifx_VADC_G_REVNP1_Bits',0,3,242,5,16,4,9
	.byte	'REV8NP',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'REV9NP',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'REV10NP',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'REV11NP',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'REV12NP',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'REV13NP',0,1
	.word	612
	.byte	4,0,2,35,2,9
	.byte	'REV14NP',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'REV15NP',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_REVNP1_Bits',0,3,252,5,3
	.word	11838
	.byte	7
	.byte	'_Ifx_VADC_G_RRASS_Bits',0,3,255,5,16,4,9
	.byte	'ASSRR0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ASSRR1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'ASSRR2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'ASSRR3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'ASSRR4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'ASSRR5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'ASSRR6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'ASSRR7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'ASSRR8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'ASSRR9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'ASSRR10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'ASSRR11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'ASSRR12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'ASSRR13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'ASSRR14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'ASSRR15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_RRASS_Bits',0,3,146,6,3
	.word	12051
	.byte	7
	.byte	'_Ifx_VADC_G_SEFCLR_Bits',0,3,149,6,16,4,9
	.byte	'SEV0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'SEV1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'SEV3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_SEFCLR_Bits',0,3,156,6,3
	.word	12429
	.byte	7
	.byte	'_Ifx_VADC_G_SEFLAG_Bits',0,3,159,6,16,4,9
	.byte	'SEV0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'SEV1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'SEV3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_SEFLAG_Bits',0,3,166,6,3
	.word	12584
	.byte	7
	.byte	'_Ifx_VADC_G_SEVNP_Bits',0,3,169,6,16,4,9
	.byte	'SEV0NP',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'SEV1NP',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'SEV3NP',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_SEVNP_Bits',0,3,176,6,3
	.word	12739
	.byte	7
	.byte	'_Ifx_VADC_G_SRACT_Bits',0,3,179,6,16,4,9
	.byte	'AGSR0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'AGSR1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'AGSR2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'AGSR3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'ASSR0',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'ASSR1',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'ASSR2',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'ASSR3',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,4
	.word	2267
	.byte	20,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_SRACT_Bits',0,3,191,6,3
	.word	12899
	.byte	7
	.byte	'_Ifx_VADC_G_SYNCTR_Bits',0,3,194,6,16,4,9
	.byte	'STSEL',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'EVALR1',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'EVALR2',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'EVALR3',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,4
	.word	2267
	.byte	25,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_SYNCTR_Bits',0,3,202,6,3
	.word	13141
	.byte	7
	.byte	'_Ifx_VADC_G_TRCTR_Bits',0,3,205,6,16,4,9
	.byte	'TSC',0,1
	.word	612
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	1254
	.byte	8,2,2,35,0,9
	.byte	'Q3ACT',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'OV',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'TSCSET',0,1
	.word	612
	.byte	6,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	612
	.byte	2,0,2,35,2,9
	.byte	'ITSEL',0,1
	.word	612
	.byte	2,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	2,4,2,35,3,9
	.byte	'SRDIS',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	612
	.byte	2,1,2,35,3,9
	.byte	'COV',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_G_TRCTR_Bits',0,3,218,6,3
	.word	13319
	.byte	7
	.byte	'_Ifx_VADC_G_VFR_Bits',0,3,221,6,16,4,9
	.byte	'VF0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'VF1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'VF2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'VF3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'VF4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'VF5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'VF6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'VF7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'VF8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'VF9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'VF10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'VF11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'VF12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'VF13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'VF14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'VF15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_G_VFR_Bits',0,3,240,6,3
	.word	13584
	.byte	7
	.byte	'_Ifx_VADC_GLOBBOUND_Bits',0,3,243,6,16,4,9
	.byte	'BOUNDARY0',0,2
	.word	1254
	.byte	12,4,2,35,0,9
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'BOUNDARY1',0,2
	.word	1254
	.byte	12,4,2,35,2,9
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_VADC_GLOBBOUND_Bits',0,3,249,6,3
	.word	13910
	.byte	7
	.byte	'_Ifx_VADC_GLOBCFG_Bits',0,3,252,6,16,4,9
	.byte	'DIVA',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	612
	.byte	2,1,2,35,0,9
	.byte	'DCMSB',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'DIVD',0,1
	.word	612
	.byte	2,6,2,35,1,9
	.byte	'reserved_10',0,1
	.word	612
	.byte	2,4,2,35,1,9
	.byte	'REFPC',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'reserved_13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'LOSUP',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'DIVWC',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'DPCAL0',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'DPCAL1',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'DPCAL2',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'DPCAL3',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,2
	.word	1254
	.byte	11,1,2,35,2,9
	.byte	'SUCAL',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_GLOBCFG_Bits',0,3,141,7,3
	.word	14063
	.byte	7
	.byte	'_Ifx_VADC_GLOBEFLAG_Bits',0,3,144,7,16,4,9
	.byte	'SEVGLB',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	612
	.byte	7,0,2,35,0,9
	.byte	'REVGLB',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,1
	.word	612
	.byte	7,0,2,35,1,9
	.byte	'SEVGLBCLR',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	612
	.byte	7,0,2,35,2,9
	.byte	'REVGLBCLR',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	612
	.byte	7,0,2,35,3,0,5
	.byte	'Ifx_VADC_GLOBEFLAG_Bits',0,3,154,7,3
	.word	14404
	.byte	7
	.byte	'_Ifx_VADC_GLOBEVNP_Bits',0,3,157,7,16,4,9
	.byte	'SEV0NP',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,2
	.word	1254
	.byte	12,0,2,35,0,9
	.byte	'REV0NP',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'reserved_20',0,2
	.word	1254
	.byte	12,0,2,35,2,0,5
	.byte	'Ifx_VADC_GLOBEVNP_Bits',0,3,163,7,3
	.word	14637
	.byte	7
	.byte	'_Ifx_VADC_GLOBRCR_Bits',0,3,166,7,16,4,9
	.byte	'reserved_0',0,2
	.word	1254
	.byte	16,0,2,35,0,9
	.byte	'DRCTR',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'reserved_20',0,1
	.word	612
	.byte	4,0,2,35,2,9
	.byte	'WFR',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	612
	.byte	6,1,2,35,3,9
	.byte	'SRGEN',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_GLOBRCR_Bits',0,3,174,7,3
	.word	14781
	.byte	7
	.byte	'_Ifx_VADC_GLOBRES_Bits',0,3,177,7,16,4,9
	.byte	'RESULT',0,2
	.word	1254
	.byte	16,0,2,35,0,9
	.byte	'GNR',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'CHNR',0,2
	.word	1254
	.byte	5,7,2,35,2,9
	.byte	'EMUX',0,1
	.word	612
	.byte	3,4,2,35,3,9
	.byte	'CRS',0,1
	.word	612
	.byte	2,2,2,35,3,9
	.byte	'FCR',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'VF',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_GLOBRES_Bits',0,3,186,7,3
	.word	14959
	.byte	7
	.byte	'_Ifx_VADC_GLOBRESD_Bits',0,3,189,7,16,4,9
	.byte	'RESULT',0,2
	.word	1254
	.byte	16,0,2,35,0,9
	.byte	'GNR',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'CHNR',0,2
	.word	1254
	.byte	5,7,2,35,2,9
	.byte	'EMUX',0,1
	.word	612
	.byte	3,4,2,35,3,9
	.byte	'CRS',0,1
	.word	612
	.byte	2,2,2,35,3,9
	.byte	'FCR',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'VF',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_GLOBRESD_Bits',0,3,198,7,3
	.word	15129
	.byte	7
	.byte	'_Ifx_VADC_GLOBTE_Bits',0,3,201,7,16,4,9
	.byte	'TFEG0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'TFEG1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	2267
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_VADC_GLOBTE_Bits',0,3,206,7,3
	.word	15301
	.byte	7
	.byte	'_Ifx_VADC_GLOBTF_Bits',0,3,209,7,16,4,9
	.byte	'CDCH',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'CDGR',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'CDEN',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'CDSEL',0,1
	.word	612
	.byte	2,5,2,35,1,9
	.byte	'reserved_11',0,1
	.word	612
	.byte	4,1,2,35,1,9
	.byte	'CDWC',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'PDD',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'MDPD',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'MDPU',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	612
	.byte	4,1,2,35,2,9
	.byte	'MDWC',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	6,2,2,35,3,9
	.byte	'RCEN',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'RCWC',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_VADC_GLOBTF_Bits',0,3,225,7,3
	.word	15416
	.byte	7
	.byte	'_Ifx_VADC_ICLASS_Bits',0,3,228,7,16,4,9
	.byte	'STCS',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	612
	.byte	3,0,2,35,0,9
	.byte	'CMS',0,1
	.word	612
	.byte	3,5,2,35,1,9
	.byte	'reserved_11',0,1
	.word	612
	.byte	5,0,2,35,1,9
	.byte	'STCE',0,1
	.word	612
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	3,0,2,35,2,9
	.byte	'CME',0,1
	.word	612
	.byte	3,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	612
	.byte	5,0,2,35,3,0,5
	.byte	'Ifx_VADC_ICLASS_Bits',0,3,238,7,3
	.word	15720
	.byte	7
	.byte	'_Ifx_VADC_ID_Bits',0,3,241,7,16,4,9
	.byte	'MODREV',0,1
	.word	612
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_VADC_ID_Bits',0,3,246,7,3
	.word	15932
	.byte	7
	.byte	'_Ifx_VADC_KRST0_Bits',0,3,249,7,16,4,9
	.byte	'RST',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'RSTSTAT',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	2267
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_VADC_KRST0_Bits',0,3,254,7,3
	.word	16041
	.byte	7
	.byte	'_Ifx_VADC_KRST1_Bits',0,3,129,8,16,4,9
	.byte	'RST',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	2267
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_VADC_KRST1_Bits',0,3,133,8,3
	.word	16154
	.byte	7
	.byte	'_Ifx_VADC_KRSTCLR_Bits',0,3,136,8,16,4,9
	.byte	'CLR',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	2267
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_VADC_KRSTCLR_Bits',0,3,140,8,3
	.word	16248
	.byte	7
	.byte	'_Ifx_VADC_OCS_Bits',0,3,143,8,16,4,9
	.byte	'TGS',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'TGB',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'TG_P',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	20,8,2,35,2,9
	.byte	'SUS',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'SUS_P',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'SUSSTA',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	612
	.byte	2,0,2,35,3,0,5
	.byte	'Ifx_VADC_OCS_Bits',0,3,153,8,3
	.word	16346
	.byte	10,3,161,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,8
	.byte	'int',0,4,5,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	585
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_ACCEN0',0,3,166,8,3
	.word	16540
	.byte	10,3,169,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1161
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_ACCPROT0',0,3,174,8,3
	.word	16612
	.byte	10,3,177,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1449
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_ACCPROT1',0,3,182,8,3
	.word	16679
	.byte	10,3,185,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1699
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_BRSCTRL',0,3,190,8,3
	.word	16746
	.byte	10,3,193,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1966
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_BRSMR',0,3,198,8,3
	.word	16812
	.byte	10,3,201,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2239
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_BRSPND',0,3,206,8,3
	.word	16876
	.byte	10,3,209,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2333
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_BRSSEL',0,3,214,8,3
	.word	16941
	.byte	10,3,217,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2411
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_CLC',0,3,222,8,3
	.word	17006
	.byte	10,3,225,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2556
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_EMUXSEL',0,3,230,8,3
	.word	17068
	.byte	10,3,233,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2679
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_ALIAS',0,3,238,8,3
	.word	17134
	.byte	10,3,241,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2821
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_ARBCFG',0,3,246,8,3
	.word	17200
	.byte	10,3,249,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3156
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_ARBPR',0,3,254,8,3
	.word	17267
	.byte	10,3,129,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3553
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_ASCTRL',0,3,134,9,3
	.word	17333
	.byte	10,3,137,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3878
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_ASMR',0,3,142,9,3
	.word	17400
	.byte	10,3,145,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4153
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_ASPND',0,3,150,9,3
	.word	17465
	.byte	10,3,153,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4231
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_ASSEL',0,3,158,9,3
	.word	17531
	.byte	10,3,161,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4309
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_BFL',0,3,166,9,3
	.word	17597
	.byte	10,3,169,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4626
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_BFLC',0,3,174,9,3
	.word	17661
	.byte	10,3,177,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4772
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_BFLNP',0,3,182,9,3
	.word	17726
	.byte	10,3,185,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4928
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_BFLS',0,3,190,9,3
	.word	17792
	.byte	10,3,193,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5160
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_BOUND',0,3,198,9,3
	.word	17857
	.byte	10,3,201,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5309
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_CEFCLR',0,3,206,9,3
	.word	17923
	.byte	10,3,209,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5657
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_CEFLAG',0,3,214,9,3
	.word	17990
	.byte	10,3,217,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6005
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_CEVNP0',0,3,222,9,3
	.word	18057
	.byte	10,3,225,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6212
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_CEVNP1',0,3,230,9,3
	.word	18124
	.byte	10,3,233,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6425
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_CHASS',0,3,238,9,3
	.word	18191
	.byte	10,3,241,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7084
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_CHCTR',0,3,246,9,3
	.word	18257
	.byte	10,3,249,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7430
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_EMUXCTR',0,3,254,9,3
	.word	18323
	.byte	10,3,129,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7686
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_Q0R0',0,3,134,10,3
	.word	18391
	.byte	10,3,137,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7845
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_Q0R3',0,3,142,10,3
	.word	18456
	.byte	10,3,145,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8085
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QBUR0',0,3,150,10,3
	.word	18521
	.byte	10,3,153,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8246
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QBUR3',0,3,158,10,3
	.word	18587
	.byte	10,3,161,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8488
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QCTRL0',0,3,166,10,3
	.word	18653
	.byte	10,3,169,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8813
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QCTRL3',0,3,174,10,3
	.word	18720
	.byte	10,3,177,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9138
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QINR0',0,3,182,10,3
	.word	18787
	.byte	10,3,185,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9286
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QINR3',0,3,190,10,3
	.word	18853
	.byte	10,3,193,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9537
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QMR0',0,3,198,10,3
	.word	18919
	.byte	10,3,201,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9778
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QMR3',0,3,206,10,3
	.word	18984
	.byte	10,3,209,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10019
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QSR0',0,3,214,10,3
	.word	19049
	.byte	10,3,217,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10208
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_QSR3',0,3,222,10,3
	.word	19114
	.byte	10,3,225,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10397
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_RCR',0,3,230,10,3
	.word	19179
	.byte	10,3,233,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10601
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_REFCLR',0,3,238,10,3
	.word	19243
	.byte	10,3,241,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10949
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_REFLAG',0,3,246,10,3
	.word	19310
	.byte	10,3,249,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11297
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_RES',0,3,254,10,3
	.word	19377
	.byte	10,3,129,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11463
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_RESD',0,3,134,11,3
	.word	19441
	.byte	10,3,137,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11631
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_REVNP0',0,3,142,11,3
	.word	19506
	.byte	10,3,145,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11838
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_REVNP1',0,3,150,11,3
	.word	19573
	.byte	10,3,153,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12051
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_RRASS',0,3,158,11,3
	.word	19640
	.byte	10,3,161,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12429
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_SEFCLR',0,3,166,11,3
	.word	19706
	.byte	10,3,169,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12584
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_SEFLAG',0,3,174,11,3
	.word	19773
	.byte	10,3,177,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12739
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_SEVNP',0,3,182,11,3
	.word	19840
	.byte	10,3,185,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12899
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_SRACT',0,3,190,11,3
	.word	19906
	.byte	10,3,193,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13141
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_SYNCTR',0,3,198,11,3
	.word	19972
	.byte	10,3,201,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13319
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_TRCTR',0,3,206,11,3
	.word	20039
	.byte	10,3,209,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13584
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_G_VFR',0,3,214,11,3
	.word	20105
	.byte	10,3,217,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13910
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBBOUND',0,3,222,11,3
	.word	20169
	.byte	10,3,225,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14063
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBCFG',0,3,230,11,3
	.word	20237
	.byte	10,3,233,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14404
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBEFLAG',0,3,238,11,3
	.word	20303
	.byte	10,3,241,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14637
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBEVNP',0,3,246,11,3
	.word	20371
	.byte	10,3,249,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14781
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBRCR',0,3,254,11,3
	.word	20438
	.byte	10,3,129,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14959
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBRES',0,3,134,12,3
	.word	20504
	.byte	10,3,137,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15129
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBRESD',0,3,142,12,3
	.word	20570
	.byte	10,3,145,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15301
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBTE',0,3,150,12,3
	.word	20637
	.byte	10,3,153,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15416
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_GLOBTF',0,3,158,12,3
	.word	20702
	.byte	10,3,161,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15720
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_ICLASS',0,3,166,12,3
	.word	20767
	.byte	10,3,169,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15932
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_ID',0,3,174,12,3
	.word	20832
	.byte	10,3,177,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16041
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_KRST0',0,3,182,12,3
	.word	20893
	.byte	10,3,185,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16154
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_KRST1',0,3,190,12,3
	.word	20957
	.byte	10,3,193,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16248
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_KRSTCLR',0,3,198,12,3
	.word	21021
	.byte	10,3,201,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16346
	.byte	2,35,0,0,5
	.byte	'Ifx_VADC_OCS',0,3,206,12,3
	.word	21087
	.byte	7
	.byte	'_Ifx_VADC_G',0,3,217,12,25,128,8,11
	.byte	'ARBCFG',0,4
	.word	17200
	.byte	2,35,0,11
	.byte	'ARBPR',0,4
	.word	17267
	.byte	2,35,4,11
	.byte	'CHASS',0,4
	.word	18191
	.byte	2,35,8,11
	.byte	'RRASS',0,4
	.word	19640
	.byte	2,35,12,12,16
	.word	612
	.byte	13,15,0,11
	.byte	'reserved_10',0,16
	.word	21229
	.byte	2,35,16,12,8
	.word	20767
	.byte	13,1,0,11
	.byte	'ICLASS',0,8
	.word	21259
	.byte	2,35,32,12,8
	.word	612
	.byte	13,7,0,11
	.byte	'reserved_28',0,8
	.word	21284
	.byte	2,35,40,11
	.byte	'ALIAS',0,4
	.word	17134
	.byte	2,35,48,12,4
	.word	612
	.byte	13,3,0,11
	.byte	'reserved_34',0,4
	.word	21329
	.byte	2,35,52,11
	.byte	'BOUND',0,4
	.word	17857
	.byte	2,35,56,11
	.byte	'reserved_3C',0,4
	.word	21329
	.byte	2,35,60,11
	.byte	'SYNCTR',0,4
	.word	19972
	.byte	2,35,64,11
	.byte	'reserved_44',0,4
	.word	21329
	.byte	2,35,68,11
	.byte	'BFL',0,4
	.word	17597
	.byte	2,35,72,11
	.byte	'BFLS',0,4
	.word	17792
	.byte	2,35,76,11
	.byte	'BFLC',0,4
	.word	17661
	.byte	2,35,80,11
	.byte	'BFLNP',0,4
	.word	17726
	.byte	2,35,84,12,40
	.word	612
	.byte	13,39,0,11
	.byte	'reserved_58',0,40
	.word	21488
	.byte	2,35,88,11
	.byte	'QCTRL0',0,4
	.word	18653
	.byte	3,35,128,1,11
	.byte	'QMR0',0,4
	.word	18919
	.byte	3,35,132,1,11
	.byte	'QSR0',0,4
	.word	19049
	.byte	3,35,136,1,11
	.byte	'Q0R0',0,4
	.word	18391
	.byte	3,35,140,1,10,3,241,12,5,4,11
	.byte	'QBUR0',0,4
	.word	18521
	.byte	2,35,0,11
	.byte	'QINR0',0,4
	.word	18787
	.byte	2,35,0,0,14,4
	.word	21580
	.byte	3,35,144,1,12,12
	.word	612
	.byte	13,11,0,11
	.byte	'reserved_94',0,12
	.word	21627
	.byte	3,35,148,1,11
	.byte	'ASCTRL',0,4
	.word	17333
	.byte	3,35,160,1,11
	.byte	'ASMR',0,4
	.word	17400
	.byte	3,35,164,1,11
	.byte	'ASSEL',0,4
	.word	17531
	.byte	3,35,168,1,11
	.byte	'ASPND',0,4
	.word	17465
	.byte	3,35,172,1,11
	.byte	'reserved_B0',0,16
	.word	21229
	.byte	3,35,176,1,11
	.byte	'QCTRL3',0,4
	.word	18720
	.byte	3,35,192,1,11
	.byte	'QMR3',0,4
	.word	18984
	.byte	3,35,196,1,11
	.byte	'QSR3',0,4
	.word	19114
	.byte	3,35,200,1,11
	.byte	'Q0R3',0,4
	.word	18456
	.byte	3,35,204,1,10,3,129,13,5,4,11
	.byte	'QBUR3',0,4
	.word	18587
	.byte	2,35,0,11
	.byte	'QINR3',0,4
	.word	18853
	.byte	2,35,0,0,14,4
	.word	21806
	.byte	3,35,208,1,11
	.byte	'TRCTR',0,4
	.word	20039
	.byte	3,35,212,1,11
	.byte	'reserved_D8',0,40
	.word	21488
	.byte	3,35,216,1,11
	.byte	'CEFLAG',0,4
	.word	17990
	.byte	3,35,128,2,11
	.byte	'REFLAG',0,4
	.word	19310
	.byte	3,35,132,2,11
	.byte	'SEFLAG',0,4
	.word	19773
	.byte	3,35,136,2,11
	.byte	'reserved_10C',0,4
	.word	21329
	.byte	3,35,140,2,11
	.byte	'CEFCLR',0,4
	.word	17923
	.byte	3,35,144,2,11
	.byte	'REFCLR',0,4
	.word	19243
	.byte	3,35,148,2,11
	.byte	'SEFCLR',0,4
	.word	19706
	.byte	3,35,152,2,11
	.byte	'reserved_11C',0,4
	.word	21329
	.byte	3,35,156,2,11
	.byte	'CEVNP0',0,4
	.word	18057
	.byte	3,35,160,2,11
	.byte	'CEVNP1',0,4
	.word	18124
	.byte	3,35,164,2,11
	.byte	'reserved_128',0,8
	.word	21284
	.byte	3,35,168,2,11
	.byte	'REVNP0',0,4
	.word	19506
	.byte	3,35,176,2,11
	.byte	'REVNP1',0,4
	.word	19573
	.byte	3,35,180,2,11
	.byte	'reserved_138',0,8
	.word	21284
	.byte	3,35,184,2,11
	.byte	'SEVNP',0,4
	.word	19840
	.byte	3,35,192,2,11
	.byte	'reserved_144',0,4
	.word	21329
	.byte	3,35,196,2,11
	.byte	'SRACT',0,4
	.word	19906
	.byte	3,35,200,2,12,36
	.word	612
	.byte	13,35,0,11
	.byte	'reserved_14C',0,36
	.word	22208
	.byte	3,35,204,2,11
	.byte	'EMUXCTR',0,4
	.word	18323
	.byte	3,35,240,2,11
	.byte	'reserved_174',0,4
	.word	21329
	.byte	3,35,244,2,11
	.byte	'VFR',0,4
	.word	20105
	.byte	3,35,248,2,11
	.byte	'reserved_17C',0,4
	.word	21329
	.byte	3,35,252,2,12,48
	.word	18257
	.byte	13,11,0,11
	.byte	'CHCTR',0,48
	.word	22318
	.byte	3,35,128,3,12,80
	.word	612
	.byte	13,79,0,11
	.byte	'reserved_1B0',0,80
	.word	22343
	.byte	3,35,176,3,12,64
	.word	19179
	.byte	13,15,0,11
	.byte	'RCR',0,64
	.word	22375
	.byte	3,35,128,4,12,64
	.word	612
	.byte	13,63,0,11
	.byte	'reserved_240',0,64
	.word	22398
	.byte	3,35,192,4,12,64
	.word	19377
	.byte	13,15,0,11
	.byte	'RES',0,64
	.word	22430
	.byte	3,35,128,5,11
	.byte	'reserved_2C0',0,64
	.word	22398
	.byte	3,35,192,5,12,64
	.word	19441
	.byte	13,15,0,11
	.byte	'RESD',0,64
	.word	22476
	.byte	3,35,128,6,12,192,1
	.word	612
	.byte	13,191,1,0,11
	.byte	'reserved_340',0,192,1
	.word	22500
	.byte	3,35,192,6,0,15
	.word	21149
	.byte	5
	.byte	'Ifx_VADC_G',0,3,167,13,3
	.word	22536
	.byte	7
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,9
	.byte	'SRPN',0,1
	.word	612
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	2,6,2,35,1,9
	.byte	'SRE',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'TOS',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'ECC',0,1
	.word	612
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	3,0,2,35,2,9
	.byte	'SRR',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'CLRR',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'SETR',0,1
	.word	612
	.byte	1,5,2,35,3,9
	.byte	'IOV',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'IOVCLR',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'SWS',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'SWSCLR',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	22561
	.byte	10,4,70,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22561
	.byte	2,35,0,0,5
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	22877
	.byte	7
	.byte	'_Ifx_SRC_ASCLIN',0,4,86,25,12,11
	.byte	'TX',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	22877
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	22877
	.byte	2,35,8,0,15
	.word	22937
	.byte	5
	.byte	'Ifx_SRC_ASCLIN',0,4,91,3
	.word	22996
	.byte	7
	.byte	'_Ifx_SRC_BCUSPB',0,4,94,25,4,11
	.byte	'SBSRC',0,4
	.word	22877
	.byte	2,35,0,0,15
	.word	23024
	.byte	5
	.byte	'Ifx_SRC_BCUSPB',0,4,97,3
	.word	23061
	.byte	7
	.byte	'_Ifx_SRC_CAN',0,4,100,25,64,12,64
	.word	22877
	.byte	13,15,0,11
	.byte	'INT',0,64
	.word	23107
	.byte	2,35,0,0,15
	.word	23089
	.byte	5
	.byte	'Ifx_SRC_CAN',0,4,103,3
	.word	23130
	.byte	7
	.byte	'_Ifx_SRC_CAN1',0,4,106,25,32,12,32
	.word	22877
	.byte	13,7,0,11
	.byte	'INT',0,32
	.word	23174
	.byte	2,35,0,0,15
	.word	23155
	.byte	5
	.byte	'Ifx_SRC_CAN1',0,4,109,3
	.word	23197
	.byte	7
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,11
	.byte	'SR0',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	22877
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	22877
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	22877
	.byte	2,35,12,0,15
	.word	23223
	.byte	5
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	23295
	.byte	7
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,12,8
	.word	22877
	.byte	13,1,0,11
	.byte	'SR',0,8
	.word	23344
	.byte	2,35,0,0,15
	.word	23321
	.byte	5
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	23366
	.byte	7
	.byte	'_Ifx_SRC_CPU',0,4,127,25,32,11
	.byte	'SBSRC',0,4
	.word	22877
	.byte	2,35,0,12,28
	.word	612
	.byte	13,27,0,11
	.byte	'reserved_4',0,28
	.word	23429
	.byte	2,35,4,0,15
	.word	23396
	.byte	5
	.byte	'Ifx_SRC_CPU',0,4,131,1,3
	.word	23459
	.byte	7
	.byte	'_Ifx_SRC_DMA',0,4,134,1,25,80,11
	.byte	'ERR',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'reserved_4',0,12
	.word	21627
	.byte	2,35,4,11
	.byte	'CH',0,64
	.word	23107
	.byte	2,35,16,0,15
	.word	23485
	.byte	5
	.byte	'Ifx_SRC_DMA',0,4,139,1,3
	.word	23550
	.byte	7
	.byte	'_Ifx_SRC_EMEM',0,4,142,1,25,4,11
	.byte	'SR',0,4
	.word	22877
	.byte	2,35,0,0,15
	.word	23576
	.byte	5
	.byte	'Ifx_SRC_EMEM',0,4,145,1,3
	.word	23609
	.byte	7
	.byte	'_Ifx_SRC_ERAY',0,4,148,1,25,80,11
	.byte	'INT',0,8
	.word	23344
	.byte	2,35,0,11
	.byte	'TINT',0,8
	.word	23344
	.byte	2,35,8,11
	.byte	'NDAT',0,8
	.word	23344
	.byte	2,35,16,11
	.byte	'MBSC',0,8
	.word	23344
	.byte	2,35,24,11
	.byte	'OBUSY',0,4
	.word	22877
	.byte	2,35,32,11
	.byte	'IBUSY',0,4
	.word	22877
	.byte	2,35,36,11
	.byte	'reserved_28',0,40
	.word	21488
	.byte	2,35,40,0,15
	.word	23636
	.byte	5
	.byte	'Ifx_SRC_ERAY',0,4,157,1,3
	.word	23763
	.byte	7
	.byte	'_Ifx_SRC_ETH',0,4,160,1,25,4,11
	.byte	'SR',0,4
	.word	22877
	.byte	2,35,0,0,15
	.word	23790
	.byte	5
	.byte	'Ifx_SRC_ETH',0,4,163,1,3
	.word	23822
	.byte	7
	.byte	'_Ifx_SRC_EVR',0,4,166,1,25,8,11
	.byte	'WUT',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'SCDC',0,4
	.word	22877
	.byte	2,35,4,0,15
	.word	23848
	.byte	5
	.byte	'Ifx_SRC_EVR',0,4,170,1,3
	.word	23895
	.byte	7
	.byte	'_Ifx_SRC_FFT',0,4,173,1,25,12,11
	.byte	'DONE',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'ERR',0,4
	.word	22877
	.byte	2,35,4,11
	.byte	'RFS',0,4
	.word	22877
	.byte	2,35,8,0,15
	.word	23921
	.byte	5
	.byte	'Ifx_SRC_FFT',0,4,178,1,3
	.word	23981
	.byte	7
	.byte	'_Ifx_SRC_GPSR',0,4,181,1,25,128,12,11
	.byte	'SR0',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	22877
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	22877
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	22877
	.byte	2,35,12,12,240,11
	.word	612
	.byte	13,239,11,0,11
	.byte	'reserved_10',0,240,11
	.word	24080
	.byte	2,35,16,0,15
	.word	24007
	.byte	5
	.byte	'Ifx_SRC_GPSR',0,4,188,1,3
	.word	24114
	.byte	7
	.byte	'_Ifx_SRC_GPT12',0,4,191,1,25,48,11
	.byte	'CIRQ',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'T2',0,4
	.word	22877
	.byte	2,35,4,11
	.byte	'T3',0,4
	.word	22877
	.byte	2,35,8,11
	.byte	'T4',0,4
	.word	22877
	.byte	2,35,12,11
	.byte	'T5',0,4
	.word	22877
	.byte	2,35,16,11
	.byte	'T6',0,4
	.word	22877
	.byte	2,35,20,12,24
	.word	612
	.byte	13,23,0,11
	.byte	'reserved_18',0,24
	.word	24236
	.byte	2,35,24,0,15
	.word	24141
	.byte	5
	.byte	'Ifx_SRC_GPT12',0,4,200,1,3
	.word	24267
	.byte	7
	.byte	'_Ifx_SRC_GTM',0,4,203,1,25,192,11,11
	.byte	'AEIIRQ',0,4
	.word	22877
	.byte	2,35,0,12,236,2
	.word	612
	.byte	13,235,2,0,11
	.byte	'reserved_4',0,236,2
	.word	24331
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	22877
	.byte	3,35,240,2,11
	.byte	'reserved_174',0,12
	.word	21627
	.byte	3,35,244,2,12,32
	.word	23174
	.byte	13,0,0,11
	.byte	'TIM',0,32
	.word	24400
	.byte	3,35,128,3,12,224,7
	.word	612
	.byte	13,223,7,0,11
	.byte	'reserved_1A0',0,224,7
	.word	24423
	.byte	3,35,160,3,12,64
	.word	23174
	.byte	13,1,0,11
	.byte	'TOM',0,64
	.word	24458
	.byte	3,35,128,11,0,15
	.word	24295
	.byte	5
	.byte	'Ifx_SRC_GTM',0,4,212,1,3
	.word	24482
	.byte	7
	.byte	'_Ifx_SRC_HSM',0,4,215,1,25,8,11
	.byte	'HSM',0,8
	.word	23344
	.byte	2,35,0,0,15
	.word	24508
	.byte	5
	.byte	'Ifx_SRC_HSM',0,4,218,1,3
	.word	24541
	.byte	7
	.byte	'_Ifx_SRC_LMU',0,4,221,1,25,4,11
	.byte	'SR',0,4
	.word	22877
	.byte	2,35,0,0,15
	.word	24567
	.byte	5
	.byte	'Ifx_SRC_LMU',0,4,224,1,3
	.word	24599
	.byte	7
	.byte	'_Ifx_SRC_PMU',0,4,227,1,25,4,11
	.byte	'SR',0,4
	.word	22877
	.byte	2,35,0,0,15
	.word	24625
	.byte	5
	.byte	'Ifx_SRC_PMU',0,4,230,1,3
	.word	24657
	.byte	7
	.byte	'_Ifx_SRC_QSPI',0,4,233,1,25,24,11
	.byte	'TX',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	22877
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	22877
	.byte	2,35,8,11
	.byte	'PT',0,4
	.word	22877
	.byte	2,35,12,11
	.byte	'HC',0,4
	.word	22877
	.byte	2,35,16,11
	.byte	'U',0,4
	.word	22877
	.byte	2,35,20,0,15
	.word	24683
	.byte	5
	.byte	'Ifx_SRC_QSPI',0,4,241,1,3
	.word	24776
	.byte	7
	.byte	'_Ifx_SRC_SCU',0,4,244,1,25,20,11
	.byte	'DTS',0,4
	.word	22877
	.byte	2,35,0,12,16
	.word	22877
	.byte	13,3,0,11
	.byte	'ERU',0,16
	.word	24835
	.byte	2,35,4,0,15
	.word	24803
	.byte	5
	.byte	'Ifx_SRC_SCU',0,4,248,1,3
	.word	24858
	.byte	7
	.byte	'_Ifx_SRC_SENT',0,4,251,1,25,16,11
	.byte	'SR',0,16
	.word	24835
	.byte	2,35,0,0,15
	.word	24884
	.byte	5
	.byte	'Ifx_SRC_SENT',0,4,254,1,3
	.word	24917
	.byte	7
	.byte	'_Ifx_SRC_SMU',0,4,129,2,25,12,12,12
	.word	22877
	.byte	13,2,0,11
	.byte	'SR',0,12
	.word	24963
	.byte	2,35,0,0,15
	.word	24944
	.byte	5
	.byte	'Ifx_SRC_SMU',0,4,132,2,3
	.word	24985
	.byte	7
	.byte	'_Ifx_SRC_STM',0,4,135,2,25,96,11
	.byte	'SR0',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	22877
	.byte	2,35,4,12,88
	.word	612
	.byte	13,87,0,11
	.byte	'reserved_8',0,88
	.word	25056
	.byte	2,35,8,0,15
	.word	25011
	.byte	5
	.byte	'Ifx_SRC_STM',0,4,140,2,3
	.word	25086
	.byte	7
	.byte	'_Ifx_SRC_VADCCG',0,4,143,2,25,192,2,11
	.byte	'SR0',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	22877
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	22877
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	22877
	.byte	2,35,12,12,176,2
	.word	612
	.byte	13,175,2,0,11
	.byte	'reserved_10',0,176,2
	.word	25187
	.byte	2,35,16,0,15
	.word	25112
	.byte	5
	.byte	'Ifx_SRC_VADCCG',0,4,150,2,3
	.word	25221
	.byte	7
	.byte	'_Ifx_SRC_VADCG',0,4,153,2,25,16,11
	.byte	'SR0',0,4
	.word	22877
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	22877
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	22877
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	22877
	.byte	2,35,12,0,15
	.word	25250
	.byte	5
	.byte	'Ifx_SRC_VADCG',0,4,159,2,3
	.word	25324
	.byte	7
	.byte	'_Ifx_SRC_XBAR',0,4,162,2,25,4,11
	.byte	'SRC',0,4
	.word	22877
	.byte	2,35,0,0,15
	.word	25352
	.byte	5
	.byte	'Ifx_SRC_XBAR',0,4,165,2,3
	.word	25386
	.byte	7
	.byte	'_Ifx_SRC_GASCLIN',0,4,178,2,25,24,12,24
	.word	22937
	.byte	13,1,0,15
	.word	25436
	.byte	11
	.byte	'ASCLIN',0,24
	.word	25445
	.byte	2,35,0,0,15
	.word	25413
	.byte	5
	.byte	'Ifx_SRC_GASCLIN',0,4,181,2,3
	.word	25467
	.byte	7
	.byte	'_Ifx_SRC_GBCU',0,4,184,2,25,4,15
	.word	23024
	.byte	11
	.byte	'SPB',0,4
	.word	25517
	.byte	2,35,0,0,15
	.word	25497
	.byte	5
	.byte	'Ifx_SRC_GBCU',0,4,187,2,3
	.word	25536
	.byte	7
	.byte	'_Ifx_SRC_GCAN',0,4,190,2,25,96,12,64
	.word	23089
	.byte	13,0,0,15
	.word	25583
	.byte	11
	.byte	'CAN',0,64
	.word	25592
	.byte	2,35,0,12,32
	.word	23155
	.byte	13,0,0,15
	.word	25610
	.byte	11
	.byte	'CAN1',0,32
	.word	25619
	.byte	2,35,64,0,15
	.word	25563
	.byte	5
	.byte	'Ifx_SRC_GCAN',0,4,194,2,3
	.word	25639
	.byte	7
	.byte	'_Ifx_SRC_GCCU6',0,4,197,2,25,32,12,32
	.word	23223
	.byte	13,1,0,15
	.word	25687
	.byte	11
	.byte	'CCU6',0,32
	.word	25696
	.byte	2,35,0,0,15
	.word	25666
	.byte	5
	.byte	'Ifx_SRC_GCCU6',0,4,200,2,3
	.word	25716
	.byte	7
	.byte	'_Ifx_SRC_GCERBERUS',0,4,203,2,25,8,15
	.word	23321
	.byte	11
	.byte	'CERBERUS',0,8
	.word	25769
	.byte	2,35,0,0,15
	.word	25744
	.byte	5
	.byte	'Ifx_SRC_GCERBERUS',0,4,206,2,3
	.word	25793
	.byte	7
	.byte	'_Ifx_SRC_GCPU',0,4,209,2,25,32,12,32
	.word	23396
	.byte	13,0,0,15
	.word	25845
	.byte	11
	.byte	'CPU',0,32
	.word	25854
	.byte	2,35,0,0,15
	.word	25825
	.byte	5
	.byte	'Ifx_SRC_GCPU',0,4,212,2,3
	.word	25873
	.byte	7
	.byte	'_Ifx_SRC_GDMA',0,4,215,2,25,80,12,80
	.word	23485
	.byte	13,0,0,15
	.word	25920
	.byte	11
	.byte	'DMA',0,80
	.word	25929
	.byte	2,35,0,0,15
	.word	25900
	.byte	5
	.byte	'Ifx_SRC_GDMA',0,4,218,2,3
	.word	25948
	.byte	7
	.byte	'_Ifx_SRC_GEMEM',0,4,221,2,25,4,12,4
	.word	23576
	.byte	13,0,0,15
	.word	25996
	.byte	11
	.byte	'EMEM',0,4
	.word	26005
	.byte	2,35,0,0,15
	.word	25975
	.byte	5
	.byte	'Ifx_SRC_GEMEM',0,4,224,2,3
	.word	26025
	.byte	7
	.byte	'_Ifx_SRC_GERAY',0,4,227,2,25,80,12,80
	.word	23636
	.byte	13,0,0,15
	.word	26074
	.byte	11
	.byte	'ERAY',0,80
	.word	26083
	.byte	2,35,0,0,15
	.word	26053
	.byte	5
	.byte	'Ifx_SRC_GERAY',0,4,230,2,3
	.word	26103
	.byte	7
	.byte	'_Ifx_SRC_GETH',0,4,233,2,25,4,12,4
	.word	23790
	.byte	13,0,0,15
	.word	26151
	.byte	11
	.byte	'ETH',0,4
	.word	26160
	.byte	2,35,0,0,15
	.word	26131
	.byte	5
	.byte	'Ifx_SRC_GETH',0,4,236,2,3
	.word	26179
	.byte	7
	.byte	'_Ifx_SRC_GEVR',0,4,239,2,25,8,12,8
	.word	23848
	.byte	13,0,0,15
	.word	26226
	.byte	11
	.byte	'EVR',0,8
	.word	26235
	.byte	2,35,0,0,15
	.word	26206
	.byte	5
	.byte	'Ifx_SRC_GEVR',0,4,242,2,3
	.word	26254
	.byte	7
	.byte	'_Ifx_SRC_GFFT',0,4,245,2,25,12,12,12
	.word	23921
	.byte	13,0,0,15
	.word	26301
	.byte	11
	.byte	'FFT',0,12
	.word	26310
	.byte	2,35,0,0,15
	.word	26281
	.byte	5
	.byte	'Ifx_SRC_GFFT',0,4,248,2,3
	.word	26329
	.byte	7
	.byte	'_Ifx_SRC_GGPSR',0,4,251,2,25,128,12,12,128,12
	.word	24007
	.byte	13,0,0,15
	.word	26378
	.byte	11
	.byte	'GPSR',0,128,12
	.word	26388
	.byte	2,35,0,0,15
	.word	26356
	.byte	5
	.byte	'Ifx_SRC_GGPSR',0,4,254,2,3
	.word	26409
	.byte	7
	.byte	'_Ifx_SRC_GGPT12',0,4,129,3,25,48,12,48
	.word	24141
	.byte	13,0,0,15
	.word	26459
	.byte	11
	.byte	'GPT12',0,48
	.word	26468
	.byte	2,35,0,0,15
	.word	26437
	.byte	5
	.byte	'Ifx_SRC_GGPT12',0,4,132,3,3
	.word	26489
	.byte	7
	.byte	'_Ifx_SRC_GGTM',0,4,135,3,25,192,11,12,192,11
	.word	24295
	.byte	13,0,0,15
	.word	26539
	.byte	11
	.byte	'GTM',0,192,11
	.word	26549
	.byte	2,35,0,0,15
	.word	26518
	.byte	5
	.byte	'Ifx_SRC_GGTM',0,4,138,3,3
	.word	26569
	.byte	7
	.byte	'_Ifx_SRC_GHSM',0,4,141,3,25,8,12,8
	.word	24508
	.byte	13,0,0,15
	.word	26616
	.byte	11
	.byte	'HSM',0,8
	.word	26625
	.byte	2,35,0,0,15
	.word	26596
	.byte	5
	.byte	'Ifx_SRC_GHSM',0,4,144,3,3
	.word	26644
	.byte	7
	.byte	'_Ifx_SRC_GLMU',0,4,147,3,25,4,12,4
	.word	24567
	.byte	13,0,0,15
	.word	26691
	.byte	11
	.byte	'LMU',0,4
	.word	26700
	.byte	2,35,0,0,15
	.word	26671
	.byte	5
	.byte	'Ifx_SRC_GLMU',0,4,150,3,3
	.word	26719
	.byte	7
	.byte	'_Ifx_SRC_GPMU',0,4,153,3,25,8,12,8
	.word	24625
	.byte	13,1,0,15
	.word	26766
	.byte	11
	.byte	'PMU',0,8
	.word	26775
	.byte	2,35,0,0,15
	.word	26746
	.byte	5
	.byte	'Ifx_SRC_GPMU',0,4,156,3,3
	.word	26794
	.byte	7
	.byte	'_Ifx_SRC_GQSPI',0,4,159,3,25,96,12,96
	.word	24683
	.byte	13,3,0,15
	.word	26842
	.byte	11
	.byte	'QSPI',0,96
	.word	26851
	.byte	2,35,0,0,15
	.word	26821
	.byte	5
	.byte	'Ifx_SRC_GQSPI',0,4,162,3,3
	.word	26871
	.byte	7
	.byte	'_Ifx_SRC_GSCU',0,4,165,3,25,20,15
	.word	24803
	.byte	11
	.byte	'SCU',0,20
	.word	26919
	.byte	2,35,0,0,15
	.word	26899
	.byte	5
	.byte	'Ifx_SRC_GSCU',0,4,168,3,3
	.word	26938
	.byte	7
	.byte	'_Ifx_SRC_GSENT',0,4,171,3,25,16,12,16
	.word	24884
	.byte	13,0,0,15
	.word	26986
	.byte	11
	.byte	'SENT',0,16
	.word	26995
	.byte	2,35,0,0,15
	.word	26965
	.byte	5
	.byte	'Ifx_SRC_GSENT',0,4,174,3,3
	.word	27015
	.byte	7
	.byte	'_Ifx_SRC_GSMU',0,4,177,3,25,12,12,12
	.word	24944
	.byte	13,0,0,15
	.word	27063
	.byte	11
	.byte	'SMU',0,12
	.word	27072
	.byte	2,35,0,0,15
	.word	27043
	.byte	5
	.byte	'Ifx_SRC_GSMU',0,4,180,3,3
	.word	27091
	.byte	7
	.byte	'_Ifx_SRC_GSTM',0,4,183,3,25,96,12,96
	.word	25011
	.byte	13,0,0,15
	.word	27138
	.byte	11
	.byte	'STM',0,96
	.word	27147
	.byte	2,35,0,0,15
	.word	27118
	.byte	5
	.byte	'Ifx_SRC_GSTM',0,4,186,3,3
	.word	27166
	.byte	7
	.byte	'_Ifx_SRC_GVADC',0,4,189,3,25,224,4,12,64
	.word	25250
	.byte	13,3,0,15
	.word	27215
	.byte	11
	.byte	'G',0,64
	.word	27224
	.byte	2,35,0,12,224,1
	.word	612
	.byte	13,223,1,0,11
	.byte	'reserved_40',0,224,1
	.word	27240
	.byte	2,35,64,12,192,2
	.word	25112
	.byte	13,0,0,15
	.word	27273
	.byte	11
	.byte	'CG',0,192,2
	.word	27283
	.byte	3,35,160,2,0,15
	.word	27193
	.byte	5
	.byte	'Ifx_SRC_GVADC',0,4,194,3,3
	.word	27303
	.byte	7
	.byte	'_Ifx_SRC_GXBAR',0,4,197,3,25,4,15
	.word	25352
	.byte	11
	.byte	'XBAR',0,4
	.word	27352
	.byte	2,35,0,0,15
	.word	27331
	.byte	5
	.byte	'Ifx_SRC_GXBAR',0,4,200,3,3
	.word	27372
	.byte	7
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,5,45,16,4,9
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	612
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	612
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	612
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_ACCEN0_Bits',0,5,79,3
	.word	27400
	.byte	7
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,5,82,16,4,9
	.byte	'reserved_0',0,4
	.word	2267
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_SCU_ACCEN1_Bits',0,5,85,3
	.word	27957
	.byte	7
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,5,88,16,4,9
	.byte	'STM0DIS',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'STM1DIS',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'STM2DIS',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,4
	.word	2267
	.byte	29,0,2,35,2,0,5
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,5,94,3
	.word	28034
	.byte	7
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,5,97,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'BAUD2DIV',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'SRIDIV',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'LPDIV',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'SPBDIV',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'FSI2DIV',0,1
	.word	612
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	612
	.byte	2,0,2,35,2,9
	.byte	'FSIDIV',0,1
	.word	612
	.byte	2,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	2,4,2,35,3,9
	.byte	'CLKSEL',0,1
	.word	612
	.byte	2,2,2,35,3,9
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON0_Bits',0,5,111,3
	.word	28170
	.byte	7
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,5,114,16,4,9
	.byte	'CANDIV',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'ERAYDIV',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'STMDIV',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'GTMDIV',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'ETHDIV',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'ASCLINFDIV',0,1
	.word	612
	.byte	4,0,2,35,2,9
	.byte	'ASCLINSDIV',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'INSEL',0,1
	.word	612
	.byte	2,2,2,35,3,9
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON1_Bits',0,5,126,3
	.word	28452
	.byte	7
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,5,129,1,16,4,9
	.byte	'BBBDIV',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	26,2,2,35,2,9
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON2_Bits',0,5,135,1,3
	.word	28690
	.byte	7
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,5,138,1,16,4,9
	.byte	'PLLDIV',0,1
	.word	612
	.byte	6,2,2,35,0,9
	.byte	'PLLSEL',0,1
	.word	612
	.byte	2,0,2,35,0,9
	.byte	'PLLERAYDIV',0,1
	.word	612
	.byte	6,2,2,35,1,9
	.byte	'PLLERAYSEL',0,1
	.word	612
	.byte	2,0,2,35,1,9
	.byte	'SRIDIV',0,1
	.word	612
	.byte	6,2,2,35,2,9
	.byte	'SRISEL',0,1
	.word	612
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	6,2,2,35,3,9
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON3_Bits',0,5,149,1,3
	.word	28818
	.byte	7
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,5,152,1,16,4,9
	.byte	'SPBDIV',0,1
	.word	612
	.byte	6,2,2,35,0,9
	.byte	'SPBSEL',0,1
	.word	612
	.byte	2,0,2,35,0,9
	.byte	'GTMDIV',0,1
	.word	612
	.byte	6,2,2,35,1,9
	.byte	'GTMSEL',0,1
	.word	612
	.byte	2,0,2,35,1,9
	.byte	'STMDIV',0,1
	.word	612
	.byte	6,2,2,35,2,9
	.byte	'STMSEL',0,1
	.word	612
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	6,2,2,35,3,9
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON4_Bits',0,5,163,1,3
	.word	29045
	.byte	7
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,5,166,1,16,4,9
	.byte	'MAXDIV',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	26,2,2,35,2,9
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON5_Bits',0,5,172,1,3
	.word	29264
	.byte	7
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,5,175,1,16,4,9
	.byte	'CPU0DIV',0,1
	.word	612
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	2267
	.byte	26,0,2,35,2,0,5
	.byte	'Ifx_SCU_CCUCON6_Bits',0,5,179,1,3
	.word	29392
	.byte	7
	.byte	'_Ifx_SCU_CHIPID_Bits',0,5,182,1,16,4,9
	.byte	'CHREV',0,1
	.word	612
	.byte	6,2,2,35,0,9
	.byte	'CHTEC',0,1
	.word	612
	.byte	2,0,2,35,0,9
	.byte	'CHID',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'EEA',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'UCODE',0,1
	.word	612
	.byte	7,0,2,35,2,9
	.byte	'FSIZE',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'SP',0,1
	.word	612
	.byte	2,2,2,35,3,9
	.byte	'SEC',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CHIPID_Bits',0,5,193,1,3
	.word	29492
	.byte	7
	.byte	'_Ifx_SCU_DTSCON_Bits',0,5,196,1,16,4,9
	.byte	'PWD',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'START',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'CAL',0,4
	.word	2267
	.byte	22,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	5,1,2,35,3,9
	.byte	'SLCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_DTSCON_Bits',0,5,204,1,3
	.word	29700
	.byte	7
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,5,207,1,16,4,9
	.byte	'LOWER',0,2
	.word	1254
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	612
	.byte	5,1,2,35,1,9
	.byte	'LLU',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'UPPER',0,2
	.word	1254
	.byte	10,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	4,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'UOF',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_DTSLIM_Bits',0,5,216,1,3
	.word	29865
	.byte	7
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,5,219,1,16,4,9
	.byte	'RESULT',0,2
	.word	1254
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	612
	.byte	4,2,2,35,1,9
	.byte	'RDY',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'BUSY',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,5,226,1,3
	.word	30048
	.byte	7
	.byte	'_Ifx_SCU_EICR_Bits',0,5,229,1,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'EXIS0',0,1
	.word	612
	.byte	3,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'FEN0',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'REN0',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'LDEN0',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'EIEN0',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'INP0',0,1
	.word	612
	.byte	3,1,2,35,1,9
	.byte	'reserved_15',0,4
	.word	2267
	.byte	5,12,2,35,2,9
	.byte	'EXIS1',0,1
	.word	612
	.byte	3,1,2,35,2,9
	.byte	'reserved_23',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'FEN1',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'REN1',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'LDEN1',0,1
	.word	612
	.byte	1,5,2,35,3,9
	.byte	'EIEN1',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'INP1',0,1
	.word	612
	.byte	3,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EICR_Bits',0,5,248,1,3
	.word	30202
	.byte	7
	.byte	'_Ifx_SCU_EIFR_Bits',0,5,251,1,16,4,9
	.byte	'INTF0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'INTF1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'INTF2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'INTF3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'INTF4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'INTF5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'INTF6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'INTF7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	2267
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_SCU_EIFR_Bits',0,5,134,2,3
	.word	30566
	.byte	7
	.byte	'_Ifx_SCU_EMSR_Bits',0,5,137,2,16,4,9
	.byte	'POL',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'MODE',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'ENON',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'PSEL',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,2
	.word	1254
	.byte	12,0,2,35,0,9
	.byte	'EMSF',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'SEMSF',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	612
	.byte	6,0,2,35,2,9
	.byte	'EMSFM',0,1
	.word	612
	.byte	2,6,2,35,3,9
	.byte	'SEMSFM',0,1
	.word	612
	.byte	2,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_SCU_EMSR_Bits',0,5,150,2,3
	.word	30777
	.byte	7
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,5,153,2,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	7,1,2,35,0,9
	.byte	'EDCON',0,2
	.word	1254
	.byte	2,7,2,35,0,9
	.byte	'reserved_9',0,4
	.word	2267
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_SCU_ESRCFG_Bits',0,5,158,2,3
	.word	31029
	.byte	7
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,5,161,2,16,4,9
	.byte	'ARI',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ARC',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	2267
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_ESROCFG_Bits',0,5,166,2,3
	.word	31147
	.byte	7
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,5,169,2,16,4,9
	.byte	'reserved_0',0,4
	.word	2267
	.byte	28,4,2,35,2,9
	.byte	'EVR13OFF',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'BPEVR13OFF',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVR13CON_Bits',0,5,176,2,3
	.word	31258
	.byte	7
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,5,179,2,16,4,9
	.byte	'ADC13V',0,1
	.word	612
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'ADCSWDV',0,1
	.word	612
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,9
	.byte	'VAL',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,5,186,2,3
	.word	31421
	.byte	7
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,5,189,2,16,4,9
	.byte	'EVR13OVMOD',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'EVR13UVMOD',0,1
	.word	612
	.byte	2,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	1254
	.byte	10,0,2,35,0,9
	.byte	'SWDOVMOD',0,1
	.word	612
	.byte	2,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	612
	.byte	2,4,2,35,2,9
	.byte	'SWDUVMOD',0,1
	.word	612
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,2
	.word	1254
	.byte	8,2,2,35,2,9
	.byte	'SLCK',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,5,201,2,3
	.word	31583
	.byte	7
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,5,204,2,16,4,9
	.byte	'EVR13OVVAL',0,1
	.word	612
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'SWDOVVAL',0,1
	.word	612
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	6,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVROVMON_Bits',0,5,212,2,3
	.word	31861
	.byte	7
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,5,215,2,16,4,9
	.byte	'reserved_0',0,4
	.word	2267
	.byte	28,4,2,35,2,9
	.byte	'RSTSWDOFF',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'BPRSTSWDOFF',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,5,222,2,3
	.word	32040
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,5,225,2,16,4,9
	.byte	'SD33P',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'SD33I',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'reserved_12',0,4
	.word	2267
	.byte	19,1,2,35,2,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,5,232,2,3
	.word	32200
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,5,235,2,16,4,9
	.byte	'SDFREQSPRD',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'TON',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'TOFF',0,1
	.word	612
	.byte	8,0,2,35,2,9
	.byte	'SDSTEP',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'SYNCDIV',0,1
	.word	612
	.byte	3,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,5,244,2,3
	.word	32361
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,5,247,2,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	8,0,2,35,0,9
	.byte	'STBS',0,1
	.word	612
	.byte	2,6,2,35,1,9
	.byte	'STSP',0,1
	.word	612
	.byte	2,4,2,35,1,9
	.byte	'NS',0,1
	.word	612
	.byte	2,2,2,35,1,9
	.byte	'OL',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'PIAD',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'ADCMODE',0,1
	.word	612
	.byte	4,4,2,35,2,9
	.byte	'ADCLPF',0,1
	.word	612
	.byte	2,2,2,35,2,9
	.byte	'ADCLSB',0,1
	.word	612
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'SDLUT',0,1
	.word	612
	.byte	6,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,5,134,3,3
	.word	32553
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,5,137,3,16,4,9
	.byte	'SDOLCON',0,1
	.word	612
	.byte	7,1,2,35,0,9
	.byte	'MODSEL',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'MODLOW',0,1
	.word	612
	.byte	7,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'SDVOKLVL',0,1
	.word	612
	.byte	6,2,2,35,2,9
	.byte	'MODMAN',0,1
	.word	612
	.byte	2,0,2,35,2,9
	.byte	'MODHIGH',0,1
	.word	612
	.byte	7,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,5,147,3,3
	.word	32849
	.byte	7
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,5,150,3,16,4,9
	.byte	'EVR13',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'OV13',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'OVSWD',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'UV13',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'UVSWD',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	2,6,2,35,1,9
	.byte	'BGPROK',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'reserved_11',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'SCMOD',0,1
	.word	612
	.byte	2,2,2,35,1,9
	.byte	'reserved_14',0,4
	.word	2267
	.byte	18,0,2,35,2,0,5
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,5,164,3,3
	.word	33064
	.byte	7
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,5,167,3,16,4,9
	.byte	'EVR13UVVAL',0,1
	.word	612
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'SWDUVVAL',0,1
	.word	612
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	6,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,5,175,3,3
	.word	33353
	.byte	7
	.byte	'_Ifx_SCU_EXTCON_Bits',0,5,178,3,16,4,9
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'SEL0',0,1
	.word	612
	.byte	4,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	1254
	.byte	10,0,2,35,0,9
	.byte	'EN1',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'NSEL',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'SEL1',0,1
	.word	612
	.byte	4,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	612
	.byte	2,0,2,35,2,9
	.byte	'DIV1',0,1
	.word	612
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_SCU_EXTCON_Bits',0,5,189,3,3
	.word	33532
	.byte	7
	.byte	'_Ifx_SCU_FDR_Bits',0,5,192,3,16,4,9
	.byte	'STEP',0,2
	.word	1254
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	612
	.byte	4,2,2,35,1,9
	.byte	'DM',0,1
	.word	612
	.byte	2,0,2,35,1,9
	.byte	'RESULT',0,2
	.word	1254
	.byte	10,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	5,1,2,35,3,9
	.byte	'DISCLK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_FDR_Bits',0,5,200,3,3
	.word	33750
	.byte	7
	.byte	'_Ifx_SCU_FMR_Bits',0,5,203,3,16,4,9
	.byte	'FS0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'FS1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'FS2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'FS3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'FS4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'FS5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'FS6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'FS7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'FC0',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'FC1',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'FC2',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'FC3',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'FC4',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'FC5',0,1
	.word	612
	.byte	1,2,2,35,2,9
	.byte	'FC6',0,1
	.word	612
	.byte	1,1,2,35,2,9
	.byte	'FC7',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_SCU_FMR_Bits',0,5,223,3,3
	.word	33913
	.byte	7
	.byte	'_Ifx_SCU_ID_Bits',0,5,226,3,16,4,9
	.byte	'MODREV',0,1
	.word	612
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_ID_Bits',0,5,231,3,3
	.word	34249
	.byte	7
	.byte	'_Ifx_SCU_IGCR_Bits',0,5,234,3,16,4,9
	.byte	'IPEN00',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'IPEN01',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'IPEN02',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'IPEN03',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'IPEN04',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'IPEN05',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'IPEN06',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'IPEN07',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	5,3,2,35,1,9
	.byte	'GEEN0',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'IGP0',0,1
	.word	612
	.byte	2,0,2,35,1,9
	.byte	'IPEN10',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'IPEN11',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'IPEN12',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'IPEN13',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'IPEN14',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'IPEN15',0,1
	.word	612
	.byte	1,2,2,35,2,9
	.byte	'IPEN16',0,1
	.word	612
	.byte	1,1,2,35,2,9
	.byte	'IPEN17',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	5,3,2,35,3,9
	.byte	'GEEN1',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'IGP1',0,1
	.word	612
	.byte	2,0,2,35,3,0,5
	.byte	'Ifx_SCU_IGCR_Bits',0,5,130,4,3
	.word	34356
	.byte	7
	.byte	'_Ifx_SCU_IN_Bits',0,5,133,4,16,4,9
	.byte	'P0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	2267
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_IN_Bits',0,5,138,4,3
	.word	34808
	.byte	7
	.byte	'_Ifx_SCU_IOCR_Bits',0,5,141,4,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	4,4,2,35,0,9
	.byte	'PC0',0,1
	.word	612
	.byte	4,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'PC1',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_IOCR_Bits',0,5,148,4,3
	.word	34907
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,5,151,4,16,4,9
	.byte	'LBISTREQ',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'LBISTREQP',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'PATTERNS',0,2
	.word	1254
	.byte	14,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,5,157,4,3
	.word	35057
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,5,160,4,16,4,9
	.byte	'SEED',0,4
	.word	2267
	.byte	23,9,2,35,2,9
	.byte	'reserved_23',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'SPLITSH',0,1
	.word	612
	.byte	3,5,2,35,3,9
	.byte	'BODY',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'LBISTFREQU',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,5,167,4,3
	.word	35206
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,5,170,4,16,4,9
	.byte	'SIGNATURE',0,4
	.word	2267
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,9
	.byte	'LBISTDONE',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,5,175,4,3
	.word	35367
	.byte	7
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,5,178,4,16,4,9
	.byte	'reserved_0',0,2
	.word	1254
	.byte	16,0,2,35,0,9
	.byte	'LS',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,2
	.word	1254
	.byte	14,1,2,35,2,9
	.byte	'LSEN',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_LCLCON0_Bits',0,5,184,4,3
	.word	35497
	.byte	7
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,5,187,4,16,4,9
	.byte	'LCLT0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'LCLT1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	2267
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_LCLTEST_Bits',0,5,192,4,3
	.word	35631
	.byte	7
	.byte	'_Ifx_SCU_MANID_Bits',0,5,195,4,16,4,9
	.byte	'DEPT',0,1
	.word	612
	.byte	5,3,2,35,0,9
	.byte	'MANUF',0,2
	.word	1254
	.byte	11,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_MANID_Bits',0,5,200,4,3
	.word	35746
	.byte	7
	.byte	'_Ifx_SCU_OMR_Bits',0,5,203,4,16,4,9
	.byte	'PS0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'PS1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,2
	.word	1254
	.byte	14,0,2,35,0,9
	.byte	'PCL0',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'PCL1',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	1254
	.byte	14,0,2,35,2,0,5
	.byte	'Ifx_SCU_OMR_Bits',0,5,211,4,3
	.word	35857
	.byte	7
	.byte	'_Ifx_SCU_OSCCON_Bits',0,5,214,4,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'PLLLV',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'OSCRES',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'GAINSEL',0,1
	.word	612
	.byte	2,3,2,35,0,9
	.byte	'MODE',0,1
	.word	612
	.byte	2,1,2,35,0,9
	.byte	'SHBY',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'PLLHV',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'X1D',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'X1DEN',0,1
	.word	612
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'OSCVAL',0,1
	.word	612
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,9
	.byte	'APREN',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_SCU_OSCCON_Bits',0,5,231,4,3
	.word	36015
	.byte	7
	.byte	'_Ifx_SCU_OUT_Bits',0,5,234,4,16,4,9
	.byte	'P0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	2267
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_OUT_Bits',0,5,239,4,3
	.word	36355
	.byte	7
	.byte	'_Ifx_SCU_OVCCON_Bits',0,5,242,4,16,4,9
	.byte	'CSEL0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'CSEL1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'CSEL2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,2
	.word	1254
	.byte	13,0,2,35,0,9
	.byte	'OVSTRT',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'OVSTP',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'DCINVAL',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	612
	.byte	5,0,2,35,2,9
	.byte	'OVCONF',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'POVCONF',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	6,0,2,35,3,0,5
	.byte	'Ifx_SCU_OVCCON_Bits',0,5,255,4,3
	.word	36456
	.byte	7
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,5,130,5,16,4,9
	.byte	'OVEN0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'OVEN1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'OVEN2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,4
	.word	2267
	.byte	29,0,2,35,2,0,5
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,5,136,5,3
	.word	36723
	.byte	7
	.byte	'_Ifx_SCU_PDISC_Bits',0,5,139,5,16,4,9
	.byte	'PDIS0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'PDIS1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	2267
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_PDISC_Bits',0,5,144,5,3
	.word	36859
	.byte	7
	.byte	'_Ifx_SCU_PDR_Bits',0,5,147,5,16,4,9
	.byte	'PD0',0,1
	.word	612
	.byte	3,5,2,35,0,9
	.byte	'PL0',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'PD1',0,1
	.word	612
	.byte	3,1,2,35,0,9
	.byte	'PL1',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	2267
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_SCU_PDR_Bits',0,5,154,5,3
	.word	36970
	.byte	7
	.byte	'_Ifx_SCU_PDRR_Bits',0,5,157,5,16,4,9
	.byte	'PDR0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'PDR1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'PDR2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'PDR3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'PDR4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'PDR5',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'PDR6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'PDR7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	2267
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_SCU_PDRR_Bits',0,5,168,5,3
	.word	37103
	.byte	7
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,5,171,5,16,4,9
	.byte	'VCOBYP',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'VCOPWD',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'MODEN',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'SETFINDIS',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'CLRFINDIS',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'OSCDISCDIS',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	1254
	.byte	2,7,2,35,0,9
	.byte	'NDIV',0,1
	.word	612
	.byte	7,0,2,35,1,9
	.byte	'PLLPWD',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'RESLD',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	612
	.byte	5,0,2,35,2,9
	.byte	'PDIV',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_SCU_PLLCON0_Bits',0,5,188,5,3
	.word	37306
	.byte	7
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,5,191,5,16,4,9
	.byte	'K2DIV',0,1
	.word	612
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'K3DIV',0,1
	.word	612
	.byte	7,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'K1DIV',0,1
	.word	612
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	1254
	.byte	9,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLCON1_Bits',0,5,199,5,3
	.word	37662
	.byte	7
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,5,202,5,16,4,9
	.byte	'MODCFG',0,2
	.word	1254
	.byte	16,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLCON2_Bits',0,5,206,5,3
	.word	37840
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,5,209,5,16,4,9
	.byte	'VCOBYP',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'VCOPWD',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'SETFINDIS',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'CLRFINDIS',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'OSCDISCDIS',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	1254
	.byte	2,7,2,35,0,9
	.byte	'NDIV',0,1
	.word	612
	.byte	5,2,2,35,1,9
	.byte	'reserved_14',0,1
	.word	612
	.byte	2,0,2,35,1,9
	.byte	'PLLPWD',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'RESLD',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	612
	.byte	5,0,2,35,2,9
	.byte	'PDIV',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,5,226,5,3
	.word	37940
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,5,229,5,16,4,9
	.byte	'K2DIV',0,1
	.word	612
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'K3DIV',0,1
	.word	612
	.byte	4,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,9
	.byte	'K1DIV',0,1
	.word	612
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	1254
	.byte	9,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,5,237,5,3
	.word	38310
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,5,240,5,16,4,9
	.byte	'VCOBYST',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'PWDSTAT',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'VCOLOCK',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'FINDIS',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'K1RDY',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'K2RDY',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	2267
	.byte	26,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,5,249,5,3
	.word	38496
	.byte	7
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,5,252,5,16,4,9
	.byte	'VCOBYST',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'VCOLOCK',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'FINDIS',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'K1RDY',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'K2RDY',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'MODRUN',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	2267
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,5,135,6,3
	.word	38694
	.byte	7
	.byte	'_Ifx_SCU_PMCSR_Bits',0,5,138,6,16,4,9
	.byte	'REQSLP',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'SMUSLP',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	612
	.byte	5,0,2,35,0,9
	.byte	'PMST',0,1
	.word	612
	.byte	3,5,2,35,1,9
	.byte	'reserved_11',0,4
	.word	2267
	.byte	21,0,2,35,2,0,5
	.byte	'Ifx_SCU_PMCSR_Bits',0,5,145,6,3
	.word	38927
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,5,148,6,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ESR1WKEN',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'PINAWKEN',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'PINBWKEN',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'ESR0DFEN',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'ESR0EDCON',0,1
	.word	612
	.byte	2,1,2,35,0,9
	.byte	'ESR1DFEN',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'ESR1EDCON',0,1
	.word	612
	.byte	2,6,2,35,1,9
	.byte	'PINADFEN',0,1
	.word	612
	.byte	1,5,2,35,1,9
	.byte	'PINAEDCON',0,1
	.word	612
	.byte	2,3,2,35,1,9
	.byte	'PINBDFEN',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'PINBEDCON',0,1
	.word	612
	.byte	2,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'STBYRAMSEL',0,1
	.word	612
	.byte	2,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'WUTWKEN',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,9
	.byte	'PORSTDF',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'DCDCSYNC',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	3,3,2,35,3,9
	.byte	'ESR0TRIST',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,5,174,6,3
	.word	39079
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,5,177,6,16,4,9
	.byte	'reserved_0',0,2
	.word	1254
	.byte	12,4,2,35,0,9
	.byte	'IRADIS',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'reserved_13',0,4
	.word	2267
	.byte	14,5,2,35,2,9
	.byte	'STBYEVEN',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'STBYEV',0,1
	.word	612
	.byte	3,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,5,185,6,3
	.word	39638
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,5,188,6,16,4,9
	.byte	'WUTREL',0,4
	.word	2267
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	4,4,2,35,3,9
	.byte	'WUTDIV',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'WUTEN',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'WUTMODE',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,5,196,6,3
	.word	39821
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,5,199,6,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'ESR1WKP',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'ESR1OVRUN',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'PINAWKP',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'PINAOVRUN',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'PINBWKP',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'PINBOVRUN',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'PORSTDF',0,1
	.word	612
	.byte	1,6,2,35,1,9
	.byte	'HWCFGEVR',0,1
	.word	612
	.byte	3,3,2,35,1,9
	.byte	'STBYRAM',0,1
	.word	612
	.byte	2,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'WUTWKP',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'WUTOVRUN',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'WUTWKEN',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'ESR1WKEN',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'PINAWKEN',0,1
	.word	612
	.byte	1,2,2,35,2,9
	.byte	'PINBWKEN',0,1
	.word	612
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	1254
	.byte	4,5,2,35,2,9
	.byte	'ESR0TRIST',0,1
	.word	612
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'WUTEN',0,1
	.word	612
	.byte	1,2,2,35,3,9
	.byte	'WUTMODE',0,1
	.word	612
	.byte	1,1,2,35,3,9
	.byte	'WUTRUN',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,5,226,6,3
	.word	39990
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,5,229,6,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'ESR1WKPCLR',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'ESR1OVRUNCLR',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'PINAWKPCLR',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'PINAOVRUNCLR',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'PINBWKPCLR',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'PINBOVRUNCLR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'WUTWKPCLR',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'WUTOVRUNCLR',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	1254
	.byte	14,0,2,35,2,0,5
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,5,242,6,3
	.word	40557
	.byte	7
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,5,245,6,16,4,9
	.byte	'WUTCNT',0,4
	.word	2267
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,9
	.byte	'VAL',0,1
	.word	612
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,5,250,6,3
	.word	40873
	.byte	7
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,5,253,6,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'CLRC',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,2
	.word	1254
	.byte	10,4,2,35,0,9
	.byte	'CSS0',0,1
	.word	612
	.byte	1,3,2,35,1,9
	.byte	'CSS1',0,1
	.word	612
	.byte	1,2,2,35,1,9
	.byte	'CSS2',0,1
	.word	612
	.byte	1,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'USRINFO',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_RSTCON2_Bits',0,5,135,7,3
	.word	40992
	.byte	7
	.byte	'_Ifx_SCU_RSTCON_Bits',0,5,138,7,16,4,9
	.byte	'ESR0',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'ESR1',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	2,2,2,35,0,9
	.byte	'SMU',0,1
	.word	612
	.byte	2,0,2,35,0,9
	.byte	'SW',0,1
	.word	612
	.byte	2,6,2,35,1,9
	.byte	'STM0',0,1
	.word	612
	.byte	2,4,2,35,1,9
	.byte	'STM1',0,1
	.word	612
	.byte	2,2,2,35,1,9
	.byte	'STM2',0,1
	.word	612
	.byte	2,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_RSTCON_Bits',0,5,149,7,3
	.word	41201
	.byte	7
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,5,152,7,16,4,9
	.byte	'ESR0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ESR1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'SMU',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'SW',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'STM0',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'STM1',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'STM2',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,9
	.byte	'PORST',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'CB0',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'CB1',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'CB3',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,9
	.byte	'EVR13',0,1
	.word	612
	.byte	1,0,2,35,2,9
	.byte	'EVR33',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'SWD',0,1
	.word	612
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	612
	.byte	2,4,2,35,3,9
	.byte	'STBYR',0,1
	.word	612
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	612
	.byte	3,0,2,35,3,0,5
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,5,175,7,3
	.word	41412
	.byte	7
	.byte	'_Ifx_SCU_SAFECON_Bits',0,5,178,7,16,4,9
	.byte	'HBT',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	2267
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_SCU_SAFECON_Bits',0,5,182,7,3
	.word	41844
	.byte	7
	.byte	'_Ifx_SCU_STSTAT_Bits',0,5,185,7,16,4,9
	.byte	'HWCFG',0,1
	.word	612
	.byte	8,0,2,35,0,9
	.byte	'FTM',0,1
	.word	612
	.byte	7,1,2,35,1,9
	.byte	'MODE',0,1
	.word	612
	.byte	1,0,2,35,1,9
	.byte	'FCBAE',0,1
	.word	612
	.byte	1,7,2,35,2,9
	.byte	'LUDIS',0,1
	.word	612
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	612
	.byte	1,5,2,35,2,9
	.byte	'TRSTL',0,1
	.word	612
	.byte	1,4,2,35,2,9
	.byte	'SPDEN',0,1
	.word	612
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	612
	.byte	3,0,2,35,2,9
	.byte	'RAMINT',0,1
	.word	612
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	612
	.byte	7,0,2,35,3,0,5
	.byte	'Ifx_SCU_STSTAT_Bits',0,5,198,7,3
	.word	41940
	.byte	7
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,5,201,7,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'SWRSTREQ',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	2267
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,5,206,7,3
	.word	42200
	.byte	7
	.byte	'_Ifx_SCU_SYSCON_Bits',0,5,209,7,16,4,9
	.byte	'CCTRIG0',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'RAMINTM',0,1
	.word	612
	.byte	2,4,2,35,0,9
	.byte	'SETLUDIS',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	612
	.byte	3,0,2,35,0,9
	.byte	'DATM',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,4
	.word	2267
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_SCU_SYSCON_Bits',0,5,218,7,3
	.word	42325
	.byte	7
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,5,221,7,16,4,9
	.byte	'ESR0T',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,5,228,7,3
	.word	42522
	.byte	7
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,5,231,7,16,4,9
	.byte	'ESR0T',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,5,238,7,3
	.word	42675
	.byte	7
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,5,241,7,16,4,9
	.byte	'ESR0T',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_SCU_TRAPSET_Bits',0,5,248,7,3
	.word	42828
	.byte	7
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,5,251,7,16,4,9
	.byte	'ESR0T',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	2267
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,5,130,8,3
	.word	42981
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,5,133,8,16,4,8
	.byte	'unsigned int',0,4,7,9
	.byte	'ENDINIT',0,4
	.word	43168
	.byte	1,31,2,35,0,9
	.byte	'LCK',0,4
	.word	43168
	.byte	1,30,2,35,0,9
	.byte	'PW',0,4
	.word	43168
	.byte	14,16,2,35,0,9
	.byte	'REL',0,4
	.word	43168
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,5,139,8,3
	.word	43136
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,5,142,8,16,4,9
	.byte	'reserved_0',0,1
	.word	612
	.byte	2,6,2,35,0,9
	.byte	'IR0',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'DR',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'IR1',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'UR',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'PAR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'TCR',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'TCTR',0,1
	.word	612
	.byte	7,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,5,154,8,3
	.word	43282
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,5,157,8,16,4,9
	.byte	'AE',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'OE',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'IS0',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'DS',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'TO',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'IS1',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'US',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'PAS',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'TCS',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'TCT',0,1
	.word	612
	.byte	7,0,2,35,1,9
	.byte	'TIM',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,5,170,8,3
	.word	43520
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,5,173,8,16,4,9
	.byte	'ENDINIT',0,4
	.word	43168
	.byte	1,31,2,35,0,9
	.byte	'LCK',0,4
	.word	43168
	.byte	1,30,2,35,0,9
	.byte	'PW',0,4
	.word	43168
	.byte	14,16,2,35,0,9
	.byte	'REL',0,4
	.word	43168
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,5,179,8,3
	.word	43743
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,5,182,8,16,4,9
	.byte	'CLRIRF',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'IR0',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'DR',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'IR1',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'UR',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'PAR',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'TCR',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'TCTR',0,1
	.word	612
	.byte	7,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,5,195,8,3
	.word	43869
	.byte	7
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,5,198,8,16,4,9
	.byte	'AE',0,1
	.word	612
	.byte	1,7,2,35,0,9
	.byte	'OE',0,1
	.word	612
	.byte	1,6,2,35,0,9
	.byte	'IS0',0,1
	.word	612
	.byte	1,5,2,35,0,9
	.byte	'DS',0,1
	.word	612
	.byte	1,4,2,35,0,9
	.byte	'TO',0,1
	.word	612
	.byte	1,3,2,35,0,9
	.byte	'IS1',0,1
	.word	612
	.byte	1,2,2,35,0,9
	.byte	'US',0,1
	.word	612
	.byte	1,1,2,35,0,9
	.byte	'PAS',0,1
	.word	612
	.byte	1,0,2,35,0,9
	.byte	'TCS',0,1
	.word	612
	.byte	1,7,2,35,1,9
	.byte	'TCT',0,1
	.word	612
	.byte	7,0,2,35,1,9
	.byte	'TIM',0,2
	.word	1254
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,5,211,8,3
	.word	44121
	.byte	10,5,219,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	27400
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ACCEN0',0,5,224,8,3
	.word	44340
	.byte	10,5,227,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	27957
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ACCEN1',0,5,232,8,3
	.word	44404
	.byte	10,5,235,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28034
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ARSTDIS',0,5,240,8,3
	.word	44468
	.byte	10,5,243,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28170
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON0',0,5,248,8,3
	.word	44533
	.byte	10,5,251,8,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28452
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON1',0,5,128,9,3
	.word	44598
	.byte	10,5,131,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28690
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON2',0,5,136,9,3
	.word	44663
	.byte	10,5,139,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28818
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON3',0,5,144,9,3
	.word	44728
	.byte	10,5,147,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29045
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON4',0,5,152,9,3
	.word	44793
	.byte	10,5,155,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29264
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON5',0,5,160,9,3
	.word	44858
	.byte	10,5,163,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29392
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON6',0,5,168,9,3
	.word	44923
	.byte	10,5,171,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29492
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CHIPID',0,5,176,9,3
	.word	44988
	.byte	10,5,179,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29700
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_DTSCON',0,5,184,9,3
	.word	45052
	.byte	10,5,187,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29865
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_DTSLIM',0,5,192,9,3
	.word	45116
	.byte	10,5,195,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	30048
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_DTSSTAT',0,5,200,9,3
	.word	45180
	.byte	10,5,203,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	30202
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EICR',0,5,208,9,3
	.word	45245
	.byte	10,5,211,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	30566
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EIFR',0,5,216,9,3
	.word	45307
	.byte	10,5,219,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	30777
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EMSR',0,5,224,9,3
	.word	45369
	.byte	10,5,227,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31029
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ESRCFG',0,5,232,9,3
	.word	45431
	.byte	10,5,235,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31147
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ESROCFG',0,5,240,9,3
	.word	45495
	.byte	10,5,243,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31258
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVR13CON',0,5,248,9,3
	.word	45560
	.byte	10,5,251,9,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31421
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRADCSTAT',0,5,128,10,3
	.word	45626
	.byte	10,5,131,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31583
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRMONCTRL',0,5,136,10,3
	.word	45694
	.byte	10,5,139,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31861
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVROVMON',0,5,144,10,3
	.word	45762
	.byte	10,5,147,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32040
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRRSTCON',0,5,152,10,3
	.word	45828
	.byte	10,5,155,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32200
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,5,160,10,3
	.word	45895
	.byte	10,5,163,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32361
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSDCTRL1',0,5,168,10,3
	.word	45964
	.byte	10,5,171,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32553
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSDCTRL2',0,5,176,10,3
	.word	46032
	.byte	10,5,179,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32849
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSDCTRL3',0,5,184,10,3
	.word	46100
	.byte	10,5,187,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33064
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSTAT',0,5,192,10,3
	.word	46168
	.byte	10,5,195,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33353
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRUVMON',0,5,200,10,3
	.word	46233
	.byte	10,5,203,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33532
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EXTCON',0,5,208,10,3
	.word	46299
	.byte	10,5,211,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33750
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_FDR',0,5,216,10,3
	.word	46363
	.byte	10,5,219,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33913
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_FMR',0,5,224,10,3
	.word	46424
	.byte	10,5,227,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34249
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ID',0,5,232,10,3
	.word	46485
	.byte	10,5,235,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34356
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_IGCR',0,5,240,10,3
	.word	46545
	.byte	10,5,243,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34808
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_IN',0,5,248,10,3
	.word	46607
	.byte	10,5,251,10,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34907
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_IOCR',0,5,128,11,3
	.word	46667
	.byte	10,5,131,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35057
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LBISTCTRL0',0,5,136,11,3
	.word	46729
	.byte	10,5,139,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35206
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LBISTCTRL1',0,5,144,11,3
	.word	46797
	.byte	10,5,147,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35367
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LBISTCTRL2',0,5,152,11,3
	.word	46865
	.byte	10,5,155,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35497
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LCLCON0',0,5,160,11,3
	.word	46933
	.byte	10,5,163,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35631
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LCLTEST',0,5,168,11,3
	.word	46998
	.byte	10,5,171,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35746
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_MANID',0,5,176,11,3
	.word	47063
	.byte	10,5,179,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35857
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OMR',0,5,184,11,3
	.word	47126
	.byte	10,5,187,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36015
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OSCCON',0,5,192,11,3
	.word	47187
	.byte	10,5,195,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36355
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OUT',0,5,200,11,3
	.word	47251
	.byte	10,5,203,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36456
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OVCCON',0,5,208,11,3
	.word	47312
	.byte	10,5,211,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36723
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OVCENABLE',0,5,216,11,3
	.word	47376
	.byte	10,5,219,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36859
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PDISC',0,5,224,11,3
	.word	47443
	.byte	10,5,227,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36970
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PDR',0,5,232,11,3
	.word	47506
	.byte	10,5,235,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37103
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PDRR',0,5,240,11,3
	.word	47567
	.byte	10,5,243,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37306
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLCON0',0,5,248,11,3
	.word	47629
	.byte	10,5,251,11,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37662
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLCON1',0,5,128,12,3
	.word	47694
	.byte	10,5,131,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37840
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLCON2',0,5,136,12,3
	.word	47759
	.byte	10,5,139,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37940
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLERAYCON0',0,5,144,12,3
	.word	47824
	.byte	10,5,147,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38310
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLERAYCON1',0,5,152,12,3
	.word	47893
	.byte	10,5,155,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38496
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLERAYSTAT',0,5,160,12,3
	.word	47962
	.byte	10,5,163,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38694
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLSTAT',0,5,168,12,3
	.word	48031
	.byte	10,5,171,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38927
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMCSR',0,5,176,12,3
	.word	48096
	.byte	10,5,179,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39079
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWCR0',0,5,184,12,3
	.word	48159
	.byte	10,5,187,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39638
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWCR1',0,5,192,12,3
	.word	48224
	.byte	10,5,195,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39821
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWCR3',0,5,200,12,3
	.word	48289
	.byte	10,5,203,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39990
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWSTAT',0,5,208,12,3
	.word	48354
	.byte	10,5,211,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40557
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWSTATCLR',0,5,216,12,3
	.word	48420
	.byte	10,5,219,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40873
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWUTCNT',0,5,224,12,3
	.word	48489
	.byte	10,5,227,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41201
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_RSTCON',0,5,232,12,3
	.word	48556
	.byte	10,5,235,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40992
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_RSTCON2',0,5,240,12,3
	.word	48620
	.byte	10,5,243,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41412
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_RSTSTAT',0,5,248,12,3
	.word	48685
	.byte	10,5,251,12,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41844
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_SAFECON',0,5,128,13,3
	.word	48750
	.byte	10,5,131,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41940
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_STSTAT',0,5,136,13,3
	.word	48815
	.byte	10,5,139,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42200
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_SWRSTCON',0,5,144,13,3
	.word	48879
	.byte	10,5,147,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42325
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_SYSCON',0,5,152,13,3
	.word	48945
	.byte	10,5,155,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42522
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_TRAPCLR',0,5,160,13,3
	.word	49009
	.byte	10,5,163,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42675
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_TRAPDIS',0,5,168,13,3
	.word	49074
	.byte	10,5,171,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42828
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_TRAPSET',0,5,176,13,3
	.word	49139
	.byte	10,5,179,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42981
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_TRAPSTAT',0,5,184,13,3
	.word	49204
	.byte	10,5,187,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43136
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTCPU_CON0',0,5,192,13,3
	.word	49270
	.byte	10,5,195,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43282
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTCPU_CON1',0,5,200,13,3
	.word	49339
	.byte	10,5,203,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43520
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTCPU_SR',0,5,208,13,3
	.word	49408
	.byte	10,5,211,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43743
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTS_CON0',0,5,216,13,3
	.word	49475
	.byte	10,5,219,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43869
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTS_CON1',0,5,224,13,3
	.word	49542
	.byte	10,5,227,13,9,4,11
	.byte	'U',0,4
	.word	2267
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	16557
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	44121
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTS_SR',0,5,232,13,3
	.word	49609
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU',0,5,243,13,25,12,11
	.byte	'CON0',0,4
	.word	49270
	.byte	2,35,0,11
	.byte	'CON1',0,4
	.word	49339
	.byte	2,35,4,11
	.byte	'SR',0,4
	.word	49408
	.byte	2,35,8,0,15
	.word	49674
	.byte	5
	.byte	'Ifx_SCU_WDTCPU',0,5,248,13,3
	.word	49737
	.byte	7
	.byte	'_Ifx_SCU_WDTS',0,5,251,13,25,12,11
	.byte	'CON0',0,4
	.word	49475
	.byte	2,35,0,11
	.byte	'CON1',0,4
	.word	49542
	.byte	2,35,4,11
	.byte	'SR',0,4
	.word	49609
	.byte	2,35,8,0,15
	.word	49766
	.byte	5
	.byte	'Ifx_SCU_WDTS',0,5,128,14,3
	.word	49827
	.byte	5
	.byte	'uint8',0,6,90,29
	.word	612
	.byte	5
	.byte	'uint16',0,6,92,29
	.word	1254
	.byte	8
	.byte	'unsigned long int',0,4,7,5
	.byte	'uint32',0,6,94,29
	.word	49883
	.byte	5
	.byte	'Adc_ChannelType',0,7,227,2,15
	.word	612
	.byte	5
	.byte	'Adc_GroupType',0,7,233,2,16
	.word	1254
	.byte	5
	.byte	'Adc_ValueGroupType',0,7,247,2,16
	.word	1254
	.byte	5
	.byte	'Adc_PrescaleType',0,7,144,3,16
	.word	49883
	.byte	5
	.byte	'Adc_StatusType',0,7,192,3,15
	.word	612
	.byte	5
	.byte	'Adc_TriggerSourceType',0,7,200,3,15
	.word	612
	.byte	5
	.byte	'Adc_GroupConvModeType',0,7,208,3,15
	.word	612
	.byte	5
	.byte	'Adc_GroupPriorityType',0,7,215,3,15
	.word	612
	.byte	5
	.byte	'Adc_GroupDefType',0,7,130,4,25
	.word	612
	.byte	5
	.byte	'Adc_StreamNumSampleType',0,7,135,4,15
	.word	612
	.byte	5
	.byte	'Adc_StreamBufferModeType',0,7,148,4,15
	.word	612
	.byte	5
	.byte	'Adc_TriggSrcArbLevelType',0,7,179,4,16
	.word	49883
	.byte	7
	.byte	'Adc_TriggSrcData',0,7,205,4,16,4,11
	.byte	'GrpId',0,2
	.word	1254
	.byte	2,35,0,11
	.byte	'GrpPriority',0,1
	.word	612
	.byte	2,35,2,11
	.byte	'IsrDoNothing',0,1
	.word	612
	.byte	2,35,3,0,5
	.byte	'Adc_TriggSrcDataType',0,7,223,4,2
	.word	50265
	.byte	7
	.byte	'Adc_GroupData',0,7,232,4,16,32,12,16
	.word	612
	.byte	13,15,0,11
	.byte	'GrpChannels',0,16
	.word	50397
	.byte	2,35,0,12,16
	.word	612
	.byte	13,15,0,11
	.byte	'GrpChannelRes',0,16
	.word	50427
	.byte	2,35,16,0,5
	.byte	'Adc_GroupDataType',0,7,250,4,2
	.word	50377
	.byte	7
	.byte	'Adc_GlobalData',0,7,157,5,16,164,1,4
	.word	1254
	.byte	12,4
	.word	50509
	.byte	13,0,0,11
	.byte	'GroupResultBuffer',0,4
	.word	50514
	.byte	2,35,0,11
	.byte	'GroupStatus',0,4
	.word	49883
	.byte	2,35,4,11
	.byte	'GroupResultStatus',0,4
	.word	49883
	.byte	2,35,8,11
	.byte	'GrpBufferEndResultStatus',0,4
	.word	49883
	.byte	2,35,12,12,16
	.word	50265
	.byte	13,3,0,11
	.byte	'TriggSrcData',0,16
	.word	50632
	.byte	2,35,16,12,128,1
	.word	50377
	.byte	13,3,0,11
	.byte	'RsGroupData',0,128,1
	.word	50663
	.byte	2,35,32,12,1
	.word	612
	.byte	13,0,0,11
	.byte	'NumValidConRes',0,1
	.word	50695
	.byte	3,35,160,1,0,5
	.byte	'Adc_GlobalDataType',0,7,255,5,2
	.word	50487
	.byte	7
	.byte	'Adc_HwUnitCfgType',0,7,138,6,16,20,11
	.byte	'ArbitrationLength',0,4
	.word	49883
	.byte	2,35,0,11
	.byte	'TriggSrcArbLevel',0,4
	.word	49883
	.byte	2,35,4,12,8
	.word	49883
	.byte	13,1,0,11
	.byte	'KernelInputClass',0,8
	.word	50835
	.byte	2,35,8,11
	.byte	'SyncConvMode',0,1
	.word	612
	.byte	2,35,16,11
	.byte	'SlaveReady',0,1
	.word	612
	.byte	2,35,17,11
	.byte	'DmaChannel',0,1
	.word	612
	.byte	2,35,18,0,5
	.byte	'Adc_HwUnitCfgType',0,7,179,6,2
	.word	50758
	.byte	7
	.byte	'Adc_GroupCfgType',0,7,182,6,16,12,16
	.word	612
	.byte	4
	.word	50983
	.byte	11
	.byte	'GroupDefinition',0,4
	.word	50988
	.byte	2,35,0,11
	.byte	'IntChMask',0,2
	.word	1254
	.byte	2,35,4,11
	.byte	'TriggSrc',0,1
	.word	612
	.byte	2,35,6,11
	.byte	'GrpRequestSrc',0,1
	.word	612
	.byte	2,35,7,11
	.byte	'ConvMode',0,1
	.word	612
	.byte	2,35,8,11
	.byte	'NumSamples',0,1
	.word	612
	.byte	2,35,9,11
	.byte	'StreamBufferMode',0,1
	.word	612
	.byte	2,35,10,0,5
	.byte	'Adc_GroupCfgType',0,7,129,7,3
	.word	50960
	.byte	7
	.byte	'Adc_ChannelCfgType',0,7,136,7,17,28,11
	.byte	'AdcChConfigValue',0,4
	.word	49883
	.byte	2,35,0,11
	.byte	'AdcChResAccumulation',0,4
	.word	49883
	.byte	2,35,4,11
	.byte	'AdcIsChLimitChkEnabled',0,4
	.word	49883
	.byte	2,35,8,11
	.byte	'AdcLimitChkRange',0,4
	.word	49883
	.byte	2,35,12,11
	.byte	'AdcLimitChkBnd',0,4
	.word	49883
	.byte	2,35,16,11
	.byte	'AdcChBndSelxValue',0,4
	.word	49883
	.byte	2,35,20,11
	.byte	'AdcSyncChannel',0,1
	.word	612
	.byte	2,35,24,0,5
	.byte	'Adc_ChannelCfgType',0,7,160,7,3
	.word	51169
	.byte	7
	.byte	'Adc_KernelConfigType',0,7,163,7,17,16,16
	.word	50758
	.byte	4
	.word	51439
	.byte	11
	.byte	'HwCfgPtr',0,4
	.word	51444
	.byte	2,35,0,16
	.word	51169
	.byte	4
	.word	51467
	.byte	11
	.byte	'ChCfgPtr',0,4
	.word	51472
	.byte	2,35,4,16
	.word	50960
	.byte	4
	.word	51495
	.byte	11
	.byte	'GrpCfgPtr',0,4
	.word	51500
	.byte	2,35,8,11
	.byte	'TotCfgGrps',0,1
	.word	612
	.byte	2,35,12,0,5
	.byte	'Adc_KernelConfigType',0,7,183,7,3
	.word	51412
	.byte	7
	.byte	'Adc_GlobalCfgType',0,7,186,7,17,28,11
	.byte	'ClkPrescale',0,4
	.word	49883
	.byte	2,35,0,11
	.byte	'GlobInputClass',0,8
	.word	50835
	.byte	2,35,4,11
	.byte	'PostCalEnable',0,4
	.word	49883
	.byte	2,35,12,11
	.byte	'LowPowerSupply',0,4
	.word	49883
	.byte	2,35,16,11
	.byte	'RefPrechargeCtrl',0,4
	.word	49883
	.byte	2,35,20,11
	.byte	'OperationMode',0,1
	.word	612
	.byte	2,35,24,0,5
	.byte	'Adc_GlobalCfgType',0,7,244,7,3
	.word	51575
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L24:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,59,0,3,8,0,0,4,15,0,73,19,0,0,5,22,0,3,8,58,15,59,15,57,15,73,19,0,0,6,21,0,54,15,0,0,7,19,1,3,8,58
	.byte	15,59,15,57,15,11,15,0,0,8,36,0,3,8,11,15,62,15,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,23
	.byte	1,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,56,9,0,0,12,1,1,11,15,73,19,0,0,13,33,0,47,15,0
	.byte	0,14,13,0,11,15,73,19,56,9,0,0,15,53,0,73,19,0,0,16,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L25:
	.word	.L87-.L86
.L86:
	.half	3
	.word	.L89-.L88
.L88:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM_Adc.h',0,0,0,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0
	.byte	'..\\mcal_src\\IfxVadc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Adc.h',0,0,0,0,0
.L89:
.L87:
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmEnterStartGroup')
	.sect	'.debug_info'
.L26:
	.word	237
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L29,.L28
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmEnterStartGroup',0,1,177,1,6,1,1,1
	.word	.L3,.L76,.L2
	.byte	4
	.word	.L3,.L76
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmEnterStartGroup')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmEnterStartGroup')
	.sect	'.debug_line'
.L28:
	.word	.L91-.L90
.L90:
	.half	3
	.word	.L93-.L92
.L92:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L93:
	.byte	5,28,7,0,5,2
	.word	.L3
	.byte	3,178,1,1,5,1,7,9
	.half	.L30-.L3
	.byte	3,1,0,1,1
.L91:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmEnterStartGroup')
	.sect	'.debug_ranges'
.L29:
	.word	-1,.L3,0,.L30-.L3,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmExitStartGroup')
	.sect	'.debug_info'
.L31:
	.word	236
	.half	3
	.word	.L32
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L34,.L33
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmExitStartGroup',0,1,202,1,6,1,1,1
	.word	.L5,.L77,.L4
	.byte	4
	.word	.L5,.L77
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmExitStartGroup')
	.sect	'.debug_abbrev'
.L32:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmExitStartGroup')
	.sect	'.debug_line'
.L33:
	.word	.L95-.L94
.L94:
	.half	3
	.word	.L97-.L96
.L96:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L97:
	.byte	5,27,7,0,5,2
	.word	.L5
	.byte	3,203,1,1,5,1,7,9
	.half	.L35-.L5
	.byte	3,1,0,1,1
.L95:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmExitStartGroup')
	.sect	'.debug_ranges'
.L34:
	.word	-1,.L5,0,.L35-.L5,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmEnterStopGroup')
	.sect	'.debug_info'
.L36:
	.word	236
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L39,.L38
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmEnterStopGroup',0,1,227,1,6,1,1,1
	.word	.L7,.L78,.L6
	.byte	4
	.word	.L7,.L78
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmEnterStopGroup')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmEnterStopGroup')
	.sect	'.debug_line'
.L38:
	.word	.L99-.L98
.L98:
	.half	3
	.word	.L101-.L100
.L100:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L101:
	.byte	5,27,7,0,5,2
	.word	.L7
	.byte	3,228,1,1,5,1,7,9
	.half	.L40-.L7
	.byte	3,1,0,1,1
.L99:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmEnterStopGroup')
	.sect	'.debug_ranges'
.L39:
	.word	-1,.L7,0,.L40-.L7,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmExitStopGroup')
	.sect	'.debug_info'
.L41:
	.word	235
	.half	3
	.word	.L42
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L44,.L43
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmExitStopGroup',0,1,252,1,6,1,1,1
	.word	.L9,.L79,.L8
	.byte	4
	.word	.L9,.L79
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmExitStopGroup')
	.sect	'.debug_abbrev'
.L42:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmExitStopGroup')
	.sect	'.debug_line'
.L43:
	.word	.L103-.L102
.L102:
	.half	3
	.word	.L105-.L104
.L104:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L105:
	.byte	5,26,7,0,5,2
	.word	.L9
	.byte	3,253,1,1,5,1,7,9
	.half	.L45-.L9
	.byte	3,1,0,1,1
.L103:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmExitStopGroup')
	.sect	'.debug_ranges'
.L44:
	.word	-1,.L9,0,.L45-.L9,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmEnterGetGrpStatus')
	.sect	'.debug_info'
.L46:
	.word	239
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L49,.L48
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmEnterGetGrpStatus',0,1,254,2,6,1,1,1
	.word	.L11,.L80,.L10
	.byte	4
	.word	.L11,.L80
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmEnterGetGrpStatus')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmEnterGetGrpStatus')
	.sect	'.debug_line'
.L48:
	.word	.L107-.L106
.L106:
	.half	3
	.word	.L109-.L108
.L108:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L109:
	.byte	5,30,7,0,5,2
	.word	.L11
	.byte	3,255,2,1,5,1,7,9
	.half	.L50-.L11
	.byte	3,1,0,1,1
.L107:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmEnterGetGrpStatus')
	.sect	'.debug_ranges'
.L49:
	.word	-1,.L11,0,.L50-.L11,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmExitGetGrpStatus')
	.sect	'.debug_info'
.L51:
	.word	238
	.half	3
	.word	.L52
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L54,.L53
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmExitGetGrpStatus',0,1,151,3,6,1,1,1
	.word	.L13,.L81,.L12
	.byte	4
	.word	.L13,.L81
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmExitGetGrpStatus')
	.sect	'.debug_abbrev'
.L52:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmExitGetGrpStatus')
	.sect	'.debug_line'
.L53:
	.word	.L111-.L110
.L110:
	.half	3
	.word	.L113-.L112
.L112:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L113:
	.byte	5,29,7,0,5,2
	.word	.L13
	.byte	3,152,3,1,5,1,7,9
	.half	.L55-.L13
	.byte	3,1,0,1,1
.L111:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmExitGetGrpStatus')
	.sect	'.debug_ranges'
.L54:
	.word	-1,.L13,0,.L55-.L13,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmEnterGetStreamLastPtr')
	.sect	'.debug_info'
.L56:
	.word	243
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L59,.L58
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmEnterGetStreamLastPtr',0,1,178,3,6,1,1,1
	.word	.L15,.L82,.L14
	.byte	4
	.word	.L15,.L82
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmEnterGetStreamLastPtr')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmEnterGetStreamLastPtr')
	.sect	'.debug_line'
.L58:
	.word	.L115-.L114
.L114:
	.half	3
	.word	.L117-.L116
.L116:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L117:
	.byte	5,34,7,0,5,2
	.word	.L15
	.byte	3,179,3,1,5,1,7,9
	.half	.L60-.L15
	.byte	3,1,0,1,1
.L115:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmEnterGetStreamLastPtr')
	.sect	'.debug_ranges'
.L59:
	.word	-1,.L15,0,.L60-.L15,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmExitGetStreamLastPtr')
	.sect	'.debug_info'
.L61:
	.word	242
	.half	3
	.word	.L62
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L64,.L63
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmExitGetStreamLastPtr',0,1,203,3,6,1,1,1
	.word	.L17,.L83,.L16
	.byte	4
	.word	.L17,.L83
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmExitGetStreamLastPtr')
	.sect	'.debug_abbrev'
.L62:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmExitGetStreamLastPtr')
	.sect	'.debug_line'
.L63:
	.word	.L119-.L118
.L118:
	.half	3
	.word	.L121-.L120
.L120:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L121:
	.byte	5,33,7,0,5,2
	.word	.L17
	.byte	3,204,3,1,5,1,7,9
	.half	.L65-.L17
	.byte	3,1,0,1,1
.L119:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmExitGetStreamLastPtr')
	.sect	'.debug_ranges'
.L64:
	.word	-1,.L17,0,.L65-.L17,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmEnterReadGroup')
	.sect	'.debug_info'
.L66:
	.word	236
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L69,.L68
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmEnterReadGroup',0,1,230,3,6,1,1,1
	.word	.L19,.L84,.L18
	.byte	4
	.word	.L19,.L84
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmEnterReadGroup')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmEnterReadGroup')
	.sect	'.debug_line'
.L68:
	.word	.L123-.L122
.L122:
	.half	3
	.word	.L125-.L124
.L124:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L125:
	.byte	5,27,7,0,5,2
	.word	.L19
	.byte	3,231,3,1,5,1,7,9
	.half	.L70-.L19
	.byte	3,1,0,1,1
.L123:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmEnterReadGroup')
	.sect	'.debug_ranges'
.L69:
	.word	-1,.L19,0,.L70-.L19,0,0
	.sdecl	'.debug_info',debug,cluster('Adc_lSchmExitReadGroup')
	.sect	'.debug_info'
.L71:
	.word	235
	.half	3
	.word	.L72
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L74,.L73
	.byte	2
	.word	.L22
	.byte	3
	.byte	'Adc_lSchmExitReadGroup',0,1,255,3,6,1,1,1
	.word	.L21,.L85,.L20
	.byte	4
	.word	.L21,.L85
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_lSchmExitReadGroup')
	.sect	'.debug_abbrev'
.L72:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_lSchmExitReadGroup')
	.sect	'.debug_line'
.L73:
	.word	.L127-.L126
.L126:
	.half	3
	.word	.L129-.L128
.L128:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Ver.c',0,0,0,0,0
.L129:
	.byte	5,26,7,0,5,2
	.word	.L21
	.byte	3,128,4,1,5,1,7,9
	.half	.L75-.L21
	.byte	3,1,0,1,1
.L127:
	.sdecl	'.debug_ranges',debug,cluster('Adc_lSchmExitReadGroup')
	.sect	'.debug_ranges'
.L74:
	.word	-1,.L21,0,.L75-.L21,0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmEnterGetGrpStatus')
	.sect	'.debug_loc'
.L10:
	.word	-1,.L11,0,.L80-.L11
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmEnterGetStreamLastPtr')
	.sect	'.debug_loc'
.L14:
	.word	-1,.L15,0,.L82-.L15
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmEnterReadGroup')
	.sect	'.debug_loc'
.L18:
	.word	-1,.L19,0,.L84-.L19
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmEnterStartGroup')
	.sect	'.debug_loc'
.L2:
	.word	-1,.L3,0,.L76-.L3
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmEnterStopGroup')
	.sect	'.debug_loc'
.L6:
	.word	-1,.L7,0,.L78-.L7
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmExitGetGrpStatus')
	.sect	'.debug_loc'
.L12:
	.word	-1,.L13,0,.L81-.L13
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmExitGetStreamLastPtr')
	.sect	'.debug_loc'
.L16:
	.word	-1,.L17,0,.L83-.L17
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmExitReadGroup')
	.sect	'.debug_loc'
.L20:
	.word	-1,.L21,0,.L85-.L21
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmExitStartGroup')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L77-.L5
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_lSchmExitStopGroup')
	.sect	'.debug_loc'
.L8:
	.word	-1,.L9,0,.L79-.L9
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L130:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmEnterStartGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L3,.L76-.L3
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmExitStartGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L5,.L77-.L5
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmEnterStopGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L7,.L78-.L7
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmExitStopGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L9,.L79-.L9
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmEnterGetGrpStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L11,.L80-.L11
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmExitGetGrpStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L13,.L81-.L13
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmEnterGetStreamLastPtr')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L15,.L82-.L15
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmExitGetStreamLastPtr')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L17,.L83-.L17
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmEnterReadGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L19,.L84-.L19
	.sdecl	'.debug_frame',debug,cluster('Adc_lSchmExitReadGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L130,.L21,.L85-.L21

; ..\mcal_src\Adc_Ver.c	   514  }
; ..\mcal_src\Adc_Ver.c	   515  #endif /* (ADC_READ_GROUP_API == STD_ON) */
; ..\mcal_src\Adc_Ver.c	   516  #endif /* (ADC_RESULT_HANDLING_MODE == ADC_AUTOSAR) */
; ..\mcal_src\Adc_Ver.c	   517  
; ..\mcal_src\Adc_Ver.c	   518  /* Queue Enable is STD_ON only during No priority */
; ..\mcal_src\Adc_Ver.c	   519  #if (ADC_ENABLE_QUEUING == STD_ON)
; ..\mcal_src\Adc_Ver.c	   520  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   521  ** Syntax           : void Adc_lSchmEnterPopQueue                             **
; ..\mcal_src\Adc_Ver.c	   522  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   523  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   524  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   525  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   526  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   527  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   528  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   529  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   530  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   531  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   532  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   533  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   534  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   535  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   536  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   537  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   538  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   539  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   540  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   541  void Adc_lSchmEnterPopQueue(void)
; ..\mcal_src\Adc_Ver.c	   542  {
; ..\mcal_src\Adc_Ver.c	   543    SchM_Enter_Adc_PopQueue();
; ..\mcal_src\Adc_Ver.c	   544  }
; ..\mcal_src\Adc_Ver.c	   545  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   546  ** Syntax           : void Adc_lSchmExitPopQueue                              **
; ..\mcal_src\Adc_Ver.c	   547  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   548  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   549  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   550  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   551  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   552  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   553  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   554  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   555  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   556  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   557  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   558  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   559  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   560  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   561  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   562  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   563  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   564  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   565  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   566  void Adc_lSchmExitPopQueue(void)
; ..\mcal_src\Adc_Ver.c	   567  {
; ..\mcal_src\Adc_Ver.c	   568    SchM_Exit_Adc_PopQueue();
; ..\mcal_src\Adc_Ver.c	   569  }
; ..\mcal_src\Adc_Ver.c	   570  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   571  ** Syntax           : void Adc_lSchmEnterPushQueue                            **
; ..\mcal_src\Adc_Ver.c	   572  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   573  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   574  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   575  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   576  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   577  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   578  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   579  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   580  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   581  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   582  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   583  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   584  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   585  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   586  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   587  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   588  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   589  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   590  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   591  void Adc_lSchmEnterPushQueue(void)
; ..\mcal_src\Adc_Ver.c	   592  {
; ..\mcal_src\Adc_Ver.c	   593    SchM_Enter_Adc_PushQueue();
; ..\mcal_src\Adc_Ver.c	   594  }
; ..\mcal_src\Adc_Ver.c	   595  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   596  ** Syntax           : void Adc_lSchmExitPushQueue                             **
; ..\mcal_src\Adc_Ver.c	   597  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   598  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   599  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   600  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   601  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   602  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   603  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   604  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   605  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   606  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   607  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   608  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   609  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   610  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   611  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   612  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   613  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   614  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   615  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   616  void Adc_lSchmExitPushQueue(void)
; ..\mcal_src\Adc_Ver.c	   617  {
; ..\mcal_src\Adc_Ver.c	   618    SchM_Exit_Adc_PushQueue();
; ..\mcal_src\Adc_Ver.c	   619  }
; ..\mcal_src\Adc_Ver.c	   620  #endif /* (ADC_ENABLE_QUEUING == STD_ON) */
; ..\mcal_src\Adc_Ver.c	   621  
; ..\mcal_src\Adc_Ver.c	   622  /* Full priority considered */
; ..\mcal_src\Adc_Ver.c	   623  #if (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW)
; ..\mcal_src\Adc_Ver.c	   624  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   625  ** Syntax           : void Adc_lSchmEnterScheduleNext                         **
; ..\mcal_src\Adc_Ver.c	   626  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   627  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   628  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   629  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   630  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   631  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   632  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   633  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   634  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   635  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   636  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   637  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   638  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   639  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   640  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   641  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   642  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   643  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   644  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   645  void Adc_lSchmEnterScheduleNext(void)
; ..\mcal_src\Adc_Ver.c	   646  {
; ..\mcal_src\Adc_Ver.c	   647    SchM_Enter_Adc_ScheduleNext();
; ..\mcal_src\Adc_Ver.c	   648  }
; ..\mcal_src\Adc_Ver.c	   649  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   650  ** Syntax           : void Adc_lSchmExitScheduleNext                          **
; ..\mcal_src\Adc_Ver.c	   651  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   652  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   653  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   654  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   655  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   656  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   657  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   658  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   659  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   660  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   661  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   662  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   663  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   664  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   665  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   666  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   667  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   668  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   669  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   670  void Adc_lSchmExitScheduleNext(void)
; ..\mcal_src\Adc_Ver.c	   671  {
; ..\mcal_src\Adc_Ver.c	   672    SchM_Exit_Adc_ScheduleNext();
; ..\mcal_src\Adc_Ver.c	   673  }
; ..\mcal_src\Adc_Ver.c	   674  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   675  ** Syntax           : void Adc_lSchmEnterScheduleStart                        **
; ..\mcal_src\Adc_Ver.c	   676  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   677  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   678  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   679  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   680  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   681  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   682  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   683  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   684  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   685  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   686  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   687  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   688  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   689  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   690  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   691  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   692  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   693  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   694  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   695  void Adc_lSchmEnterScheduleStart(void)
; ..\mcal_src\Adc_Ver.c	   696  {
; ..\mcal_src\Adc_Ver.c	   697    SchM_Enter_Adc_ScheduleStart();
; ..\mcal_src\Adc_Ver.c	   698  }
; ..\mcal_src\Adc_Ver.c	   699  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   700  ** Syntax           : void Adc_lSchmExitScheduleStart                         **
; ..\mcal_src\Adc_Ver.c	   701  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   702  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   703  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   704  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   705  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   706  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   707  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   708  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   709  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   710  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   711  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   712  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   713  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   714  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   715  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   716  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   717  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   718  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   719  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   720  void Adc_lSchmExitScheduleStart(void)
; ..\mcal_src\Adc_Ver.c	   721  {
; ..\mcal_src\Adc_Ver.c	   722    SchM_Exit_Adc_ScheduleStart();
; ..\mcal_src\Adc_Ver.c	   723  }
; ..\mcal_src\Adc_Ver.c	   724  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   725  ** Syntax           : void Adc_lSchmEnterScheduleStop                         **
; ..\mcal_src\Adc_Ver.c	   726  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   727  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   728  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   729  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   730  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   731  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   732  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   733  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   734  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   735  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   736  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   737  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   738  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   739  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   740  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   741  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   742  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Ver.c	   743  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   744  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   745  void Adc_lSchmEnterScheduleStop(void)
; ..\mcal_src\Adc_Ver.c	   746  {
; ..\mcal_src\Adc_Ver.c	   747    SchM_Enter_Adc_ScheduleStop();
; ..\mcal_src\Adc_Ver.c	   748  }
; ..\mcal_src\Adc_Ver.c	   749  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   750  ** Syntax           : void Adc_lSchmExitScheduleStop                          **
; ..\mcal_src\Adc_Ver.c	   751  **                    (                                                       **
; ..\mcal_src\Adc_Ver.c	   752  **                       void                                                 **
; ..\mcal_src\Adc_Ver.c	   753  **                    )                                                       **
; ..\mcal_src\Adc_Ver.c	   754  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   755  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Ver.c	   756  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   757  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Ver.c	   758  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   759  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Ver.c	   760  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   761  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Ver.c	   762  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   763  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Ver.c	   764  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   765  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Ver.c	   766  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   767  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Ver.c	   768  **                                                                            **
; ..\mcal_src\Adc_Ver.c	   769  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   770  void Adc_lSchmExitScheduleStop(void)
; ..\mcal_src\Adc_Ver.c	   771  {
; ..\mcal_src\Adc_Ver.c	   772    SchM_Exit_Adc_ScheduleStop();
; ..\mcal_src\Adc_Ver.c	   773  }
; ..\mcal_src\Adc_Ver.c	   774  #endif /* (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW) */
; ..\mcal_src\Adc_Ver.c	   775  
; ..\mcal_src\Adc_Ver.c	   776  #define ADC_STOP_SEC_CODE
; ..\mcal_src\Adc_Ver.c	   777  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is 
; ..\mcal_src\Adc_Ver.c	   778  allowed only for MemMap.h*/
; ..\mcal_src\Adc_Ver.c	   779  #include "MemMap.h"
; ..\mcal_src\Adc_Ver.c	   780  /*******************************************************************************
; ..\mcal_src\Adc_Ver.c	   781  **                            General Notes                                   **
; ..\mcal_src\Adc_Ver.c	   782  *******************************************************************************/
; ..\mcal_src\Adc_Ver.c	   783  

	; Module end
