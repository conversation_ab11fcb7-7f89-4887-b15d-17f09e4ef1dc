	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc29752a -c99 --dep-file=eeprom\\.Dem_Data.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\Dem_Data.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\Dem_Data.src ..\\eeprom\\Dem_Data.c"
	.compiler_name		"ctc"
	.name	"Dem_Data"

	
$TC16X
	
	.sdecl	'.text.Dem_Data.Dem_NvM_InitAdminData',code,cluster('Dem_NvM_InitAdminData')
	.sect	'.text.Dem_Data.Dem_NvM_InitAdminData'
	.align	2
	
	.global	Dem_NvM_InitAdminData

; ..\eeprom\Dem_Data.c	     1  /*
; ..\eeprom\Dem_Data.c	     2   * Dem_Data.c
; ..\eeprom\Dem_Data.c	     3   *
; ..\eeprom\Dem_Data.c	     4   *  Created on: 2021-1-11
; ..\eeprom\Dem_Data.c	     5   *      Author: fanghongqing
; ..\eeprom\Dem_Data.c	     6   */
; ..\eeprom\Dem_Data.c	     7  
; ..\eeprom\Dem_Data.c	     8  #include "Std_Types.h"
; ..\eeprom\Dem_Data.c	     9  #include "Dem_Data.h"
; ..\eeprom\Dem_Data.c	    10  Dem_Cfg_AdminDataType Dem_Cfg_AdminData;
; ..\eeprom\Dem_Data.c	    11  Dem_Cfg_StatusDataType Dem_Cfg_StatusData;
; ..\eeprom\Dem_Data.c	    12  Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_0;
; ..\eeprom\Dem_Data.c	    13  const Dem_Cfg_PrimaryEntryType Dem_Cfg_MemoryEntryInit;
; ..\eeprom\Dem_Data.c	    14  Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_1;
; ..\eeprom\Dem_Data.c	    15  Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_2;
; ..\eeprom\Dem_Data.c	    16  Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_3;
; ..\eeprom\Dem_Data.c	    17  Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_4;
; ..\eeprom\Dem_Data.c	    18  Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_5;
; ..\eeprom\Dem_Data.c	    19  Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_6;
; ..\eeprom\Dem_Data.c	    20  Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_7;
; ..\eeprom\Dem_Data.c	    21  void Dem_NvM_InitAdminData(void)
; Function Dem_NvM_InitAdminData
.L3:
Dem_NvM_InitAdminData:	.type	func

; ..\eeprom\Dem_Data.c	    22  {
; ..\eeprom\Dem_Data.c	    23  
; ..\eeprom\Dem_Data.c	    24  }
	ret
.L49:
	
__Dem_NvM_InitAdminData_function_end:
	.size	Dem_NvM_InitAdminData,__Dem_NvM_InitAdminData_function_end-Dem_NvM_InitAdminData
.L16:
	; End of function
	
	.sdecl	'.text.Dem_Data.Dem_NvM_JobFinished',code,cluster('Dem_NvM_JobFinished')
	.sect	'.text.Dem_Data.Dem_NvM_JobFinished'
	.align	2
	
	.global	Dem_NvM_JobFinished

; ..\eeprom\Dem_Data.c	    25  
; ..\eeprom\Dem_Data.c	    26  void Dem_NvM_JobFinished(void)
; Function Dem_NvM_JobFinished
.L5:
Dem_NvM_JobFinished:	.type	func

; ..\eeprom\Dem_Data.c	    27  {
; ..\eeprom\Dem_Data.c	    28  
; ..\eeprom\Dem_Data.c	    29  }
	ret
.L50:
	
__Dem_NvM_JobFinished_function_end:
	.size	Dem_NvM_JobFinished,__Dem_NvM_JobFinished_function_end-Dem_NvM_JobFinished
.L21:
	; End of function
	
	.sdecl	'.text.Dem_Data.Dem_NvM_InitStatusData',code,cluster('Dem_NvM_InitStatusData')
	.sect	'.text.Dem_Data.Dem_NvM_InitStatusData'
	.align	2
	
	.global	Dem_NvM_InitStatusData

; ..\eeprom\Dem_Data.c	    30  
; ..\eeprom\Dem_Data.c	    31  void Dem_NvM_InitStatusData(void)
; Function Dem_NvM_InitStatusData
.L7:
Dem_NvM_InitStatusData:	.type	func

; ..\eeprom\Dem_Data.c	    32  {
; ..\eeprom\Dem_Data.c	    33  
; ..\eeprom\Dem_Data.c	    34  }
	ret
.L51:
	
__Dem_NvM_InitStatusData_function_end:
	.size	Dem_NvM_InitStatusData,__Dem_NvM_InitStatusData_function_end-Dem_NvM_InitStatusData
.L26:
	; End of function
	
	.sdecl	'.bss.Dem_Data.Dem_Cfg_AdminData',data,cluster('Dem_Cfg_AdminData')
	.sect	'.bss.Dem_Data.Dem_Cfg_AdminData'
	.global	Dem_Cfg_AdminData
	.align	4
Dem_Cfg_AdminData:	.type	object
	.size	Dem_Cfg_AdminData,8
	.space	8
	.sdecl	'.bss.Dem_Data.Dem_Cfg_StatusData',data,cluster('Dem_Cfg_StatusData')
	.sect	'.bss.Dem_Data.Dem_Cfg_StatusData'
	.global	Dem_Cfg_StatusData
	.align	4
Dem_Cfg_StatusData:	.type	object
	.size	Dem_Cfg_StatusData,72
	.space	72
	.sdecl	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_0',data,cluster('Dem_Cfg_PrimaryEntry_0')
	.sect	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_0'
	.global	Dem_Cfg_PrimaryEntry_0
	.align	4
Dem_Cfg_PrimaryEntry_0:	.type	object
	.size	Dem_Cfg_PrimaryEntry_0,68
	.space	68
	.sdecl	'.bss.Dem_Data.Dem_Cfg_MemoryEntryInit',data,cluster('Dem_Cfg_MemoryEntryInit')
	.sect	'.bss.Dem_Data.Dem_Cfg_MemoryEntryInit'
	.global	Dem_Cfg_MemoryEntryInit
	.align	4
Dem_Cfg_MemoryEntryInit:	.type	object
	.size	Dem_Cfg_MemoryEntryInit,68
	.space	68
	.sdecl	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_1',data,cluster('Dem_Cfg_PrimaryEntry_1')
	.sect	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_1'
	.global	Dem_Cfg_PrimaryEntry_1
	.align	4
Dem_Cfg_PrimaryEntry_1:	.type	object
	.size	Dem_Cfg_PrimaryEntry_1,68
	.space	68
	.sdecl	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_2',data,cluster('Dem_Cfg_PrimaryEntry_2')
	.sect	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_2'
	.global	Dem_Cfg_PrimaryEntry_2
	.align	4
Dem_Cfg_PrimaryEntry_2:	.type	object
	.size	Dem_Cfg_PrimaryEntry_2,68
	.space	68
	.sdecl	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_3',data,cluster('Dem_Cfg_PrimaryEntry_3')
	.sect	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_3'
	.global	Dem_Cfg_PrimaryEntry_3
	.align	4
Dem_Cfg_PrimaryEntry_3:	.type	object
	.size	Dem_Cfg_PrimaryEntry_3,68
	.space	68
	.sdecl	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_4',data,cluster('Dem_Cfg_PrimaryEntry_4')
	.sect	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_4'
	.global	Dem_Cfg_PrimaryEntry_4
	.align	4
Dem_Cfg_PrimaryEntry_4:	.type	object
	.size	Dem_Cfg_PrimaryEntry_4,68
	.space	68
	.sdecl	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_5',data,cluster('Dem_Cfg_PrimaryEntry_5')
	.sect	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_5'
	.global	Dem_Cfg_PrimaryEntry_5
	.align	4
Dem_Cfg_PrimaryEntry_5:	.type	object
	.size	Dem_Cfg_PrimaryEntry_5,68
	.space	68
	.sdecl	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_6',data,cluster('Dem_Cfg_PrimaryEntry_6')
	.sect	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_6'
	.global	Dem_Cfg_PrimaryEntry_6
	.align	4
Dem_Cfg_PrimaryEntry_6:	.type	object
	.size	Dem_Cfg_PrimaryEntry_6,68
	.space	68
	.sdecl	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_7',data,cluster('Dem_Cfg_PrimaryEntry_7')
	.sect	'.bss.Dem_Data.Dem_Cfg_PrimaryEntry_7'
	.global	Dem_Cfg_PrimaryEntry_7
	.align	4
Dem_Cfg_PrimaryEntry_7:	.type	object
	.size	Dem_Cfg_PrimaryEntry_7,68
	.space	68
	.calls	'Dem_NvM_InitAdminData','',0
	.calls	'Dem_NvM_JobFinished','',0
	.calls	'Dem_NvM_InitStatusData','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L9:
	.word	992
	.half	3
	.word	.L10
	.byte	4
.L8:
	.byte	1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L11
	.byte	2
	.byte	'void',0,3
	.word	175
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	181
	.byte	5,1,3
	.word	205
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	207
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	230
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	261
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	298
.L52:
	.byte	7
	.byte	'Dem_Cfg_AdminDataType_s',0,3,11,16,8,8
	.byte	'ImplementationVersion',0,2
	.word	261
	.byte	2,35,0,8
	.byte	'ConfigurationId',0,2
	.word	261
	.byte	2,35,2,9,2
	.word	261
	.byte	10,0,0,8
	.byte	'CycleCounter',0,2
	.word	419
	.byte	2,35,4,8
	.byte	'MemoryOverflow',0,1
	.word	230
	.byte	2,35,6,0,4
	.byte	'Dem_Cfg_AdminDataType',0,3,16,71
	.word	334
.L53:
	.byte	7
	.byte	'Dem_Cfg_StatusDataType_s',0,3,19,16,72,8
	.byte	'FirstFailedEvent',0,2
	.word	261
	.byte	2,35,0,8
	.byte	'FirstConfirmedEvent',0,2
	.word	261
	.byte	2,35,2,8
	.byte	'MostRecentFailedEvent',0,2
	.word	261
	.byte	2,35,4,8
	.byte	'MostRecentConfmdEvent',0,2
	.word	261
	.byte	2,35,6,9,31
	.word	230
	.byte	10,30,0,8
	.byte	'TripCount',0,31
	.word	652
	.byte	2,35,8,11
	.word	652
	.byte	8
	.byte	'EventStatus',0,31
	.word	680
	.byte	2,35,39,0,4
	.byte	'Dem_Cfg_StatusDataType',0,3,26,71
	.word	505
.L54:
	.byte	7
	.byte	'Dem_Cfg_PrimaryEntryType_s',0,3,28,16,68,8
	.byte	'Timestamp',0,4
	.word	298
	.byte	2,35,0,8
	.byte	'AgingCounter',0,2
	.word	261
	.byte	2,35,4,8
	.byte	'EventId',0,2
	.word	261
	.byte	2,35,6,9,1
	.word	230
	.byte	10,0,0,9,1
	.word	828
	.byte	10,0,0,8
	.byte	'ExtendedData',0,1
	.word	837
	.byte	2,35,8,9,55
	.word	230
	.byte	10,54,0,9,55
	.word	868
	.byte	10,0,0,8
	.byte	'SnapshotData',0,55
	.word	877
	.byte	2,35,9,8
	.byte	'ExtendedHeader',0,1
	.word	230
	.byte	2,35,64,8
	.byte	'SnapshotHeader',0,1
	.word	230
	.byte	2,35,65,0,4
	.byte	'Dem_Cfg_PrimaryEntryType',0,3,36,71
	.word	738
.L55:
	.byte	12
	.word	738
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L10:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,56,9,0,0,9,1,1,11,15,73,19,0,0,10,33,0,47,15,0,0,11,53,0,73,19,0,0,12,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L11:
	.word	.L57-.L56
.L56:
	.half	3
	.word	.L59-.L58
.L58:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'..\\eeprom\\Dem_Data.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'..\\eeprom\\Dem_Data.h',0,0,0,0,0
.L59:
.L57:
	.sdecl	'.debug_info',debug,cluster('Dem_NvM_InitAdminData')
	.sect	'.debug_info'
.L12:
	.word	232
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L15,.L14
	.byte	2
	.word	.L8
	.byte	3
	.byte	'Dem_NvM_InitAdminData',0,1,21,6,1,1,1
	.word	.L3,.L49,.L2
	.byte	4
	.word	.L3,.L49
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_NvM_InitAdminData')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dem_NvM_InitAdminData')
	.sect	'.debug_line'
.L14:
	.word	.L61-.L60
.L60:
	.half	3
	.word	.L63-.L62
.L62:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\Dem_Data.c',0,0,0,0,0
.L63:
	.byte	5,1,7,0,5,2
	.word	.L3
	.byte	3,23,1,7,9
	.half	.L16-.L3
	.byte	0,1,1
.L61:
	.sdecl	'.debug_ranges',debug,cluster('Dem_NvM_InitAdminData')
	.sect	'.debug_ranges'
.L15:
	.word	-1,.L3,0,.L16-.L3,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_NvM_JobFinished')
	.sect	'.debug_info'
.L17:
	.word	230
	.half	3
	.word	.L18
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L20,.L19
	.byte	2
	.word	.L8
	.byte	3
	.byte	'Dem_NvM_JobFinished',0,1,26,6,1,1,1
	.word	.L5,.L50,.L4
	.byte	4
	.word	.L5,.L50
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_NvM_JobFinished')
	.sect	'.debug_abbrev'
.L18:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dem_NvM_JobFinished')
	.sect	'.debug_line'
.L19:
	.word	.L65-.L64
.L64:
	.half	3
	.word	.L67-.L66
.L66:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\Dem_Data.c',0,0,0,0,0
.L67:
	.byte	5,1,7,0,5,2
	.word	.L5
	.byte	3,28,1,7,9
	.half	.L21-.L5
	.byte	0,1,1
.L65:
	.sdecl	'.debug_ranges',debug,cluster('Dem_NvM_JobFinished')
	.sect	'.debug_ranges'
.L20:
	.word	-1,.L5,0,.L21-.L5,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_NvM_InitStatusData')
	.sect	'.debug_info'
.L22:
	.word	233
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L25,.L24
	.byte	2
	.word	.L8
	.byte	3
	.byte	'Dem_NvM_InitStatusData',0,1,31,6,1,1,1
	.word	.L7,.L51,.L6
	.byte	4
	.word	.L7,.L51
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_NvM_InitStatusData')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dem_NvM_InitStatusData')
	.sect	'.debug_line'
.L24:
	.word	.L69-.L68
.L68:
	.half	3
	.word	.L71-.L70
.L70:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\Dem_Data.c',0,0,0,0,0
.L71:
	.byte	5,1,7,0,5,2
	.word	.L7
	.byte	3,33,1,7,9
	.half	.L26-.L7
	.byte	0,1,1
.L69:
	.sdecl	'.debug_ranges',debug,cluster('Dem_NvM_InitStatusData')
	.sect	'.debug_ranges'
.L25:
	.word	-1,.L7,0,.L26-.L7,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_AdminData')
	.sect	'.debug_info'
.L27:
	.word	206
	.half	3
	.word	.L28
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_AdminData',0,1,10,23
	.word	.L52
	.byte	1,5,3
	.word	Dem_Cfg_AdminData
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_AdminData')
	.sect	'.debug_abbrev'
.L28:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_StatusData')
	.sect	'.debug_info'
.L29:
	.word	207
	.half	3
	.word	.L30
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_StatusData',0,1,11,24
	.word	.L53
	.byte	1,5,3
	.word	Dem_Cfg_StatusData
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_StatusData')
	.sect	'.debug_abbrev'
.L30:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_PrimaryEntry_0')
	.sect	'.debug_info'
.L31:
	.word	211
	.half	3
	.word	.L32
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_PrimaryEntry_0',0,1,12,26
	.word	.L54
	.byte	1,5,3
	.word	Dem_Cfg_PrimaryEntry_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_PrimaryEntry_0')
	.sect	'.debug_abbrev'
.L32:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_PrimaryEntry_1')
	.sect	'.debug_info'
.L33:
	.word	211
	.half	3
	.word	.L34
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_PrimaryEntry_1',0,1,14,26
	.word	.L54
	.byte	1,5,3
	.word	Dem_Cfg_PrimaryEntry_1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_PrimaryEntry_1')
	.sect	'.debug_abbrev'
.L34:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_PrimaryEntry_2')
	.sect	'.debug_info'
.L35:
	.word	211
	.half	3
	.word	.L36
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_PrimaryEntry_2',0,1,15,26
	.word	.L54
	.byte	1,5,3
	.word	Dem_Cfg_PrimaryEntry_2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_PrimaryEntry_2')
	.sect	'.debug_abbrev'
.L36:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_PrimaryEntry_3')
	.sect	'.debug_info'
.L37:
	.word	211
	.half	3
	.word	.L38
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_PrimaryEntry_3',0,1,16,26
	.word	.L54
	.byte	1,5,3
	.word	Dem_Cfg_PrimaryEntry_3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_PrimaryEntry_3')
	.sect	'.debug_abbrev'
.L38:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_PrimaryEntry_4')
	.sect	'.debug_info'
.L39:
	.word	211
	.half	3
	.word	.L40
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_PrimaryEntry_4',0,1,17,26
	.word	.L54
	.byte	1,5,3
	.word	Dem_Cfg_PrimaryEntry_4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_PrimaryEntry_4')
	.sect	'.debug_abbrev'
.L40:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_PrimaryEntry_5')
	.sect	'.debug_info'
.L41:
	.word	211
	.half	3
	.word	.L42
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_PrimaryEntry_5',0,1,18,26
	.word	.L54
	.byte	1,5,3
	.word	Dem_Cfg_PrimaryEntry_5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_PrimaryEntry_5')
	.sect	'.debug_abbrev'
.L42:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_PrimaryEntry_6')
	.sect	'.debug_info'
.L43:
	.word	211
	.half	3
	.word	.L44
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_PrimaryEntry_6',0,1,19,26
	.word	.L54
	.byte	1,5,3
	.word	Dem_Cfg_PrimaryEntry_6
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_PrimaryEntry_6')
	.sect	'.debug_abbrev'
.L44:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_PrimaryEntry_7')
	.sect	'.debug_info'
.L45:
	.word	211
	.half	3
	.word	.L46
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_PrimaryEntry_7',0,1,20,26
	.word	.L54
	.byte	1,5,3
	.word	Dem_Cfg_PrimaryEntry_7
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_PrimaryEntry_7')
	.sect	'.debug_abbrev'
.L46:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dem_Cfg_MemoryEntryInit')
	.sect	'.debug_info'
.L47:
	.word	212
	.half	3
	.word	.L48
	.byte	4,1
	.byte	'..\\eeprom\\Dem_Data.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L8
	.byte	3
	.byte	'Dem_Cfg_MemoryEntryInit',0,1,13,32
	.word	.L55
	.byte	1,5,3
	.word	Dem_Cfg_MemoryEntryInit
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dem_Cfg_MemoryEntryInit')
	.sect	'.debug_abbrev'
.L48:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('Dem_NvM_InitAdminData')
	.sect	'.debug_loc'
.L2:
	.word	-1,.L3,0,.L49-.L3
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dem_NvM_InitStatusData')
	.sect	'.debug_loc'
.L6:
	.word	-1,.L7,0,.L51-.L7
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dem_NvM_JobFinished')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L50-.L5
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L72:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Dem_NvM_InitAdminData')
	.sect	'.debug_frame'
	.word	24
	.word	.L72,.L3,.L49-.L3
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Dem_NvM_JobFinished')
	.sect	'.debug_frame'
	.word	24
	.word	.L72,.L5,.L50-.L5
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Dem_NvM_InitStatusData')
	.sect	'.debug_frame'
	.word	24
	.word	.L72,.L7,.L51-.L7
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\eeprom\Dem_Data.c	    35  
; ..\eeprom\Dem_Data.c	    36  
; ..\eeprom\Dem_Data.c	    37  
; ..\eeprom\Dem_Data.c	    38  

	; Module end
