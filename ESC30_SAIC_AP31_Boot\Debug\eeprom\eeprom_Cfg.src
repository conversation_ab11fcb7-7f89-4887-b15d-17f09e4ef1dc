	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc9896a -c99 --dep-file=eeprom\\.eeprom_Cfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\eeprom_Cfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\eeprom_Cfg.src ..\\eeprom\\eeprom_Cfg.c"
	.compiler_name		"ctc"
	.name	"eeprom_Cfg"

	
$TC16X
	
	.sdecl	'.data.eeprom_Cfg.NvM_DID_FFFF',data,cluster('NvM_DID_FFFF')
	.sect	'.data.eeprom_Cfg.NvM_DID_FFFF'
	.global	NvM_DID_FFFF
	.align	4
NvM_DID_FFFF:	.type	object
	.size	NvM_DID_FFFF,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F100',data,cluster('NvM_DID_F100')
	.sect	'.data.eeprom_Cfg.NvM_DID_F100'
	.global	NvM_DID_F100
	.align	4
NvM_DID_F100:	.type	object
	.size	NvM_DID_F100,6
	.byte	1,1
	.space	4
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F110',data,cluster('NvM_DID_F110')
	.sect	'.data.eeprom_Cfg.NvM_DID_F110'
	.global	NvM_DID_F110
	.align	4
NvM_DID_F110:	.type	object
	.size	NvM_DID_F110,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F111',data,cluster('NvM_DID_F111')
	.sect	'.data.eeprom_Cfg.NvM_DID_F111'
	.global	NvM_DID_F111
	.align	4
NvM_DID_F111:	.type	object
	.size	NvM_DID_F111,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F112',data,cluster('NvM_DID_F112')
	.sect	'.data.eeprom_Cfg.NvM_DID_F112'
	.global	NvM_DID_F112
	.align	4
NvM_DID_F112:	.type	object
	.size	NvM_DID_F112,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F113',data,cluster('NvM_DID_F113')
	.sect	'.data.eeprom_Cfg.NvM_DID_F113'
	.global	NvM_DID_F113
	.align	4
NvM_DID_F113:	.type	object
	.size	NvM_DID_F113,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F114',data,cluster('NvM_DID_F114')
	.sect	'.data.eeprom_Cfg.NvM_DID_F114'
	.global	NvM_DID_F114
	.align	4
NvM_DID_F114:	.type	object
	.size	NvM_DID_F114,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F115',data,cluster('NvM_DID_F115')
	.sect	'.data.eeprom_Cfg.NvM_DID_F115'
	.global	NvM_DID_F115
	.align	4
NvM_DID_F115:	.type	object
	.size	NvM_DID_F115,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F116',data,cluster('NvM_DID_F116')
	.sect	'.data.eeprom_Cfg.NvM_DID_F116'
	.global	NvM_DID_F116
	.align	4
NvM_DID_F116:	.type	object
	.size	NvM_DID_F116,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F117',data,cluster('NvM_DID_F117')
	.sect	'.data.eeprom_Cfg.NvM_DID_F117'
	.global	NvM_DID_F117
	.align	4
NvM_DID_F117:	.type	object
	.size	NvM_DID_F117,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F118',data,cluster('NvM_DID_F118')
	.sect	'.data.eeprom_Cfg.NvM_DID_F118'
	.global	NvM_DID_F118
	.align	4
NvM_DID_F118:	.type	object
	.size	NvM_DID_F118,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F119',data,cluster('NvM_DID_F119')
	.sect	'.data.eeprom_Cfg.NvM_DID_F119'
	.global	NvM_DID_F119
	.align	4
NvM_DID_F119:	.type	object
	.size	NvM_DID_F119,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F11A',data,cluster('NvM_DID_F11A')
	.sect	'.data.eeprom_Cfg.NvM_DID_F11A'
	.global	NvM_DID_F11A
	.align	4
NvM_DID_F11A:	.type	object
	.size	NvM_DID_F11A,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F11B',data,cluster('NvM_DID_F11B')
	.sect	'.data.eeprom_Cfg.NvM_DID_F11B'
	.global	NvM_DID_F11B
	.align	4
NvM_DID_F11B:	.type	object
	.size	NvM_DID_F11B,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F11C',data,cluster('NvM_DID_F11C')
	.sect	'.data.eeprom_Cfg.NvM_DID_F11C'
	.global	NvM_DID_F11C
	.align	4
NvM_DID_F11C:	.type	object
	.size	NvM_DID_F11C,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F11D',data,cluster('NvM_DID_F11D')
	.sect	'.data.eeprom_Cfg.NvM_DID_F11D'
	.global	NvM_DID_F11D
	.align	4
NvM_DID_F11D:	.type	object
	.size	NvM_DID_F11D,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F11E',data,cluster('NvM_DID_F11E')
	.sect	'.data.eeprom_Cfg.NvM_DID_F11E'
	.global	NvM_DID_F11E
	.align	4
NvM_DID_F11E:	.type	object
	.size	NvM_DID_F11E,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F11F',data,cluster('NvM_DID_F11F')
	.sect	'.data.eeprom_Cfg.NvM_DID_F11F'
	.global	NvM_DID_F11F
	.align	4
NvM_DID_F11F:	.type	object
	.size	NvM_DID_F11F,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F183',data,cluster('NvM_DID_F183')
	.sect	'.data.eeprom_Cfg.NvM_DID_F183'
	.global	NvM_DID_F183
	.align	4
NvM_DID_F183:	.type	object
	.size	NvM_DID_F183,10
	.space	10
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F187',data,cluster('NvM_DID_F187')
	.sect	'.data.eeprom_Cfg.NvM_DID_F187'
	.global	NvM_DID_F187
	.align	4
NvM_DID_F187:	.type	object
	.size	NvM_DID_F187,5
	.space	5
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F18A',data,cluster('NvM_DID_F18A')
	.sect	'.data.eeprom_Cfg.NvM_DID_F18A'
	.global	NvM_DID_F18A
	.align	4
NvM_DID_F18A:	.type	object
	.size	NvM_DID_F18A,5
	.byte	5,69,41,132
	.byte	36
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F190',data,cluster('NvM_DID_F190')
	.sect	'.data.eeprom_Cfg.NvM_DID_F190'
	.global	NvM_DID_F190
	.align	4
NvM_DID_F190:	.type	object
	.size	NvM_DID_F190,17
	.space	17
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F191',data,cluster('NvM_DID_F191')
	.sect	'.data.eeprom_Cfg.NvM_DID_F191'
	.global	NvM_DID_F191
	.align	4
NvM_DID_F191:	.type	object
	.size	NvM_DID_F191,5
	.byte	17,3,66,97
	.byte	1
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F192',data,cluster('NvM_DID_F192')
	.sect	'.data.eeprom_Cfg.NvM_DID_F192'
	.global	NvM_DID_F192
	.align	4
NvM_DID_F192:	.type	object
	.size	NvM_DID_F192,10
	.byte	80,71,77,32,32,33,3,36
	.space	1
	.byte	2
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F194',data,cluster('NvM_DID_F194')
	.sect	'.data.eeprom_Cfg.NvM_DID_F194'
	.global	NvM_DID_F194
	.align	4
NvM_DID_F194:	.type	object
	.size	NvM_DID_F194,10
	.byte	65,80,80,48,46,48,46,52
	.byte	46,48
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1A0',data,cluster('NvM_DID_F1A0')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1A0'
	.global	NvM_DID_F1A0
	.align	4
NvM_DID_F1A0:	.type	object
	.size	NvM_DID_F1A0,5
	.byte	17,3,66,98
	.byte	1
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1A1',data,cluster('NvM_DID_F1A1')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1A1'
	.global	NvM_DID_F1A1
	.align	4
NvM_DID_F1A1:	.type	object
	.size	NvM_DID_F1A1,5
	.space	5
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1A2',data,cluster('NvM_DID_F1A2')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1A2'
	.global	NvM_DID_F1A2
	.align	4
NvM_DID_F1A2:	.type	object
	.size	NvM_DID_F1A2,8
	.byte	17,16,56,145
	.byte	2
	.space	3
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1A8',data,cluster('NvM_DID_F1A8')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1A8'
	.global	NvM_DID_F1A8
	.align	4
NvM_DID_F1A8:	.type	object
	.size	NvM_DID_F1A8,20
	.space	20
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F18B',data,cluster('NvM_DID_F18B')
	.sect	'.data.eeprom_Cfg.NvM_DID_F18B'
	.global	NvM_DID_F18B
NvM_DID_F18B:	.type	object
	.size	NvM_DID_F18B,3
	.space	3
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F18C',data,cluster('NvM_DID_F18C')
	.sect	'.data.eeprom_Cfg.NvM_DID_F18C'
	.global	NvM_DID_F18C
	.align	4
NvM_DID_F18C:	.type	object
	.size	NvM_DID_F18C,16
	.space	16
	.sdecl	'.data.eeprom_Cfg.NvM_DID_AFFC',data,cluster('NvM_DID_AFFC')
	.sect	'.data.eeprom_Cfg.NvM_DID_AFFC'
	.global	NvM_DID_AFFC
NvM_DID_AFFC:	.type	object
	.size	NvM_DID_AFFC,2
	.space	2
	.sdecl	'.data.eeprom_Cfg.NvM_DID_AFFD',data,cluster('NvM_DID_AFFD')
	.sect	'.data.eeprom_Cfg.NvM_DID_AFFD'
	.global	NvM_DID_AFFD
NvM_DID_AFFD:	.type	object
	.size	NvM_DID_AFFD,1
	.space	1
	.sdecl	'.data.eeprom_Cfg.NvM_DID_AFFE',data,cluster('NvM_DID_AFFE')
	.sect	'.data.eeprom_Cfg.NvM_DID_AFFE'
	.global	NvM_DID_AFFE
NvM_DID_AFFE:	.type	object
	.size	NvM_DID_AFFE,1
	.space	1
	.sdecl	'.data.eeprom_Cfg.NvM_DID_AFFF',data,cluster('NvM_DID_AFFF')
	.sect	'.data.eeprom_Cfg.NvM_DID_AFFF'
	.global	NvM_DID_AFFF
NvM_DID_AFFF:	.type	object
	.size	NvM_DID_AFFF,1
	.space	1
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F120',data,cluster('NvM_DID_F120')
	.sect	'.data.eeprom_Cfg.NvM_DID_F120'
	.global	NvM_DID_F120
	.align	4
NvM_DID_F120:	.type	object
	.size	NvM_DID_F120,16
	.byte	17,16,56,145
	.byte	2
	.space	11
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F121',data,cluster('NvM_DID_F121')
	.sect	'.data.eeprom_Cfg.NvM_DID_F121'
	.global	NvM_DID_F121
	.align	4
NvM_DID_F121:	.type	object
	.size	NvM_DID_F121,16
	.byte	17,16,56,145
	.byte	2
	.space	11
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F198',data,cluster('NvM_DID_F198')
	.sect	'.data.eeprom_Cfg.NvM_DID_F198'
	.global	NvM_DID_F198
	.align	4
NvM_DID_F198:	.type	object
	.size	NvM_DID_F198,11
	.space	11
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1A5',data,cluster('NvM_DID_F1A5')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1A5'
	.global	NvM_DID_F1A5
NvM_DID_F1A5:	.type	object
	.size	NvM_DID_F1A5,3
	.space	3
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1A9',data,cluster('NvM_DID_F1A9')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1A9'
	.global	NvM_DID_F1A9
	.align	4
NvM_DID_F1A9:	.type	object
	.size	NvM_DID_F1A9,5
	.space	5
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1AA',data,cluster('NvM_DID_F1AA')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1AA'
	.global	NvM_DID_F1AA
	.align	4
NvM_DID_F1AA:	.type	object
	.size	NvM_DID_F1AA,5
	.space	5
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1A3',data,cluster('NvM_DID_F1A3')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1A3'
	.global	NvM_DID_F1A3
NvM_DID_F1A3:	.type	object
	.size	NvM_DID_F1A3,3
	.space	3
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1E0',data,cluster('NvM_DID_F1E0')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1E0'
	.global	NvM_DID_F1E0
NvM_DID_F1E0:	.type	object
	.size	NvM_DID_F1E0,1
	.byte	2
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1E1',data,cluster('NvM_DID_F1E1')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1E1'
	.global	NvM_DID_F1E1
	.align	4
NvM_DID_F1E1:	.type	object
	.size	NvM_DID_F1E1,20
	.space	20
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1E2',data,cluster('NvM_DID_F1E2')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1E2'
	.global	NvM_DID_F1E2
	.align	4
NvM_DID_F1E2:	.type	object
	.size	NvM_DID_F1E2,20
	.space	20
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1EA',data,cluster('NvM_DID_F1EA')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1EA'
	.global	NvM_DID_F1EA
	.align	4
NvM_DID_F1EA:	.type	object
	.size	NvM_DID_F1EA,20
	.space	20
	.sdecl	'.data.eeprom_Cfg.NvM_DID_F1EB',data,cluster('NvM_DID_F1EB')
	.sect	'.data.eeprom_Cfg.NvM_DID_F1EB'
	.global	NvM_DID_F1EB
	.align	4
NvM_DID_F1EB:	.type	object
	.size	NvM_DID_F1EB,20
	.space	20
	.sdecl	'.bss.eeprom_Cfg.VSS_SecurityKey_PIM',data,cluster('VSS_SecurityKey_PIM')
	.sect	'.bss.eeprom_Cfg.VSS_SecurityKey_PIM'
	.global	VSS_SecurityKey_PIM
	.align	4
VSS_SecurityKey_PIM:	.type	object
	.size	VSS_SecurityKey_PIM,576
	.space	576
	.sdecl	'.bss.eeprom_Cfg.Ram_File_Digest',data,cluster('Ram_File_Digest')
	.sect	'.bss.eeprom_Cfg.Ram_File_Digest'
	.global	Ram_File_Digest
	.align	4
Ram_File_Digest:	.type	object
	.size	Ram_File_Digest,64
	.space	64
	.sdecl	'.bss.eeprom_Cfg.Ram_Date_Information',data,cluster('Ram_Date_Information')
	.sect	'.bss.eeprom_Cfg.Ram_Date_Information'
	.global	Ram_Date_Information
Ram_Date_Information:	.type	object
	.size	Ram_Date_Information,3
	.space	3
	.sdecl	'.data.eeprom_Cfg.NvM_SECERR_DATA',data,cluster('NvM_SECERR_DATA')
	.sect	'.data.eeprom_Cfg.NvM_SECERR_DATA'
	.global	NvM_SECERR_DATA
	.align	4
NvM_SECERR_DATA:	.type	object
	.size	NvM_SECERR_DATA,64
	.space	64
	.sdecl	'.data.eeprom_Cfg.NvM_SECLOG_DATA',data,cluster('NvM_SECLOG_DATA')
	.sect	'.data.eeprom_Cfg.NvM_SECLOG_DATA'
	.global	NvM_SECLOG_DATA
	.align	4
NvM_SECLOG_DATA:	.type	object
	.size	NvM_SECLOG_DATA,40
	.space	40
	.sdecl	'.rodata.eeprom_Cfg.Nvm_Did_Cfg',data,rom,cluster('Nvm_Did_Cfg')
	.sect	'.rodata.eeprom_Cfg.Nvm_Did_Cfg'
	.global	Nvm_Did_Cfg
	.align	4
Nvm_Did_Cfg:	.type	object
	.size	Nvm_Did_Cfg,960
	.half	61696
	.byte	3
	.space	5
	.word	NvM_DID_F100
	.half	6
	.space	6
	.word	EEP_GetSWVerification_F100
	.half	61713
	.byte	1
	.space	1
	.word	50,NvM_DID_F111
	.half	16
	.space	10
	.half	61712
	.byte	1
	.space	1
	.word	49,NvM_DID_F110
	.half	16
	.space	2
	.word	EEP_SetPIF_F110,EEP_GetPIF_F110
	.half	61714
	.byte	1
	.space	1
	.word	51,NvM_DID_F112
	.half	16
	.space	10
	.half	61715
	.byte	1
	.space	1
	.word	52,NvM_DID_F113
	.half	16
	.space	10
	.half	61716
	.byte	1
	.space	1
	.word	53,NvM_DID_F114
	.half	16
	.space	10
	.half	61717
	.byte	1
	.space	1
	.word	54,NvM_DID_F115
	.half	16
	.space	10
	.half	61718
	.byte	1
	.space	1
	.word	55,NvM_DID_F116
	.half	16
	.space	10
	.half	61719
	.byte	1
	.space	1
	.word	56,NvM_DID_F117
	.half	16
	.space	10
	.half	61720
	.byte	1
	.space	1
	.word	57,NvM_DID_F118
	.half	16
	.space	10
	.half	61721
	.byte	1
	.space	1
	.word	58,NvM_DID_F119
	.half	16
	.space	10
	.half	61722
	.byte	1
	.space	1
	.word	59,NvM_DID_F11A
	.half	16
	.space	10
	.half	61723
	.byte	1
	.space	1
	.word	60,NvM_DID_F11B
	.half	16
	.space	10
	.half	61724
	.byte	1
	.space	1
	.word	61,NvM_DID_F11C
	.half	16
	.space	10
	.half	61725
	.byte	1
	.space	1
	.word	62,NvM_DID_F11D
	.half	16
	.space	10
	.half	61726
	.byte	1
	.space	1
	.word	63,NvM_DID_F11E
	.half	16
	.space	10
	.half	61727
	.byte	1
	.space	1
	.word	64,NvM_DID_F11F
	.half	16
	.space	10
	.half	61728
	.byte	1
	.space	1
	.word	65,NvM_DID_F120
	.half	16
	.space	10
	.half	61729
	.byte	1
	.space	1
	.word	66,NvM_DID_F121
	.half	16
	.space	10
	.half	61827
	.byte	2
	.space	1
	.word	Dcm_PflashDidInfo,NvM_DID_F183
	.half	10
	.space	10
	.half	61831
	.byte	1
	.space	1
	.word	67,NvM_DID_F187
	.half	5
	.space	10
	.half	61834
	.byte	4
	.space	5
	.word	NvM_DID_F18A
	.half	5
	.space	10
	.half	61835
	.byte	1
	.space	1
	.word	69,NvM_DID_F18B
	.half	3
	.space	10
	.half	61836
	.byte	1
	.space	1
	.word	70,NvM_DID_F18C
	.half	16
	.space	10
	.half	61840
	.byte	1
	.space	1
	.word	71,NvM_DID_F190
	.half	17
	.space	10
	.half	61841
	.byte	1
	.space	1
	.word	72,NvM_DID_F191
	.half	5
	.space	10
	.half	61842
	.byte	1
	.space	1
	.word	73,NvM_DID_F192
	.half	10
	.space	10
	.half	61844
	.byte	2
	.space	1
	.word	-1610383280,NvM_DID_F194
	.half	10
	.space	10
	.half	61848
	.byte	1
	.space	1
	.word	74,NvM_DID_F198
	.half	11
	.space	10
	.half	61856
	.byte	2
	.space	1
	.word	-1610383312,NvM_DID_F1A0
	.half	5
	.space	10
	.half	61857
	.byte	2
	.space	1
	.word	-1610448848,NvM_DID_F1A1
	.half	5
	.space	10
	.half	61858
	.byte	2
	.space	1
	.word	-1610448880,NvM_DID_F1A2
	.half	8
	.space	10
	.half	61861
	.byte	2
	.space	1
	.word	-1610383264,NvM_DID_F1A5
	.half	3
	.space	10
	.half	61864
	.byte	1
	.space	1
	.word	76,NvM_DID_F1A8
	.half	20
	.space	10
	.half	61865
	.byte	1
	.space	1
	.word	93,NvM_DID_F1A9
	.half	5
	.space	10
	.half	61866
	.byte	1
	.space	1
	.word	94,NvM_DID_F1AA
	.half	5
	.space	10
	.half	45052
	.byte	3
	.space	5
	.word	NvM_DID_AFFC
	.half	2
	.space	6
	.word	EEP_GetProgrammingCounter_AFFC
	.half	45053
	.byte	3
	.space	5
	.word	NvM_DID_AFFD
	.half	1
	.space	6
	.word	EEP_GetSoftwareIntegrityStatus_AFFD
	.half	45054
	.byte	3
	.space	5
	.word	NvM_DID_AFFE
	.half	1
	.space	6
	.word	EEP_GetSoftwareCompatibilityStatus_AFFE
	.half	45055
	.byte	3
	.space	5
	.word	NvM_DID_AFFF
	.half	1
	.space	6
	.word	EEP_GetSoftwareValidFlag_AFFF
	.sdecl	'.rodata.eeprom_Cfg.Nvm_Did_Cfg_Size',data,rom,cluster('Nvm_Did_Cfg_Size')
	.sect	'.rodata.eeprom_Cfg.Nvm_Did_Cfg_Size'
	.global	Nvm_Did_Cfg_Size
Nvm_Did_Cfg_Size:	.type	object
	.size	Nvm_Did_Cfg_Size,1
	.byte	40
	.calls	'__INDIRECT__','EEP_GetPIF_F110'
	.calls	'__INDIRECT__','EEP_SetPIF_F110'
	.calls	'__INDIRECT__','EEP_GetSWVerification_F100'
	.calls	'__INDIRECT__','EEP_GetSoftwareValidFlag_AFFF'
	.calls	'__INDIRECT__','EEP_GetSoftwareIntegrityStatus_AFFD'
	.calls	'__INDIRECT__','EEP_GetSoftwareCompatibilityStatus_AFFE'
	.extern	Dcm_PflashDidInfo
	.extern	EEP_GetPIF_F110
	.extern	EEP_SetPIF_F110
	.extern	EEP_GetSWVerification_F100
	.extern	EEP_GetSoftwareValidFlag_AFFF
	.extern	EEP_GetSoftwareIntegrityStatus_AFFD
	.extern	EEP_GetSoftwareCompatibilityStatus_AFFE
	.extern	EEP_GetProgrammingCounter_AFFC
	.extern	__INDIRECT__
	.calls	'__INDIRECT__','EEP_GetProgrammingCounter_AFFC'
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	3770
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'EEP_GetPIF_F110',0,1,93,15
	.word	177
	.byte	1,1,1,1,2
	.byte	'unsigned char',0,1,8,4
	.byte	'f110index',0,1,93,37
	.word	227
	.byte	5
	.word	227
	.byte	4
	.byte	'data',0,1,93,54
	.word	262
	.byte	0,3
	.byte	'EEP_SetPIF_F110',0,1,94,15
	.word	177
	.byte	1,1,1,1,4
	.byte	'index',0,1,94,37
	.word	227
	.byte	4
	.byte	'data',0,1,94,50
	.word	262
	.byte	0,3
	.byte	'EEP_GetSWVerification_F100',0,1,95,15
	.word	177
	.byte	1,1,1,1,4
	.byte	'f100index',0,1,95,48
	.word	227
	.byte	4
	.byte	'data',0,1,95,65
	.word	262
	.byte	0,3
	.byte	'EEP_GetSoftwareValidFlag_AFFF',0,1,97,15
	.word	177
	.byte	1,1,1,1,4
	.byte	'afffindex',0,1,97,51
	.word	227
	.byte	4
	.byte	'data',0,1,97,68
	.word	262
	.byte	0,3
	.byte	'EEP_GetSoftwareIntegrityStatus_AFFD',0,1,98,15
	.word	177
	.byte	1,1,1,1,4
	.byte	'affdindex',0,1,98,57
	.word	227
	.byte	4
	.byte	'data',0,1,98,74
	.word	262
	.byte	0,3
	.byte	'EEP_GetSoftwareCompatibilityStatus_AFFE',0,1,99,15
	.word	177
	.byte	1,1,1,1,4
	.byte	'affeindex',0,1,99,61
	.word	227
	.byte	4
	.byte	'data',0,1,99,78
	.word	262
	.byte	0,3
	.byte	'EEP_GetProgrammingCounter_AFFC',0,1,100,15
	.word	177
	.byte	1,1,1,1,4
	.byte	'affcindex',0,1,100,52
	.word	227
	.byte	4
	.byte	'data',0,1,100,69
	.word	262
	.byte	0,6
	.byte	'__INDIRECT__',0,2,1,1,1,1,1,7
	.byte	'void',0,5
	.word	741
	.byte	8
	.byte	'__prof_adm',0,2,1,1
	.word	747
	.byte	9,1,5
	.word	771
	.byte	8
	.byte	'__codeptr',0,2,1,1
	.word	773
	.byte	8
	.byte	'uint8',0,3,90,29
	.word	227
	.byte	2
	.byte	'short int',0,2,5,8
	.byte	'sint16',0,3,91,29
	.word	810
	.byte	8
	.byte	'uint16',0,3,92,29
	.word	177
	.byte	2
	.byte	'unsigned long int',0,4,7,8
	.byte	'uint32',0,3,94,29
	.word	853
	.byte	8
	.byte	'boolean',0,3,105,29
	.word	227
	.byte	2
	.byte	'unsigned long long int',0,8,7,8
	.byte	'uint64',0,3,130,1,30
	.word	905
	.byte	10,4,11,9,48
.L114:
	.byte	11,16
	.word	227
	.byte	12,15,0,13
	.byte	'fbl_ref_num',0,16
	.word	952
	.byte	2,35,0,13
	.byte	'fbl_part_num',0,16
	.word	952
	.byte	2,35,16,13
	.byte	'fbl_cbp_code',0,16
	.word	952
	.byte	2,35,32,0,8
	.byte	'Dcm_PflashDidInfoType',0,4,17,3
	.word	947
	.byte	14
	.word	947
	.byte	15
	.byte	'Dcm_PflashDidInfo',0,4,18,36
	.word	1057
	.byte	1,1,16,5,52,9,164,1,11,164,1
	.word	227
	.byte	12,163,1,0,13
	.byte	'datas',0,164,1
	.word	1096
	.byte	2,35,0,10,5,55,5,164,1,13
	.byte	'certFmt',0,1
	.word	227
	.byte	2,35,0
.L127:
	.byte	11,8
	.word	227
	.byte	12,7,0,13
	.byte	'pModNum',0,8
	.word	1146
	.byte	2,35,1,13
	.byte	'customPars',0,16
	.word	952
	.byte	2,35,9
.L116:
	.byte	11,3
	.word	227
	.byte	12,2,0,13
	.byte	'certFailDate',0,3
	.word	1192
	.byte	2,35,25,11,4
	.word	227
	.byte	12,3,0,13
	.byte	'certSequenceNum',0,4
	.word	1223
	.byte	2,35,28,13
	.byte	'signAlgoFlg',0,1
	.word	227
	.byte	2,35,32,13
	.byte	'pubKeyCurPar',0,1
	.word	227
	.byte	2,35,33,13
	.byte	'hashAlgoFlg',0,1
	.word	227
	.byte	2,35,34,13
	.byte	'pubKeyIdx',0,1
	.word	227
	.byte	2,35,35
.L122:
	.byte	11,64
	.word	227
	.byte	12,63,0,13
	.byte	'certPubKey',0,64
	.word	1340
	.byte	2,35,36,13
	.byte	'certSigner',0,64
	.word	1340
	.byte	2,35,100,0,13
	.byte	'parameters',0,164,1
	.word	1123
	.byte	2,35,0,0,8
	.byte	'Secure_SignerInfoType',0,5,68,3
	.word	1090
	.byte	8
	.byte	'_iob_flag_t',0,6,75,25
	.word	177
	.byte	17,7,72,9,1,18
	.byte	'INTERNAL_FLS',0,0,18
	.byte	'EXTERNAL_FLS',0,1,0,8
	.byte	'FL_FlashType',0,7,76,2
	.word	1462
	.byte	17,7,78,9,1,18
	.byte	'NO_CRC',0,0,18
	.byte	'LAST_ADDR',0,1,18
	.byte	'HEAD_ADDR',0,2,0,8
	.byte	'FL_CrcAddrType',0,7,83,2
	.word	1519
	.byte	10,7,108,9,8,13
	.byte	'address',0,4
	.word	853
	.byte	2,35,0,13
	.byte	'length',0,4
	.word	853
	.byte	2,35,4,0,8
	.byte	'FL_SegmentInfoType',0,7,116,3
	.word	1581
	.byte	10,7,129,1,9,20,13
	.byte	'blkValid',0,1
	.word	227
	.byte	2,35,0,13
	.byte	'blkProgAttempt',0,2
	.word	177
	.byte	2,35,2,13
	.byte	'blkChecksum',0,4
	.word	853
	.byte	2,35,4,11,9
	.word	227
	.byte	12,8,0,13
	.byte	'fingerPrint',0,9
	.word	1716
	.byte	2,35,8,0,8
	.byte	'FL_blockInfoType',0,7,139,1,3
	.word	1647
	.byte	19
	.word	177
	.byte	1,1,20
	.word	227
	.byte	5
	.word	227
	.byte	20
	.word	1785
	.byte	0,5
	.word	1773
	.byte	8
	.byte	'Eep_WriteFct',0,8,42,18
	.word	1796
	.byte	8
	.byte	'Eep_ReadFct',0,8,43,18
	.word	1796
	.byte	10,8,45,9,24,14
	.word	177
	.byte	13
	.byte	'u16t_did',0,2
	.word	1847
	.byte	2,35,0,13
	.byte	'u8t_datatype',0,1
	.word	227
	.byte	2,35,2,13
	.byte	'u32t_addr',0,4
	.word	853
	.byte	2,35,4,13
	.byte	'u8t_buffer',0,4
	.word	262
	.byte	2,35,8,14
	.word	177
	.byte	13
	.byte	'len',0,2
	.word	1931
	.byte	2,35,12,14
	.word	1801
	.byte	13
	.byte	'writeeeprom',0,4
	.word	1949
	.byte	2,35,16,14
	.word	1822
	.byte	13
	.byte	'readeeprom',0,4
	.word	1975
	.byte	2,35,20,0,8
	.byte	'ST_NVM_DID_TYPE',0,8,53,2
	.word	1842
	.byte	5
	.word	1773
	.byte	5
	.word	1773
	.byte	8
	.byte	'PduLengthType',0,9,76,22
	.word	177
	.byte	8
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,10,112,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,10,115,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,10,118,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,10,121,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,10,124,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,10,136,1,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,10,139,1,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,10,148,1,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,10,151,1,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,10,141,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,10,144,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,10,147,3,16
	.word	177
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,10,150,3,16
	.word	177
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,10,153,3,16
	.word	177
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,10,156,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,10,159,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,10,162,3,16
	.word	177
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,10,165,3,16
	.word	177
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,10,180,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,10,183,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,10,186,3,16
	.word	853
	.byte	8
	.byte	'IdtAppCom_MainPwrFltRsn',0,10,192,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGMDiags',0,10,195,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGMFltRsn',0,10,198,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGMSts',0,10,201,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGMSwSts',0,10,207,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,10,210,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,10,213,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,10,216,3,16
	.word	177
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,10,219,3,16
	.word	177
	.byte	8
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,10,225,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,10,228,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_RednPwrFltRsn',0,10,231,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,10,234,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_SHWAIndSts',0,10,237,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_SHWASysFltSts',0,10,240,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_SHWASysMsg',0,10,243,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,10,246,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_SHWASysSts',0,10,249,3,15
	.word	227
	.byte	8
	.byte	'IdtAppCom_SHWASysTakeOver',0,10,252,3,15
	.word	227
	.byte	2
	.byte	'unsigned int',0,4,7,8
	.byte	'Rte_BitType',0,10,230,7,22
	.word	3614
.L115:
	.byte	11,5
	.word	227
	.byte	12,4,0
.L117:
	.byte	11,20
	.word	227
	.byte	12,19,0
.L118:
	.byte	11,10
	.word	227
	.byte	12,9,0
.L119:
	.byte	11,17
	.word	227
	.byte	12,16,0
.L120:
	.byte	11,11
	.word	227
	.byte	12,10,0
.L121:
	.byte	11,192,4
	.word	227
	.byte	12,191,4,0
.L123:
	.byte	11,40
	.word	227
	.byte	12,39,0,11,192,7
	.word	1842
	.byte	12,39,0,21
	.word	3716
.L124:
	.byte	14
	.word	3726
	.byte	21
	.word	227
.L125:
	.byte	14
	.word	3736
.L126:
	.byte	11,6
	.word	227
	.byte	12,5,0
.L128:
	.byte	11,2
	.word	227
	.byte	12,1,0
.L129:
	.byte	11,1
	.word	227
	.byte	12,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,58,15,59,15,57,15
	.byte	73,19,54,15,39,12,63,12,60,12,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,15,0,73,19,0,0,6,46,0,3,8,58
	.byte	15,59,15,57,15,54,15,63,12,60,12,0,0,7,59,0,3,8,0,0,8,22,0,3,8,58,15,59,15,57,15,73,19,0,0,9,21,0,54,15
	.byte	0,0,10,19,1,58,15,59,15,57,15,11,15,0,0,11,1,1,11,15,73,19,0,0,12,33,0,47,15,0,0,13,13,0,3,8,11,15,73
	.byte	19,56,9,0,0,14,38,0,73,19,0,0,15,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,16,23,1,58,15,59,15
	.byte	57,15,11,15,0,0,17,4,1,58,15,59,15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,21,1,73,19,54,15,39,12,0,0
	.byte	20,5,0,73,19,0,0,21,53,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L131-.L130
.L130:
	.half	3
	.word	.L133-.L132
.L132:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash',0
	.byte	0
	.byte	'..\\eeprom\\eeprom_Cfg.h',0,0,0,0
	.byte	'..\\eeprom\\eeprom_Cfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Did_Cfg.h',0,2,0,0
	.byte	'Secure_Types.h',0,3,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'FL.h',0,5,0,0
	.byte	'..\\eeprom\\eeprom.h',0,0,0,0
	.byte	'ComStack_Types.h',0,1,0,0
	.byte	'..\\eeprom\\Rte_Type.h',0,0,0,0,0
.L133:
.L131:
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F110')
	.sect	'.debug_info'
.L6:
	.word	203
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F110',0,2,15,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F110
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F110')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F111')
	.sect	'.debug_info'
.L8:
	.word	203
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F111',0,2,16,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F111
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F111')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F112')
	.sect	'.debug_info'
.L10:
	.word	203
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F112',0,2,17,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F112
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F112')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F113')
	.sect	'.debug_info'
.L12:
	.word	203
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F113',0,2,18,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F113
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F113')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F114')
	.sect	'.debug_info'
.L14:
	.word	203
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F114',0,2,19,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F114
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F114')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F115')
	.sect	'.debug_info'
.L16:
	.word	203
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F115',0,2,20,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F115
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F115')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F116')
	.sect	'.debug_info'
.L18:
	.word	203
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F116',0,2,21,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F116
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F116')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F117')
	.sect	'.debug_info'
.L20:
	.word	203
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F117',0,2,22,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F117
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F117')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F118')
	.sect	'.debug_info'
.L22:
	.word	203
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F118',0,2,23,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F118
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F118')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F119')
	.sect	'.debug_info'
.L24:
	.word	203
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F119',0,2,24,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F119
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F119')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F11A')
	.sect	'.debug_info'
.L26:
	.word	203
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F11A',0,2,25,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F11A
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F11A')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F11B')
	.sect	'.debug_info'
.L28:
	.word	203
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F11B',0,2,26,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F11B
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F11B')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F11C')
	.sect	'.debug_info'
.L30:
	.word	203
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F11C',0,2,27,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F11C
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F11C')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F11D')
	.sect	'.debug_info'
.L32:
	.word	203
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F11D',0,2,28,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F11D
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F11D')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F11E')
	.sect	'.debug_info'
.L34:
	.word	203
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F11E',0,2,29,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F11E
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F11E')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F11F')
	.sect	'.debug_info'
.L36:
	.word	203
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F11F',0,2,30,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F11F
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F11F')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F187')
	.sect	'.debug_info'
.L38:
	.word	203
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F187',0,2,32,7
	.word	.L115
	.byte	1,5,3
	.word	NvM_DID_F187
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F187')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F191')
	.sect	'.debug_info'
.L40:
	.word	203
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F191',0,2,35,7
	.word	.L115
	.byte	1,5,3
	.word	NvM_DID_F191
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F191')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F18B')
	.sect	'.debug_info'
.L42:
	.word	203
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F18B',0,2,42,7
	.word	.L116
	.byte	1,5,3
	.word	NvM_DID_F18B
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F18B')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1A8')
	.sect	'.debug_info'
.L44:
	.word	203
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1A8',0,2,41,7
	.word	.L117
	.byte	1,5,3
	.word	NvM_DID_F1A8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1A8')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F192')
	.sect	'.debug_info'
.L46:
	.word	203
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F192',0,2,36,7
	.word	.L118
	.byte	1,5,3
	.word	NvM_DID_F192
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F192')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F18C')
	.sect	'.debug_info'
.L48:
	.word	203
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F18C',0,2,43,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F18C
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F18C')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F190')
	.sect	'.debug_info'
.L50:
	.word	203
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F190',0,2,34,7
	.word	.L119
	.byte	1,5,3
	.word	NvM_DID_F190
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F190')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F120')
	.sect	'.debug_info'
.L52:
	.word	203
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F120',0,2,50,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F120
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F120')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F121')
	.sect	'.debug_info'
.L54:
	.word	203
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F121',0,2,51,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_F121
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F121')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F198')
	.sect	'.debug_info'
.L56:
	.word	203
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F198',0,2,52,7
	.word	.L120
	.byte	1,5,3
	.word	NvM_DID_F198
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F198')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1A5')
	.sect	'.debug_info'
.L58:
	.word	203
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1A5',0,2,53,7
	.word	.L116
	.byte	1,5,3
	.word	NvM_DID_F1A5
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1A5')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1A9')
	.sect	'.debug_info'
.L60:
	.word	203
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1A9',0,2,54,7
	.word	.L115
	.byte	1,5,3
	.word	NvM_DID_F1A9
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1A9')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1AA')
	.sect	'.debug_info'
.L62:
	.word	203
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1AA',0,2,55,7
	.word	.L115
	.byte	1,5,3
	.word	NvM_DID_F1AA
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1AA')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F18A')
	.sect	'.debug_info'
.L64:
	.word	203
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F18A',0,2,33,7
	.word	.L115
	.byte	1,5,3
	.word	NvM_DID_F18A
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F18A')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('VSS_SecurityKey_PIM')
	.sect	'.debug_info'
.L66:
	.word	210
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'VSS_SecurityKey_PIM',0,2,64,7
	.word	.L121
	.byte	1,5,3
	.word	VSS_SecurityKey_PIM
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('VSS_SecurityKey_PIM')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Ram_File_Digest')
	.sect	'.debug_info'
.L68:
	.word	206
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Ram_File_Digest',0,2,66,7
	.word	.L122
	.byte	1,5,3
	.word	Ram_File_Digest
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Ram_File_Digest')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Ram_Date_Information')
	.sect	'.debug_info'
.L70:
	.word	211
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Ram_Date_Information',0,2,67,7
	.word	.L116
	.byte	1,5,3
	.word	Ram_Date_Information
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Ram_Date_Information')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_SECERR_DATA')
	.sect	'.debug_info'
.L72:
	.word	206
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_SECERR_DATA',0,2,69,7
	.word	.L122
	.byte	1,5,3
	.word	NvM_SECERR_DATA
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_SECERR_DATA')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_SECLOG_DATA')
	.sect	'.debug_info'
.L74:
	.word	206
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_SECLOG_DATA',0,2,70,7
	.word	.L123
	.byte	1,5,3
	.word	NvM_SECLOG_DATA
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_SECLOG_DATA')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Nvm_Did_Cfg')
	.sect	'.debug_info'
.L76:
	.word	202
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Nvm_Did_Cfg',0,2,74,32
	.word	.L124
	.byte	1,5,3
	.word	Nvm_Did_Cfg
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Nvm_Did_Cfg')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Nvm_Did_Cfg_Size')
	.sect	'.debug_info'
.L78:
	.word	207
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Nvm_Did_Cfg_Size',0,2,124,22
	.word	.L125
	.byte	1,5,3
	.word	Nvm_Did_Cfg_Size
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Nvm_Did_Cfg_Size')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_FFFF')
	.sect	'.debug_info'
.L80:
	.word	203
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_FFFF',0,2,12,7
	.word	.L114
	.byte	1,5,3
	.word	NvM_DID_FFFF
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_FFFF')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F100')
	.sect	'.debug_info'
.L82:
	.word	203
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F100',0,2,14,7
	.word	.L126
	.byte	1,5,3
	.word	NvM_DID_F100
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F100')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F183')
	.sect	'.debug_info'
.L84:
	.word	203
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F183',0,2,31,7
	.word	.L118
	.byte	1,5,3
	.word	NvM_DID_F183
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F183')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F194')
	.sect	'.debug_info'
.L86:
	.word	203
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F194',0,2,37,7
	.word	.L118
	.byte	1,5,3
	.word	NvM_DID_F194
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F194')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1A0')
	.sect	'.debug_info'
.L88:
	.word	203
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1A0',0,2,38,7
	.word	.L115
	.byte	1,5,3
	.word	NvM_DID_F1A0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1A0')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1A1')
	.sect	'.debug_info'
.L90:
	.word	203
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1A1',0,2,39,7
	.word	.L115
	.byte	1,5,3
	.word	NvM_DID_F1A1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1A1')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1A2')
	.sect	'.debug_info'
.L92:
	.word	203
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1A2',0,2,40,7
	.word	.L127
	.byte	1,5,3
	.word	NvM_DID_F1A2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1A2')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_AFFC')
	.sect	'.debug_info'
.L94:
	.word	203
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_AFFC',0,2,45,7
	.word	.L128
	.byte	1,5,3
	.word	NvM_DID_AFFC
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_AFFC')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_AFFD')
	.sect	'.debug_info'
.L96:
	.word	203
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_AFFD',0,2,46,7
	.word	.L129
	.byte	1,5,3
	.word	NvM_DID_AFFD
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_AFFD')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_AFFE')
	.sect	'.debug_info'
.L98:
	.word	203
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_AFFE',0,2,47,7
	.word	.L129
	.byte	1,5,3
	.word	NvM_DID_AFFE
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_AFFE')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_AFFF')
	.sect	'.debug_info'
.L100:
	.word	203
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_AFFF',0,2,48,7
	.word	.L129
	.byte	1,5,3
	.word	NvM_DID_AFFF
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_AFFF')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1A3')
	.sect	'.debug_info'
.L102:
	.word	203
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1A3',0,2,57,7
	.word	.L116
	.byte	1,5,3
	.word	NvM_DID_F1A3
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1A3')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1E0')
	.sect	'.debug_info'
.L104:
	.word	203
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1E0',0,2,58,7
	.word	.L129
	.byte	1,5,3
	.word	NvM_DID_F1E0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1E0')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1E1')
	.sect	'.debug_info'
.L106:
	.word	203
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1E1',0,2,59,7
	.word	.L117
	.byte	1,5,3
	.word	NvM_DID_F1E1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1E1')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1E2')
	.sect	'.debug_info'
.L108:
	.word	203
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1E2',0,2,60,7
	.word	.L117
	.byte	1,5,3
	.word	NvM_DID_F1E2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1E2')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1EA')
	.sect	'.debug_info'
.L110:
	.word	203
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1EA',0,2,61,7
	.word	.L117
	.byte	1,5,3
	.word	NvM_DID_F1EA
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1EA')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_DID_F1EB')
	.sect	'.debug_info'
.L112:
	.word	203
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'..\\eeprom\\eeprom_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'NvM_DID_F1EB',0,2,62,7
	.word	.L117
	.byte	1,5,3
	.word	NvM_DID_F1EB
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_DID_F1EB')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0

; ..\eeprom\eeprom_Cfg.c	     1  /*
; ..\eeprom\eeprom_Cfg.c	     2   * eeprom_Cfg.c
; ..\eeprom\eeprom_Cfg.c	     3   *
; ..\eeprom\eeprom_Cfg.c	     4   *  Created on: 2020-11-5
; ..\eeprom\eeprom_Cfg.c	     5   *      Author: fanghongqing
; ..\eeprom\eeprom_Cfg.c	     6   */
; ..\eeprom\eeprom_Cfg.c	     7  #include "Platform_Types.h"
; ..\eeprom\eeprom_Cfg.c	     8  #include "Did_Cfg.h"
; ..\eeprom\eeprom_Cfg.c	     9  #include "FL.h"
; ..\eeprom\eeprom_Cfg.c	    10  #include "eeprom.h"
; ..\eeprom\eeprom_Cfg.c	    11  #include "eeprom_Cfg.h"
; ..\eeprom\eeprom_Cfg.c	    12  uint8 NvM_DID_FFFF[16]={0};
; ..\eeprom\eeprom_Cfg.c	    13  
; ..\eeprom\eeprom_Cfg.c	    14  uint8 NvM_DID_F100[6]={0x01,0x01,0x00,0x00,0x00,0x00};
; ..\eeprom\eeprom_Cfg.c	    15  uint8 NvM_DID_F110[16]={0};
; ..\eeprom\eeprom_Cfg.c	    16  uint8 NvM_DID_F111[16]={0};
; ..\eeprom\eeprom_Cfg.c	    17  uint8 NvM_DID_F112[16]={0};
; ..\eeprom\eeprom_Cfg.c	    18  uint8 NvM_DID_F113[16]={0};
; ..\eeprom\eeprom_Cfg.c	    19  uint8 NvM_DID_F114[16]={0};
; ..\eeprom\eeprom_Cfg.c	    20  uint8 NvM_DID_F115[16]={0};
; ..\eeprom\eeprom_Cfg.c	    21  uint8 NvM_DID_F116[16]={0};
; ..\eeprom\eeprom_Cfg.c	    22  uint8 NvM_DID_F117[16]={0};
; ..\eeprom\eeprom_Cfg.c	    23  uint8 NvM_DID_F118[16]={0};
; ..\eeprom\eeprom_Cfg.c	    24  uint8 NvM_DID_F119[16]={0};
; ..\eeprom\eeprom_Cfg.c	    25  uint8 NvM_DID_F11A[16]={0};
; ..\eeprom\eeprom_Cfg.c	    26  uint8 NvM_DID_F11B[16]={0};
; ..\eeprom\eeprom_Cfg.c	    27  uint8 NvM_DID_F11C[16]={0};
; ..\eeprom\eeprom_Cfg.c	    28  uint8 NvM_DID_F11D[16]={0};
; ..\eeprom\eeprom_Cfg.c	    29  uint8 NvM_DID_F11E[16]={0};
; ..\eeprom\eeprom_Cfg.c	    30  uint8 NvM_DID_F11F[16]={0};
; ..\eeprom\eeprom_Cfg.c	    31  uint8 NvM_DID_F183[10]={0};
; ..\eeprom\eeprom_Cfg.c	    32  uint8 NvM_DID_F187[5]={0,0,0,0,0};
; ..\eeprom\eeprom_Cfg.c	    33  uint8 NvM_DID_F18A[5]={0x05,0x45,0x29,0x84,0x24};
; ..\eeprom\eeprom_Cfg.c	    34  uint8 NvM_DID_F190[17]={0};
; ..\eeprom\eeprom_Cfg.c	    35  uint8 NvM_DID_F191[5]={0x11,0x03,0x42,0x61,0x01};
; ..\eeprom\eeprom_Cfg.c	    36  uint8 NvM_DID_F192[10]={0x50, 0x47, 0x4D, 0x20, 0x20, 0x21, 0x03, 0x24, 0x00, 0x02};
; ..\eeprom\eeprom_Cfg.c	    37  uint8 NvM_DID_F194[10]={0x41, 0x50, 0x50, 0x30, 0x2E, 0x30, 0x2E, 0x34, 0x2E, 0x30};
; ..\eeprom\eeprom_Cfg.c	    38  uint8 NvM_DID_F1A0[5]={0x11,0x03,0x42,0x62,0x01};
; ..\eeprom\eeprom_Cfg.c	    39  uint8 NvM_DID_F1A1[5]={0};
; ..\eeprom\eeprom_Cfg.c	    40  uint8 NvM_DID_F1A2[8]={0x11,0x10,0x38,0x91,0x02,0x00,0x00,0x00};
; ..\eeprom\eeprom_Cfg.c	    41  uint8 NvM_DID_F1A8[20]={0};
; ..\eeprom\eeprom_Cfg.c	    42  uint8 NvM_DID_F18B[3]={0};
; ..\eeprom\eeprom_Cfg.c	    43  uint8 NvM_DID_F18C[16]={0};
; ..\eeprom\eeprom_Cfg.c	    44  
; ..\eeprom\eeprom_Cfg.c	    45  uint8 NvM_DID_AFFC[2]={0};
; ..\eeprom\eeprom_Cfg.c	    46  uint8 NvM_DID_AFFD[1]={0};
; ..\eeprom\eeprom_Cfg.c	    47  uint8 NvM_DID_AFFE[1]={0};
; ..\eeprom\eeprom_Cfg.c	    48  uint8 NvM_DID_AFFF[1]={0};
; ..\eeprom\eeprom_Cfg.c	    49  
; ..\eeprom\eeprom_Cfg.c	    50  uint8 NvM_DID_F120[16]={0x11, 0x10, 0x38, 0x91, 0x02, 0x00, 0x00, 0x00, 0x00,0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
; ..\eeprom\eeprom_Cfg.c	    51  uint8 NvM_DID_F121[16]={0x11, 0x10, 0x38, 0x91, 0x02, 0x00, 0x00, 0x00, 0x00,0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
; ..\eeprom\eeprom_Cfg.c	    52  uint8 NvM_DID_F198[11]={0};
; ..\eeprom\eeprom_Cfg.c	    53  uint8 NvM_DID_F1A5[3]={0};
; ..\eeprom\eeprom_Cfg.c	    54  uint8 NvM_DID_F1A9[5]={0};
; ..\eeprom\eeprom_Cfg.c	    55  uint8 NvM_DID_F1AA[5]={0};
; ..\eeprom\eeprom_Cfg.c	    56  
; ..\eeprom\eeprom_Cfg.c	    57  uint8 NvM_DID_F1A3[3]={0};
; ..\eeprom\eeprom_Cfg.c	    58  uint8 NvM_DID_F1E0[1]={2};
; ..\eeprom\eeprom_Cfg.c	    59  uint8 NvM_DID_F1E1[20]={0};
; ..\eeprom\eeprom_Cfg.c	    60  uint8 NvM_DID_F1E2[20]={0};
; ..\eeprom\eeprom_Cfg.c	    61  uint8 NvM_DID_F1EA[20]={0};
; ..\eeprom\eeprom_Cfg.c	    62  uint8 NvM_DID_F1EB[20]={0};
; ..\eeprom\eeprom_Cfg.c	    63  
; ..\eeprom\eeprom_Cfg.c	    64  uint8 VSS_SecurityKey_PIM[576];
; ..\eeprom\eeprom_Cfg.c	    65  
; ..\eeprom\eeprom_Cfg.c	    66  uint8 Ram_File_Digest[64];
; ..\eeprom\eeprom_Cfg.c	    67  uint8 Ram_Date_Information[3];
; ..\eeprom\eeprom_Cfg.c	    68  
; ..\eeprom\eeprom_Cfg.c	    69  uint8 NvM_SECERR_DATA[64]={0};
; ..\eeprom\eeprom_Cfg.c	    70  uint8 NvM_SECLOG_DATA[40]={0};
; ..\eeprom\eeprom_Cfg.c	    71  
; ..\eeprom\eeprom_Cfg.c	    72  #define F110_INDEX 2
; ..\eeprom\eeprom_Cfg.c	    73  
; ..\eeprom\eeprom_Cfg.c	    74  volatile const ST_NVM_DID_TYPE Nvm_Did_Cfg[]=
; ..\eeprom\eeprom_Cfg.c	    75  {
; ..\eeprom\eeprom_Cfg.c	    76  
; ..\eeprom\eeprom_Cfg.c	    77  	{0xF100,FUNC_TYPE,0 ,NvM_DID_F100,	6,NULL,&EEP_GetSWVerification_F100},
; ..\eeprom\eeprom_Cfg.c	    78  	{0xF111,EEPROM_TYPE,EEP_DIDF111_BLOCK_ID ,NvM_DID_F111,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    79  	{0xF110,EEPROM_TYPE,EEP_DIDF110_BLOCK_ID ,NvM_DID_F110,	16,&EEP_SetPIF_F110,&EEP_GetPIF_F110},
; ..\eeprom\eeprom_Cfg.c	    80  	{0xF112,EEPROM_TYPE,EEP_DIDF112_BLOCK_ID ,NvM_DID_F112,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    81  	{0xF113,EEPROM_TYPE,EEP_DIDF113_BLOCK_ID ,NvM_DID_F113,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    82  	{0xF114,EEPROM_TYPE,EEP_DIDF114_BLOCK_ID ,NvM_DID_F114,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    83  	{0xF115,EEPROM_TYPE,EEP_DIDF115_BLOCK_ID ,NvM_DID_F115,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    84  	{0xF116,EEPROM_TYPE,EEP_DIDF116_BLOCK_ID ,NvM_DID_F116,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    85  	{0xF117,EEPROM_TYPE,EEP_DIDF117_BLOCK_ID ,NvM_DID_F117,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    86  	{0xF118,EEPROM_TYPE,EEP_DIDF118_BLOCK_ID ,NvM_DID_F118,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    87  	{0xF119,EEPROM_TYPE,EEP_DIDF119_BLOCK_ID ,NvM_DID_F119,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    88  	{0xF11A,EEPROM_TYPE,EEP_DIDF11A_BLOCK_ID ,NvM_DID_F11A,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    89  	{0xF11B,EEPROM_TYPE,EEP_DIDF11B_BLOCK_ID ,NvM_DID_F11B,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    90  	{0xF11C,EEPROM_TYPE,EEP_DIDF11C_BLOCK_ID ,NvM_DID_F11C,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    91  	{0xF11D,EEPROM_TYPE,EEP_DIDF11D_BLOCK_ID ,NvM_DID_F11D,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    92  	{0xF11E,EEPROM_TYPE,EEP_DIDF11E_BLOCK_ID ,NvM_DID_F11E,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    93  	{0xF11F,EEPROM_TYPE,EEP_DIDF11F_BLOCK_ID ,NvM_DID_F11F,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    94  
; ..\eeprom\eeprom_Cfg.c	    95  	{0xF120,EEPROM_TYPE,EEP_DIDF120_BLOCK_ID ,NvM_DID_F120,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    96  	{0xF121,EEPROM_TYPE,EEP_DIDF121_BLOCK_ID ,NvM_DID_F121,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    97  	{0xF183,FLASH_TYPE,(uint32)&Dcm_PflashDidInfo.fbl_ref_num[0] ,NvM_DID_F183,	10,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    98  	{0xF187,EEPROM_TYPE, EEP_DIDF187_BLOCK_ID , 	NvM_DID_F187,	5,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	    99  	{0xF18A,DEFAULT_TYPE,0 ,NvM_DID_F18A,	5,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   100  	{0xF18B,EEPROM_TYPE,EEP_DIDF18B_BLOCK_ID ,NvM_DID_F18B,	3,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   101  	{0xF18C,EEPROM_TYPE,EEP_DIDF18C_BLOCK_ID ,NvM_DID_F18C,	16,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   102  	{0xF190,EEPROM_TYPE,EEP_DIDF190_BLOCK_ID ,NvM_DID_F190,	17,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   103  	{0xF191,EEPROM_TYPE, EEP_DIDF191_BLOCK_ID , 	NvM_DID_F191,	5,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   104  	{0xF192,EEPROM_TYPE,EEP_DIDF192_BLOCK_ID ,NvM_DID_F192,	10,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   105  	{0xF194,FLASH_TYPE,0xA0038050,NvM_DID_F194,	10,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   106  	{0xF198,EEPROM_TYPE,EEP_DIDF198_BLOCK_ID,NvM_DID_F198,	11,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   107  	{0xF1A0,FLASH_TYPE,0xA0038030 ,NvM_DID_F1A0,	5,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   108  	{0xF1A1,FLASH_TYPE,0xA0028030 ,NvM_DID_F1A1,	5,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   109  	{0xF1A2,FLASH_TYPE,0xA0028010 ,NvM_DID_F1A2,	8,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   110  	{0xF1A5,FLASH_TYPE,0xA0038060 ,NvM_DID_F1A5,	3,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   111  	{0xF1A8,EEPROM_TYPE,EEP_DIDF1A8_BLOCK_ID ,NvM_DID_F1A8,	20,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   112  	{0xF1A9,EEPROM_TYPE,EEP_DIDF1A9_BLOCK_ID ,NvM_DID_F1A9,	5,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   113  	{0xF1AA,EEPROM_TYPE,EEP_DIDF1AA_BLOCK_ID ,NvM_DID_F1AA,	5,NULL,NULL},
; ..\eeprom\eeprom_Cfg.c	   114  	{0xAFFC,FUNC_TYPE,0 ,NvM_DID_AFFC,	2,NULL,&EEP_GetProgrammingCounter_AFFC},
; ..\eeprom\eeprom_Cfg.c	   115  	{0xAFFD,FUNC_TYPE,0 ,NvM_DID_AFFD,	1,NULL,&EEP_GetSoftwareIntegrityStatus_AFFD},
; ..\eeprom\eeprom_Cfg.c	   116  	{0xAFFE,FUNC_TYPE,0 ,NvM_DID_AFFE,	1,NULL,&EEP_GetSoftwareCompatibilityStatus_AFFE},
; ..\eeprom\eeprom_Cfg.c	   117  	{0xAFFF,FUNC_TYPE,0 ,NvM_DID_AFFF,	1,NULL,&EEP_GetSoftwareValidFlag_AFFF},
; ..\eeprom\eeprom_Cfg.c	   118  
; ..\eeprom\eeprom_Cfg.c	   119  
; ..\eeprom\eeprom_Cfg.c	   120  
; ..\eeprom\eeprom_Cfg.c	   121  };
; ..\eeprom\eeprom_Cfg.c	   122  
; ..\eeprom\eeprom_Cfg.c	   123  
; ..\eeprom\eeprom_Cfg.c	   124  volatile const uint8 Nvm_Did_Cfg_Size=sizeof(Nvm_Did_Cfg)/sizeof(ST_NVM_DID_TYPE);
; ..\eeprom\eeprom_Cfg.c	   125  
; ..\eeprom\eeprom_Cfg.c	   126  

	; Module end
