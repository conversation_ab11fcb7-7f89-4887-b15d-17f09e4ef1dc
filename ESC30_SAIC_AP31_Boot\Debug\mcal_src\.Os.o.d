mcal_src\Os.o :	..\mcal_src\Os.c
..\mcal_src\Os.c :
mcal_src\Os.o :	..\mcal_src\Std_Types.h
..\mcal_src\Std_Types.h :
mcal_src\Os.o :	..\mcal_src\Compiler.h
..\mcal_src\Compiler.h :
mcal_src\Os.o :	..\mcal_src\Compiler_Cfg.h
..\mcal_src\Compiler_Cfg.h :
mcal_src\Os.o :	..\mcal_src\Platform_Types.h
..\mcal_src\Platform_Types.h :
mcal_src\Os.o :	..\mcal_src\IfxCpu_reg.h
..\mcal_src\IfxCpu_reg.h :
mcal_src\Os.o :	..\mcal_src\IfxCpu_regdef.h
..\mcal_src\IfxCpu_regdef.h :
mcal_src\Os.o :	..\mcal_src\Ifx_TypesReg.h
..\mcal_src\Ifx_TypesReg.h :
mcal_src\Os.o :	..\mcal_src\Mcal.h
..\mcal_src\Mcal.h :
mcal_src\Os.o :	..\mcal_src\Mcal_TcLib.h
..\mcal_src\Mcal_TcLib.h :
mcal_src\Os.o :	..\mcal_src\Mcal_Compiler.h
..\mcal_src\Mcal_Compiler.h :
mcal_src\Os.o :	..\mcal_src\Mcal_Options.h
..\mcal_src\Mcal_Options.h :
mcal_src\Os.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Os.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Os.o :	..\mcal_src\Mcal_WdgLib.h
..\mcal_src\Mcal_WdgLib.h :
mcal_src\Os.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Os.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Os.o :	..\mcal_src\Os.h
..\mcal_src\Os.h :
mcal_src\Os.o :	..\mcal_src\Mcal_DmaLib.h
..\mcal_src\Mcal_DmaLib.h :
mcal_src\Os.o :	..\mcal_src\IfxDma_reg.h
..\mcal_src\IfxDma_reg.h :
mcal_src\Os.o :	..\mcal_src\IfxDma_regdef.h
..\mcal_src\IfxDma_regdef.h :
mcal_src\Os.o :	..\mcal_src\IfxDma_bf.h
..\mcal_src\IfxDma_bf.h :
mcal_src\Os.o :	..\mcal_src\IfxScu_reg.h
..\mcal_src\IfxScu_reg.h :
mcal_src\Os.o :	..\mcal_src\IfxScu_regdef.h
..\mcal_src\IfxScu_regdef.h :
mcal_src\Os.o :	..\mcal_src\IfxSrc_reg.h
..\mcal_src\IfxSrc_reg.h :
mcal_src\Os.o :	..\mcal_src\IfxSrc_regdef.h
..\mcal_src\IfxSrc_regdef.h :
mcal_src\Os.o :	..\mcal_src\IfxSrc_bf.h
..\mcal_src\IfxSrc_bf.h :
mcal_src\Os.o :	..\mcal_src\Mcal.h
..\mcal_src\Mcal.h :
mcal_src\Os.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Os.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
