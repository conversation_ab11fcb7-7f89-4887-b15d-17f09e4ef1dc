#!/usr/bin/env python3
"""
简单的MCP服务器示例
用于验证MCP环境是否正确安装
"""

import asyncio
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp import types


# 创建服务器实例
server = Server("test-server")


@server.list_tools()
async def list_tools() -> list[types.Tool]:
    """列出可用的工具"""
    return [
        types.Tool(
            name="echo",
            description="回显输入的文本",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "要回显的文本"
                    }
                },
                "required": ["text"]
            }
        ),
        types.Tool(
            name="add",
            description="计算两个数字的和",
            inputSchema={
                "type": "object",
                "properties": {
                    "a": {
                        "type": "number",
                        "description": "第一个数字"
                    },
                    "b": {
                        "type": "number", 
                        "description": "第二个数字"
                    }
                },
                "required": ["a", "b"]
            }
        )
    ]


@server.call_tool()
async def call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    """处理工具调用"""
    if name == "echo":
        text = arguments.get("text", "")
        return [types.TextContent(type="text", text=f"回显: {text}")]
    
    elif name == "add":
        a = arguments.get("a", 0)
        b = arguments.get("b", 0)
        result = a + b
        return [types.TextContent(type="text", text=f"{a} + {b} = {result}")]
    
    else:
        raise ValueError(f"未知工具: {name}")


async def main():
    """主函数"""
    print("MCP测试服务器启动中...")
    print("服务器名称: test-server")
    print("可用工具: echo, add")
    print("按 Ctrl+C 停止服务器")
    
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务器已停止")
