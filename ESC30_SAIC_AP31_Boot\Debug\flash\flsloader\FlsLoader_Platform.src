	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc26680a -c99 --dep-file=flash\\flsloader\\.FlsLoader_Platform.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=flash\\flsloader\\FlsLoader_Platform.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o flash\\flsloader\\FlsLoader_Platform.src ..\\flash\\flsloader\\FlsLoader_Platform.c"
	.compiler_name		"ctc"
	.name	"FlsLoader_Platform"

	
$TC16X
	
	.sdecl	'.text.FLSLOADERRAMCODE',code,cluster('FlsLoader_lCheckOTPWOP')
	.sect	'.text.FLSLOADERRAMCODE'
	.align	2
	
	.global	FlsLoader_lCheckOTPWOP

; ..\flash\flsloader\FlsLoader_Platform.c	     1  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	     2  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	     3  ** Copyright (C) Infineon Technologies (2018)                                 **
; ..\flash\flsloader\FlsLoader_Platform.c	     4  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	     5  ** All rights reserved.                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	     6  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\flash\flsloader\FlsLoader_Platform.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\flash\flsloader\FlsLoader_Platform.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\flash\flsloader\FlsLoader_Platform.c	    10  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	    11  ********************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	    12  **                                                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	    13  **  $FILENAME   : FlsLoader_Platform.c $                                     **
; ..\flash\flsloader\FlsLoader_Platform.c	    14  **                                                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	    15  **  $CC VERSION : \main\dev_tc23x\27 $                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	    16  **                                                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	    17  **  $DATE       : 2018-06-19 $                                               **
; ..\flash\flsloader\FlsLoader_Platform.c	    18  **                                                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\flash\flsloader\FlsLoader_Platform.c	    20  **                                                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\flash\flsloader\FlsLoader_Platform.c	    22  **                                                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	    23  **  DESCRIPTION : This file contains AURIX derivative (platform specific)    **
; ..\flash\flsloader\FlsLoader_Platform.c	    24  **                functionality of FlsLoader driver.                         **
; ..\flash\flsloader\FlsLoader_Platform.c	    25  **                                                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	    26  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\flash\flsloader\FlsLoader_Platform.c	    27  **                                                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	    28  ******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	    29  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	    30  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	    31      TRACEABILITY : [cover parentID=] [/cover]
; ..\flash\flsloader\FlsLoader_Platform.c	    32  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	    33  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	    34  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	    35  **                      Includes                                              **
; ..\flash\flsloader\FlsLoader_Platform.c	    36  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	    37  
; ..\flash\flsloader\FlsLoader_Platform.c	    38  /* Inclusion of Tasking sfr file */
; ..\flash\flsloader\FlsLoader_Platform.c	    39  #include "IfxFlash_reg.h"
; ..\flash\flsloader\FlsLoader_Platform.c	    40  #include "IfxSrc_reg.h"
; ..\flash\flsloader\FlsLoader_Platform.c	    41  #include "FlsLoader_Local.h"
; ..\flash\flsloader\FlsLoader_Platform.c	    42  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	    43  **                      Private Macro Definitions                             **
; ..\flash\flsloader\FlsLoader_Platform.c	    44  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	    45  #define FLSLOADER_EEPROM_OFFSETEND      (FLSLOADER_DFLASH_BANK_SIZE - 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	    46  #define FLSLOADER_DF_EEPROM_SEGMENT     (0xAF0U)
; ..\flash\flsloader\FlsLoader_Platform.c	    47  #define FLSLOADER_DF_UCB_SEGMENT        (0xAF1U)
; ..\flash\flsloader\FlsLoader_Platform.c	    48  #define FLSLOADER_EEPROM_SECTOR_SIZE    (0x1FFFU)
; ..\flash\flsloader\FlsLoader_Platform.c	    49  #define FLSLOADER_UCB_SECTOR_SIZE_1     (0x3FFU)
; ..\flash\flsloader\FlsLoader_Platform.c	    50  #define FLSLOADER_MOD_8                 (0x7U)
; ..\flash\flsloader\FlsLoader_Platform.c	    51  #define FLSLOADER_NUM_8                 (0x8U)
; ..\flash\flsloader\FlsLoader_Platform.c	    52  #define FLSLOADER_UCB_DATA_SIZE         (0x80U)
; ..\flash\flsloader\FlsLoader_Platform.c	    53  #define FLSLOADER_UCB_SECTOR_SIZE       (0x400U)
; ..\flash\flsloader\FlsLoader_Platform.c	    54  #define FLSLOADER_UCBSIZE               (0x4000U)
; ..\flash\flsloader\FlsLoader_Platform.c	    55  #define FLSLOADER_NUM_OF_UCB_SECTORS    (16U)
; ..\flash\flsloader\FlsLoader_Platform.c	    56  #define FLSLOADER_UCB_OFFSETEND         (0x0003FFFU)
; ..\flash\flsloader\FlsLoader_Platform.c	    57  
; ..\flash\flsloader\FlsLoader_Platform.c	    58  #define FLSLOADER_NUM_UCB_SECTORS       (1U)
; ..\flash\flsloader\FlsLoader_Platform.c	    59  #define FLSLOADER_NUM_UCB_PAGES         (16U)
; ..\flash\flsloader\FlsLoader_Platform.c	    60  #define FLSLOADER_PAGE_SIZE             (8U)
; ..\flash\flsloader\FlsLoader_Platform.c	    61  #define FLSLOADER_SHIFT_BY_12           (12U)
; ..\flash\flsloader\FlsLoader_Platform.c	    62  #define FLSLOADER_ADDRESS_MASK          ((FlsLoader_AddressType)(0x000FFFFFU))
; ..\flash\flsloader\FlsLoader_Platform.c	    63  #define FLSLOADER_DFLASH_SECTOR_SIZE    (0x2000U)
; ..\flash\flsloader\FlsLoader_Platform.c	    64  
; ..\flash\flsloader\FlsLoader_Platform.c	    65  #define FLSLOADER_MAX_SECTORS           ((uint32) 27U)
; ..\flash\flsloader\FlsLoader_Platform.c	    66  
; ..\flash\flsloader\FlsLoader_Platform.c	    67  #define FLASH_HW_MODULE ((volatile Ifx_FLASH *)(void *) &(MODULE_FLASH0))
; ..\flash\flsloader\FlsLoader_Platform.c	    68  #define FLASH0_PROCONP ((volatile Ifx_FLASH_PROCONP*)(void *)(&FLASH0_PROCONP0))
; ..\flash\flsloader\FlsLoader_Platform.c	    69  #define FLASH0_PROCONOTP \ 
; ..\flash\flsloader\FlsLoader_Platform.c	    70           ((volatile Ifx_FLASH_PROCONOTP*)(void *)(&FLASH0_PROCONOTP0))
; ..\flash\flsloader\FlsLoader_Platform.c	    71  #define FLASH0_PROCONWOP \ 
; ..\flash\flsloader\FlsLoader_Platform.c	    72           ((volatile Ifx_FLASH_PROCONWOP*)(void *)(&FLASH0_PROCONWOP0))
; ..\flash\flsloader\FlsLoader_Platform.c	    73  
; ..\flash\flsloader\FlsLoader_Platform.c	    74  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	    75  **                      Private Constant Definitions                          **
; ..\flash\flsloader\FlsLoader_Platform.c	    76  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	    77  #define FLSLOADER_START_SEC_CONST_32BIT
; ..\flash\flsloader\FlsLoader_Platform.c	    78  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	    79  
; ..\flash\flsloader\FlsLoader_Platform.c	    80  /* Array of PFlash sector start address offsets. 
; ..\flash\flsloader\FlsLoader_Platform.c	    81     Size of this array has to be the number of sectors in PFx which has 
; ..\flash\flsloader\FlsLoader_Platform.c	    82     maximum number of sectors */
; ..\flash\flsloader\FlsLoader_Platform.c	    83  /*IFX_MISRA_RULE_08_07_STATUS=FlsLoader_PFlashSectorOffset is a constant 
; ..\flash\flsloader\FlsLoader_Platform.c	    84    array hence not declared inside the function*/
; ..\flash\flsloader\FlsLoader_Platform.c	    85  static const uint32 FlsLoader_PFlashSectorOffset[FLSLOADER_MAX_SECTORS]=
; ..\flash\flsloader\FlsLoader_Platform.c	    86  {
; ..\flash\flsloader\FlsLoader_Platform.c	    87    FLSLOADER_PF_S0,
; ..\flash\flsloader\FlsLoader_Platform.c	    88    FLSLOADER_PF_S1,
; ..\flash\flsloader\FlsLoader_Platform.c	    89    FLSLOADER_PF_S2,
; ..\flash\flsloader\FlsLoader_Platform.c	    90    FLSLOADER_PF_S3,
; ..\flash\flsloader\FlsLoader_Platform.c	    91    FLSLOADER_PF_S4,
; ..\flash\flsloader\FlsLoader_Platform.c	    92    FLSLOADER_PF_S5,
; ..\flash\flsloader\FlsLoader_Platform.c	    93    FLSLOADER_PF_S6,
; ..\flash\flsloader\FlsLoader_Platform.c	    94    FLSLOADER_PF_S7,
; ..\flash\flsloader\FlsLoader_Platform.c	    95    FLSLOADER_PF_S8,
; ..\flash\flsloader\FlsLoader_Platform.c	    96    FLSLOADER_PF_S9,
; ..\flash\flsloader\FlsLoader_Platform.c	    97    FLSLOADER_PF_S10,
; ..\flash\flsloader\FlsLoader_Platform.c	    98    FLSLOADER_PF_S11,
; ..\flash\flsloader\FlsLoader_Platform.c	    99    FLSLOADER_PF_S12,
; ..\flash\flsloader\FlsLoader_Platform.c	   100    FLSLOADER_PF_S13,
; ..\flash\flsloader\FlsLoader_Platform.c	   101    FLSLOADER_PF_S14,
; ..\flash\flsloader\FlsLoader_Platform.c	   102    FLSLOADER_PF_S15,
; ..\flash\flsloader\FlsLoader_Platform.c	   103    FLSLOADER_PF_S16,
; ..\flash\flsloader\FlsLoader_Platform.c	   104    FLSLOADER_PF_S17,
; ..\flash\flsloader\FlsLoader_Platform.c	   105    FLSLOADER_PF_S18,
; ..\flash\flsloader\FlsLoader_Platform.c	   106    FLSLOADER_PF_S19,
; ..\flash\flsloader\FlsLoader_Platform.c	   107    FLSLOADER_PF_S20,
; ..\flash\flsloader\FlsLoader_Platform.c	   108    FLSLOADER_PF_S21,
; ..\flash\flsloader\FlsLoader_Platform.c	   109    FLSLOADER_PF_S22,
; ..\flash\flsloader\FlsLoader_Platform.c	   110    FLSLOADER_PF_S23,
; ..\flash\flsloader\FlsLoader_Platform.c	   111    FLSLOADER_PF_S24,
; ..\flash\flsloader\FlsLoader_Platform.c	   112    FLSLOADER_PF_S25,
; ..\flash\flsloader\FlsLoader_Platform.c	   113    FLSLOADER_PF_S26
; ..\flash\flsloader\FlsLoader_Platform.c	   114  };
; ..\flash\flsloader\FlsLoader_Platform.c	   115  
; ..\flash\flsloader\FlsLoader_Platform.c	   116  #define FLSLOADER_STOP_SEC_CONST_32BIT
; ..\flash\flsloader\FlsLoader_Platform.c	   117    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	   118    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	   119  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   120  
; ..\flash\flsloader\FlsLoader_Platform.c	   121  #if (FLSLOADER_LOCK_UNLOCK_API == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	   122  
; ..\flash\flsloader\FlsLoader_Platform.c	   123  #define FLSLOADER_START_SEC_CONST_8BIT
; ..\flash\flsloader\FlsLoader_Platform.c	   124    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	   125    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	   126  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   127  
; ..\flash\flsloader\FlsLoader_Platform.c	   128  /* UCB_PFLASH Content */
; ..\flash\flsloader\FlsLoader_Platform.c	   129  #if ((FLSLOADER_PF0_PROT == WRITE_PROTECTION)||\ 
; ..\flash\flsloader\FlsLoader_Platform.c	   130       (FLSLOADER_PF0_PROT == READ_PROTECTION))
; ..\flash\flsloader\FlsLoader_Platform.c	   131  /* UCB_PFLASH
; ..\flash\flsloader\FlsLoader_Platform.c	   132    page 0 -bytes 0-3: FLASH0_PROCONP0
; ..\flash\flsloader\FlsLoader_Platform.c	   133            bytes 4-7: FLASH0_PROCONP1
; ..\flash\flsloader\FlsLoader_Platform.c	   134    page 1 -bytes 8-11 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   135            bytes 12-15 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   136    page 2 -bytes 16-19: copy of FLASH0_PROCONP0
; ..\flash\flsloader\FlsLoader_Platform.c	   137            bytes 20-23: copy of FLASH0_PROCONP1
; ..\flash\flsloader\FlsLoader_Platform.c	   138    page 3 -bytes 24-27 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   139            bytes 28-31 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   140    page 4 -bytes 32-39: PW0
; ..\flash\flsloader\FlsLoader_Platform.c	   141    page 5 -bytes 40-47: PW1
; ..\flash\flsloader\FlsLoader_Platform.c	   142    page 6 -bytes 48-55: PW2
; ..\flash\flsloader\FlsLoader_Platform.c	   143    page 7 -bytes 56-63: PW3
; ..\flash\flsloader\FlsLoader_Platform.c	   144    page 8 -bytes 64-71: PW0
; ..\flash\flsloader\FlsLoader_Platform.c	   145    page 9  -bytes 72-79: PW1
; ..\flash\flsloader\FlsLoader_Platform.c	   146    page 10 -bytes 80-87: PW2
; ..\flash\flsloader\FlsLoader_Platform.c	   147    page 11 -bytes 88-95: PW3
; ..\flash\flsloader\FlsLoader_Platform.c	   148    page 12 -bytes 96-103: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   149    page 13 -bytes 104-111: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   150    page 14 -bytes 112-115: confirmation code
; ..\flash\flsloader\FlsLoader_Platform.c	   151             bytes 116-119: zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   152    page 15 -bytes 120-123: confirmation code
; ..\flash\flsloader\FlsLoader_Platform.c	   153             bytes 124-127: zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   154  */
; ..\flash\flsloader\FlsLoader_Platform.c	   155  
; ..\flash\flsloader\FlsLoader_Platform.c	   156    /*IFX_MISRA_RULE_08_07_STATUS=FlsLoader_UcbPFlashPage0 is a constant array 
; ..\flash\flsloader\FlsLoader_Platform.c	   157    hence not declare in the calling function*/
; ..\flash\flsloader\FlsLoader_Platform.c	   158  static const uint8 FlsLoader_UcbPFlashPage0[FLSLOADER_UCB_SIZE] =
; ..\flash\flsloader\FlsLoader_Platform.c	   159  {
; ..\flash\flsloader\FlsLoader_Platform.c	   160  FLSLOADER_PROCONP0_BYTE0,FLSLOADER_PROCONP0_BYTE1,
; ..\flash\flsloader\FlsLoader_Platform.c	   161  FLSLOADER_PROCONP0_BYTE2,FLSLOADER_PROCONP0_BYTE3,
; ..\flash\flsloader\FlsLoader_Platform.c	   162  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   163  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   164  FLSLOADER_PROCONP0_BYTE0,FLSLOADER_PROCONP0_BYTE1,
; ..\flash\flsloader\FlsLoader_Platform.c	   165  FLSLOADER_PROCONP0_BYTE2,FLSLOADER_PROCONP0_BYTE3,
; ..\flash\flsloader\FlsLoader_Platform.c	   166  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   167  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   168  FLSLOADER_P0_PW00_B0,FLSLOADER_P0_PW00_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   169  FLSLOADER_P0_PW00_B2,FLSLOADER_P0_PW00_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   170  FLSLOADER_P0_PW01_B0,FLSLOADER_P0_PW01_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   171  FLSLOADER_P0_PW01_B2,FLSLOADER_P0_PW01_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   172  FLSLOADER_P0_PW10_B0,FLSLOADER_P0_PW10_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   173  FLSLOADER_P0_PW10_B2,FLSLOADER_P0_PW10_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   174  FLSLOADER_P0_PW11_B0,FLSLOADER_P0_PW11_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   175  FLSLOADER_P0_PW11_B2,FLSLOADER_P0_PW11_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   176  FLSLOADER_P0_PW20_B0,FLSLOADER_P0_PW20_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   177  FLSLOADER_P0_PW20_B2,FLSLOADER_P0_PW20_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   178  FLSLOADER_P0_PW21_B0,FLSLOADER_P0_PW21_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   179  FLSLOADER_P0_PW21_B2,FLSLOADER_P0_PW21_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   180  FLSLOADER_P0_PW30_B0,FLSLOADER_P0_PW30_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   181  FLSLOADER_P0_PW30_B2,FLSLOADER_P0_PW30_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   182  FLSLOADER_P0_PW31_B0,FLSLOADER_P0_PW31_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   183  FLSLOADER_P0_PW31_B2,FLSLOADER_P0_PW31_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   184  FLSLOADER_P0_PW00_B0,FLSLOADER_P0_PW00_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   185  FLSLOADER_P0_PW00_B2,FLSLOADER_P0_PW00_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   186  FLSLOADER_P0_PW01_B0,FLSLOADER_P0_PW01_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   187  FLSLOADER_P0_PW01_B2,FLSLOADER_P0_PW01_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   188  FLSLOADER_P0_PW10_B0,FLSLOADER_P0_PW10_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   189  FLSLOADER_P0_PW10_B2,FLSLOADER_P0_PW10_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   190  FLSLOADER_P0_PW11_B0,FLSLOADER_P0_PW11_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   191  FLSLOADER_P0_PW11_B2,FLSLOADER_P0_PW11_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   192  FLSLOADER_P0_PW20_B0,FLSLOADER_P0_PW20_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   193  FLSLOADER_P0_PW20_B2,FLSLOADER_P0_PW20_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   194  FLSLOADER_P0_PW21_B0,FLSLOADER_P0_PW21_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   195  FLSLOADER_P0_PW21_B2,FLSLOADER_P0_PW21_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   196  FLSLOADER_P0_PW30_B0,FLSLOADER_P0_PW30_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   197  FLSLOADER_P0_PW30_B2,FLSLOADER_P0_PW30_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   198  FLSLOADER_P0_PW31_B0,FLSLOADER_P0_PW31_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   199  FLSLOADER_P0_PW31_B2,FLSLOADER_P0_PW31_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   200  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   201  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   202  FLSLOADER_CONFIRMATION_CODE_B0,FLSLOADER_CONFIRMATION_CODE_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   203  FLSLOADER_CONFIRMATION_CODE_B2,FLSLOADER_CONFIRMATION_CODE_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   204  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   205  FLSLOADER_CONFIRMATION_CODE_B0,FLSLOADER_CONFIRMATION_CODE_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   206  FLSLOADER_CONFIRMATION_CODE_B2,FLSLOADER_CONFIRMATION_CODE_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   207  0x00U,0x00U,0x00U,0x00U
; ..\flash\flsloader\FlsLoader_Platform.c	   208  };
; ..\flash\flsloader\FlsLoader_Platform.c	   209  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   210    /*#if ((FLSLOADER_PF0_PROT == WRITE_PROTECTION)||...*/
; ..\flash\flsloader\FlsLoader_Platform.c	   211  
; ..\flash\flsloader\FlsLoader_Platform.c	   212    /* UCB_DFLASH Content */
; ..\flash\flsloader\FlsLoader_Platform.c	   213  #if (FLSLOADER_DF0_PROT != NO_PROTECTION)
; ..\flash\flsloader\FlsLoader_Platform.c	   214    /* UCB_DFLASH
; ..\flash\flsloader\FlsLoader_Platform.c	   215    page 0 -bytes 0-3: PROCOND
; ..\flash\flsloader\FlsLoader_Platform.c	   216            bytes 4-7: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   217    page 1 -bytes 8-11 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   218            bytes 12-15 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   219    page 2 -bytes 16-19: copy of PROCOND
; ..\flash\flsloader\FlsLoader_Platform.c	   220            bytes 20-23: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   221    page 3 -bytes 24-27 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   222            bytes 28-31 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   223    page 4 -bytes 32-39: PW0
; ..\flash\flsloader\FlsLoader_Platform.c	   224    page 5 -bytes 40-47: PW1
; ..\flash\flsloader\FlsLoader_Platform.c	   225    page 6 -bytes 48-55: PW2
; ..\flash\flsloader\FlsLoader_Platform.c	   226    page 7 -bytes 56-63: PW3
; ..\flash\flsloader\FlsLoader_Platform.c	   227    page 8 -bytes 64-71: PW0
; ..\flash\flsloader\FlsLoader_Platform.c	   228    page 9  -bytes 72-79: PW1
; ..\flash\flsloader\FlsLoader_Platform.c	   229    page 10 -bytes 80-87: PW2
; ..\flash\flsloader\FlsLoader_Platform.c	   230    page 11 -bytes 88-95: PW3
; ..\flash\flsloader\FlsLoader_Platform.c	   231    page 12 -bytes 96-103: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   232    page 13 -bytes 104-111: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   233    page 14 -bytes 112-115: confirmation code
; ..\flash\flsloader\FlsLoader_Platform.c	   234             bytes 116-119: zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   235    page 15 -bytes 120-123: confirmation code
; ..\flash\flsloader\FlsLoader_Platform.c	   236             bytes 124-127: zeroes*/
; ..\flash\flsloader\FlsLoader_Platform.c	   237  
; ..\flash\flsloader\FlsLoader_Platform.c	   238    /*IFX_MISRA_RULE_08_07_STATUS=lsLoader_UcbDFlashPage0 is a constant array 
; ..\flash\flsloader\FlsLoader_Platform.c	   239    hence not declare in the calling function*/
; ..\flash\flsloader\FlsLoader_Platform.c	   240  static const uint8 FlsLoader_UcbDFlashPage0[FLSLOADER_UCB_SIZE] = {
; ..\flash\flsloader\FlsLoader_Platform.c	   241  FLSLOADER_PROCOND_BYTE0,FLSLOADER_PROCOND_BYTE1,
; ..\flash\flsloader\FlsLoader_Platform.c	   242  FLSLOADER_PROCOND_BYTE2,FLSLOADER_PROCOND_BYTE3,
; ..\flash\flsloader\FlsLoader_Platform.c	   243  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   244  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   245  FLSLOADER_PROCOND_BYTE0,FLSLOADER_PROCOND_BYTE1,
; ..\flash\flsloader\FlsLoader_Platform.c	   246  FLSLOADER_PROCOND_BYTE2,FLSLOADER_PROCOND_BYTE3,
; ..\flash\flsloader\FlsLoader_Platform.c	   247  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   248  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   249  FLSLOADER_DF0_PW00_B0,FLSLOADER_DF0_PW00_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   250  FLSLOADER_DF0_PW00_B2,FLSLOADER_DF0_PW00_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   251  FLSLOADER_DF0_PW01_B0,FLSLOADER_DF0_PW01_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   252  FLSLOADER_DF0_PW01_B2,FLSLOADER_DF0_PW01_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   253  FLSLOADER_DF0_PW10_B0,FLSLOADER_DF0_PW10_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   254  FLSLOADER_DF0_PW10_B2,FLSLOADER_DF0_PW10_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   255  FLSLOADER_DF0_PW11_B0,FLSLOADER_DF0_PW11_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   256  FLSLOADER_DF0_PW11_B2,FLSLOADER_DF0_PW11_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   257  FLSLOADER_DF0_PW20_B0,FLSLOADER_DF0_PW20_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   258  FLSLOADER_DF0_PW20_B2,FLSLOADER_DF0_PW20_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   259  FLSLOADER_DF0_PW21_B0,FLSLOADER_DF0_PW21_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   260  FLSLOADER_DF0_PW21_B2,FLSLOADER_DF0_PW21_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   261  FLSLOADER_DF0_PW30_B0,FLSLOADER_DF0_PW30_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   262  FLSLOADER_DF0_PW30_B2,FLSLOADER_DF0_PW30_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   263  FLSLOADER_DF0_PW31_B0,FLSLOADER_DF0_PW31_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   264  FLSLOADER_DF0_PW31_B2,FLSLOADER_DF0_PW31_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   265  FLSLOADER_DF0_PW00_B0,FLSLOADER_DF0_PW00_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   266  FLSLOADER_DF0_PW00_B2,FLSLOADER_DF0_PW00_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   267  FLSLOADER_DF0_PW01_B0,FLSLOADER_DF0_PW01_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   268  FLSLOADER_DF0_PW01_B2,FLSLOADER_DF0_PW01_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   269  FLSLOADER_DF0_PW10_B0,FLSLOADER_DF0_PW10_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   270  FLSLOADER_DF0_PW10_B2,FLSLOADER_DF0_PW10_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   271  FLSLOADER_DF0_PW11_B0,FLSLOADER_DF0_PW11_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   272  FLSLOADER_DF0_PW11_B2,FLSLOADER_DF0_PW11_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   273  FLSLOADER_DF0_PW20_B0,FLSLOADER_DF0_PW20_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   274  FLSLOADER_DF0_PW20_B2,FLSLOADER_DF0_PW20_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   275  FLSLOADER_DF0_PW21_B0,FLSLOADER_DF0_PW21_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   276  FLSLOADER_DF0_PW21_B2,FLSLOADER_DF0_PW21_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   277  FLSLOADER_DF0_PW30_B0,FLSLOADER_DF0_PW30_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   278  FLSLOADER_DF0_PW30_B2,FLSLOADER_DF0_PW30_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   279  FLSLOADER_DF0_PW31_B0,FLSLOADER_DF0_PW31_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   280  FLSLOADER_DF0_PW31_B2,FLSLOADER_DF0_PW31_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   281  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   282  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   283  FLSLOADER_CONFIRMATION_CODE_B0,FLSLOADER_CONFIRMATION_CODE_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   284  FLSLOADER_CONFIRMATION_CODE_B2,FLSLOADER_CONFIRMATION_CODE_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   285  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   286  FLSLOADER_CONFIRMATION_CODE_B0,FLSLOADER_CONFIRMATION_CODE_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   287  FLSLOADER_CONFIRMATION_CODE_B2,FLSLOADER_CONFIRMATION_CODE_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   288  0x00U,0x00U,0x00U,0x00U
; ..\flash\flsloader\FlsLoader_Platform.c	   289  };
; ..\flash\flsloader\FlsLoader_Platform.c	   290  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   291    /*#if (FLSLOADER_PF0_PROT != NO_PROTECTION)*/
; ..\flash\flsloader\FlsLoader_Platform.c	   292    /* UCB OTP FLASH content */
; ..\flash\flsloader\FlsLoader_Platform.c	   293  #if ((FLSLOADER_PF0_PROT == OTP_PROTECTION)||\ 
; ..\flash\flsloader\FlsLoader_Platform.c	   294       (FLSLOADER_PF0_PROT == WOP_PROTECTION))
; ..\flash\flsloader\FlsLoader_Platform.c	   295  /* UCB_OTP
; ..\flash\flsloader\FlsLoader_Platform.c	   296    page 0 -bytes 0-3: PROCONOTP0
; ..\flash\flsloader\FlsLoader_Platform.c	   297            bytes 4-7: PROCONOTP1
; ..\flash\flsloader\FlsLoader_Platform.c	   298    page 1 -bytes 8-15 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   299    page 2 -bytes 16-19: copy of PROCONOTP0
; ..\flash\flsloader\FlsLoader_Platform.c	   300            bytes 20-23: copy of PROCONOTP1
; ..\flash\flsloader\FlsLoader_Platform.c	   301    page 3 -bytes 24-31 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   302    page 4 -bytes 32-35: PROCONWOP0
; ..\flash\flsloader\FlsLoader_Platform.c	   303            bytes 36-39: PROCONWOP1
; ..\flash\flsloader\FlsLoader_Platform.c	   304    page 5  -bytes 40-47: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   305    page 6  -bytes 48-51: copy of PROCONWOP0
; ..\flash\flsloader\FlsLoader_Platform.c	   306    page    -bytes 52-55: copy of ROCONWOP1
; ..\flash\flsloader\FlsLoader_Platform.c	   307    page 7   -bytes 56-63: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   308    page 8   -bytes 64-71: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   309    page 9  -bytes 72-79: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   310    page 10  -bytes 80-87: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   311    page 11  -bytes 88-95: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   312    page 12  -bytes 96-103: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   313    page 13  -bytes 104-111 : all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   314    page 14   bytes 112-115: confirmation code
; ..\flash\flsloader\FlsLoader_Platform.c	   315             -bytes 116-119: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   316    page 15  -bytes 120-123: copy of confirmation code
; ..\flash\flsloader\FlsLoader_Platform.c	   317              bytes 124-127: all zeroes
; ..\flash\flsloader\FlsLoader_Platform.c	   318  */
; ..\flash\flsloader\FlsLoader_Platform.c	   319  
; ..\flash\flsloader\FlsLoader_Platform.c	   320    /*IFX_MISRA_RULE_08_07_STATUS=lsLoader_UcbOTPPFlashPage0 is a constant array 
; ..\flash\flsloader\FlsLoader_Platform.c	   321    hence not declare in the calling function*/
; ..\flash\flsloader\FlsLoader_Platform.c	   322  static const uint8 FlsLoader_UcbOTPFlashPage0[FLSLOADER_UCB_SIZE] = {
; ..\flash\flsloader\FlsLoader_Platform.c	   323  FLSLOADER_PROCONOTP0_BYTE0,FLSLOADER_PROCONOTP0_BYTE1,
; ..\flash\flsloader\FlsLoader_Platform.c	   324  FLSLOADER_PROCONOTP0_BYTE2,FLSLOADER_PROCONOTP0_BYTE3,
; ..\flash\flsloader\FlsLoader_Platform.c	   325  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   326  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   327  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   328  FLSLOADER_PROCONOTP0_BYTE0,FLSLOADER_PROCONOTP0_BYTE1,
; ..\flash\flsloader\FlsLoader_Platform.c	   329  FLSLOADER_PROCONOTP0_BYTE2,FLSLOADER_PROCONOTP0_BYTE3,
; ..\flash\flsloader\FlsLoader_Platform.c	   330  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   331  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   332  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   333  FLSLOADER_PROCONWOP0_BYTE0,FLSLOADER_PROCONWOP0_BYTE1,
; ..\flash\flsloader\FlsLoader_Platform.c	   334  FLSLOADER_PROCONWOP0_BYTE2,FLSLOADER_PROCONWOP0_BYTE3,
; ..\flash\flsloader\FlsLoader_Platform.c	   335  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   336  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   337  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   338  FLSLOADER_PROCONWOP0_BYTE0,FLSLOADER_PROCONWOP0_BYTE1,
; ..\flash\flsloader\FlsLoader_Platform.c	   339  FLSLOADER_PROCONWOP0_BYTE2,FLSLOADER_PROCONWOP0_BYTE3,
; ..\flash\flsloader\FlsLoader_Platform.c	   340  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   341  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   342  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   343  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   344  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   345  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   346  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   347  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   348  0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   349  FLSLOADER_CONFIRMATION_CODE_B0,FLSLOADER_CONFIRMATION_CODE_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   350  FLSLOADER_CONFIRMATION_CODE_B2,FLSLOADER_CONFIRMATION_CODE_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   351  0x00U,0x00U,0x00U,0x00U,
; ..\flash\flsloader\FlsLoader_Platform.c	   352  FLSLOADER_CONFIRMATION_CODE_B0,FLSLOADER_CONFIRMATION_CODE_B1,
; ..\flash\flsloader\FlsLoader_Platform.c	   353  FLSLOADER_CONFIRMATION_CODE_B2,FLSLOADER_CONFIRMATION_CODE_B3,
; ..\flash\flsloader\FlsLoader_Platform.c	   354  0x00U,0x00U,0x00U,0x00U};
; ..\flash\flsloader\FlsLoader_Platform.c	   355  
; ..\flash\flsloader\FlsLoader_Platform.c	   356  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   357  /*  #if ((FLSLOADER_PF0_PROT == OTP_PROTECTION)...*/
; ..\flash\flsloader\FlsLoader_Platform.c	   358  #define FLSLOADER_STOP_SEC_CONST_8BIT
; ..\flash\flsloader\FlsLoader_Platform.c	   359    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	   360    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	   361  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   362  #endif 
; ..\flash\flsloader\FlsLoader_Platform.c	   363  /*  #if (FLSLOADER_LOCK_UNLOCK_API == STD_ON)...*/
; ..\flash\flsloader\FlsLoader_Platform.c	   364  
; ..\flash\flsloader\FlsLoader_Platform.c	   365  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	   366  **                    Prototypes Of Local Functions                           **
; ..\flash\flsloader\FlsLoader_Platform.c	   367  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	   368  #define FLSLOADER_START_SEC_WRITE_CODE
; ..\flash\flsloader\FlsLoader_Platform.c	   369    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	   370    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	   371  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   372  #if (FLSLOADER_DEV_ERROR_DETECT == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	   373    /*
; ..\flash\flsloader\FlsLoader_Platform.c	   374    Function FlsLoader_lAddressPageCheck
; ..\flash\flsloader\FlsLoader_Platform.c	   375    Input Parameters: TargetAddress
; ..\flash\flsloader\FlsLoader_Platform.c	   376    */
; ..\flash\flsloader\FlsLoader_Platform.c	   377  IFX_LOCAL_INLINE uint8 FlsLoader_lAddressPageCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	   378                                           FlsLoader_AddressType TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	   379    /*
; ..\flash\flsloader\FlsLoader_Platform.c	   380    Function FlsLoader_lDFlashCheck
; ..\flash\flsloader\FlsLoader_Platform.c	   381    Input Parameters: TargetAddress
; ..\flash\flsloader\FlsLoader_Platform.c	   382    */
; ..\flash\flsloader\FlsLoader_Platform.c	   383  IFX_LOCAL_INLINE boolean FlsLoader_lDFlashCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	   384                                       FlsLoader_AddressType TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	   385    /*
; ..\flash\flsloader\FlsLoader_Platform.c	   386    Function FlsLoader_lDFlashAddressCheck
; ..\flash\flsloader\FlsLoader_Platform.c	   387    Input Parameters: TargetAddress
; ..\flash\flsloader\FlsLoader_Platform.c	   388    */
; ..\flash\flsloader\FlsLoader_Platform.c	   389  IFX_LOCAL_INLINE uint8 FlsLoader_lDFlashAddressCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	   390                                             FlsLoader_AddressType TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	   391    /*
; ..\flash\flsloader\FlsLoader_Platform.c	   392    Function FlsLoader_lDFlashLengthCheck
; ..\flash\flsloader\FlsLoader_Platform.c	   393    Input Parameters: TargetAddress,Length
; ..\flash\flsloader\FlsLoader_Platform.c	   394    */
; ..\flash\flsloader\FlsLoader_Platform.c	   395  IFX_LOCAL_INLINE uint8 FlsLoader_lDFlashLengthCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	   396                 FlsLoader_LengthType Length,FlsLoader_AddressType TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	   397      /*
; ..\flash\flsloader\FlsLoader_Platform.c	   398    Function FlsLoader_lPFlashCheck
; ..\flash\flsloader\FlsLoader_Platform.c	   399    Input Parameters: TargetAddress
; ..\flash\flsloader\FlsLoader_Platform.c	   400    */
; ..\flash\flsloader\FlsLoader_Platform.c	   401  IFX_LOCAL_INLINE boolean FlsLoader_lPFlashCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	   402                                       FlsLoader_AddressType TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	   403    /*
; ..\flash\flsloader\FlsLoader_Platform.c	   404    Function FlsLoader_lPFlashAddressCheck
; ..\flash\flsloader\FlsLoader_Platform.c	   405    Input Parameters: TargetAddress
; ..\flash\flsloader\FlsLoader_Platform.c	   406    */
; ..\flash\flsloader\FlsLoader_Platform.c	   407  IFX_LOCAL_INLINE uint8 FlsLoader_lPFlashAddressCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	   408                                             FlsLoader_AddressType TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	   409    /*
; ..\flash\flsloader\FlsLoader_Platform.c	   410    Function FlsLoader_lDFlashPageCheck
; ..\flash\flsloader\FlsLoader_Platform.c	   411    Input Parameters: TargetAddress,Length
; ..\flash\flsloader\FlsLoader_Platform.c	   412    */
; ..\flash\flsloader\FlsLoader_Platform.c	   413  IFX_LOCAL_INLINE boolean FlsLoader_lDFlashPageCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	   414               FlsLoader_AddressType TargetAddress,FlsLoader_LengthType Length);
; ..\flash\flsloader\FlsLoader_Platform.c	   415  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   416  
; ..\flash\flsloader\FlsLoader_Platform.c	   417  #if (FLSLOADER_ENABLE_LOCKCHECK == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	   418  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lPFlashSectorCheck (
; ..\flash\flsloader\FlsLoader_Platform.c	   419                                      FlsLoader_AddressType TargetAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   420                                      FlsLoader_LengthType Length);
; ..\flash\flsloader\FlsLoader_Platform.c	   421  
; ..\flash\flsloader\FlsLoader_Platform.c	   422  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lCheckSectorProt(
; ..\flash\flsloader\FlsLoader_Platform.c	   423                                      uint32 TargetAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   424                                      FlsLoader_LengthType Length);
; ..\flash\flsloader\FlsLoader_Platform.c	   425  
; ..\flash\flsloader\FlsLoader_Platform.c	   426  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lSectorProtCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	   427                                      uint32 SectorNumber,
; ..\flash\flsloader\FlsLoader_Platform.c	   428                                      FlsLoader_LengthType Length );
; ..\flash\flsloader\FlsLoader_Platform.c	   429  
; ..\flash\flsloader\FlsLoader_Platform.c	   430  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   431  #define FLSLOADER_STOP_SEC_WRITE_CODE
; ..\flash\flsloader\FlsLoader_Platform.c	   432    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	   433    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	   434  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   435  
; ..\flash\flsloader\FlsLoader_Platform.c	   436  #define FLSLOADER_START_SEC_CODE
; ..\flash\flsloader\FlsLoader_Platform.c	   437    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	   438    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	   439  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   440  
; ..\flash\flsloader\FlsLoader_Platform.c	   441  #if (FLSLOADER_LOCK_UNLOCK_API == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	   442  IFX_LOCAL_INLINE FlsLoader_ReturnType  FlsLoader_lLockCmdCycles(
; ..\flash\flsloader\FlsLoader_Platform.c	   443                          FlsLoader_AddressType StartAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   444                          FlsLoader_AddressType UcbSectorAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   445                          const FlsLoader_AddressType *PdataPtr);
; ..\flash\flsloader\FlsLoader_Platform.c	   446  
; ..\flash\flsloader\FlsLoader_Platform.c	   447  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lLockCmdCyclesWrite(
; ..\flash\flsloader\FlsLoader_Platform.c	   448                          FlsLoader_AddressType StartAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   449                          FlsLoader_AddressType UcbSectorAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   450                          const FlsLoader_AddressType *PdataPtr);
; ..\flash\flsloader\FlsLoader_Platform.c	   451  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   452  
; ..\flash\flsloader\FlsLoader_Platform.c	   453  IFX_LOCAL_INLINE uint32 FlsLoader_lSectorNumber(uint32 Offset);
; ..\flash\flsloader\FlsLoader_Platform.c	   454  #define FLSLOADER_STOP_SEC_CODE
; ..\flash\flsloader\FlsLoader_Platform.c	   455    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	   456    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	   457  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   458  /******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	   459  **                      Private Variable Definitions                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   460  ******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	   461  #ifdef IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   462    #define FLSLOADER_START_SEC_VAR_32BIT
; ..\flash\flsloader\FlsLoader_Platform.c	   463    #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   464      extern volatile uint32 TestFlsloader_DebugMask00;
; ..\flash\flsloader\FlsLoader_Platform.c	   465      extern volatile uint32 TestFlsloader_DebugMask01;
; ..\flash\flsloader\FlsLoader_Platform.c	   466      extern volatile uint32 TestFlsloader_DebugMask03;
; ..\flash\flsloader\FlsLoader_Platform.c	   467      extern volatile uint32 TestFlsloader_DebugMask09;
; ..\flash\flsloader\FlsLoader_Platform.c	   468      extern volatile uint32 TestFlsloader_DebugMask06;
; ..\flash\flsloader\FlsLoader_Platform.c	   469      extern volatile uint32 TestFlsloader_DebugMask08;
; ..\flash\flsloader\FlsLoader_Platform.c	   470      extern volatile uint32 TestFlsloader_DebugMask10;
; ..\flash\flsloader\FlsLoader_Platform.c	   471      extern volatile uint32 TestFlsloader_DebugMask07;
; ..\flash\flsloader\FlsLoader_Platform.c	   472      extern volatile uint32 TestFlsloader_DebugMask11;
; ..\flash\flsloader\FlsLoader_Platform.c	   473      extern volatile uint32 TestFlsloader_DebugMask12;
; ..\flash\flsloader\FlsLoader_Platform.c	   474      extern volatile uint32 TestFlsloader_DebugMask13;
; ..\flash\flsloader\FlsLoader_Platform.c	   475      extern volatile uint32 TestFlsloader_DebugMask14;
; ..\flash\flsloader\FlsLoader_Platform.c	   476      extern volatile uint32 TestFlsloader_DebugMask15;
; ..\flash\flsloader\FlsLoader_Platform.c	   477    #define FLSLOADER_STOP_SEC_VAR_32BIT
; ..\flash\flsloader\FlsLoader_Platform.c	   478    #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   479  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   480  
; ..\flash\flsloader\FlsLoader_Platform.c	   481  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	   482  **                      Global Function Definitions                           **
; ..\flash\flsloader\FlsLoader_Platform.c	   483  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	   484  
; ..\flash\flsloader\FlsLoader_Platform.c	   485  #define FLSLOADER_START_SEC_CODE
; ..\flash\flsloader\FlsLoader_Platform.c	   486    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	   487    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	   488  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	   489  
; ..\flash\flsloader\FlsLoader_Platform.c	   490  #if (FLSLOADER_LOCK_UNLOCK_API == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	   491  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	   492  ** Syntax           : FlsLoader_ReturnType FlsLoader_lLock(void)              **
; ..\flash\flsloader\FlsLoader_Platform.c	   493  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   494  ** Service ID       :   NA                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   495  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   496  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	   497  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   498  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   499  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   500  ** Parameters (in)  : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   501  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   502  ** Parameters (out) :                                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   503  ** Return value     : FlsLoader_ReturnType                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   504  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   505  ** Description      : This function Locks the required profile and called     **
; ..\flash\flsloader\FlsLoader_Platform.c	   506  **                    from Lock API.                                          **
; ..\flash\flsloader\FlsLoader_Platform.c	   507  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   508  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	   509  FlsLoader_ReturnType FlsLoader_lLock(void)
; ..\flash\flsloader\FlsLoader_Platform.c	   510  {
; ..\flash\flsloader\FlsLoader_Platform.c	   511    FlsLoader_ReturnType RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	   512    #if (FLSLOADER_DF0_PROT != NO_PROTECTION)
; ..\flash\flsloader\FlsLoader_Platform.c	   513    FlsLoader_ReturnType RetErrorDF;
; ..\flash\flsloader\FlsLoader_Platform.c	   514    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   515    
; ..\flash\flsloader\FlsLoader_Platform.c	   516    #if ((FLSLOADER_DF0_PROT != NO_PROTECTION) ||\ 
; ..\flash\flsloader\FlsLoader_Platform.c	   517         (FLSLOADER_PF0_PROT != NO_PROTECTION))  
; ..\flash\flsloader\FlsLoader_Platform.c	   518    FlsLoader_AddressType StartAddress;
; ..\flash\flsloader\FlsLoader_Platform.c	   519    FlsLoader_AddressType UcbSectorAddress;
; ..\flash\flsloader\FlsLoader_Platform.c	   520    const FlsLoader_AddressType *PdataPtr ;
; ..\flash\flsloader\FlsLoader_Platform.c	   521    StartAddress = FLSLOADER_DFLASH0_START_ADDRESS;
; ..\flash\flsloader\FlsLoader_Platform.c	   522    #endif  
; ..\flash\flsloader\FlsLoader_Platform.c	   523    
; ..\flash\flsloader\FlsLoader_Platform.c	   524    #if ((FLSLOADER_DF0_PROT == NO_PROTECTION) &&\ 
; ..\flash\flsloader\FlsLoader_Platform.c	   525         (FLSLOADER_PF0_PROT == NO_PROTECTION))
; ..\flash\flsloader\FlsLoader_Platform.c	   526    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	   527    #else
; ..\flash\flsloader\FlsLoader_Platform.c	   528    #if ((FLSLOADER_PF0_PROT == WRITE_PROTECTION)||\ 
; ..\flash\flsloader\FlsLoader_Platform.c	   529         (FLSLOADER_PF0_PROT == READ_PROTECTION))
; ..\flash\flsloader\FlsLoader_Platform.c	   530    UcbSectorAddress = FLSLOADER_UCB_PFLASH;
; ..\flash\flsloader\FlsLoader_Platform.c	   531    /*IFX_MISRA_RULE_11_05_STATUS="FlsLoader_UcbPFlashPage0" attempt to cast away
; ..\flash\flsloader\FlsLoader_Platform.c	   532    const in terms of pointer access.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   533    PdataPtr = (FlsLoader_AddressType *)(void*)(&FlsLoader_UcbPFlashPage0[0]);
; ..\flash\flsloader\FlsLoader_Platform.c	   534    RetError = FlsLoader_lLockCmdCycles(StartAddress,UcbSectorAddress,PdataPtr);
; ..\flash\flsloader\FlsLoader_Platform.c	   535    #elif ((FLSLOADER_PF0_PROT == OTP_PROTECTION)||\ 
; ..\flash\flsloader\FlsLoader_Platform.c	   536           (FLSLOADER_PF0_PROT == WOP_PROTECTION))
; ..\flash\flsloader\FlsLoader_Platform.c	   537    UcbSectorAddress = FLSLOADER_UCB_OTP;
; ..\flash\flsloader\FlsLoader_Platform.c	   538    /*IFX_MISRA_RULE_11_05_STATUS="FlsLoader_UcbOTPFlashPage0" attempt to 
; ..\flash\flsloader\FlsLoader_Platform.c	   539    cast away const in terms of pointer access.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   540    PdataPtr = (FlsLoader_AddressType *)(void*)(&FlsLoader_UcbOTPFlashPage0[0]);
; ..\flash\flsloader\FlsLoader_Platform.c	   541    RetError = FlsLoader_lLockCmdCycles(StartAddress,UcbSectorAddress,PdataPtr);
; ..\flash\flsloader\FlsLoader_Platform.c	   542    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   543    #if (FLSLOADER_DF0_PROT != NO_PROTECTION)
; ..\flash\flsloader\FlsLoader_Platform.c	   544    UcbSectorAddress = FLSLOADER_UCB_DFLASH;
; ..\flash\flsloader\FlsLoader_Platform.c	   545    /*IFX_MISRA_RULE_11_05_STATUS="FlsLoader_UcbDFlashPage0" attempt to cast away
; ..\flash\flsloader\FlsLoader_Platform.c	   546    const in terms of pointer access.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   547    PdataPtr = (FlsLoader_AddressType *)((void*)FlsLoader_UcbDFlashPage0);
; ..\flash\flsloader\FlsLoader_Platform.c	   548    RetErrorDF = FlsLoader_lLockCmdCycles(StartAddress,UcbSectorAddress,PdataPtr);
; ..\flash\flsloader\FlsLoader_Platform.c	   549    #if (FLSLOADER_PF0_PROT != NO_PROTECTION)
; ..\flash\flsloader\FlsLoader_Platform.c	   550    if(RetError == FLSLOADER_E_OK)
; ..\flash\flsloader\FlsLoader_Platform.c	   551    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   552    {
; ..\flash\flsloader\FlsLoader_Platform.c	   553      RetError = RetErrorDF;
; ..\flash\flsloader\FlsLoader_Platform.c	   554    }
; ..\flash\flsloader\FlsLoader_Platform.c	   555    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   556    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   557    return RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	   558  }
; ..\flash\flsloader\FlsLoader_Platform.c	   559  
; ..\flash\flsloader\FlsLoader_Platform.c	   560  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	   561  ** Traceability : [cover parentID=DS_NAS_FLSLOADER_PR3161_2]                  **
; ..\flash\flsloader\FlsLoader_Platform.c	   562  **            [/cover]                                                        **
; ..\flash\flsloader\FlsLoader_Platform.c	   563  ** Syntax   :  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lLockCmdCycles(**
; ..\flash\flsloader\FlsLoader_Platform.c	   564  **                        FlsLoader_AddressType TargetAddress ,               **
; ..\flash\flsloader\FlsLoader_Platform.c	   565  **                        FlsLoader_AddressType UcbSectorAddress,             **
; ..\flash\flsloader\FlsLoader_Platform.c	   566  **                        FlashLoader_DataType* PdataPtr                      **
; ..\flash\flsloader\FlsLoader_Platform.c	   567  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	   568  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   569  ** Service ID       :   NA                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   570  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   571  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	   572  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   573  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   574  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   575  ** Parameters (in)  : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   576  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   577  ** Parameters (out) :                                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   578  ** Return value     : FlsLoader_ReturnType                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   579  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   580  ** Description      : This function Locks the required profile and called     **
; ..\flash\flsloader\FlsLoader_Platform.c	   581  **                    from Lock  API.                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   582  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   583  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	   584  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lLockCmdCycles(
; ..\flash\flsloader\FlsLoader_Platform.c	   585                           FlsLoader_AddressType StartAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   586                           FlsLoader_AddressType UcbSectorAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   587                           const FlsLoader_AddressType *PdataPtr)
; ..\flash\flsloader\FlsLoader_Platform.c	   588  {
; ..\flash\flsloader\FlsLoader_Platform.c	   589    FlsLoader_ReturnType  RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	   590    volatile uint32       BusyCheck;
; ..\flash\flsloader\FlsLoader_Platform.c	   591    uint32                InnerCount;
; ..\flash\flsloader\FlsLoader_Platform.c	   592    uint32                Icr;
; ..\flash\flsloader\FlsLoader_Platform.c	   593    volatile uint32       FlashFsr;
; ..\flash\flsloader\FlsLoader_Platform.c	   594    
; ..\flash\flsloader\FlsLoader_Platform.c	   595    #if ((FLSLOADER_PF0_PROT == OTP_PROTECTION)||\ 
; ..\flash\flsloader\FlsLoader_Platform.c	   596         (FLSLOADER_PF0_PROT == WOP_PROTECTION))
; ..\flash\flsloader\FlsLoader_Platform.c	   597    uint32 LoopCount;
; ..\flash\flsloader\FlsLoader_Platform.c	   598    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   599  
; ..\flash\flsloader\FlsLoader_Platform.c	   600    /* Enter Critical Section */
; ..\flash\flsloader\FlsLoader_Platform.c	   601    Icr = FlsLoader_lDisableInts();
; ..\flash\flsloader\FlsLoader_Platform.c	   602  
; ..\flash\flsloader\FlsLoader_Platform.c	   603    #ifndef IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   604    /* Erase the corresponding User Configuraiton Block */
; ..\flash\flsloader\FlsLoader_Platform.c	   605    /* cycle 1 */
; ..\flash\flsloader\FlsLoader_Platform.c	   606    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	   607    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   608    /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   609    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   610    /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   611    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   612    *((FlsLoader_AddressType*)FLSLOADER_GET_CYCLE_AA50(StartAddress))  = 
; ..\flash\flsloader\FlsLoader_Platform.c	   613                                                               UcbSectorAddress;
; ..\flash\flsloader\FlsLoader_Platform.c	   614    /* cycle 2 */
; ..\flash\flsloader\FlsLoader_Platform.c	   615    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	   616    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   617    /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   618    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   619    /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   620    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   621    *((FlsLoader_AddressType*)FLSLOADER_GET_CYCLE_AA58(StartAddress))  = 
; ..\flash\flsloader\FlsLoader_Platform.c	   622                                                      FLSLOADER_NUM_UCB_SECTORS;
; ..\flash\flsloader\FlsLoader_Platform.c	   623    /* cycle 3 */
; ..\flash\flsloader\FlsLoader_Platform.c	   624    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	   625    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   626    /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   627    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   628    /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   629    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   630    *((FlsLoader_AddressType*)FLSLOADER_GET_CYCLE_AAA8(StartAddress))  = 
; ..\flash\flsloader\FlsLoader_Platform.c	   631                                                             FLSLOADER_VALUE_80;
; ..\flash\flsloader\FlsLoader_Platform.c	   632    /* cycle 4 */
; ..\flash\flsloader\FlsLoader_Platform.c	   633    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	   634    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   635    /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   636    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   637    /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   638    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   639    *((FlsLoader_AddressType*)FLSLOADER_GET_CYCLE_AAA8(StartAddress))  = 
; ..\flash\flsloader\FlsLoader_Platform.c	   640                                                             FLSLOADER_VALUE_50;
; ..\flash\flsloader\FlsLoader_Platform.c	   641    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   642    InnerCount = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	   643  
; ..\flash\flsloader\FlsLoader_Platform.c	   644    #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   645    FlashFsr =  TestFlsloader_DebugMask12;
; ..\flash\flsloader\FlsLoader_Platform.c	   646    #else
; ..\flash\flsloader\FlsLoader_Platform.c	   647    FlashFsr = FLASH0_FSR.B.ERASE;
; ..\flash\flsloader\FlsLoader_Platform.c	   648    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   649  
; ..\flash\flsloader\FlsLoader_Platform.c	   650    while((FlashFsr != FLSLOADER_BIT_SET) &&
; ..\flash\flsloader\FlsLoader_Platform.c	   651         (InnerCount < FLSLOADER_CMDCYCLE_TIMEOUT))
; ..\flash\flsloader\FlsLoader_Platform.c	   652    {
; ..\flash\flsloader\FlsLoader_Platform.c	   653      InnerCount++;
; ..\flash\flsloader\FlsLoader_Platform.c	   654      #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   655      FlashFsr =  TestFlsloader_DebugMask12;
; ..\flash\flsloader\FlsLoader_Platform.c	   656      #else
; ..\flash\flsloader\FlsLoader_Platform.c	   657      FlashFsr = FLASH0_FSR.B.ERASE;
; ..\flash\flsloader\FlsLoader_Platform.c	   658      #endif   
; ..\flash\flsloader\FlsLoader_Platform.c	   659    }
; ..\flash\flsloader\FlsLoader_Platform.c	   660  
; ..\flash\flsloader\FlsLoader_Platform.c	   661    if(InnerCount < FLSLOADER_CMDCYCLE_TIMEOUT)
; ..\flash\flsloader\FlsLoader_Platform.c	   662    {
; ..\flash\flsloader\FlsLoader_Platform.c	   663      /* Check for FLASH BUSY flag */
; ..\flash\flsloader\FlsLoader_Platform.c	   664      /* PnBUSY and D0BUSY */
; ..\flash\flsloader\FlsLoader_Platform.c	   665      InnerCount = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	   666      #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   667      BusyCheck =  TestFlsloader_DebugMask03;
; ..\flash\flsloader\FlsLoader_Platform.c	   668      #else
; ..\flash\flsloader\FlsLoader_Platform.c	   669      BusyCheck = (FLASH0_FSR.U & (uint32)FLSLOADER_NUM_2);
; ..\flash\flsloader\FlsLoader_Platform.c	   670      #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   671      while(( BusyCheck != 0U) && 
; ..\flash\flsloader\FlsLoader_Platform.c	   672            (InnerCount < FLSLOADER_BUSY_TIMEOUT))
; ..\flash\flsloader\FlsLoader_Platform.c	   673      {
; ..\flash\flsloader\FlsLoader_Platform.c	   674        InnerCount++;
; ..\flash\flsloader\FlsLoader_Platform.c	   675        #if (FLSLOADER_USER_FUNC_CONFIGURED == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	   676        (FLSLOADER_USER_DEFINED_FUNCTION)();
; ..\flash\flsloader\FlsLoader_Platform.c	   677        #endif      
; ..\flash\flsloader\FlsLoader_Platform.c	   678        #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   679        BusyCheck = TestFlsloader_DebugMask03;
; ..\flash\flsloader\FlsLoader_Platform.c	   680        #else
; ..\flash\flsloader\FlsLoader_Platform.c	   681        BusyCheck = (FLASH0_FSR.U & (uint32)FLSLOADER_NUM_2);
; ..\flash\flsloader\FlsLoader_Platform.c	   682        #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   683      }
; ..\flash\flsloader\FlsLoader_Platform.c	   684      if (InnerCount < FLSLOADER_BUSY_TIMEOUT )
; ..\flash\flsloader\FlsLoader_Platform.c	   685      {
; ..\flash\flsloader\FlsLoader_Platform.c	   686        /* This check makes sure that  erase operation is requested,
; ..\flash\flsloader\FlsLoader_Platform.c	   687        No protection error and no sequence error has occurred. */
; ..\flash\flsloader\FlsLoader_Platform.c	   688        
; ..\flash\flsloader\FlsLoader_Platform.c	   689        /* This check makes sure No protection error and no sequence error
; ..\flash\flsloader\FlsLoader_Platform.c	   690        and No operation error has occurred.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   691        #ifdef IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   692        RetError = TestFlsloader_DebugMask13;
; ..\flash\flsloader\FlsLoader_Platform.c	   693        #else
; ..\flash\flsloader\FlsLoader_Platform.c	   694        RetError = FlsLoader_lProtSeqOperErrorCheck();
; ..\flash\flsloader\FlsLoader_Platform.c	   695        #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   696      }
; ..\flash\flsloader\FlsLoader_Platform.c	   697      else
; ..\flash\flsloader\FlsLoader_Platform.c	   698      {
; ..\flash\flsloader\FlsLoader_Platform.c	   699        RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	   700      }
; ..\flash\flsloader\FlsLoader_Platform.c	   701    }
; ..\flash\flsloader\FlsLoader_Platform.c	   702    else
; ..\flash\flsloader\FlsLoader_Platform.c	   703    {
; ..\flash\flsloader\FlsLoader_Platform.c	   704      RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	   705    }
; ..\flash\flsloader\FlsLoader_Platform.c	   706    /* Clear the status if the operation is successful.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   707    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	   708    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   709    /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   710    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   711    /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 for address
; ..\flash\flsloader\FlsLoader_Platform.c	   712    comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   713     *((FlsLoader_AddressType*)FlsLoader_lClearStatus(StartAddress)) = 
; ..\flash\flsloader\FlsLoader_Platform.c	   714                                                             FLSLOADER_VALUE_FA;
; ..\flash\flsloader\FlsLoader_Platform.c	   715  
; ..\flash\flsloader\FlsLoader_Platform.c	   716    /* Enter Critical Section */
; ..\flash\flsloader\FlsLoader_Platform.c	   717    FlsLoader_lEnableInts(Icr);
; ..\flash\flsloader\FlsLoader_Platform.c	   718  
; ..\flash\flsloader\FlsLoader_Platform.c	   719    /* Erase is completed now program 16 pages of UCB in Page mode. */
; ..\flash\flsloader\FlsLoader_Platform.c	   720  
; ..\flash\flsloader\FlsLoader_Platform.c	   721    /* Enter Critical Section */
; ..\flash\flsloader\FlsLoader_Platform.c	   722    Icr = FlsLoader_lDisableInts();
; ..\flash\flsloader\FlsLoader_Platform.c	   723  
; ..\flash\flsloader\FlsLoader_Platform.c	   724    #if ((FLSLOADER_PF0_PROT == OTP_PROTECTION)||\ 
; ..\flash\flsloader\FlsLoader_Platform.c	   725        (FLSLOADER_PF0_PROT == WOP_PROTECTION))
; ..\flash\flsloader\FlsLoader_Platform.c	   726    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	   727    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   728    if(UcbSectorAddress == FLSLOADER_UCB_OTP)
; ..\flash\flsloader\FlsLoader_Platform.c	   729    {
; ..\flash\flsloader\FlsLoader_Platform.c	   730      for(LoopCount = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	   731          (LoopCount < FLSLOADER_NUM_8);
; ..\flash\flsloader\FlsLoader_Platform.c	   732          LoopCount++)
; ..\flash\flsloader\FlsLoader_Platform.c	   733      {
; ..\flash\flsloader\FlsLoader_Platform.c	   734        if (RetError == FLSLOADER_E_OK)
; ..\flash\flsloader\FlsLoader_Platform.c	   735        {
; ..\flash\flsloader\FlsLoader_Platform.c	   736          RetError = FlsLoader_lLockCmdCyclesWrite(StartAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   737                           UcbSectorAddress,PdataPtr);
; ..\flash\flsloader\FlsLoader_Platform.c	   738          UcbSectorAddress+=FLSLOADER_UCB_DATA_SIZE;
; ..\flash\flsloader\FlsLoader_Platform.c	   739        }
; ..\flash\flsloader\FlsLoader_Platform.c	   740      }
; ..\flash\flsloader\FlsLoader_Platform.c	   741    }
; ..\flash\flsloader\FlsLoader_Platform.c	   742    else 
; ..\flash\flsloader\FlsLoader_Platform.c	   743    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   744    {
; ..\flash\flsloader\FlsLoader_Platform.c	   745      if (RetError == FLSLOADER_E_OK)
; ..\flash\flsloader\FlsLoader_Platform.c	   746      {
; ..\flash\flsloader\FlsLoader_Platform.c	   747        RetError = FlsLoader_lLockCmdCyclesWrite(StartAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   748                         UcbSectorAddress,PdataPtr);
; ..\flash\flsloader\FlsLoader_Platform.c	   749      }
; ..\flash\flsloader\FlsLoader_Platform.c	   750    }
; ..\flash\flsloader\FlsLoader_Platform.c	   751  
; ..\flash\flsloader\FlsLoader_Platform.c	   752    /* Exit Crtical Section */
; ..\flash\flsloader\FlsLoader_Platform.c	   753    FlsLoader_lEnableInts(Icr);
; ..\flash\flsloader\FlsLoader_Platform.c	   754  
; ..\flash\flsloader\FlsLoader_Platform.c	   755    return(RetError);
; ..\flash\flsloader\FlsLoader_Platform.c	   756  }
; ..\flash\flsloader\FlsLoader_Platform.c	   757  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	   758  ** Traceability : [cover parentID=DS_NAS_FLSLOADER_PR3161_3]                  **
; ..\flash\flsloader\FlsLoader_Platform.c	   759  **            [/cover]                                                        **
; ..\flash\flsloader\FlsLoader_Platform.c	   760  ** Syntax   :  IFX_LOCAL_INLINE FlsLoader_ReturnType                          **
; ..\flash\flsloader\FlsLoader_Platform.c	   761  **                                FlsLoader_lLockCmdCyclesWrite(              **
; ..\flash\flsloader\FlsLoader_Platform.c	   762  **                                FlsLoader_AddressType TargetAddress ,       **
; ..\flash\flsloader\FlsLoader_Platform.c	   763  **                                FlsLoader_AddressType UcbSectorAddress,     **
; ..\flash\flsloader\FlsLoader_Platform.c	   764  **                                const FlashLoader_DataType* PdataPtr        **
; ..\flash\flsloader\FlsLoader_Platform.c	   765  **                                )                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	   766  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   767  ** Service ID       :   NA                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   768  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   769  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	   770  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   771  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   772  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   773  ** Parameters (in)  : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   774  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   775  ** Parameters (out) :                                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   776  ** Return value     : FlsLoader_ReturnType                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   777  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   778  ** Description      : This function Locks the required profile and called     **
; ..\flash\flsloader\FlsLoader_Platform.c	   779  **                    from Lock  API.                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   780  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   781  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	   782  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lLockCmdCyclesWrite(
; ..\flash\flsloader\FlsLoader_Platform.c	   783                           FlsLoader_AddressType StartAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   784                           FlsLoader_AddressType UcbSectorAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	   785                           const FlsLoader_AddressType *PdataPtr)
; ..\flash\flsloader\FlsLoader_Platform.c	   786  {
; ..\flash\flsloader\FlsLoader_Platform.c	   787    FlsLoader_ReturnType      RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	   788    uint32                    Count;
; ..\flash\flsloader\FlsLoader_Platform.c	   789    uint32                    InCount;
; ..\flash\flsloader\FlsLoader_Platform.c	   790    volatile uint32           FlashBusyStat;
; ..\flash\flsloader\FlsLoader_Platform.c	   791    volatile uint32         BusyCheck;
; ..\flash\flsloader\FlsLoader_Platform.c	   792    FlsLoader_AddressType    *LoadPageAddress;
; ..\flash\flsloader\FlsLoader_Platform.c	   793    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	   794    
; ..\flash\flsloader\FlsLoader_Platform.c	   795    for (Count = 0U; 
; ..\flash\flsloader\FlsLoader_Platform.c	   796        (Count < FLSLOADER_NUM_UCB_PAGES) && (RetError == FLSLOADER_E_OK);
; ..\flash\flsloader\FlsLoader_Platform.c	   797         Count++)
; ..\flash\flsloader\FlsLoader_Platform.c	   798    {
; ..\flash\flsloader\FlsLoader_Platform.c	   799      /* Enter the Page Mode */
; ..\flash\flsloader\FlsLoader_Platform.c	   800      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	   801      volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   802      /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 for 
; ..\flash\flsloader\FlsLoader_Platform.c	   803      address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   804      /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 for 
; ..\flash\flsloader\FlsLoader_Platform.c	   805      address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   806      *((FlsLoader_AddressType*)FlsLoader_lEnterPageMode(StartAddress)) =
; ..\flash\flsloader\FlsLoader_Platform.c	   807                                               FLSLOADER_DFLASH_VAL;
; ..\flash\flsloader\FlsLoader_Platform.c	   808      InCount = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	   809      #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   810      FlashBusyStat =  TestFlsloader_DebugMask08;
; ..\flash\flsloader\FlsLoader_Platform.c	   811      #else
; ..\flash\flsloader\FlsLoader_Platform.c	   812      FlashBusyStat = FLASH0_FSR.B.DFPAGE;
; ..\flash\flsloader\FlsLoader_Platform.c	   813      #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   814      /* Check if Program Flash is in page mode */
; ..\flash\flsloader\FlsLoader_Platform.c	   815      while((FlashBusyStat != FLSLOADER_BIT_SET) && 
; ..\flash\flsloader\FlsLoader_Platform.c	   816           (InCount < FLSLOADER_CMDCYCLE_TIMEOUT))
; ..\flash\flsloader\FlsLoader_Platform.c	   817      {
; ..\flash\flsloader\FlsLoader_Platform.c	   818        InCount++;
; ..\flash\flsloader\FlsLoader_Platform.c	   819        #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   820        FlashBusyStat =  TestFlsloader_DebugMask08;
; ..\flash\flsloader\FlsLoader_Platform.c	   821        #else
; ..\flash\flsloader\FlsLoader_Platform.c	   822        FlashBusyStat = FLASH0_FSR.B.DFPAGE;
; ..\flash\flsloader\FlsLoader_Platform.c	   823        #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   824      }
; ..\flash\flsloader\FlsLoader_Platform.c	   825      /* Check PFPAGE/DFPAGE bit was SET */
; ..\flash\flsloader\FlsLoader_Platform.c	   826      if(InCount < FLSLOADER_CMDCYCLE_TIMEOUT)
; ..\flash\flsloader\FlsLoader_Platform.c	   827      {
; ..\flash\flsloader\FlsLoader_Platform.c	   828        /* Load the data */
; ..\flash\flsloader\FlsLoader_Platform.c	   829        /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	   830        type volatile uint32 to avoid compiler optimization in command 
; ..\flash\flsloader\FlsLoader_Platform.c	   831        cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   832        /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   833        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   834        /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   835        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   836        LoadPageAddress = 
; ..\flash\flsloader\FlsLoader_Platform.c	   837                (FlsLoader_AddressType *)FlsLoader_lLoadPage(StartAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	   838        *LoadPageAddress = *PdataPtr;
; ..\flash\flsloader\FlsLoader_Platform.c	   839        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used to load 
; ..\flash\flsloader\FlsLoader_Platform.c	   840        data into address and is within allowed range*/
; ..\flash\flsloader\FlsLoader_Platform.c	   841        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used to load 
; ..\flash\flsloader\FlsLoader_Platform.c	   842        data into address and is within allowed range*/
; ..\flash\flsloader\FlsLoader_Platform.c	   843        *(LoadPageAddress + 1U) = *(PdataPtr + 1U);
; ..\flash\flsloader\FlsLoader_Platform.c	   844        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used to load 
; ..\flash\flsloader\FlsLoader_Platform.c	   845        data into address and is within allowed range*/
; ..\flash\flsloader\FlsLoader_Platform.c	   846        PdataPtr += FLSLOADER_NUM_2;
; ..\flash\flsloader\FlsLoader_Platform.c	   847        /* Load Page register gets incremented automatically. */
; ..\flash\flsloader\FlsLoader_Platform.c	   848        /* cycle 1 */
; ..\flash\flsloader\FlsLoader_Platform.c	   849        /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	   850        type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	   851        command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   852        /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   853        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   854        /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   855        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   856        *((FlsLoader_AddressType*)FLSLOADER_GET_CYCLE_AA50(StartAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	   857                                      = UcbSectorAddress;
; ..\flash\flsloader\FlsLoader_Platform.c	   858        /* cycle 2 */
; ..\flash\flsloader\FlsLoader_Platform.c	   859        /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	   860        type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	   861        command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   862        /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   863        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   864        /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   865        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   866        *((FlsLoader_AddressType*)FLSLOADER_GET_CYCLE_AA58(StartAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	   867                                      =  FLSLOADER_VALUE_00;
; ..\flash\flsloader\FlsLoader_Platform.c	   868        /* cycle 3 */
; ..\flash\flsloader\FlsLoader_Platform.c	   869        /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	   870        type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	   871        command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   872        /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   873        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   874        /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   875        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   876          *((FlsLoader_AddressType*)FLSLOADER_GET_CYCLE_AAA8(StartAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	   877                                      =  FLSLOADER_VALUE_A0;
; ..\flash\flsloader\FlsLoader_Platform.c	   878        /*cycle 4 */
; ..\flash\flsloader\FlsLoader_Platform.c	   879        /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	   880        type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	   881        command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   882        /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   883        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   884        /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to uint32 
; ..\flash\flsloader\FlsLoader_Platform.c	   885        for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   886        *((FlsLoader_AddressType*)FLSLOADER_GET_CYCLE_AAA8(StartAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	   887                                      = FLSLOADER_VALUE_AA;
; ..\flash\flsloader\FlsLoader_Platform.c	   888        
; ..\flash\flsloader\FlsLoader_Platform.c	   889        InCount = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	   890        #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   891        FlashBusyStat =  TestFlsloader_DebugMask14;
; ..\flash\flsloader\FlsLoader_Platform.c	   892        #else
; ..\flash\flsloader\FlsLoader_Platform.c	   893        FlashBusyStat = FLASH0_FSR.B.PROG;
; ..\flash\flsloader\FlsLoader_Platform.c	   894        #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   895        while((FlashBusyStat != FLSLOADER_BIT_SET) &&
; ..\flash\flsloader\FlsLoader_Platform.c	   896              (InCount < FLSLOADER_CMDCYCLE_TIMEOUT))
; ..\flash\flsloader\FlsLoader_Platform.c	   897        {
; ..\flash\flsloader\FlsLoader_Platform.c	   898          InCount++;
; ..\flash\flsloader\FlsLoader_Platform.c	   899          #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   900          FlashBusyStat =  TestFlsloader_DebugMask14;
; ..\flash\flsloader\FlsLoader_Platform.c	   901          #else
; ..\flash\flsloader\FlsLoader_Platform.c	   902          FlashBusyStat = FLASH0_FSR.B.PROG;
; ..\flash\flsloader\FlsLoader_Platform.c	   903          #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   904        }
; ..\flash\flsloader\FlsLoader_Platform.c	   905        if (InCount < FLSLOADER_CMDCYCLE_TIMEOUT)
; ..\flash\flsloader\FlsLoader_Platform.c	   906        {
; ..\flash\flsloader\FlsLoader_Platform.c	   907          /* Check for FLASH BUSY flag */
; ..\flash\flsloader\FlsLoader_Platform.c	   908          InCount = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	   909          #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   910          BusyCheck = TestFlsloader_DebugMask15;
; ..\flash\flsloader\FlsLoader_Platform.c	   911          #else
; ..\flash\flsloader\FlsLoader_Platform.c	   912          BusyCheck = (FLASH0_FSR.U & (uint32)FLSLOADER_NUM_2);
; ..\flash\flsloader\FlsLoader_Platform.c	   913          #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   914          while((BusyCheck != 0U)&& 
; ..\flash\flsloader\FlsLoader_Platform.c	   915               (InCount < FLSLOADER_BUSY_TIMEOUT))
; ..\flash\flsloader\FlsLoader_Platform.c	   916          {
; ..\flash\flsloader\FlsLoader_Platform.c	   917            InCount++;
; ..\flash\flsloader\FlsLoader_Platform.c	   918            #if (FLSLOADER_USER_FUNC_CONFIGURED == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	   919            (FLSLOADER_USER_DEFINED_FUNCTION)();
; ..\flash\flsloader\FlsLoader_Platform.c	   920            #endif          
; ..\flash\flsloader\FlsLoader_Platform.c	   921            #ifdef IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   922            BusyCheck = TestFlsloader_DebugMask15;
; ..\flash\flsloader\FlsLoader_Platform.c	   923            #else
; ..\flash\flsloader\FlsLoader_Platform.c	   924            BusyCheck = (FLASH0_FSR.U & (uint32)FLSLOADER_NUM_2);
; ..\flash\flsloader\FlsLoader_Platform.c	   925            #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   926          }
; ..\flash\flsloader\FlsLoader_Platform.c	   927          if (InCount < FLSLOADER_BUSY_TIMEOUT )
; ..\flash\flsloader\FlsLoader_Platform.c	   928          {
; ..\flash\flsloader\FlsLoader_Platform.c	   929          /* This check makes sure No protection error and no sequence error
; ..\flash\flsloader\FlsLoader_Platform.c	   930          and No operation error has occurred.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   931          #ifdef IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   932          RetError = TestFlsloader_DebugMask14;
; ..\flash\flsloader\FlsLoader_Platform.c	   933          #else
; ..\flash\flsloader\FlsLoader_Platform.c	   934          RetError = FlsLoader_lProtSeqOperErrorCheck();
; ..\flash\flsloader\FlsLoader_Platform.c	   935          #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   936          }
; ..\flash\flsloader\FlsLoader_Platform.c	   937          else
; ..\flash\flsloader\FlsLoader_Platform.c	   938          {
; ..\flash\flsloader\FlsLoader_Platform.c	   939            RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	   940          }
; ..\flash\flsloader\FlsLoader_Platform.c	   941        }
; ..\flash\flsloader\FlsLoader_Platform.c	   942        else
; ..\flash\flsloader\FlsLoader_Platform.c	   943        {
; ..\flash\flsloader\FlsLoader_Platform.c	   944          RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	   945        }
; ..\flash\flsloader\FlsLoader_Platform.c	   946        /* Clear the status if the operation is successful.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   947        /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined 
; ..\flash\flsloader\FlsLoader_Platform.c	   948        of type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	   949        command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	   950        /*IFX_MISRA_RULE_11_01_STATUS=The Pointers are type-cast to 
; ..\flash\flsloader\FlsLoader_Platform.c	   951        uint32 for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   952        /*IFX_MISRA_RULE_11_03_STATUS=The Pointers are type-cast to 
; ..\flash\flsloader\FlsLoader_Platform.c	   953        uint32 for address comparison/calculation purpose.*/
; ..\flash\flsloader\FlsLoader_Platform.c	   954        *((FlsLoader_AddressType*)FlsLoader_lClearStatus(StartAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	   955                                      =  FLSLOADER_VALUE_FA;
; ..\flash\flsloader\FlsLoader_Platform.c	   956  
; ..\flash\flsloader\FlsLoader_Platform.c	   957        /* Iterate the TargetAddress to the next page Address */
; ..\flash\flsloader\FlsLoader_Platform.c	   958        UcbSectorAddress = UcbSectorAddress + FLSLOADER_PAGE_SIZE;
; ..\flash\flsloader\FlsLoader_Platform.c	   959      }  /* End of InCount < FLSLOADER_CMDCYCLE_TIMEOUT*/
; ..\flash\flsloader\FlsLoader_Platform.c	   960      else
; ..\flash\flsloader\FlsLoader_Platform.c	   961      {
; ..\flash\flsloader\FlsLoader_Platform.c	   962        RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	   963      }  
; ..\flash\flsloader\FlsLoader_Platform.c	   964    }  /* End of For Loop */
; ..\flash\flsloader\FlsLoader_Platform.c	   965    return(RetError);
; ..\flash\flsloader\FlsLoader_Platform.c	   966  }
; ..\flash\flsloader\FlsLoader_Platform.c	   967  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	   968    /* #if (FLSLOADER_LOCK_UNLOCK_API == STD_ON)*/
; ..\flash\flsloader\FlsLoader_Platform.c	   969  
; ..\flash\flsloader\FlsLoader_Platform.c	   970  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	   971  ** Syntax :           FlsLoader_ReturnType FlsLoader_lCheckOTPWOP(void)       **
; ..\flash\flsloader\FlsLoader_Platform.c	   972  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   973  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   974  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   975  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	   976  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   977  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	   978  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   979  ** Parameters(in)   : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   980  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   981  ** Parameters (out) : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	   982  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   983  ** Return value     : FLSLOADER_E_OK                                          **
; ..\flash\flsloader\FlsLoader_Platform.c	   984  **                    FLSLOADER_E_ROMVERSION                                  **
; ..\flash\flsloader\FlsLoader_Platform.c	   985  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	   986  ** Description      : This function checks whether all the sectors have been  **
; ..\flash\flsloader\FlsLoader_Platform.c	   987  **                    protected under OTP and WOP                             **
; ..\flash\flsloader\FlsLoader_Platform.c	   988  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	   989  FlsLoader_ReturnType FlsLoader_lCheckOTPWOP(void)
; Function FlsLoader_lCheckOTPWOP
.L9:
FlsLoader_lCheckOTPWOP:	.type	func

; ..\flash\flsloader\FlsLoader_Platform.c	   990  {
; ..\flash\flsloader\FlsLoader_Platform.c	   991    FlsLoader_ReturnType  RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	   992    uint32                Fls0Check;
; ..\flash\flsloader\FlsLoader_Platform.c	   993    uint32                Fls0WOPCheck;
; ..\flash\flsloader\FlsLoader_Platform.c	   994    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	   995  
; ..\flash\flsloader\FlsLoader_Platform.c	   996    #ifdef IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	   997    Fls0Check = TestFlsloader_DebugMask00;
; ..\flash\flsloader\FlsLoader_Platform.c	   998    Fls0WOPCheck = TestFlsloader_DebugMask00;
; ..\flash\flsloader\FlsLoader_Platform.c	   999    #else
; ..\flash\flsloader\FlsLoader_Platform.c	  1000    Fls0Check = (uint32)(FLASH0_PROCONOTP0.U & FLSLOADER_PROCON_CHECK);
	movh.a	a15,#63488
	ld.w	d15,[a15]@los(0xf8002038)
.L46:
	mov	d2,#0
.L35:

; ..\flash\flsloader\FlsLoader_Platform.c	  1001    Fls0WOPCheck = (uint32)(FLASH0_PROCONWOP0.U & FLSLOADER_PROCON_CHECK);
	insert	d0,d15,#0,#27,#5
	ld.w	d15,[a15]@los(0xf8002048)
.L36:
	insert	d1,d15,#0,#27,#5
.L37:

; ..\flash\flsloader\FlsLoader_Platform.c	  1002    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  1003  
; ..\flash\flsloader\FlsLoader_Platform.c	  1004    if ((Fls0Check == FLSLOADER_PROCON_CHECK) ||
	mov.u	d15,#65535
	addih	d15,d15,#2047
.L47:
	jeq	d15,d0,.L2
.L48:

; ..\flash\flsloader\FlsLoader_Platform.c	  1005        (Fls0WOPCheck == FLSLOADER_PROCON_CHECK))
	jne	d15,d1,.L3
.L2:

; ..\flash\flsloader\FlsLoader_Platform.c	  1006    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1007      RetError = FLSLOADER_E_ROMVERSION;
	mov	d2,#3
.L3:

; ..\flash\flsloader\FlsLoader_Platform.c	  1008    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1009    return (RetError);
; ..\flash\flsloader\FlsLoader_Platform.c	  1010  }
	ret
.L27:
	
__FlsLoader_lCheckOTPWOP_function_end:
	.size	FlsLoader_lCheckOTPWOP,__FlsLoader_lCheckOTPWOP_function_end-FlsLoader_lCheckOTPWOP
.L20:
	; End of function
	
	.sdecl	'.text.FLSLOADERRAMCODE',code,cluster('FlsLoader_lGetFlashType')
	.sect	'.text.FLSLOADERRAMCODE'
	.align	2
	
	.global	FlsLoader_lGetFlashType

; ..\flash\flsloader\FlsLoader_Platform.c	  1011  
; ..\flash\flsloader\FlsLoader_Platform.c	  1012  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1013  ** Syntax :           IFX_LOCAL_INLINE uint32 FlsLoader_lSectorNumber(        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1014  **                      uint32 Offset                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1015  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1016  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1017  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1018  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1019  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1020  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1021  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1022  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1023  ** Parameters(in)   : offset: PFLASH0 Offset address                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1024  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1025  ** Parameters (out) : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1026  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1027  ** Return value     : SectorNumber                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1028  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1029  ** Description      : This function will return the sector number             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1030  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1031  IFX_LOCAL_INLINE uint32 FlsLoader_lSectorNumber(uint32 Offset)
; ..\flash\flsloader\FlsLoader_Platform.c	  1032  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1033    uint32 SectorNumber;
; ..\flash\flsloader\FlsLoader_Platform.c	  1034    for(SectorNumber = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1035        SectorNumber < FLSLOADER_NUM_OF_PF0_SECTORS;
; ..\flash\flsloader\FlsLoader_Platform.c	  1036        SectorNumber++)
; ..\flash\flsloader\FlsLoader_Platform.c	  1037    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1038      if(FlsLoader_PFlashSectorOffset[SectorNumber] == Offset)
; ..\flash\flsloader\FlsLoader_Platform.c	  1039      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1040        break;
; ..\flash\flsloader\FlsLoader_Platform.c	  1041      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1042    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1043   if(SectorNumber == FLSLOADER_NUM_OF_PF0_SECTORS)
; ..\flash\flsloader\FlsLoader_Platform.c	  1044    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1045      SectorNumber = FLSLOADER_INV;
; ..\flash\flsloader\FlsLoader_Platform.c	  1046    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1047    return (SectorNumber);
; ..\flash\flsloader\FlsLoader_Platform.c	  1048  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1049  
; ..\flash\flsloader\FlsLoader_Platform.c	  1050  #define FLSLOADER_STOP_SEC_CODE
; ..\flash\flsloader\FlsLoader_Platform.c	  1051    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	  1052    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1053  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	  1054  
; ..\flash\flsloader\FlsLoader_Platform.c	  1055  #define FLSLOADER_START_SEC_WRITE_CODE
; ..\flash\flsloader\FlsLoader_Platform.c	  1056    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\flash\flsloader\FlsLoader_Platform.c	  1057    allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1058  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	  1059  
; ..\flash\flsloader\FlsLoader_Platform.c	  1060  #if (FLSLOADER_DEV_ERROR_DETECT == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	  1061  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1062  ** Syntax             FlsLoader_ReturnType FlsLoader_lWriteDetCheck(          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1063  **                      FlsLoader_LengthType Length,                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1064  **                      FlsLoader_AddressType TargetAddress                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1065  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1066  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1067  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1068  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1069  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1070  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1071  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1072  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1073  ** Parameters(in)   : Length :Length of the data                              **
; ..\flash\flsloader\FlsLoader_Platform.c	  1074  **                    TargetAddress :Destination Address in Flash             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1075  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1076  ** Parameters (out) : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1077  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1078  ** Return value     : RetError                                                **
; ..\flash\flsloader\FlsLoader_Platform.c	  1079  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1080  ** Description      : This function is DET Check function for                 **
; ..\flash\flsloader\FlsLoader_Platform.c	  1081  **                    FlsLoader_lWrite                                        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1082  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1083  FlsLoader_ReturnType FlsLoader_lWriteDetCheck(FlsLoader_LengthType Length,
; ..\flash\flsloader\FlsLoader_Platform.c	  1084                                             FlsLoader_AddressType TargetAddress)
; ..\flash\flsloader\FlsLoader_Platform.c	  1085  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1086    uint8                     ErrorHook;
; ..\flash\flsloader\FlsLoader_Platform.c	  1087    FlsLoader_ReturnType      RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1088    FlsLoader_LengthType      TargetEnd;
; ..\flash\flsloader\FlsLoader_Platform.c	  1089    /* Init RetError to OK */
; ..\flash\flsloader\FlsLoader_Platform.c	  1090    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1091  
; ..\flash\flsloader\FlsLoader_Platform.c	  1092    /* check for Init DET */
; ..\flash\flsloader\FlsLoader_Platform.c	  1093    if (FlsLoader_InitStatus != FLSLOADER_INITIALIZED)
; ..\flash\flsloader\FlsLoader_Platform.c	  1094    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1095      /* CD_FlsLoader034 :Report missing initialization DET */
; ..\flash\flsloader\FlsLoader_Platform.c	  1096      Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1097                      FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1098                      FLSLOADER_SID_WRITE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1099                      FLSLOADER_E_UNINIT);
; ..\flash\flsloader\FlsLoader_Platform.c	  1100      RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1101    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1102    /* Check of TargetAddress
; ..\flash\flsloader\FlsLoader_Platform.c	  1103    TargetAddress can be multiple of 256 bytes in caseof PFLASH or 32 bytes.
; ..\flash\flsloader\FlsLoader_Platform.c	  1104    incase of DFLASH. */
; ..\flash\flsloader\FlsLoader_Platform.c	  1105    /* Report Invalid TargetAddress to DET */
; ..\flash\flsloader\FlsLoader_Platform.c	  1106    if ( RetError != FLSLOADER_E_NOT_OK)
; ..\flash\flsloader\FlsLoader_Platform.c	  1107    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1108      ErrorHook = FlsLoader_lAddressPageCheck(TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	  1109      if  (0U == ErrorHook)
; ..\flash\flsloader\FlsLoader_Platform.c	  1110      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1111        Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1112                        FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1113                        FLSLOADER_SID_WRITE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1114                        FLSLOADER_E_PARAM_ADDRESS);
; ..\flash\flsloader\FlsLoader_Platform.c	  1115        RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1116      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1117    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1118  
; ..\flash\flsloader\FlsLoader_Platform.c	  1119    if ( RetError != FLSLOADER_E_NOT_OK)
; ..\flash\flsloader\FlsLoader_Platform.c	  1120    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1121      /* Returns true if, position is DFLASH address */
; ..\flash\flsloader\FlsLoader_Platform.c	  1122      if (FlsLoader_lCheckFlashType(TargetAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	  1123      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1124        /* Given TargetAddress and Length should not exceed the flash boundary */
; ..\flash\flsloader\FlsLoader_Platform.c	  1125        /* Its Data Flash assign,Report invalid Length to DET */
; ..\flash\flsloader\FlsLoader_Platform.c	  1126        if (FlsLoader_lDFlashPageCheck(TargetAddress,Length))
; ..\flash\flsloader\FlsLoader_Platform.c	  1127        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1128          Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1129                          FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1130                          FLSLOADER_SID_WRITE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1131                          FLSLOADER_E_PARAM_LENGTH);
; ..\flash\flsloader\FlsLoader_Platform.c	  1132          RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1133        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1134      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1135      else /*PFlash*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1136      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1137        TargetEnd = (Length + TargetAddress)-1U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1138        /*PFLASH0*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1139        if ((Length == (FlsLoader_LengthType)0U) ||
; ..\flash\flsloader\FlsLoader_Platform.c	  1140           ((Length & ((FlsLoader_LengthType)FLSLOADER_MOD_32)) != 0U))
; ..\flash\flsloader\FlsLoader_Platform.c	  1141        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1142          Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1143                          FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1144                          FLSLOADER_SID_WRITE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1145                          FLSLOADER_E_PARAM_LENGTH);
; ..\flash\flsloader\FlsLoader_Platform.c	  1146          RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1147        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1148        else if(TargetEnd > (FlsLoader_LengthType)FLSLOADER_PFLASH0_END)
; ..\flash\flsloader\FlsLoader_Platform.c	  1149        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1150          Det_ReportError(
; ..\flash\flsloader\FlsLoader_Platform.c	  1151                          FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1152                          FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1153                          FLSLOADER_SID_WRITE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1154                          FLSLOADER_E_PARAM_LENGTH);
; ..\flash\flsloader\FlsLoader_Platform.c	  1155          RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1156        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1157        else
; ..\flash\flsloader\FlsLoader_Platform.c	  1158        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1159          /* dummy else */
; ..\flash\flsloader\FlsLoader_Platform.c	  1160        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1161      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1162    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1163    return (RetError);
; ..\flash\flsloader\FlsLoader_Platform.c	  1164  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1165  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1166  ** Syntax           :  FlsLoader_ReturnType FlsLoader_lEraseDetCheck(         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1167  **                      FlsLoader_LengthType Length,                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1168  **                      FlsLoader_AddressType TargetAddress                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1169  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1170  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1171  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1172  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1173  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1174  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1175  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1176  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1177  ** Parameters(in)   : Length :Length of the data                              **
; ..\flash\flsloader\FlsLoader_Platform.c	  1178  **                    TargetAddress :Destination Address in Flash             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1179  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1180  ** Parameters (out) : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1181  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1182  ** Return value     : RetError                                                **
; ..\flash\flsloader\FlsLoader_Platform.c	  1183  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1184  ** Description      : This function is DET Check  for FlsLoader_Erase         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1185  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1186  FlsLoader_ReturnType FlsLoader_lEraseDetCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  1187                                        FlsLoader_LengthType Length,
; ..\flash\flsloader\FlsLoader_Platform.c	  1188                                        FlsLoader_AddressType TargetAddress)
; ..\flash\flsloader\FlsLoader_Platform.c	  1189  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1190    FlsLoader_ReturnType  RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1191    uint32                Offset;
; ..\flash\flsloader\FlsLoader_Platform.c	  1192    uint32                SectorNumber;
; ..\flash\flsloader\FlsLoader_Platform.c	  1193    uint8                 ErrorHook;
; ..\flash\flsloader\FlsLoader_Platform.c	  1194  
; ..\flash\flsloader\FlsLoader_Platform.c	  1195    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1196    
; ..\flash\flsloader\FlsLoader_Platform.c	  1197    if (FlsLoader_InitStatus != FLSLOADER_INITIALIZED)
; ..\flash\flsloader\FlsLoader_Platform.c	  1198    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1199      /* CD_FlsLoader039: Report missing initilization to DET */
; ..\flash\flsloader\FlsLoader_Platform.c	  1200      Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1201                      FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1202                      FLSLOADER_SID_ERASE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1203                      FLSLOADER_E_UNINIT);
; ..\flash\flsloader\FlsLoader_Platform.c	  1204      RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1205    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1206    else
; ..\flash\flsloader\FlsLoader_Platform.c	  1207    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1208      /* CD_FlsLoader041: Report invalid TargetAddress to DET */
; ..\flash\flsloader\FlsLoader_Platform.c	  1209      if (FlsLoader_lCheckFlashType(TargetAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	  1210      { /* Check for DFLASH Address*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1211        if (FlsLoader_lDFlashCheck(TargetAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	  1212        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1213          ErrorHook = FlsLoader_lDFlashAddressCheck(TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	  1214          if  (0U == ErrorHook)
; ..\flash\flsloader\FlsLoader_Platform.c	  1215          {
; ..\flash\flsloader\FlsLoader_Platform.c	  1216            Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1217                            FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1218                            FLSLOADER_SID_ERASE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1219                            FLSLOADER_E_PARAM_ADDRESS);
; ..\flash\flsloader\FlsLoader_Platform.c	  1220            RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1221          }
; ..\flash\flsloader\FlsLoader_Platform.c	  1222        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1223        if (RetError != FLSLOADER_E_NOT_OK)
; ..\flash\flsloader\FlsLoader_Platform.c	  1224        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1225          ErrorHook = FlsLoader_lDFlashLengthCheck(Length,TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	  1226          if(0U == ErrorHook)
; ..\flash\flsloader\FlsLoader_Platform.c	  1227          {
; ..\flash\flsloader\FlsLoader_Platform.c	  1228            Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1229                            FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1230                            FLSLOADER_SID_ERASE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1231                            FLSLOADER_E_PARAM_LENGTH);
; ..\flash\flsloader\FlsLoader_Platform.c	  1232            RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1233          }
; ..\flash\flsloader\FlsLoader_Platform.c	  1234        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1235      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1236      else
; ..\flash\flsloader\FlsLoader_Platform.c	  1237      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1238        /* Check for PFLASH Address.*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1239          /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1240          volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1241        if(TargetAddress < FLSLOADER_PFLASH0_START_ADDRESS)
; ..\flash\flsloader\FlsLoader_Platform.c	  1242        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1243          Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1244                          FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1245                          FLSLOADER_SID_ERASE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1246                          FLSLOADER_E_PARAM_ADDRESS);
; ..\flash\flsloader\FlsLoader_Platform.c	  1247          RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1248        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1249        /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1250          volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1251        else if(TargetAddress > FLSLOADER_PFLASH0_END)
; ..\flash\flsloader\FlsLoader_Platform.c	  1252        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1253          Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1254                        FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1255                        FLSLOADER_SID_ERASE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1256                        FLSLOADER_E_PARAM_ADDRESS);
; ..\flash\flsloader\FlsLoader_Platform.c	  1257          RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1258        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1259        else
; ..\flash\flsloader\FlsLoader_Platform.c	  1260        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1261          ErrorHook = FlsLoader_lPFlashAddressCheck(TargetAddress);
; ..\flash\flsloader\FlsLoader_Platform.c	  1262          if  (0U == ErrorHook)
; ..\flash\flsloader\FlsLoader_Platform.c	  1263          {
; ..\flash\flsloader\FlsLoader_Platform.c	  1264            Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1265                            FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1266                            FLSLOADER_SID_ERASE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1267                            FLSLOADER_E_PARAM_ADDRESS);
; ..\flash\flsloader\FlsLoader_Platform.c	  1268            RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1269          }
; ..\flash\flsloader\FlsLoader_Platform.c	  1270        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1271        if (RetError != FLSLOADER_E_NOT_OK)
; ..\flash\flsloader\FlsLoader_Platform.c	  1272        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1273          /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1274          volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1275          Offset = TargetAddress - FLSLOADER_PFLASH0_START_ADDRESS;
; ..\flash\flsloader\FlsLoader_Platform.c	  1276          SectorNumber = FlsLoader_lSectorNumber(Offset);
; ..\flash\flsloader\FlsLoader_Platform.c	  1277          if ((Length == 0U)||
; ..\flash\flsloader\FlsLoader_Platform.c	  1278             ((SectorNumber + Length) > FLSLOADER_NUM_OF_PF0_SECTORS))
; ..\flash\flsloader\FlsLoader_Platform.c	  1279          {
; ..\flash\flsloader\FlsLoader_Platform.c	  1280            Det_ReportError(FLSLOADER_MODULE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1281                            FLSLOADER_INSTANCE_ID,
; ..\flash\flsloader\FlsLoader_Platform.c	  1282                            FLSLOADER_SID_ERASE,
; ..\flash\flsloader\FlsLoader_Platform.c	  1283                            FLSLOADER_E_PARAM_LENGTH);
; ..\flash\flsloader\FlsLoader_Platform.c	  1284            RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1285          }
; ..\flash\flsloader\FlsLoader_Platform.c	  1286        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1287      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1288    } /* end of if check FlashType */
; ..\flash\flsloader\FlsLoader_Platform.c	  1289    return (RetError);
; ..\flash\flsloader\FlsLoader_Platform.c	  1290  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1291  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1292  ** Syntax   :   IFX_LOCAL_INLINE uint8 FlsLoader_lAddressPageCheck(           **
; ..\flash\flsloader\FlsLoader_Platform.c	  1293  **                      FlsLoader_AddressType TargetAddress                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1294  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1295  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1296  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1297  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1298  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1299  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1300  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1301  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1302  ** Parameters(in)   : TargetAddress : Address passed                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1303  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1304  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1305  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1306  ** Return value     : RetVal                                                  **
; ..\flash\flsloader\FlsLoader_Platform.c	  1307  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1308  ** Description      : This function returns the give TargetAddress last two   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1309  **                    nibbles are zero value                                  **
; ..\flash\flsloader\FlsLoader_Platform.c	  1310  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1311  IFX_LOCAL_INLINE uint8 FlsLoader_lAddressPageCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  1312                                             FlsLoader_AddressType TargetAddress)
; ..\flash\flsloader\FlsLoader_Platform.c	  1313  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1314    uint8     RetVal;
; ..\flash\flsloader\FlsLoader_Platform.c	  1315    /* Init ErrorHook to false */
; ..\flash\flsloader\FlsLoader_Platform.c	  1316    RetVal = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1317    /* Check of TargetAddress
; ..\flash\flsloader\FlsLoader_Platform.c	  1318    TargetAddress can be multiple of 256 bytes or 32 bytes. */
; ..\flash\flsloader\FlsLoader_Platform.c	  1319    /* CD_FlsLoader036 :Report Invalid TargetAddress to DET */
; ..\flash\flsloader\FlsLoader_Platform.c	  1320    if (FlsLoader_lPFlashCheck(TargetAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	  1321    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1322      /* First byte is already checked, last two nibbles must be zero.*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1323      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1324      volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1325      /*IFX_MISRA_RULE_01_02_STATUS=TargetAddress is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1326      volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1327      if ((TargetAddress & (FlsLoader_AddressType)FLSLOADER_MOD_32) == 
; ..\flash\flsloader\FlsLoader_Platform.c	  1328                                                            FLSLOADER_POSITION_0 )
; ..\flash\flsloader\FlsLoader_Platform.c	  1329      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1330        RetVal = 1U; /* valid sector Address */
; ..\flash\flsloader\FlsLoader_Platform.c	  1331      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1332    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1333    else
; ..\flash\flsloader\FlsLoader_Platform.c	  1334    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1335      if (FlsLoader_lDFlashCheck(TargetAddress))
; ..\flash\flsloader\FlsLoader_Platform.c	  1336      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1337        /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1338        volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1339        /*IFX_MISRA_RULE_01_02_STATUS=TargetAddress is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1340        volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1341        if ( (TargetAddress & (FlsLoader_AddressType)FLSLOADER_MOD_8) == 
; ..\flash\flsloader\FlsLoader_Platform.c	  1342                                                            FLSLOADER_POSITION_0 )
; ..\flash\flsloader\FlsLoader_Platform.c	  1343        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1344          RetVal = 1U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1345        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1346      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1347    } /* end of if TargetAddress check */
; ..\flash\flsloader\FlsLoader_Platform.c	  1348    return (RetVal);
; ..\flash\flsloader\FlsLoader_Platform.c	  1349  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1350  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1351  ** Syntax           :  IFX_LOCAL_INLINE boolean FlsLoader_lDFlashCheck(       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1352  **                                       FlsLoader_AddressType TargetAddress) **
; ..\flash\flsloader\FlsLoader_Platform.c	  1353  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1354  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1355  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1356  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1357  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1358  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1359  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1360  ** Parameters(in)   : TargetAddress : Address passed                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1361  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1362  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1363  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1364  ** Return value     : RetVal                                                  **
; ..\flash\flsloader\FlsLoader_Platform.c	  1365  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1366  ** Description      : Checks that TargetAddress is within DFLASH Address Range**
; ..\flash\flsloader\FlsLoader_Platform.c	  1367  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1368  IFX_LOCAL_INLINE boolean FlsLoader_lDFlashCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  1369                           FlsLoader_AddressType TargetAddress)
; ..\flash\flsloader\FlsLoader_Platform.c	  1370  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1371    boolean RetVal;
; ..\flash\flsloader\FlsLoader_Platform.c	  1372    RetVal  = FALSE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1373  
; ..\flash\flsloader\FlsLoader_Platform.c	  1374    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1375    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1376    if(TargetAddress >= FLSLOADER_DFLASH0_START_ADDRESS)
; ..\flash\flsloader\FlsLoader_Platform.c	  1377    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1378      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1379      volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1380      if(TargetAddress <= FLSLOADER_DFLASH0_END)
; ..\flash\flsloader\FlsLoader_Platform.c	  1381      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1382        RetVal = TRUE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1383      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1384    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1385    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1386    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1387    if(TargetAddress >= FLSLOADER_UCB_START_ADDRESS)
; ..\flash\flsloader\FlsLoader_Platform.c	  1388    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1389      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1390      volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1391      if(TargetAddress <= FLSLOADER_UCB_END_ADDRESS)
; ..\flash\flsloader\FlsLoader_Platform.c	  1392      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1393        RetVal = TRUE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1394      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1395    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1396    return (RetVal);
; ..\flash\flsloader\FlsLoader_Platform.c	  1397  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1398  
; ..\flash\flsloader\FlsLoader_Platform.c	  1399  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1400  ** Syntax   :   IFX_LOCAL_INLINE uint8 FlsLoader_lDFlashAddressCheck(         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1401  **                      FlsLoader_AddressType TargetAddress                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1402  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1403  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1404  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1405  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1406  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1407  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1408  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1409  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1410  ** Parameters(in)   : TargetAddress : Address passed                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1411  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1412  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1413  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1414  ** Return value     : RetVal                                                  **
; ..\flash\flsloader\FlsLoader_Platform.c	  1415  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1416  ** Description      : This function returns if the given TargetAddress        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1417  **                    matches the start of DF_EEPROM or DF_UCB sector address **
; ..\flash\flsloader\FlsLoader_Platform.c	  1418  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1419  IFX_LOCAL_INLINE uint8 FlsLoader_lDFlashAddressCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  1420                                             FlsLoader_AddressType TargetAddress)
; ..\flash\flsloader\FlsLoader_Platform.c	  1421  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1422    uint16               DfSegment;
; ..\flash\flsloader\FlsLoader_Platform.c	  1423    uint8                RetVal;
; ..\flash\flsloader\FlsLoader_Platform.c	  1424    RetVal = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1425    DfSegment = (uint16)(TargetAddress >> FLSLOADER_SHIFT_BY_20);
; ..\flash\flsloader\FlsLoader_Platform.c	  1426    if (DfSegment == FLSLOADER_DF_EEPROM_SEGMENT)
; ..\flash\flsloader\FlsLoader_Platform.c	  1427    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1428      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1429      type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	  1430      command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1431      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1432      type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	  1433      command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1434      if ((TargetAddress & (FlsLoader_AddressType)FLSLOADER_EEPROM_SECTOR_SIZE) 
; ..\flash\flsloader\FlsLoader_Platform.c	  1435          == FLSLOADER_POSITION_0)
; ..\flash\flsloader\FlsLoader_Platform.c	  1436      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1437        RetVal = 1U; /*Valid DF_EEPROM Start Sector Address */
; ..\flash\flsloader\FlsLoader_Platform.c	  1438      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1439    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1440    if (DfSegment == FLSLOADER_DF_UCB_SEGMENT)
; ..\flash\flsloader\FlsLoader_Platform.c	  1441    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1442      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1443      type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	  1444      command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1445      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1446      type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	  1447      command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1448      if ((TargetAddress & (FlsLoader_AddressType)FLSLOADER_UCB_SECTOR_SIZE_1) 
; ..\flash\flsloader\FlsLoader_Platform.c	  1449          == FLSLOADER_POSITION_0)
; ..\flash\flsloader\FlsLoader_Platform.c	  1450      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1451        RetVal = 1U; /*Valid DF_UCB Start Sector Address */
; ..\flash\flsloader\FlsLoader_Platform.c	  1452      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1453    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1454    return (RetVal);
; ..\flash\flsloader\FlsLoader_Platform.c	  1455  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1456  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1457  ** Syntax   :   IFX_LOCAL_INLINE uint8 FlsLoader_lDFlashLengthCheck(          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1458  **                      FlsLoader_LengthType Length,                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1459  **                      FlsLoader_AddressType TargetAddress                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1460  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1461  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1462  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1463  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1464  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1465  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1466  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1467  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1468  ** Parameters(in)   : TargetAddress : Address passed                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1469  **                    Length        : Length of the data                      **
; ..\flash\flsloader\FlsLoader_Platform.c	  1470  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1471  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1472  ** Return value     : RetVal                                                  **
; ..\flash\flsloader\FlsLoader_Platform.c	  1473  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1474  ** Description      : This function returns if the given Length               **
; ..\flash\flsloader\FlsLoader_Platform.c	  1475  **                    is within DF_EEPROM or DF_UCB range                     **
; ..\flash\flsloader\FlsLoader_Platform.c	  1476  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1477  IFX_LOCAL_INLINE uint8 FlsLoader_lDFlashLengthCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  1478                 FlsLoader_LengthType Length,FlsLoader_AddressType TargetAddress)
; ..\flash\flsloader\FlsLoader_Platform.c	  1479  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1480    uint32               DFLengthCheck;
; ..\flash\flsloader\FlsLoader_Platform.c	  1481    uint16               DfSegment;
; ..\flash\flsloader\FlsLoader_Platform.c	  1482    uint8                RetVal;
; ..\flash\flsloader\FlsLoader_Platform.c	  1483    
; ..\flash\flsloader\FlsLoader_Platform.c	  1484    RetVal = 1U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1485    DfSegment = (uint16)(TargetAddress >> FLSLOADER_SHIFT_BY_20);
; ..\flash\flsloader\FlsLoader_Platform.c	  1486    if (Length == 0U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1487    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1488      RetVal = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1489    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1490    if (DfSegment == FLSLOADER_DF_EEPROM_SEGMENT)
; ..\flash\flsloader\FlsLoader_Platform.c	  1491    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1492      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1493      type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	  1494      command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1495      DFLengthCheck = ((uint32)(TargetAddress + (FlsLoader_AddressType)
; ..\flash\flsloader\FlsLoader_Platform.c	  1496                              (Length * FLSLOADER_DFLASH_SECTOR_SIZE)))-1U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1497      if (( DFLengthCheck > FLSLOADER_DFLASH0_END ) || 
; ..\flash\flsloader\FlsLoader_Platform.c	  1498          ( Length > FLSLOADER_NUM_OF_DF_SECTORS ))
; ..\flash\flsloader\FlsLoader_Platform.c	  1499      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1500        RetVal = 0U; /*InValid DF_EEPROM Sector Length */
; ..\flash\flsloader\FlsLoader_Platform.c	  1501      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1502    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1503    if (DfSegment == FLSLOADER_DF_UCB_SEGMENT)
; ..\flash\flsloader\FlsLoader_Platform.c	  1504    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1505      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1506      type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	  1507      command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1508      DFLengthCheck = ((uint32)(TargetAddress + (FlsLoader_AddressType)
; ..\flash\flsloader\FlsLoader_Platform.c	  1509                              (Length * FLSLOADER_UCB_SECTOR_SIZE)))-1U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1510      if (( DFLengthCheck > FLSLOADER_UCB_END_ADDRESS ) ||
; ..\flash\flsloader\FlsLoader_Platform.c	  1511          ( Length > FLSLOADER_NUM_OF_UCB_SECTORS ))
; ..\flash\flsloader\FlsLoader_Platform.c	  1512      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1513        RetVal = 0U; /*InValid DF_EEPROM Sector Length */
; ..\flash\flsloader\FlsLoader_Platform.c	  1514      }    
; ..\flash\flsloader\FlsLoader_Platform.c	  1515    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1516    return (RetVal);
; ..\flash\flsloader\FlsLoader_Platform.c	  1517  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1518  
; ..\flash\flsloader\FlsLoader_Platform.c	  1519  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1520  ** Syntax           :  IFX_LOCAL_INLINE boolean FlsLoader_lPFlashCheck(       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1521  **                                       FlsLoader_AddressType TargetAddress) **
; ..\flash\flsloader\FlsLoader_Platform.c	  1522  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1523  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1524  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1525  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1526  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1527  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1528  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1529  ** Parameters(in)   : TargetAddress : Address passed                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1530  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1531  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1532  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1533  ** Return value     : uint8: RetVal                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	  1534  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1535  ** Description      :Checks that TargetAddress is within PFLASH Address Range **
; ..\flash\flsloader\FlsLoader_Platform.c	  1536  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1537  IFX_LOCAL_INLINE boolean FlsLoader_lPFlashCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  1538                           FlsLoader_AddressType TargetAddress)
; ..\flash\flsloader\FlsLoader_Platform.c	  1539  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1540    boolean RetVal;
; ..\flash\flsloader\FlsLoader_Platform.c	  1541    RetVal = FALSE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1542    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1543    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1544    if(TargetAddress >= FLSLOADER_PFLASH0_START_ADDRESS)
; ..\flash\flsloader\FlsLoader_Platform.c	  1545    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1546      /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1547      volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1548      if(TargetAddress <= FLSLOADER_PFLASH0_END)
; ..\flash\flsloader\FlsLoader_Platform.c	  1549      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1550        RetVal = TRUE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1551      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1552    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1553    return (RetVal);
; ..\flash\flsloader\FlsLoader_Platform.c	  1554  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1555  
; ..\flash\flsloader\FlsLoader_Platform.c	  1556  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1557  ** Syntax   :   IFX_LOCAL_INLINE uint8 FlsLoader_lPFlashAddressCheck(         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1558  **                      FlsLoader_AddressType TargetAddress                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1559  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1560  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1561  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1562  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1563  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1564  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1565  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1566  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1567  ** Parameters(in)   : TargetAddress : Address passed                          **
; ..\flash\flsloader\FlsLoader_Platform.c	  1568  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1569  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1570  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1571  ** Return value     :  RetVal                                                 **
; ..\flash\flsloader\FlsLoader_Platform.c	  1572  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1573  ** Description      : This function returns if the given TargetAddress        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1574  **                    matches the start PFlash sector start address           **
; ..\flash\flsloader\FlsLoader_Platform.c	  1575  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1576  IFX_LOCAL_INLINE uint8 FlsLoader_lPFlashAddressCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  1577                                             FlsLoader_AddressType TargetAddress)
; ..\flash\flsloader\FlsLoader_Platform.c	  1578  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1579    uint8                RetVal;
; ..\flash\flsloader\FlsLoader_Platform.c	  1580    uint32               Offset;
; ..\flash\flsloader\FlsLoader_Platform.c	  1581    uint32               SectorNumber;
; ..\flash\flsloader\FlsLoader_Platform.c	  1582    RetVal = 1U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1583  
; ..\flash\flsloader\FlsLoader_Platform.c	  1584    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1585    type volatile uint32 to avoid compiler optimization in 
; ..\flash\flsloader\FlsLoader_Platform.c	  1586    command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1587    Offset = TargetAddress - FLSLOADER_PFLASH0_START_ADDRESS ;
; ..\flash\flsloader\FlsLoader_Platform.c	  1588  
; ..\flash\flsloader\FlsLoader_Platform.c	  1589    for(SectorNumber = 0U; SectorNumber < FLSLOADER_NUM_OF_PF0_SECTORS;
; ..\flash\flsloader\FlsLoader_Platform.c	  1590        SectorNumber++)
; ..\flash\flsloader\FlsLoader_Platform.c	  1591    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1592      if (FlsLoader_PFlashSectorOffset[SectorNumber] == Offset)
; ..\flash\flsloader\FlsLoader_Platform.c	  1593      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1594        break;
; ..\flash\flsloader\FlsLoader_Platform.c	  1595      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1596    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1597    if (SectorNumber == FLSLOADER_NUM_OF_PF0_SECTORS)
; ..\flash\flsloader\FlsLoader_Platform.c	  1598    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1599      RetVal = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1600    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1601    return (RetVal);
; ..\flash\flsloader\FlsLoader_Platform.c	  1602  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1603  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1604  ** Syntax           :  IFX_LOCAL_INLINE boolean FlsLoader_lDFlashPageCheck(   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1605  **                                       FlsLoader_AddressType TargetAddress, **
; ..\flash\flsloader\FlsLoader_Platform.c	  1606                                           FlsLoader_LengthType Length )        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1607  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1608  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1609  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1610  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1611  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1612  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1613  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1614  ** Parameters(in)   : TargetAddress, Length                                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1615  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1616  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1617  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1618  ** Return value     : boolean                                                 **
; ..\flash\flsloader\FlsLoader_Platform.c	  1619  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1620  ** Description      : Checks the length for DFlash operation                  **
; ..\flash\flsloader\FlsLoader_Platform.c	  1621  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1622  IFX_LOCAL_INLINE boolean FlsLoader_lDFlashPageCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  1623                  FlsLoader_AddressType TargetAddress,FlsLoader_LengthType Length)
; ..\flash\flsloader\FlsLoader_Platform.c	  1624  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1625    boolean                   RetVal;
; ..\flash\flsloader\FlsLoader_Platform.c	  1626    uint16                    DfSegment;
; ..\flash\flsloader\FlsLoader_Platform.c	  1627    FlsLoader_LengthType      TargetEnd;
; ..\flash\flsloader\FlsLoader_Platform.c	  1628    RetVal = FALSE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1629     
; ..\flash\flsloader\FlsLoader_Platform.c	  1630    if ((Length) == (FlsLoader_LengthType)0U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1631    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1632      RetVal = TRUE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1633    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1634    if (((Length) & ((FlsLoader_LengthType)FLSLOADER_MOD_8)) != 0U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1635    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1636      RetVal = TRUE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1637    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1638    DfSegment = (uint16)(TargetAddress >> FLSLOADER_SHIFT_BY_20);
; ..\flash\flsloader\FlsLoader_Platform.c	  1639    /*IFX_MISRA_RULE_01_02_STATUS=FlsLoader_AddressType is defined of type 
; ..\flash\flsloader\FlsLoader_Platform.c	  1640    volatile uint32 to avoid compiler optimization in command cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1641    TargetEnd = (Length + (TargetAddress & FLSLOADER_ADDRESS_MASK)) - 1U ;
; ..\flash\flsloader\FlsLoader_Platform.c	  1642    if (DfSegment == FLSLOADER_DF_EEPROM_SEGMENT)
; ..\flash\flsloader\FlsLoader_Platform.c	  1643    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1644      if ((TargetEnd > (FlsLoader_LengthType)FLSLOADER_EEPROM_OFFSETEND) ||
; ..\flash\flsloader\FlsLoader_Platform.c	  1645          (Length > FLSLOADER_DFLASH_BANK_SIZE))
; ..\flash\flsloader\FlsLoader_Platform.c	  1646      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1647        RetVal = TRUE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1648      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1649    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1650    if (DfSegment == FLSLOADER_DF_UCB_SEGMENT)
; ..\flash\flsloader\FlsLoader_Platform.c	  1651    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1652      if ((TargetEnd > (FlsLoader_LengthType)FLSLOADER_UCB_OFFSETEND) ||
; ..\flash\flsloader\FlsLoader_Platform.c	  1653          (Length > FLSLOADER_UCBSIZE))
; ..\flash\flsloader\FlsLoader_Platform.c	  1654      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1655        RetVal = TRUE;
; ..\flash\flsloader\FlsLoader_Platform.c	  1656      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1657    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1658    return (RetVal);
; ..\flash\flsloader\FlsLoader_Platform.c	  1659  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1660  #endif /* #if (FLSLOADER_DEV_ERROR_DETECT == STD_ON) */
; ..\flash\flsloader\FlsLoader_Platform.c	  1661  
; ..\flash\flsloader\FlsLoader_Platform.c	  1662  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1663  ** Syntax           :  uint32 FlsLoader_lGetFlashType(                        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1664  **                                       FlsLoader_AddressType TargetAddress) **
; ..\flash\flsloader\FlsLoader_Platform.c	  1665                                                                                **
; ..\flash\flsloader\FlsLoader_Platform.c	  1666  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1667  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1668  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1669  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1670  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1671  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1672  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1673  ** Parameters(in)   : TargetAddress                                           **
; ..\flash\flsloader\FlsLoader_Platform.c	  1674  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1675  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1676  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1677  ** Return value     : uint32 FlashType                                        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1678  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1679  ** Description      : Checks the whether Flash is DFlash or Pflash            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1680  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1681  uint32 FlsLoader_lGetFlashType(FlsLoader_AddressType TargetAddress)
; Function FlsLoader_lGetFlashType
.L11:
FlsLoader_lGetFlashType:	.type	func

; ..\flash\flsloader\FlsLoader_Platform.c	  1682  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1683    uint32 Flash_Type; 
; ..\flash\flsloader\FlsLoader_Platform.c	  1684    
; ..\flash\flsloader\FlsLoader_Platform.c	  1685    if (FlsLoader_lCheckFlashType(TargetAddress))
	sh	d15,d4,#-24
	eq	d15,d15,#175
.L53:

; ..\flash\flsloader\FlsLoader_Platform.c	  1686    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1687      /* Given Address belongs to DFLASH */
; ..\flash\flsloader\FlsLoader_Platform.c	  1688      /* Data Flash Bank 0 or Bank 1 is assigned */
; ..\flash\flsloader\FlsLoader_Platform.c	  1689      Flash_Type = FLSLOADER_DFLASH_BANK0;
	mov	d0,#2
.L54:

; ..\flash\flsloader\FlsLoader_Platform.c	  1690    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1691    else
; ..\flash\flsloader\FlsLoader_Platform.c	  1692    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1693      /* Given Address belongs to PFLASH */
; ..\flash\flsloader\FlsLoader_Platform.c	  1694      /* Program Flash Bank 0 or Bank 1 is assigned */
; ..\flash\flsloader\FlsLoader_Platform.c	  1695      Flash_Type = FLSLOADER_PFLASH_BANK0;
; ..\flash\flsloader\FlsLoader_Platform.c	  1696    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1697    return Flash_Type;
; ..\flash\flsloader\FlsLoader_Platform.c	  1698  }
	sel	d2,d15,d0,#8
	ret
.L31:
	
__FlsLoader_lGetFlashType_function_end:
	.size	FlsLoader_lGetFlashType,__FlsLoader_lGetFlashType_function_end-FlsLoader_lGetFlashType
.L25:
	; End of function
	
	.calls	'FlsLoader_lCheckOTPWOP','',0
	.calls	'FlsLoader_lGetFlashType','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L13:
	.word	31289
	.half	3
	.word	.L14
	.byte	4
.L12:
	.byte	1
	.byte	'..\\flash\\flsloader\\FlsLoader_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L15
.L26:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L32:
	.byte	3
	.word	194
	.byte	4
	.byte	'void',0,5
	.word	220
	.byte	6
	.byte	'__prof_adm',0,1,1,1
	.word	226
	.byte	7,1,5
	.word	250
	.byte	6
	.byte	'__codeptr',0,1,1,1
	.word	252
	.byte	8
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,2,45,16,4,2
	.byte	'unsigned char',0,1,8,9
	.byte	'EN0',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	303
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	303
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	303
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	303
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	303
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	303
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	303
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	303
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	303
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	303
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	303
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	303
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	303
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	303
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	303
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	303
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	303
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	303
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	303
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	303
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,2,79,3
	.word	275
	.byte	8
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,2,82,16,4,2
	.byte	'unsigned int',0,4,7,9
	.byte	'reserved_0',0,4
	.word	881
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,2,85,3
	.word	853
	.byte	8
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,2,88,16,4,9
	.byte	'SEL',0,1
	.word	303
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	303
	.byte	2,0,2,35,0,9
	.byte	'CLR',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'DIS',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'reserved_10',0,4
	.word	881
	.byte	22,0,2,35,2,0,6
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,2,95,3
	.word	950
	.byte	8
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,2,98,16,4,9
	.byte	'VLD0',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'VLD1',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'VLD2',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'VLD3',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'VLD4',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'VLD5',0,1
	.word	303
	.byte	1,2,2,35,0,9
	.byte	'VLD6',0,1
	.word	303
	.byte	1,1,2,35,0,9
	.byte	'VLD7',0,1
	.word	303
	.byte	1,0,2,35,0,9
	.byte	'VLD8',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'VLD9',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'reserved_10',0,4
	.word	881
	.byte	22,0,2,35,2,0,6
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,2,111,3
	.word	1103
	.byte	8
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,2,114,16,4,9
	.byte	'reserved_0',0,1
	.word	303
	.byte	5,3,2,35,0,9
	.byte	'ADDR',0,4
	.word	881
	.byte	19,8,2,35,2,9
	.byte	'ERR',0,1
	.word	303
	.byte	6,2,2,35,3,9
	.byte	'VLD',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'CLR',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,2,121,3
	.word	1351
	.byte	8
	.byte	'_Ifx_FLASH_COMM0_Bits',0,2,124,16,4,9
	.byte	'STATUS',0,1
	.word	303
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	881
	.byte	24,0,2,35,2,0,6
	.byte	'Ifx_FLASH_COMM0_Bits',0,2,128,1,3
	.word	1497
	.byte	8
	.byte	'_Ifx_FLASH_COMM1_Bits',0,2,131,1,16,4,9
	.byte	'STATUS',0,1
	.word	303
	.byte	8,0,2,35,0,9
	.byte	'DATA',0,1
	.word	303
	.byte	8,0,2,35,1,2
	.byte	'unsigned short int',0,2,7,9
	.byte	'reserved_16',0,2
	.word	1657
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_FLASH_COMM1_Bits',0,2,136,1,3
	.word	1595
	.byte	8
	.byte	'_Ifx_FLASH_COMM2_Bits',0,2,139,1,16,4,9
	.byte	'STATUS',0,1
	.word	303
	.byte	8,0,2,35,0,9
	.byte	'DATA',0,1
	.word	303
	.byte	8,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1657
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_FLASH_COMM2_Bits',0,2,144,1,3
	.word	1733
	.byte	8
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,2,147,1,16,4,9
	.byte	'RCODE',0,4
	.word	881
	.byte	22,10,2,35,2,9
	.byte	'reserved_22',0,2
	.word	1657
	.byte	8,2,2,35,2,9
	.byte	'EDCERRINJ',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'ECCORDIS',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_ECCRD_Bits',0,2,153,1,3
	.word	1849
	.byte	8
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,2,156,1,16,4,9
	.byte	'RCODE',0,4
	.word	881
	.byte	22,10,2,35,2,9
	.byte	'reserved_22',0,2
	.word	1657
	.byte	8,2,2,35,2,9
	.byte	'EDCERRINJ',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'ECCORDIS',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_ECCRP_Bits',0,2,162,1,3
	.word	1989
	.byte	8
	.byte	'_Ifx_FLASH_ECCW_Bits',0,2,165,1,16,4,9
	.byte	'WCODE',0,4
	.word	881
	.byte	22,10,2,35,2,9
	.byte	'reserved_22',0,2
	.word	1657
	.byte	8,2,2,35,2,9
	.byte	'DECENCDIS',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'PECENCDIS',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_ECCW_Bits',0,2,171,1,3
	.word	2129
	.byte	8
	.byte	'_Ifx_FLASH_FCON_Bits',0,2,174,1,16,4,9
	.byte	'WSPFLASH',0,1
	.word	303
	.byte	4,4,2,35,0,9
	.byte	'WSECPF',0,1
	.word	303
	.byte	2,2,2,35,0,9
	.byte	'WSDFLASH',0,2
	.word	1657
	.byte	6,4,2,35,0,9
	.byte	'WSECDF',0,1
	.word	303
	.byte	3,1,2,35,1,9
	.byte	'IDLE',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'ESLDIS',0,1
	.word	303
	.byte	1,7,2,35,2,9
	.byte	'SLEEP',0,1
	.word	303
	.byte	1,6,2,35,2,9
	.byte	'NSAFECC',0,1
	.word	303
	.byte	1,5,2,35,2,9
	.byte	'STALL',0,1
	.word	303
	.byte	1,4,2,35,2,9
	.byte	'RES21',0,1
	.word	303
	.byte	2,2,2,35,2,9
	.byte	'RES23',0,1
	.word	303
	.byte	2,0,2,35,2,9
	.byte	'VOPERM',0,1
	.word	303
	.byte	1,7,2,35,3,9
	.byte	'SQERM',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'PROERM',0,1
	.word	303
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	303
	.byte	3,2,2,35,3,9
	.byte	'PR5V',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'EOBM',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_FCON_Bits',0,2,193,1,3
	.word	2268
	.byte	8
	.byte	'_Ifx_FLASH_FPRO_Bits',0,2,196,1,16,4,9
	.byte	'PROINP',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'PRODISP',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'PROIND',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'PRODISD',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'PROINHSMCOTP',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'RES5',0,1
	.word	303
	.byte	1,2,2,35,0,9
	.byte	'PROINOTP',0,1
	.word	303
	.byte	1,1,2,35,0,9
	.byte	'RES7',0,1
	.word	303
	.byte	1,0,2,35,0,9
	.byte	'PROINDBG',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'PRODISDBG',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'PROINHSM',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'reserved_11',0,1
	.word	303
	.byte	5,0,2,35,1,9
	.byte	'DCFP',0,1
	.word	303
	.byte	1,7,2,35,2,9
	.byte	'DDFP',0,1
	.word	303
	.byte	1,6,2,35,2,9
	.byte	'DDFPX',0,1
	.word	303
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	303
	.byte	1,4,2,35,2,9
	.byte	'DDFD',0,1
	.word	303
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	303
	.byte	1,2,2,35,2,9
	.byte	'ENPE',0,1
	.word	303
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	303
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_FLASH_FPRO_Bits',0,2,218,1,3
	.word	2630
	.byte	8
	.byte	'_Ifx_FLASH_FSR_Bits',0,2,221,1,16,4,9
	.byte	'FABUSY',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'D0BUSY',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'RES1',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'P0BUSY',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'RES4',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'RES5',0,1
	.word	303
	.byte	1,2,2,35,0,9
	.byte	'RES6',0,1
	.word	303
	.byte	1,1,2,35,0,9
	.byte	'PROG',0,1
	.word	303
	.byte	1,0,2,35,0,9
	.byte	'ERASE',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'PFPAGE',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'DFPAGE',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'OPER',0,1
	.word	303
	.byte	1,4,2,35,1,9
	.byte	'SQER',0,1
	.word	303
	.byte	1,3,2,35,1,9
	.byte	'PROER',0,1
	.word	303
	.byte	1,2,2,35,1,9
	.byte	'PFSBER',0,1
	.word	303
	.byte	1,1,2,35,1,9
	.byte	'PFDBER',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'PFMBER',0,1
	.word	303
	.byte	1,7,2,35,2,9
	.byte	'RES17',0,1
	.word	303
	.byte	1,6,2,35,2,9
	.byte	'DFSBER',0,1
	.word	303
	.byte	1,5,2,35,2,9
	.byte	'DFDBER',0,1
	.word	303
	.byte	1,4,2,35,2,9
	.byte	'DFTBER',0,1
	.word	303
	.byte	1,3,2,35,2,9
	.byte	'DFMBER',0,1
	.word	303
	.byte	1,2,2,35,2,9
	.byte	'SRIADDERR',0,1
	.word	303
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	1657
	.byte	2,7,2,35,2,9
	.byte	'PVER',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'EVER',0,1
	.word	303
	.byte	1,5,2,35,3,9
	.byte	'SPND',0,1
	.word	303
	.byte	1,4,2,35,3,9
	.byte	'SLM',0,1
	.word	303
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	303
	.byte	1,2,2,35,3,9
	.byte	'ORIER',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_FSR_Bits',0,2,254,1,3
	.word	3071
	.byte	8
	.byte	'_Ifx_FLASH_HSMFCON_Bits',0,2,129,2,16,4,9
	.byte	'LCKHSMUCB',0,1
	.word	303
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	881
	.byte	22,8,2,35,2,9
	.byte	'VOPERM',0,1
	.word	303
	.byte	1,7,2,35,3,9
	.byte	'SQERM',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	303
	.byte	5,1,2,35,3,9
	.byte	'EOBM',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_HSMFCON_Bits',0,2,137,2,3
	.word	3675
	.byte	8
	.byte	'_Ifx_FLASH_HSMFSR_Bits',0,2,140,2,16,4,9
	.byte	'reserved_0',0,1
	.word	303
	.byte	2,6,2,35,0,9
	.byte	'D1BUSY',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	303
	.byte	4,1,2,35,0,9
	.byte	'PROG',0,1
	.word	303
	.byte	1,0,2,35,0,9
	.byte	'ERASE',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'DFPAGE',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'OPER',0,1
	.word	303
	.byte	1,4,2,35,1,9
	.byte	'SQER',0,1
	.word	303
	.byte	1,3,2,35,1,9
	.byte	'reserved_13',0,4
	.word	881
	.byte	12,7,2,35,2,9
	.byte	'PVER',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'EVER',0,1
	.word	303
	.byte	1,5,2,35,3,9
	.byte	'SPND',0,1
	.word	303
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	303
	.byte	4,0,2,35,3,0,6
	.byte	'Ifx_FLASH_HSMFSR_Bits',0,2,156,2,3
	.word	3855
	.byte	8
	.byte	'_Ifx_FLASH_HSMMARD_Bits',0,2,159,2,16,4,9
	.byte	'reserved_0',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'SELD1',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'SPND',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'SPNDERR',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,4
	.word	881
	.byte	27,0,2,35,2,0,6
	.byte	'Ifx_FLASH_HSMMARD_Bits',0,2,167,2,3
	.word	4177
	.byte	8
	.byte	'_Ifx_FLASH_HSMRRAD_Bits',0,2,170,2,16,4,9
	.byte	'reserved_0',0,1
	.word	303
	.byte	3,5,2,35,0,9
	.byte	'ADD',0,4
	.word	881
	.byte	29,0,2,35,2,0,6
	.byte	'Ifx_FLASH_HSMRRAD_Bits',0,2,174,2,3
	.word	4358
	.byte	8
	.byte	'_Ifx_FLASH_HSMRRCT_Bits',0,2,177,2,16,4,9
	.byte	'STRT',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'STP',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'BUSY',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'DONE',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'ERR',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	303
	.byte	3,0,2,35,0,9
	.byte	'EOBM',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,1
	.word	303
	.byte	7,0,2,35,1,9
	.byte	'CNT',0,2
	.word	1657
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_FLASH_HSMRRCT_Bits',0,2,188,2,3
	.word	4458
	.byte	8
	.byte	'_Ifx_FLASH_HSMRRD0_Bits',0,2,191,2,16,4,9
	.byte	'DATA',0,4
	.word	881
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_FLASH_HSMRRD0_Bits',0,2,194,2,3
	.word	4674
	.byte	8
	.byte	'_Ifx_FLASH_HSMRRD1_Bits',0,2,197,2,16,4,9
	.byte	'DATA',0,4
	.word	881
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_FLASH_HSMRRD1_Bits',0,2,200,2,3
	.word	4753
	.byte	8
	.byte	'_Ifx_FLASH_ID_Bits',0,2,203,2,16,4,9
	.byte	'MODREV',0,1
	.word	303
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	303
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	1657
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_FLASH_ID_Bits',0,2,208,2,3
	.word	4832
	.byte	8
	.byte	'_Ifx_FLASH_MARD_Bits',0,2,211,2,16,4,9
	.byte	'HMARGIN',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'SELD0',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'SPND',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'SPNDERR',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,2
	.word	1657
	.byte	10,1,2,35,0,9
	.byte	'TRAPDIS',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1657
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_FLASH_MARD_Bits',0,2,221,2,3
	.word	4943
	.byte	8
	.byte	'_Ifx_FLASH_MARP_Bits',0,2,224,2,16,4,9
	.byte	'SELP0',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'RES1',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'RES2',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'RES3',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,2
	.word	1657
	.byte	11,1,2,35,0,9
	.byte	'TRAPDIS',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	1657
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_FLASH_MARP_Bits',0,2,233,2,3
	.word	5157
	.byte	8
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,2,236,2,16,4,9
	.byte	'L',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'NSAFECC',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'RAMIN',0,1
	.word	303
	.byte	2,4,2,35,0,9
	.byte	'RAMINSEL',0,1
	.word	303
	.byte	4,0,2,35,0,9
	.byte	'RES8',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'RES9',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'RES10',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'RES11',0,1
	.word	303
	.byte	1,4,2,35,1,9
	.byte	'RES12',0,1
	.word	303
	.byte	1,3,2,35,1,9
	.byte	'RES13',0,1
	.word	303
	.byte	1,2,2,35,1,9
	.byte	'RES14',0,1
	.word	303
	.byte	1,1,2,35,1,9
	.byte	'RES15',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'ESR0CNT',0,2
	.word	1657
	.byte	12,4,2,35,2,9
	.byte	'RES29',0,1
	.word	303
	.byte	2,2,2,35,3,9
	.byte	'RES30',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'RPRO',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_PROCOND_Bits',0,2,254,2,3
	.word	5343
	.byte	8
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,2,129,3,16,4,9
	.byte	'OCDSDIS',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'DBGIFLCK',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'EDM',0,1
	.word	303
	.byte	2,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	881
	.byte	28,0,2,35,2,0,6
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,2,135,3,3
	.word	5678
	.byte	8
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,2,138,3,16,4,9
	.byte	'HSMDBGDIS',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'DBGIFLCK',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'TSTIFLCK',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'HSMTSTDIS',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'RES15',0,2
	.word	1657
	.byte	12,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	1657
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,2,146,3,3
	.word	5821
	.byte	8
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,2,149,3,16,4,9
	.byte	'HSMBOOTEN',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'SSWWAIT',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'HSMDX',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'HSM6X',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'HSM16X',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'HSM17X',0,1
	.word	303
	.byte	1,2,2,35,0,9
	.byte	'S6ROM',0,1
	.word	303
	.byte	1,1,2,35,0,9
	.byte	'HSMENPINS',0,2
	.word	1657
	.byte	2,7,2,35,0,9
	.byte	'HSMENRES',0,1
	.word	303
	.byte	2,5,2,35,1,9
	.byte	'DESTDBG',0,1
	.word	303
	.byte	2,3,2,35,1,9
	.byte	'BLKFLAN',0,1
	.word	303
	.byte	1,2,2,35,1,9
	.byte	'BOOTSEL',0,1
	.word	303
	.byte	2,0,2,35,1,9
	.byte	'S16ROM',0,1
	.word	303
	.byte	1,7,2,35,2,9
	.byte	'S17ROM',0,1
	.word	303
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	1657
	.byte	14,0,2,35,2,0,6
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,2,166,3,3
	.word	6010
	.byte	8
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,2,169,3,16,4,9
	.byte	'S0ROM',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'S1ROM',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'S2ROM',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'S3ROM',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'S4ROM',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'S5ROM',0,1
	.word	303
	.byte	1,2,2,35,0,9
	.byte	'S6ROM',0,1
	.word	303
	.byte	1,1,2,35,0,9
	.byte	'S7ROM',0,1
	.word	303
	.byte	1,0,2,35,0,9
	.byte	'S8ROM',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'S9ROM',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'S10ROM',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'S11ROM',0,1
	.word	303
	.byte	1,4,2,35,1,9
	.byte	'S12ROM',0,1
	.word	303
	.byte	1,3,2,35,1,9
	.byte	'S13ROM',0,1
	.word	303
	.byte	1,2,2,35,1,9
	.byte	'S14ROM',0,1
	.word	303
	.byte	1,1,2,35,1,9
	.byte	'S15ROM',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'S16ROM',0,1
	.word	303
	.byte	1,7,2,35,2,9
	.byte	'S17ROM',0,1
	.word	303
	.byte	1,6,2,35,2,9
	.byte	'S18ROM',0,1
	.word	303
	.byte	1,5,2,35,2,9
	.byte	'S19ROM',0,1
	.word	303
	.byte	1,4,2,35,2,9
	.byte	'S20ROM',0,1
	.word	303
	.byte	1,3,2,35,2,9
	.byte	'S21ROM',0,1
	.word	303
	.byte	1,2,2,35,2,9
	.byte	'S22ROM',0,1
	.word	303
	.byte	1,1,2,35,2,9
	.byte	'S23ROM',0,1
	.word	303
	.byte	1,0,2,35,2,9
	.byte	'S24ROM',0,1
	.word	303
	.byte	1,7,2,35,3,9
	.byte	'S25ROM',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'S26ROM',0,1
	.word	303
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	303
	.byte	2,3,2,35,3,9
	.byte	'BML',0,1
	.word	303
	.byte	2,1,2,35,3,9
	.byte	'TP',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,2,201,3,3
	.word	6369
	.byte	8
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,2,204,3,16,4,9
	.byte	'S0L',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'S1L',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'S2L',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'S3L',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'S4L',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'S5L',0,1
	.word	303
	.byte	1,2,2,35,0,9
	.byte	'S6L',0,1
	.word	303
	.byte	1,1,2,35,0,9
	.byte	'S7L',0,1
	.word	303
	.byte	1,0,2,35,0,9
	.byte	'S8L',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'S9L',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'S10L',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'S11L',0,1
	.word	303
	.byte	1,4,2,35,1,9
	.byte	'S12L',0,1
	.word	303
	.byte	1,3,2,35,1,9
	.byte	'S13L',0,1
	.word	303
	.byte	1,2,2,35,1,9
	.byte	'S14L',0,1
	.word	303
	.byte	1,1,2,35,1,9
	.byte	'S15L',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'S16L',0,1
	.word	303
	.byte	1,7,2,35,2,9
	.byte	'S17L',0,1
	.word	303
	.byte	1,6,2,35,2,9
	.byte	'S18L',0,1
	.word	303
	.byte	1,5,2,35,2,9
	.byte	'S19L',0,1
	.word	303
	.byte	1,4,2,35,2,9
	.byte	'S20L',0,1
	.word	303
	.byte	1,3,2,35,2,9
	.byte	'S21L',0,1
	.word	303
	.byte	1,2,2,35,2,9
	.byte	'S22L',0,1
	.word	303
	.byte	1,1,2,35,2,9
	.byte	'S23L',0,1
	.word	303
	.byte	1,0,2,35,2,9
	.byte	'S24L',0,1
	.word	303
	.byte	1,7,2,35,3,9
	.byte	'S25L',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'S26L',0,1
	.word	303
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	303
	.byte	4,1,2,35,3,9
	.byte	'RPRO',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_PROCONP_Bits',0,2,235,3,3
	.word	6964
	.byte	8
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,2,238,3,16,4,9
	.byte	'S0WOP',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'S1WOP',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'S2WOP',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'S3WOP',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'S4WOP',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'S5WOP',0,1
	.word	303
	.byte	1,2,2,35,0,9
	.byte	'S6WOP',0,1
	.word	303
	.byte	1,1,2,35,0,9
	.byte	'S7WOP',0,1
	.word	303
	.byte	1,0,2,35,0,9
	.byte	'S8WOP',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'S9WOP',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'S10WOP',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'S11WOP',0,1
	.word	303
	.byte	1,4,2,35,1,9
	.byte	'S12WOP',0,1
	.word	303
	.byte	1,3,2,35,1,9
	.byte	'S13WOP',0,1
	.word	303
	.byte	1,2,2,35,1,9
	.byte	'S14WOP',0,1
	.word	303
	.byte	1,1,2,35,1,9
	.byte	'S15WOP',0,1
	.word	303
	.byte	1,0,2,35,1,9
	.byte	'S16WOP',0,1
	.word	303
	.byte	1,7,2,35,2,9
	.byte	'S17WOP',0,1
	.word	303
	.byte	1,6,2,35,2,9
	.byte	'S18WOP',0,1
	.word	303
	.byte	1,5,2,35,2,9
	.byte	'S19WOP',0,1
	.word	303
	.byte	1,4,2,35,2,9
	.byte	'S20WOP',0,1
	.word	303
	.byte	1,3,2,35,2,9
	.byte	'S21WOP',0,1
	.word	303
	.byte	1,2,2,35,2,9
	.byte	'S22WOP',0,1
	.word	303
	.byte	1,1,2,35,2,9
	.byte	'S23WOP',0,1
	.word	303
	.byte	1,0,2,35,2,9
	.byte	'S24WOP',0,1
	.word	303
	.byte	1,7,2,35,3,9
	.byte	'S25WOP',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'S26WOP',0,1
	.word	303
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	303
	.byte	4,1,2,35,3,9
	.byte	'DATM',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,2,141,4,3
	.word	7488
	.byte	8
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,2,144,4,16,4,9
	.byte	'TAG',0,1
	.word	303
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	881
	.byte	26,0,2,35,2,0,6
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,2,148,4,3
	.word	8070
	.byte	8
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,2,151,4,16,4,9
	.byte	'TAG',0,1
	.word	303
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	881
	.byte	26,0,2,35,2,0,6
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,2,155,4,3
	.word	8172
	.byte	8
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,2,158,4,16,4,9
	.byte	'TAG',0,1
	.word	303
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	881
	.byte	26,0,2,35,2,0,6
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,2,162,4,3
	.word	8274
	.byte	8
	.byte	'_Ifx_FLASH_RRAD_Bits',0,2,165,4,16,4,9
	.byte	'reserved_0',0,1
	.word	303
	.byte	3,5,2,35,0,9
	.byte	'ADD',0,4
	.word	881
	.byte	29,0,2,35,2,0,6
	.byte	'Ifx_FLASH_RRAD_Bits',0,2,169,4,3
	.word	8376
	.byte	8
	.byte	'_Ifx_FLASH_RRCT_Bits',0,2,172,4,16,4,9
	.byte	'STRT',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'STP',0,1
	.word	303
	.byte	1,6,2,35,0,9
	.byte	'BUSY',0,1
	.word	303
	.byte	1,5,2,35,0,9
	.byte	'DONE',0,1
	.word	303
	.byte	1,4,2,35,0,9
	.byte	'ERR',0,1
	.word	303
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	303
	.byte	3,0,2,35,0,9
	.byte	'EOBM',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,1
	.word	303
	.byte	7,0,2,35,1,9
	.byte	'CNT',0,2
	.word	1657
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_FLASH_RRCT_Bits',0,2,183,4,3
	.word	8470
	.byte	8
	.byte	'_Ifx_FLASH_RRD0_Bits',0,2,186,4,16,4,9
	.byte	'DATA',0,4
	.word	881
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_FLASH_RRD0_Bits',0,2,189,4,3
	.word	8680
	.byte	8
	.byte	'_Ifx_FLASH_RRD1_Bits',0,2,192,4,16,4,9
	.byte	'DATA',0,4
	.word	881
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_FLASH_RRD1_Bits',0,2,195,4,3
	.word	8753
	.byte	8
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,2,198,4,16,4,9
	.byte	'SEL',0,1
	.word	303
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	303
	.byte	2,0,2,35,0,9
	.byte	'CLR',0,1
	.word	303
	.byte	1,7,2,35,1,9
	.byte	'DIS',0,1
	.word	303
	.byte	1,6,2,35,1,9
	.byte	'reserved_10',0,4
	.word	881
	.byte	22,0,2,35,2,0,6
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,2,205,4,3
	.word	8826
	.byte	8
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,2,208,4,16,4,9
	.byte	'VLD0',0,1
	.word	303
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	881
	.byte	31,0,2,35,2,0,6
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,2,212,4,3
	.word	8981
	.byte	8
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,2,215,4,16,4,9
	.byte	'reserved_0',0,1
	.word	303
	.byte	5,3,2,35,0,9
	.byte	'ADDR',0,4
	.word	881
	.byte	19,8,2,35,2,9
	.byte	'ERR',0,1
	.word	303
	.byte	6,2,2,35,3,9
	.byte	'VLD',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'CLR',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,2,222,4,3
	.word	9086
	.byte	10,2,230,4,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,2
	.byte	'int',0,4,5,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	275
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_ACCEN0',0,2,235,4,3
	.word	9234
	.byte	10,2,238,4,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	853
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_ACCEN1',0,2,243,4,3
	.word	9307
	.byte	10,2,246,4,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	950
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_CBAB_CFG',0,2,251,4,3
	.word	9373
	.byte	10,2,254,4,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1103
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_CBAB_STAT',0,2,131,5,3
	.word	9441
	.byte	10,2,134,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1351
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_CBAB_TOP',0,2,139,5,3
	.word	9510
	.byte	10,2,142,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1497
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_COMM0',0,2,147,5,3
	.word	9578
	.byte	10,2,150,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1595
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_COMM1',0,2,155,5,3
	.word	9643
	.byte	10,2,158,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1733
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_COMM2',0,2,163,5,3
	.word	9708
	.byte	10,2,166,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1849
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_ECCRD',0,2,171,5,3
	.word	9773
	.byte	10,2,174,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1989
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_ECCRP',0,2,179,5,3
	.word	9838
	.byte	10,2,182,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2129
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_ECCW',0,2,187,5,3
	.word	9903
	.byte	10,2,190,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2268
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_FCON',0,2,195,5,3
	.word	9967
	.byte	10,2,198,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2630
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_FPRO',0,2,203,5,3
	.word	10031
	.byte	10,2,206,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3071
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_FSR',0,2,211,5,3
	.word	10095
	.byte	10,2,214,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3675
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_HSMFCON',0,2,219,5,3
	.word	10158
	.byte	10,2,222,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3855
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_HSMFSR',0,2,227,5,3
	.word	10225
	.byte	10,2,230,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4177
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_HSMMARD',0,2,235,5,3
	.word	10291
	.byte	10,2,238,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4358
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_HSMRRAD',0,2,243,5,3
	.word	10358
	.byte	10,2,246,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4458
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_HSMRRCT',0,2,251,5,3
	.word	10425
	.byte	10,2,254,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4674
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_HSMRRD0',0,2,131,6,3
	.word	10492
	.byte	10,2,134,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4753
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_HSMRRD1',0,2,139,6,3
	.word	10559
	.byte	10,2,142,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4832
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_ID',0,2,147,6,3
	.word	10626
	.byte	10,2,150,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4943
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_MARD',0,2,155,6,3
	.word	10688
	.byte	10,2,158,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5157
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_MARP',0,2,163,6,3
	.word	10752
	.byte	10,2,166,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5343
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_PROCOND',0,2,171,6,3
	.word	10816
	.byte	10,2,174,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5678
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_PROCONDBG',0,2,179,6,3
	.word	10883
	.byte	10,2,182,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5821
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_PROCONHSM',0,2,187,6,3
	.word	10952
	.byte	10,2,190,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6010
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,2,195,6,3
	.word	11021
	.byte	10,2,198,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6369
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_PROCONOTP',0,2,203,6,3
	.word	11094
	.byte	10,2,206,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6964
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_PROCONP',0,2,211,6,3
	.word	11163
	.byte	10,2,214,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7488
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_PROCONWOP',0,2,219,6,3
	.word	11230
	.byte	10,2,222,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8070
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_RDB_CFG0',0,2,227,6,3
	.word	11299
	.byte	10,2,230,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8172
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_RDB_CFG1',0,2,235,6,3
	.word	11367
	.byte	10,2,238,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8274
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_RDB_CFG2',0,2,243,6,3
	.word	11435
	.byte	10,2,246,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8376
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_RRAD',0,2,251,6,3
	.word	11503
	.byte	10,2,254,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8470
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_RRCT',0,2,131,7,3
	.word	11567
	.byte	10,2,134,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8680
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_RRD0',0,2,139,7,3
	.word	11631
	.byte	10,2,142,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8753
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_RRD1',0,2,147,7,3
	.word	11695
	.byte	10,2,150,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8826
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_UBAB_CFG',0,2,155,7,3
	.word	11759
	.byte	10,2,158,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8981
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_UBAB_STAT',0,2,163,7,3
	.word	11827
	.byte	10,2,166,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9086
	.byte	2,35,0,0,6
	.byte	'Ifx_FLASH_UBAB_TOP',0,2,171,7,3
	.word	11896
	.byte	8
	.byte	'_Ifx_FLASH_CBAB',0,2,182,7,25,48,11
	.byte	'CFG',0,4
	.word	9373
	.byte	2,35,0,11
	.byte	'STAT',0,4
	.word	9441
	.byte	2,35,4,11
	.byte	'TOP',0,4
	.word	9510
	.byte	2,35,8,12,36
	.word	303
	.byte	13,35,0,11
	.byte	'reserved_C',0,36
	.word	12026
	.byte	2,35,12,0,3
	.word	11964
	.byte	6
	.byte	'Ifx_FLASH_CBAB',0,2,188,7,3
	.word	12056
	.byte	8
	.byte	'_Ifx_FLASH_RDB',0,2,191,7,25,48,11
	.byte	'CFG0',0,4
	.word	11299
	.byte	2,35,0,11
	.byte	'CFG1',0,4
	.word	11367
	.byte	2,35,4,11
	.byte	'CFG2',0,4
	.word	11435
	.byte	2,35,8,11
	.byte	'reserved_C',0,36
	.word	12026
	.byte	2,35,12,0,3
	.word	12085
	.byte	6
	.byte	'Ifx_FLASH_RDB',0,2,197,7,3
	.word	12169
	.byte	8
	.byte	'_Ifx_FLASH_UBAB',0,2,200,7,25,92,11
	.byte	'CFG',0,4
	.word	11759
	.byte	2,35,0,11
	.byte	'STAT',0,4
	.word	11827
	.byte	2,35,4,11
	.byte	'TOP',0,4
	.word	11896
	.byte	2,35,8,12,80
	.word	303
	.byte	13,79,0,11
	.byte	'reserved_C',0,80
	.word	12259
	.byte	2,35,12,0,3
	.word	12197
	.byte	6
	.byte	'Ifx_FLASH_UBAB',0,2,206,7,3
	.word	12289
	.byte	8
	.byte	'_Ifx_SRC_SRCR_Bits',0,3,45,16,4,9
	.byte	'SRPN',0,1
	.word	303
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	303
	.byte	2,6,2,35,1,9
	.byte	'SRE',0,1
	.word	303
	.byte	1,5,2,35,1,9
	.byte	'TOS',0,1
	.word	303
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	303
	.byte	4,0,2,35,1,9
	.byte	'ECC',0,1
	.word	303
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	303
	.byte	3,0,2,35,2,9
	.byte	'SRR',0,1
	.word	303
	.byte	1,7,2,35,3,9
	.byte	'CLRR',0,1
	.word	303
	.byte	1,6,2,35,3,9
	.byte	'SETR',0,1
	.word	303
	.byte	1,5,2,35,3,9
	.byte	'IOV',0,1
	.word	303
	.byte	1,4,2,35,3,9
	.byte	'IOVCLR',0,1
	.word	303
	.byte	1,3,2,35,3,9
	.byte	'SWS',0,1
	.word	303
	.byte	1,2,2,35,3,9
	.byte	'SWSCLR',0,1
	.word	303
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	303
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SRC_SRCR_Bits',0,3,62,3
	.word	12318
	.byte	10,3,70,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12318
	.byte	2,35,0,0,6
	.byte	'Ifx_SRC_SRCR',0,3,75,3
	.word	12634
	.byte	8
	.byte	'_Ifx_SRC_ASCLIN',0,3,86,25,12,11
	.byte	'TX',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	12634
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	12634
	.byte	2,35,8,0,3
	.word	12694
	.byte	6
	.byte	'Ifx_SRC_ASCLIN',0,3,91,3
	.word	12753
	.byte	8
	.byte	'_Ifx_SRC_BCUSPB',0,3,94,25,4,11
	.byte	'SBSRC',0,4
	.word	12634
	.byte	2,35,0,0,3
	.word	12781
	.byte	6
	.byte	'Ifx_SRC_BCUSPB',0,3,97,3
	.word	12818
	.byte	8
	.byte	'_Ifx_SRC_CAN',0,3,100,25,64,12,64
	.word	12634
	.byte	13,15,0,11
	.byte	'INT',0,64
	.word	12864
	.byte	2,35,0,0,3
	.word	12846
	.byte	6
	.byte	'Ifx_SRC_CAN',0,3,103,3
	.word	12887
	.byte	8
	.byte	'_Ifx_SRC_CAN1',0,3,106,25,32,12,32
	.word	12634
	.byte	13,7,0,11
	.byte	'INT',0,32
	.word	12931
	.byte	2,35,0,0,3
	.word	12912
	.byte	6
	.byte	'Ifx_SRC_CAN1',0,3,109,3
	.word	12954
	.byte	8
	.byte	'_Ifx_SRC_CCU6',0,3,112,25,16,11
	.byte	'SR0',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	12634
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	12634
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	12634
	.byte	2,35,12,0,3
	.word	12980
	.byte	6
	.byte	'Ifx_SRC_CCU6',0,3,118,3
	.word	13052
	.byte	8
	.byte	'_Ifx_SRC_CERBERUS',0,3,121,25,8,12,8
	.word	12634
	.byte	13,1,0,11
	.byte	'SR',0,8
	.word	13101
	.byte	2,35,0,0,3
	.word	13078
	.byte	6
	.byte	'Ifx_SRC_CERBERUS',0,3,124,3
	.word	13123
	.byte	8
	.byte	'_Ifx_SRC_CPU',0,3,127,25,32,11
	.byte	'SBSRC',0,4
	.word	12634
	.byte	2,35,0,12,28
	.word	303
	.byte	13,27,0,11
	.byte	'reserved_4',0,28
	.word	13186
	.byte	2,35,4,0,3
	.word	13153
	.byte	6
	.byte	'Ifx_SRC_CPU',0,3,131,1,3
	.word	13216
	.byte	8
	.byte	'_Ifx_SRC_DMA',0,3,134,1,25,80,11
	.byte	'ERR',0,4
	.word	12634
	.byte	2,35,0,12,12
	.word	303
	.byte	13,11,0,11
	.byte	'reserved_4',0,12
	.word	13274
	.byte	2,35,4,11
	.byte	'CH',0,64
	.word	12864
	.byte	2,35,16,0,3
	.word	13242
	.byte	6
	.byte	'Ifx_SRC_DMA',0,3,139,1,3
	.word	13316
	.byte	8
	.byte	'_Ifx_SRC_EMEM',0,3,142,1,25,4,11
	.byte	'SR',0,4
	.word	12634
	.byte	2,35,0,0,3
	.word	13342
	.byte	6
	.byte	'Ifx_SRC_EMEM',0,3,145,1,3
	.word	13375
	.byte	8
	.byte	'_Ifx_SRC_ERAY',0,3,148,1,25,80,11
	.byte	'INT',0,8
	.word	13101
	.byte	2,35,0,11
	.byte	'TINT',0,8
	.word	13101
	.byte	2,35,8,11
	.byte	'NDAT',0,8
	.word	13101
	.byte	2,35,16,11
	.byte	'MBSC',0,8
	.word	13101
	.byte	2,35,24,11
	.byte	'OBUSY',0,4
	.word	12634
	.byte	2,35,32,11
	.byte	'IBUSY',0,4
	.word	12634
	.byte	2,35,36,12,40
	.word	303
	.byte	13,39,0,11
	.byte	'reserved_28',0,40
	.word	13507
	.byte	2,35,40,0,3
	.word	13402
	.byte	6
	.byte	'Ifx_SRC_ERAY',0,3,157,1,3
	.word	13538
	.byte	8
	.byte	'_Ifx_SRC_ETH',0,3,160,1,25,4,11
	.byte	'SR',0,4
	.word	12634
	.byte	2,35,0,0,3
	.word	13565
	.byte	6
	.byte	'Ifx_SRC_ETH',0,3,163,1,3
	.word	13597
	.byte	8
	.byte	'_Ifx_SRC_EVR',0,3,166,1,25,8,11
	.byte	'WUT',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'SCDC',0,4
	.word	12634
	.byte	2,35,4,0,3
	.word	13623
	.byte	6
	.byte	'Ifx_SRC_EVR',0,3,170,1,3
	.word	13670
	.byte	8
	.byte	'_Ifx_SRC_FFT',0,3,173,1,25,12,11
	.byte	'DONE',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'ERR',0,4
	.word	12634
	.byte	2,35,4,11
	.byte	'RFS',0,4
	.word	12634
	.byte	2,35,8,0,3
	.word	13696
	.byte	6
	.byte	'Ifx_SRC_FFT',0,3,178,1,3
	.word	13756
	.byte	8
	.byte	'_Ifx_SRC_GPSR',0,3,181,1,25,128,12,11
	.byte	'SR0',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	12634
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	12634
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	12634
	.byte	2,35,12,12,240,11
	.word	303
	.byte	13,239,11,0,11
	.byte	'reserved_10',0,240,11
	.word	13855
	.byte	2,35,16,0,3
	.word	13782
	.byte	6
	.byte	'Ifx_SRC_GPSR',0,3,188,1,3
	.word	13889
	.byte	8
	.byte	'_Ifx_SRC_GPT12',0,3,191,1,25,48,11
	.byte	'CIRQ',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'T2',0,4
	.word	12634
	.byte	2,35,4,11
	.byte	'T3',0,4
	.word	12634
	.byte	2,35,8,11
	.byte	'T4',0,4
	.word	12634
	.byte	2,35,12,11
	.byte	'T5',0,4
	.word	12634
	.byte	2,35,16,11
	.byte	'T6',0,4
	.word	12634
	.byte	2,35,20,12,24
	.word	303
	.byte	13,23,0,11
	.byte	'reserved_18',0,24
	.word	14011
	.byte	2,35,24,0,3
	.word	13916
	.byte	6
	.byte	'Ifx_SRC_GPT12',0,3,200,1,3
	.word	14042
	.byte	8
	.byte	'_Ifx_SRC_GTM',0,3,203,1,25,192,11,11
	.byte	'AEIIRQ',0,4
	.word	12634
	.byte	2,35,0,12,236,2
	.word	303
	.byte	13,235,2,0,11
	.byte	'reserved_4',0,236,2
	.word	14106
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	12634
	.byte	3,35,240,2,11
	.byte	'reserved_174',0,12
	.word	13274
	.byte	3,35,244,2,12,32
	.word	12931
	.byte	13,0,0,11
	.byte	'TIM',0,32
	.word	14175
	.byte	3,35,128,3,12,224,7
	.word	303
	.byte	13,223,7,0,11
	.byte	'reserved_1A0',0,224,7
	.word	14198
	.byte	3,35,160,3,12,64
	.word	12931
	.byte	13,1,0,11
	.byte	'TOM',0,64
	.word	14233
	.byte	3,35,128,11,0,3
	.word	14070
	.byte	6
	.byte	'Ifx_SRC_GTM',0,3,212,1,3
	.word	14257
	.byte	8
	.byte	'_Ifx_SRC_HSM',0,3,215,1,25,8,11
	.byte	'HSM',0,8
	.word	13101
	.byte	2,35,0,0,3
	.word	14283
	.byte	6
	.byte	'Ifx_SRC_HSM',0,3,218,1,3
	.word	14316
	.byte	8
	.byte	'_Ifx_SRC_LMU',0,3,221,1,25,4,11
	.byte	'SR',0,4
	.word	12634
	.byte	2,35,0,0,3
	.word	14342
	.byte	6
	.byte	'Ifx_SRC_LMU',0,3,224,1,3
	.word	14374
	.byte	8
	.byte	'_Ifx_SRC_PMU',0,3,227,1,25,4,11
	.byte	'SR',0,4
	.word	12634
	.byte	2,35,0,0,3
	.word	14400
	.byte	6
	.byte	'Ifx_SRC_PMU',0,3,230,1,3
	.word	14432
	.byte	8
	.byte	'_Ifx_SRC_QSPI',0,3,233,1,25,24,11
	.byte	'TX',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	12634
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	12634
	.byte	2,35,8,11
	.byte	'PT',0,4
	.word	12634
	.byte	2,35,12,11
	.byte	'HC',0,4
	.word	12634
	.byte	2,35,16,11
	.byte	'U',0,4
	.word	12634
	.byte	2,35,20,0,3
	.word	14458
	.byte	6
	.byte	'Ifx_SRC_QSPI',0,3,241,1,3
	.word	14551
	.byte	8
	.byte	'_Ifx_SRC_SCU',0,3,244,1,25,20,11
	.byte	'DTS',0,4
	.word	12634
	.byte	2,35,0,12,16
	.word	12634
	.byte	13,3,0,11
	.byte	'ERU',0,16
	.word	14610
	.byte	2,35,4,0,3
	.word	14578
	.byte	6
	.byte	'Ifx_SRC_SCU',0,3,248,1,3
	.word	14633
	.byte	8
	.byte	'_Ifx_SRC_SENT',0,3,251,1,25,16,11
	.byte	'SR',0,16
	.word	14610
	.byte	2,35,0,0,3
	.word	14659
	.byte	6
	.byte	'Ifx_SRC_SENT',0,3,254,1,3
	.word	14692
	.byte	8
	.byte	'_Ifx_SRC_SMU',0,3,129,2,25,12,12,12
	.word	12634
	.byte	13,2,0,11
	.byte	'SR',0,12
	.word	14738
	.byte	2,35,0,0,3
	.word	14719
	.byte	6
	.byte	'Ifx_SRC_SMU',0,3,132,2,3
	.word	14760
	.byte	8
	.byte	'_Ifx_SRC_STM',0,3,135,2,25,96,11
	.byte	'SR0',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	12634
	.byte	2,35,4,12,88
	.word	303
	.byte	13,87,0,11
	.byte	'reserved_8',0,88
	.word	14831
	.byte	2,35,8,0,3
	.word	14786
	.byte	6
	.byte	'Ifx_SRC_STM',0,3,140,2,3
	.word	14861
	.byte	8
	.byte	'_Ifx_SRC_VADCCG',0,3,143,2,25,192,2,11
	.byte	'SR0',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	12634
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	12634
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	12634
	.byte	2,35,12,12,176,2
	.word	303
	.byte	13,175,2,0,11
	.byte	'reserved_10',0,176,2
	.word	14962
	.byte	2,35,16,0,3
	.word	14887
	.byte	6
	.byte	'Ifx_SRC_VADCCG',0,3,150,2,3
	.word	14996
	.byte	8
	.byte	'_Ifx_SRC_VADCG',0,3,153,2,25,16,11
	.byte	'SR0',0,4
	.word	12634
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	12634
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	12634
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	12634
	.byte	2,35,12,0,3
	.word	15025
	.byte	6
	.byte	'Ifx_SRC_VADCG',0,3,159,2,3
	.word	15099
	.byte	8
	.byte	'_Ifx_SRC_XBAR',0,3,162,2,25,4,11
	.byte	'SRC',0,4
	.word	12634
	.byte	2,35,0,0,3
	.word	15127
	.byte	6
	.byte	'Ifx_SRC_XBAR',0,3,165,2,3
	.word	15161
	.byte	8
	.byte	'_Ifx_SRC_GASCLIN',0,3,178,2,25,24,12,24
	.word	12694
	.byte	13,1,0,3
	.word	15211
	.byte	11
	.byte	'ASCLIN',0,24
	.word	15220
	.byte	2,35,0,0,3
	.word	15188
	.byte	6
	.byte	'Ifx_SRC_GASCLIN',0,3,181,2,3
	.word	15242
	.byte	8
	.byte	'_Ifx_SRC_GBCU',0,3,184,2,25,4,3
	.word	12781
	.byte	11
	.byte	'SPB',0,4
	.word	15292
	.byte	2,35,0,0,3
	.word	15272
	.byte	6
	.byte	'Ifx_SRC_GBCU',0,3,187,2,3
	.word	15311
	.byte	8
	.byte	'_Ifx_SRC_GCAN',0,3,190,2,25,96,12,64
	.word	12846
	.byte	13,0,0,3
	.word	15358
	.byte	11
	.byte	'CAN',0,64
	.word	15367
	.byte	2,35,0,12,32
	.word	12912
	.byte	13,0,0,3
	.word	15385
	.byte	11
	.byte	'CAN1',0,32
	.word	15394
	.byte	2,35,64,0,3
	.word	15338
	.byte	6
	.byte	'Ifx_SRC_GCAN',0,3,194,2,3
	.word	15414
	.byte	8
	.byte	'_Ifx_SRC_GCCU6',0,3,197,2,25,32,12,32
	.word	12980
	.byte	13,1,0,3
	.word	15462
	.byte	11
	.byte	'CCU6',0,32
	.word	15471
	.byte	2,35,0,0,3
	.word	15441
	.byte	6
	.byte	'Ifx_SRC_GCCU6',0,3,200,2,3
	.word	15491
	.byte	8
	.byte	'_Ifx_SRC_GCERBERUS',0,3,203,2,25,8,3
	.word	13078
	.byte	11
	.byte	'CERBERUS',0,8
	.word	15544
	.byte	2,35,0,0,3
	.word	15519
	.byte	6
	.byte	'Ifx_SRC_GCERBERUS',0,3,206,2,3
	.word	15568
	.byte	8
	.byte	'_Ifx_SRC_GCPU',0,3,209,2,25,32,12,32
	.word	13153
	.byte	13,0,0,3
	.word	15620
	.byte	11
	.byte	'CPU',0,32
	.word	15629
	.byte	2,35,0,0,3
	.word	15600
	.byte	6
	.byte	'Ifx_SRC_GCPU',0,3,212,2,3
	.word	15648
	.byte	8
	.byte	'_Ifx_SRC_GDMA',0,3,215,2,25,80,12,80
	.word	13242
	.byte	13,0,0,3
	.word	15695
	.byte	11
	.byte	'DMA',0,80
	.word	15704
	.byte	2,35,0,0,3
	.word	15675
	.byte	6
	.byte	'Ifx_SRC_GDMA',0,3,218,2,3
	.word	15723
	.byte	8
	.byte	'_Ifx_SRC_GEMEM',0,3,221,2,25,4,12,4
	.word	13342
	.byte	13,0,0,3
	.word	15771
	.byte	11
	.byte	'EMEM',0,4
	.word	15780
	.byte	2,35,0,0,3
	.word	15750
	.byte	6
	.byte	'Ifx_SRC_GEMEM',0,3,224,2,3
	.word	15800
	.byte	8
	.byte	'_Ifx_SRC_GERAY',0,3,227,2,25,80,12,80
	.word	13402
	.byte	13,0,0,3
	.word	15849
	.byte	11
	.byte	'ERAY',0,80
	.word	15858
	.byte	2,35,0,0,3
	.word	15828
	.byte	6
	.byte	'Ifx_SRC_GERAY',0,3,230,2,3
	.word	15878
	.byte	8
	.byte	'_Ifx_SRC_GETH',0,3,233,2,25,4,12,4
	.word	13565
	.byte	13,0,0,3
	.word	15926
	.byte	11
	.byte	'ETH',0,4
	.word	15935
	.byte	2,35,0,0,3
	.word	15906
	.byte	6
	.byte	'Ifx_SRC_GETH',0,3,236,2,3
	.word	15954
	.byte	8
	.byte	'_Ifx_SRC_GEVR',0,3,239,2,25,8,12,8
	.word	13623
	.byte	13,0,0,3
	.word	16001
	.byte	11
	.byte	'EVR',0,8
	.word	16010
	.byte	2,35,0,0,3
	.word	15981
	.byte	6
	.byte	'Ifx_SRC_GEVR',0,3,242,2,3
	.word	16029
	.byte	8
	.byte	'_Ifx_SRC_GFFT',0,3,245,2,25,12,12,12
	.word	13696
	.byte	13,0,0,3
	.word	16076
	.byte	11
	.byte	'FFT',0,12
	.word	16085
	.byte	2,35,0,0,3
	.word	16056
	.byte	6
	.byte	'Ifx_SRC_GFFT',0,3,248,2,3
	.word	16104
	.byte	8
	.byte	'_Ifx_SRC_GGPSR',0,3,251,2,25,128,12,12,128,12
	.word	13782
	.byte	13,0,0,3
	.word	16153
	.byte	11
	.byte	'GPSR',0,128,12
	.word	16163
	.byte	2,35,0,0,3
	.word	16131
	.byte	6
	.byte	'Ifx_SRC_GGPSR',0,3,254,2,3
	.word	16184
	.byte	8
	.byte	'_Ifx_SRC_GGPT12',0,3,129,3,25,48,12,48
	.word	13916
	.byte	13,0,0,3
	.word	16234
	.byte	11
	.byte	'GPT12',0,48
	.word	16243
	.byte	2,35,0,0,3
	.word	16212
	.byte	6
	.byte	'Ifx_SRC_GGPT12',0,3,132,3,3
	.word	16264
	.byte	8
	.byte	'_Ifx_SRC_GGTM',0,3,135,3,25,192,11,12,192,11
	.word	14070
	.byte	13,0,0,3
	.word	16314
	.byte	11
	.byte	'GTM',0,192,11
	.word	16324
	.byte	2,35,0,0,3
	.word	16293
	.byte	6
	.byte	'Ifx_SRC_GGTM',0,3,138,3,3
	.word	16344
	.byte	8
	.byte	'_Ifx_SRC_GHSM',0,3,141,3,25,8,12,8
	.word	14283
	.byte	13,0,0,3
	.word	16391
	.byte	11
	.byte	'HSM',0,8
	.word	16400
	.byte	2,35,0,0,3
	.word	16371
	.byte	6
	.byte	'Ifx_SRC_GHSM',0,3,144,3,3
	.word	16419
	.byte	8
	.byte	'_Ifx_SRC_GLMU',0,3,147,3,25,4,12,4
	.word	14342
	.byte	13,0,0,3
	.word	16466
	.byte	11
	.byte	'LMU',0,4
	.word	16475
	.byte	2,35,0,0,3
	.word	16446
	.byte	6
	.byte	'Ifx_SRC_GLMU',0,3,150,3,3
	.word	16494
	.byte	8
	.byte	'_Ifx_SRC_GPMU',0,3,153,3,25,8,12,8
	.word	14400
	.byte	13,1,0,3
	.word	16541
	.byte	11
	.byte	'PMU',0,8
	.word	16550
	.byte	2,35,0,0,3
	.word	16521
	.byte	6
	.byte	'Ifx_SRC_GPMU',0,3,156,3,3
	.word	16569
	.byte	8
	.byte	'_Ifx_SRC_GQSPI',0,3,159,3,25,96,12,96
	.word	14458
	.byte	13,3,0,3
	.word	16617
	.byte	11
	.byte	'QSPI',0,96
	.word	16626
	.byte	2,35,0,0,3
	.word	16596
	.byte	6
	.byte	'Ifx_SRC_GQSPI',0,3,162,3,3
	.word	16646
	.byte	8
	.byte	'_Ifx_SRC_GSCU',0,3,165,3,25,20,3
	.word	14578
	.byte	11
	.byte	'SCU',0,20
	.word	16694
	.byte	2,35,0,0,3
	.word	16674
	.byte	6
	.byte	'Ifx_SRC_GSCU',0,3,168,3,3
	.word	16713
	.byte	8
	.byte	'_Ifx_SRC_GSENT',0,3,171,3,25,16,12,16
	.word	14659
	.byte	13,0,0,3
	.word	16761
	.byte	11
	.byte	'SENT',0,16
	.word	16770
	.byte	2,35,0,0,3
	.word	16740
	.byte	6
	.byte	'Ifx_SRC_GSENT',0,3,174,3,3
	.word	16790
	.byte	8
	.byte	'_Ifx_SRC_GSMU',0,3,177,3,25,12,12,12
	.word	14719
	.byte	13,0,0,3
	.word	16838
	.byte	11
	.byte	'SMU',0,12
	.word	16847
	.byte	2,35,0,0,3
	.word	16818
	.byte	6
	.byte	'Ifx_SRC_GSMU',0,3,180,3,3
	.word	16866
	.byte	8
	.byte	'_Ifx_SRC_GSTM',0,3,183,3,25,96,12,96
	.word	14786
	.byte	13,0,0,3
	.word	16913
	.byte	11
	.byte	'STM',0,96
	.word	16922
	.byte	2,35,0,0,3
	.word	16893
	.byte	6
	.byte	'Ifx_SRC_GSTM',0,3,186,3,3
	.word	16941
	.byte	8
	.byte	'_Ifx_SRC_GVADC',0,3,189,3,25,224,4,12,64
	.word	15025
	.byte	13,3,0,3
	.word	16990
	.byte	11
	.byte	'G',0,64
	.word	16999
	.byte	2,35,0,12,224,1
	.word	303
	.byte	13,223,1,0,11
	.byte	'reserved_40',0,224,1
	.word	17015
	.byte	2,35,64,12,192,2
	.word	14887
	.byte	13,0,0,3
	.word	17048
	.byte	11
	.byte	'CG',0,192,2
	.word	17058
	.byte	3,35,160,2,0,3
	.word	16968
	.byte	6
	.byte	'Ifx_SRC_GVADC',0,3,194,3,3
	.word	17078
	.byte	8
	.byte	'_Ifx_SRC_GXBAR',0,3,197,3,25,4,3
	.word	15127
	.byte	11
	.byte	'XBAR',0,4
	.word	17127
	.byte	2,35,0,0,3
	.word	17106
	.byte	6
	.byte	'Ifx_SRC_GXBAR',0,3,200,3,3
	.word	17147
	.byte	6
	.byte	'uint8',0,4,90,29
	.word	303
	.byte	6
	.byte	'uint16',0,4,92,29
	.word	1657
	.byte	6
	.byte	'uint32',0,4,94,29
	.word	194
	.byte	3
	.word	194
	.byte	6
	.byte	'FlsLoader_AddressType',0,5,158,1,25
	.word	17219
	.byte	6
	.byte	'FlsLoader_ReturnType',0,5,170,1,16
	.word	194
	.byte	8
	.byte	'_Ifx_CPU_A_Bits',0,6,45,16,4,2
	.byte	'unsigned int',0,4,7,9
	.byte	'ADDR',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_A_Bits',0,6,48,3
	.word	17285
	.byte	8
	.byte	'_Ifx_CPU_BIV_Bits',0,6,51,16,4,9
	.byte	'VSS',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'BIV',0,4
	.word	17306
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_BIV_Bits',0,6,55,3
	.word	17362
	.byte	8
	.byte	'_Ifx_CPU_BTV_Bits',0,6,58,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'BTV',0,4
	.word	17306
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_BTV_Bits',0,6,62,3
	.word	17441
	.byte	8
	.byte	'_Ifx_CPU_CCNT_Bits',0,6,65,16,4,9
	.byte	'CountValue',0,4
	.word	17306
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	17306
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_CCNT_Bits',0,6,69,3
	.word	17527
	.byte	8
	.byte	'_Ifx_CPU_CCTRL_Bits',0,6,72,16,4,9
	.byte	'CM',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'CE',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'M1',0,4
	.word	17306
	.byte	3,27,2,35,0,9
	.byte	'M2',0,4
	.word	17306
	.byte	3,24,2,35,0,9
	.byte	'M3',0,4
	.word	17306
	.byte	3,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	17306
	.byte	21,0,2,35,0,0,6
	.byte	'Ifx_CPU_CCTRL_Bits',0,6,80,3
	.word	17616
	.byte	8
	.byte	'_Ifx_CPU_COMPAT_Bits',0,6,83,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'RM',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'SP',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	17306
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_COMPAT_Bits',0,6,89,3
	.word	17762
	.byte	8
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,6,92,16,4,9
	.byte	'CORE_ID',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	17306
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_CORE_ID_Bits',0,6,96,3
	.word	17889
	.byte	8
	.byte	'_Ifx_CPU_CPR_L_Bits',0,6,99,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'LOWBND',0,4
	.word	17306
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_CPR_L_Bits',0,6,103,3
	.word	17987
	.byte	8
	.byte	'_Ifx_CPU_CPR_U_Bits',0,6,106,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'UPPBND',0,4
	.word	17306
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_CPR_U_Bits',0,6,110,3
	.word	18080
	.byte	8
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,6,113,16,4,9
	.byte	'MODREV',0,4
	.word	17306
	.byte	8,24,2,35,0,9
	.byte	'MOD_32B',0,4
	.word	17306
	.byte	8,16,2,35,0,9
	.byte	'MOD',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_CPU_ID_Bits',0,6,118,3
	.word	18173
	.byte	8
	.byte	'_Ifx_CPU_CPXE_Bits',0,6,121,16,4,9
	.byte	'XE',0,4
	.word	17306
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	17306
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_CPXE_Bits',0,6,125,3
	.word	18280
	.byte	8
	.byte	'_Ifx_CPU_CREVT_Bits',0,6,128,1,16,4,9
	.byte	'EVTA',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	17306
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	17306
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	17306
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_CREVT_Bits',0,6,136,1,3
	.word	18367
	.byte	8
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,6,139,1,16,4,9
	.byte	'CID',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	17306
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_CUS_ID_Bits',0,6,143,1,3
	.word	18521
	.byte	8
	.byte	'_Ifx_CPU_D_Bits',0,6,146,1,16,4,9
	.byte	'DATA',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_D_Bits',0,6,149,1,3
	.word	18615
	.byte	8
	.byte	'_Ifx_CPU_DATR_Bits',0,6,152,1,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'SBE',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	17306
	.byte	5,23,2,35,0,9
	.byte	'CWE',0,4
	.word	17306
	.byte	1,22,2,35,0,9
	.byte	'CFE',0,4
	.word	17306
	.byte	1,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	17306
	.byte	3,18,2,35,0,9
	.byte	'SOE',0,4
	.word	17306
	.byte	1,17,2,35,0,9
	.byte	'SME',0,4
	.word	17306
	.byte	1,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_DATR_Bits',0,6,163,1,3
	.word	18678
	.byte	8
	.byte	'_Ifx_CPU_DBGSR_Bits',0,6,166,1,16,4,9
	.byte	'DE',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'HALT',0,4
	.word	17306
	.byte	2,29,2,35,0,9
	.byte	'SIH',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'SUSP',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	17306
	.byte	1,26,2,35,0,9
	.byte	'PREVSUSP',0,4
	.word	17306
	.byte	1,25,2,35,0,9
	.byte	'PEVT',0,4
	.word	17306
	.byte	1,24,2,35,0,9
	.byte	'EVTSRC',0,4
	.word	17306
	.byte	5,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	17306
	.byte	19,0,2,35,0,0,6
	.byte	'Ifx_CPU_DBGSR_Bits',0,6,177,1,3
	.word	18896
	.byte	8
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,6,180,1,16,4,9
	.byte	'DTA',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	17306
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_DBGTCR_Bits',0,6,184,1,3
	.word	19111
	.byte	8
	.byte	'_Ifx_CPU_DCON0_Bits',0,6,187,1,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'DCBYP',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	17306
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_CPU_DCON0_Bits',0,6,192,1,3
	.word	19205
	.byte	8
	.byte	'_Ifx_CPU_DCON2_Bits',0,6,195,1,16,4,9
	.byte	'DCACHE_SZE',0,4
	.word	17306
	.byte	16,16,2,35,0,9
	.byte	'DSCRATCH_SZE',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_DCON2_Bits',0,6,199,1,3
	.word	19321
	.byte	8
	.byte	'_Ifx_CPU_DCX_Bits',0,6,202,1,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	6,26,2,35,0,9
	.byte	'DCXValue',0,4
	.word	17306
	.byte	26,0,2,35,0,0,6
	.byte	'Ifx_CPU_DCX_Bits',0,6,206,1,3
	.word	19422
	.byte	8
	.byte	'_Ifx_CPU_DEADD_Bits',0,6,209,1,16,4,9
	.byte	'ERROR_ADDRESS',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_DEADD_Bits',0,6,212,1,3
	.word	19515
	.byte	8
	.byte	'_Ifx_CPU_DIEAR_Bits',0,6,215,1,16,4,9
	.byte	'TA',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_DIEAR_Bits',0,6,218,1,3
	.word	19595
	.byte	8
	.byte	'_Ifx_CPU_DIETR_Bits',0,6,221,1,16,4,9
	.byte	'IED',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'IE_T',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'IE_C',0,4
	.word	17306
	.byte	1,29,2,35,0,9
	.byte	'IE_S',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'IE_BI',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'E_INFO',0,4
	.word	17306
	.byte	6,21,2,35,0,9
	.byte	'IE_DUAL',0,4
	.word	17306
	.byte	1,20,2,35,0,9
	.byte	'IE_SP',0,4
	.word	17306
	.byte	1,19,2,35,0,9
	.byte	'IE_BS',0,4
	.word	17306
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	17306
	.byte	18,0,2,35,0,0,6
	.byte	'Ifx_CPU_DIETR_Bits',0,6,233,1,3
	.word	19664
	.byte	8
	.byte	'_Ifx_CPU_DMS_Bits',0,6,236,1,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'DMSValue',0,4
	.word	17306
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_DMS_Bits',0,6,240,1,3
	.word	19893
	.byte	8
	.byte	'_Ifx_CPU_DPR_L_Bits',0,6,243,1,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'LOWBND',0,4
	.word	17306
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_DPR_L_Bits',0,6,247,1,3
	.word	19986
	.byte	8
	.byte	'_Ifx_CPU_DPR_U_Bits',0,6,250,1,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'UPPBND',0,4
	.word	17306
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_DPR_U_Bits',0,6,254,1,3
	.word	20081
	.byte	8
	.byte	'_Ifx_CPU_DPRE_Bits',0,6,129,2,16,4,9
	.byte	'RE',0,4
	.word	17306
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_DPRE_Bits',0,6,133,2,3
	.word	20176
	.byte	8
	.byte	'_Ifx_CPU_DPWE_Bits',0,6,136,2,16,4,9
	.byte	'WE',0,4
	.word	17306
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_DPWE_Bits',0,6,140,2,3
	.word	20266
	.byte	8
	.byte	'_Ifx_CPU_DSTR_Bits',0,6,143,2,16,4,9
	.byte	'SRE',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'GAE',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'LBE',0,4
	.word	17306
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	17306
	.byte	3,26,2,35,0,9
	.byte	'CRE',0,4
	.word	17306
	.byte	1,25,2,35,0,9
	.byte	'reserved_7',0,4
	.word	17306
	.byte	7,18,2,35,0,9
	.byte	'DTME',0,4
	.word	17306
	.byte	1,17,2,35,0,9
	.byte	'LOE',0,4
	.word	17306
	.byte	1,16,2,35,0,9
	.byte	'SDE',0,4
	.word	17306
	.byte	1,15,2,35,0,9
	.byte	'SCE',0,4
	.word	17306
	.byte	1,14,2,35,0,9
	.byte	'CAC',0,4
	.word	17306
	.byte	1,13,2,35,0,9
	.byte	'MPE',0,4
	.word	17306
	.byte	1,12,2,35,0,9
	.byte	'CLE',0,4
	.word	17306
	.byte	1,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	17306
	.byte	3,8,2,35,0,9
	.byte	'ALN',0,4
	.word	17306
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	17306
	.byte	7,0,2,35,0,0,6
	.byte	'Ifx_CPU_DSTR_Bits',0,6,161,2,3
	.word	20356
	.byte	8
	.byte	'_Ifx_CPU_EXEVT_Bits',0,6,164,2,16,4,9
	.byte	'EVTA',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	17306
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	17306
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	17306
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_EXEVT_Bits',0,6,172,2,3
	.word	20680
	.byte	8
	.byte	'_Ifx_CPU_FCX_Bits',0,6,175,2,16,4,9
	.byte	'FCXO',0,4
	.word	17306
	.byte	16,16,2,35,0,9
	.byte	'FCXS',0,4
	.word	17306
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	17306
	.byte	12,0,2,35,0,0,6
	.byte	'Ifx_CPU_FCX_Bits',0,6,180,2,3
	.word	20834
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,6,183,2,16,4,9
	.byte	'TST',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'TCL',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	17306
	.byte	6,24,2,35,0,9
	.byte	'RM',0,4
	.word	17306
	.byte	2,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	17306
	.byte	8,14,2,35,0,9
	.byte	'FXE',0,4
	.word	17306
	.byte	1,13,2,35,0,9
	.byte	'FUE',0,4
	.word	17306
	.byte	1,12,2,35,0,9
	.byte	'FZE',0,4
	.word	17306
	.byte	1,11,2,35,0,9
	.byte	'FVE',0,4
	.word	17306
	.byte	1,10,2,35,0,9
	.byte	'FIE',0,4
	.word	17306
	.byte	1,9,2,35,0,9
	.byte	'reserved_23',0,4
	.word	17306
	.byte	3,6,2,35,0,9
	.byte	'FX',0,4
	.word	17306
	.byte	1,5,2,35,0,9
	.byte	'FU',0,4
	.word	17306
	.byte	1,4,2,35,0,9
	.byte	'FZ',0,4
	.word	17306
	.byte	1,3,2,35,0,9
	.byte	'FV',0,4
	.word	17306
	.byte	1,2,2,35,0,9
	.byte	'FI',0,4
	.word	17306
	.byte	1,1,2,35,0,9
	.byte	'reserved_31',0,4
	.word	17306
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,6,202,2,3
	.word	20940
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,205,2,16,4,9
	.byte	'OPC',0,4
	.word	17306
	.byte	8,24,2,35,0,9
	.byte	'FMT',0,4
	.word	17306
	.byte	1,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	17306
	.byte	7,16,2,35,0,9
	.byte	'DREG',0,4
	.word	17306
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	17306
	.byte	12,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,212,2,3
	.word	21289
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,6,215,2,16,4,9
	.byte	'PC',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,6,218,2,3
	.word	21449
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,221,2,16,4,9
	.byte	'SRC1',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,224,2,3
	.word	21530
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,227,2,16,4,9
	.byte	'SRC2',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,230,2,3
	.word	21617
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,233,2,16,4,9
	.byte	'SRC3',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,236,2,3
	.word	21704
	.byte	8
	.byte	'_Ifx_CPU_ICNT_Bits',0,6,239,2,16,4,9
	.byte	'CountValue',0,4
	.word	17306
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	17306
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_ICNT_Bits',0,6,243,2,3
	.word	21791
	.byte	8
	.byte	'_Ifx_CPU_ICR_Bits',0,6,246,2,16,4,9
	.byte	'CCPN',0,4
	.word	17306
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	17306
	.byte	5,17,2,35,0,9
	.byte	'IE',0,4
	.word	17306
	.byte	1,16,2,35,0,9
	.byte	'PIPN',0,4
	.word	17306
	.byte	10,6,2,35,0,9
	.byte	'reserved_26',0,4
	.word	17306
	.byte	6,0,2,35,0,0,6
	.byte	'Ifx_CPU_ICR_Bits',0,6,253,2,3
	.word	21882
	.byte	8
	.byte	'_Ifx_CPU_ISP_Bits',0,6,128,3,16,4,9
	.byte	'ISP',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_ISP_Bits',0,6,131,3,3
	.word	22025
	.byte	8
	.byte	'_Ifx_CPU_LCX_Bits',0,6,134,3,16,4,9
	.byte	'LCXO',0,4
	.word	17306
	.byte	16,16,2,35,0,9
	.byte	'LCXS',0,4
	.word	17306
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	17306
	.byte	12,0,2,35,0,0,6
	.byte	'Ifx_CPU_LCX_Bits',0,6,139,3,3
	.word	22091
	.byte	8
	.byte	'_Ifx_CPU_M1CNT_Bits',0,6,142,3,16,4,9
	.byte	'CountValue',0,4
	.word	17306
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	17306
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_M1CNT_Bits',0,6,146,3,3
	.word	22197
	.byte	8
	.byte	'_Ifx_CPU_M2CNT_Bits',0,6,149,3,16,4,9
	.byte	'CountValue',0,4
	.word	17306
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	17306
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_M2CNT_Bits',0,6,153,3,3
	.word	22290
	.byte	8
	.byte	'_Ifx_CPU_M3CNT_Bits',0,6,156,3,16,4,9
	.byte	'CountValue',0,4
	.word	17306
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	17306
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_M3CNT_Bits',0,6,160,3,3
	.word	22383
	.byte	8
	.byte	'_Ifx_CPU_PC_Bits',0,6,163,3,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'PC',0,4
	.word	17306
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_PC_Bits',0,6,167,3,3
	.word	22476
	.byte	8
	.byte	'_Ifx_CPU_PCON0_Bits',0,6,170,3,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'PCBYP',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	17306
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_CPU_PCON0_Bits',0,6,175,3,3
	.word	22561
	.byte	8
	.byte	'_Ifx_CPU_PCON1_Bits',0,6,178,3,16,4,9
	.byte	'PCINV',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'PBINV',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	17306
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_CPU_PCON1_Bits',0,6,183,3,3
	.word	22677
	.byte	8
	.byte	'_Ifx_CPU_PCON2_Bits',0,6,186,3,16,4,9
	.byte	'PCACHE_SZE',0,4
	.word	17306
	.byte	16,16,2,35,0,9
	.byte	'PSCRATCH_SZE',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_PCON2_Bits',0,6,190,3,3
	.word	22788
	.byte	8
	.byte	'_Ifx_CPU_PCXI_Bits',0,6,193,3,16,4,9
	.byte	'PCXO',0,4
	.word	17306
	.byte	16,16,2,35,0,9
	.byte	'PCXS',0,4
	.word	17306
	.byte	4,12,2,35,0,9
	.byte	'UL',0,4
	.word	17306
	.byte	1,11,2,35,0,9
	.byte	'PIE',0,4
	.word	17306
	.byte	1,10,2,35,0,9
	.byte	'PCPN',0,4
	.word	17306
	.byte	10,0,2,35,0,0,6
	.byte	'Ifx_CPU_PCXI_Bits',0,6,200,3,3
	.word	22889
	.byte	8
	.byte	'_Ifx_CPU_PIEAR_Bits',0,6,203,3,16,4,9
	.byte	'TA',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_PIEAR_Bits',0,6,206,3,3
	.word	23019
	.byte	8
	.byte	'_Ifx_CPU_PIETR_Bits',0,6,209,3,16,4,9
	.byte	'IED',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'IE_T',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'IE_C',0,4
	.word	17306
	.byte	1,29,2,35,0,9
	.byte	'IE_S',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'IE_BI',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'E_INFO',0,4
	.word	17306
	.byte	6,21,2,35,0,9
	.byte	'IE_DUAL',0,4
	.word	17306
	.byte	1,20,2,35,0,9
	.byte	'IE_SP',0,4
	.word	17306
	.byte	1,19,2,35,0,9
	.byte	'IE_BS',0,4
	.word	17306
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	17306
	.byte	18,0,2,35,0,0,6
	.byte	'Ifx_CPU_PIETR_Bits',0,6,221,3,3
	.word	23088
	.byte	8
	.byte	'_Ifx_CPU_PMA0_Bits',0,6,224,3,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	13,19,2,35,0,9
	.byte	'DAC',0,4
	.word	17306
	.byte	3,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_PMA0_Bits',0,6,229,3,3
	.word	23317
	.byte	8
	.byte	'_Ifx_CPU_PMA1_Bits',0,6,232,3,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	14,18,2,35,0,9
	.byte	'CAC',0,4
	.word	17306
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_PMA1_Bits',0,6,237,3,3
	.word	23430
	.byte	8
	.byte	'_Ifx_CPU_PMA2_Bits',0,6,240,3,16,4,9
	.byte	'PSI',0,4
	.word	17306
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	17306
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_PMA2_Bits',0,6,244,3,3
	.word	23543
	.byte	8
	.byte	'_Ifx_CPU_PSTR_Bits',0,6,247,3,16,4,9
	.byte	'FRE',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'FBE',0,4
	.word	17306
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	17306
	.byte	9,20,2,35,0,9
	.byte	'FPE',0,4
	.word	17306
	.byte	1,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	17306
	.byte	1,18,2,35,0,9
	.byte	'FME',0,4
	.word	17306
	.byte	1,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	17306
	.byte	17,0,2,35,0,0,6
	.byte	'Ifx_CPU_PSTR_Bits',0,6,129,4,3
	.word	23634
	.byte	8
	.byte	'_Ifx_CPU_PSW_Bits',0,6,132,4,16,4,9
	.byte	'CDC',0,4
	.word	17306
	.byte	7,25,2,35,0,9
	.byte	'CDE',0,4
	.word	17306
	.byte	1,24,2,35,0,9
	.byte	'GW',0,4
	.word	17306
	.byte	1,23,2,35,0,9
	.byte	'IS',0,4
	.word	17306
	.byte	1,22,2,35,0,9
	.byte	'IO',0,4
	.word	17306
	.byte	2,20,2,35,0,9
	.byte	'PRS',0,4
	.word	17306
	.byte	2,18,2,35,0,9
	.byte	'S',0,4
	.word	17306
	.byte	1,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	17306
	.byte	12,5,2,35,0,9
	.byte	'SAV',0,4
	.word	17306
	.byte	1,4,2,35,0,9
	.byte	'AV',0,4
	.word	17306
	.byte	1,3,2,35,0,9
	.byte	'SV',0,4
	.word	17306
	.byte	1,2,2,35,0,9
	.byte	'V',0,4
	.word	17306
	.byte	1,1,2,35,0,9
	.byte	'C',0,4
	.word	17306
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_PSW_Bits',0,6,147,4,3
	.word	23837
	.byte	8
	.byte	'_Ifx_CPU_SEGEN_Bits',0,6,150,4,16,4,9
	.byte	'ADFLIP',0,4
	.word	17306
	.byte	8,24,2,35,0,9
	.byte	'ADTYPE',0,4
	.word	17306
	.byte	2,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	17306
	.byte	21,1,2,35,0,9
	.byte	'AE',0,4
	.word	17306
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_SEGEN_Bits',0,6,156,4,3
	.word	24080
	.byte	8
	.byte	'_Ifx_CPU_SMACON_Bits',0,6,159,4,16,4,9
	.byte	'PC',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'PT',0,4
	.word	17306
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	17306
	.byte	5,24,2,35,0,9
	.byte	'DC',0,4
	.word	17306
	.byte	1,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	17306
	.byte	1,22,2,35,0,9
	.byte	'DT',0,4
	.word	17306
	.byte	1,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	17306
	.byte	13,8,2,35,0,9
	.byte	'IODT',0,4
	.word	17306
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	17306
	.byte	7,0,2,35,0,0,6
	.byte	'Ifx_CPU_SMACON_Bits',0,6,171,4,3
	.word	24208
	.byte	8
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,6,174,4,16,4,9
	.byte	'EN',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,6,177,4,3
	.word	24449
	.byte	8
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,6,180,4,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,6,183,4,3
	.word	24532
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,186,4,16,4,9
	.byte	'EN',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,189,4,3
	.word	24623
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,192,4,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,195,4,3
	.word	24714
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,6,198,4,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	5,27,2,35,0,9
	.byte	'ADDR',0,4
	.word	17306
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,6,202,4,3
	.word	24813
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,6,205,4,16,4,9
	.byte	'reserved_0',0,4
	.word	17306
	.byte	5,27,2,35,0,9
	.byte	'ADDR',0,4
	.word	17306
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,6,209,4,3
	.word	24920
	.byte	8
	.byte	'_Ifx_CPU_SWEVT_Bits',0,6,212,4,16,4,9
	.byte	'EVTA',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	17306
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	17306
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	17306
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_SWEVT_Bits',0,6,220,4,3
	.word	25027
	.byte	8
	.byte	'_Ifx_CPU_SYSCON_Bits',0,6,223,4,16,4,9
	.byte	'FCDSF',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'PROTEN',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'TPROTEN',0,4
	.word	17306
	.byte	1,29,2,35,0,9
	.byte	'IS',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'IT',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	17306
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_SYSCON_Bits',0,6,231,4,3
	.word	25181
	.byte	8
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,6,234,4,16,4,9
	.byte	'ASI',0,4
	.word	17306
	.byte	5,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	17306
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,6,238,4,3
	.word	25342
	.byte	8
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,6,241,4,16,4,9
	.byte	'TEXP0',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'TEXP1',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'TEXP2',0,4
	.word	17306
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	17306
	.byte	13,16,2,35,0,9
	.byte	'TTRAP',0,4
	.word	17306
	.byte	1,15,2,35,0,9
	.byte	'reserved_17',0,4
	.word	17306
	.byte	15,0,2,35,0,0,6
	.byte	'Ifx_CPU_TPS_CON_Bits',0,6,249,4,3
	.word	25440
	.byte	8
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,6,252,4,16,4,9
	.byte	'Timer',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,6,255,4,3
	.word	25612
	.byte	8
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,6,130,5,16,4,9
	.byte	'ADDR',0,4
	.word	17306
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_TR_ADR_Bits',0,6,133,5,3
	.word	25692
	.byte	8
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,6,136,5,16,4,9
	.byte	'EVTA',0,4
	.word	17306
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	17306
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	17306
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	17306
	.byte	4,20,2,35,0,9
	.byte	'TYP',0,4
	.word	17306
	.byte	1,19,2,35,0,9
	.byte	'RNG',0,4
	.word	17306
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	17306
	.byte	1,17,2,35,0,9
	.byte	'ASI_EN',0,4
	.word	17306
	.byte	1,16,2,35,0,9
	.byte	'ASI',0,4
	.word	17306
	.byte	5,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	17306
	.byte	6,5,2,35,0,9
	.byte	'AST',0,4
	.word	17306
	.byte	1,4,2,35,0,9
	.byte	'ALD',0,4
	.word	17306
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	17306
	.byte	3,0,2,35,0,0,6
	.byte	'Ifx_CPU_TR_EVT_Bits',0,6,153,5,3
	.word	25765
	.byte	8
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,6,156,5,16,4,9
	.byte	'T0',0,4
	.word	17306
	.byte	1,31,2,35,0,9
	.byte	'T1',0,4
	.word	17306
	.byte	1,30,2,35,0,9
	.byte	'T2',0,4
	.word	17306
	.byte	1,29,2,35,0,9
	.byte	'T3',0,4
	.word	17306
	.byte	1,28,2,35,0,9
	.byte	'T4',0,4
	.word	17306
	.byte	1,27,2,35,0,9
	.byte	'T5',0,4
	.word	17306
	.byte	1,26,2,35,0,9
	.byte	'T6',0,4
	.word	17306
	.byte	1,25,2,35,0,9
	.byte	'T7',0,4
	.word	17306
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	17306
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,6,167,5,3
	.word	26083
	.byte	10,6,175,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17285
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_A',0,6,180,5,3
	.word	26278
	.byte	10,6,183,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17362
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_BIV',0,6,188,5,3
	.word	26337
	.byte	10,6,191,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17441
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_BTV',0,6,196,5,3
	.word	26398
	.byte	10,6,199,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17527
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CCNT',0,6,204,5,3
	.word	26459
	.byte	10,6,207,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17616
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CCTRL',0,6,212,5,3
	.word	26521
	.byte	10,6,215,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17762
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_COMPAT',0,6,220,5,3
	.word	26584
	.byte	10,6,223,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17889
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CORE_ID',0,6,228,5,3
	.word	26648
	.byte	10,6,231,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17987
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CPR_L',0,6,236,5,3
	.word	26713
	.byte	10,6,239,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18080
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CPR_U',0,6,244,5,3
	.word	26776
	.byte	10,6,247,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18173
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CPU_ID',0,6,252,5,3
	.word	26839
	.byte	10,6,255,5,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18280
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CPXE',0,6,132,6,3
	.word	26903
	.byte	10,6,135,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18367
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CREVT',0,6,140,6,3
	.word	26965
	.byte	10,6,143,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18521
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CUS_ID',0,6,148,6,3
	.word	27028
	.byte	10,6,151,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18615
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_D',0,6,156,6,3
	.word	27092
	.byte	10,6,159,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18678
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DATR',0,6,164,6,3
	.word	27151
	.byte	10,6,167,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18896
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DBGSR',0,6,172,6,3
	.word	27213
	.byte	10,6,175,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19111
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DBGTCR',0,6,180,6,3
	.word	27276
	.byte	10,6,183,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19205
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DCON0',0,6,188,6,3
	.word	27340
	.byte	10,6,191,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19321
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DCON2',0,6,196,6,3
	.word	27403
	.byte	10,6,199,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19422
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DCX',0,6,204,6,3
	.word	27466
	.byte	10,6,207,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19515
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DEADD',0,6,212,6,3
	.word	27527
	.byte	10,6,215,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19595
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DIEAR',0,6,220,6,3
	.word	27590
	.byte	10,6,223,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19664
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DIETR',0,6,228,6,3
	.word	27653
	.byte	10,6,231,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19893
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DMS',0,6,236,6,3
	.word	27716
	.byte	10,6,239,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19986
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DPR_L',0,6,244,6,3
	.word	27777
	.byte	10,6,247,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20081
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DPR_U',0,6,252,6,3
	.word	27840
	.byte	10,6,255,6,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20176
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DPRE',0,6,132,7,3
	.word	27903
	.byte	10,6,135,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20266
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DPWE',0,6,140,7,3
	.word	27965
	.byte	10,6,143,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20356
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DSTR',0,6,148,7,3
	.word	28027
	.byte	10,6,151,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20680
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_EXEVT',0,6,156,7,3
	.word	28089
	.byte	10,6,159,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20834
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FCX',0,6,164,7,3
	.word	28152
	.byte	10,6,167,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20940
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,6,172,7,3
	.word	28213
	.byte	10,6,175,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21289
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,6,180,7,3
	.word	28283
	.byte	10,6,183,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21449
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,6,188,7,3
	.word	28353
	.byte	10,6,191,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21530
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,6,196,7,3
	.word	28422
	.byte	10,6,199,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21617
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,6,204,7,3
	.word	28493
	.byte	10,6,207,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21704
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,6,212,7,3
	.word	28564
	.byte	10,6,215,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21791
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_ICNT',0,6,220,7,3
	.word	28635
	.byte	10,6,223,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21882
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_ICR',0,6,228,7,3
	.word	28697
	.byte	10,6,231,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22025
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_ISP',0,6,236,7,3
	.word	28758
	.byte	10,6,239,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22091
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_LCX',0,6,244,7,3
	.word	28819
	.byte	10,6,247,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22197
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_M1CNT',0,6,252,7,3
	.word	28880
	.byte	10,6,255,7,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22290
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_M2CNT',0,6,132,8,3
	.word	28943
	.byte	10,6,135,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22383
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_M3CNT',0,6,140,8,3
	.word	29006
	.byte	10,6,143,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22476
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PC',0,6,148,8,3
	.word	29069
	.byte	10,6,151,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22561
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PCON0',0,6,156,8,3
	.word	29129
	.byte	10,6,159,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22677
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PCON1',0,6,164,8,3
	.word	29192
	.byte	10,6,167,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22788
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PCON2',0,6,172,8,3
	.word	29255
	.byte	10,6,175,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22889
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PCXI',0,6,180,8,3
	.word	29318
	.byte	10,6,183,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23019
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PIEAR',0,6,188,8,3
	.word	29380
	.byte	10,6,191,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23088
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PIETR',0,6,196,8,3
	.word	29443
	.byte	10,6,199,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23317
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PMA0',0,6,204,8,3
	.word	29506
	.byte	10,6,207,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23430
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PMA1',0,6,212,8,3
	.word	29568
	.byte	10,6,215,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23543
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PMA2',0,6,220,8,3
	.word	29630
	.byte	10,6,223,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23634
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PSTR',0,6,228,8,3
	.word	29692
	.byte	10,6,231,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23837
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PSW',0,6,236,8,3
	.word	29754
	.byte	10,6,239,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24080
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SEGEN',0,6,244,8,3
	.word	29815
	.byte	10,6,247,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24208
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SMACON',0,6,252,8,3
	.word	29878
	.byte	10,6,255,8,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24449
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_ACCENA',0,6,132,9,3
	.word	29942
	.byte	10,6,135,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24532
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_ACCENB',0,6,140,9,3
	.word	30012
	.byte	10,6,143,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24623
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,6,148,9,3
	.word	30082
	.byte	10,6,151,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24714
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,6,156,9,3
	.word	30156
	.byte	10,6,159,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24813
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,6,164,9,3
	.word	30230
	.byte	10,6,167,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24920
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,6,172,9,3
	.word	30300
	.byte	10,6,175,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25027
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SWEVT',0,6,180,9,3
	.word	30370
	.byte	10,6,183,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25181
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SYSCON',0,6,188,9,3
	.word	30433
	.byte	10,6,191,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25342
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TASK_ASI',0,6,196,9,3
	.word	30497
	.byte	10,6,199,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25440
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TPS_CON',0,6,204,9,3
	.word	30563
	.byte	10,6,207,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25612
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TPS_TIMER',0,6,212,9,3
	.word	30628
	.byte	10,6,215,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25692
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TR_ADR',0,6,220,9,3
	.word	30695
	.byte	10,6,223,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25765
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TR_EVT',0,6,228,9,3
	.word	30759
	.byte	10,6,231,9,9,4,11
	.byte	'U',0,4
	.word	881
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9251
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	26083
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TRIG_ACC',0,6,236,9,3
	.word	30823
	.byte	8
	.byte	'_Ifx_CPU_CPR',0,6,247,9,25,8,11
	.byte	'L',0,4
	.word	26713
	.byte	2,35,0,11
	.byte	'U',0,4
	.word	26776
	.byte	2,35,4,0,3
	.word	30889
	.byte	6
	.byte	'Ifx_CPU_CPR',0,6,251,9,3
	.word	30931
	.byte	8
	.byte	'_Ifx_CPU_DPR',0,6,254,9,25,8,11
	.byte	'L',0,4
	.word	27777
	.byte	2,35,0,11
	.byte	'U',0,4
	.word	27840
	.byte	2,35,4,0,3
	.word	30957
	.byte	6
	.byte	'Ifx_CPU_DPR',0,6,130,10,3
	.word	30999
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN',0,6,133,10,25,16,11
	.byte	'LA',0,4
	.word	30230
	.byte	2,35,0,11
	.byte	'UA',0,4
	.word	30300
	.byte	2,35,4,11
	.byte	'ACCENA',0,4
	.word	30082
	.byte	2,35,8,11
	.byte	'ACCENB',0,4
	.word	30156
	.byte	2,35,12,0,3
	.word	31025
	.byte	6
	.byte	'Ifx_CPU_SPROT_RGN',0,6,139,10,3
	.word	31107
	.byte	8
	.byte	'_Ifx_CPU_TPS',0,6,142,10,25,16,11
	.byte	'CON',0,4
	.word	30563
	.byte	2,35,0,12,12
	.word	30628
	.byte	13,2,0,11
	.byte	'TIMER',0,12
	.word	31171
	.byte	2,35,4,0,3
	.word	31139
	.byte	6
	.byte	'Ifx_CPU_TPS',0,6,146,10,3
	.word	31196
	.byte	8
	.byte	'_Ifx_CPU_TR',0,6,149,10,25,8,11
	.byte	'EVT',0,4
	.word	30759
	.byte	2,35,0,11
	.byte	'ADR',0,4
	.word	30695
	.byte	2,35,4,0,3
	.word	31222
	.byte	6
	.byte	'Ifx_CPU_TR',0,6,153,10,3
	.word	31267
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L14:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,53,0,73,19,0,0,4,59,0,3,8,0
	.byte	0,5,15,0,73,19,0,0,6,22,0,3,8,58,15,59,15,57,15,73,19,0,0,7,21,0,54,15,0,0,8,19,1,3,8,58,15,59,15,57,15
	.byte	11,15,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,23,1,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8
	.byte	11,15,73,19,56,9,0,0,12,1,1,11,15,73,19,0,0,13,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L15:
	.word	.L39-.L38
.L38:
	.half	3
	.word	.L41-.L40
.L40:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'..\\flash\\flsloader\\FlsLoader_Platform.c',0,0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'Platform_Types.h',0,2,0,0
	.byte	'..\\flash\\flsloader\\FlsLoader.h',0,0,0,0
	.byte	'IfxCpu_regdef.h',0,2,0,0,0
.L41:
.L39:
	.sdecl	'.debug_info',debug,cluster('FlsLoader_lCheckOTPWOP')
	.sect	'.debug_info'
.L16:
	.word	329
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'..\\flash\\flsloader\\FlsLoader_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L19,.L18
	.byte	2
	.word	.L12
	.byte	3
	.byte	'FlsLoader_lCheckOTPWOP',0,1,221,7,22
	.word	.L26
	.byte	1,1,1
	.word	.L9,.L27,.L8
	.byte	4
	.word	.L9,.L27
	.byte	5
	.byte	'RetError',0,1,223,7,25
	.word	.L26,.L28
	.byte	5
	.byte	'Fls0Check',0,1,224,7,25
	.word	.L26,.L29
	.byte	5
	.byte	'Fls0WOPCheck',0,1,225,7,25
	.word	.L26,.L30
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FlsLoader_lCheckOTPWOP')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FlsLoader_lCheckOTPWOP')
	.sect	'.debug_line'
.L18:
	.word	.L43-.L42
.L42:
	.half	3
	.word	.L45-.L44
.L44:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\flsloader\\FlsLoader_Platform.c',0,0,0,0,0
.L45:
	.byte	5,41,7,0,5,2
	.word	.L9
	.byte	3,231,7,1,5,12,9
	.half	.L46-.L9
	.byte	3,122,1,5,44,9
	.half	.L35-.L46
	.byte	3,6,1,3,1,1,5,47,9
	.half	.L36-.L35
	.byte	1,5,21,9
	.half	.L37-.L36
	.byte	3,3,1,5,7,9
	.half	.L47-.L37
	.byte	1,5,21,7,9
	.half	.L48-.L47
	.byte	3,1,1,5,14,7,9
	.half	.L2-.L48
	.byte	3,2,1,5,1,9
	.half	.L3-.L2
	.byte	3,3,1,7,9
	.half	.L20-.L3
	.byte	0,1,1
.L43:
	.sdecl	'.debug_ranges',debug,cluster('FlsLoader_lCheckOTPWOP')
	.sect	'.debug_ranges'
.L19:
	.word	-1,.L9,0,.L20-.L9,0,0
	.sdecl	'.debug_info',debug,cluster('FlsLoader_lGetFlashType')
	.sect	'.debug_info'
.L21:
	.word	310
	.half	3
	.word	.L22
	.byte	4,1
	.byte	'..\\flash\\flsloader\\FlsLoader_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L24,.L23
	.byte	2
	.word	.L12
	.byte	3
	.byte	'FlsLoader_lGetFlashType',0,1,145,13,8
	.word	.L26
	.byte	1,1,1
	.word	.L11,.L31,.L10
	.byte	4
	.byte	'TargetAddress',0,1,145,13,54
	.word	.L32,.L33
	.byte	5
	.word	.L11,.L31
	.byte	6
	.byte	'Flash_Type',0,1,147,13,10
	.word	.L26,.L34
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FlsLoader_lGetFlashType')
	.sect	'.debug_abbrev'
.L22:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FlsLoader_lGetFlashType')
	.sect	'.debug_line'
.L23:
	.word	.L50-.L49
.L49:
	.half	3
	.word	.L52-.L51
.L51:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\flsloader\\FlsLoader_Platform.c',0,0,0,0,0
.L52:
	.byte	5,7,7,0,5,2
	.word	.L11
	.byte	3,148,13,1,5,16,9
	.half	.L53-.L11
	.byte	3,4,1,5,1,9
	.half	.L54-.L53
	.byte	3,9,1,7,9
	.half	.L25-.L54
	.byte	0,1,1
.L50:
	.sdecl	'.debug_ranges',debug,cluster('FlsLoader_lGetFlashType')
	.sect	'.debug_ranges'
.L24:
	.word	-1,.L11,0,.L25-.L11,0,0
	.sdecl	'.debug_loc',debug,cluster('FlsLoader_lCheckOTPWOP')
	.sect	'.debug_loc'
.L29:
	.word	-1,.L9,.L36-.L9,.L27-.L9
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L30:
	.word	-1,.L9,.L37-.L9,.L27-.L9
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L8:
	.word	-1,.L9,0,.L27-.L9
	.half	2
	.byte	138,0
	.word	0,0
.L28:
	.word	-1,.L9,.L35-.L9,.L27-.L9
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FlsLoader_lGetFlashType')
	.sect	'.debug_loc'
.L34:
	.word	0,0
.L10:
	.word	-1,.L11,0,.L31-.L11
	.half	2
	.byte	138,0
	.word	0,0
.L33:
	.word	-1,.L11,0,.L31-.L11
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L55:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('FlsLoader_lCheckOTPWOP')
	.sect	'.debug_frame'
	.word	24
	.word	.L55,.L9,.L27-.L9
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FlsLoader_lGetFlashType')
	.sect	'.debug_frame'
	.word	24
	.word	.L55,.L11,.L31-.L11
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\flash\flsloader\FlsLoader_Platform.c	  1699  
; ..\flash\flsloader\FlsLoader_Platform.c	  1700  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1701  ** Syntax           :  FlsLoader_ReturnType                                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1702  **                     FlsLoader_lFlashLockCheck (                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1703  **                                    FlsLoader_AddressType TargetAddress,    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1704  **                                    uint32 FlashType,                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1705  **                                    FlsLoader_LengthType Length)            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1706  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1707  ** Service ID       : NA                                                      **
; ..\flash\flsloader\FlsLoader_Platform.c	  1708  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1709  ** Sync/Async       :  Synchronous                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1710  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1711  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1712  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1713  ** Parameters (in)  : TargetAddress, FlashType, Length                        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1714  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1715  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1716  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1717  ** Return value     : FlsLoader_ReturnType                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1718  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1719  ** Description      : This function will return if the sector is protected    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1720  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1721  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1722  #if (FLSLOADER_ENABLE_LOCKCHECK == STD_ON)
; ..\flash\flsloader\FlsLoader_Platform.c	  1723  FlsLoader_ReturnType FlsLoader_lFlashLockCheck (
; ..\flash\flsloader\FlsLoader_Platform.c	  1724                          FlsLoader_AddressType TargetAddress,uint32 FlashType,
; ..\flash\flsloader\FlsLoader_Platform.c	  1725                          FlsLoader_LengthType Length)
; ..\flash\flsloader\FlsLoader_Platform.c	  1726  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1727    volatile uint32                FlsFpro;
; ..\flash\flsloader\FlsLoader_Platform.c	  1728    volatile uint32                FlsFprod;
; ..\flash\flsloader\FlsLoader_Platform.c	  1729    volatile uint32                FlsProconp1;
; ..\flash\flsloader\FlsLoader_Platform.c	  1730    volatile uint32                FlsProcond1;
; ..\flash\flsloader\FlsLoader_Platform.c	  1731    volatile uint32                FlsProcondrpro1;
; ..\flash\flsloader\FlsLoader_Platform.c	  1732    FlsLoader_ReturnType           RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1733    
; ..\flash\flsloader\FlsLoader_Platform.c	  1734    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1735    
; ..\flash\flsloader\FlsLoader_Platform.c	  1736    if (FlashType != FLSLOADER_DFLASH_BANK0)
; ..\flash\flsloader\FlsLoader_Platform.c	  1737    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1738      #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	  1739      FlsFpro = TestFlsloader_DebugMask06;
; ..\flash\flsloader\FlsLoader_Platform.c	  1740      #else
; ..\flash\flsloader\FlsLoader_Platform.c	  1741      FlsFpro = FLASH0_FPRO.B.PRODISP;
; ..\flash\flsloader\FlsLoader_Platform.c	  1742      #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  1743      if (FlsFpro != 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1744      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1745        #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	  1746        FlsProconp1 = TestFlsloader_DebugMask09;
; ..\flash\flsloader\FlsLoader_Platform.c	  1747        #else
; ..\flash\flsloader\FlsLoader_Platform.c	  1748        FlsProconp1 = FLASH0_PROCONP0.B.RPRO;
; ..\flash\flsloader\FlsLoader_Platform.c	  1749        #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  1750        if (FlsProconp1 == 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1751        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1752          RetError = FLSLOADER_E_LOCKED;
; ..\flash\flsloader\FlsLoader_Platform.c	  1753        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1754        else
; ..\flash\flsloader\FlsLoader_Platform.c	  1755        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1756          RetError = FlsLoader_lPFlashSectorCheck(TargetAddress,Length);
; ..\flash\flsloader\FlsLoader_Platform.c	  1757        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1758      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1759    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1760    else
; ..\flash\flsloader\FlsLoader_Platform.c	  1761    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1762      /*Check if global write protection is installed in DFLASH*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1763      #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	  1764      FlsFprod =  TestFlsloader_DebugMask09;
; ..\flash\flsloader\FlsLoader_Platform.c	  1765      FlsProcond1 =  TestFlsloader_DebugMask06;
; ..\flash\flsloader\FlsLoader_Platform.c	  1766      FlsProcondrpro1 = TestFlsloader_DebugMask06;
; ..\flash\flsloader\FlsLoader_Platform.c	  1767      #else
; ..\flash\flsloader\FlsLoader_Platform.c	  1768      FlsFprod = FLASH0_FPRO.B.PRODISD;
; ..\flash\flsloader\FlsLoader_Platform.c	  1769      FlsProcond1 = FLASH0_PROCOND.B.L;
; ..\flash\flsloader\FlsLoader_Platform.c	  1770      FlsProcondrpro1 = FLASH0_PROCOND.B.RPRO;
; ..\flash\flsloader\FlsLoader_Platform.c	  1771      #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  1772      if(FlsFprod != 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1773      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1774        if(FlsProcond1 == 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1775        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1776          RetError = FLSLOADER_E_LOCKED;
; ..\flash\flsloader\FlsLoader_Platform.c	  1777        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1778        if(FlsProcondrpro1 == 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1779        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1780          RetError = FLSLOADER_E_LOCKED;
; ..\flash\flsloader\FlsLoader_Platform.c	  1781        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1782      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1783    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1784    return RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1785  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1786  
; ..\flash\flsloader\FlsLoader_Platform.c	  1787  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1788  ** Syntax           :  FlsLoader_ReturnType                                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1789  **                     FlsLoader_lPFlashSectorCheck (                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1790  **                                       FlsLoader_AddressType TargetAddress, **
; ..\flash\flsloader\FlsLoader_Platform.c	  1791  **                                       FlsLoader_LengthType Length)         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1792  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1793  ** Service ID       : NA                                                      **
; ..\flash\flsloader\FlsLoader_Platform.c	  1794  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1795  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1796  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1797  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1798  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1799  ** Parameters (in)  : TargetAddress, Length                                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1800  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1801  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1802  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1803  ** Return value     : FlsLoader_ReturnType                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1804  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1805  ** Description      : This function will return if the sector is protected    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1806  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1807  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1808  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lPFlashSectorCheck (
; ..\flash\flsloader\FlsLoader_Platform.c	  1809                            FlsLoader_AddressType TargetAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	  1810                            FlsLoader_LengthType Length)
; ..\flash\flsloader\FlsLoader_Platform.c	  1811  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1812    
; ..\flash\flsloader\FlsLoader_Platform.c	  1813    uint32                SectorNumber;
; ..\flash\flsloader\FlsLoader_Platform.c	  1814    uint32                SectorNumber1;
; ..\flash\flsloader\FlsLoader_Platform.c	  1815    uint32                NoOfSectors;
; ..\flash\flsloader\FlsLoader_Platform.c	  1816    uint32                SectorMask;
; ..\flash\flsloader\FlsLoader_Platform.c	  1817    uint32                Offset;
; ..\flash\flsloader\FlsLoader_Platform.c	  1818    uint32                Offset1;
; ..\flash\flsloader\FlsLoader_Platform.c	  1819    volatile uint32       FlsRegProconp0;
; ..\flash\flsloader\FlsLoader_Platform.c	  1820    FlsLoader_ReturnType  RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1821    
; ..\flash\flsloader\FlsLoader_Platform.c	  1822    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1823    #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	  1824    FlsRegProconp0 = TestFlsloader_DebugMask10;
; ..\flash\flsloader\FlsLoader_Platform.c	  1825    #else
; ..\flash\flsloader\FlsLoader_Platform.c	  1826    FlsRegProconp0 = FLASH0_PROCONP0.U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1827    #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  1828  
; ..\flash\flsloader\FlsLoader_Platform.c	  1829    /*IFX_MISRA_RULE_01_02_STATUS=TargetAddress is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1830    type volatile uint32 to avoid compiler optimization in command 
; ..\flash\flsloader\FlsLoader_Platform.c	  1831    cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1832    Offset = TargetAddress - FLSLOADER_PFLASH0_START_ADDRESS;
; ..\flash\flsloader\FlsLoader_Platform.c	  1833    for(SectorNumber = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1834        SectorNumber < FLSLOADER_NUM_OF_PF0_SECTORS;
; ..\flash\flsloader\FlsLoader_Platform.c	  1835        SectorNumber++)
; ..\flash\flsloader\FlsLoader_Platform.c	  1836    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1837      if(Offset < FlsLoader_PFlashSectorOffset[SectorNumber])
; ..\flash\flsloader\FlsLoader_Platform.c	  1838      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1839        break;
; ..\flash\flsloader\FlsLoader_Platform.c	  1840      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1841    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1842    SectorNumber--;
; ..\flash\flsloader\FlsLoader_Platform.c	  1843    TargetAddress = TargetAddress + Length ;
; ..\flash\flsloader\FlsLoader_Platform.c	  1844    /*IFX_MISRA_RULE_01_02_STATUS=TargetAddress is defined of 
; ..\flash\flsloader\FlsLoader_Platform.c	  1845    type volatile uint32 to avoid compiler optimization in command 
; ..\flash\flsloader\FlsLoader_Platform.c	  1846    cycles*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1847    Offset1 = TargetAddress - FLSLOADER_PFLASH0_START_ADDRESS;
; ..\flash\flsloader\FlsLoader_Platform.c	  1848    for(SectorNumber1 = 0U;
; ..\flash\flsloader\FlsLoader_Platform.c	  1849        SectorNumber1 < FLSLOADER_NUM_OF_PF0_SECTORS;
; ..\flash\flsloader\FlsLoader_Platform.c	  1850        SectorNumber1++)
; ..\flash\flsloader\FlsLoader_Platform.c	  1851    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1852      if(Offset1 < FlsLoader_PFlashSectorOffset[SectorNumber1])
; ..\flash\flsloader\FlsLoader_Platform.c	  1853      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1854        break;
; ..\flash\flsloader\FlsLoader_Platform.c	  1855      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1856    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1857    NoOfSectors = SectorNumber1 - SectorNumber;
; ..\flash\flsloader\FlsLoader_Platform.c	  1858    SectorMask = (((uint32)1U << NoOfSectors) - (uint32)1U) << SectorNumber;
; ..\flash\flsloader\FlsLoader_Platform.c	  1859    if((SectorMask & FlsRegProconp0) != 0U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1860    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1861      RetError = FLSLOADER_E_LOCKED;
; ..\flash\flsloader\FlsLoader_Platform.c	  1862    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1863    return RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1864  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1865  
; ..\flash\flsloader\FlsLoader_Platform.c	  1866  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1867  ** Syntax           :  FlsLoader_ReturnType                                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1868  **                     FlsLoader_lEraseLockCheck (                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1869  **                                    FlsLoader_AddressType TargetAddress,    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1870  **                                    uint32 FlashType,                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1871  **                                    FlsLoader_LengthType Length)            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1872  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1873  ** Service ID       : NA                                                      **
; ..\flash\flsloader\FlsLoader_Platform.c	  1874  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1875  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1876  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1877  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1878  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1879  ** Parameters (in)  : TargetAddress, FlashType, Length                        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1880  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1881  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1882  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1883  ** Return value     : FlsLoader_ReturnType                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1884  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1885  ** Description      : This function will return if the sector is protected    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1886  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1887  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1888  FlsLoader_ReturnType FlsLoader_lEraseLockCheck (
; ..\flash\flsloader\FlsLoader_Platform.c	  1889                            FlsLoader_AddressType TargetAddress,uint32 FlashType,
; ..\flash\flsloader\FlsLoader_Platform.c	  1890                            FlsLoader_LengthType Length)
; ..\flash\flsloader\FlsLoader_Platform.c	  1891  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1892    volatile uint32                FlsFproD;
; ..\flash\flsloader\FlsLoader_Platform.c	  1893    volatile uint32                FlsProcond2;
; ..\flash\flsloader\FlsLoader_Platform.c	  1894    volatile uint32                FlsProcondrpro2;
; ..\flash\flsloader\FlsLoader_Platform.c	  1895    volatile uint32                FlsFpro;
; ..\flash\flsloader\FlsLoader_Platform.c	  1896    volatile uint32                FlsProconp2;
; ..\flash\flsloader\FlsLoader_Platform.c	  1897    FlsLoader_ReturnType           RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1898    
; ..\flash\flsloader\FlsLoader_Platform.c	  1899    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  1900    /* Given Address belongs to Data FLASH */
; ..\flash\flsloader\FlsLoader_Platform.c	  1901    /*Check if the Target Address passed is UCB protected */
; ..\flash\flsloader\FlsLoader_Platform.c	  1902    /* Check if global write protection is installed in DFLASH*/
; ..\flash\flsloader\FlsLoader_Platform.c	  1903    if (FlashType == FLSLOADER_DFLASH_BANK0)
; ..\flash\flsloader\FlsLoader_Platform.c	  1904    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1905      #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	  1906      FlsFproD =  TestFlsloader_DebugMask09;
; ..\flash\flsloader\FlsLoader_Platform.c	  1907      FlsProcond2 =  TestFlsloader_DebugMask06;
; ..\flash\flsloader\FlsLoader_Platform.c	  1908      FlsProcondrpro2 =  TestFlsloader_DebugMask06;
; ..\flash\flsloader\FlsLoader_Platform.c	  1909      #else
; ..\flash\flsloader\FlsLoader_Platform.c	  1910      FlsFproD = FLASH0_FPRO.B.PRODISD;
; ..\flash\flsloader\FlsLoader_Platform.c	  1911      FlsProcond2 = FLASH0_PROCOND.B.L;
; ..\flash\flsloader\FlsLoader_Platform.c	  1912      FlsProcondrpro2 = FLASH0_PROCOND.B.RPRO;
; ..\flash\flsloader\FlsLoader_Platform.c	  1913      #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  1914      if (FlsFproD != 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1915      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1916        if(FlsProcond2 == 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1917        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1918          RetError = FLSLOADER_E_LOCKED;
; ..\flash\flsloader\FlsLoader_Platform.c	  1919        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1920        if(FlsProcondrpro2 == 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1921        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1922          RetError = FLSLOADER_E_LOCKED;
; ..\flash\flsloader\FlsLoader_Platform.c	  1923        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1924      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1925    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1926    else
; ..\flash\flsloader\FlsLoader_Platform.c	  1927    {
; ..\flash\flsloader\FlsLoader_Platform.c	  1928      /*Check if the Target Address passed is UCB protected */
; ..\flash\flsloader\FlsLoader_Platform.c	  1929      /* Protection Check Algorithm */
; ..\flash\flsloader\FlsLoader_Platform.c	  1930      /* Preprocessor switch to enable/disable the lockcheck functionality */
; ..\flash\flsloader\FlsLoader_Platform.c	  1931      /* Check if global write protection is installed */
; ..\flash\flsloader\FlsLoader_Platform.c	  1932      #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	  1933      FlsFpro =  TestFlsloader_DebugMask09;
; ..\flash\flsloader\FlsLoader_Platform.c	  1934      #else
; ..\flash\flsloader\FlsLoader_Platform.c	  1935      FlsFpro = FLASH0_FPRO.B.PRODISP;
; ..\flash\flsloader\FlsLoader_Platform.c	  1936      #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  1937      if(FlsFpro != 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1938      {
; ..\flash\flsloader\FlsLoader_Platform.c	  1939        #ifdef  IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	  1940        FlsProconp2 =  TestFlsloader_DebugMask06;
; ..\flash\flsloader\FlsLoader_Platform.c	  1941        #else
; ..\flash\flsloader\FlsLoader_Platform.c	  1942        FlsProconp2 = FLASH0_PROCONP0.B.RPRO;
; ..\flash\flsloader\FlsLoader_Platform.c	  1943        #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  1944        if (FlsProconp2 == 1U)
; ..\flash\flsloader\FlsLoader_Platform.c	  1945        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1946          RetError = FLSLOADER_E_LOCKED;
; ..\flash\flsloader\FlsLoader_Platform.c	  1947        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1948        else
; ..\flash\flsloader\FlsLoader_Platform.c	  1949        {
; ..\flash\flsloader\FlsLoader_Platform.c	  1950          RetError = FlsLoader_lCheckSectorProt(TargetAddress, Length);
; ..\flash\flsloader\FlsLoader_Platform.c	  1951        }
; ..\flash\flsloader\FlsLoader_Platform.c	  1952      }
; ..\flash\flsloader\FlsLoader_Platform.c	  1953    }
; ..\flash\flsloader\FlsLoader_Platform.c	  1954    return RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1955  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1956  
; ..\flash\flsloader\FlsLoader_Platform.c	  1957  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1958  ** Syntax  :          FlsLoader_ReturnType FlsLoader_lCheckSectorProt(        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1959  **                      uint32 TargetAddress, FlsLoader_LengthType Length)    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1960  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1961  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1962  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1963  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1964  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  1965  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1966  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1967  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1968  ** Parameters(in)   : TargetAddress, Length                                   **
; ..\flash\flsloader\FlsLoader_Platform.c	  1969  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1970  ** Parameters (out) : none                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1971  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1972  ** Return value     : FlsLoader_ReturnType                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1973  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1974  ** Description      : This function will return if the sector is protected    **
; ..\flash\flsloader\FlsLoader_Platform.c	  1975  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  1976  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lCheckSectorProt(
; ..\flash\flsloader\FlsLoader_Platform.c	  1977                                                   uint32 TargetAddress,
; ..\flash\flsloader\FlsLoader_Platform.c	  1978                                                   FlsLoader_LengthType Length )
; ..\flash\flsloader\FlsLoader_Platform.c	  1979  {
; ..\flash\flsloader\FlsLoader_Platform.c	  1980    FlsLoader_ReturnType RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1981    uint32 SectorNumber;
; ..\flash\flsloader\FlsLoader_Platform.c	  1982    uint32 Offset;
; ..\flash\flsloader\FlsLoader_Platform.c	  1983  
; ..\flash\flsloader\FlsLoader_Platform.c	  1984    Offset = TargetAddress - FLSLOADER_PFLASH0_START_ADDRESS;
; ..\flash\flsloader\FlsLoader_Platform.c	  1985    SectorNumber = FlsLoader_lSectorNumber(Offset);
; ..\flash\flsloader\FlsLoader_Platform.c	  1986    /* Check if any of the 26 sectors of PF0 are locked for
; ..\flash\flsloader\FlsLoader_Platform.c	  1987    write/OTP/WOP protection */
; ..\flash\flsloader\FlsLoader_Platform.c	  1988    RetError = FlsLoader_lSectorProtCheck(SectorNumber, Length);
; ..\flash\flsloader\FlsLoader_Platform.c	  1989  
; ..\flash\flsloader\FlsLoader_Platform.c	  1990    return RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  1991  }
; ..\flash\flsloader\FlsLoader_Platform.c	  1992  
; ..\flash\flsloader\FlsLoader_Platform.c	  1993  /*******************************************************************************
; ..\flash\flsloader\FlsLoader_Platform.c	  1994  ** Syntax           : FlsLoader_ReturnType FlsLoader_lSectorProtCheck(        **
; ..\flash\flsloader\FlsLoader_Platform.c	  1995  **                      uint32 SectorNumber,                                  **
; ..\flash\flsloader\FlsLoader_Platform.c	  1996  **                      FlsLoader_LengthType Length )                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  1997  **                    )                                                       **
; ..\flash\flsloader\FlsLoader_Platform.c	  1998  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  1999  ** Service ID       : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  2000  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  2001  ** Sync/Async       : Synchronous                                             **
; ..\flash\flsloader\FlsLoader_Platform.c	  2002  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  2003  ** Reentrancy       : non - reentrant                                         **
; ..\flash\flsloader\FlsLoader_Platform.c	  2004  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  2005  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  2006  ** Parameters(in)   : SectorNumber, Length                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  2007  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  2008  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  2009  ** Parameters (out) : None                                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  2010  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  2011  ** Return value     : FlsLoader_ReturnType                                    **
; ..\flash\flsloader\FlsLoader_Platform.c	  2012  **                                                                            **
; ..\flash\flsloader\FlsLoader_Platform.c	  2013  ** Description      : This function returns sector locked or not.             **
; ..\flash\flsloader\FlsLoader_Platform.c	  2014  *******************************************************************************/
; ..\flash\flsloader\FlsLoader_Platform.c	  2015  IFX_LOCAL_INLINE FlsLoader_ReturnType FlsLoader_lSectorProtCheck(
; ..\flash\flsloader\FlsLoader_Platform.c	  2016                                  uint32 SectorNumber,FlsLoader_LengthType Length)
; ..\flash\flsloader\FlsLoader_Platform.c	  2017  {
; ..\flash\flsloader\FlsLoader_Platform.c	  2018    FlsLoader_ReturnType RetError;
; ..\flash\flsloader\FlsLoader_Platform.c	  2019    uint32               SectorMask;
; ..\flash\flsloader\FlsLoader_Platform.c	  2020    uint32               FlsRegProconp0;
; ..\flash\flsloader\FlsLoader_Platform.c	  2021    uint32               FlsRegProconWop0;
; ..\flash\flsloader\FlsLoader_Platform.c	  2022    uint32               FlsRegProconOtp0;
; ..\flash\flsloader\FlsLoader_Platform.c	  2023  
; ..\flash\flsloader\FlsLoader_Platform.c	  2024    /* Init RetError to OK */
; ..\flash\flsloader\FlsLoader_Platform.c	  2025    RetError = FLSLOADER_E_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  2026  
; ..\flash\flsloader\FlsLoader_Platform.c	  2027    if(SectorNumber != FLSLOADER_INV)
; ..\flash\flsloader\FlsLoader_Platform.c	  2028    {
; ..\flash\flsloader\FlsLoader_Platform.c	  2029      #ifdef IFX_FLSLOADER_DEBUG
; ..\flash\flsloader\FlsLoader_Platform.c	  2030      FlsRegProconp0 = TestFlsloader_DebugMask10;
; ..\flash\flsloader\FlsLoader_Platform.c	  2031      FlsRegProconWop0 = TestFlsloader_DebugMask10;
; ..\flash\flsloader\FlsLoader_Platform.c	  2032      FlsRegProconOtp0 = TestFlsloader_DebugMask10;
; ..\flash\flsloader\FlsLoader_Platform.c	  2033      #else
; ..\flash\flsloader\FlsLoader_Platform.c	  2034      FlsRegProconp0 = FLASH0_PROCONP0.U;
; ..\flash\flsloader\FlsLoader_Platform.c	  2035      FlsRegProconWop0 = FLASH0_PROCONWOP0.U;
; ..\flash\flsloader\FlsLoader_Platform.c	  2036      FlsRegProconOtp0 = FLASH0_PROCONOTP0.U;
; ..\flash\flsloader\FlsLoader_Platform.c	  2037  
; ..\flash\flsloader\FlsLoader_Platform.c	  2038      #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  2039        
; ..\flash\flsloader\FlsLoader_Platform.c	  2040      SectorMask = (((uint32)1U << Length) - (uint32)1U) << SectorNumber;
; ..\flash\flsloader\FlsLoader_Platform.c	  2041  
; ..\flash\flsloader\FlsLoader_Platform.c	  2042      if (((FlsRegProconp0   & SectorMask) != 0U) ||
; ..\flash\flsloader\FlsLoader_Platform.c	  2043          ((FlsRegProconOtp0 & SectorMask) != 0U) ||
; ..\flash\flsloader\FlsLoader_Platform.c	  2044          ((FlsRegProconWop0 & SectorMask) != 0U))
; ..\flash\flsloader\FlsLoader_Platform.c	  2045      {
; ..\flash\flsloader\FlsLoader_Platform.c	  2046        RetError = FLSLOADER_E_LOCKED;
; ..\flash\flsloader\FlsLoader_Platform.c	  2047      }
; ..\flash\flsloader\FlsLoader_Platform.c	  2048    }
; ..\flash\flsloader\FlsLoader_Platform.c	  2049    else
; ..\flash\flsloader\FlsLoader_Platform.c	  2050    {
; ..\flash\flsloader\FlsLoader_Platform.c	  2051      RetError = FLSLOADER_E_NOT_OK;
; ..\flash\flsloader\FlsLoader_Platform.c	  2052    }
; ..\flash\flsloader\FlsLoader_Platform.c	  2053    
; ..\flash\flsloader\FlsLoader_Platform.c	  2054    return (RetError);
; ..\flash\flsloader\FlsLoader_Platform.c	  2055  }
; ..\flash\flsloader\FlsLoader_Platform.c	  2056  
; ..\flash\flsloader\FlsLoader_Platform.c	  2057  #endif
; ..\flash\flsloader\FlsLoader_Platform.c	  2058  
; ..\flash\flsloader\FlsLoader_Platform.c	  2059  #define FLSLOADER_STOP_SEC_WRITE_CODE
; ..\flash\flsloader\FlsLoader_Platform.c	  2060    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives 
; ..\flash\flsloader\FlsLoader_Platform.c	  2061    is allowed only for MemMap.h*/
; ..\flash\flsloader\FlsLoader_Platform.c	  2062  #include "MemMap.h"
; ..\flash\flsloader\FlsLoader_Platform.c	  2063  

	; Module end
