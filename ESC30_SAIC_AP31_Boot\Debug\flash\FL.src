	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc30372a -c99 --dep-file=flash\\.FL.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=flash\\FL.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o flash\\FL.src ..\\flash\\FL.c"
	.compiler_name		"ctc"
	.name	"FL"

	
$TC16X
	
	.sdecl	'.text.FL.FL_KeepNvmInforNoUsed',code,cluster('FL_KeepNvmInforNoUsed')
	.sect	'.text.FL.FL_KeepNvmInforNoUsed'
	.align	2
	
	.global	FL_KeepNvmInforNoUsed

; ..\flash\FL.c	     1  /*============================================================================*/
; ..\flash\FL.c	     2  /** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
; ..\flash\FL.c	     3   *  
; ..\flash\FL.c	     4   *  All rights reserved. This software is iSOFT property. Duplication 
; ..\flash\FL.c	     5   *  or disclosure without iSOFT written authorization is prohibited.
; ..\flash\FL.c	     6   *  
; ..\flash\FL.c	     7   *  @file       <FL.c>
; ..\flash\FL.c	     8   *  @brief      <His Flash Loader >
; ..\flash\FL.c	     9   *  
; ..\flash\FL.c	    10   *  < The Code process checksum,erase and program for bootloader project>
; ..\flash\FL.c	    11   *
; ..\flash\FL.c	    12   *  <Compiler: CodeWarrior    MCU:9S12G64>
; ..\flash\FL.c	    13   *
; ..\flash\FL.c	    14   *  <AUTHOR>
; ..\flash\FL.c	    15   *  @date       <2013-09-13>
; ..\flash\FL.c	    16   */
; ..\flash\FL.c	    17  /*============================================================================*/
; ..\flash\FL.c	    18  
; ..\flash\FL.c	    19  /*=======[R E V I S I O N   H I S T O R Y]====================================*/
; ..\flash\FL.c	    20  /** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
; ..\flash\FL.c	    21   *  V1.0    20121227    Gary       Initial version
; ..\flash\FL.c	    22   *
; ..\flash\FL.c	    23   *  V1.1    20130913    ccl        update
; ..\flash\FL.c	    24   *  V1.2    20190520    锟斤拷锟斤拷锟斤拷               锟斤拷锟斤拷DID AFFC锟斤拷AFFD锟斤拷AFFE锟斤拷AFFF锟侥讹拷锟斤拷锟斤拷
; ..\flash\FL.c	    25   */
; ..\flash\FL.c	    26  /*============================================================================*/
; ..\flash\FL.c	    27  
; ..\flash\FL.c	    28  /*=======[I N C L U D E S]====================================================*/
; ..\flash\FL.c	    29  #include "FL.h"
; ..\flash\FL.c	    30  #include "Fls.h"
; ..\flash\FL.c	    31  #include "Mcal_Compiler.h"
; ..\flash\FL.c	    32  #include "Appl.h"
; ..\flash\FL.c	    33  #include "Ext_Fls.h"
; ..\flash\FL.c	    34  #include "ICM_Bootloader_Version.h"
; ..\flash\FL.c	    35  #include "Secure.h"
; ..\flash\FL.c	    36  #include "Vss.h"
; ..\flash\FL.c	    37  //#include "Wdg.h"
; ..\flash\FL.c	    38  //#include "Nvm_Did_Cfg.h"
; ..\flash\FL.c	    39  #include "eeprom_Cfg.h"
; ..\flash\FL.c	    40  /*=======[M A C R O S]========================================================*/
; ..\flash\FL.c	    41  #define BL_USE_COMBINATION_SWITCH       STD_OFF
; ..\flash\FL.c	    42  
; ..\flash\FL.c	    43  /** maxmun length of program buffer */
; ..\flash\FL.c	    44  #define FL_PROGRAM_SIZE (0x100uL)
; ..\flash\FL.c	    45  #define SYS_SUP_LENGTH          6u
; ..\flash\FL.c	    46  #define HW_VER_LENGTH           6u
; ..\flash\FL.c	    47  #define SW_VER_LENGTH           6u
; ..\flash\FL.c	    48  #define MD_VER_LENGTH           6u
; ..\flash\FL.c	    49  #define SAIC_VER_LENGTH           6u
; ..\flash\FL.c	    50  
; ..\flash\FL.c	    51  #define VIN_VER_LENGTH           17u
; ..\flash\FL.c	    52  #define VEH_VER_LENGTH          20u
; ..\flash\FL.c	    53  
; ..\flash\FL.c	    54  #define HEADER_TYPE_APP         1
; ..\flash\FL.c	    55  /*=======[T Y P E   D E F I N I T I O N S]====================================*/
; ..\flash\FL.c	    56  /** flashloader job status */
; ..\flash\FL.c	    57  typedef enum
; ..\flash\FL.c	    58  {
; ..\flash\FL.c	    59      FL_JOB_IDLE,
; ..\flash\FL.c	    60      
; ..\flash\FL.c	    61      FL_JOB_ERASING,
; ..\flash\FL.c	    62      
; ..\flash\FL.c	    63      FL_JOB_PROGRAMMING,
; ..\flash\FL.c	    64      
; ..\flash\FL.c	    65      FL_JOB_CHECKING,//锟斤拷锟斤拷锟斤拷校锟斤拷
; ..\flash\FL.c	    66  	FL_JOB_S37CHECKING,
; ..\flash\FL.c	    67  	FL_JOB_COMPATIBLE,
; ..\flash\FL.c	    68  
; ..\flash\FL.c	    69  } FL_ActiveJobType;
; ..\flash\FL.c	    70  
; ..\flash\FL.c	    71  /** flashloader download step */
; ..\flash\FL.c	    72  typedef enum
; ..\flash\FL.c	    73  {
; ..\flash\FL.c	    74      FL_REQUEST_STEP,
; ..\flash\FL.c	    75  
; ..\flash\FL.c	    76      FL_HEADER_TRASNFER_STEP,
; ..\flash\FL.c	    77  
; ..\flash\FL.c	    78      FL_HEADER_EXIT_TRANSFER_STEP,
; ..\flash\FL.c	    79      
; ..\flash\FL.c	    80      FL_TRANSFER_STEP,
; ..\flash\FL.c	    81      
; ..\flash\FL.c	    82      FL_EXIT_TRANSFER_STEP,
; ..\flash\FL.c	    83      
; ..\flash\FL.c	    84      FL_CHECKSUM_STEP
; ..\flash\FL.c	    85  
; ..\flash\FL.c	    86  } FL_DownloadStepType;
; ..\flash\FL.c	    87  
; ..\flash\FL.c	    88  /** flashloader status infomation */
; ..\flash\FL.c	    89  typedef struct
; ..\flash\FL.c	    90  {
; ..\flash\FL.c	    91      /* flag if fingerprint has written */
; ..\flash\FL.c	    92      boolean fingerPrintWritten;
; ..\flash\FL.c	    93      
; ..\flash\FL.c	    94      /* flag if fingerprint buffer */
; ..\flash\FL.c	    95      uint8 fingerPrint[FL_FINGER_PRINT_LENGTH];
; ..\flash\FL.c	    96      
; ..\flash\FL.c	    97      /* flag if flash driver has downloaded */
; ..\flash\FL.c	    98      boolean flDrvDownloaded;
; ..\flash\FL.c	    99      
; ..\flash\FL.c	   100      /* error code for flash active job */
; ..\flash\FL.c	   101      FL_ResultType errorCode;
; ..\flash\FL.c	   102      
; ..\flash\FL.c	   103      /* flag if current block is erased */
; ..\flash\FL.c	   104      boolean blockErased;
; ..\flash\FL.c	   105  
; ..\flash\FL.c	   106      /* current process block index */
; ..\flash\FL.c	   107      uint8 blockIndex;
; ..\flash\FL.c	   108      
; ..\flash\FL.c	   109      /* current procees start address */
; ..\flash\FL.c	   110      uint32 startAddr;
; ..\flash\FL.c	   111      
; ..\flash\FL.c	   112      /* current procees length */
; ..\flash\FL.c	   113      uint32 length;
; ..\flash\FL.c	   114      
; ..\flash\FL.c	   115      /* current procees buffer point,point to buffer supplied from DCM */
; ..\flash\FL.c	   116      const uint8 *dataBuff;
; ..\flash\FL.c	   117      
; ..\flash\FL.c	   118      /* segment list of current procees block */
; ..\flash\FL.c	   119      FL_SegmentListType segmentList[FL_NUM_LOGICAL_BLOCKS];
; ..\flash\FL.c	   120      
; ..\flash\FL.c	   121      /* flashloader download step */
; ..\flash\FL.c	   122      FL_DownloadStepType downloadStep;
; ..\flash\FL.c	   123      
; ..\flash\FL.c	   124      /* current job status */
; ..\flash\FL.c	   125      FL_ActiveJobType activeJob;
; ..\flash\FL.c	   126  
; ..\flash\FL.c	   127  } FL_DownloadStateType;
; ..\flash\FL.c	   128  
; ..\flash\FL.c	   129  
; ..\flash\FL.c	   130  
; ..\flash\FL.c	   131  /*handle the two segment in one page*/
; ..\flash\FL.c	   132  typedef struct
; ..\flash\FL.c	   133  {
; ..\flash\FL.c	   134      /* current procees start address */
; ..\flash\FL.c	   135      uint32 remainAddr;
; ..\flash\FL.c	   136      
; ..\flash\FL.c	   137      /* current procees length */
; ..\flash\FL.c	   138      uint32 remainLength;
; ..\flash\FL.c	   139  } FL_RemainDataType;
; ..\flash\FL.c	   140  
; ..\flash\FL.c	   141  STATIC uint8 FBL_HeaderType;
; ..\flash\FL.c	   142  STATIC uint8 FBL_DownloadFlags[1];
; ..\flash\FL.c	   143  STATIC uint8 FBL_SignVerifFlags[1];
; ..\flash\FL.c	   144  uint8 fblFileIndex = 0;
; ..\flash\FL.c	   145  extern uint8 SecurityErrorFlag;
; ..\flash\FL.c	   146  /*=======[E X T E R N A L   D A T A]==========================================*/
; ..\flash\FL.c	   147  /** NVM infomation witch include bootloader infomation,read from EEPROM */
; ..\flash\FL.c	   148  FL_NvmInfoType FL_NvmInfo;
; ..\flash\FL.c	   149  /*=======[I N T E R N A L   D A T A]==========================================*/
; ..\flash\FL.c	   150  /*save the data which not aligned*/
; ..\flash\FL.c	   151  STATIC FL_RemainDataType FL_RemainDataStruct;
; ..\flash\FL.c	   152  /** flashloader status infomation */
; ..\flash\FL.c	   153  STATIC FL_DownloadStateType FldownloadStatus;
; ..\flash\FL.c	   154  /** flashloader program buffer */
; ..\flash\FL.c	   155  STATIC uint8 FlProgramData[FL_PROGRAM_SIZE];
; ..\flash\FL.c	   156  /** flashloader program length */
; ..\flash\FL.c	   157  STATIC uint32 FlProgramLength;
; ..\flash\FL.c	   158  /** flash driver API input parameter */
; ..\flash\FL.c	   159  STATIC tFlashParam flashParamInfo =
; ..\flash\FL.c	   160      {
; ..\flash\FL.c	   161          FLASH_DRIVER_VERSION_PATCH,
; ..\flash\FL.c	   162          FLASH_DRIVER_VERSION_MINOR,
; ..\flash\FL.c	   163          FLASH_DRIVER_VERSION_MAJOR,
; ..\flash\FL.c	   164          0x00u,
; ..\flash\FL.c	   165          kFlashOk,
; ..\flash\FL.c	   166          0x0000u,
; ..\flash\FL.c	   167          0x00000000uL,
; ..\flash\FL.c	   168          0x00000000uL,
; ..\flash\FL.c	   169          NULL_PTR,
; ..\flash\FL.c	   170          &Appl_UpdateTriggerCondition,
; ..\flash\FL.c	   171      };
; ..\flash\FL.c	   172  STATIC boolean FlIntegrityChkIsHash = TRUE;
; ..\flash\FL.c	   173  /* ECU Bootloader Version Number Data Identifier, ASCII code of BL:x.y.z*/
; ..\flash\FL.c	   174  //const uint8 BLVer[MD_VER_LENGTH] = {0x42u, 0x4Cu, 0x3Au, 0x61u, 0x2Eu, 0x31u};
; ..\flash\FL.c	   175  /*
; ..\flash\FL.c	   176   #pragma section ".sersys_CODE" aw
; ..\flash\FL.c	   177   const uint8 DIDTemp[DID_BYTE_LENGTH] = {	0xF0u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u,
; ..\flash\FL.c	   178  											0xF1u, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0x00u, 0x00u, 0x00u,
; ..\flash\FL.c	   179  											0xF2u, 0xF2u, 0xFFu, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u,
; ..\flash\FL.c	   180  											0xF3u, 0xF2u, 0xF3u, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu,
; ..\flash\FL.c	   181  											0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0xFFu,
; ..\flash\FL.c	   182  											0xF4u, 0xF2u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u,
; ..\flash\FL.c	   183  											0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u,
; ..\flash\FL.c	   184  											0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u, 0x00u,
; ..\flash\FL.c	   185  											0xF5u, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0x00u, 0x00u, 0x00u,
; ..\flash\FL.c	   186  											0xF6u, 0xFFu, 0xFFu, 0xFFu, 0xFFu, 0x00u, 0x00u, 0x00u};
; ..\flash\FL.c	   187  
; ..\flash\FL.c	   188  #pragma section
; ..\flash\FL.c	   189  */
; ..\flash\FL.c	   190  #if (STD_ON == BL_USE_COMBINATION_SWITCH)
; ..\flash\FL.c	   191  /*Pflash and Dflash all used.*/
; ..\flash\FL.c	   192   #pragma section ".BlInf_CODE" aw
; ..\flash\FL.c	   193   const uint8 BL_Information[FL_NUM_LOGICAL_BLOCKS*20+4] ={0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   194  														  0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   195  														  0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   196  														  0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   197  														  0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   198  														  0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   199  														  0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   200  														  0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   201  														  0x00,0x00,0x00,0x00,
; ..\flash\FL.c	   202  														  0xEF,0x7E,0x29,0xFC};
; ..\flash\FL.c	   203   #pragma section
; ..\flash\FL.c	   204  #else
; ..\flash\FL.c	   205   /*just have DFlash .*/
; ..\flash\FL.c	   206    #pragma section farrom="FlsCheckData"
; ..\flash\FL.c	   207   const uint8 BL_Information[FL_NUM_LOGICAL_BLOCKS*20+8] ={0x00};
; ..\flash\FL.c	   208  #pragma section farrom restore
; ..\flash\FL.c	   209  #endif
; ..\flash\FL.c	   210  extern uint16 lastcounter;
; ..\flash\FL.c	   211  uint8 addblkProgAttempt = TRUE;
; ..\flash\FL.c	   212  uint8 appblkIntDefault  = FALSE;
; ..\flash\FL.c	   213  uint8 appblkCpbDefault  = FALSE;
; ..\flash\FL.c	   214  #pragma section farrom "fbl_cpb_data"
; ..\flash\FL.c	   215  	const char boot_cpb_data[4] = {0x00,0x00,0x00,0x00};
; ..\flash\FL.c	   216  #pragma section farrom restore
; ..\flash\FL.c	   217  
; ..\flash\FL.c	   218  /*=======[I N T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
; ..\flash\FL.c	   219  STATIC FL_ResultType FL_Erasing(void);
; ..\flash\FL.c	   220  STATIC FL_ResultType FL_CheckDownloadSegment(void);
; ..\flash\FL.c	   221  STATIC FL_ResultType FL_DownloadRemainData(void);
; ..\flash\FL.c	   222  STATIC FL_ResultType FL_HandleRemainData(void);
; ..\flash\FL.c	   223  STATIC FL_ResultType FL_ProgrammingData(void);
; ..\flash\FL.c	   224  STATIC FL_ResultType FL_Programming(void);
; ..\flash\FL.c	   225  #if(FL_USE_GAP_FILL == STD_ON)
; ..\flash\FL.c	   226  STATIC FL_ResultType FL_FillGap(void);
; ..\flash\FL.c	   227  #endif
; ..\flash\FL.c	   228  
; ..\flash\FL.c	   229  STATIC FL_ResultType FL_CheckSuming(void);
; ..\flash\FL.c	   230  STATIC FL_ResultType FL_CheckSuming_CRC16(uint8 index);
; ..\flash\FL.c	   231  STATIC FL_ResultType FL_CheckSuming_Hash(uint8 index);
; ..\flash\FL.c	   232  STATIC FL_ResultType FL_UpdateNvm(void);
; ..\flash\FL.c	   233  static FL_ResultType FL_CheckCompatibility(void);
; ..\flash\FL.c	   234  /*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
; ..\flash\FL.c	   235  uint16 FL_KeepNvmInforNoUsed()
; Function FL_KeepNvmInforNoUsed
.L194:
FL_KeepNvmInforNoUsed:	.type	func

; ..\flash\FL.c	   236  {
; ..\flash\FL.c	   237      uint8 index;
; ..\flash\FL.c	   238      uint16 sum = 0;
; ..\flash\FL.c	   239  
; ..\flash\FL.c	   240  //    for (index  = 0; index < 64; index++)
; ..\flash\FL.c	   241  //    {
; ..\flash\FL.c	   242  //        sum += BL_Information[index];
; ..\flash\FL.c	   243  //    }
; ..\flash\FL.c	   244  
; ..\flash\FL.c	   245      return sum;
; ..\flash\FL.c	   246  }
	mov	d2,#0
	ret
.L687:
	
__FL_KeepNvmInforNoUsed_function_end:
	.size	FL_KeepNvmInforNoUsed,__FL_KeepNvmInforNoUsed_function_end-FL_KeepNvmInforNoUsed
.L483:
	; End of function
	
	.sdecl	'.text.FL.FL_InitState',code,cluster('FL_InitState')
	.sect	'.text.FL.FL_InitState'
	.align	2
	
	.global	FL_InitState

; ..\flash\FL.c	   247  
; ..\flash\FL.c	   248  
; ..\flash\FL.c	   249  /******************************************************************************/
; ..\flash\FL.c	   250  /**
; ..\flash\FL.c	   251   * @brief               <flashloader module initialize>
; ..\flash\FL.c	   252   * 
; ..\flash\FL.c	   253   * <initialize download status> .
; ..\flash\FL.c	   254   * Service ID   :       <NONE>
; ..\flash\FL.c	   255   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   256   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   257   * @param[in]           <NONE>
; ..\flash\FL.c	   258   * @param[out]          <NONE>
; ..\flash\FL.c	   259   * @param[in/out]       <NONE>
; ..\flash\FL.c	   260   * @return              <NONE>    
; ..\flash\FL.c	   261   */
; ..\flash\FL.c	   262  /******************************************************************************/
; ..\flash\FL.c	   263  void FL_InitState(void)
; Function FL_InitState
.L196:
FL_InitState:	.type	func

; ..\flash\FL.c	   264  {
; ..\flash\FL.c	   265  	uint8 i;
; ..\flash\FL.c	   266      if (0 == FL_KeepNvmInforNoUsed())
; ..\flash\FL.c	   267      {
; ..\flash\FL.c	   268          //return;
; ..\flash\FL.c	   269      }
; ..\flash\FL.c	   270      /*read data from data flash*/
; ..\flash\FL.c	   271      //FL_ReadMemory(DID_BASEADD,DID_BUFF_LENGTH,DIDDataCopy);
; ..\flash\FL.c	   272      /* fingerprint is not written */
; ..\flash\FL.c	   273      FldownloadStatus.fingerPrintWritten = TRUE;
	fcall	.cocofun_1
.L827:
	mov	d15,#1
	st.b	[a15],d15
.L828:

; ..\flash\FL.c	   274      
; ..\flash\FL.c	   275  #if( FLS_USED == STD_OFF )
; ..\flash\FL.c	   276      /* flash driver is not downloaded */
; ..\flash\FL.c	   277      FldownloadStatus.flDrvDownloaded = FALSE;
; ..\flash\FL.c	   278  #else
; ..\flash\FL.c	   279      FldownloadStatus.flDrvDownloaded = TRUE;
	st.b	[a15]10,d15
.L829:

; ..\flash\FL.c	   280  #endif
; ..\flash\FL.c	   281      /* current block is not erased */
; ..\flash\FL.c	   282      FldownloadStatus.blockErased = FALSE;
	mov	d15,#0
	st.b	[a15]12,d15
.L830:

; ..\flash\FL.c	   283      
; ..\flash\FL.c	   284      /* download step is download request 0x34 */
; ..\flash\FL.c	   285      FldownloadStatus.downloadStep = FL_REQUEST_STEP;
	st.b	[a15]1636,d15
.L831:

; ..\flash\FL.c	   286      
; ..\flash\FL.c	   287      /* current active job is idle */
; ..\flash\FL.c	   288      FldownloadStatus.activeJob = FL_JOB_IDLE;
	st.b	[a15]1637,d15
.L832:

; ..\flash\FL.c	   289  
; ..\flash\FL.c	   290      FL_RemainDataStruct.remainLength = 0x00ul;
	movh.a	a15,#@his(FL_RemainDataStruct+4)
.L833:
	st.w	[a15]@los(FL_RemainDataStruct+4),d15
.L834:

; ..\flash\FL.c	   291  
; ..\flash\FL.c	   292  
; ..\flash\FL.c	   293  
; ..\flash\FL.c	   294      return;
; ..\flash\FL.c	   295  }
	ret
.L559:
	
__FL_InitState_function_end:
	.size	FL_InitState,__FL_InitState_function_end-FL_InitState
.L293:
	; End of function
	
	.sdecl	'.text.FL..cocofun_1',code,cluster('.cocofun_1')
	.sect	'.text.FL..cocofun_1'
	.align	2
; Function .cocofun_1
.L198:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh.a	a15,#@his(FldownloadStatus)
.L755:
	lea	a15,[a15]@los(FldownloadStatus)
.L1487:
	fret
.L503:
	; End of function
	.sdecl	'.text.FL.FL_ReadConstDIDData',code,cluster('FL_ReadConstDIDData')
	.sect	'.text.FL.FL_ReadConstDIDData'
	.align	2
	
	.global	FL_ReadConstDIDData

; ..\flash\FL.c	   296  
; ..\flash\FL.c	   297  void FL_ReadConstDIDData(uint8 *readData, uint32 Addr, uint8 length)
; Function FL_ReadConstDIDData
.L200:
FL_ReadConstDIDData:	.type	func

; ..\flash\FL.c	   298  {
; ..\flash\FL.c	   299  	uint8 index;
; ..\flash\FL.c	   300  	uint8 readDataBuf = 0;
; ..\flash\FL.c	   301  	for (index = 0; index < length; index++)
	mov	d15,#0
	j	.L3
.L4:

; ..\flash\FL.c	   302  	{
; ..\flash\FL.c	   303  		//readData[index] = DIDTemp[startIndex + index];
; ..\flash\FL.c	   304  		readDataBuf = *(uint8 *)Addr;
	mov.a	a15,d4
.L707:
	add	d15,#1
.L706:
	ld.bu	d0,[a15]
.L1465:

; ..\flash\FL.c	   305          *readData = readDataBuf;
	extr.u	d15,d15,#0,#8
	st.b	[a4+],d0
.L708:

; ..\flash\FL.c	   306          Addr++ ;
	add	d4,#1
.L3:
	jlt.u	d15,d5,.L4
.L1466:

; ..\flash\FL.c	   307          readData++;
; ..\flash\FL.c	   308  	}
; ..\flash\FL.c	   309  
; ..\flash\FL.c	   310  }
	ret
.L688:
	
__FL_ReadConstDIDData_function_end:
	.size	FL_ReadConstDIDData,__FL_ReadConstDIDData_function_end-FL_ReadConstDIDData
.L488:
	; End of function
	
	.sdecl	'.text.FL.FL_ReadMemory',code,cluster('FL_ReadMemory')
	.sect	'.text.FL.FL_ReadMemory'
	.align	2
	
	.global	FL_ReadMemory

; ..\flash\FL.c	   311  
; ..\flash\FL.c	   312  #if 0
; ..\flash\FL.c	   313  	void FlashReadMemory(uint8* DataBuf,uint32 Addr, uint32 Length)
; ..\flash\FL.c	   314  	{
; ..\flash\FL.c	   315  	    while(Length > 0)
; ..\flash\FL.c	   316  	    {
; ..\flash\FL.c	   317  	        *DataBuf = FlashReadByte(Addr);
; ..\flash\FL.c	   318  	        Addr++;
; ..\flash\FL.c	   319  	        DataBuf++;
; ..\flash\FL.c	   320  	        Length--;
; ..\flash\FL.c	   321  	    }
; ..\flash\FL.c	   322  	}
; ..\flash\FL.c	   323  
; ..\flash\FL.c	   324  	uint8 FlashReadByte(uint32 globalAddr)
; ..\flash\FL.c	   325  	{
; ..\flash\FL.c	   326  	    uint8 readData = 0;
; ..\flash\FL.c	   327  
; ..\flash\FL.c	   328  		/* read data in local address */
; ..\flash\FL.c	   329  		readData = *(uint8 *)globalAddr;
; ..\flash\FL.c	   330  
; ..\flash\FL.c	   331  	    return readData;
; ..\flash\FL.c	   332  	}
; ..\flash\FL.c	   333  #endif
; ..\flash\FL.c	   334  
; ..\flash\FL.c	   335  
; ..\flash\FL.c	   336  
; ..\flash\FL.c	   337  uint32 FL_ReadMemory(uint32 address,
; Function FL_ReadMemory
.L202:
FL_ReadMemory:	.type	func
	mov	e8,d5,d4
	mov.aa	a13,a4
.L710:

; ..\flash\FL.c	   338      uint32 length,
; ..\flash\FL.c	   339      uint8 * data)
; ..\flash\FL.c	   340  {
; ..\flash\FL.c	   341      uint32 readLength = 0x00u;
; ..\flash\FL.c	   342      uint8 curBlockIndex = 0;
; ..\flash\FL.c	   343      
; ..\flash\FL.c	   344      /* read data from flash block */
; ..\flash\FL.c	   345      for (curBlockIndex = 0;
; ..\flash\FL.c	   346          curBlockIndex < FL_NUM_LOGICAL_BLOCKS ;
; ..\flash\FL.c	   347          curBlockIndex++)
; ..\flash\FL.c	   348      {
; ..\flash\FL.c	   349          /* check if address is in range of logical blocks */
; ..\flash\FL.c	   350          if ((address >= FL_BlkInfo[curBlockIndex].address) &&
	mov	d2,#0
	fcall	.cocofun_3
.L839:
	mov.a	a12,#1
.L5:
	ld.w	d0,[a15]
.L712:
	jlt.u	d8,d0,.L6
.L713:

; ..\flash\FL.c	   351              (address < (FL_BlkInfo[curBlockIndex].address +
; ..\flash\FL.c	   352                  FL_BlkInfo[curBlockIndex].length)))
	ld.w	d15,[a15]4
.L840:
	add	d0,d15
.L714:
	jge.u	d8,d0,.L7
.L715:

; ..\flash\FL.c	   353          {
; ..\flash\FL.c	   354              FlashReadMemory(data,address,length);
	mov.aa	a4,a13
.L716:
	mov	e4,d9,d8
	call	FlashReadMemory
.L711:

; ..\flash\FL.c	   355              readLength = length;
	mov	d2,d9
.L7:
.L6:

; ..\flash\FL.c	   356          }
; ..\flash\FL.c	   357  
; ..\flash\FL.c	   358          /* check if address is for did data */
; ..\flash\FL.c	   359          if ((address >= 0xAF010000) && (address <= 0xAF01FFFF))
	movh	d0,#44801
.L718:
	sub	d0,d8,d0
.L719:
	mov.u	d1,#65535
.L841:
	jlt.u	d1,d0,.L8
.L842:

; ..\flash\FL.c	   360          {
; ..\flash\FL.c	   361              FlashReadMemory(data,address,length);
	mov.aa	a4,a13
.L720:
	mov	e4,d9,d8
	call	FlashReadMemory
.L717:

; ..\flash\FL.c	   362              readLength = length;
	mov	d2,d9
.L8:
	lea	a15,[a15]24
	loop	a12,.L5
.L843:

; ..\flash\FL.c	   363          }
; ..\flash\FL.c	   364      }
; ..\flash\FL.c	   365      /* read data from header */
; ..\flash\FL.c	   366      if ((address >= (&SecureSignHeader.datas)) &&
	movh.a	a15,#@his(SecureSignHeader)
	lea	a15,[a15]@los(SecureSignHeader)
	mov.d	d15,a15
.L721:
	jlt.u	d8,d15,.L9
.L722:

; ..\flash\FL.c	   367          (address < (&SecureSignHeader.datas + SECURE_HEADER_LEN)))
	movh.a	a3,#4
	add.a	a3,a15
	lea	a15,[a3]23012
	mov.d	d15,a15
.L723:
	jge.u	d8,d15,.L10
.L724:

; ..\flash\FL.c	   368      {
; ..\flash\FL.c	   369          while ((length > 0) &&
; ..\flash\FL.c	   370              (address < (&SecureSignHeader.datas + SECURE_HEADER_LEN)))
	j	.L11
.L12:

; ..\flash\FL.c	   371          {
; ..\flash\FL.c	   372              /* read data from local address in RAM,direct read and write */
; ..\flash\FL.c	   373              *data = *(uint8 *)address;
	mov.a	a2,d8
.L725:

; ..\flash\FL.c	   374              data++;
; ..\flash\FL.c	   375              address++;
	add	d8,#1
.L844:
	ld.bu	d15,[a2]
.L726:

; ..\flash\FL.c	   376              length--;
	add	d9,#-1
	st.b	[a13+],d15
.L845:

; ..\flash\FL.c	   377              readLength++;
	add	d2,#1
.L11:
	jeq	d9,#0,.L13
.L727:
	mov.d	d15,a15
.L728:
	jlt.u	d8,d15,.L12
.L13:
.L10:
.L9:

; ..\flash\FL.c	   378          }
; ..\flash\FL.c	   379      }
; ..\flash\FL.c	   380  
; ..\flash\FL.c	   381  
; ..\flash\FL.c	   382      /* read data from RAM of flash driver */
; ..\flash\FL.c	   383      if ((address >= FL_DEV_BASE_ADDRESS) &&
	mov	d15,#4096
	addih	d15,d15,#28688
.L729:

; ..\flash\FL.c	   384          (address < (FL_DEV_BASE_ADDRESS + FL_DEV_SIZE)))
	sub	d15,d8,d15
.L730:
	mov	d0,#554
.L846:
	jge.u	d15,d0,.L14
.L847:

; ..\flash\FL.c	   385      {
; ..\flash\FL.c	   386          while ((length > 0) &&
; ..\flash\FL.c	   387              (address < (FL_DEV_BASE_ADDRESS + FL_DEV_SIZE)))
	j	.L15
.L16:

; ..\flash\FL.c	   388          {
; ..\flash\FL.c	   389              /* read data from local address in RAM,direct read and write */
; ..\flash\FL.c	   390              *data = *(uint8 *)address;
	mov.a	a15,d8
.L731:

; ..\flash\FL.c	   391              data++;
; ..\flash\FL.c	   392              address++;
	add	d8,#1
.L848:
	ld.bu	d15,[a15]
.L732:

; ..\flash\FL.c	   393              length--;
	add	d9,#-1
	st.b	[a13+],d15
.L849:

; ..\flash\FL.c	   394              readLength++;
	add	d2,#1
.L15:
	jeq	d9,#0,.L17
.L733:
	mov	d15,#4650
	addih	d15,d15,#28688
.L734:
	jlt.u	d8,d15,.L16
.L17:
.L14:

; ..\flash\FL.c	   395          }
; ..\flash\FL.c	   396      }
; ..\flash\FL.c	   397  
; ..\flash\FL.c	   398      /*锟斤拷锟斤拷址锟角凤拷锟斤拷刷写remain锟斤拷锟斤拷锟斤拷*/
; ..\flash\FL.c	   399      if ((address >= (uint32)&FlProgramData[0]) &&(address <= (uint32)&FlProgramData[FL_PROGRAM_SIZE-1]))
	movh.a	a15,#@his(FlProgramData)
	lea	a15,[a15]@los(FlProgramData)
	mov.d	d15,a15
.L735:
	jlt.u	d8,d15,.L18
.L736:
	lea	a15,[a15]255
	mov.d	d15,a15
.L737:
	jlt.u	d15,d8,.L19
.L738:

; ..\flash\FL.c	   400      {
; ..\flash\FL.c	   401  		while ((length > 0) &&(address < (uint32)&FlProgramData[FL_PROGRAM_SIZE-1]))
	j	.L20
.L21:

; ..\flash\FL.c	   402  		{
; ..\flash\FL.c	   403  			/* read data from local address in RAM,direct read and write */
; ..\flash\FL.c	   404  			*data = *(uint8 *)address;
	mov.a	a15,d8
.L739:

; ..\flash\FL.c	   405  			data++;
; ..\flash\FL.c	   406  			address++;
	add	d8,#1
.L850:
	ld.bu	d0,[a15]
.L740:

; ..\flash\FL.c	   407  			length--;
	add	d9,#-1
	st.b	[a13+],d0
.L851:

; ..\flash\FL.c	   408  			readLength++;
	add	d2,#1
.L20:
	jeq	d9,#0,.L22
.L741:
	jlt.u	d8,d15,.L21
.L22:
.L19:
.L18:

; ..\flash\FL.c	   409  		}
; ..\flash\FL.c	   410       }
; ..\flash\FL.c	   411      return readLength;
; ..\flash\FL.c	   412  }
	ret
.L562:
	
__FL_ReadMemory_function_end:
	.size	FL_ReadMemory,__FL_ReadMemory_function_end-FL_ReadMemory
.L298:
	; End of function
	
	.sdecl	'.text.FL..cocofun_3',code,cluster('.cocofun_3')
	.sect	'.text.FL..cocofun_3'
	.align	2
; Function .cocofun_3
.L204:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:0
	movh.a	a15,#@his(FL_BlkInfo)
.L709:
	lea	a15,[a15]@los(FL_BlkInfo)
.L1497:
	fret
.L513:
	; End of function
	.sdecl	'.text.FL.FL_CheckProgPreCondition',code,cluster('FL_CheckProgPreCondition')
	.sect	'.text.FL.FL_CheckProgPreCondition'
	.align	2
	
	.global	FL_CheckProgPreCondition

; ..\flash\FL.c	   413  
; ..\flash\FL.c	   414  /******************************************************************************/
; ..\flash\FL.c	   415  /**
; ..\flash\FL.c	   416   * @brief               <0x31 check program pre-condition>
; ..\flash\FL.c	   417   * 
; ..\flash\FL.c	   418   * <0x31 check program pre-condition .
; ..\flash\FL.c	   419   * Service ID   :       <NONE>
; ..\flash\FL.c	   420   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   421   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   422   * @param[in]           <NONE>
; ..\flash\FL.c	   423   * @param[out]          <conditions (OUT)>
; ..\flash\FL.c	   424   * @param[in/out]       <NONE>
; ..\flash\FL.c	   425   * @return              <uint8>    
; ..\flash\FL.c	   426   */
; ..\flash\FL.c	   427  /******************************************************************************/
; ..\flash\FL.c	   428  uint8 FL_CheckProgPreCondition(uint8 * conditions)
; Function FL_CheckProgPreCondition
.L206:
FL_CheckProgPreCondition:	.type	func

; ..\flash\FL.c	   429  {
; ..\flash\FL.c	   430      *conditions = 0x00u; 
	mov	d15,#0
	st.b	[a4],d15
.L856:

; ..\flash\FL.c	   431      
; ..\flash\FL.c	   432      return 0x00u;
; ..\flash\FL.c	   433  }
	mov	d2,#0
	ret
.L571:
	
__FL_CheckProgPreCondition_function_end:
	.size	FL_CheckProgPreCondition,__FL_CheckProgPreCondition_function_end-FL_CheckProgPreCondition
.L303:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckSumRoutine',code,cluster('FL_CheckSumRoutine')
	.sect	'.text.FL.FL_CheckSumRoutine'
	.align	2
	
	.global	FL_CheckSumRoutine

; ..\flash\FL.c	   434  
; ..\flash\FL.c	   435  
; ..\flash\FL.c	   436  uint8 Fl_crc = 1;
; ..\flash\FL.c	   437  FL_ResultType FL_CheckSumRoutine(const boolean isHash)
; Function FL_CheckSumRoutine
.L208:
FL_CheckSumRoutine:	.type	func

; ..\flash\FL.c	   438  {
; ..\flash\FL.c	   439      
; ..\flash\FL.c	   440      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	   441      
; ..\flash\FL.c	   442      /* record checksum buffer address */
; ..\flash\FL.c	   443      // FldownloadStatus.dataBuff = checkSum;
; ..\flash\FL.c	   444      FlIntegrityChkIsHash = isHash;
	mov	d8,#0
	movh.a	a15,#@his(FlIntegrityChkIsHash)
.L743:
	st.b	[a15]@los(FlIntegrityChkIsHash),d4
.L870:

; ..\flash\FL.c	   445      /* check if download step is checksum step */
; ..\flash\FL.c	   446      if (FL_CHECKSUM_STEP == FldownloadStatus.downloadStep)
	fcall	.cocofun_1
.L871:
	ld.bu	d15,[a15]1636
.L872:
	jne	d15,#5,.L25
.L873:

; ..\flash\FL.c	   447      {
; ..\flash\FL.c	   448          /* set the download active state to checksum */
; ..\flash\FL.c	   449          FldownloadStatus.activeJob = FL_JOB_CHECKING;
	mov	d15,#3
	st.b	[a15]1637,d15
.L874:

; ..\flash\FL.c	   450          FldownloadStatus.errorCode = FL_OK;
	st.b	[a15]11,d8
.L875:

; ..\flash\FL.c	   451          if (FldownloadStatus.flDrvDownloaded == TRUE)
	ld.bu	d15,[a15]10
.L876:
	jne	d15,#1,.L26
.L877:

; ..\flash\FL.c	   452          {
; ..\flash\FL.c	   453          	if (FL_RemainDataStruct.remainLength != 0x00ul)
	movh.a	a15,#@his(FL_RemainDataStruct+4)
.L878:
	ld.w	d15,[a15]@los(FL_RemainDataStruct+4)
.L879:
	jeq	d15,#0,.L27
.L880:

; ..\flash\FL.c	   454  				ret = FL_DownloadRemainData();
	call	FL_DownloadRemainData
.L742:
	mov	d8,d2
	j	.L28
.L27:

; ..\flash\FL.c	   455  			else
; ..\flash\FL.c	   456  				ret =FL_OK;
	mov	d8,#0
	j	.L29
.L25:

; ..\flash\FL.c	   457          }
; ..\flash\FL.c	   458      }
; ..\flash\FL.c	   459      else
; ..\flash\FL.c	   460      {
; ..\flash\FL.c	   461          ret = FL_ERR_SEQUENCE;
; ..\flash\FL.c	   462          
; ..\flash\FL.c	   463          /* initialize the flash download state */
; ..\flash\FL.c	   464          FL_InitState();
	mov	d8,#2
	call	FL_InitState
.L29:
.L28:
.L26:

; ..\flash\FL.c	   465      }
; ..\flash\FL.c	   466      
; ..\flash\FL.c	   467      return ret;
; ..\flash\FL.c	   468  }
	mov	d2,d8
	ret
.L580:
	
__FL_CheckSumRoutine_function_end:
	.size	FL_CheckSumRoutine,__FL_CheckSumRoutine_function_end-FL_CheckSumRoutine
.L313:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckCPBRoutine',code,cluster('FL_CheckCPBRoutine')
	.sect	'.text.FL.FL_CheckCPBRoutine'
	.align	2
	
	.global	FL_CheckCPBRoutine

; ..\flash\FL.c	   469  
; ..\flash\FL.c	   470  FL_ResultType FL_CheckCPBRoutine(void)
; Function FL_CheckCPBRoutine
.L210:
FL_CheckCPBRoutine:	.type	func

; ..\flash\FL.c	   471  {
; ..\flash\FL.c	   472  	FL_ResultType ret = FL_OK;
; ..\flash\FL.c	   473  	FldownloadStatus.activeJob = FL_JOB_COMPATIBLE;
	movh.a	a15,#@his(FldownloadStatus+1637)
.L885:
	mov	d15,#5
	st.b	[a15]@los(FldownloadStatus+1637),d15
.L886:

; ..\flash\FL.c	   474  	Appl_UpdateTriggerConditionImmediate(1);
	mov	d4,#1
	call	Appl_UpdateTriggerConditionImmediate
.L887:

; ..\flash\FL.c	   475  	return ret;
; ..\flash\FL.c	   476  }
	mov	d2,#0
	ret
.L584:
	
__FL_CheckCPBRoutine_function_end:
	.size	FL_CheckCPBRoutine,__FL_CheckCPBRoutine_function_end-FL_CheckCPBRoutine
.L318:
	; End of function
	
	.sdecl	'.text.FL.FBL_IntegrityCheck',code,cluster('FBL_IntegrityCheck')
	.sect	'.text.FL.FBL_IntegrityCheck'
	.align	2
	
	.global	FBL_IntegrityCheck

; ..\flash\FL.c	   477  FL_ResultType FBL_IntegrityCheck(void)
; Function FBL_IntegrityCheck
.L212:
FBL_IntegrityCheck:	.type	func

; ..\flash\FL.c	   478  {
; ..\flash\FL.c	   479      FL_ResultType ret;
; ..\flash\FL.c	   480      uint8 idx = 0;
; ..\flash\FL.c	   481      uint8 someSignVerifiedFlag = FBL_FALSE;
; ..\flash\FL.c	   482      // if bypass is opened or file is not app
; ..\flash\FL.c	   483      // for(idx = 0; idx < 5u; idx++)
; ..\flash\FL.c	   484      {
; ..\flash\FL.c	   485          if((1u == FBL_DownloadFlags[idx]) && (1u == FBL_SignVerifFlags[idx]))
	movh.a	a15,#@his(FBL_DownloadFlags)
.L892:
	ld.bu	d15,[a15]@los(FBL_DownloadFlags)
.L893:
	mov	d0,#0
.L744:
	jne	d15,#1,.L32
.L894:
	movh.a	a15,#@his(FBL_SignVerifFlags)
.L895:
	ld.bu	d15,[a15]@los(FBL_SignVerifFlags)
.L896:
	eq	d15,d15,#1
.L897:

; ..\flash\FL.c	   486          {
; ..\flash\FL.c	   487              someSignVerifiedFlag = FBL_TRUE;
	cmov	d0,d15,#1
.L32:

; ..\flash\FL.c	   488              // break;
; ..\flash\FL.c	   489          }
; ..\flash\FL.c	   490      }
; ..\flash\FL.c	   491      if ((FL_CHECKSUM_STEP == FldownloadStatus.downloadStep) || 
	movh.a	a15,#@his(FldownloadStatus+1636)
.L898:
	ld.bu	d15,[a15]@los(FldownloadStatus+1636)
.L899:
	jeq	d15,#5,.L34
.L900:

; ..\flash\FL.c	   492          ((FL_REQUEST_STEP == FldownloadStatus.downloadStep) && (FBL_TRUE == someSignVerifiedFlag)))
	jne	d15,#0,.L35
.L901:
	jeq	d0,#0,.L36
.L34:

; ..\flash\FL.c	   493      {
; ..\flash\FL.c	   494          // if ((FBL_TRUE == FBL_BypassFlag))
; ..\flash\FL.c	   495          // {
; ..\flash\FL.c	   496          //     ret = FL_CheckSumRoutine(FALSE);
; ..\flash\FL.c	   497          // }
; ..\flash\FL.c	   498          // else if (FBL_TRUE == someSignVerifiedFlag)
; ..\flash\FL.c	   499          if (FBL_TRUE == someSignVerifiedFlag)
	jeq	d0,#0,.L37
.L902:

; ..\flash\FL.c	   500          {
; ..\flash\FL.c	   501              ret = FL_CheckSumRoutine(TRUE);
	mov	d4,#1
	j	FL_CheckSumRoutine
.L37:

; ..\flash\FL.c	   502          }
; ..\flash\FL.c	   503          else
; ..\flash\FL.c	   504          {
; ..\flash\FL.c	   505              Fl_crc = 1;
	movh.a	a15,#@his(Fl_crc)
.L903:
	mov	d15,#1
	st.b	[a15]@los(Fl_crc),d15
.L904:

; ..\flash\FL.c	   506              ret = FL_OK;
; ..\flash\FL.c	   507          }
; ..\flash\FL.c	   508      }
; ..\flash\FL.c	   509      else
; ..\flash\FL.c	   510      {
; ..\flash\FL.c	   511          ret = FL_ERR_SEQUENCE;
; ..\flash\FL.c	   512      }
; ..\flash\FL.c	   513      // FldownloadStatus.downloadStep = FL_REQUEST_STEP;
; ..\flash\FL.c	   514      return ret;
; ..\flash\FL.c	   515  }
	mov	d2,#0
	ret
.L36:
.L35:
	mov	d2,#2
	ret
.L585:
	
__FBL_IntegrityCheck_function_end:
	.size	FBL_IntegrityCheck,__FBL_IntegrityCheck_function_end-FBL_IntegrityCheck
.L323:
	; End of function
	
	.sdecl	'.text.FL.FL_EraseRoutine',code,cluster('FL_EraseRoutine')
	.sect	'.text.FL.FL_EraseRoutine'
	.align	2
	
	.global	FL_EraseRoutine

; ..\flash\FL.c	   516  /******************************************************************************/
; ..\flash\FL.c	   517  /**
; ..\flash\FL.c	   518   * @brief               <0x31 service routine erase>
; ..\flash\FL.c	   519   * 
; ..\flash\FL.c	   520   * <erase routine for  logical block> .
; ..\flash\FL.c	   521   * Service ID   :       <NONE>
; ..\flash\FL.c	   522   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   523   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   524   * @param[in]           <blockIndex (IN)>
; ..\flash\FL.c	   525   * @param[out]          <NONE>
; ..\flash\FL.c	   526   * @param[in/out]       <NONE>
; ..\flash\FL.c	   527   * @return              <FL_ResultType>    
; ..\flash\FL.c	   528   */
; ..\flash\FL.c	   529  /******************************************************************************/
; ..\flash\FL.c	   530  FL_ResultType FL_EraseRoutine(const uint8 blockIndex)
; Function FL_EraseRoutine
.L214:
FL_EraseRoutine:	.type	func
	mov	d8,d4
.L746:

; ..\flash\FL.c	   531  {
; ..\flash\FL.c	   532      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	   533      FBL_DownloadFlags[0] = 1;
	mov	d9,#0
	movh.a	a15,#@his(FBL_DownloadFlags)
.L747:
	mov	d0,#1
	st.b	[a15]@los(FBL_DownloadFlags),d0
.L745:

; ..\flash\FL.c	   534      /* check if finger print is written */
; ..\flash\FL.c	   535      if (FALSE == FldownloadStatus.fingerPrintWritten)
	fcall	.cocofun_1
.L748:
	ld.bu	d15,[a15]
.L909:
	jne	d15,#0,.L41
.L910:

; ..\flash\FL.c	   536      {
; ..\flash\FL.c	   537          ret = FL_NO_FINGERPRINT;
	mov	d9,#3
	j	.L42
.L41:

; ..\flash\FL.c	   538      }
; ..\flash\FL.c	   539      else
; ..\flash\FL.c	   540      {
; ..\flash\FL.c	   541          /* check if flash driver is downloaded */
; ..\flash\FL.c	   542          if (FALSE == FldownloadStatus.flDrvDownloaded)
	ld.bu	d15,[a15]10
.L911:
	jne	d15,#0,.L43
.L912:

; ..\flash\FL.c	   543          {
; ..\flash\FL.c	   544              ret = FL_NO_FLASHDRIVER;
	mov	d9,#4
	j	.L44
.L43:

; ..\flash\FL.c	   545          }
; ..\flash\FL.c	   546          else
; ..\flash\FL.c	   547          {
; ..\flash\FL.c	   548              /* check download step sequence */
; ..\flash\FL.c	   549              if (FL_REQUEST_STEP != FldownloadStatus.downloadStep)
	ld.bu	d15,[a15]1636
.L913:
	jeq	d15,#0,.L45
.L914:

; ..\flash\FL.c	   550              {
; ..\flash\FL.c	   551              	//一锟斤拷锟竭硷拷block锟斤拷锟斤拷锟疥，锟斤拷锟斤拷锟斤拷要锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷校锟介，也锟斤拷锟斤拷锟斤拷要锟斤拷锟斤拷锟斤拷一锟斤拷锟竭硷拷block锟侥诧拷锟斤拷
; ..\flash\FL.c	   552              	if(FldownloadStatus.downloadStep==FL_CHECKSUM_STEP)
	jne	d15,#5,.L46
.L915:

; ..\flash\FL.c	   553              	{
; ..\flash\FL.c	   554              		if (FL_RemainDataStruct.remainLength != 0x00ul)
	movh.a	a2,#@his(FL_RemainDataStruct+4)
.L916:
	ld.w	d15,[a2]@los(FL_RemainDataStruct+4)
.L917:
	jeq	d15,#0,.L47
.L918:

; ..\flash\FL.c	   555              						ret = FL_DownloadRemainData();
	call	FL_DownloadRemainData
.L749:
	mov	d9,d2
	j	.L48
.L46:

; ..\flash\FL.c	   556  
; ..\flash\FL.c	   557                      
; ..\flash\FL.c	   558              	}
; ..\flash\FL.c	   559              	else
; ..\flash\FL.c	   560              	{
; ..\flash\FL.c	   561                  ret = FL_ERR_SEQUENCE;
	mov	d9,#2
.L48:
.L47:
.L45:
.L44:
.L42:

; ..\flash\FL.c	   562              }
; ..\flash\FL.c	   563  
; ..\flash\FL.c	   564              }
; ..\flash\FL.c	   565          }
; ..\flash\FL.c	   566      }
; ..\flash\FL.c	   567      
; ..\flash\FL.c	   568      /* check the conditions of erase */
; ..\flash\FL.c	   569      if (FL_OK == ret)
	jne	d9,#0,.L49
.L919:

; ..\flash\FL.c	   570      {
; ..\flash\FL.c	   571          /* check the erase block index and the pragram attempt*/
; ..\flash\FL.c	   572          if (blockIndex < FL_NUM_LOGICAL_BLOCKS)
	jge.u	d8,#2,.L50
.L920:

; ..\flash\FL.c	   573          {
; ..\flash\FL.c	   574              if (((FL_NvmInfo.blockInfo[blockIndex].blkProgAttempt <
; ..\flash\FL.c	   575                  FL_BlkInfo[blockIndex].maxProgAttempt) ||
	mul	d15,d8,#24
	movh.a	a2,#@his(FL_BlkInfo)
	lea	a2,[a2]@los(FL_BlkInfo)
.L921:
	addsc.a	a2,a2,d15,#0
.L922:
	mul	d0,d8,#20
.L923:
	ld.w	d15,[a2]16
.L924:
	movh.a	a2,#@his(FL_NvmInfo)
	lea	a2,[a2]@los(FL_NvmInfo)
.L925:
	addsc.a	a4,a2,d0,#0
.L926:
	ld.hu	d0,[a4]2
	lea	a5,[a4]2
.L927:
	jlt.u	d0,d15,.L51
.L928:

; ..\flash\FL.c	   576                  (0x00u == FL_BlkInfo[blockIndex].maxProgAttempt)))
	jne	d15,#0,.L52
.L51:

; ..\flash\FL.c	   577              {
; ..\flash\FL.c	   578                  /* set current block is valid */
; ..\flash\FL.c	   579                  FL_NvmInfo.blockInfo[blockIndex].blkValid = FALSE;
	mov	d15,#0
	st.b	[a4],d15
.L929:

; ..\flash\FL.c	   580                  
; ..\flash\FL.c	   581                  /* set the Compatibility flag related to current block  invalid */
; ..\flash\FL.c	   582                   switch(blockIndex)
	jeq	d8,#0,.L53
.L930:

; ..\flash\FL.c	   583                   {
; ..\flash\FL.c	   584  					 case APPBLOCKINDEX:
; ..\flash\FL.c	   585  					 {
; ..\flash\FL.c	   586  						 FL_NvmInfo.isAppCalCpb=TRUE;
	mov	d0,#1
	st.b	[a2]40,d0
.L931:

; ..\flash\FL.c	   587  						 FL_NvmInfo.isAppFblCpb=FALSE;
	st.b	[a2]41,d15
.L932:

; ..\flash\FL.c	   588                           appblkIntDefault = TRUE;
	movh.a	a2,#@his(appblkIntDefault)
.L933:
	st.b	[a2]@los(appblkIntDefault),d0
.L934:

; ..\flash\FL.c	   589                           appblkCpbDefault = TRUE;
	movh.a	a2,#@his(appblkCpbDefault)
.L935:
	st.b	[a2]@los(appblkCpbDefault),d0
.L53:

; ..\flash\FL.c	   590  						 break;
; ..\flash\FL.c	   591  					 }
; ..\flash\FL.c	   592  					//  case CALDATABLOCKINDEX:
; ..\flash\FL.c	   593  					//  {
; ..\flash\FL.c	   594  					// 	 FL_NvmInfo.isAppCalCpb=FALSE;
; ..\flash\FL.c	   595  
; ..\flash\FL.c	   596  					// 	 break;
; ..\flash\FL.c	   597  					//  }
; ..\flash\FL.c	   598                   }
; ..\flash\FL.c	   599                  if(addblkProgAttempt == TRUE)
	movh.a	a2,#@his(addblkProgAttempt)
	lea	a2,[a2]@los(addblkProgAttempt)
	ld.bu	d15,[a2]
.L936:
	jne	d15,#1,.L54
.L937:

; ..\flash\FL.c	   600                  {
; ..\flash\FL.c	   601                      /* increment program attempt counter of current block */
; ..\flash\FL.c	   602                      FL_NvmInfo.blockInfo[blockIndex].blkProgAttempt++;
	ld.hu	d15,[a4]2
.L938:
	add	d15,#1
	st.h	[a5],d15
.L939:

; ..\flash\FL.c	   603                      addblkProgAttempt = FALSE;     
	mov	d15,#0
	st.b	[a2],d15
.L54:

; ..\flash\FL.c	   604                  }
; ..\flash\FL.c	   605                  else
; ..\flash\FL.c	   606                  {
; ..\flash\FL.c	   607                      /* do nothing */
; ..\flash\FL.c	   608                  }
; ..\flash\FL.c	   609                  
; ..\flash\FL.c	   610                  /* store fingerprint of current block */
; ..\flash\FL.c	   611                  Appl_Memcpy(FL_NvmInfo.blockInfo[blockIndex].fingerPrint,
	lea	a4,[a4]8
.L940:

; ..\flash\FL.c	   612                      FldownloadStatus.fingerPrint, FL_FINGER_PRINT_LENGTH);
	lea	a5,[a15]1
.L941:
	mov	d4,#9
	call	Appl_Memcpy
.L942:

; ..\flash\FL.c	   613  
; ..\flash\FL.c	   614                  /* change and initialize status of the programmed block */
; ..\flash\FL.c	   615  
; ..\flash\FL.c	   616                  FldownloadStatus.segmentList[blockIndex].nrOfSegments = 0x00u;
	mov	d15,#804
	mul	d15,d8
	addsc.a	a2,a15,d15,#0
.L943:
	mov	d15,#0
.L944:
	st.h	[a2]28,d15
.L945:

; ..\flash\FL.c	   617                  FldownloadStatus.segmentList[blockIndex].blockChecked=FALSE;
	st.b	[a2]30,d15
.L946:

; ..\flash\FL.c	   618  
; ..\flash\FL.c	   619  
; ..\flash\FL.c	   620                  FldownloadStatus.blockIndex = blockIndex;
	st.b	[a15]13,d8
.L947:

; ..\flash\FL.c	   621                  FldownloadStatus.blockErased = FALSE;
	st.b	[a15]12,d15
.L948:

; ..\flash\FL.c	   622                  FldownloadStatus.errorCode = FL_OK;
	st.b	[a15]11,d15
.L949:

; ..\flash\FL.c	   623                  
; ..\flash\FL.c	   624                  /* set the download active state to erase */
; ..\flash\FL.c	   625                  FldownloadStatus.activeJob = FL_JOB_ERASING;
	mov	d15,#1
	st.b	[a15]1637,d15
.L950:
	j	.L55
.L52:

; ..\flash\FL.c	   626              }
; ..\flash\FL.c	   627              else
; ..\flash\FL.c	   628              {
; ..\flash\FL.c	   629                  ret = FL_FAILED;
	mov	d9,#1
	j	.L56
.L50:

; ..\flash\FL.c	   630              }
; ..\flash\FL.c	   631          }
; ..\flash\FL.c	   632          else
; ..\flash\FL.c	   633          {
; ..\flash\FL.c	   634              ret = FL_INVALID_DATA;
	mov	d9,#6
.L56:
.L55:
.L49:

; ..\flash\FL.c	   635          }
; ..\flash\FL.c	   636      }
; ..\flash\FL.c	   637      
; ..\flash\FL.c	   638      if (ret != FL_OK)
	jeq	d9,#0,.L57
.L951:

; ..\flash\FL.c	   639      {
; ..\flash\FL.c	   640          /* initialize the flash download state */
; ..\flash\FL.c	   641          FL_InitState();
	call	FL_InitState
.L57:

; ..\flash\FL.c	   642      }
; ..\flash\FL.c	   643      
; ..\flash\FL.c	   644      return ret;
; ..\flash\FL.c	   645  }
	mov	d2,d9
	ret
.L588:
	
__FL_EraseRoutine_function_end:
	.size	FL_EraseRoutine,__FL_EraseRoutine_function_end-FL_EraseRoutine
.L328:
	; End of function
	
	.sdecl	'.text.FL.FL_DownloadRequestValid',code,cluster('FL_DownloadRequestValid')
	.sect	'.text.FL.FL_DownloadRequestValid'
	.align	2
	
	.global	FL_DownloadRequestValid

; ..\flash\FL.c	   646  
; ..\flash\FL.c	   647  
; ..\flash\FL.c	   648  
; ..\flash\FL.c	   649  /******************************************************************************/
; ..\flash\FL.c	   650  /**
; ..\flash\FL.c	   651   * @brief               <0x34 service download request>
; ..\flash\FL.c	   652   * 
; ..\flash\FL.c	   653   * <download request for current logical block> .
; ..\flash\FL.c	   654   * Service ID   :       <NONE>
; ..\flash\FL.c	   655   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   656   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   657   * @param[in]           <startAdd (IN), length (IN)>
; ..\flash\FL.c	   658   * @param[out]          <NONE>
; ..\flash\FL.c	   659   * @param[in/out]       <NONE>
; ..\flash\FL.c	   660   * @return              <FL_ResultType>    
; ..\flash\FL.c	   661   */
; ..\flash\FL.c	   662  /******************************************************************************/
; ..\flash\FL.c	   663  uint8 FLERRStatus=0;
; ..\flash\FL.c	   664  FL_ResultType FL_DownloadRequestValid(const uint32 startAdd,
; Function FL_DownloadRequestValid
.L216:
FL_DownloadRequestValid:	.type	func

; ..\flash\FL.c	   665      const uint32 length)
; ..\flash\FL.c	   666  {
; ..\flash\FL.c	   667      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	   668  
; ..\flash\FL.c	   669      /* save the parameter used for active job use */
; ..\flash\FL.c	   670          FldownloadStatus.startAddr = startAdd;
	movh.a	a12,#@his(FldownloadStatus)
	lea	a12,[a12]@los(FldownloadStatus)
.L956:
	lea	a2,[a12]14
.L957:
	st.w	[a2],d4
.L958:

; ..\flash\FL.c	   671          FldownloadStatus.length = length;
	lea	a4,[a12]18
.L959:
	st.w	[a4],d5
.L960:
	mov	d8,#0
.L751:

; ..\flash\FL.c	   672  
; ..\flash\FL.c	   673      /* check if finger print is written */
; ..\flash\FL.c	   674      if (FALSE == FldownloadStatus.fingerPrintWritten)
	ld.bu	d15,[a12]
.L961:
	jne	d15,#0,.L59
.L962:

; ..\flash\FL.c	   675      {
; ..\flash\FL.c	   676          ret = FL_NO_FINGERPRINT;
	mov	d8,#3
	j	.L60
.L59:

; ..\flash\FL.c	   677      }
; ..\flash\FL.c	   678      else
; ..\flash\FL.c	   679      {
; ..\flash\FL.c	   680          /* check download step sequence */
; ..\flash\FL.c	   681          if ((FL_REQUEST_STEP != FldownloadStatus.downloadStep) &&
	ld.bu	d15,[a12]1636
.L963:
	jeq	d15,#0,.L61
.L964:

; ..\flash\FL.c	   682              (FL_CHECKSUM_STEP != FldownloadStatus.downloadStep))
	jeq	d15,#5,.L62
.L965:

; ..\flash\FL.c	   683          {
; ..\flash\FL.c	   684              ret = FL_ERR_SEQUENCE;
	mov	d8,#2
	j	.L63
.L62:
.L61:

; ..\flash\FL.c	   685          }
; ..\flash\FL.c	   686          else
; ..\flash\FL.c	   687          {
; ..\flash\FL.c	   688              if (0 == FldownloadStatus.length)
; ..\flash\FL.c	   689              {
; ..\flash\FL.c	   690                  ret = FL_ERR_ADDR_LENGTH;
	sel	d8,d5,d8,#5
.L63:
.L60:

; ..\flash\FL.c	   691              }
; ..\flash\FL.c	   692          }
; ..\flash\FL.c	   693      }
; ..\flash\FL.c	   694      
; ..\flash\FL.c	   695      /* check the condition for download request */
; ..\flash\FL.c	   696      if (FL_OK == ret)
	jne	d8,#0,.L65
.L966:

; ..\flash\FL.c	   697      {
; ..\flash\FL.c	   698          /* check if flash driver is downloaded */
; ..\flash\FL.c	   699          if (FALSE == FldownloadStatus.flDrvDownloaded)
	ld.bu	d15,[a12]10
.L967:
	jne	d15,#0,.L66
.L968:

; ..\flash\FL.c	   700          {
; ..\flash\FL.c	   701              /* check if download address is in flash driver RAM range */
; ..\flash\FL.c	   702              if ((FL_DEV_BASE_ADDRESS == FldownloadStatus.startAddr) &&
	mov	d15,#4096
	ld.w	d0,[a2]
.L969:
	addih	d15,d15,#28688
.L970:
	jne	d15,d0,.L67
.L971:

; ..\flash\FL.c	   703                  (FldownloadStatus.length <= FL_DEV_SIZE))
	ld.w	d0,[a4]
.L972:
	mov	d1,#554
.L973:
	jlt.u	d1,d0,.L68
.L974:

; ..\flash\FL.c	   704              {
; ..\flash\FL.c	   705                  /* set the download step*/
; ..\flash\FL.c	   706                  FldownloadStatus.downloadStep = FL_TRANSFER_STEP;
	mov	d15,#3
	st.b	[a12]1636,d15
.L975:

; ..\flash\FL.c	   707  
; ..\flash\FL.c	   708                  FldownloadStatus.segmentList[CurrentProgrammingBlock].nrOfSegments=0x01u;
	movh.a	a5,#@his(CurrentProgrammingBlock)
	ld.bu	d15,[a5]@los(CurrentProgrammingBlock)
.L976:
	mov	d0,#804
	mul	d15,d0
	addsc.a	a15,a12,d15,#0
.L977:
	mov	d15,#1
.L978:
	st.h	[+a15]28,d15
.L979:

; ..\flash\FL.c	   709                  FldownloadStatus.segmentList[CurrentProgrammingBlock].segmentInfo[0].address =
; ..\flash\FL.c	   710                                      FldownloadStatus.startAddr;
	ld.w	d15,[a2]
.L980:
	st.w	[a15]4,d15
.L981:

; ..\flash\FL.c	   711                  FldownloadStatus.segmentList[CurrentProgrammingBlock].segmentInfo[0].length =
; ..\flash\FL.c	   712                  						FldownloadStatus.length;
	ld.w	d15,[a4]
.L982:
	st.w	[a15]8,d15
.L983:
	j	.L69
.L68:
.L67:

; ..\flash\FL.c	   713              }
; ..\flash\FL.c	   714              else
; ..\flash\FL.c	   715              {
; ..\flash\FL.c	   716                  ret = FL_NO_FLASHDRIVER;
	mov	d8,#4
	j	.L70
.L66:

; ..\flash\FL.c	   717              }
; ..\flash\FL.c	   718          }
; ..\flash\FL.c	   719          else
; ..\flash\FL.c	   720          {
; ..\flash\FL.c	   721              /* check if download address is in correct range */
; ..\flash\FL.c	   722              ret = FL_CheckDownloadSegment();
	call	FL_CheckDownloadSegment
.L750:
	mov	d8,d2
.L752:

; ..\flash\FL.c	   723  
; ..\flash\FL.c	   724              if (FL_OK == ret)
	jne	d8,#0,.L71
.L984:

; ..\flash\FL.c	   725              {
; ..\flash\FL.c	   726                  ret = FL_HandleRemainData();
	call	FL_HandleRemainData
.L753:

; ..\flash\FL.c	   727                  /* Signature Header */
; ..\flash\FL.c	   728                  if(FldownloadStatus.blockIndex == 0)
	mov	d8,d2
	ld.bu	d15,[a12]13
.L754:
	lea	a15,[a12]1636
	ld.bu	d0,[a15]
.L985:
	jne	d15,#0,.L72
.L986:

; ..\flash\FL.c	   729                  {
; ..\flash\FL.c	   730                      /* set the download step*/
; ..\flash\FL.c	   731                      FldownloadStatus.downloadStep = FL_HEADER_TRASNFER_STEP;
	mov	d0,#1
	j	.L73
.L72:

; ..\flash\FL.c	   732                  }
; ..\flash\FL.c	   733                  /* APP File */
; ..\flash\FL.c	   734                  else if(FldownloadStatus.blockIndex == 1)
	eq	d15,d15,#1
.L987:

; ..\flash\FL.c	   735                  {
; ..\flash\FL.c	   736                      /* set the download step*/
; ..\flash\FL.c	   737                      FldownloadStatus.downloadStep = FL_TRANSFER_STEP;
	cmov	d0,d15,#3
.L73:
	st.b	[a15],d0
.L71:
.L70:
.L69:
.L65:

; ..\flash\FL.c	   738                  }                
; ..\flash\FL.c	   739  
; ..\flash\FL.c	   740              }
; ..\flash\FL.c	   741          }
; ..\flash\FL.c	   742      }
; ..\flash\FL.c	   743      
; ..\flash\FL.c	   744      if (ret != FL_OK)
	jeq	d8,#0,.L75
.L988:

; ..\flash\FL.c	   745      {
; ..\flash\FL.c	   746          /* initialize the flash download state */
; ..\flash\FL.c	   747          FL_InitState();
	call	FL_InitState
.L75:

; ..\flash\FL.c	   748      }
; ..\flash\FL.c	   749      
; ..\flash\FL.c	   750      FldownloadStatus.activeJob = FL_JOB_IDLE;
	mov	d15,#0
	st.b	[a12]1637,d15
.L989:

; ..\flash\FL.c	   751      FLERRStatus=ret;
	movh.a	a15,#@his(FLERRStatus)
.L990:
	st.b	[a15]@los(FLERRStatus),d8
.L991:

; ..\flash\FL.c	   752      return ret;
; ..\flash\FL.c	   753  }
	mov	d2,d8
	ret
.L592:
	
__FL_DownloadRequestValid_function_end:
	.size	FL_DownloadRequestValid,__FL_DownloadRequestValid_function_end-FL_DownloadRequestValid
.L333:
	; End of function
	
	.sdecl	'.text.FL.FL_FlashProgramRegion',code,cluster('FL_FlashProgramRegion')
	.sect	'.text.FL.FL_FlashProgramRegion'
	.align	2
	
	.global	FL_FlashProgramRegion

; ..\flash\FL.c	   754  
; ..\flash\FL.c	   755  /******************************************************************************/
; ..\flash\FL.c	   756  /**
; ..\flash\FL.c	   757   * @brief               <0x36 service download data>
; ..\flash\FL.c	   758   * 
; ..\flash\FL.c	   759   * <download data for current logical block> .
; ..\flash\FL.c	   760   * Service ID   :       <NONE>
; ..\flash\FL.c	   761   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   762   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   763   * @param[in]           <destAddr (IN), sourceBuff (IN), length (IN)>
; ..\flash\FL.c	   764   * @param[out]          <NONE>
; ..\flash\FL.c	   765   * @param[in/out]       <NONE>
; ..\flash\FL.c	   766   * @return              <FL_ResultType>    
; ..\flash\FL.c	   767   */
; ..\flash\FL.c	   768  /******************************************************************************/
; ..\flash\FL.c	   769  FL_ResultType FL_FlashProgramRegion(const uint32 destAddr,
; Function FL_FlashProgramRegion
.L218:
FL_FlashProgramRegion:	.type	func
	mov	d8,d5
	mov.aa	a5,a4
.L758:

; ..\flash\FL.c	   770      const uint8 *sourceBuff,
; ..\flash\FL.c	   771      const uint32 length)
; ..\flash\FL.c	   772  {
; ..\flash\FL.c	   773      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	   774      FL_ResultType result;
; ..\flash\FL.c	   775      /* change local address to global address*/
; ..\flash\FL.c	   776      uint32 globalAddr = destAddr;
; ..\flash\FL.c	   777      
; ..\flash\FL.c	   778      /* check the conditions of the program*/
; ..\flash\FL.c	   779      if ((FL_TRANSFER_STEP != FldownloadStatus.downloadStep) && (FL_HEADER_TRASNFER_STEP != FldownloadStatus.downloadStep))
	mov	d9,#0
	fcall	.cocofun_1
.L996:
	lea	a12,[a15]1636
	ld.bu	d15,[a12]
.L997:
	jeq	d15,#3,.L77
.L998:
	jeq	d15,#1,.L78
.L999:

; ..\flash\FL.c	   780      {
; ..\flash\FL.c	   781          ret = FL_ERR_SEQUENCE;
	mov	d9,#2
	j	.L79
.L78:
.L77:

; ..\flash\FL.c	   782      }
; ..\flash\FL.c	   783      else
; ..\flash\FL.c	   784      {
; ..\flash\FL.c	   785          if ((FldownloadStatus.startAddr != globalAddr) ||
	ld.w	d15,[a15]14
.L1000:
	jne	d15,d4,.L80
.L1001:

; ..\flash\FL.c	   786              (FldownloadStatus.length < length))
	ld.w	d0,[a15]18
.L1002:
	jge.u	d0,d8,.L81
.L80:

; ..\flash\FL.c	   787          {
; ..\flash\FL.c	   788              ret = FL_ERR_ADDR_LENGTH;
	mov	d9,#5
.L81:
.L79:

; ..\flash\FL.c	   789          }
; ..\flash\FL.c	   790      }
; ..\flash\FL.c	   791      
; ..\flash\FL.c	   792      if (FL_OK == ret)
	jne	d9,#0,.L82
.L1003:

; ..\flash\FL.c	   793      {
; ..\flash\FL.c	   794          /* check if flash driver is downloaded */
; ..\flash\FL.c	   795          if (FALSE == FldownloadStatus.flDrvDownloaded)
	ld.bu	d15,[a15]10
.L1004:
	jne	d15,#0,.L83
.L1005:

; ..\flash\FL.c	   796          {
; ..\flash\FL.c	   797              /* copy the data to the request address*/
; ..\flash\FL.c	   798              Appl_Memcpy((uint8 *)globalAddr, sourceBuff, (uint32)length);
	mov.a	a4,d4
.L757:
	mov	d4,d8
	call	Appl_Memcpy
.L756:

; ..\flash\FL.c	   799              
; ..\flash\FL.c	   800              /* index start address and length */
; ..\flash\FL.c	   801              FldownloadStatus.startAddr += length;
	ld.w	d0,[a15]14
.L1006:
	add	d0,d8
	st.w	[a15]14,d0
.L1007:

; ..\flash\FL.c	   802              FldownloadStatus.length -= length;
	ld.w	d15,[a15]18
.L1008:
	sub	d15,d8
	st.w	[a15]18,d15
.L1009:

; ..\flash\FL.c	   803              
; ..\flash\FL.c	   804              /* check if flash driver download is finished */
; ..\flash\FL.c	   805              if (0x00uL == FldownloadStatus.length)
	jne	d15,#0,.L84
.L1010:

; ..\flash\FL.c	   806              {
; ..\flash\FL.c	   807                  /* set the download step*/
; ..\flash\FL.c	   808                  FldownloadStatus.downloadStep = FL_EXIT_TRANSFER_STEP;
	mov	d15,#4
	st.b	[a12],d15
.L1011:

; ..\flash\FL.c	   809              }
; ..\flash\FL.c	   810              
; ..\flash\FL.c	   811              FldownloadStatus.activeJob = FL_JOB_IDLE;
	j	.L85
.L83:

; ..\flash\FL.c	   812          }
; ..\flash\FL.c	   813  		/* Header */
; ..\flash\FL.c	   814  		else if((globalAddr >= FL_BlkInfo[0].address) &&
	movh.a	a2,#@his(FL_BlkInfo)
	lea	a2,[a2]@los(FL_BlkInfo)
.L1012:
	ld.w	d0,[a2]
.L1013:
	jlt.u	d4,d0,.L86
.L1014:

; ..\flash\FL.c	   815                  (globalAddr < (FL_BlkInfo[0].address + FL_BlkInfo[0].length)))
	ld.w	d15,[a2]4
.L1015:
	add	d0,d15
.L1016:
	jge.u	d4,d0,.L87
.L1017:

; ..\flash\FL.c	   816  		{
; ..\flash\FL.c	   817              FBL_HeaderType = HEADER_TYPE_APP;
	movh.a	a2,#@his(FBL_HeaderType)
.L1018:
	mov	d5,#1
	st.b	[a2]@los(FBL_HeaderType),d5
.L760:

; ..\flash\FL.c	   818              result = Secure_SaveSignHeader(sourceBuff, length, FBL_HeaderType);
	extr.u	d4,d8,#0,#16
	mov.aa	a4,a5
.L759:
	call	Secure_SaveSignHeader
.L761:

; ..\flash\FL.c	   819              FldownloadStatus.startAddr += length;
	ld.w	d15,[a15]14
.L1019:
	add	d15,d8
	st.w	[a15]14,d15
.L1020:

; ..\flash\FL.c	   820              FldownloadStatus.length -= length;
	ld.w	d15,[a15]18
.L1021:
	sub	d15,d8
	st.w	[a15]18,d15
.L1022:

; ..\flash\FL.c	   821              if ((SECURE_SAVE_FINISH == result) && (0x00uL == FldownloadStatus.length))
	jne	d2,#3,.L88
.L1023:
	jne	d15,#0,.L89
.L1024:

; ..\flash\FL.c	   822              {
; ..\flash\FL.c	   823                  /* set the download step*/
; ..\flash\FL.c	   824                  FldownloadStatus.downloadStep = FL_HEADER_EXIT_TRANSFER_STEP;
	mov	d15,#2
	st.b	[a12],d15
.L1025:
	j	.L90
.L89:
.L88:

; ..\flash\FL.c	   825              }
; ..\flash\FL.c	   826              else if (SECURE_SAVE_OVERFLOW == result)
	eq	d15,d2,#4
.L1026:

; ..\flash\FL.c	   827              {
; ..\flash\FL.c	   828                  ret = FL_ERR_ADDR_LENGTH;
	cmov	d9,d15,#5
.L90:
.L85:
.L84:

; ..\flash\FL.c	   829              } 
; ..\flash\FL.c	   830              // else 
; ..\flash\FL.c	   831              // {
; ..\flash\FL.c	   832              //     ret = FL_DOWNLOAD_HEADER;
; ..\flash\FL.c	   833              // }
; ..\flash\FL.c	   834              FldownloadStatus.activeJob = FL_JOB_IDLE;
	fcall	.cocofun_4
.L762:
	j	.L92
.L87:
.L86:

; ..\flash\FL.c	   835  
; ..\flash\FL.c	   836  			// Appl_Memcpy( &FL_Header_Buffer[globalAddr], sourceBuff, (uint32)length );
; ..\flash\FL.c	   837  			// FldownloadStatus.startAddr += length;
; ..\flash\FL.c	   838              // FldownloadStatus.length -= length;
; ..\flash\FL.c	   839  			// if (0x00uL == FldownloadStatus.length)
; ..\flash\FL.c	   840              // {
; ..\flash\FL.c	   841              //     /* set the download step*/
; ..\flash\FL.c	   842              //     FldownloadStatus.downloadStep = FL_EXIT_TRANSFER_STEP;
; ..\flash\FL.c	   843              // }
; ..\flash\FL.c	   844              // FldownloadStatus.activeJob = FL_JOB_IDLE;
; ..\flash\FL.c	   845  		}
; ..\flash\FL.c	   846          else
; ..\flash\FL.c	   847          {
; ..\flash\FL.c	   848              /* save parameter for program active job */
; ..\flash\FL.c	   849              FldownloadStatus.dataBuff = sourceBuff;
	st.a	[a15]24,a5
.L1027:

; ..\flash\FL.c	   850              FlProgramLength = length;
	movh.a	a2,#@his(FlProgramLength)
.L1028:
	st.w	[a2]@los(FlProgramLength),d8
.L1029:

; ..\flash\FL.c	   851              
; ..\flash\FL.c	   852              /* set the download active state to program*/
; ..\flash\FL.c	   853              FldownloadStatus.activeJob = FL_JOB_PROGRAMMING;
	mov	d15,#2
	st.b	[a15]1637,d15
.L1030:

; ..\flash\FL.c	   854              FldownloadStatus.errorCode = FL_OK;
	mov	d15,#0
	st.b	[a15]11,d15
.L92:
.L82:

; ..\flash\FL.c	   855          }
; ..\flash\FL.c	   856      }
; ..\flash\FL.c	   857  
; ..\flash\FL.c	   858      if (ret != FL_OK)
	jeq	d9,#0,.L93
.L1031:

; ..\flash\FL.c	   859      {
; ..\flash\FL.c	   860          /* initialize the flash download state */
; ..\flash\FL.c	   861          FL_InitState();
	call	FL_InitState
.L93:

; ..\flash\FL.c	   862      }
; ..\flash\FL.c	   863      
; ..\flash\FL.c	   864      return ret;
; ..\flash\FL.c	   865  }
	mov	d2,d9
	ret
.L598:
	
__FL_FlashProgramRegion_function_end:
	.size	FL_FlashProgramRegion,__FL_FlashProgramRegion_function_end-FL_FlashProgramRegion
.L338:
	; End of function
	
	.sdecl	'.text.FL..cocofun_4',code,cluster('.cocofun_4')
	.sect	'.text.FL..cocofun_4'
	.align	2
; Function .cocofun_4
.L220:
.cocofun_4:	.type	func
; Function body .cocofun_4, coco_iter:0
	mov	d15,#0
	st.b	[a15]1637,d15
.L763:
	fret
.L518:
	; End of function
	.sdecl	'.text.FL.FL_SetExitTransferStep',code,cluster('FL_SetExitTransferStep')
	.sect	'.text.FL.FL_SetExitTransferStep'
	.align	2
	
	.global	FL_SetExitTransferStep

; ..\flash\FL.c	   866  
; ..\flash\FL.c	   867  /******************************************************************************/
; ..\flash\FL.c	   868  /**
; ..\flash\FL.c	   869   * @brief               <0x36 service download data>
; ..\flash\FL.c	   870   *
; ..\flash\FL.c	   871   * <download data for current logical block> .
; ..\flash\FL.c	   872   * Service ID   :       <NONE>
; ..\flash\FL.c	   873   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   874   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   875   * @param[in]           <destAddr (IN), sourceBuff (IN), length (IN)>
; ..\flash\FL.c	   876   * @param[out]          <NONE>
; ..\flash\FL.c	   877   * @param[in/out]       <NONE>
; ..\flash\FL.c	   878   * @return              <FL_ResultType>
; ..\flash\FL.c	   879   */
; ..\flash\FL.c	   880  /******************************************************************************/
; ..\flash\FL.c	   881  void FL_SetExitTransferStep(void)
; Function FL_SetExitTransferStep
.L222:
FL_SetExitTransferStep:	.type	func

; ..\flash\FL.c	   882  {
; ..\flash\FL.c	   883  	FldownloadStatus.downloadStep = FL_EXIT_TRANSFER_STEP;
	fcall	.cocofun_1
.L1036:
	mov	d15,#4
	st.b	[a15]1636,d15
.L1037:

; ..\flash\FL.c	   884  	FldownloadStatus.activeJob = FL_JOB_IDLE;
	fcall	.cocofun_4
.L1038:

; ..\flash\FL.c	   885  }
	ret
.L608:
	
__FL_SetExitTransferStep_function_end:
	.size	FL_SetExitTransferStep,__FL_SetExitTransferStep_function_end-FL_SetExitTransferStep
.L343:
	; End of function
	
	.sdecl	'.text.FL.FL_SetHeadBlockErased',code,cluster('FL_SetHeadBlockErased')
	.sect	'.text.FL.FL_SetHeadBlockErased'
	.align	2
	
	.global	FL_SetHeadBlockErased

; ..\flash\FL.c	   886  
; ..\flash\FL.c	   887  /******************************************************************************/
; ..\flash\FL.c	   888  /**
; ..\flash\FL.c	   889   * @brief               <0x36 service download data>
; ..\flash\FL.c	   890   *
; ..\flash\FL.c	   891   * <download data for current logical block> .
; ..\flash\FL.c	   892   * Service ID   :       <NONE>
; ..\flash\FL.c	   893   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   894   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   895   * @param[in]           <destAddr (IN), sourceBuff (IN), length (IN)>
; ..\flash\FL.c	   896   * @param[out]          <NONE>
; ..\flash\FL.c	   897   * @param[in/out]       <NONE>
; ..\flash\FL.c	   898   * @return              <FL_ResultType>
; ..\flash\FL.c	   899   */
; ..\flash\FL.c	   900  /******************************************************************************/
; ..\flash\FL.c	   901  void FL_SetHeadBlockErased(void)
; Function FL_SetHeadBlockErased
.L224:
FL_SetHeadBlockErased:	.type	func

; ..\flash\FL.c	   902  {
; ..\flash\FL.c	   903  	FldownloadStatus.blockErased = TRUE;
	fcall	.cocofun_1
.L1043:
	mov	d15,#1
	st.b	[a15]12,d15
.L1044:

; ..\flash\FL.c	   904  	FldownloadStatus.activeJob = FL_JOB_IDLE;
	fcall	.cocofun_4
.L1045:

; ..\flash\FL.c	   905  	FldownloadStatus.errorCode = FL_OK;
	st.b	[a15]11,d15
.L1046:

; ..\flash\FL.c	   906  }
	ret
.L609:
	
__FL_SetHeadBlockErased_function_end:
	.size	FL_SetHeadBlockErased,__FL_SetHeadBlockErased_function_end-FL_SetHeadBlockErased
.L348:
	; End of function
	
	.sdecl	'.text.FL.FL_ExitTransferData',code,cluster('FL_ExitTransferData')
	.sect	'.text.FL.FL_ExitTransferData'
	.align	2
	
	.global	FL_ExitTransferData

; ..\flash\FL.c	   907  
; ..\flash\FL.c	   908  /******************************************************************************/
; ..\flash\FL.c	   909  /**
; ..\flash\FL.c	   910   * @brief               <0x37 service download finish>
; ..\flash\FL.c	   911   * 
; ..\flash\FL.c	   912   * <download finish for current logical block> .
; ..\flash\FL.c	   913   * Service ID   :       <NONE>
; ..\flash\FL.c	   914   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   915   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   916   * @param[in]           <NONE>
; ..\flash\FL.c	   917   * @param[out]          <NONE>
; ..\flash\FL.c	   918   * @param[in/out]       <NONE>
; ..\flash\FL.c	   919   * @return              <FL_ResultType>    
; ..\flash\FL.c	   920   */
; ..\flash\FL.c	   921  /******************************************************************************/
; ..\flash\FL.c	   922  FL_ResultType FL_ExitTransferData(void)
; Function FL_ExitTransferData
.L226:
FL_ExitTransferData:	.type	func

; ..\flash\FL.c	   923  {
; ..\flash\FL.c	   924      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	   925      FL_ResultType signVerifRet;
; ..\flash\FL.c	   926      if (FL_EXIT_TRANSFER_STEP == FldownloadStatus.downloadStep)
	mov	d8,#0
	fcall	.cocofun_1
.L1051:
	lea	a2,[a15]1636
	ld.bu	d15,[a2]
.L1052:
	jne	d15,#4,.L95
.L1053:

; ..\flash\FL.c	   927      {
; ..\flash\FL.c	   928          /* set the download step*/
; ..\flash\FL.c	   929          FldownloadStatus.downloadStep = FL_CHECKSUM_STEP;
	mov	d15,#5
	st.b	[a2],d15
.L1054:

; ..\flash\FL.c	   930          FldownloadStatus.activeJob = FL_JOB_IDLE;
	st.b	[a15]1637,d8
.L1055:
	j	.L96
.L95:

; ..\flash\FL.c	   931      }
; ..\flash\FL.c	   932      else if (FL_HEADER_EXIT_TRANSFER_STEP == FldownloadStatus.downloadStep)
	jne	d15,#2,.L97
.L1056:

; ..\flash\FL.c	   933      {
; ..\flash\FL.c	   934          /* Sign Verification */
; ..\flash\FL.c	   935           signVerifRet = Secure_SignHeaderChk(&fblFileIndex);
	movh.a	a4,#@his(fblFileIndex)
	lea	a4,[a4]@los(fblFileIndex)
	call	Secure_SignHeaderChk
.L764:

; ..\flash\FL.c	   936  //        signVerifRet = SECURE_PASS;
; ..\flash\FL.c	   937          if(SECURE_ERRORSEQUENCE == signVerifRet)
	mov	d15,#16
.L1057:
	jne	d15,d2,.L98
.L1058:

; ..\flash\FL.c	   938          {
; ..\flash\FL.c	   939              ret = FL_ERR_SEQUENCE;
	mov	d8,#2
	j	.L99
.L98:

; ..\flash\FL.c	   940          }
; ..\flash\FL.c	   941          else
; ..\flash\FL.c	   942          {
; ..\flash\FL.c	   943              ret = signVerifRet + FBL_SECURE_NRC_OFFSET;
	add	d15,d2,#16
	extr.u	d8,d15,#0,#8
.L99:

; ..\flash\FL.c	   944          }
; ..\flash\FL.c	   945              
; ..\flash\FL.c	   946          if(SECURE_PASS == signVerifRet) 
	jne	d2,#0,.L100
.L1059:

; ..\flash\FL.c	   947          {
; ..\flash\FL.c	   948              FBL_SignVerifFlags[0] = FBL_TRUE;
	movh.a	a15,#@his(FBL_SignVerifFlags)
.L1060:
	mov	d15,#1
	st.b	[a15]@los(FBL_SignVerifFlags),d15
.L1061:

; ..\flash\FL.c	   949              ret = FL_OK;
	mov	d8,#0
	j	.L101
.L100:

; ..\flash\FL.c	   950          } 
; ..\flash\FL.c	   951          else
; ..\flash\FL.c	   952          {
; ..\flash\FL.c	   953              Secure_Init();
	call	Secure_Init
.L765:
	j	.L102
.L97:

; ..\flash\FL.c	   954          }   
; ..\flash\FL.c	   955          /* set the request step*/
; ..\flash\FL.c	   956          // FldownloadStatus.downloadStep = FL_REQUEST_STEP;         
; ..\flash\FL.c	   957          // FldownloadStatus.activeJob = FL_JOB_IDLE;
; ..\flash\FL.c	   958      }
; ..\flash\FL.c	   959      else
; ..\flash\FL.c	   960      {
; ..\flash\FL.c	   961          /* initialize the flash download state */
; ..\flash\FL.c	   962          ret = FL_ERR_SEQUENCE;
; ..\flash\FL.c	   963          FL_InitState();
	mov	d8,#2
	call	FL_InitState
.L102:
.L101:
.L96:

; ..\flash\FL.c	   964      }
; ..\flash\FL.c	   965      
; ..\flash\FL.c	   966      return ret;
; ..\flash\FL.c	   967  }
	mov	d2,d8
	ret
.L610:
	
__FL_ExitTransferData_function_end:
	.size	FL_ExitTransferData,__FL_ExitTransferData_function_end-FL_ExitTransferData
.L353:
	; End of function
	
	.sdecl	'.text.FL.FL_ServiceFinished',code,cluster('FL_ServiceFinished')
	.sect	'.text.FL.FL_ServiceFinished'
	.align	2
	
	.global	FL_ServiceFinished

; ..\flash\FL.c	   968  
; ..\flash\FL.c	   969  /******************************************************************************/
; ..\flash\FL.c	   970  /**
; ..\flash\FL.c	   971   * @brief               <get service status>
; ..\flash\FL.c	   972   * 
; ..\flash\FL.c	   973   * <supply active job status for service witch can not response with in 50ms,
; ..\flash\FL.c	   974   *  and send pending> .
; ..\flash\FL.c	   975   * Service ID   :       <NONE>
; ..\flash\FL.c	   976   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	   977   * Reentrancy           <NON Reentrant>
; ..\flash\FL.c	   978   * @param[in]           <NONE>
; ..\flash\FL.c	   979   * @param[out]          <error (OUT)>
; ..\flash\FL.c	   980   * @param[in/out]       <NONE>
; ..\flash\FL.c	   981   * @return              <boolean>    
; ..\flash\FL.c	   982   */
; ..\flash\FL.c	   983  /******************************************************************************/
; ..\flash\FL.c	   984  boolean FL_ServiceFinished(FL_ResultType *error)
; Function FL_ServiceFinished
.L228:
FL_ServiceFinished:	.type	func

; ..\flash\FL.c	   985  {
; ..\flash\FL.c	   986      boolean ret = FALSE;
; ..\flash\FL.c	   987      
; ..\flash\FL.c	   988      /* check if service is finished */
; ..\flash\FL.c	   989      if (FL_JOB_IDLE == FldownloadStatus.activeJob)
	fcall	.cocofun_1
.L766:
	ld.bu	d15,[a15]1637
.L1066:
	jne	d15,#0,.L104
.L1067:

; ..\flash\FL.c	   990      {
; ..\flash\FL.c	   991          *error = FldownloadStatus.errorCode;
	ld.bu	d15,[a15]11
.L1068:
	st.b	[a4],d15
.L1069:

; ..\flash\FL.c	   992          ret = TRUE;
; ..\flash\FL.c	   993      }
; ..\flash\FL.c	   994      else
; ..\flash\FL.c	   995      {
; ..\flash\FL.c	   996          *error = FL_OK;
; ..\flash\FL.c	   997          ret = FALSE;
; ..\flash\FL.c	   998      }
; ..\flash\FL.c	   999      
; ..\flash\FL.c	  1000      return ret;
; ..\flash\FL.c	  1001  }
	mov	d2,#1
	ret
.L104:
	mov	d2,#0
	st.b	[a4],d2
.L767:
	ret
.L613:
	
__FL_ServiceFinished_function_end:
	.size	FL_ServiceFinished,__FL_ServiceFinished_function_end-FL_ServiceFinished
.L358:
	; End of function
	
	.sdecl	'.text.FL.SetS37JobStatusBusy',code,cluster('SetS37JobStatusBusy')
	.sect	'.text.FL.SetS37JobStatusBusy'
	.align	2
	
	.global	SetS37JobStatusBusy

; ..\flash\FL.c	  1002  
; ..\flash\FL.c	  1003  /******************************************************************************/
; ..\flash\FL.c	  1004  /**
; ..\flash\FL.c	  1005   * 锟斤拷锟斤拷锟斤拷锟藉：使锟斤拷锟斤拷锟斤拷37锟斤拷锟斤拷锟斤拷busy状态
; ..\flash\FL.c	  1006   *
; ..\flash\FL.c	  1007   */
; ..\flash\FL.c	  1008  /******************************************************************************/
; ..\flash\FL.c	  1009  void SetS37JobStatusBusy(void)
; Function SetS37JobStatusBusy
.L230:
SetS37JobStatusBusy:	.type	func

; ..\flash\FL.c	  1010  {
; ..\flash\FL.c	  1011  	FldownloadStatus.activeJob=FL_JOB_S37CHECKING;
	movh.a	a15,#@his(FldownloadStatus+1637)
.L1139:
	mov	d15,#4
	st.b	[a15]@los(FldownloadStatus+1637),d15
.L1140:

; ..\flash\FL.c	  1012  }
	ret
.L635:
	
__SetS37JobStatusBusy_function_end:
	.size	SetS37JobStatusBusy,__SetS37JobStatusBusy_function_end-SetS37JobStatusBusy
.L378:
	; End of function
	
	.sdecl	'.text.FL.GetS37JobStatusResult',code,cluster('GetS37JobStatusResult')
	.sect	'.text.FL.GetS37JobStatusResult'
	.align	2
	
	.global	GetS37JobStatusResult

; ..\flash\FL.c	  1013  
; ..\flash\FL.c	  1014  /******************************************************************************/
; ..\flash\FL.c	  1015  /**
; ..\flash\FL.c	  1016   * 锟斤拷锟斤拷锟斤拷锟藉：锟斤拷取37锟斤拷锟斤拷前状态
; ..\flash\FL.c	  1017   *
; ..\flash\FL.c	  1018   */
; ..\flash\FL.c	  1019  /******************************************************************************/
; ..\flash\FL.c	  1020  void GetS37JobStatusResult(FL_ResultType status)
; Function GetS37JobStatusResult
.L232:
GetS37JobStatusResult:	.type	func

; ..\flash\FL.c	  1021  {
; ..\flash\FL.c	  1022  	FldownloadStatus.activeJob=FL_JOB_IDLE;
	fcall	.cocofun_1
.L1145:
	fcall	.cocofun_4
.L768:

; ..\flash\FL.c	  1023  	FldownloadStatus.errorCode = status;
	st.b	[a15]11,d4
.L1146:

; ..\flash\FL.c	  1024  }
	ret
.L636:
	
__GetS37JobStatusResult_function_end:
	.size	GetS37JobStatusResult,__GetS37JobStatusResult_function_end-GetS37JobStatusResult
.L383:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckSumFor37',code,cluster('FL_CheckSumFor37')
	.sect	'.text.FL.FL_CheckSumFor37'
	.align	2
	
	.global	FL_CheckSumFor37

; ..\flash\FL.c	  1025  
; ..\flash\FL.c	  1026  /******************************************************************************/
; ..\flash\FL.c	  1027  /**
; ..\flash\FL.c	  1028   * 锟斤拷锟斤拷锟斤拷锟藉：锟斤拷锟斤拷37锟斤拷锟斤拷锟紺RC32校锟斤拷
; ..\flash\FL.c	  1029   *
; ..\flash\FL.c	  1030   */
; ..\flash\FL.c	  1031  /******************************************************************************/
; ..\flash\FL.c	  1032  boolean headRemainDataDownloaded = FALSE;
; ..\flash\FL.c	  1033  FL_ResultType FL_CheckSumFor37(SecM_CRCType* crc)
; Function FL_CheckSumFor37
.L234:
FL_CheckSumFor37:	.type	func
	lea	a10,[a10]-816
.L769:

; ..\flash\FL.c	  1034  {
; ..\flash\FL.c	  1035  	FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  1036  	SecM_VerifyParamType verifyParam;
; ..\flash\FL.c	  1037  	SecM_StatusType crcret=SECM_OK;
; ..\flash\FL.c	  1038  	uint16 l_nrOfSegments;
; ..\flash\FL.c	  1039  	uint32 currentaddr,currentlength;
; ..\flash\FL.c	  1040  	FL_SegmentInfoType remainsegment;
; ..\flash\FL.c	  1041  
; ..\flash\FL.c	  1042  	/*add by fhq 20210326*/
; ..\flash\FL.c	  1043  	FL_SegmentListType segmentList;
; ..\flash\FL.c	  1044  	verifyParam.segmentList=&segmentList;
	lea	a15,[a10]12
.L1091:
	st.a	[a10],a15
.L1092:

; ..\flash\FL.c	  1045  
; ..\flash\FL.c	  1046  	if(EXTERNAL_FLS==FL_BlkInfo[FldownloadStatus.blockIndex].flashtype)
	movh.a	a2,#@his(FldownloadStatus)
	lea	a2,[a2]@los(FldownloadStatus)
.L1093:
	ld.bu	d15,[a2]13
.L1094:
	mov	d8,#0
	mov.aa	a12,a4
.L771:
	mul	d15,d15,#24
	fcall	.cocofun_3
.L1095:
	addsc.a	a15,a15,d15,#0
.L1096:
	ld.bu	d15,[a15]8
.L1097:
	jne	d15,#1,.L107
.L1098:

; ..\flash\FL.c	  1047  	{
; ..\flash\FL.c	  1048  
; ..\flash\FL.c	  1049  //		if(FL_RemainDataStruct.remainLength>0)
; ..\flash\FL.c	  1050  //		{
; ..\flash\FL.c	  1051  //			remainsegment.address=(uint32)(&FlProgramData[0]);
; ..\flash\FL.c	  1052  //			remainsegment.length=FL_RemainDataStruct.remainLength;
; ..\flash\FL.c	  1053  //			crcret=extFls_CheckSumFor37(crc,&remainsegment);
; ..\flash\FL.c	  1054  //		}
; ..\flash\FL.c	  1055  //		else
; ..\flash\FL.c	  1056  //		{
; ..\flash\FL.c	  1057  //			crcret=extFls_CheckSumFor37(crc,NULL);
; ..\flash\FL.c	  1058  //		}
; ..\flash\FL.c	  1059  //
; ..\flash\FL.c	  1060  //		if(crcret==SECM_OK)
; ..\flash\FL.c	  1061  //		{
; ..\flash\FL.c	  1062  //			return FL_OK;
; ..\flash\FL.c	  1063  //		}
; ..\flash\FL.c	  1064  //		else
; ..\flash\FL.c	  1065  //		{
; ..\flash\FL.c	  1066  //			return FL_FAILED;
; ..\flash\FL.c	  1067  //		}
; ..\flash\FL.c	  1068          if (FALSE == headRemainDataDownloaded)
	movh.a	a15,#@his(headRemainDataDownloaded)
	lea	a15,[a15]@los(headRemainDataDownloaded)
	ld.bu	d15,[a15]
.L1099:
	jne	d15,#0,.L108
.L1100:

; ..\flash\FL.c	  1069  		{
; ..\flash\FL.c	  1070          	headRemainDataDownloaded = TRUE;
	mov	d15,#1
	st.b	[a15],d15
.L108:
.L107:

; ..\flash\FL.c	  1071  		}
; ..\flash\FL.c	  1072  
; ..\flash\FL.c	  1073  	}
; ..\flash\FL.c	  1074  
; ..\flash\FL.c	  1075  	l_nrOfSegments= FldownloadStatus.segmentList[CurrentProgrammingBlock].nrOfSegments;
	movh.a	a15,#@his(CurrentProgrammingBlock)
	ld.bu	d15,[a15]@los(CurrentProgrammingBlock)
.L1101:
	mov	d0,#804
	mul	d15,d0
	addsc.a	a15,a2,d15,#0
.L1102:
	ld.hu	d15,[+a15]28
.L772:

; ..\flash\FL.c	  1076  	currentaddr=FldownloadStatus.segmentList[CurrentProgrammingBlock].segmentInfo[l_nrOfSegments-1].address;
	addsc.a	a15,a15,d15,#3
.L1103:
	ld.w	d0,[a15]-4
.L774:

; ..\flash\FL.c	  1077  	currentlength=FldownloadStatus.segmentList[CurrentProgrammingBlock].segmentInfo[l_nrOfSegments-1].length;
	ld.w	d1,[a15]
.L776:

; ..\flash\FL.c	  1078  
; ..\flash\FL.c	  1079  
; ..\flash\FL.c	  1080  	if(FL_RemainDataStruct.remainLength>0)//锟斤拷锟斤拷锟斤拷未写锟斤拷flash
	movh.a	a15,#@his(FL_RemainDataStruct)
	lea	a15,[a15]@los(FL_RemainDataStruct)
.L1104:
	ld.w	d15,[a15]4
.L773:
	jeq	d15,#0,.L109
.L1105:

; ..\flash\FL.c	  1081  	{
; ..\flash\FL.c	  1082  		if(currentaddr<FL_RemainDataStruct.remainAddr)//flash锟斤拷ram锟叫讹拷锟斤拷锟斤拷锟斤拷
	ld.w	d2,[a15]
.L1106:

; ..\flash\FL.c	  1083  		{
; ..\flash\FL.c	  1084  			verifyParam.segmentList->nrOfSegments=2;
; ..\flash\FL.c	  1085  			verifyParam.segmentList->segmentInfo[0].address=currentaddr;
; ..\flash\FL.c	  1086  			verifyParam.segmentList->segmentInfo[0].length=FL_RemainDataStruct.remainAddr-currentaddr;
; ..\flash\FL.c	  1087  
; ..\flash\FL.c	  1088  			verifyParam.segmentList->segmentInfo[1].address=(uint32)(&FlProgramData[0]);
	movh.a	a2,#@his(FlProgramData)
	lea	a2,[a2]@los(FlProgramData)
.L1107:
	jge.u	d0,d2,.L110
.L1108:
	sub	d1,d2,d0
	mov.d	d2,a2
.L1109:
	mov	d3,#2
	st.w	[a10]24,d2
.L1110:

; ..\flash\FL.c	  1089  			verifyParam.segmentList->segmentInfo[1].length=FL_RemainDataStruct.remainLength;
	st.w	[a10]28,d15
.L1111:
	j	.L111
.L110:

; ..\flash\FL.c	  1090  		}
; ..\flash\FL.c	  1091  		else//只锟斤拷ram锟斤拷锟斤拷锟斤拷锟斤拷
; ..\flash\FL.c	  1092  		{
; ..\flash\FL.c	  1093  			verifyParam.segmentList->nrOfSegments=1;
; ..\flash\FL.c	  1094  			verifyParam.segmentList->segmentInfo[0].address=(uint32)(&FlProgramData[FL_RemainDataStruct.remainLength-currentlength]);
	sub	d15,d1
.L1112:
	addsc.a	a15,a2,d15,#0
.L775:
	mov	d3,#1
.L1113:
	mov.d	d0,a15
.L778:
	j	.L112
.L109:

; ..\flash\FL.c	  1095  			verifyParam.segmentList->segmentInfo[0].length=currentlength;
; ..\flash\FL.c	  1096  		}
; ..\flash\FL.c	  1097  	}
; ..\flash\FL.c	  1098  	else//锟斤拷锟斤拷锟斤拷锟捷讹拷锟窖撅拷写锟斤拷锟斤拷flash
; ..\flash\FL.c	  1099  	{
; ..\flash\FL.c	  1100  		verifyParam.segmentList->nrOfSegments=1;
	mov	d3,#1
.L112:
.L111:

; ..\flash\FL.c	  1101  		verifyParam.segmentList->segmentInfo[0].address=currentaddr;
; ..\flash\FL.c	  1102  		verifyParam.segmentList->segmentInfo[0].length=currentlength;
; ..\flash\FL.c	  1103  	}
; ..\flash\FL.c	  1104  
; ..\flash\FL.c	  1105  	crcret=SecM_CRC_Calculate(&verifyParam);
	st.w	[a10]20,d1
	st.w	[a10]16,d0
	st.h	[a10]12,d3
.L1114:
	mov.aa	a4,a10
.L770:
	call	SecM_CRC_Calculate
.L777:

; ..\flash\FL.c	  1106  	if(crcret==SECM_OK)
	jne	d2,#0,.L113
.L1115:

; ..\flash\FL.c	  1107  	{
; ..\flash\FL.c	  1108  		*crc=verifyParam.crcTotle;
	ld.hu	d15,[a10]8
.L1116:
	st.h	[a12],d15
.L1117:
	j	.L114
.L113:

; ..\flash\FL.c	  1109  
; ..\flash\FL.c	  1110  	}
; ..\flash\FL.c	  1111  	else
; ..\flash\FL.c	  1112  	{
; ..\flash\FL.c	  1113  		ret=FL_FAILED;
	mov	d8,#1
.L114:

; ..\flash\FL.c	  1114  
; ..\flash\FL.c	  1115  	}
; ..\flash\FL.c	  1116  
; ..\flash\FL.c	  1117  	return ret;
; ..\flash\FL.c	  1118  }
	mov	d2,d8
	ret
.L618:
	
__FL_CheckSumFor37_function_end:
	.size	FL_CheckSumFor37,__FL_CheckSumFor37_function_end-FL_CheckSumFor37
.L368:
	; End of function
	
	.sdecl	'.text.FL.FBL_CheckSumFor37',code,cluster('FBL_CheckSumFor37')
	.sect	'.text.FL.FBL_CheckSumFor37'
	.align	2
	
	.global	FBL_CheckSumFor37

; ..\flash\FL.c	  1119  FL_ResultType FBL_CheckSumFor37(uint16 * crcValPtr)
; Function FBL_CheckSumFor37
.L236:
FBL_CheckSumFor37:	.type	func

; ..\flash\FL.c	  1120  {
; ..\flash\FL.c	  1121      FL_ResultType ret;
; ..\flash\FL.c	  1122      /* Try: Assume that the first 34 sends the address of corresponding block */
; ..\flash\FL.c	  1123      if ((FBL_TRUE == FBL_SignVerifFlags[0]) && (FL_HEADER_EXIT_TRANSFER_STEP == FldownloadStatus.downloadStep))
	movh.a	a15,#@his(FBL_SignVerifFlags)
.L1122:
	ld.bu	d15,[a15]@los(FBL_SignVerifFlags)
.L1123:
	jne	d15,#1,.L116
.L1124:
	movh.a	a15,#@his(FldownloadStatus+1636)
.L1125:
	lea	a15,[a15]@los(FldownloadStatus+1636)
	ld.bu	d15,[a15]
.L1126:
	jne	d15,#2,.L117
.L1127:

; ..\flash\FL.c	  1124      {
; ..\flash\FL.c	  1125          ret = Secure_CheckSumForHeader(crcValPtr);
	call	Secure_CheckSumForHeader
.L779:
	mov	d8,d2
.L781:

; ..\flash\FL.c	  1126          FldownloadStatus.downloadStep = FL_REQUEST_STEP;
	mov	d15,#0
	st.b	[a15],d15
.L1128:

; ..\flash\FL.c	  1127          Secure_Init();
	call	Secure_Init
.L780:

; ..\flash\FL.c	  1128          if (FALSE == headRemainDataDownloaded)
	movh.a	a15,#@his(headRemainDataDownloaded)
	lea	a15,[a15]@los(headRemainDataDownloaded)
	ld.bu	d15,[a15]
.L1129:
	jne	d15,#0,.L118
.L1130:

; ..\flash\FL.c	  1129  		{
; ..\flash\FL.c	  1130          	headRemainDataDownloaded = TRUE;
	mov	d15,#1
	j	.L119
.L117:
.L116:
	movh.a	a15,#@his(FldownloadStatus+1636)
.L1131:
	lea	a15,[a15]@los(FldownloadStatus+1636)
.L1132:

; ..\flash\FL.c	  1131  		}
; ..\flash\FL.c	  1132      } 
; ..\flash\FL.c	  1133      else if (FL_CHECKSUM_STEP == FldownloadStatus.downloadStep)
	ld.bu	d15,[a15]
.L1133:
	jne	d15,#5,.L120
.L1134:

; ..\flash\FL.c	  1134      {   
; ..\flash\FL.c	  1135          ret = FL_CheckSumFor37(crcValPtr);
	call	FL_CheckSumFor37
.L782:
	mov	d8,d2
	j	.L121
.L120:

; ..\flash\FL.c	  1136      } 
; ..\flash\FL.c	  1137      else 
; ..\flash\FL.c	  1138      {
; ..\flash\FL.c	  1139              ret = FL_ERR_SEQUENCE;
	mov	d8,#2
.L783:

; ..\flash\FL.c	  1140              FldownloadStatus.downloadStep = FL_REQUEST_STEP;
	mov	d15,#0
.L119:
	st.b	[a15],d15
.L121:
.L118:

; ..\flash\FL.c	  1141      }
; ..\flash\FL.c	  1142      return ret;
; ..\flash\FL.c	  1143  }
	mov	d2,d8
	ret
.L631:
	
__FBL_CheckSumFor37_function_end:
	.size	FBL_CheckSumFor37,__FBL_CheckSumFor37_function_end-FBL_CheckSumFor37
.L373:
	; End of function
	
	.sdecl	'.text.FL.FL_Get4Byte',code,cluster('FL_Get4Byte')
	.sect	'.text.FL.FL_Get4Byte'
	.align	2
	

; ..\flash\FL.c	  1144  /******************************************************************************/
; ..\flash\FL.c	  1145  /**
; ..\flash\FL.c	  1146   * @brief               <get uint32 from data buffer>
; ..\flash\FL.c	  1147   * 
; ..\flash\FL.c	  1148   * <get uint32 from data buffer> .
; ..\flash\FL.c	  1149   * Service ID   :       <NONE>
; ..\flash\FL.c	  1150   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	  1151   * Reentrancy           <Reentrant>
; ..\flash\FL.c	  1152   * @param[in]           <data (IN)>
; ..\flash\FL.c	  1153   * @param[out]          <NONE>
; ..\flash\FL.c	  1154   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1155   * @return              <uint32>    
; ..\flash\FL.c	  1156   */
; ..\flash\FL.c	  1157  /******************************************************************************/
; ..\flash\FL.c	  1158  STATIC uint32 FL_Get4Byte(const uint8* data)
; Function FL_Get4Byte
.L238:
FL_Get4Byte:	.type	func

; ..\flash\FL.c	  1159  {
; ..\flash\FL.c	  1160      uint32 retData;
; ..\flash\FL.c	  1161      
; ..\flash\FL.c	  1162      retData = ((uint32)data[0]) << 24;
	ld.bu	d15,[a4]
.L1471:

; ..\flash\FL.c	  1163      retData += ((uint32)data[1]) << 16;
	sh	d0,d15,#24
	ld.bu	d15,[a4]1
.L1472:
	sh	d15,d15,#16
.L1473:

; ..\flash\FL.c	  1164      retData += ((uint32)data[2]) << 8;
	add	d0,d15
	ld.bu	d15,[a4]2
.L1474:
	sh	d15,d15,#8
.L1475:

; ..\flash\FL.c	  1165      retData += (uint32)data[3];
	add	d0,d15
	ld.bu	d15,[a4]3
.L1476:

; ..\flash\FL.c	  1166      
; ..\flash\FL.c	  1167      return retData;
; ..\flash\FL.c	  1168  }
	add	d2,d0,d15
	ret
.L693:
	
__FL_Get4Byte_function_end:
	.size	FL_Get4Byte,__FL_Get4Byte_function_end-FL_Get4Byte
.L493:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckModuleId',code,cluster('FL_CheckModuleId')
	.sect	'.text.FL.FL_CheckModuleId'
	.align	2
	
	.global	FL_CheckModuleId

; ..\flash\FL.c	  1169  
; ..\flash\FL.c	  1170  FL_ResultType FL_CheckModuleId(uint8 moduleId, uint8* locationInfo, uint8* idxRet)
; Function FL_CheckModuleId
.L240:
FL_CheckModuleId:	.type	func
	mov.aa	a15,a4
.L786:
	mov.aa	a12,a5
.L789:

; ..\flash\FL.c	  1171  {
; ..\flash\FL.c	  1172      FL_ResultType result = FL_OUT_OF_RANGE;
; ..\flash\FL.c	  1173      uint32 locAdr;
; ..\flash\FL.c	  1174      uint32 locLength;
; ..\flash\FL.c	  1175      uint8 index = 1;
; ..\flash\FL.c	  1176  
; ..\flash\FL.c	  1177      locAdr = FL_Get4Byte(&locationInfo[2]);
	lea	a4,[a15]2
.L785:
	call	FL_Get4Byte
.L784:

; ..\flash\FL.c	  1178      locAdr=locAdr|0xa0000000;
; ..\flash\FL.c	  1179      locLength = FL_Get4Byte(&locationInfo[6]);
	movh	d8,#40960
	add.a	a15,#6
.L790:
	or	d8,d2
	mov.aa	a4,a15
.L788:
	call	FL_Get4Byte
.L791:

; ..\flash\FL.c	  1180  
; ..\flash\FL.c	  1181      // for(index = APP_FL_INDEX; index < FL_NUM_LOGICAL_BLOCKS; index++)
; ..\flash\FL.c	  1182      {
; ..\flash\FL.c	  1183          // if (moduleId == FL_BlkInfo[index].moduleId)
; ..\flash\FL.c	  1184          {
; ..\flash\FL.c	  1185              if((locAdr == FL_BlkInfo[index].address) && (locLength <= FL_BlkInfo[index].length))
	fcall	.cocofun_3
.L787:
	ld.w	d15,[a15]24
.L861:
	jne	d15,d8,.L124
.L862:
	ld.w	d15,[a15]28
.L863:
	jlt.u	d15,d2,.L125
.L864:

; ..\flash\FL.c	  1186              {
; ..\flash\FL.c	  1187                  *idxRet = index;
	mov	d15,#1
	st.b	[a12],d15
.L865:

; ..\flash\FL.c	  1188                  result = FL_OK;
; ..\flash\FL.c	  1189              }
; ..\flash\FL.c	  1190              else
; ..\flash\FL.c	  1191              {
; ..\flash\FL.c	  1192                  result = FL_MODULEID_UNMATCH;
; ..\flash\FL.c	  1193              }
; ..\flash\FL.c	  1194              // break;
; ..\flash\FL.c	  1195          }
; ..\flash\FL.c	  1196      }
; ..\flash\FL.c	  1197      return result;
; ..\flash\FL.c	  1198  }
	mov	d2,#0
	ret
.L125:
.L124:
	mov	d2,#7
	ret
.L573:
	
__FL_CheckModuleId_function_end:
	.size	FL_CheckModuleId,__FL_CheckModuleId_function_end-FL_CheckModuleId
.L308:
	; End of function
	
	.sdecl	'.text.FL.FL_MainFunction',code,cluster('FL_MainFunction')
	.sect	'.text.FL.FL_MainFunction'
	.align	2
	
	.global	FL_MainFunction

; ..\flash\FL.c	  1199  /******************************************************************************/
; ..\flash\FL.c	  1200  /**
; ..\flash\FL.c	  1201   * @brief               <flash main function for active job>
; ..\flash\FL.c	  1202   * 
; ..\flash\FL.c	  1203   * <flash main function for active job,process checksum,erase and program> .
; ..\flash\FL.c	  1204   * Service ID   :       <NONE>
; ..\flash\FL.c	  1205   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	  1206   * Reentrancy           <Reentrant>
; ..\flash\FL.c	  1207   * @param[in]           <NONE>
; ..\flash\FL.c	  1208   * @param[out]          <NONE>
; ..\flash\FL.c	  1209   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1210   * @return              <NONE>    
; ..\flash\FL.c	  1211   */
; ..\flash\FL.c	  1212  /******************************************************************************/
; ..\flash\FL.c	  1213  void FL_MainFunction(void)
; Function FL_MainFunction
.L242:
FL_MainFunction:	.type	func

; ..\flash\FL.c	  1214  {
; ..\flash\FL.c	  1215      switch (FldownloadStatus.activeJob)
	fcall	.cocofun_1
.L1074:
	lea	a12,[a15]1637
	ld.bu	d15,[a12]
.L1075:

; ..\flash\FL.c	  1216      {
; ..\flash\FL.c	  1217          case FL_JOB_ERASING:
	jeq	d15,#1,.L128
.L1076:

; ..\flash\FL.c	  1218              /* do the flash erase*/
; ..\flash\FL.c	  1219              FldownloadStatus.errorCode = FL_Erasing();
; ..\flash\FL.c	  1220              FldownloadStatus.activeJob = FL_JOB_IDLE;
; ..\flash\FL.c	  1221  
; ..\flash\FL.c	  1222              break;
; ..\flash\FL.c	  1223  
; ..\flash\FL.c	  1224          case FL_JOB_PROGRAMMING:
	jeq	d15,#2,.L129
.L1077:

; ..\flash\FL.c	  1225              /* do the flash program*/
; ..\flash\FL.c	  1226              FldownloadStatus.errorCode = FL_Programming();
; ..\flash\FL.c	  1227              FldownloadStatus.activeJob = FL_JOB_IDLE;
; ..\flash\FL.c	  1228              break;
; ..\flash\FL.c	  1229  
; ..\flash\FL.c	  1230          case FL_JOB_CHECKING:
	jeq	d15,#3,.L130
.L1078:

; ..\flash\FL.c	  1231              /* do the flash checksum*/
; ..\flash\FL.c	  1232              FldownloadStatus.errorCode = FL_CheckSuming();
; ..\flash\FL.c	  1233              FldownloadStatus.activeJob = FL_JOB_IDLE;
; ..\flash\FL.c	  1234              break;
; ..\flash\FL.c	  1235          case FL_JOB_COMPATIBLE:
	jeq	d15,#5,.L131
.L1079:
	j	.L132
.L128:
	call	FL_Erasing
.L1080:
	j	.L133
.L129:
	call	FL_Programming
.L1081:
	j	.L134
.L130:
	call	FL_CheckSuming
.L1082:
	j	.L135
.L131:

; ..\flash\FL.c	  1236          {
; ..\flash\FL.c	  1237          	FldownloadStatus.errorCode =FL_CheckCompatibility();
	call	FL_CheckCompatibility

; ..\flash\FL.c	  1238          	FldownloadStatus.activeJob = FL_JOB_IDLE;
; ..\flash\FL.c	  1239          	break;
; ..\flash\FL.c	  1240          }
; ..\flash\FL.c	  1241          default:
; ..\flash\FL.c	  1242              break;
; ..\flash\FL.c	  1243      }
; ..\flash\FL.c	  1244  
; ..\flash\FL.c	  1245      if (FldownloadStatus.errorCode != FL_OK)
.L133:
.L134:
.L135:
	st.b	[a15]11,d2
.L1083:
	mov	d15,#0
	st.b	[a12],d15
.L132:
	ld.bu	d15,[a15]11
.L1084:
	jeq	d15,#0,.L136
.L1085:

; ..\flash\FL.c	  1246      {
; ..\flash\FL.c	  1247          /* initialize the flash download state */
; ..\flash\FL.c	  1248          FL_InitState();
	call	FL_InitState
.L1086:

; ..\flash\FL.c	  1249          FldownloadStatus.activeJob = FL_JOB_IDLE;
	mov	d15,#0
	st.b	[a12],d15
.L136:

; ..\flash\FL.c	  1250      }
; ..\flash\FL.c	  1251      
; ..\flash\FL.c	  1252      
; ..\flash\FL.c	  1253  
; ..\flash\FL.c	  1254      return;
; ..\flash\FL.c	  1255  }
	ret
.L617:
	
__FL_MainFunction_function_end:
	.size	FL_MainFunction,__FL_MainFunction_function_end-FL_MainFunction
.L363:
	; End of function
	
	.sdecl	'.text.FL.FL_UpdateNvm',code,cluster('FL_UpdateNvm')
	.sect	'.text.FL.FL_UpdateNvm'
	.align	2
	

; ..\flash\FL.c	  1256  
; ..\flash\FL.c	  1257  /******************************************************************************/
; ..\flash\FL.c	  1258  /**
; ..\flash\FL.c	  1259   * @brief               <program bootloader infomation to EEPROM>
; ..\flash\FL.c	  1260   * 
; ..\flash\FL.c	  1261   * <program bootloader infomation to EEPROM,e.g. block valid,checksum,
; ..\flash\FL.c	  1262   *  fingerprint..> .
; ..\flash\FL.c	  1263   * @param[in]           <NONE>
; ..\flash\FL.c	  1264   * @param[out]          <NONE>
; ..\flash\FL.c	  1265   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1266   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1267   */
; ..\flash\FL.c	  1268  /******************************************************************************/
; ..\flash\FL.c	  1269  STATIC FL_ResultType FL_UpdateNvm(void)
; Function FL_UpdateNvm
.L244:
FL_UpdateNvm:	.type	func

; ..\flash\FL.c	  1270  {
; ..\flash\FL.c	  1271      FL_ResultType ret = FL_FAILED;
	mov	d8,#1
	sub.a	a10,#16
.L792:

; ..\flash\FL.c	  1272      /* CRC32 parameter */
; ..\flash\FL.c	  1273      SecM_CRCParamType crcParam;
; ..\flash\FL.c	  1274      
; ..\flash\FL.c	  1275      /* Initialize CRC32 parameter */
; ..\flash\FL.c	  1276      crcParam.crcState = SECM_CRC_INIT;
	mov	d15,#0
	st.b	[a10]2,d15
.L793:

; ..\flash\FL.c	  1277      crcParam.crcSourceBuffer = (const uint8 *)&FL_NvmInfo;
	fcall	.cocofun_2
.L794:
	st.a	[a10]4,a15
.L1424:

; ..\flash\FL.c	  1278      crcParam.crcByteCount = sizeof(FL_NvmInfoType) - 6;
	mov	d15,#42
	st.h	[a10]8,d15
.L1425:

; ..\flash\FL.c	  1279      
; ..\flash\FL.c	  1280      /* compute CRC of the block infomation */
; ..\flash\FL.c	  1281      (void)SecM_ComputeCRC(&crcParam);
	mov.aa	a4,a10
	call	SecM_ComputeCRC
.L1426:

; ..\flash\FL.c	  1282      crcParam.crcState = SECM_CRC_COMPUTE;
	mov	d15,#1
	st.b	[a10]2,d15
.L1427:

; ..\flash\FL.c	  1283      (void)SecM_ComputeCRC(&crcParam);
	mov.aa	a4,a10
	call	SecM_ComputeCRC
.L1428:

; ..\flash\FL.c	  1284      crcParam.crcState = SECM_CRC_FINALIZE;
	mov	d15,#2
	st.b	[a10]2,d15
.L1429:

; ..\flash\FL.c	  1285      (void)SecM_ComputeCRC(&crcParam);
	mov.aa	a4,a10
	call	SecM_ComputeCRC
.L1430:

; ..\flash\FL.c	  1286      
; ..\flash\FL.c	  1287      /* program computed CRC value to flash */
; ..\flash\FL.c	  1288      FL_NvmInfo.infoChecksum = crcParam.currentCRC;
	ld.hu	d15,[a10]0
.L1431:
	st.w	[a15]42,d15
.L1432:

; ..\flash\FL.c	  1289  
; ..\flash\FL.c	  1290      /* set input parameter of flash driver interface */
; ..\flash\FL.c	  1291      flashParamInfo.data = (const uint8 *)&FL_NvmInfo;
	movh.a	a13,#@his(flashParamInfo)
	lea	a13,[a13]@los(flashParamInfo)
.L1433:
	st.a	[a13]16,a15
.L1434:

; ..\flash\FL.c	  1292      flashParamInfo.address = FL_NVM_INFO_ADDRESS;
	movh	d15,#40965
	st.w	[a13]8,d15
.L1435:

; ..\flash\FL.c	  1293      flashParamInfo.length = sizeof(FL_NvmInfoType);
	lea	a12,[a13]12
.L1436:

; ..\flash\FL.c	  1294  
; ..\flash\FL.c	  1295      /* align program size */
; ..\flash\FL.c	  1296      if ((flashParamInfo.length & (FLASH_ONE_SECTOR - 1)) > 0)
; ..\flash\FL.c	  1297      {
; ..\flash\FL.c	  1298          flashParamInfo.length &= ~(FLASH_ONE_SECTOR - 1);
; ..\flash\FL.c	  1299          flashParamInfo.length += FLASH_ONE_SECTOR;
; ..\flash\FL.c	  1300      }
; ..\flash\FL.c	  1301      
; ..\flash\FL.c	  1302      /* erase flash block witch store the blocks infomation */
; ..\flash\FL.c	  1303      BLFlash_InfoPtr->flashEraseFct(&flashParamInfo);
	mov	d15,#16384
	st.w	[a12],d15
.L1437:
	movh.a	a14,#@his(BLFlash_InfoPtr)
	lea	a14,[a14]@los(BLFlash_InfoPtr)
	ld.a	a15,[a14]
.L1438:
	ld.a	a15,[a15]12
.L1439:
	mov.aa	a4,a13
	calli	a15
.L1440:

; ..\flash\FL.c	  1304  
; ..\flash\FL.c	  1305  
; ..\flash\FL.c	  1306  
; ..\flash\FL.c	  1307      if (kFlashOk == flashParamInfo.errorCode)
	mov.aa	a15,a13
	ld.bu	d15,[+a15]4
.L1441:
	jne	d15,#0,.L137
.L1442:

; ..\flash\FL.c	  1308      {
; ..\flash\FL.c	  1309          /* program blocks infomation */
; ..\flash\FL.c	  1310          flashParamInfo.length = sizeof(FL_NvmInfoType);
; ..\flash\FL.c	  1311          if ((flashParamInfo.length & (FLASH_ONE_PHRASE - 1)) > 0)
; ..\flash\FL.c	  1312          {
; ..\flash\FL.c	  1313              flashParamInfo.length &= ~(FLASH_ONE_PHRASE - 1);
; ..\flash\FL.c	  1314              flashParamInfo.length += FLASH_ONE_PHRASE;
; ..\flash\FL.c	  1315          }
; ..\flash\FL.c	  1316          BLFlash_InfoPtr->flashWriteFct(&flashParamInfo);
	mov	d15,#64
	st.w	[a12],d15
.L1443:
	mov.aa	a4,a13
.L1444:
	ld.a	a2,[a14]
.L1445:
	ld.a	a2,[a2]16
.L1446:
	calli	a2
.L137:

; ..\flash\FL.c	  1317      }
; ..\flash\FL.c	  1318      
; ..\flash\FL.c	  1319      if (kFlashOk == flashParamInfo.errorCode)
	ld.bu	d15,[a15]
.L1447:

; ..\flash\FL.c	  1320      {
; ..\flash\FL.c	  1321          ret = FL_OK;
; ..\flash\FL.c	  1322      }
; ..\flash\FL.c	  1323      
; ..\flash\FL.c	  1324      return ret;
; ..\flash\FL.c	  1325  }
	sel	d2,d15,d8,#0
	ret
.L678:
	
__FL_UpdateNvm_function_end:
	.size	FL_UpdateNvm,__FL_UpdateNvm_function_end-FL_UpdateNvm
.L473:
	; End of function
	
	.sdecl	'.text.FL..cocofun_2',code,cluster('.cocofun_2')
	.sect	'.text.FL..cocofun_2'
	.align	2
; Function .cocofun_2
.L246:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	movh.a	a15,#@his(FL_NvmInfo)
	lea	a15,[a15]@los(FL_NvmInfo)
.L1492:
	fret
.L508:
	; End of function
	.sdecl	'.text.FL.FL_Erasing',code,cluster('FL_Erasing')
	.sect	'.text.FL.FL_Erasing'
	.align	2
	

; ..\flash\FL.c	  1326  
; ..\flash\FL.c	  1327  /******************************************************************************/
; ..\flash\FL.c	  1328  /**
; ..\flash\FL.c	  1329   * @brief               <active job erase>
; ..\flash\FL.c	  1330   * 
; ..\flash\FL.c	  1331   * <erase the current logical block witch requested by 0x31 service> .
; ..\flash\FL.c	  1332   * @param[in]           <NONE>
; ..\flash\FL.c	  1333   * @param[out]          <NONE>
; ..\flash\FL.c	  1334   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1335   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1336   */
; ..\flash\FL.c	  1337  /******************************************************************************/
; ..\flash\FL.c	  1338  
; ..\flash\FL.c	  1339  STATIC FL_ResultType FL_Erasing(void)
; Function FL_Erasing
.L248:
FL_Erasing:	.type	func

; ..\flash\FL.c	  1340  {
; ..\flash\FL.c	  1341      FL_ResultType ret = FL_OK;
	mov	d8,#0
.L795:

; ..\flash\FL.c	  1342      
; ..\flash\FL.c	  1343      /* update the bootloader infomation to EEPROM */
; ..\flash\FL.c	  1344      Appl_UpdateTriggerConditionImmediate(1);
	mov	d4,#1
	call	Appl_UpdateTriggerConditionImmediate
.L1218:

; ..\flash\FL.c	  1345  //    ret = FL_UpdateNvm();
; ..\flash\FL.c	  1346      
; ..\flash\FL.c	  1347      if (FL_OK == ret)
; ..\flash\FL.c	  1348      {
; ..\flash\FL.c	  1349          { /* set flash driver input parameter */
; ..\flash\FL.c	  1350              flashParamInfo.address = 0xA0050000uL;//CurrentErasingAddress;
	movh.a	a15,#@his(flashParamInfo)
.L1219:
	movh	d15,#40965
	lea	a15,[a15]@los(flashParamInfo)
.L1220:
	st.w	[a15]8,d15
.L1221:

; ..\flash\FL.c	  1351              flashParamInfo.length = 0x1b0000uL;//CurrentErasingLength;
	movh	d15,#27
	st.w	[a15]12,d15
.L1222:

; ..\flash\FL.c	  1352          	BLFlash_InfoPtr->flashEraseFct(&flashParamInfo);
	movh.a	a2,#@his(BLFlash_InfoPtr)
	ld.a	a2,[a2]@los(BLFlash_InfoPtr)
.L1223:
	mov.aa	a4,a15
.L1224:
	ld.a	a2,[a2]12
.L1225:
	calli	a2
.L1226:

; ..\flash\FL.c	  1353          }
; ..\flash\FL.c	  1354  
; ..\flash\FL.c	  1355          /* check if erase success */
; ..\flash\FL.c	  1356          if (kFlashOk == flashParamInfo.errorCode)
	ld.bu	d15,[a15]4
.L1227:
	jne	d15,#0,.L140
.L1228:

; ..\flash\FL.c	  1357          {
; ..\flash\FL.c	  1358              /*set the block erased */
; ..\flash\FL.c	  1359              FldownloadStatus.blockErased = TRUE;
	movh.a	a15,#@his(FldownloadStatus+12)
.L1229:
	mov	d15,#1
	st.b	[a15]@los(FldownloadStatus+12),d15
.L1230:
	j	.L141
.L140:

; ..\flash\FL.c	  1360          }
; ..\flash\FL.c	  1361          else
; ..\flash\FL.c	  1362          {
; ..\flash\FL.c	  1363              ret = FL_FAILED;
	mov	d8,#1
.L141:

; ..\flash\FL.c	  1364          }
; ..\flash\FL.c	  1365      }
; ..\flash\FL.c	  1366      
; ..\flash\FL.c	  1367      return ret;
; ..\flash\FL.c	  1368  }
	mov	d2,d8
	ret
.L652:
	
__FL_Erasing_function_end:
	.size	FL_Erasing,__FL_Erasing_function_end-FL_Erasing
.L433:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckDownloadSegment',code,cluster('FL_CheckDownloadSegment')
	.sect	'.text.FL.FL_CheckDownloadSegment'
	.align	2
	

; ..\flash\FL.c	  1369  
; ..\flash\FL.c	  1370  
; ..\flash\FL.c	  1371  /******************************************************************************/
; ..\flash\FL.c	  1372  /**
; ..\flash\FL.c	  1373   * @brief               <check segment address>
; ..\flash\FL.c	  1374   * 
; ..\flash\FL.c	  1375   * <check if the transfered address is in current block,and if the address is 
; ..\flash\FL.c	  1376   *  increased by segment> .
; ..\flash\FL.c	  1377   * @param[in]           <NONE>
; ..\flash\FL.c	  1378   * @param[out]          <NONE>
; ..\flash\FL.c	  1379   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1380   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1381   */
; ..\flash\FL.c	  1382  /******************************************************************************/
; ..\flash\FL.c	  1383  STATIC FL_ResultType FL_CheckDownloadSegment(void)
; Function FL_CheckDownloadSegment
.L250:
FL_CheckDownloadSegment:	.type	func

; ..\flash\FL.c	  1384  {
; ..\flash\FL.c	  1385      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  1386      FL_SegmentInfoType * curSegment;
; ..\flash\FL.c	  1387      uint16 segmentIndex;//uint8锟斤拷为uint16
; ..\flash\FL.c	  1388      
; ..\flash\FL.c	  1389      /* 
; ..\flash\FL.c	  1390       ** check if block is erased,if current num of segment is less than maxmun,
; ..\flash\FL.c	  1391       ** if address if in current block.
; ..\flash\FL.c	  1392       */
; ..\flash\FL.c	  1393      if (FALSE == FldownloadStatus.blockErased)
	mov	d2,#0
	fcall	.cocofun_1
.L1235:
	ld.bu	d15,[a15]12
.L1236:
	jne	d15,#0,.L143
.L1237:

; ..\flash\FL.c	  1394      {
; ..\flash\FL.c	  1395          ret = FL_ERR_SEQUENCE;
; ..\flash\FL.c	  1396      }
; ..\flash\FL.c	  1397      else
; ..\flash\FL.c	  1398      {
; ..\flash\FL.c	  1399      	uint16 useSegment=0;
; ..\flash\FL.c	  1400      	useSegment=FldownloadStatus.segmentList[CurrentProgrammingBlock].nrOfSegments;
; ..\flash\FL.c	  1401  
; ..\flash\FL.c	  1402  
; ..\flash\FL.c	  1403          if ((useSegment < FL_MAX_SEGMENTS ) &&
; ..\flash\FL.c	  1404              (FldownloadStatus.startAddr == FL_BlkInfo[FldownloadStatus.blockIndex].address) &&
; ..\flash\FL.c	  1405              ((FldownloadStatus.startAddr + FldownloadStatus.length) <=
; ..\flash\FL.c	  1406                  (FL_BlkInfo[FldownloadStatus.blockIndex].address + FL_BlkInfo[FldownloadStatus.blockIndex].length)))
; ..\flash\FL.c	  1407          {
; ..\flash\FL.c	  1408              /* get current segment num */
; ..\flash\FL.c	  1409              segmentIndex = useSegment;
; ..\flash\FL.c	  1410              
; ..\flash\FL.c	  1411              /* check if segment address is increase */
; ..\flash\FL.c	  1412              if (segmentIndex > 0x00u)
; ..\flash\FL.c	  1413              {                
; ..\flash\FL.c	  1414              	curSegment=&FldownloadStatus.segmentList[CurrentProgrammingBlock].segmentInfo[segmentIndex - 1];
; ..\flash\FL.c	  1415  
; ..\flash\FL.c	  1416                  /* check if download address is in front segment range */
; ..\flash\FL.c	  1417                  if (FldownloadStatus.startAddr  <
; ..\flash\FL.c	  1418                      (curSegment->address + curSegment->length))
; ..\flash\FL.c	  1419                  {
; ..\flash\FL.c	  1420                      ret = FL_ERR_ADDR_LENGTH;
; ..\flash\FL.c	  1421                  }
; ..\flash\FL.c	  1422  
; ..\flash\FL.c	  1423              }
; ..\flash\FL.c	  1424              
; ..\flash\FL.c	  1425              if (FL_OK == ret)
; ..\flash\FL.c	  1426              {
; ..\flash\FL.c	  1427                  /* set the flash download info */
; ..\flash\FL.c	  1428              	curSegment = &FldownloadStatus.segmentList[CurrentProgrammingBlock].segmentInfo[segmentIndex];
; ..\flash\FL.c	  1429              	FldownloadStatus.segmentList[CurrentProgrammingBlock].nrOfSegments++;
; ..\flash\FL.c	  1430  
; ..\flash\FL.c	  1431  
; ..\flash\FL.c	  1432                  curSegment->address = FldownloadStatus.startAddr;
; ..\flash\FL.c	  1433                  curSegment->length = FldownloadStatus.length;
; ..\flash\FL.c	  1434              }
; ..\flash\FL.c	  1435          }
; ..\flash\FL.c	  1436          else
; ..\flash\FL.c	  1437          {
; ..\flash\FL.c	  1438              ret = FL_ERR_ADDR_LENGTH;
; ..\flash\FL.c	  1439          }
; ..\flash\FL.c	  1440      }
; ..\flash\FL.c	  1441      
; ..\flash\FL.c	  1442      return ret;
; ..\flash\FL.c	  1443  }
	mov	d2,#2
	ret
.L143:
	movh.a	a2,#@his(CurrentProgrammingBlock)
	ld.bu	d15,[a2]@los(CurrentProgrammingBlock)
.L1238:
	mov	d0,#804
	mul	d15,d0
	addsc.a	a2,a15,d15,#0
.L1239:
	ld.hu	d0,[+a2]28
.L1240:
	mov	d15,#100
.L1241:
	jge.u	d0,d15,.L145
.L1242:
	ld.bu	d15,[a15]13
.L1243:
	movh.a	a4,#@his(FL_BlkInfo)
.L1244:
	mul	d15,d15,#24
	lea	a4,[a4]@los(FL_BlkInfo)
.L1245:
	lea	a5,[a15]14
.L1246:
	addsc.a	a4,a4,d15,#0
.L1247:
	ld.w	d15,[a5]
.L1248:
	ld.w	d1,[a4]
.L1249:
	jne	d15,d1,.L146
.L1250:
	lea	a6,[a15]18
	ld.w	d3,[a6]
.L1251:
	ld.w	d15,[a4]4
.L1252:
	add	d3,d1
.L1253:
	add	d15,d1
.L1254:
	jlt.u	d15,d3,.L147
.L1255:
	jeq	d0,#0,.L148
.L1256:
	addsc.a	a15,a2,d0,#3
.L1257:
	ld.w	d15,[a15]-4
.L1258:
	ld.w	d3,[a15]
.L1259:
	add	d15,d3
.L1260:
	ge.u	d15,d1,d15
.L1261:
	cmovn	d2,d15,#5
.L148:
	jne	d2,#0,.L150
.L1262:
	lea	a15,[a2]4
.L1263:
	addsc.a	a15,a15,d0,#3
.L796:
	add	d0,#1
	st.h	[a2],d0
.L1264:
	ld.w	d15,[a5]
.L1265:
	st.w	[a15],d15
.L1266:
	ld.w	d15,[a6]
.L1267:
	st.w	[a15]4,d15
.L150:
	ret
.L147:
.L146:
.L145:
	mov	d2,#5
	ret
.L654:
	
__FL_CheckDownloadSegment_function_end:
	.size	FL_CheckDownloadSegment,__FL_CheckDownloadSegment_function_end-FL_CheckDownloadSegment
.L438:
	; End of function
	
	.sdecl	'.text.FL.FL_DownloadRemainData',code,cluster('FL_DownloadRemainData')
	.sect	'.text.FL.FL_DownloadRemainData'
	.align	2
	

; ..\flash\FL.c	  1444  
; ..\flash\FL.c	  1445  /******************************************************************************/
; ..\flash\FL.c	  1446  /**
; ..\flash\FL.c	  1447   * @brief               <FL_DownloadRemainData>
; ..\flash\FL.c	  1448   * 
; ..\flash\FL.c	  1449   * 
; ..\flash\FL.c	  1450   * 
; ..\flash\FL.c	  1451   * @param[in]           <NONE>
; ..\flash\FL.c	  1452   * @param[out]          <NONE>
; ..\flash\FL.c	  1453   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1454   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1455   */
; ..\flash\FL.c	  1456  /******************************************************************************/
; ..\flash\FL.c	  1457  FL_ResultType FL_DownloadRemainData(void)
; Function FL_DownloadRemainData
.L252:
FL_DownloadRemainData:	.type	func

; ..\flash\FL.c	  1458  {
; ..\flash\FL.c	  1459      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  1460  
; ..\flash\FL.c	  1461      /*two segment is not in one page or checking sum is started,so download the last segment first*/
; ..\flash\FL.c	  1462      flashParamInfo.address = FL_RemainDataStruct.remainAddr;
	movh.a	a12,#@his(FL_RemainDataStruct)
	lea	a12,[a12]@los(FL_RemainDataStruct)
.L1272:
	movh.a	a15,#@his(flashParamInfo)
	lea	a15,[a15]@los(flashParamInfo)
.L1273:
	ld.w	d15,[a12+]
.L1274:
	st.w	[a15]8,d15
.L1275:

; ..\flash\FL.c	  1463      /* fill pad for the left data */
; ..\flash\FL.c	  1464      Appl_Memset(&FlProgramData[FL_RemainDataStruct.remainLength],
; ..\flash\FL.c	  1465          (const uint8)FL_GAP_FILL_VALUE,
; ..\flash\FL.c	  1466              FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength);
	mov	d15,#256
	movh.a	a13,#@his(FlProgramData)
.L1276:
	ld.w	d0,[a12]
.L1277:
	mov	d8,#0
	lea	a13,[a13]@los(FlProgramData)
.L797:
	addsc.a	a4,a13,d0,#0
.L1278:
	mov	d4,#255
.L1279:
	sub	d5,d15,d0
	call	Appl_Memset
.L1280:

; ..\flash\FL.c	  1467  
; ..\flash\FL.c	  1468      /* set the flash download info */
; ..\flash\FL.c	  1469      flashParamInfo.data = &FlProgramData[0];
	st.a	[a15]16,a13
.L1281:

; ..\flash\FL.c	  1470      flashParamInfo.length = FL_PROGRAM_SIZE;
	st.w	[a15]12,d15
.L1282:

; ..\flash\FL.c	  1471      FL_RemainDataStruct.remainLength = 0x00ul;
	mov	d15,#0
	st.w	[a12],d15
.L1283:

; ..\flash\FL.c	  1472      /* write the last 0x36 aligned data */
; ..\flash\FL.c	  1473      {
; ..\flash\FL.c	  1474      	 BLFlash_InfoPtr->flashWriteFct(&flashParamInfo);
	movh.a	a2,#@his(BLFlash_InfoPtr)
	ld.a	a2,[a2]@los(BLFlash_InfoPtr)
.L1284:
	mov.aa	a4,a15
.L1285:
	ld.a	a2,[a2]16
.L1286:
	calli	a2
.L1287:

; ..\flash\FL.c	  1475      }
; ..\flash\FL.c	  1476  
; ..\flash\FL.c	  1477      
; ..\flash\FL.c	  1478      if (flashParamInfo.errorCode != kFlashOk)
	ld.bu	d15,[a15]4
.L1288:

; ..\flash\FL.c	  1479      {
; ..\flash\FL.c	  1480          ret = FL_FAILED;
; ..\flash\FL.c	  1481      }
; ..\flash\FL.c	  1482         
; ..\flash\FL.c	  1483      return ret;
; ..\flash\FL.c	  1484  }
	seln	d2,d15,d8,#1
	ret
.L659:
	
__FL_DownloadRemainData_function_end:
	.size	FL_DownloadRemainData,__FL_DownloadRemainData_function_end-FL_DownloadRemainData
.L443:
	; End of function
	
	.sdecl	'.text.FL.FL_HandleRemainData',code,cluster('FL_HandleRemainData')
	.sect	'.text.FL.FL_HandleRemainData'
	.align	2
	

; ..\flash\FL.c	  1485  
; ..\flash\FL.c	  1486  /******************************************************************************/
; ..\flash\FL.c	  1487  /**
; ..\flash\FL.c	  1488   * @brief               <FL_HandleRemainData>
; ..\flash\FL.c	  1489   * 
; ..\flash\FL.c	  1490   * 
; ..\flash\FL.c	  1491   *  
; ..\flash\FL.c	  1492   * @param[out]          <NONE>
; ..\flash\FL.c	  1493   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1494   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1495   */
; ..\flash\FL.c	  1496  /******************************************************************************/
; ..\flash\FL.c	  1497  FL_ResultType FL_HandleRemainData(void)
; Function FL_HandleRemainData
.L254:
FL_HandleRemainData:	.type	func

; ..\flash\FL.c	  1498  {
; ..\flash\FL.c	  1499      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  1500      
; ..\flash\FL.c	  1501      if (FL_RemainDataStruct.remainLength != 0x00ul)
	mov	d8,#0
	movh.a	a12,#@his(FL_RemainDataStruct)
.L798:
	lea	a12,[a12]@los(FL_RemainDataStruct)
.L1293:
	mov.aa	a13,a12
	ld.w	d15,[+a13]4
.L1294:
	jeq	d15,#0,.L155
.L1295:

; ..\flash\FL.c	  1502      {
; ..\flash\FL.c	  1503          if ((FL_RemainDataStruct.remainAddr & ~(FL_PROGRAM_SIZE - 1ul)) \ 
; ..\flash\FL.c	  1504              == (FldownloadStatus.startAddr & ~(FL_PROGRAM_SIZE - 1ul)))
	movh.a	a15,#@his(FldownloadStatus+14)
.L1296:
	lea	a15,[a15]@los(FldownloadStatus+14)
	ld.w	d0,[a15]
.L1297:
	ld.w	d1,[a12]
.L1298:
	insert	d3,d0,#0,#0,#8
.L1299:
	insert	d2,d1,#0,#0,#8
.L1300:
	jne	d2,d3,.L156
.L1301:

; ..\flash\FL.c	  1505          {        
; ..\flash\FL.c	  1506              /*link the remain data and new segment because one page, and download together via 0x36*/
; ..\flash\FL.c	  1507              Appl_Memset(&FlProgramData[FL_RemainDataStruct.remainLength],
; ..\flash\FL.c	  1508                  (const uint8)FL_GAP_FILL_VALUE,
; ..\flash\FL.c	  1509                  FldownloadStatus.startAddr - FL_RemainDataStruct.remainLength - FL_RemainDataStruct.remainAddr);
	sub	d0,d15
	movh.a	a2,#@his(FlProgramData)
.L1302:
	mov	d4,#255
	lea	a2,[a2]@los(FlProgramData)
.L1303:
	addsc.a	a4,a2,d15,#0
.L1304:
	sub	d5,d0,d1
	call	Appl_Memset
.L1305:

; ..\flash\FL.c	  1510              FL_RemainDataStruct.remainLength = FldownloadStatus.startAddr - FL_RemainDataStruct.remainAddr;;
	ld.w	d15,[a15]
.L1306:
	ld.w	d0,[a12]
.L1307:
	sub	d15,d0
	st.w	[a13],d15
.L1308:
	j	.L157
.L156:

; ..\flash\FL.c	  1511          }
; ..\flash\FL.c	  1512          else 
; ..\flash\FL.c	  1513          {
; ..\flash\FL.c	  1514              ret = FL_DownloadRemainData();
	call	FL_DownloadRemainData
.L799:
	mov	d8,d2
.L155:
	movh.a	a15,#@his(FldownloadStatus+14)
.L1309:

; ..\flash\FL.c	  1515              FL_RemainDataStruct.remainLength = FldownloadStatus.startAddr & (FL_PROGRAM_SIZE-1);
; ..\flash\FL.c	  1516              /* initialize the program buffer */
; ..\flash\FL.c	  1517              Appl_Memset(&FlProgramData[0],
; ..\flash\FL.c	  1518                 (uint8)FL_GAP_FILL_VALUE,
; ..\flash\FL.c	  1519                 (uint32)FL_PROGRAM_SIZE);
; ..\flash\FL.c	  1520          }
; ..\flash\FL.c	  1521      }
; ..\flash\FL.c	  1522      else 
; ..\flash\FL.c	  1523      {
; ..\flash\FL.c	  1524         FL_RemainDataStruct.remainLength = FldownloadStatus.startAddr & (FL_PROGRAM_SIZE-1);
	ld.w	d15,[a15]@los(FldownloadStatus+14)
.L1310:

; ..\flash\FL.c	  1525         /* initialize the program buffer */
; ..\flash\FL.c	  1526         Appl_Memset(&FlProgramData[0],
; ..\flash\FL.c	  1527             (uint8)FL_GAP_FILL_VALUE,
	mov	d4,#255
	movh.a	a4,#@his(FlProgramData)
.L1311:
	and	d15,#255
	st.w	[a13],d15
.L1312:

; ..\flash\FL.c	  1528             (uint32)FL_PROGRAM_SIZE);
	mov	d5,#256
	lea	a4,[a4]@los(FlProgramData)
.L1313:
	call	Appl_Memset
.L157:

; ..\flash\FL.c	  1529      }
; ..\flash\FL.c	  1530      return ret;
; ..\flash\FL.c	  1531  }
	mov	d2,d8
	ret
.L661:
	
__FL_HandleRemainData_function_end:
	.size	FL_HandleRemainData,__FL_HandleRemainData_function_end-FL_HandleRemainData
.L448:
	; End of function
	
	.sdecl	'.text.FL.FL_ProgrammingData',code,cluster('FL_ProgrammingData')
	.sect	'.text.FL.FL_ProgrammingData'
	.align	2
	

; ..\flash\FL.c	  1532  
; ..\flash\FL.c	  1533  /******************************************************************************/
; ..\flash\FL.c	  1534  /**
; ..\flash\FL.c	  1535   * @brief               <program data>
; ..\flash\FL.c	  1536   * 
; ..\flash\FL.c	  1537   * <program the aligned data transfered by 0x36 service request > .
; ..\flash\FL.c	  1538   * @param[in]           <NONE>
; ..\flash\FL.c	  1539   * @param[out]          <NONE>
; ..\flash\FL.c	  1540   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1541   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1542   */
; ..\flash\FL.c	  1543  /******************************************************************************/
; ..\flash\FL.c	  1544  STATIC FL_ResultType FL_ProgrammingData(void)
; Function FL_ProgrammingData
.L256:
FL_ProgrammingData:	.type	func

; ..\flash\FL.c	  1545  {
; ..\flash\FL.c	  1546      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  1547  
; ..\flash\FL.c	  1548      /* check the program length and program status */
; ..\flash\FL.c	  1549      while ((FlProgramLength > 0) && (FL_OK == ret))
; ..\flash\FL.c	  1550      {
; ..\flash\FL.c	  1551          /* check if the program size is more than maxmun size of program buffer */
; ..\flash\FL.c	  1552          if ((FlProgramLength + FL_RemainDataStruct.remainLength) >= FL_PROGRAM_SIZE)
	mov	d8,#0
	movh.a	a15,#@his(FlProgramLength)
.L801:
	mov	d9,d8
	lea	a15,[a15]@los(FlProgramLength)
.L1318:
	sub.a	a10,#16
.L800:
	st.a	[a10]8,a15
.L1319:
	j	.L159
.L160:

; ..\flash\FL.c	  1553          {
; ..\flash\FL.c	  1554              /* get the download datas */
; ..\flash\FL.c	  1555              Appl_Memcpy(&FlProgramData[FL_RemainDataStruct.remainLength],
; ..\flash\FL.c	  1556                  FldownloadStatus.dataBuff,
	movh.a	a2,#@his(FldownloadStatus)
	lea	a2,[a2]@los(FldownloadStatus)
.L1320:
	lea	a13,[a2]24
	ld.a	a5,[a13]
.L1321:
	movh.a	a14,#@his(FL_RemainDataStruct)
	lea	a14,[a14]@los(FL_RemainDataStruct)
.L1322:
	mov.aa	a12,a14
	ld.w	d15,[+a12]4
.L1323:
	movh.a	a15,#@his(FlProgramData)
	lea	a15,[a15]@los(FlProgramData)
	st.a	[a10],a15
.L1324:
	addsc.a	a4,a15,d15,#0
.L1325:

; ..\flash\FL.c	  1557                      FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength);
; ..\flash\FL.c	  1558  
; ..\flash\FL.c	  1559              /* index the databuff point in tranfered buffer */
; ..\flash\FL.c	  1560              FldownloadStatus.dataBuff += FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength;
; ..\flash\FL.c	  1561              
; ..\flash\FL.c	  1562              /* index the totle program length */
; ..\flash\FL.c	  1563              FlProgramLength -= FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength;
; ..\flash\FL.c	  1564              
; ..\flash\FL.c	  1565              /* set the flash driver input parameter */
; ..\flash\FL.c	  1566              flashParamInfo.address = FldownloadStatus.startAddr
; ..\flash\FL.c	  1567                  - FL_RemainDataStruct.remainLength;
; ..\flash\FL.c	  1568              flashParamInfo.length = (uint32)FL_PROGRAM_SIZE;
; ..\flash\FL.c	  1569              flashParamInfo.data = (uint8 *)&FlProgramData[0];
; ..\flash\FL.c	  1570              
; ..\flash\FL.c	  1571              /* program the data */
; ..\flash\FL.c	  1572              if (TRUE == headRemainDataDownloaded)
; ..\flash\FL.c	  1573  			{
; ..\flash\FL.c	  1574  				 BLFlash_InfoPtr->flashWriteFct(&flashParamInfo);
; ..\flash\FL.c	  1575  			}
; ..\flash\FL.c	  1576  
; ..\flash\FL.c	  1577              /* index the start address and length that record in 0x34 service */
; ..\flash\FL.c	  1578              FldownloadStatus.startAddr += FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength;
; ..\flash\FL.c	  1579              FldownloadStatus.length -= FL_PROGRAM_SIZE - FL_RemainDataStruct.remainLength;
	lea	a15,[a2]18
	st.a	[a10]12,a15
.L1326:
	lea	a15,[a2]14
	st.a	[a10]4,a15
.L1327:
	mov	d10,#256
.L1328:
	ld.a	a15,[a10]8
	ld.w	d4,[a15]
.L1329:
	add	d0,d4,d15
.L1330:
	jlt.u	d0,d10,.L161
.L1331:
	sub	d4,d10,d15
	call	Appl_Memcpy
.L1332:
	ld.w	d1,[a13]
.L1333:
	ld.w	d0,[a12]
.L1334:
	addi	d1,d1,#256
	movh.a	a2,#@his(headRemainDataDownloaded)
.L1335:
	sub	d1,d0
	st.w	[a13],d1
.L1336:
	sub	d1,d10,d0
	movh.a	a13,#@his(flashParamInfo)
.L1337:
	ld.w	d15,[a15]
.L1338:
	lea	a13,[a13]@los(flashParamInfo)
.L1339:
	sub	d15,d1
	st.w	[a15],d15
.L1340:
	ld.a	a15,[a10]4
	ld.w	d15,[a15]
.L1341:
	sub	d15,d0
	st.w	[a13]8,d15
.L1342:
	st.w	[a13]12,d10
.L1343:
	ld.a	a15,[a10]
	st.a	[a13]16,a15
.L1344:
	ld.bu	d15,[a2]@los(headRemainDataDownloaded)
.L1345:
	jne	d15,#1,.L162
.L1346:
	movh.a	a15,#@his(BLFlash_InfoPtr)
	ld.a	a15,[a15]@los(BLFlash_InfoPtr)
.L1347:
	ld.a	a15,[a15]16
.L1348:
	mov.aa	a4,a13
	calli	a15
.L162:
	ld.a	a15,[a10]4
.L1349:
	ld.w	d15,[a12]
.L1350:
	ld.w	d0,[a15]
.L1351:
	sub	d10,d15
.L1352:
	addi	d0,d0,#256
.L1353:
	sub	d0,d15
	st.w	[a15],d0
.L1354:
	ld.a	a15,[a10]12
	ld.w	d0,[a15]
.L1355:
	sub	d0,d10
	st.w	[a15],d0
.L1356:

; ..\flash\FL.c	  1580              
; ..\flash\FL.c	  1581              FL_RemainDataStruct.remainLength = 0x00ul;
	st.w	[a12],d9
.L1357:

; ..\flash\FL.c	  1582              /* check if program success */
; ..\flash\FL.c	  1583              if (flashParamInfo.errorCode != kFlashOk)
	ld.bu	d15,[a13]4
.L1358:
	jeq	d15,#0,.L163
.L1359:

; ..\flash\FL.c	  1584              {
; ..\flash\FL.c	  1585                  ret = FL_FAILED;
	mov	d8,#1
	j	.L164
.L161:

; ..\flash\FL.c	  1586              }
; ..\flash\FL.c	  1587          }
; ..\flash\FL.c	  1588          else
; ..\flash\FL.c	  1589          {
; ..\flash\FL.c	  1590              /* set the last datas for write of current service 0x36 */
; ..\flash\FL.c	  1591              Appl_Memcpy(&FlProgramData[FL_RemainDataStruct.remainLength],
; ..\flash\FL.c	  1592                  FldownloadStatus.dataBuff,
; ..\flash\FL.c	  1593                  (uint32)FlProgramLength);
	call	Appl_Memcpy
.L1360:

; ..\flash\FL.c	  1594              FL_RemainDataStruct.remainAddr = FldownloadStatus.startAddr - FL_RemainDataStruct.remainLength;
	ld.a	a15,[a10]4
.L1361:
	ld.w	d0,[a12]
.L1362:
	ld.w	d15,[a15]
.L1363:
	sub	d1,d15,d0
	st.w	[a14],d1
.L1364:

; ..\flash\FL.c	  1595              FL_RemainDataStruct.remainLength += FlProgramLength;
	ld.a	a15,[a10]8
	ld.w	d1,[a15]
.L1365:
	add	d0,d1
	st.w	[a12],d0
.L1366:

; ..\flash\FL.c	  1596              /* index the start address and length that record in 0x34 service */
; ..\flash\FL.c	  1597              FldownloadStatus.startAddr += FlProgramLength;
	add	d15,d1
	ld.a	a15,[a10]4
	st.w	[a15],d15
.L1367:

; ..\flash\FL.c	  1598              FldownloadStatus.length -= FlProgramLength;
	ld.a	a15,[a10]12
	ld.w	d15,[a15]
.L1368:
	sub	d15,d1
	st.w	[a15],d15
.L1369:

; ..\flash\FL.c	  1599              /* end of current service 0x36 */
; ..\flash\FL.c	  1600              FlProgramLength = 0;
	ld.a	a15,[a10]8
	st.w	[a15],d9
.L164:
.L163:
.L159:
	ld.a	a15,[a10]8
	ld.w	d15,[a15]
.L1370:
	jeq	d15,#0,.L165
.L1371:
	jeq	d8,#0,.L160
.L165:

; ..\flash\FL.c	  1601          }
; ..\flash\FL.c	  1602      }
; ..\flash\FL.c	  1603      
; ..\flash\FL.c	  1604      return ret;
; ..\flash\FL.c	  1605  }
	mov	d2,d8
	ret
.L663:
	
__FL_ProgrammingData_function_end:
	.size	FL_ProgrammingData,__FL_ProgrammingData_function_end-FL_ProgrammingData
.L453:
	; End of function
	
	.sdecl	'.text.FL.FL_Programming',code,cluster('FL_Programming')
	.sect	'.text.FL.FL_Programming'
	.align	2
	

; ..\flash\FL.c	  1606  
; ..\flash\FL.c	  1607  /******************************************************************************/
; ..\flash\FL.c	  1608  /**
; ..\flash\FL.c	  1609   * @brief               <active job program>
; ..\flash\FL.c	  1610   * 
; ..\flash\FL.c	  1611   * <program the data transfered by 0x36 service request > .
; ..\flash\FL.c	  1612   * @param[in]           <NONE>
; ..\flash\FL.c	  1613   * @param[out]          <NONE>
; ..\flash\FL.c	  1614   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1615   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1616   */
; ..\flash\FL.c	  1617  /******************************************************************************/
; ..\flash\FL.c	  1618  STATIC FL_ResultType FL_Programming(void)
; Function FL_Programming
.L258:
FL_Programming:	.type	func

; ..\flash\FL.c	  1619  {
; ..\flash\FL.c	  1620      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  1621      
; ..\flash\FL.c	  1622      /* program buffer aligned data */
; ..\flash\FL.c	  1623      ret = FL_ProgrammingData();
	call	FL_ProgrammingData
.L802:

; ..\flash\FL.c	  1624  
; ..\flash\FL.c	  1625      /* check if the last not aligned data should be programmed */
; ..\flash\FL.c	  1626      if ((FldownloadStatus.length == 0x00uL) &&
	fcall	.cocofun_1
.L1376:
	ld.w	d15,[a15]18
.L1377:
	jne	d15,#0,.L167
.L1378:

; ..\flash\FL.c	  1627          (FL_OK == ret))
	jne	d2,#0,.L168
.L1379:

; ..\flash\FL.c	  1628      {
; ..\flash\FL.c	  1629          /* program the not aligned data */
; ..\flash\FL.c	  1630          //ret = FL_ProgrammingAlignData();
; ..\flash\FL.c	  1631          FldownloadStatus.downloadStep = FL_EXIT_TRANSFER_STEP;
	mov	d15,#4
	st.b	[a15]1636,d15
.L168:
.L167:

; ..\flash\FL.c	  1632      }
; ..\flash\FL.c	  1633      return ret;
; ..\flash\FL.c	  1634  }
	ret
.L665:
	
__FL_Programming_function_end:
	.size	FL_Programming,__FL_Programming_function_end-FL_Programming
.L458:
	; End of function
	
	.sdecl	'.text.FL.FBL_GetSignVerifFlag',code,cluster('FBL_GetSignVerifFlag')
	.sect	'.text.FL.FBL_GetSignVerifFlag'
	.align	2
	
	.global	FBL_GetSignVerifFlag

; ..\flash\FL.c	  1635  
; ..\flash\FL.c	  1636  /******************************************************************************/
; ..\flash\FL.c	  1637  /**
; ..\flash\FL.c	  1638   * @brief               <fill pad>
; ..\flash\FL.c	  1639   * 
; ..\flash\FL.c	  1640   * <fill the pad between segments of current block> .
; ..\flash\FL.c	  1641   * @param[in]           <NONE>
; ..\flash\FL.c	  1642   * @param[out]          <NONE>
; ..\flash\FL.c	  1643   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1644   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1645   */
; ..\flash\FL.c	  1646  /******************************************************************************/
; ..\flash\FL.c	  1647  #if(FL_USE_GAP_FILL == STD_ON)
; ..\flash\FL.c	  1648  STATIC FL_ResultType FL_FillGap(void)
; ..\flash\FL.c	  1649  {
; ..\flash\FL.c	  1650      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  1651      uint8 segmentIndex = 0;
; ..\flash\FL.c	  1652      uint32 startAddress = FL_BlkInfo[FldownloadStatus.blockIndex].address;
; ..\flash\FL.c	  1653      uint32 gapLength;
; ..\flash\FL.c	  1654  
; ..\flash\FL.c	  1655      /* set the download datas with FL_GAP_FILL_VALUE */
; ..\flash\FL.c	  1656      Appl_Memset((uint8 *)&FlProgramData[0], (uint8)FL_GAP_FILL_VALUE, (uint32)FL_PROGRAM_SIZE);
; ..\flash\FL.c	  1657  
; ..\flash\FL.c	  1658      flashParamInfo.data = (uint8 *)&FlProgramData[0];
; ..\flash\FL.c	  1659  
; ..\flash\FL.c	  1660      while ((segmentIndex <= FldownloadStatus.segmentList.nrOfSegments) &&
; ..\flash\FL.c	  1661          (FL_OK == ret))
; ..\flash\FL.c	  1662      {
; ..\flash\FL.c	  1663          /* find length ofthe gap in the segment*/
; ..\flash\FL.c	  1664          if (segmentIndex < FldownloadStatus.segmentList.nrOfSegments)
; ..\flash\FL.c	  1665          {
; ..\flash\FL.c	  1666              gapLength = FldownloadStatus.segmentList.segmentInfo[segmentIndex].address
; ..\flash\FL.c	  1667              - startAddress;
; ..\flash\FL.c	  1668          }
; ..\flash\FL.c	  1669          else
; ..\flash\FL.c	  1670          {
; ..\flash\FL.c	  1671              gapLength = (FL_BlkInfo[FldownloadStatus.blockIndex].address +
; ..\flash\FL.c	  1672                  FL_BlkInfo[FldownloadStatus.blockIndex].length) - startAddress;
; ..\flash\FL.c	  1673          }
; ..\flash\FL.c	  1674  
; ..\flash\FL.c	  1675          gapLength &= ~(FL_FLASH_ALIGN_SIZE - 1);
; ..\flash\FL.c	  1676  
; ..\flash\FL.c	  1677          /* set the flash download address of gap */
; ..\flash\FL.c	  1678          flashParamInfo.address = startAddress;
; ..\flash\FL.c	  1679  
; ..\flash\FL.c	  1680          while ((gapLength > 0) && (FL_OK == ret))
; ..\flash\FL.c	  1681          {
; ..\flash\FL.c	  1682              if (gapLength >= FL_PROGRAM_SIZE)
; ..\flash\FL.c	  1683              {
; ..\flash\FL.c	  1684                  /* set the download length */
; ..\flash\FL.c	  1685                  flashParamInfo.length = FL_PROGRAM_SIZE;
; ..\flash\FL.c	  1686  
; ..\flash\FL.c	  1687                  /* update the gap length*/
; ..\flash\FL.c	  1688                  gapLength -= FL_PROGRAM_SIZE;
; ..\flash\FL.c	  1689              }
; ..\flash\FL.c	  1690              else
; ..\flash\FL.c	  1691              {
; ..\flash\FL.c	  1692                  /* the last gap*/
; ..\flash\FL.c	  1693                  flashParamInfo.length = gapLength;
; ..\flash\FL.c	  1694                  gapLength = 0;
; ..\flash\FL.c	  1695              }
; ..\flash\FL.c	  1696  
; ..\flash\FL.c	  1697              /* write the flash of the FlashParam for gap*/
; ..\flash\FL.c	  1698              BLFlash_Info.flashWriteFct(&flashParamInfo);
; ..\flash\FL.c	  1699  
; ..\flash\FL.c	  1700              flashParamInfo.address += flashParamInfo.length;
; ..\flash\FL.c	  1701  
; ..\flash\FL.c	  1702              /* check if program pad success */
; ..\flash\FL.c	  1703              if (flashParamInfo.errorCode != kFlashOk)
; ..\flash\FL.c	  1704              {
; ..\flash\FL.c	  1705                  ret = FL_FAILED;
; ..\flash\FL.c	  1706              }
; ..\flash\FL.c	  1707          }
; ..\flash\FL.c	  1708  
; ..\flash\FL.c	  1709          if (segmentIndex < FldownloadStatus.segmentList.nrOfSegments)
; ..\flash\FL.c	  1710          {
; ..\flash\FL.c	  1711              /* set the next start address */
; ..\flash\FL.c	  1712              startAddress = FldownloadStatus.segmentList.segmentInfo[segmentIndex].address +
; ..\flash\FL.c	  1713              FldownloadStatus.segmentList.segmentInfo[segmentIndex].length;
; ..\flash\FL.c	  1714  
; ..\flash\FL.c	  1715              if ((startAddress & (FL_FLASH_ALIGN_SIZE - 1)) > 0)
; ..\flash\FL.c	  1716              {
; ..\flash\FL.c	  1717                  startAddress &= ~(FL_FLASH_ALIGN_SIZE - 1);
; ..\flash\FL.c	  1718                  startAddress += FL_FLASH_ALIGN_SIZE;
; ..\flash\FL.c	  1719              }
; ..\flash\FL.c	  1720          }
; ..\flash\FL.c	  1721  
; ..\flash\FL.c	  1722          segmentIndex ++;
; ..\flash\FL.c	  1723      }
; ..\flash\FL.c	  1724  
; ..\flash\FL.c	  1725      return ret;
; ..\flash\FL.c	  1726  }
; ..\flash\FL.c	  1727  #endif
; ..\flash\FL.c	  1728  
; ..\flash\FL.c	  1729  
; ..\flash\FL.c	  1730  
; ..\flash\FL.c	  1731  
; ..\flash\FL.c	  1732  /******************************************************************************/
; ..\flash\FL.c	  1733  /**
; ..\flash\FL.c	  1734   * @brief               <active job checksum> 之前刷锟斤拷锟剿讹拷锟斤拷锟斤拷锟捷撅拷校锟斤拷锟斤拷锟斤拷锟斤拷锟�
; ..\flash\FL.c	  1735   * 
; ..\flash\FL.c	  1736   * <active checksum that request by 0x31 service> .
; ..\flash\FL.c	  1737   * @param[in]           <NONE>
; ..\flash\FL.c	  1738   * @param[out]          <NONE>
; ..\flash\FL.c	  1739   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1740   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1741   */
; ..\flash\FL.c	  1742  /******************************************************************************/
; ..\flash\FL.c	  1743  uint8 FBL_GetSignVerifFlag(uint8 index) 
; Function FBL_GetSignVerifFlag
.L260:
FBL_GetSignVerifFlag:	.type	func

; ..\flash\FL.c	  1744  {
; ..\flash\FL.c	  1745      return FBL_SignVerifFlags[index];
	movh.a	a15,#@his(FBL_SignVerifFlags)
	lea	a15,[a15]@los(FBL_SignVerifFlags)
.L1481:
	addsc.a	a15,a15,d4,#0
	ld.bu	d2,[a15]
.L1482:

; ..\flash\FL.c	  1746  }
	ret
.L695:
	
__FBL_GetSignVerifFlag_function_end:
	.size	FBL_GetSignVerifFlag,__FBL_GetSignVerifFlag_function_end-FBL_GetSignVerifFlag
.L498:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckSuming',code,cluster('FL_CheckSuming')
	.sect	'.text.FL.FL_CheckSuming'
	.align	2
	

; ..\flash\FL.c	  1747  STATIC FL_ResultType FL_CheckSuming(void)
; Function FL_CheckSuming
.L262:
FL_CheckSuming:	.type	func

; ..\flash\FL.c	  1748  {
; ..\flash\FL.c	  1749      FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  1750      FL_ResultType checkStatus;
; ..\flash\FL.c	  1751      SecM_VerifyParamType verifyParam;
; ..\flash\FL.c	  1752      uint16 l_nrOfSegments;
; ..\flash\FL.c	  1753      int i=0;
; ..\flash\FL.c	  1754      uint8 failcount=0;
; ..\flash\FL.c	  1755  
; ..\flash\FL.c	  1756      for(i=1;i<FL_NUM_LOGICAL_BLOCKS;i++)
; ..\flash\FL.c	  1757      {
; ..\flash\FL.c	  1758      	if(FldownloadStatus.segmentList[i].nrOfSegments!=0)
; ..\flash\FL.c	  1759      	{
; ..\flash\FL.c	  1760      		//  l_nrOfSegments= FldownloadStatus.segmentList[i].nrOfSegments;
; ..\flash\FL.c	  1761      		//  verifyParam.segmentList = &FldownloadStatus.segmentList[i];
; ..\flash\FL.c	  1762  
; ..\flash\FL.c	  1763               if(TRUE == FlIntegrityChkIsHash)
; ..\flash\FL.c	  1764               {
; ..\flash\FL.c	  1765                   if(1u == FBL_GetSignVerifFlag(0))
; ..\flash\FL.c	  1766                   {
; ..\flash\FL.c	  1767                       checkStatus = FL_CheckSuming_Hash(1);
; ..\flash\FL.c	  1768                   }
; ..\flash\FL.c	  1769                   else
; ..\flash\FL.c	  1770                   {
; ..\flash\FL.c	  1771                       checkStatus = FL_FAILED;
; ..\flash\FL.c	  1772                   }
; ..\flash\FL.c	  1773               }
; ..\flash\FL.c	  1774              //  else
; ..\flash\FL.c	  1775              //  {
; ..\flash\FL.c	  1776              //      checkStatus = FL_CheckSuming_CRC16(i);
; ..\flash\FL.c	  1777              //  }
; ..\flash\FL.c	  1778              // checkStatus = SECM_OK;//ignore sign
; ..\flash\FL.c	  1779  			 FldownloadStatus.segmentList[i].blockChecked=TRUE;
	movh.a	a12,#@his(FldownloadStatus)
	lea	a12,[a12]@los(FldownloadStatus)
.L1384:
	mov	d8,#0
	ld.hu	d15,[a12]832
.L807:
	lea	a15,[a12]834
.L1385:
	sub.a	a10,#16
.L803:
	jeq	d15,#0,.L171
.L1386:
	movh.a	a2,#@his(FlIntegrityChkIsHash)
	ld.bu	d15,[a2]@los(FlIntegrityChkIsHash)
.L1387:
	jne	d15,#1,.L172
.L1388:
	mov	d4,d8
	call	FBL_GetSignVerifFlag
.L1389:
	jne	d2,#1,.L173
.L1390:
	mov	d4,#1
	call	FL_CheckSuming_Hash
.L804:
	j	.L174
.L173:
	mov	d2,#1
.L174:
.L172:
	mov	d15,#1
	st.b	[a15],d15
.L1391:

; ..\flash\FL.c	  1780               appblkIntDefault = FALSE;
	movh.a	a15,#@his(appblkIntDefault)
.L1392:
	mov	d15,#0
	st.b	[a15]@los(appblkIntDefault),d15
.L805:

; ..\flash\FL.c	  1781  //             checkStatus=SECM_OK;
; ..\flash\FL.c	  1782      		 if (SECM_OK == checkStatus)
	jne	d2,#0,.L175
.L806:

; ..\flash\FL.c	  1783  		     {
; ..\flash\FL.c	  1784                  Fl_crc = 0;
	movh.a	a15,#@his(Fl_crc)
.L1393:
	st.b	[a15]@los(Fl_crc),d15
.L1394:

; ..\flash\FL.c	  1785  			   /* check if flash driver is downloaded */
; ..\flash\FL.c	  1786  			   if (FALSE == FldownloadStatus.flDrvDownloaded)
	lea	a13,[a12]10
	ld.bu	d15,[a13]
.L1395:
	jne	d15,#0,.L176
.L1396:

; ..\flash\FL.c	  1787  			   {
; ..\flash\FL.c	  1788  				   /* flash driver initialize */
; ..\flash\FL.c	  1789  				   BLFlash_InfoPtr->flashInitFct(&flashParamInfo);
	movh.a	a2,#@his(BLFlash_InfoPtr)
	ld.a	a15,[a2]@los(BLFlash_InfoPtr)
.L1397:
	movh.a	a14,#@his(flashParamInfo)
	lea	a14,[a14]@los(flashParamInfo)
.L1398:
	ld.a	a15,[a15]4
.L1399:
	mov.aa	a4,a14
	calli	a15
.L1400:

; ..\flash\FL.c	  1790  				   /* check if flash driver is initialized success */
; ..\flash\FL.c	  1791  				   if (flashParamInfo.errorCode != kFlashOk)
	ld.bu	d15,[a14]4
.L1401:
	jne	d15,#0,.L177
.L1402:

; ..\flash\FL.c	  1792  				   {
; ..\flash\FL.c	  1793  					   failcount++;
; ..\flash\FL.c	  1794  					   break;
; ..\flash\FL.c	  1795  				   }
; ..\flash\FL.c	  1796  				   else
; ..\flash\FL.c	  1797  				   {
; ..\flash\FL.c	  1798  					   FldownloadStatus.flDrvDownloaded = TRUE;
	mov	d15,#1
	st.b	[a13],d15
.L1403:
	j	.L178
.L176:

; ..\flash\FL.c	  1799  				   }
; ..\flash\FL.c	  1800  			   }
; ..\flash\FL.c	  1801  			   else
; ..\flash\FL.c	  1802  			   {
; ..\flash\FL.c	  1803  				   /* set current block is valid */
; ..\flash\FL.c	  1804  				   FL_NvmInfo.blockInfo[i].blkValid = TRUE;
	fcall	.cocofun_2
.L1404:
	mov	d15,#1
	st.b	[a15]20,d15
.L1405:

; ..\flash\FL.c	  1805  
; ..\flash\FL.c	  1806  				   /* save computed CRC to NVM if CRC success */
; ..\flash\FL.c	  1807  				   FL_NvmInfo.blockInfo[i].blkChecksum =
; ..\flash\FL.c	  1808  					   verifyParam.crcTotle;
	ld.hu	d15,[a10]8
.L1406:
	st.w	[a15]24,d15
.L1407:
	j	.L179
.L175:

; ..\flash\FL.c	  1809  			   }
; ..\flash\FL.c	  1810  		    }
; ..\flash\FL.c	  1811  		    else
; ..\flash\FL.c	  1812  		    {
; ..\flash\FL.c	  1813  			   /* set current block is valid */
; ..\flash\FL.c	  1814  			   FL_NvmInfo.blockInfo[i].blkValid = FALSE;
	fcall	.cocofun_2
.L1408:
	mov	d15,#0
	st.b	[a15]20,d15
.L1409:

; ..\flash\FL.c	  1815  
; ..\flash\FL.c	  1816  			   /* save computed CRC to NVM if CRC success */
; ..\flash\FL.c	  1817  			   FL_NvmInfo.blockInfo[i].blkChecksum =
; ..\flash\FL.c	  1818  				   verifyParam.crcTotle;
	ld.hu	d15,[a10]8
.L1410:
	st.w	[a15]24,d15

; ..\flash\FL.c	  1819  
; ..\flash\FL.c	  1820  			   failcount++;
; ..\flash\FL.c	  1821  		    }
; ..\flash\FL.c	  1822  		 /* update the flashinfo in eep*/
; ..\flash\FL.c	  1823      		// Appl_UpdateTriggerConditionImmediate(1);
; ..\flash\FL.c	  1824  
; ..\flash\FL.c	  1825  		   /* check if EEPROM UPDATE failed */
; ..\flash\FL.c	  1826  		   if (ret != FL_OK)
; ..\flash\FL.c	  1827  		   {
; ..\flash\FL.c	  1828  			   failcount++;
; ..\flash\FL.c	  1829  			   FL_NvmInfo.blockInfo[i].blkValid = FALSE;
; ..\flash\FL.c	  1830  		   }
; ..\flash\FL.c	  1831      	}
; ..\flash\FL.c	  1832      	else
; ..\flash\FL.c	  1833      	{
; ..\flash\FL.c	  1834      		FldownloadStatus.segmentList[i].blockChecked=FALSE;
; ..\flash\FL.c	  1835      	}
; ..\flash\FL.c	  1836      }
; ..\flash\FL.c	  1837      ret = FL_UpdateNvm();
.L177:
	mov	d8,#1
	j	.L180
.L171:
	mov	d15,#0
	st.b	[a15],d15
.L180:
.L179:
.L178:
	call	FL_UpdateNvm
.L808:

; ..\flash\FL.c	  1838  
; ..\flash\FL.c	  1839      if(ret!= FL_OK)
; ..\flash\FL.c	  1840      {
; ..\flash\FL.c	  1841      	failcount++;
	cadd	d8,d2,d8,#1
.L1411:

; ..\flash\FL.c	  1842      }
; ..\flash\FL.c	  1843  
; ..\flash\FL.c	  1844     if(failcount>0)
	ne	d2,d8,#0
.L1412:

; ..\flash\FL.c	  1845     {
; ..\flash\FL.c	  1846  
; ..\flash\FL.c	  1847  	   ret=FL_FAILED;
; ..\flash\FL.c	  1848     }
; ..\flash\FL.c	  1849     else
; ..\flash\FL.c	  1850     {
; ..\flash\FL.c	  1851  	   ret=FL_OK;
; ..\flash\FL.c	  1852     }
; ..\flash\FL.c	  1853     FldownloadStatus.downloadStep = FL_REQUEST_STEP;
	mov	d15,#0
	st.b	[a12]1636,d15
.L1413:

; ..\flash\FL.c	  1854     return ret;
; ..\flash\FL.c	  1855  }
	ret
.L667:
	
__FL_CheckSuming_function_end:
	.size	FL_CheckSuming,__FL_CheckSuming_function_end-FL_CheckSuming
.L463:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckSuming_Hash',code,cluster('FL_CheckSuming_Hash')
	.sect	'.text.FL.FL_CheckSuming_Hash'
	.align	2
	

; ..\flash\FL.c	  1856  
; ..\flash\FL.c	  1857  /******************************************************************************/
; ..\flash\FL.c	  1858  /**
; ..\flash\FL.c	  1859   * @brief               <active job CRC16 check>
; ..\flash\FL.c	  1860   * 
; ..\flash\FL.c	  1861   * <active checksum that request by 0x31 service> .
; ..\flash\FL.c	  1862   * @param[in]           <NONE>
; ..\flash\FL.c	  1863   * @param[out]          <NONE>
; ..\flash\FL.c	  1864   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1865   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1866   */
; ..\flash\FL.c	  1867  /******************************************************************************/
; ..\flash\FL.c	  1868  STATIC FL_ResultType FL_CheckSuming_CRC16(uint8 index)
; ..\flash\FL.c	  1869  {
; ..\flash\FL.c	  1870      SecM_VerifyParamType verifyParam;
; ..\flash\FL.c	  1871      uint16 l_nrOfSegments;
; ..\flash\FL.c	  1872      FL_ResultType checkStatus;
; ..\flash\FL.c	  1873      uint8 RequestResultPtr = 0;
; ..\flash\FL.c	  1874  
; ..\flash\FL.c	  1875  //     l_nrOfSegments= FldownloadStatus.segmentList[index].nrOfSegments;
; ..\flash\FL.c	  1876  //     verifyParam.segmentList = &FldownloadStatus.segmentList[index];
; ..\flash\FL.c	  1877  
; ..\flash\FL.c	  1878  //     switch (FL_BlkInfo[index].crcaddrtype)
; ..\flash\FL.c	  1879  //     {
; ..\flash\FL.c	  1880  //     case NO_CRC:
; ..\flash\FL.c	  1881  //     {
; ..\flash\FL.c	  1882  //        break;
; ..\flash\FL.c	  1883  //     }
; ..\flash\FL.c	  1884  //     case LAST_ADDR:
; ..\flash\FL.c	  1885  //     {
; ..\flash\FL.c	  1886  //        verifyParam.verificationData = (uint8 *)FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments - 1].address;
; ..\flash\FL.c	  1887  //        verifyParam.verificationData += FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments - 1].length;
; ..\flash\FL.c	  1888  // #if (CAL_CRC32 == CAL_METHOD)
; ..\flash\FL.c	  1889  //        verifyParam.verificationData -= 4; /* last 4 byte is CRC value */
; ..\flash\FL.c	  1890  //        FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments - 1].length -= 4;
; ..\flash\FL.c	  1891  // #else
; ..\flash\FL.c	  1892  //        verifyParam.verificationData -= 2; /* last 2 byte is CRC value */
; ..\flash\FL.c	  1893  //        FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments - 1].length -= 2;
; ..\flash\FL.c	  1894  // #endif
; ..\flash\FL.c	  1895  //        break;
; ..\flash\FL.c	  1896  //     }
; ..\flash\FL.c	  1897  //     case HEAD_ADDR:
; ..\flash\FL.c	  1898  //     {
; ..\flash\FL.c	  1899  // #if (CAL_CRC32 == CAL_METHOD)
; ..\flash\FL.c	  1900  //        verifyParam.verificationData = (uint8 *)FldownloadStatus.segmentList[index].segmentInfo[0].address;
; ..\flash\FL.c	  1901  //        FldownloadStatus.segmentList[index].segmentInfo[0].address += 4;
; ..\flash\FL.c	  1902  //        FldownloadStatus.segmentList[index].segmentInfo[0].length -= 4;
; ..\flash\FL.c	  1903  
; ..\flash\FL.c	  1904  // #else
; ..\flash\FL.c	  1905  //        FldownloadStatus.segmentList[index].segmentInfo[0].address += 2;
; ..\flash\FL.c	  1906  //        FldownloadStatus.segmentList[index].segmentInfo[0].length -= 2;
; ..\flash\FL.c	  1907  //        verifyParam.verificationData = (uint8 *)FldownloadStatus.segmentList[index].segmentInfo[0].address;
; ..\flash\FL.c	  1908  // #endif
; ..\flash\FL.c	  1909  //        break;
; ..\flash\FL.c	  1910  //     }
; ..\flash\FL.c	  1911  //     }
; ..\flash\FL.c	  1912  
; ..\flash\FL.c	  1913  //     /* Execute the CRC16 */
; ..\flash\FL.c	  1914  //     checkStatus = SecM_Verification(&verifyParam);
; ..\flash\FL.c	  1915  
; ..\flash\FL.c	  1916  //     if (index == 0x00u)
; ..\flash\FL.c	  1917  //     {
; ..\flash\FL.c	  1918  //        Ram_B301[1] = 1;
; ..\flash\FL.c	  1919  //        Ram_B301[2] = 0;
; ..\flash\FL.c	  1920  //        Ram_B301[3] = 0;
; ..\flash\FL.c	  1921  //        Ram_B301[4] = 0;
; ..\flash\FL.c	  1922  //        Ram_B301[5] = 0;
; ..\flash\FL.c	  1923  //     }
; ..\flash\FL.c	  1924  //     else if (index == 0x01u)
; ..\flash\FL.c	  1925  //     {
; ..\flash\FL.c	  1926  //        Ram_B301[26] = 0x5A;
; ..\flash\FL.c	  1927  //        Ram_B301[27] = 0;
; ..\flash\FL.c	  1928  //        Ram_B301[28] = 0;
; ..\flash\FL.c	  1929  //        Ram_B301[29] = 0;
; ..\flash\FL.c	  1930  //        Ram_B301[30] = 0;
; ..\flash\FL.c	  1931  //     }
; ..\flash\FL.c	  1932  //     else
; ..\flash\FL.c	  1933  //     {
; ..\flash\FL.c	  1934  //        Ram_B301[(1 + 5 * (index - 1))] = index;
; ..\flash\FL.c	  1935  //        Ram_B301[(2 + 5 * (index - 1))] = 0;
; ..\flash\FL.c	  1936  //        Ram_B301[(3 + 5 * (index - 1))] = 0;
; ..\flash\FL.c	  1937  //        Ram_B301[(4 + 5 * (index - 1))] = 0;
; ..\flash\FL.c	  1938  //        Ram_B301[(5 + 5 * (index - 1))] = 0;
; ..\flash\FL.c	  1939  //     }
; ..\flash\FL.c	  1940  
; ..\flash\FL.c	  1941  //     if (SECM_OK == checkStatus)
; ..\flash\FL.c	  1942  //     {
; ..\flash\FL.c	  1943  //        if (index == 0x00u)
; ..\flash\FL.c	  1944  //        {
; ..\flash\FL.c	  1945  //            Ram_B301[4] = verifyParam.verificationData[0];
; ..\flash\FL.c	  1946  //            Ram_B301[5] = verifyParam.verificationData[1];
; ..\flash\FL.c	  1947  //        }
; ..\flash\FL.c	  1948  //        else if (index == 0x01u)
; ..\flash\FL.c	  1949  //        {
; ..\flash\FL.c	  1950  //            Ram_B301[29] = verifyParam.verificationData[0];
; ..\flash\FL.c	  1951  //            Ram_B301[30] = verifyParam.verificationData[1];
; ..\flash\FL.c	  1952  //        }
; ..\flash\FL.c	  1953  //        else
; ..\flash\FL.c	  1954  //        {
; ..\flash\FL.c	  1955  //            Ram_B301[(4 + 5 * (index - 1))] = verifyParam.verificationData[0];
; ..\flash\FL.c	  1956  //            Ram_B301[(5 + 5 * (index - 1))] = verifyParam.verificationData[1];
; ..\flash\FL.c	  1957  //        }
; ..\flash\FL.c	  1958  //     }
; ..\flash\FL.c	  1959  //     // NvM_WriteBlock(EEP_DIDB301_BLOCK_ID,NULL);
; ..\flash\FL.c	  1960  // 	// NvM_WriteAll();
; ..\flash\FL.c	  1961  // 	// do
; ..\flash\FL.c	  1962  // 	// {
; ..\flash\FL.c	  1963  // 	// 	#include "Appl.h"
; ..\flash\FL.c	  1964  		
; ..\flash\FL.c	  1965  // 	// 	Appl_UpdateTriggerCondition();
; ..\flash\FL.c	  1966  // 	// 	NvM_MainFunction();
; ..\flash\FL.c	  1967  // 	// 	Fee_MainFunction();
; ..\flash\FL.c	  1968  // 	// 	Fls_17_Dmu_MainFunction();
; ..\flash\FL.c	  1969  // 	// 	NvM_GetErrorStatus(NvMConf___MultiBlockRequest,&RequestResultPtr);
; ..\flash\FL.c	  1970  // 	// }while(RequestResultPtr == NVM_REQ_PENDING);
; ..\flash\FL.c	  1971  
; ..\flash\FL.c	  1972  //     /* save computed CRC to NVM if CRC success */
; ..\flash\FL.c	  1973  //     FL_NvmInfo.blockInfo[index].blkChecksum =
; ..\flash\FL.c	  1974  //         verifyParam.crcTotle;
; ..\flash\FL.c	  1975  
; ..\flash\FL.c	  1976      return (checkStatus == SECM_OK) ? FL_OK : FL_FAILED;
; ..\flash\FL.c	  1977  }
; ..\flash\FL.c	  1978  
; ..\flash\FL.c	  1979  /******************************************************************************/
; ..\flash\FL.c	  1980  /**
; ..\flash\FL.c	  1981   * @brief               <active job Hash check> 脙鈥撁偮冣�∶偮懊冣�姑偮⒚冿拷脙鈥毭冿拷脙鈥姑偮睹兟犆冣�懊冣劉脙艩脙陆脗戮脙锟矫偮久冿拷脙锟矫偮Ｃ冣�樏兟┟偮睹兟犆冣�懊冣劉脙艩脙陆脗戮脙锟�
; ..\flash\FL.c	  1982   * 
; ..\flash\FL.c	  1983   * <active checksum that request by 0x31 service> .
; ..\flash\FL.c	  1984   * @param[in]           <NONE>
; ..\flash\FL.c	  1985   * @param[out]          <NONE>
; ..\flash\FL.c	  1986   * @param[in/out]       <NONE>
; ..\flash\FL.c	  1987   * @return              <FL_ResultType>    
; ..\flash\FL.c	  1988   */
; ..\flash\FL.c	  1989  /******************************************************************************/
; ..\flash\FL.c	  1990  STATIC FL_ResultType FL_CheckSuming_Hash(uint8 index)
; Function FL_CheckSuming_Hash
.L264:
FL_CheckSuming_Hash:	.type	func

; ..\flash\FL.c	  1991  {
; ..\flash\FL.c	  1992      uint32 address;
; ..\flash\FL.c	  1993      uint32 length;
; ..\flash\FL.c	  1994      Std_ReturnType checkStatus;
; ..\flash\FL.c	  1995      uint16 l_nrOfSegments;
; ..\flash\FL.c	  1996  
; ..\flash\FL.c	  1997      l_nrOfSegments = FldownloadStatus.segmentList[index].nrOfSegments;
	fcall	.cocofun_1
.L809:
	ld.hu	d15,[a15]832
.L811:

; ..\flash\FL.c	  1998      address = FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments - 1].address;
; ..\flash\FL.c	  1999      length = FldownloadStatus.segmentList[index].segmentInfo[l_nrOfSegments - 1].length;
; ..\flash\FL.c	  2000  
; ..\flash\FL.c	  2001      /* To-Do: when executing hash calculation, consider the P2* time-out situation */
; ..\flash\FL.c	  2002      checkStatus = Secure_HashVerification(address, length, index);
	mov	d6,#1
.L1418:
	addsc.a	a15,a15,d15,#3
.L1419:
	ld.w	d4,[a15]828
.L810:
	ld.w	d5,[a15]832
.L813:
	call	Secure_HashVerification
.L812:

; ..\flash\FL.c	  2003      return (checkStatus == SECURE_PASS) ? FL_OK : FL_FAILED;
; ..\flash\FL.c	  2004  }
	ne	d2,d2,#0
	ret
.L672:
	
__FL_CheckSuming_Hash_function_end:
	.size	FL_CheckSuming_Hash,__FL_CheckSuming_Hash_function_end-FL_CheckSuming_Hash
.L468:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckProgramIntegrity',code,cluster('FL_CheckProgramIntegrity')
	.sect	'.text.FL.FL_CheckProgramIntegrity'
	.align	2
	
	.global	FL_CheckProgramIntegrity

; ..\flash\FL.c	  2005  /******************************************************************************/
; ..\flash\FL.c	  2006  /**
; ..\flash\FL.c	  2007   * @brief               <check programming integrity>
; ..\flash\FL.c	  2008   *
; ..\flash\FL.c	  2009   *
; ..\flash\FL.c	  2010   *
; ..\flash\FL.c	  2011   *
; ..\flash\FL.c	  2012   * Service ID   :       <NONE>
; ..\flash\FL.c	  2013   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	  2014   * Reentrancy           <Non Reentrant>
; ..\flash\FL.c	  2015   * @param[in]           <NONE>
; ..\flash\FL.c	  2016   * @param[out]          <NONE>
; ..\flash\FL.c	  2017   * @param[in/out]       <NONE>
; ..\flash\FL.c	  2018   * @return              <FL_ResultType>
; ..\flash\FL.c	  2019   */
; ..\flash\FL.c	  2020  /******************************************************************************/
; ..\flash\FL.c	  2021  uint8 FL_CheckProgramIntegrity(void)
; Function FL_CheckProgramIntegrity
.L266:
FL_CheckProgramIntegrity:	.type	func

; ..\flash\FL.c	  2022  {
; ..\flash\FL.c	  2023  	uint8 ret = NO_INTEGRITY_ERR;
; ..\flash\FL.c	  2024  	uint8 i=0;
; ..\flash\FL.c	  2025  
; ..\flash\FL.c	  2026  
; ..\flash\FL.c	  2027      if(FL_NvmInfo.blockInfo[APPBLOCKINDEX].blkValid==FALSE)
	mov	d2,#0
	movh.a	a15,#@his(FL_NvmInfo+20)
.L814:
	ld.bu	d15,[a15]@los(FL_NvmInfo+20)
.L1151:

; ..\flash\FL.c	  2028      {
; ..\flash\FL.c	  2029      	ret|=APP_INTEGRITY_ERR;
; ..\flash\FL.c	  2030      }
; ..\flash\FL.c	  2031  
; ..\flash\FL.c	  2032  //    if(FL_NvmInfo.blockInfo[CALDATABLOCKINDEX].blkValid==FALSE)
; ..\flash\FL.c	  2033  //	{
; ..\flash\FL.c	  2034  //		ret|=CAL_INTEGRITY_ERR;
; ..\flash\FL.c	  2035  //	}
; ..\flash\FL.c	  2036  //
; ..\flash\FL.c	  2037  
; ..\flash\FL.c	  2038      return ret;
; ..\flash\FL.c	  2039  }
	cmovn	d2,d15,#1
	ret
.L638:
	
__FL_CheckProgramIntegrity_function_end:
	.size	FL_CheckProgramIntegrity,__FL_CheckProgramIntegrity_function_end-FL_CheckProgramIntegrity
.L388:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckCompatibility',code,cluster('FL_CheckCompatibility')
	.sect	'.text.FL.FL_CheckCompatibility'
	.align	2
	

; ..\flash\FL.c	  2040  
; ..\flash\FL.c	  2041  
; ..\flash\FL.c	  2042  /******************************************************************************/
; ..\flash\FL.c	  2043  /**
; ..\flash\FL.c	  2044   * @brief               <check programming integrity>
; ..\flash\FL.c	  2045   *
; ..\flash\FL.c	  2046   *
; ..\flash\FL.c	  2047   *
; ..\flash\FL.c	  2048   *
; ..\flash\FL.c	  2049   * Service ID   :       <NONE>
; ..\flash\FL.c	  2050   * Sync/Async   :       <Synchronous>
; ..\flash\FL.c	  2051   * Reentrancy           <Non Reentrant>
; ..\flash\FL.c	  2052   * @param[in]           <NONE>
; ..\flash\FL.c	  2053   * @param[out]          <NONE>
; ..\flash\FL.c	  2054   * @param[in/out]       <NONE>
; ..\flash\FL.c	  2055   * @return              <FL_ResultType>
; ..\flash\FL.c	  2056   */
; ..\flash\FL.c	  2057  /******************************************************************************/
; ..\flash\FL.c	  2058  #include "Os.h"
; ..\flash\FL.c	  2059  const uint8 Compatibility_Code[4] = {'V', '1', '.', '1'};
; ..\flash\FL.c	  2060  static FL_ResultType FL_CheckCompatibility(void)
; Function FL_CheckCompatibility
.L268:
FL_CheckCompatibility:	.type	func

; ..\flash\FL.c	  2061  {
; ..\flash\FL.c	  2062  	FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  2063  	// uint8 sourcedata[4],destdata[4];
; ..\flash\FL.c	  2064      uint8 destdata[4];
; ..\flash\FL.c	  2065  	// FlashReadMemory(sourcedata,FBL_APP_CPB_FBL_ADDRESS,4);
; ..\flash\FL.c	  2066  	FlashReadMemory(destdata,FBL_APP_CPB_APP_ADDRESS,4);
	mov.u	d4,#32880
	sub.a	a10,#8
.L815:
	addih	d4,d4,#40963
	mov.aa	a4,a10
.L1452:
	mov	d5,#4
	call	FlashReadMemory
.L1453:

; ..\flash\FL.c	  2067      appblkCpbDefault = FALSE;
	movh.a	a15,#@his(appblkCpbDefault)
.L1454:
	mov	d15,#0
	st.b	[a15]@los(appblkCpbDefault),d15
.L816:

; ..\flash\FL.c	  2068      /*check project byte and cpbbyte*/
; ..\flash\FL.c	  2069      // if((U8_FBL_APP_CPB_BYTE1==destdata[0])&&(U8_FBL_APP_CPB_BYTE3==destdata[2])&&(destdata[1]>=sourcedata[1])&&(destdata[3]>=sourcedata[3]))
; ..\flash\FL.c	  2070     /* if(TRUE == FL_NvmInfo.blockInfo[APPBLOCKINDEX].blkValid)
; ..\flash\FL.c	  2071      {
; ..\flash\FL.c	  2072          if((Compatibility_Code[0]==destdata[0])&&(Compatibility_Code[1]==destdata[1])&&(Compatibility_Code[2]==destdata[2])&&(Compatibility_Code[3]==destdata[3]))
; ..\flash\FL.c	  2073          {    
; ..\flash\FL.c	  2074             FL_NvmInfo.isAppFblCpb=TRUE;
; ..\flash\FL.c	  2075          }
; ..\flash\FL.c	  2076          else
; ..\flash\FL.c	  2077          {
; ..\flash\FL.c	  2078          FL_NvmInfo.isAppFblCpb=FALSE;
; ..\flash\FL.c	  2079          }    
; ..\flash\FL.c	  2080      }
; ..\flash\FL.c	  2081      else
; ..\flash\FL.c	  2082      {
; ..\flash\FL.c	  2083  	   FL_NvmInfo.isAppFblCpb=FALSE;
; ..\flash\FL.c	  2084      }*/
; ..\flash\FL.c	  2085      FL_NvmInfo.isAppFblCpb=TRUE;
	fcall	.cocofun_2
.L817:
	mov	d15,#1
	st.b	[a15]41,d15
.L1455:

; ..\flash\FL.c	  2086     FL_NvmInfo.isAppCalCpb=TRUE;
	st.b	[a15]40,d15
.L1456:

; ..\flash\FL.c	  2087     ret=FL_UpdateNvm();
	j	FL_UpdateNvm
.L683:
	
__FL_CheckCompatibility_function_end:
	.size	FL_CheckCompatibility,__FL_CheckCompatibility_function_end-FL_CheckCompatibility
.L478:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckProgramDependencies',code,cluster('FL_CheckProgramDependencies')
	.sect	'.text.FL.FL_CheckProgramDependencies'
	.align	2
	
	.global	FL_CheckProgramDependencies

; ..\flash\FL.c	  2088  
; ..\flash\FL.c	  2089     return ret;
; ..\flash\FL.c	  2090  }
; ..\flash\FL.c	  2091  
; ..\flash\FL.c	  2092  FL_ResultType FL_CheckProgramDependencies(uint8* errorvalue)
; Function FL_CheckProgramDependencies
.L270:
FL_CheckProgramDependencies:	.type	func

; ..\flash\FL.c	  2093  {
; ..\flash\FL.c	  2094  	FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  2095  	*errorvalue=00;
	mov	d15,#0
	st.b	[a4],d15
.L1156:

; ..\flash\FL.c	  2096  //   if(FALSE==FL_NvmInfo.isAppCalCpb)
; ..\flash\FL.c	  2097  //   {
; ..\flash\FL.c	  2098  //	   *errorvalue|=CAL_APP_CPB_ERR ;
; ..\flash\FL.c	  2099  //   }
; ..\flash\FL.c	  2100  
; ..\flash\FL.c	  2101     if(FALSE==FL_NvmInfo.isAppFblCpb)
	movh.a	a15,#@his(FL_NvmInfo+41)
.L1157:
	ld.bu	d15,[a15]@los(FL_NvmInfo+41)
.L1158:
	jne	d15,#0,.L186
.L1159:

; ..\flash\FL.c	  2102     {
; ..\flash\FL.c	  2103  	   *errorvalue|=FBL_APP_CPB_ERR;
	mov	d15,#1
	st.b	[a4],d15
.L186:

; ..\flash\FL.c	  2104     }
; ..\flash\FL.c	  2105  
; ..\flash\FL.c	  2106      return ret;
; ..\flash\FL.c	  2107  }
	mov	d2,#0
	ret
.L640:
	
__FL_CheckProgramDependencies_function_end:
	.size	FL_CheckProgramDependencies,__FL_CheckProgramDependencies_function_end-FL_CheckProgramDependencies
.L393:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckProgramCounter',code,cluster('FL_CheckProgramCounter')
	.sect	'.text.FL.FL_CheckProgramCounter'
	.align	2
	
	.global	FL_CheckProgramCounter

; ..\flash\FL.c	  2108  
; ..\flash\FL.c	  2109  uint16 FL_CheckProgramCounter(void)
; Function FL_CheckProgramCounter
.L272:
FL_CheckProgramCounter:	.type	func

; ..\flash\FL.c	  2110  {
; ..\flash\FL.c	  2111  	FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  2112  	uint16 counter=0;
; ..\flash\FL.c	  2113  	// counter=  FL_NvmInfo.blockInfo[APPBLOCKINDEX].blkProgAttempt;
; ..\flash\FL.c	  2114      counter = lastcounter;
	movh.a	a15,#@his(lastcounter)
	ld.hu	d2,[a15]@los(lastcounter)
.L1184:

; ..\flash\FL.c	  2115      return counter;
; ..\flash\FL.c	  2116  }
	ret
.L646:
	
__FL_CheckProgramCounter_function_end:
	.size	FL_CheckProgramCounter,__FL_CheckProgramCounter_function_end-FL_CheckProgramCounter
.L403:
	; End of function
	
	.sdecl	'.text.FL.FL_CheckSWVerification',code,cluster('FL_CheckSWVerification')
	.sect	'.text.FL.FL_CheckSWVerification'
	.align	2
	
	.global	FL_CheckSWVerification

; ..\flash\FL.c	  2117  
; ..\flash\FL.c	  2118  FL_ResultType FL_CheckSWVerification(uint8* checkdata)
; Function FL_CheckSWVerification
.L274:
FL_CheckSWVerification:	.type	func

; ..\flash\FL.c	  2119  {
; ..\flash\FL.c	  2120  	FL_ResultType rtn=0;
; ..\flash\FL.c	  2121  	int i;
; ..\flash\FL.c	  2122  	checkdata[0]=FL_NUM_LOGICAL_BLOCKS;
	mov	d15,#2
	st.b	[a4],d15
.L1164:

; ..\flash\FL.c	  2123  
; ..\flash\FL.c	  2124  	for(i=0;i<FL_NUM_LOGICAL_BLOCKS;i++)
	mov	d0,#0
.L818:
	mov	d2,d0
	mov.a	a2,#1
.L189:

; ..\flash\FL.c	  2125  	{
; ..\flash\FL.c	  2126  		checkdata[i*5+1]=FL_BlkInfo[i].moduleid;
	mul	d15,d0,#5
	movh.a	a5,#@his(FL_BlkInfo)
	lea	a5,[a5]@los(FL_BlkInfo)
.L1165:
	addsc.a	a15,a4,d15,#0
.L1166:
	mul	d15,d0,#24
	addsc.a	a5,a5,d15,#0
.L1167:
	ld.bu	d15,[a5]20
.L1168:
	st.b	[a15]1,d15
.L1169:

; ..\flash\FL.c	  2127  #if (CAL_CRC32 == CAL_METHOD)
; ..\flash\FL.c	  2128  		checkdata[i*5+2]=(uint8)((FL_NvmInfo.blockInfo[i].blkChecksum&0xFF000000)>>24);
; ..\flash\FL.c	  2129  		checkdata[i*5+3]=(uint8)((FL_NvmInfo.blockInfo[i].blkChecksum&0x00FF0000)>>16);
; ..\flash\FL.c	  2130  		checkdata[i*5+4]=(uint8)((FL_NvmInfo.blockInfo[i].blkChecksum&0x0000FF00)>>8);
; ..\flash\FL.c	  2131  		checkdata[i*5+5]=(uint8)(FL_NvmInfo.blockInfo[i].blkChecksum&0x000000FF);
; ..\flash\FL.c	  2132  
; ..\flash\FL.c	  2133  #else
; ..\flash\FL.c	  2134  		checkdata[i*5+2]=0x00;
; ..\flash\FL.c	  2135  		checkdata[i*5+3]=0x00;
; ..\flash\FL.c	  2136  		checkdata[i*5+4]=(uint8)((FL_NvmInfo.blockInfo[i].blkChecksum&0x0000FF00)>>8);
	mul	d15,d0,#20
	movh.a	a5,#@his(FL_NvmInfo)
.L1170:
	st.b	[a15]2,d2
.L1171:
	lea	a5,[a5]@los(FL_NvmInfo)
.L1172:
	addsc.a	a5,a5,d15,#0
.L1173:
	st.b	[a15]3,d2
.L1174:
	add	d0,#1
.L1175:
	ld.w	d15,[+a5]4
.L1176:
	sh	d15,#-8
	st.b	[a15]4,d15
.L1177:

; ..\flash\FL.c	  2137  		checkdata[i*5+5]=(uint8)(FL_NvmInfo.blockInfo[i].blkChecksum&0x000000FF);
	ld.w	d15,[a5]
.L1178:
	st.b	[a15]5,d15
	loop	a2,.L189
.L1179:

; ..\flash\FL.c	  2138  
; ..\flash\FL.c	  2139  #endif
; ..\flash\FL.c	  2140  	}
; ..\flash\FL.c	  2141  	return rtn;
; ..\flash\FL.c	  2142  
; ..\flash\FL.c	  2143  }
	ret
.L642:
	
__FL_CheckSWVerification_function_end:
	.size	FL_CheckSWVerification,__FL_CheckSWVerification_function_end-FL_CheckSWVerification
.L398:
	; End of function
	
	.sdecl	'.text.FL.FL_SignVerifFlags',code,cluster('FL_SignVerifFlags')
	.sect	'.text.FL.FL_SignVerifFlags'
	.align	2
	
	.global	FL_SignVerifFlags

; ..\flash\FL.c	  2144  FL_ResultType FL_SignVerifFlags(void)
; Function FL_SignVerifFlags
.L276:
FL_SignVerifFlags:	.type	func

; ..\flash\FL.c	  2145  {
; ..\flash\FL.c	  2146      FL_ResultType ret = FBL_FALSE;
; ..\flash\FL.c	  2147      if (FL_HEADER_EXIT_TRANSFER_STEP == FldownloadStatus.downloadStep)
	movh.a	a15,#@his(FldownloadStatus+1636)
.L1189:
	ld.bu	d15,[a15]@los(FldownloadStatus+1636)
.L1190:

; ..\flash\FL.c	  2148      {
; ..\flash\FL.c	  2149          ret = FBL_TRUE;
; ..\flash\FL.c	  2150      }
; ..\flash\FL.c	  2151      else
; ..\flash\FL.c	  2152      {
; ..\flash\FL.c	  2153          ret = FBL_FALSE;
; ..\flash\FL.c	  2154      }
; ..\flash\FL.c	  2155      return ret;
; ..\flash\FL.c	  2156  }
	eq	d2,d15,#2
	ret
.L647:
	
__FL_SignVerifFlags_function_end:
	.size	FL_SignVerifFlags,__FL_SignVerifFlags_function_end-FL_SignVerifFlags
.L408:
	; End of function
	
	.sdecl	'.text.FL.FL_SetRequestStep',code,cluster('FL_SetRequestStep')
	.sect	'.text.FL.FL_SetRequestStep'
	.align	2
	
	.global	FL_SetRequestStep

; ..\flash\FL.c	  2157  void FL_SetRequestStep(void)
; Function FL_SetRequestStep
.L278:
FL_SetRequestStep:	.type	func

; ..\flash\FL.c	  2158  {
; ..\flash\FL.c	  2159      /* set the request step*/
; ..\flash\FL.c	  2160      FldownloadStatus.downloadStep = FL_REQUEST_STEP;         
	fcall	.cocofun_1
.L1195:
	mov	d15,#0
	st.b	[a15]1636,d15
.L1196:

; ..\flash\FL.c	  2161      FldownloadStatus.activeJob = FL_JOB_IDLE;
	st.b	[a15]1637,d15
.L1197:

; ..\flash\FL.c	  2162  }
	ret
.L648:
	
__FL_SetRequestStep_function_end:
	.size	FL_SetRequestStep,__FL_SetRequestStep_function_end-FL_SetRequestStep
.L413:
	; End of function
	
	.sdecl	'.text.FL.FL_SetChecksumStep',code,cluster('FL_SetChecksumStep')
	.sect	'.text.FL.FL_SetChecksumStep'
	.align	2
	
	.global	FL_SetChecksumStep

; ..\flash\FL.c	  2163  void FL_SetChecksumStep(void)
; Function FL_SetChecksumStep
.L280:
FL_SetChecksumStep:	.type	func

; ..\flash\FL.c	  2164  {
; ..\flash\FL.c	  2165      /* set the checksum step*/
; ..\flash\FL.c	  2166      FldownloadStatus.downloadStep = FL_CHECKSUM_STEP;
	fcall	.cocofun_1
.L1202:
	mov	d15,#5
	st.b	[a15]1636,d15
.L1203:

; ..\flash\FL.c	  2167      FldownloadStatus.activeJob = FL_JOB_IDLE;
	fcall	.cocofun_4
.L1204:

; ..\flash\FL.c	  2168  }
	ret
.L649:
	
__FL_SetChecksumStep_function_end:
	.size	FL_SetChecksumStep,__FL_SetChecksumStep_function_end-FL_SetChecksumStep
.L418:
	; End of function
	
	.sdecl	'.text.FL.UpdateSecurityErrorFlag',code,cluster('UpdateSecurityErrorFlag')
	.sect	'.text.FL.UpdateSecurityErrorFlag'
	.align	2
	
	.global	UpdateSecurityErrorFlag

; ..\flash\FL.c	  2169  void UpdateSecurityErrorFlag(void)
; Function UpdateSecurityErrorFlag
.L282:
UpdateSecurityErrorFlag:	.type	func

; ..\flash\FL.c	  2170  {
; ..\flash\FL.c	  2171      EEP_SetSecErrFlag(&SecurityErrorFlag);
	movh.a	a4,#@his(SecurityErrorFlag)
	lea	a4,[a4]@los(SecurityErrorFlag)
	j	EEP_SetSecErrFlag
.L650:
	
__UpdateSecurityErrorFlag_function_end:
	.size	UpdateSecurityErrorFlag,__UpdateSecurityErrorFlag_function_end-UpdateSecurityErrorFlag
.L423:
	; End of function
	
	.sdecl	'.text.FL.FL_GetSecErrFlag',code,cluster('FL_GetSecErrFlag')
	.sect	'.text.FL.FL_GetSecErrFlag'
	.align	2
	
	.global	FL_GetSecErrFlag

; ..\flash\FL.c	  2172  
; ..\flash\FL.c	  2173  }
; ..\flash\FL.c	  2174  uint8 FL_GetSecErrFlag(void)
; Function FL_GetSecErrFlag
.L284:
FL_GetSecErrFlag:	.type	func

; ..\flash\FL.c	  2175  {
; ..\flash\FL.c	  2176  	FL_ResultType ret = FL_OK;
; ..\flash\FL.c	  2177  	uint8 flag=0;
; ..\flash\FL.c	  2178      flag = SecurityErrorFlag;
	movh.a	a15,#@his(SecurityErrorFlag)
	ld.bu	d2,[a15]@los(SecurityErrorFlag)
.L1213:

; ..\flash\FL.c	  2179      return flag;
; ..\flash\FL.c	  2180  }
	ret
.L651:
	
__FL_GetSecErrFlag_function_end:
	.size	FL_GetSecErrFlag,__FL_GetSecErrFlag_function_end-FL_GetSecErrFlag
.L428:
	; End of function
	
	.sdecl	'.bss.FL.FBL_HeaderType',data,cluster('FBL_HeaderType')
	.sect	'.bss.FL.FBL_HeaderType'
FBL_HeaderType:	.type	object
	.size	FBL_HeaderType,1
	.space	1
	.sdecl	'.bss.FL.FBL_DownloadFlags',data,cluster('FBL_DownloadFlags')
	.sect	'.bss.FL.FBL_DownloadFlags'
FBL_DownloadFlags:	.type	object
	.size	FBL_DownloadFlags,1
	.space	1
	.sdecl	'.bss.FL.FBL_SignVerifFlags',data,cluster('FBL_SignVerifFlags')
	.sect	'.bss.FL.FBL_SignVerifFlags'
FBL_SignVerifFlags:	.type	object
	.size	FBL_SignVerifFlags,1
	.space	1
	.sdecl	'.data.FL.fblFileIndex',data,cluster('fblFileIndex')
	.sect	'.data.FL.fblFileIndex'
	.global	fblFileIndex
fblFileIndex:	.type	object
	.size	fblFileIndex,1
	.space	1
	.sdecl	'.bss.FL.FL_NvmInfo',data,cluster('FL_NvmInfo')
	.sect	'.bss.FL.FL_NvmInfo'
	.global	FL_NvmInfo
	.align	4
FL_NvmInfo:	.type	object
	.size	FL_NvmInfo,48
	.space	48
	.sdecl	'.bss.FL.FL_RemainDataStruct',data,cluster('FL_RemainDataStruct')
	.sect	'.bss.FL.FL_RemainDataStruct'
	.align	4
FL_RemainDataStruct:	.type	object
	.size	FL_RemainDataStruct,8
	.space	8
	.sdecl	'.bss.FL.FldownloadStatus',data,cluster('FldownloadStatus')
	.sect	'.bss.FL.FldownloadStatus'
	.align	4
FldownloadStatus:	.type	object
	.size	FldownloadStatus,1640
	.space	1640
	.sdecl	'.bss.FL.FlProgramData',data,cluster('FlProgramData')
	.sect	'.bss.FL.FlProgramData'
	.align	4
FlProgramData:	.type	object
	.size	FlProgramData,256
	.space	256
	.sdecl	'.bss.FL.FlProgramLength',data,cluster('FlProgramLength')
	.sect	'.bss.FL.FlProgramLength'
	.align	4
FlProgramLength:	.type	object
	.size	FlProgramLength,4
	.space	4
	.sdecl	'.data.FL.flashParamInfo',data,cluster('flashParamInfo')
	.sect	'.data.FL.flashParamInfo'
	.align	4
flashParamInfo:	.type	object
	.size	flashParamInfo,24
	.space	1
	.byte	1,1
	.space	17
	.word	Appl_UpdateTriggerCondition
	.sdecl	'.data.FL.FlIntegrityChkIsHash',data,cluster('FlIntegrityChkIsHash')
	.sect	'.data.FL.FlIntegrityChkIsHash'
FlIntegrityChkIsHash:	.type	object
	.size	FlIntegrityChkIsHash,1
	.byte	1
	.sdecl	'.rodata.FlsCheckData',data,rom,cluster('BL_Information')
	.sect	'.rodata.FlsCheckData'
	.global	BL_Information
	.align	4
BL_Information:	.type	object
	.size	BL_Information,48
	.space	48
	.sdecl	'.data.FL.addblkProgAttempt',data,cluster('addblkProgAttempt')
	.sect	'.data.FL.addblkProgAttempt'
	.global	addblkProgAttempt
addblkProgAttempt:	.type	object
	.size	addblkProgAttempt,1
	.byte	1
	.sdecl	'.data.FL.appblkIntDefault',data,cluster('appblkIntDefault')
	.sect	'.data.FL.appblkIntDefault'
	.global	appblkIntDefault
appblkIntDefault:	.type	object
	.size	appblkIntDefault,1
	.space	1
	.sdecl	'.data.FL.appblkCpbDefault',data,cluster('appblkCpbDefault')
	.sect	'.data.FL.appblkCpbDefault'
	.global	appblkCpbDefault
appblkCpbDefault:	.type	object
	.size	appblkCpbDefault,1
	.space	1
	.sdecl	'.rodata.fbl_cpb_data',data,rom,cluster('boot_cpb_data')
	.sect	'.rodata.fbl_cpb_data'
	.global	boot_cpb_data
	.align	4
boot_cpb_data:	.type	object
	.size	boot_cpb_data,4
	.space	4
	.sdecl	'.data.FL.Fl_crc',data,cluster('Fl_crc')
	.sect	'.data.FL.Fl_crc'
	.global	Fl_crc
Fl_crc:	.type	object
	.size	Fl_crc,1
	.byte	1
	.sdecl	'.data.FL.FLERRStatus',data,cluster('FLERRStatus')
	.sect	'.data.FL.FLERRStatus'
	.global	FLERRStatus
FLERRStatus:	.type	object
	.size	FLERRStatus,1
	.space	1
	.sdecl	'.data.FL.headRemainDataDownloaded',data,cluster('headRemainDataDownloaded')
	.sect	'.data.FL.headRemainDataDownloaded'
	.global	headRemainDataDownloaded
headRemainDataDownloaded:	.type	object
	.size	headRemainDataDownloaded,1
	.space	1
	.sdecl	'.rodata.FL.Compatibility_Code',data,rom,cluster('Compatibility_Code')
	.sect	'.rodata.FL.Compatibility_Code'
	.global	Compatibility_Code
	.align	4
Compatibility_Code:	.type	object
	.size	Compatibility_Code,4
	.byte	86,49,46,49
	.calls	'__INDIRECT__','Appl_UpdateTriggerCondition'
	.calls	'FL_ReadMemory','FlashReadMemory'
	.calls	'FL_CheckSumRoutine','FL_DownloadRemainData'
	.calls	'FL_CheckSumRoutine','FL_InitState'
	.calls	'FL_CheckCPBRoutine','Appl_UpdateTriggerConditionImmediate'
	.calls	'FBL_IntegrityCheck','FL_CheckSumRoutine'
	.calls	'FL_EraseRoutine','FL_DownloadRemainData'
	.calls	'FL_EraseRoutine','Appl_Memcpy'
	.calls	'FL_EraseRoutine','FL_InitState'
	.calls	'FL_DownloadRequestValid','FL_CheckDownloadSegment'
	.calls	'FL_DownloadRequestValid','FL_HandleRemainData'
	.calls	'FL_DownloadRequestValid','FL_InitState'
	.calls	'FL_FlashProgramRegion','Appl_Memcpy'
	.calls	'FL_FlashProgramRegion','Secure_SaveSignHeader'
	.calls	'FL_FlashProgramRegion','FL_InitState'
	.calls	'FL_ExitTransferData','Secure_SignHeaderChk'
	.calls	'FL_ExitTransferData','Secure_Init'
	.calls	'FL_ExitTransferData','FL_InitState'
	.calls	'FL_CheckSumFor37','SecM_CRC_Calculate'
	.calls	'FBL_CheckSumFor37','Secure_CheckSumForHeader'
	.calls	'FBL_CheckSumFor37','Secure_Init'
	.calls	'FBL_CheckSumFor37','FL_CheckSumFor37'
	.calls	'FL_CheckModuleId','FL_Get4Byte'
	.calls	'FL_MainFunction','FL_Erasing'
	.calls	'FL_MainFunction','FL_Programming'
	.calls	'FL_MainFunction','FL_CheckSuming'
	.calls	'FL_MainFunction','FL_CheckCompatibility'
	.calls	'FL_MainFunction','FL_InitState'
	.calls	'FL_UpdateNvm','SecM_ComputeCRC'
	.calls	'FL_UpdateNvm','__INDIRECT__'
	.calls	'FL_Erasing','Appl_UpdateTriggerConditionImmediate'
	.calls	'FL_Erasing','__INDIRECT__'
	.calls	'FL_DownloadRemainData','Appl_Memset'
	.calls	'FL_DownloadRemainData','__INDIRECT__'
	.calls	'FL_HandleRemainData','Appl_Memset'
	.calls	'FL_HandleRemainData','FL_DownloadRemainData'
	.calls	'FL_ProgrammingData','Appl_Memcpy'
	.calls	'FL_ProgrammingData','__INDIRECT__'
	.calls	'FL_Programming','FL_ProgrammingData'
	.calls	'FL_CheckSuming','FBL_GetSignVerifFlag'
	.calls	'FL_CheckSuming','FL_CheckSuming_Hash'
	.calls	'FL_CheckSuming','__INDIRECT__'
	.calls	'FL_CheckSuming','FL_UpdateNvm'
	.calls	'FL_CheckSuming_Hash','Secure_HashVerification'
	.calls	'FL_CheckCompatibility','FlashReadMemory'
	.calls	'FL_CheckCompatibility','FL_UpdateNvm'
	.calls	'UpdateSecurityErrorFlag','EEP_SetSecErrFlag'
	.calls	'FL_InitState','.cocofun_1'
	.calls	'FL_ReadMemory','.cocofun_3'
	.calls	'FL_CheckSumRoutine','.cocofun_1'
	.calls	'FL_EraseRoutine','.cocofun_1'
	.calls	'FL_FlashProgramRegion','.cocofun_1'
	.calls	'FL_FlashProgramRegion','.cocofun_4'
	.calls	'FL_SetExitTransferStep','.cocofun_1'
	.calls	'FL_SetExitTransferStep','.cocofun_4'
	.calls	'FL_SetHeadBlockErased','.cocofun_1'
	.calls	'FL_SetHeadBlockErased','.cocofun_4'
	.calls	'FL_ExitTransferData','.cocofun_1'
	.calls	'FL_ServiceFinished','.cocofun_1'
	.calls	'GetS37JobStatusResult','.cocofun_1'
	.calls	'GetS37JobStatusResult','.cocofun_4'
	.calls	'FL_CheckSumFor37','.cocofun_3'
	.calls	'FL_CheckModuleId','.cocofun_3'
	.calls	'FL_MainFunction','.cocofun_1'
	.calls	'FL_UpdateNvm','.cocofun_2'
	.calls	'FL_CheckDownloadSegment','.cocofun_1'
	.calls	'FL_Programming','.cocofun_1'
	.calls	'FL_CheckSuming','.cocofun_2'
	.calls	'FL_CheckSuming_Hash','.cocofun_1'
	.calls	'FL_CheckCompatibility','.cocofun_2'
	.calls	'FL_SetRequestStep','.cocofun_1'
	.calls	'FL_SetChecksumStep','.cocofun_1'
	.calls	'FL_SetChecksumStep','.cocofun_4'
	.calls	'FL_KeepNvmInforNoUsed','',0
	.calls	'FL_InitState','',0
	.calls	'.cocofun_1','',0
	.calls	'FL_ReadConstDIDData','',0
	.calls	'FL_ReadMemory','',0
	.calls	'.cocofun_3','',0
	.calls	'FL_CheckProgPreCondition','',0
	.calls	'FL_CheckSumRoutine','',0
	.calls	'FL_CheckCPBRoutine','',0
	.calls	'FBL_IntegrityCheck','',0
	.calls	'FL_EraseRoutine','',0
	.calls	'FL_DownloadRequestValid','',0
	.calls	'FL_FlashProgramRegion','',0
	.calls	'.cocofun_4','',0
	.calls	'FL_SetExitTransferStep','',0
	.calls	'FL_SetHeadBlockErased','',0
	.calls	'FL_ExitTransferData','',0
	.calls	'FL_ServiceFinished','',0
	.calls	'SetS37JobStatusBusy','',0
	.calls	'GetS37JobStatusResult','',0
	.calls	'FL_CheckSumFor37','',816
	.calls	'FBL_CheckSumFor37','',0
	.calls	'FL_Get4Byte','',0
	.calls	'FL_CheckModuleId','',0
	.calls	'FL_MainFunction','',0
	.calls	'FL_UpdateNvm','',16
	.calls	'.cocofun_2','',0
	.calls	'FL_Erasing','',0
	.calls	'FL_CheckDownloadSegment','',0
	.calls	'FL_DownloadRemainData','',0
	.calls	'FL_HandleRemainData','',0
	.calls	'FL_ProgrammingData','',16
	.calls	'FL_Programming','',0
	.calls	'FBL_GetSignVerifFlag','',0
	.calls	'FL_CheckSuming','',16
	.calls	'FL_CheckSuming_Hash','',0
	.calls	'FL_CheckProgramIntegrity','',0
	.calls	'FL_CheckCompatibility','',8
	.calls	'FL_CheckProgramDependencies','',0
	.calls	'FL_CheckProgramCounter','',0
	.calls	'FL_CheckSWVerification','',0
	.calls	'FL_SignVerifFlags','',0
	.calls	'FL_SetRequestStep','',0
	.calls	'FL_SetChecksumStep','',0
	.calls	'UpdateSecurityErrorFlag','',0
	.extern	FL_BlkInfo
	.extern	CurrentProgrammingBlock
	.extern	BLFlash_InfoPtr
	.extern	FlashReadMemory
	.extern	Appl_UpdateTriggerCondition
	.extern	Appl_UpdateTriggerConditionImmediate
	.extern	Appl_Memcpy
	.extern	Appl_Memset
	.extern	SecM_ComputeCRC
	.extern	SecM_CRC_Calculate
	.extern	SecureSignHeader
	.extern	Secure_Init
	.extern	Secure_SaveSignHeader
	.extern	Secure_SignHeaderChk
	.extern	Secure_CheckSumForHeader
	.extern	Secure_HashVerification
	.extern	EEP_SetSecErrFlag
	.extern	SecurityErrorFlag
	.extern	lastcounter
	.extern	__INDIRECT__
	.calls	'FL_GetSecErrFlag','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L286:
	.word	10414
	.half	3
	.word	.L287
	.byte	4
.L285:
	.byte	1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L288
.L561:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L569:
	.byte	2
	.byte	'unsigned char',0,1,8
.L565:
	.byte	3
	.word	189
.L581:
	.byte	4
	.word	189
.L589:
	.byte	4
	.word	189
.L593:
	.byte	4
	.word	168
.L595:
	.byte	4
	.word	168
.L599:
	.byte	4
	.word	168
	.byte	4
	.word	189
.L601:
	.byte	3
	.word	236
.L603:
	.byte	4
	.word	168
.L614:
	.byte	3
	.word	189
.L625:
	.byte	2
	.byte	'unsigned short int',0,2,7
.L619:
	.byte	3
	.word	256
.L622:
	.byte	5,1,101,9,12
.L629:
	.byte	5,2,119,9,164,6,6
	.byte	'nrOfSegments',0,2
	.word	256
	.byte	2,35,0,6
	.byte	'blockChecked',0,1
	.word	189
	.byte	2,35,2,5,2,108,9,8,6
	.byte	'address',0,4
	.word	168
	.byte	2,35,0,6
	.byte	'length',0,4
	.word	168
	.byte	2,35,4,0,7,160,6
	.word	338
	.byte	8,99,0,6
	.byte	'segmentInfo',0,160,6
	.word	377
	.byte	2,35,4,0,3
	.word	288
	.byte	6
	.byte	'segmentList',0,4
	.word	410
	.byte	2,35,0,6
	.byte	'verificationData',0,4
	.word	241
	.byte	2,35,4,6
	.byte	'crcTotle',0,2
	.word	256
	.byte	2,35,8,0
.L632:
	.byte	3
	.word	256
.L644:
	.byte	2
	.byte	'int',0,4,5,9
	.byte	'FlashReadMemory',0,3,139,1,13,1,1,1,1,10
	.byte	'DataBuf',0,3,139,1,36
	.word	206
	.byte	10
	.byte	'Addr',0,3,139,1,51
	.word	168
	.byte	10
	.byte	'Length',0,3,139,1,64
	.word	168
	.byte	0,11
	.byte	'Appl_UpdateTriggerCondition',0,4,49,13,1,1,1,1,9
	.byte	'Appl_UpdateTriggerConditionImmediate',0,4,50,13,1,1,1,1,10
	.byte	'count',0,4,50,56
	.word	189
	.byte	0,9
	.byte	'Appl_Memcpy',0,4,51,13,1,1,1,1,10
	.byte	'dest',0,4,51,33
	.word	206
	.byte	10
	.byte	'source',0,4,52,18
	.word	241
	.byte	10
	.byte	'length',0,4,53,12
	.word	168
	.byte	0,9
	.byte	'Appl_Memset',0,4,55,13,1,1,1,1,10
	.byte	'dest',0,4,55,33
	.word	206
	.byte	4
	.word	189
	.byte	10
	.byte	'source',0,4,56,17
	.word	759
	.byte	10
	.byte	'length',0,4,57,12
	.word	168
	.byte	0,12
	.byte	'SecM_ComputeCRC',0,1,126,24
	.word	189
	.byte	1,1,1,1
.L681:
	.byte	5,1,84,9,12,6
	.byte	'currentCRC',0,2
	.word	256
	.byte	2,35,0,6
	.byte	'crcState',0,1
	.word	189
	.byte	2,35,2,6
	.byte	'crcSourceBuffer',0,4
	.word	241
	.byte	2,35,4,6
	.byte	'crcByteCount',0,2
	.word	256
	.byte	2,35,8,0,3
	.word	823
	.byte	10
	.byte	'crcParam',0,1,126,59
	.word	914
	.byte	0,12
	.byte	'SecM_CRC_Calculate',0,1,129,1,24
	.word	189
	.byte	1,1,1,1,3
	.word	283
	.byte	10
	.byte	'verifyParam',0,1,129,1,65
	.word	969
	.byte	0,11
	.byte	'Secure_Init',0,5,44,6,1,1,1,1,12
	.byte	'Secure_SaveSignHeader',0,5,47,16
	.word	189
	.byte	1,1,1,1,10
	.byte	'data',0,5,47,45
	.word	206
	.byte	10
	.byte	'length',0,5,47,58
	.word	256
	.byte	10
	.byte	'headerType',0,5,47,72
	.word	189
	.byte	0,12
	.byte	'Secure_SignHeaderChk',0,5,49,16
	.word	189
	.byte	1,1,1,1,10
	.byte	'fileIdx',0,5,49,44
	.word	206
	.byte	0,12
	.byte	'Secure_CheckSumForHeader',0,5,50,16
	.word	189
	.byte	1,1,1,1,10
	.byte	'crcValPtr',0,5,50,50
	.word	481
	.byte	0,12
	.byte	'Secure_HashVerification',0,5,51,16
	.word	189
	.byte	1,1,1,1,10
	.byte	'address',0,5,51,47
	.word	168
	.byte	10
	.byte	'length',0,5,51,63
	.word	168
	.byte	10
	.byte	'index',0,5,51,77
	.word	189
	.byte	0,12
	.byte	'EEP_SetSecErrFlag',0,6,63,15
	.word	256
	.byte	1,1,1,1,10
	.byte	'data',0,6,63,40
	.word	206
	.byte	0
.L656:
	.byte	3
	.word	338
.L685:
	.byte	7,4
	.word	189
	.byte	8,3,0,13
	.byte	'__INDIRECT__',0,7,1,1,1,1,1,14
	.byte	'void',0,3
	.word	1364
	.byte	15
	.byte	'__prof_adm',0,7,1,1
	.word	1370
	.byte	16,1,3
	.word	1394
	.byte	15
	.byte	'__codeptr',0,7,1,1
	.word	1396
	.byte	15
	.byte	'uint8',0,8,90,29
	.word	189
	.byte	2
	.byte	'short int',0,2,5,15
	.byte	'sint16',0,8,91,29
	.word	1433
	.byte	15
	.byte	'uint16',0,8,92,29
	.word	256
	.byte	15
	.byte	'uint32',0,8,94,29
	.word	168
	.byte	15
	.byte	'boolean',0,8,105,29
	.word	189
	.byte	2
	.byte	'unsigned long long int',0,8,7,15
	.byte	'uint64',0,8,130,1,30
	.word	1507
	.byte	15
	.byte	'Std_ReturnType',0,9,113,15
	.word	189
	.byte	17,10,52,9,164,1,7,164,1
	.word	189
	.byte	8,163,1,0,6
	.byte	'datas',0,164,1
	.word	1578
	.byte	2,35,0,5,10,55,5,164,1,6
	.byte	'certFmt',0,1
	.word	189
	.byte	2,35,0,7,8
	.word	189
	.byte	8,7,0,6
	.byte	'pModNum',0,8
	.word	1628
	.byte	2,35,1,7,16
	.word	189
	.byte	8,15,0,6
	.byte	'customPars',0,16
	.word	1654
	.byte	2,35,9,7,3
	.word	189
	.byte	8,2,0,6
	.byte	'certFailDate',0,3
	.word	1683
	.byte	2,35,25,6
	.byte	'certSequenceNum',0,4
	.word	1335
	.byte	2,35,28,6
	.byte	'signAlgoFlg',0,1
	.word	189
	.byte	2,35,32,6
	.byte	'pubKeyCurPar',0,1
	.word	189
	.byte	2,35,33,6
	.byte	'hashAlgoFlg',0,1
	.word	189
	.byte	2,35,34,6
	.byte	'pubKeyIdx',0,1
	.word	189
	.byte	2,35,35,7,64
	.word	189
	.byte	8,63,0,6
	.byte	'certPubKey',0,64
	.word	1822
	.byte	2,35,36,6
	.byte	'certSigner',0,64
	.word	1822
	.byte	2,35,100,0,6
	.byte	'parameters',0,164,1
	.word	1605
	.byte	2,35,0,0,15
	.byte	'Secure_SignerInfoType',0,10,68,3
	.word	1572
	.byte	17,10,82,9,152,4,7,150,4
	.word	189
	.byte	8,149,4,0,6
	.byte	'datas',0,150,4
	.word	1930
	.byte	2,35,0,5,10,85,5,152,4,7,2
	.word	189
	.byte	8,1,0,6
	.byte	'moduleId',0,2
	.word	1963
	.byte	2,35,0,6
	.byte	'NBID',0,2
	.word	1963
	.byte	2,35,2,7,10
	.word	189
	.byte	8,9,0,6
	.byte	'locInfo',0,10
	.word	2004
	.byte	2,35,4,6
	.byte	'signerInfoNtl',0,164,1
	.word	1578
	.byte	2,35,14,7,32
	.word	189
	.byte	8,31,0,6
	.byte	'msgDigestNtl',0,32
	.word	2054
	.byte	3,35,178,1,6
	.byte	'signNtl',0,64
	.word	1822
	.byte	3,35,210,1,6
	.byte	'signerInfoIntl',0,164,1
	.word	1578
	.byte	3,35,146,2,6
	.byte	'msgDigestIntl',0,32
	.word	2054
	.byte	3,35,182,3,6
	.byte	'signIntl',0,64
	.word	1822
	.byte	3,35,214,3,0,6
	.byte	'app_parameters',0,152,4
	.word	1957
	.byte	2,35,0,0,15
	.byte	'Secure_SignHeaderType',0,10,110,3
	.word	1924
	.byte	15
	.byte	'_iob_flag_t',0,11,75,25
	.word	256
	.byte	15
	.byte	'FL_ResultType',0,2,69,15
	.word	189
	.byte	18,2,72,9,1,19
	.byte	'INTERNAL_FLS',0,0,19
	.byte	'EXTERNAL_FLS',0,1,0,15
	.byte	'FL_FlashType',0,2,76,2
	.word	2272
	.byte	18,2,78,9,1,19
	.byte	'NO_CRC',0,0,19
	.byte	'LAST_ADDR',0,1,19
	.byte	'HEAD_ADDR',0,2,0,15
	.byte	'FL_CrcAddrType',0,2,83,2
	.word	2329
	.byte	5,2,89,9,24,4
	.word	168
	.byte	6
	.byte	'address',0,4
	.word	2396
	.byte	2,35,0,4
	.word	168
	.byte	6
	.byte	'length',0,4
	.word	2418
	.byte	2,35,4,4
	.word	2272
	.byte	6
	.byte	'flashtype',0,1
	.word	2439
	.byte	2,35,8,4
	.word	2329
	.byte	6
	.byte	'crcaddrtype',0,1
	.word	2463
	.byte	2,35,9,4
	.word	168
	.byte	6
	.byte	'crcaddress',0,4
	.word	2489
	.byte	2,35,10,4
	.word	189
	.byte	6
	.byte	'isvital',0,1
	.word	2514
	.byte	2,35,14,4
	.word	168
	.byte	6
	.byte	'maxProgAttempt',0,4
	.word	2536
	.byte	2,35,16,4
	.word	189
	.byte	6
	.byte	'moduleid',0,1
	.word	2565
	.byte	2,35,20,0,15
	.byte	'FL_BlockDescriptorType',0,2,105,3
	.word	2391
	.byte	15
	.byte	'FL_SegmentInfoType',0,2,116,3
	.word	338
	.byte	15
	.byte	'FL_SegmentListType',0,2,126,3
	.word	288
	.byte	5,2,129,1,9,20,6
	.byte	'blkValid',0,1
	.word	189
	.byte	2,35,0,6
	.byte	'blkProgAttempt',0,2
	.word	256
	.byte	2,35,2,6
	.byte	'blkChecksum',0,4
	.word	168
	.byte	2,35,4,7,9
	.word	189
	.byte	8,8,0,6
	.byte	'fingerPrint',0,9
	.word	2743
	.byte	2,35,8,0,15
	.byte	'FL_blockInfoType',0,2,139,1,3
	.word	2674
.L697:
	.byte	5,2,142,1,9,48,7,40
	.word	2674
	.byte	8,1,0,6
	.byte	'blockInfo',0,40
	.word	2806
	.byte	2,35,0,6
	.byte	'isAppCalCpb',0,1
	.word	189
	.byte	2,35,40,6
	.byte	'isAppFblCpb',0,1
	.word	189
	.byte	2,35,41,6
	.byte	'infoChecksum',0,4
	.word	168
	.byte	2,35,42,0,15
	.byte	'FL_NvmInfoType',0,2,150,1,3
	.word	2800
	.byte	7,48
	.word	2391
	.byte	8,1,0,4
	.word	2923
	.byte	20
	.byte	'FL_BlkInfo',0,2,161,1,37
	.word	2932
	.byte	1,1,20
	.byte	'CurrentProgrammingBlock',0,2,162,1,14
	.word	189
	.byte	1,1,15
	.byte	'tMajorVersion',0,3,35,15
	.word	189
	.byte	15
	.byte	'tMinorVersion',0,3,38,15
	.word	189
	.byte	15
	.byte	'tBugfixVersion',0,3,41,15
	.word	189
	.byte	15
	.byte	'tFlashResult',0,3,44,15
	.word	189
	.byte	15
	.byte	'tFlashAddress',0,3,47,16
	.word	168
	.byte	15
	.byte	'tFlashLength',0,3,50,16
	.word	168
	.byte	15
	.byte	'tFlashData',0,3,53,15
	.word	189
	.byte	21,1,1,3
	.word	3144
	.byte	15
	.byte	'tWDTriggerFct',0,3,56,16
	.word	3147
.L702:
	.byte	5,3,59,9,24,6
	.byte	'patchLevel',0,1
	.word	189
	.byte	2,35,0,6
	.byte	'minorNumber',0,1
	.word	189
	.byte	2,35,1,6
	.byte	'majorNumber',0,1
	.word	189
	.byte	2,35,2,6
	.byte	'reserved1',0,1
	.word	189
	.byte	2,35,3,6
	.byte	'errorCode',0,1
	.word	189
	.byte	2,35,4,6
	.byte	'reserved2',0,2
	.word	256
	.byte	2,35,6,6
	.byte	'address',0,4
	.word	168
	.byte	2,35,8,6
	.byte	'length',0,4
	.word	168
	.byte	2,35,12,4
	.word	189
	.byte	3
	.word	3331
	.byte	6
	.byte	'data',0,4
	.word	3336
	.byte	2,35,16,6
	.byte	'wdTriggerFct',0,4
	.word	3152
	.byte	2,35,20,0,15
	.byte	'tFlashParam',0,3,92,3
	.word	3174
	.byte	3
	.word	3144
	.byte	22,1,1,3
	.word	3174
	.byte	23
	.word	3406
	.byte	0,3
	.word	3403
	.byte	15
	.byte	'tFlashFct',0,3,95,16
	.word	3417
	.byte	5,3,98,9,20,4
	.word	189
	.byte	6
	.byte	'mcuType',0,1
	.word	3445
	.byte	2,35,0,4
	.word	189
	.byte	6
	.byte	'maskType',0,1
	.word	3467
	.byte	2,35,1,4
	.word	189
	.byte	6
	.byte	'reserve',0,1
	.word	3490
	.byte	2,35,2,4
	.word	189
	.byte	6
	.byte	'interface',0,1
	.word	3512
	.byte	2,35,3,4
	.word	3422
	.byte	6
	.byte	'flashInitFct',0,4
	.word	3536
	.byte	2,35,4,4
	.word	3422
	.byte	6
	.byte	'flashDeInitFct',0,4
	.word	3563
	.byte	2,35,8,4
	.word	3422
	.byte	6
	.byte	'flashEraseFct',0,4
	.word	3592
	.byte	2,35,12,4
	.word	3422
	.byte	6
	.byte	'flashWriteFct',0,4
	.word	3620
	.byte	2,35,16,0,15
	.byte	'tFlash_InfoType',0,3,124,3
	.word	3440
	.byte	3
	.word	3403
	.byte	4
	.word	3440
	.byte	3
	.word	3678
	.byte	20
	.byte	'BLFlash_InfoPtr',0,3,129,1,31
	.word	3683
	.byte	1,1,24
	.byte	'_Ifx_STM_ACCEN0_Bits',0,12,45,16,4,25
	.byte	'EN0',0,1
	.word	189
	.byte	1,7,2,35,0,25
	.byte	'EN1',0,1
	.word	189
	.byte	1,6,2,35,0,25
	.byte	'EN2',0,1
	.word	189
	.byte	1,5,2,35,0,25
	.byte	'EN3',0,1
	.word	189
	.byte	1,4,2,35,0,25
	.byte	'EN4',0,1
	.word	189
	.byte	1,3,2,35,0,25
	.byte	'EN5',0,1
	.word	189
	.byte	1,2,2,35,0,25
	.byte	'EN6',0,1
	.word	189
	.byte	1,1,2,35,0,25
	.byte	'EN7',0,1
	.word	189
	.byte	1,0,2,35,0,25
	.byte	'EN8',0,1
	.word	189
	.byte	1,7,2,35,1,25
	.byte	'EN9',0,1
	.word	189
	.byte	1,6,2,35,1,25
	.byte	'EN10',0,1
	.word	189
	.byte	1,5,2,35,1,25
	.byte	'EN11',0,1
	.word	189
	.byte	1,4,2,35,1,25
	.byte	'EN12',0,1
	.word	189
	.byte	1,3,2,35,1,25
	.byte	'EN13',0,1
	.word	189
	.byte	1,2,2,35,1,25
	.byte	'EN14',0,1
	.word	189
	.byte	1,1,2,35,1,25
	.byte	'EN15',0,1
	.word	189
	.byte	1,0,2,35,1,25
	.byte	'EN16',0,1
	.word	189
	.byte	1,7,2,35,2,25
	.byte	'EN17',0,1
	.word	189
	.byte	1,6,2,35,2,25
	.byte	'EN18',0,1
	.word	189
	.byte	1,5,2,35,2,25
	.byte	'EN19',0,1
	.word	189
	.byte	1,4,2,35,2,25
	.byte	'EN20',0,1
	.word	189
	.byte	1,3,2,35,2,25
	.byte	'EN21',0,1
	.word	189
	.byte	1,2,2,35,2,25
	.byte	'EN22',0,1
	.word	189
	.byte	1,1,2,35,2,25
	.byte	'EN23',0,1
	.word	189
	.byte	1,0,2,35,2,25
	.byte	'EN24',0,1
	.word	189
	.byte	1,7,2,35,3,25
	.byte	'EN25',0,1
	.word	189
	.byte	1,6,2,35,3,25
	.byte	'EN26',0,1
	.word	189
	.byte	1,5,2,35,3,25
	.byte	'EN27',0,1
	.word	189
	.byte	1,4,2,35,3,25
	.byte	'EN28',0,1
	.word	189
	.byte	1,3,2,35,3,25
	.byte	'EN29',0,1
	.word	189
	.byte	1,2,2,35,3,25
	.byte	'EN30',0,1
	.word	189
	.byte	1,1,2,35,3,25
	.byte	'EN31',0,1
	.word	189
	.byte	1,0,2,35,3,0,15
	.byte	'Ifx_STM_ACCEN0_Bits',0,12,79,3
	.word	3715
	.byte	24
	.byte	'_Ifx_STM_ACCEN1_Bits',0,12,82,16,4,2
	.byte	'unsigned int',0,4,7,25
	.byte	'reserved_0',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_ACCEN1_Bits',0,12,85,3
	.word	4272
	.byte	24
	.byte	'_Ifx_STM_CAP_Bits',0,12,88,16,4,25
	.byte	'STMCAP63_32',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_CAP_Bits',0,12,91,3
	.word	4365
	.byte	24
	.byte	'_Ifx_STM_CAPSV_Bits',0,12,94,16,4,25
	.byte	'STMCAP63_32',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_CAPSV_Bits',0,12,97,3
	.word	4437
	.byte	24
	.byte	'_Ifx_STM_CLC_Bits',0,12,100,16,4,25
	.byte	'DISR',0,1
	.word	189
	.byte	1,7,2,35,0,25
	.byte	'DISS',0,1
	.word	189
	.byte	1,6,2,35,0,25
	.byte	'reserved_2',0,1
	.word	189
	.byte	1,5,2,35,0,25
	.byte	'EDIS',0,1
	.word	189
	.byte	1,4,2,35,0,25
	.byte	'reserved_4',0,4
	.word	4298
	.byte	28,0,2,35,2,0,15
	.byte	'Ifx_STM_CLC_Bits',0,12,107,3
	.word	4513
	.byte	24
	.byte	'_Ifx_STM_CMCON_Bits',0,12,110,16,4,25
	.byte	'MSIZE0',0,1
	.word	189
	.byte	5,3,2,35,0,25
	.byte	'reserved_5',0,1
	.word	189
	.byte	3,0,2,35,0,25
	.byte	'MSTART0',0,1
	.word	189
	.byte	5,3,2,35,1,25
	.byte	'reserved_13',0,1
	.word	189
	.byte	3,0,2,35,1,25
	.byte	'MSIZE1',0,1
	.word	189
	.byte	5,3,2,35,2,25
	.byte	'reserved_21',0,1
	.word	189
	.byte	3,0,2,35,2,25
	.byte	'MSTART1',0,1
	.word	189
	.byte	5,3,2,35,3,25
	.byte	'reserved_29',0,1
	.word	189
	.byte	3,0,2,35,3,0,15
	.byte	'Ifx_STM_CMCON_Bits',0,12,120,3
	.word	4654
	.byte	24
	.byte	'_Ifx_STM_CMP_Bits',0,12,123,16,4,25
	.byte	'CMPVAL',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_CMP_Bits',0,12,126,3
	.word	4872
	.byte	24
	.byte	'_Ifx_STM_ICR_Bits',0,12,129,1,16,4,25
	.byte	'CMP0EN',0,1
	.word	189
	.byte	1,7,2,35,0,25
	.byte	'CMP0IR',0,1
	.word	189
	.byte	1,6,2,35,0,25
	.byte	'CMP0OS',0,1
	.word	189
	.byte	1,5,2,35,0,25
	.byte	'reserved_3',0,1
	.word	189
	.byte	1,4,2,35,0,25
	.byte	'CMP1EN',0,1
	.word	189
	.byte	1,3,2,35,0,25
	.byte	'CMP1IR',0,1
	.word	189
	.byte	1,2,2,35,0,25
	.byte	'CMP1OS',0,1
	.word	189
	.byte	1,1,2,35,0,25
	.byte	'reserved_7',0,4
	.word	4298
	.byte	25,0,2,35,2,0,15
	.byte	'Ifx_STM_ICR_Bits',0,12,139,1,3
	.word	4939
	.byte	24
	.byte	'_Ifx_STM_ID_Bits',0,12,142,1,16,4,25
	.byte	'MODREV',0,1
	.word	189
	.byte	8,0,2,35,0,25
	.byte	'MODTYPE',0,1
	.word	189
	.byte	8,0,2,35,1,25
	.byte	'MODNUMBER',0,2
	.word	256
	.byte	16,0,2,35,2,0,15
	.byte	'Ifx_STM_ID_Bits',0,12,147,1,3
	.word	5142
	.byte	24
	.byte	'_Ifx_STM_ISCR_Bits',0,12,150,1,16,4,25
	.byte	'CMP0IRR',0,1
	.word	189
	.byte	1,7,2,35,0,25
	.byte	'CMP0IRS',0,1
	.word	189
	.byte	1,6,2,35,0,25
	.byte	'CMP1IRR',0,1
	.word	189
	.byte	1,5,2,35,0,25
	.byte	'CMP1IRS',0,1
	.word	189
	.byte	1,4,2,35,0,25
	.byte	'reserved_4',0,4
	.word	4298
	.byte	28,0,2,35,2,0,15
	.byte	'Ifx_STM_ISCR_Bits',0,12,157,1,3
	.word	5249
	.byte	24
	.byte	'_Ifx_STM_KRST0_Bits',0,12,160,1,16,4,25
	.byte	'RST',0,1
	.word	189
	.byte	1,7,2,35,0,25
	.byte	'RSTSTAT',0,1
	.word	189
	.byte	1,6,2,35,0,25
	.byte	'reserved_2',0,4
	.word	4298
	.byte	30,0,2,35,2,0,15
	.byte	'Ifx_STM_KRST0_Bits',0,12,165,1,3
	.word	5400
	.byte	24
	.byte	'_Ifx_STM_KRST1_Bits',0,12,168,1,16,4,25
	.byte	'RST',0,1
	.word	189
	.byte	1,7,2,35,0,25
	.byte	'reserved_1',0,4
	.word	4298
	.byte	31,0,2,35,2,0,15
	.byte	'Ifx_STM_KRST1_Bits',0,12,172,1,3
	.word	5511
	.byte	24
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,12,175,1,16,4,25
	.byte	'CLR',0,1
	.word	189
	.byte	1,7,2,35,0,25
	.byte	'reserved_1',0,4
	.word	4298
	.byte	31,0,2,35,2,0,15
	.byte	'Ifx_STM_KRSTCLR_Bits',0,12,179,1,3
	.word	5603
	.byte	24
	.byte	'_Ifx_STM_OCS_Bits',0,12,182,1,16,4,25
	.byte	'reserved_0',0,4
	.word	4298
	.byte	24,8,2,35,2,25
	.byte	'SUS',0,1
	.word	189
	.byte	4,4,2,35,3,25
	.byte	'SUS_P',0,1
	.word	189
	.byte	1,3,2,35,3,25
	.byte	'SUSSTA',0,1
	.word	189
	.byte	1,2,2,35,3,25
	.byte	'reserved_30',0,1
	.word	189
	.byte	2,0,2,35,3,0,15
	.byte	'Ifx_STM_OCS_Bits',0,12,189,1,3
	.word	5699
	.byte	24
	.byte	'_Ifx_STM_TIM0_Bits',0,12,192,1,16,4,25
	.byte	'STM31_0',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_TIM0_Bits',0,12,195,1,3
	.word	5845
	.byte	24
	.byte	'_Ifx_STM_TIM0SV_Bits',0,12,198,1,16,4,25
	.byte	'STM31_0',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_TIM0SV_Bits',0,12,201,1,3
	.word	5917
	.byte	24
	.byte	'_Ifx_STM_TIM1_Bits',0,12,204,1,16,4,25
	.byte	'STM35_4',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_TIM1_Bits',0,12,207,1,3
	.word	5993
	.byte	24
	.byte	'_Ifx_STM_TIM2_Bits',0,12,210,1,16,4,25
	.byte	'STM39_8',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_TIM2_Bits',0,12,213,1,3
	.word	6065
	.byte	24
	.byte	'_Ifx_STM_TIM3_Bits',0,12,216,1,16,4,25
	.byte	'STM43_12',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_TIM3_Bits',0,12,219,1,3
	.word	6137
	.byte	24
	.byte	'_Ifx_STM_TIM4_Bits',0,12,222,1,16,4,25
	.byte	'STM47_16',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_TIM4_Bits',0,12,225,1,3
	.word	6210
	.byte	24
	.byte	'_Ifx_STM_TIM5_Bits',0,12,228,1,16,4,25
	.byte	'STM51_20',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_TIM5_Bits',0,12,231,1,3
	.word	6283
	.byte	24
	.byte	'_Ifx_STM_TIM6_Bits',0,12,234,1,16,4,25
	.byte	'STM63_32',0,4
	.word	4298
	.byte	32,0,2,35,2,0,15
	.byte	'Ifx_STM_TIM6_Bits',0,12,237,1,3
	.word	6356
	.byte	17,12,245,1,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	3715
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_ACCEN0',0,12,250,1,3
	.word	6429
	.byte	17,12,253,1,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	4272
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_ACCEN1',0,12,130,2,3
	.word	6493
	.byte	17,12,133,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	4365
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_CAP',0,12,138,2,3
	.word	6557
	.byte	17,12,141,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	4437
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_CAPSV',0,12,146,2,3
	.word	6618
	.byte	17,12,149,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	4513
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_CLC',0,12,154,2,3
	.word	6681
	.byte	17,12,157,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	4654
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_CMCON',0,12,162,2,3
	.word	6742
	.byte	17,12,165,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	4872
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_CMP',0,12,170,2,3
	.word	6805
	.byte	17,12,173,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	4939
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_ICR',0,12,178,2,3
	.word	6866
	.byte	17,12,181,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5142
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_ID',0,12,186,2,3
	.word	6927
	.byte	17,12,189,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5249
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_ISCR',0,12,194,2,3
	.word	6987
	.byte	17,12,197,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5400
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_KRST0',0,12,202,2,3
	.word	7049
	.byte	17,12,205,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5511
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_KRST1',0,12,210,2,3
	.word	7112
	.byte	17,12,213,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5603
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_KRSTCLR',0,12,218,2,3
	.word	7175
	.byte	17,12,221,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5699
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_OCS',0,12,226,2,3
	.word	7240
	.byte	17,12,229,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5845
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_TIM0',0,12,234,2,3
	.word	7301
	.byte	17,12,237,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5917
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_TIM0SV',0,12,242,2,3
	.word	7363
	.byte	17,12,245,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	5993
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_TIM1',0,12,250,2,3
	.word	7427
	.byte	17,12,253,2,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	6065
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_TIM2',0,12,130,3,3
	.word	7489
	.byte	17,12,133,3,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	6137
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_TIM3',0,12,138,3,3
	.word	7551
	.byte	17,12,141,3,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	6210
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_TIM4',0,12,146,3,3
	.word	7613
	.byte	17,12,149,3,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	6283
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_TIM5',0,12,154,3,3
	.word	7675
	.byte	17,12,157,3,9,4,6
	.byte	'U',0,4
	.word	4298
	.byte	2,35,0,6
	.byte	'I',0,4
	.word	486
	.byte	2,35,0,6
	.byte	'B',0,4
	.word	6356
	.byte	2,35,0,0,15
	.byte	'Ifx_STM_TIM6',0,12,162,3,3
	.word	7737
	.byte	15
	.byte	'SecM_StatusType',0,1,65,15
	.word	189
	.byte	15
	.byte	'SecM_WordType',0,1,68,16
	.word	168
	.byte	15
	.byte	'SecM_CRCType',0,1,80,16
	.word	256
	.byte	15
	.byte	'SecM_CRCParamType',0,1,98,3
	.word	823
	.byte	15
	.byte	'SecM_VerifyParamType',0,1,111,3
	.word	283
	.byte	20
	.byte	'SecureSignHeader',0,5,40,30
	.word	1924
	.byte	1,1,26
	.word	256
	.byte	1,1,23
	.word	189
	.byte	3
	.word	189
	.byte	23
	.word	7960
	.byte	0,3
	.word	7948
	.byte	15
	.byte	'Eep_WriteFct',0,6,42,18
	.word	7971
	.byte	15
	.byte	'Eep_ReadFct',0,6,43,18
	.word	7971
	.byte	15
	.byte	'PduLengthType',0,13,76,22
	.word	256
	.byte	15
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,14,112,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,14,115,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,14,118,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,14,121,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,14,124,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,14,136,1,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,14,139,1,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,14,148,1,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,14,151,1,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,14,141,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,14,144,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,14,147,3,16
	.word	256
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,14,150,3,16
	.word	256
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,14,153,3,16
	.word	256
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,14,156,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,14,159,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,14,162,3,16
	.word	256
	.byte	15
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,14,165,3,16
	.word	256
	.byte	15
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,14,180,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,14,183,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,14,186,3,16
	.word	168
	.byte	15
	.byte	'IdtAppCom_MainPwrFltRsn',0,14,192,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGMDiags',0,14,195,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGMFltRsn',0,14,198,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGMSts',0,14,201,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGMSwSts',0,14,207,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,14,210,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,14,213,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,14,216,3,16
	.word	256
	.byte	15
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,14,219,3,16
	.word	256
	.byte	15
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,14,225,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,14,228,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_RednPwrFltRsn',0,14,231,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,14,234,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_SHWAIndSts',0,14,237,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_SHWASysFltSts',0,14,240,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_SHWASysMsg',0,14,243,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,14,246,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_SHWASysSts',0,14,249,3,15
	.word	189
	.byte	15
	.byte	'IdtAppCom_SHWASysTakeOver',0,14,252,3,15
	.word	189
	.byte	15
	.byte	'Rte_BitType',0,14,230,7,22
	.word	4298
	.byte	18,7,57,9,1,19
	.byte	'FL_JOB_IDLE',0,0,19
	.byte	'FL_JOB_ERASING',0,1,19
	.byte	'FL_JOB_PROGRAMMING',0,2,19
	.byte	'FL_JOB_CHECKING',0,3,19
	.byte	'FL_JOB_S37CHECKING',0,4,19
	.byte	'FL_JOB_COMPATIBLE',0,5,0,15
	.byte	'FL_ActiveJobType',0,7,69,3
	.word	9617
	.byte	18,7,72,9,1,19
	.byte	'FL_REQUEST_STEP',0,0,19
	.byte	'FL_HEADER_TRASNFER_STEP',0,1,19
	.byte	'FL_HEADER_EXIT_TRANSFER_STEP',0,2,19
	.byte	'FL_TRANSFER_STEP',0,3,19
	.byte	'FL_EXIT_TRANSFER_STEP',0,4,19
	.byte	'FL_CHECKSUM_STEP',0,5,0,15
	.byte	'FL_DownloadStepType',0,7,86,3
	.word	9759
.L700:
	.byte	5,7,89,9,232,12,6
	.byte	'fingerPrintWritten',0,1
	.word	189
	.byte	2,35,0,6
	.byte	'fingerPrint',0,9
	.word	2743
	.byte	2,35,1,6
	.byte	'flDrvDownloaded',0,1
	.word	189
	.byte	2,35,10,6
	.byte	'errorCode',0,1
	.word	189
	.byte	2,35,11,6
	.byte	'blockErased',0,1
	.word	189
	.byte	2,35,12,6
	.byte	'blockIndex',0,1
	.word	189
	.byte	2,35,13,6
	.byte	'startAddr',0,4
	.word	168
	.byte	2,35,14,6
	.byte	'length',0,4
	.word	168
	.byte	2,35,18,6
	.byte	'dataBuff',0,4
	.word	241
	.byte	2,35,24,7,200,12
	.word	288
	.byte	8,1,0,6
	.byte	'segmentList',0,200,12
	.word	10123
	.byte	2,35,28,6
	.byte	'downloadStep',0,1
	.word	9759
	.byte	3,35,228,12,6
	.byte	'activeJob',0,1
	.word	9617
	.byte	3,35,229,12,0,15
	.byte	'FL_DownloadStateType',0,7,127,3
	.word	9930
.L699:
	.byte	5,7,132,1,9,8,6
	.byte	'remainAddr',0,4
	.word	168
	.byte	2,35,0,6
	.byte	'remainLength',0,4
	.word	168
	.byte	2,35,4,0,15
	.byte	'FL_RemainDataType',0,7,139,1,3
	.word	10228
.L698:
	.byte	7,1
	.word	189
	.byte	8,0,0,20
	.byte	'SecurityErrorFlag',0,7,145,1,14
	.word	189
	.byte	1,1
.L701:
	.byte	7,128,2
	.word	189
	.byte	8,255,1,0,7,48
	.word	189
	.byte	8,47,0
.L703:
	.byte	4
	.word	10353
	.byte	20
	.byte	'lastcounter',0,7,210,1,15
	.word	256
	.byte	1,1,2
	.byte	'char',0,1,6,7,4
	.word	10390
	.byte	8,3,0
.L704:
	.byte	4
	.word	10398
.L705:
	.byte	4
	.word	1335
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L287:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,15,0,73,19,0,0,4,38,0,73,19
	.byte	0,0,5,19,1,58,15,59,15,57,15,11,15,0,0,6,13,0,3,8,11,15,73,19,56,9,0,0,7,1,1,11,15,73,19,0,0,8,33,0,47
	.byte	15,0,0,9,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,10,5,0,3,8,58,15,59,15,57,15,73,19,0,0
	.byte	11,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,12,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39
	.byte	12,63,12,60,12,0,0,13,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,14,59,0,3,8,0,0,15,22,0,3,8,58
	.byte	15,59,15,57,15,73,19,0,0,16,21,0,54,15,0,0,17,23,1,58,15,59,15,57,15,11,15,0,0,18,4,1,58,15,59,15,57,15
	.byte	11,15,0,0,19,40,0,3,8,28,13,0,0,20,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,21,21,0,54,15,39,12
	.byte	0,0,22,21,1,54,15,39,12,0,0,23,5,0,73,19,0,0,24,19,1,3,8,58,15,59,15,57,15,11,15,0,0,25,13,0,3,8,11,15
	.byte	73,19,13,15,12,15,56,9,0,0,26,21,1,73,19,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L288:
	.word	.L820-.L819
.L819:
	.half	3
	.word	.L822-.L821
.L821:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0,0
	.byte	'..\\flash\\SecM.h',0,0,0,0
	.byte	'..\\flash\\FL.h',0,0,0,0
	.byte	'..\\flash\\Fls.h',0,0,0,0
	.byte	'Appl.h',0,1,0,0
	.byte	'Secure.h',0,2,0,0
	.byte	'eeprom.h',0,3,0,0
	.byte	'..\\flash\\FL.c',0,0,0,0
	.byte	'Platform_Types.h',0,4,0,0
	.byte	'Std_Types.h',0,4,0,0
	.byte	'Secure_Types.h',0,2,0,0
	.byte	'stdio.h',0,5,0,0
	.byte	'IfxStm_regdef.h',0,4,0,0
	.byte	'ComStack_Types.h',0,4,0,0
	.byte	'Rte_Type.h',0,3,0,0,0
.L822:
.L820:
	.sdecl	'.debug_info',debug,cluster('FL_InitState')
	.sect	'.debug_info'
.L289:
	.word	213
	.half	3
	.word	.L290
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L292,.L291
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_InitState',0,1,135,2,6,1,1,1
	.word	.L196,.L559,.L195
	.byte	4
	.word	.L560
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_InitState')
	.sect	'.debug_abbrev'
.L290:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_InitState')
	.sect	'.debug_line'
.L291:
	.word	.L824-.L823
.L823:
	.half	3
	.word	.L826-.L825
.L825:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L826:
	.byte	5,5,7,0,5,2
	.word	.L196
	.byte	3,144,2,1,5,43,9
	.half	.L827-.L196
	.byte	1,5,41,1,5,38,9
	.half	.L828-.L827
	.byte	3,6,1,5,36,9
	.half	.L829-.L828
	.byte	3,3,1,5,34,1,5,35,9
	.half	.L830-.L829
	.byte	3,3,1,5,32,9
	.half	.L831-.L830
	.byte	3,3,1,5,5,9
	.half	.L832-.L831
	.byte	3,2,1,5,38,9
	.half	.L833-.L832
	.byte	1,5,1,9
	.half	.L834-.L833
	.byte	3,5,1,7,9
	.half	.L293-.L834
	.byte	0,1,1
.L824:
	.sdecl	'.debug_ranges',debug,cluster('FL_InitState')
	.sect	'.debug_ranges'
.L292:
	.word	-1,.L196,0,.L293-.L196,0,0
.L560:
	.word	-1,.L196,0,.L559-.L196,-1,.L198,0,.L503-.L198,0,0
	.sdecl	'.debug_info',debug,cluster('FL_ReadMemory')
	.sect	'.debug_info'
.L294:
	.word	329
	.half	3
	.word	.L295
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L297,.L296
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_ReadMemory',0,1,209,2,8
	.word	.L561
	.byte	1,1,1
	.word	.L202,.L562,.L201
	.byte	4
	.byte	'address',0,1,209,2,29
	.word	.L561,.L563
	.byte	4
	.byte	'length',0,1,210,2,12
	.word	.L561,.L564
	.byte	4
	.byte	'data',0,1,211,2,13
	.word	.L565,.L566
	.byte	5
	.word	.L567
	.byte	6
	.byte	'readLength',0,1,213,2,12
	.word	.L561,.L568
	.byte	6
	.byte	'curBlockIndex',0,1,214,2,11
	.word	.L569,.L570
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_ReadMemory')
	.sect	'.debug_abbrev'
.L295:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_ReadMemory')
	.sect	'.debug_line'
.L296:
	.word	.L836-.L835
.L835:
	.half	3
	.word	.L838-.L837
.L837:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L838:
	.byte	5,8,7,0,5,2
	.word	.L202
	.byte	3,208,2,1,5,23,9
	.half	.L710-.L202
	.byte	3,4,1,5,25,3,9,1,5,47,9
	.half	.L839-.L710
	.byte	3,124,1,5,50,9
	.half	.L5-.L839
	.byte	3,4,1,5,13,9
	.half	.L712-.L5
	.byte	1,5,42,7,9
	.half	.L713-.L712
	.byte	3,2,1,5,59,9
	.half	.L840-.L713
	.byte	3,127,1,5,22,9
	.half	.L714-.L840
	.byte	1,5,42,7,9
	.half	.L715-.L714
	.byte	3,3,1,5,24,9
	.half	.L711-.L715
	.byte	3,1,1,5,25,9
	.half	.L6-.L711
	.byte	3,4,1,5,41,9
	.half	.L718-.L6
	.byte	1,5,52,9
	.half	.L719-.L718
	.byte	1,5,49,9
	.half	.L841-.L719
	.byte	1,5,42,7,9
	.half	.L842-.L841
	.byte	3,2,1,5,24,9
	.half	.L717-.L842
	.byte	3,1,1,5,22,9
	.half	.L8-.L717
	.byte	3,113,1,5,47,3,127,1,5,23,7,9
	.half	.L843-.L8
	.byte	3,20,1,5,9,9
	.half	.L721-.L843
	.byte	1,5,45,7,9
	.half	.L722-.L721
	.byte	3,1,1,5,18,9
	.half	.L723-.L722
	.byte	1,5,70,7,9
	.half	.L724-.L723
	.byte	3,3,1,5,22,9
	.half	.L12-.L724
	.byte	3,3,1,5,20,9
	.half	.L725-.L12
	.byte	3,2,1,5,21,9
	.half	.L844-.L725
	.byte	3,126,1,5,19,9
	.half	.L726-.L844
	.byte	3,3,1,3,125,1,5,23,9
	.half	.L845-.L726
	.byte	3,4,1,5,16,9
	.half	.L11-.L845
	.byte	3,120,1,5,49,7,9
	.half	.L727-.L11
	.byte	3,1,1,5,22,9
	.half	.L728-.L727
	.byte	1,5,21,7,9
	.half	.L9-.L728
	.byte	3,13,1,5,10,9
	.half	.L729-.L9
	.byte	3,1,1,5,41,9
	.half	.L730-.L729
	.byte	1,5,18,9
	.half	.L846-.L730
	.byte	1,5,60,7,9
	.half	.L847-.L846
	.byte	3,3,1,5,22,9
	.half	.L16-.L847
	.byte	3,3,1,5,20,9
	.half	.L731-.L16
	.byte	3,2,1,5,21,9
	.half	.L848-.L731
	.byte	3,126,1,5,19,9
	.half	.L732-.L848
	.byte	3,3,1,3,125,1,5,23,9
	.half	.L849-.L732
	.byte	3,4,1,5,16,9
	.half	.L15-.L849
	.byte	3,120,1,5,45,7,9
	.half	.L733-.L15
	.byte	3,1,1,5,22,9
	.half	.L734-.L733
	.byte	1,5,30,7,9
	.half	.L14-.L734
	.byte	3,12,1,5,9,9
	.half	.L735-.L14
	.byte	1,5,84,7,9
	.half	.L736-.L735
	.byte	1,5,59,9
	.half	.L737-.L736
	.byte	1,5,78,7,9
	.half	.L738-.L737
	.byte	3,2,1,5,13,9
	.half	.L21-.L738
	.byte	3,3,1,5,11,9
	.half	.L739-.L21
	.byte	3,2,1,5,12,9
	.half	.L850-.L739
	.byte	3,126,1,5,10,9
	.half	.L740-.L850
	.byte	3,3,1,3,125,1,5,14,9
	.half	.L851-.L740
	.byte	3,4,1,5,10,9
	.half	.L20-.L851
	.byte	3,121,1,5,34,7,9
	.half	.L741-.L20
	.byte	1,5,1,7,9
	.half	.L18-.L741
	.byte	3,11,1,7,9
	.half	.L298-.L18
	.byte	0,1,1
.L836:
	.sdecl	'.debug_ranges',debug,cluster('FL_ReadMemory')
	.sect	'.debug_ranges'
.L297:
	.word	-1,.L202,0,.L298-.L202,0,0
.L567:
	.word	-1,.L202,0,.L562-.L202,-1,.L204,0,.L513-.L204,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckProgPreCondition')
	.sect	'.debug_info'
.L299:
	.word	257
	.half	3
	.word	.L300
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L302,.L301
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckProgPreCondition',0,1,172,3,7
	.word	.L569
	.byte	1,1,1
	.word	.L206,.L571,.L205
	.byte	4
	.byte	'conditions',0,1,172,3,40
	.word	.L565,.L572
	.byte	5
	.word	.L206,.L571
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckProgPreCondition')
	.sect	'.debug_abbrev'
.L300:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckProgPreCondition')
	.sect	'.debug_line'
.L301:
	.word	.L853-.L852
.L852:
	.half	3
	.word	.L855-.L854
.L854:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L855:
	.byte	5,19,7,0,5,2
	.word	.L206
	.byte	3,173,3,1,5,17,1,5,12,9
	.half	.L856-.L206
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L303-.L856
	.byte	0,1,1
.L853:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckProgPreCondition')
	.sect	'.debug_ranges'
.L302:
	.word	-1,.L206,0,.L303-.L206,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckModuleId')
	.sect	'.debug_info'
.L304:
	.word	357
	.half	3
	.word	.L305
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L307,.L306
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckModuleId',0,1,146,9,15
	.word	.L569
	.byte	1,1,1
	.word	.L240,.L573,.L239
	.byte	4
	.byte	'moduleId',0,1,146,9,38
	.word	.L569,.L574
	.byte	4
	.byte	'locationInfo',0,1,146,9,55
	.word	.L565,.L575
	.byte	4
	.byte	'idxRet',0,1,146,9,76
	.word	.L565,.L576
	.byte	5
	.word	.L240,.L573
	.byte	6
	.byte	'result',0,1,148,9,19
	.word	.L569,.L577
	.byte	6
	.byte	'locAdr',0,1,149,9,12
	.word	.L561,.L578
	.byte	6
	.byte	'locLength',0,1,150,9,12
	.word	.L561,.L579
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckModuleId')
	.sect	'.debug_abbrev'
.L305:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckModuleId')
	.sect	'.debug_line'
.L306:
	.word	.L858-.L857
.L857:
	.half	3
	.word	.L860-.L859
.L859:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L860:
	.byte	5,15,7,0,5,2
	.word	.L240
	.byte	3,145,9,1,5,40,9
	.half	.L789-.L240
	.byte	3,7,1,5,39,9
	.half	.L785-.L789
	.byte	1,5,19,9
	.half	.L784-.L785
	.byte	3,1,1,5,42,3,1,1,5,18,9
	.half	.L790-.L784
	.byte	3,127,1,5,42,3,1,1,5,27,9
	.half	.L791-.L790
	.byte	3,6,1,5,44,9
	.half	.L787-.L791
	.byte	1,5,16,9
	.half	.L861-.L787
	.byte	1,5,88,7,9
	.half	.L862-.L861
	.byte	1,5,68,9
	.half	.L863-.L862
	.byte	1,5,27,7,9
	.half	.L864-.L863
	.byte	3,2,1,5,25,1,5,24,9
	.half	.L865-.L864
	.byte	3,1,1,5,1,3,10,1,5,24,7,9
	.half	.L124-.L865
	.byte	3,122,1,5,1,3,6,1,7,9
	.half	.L308-.L124
	.byte	0,1,1
.L858:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckModuleId')
	.sect	'.debug_ranges'
.L307:
	.word	-1,.L240,0,.L308-.L240,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckSumRoutine')
	.sect	'.debug_info'
.L309:
	.word	265
	.half	3
	.word	.L310
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L312,.L311
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckSumRoutine',0,1,181,3,15
	.word	.L569
	.byte	1,1,1
	.word	.L208,.L580,.L207
	.byte	4
	.byte	'isHash',0,1,181,3,48
	.word	.L581,.L582
	.byte	5
	.word	.L208,.L580
	.byte	6
	.byte	'ret',0,1,184,3,19
	.word	.L569,.L583
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckSumRoutine')
	.sect	'.debug_abbrev'
.L310:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckSumRoutine')
	.sect	'.debug_line'
.L311:
	.word	.L867-.L866
.L866:
	.half	3
	.word	.L869-.L868
.L868:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L869:
	.byte	5,23,7,0,5,2
	.word	.L208
	.byte	3,183,3,1,5,5,3,4,1,5,26,9
	.half	.L743-.L208
	.byte	1,5,29,9
	.half	.L870-.L743
	.byte	3,2,1,5,45,9
	.half	.L871-.L870
	.byte	1,5,5,9
	.half	.L872-.L871
	.byte	1,5,38,7,9
	.half	.L873-.L872
	.byte	3,3,1,5,36,1,9
	.half	.L874-.L873
	.byte	3,1,1,5,29,9
	.half	.L875-.L874
	.byte	3,1,1,5,9,9
	.half	.L876-.L875
	.byte	1,5,14,7,9
	.half	.L877-.L876
	.byte	3,2,1,5,33,9
	.half	.L878-.L877
	.byte	1,5,10,9
	.half	.L879-.L878
	.byte	1,5,32,7,9
	.half	.L880-.L879
	.byte	3,1,1,5,9,9
	.half	.L742-.L880
	.byte	1,5,34,1,5,9,9
	.half	.L27-.L742
	.byte	3,2,1,3,123,1,5,13,9
	.half	.L25-.L27
	.byte	3,10,1,5,21,3,3,1,5,5,9
	.half	.L26-.L25
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L313-.L26
	.byte	0,1,1
.L867:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckSumRoutine')
	.sect	'.debug_ranges'
.L312:
	.word	-1,.L208,0,.L313-.L208,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckCPBRoutine')
	.sect	'.debug_info'
.L314:
	.word	227
	.half	3
	.word	.L315
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L317,.L316
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckCPBRoutine',0,1,214,3,15
	.word	.L569
	.byte	1,1,1
	.word	.L210,.L584,.L209
	.byte	4
	.word	.L210,.L584
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckCPBRoutine')
	.sect	'.debug_abbrev'
.L315:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckCPBRoutine')
	.sect	'.debug_line'
.L316:
	.word	.L882-.L881
.L881:
	.half	3
	.word	.L884-.L883
.L883:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L884:
	.byte	5,2,7,0,5,2
	.word	.L210
	.byte	3,216,3,1,5,31,9
	.half	.L885-.L210
	.byte	1,5,29,1,5,39,9
	.half	.L886-.L885
	.byte	3,1,1,5,9,9
	.half	.L887-.L886
	.byte	3,1,1,5,1,3,1,1,7,9
	.half	.L318-.L887
	.byte	0,1,1
.L882:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckCPBRoutine')
	.sect	'.debug_ranges'
.L317:
	.word	-1,.L210,0,.L318-.L210,0,0
	.sdecl	'.debug_info',debug,cluster('FBL_IntegrityCheck')
	.sect	'.debug_info'
.L319:
	.word	279
	.half	3
	.word	.L320
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L322,.L321
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FBL_IntegrityCheck',0,1,221,3,15
	.word	.L569
	.byte	1,1,1
	.word	.L212,.L585,.L211
	.byte	4
	.word	.L212,.L585
	.byte	5
	.byte	'ret',0,1,223,3,19
	.word	.L569,.L586
	.byte	5
	.byte	'someSignVerifiedFlag',0,1,225,3,11
	.word	.L569,.L587
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FBL_IntegrityCheck')
	.sect	'.debug_abbrev'
.L320:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FBL_IntegrityCheck')
	.sect	'.debug_line'
.L321:
	.word	.L889-.L888
.L888:
	.half	3
	.word	.L891-.L890
.L890:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L891:
	.byte	5,19,7,0,5,2
	.word	.L212
	.byte	3,228,3,1,5,36,9
	.half	.L892-.L212
	.byte	1,5,32,9
	.half	.L893-.L892
	.byte	3,124,1,5,12,9
	.half	.L744-.L893
	.byte	3,4,1,5,53,7,9
	.half	.L894-.L744
	.byte	1,5,71,9
	.half	.L895-.L894
	.byte	1,5,50,9
	.half	.L896-.L895
	.byte	1,5,34,9
	.half	.L897-.L896
	.byte	3,2,1,5,30,9
	.half	.L32-.L897
	.byte	3,4,1,5,46,9
	.half	.L898-.L32
	.byte	1,5,9,9
	.half	.L899-.L898
	.byte	1,5,10,7,9
	.half	.L900-.L899
	.byte	3,1,1,5,74,7,9
	.half	.L901-.L900
	.byte	1,5,9,7,9
	.half	.L34-.L901
	.byte	3,7,1,5,38,7,9
	.half	.L902-.L34
	.byte	3,2,1,5,13,9
	.half	.L37-.L902
	.byte	3,4,1,5,22,9
	.half	.L903-.L37
	.byte	1,5,20,1,5,17,9
	.half	.L904-.L903
	.byte	3,1,1,5,1,3,9,1,5,13,7,9
	.half	.L35-.L904
	.byte	3,124,1,5,1,3,4,1,7,9
	.half	.L323-.L35
	.byte	0,1,1
.L889:
	.sdecl	'.debug_ranges',debug,cluster('FBL_IntegrityCheck')
	.sect	'.debug_ranges'
.L322:
	.word	-1,.L212,0,.L323-.L212,0,0
	.sdecl	'.debug_info',debug,cluster('FL_EraseRoutine')
	.sect	'.debug_info'
.L324:
	.word	266
	.half	3
	.word	.L325
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L327,.L326
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_EraseRoutine',0,1,146,4,15
	.word	.L569
	.byte	1,1,1
	.word	.L214,.L588,.L213
	.byte	4
	.byte	'blockIndex',0,1,146,4,43
	.word	.L589,.L590
	.byte	5
	.word	.L214,.L588
	.byte	6
	.byte	'ret',0,1,148,4,19
	.word	.L569,.L591
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_EraseRoutine')
	.sect	'.debug_abbrev'
.L325:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_EraseRoutine')
	.sect	'.debug_line'
.L326:
	.word	.L906-.L905
.L905:
	.half	3
	.word	.L908-.L907
.L907:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L908:
	.byte	5,15,7,0,5,2
	.word	.L214
	.byte	3,145,4,1,5,23,9
	.half	.L746-.L214
	.byte	3,2,1,5,5,3,1,1,5,28,9
	.half	.L747-.L746
	.byte	1,5,26,1,5,18,9
	.half	.L745-.L747
	.byte	3,2,1,5,34,9
	.half	.L748-.L745
	.byte	1,5,5,9
	.half	.L909-.L748
	.byte	1,5,13,7,9
	.half	.L910-.L909
	.byte	3,2,1,5,32,1,5,38,9
	.half	.L41-.L910
	.byte	3,5,1,5,9,9
	.half	.L911-.L41
	.byte	1,5,17,7,9
	.half	.L912-.L911
	.byte	3,2,1,5,36,1,5,52,9
	.half	.L43-.L912
	.byte	3,5,1,5,13,9
	.half	.L913-.L43
	.byte	1,5,14,7,9
	.half	.L914-.L913
	.byte	3,3,1,5,19,7,9
	.half	.L915-.L914
	.byte	3,2,1,5,38,9
	.half	.L916-.L915
	.byte	1,5,15,9
	.half	.L917-.L916
	.byte	1,5,46,7,9
	.half	.L918-.L917
	.byte	3,1,1,5,23,9
	.half	.L749-.L918
	.byte	1,5,15,3,127,1,5,21,9
	.half	.L46-.L749
	.byte	3,7,1,5,5,9
	.half	.L42-.L46
	.byte	3,8,1,5,9,7,9
	.half	.L919-.L42
	.byte	3,3,1,5,27,7,9
	.half	.L920-.L919
	.byte	3,3,1,5,17,1,5,27,9
	.half	.L921-.L920
	.byte	1,5,39,9
	.half	.L922-.L921
	.byte	3,127,1,9
	.half	.L923-.L922
	.byte	3,1,1,5,19,9
	.half	.L924-.L923
	.byte	3,127,1,5,39,9
	.half	.L925-.L924
	.byte	1,5,51,9
	.half	.L926-.L925
	.byte	1,5,18,9
	.half	.L927-.L926
	.byte	1,5,24,7,9
	.half	.L928-.L927
	.byte	3,2,1,5,61,7,9
	.half	.L51-.L928
	.byte	3,3,1,5,59,1,5,18,9
	.half	.L929-.L51
	.byte	3,3,1,5,31,7,9
	.half	.L930-.L929
	.byte	3,4,1,5,30,1,9
	.half	.L931-.L930
	.byte	3,1,1,5,26,9
	.half	.L932-.L931
	.byte	3,1,1,5,43,9
	.half	.L933-.L932
	.byte	1,5,26,9
	.half	.L934-.L933
	.byte	3,1,1,5,43,9
	.half	.L935-.L934
	.byte	1,5,20,9
	.half	.L53-.L935
	.byte	3,10,1,5,17,9
	.half	.L936-.L53
	.byte	1,5,53,7,9
	.half	.L937-.L936
	.byte	3,3,1,5,68,9
	.half	.L938-.L937
	.byte	1,5,41,9
	.half	.L939-.L938
	.byte	3,1,1,5,39,1,5,61,9
	.half	.L54-.L939
	.byte	3,8,1,5,37,9
	.half	.L940-.L54
	.byte	3,1,1,5,51,9
	.half	.L941-.L940
	.byte	1,5,45,9
	.half	.L942-.L941
	.byte	3,4,1,5,73,9
	.half	.L943-.L942
	.byte	1,5,71,9
	.half	.L944-.L943
	.byte	1,5,70,9
	.half	.L945-.L944
	.byte	3,1,1,5,45,9
	.half	.L946-.L945
	.byte	3,3,1,5,46,9
	.half	.L947-.L946
	.byte	3,1,1,5,44,9
	.half	.L948-.L947
	.byte	3,1,1,5,46,9
	.half	.L949-.L948
	.byte	3,3,1,5,44,1,5,74,9
	.half	.L950-.L949
	.byte	3,115,1,5,21,9
	.half	.L52-.L950
	.byte	3,17,1,5,13,3,126,1,5,17,9
	.half	.L50-.L52
	.byte	3,7,1,5,5,9
	.half	.L49-.L50
	.byte	3,4,1,5,21,7,9
	.half	.L951-.L49
	.byte	3,3,1,5,5,9
	.half	.L57-.L951
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L328-.L57
	.byte	0,1,1
.L906:
	.sdecl	'.debug_ranges',debug,cluster('FL_EraseRoutine')
	.sect	'.debug_ranges'
.L327:
	.word	-1,.L214,0,.L328-.L214,0,0
	.sdecl	'.debug_info',debug,cluster('FL_DownloadRequestValid')
	.sect	'.debug_info'
.L329:
	.word	292
	.half	3
	.word	.L330
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L332,.L331
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_DownloadRequestValid',0,1,152,5,15
	.word	.L569
	.byte	1,1,1
	.word	.L216,.L592,.L215
	.byte	4
	.byte	'startAdd',0,1,152,5,52
	.word	.L593,.L594
	.byte	4
	.byte	'length',0,1,153,5,18
	.word	.L595,.L596
	.byte	5
	.word	.L216,.L592
	.byte	6
	.byte	'ret',0,1,155,5,19
	.word	.L569,.L597
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_DownloadRequestValid')
	.sect	'.debug_abbrev'
.L330:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_DownloadRequestValid')
	.sect	'.debug_line'
.L331:
	.word	.L953-.L952
.L952:
	.half	3
	.word	.L955-.L954
.L954:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L955:
	.byte	5,9,7,0,5,2
	.word	.L216
	.byte	3,157,5,1,5,25,9
	.half	.L956-.L216
	.byte	1,5,36,9
	.half	.L957-.L956
	.byte	1,5,25,9
	.half	.L958-.L957
	.byte	3,1,1,5,33,9
	.half	.L959-.L958
	.byte	1,5,23,9
	.half	.L960-.L959
	.byte	3,124,1,5,34,9
	.half	.L751-.L960
	.byte	3,7,1,5,5,9
	.half	.L961-.L751
	.byte	1,5,13,7,9
	.half	.L962-.L961
	.byte	3,2,1,5,32,1,5,49,9
	.half	.L59-.L962
	.byte	3,5,1,5,13,9
	.half	.L963-.L59
	.byte	1,5,31,7,9
	.half	.L964-.L963
	.byte	3,1,1,5,17,7,9
	.half	.L965-.L964
	.byte	3,2,1,5,34,1,5,21,9
	.half	.L61-.L965
	.byte	3,6,1,5,5,9
	.half	.L60-.L61
	.byte	3,6,1,5,38,7,9
	.half	.L966-.L60
	.byte	3,3,1,5,9,9
	.half	.L967-.L966
	.byte	1,5,18,7,9
	.half	.L968-.L967
	.byte	3,3,1,5,57,1,5,18,9
	.half	.L969-.L968
	.byte	1,5,17,9
	.half	.L970-.L969
	.byte	1,5,34,7,9
	.half	.L971-.L970
	.byte	3,1,1,5,45,9
	.half	.L972-.L971
	.byte	1,5,42,9
	.half	.L973-.L972
	.byte	1,5,49,7,9
	.half	.L974-.L973
	.byte	3,3,1,5,47,1,5,46,9
	.half	.L975-.L974
	.byte	3,2,1,5,45,9
	.half	.L976-.L975
	.byte	1,5,84,9
	.half	.L977-.L976
	.byte	1,5,83,9
	.half	.L978-.L977
	.byte	1,5,53,9
	.half	.L979-.L978
	.byte	3,2,1,5,94,9
	.half	.L980-.L979
	.byte	3,127,1,5,39,9
	.half	.L981-.L980
	.byte	3,3,1,5,93,9
	.half	.L982-.L981
	.byte	3,127,1,5,65,9
	.half	.L983-.L982
	.byte	3,123,1,5,21,9
	.half	.L67-.L983
	.byte	3,10,1,5,13,3,126,1,5,42,9
	.half	.L66-.L67
	.byte	3,8,1,5,17,9
	.half	.L750-.L66
	.byte	1,5,13,9
	.half	.L752-.L750
	.byte	3,2,1,5,42,7,9
	.half	.L984-.L752
	.byte	3,2,1,5,21,9
	.half	.L753-.L984
	.byte	1,5,36,3,2,1,5,49,9
	.half	.L754-.L753
	.byte	3,81,1,5,17,9
	.half	.L985-.L754
	.byte	3,47,1,5,51,7,9
	.half	.L986-.L985
	.byte	3,3,1,5,76,1,5,22,9
	.half	.L72-.L986
	.byte	3,3,1,5,51,9
	.half	.L987-.L72
	.byte	3,3,1,5,13,9
	.half	.L73-.L987
	.byte	3,115,1,5,5,9
	.half	.L65-.L73
	.byte	3,20,1,5,21,7,9
	.half	.L988-.L65
	.byte	3,3,1,5,34,9
	.half	.L75-.L988
	.byte	3,3,1,5,32,1,5,5,9
	.half	.L989-.L75
	.byte	3,1,1,5,16,9
	.half	.L990-.L989
	.byte	1,5,5,9
	.half	.L991-.L990
	.byte	3,1,1,5,1,3,1,1,7,9
	.half	.L333-.L991
	.byte	0,1,1
.L953:
	.sdecl	'.debug_ranges',debug,cluster('FL_DownloadRequestValid')
	.sect	'.debug_ranges'
.L332:
	.word	-1,.L216,0,.L333-.L216,0,0
	.sdecl	'.debug_info',debug,cluster('FL_FlashProgramRegion')
	.sect	'.debug_info'
.L334:
	.word	330
	.half	3
	.word	.L335
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L337,.L336
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_FlashProgramRegion',0,1,129,6,15
	.word	.L569
	.byte	1,1,1
	.word	.L218,.L598,.L217
	.byte	4
	.byte	'destAddr',0,1,129,6,50
	.word	.L599,.L600
	.byte	4
	.byte	'sourceBuff',0,1,130,6,18
	.word	.L601,.L602
	.byte	4
	.byte	'length',0,1,131,6,18
	.word	.L603,.L604
	.byte	5
	.word	.L605
	.byte	6
	.byte	'ret',0,1,133,6,19
	.word	.L569,.L606
	.byte	6
	.byte	'result',0,1,134,6,19
	.word	.L569,.L607
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_FlashProgramRegion')
	.sect	'.debug_abbrev'
.L335:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_FlashProgramRegion')
	.sect	'.debug_line'
.L336:
	.word	.L993-.L992
.L992:
	.half	3
	.word	.L995-.L994
.L994:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L995:
	.byte	5,15,7,0,5,2
	.word	.L218
	.byte	3,128,6,1,5,23,9
	.half	.L758-.L218
	.byte	3,4,1,5,30,3,6,1,5,46,9
	.half	.L996-.L758
	.byte	1,5,9,9
	.half	.L997-.L996
	.byte	1,5,89,7,9
	.half	.L998-.L997
	.byte	1,5,13,7,9
	.half	.L999-.L998
	.byte	3,2,1,5,30,1,9
	.half	.L77-.L999
	.byte	3,4,1,5,13,9
	.half	.L1000-.L77
	.byte	1,5,30,7,9
	.half	.L1001-.L1000
	.byte	3,1,1,5,38,9
	.half	.L1002-.L1001
	.byte	1,5,17,7,9
	.half	.L80-.L1002
	.byte	3,2,1,5,5,9
	.half	.L79-.L80
	.byte	3,4,1,5,38,7,9
	.half	.L1003-.L79
	.byte	3,3,1,5,9,9
	.half	.L1004-.L1003
	.byte	1,5,25,7,9
	.half	.L1005-.L1004
	.byte	3,3,1,5,66,9
	.half	.L757-.L1005
	.byte	1,5,29,9
	.half	.L756-.L757
	.byte	3,3,1,5,40,9
	.half	.L1006-.L756
	.byte	1,5,29,9
	.half	.L1007-.L1006
	.byte	3,1,1,5,37,9
	.half	.L1008-.L1007
	.byte	1,5,13,9
	.half	.L1009-.L1008
	.byte	3,3,1,5,49,7,9
	.half	.L1010-.L1009
	.byte	3,3,1,5,47,1,5,53,9
	.half	.L1011-.L1010
	.byte	3,3,1,5,26,9
	.half	.L83-.L1011
	.byte	3,3,1,5,39,9
	.half	.L1012-.L83
	.byte	1,5,11,9
	.half	.L1013-.L1012
	.byte	1,5,69,7,9
	.half	.L1014-.L1013
	.byte	3,1,1,5,54,9
	.half	.L1015-.L1014
	.byte	1,5,29,9
	.half	.L1016-.L1015
	.byte	1,5,13,7,9
	.half	.L1017-.L1016
	.byte	3,2,1,5,30,9
	.half	.L1018-.L1017
	.byte	1,5,28,1,5,56,9
	.half	.L760-.L1018
	.byte	3,1,1,5,64,1,5,29,9
	.half	.L761-.L760
	.byte	3,1,1,5,40,9
	.half	.L1019-.L761
	.byte	1,5,29,9
	.half	.L1020-.L1019
	.byte	3,1,1,5,37,9
	.half	.L1021-.L1020
	.byte	1,5,17,9
	.half	.L1022-.L1021
	.byte	3,1,1,5,59,7,9
	.half	.L1023-.L1022
	.byte	1,5,49,7,9
	.half	.L1024-.L1023
	.byte	3,3,1,5,47,1,5,77,9
	.half	.L1025-.L1024
	.byte	1,5,18,9
	.half	.L88-.L1025
	.byte	3,2,1,5,21,9
	.half	.L1026-.L88
	.byte	3,2,1,5,42,9
	.half	.L84-.L1026
	.byte	3,6,1,5,53,9
	.half	.L762-.L84
	.byte	1,5,39,9
	.half	.L86-.L762
	.byte	3,15,1,5,13,9
	.half	.L1027-.L86
	.byte	3,1,1,5,29,9
	.half	.L1028-.L1027
	.byte	1,5,42,9
	.half	.L1029-.L1028
	.byte	3,3,1,5,40,1,5,42,9
	.half	.L1030-.L1029
	.byte	3,1,1,5,40,1,5,5,9
	.half	.L82-.L1030
	.byte	3,4,1,5,21,7,9
	.half	.L1031-.L82
	.byte	3,3,1,5,5,9
	.half	.L93-.L1031
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L338-.L93
	.byte	0,1,1
.L993:
	.sdecl	'.debug_ranges',debug,cluster('FL_FlashProgramRegion')
	.sect	'.debug_ranges'
.L337:
	.word	-1,.L218,0,.L338-.L218,0,0
.L605:
	.word	-1,.L218,0,.L598-.L218,-1,.L220,0,.L518-.L220,0,0
	.sdecl	'.debug_info',debug,cluster('FL_SetExitTransferStep')
	.sect	'.debug_info'
.L339:
	.word	227
	.half	3
	.word	.L340
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L342,.L341
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_SetExitTransferStep',0,1,241,6,6,1,1,1
	.word	.L222,.L608,.L221
	.byte	4
	.word	.L222,.L608
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_SetExitTransferStep')
	.sect	'.debug_abbrev'
.L340:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_SetExitTransferStep')
	.sect	'.debug_line'
.L341:
	.word	.L1033-.L1032
.L1032:
	.half	3
	.word	.L1035-.L1034
.L1034:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1035:
	.byte	5,2,7,0,5,2
	.word	.L222
	.byte	3,242,6,1,5,34,9
	.half	.L1036-.L222
	.byte	1,5,32,1,5,31,9
	.half	.L1037-.L1036
	.byte	3,1,1,5,1,9
	.half	.L1038-.L1037
	.byte	3,1,1,7,9
	.half	.L343-.L1038
	.byte	0,1,1
.L1033:
	.sdecl	'.debug_ranges',debug,cluster('FL_SetExitTransferStep')
	.sect	'.debug_ranges'
.L342:
	.word	-1,.L222,0,.L343-.L222,0,0
	.sdecl	'.debug_info',debug,cluster('FL_SetHeadBlockErased')
	.sect	'.debug_info'
.L344:
	.word	226
	.half	3
	.word	.L345
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L347,.L346
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_SetHeadBlockErased',0,1,133,7,6,1,1,1
	.word	.L224,.L609,.L223
	.byte	4
	.word	.L224,.L609
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_SetHeadBlockErased')
	.sect	'.debug_abbrev'
.L345:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_SetHeadBlockErased')
	.sect	'.debug_line'
.L346:
	.word	.L1040-.L1039
.L1039:
	.half	3
	.word	.L1042-.L1041
.L1041:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1042:
	.byte	5,2,7,0,5,2
	.word	.L224
	.byte	3,134,7,1,5,33,9
	.half	.L1043-.L224
	.byte	1,5,31,1,9
	.half	.L1044-.L1043
	.byte	3,1,1,5,29,9
	.half	.L1045-.L1044
	.byte	3,1,1,5,1,9
	.half	.L1046-.L1045
	.byte	3,1,1,7,9
	.half	.L348-.L1046
	.byte	0,1,1
.L1040:
	.sdecl	'.debug_ranges',debug,cluster('FL_SetHeadBlockErased')
	.sect	'.debug_ranges'
.L347:
	.word	-1,.L224,0,.L348-.L224,0,0
	.sdecl	'.debug_info',debug,cluster('FL_ExitTransferData')
	.sect	'.debug_info'
.L349:
	.word	272
	.half	3
	.word	.L350
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L352,.L351
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_ExitTransferData',0,1,154,7,15
	.word	.L569
	.byte	1,1,1
	.word	.L226,.L610,.L225
	.byte	4
	.word	.L226,.L610
	.byte	5
	.byte	'ret',0,1,156,7,19
	.word	.L569,.L611
	.byte	5
	.byte	'signVerifRet',0,1,157,7,19
	.word	.L569,.L612
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_ExitTransferData')
	.sect	'.debug_abbrev'
.L350:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_ExitTransferData')
	.sect	'.debug_line'
.L351:
	.word	.L1048-.L1047
.L1047:
	.half	3
	.word	.L1050-.L1049
.L1049:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1050:
	.byte	5,23,7,0,5,2
	.word	.L226
	.byte	3,155,7,1,5,34,3,2,1,5,50,9
	.half	.L1051-.L226
	.byte	1,5,5,9
	.half	.L1052-.L1051
	.byte	1,5,41,7,9
	.half	.L1053-.L1052
	.byte	3,3,1,5,39,1,5,36,9
	.half	.L1054-.L1053
	.byte	3,1,1,5,57,9
	.half	.L1055-.L1054
	.byte	3,127,1,5,10,9
	.half	.L95-.L1055
	.byte	3,3,1,5,47,7,9
	.half	.L1056-.L95
	.byte	3,3,1,5,12,9
	.half	.L764-.L1056
	.byte	3,2,1,5,9,9
	.half	.L1057-.L764
	.byte	1,5,17,7,9
	.half	.L1058-.L1057
	.byte	3,2,1,5,34,1,5,32,9
	.half	.L98-.L1058
	.byte	3,4,1,5,9,9
	.half	.L99-.L98
	.byte	3,3,1,5,13,7,9
	.half	.L1059-.L99
	.byte	3,2,1,5,37,9
	.half	.L1060-.L1059
	.byte	1,5,35,1,5,17,9
	.half	.L1061-.L1060
	.byte	3,1,1,5,45,3,127,1,5,24,9
	.half	.L100-.L1061
	.byte	3,5,1,5,9,9
	.half	.L765-.L100
	.byte	3,126,1,5,13,9
	.half	.L97-.L765
	.byte	3,11,1,5,21,3,1,1,5,5,9
	.half	.L96-.L97
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L353-.L96
	.byte	0,1,1
.L1048:
	.sdecl	'.debug_ranges',debug,cluster('FL_ExitTransferData')
	.sect	'.debug_ranges'
.L352:
	.word	-1,.L226,0,.L353-.L226,0,0
	.sdecl	'.debug_info',debug,cluster('FL_ServiceFinished')
	.sect	'.debug_info'
.L354:
	.word	264
	.half	3
	.word	.L355
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L357,.L356
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_ServiceFinished',0,1,216,7,9
	.word	.L569
	.byte	1,1,1
	.word	.L228,.L613,.L227
	.byte	4
	.byte	'error',0,1,216,7,43
	.word	.L614,.L615
	.byte	5
	.word	.L228,.L613
	.byte	6
	.byte	'ret',0,1,218,7,13
	.word	.L569,.L616
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_ServiceFinished')
	.sect	'.debug_abbrev'
.L355:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_ServiceFinished')
	.sect	'.debug_line'
.L356:
	.word	.L1063-.L1062
.L1062:
	.half	3
	.word	.L1065-.L1064
.L1064:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1065:
	.byte	5,24,7,0,5,2
	.word	.L228
	.byte	3,220,7,1,5,40,9
	.half	.L766-.L228
	.byte	1,5,5,9
	.half	.L1066-.L766
	.byte	1,5,34,7,9
	.half	.L1067-.L1066
	.byte	3,2,1,5,16,9
	.half	.L1068-.L1067
	.byte	1,5,13,9
	.half	.L1069-.L1068
	.byte	3,1,1,5,1,3,9,1,5,18,7,9
	.half	.L104-.L1069
	.byte	3,123,1,5,16,1,5,1,9
	.half	.L767-.L104
	.byte	3,5,1,7,9
	.half	.L358-.L767
	.byte	0,1,1
.L1063:
	.sdecl	'.debug_ranges',debug,cluster('FL_ServiceFinished')
	.sect	'.debug_ranges'
.L357:
	.word	-1,.L228,0,.L358-.L228,0,0
	.sdecl	'.debug_info',debug,cluster('FL_MainFunction')
	.sect	'.debug_info'
.L359:
	.word	220
	.half	3
	.word	.L360
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L362,.L361
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_MainFunction',0,1,189,9,6,1,1,1
	.word	.L242,.L617,.L241
	.byte	4
	.word	.L242,.L617
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_MainFunction')
	.sect	'.debug_abbrev'
.L360:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_MainFunction')
	.sect	'.debug_line'
.L361:
	.word	.L1071-.L1070
.L1070:
	.half	3
	.word	.L1073-.L1072
.L1072:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1073:
	.byte	5,13,7,0,5,2
	.word	.L242
	.byte	3,190,9,1,5,29,9
	.half	.L1074-.L242
	.byte	1,5,14,9
	.half	.L1075-.L1074
	.byte	3,2,1,7,9
	.half	.L1076-.L1075
	.byte	3,7,1,7,9
	.half	.L1077-.L1076
	.byte	3,6,1,7,9
	.half	.L1078-.L1077
	.byte	3,5,1,7,9
	.half	.L1079-.L1078
	.byte	1,5,52,9
	.half	.L128-.L1079
	.byte	3,112,1,5,13,9
	.half	.L1080-.L128
	.byte	3,3,1,5,56,9
	.half	.L129-.L1080
	.byte	3,4,1,5,13,9
	.half	.L1081-.L129
	.byte	3,2,1,5,56,9
	.half	.L130-.L1081
	.byte	3,4,1,5,13,9
	.half	.L1082-.L130
	.byte	3,2,1,5,59,9
	.half	.L131-.L1082
	.byte	3,3,1,5,37,9
	.half	.L135-.L131
	.byte	1,5,39,9
	.half	.L1083-.L135
	.byte	3,1,1,5,37,1,5,25,9
	.half	.L132-.L1083
	.byte	3,7,1,5,5,9
	.half	.L1084-.L132
	.byte	1,5,21,7,9
	.half	.L1085-.L1084
	.byte	3,3,1,5,38,9
	.half	.L1086-.L1085
	.byte	3,1,1,5,36,1,5,1,9
	.half	.L136-.L1086
	.byte	3,6,1,7,9
	.half	.L363-.L136
	.byte	0,1,1
.L1071:
	.sdecl	'.debug_ranges',debug,cluster('FL_MainFunction')
	.sect	'.debug_ranges'
.L362:
	.word	-1,.L242,0,.L363-.L242,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckSumFor37')
	.sect	'.debug_info'
.L364:
	.word	410
	.half	3
	.word	.L365
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L367,.L366
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckSumFor37',0,1,137,8,15
	.word	.L569
	.byte	1,1,1
	.word	.L234,.L618,.L233
	.byte	4
	.byte	'crc',0,1,137,8,46
	.word	.L619,.L620
	.byte	5
	.word	.L234,.L618
	.byte	6
	.byte	'ret',0,1,139,8,16
	.word	.L569,.L621
	.byte	6
	.byte	'verifyParam',0,1,140,8,23
	.word	.L622,.L623
	.byte	6
	.byte	'crcret',0,1,141,8,18
	.word	.L569,.L624
	.byte	6
	.byte	'l_nrOfSegments',0,1,142,8,9
	.word	.L625,.L626
	.byte	6
	.byte	'currentaddr',0,1,143,8,9
	.word	.L561,.L627
	.byte	6
	.byte	'currentlength',0,1,143,8,21
	.word	.L561,.L628
	.byte	6
	.byte	'segmentList',0,1,147,8,21
	.word	.L629,.L630
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckSumFor37')
	.sect	'.debug_abbrev'
.L365:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckSumFor37')
	.sect	'.debug_line'
.L366:
	.word	.L1088-.L1087
.L1087:
	.half	3
	.word	.L1090-.L1089
.L1089:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1090:
	.byte	5,15,7,0,5,2
	.word	.L234
	.byte	3,136,8,1,5,27,9
	.half	.L769-.L234
	.byte	3,11,1,5,25,9
	.half	.L1091-.L769
	.byte	1,5,30,9
	.half	.L1092-.L1091
	.byte	3,2,1,5,46,9
	.half	.L1093-.L1092
	.byte	1,5,20,9
	.half	.L1094-.L1093
	.byte	3,117,1,5,15,3,126,1,5,29,9
	.half	.L771-.L1094
	.byte	3,13,1,5,19,1,5,29,9
	.half	.L1095-.L771
	.byte	1,5,58,9
	.half	.L1096-.L1095
	.byte	1,5,2,9
	.half	.L1097-.L1096
	.byte	1,5,22,7,9
	.half	.L1098-.L1097
	.byte	3,22,1,5,9,9
	.half	.L1099-.L1098
	.byte	1,5,37,7,9
	.half	.L1100-.L1099
	.byte	3,2,1,5,35,1,5,47,9
	.half	.L107-.L1100
	.byte	3,5,1,5,46,9
	.half	.L1101-.L107
	.byte	1,5,71,9
	.half	.L1102-.L1101
	.byte	1,5,79,9
	.half	.L772-.L1102
	.byte	3,1,1,5,97,9
	.half	.L1103-.L772
	.byte	1,5,99,9
	.half	.L774-.L1103
	.byte	3,1,1,5,5,9
	.half	.L776-.L774
	.byte	3,3,1,5,24,9
	.half	.L1104-.L776
	.byte	1,5,2,9
	.half	.L773-.L1104
	.byte	1,5,37,7,9
	.half	.L1105-.L773
	.byte	3,2,1,5,62,9
	.half	.L1106-.L1105
	.byte	3,6,1,5,3,9
	.half	.L1107-.L1106
	.byte	3,122,1,5,81,7,9
	.half	.L1108-.L1107
	.byte	3,4,1,5,62,3,2,1,5,41,9
	.half	.L1109-.L1108
	.byte	3,124,1,5,51,3,4,1,5,50,9
	.half	.L1110-.L1109
	.byte	3,1,1,5,43,9
	.half	.L1111-.L1110
	.byte	3,123,1,5,108,9
	.half	.L110-.L1111
	.byte	3,10,1,5,75,9
	.half	.L1112-.L110
	.byte	1,5,41,9
	.half	.L775-.L1112
	.byte	3,127,1,5,75,9
	.half	.L1113-.L775
	.byte	3,1,1,5,3,9
	.half	.L778-.L1113
	.byte	3,125,1,5,40,9
	.half	.L109-.L778
	.byte	3,9,1,5,41,9
	.half	.L111-.L109
	.byte	3,5,1,5,29,9
	.half	.L1114-.L111
	.byte	1,5,2,9
	.half	.L777-.L1114
	.byte	3,1,1,5,19,7,9
	.half	.L1115-.L777
	.byte	3,2,1,5,7,9
	.half	.L1116-.L1115
	.byte	1,5,28,9
	.half	.L1117-.L1116
	.byte	1,5,6,9
	.half	.L113-.L1117
	.byte	3,5,1,5,2,9
	.half	.L114-.L113
	.byte	3,4,1,5,1,3,1,1,7,9
	.half	.L368-.L114
	.byte	0,1,1
.L1088:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckSumFor37')
	.sect	'.debug_ranges'
.L367:
	.word	-1,.L234,0,.L368-.L234,0,0
	.sdecl	'.debug_info',debug,cluster('FBL_CheckSumFor37')
	.sect	'.debug_info'
.L369:
	.word	267
	.half	3
	.word	.L370
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L372,.L371
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FBL_CheckSumFor37',0,1,223,8,15
	.word	.L569
	.byte	1,1,1
	.word	.L236,.L631,.L235
	.byte	4
	.byte	'crcValPtr',0,1,223,8,42
	.word	.L632,.L633
	.byte	5
	.word	.L236,.L631
	.byte	6
	.byte	'ret',0,1,225,8,19
	.word	.L569,.L634
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FBL_CheckSumFor37')
	.sect	'.debug_abbrev'
.L370:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FBL_CheckSumFor37')
	.sect	'.debug_line'
.L371:
	.word	.L1119-.L1118
.L1118:
	.half	3
	.word	.L1121-.L1120
.L1120:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1121:
	.byte	5,22,7,0,5,2
	.word	.L236
	.byte	3,226,8,1,5,40,9
	.half	.L1122-.L236
	.byte	1,5,9,9
	.half	.L1123-.L1122
	.byte	1,5,81,7,9
	.half	.L1124-.L1123
	.byte	1,5,97,9
	.half	.L1125-.L1124
	.byte	1,5,78,9
	.half	.L1126-.L1125
	.byte	1,5,40,7,9
	.half	.L1127-.L1126
	.byte	3,2,1,5,13,9
	.half	.L779-.L1127
	.byte	1,5,41,9
	.half	.L781-.L779
	.byte	3,1,1,5,39,1,5,20,9
	.half	.L1128-.L781
	.byte	3,1,1,5,22,9
	.half	.L780-.L1128
	.byte	3,1,1,5,9,9
	.half	.L1129-.L780
	.byte	1,5,37,7,9
	.half	.L1130-.L1129
	.byte	3,2,1,5,9,3,126,1,5,81,9
	.half	.L116-.L1130
	.byte	3,123,1,5,97,9
	.half	.L1131-.L116
	.byte	1,5,50,9
	.half	.L1132-.L1131
	.byte	3,10,1,5,10,9
	.half	.L1133-.L1132
	.byte	1,5,32,7,9
	.half	.L1134-.L1133
	.byte	3,2,1,5,13,9
	.half	.L782-.L1134
	.byte	1,5,42,1,5,17,9
	.half	.L120-.L782
	.byte	3,4,1,5,45,9
	.half	.L783-.L120
	.byte	3,1,1,5,43,9
	.half	.L119-.L783
	.byte	1,5,5,9
	.half	.L118-.L119
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L373-.L118
	.byte	0,1,1
.L1119:
	.sdecl	'.debug_ranges',debug,cluster('FBL_CheckSumFor37')
	.sect	'.debug_ranges'
.L372:
	.word	-1,.L236,0,.L373-.L236,0,0
	.sdecl	'.debug_info',debug,cluster('SetS37JobStatusBusy')
	.sect	'.debug_info'
.L374:
	.word	224
	.half	3
	.word	.L375
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L377,.L376
	.byte	2
	.word	.L285
	.byte	3
	.byte	'SetS37JobStatusBusy',0,1,241,7,6,1,1,1
	.word	.L230,.L635,.L229
	.byte	4
	.word	.L230,.L635
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SetS37JobStatusBusy')
	.sect	'.debug_abbrev'
.L375:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SetS37JobStatusBusy')
	.sect	'.debug_line'
.L376:
	.word	.L1136-.L1135
.L1135:
	.half	3
	.word	.L1138-.L1137
.L1137:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1138:
	.byte	5,2,7,0,5,2
	.word	.L230
	.byte	3,242,7,1,5,29,9
	.half	.L1139-.L230
	.byte	1,5,28,1,5,1,9
	.half	.L1140-.L1139
	.byte	3,1,1,7,9
	.half	.L378-.L1140
	.byte	0,1,1
.L1136:
	.sdecl	'.debug_ranges',debug,cluster('SetS37JobStatusBusy')
	.sect	'.debug_ranges'
.L377:
	.word	-1,.L230,0,.L378-.L230,0,0
	.sdecl	'.debug_info',debug,cluster('GetS37JobStatusResult')
	.sect	'.debug_info'
.L379:
	.word	246
	.half	3
	.word	.L380
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L382,.L381
	.byte	2
	.word	.L285
	.byte	3
	.byte	'GetS37JobStatusResult',0,1,252,7,6,1,1,1
	.word	.L232,.L636,.L231
	.byte	4
	.byte	'status',0,1,252,7,42
	.word	.L569,.L637
	.byte	5
	.word	.L232,.L636
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('GetS37JobStatusResult')
	.sect	'.debug_abbrev'
.L380:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('GetS37JobStatusResult')
	.sect	'.debug_line'
.L381:
	.word	.L1142-.L1141
.L1141:
	.half	3
	.word	.L1144-.L1143
.L1143:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1144:
	.byte	5,2,7,0,5,2
	.word	.L232
	.byte	3,253,7,1,5,29,9
	.half	.L1145-.L232
	.byte	1,9
	.half	.L768-.L1145
	.byte	3,1,1,5,1,9
	.half	.L1146-.L768
	.byte	3,1,1,7,9
	.half	.L383-.L1146
	.byte	0,1,1
.L1142:
	.sdecl	'.debug_ranges',debug,cluster('GetS37JobStatusResult')
	.sect	'.debug_ranges'
.L382:
	.word	-1,.L232,0,.L383-.L232,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckProgramIntegrity')
	.sect	'.debug_info'
.L384:
	.word	251
	.half	3
	.word	.L385
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L387,.L386
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckProgramIntegrity',0,1,229,15,7
	.word	.L569
	.byte	1,1,1
	.word	.L266,.L638,.L265
	.byte	4
	.word	.L266,.L638
	.byte	5
	.byte	'ret',0,1,231,15,8
	.word	.L569,.L639
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckProgramIntegrity')
	.sect	'.debug_abbrev'
.L385:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckProgramIntegrity')
	.sect	'.debug_line'
.L386:
	.word	.L1148-.L1147
.L1147:
	.half	3
	.word	.L1150-.L1149
.L1149:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1150:
	.byte	5,12,7,0,5,2
	.word	.L266
	.byte	3,230,15,1,5,8,3,4,1,5,43,9
	.half	.L814-.L266
	.byte	1,5,9,9
	.half	.L1151-.L814
	.byte	3,2,1,5,1,3,10,1,7,9
	.half	.L388-.L1151
	.byte	0,1,1
.L1148:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckProgramIntegrity')
	.sect	'.debug_ranges'
.L387:
	.word	-1,.L266,0,.L388-.L266,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckProgramDependencies')
	.sect	'.debug_info'
.L389:
	.word	260
	.half	3
	.word	.L390
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L392,.L391
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckProgramDependencies',0,1,172,16,15
	.word	.L569
	.byte	1,1,1
	.word	.L270,.L640,.L269
	.byte	4
	.byte	'errorvalue',0,1,172,16,50
	.word	.L565,.L641
	.byte	5
	.word	.L270,.L640
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckProgramDependencies')
	.sect	'.debug_abbrev'
.L390:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckProgramDependencies')
	.sect	'.debug_line'
.L391:
	.word	.L1153-.L1152
.L1152:
	.half	3
	.word	.L1155-.L1154
.L1154:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1155:
	.byte	5,14,7,0,5,2
	.word	.L270
	.byte	3,174,16,1,5,13,1,5,14,9
	.half	.L1156-.L270
	.byte	3,6,1,5,24,9
	.half	.L1157-.L1156
	.byte	1,5,4,9
	.half	.L1158-.L1157
	.byte	1,5,16,7,9
	.half	.L1159-.L1158
	.byte	3,2,1,5,12,9
	.half	.L186-.L1159
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L393-.L186
	.byte	0,1,1
.L1153:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckProgramDependencies')
	.sect	'.debug_ranges'
.L392:
	.word	-1,.L270,0,.L393-.L270,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckSWVerification')
	.sect	'.debug_info'
.L394:
	.word	270
	.half	3
	.word	.L395
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L397,.L396
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckSWVerification',0,1,198,16,15
	.word	.L569
	.byte	1,1,1
	.word	.L274,.L642,.L273
	.byte	4
	.byte	'checkdata',0,1,198,16,45
	.word	.L565,.L643
	.byte	5
	.word	.L274,.L642
	.byte	6
	.byte	'i',0,1,201,16,6
	.word	.L644,.L645
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckSWVerification')
	.sect	'.debug_abbrev'
.L395:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckSWVerification')
	.sect	'.debug_line'
.L396:
	.word	.L1161-.L1160
.L1160:
	.half	3
	.word	.L1163-.L1162
.L1162:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1163:
	.byte	5,15,7,0,5,2
	.word	.L274
	.byte	3,201,16,1,5,14,1,5,7,9
	.half	.L1164-.L274
	.byte	3,2,1,5,19,9
	.half	.L818-.L1164
	.byte	3,124,1,5,33,3,4,1,5,14,9
	.half	.L189-.L818
	.byte	3,2,1,5,20,1,5,3,9
	.half	.L1165-.L189
	.byte	1,5,30,9
	.half	.L1166-.L1165
	.byte	1,5,33,9
	.half	.L1167-.L1166
	.byte	1,5,19,9
	.half	.L1168-.L1167
	.byte	1,5,49,9
	.half	.L1169-.L1168
	.byte	3,10,1,5,29,1,5,19,9
	.half	.L1170-.L1169
	.byte	3,126,1,5,29,9
	.half	.L1171-.L1170
	.byte	3,2,1,5,49,9
	.half	.L1172-.L1171
	.byte	1,5,19,9
	.half	.L1173-.L1172
	.byte	3,127,1,5,35,9
	.half	.L1174-.L1173
	.byte	3,117,1,5,52,9
	.half	.L1175-.L1174
	.byte	3,12,1,5,76,9
	.half	.L1176-.L1175
	.byte	1,5,19,1,5,51,9
	.half	.L1177-.L1176
	.byte	3,1,1,5,19,9
	.half	.L1178-.L1177
	.byte	1,5,33,3,115,1,5,1,7,9
	.half	.L1179-.L1178
	.byte	3,19,1,7,9
	.half	.L398-.L1179
	.byte	0,1,1
.L1161:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckSWVerification')
	.sect	'.debug_ranges'
.L397:
	.word	-1,.L274,0,.L398-.L274,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckProgramCounter')
	.sect	'.debug_info'
.L399:
	.word	231
	.half	3
	.word	.L400
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L402,.L401
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckProgramCounter',0,1,189,16,8
	.word	.L625
	.byte	1,1,1
	.word	.L272,.L646,.L271
	.byte	4
	.word	.L272,.L646
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckProgramCounter')
	.sect	'.debug_abbrev'
.L400:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckProgramCounter')
	.sect	'.debug_line'
.L401:
	.word	.L1181-.L1180
.L1180:
	.half	3
	.word	.L1183-.L1182
.L1182:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1183:
	.byte	5,15,7,0,5,2
	.word	.L272
	.byte	3,193,16,1,5,1,9
	.half	.L1184-.L272
	.byte	3,2,1,7,9
	.half	.L403-.L1184
	.byte	0,1,1
.L1181:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckProgramCounter')
	.sect	'.debug_ranges'
.L402:
	.word	-1,.L272,0,.L403-.L272,0,0
	.sdecl	'.debug_info',debug,cluster('FL_SignVerifFlags')
	.sect	'.debug_info'
.L404:
	.word	226
	.half	3
	.word	.L405
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L407,.L406
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_SignVerifFlags',0,1,224,16,15
	.word	.L569
	.byte	1,1,1
	.word	.L276,.L647,.L275
	.byte	4
	.word	.L276,.L647
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_SignVerifFlags')
	.sect	'.debug_abbrev'
.L405:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_SignVerifFlags')
	.sect	'.debug_line'
.L406:
	.word	.L1186-.L1185
.L1185:
	.half	3
	.word	.L1188-.L1187
.L1187:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1188:
	.byte	5,41,7,0,5,2
	.word	.L276
	.byte	3,226,16,1,5,57,9
	.half	.L1189-.L276
	.byte	1,5,38,9
	.half	.L1190-.L1189
	.byte	1,5,1,3,9,1,7,9
	.half	.L408-.L1190
	.byte	0,1,1
.L1186:
	.sdecl	'.debug_ranges',debug,cluster('FL_SignVerifFlags')
	.sect	'.debug_ranges'
.L407:
	.word	-1,.L276,0,.L408-.L276,0,0
	.sdecl	'.debug_info',debug,cluster('FL_SetRequestStep')
	.sect	'.debug_info'
.L409:
	.word	222
	.half	3
	.word	.L410
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L412,.L411
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_SetRequestStep',0,1,237,16,6,1,1,1
	.word	.L278,.L648,.L277
	.byte	4
	.word	.L278,.L648
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_SetRequestStep')
	.sect	'.debug_abbrev'
.L410:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_SetRequestStep')
	.sect	'.debug_line'
.L411:
	.word	.L1192-.L1191
.L1191:
	.half	3
	.word	.L1194-.L1193
.L1193:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1194:
	.byte	5,5,7,0,5,2
	.word	.L278
	.byte	3,239,16,1,5,37,9
	.half	.L1195-.L278
	.byte	1,5,35,1,5,32,9
	.half	.L1196-.L1195
	.byte	3,1,1,5,1,9
	.half	.L1197-.L1196
	.byte	3,1,1,7,9
	.half	.L413-.L1197
	.byte	0,1,1
.L1192:
	.sdecl	'.debug_ranges',debug,cluster('FL_SetRequestStep')
	.sect	'.debug_ranges'
.L412:
	.word	-1,.L278,0,.L413-.L278,0,0
	.sdecl	'.debug_info',debug,cluster('FL_SetChecksumStep')
	.sect	'.debug_info'
.L414:
	.word	223
	.half	3
	.word	.L415
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L417,.L416
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_SetChecksumStep',0,1,243,16,6,1,1,1
	.word	.L280,.L649,.L279
	.byte	4
	.word	.L280,.L649
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_SetChecksumStep')
	.sect	'.debug_abbrev'
.L415:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_SetChecksumStep')
	.sect	'.debug_line'
.L416:
	.word	.L1199-.L1198
.L1198:
	.half	3
	.word	.L1201-.L1200
.L1200:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1201:
	.byte	5,5,7,0,5,2
	.word	.L280
	.byte	3,245,16,1,5,37,9
	.half	.L1202-.L280
	.byte	1,5,35,1,5,34,9
	.half	.L1203-.L1202
	.byte	3,1,1,5,1,9
	.half	.L1204-.L1203
	.byte	3,1,1,7,9
	.half	.L418-.L1204
	.byte	0,1,1
.L1199:
	.sdecl	'.debug_ranges',debug,cluster('FL_SetChecksumStep')
	.sect	'.debug_ranges'
.L417:
	.word	-1,.L280,0,.L418-.L280,0,0
	.sdecl	'.debug_info',debug,cluster('UpdateSecurityErrorFlag')
	.sect	'.debug_info'
.L419:
	.word	228
	.half	3
	.word	.L420
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L422,.L421
	.byte	2
	.word	.L285
	.byte	3
	.byte	'UpdateSecurityErrorFlag',0,1,249,16,6,1,1,1
	.word	.L282,.L650,.L281
	.byte	4
	.word	.L282,.L650
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('UpdateSecurityErrorFlag')
	.sect	'.debug_abbrev'
.L420:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('UpdateSecurityErrorFlag')
	.sect	'.debug_line'
.L421:
	.word	.L1206-.L1205
.L1205:
	.half	3
	.word	.L1208-.L1207
.L1207:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1208:
	.byte	5,24,7,0,5,2
	.word	.L282
	.byte	3,250,16,1,5,1,9
	.half	.L423-.L282
	.byte	3,2,0,1,1
.L1206:
	.sdecl	'.debug_ranges',debug,cluster('UpdateSecurityErrorFlag')
	.sect	'.debug_ranges'
.L422:
	.word	-1,.L282,0,.L423-.L282,0,0
	.sdecl	'.debug_info',debug,cluster('FL_GetSecErrFlag')
	.sect	'.debug_info'
.L424:
	.word	225
	.half	3
	.word	.L425
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L427,.L426
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_GetSecErrFlag',0,1,254,16,7
	.word	.L569
	.byte	1,1,1
	.word	.L284,.L651,.L283
	.byte	4
	.word	.L284,.L651
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_GetSecErrFlag')
	.sect	'.debug_abbrev'
.L425:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_GetSecErrFlag')
	.sect	'.debug_line'
.L426:
	.word	.L1210-.L1209
.L1209:
	.half	3
	.word	.L1212-.L1211
.L1211:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1212:
	.byte	5,12,7,0,5,2
	.word	.L284
	.byte	3,129,17,1,5,1,9
	.half	.L1213-.L284
	.byte	3,2,1,7,9
	.half	.L428-.L1213
	.byte	0,1,1
.L1210:
	.sdecl	'.debug_ranges',debug,cluster('FL_GetSecErrFlag')
	.sect	'.debug_ranges'
.L427:
	.word	-1,.L284,0,.L428-.L284,0,0
	.sdecl	'.debug_info',debug,cluster('FL_Erasing')
	.sect	'.debug_info'
.L429:
	.word	236
	.half	3
	.word	.L430
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L432,.L431
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_Erasing',0,1,187,10,22
	.word	.L569
	.byte	1,1
	.word	.L248,.L652,.L247
	.byte	4
	.word	.L248,.L652
	.byte	5
	.byte	'ret',0,1,189,10,19
	.word	.L569,.L653
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_Erasing')
	.sect	'.debug_abbrev'
.L430:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_Erasing')
	.sect	'.debug_line'
.L431:
	.word	.L1215-.L1214
.L1214:
	.half	3
	.word	.L1217-.L1216
.L1216:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1217:
	.byte	5,23,7,0,5,2
	.word	.L248
	.byte	3,188,10,1,5,42,9
	.half	.L795-.L248
	.byte	3,3,1,5,13,9
	.half	.L1218-.L795
	.byte	3,6,1,5,38,9
	.half	.L1219-.L1218
	.byte	1,5,13,1,5,36,9
	.half	.L1220-.L1219
	.byte	1,5,37,9
	.half	.L1221-.L1220
	.byte	3,1,1,5,35,1,5,10,9
	.half	.L1222-.L1221
	.byte	3,1,1,5,42,9
	.half	.L1223-.L1222
	.byte	1,5,25,9
	.half	.L1224-.L1223
	.byte	1,5,42,9
	.half	.L1225-.L1224
	.byte	1,5,39,9
	.half	.L1226-.L1225
	.byte	3,4,1,5,9,9
	.half	.L1227-.L1226
	.byte	1,5,13,7,9
	.half	.L1228-.L1227
	.byte	3,3,1,5,44,9
	.half	.L1229-.L1228
	.byte	1,5,42,1,5,48,9
	.half	.L1230-.L1229
	.byte	1,5,17,9
	.half	.L140-.L1230
	.byte	3,4,1,5,5,9
	.half	.L141-.L140
	.byte	3,4,1,5,1,3,1,1,7,9
	.half	.L433-.L141
	.byte	0,1,1
.L1215:
	.sdecl	'.debug_ranges',debug,cluster('FL_Erasing')
	.sect	'.debug_ranges'
.L432:
	.word	-1,.L248,0,.L433-.L248,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckDownloadSegment')
	.sect	'.debug_info'
.L434:
	.word	278
	.half	3
	.word	.L435
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L437,.L436
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckDownloadSegment',0,1,231,10,22
	.word	.L569
	.byte	1,1
	.word	.L250,.L654,.L249
	.byte	4
	.word	.L250,.L654
	.byte	5
	.byte	'ret',0,1,233,10,19
	.word	.L569,.L655
	.byte	5
	.byte	'curSegment',0,1,234,10,26
	.word	.L656,.L657
	.byte	6
	.word	.L658
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckDownloadSegment')
	.sect	'.debug_abbrev'
.L435:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,11,0
	.byte	85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckDownloadSegment')
	.sect	'.debug_line'
.L436:
	.word	.L1232-.L1231
.L1231:
	.half	3
	.word	.L1234-.L1233
.L1233:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1234:
	.byte	5,23,7,0,5,2
	.word	.L250
	.byte	3,232,10,1,5,18,3,8,1,5,34,9
	.half	.L1235-.L250
	.byte	1,5,5,9
	.half	.L1236-.L1235
	.byte	1,5,13,7,9
	.half	.L1237-.L1236
	.byte	3,2,1,5,1,3,48,1,5,46,7,9
	.half	.L143-.L1237
	.byte	3,85,1,5,45,9
	.half	.L1238-.L143
	.byte	1,5,70,9
	.half	.L1239-.L1238
	.byte	1,5,27,9
	.half	.L1240-.L1239
	.byte	3,3,1,5,13,9
	.half	.L1241-.L1240
	.byte	1,5,71,7,9
	.half	.L1242-.L1241
	.byte	3,1,1,5,44,9
	.half	.L1243-.L1242
	.byte	1,5,54,9
	.half	.L1244-.L1243
	.byte	1,5,44,1,5,30,9
	.half	.L1245-.L1244
	.byte	1,5,54,9
	.half	.L1246-.L1245
	.byte	1,5,30,9
	.half	.L1247-.L1246
	.byte	1,5,83,9
	.half	.L1248-.L1247
	.byte	1,5,41,9
	.half	.L1249-.L1248
	.byte	1,5,60,7,9
	.half	.L1250-.L1249
	.byte	3,1,1,5,107,9
	.half	.L1251-.L1250
	.byte	3,1,1,5,42,9
	.half	.L1252-.L1251
	.byte	3,127,1,5,66,9
	.half	.L1253-.L1252
	.byte	3,1,1,5,69,9
	.half	.L1254-.L1253
	.byte	3,127,1,5,13,7,9
	.half	.L1255-.L1254
	.byte	3,7,1,5,79,7,9
	.half	.L1256-.L1255
	.byte	3,2,1,5,32,9
	.half	.L1257-.L1256
	.byte	3,4,1,5,54,9
	.half	.L1258-.L1257
	.byte	1,5,42,9
	.half	.L1259-.L1258
	.byte	1,5,17,9
	.half	.L1260-.L1259
	.byte	3,127,1,5,25,9
	.half	.L1261-.L1260
	.byte	3,3,1,5,13,9
	.half	.L148-.L1261
	.byte	3,5,1,5,81,7,9
	.half	.L1262-.L148
	.byte	3,3,1,5,93,9
	.half	.L1263-.L1262
	.byte	1,5,80,9
	.half	.L796-.L1263
	.byte	3,1,1,5,55,9
	.half	.L1264-.L796
	.byte	3,3,1,5,37,9
	.half	.L1265-.L1264
	.byte	1,5,54,9
	.half	.L1266-.L1265
	.byte	3,1,1,5,36,9
	.half	.L1267-.L1266
	.byte	1,5,1,9
	.half	.L150-.L1267
	.byte	3,10,1,5,17,7,9
	.half	.L145-.L150
	.byte	3,123,1,5,1,3,5,1,7,9
	.half	.L438-.L145
	.byte	0,1,1
.L1232:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckDownloadSegment')
	.sect	'.debug_ranges'
.L437:
	.word	-1,.L250,0,.L438-.L250,0,0
.L658:
	.word	-1,.L250,.L143-.L250,.L150-.L250,.L145-.L250,.L654-.L250,0,0
	.sdecl	'.debug_info',debug,cluster('FL_DownloadRemainData')
	.sect	'.debug_info'
.L439:
	.word	247
	.half	3
	.word	.L440
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L442,.L441
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_DownloadRemainData',0,1,177,11,15
	.word	.L569
	.byte	1,1
	.word	.L252,.L659,.L251
	.byte	4
	.word	.L252,.L659
	.byte	5
	.byte	'ret',0,1,179,11,19
	.word	.L569,.L660
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_DownloadRemainData')
	.sect	'.debug_abbrev'
.L440:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_DownloadRemainData')
	.sect	'.debug_line'
.L441:
	.word	.L1269-.L1268
.L1268:
	.half	3
	.word	.L1271-.L1270
.L1270:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1271:
	.byte	5,30,7,0,5,2
	.word	.L252
	.byte	3,181,11,1,5,5,9
	.half	.L1272-.L252
	.byte	1,5,49,9
	.half	.L1273-.L1272
	.byte	1,5,28,9
	.half	.L1274-.L1273
	.byte	1,5,13,9
	.half	.L1275-.L1274
	.byte	3,4,1,5,18,3,126,1,5,50,9
	.half	.L1276-.L1275
	.byte	3,2,1,5,23,9
	.half	.L1277-.L1276
	.byte	3,121,1,5,18,3,5,1,5,31,9
	.half	.L797-.L1277
	.byte	1,5,9,9
	.half	.L1278-.L797
	.byte	3,1,1,5,29,9
	.half	.L1279-.L1278
	.byte	3,1,1,5,25,9
	.half	.L1280-.L1279
	.byte	3,3,1,5,27,9
	.half	.L1281-.L1280
	.byte	3,1,1,5,40,9
	.half	.L1282-.L1281
	.byte	3,1,1,5,38,1,5,7,9
	.half	.L1283-.L1282
	.byte	3,3,1,5,39,9
	.half	.L1284-.L1283
	.byte	1,5,22,9
	.half	.L1285-.L1284
	.byte	1,5,39,9
	.half	.L1286-.L1285
	.byte	1,5,23,9
	.half	.L1287-.L1286
	.byte	3,4,1,5,13,9
	.half	.L1288-.L1287
	.byte	3,2,1,5,1,3,4,1,7,9
	.half	.L443-.L1288
	.byte	0,1,1
.L1269:
	.sdecl	'.debug_ranges',debug,cluster('FL_DownloadRemainData')
	.sect	'.debug_ranges'
.L442:
	.word	-1,.L252,0,.L443-.L252,0,0
	.sdecl	'.debug_info',debug,cluster('FL_HandleRemainData')
	.sect	'.debug_info'
.L444:
	.word	245
	.half	3
	.word	.L445
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L447,.L446
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_HandleRemainData',0,1,217,11,15
	.word	.L569
	.byte	1,1
	.word	.L254,.L661,.L253
	.byte	4
	.word	.L254,.L661
	.byte	5
	.byte	'ret',0,1,219,11,19
	.word	.L569,.L662
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_HandleRemainData')
	.sect	'.debug_abbrev'
.L445:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_HandleRemainData')
	.sect	'.debug_line'
.L446:
	.word	.L1290-.L1289
.L1289:
	.half	3
	.word	.L1292-.L1291
.L1291:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1292:
	.byte	5,23,7,0,5,2
	.word	.L254
	.byte	3,218,11,1,5,9,3,2,1,5,28,9
	.half	.L1293-.L254
	.byte	1,5,5,9
	.half	.L1294-.L1293
	.byte	1,5,17,7,9
	.half	.L1295-.L1294
	.byte	3,3,1,5,33,9
	.half	.L1296-.L1295
	.byte	1,9
	.half	.L1297-.L1296
	.byte	3,127,1,5,44,9
	.half	.L1298-.L1297
	.byte	3,1,1,5,45,9
	.half	.L1299-.L1298
	.byte	3,127,1,5,9,9
	.half	.L1300-.L1299
	.byte	1,5,44,7,9
	.half	.L1301-.L1300
	.byte	3,6,1,5,26,3,126,1,5,17,9
	.half	.L1302-.L1301
	.byte	3,1,1,5,26,3,127,1,5,39,9
	.half	.L1303-.L1302
	.byte	1,5,79,9
	.half	.L1304-.L1303
	.byte	3,2,1,5,64,9
	.half	.L1305-.L1304
	.byte	3,1,1,5,96,9
	.half	.L1306-.L1305
	.byte	1,5,75,9
	.half	.L1307-.L1306
	.byte	1,5,46,1,5,112,9
	.half	.L1308-.L1307
	.byte	3,127,1,5,40,9
	.half	.L156-.L1308
	.byte	3,5,1,5,17,9
	.half	.L799-.L156
	.byte	1,9
	.half	.L155-.L799
	.byte	3,118,1,5,59,9
	.half	.L1309-.L155
	.byte	3,20,1,5,12,9
	.half	.L1310-.L1309
	.byte	3,3,1,5,21,3,127,1,5,70,9
	.half	.L1311-.L1310
	.byte	3,126,1,5,41,1,5,20,9
	.half	.L1312-.L1311
	.byte	3,4,1,5,21,3,126,1,5,20,9
	.half	.L1313-.L1312
	.byte	3,2,1,5,5,9
	.half	.L157-.L1313
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L448-.L157
	.byte	0,1,1
.L1290:
	.sdecl	'.debug_ranges',debug,cluster('FL_HandleRemainData')
	.sect	'.debug_ranges'
.L447:
	.word	-1,.L254,0,.L448-.L254,0,0
	.sdecl	'.debug_info',debug,cluster('FL_ProgrammingData')
	.sect	'.debug_info'
.L449:
	.word	244
	.half	3
	.word	.L450
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L452,.L451
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_ProgrammingData',0,1,136,12,22
	.word	.L569
	.byte	1,1
	.word	.L256,.L663,.L255
	.byte	4
	.word	.L256,.L663
	.byte	5
	.byte	'ret',0,1,138,12,19
	.word	.L569,.L664
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_ProgrammingData')
	.sect	'.debug_abbrev'
.L450:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_ProgrammingData')
	.sect	'.debug_line'
.L451:
	.word	.L1315-.L1314
.L1314:
	.half	3
	.word	.L1317-.L1316
.L1316:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1317:
	.byte	5,23,7,0,5,2
	.word	.L256
	.byte	3,137,12,1,5,14,3,6,1,5,51,9
	.half	.L801-.L256
	.byte	3,125,1,5,14,3,3,1,5,22,9
	.half	.L1318-.L801
	.byte	3,120,1,5,14,9
	.half	.L800-.L1318
	.byte	3,8,1,5,51,9
	.half	.L1319-.L800
	.byte	3,125,1,5,17,9
	.half	.L160-.L1319
	.byte	3,7,1,5,33,9
	.half	.L1320-.L160
	.byte	1,5,40,9
	.half	.L1321-.L1320
	.byte	3,127,1,5,59,9
	.half	.L1322-.L1321
	.byte	1,5,26,9
	.half	.L1323-.L1322
	.byte	1,5,39,9
	.half	.L1324-.L1323
	.byte	1,5,29,9
	.half	.L1325-.L1324
	.byte	3,24,1,5,54,9
	.half	.L1326-.L1325
	.byte	3,115,1,5,69,9
	.half	.L1327-.L1326
	.byte	3,114,1,5,14,9
	.half	.L1328-.L1327
	.byte	1,5,30,9
	.half	.L1329-.L1328
	.byte	1,5,9,9
	.half	.L1330-.L1329
	.byte	1,5,37,7,9
	.half	.L1331-.L1330
	.byte	3,5,1,5,29,9
	.half	.L1332-.L1331
	.byte	3,3,1,5,79,9
	.half	.L1333-.L1332
	.byte	1,5,29,9
	.half	.L1334-.L1333
	.byte	1,5,25,3,12,1,5,29,9
	.half	.L1335-.L1334
	.byte	3,116,1,5,39,1,5,48,9
	.half	.L1336-.L1335
	.byte	3,3,1,5,13,3,3,1,9
	.half	.L1337-.L1336
	.byte	3,125,1,9
	.half	.L1338-.L1337
	.byte	3,3,1,5,29,9
	.half	.L1339-.L1338
	.byte	3,125,1,5,54,9
	.half	.L1340-.L1339
	.byte	3,3,1,5,17,9
	.half	.L1341-.L1340
	.byte	3,1,1,5,36,3,127,1,5,35,9
	.half	.L1342-.L1341
	.byte	3,2,1,5,33,9
	.half	.L1343-.L1342
	.byte	3,1,1,5,25,9
	.half	.L1344-.L1343
	.byte	3,3,1,5,13,9
	.half	.L1345-.L1344
	.byte	1,5,6,7,9
	.half	.L1346-.L1345
	.byte	3,2,1,5,21,9
	.half	.L1347-.L1346
	.byte	1,5,38,9
	.half	.L1348-.L1347
	.byte	1,5,29,9
	.half	.L162-.L1348
	.byte	3,4,1,5,80,9
	.half	.L1349-.L162
	.byte	1,5,29,9
	.half	.L1350-.L1349
	.byte	1,5,56,9
	.half	.L1351-.L1350
	.byte	3,1,1,5,29,9
	.half	.L1352-.L1351
	.byte	3,127,1,5,40,9
	.half	.L1353-.L1352
	.byte	1,5,29,9
	.half	.L1354-.L1353
	.byte	3,1,1,5,37,9
	.half	.L1355-.L1354
	.byte	1,5,46,9
	.half	.L1356-.L1355
	.byte	3,2,1,5,31,9
	.half	.L1357-.L1356
	.byte	3,2,1,5,13,9
	.half	.L1358-.L1357
	.byte	1,5,21,7,9
	.half	.L1359-.L1358
	.byte	3,2,1,5,13,3,126,1,5,25,9
	.half	.L161-.L1359
	.byte	3,10,1,5,62,9
	.half	.L1360-.L161
	.byte	3,1,1,5,94,9
	.half	.L1361-.L1360
	.byte	1,5,62,9
	.half	.L1362-.L1361
	.byte	1,5,73,9
	.half	.L1363-.L1362
	.byte	1,5,44,1,5,49,9
	.half	.L1364-.L1363
	.byte	3,1,1,5,46,9
	.half	.L1365-.L1364
	.byte	1,5,40,9
	.half	.L1366-.L1365
	.byte	3,2,1,5,29,9
	.half	.L1367-.L1366
	.byte	3,1,1,5,37,9
	.half	.L1368-.L1367
	.byte	1,5,29,9
	.half	.L1369-.L1368
	.byte	3,2,1,5,13,9
	.half	.L159-.L1369
	.byte	3,77,1,5,12,9
	.half	.L1370-.L159
	.byte	1,5,44,7,9
	.half	.L1371-.L1370
	.byte	1,5,5,7,9
	.half	.L165-.L1371
	.byte	3,55,1,5,1,3,1,1,7,9
	.half	.L453-.L165
	.byte	0,1,1
.L1315:
	.sdecl	'.debug_ranges',debug,cluster('FL_ProgrammingData')
	.sect	'.debug_ranges'
.L452:
	.word	-1,.L256,0,.L453-.L256,0,0
	.sdecl	'.debug_info',debug,cluster('FL_Programming')
	.sect	'.debug_info'
.L454:
	.word	240
	.half	3
	.word	.L455
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L457,.L456
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_Programming',0,1,210,12,22
	.word	.L569
	.byte	1,1
	.word	.L258,.L665,.L257
	.byte	4
	.word	.L258,.L665
	.byte	5
	.byte	'ret',0,1,212,12,19
	.word	.L569,.L666
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_Programming')
	.sect	'.debug_abbrev'
.L455:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_Programming')
	.sect	'.debug_line'
.L456:
	.word	.L1373-.L1372
.L1372:
	.half	3
	.word	.L1375-.L1374
.L1374:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1375:
	.byte	5,29,7,0,5,2
	.word	.L258
	.byte	3,214,12,1,5,10,9
	.half	.L802-.L258
	.byte	3,3,1,5,26,9
	.half	.L1376-.L802
	.byte	1,5,9,9
	.half	.L1377-.L1376
	.byte	1,5,16,7,9
	.half	.L1378-.L1377
	.byte	3,1,1,5,41,7,9
	.half	.L1379-.L1378
	.byte	3,4,1,5,39,1,5,1,9
	.half	.L167-.L1379
	.byte	3,3,1,7,9
	.half	.L458-.L167
	.byte	0,1,1
.L1373:
	.sdecl	'.debug_ranges',debug,cluster('FL_Programming')
	.sect	'.debug_ranges'
.L457:
	.word	-1,.L258,0,.L458-.L258,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckSuming')
	.sect	'.debug_info'
.L459:
	.word	313
	.half	3
	.word	.L460
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L462,.L461
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckSuming',0,1,211,13,22
	.word	.L569
	.byte	1,1
	.word	.L262,.L667,.L261
	.byte	4
	.word	.L262,.L667
	.byte	5
	.byte	'ret',0,1,213,13,19
	.word	.L569,.L668
	.byte	5
	.byte	'checkStatus',0,1,214,13,19
	.word	.L569,.L669
	.byte	5
	.byte	'verifyParam',0,1,215,13,26
	.word	.L622,.L670
	.byte	5
	.byte	'failcount',0,1,218,13,11
	.word	.L569,.L671
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckSuming')
	.sect	'.debug_abbrev'
.L460:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckSuming')
	.sect	'.debug_line'
.L461:
	.word	.L1381-.L1380
.L1380:
	.half	3
	.word	.L1383-.L1382
.L1382:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1383:
	.byte	5,5,7,0,5,2
	.word	.L262
	.byte	3,242,13,1,5,20,9
	.half	.L1384-.L262
	.byte	3,103,1,5,40,3,4,1,5,36,9
	.half	.L807-.L1384
	.byte	3,21,1,5,22,9
	.half	.L1385-.L807
	.byte	3,96,1,5,6,9
	.half	.L803-.L1385
	.byte	3,11,1,5,25,7,9
	.half	.L1386-.L803
	.byte	3,5,1,5,14,9
	.half	.L1387-.L1386
	.byte	1,5,48,7,9
	.half	.L1388-.L1387
	.byte	3,2,1,5,18,9
	.half	.L1389-.L1388
	.byte	1,5,56,7,9
	.half	.L1390-.L1389
	.byte	3,2,1,5,58,9
	.half	.L804-.L1390
	.byte	1,5,34,9
	.half	.L173-.L804
	.byte	3,4,1,5,50,9
	.half	.L172-.L173
	.byte	3,8,1,5,49,1,5,14,9
	.half	.L1391-.L172
	.byte	3,1,1,5,33,9
	.half	.L1392-.L1391
	.byte	1,5,31,1,5,8,9
	.half	.L805-.L1392
	.byte	3,2,1,5,17,7,9
	.half	.L806-.L805
	.byte	3,2,1,5,24,9
	.half	.L1393-.L806
	.byte	1,5,36,9
	.half	.L1394-.L1393
	.byte	3,2,1,5,7,9
	.half	.L1395-.L1394
	.byte	1,5,8,7,9
	.half	.L1396-.L1395
	.byte	3,3,1,5,39,9
	.half	.L1397-.L1396
	.byte	1,5,23,9
	.half	.L1398-.L1397
	.byte	1,5,39,9
	.half	.L1399-.L1398
	.byte	1,5,26,9
	.half	.L1400-.L1399
	.byte	3,2,1,5,8,9
	.half	.L1401-.L1400
	.byte	1,5,44,7,9
	.half	.L1402-.L1401
	.byte	3,7,1,5,42,1,5,48,9
	.half	.L1403-.L1402
	.byte	1,5,8,9
	.half	.L176-.L1403
	.byte	3,6,1,5,43,9
	.half	.L1404-.L176
	.byte	1,5,41,1,5,20,9
	.half	.L1405-.L1404
	.byte	3,4,1,5,44,9
	.half	.L1406-.L1405
	.byte	3,127,1,5,7,9
	.half	.L1407-.L1406
	.byte	3,122,1,9
	.half	.L175-.L1407
	.byte	3,13,1,5,42,9
	.half	.L1408-.L175
	.byte	1,5,40,1,5,19,9
	.half	.L1409-.L1408
	.byte	3,4,1,5,43,9
	.half	.L1410-.L1409
	.byte	3,127,1,5,16,9
	.half	.L177-.L1410
	.byte	3,3,1,5,6,3,6,1,5,52,9
	.half	.L171-.L177
	.byte	3,8,1,5,51,1,5,23,9
	.half	.L178-.L171
	.byte	3,3,1,5,15,9
	.half	.L808-.L178
	.byte	3,4,1,5,16,9
	.half	.L1411-.L808
	.byte	3,3,1,5,36,9
	.half	.L1412-.L1411
	.byte	3,9,1,5,34,1,5,1,9
	.half	.L1413-.L1412
	.byte	3,2,1,7,9
	.half	.L463-.L1413
	.byte	0,1,1
.L1381:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckSuming')
	.sect	'.debug_ranges'
.L462:
	.word	-1,.L262,0,.L463-.L262,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckSuming_Hash')
	.sect	'.debug_info'
.L464:
	.word	341
	.half	3
	.word	.L465
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L467,.L466
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckSuming_Hash',0,1,198,15,22
	.word	.L569
	.byte	1,1
	.word	.L264,.L672,.L263
	.byte	4
	.byte	'index',0,1,198,15,48
	.word	.L569,.L673
	.byte	5
	.word	.L264,.L672
	.byte	6
	.byte	'address',0,1,200,15,12
	.word	.L561,.L674
	.byte	6
	.byte	'length',0,1,201,15,12
	.word	.L561,.L675
	.byte	6
	.byte	'checkStatus',0,1,202,15,20
	.word	.L569,.L676
	.byte	6
	.byte	'l_nrOfSegments',0,1,203,15,12
	.word	.L625,.L677
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckSuming_Hash')
	.sect	'.debug_abbrev'
.L465:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckSuming_Hash')
	.sect	'.debug_line'
.L466:
	.word	.L1415-.L1414
.L1414:
	.half	3
	.word	.L1417-.L1416
.L1416:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1417:
	.byte	5,22,7,0,5,2
	.word	.L264
	.byte	3,204,15,1,5,57,9
	.half	.L809-.L264
	.byte	1,5,60,9
	.half	.L811-.L809
	.byte	3,5,1,5,62,9
	.half	.L1418-.L811
	.byte	3,124,1,5,82,9
	.half	.L1419-.L1418
	.byte	1,5,81,9
	.half	.L810-.L1419
	.byte	3,1,1,5,60,9
	.half	.L813-.L810
	.byte	3,3,1,5,13,9
	.half	.L812-.L813
	.byte	3,1,1,5,1,3,1,1,7,9
	.half	.L468-.L812
	.byte	0,1,1
.L1415:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckSuming_Hash')
	.sect	'.debug_ranges'
.L467:
	.word	-1,.L264,0,.L468-.L264,0,0
	.sdecl	'.debug_info',debug,cluster('FL_UpdateNvm')
	.sect	'.debug_info'
.L469:
	.word	256
	.half	3
	.word	.L470
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L472,.L471
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_UpdateNvm',0,1,245,9,22
	.word	.L569
	.byte	1,1
	.word	.L244,.L678,.L243
	.byte	4
	.word	.L679
	.byte	5
	.byte	'ret',0,1,247,9,19
	.word	.L569,.L680
	.byte	5
	.byte	'crcParam',0,1,249,9,23
	.word	.L681,.L682
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_UpdateNvm')
	.sect	'.debug_abbrev'
.L470:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_UpdateNvm')
	.sect	'.debug_line'
.L471:
	.word	.L1421-.L1420
.L1420:
	.half	3
	.word	.L1423-.L1422
.L1422:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1423:
	.byte	5,23,7,0,5,2
	.word	.L244
	.byte	3,246,9,1,5,22,3,126,1,5,25,9
	.half	.L792-.L244
	.byte	3,7,1,5,23,1,5,48,9
	.half	.L793-.L792
	.byte	3,1,1,5,30,9
	.half	.L794-.L793
	.byte	1,5,52,9
	.half	.L1424-.L794
	.byte	3,1,1,5,27,1,5,28,9
	.half	.L1425-.L1424
	.byte	3,3,1,5,25,9
	.half	.L1426-.L1425
	.byte	3,1,1,5,23,1,5,28,9
	.half	.L1427-.L1426
	.byte	3,1,1,5,25,9
	.half	.L1428-.L1427
	.byte	3,1,1,5,23,1,5,28,9
	.half	.L1429-.L1428
	.byte	3,1,1,5,39,9
	.half	.L1430-.L1429
	.byte	3,3,1,5,29,9
	.half	.L1431-.L1430
	.byte	1,5,5,9
	.half	.L1432-.L1431
	.byte	3,3,1,5,25,9
	.half	.L1433-.L1432
	.byte	1,5,30,9
	.half	.L1434-.L1433
	.byte	3,1,1,5,28,1,5,19,9
	.half	.L1435-.L1434
	.byte	3,1,1,5,52,9
	.half	.L1436-.L1435
	.byte	3,10,1,5,5,9
	.half	.L1437-.L1436
	.byte	1,5,20,9
	.half	.L1438-.L1437
	.byte	1,5,37,9
	.half	.L1439-.L1438
	.byte	1,5,35,9
	.half	.L1440-.L1439
	.byte	3,4,1,5,5,9
	.half	.L1441-.L1440
	.byte	1,5,56,7,9
	.half	.L1442-.L1441
	.byte	3,9,1,5,41,9
	.half	.L1443-.L1442
	.byte	1,5,9,9
	.half	.L1444-.L1443
	.byte	1,5,24,9
	.half	.L1445-.L1444
	.byte	1,5,41,9
	.half	.L1446-.L1445
	.byte	1,5,35,9
	.half	.L137-.L1446
	.byte	3,3,1,5,13,9
	.half	.L1447-.L137
	.byte	3,2,1,5,1,3,4,1,7,9
	.half	.L473-.L1447
	.byte	0,1,1
.L1421:
	.sdecl	'.debug_ranges',debug,cluster('FL_UpdateNvm')
	.sect	'.debug_ranges'
.L472:
	.word	-1,.L244,0,.L473-.L244,0,0
.L679:
	.word	-1,.L244,0,.L678-.L244,-1,.L246,0,.L508-.L246,0,0
	.sdecl	'.debug_info',debug,cluster('FL_CheckCompatibility')
	.sect	'.debug_info'
.L474:
	.word	269
	.half	3
	.word	.L475
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L477,.L476
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_CheckCompatibility',0,1,140,16,22
	.word	.L569
	.byte	1,1
	.word	.L268,.L683,.L267
	.byte	4
	.word	.L268,.L683
	.byte	5
	.byte	'ret',0,1,142,16,16
	.word	.L569,.L684
	.byte	5
	.byte	'destdata',0,1,144,16,11
	.word	.L685,.L686
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_CheckCompatibility')
	.sect	'.debug_abbrev'
.L475:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_CheckCompatibility')
	.sect	'.debug_line'
.L476:
	.word	.L1449-.L1448
.L1448:
	.half	3
	.word	.L1451-.L1450
.L1450:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1451:
	.byte	5,27,7,0,5,2
	.word	.L268
	.byte	3,145,16,1,5,22,3,122,1,5,27,9
	.half	.L815-.L268
	.byte	3,6,1,5,18,1,5,51,9
	.half	.L1452-.L815
	.byte	1,5,5,9
	.half	.L1453-.L1452
	.byte	3,1,1,5,24,9
	.half	.L1454-.L1453
	.byte	1,5,22,1,5,5,9
	.half	.L816-.L1454
	.byte	3,18,1,5,28,9
	.half	.L817-.L816
	.byte	1,5,27,1,5,26,9
	.half	.L1455-.L817
	.byte	3,1,1,5,20,9
	.half	.L1456-.L1455
	.byte	3,1,1,5,1,7,9
	.half	.L478-.L1456
	.byte	3,3,0,1,1
.L1449:
	.sdecl	'.debug_ranges',debug,cluster('FL_CheckCompatibility')
	.sect	'.debug_ranges'
.L477:
	.word	-1,.L268,0,.L478-.L268,0,0
	.sdecl	'.debug_info',debug,cluster('FL_KeepNvmInforNoUsed')
	.sect	'.debug_info'
.L479:
	.word	229
	.half	3
	.word	.L480
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L482,.L481
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_KeepNvmInforNoUsed',0,1,235,1,8
	.word	.L625
	.byte	1,1
	.word	.L194,.L687,.L193
	.byte	4
	.word	.L194,.L687
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_KeepNvmInforNoUsed')
	.sect	'.debug_abbrev'
.L480:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_KeepNvmInforNoUsed')
	.sect	'.debug_line'
.L481:
	.word	.L1458-.L1457
.L1457:
	.half	3
	.word	.L1460-.L1459
.L1459:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1460:
	.byte	5,12,7,0,5,2
	.word	.L194
	.byte	3,244,1,1,5,1,3,1,1,7,9
	.half	.L483-.L194
	.byte	0,1,1
.L1458:
	.sdecl	'.debug_ranges',debug,cluster('FL_KeepNvmInforNoUsed')
	.sect	'.debug_ranges'
.L482:
	.word	-1,.L194,0,.L483-.L194,0,0
	.sdecl	'.debug_info',debug,cluster('FL_ReadConstDIDData')
	.sect	'.debug_info'
.L484:
	.word	304
	.half	3
	.word	.L485
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L487,.L486
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_ReadConstDIDData',0,1,169,2,6,1,1,1
	.word	.L200,.L688,.L199
	.byte	4
	.byte	'readData',0,1,169,2,33
	.word	.L565,.L689
	.byte	4
	.byte	'Addr',0,1,169,2,50
	.word	.L561,.L690
	.byte	4
	.byte	'length',0,1,169,2,62
	.word	.L569,.L691
	.byte	5
	.word	.L200,.L688
	.byte	6
	.byte	'index',0,1,171,2,8
	.word	.L569,.L692
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_ReadConstDIDData')
	.sect	'.debug_abbrev'
.L485:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_ReadConstDIDData')
	.sect	'.debug_line'
.L486:
	.word	.L1462-.L1461
.L1461:
	.half	3
	.word	.L1464-.L1463
.L1463:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1464:
	.byte	5,13,7,0,5,2
	.word	.L200
	.byte	3,172,2,1,5,32,1,5,18,9
	.half	.L4-.L200
	.byte	3,3,1,5,39,9
	.half	.L707-.L4
	.byte	3,125,1,5,17,9
	.half	.L706-.L707
	.byte	3,3,1,5,39,9
	.half	.L1465-.L706
	.byte	3,125,1,5,19,3,4,1,5,13,9
	.half	.L708-.L1465
	.byte	3,1,1,5,32,9
	.half	.L3-.L708
	.byte	3,123,1,5,1,7,9
	.half	.L1466-.L3
	.byte	3,9,1,7,9
	.half	.L488-.L1466
	.byte	0,1,1
.L1462:
	.sdecl	'.debug_ranges',debug,cluster('FL_ReadConstDIDData')
	.sect	'.debug_ranges'
.L487:
	.word	-1,.L200,0,.L488-.L200,0,0
	.sdecl	'.debug_info',debug,cluster('FL_Get4Byte')
	.sect	'.debug_info'
.L489:
	.word	237
	.half	3
	.word	.L490
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L492,.L491
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FL_Get4Byte',0,1,134,9,15
	.word	.L561
	.byte	1,1
	.word	.L238,.L693,.L237
	.byte	4
	.byte	'data',0,1,134,9,40
	.word	.L601,.L694
	.byte	5
	.word	.L238,.L693
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FL_Get4Byte')
	.sect	'.debug_abbrev'
.L490:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FL_Get4Byte')
	.sect	'.debug_line'
.L491:
	.word	.L1468-.L1467
.L1467:
	.half	3
	.word	.L1470-.L1469
.L1469:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1470:
	.byte	5,28,7,0,5,2
	.word	.L238
	.byte	3,137,9,1,5,33,9
	.half	.L1471-.L238
	.byte	1,5,29,3,1,1,5,34,9
	.half	.L1472-.L1471
	.byte	1,5,13,9
	.half	.L1473-.L1472
	.byte	1,5,29,3,1,1,5,34,9
	.half	.L1474-.L1473
	.byte	1,5,13,9
	.half	.L1475-.L1474
	.byte	1,5,28,3,1,1,5,13,9
	.half	.L1476-.L1475
	.byte	1,5,1,3,3,1,7,9
	.half	.L493-.L1476
	.byte	0,1,1
.L1468:
	.sdecl	'.debug_ranges',debug,cluster('FL_Get4Byte')
	.sect	'.debug_ranges'
.L492:
	.word	-1,.L238,0,.L493-.L238,0,0
	.sdecl	'.debug_info',debug,cluster('FBL_GetSignVerifFlag')
	.sect	'.debug_info'
.L494:
	.word	248
	.half	3
	.word	.L495
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L497,.L496
	.byte	2
	.word	.L285
	.byte	3
	.byte	'FBL_GetSignVerifFlag',0,1,207,13,7
	.word	.L569
	.byte	1,1,1
	.word	.L260,.L695,.L259
	.byte	4
	.byte	'index',0,1,207,13,34
	.word	.L569,.L696
	.byte	5
	.word	.L260,.L695
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FBL_GetSignVerifFlag')
	.sect	'.debug_abbrev'
.L495:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FBL_GetSignVerifFlag')
	.sect	'.debug_line'
.L496:
	.word	.L1478-.L1477
.L1477:
	.half	3
	.word	.L1480-.L1479
.L1479:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1480:
	.byte	5,12,7,0,5,2
	.word	.L260
	.byte	3,208,13,1,5,30,9
	.half	.L1481-.L260
	.byte	1,5,1,9
	.half	.L1482-.L1481
	.byte	3,1,1,7,9
	.half	.L498-.L1482
	.byte	0,1,1
.L1478:
	.sdecl	'.debug_ranges',debug,cluster('FBL_GetSignVerifFlag')
	.sect	'.debug_ranges'
.L497:
	.word	-1,.L260,0,.L498-.L260,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L499:
	.word	203
	.half	3
	.word	.L500
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L502,.L501
	.byte	2
	.word	.L285
	.byte	3
	.byte	'.cocofun_1',0,1,135,2,6,1
	.word	.L198,.L503,.L197
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L500:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L501:
	.word	.L1484-.L1483
.L1483:
	.half	3
	.word	.L1486-.L1485
.L1485:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1486:
	.byte	5,5,7,0,5,2
	.word	.L198
	.byte	3,144,2,1,9
	.half	.L503-.L198
	.byte	0,1,1,5,29,0,5,2
	.word	.L198
	.byte	3,189,3,1,5,5,9
	.half	.L1487-.L198
	.byte	3,211,126,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,18,0,5,2
	.word	.L198
	.byte	3,150,4,1,5,5,9
	.half	.L1487-.L198
	.byte	3,250,125,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,30,0,5,2
	.word	.L198
	.byte	3,138,6,1,5,5,9
	.half	.L1487-.L198
	.byte	3,134,124,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,2,0,5,2
	.word	.L198
	.byte	3,242,6,1,5,5,9
	.half	.L1487-.L198
	.byte	3,158,123,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,2,0,5,2
	.word	.L198
	.byte	3,134,7,1,5,5,9
	.half	.L1487-.L198
	.byte	3,138,123,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,34,0,5,2
	.word	.L198
	.byte	3,157,7,1,5,5,9
	.half	.L1487-.L198
	.byte	3,243,122,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,24,0,5,2
	.word	.L198
	.byte	3,220,7,1,5,5,9
	.half	.L1487-.L198
	.byte	3,180,122,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,2,0,5,2
	.word	.L198
	.byte	3,253,7,1,5,5,9
	.half	.L1487-.L198
	.byte	3,147,122,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,13,0,5,2
	.word	.L198
	.byte	3,190,9,1,5,5,9
	.half	.L1487-.L198
	.byte	3,210,120,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,18,0,5,2
	.word	.L198
	.byte	3,240,10,1,5,5,9
	.half	.L1487-.L198
	.byte	3,160,119,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,10,0,5,2
	.word	.L198
	.byte	3,217,12,1,5,5,9
	.half	.L1487-.L198
	.byte	3,183,117,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,22,0,5,2
	.word	.L198
	.byte	3,204,15,1,5,5,9
	.half	.L1487-.L198
	.byte	3,196,114,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,5,0,5,2
	.word	.L198
	.byte	3,239,16,1,9
	.half	.L1487-.L198
	.byte	3,161,113,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1,5,5,0,5,2
	.word	.L198
	.byte	3,245,16,1,9
	.half	.L1487-.L198
	.byte	3,155,113,1,7,9
	.half	.L503-.L1487
	.byte	0,1,1
.L1484:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L502:
	.word	-1,.L198,0,.L503-.L198,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L504:
	.word	203
	.half	3
	.word	.L505
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L507,.L506
	.byte	2
	.word	.L285
	.byte	3
	.byte	'.cocofun_2',0,1,245,9,22,1
	.word	.L246,.L508,.L245
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L505:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L506:
	.word	.L1489-.L1488
.L1488:
	.half	3
	.word	.L1491-.L1490
.L1490:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1491:
	.byte	5,48,7,0,5,2
	.word	.L246
	.byte	3,252,9,1,9
	.half	.L508-.L246
	.byte	0,1,1,5,8,0,5,2
	.word	.L246
	.byte	3,139,14,1,5,48,9
	.half	.L1492-.L246
	.byte	3,241,123,1,7,9
	.half	.L508-.L1492
	.byte	0,1,1,5,7,0,5,2
	.word	.L246
	.byte	3,149,14,1,5,48,9
	.half	.L1492-.L246
	.byte	3,231,123,1,7,9
	.half	.L508-.L1492
	.byte	0,1,1,5,5,0,5,2
	.word	.L246
	.byte	3,164,16,1,5,48,9
	.half	.L1492-.L246
	.byte	3,216,121,1,7,9
	.half	.L508-.L1492
	.byte	0,1,1
.L1489:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L507:
	.word	-1,.L246,0,.L508-.L246,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L509:
	.word	203
	.half	3
	.word	.L510
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L512,.L511
	.byte	2
	.word	.L285
	.byte	3
	.byte	'.cocofun_3',0,1,209,2,8,1
	.word	.L204,.L513,.L203
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L510:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L511:
	.word	.L1494-.L1493
.L1493:
	.half	3
	.word	.L1496-.L1495
.L1495:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1496:
	.byte	5,25,7,0,5,2
	.word	.L204
	.byte	3,221,2,1,9
	.half	.L513-.L204
	.byte	0,1,1,5,19,0,5,2
	.word	.L204
	.byte	3,149,8,1,5,25,9
	.half	.L1497-.L204
	.byte	3,200,122,1,7,9
	.half	.L513-.L1497
	.byte	0,1,1,5,27,0,5,2
	.word	.L204
	.byte	3,160,9,1,5,25,9
	.half	.L1497-.L204
	.byte	3,189,121,1,7,9
	.half	.L513-.L1497
	.byte	0,1,1
.L1494:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L512:
	.word	-1,.L204,0,.L513-.L204,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_4')
	.sect	'.debug_info'
.L514:
	.word	203
	.half	3
	.word	.L515
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L517,.L516
	.byte	2
	.word	.L285
	.byte	3
	.byte	'.cocofun_4',0,1,129,6,15,1
	.word	.L220,.L518,.L219
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_4')
	.sect	'.debug_abbrev'
.L515:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_4')
	.sect	'.debug_line'
.L516:
	.word	.L1499-.L1498
.L1498:
	.half	3
	.word	.L1501-.L1500
.L1500:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\FL.c',0,0,0,0,0
.L1501:
	.byte	5,42,7,0,5,2
	.word	.L220
	.byte	3,193,6,1,5,40,1,9
	.half	.L518-.L220
	.byte	0,1,1,5,31,0,5,2
	.word	.L220
	.byte	3,243,6,1,5,29,1,5,40,9
	.half	.L763-.L220
	.byte	3,78,1,7,9
	.half	.L518-.L763
	.byte	0,1,1,5,31,0,5,2
	.word	.L220
	.byte	3,135,7,1,5,29,1,5,40,9
	.half	.L763-.L220
	.byte	3,186,127,1,7,9
	.half	.L518-.L763
	.byte	0,1,1,5,29,0,5,2
	.word	.L220
	.byte	3,253,7,1,5,28,1,5,40,9
	.half	.L763-.L220
	.byte	3,196,126,1,7,9
	.half	.L518-.L763
	.byte	0,1,1,5,34,0,5,2
	.word	.L220
	.byte	3,246,16,1,5,32,1,5,40,9
	.half	.L763-.L220
	.byte	3,203,117,1,7,9
	.half	.L518-.L763
	.byte	0,1,1
.L1499:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_4')
	.sect	'.debug_ranges'
.L517:
	.word	-1,.L220,0,.L518-.L220,0,0
	.sdecl	'.debug_info',debug,cluster('Fl_crc')
	.sect	'.debug_info'
.L519:
	.word	189
	.half	3
	.word	.L520
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'Fl_crc',0,7,180,3,7
	.word	.L569
	.byte	1,5,3
	.word	Fl_crc
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Fl_crc')
	.sect	'.debug_abbrev'
.L520:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('FL_NvmInfo')
	.sect	'.debug_info'
.L521:
	.word	193
	.half	3
	.word	.L522
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FL_NvmInfo',0,7,148,1,16
	.word	.L697
	.byte	1,5,3
	.word	FL_NvmInfo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FL_NvmInfo')
	.sect	'.debug_abbrev'
.L522:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('FBL_HeaderType')
	.sect	'.debug_info'
.L523:
	.word	196
	.half	3
	.word	.L524
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FBL_HeaderType',0,7,141,1,14
	.word	.L569
	.byte	5,3
	.word	FBL_HeaderType
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FBL_HeaderType')
	.sect	'.debug_abbrev'
.L524:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('FBL_DownloadFlags')
	.sect	'.debug_info'
.L525:
	.word	199
	.half	3
	.word	.L526
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FBL_DownloadFlags',0,7,142,1,14
	.word	.L698
	.byte	5,3
	.word	FBL_DownloadFlags
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FBL_DownloadFlags')
	.sect	'.debug_abbrev'
.L526:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('FBL_SignVerifFlags')
	.sect	'.debug_info'
.L527:
	.word	200
	.half	3
	.word	.L528
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FBL_SignVerifFlags',0,7,143,1,14
	.word	.L698
	.byte	5,3
	.word	FBL_SignVerifFlags
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FBL_SignVerifFlags')
	.sect	'.debug_abbrev'
.L528:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('fblFileIndex')
	.sect	'.debug_info'
.L529:
	.word	195
	.half	3
	.word	.L530
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'fblFileIndex',0,7,144,1,7
	.word	.L569
	.byte	1,5,3
	.word	fblFileIndex
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('fblFileIndex')
	.sect	'.debug_abbrev'
.L530:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('FL_RemainDataStruct')
	.sect	'.debug_info'
.L531:
	.word	201
	.half	3
	.word	.L532
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FL_RemainDataStruct',0,7,151,1,26
	.word	.L699
	.byte	5,3
	.word	FL_RemainDataStruct
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FL_RemainDataStruct')
	.sect	'.debug_abbrev'
.L532:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('FldownloadStatus')
	.sect	'.debug_info'
.L533:
	.word	198
	.half	3
	.word	.L534
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FldownloadStatus',0,7,153,1,29
	.word	.L700
	.byte	5,3
	.word	FldownloadStatus
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FldownloadStatus')
	.sect	'.debug_abbrev'
.L534:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('FlProgramData')
	.sect	'.debug_info'
.L535:
	.word	195
	.half	3
	.word	.L536
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FlProgramData',0,7,155,1,14
	.word	.L701
	.byte	5,3
	.word	FlProgramData
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FlProgramData')
	.sect	'.debug_abbrev'
.L536:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('FlProgramLength')
	.sect	'.debug_info'
.L537:
	.word	197
	.half	3
	.word	.L538
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FlProgramLength',0,7,157,1,15
	.word	.L561
	.byte	5,3
	.word	FlProgramLength
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FlProgramLength')
	.sect	'.debug_abbrev'
.L538:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('flashParamInfo')
	.sect	'.debug_info'
.L539:
	.word	196
	.half	3
	.word	.L540
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'flashParamInfo',0,7,159,1,20
	.word	.L702
	.byte	5,3
	.word	flashParamInfo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('flashParamInfo')
	.sect	'.debug_abbrev'
.L540:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('FlIntegrityChkIsHash')
	.sect	'.debug_info'
.L541:
	.word	202
	.half	3
	.word	.L542
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FlIntegrityChkIsHash',0,7,172,1,16
	.word	.L569
	.byte	5,3
	.word	FlIntegrityChkIsHash
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FlIntegrityChkIsHash')
	.sect	'.debug_abbrev'
.L542:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('BL_Information')
	.sect	'.debug_info'
.L543:
	.word	197
	.half	3
	.word	.L544
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'BL_Information',0,7,207,1,14
	.word	.L703
	.byte	1,5,3
	.word	BL_Information
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('BL_Information')
	.sect	'.debug_abbrev'
.L544:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('addblkProgAttempt')
	.sect	'.debug_info'
.L545:
	.word	200
	.half	3
	.word	.L546
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'addblkProgAttempt',0,7,211,1,7
	.word	.L569
	.byte	1,5,3
	.word	addblkProgAttempt
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('addblkProgAttempt')
	.sect	'.debug_abbrev'
.L546:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('appblkIntDefault')
	.sect	'.debug_info'
.L547:
	.word	199
	.half	3
	.word	.L548
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'appblkIntDefault',0,7,212,1,7
	.word	.L569
	.byte	1,5,3
	.word	appblkIntDefault
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('appblkIntDefault')
	.sect	'.debug_abbrev'
.L548:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('appblkCpbDefault')
	.sect	'.debug_info'
.L549:
	.word	199
	.half	3
	.word	.L550
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'appblkCpbDefault',0,7,213,1,7
	.word	.L569
	.byte	1,5,3
	.word	appblkCpbDefault
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('appblkCpbDefault')
	.sect	'.debug_abbrev'
.L550:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('boot_cpb_data')
	.sect	'.debug_info'
.L551:
	.word	196
	.half	3
	.word	.L552
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'boot_cpb_data',0,7,215,1,13
	.word	.L704
	.byte	1,5,3
	.word	boot_cpb_data
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('boot_cpb_data')
	.sect	'.debug_abbrev'
.L552:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('FLERRStatus')
	.sect	'.debug_info'
.L553:
	.word	194
	.half	3
	.word	.L554
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'FLERRStatus',0,7,151,5,7
	.word	.L569
	.byte	1,5,3
	.word	FLERRStatus
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FLERRStatus')
	.sect	'.debug_abbrev'
.L554:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('headRemainDataDownloaded')
	.sect	'.debug_info'
.L555:
	.word	207
	.half	3
	.word	.L556
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'headRemainDataDownloaded',0,7,136,8,9
	.word	.L569
	.byte	1,5,3
	.word	headRemainDataDownloaded
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('headRemainDataDownloaded')
	.sect	'.debug_abbrev'
.L556:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Compatibility_Code')
	.sect	'.debug_info'
.L557:
	.word	201
	.half	3
	.word	.L558
	.byte	4,1
	.byte	'..\\flash\\FL.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L285
	.byte	3
	.byte	'Compatibility_Code',0,7,139,16,13
	.word	.L705
	.byte	1,5,3
	.word	Compatibility_Code
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Compatibility_Code')
	.sect	'.debug_abbrev'
.L558:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L197:
	.word	-1,.L198,0,.L503-.L198
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L245:
	.word	-1,.L246,0,.L508-.L246
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L203:
	.word	-1,.L204,0,.L513-.L204
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_4')
	.sect	'.debug_loc'
.L219:
	.word	-1,.L220,0,.L518-.L220
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FBL_CheckSumFor37')
	.sect	'.debug_loc'
.L235:
	.word	-1,.L236,0,.L631-.L236
	.half	2
	.byte	138,0
	.word	0,0
.L633:
	.word	-1,.L236,0,.L779-.L236
	.half	1
	.byte	100
	.word	.L116-.L236,.L782-.L236
	.half	1
	.byte	100
	.word	.L120-.L236,.L119-.L236
	.half	1
	.byte	100
	.word	0,0
.L634:
	.word	-1,.L236,.L779-.L236,.L780-.L236
	.half	5
	.byte	144,33,157,32,0
	.word	.L781-.L236,.L116-.L236
	.half	5
	.byte	144,36,157,32,0
	.word	.L782-.L236,.L120-.L236
	.half	5
	.byte	144,33,157,32,0
	.word	.L783-.L236,.L631-.L236
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FBL_GetSignVerifFlag')
	.sect	'.debug_loc'
.L259:
	.word	-1,.L260,0,.L695-.L260
	.half	2
	.byte	138,0
	.word	0,0
.L696:
	.word	-1,.L260,0,.L695-.L260
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FBL_IntegrityCheck')
	.sect	'.debug_loc'
.L211:
	.word	-1,.L212,0,.L585-.L212
	.half	2
	.byte	138,0
	.word	0,0
.L586:
	.word	0,0
.L587:
	.word	-1,.L212,.L744-.L212,.L585-.L212
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckCPBRoutine')
	.sect	'.debug_loc'
.L209:
	.word	-1,.L210,0,.L584-.L210
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckCompatibility')
	.sect	'.debug_loc'
.L267:
	.word	-1,.L268,0,.L815-.L268
	.half	2
	.byte	138,0
	.word	.L815-.L268,.L683-.L268
	.half	2
	.byte	138,8
	.word	.L683-.L268,.L683-.L268
	.half	2
	.byte	138,0
	.word	0,0
.L686:
	.word	-1,.L268,0,.L816-.L268
	.half	2
	.byte	145,120
	.word	.L246-.L268,.L508-.L268
	.half	2
	.byte	145,120
	.word	.L817-.L268,.L683-.L268
	.half	2
	.byte	145,120
	.word	0,0
.L684:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckDownloadSegment')
	.sect	'.debug_loc'
.L249:
	.word	-1,.L250,0,.L654-.L250
	.half	2
	.byte	138,0
	.word	0,0
.L657:
	.word	-1,.L250,.L796-.L250,.L150-.L250
	.half	1
	.byte	111
	.word	0,0
.L655:
	.word	-1,.L250,.L755-.L250,.L503-.L250
	.half	5
	.byte	144,33,157,32,0
	.word	0,.L654-.L250
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckModuleId')
	.sect	'.debug_loc'
.L239:
	.word	-1,.L240,0,.L573-.L240
	.half	2
	.byte	138,0
	.word	0,0
.L576:
	.word	-1,.L240,0,.L784-.L240
	.half	1
	.byte	101
	.word	.L204-.L240,.L513-.L240
	.half	1
	.byte	108
	.word	.L789-.L240,.L573-.L240
	.half	1
	.byte	108
	.word	0,0
.L578:
	.word	-1,.L240,.L204-.L240,.L513-.L240
	.half	5
	.byte	144,36,157,32,0
	.word	.L790-.L240,.L573-.L240
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L579:
	.word	-1,.L240,.L204-.L240,.L513-.L240
	.half	5
	.byte	144,33,157,32,0
	.word	.L791-.L240,.L573-.L240
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L575:
	.word	-1,.L240,0,.L785-.L240
	.half	1
	.byte	100
	.word	.L786-.L240,.L787-.L240
	.half	1
	.byte	111
	.word	.L788-.L240,.L787-.L240
	.half	1
	.byte	100
	.word	.L204-.L240,.L709-.L240
	.half	1
	.byte	111
	.word	0,0
.L574:
	.word	-1,.L240,0,.L784-.L240
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L577:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckProgPreCondition')
	.sect	'.debug_loc'
.L205:
	.word	-1,.L206,0,.L571-.L206
	.half	2
	.byte	138,0
	.word	0,0
.L572:
	.word	-1,.L206,0,.L571-.L206
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckProgramCounter')
	.sect	'.debug_loc'
.L271:
	.word	-1,.L272,0,.L646-.L272
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckProgramDependencies')
	.sect	'.debug_loc'
.L269:
	.word	-1,.L270,0,.L640-.L270
	.half	2
	.byte	138,0
	.word	0,0
.L641:
	.word	-1,.L270,0,.L640-.L270
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckProgramIntegrity')
	.sect	'.debug_loc'
.L265:
	.word	-1,.L266,0,.L638-.L266
	.half	2
	.byte	138,0
	.word	0,0
.L639:
	.word	-1,.L266,.L814-.L266,.L638-.L266
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckSWVerification')
	.sect	'.debug_loc'
.L273:
	.word	-1,.L274,0,.L642-.L274
	.half	2
	.byte	138,0
	.word	0,0
.L643:
	.word	-1,.L274,0,.L642-.L274
	.half	1
	.byte	100
	.word	0,0
.L645:
	.word	-1,.L274,.L818-.L274,.L642-.L274
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckSumFor37')
	.sect	'.debug_loc'
.L233:
	.word	-1,.L234,0,.L769-.L234
	.half	2
	.byte	138,0
	.word	.L769-.L234,.L618-.L234
	.half	3
	.byte	138,176,6
	.word	.L618-.L234,.L618-.L234
	.half	2
	.byte	138,0
	.word	0,0
.L620:
	.word	-1,.L234,.L709-.L234,.L513-.L234
	.half	1
	.byte	100
	.word	.L709-.L234,.L513-.L234
	.half	1
	.byte	108
	.word	0,.L770-.L234
	.half	1
	.byte	100
	.word	.L771-.L234,.L618-.L234
	.half	1
	.byte	108
	.word	0,0
.L624:
	.word	-1,.L234,.L777-.L234,.L618-.L234
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L627:
	.word	-1,.L234,.L774-.L234,.L775-.L234
	.half	5
	.byte	144,32,157,32,0
	.word	.L775-.L234,.L109-.L234
	.half	1
	.byte	111
	.word	.L778-.L234,.L777-.L234
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L628:
	.word	-1,.L234,.L776-.L234,.L777-.L234
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L626:
	.word	-1,.L234,.L772-.L234,.L773-.L234
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L621:
	.word	-1,.L234,.L709-.L234,.L513-.L234
	.half	5
	.byte	144,36,157,32,0
	.word	.L771-.L234,.L618-.L234
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L630:
	.word	-1,.L234,.L709-.L234,.L513-.L234
	.half	3
	.byte	145,220,121
	.word	0,.L618-.L234
	.half	3
	.byte	145,220,121
	.word	0,0
.L623:
	.word	-1,.L234,.L709-.L234,.L513-.L234
	.half	3
	.byte	145,208,121
	.word	0,.L618-.L234
	.half	3
	.byte	145,208,121
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckSumRoutine')
	.sect	'.debug_loc'
.L207:
	.word	-1,.L208,0,.L580-.L208
	.half	2
	.byte	138,0
	.word	0,0
.L582:
	.word	-1,.L208,.L198-.L208,.L503-.L208
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L742-.L208
	.half	5
	.byte	144,34,157,32,0
	.word	.L27-.L208,.L26-.L208
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L583:
	.word	-1,.L208,.L198-.L208,.L503-.L208
	.half	5
	.byte	144,36,157,32,0
	.word	.L743-.L208,.L742-.L208
	.half	5
	.byte	144,36,157,32,0
	.word	.L742-.L208,.L27-.L208
	.half	5
	.byte	144,33,157,32,0
	.word	.L27-.L208,.L580-.L208
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckSuming')
	.sect	'.debug_loc'
.L261:
	.word	-1,.L262,0,.L803-.L262
	.half	2
	.byte	138,0
	.word	.L803-.L262,.L667-.L262
	.half	2
	.byte	138,16
	.word	.L667-.L262,.L667-.L262
	.half	2
	.byte	138,0
	.word	0,0
.L669:
	.word	-1,.L262,.L804-.L262,.L173-.L262
	.half	5
	.byte	144,33,157,32,0
	.word	.L805-.L262,.L806-.L262
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L671:
	.word	-1,.L262,.L246-.L262,.L508-.L262
	.half	5
	.byte	144,36,157,32,0
	.word	.L807-.L262,.L667-.L262
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L668:
	.word	-1,.L262,.L808-.L262,.L667-.L262
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L670:
	.word	-1,.L262,.L246-.L262,.L508-.L262
	.half	2
	.byte	145,112
	.word	0,.L667-.L262
	.half	2
	.byte	145,112
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_CheckSuming_Hash')
	.sect	'.debug_loc'
.L263:
	.word	-1,.L264,0,.L672-.L264
	.half	2
	.byte	138,0
	.word	0,0
.L674:
	.word	-1,.L264,.L810-.L264,.L812-.L264
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L676:
	.word	-1,.L264,.L812-.L264,.L672-.L264
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L673:
	.word	-1,.L264,.L198-.L264,.L503-.L264
	.half	5
	.byte	144,34,157,32,0
	.word	.L809-.L264,.L810-.L264
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L677:
	.word	-1,.L264,.L811-.L264,.L672-.L264
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L675:
	.word	-1,.L264,.L813-.L264,.L812-.L264
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_DownloadRemainData')
	.sect	'.debug_loc'
.L251:
	.word	-1,.L252,0,.L659-.L252
	.half	2
	.byte	138,0
	.word	0,0
.L660:
	.word	-1,.L252,.L797-.L252,.L659-.L252
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_DownloadRequestValid')
	.sect	'.debug_loc'
.L215:
	.word	-1,.L216,0,.L592-.L216
	.half	2
	.byte	138,0
	.word	0,0
.L596:
	.word	-1,.L216,0,.L750-.L216
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L597:
	.word	-1,.L216,.L751-.L216,.L750-.L216
	.half	5
	.byte	144,36,157,32,0
	.word	.L750-.L216,.L65-.L216
	.half	5
	.byte	144,33,157,32,0
	.word	.L752-.L216,.L753-.L216
	.half	5
	.byte	144,36,157,32,0
	.word	.L754-.L216,.L592-.L216
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L594:
	.word	-1,.L216,0,.L750-.L216
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_EraseRoutine')
	.sect	'.debug_loc'
.L213:
	.word	-1,.L214,0,.L588-.L214
	.half	2
	.byte	138,0
	.word	0,0
.L590:
	.word	-1,.L214,0,.L745-.L214
	.half	5
	.byte	144,34,157,32,0
	.word	.L746-.L214,.L745-.L214
	.half	5
	.byte	144,36,157,32,0
	.word	.L198-.L214,.L503-.L214
	.half	5
	.byte	144,36,157,32,0
	.word	.L198-.L214,.L503-.L214
	.half	5
	.byte	144,34,157,32,0
	.word	.L748-.L214,.L588-.L214
	.half	5
	.byte	144,36,157,32,0
	.word	.L748-.L214,.L749-.L214
	.half	5
	.byte	144,34,157,32,0
	.word	.L46-.L214,.L42-.L214
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L591:
	.word	-1,.L214,.L747-.L214,.L745-.L214
	.half	5
	.byte	144,36,157,32,32
	.word	.L198-.L214,.L503-.L214
	.half	5
	.byte	144,36,157,32,32
	.word	.L748-.L214,.L749-.L214
	.half	5
	.byte	144,36,157,32,32
	.word	.L749-.L214,.L46-.L214
	.half	5
	.byte	144,33,157,32,0
	.word	.L46-.L214,.L588-.L214
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_Erasing')
	.sect	'.debug_loc'
.L247:
	.word	-1,.L248,0,.L652-.L248
	.half	2
	.byte	138,0
	.word	0,0
.L653:
	.word	-1,.L248,.L795-.L248,.L652-.L248
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_ExitTransferData')
	.sect	'.debug_loc'
.L225:
	.word	-1,.L226,0,.L610-.L226
	.half	2
	.byte	138,0
	.word	0,0
.L611:
	.word	-1,.L226,.L755-.L226,.L503-.L226
	.half	5
	.byte	144,36,157,32,0
	.word	0,.L610-.L226
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L612:
	.word	-1,.L226,.L764-.L226,.L765-.L226
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_FlashProgramRegion')
	.sect	'.debug_loc'
.L217:
	.word	-1,.L218,0,.L598-.L218
	.half	2
	.byte	138,0
	.word	0,0
.L600:
	.word	-1,.L218,.L755-.L218,.L503-.L218
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L756-.L218
	.half	5
	.byte	144,34,157,32,0
	.word	.L757-.L218,.L756-.L218
	.half	1
	.byte	100
	.word	.L83-.L218,.L759-.L218
	.half	5
	.byte	144,34,157,32,0
	.word	.L86-.L218,.L82-.L218
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L604:
	.word	-1,.L218,.L755-.L218,.L503-.L218
	.half	5
	.byte	144,36,157,32,0
	.word	.L755-.L218,.L503-.L218
	.half	5
	.byte	144,34,157,32,32
	.word	0,.L756-.L218
	.half	5
	.byte	144,34,157,32,32
	.word	.L83-.L218,.L760-.L218
	.half	5
	.byte	144,34,157,32,32
	.word	.L220-.L218,.L763-.L218
	.half	5
	.byte	144,36,157,32,0
	.word	.L758-.L218,.L598-.L218
	.half	5
	.byte	144,36,157,32,0
	.word	.L86-.L218,.L82-.L218
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L607:
	.word	-1,.L218,.L761-.L218,.L762-.L218
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L606:
	.word	-1,.L218,.L755-.L218,.L503-.L218
	.half	5
	.byte	144,36,157,32,32
	.word	.L220-.L218,.L763-.L218
	.half	5
	.byte	144,36,157,32,32
	.word	.L758-.L218,.L598-.L218
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L602:
	.word	-1,.L218,.L755-.L218,.L503-.L218
	.half	1
	.byte	100
	.word	.L755-.L218,.L503-.L218
	.half	1
	.byte	101
	.word	0,.L757-.L218
	.half	1
	.byte	100
	.word	.L758-.L218,.L756-.L218
	.half	1
	.byte	101
	.word	.L83-.L218,.L761-.L218
	.half	1
	.byte	100
	.word	.L83-.L218,.L761-.L218
	.half	1
	.byte	101
	.word	.L86-.L218,.L82-.L218
	.half	1
	.byte	100
	.word	.L86-.L218,.L82-.L218
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_Get4Byte')
	.sect	'.debug_loc'
.L237:
	.word	-1,.L238,0,.L693-.L238
	.half	2
	.byte	138,0
	.word	0,0
.L694:
	.word	-1,.L238,0,.L693-.L238
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_GetSecErrFlag')
	.sect	'.debug_loc'
.L283:
	.word	-1,.L284,0,.L651-.L284
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_HandleRemainData')
	.sect	'.debug_loc'
.L253:
	.word	-1,.L254,0,.L661-.L254
	.half	2
	.byte	138,0
	.word	0,0
.L662:
	.word	-1,.L254,.L798-.L254,.L799-.L254
	.half	5
	.byte	144,36,157,32,0
	.word	.L799-.L254,.L155-.L254
	.half	5
	.byte	144,33,157,32,0
	.word	.L155-.L254,.L661-.L254
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_InitState')
	.sect	'.debug_loc'
.L195:
	.word	-1,.L196,0,.L559-.L196
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_KeepNvmInforNoUsed')
	.sect	'.debug_loc'
.L193:
	.word	-1,.L194,0,.L687-.L194
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_MainFunction')
	.sect	'.debug_loc'
.L241:
	.word	-1,.L242,0,.L617-.L242
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_Programming')
	.sect	'.debug_loc'
.L257:
	.word	-1,.L258,0,.L665-.L258
	.half	2
	.byte	138,0
	.word	0,0
.L666:
	.word	-1,.L258,.L198-.L258,.L503-.L258
	.half	5
	.byte	144,33,157,32,0
	.word	.L802-.L258,.L665-.L258
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_ProgrammingData')
	.sect	'.debug_loc'
.L255:
	.word	-1,.L256,0,.L800-.L256
	.half	2
	.byte	138,0
	.word	.L800-.L256,.L663-.L256
	.half	2
	.byte	138,16
	.word	.L663-.L256,.L663-.L256
	.half	2
	.byte	138,0
	.word	0,0
.L664:
	.word	-1,.L256,.L801-.L256,.L663-.L256
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_ReadConstDIDData')
	.sect	'.debug_loc'
.L690:
	.word	-1,.L200,0,.L688-.L200
	.half	5
	.byte	144,34,157,32,0
	.word	.L707-.L200,.L3-.L200
	.half	1
	.byte	111
	.word	0,0
.L199:
	.word	-1,.L200,0,.L688-.L200
	.half	2
	.byte	138,0
	.word	0,0
.L692:
	.word	-1,.L200,.L4-.L200,.L706-.L200
	.half	5
	.byte	144,39,157,32,32
	.word	.L708-.L200,.L688-.L200
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L691:
	.word	-1,.L200,0,.L688-.L200
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L689:
	.word	-1,.L200,0,.L688-.L200
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_ReadMemory')
	.sect	'.debug_loc'
.L201:
	.word	-1,.L202,0,.L562-.L202
	.half	2
	.byte	138,0
	.word	0,0
.L563:
	.word	-1,.L202,.L709-.L202,.L513-.L202
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L5-.L202
	.half	5
	.byte	144,34,157,32,0
	.word	.L712-.L202,.L713-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L714-.L202,.L715-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L716-.L202,.L711-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L718-.L202,.L719-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L720-.L202,.L717-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L721-.L202,.L722-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L723-.L202,.L724-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L12-.L202,.L11-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L725-.L202,.L726-.L202
	.half	1
	.byte	98
	.word	.L728-.L202,.L9-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L729-.L202,.L730-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L16-.L202,.L15-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L731-.L202,.L732-.L202
	.half	1
	.byte	111
	.word	.L734-.L202,.L14-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L735-.L202,.L736-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L737-.L202,.L738-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L21-.L202,.L20-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	.L739-.L202,.L740-.L202
	.half	1
	.byte	111
	.word	.L741-.L202,.L18-.L202
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L570:
	.word	0,0
.L566:
	.word	-1,.L202,.L709-.L202,.L513-.L202
	.half	1
	.byte	109
	.word	.L709-.L202,.L513-.L202
	.half	1
	.byte	100
	.word	.L710-.L202,.L562-.L202
	.half	1
	.byte	109
	.word	0,.L5-.L202
	.half	1
	.byte	100
	.word	.L716-.L202,.L711-.L202
	.half	1
	.byte	100
	.word	.L720-.L202,.L717-.L202
	.half	1
	.byte	100
	.word	0,0
.L564:
	.word	-1,.L202,.L709-.L202,.L513-.L202
	.half	5
	.byte	144,34,157,32,32
	.word	0,.L5-.L202
	.half	5
	.byte	144,34,157,32,32
	.word	.L716-.L202,.L6-.L202
	.half	5
	.byte	144,36,157,32,32
	.word	.L720-.L202,.L8-.L202
	.half	5
	.byte	144,36,157,32,32
	.word	.L726-.L202,.L727-.L202
	.half	5
	.byte	144,36,157,32,32
	.word	.L732-.L202,.L733-.L202
	.half	5
	.byte	144,36,157,32,32
	.word	.L740-.L202,.L741-.L202
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L568:
	.word	-1,.L202,.L709-.L202,.L513-.L202
	.half	5
	.byte	144,33,157,32,0
	.word	.L710-.L202,.L711-.L202
	.half	5
	.byte	144,33,157,32,0
	.word	.L6-.L202,.L717-.L202
	.half	5
	.byte	144,33,157,32,0
	.word	.L8-.L202,.L562-.L202
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_ServiceFinished')
	.sect	'.debug_loc'
.L227:
	.word	-1,.L228,0,.L613-.L228
	.half	2
	.byte	138,0
	.word	0,0
.L615:
	.word	-1,.L228,.L198-.L228,.L503-.L228
	.half	1
	.byte	100
	.word	.L766-.L228,.L613-.L228
	.half	1
	.byte	100
	.word	0,0
.L616:
	.word	-1,.L228,.L767-.L228,.L613-.L228
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_SetChecksumStep')
	.sect	'.debug_loc'
.L279:
	.word	-1,.L280,0,.L649-.L280
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_SetExitTransferStep')
	.sect	'.debug_loc'
.L221:
	.word	-1,.L222,0,.L608-.L222
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_SetHeadBlockErased')
	.sect	'.debug_loc'
.L223:
	.word	-1,.L224,0,.L609-.L224
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_SetRequestStep')
	.sect	'.debug_loc'
.L277:
	.word	-1,.L278,0,.L648-.L278
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_SignVerifFlags')
	.sect	'.debug_loc'
.L275:
	.word	-1,.L276,0,.L647-.L276
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FL_UpdateNvm')
	.sect	'.debug_loc'
.L243:
	.word	-1,.L244,0,.L792-.L244
	.half	2
	.byte	138,0
	.word	.L792-.L244,.L678-.L244
	.half	2
	.byte	138,16
	.word	.L678-.L244,.L678-.L244
	.half	2
	.byte	138,0
	.word	0,0
.L682:
	.word	-1,.L244,0,.L793-.L244
	.half	2
	.byte	145,112
	.word	.L246-.L244,.L508-.L244
	.half	2
	.byte	145,112
	.word	.L794-.L244,.L678-.L244
	.half	2
	.byte	145,112
	.word	0,0
.L680:
	.word	-1,.L244,.L792-.L244,.L793-.L244
	.half	5
	.byte	144,36,157,32,0
	.word	.L246-.L244,.L508-.L244
	.half	5
	.byte	144,36,157,32,0
	.word	.L794-.L244,.L678-.L244
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GetS37JobStatusResult')
	.sect	'.debug_loc'
.L231:
	.word	-1,.L232,0,.L636-.L232
	.half	2
	.byte	138,0
	.word	0,0
.L637:
	.word	-1,.L232,.L198-.L232,.L503-.L232
	.half	5
	.byte	144,34,157,32,0
	.word	.L220-.L232,.L518-.L232
	.half	5
	.byte	144,34,157,32,0
	.word	.L768-.L232,.L636-.L232
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SetS37JobStatusBusy')
	.sect	'.debug_loc'
.L229:
	.word	-1,.L230,0,.L635-.L230
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('UpdateSecurityErrorFlag')
	.sect	'.debug_loc'
.L281:
	.word	-1,.L282,0,.L650-.L282
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1502:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('FL_KeepNvmInforNoUsed')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L194,.L687-.L194
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_InitState')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L196,.L559-.L196
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_ReadConstDIDData')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L200,.L688-.L200
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_ReadMemory')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L202,.L562-.L202
	.sdecl	'.debug_frame',debug,cluster('FL_CheckProgPreCondition')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L206,.L571-.L206
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_CheckSumRoutine')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L208,.L580-.L208
	.sdecl	'.debug_frame',debug,cluster('FL_CheckCPBRoutine')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L210,.L584-.L210
	.sdecl	'.debug_frame',debug,cluster('FBL_IntegrityCheck')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L212,.L585-.L212
	.sdecl	'.debug_frame',debug,cluster('FL_EraseRoutine')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L214,.L588-.L214
	.sdecl	'.debug_frame',debug,cluster('FL_DownloadRequestValid')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L216,.L592-.L216
	.sdecl	'.debug_frame',debug,cluster('FL_FlashProgramRegion')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L218,.L598-.L218
	.sdecl	'.debug_frame',debug,cluster('FL_SetExitTransferStep')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L222,.L608-.L222
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_SetHeadBlockErased')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L224,.L609-.L224
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_ExitTransferData')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L226,.L610-.L226
	.sdecl	'.debug_frame',debug,cluster('FL_ServiceFinished')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L228,.L613-.L228
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('SetS37JobStatusBusy')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L230,.L635-.L230
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('GetS37JobStatusResult')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L232,.L636-.L232
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_CheckSumFor37')
	.sect	'.debug_frame'
	.word	36
	.word	.L1502,.L234,.L618-.L234
	.byte	4
	.word	(.L769-.L234)/2
	.byte	19,176,6,22,26,4,19,138,176,6,4
	.word	(.L618-.L769)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('FBL_CheckSumFor37')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L236,.L631-.L236
	.sdecl	'.debug_frame',debug,cluster('FL_Get4Byte')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L238,.L693-.L238
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_CheckModuleId')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L240,.L573-.L240
	.sdecl	'.debug_frame',debug,cluster('FL_MainFunction')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L242,.L617-.L242
	.sdecl	'.debug_frame',debug,cluster('FL_UpdateNvm')
	.sect	'.debug_frame'
	.word	36
	.word	.L1502,.L244,.L678-.L244
	.byte	4
	.word	(.L792-.L244)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L678-.L792)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_Erasing')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L248,.L652-.L248
	.sdecl	'.debug_frame',debug,cluster('FL_CheckDownloadSegment')
	.sect	'.debug_frame'
	.word	16
	.word	.L1502,.L250,.L654-.L250
	.byte	8,19,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_DownloadRemainData')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L252,.L659-.L252
	.sdecl	'.debug_frame',debug,cluster('FL_HandleRemainData')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L254,.L661-.L254
	.sdecl	'.debug_frame',debug,cluster('FL_ProgrammingData')
	.sect	'.debug_frame'
	.word	36
	.word	.L1502,.L256,.L663-.L256
	.byte	4
	.word	(.L800-.L256)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L663-.L800)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_Programming')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L258,.L665-.L258
	.sdecl	'.debug_frame',debug,cluster('FBL_GetSignVerifFlag')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L260,.L695-.L260
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_CheckSuming')
	.sect	'.debug_frame'
	.word	36
	.word	.L1502,.L262,.L667-.L262
	.byte	4
	.word	(.L803-.L262)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L667-.L803)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_CheckSuming_Hash')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L264,.L672-.L264
	.sdecl	'.debug_frame',debug,cluster('FL_CheckProgramIntegrity')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L266,.L638-.L266
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_CheckCompatibility')
	.sect	'.debug_frame'
	.word	36
	.word	.L1502,.L268,.L683-.L268
	.byte	4
	.word	(.L815-.L268)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L683-.L815)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_CheckProgramDependencies')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L270,.L640-.L270
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_CheckProgramCounter')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L272,.L646-.L272
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_CheckSWVerification')
	.sect	'.debug_frame'
	.word	20
	.word	.L1502,.L274,.L642-.L274
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('FL_SignVerifFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L276,.L647-.L276
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_SetRequestStep')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L278,.L648-.L278
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FL_SetChecksumStep')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L280,.L649-.L280
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('UpdateSecurityErrorFlag')
	.sect	'.debug_frame'
	.word	12
	.word	.L1502,.L282,.L650-.L282
	.sdecl	'.debug_frame',debug,cluster('FL_GetSecErrFlag')
	.sect	'.debug_frame'
	.word	24
	.word	.L1502,.L284,.L651-.L284
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1503:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L1503,.L198,.L503-.L198
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L1503,.L204,.L513-.L204
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_4')
	.sect	'.debug_frame'
	.word	24
	.word	.L1503,.L220,.L518-.L220
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L1503,.L246,.L508-.L246
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\flash\FL.c	  2181  
; ..\flash\FL.c	  2182  \ 
; ..\flash\FL.c	  2183  /*=======[E N D   O F   F I L E]==============================================*/
; ..\flash\FL.c	  2184  

	; Module end
