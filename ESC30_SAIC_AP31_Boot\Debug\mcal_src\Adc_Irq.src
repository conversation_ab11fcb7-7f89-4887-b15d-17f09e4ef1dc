	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc20852a -c99 --dep-file=mcal_src\\.Adc_Irq.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Adc_Irq.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Adc_Irq.src ..\\mcal_src\\Adc_Irq.c"
	.compiler_name		"ctc"
	.name	"Adc_Irq"

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1886
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_src\\Adc_Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	176
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	182
	.byte	5,1,3
	.word	206
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	208
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	231
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	262
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	299
	.byte	4
	.byte	'Adc_ChannelType',0,3,227,2,15
	.word	231
	.byte	4
	.byte	'Adc_GroupType',0,3,233,2,16
	.word	262
	.byte	4
	.byte	'Adc_ValueGroupType',0,3,247,2,16
	.word	262
	.byte	4
	.byte	'Adc_PrescaleType',0,3,144,3,16
	.word	299
	.byte	4
	.byte	'Adc_TriggerSourceType',0,3,200,3,15
	.word	231
	.byte	4
	.byte	'Adc_GroupConvModeType',0,3,208,3,15
	.word	231
	.byte	4
	.byte	'Adc_GroupPriorityType',0,3,215,3,15
	.word	231
	.byte	4
	.byte	'Adc_GroupDefType',0,3,130,4,25
	.word	231
	.byte	4
	.byte	'Adc_StreamNumSampleType',0,3,135,4,15
	.word	231
	.byte	4
	.byte	'Adc_StreamBufferModeType',0,3,148,4,15
	.word	231
	.byte	4
	.byte	'Adc_TriggSrcArbLevelType',0,3,179,4,16
	.word	299
	.byte	7
	.byte	'Adc_TriggSrcData',0,3,205,4,16,4,8
	.byte	'GrpId',0,2
	.word	262
	.byte	2,35,0,8
	.byte	'GrpPriority',0,1
	.word	231
	.byte	2,35,2,8
	.byte	'IsrDoNothing',0,1
	.word	231
	.byte	2,35,3,0,4
	.byte	'Adc_TriggSrcDataType',0,3,223,4,2
	.word	657
	.byte	7
	.byte	'Adc_GroupData',0,3,232,4,16,32,9,16
	.word	231
	.byte	10,15,0,8
	.byte	'GrpChannels',0,16
	.word	789
	.byte	2,35,0,9,16
	.word	231
	.byte	10,15,0,8
	.byte	'GrpChannelRes',0,16
	.word	819
	.byte	2,35,16,0,4
	.byte	'Adc_GroupDataType',0,3,250,4,2
	.word	769
	.byte	7
	.byte	'Adc_HwUnitCfgType',0,3,138,6,16,20,8
	.byte	'ArbitrationLength',0,4
	.word	299
	.byte	2,35,0,8
	.byte	'TriggSrcArbLevel',0,4
	.word	299
	.byte	2,35,4,9,8
	.word	299
	.byte	10,1,0,8
	.byte	'KernelInputClass',0,8
	.word	956
	.byte	2,35,8,8
	.byte	'SyncConvMode',0,1
	.word	231
	.byte	2,35,16,8
	.byte	'SlaveReady',0,1
	.word	231
	.byte	2,35,17,8
	.byte	'DmaChannel',0,1
	.word	231
	.byte	2,35,18,0,4
	.byte	'Adc_HwUnitCfgType',0,3,179,6,2
	.word	879
	.byte	7
	.byte	'Adc_GroupCfgType',0,3,182,6,16,12,11
	.word	231
	.byte	3
	.word	1104
	.byte	8
	.byte	'GroupDefinition',0,4
	.word	1109
	.byte	2,35,0,8
	.byte	'IntChMask',0,2
	.word	262
	.byte	2,35,4,8
	.byte	'TriggSrc',0,1
	.word	231
	.byte	2,35,6,8
	.byte	'GrpRequestSrc',0,1
	.word	231
	.byte	2,35,7,8
	.byte	'ConvMode',0,1
	.word	231
	.byte	2,35,8,8
	.byte	'NumSamples',0,1
	.word	231
	.byte	2,35,9,8
	.byte	'StreamBufferMode',0,1
	.word	231
	.byte	2,35,10,0,4
	.byte	'Adc_GroupCfgType',0,3,129,7,3
	.word	1081
	.byte	7
	.byte	'Adc_ChannelCfgType',0,3,136,7,17,28,8
	.byte	'AdcChConfigValue',0,4
	.word	299
	.byte	2,35,0,8
	.byte	'AdcChResAccumulation',0,4
	.word	299
	.byte	2,35,4,8
	.byte	'AdcIsChLimitChkEnabled',0,4
	.word	299
	.byte	2,35,8,8
	.byte	'AdcLimitChkRange',0,4
	.word	299
	.byte	2,35,12,8
	.byte	'AdcLimitChkBnd',0,4
	.word	299
	.byte	2,35,16,8
	.byte	'AdcChBndSelxValue',0,4
	.word	299
	.byte	2,35,20,8
	.byte	'AdcSyncChannel',0,1
	.word	231
	.byte	2,35,24,0,4
	.byte	'Adc_ChannelCfgType',0,3,160,7,3
	.word	1290
	.byte	7
	.byte	'Adc_KernelConfigType',0,3,163,7,17,16,11
	.word	879
	.byte	3
	.word	1560
	.byte	8
	.byte	'HwCfgPtr',0,4
	.word	1565
	.byte	2,35,0,11
	.word	1290
	.byte	3
	.word	1588
	.byte	8
	.byte	'ChCfgPtr',0,4
	.word	1593
	.byte	2,35,4,11
	.word	1081
	.byte	3
	.word	1616
	.byte	8
	.byte	'GrpCfgPtr',0,4
	.word	1621
	.byte	2,35,8,8
	.byte	'TotCfgGrps',0,1
	.word	231
	.byte	2,35,12,0,4
	.byte	'Adc_KernelConfigType',0,3,183,7,3
	.word	1533
	.byte	7
	.byte	'Adc_GlobalCfgType',0,3,186,7,17,28,8
	.byte	'ClkPrescale',0,4
	.word	299
	.byte	2,35,0,8
	.byte	'GlobInputClass',0,8
	.word	956
	.byte	2,35,4,8
	.byte	'PostCalEnable',0,4
	.word	299
	.byte	2,35,12,8
	.byte	'LowPowerSupply',0,4
	.word	299
	.byte	2,35,16,8
	.byte	'RefPrechargeCtrl',0,4
	.word	299
	.byte	2,35,20,8
	.byte	'OperationMode',0,1
	.word	231
	.byte	2,35,24,0,4
	.byte	'Adc_GlobalCfgType',0,3,244,7,3
	.word	1696
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,56,9,0,0,9,1,1,11,15,73,19,0,0,10,33,0,47,15,0,0,11,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Irq.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Adc.h',0,0,0,0,0
.L9:
.L7:

; ..\mcal_src\Adc_Irq.c	     1  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	     2  **                                                                           **
; ..\mcal_src\Adc_Irq.c	     3  ** Copyright (C) Infineon Technologies (2014)                                **
; ..\mcal_src\Adc_Irq.c	     4  **                                                                           **
; ..\mcal_src\Adc_Irq.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Adc_Irq.c	     6  **                                                                           **
; ..\mcal_src\Adc_Irq.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Adc_Irq.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Adc_Irq.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Adc_Irq.c	    10  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    11  *******************************************************************************
; ..\mcal_src\Adc_Irq.c	    12  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    13  **  $FILENAME   : Adc_Irq.c $                                                **
; ..\mcal_src\Adc_Irq.c	    14  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    15  **  $CC VERSION : \main\dev_tc23x\7 $                                        **
; ..\mcal_src\Adc_Irq.c	    16  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    17  **  $DATE       : 2016-06-27 $                                               **
; ..\mcal_src\Adc_Irq.c	    18  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Adc_Irq.c	    20  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Adc_Irq.c	    22  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    23  **  DESCRIPTION : This file contains the interrupt frames for the ADC Module.**
; ..\mcal_src\Adc_Irq.c	    24  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    25  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\Adc_Irq.c	    26  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    27  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    28  
; ..\mcal_src\Adc_Irq.c	    29  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    30  **                      Includes                                              **
; ..\mcal_src\Adc_Irq.c	    31  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    32  
; ..\mcal_src\Adc_Irq.c	    33  #include "Std_Types.h"
; ..\mcal_src\Adc_Irq.c	    34  #include "Irq.h"
; ..\mcal_src\Adc_Irq.c	    35  #include "Mcal.h"
; ..\mcal_src\Adc_Irq.c	    36  #include "Adc.h"
; ..\mcal_src\Adc_Irq.c	    37  
; ..\mcal_src\Adc_Irq.c	    38  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    39  **                      Private Macros Definitions                            **
; ..\mcal_src\Adc_Irq.c	    40  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    41  
; ..\mcal_src\Adc_Irq.c	    42  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    43  **                      Private Type Definitions                              **
; ..\mcal_src\Adc_Irq.c	    44  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    45  
; ..\mcal_src\Adc_Irq.c	    46  
; ..\mcal_src\Adc_Irq.c	    47  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    48  **                      Private Function Declarations                         **
; ..\mcal_src\Adc_Irq.c	    49  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    50  
; ..\mcal_src\Adc_Irq.c	    51  
; ..\mcal_src\Adc_Irq.c	    52  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    53  **                      Global Constant Definitions                           **
; ..\mcal_src\Adc_Irq.c	    54  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    55  
; ..\mcal_src\Adc_Irq.c	    56  
; ..\mcal_src\Adc_Irq.c	    57  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    58  **                      Global Variable Definitions                           **
; ..\mcal_src\Adc_Irq.c	    59  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    60  
; ..\mcal_src\Adc_Irq.c	    61  
; ..\mcal_src\Adc_Irq.c	    62  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    63  **                      Private Constant Definitions                          **
; ..\mcal_src\Adc_Irq.c	    64  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    65  
; ..\mcal_src\Adc_Irq.c	    66  
; ..\mcal_src\Adc_Irq.c	    67  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    68  **                      Private Variable Definitions                          **
; ..\mcal_src\Adc_Irq.c	    69  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    70  
; ..\mcal_src\Adc_Irq.c	    71  /*******************************************************************************
; ..\mcal_src\Adc_Irq.c	    72  **                      Private Function Definitions                          **
; ..\mcal_src\Adc_Irq.c	    73  *******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    74  #define IRQ_START_SEC_CODE
; ..\mcal_src\Adc_Irq.c	    75  #include "MemMap.h"
; ..\mcal_src\Adc_Irq.c	    76  
; ..\mcal_src\Adc_Irq.c	    77  #if (IRQ_ADC0_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	    78  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	    79  ** Syntax :          void ADC0SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	    80  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    81  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	    82  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    83  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	    84  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    85  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	    86  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    87  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	    88  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    89  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	    90  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    91  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	    92  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    93  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	    94  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	    95  **                                                                           **
; ..\mcal_src\Adc_Irq.c	    96  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	    97  #if((IRQ_ADC0_SR0_PRIO > 0) || (IRQ_ADC0_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	    98  #if((IRQ_ADC0_SR0_PRIO > 0) && (IRQ_ADC0_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	    99  IFX_INTERRUPT(ADC0SR0_ISR, 0, IRQ_ADC0_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   100  #elif IRQ_ADC0_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   101  ISR(ADC0SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   102  #endif
; ..\mcal_src\Adc_Irq.c	   103  {
; ..\mcal_src\Adc_Irq.c	   104    
; ..\mcal_src\Adc_Irq.c	   105    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   106  #if (IRQ_ADC0_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   107    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   108  #endif
; ..\mcal_src\Adc_Irq.c	   109  
; ..\mcal_src\Adc_Irq.c	   110  /* ADC0 SRN0 is used by request source 0 of ADC0 */
; ..\mcal_src\Adc_Irq.c	   111  #if (ADC0_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   112    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   113    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC0);
; ..\mcal_src\Adc_Irq.c	   114  #endif
; ..\mcal_src\Adc_Irq.c	   115  
; ..\mcal_src\Adc_Irq.c	   116  }
; ..\mcal_src\Adc_Irq.c	   117  #endif
; ..\mcal_src\Adc_Irq.c	   118  
; ..\mcal_src\Adc_Irq.c	   119  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   120  ** Syntax :          void ADC0SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   121  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   122  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   123  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   124  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   125  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   126  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   127  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   128  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   129  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   130  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   131  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   132  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   133  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   134  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   135  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   136  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   137  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   138  #if((IRQ_ADC0_SR1_PRIO > 0) || (IRQ_ADC0_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   139  #if((IRQ_ADC0_SR1_PRIO > 0) && (IRQ_ADC0_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   140  IFX_INTERRUPT(ADC0SR1_ISR,0,IRQ_ADC0_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   141  #elif IRQ_ADC0_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   142  ISR(ADC0SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   143  #endif
; ..\mcal_src\Adc_Irq.c	   144  {
; ..\mcal_src\Adc_Irq.c	   145       
; ..\mcal_src\Adc_Irq.c	   146    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   147  #if (IRQ_ADC0_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   148    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   149  #endif
; ..\mcal_src\Adc_Irq.c	   150  
; ..\mcal_src\Adc_Irq.c	   151  /* ADC0 SRN1 is used by request source 1 of ADC0 */
; ..\mcal_src\Adc_Irq.c	   152  #if (ADC0_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   153    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   154    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC0);
; ..\mcal_src\Adc_Irq.c	   155  #endif
; ..\mcal_src\Adc_Irq.c	   156  }
; ..\mcal_src\Adc_Irq.c	   157  #endif
; ..\mcal_src\Adc_Irq.c	   158  
; ..\mcal_src\Adc_Irq.c	   159  #endif /* (IRQ_ADC0_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   160  
; ..\mcal_src\Adc_Irq.c	   161  #if (IRQ_ADC1_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   162  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   163  ** Syntax :          void ADC1SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   164  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   165  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   166  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   167  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   168  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   169  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   170  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   171  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   172  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   173  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   174  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   175  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   176  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   177  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   178  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   179  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   180  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   181  #if((IRQ_ADC1_SR0_PRIO > 0) || (IRQ_ADC1_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   182  #if((IRQ_ADC1_SR0_PRIO > 0) && (IRQ_ADC1_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   183  IFX_INTERRUPT(ADC1SR0_ISR,0,IRQ_ADC1_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   184  #elif IRQ_ADC1_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   185  ISR(ADC1SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   186  #endif
; ..\mcal_src\Adc_Irq.c	   187  {
; ..\mcal_src\Adc_Irq.c	   188    
; ..\mcal_src\Adc_Irq.c	   189    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   190  #if (IRQ_ADC1_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   191    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   192  #endif
; ..\mcal_src\Adc_Irq.c	   193  
; ..\mcal_src\Adc_Irq.c	   194  /* ADC1 SRN0 is used by request source 0 of ADC1 */
; ..\mcal_src\Adc_Irq.c	   195  #if ( (ADC1_REQSRC0 == ADC_REQSRC_USED) || (ADC1_REQSRC3 == ADC_REQSRC_USED) )
; ..\mcal_src\Adc_Irq.c	   196    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   197    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC1);
; ..\mcal_src\Adc_Irq.c	   198  #endif
; ..\mcal_src\Adc_Irq.c	   199  
; ..\mcal_src\Adc_Irq.c	   200  }
; ..\mcal_src\Adc_Irq.c	   201  #endif
; ..\mcal_src\Adc_Irq.c	   202  
; ..\mcal_src\Adc_Irq.c	   203  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   204  ** Syntax :          void ADC1SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   205  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   206  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   207  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   208  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   209  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   210  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   211  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   212  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   213  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   214  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   215  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   216  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   217  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   218  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   219  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   220  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   221  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   222  #if((IRQ_ADC1_SR1_PRIO > 0) || (IRQ_ADC1_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   223  #if((IRQ_ADC1_SR1_PRIO > 0) && (IRQ_ADC1_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   224  IFX_INTERRUPT(ADC1SR1_ISR,0,IRQ_ADC1_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   225  #elif IRQ_ADC1_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   226  ISR(ADC1SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   227  #endif
; ..\mcal_src\Adc_Irq.c	   228  {
; ..\mcal_src\Adc_Irq.c	   229       
; ..\mcal_src\Adc_Irq.c	   230    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   231  #if (IRQ_ADC1_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   232    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   233  #endif
; ..\mcal_src\Adc_Irq.c	   234  
; ..\mcal_src\Adc_Irq.c	   235  /* ADC1 SRN1 is used by request source 1 of ADC1 */
; ..\mcal_src\Adc_Irq.c	   236  #if (ADC1_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   237    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   238    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC1);
; ..\mcal_src\Adc_Irq.c	   239  #endif
; ..\mcal_src\Adc_Irq.c	   240  }
; ..\mcal_src\Adc_Irq.c	   241  #endif
; ..\mcal_src\Adc_Irq.c	   242  
; ..\mcal_src\Adc_Irq.c	   243  #endif /* (IRQ_ADC1_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   244  
; ..\mcal_src\Adc_Irq.c	   245  #if (IRQ_ADC2_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   246  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   247  ** Syntax :          void ADC2SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   248  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   249  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   250  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   251  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   252  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   253  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   254  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   255  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   256  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   257  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   258  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   259  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   260  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   261  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   262  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   263  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   264  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   265  #if((IRQ_ADC2_SR0_PRIO > 0) || (IRQ_ADC2_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   266  #if((IRQ_ADC2_SR0_PRIO > 0) && (IRQ_ADC2_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   267  IFX_INTERRUPT(ADC2SR0_ISR,0,IRQ_ADC2_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   268  #elif IRQ_ADC2_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   269  ISR(ADC2SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   270  #endif
; ..\mcal_src\Adc_Irq.c	   271  {
; ..\mcal_src\Adc_Irq.c	   272    
; ..\mcal_src\Adc_Irq.c	   273    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   274  #if (IRQ_ADC2_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   275    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   276  #endif
; ..\mcal_src\Adc_Irq.c	   277  
; ..\mcal_src\Adc_Irq.c	   278  /* ADC2 SRN0 is used by request source 0 of ADC2 */
; ..\mcal_src\Adc_Irq.c	   279  #if (ADC2_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   280    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   281    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC2);
; ..\mcal_src\Adc_Irq.c	   282  #endif
; ..\mcal_src\Adc_Irq.c	   283  
; ..\mcal_src\Adc_Irq.c	   284  }
; ..\mcal_src\Adc_Irq.c	   285  #endif
; ..\mcal_src\Adc_Irq.c	   286  
; ..\mcal_src\Adc_Irq.c	   287  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   288  ** Syntax :          void ADC2SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   289  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   290  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   291  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   292  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   293  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   294  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   295  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   296  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   297  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   298  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   299  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   300  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   301  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   302  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   303  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   304  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   305  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   306  #if((IRQ_ADC2_SR1_PRIO > 0) || (IRQ_ADC2_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   307  #if((IRQ_ADC2_SR1_PRIO > 0) && (IRQ_ADC2_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   308  IFX_INTERRUPT(ADC2SR1_ISR,0,IRQ_ADC2_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   309  #elif IRQ_ADC2_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   310  ISR(ADC2SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   311  #endif
; ..\mcal_src\Adc_Irq.c	   312  {
; ..\mcal_src\Adc_Irq.c	   313       
; ..\mcal_src\Adc_Irq.c	   314    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   315  #if (IRQ_ADC2_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   316    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   317  #endif
; ..\mcal_src\Adc_Irq.c	   318  
; ..\mcal_src\Adc_Irq.c	   319  /* ADC2 SRN1 is used by request source 1 of ADC2 */
; ..\mcal_src\Adc_Irq.c	   320  #if (ADC2_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   321    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   322    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC2);
; ..\mcal_src\Adc_Irq.c	   323  #endif
; ..\mcal_src\Adc_Irq.c	   324  }
; ..\mcal_src\Adc_Irq.c	   325  #endif
; ..\mcal_src\Adc_Irq.c	   326  
; ..\mcal_src\Adc_Irq.c	   327  #endif /* (IRQ_ADC2_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   328  
; ..\mcal_src\Adc_Irq.c	   329  #if (IRQ_ADC3_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   330  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   331  ** Syntax :          void ADC3SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   332  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   333  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   334  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   335  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   336  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   337  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   338  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   339  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   340  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   341  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   342  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   343  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   344  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   345  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   346  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   347  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   348  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   349  #if((IRQ_ADC3_SR0_PRIO > 0) || (IRQ_ADC3_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   350  #if((IRQ_ADC3_SR0_PRIO > 0) && (IRQ_ADC3_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   351  IFX_INTERRUPT(ADC3SR0_ISR,0,IRQ_ADC3_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   352  #elif IRQ_ADC3_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   353  ISR(ADC3SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   354  #endif
; ..\mcal_src\Adc_Irq.c	   355  {
; ..\mcal_src\Adc_Irq.c	   356    
; ..\mcal_src\Adc_Irq.c	   357    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   358  #if (IRQ_ADC3_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   359    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   360  #endif
; ..\mcal_src\Adc_Irq.c	   361  
; ..\mcal_src\Adc_Irq.c	   362  /* ADC3 SRN0 is used by request source 0 of ADC3 */
; ..\mcal_src\Adc_Irq.c	   363  #if (ADC3_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   364    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   365    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC3);
; ..\mcal_src\Adc_Irq.c	   366  #endif
; ..\mcal_src\Adc_Irq.c	   367  
; ..\mcal_src\Adc_Irq.c	   368  }
; ..\mcal_src\Adc_Irq.c	   369  #endif
; ..\mcal_src\Adc_Irq.c	   370  
; ..\mcal_src\Adc_Irq.c	   371  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   372  ** Syntax :          void ADC3SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   373  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   374  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   375  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   376  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   377  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   378  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   379  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   380  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   381  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   382  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   383  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   384  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   385  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   386  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   387  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   388  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   389  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   390  #if((IRQ_ADC3_SR1_PRIO > 0) || (IRQ_ADC3_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   391  #if((IRQ_ADC3_SR1_PRIO > 0) && (IRQ_ADC3_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   392  IFX_INTERRUPT(ADC3SR1_ISR,0,IRQ_ADC3_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   393  #elif IRQ_ADC3_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   394  ISR(ADC3SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   395  #endif
; ..\mcal_src\Adc_Irq.c	   396  {
; ..\mcal_src\Adc_Irq.c	   397       
; ..\mcal_src\Adc_Irq.c	   398    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   399  #if (IRQ_ADC3_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   400    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   401  #endif
; ..\mcal_src\Adc_Irq.c	   402  
; ..\mcal_src\Adc_Irq.c	   403  /* ADC3 SRN1 is used by request source 1 of ADC3 */
; ..\mcal_src\Adc_Irq.c	   404  #if (ADC3_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   405    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   406    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC3);
; ..\mcal_src\Adc_Irq.c	   407  #endif
; ..\mcal_src\Adc_Irq.c	   408  }
; ..\mcal_src\Adc_Irq.c	   409  #endif
; ..\mcal_src\Adc_Irq.c	   410  
; ..\mcal_src\Adc_Irq.c	   411  #endif /* (IRQ_ADC3_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   412  
; ..\mcal_src\Adc_Irq.c	   413  #if (IRQ_ADC4_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   414  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   415  ** Syntax :          void ADC4SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   416  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   417  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   418  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   419  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   420  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   421  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   422  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   423  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   424  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   425  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   426  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   427  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   428  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   429  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   430  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   431  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   432  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   433  #if((IRQ_ADC4_SR0_PRIO > 0) || (IRQ_ADC4_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   434  #if((IRQ_ADC4_SR0_PRIO > 0) && (IRQ_ADC4_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   435  IFX_INTERRUPT(ADC4SR0_ISR,0,IRQ_ADC4_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   436  #elif IRQ_ADC4_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   437  ISR(ADC4SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   438  #endif
; ..\mcal_src\Adc_Irq.c	   439  {
; ..\mcal_src\Adc_Irq.c	   440    
; ..\mcal_src\Adc_Irq.c	   441    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   442  #if (IRQ_ADC4_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   443    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   444  #endif
; ..\mcal_src\Adc_Irq.c	   445  
; ..\mcal_src\Adc_Irq.c	   446  /* ADC4 SRN0 is used by request source 0 of ADC4 */
; ..\mcal_src\Adc_Irq.c	   447  #if (ADC4_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   448    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   449    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC4);
; ..\mcal_src\Adc_Irq.c	   450  #endif
; ..\mcal_src\Adc_Irq.c	   451  
; ..\mcal_src\Adc_Irq.c	   452  }
; ..\mcal_src\Adc_Irq.c	   453  #endif
; ..\mcal_src\Adc_Irq.c	   454  
; ..\mcal_src\Adc_Irq.c	   455  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   456  ** Syntax :          void ADC4SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   457  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   458  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   459  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   460  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   461  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   462  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   463  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   464  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   465  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   466  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   467  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   468  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   469  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   470  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   471  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   472  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   473  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   474  #if((IRQ_ADC4_SR1_PRIO > 0) || (IRQ_ADC4_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   475  #if((IRQ_ADC4_SR1_PRIO > 0) && (IRQ_ADC4_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   476  IFX_INTERRUPT(ADC4SR1_ISR,0,IRQ_ADC4_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   477  #elif IRQ_ADC4_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   478  ISR(ADC4SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   479  #endif
; ..\mcal_src\Adc_Irq.c	   480  {
; ..\mcal_src\Adc_Irq.c	   481       
; ..\mcal_src\Adc_Irq.c	   482    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   483  #if (IRQ_ADC4_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   484    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   485  #endif
; ..\mcal_src\Adc_Irq.c	   486  
; ..\mcal_src\Adc_Irq.c	   487  /* ADC4 SRN1 is used by request source 1 of ADC4 */
; ..\mcal_src\Adc_Irq.c	   488  #if (ADC4_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   489    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   490    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC4);
; ..\mcal_src\Adc_Irq.c	   491  #endif
; ..\mcal_src\Adc_Irq.c	   492  }
; ..\mcal_src\Adc_Irq.c	   493  #endif
; ..\mcal_src\Adc_Irq.c	   494  
; ..\mcal_src\Adc_Irq.c	   495  #endif /* (IRQ_ADC4_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   496  
; ..\mcal_src\Adc_Irq.c	   497  #if (IRQ_ADC5_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   498  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   499  ** Syntax :          void ADC5SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   500  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   501  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   502  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   503  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   504  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   505  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   506  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   507  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   508  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   509  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   510  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   511  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   512  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   513  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   514  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   515  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   516  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   517  #if((IRQ_ADC5_SR0_PRIO > 0) || (IRQ_ADC5_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   518  #if((IRQ_ADC5_SR0_PRIO > 0) && (IRQ_ADC5_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   519  IFX_INTERRUPT(ADC5SR0_ISR,0,IRQ_ADC5_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   520  #elif IRQ_ADC5_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   521  ISR(ADC5SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   522  #endif
; ..\mcal_src\Adc_Irq.c	   523  {
; ..\mcal_src\Adc_Irq.c	   524    
; ..\mcal_src\Adc_Irq.c	   525    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   526  #if (IRQ_ADC5_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   527    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   528  #endif
; ..\mcal_src\Adc_Irq.c	   529  
; ..\mcal_src\Adc_Irq.c	   530  /* ADC5 SRN0 is used by request source 0 of ADC5 */
; ..\mcal_src\Adc_Irq.c	   531  #if (ADC5_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   532    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   533    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC5);
; ..\mcal_src\Adc_Irq.c	   534  #endif
; ..\mcal_src\Adc_Irq.c	   535  
; ..\mcal_src\Adc_Irq.c	   536  }
; ..\mcal_src\Adc_Irq.c	   537  #endif
; ..\mcal_src\Adc_Irq.c	   538  
; ..\mcal_src\Adc_Irq.c	   539  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   540  ** Syntax :          void ADC5SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   541  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   542  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   543  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   544  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   545  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   546  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   547  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   548  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   549  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   550  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   551  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   552  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   553  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   554  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   555  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   556  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   557  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   558  #if((IRQ_ADC5_SR1_PRIO > 0) || (IRQ_ADC5_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   559  #if((IRQ_ADC5_SR1_PRIO > 0) && (IRQ_ADC5_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   560  IFX_INTERRUPT(ADC5SR1_ISR,0,IRQ_ADC5_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   561  #elif IRQ_ADC5_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   562  ISR(ADC5SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   563  #endif
; ..\mcal_src\Adc_Irq.c	   564  {
; ..\mcal_src\Adc_Irq.c	   565       
; ..\mcal_src\Adc_Irq.c	   566    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   567  #if (IRQ_ADC5_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   568    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   569  #endif
; ..\mcal_src\Adc_Irq.c	   570  
; ..\mcal_src\Adc_Irq.c	   571  /* ADC5 SRN1 is used by request source 1 of ADC5 */
; ..\mcal_src\Adc_Irq.c	   572  #if (ADC5_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   573    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   574    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC5);
; ..\mcal_src\Adc_Irq.c	   575  #endif
; ..\mcal_src\Adc_Irq.c	   576  }
; ..\mcal_src\Adc_Irq.c	   577  #endif
; ..\mcal_src\Adc_Irq.c	   578  
; ..\mcal_src\Adc_Irq.c	   579  #endif /* (IRQ_ADC5_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   580  
; ..\mcal_src\Adc_Irq.c	   581  #if (IRQ_ADC6_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   582  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   583  ** Syntax :          void ADC6SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   584  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   585  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   586  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   587  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   588  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   589  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   590  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   591  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   592  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   593  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   594  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   595  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   596  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   597  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   598  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   599  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   600  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   601  #if((IRQ_ADC6_SR0_PRIO > 0) || (IRQ_ADC6_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   602  #if((IRQ_ADC6_SR0_PRIO > 0) && (IRQ_ADC6_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   603  IFX_INTERRUPT(ADC6SR0_ISR,0,IRQ_ADC6_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   604  #elif IRQ_ADC6_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   605  ISR(ADC6SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   606  #endif
; ..\mcal_src\Adc_Irq.c	   607  {
; ..\mcal_src\Adc_Irq.c	   608    
; ..\mcal_src\Adc_Irq.c	   609    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   610  #if (IRQ_ADC6_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   611    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   612  #endif
; ..\mcal_src\Adc_Irq.c	   613  
; ..\mcal_src\Adc_Irq.c	   614  /* ADC6 SRN0 is used by request source 0 of ADC6 */
; ..\mcal_src\Adc_Irq.c	   615  #if (ADC6_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   616    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   617    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC6);
; ..\mcal_src\Adc_Irq.c	   618  #endif
; ..\mcal_src\Adc_Irq.c	   619  
; ..\mcal_src\Adc_Irq.c	   620  }
; ..\mcal_src\Adc_Irq.c	   621  #endif
; ..\mcal_src\Adc_Irq.c	   622  
; ..\mcal_src\Adc_Irq.c	   623  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   624  ** Syntax :          void ADC6SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   625  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   626  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   627  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   628  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   629  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   630  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   631  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   632  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   633  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   634  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   635  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   636  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   637  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   638  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   639  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   640  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   641  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   642  #if((IRQ_ADC6_SR1_PRIO > 0) || (IRQ_ADC6_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   643  #if((IRQ_ADC6_SR1_PRIO > 0) && (IRQ_ADC6_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   644  IFX_INTERRUPT(ADC6SR1_ISR,0,IRQ_ADC6_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   645  #elif IRQ_ADC6_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   646  ISR(ADC6SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   647  #endif
; ..\mcal_src\Adc_Irq.c	   648  {
; ..\mcal_src\Adc_Irq.c	   649       
; ..\mcal_src\Adc_Irq.c	   650    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   651  #if (IRQ_ADC6_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   652    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   653  #endif
; ..\mcal_src\Adc_Irq.c	   654  
; ..\mcal_src\Adc_Irq.c	   655  /* ADC6 SRN1 is used by request source 1 of ADC6 */
; ..\mcal_src\Adc_Irq.c	   656  #if (ADC6_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   657    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   658    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC6);
; ..\mcal_src\Adc_Irq.c	   659  #endif
; ..\mcal_src\Adc_Irq.c	   660  }
; ..\mcal_src\Adc_Irq.c	   661  #endif
; ..\mcal_src\Adc_Irq.c	   662  
; ..\mcal_src\Adc_Irq.c	   663  #endif /* (IRQ_ADC6_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   664  
; ..\mcal_src\Adc_Irq.c	   665  #if (IRQ_ADC7_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   666  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   667  ** Syntax :          void ADC7SR0_ISR(void)                                 **
; ..\mcal_src\Adc_Irq.c	   668  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   669  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   670  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   671  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   672  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   673  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   674  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   675  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   676  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   677  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   678  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   679  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   680  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   681  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   682  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   683  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   684  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   685  #if((IRQ_ADC7_SR0_PRIO > 0) || (IRQ_ADC7_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   686  #if((IRQ_ADC7_SR0_PRIO > 0) && (IRQ_ADC7_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   687  IFX_INTERRUPT(ADC7SR0_ISR,0,IRQ_ADC7_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   688  #elif IRQ_ADC7_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   689  ISR(ADC7SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   690  #endif
; ..\mcal_src\Adc_Irq.c	   691  {
; ..\mcal_src\Adc_Irq.c	   692    
; ..\mcal_src\Adc_Irq.c	   693    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   694  #if (IRQ_ADC7_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   695    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   696  #endif
; ..\mcal_src\Adc_Irq.c	   697  
; ..\mcal_src\Adc_Irq.c	   698  /* ADC7 SRN0 is used by request source 0 of ADC7 */
; ..\mcal_src\Adc_Irq.c	   699  #if (ADC7_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   700    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   701    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC7);
; ..\mcal_src\Adc_Irq.c	   702  #endif
; ..\mcal_src\Adc_Irq.c	   703  
; ..\mcal_src\Adc_Irq.c	   704  }
; ..\mcal_src\Adc_Irq.c	   705  #endif
; ..\mcal_src\Adc_Irq.c	   706  
; ..\mcal_src\Adc_Irq.c	   707  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   708  ** Syntax :          void ADC7SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   709  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   710  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   711  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   712  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   713  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   714  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   715  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   716  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   717  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   718  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   719  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   720  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   721  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   722  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   723  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   724  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   725  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   726  #if((IRQ_ADC7_SR1_PRIO > 0) || (IRQ_ADC7_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   727  #if((IRQ_ADC7_SR1_PRIO > 0) && (IRQ_ADC7_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   728  IFX_INTERRUPT(ADC7SR1_ISR,0,IRQ_ADC7_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   729  #elif IRQ_ADC7_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   730  ISR(ADC7SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   731  #endif
; ..\mcal_src\Adc_Irq.c	   732  {
; ..\mcal_src\Adc_Irq.c	   733       
; ..\mcal_src\Adc_Irq.c	   734    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   735  #if (IRQ_ADC7_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   736    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   737  #endif
; ..\mcal_src\Adc_Irq.c	   738  
; ..\mcal_src\Adc_Irq.c	   739  /* ADC7 SRN1 is used by request source 1 of ADC7 */
; ..\mcal_src\Adc_Irq.c	   740  #if (ADC7_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   741    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   742    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC7);
; ..\mcal_src\Adc_Irq.c	   743  #endif
; ..\mcal_src\Adc_Irq.c	   744  }
; ..\mcal_src\Adc_Irq.c	   745  #endif
; ..\mcal_src\Adc_Irq.c	   746  
; ..\mcal_src\Adc_Irq.c	   747  #endif /* (IRQ_ADC7_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   748  
; ..\mcal_src\Adc_Irq.c	   749  #if (IRQ_ADC8_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   750  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   751  ** Syntax :          void ADC8SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   752  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   753  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   754  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   755  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   756  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   757  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   758  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   759  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   760  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   761  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   762  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   763  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   764  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   765  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   766  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   767  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   768  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   769  #if((IRQ_ADC8_SR0_PRIO > 0) || (IRQ_ADC8_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   770  #if((IRQ_ADC8_SR0_PRIO > 0) && (IRQ_ADC8_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   771  IFX_INTERRUPT(ADC8SR0_ISR,0,IRQ_ADC8_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   772  #elif IRQ_ADC8_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   773  ISR(ADC8SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   774  #endif
; ..\mcal_src\Adc_Irq.c	   775  {
; ..\mcal_src\Adc_Irq.c	   776    
; ..\mcal_src\Adc_Irq.c	   777    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   778  #if (IRQ_ADC8_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   779    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   780  #endif
; ..\mcal_src\Adc_Irq.c	   781  
; ..\mcal_src\Adc_Irq.c	   782  /* ADC8 SRN0 is used by request source 0 of ADC8 */
; ..\mcal_src\Adc_Irq.c	   783  #if (ADC8_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   784    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   785    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC8);
; ..\mcal_src\Adc_Irq.c	   786  #endif
; ..\mcal_src\Adc_Irq.c	   787  
; ..\mcal_src\Adc_Irq.c	   788  }
; ..\mcal_src\Adc_Irq.c	   789  #endif
; ..\mcal_src\Adc_Irq.c	   790  
; ..\mcal_src\Adc_Irq.c	   791  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   792  ** Syntax :          void ADC8SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   793  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   794  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   795  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   796  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   797  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   798  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   799  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   800  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   801  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   802  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   803  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   804  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   805  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   806  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   807  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   808  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   809  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   810  #if((IRQ_ADC8_SR1_PRIO > 0) || (IRQ_ADC8_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   811  #if((IRQ_ADC8_SR1_PRIO > 0) && (IRQ_ADC8_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   812  IFX_INTERRUPT(ADC8SR1_ISR,0,IRQ_ADC8_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   813  #elif IRQ_ADC8_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   814  ISR(ADC8SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   815  #endif
; ..\mcal_src\Adc_Irq.c	   816  {
; ..\mcal_src\Adc_Irq.c	   817       
; ..\mcal_src\Adc_Irq.c	   818    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   819  #if (IRQ_ADC8_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   820    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   821  #endif
; ..\mcal_src\Adc_Irq.c	   822  
; ..\mcal_src\Adc_Irq.c	   823  /* ADC8 SRN1 is used by request source 1 of ADC8 */
; ..\mcal_src\Adc_Irq.c	   824  #if (ADC8_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   825    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   826    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC8);
; ..\mcal_src\Adc_Irq.c	   827  #endif
; ..\mcal_src\Adc_Irq.c	   828  }
; ..\mcal_src\Adc_Irq.c	   829  #endif
; ..\mcal_src\Adc_Irq.c	   830  
; ..\mcal_src\Adc_Irq.c	   831  #endif /* (IRQ_ADC8_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   832  
; ..\mcal_src\Adc_Irq.c	   833  #if (IRQ_ADC9_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   834  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   835  ** Syntax :          void ADC9SR0_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   836  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   837  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   838  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   839  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   840  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   841  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   842  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   843  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   844  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   845  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   846  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   847  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   848  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   849  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   850  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   851  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   852  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   853  #if((IRQ_ADC9_SR0_PRIO > 0) || (IRQ_ADC9_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   854  #if((IRQ_ADC9_SR0_PRIO > 0) && (IRQ_ADC9_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   855  IFX_INTERRUPT(ADC9SR0_ISR,0,IRQ_ADC9_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   856  #elif IRQ_ADC9_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   857  ISR(ADC9SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   858  #endif
; ..\mcal_src\Adc_Irq.c	   859  {
; ..\mcal_src\Adc_Irq.c	   860    
; ..\mcal_src\Adc_Irq.c	   861    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   862  #if (IRQ_ADC9_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   863    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   864  #endif
; ..\mcal_src\Adc_Irq.c	   865  
; ..\mcal_src\Adc_Irq.c	   866  /* ADC9 SRN0 is used by request source 0 of ADC9 */
; ..\mcal_src\Adc_Irq.c	   867  #if (ADC9_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   868    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   869    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC9);
; ..\mcal_src\Adc_Irq.c	   870  #endif
; ..\mcal_src\Adc_Irq.c	   871  
; ..\mcal_src\Adc_Irq.c	   872  }
; ..\mcal_src\Adc_Irq.c	   873  #endif
; ..\mcal_src\Adc_Irq.c	   874  
; ..\mcal_src\Adc_Irq.c	   875  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   876  ** Syntax :          void ADC9SR1_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	   877  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   878  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   879  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   880  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   881  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   882  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   883  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   884  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   885  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   886  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   887  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   888  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   889  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   890  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   891  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   892  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   893  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   894  #if((IRQ_ADC9_SR1_PRIO > 0) || (IRQ_ADC9_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   895  #if((IRQ_ADC9_SR1_PRIO > 0) && (IRQ_ADC9_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   896  IFX_INTERRUPT(ADC9SR1_ISR,0,IRQ_ADC9_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   897  #elif IRQ_ADC9_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   898  ISR(ADC9SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   899  #endif
; ..\mcal_src\Adc_Irq.c	   900  {
; ..\mcal_src\Adc_Irq.c	   901       
; ..\mcal_src\Adc_Irq.c	   902    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   903  #if (IRQ_ADC9_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   904    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   905  #endif
; ..\mcal_src\Adc_Irq.c	   906  
; ..\mcal_src\Adc_Irq.c	   907  /* ADC9 SRN1 is used by request source 1 of ADC9 */
; ..\mcal_src\Adc_Irq.c	   908  #if (ADC9_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   909    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   910    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC9);
; ..\mcal_src\Adc_Irq.c	   911  #endif
; ..\mcal_src\Adc_Irq.c	   912  }
; ..\mcal_src\Adc_Irq.c	   913  #endif
; ..\mcal_src\Adc_Irq.c	   914  
; ..\mcal_src\Adc_Irq.c	   915  #endif /* (IRQ_ADC9_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	   916  
; ..\mcal_src\Adc_Irq.c	   917  #if (IRQ_ADC10_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	   918  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   919  ** Syntax :          void ADC10SR0_ISR(void)                                 **
; ..\mcal_src\Adc_Irq.c	   920  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   921  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   922  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   923  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   924  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   925  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   926  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   927  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   928  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   929  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   930  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   931  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   932  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   933  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   934  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   935  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   936  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   937  #if((IRQ_ADC10_SR0_PRIO > 0) || (IRQ_ADC10_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   938  #if((IRQ_ADC10_SR0_PRIO > 0) && (IRQ_ADC10_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   939  IFX_INTERRUPT(ADC10SR0_ISR,0,IRQ_ADC10_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	   940  #elif IRQ_ADC10_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   941  ISR(ADC10SR0_ISR)
; ..\mcal_src\Adc_Irq.c	   942  #endif
; ..\mcal_src\Adc_Irq.c	   943  {
; ..\mcal_src\Adc_Irq.c	   944    
; ..\mcal_src\Adc_Irq.c	   945    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   946  #if (IRQ_ADC10_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   947    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   948  #endif
; ..\mcal_src\Adc_Irq.c	   949  
; ..\mcal_src\Adc_Irq.c	   950  /* ADC10 SRN0 is used by request source 0 of ADC10 */
; ..\mcal_src\Adc_Irq.c	   951  #if (ADC10_REQSRC0 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   952    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   953    Adc_IsrSrn0AdcRS0(ADC_HWUNIT_ADC10);
; ..\mcal_src\Adc_Irq.c	   954  #endif
; ..\mcal_src\Adc_Irq.c	   955  
; ..\mcal_src\Adc_Irq.c	   956  }
; ..\mcal_src\Adc_Irq.c	   957  #endif
; ..\mcal_src\Adc_Irq.c	   958  
; ..\mcal_src\Adc_Irq.c	   959  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	   960  ** Syntax :          void ADC10SR1_ISR(void)                                 **
; ..\mcal_src\Adc_Irq.c	   961  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   962  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	   963  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   964  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	   965  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   966  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	   967  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   968  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	   969  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   970  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	   971  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   972  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	   973  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   974  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	   975  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	   976  **                                                                           **
; ..\mcal_src\Adc_Irq.c	   977  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	   978  #if((IRQ_ADC10_SR1_PRIO > 0) || (IRQ_ADC10_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	   979  #if((IRQ_ADC10_SR1_PRIO > 0) && (IRQ_ADC10_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	   980  IFX_INTERRUPT(ADC10SR1_ISR,0,IRQ_ADC10_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	   981  #elif IRQ_ADC10_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	   982  ISR(ADC10SR1_ISR)
; ..\mcal_src\Adc_Irq.c	   983  #endif
; ..\mcal_src\Adc_Irq.c	   984  {
; ..\mcal_src\Adc_Irq.c	   985       
; ..\mcal_src\Adc_Irq.c	   986    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	   987  #if (IRQ_ADC10_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	   988    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	   989  #endif
; ..\mcal_src\Adc_Irq.c	   990  
; ..\mcal_src\Adc_Irq.c	   991  /* ADC10 SRN1 is used by request source 1 of ADC10 */
; ..\mcal_src\Adc_Irq.c	   992  #if (ADC10_REQSRC1 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	   993    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	   994    Adc_IsrSrn1AdcRS1(ADC_HWUNIT_ADC10);
; ..\mcal_src\Adc_Irq.c	   995  #endif
; ..\mcal_src\Adc_Irq.c	   996  }
; ..\mcal_src\Adc_Irq.c	   997  #endif
; ..\mcal_src\Adc_Irq.c	   998  
; ..\mcal_src\Adc_Irq.c	   999  #endif /* (IRQ_ADC10_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1000  
; ..\mcal_src\Adc_Irq.c	  1001  #if (IRQ_ADCCG0_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1002  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1003  ** Syntax :          void ADCCG0SR0_ISR(void)                                **
; ..\mcal_src\Adc_Irq.c	  1004  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1005  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1006  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1007  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1008  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1009  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1010  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1011  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1012  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1013  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1014  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1015  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1016  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1017  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1018  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1019  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1020  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1021  #if((IRQ_ADCCG0_SR0_PRIO > 0) || (IRQ_ADCCG0_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1022  #if((IRQ_ADCCG0_SR0_PRIO > 0) && (IRQ_ADCCG0_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1023  IFX_INTERRUPT(ADCCG0SR0_ISR,0,IRQ_ADCCG0_SR0_PRIO)
; ..\mcal_src\Adc_Irq.c	  1024  #elif IRQ_ADCCG0_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1025  ISR(ADCCG0SR0_ISR)
; ..\mcal_src\Adc_Irq.c	  1026  #endif
; ..\mcal_src\Adc_Irq.c	  1027  {
; ..\mcal_src\Adc_Irq.c	  1028       
; ..\mcal_src\Adc_Irq.c	  1029    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1030  #if (IRQ_ADCCG0_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1031    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1032  #endif
; ..\mcal_src\Adc_Irq.c	  1033  
; ..\mcal_src\Adc_Irq.c	  1034  /* CG0 SRN0 is used by request source 2 of All ADC kernels */
; ..\mcal_src\Adc_Irq.c	  1035  #if ( (ADC0_REQSRC2 == ADC_REQSRC_USED) || (ADC1_REQSRC2 == ADC_REQSRC_USED)||\ 
; ..\mcal_src\Adc_Irq.c	  1036        (ADC2_REQSRC2 == ADC_REQSRC_USED) || (ADC3_REQSRC2 == ADC_REQSRC_USED)||\ 
; ..\mcal_src\Adc_Irq.c	  1037        (ADC4_REQSRC2 == ADC_REQSRC_USED) || (ADC5_REQSRC2 == ADC_REQSRC_USED)||\ 
; ..\mcal_src\Adc_Irq.c	  1038        (ADC6_REQSRC2 == ADC_REQSRC_USED) || (ADC7_REQSRC2 == ADC_REQSRC_USED)||\ 
; ..\mcal_src\Adc_Irq.c	  1039        (ADC8_REQSRC2 == ADC_REQSRC_USED) || (ADC9_REQSRC2 == ADC_REQSRC_USED)||\ 
; ..\mcal_src\Adc_Irq.c	  1040        (ADC10_REQSRC2 == ADC_REQSRC_USED) )
; ..\mcal_src\Adc_Irq.c	  1041    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1042    Adc_IsrSrn0CG0AdcRS2();
; ..\mcal_src\Adc_Irq.c	  1043  #endif
; ..\mcal_src\Adc_Irq.c	  1044  }
; ..\mcal_src\Adc_Irq.c	  1045  #endif
; ..\mcal_src\Adc_Irq.c	  1046  
; ..\mcal_src\Adc_Irq.c	  1047  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1048  ** Syntax :          void ADCCG0SR1_ISR(void)                                **
; ..\mcal_src\Adc_Irq.c	  1049  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1050  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1051  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1052  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1053  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1054  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1055  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1056  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1057  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1058  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1059  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1060  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1061  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1062  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1063  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1064  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1065  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1066  #if((IRQ_ADCCG0_SR1_PRIO > 0) || (IRQ_ADCCG0_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1067  #if((IRQ_ADCCG0_SR1_PRIO > 0) && (IRQ_ADCCG0_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1068  IFX_INTERRUPT(ADCCG0SR1_ISR,0,IRQ_ADCCG0_SR1_PRIO)
; ..\mcal_src\Adc_Irq.c	  1069  #elif IRQ_ADCCG0_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1070  ISR(ADCCG0SR1_ISR)
; ..\mcal_src\Adc_Irq.c	  1071  #endif
; ..\mcal_src\Adc_Irq.c	  1072  {
; ..\mcal_src\Adc_Irq.c	  1073       
; ..\mcal_src\Adc_Irq.c	  1074    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1075  #if (IRQ_ADCCG0_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1076    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1077  #endif
; ..\mcal_src\Adc_Irq.c	  1078  
; ..\mcal_src\Adc_Irq.c	  1079  /* CG0 SRN1 is used by request source 3 of ADC0 */
; ..\mcal_src\Adc_Irq.c	  1080  #if (ADC0_REQSRC3 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Irq.c	  1081    /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1082    Adc_IsrSrn1CG0AdcRS3(ADC_HWUNIT_ADC0);
; ..\mcal_src\Adc_Irq.c	  1083  #endif
; ..\mcal_src\Adc_Irq.c	  1084  }
; ..\mcal_src\Adc_Irq.c	  1085  #endif
; ..\mcal_src\Adc_Irq.c	  1086  
; ..\mcal_src\Adc_Irq.c	  1087  #endif /* (IRQ_ADCCG0_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1088  
; ..\mcal_src\Adc_Irq.c	  1089  #if (IRQ_ADC0_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1090  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1091  ** Syntax :          void ADC0SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1092  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1093  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1094  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1095  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1096  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1097  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1098  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1099  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1100  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1101  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1102  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1103  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1104  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1105  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1106  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1107  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1108  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1109  #if((IRQ_ADC0_SR2_PRIO > 0) || (IRQ_ADC0_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1110  #if((IRQ_ADC0_SR2_PRIO > 0) && (IRQ_ADC0_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1111  IFX_INTERRUPT(ADC0SR2_ISR,0,IRQ_ADC0_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1112  #elif IRQ_ADC0_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1113  ISR(ADC0SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1114  #endif
; ..\mcal_src\Adc_Irq.c	  1115  {
; ..\mcal_src\Adc_Irq.c	  1116       
; ..\mcal_src\Adc_Irq.c	  1117    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1118  #if (IRQ_ADC0_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1119    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1120  #endif
; ..\mcal_src\Adc_Irq.c	  1121  
; ..\mcal_src\Adc_Irq.c	  1122  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1123  /* ADC0 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1124  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1125    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC0);
; ..\mcal_src\Adc_Irq.c	  1126  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1127  }
; ..\mcal_src\Adc_Irq.c	  1128  #endif
; ..\mcal_src\Adc_Irq.c	  1129  
; ..\mcal_src\Adc_Irq.c	  1130  #endif /* (IRQ_ADC0_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1131  
; ..\mcal_src\Adc_Irq.c	  1132  #if (IRQ_ADC1_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1133  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1134  ** Syntax :          void ADC1SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1135  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1136  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1137  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1138  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1139  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1140  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1141  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1142  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1143  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1144  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1145  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1146  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1147  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1148  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1149  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1150  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1151  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1152  #if((IRQ_ADC1_SR2_PRIO > 0) || (IRQ_ADC1_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1153  #if((IRQ_ADC1_SR2_PRIO > 0) && (IRQ_ADC1_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1154  IFX_INTERRUPT(ADC1SR2_ISR,0,IRQ_ADC1_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1155  #elif IRQ_ADC1_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1156  ISR(ADC1SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1157  #endif
; ..\mcal_src\Adc_Irq.c	  1158  {
; ..\mcal_src\Adc_Irq.c	  1159       
; ..\mcal_src\Adc_Irq.c	  1160    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1161  #if (IRQ_ADC1_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1162    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1163  #endif
; ..\mcal_src\Adc_Irq.c	  1164  
; ..\mcal_src\Adc_Irq.c	  1165  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1166  /* ADC1 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1167  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1168    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC1);
; ..\mcal_src\Adc_Irq.c	  1169  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1170  }
; ..\mcal_src\Adc_Irq.c	  1171  #endif
; ..\mcal_src\Adc_Irq.c	  1172  
; ..\mcal_src\Adc_Irq.c	  1173  #endif /* (IRQ_ADC1_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1174  
; ..\mcal_src\Adc_Irq.c	  1175  #if (IRQ_ADC2_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1176  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1177  ** Syntax :          void ADC2SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1178  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1179  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1180  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1181  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1182  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1183  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1184  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1185  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1186  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1187  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1188  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1189  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1190  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1191  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1192  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1193  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1194  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1195  #if((IRQ_ADC2_SR2_PRIO > 0) || (IRQ_ADC2_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1196  #if((IRQ_ADC2_SR2_PRIO > 0) && (IRQ_ADC2_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1197  IFX_INTERRUPT(ADC2SR2_ISR,0,IRQ_ADC2_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1198  #elif IRQ_ADC2_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1199  ISR(ADC2SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1200  #endif
; ..\mcal_src\Adc_Irq.c	  1201  {
; ..\mcal_src\Adc_Irq.c	  1202       
; ..\mcal_src\Adc_Irq.c	  1203    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1204  #if (IRQ_ADC2_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1205    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1206  #endif
; ..\mcal_src\Adc_Irq.c	  1207  
; ..\mcal_src\Adc_Irq.c	  1208  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1209  /* ADC2 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1210  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1211    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC2);
; ..\mcal_src\Adc_Irq.c	  1212  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1213  }
; ..\mcal_src\Adc_Irq.c	  1214  #endif
; ..\mcal_src\Adc_Irq.c	  1215  
; ..\mcal_src\Adc_Irq.c	  1216  #endif /* (IRQ_ADC2_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1217  
; ..\mcal_src\Adc_Irq.c	  1218  #if (IRQ_ADC3_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1219  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1220  ** Syntax :          void ADC3SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1221  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1222  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1223  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1224  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1225  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1226  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1227  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1228  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1229  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1230  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1231  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1232  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1233  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1234  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1235  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1236  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1237  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1238  #if((IRQ_ADC3_SR2_PRIO > 0) || (IRQ_ADC3_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1239  #if((IRQ_ADC3_SR2_PRIO > 0) && (IRQ_ADC3_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1240  IFX_INTERRUPT(ADC3SR2_ISR,0,IRQ_ADC3_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1241  #elif IRQ_ADC3_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1242  ISR(ADC3SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1243  #endif
; ..\mcal_src\Adc_Irq.c	  1244  {
; ..\mcal_src\Adc_Irq.c	  1245       
; ..\mcal_src\Adc_Irq.c	  1246    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1247  #if (IRQ_ADC3_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1248    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1249  #endif
; ..\mcal_src\Adc_Irq.c	  1250  
; ..\mcal_src\Adc_Irq.c	  1251  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1252  /* ADC3 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1253  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1254    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC3);
; ..\mcal_src\Adc_Irq.c	  1255  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1256  }
; ..\mcal_src\Adc_Irq.c	  1257  #endif
; ..\mcal_src\Adc_Irq.c	  1258  
; ..\mcal_src\Adc_Irq.c	  1259  #endif /* (IRQ_ADC3_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1260  
; ..\mcal_src\Adc_Irq.c	  1261  #if (IRQ_ADC4_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1262  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1263  ** Syntax :          void ADC4SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1264  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1265  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1266  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1267  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1268  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1269  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1270  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1271  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1272  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1273  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1274  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1275  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1276  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1277  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1278  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1279  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1280  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1281  #if((IRQ_ADC4_SR2_PRIO > 0) || (IRQ_ADC4_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1282  #if((IRQ_ADC4_SR2_PRIO > 0) && (IRQ_ADC4_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1283  IFX_INTERRUPT(ADC4SR2_ISR,0,IRQ_ADC4_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1284  #elif IRQ_ADC4_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1285  ISR(ADC4SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1286  #endif
; ..\mcal_src\Adc_Irq.c	  1287  {
; ..\mcal_src\Adc_Irq.c	  1288       
; ..\mcal_src\Adc_Irq.c	  1289    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1290  #if (IRQ_ADC4_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1291    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1292  #endif
; ..\mcal_src\Adc_Irq.c	  1293  
; ..\mcal_src\Adc_Irq.c	  1294  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1295  /* ADC4 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1296  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1297    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC4);
; ..\mcal_src\Adc_Irq.c	  1298  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1299  }
; ..\mcal_src\Adc_Irq.c	  1300  #endif
; ..\mcal_src\Adc_Irq.c	  1301  
; ..\mcal_src\Adc_Irq.c	  1302  #endif /* (IRQ_ADC4_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1303  
; ..\mcal_src\Adc_Irq.c	  1304  #if (IRQ_ADC5_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1305  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1306  ** Syntax :          void ADC5SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1307  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1308  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1309  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1310  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1311  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1312  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1313  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1314  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1315  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1316  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1317  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1318  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1319  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1320  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1321  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1322  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1323  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1324  #if((IRQ_ADC5_SR2_PRIO > 0) || (IRQ_ADC5_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1325  #if((IRQ_ADC5_SR2_PRIO > 0) && (IRQ_ADC5_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1326  IFX_INTERRUPT(ADC5SR2_ISR,0,IRQ_ADC5_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1327  #elif IRQ_ADC5_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1328  ISR(ADC5SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1329  #endif
; ..\mcal_src\Adc_Irq.c	  1330  {
; ..\mcal_src\Adc_Irq.c	  1331       
; ..\mcal_src\Adc_Irq.c	  1332    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1333  #if (IRQ_ADC5_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1334    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1335  #endif
; ..\mcal_src\Adc_Irq.c	  1336  
; ..\mcal_src\Adc_Irq.c	  1337  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1338  /* ADC5 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1339  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1340    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC5);
; ..\mcal_src\Adc_Irq.c	  1341  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1342  }
; ..\mcal_src\Adc_Irq.c	  1343  #endif
; ..\mcal_src\Adc_Irq.c	  1344  
; ..\mcal_src\Adc_Irq.c	  1345  #endif /* (IRQ_ADC5_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1346  
; ..\mcal_src\Adc_Irq.c	  1347  #if (IRQ_ADC6_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1348  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1349  ** Syntax :          void ADC6SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1350  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1351  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1352  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1353  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1354  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1355  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1356  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1357  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1358  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1359  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1360  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1361  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1362  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1363  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1364  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1365  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1366  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1367  #if((IRQ_ADC6_SR2_PRIO > 0) || (IRQ_ADC6_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1368  #if((IRQ_ADC6_SR2_PRIO > 0) && (IRQ_ADC6_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1369  IFX_INTERRUPT(ADC6SR2_ISR,0,IRQ_ADC6_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1370  #elif IRQ_ADC6_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1371  ISR(ADC6SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1372  #endif
; ..\mcal_src\Adc_Irq.c	  1373  {
; ..\mcal_src\Adc_Irq.c	  1374       
; ..\mcal_src\Adc_Irq.c	  1375    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1376  #if (IRQ_ADC6_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1377    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1378  #endif
; ..\mcal_src\Adc_Irq.c	  1379  
; ..\mcal_src\Adc_Irq.c	  1380  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1381  /* ADC6 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1382  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1383    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC6);
; ..\mcal_src\Adc_Irq.c	  1384  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1385  }
; ..\mcal_src\Adc_Irq.c	  1386  #endif
; ..\mcal_src\Adc_Irq.c	  1387  
; ..\mcal_src\Adc_Irq.c	  1388  #endif /* (IRQ_ADC6_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1389  
; ..\mcal_src\Adc_Irq.c	  1390  #if (IRQ_ADC7_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1391  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1392  ** Syntax :          void ADC7SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1393  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1394  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1395  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1396  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1397  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1398  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1399  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1400  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1401  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1402  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1403  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1404  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1405  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1406  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1407  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1408  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1409  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1410  #if((IRQ_ADC7_SR2_PRIO > 0) || (IRQ_ADC7_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1411  #if((IRQ_ADC7_SR2_PRIO > 0) && (IRQ_ADC7_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1412  IFX_INTERRUPT(ADC7SR2_ISR,0,IRQ_ADC7_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1413  #elif IRQ_ADC7_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1414  ISR(ADC7SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1415  #endif
; ..\mcal_src\Adc_Irq.c	  1416  {
; ..\mcal_src\Adc_Irq.c	  1417       
; ..\mcal_src\Adc_Irq.c	  1418    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1419  #if (IRQ_ADC7_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1420    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1421  #endif
; ..\mcal_src\Adc_Irq.c	  1422  
; ..\mcal_src\Adc_Irq.c	  1423  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1424  /* ADC7 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1425  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1426    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC7);
; ..\mcal_src\Adc_Irq.c	  1427  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1428  }
; ..\mcal_src\Adc_Irq.c	  1429  #endif
; ..\mcal_src\Adc_Irq.c	  1430  
; ..\mcal_src\Adc_Irq.c	  1431  #endif /* (IRQ_ADC7_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1432  
; ..\mcal_src\Adc_Irq.c	  1433  #if (IRQ_ADC8_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1434  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1435  ** Syntax :          void ADC8SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1436  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1437  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1438  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1439  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1440  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1441  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1442  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1443  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1444  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1445  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1446  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1447  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1448  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1449  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1450  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1451  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1452  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1453  #if((IRQ_ADC8_SR2_PRIO > 0) || (IRQ_ADC8_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1454  #if((IRQ_ADC8_SR2_PRIO > 0) && (IRQ_ADC8_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1455  IFX_INTERRUPT(ADC8SR2_ISR,0,IRQ_ADC8_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1456  #elif IRQ_ADC8_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1457  ISR(ADC8SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1458  #endif
; ..\mcal_src\Adc_Irq.c	  1459  {
; ..\mcal_src\Adc_Irq.c	  1460       
; ..\mcal_src\Adc_Irq.c	  1461    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1462  #if (IRQ_ADC8_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1463    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1464  #endif
; ..\mcal_src\Adc_Irq.c	  1465  
; ..\mcal_src\Adc_Irq.c	  1466  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1467  /* ADC8 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1468  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1469    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC8);
; ..\mcal_src\Adc_Irq.c	  1470  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1471  }
; ..\mcal_src\Adc_Irq.c	  1472  #endif
; ..\mcal_src\Adc_Irq.c	  1473  
; ..\mcal_src\Adc_Irq.c	  1474  #endif /* (IRQ_ADC8_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1475  
; ..\mcal_src\Adc_Irq.c	  1476  #if (IRQ_ADC9_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1477  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1478  ** Syntax :          void ADC9SR2_ISR(void)                                  **
; ..\mcal_src\Adc_Irq.c	  1479  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1480  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1481  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1482  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1483  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1484  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1485  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1486  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1487  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1488  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1489  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1490  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1491  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1492  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1493  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1494  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1495  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1496  #if((IRQ_ADC9_SR2_PRIO > 0) || (IRQ_ADC9_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1497  #if((IRQ_ADC9_SR2_PRIO > 0) && (IRQ_ADC9_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1498  IFX_INTERRUPT(ADC9SR2_ISR,0,IRQ_ADC9_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1499  #elif IRQ_ADC9_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1500  ISR(ADC9SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1501  #endif
; ..\mcal_src\Adc_Irq.c	  1502  {
; ..\mcal_src\Adc_Irq.c	  1503       
; ..\mcal_src\Adc_Irq.c	  1504    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1505  #if (IRQ_ADC9_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1506    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1507  #endif
; ..\mcal_src\Adc_Irq.c	  1508  
; ..\mcal_src\Adc_Irq.c	  1509  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1510  /* ADC9 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1511  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1512    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC9);
; ..\mcal_src\Adc_Irq.c	  1513  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1514  }
; ..\mcal_src\Adc_Irq.c	  1515  #endif
; ..\mcal_src\Adc_Irq.c	  1516  
; ..\mcal_src\Adc_Irq.c	  1517  #endif /* (IRQ_ADC9_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1518  
; ..\mcal_src\Adc_Irq.c	  1519  #if (IRQ_ADC10_EXIST == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1520  /******************************************************************************
; ..\mcal_src\Adc_Irq.c	  1521  ** Syntax :          void ADC10SR2_ISR(void)                                 **
; ..\mcal_src\Adc_Irq.c	  1522  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1523  ** Service ID:       NA                                                      **
; ..\mcal_src\Adc_Irq.c	  1524  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1525  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Adc_Irq.c	  1526  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1527  ** Reentrancy:       non reentrant                                           **
; ..\mcal_src\Adc_Irq.c	  1528  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1529  ** Parameters (in):  none                                                    **
; ..\mcal_src\Adc_Irq.c	  1530  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1531  ** Parameters (out): none                                                    **
; ..\mcal_src\Adc_Irq.c	  1532  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1533  ** Return value:     none                                                    **
; ..\mcal_src\Adc_Irq.c	  1534  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1535  ** Description :     Service on ADC Request source conversion complete       **
; ..\mcal_src\Adc_Irq.c	  1536  **                   service request                                         **
; ..\mcal_src\Adc_Irq.c	  1537  **                                                                           **
; ..\mcal_src\Adc_Irq.c	  1538  ******************************************************************************/
; ..\mcal_src\Adc_Irq.c	  1539  #if((IRQ_ADC10_SR2_PRIO > 0) || (IRQ_ADC10_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Adc_Irq.c	  1540  #if((IRQ_ADC10_SR2_PRIO > 0) && (IRQ_ADC10_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Adc_Irq.c	  1541  IFX_INTERRUPT(ADC10SR2_ISR,0,IRQ_ADC10_SR2_PRIO)
; ..\mcal_src\Adc_Irq.c	  1542  #elif IRQ_ADC10_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Adc_Irq.c	  1543  ISR(ADC10SR2_ISR)
; ..\mcal_src\Adc_Irq.c	  1544  #endif
; ..\mcal_src\Adc_Irq.c	  1545  {
; ..\mcal_src\Adc_Irq.c	  1546       
; ..\mcal_src\Adc_Irq.c	  1547    /* Enable Global Interrupts */
; ..\mcal_src\Adc_Irq.c	  1548  #if (IRQ_ADC10_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Adc_Irq.c	  1549    Mcal_EnableAllInterrupts();
; ..\mcal_src\Adc_Irq.c	  1550  #endif
; ..\mcal_src\Adc_Irq.c	  1551  
; ..\mcal_src\Adc_Irq.c	  1552  #if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
; ..\mcal_src\Adc_Irq.c	  1553  /* ADC10 SRN2 is used by Channel Event for Limit check */
; ..\mcal_src\Adc_Irq.c	  1554  /* Call Adc Interrupt function*/
; ..\mcal_src\Adc_Irq.c	  1555    Adc_IsrSrn2AdcChEvnt(ADC_HWUNIT_ADC10);
; ..\mcal_src\Adc_Irq.c	  1556  #endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1557  }
; ..\mcal_src\Adc_Irq.c	  1558  #endif
; ..\mcal_src\Adc_Irq.c	  1559  
; ..\mcal_src\Adc_Irq.c	  1560  #endif /* (IRQ_ADC10_EXIST == STD_ON) */
; ..\mcal_src\Adc_Irq.c	  1561  
; ..\mcal_src\Adc_Irq.c	  1562  #define IRQ_STOP_SEC_CODE
; ..\mcal_src\Adc_Irq.c	  1563  #include "MemMap.h"
; ..\mcal_src\Adc_Irq.c	  1564  

	; Module end
