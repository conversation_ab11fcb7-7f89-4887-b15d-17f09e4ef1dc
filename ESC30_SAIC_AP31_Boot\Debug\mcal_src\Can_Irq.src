	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc29336a -c99 --dep-file=mcal_src\\.Can_Irq.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Can_Irq.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Can_Irq.src ..\\mcal_src\\Can_Irq.c"
	.compiler_name		"ctc"
	.name	"Can_Irq"

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1443
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_src\\Can_Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	176
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	182
	.byte	5,1,3
	.word	206
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	208
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	231
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	262
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	299
	.byte	4
	.byte	'PduIdType',0,3,72,22
	.word	231
	.byte	4
	.byte	'PduLengthType',0,3,76,22
	.word	262
	.byte	4
	.byte	'Can_IdType',0,4,46,16
	.word	299
	.byte	7
	.byte	'Can_TxHwObjectConfigType',0,5,218,3,16,2,8
	.byte	'MsgObjId',0,1
	.word	231
	.byte	2,35,0,8
	.byte	'HwControllerId',0,1
	.word	231
	.byte	2,35,1,0,4
	.byte	'Can_TxHwObjectConfigType',0,5,236,3,3
	.word	394
	.byte	7
	.byte	'Can_RxHwObjectConfigType',0,5,241,3,16,12,8
	.byte	'MaskRef',0,4
	.word	299
	.byte	2,35,0,8
	.byte	'MsgId',0,4
	.word	299
	.byte	2,35,4,8
	.byte	'MsgObjId',0,1
	.word	231
	.byte	2,35,8,8
	.byte	'HwControllerId',0,1
	.word	231
	.byte	2,35,9,0,4
	.byte	'Can_RxHwObjectConfigType',0,5,131,4,3
	.word	502
	.byte	7
	.byte	'Can_ControllerMOMapConfigType',0,5,165,4,16,4,9,4
	.word	231
	.byte	10,3,0,8
	.byte	'ControllerMOMap',0,4
	.word	678
	.byte	2,35,0,0,4
	.byte	'Can_ControllerMOMapConfigType',0,5,168,4,3
	.word	642
	.byte	7
	.byte	'Can_NPCRValueType',0,5,172,4,16,2,8
	.byte	'Can_NPCRValue',0,2
	.word	262
	.byte	2,35,0,0,4
	.byte	'Can_NPCRValueType',0,5,175,4,3
	.word	752
	.byte	7
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,5,178,4,16,6,8
	.byte	'CanControllerBaudrate',0,4
	.word	299
	.byte	2,35,0,8
	.byte	'CanControllerBaudrateCfg',0,2
	.word	262
	.byte	2,35,4,0,4
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,5,182,4,3
	.word	827
	.byte	7
	.byte	'Can_BaudrateConfigPtrType',0,5,185,4,16,4,11
	.word	827
	.byte	3
	.word	1024
	.byte	8
	.byte	'Can_kBaudrateConfigPtr',0,4
	.word	1029
	.byte	2,35,0,0,4
	.byte	'Can_BaudrateConfigPtrType',0,5,188,4,3
	.word	992
	.byte	7
	.byte	'Can_FDConfigParamType',0,5,193,4,16,6,8
	.byte	'CanControllerFDBaudrate',0,2
	.word	262
	.byte	2,35,0,8
	.byte	'CanControllerTxDelayComp',0,2
	.word	262
	.byte	2,35,2,8
	.byte	'CanControllerTxBRS',0,1
	.word	231
	.byte	2,35,4,0,4
	.byte	'Can_FDConfigParamType',0,5,198,4,3
	.word	1102
	.byte	7
	.byte	'Can_FDConfigParamPtrType',0,5,200,4,16,4,11
	.word	1102
	.byte	3
	.word	1288
	.byte	8
	.byte	'Can_kFDConfigParamPtr',0,4
	.word	1293
	.byte	2,35,0,0,4
	.byte	'Can_FDConfigParamPtrType',0,5,203,4,3
	.word	1257
	.byte	7
	.byte	'Can_EventHandlingType',0,5,210,4,16,4,8
	.byte	'CanEventType',0,4
	.word	678
	.byte	2,35,0,0,4
	.byte	'Can_EventHandlingType',0,5,213,4,3
	.word	1364
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,56,9,0,0,9,1,1,11,15,73,19,0,0,10,33,0,47,15,0,0,11,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Can_Irq.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\ComStack_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Can_GeneralTypes.h',0,0,0,0
	.byte	'..\\mcal_src\\Can_17_MCanP.h',0,0,0,0,0
.L9:
.L7:

; ..\mcal_src\Can_Irq.c	     1  /******************************************************************************
; ..\mcal_src\Can_Irq.c	     2  **                                                                           **
; ..\mcal_src\Can_Irq.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_src\Can_Irq.c	     4  **                                                                           **
; ..\mcal_src\Can_Irq.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Can_Irq.c	     6  **                                                                           **
; ..\mcal_src\Can_Irq.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Can_Irq.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Can_Irq.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Can_Irq.c	    10  **                                                                           **
; ..\mcal_src\Can_Irq.c	    11  *******************************************************************************
; ..\mcal_src\Can_Irq.c	    12  **                                                                           **
; ..\mcal_src\Can_Irq.c	    13  **  $FILENAME   : Can_Irq.c $                                                **
; ..\mcal_src\Can_Irq.c	    14  **                                                                           **
; ..\mcal_src\Can_Irq.c	    15  **  $CC VERSION : \main\dev_tc23x\5 $                                        **
; ..\mcal_src\Can_Irq.c	    16  **                                                                           **
; ..\mcal_src\Can_Irq.c	    17  **  $DATE       : 2016-06-27 $                                               **
; ..\mcal_src\Can_Irq.c	    18  **                                                                           **
; ..\mcal_src\Can_Irq.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Can_Irq.c	    20  **                                                                           **
; ..\mcal_src\Can_Irq.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Can_Irq.c	    22  **                                                                           **
; ..\mcal_src\Can_Irq.c	    23  **  DESCRIPTION : This file contains CAN Module interrupt frames             **
; ..\mcal_src\Can_Irq.c	    24  **                                                                           **
; ..\mcal_src\Can_Irq.c	    25  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\Can_Irq.c	    26  **                                                                           **
; ..\mcal_src\Can_Irq.c	    27  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	    28  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    29  **                      Includes                                              **
; ..\mcal_src\Can_Irq.c	    30  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    31  #include "Std_Types.h"
; ..\mcal_src\Can_Irq.c	    32  #include "Mcal.h"
; ..\mcal_src\Can_Irq.c	    33  #include "Can_17_MCanP.h"
; ..\mcal_src\Can_Irq.c	    34  #include "Irq.h"
; ..\mcal_src\Can_Irq.c	    35  
; ..\mcal_src\Can_Irq.c	    36  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    37  **                      Imported Compiler Switch Checks                       **
; ..\mcal_src\Can_Irq.c	    38  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    39  
; ..\mcal_src\Can_Irq.c	    40  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    41  **                      Private Macro Definitions                             **
; ..\mcal_src\Can_Irq.c	    42  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    43  
; ..\mcal_src\Can_Irq.c	    44  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    45  **                      Private Type Definitions                              **
; ..\mcal_src\Can_Irq.c	    46  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    47  
; ..\mcal_src\Can_Irq.c	    48  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    49  **                      Private Function Declarations                         **
; ..\mcal_src\Can_Irq.c	    50  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    51  
; ..\mcal_src\Can_Irq.c	    52  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    53  **                      Global Constant Definitions                           **
; ..\mcal_src\Can_Irq.c	    54  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    55  
; ..\mcal_src\Can_Irq.c	    56  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    57  **                      Global Variable Definitions                           **
; ..\mcal_src\Can_Irq.c	    58  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    59  
; ..\mcal_src\Can_Irq.c	    60  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    61  **                      Private Constant Definitions                          **
; ..\mcal_src\Can_Irq.c	    62  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    63  
; ..\mcal_src\Can_Irq.c	    64  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    65  **                      Private Variable Definitions                          **
; ..\mcal_src\Can_Irq.c	    66  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    67  
; ..\mcal_src\Can_Irq.c	    68  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    69  **                      Private Function Definitions                          **
; ..\mcal_src\Can_Irq.c	    70  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    71  
; ..\mcal_src\Can_Irq.c	    72  /*******************************************************************************
; ..\mcal_src\Can_Irq.c	    73  **                      Global Function Definitions                           **
; ..\mcal_src\Can_Irq.c	    74  *******************************************************************************/
; ..\mcal_src\Can_Irq.c	    75  #define IRQ_START_SEC_CODE
; ..\mcal_src\Can_Irq.c	    76  #include "MemMap.h"
; ..\mcal_src\Can_Irq.c	    77  /******************************************************************************
; ..\mcal_src\Can_Irq.c	    78  ** Syntax : void CANSR0_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	    79  **                                                                           **
; ..\mcal_src\Can_Irq.c	    80  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	    81  **                                                                           **
; ..\mcal_src\Can_Irq.c	    82  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	    83  **                                                                           **
; ..\mcal_src\Can_Irq.c	    84  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	    85  **                                                                           **
; ..\mcal_src\Can_Irq.c	    86  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	    87  **                                                                           **
; ..\mcal_src\Can_Irq.c	    88  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	    89  **                                                                           **
; ..\mcal_src\Can_Irq.c	    90  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	    91  **                                                                           **
; ..\mcal_src\Can_Irq.c	    92  ** Description : Service for CAN Controller 0 Transmission event                   **
; ..\mcal_src\Can_Irq.c	    93  **                                                                           **
; ..\mcal_src\Can_Irq.c	    94  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	    95  #if (IRQ_CAN0_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	    96  #if((IRQ_CAN_SR0_PRIO > 0) || (IRQ_CAN_SR0_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	    97  #if((IRQ_CAN_SR0_PRIO > 0) && (IRQ_CAN_SR0_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	    98  IFX_INTERRUPT(CANSR0_ISR, 0, IRQ_CAN_SR0_PRIO)
; ..\mcal_src\Can_Irq.c	    99  #elif IRQ_CAN_SR0_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   100  ISR(CANSR0_ISR)
; ..\mcal_src\Can_Irq.c	   101  #endif
; ..\mcal_src\Can_Irq.c	   102  {
; ..\mcal_src\Can_Irq.c	   103  #if (IRQ_CAN_SR0_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   104    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   105  #endif
; ..\mcal_src\Can_Irq.c	   106    #ifdef CAN_TX_PROCESSING_HWCONTROLLER0
; ..\mcal_src\Can_Irq.c	   107    #if (CAN_TX_PROCESSING_HWCONTROLLER0 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   108    Can_17_MCanP_IsrTransmitHandler(CAN_HWCONTROLLER0);
; ..\mcal_src\Can_Irq.c	   109    #endif
; ..\mcal_src\Can_Irq.c	   110    #endif
; ..\mcal_src\Can_Irq.c	   111  }
; ..\mcal_src\Can_Irq.c	   112  #endif
; ..\mcal_src\Can_Irq.c	   113  #endif
; ..\mcal_src\Can_Irq.c	   114  
; ..\mcal_src\Can_Irq.c	   115  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   116  ** Syntax : void CANSR1_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   117  **                                                                           **
; ..\mcal_src\Can_Irq.c	   118  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   119  **                                                                           **
; ..\mcal_src\Can_Irq.c	   120  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   121  **                                                                           **
; ..\mcal_src\Can_Irq.c	   122  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   123  **                                                                           **
; ..\mcal_src\Can_Irq.c	   124  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   125  **                                                                           **
; ..\mcal_src\Can_Irq.c	   126  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   127  **                                                                           **
; ..\mcal_src\Can_Irq.c	   128  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   129  **                                                                           **
; ..\mcal_src\Can_Irq.c	   130  ** Description : Service for CAN Controller 1 Transmission event                   **
; ..\mcal_src\Can_Irq.c	   131  **                                                                           **
; ..\mcal_src\Can_Irq.c	   132  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   133  #if (IRQ_CAN1_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   134  #if((IRQ_CAN_SR1_PRIO > 0) || (IRQ_CAN_SR1_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   135  #if((IRQ_CAN_SR1_PRIO > 0) && (IRQ_CAN_SR1_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   136  IFX_INTERRUPT(CANSR1_ISR, 0, IRQ_CAN_SR1_PRIO)
; ..\mcal_src\Can_Irq.c	   137  #elif IRQ_CAN_SR1_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   138  ISR(CANSR1_ISR)
; ..\mcal_src\Can_Irq.c	   139  #endif
; ..\mcal_src\Can_Irq.c	   140  {
; ..\mcal_src\Can_Irq.c	   141  #if (IRQ_CAN_SR1_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   142    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   143  #endif
; ..\mcal_src\Can_Irq.c	   144    #ifdef CAN_TX_PROCESSING_HWCONTROLLER1
; ..\mcal_src\Can_Irq.c	   145   #if (CAN_TX_PROCESSING_HWCONTROLLER1 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   146    Can_17_MCanP_IsrTransmitHandler(CAN_HWCONTROLLER1);
; ..\mcal_src\Can_Irq.c	   147    #endif
; ..\mcal_src\Can_Irq.c	   148    #endif
; ..\mcal_src\Can_Irq.c	   149  }
; ..\mcal_src\Can_Irq.c	   150  #endif
; ..\mcal_src\Can_Irq.c	   151  #endif
; ..\mcal_src\Can_Irq.c	   152  
; ..\mcal_src\Can_Irq.c	   153  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   154  ** Syntax : void CANSR2_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   155  **                                                                           **
; ..\mcal_src\Can_Irq.c	   156  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   157  **                                                                           **
; ..\mcal_src\Can_Irq.c	   158  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   159  **                                                                           **
; ..\mcal_src\Can_Irq.c	   160  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   161  **                                                                           **
; ..\mcal_src\Can_Irq.c	   162  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   163  **                                                                           **
; ..\mcal_src\Can_Irq.c	   164  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   165  **                                                                           **
; ..\mcal_src\Can_Irq.c	   166  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   167  **                                                                           **
; ..\mcal_src\Can_Irq.c	   168  ** Description : Service for CAN Controller 2 Transmission event                   **
; ..\mcal_src\Can_Irq.c	   169  **                                                                           **
; ..\mcal_src\Can_Irq.c	   170  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   171  #if (IRQ_CAN2_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   172  #if((IRQ_CAN_SR2_PRIO > 0) || (IRQ_CAN_SR2_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   173  #if((IRQ_CAN_SR2_PRIO > 0) && (IRQ_CAN_SR2_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   174  IFX_INTERRUPT(CANSR2_ISR, 0, IRQ_CAN_SR2_PRIO)
; ..\mcal_src\Can_Irq.c	   175  #elif IRQ_CAN_SR2_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   176  ISR(CANSR2_ISR)
; ..\mcal_src\Can_Irq.c	   177  #endif
; ..\mcal_src\Can_Irq.c	   178  {
; ..\mcal_src\Can_Irq.c	   179  #if (IRQ_CAN_SR2_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   180    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   181  #endif
; ..\mcal_src\Can_Irq.c	   182    #ifdef CAN_TX_PROCESSING_HWCONTROLLER2
; ..\mcal_src\Can_Irq.c	   183    #if (CAN_TX_PROCESSING_HWCONTROLLER2 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   184    Can_17_MCanP_IsrTransmitHandler(CAN_HWCONTROLLER2);
; ..\mcal_src\Can_Irq.c	   185    #endif
; ..\mcal_src\Can_Irq.c	   186    #endif
; ..\mcal_src\Can_Irq.c	   187  }
; ..\mcal_src\Can_Irq.c	   188  #endif
; ..\mcal_src\Can_Irq.c	   189  #endif
; ..\mcal_src\Can_Irq.c	   190  
; ..\mcal_src\Can_Irq.c	   191  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   192  ** Syntax : void CANSR3_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   193  **                                                                           **
; ..\mcal_src\Can_Irq.c	   194  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   195  **                                                                           **
; ..\mcal_src\Can_Irq.c	   196  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   197  **                                                                           **
; ..\mcal_src\Can_Irq.c	   198  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   199  **                                                                           **
; ..\mcal_src\Can_Irq.c	   200  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   201  **                                                                           **
; ..\mcal_src\Can_Irq.c	   202  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   203  **                                                                           **
; ..\mcal_src\Can_Irq.c	   204  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   205  **                                                                           **
; ..\mcal_src\Can_Irq.c	   206  ** Description : Service for CAN Controller 0 Reception/Wakeup event                   **
; ..\mcal_src\Can_Irq.c	   207  **                                                                           **
; ..\mcal_src\Can_Irq.c	   208  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   209  #if (IRQ_CAN3_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   210  #if((IRQ_CAN_SR3_PRIO > 0) || (IRQ_CAN_SR3_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   211  #if((IRQ_CAN_SR3_PRIO > 0) && (IRQ_CAN_SR3_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   212  IFX_INTERRUPT(CANSR3_ISR, 0, IRQ_CAN_SR3_PRIO)
; ..\mcal_src\Can_Irq.c	   213  #elif IRQ_CAN_SR3_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   214  ISR(CANSR3_ISR)
; ..\mcal_src\Can_Irq.c	   215  #endif
; ..\mcal_src\Can_Irq.c	   216  {
; ..\mcal_src\Can_Irq.c	   217  #if (IRQ_CAN_SR3_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   218    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   219  #endif
; ..\mcal_src\Can_Irq.c	   220    #ifdef CAN_RX_PROCESSING_HWCONTROLLER0
; ..\mcal_src\Can_Irq.c	   221    #if (CAN_RX_PROCESSING_HWCONTROLLER0 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   222    Can_17_MCanP_IsrReceiveHandler(CAN_HWCONTROLLER0);
; ..\mcal_src\Can_Irq.c	   223    #endif
; ..\mcal_src\Can_Irq.c	   224    #endif
; ..\mcal_src\Can_Irq.c	   225  }
; ..\mcal_src\Can_Irq.c	   226  #endif
; ..\mcal_src\Can_Irq.c	   227  #endif
; ..\mcal_src\Can_Irq.c	   228  
; ..\mcal_src\Can_Irq.c	   229  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   230  ** Syntax : void CANSR4_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   231  **                                                                           **
; ..\mcal_src\Can_Irq.c	   232  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   233  **                                                                           **
; ..\mcal_src\Can_Irq.c	   234  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   235  **                                                                           **
; ..\mcal_src\Can_Irq.c	   236  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   237  **                                                                           **
; ..\mcal_src\Can_Irq.c	   238  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   239  **                                                                           **
; ..\mcal_src\Can_Irq.c	   240  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   241  **                                                                           **
; ..\mcal_src\Can_Irq.c	   242  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   243  **                                                                           **
; ..\mcal_src\Can_Irq.c	   244  ** Description : Service for CAN Controller 1 Reception/Wakeup event         **
; ..\mcal_src\Can_Irq.c	   245  **                                                                           **
; ..\mcal_src\Can_Irq.c	   246  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   247  #if (IRQ_CAN4_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   248  #if((IRQ_CAN_SR4_PRIO > 0) || (IRQ_CAN_SR4_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   249  #if((IRQ_CAN_SR4_PRIO > 0) && (IRQ_CAN_SR4_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   250  IFX_INTERRUPT(CANSR4_ISR, 0, IRQ_CAN_SR4_PRIO)
; ..\mcal_src\Can_Irq.c	   251  #elif IRQ_CAN_SR4_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   252  ISR(CANSR4_ISR)
; ..\mcal_src\Can_Irq.c	   253  #endif
; ..\mcal_src\Can_Irq.c	   254  {
; ..\mcal_src\Can_Irq.c	   255  #if (IRQ_CAN_SR4_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   256    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   257  #endif
; ..\mcal_src\Can_Irq.c	   258    #ifdef CAN_RX_PROCESSING_HWCONTROLLER1
; ..\mcal_src\Can_Irq.c	   259    #if (CAN_RX_PROCESSING_HWCONTROLLER1 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   260    Can_17_MCanP_IsrReceiveHandler(CAN_HWCONTROLLER1);
; ..\mcal_src\Can_Irq.c	   261    #endif
; ..\mcal_src\Can_Irq.c	   262    #endif
; ..\mcal_src\Can_Irq.c	   263  }
; ..\mcal_src\Can_Irq.c	   264  #endif
; ..\mcal_src\Can_Irq.c	   265  #endif
; ..\mcal_src\Can_Irq.c	   266  
; ..\mcal_src\Can_Irq.c	   267  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   268  ** Syntax : void CANSR5_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   269  **                                                                           **
; ..\mcal_src\Can_Irq.c	   270  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   271  **                                                                           **
; ..\mcal_src\Can_Irq.c	   272  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   273  **                                                                           **
; ..\mcal_src\Can_Irq.c	   274  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   275  **                                                                           **
; ..\mcal_src\Can_Irq.c	   276  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   277  **                                                                           **
; ..\mcal_src\Can_Irq.c	   278  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   279  **                                                                           **
; ..\mcal_src\Can_Irq.c	   280  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   281  **                                                                           **
; ..\mcal_src\Can_Irq.c	   282  ** Description : Service for CAN Controller 2 Reception/Wakeup event         **
; ..\mcal_src\Can_Irq.c	   283  **                                                                           **
; ..\mcal_src\Can_Irq.c	   284  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   285  #if (IRQ_CAN5_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   286  #if((IRQ_CAN_SR5_PRIO > 0) || (IRQ_CAN_SR5_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   287  #if((IRQ_CAN_SR5_PRIO > 0) && (IRQ_CAN_SR5_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   288  IFX_INTERRUPT(CANSR5_ISR, 0, IRQ_CAN_SR5_PRIO)
; ..\mcal_src\Can_Irq.c	   289  #elif IRQ_CAN_SR5_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   290  ISR(CANSR5_ISR)
; ..\mcal_src\Can_Irq.c	   291  #endif
; ..\mcal_src\Can_Irq.c	   292  {
; ..\mcal_src\Can_Irq.c	   293  #if (IRQ_CAN_SR5_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   294    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   295  #endif
; ..\mcal_src\Can_Irq.c	   296    #ifdef CAN_RX_PROCESSING_HWCONTROLLER2
; ..\mcal_src\Can_Irq.c	   297    #if (CAN_RX_PROCESSING_HWCONTROLLER2 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   298    Can_17_MCanP_IsrReceiveHandler(CAN_HWCONTROLLER2);
; ..\mcal_src\Can_Irq.c	   299    #endif
; ..\mcal_src\Can_Irq.c	   300    #endif
; ..\mcal_src\Can_Irq.c	   301  }
; ..\mcal_src\Can_Irq.c	   302  #endif
; ..\mcal_src\Can_Irq.c	   303  #endif
; ..\mcal_src\Can_Irq.c	   304  
; ..\mcal_src\Can_Irq.c	   305  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   306  ** Syntax : void CANSR6_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   307  **                                                                           **
; ..\mcal_src\Can_Irq.c	   308  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   309  **                                                                           **
; ..\mcal_src\Can_Irq.c	   310  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   311  **                                                                           **
; ..\mcal_src\Can_Irq.c	   312  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   313  **                                                                           **
; ..\mcal_src\Can_Irq.c	   314  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   315  **                                                                           **
; ..\mcal_src\Can_Irq.c	   316  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   317  **                                                                           **
; ..\mcal_src\Can_Irq.c	   318  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   319  **                                                                           **
; ..\mcal_src\Can_Irq.c	   320  ** Description : Service for CAN Controller 0,1,2 BusOff event         **
; ..\mcal_src\Can_Irq.c	   321  **                                                                           **
; ..\mcal_src\Can_Irq.c	   322  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   323  #if (IRQ_CAN6_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   324  #if((IRQ_CAN_SR6_PRIO > 0) || (IRQ_CAN_SR6_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   325  #if((IRQ_CAN_SR6_PRIO > 0) && (IRQ_CAN_SR6_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   326  IFX_INTERRUPT(CANSR6_ISR, 0, IRQ_CAN_SR6_PRIO)
; ..\mcal_src\Can_Irq.c	   327  #elif IRQ_CAN_SR6_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   328  ISR(CANSR6_ISR)
; ..\mcal_src\Can_Irq.c	   329  #endif
; ..\mcal_src\Can_Irq.c	   330  {
; ..\mcal_src\Can_Irq.c	   331  #if (IRQ_CAN_SR6_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   332    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   333  #endif
; ..\mcal_src\Can_Irq.c	   334    #ifdef CAN_BO_PROCESSING_HWCONTROLLER0
; ..\mcal_src\Can_Irq.c	   335    #if (CAN_BO_PROCESSING_HWCONTROLLER0 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   336    Can_17_MCanP_IsrBusOffHandler(CAN_HWCONTROLLER0);
; ..\mcal_src\Can_Irq.c	   337    #endif
; ..\mcal_src\Can_Irq.c	   338    #endif
; ..\mcal_src\Can_Irq.c	   339    #ifdef CAN_BO_PROCESSING_HWCONTROLLER1
; ..\mcal_src\Can_Irq.c	   340    #if (CAN_BO_PROCESSING_HWCONTROLLER1 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   341    Can_17_MCanP_IsrBusOffHandler(CAN_HWCONTROLLER1);
; ..\mcal_src\Can_Irq.c	   342    #endif
; ..\mcal_src\Can_Irq.c	   343    #endif
; ..\mcal_src\Can_Irq.c	   344    #ifdef CAN_BO_PROCESSING_HWCONTROLLER2
; ..\mcal_src\Can_Irq.c	   345    #if (CAN_BO_PROCESSING_HWCONTROLLER2 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   346    Can_17_MCanP_IsrBusOffHandler(CAN_HWCONTROLLER2);
; ..\mcal_src\Can_Irq.c	   347    #endif
; ..\mcal_src\Can_Irq.c	   348    #endif
; ..\mcal_src\Can_Irq.c	   349  }
; ..\mcal_src\Can_Irq.c	   350  #endif
; ..\mcal_src\Can_Irq.c	   351  #endif
; ..\mcal_src\Can_Irq.c	   352  
; ..\mcal_src\Can_Irq.c	   353  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   354  ** Syntax : void CANSR16_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   355  **                                                                           **
; ..\mcal_src\Can_Irq.c	   356  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   357  **                                                                           **
; ..\mcal_src\Can_Irq.c	   358  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   359  **                                                                           **
; ..\mcal_src\Can_Irq.c	   360  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   361  **                                                                           **
; ..\mcal_src\Can_Irq.c	   362  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   363  **                                                                           **
; ..\mcal_src\Can_Irq.c	   364  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   365  **                                                                           **
; ..\mcal_src\Can_Irq.c	   366  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   367  **                                                                           **
; ..\mcal_src\Can_Irq.c	   368  ** Description : Service for CAN Controller 3 Transmission event                   **
; ..\mcal_src\Can_Irq.c	   369  **                                                                           **
; ..\mcal_src\Can_Irq.c	   370  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   371  #if (IRQ_CAN16_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   372  #if((IRQ_CAN_SR16_PRIO > 0) || (IRQ_CAN_SR16_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   373  #if((IRQ_CAN_SR16_PRIO > 0) && (IRQ_CAN_SR16_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   374  IFX_INTERRUPT(CANSR16_ISR, 0, IRQ_CAN_SR16_PRIO)
; ..\mcal_src\Can_Irq.c	   375  #elif IRQ_CAN_SR16_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   376  ISR(CANSR16_ISR)
; ..\mcal_src\Can_Irq.c	   377  #endif
; ..\mcal_src\Can_Irq.c	   378  {
; ..\mcal_src\Can_Irq.c	   379  #if (IRQ_CAN_SR16_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   380    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   381  #endif
; ..\mcal_src\Can_Irq.c	   382    #ifdef CAN_TX_PROCESSING_HWCONTROLLER3
; ..\mcal_src\Can_Irq.c	   383    #if (CAN_TX_PROCESSING_HWCONTROLLER3 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   384    Can_17_MCanP_IsrTransmitHandler(CAN_HWCONTROLLER3);
; ..\mcal_src\Can_Irq.c	   385    #endif
; ..\mcal_src\Can_Irq.c	   386    #endif
; ..\mcal_src\Can_Irq.c	   387  }
; ..\mcal_src\Can_Irq.c	   388  #endif
; ..\mcal_src\Can_Irq.c	   389  #endif
; ..\mcal_src\Can_Irq.c	   390  
; ..\mcal_src\Can_Irq.c	   391  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   392  ** Syntax : void CANSR17_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   393  **                                                                           **
; ..\mcal_src\Can_Irq.c	   394  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   395  **                                                                           **
; ..\mcal_src\Can_Irq.c	   396  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   397  **                                                                           **
; ..\mcal_src\Can_Irq.c	   398  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   399  **                                                                           **
; ..\mcal_src\Can_Irq.c	   400  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   401  **                                                                           **
; ..\mcal_src\Can_Irq.c	   402  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   403  **                                                                           **
; ..\mcal_src\Can_Irq.c	   404  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   405  **                                                                           **
; ..\mcal_src\Can_Irq.c	   406  ** Description : Service for CAN Controller 4 Transmission event                   **
; ..\mcal_src\Can_Irq.c	   407  **                                                                           **
; ..\mcal_src\Can_Irq.c	   408  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   409  #if (IRQ_CAN17_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   410  #if((IRQ_CAN_SR17_PRIO > 0) || (IRQ_CAN_SR17_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   411  #if((IRQ_CAN_SR17_PRIO > 0) && (IRQ_CAN_SR17_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   412  IFX_INTERRUPT(CANSR17_ISR, 0, IRQ_CAN_SR17_PRIO)
; ..\mcal_src\Can_Irq.c	   413  #elif IRQ_CAN_SR17_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   414  ISR(CANSR17_ISR)
; ..\mcal_src\Can_Irq.c	   415  #endif
; ..\mcal_src\Can_Irq.c	   416  {
; ..\mcal_src\Can_Irq.c	   417  #if (IRQ_CAN_SR17_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   418    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   419  #endif
; ..\mcal_src\Can_Irq.c	   420    #ifdef CAN_TX_PROCESSING_HWCONTROLLER4
; ..\mcal_src\Can_Irq.c	   421   #if (CAN_TX_PROCESSING_HWCONTROLLER4 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   422    Can_17_MCanP_IsrTransmitHandler(CAN_HWCONTROLLER4);
; ..\mcal_src\Can_Irq.c	   423    #endif
; ..\mcal_src\Can_Irq.c	   424    #endif
; ..\mcal_src\Can_Irq.c	   425  }
; ..\mcal_src\Can_Irq.c	   426  #endif
; ..\mcal_src\Can_Irq.c	   427  #endif
; ..\mcal_src\Can_Irq.c	   428  
; ..\mcal_src\Can_Irq.c	   429  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   430  ** Syntax : void CANSR18_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   431  **                                                                           **
; ..\mcal_src\Can_Irq.c	   432  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   433  **                                                                           **
; ..\mcal_src\Can_Irq.c	   434  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   435  **                                                                           **
; ..\mcal_src\Can_Irq.c	   436  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   437  **                                                                           **
; ..\mcal_src\Can_Irq.c	   438  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   439  **                                                                           **
; ..\mcal_src\Can_Irq.c	   440  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   441  **                                                                           **
; ..\mcal_src\Can_Irq.c	   442  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   443  **                                                                           **
; ..\mcal_src\Can_Irq.c	   444  ** Description : Service for CAN Controller 5 Transmission event         **
; ..\mcal_src\Can_Irq.c	   445  **                                                                           **
; ..\mcal_src\Can_Irq.c	   446  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   447  #if (IRQ_CAN18_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   448  #if((IRQ_CAN_SR18_PRIO > 0) || (IRQ_CAN_SR18_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   449  #if((IRQ_CAN_SR18_PRIO > 0) && (IRQ_CAN_SR18_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   450  IFX_INTERRUPT(CANSR18_ISR, 0, IRQ_CAN_SR18_PRIO)
; ..\mcal_src\Can_Irq.c	   451  #elif IRQ_CAN_SR18_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   452  ISR(CANSR18_ISR)
; ..\mcal_src\Can_Irq.c	   453  #endif
; ..\mcal_src\Can_Irq.c	   454  {
; ..\mcal_src\Can_Irq.c	   455  #if (IRQ_CAN_SR18_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   456    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   457  #endif
; ..\mcal_src\Can_Irq.c	   458    #ifdef CAN_TX_PROCESSING_HWCONTROLLER5
; ..\mcal_src\Can_Irq.c	   459    #if (CAN_TX_PROCESSING_HWCONTROLLER5 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   460    Can_17_MCanP_IsrTransmitHandler(CAN_HWCONTROLLER5);
; ..\mcal_src\Can_Irq.c	   461    #endif
; ..\mcal_src\Can_Irq.c	   462    #endif
; ..\mcal_src\Can_Irq.c	   463  }
; ..\mcal_src\Can_Irq.c	   464  #endif
; ..\mcal_src\Can_Irq.c	   465  #endif
; ..\mcal_src\Can_Irq.c	   466  
; ..\mcal_src\Can_Irq.c	   467  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   468  ** Syntax : void CANSR19_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   469  **                                                                           **
; ..\mcal_src\Can_Irq.c	   470  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   471  **                                                                           **
; ..\mcal_src\Can_Irq.c	   472  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   473  **                                                                           **
; ..\mcal_src\Can_Irq.c	   474  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   475  **                                                                           **
; ..\mcal_src\Can_Irq.c	   476  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   477  **                                                                           **
; ..\mcal_src\Can_Irq.c	   478  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   479  **                                                                           **
; ..\mcal_src\Can_Irq.c	   480  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   481  **                                                                           **
; ..\mcal_src\Can_Irq.c	   482  ** Description : Service for CAN Controller 3 Reception/Wakeup event         **
; ..\mcal_src\Can_Irq.c	   483  **                                                                           **
; ..\mcal_src\Can_Irq.c	   484  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   485  #if (IRQ_CAN19_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   486  #if((IRQ_CAN_SR19_PRIO > 0) || (IRQ_CAN_SR19_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   487  #if((IRQ_CAN_SR19_PRIO > 0) && (IRQ_CAN_SR19_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   488  IFX_INTERRUPT(CANSR19_ISR, 0, IRQ_CAN_SR19_PRIO)
; ..\mcal_src\Can_Irq.c	   489  #elif IRQ_CAN_SR19_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   490  ISR(CANSR19_ISR)
; ..\mcal_src\Can_Irq.c	   491  #endif
; ..\mcal_src\Can_Irq.c	   492  {
; ..\mcal_src\Can_Irq.c	   493  #if (IRQ_CAN_SR19_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   494    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   495  #endif
; ..\mcal_src\Can_Irq.c	   496    #ifdef CAN_RX_PROCESSING_HWCONTROLLER3
; ..\mcal_src\Can_Irq.c	   497    #if (CAN_RX_PROCESSING_HWCONTROLLER3 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   498    Can_17_MCanP_IsrReceiveHandler(CAN_HWCONTROLLER3);
; ..\mcal_src\Can_Irq.c	   499    #endif
; ..\mcal_src\Can_Irq.c	   500    #endif
; ..\mcal_src\Can_Irq.c	   501  }
; ..\mcal_src\Can_Irq.c	   502  #endif
; ..\mcal_src\Can_Irq.c	   503  #endif
; ..\mcal_src\Can_Irq.c	   504  
; ..\mcal_src\Can_Irq.c	   505  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   506  ** Syntax : void CANSR20_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   507  **                                                                           **
; ..\mcal_src\Can_Irq.c	   508  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   509  **                                                                           **
; ..\mcal_src\Can_Irq.c	   510  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   511  **                                                                           **
; ..\mcal_src\Can_Irq.c	   512  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   513  **                                                                           **
; ..\mcal_src\Can_Irq.c	   514  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   515  **                                                                           **
; ..\mcal_src\Can_Irq.c	   516  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   517  **                                                                           **
; ..\mcal_src\Can_Irq.c	   518  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   519  **                                                                           **
; ..\mcal_src\Can_Irq.c	   520  ** Description : Service for CAN Controller 4 Reception/Wakeup event             **
; ..\mcal_src\Can_Irq.c	   521  **                                                                           **
; ..\mcal_src\Can_Irq.c	   522  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   523  #if (IRQ_CAN20_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   524  #if((IRQ_CAN_SR20_PRIO > 0) || (IRQ_CAN_SR20_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   525  #if((IRQ_CAN_SR20_PRIO > 0) && (IRQ_CAN_SR20_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   526  IFX_INTERRUPT(CANSR20_ISR, 0, IRQ_CAN_SR20_PRIO)
; ..\mcal_src\Can_Irq.c	   527  #elif IRQ_CAN_SR20_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   528  ISR(CANSR20_ISR)
; ..\mcal_src\Can_Irq.c	   529  #endif
; ..\mcal_src\Can_Irq.c	   530  {
; ..\mcal_src\Can_Irq.c	   531  #if (IRQ_CAN_SR20_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   532    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   533  #endif
; ..\mcal_src\Can_Irq.c	   534    #ifdef CAN_RX_PROCESSING_HWCONTROLLER4
; ..\mcal_src\Can_Irq.c	   535    #if (CAN_RX_PROCESSING_HWCONTROLLER4 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   536    Can_17_MCanP_IsrReceiveHandler(CAN_HWCONTROLLER4);
; ..\mcal_src\Can_Irq.c	   537    #endif
; ..\mcal_src\Can_Irq.c	   538    #endif
; ..\mcal_src\Can_Irq.c	   539  }
; ..\mcal_src\Can_Irq.c	   540  #endif
; ..\mcal_src\Can_Irq.c	   541  #endif
; ..\mcal_src\Can_Irq.c	   542  
; ..\mcal_src\Can_Irq.c	   543  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   544  ** Syntax : void CANSR21_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   545  **                                                                           **
; ..\mcal_src\Can_Irq.c	   546  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   547  **                                                                           **
; ..\mcal_src\Can_Irq.c	   548  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   549  **                                                                           **
; ..\mcal_src\Can_Irq.c	   550  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   551  **                                                                           **
; ..\mcal_src\Can_Irq.c	   552  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   553  **                                                                           **
; ..\mcal_src\Can_Irq.c	   554  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   555  **                                                                           **
; ..\mcal_src\Can_Irq.c	   556  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   557  **                                                                           **
; ..\mcal_src\Can_Irq.c	   558  ** Description : Service for CAN Controller 5 Reception event             **
; ..\mcal_src\Can_Irq.c	   559  **                                                                           **
; ..\mcal_src\Can_Irq.c	   560  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   561  #if (IRQ_CAN21_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   562  #if((IRQ_CAN_SR21_PRIO > 0) || (IRQ_CAN_SR21_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   563  #if((IRQ_CAN_SR21_PRIO > 0) && (IRQ_CAN_SR21_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   564  IFX_INTERRUPT(CANSR21_ISR, 0, IRQ_CAN_SR21_PRIO)
; ..\mcal_src\Can_Irq.c	   565  #elif IRQ_CAN_SR21_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   566  ISR(CANSR21_ISR)
; ..\mcal_src\Can_Irq.c	   567  #endif
; ..\mcal_src\Can_Irq.c	   568  {
; ..\mcal_src\Can_Irq.c	   569  #if (IRQ_CAN_SR21_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   570    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   571  #endif
; ..\mcal_src\Can_Irq.c	   572    #ifdef CAN_RX_PROCESSING_HWCONTROLLER5  
; ..\mcal_src\Can_Irq.c	   573    #if (CAN_RX_PROCESSING_HWCONTROLLER5 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   574    Can_17_MCanP_IsrReceiveHandler(CAN_HWCONTROLLER5);
; ..\mcal_src\Can_Irq.c	   575    #endif
; ..\mcal_src\Can_Irq.c	   576    #endif
; ..\mcal_src\Can_Irq.c	   577  }
; ..\mcal_src\Can_Irq.c	   578  #endif
; ..\mcal_src\Can_Irq.c	   579  #endif
; ..\mcal_src\Can_Irq.c	   580  
; ..\mcal_src\Can_Irq.c	   581  /******************************************************************************
; ..\mcal_src\Can_Irq.c	   582  ** Syntax : void CANSR22_ISR(void)                                          **
; ..\mcal_src\Can_Irq.c	   583  **                                                                           **
; ..\mcal_src\Can_Irq.c	   584  ** Service ID: NA                                                            **
; ..\mcal_src\Can_Irq.c	   585  **                                                                           **
; ..\mcal_src\Can_Irq.c	   586  ** Sync/Async: Synchronous                                                   **
; ..\mcal_src\Can_Irq.c	   587  **                                                                           **
; ..\mcal_src\Can_Irq.c	   588  ** Reentrancy: Reentrant                                                     **
; ..\mcal_src\Can_Irq.c	   589  **                                                                           **
; ..\mcal_src\Can_Irq.c	   590  ** Parameters (in): None                                                     **
; ..\mcal_src\Can_Irq.c	   591  **                                                                           **
; ..\mcal_src\Can_Irq.c	   592  ** Parameters (out): None                                                    **
; ..\mcal_src\Can_Irq.c	   593  **                                                                           **
; ..\mcal_src\Can_Irq.c	   594  ** Return value: None                                                        **
; ..\mcal_src\Can_Irq.c	   595  **                                                                           **
; ..\mcal_src\Can_Irq.c	   596  ** Description : Service for CAN Controller 3,4,5 BusOff event             **
; ..\mcal_src\Can_Irq.c	   597  **                                                                           **
; ..\mcal_src\Can_Irq.c	   598  ******************************************************************************/
; ..\mcal_src\Can_Irq.c	   599  #if (IRQ_CAN22_EXIST == STD_ON)
; ..\mcal_src\Can_Irq.c	   600  #if((IRQ_CAN_SR22_PRIO > 0) || (IRQ_CAN_SR22_CAT == IRQ_CAT23))
; ..\mcal_src\Can_Irq.c	   601  #if((IRQ_CAN_SR22_PRIO > 0) && (IRQ_CAN_SR22_CAT == IRQ_CAT1))
; ..\mcal_src\Can_Irq.c	   602  IFX_INTERRUPT(CANSR22_ISR, 0, IRQ_CAN_SR22_PRIO)
; ..\mcal_src\Can_Irq.c	   603  #elif IRQ_CAN_SR22_CAT == IRQ_CAT23
; ..\mcal_src\Can_Irq.c	   604  ISR(CANSR22_ISR)
; ..\mcal_src\Can_Irq.c	   605  #endif
; ..\mcal_src\Can_Irq.c	   606  {
; ..\mcal_src\Can_Irq.c	   607  #if (IRQ_CAN_SR22_CAT == IRQ_CAT1)
; ..\mcal_src\Can_Irq.c	   608    Mcal_EnableAllInterrupts();
; ..\mcal_src\Can_Irq.c	   609  #endif
; ..\mcal_src\Can_Irq.c	   610    #ifdef CAN_BO_PROCESSING_HWCONTROLLER3
; ..\mcal_src\Can_Irq.c	   611    #if (CAN_BO_PROCESSING_HWCONTROLLER3 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   612    Can_17_MCanP_IsrBusOffHandler(CAN_HWCONTROLLER3);
; ..\mcal_src\Can_Irq.c	   613    #endif
; ..\mcal_src\Can_Irq.c	   614    #endif
; ..\mcal_src\Can_Irq.c	   615    #ifdef CAN_BO_PROCESSING_HWCONTROLLER4
; ..\mcal_src\Can_Irq.c	   616    #if (CAN_BO_PROCESSING_HWCONTROLLER4 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   617    Can_17_MCanP_IsrBusOffHandler(CAN_HWCONTROLLER4);
; ..\mcal_src\Can_Irq.c	   618    #endif
; ..\mcal_src\Can_Irq.c	   619    #endif
; ..\mcal_src\Can_Irq.c	   620    #ifdef CAN_BO_PROCESSING_HWCONTROLLER5
; ..\mcal_src\Can_Irq.c	   621    #if (CAN_BO_PROCESSING_HWCONTROLLER5 == CAN_INTERRUPT)
; ..\mcal_src\Can_Irq.c	   622    Can_17_MCanP_IsrBusOffHandler(CAN_HWCONTROLLER5);
; ..\mcal_src\Can_Irq.c	   623    #endif
; ..\mcal_src\Can_Irq.c	   624    #endif
; ..\mcal_src\Can_Irq.c	   625  }
; ..\mcal_src\Can_Irq.c	   626  #endif
; ..\mcal_src\Can_Irq.c	   627  #endif
; ..\mcal_src\Can_Irq.c	   628  
; ..\mcal_src\Can_Irq.c	   629  #define IRQ_STOP_SEC_CODE
; ..\mcal_src\Can_Irq.c	   630  #include "MemMap.h"
; ..\mcal_src\Can_Irq.c	   631  
; ..\mcal_src\Can_Irq.c	   632  

	; Module end
