	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc26036a -c99 --dep-file=mcal_cfg\\.Dio_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\Dio_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\Dio_PBCfg.src ..\\mcal_cfg\\Dio_PBCfg.c"
	.compiler_name		"ctc"
	.name	"Dio_PBCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Dio_kPortChannelConfig_0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Dio_kPortChannelConfig_0:	.type	object
	.size	Dio_kPortChannelConfig_0,120
	.word	1,207,1,268,1,14,1,844
	.word	1,4,1
	.space	4
	.word	1
	.space	4
	.word	1,1,1,60
	.word	1
	.space	4
	.word	1
	.space	4
	.word	1,4083,1,8
	.word	1,69,1
	.space	4
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Dio_ConfigRoot')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.global	Dio_ConfigRoot
	.align	4
Dio_ConfigRoot:	.type	object
	.size	Dio_ConfigRoot,12
	.word	Dio_kPortChannelConfig_0
	.space	8
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	8336
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\Dio_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	178
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	184
	.byte	5,1,3
	.word	208
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	210
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	233
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	264
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	301
	.byte	7
	.byte	'_Ifx_P_ACCEN0_Bits',0,3,45,16,4,8
	.byte	'EN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_ACCEN0_Bits',0,3,79,3
	.word	337
	.byte	7
	.byte	'_Ifx_P_ACCEN1_Bits',0,3,82,16,4,6
	.byte	'unsigned int',0,4,7,8
	.byte	'reserved_0',0,4
	.word	914
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_P_ACCEN1_Bits',0,3,85,3
	.word	890
	.byte	7
	.byte	'_Ifx_P_ESR_Bits',0,3,88,16,4,8
	.byte	'EN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_ESR_Bits',0,3,107,3
	.word	979
	.byte	7
	.byte	'_Ifx_P_ID_Bits',0,3,110,16,4,8
	.byte	'MODREV',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'MODTYPE',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'MODNUMBER',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_ID_Bits',0,3,115,3
	.word	1293
	.byte	7
	.byte	'_Ifx_P_IN_Bits',0,3,118,16,4,8
	.byte	'P0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'P2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'P3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'P4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'P5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'P6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'P7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'P8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'P9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'P10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'P11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'P12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'P13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'P14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'P15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_IN_Bits',0,3,137,1,3
	.word	1394
	.byte	7
	.byte	'_Ifx_P_IOCR0_Bits',0,3,140,1,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'PC0',0,1
	.word	233
	.byte	5,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	3,5,2,35,1,8
	.byte	'PC1',0,1
	.word	233
	.byte	5,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	233
	.byte	3,5,2,35,2,8
	.byte	'PC2',0,1
	.word	233
	.byte	5,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'PC3',0,1
	.word	233
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_P_IOCR0_Bits',0,3,150,1,3
	.word	1691
	.byte	7
	.byte	'_Ifx_P_IOCR12_Bits',0,3,153,1,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'PC12',0,1
	.word	233
	.byte	5,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	3,5,2,35,1,8
	.byte	'PC13',0,1
	.word	233
	.byte	5,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	233
	.byte	3,5,2,35,2,8
	.byte	'PC14',0,1
	.word	233
	.byte	5,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'PC15',0,1
	.word	233
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_P_IOCR12_Bits',0,3,163,1,3
	.word	1892
	.byte	7
	.byte	'_Ifx_P_IOCR4_Bits',0,3,166,1,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'PC4',0,1
	.word	233
	.byte	5,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	3,5,2,35,1,8
	.byte	'PC5',0,1
	.word	233
	.byte	5,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	233
	.byte	3,5,2,35,2,8
	.byte	'PC6',0,1
	.word	233
	.byte	5,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'PC7',0,1
	.word	233
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_P_IOCR4_Bits',0,3,176,1,3
	.word	2099
	.byte	7
	.byte	'_Ifx_P_IOCR8_Bits',0,3,179,1,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'PC8',0,1
	.word	233
	.byte	5,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	3,5,2,35,1,8
	.byte	'PC9',0,1
	.word	233
	.byte	5,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	233
	.byte	3,5,2,35,2,8
	.byte	'PC10',0,1
	.word	233
	.byte	5,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'PC11',0,1
	.word	233
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_P_IOCR8_Bits',0,3,189,1,3
	.word	2300
	.byte	7
	.byte	'_Ifx_P_OMCR0_Bits',0,3,192,1,16,4,8
	.byte	'reserved_0',0,2
	.word	264
	.byte	16,0,2,35,0,8
	.byte	'PCL0',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'PCL2',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'PCL3',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'reserved_20',0,2
	.word	264
	.byte	12,0,2,35,2,0,4
	.byte	'Ifx_P_OMCR0_Bits',0,3,200,1,3
	.word	2503
	.byte	7
	.byte	'_Ifx_P_OMCR12_Bits',0,3,203,1,16,4,8
	.byte	'reserved_0',0,4
	.word	914
	.byte	28,4,2,35,2,8
	.byte	'PCL12',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'PCL13',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'PCL14',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'PCL15',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_OMCR12_Bits',0,3,210,1,3
	.word	2663
	.byte	7
	.byte	'_Ifx_P_OMCR4_Bits',0,3,213,1,16,4,8
	.byte	'reserved_0',0,4
	.word	914
	.byte	20,12,2,35,2,8
	.byte	'PCL4',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'PCL5',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'PCL6',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'PCL7',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_P_OMCR4_Bits',0,3,221,1,3
	.word	2806
	.byte	7
	.byte	'_Ifx_P_OMCR8_Bits',0,3,224,1,16,4,8
	.byte	'reserved_0',0,4
	.word	914
	.byte	24,8,2,35,2,8
	.byte	'PCL8',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'PCL9',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'PCL10',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'PCL11',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	233
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_P_OMCR8_Bits',0,3,232,1,3
	.word	2966
	.byte	7
	.byte	'_Ifx_P_OMCR_Bits',0,3,235,1,16,4,8
	.byte	'reserved_0',0,2
	.word	264
	.byte	16,0,2,35,0,8
	.byte	'PCL0',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'PCL2',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'PCL3',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'PCL4',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'PCL5',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'PCL6',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'PCL7',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'PCL8',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'PCL9',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'PCL10',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'PCL11',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'PCL12',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'PCL13',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'PCL14',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'PCL15',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_OMCR_Bits',0,3,254,1,3
	.word	3128
	.byte	7
	.byte	'_Ifx_P_OMR_Bits',0,3,129,2,16,4,8
	.byte	'PS0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'PS2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'PS3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PS4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'PS5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'PS6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PS7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'PS8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'PS9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'PS10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'PS11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'PS12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'PS13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'PS14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'PS15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'PCL0',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'PCL2',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'PCL3',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'PCL4',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'PCL5',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'PCL6',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'PCL7',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'PCL8',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'PCL9',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'PCL10',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'PCL11',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'PCL12',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'PCL13',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'PCL14',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'PCL15',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_OMR_Bits',0,3,163,2,3
	.word	3461
	.byte	7
	.byte	'_Ifx_P_OMSR0_Bits',0,3,166,2,16,4,8
	.byte	'PS0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'PS2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'PS3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	914
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR0_Bits',0,3,173,2,3
	.word	4016
	.byte	7
	.byte	'_Ifx_P_OMSR12_Bits',0,3,176,2,16,4,8
	.byte	'reserved_0',0,2
	.word	264
	.byte	12,4,2,35,0,8
	.byte	'PS12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'PS13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'PS14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'PS15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR12_Bits',0,3,184,2,3
	.word	4149
	.byte	7
	.byte	'_Ifx_P_OMSR4_Bits',0,3,187,2,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'PS4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'PS5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'PS6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PS7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	914
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR4_Bits',0,3,195,2,3
	.word	4311
	.byte	7
	.byte	'_Ifx_P_OMSR8_Bits',0,3,198,2,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'PS8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'PS9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'PS10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'PS11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,4
	.word	914
	.byte	20,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR8_Bits',0,3,206,2,3
	.word	4466
	.byte	7
	.byte	'_Ifx_P_OMSR_Bits',0,3,209,2,16,4,8
	.byte	'PS0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'PS2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'PS3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PS4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'PS5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'PS6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PS7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'PS8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'PS9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'PS10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'PS11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'PS12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'PS13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'PS14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'PS15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR_Bits',0,3,228,2,3
	.word	4624
	.byte	7
	.byte	'_Ifx_P_OUT_Bits',0,3,231,2,16,4,8
	.byte	'P0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'P2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'P3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'P4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'P5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'P6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'P7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'P8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'P9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'P10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'P11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'P12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'P13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'P14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'P15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_OUT_Bits',0,3,250,2,3
	.word	4942
	.byte	7
	.byte	'_Ifx_P_PCSR_Bits',0,3,253,2,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'SEL1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'SEL2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,2
	.word	264
	.byte	6,7,2,35,0,8
	.byte	'SEL9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'SEL10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'reserved_11',0,4
	.word	914
	.byte	20,1,2,35,2,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_PCSR_Bits',0,3,135,3,3
	.word	5242
	.byte	7
	.byte	'_Ifx_P_PDISC_Bits',0,3,138,3,16,4,8
	.byte	'PDIS0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PDIS1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'PDIS2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'PDIS3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PDIS4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'PDIS5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'PDIS6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PDIS7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'PDIS8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'PDIS9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'PDIS10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'PDIS11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'PDIS12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'PDIS13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'PDIS14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'PDIS15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_PDISC_Bits',0,3,157,3,3
	.word	5438
	.byte	7
	.byte	'_Ifx_P_PDR0_Bits',0,3,160,3,16,4,8
	.byte	'PD0',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'PL0',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PD1',0,1
	.word	233
	.byte	3,1,2,35,0,8
	.byte	'PL1',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'PD2',0,1
	.word	233
	.byte	3,5,2,35,1,8
	.byte	'PL2',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'PD3',0,1
	.word	233
	.byte	3,1,2,35,1,8
	.byte	'PL3',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'PD4',0,1
	.word	233
	.byte	3,5,2,35,2,8
	.byte	'PL4',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'PD5',0,1
	.word	233
	.byte	3,1,2,35,2,8
	.byte	'PL5',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'PD6',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'PL6',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'PD7',0,1
	.word	233
	.byte	3,1,2,35,3,8
	.byte	'PL7',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_PDR0_Bits',0,3,178,3,3
	.word	5790
	.byte	7
	.byte	'_Ifx_P_PDR1_Bits',0,3,181,3,16,4,8
	.byte	'PD8',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'PL8',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PD9',0,1
	.word	233
	.byte	3,1,2,35,0,8
	.byte	'PL9',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'PD10',0,1
	.word	233
	.byte	3,5,2,35,1,8
	.byte	'PL10',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'PD11',0,1
	.word	233
	.byte	3,1,2,35,1,8
	.byte	'PL11',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'PD12',0,1
	.word	233
	.byte	3,5,2,35,2,8
	.byte	'PL12',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'PD13',0,1
	.word	233
	.byte	3,1,2,35,2,8
	.byte	'PL13',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'PD14',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'PL14',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'PD15',0,1
	.word	233
	.byte	3,1,2,35,3,8
	.byte	'PL15',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_PDR1_Bits',0,3,199,3,3
	.word	6079
	.byte	9,3,207,3,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,6
	.byte	'int',0,4,5,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	337
	.byte	2,35,0,0,4
	.byte	'Ifx_P_ACCEN0',0,3,212,3,3
	.word	6380
	.byte	9,3,215,3,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	890
	.byte	2,35,0,0,4
	.byte	'Ifx_P_ACCEN1',0,3,220,3,3
	.word	6449
	.byte	9,3,223,3,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	979
	.byte	2,35,0,0,4
	.byte	'Ifx_P_ESR',0,3,228,3,3
	.word	6511
	.byte	9,3,231,3,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1293
	.byte	2,35,0,0,4
	.byte	'Ifx_P_ID',0,3,236,3,3
	.word	6570
	.byte	9,3,239,3,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1394
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IN',0,3,244,3,3
	.word	6628
	.byte	9,3,247,3,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1691
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IOCR0',0,3,252,3,3
	.word	6686
	.byte	9,3,255,3,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1892
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IOCR12',0,3,132,4,3
	.word	6747
	.byte	9,3,135,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2099
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IOCR4',0,3,140,4,3
	.word	6809
	.byte	9,3,143,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2300
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IOCR8',0,3,148,4,3
	.word	6870
	.byte	9,3,151,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3128
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR',0,3,156,4,3
	.word	6931
	.byte	9,3,159,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2503
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR0',0,3,164,4,3
	.word	6991
	.byte	9,3,167,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2663
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR12',0,3,172,4,3
	.word	7052
	.byte	9,3,175,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2806
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR4',0,3,180,4,3
	.word	7114
	.byte	9,3,183,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2966
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR8',0,3,188,4,3
	.word	7175
	.byte	9,3,191,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3461
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMR',0,3,196,4,3
	.word	7236
	.byte	9,3,199,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4624
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR',0,3,204,4,3
	.word	7295
	.byte	9,3,207,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4016
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR0',0,3,212,4,3
	.word	7355
	.byte	9,3,215,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4149
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR12',0,3,220,4,3
	.word	7416
	.byte	9,3,223,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4311
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR4',0,3,228,4,3
	.word	7478
	.byte	9,3,231,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4466
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR8',0,3,236,4,3
	.word	7539
	.byte	9,3,239,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4942
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OUT',0,3,244,4,3
	.word	7600
	.byte	9,3,247,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5242
	.byte	2,35,0,0,4
	.byte	'Ifx_P_PCSR',0,3,252,4,3
	.word	7659
	.byte	9,3,255,4,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5438
	.byte	2,35,0,0,4
	.byte	'Ifx_P_PDISC',0,3,132,5,3
	.word	7719
	.byte	9,3,135,5,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5790
	.byte	2,35,0,0,4
	.byte	'Ifx_P_PDR0',0,3,140,5,3
	.word	7780
	.byte	9,3,143,5,9,4,10
	.byte	'U',0,4
	.word	914
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	6397
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6079
	.byte	2,35,0,0,4
	.byte	'Ifx_P_PDR1',0,3,148,5,3
	.word	7840
	.byte	4
	.byte	'Dio_PortType',0,4,249,1,17
	.word	233
	.byte	4
	.byte	'Dio_PortLevelType',0,4,252,1,17
	.word	264
	.byte	7
	.byte	'Dio_ChannelGroupType',0,4,129,2,16,4,10
	.byte	'mask',0,2
	.word	264
	.byte	2,35,0,10
	.byte	'offset',0,1
	.word	233
	.byte	2,35,2,10
	.byte	'port',0,1
	.word	233
	.byte	2,35,3,0,4
	.byte	'Dio_ChannelGroupType',0,4,137,2,3
	.word	7949
	.byte	7
	.byte	'Dio_PortChannelIdType',0,4,139,2,16,8,10
	.byte	'Dio_PortIdConfig',0,4
	.word	301
	.byte	2,35,0,10
	.byte	'Dio_ChannelConfig',0,4
	.word	301
	.byte	2,35,4,0,4
	.byte	'Dio_PortChannelIdType',0,4,145,2,2
	.word	8051
	.byte	7
	.byte	'Dio_ConfigType',0,4,147,2,16,12,11
	.word	8051
	.byte	3
	.word	8185
	.byte	10
	.byte	'Dio_PortChannelConfigPtr',0,4
	.word	8190
	.byte	2,35,0,11
	.word	7949
	.byte	3
	.word	8229
	.byte	10
	.byte	'Dio_ChannelGroupConfigPtr',0,4
	.word	8234
	.byte	2,35,4,10
	.byte	'Dio_ChannelGroupConfigSize',0,4
	.word	301
	.byte	2,35,8,0,12,12
	.word	8164
	.byte	13,0,0
.L10:
	.byte	11
	.word	8311
	.byte	12,120
	.word	8051
	.byte	13,14,0
.L11:
	.byte	11
	.word	8325
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,23,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0
	.byte	0,11,38,0,73,19,0,0,12,1,1,11,15,73,19,0,0,13,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L13-.L12
.L12:
	.half	3
	.word	.L15-.L14
.L14:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'..\\mcal_cfg\\Dio_PBCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'Dio.h',0,1,0,0,0
.L15:
.L13:
	.sdecl	'.debug_info',debug,cluster('Dio_ConfigRoot')
	.sect	'.debug_info'
.L6:
	.word	207
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\Dio_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Dio_ConfigRoot',0,1,132,1,22
	.word	.L10
	.byte	1,5,3
	.word	Dio_ConfigRoot
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_ConfigRoot')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_kPortChannelConfig_0')
	.sect	'.debug_info'
.L8:
	.word	215
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_cfg\\Dio_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Dio_kPortChannelConfig_0',0,1,67,36
	.word	.L11
	.byte	5,3
	.word	Dio_kPortChannelConfig_0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_kPortChannelConfig_0')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0

; ..\mcal_cfg\Dio_PBCfg.c	     1  /*******************************************************************************
; ..\mcal_cfg\Dio_PBCfg.c	     2  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2014)                                 **
; ..\mcal_cfg\Dio_PBCfg.c	     4  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	     5  ** All rights reserved.                                                       **
; ..\mcal_cfg\Dio_PBCfg.c	     6  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_cfg\Dio_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_cfg\Dio_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_cfg\Dio_PBCfg.c	    10  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    11  ********************************************************************************
; ..\mcal_cfg\Dio_PBCfg.c	    12  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    13  **   $FILENAME   : Dio_PBCfg.c $                                              **
; ..\mcal_cfg\Dio_PBCfg.c	    14  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    15  **   $CC VERSION : \main\30 $                                                 **
; ..\mcal_cfg\Dio_PBCfg.c	    16  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    17  **   DATE, TIME  : 2020-07-10, 14:56:10                                       **
; ..\mcal_cfg\Dio_PBCfg.c	    18  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    19  **   GENERATOR   : Build b141014-0350                                         **
; ..\mcal_cfg\Dio_PBCfg.c	    20  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    21  **   AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_cfg\Dio_PBCfg.c	    22  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    23  **   VENDOR      : Infineon Technologies                                      **
; ..\mcal_cfg\Dio_PBCfg.c	    24  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    25  **   DESCRIPTION : DIO configuration generated out of ECU configuration       **
; ..\mcal_cfg\Dio_PBCfg.c	    26  **                  file                                                      **
; ..\mcal_cfg\Dio_PBCfg.c	    27  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    28  **   MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_cfg\Dio_PBCfg.c	    29  **                                                                            **
; ..\mcal_cfg\Dio_PBCfg.c	    30  *******************************************************************************/
; ..\mcal_cfg\Dio_PBCfg.c	    31  
; ..\mcal_cfg\Dio_PBCfg.c	    32  
; ..\mcal_cfg\Dio_PBCfg.c	    33  /*******************************************************************************
; ..\mcal_cfg\Dio_PBCfg.c	    34  **                             Includes                                       **
; ..\mcal_cfg\Dio_PBCfg.c	    35  *******************************************************************************/
; ..\mcal_cfg\Dio_PBCfg.c	    36  
; ..\mcal_cfg\Dio_PBCfg.c	    37  /* Include Port Module File */
; ..\mcal_cfg\Dio_PBCfg.c	    38  #include "Dio.h"
; ..\mcal_cfg\Dio_PBCfg.c	    39  
; ..\mcal_cfg\Dio_PBCfg.c	    40  /*******************************************************************************
; ..\mcal_cfg\Dio_PBCfg.c	    41  **                      Private Macro Definitions                             **
; ..\mcal_cfg\Dio_PBCfg.c	    42  *******************************************************************************/
; ..\mcal_cfg\Dio_PBCfg.c	    43  
; ..\mcal_cfg\Dio_PBCfg.c	    44  /*******************************************************************************
; ..\mcal_cfg\Dio_PBCfg.c	    45  **                      Global Constant Definitions                           **
; ..\mcal_cfg\Dio_PBCfg.c	    46  *******************************************************************************/
; ..\mcal_cfg\Dio_PBCfg.c	    47  /*
; ..\mcal_cfg\Dio_PBCfg.c	    48    Information on MISRA-2004 violations:
; ..\mcal_cfg\Dio_PBCfg.c	    49    - MISRA Rule 19.1: Only preprocessor statements and comments before 
; ..\mcal_cfg\Dio_PBCfg.c	    50      '#include'Inclusion of the file MemMap.h is done for memory mapping of 
; ..\mcal_cfg\Dio_PBCfg.c	    51      the code and data. This is violation of MISRA Rule 19.1, but cannot 
; ..\mcal_cfg\Dio_PBCfg.c	    52      be avoided.
; ..\mcal_cfg\Dio_PBCfg.c	    53  */
; ..\mcal_cfg\Dio_PBCfg.c	    54  
; ..\mcal_cfg\Dio_PBCfg.c	    55  /* Memory mapping of the DIO configuration */
; ..\mcal_cfg\Dio_PBCfg.c	    56  #define DIO_START_SEC_POSTBUILDCFG
; ..\mcal_cfg\Dio_PBCfg.c	    57  #include "MemMap.h"
; ..\mcal_cfg\Dio_PBCfg.c	    58  
; ..\mcal_cfg\Dio_PBCfg.c	    59  
; ..\mcal_cfg\Dio_PBCfg.c	    60  
; ..\mcal_cfg\Dio_PBCfg.c	    61  /*
; ..\mcal_cfg\Dio_PBCfg.c	    62        Configuration of DIO Channel groups 
; ..\mcal_cfg\Dio_PBCfg.c	    63        DioConfig 1
; ..\mcal_cfg\Dio_PBCfg.c	    64  */
; ..\mcal_cfg\Dio_PBCfg.c	    65  /* No Groups are configured */
; ..\mcal_cfg\Dio_PBCfg.c	    66  
; ..\mcal_cfg\Dio_PBCfg.c	    67  static const Dio_PortChannelIdType Dio_kPortChannelConfig_0[] =
; ..\mcal_cfg\Dio_PBCfg.c	    68  {
; ..\mcal_cfg\Dio_PBCfg.c	    69    { /* Port0 */
; ..\mcal_cfg\Dio_PBCfg.c	    70       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	    71       (0x00cfU)
; ..\mcal_cfg\Dio_PBCfg.c	    72    },
; ..\mcal_cfg\Dio_PBCfg.c	    73    { /* Port2 */
; ..\mcal_cfg\Dio_PBCfg.c	    74       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	    75       (0x010cU)
; ..\mcal_cfg\Dio_PBCfg.c	    76    },
; ..\mcal_cfg\Dio_PBCfg.c	    77    { /* Port10 */
; ..\mcal_cfg\Dio_PBCfg.c	    78       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	    79       (0x000eU)
; ..\mcal_cfg\Dio_PBCfg.c	    80    },
; ..\mcal_cfg\Dio_PBCfg.c	    81    { /* Port11 */
; ..\mcal_cfg\Dio_PBCfg.c	    82       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	    83       (0x034cU)
; ..\mcal_cfg\Dio_PBCfg.c	    84    },
; ..\mcal_cfg\Dio_PBCfg.c	    85    { /* Port13 */
; ..\mcal_cfg\Dio_PBCfg.c	    86       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	    87       (0x0004U)
; ..\mcal_cfg\Dio_PBCfg.c	    88    },
; ..\mcal_cfg\Dio_PBCfg.c	    89    { /* Port14 */
; ..\mcal_cfg\Dio_PBCfg.c	    90       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	    91       (0x0000U)
; ..\mcal_cfg\Dio_PBCfg.c	    92    },
; ..\mcal_cfg\Dio_PBCfg.c	    93    { /* Port15 */
; ..\mcal_cfg\Dio_PBCfg.c	    94       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	    95       (0x0000U)
; ..\mcal_cfg\Dio_PBCfg.c	    96    },
; ..\mcal_cfg\Dio_PBCfg.c	    97    { /* Port20 */
; ..\mcal_cfg\Dio_PBCfg.c	    98       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	    99       (0x0001U)
; ..\mcal_cfg\Dio_PBCfg.c	   100    },
; ..\mcal_cfg\Dio_PBCfg.c	   101    { /* Port21 */
; ..\mcal_cfg\Dio_PBCfg.c	   102       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	   103       (0x003cU)
; ..\mcal_cfg\Dio_PBCfg.c	   104    },
; ..\mcal_cfg\Dio_PBCfg.c	   105    { /* Port22 */
; ..\mcal_cfg\Dio_PBCfg.c	   106       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	   107       (0x0000U)
; ..\mcal_cfg\Dio_PBCfg.c	   108    },
; ..\mcal_cfg\Dio_PBCfg.c	   109    { /* Port23 */
; ..\mcal_cfg\Dio_PBCfg.c	   110       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	   111       (0x0000U)
; ..\mcal_cfg\Dio_PBCfg.c	   112    },
; ..\mcal_cfg\Dio_PBCfg.c	   113    { /* Port33 */
; ..\mcal_cfg\Dio_PBCfg.c	   114       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	   115       (0x0ff3U)
; ..\mcal_cfg\Dio_PBCfg.c	   116    },
; ..\mcal_cfg\Dio_PBCfg.c	   117    { /* Port34 */
; ..\mcal_cfg\Dio_PBCfg.c	   118       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	   119       (0x0008U)
; ..\mcal_cfg\Dio_PBCfg.c	   120    },
; ..\mcal_cfg\Dio_PBCfg.c	   121    { /* Port40 */
; ..\mcal_cfg\Dio_PBCfg.c	   122       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	   123       (0x0045U)
; ..\mcal_cfg\Dio_PBCfg.c	   124    },
; ..\mcal_cfg\Dio_PBCfg.c	   125    { /* Port41 */
; ..\mcal_cfg\Dio_PBCfg.c	   126       DIO_PORT_CONFIGURED,
; ..\mcal_cfg\Dio_PBCfg.c	   127       (0x0000U)
; ..\mcal_cfg\Dio_PBCfg.c	   128    }
; ..\mcal_cfg\Dio_PBCfg.c	   129  };
; ..\mcal_cfg\Dio_PBCfg.c	   130  
; ..\mcal_cfg\Dio_PBCfg.c	   131  
; ..\mcal_cfg\Dio_PBCfg.c	   132  const Dio_ConfigType Dio_ConfigRoot[1] =
; ..\mcal_cfg\Dio_PBCfg.c	   133  {
; ..\mcal_cfg\Dio_PBCfg.c	   134    {
; ..\mcal_cfg\Dio_PBCfg.c	   135  
; ..\mcal_cfg\Dio_PBCfg.c	   136      /* Dio Port and Channelconfiguration set 0 */
; ..\mcal_cfg\Dio_PBCfg.c	   137      &Dio_kPortChannelConfig_0[0],
; ..\mcal_cfg\Dio_PBCfg.c	   138      /* Dio Channelgroup configuration set 0 */
; ..\mcal_cfg\Dio_PBCfg.c	   139      NULL_PTR,
; ..\mcal_cfg\Dio_PBCfg.c	   140      /* Configured number of Dio Channelgroups for configuration set 0 */
; ..\mcal_cfg\Dio_PBCfg.c	   141      DIO_CHANNELGROUPCOUNT_0,
; ..\mcal_cfg\Dio_PBCfg.c	   142    }
; ..\mcal_cfg\Dio_PBCfg.c	   143  };
; ..\mcal_cfg\Dio_PBCfg.c	   144  
; ..\mcal_cfg\Dio_PBCfg.c	   145  #define DIO_STOP_SEC_POSTBUILDCFG
; ..\mcal_cfg\Dio_PBCfg.c	   146  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_cfg\Dio_PBCfg.c	   147    is allowed only for MemMap.h*/
; ..\mcal_cfg\Dio_PBCfg.c	   148  #include "MemMap.h"
; ..\mcal_cfg\Dio_PBCfg.c	   149  
; ..\mcal_cfg\Dio_PBCfg.c	   150  /*******************************************************************************
; ..\mcal_cfg\Dio_PBCfg.c	   151  **                      Private Constant Definitions                          **
; ..\mcal_cfg\Dio_PBCfg.c	   152  *******************************************************************************/

	; Module end
