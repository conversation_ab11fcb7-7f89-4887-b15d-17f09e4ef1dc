	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc15128a -c99 --dep-file=mcal_cfg\\.Dma_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\Dma_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\Dma_PBCfg.src ..\\mcal_cfg\\Dma_PBCfg.c"
	.compiler_name		"ctc"
	.name	"Dma_PBCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Dma_kChannelConfigRoot0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Dma_kChannelConfigRoot0:	.type	object
	.size	Dma_kChannelConfigRoot0,12
	.word	5242880,137392264
	.byte	1
	.space	1
	.byte	3
	.space	1
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Dma_ConfigRoot')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.global	Dma_ConfigRoot
	.align	4
Dma_ConfigRoot:	.type	object
	.size	Dma_ConfigRoot,24
	.word	Dma_kChannelConfigRoot0
	.space	8
	.word	84082688,84082688
	.byte	1
	.space	3
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1784
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\Dma_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	178
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	184
	.byte	5,1,3
	.word	208
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	210
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	233
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	264
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	301
	.byte	6
	.byte	'unsigned int',0,4,7,4
	.byte	'unsigned_int',0,3,121,22
	.word	337
	.byte	7,4,215,3,9,1,8
	.byte	'DMA_CH_INCREMENT_DIR_NEG',0,0,8
	.byte	'DMA_CH_INCREMENT_DIR_POS',0,1,0,4
	.byte	'Dma_ChIncrementDirectionType',0,4,219,3,3
	.word	374
	.byte	7,4,226,3,9,1,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_1',0,0,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_2',0,1,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_4',0,2,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_8',0,3,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_16',0,4,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_32',0,5,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_64',0,6,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_128',0,7,0,4
	.byte	'Dma_ChAddressModOffsetType',0,4,236,3,3
	.word	473
	.byte	7,4,243,3,9,1,8
	.byte	'DMA_CH_CIRC_BUFF_DISABLE',0,0,8
	.byte	'DMA_CH_CIRC_BUFF_ENABLE',0,1,0,4
	.byte	'Dma_ChCircularBuffEnType',0,4,247,3,3
	.word	737
	.byte	7,4,254,3,9,1,8
	.byte	'DMA_CH_WRAP_DISABLE',0,0,8
	.byte	'DMA_CH_WRAP_ENABLE',0,1,0,4
	.byte	'Dma_ChWrapEnType',0,4,130,4,3
	.word	831
	.byte	7,4,133,4,9,1,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_1',0,0,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_2',0,1,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_4',0,2,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_8',0,3,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_16',0,4,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_32',0,5,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_64',0,6,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_128',0,7,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_256',0,8,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_512',0,9,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_1KB',0,10,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_2KB',0,11,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_4KB',0,12,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_8KB',0,13,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_16KB',0,14,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_32KB',0,15,0,4
	.byte	'Dma_ChCircBuffSizeType',0,4,151,4,3
	.word	907
	.byte	9
	.byte	'Dma_ChannelConfigType',0,4,174,4,16,12,10
	.byte	'DmaChannelConfig',0,4
	.word	301
	.byte	2,35,0,10
	.byte	'DmaAddrIntControl',0,4
	.word	301
	.byte	2,35,4,10
	.byte	'DmaHwResourceMode',0,1
	.word	233
	.byte	2,35,8,10
	.byte	'DmaChannelHwPartitionConfig',0,1
	.word	233
	.byte	2,35,9,10
	.byte	'DmaChannelNumber',0,1
	.word	233
	.byte	2,35,10,0,4
	.byte	'Dma_ChannelConfigType',0,4,181,4,2
	.word	1369
	.byte	9
	.byte	'Dma_ConfigType',0,4,187,4,16,24,11
	.word	1369
	.byte	3
	.word	1593
	.byte	10
	.byte	'ChannelCfgPtr',0,4
	.word	1598
	.byte	2,35,0,10
	.byte	'DmaPat0',0,4
	.word	301
	.byte	2,35,4,10
	.byte	'DmaPat1',0,4
	.word	301
	.byte	2,35,8,10
	.byte	'DmaMovEng0Err',0,4
	.word	301
	.byte	2,35,12,10
	.byte	'DmaMovEng1Err',0,4
	.word	301
	.byte	2,35,16,10
	.byte	'ChannelsConfigured',0,1
	.word	233
	.byte	2,35,20,0,4
	.byte	'Dma_ConfigType',0,4,195,4,2
	.word	1572
	.byte	12,24
	.word	1572
	.byte	13,0,0
.L10:
	.byte	11
	.word	1759
	.byte	12,12
	.word	1369
	.byte	13,0,0
.L11:
	.byte	11
	.word	1773
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,4,1,58,15,59,15,57,15,11,15,0,0,8,40,0,3,8
	.byte	28,13,0,0,9,19,1,3,8,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0,0,11,38,0,73,19,0,0,12
	.byte	1,1,11,15,73,19,0,0,13,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L13-.L12
.L12:
	.half	3
	.word	.L15-.L14
.L14:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc',0
	.byte	0
	.byte	'..\\mcal_cfg\\Dma_PBCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Mcal_TcLib.h',0,1,0,0
	.byte	'Dma.h',0,2,0,0,0
.L15:
.L13:
	.sdecl	'.debug_info',debug,cluster('Dma_ConfigRoot')
	.sect	'.debug_info'
.L6:
	.word	206
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\Dma_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Dma_ConfigRoot',0,1,80,22
	.word	.L10
	.byte	1,5,3
	.word	Dma_ConfigRoot
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dma_ConfigRoot')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Dma_kChannelConfigRoot0')
	.sect	'.debug_info'
.L8:
	.word	214
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_cfg\\Dma_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Dma_kChannelConfigRoot0',0,1,66,36
	.word	.L11
	.byte	5,3
	.word	Dma_kChannelConfigRoot0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dma_kChannelConfigRoot0')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0

; ..\mcal_cfg\Dma_PBCfg.c	     1  /******************************************************************************
; ..\mcal_cfg\Dma_PBCfg.c	     2  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_cfg\Dma_PBCfg.c	     4  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	     5  ** All rights reserved.                                                      **
; ..\mcal_cfg\Dma_PBCfg.c	     6  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_cfg\Dma_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_cfg\Dma_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_cfg\Dma_PBCfg.c	    10  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    11  *******************************************************************************
; ..\mcal_cfg\Dma_PBCfg.c	    12  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    13  **  FILENAME  : Dma_PBCfg.c                                                  **
; ..\mcal_cfg\Dma_PBCfg.c	    14  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    15  **  $CC VERSION : \main\13 $                                                 **
; ..\mcal_cfg\Dma_PBCfg.c	    16  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    17  **  DATE, TIME: 2020-07-10, 14:56:12                                         **
; ..\mcal_cfg\Dma_PBCfg.c	    18  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    19  **  GENERATOR : Build b141014-0350                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    20  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    21  **  AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\mcal_cfg\Dma_PBCfg.c	    22  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    23  **  VENDOR    : Infineon Technologies                                        **
; ..\mcal_cfg\Dma_PBCfg.c	    24  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    25  **  DESCRIPTION  : DMA configuration generated out of ECU configuration      **
; ..\mcal_cfg\Dma_PBCfg.c	    26  **                 file (Dma.bmd/.xdm)                                       **
; ..\mcal_cfg\Dma_PBCfg.c	    27  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    28  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_cfg\Dma_PBCfg.c	    29  **                                                                           **
; ..\mcal_cfg\Dma_PBCfg.c	    30  ******************************************************************************/
; ..\mcal_cfg\Dma_PBCfg.c	    31  /*******************************************************************************
; ..\mcal_cfg\Dma_PBCfg.c	    32  **                                                                            **
; ..\mcal_cfg\Dma_PBCfg.c	    33  **  TRACEBILITY : 
; ..\mcal_cfg\Dma_PBCfg.c	    34      [cover parentID = DS_NAS_DMA_PR69_PR469_PR122_PR123,DS_NAS_DMA_PR647,
; ..\mcal_cfg\Dma_PBCfg.c	    35      DS_NAS_DMA_PR446,DS_NAS_DMA_PR699,DS_NAS_DMA_PR709,
; ..\mcal_cfg\Dma_PBCfg.c	    36      SAS_NAS_DMA_PR914,SAS_NAS_DMA_PR915,SAS_NAS_DMA_PR916,
; ..\mcal_cfg\Dma_PBCfg.c	    37      SAS_NAS_DMA_PR82,DS_NAS_DMA_PR704,SAS_NAS_DMA_PR60,
; ..\mcal_cfg\Dma_PBCfg.c	    38      DS_NAS_DMA_PR707,DS_NAS_DMA_PR708,DS_NAS_DMA_PR712
; ..\mcal_cfg\Dma_PBCfg.c	    39      ]
; ..\mcal_cfg\Dma_PBCfg.c	    40  **  [/cover]                                                                 **
; ..\mcal_cfg\Dma_PBCfg.c	    41  *******************************************************************************/
; ..\mcal_cfg\Dma_PBCfg.c	    42  
; ..\mcal_cfg\Dma_PBCfg.c	    43  /*******************************************************************************
; ..\mcal_cfg\Dma_PBCfg.c	    44  **                            Includes                                        **
; ..\mcal_cfg\Dma_PBCfg.c	    45  *******************************************************************************/
; ..\mcal_cfg\Dma_PBCfg.c	    46  /* Include module header file */
; ..\mcal_cfg\Dma_PBCfg.c	    47  #include "Dma.h"
; ..\mcal_cfg\Dma_PBCfg.c	    48  /*******************************************************************************
; ..\mcal_cfg\Dma_PBCfg.c	    49  **                      Private Macro Definitions                             **
; ..\mcal_cfg\Dma_PBCfg.c	    50  *******************************************************************************/
; ..\mcal_cfg\Dma_PBCfg.c	    51  
; ..\mcal_cfg\Dma_PBCfg.c	    52  /*******************************************************************************
; ..\mcal_cfg\Dma_PBCfg.c	    53  **                      Global Constant Definitions                           **
; ..\mcal_cfg\Dma_PBCfg.c	    54  *******************************************************************************/
; ..\mcal_cfg\Dma_PBCfg.c	    55  /*
; ..\mcal_cfg\Dma_PBCfg.c	    56                       Container: DmaChannelConfiguration
; ..\mcal_cfg\Dma_PBCfg.c	    57  */
; ..\mcal_cfg\Dma_PBCfg.c	    58  
; ..\mcal_cfg\Dma_PBCfg.c	    59  /* Memory Mapping the configuration constant */
; ..\mcal_cfg\Dma_PBCfg.c	    60  #define DMA_START_SEC_POSTBUILDCFG
; ..\mcal_cfg\Dma_PBCfg.c	    61  #include "MemMap.h"
; ..\mcal_cfg\Dma_PBCfg.c	    62  
; ..\mcal_cfg\Dma_PBCfg.c	    63  /************************** DMA Channel Config Root ***************************/
; ..\mcal_cfg\Dma_PBCfg.c	    64  
; ..\mcal_cfg\Dma_PBCfg.c	    65  /******** Configuration of channels in DmaConfigSet(0) ********/
; ..\mcal_cfg\Dma_PBCfg.c	    66  static const Dma_ChannelConfigType Dma_kChannelConfigRoot0[]=
; ..\mcal_cfg\Dma_PBCfg.c	    67  {
; ..\mcal_cfg\Dma_PBCfg.c	    68   {
; ..\mcal_cfg\Dma_PBCfg.c	    69  /* Configuration for DMA Channel(3) */
; ..\mcal_cfg\Dma_PBCfg.c	    70    0X00500000U, /* Configuration for DMA register CHCFGR */
; ..\mcal_cfg\Dma_PBCfg.c	    71    0X08307088U, /* Configuration for DMA register ADICR */
; ..\mcal_cfg\Dma_PBCfg.c	    72    0X01U,       /* Configuration for DMA register MODE */
; ..\mcal_cfg\Dma_PBCfg.c	    73    0X00U,       /* Configuration for DMA register HRR */
; ..\mcal_cfg\Dma_PBCfg.c	    74    3U           /*DMA channel ID*/
; ..\mcal_cfg\Dma_PBCfg.c	    75   }
; ..\mcal_cfg\Dma_PBCfg.c	    76  };
; ..\mcal_cfg\Dma_PBCfg.c	    77  
; ..\mcal_cfg\Dma_PBCfg.c	    78  /*************************** DMA Config Root **********************************/
; ..\mcal_cfg\Dma_PBCfg.c	    79  
; ..\mcal_cfg\Dma_PBCfg.c	    80  const Dma_ConfigType Dma_ConfigRoot[1]=
; ..\mcal_cfg\Dma_PBCfg.c	    81  {
; ..\mcal_cfg\Dma_PBCfg.c	    82  /*************** Configuration for DmaConfigSet(0) ***************/
; ..\mcal_cfg\Dma_PBCfg.c	    83   {
; ..\mcal_cfg\Dma_PBCfg.c	    84    &Dma_kChannelConfigRoot0[0U], /* Address of channel config root
; ..\mcal_cfg\Dma_PBCfg.c	    85                                    for DMA configuration [0] */
; ..\mcal_cfg\Dma_PBCfg.c	    86    0X00000000U, /* Configuration for DMA register PRR0 */
; ..\mcal_cfg\Dma_PBCfg.c	    87    0X00000000U, /* Configuration for DMA register PRR1 */
; ..\mcal_cfg\Dma_PBCfg.c	    88    0X05030000U, /* Configuration for DMA register ERR0 */
; ..\mcal_cfg\Dma_PBCfg.c	    89    0X05030000U, /* Configuration for DMA register ERR1 */
; ..\mcal_cfg\Dma_PBCfg.c	    90    1U           /*Number of channels configured*/
; ..\mcal_cfg\Dma_PBCfg.c	    91   }
; ..\mcal_cfg\Dma_PBCfg.c	    92  };
; ..\mcal_cfg\Dma_PBCfg.c	    93  
; ..\mcal_cfg\Dma_PBCfg.c	    94  #define DMA_STOP_SEC_POSTBUILDCFG
; ..\mcal_cfg\Dma_PBCfg.c	    95  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is 
; ..\mcal_cfg\Dma_PBCfg.c	    96   allowed only for MemMap.h*/
; ..\mcal_cfg\Dma_PBCfg.c	    97  #include "MemMap.h"

	; Module end
