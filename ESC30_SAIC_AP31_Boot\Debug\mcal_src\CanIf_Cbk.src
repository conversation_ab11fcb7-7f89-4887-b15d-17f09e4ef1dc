	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc30132a -c99 --dep-file=mcal_src\\.CanIf_Cbk.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\CanIf_Cbk.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\CanIf_Cbk.src ..\\mcal_src\\CanIf_Cbk.c"
	.compiler_name		"ctc"
	.name	"CanIf_Cbk"

	
$TC16X
	
	.sdecl	'.text.CanIf_Cbk.CanIf_RxIndication',code,cluster('CanIf_RxIndication')
	.sect	'.text.CanIf_Cbk.CanIf_RxIndication'
	.align	2
	
	.global	CanIf_RxIndication

; ..\mcal_src\CanIf_Cbk.c	     1  /******************************************************************************
; ..\mcal_src\CanIf_Cbk.c	     2  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	     3  ** Copyright (C) Infineon Technologies (2018)                                **
; ..\mcal_src\CanIf_Cbk.c	     4  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\CanIf_Cbk.c	     6  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\CanIf_Cbk.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\CanIf_Cbk.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\CanIf_Cbk.c	    10  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    11  *******************************************************************************
; ..\mcal_src\CanIf_Cbk.c	    12  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    13  **  $FILENAME   : CanIf_Cbk.c $                                              **
; ..\mcal_src\CanIf_Cbk.c	    14  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    15  **  $CC VERSION : \main\13 $                                                 **
; ..\mcal_src\CanIf_Cbk.c	    16  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    17  **  $DATE       : 2018-01-30 $                                               **
; ..\mcal_src\CanIf_Cbk.c	    18  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\CanIf_Cbk.c	    20  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\CanIf_Cbk.c	    22  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    23  **  DESCRIPTION : Implementation of CAN interface callback functions for     **
; ..\mcal_src\CanIf_Cbk.c	    24  **                module testing                                             **
; ..\mcal_src\CanIf_Cbk.c	    25  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    26  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\CanIf_Cbk.c	    27  **                                                                           **
; ..\mcal_src\CanIf_Cbk.c	    28  ******************************************************************************/
; ..\mcal_src\CanIf_Cbk.c	    29  
; ..\mcal_src\CanIf_Cbk.c	    30  /*******************************************************************************
; ..\mcal_src\CanIf_Cbk.c	    31  **                      Includes                                              **
; ..\mcal_src\CanIf_Cbk.c	    32  *******************************************************************************/
; ..\mcal_src\CanIf_Cbk.c	    33  
; ..\mcal_src\CanIf_Cbk.c	    34  #include "Mcal.h"
; ..\mcal_src\CanIf_Cbk.c	    35  #include "CanIf_Cbk.h"
; ..\mcal_src\CanIf_Cbk.c	    36  #include "Test_Print.h"
; ..\mcal_src\CanIf_Cbk.c	    37  #include "Platform_Types.h"
; ..\mcal_src\CanIf_Cbk.c	    38  
; ..\mcal_src\CanIf_Cbk.c	    39  #include "Uds_CanIf.h"
; ..\mcal_src\CanIf_Cbk.c	    40  #ifdef ECUM_USES_CAN
; ..\mcal_src\CanIf_Cbk.c	    41  /* Macro to set MSB for Extended messages */
; ..\mcal_src\CanIf_Cbk.c	    42  #define CAN_EXTENDED_MSB_SET            (0x80000000U)
; ..\mcal_src\CanIf_Cbk.c	    43  
; ..\mcal_src\CanIf_Cbk.c	    44  uint8 RXD_FLAG=0;
; ..\mcal_src\CanIf_Cbk.c	    45  
; ..\mcal_src\CanIf_Cbk.c	    46  /*******************************************************************************
; ..\mcal_src\CanIf_Cbk.c	    47                        CanIf_RxIndication
; ..\mcal_src\CanIf_Cbk.c	    48  *******************************************************************************/
; ..\mcal_src\CanIf_Cbk.c	    49  
; ..\mcal_src\CanIf_Cbk.c	    50  
; ..\mcal_src\CanIf_Cbk.c	    51  void CanIf_RxIndication(Can_HwHandleType Hrh,
; Function CanIf_RxIndication
.L4:
CanIf_RxIndication:	.type	func

; ..\mcal_src\CanIf_Cbk.c	    52                          Can_IdType CanId,
; ..\mcal_src\CanIf_Cbk.c	    53                          uint8 CanDlc, 
; ..\mcal_src\CanIf_Cbk.c	    54                          const uint8 *CanSduPtr)
; ..\mcal_src\CanIf_Cbk.c	    55  {
; ..\mcal_src\CanIf_Cbk.c	    56  	uint8 *data=(uint8*)CanSduPtr;
; ..\mcal_src\CanIf_Cbk.c	    57  	RXD_FLAG++;
; ..\mcal_src\CanIf_Cbk.c	    58  	Uds_CanIf_RxIndication((uint8)Hrh,CanId,CanDlc,data);
	extr.u	d4,d4,#0,#8
	movh.a	a15,#@his(RXD_FLAG)
.L87:
	ld.bu	d15,[a15]@los(RXD_FLAG)
.L96:
	add	d15,#1
	st.b	[a15]@los(RXD_FLAG),d15
.L97:
	j	Uds_CanIf_RxIndication
.L60:
	
__CanIf_RxIndication_function_end:
	.size	CanIf_RxIndication,__CanIf_RxIndication_function_end-CanIf_RxIndication
.L25:
	; End of function
	
	.sdecl	'.text.CanIf_Cbk.CanIf_TxConfirmation',code,cluster('CanIf_TxConfirmation')
	.sect	'.text.CanIf_Cbk.CanIf_TxConfirmation'
	.align	2
	
	.global	CanIf_TxConfirmation

; ..\mcal_src\CanIf_Cbk.c	    59  }
; ..\mcal_src\CanIf_Cbk.c	    60  
; ..\mcal_src\CanIf_Cbk.c	    61  /*******************************************************************************
; ..\mcal_src\CanIf_Cbk.c	    62                        CanIf_TxConfirmation
; ..\mcal_src\CanIf_Cbk.c	    63  *******************************************************************************/
; ..\mcal_src\CanIf_Cbk.c	    64  PduIdType testPDU;
; ..\mcal_src\CanIf_Cbk.c	    65  
; ..\mcal_src\CanIf_Cbk.c	    66  void CanIf_TxConfirmation (PduIdType PduId)
; Function CanIf_TxConfirmation
.L6:
CanIf_TxConfirmation:	.type	func

; ..\mcal_src\CanIf_Cbk.c	    67  {
; ..\mcal_src\CanIf_Cbk.c	    68  	Uds_CanIf_TxConfirmation(PduId);
	j	Uds_CanIf_TxConfirmation
.L69:
	
__CanIf_TxConfirmation_function_end:
	.size	CanIf_TxConfirmation,__CanIf_TxConfirmation_function_end-CanIf_TxConfirmation
.L30:
	; End of function
	
	.sdecl	'.text.CanIf_Cbk.CanIf_ControllerBusOff',code,cluster('CanIf_ControllerBusOff')
	.sect	'.text.CanIf_Cbk.CanIf_ControllerBusOff'
	.align	2
	
	.global	CanIf_ControllerBusOff

; ..\mcal_src\CanIf_Cbk.c	    69  }
; ..\mcal_src\CanIf_Cbk.c	    70  
; ..\mcal_src\CanIf_Cbk.c	    71  /*******************************************************************************
; ..\mcal_src\CanIf_Cbk.c	    72                        CanIf_ControllerBusOff
; ..\mcal_src\CanIf_Cbk.c	    73  *******************************************************************************/
; ..\mcal_src\CanIf_Cbk.c	    74  void CanIf_ControllerBusOff(uint8 ControllerId)
; Function CanIf_ControllerBusOff
.L8:
CanIf_ControllerBusOff:	.type	func

; ..\mcal_src\CanIf_Cbk.c	    75  {
; ..\mcal_src\CanIf_Cbk.c	    76  
; ..\mcal_src\CanIf_Cbk.c	    77  }
	ret
.L71:
	
__CanIf_ControllerBusOff_function_end:
	.size	CanIf_ControllerBusOff,__CanIf_ControllerBusOff_function_end-CanIf_ControllerBusOff
.L35:
	; End of function
	
	.sdecl	'.text.CanIf_Cbk.CanIf_ControllerModeIndication',code,cluster('CanIf_ControllerModeIndication')
	.sect	'.text.CanIf_Cbk.CanIf_ControllerModeIndication'
	.align	2
	
	.global	CanIf_ControllerModeIndication

; ..\mcal_src\CanIf_Cbk.c	    78  
; ..\mcal_src\CanIf_Cbk.c	    79  /*******************************************************************************
; ..\mcal_src\CanIf_Cbk.c	    80                      CanIf_ControllerModeIndication
; ..\mcal_src\CanIf_Cbk.c	    81  *******************************************************************************/
; ..\mcal_src\CanIf_Cbk.c	    82  void CanIf_ControllerModeIndication( uint8 ControllerId,
; Function CanIf_ControllerModeIndication
.L10:
CanIf_ControllerModeIndication:	.type	func

; ..\mcal_src\CanIf_Cbk.c	    83                                       CanIf_ControllerModeType ControllerMode )
; ..\mcal_src\CanIf_Cbk.c	    84  {
; ..\mcal_src\CanIf_Cbk.c	    85  //  print_f("\n Callback function CanIf_ControllerModeIndication is called!\n");
; ..\mcal_src\CanIf_Cbk.c	    86  //  print_f(" ControllerId = %d \n", ControllerId);
; ..\mcal_src\CanIf_Cbk.c	    87  //  print_f(" ControllerMode = %d \n", ControllerMode);
; ..\mcal_src\CanIf_Cbk.c	    88  }
	ret
.L73:
	
__CanIf_ControllerModeIndication_function_end:
	.size	CanIf_ControllerModeIndication,__CanIf_ControllerModeIndication_function_end-CanIf_ControllerModeIndication
.L40:
	; End of function
	
	.sdecl	'.text.CanIf_Cbk.CanLPudReceiveCalloutFunction',code,cluster('CanLPudReceiveCalloutFunction')
	.sect	'.text.CanIf_Cbk.CanLPudReceiveCalloutFunction'
	.align	2
	
	.global	CanLPudReceiveCalloutFunction

; ..\mcal_src\CanIf_Cbk.c	    89  
; ..\mcal_src\CanIf_Cbk.c	    90  /*******************************************************************************
; ..\mcal_src\CanIf_Cbk.c	    91                    CAN L-PDU Rx Callout Function Definition
; ..\mcal_src\CanIf_Cbk.c	    92  *******************************************************************************/
; ..\mcal_src\CanIf_Cbk.c	    93  boolean CanLPudReceiveCalloutFunction(Can_HwHandleType Hrh,
; Function CanLPudReceiveCalloutFunction
.L12:
CanLPudReceiveCalloutFunction:	.type	func

; ..\mcal_src\CanIf_Cbk.c	    94                                     Can_IdType CanId,
; ..\mcal_src\CanIf_Cbk.c	    95                                     uint8 CanDlc,
; ..\mcal_src\CanIf_Cbk.c	    96                                     const uint8 *CanSduPtr)
; ..\mcal_src\CanIf_Cbk.c	    97  {
; ..\mcal_src\CanIf_Cbk.c	    98    UNUSED_PARAMETER (Hrh)
; ..\mcal_src\CanIf_Cbk.c	    99    UNUSED_PARAMETER (CanId)
; ..\mcal_src\CanIf_Cbk.c	   100    UNUSED_PARAMETER (CanDlc)
; ..\mcal_src\CanIf_Cbk.c	   101    UNUSED_PARAMETER (CanSduPtr)
; ..\mcal_src\CanIf_Cbk.c	   102  
; ..\mcal_src\CanIf_Cbk.c	   103    return(TRUE);
; ..\mcal_src\CanIf_Cbk.c	   104  }
	mov	d2,#1
	ret
.L76:
	
__CanLPudReceiveCalloutFunction_function_end:
	.size	CanLPudReceiveCalloutFunction,__CanLPudReceiveCalloutFunction_function_end-CanLPudReceiveCalloutFunction
.L45:
	; End of function
	
	.sdecl	'.text.CanIf_Cbk.CanIf_TrcvModeIndication',code,cluster('CanIf_TrcvModeIndication')
	.sect	'.text.CanIf_Cbk.CanIf_TrcvModeIndication'
	.align	2
	
	.global	CanIf_TrcvModeIndication

; ..\mcal_src\CanIf_Cbk.c	   105  #endif
; ..\mcal_src\CanIf_Cbk.c	   106  /*invoking calback function CanIf_TrcvModeIndication is not supported in \ 
; ..\mcal_src\CanIf_Cbk.c	   107   IFIN CANTRCV(AS321) Req id: CanTrcv158*/
; ..\mcal_src\CanIf_Cbk.c	   108  #if (CANIF_AR_RELEASE_MAJOR_VERSION ==(4U))
; ..\mcal_src\CanIf_Cbk.c	   109  /* Callback function from CanIf module are moved to CanIf_Cbk.c file based on 
; ..\mcal_src\CanIf_Cbk.c	   110  JIRA 0000051018-1132 */
; ..\mcal_src\CanIf_Cbk.c	   111  void CanIf_TrcvModeIndication(uint8 Transceiver,CanTrcv_TrcvModeType OpMode)
; Function CanIf_TrcvModeIndication
.L14:
CanIf_TrcvModeIndication:	.type	func

; ..\mcal_src\CanIf_Cbk.c	   112  {
; ..\mcal_src\CanIf_Cbk.c	   113  //    UNUSED_PARAMETER(Transceiver)
; ..\mcal_src\CanIf_Cbk.c	   114  //    UNUSED_PARAMETER(OpMode)
; ..\mcal_src\CanIf_Cbk.c	   115  //  print_f("\n Callback function CanIf_TrcvModeIndication is called!\n");
; ..\mcal_src\CanIf_Cbk.c	   116  //  print_f(" Transceiver = %d \n", Transceiver);
; ..\mcal_src\CanIf_Cbk.c	   117  //  print_f(" OpMode = %d \n", OpMode);
; ..\mcal_src\CanIf_Cbk.c	   118  }
	ret
.L81:
	
__CanIf_TrcvModeIndication_function_end:
	.size	CanIf_TrcvModeIndication,__CanIf_TrcvModeIndication_function_end-CanIf_TrcvModeIndication
.L50:
	; End of function
	
	.sdecl	'.text.CanIf_Cbk.CanIf_CheckTrcvWakeFlagIndication',code,cluster('CanIf_CheckTrcvWakeFlagIndication')
	.sect	'.text.CanIf_Cbk.CanIf_CheckTrcvWakeFlagIndication'
	.align	2
	
	.global	CanIf_CheckTrcvWakeFlagIndication

; ..\mcal_src\CanIf_Cbk.c	   119  
; ..\mcal_src\CanIf_Cbk.c	   120  void CanIf_CheckTrcvWakeFlagIndication(uint8 Transceiver)
; Function CanIf_CheckTrcvWakeFlagIndication
.L16:
CanIf_CheckTrcvWakeFlagIndication:	.type	func

; ..\mcal_src\CanIf_Cbk.c	   121  {
; ..\mcal_src\CanIf_Cbk.c	   122  //        UNUSED_PARAMETER(Transceiver)
; ..\mcal_src\CanIf_Cbk.c	   123  //    print_f("\n Callback function CanIf_CheckTrcvWakeFlagIndication is called!\n");
; ..\mcal_src\CanIf_Cbk.c	   124  //    print_f(" Transceiver = %d \n", Transceiver);
; ..\mcal_src\CanIf_Cbk.c	   125  }
	ret
.L85:
	
__CanIf_CheckTrcvWakeFlagIndication_function_end:
	.size	CanIf_CheckTrcvWakeFlagIndication,__CanIf_CheckTrcvWakeFlagIndication_function_end-CanIf_CheckTrcvWakeFlagIndication
.L55:
	; End of function
	
	.sdecl	'.data.CanIf_Cbk.RXD_FLAG',data,cluster('RXD_FLAG')
	.sect	'.data.CanIf_Cbk.RXD_FLAG'
	.global	RXD_FLAG
RXD_FLAG:	.type	object
	.size	RXD_FLAG,1
	.space	1
	.sdecl	'.bss.CanIf_Cbk.testPDU',data,cluster('testPDU')
	.sect	'.bss.CanIf_Cbk.testPDU'
	.global	testPDU
testPDU:	.type	object
	.size	testPDU,1
	.space	1
	.calls	'CanIf_RxIndication','Uds_CanIf_RxIndication'
	.calls	'CanIf_TxConfirmation','Uds_CanIf_TxConfirmation'
	.calls	'CanIf_RxIndication','',0
	.calls	'CanIf_TxConfirmation','',0
	.calls	'CanIf_ControllerBusOff','',0
	.calls	'CanIf_ControllerModeIndication','',0
	.calls	'CanLPudReceiveCalloutFunction','',0
	.calls	'CanIf_TrcvModeIndication','',0
	.extern	Uds_CanIf_TxConfirmation
	.extern	Uds_CanIf_RxIndication
	.calls	'CanIf_CheckTrcvWakeFlagIndication','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L18:
	.word	1811
	.half	3
	.word	.L19
	.byte	4
.L17:
	.byte	1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L20
.L61:
	.byte	2
	.byte	'unsigned short int',0,2,7
.L63:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L65:
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.word	221
.L67:
	.byte	4
	.word	238
.L83:
	.byte	5,1,86,9,1,6
	.byte	'CANTRCV_TRCVMODE_NORMAL',0,0,6
	.byte	'CANTRCV_TRCVMODE_SLEEP',0,1,6
	.byte	'CANTRCV_TRCVMODE_STANDBY',0,2,0,7
	.byte	'Uds_CanIf_TxConfirmation',0,2,70,13,1,1,1,1,8
	.byte	'CanTxPduId',0,2,70,48
	.word	221
	.byte	0,7
	.byte	'Uds_CanIf_RxIndication',0,2,72,13,1,1,1,1,8
	.byte	'Hrh',0,2,72,42
	.word	221
	.byte	8
	.byte	'CanId',0,2,72,57
	.word	200
	.byte	8
	.byte	'CanDlc',0,2,72,69
	.word	221
	.byte	4
	.word	221
	.byte	8
	.byte	'CanSduPtr',0,2,72,83
	.word	457
	.byte	0,9
	.byte	'void',0,4
	.word	481
	.byte	10
	.byte	'__prof_adm',0,3,1,1
	.word	487
	.byte	11,1,4
	.word	511
	.byte	10
	.byte	'__codeptr',0,3,1,1
	.word	513
	.byte	10
	.byte	'uint8',0,4,90,29
	.word	221
	.byte	10
	.byte	'uint16',0,4,92,29
	.word	178
	.byte	10
	.byte	'uint32',0,4,94,29
	.word	200
	.byte	10
	.byte	'boolean',0,4,105,29
	.word	221
	.byte	10
	.byte	'PduIdType',0,5,72,22
	.word	221
	.byte	10
	.byte	'PduLengthType',0,5,76,22
	.word	178
	.byte	10
	.byte	'Can_IdType',0,1,46,16
	.word	200
	.byte	10
	.byte	'Can_HwHandleType',0,1,61,16
	.word	178
	.byte	10
	.byte	'CanTrcv_TrcvModeType',0,1,91,3
	.word	248
	.byte	10
	.byte	'CanIf_ControllerModeType',0,6,58,15
	.word	221
	.byte	12
	.byte	'Can_TxHwObjectConfigType',0,7,218,3,16,2,13
	.byte	'MsgObjId',0,1
	.word	221
	.byte	2,35,0,13
	.byte	'HwControllerId',0,1
	.word	221
	.byte	2,35,1,0,10
	.byte	'Can_TxHwObjectConfigType',0,7,236,3,3
	.word	742
	.byte	12
	.byte	'Can_RxHwObjectConfigType',0,7,241,3,16,12,13
	.byte	'MaskRef',0,4
	.word	200
	.byte	2,35,0,13
	.byte	'MsgId',0,4
	.word	200
	.byte	2,35,4,13
	.byte	'MsgObjId',0,1
	.word	221
	.byte	2,35,8,13
	.byte	'HwControllerId',0,1
	.word	221
	.byte	2,35,9,0,10
	.byte	'Can_RxHwObjectConfigType',0,7,131,4,3
	.word	850
	.byte	12
	.byte	'Can_ControllerMOMapConfigType',0,7,165,4,16,4,14,4
	.word	221
	.byte	15,3,0,13
	.byte	'ControllerMOMap',0,4
	.word	1026
	.byte	2,35,0,0,10
	.byte	'Can_ControllerMOMapConfigType',0,7,168,4,3
	.word	990
	.byte	12
	.byte	'Can_NPCRValueType',0,7,172,4,16,2,13
	.byte	'Can_NPCRValue',0,2
	.word	178
	.byte	2,35,0,0,10
	.byte	'Can_NPCRValueType',0,7,175,4,3
	.word	1100
	.byte	12
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,7,178,4,16,6,13
	.byte	'CanControllerBaudrate',0,4
	.word	200
	.byte	2,35,0,13
	.byte	'CanControllerBaudrateCfg',0,2
	.word	178
	.byte	2,35,4,0,10
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,7,182,4,3
	.word	1175
	.byte	12
	.byte	'Can_BaudrateConfigPtrType',0,7,185,4,16,4,3
	.word	1175
	.byte	4
	.word	1372
	.byte	13
	.byte	'Can_kBaudrateConfigPtr',0,4
	.word	1377
	.byte	2,35,0,0,10
	.byte	'Can_BaudrateConfigPtrType',0,7,188,4,3
	.word	1340
	.byte	12
	.byte	'Can_FDConfigParamType',0,7,193,4,16,6,13
	.byte	'CanControllerFDBaudrate',0,2
	.word	178
	.byte	2,35,0,13
	.byte	'CanControllerTxDelayComp',0,2
	.word	178
	.byte	2,35,2,13
	.byte	'CanControllerTxBRS',0,1
	.word	221
	.byte	2,35,4,0,10
	.byte	'Can_FDConfigParamType',0,7,198,4,3
	.word	1450
	.byte	12
	.byte	'Can_FDConfigParamPtrType',0,7,200,4,16,4,3
	.word	1450
	.byte	4
	.word	1636
	.byte	13
	.byte	'Can_kFDConfigParamPtr',0,4
	.word	1641
	.byte	2,35,0,0,10
	.byte	'Can_FDConfigParamPtrType',0,7,203,4,3
	.word	1605
	.byte	12
	.byte	'Can_EventHandlingType',0,7,210,4,16,4,13
	.byte	'CanEventType',0,4
	.word	1026
	.byte	2,35,0,0,10
	.byte	'Can_EventHandlingType',0,7,213,4,3
	.word	1712
	.byte	10
	.byte	'_iob_flag_t',0,8,75,25
	.word	178
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,38,0,73,19,0,0,4,15,0,73,19
	.byte	0,0,5,4,1,58,15,59,15,57,15,11,15,0,0,6,40,0,3,8,28,13,0,0,7,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63
	.byte	12,60,12,0,0,8,5,0,3,8,58,15,59,15,57,15,73,19,0,0,9,59,0,3,8,0,0,10,22,0,3,8,58,15,59,15,57,15,73,19
	.byte	0,0,11,21,0,54,15,0,0,12,19,1,3,8,58,15,59,15,57,15,11,15,0,0,13,13,0,3,8,11,15,73,19,56,9,0,0,14,1,1
	.byte	11,15,73,19,0,0,15,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L20:
	.word	.L89-.L88
.L88:
	.half	3
	.word	.L91-.L90
.L90:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0,0
	.byte	'..\\mcal_src\\Can_GeneralTypes.h',0,0,0,0
	.byte	'Uds_CanIf.h',0,1,0,0
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\ComStack_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\CanIf_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Can_17_MCanP.h',0,0,0,0
	.byte	'stdio.h',0,2,0,0,0
.L91:
.L89:
	.sdecl	'.debug_info',debug,cluster('CanIf_RxIndication')
	.sect	'.debug_info'
.L21:
	.word	307
	.half	3
	.word	.L22
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L24,.L23
	.byte	2
	.word	.L17
	.byte	3
	.byte	'CanIf_RxIndication',0,1,51,6,1,1,1
	.word	.L4,.L60,.L3
	.byte	4
	.byte	'Hrh',0,1,51,42
	.word	.L61,.L62
	.byte	4
	.byte	'CanId',0,1,52,36
	.word	.L63,.L64
	.byte	4
	.byte	'CanDlc',0,1,53,31
	.word	.L65,.L66
	.byte	4
	.byte	'CanSduPtr',0,1,54,38
	.word	.L67,.L68
	.byte	5
	.word	.L4,.L60
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CanIf_RxIndication')
	.sect	'.debug_abbrev'
.L22:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CanIf_RxIndication')
	.sect	'.debug_line'
.L23:
	.word	.L93-.L92
.L92:
	.half	3
	.word	.L95-.L94
.L94:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0,0,0,0,0
.L95:
	.byte	5,25,7,0,5,2
	.word	.L4
	.byte	3,57,1,5,2,3,127,1,5,10,9
	.half	.L96-.L4
	.byte	1,5,14,9
	.half	.L97-.L96
	.byte	3,127,1,5,1,7,9
	.half	.L25-.L97
	.byte	3,3,0,1,1
.L93:
	.sdecl	'.debug_ranges',debug,cluster('CanIf_RxIndication')
	.sect	'.debug_ranges'
.L24:
	.word	-1,.L4,0,.L25-.L4,0,0
	.sdecl	'.debug_info',debug,cluster('CanIf_TxConfirmation')
	.sect	'.debug_info'
.L26:
	.word	252
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L29,.L28
	.byte	2
	.word	.L17
	.byte	3
	.byte	'CanIf_TxConfirmation',0,1,66,6,1,1,1
	.word	.L6,.L69,.L5
	.byte	4
	.byte	'PduId',0,1,66,38
	.word	.L65,.L70
	.byte	5
	.word	.L6,.L69
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CanIf_TxConfirmation')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CanIf_TxConfirmation')
	.sect	'.debug_line'
.L28:
	.word	.L99-.L98
.L98:
	.half	3
	.word	.L101-.L100
.L100:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0,0,0,0,0
.L101:
	.byte	5,27,7,0,5,2
	.word	.L6
	.byte	3,195,0,1,5,1,7,9
	.half	.L30-.L6
	.byte	3,1,0,1,1
.L99:
	.sdecl	'.debug_ranges',debug,cluster('CanIf_TxConfirmation')
	.sect	'.debug_ranges'
.L29:
	.word	-1,.L6,0,.L30-.L6,0,0
	.sdecl	'.debug_info',debug,cluster('CanIf_ControllerBusOff')
	.sect	'.debug_info'
.L31:
	.word	261
	.half	3
	.word	.L32
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L34,.L33
	.byte	2
	.word	.L17
	.byte	3
	.byte	'CanIf_ControllerBusOff',0,1,74,6,1,1,1
	.word	.L8,.L71,.L7
	.byte	4
	.byte	'ControllerId',0,1,74,35
	.word	.L65,.L72
	.byte	5
	.word	.L8,.L71
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CanIf_ControllerBusOff')
	.sect	'.debug_abbrev'
.L32:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CanIf_ControllerBusOff')
	.sect	'.debug_line'
.L33:
	.word	.L103-.L102
.L102:
	.half	3
	.word	.L105-.L104
.L104:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0,0,0,0,0
.L105:
	.byte	5,1,7,0,5,2
	.word	.L8
	.byte	3,204,0,1,7,9
	.half	.L35-.L8
	.byte	0,1,1
.L103:
	.sdecl	'.debug_ranges',debug,cluster('CanIf_ControllerBusOff')
	.sect	'.debug_ranges'
.L34:
	.word	-1,.L8,0,.L35-.L8,0,0
	.sdecl	'.debug_info',debug,cluster('CanIf_ControllerModeIndication')
	.sect	'.debug_info'
.L36:
	.word	296
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L39,.L38
	.byte	2
	.word	.L17
	.byte	3
	.byte	'CanIf_ControllerModeIndication',0,1,82,6,1,1,1
	.word	.L10,.L73,.L9
	.byte	4
	.byte	'ControllerId',0,1,82,44
	.word	.L65,.L74
	.byte	4
	.byte	'ControllerMode',0,1,83,63
	.word	.L65,.L75
	.byte	5
	.word	.L10,.L73
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CanIf_ControllerModeIndication')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CanIf_ControllerModeIndication')
	.sect	'.debug_line'
.L38:
	.word	.L107-.L106
.L106:
	.half	3
	.word	.L109-.L108
.L108:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0,0,0,0,0
.L109:
	.byte	5,1,7,0,5,2
	.word	.L10
	.byte	3,215,0,1,7,9
	.half	.L40-.L10
	.byte	0,1,1
.L107:
	.sdecl	'.debug_ranges',debug,cluster('CanIf_ControllerModeIndication')
	.sect	'.debug_ranges'
.L39:
	.word	-1,.L10,0,.L40-.L10,0,0
	.sdecl	'.debug_info',debug,cluster('CanLPudReceiveCalloutFunction')
	.sect	'.debug_info'
.L41:
	.word	322
	.half	3
	.word	.L42
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L44,.L43
	.byte	2
	.word	.L17
	.byte	3
	.byte	'CanLPudReceiveCalloutFunction',0,1,93,9
	.word	.L65
	.byte	1,1,1
	.word	.L12,.L76,.L11
	.byte	4
	.byte	'Hrh',0,1,93,56
	.word	.L61,.L77
	.byte	4
	.byte	'CanId',0,1,94,47
	.word	.L63,.L78
	.byte	4
	.byte	'CanDlc',0,1,95,42
	.word	.L65,.L79
	.byte	4
	.byte	'CanSduPtr',0,1,96,49
	.word	.L67,.L80
	.byte	5
	.word	.L12,.L76
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CanLPudReceiveCalloutFunction')
	.sect	'.debug_abbrev'
.L42:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CanLPudReceiveCalloutFunction')
	.sect	'.debug_line'
.L43:
	.word	.L111-.L110
.L110:
	.half	3
	.word	.L113-.L112
.L112:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0,0,0,0,0
.L113:
	.byte	5,10,7,0,5,2
	.word	.L12
	.byte	3,230,0,1,5,1,3,1,1,7,9
	.half	.L45-.L12
	.byte	0,1,1
.L111:
	.sdecl	'.debug_ranges',debug,cluster('CanLPudReceiveCalloutFunction')
	.sect	'.debug_ranges'
.L44:
	.word	-1,.L12,0,.L45-.L12,0,0
	.sdecl	'.debug_info',debug,cluster('CanIf_TrcvModeIndication')
	.sect	'.debug_info'
.L46:
	.word	281
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L49,.L48
	.byte	2
	.word	.L17
	.byte	3
	.byte	'CanIf_TrcvModeIndication',0,1,111,6,1,1,1
	.word	.L14,.L81,.L13
	.byte	4
	.byte	'Transceiver',0,1,111,37
	.word	.L65,.L82
	.byte	4
	.byte	'OpMode',0,1,111,70
	.word	.L83,.L84
	.byte	5
	.word	.L14,.L81
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CanIf_TrcvModeIndication')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CanIf_TrcvModeIndication')
	.sect	'.debug_line'
.L48:
	.word	.L115-.L114
.L114:
	.half	3
	.word	.L117-.L116
.L116:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0,0,0,0,0
.L117:
	.byte	5,1,7,0,5,2
	.word	.L14
	.byte	3,245,0,1,7,9
	.half	.L50-.L14
	.byte	0,1,1
.L115:
	.sdecl	'.debug_ranges',debug,cluster('CanIf_TrcvModeIndication')
	.sect	'.debug_ranges'
.L49:
	.word	-1,.L14,0,.L50-.L14,0,0
	.sdecl	'.debug_info',debug,cluster('CanIf_CheckTrcvWakeFlagIndication')
	.sect	'.debug_info'
.L51:
	.word	271
	.half	3
	.word	.L52
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L54,.L53
	.byte	2
	.word	.L17
	.byte	3
	.byte	'CanIf_CheckTrcvWakeFlagIndication',0,1,120,6,1,1,1
	.word	.L16,.L85,.L15
	.byte	4
	.byte	'Transceiver',0,1,120,46
	.word	.L65,.L86
	.byte	5
	.word	.L16,.L85
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CanIf_CheckTrcvWakeFlagIndication')
	.sect	'.debug_abbrev'
.L52:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CanIf_CheckTrcvWakeFlagIndication')
	.sect	'.debug_line'
.L53:
	.word	.L119-.L118
.L118:
	.half	3
	.word	.L121-.L120
.L120:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0,0,0,0,0
.L121:
	.byte	5,1,7,0,5,2
	.word	.L16
	.byte	3,252,0,1,7,9
	.half	.L55-.L16
	.byte	0,1,1
.L119:
	.sdecl	'.debug_ranges',debug,cluster('CanIf_CheckTrcvWakeFlagIndication')
	.sect	'.debug_ranges'
.L54:
	.word	-1,.L16,0,.L55-.L16,0,0
	.sdecl	'.debug_info',debug,cluster('RXD_FLAG')
	.sect	'.debug_info'
.L56:
	.word	200
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L17
	.byte	3
	.byte	'RXD_FLAG',0,3,44,7
	.word	.L65
	.byte	1,5,3
	.word	RXD_FLAG
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('RXD_FLAG')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('testPDU')
	.sect	'.debug_info'
.L58:
	.word	199
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'..\\mcal_src\\CanIf_Cbk.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L17
	.byte	3
	.byte	'testPDU',0,3,64,11
	.word	.L65
	.byte	1,5,3
	.word	testPDU
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('testPDU')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('CanIf_CheckTrcvWakeFlagIndication')
	.sect	'.debug_loc'
.L15:
	.word	-1,.L16,0,.L85-.L16
	.half	2
	.byte	138,0
	.word	0,0
.L86:
	.word	-1,.L16,0,.L85-.L16
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CanIf_ControllerBusOff')
	.sect	'.debug_loc'
.L7:
	.word	-1,.L8,0,.L71-.L8
	.half	2
	.byte	138,0
	.word	0,0
.L72:
	.word	-1,.L8,0,.L71-.L8
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CanIf_ControllerModeIndication')
	.sect	'.debug_loc'
.L9:
	.word	-1,.L10,0,.L73-.L10
	.half	2
	.byte	138,0
	.word	0,0
.L74:
	.word	-1,.L10,0,.L73-.L10
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L75:
	.word	-1,.L10,0,.L73-.L10
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CanIf_RxIndication')
	.sect	'.debug_loc'
.L66:
	.word	-1,.L4,0,.L60-.L4
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L64:
	.word	-1,.L4,0,.L60-.L4
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L3:
	.word	-1,.L4,0,.L60-.L4
	.half	2
	.byte	138,0
	.word	0,0
.L68:
	.word	-1,.L4,0,.L60-.L4
	.half	1
	.byte	100
	.word	0,0
.L62:
	.word	-1,.L4,0,.L87-.L4
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CanIf_TrcvModeIndication')
	.sect	'.debug_loc'
.L13:
	.word	-1,.L14,0,.L81-.L14
	.half	2
	.byte	138,0
	.word	0,0
.L84:
	.word	-1,.L14,0,.L81-.L14
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L82:
	.word	-1,.L14,0,.L81-.L14
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CanIf_TxConfirmation')
	.sect	'.debug_loc'
.L5:
	.word	-1,.L6,0,.L69-.L6
	.half	2
	.byte	138,0
	.word	0,0
.L70:
	.word	-1,.L6,0,.L69-.L6
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CanLPudReceiveCalloutFunction')
	.sect	'.debug_loc'
.L79:
	.word	-1,.L12,0,.L76-.L12
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L78:
	.word	-1,.L12,0,.L76-.L12
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L11:
	.word	-1,.L12,0,.L76-.L12
	.half	2
	.byte	138,0
	.word	0,0
.L80:
	.word	-1,.L12,0,.L76-.L12
	.half	1
	.byte	100
	.word	0,0
.L77:
	.word	-1,.L12,0,.L76-.L12
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L122:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('CanIf_RxIndication')
	.sect	'.debug_frame'
	.word	12
	.word	.L122,.L4,.L60-.L4
	.sdecl	'.debug_frame',debug,cluster('CanIf_TxConfirmation')
	.sect	'.debug_frame'
	.word	12
	.word	.L122,.L6,.L69-.L6
	.sdecl	'.debug_frame',debug,cluster('CanIf_ControllerBusOff')
	.sect	'.debug_frame'
	.word	24
	.word	.L122,.L8,.L71-.L8
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('CanIf_ControllerModeIndication')
	.sect	'.debug_frame'
	.word	24
	.word	.L122,.L10,.L73-.L10
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('CanLPudReceiveCalloutFunction')
	.sect	'.debug_frame'
	.word	24
	.word	.L122,.L12,.L76-.L12
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('CanIf_TrcvModeIndication')
	.sect	'.debug_frame'
	.word	24
	.word	.L122,.L14,.L81-.L14
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('CanIf_CheckTrcvWakeFlagIndication')
	.sect	'.debug_frame'
	.word	24
	.word	.L122,.L16,.L85-.L16
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\CanIf_Cbk.c	   126  
; ..\mcal_src\CanIf_Cbk.c	   127  #endif

	; Module end
