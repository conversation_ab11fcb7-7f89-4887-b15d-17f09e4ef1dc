	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc30600a -c99 --dep-file=eeprom\\MemIf\\.MemIf.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\MemIf\\MemIf.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\MemIf\\MemIf.src ..\\eeprom\\MemIf\\MemIf.c"
	.compiler_name		"ctc"
	.name	"MemIf"

	
$TC16X
	
	.sdecl	'.text.MemIf.MemIf_IsBitSet',code,cluster('MemIf_IsBitSet')
	.sect	'.text.MemIf.MemIf_IsBitSet'
	.align	2
	

; ..\eeprom\MemIf\MemIf.c	     1  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	     2   *  COPYRIGHT
; ..\eeprom\MemIf\MemIf.c	     3   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\MemIf\MemIf.c	     4   *  \verbatim
; ..\eeprom\MemIf\MemIf.c	     5   *  Copyright (c) 2019 by Vector Informatik GmbH.                                              All rights reserved.
; ..\eeprom\MemIf\MemIf.c	     6   *
; ..\eeprom\MemIf\MemIf.c	     7   *                This software is copyright protected and proprietary to Vector Informatik GmbH.
; ..\eeprom\MemIf\MemIf.c	     8   *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
; ..\eeprom\MemIf\MemIf.c	     9   *                All other rights remain with Vector Informatik GmbH.
; ..\eeprom\MemIf\MemIf.c	    10   *  \endverbatim
; ..\eeprom\MemIf\MemIf.c	    11   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\MemIf\MemIf.c	    12   *  FILE DESCRIPTION
; ..\eeprom\MemIf\MemIf.c	    13   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\MemIf\MemIf.c	    14   *         \file  MemIf.c
; ..\eeprom\MemIf\MemIf.c	    15   *        \brief  Memory Interface source file
; ..\eeprom\MemIf\MemIf.c	    16   *
; ..\eeprom\MemIf\MemIf.c	    17   *      \details  The Memory Abstraction Interface provides uniform access to services of underlying
; ..\eeprom\MemIf\MemIf.c	    18   *                Memory Hardware abstraction (MemHwA) modules, i.e. EEPROM Abstraction (EA) and
; ..\eeprom\MemIf\MemIf.c	    19   *                Flash EEPROM Emulation (FEE). The appropriate MemHwA module is selected by a device index.
; ..\eeprom\MemIf\MemIf.c	    20   *
; ..\eeprom\MemIf\MemIf.c	    21   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	    22  
; ..\eeprom\MemIf\MemIf.c	    23  
; ..\eeprom\MemIf\MemIf.c	    24  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	    25   *  INCLUDES
; ..\eeprom\MemIf\MemIf.c	    26   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	    27  #include "Std_Types.h"
; ..\eeprom\MemIf\MemIf.c	    28  #include "MemIf.h"
; ..\eeprom\MemIf\MemIf.c	    29  
; ..\eeprom\MemIf\MemIf.c	    30  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	    31   *  VERSION CHECK
; ..\eeprom\MemIf\MemIf.c	    32   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	    33  
; ..\eeprom\MemIf\MemIf.c	    34  #if ((MEMIF_SW_MAJOR_VERSION != (3U)) \ 
; ..\eeprom\MemIf\MemIf.c	    35    || (MEMIF_SW_MINOR_VERSION != (4U)) )
; ..\eeprom\MemIf\MemIf.c	    36  # error "Version numbers of MemIf.c and MemIf.h are inconsistent!"
; ..\eeprom\MemIf\MemIf.c	    37  #endif
; ..\eeprom\MemIf\MemIf.c	    38  
; ..\eeprom\MemIf\MemIf.c	    39  #if ((MEMIF_CFG_MAJOR_VERSION != (5U)) \ 
; ..\eeprom\MemIf\MemIf.c	    40    || (MEMIF_CFG_MINOR_VERSION != (2U)) )
; ..\eeprom\MemIf\MemIf.c	    41  # error "Version numbers of MemIf.c and MemIf_Cfg.h are inconsistent!"
; ..\eeprom\MemIf\MemIf.c	    42  #endif
; ..\eeprom\MemIf\MemIf.c	    43  
; ..\eeprom\MemIf\MemIf.c	    44  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	    45   *  LOCAL DATA TYPES AND STRUCTURES
; ..\eeprom\MemIf\MemIf.c	    46   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	    47  
; ..\eeprom\MemIf\MemIf.c	    48  #if !defined (MEMIF_LOCAL) /* COV_MEMIF_COMPATIBILITY */
; ..\eeprom\MemIf\MemIf.c	    49  # define MEMIF_LOCAL static
; ..\eeprom\MemIf\MemIf.c	    50  #endif
; ..\eeprom\MemIf\MemIf.c	    51  
; ..\eeprom\MemIf\MemIf.c	    52  #if !defined (MEMIF_LOCAL_INLINE) /* COV_MEMIF_COMPATIBILITY */
; ..\eeprom\MemIf\MemIf.c	    53  # define MEMIF_LOCAL_INLINE LOCAL_INLINE
; ..\eeprom\MemIf\MemIf.c	    54  #endif
; ..\eeprom\MemIf\MemIf.c	    55  
; ..\eeprom\MemIf\MemIf.c	    56  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	    57   *  LOCAL FUNCTION PROTOTYPES
; ..\eeprom\MemIf\MemIf.c	    58   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	    59  #define MEMIF_START_SEC_CODE
; ..\eeprom\MemIf\MemIf.c	    60    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\MemIf\MemIf.c	    61  
; ..\eeprom\MemIf\MemIf.c	    62  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	    63  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	    64   *  MemIf_DetChkDeviceIndex()
; ..\eeprom\MemIf\MemIf.c	    65   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	    66  /*!
; ..\eeprom\MemIf\MemIf.c	    67   * \brief      Checks if device index passed to MemIf is in range
; ..\eeprom\MemIf\MemIf.c	    68   * \details    Checks if device index passed to MemIf is in range
; ..\eeprom\MemIf\MemIf.c	    69   * \param[in]  DeviceIndex
; ..\eeprom\MemIf\MemIf.c	    70   * \return     E_OK: DeviceIndex is valid
; ..\eeprom\MemIf\MemIf.c	    71   * \return     E_NOT_OK: DeviceIndex is out of range
; ..\eeprom\MemIf\MemIf.c	    72   * \pre        Pre-compile switch MEMIF_DEV_ERROR_DETECT is enabled
; ..\eeprom\MemIf\MemIf.c	    73   * \context    TASK
; ..\eeprom\MemIf\MemIf.c	    74   * \reentrant  FALSE
; ..\eeprom\MemIf\MemIf.c	    75   * \synchronous TRUE
; ..\eeprom\MemIf\MemIf.c	    76   */
; ..\eeprom\MemIf\MemIf.c	    77  MEMIF_LOCAL_INLINE FUNC(Std_ReturnType, MEMIF_PRIVATE_CODE) MemIf_DetChkDeviceIndex(uint8 DeviceIndex);
; ..\eeprom\MemIf\MemIf.c	    78  #endif
; ..\eeprom\MemIf\MemIf.c	    79  
; ..\eeprom\MemIf\MemIf.c	    80  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	    81   *  MemIf_IsBitSet()
; ..\eeprom\MemIf\MemIf.c	    82   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	    83  /*!
; ..\eeprom\MemIf\MemIf.c	    84   * \brief      Checks if Status is set in Mask
; ..\eeprom\MemIf\MemIf.c	    85   * \details    Checks if Status is set in Mask
; ..\eeprom\MemIf\MemIf.c	    86   * \param[in]  Mask
; ..\eeprom\MemIf\MemIf.c	    87   * \param[in]  Status
; ..\eeprom\MemIf\MemIf.c	    88   * \return     E_OK: Status bit is set
; ..\eeprom\MemIf\MemIf.c	    89   * \return     E_NOT_OK: Status bit is not set
; ..\eeprom\MemIf\MemIf.c	    90   * \pre        -
; ..\eeprom\MemIf\MemIf.c	    91   * \context    TASK
; ..\eeprom\MemIf\MemIf.c	    92   * \reentrant  FALSE
; ..\eeprom\MemIf\MemIf.c	    93   * \synchronous TRUE
; ..\eeprom\MemIf\MemIf.c	    94   */
; ..\eeprom\MemIf\MemIf.c	    95  MEMIF_LOCAL FUNC(Std_ReturnType, MEMIF_PRIVATE_CODE) MemIf_IsBitSet(uint8 Mask, MemIf_StatusType Status);
; ..\eeprom\MemIf\MemIf.c	    96  
; ..\eeprom\MemIf\MemIf.c	    97  
; ..\eeprom\MemIf\MemIf.c	    98  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	    99   *  LOCAL FUNCTIONS
; ..\eeprom\MemIf\MemIf.c	   100   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   101  
; ..\eeprom\MemIf\MemIf.c	   102  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   103  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   104   *  MemIf_DetChkDeviceIndex()
; ..\eeprom\MemIf\MemIf.c	   105   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   106  /*!
; ..\eeprom\MemIf\MemIf.c	   107   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   108   *
; ..\eeprom\MemIf\MemIf.c	   109   *
; ..\eeprom\MemIf\MemIf.c	   110   */
; ..\eeprom\MemIf\MemIf.c	   111  MEMIF_LOCAL_INLINE FUNC(Std_ReturnType, MEMIF_PRIVATE_CODE) MemIf_DetChkDeviceIndex(uint8 DeviceIndex)
; ..\eeprom\MemIf\MemIf.c	   112  {
; ..\eeprom\MemIf\MemIf.c	   113    return (Std_ReturnType) ((DeviceIndex >= MemIf_NumberOfDevices) ? E_NOT_OK : E_OK);
; ..\eeprom\MemIf\MemIf.c	   114  }
; ..\eeprom\MemIf\MemIf.c	   115  #endif
; ..\eeprom\MemIf\MemIf.c	   116  
; ..\eeprom\MemIf\MemIf.c	   117  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   118   *  MemIf_IsBitSet()
; ..\eeprom\MemIf\MemIf.c	   119   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   120  /*!
; ..\eeprom\MemIf\MemIf.c	   121   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   122   *
; ..\eeprom\MemIf\MemIf.c	   123   *
; ..\eeprom\MemIf\MemIf.c	   124   */
; ..\eeprom\MemIf\MemIf.c	   125  MEMIF_LOCAL FUNC(Std_ReturnType, MEMIF_PRIVATE_CODE) MemIf_IsBitSet(uint8 Mask, MemIf_StatusType Status)
; Function MemIf_IsBitSet
.L22:
MemIf_IsBitSet:	.type	func

; ..\eeprom\MemIf\MemIf.c	   126  {
; ..\eeprom\MemIf\MemIf.c	   127    return ((Mask & (uint8)(1u << (uint8)Status)) > 0u) ? E_OK : E_NOT_OK;
	mov	d15,#1
.L236:
	sh	d15,d15,d5
.L237:
	and	d4,d15
.L145:

; ..\eeprom\MemIf\MemIf.c	   128  }
	eq	d2,d4,#0
	ret
.L142:
	
__MemIf_IsBitSet_function_end:
	.size	MemIf_IsBitSet,__MemIf_IsBitSet_function_end-MemIf_IsBitSet
.L91:
	; End of function
	
	.sdecl	'.text.MemIf.MemIf_Read',code,cluster('MemIf_Read')
	.sect	'.text.MemIf.MemIf_Read'
	.align	2
	
	.global	MemIf_Read

; ..\eeprom\MemIf\MemIf.c	   129  
; ..\eeprom\MemIf\MemIf.c	   130  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   131   *  GLOBAL FUNCTIONS
; ..\eeprom\MemIf\MemIf.c	   132   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   133  
; ..\eeprom\MemIf\MemIf.c	   134  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   135   *  MemIf_Read()
; ..\eeprom\MemIf\MemIf.c	   136   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   137  /*!
; ..\eeprom\MemIf\MemIf.c	   138   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   139   *
; ..\eeprom\MemIf\MemIf.c	   140   *
; ..\eeprom\MemIf\MemIf.c	   141   *
; ..\eeprom\MemIf\MemIf.c	   142   *
; ..\eeprom\MemIf\MemIf.c	   143   */
; ..\eeprom\MemIf\MemIf.c	   144  FUNC(Std_ReturnType, MEMIF_CODE) MemIf_Read(uint8 DeviceIndex, uint16 BlockNumber, uint16 BlockOffset, MemIf_DataPtr_pu8 DataBufferPtr, uint16 Length)
; Function MemIf_Read
.L24:
MemIf_Read:	.type	func

; ..\eeprom\MemIf\MemIf.c	   145  {
; ..\eeprom\MemIf\MemIf.c	   146    /* ----- Local Variables ------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   147    Std_ReturnType retVal;
; ..\eeprom\MemIf\MemIf.c	   148    uint8 errorId;
; ..\eeprom\MemIf\MemIf.c	   149  
; ..\eeprom\MemIf\MemIf.c	   150    /* ----- Development Error Detection ------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   151  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   152    if (MemIf_DetChkDeviceIndex(DeviceIndex) == E_NOT_OK)
; ..\eeprom\MemIf\MemIf.c	   153    {
; ..\eeprom\MemIf\MemIf.c	   154      errorId = MEMIF_E_PARAM_DEVICE;
; ..\eeprom\MemIf\MemIf.c	   155      retVal = E_NOT_OK;
; ..\eeprom\MemIf\MemIf.c	   156    }
; ..\eeprom\MemIf\MemIf.c	   157    else
; ..\eeprom\MemIf\MemIf.c	   158  #endif
; ..\eeprom\MemIf\MemIf.c	   159    {
; ..\eeprom\MemIf\MemIf.c	   160    /* ----- Implementation -------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   161      errorId = MEMIF_E_NO_ERROR;
; ..\eeprom\MemIf\MemIf.c	   162      retVal = MemIf_MemHwaApis[DeviceIndex].Read(BlockNumber, BlockOffset, DataBufferPtr, Length); /* SBSW_MEMIF_02 */
	fcall	.cocofun_1
.L148:
	ld.a	a15,[a15]
.L172:
	mov	d4,d5
.L151:
	mov	d5,d6
.L149:
	mov	d6,d7
.L150:
	ji	a15
.L103:
	
__MemIf_Read_function_end:
	.size	MemIf_Read,__MemIf_Read_function_end-MemIf_Read
.L51:
	; End of function
	
	.sdecl	'.text.MemIf..cocofun_1',code,cluster('.cocofun_1')
	.sect	'.text.MemIf..cocofun_1'
	.align	2
; Function .cocofun_1
.L26:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	sha	d4,#5
	fcall	.cocofun_2
.L146:
	addsc.a	a15,a15,d4,#0
.L242:
	fret
.L96:
	; End of function
	.sdecl	'.text.MemIf..cocofun_2',code,cluster('.cocofun_2')
	.sect	'.text.MemIf..cocofun_2'
	.align	2
; Function .cocofun_2
.L28:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:1
	movh.a	a15,#@his(MemIf_MemHwaApis)
.L147:
	lea	a15,[a15]@los(MemIf_MemHwaApis)
.L247:
	fret
.L101:
	; End of function
	.sdecl	'.text.MemIf.MemIf_Write',code,cluster('MemIf_Write')
	.sect	'.text.MemIf.MemIf_Write'
	.align	2
	
	.global	MemIf_Write

; ..\eeprom\MemIf\MemIf.c	   163    }
; ..\eeprom\MemIf\MemIf.c	   164  
; ..\eeprom\MemIf\MemIf.c	   165    /* ----- Development Error Report ---------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   166  #if (MEMIF_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   167    if (errorId != MEMIF_E_NO_ERROR)
; ..\eeprom\MemIf\MemIf.c	   168    {
; ..\eeprom\MemIf\MemIf.c	   169      (void)Det_ReportError(MEMIF_MODULE_ID, MEMIF_INSTANCE_ID, MEMIF_SID_READ, errorId);
; ..\eeprom\MemIf\MemIf.c	   170    }
; ..\eeprom\MemIf\MemIf.c	   171  #else
; ..\eeprom\MemIf\MemIf.c	   172    MEMIF_DUMMY_STATEMENT(errorId); /* SBSW_MEMIF_03 */
; ..\eeprom\MemIf\MemIf.c	   173  #endif
; ..\eeprom\MemIf\MemIf.c	   174  
; ..\eeprom\MemIf\MemIf.c	   175    return retVal;
; ..\eeprom\MemIf\MemIf.c	   176  }
; ..\eeprom\MemIf\MemIf.c	   177  
; ..\eeprom\MemIf\MemIf.c	   178  
; ..\eeprom\MemIf\MemIf.c	   179  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   180   *  MemIf_Write()
; ..\eeprom\MemIf\MemIf.c	   181   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   182  /*!
; ..\eeprom\MemIf\MemIf.c	   183   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   184   *
; ..\eeprom\MemIf\MemIf.c	   185   *
; ..\eeprom\MemIf\MemIf.c	   186   *
; ..\eeprom\MemIf\MemIf.c	   187   *
; ..\eeprom\MemIf\MemIf.c	   188   */
; ..\eeprom\MemIf\MemIf.c	   189  FUNC(Std_ReturnType, MEMIF_CODE) MemIf_Write(uint8 DeviceIndex, uint16 BlockNumber, MemIf_DataPtr_pu8 DataBufferPtr)
; Function MemIf_Write
.L30:
MemIf_Write:	.type	func

; ..\eeprom\MemIf\MemIf.c	   190  {
; ..\eeprom\MemIf\MemIf.c	   191    /* ----- Local Variables ------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   192    Std_ReturnType retVal;
; ..\eeprom\MemIf\MemIf.c	   193    uint8 errorId;
; ..\eeprom\MemIf\MemIf.c	   194  
; ..\eeprom\MemIf\MemIf.c	   195    /* ----- Development Error Detection ------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   196  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   197    if (MemIf_DetChkDeviceIndex(DeviceIndex) == E_NOT_OK)
; ..\eeprom\MemIf\MemIf.c	   198    {
; ..\eeprom\MemIf\MemIf.c	   199      retVal = E_NOT_OK;
; ..\eeprom\MemIf\MemIf.c	   200      errorId = MEMIF_E_PARAM_DEVICE;
; ..\eeprom\MemIf\MemIf.c	   201    }
; ..\eeprom\MemIf\MemIf.c	   202    else
; ..\eeprom\MemIf\MemIf.c	   203  #endif
; ..\eeprom\MemIf\MemIf.c	   204    {
; ..\eeprom\MemIf\MemIf.c	   205    /* ----- Implementation -------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   206      errorId = MEMIF_E_NO_ERROR;
; ..\eeprom\MemIf\MemIf.c	   207      retVal = MemIf_MemHwaApis[DeviceIndex].Write(BlockNumber, DataBufferPtr); /* SBSW_MEMIF_02 */
	fcall	.cocofun_1
.L152:
	ld.a	a15,[a15]4
.L177:
	mov	d4,d5
.L153:
	ji	a15
.L113:
	
__MemIf_Write_function_end:
	.size	MemIf_Write,__MemIf_Write_function_end-MemIf_Write
.L56:
	; End of function
	
	.sdecl	'.text.MemIf.MemIf_InvalidateBlock',code,cluster('MemIf_InvalidateBlock')
	.sect	'.text.MemIf.MemIf_InvalidateBlock'
	.align	2
	
	.global	MemIf_InvalidateBlock

; ..\eeprom\MemIf\MemIf.c	   208    }
; ..\eeprom\MemIf\MemIf.c	   209  
; ..\eeprom\MemIf\MemIf.c	   210    /* ----- Development Error Report ---------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   211  #if (MEMIF_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   212    if (errorId != MEMIF_E_NO_ERROR)
; ..\eeprom\MemIf\MemIf.c	   213    {
; ..\eeprom\MemIf\MemIf.c	   214      (void)Det_ReportError(MEMIF_MODULE_ID, MEMIF_INSTANCE_ID, MEMIF_SID_WRITE, errorId);
; ..\eeprom\MemIf\MemIf.c	   215    }
; ..\eeprom\MemIf\MemIf.c	   216  #else
; ..\eeprom\MemIf\MemIf.c	   217    MEMIF_DUMMY_STATEMENT(errorId); /* SBSW_MEMIF_03 */
; ..\eeprom\MemIf\MemIf.c	   218  #endif
; ..\eeprom\MemIf\MemIf.c	   219  
; ..\eeprom\MemIf\MemIf.c	   220    return retVal;
; ..\eeprom\MemIf\MemIf.c	   221  }
; ..\eeprom\MemIf\MemIf.c	   222  
; ..\eeprom\MemIf\MemIf.c	   223  
; ..\eeprom\MemIf\MemIf.c	   224  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   225   *  MemIf_InvalidateBlock()
; ..\eeprom\MemIf\MemIf.c	   226   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   227  /*!
; ..\eeprom\MemIf\MemIf.c	   228   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   229   *
; ..\eeprom\MemIf\MemIf.c	   230   *
; ..\eeprom\MemIf\MemIf.c	   231   *
; ..\eeprom\MemIf\MemIf.c	   232   *
; ..\eeprom\MemIf\MemIf.c	   233   */
; ..\eeprom\MemIf\MemIf.c	   234  FUNC(Std_ReturnType, MEMIF_CODE) MemIf_InvalidateBlock(uint8  DeviceIndex, uint16 BlockNumber)
; Function MemIf_InvalidateBlock
.L32:
MemIf_InvalidateBlock:	.type	func

; ..\eeprom\MemIf\MemIf.c	   235  {
; ..\eeprom\MemIf\MemIf.c	   236    /* ----- Local Variables ------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   237    Std_ReturnType retVal;
; ..\eeprom\MemIf\MemIf.c	   238    uint8 errorId;
; ..\eeprom\MemIf\MemIf.c	   239  
; ..\eeprom\MemIf\MemIf.c	   240    /* ----- Development Error Detection ------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   241  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   242    if (MemIf_DetChkDeviceIndex(DeviceIndex) == E_NOT_OK)
; ..\eeprom\MemIf\MemIf.c	   243    {
; ..\eeprom\MemIf\MemIf.c	   244      retVal = E_NOT_OK;
; ..\eeprom\MemIf\MemIf.c	   245      errorId = MEMIF_E_PARAM_DEVICE;
; ..\eeprom\MemIf\MemIf.c	   246    }
; ..\eeprom\MemIf\MemIf.c	   247    else
; ..\eeprom\MemIf\MemIf.c	   248  #endif
; ..\eeprom\MemIf\MemIf.c	   249    {
; ..\eeprom\MemIf\MemIf.c	   250    /* ----- Implementation -------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   251      errorId = MEMIF_E_NO_ERROR;
; ..\eeprom\MemIf\MemIf.c	   252      retVal = MemIf_MemHwaApis[DeviceIndex].InvalidateBlock(BlockNumber); /* SBSW_MEMIF_02 */
	fcall	.cocofun_1
.L154:
	ld.a	a15,[a15]12
.L182:
	mov	d4,d5
.L155:
	ji	a15
.L118:
	
__MemIf_InvalidateBlock_function_end:
	.size	MemIf_InvalidateBlock,__MemIf_InvalidateBlock_function_end-MemIf_InvalidateBlock
.L61:
	; End of function
	
	.sdecl	'.text.MemIf.MemIf_EraseImmediateBlock',code,cluster('MemIf_EraseImmediateBlock')
	.sect	'.text.MemIf.MemIf_EraseImmediateBlock'
	.align	2
	
	.global	MemIf_EraseImmediateBlock

; ..\eeprom\MemIf\MemIf.c	   253    }
; ..\eeprom\MemIf\MemIf.c	   254  
; ..\eeprom\MemIf\MemIf.c	   255    /* ----- Development Error Report ---------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   256  #if (MEMIF_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   257    if (errorId != MEMIF_E_NO_ERROR)
; ..\eeprom\MemIf\MemIf.c	   258    {
; ..\eeprom\MemIf\MemIf.c	   259      (void)Det_ReportError(MEMIF_MODULE_ID, MEMIF_INSTANCE_ID, MEMIF_SID_INVALIDATE, errorId);
; ..\eeprom\MemIf\MemIf.c	   260    }
; ..\eeprom\MemIf\MemIf.c	   261  #else
; ..\eeprom\MemIf\MemIf.c	   262    MEMIF_DUMMY_STATEMENT(errorId); /* SBSW_MEMIF_03 */
; ..\eeprom\MemIf\MemIf.c	   263  #endif
; ..\eeprom\MemIf\MemIf.c	   264  
; ..\eeprom\MemIf\MemIf.c	   265    return retVal;
; ..\eeprom\MemIf\MemIf.c	   266  }
; ..\eeprom\MemIf\MemIf.c	   267  
; ..\eeprom\MemIf\MemIf.c	   268  
; ..\eeprom\MemIf\MemIf.c	   269  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   270   *  MemIf_EraseImmediateBlock()
; ..\eeprom\MemIf\MemIf.c	   271   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   272  /*!
; ..\eeprom\MemIf\MemIf.c	   273   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   274   *
; ..\eeprom\MemIf\MemIf.c	   275   *
; ..\eeprom\MemIf\MemIf.c	   276   *
; ..\eeprom\MemIf\MemIf.c	   277   *
; ..\eeprom\MemIf\MemIf.c	   278   */
; ..\eeprom\MemIf\MemIf.c	   279  FUNC(Std_ReturnType, MEMIF_CODE) MemIf_EraseImmediateBlock(uint8  DeviceIndex, uint16 BlockNumber)
; Function MemIf_EraseImmediateBlock
.L34:
MemIf_EraseImmediateBlock:	.type	func

; ..\eeprom\MemIf\MemIf.c	   280  {
; ..\eeprom\MemIf\MemIf.c	   281    /* ----- Local Variables ------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   282    Std_ReturnType retVal;
; ..\eeprom\MemIf\MemIf.c	   283    uint8 errorId;
; ..\eeprom\MemIf\MemIf.c	   284  
; ..\eeprom\MemIf\MemIf.c	   285    /* ----- Development Error Detection ------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   286  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   287    if (MemIf_DetChkDeviceIndex(DeviceIndex) == E_NOT_OK)
; ..\eeprom\MemIf\MemIf.c	   288    {
; ..\eeprom\MemIf\MemIf.c	   289      retVal = E_NOT_OK;
; ..\eeprom\MemIf\MemIf.c	   290      errorId = MEMIF_E_PARAM_DEVICE;
; ..\eeprom\MemIf\MemIf.c	   291    }
; ..\eeprom\MemIf\MemIf.c	   292    else
; ..\eeprom\MemIf\MemIf.c	   293  #endif
; ..\eeprom\MemIf\MemIf.c	   294    {
; ..\eeprom\MemIf\MemIf.c	   295    /* ----- Implementation -------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   296      errorId = MEMIF_E_NO_ERROR;
; ..\eeprom\MemIf\MemIf.c	   297      retVal = MemIf_MemHwaApis[DeviceIndex].EraseImmediateBlock(BlockNumber); /* SBSW_MEMIF_02 */
	fcall	.cocofun_1
.L156:
	ld.a	a15,[a15]8
.L187:
	mov	d4,d5
.L157:
	ji	a15
.L122:
	
__MemIf_EraseImmediateBlock_function_end:
	.size	MemIf_EraseImmediateBlock,__MemIf_EraseImmediateBlock_function_end-MemIf_EraseImmediateBlock
.L66:
	; End of function
	
	.sdecl	'.text.MemIf.MemIf_Cancel',code,cluster('MemIf_Cancel')
	.sect	'.text.MemIf.MemIf_Cancel'
	.align	2
	
	.global	MemIf_Cancel

; ..\eeprom\MemIf\MemIf.c	   298    }
; ..\eeprom\MemIf\MemIf.c	   299  
; ..\eeprom\MemIf\MemIf.c	   300    /* ----- Development Error Report ---------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   301  #if (MEMIF_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   302    if (errorId != MEMIF_E_NO_ERROR)
; ..\eeprom\MemIf\MemIf.c	   303    {
; ..\eeprom\MemIf\MemIf.c	   304      (void)Det_ReportError(MEMIF_MODULE_ID, MEMIF_INSTANCE_ID, MEMIF_SID_ERASE, errorId);
; ..\eeprom\MemIf\MemIf.c	   305    }
; ..\eeprom\MemIf\MemIf.c	   306  #else
; ..\eeprom\MemIf\MemIf.c	   307    MEMIF_DUMMY_STATEMENT(errorId); /* SBSW_MEMIF_03 */
; ..\eeprom\MemIf\MemIf.c	   308  #endif
; ..\eeprom\MemIf\MemIf.c	   309  
; ..\eeprom\MemIf\MemIf.c	   310    return retVal;
; ..\eeprom\MemIf\MemIf.c	   311  }
; ..\eeprom\MemIf\MemIf.c	   312  
; ..\eeprom\MemIf\MemIf.c	   313  
; ..\eeprom\MemIf\MemIf.c	   314  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   315   *  MemIf_Cancel()
; ..\eeprom\MemIf\MemIf.c	   316   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   317  /*!
; ..\eeprom\MemIf\MemIf.c	   318   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   319   *
; ..\eeprom\MemIf\MemIf.c	   320   *
; ..\eeprom\MemIf\MemIf.c	   321   *
; ..\eeprom\MemIf\MemIf.c	   322   *
; ..\eeprom\MemIf\MemIf.c	   323   */
; ..\eeprom\MemIf\MemIf.c	   324  FUNC(void, MEMIF_CODE) MemIf_Cancel(uint8 DeviceIndex)
; Function MemIf_Cancel
.L36:
MemIf_Cancel:	.type	func

; ..\eeprom\MemIf\MemIf.c	   325  {
; ..\eeprom\MemIf\MemIf.c	   326    /* ----- Local Variables ------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   327    uint8 errorId;
; ..\eeprom\MemIf\MemIf.c	   328  
; ..\eeprom\MemIf\MemIf.c	   329    /* ----- Development Error Detection ------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   330  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   331    if (MemIf_DetChkDeviceIndex(DeviceIndex) == E_NOT_OK)
; ..\eeprom\MemIf\MemIf.c	   332    {
; ..\eeprom\MemIf\MemIf.c	   333      errorId = MEMIF_E_PARAM_DEVICE;
; ..\eeprom\MemIf\MemIf.c	   334    }
; ..\eeprom\MemIf\MemIf.c	   335    else
; ..\eeprom\MemIf\MemIf.c	   336  #endif
; ..\eeprom\MemIf\MemIf.c	   337    {
; ..\eeprom\MemIf\MemIf.c	   338    /* ----- Implementation -------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   339      errorId = MEMIF_E_NO_ERROR;
; ..\eeprom\MemIf\MemIf.c	   340      MemIf_MemHwaApis[DeviceIndex].Cancel(); /* SBSW_MEMIF_02 */
	fcall	.cocofun_1
.L192:
	ld.a	a15,[a15]16
.L193:
	ji	a15
.L126:
	
__MemIf_Cancel_function_end:
	.size	MemIf_Cancel,__MemIf_Cancel_function_end-MemIf_Cancel
.L71:
	; End of function
	
	.sdecl	'.text.MemIf.MemIf_GetStatus',code,cluster('MemIf_GetStatus')
	.sect	'.text.MemIf.MemIf_GetStatus'
	.align	2
	
	.global	MemIf_GetStatus

; ..\eeprom\MemIf\MemIf.c	   341    }
; ..\eeprom\MemIf\MemIf.c	   342  
; ..\eeprom\MemIf\MemIf.c	   343    /* ----- Development Error Report ---------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   344  #if (MEMIF_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   345    if (errorId != MEMIF_E_NO_ERROR)
; ..\eeprom\MemIf\MemIf.c	   346    {
; ..\eeprom\MemIf\MemIf.c	   347      (void)Det_ReportError(MEMIF_MODULE_ID, MEMIF_INSTANCE_ID, MEMIF_SID_CANCEL, errorId);
; ..\eeprom\MemIf\MemIf.c	   348    }
; ..\eeprom\MemIf\MemIf.c	   349  #else
; ..\eeprom\MemIf\MemIf.c	   350    MEMIF_DUMMY_STATEMENT(errorId); /* SBSW_MEMIF_03 */
; ..\eeprom\MemIf\MemIf.c	   351  #endif
; ..\eeprom\MemIf\MemIf.c	   352  
; ..\eeprom\MemIf\MemIf.c	   353  }
; ..\eeprom\MemIf\MemIf.c	   354  
; ..\eeprom\MemIf\MemIf.c	   355  
; ..\eeprom\MemIf\MemIf.c	   356  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   357   *  MemIf_GetStatus()
; ..\eeprom\MemIf\MemIf.c	   358   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   359  /*!
; ..\eeprom\MemIf\MemIf.c	   360   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   361   *
; ..\eeprom\MemIf\MemIf.c	   362   *
; ..\eeprom\MemIf\MemIf.c	   363   *
; ..\eeprom\MemIf\MemIf.c	   364   *
; ..\eeprom\MemIf\MemIf.c	   365   *
; ..\eeprom\MemIf\MemIf.c	   366   *
; ..\eeprom\MemIf\MemIf.c	   367   *
; ..\eeprom\MemIf\MemIf.c	   368   *
; ..\eeprom\MemIf\MemIf.c	   369   *
; ..\eeprom\MemIf\MemIf.c	   370   *
; ..\eeprom\MemIf\MemIf.c	   371   */
; ..\eeprom\MemIf\MemIf.c	   372  FUNC(MemIf_StatusType, MEMIF_CODE) MemIf_GetStatus(uint8 DeviceIndex)
; Function MemIf_GetStatus
.L38:
MemIf_GetStatus:	.type	func

; ..\eeprom\MemIf\MemIf.c	   373  {
; ..\eeprom\MemIf\MemIf.c	   374    /* ----- Local Variables ------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   375    uint8 errorId;
; ..\eeprom\MemIf\MemIf.c	   376    MemIf_StatusType retVal;
; ..\eeprom\MemIf\MemIf.c	   377  
; ..\eeprom\MemIf\MemIf.c	   378    uint8 MemIf_LoopCounter;
; ..\eeprom\MemIf\MemIf.c	   379    uint8 MemIf_StatusMask = 0;
; ..\eeprom\MemIf\MemIf.c	   380  
; ..\eeprom\MemIf\MemIf.c	   381    /* ----- Development Error Detection ------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   382  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   383    /* #10 Check if DeviceIndex is out of bounds. BroadcastId (0xFF) is a valid input. */
; ..\eeprom\MemIf\MemIf.c	   384    if ( (DeviceIndex != MEMIF_BROADCAST_ID) && (DeviceIndex >= MemIf_NumberOfDevices) )
; ..\eeprom\MemIf\MemIf.c	   385    {
; ..\eeprom\MemIf\MemIf.c	   386      errorId = MEMIF_E_PARAM_DEVICE;
; ..\eeprom\MemIf\MemIf.c	   387      retVal = MEMIF_UNINIT;
; ..\eeprom\MemIf\MemIf.c	   388    }
; ..\eeprom\MemIf\MemIf.c	   389    else
; ..\eeprom\MemIf\MemIf.c	   390  #endif
; ..\eeprom\MemIf\MemIf.c	   391    {
; ..\eeprom\MemIf\MemIf.c	   392      /* ----- Implementation -------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   393  
; ..\eeprom\MemIf\MemIf.c	   394      errorId = MEMIF_E_NO_ERROR;
; ..\eeprom\MemIf\MemIf.c	   395  
; ..\eeprom\MemIf\MemIf.c	   396      if (MEMIF_BROADCAST_ID == DeviceIndex)
; ..\eeprom\MemIf\MemIf.c	   397      {
; ..\eeprom\MemIf\MemIf.c	   398  
; ..\eeprom\MemIf\MemIf.c	   399        /* #20 Get status of all configured MemHwA modules */
; ..\eeprom\MemIf\MemIf.c	   400        for (MemIf_LoopCounter = 0; MemIf_LoopCounter < MemIf_NumberOfDevices; MemIf_LoopCounter++)
; ..\eeprom\MemIf\MemIf.c	   401        {
; ..\eeprom\MemIf\MemIf.c	   402          /* #25 Store each status to status mask */
; ..\eeprom\MemIf\MemIf.c	   403          MemIf_StatusMask |= (uint8) ((uint8) 1u << (uint8) MemIf_MemHwaApis[MemIf_LoopCounter].GetStatus()); /* SBSW_MEMIF_02 */
	mov	d8,#0
	fcall	.cocofun_2
.L198:
	mov	d15,#255
.L199:
	jne	d15,d4,.L7
.L200:
	mov	d15,d8
	movh.a	a12,#@his(MemIf_NumberOfDevices)
.L158:
	lea	a12,[a12]@los(MemIf_NumberOfDevices)
.L201:
	j	.L8
.L9:
	mov	d9,#1
	ld.a	a2,[a15]20
.L202:
	calli	a2
.L203:
	sha	d9,d9,d2
	lea	a15,[a15]32
.L204:
	extr.u	d0,d9,#0,#8
.L205:
	add	d15,#1
.L159:
	extr.u	d15,d15,#0,#8
.L160:
	or	d8,d0
.L8:
	ld.bu	d0,[a12]
.L206:
	jlt.u	d15,d0,.L9
.L207:

; ..\eeprom\MemIf\MemIf.c	   404        }
; ..\eeprom\MemIf\MemIf.c	   405  
; ..\eeprom\MemIf\MemIf.c	   406        /* #30 Check if UNINIT-bit is set */
; ..\eeprom\MemIf\MemIf.c	   407        if (MemIf_IsBitSet(MemIf_StatusMask, MEMIF_UNINIT) == E_OK)
	mov	d5,#0
	mov	d4,d8
	call	MemIf_IsBitSet
.L208:
	jne	d2,#0,.L10
.L209:

; ..\eeprom\MemIf\MemIf.c	   408        {
; ..\eeprom\MemIf\MemIf.c	   409          retVal = MEMIF_UNINIT;
; ..\eeprom\MemIf\MemIf.c	   410        }
; ..\eeprom\MemIf\MemIf.c	   411        /* #31 Check if BUSY-bit is set */
; ..\eeprom\MemIf\MemIf.c	   412        else if (MemIf_IsBitSet(MemIf_StatusMask, MEMIF_BUSY) == E_OK)
; ..\eeprom\MemIf\MemIf.c	   413        {
; ..\eeprom\MemIf\MemIf.c	   414          retVal = MEMIF_BUSY;
; ..\eeprom\MemIf\MemIf.c	   415        }
; ..\eeprom\MemIf\MemIf.c	   416        /* #32 Check if BUSY_INTERNAL-bit is set */
; ..\eeprom\MemIf\MemIf.c	   417        else if (MemIf_IsBitSet(MemIf_StatusMask, MEMIF_BUSY_INTERNAL) == E_OK)
; ..\eeprom\MemIf\MemIf.c	   418        {
; ..\eeprom\MemIf\MemIf.c	   419          retVal = MEMIF_BUSY_INTERNAL;
; ..\eeprom\MemIf\MemIf.c	   420        }
; ..\eeprom\MemIf\MemIf.c	   421        /* #33 All MemHwA modules are MEMIF_IDLE */
; ..\eeprom\MemIf\MemIf.c	   422        else
; ..\eeprom\MemIf\MemIf.c	   423        {
; ..\eeprom\MemIf\MemIf.c	   424          retVal = MEMIF_IDLE;
; ..\eeprom\MemIf\MemIf.c	   425        }
; ..\eeprom\MemIf\MemIf.c	   426  
; ..\eeprom\MemIf\MemIf.c	   427      }
; ..\eeprom\MemIf\MemIf.c	   428      else
; ..\eeprom\MemIf\MemIf.c	   429      /* #40 Get status of MemHwA module indexed by DeviceIndex */
; ..\eeprom\MemIf\MemIf.c	   430      {
; ..\eeprom\MemIf\MemIf.c	   431        retVal = MemIf_MemHwaApis[DeviceIndex].GetStatus(); /* SBSW_MEMIF_02 */
; ..\eeprom\MemIf\MemIf.c	   432      }
; ..\eeprom\MemIf\MemIf.c	   433    }
; ..\eeprom\MemIf\MemIf.c	   434  
; ..\eeprom\MemIf\MemIf.c	   435    /* #50 Report development error if configured */
; ..\eeprom\MemIf\MemIf.c	   436    /* ----- Development Error Report ---------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   437  #if (MEMIF_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   438    if (errorId != MEMIF_E_NO_ERROR)
; ..\eeprom\MemIf\MemIf.c	   439    {
; ..\eeprom\MemIf\MemIf.c	   440      (void)Det_ReportError(MEMIF_MODULE_ID, MEMIF_INSTANCE_ID, MEMIF_SID_GET_STATUS, errorId);
; ..\eeprom\MemIf\MemIf.c	   441    }
; ..\eeprom\MemIf\MemIf.c	   442  #else
; ..\eeprom\MemIf\MemIf.c	   443    MEMIF_DUMMY_STATEMENT(errorId); /* SBSW_MEMIF_03 */
; ..\eeprom\MemIf\MemIf.c	   444  #endif
; ..\eeprom\MemIf\MemIf.c	   445  
; ..\eeprom\MemIf\MemIf.c	   446    return retVal;
; ..\eeprom\MemIf\MemIf.c	   447  } /* PRQA S 6080 */ /* MD_MSR_STMIF */
	mov	d2,#0
	ret
.L10:
	mov	d5,#2
	mov	d4,d8
	call	MemIf_IsBitSet
.L210:
	jne	d2,#0,.L12
.L211:
	mov	d2,#2
	ret
.L12:
	mov	d5,#3
	mov	d4,d8
	call	MemIf_IsBitSet
.L212:
	jne	d2,#0,.L14
.L213:
	mov	d2,#3
	ret
.L14:
	mov	d2,#1
	ret
.L7:
	sha	d4,#5
.L161:
	addsc.a	a15,a15,d4,#0
.L214:
	ld.a	a15,[a15]20
.L215:
	ji	a15
.L129:
	
__MemIf_GetStatus_function_end:
	.size	MemIf_GetStatus,__MemIf_GetStatus_function_end-MemIf_GetStatus
.L76:
	; End of function
	
	.sdecl	'.text.MemIf.MemIf_GetJobResult',code,cluster('MemIf_GetJobResult')
	.sect	'.text.MemIf.MemIf_GetJobResult'
	.align	2
	
	.global	MemIf_GetJobResult

; ..\eeprom\MemIf\MemIf.c	   448  
; ..\eeprom\MemIf\MemIf.c	   449  
; ..\eeprom\MemIf\MemIf.c	   450  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   451   *  MemIf_GetJobResult()
; ..\eeprom\MemIf\MemIf.c	   452   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   453  /*!
; ..\eeprom\MemIf\MemIf.c	   454   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   455   *
; ..\eeprom\MemIf\MemIf.c	   456   *
; ..\eeprom\MemIf\MemIf.c	   457   *
; ..\eeprom\MemIf\MemIf.c	   458   *
; ..\eeprom\MemIf\MemIf.c	   459   */
; ..\eeprom\MemIf\MemIf.c	   460  FUNC(MemIf_JobResultType, MEMIF_CODE) MemIf_GetJobResult(uint8 DeviceIndex)
; Function MemIf_GetJobResult
.L40:
MemIf_GetJobResult:	.type	func

; ..\eeprom\MemIf\MemIf.c	   461  {
; ..\eeprom\MemIf\MemIf.c	   462    /* ----- Local Variables ------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   463    uint8 errorId;
; ..\eeprom\MemIf\MemIf.c	   464    MemIf_JobResultType retVal;
; ..\eeprom\MemIf\MemIf.c	   465  
; ..\eeprom\MemIf\MemIf.c	   466    /* ----- Development Error Detection ------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   467  #if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   468    if (MemIf_DetChkDeviceIndex(DeviceIndex) == E_NOT_OK)
; ..\eeprom\MemIf\MemIf.c	   469    {
; ..\eeprom\MemIf\MemIf.c	   470      errorId = MEMIF_E_PARAM_DEVICE;
; ..\eeprom\MemIf\MemIf.c	   471      retVal = MEMIF_JOB_FAILED;
; ..\eeprom\MemIf\MemIf.c	   472    }
; ..\eeprom\MemIf\MemIf.c	   473    else
; ..\eeprom\MemIf\MemIf.c	   474  #endif
; ..\eeprom\MemIf\MemIf.c	   475    {
; ..\eeprom\MemIf\MemIf.c	   476    /* ----- Implementation -------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   477      errorId = MEMIF_E_NO_ERROR;
; ..\eeprom\MemIf\MemIf.c	   478      retVal = MemIf_MemHwaApis[DeviceIndex].GetJobResult(); /* SBSW_MEMIF_02 */
	fcall	.cocofun_1
.L220:
	ld.a	a15,[a15]24
.L221:
	ji	a15
.L135:
	
__MemIf_GetJobResult_function_end:
	.size	MemIf_GetJobResult,__MemIf_GetJobResult_function_end-MemIf_GetJobResult
.L81:
	; End of function
	
	.sdecl	'.text.MemIf.MemIf_SetMode',code,cluster('MemIf_SetMode')
	.sect	'.text.MemIf.MemIf_SetMode'
	.align	2
	
	.global	MemIf_SetMode

; ..\eeprom\MemIf\MemIf.c	   479    }
; ..\eeprom\MemIf\MemIf.c	   480  
; ..\eeprom\MemIf\MemIf.c	   481    /* ----- Development Error Report ---------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   482  #if (MEMIF_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   483    if (errorId != MEMIF_E_NO_ERROR)
; ..\eeprom\MemIf\MemIf.c	   484    {
; ..\eeprom\MemIf\MemIf.c	   485      (void)Det_ReportError(MEMIF_MODULE_ID, MEMIF_INSTANCE_ID, MEMIF_SID_GET_JOB_RESULT, errorId);
; ..\eeprom\MemIf\MemIf.c	   486    }
; ..\eeprom\MemIf\MemIf.c	   487  #else
; ..\eeprom\MemIf\MemIf.c	   488    MEMIF_DUMMY_STATEMENT(errorId); /* SBSW_MEMIF_03 */
; ..\eeprom\MemIf\MemIf.c	   489  #endif
; ..\eeprom\MemIf\MemIf.c	   490  
; ..\eeprom\MemIf\MemIf.c	   491    return retVal;
; ..\eeprom\MemIf\MemIf.c	   492  }
; ..\eeprom\MemIf\MemIf.c	   493  
; ..\eeprom\MemIf\MemIf.c	   494  
; ..\eeprom\MemIf\MemIf.c	   495  #if (MEMIF_VERSION_INFO_API == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   496  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   497   *  MemIf_GetVersionInfo()
; ..\eeprom\MemIf\MemIf.c	   498   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   499  /*!
; ..\eeprom\MemIf\MemIf.c	   500   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   501   *
; ..\eeprom\MemIf\MemIf.c	   502   *
; ..\eeprom\MemIf\MemIf.c	   503   *
; ..\eeprom\MemIf\MemIf.c	   504   *
; ..\eeprom\MemIf\MemIf.c	   505   */
; ..\eeprom\MemIf\MemIf.c	   506  void MemIf_GetVersionInfo(P2VAR(Std_VersionInfoType, AUTOMATIC, MEMIF_APPL_DATA) VersionInfoPtr)
; ..\eeprom\MemIf\MemIf.c	   507  {
; ..\eeprom\MemIf\MemIf.c	   508    /* ----- Local Variables ------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   509    uint8 errorId;
; ..\eeprom\MemIf\MemIf.c	   510  
; ..\eeprom\MemIf\MemIf.c	   511    /* ----- Development Error Detection ------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   512  # if (MEMIF_DEV_ERROR_DETECT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   513    if (VersionInfoPtr == NULL_PTR)
; ..\eeprom\MemIf\MemIf.c	   514    {
; ..\eeprom\MemIf\MemIf.c	   515      errorId = MEMIF_E_PARAM_POINTER;
; ..\eeprom\MemIf\MemIf.c	   516    }
; ..\eeprom\MemIf\MemIf.c	   517    else
; ..\eeprom\MemIf\MemIf.c	   518  # endif
; ..\eeprom\MemIf\MemIf.c	   519    {
; ..\eeprom\MemIf\MemIf.c	   520    /* ----- Implementation -------------------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   521  
; ..\eeprom\MemIf\MemIf.c	   522      errorId = MEMIF_E_NO_ERROR;
; ..\eeprom\MemIf\MemIf.c	   523  
; ..\eeprom\MemIf\MemIf.c	   524      VersionInfoPtr->vendorID         = MEMIF_VENDOR_ID; /* SBSW_MEMIF_01 */
; ..\eeprom\MemIf\MemIf.c	   525      VersionInfoPtr->moduleID         = MEMIF_MODULE_ID; /* SBSW_MEMIF_01 */
; ..\eeprom\MemIf\MemIf.c	   526      VersionInfoPtr->sw_major_version = MEMIF_SW_MAJOR_VERSION; /* SBSW_MEMIF_01 */
; ..\eeprom\MemIf\MemIf.c	   527      VersionInfoPtr->sw_minor_version = MEMIF_SW_MINOR_VERSION; /* SBSW_MEMIF_01 */
; ..\eeprom\MemIf\MemIf.c	   528      VersionInfoPtr->sw_patch_version = MEMIF_SW_PATCH_VERSION; /* SBSW_MEMIF_01 */
; ..\eeprom\MemIf\MemIf.c	   529    }
; ..\eeprom\MemIf\MemIf.c	   530  
; ..\eeprom\MemIf\MemIf.c	   531    /* ----- Development Error Report ---------------------------------------------------------------------*/
; ..\eeprom\MemIf\MemIf.c	   532  # if (MEMIF_DEV_ERROR_REPORT == STD_ON)
; ..\eeprom\MemIf\MemIf.c	   533    if (errorId != MEMIF_E_NO_ERROR)
; ..\eeprom\MemIf\MemIf.c	   534    {
; ..\eeprom\MemIf\MemIf.c	   535      (void)Det_ReportError(MEMIF_MODULE_ID, MEMIF_INSTANCE_ID, MEMIF_SID_VERSION_INFO, errorId);
; ..\eeprom\MemIf\MemIf.c	   536    }
; ..\eeprom\MemIf\MemIf.c	   537  # else
; ..\eeprom\MemIf\MemIf.c	   538    MEMIF_DUMMY_STATEMENT(errorId); /* SBSW_MEMIF_03 */
; ..\eeprom\MemIf\MemIf.c	   539  # endif
; ..\eeprom\MemIf\MemIf.c	   540  
; ..\eeprom\MemIf\MemIf.c	   541  }
; ..\eeprom\MemIf\MemIf.c	   542  #endif /* MEMIF_VERSION_INFO_API */
; ..\eeprom\MemIf\MemIf.c	   543  
; ..\eeprom\MemIf\MemIf.c	   544  
; ..\eeprom\MemIf\MemIf.c	   545  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   546   *  MemIf_SetMode()
; ..\eeprom\MemIf\MemIf.c	   547   *********************************************************************************************************************/
; ..\eeprom\MemIf\MemIf.c	   548  /*!
; ..\eeprom\MemIf\MemIf.c	   549   * Internal comment removed.
; ..\eeprom\MemIf\MemIf.c	   550   *
; ..\eeprom\MemIf\MemIf.c	   551   *
; ..\eeprom\MemIf\MemIf.c	   552   */
; ..\eeprom\MemIf\MemIf.c	   553  FUNC(void, MEMIF_CODE) MemIf_SetMode(MemIf_ModeType Mode)
; Function MemIf_SetMode
.L42:
MemIf_SetMode:	.type	func
	mov	d8,d4
.L162:

; ..\eeprom\MemIf\MemIf.c	   554  {
; ..\eeprom\MemIf\MemIf.c	   555    uint8 MemIf_LoopCounter;
; ..\eeprom\MemIf\MemIf.c	   556  
; ..\eeprom\MemIf\MemIf.c	   557    for(MemIf_LoopCounter = 0; MemIf_LoopCounter < MemIf_NumberOfDevices; MemIf_LoopCounter++)
; ..\eeprom\MemIf\MemIf.c	   558    {
; ..\eeprom\MemIf\MemIf.c	   559      MemIf_MemHwaApis[MemIf_LoopCounter].SetMode(Mode); /* SBSW_MEMIF_02 */
	mov	d15,#0
	fcall	.cocofun_2
.L226:
	movh.a	a12,#@his(MemIf_NumberOfDevices)
	lea	a12,[a12]@los(MemIf_NumberOfDevices)
.L227:
	j	.L19
.L20:
	ld.a	a2,[a15]28
.L228:
	mov	d4,d8
	calli	a2
.L229:
	add	d15,#1
	lea	a15,[a15]32
.L163:
	extr.u	d15,d15,#0,#8
.L19:
	ld.bu	d0,[a12]
.L230:
	jlt.u	d15,d0,.L20
.L231:

; ..\eeprom\MemIf\MemIf.c	   560    }
; ..\eeprom\MemIf\MemIf.c	   561  }
	ret
.L138:
	
__MemIf_SetMode_function_end:
	.size	MemIf_SetMode,__MemIf_SetMode_function_end-MemIf_SetMode
.L86:
	; End of function
	
	.calls	'MemIf_Read','__INDIRECT__'
	.calls	'MemIf_Write','__INDIRECT__'
	.calls	'MemIf_InvalidateBlock','__INDIRECT__'
	.calls	'MemIf_EraseImmediateBlock','__INDIRECT__'
	.calls	'MemIf_Cancel','__INDIRECT__'
	.calls	'MemIf_GetStatus','__INDIRECT__'
	.calls	'MemIf_GetStatus','MemIf_IsBitSet'
	.calls	'MemIf_GetJobResult','__INDIRECT__'
	.calls	'MemIf_SetMode','__INDIRECT__'
	.calls	'MemIf_Read','.cocofun_1'
	.calls	'.cocofun_1','.cocofun_2'
	.calls	'MemIf_Write','.cocofun_1'
	.calls	'MemIf_InvalidateBlock','.cocofun_1'
	.calls	'MemIf_EraseImmediateBlock','.cocofun_1'
	.calls	'MemIf_Cancel','.cocofun_1'
	.calls	'MemIf_GetStatus','.cocofun_2'
	.calls	'MemIf_GetJobResult','.cocofun_1'
	.calls	'MemIf_SetMode','.cocofun_2'
	.calls	'MemIf_IsBitSet','',0
	.calls	'MemIf_Read','',0
	.calls	'.cocofun_1','',0
	.calls	'.cocofun_2','',0
	.calls	'MemIf_Write','',0
	.calls	'MemIf_InvalidateBlock','',0
	.calls	'MemIf_EraseImmediateBlock','',0
	.calls	'MemIf_Cancel','',0
	.calls	'MemIf_GetStatus','',0
	.calls	'MemIf_GetJobResult','',0
	.extern	MemIf_NumberOfDevices
	.extern	MemIf_MemHwaApis
	.extern	__INDIRECT__
	.calls	'MemIf_SetMode','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L44:
	.word	1422
	.half	3
	.word	.L45
	.byte	4
.L43:
	.byte	1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L46
.L102:
	.byte	2
	.byte	'unsigned char',0,1,8
.L105:
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.word	178
	.byte	3
	.word	178
.L108:
	.byte	4
	.byte	'MemIf_DataPtr_pu8',0,1,96,50
	.word	222
.L128:
	.byte	5,1,59,9,1,6
	.byte	'MEMIF_UNINIT',0,0,6
	.byte	'MEMIF_IDLE',0,1,6
	.byte	'MEMIF_BUSY',0,2,6
	.byte	'MEMIF_BUSY_INTERNAL',0,3,0
.L134:
	.byte	5,1,72,9,1,6
	.byte	'MEMIF_JOB_OK',0,0,6
	.byte	'MEMIF_JOB_FAILED',0,1,6
	.byte	'MEMIF_JOB_PENDING',0,2,6
	.byte	'MEMIF_JOB_CANCELED',0,3,6
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,6
	.byte	'MEMIF_BLOCK_INVALID',0,5,0
.L139:
	.byte	5,1,88,9,1,6
	.byte	'MEMIF_MODE_SLOW',0,0,6
	.byte	'MEMIF_MODE_FAST',0,1,0,7
	.byte	'__INDIRECT__',0,2,1,1,1,1,1,8
	.byte	'void',0,3
	.word	514
	.byte	4
	.byte	'__prof_adm',0,2,1,1
	.word	520
	.byte	9,1,3
	.word	544
	.byte	4
	.byte	'__codeptr',0,2,1,1
	.word	546
	.byte	4
	.byte	'uint8',0,3,90,29
	.word	178
	.byte	4
	.byte	'uint16',0,3,92,29
	.word	195
	.byte	2
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,3,94,29
	.word	598
	.byte	4
	.byte	'Std_ReturnType',0,4,113,15
	.word	178
	.byte	4
	.byte	'MemIf_StatusType',0,1,65,3
	.word	253
	.byte	4
	.byte	'MemIf_JobResultType',0,1,80,3
	.word	322
	.byte	4
	.byte	'MemIf_ModeType',0,1,92,3
	.word	452
	.byte	10
	.word	178
	.byte	1,1,11
	.word	195
	.byte	11
	.word	195
	.byte	3
	.word	178
	.byte	11
	.word	750
	.byte	11
	.word	195
	.byte	0,3
	.word	733
	.byte	4
	.byte	'MemIf_ApiReadType',0,5,120,9
	.word	766
	.byte	10
	.word	178
	.byte	1,1,11
	.word	195
	.byte	11
	.word	750
	.byte	0,3
	.word	797
	.byte	4
	.byte	'MemIf_ApiWriteType',0,5,121,9
	.word	815
	.byte	10
	.word	178
	.byte	1,1,11
	.word	195
	.byte	0,3
	.word	847
	.byte	4
	.byte	'MemIf_ApiEraseImmediateBlockType',0,5,122,9
	.word	860
	.byte	4
	.byte	'MemIf_ApiInvalidateBlockType',0,5,123,9
	.word	860
	.byte	12,1,1,3
	.word	943
	.byte	4
	.byte	'MemIf_ApiCancelType',0,5,124,9
	.word	946
	.byte	13
	.word	253
	.byte	1,1,3
	.word	979
	.byte	4
	.byte	'MemIf_ApiGetStatusType',0,5,125,9
	.word	986
	.byte	13
	.word	322
	.byte	1,1,3
	.word	1022
	.byte	4
	.byte	'MemIf_ApiGetJobResultType',0,5,126,9
	.word	1029
	.byte	14,1,1,11
	.word	452
	.byte	0,3
	.word	1068
	.byte	4
	.byte	'MemIf_ApiSetModeType',0,5,127,9
	.word	1077
	.byte	15,5,130,1,9,32,16
	.byte	'Read',0,4
	.word	771
	.byte	2,35,0,16
	.byte	'Write',0,4
	.word	820
	.byte	2,35,4,16
	.byte	'EraseImmediateBlock',0,4
	.word	865
	.byte	2,35,8,16
	.byte	'InvalidateBlock',0,4
	.word	906
	.byte	2,35,12,16
	.byte	'Cancel',0,4
	.word	951
	.byte	2,35,16,16
	.byte	'GetStatus',0,4
	.word	991
	.byte	2,35,20,16
	.byte	'GetJobResult',0,4
	.word	1034
	.byte	2,35,24,16
	.byte	'SetMode',0,4
	.word	1082
	.byte	2,35,28,0,4
	.byte	'MemIf_MemHwAApi_Type',0,5,140,1,3
	.word	1111
	.byte	3
	.word	733
	.byte	3
	.word	797
	.byte	3
	.word	847
	.byte	3
	.word	847
	.byte	3
	.word	943
	.byte	3
	.word	979
	.byte	3
	.word	1022
	.byte	3
	.word	1068
	.byte	17
	.word	178
	.byte	18
	.byte	'MemIf_NumberOfDevices',0,5,150,1,34
	.word	1345
	.byte	1,1,19,32
	.word	1111
	.byte	20,0,0,17
	.word	1383
	.byte	18
	.byte	'MemIf_MemHwaApis',0,5,160,1,49
	.word	1392
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58
	.byte	15,59,15,57,15,73,19,0,0,5,4,1,58,15,59,15,57,15,11,15,0,0,6,40,0,3,8,28,13,0,0,7,46,0,3,8,58,15,59,15
	.byte	57,15,54,15,63,12,60,12,0,0,8,59,0,3,8,0,0,9,21,0,54,15,0,0,10,21,1,73,19,54,15,39,12,0,0,11,5,0,73,19
	.byte	0,0,12,21,0,54,15,39,12,0,0,13,21,0,73,19,54,15,39,12,0,0,14,21,1,54,15,39,12,0,0,15,19,1,58,15,59,15
	.byte	57,15,11,15,0,0,16,13,0,3,8,11,15,73,19,56,9,0,0,17,38,0,73,19,0,0,18,52,0,3,8,58,15,59,15,57,15,73,19
	.byte	63,12,60,12,0,0,19,1,1,11,15,73,19,0,0,20,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L46:
	.word	.L165-.L164
.L164:
	.half	3
	.word	.L167-.L166
.L166:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	0
	.byte	'..\\eeprom\\MemIf\\MemIf_Types.h',0,0,0,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Std_Types.h',0,1,0,0
	.byte	'_Cfg.h',0,2,0,0,0
.L167:
.L165:
	.sdecl	'.debug_info',debug,cluster('MemIf_Read')
	.sect	'.debug_info'
.L47:
	.word	369
	.half	3
	.word	.L48
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L50,.L49
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_Read',0,1,144,1,34
	.word	.L102
	.byte	1,1,1
	.word	.L24,.L103,.L23
	.byte	4
	.byte	'DeviceIndex',0,1,144,1,51
	.word	.L102,.L104
	.byte	4
	.byte	'BlockNumber',0,1,144,1,71
	.word	.L105,.L106
	.byte	4
	.byte	'BlockOffset',0,1,144,1,91
	.word	.L105,.L107
	.byte	4
	.byte	'DataBufferPtr',0,1,144,1,122
	.word	.L108,.L109
	.byte	4
	.byte	'Length',0,1,144,1,144,1
	.word	.L105,.L110
	.byte	5
	.word	.L111
	.byte	6
	.byte	'retVal',0,1,147,1,18
	.word	.L102,.L112
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_Read')
	.sect	'.debug_abbrev'
.L48:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_Read')
	.sect	'.debug_line'
.L49:
	.word	.L169-.L168
.L168:
	.half	3
	.word	.L171-.L170
.L170:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L171:
	.byte	5,31,7,0,5,2
	.word	.L24
	.byte	3,161,1,1,5,43,9
	.half	.L148-.L24
	.byte	1,5,90,9
	.half	.L172-.L148
	.byte	1,5,1,9
	.half	.L51-.L172
	.byte	3,14,0,1,1
.L169:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_Read')
	.sect	'.debug_ranges'
.L50:
	.word	-1,.L24,0,.L51-.L24,0,0
.L111:
	.word	-1,.L24,0,.L103-.L24,-1,.L26,0,.L96-.L26,-1,.L28,0,.L101-.L28,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_Write')
	.sect	'.debug_info'
.L52:
	.word	328
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L55,.L54
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_Write',0,1,189,1,34
	.word	.L102
	.byte	1,1,1
	.word	.L30,.L113,.L29
	.byte	4
	.byte	'DeviceIndex',0,1,189,1,52
	.word	.L102,.L114
	.byte	4
	.byte	'BlockNumber',0,1,189,1,72
	.word	.L105,.L115
	.byte	4
	.byte	'DataBufferPtr',0,1,189,1,103
	.word	.L108,.L116
	.byte	5
	.word	.L30,.L113
	.byte	6
	.byte	'retVal',0,1,192,1,18
	.word	.L102,.L117
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_Write')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_Write')
	.sect	'.debug_line'
.L54:
	.word	.L174-.L173
.L173:
	.half	3
	.word	.L176-.L175
.L175:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L176:
	.byte	5,31,7,0,5,2
	.word	.L30
	.byte	3,206,1,1,5,43,9
	.half	.L152-.L30
	.byte	1,5,63,9
	.half	.L177-.L152
	.byte	1,5,1,9
	.half	.L56-.L177
	.byte	3,14,0,1,1
.L174:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_Write')
	.sect	'.debug_ranges'
.L55:
	.word	-1,.L30,0,.L56-.L30,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_InvalidateBlock')
	.sect	'.debug_info'
.L57:
	.word	311
	.half	3
	.word	.L58
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L60,.L59
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_InvalidateBlock',0,1,234,1,34
	.word	.L102
	.byte	1,1,1
	.word	.L32,.L118,.L31
	.byte	4
	.byte	'DeviceIndex',0,1,234,1,63
	.word	.L102,.L119
	.byte	4
	.byte	'BlockNumber',0,1,234,1,83
	.word	.L105,.L120
	.byte	5
	.word	.L32,.L118
	.byte	6
	.byte	'retVal',0,1,237,1,18
	.word	.L102,.L121
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_InvalidateBlock')
	.sect	'.debug_abbrev'
.L58:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_InvalidateBlock')
	.sect	'.debug_line'
.L59:
	.word	.L179-.L178
.L178:
	.half	3
	.word	.L181-.L180
.L180:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L181:
	.byte	5,31,7,0,5,2
	.word	.L32
	.byte	3,251,1,1,5,43,9
	.half	.L154-.L32
	.byte	1,5,60,9
	.half	.L182-.L154
	.byte	1,5,1,9
	.half	.L61-.L182
	.byte	3,14,0,1,1
.L179:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_InvalidateBlock')
	.sect	'.debug_ranges'
.L60:
	.word	-1,.L32,0,.L61-.L32,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_EraseImmediateBlock')
	.sect	'.debug_info'
.L62:
	.word	315
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L65,.L64
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_EraseImmediateBlock',0,1,151,2,34
	.word	.L102
	.byte	1,1,1
	.word	.L34,.L122,.L33
	.byte	4
	.byte	'DeviceIndex',0,1,151,2,67
	.word	.L102,.L123
	.byte	4
	.byte	'BlockNumber',0,1,151,2,87
	.word	.L105,.L124
	.byte	5
	.word	.L34,.L122
	.byte	6
	.byte	'retVal',0,1,154,2,18
	.word	.L102,.L125
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_EraseImmediateBlock')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_EraseImmediateBlock')
	.sect	'.debug_line'
.L64:
	.word	.L184-.L183
.L183:
	.half	3
	.word	.L186-.L185
.L185:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L186:
	.byte	5,31,7,0,5,2
	.word	.L34
	.byte	3,168,2,1,5,43,9
	.half	.L156-.L34
	.byte	1,5,64,9
	.half	.L187-.L156
	.byte	1,5,1,9
	.half	.L66-.L187
	.byte	3,14,0,1,1
.L184:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_EraseImmediateBlock')
	.sect	'.debug_ranges'
.L65:
	.word	-1,.L34,0,.L66-.L34,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_Cancel')
	.sect	'.debug_info'
.L67:
	.word	252
	.half	3
	.word	.L68
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L70,.L69
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_Cancel',0,1,196,2,24,1,1,1
	.word	.L36,.L126,.L35
	.byte	4
	.byte	'DeviceIndex',0,1,196,2,43
	.word	.L102,.L127
	.byte	5
	.word	.L36,.L126
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_Cancel')
	.sect	'.debug_abbrev'
.L68:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_Cancel')
	.sect	'.debug_line'
.L69:
	.word	.L189-.L188
.L188:
	.half	3
	.word	.L191-.L190
.L190:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L191:
	.byte	5,22,7,0,5,2
	.word	.L36
	.byte	3,211,2,1,5,34,9
	.half	.L192-.L36
	.byte	1,5,41,9
	.half	.L193-.L192
	.byte	1,5,1,7,9
	.half	.L71-.L193
	.byte	3,13,0,1,1
.L189:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_Cancel')
	.sect	'.debug_ranges'
.L70:
	.word	-1,.L36,0,.L71-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_GetStatus')
	.sect	'.debug_info'
.L72:
	.word	341
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L75,.L74
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_GetStatus',0,1,244,2,36
	.word	.L128
	.byte	1,1,1
	.word	.L38,.L129,.L37
	.byte	4
	.byte	'DeviceIndex',0,1,244,2,58
	.word	.L102,.L130
	.byte	5
	.word	.L38,.L129
	.byte	6
	.byte	'retVal',0,1,248,2,20
	.word	.L128,.L131
	.byte	6
	.byte	'MemIf_LoopCounter',0,1,250,2,9
	.word	.L102,.L132
	.byte	6
	.byte	'MemIf_StatusMask',0,1,251,2,9
	.word	.L102,.L133
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_GetStatus')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_GetStatus')
	.sect	'.debug_line'
.L74:
	.word	.L195-.L194
.L194:
	.half	3
	.word	.L197-.L196
.L196:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L197:
	.byte	5,26,7,0,5,2
	.word	.L38
	.byte	3,250,2,1,5,60,3,24,1,5,9,9
	.half	.L198-.L38
	.byte	3,121,1,5,5,9
	.half	.L199-.L198
	.byte	1,5,30,7,9
	.half	.L200-.L199
	.byte	3,4,1,5,55,1,5,76,9
	.half	.L201-.L200
	.byte	1,5,38,9
	.half	.L9-.L201
	.byte	3,3,1,5,95,1,5,105,9
	.half	.L202-.L9
	.byte	1,5,49,9
	.half	.L203-.L202
	.byte	1,5,95,3,125,1,5,29,9
	.half	.L204-.L203
	.byte	3,3,1,5,95,9
	.half	.L205-.L204
	.byte	3,125,1,5,26,9
	.half	.L160-.L205
	.byte	3,3,1,5,55,9
	.half	.L8-.L160
	.byte	3,125,1,5,76,9
	.half	.L206-.L8
	.byte	1,5,44,7,9
	.half	.L207-.L206
	.byte	3,7,1,5,7,9
	.half	.L208-.L207
	.byte	1,5,16,7,9
	.half	.L209-.L208
	.byte	3,2,1,5,1,3,38,1,5,49,7,9
	.half	.L10-.L209
	.byte	3,93,1,5,12,9
	.half	.L210-.L10
	.byte	1,5,16,7,9
	.half	.L211-.L210
	.byte	3,2,1,5,1,3,33,1,5,49,7,9
	.half	.L12-.L211
	.byte	3,98,1,5,12,9
	.half	.L212-.L12
	.byte	1,5,16,7,9
	.half	.L213-.L212
	.byte	3,2,1,5,1,3,28,1,5,16,7,9
	.half	.L14-.L213
	.byte	3,105,1,5,1,3,23,1,5,33,7,9
	.half	.L7-.L14
	.byte	3,112,1,5,32,9
	.half	.L161-.L7
	.byte	1,5,45,9
	.half	.L214-.L161
	.byte	1,5,55,9
	.half	.L215-.L214
	.byte	1,5,1,7,9
	.half	.L76-.L215
	.byte	3,16,0,1,1
.L195:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_GetStatus')
	.sect	'.debug_ranges'
.L75:
	.word	-1,.L38,0,.L76-.L38,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_GetJobResult')
	.sect	'.debug_info'
.L77:
	.word	283
	.half	3
	.word	.L78
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L80,.L79
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_GetJobResult',0,1,204,3,39
	.word	.L134
	.byte	1,1,1
	.word	.L40,.L135,.L39
	.byte	4
	.byte	'DeviceIndex',0,1,204,3,64
	.word	.L102,.L136
	.byte	5
	.word	.L40,.L135
	.byte	6
	.byte	'retVal',0,1,208,3,23
	.word	.L134,.L137
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_GetJobResult')
	.sect	'.debug_abbrev'
.L78:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_GetJobResult')
	.sect	'.debug_line'
.L79:
	.word	.L217-.L216
.L216:
	.half	3
	.word	.L219-.L218
.L218:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L219:
	.byte	5,31,7,0,5,2
	.word	.L40
	.byte	3,221,3,1,5,43,9
	.half	.L220-.L40
	.byte	1,5,56,9
	.half	.L221-.L220
	.byte	1,5,1,7,9
	.half	.L81-.L221
	.byte	3,14,0,1,1
.L217:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_GetJobResult')
	.sect	'.debug_ranges'
.L80:
	.word	-1,.L40,0,.L81-.L40,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_SetMode')
	.sect	'.debug_info'
.L82:
	.word	278
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L85,.L84
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_SetMode',0,1,169,4,24,1,1,1
	.word	.L42,.L138,.L41
	.byte	4
	.byte	'Mode',0,1,169,4,53
	.word	.L139,.L140
	.byte	5
	.word	.L42,.L138
	.byte	6
	.byte	'MemIf_LoopCounter',0,1,171,4,9
	.word	.L102,.L141
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_SetMode')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_SetMode')
	.sect	'.debug_line'
.L84:
	.word	.L223-.L222
.L222:
	.half	3
	.word	.L225-.L224
.L224:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L225:
	.byte	5,24,7,0,5,2
	.word	.L42
	.byte	3,168,4,1,5,25,9
	.half	.L162-.L42
	.byte	3,4,1,5,5,3,2,1,5,50,9
	.half	.L226-.L162
	.byte	3,126,1,5,71,9
	.half	.L227-.L226
	.byte	1,5,40,9
	.half	.L20-.L227
	.byte	3,2,1,5,49,9
	.half	.L228-.L20
	.byte	1,5,90,9
	.half	.L229-.L228
	.byte	3,126,1,5,50,9
	.half	.L19-.L229
	.byte	1,5,71,9
	.half	.L230-.L19
	.byte	1,5,1,7,9
	.half	.L231-.L230
	.byte	3,4,1,7,9
	.half	.L86-.L231
	.byte	0,1,1
.L223:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_SetMode')
	.sect	'.debug_ranges'
.L85:
	.word	-1,.L42,0,.L86-.L42,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_IsBitSet')
	.sect	'.debug_info'
.L87:
	.word	267
	.half	3
	.word	.L88
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L90,.L89
	.byte	2
	.word	.L43
	.byte	3
	.byte	'MemIf_IsBitSet',0,1,125,54
	.word	.L102
	.byte	1,1
	.word	.L22,.L142,.L21
	.byte	4
	.byte	'Mask',0,1,125,75
	.word	.L102,.L143
	.byte	4
	.byte	'Status',0,1,125,98
	.word	.L128,.L144
	.byte	5
	.word	.L22,.L142
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_IsBitSet')
	.sect	'.debug_abbrev'
.L88:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_IsBitSet')
	.sect	'.debug_line'
.L89:
	.word	.L233-.L232
.L232:
	.half	3
	.word	.L235-.L234
.L234:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L235:
	.byte	5,27,7,0,5,2
	.word	.L22
	.byte	3,254,0,1,5,30,9
	.half	.L236-.L22
	.byte	1,5,17,9
	.half	.L237-.L236
	.byte	1,5,1,9
	.half	.L145-.L237
	.byte	3,1,1,7,9
	.half	.L91-.L145
	.byte	0,1,1
.L233:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_IsBitSet')
	.sect	'.debug_ranges'
.L90:
	.word	-1,.L22,0,.L91-.L22,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L92:
	.word	213
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L95,.L94
	.byte	2
	.word	.L43
	.byte	3
	.byte	'.cocofun_1',0,1,144,1,34,1
	.word	.L26,.L96,.L25
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L94:
	.word	.L239-.L238
.L238:
	.half	3
	.word	.L241-.L240
.L240:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L241:
	.byte	5,31,7,0,5,2
	.word	.L26
	.byte	3,161,1,1,5,14,1,5,30,9
	.half	.L146-.L26
	.byte	1,9
	.half	.L96-.L146
	.byte	0,1,1,5,31,0,5,2
	.word	.L26
	.byte	3,206,1,1,5,14,3,83,1,5,30,9
	.half	.L146-.L26
	.byte	3,45,1,9
	.half	.L242-.L146
	.byte	3,83,1,7,9
	.half	.L96-.L242
	.byte	0,1,1,5,31,0,5,2
	.word	.L26
	.byte	3,251,1,1,5,14,3,166,127,1,5,30,9
	.half	.L146-.L26
	.byte	3,218,0,1,9
	.half	.L242-.L146
	.byte	3,166,127,1,7,9
	.half	.L96-.L242
	.byte	0,1,1,5,31,0,5,2
	.word	.L26
	.byte	3,168,2,1,5,14,3,249,126,1,5,30,9
	.half	.L146-.L26
	.byte	3,135,1,1,9
	.half	.L242-.L146
	.byte	3,249,126,1,7,9
	.half	.L96-.L242
	.byte	0,1,1,5,22,0,5,2
	.word	.L26
	.byte	3,211,2,1,5,14,3,206,126,1,5,21,9
	.half	.L146-.L26
	.byte	3,178,1,1,5,30,9
	.half	.L242-.L146
	.byte	3,206,126,1,7,9
	.half	.L96-.L242
	.byte	0,1,1,5,31,0,5,2
	.word	.L26
	.byte	3,221,3,1,5,14,3,196,125,1,5,30,9
	.half	.L146-.L26
	.byte	3,188,2,1,9
	.half	.L242-.L146
	.byte	3,196,125,1,7,9
	.half	.L96-.L242
	.byte	0,1,1
.L239:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L95:
	.word	-1,.L26,0,.L96-.L26,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L97:
	.word	213
	.half	3
	.word	.L98
	.byte	4,1
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L100,.L99
	.byte	2
	.word	.L43
	.byte	3
	.byte	'.cocofun_2',0,1,144,1,34,1
	.word	.L28,.L101,.L27
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L98:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L99:
	.word	.L244-.L243
.L243:
	.half	3
	.word	.L246-.L245
.L245:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf\\MemIf.c',0,0,0,0,0
.L246:
	.byte	5,14,7,0,5,2
	.word	.L28
	.byte	3,161,1,1,9
	.half	.L101-.L28
	.byte	0,1,1,5,14,0,5,2
	.word	.L28
	.byte	3,206,1,1,9
	.half	.L247-.L28
	.byte	3,83,1,7,9
	.half	.L101-.L247
	.byte	0,1,1,5,14,0,5,2
	.word	.L28
	.byte	3,251,1,1,9
	.half	.L247-.L28
	.byte	3,166,127,1,7,9
	.half	.L101-.L247
	.byte	0,1,1,5,14,0,5,2
	.word	.L28
	.byte	3,168,2,1,9
	.half	.L247-.L28
	.byte	3,249,126,1,7,9
	.half	.L101-.L247
	.byte	0,1,1,5,5,0,5,2
	.word	.L28
	.byte	3,211,2,1,5,14,9
	.half	.L247-.L28
	.byte	3,206,126,1,7,9
	.half	.L101-.L247
	.byte	0,1,1,5,14,0,5,2
	.word	.L28
	.byte	3,221,3,1,9
	.half	.L247-.L28
	.byte	3,196,125,1,7,9
	.half	.L101-.L247
	.byte	0,1,1,5,60,0,5,2
	.word	.L28
	.byte	3,146,3,1,5,14,9
	.half	.L247-.L28
	.byte	3,143,126,1,7,9
	.half	.L101-.L247
	.byte	0,1,1,5,5,0,5,2
	.word	.L28
	.byte	3,174,4,1,5,14,9
	.half	.L247-.L28
	.byte	3,243,124,1,7,9
	.half	.L101-.L247
	.byte	0,1,1
.L244:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L100:
	.word	-1,.L28,0,.L101-.L28,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L25:
	.word	-1,.L26,0,.L96-.L26
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L27:
	.word	-1,.L28,0,.L101-.L28
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_Cancel')
	.sect	'.debug_loc'
.L127:
	.word	-1,.L36,.L26-.L36,.L146-.L36
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L35:
	.word	-1,.L36,0,.L126-.L36
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_EraseImmediateBlock')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L34,.L28-.L34,.L101-.L34
	.half	5
	.byte	144,34,157,32,32
	.word	.L26-.L34,.L96-.L34
	.half	5
	.byte	144,34,157,32,32
	.word	.L156-.L34,.L122-.L34
	.half	5
	.byte	144,34,157,32,32
	.word	.L157-.L34,.L122-.L34
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L123:
	.word	-1,.L34,.L26-.L34,.L146-.L34
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L33:
	.word	-1,.L34,0,.L122-.L34
	.half	2
	.byte	138,0
	.word	0,0
.L125:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_GetJobResult')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L40,.L26-.L40,.L146-.L40
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L39:
	.word	-1,.L40,0,.L135-.L40
	.half	2
	.byte	138,0
	.word	0,0
.L137:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_GetStatus')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L38,.L147-.L38,.L101-.L38
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L9-.L38
	.half	5
	.byte	144,34,157,32,0
	.word	.L7-.L38,.L161-.L38
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L37:
	.word	-1,.L38,0,.L129-.L38
	.half	2
	.byte	138,0
	.word	0,0
.L132:
	.word	-1,.L38,.L158-.L38,.L159-.L38
	.half	5
	.byte	144,39,157,32,32
	.word	.L160-.L38,.L7-.L38
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L133:
	.word	-1,.L38,.L147-.L38,.L101-.L38
	.half	5
	.byte	144,36,157,32,0
	.word	0,.L129-.L38
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L131:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_InvalidateBlock')
	.sect	'.debug_loc'
.L120:
	.word	-1,.L32,.L28-.L32,.L101-.L32
	.half	5
	.byte	144,34,157,32,32
	.word	.L26-.L32,.L96-.L32
	.half	5
	.byte	144,34,157,32,32
	.word	.L154-.L32,.L118-.L32
	.half	5
	.byte	144,34,157,32,32
	.word	.L155-.L32,.L118-.L32
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L119:
	.word	-1,.L32,.L26-.L32,.L146-.L32
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L31:
	.word	-1,.L32,0,.L118-.L32
	.half	2
	.byte	138,0
	.word	0,0
.L121:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_IsBitSet')
	.sect	'.debug_loc'
.L143:
	.word	-1,.L22,0,.L145-.L22
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L21:
	.word	-1,.L22,0,.L142-.L22
	.half	2
	.byte	138,0
	.word	0,0
.L144:
	.word	-1,.L22,0,.L142-.L22
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_Read')
	.sect	'.debug_loc'
.L106:
	.word	-1,.L24,.L147-.L24,.L101-.L24
	.half	5
	.byte	144,34,157,32,32
	.word	.L26-.L24,.L96-.L24
	.half	5
	.byte	144,34,157,32,32
	.word	.L148-.L24,.L149-.L24
	.half	5
	.byte	144,34,157,32,32
	.word	.L151-.L24,.L103-.L24
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L107:
	.word	-1,.L24,.L147-.L24,.L101-.L24
	.half	5
	.byte	144,35,157,32,0
	.word	.L26-.L24,.L96-.L24
	.half	5
	.byte	144,35,157,32,0
	.word	.L148-.L24,.L150-.L24
	.half	5
	.byte	144,35,157,32,0
	.word	.L149-.L24,.L103-.L24
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L109:
	.word	-1,.L24,.L147-.L24,.L101-.L24
	.half	1
	.byte	100
	.word	.L26-.L24,.L96-.L24
	.half	1
	.byte	100
	.word	.L148-.L24,.L103-.L24
	.half	1
	.byte	100
	.word	0,0
.L104:
	.word	-1,.L24,.L26-.L24,.L146-.L24
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L110:
	.word	-1,.L24,.L147-.L24,.L101-.L24
	.half	5
	.byte	144,35,157,32,32
	.word	.L26-.L24,.L96-.L24
	.half	5
	.byte	144,35,157,32,32
	.word	.L148-.L24,.L103-.L24
	.half	5
	.byte	144,35,157,32,32
	.word	.L150-.L24,.L103-.L24
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L23:
	.word	-1,.L24,0,.L103-.L24
	.half	2
	.byte	138,0
	.word	0,0
.L112:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_SetMode')
	.sect	'.debug_loc'
.L141:
	.word	-1,.L42,.L147-.L42,.L101-.L42
	.half	5
	.byte	144,39,157,32,32
	.word	.L162-.L42,.L163-.L42
	.half	5
	.byte	144,39,157,32,32
	.word	.L19-.L42,.L138-.L42
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L41:
	.word	-1,.L42,0,.L138-.L42
	.half	2
	.byte	138,0
	.word	0,0
.L140:
	.word	-1,.L42,.L147-.L42,.L101-.L42
	.half	5
	.byte	144,36,157,32,0
	.word	.L147-.L42,.L101-.L42
	.half	5
	.byte	144,34,157,32,0
	.word	.L162-.L42,.L138-.L42
	.half	5
	.byte	144,36,157,32,0
	.word	0,.L20-.L42
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_Write')
	.sect	'.debug_loc'
.L115:
	.word	-1,.L30,.L28-.L30,.L101-.L30
	.half	5
	.byte	144,34,157,32,32
	.word	.L26-.L30,.L96-.L30
	.half	5
	.byte	144,34,157,32,32
	.word	.L152-.L30,.L113-.L30
	.half	5
	.byte	144,34,157,32,32
	.word	.L153-.L30,.L113-.L30
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L116:
	.word	-1,.L30,.L28-.L30,.L101-.L30
	.half	1
	.byte	100
	.word	.L26-.L30,.L96-.L30
	.half	1
	.byte	100
	.word	.L152-.L30,.L113-.L30
	.half	1
	.byte	100
	.word	0,0
.L114:
	.word	-1,.L30,.L26-.L30,.L146-.L30
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L29:
	.word	-1,.L30,0,.L113-.L30
	.half	2
	.byte	138,0
	.word	0,0
.L117:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L248:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('MemIf_IsBitSet')
	.sect	'.debug_frame'
	.word	24
	.word	.L248,.L22,.L142-.L22
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('MemIf_Read')
	.sect	'.debug_frame'
	.word	12
	.word	.L248,.L24,.L103-.L24
	.sdecl	'.debug_frame',debug,cluster('MemIf_Write')
	.sect	'.debug_frame'
	.word	12
	.word	.L248,.L30,.L113-.L30
	.sdecl	'.debug_frame',debug,cluster('MemIf_InvalidateBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L248,.L32,.L118-.L32
	.sdecl	'.debug_frame',debug,cluster('MemIf_EraseImmediateBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L248,.L34,.L122-.L34
	.sdecl	'.debug_frame',debug,cluster('MemIf_Cancel')
	.sect	'.debug_frame'
	.word	12
	.word	.L248,.L36,.L126-.L36
	.sdecl	'.debug_frame',debug,cluster('MemIf_GetStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L248,.L38,.L129-.L38
	.sdecl	'.debug_frame',debug,cluster('MemIf_GetJobResult')
	.sect	'.debug_frame'
	.word	12
	.word	.L248,.L40,.L135-.L40
	.sdecl	'.debug_frame',debug,cluster('MemIf_SetMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L248,.L42,.L138-.L42
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L249:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L249,.L26,.L96-.L26
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L249,.L28,.L101-.L28
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\eeprom\MemIf\MemIf.c	   562  
; ..\eeprom\MemIf\MemIf.c	   563  
; ..\eeprom\MemIf\MemIf.c	   564  #define MEMIF_STOP_SEC_CODE
; ..\eeprom\MemIf\MemIf.c	   565    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\MemIf\MemIf.c	   566  
; ..\eeprom\MemIf\MemIf.c	   567  /* START_COVERAGE_JUSTIFICATION
; ..\eeprom\MemIf\MemIf.c	   568      \ID COV_MEMIF_COMPATIBILITY
; ..\eeprom\MemIf\MemIf.c	   569        \ACCEPT XF
; ..\eeprom\MemIf\MemIf.c	   570        \ACCEPT TX
; ..\eeprom\MemIf\MemIf.c	   571        \REASON [COV_MSR_COMPATIBILITY]
; ..\eeprom\MemIf\MemIf.c	   572  END_COVERAGE_JUSTIFICATION */
; ..\eeprom\MemIf\MemIf.c	   573  
; ..\eeprom\MemIf\MemIf.c	   574  /* SBSW_JUSTIFICATION_BEGIN
; ..\eeprom\MemIf\MemIf.c	   575     \ID SBSW_MEMIF_01
; ..\eeprom\MemIf\MemIf.c	   576       \DESCRIPTION The function Ea_GetVersionInfo writes to the object referenced by parameter VersionInfoPtr
; ..\eeprom\MemIf\MemIf.c	   577       \COUNTERMEASURE \N The caller ensures that the pointers passed to the parameters VersionInfoPtr is valid.
; ..\eeprom\MemIf\MemIf.c	   578     \ID SBSW_MEMIF_02
; ..\eeprom\MemIf\MemIf.c	   579        \DESCRIPTION Function pointer from function pointer structure is called
; ..\eeprom\MemIf\MemIf.c	   580        \COUNTERMEASURE \N  The compiler performs type check and therefore assures that valid function pointer is called.
; ..\eeprom\MemIf\MemIf.c	   581                            Reference of pointer structure is retrieved by array access. Used index DeviceIndex is checked by DET for being valid.
; ..\eeprom\MemIf\MemIf.c	   582     \ID SBSW_MEMIF_03
; ..\eeprom\MemIf\MemIf.c	   583        \DESCRIPTION Implicit function call of MEMIF_DUMMY_STATEMENT
; ..\eeprom\MemIf\MemIf.c	   584        \COUNTERMEASURE \N Macro MEMIF_DUMMY_STATEMENT is defined empty. It's used to prevent the compiler from reporting warnings due to unused parameters.
; ..\eeprom\MemIf\MemIf.c	   585  
; ..\eeprom\MemIf\MemIf.c	   586      \ID SBSW_MEMIF_04
; ..\eeprom\MemIf\MemIf.c	   587        \DESCRIPTION Fee or Ea function is called
; ..\eeprom\MemIf\MemIf.c	   588        \COUNTERMEASURE \N  The compiler performs type check and therefore assures that valid function is called.
; ..\eeprom\MemIf\MemIf.c	   589  SBSW_JUSTIFICATION_END */
; ..\eeprom\MemIf\MemIf.c	   590  
; ..\eeprom\MemIf\MemIf.c	   591  /**********************************************************************************************************************
; ..\eeprom\MemIf\MemIf.c	   592   *  END OF FILE: MemIf.c
; ..\eeprom\MemIf\MemIf.c	   593   *********************************************************************************************************************/

	; Module end
