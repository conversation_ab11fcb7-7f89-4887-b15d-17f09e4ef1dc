	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc27952a -c99 --dep-file=mcal_src\\.Mcu_Dma.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Mcu_Dma.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Mcu_Dma.src ..\\mcal_src\\Mcu_Dma.c"
	.compiler_name		"ctc"
	.name	"Mcu_Dma"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Mcu_lDmaInit')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Mcu_lDmaInit

; ..\mcal_src\Mcu_Dma.c	     1  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	     2  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_src\Mcu_Dma.c	     4  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	     5  ** All rights reserved.                                                       **
; ..\mcal_src\Mcu_Dma.c	     6  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Mcu_Dma.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Mcu_Dma.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Mcu_Dma.c	    10  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    11  ********************************************************************************
; ..\mcal_src\Mcu_Dma.c	    12  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    13  **  $FILENAME   : Mcu_Dma.c $                                                **
; ..\mcal_src\Mcu_Dma.c	    14  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    15  **  $CC VERSION : \main\19 $                                                 **
; ..\mcal_src\Mcu_Dma.c	    16  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    17  **  $DATE       : 2016-05-06 $                                               **
; ..\mcal_src\Mcu_Dma.c	    18  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                      **
; ..\mcal_src\Mcu_Dma.c	    20  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    21  **  VENDOR      : Infineon Technologies                                       **
; ..\mcal_src\Mcu_Dma.c	    22  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    23  **  DESCRIPTION : This file contains basic initialization of DMA module.      **
; ..\mcal_src\Mcu_Dma.c	    24  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    25  **  MAY BE CHANGED BY USER [yes/no]: No                                       **
; ..\mcal_src\Mcu_Dma.c	    26  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    27  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    28  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	    29  **                      Includes                                              **
; ..\mcal_src\Mcu_Dma.c	    30  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    31    /* Inclusion of controller sfr file */
; ..\mcal_src\Mcu_Dma.c	    32  #include "Mcu.h"
; ..\mcal_src\Mcu_Dma.c	    33  #include "Mcu_Local.h"
; ..\mcal_src\Mcu_Dma.c	    34  #include "Mcal_DmaLib.h"
; ..\mcal_src\Mcu_Dma.c	    35  
; ..\mcal_src\Mcu_Dma.c	    36  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	    37  **                      Private Macro Definitions                             **
; ..\mcal_src\Mcu_Dma.c	    38  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    39  
; ..\mcal_src\Mcu_Dma.c	    40  #define MCU_DMA_CLC_DISR_CLEARMASK            0xFFFFFFFEU
; ..\mcal_src\Mcu_Dma.c	    41  #define MCU_DMA_CLC_DISR_CLEARMASK1           0xFFFFFFFEU
; ..\mcal_src\Mcu_Dma.c	    42  #define MCU_DMA_CLC_DISR_SETMASK              0x00000001U
; ..\mcal_src\Mcu_Dma.c	    43  #define MCU_DMA_CLC_DISS_CLEARMASK            0x00000002U
; ..\mcal_src\Mcu_Dma.c	    44  #define MCU_DMA_CLC_DISS_BITPOS               1U
; ..\mcal_src\Mcu_Dma.c	    45  
; ..\mcal_src\Mcu_Dma.c	    46  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	    47  **                      Global Constant Definitions                           **
; ..\mcal_src\Mcu_Dma.c	    48  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    49  
; ..\mcal_src\Mcu_Dma.c	    50  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	    51  **                      Global Variable Definitions                           **
; ..\mcal_src\Mcu_Dma.c	    52  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    53  #ifdef IFX_MCU_DEBUG03
; ..\mcal_src\Mcu_Dma.c	    54  extern volatile uint32 TestMcu_DebugMask03;
; ..\mcal_src\Mcu_Dma.c	    55  #endif
; ..\mcal_src\Mcu_Dma.c	    56  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	    57  **                      Private Constant Definitions                          **
; ..\mcal_src\Mcu_Dma.c	    58  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    59  
; ..\mcal_src\Mcu_Dma.c	    60  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	    61  **                      Private Variable Definitions                          **
; ..\mcal_src\Mcu_Dma.c	    62  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    63  
; ..\mcal_src\Mcu_Dma.c	    64  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	    65  **                      Global Function Definitions                           **
; ..\mcal_src\Mcu_Dma.c	    66  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    67  
; ..\mcal_src\Mcu_Dma.c	    68  
; ..\mcal_src\Mcu_Dma.c	    69  /*Memory Map of the MCU Code*/
; ..\mcal_src\Mcu_Dma.c	    70  #define MCU_START_SEC_CODE
; ..\mcal_src\Mcu_Dma.c	    71  #include "MemMap.h"
; ..\mcal_src\Mcu_Dma.c	    72  
; ..\mcal_src\Mcu_Dma.c	    73  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	    74  ** Syntax : Std_ReturnType Mcu_lDmaInit (void)                                **
; ..\mcal_src\Mcu_Dma.c	    75  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    76  ** Service ID:    None                                                        **
; ..\mcal_src\Mcu_Dma.c	    77  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    78  ** Sync/Async:    Synchronous                                                 **
; ..\mcal_src\Mcu_Dma.c	    79  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    80  ** Reentrancy:    Non-reentrant                                               **
; ..\mcal_src\Mcu_Dma.c	    81  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    82  ** Parameters (in):   None                                                    **
; ..\mcal_src\Mcu_Dma.c	    83  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    84  ** Parameters (out):  None                                                    **
; ..\mcal_src\Mcu_Dma.c	    85  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    86  ** Return value: E_OK - CLC is enabled                                        **
; ..\mcal_src\Mcu_Dma.c	    87  **               E_NOT_OK - Enabling of CLC failed                            **
; ..\mcal_src\Mcu_Dma.c	    88  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    89  ** Description :  This service shall initialize DMA clock for other drivers   **
; ..\mcal_src\Mcu_Dma.c	    90  **                to use DMA if DMA Complex driver is not used.               **
; ..\mcal_src\Mcu_Dma.c	    91  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	    92  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	    93  Std_ReturnType Mcu_lDmaInit(void)
; Function Mcu_lDmaInit
.L5:
Mcu_lDmaInit:	.type	func

; ..\mcal_src\Mcu_Dma.c	    94  {
; ..\mcal_src\Mcu_Dma.c	    95    Std_ReturnType RetVal = E_OK;
; ..\mcal_src\Mcu_Dma.c	    96    uint32 Readback;
; ..\mcal_src\Mcu_Dma.c	    97  
; ..\mcal_src\Mcu_Dma.c	    98    MCU_SFR_INIT_RESETENDINIT();
	mov	d8,#0
	call	Mcal_ResetENDINIT
.L27:

; ..\mcal_src\Mcu_Dma.c	    99    /* Enable the DMA clock */
; ..\mcal_src\Mcu_Dma.c	   100    MCU_SFR_INIT_MODIFY32(MODULE_DMA.CLC.U,MCU_DMA_CLC_DISR_CLEARMASK,0U)
	movh.a	a15,#61441
	ld.w	d15,[a15]
	insert	d15,d15,#0,#0,#1
	st.w	[a15],d15
.L28:

; ..\mcal_src\Mcu_Dma.c	   101    MCU_SFR_INIT_SETENDINIT();
	call	Mcal_SetENDINIT
.L44:

; ..\mcal_src\Mcu_Dma.c	   102  
; ..\mcal_src\Mcu_Dma.c	   103    Readback = (uint32)((MCU_SFR_INIT_USER_MODE_READ32(MODULE_DMA.CLC.U)\ 
	ld.w	d15,[a15]
.L34:

; ..\mcal_src\Mcu_Dma.c	   104                          & MCU_DMA_CLC_DISS_CLEARMASK)>>MCU_DMA_CLC_DISS_BITPOS);
; ..\mcal_src\Mcu_Dma.c	   105    #ifdef IFX_MCU_DEBUG03
; ..\mcal_src\Mcu_Dma.c	   106    Readback |= TestMcu_DebugMask03;
; ..\mcal_src\Mcu_Dma.c	   107    #endif
; ..\mcal_src\Mcu_Dma.c	   108  
; ..\mcal_src\Mcu_Dma.c	   109    if(Readback == 1U)
	jz.t	d15:1,.L2
.L45:

; ..\mcal_src\Mcu_Dma.c	   110    {
; ..\mcal_src\Mcu_Dma.c	   111      RetVal = E_NOT_OK;
	mov	d8,#1
.L2:

; ..\mcal_src\Mcu_Dma.c	   112    }
; ..\mcal_src\Mcu_Dma.c	   113  
; ..\mcal_src\Mcu_Dma.c	   114  return(RetVal);
; ..\mcal_src\Mcu_Dma.c	   115  }/*End of Mcu_lDmaInit()*/
	mov	d2,d8
	ret
.L23:
	
__Mcu_lDmaInit_function_end:
	.size	Mcu_lDmaInit,__Mcu_lDmaInit_function_end-Mcu_lDmaInit
.L16:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Mcu_lDmaDeInit')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Mcu_lDmaDeInit

; ..\mcal_src\Mcu_Dma.c	   116  
; ..\mcal_src\Mcu_Dma.c	   117  #if (MCU_DEINIT_API == STD_ON)
; ..\mcal_src\Mcu_Dma.c	   118  
; ..\mcal_src\Mcu_Dma.c	   119  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	   120  ** Syntax : void Mcu_lDmaDeInit (void)                                        **
; ..\mcal_src\Mcu_Dma.c	   121  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   122  ** Service ID:    None                                                        **
; ..\mcal_src\Mcu_Dma.c	   123  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   124  ** Sync/Async:    Synchronous                                                 **
; ..\mcal_src\Mcu_Dma.c	   125  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   126  ** Reentrancy:    Non-reentrant                                               **
; ..\mcal_src\Mcu_Dma.c	   127  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   128  ** Parameters (in):   None                                                    **
; ..\mcal_src\Mcu_Dma.c	   129  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   130  ** Parameters (out):  None                                                    **
; ..\mcal_src\Mcu_Dma.c	   131  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   132  ** Return value:      None                                                    **
; ..\mcal_src\Mcu_Dma.c	   133  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   134  ** Description :  This service shall de-initialize DMA clock                  **
; ..\mcal_src\Mcu_Dma.c	   135  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   136  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	   137  void Mcu_lDmaDeInit(void)
; Function Mcu_lDmaDeInit
.L7:
Mcu_lDmaDeInit:	.type	func

; ..\mcal_src\Mcu_Dma.c	   138  {
; ..\mcal_src\Mcu_Dma.c	   139    /* Disable the DMA clock */
; ..\mcal_src\Mcu_Dma.c	   140    MCU_SFR_DEINIT_RESETENDINIT();
	call	Mcal_ResetENDINIT
.L31:

; ..\mcal_src\Mcu_Dma.c	   141    MCU_SFR_DEINIT_MODIFY32(MODULE_DMA.CLC.U,MCU_DMA_CLC_DISR_CLEARMASK1,\ 
	movh.a	a15,#61441
	ld.w	d15,[a15]
.L35:
	or	d15,#1
	st.w	[a15],d15
.L32:

; ..\mcal_src\Mcu_Dma.c	   142                                                       MCU_DMA_CLC_DISR_SETMASK)
; ..\mcal_src\Mcu_Dma.c	   143    MCU_SFR_DEINIT_SETENDINIT();
	j	Mcal_SetENDINIT
.L30:
	
__Mcu_lDmaDeInit_function_end:
	.size	Mcu_lDmaDeInit,__Mcu_lDmaDeInit_function_end-Mcu_lDmaDeInit
.L21:
	; End of function
	
	.calls	'Mcu_lDmaInit','Mcal_ResetENDINIT'
	.calls	'Mcu_lDmaInit','Mcal_SetENDINIT'
	.calls	'Mcu_lDmaDeInit','Mcal_ResetENDINIT'
	.calls	'Mcu_lDmaDeInit','Mcal_SetENDINIT'
	.calls	'Mcu_lDmaInit','',0
	.extern	Mcal_ResetENDINIT
	.extern	Mcal_SetENDINIT
	.calls	'Mcu_lDmaDeInit','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L9:
	.word	94993
	.half	3
	.word	.L10
	.byte	4
.L8:
	.byte	1
	.byte	'..\\mcal_src\\Mcu_Dma.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L11
	.byte	2
	.byte	'Mcal_ResetENDINIT',0,1,119,13,1,1,1,1,2
	.byte	'Mcal_SetENDINIT',0,1,146,1,13,1,1,1,1
.L22:
	.byte	3
	.byte	'unsigned char',0,1,8
.L25:
	.byte	3
	.byte	'unsigned long int',0,4,7,4
	.byte	'void',0,5
	.word	265
	.byte	6
	.byte	'__prof_adm',0,2,1,1
	.word	271
	.byte	7,1,5
	.word	295
	.byte	6
	.byte	'__codeptr',0,2,1,1
	.word	297
	.byte	6
	.byte	'uint8',0,3,90,29
	.word	227
	.byte	3
	.byte	'unsigned short int',0,2,7,6
	.byte	'uint16',0,3,92,29
	.word	334
	.byte	6
	.byte	'uint32',0,3,94,29
	.word	244
	.byte	6
	.byte	'boolean',0,3,105,29
	.word	227
	.byte	6
	.byte	'Std_ReturnType',0,4,113,15
	.word	227
	.byte	3
	.byte	'unsigned int',0,4,7,6
	.byte	'unsigned_int',0,5,121,22
	.word	425
	.byte	8
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,9
	.byte	'EN0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	227
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	227
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	462
	.byte	8
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,9
	.byte	'reserved_0',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	1019
	.byte	8
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,9
	.byte	'STM0DIS',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'STM1DIS',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'STM2DIS',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,4
	.word	425
	.byte	29,0,2,35,2,0,6
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	1096
	.byte	8
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'BAUD2DIV',0,1
	.word	227
	.byte	4,0,2,35,0,9
	.byte	'SRIDIV',0,1
	.word	227
	.byte	4,4,2,35,1,9
	.byte	'LPDIV',0,1
	.word	227
	.byte	4,0,2,35,1,9
	.byte	'SPBDIV',0,1
	.word	227
	.byte	4,4,2,35,2,9
	.byte	'FSI2DIV',0,1
	.word	227
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	227
	.byte	2,0,2,35,2,9
	.byte	'FSIDIV',0,1
	.word	227
	.byte	2,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	227
	.byte	2,4,2,35,3,9
	.byte	'CLKSEL',0,1
	.word	227
	.byte	2,2,2,35,3,9
	.byte	'UP',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	1232
	.byte	8
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,9
	.byte	'CANDIV',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'ERAYDIV',0,1
	.word	227
	.byte	4,0,2,35,0,9
	.byte	'STMDIV',0,1
	.word	227
	.byte	4,4,2,35,1,9
	.byte	'GTMDIV',0,1
	.word	227
	.byte	4,0,2,35,1,9
	.byte	'ETHDIV',0,1
	.word	227
	.byte	4,4,2,35,2,9
	.byte	'ASCLINFDIV',0,1
	.word	227
	.byte	4,0,2,35,2,9
	.byte	'ASCLINSDIV',0,1
	.word	227
	.byte	4,4,2,35,3,9
	.byte	'INSEL',0,1
	.word	227
	.byte	2,2,2,35,3,9
	.byte	'UP',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	1514
	.byte	8
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,9
	.byte	'BBBDIV',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	425
	.byte	26,2,2,35,2,9
	.byte	'UP',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	1752
	.byte	8
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,9
	.byte	'PLLDIV',0,1
	.word	227
	.byte	6,2,2,35,0,9
	.byte	'PLLSEL',0,1
	.word	227
	.byte	2,0,2,35,0,9
	.byte	'PLLERAYDIV',0,1
	.word	227
	.byte	6,2,2,35,1,9
	.byte	'PLLERAYSEL',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'SRIDIV',0,1
	.word	227
	.byte	6,2,2,35,2,9
	.byte	'SRISEL',0,1
	.word	227
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	6,2,2,35,3,9
	.byte	'UP',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,149,1,3
	.word	1880
	.byte	8
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,152,1,16,4,9
	.byte	'SPBDIV',0,1
	.word	227
	.byte	6,2,2,35,0,9
	.byte	'SPBSEL',0,1
	.word	227
	.byte	2,0,2,35,0,9
	.byte	'GTMDIV',0,1
	.word	227
	.byte	6,2,2,35,1,9
	.byte	'GTMSEL',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'STMDIV',0,1
	.word	227
	.byte	6,2,2,35,2,9
	.byte	'STMSEL',0,1
	.word	227
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	6,2,2,35,3,9
	.byte	'UP',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,163,1,3
	.word	2107
	.byte	8
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,166,1,16,4,9
	.byte	'MAXDIV',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	425
	.byte	26,2,2,35,2,9
	.byte	'UP',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,172,1,3
	.word	2326
	.byte	8
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,175,1,16,4,9
	.byte	'CPU0DIV',0,1
	.word	227
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	425
	.byte	26,0,2,35,2,0,6
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,179,1,3
	.word	2454
	.byte	8
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,182,1,16,4,9
	.byte	'CHREV',0,1
	.word	227
	.byte	6,2,2,35,0,9
	.byte	'CHTEC',0,1
	.word	227
	.byte	2,0,2,35,0,9
	.byte	'CHID',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'EEA',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'UCODE',0,1
	.word	227
	.byte	7,0,2,35,2,9
	.byte	'FSIZE',0,1
	.word	227
	.byte	4,4,2,35,3,9
	.byte	'SP',0,1
	.word	227
	.byte	2,2,2,35,3,9
	.byte	'SEC',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,193,1,3
	.word	2554
	.byte	8
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,196,1,16,4,9
	.byte	'PWD',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'START',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	2,4,2,35,0,9
	.byte	'CAL',0,4
	.word	425
	.byte	22,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	227
	.byte	5,1,2,35,3,9
	.byte	'SLCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,204,1,3
	.word	2762
	.byte	8
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,207,1,16,4,9
	.byte	'LOWER',0,2
	.word	334
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	227
	.byte	5,1,2,35,1,9
	.byte	'LLU',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'UPPER',0,2
	.word	334
	.byte	10,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	227
	.byte	4,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'UOF',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,216,1,3
	.word	2927
	.byte	8
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,219,1,16,4,9
	.byte	'RESULT',0,2
	.word	334
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	227
	.byte	4,2,2,35,1,9
	.byte	'RDY',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'BUSY',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,226,1,3
	.word	3110
	.byte	8
	.byte	'_Ifx_SCU_EICR_Bits',0,6,229,1,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'EXIS0',0,1
	.word	227
	.byte	3,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'FEN0',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'REN0',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'LDEN0',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'EIEN0',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'INP0',0,1
	.word	227
	.byte	3,1,2,35,1,9
	.byte	'reserved_15',0,4
	.word	425
	.byte	5,12,2,35,2,9
	.byte	'EXIS1',0,1
	.word	227
	.byte	3,1,2,35,2,9
	.byte	'reserved_23',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'FEN1',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'REN1',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'LDEN1',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'EIEN1',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'INP1',0,1
	.word	227
	.byte	3,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EICR_Bits',0,6,248,1,3
	.word	3264
	.byte	8
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,251,1,16,4,9
	.byte	'INTF0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'INTF1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'INTF2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'INTF3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'INTF4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'INTF5',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'INTF6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'INTF7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	425
	.byte	24,0,2,35,2,0,6
	.byte	'Ifx_SCU_EIFR_Bits',0,6,134,2,3
	.word	3628
	.byte	8
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,137,2,16,4,9
	.byte	'POL',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'MODE',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'ENON',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'PSEL',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,2
	.word	334
	.byte	12,0,2,35,0,9
	.byte	'EMSF',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'SEMSF',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	227
	.byte	6,0,2,35,2,9
	.byte	'EMSFM',0,1
	.word	227
	.byte	2,6,2,35,3,9
	.byte	'SEMSFM',0,1
	.word	227
	.byte	2,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	227
	.byte	4,0,2,35,3,0,6
	.byte	'Ifx_SCU_EMSR_Bits',0,6,150,2,3
	.word	3839
	.byte	8
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,153,2,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	7,1,2,35,0,9
	.byte	'EDCON',0,2
	.word	334
	.byte	2,7,2,35,0,9
	.byte	'reserved_9',0,4
	.word	425
	.byte	23,0,2,35,2,0,6
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,158,2,3
	.word	4091
	.byte	8
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,161,2,16,4,9
	.byte	'ARI',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'ARC',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	425
	.byte	30,0,2,35,2,0,6
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,166,2,3
	.word	4209
	.byte	8
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,169,2,16,4,9
	.byte	'reserved_0',0,4
	.word	425
	.byte	28,4,2,35,2,9
	.byte	'EVR13OFF',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'BPEVR13OFF',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,176,2,3
	.word	4320
	.byte	8
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,179,2,16,4,9
	.byte	'ADC13V',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'ADCSWDV',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	7,1,2,35,3,9
	.byte	'VAL',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,186,2,3
	.word	4483
	.byte	8
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,189,2,16,4,9
	.byte	'EVR13OVMOD',0,1
	.word	227
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	2,4,2,35,0,9
	.byte	'EVR13UVMOD',0,1
	.word	227
	.byte	2,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	334
	.byte	10,0,2,35,0,9
	.byte	'SWDOVMOD',0,1
	.word	227
	.byte	2,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	227
	.byte	2,4,2,35,2,9
	.byte	'SWDUVMOD',0,1
	.word	227
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,2
	.word	334
	.byte	8,2,2,35,2,9
	.byte	'SLCK',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,201,2,3
	.word	4645
	.byte	8
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,204,2,16,4,9
	.byte	'EVR13OVVAL',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'SWDOVVAL',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	6,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,212,2,3
	.word	4923
	.byte	8
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,215,2,16,4,9
	.byte	'reserved_0',0,4
	.word	425
	.byte	28,4,2,35,2,9
	.byte	'RSTSWDOFF',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'BPRSTSWDOFF',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,222,2,3
	.word	5102
	.byte	8
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,225,2,16,4,9
	.byte	'SD33P',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	227
	.byte	4,0,2,35,0,9
	.byte	'SD33I',0,1
	.word	227
	.byte	4,4,2,35,1,9
	.byte	'reserved_12',0,4
	.word	425
	.byte	19,1,2,35,2,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,232,2,3
	.word	5262
	.byte	8
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,235,2,16,4,9
	.byte	'SDFREQSPRD',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	227
	.byte	4,0,2,35,0,9
	.byte	'TON',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'TOFF',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'SDSTEP',0,1
	.word	227
	.byte	4,4,2,35,3,9
	.byte	'SYNCDIV',0,1
	.word	227
	.byte	3,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,244,2,3
	.word	5423
	.byte	8
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,247,2,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'STBS',0,1
	.word	227
	.byte	2,6,2,35,1,9
	.byte	'STSP',0,1
	.word	227
	.byte	2,4,2,35,1,9
	.byte	'NS',0,1
	.word	227
	.byte	2,2,2,35,1,9
	.byte	'OL',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'PIAD',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'ADCMODE',0,1
	.word	227
	.byte	4,4,2,35,2,9
	.byte	'ADCLPF',0,1
	.word	227
	.byte	2,2,2,35,2,9
	.byte	'ADCLSB',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'SDLUT',0,1
	.word	227
	.byte	6,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,134,3,3
	.word	5615
	.byte	8
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,137,3,16,4,9
	.byte	'SDOLCON',0,1
	.word	227
	.byte	7,1,2,35,0,9
	.byte	'MODSEL',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'MODLOW',0,1
	.word	227
	.byte	7,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'SDVOKLVL',0,1
	.word	227
	.byte	6,2,2,35,2,9
	.byte	'MODMAN',0,1
	.word	227
	.byte	2,0,2,35,2,9
	.byte	'MODHIGH',0,1
	.word	227
	.byte	7,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,147,3,3
	.word	5911
	.byte	8
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,150,3,16,4,9
	.byte	'EVR13',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'OV13',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	2,4,2,35,0,9
	.byte	'OVSWD',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'UV13',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'UVSWD',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	2,6,2,35,1,9
	.byte	'BGPROK',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'reserved_11',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'SCMOD',0,1
	.word	227
	.byte	2,2,2,35,1,9
	.byte	'reserved_14',0,4
	.word	425
	.byte	18,0,2,35,2,0,6
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,164,3,3
	.word	6126
	.byte	8
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,167,3,16,4,9
	.byte	'EVR13UVVAL',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'SWDUVVAL',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	6,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,175,3,3
	.word	6415
	.byte	8
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,178,3,16,4,9
	.byte	'EN0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'SEL0',0,1
	.word	227
	.byte	4,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	334
	.byte	10,0,2,35,0,9
	.byte	'EN1',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'NSEL',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'SEL1',0,1
	.word	227
	.byte	4,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	227
	.byte	2,0,2,35,2,9
	.byte	'DIV1',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,189,3,3
	.word	6594
	.byte	8
	.byte	'_Ifx_SCU_FDR_Bits',0,6,192,3,16,4,9
	.byte	'STEP',0,2
	.word	334
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	227
	.byte	4,2,2,35,1,9
	.byte	'DM',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'RESULT',0,2
	.word	334
	.byte	10,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	227
	.byte	5,1,2,35,3,9
	.byte	'DISCLK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_FDR_Bits',0,6,200,3,3
	.word	6812
	.byte	8
	.byte	'_Ifx_SCU_FMR_Bits',0,6,203,3,16,4,9
	.byte	'FS0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'FS1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'FS2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'FS3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'FS4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'FS5',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'FS6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'FS7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'FC0',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'FC1',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'FC2',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'FC3',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'FC4',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'FC5',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'FC6',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'FC7',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_SCU_FMR_Bits',0,6,223,3,3
	.word	6975
	.byte	8
	.byte	'_Ifx_SCU_ID_Bits',0,6,226,3,16,4,9
	.byte	'MODREV',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_ID_Bits',0,6,231,3,3
	.word	7311
	.byte	8
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,234,3,16,4,9
	.byte	'IPEN00',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'IPEN01',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'IPEN02',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'IPEN03',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'IPEN04',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'IPEN05',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'IPEN06',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'IPEN07',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	5,3,2,35,1,9
	.byte	'GEEN0',0,1
	.word	227
	.byte	1,2,2,35,1,9
	.byte	'IGP0',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'IPEN10',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'IPEN11',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'IPEN12',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'IPEN13',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'IPEN14',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'IPEN15',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'IPEN16',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'IPEN17',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	5,3,2,35,3,9
	.byte	'GEEN1',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'IGP1',0,1
	.word	227
	.byte	2,0,2,35,3,0,6
	.byte	'Ifx_SCU_IGCR_Bits',0,6,130,4,3
	.word	7418
	.byte	8
	.byte	'_Ifx_SCU_IN_Bits',0,6,133,4,16,4,9
	.byte	'P0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	425
	.byte	30,0,2,35,2,0,6
	.byte	'Ifx_SCU_IN_Bits',0,6,138,4,3
	.word	7870
	.byte	8
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,141,4,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'PC0',0,1
	.word	227
	.byte	4,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	4,4,2,35,1,9
	.byte	'PC1',0,1
	.word	227
	.byte	4,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_IOCR_Bits',0,6,148,4,3
	.word	7969
	.byte	8
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,151,4,16,4,9
	.byte	'LBISTREQ',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'LBISTREQP',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'PATTERNS',0,2
	.word	334
	.byte	14,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,157,4,3
	.word	8119
	.byte	8
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,160,4,16,4,9
	.byte	'SEED',0,4
	.word	425
	.byte	23,9,2,35,2,9
	.byte	'reserved_23',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'SPLITSH',0,1
	.word	227
	.byte	3,5,2,35,3,9
	.byte	'BODY',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'LBISTFREQU',0,1
	.word	227
	.byte	4,0,2,35,3,0,6
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,167,4,3
	.word	8268
	.byte	8
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,170,4,16,4,9
	.byte	'SIGNATURE',0,4
	.word	425
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	7,1,2,35,3,9
	.byte	'LBISTDONE',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,175,4,3
	.word	8429
	.byte	8
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,6,178,4,16,4,9
	.byte	'reserved_0',0,2
	.word	334
	.byte	16,0,2,35,0,9
	.byte	'LS',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,2
	.word	334
	.byte	14,1,2,35,2,9
	.byte	'LSEN',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_LCLCON0_Bits',0,6,184,4,3
	.word	8559
	.byte	8
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,187,4,16,4,9
	.byte	'LCLT0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'LCLT1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	425
	.byte	30,0,2,35,2,0,6
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,192,4,3
	.word	8693
	.byte	8
	.byte	'_Ifx_SCU_MANID_Bits',0,6,195,4,16,4,9
	.byte	'DEPT',0,1
	.word	227
	.byte	5,3,2,35,0,9
	.byte	'MANUF',0,2
	.word	334
	.byte	11,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_MANID_Bits',0,6,200,4,3
	.word	8808
	.byte	8
	.byte	'_Ifx_SCU_OMR_Bits',0,6,203,4,16,4,9
	.byte	'PS0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'PS1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,2
	.word	334
	.byte	14,0,2,35,0,9
	.byte	'PCL0',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'PCL1',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	334
	.byte	14,0,2,35,2,0,6
	.byte	'Ifx_SCU_OMR_Bits',0,6,211,4,3
	.word	8919
	.byte	8
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,214,4,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'PLLLV',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'OSCRES',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'GAINSEL',0,1
	.word	227
	.byte	2,3,2,35,0,9
	.byte	'MODE',0,1
	.word	227
	.byte	2,1,2,35,0,9
	.byte	'SHBY',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'PLLHV',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'X1D',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'X1DEN',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	227
	.byte	4,0,2,35,1,9
	.byte	'OSCVAL',0,1
	.word	227
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	227
	.byte	2,1,2,35,2,9
	.byte	'APREN',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,231,4,3
	.word	9077
	.byte	8
	.byte	'_Ifx_SCU_OUT_Bits',0,6,234,4,16,4,9
	.byte	'P0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	425
	.byte	30,0,2,35,2,0,6
	.byte	'Ifx_SCU_OUT_Bits',0,6,239,4,3
	.word	9417
	.byte	8
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,242,4,16,4,9
	.byte	'CSEL0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'CSEL1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'CSEL2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,2
	.word	334
	.byte	13,0,2,35,0,9
	.byte	'OVSTRT',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'OVSTP',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'DCINVAL',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	227
	.byte	5,0,2,35,2,9
	.byte	'OVCONF',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'POVCONF',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	227
	.byte	6,0,2,35,3,0,6
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,255,4,3
	.word	9518
	.byte	8
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,130,5,16,4,9
	.byte	'OVEN0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'OVEN1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'OVEN2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,4
	.word	425
	.byte	29,0,2,35,2,0,6
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,136,5,3
	.word	9785
	.byte	8
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,139,5,16,4,9
	.byte	'PDIS0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'PDIS1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	425
	.byte	30,0,2,35,2,0,6
	.byte	'Ifx_SCU_PDISC_Bits',0,6,144,5,3
	.word	9921
	.byte	8
	.byte	'_Ifx_SCU_PDR_Bits',0,6,147,5,16,4,9
	.byte	'PD0',0,1
	.word	227
	.byte	3,5,2,35,0,9
	.byte	'PL0',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'PD1',0,1
	.word	227
	.byte	3,1,2,35,0,9
	.byte	'PL1',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	425
	.byte	24,0,2,35,2,0,6
	.byte	'Ifx_SCU_PDR_Bits',0,6,154,5,3
	.word	10032
	.byte	8
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,157,5,16,4,9
	.byte	'PDR0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'PDR1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'PDR2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'PDR3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'PDR4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'PDR5',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'PDR6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'PDR7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	425
	.byte	24,0,2,35,2,0,6
	.byte	'Ifx_SCU_PDRR_Bits',0,6,168,5,3
	.word	10165
	.byte	8
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,171,5,16,4,9
	.byte	'VCOBYP',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'VCOPWD',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'MODEN',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'SETFINDIS',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'CLRFINDIS',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'OSCDISCDIS',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	334
	.byte	2,7,2,35,0,9
	.byte	'NDIV',0,1
	.word	227
	.byte	7,0,2,35,1,9
	.byte	'PLLPWD',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'RESLD',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	227
	.byte	5,0,2,35,2,9
	.byte	'PDIV',0,1
	.word	227
	.byte	4,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	227
	.byte	4,0,2,35,3,0,6
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,188,5,3
	.word	10368
	.byte	8
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,191,5,16,4,9
	.byte	'K2DIV',0,1
	.word	227
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'K3DIV',0,1
	.word	227
	.byte	7,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'K1DIV',0,1
	.word	227
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	334
	.byte	9,0,2,35,2,0,6
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,199,5,3
	.word	10724
	.byte	8
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,202,5,16,4,9
	.byte	'MODCFG',0,2
	.word	334
	.byte	16,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,206,5,3
	.word	10902
	.byte	8
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,209,5,16,4,9
	.byte	'VCOBYP',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'VCOPWD',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	2,4,2,35,0,9
	.byte	'SETFINDIS',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'CLRFINDIS',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'OSCDISCDIS',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	334
	.byte	2,7,2,35,0,9
	.byte	'NDIV',0,1
	.word	227
	.byte	5,2,2,35,1,9
	.byte	'reserved_14',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'PLLPWD',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'RESLD',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	227
	.byte	5,0,2,35,2,9
	.byte	'PDIV',0,1
	.word	227
	.byte	4,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	227
	.byte	4,0,2,35,3,0,6
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,226,5,3
	.word	11002
	.byte	8
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,229,5,16,4,9
	.byte	'K2DIV',0,1
	.word	227
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'K3DIV',0,1
	.word	227
	.byte	4,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	227
	.byte	4,0,2,35,1,9
	.byte	'K1DIV',0,1
	.word	227
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	334
	.byte	9,0,2,35,2,0,6
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,237,5,3
	.word	11372
	.byte	8
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,240,5,16,4,9
	.byte	'VCOBYST',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'PWDSTAT',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'VCOLOCK',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'FINDIS',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'K1RDY',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'K2RDY',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	425
	.byte	26,0,2,35,2,0,6
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,249,5,3
	.word	11558
	.byte	8
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,252,5,16,4,9
	.byte	'VCOBYST',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'VCOLOCK',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'FINDIS',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'K1RDY',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'K2RDY',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'MODRUN',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	425
	.byte	24,0,2,35,2,0,6
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,135,6,3
	.word	11756
	.byte	8
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,138,6,16,4,9
	.byte	'REQSLP',0,1
	.word	227
	.byte	2,6,2,35,0,9
	.byte	'SMUSLP',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	227
	.byte	5,0,2,35,0,9
	.byte	'PMST',0,1
	.word	227
	.byte	3,5,2,35,1,9
	.byte	'reserved_11',0,4
	.word	425
	.byte	21,0,2,35,2,0,6
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,145,6,3
	.word	11989
	.byte	8
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,148,6,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'ESR1WKEN',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'PINAWKEN',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'PINBWKEN',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'ESR0DFEN',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'ESR0EDCON',0,1
	.word	227
	.byte	2,1,2,35,0,9
	.byte	'ESR1DFEN',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'ESR1EDCON',0,1
	.word	227
	.byte	2,6,2,35,1,9
	.byte	'PINADFEN',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'PINAEDCON',0,1
	.word	227
	.byte	2,3,2,35,1,9
	.byte	'PINBDFEN',0,1
	.word	227
	.byte	1,2,2,35,1,9
	.byte	'PINBEDCON',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'STBYRAMSEL',0,1
	.word	227
	.byte	2,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'WUTWKEN',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	227
	.byte	2,1,2,35,2,9
	.byte	'PORSTDF',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'DCDCSYNC',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	227
	.byte	3,3,2,35,3,9
	.byte	'ESR0TRIST',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,174,6,3
	.word	12141
	.byte	8
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,177,6,16,4,9
	.byte	'reserved_0',0,2
	.word	334
	.byte	12,4,2,35,0,9
	.byte	'IRADIS',0,1
	.word	227
	.byte	1,3,2,35,1,9
	.byte	'reserved_13',0,4
	.word	425
	.byte	14,5,2,35,2,9
	.byte	'STBYEVEN',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'STBYEV',0,1
	.word	227
	.byte	3,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,185,6,3
	.word	12700
	.byte	8
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,6,188,6,16,4,9
	.byte	'WUTREL',0,4
	.word	425
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	4,4,2,35,3,9
	.byte	'WUTDIV',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'WUTEN',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'WUTMODE',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,6,196,6,3
	.word	12883
	.byte	8
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,199,6,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	2,6,2,35,0,9
	.byte	'ESR1WKP',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'ESR1OVRUN',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'PINAWKP',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'PINAOVRUN',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'PINBWKP',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'PINBOVRUN',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'PORSTDF',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'HWCFGEVR',0,1
	.word	227
	.byte	3,3,2,35,1,9
	.byte	'STBYRAM',0,1
	.word	227
	.byte	2,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'WUTWKP',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'WUTOVRUN',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'WUTWKEN',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'ESR1WKEN',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'PINAWKEN',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'PINBWKEN',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	334
	.byte	4,5,2,35,2,9
	.byte	'ESR0TRIST',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'WUTEN',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'WUTMODE',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'WUTRUN',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,226,6,3
	.word	13052
	.byte	8
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,229,6,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	2,6,2,35,0,9
	.byte	'ESR1WKPCLR',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'ESR1OVRUNCLR',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'PINAWKPCLR',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'PINAOVRUNCLR',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'PINBWKPCLR',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'PINBOVRUNCLR',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'WUTWKPCLR',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'WUTOVRUNCLR',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	334
	.byte	14,0,2,35,2,0,6
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,242,6,3
	.word	13619
	.byte	8
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,6,245,6,16,4,9
	.byte	'WUTCNT',0,4
	.word	425
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	7,1,2,35,3,9
	.byte	'VAL',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,6,250,6,3
	.word	13935
	.byte	8
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,253,6,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'CLRC',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,2
	.word	334
	.byte	10,4,2,35,0,9
	.byte	'CSS0',0,1
	.word	227
	.byte	1,3,2,35,1,9
	.byte	'CSS1',0,1
	.word	227
	.byte	1,2,2,35,1,9
	.byte	'CSS2',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'USRINFO',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,135,7,3
	.word	14054
	.byte	8
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,138,7,16,4,9
	.byte	'ESR0',0,1
	.word	227
	.byte	2,6,2,35,0,9
	.byte	'ESR1',0,1
	.word	227
	.byte	2,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	227
	.byte	2,2,2,35,0,9
	.byte	'SMU',0,1
	.word	227
	.byte	2,0,2,35,0,9
	.byte	'SW',0,1
	.word	227
	.byte	2,6,2,35,1,9
	.byte	'STM0',0,1
	.word	227
	.byte	2,4,2,35,1,9
	.byte	'STM1',0,1
	.word	227
	.byte	2,2,2,35,1,9
	.byte	'STM2',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,149,7,3
	.word	14263
	.byte	8
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,152,7,16,4,9
	.byte	'ESR0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'ESR1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'SMU',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'SW',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'STM0',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'STM1',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'STM2',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'PORST',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'CB0',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'CB1',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'CB3',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	227
	.byte	2,1,2,35,2,9
	.byte	'EVR13',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'EVR33',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'SWD',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	227
	.byte	2,4,2,35,3,9
	.byte	'STBYR',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	227
	.byte	3,0,2,35,3,0,6
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,175,7,3
	.word	14474
	.byte	8
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,178,7,16,4,9
	.byte	'HBT',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	425
	.byte	31,0,2,35,2,0,6
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,182,7,3
	.word	14906
	.byte	8
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,185,7,16,4,9
	.byte	'HWCFG',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'FTM',0,1
	.word	227
	.byte	7,1,2,35,1,9
	.byte	'MODE',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'FCBAE',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'LUDIS',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'TRSTL',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'SPDEN',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	227
	.byte	3,0,2,35,2,9
	.byte	'RAMINT',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	227
	.byte	7,0,2,35,3,0,6
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,198,7,3
	.word	15002
	.byte	8
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,201,7,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'SWRSTREQ',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	425
	.byte	30,0,2,35,2,0,6
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,206,7,3
	.word	15262
	.byte	8
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,209,7,16,4,9
	.byte	'CCTRIG0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'RAMINTM',0,1
	.word	227
	.byte	2,4,2,35,0,9
	.byte	'SETLUDIS',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	227
	.byte	3,0,2,35,0,9
	.byte	'DATM',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,4
	.word	425
	.byte	23,0,2,35,2,0,6
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,218,7,3
	.word	15387
	.byte	8
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,221,7,16,4,9
	.byte	'ESR0T',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	425
	.byte	28,0,2,35,2,0,6
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,228,7,3
	.word	15584
	.byte	8
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,231,7,16,4,9
	.byte	'ESR0T',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	425
	.byte	28,0,2,35,2,0,6
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,238,7,3
	.word	15737
	.byte	8
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,241,7,16,4,9
	.byte	'ESR0T',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	425
	.byte	28,0,2,35,2,0,6
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,248,7,3
	.word	15890
	.byte	8
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,251,7,16,4,9
	.byte	'ESR0T',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	425
	.byte	28,0,2,35,2,0,6
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,130,8,3
	.word	16043
	.byte	8
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,133,8,16,4,3
	.byte	'unsigned int',0,4,7,9
	.byte	'ENDINIT',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'LCK',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'PW',0,4
	.word	16230
	.byte	14,16,2,35,0,9
	.byte	'REL',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,139,8,3
	.word	16198
	.byte	8
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,142,8,16,4,9
	.byte	'reserved_0',0,1
	.word	227
	.byte	2,6,2,35,0,9
	.byte	'IR0',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'DR',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'IR1',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'UR',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'PAR',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'TCR',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'TCTR',0,1
	.word	227
	.byte	7,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,154,8,3
	.word	16344
	.byte	8
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,157,8,16,4,9
	.byte	'AE',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'OE',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'IS0',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'DS',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'TO',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'IS1',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'US',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'PAS',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'TCS',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'TCT',0,1
	.word	227
	.byte	7,0,2,35,1,9
	.byte	'TIM',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,170,8,3
	.word	16582
	.byte	8
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,173,8,16,4,9
	.byte	'ENDINIT',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'LCK',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'PW',0,4
	.word	16230
	.byte	14,16,2,35,0,9
	.byte	'REL',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,179,8,3
	.word	16805
	.byte	8
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,182,8,16,4,9
	.byte	'CLRIRF',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'IR0',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'DR',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'IR1',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'UR',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'PAR',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'TCR',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'TCTR',0,1
	.word	227
	.byte	7,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,195,8,3
	.word	16931
	.byte	8
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,198,8,16,4,9
	.byte	'AE',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'OE',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'IS0',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'DS',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'TO',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'IS1',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'US',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'PAS',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'TCS',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'TCT',0,1
	.word	227
	.byte	7,0,2,35,1,9
	.byte	'TIM',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,211,8,3
	.word	17183
	.byte	10,6,219,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,3
	.byte	'int',0,4,5,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	462
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_ACCEN0',0,6,224,8,3
	.word	17402
	.byte	10,6,227,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1019
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_ACCEN1',0,6,232,8,3
	.word	17473
	.byte	10,6,235,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1096
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_ARSTDIS',0,6,240,8,3
	.word	17537
	.byte	10,6,243,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1232
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_CCUCON0',0,6,248,8,3
	.word	17602
	.byte	10,6,251,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1514
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_CCUCON1',0,6,128,9,3
	.word	17667
	.byte	10,6,131,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1752
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_CCUCON2',0,6,136,9,3
	.word	17732
	.byte	10,6,139,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1880
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_CCUCON3',0,6,144,9,3
	.word	17797
	.byte	10,6,147,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2107
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_CCUCON4',0,6,152,9,3
	.word	17862
	.byte	10,6,155,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2326
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_CCUCON5',0,6,160,9,3
	.word	17927
	.byte	10,6,163,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2454
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_CCUCON6',0,6,168,9,3
	.word	17992
	.byte	10,6,171,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2554
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_CHIPID',0,6,176,9,3
	.word	18057
	.byte	10,6,179,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2762
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_DTSCON',0,6,184,9,3
	.word	18121
	.byte	10,6,187,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2927
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_DTSLIM',0,6,192,9,3
	.word	18185
	.byte	10,6,195,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3110
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_DTSSTAT',0,6,200,9,3
	.word	18249
	.byte	10,6,203,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3264
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EICR',0,6,208,9,3
	.word	18314
	.byte	10,6,211,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3628
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EIFR',0,6,216,9,3
	.word	18376
	.byte	10,6,219,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3839
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EMSR',0,6,224,9,3
	.word	18438
	.byte	10,6,227,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4091
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_ESRCFG',0,6,232,9,3
	.word	18500
	.byte	10,6,235,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4209
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_ESROCFG',0,6,240,9,3
	.word	18564
	.byte	10,6,243,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4320
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVR13CON',0,6,248,9,3
	.word	18629
	.byte	10,6,251,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4483
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,128,10,3
	.word	18695
	.byte	10,6,131,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4645
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,136,10,3
	.word	18763
	.byte	10,6,139,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4923
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVROVMON',0,6,144,10,3
	.word	18831
	.byte	10,6,147,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5102
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRRSTCON',0,6,152,10,3
	.word	18897
	.byte	10,6,155,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5262
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,160,10,3
	.word	18964
	.byte	10,6,163,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5423
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,168,10,3
	.word	19033
	.byte	10,6,171,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5615
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,176,10,3
	.word	19101
	.byte	10,6,179,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5911
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,184,10,3
	.word	19169
	.byte	10,6,187,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6126
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRSTAT',0,6,192,10,3
	.word	19237
	.byte	10,6,195,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6415
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EVRUVMON',0,6,200,10,3
	.word	19302
	.byte	10,6,203,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6594
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_EXTCON',0,6,208,10,3
	.word	19368
	.byte	10,6,211,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6812
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_FDR',0,6,216,10,3
	.word	19432
	.byte	10,6,219,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6975
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_FMR',0,6,224,10,3
	.word	19493
	.byte	10,6,227,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7311
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_ID',0,6,232,10,3
	.word	19554
	.byte	10,6,235,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7418
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_IGCR',0,6,240,10,3
	.word	19614
	.byte	10,6,243,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7870
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_IN',0,6,248,10,3
	.word	19676
	.byte	10,6,251,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7969
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_IOCR',0,6,128,11,3
	.word	19736
	.byte	10,6,131,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8119
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,136,11,3
	.word	19798
	.byte	10,6,139,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8268
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,144,11,3
	.word	19866
	.byte	10,6,147,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8429
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,152,11,3
	.word	19934
	.byte	10,6,155,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8559
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_LCLCON0',0,6,160,11,3
	.word	20002
	.byte	10,6,163,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8693
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_LCLTEST',0,6,168,11,3
	.word	20067
	.byte	10,6,171,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8808
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_MANID',0,6,176,11,3
	.word	20132
	.byte	10,6,179,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8919
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_OMR',0,6,184,11,3
	.word	20195
	.byte	10,6,187,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9077
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_OSCCON',0,6,192,11,3
	.word	20256
	.byte	10,6,195,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9417
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_OUT',0,6,200,11,3
	.word	20320
	.byte	10,6,203,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9518
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_OVCCON',0,6,208,11,3
	.word	20381
	.byte	10,6,211,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9785
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_OVCENABLE',0,6,216,11,3
	.word	20445
	.byte	10,6,219,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	9921
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PDISC',0,6,224,11,3
	.word	20512
	.byte	10,6,227,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10032
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PDR',0,6,232,11,3
	.word	20575
	.byte	10,6,235,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10165
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PDRR',0,6,240,11,3
	.word	20636
	.byte	10,6,243,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10368
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PLLCON0',0,6,248,11,3
	.word	20698
	.byte	10,6,251,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10724
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PLLCON1',0,6,128,12,3
	.word	20763
	.byte	10,6,131,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	10902
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PLLCON2',0,6,136,12,3
	.word	20828
	.byte	10,6,139,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11002
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,144,12,3
	.word	20893
	.byte	10,6,147,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11372
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,152,12,3
	.word	20962
	.byte	10,6,155,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11558
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,160,12,3
	.word	21031
	.byte	10,6,163,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11756
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PLLSTAT',0,6,168,12,3
	.word	21100
	.byte	10,6,171,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	11989
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PMCSR',0,6,176,12,3
	.word	21165
	.byte	10,6,179,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12141
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PMSWCR0',0,6,184,12,3
	.word	21228
	.byte	10,6,187,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12700
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PMSWCR1',0,6,192,12,3
	.word	21293
	.byte	10,6,195,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	12883
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PMSWCR3',0,6,200,12,3
	.word	21358
	.byte	10,6,203,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13052
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PMSWSTAT',0,6,208,12,3
	.word	21423
	.byte	10,6,211,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13619
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,216,12,3
	.word	21489
	.byte	10,6,219,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13935
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_PMSWUTCNT',0,6,224,12,3
	.word	21558
	.byte	10,6,227,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14263
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_RSTCON',0,6,232,12,3
	.word	21625
	.byte	10,6,235,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14054
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_RSTCON2',0,6,240,12,3
	.word	21689
	.byte	10,6,243,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14474
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_RSTSTAT',0,6,248,12,3
	.word	21754
	.byte	10,6,251,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14906
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_SAFECON',0,6,128,13,3
	.word	21819
	.byte	10,6,131,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15002
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_STSTAT',0,6,136,13,3
	.word	21884
	.byte	10,6,139,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15262
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_SWRSTCON',0,6,144,13,3
	.word	21948
	.byte	10,6,147,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15387
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_SYSCON',0,6,152,13,3
	.word	22014
	.byte	10,6,155,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15584
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_TRAPCLR',0,6,160,13,3
	.word	22078
	.byte	10,6,163,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15737
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_TRAPDIS',0,6,168,13,3
	.word	22143
	.byte	10,6,171,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15890
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_TRAPSET',0,6,176,13,3
	.word	22208
	.byte	10,6,179,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16043
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_TRAPSTAT',0,6,184,13,3
	.word	22273
	.byte	10,6,187,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16198
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,192,13,3
	.word	22339
	.byte	10,6,195,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16344
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,200,13,3
	.word	22408
	.byte	10,6,203,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16582
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,208,13,3
	.word	22477
	.byte	10,6,211,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16805
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_WDTS_CON0',0,6,216,13,3
	.word	22544
	.byte	10,6,219,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16931
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_WDTS_CON1',0,6,224,13,3
	.word	22611
	.byte	10,6,227,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17183
	.byte	2,35,0,0,6
	.byte	'Ifx_SCU_WDTS_SR',0,6,232,13,3
	.word	22678
	.byte	8
	.byte	'_Ifx_SCU_WDTCPU',0,6,243,13,25,12,11
	.byte	'CON0',0,4
	.word	22339
	.byte	2,35,0,11
	.byte	'CON1',0,4
	.word	22408
	.byte	2,35,4,11
	.byte	'SR',0,4
	.word	22477
	.byte	2,35,8,0,12
	.word	22743
	.byte	6
	.byte	'Ifx_SCU_WDTCPU',0,6,248,13,3
	.word	22806
	.byte	8
	.byte	'_Ifx_SCU_WDTS',0,6,251,13,25,12,11
	.byte	'CON0',0,4
	.word	22544
	.byte	2,35,0,11
	.byte	'CON1',0,4
	.word	22611
	.byte	2,35,4,11
	.byte	'SR',0,4
	.word	22678
	.byte	2,35,8,0,12
	.word	22835
	.byte	6
	.byte	'Ifx_SCU_WDTS',0,6,128,14,3
	.word	22896
	.byte	8
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,7,45,16,4,9
	.byte	'EN0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'EN1',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'EN2',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'EN3',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'EN4',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'EN5',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'EN6',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'EN7',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'EN8',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'EN9',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'EN10',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'EN11',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'EN12',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'EN13',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'EN14',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'EN15',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'EN16',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'EN17',0,4
	.word	16230
	.byte	1,14,2,35,0,9
	.byte	'EN18',0,4
	.word	16230
	.byte	1,13,2,35,0,9
	.byte	'EN19',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'EN20',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'EN21',0,4
	.word	16230
	.byte	1,10,2,35,0,9
	.byte	'EN22',0,4
	.word	16230
	.byte	1,9,2,35,0,9
	.byte	'EN23',0,4
	.word	16230
	.byte	1,8,2,35,0,9
	.byte	'EN24',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'EN25',0,4
	.word	16230
	.byte	1,6,2,35,0,9
	.byte	'EN26',0,4
	.word	16230
	.byte	1,5,2,35,0,9
	.byte	'EN27',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'EN28',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'EN29',0,4
	.word	16230
	.byte	1,2,2,35,0,9
	.byte	'EN30',0,4
	.word	16230
	.byte	1,1,2,35,0,9
	.byte	'EN31',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_GTM_ACCEN0_Bits',0,7,79,3
	.word	22923
	.byte	8
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,7,82,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_GTM_ACCEN1_Bits',0,7,85,3
	.word	23480
	.byte	8
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,7,88,16,4,9
	.byte	'SEL0',0,4
	.word	16230
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	16230
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	16230
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	16230
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,7,95,3
	.word	23557
	.byte	8
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,7,98,16,4,9
	.byte	'SEL0',0,4
	.word	16230
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	16230
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	16230
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	16230
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,7,105,3
	.word	23711
	.byte	8
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,7,108,16,4,9
	.byte	'TO_ADDR',0,4
	.word	16230
	.byte	20,12,2,35,0,9
	.byte	'TO_W1R0',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	16230
	.byte	11,0,2,35,0,0,6
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,7,113,3
	.word	23865
	.byte	8
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,7,116,16,4,9
	.byte	'BRG_MODE',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'MSK_WR_RSP',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	6,24,2,35,0,9
	.byte	'MODE_UP_PGR',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'BUFF_OVL',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'SYNC_INPUT_REG',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	16230
	.byte	3,16,2,35,0,9
	.byte	'BRG_RST',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'reserved_17',0,4
	.word	16230
	.byte	7,8,2,35,0,9
	.byte	'BUFF_DPT',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,7,129,1,3
	.word	23993
	.byte	8
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,7,132,1,16,4,9
	.byte	'NEW_TRAN_PTR',0,4
	.word	16230
	.byte	5,27,2,35,0,9
	.byte	'FIRST_RSP_PTR',0,4
	.word	16230
	.byte	5,22,2,35,0,9
	.byte	'TRAN_IN_PGR',0,4
	.word	16230
	.byte	5,17,2,35,0,9
	.byte	'ABT_TRAN_PGR',0,4
	.word	16230
	.byte	5,12,2,35,0,9
	.byte	'FBC',0,4
	.word	16230
	.byte	6,6,2,35,0,9
	.byte	'RSP_TRAN_RDY',0,4
	.word	16230
	.byte	6,0,2,35,0,0,6
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,7,140,1,3
	.word	24300
	.byte	8
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,7,143,1,16,4,9
	.byte	'TRAN_IN_PGR2',0,4
	.word	16230
	.byte	5,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,7,147,1,3
	.word	24502
	.byte	8
	.byte	'_Ifx_GTM_CLC_Bits',0,7,150,1,16,4,9
	.byte	'DISR',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'DISS',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'EDIS',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_CLC_Bits',0,7,157,1,3
	.word	24615
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,7,160,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,7,164,1,3
	.word	24758
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,7,167,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'CLK6_SEL',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	16230
	.byte	7,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,7,172,1,3
	.word	24875
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,7,175,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'CLK7_SEL',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	16230
	.byte	7,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,7,180,1,3
	.word	25010
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,7,183,1,16,4,9
	.byte	'EN_CLK0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'EN_CLK1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'EN_CLK2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'EN_CLK3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'EN_CLK4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'EN_CLK5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'EN_CLK6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'EN_CLK7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'EN_ECLK0',0,4
	.word	16230
	.byte	2,14,2,35,0,9
	.byte	'EN_ECLK1',0,4
	.word	16230
	.byte	2,12,2,35,0,9
	.byte	'EN_ECLK2',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'EN_FXCLK',0,4
	.word	16230
	.byte	2,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,7,198,1,3
	.word	25145
	.byte	8
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,7,201,1,16,4,9
	.byte	'ECLK_DEN',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,7,205,1,3
	.word	25465
	.byte	8
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,7,208,1,16,4,9
	.byte	'ECLK_NUM',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,7,212,1,3
	.word	25577
	.byte	8
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,7,215,1,16,4,9
	.byte	'FXCLK_SEL',0,4
	.word	16230
	.byte	4,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,7,219,1,3
	.word	25689
	.byte	8
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,7,222,1,16,4,9
	.byte	'GCLK_DEN',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,7,226,1,3
	.word	25805
	.byte	8
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,7,229,1,16,4,9
	.byte	'GCLK_NUM',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,7,233,1,3
	.word	25917
	.byte	8
	.byte	'_Ifx_GTM_CTRL_Bits',0,7,236,1,16,4,9
	.byte	'RF_PROT',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TO_MODE',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'TO_VAL',0,4
	.word	16230
	.byte	5,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	16230
	.byte	23,0,2,35,0,0,6
	.byte	'Ifx_GTM_CTRL_Bits',0,7,243,1,3
	.word	26029
	.byte	8
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,7,246,1,16,4,9
	.byte	'O1SEL_0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	2,29,2,35,0,9
	.byte	'SWAP_0',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'O1F_0',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'O1SEL_1',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'I1SEL_1',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'SH_EN_1',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'SWAP_1',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'O1F_1',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'O1SEL_2',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'I1SEL_2',0,4
	.word	16230
	.byte	1,14,2,35,0,9
	.byte	'SH_EN_2',0,4
	.word	16230
	.byte	1,13,2,35,0,9
	.byte	'SWAP_2',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'O1F_2',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'reserved_22',0,4
	.word	16230
	.byte	2,8,2,35,0,9
	.byte	'O1SEL_3',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'I1SEL_3',0,4
	.word	16230
	.byte	1,6,2,35,0,9
	.byte	'SH_EN_3',0,4
	.word	16230
	.byte	1,5,2,35,0,9
	.byte	'SWAP_3',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'O1F_3',0,4
	.word	16230
	.byte	2,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,7,143,2,3
	.word	26182
	.byte	8
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,7,146,2,16,4,9
	.byte	'POL0_0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'OC0_0',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'SL0_0',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'DT0_0',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'POL1_0',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'OC1_0',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'SL1_0',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'DT1_0',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'POL0_1',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'OC0_1',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'SL0_1',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'DT0_1',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'POL1_1',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'OC1_1',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'SL1_1',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'DT1_1',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'POL0_2',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'OC0_2',0,4
	.word	16230
	.byte	1,14,2,35,0,9
	.byte	'SL0_2',0,4
	.word	16230
	.byte	1,13,2,35,0,9
	.byte	'DT0_2',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'POL1_2',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'OC1_2',0,4
	.word	16230
	.byte	1,10,2,35,0,9
	.byte	'SL1_2',0,4
	.word	16230
	.byte	1,9,2,35,0,9
	.byte	'DT1_2',0,4
	.word	16230
	.byte	1,8,2,35,0,9
	.byte	'POL0_3',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'OC0_3',0,4
	.word	16230
	.byte	1,6,2,35,0,9
	.byte	'SL0_3',0,4
	.word	16230
	.byte	1,5,2,35,0,9
	.byte	'DT0_3',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'POL1_3',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'OC1_3',0,4
	.word	16230
	.byte	1,2,2,35,0,9
	.byte	'SL1_3',0,4
	.word	16230
	.byte	1,1,2,35,0,9
	.byte	'DT1_3',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,7,180,2,3
	.word	26694
	.byte	8
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,7,183,2,16,4,9
	.byte	'POL0_0_SR',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'OC0_0_SR',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'SL0_0_SR',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'DT0_0_SR',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'POL1_0_SR',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'OC1_0_SR',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'SL1_0_SR',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'DT1_0_SR',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'POL0_1_SR',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'OC0_1_SR',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'SL0_1_SR',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'DT0_1_SR',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'POL1_1_SR',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'OC1_1_SR',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'SL1_1_SR',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'DT1_1_SR',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'POL0_2_SR',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'OC0_2_SR',0,4
	.word	16230
	.byte	1,14,2,35,0,9
	.byte	'SL0_2_SR',0,4
	.word	16230
	.byte	1,13,2,35,0,9
	.byte	'DT0_2_SR',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'POL1_2_SR',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'OC1_2_SR',0,4
	.word	16230
	.byte	1,10,2,35,0,9
	.byte	'SL1_2_SR',0,4
	.word	16230
	.byte	1,9,2,35,0,9
	.byte	'DT1_2_SR',0,4
	.word	16230
	.byte	1,8,2,35,0,9
	.byte	'POL0_3_SR',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'OC0_3_SR',0,4
	.word	16230
	.byte	1,6,2,35,0,9
	.byte	'SL0_3_SR',0,4
	.word	16230
	.byte	1,5,2,35,0,9
	.byte	'DT0_3_SR',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'POL1_3_SR',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'OC1_3_SR',0,4
	.word	16230
	.byte	1,2,2,35,0,9
	.byte	'SL1_3_SR',0,4
	.word	16230
	.byte	1,1,2,35,0,9
	.byte	'DT1_3_SR',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,7,217,2,3
	.word	27315
	.byte	8
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,7,220,2,16,4,9
	.byte	'CLK_SEL',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'UPD_MODE',0,4
	.word	16230
	.byte	3,25,2,35,0,9
	.byte	'reserved_7',0,4
	.word	16230
	.byte	25,0,2,35,0,0,6
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,7,226,2,3
	.word	28038
	.byte	8
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,7,229,2,16,4,9
	.byte	'RELRISE',0,4
	.word	16230
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	16230
	.byte	6,16,2,35,0,9
	.byte	'RELFALL',0,4
	.word	16230
	.byte	10,6,2,35,0,9
	.byte	'reserved_26',0,4
	.word	16230
	.byte	6,0,2,35,0,0,6
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,7,235,2,3
	.word	28182
	.byte	8
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,7,238,2,16,4,9
	.byte	'RELBLK',0,4
	.word	16230
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	16230
	.byte	6,16,2,35,0,9
	.byte	'PSU_IN_SEL',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'IN_POL',0,4
	.word	16230
	.byte	1,14,2,35,0,9
	.byte	'reserved_18',0,4
	.word	16230
	.byte	2,12,2,35,0,9
	.byte	'SHIFT_SEL',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'reserved_22',0,4
	.word	16230
	.byte	10,0,2,35,0,0,6
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,7,247,2,3
	.word	28331
	.byte	8
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,7,250,2,16,4,9
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,7,129,3,3
	.word	28546
	.byte	8
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,7,132,3,16,4,9
	.byte	'GRSTEN',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'BRIDGE_MODE_RST',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'AEI_IN',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	5,24,2,35,0,9
	.byte	'TOM_OUT_RST',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	16230
	.byte	3,20,2,35,0,9
	.byte	'reserved_12',0,4
	.word	16230
	.byte	4,16,2,35,0,9
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'IRQ_MODE_PULSE',0,4
	.word	16230
	.byte	1,14,2,35,0,9
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	16230
	.byte	1,13,2,35,0,9
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	16230
	.byte	12,0,2,35,0,0,6
	.byte	'Ifx_GTM_HW_CONF_Bits',0,7,146,3,3
	.word	28750
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,7,149,3,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	4,28,2,35,0,9
	.byte	'AEI_IRQ',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,7,154,3,3
	.word	29107
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,7,157,3,16,4,9
	.byte	'TIM0_CH0_IRQ',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TIM0_CH1_IRQ',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'TIM0_CH2_IRQ',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'TIM0_CH3_IRQ',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'TIM0_CH4_IRQ',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'TIM0_CH5_IRQ',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'TIM0_CH6_IRQ',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'TIM0_CH7_IRQ',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,7,168,3,3
	.word	29235
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,7,171,3,16,4,9
	.byte	'TOM0_CH0_IRQ',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TOM0_CH1_IRQ',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'TOM0_CH2_IRQ',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'TOM0_CH3_IRQ',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'TOM0_CH4_IRQ',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'TOM0_CH5_IRQ',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'TOM0_CH6_IRQ',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'TOM0_CH7_IRQ',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'TOM0_CH8_IRQ',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'TOM0_CH9_IRQ',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'TOM0_CH10_IRQ',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'TOM0_CH11_IRQ',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'TOM0_CH12_IRQ',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'TOM0_CH13_IRQ',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'TOM0_CH14_IRQ',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'TOM0_CH15_IRQ',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'TOM1_CH0_IRQ',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'TOM1_CH1_IRQ',0,4
	.word	16230
	.byte	1,14,2,35,0,9
	.byte	'TOM1_CH2_IRQ',0,4
	.word	16230
	.byte	1,13,2,35,0,9
	.byte	'TOM1_CH3_IRQ',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'TOM1_CH4_IRQ',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'TOM1_CH5_IRQ',0,4
	.word	16230
	.byte	1,10,2,35,0,9
	.byte	'TOM1_CH6_IRQ',0,4
	.word	16230
	.byte	1,9,2,35,0,9
	.byte	'TOM1_CH7_IRQ',0,4
	.word	16230
	.byte	1,8,2,35,0,9
	.byte	'TOM1_CH8_IRQ',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'TOM1_CH9_IRQ',0,4
	.word	16230
	.byte	1,6,2,35,0,9
	.byte	'TOM1_CH10_IRQ',0,4
	.word	16230
	.byte	1,5,2,35,0,9
	.byte	'TOM1_CH11_IRQ',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'TOM1_CH12_IRQ',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'TOM1_CH13_IRQ',0,4
	.word	16230
	.byte	1,2,2,35,0,9
	.byte	'TOM1_CH14_IRQ',0,4
	.word	16230
	.byte	1,1,2,35,0,9
	.byte	'TOM1_CH15_IRQ',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,7,205,3,3
	.word	29514
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,7,208,3,16,4,9
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,7,219,3,3
	.word	30359
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,7,222,3,16,4,9
	.byte	'GTM_EIRQ',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	3,28,2,35,0,9
	.byte	'TIM0_EIRQ',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,7,228,3,3
	.word	30652
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,7,231,3,16,4,9
	.byte	'SEL0',0,4
	.word	16230
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	16230
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	16230
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	16230
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,7,238,3,3
	.word	30806
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,7,241,3,16,4,9
	.byte	'SEL0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'SEL1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'SEL2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'SEL3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'SEL4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'SEL5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'SEL6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'SEL7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'SEL8',0,4
	.word	16230
	.byte	2,14,2,35,0,9
	.byte	'SEL9',0,4
	.word	16230
	.byte	2,12,2,35,0,9
	.byte	'SEL10',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'SEL11',0,4
	.word	16230
	.byte	2,8,2,35,0,9
	.byte	'SEL12',0,4
	.word	16230
	.byte	2,6,2,35,0,9
	.byte	'SEL13',0,4
	.word	16230
	.byte	2,4,2,35,0,9
	.byte	'SEL14',0,4
	.word	16230
	.byte	2,2,2,35,0,9
	.byte	'SEL15',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,7,131,4,3
	.word	30976
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,7,134,4,16,4,9
	.byte	'CH0SEL',0,4
	.word	16230
	.byte	4,28,2,35,0,9
	.byte	'CH1SEL',0,4
	.word	16230
	.byte	4,24,2,35,0,9
	.byte	'CH2SEL',0,4
	.word	16230
	.byte	4,20,2,35,0,9
	.byte	'CH3SEL',0,4
	.word	16230
	.byte	4,16,2,35,0,9
	.byte	'CH4SEL',0,4
	.word	16230
	.byte	4,12,2,35,0,9
	.byte	'CH5SEL',0,4
	.word	16230
	.byte	4,8,2,35,0,9
	.byte	'CH6SEL',0,4
	.word	16230
	.byte	4,4,2,35,0,9
	.byte	'CH7SEL',0,4
	.word	16230
	.byte	4,0,2,35,0,0,6
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,7,144,4,3
	.word	31317
	.byte	8
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,7,147,4,16,4,9
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,7,154,4,3
	.word	31542
	.byte	8
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,7,157,4,16,4,9
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'TRG_AEI_USP_BE',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,7,164,4,3
	.word	31740
	.byte	8
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,7,167,4,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,7,171,4,3
	.word	31936
	.byte	8
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,7,174,4,16,4,9
	.byte	'AEI_TO_XPT',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,7,181,4,3
	.word	32039
	.byte	8
	.byte	'_Ifx_GTM_KRST0_Bits',0,7,184,4,16,4,9
	.byte	'RST',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'RSTSTAT',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_GTM_KRST0_Bits',0,7,189,4,3
	.word	32217
	.byte	8
	.byte	'_Ifx_GTM_KRST1_Bits',0,7,192,4,16,4,9
	.byte	'RST',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_GTM_KRST1_Bits',0,7,196,4,3
	.word	32328
	.byte	8
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,7,199,4,16,4,9
	.byte	'CLR',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,7,203,4,3
	.word	32420
	.byte	8
	.byte	'_Ifx_GTM_OCS_Bits',0,7,206,4,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'SUS',0,4
	.word	16230
	.byte	4,4,2,35,0,9
	.byte	'SUS_P',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'SUSSTA',0,4
	.word	16230
	.byte	1,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_OCS_Bits',0,7,213,4,3
	.word	32516
	.byte	8
	.byte	'_Ifx_GTM_ODA_Bits',0,7,216,4,16,4,9
	.byte	'DDREN',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'DREN',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_GTM_ODA_Bits',0,7,221,4,3
	.word	32662
	.byte	8
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,7,224,4,16,4,9
	.byte	'CV',0,4
	.word	16230
	.byte	27,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'CM',0,4
	.word	16230
	.byte	2,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_OTBU0T_Bits',0,7,230,4,3
	.word	32768
	.byte	8
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,7,233,4,16,4,9
	.byte	'CV',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	4,4,2,35,0,9
	.byte	'EN',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	16230
	.byte	3,0,2,35,0,0,6
	.byte	'Ifx_GTM_OTBU1T_Bits',0,7,239,4,3
	.word	32899
	.byte	8
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,7,242,4,16,4,9
	.byte	'CV',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	4,4,2,35,0,9
	.byte	'EN',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	16230
	.byte	3,0,2,35,0,0,6
	.byte	'Ifx_GTM_OTBU2T_Bits',0,7,248,4,3
	.word	33030
	.byte	8
	.byte	'_Ifx_GTM_OTSC0_Bits',0,7,251,4,16,4,9
	.byte	'B0LMT',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'B0LMI',0,4
	.word	16230
	.byte	4,24,2,35,0,9
	.byte	'B0HMT',0,4
	.word	16230
	.byte	3,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'B0HMI',0,4
	.word	16230
	.byte	4,16,2,35,0,9
	.byte	'B1LMT',0,4
	.word	16230
	.byte	3,13,2,35,0,9
	.byte	'reserved_19',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'B1LMI',0,4
	.word	16230
	.byte	4,8,2,35,0,9
	.byte	'B1HMT',0,4
	.word	16230
	.byte	3,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'B1HMI',0,4
	.word	16230
	.byte	4,0,2,35,0,0,6
	.byte	'Ifx_GTM_OTSC0_Bits',0,7,137,5,3
	.word	33161
	.byte	8
	.byte	'_Ifx_GTM_OTSS_Bits',0,7,140,5,16,4,9
	.byte	'OTGB0',0,4
	.word	16230
	.byte	4,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	4,24,2,35,0,9
	.byte	'OTGB1',0,4
	.word	16230
	.byte	4,20,2,35,0,9
	.byte	'reserved_12',0,4
	.word	16230
	.byte	4,16,2,35,0,9
	.byte	'OTGB2',0,4
	.word	16230
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	16230
	.byte	12,0,2,35,0,0,6
	.byte	'Ifx_GTM_OTSS_Bits',0,7,148,5,3
	.word	33443
	.byte	8
	.byte	'_Ifx_GTM_REV_Bits',0,7,151,5,16,4,9
	.byte	'STEP',0,4
	.word	16230
	.byte	8,24,2,35,0,9
	.byte	'NO',0,4
	.word	16230
	.byte	4,20,2,35,0,9
	.byte	'MINOR',0,4
	.word	16230
	.byte	4,16,2,35,0,9
	.byte	'MAJOR',0,4
	.word	16230
	.byte	4,12,2,35,0,9
	.byte	'DEV_CODE0',0,4
	.word	16230
	.byte	4,8,2,35,0,9
	.byte	'DEV_CODE1',0,4
	.word	16230
	.byte	4,4,2,35,0,9
	.byte	'DEV_CODE2',0,4
	.word	16230
	.byte	4,0,2,35,0,0,6
	.byte	'Ifx_GTM_REV_Bits',0,7,160,5,3
	.word	33615
	.byte	8
	.byte	'_Ifx_GTM_RST_Bits',0,7,163,5,16,4,9
	.byte	'RST',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_GTM_RST_Bits',0,7,167,5,3
	.word	33793
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,7,170,5,16,4,9
	.byte	'BASE',0,4
	.word	16230
	.byte	27,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	16230
	.byte	5,0,2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,7,174,5,3
	.word	33881
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,7,177,5,16,4,9
	.byte	'LOW_RES',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	16230
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,7,182,5,3
	.word	33989
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,7,185,5,16,4,9
	.byte	'BASE',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,7,189,5,3
	.word	34121
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,7,192,5,16,4,9
	.byte	'CH_MODE',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	16230
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,7,197,5,3
	.word	34229
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,7,200,5,16,4,9
	.byte	'BASE',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,7,204,5,3
	.word	34361
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,7,207,5,16,4,9
	.byte	'CH_MODE',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	16230
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	28,0,2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,7,212,5,3
	.word	34469
	.byte	8
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,7,215,5,16,4,9
	.byte	'ENDIS_CH0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CH1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CH2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	16230
	.byte	26,0,2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,7,221,5,3
	.word	34601
	.byte	8
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,7,224,5,16,4,9
	.byte	'SRC_CH0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'SRC_CH1',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'SRC_CH2',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'SRC_CH3',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'SRC_CH4',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'SRC_CH5',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'SRC_CH6',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'SRC_CH7',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,7,235,5,3
	.word	34747
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,7,238,5,16,4,9
	.byte	'CNT',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,7,242,5,3
	.word	34994
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,7,245,5,16,4,9
	.byte	'CNTS',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,7,249,5,3
	.word	35097
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,7,252,5,16,4,9
	.byte	'TIM_EN',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TIM_MODE',0,4
	.word	16230
	.byte	3,28,2,35,0,9
	.byte	'OSM',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'CICTRL',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'TBU0x_SEL',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'GPR0_SEL',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'GPR1_SEL',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'CNTS_SEL',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'DSL',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'ISL',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'ECNT_RESET',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'FLT_EN',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'FLT_CNT_FRQ',0,4
	.word	16230
	.byte	2,13,2,35,0,9
	.byte	'EXT_CAP_EN',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'FLT_MODE_RE',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'FLT_CTR_RE',0,4
	.word	16230
	.byte	1,10,2,35,0,9
	.byte	'FLT_MODE_FE',0,4
	.word	16230
	.byte	1,9,2,35,0,9
	.byte	'FLT_CTR_FE',0,4
	.word	16230
	.byte	1,8,2,35,0,9
	.byte	'CLK_SEL',0,4
	.word	16230
	.byte	3,5,2,35,0,9
	.byte	'FR_ECNT_OFL',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'EGPR0_SEL',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'EGPR1_SEL',0,4
	.word	16230
	.byte	1,2,2,35,0,9
	.byte	'TOCTRL',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,7,150,6,3
	.word	35196
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,7,153,6,16,4,9
	.byte	'ECNT',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,7,157,6,3
	.word	35744
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,7,160,6,16,4,9
	.byte	'EXT_CAP_SRC',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,7,164,6,3
	.word	35850
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,7,167,6,16,4,9
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'TODET_EIRQ_EN',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	16230
	.byte	26,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,7,176,6,3
	.word	35964
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,7,179,6,16,4,9
	.byte	'FLT_FE',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,7,183,6,3
	.word	36219
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,7,186,6,16,4,9
	.byte	'FLT_RE',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,7,190,6,3
	.word	36331
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,7,193,6,16,4,9
	.byte	'GPR0',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,7,197,6,3
	.word	36443
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,7,200,6,16,4,9
	.byte	'GPR1',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,7,204,6,3
	.word	36542
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,7,207,6,16,4,9
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'TODET_IRQ_EN',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	16230
	.byte	26,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,7,216,6,3
	.word	36641
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,7,219,6,16,4,9
	.byte	'TRG_NEWVAL',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TRG_ECNTOFL',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'TRG_CNTOFL',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'TRG_GPRzOFL',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'TRG_TODET',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'TRG_GLITCHDET',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	16230
	.byte	26,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,7,228,6,3
	.word	36888
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,7,231,6,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,7,235,6,3
	.word	37127
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,7,238,6,16,4,9
	.byte	'NEWVAL',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'TODET',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	16230
	.byte	26,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,7,247,6,3
	.word	37244
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,7,250,6,16,4,9
	.byte	'TO_CNT',0,4
	.word	16230
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,7,254,6,3
	.word	37457
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,7,129,7,16,4,9
	.byte	'TOV',0,4
	.word	16230
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	20,4,2,35,0,9
	.byte	'TCS',0,4
	.word	16230
	.byte	3,1,2,35,0,9
	.byte	'reserved_31',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,7,135,7,3
	.word	37564
	.byte	8
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,7,138,7,16,4,9
	.byte	'VAL_0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'MODE_0',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'VAL_1',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'MODE_1',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'VAL_2',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'MODE_2',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'VAL_3',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'MODE_3',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'VAL_4',0,4
	.word	16230
	.byte	2,14,2,35,0,9
	.byte	'MODE_4',0,4
	.word	16230
	.byte	2,12,2,35,0,9
	.byte	'VAL_5',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'MODE_5',0,4
	.word	16230
	.byte	2,8,2,35,0,9
	.byte	'VAL_6',0,4
	.word	16230
	.byte	2,6,2,35,0,9
	.byte	'MODE_6',0,4
	.word	16230
	.byte	2,4,2,35,0,9
	.byte	'VAL_7',0,4
	.word	16230
	.byte	2,2,2,35,0,9
	.byte	'MODE_7',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,7,156,7,3
	.word	37706
	.byte	8
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,7,159,7,16,4,9
	.byte	'F_OUT',0,4
	.word	16230
	.byte	8,24,2,35,0,9
	.byte	'F_IN',0,4
	.word	16230
	.byte	8,16,2,35,0,9
	.byte	'TIM_IN',0,4
	.word	16230
	.byte	8,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	16230
	.byte	8,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,7,165,7,3
	.word	38051
	.byte	8
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,7,168,7,16,4,9
	.byte	'RST_CH0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_GTM_TIM_RST_Bits',0,7,179,7,3
	.word	38192
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,7,182,7,16,4,9
	.byte	'CM0',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,7,186,7,3
	.word	38425
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,7,189,7,16,4,9
	.byte	'CM1',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,7,193,7,3
	.word	38528
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,7,196,7,16,4,9
	.byte	'CN0',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,7,200,7,3
	.word	38631
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,7,203,7,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	11,21,2,35,0,9
	.byte	'SL',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'CLK_SRC_SR',0,4
	.word	16230
	.byte	3,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	16230
	.byte	5,12,2,35,0,9
	.byte	'RST_CCU0',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'OSM_TRIG',0,4
	.word	16230
	.byte	1,10,2,35,0,9
	.byte	'EXT_TRIG',0,4
	.word	16230
	.byte	1,9,2,35,0,9
	.byte	'EXTTRIGOUT',0,4
	.word	16230
	.byte	1,8,2,35,0,9
	.byte	'TRIGOUT',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	16230
	.byte	1,6,2,35,0,9
	.byte	'OSM',0,4
	.word	16230
	.byte	1,5,2,35,0,9
	.byte	'BITREV',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'reserved_28',0,4
	.word	16230
	.byte	4,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,7,218,7,3
	.word	38734
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,7,221,7,16,4,9
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,7,226,7,3
	.word	39062
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,7,229,7,16,4,9
	.byte	'TRG_CCU0TC0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TRG_CCU1TC0',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,7,234,7,3
	.word	39205
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,7,237,7,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,7,241,7,3
	.word	39354
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,7,244,7,16,4,9
	.byte	'CCU0TC',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'CCU1TC',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,7,249,7,3
	.word	39471
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,7,252,7,16,4,9
	.byte	'SR0',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,7,128,8,3
	.word	39608
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,7,131,8,16,4,9
	.byte	'SR1',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,7,135,8,3
	.word	39711
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,7,138,8,16,4,9
	.byte	'OL',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,7,142,8,3
	.word	39814
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,7,145,8,16,4,9
	.byte	'ACT_TB',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'TB_TRIG',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'TBU_SEL',0,4
	.word	16230
	.byte	2,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	16230
	.byte	5,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,7,151,8,3
	.word	39917
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,7,154,8,16,4,9
	.byte	'ENDIS_CTRL0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CTRL1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CTRL2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_CTRL3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_CTRL4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_CTRL5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_CTRL6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_CTRL7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,7,165,8,3
	.word	40071
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,7,168,8,16,4,9
	.byte	'ENDIS_STAT0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_STAT1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_STAT2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_STAT3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_STAT4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_STAT5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_STAT6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_STAT7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,7,179,8,3
	.word	40361
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,7,182,8,16,4,9
	.byte	'FUPD_CTRL0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'FUPD_CTRL1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'FUPD_CTRL2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'FUPD_CTRL3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'FUPD_CTRL4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'FUPD_CTRL5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'FUPD_CTRL6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'FUPD_CTRL7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'RSTCN0_CH0',0,4
	.word	16230
	.byte	2,14,2,35,0,9
	.byte	'RSTCN0_CH1',0,4
	.word	16230
	.byte	2,12,2,35,0,9
	.byte	'RSTCN0_CH2',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'RSTCN0_CH3',0,4
	.word	16230
	.byte	2,8,2,35,0,9
	.byte	'RSTCN0_CH4',0,4
	.word	16230
	.byte	2,6,2,35,0,9
	.byte	'RSTCN0_CH5',0,4
	.word	16230
	.byte	2,4,2,35,0,9
	.byte	'RSTCN0_CH6',0,4
	.word	16230
	.byte	2,2,2,35,0,9
	.byte	'RSTCN0_CH7',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,7,200,8,3
	.word	40651
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,7,203,8,16,4,9
	.byte	'HOST_TRIG',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	7,24,2,35,0,9
	.byte	'RST_CH0',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'UPEN_CTRL0',0,4
	.word	16230
	.byte	2,14,2,35,0,9
	.byte	'UPEN_CTRL1',0,4
	.word	16230
	.byte	2,12,2,35,0,9
	.byte	'UPEN_CTRL2',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'UPEN_CTRL3',0,4
	.word	16230
	.byte	2,8,2,35,0,9
	.byte	'UPEN_CTRL4',0,4
	.word	16230
	.byte	2,6,2,35,0,9
	.byte	'UPEN_CTRL5',0,4
	.word	16230
	.byte	2,4,2,35,0,9
	.byte	'UPEN_CTRL6',0,4
	.word	16230
	.byte	2,2,2,35,0,9
	.byte	'UPEN_CTRL7',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,7,223,8,3
	.word	41084
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,7,226,8,16,4,9
	.byte	'INT_TRIG0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'INT_TRIG1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'INT_TRIG2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'INT_TRIG3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'INT_TRIG4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'INT_TRIG5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'INT_TRIG6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'INT_TRIG7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,7,237,8,3
	.word	41534
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,7,240,8,16,4,9
	.byte	'OUTEN_CTRL0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_CTRL1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_CTRL2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_CTRL3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_CTRL4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_CTRL5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_CTRL6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_CTRL7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,7,251,8,3
	.word	41804
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,7,254,8,16,4,9
	.byte	'OUTEN_STAT0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_STAT1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_STAT2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_STAT3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_STAT4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_STAT5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_STAT6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_STAT7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,7,137,9,3
	.word	42094
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,7,140,9,16,4,9
	.byte	'ACT_TB',0,4
	.word	16230
	.byte	24,8,2,35,0,9
	.byte	'TB_TRIG',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'TBU_SEL',0,4
	.word	16230
	.byte	2,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	16230
	.byte	5,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,7,146,9,3
	.word	42384
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,7,149,9,16,4,9
	.byte	'ENDIS_CTRL0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CTRL1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CTRL2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_CTRL3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_CTRL4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_CTRL5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_CTRL6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_CTRL7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,7,160,9,3
	.word	42538
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,7,163,9,16,4,9
	.byte	'ENDIS_STAT0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_STAT1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_STAT2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_STAT3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_STAT4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_STAT5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_STAT6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_STAT7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,7,174,9,3
	.word	42828
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,7,177,9,16,4,9
	.byte	'FUPD_CTRL0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'FUPD_CTRL1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'FUPD_CTRL2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'FUPD_CTRL3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'FUPD_CTRL4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'FUPD_CTRL5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'FUPD_CTRL6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'FUPD_CTRL7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'RSTCN0_CH0',0,4
	.word	16230
	.byte	2,14,2,35,0,9
	.byte	'RSTCN0_CH1',0,4
	.word	16230
	.byte	2,12,2,35,0,9
	.byte	'RSTCN0_CH2',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'RSTCN0_CH3',0,4
	.word	16230
	.byte	2,8,2,35,0,9
	.byte	'RSTCN0_CH4',0,4
	.word	16230
	.byte	2,6,2,35,0,9
	.byte	'RSTCN0_CH5',0,4
	.word	16230
	.byte	2,4,2,35,0,9
	.byte	'RSTCN0_CH6',0,4
	.word	16230
	.byte	2,2,2,35,0,9
	.byte	'RSTCN0_CH7',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,7,195,9,3
	.word	43118
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,7,198,9,16,4,9
	.byte	'HOST_TRIG',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	7,24,2,35,0,9
	.byte	'RST_CH0',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'UPEN_CTRL0',0,4
	.word	16230
	.byte	2,14,2,35,0,9
	.byte	'UPEN_CTRL1',0,4
	.word	16230
	.byte	2,12,2,35,0,9
	.byte	'UPEN_CTRL2',0,4
	.word	16230
	.byte	2,10,2,35,0,9
	.byte	'UPEN_CTRL3',0,4
	.word	16230
	.byte	2,8,2,35,0,9
	.byte	'UPEN_CTRL4',0,4
	.word	16230
	.byte	2,6,2,35,0,9
	.byte	'UPEN_CTRL5',0,4
	.word	16230
	.byte	2,4,2,35,0,9
	.byte	'UPEN_CTRL6',0,4
	.word	16230
	.byte	2,2,2,35,0,9
	.byte	'UPEN_CTRL7',0,4
	.word	16230
	.byte	2,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,7,218,9,3
	.word	43551
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,7,221,9,16,4,9
	.byte	'INT_TRIG0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'INT_TRIG1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'INT_TRIG2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'INT_TRIG3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'INT_TRIG4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'INT_TRIG5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'INT_TRIG6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'INT_TRIG7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,7,232,9,3
	.word	44001
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,7,235,9,16,4,9
	.byte	'OUTEN_CTRL0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_CTRL1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_CTRL2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_CTRL3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_CTRL4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_CTRL5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_CTRL6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_CTRL7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,7,246,9,3
	.word	44271
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,7,249,9,16,4,9
	.byte	'OUTEN_STAT0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_STAT1',0,4
	.word	16230
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_STAT2',0,4
	.word	16230
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_STAT3',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_STAT4',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_STAT5',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_STAT6',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_STAT7',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,7,132,10,3
	.word	44561
	.byte	10,7,140,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22923
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ACCEN0',0,7,145,10,3
	.word	44851
	.byte	10,7,148,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23480
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ACCEN1',0,7,153,10,3
	.word	44915
	.byte	10,7,156,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23557
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,7,161,10,3
	.word	44979
	.byte	10,7,164,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23711
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,7,169,10,3
	.word	45049
	.byte	10,7,172,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23865
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,7,177,10,3
	.word	45119
	.byte	10,7,180,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23993
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_BRIDGE_MODE',0,7,185,10,3
	.word	45189
	.byte	10,7,188,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24300
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,7,193,10,3
	.word	45258
	.byte	10,7,196,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24502
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,7,201,10,3
	.word	45327
	.byte	10,7,204,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24615
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CLC',0,7,209,10,3
	.word	45396
	.byte	10,7,212,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24758
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,7,217,10,3
	.word	45457
	.byte	10,7,220,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24875
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,7,225,10,3
	.word	45530
	.byte	10,7,228,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25010
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,7,233,10,3
	.word	45602
	.byte	10,7,236,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25145
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_CLK_EN',0,7,241,10,3
	.word	45674
	.byte	10,7,244,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25465
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,7,249,10,3
	.word	45742
	.byte	10,7,252,10,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25577
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,7,129,11,3
	.word	45812
	.byte	10,7,132,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25689
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,7,137,11,3
	.word	45882
	.byte	10,7,140,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25805
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,7,145,11,3
	.word	45954
	.byte	10,7,148,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25917
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,7,153,11,3
	.word	46024
	.byte	10,7,156,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	26029
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_CTRL',0,7,161,11,3
	.word	46094
	.byte	10,7,164,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	26182
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,7,169,11,3
	.word	46156
	.byte	10,7,172,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	26694
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,7,177,11,3
	.word	46226
	.byte	10,7,180,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	27315
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,7,185,11,3
	.word	46296
	.byte	10,7,188,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28038
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_DTM_CTRL',0,7,193,11,3
	.word	46369
	.byte	10,7,196,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28182
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_DTM_DTV_CH',0,7,201,11,3
	.word	46435
	.byte	10,7,204,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28331
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,7,209,11,3
	.word	46503
	.byte	10,7,212,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28546
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_EIRQ_EN',0,7,217,11,3
	.word	46572
	.byte	10,7,220,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28750
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_HW_CONF',0,7,225,11,3
	.word	46637
	.byte	10,7,228,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29107
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_0',0,7,233,11,3
	.word	46702
	.byte	10,7,236,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29235
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_2',0,7,241,11,3
	.word	46770
	.byte	10,7,244,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29514
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_6',0,7,249,11,3
	.word	46838
	.byte	10,7,252,11,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	30359
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,7,129,12,3
	.word	46906
	.byte	10,7,132,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	30652
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,7,137,12,3
	.word	46977
	.byte	10,7,140,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	30806
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,7,145,12,3
	.word	47047
	.byte	10,7,148,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	30976
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,7,153,12,3
	.word	47124
	.byte	10,7,156,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31317
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,7,161,12,3
	.word	47199
	.byte	10,7,164,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31542
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_IRQ_EN',0,7,169,12,3
	.word	47275
	.byte	10,7,172,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31740
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_IRQ_FORCINT',0,7,177,12,3
	.word	47339
	.byte	10,7,180,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	31936
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_IRQ_MODE',0,7,185,12,3
	.word	47408
	.byte	10,7,188,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32039
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,7,193,12,3
	.word	47474
	.byte	10,7,196,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32217
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_KRST0',0,7,201,12,3
	.word	47542
	.byte	10,7,204,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32328
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_KRST1',0,7,209,12,3
	.word	47605
	.byte	10,7,212,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32420
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_KRSTCLR',0,7,217,12,3
	.word	47668
	.byte	10,7,220,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32516
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_OCS',0,7,225,12,3
	.word	47733
	.byte	10,7,228,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32662
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_ODA',0,7,233,12,3
	.word	47794
	.byte	10,7,236,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32768
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_OTBU0T',0,7,241,12,3
	.word	47855
	.byte	10,7,244,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	32899
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_OTBU1T',0,7,249,12,3
	.word	47919
	.byte	10,7,252,12,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33030
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_OTBU2T',0,7,129,13,3
	.word	47983
	.byte	10,7,132,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33161
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_OTSC0',0,7,137,13,3
	.word	48047
	.byte	10,7,140,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33443
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_OTSS',0,7,145,13,3
	.word	48110
	.byte	10,7,148,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33615
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_REV',0,7,153,13,3
	.word	48172
	.byte	10,7,156,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33793
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_RST',0,7,161,13,3
	.word	48233
	.byte	10,7,164,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33881
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,7,169,13,3
	.word	48294
	.byte	10,7,172,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	33989
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,7,177,13,3
	.word	48364
	.byte	10,7,180,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34121
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,7,185,13,3
	.word	48434
	.byte	10,7,188,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34229
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,7,193,13,3
	.word	48504
	.byte	10,7,196,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34361
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,7,201,13,3
	.word	48574
	.byte	10,7,204,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34469
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,7,209,13,3
	.word	48644
	.byte	10,7,212,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34601
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TBU_CHEN',0,7,217,13,3
	.word	48714
	.byte	10,7,220,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34747
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,7,225,13,3
	.word	48780
	.byte	10,7,228,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	34994
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_CNT',0,7,233,13,3
	.word	48852
	.byte	10,7,236,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35097
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,7,241,13,3
	.word	48920
	.byte	10,7,244,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35196
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,7,249,13,3
	.word	48989
	.byte	10,7,252,13,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35744
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,7,129,14,3
	.word	49058
	.byte	10,7,132,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35850
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,7,137,14,3
	.word	49127
	.byte	10,7,140,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35964
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,7,145,14,3
	.word	49197
	.byte	10,7,148,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36219
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,7,153,14,3
	.word	49269
	.byte	10,7,156,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36331
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,7,161,14,3
	.word	49340
	.byte	10,7,164,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36443
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,7,169,14,3
	.word	49411
	.byte	10,7,172,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36542
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,7,177,14,3
	.word	49480
	.byte	10,7,180,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36641
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,7,185,14,3
	.word	49549
	.byte	10,7,188,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36888
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,7,193,14,3
	.word	49620
	.byte	10,7,196,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37127
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,7,201,14,3
	.word	49696
	.byte	10,7,204,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37244
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,7,209,14,3
	.word	49769
	.byte	10,7,212,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37457
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,7,217,14,3
	.word	49844
	.byte	10,7,220,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37564
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,7,225,14,3
	.word	49913
	.byte	10,7,228,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37706
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_IN_SRC',0,7,233,14,3
	.word	49982
	.byte	10,7,236,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38051
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_INP_VAL',0,7,241,14,3
	.word	50050
	.byte	10,7,244,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38192
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TIM_RST',0,7,249,14,3
	.word	50119
	.byte	10,7,252,14,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38425
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_CM0',0,7,129,15,3
	.word	50184
	.byte	10,7,132,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38528
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_CM1',0,7,137,15,3
	.word	50252
	.byte	10,7,140,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38631
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_CN0',0,7,145,15,3
	.word	50320
	.byte	10,7,148,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38734
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,7,153,15,3
	.word	50388
	.byte	10,7,156,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39062
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,7,161,15,3
	.word	50457
	.byte	10,7,164,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39205
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,7,169,15,3
	.word	50528
	.byte	10,7,172,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39354
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,7,177,15,3
	.word	50604
	.byte	10,7,180,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39471
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,7,185,15,3
	.word	50677
	.byte	10,7,188,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39608
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_SR0',0,7,193,15,3
	.word	50752
	.byte	10,7,196,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39711
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_SR1',0,7,201,15,3
	.word	50820
	.byte	10,7,204,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39814
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_CH_STAT',0,7,209,15,3
	.word	50888
	.byte	10,7,212,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39917
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,7,217,15,3
	.word	50957
	.byte	10,7,220,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40071
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,7,225,15,3
	.word	51030
	.byte	10,7,228,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40361
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,7,233,15,3
	.word	51107
	.byte	10,7,236,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40651
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,7,241,15,3
	.word	51184
	.byte	10,7,244,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41084
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,7,249,15,3
	.word	51260
	.byte	10,7,252,15,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41534
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,7,129,16,3
	.word	51335
	.byte	10,7,132,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41804
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,7,137,16,3
	.word	51410
	.byte	10,7,140,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42094
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,7,145,16,3
	.word	51487
	.byte	10,7,148,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42384
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,7,153,16,3
	.word	51564
	.byte	10,7,156,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42538
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,7,161,16,3
	.word	51637
	.byte	10,7,164,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42828
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,7,169,16,3
	.word	51714
	.byte	10,7,172,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43118
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,7,177,16,3
	.word	51791
	.byte	10,7,180,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43551
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,7,185,16,3
	.word	51867
	.byte	10,7,188,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	44001
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,7,193,16,3
	.word	51942
	.byte	10,7,196,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	44271
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,7,201,16,3
	.word	52017
	.byte	10,7,204,16,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	44561
	.byte	2,35,0,0,6
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,7,209,16,3
	.word	52094
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,7,220,16,25,4,11
	.byte	'CTRL',0,4
	.word	45457
	.byte	2,35,0,0,12
	.word	52171
	.byte	6
	.byte	'Ifx_GTM_CMU_CLK0_5',0,7,223,16,3
	.word	52212
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_6',0,7,226,16,25,4,11
	.byte	'CTRL',0,4
	.word	45530
	.byte	2,35,0,0,12
	.word	52245
	.byte	6
	.byte	'Ifx_GTM_CMU_CLK_6',0,7,229,16,3
	.word	52285
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_7',0,7,232,16,25,4,11
	.byte	'CTRL',0,4
	.word	45602
	.byte	2,35,0,0,12
	.word	52317
	.byte	6
	.byte	'Ifx_GTM_CMU_CLK_7',0,7,235,16,3
	.word	52357
	.byte	8
	.byte	'_Ifx_GTM_CMU_ECLK',0,7,238,16,25,8,11
	.byte	'NUM',0,4
	.word	45812
	.byte	2,35,0,11
	.byte	'DEN',0,4
	.word	45742
	.byte	2,35,4,0,12
	.word	52389
	.byte	6
	.byte	'Ifx_GTM_CMU_ECLK',0,7,242,16,3
	.word	52440
	.byte	8
	.byte	'_Ifx_GTM_CMU_FXCLK',0,7,245,16,25,4,11
	.byte	'CTRL',0,4
	.word	45882
	.byte	2,35,0,0,12
	.word	52471
	.byte	6
	.byte	'Ifx_GTM_CMU_FXCLK',0,7,248,16,3
	.word	52511
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,7,251,16,25,4,11
	.byte	'OUTSEL',0,4
	.word	47047
	.byte	2,35,0,0,12
	.word	52543
	.byte	6
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,7,254,16,3
	.word	52588
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_T',0,7,129,17,25,32,13,32
	.word	47124
	.byte	14,7,0,11
	.byte	'OUTSEL',0,32
	.word	52649
	.byte	2,35,0,0,12
	.word	52623
	.byte	6
	.byte	'Ifx_GTM_INOUTSEL_T',0,7,132,17,3
	.word	52675
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,7,135,17,25,32,11
	.byte	'INSEL',0,4
	.word	47199
	.byte	2,35,0,13,28
	.word	227
	.byte	14,27,0,11
	.byte	'reserved_4',0,28
	.word	52751
	.byte	2,35,4,0,12
	.word	52708
	.byte	6
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,7,139,17,3
	.word	52781
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH',0,7,142,17,25,116,11
	.byte	'GPR0',0,4
	.word	49411
	.byte	2,35,0,11
	.byte	'GPR1',0,4
	.word	49480
	.byte	2,35,4,11
	.byte	'CNT',0,4
	.word	48852
	.byte	2,35,8,11
	.byte	'ECNT',0,4
	.word	49058
	.byte	2,35,12,11
	.byte	'CNTS',0,4
	.word	48920
	.byte	2,35,16,11
	.byte	'TDUC',0,4
	.word	49844
	.byte	2,35,20,11
	.byte	'TDUV',0,4
	.word	49913
	.byte	2,35,24,11
	.byte	'FLT_RE',0,4
	.word	49340
	.byte	2,35,28,11
	.byte	'FLT_FE',0,4
	.word	49269
	.byte	2,35,32,11
	.byte	'CTRL',0,4
	.word	48989
	.byte	2,35,36,11
	.byte	'ECTRL',0,4
	.word	49127
	.byte	2,35,40,11
	.byte	'IRQ_NOTIFY',0,4
	.word	49769
	.byte	2,35,44,11
	.byte	'IRQ_EN',0,4
	.word	49549
	.byte	2,35,48,11
	.byte	'IRQ_FORCINT',0,4
	.word	49620
	.byte	2,35,52,11
	.byte	'IRQ_MODE',0,4
	.word	49696
	.byte	2,35,56,11
	.byte	'EIRQ_EN',0,4
	.word	49197
	.byte	2,35,60,13,52
	.word	227
	.byte	14,51,0,11
	.byte	'reserved_40',0,52
	.word	53088
	.byte	2,35,64,0,12
	.word	52816
	.byte	6
	.byte	'Ifx_GTM_TIM_CH',0,7,161,17,3
	.word	53119
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH',0,7,164,17,25,48,11
	.byte	'CTRL',0,4
	.word	50388
	.byte	2,35,0,11
	.byte	'SR0',0,4
	.word	50752
	.byte	2,35,4,11
	.byte	'SR1',0,4
	.word	50820
	.byte	2,35,8,11
	.byte	'CM0',0,4
	.word	50184
	.byte	2,35,12,11
	.byte	'CM1',0,4
	.word	50252
	.byte	2,35,16,11
	.byte	'CN0',0,4
	.word	50320
	.byte	2,35,20,11
	.byte	'STAT',0,4
	.word	50888
	.byte	2,35,24,11
	.byte	'IRQ_NOTIFY',0,4
	.word	50677
	.byte	2,35,28,11
	.byte	'IRQ_EN',0,4
	.word	50457
	.byte	2,35,32,11
	.byte	'IRQ_FORCINT',0,4
	.word	50528
	.byte	2,35,36,11
	.byte	'IRQ_MODE',0,4
	.word	50604
	.byte	2,35,40,13,4
	.word	227
	.byte	14,3,0,11
	.byte	'reserved_2C',0,4
	.word	53338
	.byte	2,35,44,0,12
	.word	53148
	.byte	6
	.byte	'Ifx_GTM_TOM_CH',0,7,178,17,3
	.word	53369
	.byte	8
	.byte	'_Ifx_GTM_BRIDGE',0,7,191,17,25,12,11
	.byte	'MODE',0,4
	.word	45189
	.byte	2,35,0,11
	.byte	'PTR1',0,4
	.word	45258
	.byte	2,35,4,11
	.byte	'PTR2',0,4
	.word	45327
	.byte	2,35,8,0,12
	.word	53398
	.byte	6
	.byte	'Ifx_GTM_BRIDGE',0,7,196,17,3
	.word	53463
	.byte	8
	.byte	'_Ifx_GTM_CMU',0,7,199,17,25,72,11
	.byte	'CLK_EN',0,4
	.word	45674
	.byte	2,35,0,11
	.byte	'GCLK_NUM',0,4
	.word	46024
	.byte	2,35,4,11
	.byte	'GCLK_DEN',0,4
	.word	45954
	.byte	2,35,8,13,24
	.word	52171
	.byte	14,5,0,12
	.word	53563
	.byte	11
	.byte	'CLK0_5',0,24
	.word	53572
	.byte	2,35,12,12
	.word	52245
	.byte	11
	.byte	'CLK_6',0,4
	.word	53593
	.byte	2,35,36,12
	.word	52317
	.byte	11
	.byte	'CLK_7',0,4
	.word	53613
	.byte	2,35,40,13,24
	.word	52389
	.byte	14,2,0,12
	.word	53633
	.byte	11
	.byte	'ECLK',0,24
	.word	53642
	.byte	2,35,44,12
	.word	52471
	.byte	11
	.byte	'FXCLK',0,4
	.word	53661
	.byte	2,35,68,0,12
	.word	53492
	.byte	6
	.byte	'Ifx_GTM_CMU',0,7,209,17,3
	.word	53682
	.byte	8
	.byte	'_Ifx_GTM_DTM',0,7,212,17,25,36,11
	.byte	'CTRL',0,4
	.word	46369
	.byte	2,35,0,11
	.byte	'CH_CTRL1',0,4
	.word	46156
	.byte	2,35,4,11
	.byte	'CH_CTRL2',0,4
	.word	46226
	.byte	2,35,8,11
	.byte	'CH_CTRL2_SR',0,4
	.word	46296
	.byte	2,35,12,11
	.byte	'PS_CTRL',0,4
	.word	46503
	.byte	2,35,16,13,16
	.word	46435
	.byte	14,3,0,11
	.byte	'DTV_CH',0,16
	.word	53815
	.byte	2,35,20,0,12
	.word	53708
	.byte	6
	.byte	'Ifx_GTM_DTM',0,7,220,17,3
	.word	53841
	.byte	8
	.byte	'_Ifx_GTM_ICM',0,7,223,17,25,60,11
	.byte	'IRQG_0',0,4
	.word	46702
	.byte	2,35,0,11
	.byte	'reserved_4',0,4
	.word	53338
	.byte	2,35,4,11
	.byte	'IRQG_2',0,4
	.word	46770
	.byte	2,35,8,13,12
	.word	227
	.byte	14,11,0,11
	.byte	'reserved_C',0,12
	.word	53938
	.byte	2,35,12,11
	.byte	'IRQG_6',0,4
	.word	46838
	.byte	2,35,24,13,20
	.word	227
	.byte	14,19,0,11
	.byte	'reserved_1C',0,20
	.word	53983
	.byte	2,35,28,11
	.byte	'IRQG_MEI',0,4
	.word	46977
	.byte	2,35,48,11
	.byte	'reserved_34',0,4
	.word	53338
	.byte	2,35,52,11
	.byte	'IRQG_CEI1',0,4
	.word	46906
	.byte	2,35,56,0,12
	.word	53867
	.byte	6
	.byte	'Ifx_GTM_ICM',0,7,234,17,3
	.word	54072
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL',0,7,237,17,25,148,1,13,32
	.word	52708
	.byte	14,0,0,12
	.word	54123
	.byte	11
	.byte	'TIM',0,32
	.word	54132
	.byte	2,35,0,12
	.word	52623
	.byte	11
	.byte	'T',0,32
	.word	54150
	.byte	2,35,32,13,80
	.word	227
	.byte	14,79,0,11
	.byte	'reserved_40',0,80
	.word	54166
	.byte	2,35,64,12
	.word	52543
	.byte	11
	.byte	'CAN',0,4
	.word	54196
	.byte	3,35,144,1,0,12
	.word	54098
	.byte	6
	.byte	'Ifx_GTM_INOUTSEL',0,7,243,17,3
	.word	54216
	.byte	8
	.byte	'_Ifx_GTM_TBU',0,7,246,17,25,28,11
	.byte	'CHEN',0,4
	.word	48714
	.byte	2,35,0,11
	.byte	'CH0_CTRL',0,4
	.word	48364
	.byte	2,35,4,11
	.byte	'CH0_BASE',0,4
	.word	48294
	.byte	2,35,8,11
	.byte	'CH1_CTRL',0,4
	.word	48504
	.byte	2,35,12,11
	.byte	'CH1_BASE',0,4
	.word	48434
	.byte	2,35,16,11
	.byte	'CH2_CTRL',0,4
	.word	48644
	.byte	2,35,20,11
	.byte	'CH2_BASE',0,4
	.word	48574
	.byte	2,35,24,0,12
	.word	54247
	.byte	6
	.byte	'Ifx_GTM_TBU',0,7,255,17,3
	.word	54389
	.byte	8
	.byte	'_Ifx_GTM_TIM',0,7,130,18,25,128,8,12
	.word	52816
	.byte	11
	.byte	'CH0',0,116
	.word	54435
	.byte	2,35,0,11
	.byte	'INP_VAL',0,4
	.word	50050
	.byte	2,35,116,11
	.byte	'IN_SRC',0,4
	.word	49982
	.byte	2,35,120,11
	.byte	'RST',0,4
	.word	50119
	.byte	2,35,124,12
	.word	52816
	.byte	11
	.byte	'CH1',0,116
	.word	54499
	.byte	3,35,128,1,11
	.byte	'reserved_F4',0,12
	.word	53938
	.byte	3,35,244,1,12
	.word	52816
	.byte	11
	.byte	'CH2',0,116
	.word	54540
	.byte	3,35,128,2,11
	.byte	'reserved_174',0,12
	.word	53938
	.byte	3,35,244,2,12
	.word	52816
	.byte	11
	.byte	'CH3',0,116
	.word	54582
	.byte	3,35,128,3,11
	.byte	'reserved_1F4',0,12
	.word	53938
	.byte	3,35,244,3,12
	.word	52816
	.byte	11
	.byte	'CH4',0,116
	.word	54624
	.byte	3,35,128,4,11
	.byte	'reserved_274',0,12
	.word	53938
	.byte	3,35,244,4,12
	.word	52816
	.byte	11
	.byte	'CH5',0,116
	.word	54666
	.byte	3,35,128,5,11
	.byte	'reserved_2F4',0,12
	.word	53938
	.byte	3,35,244,5,12
	.word	52816
	.byte	11
	.byte	'CH6',0,116
	.word	54708
	.byte	3,35,128,6,11
	.byte	'reserved_374',0,12
	.word	53938
	.byte	3,35,244,6,12
	.word	52816
	.byte	11
	.byte	'CH7',0,116
	.word	54750
	.byte	3,35,128,7,11
	.byte	'reserved_3F4',0,12
	.word	53938
	.byte	3,35,244,7,0,12
	.word	54415
	.byte	6
	.byte	'Ifx_GTM_TIM',0,7,150,18,3
	.word	54793
	.byte	8
	.byte	'_Ifx_GTM_TOM',0,7,153,18,25,128,16,12
	.word	53148
	.byte	11
	.byte	'CH0',0,48
	.word	54839
	.byte	2,35,0,11
	.byte	'TGC0_GLB_CTRL',0,4
	.word	51260
	.byte	2,35,48,11
	.byte	'TGC0_ACT_TB',0,4
	.word	50957
	.byte	2,35,52,11
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	51184
	.byte	2,35,56,11
	.byte	'TGC0_INT_TRIG',0,4
	.word	51335
	.byte	2,35,60,12
	.word	53148
	.byte	11
	.byte	'CH1',0,48
	.word	54948
	.byte	2,35,64,11
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	51030
	.byte	2,35,112,11
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	51107
	.byte	2,35,116,11
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	51410
	.byte	2,35,120,11
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	51487
	.byte	2,35,124,12
	.word	53148
	.byte	11
	.byte	'CH2',0,48
	.word	55066
	.byte	3,35,128,1,13,16
	.word	227
	.byte	14,15,0,11
	.byte	'reserved_B0',0,16
	.word	55085
	.byte	3,35,176,1,12
	.word	53148
	.byte	11
	.byte	'CH3',0,48
	.word	55116
	.byte	3,35,192,1,11
	.byte	'reserved_F0',0,16
	.word	55085
	.byte	3,35,240,1,12
	.word	53148
	.byte	11
	.byte	'CH4',0,48
	.word	55157
	.byte	3,35,128,2,11
	.byte	'reserved_130',0,16
	.word	55085
	.byte	3,35,176,2,12
	.word	53148
	.byte	11
	.byte	'CH5',0,48
	.word	55199
	.byte	3,35,192,2,11
	.byte	'reserved_170',0,16
	.word	55085
	.byte	3,35,240,2,12
	.word	53148
	.byte	11
	.byte	'CH6',0,48
	.word	55241
	.byte	3,35,128,3,11
	.byte	'reserved_1B0',0,16
	.word	55085
	.byte	3,35,176,3,12
	.word	53148
	.byte	11
	.byte	'CH7',0,48
	.word	55283
	.byte	3,35,192,3,11
	.byte	'reserved_1F0',0,16
	.word	55085
	.byte	3,35,240,3,12
	.word	53148
	.byte	11
	.byte	'CH8',0,48
	.word	55325
	.byte	3,35,128,4,11
	.byte	'TGC1_GLB_CTRL',0,4
	.word	51867
	.byte	3,35,176,4,11
	.byte	'TGC1_ACT_TB',0,4
	.word	51564
	.byte	3,35,180,4,11
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	51791
	.byte	3,35,184,4,11
	.byte	'TGC1_INT_TRIG',0,4
	.word	51942
	.byte	3,35,188,4,12
	.word	53148
	.byte	11
	.byte	'CH9',0,48
	.word	55439
	.byte	3,35,192,4,11
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	51637
	.byte	3,35,240,4,11
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	51714
	.byte	3,35,244,4,11
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	52017
	.byte	3,35,248,4,11
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	52094
	.byte	3,35,252,4,12
	.word	53148
	.byte	11
	.byte	'CH10',0,48
	.word	55562
	.byte	3,35,128,5,11
	.byte	'reserved_2B0',0,16
	.word	55085
	.byte	3,35,176,5,12
	.word	53148
	.byte	11
	.byte	'CH11',0,48
	.word	55605
	.byte	3,35,192,5,11
	.byte	'reserved_2F0',0,16
	.word	55085
	.byte	3,35,240,5,12
	.word	53148
	.byte	11
	.byte	'CH12',0,48
	.word	55648
	.byte	3,35,128,6,11
	.byte	'reserved_330',0,16
	.word	55085
	.byte	3,35,176,6,12
	.word	53148
	.byte	11
	.byte	'CH13',0,48
	.word	55691
	.byte	3,35,192,6,11
	.byte	'reserved_370',0,16
	.word	55085
	.byte	3,35,240,6,12
	.word	53148
	.byte	11
	.byte	'CH14',0,48
	.word	55734
	.byte	3,35,128,7,11
	.byte	'reserved_3B0',0,16
	.word	55085
	.byte	3,35,176,7,12
	.word	53148
	.byte	11
	.byte	'CH15',0,48
	.word	55777
	.byte	3,35,192,7,13,144,8
	.word	227
	.byte	14,143,8,0,11
	.byte	'reserved_3F0',0,144,8
	.word	55797
	.byte	3,35,240,7,0,12
	.word	54819
	.byte	6
	.byte	'Ifx_GTM_TOM',0,7,199,18,3
	.word	55833
	.byte	15,8,130,4,20,64,11
	.byte	'CTRL',0,4
	.word	50388
	.byte	2,35,0,11
	.byte	'SR0',0,4
	.word	50752
	.byte	2,35,4,11
	.byte	'SR1',0,4
	.word	50820
	.byte	2,35,8,11
	.byte	'CM0',0,4
	.word	50184
	.byte	2,35,12,11
	.byte	'CM1',0,4
	.word	50252
	.byte	2,35,16,11
	.byte	'CN0',0,4
	.word	50320
	.byte	2,35,20,11
	.byte	'STAT',0,4
	.word	50888
	.byte	2,35,24,11
	.byte	'IRQ_NOTIFY',0,4
	.word	50677
	.byte	2,35,28,11
	.byte	'IRQ_EN',0,4
	.word	50457
	.byte	2,35,32,11
	.byte	'IRQ_FORCINT',0,4
	.word	50528
	.byte	2,35,36,11
	.byte	'IRQ_MODE',0,4
	.word	50604
	.byte	2,35,40,13,20
	.word	227
	.byte	14,19,0,11
	.byte	'reserved_2C',0,20
	.word	56033
	.byte	2,35,44,0,12
	.word	55859
	.byte	6
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,8,155,4,4
	.word	56064
	.byte	15,8,157,4,20,128,4,11
	.byte	'GLB_CTRL',0,4
	.word	51260
	.byte	2,35,0,11
	.byte	'ACT_TB',0,4
	.word	50957
	.byte	2,35,4,11
	.byte	'FUPD_CTRL',0,4
	.word	51184
	.byte	2,35,8,11
	.byte	'INT_TRIG',0,4
	.word	51335
	.byte	2,35,12,13,48
	.word	227
	.byte	14,47,0,11
	.byte	'reserved_tgc0',0,48
	.word	56176
	.byte	2,35,16,11
	.byte	'ENDIS_CTRL',0,4
	.word	51030
	.byte	2,35,64,11
	.byte	'ENDIS_STAT',0,4
	.word	51107
	.byte	2,35,68,11
	.byte	'OUTEN_CTRL',0,4
	.word	51410
	.byte	2,35,72,11
	.byte	'OUTEN_STAT',0,4
	.word	51487
	.byte	2,35,76,13,176,3
	.word	227
	.byte	14,175,3,0,11
	.byte	'reserved_tgc1',0,176,3
	.word	56288
	.byte	2,35,80,0,12
	.word	56098
	.byte	6
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,8,177,4,5
	.word	56324
	.byte	15,8,179,4,20,128,16,11
	.byte	'reserved_tom0',0,48
	.word	56176
	.byte	2,35,0,13,128,8
	.word	56098
	.byte	14,1,0,12
	.word	56389
	.byte	11
	.byte	'TGC',0,128,8
	.word	56399
	.byte	2,35,48,13,208,7
	.word	227
	.byte	14,207,7,0,11
	.byte	'reserved_tgc2',0,208,7
	.word	56418
	.byte	3,35,176,8,0,12
	.word	56359
	.byte	6
	.byte	'Ifx_GTM_TOM_TGCx',0,8,184,4,5
	.word	56455
	.byte	15,8,187,4,20,128,16,13,128,8
	.word	55859
	.byte	14,15,0,12
	.word	56493
	.byte	11
	.byte	'CH',0,128,8
	.word	56503
	.byte	2,35,0,13,128,8
	.word	227
	.byte	14,255,7,0,11
	.byte	'reserved_tom1',0,128,8
	.word	56521
	.byte	3,35,128,8,0,12
	.word	56486
	.byte	6
	.byte	'Ifx_GTM_TOM_CHx',0,8,191,4,5
	.word	56558
	.byte	15,8,212,4,20,128,1,11
	.byte	'CH_GPR0',0,4
	.word	49411
	.byte	2,35,0,11
	.byte	'CH_GPR1',0,4
	.word	49480
	.byte	2,35,4,11
	.byte	'CH_CNT',0,4
	.word	48852
	.byte	2,35,8,11
	.byte	'CH_ECNT',0,4
	.word	49058
	.byte	2,35,12,11
	.byte	'CH_CNTS',0,4
	.word	48920
	.byte	2,35,16,11
	.byte	'CH_TDUC',0,4
	.word	49844
	.byte	2,35,20,11
	.byte	'CH_TDUV',0,4
	.word	49913
	.byte	2,35,24,11
	.byte	'CH_FLT_RE',0,4
	.word	49340
	.byte	2,35,28,11
	.byte	'CH_FLT_FE',0,4
	.word	49269
	.byte	2,35,32,11
	.byte	'CH_CTRL',0,4
	.word	48989
	.byte	2,35,36,11
	.byte	'CH_ECTRL',0,4
	.word	49127
	.byte	2,35,40,11
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	49769
	.byte	2,35,44,11
	.byte	'CH_IRQ_EN',0,4
	.word	49549
	.byte	2,35,48,11
	.byte	'CH_IRQ_FORCINT',0,4
	.word	49620
	.byte	2,35,52,11
	.byte	'CH_IRQ_MODE',0,4
	.word	49696
	.byte	2,35,56,11
	.byte	'CH_EIRQ_EN',0,4
	.word	49197
	.byte	2,35,60,13,64
	.word	227
	.byte	14,63,0,11
	.byte	'reserved_40',0,64
	.word	56893
	.byte	2,35,64,0,12
	.word	56588
	.byte	6
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,8,248,4,4
	.word	56924
	.byte	15,8,250,4,20,8,11
	.byte	'IN_SRC',0,4
	.word	49982
	.byte	2,35,0,11
	.byte	'RST',0,4
	.word	50119
	.byte	2,35,4,0,12
	.word	56958
	.byte	6
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,8,255,4,4
	.word	56994
	.byte	15,8,129,5,21,128,16,13,128,8
	.word	56588
	.byte	14,7,0,12
	.word	57045
	.byte	11
	.byte	'CH',0,128,8
	.word	57055
	.byte	2,35,0,11
	.byte	'reserved_tim1',0,128,8
	.word	56521
	.byte	3,35,128,8,0,12
	.word	57038
	.byte	6
	.byte	'Ifx_GTM_TIM_CHx',0,8,133,5,4
	.word	57099
	.byte	15,8,135,5,20,128,16,13,120
	.word	227
	.byte	14,119,0,11
	.byte	'reserved_tim2',0,120
	.word	57136
	.byte	2,35,0,12
	.word	56958
	.byte	11
	.byte	'IN_SRC_RESET',0,8
	.word	57168
	.byte	2,35,120,13,128,15
	.word	227
	.byte	14,255,14,0,11
	.byte	'reserved_tim3',0,128,15
	.word	57195
	.byte	3,35,128,1,0,12
	.word	57129
	.byte	6
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,8,140,5,4
	.word	57232
	.byte	16,8,174,5,11,1,17
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,17
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,17
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,17
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,17
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,17
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,17
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,17
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,6
	.byte	'Gtm_ConfigurableClockType',0,8,184,5,4
	.word	57270
	.byte	16,8,188,5,11,1,17
	.byte	'GTM_LOW',0,0,17
	.byte	'GTM_HIGH',0,1,0,6
	.byte	'Gtm_OutputLevelType',0,8,192,5,4
	.word	57504
	.byte	16,8,195,5,11,1,17
	.byte	'TOM_GLB_CTRL',0,0,17
	.byte	'TOM_ACT_TB',0,1,17
	.byte	'TOM_FUPD_CTRL',0,2,17
	.byte	'TOM_INT_TRIG',0,3,17
	.byte	'TOM_RESERVED_0',0,4,17
	.byte	'TOM_RESERVED_1',0,5,17
	.byte	'TOM_RESERVED_2',0,6,17
	.byte	'TOM_RESERVED_3',0,7,17
	.byte	'TOM_RESERVED_4',0,8,17
	.byte	'TOM_RESERVED_5',0,9,17
	.byte	'TOM_RESERVED_6',0,10,17
	.byte	'TOM_RESERVED_7',0,11,17
	.byte	'TOM_RESERVED_8',0,12,17
	.byte	'TOM_RESERVED_9',0,13,17
	.byte	'TOM_RESERVED_10',0,14,17
	.byte	'TOM_RESERVED_11',0,15,17
	.byte	'TOM_ENDIS_CTRL',0,16,17
	.byte	'TOM_ENDIS_STAT',0,17,17
	.byte	'TOM_OUTEN_CTRL',0,18,17
	.byte	'TOM_OUTEN_STAT',0,19,0,6
	.byte	'Gtm_TomTimerRegistersType',0,8,217,5,4
	.word	57561
	.byte	15,8,221,5,11,8,11
	.byte	'FltRisingEdge',0,4
	.word	244
	.byte	2,35,0,11
	.byte	'FltFallingEdge',0,4
	.word	244
	.byte	2,35,4,0,6
	.byte	'Gtm_TimFilterType',0,8,225,5,4
	.word	57936
	.byte	6
	.byte	'Gtm_TbuChCtrlType',0,8,230,5,32
	.word	48364
	.byte	6
	.byte	'Gtm_TbuChBaseType',0,8,231,5,32
	.word	48294
	.byte	15,8,233,5,11,8,11
	.byte	'CH_CTRL',0,4
	.word	48364
	.byte	2,35,0,11
	.byte	'CH_BASE',0,4
	.word	48294
	.byte	2,35,4,0,6
	.byte	'Gtm_TbuChType',0,8,237,5,4
	.word	58071
	.byte	15,8,249,5,9,36,13,4
	.word	244
	.byte	14,0,0,11
	.byte	'TimInSel',0,4
	.word	58141
	.byte	2,35,0,13,32
	.word	244
	.byte	14,7,0,11
	.byte	'ToutSel',0,32
	.word	58168
	.byte	2,35,4,0,6
	.byte	'Gtm_PortConfigType',0,8,253,5,2
	.word	58135
	.byte	15,8,129,6,9,8,11
	.byte	'TimRisingEdgeFilter',0,4
	.word	244
	.byte	2,35,0,11
	.byte	'TimFallingEdgeFilter',0,4
	.word	244
	.byte	2,35,4,0,6
	.byte	'Gtm_TimFltType',0,8,134,6,2
	.word	58223
	.byte	15,8,138,6,11,24,11
	.byte	'TimUsage',0,1
	.word	227
	.byte	2,35,0,11
	.byte	'TimIrqEn',0,1
	.word	227
	.byte	2,35,1,11
	.byte	'TimErrIrqEn',0,1
	.word	227
	.byte	2,35,2,11
	.byte	'TimExtCapSrc',0,1
	.word	227
	.byte	2,35,3,11
	.byte	'TimCtrlValue',0,4
	.word	244
	.byte	2,35,4,18
	.word	58223
	.byte	5
	.word	58420
	.byte	11
	.byte	'GtmTimFltPtr',0,4
	.word	58425
	.byte	2,35,8,11
	.byte	'TimCntsValue',0,4
	.word	244
	.byte	2,35,12,11
	.byte	'TimTduValue',0,4
	.word	244
	.byte	2,35,16,11
	.byte	'TimInSrcSel',0,4
	.word	244
	.byte	2,35,20,0,6
	.byte	'Gtm_TimConfigType',0,8,151,6,4
	.word	58313
	.byte	15,8,154,6,11,40,13,8
	.word	227
	.byte	14,7,0,11
	.byte	'Gtm_TimUsage',0,8
	.word	58550
	.byte	2,35,0,13,16
	.word	227
	.byte	14,15,0,13,32
	.word	58581
	.byte	14,1,0,11
	.byte	'Gtm_TomUsage',0,32
	.word	58590
	.byte	2,35,8,0,6
	.byte	'Gtm_ModUsageConfigType',0,8,163,6,4
	.word	58544
	.byte	15,8,177,6,9,16,11
	.byte	'GtmTomUpdateEn',0,2
	.word	334
	.byte	2,35,0,11
	.byte	'GtmTomEndisCtrl',0,2
	.word	334
	.byte	2,35,2,11
	.byte	'GtmTomEndisStat',0,2
	.word	334
	.byte	2,35,4,11
	.byte	'GtmTomOutenCtrl',0,2
	.word	334
	.byte	2,35,6,11
	.byte	'GtmTomOutenStat',0,2
	.word	334
	.byte	2,35,8,11
	.byte	'GtmTomFupd',0,4
	.word	244
	.byte	2,35,10,0,6
	.byte	'Gtm_TomTgcConfigGroupType',0,8,185,6,2
	.word	58654
	.byte	15,8,189,6,9,12,11
	.byte	'GtmTomIntTrig',0,2
	.word	334
	.byte	2,35,0,11
	.byte	'GtmTomActTb',0,4
	.word	244
	.byte	2,35,2,18
	.word	58654
	.byte	5
	.word	58890
	.byte	11
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	58895
	.byte	2,35,8,0,6
	.byte	'Gtm_TomTgcConfigType',0,8,196,6,2
	.word	58840
	.byte	15,8,199,6,9,12,11
	.byte	'GtmTomIrqEn',0,1
	.word	227
	.byte	2,35,0,11
	.byte	'GtmTomCn0Value',0,2
	.word	334
	.byte	2,35,2,11
	.byte	'GtmTomCm0Value',0,2
	.word	334
	.byte	2,35,4,11
	.byte	'GtmTomCm1Value',0,2
	.word	334
	.byte	2,35,6,11
	.byte	'GtmTomSr0Value',0,2
	.word	334
	.byte	2,35,8,11
	.byte	'GtmTomSr1Value',0,2
	.word	334
	.byte	2,35,10,0,6
	.byte	'Gtm_TomChannelConfigType',0,8,207,6,2
	.word	58962
	.byte	15,8,211,6,9,12,11
	.byte	'TomUsage',0,1
	.word	227
	.byte	2,35,0,11
	.byte	'GtmTomIrqMode',0,1
	.word	227
	.byte	2,35,1,11
	.byte	'GtmTomControlWord',0,4
	.word	244
	.byte	2,35,2,18
	.word	58962
	.byte	5
	.word	59218
	.byte	11
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	59223
	.byte	2,35,8,0,6
	.byte	'Gtm_TomConfigType',0,8,219,6,2
	.word	59144
	.byte	15,8,223,6,9,8,11
	.byte	'CmuEclkNum',0,4
	.word	244
	.byte	2,35,0,11
	.byte	'CmuEclkDen',0,4
	.word	244
	.byte	2,35,4,0,6
	.byte	'Gtm_ExtClkType',0,8,227,6,2
	.word	59285
	.byte	15,8,230,6,9,64,11
	.byte	'GtmClockEnable',0,4
	.word	244
	.byte	2,35,0,11
	.byte	'GtmCmuClkCnt',0,32
	.word	58168
	.byte	2,35,4,11
	.byte	'GtmFxdClkControl',0,4
	.word	244
	.byte	2,35,36,13,24
	.word	59285
	.byte	14,2,0,11
	.byte	'GtmEclk',0,24
	.word	59434
	.byte	2,35,40,0,6
	.byte	'Gtm_ClockSettingType',0,8,236,6,2
	.word	59356
	.byte	15,8,240,6,9,4,11
	.byte	'GtmCtrlValue',0,2
	.word	334
	.byte	2,35,0,11
	.byte	'GtmIrqEnable',0,2
	.word	334
	.byte	2,35,2,0,6
	.byte	'Gtm_GeneralConfigType',0,8,245,6,2
	.word	59491
	.byte	15,8,249,6,9,6,11
	.byte	'TbuChannelCtrl',0,1
	.word	227
	.byte	2,35,0,11
	.byte	'TbuBaseValue',0,4
	.word	244
	.byte	2,35,2,0,6
	.byte	'Gtm_TbuConfigType',0,8,253,6,2
	.word	59573
	.byte	15,8,129,7,9,72,11
	.byte	'GtmModuleSleepEnable',0,1
	.word	227
	.byte	2,35,0,11
	.byte	'GtmGclkNum',0,4
	.word	244
	.byte	2,35,2,11
	.byte	'GtmGclkDen',0,4
	.word	244
	.byte	2,35,6,11
	.byte	'GtmAccessEnable0',0,4
	.word	244
	.byte	2,35,10,11
	.byte	'GtmAccessEnable1',0,4
	.word	244
	.byte	2,35,14,13,2
	.word	334
	.byte	14,0,0,11
	.byte	'GtmTimModuleUsage',0,2
	.word	59781
	.byte	2,35,18,13,1
	.word	227
	.byte	14,0,0,11
	.byte	'GtmTimUsage',0,1
	.word	59817
	.byte	2,35,20,18
	.word	58313
	.byte	5
	.word	59847
	.byte	11
	.byte	'GtmTimConfigPtr',0,4
	.word	59852
	.byte	2,35,24,11
	.byte	'GtmTomTgcUsage',0,1
	.word	59817
	.byte	2,35,28,18
	.word	58840
	.byte	5
	.word	59906
	.byte	11
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	59911
	.byte	2,35,32,13,8
	.word	244
	.byte	14,1,0,11
	.byte	'GtmTomModuleUsage',0,8
	.word	59944
	.byte	2,35,36,11
	.byte	'GtmTomUsage',0,4
	.word	58141
	.byte	2,35,44,18
	.word	59144
	.byte	5
	.word	60001
	.byte	11
	.byte	'GtmTomConfigPtr',0,4
	.word	60006
	.byte	2,35,48,18
	.word	58544
	.byte	5
	.word	60036
	.byte	11
	.byte	'GtmModUsageConfigPtr',0,4
	.word	60041
	.byte	2,35,52,18
	.word	59491
	.byte	5
	.word	60076
	.byte	11
	.byte	'GtmGeneralConfigPtr',0,4
	.word	60081
	.byte	2,35,56,18
	.word	59573
	.byte	5
	.word	60115
	.byte	11
	.byte	'GtmTbuConfigPtr',0,4
	.word	60120
	.byte	2,35,60,18
	.word	227
	.byte	5
	.word	60150
	.byte	11
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	60155
	.byte	2,35,64,11
	.byte	'GtmTtcanTriggers',0,2
	.word	59781
	.byte	2,35,68,0,6
	.byte	'Gtm_ModuleConfigType',0,8,163,7,2
	.word	59653
	.byte	19,1,1,20
	.word	227
	.byte	20
	.word	227
	.byte	20
	.word	227
	.byte	20
	.word	334
	.byte	0,5
	.word	60247
	.byte	6
	.byte	'Gtm_NotificationPtrType',0,8,172,7,16
	.word	60271
	.byte	8
	.byte	'Gtm_ConfigType',0,8,192,7,16,12,18
	.word	59356
	.byte	5
	.word	60330
	.byte	11
	.byte	'GtmClockSettingPtr',0,4
	.word	60335
	.byte	2,35,0,18
	.word	58135
	.byte	5
	.word	60368
	.byte	11
	.byte	'GtmPortConfigPtr',0,4
	.word	60373
	.byte	2,35,4,18
	.word	59653
	.byte	5
	.word	60404
	.byte	11
	.byte	'GtmModuleConfigPtr',0,4
	.word	60409
	.byte	2,35,8,0,6
	.byte	'Gtm_ConfigType',0,8,197,7,2
	.word	60309
	.byte	6
	.byte	'Mcu_ClockType',0,9,156,3,18
	.word	244
	.byte	6
	.byte	'Mcu_ModeType',0,9,162,3,18
	.word	244
	.byte	6
	.byte	'Mcu_RamSectionType',0,9,168,3,18
	.word	244
	.byte	6
	.byte	'Mcu_RamBaseAdrType',0,9,178,3,18
	.word	271
	.byte	6
	.byte	'Mcu_RamSizeType',0,9,181,3,17
	.word	244
	.byte	6
	.byte	'Mcu_RamPrstDatType',0,9,184,3,16
	.word	227
	.byte	8
	.byte	'Mcu_ClockCfgType',0,9,236,3,16,80,11
	.byte	'K2div',0,8
	.word	58550
	.byte	2,35,0,11
	.byte	'K2RampToPllDelayTicks',0,32
	.word	58168
	.byte	2,35,8,15,9,247,3,3,4,9
	.byte	'K1div',0,1
	.word	227
	.byte	7,1,2,35,0,9
	.byte	'K3div',0,2
	.word	334
	.byte	7,2,2,35,0,9
	.byte	'Ndiv',0,4
	.word	425
	.byte	7,11,2,35,2,9
	.byte	'Pdiv',0,2
	.word	334
	.byte	4,7,2,35,2,9
	.byte	'K2steps',0,1
	.word	227
	.byte	4,3,2,35,3,9
	.byte	'PllMode',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'Reserved',0,1
	.word	227
	.byte	2,0,2,35,3,0,11
	.byte	'Mcu_ClockDivValues',0,4
	.word	60690
	.byte	2,35,40,15,9,132,4,3,4,9
	.byte	'McuErayNDivider',0,1
	.word	227
	.byte	5,3,2,35,0,9
	.byte	'McuErayK2Divider',0,2
	.word	334
	.byte	7,4,2,35,0,9
	.byte	'McuErayK3Divider',0,4
	.word	425
	.byte	7,13,2,35,2,9
	.byte	'McuErayPDivider',0,1
	.word	227
	.byte	4,1,2,35,2,9
	.byte	'Reserved',0,2
	.word	334
	.byte	9,0,2,35,2,0,11
	.byte	'MCU_ErayPllDivValues',0,4
	.word	60849
	.byte	2,35,44,11
	.byte	'Ccucon0',0,4
	.word	244
	.byte	2,35,48,11
	.byte	'Ccucon1',0,4
	.word	244
	.byte	2,35,52,11
	.byte	'Ccucon2',0,4
	.word	244
	.byte	2,35,56,11
	.byte	'Ccucon5',0,4
	.word	244
	.byte	2,35,60,11
	.byte	'Ccucon6',0,4
	.word	244
	.byte	2,35,64,11
	.byte	'Ccucon7',0,4
	.word	244
	.byte	2,35,68,11
	.byte	'Ccucon8',0,4
	.word	244
	.byte	2,35,72,11
	.byte	'K2RampToPllDelayConf',0,1
	.word	227
	.byte	2,35,76,0,6
	.byte	'Mcu_ClockCfgType',0,9,162,4,2
	.word	60621
	.byte	8
	.byte	'Mcu_StandbyModeType',0,9,171,4,16,6,11
	.byte	'PMSWCR0',0,4
	.word	244
	.byte	2,35,0,11
	.byte	'CrcCheckEnable',0,1
	.word	227
	.byte	2,35,4,0,6
	.byte	'Mcu_StandbyModeType',0,9,175,4,2
	.word	61192
	.byte	8
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,10,45,16,4,9
	.byte	'EN0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	227
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	227
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_DMA_ACCEN00_Bits',0,10,79,3
	.word	61289
	.byte	8
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,10,82,16,4,9
	.byte	'reserved_0',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_ACCEN01_Bits',0,10,85,3
	.word	61848
	.byte	8
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,10,88,16,4,9
	.byte	'EN0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	227
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	227
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_DMA_ACCEN10_Bits',0,10,122,3
	.word	61927
	.byte	8
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,10,125,16,4,9
	.byte	'reserved_0',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_ACCEN11_Bits',0,10,128,1,3
	.word	62486
	.byte	8
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,10,131,1,16,4,9
	.byte	'EN0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	227
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	227
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_DMA_ACCEN20_Bits',0,10,165,1,3
	.word	62566
	.byte	8
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,10,168,1,16,4,9
	.byte	'reserved_0',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_ACCEN21_Bits',0,10,171,1,3
	.word	63127
	.byte	8
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,10,174,1,16,4,9
	.byte	'EN0',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	227
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	227
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	227
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	227
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_DMA_ACCEN30_Bits',0,10,208,1,3
	.word	63208
	.byte	8
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,10,211,1,16,4,9
	.byte	'reserved_0',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_ACCEN31_Bits',0,10,214,1,3
	.word	63769
	.byte	8
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,10,217,1,16,4,9
	.byte	'reserved_0',0,2
	.word	334
	.byte	16,0,2,35,0,9
	.byte	'CSER',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'CDER',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	227
	.byte	2,4,2,35,2,9
	.byte	'CSPBER',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'CSRIER',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	227
	.byte	2,0,2,35,2,9
	.byte	'CRAMER',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'CSLLER',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'CDLLER',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	227
	.byte	5,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,10,230,1,3
	.word	63850
	.byte	8
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,10,233,1,16,4,9
	.byte	'reserved_0',0,2
	.word	334
	.byte	16,0,2,35,0,9
	.byte	'ESER',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'EDER',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	227
	.byte	6,0,2,35,2,9
	.byte	'ERER',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'ELER',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	227
	.byte	5,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_EER_Bits',0,10,243,1,3
	.word	64124
	.byte	8
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,10,246,1,16,4,9
	.byte	'LEC',0,1
	.word	227
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	334
	.byte	9,0,2,35,0,9
	.byte	'SER',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'DER',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	227
	.byte	2,4,2,35,2,9
	.byte	'SPBER',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'SRIER',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	227
	.byte	2,0,2,35,2,9
	.byte	'RAMER',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'SLLER',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'DLLER',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	227
	.byte	5,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,10,132,2,3
	.word	64338
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,10,135,2,16,4,9
	.byte	'SMF',0,1
	.word	227
	.byte	3,5,2,35,0,9
	.byte	'INCS',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'DMF',0,1
	.word	227
	.byte	3,1,2,35,0,9
	.byte	'INCD',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'CBLS',0,1
	.word	227
	.byte	4,4,2,35,1,9
	.byte	'CBLD',0,1
	.word	227
	.byte	4,0,2,35,1,9
	.byte	'SHCT',0,1
	.word	227
	.byte	4,4,2,35,2,9
	.byte	'SCBE',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'DCBE',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'STAMP',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'ETRL',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'WRPSE',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'WRPDE',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'INTCT',0,1
	.word	227
	.byte	2,4,2,35,3,9
	.byte	'IRDV',0,1
	.word	227
	.byte	4,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,10,152,2,3
	.word	64622
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,10,155,2,16,4,9
	.byte	'TREL',0,2
	.word	334
	.byte	14,2,2,35,0,9
	.byte	'reserved_14',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'BLKM',0,1
	.word	227
	.byte	3,5,2,35,2,9
	.byte	'RROAT',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'CHMODE',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'CHDW',0,1
	.word	227
	.byte	3,0,2,35,2,9
	.byte	'PATSEL',0,1
	.word	227
	.byte	3,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'PRSEL',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'DMAPRIO',0,1
	.word	227
	.byte	2,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,10,168,2,3
	.word	64933
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,10,171,2,16,4,9
	.byte	'TCOUNT',0,2
	.word	334
	.byte	14,2,2,35,0,9
	.byte	'reserved_14',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'LXO',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'WRPS',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'WRPD',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'ICH',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'IPM',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,1
	.word	227
	.byte	2,2,2,35,2,9
	.byte	'BUFFER',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'FROZEN',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,10,184,2,3
	.word	65206
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,10,187,2,16,4,9
	.byte	'DADR',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,10,190,2,3
	.word	65473
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,10,193,2,16,4,9
	.byte	'RD00',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'RD01',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'RD02',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'RD03',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,10,199,2,3
	.word	65556
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,10,202,2,16,4,9
	.byte	'RD10',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'RD11',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'RD12',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'RD13',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,10,208,2,3
	.word	65683
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,10,211,2,16,4,9
	.byte	'RD20',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'RD21',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'RD22',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'RD23',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,10,217,2,3
	.word	65810
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,10,220,2,16,4,9
	.byte	'RD30',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'RD31',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'RD32',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'RD33',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,10,226,2,3
	.word	65937
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,10,229,2,16,4,9
	.byte	'RD40',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'RD41',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'RD42',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'RD43',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,10,235,2,3
	.word	66064
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,10,238,2,16,4,9
	.byte	'RD50',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'RD51',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'RD52',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'RD53',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,10,244,2,3
	.word	66191
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,10,247,2,16,4,9
	.byte	'RD60',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'RD61',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'RD62',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'RD63',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,10,253,2,3
	.word	66318
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,10,128,3,16,4,9
	.byte	'RD70',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'RD71',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'RD72',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'RD73',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,10,134,3,3
	.word	66445
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,10,137,3,16,4,9
	.byte	'RDCRC',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,10,140,3,3
	.word	66572
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,10,143,3,16,4,9
	.byte	'SADR',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,10,146,3,3
	.word	66658
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,10,149,3,16,4,9
	.byte	'SDCRC',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,10,152,3,3
	.word	66741
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,10,155,3,16,4,9
	.byte	'SHADR',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,10,158,3,3
	.word	66827
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,10,161,3,16,4,9
	.byte	'RS',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	227
	.byte	3,4,2,35,0,9
	.byte	'WS',0,1
	.word	227
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,2
	.word	334
	.byte	11,0,2,35,0,9
	.byte	'CH',0,1
	.word	227
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	334
	.byte	9,0,2,35,2,0,6
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,10,169,3,3
	.word	66913
	.byte	8
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,10,172,3,16,4,9
	.byte	'SMF',0,1
	.word	227
	.byte	3,5,2,35,0,9
	.byte	'INCS',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'DMF',0,1
	.word	227
	.byte	3,1,2,35,0,9
	.byte	'INCD',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'CBLS',0,1
	.word	227
	.byte	4,4,2,35,1,9
	.byte	'CBLD',0,1
	.word	227
	.byte	4,0,2,35,1,9
	.byte	'SHCT',0,1
	.word	227
	.byte	4,4,2,35,2,9
	.byte	'SCBE',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'DCBE',0,1
	.word	227
	.byte	1,2,2,35,2,9
	.byte	'STAMP',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'ETRL',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'WRPSE',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'WRPDE',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'INTCT',0,1
	.word	227
	.byte	2,4,2,35,3,9
	.byte	'IRDV',0,1
	.word	227
	.byte	4,0,2,35,3,0,6
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,10,189,3,3
	.word	67085
	.byte	8
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,10,192,3,16,4,9
	.byte	'TREL',0,2
	.word	334
	.byte	14,2,2,35,0,9
	.byte	'reserved_14',0,1
	.word	227
	.byte	2,0,2,35,1,9
	.byte	'BLKM',0,1
	.word	227
	.byte	3,5,2,35,2,9
	.byte	'RROAT',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'CHMODE',0,1
	.word	227
	.byte	1,3,2,35,2,9
	.byte	'CHDW',0,1
	.word	227
	.byte	3,0,2,35,2,9
	.byte	'PATSEL',0,1
	.word	227
	.byte	3,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'PRSEL',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'DMAPRIO',0,1
	.word	227
	.byte	2,0,2,35,3,0,6
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,10,205,3,3
	.word	67388
	.byte	8
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,10,208,3,16,4,9
	.byte	'TCOUNT',0,2
	.word	334
	.byte	14,2,2,35,0,9
	.byte	'reserved_14',0,1
	.word	227
	.byte	1,1,2,35,1,9
	.byte	'LXO',0,1
	.word	227
	.byte	1,0,2,35,1,9
	.byte	'WRPS',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'WRPD',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'ICH',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'IPM',0,1
	.word	227
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,1
	.word	227
	.byte	2,2,2,35,2,9
	.byte	'BUFFER',0,1
	.word	227
	.byte	1,1,2,35,2,9
	.byte	'FROZEN',0,1
	.word	227
	.byte	1,0,2,35,2,9
	.byte	'SWB',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'CWRP',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'CICH',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'SIT',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	227
	.byte	3,1,2,35,3,9
	.byte	'SCH',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,10,226,3,3
	.word	67657
	.byte	8
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,10,229,3,16,4,9
	.byte	'DADR',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_CH_DADR_Bits',0,10,232,3,3
	.word	67995
	.byte	8
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,10,235,3,16,4,9
	.byte	'RDCRC',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,10,238,3,3
	.word	68070
	.byte	8
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,10,241,3,16,4,9
	.byte	'SADR',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_CH_SADR_Bits',0,10,244,3,3
	.word	68150
	.byte	8
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,10,247,3,16,4,9
	.byte	'SDCRC',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,10,250,3,3
	.word	68225
	.byte	8
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,10,253,3,16,4,9
	.byte	'SHADR',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,10,128,4,3
	.word	68305
	.byte	8
	.byte	'_Ifx_DMA_CLC_Bits',0,10,131,4,16,4,9
	.byte	'DISR',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'DISS',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'EDIS',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	425
	.byte	28,0,2,35,2,0,6
	.byte	'Ifx_DMA_CLC_Bits',0,10,138,4,3
	.word	68383
	.byte	8
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,10,141,4,16,4,9
	.byte	'SIT',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	425
	.byte	31,0,2,35,2,0,6
	.byte	'Ifx_DMA_ERRINTR_Bits',0,10,145,4,3
	.word	68526
	.byte	8
	.byte	'_Ifx_DMA_HRR_Bits',0,10,148,4,16,4,9
	.byte	'HRP',0,1
	.word	227
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	425
	.byte	30,0,2,35,2,0,6
	.byte	'Ifx_DMA_HRR_Bits',0,10,152,4,3
	.word	68622
	.byte	8
	.byte	'_Ifx_DMA_ID_Bits',0,10,155,4,16,4,9
	.byte	'MODREV',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	334
	.byte	16,0,2,35,2,0,6
	.byte	'Ifx_DMA_ID_Bits',0,10,160,4,3
	.word	68710
	.byte	8
	.byte	'_Ifx_DMA_MEMCON_Bits',0,10,163,4,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	2,30,2,35,0,9
	.byte	'INTERR',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'RMWERR',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'DATAERR',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'reserved_7',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'PMIC',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'ERRDIS',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	16230
	.byte	22,0,2,35,0,0,6
	.byte	'Ifx_DMA_MEMCON_Bits',0,10,175,4,3
	.word	68817
	.byte	8
	.byte	'_Ifx_DMA_MODE_Bits',0,10,178,4,16,4,9
	.byte	'MODE',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	425
	.byte	31,0,2,35,2,0,6
	.byte	'Ifx_DMA_MODE_Bits',0,10,182,4,3
	.word	69074
	.byte	8
	.byte	'_Ifx_DMA_OTSS_Bits',0,10,185,4,16,4,9
	.byte	'TGS',0,1
	.word	227
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	227
	.byte	3,1,2,35,0,9
	.byte	'BS',0,1
	.word	227
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	425
	.byte	24,0,2,35,2,0,6
	.byte	'Ifx_DMA_OTSS_Bits',0,10,191,4,3
	.word	69165
	.byte	8
	.byte	'_Ifx_DMA_PRR0_Bits',0,10,194,4,16,4,9
	.byte	'PAT00',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'PAT01',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'PAT02',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'PAT03',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_PRR0_Bits',0,10,200,4,3
	.word	69291
	.byte	8
	.byte	'_Ifx_DMA_PRR1_Bits',0,10,203,4,16,4,9
	.byte	'PAT10',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'PAT11',0,1
	.word	227
	.byte	8,0,2,35,1,9
	.byte	'PAT12',0,1
	.word	227
	.byte	8,0,2,35,2,9
	.byte	'PAT13',0,1
	.word	227
	.byte	8,0,2,35,3,0,6
	.byte	'Ifx_DMA_PRR1_Bits',0,10,209,4,3
	.word	69412
	.byte	8
	.byte	'_Ifx_DMA_SUSACR_Bits',0,10,212,4,16,4,9
	.byte	'SUSAC',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	425
	.byte	31,0,2,35,2,0,6
	.byte	'Ifx_DMA_SUSACR_Bits',0,10,216,4,3
	.word	69533
	.byte	8
	.byte	'_Ifx_DMA_SUSENR_Bits',0,10,219,4,16,4,9
	.byte	'SUSEN',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	425
	.byte	31,0,2,35,2,0,6
	.byte	'Ifx_DMA_SUSENR_Bits',0,10,223,4,3
	.word	69629
	.byte	8
	.byte	'_Ifx_DMA_TIME_Bits',0,10,226,4,16,4,9
	.byte	'COUNT',0,4
	.word	425
	.byte	32,0,2,35,2,0,6
	.byte	'Ifx_DMA_TIME_Bits',0,10,229,4,3
	.word	69725
	.byte	8
	.byte	'_Ifx_DMA_TSR_Bits',0,10,232,4,16,4,9
	.byte	'RST',0,1
	.word	227
	.byte	1,7,2,35,0,9
	.byte	'HTRE',0,1
	.word	227
	.byte	1,6,2,35,0,9
	.byte	'TRL',0,1
	.word	227
	.byte	1,5,2,35,0,9
	.byte	'CH',0,1
	.word	227
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	227
	.byte	4,0,2,35,0,9
	.byte	'HLTREQ',0,1
	.word	227
	.byte	1,7,2,35,1,9
	.byte	'HLTACK',0,1
	.word	227
	.byte	1,6,2,35,1,9
	.byte	'reserved_10',0,1
	.word	227
	.byte	6,0,2,35,1,9
	.byte	'ECH',0,1
	.word	227
	.byte	1,7,2,35,2,9
	.byte	'DCH',0,1
	.word	227
	.byte	1,6,2,35,2,9
	.byte	'CTL',0,1
	.word	227
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	227
	.byte	5,0,2,35,2,9
	.byte	'HLTCLR',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	227
	.byte	7,0,2,35,3,0,6
	.byte	'Ifx_DMA_TSR_Bits',0,10,248,4,3
	.word	69795
	.byte	10,10,128,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	61289
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ACCEN00',0,10,133,5,3
	.word	70096
	.byte	10,10,136,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	61848
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ACCEN01',0,10,141,5,3
	.word	70161
	.byte	10,10,144,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	61927
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ACCEN10',0,10,149,5,3
	.word	70226
	.byte	10,10,152,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	62486
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ACCEN11',0,10,157,5,3
	.word	70291
	.byte	10,10,160,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	62566
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ACCEN20',0,10,165,5,3
	.word	70356
	.byte	10,10,168,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	63127
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ACCEN21',0,10,173,5,3
	.word	70421
	.byte	10,10,176,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	63208
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ACCEN30',0,10,181,5,3
	.word	70486
	.byte	10,10,184,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	63769
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ACCEN31',0,10,189,5,3
	.word	70551
	.byte	10,10,192,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	63850
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_CLRE',0,10,197,5,3
	.word	70616
	.byte	10,10,200,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	64124
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_EER',0,10,205,5,3
	.word	70682
	.byte	10,10,208,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	64338
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ERRSR',0,10,213,5,3
	.word	70747
	.byte	10,10,216,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	64622
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,10,221,5,3
	.word	70814
	.byte	10,10,224,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	64933
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,10,229,5,3
	.word	70884
	.byte	10,10,232,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	65206
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,10,237,5,3
	.word	70953
	.byte	10,10,240,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	65473
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_DADR',0,10,245,5,3
	.word	71022
	.byte	10,10,248,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	65556
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_R0',0,10,253,5,3
	.word	71091
	.byte	10,10,128,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	65683
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_R1',0,10,133,6,3
	.word	71158
	.byte	10,10,136,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	65810
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_R2',0,10,141,6,3
	.word	71225
	.byte	10,10,144,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	65937
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_R3',0,10,149,6,3
	.word	71292
	.byte	10,10,152,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66064
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_R4',0,10,157,6,3
	.word	71359
	.byte	10,10,160,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66191
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_R5',0,10,165,6,3
	.word	71426
	.byte	10,10,168,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66318
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_R6',0,10,173,6,3
	.word	71493
	.byte	10,10,176,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66445
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_R7',0,10,181,6,3
	.word	71560
	.byte	10,10,184,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66572
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,10,189,6,3
	.word	71627
	.byte	10,10,192,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66658
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_SADR',0,10,197,6,3
	.word	71697
	.byte	10,10,200,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66741
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,10,205,6,3
	.word	71766
	.byte	10,10,208,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66827
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,10,213,6,3
	.word	71836
	.byte	10,10,216,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	66913
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_BLK_ME_SR',0,10,221,6,3
	.word	71906
	.byte	10,10,224,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	67085
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CH_ADICR',0,10,229,6,3
	.word	71973
	.byte	10,10,232,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	67388
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CH_CHCFGR',0,10,237,6,3
	.word	72039
	.byte	10,10,240,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	67657
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CH_CHCSR',0,10,245,6,3
	.word	72106
	.byte	10,10,248,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	67995
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CH_DADR',0,10,253,6,3
	.word	72172
	.byte	10,10,128,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68070
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CH_RDCRCR',0,10,133,7,3
	.word	72237
	.byte	10,10,136,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68150
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CH_SADR',0,10,141,7,3
	.word	72304
	.byte	10,10,144,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68225
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CH_SDCRCR',0,10,149,7,3
	.word	72369
	.byte	10,10,152,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68305
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CH_SHADR',0,10,157,7,3
	.word	72436
	.byte	10,10,160,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68383
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_CLC',0,10,165,7,3
	.word	72502
	.byte	10,10,168,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68526
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ERRINTR',0,10,173,7,3
	.word	72563
	.byte	10,10,176,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68622
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_HRR',0,10,181,7,3
	.word	72628
	.byte	10,10,184,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68710
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_ID',0,10,189,7,3
	.word	72689
	.byte	10,10,192,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	68817
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_MEMCON',0,10,197,7,3
	.word	72749
	.byte	10,10,200,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	69074
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_MODE',0,10,205,7,3
	.word	72813
	.byte	10,10,208,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	69165
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_OTSS',0,10,213,7,3
	.word	72875
	.byte	10,10,216,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	69291
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_PRR0',0,10,221,7,3
	.word	72937
	.byte	10,10,224,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	69412
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_PRR1',0,10,229,7,3
	.word	72999
	.byte	10,10,232,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	69533
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_SUSACR',0,10,237,7,3
	.word	73061
	.byte	10,10,240,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	69629
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_SUSENR',0,10,245,7,3
	.word	73125
	.byte	10,10,248,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	69725
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_TIME',0,10,253,7,3
	.word	73189
	.byte	10,10,128,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	69795
	.byte	2,35,0,0,6
	.byte	'Ifx_DMA_TSR',0,10,133,8,3
	.word	73251
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME',0,10,144,8,25,112,11
	.byte	'SR',0,4
	.word	71906
	.byte	2,35,0,11
	.byte	'reserved_4',0,12
	.word	53938
	.byte	2,35,4,11
	.byte	'R0',0,4
	.word	71091
	.byte	2,35,16,11
	.byte	'R1',0,4
	.word	71158
	.byte	2,35,20,11
	.byte	'R2',0,4
	.word	71225
	.byte	2,35,24,11
	.byte	'R3',0,4
	.word	71292
	.byte	2,35,28,11
	.byte	'R4',0,4
	.word	71359
	.byte	2,35,32,11
	.byte	'R5',0,4
	.word	71426
	.byte	2,35,36,11
	.byte	'R6',0,4
	.word	71493
	.byte	2,35,40,11
	.byte	'R7',0,4
	.word	71560
	.byte	2,35,44,13,32
	.word	227
	.byte	14,31,0,11
	.byte	'reserved_30',0,32
	.word	73462
	.byte	2,35,48,11
	.byte	'RDCRC',0,4
	.word	71627
	.byte	2,35,80,11
	.byte	'SDCRC',0,4
	.word	71766
	.byte	2,35,84,11
	.byte	'SADR',0,4
	.word	71697
	.byte	2,35,88,11
	.byte	'DADR',0,4
	.word	71022
	.byte	2,35,92,11
	.byte	'ADICR',0,4
	.word	70814
	.byte	2,35,96,11
	.byte	'CHCR',0,4
	.word	70884
	.byte	2,35,100,11
	.byte	'SHADR',0,4
	.word	71836
	.byte	2,35,104,11
	.byte	'CHSR',0,4
	.word	70953
	.byte	2,35,108,0,12
	.word	73312
	.byte	6
	.byte	'Ifx_DMA_BLK_ME',0,10,165,8,3
	.word	73609
	.byte	8
	.byte	'_Ifx_DMA_BLK',0,10,178,8,25,128,1,11
	.byte	'EER',0,4
	.word	70682
	.byte	2,35,0,11
	.byte	'ERRSR',0,4
	.word	70747
	.byte	2,35,4,11
	.byte	'CLRE',0,4
	.word	70616
	.byte	2,35,8,11
	.byte	'reserved_C',0,4
	.word	53338
	.byte	2,35,12,12
	.word	73312
	.byte	11
	.byte	'ME',0,112
	.word	73720
	.byte	2,35,16,0,12
	.word	73638
	.byte	6
	.byte	'Ifx_DMA_BLK',0,10,185,8,3
	.word	73738
	.byte	8
	.byte	'_Ifx_DMA_CH',0,10,188,8,25,32,11
	.byte	'RDCRCR',0,4
	.word	72237
	.byte	2,35,0,11
	.byte	'SDCRCR',0,4
	.word	72369
	.byte	2,35,4,11
	.byte	'SADR',0,4
	.word	72304
	.byte	2,35,8,11
	.byte	'DADR',0,4
	.word	72172
	.byte	2,35,12,11
	.byte	'ADICR',0,4
	.word	71973
	.byte	2,35,16,11
	.byte	'CHCFGR',0,4
	.word	72039
	.byte	2,35,20,11
	.byte	'SHADR',0,4
	.word	72436
	.byte	2,35,24,11
	.byte	'CHCSR',0,4
	.word	72106
	.byte	2,35,28,0,12
	.word	73764
	.byte	6
	.byte	'Ifx_DMA_CH',0,10,198,8,3
	.word	73904
	.byte	8
	.byte	'_Ifx_CPU_A_Bits',0,11,45,16,4,9
	.byte	'ADDR',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_A_Bits',0,11,48,3
	.word	73929
	.byte	8
	.byte	'_Ifx_CPU_BIV_Bits',0,11,51,16,4,9
	.byte	'VSS',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'BIV',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_BIV_Bits',0,11,55,3
	.word	73990
	.byte	8
	.byte	'_Ifx_CPU_BTV_Bits',0,11,58,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'BTV',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_BTV_Bits',0,11,62,3
	.word	74069
	.byte	8
	.byte	'_Ifx_CPU_CCNT_Bits',0,11,65,16,4,9
	.byte	'CountValue',0,4
	.word	16230
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_CCNT_Bits',0,11,69,3
	.word	74155
	.byte	8
	.byte	'_Ifx_CPU_CCTRL_Bits',0,11,72,16,4,9
	.byte	'CM',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'CE',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'M1',0,4
	.word	16230
	.byte	3,27,2,35,0,9
	.byte	'M2',0,4
	.word	16230
	.byte	3,24,2,35,0,9
	.byte	'M3',0,4
	.word	16230
	.byte	3,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	16230
	.byte	21,0,2,35,0,0,6
	.byte	'Ifx_CPU_CCTRL_Bits',0,11,80,3
	.word	74244
	.byte	8
	.byte	'_Ifx_CPU_COMPAT_Bits',0,11,83,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'RM',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'SP',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_COMPAT_Bits',0,11,89,3
	.word	74390
	.byte	8
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,11,92,16,4,9
	.byte	'CORE_ID',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_CORE_ID_Bits',0,11,96,3
	.word	74517
	.byte	8
	.byte	'_Ifx_CPU_CPR_L_Bits',0,11,99,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'LOWBND',0,4
	.word	16230
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_CPR_L_Bits',0,11,103,3
	.word	74615
	.byte	8
	.byte	'_Ifx_CPU_CPR_U_Bits',0,11,106,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'UPPBND',0,4
	.word	16230
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_CPR_U_Bits',0,11,110,3
	.word	74708
	.byte	8
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,11,113,16,4,9
	.byte	'MODREV',0,4
	.word	16230
	.byte	8,24,2,35,0,9
	.byte	'MOD_32B',0,4
	.word	16230
	.byte	8,16,2,35,0,9
	.byte	'MOD',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_CPU_ID_Bits',0,11,118,3
	.word	74801
	.byte	8
	.byte	'_Ifx_CPU_CPXE_Bits',0,11,121,16,4,9
	.byte	'XE',0,4
	.word	16230
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_CPXE_Bits',0,11,125,3
	.word	74908
	.byte	8
	.byte	'_Ifx_CPU_CREVT_Bits',0,11,128,1,16,4,9
	.byte	'EVTA',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_CREVT_Bits',0,11,136,1,3
	.word	74995
	.byte	8
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,11,139,1,16,4,9
	.byte	'CID',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_CUS_ID_Bits',0,11,143,1,3
	.word	75149
	.byte	8
	.byte	'_Ifx_CPU_D_Bits',0,11,146,1,16,4,9
	.byte	'DATA',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_D_Bits',0,11,149,1,3
	.word	75243
	.byte	8
	.byte	'_Ifx_CPU_DATR_Bits',0,11,152,1,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'SBE',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	16230
	.byte	5,23,2,35,0,9
	.byte	'CWE',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'CFE',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	16230
	.byte	3,18,2,35,0,9
	.byte	'SOE',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'SME',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_DATR_Bits',0,11,163,1,3
	.word	75306
	.byte	8
	.byte	'_Ifx_CPU_DBGSR_Bits',0,11,166,1,16,4,9
	.byte	'DE',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'HALT',0,4
	.word	16230
	.byte	2,29,2,35,0,9
	.byte	'SIH',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'SUSP',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'PREVSUSP',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'PEVT',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'EVTSRC',0,4
	.word	16230
	.byte	5,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	16230
	.byte	19,0,2,35,0,0,6
	.byte	'Ifx_CPU_DBGSR_Bits',0,11,177,1,3
	.word	75524
	.byte	8
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,11,180,1,16,4,9
	.byte	'DTA',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_DBGTCR_Bits',0,11,184,1,3
	.word	75739
	.byte	8
	.byte	'_Ifx_CPU_DCON0_Bits',0,11,187,1,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'DCBYP',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_CPU_DCON0_Bits',0,11,192,1,3
	.word	75833
	.byte	8
	.byte	'_Ifx_CPU_DCON2_Bits',0,11,195,1,16,4,9
	.byte	'DCACHE_SZE',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'DSCRATCH_SZE',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_DCON2_Bits',0,11,199,1,3
	.word	75949
	.byte	8
	.byte	'_Ifx_CPU_DCX_Bits',0,11,202,1,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	6,26,2,35,0,9
	.byte	'DCXValue',0,4
	.word	16230
	.byte	26,0,2,35,0,0,6
	.byte	'Ifx_CPU_DCX_Bits',0,11,206,1,3
	.word	76050
	.byte	8
	.byte	'_Ifx_CPU_DEADD_Bits',0,11,209,1,16,4,9
	.byte	'ERROR_ADDRESS',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_DEADD_Bits',0,11,212,1,3
	.word	76143
	.byte	8
	.byte	'_Ifx_CPU_DIEAR_Bits',0,11,215,1,16,4,9
	.byte	'TA',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_DIEAR_Bits',0,11,218,1,3
	.word	76223
	.byte	8
	.byte	'_Ifx_CPU_DIETR_Bits',0,11,221,1,16,4,9
	.byte	'IED',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'IE_T',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'IE_C',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'IE_S',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'IE_BI',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'E_INFO',0,4
	.word	16230
	.byte	6,21,2,35,0,9
	.byte	'IE_DUAL',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'IE_SP',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'IE_BS',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	16230
	.byte	18,0,2,35,0,0,6
	.byte	'Ifx_CPU_DIETR_Bits',0,11,233,1,3
	.word	76292
	.byte	8
	.byte	'_Ifx_CPU_DMS_Bits',0,11,236,1,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'DMSValue',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_DMS_Bits',0,11,240,1,3
	.word	76521
	.byte	8
	.byte	'_Ifx_CPU_DPR_L_Bits',0,11,243,1,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'LOWBND',0,4
	.word	16230
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_DPR_L_Bits',0,11,247,1,3
	.word	76614
	.byte	8
	.byte	'_Ifx_CPU_DPR_U_Bits',0,11,250,1,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'UPPBND',0,4
	.word	16230
	.byte	29,0,2,35,0,0,6
	.byte	'Ifx_CPU_DPR_U_Bits',0,11,254,1,3
	.word	76709
	.byte	8
	.byte	'_Ifx_CPU_DPRE_Bits',0,11,129,2,16,4,9
	.byte	'RE',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_DPRE_Bits',0,11,133,2,3
	.word	76804
	.byte	8
	.byte	'_Ifx_CPU_DPWE_Bits',0,11,136,2,16,4,9
	.byte	'WE',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_DPWE_Bits',0,11,140,2,3
	.word	76894
	.byte	8
	.byte	'_Ifx_CPU_DSTR_Bits',0,11,143,2,16,4,9
	.byte	'SRE',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'GAE',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'LBE',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	3,26,2,35,0,9
	.byte	'CRE',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'reserved_7',0,4
	.word	16230
	.byte	7,18,2,35,0,9
	.byte	'DTME',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'LOE',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'SDE',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'SCE',0,4
	.word	16230
	.byte	1,14,2,35,0,9
	.byte	'CAC',0,4
	.word	16230
	.byte	1,13,2,35,0,9
	.byte	'MPE',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'CLE',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	16230
	.byte	3,8,2,35,0,9
	.byte	'ALN',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	16230
	.byte	7,0,2,35,0,0,6
	.byte	'Ifx_CPU_DSTR_Bits',0,11,161,2,3
	.word	76984
	.byte	8
	.byte	'_Ifx_CPU_EXEVT_Bits',0,11,164,2,16,4,9
	.byte	'EVTA',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_EXEVT_Bits',0,11,172,2,3
	.word	77308
	.byte	8
	.byte	'_Ifx_CPU_FCX_Bits',0,11,175,2,16,4,9
	.byte	'FCXO',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'FCXS',0,4
	.word	16230
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	16230
	.byte	12,0,2,35,0,0,6
	.byte	'Ifx_CPU_FCX_Bits',0,11,180,2,3
	.word	77462
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,11,183,2,16,4,9
	.byte	'TST',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TCL',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	6,24,2,35,0,9
	.byte	'RM',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	16230
	.byte	8,14,2,35,0,9
	.byte	'FXE',0,4
	.word	16230
	.byte	1,13,2,35,0,9
	.byte	'FUE',0,4
	.word	16230
	.byte	1,12,2,35,0,9
	.byte	'FZE',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'FVE',0,4
	.word	16230
	.byte	1,10,2,35,0,9
	.byte	'FIE',0,4
	.word	16230
	.byte	1,9,2,35,0,9
	.byte	'reserved_23',0,4
	.word	16230
	.byte	3,6,2,35,0,9
	.byte	'FX',0,4
	.word	16230
	.byte	1,5,2,35,0,9
	.byte	'FU',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'FZ',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'FV',0,4
	.word	16230
	.byte	1,2,2,35,0,9
	.byte	'FI',0,4
	.word	16230
	.byte	1,1,2,35,0,9
	.byte	'reserved_31',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,11,202,2,3
	.word	77568
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,11,205,2,16,4,9
	.byte	'OPC',0,4
	.word	16230
	.byte	8,24,2,35,0,9
	.byte	'FMT',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	16230
	.byte	7,16,2,35,0,9
	.byte	'DREG',0,4
	.word	16230
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	16230
	.byte	12,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,11,212,2,3
	.word	77917
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,11,215,2,16,4,9
	.byte	'PC',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,11,218,2,3
	.word	78077
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,11,221,2,16,4,9
	.byte	'SRC1',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,11,224,2,3
	.word	78158
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,11,227,2,16,4,9
	.byte	'SRC2',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,11,230,2,3
	.word	78245
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,11,233,2,16,4,9
	.byte	'SRC3',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,11,236,2,3
	.word	78332
	.byte	8
	.byte	'_Ifx_CPU_ICNT_Bits',0,11,239,2,16,4,9
	.byte	'CountValue',0,4
	.word	16230
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_ICNT_Bits',0,11,243,2,3
	.word	78419
	.byte	8
	.byte	'_Ifx_CPU_ICR_Bits',0,11,246,2,16,4,9
	.byte	'CCPN',0,4
	.word	16230
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	16230
	.byte	5,17,2,35,0,9
	.byte	'IE',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'PIPN',0,4
	.word	16230
	.byte	10,6,2,35,0,9
	.byte	'reserved_26',0,4
	.word	16230
	.byte	6,0,2,35,0,0,6
	.byte	'Ifx_CPU_ICR_Bits',0,11,253,2,3
	.word	78510
	.byte	8
	.byte	'_Ifx_CPU_ISP_Bits',0,11,128,3,16,4,9
	.byte	'ISP',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_ISP_Bits',0,11,131,3,3
	.word	78653
	.byte	8
	.byte	'_Ifx_CPU_LCX_Bits',0,11,134,3,16,4,9
	.byte	'LCXO',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'LCXS',0,4
	.word	16230
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	16230
	.byte	12,0,2,35,0,0,6
	.byte	'Ifx_CPU_LCX_Bits',0,11,139,3,3
	.word	78719
	.byte	8
	.byte	'_Ifx_CPU_M1CNT_Bits',0,11,142,3,16,4,9
	.byte	'CountValue',0,4
	.word	16230
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_M1CNT_Bits',0,11,146,3,3
	.word	78825
	.byte	8
	.byte	'_Ifx_CPU_M2CNT_Bits',0,11,149,3,16,4,9
	.byte	'CountValue',0,4
	.word	16230
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_M2CNT_Bits',0,11,153,3,3
	.word	78918
	.byte	8
	.byte	'_Ifx_CPU_M3CNT_Bits',0,11,156,3,16,4,9
	.byte	'CountValue',0,4
	.word	16230
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_M3CNT_Bits',0,11,160,3,3
	.word	79011
	.byte	8
	.byte	'_Ifx_CPU_PC_Bits',0,11,163,3,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'PC',0,4
	.word	16230
	.byte	31,0,2,35,0,0,6
	.byte	'Ifx_CPU_PC_Bits',0,11,167,3,3
	.word	79104
	.byte	8
	.byte	'_Ifx_CPU_PCON0_Bits',0,11,170,3,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'PCBYP',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_CPU_PCON0_Bits',0,11,175,3,3
	.word	79189
	.byte	8
	.byte	'_Ifx_CPU_PCON1_Bits',0,11,178,3,16,4,9
	.byte	'PCINV',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'PBINV',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	16230
	.byte	30,0,2,35,0,0,6
	.byte	'Ifx_CPU_PCON1_Bits',0,11,183,3,3
	.word	79305
	.byte	8
	.byte	'_Ifx_CPU_PCON2_Bits',0,11,186,3,16,4,9
	.byte	'PCACHE_SZE',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'PSCRATCH_SZE',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_PCON2_Bits',0,11,190,3,3
	.word	79416
	.byte	8
	.byte	'_Ifx_CPU_PCXI_Bits',0,11,193,3,16,4,9
	.byte	'PCXO',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'PCXS',0,4
	.word	16230
	.byte	4,12,2,35,0,9
	.byte	'UL',0,4
	.word	16230
	.byte	1,11,2,35,0,9
	.byte	'PIE',0,4
	.word	16230
	.byte	1,10,2,35,0,9
	.byte	'PCPN',0,4
	.word	16230
	.byte	10,0,2,35,0,0,6
	.byte	'Ifx_CPU_PCXI_Bits',0,11,200,3,3
	.word	79517
	.byte	8
	.byte	'_Ifx_CPU_PIEAR_Bits',0,11,203,3,16,4,9
	.byte	'TA',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_PIEAR_Bits',0,11,206,3,3
	.word	79647
	.byte	8
	.byte	'_Ifx_CPU_PIETR_Bits',0,11,209,3,16,4,9
	.byte	'IED',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'IE_T',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'IE_C',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'IE_S',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'IE_BI',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'E_INFO',0,4
	.word	16230
	.byte	6,21,2,35,0,9
	.byte	'IE_DUAL',0,4
	.word	16230
	.byte	1,20,2,35,0,9
	.byte	'IE_SP',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'IE_BS',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	16230
	.byte	18,0,2,35,0,0,6
	.byte	'Ifx_CPU_PIETR_Bits',0,11,221,3,3
	.word	79716
	.byte	8
	.byte	'_Ifx_CPU_PMA0_Bits',0,11,224,3,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	13,19,2,35,0,9
	.byte	'DAC',0,4
	.word	16230
	.byte	3,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_PMA0_Bits',0,11,229,3,3
	.word	79945
	.byte	8
	.byte	'_Ifx_CPU_PMA1_Bits',0,11,232,3,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	14,18,2,35,0,9
	.byte	'CAC',0,4
	.word	16230
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_PMA1_Bits',0,11,237,3,3
	.word	80058
	.byte	8
	.byte	'_Ifx_CPU_PMA2_Bits',0,11,240,3,16,4,9
	.byte	'PSI',0,4
	.word	16230
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	16230
	.byte	16,0,2,35,0,0,6
	.byte	'Ifx_CPU_PMA2_Bits',0,11,244,3,3
	.word	80171
	.byte	8
	.byte	'_Ifx_CPU_PSTR_Bits',0,11,247,3,16,4,9
	.byte	'FRE',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'FBE',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	9,20,2,35,0,9
	.byte	'FPE',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'FME',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	16230
	.byte	17,0,2,35,0,0,6
	.byte	'Ifx_CPU_PSTR_Bits',0,11,129,4,3
	.word	80262
	.byte	8
	.byte	'_Ifx_CPU_PSW_Bits',0,11,132,4,16,4,9
	.byte	'CDC',0,4
	.word	16230
	.byte	7,25,2,35,0,9
	.byte	'CDE',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'GW',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'IS',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'IO',0,4
	.word	16230
	.byte	2,20,2,35,0,9
	.byte	'PRS',0,4
	.word	16230
	.byte	2,18,2,35,0,9
	.byte	'S',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	16230
	.byte	12,5,2,35,0,9
	.byte	'SAV',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'AV',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'SV',0,4
	.word	16230
	.byte	1,2,2,35,0,9
	.byte	'V',0,4
	.word	16230
	.byte	1,1,2,35,0,9
	.byte	'C',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_PSW_Bits',0,11,147,4,3
	.word	80465
	.byte	8
	.byte	'_Ifx_CPU_SEGEN_Bits',0,11,150,4,16,4,9
	.byte	'ADFLIP',0,4
	.word	16230
	.byte	8,24,2,35,0,9
	.byte	'ADTYPE',0,4
	.word	16230
	.byte	2,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	16230
	.byte	21,1,2,35,0,9
	.byte	'AE',0,4
	.word	16230
	.byte	1,0,2,35,0,0,6
	.byte	'Ifx_CPU_SEGEN_Bits',0,11,156,4,3
	.word	80708
	.byte	8
	.byte	'_Ifx_CPU_SMACON_Bits',0,11,159,4,16,4,9
	.byte	'PC',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'PT',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	5,24,2,35,0,9
	.byte	'DC',0,4
	.word	16230
	.byte	1,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	16230
	.byte	1,22,2,35,0,9
	.byte	'DT',0,4
	.word	16230
	.byte	1,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	16230
	.byte	13,8,2,35,0,9
	.byte	'IODT',0,4
	.word	16230
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	16230
	.byte	7,0,2,35,0,0,6
	.byte	'Ifx_CPU_SMACON_Bits',0,11,171,4,3
	.word	80836
	.byte	8
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,11,174,4,16,4,9
	.byte	'EN',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,11,177,4,3
	.word	81077
	.byte	8
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,11,180,4,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,11,183,4,3
	.word	81160
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,11,186,4,16,4,9
	.byte	'EN',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,11,189,4,3
	.word	81251
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,11,192,4,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,11,195,4,3
	.word	81342
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,11,198,4,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	5,27,2,35,0,9
	.byte	'ADDR',0,4
	.word	16230
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,11,202,4,3
	.word	81441
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,11,205,4,16,4,9
	.byte	'reserved_0',0,4
	.word	16230
	.byte	5,27,2,35,0,9
	.byte	'ADDR',0,4
	.word	16230
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,11,209,4,3
	.word	81548
	.byte	8
	.byte	'_Ifx_CPU_SWEVT_Bits',0,11,212,4,16,4,9
	.byte	'EVTA',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_SWEVT_Bits',0,11,220,4,3
	.word	81655
	.byte	8
	.byte	'_Ifx_CPU_SYSCON_Bits',0,11,223,4,16,4,9
	.byte	'FCDSF',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'PROTEN',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'TPROTEN',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'IS',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'IT',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_SYSCON_Bits',0,11,231,4,3
	.word	81809
	.byte	8
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,11,234,4,16,4,9
	.byte	'ASI',0,4
	.word	16230
	.byte	5,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	16230
	.byte	27,0,2,35,0,0,6
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,11,238,4,3
	.word	81970
	.byte	8
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,11,241,4,16,4,9
	.byte	'TEXP0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'TEXP1',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'TEXP2',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	16230
	.byte	13,16,2,35,0,9
	.byte	'TTRAP',0,4
	.word	16230
	.byte	1,15,2,35,0,9
	.byte	'reserved_17',0,4
	.word	16230
	.byte	15,0,2,35,0,0,6
	.byte	'Ifx_CPU_TPS_CON_Bits',0,11,249,4,3
	.word	82068
	.byte	8
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,11,252,4,16,4,9
	.byte	'Timer',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,11,255,4,3
	.word	82240
	.byte	8
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,11,130,5,16,4,9
	.byte	'ADDR',0,4
	.word	16230
	.byte	32,0,2,35,0,0,6
	.byte	'Ifx_CPU_TR_ADR_Bits',0,11,133,5,3
	.word	82320
	.byte	8
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,11,136,5,16,4,9
	.byte	'EVTA',0,4
	.word	16230
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	16230
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	4,20,2,35,0,9
	.byte	'TYP',0,4
	.word	16230
	.byte	1,19,2,35,0,9
	.byte	'RNG',0,4
	.word	16230
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	16230
	.byte	1,17,2,35,0,9
	.byte	'ASI_EN',0,4
	.word	16230
	.byte	1,16,2,35,0,9
	.byte	'ASI',0,4
	.word	16230
	.byte	5,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	16230
	.byte	6,5,2,35,0,9
	.byte	'AST',0,4
	.word	16230
	.byte	1,4,2,35,0,9
	.byte	'ALD',0,4
	.word	16230
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	16230
	.byte	3,0,2,35,0,0,6
	.byte	'Ifx_CPU_TR_EVT_Bits',0,11,153,5,3
	.word	82393
	.byte	8
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,11,156,5,16,4,9
	.byte	'T0',0,4
	.word	16230
	.byte	1,31,2,35,0,9
	.byte	'T1',0,4
	.word	16230
	.byte	1,30,2,35,0,9
	.byte	'T2',0,4
	.word	16230
	.byte	1,29,2,35,0,9
	.byte	'T3',0,4
	.word	16230
	.byte	1,28,2,35,0,9
	.byte	'T4',0,4
	.word	16230
	.byte	1,27,2,35,0,9
	.byte	'T5',0,4
	.word	16230
	.byte	1,26,2,35,0,9
	.byte	'T6',0,4
	.word	16230
	.byte	1,25,2,35,0,9
	.byte	'T7',0,4
	.word	16230
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	16230
	.byte	24,0,2,35,0,0,6
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,11,167,5,3
	.word	82711
	.byte	10,11,175,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	73929
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_A',0,11,180,5,3
	.word	82906
	.byte	10,11,183,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	73990
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_BIV',0,11,188,5,3
	.word	82965
	.byte	10,11,191,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74069
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_BTV',0,11,196,5,3
	.word	83026
	.byte	10,11,199,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74155
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CCNT',0,11,204,5,3
	.word	83087
	.byte	10,11,207,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74244
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CCTRL',0,11,212,5,3
	.word	83149
	.byte	10,11,215,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74390
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_COMPAT',0,11,220,5,3
	.word	83212
	.byte	10,11,223,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74517
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CORE_ID',0,11,228,5,3
	.word	83276
	.byte	10,11,231,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74615
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CPR_L',0,11,236,5,3
	.word	83341
	.byte	10,11,239,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74708
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CPR_U',0,11,244,5,3
	.word	83404
	.byte	10,11,247,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74801
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CPU_ID',0,11,252,5,3
	.word	83467
	.byte	10,11,255,5,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74908
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CPXE',0,11,132,6,3
	.word	83531
	.byte	10,11,135,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	74995
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CREVT',0,11,140,6,3
	.word	83593
	.byte	10,11,143,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	75149
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_CUS_ID',0,11,148,6,3
	.word	83656
	.byte	10,11,151,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	75243
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_D',0,11,156,6,3
	.word	83720
	.byte	10,11,159,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	75306
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DATR',0,11,164,6,3
	.word	83779
	.byte	10,11,167,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	75524
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DBGSR',0,11,172,6,3
	.word	83841
	.byte	10,11,175,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	75739
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DBGTCR',0,11,180,6,3
	.word	83904
	.byte	10,11,183,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	75833
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DCON0',0,11,188,6,3
	.word	83968
	.byte	10,11,191,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	75949
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DCON2',0,11,196,6,3
	.word	84031
	.byte	10,11,199,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76050
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DCX',0,11,204,6,3
	.word	84094
	.byte	10,11,207,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76143
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DEADD',0,11,212,6,3
	.word	84155
	.byte	10,11,215,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76223
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DIEAR',0,11,220,6,3
	.word	84218
	.byte	10,11,223,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76292
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DIETR',0,11,228,6,3
	.word	84281
	.byte	10,11,231,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76521
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DMS',0,11,236,6,3
	.word	84344
	.byte	10,11,239,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76614
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DPR_L',0,11,244,6,3
	.word	84405
	.byte	10,11,247,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76709
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DPR_U',0,11,252,6,3
	.word	84468
	.byte	10,11,255,6,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76804
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DPRE',0,11,132,7,3
	.word	84531
	.byte	10,11,135,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76894
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DPWE',0,11,140,7,3
	.word	84593
	.byte	10,11,143,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	76984
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_DSTR',0,11,148,7,3
	.word	84655
	.byte	10,11,151,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	77308
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_EXEVT',0,11,156,7,3
	.word	84717
	.byte	10,11,159,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	77462
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FCX',0,11,164,7,3
	.word	84780
	.byte	10,11,167,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	77568
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,11,172,7,3
	.word	84841
	.byte	10,11,175,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	77917
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,11,180,7,3
	.word	84911
	.byte	10,11,183,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78077
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,11,188,7,3
	.word	84981
	.byte	10,11,191,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78158
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,11,196,7,3
	.word	85050
	.byte	10,11,199,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78245
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,11,204,7,3
	.word	85121
	.byte	10,11,207,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78332
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,11,212,7,3
	.word	85192
	.byte	10,11,215,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78419
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_ICNT',0,11,220,7,3
	.word	85263
	.byte	10,11,223,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78510
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_ICR',0,11,228,7,3
	.word	85325
	.byte	10,11,231,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78653
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_ISP',0,11,236,7,3
	.word	85386
	.byte	10,11,239,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78719
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_LCX',0,11,244,7,3
	.word	85447
	.byte	10,11,247,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78825
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_M1CNT',0,11,252,7,3
	.word	85508
	.byte	10,11,255,7,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	78918
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_M2CNT',0,11,132,8,3
	.word	85571
	.byte	10,11,135,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79011
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_M3CNT',0,11,140,8,3
	.word	85634
	.byte	10,11,143,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79104
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PC',0,11,148,8,3
	.word	85697
	.byte	10,11,151,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79189
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PCON0',0,11,156,8,3
	.word	85757
	.byte	10,11,159,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79305
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PCON1',0,11,164,8,3
	.word	85820
	.byte	10,11,167,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79416
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PCON2',0,11,172,8,3
	.word	85883
	.byte	10,11,175,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79517
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PCXI',0,11,180,8,3
	.word	85946
	.byte	10,11,183,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79647
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PIEAR',0,11,188,8,3
	.word	86008
	.byte	10,11,191,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79716
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PIETR',0,11,196,8,3
	.word	86071
	.byte	10,11,199,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	79945
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PMA0',0,11,204,8,3
	.word	86134
	.byte	10,11,207,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	80058
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PMA1',0,11,212,8,3
	.word	86196
	.byte	10,11,215,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	80171
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PMA2',0,11,220,8,3
	.word	86258
	.byte	10,11,223,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	80262
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PSTR',0,11,228,8,3
	.word	86320
	.byte	10,11,231,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	80465
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_PSW',0,11,236,8,3
	.word	86382
	.byte	10,11,239,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	80708
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SEGEN',0,11,244,8,3
	.word	86443
	.byte	10,11,247,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	80836
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SMACON',0,11,252,8,3
	.word	86506
	.byte	10,11,255,8,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81077
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_ACCENA',0,11,132,9,3
	.word	86570
	.byte	10,11,135,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81160
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_ACCENB',0,11,140,9,3
	.word	86640
	.byte	10,11,143,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81251
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,11,148,9,3
	.word	86710
	.byte	10,11,151,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81342
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,11,156,9,3
	.word	86784
	.byte	10,11,159,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81441
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,11,164,9,3
	.word	86858
	.byte	10,11,167,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81548
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,11,172,9,3
	.word	86928
	.byte	10,11,175,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81655
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SWEVT',0,11,180,9,3
	.word	86998
	.byte	10,11,183,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81809
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_SYSCON',0,11,188,9,3
	.word	87061
	.byte	10,11,191,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	81970
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TASK_ASI',0,11,196,9,3
	.word	87125
	.byte	10,11,199,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	82068
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TPS_CON',0,11,204,9,3
	.word	87191
	.byte	10,11,207,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	82240
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TPS_TIMER',0,11,212,9,3
	.word	87256
	.byte	10,11,215,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	82320
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TR_ADR',0,11,220,9,3
	.word	87323
	.byte	10,11,223,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	82393
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TR_EVT',0,11,228,9,3
	.word	87387
	.byte	10,11,231,9,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	82711
	.byte	2,35,0,0,6
	.byte	'Ifx_CPU_TRIG_ACC',0,11,236,9,3
	.word	87451
	.byte	8
	.byte	'_Ifx_CPU_CPR',0,11,247,9,25,8,11
	.byte	'L',0,4
	.word	83341
	.byte	2,35,0,11
	.byte	'U',0,4
	.word	83404
	.byte	2,35,4,0,12
	.word	87517
	.byte	6
	.byte	'Ifx_CPU_CPR',0,11,251,9,3
	.word	87559
	.byte	8
	.byte	'_Ifx_CPU_DPR',0,11,254,9,25,8,11
	.byte	'L',0,4
	.word	84405
	.byte	2,35,0,11
	.byte	'U',0,4
	.word	84468
	.byte	2,35,4,0,12
	.word	87585
	.byte	6
	.byte	'Ifx_CPU_DPR',0,11,130,10,3
	.word	87627
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN',0,11,133,10,25,16,11
	.byte	'LA',0,4
	.word	86858
	.byte	2,35,0,11
	.byte	'UA',0,4
	.word	86928
	.byte	2,35,4,11
	.byte	'ACCENA',0,4
	.word	86710
	.byte	2,35,8,11
	.byte	'ACCENB',0,4
	.word	86784
	.byte	2,35,12,0,12
	.word	87653
	.byte	6
	.byte	'Ifx_CPU_SPROT_RGN',0,11,139,10,3
	.word	87735
	.byte	8
	.byte	'_Ifx_CPU_TPS',0,11,142,10,25,16,11
	.byte	'CON',0,4
	.word	87191
	.byte	2,35,0,13,12
	.word	87256
	.byte	14,2,0,11
	.byte	'TIMER',0,12
	.word	87799
	.byte	2,35,4,0,12
	.word	87767
	.byte	6
	.byte	'Ifx_CPU_TPS',0,11,146,10,3
	.word	87824
	.byte	8
	.byte	'_Ifx_CPU_TR',0,11,149,10,25,8,11
	.byte	'EVT',0,4
	.word	87387
	.byte	2,35,0,11
	.byte	'ADR',0,4
	.word	87323
	.byte	2,35,4,0,12
	.word	87850
	.byte	6
	.byte	'Ifx_CPU_TR',0,11,153,10,3
	.word	87895
	.byte	8
	.byte	'_Ifx_SRC_SRCR_Bits',0,12,45,16,4,9
	.byte	'SRPN',0,1
	.word	227
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	227
	.byte	2,6,2,35,1,9
	.byte	'SRE',0,1
	.word	227
	.byte	1,5,2,35,1,9
	.byte	'TOS',0,1
	.word	227
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	227
	.byte	4,0,2,35,1,9
	.byte	'ECC',0,1
	.word	227
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	227
	.byte	3,0,2,35,2,9
	.byte	'SRR',0,1
	.word	227
	.byte	1,7,2,35,3,9
	.byte	'CLRR',0,1
	.word	227
	.byte	1,6,2,35,3,9
	.byte	'SETR',0,1
	.word	227
	.byte	1,5,2,35,3,9
	.byte	'IOV',0,1
	.word	227
	.byte	1,4,2,35,3,9
	.byte	'IOVCLR',0,1
	.word	227
	.byte	1,3,2,35,3,9
	.byte	'SWS',0,1
	.word	227
	.byte	1,2,2,35,3,9
	.byte	'SWSCLR',0,1
	.word	227
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	227
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SRC_SRCR_Bits',0,12,62,3
	.word	87920
	.byte	10,12,70,9,4,11
	.byte	'U',0,4
	.word	425
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	17419
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	87920
	.byte	2,35,0,0,6
	.byte	'Ifx_SRC_SRCR',0,12,75,3
	.word	88236
	.byte	8
	.byte	'_Ifx_SRC_ASCLIN',0,12,86,25,12,11
	.byte	'TX',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	88236
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	88236
	.byte	2,35,8,0,12
	.word	88296
	.byte	6
	.byte	'Ifx_SRC_ASCLIN',0,12,91,3
	.word	88355
	.byte	8
	.byte	'_Ifx_SRC_BCUSPB',0,12,94,25,4,11
	.byte	'SBSRC',0,4
	.word	88236
	.byte	2,35,0,0,12
	.word	88383
	.byte	6
	.byte	'Ifx_SRC_BCUSPB',0,12,97,3
	.word	88420
	.byte	8
	.byte	'_Ifx_SRC_CAN',0,12,100,25,64,13,64
	.word	88236
	.byte	14,15,0,11
	.byte	'INT',0,64
	.word	88466
	.byte	2,35,0,0,12
	.word	88448
	.byte	6
	.byte	'Ifx_SRC_CAN',0,12,103,3
	.word	88489
	.byte	8
	.byte	'_Ifx_SRC_CAN1',0,12,106,25,32,13,32
	.word	88236
	.byte	14,7,0,11
	.byte	'INT',0,32
	.word	88533
	.byte	2,35,0,0,12
	.word	88514
	.byte	6
	.byte	'Ifx_SRC_CAN1',0,12,109,3
	.word	88556
	.byte	8
	.byte	'_Ifx_SRC_CCU6',0,12,112,25,16,11
	.byte	'SR0',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	88236
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	88236
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	88236
	.byte	2,35,12,0,12
	.word	88582
	.byte	6
	.byte	'Ifx_SRC_CCU6',0,12,118,3
	.word	88654
	.byte	8
	.byte	'_Ifx_SRC_CERBERUS',0,12,121,25,8,13,8
	.word	88236
	.byte	14,1,0,11
	.byte	'SR',0,8
	.word	88703
	.byte	2,35,0,0,12
	.word	88680
	.byte	6
	.byte	'Ifx_SRC_CERBERUS',0,12,124,3
	.word	88725
	.byte	8
	.byte	'_Ifx_SRC_CPU',0,12,127,25,32,11
	.byte	'SBSRC',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'reserved_4',0,28
	.word	52751
	.byte	2,35,4,0,12
	.word	88755
	.byte	6
	.byte	'Ifx_SRC_CPU',0,12,131,1,3
	.word	88809
	.byte	8
	.byte	'_Ifx_SRC_DMA',0,12,134,1,25,80,11
	.byte	'ERR',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'reserved_4',0,12
	.word	53938
	.byte	2,35,4,11
	.byte	'CH',0,64
	.word	88466
	.byte	2,35,16,0,12
	.word	88835
	.byte	6
	.byte	'Ifx_SRC_DMA',0,12,139,1,3
	.word	88900
	.byte	8
	.byte	'_Ifx_SRC_EMEM',0,12,142,1,25,4,11
	.byte	'SR',0,4
	.word	88236
	.byte	2,35,0,0,12
	.word	88926
	.byte	6
	.byte	'Ifx_SRC_EMEM',0,12,145,1,3
	.word	88959
	.byte	8
	.byte	'_Ifx_SRC_ERAY',0,12,148,1,25,80,11
	.byte	'INT',0,8
	.word	88703
	.byte	2,35,0,11
	.byte	'TINT',0,8
	.word	88703
	.byte	2,35,8,11
	.byte	'NDAT',0,8
	.word	88703
	.byte	2,35,16,11
	.byte	'MBSC',0,8
	.word	88703
	.byte	2,35,24,11
	.byte	'OBUSY',0,4
	.word	88236
	.byte	2,35,32,11
	.byte	'IBUSY',0,4
	.word	88236
	.byte	2,35,36,13,40
	.word	227
	.byte	14,39,0,11
	.byte	'reserved_28',0,40
	.word	89091
	.byte	2,35,40,0,12
	.word	88986
	.byte	6
	.byte	'Ifx_SRC_ERAY',0,12,157,1,3
	.word	89122
	.byte	8
	.byte	'_Ifx_SRC_ETH',0,12,160,1,25,4,11
	.byte	'SR',0,4
	.word	88236
	.byte	2,35,0,0,12
	.word	89149
	.byte	6
	.byte	'Ifx_SRC_ETH',0,12,163,1,3
	.word	89181
	.byte	8
	.byte	'_Ifx_SRC_EVR',0,12,166,1,25,8,11
	.byte	'WUT',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'SCDC',0,4
	.word	88236
	.byte	2,35,4,0,12
	.word	89207
	.byte	6
	.byte	'Ifx_SRC_EVR',0,12,170,1,3
	.word	89254
	.byte	8
	.byte	'_Ifx_SRC_FFT',0,12,173,1,25,12,11
	.byte	'DONE',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'ERR',0,4
	.word	88236
	.byte	2,35,4,11
	.byte	'RFS',0,4
	.word	88236
	.byte	2,35,8,0,12
	.word	89280
	.byte	6
	.byte	'Ifx_SRC_FFT',0,12,178,1,3
	.word	89340
	.byte	8
	.byte	'_Ifx_SRC_GPSR',0,12,181,1,25,128,12,11
	.byte	'SR0',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	88236
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	88236
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	88236
	.byte	2,35,12,13,240,11
	.word	227
	.byte	14,239,11,0,11
	.byte	'reserved_10',0,240,11
	.word	89439
	.byte	2,35,16,0,12
	.word	89366
	.byte	6
	.byte	'Ifx_SRC_GPSR',0,12,188,1,3
	.word	89473
	.byte	8
	.byte	'_Ifx_SRC_GPT12',0,12,191,1,25,48,11
	.byte	'CIRQ',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'T2',0,4
	.word	88236
	.byte	2,35,4,11
	.byte	'T3',0,4
	.word	88236
	.byte	2,35,8,11
	.byte	'T4',0,4
	.word	88236
	.byte	2,35,12,11
	.byte	'T5',0,4
	.word	88236
	.byte	2,35,16,11
	.byte	'T6',0,4
	.word	88236
	.byte	2,35,20,13,24
	.word	227
	.byte	14,23,0,11
	.byte	'reserved_18',0,24
	.word	89595
	.byte	2,35,24,0,12
	.word	89500
	.byte	6
	.byte	'Ifx_SRC_GPT12',0,12,200,1,3
	.word	89626
	.byte	8
	.byte	'_Ifx_SRC_GTM',0,12,203,1,25,192,11,11
	.byte	'AEIIRQ',0,4
	.word	88236
	.byte	2,35,0,13,236,2
	.word	227
	.byte	14,235,2,0,11
	.byte	'reserved_4',0,236,2
	.word	89690
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	88236
	.byte	3,35,240,2,11
	.byte	'reserved_174',0,12
	.word	53938
	.byte	3,35,244,2,13,32
	.word	88533
	.byte	14,0,0,11
	.byte	'TIM',0,32
	.word	89759
	.byte	3,35,128,3,13,224,7
	.word	227
	.byte	14,223,7,0,11
	.byte	'reserved_1A0',0,224,7
	.word	89782
	.byte	3,35,160,3,13,64
	.word	88533
	.byte	14,1,0,11
	.byte	'TOM',0,64
	.word	89817
	.byte	3,35,128,11,0,12
	.word	89654
	.byte	6
	.byte	'Ifx_SRC_GTM',0,12,212,1,3
	.word	89841
	.byte	8
	.byte	'_Ifx_SRC_HSM',0,12,215,1,25,8,11
	.byte	'HSM',0,8
	.word	88703
	.byte	2,35,0,0,12
	.word	89867
	.byte	6
	.byte	'Ifx_SRC_HSM',0,12,218,1,3
	.word	89900
	.byte	8
	.byte	'_Ifx_SRC_LMU',0,12,221,1,25,4,11
	.byte	'SR',0,4
	.word	88236
	.byte	2,35,0,0,12
	.word	89926
	.byte	6
	.byte	'Ifx_SRC_LMU',0,12,224,1,3
	.word	89958
	.byte	8
	.byte	'_Ifx_SRC_PMU',0,12,227,1,25,4,11
	.byte	'SR',0,4
	.word	88236
	.byte	2,35,0,0,12
	.word	89984
	.byte	6
	.byte	'Ifx_SRC_PMU',0,12,230,1,3
	.word	90016
	.byte	8
	.byte	'_Ifx_SRC_QSPI',0,12,233,1,25,24,11
	.byte	'TX',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	88236
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	88236
	.byte	2,35,8,11
	.byte	'PT',0,4
	.word	88236
	.byte	2,35,12,11
	.byte	'HC',0,4
	.word	88236
	.byte	2,35,16,11
	.byte	'U',0,4
	.word	88236
	.byte	2,35,20,0,12
	.word	90042
	.byte	6
	.byte	'Ifx_SRC_QSPI',0,12,241,1,3
	.word	90135
	.byte	8
	.byte	'_Ifx_SRC_SCU',0,12,244,1,25,20,11
	.byte	'DTS',0,4
	.word	88236
	.byte	2,35,0,13,16
	.word	88236
	.byte	14,3,0,11
	.byte	'ERU',0,16
	.word	90194
	.byte	2,35,4,0,12
	.word	90162
	.byte	6
	.byte	'Ifx_SRC_SCU',0,12,248,1,3
	.word	90217
	.byte	8
	.byte	'_Ifx_SRC_SENT',0,12,251,1,25,16,11
	.byte	'SR',0,16
	.word	90194
	.byte	2,35,0,0,12
	.word	90243
	.byte	6
	.byte	'Ifx_SRC_SENT',0,12,254,1,3
	.word	90276
	.byte	8
	.byte	'_Ifx_SRC_SMU',0,12,129,2,25,12,13,12
	.word	88236
	.byte	14,2,0,11
	.byte	'SR',0,12
	.word	90322
	.byte	2,35,0,0,12
	.word	90303
	.byte	6
	.byte	'Ifx_SRC_SMU',0,12,132,2,3
	.word	90344
	.byte	8
	.byte	'_Ifx_SRC_STM',0,12,135,2,25,96,11
	.byte	'SR0',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	88236
	.byte	2,35,4,13,88
	.word	227
	.byte	14,87,0,11
	.byte	'reserved_8',0,88
	.word	90415
	.byte	2,35,8,0,12
	.word	90370
	.byte	6
	.byte	'Ifx_SRC_STM',0,12,140,2,3
	.word	90445
	.byte	8
	.byte	'_Ifx_SRC_VADCCG',0,12,143,2,25,192,2,11
	.byte	'SR0',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	88236
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	88236
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	88236
	.byte	2,35,12,13,176,2
	.word	227
	.byte	14,175,2,0,11
	.byte	'reserved_10',0,176,2
	.word	90546
	.byte	2,35,16,0,12
	.word	90471
	.byte	6
	.byte	'Ifx_SRC_VADCCG',0,12,150,2,3
	.word	90580
	.byte	8
	.byte	'_Ifx_SRC_VADCG',0,12,153,2,25,16,11
	.byte	'SR0',0,4
	.word	88236
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	88236
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	88236
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	88236
	.byte	2,35,12,0,12
	.word	90609
	.byte	6
	.byte	'Ifx_SRC_VADCG',0,12,159,2,3
	.word	90683
	.byte	8
	.byte	'_Ifx_SRC_XBAR',0,12,162,2,25,4,11
	.byte	'SRC',0,4
	.word	88236
	.byte	2,35,0,0,12
	.word	90711
	.byte	6
	.byte	'Ifx_SRC_XBAR',0,12,165,2,3
	.word	90745
	.byte	8
	.byte	'_Ifx_SRC_GASCLIN',0,12,178,2,25,24,13,24
	.word	88296
	.byte	14,1,0,12
	.word	90795
	.byte	11
	.byte	'ASCLIN',0,24
	.word	90804
	.byte	2,35,0,0,12
	.word	90772
	.byte	6
	.byte	'Ifx_SRC_GASCLIN',0,12,181,2,3
	.word	90826
	.byte	8
	.byte	'_Ifx_SRC_GBCU',0,12,184,2,25,4,12
	.word	88383
	.byte	11
	.byte	'SPB',0,4
	.word	90876
	.byte	2,35,0,0,12
	.word	90856
	.byte	6
	.byte	'Ifx_SRC_GBCU',0,12,187,2,3
	.word	90895
	.byte	8
	.byte	'_Ifx_SRC_GCAN',0,12,190,2,25,96,13,64
	.word	88448
	.byte	14,0,0,12
	.word	90942
	.byte	11
	.byte	'CAN',0,64
	.word	90951
	.byte	2,35,0,13,32
	.word	88514
	.byte	14,0,0,12
	.word	90969
	.byte	11
	.byte	'CAN1',0,32
	.word	90978
	.byte	2,35,64,0,12
	.word	90922
	.byte	6
	.byte	'Ifx_SRC_GCAN',0,12,194,2,3
	.word	90998
	.byte	8
	.byte	'_Ifx_SRC_GCCU6',0,12,197,2,25,32,13,32
	.word	88582
	.byte	14,1,0,12
	.word	91046
	.byte	11
	.byte	'CCU6',0,32
	.word	91055
	.byte	2,35,0,0,12
	.word	91025
	.byte	6
	.byte	'Ifx_SRC_GCCU6',0,12,200,2,3
	.word	91075
	.byte	8
	.byte	'_Ifx_SRC_GCERBERUS',0,12,203,2,25,8,12
	.word	88680
	.byte	11
	.byte	'CERBERUS',0,8
	.word	91128
	.byte	2,35,0,0,12
	.word	91103
	.byte	6
	.byte	'Ifx_SRC_GCERBERUS',0,12,206,2,3
	.word	91152
	.byte	8
	.byte	'_Ifx_SRC_GCPU',0,12,209,2,25,32,13,32
	.word	88755
	.byte	14,0,0,12
	.word	91204
	.byte	11
	.byte	'CPU',0,32
	.word	91213
	.byte	2,35,0,0,12
	.word	91184
	.byte	6
	.byte	'Ifx_SRC_GCPU',0,12,212,2,3
	.word	91232
	.byte	8
	.byte	'_Ifx_SRC_GDMA',0,12,215,2,25,80,13,80
	.word	88835
	.byte	14,0,0,12
	.word	91279
	.byte	11
	.byte	'DMA',0,80
	.word	91288
	.byte	2,35,0,0,12
	.word	91259
	.byte	6
	.byte	'Ifx_SRC_GDMA',0,12,218,2,3
	.word	91307
	.byte	8
	.byte	'_Ifx_SRC_GEMEM',0,12,221,2,25,4,13,4
	.word	88926
	.byte	14,0,0,12
	.word	91355
	.byte	11
	.byte	'EMEM',0,4
	.word	91364
	.byte	2,35,0,0,12
	.word	91334
	.byte	6
	.byte	'Ifx_SRC_GEMEM',0,12,224,2,3
	.word	91384
	.byte	8
	.byte	'_Ifx_SRC_GERAY',0,12,227,2,25,80,13,80
	.word	88986
	.byte	14,0,0,12
	.word	91433
	.byte	11
	.byte	'ERAY',0,80
	.word	91442
	.byte	2,35,0,0,12
	.word	91412
	.byte	6
	.byte	'Ifx_SRC_GERAY',0,12,230,2,3
	.word	91462
	.byte	8
	.byte	'_Ifx_SRC_GETH',0,12,233,2,25,4,13,4
	.word	89149
	.byte	14,0,0,12
	.word	91510
	.byte	11
	.byte	'ETH',0,4
	.word	91519
	.byte	2,35,0,0,12
	.word	91490
	.byte	6
	.byte	'Ifx_SRC_GETH',0,12,236,2,3
	.word	91538
	.byte	8
	.byte	'_Ifx_SRC_GEVR',0,12,239,2,25,8,13,8
	.word	89207
	.byte	14,0,0,12
	.word	91585
	.byte	11
	.byte	'EVR',0,8
	.word	91594
	.byte	2,35,0,0,12
	.word	91565
	.byte	6
	.byte	'Ifx_SRC_GEVR',0,12,242,2,3
	.word	91613
	.byte	8
	.byte	'_Ifx_SRC_GFFT',0,12,245,2,25,12,13,12
	.word	89280
	.byte	14,0,0,12
	.word	91660
	.byte	11
	.byte	'FFT',0,12
	.word	91669
	.byte	2,35,0,0,12
	.word	91640
	.byte	6
	.byte	'Ifx_SRC_GFFT',0,12,248,2,3
	.word	91688
	.byte	8
	.byte	'_Ifx_SRC_GGPSR',0,12,251,2,25,128,12,13,128,12
	.word	89366
	.byte	14,0,0,12
	.word	91737
	.byte	11
	.byte	'GPSR',0,128,12
	.word	91747
	.byte	2,35,0,0,12
	.word	91715
	.byte	6
	.byte	'Ifx_SRC_GGPSR',0,12,254,2,3
	.word	91768
	.byte	8
	.byte	'_Ifx_SRC_GGPT12',0,12,129,3,25,48,13,48
	.word	89500
	.byte	14,0,0,12
	.word	91818
	.byte	11
	.byte	'GPT12',0,48
	.word	91827
	.byte	2,35,0,0,12
	.word	91796
	.byte	6
	.byte	'Ifx_SRC_GGPT12',0,12,132,3,3
	.word	91848
	.byte	8
	.byte	'_Ifx_SRC_GGTM',0,12,135,3,25,192,11,13,192,11
	.word	89654
	.byte	14,0,0,12
	.word	91898
	.byte	11
	.byte	'GTM',0,192,11
	.word	91908
	.byte	2,35,0,0,12
	.word	91877
	.byte	6
	.byte	'Ifx_SRC_GGTM',0,12,138,3,3
	.word	91928
	.byte	8
	.byte	'_Ifx_SRC_GHSM',0,12,141,3,25,8,13,8
	.word	89867
	.byte	14,0,0,12
	.word	91975
	.byte	11
	.byte	'HSM',0,8
	.word	91984
	.byte	2,35,0,0,12
	.word	91955
	.byte	6
	.byte	'Ifx_SRC_GHSM',0,12,144,3,3
	.word	92003
	.byte	8
	.byte	'_Ifx_SRC_GLMU',0,12,147,3,25,4,13,4
	.word	89926
	.byte	14,0,0,12
	.word	92050
	.byte	11
	.byte	'LMU',0,4
	.word	92059
	.byte	2,35,0,0,12
	.word	92030
	.byte	6
	.byte	'Ifx_SRC_GLMU',0,12,150,3,3
	.word	92078
	.byte	8
	.byte	'_Ifx_SRC_GPMU',0,12,153,3,25,8,13,8
	.word	89984
	.byte	14,1,0,12
	.word	92125
	.byte	11
	.byte	'PMU',0,8
	.word	92134
	.byte	2,35,0,0,12
	.word	92105
	.byte	6
	.byte	'Ifx_SRC_GPMU',0,12,156,3,3
	.word	92153
	.byte	8
	.byte	'_Ifx_SRC_GQSPI',0,12,159,3,25,96,13,96
	.word	90042
	.byte	14,3,0,12
	.word	92201
	.byte	11
	.byte	'QSPI',0,96
	.word	92210
	.byte	2,35,0,0,12
	.word	92180
	.byte	6
	.byte	'Ifx_SRC_GQSPI',0,12,162,3,3
	.word	92230
	.byte	8
	.byte	'_Ifx_SRC_GSCU',0,12,165,3,25,20,12
	.word	90162
	.byte	11
	.byte	'SCU',0,20
	.word	92278
	.byte	2,35,0,0,12
	.word	92258
	.byte	6
	.byte	'Ifx_SRC_GSCU',0,12,168,3,3
	.word	92297
	.byte	8
	.byte	'_Ifx_SRC_GSENT',0,12,171,3,25,16,13,16
	.word	90243
	.byte	14,0,0,12
	.word	92345
	.byte	11
	.byte	'SENT',0,16
	.word	92354
	.byte	2,35,0,0,12
	.word	92324
	.byte	6
	.byte	'Ifx_SRC_GSENT',0,12,174,3,3
	.word	92374
	.byte	8
	.byte	'_Ifx_SRC_GSMU',0,12,177,3,25,12,13,12
	.word	90303
	.byte	14,0,0,12
	.word	92422
	.byte	11
	.byte	'SMU',0,12
	.word	92431
	.byte	2,35,0,0,12
	.word	92402
	.byte	6
	.byte	'Ifx_SRC_GSMU',0,12,180,3,3
	.word	92450
	.byte	8
	.byte	'_Ifx_SRC_GSTM',0,12,183,3,25,96,13,96
	.word	90370
	.byte	14,0,0,12
	.word	92497
	.byte	11
	.byte	'STM',0,96
	.word	92506
	.byte	2,35,0,0,12
	.word	92477
	.byte	6
	.byte	'Ifx_SRC_GSTM',0,12,186,3,3
	.word	92525
	.byte	8
	.byte	'_Ifx_SRC_GVADC',0,12,189,3,25,224,4,13,64
	.word	90609
	.byte	14,3,0,12
	.word	92574
	.byte	11
	.byte	'G',0,64
	.word	92583
	.byte	2,35,0,13,224,1
	.word	227
	.byte	14,223,1,0,11
	.byte	'reserved_40',0,224,1
	.word	92599
	.byte	2,35,64,13,192,2
	.word	90471
	.byte	14,0,0,12
	.word	92632
	.byte	11
	.byte	'CG',0,192,2
	.word	92642
	.byte	3,35,160,2,0,12
	.word	92552
	.byte	6
	.byte	'Ifx_SRC_GVADC',0,12,194,3,3
	.word	92662
	.byte	8
	.byte	'_Ifx_SRC_GXBAR',0,12,197,3,25,4,12
	.word	90711
	.byte	11
	.byte	'XBAR',0,4
	.word	92711
	.byte	2,35,0,0,12
	.word	92690
	.byte	6
	.byte	'Ifx_SRC_GXBAR',0,12,200,3,3
	.word	92731
	.byte	6
	.byte	'Dma_StatusType',0,13,121,22
	.word	425
	.byte	6
	.byte	'Dma_ErrorStatusType',0,13,141,1,22
	.word	425
	.byte	16,13,147,1,9,1,17
	.byte	'DMA_CHANNEL0',0,0,17
	.byte	'DMA_CHANNEL1',0,1,17
	.byte	'DMA_CHANNEL2',0,2,17
	.byte	'DMA_CHANNEL3',0,3,17
	.byte	'DMA_CHANNEL4',0,4,17
	.byte	'DMA_CHANNEL5',0,5,17
	.byte	'DMA_CHANNEL6',0,6,17
	.byte	'DMA_CHANNEL7',0,7,17
	.byte	'DMA_CHANNEL8',0,8,17
	.byte	'DMA_CHANNEL9',0,9,17
	.byte	'DMA_CHANNEL10',0,10,17
	.byte	'DMA_CHANNEL11',0,11,17
	.byte	'DMA_CHANNEL12',0,12,17
	.byte	'DMA_CHANNEL13',0,13,17
	.byte	'DMA_CHANNEL14',0,14,17
	.byte	'DMA_CHANNEL15',0,15,17
	.byte	'DMA_CHANNEL16',0,16,17
	.byte	'DMA_CHANNEL17',0,17,17
	.byte	'DMA_CHANNEL18',0,18,17
	.byte	'DMA_CHANNEL19',0,19,17
	.byte	'DMA_CHANNEL20',0,20,17
	.byte	'DMA_CHANNEL21',0,21,17
	.byte	'DMA_CHANNEL22',0,22,17
	.byte	'DMA_CHANNEL23',0,23,17
	.byte	'DMA_CHANNEL24',0,24,17
	.byte	'DMA_CHANNEL25',0,25,17
	.byte	'DMA_CHANNEL26',0,26,17
	.byte	'DMA_CHANNEL27',0,27,17
	.byte	'DMA_CHANNEL28',0,28,17
	.byte	'DMA_CHANNEL29',0,29,17
	.byte	'DMA_CHANNEL30',0,30,17
	.byte	'DMA_CHANNEL31',0,31,17
	.byte	'DMA_CHANNEL32',0,32,17
	.byte	'DMA_CHANNEL33',0,33,17
	.byte	'DMA_CHANNEL34',0,34,17
	.byte	'DMA_CHANNEL35',0,35,17
	.byte	'DMA_CHANNEL36',0,36,17
	.byte	'DMA_CHANNEL37',0,37,17
	.byte	'DMA_CHANNEL38',0,38,17
	.byte	'DMA_CHANNEL39',0,39,17
	.byte	'DMA_CHANNEL40',0,40,17
	.byte	'DMA_CHANNEL41',0,41,17
	.byte	'DMA_CHANNEL42',0,42,17
	.byte	'DMA_CHANNEL43',0,43,17
	.byte	'DMA_CHANNEL44',0,44,17
	.byte	'DMA_CHANNEL45',0,45,17
	.byte	'DMA_CHANNEL46',0,46,17
	.byte	'DMA_CHANNEL47',0,47,17
	.byte	'DMA_CHANNEL48',0,48,17
	.byte	'DMA_CHANNEL49',0,49,17
	.byte	'DMA_CHANNEL50',0,50,17
	.byte	'DMA_CHANNEL51',0,51,17
	.byte	'DMA_CHANNEL52',0,52,17
	.byte	'DMA_CHANNEL53',0,53,17
	.byte	'DMA_CHANNEL54',0,54,17
	.byte	'DMA_CHANNEL55',0,55,17
	.byte	'DMA_CHANNEL56',0,56,17
	.byte	'DMA_CHANNEL57',0,57,17
	.byte	'DMA_CHANNEL58',0,58,17
	.byte	'DMA_CHANNEL59',0,59,17
	.byte	'DMA_CHANNEL60',0,60,17
	.byte	'DMA_CHANNEL61',0,61,17
	.byte	'DMA_CHANNEL62',0,62,17
	.byte	'DMA_CHANNEL63',0,63,17
	.byte	'DMA_CHANNEL64',0,192,0,17
	.byte	'DMA_CHANNEL65',0,193,0,17
	.byte	'DMA_CHANNEL66',0,194,0,17
	.byte	'DMA_CHANNEL67',0,195,0,17
	.byte	'DMA_CHANNEL68',0,196,0,17
	.byte	'DMA_CHANNEL69',0,197,0,17
	.byte	'DMA_CHANNEL70',0,198,0,17
	.byte	'DMA_CHANNEL71',0,199,0,17
	.byte	'DMA_CHANNEL72',0,200,0,17
	.byte	'DMA_CHANNEL73',0,201,0,17
	.byte	'DMA_CHANNEL74',0,202,0,17
	.byte	'DMA_CHANNEL75',0,203,0,17
	.byte	'DMA_CHANNEL76',0,204,0,17
	.byte	'DMA_CHANNEL77',0,205,0,17
	.byte	'DMA_CHANNEL78',0,206,0,17
	.byte	'DMA_CHANNEL79',0,207,0,17
	.byte	'DMA_CHANNEL80',0,208,0,17
	.byte	'DMA_CHANNEL81',0,209,0,17
	.byte	'DMA_CHANNEL82',0,210,0,17
	.byte	'DMA_CHANNEL83',0,211,0,17
	.byte	'DMA_CHANNEL84',0,212,0,17
	.byte	'DMA_CHANNEL85',0,213,0,17
	.byte	'DMA_CHANNEL86',0,214,0,17
	.byte	'DMA_CHANNEL87',0,215,0,17
	.byte	'DMA_CHANNEL88',0,216,0,17
	.byte	'DMA_CHANNEL89',0,217,0,17
	.byte	'DMA_CHANNEL90',0,218,0,17
	.byte	'DMA_CHANNEL91',0,219,0,17
	.byte	'DMA_CHANNEL92',0,220,0,17
	.byte	'DMA_CHANNEL93',0,221,0,17
	.byte	'DMA_CHANNEL94',0,222,0,17
	.byte	'DMA_CHANNEL95',0,223,0,17
	.byte	'DMA_CHANNEL96',0,224,0,17
	.byte	'DMA_CHANNEL97',0,225,0,17
	.byte	'DMA_CHANNEL98',0,226,0,17
	.byte	'DMA_CHANNEL99',0,227,0,17
	.byte	'DMA_CHANNEL100',0,228,0,17
	.byte	'DMA_CHANNEL101',0,229,0,17
	.byte	'DMA_CHANNEL102',0,230,0,17
	.byte	'DMA_CHANNEL103',0,231,0,17
	.byte	'DMA_CHANNEL104',0,232,0,17
	.byte	'DMA_CHANNEL105',0,233,0,17
	.byte	'DMA_CHANNEL106',0,234,0,17
	.byte	'DMA_CHANNEL107',0,235,0,17
	.byte	'DMA_CHANNEL108',0,236,0,17
	.byte	'DMA_CHANNEL109',0,237,0,17
	.byte	'DMA_CHANNEL110',0,238,0,17
	.byte	'DMA_CHANNEL111',0,239,0,17
	.byte	'DMA_CHANNEL112',0,240,0,17
	.byte	'DMA_CHANNEL113',0,241,0,17
	.byte	'DMA_CHANNEL114',0,242,0,17
	.byte	'DMA_CHANNEL115',0,243,0,17
	.byte	'DMA_CHANNEL116',0,244,0,17
	.byte	'DMA_CHANNEL117',0,245,0,17
	.byte	'DMA_CHANNEL118',0,246,0,17
	.byte	'DMA_CHANNEL119',0,247,0,17
	.byte	'DMA_CHANNEL120',0,248,0,17
	.byte	'DMA_CHANNEL121',0,249,0,17
	.byte	'DMA_CHANNEL122',0,250,0,17
	.byte	'DMA_CHANNEL123',0,251,0,17
	.byte	'DMA_CHANNEL124',0,252,0,17
	.byte	'DMA_CHANNEL125',0,253,0,17
	.byte	'DMA_CHANNEL126',0,254,0,17
	.byte	'DMA_CHANNEL127',0,255,0,17
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0,6
	.byte	'Dma_ChannelType',0,13,149,2,2
	.word	92811
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L10:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,36,0,3,8,11,15,62,15,0,0,4,59,0,3,8,0,0,5,15,0,73,19,0,0,6,22,0,3,8,58,15,59,15,57,15,73,19,0,0,7,21
	.byte	0,54,15,0,0,8,19,1,3,8,58,15,59,15,57,15,11,15,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,23,1
	.byte	58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,56,9,0,0,12,53,0,73,19,0,0,13,1,1,11,15,73,19,0,0
	.byte	14,33,0,47,15,0,0,15,19,1,58,15,59,15,57,15,11,15,0,0,16,4,1,58,15,59,15,57,15,11,15,0,0,17,40,0,3,8,28
	.byte	13,0,0,18,38,0,73,19,0,0,19,21,1,54,15,39,12,0,0,20,5,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L11:
	.word	.L37-.L36
.L36:
	.half	3
	.word	.L39-.L38
.L38:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcal_WdgLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcu_Dma.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Std_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_TcLib.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxGtm_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Gtm.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcu.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxDma_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxCpu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.h',0,0,0,0,0
.L39:
.L37:
	.sdecl	'.debug_info',debug,cluster('Mcu_lDmaInit')
	.sect	'.debug_info'
.L12:
	.word	295
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\mcal_src\\Mcu_Dma.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L15,.L14
	.byte	2
	.word	.L8
	.byte	3
	.byte	'Mcu_lDmaInit',0,1,93,16
	.word	.L22
	.byte	1,1,1
	.word	.L5,.L23,.L4
	.byte	4
	.word	.L5,.L23
	.byte	5
	.byte	'RetVal',0,1,95,18
	.word	.L22,.L24
	.byte	5
	.byte	'Readback',0,1,96,10
	.word	.L25,.L26
	.byte	4
	.word	.L27,.L28
	.byte	5
	.byte	'val',0,1,100,3
	.word	.L25,.L29
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Mcu_lDmaInit')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Mcu_lDmaInit')
	.sect	'.debug_line'
.L14:
	.word	.L41-.L40
.L40:
	.half	3
	.word	.L43-.L42
.L42:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcu_Dma.c',0,0,0,0,0
.L43:
	.byte	5,25,7,0,5,2
	.word	.L5
	.byte	3,222,0,1,5,3,3,3,1,9
	.half	.L27-.L5
	.byte	3,2,1,9
	.half	.L28-.L27
	.byte	3,1,1,5,24,9
	.half	.L44-.L28
	.byte	3,2,1,5,3,9
	.half	.L34-.L44
	.byte	3,6,1,5,12,7,9
	.half	.L45-.L34
	.byte	3,2,1,5,1,9
	.half	.L2-.L45
	.byte	3,3,1,3,1,1,7,9
	.half	.L16-.L2
	.byte	0,1,1
.L41:
	.sdecl	'.debug_ranges',debug,cluster('Mcu_lDmaInit')
	.sect	'.debug_ranges'
.L15:
	.word	-1,.L5,0,.L16-.L5,0,0
	.sdecl	'.debug_info',debug,cluster('Mcu_lDmaDeInit')
	.sect	'.debug_info'
.L17:
	.word	255
	.half	3
	.word	.L18
	.byte	4,1
	.byte	'..\\mcal_src\\Mcu_Dma.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L20,.L19
	.byte	2
	.word	.L8
	.byte	3
	.byte	'Mcu_lDmaDeInit',0,1,137,1,6,1,1,1
	.word	.L7,.L30,.L6
	.byte	4
	.word	.L7,.L30
	.byte	4
	.word	.L31,.L32
	.byte	5
	.byte	'val',0,1,141,1,3
	.word	.L25,.L33
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Mcu_lDmaDeInit')
	.sect	'.debug_abbrev'
.L18:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Mcu_lDmaDeInit')
	.sect	'.debug_line'
.L19:
	.word	.L47-.L46
.L46:
	.half	3
	.word	.L49-.L48
.L48:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcu_Dma.c',0,0,0,0,0
.L49:
	.byte	5,3,7,0,5,2
	.word	.L7
	.byte	3,139,1,1,9
	.half	.L31-.L7
	.byte	3,1,1,9
	.half	.L32-.L31
	.byte	3,2,1,5,1,7,9
	.half	.L21-.L32
	.byte	3,2,0,1,1
.L47:
	.sdecl	'.debug_ranges',debug,cluster('Mcu_lDmaDeInit')
	.sect	'.debug_ranges'
.L20:
	.word	-1,.L7,0,.L21-.L7,0,0
	.sdecl	'.debug_loc',debug,cluster('Mcu_lDmaDeInit')
	.sect	'.debug_loc'
.L6:
	.word	-1,.L7,0,.L30-.L7
	.half	2
	.byte	138,0
	.word	0,0
.L33:
	.word	-1,.L7,.L35-.L7,.L30-.L7
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Mcu_lDmaInit')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L23-.L5
	.half	2
	.byte	138,0
	.word	0,0
.L26:
	.word	0,0
.L24:
	.word	-1,.L5,.L27-.L5,.L23-.L5
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L29:
	.word	-1,.L5,.L28-.L5,.L34-.L5
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L50:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Mcu_lDmaInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L50,.L5,.L23-.L5
	.sdecl	'.debug_frame',debug,cluster('Mcu_lDmaDeInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L50,.L7,.L30-.L7

; ..\mcal_src\Mcu_Dma.c	   144  
; ..\mcal_src\Mcu_Dma.c	   145  }/*End of Mcu_lDmaDeInit()*/
; ..\mcal_src\Mcu_Dma.c	   146  #endif
; ..\mcal_src\Mcu_Dma.c	   147  #if (MCU_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Mcu_Dma.c	   148  /*******************************************************************************
; ..\mcal_src\Mcu_Dma.c	   149  ** Syntax : Std_ReturnType Mcu_lDmaInitCheck (void)                           **
; ..\mcal_src\Mcu_Dma.c	   150  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   151  ** Service ID:    None                                                        **
; ..\mcal_src\Mcu_Dma.c	   152  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   153  ** Sync/Async:    Synchronous                                                 **
; ..\mcal_src\Mcu_Dma.c	   154  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   155  ** Reentrancy:    Non-reentrant                                               **
; ..\mcal_src\Mcu_Dma.c	   156  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   157  ** Parameters (in):   None                                                    **
; ..\mcal_src\Mcu_Dma.c	   158  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   159  ** Parameters (out):  None                                                    **
; ..\mcal_src\Mcu_Dma.c	   160  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   161  ** Return value    :    E_OK - if initialization comparison is success        **
; ..\mcal_src\Mcu_Dma.c	   162  **                      E_NOT_OK - if initialization comparison fails         **
; ..\mcal_src\Mcu_Dma.c	   163  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   164  ** Description :  This service shall verify the DMA specific                  **
; ..\mcal_src\Mcu_Dma.c	   165  **                initialization done by MCU.                                 **
; ..\mcal_src\Mcu_Dma.c	   166  **                                                                            **
; ..\mcal_src\Mcu_Dma.c	   167  *******************************************************************************/
; ..\mcal_src\Mcu_Dma.c	   168  Std_ReturnType Mcu_lDmaInitCheck(void)
; ..\mcal_src\Mcu_Dma.c	   169  {
; ..\mcal_src\Mcu_Dma.c	   170    Std_ReturnType ErrorFlag;
; ..\mcal_src\Mcu_Dma.c	   171  
; ..\mcal_src\Mcu_Dma.c	   172    ErrorFlag = E_OK;
; ..\mcal_src\Mcu_Dma.c	   173  
; ..\mcal_src\Mcu_Dma.c	   174    if (((MCU_SFR_INIT_USER_MODE_READ32(MODULE_DMA.CLC.U) & \ 
; ..\mcal_src\Mcu_Dma.c	   175                     MCU_DMA_CLC_DISS_CLEARMASK)>>MCU_DMA_CLC_DISS_BITPOS)  != 0U)
; ..\mcal_src\Mcu_Dma.c	   176    {
; ..\mcal_src\Mcu_Dma.c	   177      ErrorFlag = E_NOT_OK;
; ..\mcal_src\Mcu_Dma.c	   178    }
; ..\mcal_src\Mcu_Dma.c	   179  
; ..\mcal_src\Mcu_Dma.c	   180    return ErrorFlag;
; ..\mcal_src\Mcu_Dma.c	   181  }/*End of Mcu_lDmaInitCheck()*/
; ..\mcal_src\Mcu_Dma.c	   182  
; ..\mcal_src\Mcu_Dma.c	   183  #endif /*End Of MCU_SAFETY_ENABLE*/
; ..\mcal_src\Mcu_Dma.c	   184  
; ..\mcal_src\Mcu_Dma.c	   185  #define MCU_STOP_SEC_CODE
; ..\mcal_src\Mcu_Dma.c	   186  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Mcu_Dma.c	   187   is allowed only for MemMap.h*/
; ..\mcal_src\Mcu_Dma.c	   187  #include "MemMap.h"

	; Module end
