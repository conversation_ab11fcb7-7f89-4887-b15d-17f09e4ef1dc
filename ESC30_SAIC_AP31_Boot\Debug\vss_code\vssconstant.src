	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc26556a -c99 --dep-file=vss_code\\.vssconstant.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=vss_code\\vssconstant.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o vss_code\\vssconstant.src ..\\vss_code\\vssconstant.c"
	.compiler_name		"ctc"
	.name	"vssconstant"

	
$TC16X
	
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vss_ZERO')
	.sect	'.rodata.vss_api_code'
	.global	vss_ZERO
	.align	4
vss_ZERO:	.type	object
	.size	vss_ZERO,32
	.space	32
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('VSS_VERSION')
	.sect	'.rodata.vss_api_code'
	.global	VSS_VERSION
	.align	4
VSS_VERSION:	.type	object
	.size	VSS_VERSION,16
	.byte	86,83,83,95,77,67,85,95
	.byte	86,49,46,51
	.space	4
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('aes_sbox')
	.sect	'.rodata.vss_api_code'
	.global	aes_sbox
	.align	4
aes_sbox:	.type	object
	.size	aes_sbox,256
	.byte	99,124,119,123,242,107,111,197
	.byte	48,1,103,43,254,215,171,118
	.byte	202,130,201,125,250,89,71,240
	.byte	173,212,162,175,156,164,114,192
	.byte	183,253,147,38,54,63,247,204
	.byte	52,165,229,241,113,216,49,21
	.byte	4,199,35,195,24,150,5,154
	.byte	7,18,128,226,235,39,178,117
	.byte	9,131,44,26,27,110,90,160
	.byte	82,59,214,179,41,227,47,132
	.byte	83,209
	.space	1
	.byte	237,32,252,177,91,106,203,190
	.byte	57,74,76,88,207,208,239,170
	.byte	251,67,77,51,133,69,249,2
	.byte	127,80,60,159,168,81,163,64
	.byte	143,146,157,56,245,188,182,218
	.byte	33,16,255,243,210,205,12,19
	.byte	236,95,151,68,23,196,167,126
	.byte	61,100,93,25,115,96,129,79
	.byte	220,34,42,144,136,70,238,184
	.byte	20,222,94,11,219,224,50,58
	.byte	10,73,6,36,92,194,211,172
	.byte	98,145,149,228,121,231,200,55
	.byte	109,141,213,78,169,108,86,244
	.byte	234,101,122,174,8,186,120,37
	.byte	46,28,166,180,198,232,221,116
	.byte	31,75,189,139,138,112,62,181
	.byte	102,72,3,246,14,97,53,87
	.byte	185,134,193,29,158,225,248,152
	.byte	17,105,217,142,148,155,30,135
	.byte	233,206,85,40,223,140,161,137
	.byte	13,191,230,66,104,65,153,45
	.byte	15,176,84,187
	.byte	22
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('aes_contrary_sbox')
	.sect	'.rodata.vss_api_code'
	.global	aes_contrary_sbox
	.align	4
aes_contrary_sbox:	.type	object
	.size	aes_contrary_sbox,256
	.byte	82,9,106,213,48,54,165,56
	.byte	191,64,163,158,129,243,215,251
	.byte	124,227,57,130,155,47,255,135
	.byte	52,142,67,68,196,222,233,203
	.byte	84,123,148,50,166,194,35,61
	.byte	238,76,149,11,66,250,195,78
	.byte	8,46,161,102,40,217,36,178
	.byte	118,91,162,73,109,139,209,37
	.byte	114,248,246,100,134,104,152,22
	.byte	212,164,92,204,93,101,182,146
	.byte	108,112,72,80,253,237,185,218
	.byte	94,21,70,87,167,141,157,132
	.byte	144,216,171
	.space	1
	.byte	140,188,211,10,247,228,88,5
	.byte	184,179,69,6,208,44,30,143
	.byte	202,63,15,2,193,175,189,3
	.byte	1,19,138,107,58,145,17,65
	.byte	79,103,220,234,151,242,207,206
	.byte	240,180,230,115,150,172,116,34
	.byte	231,173,53,133,226,249,55,232
	.byte	28,117,223,110,71,241,26,113
	.byte	29,41,197,137,111,183,98,14
	.byte	170,24,190,27,252,86,62,75
	.byte	198,210,121,32,154,219,192,254
	.byte	120,205,90,244,31,221,168,51
	.byte	136,7,199,49,177,18,16,89
	.byte	39,128,236,95,96,81,127,169
	.byte	25,181,74,13,45,229,122,159
	.byte	147,201,156,239,160,224,59,77
	.byte	174,42,245,176,200,235,187,60
	.byte	131,83,153,97,23,43,4,126
	.byte	186,119,214,38,225,105,20,99
	.byte	85,33,12,125
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('aes_Rcon')
	.sect	'.rodata.vss_api_code'
	.global	aes_Rcon
	.align	4
aes_Rcon:	.type	object
	.size	aes_Rcon,10
	.byte	1,2,4,8,16,32,64,128
	.byte	27,54
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('cmac_Rb')
	.sect	'.rodata.vss_api_code'
	.global	cmac_Rb
	.align	4
cmac_Rb:	.type	object
	.size	cmac_Rb,16
	.space	15
	.byte	135
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('ecc_precompute_data_table_NIST256')
	.sect	'.rodata.vss_api_code'
	.global	ecc_precompute_data_table_NIST256
	.align	4
ecc_precompute_data_table_NIST256:	.type	object
	.size	ecc_precompute_data_table_NIST256,1536
	.word	-661077354,-190760635,770388896,1996717441,1671708914,-121837851,-517193145,1796723186
	.word	935285237,-877248408,1798397646,734933847,2081398294,-1897403574,-31817829,1340293858
	.word	1
	.space	28
	.word	79349872,-137213001,975423147,1496975776,-112419555,-540269736,695987458,1023020935
	.word	1089628480,-828857589,38316207,1645353997,-2096517221,-1487361408,1891504838,-454855020
	.word	1
	.space	28
	.word	-2031125123,-583539713,143361755,-153650137,625759377,687774287,1844510046,-697760969
	.word	-1378167402,-1611642478,-1639746567,-204363857,-553172633,-1589734024,-422494942,551724348
	.word	1
	.space	28
	.word	1381853887,-1363159243,-1765891494,-1023215372,1414078628,-612191254,48945966,1646032337
	.word	-1628142096,-1992696756,1472490851,576521176,1378713456,1339738479,-1258658659,-674978562
	.word	1
	.space	28
	.word	-1061603542,-1949935190,-304523201,-144336552,-1943634552,-1626998454,-912624739,14436071
	.word	-548024078,1257572864,-2125021334,-1242882003,1378402577,1087497131,1165571108,51553623
	.word	1
	.space	28
	.word	2126041070,-1919511817,227279165,98824739,-2065486247,1983150674,363897317,1012130448
	.word	-85485356,1401650597,13776273,463412177,-1710724801,296349810,336527027,-754025254
	.word	1
	.space	28
	.word	-118950269,1845294983,1065336464,332868474,770847439,-45978220,1608751789,-310068414
	.word	1932105212,37644193,894709987,1742539406,-1751001341,217594721,1480340466,-1557973931
	.word	1
	.space	28
	.word	1595301273,-826557317,-1974538655,1355567553,458034463,-1289936045,1714957018,-1780382638
	.word	176397147,-1397928063,1094909711,-1599952530,525312303,1051361929,1796192499,1758891151
	.word	1
	.space	28
	.word	1371255793,-1827560855,-1928376475,-401660227,1810680761,-1656896716,270425572,1424114901
	.word	-240017880,-1280488338,1489167287,1127545792,903846949,431833351,-334467402,-692327410
	.word	1
	.space	28
	.word	1072680904,-393733501,-1179309813,-1364997664,1998472840,280198861,-724557386,-301459805
	.word	-1280783711,-1902564929,-1056574767,1217486287,-615972449,979361666,-1383069983,-995870493
	.word	1
	.space	28
	.word	-398798135,1567474947,-1362086466,-895518179,-875422157,1428654252,-526599696,-2036736719
	.word	849082721,945308054,-1205183751,1286737558,-953386815,605657352,-383231561,384193668
	.word	1
	.space	28
	.word	-617596707,-1576391568,-375563140,1594574549,1264585603,-910590665,1039344734,409763705
	.word	1327293823,720839776,-1847404307,-1840674433,-1926322281,-2126924950,-1699378925,-1673359944
	.word	1
	.space	28
	.word	1273382285,522080063,1499358535,1601358882,656053233,1539650590,1487270499,-970058404
	.word	1488617460,1601143898,2117510829,1742862300,-290829052,-751143337,405175886,-6347998
	.word	1
	.space	28
	.word	-949728227,-482409899,-1148189992,1076948163,-194744417,-1538758378,-1784920838,1583840524
	.word	-661941948,-488122416,1648949535,1569957999,43596522,-482538539,822137630,1151274214
	.word	1
	.space	28
	.word	-39144874,1390351752,824653132,1757465924,-1517160365,713331825,-1815032942,-455440151
	.word	372885028,1830724445,641140666,1627269686,-789332911,-1745071906,268840576,-134591211
	.word	1
	.space	28
	.word	1533633831,779461222,1509955228,438644912,1116142402,1992606080,-1904009893,-2107702892
	.word	-277858503,-604987105,-832892822,275749828,605098320,1635339343,-1133364713,1129870078
	.word	1
	.space	28
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('ecc_precompute_data_table_SM2')
	.sect	'.rodata.vss_api_code'
	.global	ecc_precompute_data_table_SM2
	.align	4
ecc_precompute_data_table_SM2:	.type	object
	.size	ecc_precompute_data_table_SM2,1536
	.word	860648647,1901741449,-228193311,-1880945729,1782172052,1603863622,521765145,851750444
	.word	557445280,48181989,-970307776,-794196100,1802051923,1505611491,-185174116,-1137232222
	.word	1
	.space	28
	.word	-1784528163,535313386,-1706562322,-1938351412,1781206401,1649501630,-1473639338,-683921284
	.word	-34249254,946376082,-2061218178,316284382,1604144213,-1230925369,1286719321,233291563
	.word	1
	.space	28
	.word	1157632041,-1369836716,1212136273,-1228938602,424845020,1100290958,514973311,-537794524
	.word	654378293,-2056141176,-560030557,-1656548634,2086508659,88246727,335412642,1039869948
	.word	1
	.space	28
	.word	-1884867982,-1350203441,104521280,-1460877018,-1219631626,-1764137263,1308498476,-986412236
	.word	1751965974,-1331491365,1802933013,737693490,-1246329984,-386480350,788378828,-1432366090
	.word	1
	.space	28
	.word	46663206,-1456856936,-1183492373,-1507730357,927140270,202601537,-327718628,1124255212
	.word	1115085725,559571018,906681787,-1296550395,1106286539,-1208692215,-166154334,776053721
	.word	1
	.space	28
	.word	636557193,653964862,948280770,-1646205149,-109327665,1598926034,-1755910222,177906785
	.word	703661678,-926030231,122361646,1420557616,-331115256,-2117122303,743082650,801744112
	.word	1
	.space	28
	.word	1794470364,472057009,547217385,1866312730,-138061503,128099823,-84750773,1437013102
	.word	2101274151,1896462943,317227157,-925125115,2129890546,1672636426,-1638723714,1360078869
	.word	1
	.space	28
	.word	875488463,-1220391254,2120501967,-1287464552,1567093015,1556558292,-1082090401,-708507496
	.word	826948630,-238347823,1689275848,1239631582,2017276092,1134613462,-1946146635,-1705487791
	.word	1
	.space	28
	.word	154658007,638943492,1588477382,-1513945416,-635778574,-1862211837,1387150373,-290406860
	.word	-855409454,-1356322869,-1307399282,1135463424,-394239358,-2055370501,1572667343,-1129199492
	.word	1
	.space	28
	.word	-371405869,-1080161141,1315129158,1584622629,-1401457942,1275300874,-1270091922,-17036842
	.word	884871368,1359314883,-1995085456,1962594109,892275517,-35172806,21472886,1133061954
	.word	1
	.space	28
	.word	402326848,-1286804935,836827960,-149858178,360211180,455231152,214517157,322958484
	.word	1379948695,-1481268842,1878374672,1909808639,-258600473,-199718344,1671354210,-1444212734
	.word	1
	.space	28
	.word	1525531925,-1688115773,-1382270310,1959876958,-654011727,-1057662940,-777064736,-1213724611
	.word	442373753,1923434279,1023242662,-974292341,726247088,-558485307,2107185455,959437555
	.word	1
	.space	28
	.word	144766287,2112887554,1909657825,223435720,1000949219,1368599632,-1251152102,1651441813
	.word	809413140,-1721824272,-107240296,548177241,2121167296,1731319700,340246245,2110744595
	.word	1
	.space	28
	.word	408810985,1570260458,-869793575,1576782922,-936024407,151870180,577838831,959481398
	.word	1811488866,-44895413,-1985442853,1054939488,251595946,1082357961,-129038579,431815571
	.word	1
	.space	28
	.word	1467962148,20673155,-1153608743,-2045725200,1755448680,652278568,1668561598,-2121592734
	.word	1679053734,1195676751,-625666298,22009811,-430946700,-981786361,1832378610,-243317115
	.word	1
	.space	28
	.word	-551750955,1081039684,-2098837816,-1907684286,-277145876,-1993203227,-1367938753,673417662
	.word	487367686,-870056516,182432936,482863413,1007282889,-514907007,1896748215,-922538262
	.word	1
	.space	28
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('ecc_precompute_point_table_SM2')
	.sect	'.rodata.vss_api_code'
	.global	ecc_precompute_point_table_SM2
	.align	4
ecc_precompute_point_table_SM2:	.type	object
	.size	ecc_precompute_point_table_SM2,192
	.word	ecc_precompute_data_table_SM2,ecc_precompute_data_table_SM2+32,ecc_precompute_data_table_SM2+64,ecc_precompute_data_table_SM2+96,ecc_precompute_data_table_SM2+128,ecc_precompute_data_table_SM2+160,ecc_precompute_data_table_SM2+192,ecc_precompute_data_table_SM2+224
	.word	ecc_precompute_data_table_SM2+256,ecc_precompute_data_table_SM2+288,ecc_precompute_data_table_SM2+320,ecc_precompute_data_table_SM2+352,ecc_precompute_data_table_SM2+384,ecc_precompute_data_table_SM2+416,ecc_precompute_data_table_SM2+448,ecc_precompute_data_table_SM2+480
	.word	ecc_precompute_data_table_SM2+512,ecc_precompute_data_table_SM2+544,ecc_precompute_data_table_SM2+576,ecc_precompute_data_table_SM2+608,ecc_precompute_data_table_SM2+640,ecc_precompute_data_table_SM2+672,ecc_precompute_data_table_SM2+704,ecc_precompute_data_table_SM2+736
	.word	ecc_precompute_data_table_SM2+768,ecc_precompute_data_table_SM2+800,ecc_precompute_data_table_SM2+832,ecc_precompute_data_table_SM2+864,ecc_precompute_data_table_SM2+896,ecc_precompute_data_table_SM2+928,ecc_precompute_data_table_SM2+960,ecc_precompute_data_table_SM2+992
	.word	ecc_precompute_data_table_SM2+1024,ecc_precompute_data_table_SM2+1056,ecc_precompute_data_table_SM2+1088,ecc_precompute_data_table_SM2+1120,ecc_precompute_data_table_SM2+1152,ecc_precompute_data_table_SM2+1184,ecc_precompute_data_table_SM2+1216,ecc_precompute_data_table_SM2+1248
	.word	ecc_precompute_data_table_SM2+1280,ecc_precompute_data_table_SM2+1312,ecc_precompute_data_table_SM2+1344,ecc_precompute_data_table_SM2+1376,ecc_precompute_data_table_SM2+1408,ecc_precompute_data_table_SM2+1440,ecc_precompute_data_table_SM2+1472,ecc_precompute_data_table_SM2+1504
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('ecc_precompute_point_table_NIST256')
	.sect	'.rodata.vss_api_code'
	.global	ecc_precompute_point_table_NIST256
	.align	4
ecc_precompute_point_table_NIST256:	.type	object
	.size	ecc_precompute_point_table_NIST256,192
	.word	ecc_precompute_data_table_NIST256,ecc_precompute_data_table_NIST256+32,ecc_precompute_data_table_NIST256+64,ecc_precompute_data_table_NIST256+96,ecc_precompute_data_table_NIST256+128,ecc_precompute_data_table_NIST256+160,ecc_precompute_data_table_NIST256+192,ecc_precompute_data_table_NIST256+224
	.word	ecc_precompute_data_table_NIST256+256,ecc_precompute_data_table_NIST256+288,ecc_precompute_data_table_NIST256+320,ecc_precompute_data_table_NIST256+352,ecc_precompute_data_table_NIST256+384,ecc_precompute_data_table_NIST256+416,ecc_precompute_data_table_NIST256+448,ecc_precompute_data_table_NIST256+480
	.word	ecc_precompute_data_table_NIST256+512,ecc_precompute_data_table_NIST256+544,ecc_precompute_data_table_NIST256+576,ecc_precompute_data_table_NIST256+608,ecc_precompute_data_table_NIST256+640,ecc_precompute_data_table_NIST256+672,ecc_precompute_data_table_NIST256+704,ecc_precompute_data_table_NIST256+736
	.word	ecc_precompute_data_table_NIST256+768,ecc_precompute_data_table_NIST256+800,ecc_precompute_data_table_NIST256+832,ecc_precompute_data_table_NIST256+864,ecc_precompute_data_table_NIST256+896,ecc_precompute_data_table_NIST256+928,ecc_precompute_data_table_NIST256+960,ecc_precompute_data_table_NIST256+992
	.word	ecc_precompute_data_table_NIST256+1024,ecc_precompute_data_table_NIST256+1056,ecc_precompute_data_table_NIST256+1088,ecc_precompute_data_table_NIST256+1120,ecc_precompute_data_table_NIST256+1152,ecc_precompute_data_table_NIST256+1184,ecc_precompute_data_table_NIST256+1216,ecc_precompute_data_table_NIST256+1248
	.word	ecc_precompute_data_table_NIST256+1280,ecc_precompute_data_table_NIST256+1312,ecc_precompute_data_table_NIST256+1344,ecc_precompute_data_table_NIST256+1376,ecc_precompute_data_table_NIST256+1408,ecc_precompute_data_table_NIST256+1440,ecc_precompute_data_table_NIST256+1472,ecc_precompute_data_table_NIST256+1504
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('KZUC_S0')
	.sect	'.rodata.vss_api_code'
	.global	KZUC_S0
	.align	4
KZUC_S0:	.type	object
	.size	KZUC_S0,256
	.byte	62,114,91,71
	.byte	202,224
	.space	1
	.byte	51,4,209,84,152,9,185,109
	.byte	203,123,27,249,50,175,157,106
	.byte	165,184,45,252,29,8,83,3
	.byte	144,77,78,132,153,228,206,217
	.byte	145,221,182,133,72,139,41,110
	.byte	172,205,193,248,30,115,67,105
	.byte	198,181,189,253,57,99,32,212
	.byte	56,118,125,178,167,207,237,87
	.byte	197,243,44,187,20,33,6,85
	.byte	155,227,239,94,49,79,127,90
	.byte	164,13,130,81,73,95,186,88
	.byte	28,74,22,213,23,168,146,36
	.byte	31,140,255,216,174,46,1,211
	.byte	173,59,75,218,70,235,201,222
	.byte	154,143,135,215,58,128,111,47
	.byte	200,177,180,55,247,10,34,19
	.byte	40,124,204,60,137,199,195,150
	.byte	86,7,191,126,240,11,43,151
	.byte	82,53,65,121,97,166,76,16
	.byte	254,188,38,149,136,138,176,163
	.byte	251,192,24,148,242,225,229,233
	.byte	93,208,220,17,102,100,92,236
	.byte	89,66,117,18,245,116,156,170
	.byte	35,14,134,171,190,42,2,231
	.byte	103,230,68,162,108,194,147,159
	.byte	241,246,250,54,210,80,104,158
	.byte	98,113,21,61,214,64,196,226
	.byte	15,142,131,119,107,37,5,63
	.byte	12,48,234,112,183,161,232,169
	.byte	101,141,39,26,219,129,179,160
	.byte	244,69,122,25,223,238,120,52
	.byte	96
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('KZUC_S1')
	.sect	'.rodata.vss_api_code'
	.global	KZUC_S1
	.align	4
KZUC_S1:	.type	object
	.size	KZUC_S1,256
	.byte	85,194,99,113,59,200,71,134
	.byte	159,60,218,91,41,170,253,119
	.byte	140,197,148,12
	.byte	166,26,19
	.space	1
	.byte	227,168,22,114,64,249,248,66
	.byte	68,38,104,150,129,217,69,62
	.byte	16,118,198,167,139,57,67,225
	.byte	58,181,86,42,192,109,179,5
	.byte	34,102,191,220,11,250,98,72
	.byte	221,32,17,6,54,201,193,207
	.byte	246,39,82,187,105,245,212,135
	.byte	127,132,76,210,156,87,164,188
	.byte	79,154,223,254,214,141,122,235
	.byte	43,83,216,92,161,20,23,251
	.byte	35,213,125,48,103,115,8,9
	.byte	238,183,112,63,97,178,25,142
	.byte	78,229,75,147,143,93,219,169
	.byte	173,241,174,46,203,13,252,244
	.byte	45,70,110,29,151,232,209,233
	.byte	77,55,165,117,94,131,158,171
	.byte	130,157,185,28,224,205,73,137
	.byte	1,182,189,88,36,162,95,56
	.byte	120,153,21,144,80,184,149,228
	.byte	208,145,199,206,237,15,180,111
	.byte	160,204,240,2,74,121,195,222
	.byte	163,239,234,81,230,107,24,236
	.byte	27,44,128,247,116,231,255,33
	.byte	90,106,84,30,65,49,146,53
	.byte	196,51,7,10,186,126,14,52
	.byte	136,177,152,124,243,61,96,108
	.byte	123,202,211,31,50,101,4,40
	.byte	100,190,133,155,47,89,138,215
	.byte	176,37,172,175,18,3,226,242
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('KZUC_d')
	.sect	'.rodata.vss_api_code'
	.global	KZUC_d
	.align	4
KZUC_d:	.type	object
	.size	KZUC_d,64
	.word	17623,9916,25195,4958,22409,13794,28981,2479
	.word	19832,12051,27588,6897,24102,15437,30874,18348
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_g_ecdsa_para_a')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_g_ecdsa_para_a
	.align	4
mizar_ecc_g_ecdsa_para_a:	.type	object
	.size	mizar_ecc_g_ecdsa_para_a,32
	.word	-1,16777216
	.space	12
	.word	-1,-1,-50331649
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_g_ecdsa_para_b')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_g_ecdsa_para_b
	.align	4
mizar_ecc_g_ecdsa_para_b:	.type	object
	.size	mizar_ecc_g_ecdsa_para_b,32
	.word	-667564454,-409781590,1438510003,-1132029834,-1341776539,-156216372,1044172347,1264636455
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_g_ecdsa_para_p')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_g_ecdsa_para_p
	.align	4
mizar_ecc_g_ecdsa_para_p:	.type	object
	.size	mizar_ecc_g_ecdsa_para_p,32
	.word	-1,16777216
	.space	12
	.word	-1,-1,-1
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_g_ecdsa_para_n')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_g_ecdsa_para_n
	.align	4
mizar_ecc_g_ecdsa_para_n:	.type	object
	.size	mizar_ecc_g_ecdsa_para_n,32
	.word	-1
	.space	4
	.word	-1,-1,-1376065860,-2070014041
	.word	-1026901517,1361404924
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_g_ecdsa_para_G')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_g_ecdsa_para_G
	.align	4
mizar_ecc_g_ecdsa_para_G:	.type	object
	.size	mizar_ecc_g_ecdsa_para_G,64
	.word	-221178005,1195519201,-437863176,-230644637,-2122513545,-1607210195,1161404916,-1765631784
	.word	-498932913,-1686168834,1256974222,379457404,1463012907,-832687765,1749071563,-179192009
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_vss_ecc_rand')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_vss_ecc_rand
	.align	4
mizar_ecc_vss_ecc_rand:	.type	object
	.size	mizar_ecc_vss_ecc_rand,32
	.byte	6,193,235,42,207,34,18,42
	.byte	84,180,125,106,6,255,98,204
	.byte	138,7,125,178,27,165,60,103
	.byte	192,122,74,68
	.byte	110,123,245
	.space	1
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_rand1')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_rand1
	.align	4
mizar_ecc_rand1:	.type	object
	.size	mizar_ecc_rand1,32
	.byte	108,227,242,187,46,197,24,107
	.byte	102,59,3,105,49,185,80,222
	.byte	168,43,65,16,86,180,159,161
	.byte	8,48,111,126,67,121,65,25
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_k')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_k
	.align	4
mizar_ecc_k:	.type	object
	.size	mizar_ecc_k,32
	.byte	138,7,125,182,27,165,60,103
	.byte	192,122,74,68,110,123,149,7
	.byte	113,193,235,74,207,34,18,42
	.byte	84,180,125,106,6,255,98,204
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('mizar_ecc_g_ecdsa_para_h')
	.sect	'.rodata.vss_api_code'
	.global	mizar_ecc_g_ecdsa_para_h
	.align	4
mizar_ecc_g_ecdsa_para_h:	.type	object
	.size	mizar_ecc_g_ecdsa_para_h,4
	.word	1
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sha256_k')
	.sect	'.rodata.vss_api_code'
	.global	sha256_k
	.align	4
sha256_k:	.type	object
	.size	sha256_k,256
	.word	1116352408,1899447441,-1245643825,-373957723,961987163,1508970993,-1841331548,-1424204075
	.word	-670586216,310598401,607225278,1426881987,1925078388,-2132889090,-1680079193,-1046744716
	.word	-459576895,-272742522,264347078,604807628,770255983,1249150122,1555081692,1996064986
	.word	-1740746414,-1473132947,-1341970488,-1084653625,-958395405,-710438585,113926993,338241895
	.word	666307205,773529912,1294757372,1396182291,1695183700,1986661051,-2117940946,-1838011259
	.word	-1564481375,-1474664885,-1035236496,-949202525,-778901479,-694614492,-200395387,275423344
	.word	430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779
	.word	1955562222,2024104815,-2067236844,-1933114872,-1866530822,-1538233109,-1090935817,-965641998
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm2_G_SM2_Gx')
	.sect	'.rodata.vss_api_code'
	.global	sm2_G_SM2_Gx
	.align	4
sm2_G_SM2_Gx:	.type	object
	.size	sm2_G_SM2_Gx,32
	.word	749650994,427890975,1174706527,-1798751894,-1089739889,-519346446,-1991943567,-948679629
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm2_G_SM2_Gy')
	.sect	'.rodata.vss_api_code'
	.global	sm2_G_SM2_Gy
	.align	4
sm2_G_SM2_Gy:	.type	object
	.size	sm2_G_SM2_Gy,32
	.word	-1573505092,-1669859596,-472990375,1394698603,2089265616,1078405830,-449650942,-1594869471
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm2_G_SM2_a')
	.sect	'.rodata.vss_api_code'
	.global	sm2_G_SM2_a
	.align	4
sm2_G_SM2_a:	.type	object
	.size	sm2_G_SM2_a,32
	.word	-16777217,-1,-1,-1
	.word	-1
	.space	4
	.word	-1,-50331649
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm2_G_SM2_b')
	.sect	'.rodata.vss_api_code'
	.global	sm2_G_SM2_b
	.align	4
sm2_G_SM2_b:	.type	object
	.size	sm2_G_SM2_b,32
	.word	-1627723480,878616477,1268669005,-1492556337,-175532045,-1836078315,1102953693,-1827761075
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm2_G_SM2_p')
	.sect	'.rodata.vss_api_code'
	.global	sm2_G_SM2_p
	.align	4
sm2_G_SM2_p:	.type	object
	.size	sm2_G_SM2_p,32
	.word	-16777217,-1,-1,-1
	.word	-1
	.space	4
	.word	-1,-1
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm2_G_SM2_n')
	.sect	'.rodata.vss_api_code'
	.global	sm2_G_SM2_n
	.align	4
sm2_G_SM2_n:	.type	object
	.size	sm2_G_SM2_n,32
	.word	-16777217,-1,-1,-1,1809777522,721798689,167033683,591516985
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm2_pub_enc_rand')
	.sect	'.rodata.vss_api_code'
	.global	sm2_pub_enc_rand
	.align	4
sm2_pub_enc_rand:	.type	object
	.size	sm2_pub_enc_rand,32
	.byte	138,7,125,182,27,165,60,103
	.byte	192,122,74,68,110,123,149,7
	.byte	113,193,235,74,207,34,18,42
	.byte	84,180,125,106,6,255,98,204
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm2_pri_dec_rand')
	.sect	'.rodata.vss_api_code'
	.global	sm2_pri_dec_rand
	.align	4
sm2_pri_dec_rand:	.type	object
	.size	sm2_pri_dec_rand,32
	.word	720093553,705831631,1786623060,-865927418,-1300428918,1732027675,1145731776,16087918
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('SM3_IV')
	.sect	'.rodata.vss_api_code'
	.global	SM3_IV
	.align	4
SM3_IV:	.type	object
	.size	SM3_IV,32
	.word	1937774191,1226093241,388252375,-628488704,-1452330820,372324522,-477237683,-1325724082
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm4_SboxTable')
	.sect	'.rodata.vss_api_code'
	.global	sm4_SboxTable
	.align	4
sm4_SboxTable:	.type	object
	.size	sm4_SboxTable,256
	.byte	214,144,233,254,204,225,61,183
	.byte	22,182,20,194,40,251,44,5
	.byte	43,103,154,118,42,190,4,195
	.byte	170,68,19,38,73,134,6,153
	.byte	156,66,80,244,145,239,152,122
	.byte	51,84,11,67,237,207,172,98
	.byte	228,179,28,169,201,8,232,149
	.byte	128,223,148,250,117,143,63,166
	.byte	71,7,167,252,243,115,23,186
	.byte	131,89,60,25,230,133,79,168
	.byte	104,107,129,178,113,100,218,139
	.byte	248,235,15,75,112,86,157,53
	.byte	30,36,14,94,99,88,209,162
	.byte	37,34,124,59,1,33,120,135
	.byte	212
	.space	1
	.byte	70,87,159,211,39,82,76,54
	.byte	2,231,160,196,200,158,234,191
	.byte	138,210,64,199,56,181,163,247
	.byte	242,206,249,97,21,161,224,174
	.byte	93,164,155,52,26,85,173,147
	.byte	50,48,245,140,177,227,29,246
	.byte	226,46,130,102,202,96,192,41
	.byte	35,171,13,83,78,111,213,219
	.byte	55,69,222,253,142,47,3,255
	.byte	106,114,109,108,91,81,141,27
	.byte	175,146,187,221,188,127,17,217
	.byte	92,65,31,16,90,216,10,193
	.byte	49,136,165,205,123,189,45,116
	.byte	208,18,184,229,180,176,137,105
	.byte	151,74,12,150,119,126,101,185
	.byte	241,9,197,110,198,132,24,240
	.byte	125,236,58,220,77,32,121,238
	.byte	95,62,215,203
	.byte	57,72
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm4_FK')
	.sect	'.rodata.vss_api_code'
	.global	sm4_FK
	.align	4
sm4_FK:	.type	object
	.size	sm4_FK,16
	.word	-1548633402,1453994832,1736282519,-1301273892
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('sm4_CK')
	.sect	'.rodata.vss_api_code'
	.global	sm4_CK
	.align	4
sm4_CK:	.type	object
	.size	sm4_CK,128
	.word	462357,472066609,943670861,1415275113,1886879365,-1936483679,-1464879427,-993275175
	.word	-521670923,-66909679,404694573,876298825,1347903077,1819507329,-2003855715,-1532251463
	.word	-1060647211,-589042959,-117504499,337322537,808926789,1280531041,1752135293,-2071227751
	.word	-1599623499,-1128019247,-656414995,-184876535,269950501,741554753,1213159005,1684763257
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vssapi_sm2_rand')
	.sect	'.rodata.vss_api_code'
	.global	vssapi_sm2_rand
	.align	4
vssapi_sm2_rand:	.type	object
	.size	vssapi_sm2_rand,32
	.byte	6,193,235,42,207,34,18,42
	.byte	84,180,125,106,6,255,98,204
	.byte	138,7,125,178,27,165,60,103
	.byte	192,122,74,68
	.byte	110,123,245
	.space	1
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vssapi_sm2_rand1')
	.sect	'.rodata.vss_api_code'
	.global	vssapi_sm2_rand1
	.align	4
vssapi_sm2_rand1:	.type	object
	.size	vssapi_sm2_rand1,32
	.byte	108,227,242,187,46,197,24,107
	.byte	102,59,3,105,49,185,80,222
	.byte	168,43,65,16,86,180,159,161
	.byte	8,48,111,126,67,121,65,25
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vssapi_SESS_KEY')
	.sect	'.rodata.vss_api_code'
	.global	vssapi_SESS_KEY
	.align	4
vssapi_SESS_KEY:	.type	object
	.size	vssapi_SESS_KEY,16
	.byte	32,33,34,35,36,37,38,39
	.byte	40,41,42,43,44,45,46,47
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vssapi_KEY')
	.sect	'.rodata.vss_api_code'
	.global	vssapi_KEY
	.align	4
vssapi_KEY:	.type	object
	.size	vssapi_KEY,4
	.byte	75,69,89
	.space	1
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vssapi_SERVER')
	.sect	'.rodata.vss_api_code'
	.global	vssapi_SERVER
	.align	4
vssapi_SERVER:	.type	object
	.size	vssapi_SERVER,7
	.byte	83,69,82,86
	.byte	69,82
	.space	1
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vssapi_CLIENT')
	.sect	'.rodata.vss_api_code'
	.global	vssapi_CLIENT
	.align	4
vssapi_CLIENT:	.type	object
	.size	vssapi_CLIENT,7
	.byte	67,76,73,69
	.byte	78,84
	.space	1
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vsskeym_KEK')
	.sect	'.rodata.vss_api_code'
	.global	vsskeym_KEK
	.align	4
vsskeym_KEK:	.type	object
	.size	vsskeym_KEK,16
	.byte	106,67,84,67,65,78,68,75
	.byte	79,65,76,73,78,70,73,47
	.sdecl	'.rodata.vss_api_code',data,rom,cluster('vsskeym_SECOC')
	.sect	'.rodata.vss_api_code'
	.global	vsskeym_SECOC
	.align	4
vsskeym_SECOC:	.type	object
	.size	vsskeym_SECOC,16
	.byte	1,2,3,4,5,6,7,8
	.byte	9,10,11,12,13,14,15,16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1074
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	180
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	186
	.byte	5,1,3
	.word	210
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	212
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'vss_uint8',0,2,8,24
	.word	235
	.byte	6
	.byte	'char',0,1,6,4
	.byte	'vss_char8',0,2,10,17
	.word	270
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'vss_uint32',0,2,13,24
	.word	296
	.byte	6
	.byte	'unsigned long long int',0,8,7,4
	.byte	'vss_uint64',0,2,17,28
	.word	336
	.byte	4
	.byte	'vss_ulong',0,2,18,24
	.word	296
	.byte	4
	.byte	'BYTE',0,2,21,22
	.word	235
	.byte	4
	.byte	'WORD',0,2,22,22
	.word	296
	.byte	4
	.byte	'SM3_WORD_T',0,2,25,22
	.word	296
	.byte	7,2,27,9,168,1,8
	.byte	'm_size',0,4
	.word	296
	.byte	2,35,0,9,128,1
	.word	235
	.byte	10,127,0,8
	.byte	'remain',0,128,1
	.word	466
	.byte	2,35,4,8
	.byte	'r_len',0,4
	.word	296
	.byte	3,35,132,1,9,32
	.word	296
	.byte	10,7,0,8
	.byte	'iv',0,32
	.word	509
	.byte	3,35,136,1,0,4
	.byte	'SM3_CTX_T',0,2,32,3
	.word	444
	.byte	7,2,34,9,108,9,64
	.word	235
	.byte	10,63,0,8
	.byte	'data',0,64
	.word	555
	.byte	2,35,0,8
	.byte	'datalen',0,4
	.word	296
	.byte	2,35,64,8
	.byte	'bitlen',0,8
	.word	336
	.byte	2,35,68,9,32
	.word	296
	.byte	10,7,0,8
	.byte	'state',0,32
	.word	611
	.byte	2,35,76,0,4
	.byte	'SHA256_CTX',0,2,39,3
	.word	550
	.byte	7,3,25,9,12,3
	.word	296
	.byte	8
	.byte	'x',0,4
	.word	660
	.byte	2,35,0,8
	.byte	'y',0,4
	.word	660
	.byte	2,35,4,8
	.byte	'z',0,4
	.word	660
	.byte	2,35,8,0,4
	.byte	'ecc_point_j',0,3,30,2
	.word	655
	.byte	9,32
	.word	235
	.byte	10,31,0
.L92:
	.byte	11
	.word	719
	.byte	9,16
	.word	270
	.byte	10,15,0
.L93:
	.byte	11
	.word	733
	.byte	9,128,2
	.word	235
	.byte	10,255,1,0
.L94:
	.byte	11
	.word	747
.L95:
	.byte	11
	.word	747
	.byte	9,10
	.word	235
	.byte	10,9,0
.L96:
	.byte	11
	.word	768
	.byte	9,16
	.word	235
	.byte	10,15,0
.L97:
	.byte	11
	.word	782
	.byte	9,128,12
	.word	296
	.byte	10,255,2,0
.L98:
	.byte	11
	.word	796
.L99:
	.byte	11
	.word	796
	.byte	9,192,1
	.word	655
	.byte	10,15,0
.L100:
	.byte	11
	.word	817
.L101:
	.byte	11
	.word	817
.L102:
	.byte	11
	.word	747
.L103:
	.byte	11
	.word	747
	.byte	9,64
	.word	296
	.byte	10,15,0
.L104:
	.byte	11
	.word	847
	.byte	9,32
	.word	296
	.byte	10,7,0
.L105:
	.byte	11
	.word	861
.L106:
	.byte	11
	.word	861
.L107:
	.byte	11
	.word	861
.L108:
	.byte	11
	.word	861
.L109:
	.byte	11
	.word	847
.L110:
	.byte	11
	.word	719
.L111:
	.byte	11
	.word	719
.L112:
	.byte	11
	.word	719
.L113:
	.byte	11
	.word	296
	.byte	9,128,2
	.word	296
	.byte	10,63,0
.L114:
	.byte	11
	.word	915
.L115:
	.byte	11
	.word	861
.L116:
	.byte	11
	.word	861
.L117:
	.byte	11
	.word	861
.L118:
	.byte	11
	.word	861
.L119:
	.byte	11
	.word	861
.L120:
	.byte	11
	.word	861
.L121:
	.byte	11
	.word	719
.L122:
	.byte	11
	.word	861
.L123:
	.byte	11
	.word	509
	.byte	9,128,2
	.word	782
	.byte	10,15,0
.L124:
	.byte	11
	.word	975
	.byte	9,16
	.word	296
	.byte	10,3,0
.L125:
	.byte	11
	.word	990
	.byte	9,128,1
	.word	296
	.byte	10,31,0
.L126:
	.byte	11
	.word	1004
.L127:
	.byte	11
	.word	719
.L128:
	.byte	11
	.word	719
.L129:
	.byte	11
	.word	782
	.byte	9,4
	.word	270
	.byte	10,3,0
.L130:
	.byte	11
	.word	1034
	.byte	9,7
	.word	270
	.byte	10,6,0
.L131:
	.byte	11
	.word	1048
.L132:
	.byte	11
	.word	1048
.L133:
	.byte	11
	.word	782
.L134:
	.byte	11
	.word	782
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,58,15,59,15,57,15,11,15,0,0,8,13,0,3,8
	.byte	11,15,73,19,56,9,0,0,9,1,1,11,15,73,19,0,0,10,33,0,47,15,0,0,11,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L136-.L135
.L135:
	.half	3
	.word	.L138-.L137
.L137:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vssconstant.c',0,0,0,0
	.byte	'..\\vss_code\\vsstype.h',0,0,0,0
	.byte	'..\\vss_code\\EccInternal.h',0,0,0,0,0
.L138:
.L136:
	.sdecl	'.debug_info',debug,cluster('vss_ZERO')
	.sect	'.debug_info'
.L6:
	.word	202
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_ZERO',0,1,8,17
	.word	.L92
	.byte	1,5,3
	.word	vss_ZERO
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_ZERO')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('VSS_VERSION')
	.sect	'.debug_info'
.L8:
	.word	205
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'VSS_VERSION',0,1,17,17
	.word	.L93
	.byte	1,5,3
	.word	VSS_VERSION
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('VSS_VERSION')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('aes_sbox')
	.sect	'.debug_info'
.L10:
	.word	202
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'aes_sbox',0,1,21,17
	.word	.L94
	.byte	1,5,3
	.word	aes_sbox
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('aes_sbox')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('aes_contrary_sbox')
	.sect	'.debug_info'
.L12:
	.word	211
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'aes_contrary_sbox',0,1,56,17
	.word	.L95
	.byte	1,5,3
	.word	aes_contrary_sbox
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('aes_contrary_sbox')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('aes_Rcon')
	.sect	'.debug_info'
.L14:
	.word	202
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'aes_Rcon',0,1,92,17
	.word	.L96
	.byte	1,5,3
	.word	aes_Rcon
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('aes_Rcon')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('cmac_Rb')
	.sect	'.debug_info'
.L16:
	.word	201
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'cmac_Rb',0,1,97,17
	.word	.L97
	.byte	1,5,3
	.word	cmac_Rb
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('cmac_Rb')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ecc_precompute_data_table_NIST256')
	.sect	'.debug_info'
.L18:
	.word	227
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'ecc_precompute_data_table_NIST256',0,1,104,18
	.word	.L98
	.byte	1,5,3
	.word	ecc_precompute_data_table_NIST256
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ecc_precompute_data_table_NIST256')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ecc_precompute_data_table_SM2')
	.sect	'.debug_info'
.L20:
	.word	224
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'ecc_precompute_data_table_SM2',0,1,155,1,18
	.word	.L99
	.byte	1,5,3
	.word	ecc_precompute_data_table_SM2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ecc_precompute_data_table_SM2')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ecc_precompute_point_table_SM2')
	.sect	'.debug_info'
.L22:
	.word	225
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'ecc_precompute_point_table_SM2',0,1,207,1,19
	.word	.L100
	.byte	1,5,3
	.word	ecc_precompute_point_table_SM2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ecc_precompute_point_table_SM2')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ecc_precompute_point_table_NIST256')
	.sect	'.debug_info'
.L24:
	.word	229
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'ecc_precompute_point_table_NIST256',0,1,227,1,19
	.word	.L101
	.byte	1,5,3
	.word	ecc_precompute_point_table_NIST256
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ecc_precompute_point_table_NIST256')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('KZUC_S0')
	.sect	'.debug_info'
.L26:
	.word	202
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'KZUC_S0',0,1,249,1,17
	.word	.L102
	.byte	1,5,3
	.word	KZUC_S0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('KZUC_S0')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('KZUC_S1')
	.sect	'.debug_info'
.L28:
	.word	202
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'KZUC_S1',0,1,140,2,18
	.word	.L103
	.byte	1,5,3
	.word	KZUC_S1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('KZUC_S1')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('KZUC_d')
	.sect	'.debug_info'
.L30:
	.word	201
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'KZUC_d',0,1,159,2,18
	.word	.L104
	.byte	1,5,3
	.word	KZUC_d
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('KZUC_d')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_g_ecdsa_para_a')
	.sect	'.debug_info'
.L32:
	.word	219
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_g_ecdsa_para_a',0,1,184,2,18
	.word	.L105
	.byte	1,5,3
	.word	mizar_ecc_g_ecdsa_para_a
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_g_ecdsa_para_a')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_g_ecdsa_para_b')
	.sect	'.debug_info'
.L34:
	.word	219
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_g_ecdsa_para_b',0,1,187,2,18
	.word	.L106
	.byte	1,5,3
	.word	mizar_ecc_g_ecdsa_para_b
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_g_ecdsa_para_b')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_g_ecdsa_para_p')
	.sect	'.debug_info'
.L36:
	.word	219
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_g_ecdsa_para_p',0,1,190,2,18
	.word	.L107
	.byte	1,5,3
	.word	mizar_ecc_g_ecdsa_para_p
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_g_ecdsa_para_p')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_g_ecdsa_para_n')
	.sect	'.debug_info'
.L38:
	.word	219
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_g_ecdsa_para_n',0,1,193,2,18
	.word	.L108
	.byte	1,5,3
	.word	mizar_ecc_g_ecdsa_para_n
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_g_ecdsa_para_n')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_g_ecdsa_para_G')
	.sect	'.debug_info'
.L40:
	.word	219
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_g_ecdsa_para_G',0,1,196,2,18
	.word	.L109
	.byte	1,5,3
	.word	mizar_ecc_g_ecdsa_para_G
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_g_ecdsa_para_G')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_vss_ecc_rand')
	.sect	'.debug_info'
.L42:
	.word	217
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_vss_ecc_rand',0,1,201,2,17
	.word	.L110
	.byte	1,5,3
	.word	mizar_ecc_vss_ecc_rand
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_vss_ecc_rand')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_rand1')
	.sect	'.debug_info'
.L44:
	.word	210
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_rand1',0,1,205,2,17
	.word	.L111
	.byte	1,5,3
	.word	mizar_ecc_rand1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_rand1')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_k')
	.sect	'.debug_info'
.L46:
	.word	206
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_k',0,1,209,2,17
	.word	.L112
	.byte	1,5,3
	.word	mizar_ecc_k
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_k')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_ecc_g_ecdsa_para_h')
	.sect	'.debug_info'
.L48:
	.word	219
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'mizar_ecc_g_ecdsa_para_h',0,1,213,2,18
	.word	.L113
	.byte	1,5,3
	.word	mizar_ecc_g_ecdsa_para_h
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_ecc_g_ecdsa_para_h')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sha256_k')
	.sect	'.debug_info'
.L50:
	.word	203
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sha256_k',0,1,217,2,12
	.word	.L114
	.byte	1,5,3
	.word	sha256_k
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sha256_k')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_G_SM2_Gx')
	.sect	'.debug_info'
.L52:
	.word	207
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_G_SM2_Gx',0,1,250,2,18
	.word	.L115
	.byte	1,5,3
	.word	sm2_G_SM2_Gx
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_G_SM2_Gx')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_G_SM2_Gy')
	.sect	'.debug_info'
.L54:
	.word	207
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_G_SM2_Gy',0,1,253,2,18
	.word	.L116
	.byte	1,5,3
	.word	sm2_G_SM2_Gy
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_G_SM2_Gy')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_G_SM2_a')
	.sect	'.debug_info'
.L56:
	.word	206
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_G_SM2_a',0,1,128,3,18
	.word	.L117
	.byte	1,5,3
	.word	sm2_G_SM2_a
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_G_SM2_a')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_G_SM2_b')
	.sect	'.debug_info'
.L58:
	.word	206
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_G_SM2_b',0,1,131,3,18
	.word	.L118
	.byte	1,5,3
	.word	sm2_G_SM2_b
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_G_SM2_b')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_G_SM2_p')
	.sect	'.debug_info'
.L60:
	.word	206
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_G_SM2_p',0,1,134,3,18
	.word	.L119
	.byte	1,5,3
	.word	sm2_G_SM2_p
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_G_SM2_p')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_G_SM2_n')
	.sect	'.debug_info'
.L62:
	.word	206
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_G_SM2_n',0,1,137,3,18
	.word	.L120
	.byte	1,5,3
	.word	sm2_G_SM2_n
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_G_SM2_n')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_pub_enc_rand')
	.sect	'.debug_info'
.L64:
	.word	211
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_pub_enc_rand',0,1,141,3,17
	.word	.L121
	.byte	1,5,3
	.word	sm2_pub_enc_rand
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_pub_enc_rand')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_pri_dec_rand')
	.sect	'.debug_info'
.L66:
	.word	211
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_pri_dec_rand',0,1,146,3,18
	.word	.L122
	.byte	1,5,3
	.word	sm2_pri_dec_rand
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_pri_dec_rand')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('SM3_IV')
	.sect	'.debug_info'
.L68:
	.word	201
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'SM3_IV',0,1,153,3,18
	.word	.L123
	.byte	1,5,3
	.word	SM3_IV
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('SM3_IV')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_SboxTable')
	.sect	'.debug_info'
.L70:
	.word	208
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm4_SboxTable',0,1,158,3,17
	.word	.L124
	.byte	1,5,3
	.word	sm4_SboxTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_SboxTable')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_FK')
	.sect	'.debug_info'
.L72:
	.word	201
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm4_FK',0,1,177,3,17
	.word	.L125
	.byte	1,5,3
	.word	sm4_FK
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_FK')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_CK')
	.sect	'.debug_info'
.L74:
	.word	201
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm4_CK',0,1,181,3,17
	.word	.L126
	.byte	1,5,3
	.word	sm4_CK
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_CK')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vssapi_sm2_rand')
	.sect	'.debug_info'
.L76:
	.word	210
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vssapi_sm2_rand',0,1,192,3,17
	.word	.L127
	.byte	1,5,3
	.word	vssapi_sm2_rand
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vssapi_sm2_rand')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vssapi_sm2_rand1')
	.sect	'.debug_info'
.L78:
	.word	211
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vssapi_sm2_rand1',0,1,197,3,17
	.word	.L128
	.byte	1,5,3
	.word	vssapi_sm2_rand1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vssapi_sm2_rand1')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vssapi_SESS_KEY')
	.sect	'.debug_info'
.L80:
	.word	210
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vssapi_SESS_KEY',0,1,202,3,17
	.word	.L129
	.byte	1,5,3
	.word	vssapi_SESS_KEY
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vssapi_SESS_KEY')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vssapi_KEY')
	.sect	'.debug_info'
.L82:
	.word	205
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vssapi_KEY',0,1,206,3,17
	.word	.L130
	.byte	1,5,3
	.word	vssapi_KEY
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vssapi_KEY')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vssapi_SERVER')
	.sect	'.debug_info'
.L84:
	.word	208
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vssapi_SERVER',0,1,207,3,17
	.word	.L131
	.byte	1,5,3
	.word	vssapi_SERVER
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vssapi_SERVER')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vssapi_CLIENT')
	.sect	'.debug_info'
.L86:
	.word	208
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vssapi_CLIENT',0,1,208,3,17
	.word	.L132
	.byte	1,5,3
	.word	vssapi_CLIENT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vssapi_CLIENT')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vsskeym_KEK')
	.sect	'.debug_info'
.L88:
	.word	206
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vsskeym_KEK',0,1,210,3,17
	.word	.L133
	.byte	1,5,3
	.word	vsskeym_KEK
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vsskeym_KEK')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vsskeym_SECOC')
	.sect	'.debug_info'
.L90:
	.word	208
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'..\\vss_code\\vssconstant.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vsskeym_SECOC',0,1,214,3,17
	.word	.L134
	.byte	1,5,3
	.word	vsskeym_SECOC
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vsskeym_SECOC')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0

; ..\vss_code\vssconstant.c	     1  #include "vsstype.h"
; ..\vss_code\vssconstant.c	     2  #include "vssconf.h"
; ..\vss_code\vssconstant.c	     3  #include "EccInternal.h"
; ..\vss_code\vssconstant.c	     4  
; ..\vss_code\vssconstant.c	     5  
; ..\vss_code\vssconstant.c	     6  #pragma section all "vss_api_code"
; ..\vss_code\vssconstant.c	     7  
; ..\vss_code\vssconstant.c	     8  const vss_uint8 vss_ZERO[32] = { 0 };
; ..\vss_code\vssconstant.c	     9  
; ..\vss_code\vssconstant.c	    10  #if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
; ..\vss_code\vssconstant.c	    11  #if (defined (_TTM_CHIP_)&&(_TTM_CHIP_ == 1U))
; ..\vss_code\vssconstant.c	    12  const vss_char8 VSS_VERSION[16] = "VSS_CHIP_V1.3";
; ..\vss_code\vssconstant.c	    13  #else
; ..\vss_code\vssconstant.c	    14  const vss_char8 VSS_VERSION[16] = "VSS_MPU_V1.3";
; ..\vss_code\vssconstant.c	    15  #endif
; ..\vss_code\vssconstant.c	    16  #else
; ..\vss_code\vssconstant.c	    17  const vss_char8 VSS_VERSION[16] = "VSS_MCU_V1.3";
; ..\vss_code\vssconstant.c	    18  #endif
; ..\vss_code\vssconstant.c	    19  
; ..\vss_code\vssconstant.c	    20  #if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))
; ..\vss_code\vssconstant.c	    21  const vss_uint8 aes_sbox[256] = {
; ..\vss_code\vssconstant.c	    22  	0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5,
; ..\vss_code\vssconstant.c	    23  	0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76,
; ..\vss_code\vssconstant.c	    24  	0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0,
; ..\vss_code\vssconstant.c	    25  	0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0,
; ..\vss_code\vssconstant.c	    26  	0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc,
; ..\vss_code\vssconstant.c	    27  	0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15,
; ..\vss_code\vssconstant.c	    28  	0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a,
; ..\vss_code\vssconstant.c	    29  	0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75,
; ..\vss_code\vssconstant.c	    30  	0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0,
; ..\vss_code\vssconstant.c	    31  	0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84,
; ..\vss_code\vssconstant.c	    32  	0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b,
; ..\vss_code\vssconstant.c	    33  	0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf,
; ..\vss_code\vssconstant.c	    34  	0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85,
; ..\vss_code\vssconstant.c	    35  	0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8,
; ..\vss_code\vssconstant.c	    36  	0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5,
; ..\vss_code\vssconstant.c	    37  	0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2,
; ..\vss_code\vssconstant.c	    38  	0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17,
; ..\vss_code\vssconstant.c	    39  	0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73,
; ..\vss_code\vssconstant.c	    40  	0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88,
; ..\vss_code\vssconstant.c	    41  	0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb,
; ..\vss_code\vssconstant.c	    42  	0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c,
; ..\vss_code\vssconstant.c	    43  	0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79,
; ..\vss_code\vssconstant.c	    44  	0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9,
; ..\vss_code\vssconstant.c	    45  	0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08,
; ..\vss_code\vssconstant.c	    46  	0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6,
; ..\vss_code\vssconstant.c	    47  	0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a,
; ..\vss_code\vssconstant.c	    48  	0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e,
; ..\vss_code\vssconstant.c	    49  	0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e,
; ..\vss_code\vssconstant.c	    50  	0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94,
; ..\vss_code\vssconstant.c	    51  	0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf,
; ..\vss_code\vssconstant.c	    52  	0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68,
; ..\vss_code\vssconstant.c	    53  	0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16,
; ..\vss_code\vssconstant.c	    54  };   
; ..\vss_code\vssconstant.c	    55  
; ..\vss_code\vssconstant.c	    56  const vss_uint8 aes_contrary_sbox[256] = {                            
; ..\vss_code\vssconstant.c	    57  	0x52, 0x09, 0x6a, 0xd5, 0x30, 0x36, 0xa5, 0x38,
; ..\vss_code\vssconstant.c	    58  	0xbf, 0x40, 0xa3, 0x9e, 0x81, 0xf3, 0xd7, 0xfb,
; ..\vss_code\vssconstant.c	    59  	0x7c, 0xe3, 0x39, 0x82, 0x9b, 0x2f, 0xff, 0x87,
; ..\vss_code\vssconstant.c	    60  	0x34, 0x8e, 0x43, 0x44, 0xc4, 0xde, 0xe9, 0xcb,
; ..\vss_code\vssconstant.c	    61  	0x54, 0x7b, 0x94, 0x32, 0xa6, 0xc2, 0x23, 0x3d,
; ..\vss_code\vssconstant.c	    62  	0xee, 0x4c, 0x95, 0x0b, 0x42, 0xfa, 0xc3, 0x4e,
; ..\vss_code\vssconstant.c	    63  	0x08, 0x2e, 0xa1, 0x66, 0x28, 0xd9, 0x24, 0xb2,
; ..\vss_code\vssconstant.c	    64  	0x76, 0x5b, 0xa2, 0x49, 0x6d, 0x8b, 0xd1, 0x25,
; ..\vss_code\vssconstant.c	    65  	0x72, 0xf8, 0xf6, 0x64, 0x86, 0x68, 0x98, 0x16,
; ..\vss_code\vssconstant.c	    66  	0xd4, 0xa4, 0x5c, 0xcc, 0x5d, 0x65, 0xb6, 0x92,
; ..\vss_code\vssconstant.c	    67  	0x6c, 0x70, 0x48, 0x50, 0xfd, 0xed, 0xb9, 0xda,
; ..\vss_code\vssconstant.c	    68  	0x5e, 0x15, 0x46, 0x57, 0xa7, 0x8d, 0x9d, 0x84,
; ..\vss_code\vssconstant.c	    69  	0x90, 0xd8, 0xab, 0x00, 0x8c, 0xbc, 0xd3, 0x0a,
; ..\vss_code\vssconstant.c	    70  	0xf7, 0xe4, 0x58, 0x05, 0xb8, 0xb3, 0x45, 0x06,
; ..\vss_code\vssconstant.c	    71  	0xd0, 0x2c, 0x1e, 0x8f, 0xca, 0x3f, 0x0f, 0x02,
; ..\vss_code\vssconstant.c	    72  	0xc1, 0xaf, 0xbd, 0x03, 0x01, 0x13, 0x8a, 0x6b,
; ..\vss_code\vssconstant.c	    73  	0x3a, 0x91, 0x11, 0x41, 0x4f, 0x67, 0xdc, 0xea,
; ..\vss_code\vssconstant.c	    74  	0x97, 0xf2, 0xcf, 0xce, 0xf0, 0xb4, 0xe6, 0x73,
; ..\vss_code\vssconstant.c	    75  	0x96, 0xac, 0x74, 0x22, 0xe7, 0xad, 0x35, 0x85,
; ..\vss_code\vssconstant.c	    76  	0xe2, 0xf9, 0x37, 0xe8, 0x1c, 0x75, 0xdf, 0x6e,
; ..\vss_code\vssconstant.c	    77  	0x47, 0xf1, 0x1a, 0x71, 0x1d, 0x29, 0xc5, 0x89,
; ..\vss_code\vssconstant.c	    78  	0x6f, 0xb7, 0x62, 0x0e, 0xaa, 0x18, 0xbe, 0x1b,
; ..\vss_code\vssconstant.c	    79  	0xfc, 0x56, 0x3e, 0x4b, 0xc6, 0xd2, 0x79, 0x20,
; ..\vss_code\vssconstant.c	    80  	0x9a, 0xdb, 0xc0, 0xfe, 0x78, 0xcd, 0x5a, 0xf4,
; ..\vss_code\vssconstant.c	    81  	0x1f, 0xdd, 0xa8, 0x33, 0x88, 0x07, 0xc7, 0x31,
; ..\vss_code\vssconstant.c	    82  	0xb1, 0x12, 0x10, 0x59, 0x27, 0x80, 0xec, 0x5f,
; ..\vss_code\vssconstant.c	    83  	0x60, 0x51, 0x7f, 0xa9, 0x19, 0xb5, 0x4a, 0x0d,
; ..\vss_code\vssconstant.c	    84  	0x2d, 0xe5, 0x7a, 0x9f, 0x93, 0xc9, 0x9c, 0xef,
; ..\vss_code\vssconstant.c	    85  	0xa0, 0xe0, 0x3b, 0x4d, 0xae, 0x2a, 0xf5, 0xb0,
; ..\vss_code\vssconstant.c	    86  	0xc8, 0xeb, 0xbb, 0x3c, 0x83, 0x53, 0x99, 0x61,
; ..\vss_code\vssconstant.c	    87  	0x17, 0x2b, 0x04, 0x7e, 0xba, 0x77, 0xd6, 0x26,
; ..\vss_code\vssconstant.c	    88  	0xe1, 0x69, 0x14, 0x63, 0x55, 0x21, 0x0c, 0x7d,
; ..\vss_code\vssconstant.c	    89  };
; ..\vss_code\vssconstant.c	    90  
; ..\vss_code\vssconstant.c	    91  /*轮常量表 The key schedule rcon table*/    
; ..\vss_code\vssconstant.c	    92  const vss_uint8 aes_Rcon[10] = {       
; ..\vss_code\vssconstant.c	    93  	0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36
; ..\vss_code\vssconstant.c	    94  };    
; ..\vss_code\vssconstant.c	    95  #endif
; ..\vss_code\vssconstant.c	    96  
; ..\vss_code\vssconstant.c	    97  const vss_uint8 cmac_Rb[16] = {
; ..\vss_code\vssconstant.c	    98  	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
; ..\vss_code\vssconstant.c	    99  	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87
; ..\vss_code\vssconstant.c	   100  };	
; ..\vss_code\vssconstant.c	   101  
; ..\vss_code\vssconstant.c	   102  #if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U)) || \ 
; ..\vss_code\vssconstant.c	   103  	(defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
; ..\vss_code\vssconstant.c	   104  const vss_uint32 ecc_precompute_data_table_NIST256[] = {  
; ..\vss_code\vssconstant.c	   105  	0xd898c296, 0xf4a13945, 0x2deb33a0, 0x77037d81, 0x63a440f2, 0xf8bce6e5, 0xe12c4247, 0x6b17d1f2, 
; ..\vss_code\vssconstant.c	   106  	0x37bf51f5, 0xcbb64068, 0x6b315ece, 0x2bce3357, 0x7c0f9e16, 0x8ee7eb4a, 0xfe1a7f9b, 0x4fe342e2, 
; ..\vss_code\vssconstant.c	   107  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   108  	0x04bac870, 0xf7d24bb7, 0x3a23c6ab, 0x593a09a0, 0xf94c9d1d, 0xdfcc2358, 0x297bed02, 0x3cfa0f87, 
; ..\vss_code\vssconstant.c	   109  	0x40f26940, 0xce98a30b, 0x0248a8af, 0x62121c0d, 0x8309af9b, 0xa758aa80, 0x70be12c6, 0xe4e37694, 
; ..\vss_code\vssconstant.c	   110  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   111  	0x86ef7d7d, 0xdd37e3ff, 0x088b86db, 0xf6d77c27, 0x254c5491, 0x28fe9a4f, 0x6df0fd5e, 0xd6690337, 
; ..\vss_code\vssconstant.c	   112  	0xaddad596, 0x9ff04992, 0x9e4373f9, 0xf3d1a7af, 0xdf074167, 0xa13e9578, 0xe6d13d22, 0x20e2a53c, 
; ..\vss_code\vssconstant.c	   113  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   114  	0x525d6abf, 0xaebfd735, 0x96bea25a, 0xc302f8f4, 0x544920a4, 0xdb82b3ea, 0x02eadb2e, 0x621c75d1, 
; ..\vss_code\vssconstant.c	   115  	0x9ef485f0, 0x8939dc4c, 0x57c46d63, 0x225d03d8, 0x522d7f70, 0x4fdac96f, 0xb4fa649d, 0xd7c4a4fe, 
; ..\vss_code\vssconstant.c	   116  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   117  	0xc0b9372a, 0x8bc659aa, 0xedd9583f, 0xf7659958, 0x8c267d88, 0x9f05f94a, 0xc99a739d, 0x00dc46e7, 
; ..\vss_code\vssconstant.c	   118  	0xdf55d0f2, 0x4af50a00, 0x8156bf6a, 0xb5eb202d, 0x5228c111, 0x40d1e3ab, 0x45793424, 0x0312a557, 
; ..\vss_code\vssconstant.c	   119  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   120  	0x7eb8cfee, 0x8d9692f7, 0x0d8c013d, 0x05e3f223, 0x84e32e59, 0x76347a52, 0x15b0a1e5, 0x3c53e290, 
; ..\vss_code\vssconstant.c	   121  	0xfae798d4, 0x538b7da5, 0x00d23591, 0x1b9f1bd1, 0x9a08693f, 0x11a9f072, 0x140efeb3, 0xd30e7cda, 
; ..\vss_code\vssconstant.c	   122  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   123  	0xf8e8f683, 0x6dfcf787, 0x3f7fbe90, 0x13d72b7a, 0x2df232cf, 0xfd426d94, 0x5fe39aad, 0xed84bb42, 
; ..\vss_code\vssconstant.c	   124  	0x732995fc, 0x023e67a1, 0x355430e3, 0x67dd0a8e, 0x97a1d703, 0x0cf83b61, 0x583c33f2, 0xa3233455, 
; ..\vss_code\vssconstant.c	   125  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   126  	0x5f165d99, 0xcebbbc7b, 0x8a4eee61, 0x50cc51c1, 0x1b4d0d1f, 0xb31d2353, 0x66382ada, 0x95e18452, 
; ..\vss_code\vssconstant.c	   127  	0x0a839b5b, 0xacad4f81, 0x4142ff0f, 0xa0a2a96e, 0x1f4fa12f, 0x3eaa8289, 0x6b0fb8f3, 0x68d68c8f, 
; ..\vss_code\vssconstant.c	   128  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   129  	0x51bbb3f1, 0x9311a269, 0x8d0f4f65, 0xe80f26bd, 0x6beccbb9, 0x9d3dc334, 0x101e5de4, 0x54e244d5, 
; ..\vss_code\vssconstant.c	   130  	0xf1b19e28, 0xb3ad4c6e, 0x58c2e3b7, 0x4334fbc0, 0x35df9c25, 0x19bd4107, 0xec106eb6, 0xd6bbec0e, 
; ..\vss_code\vssconstant.c	   131  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   132  	0x3fefcfc8, 0xe8881a83, 0xb9b5290b, 0xaea3c9e0, 0x771e4688, 0x10b37ecd, 0xd4d021b6, 0xee0816a3, 
; ..\vss_code\vssconstant.c	   133  	0xb3a8caa1, 0x8e9929bf, 0xc105f2d1, 0x48915dcf, 0xdb49019f, 0x3a5fdf82, 0xad9006e1, 0xc4a438e3, 
; ..\vss_code\vssconstant.c	   134  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   135  	0xe83ad2c9, 0x5d6dc503, 0xaed035be, 0xca9f7a1d, 0xcbd21e33, 0x552788ac, 0xe09cb9f0, 0x8699dd31, 
; ..\vss_code\vssconstant.c	   136  	0x329bf961, 0x38584196, 0xb82a5af9, 0x4cb20e96, 0xc72c78c1, 0x24199908, 0xe92859b7, 0x16e65484, 
; ..\vss_code\vssconstant.c	   137  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   138  	0xdb3038dd, 0xa20a2c70, 0xe99d5c7c, 0x5f0b46d5, 0x4b600b83, 0xc9b97d37, 0x3df3245e, 0x186c7f79, 
; ..\vss_code\vssconstant.c	   139  	0x4f1ce57f, 0x2af72460, 0x91e2d8ed, 0x9249897f, 0x8d2ea797, 0x8139b36a, 0x9ab58913, 0x9c428db8, 
; ..\vss_code\vssconstant.c	   140  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   141  	0x4be6458d, 0x1f1e4f3f, 0x595e6547, 0x5f72cc22, 0x271a93f1, 0x5bc5341e, 0x58a5f263, 0xc62e155c, 
; ..\vss_code\vssconstant.c	   142  	0x58ba7ff4, 0x5f6f845a, 0x7e36a6ad, 0x67e1f7dc, 0xeeaa4d04, 0xd33a7657, 0x18267e4e, 0xff9f2322, 
; ..\vss_code\vssconstant.c	   143  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   144  	0xc7644c1d, 0xe33f0255, 0xbb9002d8, 0x4030ecc3, 0xf4646f9f, 0xa4486916, 0x959c44fa, 0x5e677d0c, 
; ..\vss_code\vssconstant.c	   145  	0xd88b9144, 0xe2e7d7d0, 0x6248f91f, 0x5d93a86f, 0x02993aea, 0xe33d0bd5, 0x3100d31e, 0x449f0ce6, 
; ..\vss_code\vssconstant.c	   146  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   147  	0xfdaab256, 0x52df1588, 0x3127354c, 0x68c0cd44, 0xa591f853, 0x2a849471, 0x93d0cb92, 0xe4da88e9, 
; ..\vss_code\vssconstant.c	   148  	0x1639c624, 0x6d1ea35d, 0x263707ba, 0x60fe2a36, 0xd0f3bc51, 0x97fc50de, 0x10062e80, 0xf7fa4d15, 
; ..\vss_code\vssconstant.c	   149  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   150  	0x5b696527, 0x2e75a266, 0x5a00169c, 0x1a2530b0, 0x4286fb42, 0x76c4c180, 0x8e831d5b, 0x825f0194, 
; ..\vss_code\vssconstant.c	   151  	0xef703739, 0xdbf0a11f, 0xce5b106a, 0x106f9bc4, 0x24111150, 0x61794c4f, 0xbc723a17, 0x435872fe, 
; ..\vss_code\vssconstant.c	   152  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   153  };
; ..\vss_code\vssconstant.c	   154  
; ..\vss_code\vssconstant.c	   155  const vss_uint32 ecc_precompute_data_table_SM2[] = { 
; ..\vss_code\vssconstant.c	   156  	0x334c74c7, 0x715a4589, 0xf2660be1, 0x8fe30bbf, 0x6a39c994, 0x5f990446, 0x1f198119, 0x32c4ae2c, 
; ..\vss_code\vssconstant.c	   157  	0x2139f0a0, 0x02df32e5, 0xc62a4740, 0xd0a9877c, 0x6b692153, 0x59bdcee3, 0xf4f6779c, 0xbc3736a2, 
; ..\vss_code\vssconstant.c	   158  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   159  	0x95a242dd, 0x1fe83bea, 0x9a47ecee, 0x8c771acc, 0x6a2b0d81, 0x625165be, 0xa82a0c56, 0xd73c307c, 
; ..\vss_code\vssconstant.c	   160  	0xfdf565da, 0x38688d92, 0x85244e7e, 0x12da1dde, 0x5f9d4c55, 0xb6a191c7, 0x4cb1c759, 0x0de7bf2b, 
; ..\vss_code\vssconstant.c	   161  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   162  	0x45001029, 0xae59f354, 0x483fbb51, 0xb6bfe296, 0x19529edc, 0x41951b8e, 0x1eb1de7f, 0xdff1e824, 
; ..\vss_code\vssconstant.c	   163  	0x27010535, 0x8571c688, 0xde9e9ca3, 0x9d4312e6, 0x7c5d9873, 0x054289c7, 0x13fdfda2, 0x3dfb27fc, 
; ..\vss_code\vssconstant.c	   164  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   165  	0x8fa73272, 0xaf8587cf, 0x063ade40, 0xa8ecc926, 0xb74de5f6, 0x96d966d1, 0x4dfe1a2c, 0xc5348b34, 
; ..\vss_code\vssconstant.c	   166  	0x686ce116, 0xb0a30ddb, 0x6b769315, 0x2bf84f32, 0xb5b68380, 0xe8f6c722, 0x2efdb4cc, 0xaa9fd3f6, 
; ..\vss_code\vssconstant.c	   167  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   168  	0x02c80626, 0xa92a2098, 0xb97556eb, 0xa621dc4b, 0x374309ae, 0x0c137441, 0xec77691c, 0x4302c5ec, 
; ..\vss_code\vssconstant.c	   169  	0x4276db9d, 0x215a604a, 0x360addbb, 0xb2b83605, 0x41f097cb, 0xb7f4d209, 0xf618afa2, 0x2e41a3d9, 
; ..\vss_code\vssconstant.c	   170  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   171  	0x25f11789, 0x26fab63e, 0x38859dc2, 0x9de0e723, 0xf97bcacf, 0x5f4dacd2, 0x9756efb2, 0x0a9aa461, 
; ..\vss_code\vssconstant.c	   172  	0x29f1066e, 0xc8cde669, 0x074b172e, 0x54abfd30, 0xec439508, 0x81cf4701, 0x2c4a8a9a, 0x2fc9a4f0, 
; ..\vss_code\vssconstant.c	   173  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   174  	0x6af571dc, 0x1c2304b1, 0x209ddfe9, 0x6f3dac1a, 0xf7c55941, 0x07a2a5ef, 0xfaf2ce4b, 0x55a7146e, 
; ..\vss_code\vssconstant.c	   175  	0x7d3ee627, 0x7109ba5f, 0x12e88095, 0xc8dbb605, 0x7ef38cf2, 0x63b2680a, 0x9e530f7e, 0x51112815, 
; ..\vss_code\vssconstant.c	   176  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   177  	0x342ee4cf, 0xb7424eaa, 0x7e644acf, 0xb342d998, 0x5d67f117, 0x5cc731d4, 0xbf809c5f, 0xd5c50898, 
; ..\vss_code\vssconstant.c	   178  	0x314a3c16, 0xf1cb19d1, 0x64b04dc8, 0x49e346de, 0x783d30bc, 0x43a0d3d6, 0x8c0028b5, 0x9a585251, 
; ..\vss_code\vssconstant.c	   179  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   180  	0x0937e4d7, 0x26158104, 0x5eae3dc6, 0xa5c306b8, 0xda1ac9f2, 0x9100e703, 0x52ae3c25, 0xeeb0be34, 
; ..\vss_code\vssconstant.c	   181  	0xcd037cd2, 0xaf2827cb, 0xb212ab8e, 0x43adcc00, 0xe8806282, 0x857d88fb, 0x5dbcffcf, 0xbcb1c87c, 
; ..\vss_code\vssconstant.c	   182  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   183  	0xe9dccbd3, 0xbf9e0c8b, 0x4e634746, 0x5e736c25, 0xac7772ea, 0x4c038c0a, 0xb44bef6e, 0xfefc09d6, 
; ..\vss_code\vssconstant.c	   184  	0x34be10c8, 0x51057fc3, 0x89156970, 0x74facf3d, 0x352f0b3d, 0xfde74e3a, 0x0147a676, 0x43892742, 
; ..\vss_code\vssconstant.c	   185  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   186  	0x17fb0540, 0xb34cea39, 0x31e0fb38, 0xf711587e, 0x157862ec, 0x1b2246b0, 0x0cc945a5, 0x133ff494, 
; ..\vss_code\vssconstant.c	   187  	0x52405897, 0xa7b5a196, 0x6ff5b910, 0x71d55dff, 0xf09611e7, 0xf4188a38, 0x639ed762, 0xa9eb1002, 
; ..\vss_code\vssconstant.c	   188  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   189  	0x5aedc515, 0x9b6165c3, 0xad9c3a9a, 0x74d1595e, 0xd90492b1, 0xc0f55824, 0xd1aeeee0, 0xb7a8083d, 
; ..\vss_code\vssconstant.c	   190  	0x1a5e1679, 0x72a54727, 0x3cfd71a6, 0xc5ed7a8b, 0x2b49a6b0, 0xdeb630c5, 0x7d99192f, 0x392fdaf3, 
; ..\vss_code\vssconstant.c	   191  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   192  	0x08a0f54f, 0x7df01b02, 0x71d310e1, 0x0d515bc8, 0x3ba945e3, 0x51932c50, 0xb56cef1a, 0x626f0095, 
; ..\vss_code\vssconstant.c	   193  	0x303eaa14, 0x995f0bf0, 0xf99ba498, 0x20ac8559, 0x7e6e71c0, 0x6731d794, 0x1447bee5, 0x7dcf6813, 
; ..\vss_code\vssconstant.c	   194  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   195  	0x185df5e9, 0x5d9845ea, 0xcc2800d9, 0x5dfbcc4a, 0xc83566a9, 0x090d5ae4, 0x22711eef, 0x39308636, 
; ..\vss_code\vssconstant.c	   196  	0x6bf92062, 0xfd52f34b, 0x89a88bdb, 0x3ee11960, 0x0eff0caa, 0x408378c9, 0xf84f070d, 0x19bcfb93, 
; ..\vss_code\vssconstant.c	   197  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   198  	0x577f5324, 0x013b7283, 0xbb3d53d9, 0x8610b5f0, 0x68a20568, 0x26e0fb28, 0x63743abe, 0x818b1062, 
; ..\vss_code\vssconstant.c	   199  	0x641453a6, 0x4744944f, 0xdab51706, 0x014fd7d3, 0xe6504674, 0xc57b2107, 0x6d37e0f2, 0xf17f4685, 
; ..\vss_code\vssconstant.c	   200  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
; ..\vss_code\vssconstant.c	   201  	0xdf1cf2d5, 0x406f5b44, 0x82e646c8, 0x8e4b0c42, 0xef7b16ec, 0x893221e5, 0xae76e93f, 0x282389be, 
; ..\vss_code\vssconstant.c	   202  	0x1d0ca406, 0xcc23fdbc, 0x0adfb4a8, 0x1cc7e935, 0x3c09eac9, 0xe14f2481, 0x710e14b7, 0xc9032eea, 
; ..\vss_code\vssconstant.c	   203  	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
; ..\vss_code\vssconstant.c	   204  };
; ..\vss_code\vssconstant.c	   205  
; ..\vss_code\vssconstant.c	   206  #define SIZE_NUM 8
; ..\vss_code\vssconstant.c	   207  const ecc_point_j ecc_precompute_point_table_SM2[] = 
; ..\vss_code\vssconstant.c	   208  {
; ..\vss_code\vssconstant.c	   209  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[0*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[1*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[2*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   210  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[3*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[4*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[5*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   211  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[6*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[7*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[8*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   212  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[9*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[10*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[11*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   213  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[12*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[13*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[14*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   214  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[15*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[16*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[17*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   215  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[18*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[19*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[20*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   216  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[21*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[22*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[23*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   217  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[24*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[25*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[26*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   218  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[27*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[28*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[29*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   219  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[30*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[31*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[32*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   220  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[33*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[34*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[35*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   221  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[36*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[37*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[38*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   222  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[39*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[40*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[41*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   223  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[42*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[43*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[44*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   224  	{(vss_uint32 *)&ecc_precompute_data_table_SM2[45*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[46*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[47*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   225  };
; ..\vss_code\vssconstant.c	   226  
; ..\vss_code\vssconstant.c	   227  const ecc_point_j ecc_precompute_point_table_NIST256[] = 
; ..\vss_code\vssconstant.c	   228  {
; ..\vss_code\vssconstant.c	   229  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[0*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[1*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[2*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   230  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[3*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[4*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[5*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   231  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[6*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[7*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[8*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   232  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[9*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[10*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[11*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   233  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[12*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[13*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[14*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   234  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[15*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[16*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[17*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   235  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[18*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[19*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[20*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   236  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[21*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[22*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[23*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   237  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[24*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[25*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[26*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   238  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[27*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[28*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[29*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   239  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[30*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[31*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[32*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   240  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[33*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[34*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[35*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   241  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[36*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[37*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[38*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   242  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[39*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[40*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[41*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   243  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[42*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[43*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[44*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   244  	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[45*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[46*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[47*SIZE_NUM]},
; ..\vss_code\vssconstant.c	   245  };
; ..\vss_code\vssconstant.c	   246  #endif 
; ..\vss_code\vssconstant.c	   247  
; ..\vss_code\vssconstant.c	   248  #if (defined (_ENABLE_MIZAR_ZUC_)&&(_ENABLE_MIZAR_ZUC_ == 1U))
; ..\vss_code\vssconstant.c	   249  const vss_uint8 KZUC_S0[256] = {
; ..\vss_code\vssconstant.c	   250  	0x3e, 0x72, 0x5b, 0x47, 0xca, 0xe0, 0x00, 0x33, 0x04, 0xd1, 0x54, 0x98, 0x09, 0xb9, 0x6d, 0xcb,
; ..\vss_code\vssconstant.c	   251  	0x7b, 0x1b, 0xf9, 0x32, 0xaf, 0x9d, 0x6a, 0xa5, 0xb8, 0x2d, 0xfc, 0x1d, 0x08, 0x53, 0x03, 0x90,
; ..\vss_code\vssconstant.c	   252  	0x4d, 0x4e, 0x84, 0x99, 0xe4, 0xce, 0xd9, 0x91, 0xdd, 0xb6, 0x85, 0x48, 0x8b, 0x29, 0x6e, 0xac,
; ..\vss_code\vssconstant.c	   253  	0xcd, 0xc1, 0xf8, 0x1e, 0x73, 0x43, 0x69, 0xc6, 0xb5, 0xbd, 0xfd, 0x39, 0x63, 0x20, 0xd4, 0x38,
; ..\vss_code\vssconstant.c	   254  	0x76, 0x7d, 0xb2, 0xa7, 0xcf, 0xed, 0x57, 0xc5, 0xf3, 0x2c, 0xbb, 0x14, 0x21, 0x06, 0x55, 0x9b,
; ..\vss_code\vssconstant.c	   255  	0xe3, 0xef, 0x5e, 0x31, 0x4f, 0x7f, 0x5a, 0xa4, 0x0d, 0x82, 0x51, 0x49, 0x5f, 0xba, 0x58, 0x1c,
; ..\vss_code\vssconstant.c	   256  	0x4a, 0x16, 0xd5, 0x17, 0xa8, 0x92, 0x24, 0x1f, 0x8c, 0xff, 0xd8, 0xae, 0x2e, 0x01, 0xd3, 0xad,
; ..\vss_code\vssconstant.c	   257  	0x3b, 0x4b, 0xda, 0x46, 0xeb, 0xc9, 0xde, 0x9a, 0x8f, 0x87, 0xd7, 0x3a, 0x80, 0x6f, 0x2f, 0xc8,
; ..\vss_code\vssconstant.c	   258  	0xb1, 0xb4, 0x37, 0xf7, 0x0a, 0x22, 0x13, 0x28, 0x7c, 0xcc, 0x3c, 0x89, 0xc7, 0xc3, 0x96, 0x56,
; ..\vss_code\vssconstant.c	   259  	0x07, 0xbf, 0x7e, 0xf0, 0x0b, 0x2b, 0x97, 0x52, 0x35, 0x41, 0x79, 0x61, 0xa6, 0x4c, 0x10, 0xfe,
; ..\vss_code\vssconstant.c	   260  	0xbc, 0x26, 0x95, 0x88, 0x8a, 0xb0, 0xa3, 0xfb, 0xc0, 0x18, 0x94, 0xf2, 0xe1, 0xe5, 0xe9, 0x5d,
; ..\vss_code\vssconstant.c	   261  	0xd0, 0xdc, 0x11, 0x66, 0x64, 0x5c, 0xec, 0x59, 0x42, 0x75, 0x12, 0xf5, 0x74, 0x9c, 0xaa, 0x23,
; ..\vss_code\vssconstant.c	   262  	0x0e, 0x86, 0xab, 0xbe, 0x2a, 0x02, 0xe7, 0x67, 0xe6, 0x44, 0xa2, 0x6c, 0xc2, 0x93, 0x9f, 0xf1,
; ..\vss_code\vssconstant.c	   263  	0xf6, 0xfa, 0x36, 0xd2, 0x50, 0x68, 0x9e, 0x62, 0x71, 0x15, 0x3d, 0xd6, 0x40, 0xc4, 0xe2, 0x0f,
; ..\vss_code\vssconstant.c	   264  	0x8e, 0x83, 0x77, 0x6b, 0x25, 0x05, 0x3f, 0x0c, 0x30, 0xea, 0x70, 0xb7, 0xa1, 0xe8, 0xa9, 0x65,
; ..\vss_code\vssconstant.c	   265  	0x8d, 0x27, 0x1a, 0xdb, 0x81, 0xb3, 0xa0, 0xf4, 0x45, 0x7a, 0x19, 0xdf, 0xee, 0x78, 0x34, 0x60
; ..\vss_code\vssconstant.c	   266  };
; ..\vss_code\vssconstant.c	   267  
; ..\vss_code\vssconstant.c	   268   const vss_uint8 KZUC_S1[256] ={
; ..\vss_code\vssconstant.c	   269   	0x55, 0xc2, 0x63, 0x71, 0x3b, 0xc8, 0x47, 0x86, 0x9f, 0x3c, 0xda, 0x5b, 0x29, 0xaa, 0xfd, 0x77,
; ..\vss_code\vssconstant.c	   270  	0x8c, 0xc5, 0x94, 0x0c, 0xa6, 0x1a, 0x13, 0x00, 0xe3, 0xa8, 0x16, 0x72, 0x40, 0xf9, 0xf8, 0x42,
; ..\vss_code\vssconstant.c	   271  	0x44, 0x26, 0x68, 0x96, 0x81, 0xd9, 0x45, 0x3e, 0x10, 0x76, 0xc6, 0xa7, 0x8b, 0x39, 0x43, 0xe1,
; ..\vss_code\vssconstant.c	   272  	0x3a, 0xb5, 0x56, 0x2a, 0xc0, 0x6d, 0xb3, 0x05, 0x22, 0x66, 0xbf, 0xdc, 0x0b, 0xfa, 0x62, 0x48,
; ..\vss_code\vssconstant.c	   273  	0xdd, 0x20, 0x11, 0x06, 0x36, 0xc9, 0xc1, 0xcf, 0xf6, 0x27, 0x52, 0xbb, 0x69, 0xf5, 0xd4, 0x87,
; ..\vss_code\vssconstant.c	   274  	0x7f, 0x84, 0x4c, 0xd2, 0x9c, 0x57, 0xa4, 0xbc, 0x4f, 0x9a, 0xdf, 0xfe, 0xd6, 0x8d, 0x7a, 0xeb,
; ..\vss_code\vssconstant.c	   275  	0x2b, 0x53, 0xd8, 0x5c, 0xa1, 0x14, 0x17, 0xfb, 0x23, 0xd5, 0x7d, 0x30, 0x67, 0x73, 0x08, 0x09,
; ..\vss_code\vssconstant.c	   276  	0xee, 0xb7, 0x70, 0x3f, 0x61, 0xb2, 0x19, 0x8e, 0x4e, 0xe5, 0x4b, 0x93, 0x8f, 0x5d, 0xdb, 0xa9,
; ..\vss_code\vssconstant.c	   277  	0xad, 0xf1, 0xae, 0x2e, 0xcb, 0x0d, 0xfc, 0xf4, 0x2d, 0x46, 0x6e, 0x1d, 0x97, 0xe8, 0xd1, 0xe9,
; ..\vss_code\vssconstant.c	   278  	0x4d, 0x37, 0xa5, 0x75, 0x5e, 0x83, 0x9e, 0xab, 0x82, 0x9d, 0xb9, 0x1c, 0xe0, 0xcd, 0x49, 0x89,
; ..\vss_code\vssconstant.c	   279  	0x01, 0xb6, 0xbd, 0x58, 0x24, 0xa2, 0x5f, 0x38, 0x78, 0x99, 0x15, 0x90, 0x50, 0xb8, 0x95, 0xe4,
; ..\vss_code\vssconstant.c	   280  	0xd0, 0x91, 0xc7, 0xce, 0xed, 0x0f, 0xb4, 0x6f, 0xa0, 0xcc, 0xf0, 0x02, 0x4a, 0x79, 0xc3, 0xde,
; ..\vss_code\vssconstant.c	   281  	0xa3, 0xef, 0xea, 0x51, 0xe6, 0x6b, 0x18, 0xec, 0x1b, 0x2c, 0x80, 0xf7, 0x74, 0xe7, 0xff, 0x21,
; ..\vss_code\vssconstant.c	   282  	0x5a, 0x6a, 0x54, 0x1e, 0x41, 0x31, 0x92, 0x35, 0xc4, 0x33, 0x07, 0x0a, 0xba, 0x7e, 0x0e, 0x34,
; ..\vss_code\vssconstant.c	   283  	0x88, 0xb1, 0x98, 0x7c, 0xf3, 0x3d, 0x60, 0x6c, 0x7b, 0xca, 0xd3, 0x1f, 0x32, 0x65, 0x04, 0x28,
; ..\vss_code\vssconstant.c	   284  	0x64, 0xbe, 0x85, 0x9b, 0x2f, 0x59, 0x8a, 0xd7, 0xb0, 0x25, 0xac, 0xaf, 0x12, 0x03, 0xe2, 0xf2
; ..\vss_code\vssconstant.c	   285  };
; ..\vss_code\vssconstant.c	   286  
; ..\vss_code\vssconstant.c	   287  const vss_uint32 KZUC_d[16] = {
; ..\vss_code\vssconstant.c	   288  	0x44D7, 0x26BC, 0x626B, 0x135E, 0x5789, 0x35E2, 0x7135, 0x09AF,
; ..\vss_code\vssconstant.c	   289  	0x4D78, 0x2F13, 0x6BC4, 0x1AF1, 0x5E26, 0x3C4D, 0x789A, 0x47AC
; ..\vss_code\vssconstant.c	   290  };
; ..\vss_code\vssconstant.c	   291  #endif
; ..\vss_code\vssconstant.c	   292  
; ..\vss_code\vssconstant.c	   293  #if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
; ..\vss_code\vssconstant.c	   294  #if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U))
; ..\vss_code\vssconstant.c	   295  const vss_uint32 mizar_ecc_g_ecdsa_para_a[8] = {
; ..\vss_code\vssconstant.c	   296  	0xFFFFFFFF, 0x00000001, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFC
; ..\vss_code\vssconstant.c	   297  };
; ..\vss_code\vssconstant.c	   298  const vss_uint32 mizar_ecc_g_ecdsa_para_b[8] = {
; ..\vss_code\vssconstant.c	   299  	0x5AC635D8, 0xAA3A93E7, 0xB3EBBD55, 0x769886BC, 0x651D06B0, 0xCC53B0F6, 0x3BCE3C3E, 0x27D2604B
; ..\vss_code\vssconstant.c	   300  };
; ..\vss_code\vssconstant.c	   301  const vss_uint32 mizar_ecc_g_ecdsa_para_p[8] = {
; ..\vss_code\vssconstant.c	   302  	0xFFFFFFFF, 0x00000001, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
; ..\vss_code\vssconstant.c	   303  };
; ..\vss_code\vssconstant.c	   304  const vss_uint32 mizar_ecc_g_ecdsa_para_n[8] = {
; ..\vss_code\vssconstant.c	   305  	0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xBCE6FAAD, 0xA7179E84, 0xF3B9CAC2, 0xFC632551
; ..\vss_code\vssconstant.c	   306  };
; ..\vss_code\vssconstant.c	   307  const vss_uint32 mizar_ecc_g_ecdsa_para_G[16] = {
; ..\vss_code\vssconstant.c	   308  	0x6B17D1F2, 0xE12C4247, 0xF8BCE6E5, 0x63A440F2, 0x77037D81, 0x2DEB33A0, 0xF4A13945, 0xD898C296,
; ..\vss_code\vssconstant.c	   309  	0x4FE342E2, 0xFE1A7F9B, 0x8EE7EB4A, 0x7C0F9E16, 0x2BCE3357, 0x6B315ECE, 0xCBB64068, 0x37BF51F5
; ..\vss_code\vssconstant.c	   310  };
; ..\vss_code\vssconstant.c	   311  #else
; ..\vss_code\vssconstant.c	   312  const vss_uint32 mizar_ecc_g_ecdsa_para_a[8] = {
; ..\vss_code\vssconstant.c	   313  	0xFFFFFFFF, 0x01000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFCFFFFFF
; ..\vss_code\vssconstant.c	   314  };
; ..\vss_code\vssconstant.c	   315  const vss_uint32 mizar_ecc_g_ecdsa_para_b[8] = {
; ..\vss_code\vssconstant.c	   316  	0xD835C65A, 0xE7933AAA, 0x55BDEBB3, 0xBC869876, 0xB0061D65, 0xF6B053CC, 0x3E3CCE3B, 0x4B60D227
; ..\vss_code\vssconstant.c	   317  };
; ..\vss_code\vssconstant.c	   318  const vss_uint32 mizar_ecc_g_ecdsa_para_p[8] = {
; ..\vss_code\vssconstant.c	   319  	0xFFFFFFFF, 0x01000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
; ..\vss_code\vssconstant.c	   320  };
; ..\vss_code\vssconstant.c	   321  const vss_uint32 mizar_ecc_g_ecdsa_para_n[8] = {
; ..\vss_code\vssconstant.c	   322  	0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xADFAE6BC, 0x849E17A7, 0xC2CAB9F3, 0x512563FC
; ..\vss_code\vssconstant.c	   323  };
; ..\vss_code\vssconstant.c	   324  const vss_uint32 mizar_ecc_g_ecdsa_para_G[16] = {
; ..\vss_code\vssconstant.c	   325  	0xF2D1176B, 0x47422CE1, 0xE5E6BCF8, 0xF240A463, 0x817D0377, 0xA033EB2D, 0x4539A1F4, 0x96C298D8,
; ..\vss_code\vssconstant.c	   326  	0xE242E34F, 0x9B7F1AFE, 0x4AEBE78E, 0x169E0F7C, 0x5733CE2B, 0xCE5E316B, 0x6840B6CB, 0xF551BF37
; ..\vss_code\vssconstant.c	   327  };
; ..\vss_code\vssconstant.c	   328  #endif
; ..\vss_code\vssconstant.c	   329  const vss_uint8 mizar_ecc_vss_ecc_rand[32] = {
; ..\vss_code\vssconstant.c	   330  	0x06, 0xC1, 0xEB, 0x2A, 0xCF, 0x22, 0x12, 0x2A, 0x54, 0xB4, 0x7D, 0x6A, 0x06, 0xFF, 0x62, 0xCC, 
; ..\vss_code\vssconstant.c	   331  	0x8A, 0x07, 0x7D, 0xB2, 0x1B, 0xA5, 0x3C, 0x67, 0xC0, 0x7A, 0x4A, 0x44, 0x6E, 0x7B, 0xF5, 0x00
; ..\vss_code\vssconstant.c	   332  };
; ..\vss_code\vssconstant.c	   333  const vss_uint8 mizar_ecc_rand1[32] = {
; ..\vss_code\vssconstant.c	   334  	0x6C, 0xE3, 0xF2, 0xBB, 0x2E, 0xC5, 0x18, 0x6B, 0x66, 0x3B, 0x03, 0x69, 0x31, 0xB9, 0x50, 0xDE,
; ..\vss_code\vssconstant.c	   335  	0xA8, 0x2B, 0x41, 0x10, 0x56, 0xB4, 0x9F, 0xA1, 0x08, 0x30, 0x6F, 0x7E, 0x43, 0x79, 0x41, 0x19
; ..\vss_code\vssconstant.c	   336  };
; ..\vss_code\vssconstant.c	   337  const vss_uint8 mizar_ecc_k[32] = {
; ..\vss_code\vssconstant.c	   338  	0x8A, 0x07, 0x7D, 0xB6, 0x1B, 0xA5, 0x3C, 0x67, 0xC0, 0x7A, 0x4A, 0x44, 0x6E, 0x7B, 0x95, 0x07,
; ..\vss_code\vssconstant.c	   339  	0x71, 0xC1, 0xEB, 0x4A, 0xCF, 0x22, 0x12, 0x2A, 0x54, 0xB4, 0x7D, 0x6A, 0x06, 0xFF, 0x62, 0xCC
; ..\vss_code\vssconstant.c	   340  };
; ..\vss_code\vssconstant.c	   341  const vss_uint32 mizar_ecc_g_ecdsa_para_h = 1;
; ..\vss_code\vssconstant.c	   342  #endif 
; ..\vss_code\vssconstant.c	   343  
; ..\vss_code\vssconstant.c	   344  #if (defined (_ENABLE_MIZAR_SHA256_)&&(_ENABLE_MIZAR_SHA256_ == 1U))
; ..\vss_code\vssconstant.c	   345  const WORD sha256_k[64] = {
; ..\vss_code\vssconstant.c	   346  	0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
; ..\vss_code\vssconstant.c	   347  	0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
; ..\vss_code\vssconstant.c	   348  	0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
; ..\vss_code\vssconstant.c	   349  	0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
; ..\vss_code\vssconstant.c	   350  	0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
; ..\vss_code\vssconstant.c	   351  	0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
; ..\vss_code\vssconstant.c	   352  	0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
; ..\vss_code\vssconstant.c	   353  	0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
; ..\vss_code\vssconstant.c	   354  };
; ..\vss_code\vssconstant.c	   355  #endif
; ..\vss_code\vssconstant.c	   356  
; ..\vss_code\vssconstant.c	   357  #if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U)) 
; ..\vss_code\vssconstant.c	   358  #if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U))
; ..\vss_code\vssconstant.c	   359  const vss_uint32 sm2_G_SM2_Gx[8] = {
; ..\vss_code\vssconstant.c	   360  	0x32C4AE2C, 0x1F198119, 0x5F990446, 0x6A39C994, 0x8FE30BBF, 0xF2660BE1, 0x715A4589, 0x334C74C7
; ..\vss_code\vssconstant.c	   361  };
; ..\vss_code\vssconstant.c	   362  const vss_uint32 sm2_G_SM2_Gy[8] = {
; ..\vss_code\vssconstant.c	   363  	0xBC3736A2, 0xF4F6779C, 0x59BDCEE3, 0x6B692153, 0xD0A9877C, 0xC62A4740, 0x02DF32E5, 0x2139F0A0
; ..\vss_code\vssconstant.c	   364  };
; ..\vss_code\vssconstant.c	   365  const vss_uint32 sm2_G_SM2_a[8] = {
; ..\vss_code\vssconstant.c	   366  	0xFFFFFFFE, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFC
; ..\vss_code\vssconstant.c	   367  };
; ..\vss_code\vssconstant.c	   368  const vss_uint32 sm2_G_SM2_b[8] = {
; ..\vss_code\vssconstant.c	   369  	0x28E9FA9E, 0x9D9F5E34, 0x4D5A9E4B, 0xCF6509A7, 0xF39789F5, 0x15AB8F92, 0xDDBCBD41, 0x4D940E93
; ..\vss_code\vssconstant.c	   370  };
; ..\vss_code\vssconstant.c	   371  const vss_uint32 sm2_G_SM2_p[8] = {
; ..\vss_code\vssconstant.c	   372  	0xFFFFFFFE, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF
; ..\vss_code\vssconstant.c	   373  };
; ..\vss_code\vssconstant.c	   374  const vss_uint32 sm2_G_SM2_n[8] = {
; ..\vss_code\vssconstant.c	   375  	0xFFFFFFFE, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x7203DF6B, 0x21C6052B, 0x53BBF409, 0x39D54123
; ..\vss_code\vssconstant.c	   376  };
; ..\vss_code\vssconstant.c	   377  #else
; ..\vss_code\vssconstant.c	   378  const vss_uint32 sm2_G_SM2_Gx[8] = {
; ..\vss_code\vssconstant.c	   379  	0x2CAEC432, 0x1981191F, 0x4604995F, 0x94C9396A, 0xBF0BE38F, 0xE10B66F2, 0x89455A71, 0xC7744C33
; ..\vss_code\vssconstant.c	   380  };
; ..\vss_code\vssconstant.c	   381  const vss_uint32 sm2_G_SM2_Gy[8] = {
; ..\vss_code\vssconstant.c	   382  	0xA23637BC, 0x9C77F6F4, 0xE3CEBD59, 0x5321696B, 0x7C87A9D0, 0x40472AC6, 0xE532DF02, 0xA0F03921
; ..\vss_code\vssconstant.c	   383  };
; ..\vss_code\vssconstant.c	   384  const vss_uint32 sm2_G_SM2_a[8] = {
; ..\vss_code\vssconstant.c	   385  	0xFEFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFCFFFFFF
; ..\vss_code\vssconstant.c	   386  };
; ..\vss_code\vssconstant.c	   387  const vss_uint32 sm2_G_SM2_b[8] = {
; ..\vss_code\vssconstant.c	   388  	0x9EFAE928, 0x345E9F9D, 0x4B9E5A4D, 0xA70965CF, 0xF58997F3, 0x928FAB15, 0x41BDBCDD, 0x930E944D
; ..\vss_code\vssconstant.c	   389  };
; ..\vss_code\vssconstant.c	   390  const vss_uint32 sm2_G_SM2_p[8] = {
; ..\vss_code\vssconstant.c	   391  	0xFEFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF
; ..\vss_code\vssconstant.c	   392  };
; ..\vss_code\vssconstant.c	   393  const vss_uint32 sm2_G_SM2_n[8] = {
; ..\vss_code\vssconstant.c	   394  	0xFEFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x6BDF0372, 0x2B05C621, 0x09F4BB53, 0x2341D539
; ..\vss_code\vssconstant.c	   395  };
; ..\vss_code\vssconstant.c	   396  #endif
; ..\vss_code\vssconstant.c	   397  const vss_uint8 sm2_pub_enc_rand[32] = {
; ..\vss_code\vssconstant.c	   398  	0x8A, 0x07, 0x7D, 0xB6, 0x1B, 0xA5, 0x3C, 0x67, 0xC0, 0x7A, 0x4A, 0x44, 0x6E, 0x7B, 0x95, 0x07,
; ..\vss_code\vssconstant.c	   399  	0x71, 0xC1, 0xEB, 0x4A, 0xCF, 0x22, 0x12, 0x2A, 0x54, 0xB4, 0x7D, 0x6A, 0x06, 0xFF, 0x62, 0xCC
; ..\vss_code\vssconstant.c	   400  };
; ..\vss_code\vssconstant.c	   401  
; ..\vss_code\vssconstant.c	   402  const vss_uint32 sm2_pri_dec_rand[8] = {
; ..\vss_code\vssconstant.c	   403  	0x2AEBC171, 0x2A1222CF, 0x6A7DB454, 0xCC62FF06, 0xB27D078A, 0x673CA51B, 0x444A7AC0, 0x00F57B6E
; ..\vss_code\vssconstant.c	   404  };
; ..\vss_code\vssconstant.c	   405  #endif
; ..\vss_code\vssconstant.c	   406  
; ..\vss_code\vssconstant.c	   407  #if (defined (_ENABLE_MIZAR_SM3_)&&(_ENABLE_MIZAR_SM3_ == 1U)) || \ 
; ..\vss_code\vssconstant.c	   408  	(defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
; ..\vss_code\vssconstant.c	   409  const SM3_WORD_T SM3_IV[8] = {
; ..\vss_code\vssconstant.c	   410  	0x7380166f, 0x4914b2b9, 0x172442d7, 0xda8a0600, 0xa96f30bc, 0x163138aa, 0xe38dee4d, 0xb0fb0e4e
; ..\vss_code\vssconstant.c	   411  };
; ..\vss_code\vssconstant.c	   412  #endif
; ..\vss_code\vssconstant.c	   413  
; ..\vss_code\vssconstant.c	   414  const vss_uint8 sm4_SboxTable[16][16] = {
; ..\vss_code\vssconstant.c	   415  	{0xd6, 0x90, 0xe9, 0xfe, 0xcc, 0xe1, 0x3d, 0xb7, 0x16, 0xb6, 0x14, 0xc2, 0x28, 0xfb, 0x2c, 0x05},
; ..\vss_code\vssconstant.c	   416  	{0x2b, 0x67, 0x9a, 0x76, 0x2a, 0xbe, 0x04, 0xc3, 0xaa, 0x44, 0x13, 0x26, 0x49, 0x86, 0x06, 0x99},
; ..\vss_code\vssconstant.c	   417  	{0x9c, 0x42, 0x50, 0xf4, 0x91, 0xef, 0x98, 0x7a, 0x33, 0x54, 0x0b, 0x43, 0xed, 0xcf, 0xac, 0x62},
; ..\vss_code\vssconstant.c	   418  	{0xe4, 0xb3, 0x1c, 0xa9, 0xc9, 0x08, 0xe8, 0x95, 0x80, 0xdf, 0x94, 0xfa, 0x75, 0x8f, 0x3f, 0xa6},
; ..\vss_code\vssconstant.c	   419  	{0x47, 0x07, 0xa7, 0xfc, 0xf3, 0x73, 0x17, 0xba, 0x83, 0x59, 0x3c, 0x19, 0xe6, 0x85, 0x4f, 0xa8},
; ..\vss_code\vssconstant.c	   420  	{0x68, 0x6b, 0x81, 0xb2, 0x71, 0x64, 0xda, 0x8b, 0xf8, 0xeb, 0x0f, 0x4b, 0x70, 0x56, 0x9d, 0x35},
; ..\vss_code\vssconstant.c	   421  	{0x1e, 0x24, 0x0e, 0x5e, 0x63, 0x58, 0xd1, 0xa2, 0x25, 0x22, 0x7c, 0x3b, 0x01, 0x21, 0x78, 0x87},
; ..\vss_code\vssconstant.c	   422  	{0xd4, 0x00, 0x46, 0x57, 0x9f, 0xd3, 0x27, 0x52, 0x4c, 0x36, 0x02, 0xe7, 0xa0, 0xc4, 0xc8, 0x9e},
; ..\vss_code\vssconstant.c	   423  	{0xea, 0xbf, 0x8a, 0xd2, 0x40, 0xc7, 0x38, 0xb5, 0xa3, 0xf7, 0xf2, 0xce, 0xf9, 0x61, 0x15, 0xa1},
; ..\vss_code\vssconstant.c	   424  	{0xe0, 0xae, 0x5d, 0xa4, 0x9b, 0x34, 0x1a, 0x55, 0xad, 0x93, 0x32, 0x30, 0xf5, 0x8c, 0xb1, 0xe3},
; ..\vss_code\vssconstant.c	   425  	{0x1d, 0xf6, 0xe2, 0x2e, 0x82, 0x66, 0xca, 0x60, 0xc0, 0x29, 0x23, 0xab, 0x0d, 0x53, 0x4e, 0x6f},
; ..\vss_code\vssconstant.c	   426  	{0xd5, 0xdb, 0x37, 0x45, 0xde, 0xfd, 0x8e, 0x2f, 0x03, 0xff, 0x6a, 0x72, 0x6d, 0x6c, 0x5b, 0x51},
; ..\vss_code\vssconstant.c	   427  	{0x8d, 0x1b, 0xaf, 0x92, 0xbb, 0xdd, 0xbc, 0x7f, 0x11, 0xd9, 0x5c, 0x41, 0x1f, 0x10, 0x5a, 0xd8},
; ..\vss_code\vssconstant.c	   428  	{0x0a, 0xc1, 0x31, 0x88, 0xa5, 0xcd, 0x7b, 0xbd, 0x2d, 0x74, 0xd0, 0x12, 0xb8, 0xe5, 0xb4, 0xb0},
; ..\vss_code\vssconstant.c	   429  	{0x89, 0x69, 0x97, 0x4a, 0x0c, 0x96, 0x77, 0x7e, 0x65, 0xb9, 0xf1, 0x09, 0xc5, 0x6e, 0xc6, 0x84},
; ..\vss_code\vssconstant.c	   430  	{0x18, 0xf0, 0x7d, 0xec, 0x3a, 0xdc, 0x4d, 0x20, 0x79, 0xee, 0x5f, 0x3e, 0xd7, 0xcb, 0x39, 0x48}
; ..\vss_code\vssconstant.c	   431  };
; ..\vss_code\vssconstant.c	   432  
; ..\vss_code\vssconstant.c	   433  const vss_ulong sm4_FK[4] = {
; ..\vss_code\vssconstant.c	   434  	0xa3b1bac6UL, 0x56aa3350UL, 0x677d9197UL, 0xb27022dcUL
; ..\vss_code\vssconstant.c	   435  };
; ..\vss_code\vssconstant.c	   436  
; ..\vss_code\vssconstant.c	   437  const vss_ulong sm4_CK[32] = {
; ..\vss_code\vssconstant.c	   438  	0x00070e15UL, 0x1c232a31UL, 0x383f464dUL, 0x545b6269UL,
; ..\vss_code\vssconstant.c	   439  	0x70777e85UL, 0x8c939aa1UL, 0xa8afb6bdUL, 0xc4cbd2d9UL,
; ..\vss_code\vssconstant.c	   440  	0xe0e7eef5UL, 0xfc030a11UL, 0x181f262dUL, 0x343b4249UL,
; ..\vss_code\vssconstant.c	   441  	0x50575e65UL, 0x6c737a81UL, 0x888f969dUL, 0xa4abb2b9UL,
; ..\vss_code\vssconstant.c	   442  	0xc0c7ced5UL, 0xdce3eaf1UL, 0xf8ff060dUL, 0x141b2229UL,
; ..\vss_code\vssconstant.c	   443  	0x30373e45UL, 0x4c535a61UL, 0x686f767dUL, 0x848b9299UL,
; ..\vss_code\vssconstant.c	   444  	0xa0a7aeb5UL, 0xbcc3cad1UL, 0xd8dfe6edUL, 0xf4fb0209UL,
; ..\vss_code\vssconstant.c	   445  	0x10171e25UL, 0x2c333a41UL, 0x484f565dUL, 0x646b7279UL
; ..\vss_code\vssconstant.c	   446  };
; ..\vss_code\vssconstant.c	   447  
; ..\vss_code\vssconstant.c	   448  const vss_uint8 vssapi_sm2_rand[32] = {
; ..\vss_code\vssconstant.c	   449  	0x06, 0xC1, 0xEB, 0x2A, 0xCF, 0x22, 0x12, 0x2A, 0x54, 0xB4, 0x7D, 0x6A, 0x06, 0xFF, 0x62, 0xCC, 
; ..\vss_code\vssconstant.c	   450  	0x8A, 0x07, 0x7D, 0xB2, 0x1B, 0xA5, 0x3C, 0x67, 0xC0, 0x7A, 0x4A, 0x44, 0x6E, 0x7B, 0xF5, 0x00
; ..\vss_code\vssconstant.c	   451  };
; ..\vss_code\vssconstant.c	   452  
; ..\vss_code\vssconstant.c	   453  const vss_uint8 vssapi_sm2_rand1[32] = {
; ..\vss_code\vssconstant.c	   454  	0x6C, 0xE3, 0xF2, 0xBB, 0x2E, 0xC5, 0x18, 0x6B, 0x66, 0x3B, 0x03, 0x69, 0x31, 0xB9, 0x50, 0xDE, 
; ..\vss_code\vssconstant.c	   455  	0xA8, 0x2B, 0x41, 0x10, 0x56, 0xB4, 0x9F, 0xA1, 0x08, 0x30, 0x6F, 0x7E, 0x43, 0x79, 0x41, 0x19
; ..\vss_code\vssconstant.c	   456  };
; ..\vss_code\vssconstant.c	   457  	
; ..\vss_code\vssconstant.c	   458  const vss_uint8 vssapi_SESS_KEY[16] = {
; ..\vss_code\vssconstant.c	   459  	0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f
; ..\vss_code\vssconstant.c	   460  };
; ..\vss_code\vssconstant.c	   461  
; ..\vss_code\vssconstant.c	   462  const vss_char8 vssapi_KEY[4] = "KEY";
; ..\vss_code\vssconstant.c	   463  const vss_char8 vssapi_SERVER[7] = "SERVER";
; ..\vss_code\vssconstant.c	   464  const vss_char8 vssapi_CLIENT[7] = "CLIENT";
; ..\vss_code\vssconstant.c	   465  
; ..\vss_code\vssconstant.c	   466  const vss_uint8 vsskeym_KEK[16]={
; ..\vss_code\vssconstant.c	   467  	0x6a, 0x43, 0x54, 0x43, 0x41, 0x4e, 0x44, 0x4b, 0x4f, 0x41, 0x4c, 0x49, 0x4e, 0x46, 0x49, 0x2f
; ..\vss_code\vssconstant.c	   468  };
; ..\vss_code\vssconstant.c	   469  
; ..\vss_code\vssconstant.c	   470  const vss_uint8 vsskeym_SECOC[16] = {
; ..\vss_code\vssconstant.c	   471  	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
; ..\vss_code\vssconstant.c	   472  };
; ..\vss_code\vssconstant.c	   473  
; ..\vss_code\vssconstant.c	   474  #pragma section all restore
; ..\vss_code\vssconstant.c	   475  
; ..\vss_code\vssconstant.c	   476  

	; Module end
