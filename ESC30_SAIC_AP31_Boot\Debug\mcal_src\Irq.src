	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc31560a -c99 --dep-file=mcal_src\\.Irq.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Irq.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Irq.src ..\\mcal_src\\Irq.c"
	.compiler_name		"ctc"
	.name	"Irq"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearAsclinIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	     1  /******************************************************************************
; ..\mcal_src\Irq.c	     2  **                                                                           **
; ..\mcal_src\Irq.c	     3  ** Copyright (C) Infineon Technologies (2014)                                **
; ..\mcal_src\Irq.c	     4  **                                                                           **
; ..\mcal_src\Irq.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Irq.c	     6  **                                                                           **
; ..\mcal_src\Irq.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Irq.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Irq.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Irq.c	    10  **                                                                           **
; ..\mcal_src\Irq.c	    11  *******************************************************************************
; ..\mcal_src\Irq.c	    12  **                                                                           **
; ..\mcal_src\Irq.c	    13  **  $FILENAME   : Irq.c $                                                    **
; ..\mcal_src\Irq.c	    14  **                                                                           **
; ..\mcal_src\Irq.c	    15  **  $CC VERSION : \main\dev_tc23x\16 $                                       **
; ..\mcal_src\Irq.c	    16  **                                                                           **
; ..\mcal_src\Irq.c	    17  **  $DATE       : 2016-03-30 $                                               **
; ..\mcal_src\Irq.c	    18  **                                                                           **
; ..\mcal_src\Irq.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Irq.c	    20  **                                                                           **
; ..\mcal_src\Irq.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Irq.c	    22  **                                                                           **
; ..\mcal_src\Irq.c	    23  **  DESCRIPTION : This file contains initisalization of interrupt priority   **
; ..\mcal_src\Irq.c	    24  **                and interruptframe based on interrupt category.            **
; ..\mcal_src\Irq.c	    25  **                                                                           **
; ..\mcal_src\Irq.c	    26  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\Irq.c	    27  **                                                                           **
; ..\mcal_src\Irq.c	    28  ******************************************************************************/
; ..\mcal_src\Irq.c	    29  
; ..\mcal_src\Irq.c	    30  /*******************************************************************************
; ..\mcal_src\Irq.c	    31  **                      Includes                                              **
; ..\mcal_src\Irq.c	    32  *******************************************************************************/
; ..\mcal_src\Irq.c	    33  
; ..\mcal_src\Irq.c	    34  /* Inclusion of Tasking sfr file */
; ..\mcal_src\Irq.c	    35  #include "IfxSrc_reg.h"
; ..\mcal_src\Irq.c	    36  
; ..\mcal_src\Irq.c	    37  /*Include Irq Module header file*/
; ..\mcal_src\Irq.c	    38  #include "Irq.h"
; ..\mcal_src\Irq.c	    39  
; ..\mcal_src\Irq.c	    40  /* Inclusion of Global Header File */
; ..\mcal_src\Irq.c	    41  #include "Mcal.h"
; ..\mcal_src\Irq.c	    42  
; ..\mcal_src\Irq.c	    43  #include "Mcal_Options.h"
; ..\mcal_src\Irq.c	    44  
; ..\mcal_src\Irq.c	    45  
; ..\mcal_src\Irq.c	    46  /*******************************************************************************
; ..\mcal_src\Irq.c	    47  **                      Private Type Definitions                              **
; ..\mcal_src\Irq.c	    48  *******************************************************************************/
; ..\mcal_src\Irq.c	    49  
; ..\mcal_src\Irq.c	    50  /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	    51  #define IRQ_DISABLE_CLEAR_SRC     (0x02000000U)
; ..\mcal_src\Irq.c	    52  
; ..\mcal_src\Irq.c	    53  /*******************************************************************************
; ..\mcal_src\Irq.c	    54  **                      Private Function Declarations                         **
; ..\mcal_src\Irq.c	    55  *******************************************************************************/
; ..\mcal_src\Irq.c	    56  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Irq.c	    57  #define IRQ_START_SEC_CODE
; ..\mcal_src\Irq.c	    58  #include "MemMap.h"
; ..\mcal_src\Irq.c	    59  #else
; ..\mcal_src\Irq.c	    60  #define IFX_IRQ_START_SEC_CODE_ASIL_B
; ..\mcal_src\Irq.c	    61  #include "Ifx_MemMap.h"
; ..\mcal_src\Irq.c	    62  #endif
; ..\mcal_src\Irq.c	    63  
; ..\mcal_src\Irq.c	    64  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Irq.c	    65  
; ..\mcal_src\Irq.c	    66  #if (IRQ_ASCLIN_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    67  static void Irq_ClearAsclinIntFlags(void);
; ..\mcal_src\Irq.c	    68  #endif
; ..\mcal_src\Irq.c	    69  
; ..\mcal_src\Irq.c	    70  #if (IRQ_CCU6_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    71  static void Irq_ClearCcu6IntFlags (void);
; ..\mcal_src\Irq.c	    72  #endif
; ..\mcal_src\Irq.c	    73  
; ..\mcal_src\Irq.c	    74  #if (IRQ_GPT_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    75  static void Irq_ClearGptIntFlags (void);
; ..\mcal_src\Irq.c	    76  #endif
; ..\mcal_src\Irq.c	    77  
; ..\mcal_src\Irq.c	    78  #if (IRQ_GTM_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    79  static void Irq_ClearGtmIntFlags (void);
; ..\mcal_src\Irq.c	    80  #endif
; ..\mcal_src\Irq.c	    81  
; ..\mcal_src\Irq.c	    82  #if (IRQ_CAN_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    83  static void Irq_ClearCanIntFlags (void);
; ..\mcal_src\Irq.c	    84  #endif
; ..\mcal_src\Irq.c	    85  
; ..\mcal_src\Irq.c	    86  #if (IRQ_GPSRGROUP_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    87  static void Irq_ClearGpsrGroupIntFlags (void);
; ..\mcal_src\Irq.c	    88  #endif
; ..\mcal_src\Irq.c	    89  
; ..\mcal_src\Irq.c	    90  #if (IRQ_QSPI_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    91  static void Irq_ClearSpiIntFlags (void);
; ..\mcal_src\Irq.c	    92  #endif
; ..\mcal_src\Irq.c	    93  
; ..\mcal_src\Irq.c	    94  #if (IRQ_ADC_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    95  static void Irq_ClearAdcIntFlags (void);
; ..\mcal_src\Irq.c	    96  #endif
; ..\mcal_src\Irq.c	    97  
; ..\mcal_src\Irq.c	    98  #if (IRQ_FLEXRAY_EXIST == STD_ON)
; ..\mcal_src\Irq.c	    99  static void Irq_ClearFlexrayIntFlags (void);
; ..\mcal_src\Irq.c	   100  #endif
; ..\mcal_src\Irq.c	   101  
; ..\mcal_src\Irq.c	   102  #if (IRQ_ETH_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   103  static void Irq_ClearEthernetIntFlags (void);
; ..\mcal_src\Irq.c	   104  #endif
; ..\mcal_src\Irq.c	   105  
; ..\mcal_src\Irq.c	   106  #if (IRQ_DMA_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   107  static void Irq_ClearDmaIntFlags (void);
; ..\mcal_src\Irq.c	   108  #endif
; ..\mcal_src\Irq.c	   109  
; ..\mcal_src\Irq.c	   110  #if (IRQ_STM_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   111  static void Irq_ClearStmIntFlags (void);
; ..\mcal_src\Irq.c	   112  #endif
; ..\mcal_src\Irq.c	   113  
; ..\mcal_src\Irq.c	   114  #if (IRQ_SCU_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   115  static void Irq_ClearScuIntFlags(void);
; ..\mcal_src\Irq.c	   116  #endif
; ..\mcal_src\Irq.c	   117  
; ..\mcal_src\Irq.c	   118  #if (IRQ_PMU0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   119  static void Irq_ClearPmuIntFlags (void);
; ..\mcal_src\Irq.c	   120  #endif
; ..\mcal_src\Irq.c	   121  
; ..\mcal_src\Irq.c	   122  #if (IRQ_SENT_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   123  static void Irq_ClearSentIntFlags (void);
; ..\mcal_src\Irq.c	   124  #endif
; ..\mcal_src\Irq.c	   125  
; ..\mcal_src\Irq.c	   126  #if ((IRQ_HSM0_EXIST == STD_ON) || (IRQ_HSM1_EXIST == STD_ON))
; ..\mcal_src\Irq.c	   127  static void Irq_ClearHsmIntFlags (void);
; ..\mcal_src\Irq.c	   128  #endif
; ..\mcal_src\Irq.c	   129  
; ..\mcal_src\Irq.c	   130  #endif /* (IFX_MCAL_USED == STD_ON) */
; ..\mcal_src\Irq.c	   131  
; ..\mcal_src\Irq.c	   132  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Irq.c	   133  #define IRQ_STOP_SEC_CODE
; ..\mcal_src\Irq.c	   134  #include "MemMap.h"
; ..\mcal_src\Irq.c	   135  #else
; ..\mcal_src\Irq.c	   136  #define IFX_IRQ_STOP_SEC_CODE_ASIL_B
; ..\mcal_src\Irq.c	   137  #include "Ifx_MemMap.h"
; ..\mcal_src\Irq.c	   138  #endif
; ..\mcal_src\Irq.c	   139  /*******************************************************************************
; ..\mcal_src\Irq.c	   140  **                      Global Constant Definitions                           **
; ..\mcal_src\Irq.c	   141  *******************************************************************************/
; ..\mcal_src\Irq.c	   142  
; ..\mcal_src\Irq.c	   143  /*******************************************************************************
; ..\mcal_src\Irq.c	   144  **                      Global Variable Definitions                           **
; ..\mcal_src\Irq.c	   145  *******************************************************************************/
; ..\mcal_src\Irq.c	   146  
; ..\mcal_src\Irq.c	   147  /*******************************************************************************
; ..\mcal_src\Irq.c	   148  **                      Private Constant Definitions                          **
; ..\mcal_src\Irq.c	   149  *******************************************************************************/
; ..\mcal_src\Irq.c	   150  
; ..\mcal_src\Irq.c	   151  /*******************************************************************************
; ..\mcal_src\Irq.c	   152  **                      Private Variable Definitions                          **
; ..\mcal_src\Irq.c	   153  *******************************************************************************/
; ..\mcal_src\Irq.c	   154  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Irq.c	   155  #define IRQ_START_SEC_CODE
; ..\mcal_src\Irq.c	   156  #include "MemMap.h"
; ..\mcal_src\Irq.c	   157  #else
; ..\mcal_src\Irq.c	   158  #define IFX_IRQ_START_SEC_CODE_ASIL_B
; ..\mcal_src\Irq.c	   159  #include "Ifx_MemMap.h"
; ..\mcal_src\Irq.c	   160  #endif
; ..\mcal_src\Irq.c	   161  /*******************************************************************************
; ..\mcal_src\Irq.c	   162  **                      Private Function Definitions                          **
; ..\mcal_src\Irq.c	   163  *******************************************************************************/
; ..\mcal_src\Irq.c	   164  
; ..\mcal_src\Irq.c	   165  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Irq.c	   166  
; ..\mcal_src\Irq.c	   167  #if (IRQ_ASCLIN_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   168  static void Irq_ClearAsclinIntFlags(void)
; Function Irq_ClearAsclinIntFlags
.L3:
Irq_ClearAsclinIntFlags:	.type	func

; ..\mcal_src\Irq.c	   169  {
; ..\mcal_src\Irq.c	   170    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   171    #if (IRQ_ASCLIN0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   172    SRC_ASCLIN0TX.U = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L577:
	st.w	[a15]@los(0xf0038080),d15
.L578:

; ..\mcal_src\Irq.c	   173    SRC_ASCLIN0RX.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038084),d15
.L579:

; ..\mcal_src\Irq.c	   174    SRC_ASCLIN0ERR.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038088),d15
.L580:

; ..\mcal_src\Irq.c	   175    #endif
; ..\mcal_src\Irq.c	   176  
; ..\mcal_src\Irq.c	   177    #if (IRQ_ASCLIN1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   178    SRC_ASCLIN1TX.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003808c),d15
.L581:

; ..\mcal_src\Irq.c	   179    SRC_ASCLIN1RX.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038090),d15
.L582:

; ..\mcal_src\Irq.c	   180    SRC_ASCLIN1ERR.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038094),d15
.L583:

; ..\mcal_src\Irq.c	   181    #endif
; ..\mcal_src\Irq.c	   182  
; ..\mcal_src\Irq.c	   183  }
	ret
.L392:
	
__Irq_ClearAsclinIntFlags_function_end:
	.size	Irq_ClearAsclinIntFlags,__Irq_ClearAsclinIntFlags_function_end-Irq_ClearAsclinIntFlags
.L149:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_1')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_1
.L5:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh	d15,#512
	movh.a	a15,#61444
.L712:
	fret
.L194:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearGptIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	   184  #endif
; ..\mcal_src\Irq.c	   185  
; ..\mcal_src\Irq.c	   186  #if (IRQ_CCU6_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   187  static void Irq_ClearCcu6IntFlags (void)
; ..\mcal_src\Irq.c	   188  {
; ..\mcal_src\Irq.c	   189    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   190    #if (IRQ_CCU60_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   191    SRC_CCU60SR0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   192    SRC_CCU60SR1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   193    SRC_CCU60SR2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   194    SRC_CCU60SR3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   195    #endif
; ..\mcal_src\Irq.c	   196  
; ..\mcal_src\Irq.c	   197    #if (IRQ_CCU61_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   198    SRC_CCU61SR0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   199    SRC_CCU61SR1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   200    SRC_CCU61SR2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   201    SRC_CCU61SR3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   202    #endif
; ..\mcal_src\Irq.c	   203  }
; ..\mcal_src\Irq.c	   204  #endif
; ..\mcal_src\Irq.c	   205  
; ..\mcal_src\Irq.c	   206  #if (IRQ_GPT_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   207  static void Irq_ClearGptIntFlags (void)
; Function Irq_ClearGptIntFlags
.L7:
Irq_ClearGptIntFlags:	.type	func

; ..\mcal_src\Irq.c	   208  {
; ..\mcal_src\Irq.c	   209    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   210    #if (IRQ_GPT120_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   211    SRC_GPT120CIRQ.U = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L588:
	st.w	[a15]@los(0xf0038460),d15
.L589:

; ..\mcal_src\Irq.c	   212    SRC_GPT120T2.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038464),d15
.L590:

; ..\mcal_src\Irq.c	   213    SRC_GPT120T3.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038468),d15
.L591:

; ..\mcal_src\Irq.c	   214    SRC_GPT120T4.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003846c),d15
.L592:

; ..\mcal_src\Irq.c	   215    SRC_GPT120T5.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038470),d15
.L593:

; ..\mcal_src\Irq.c	   216    SRC_GPT120T6.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038474),d15
.L594:

; ..\mcal_src\Irq.c	   217    #endif
; ..\mcal_src\Irq.c	   218  }
	ret
.L394:
	
__Irq_ClearGptIntFlags_function_end:
	.size	Irq_ClearGptIntFlags,__Irq_ClearGptIntFlags_function_end-Irq_ClearGptIntFlags
.L154:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearGtmIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	   219  #endif
; ..\mcal_src\Irq.c	   220  
; ..\mcal_src\Irq.c	   221  #if (IRQ_GTM_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   222  static void Irq_ClearGtmIntFlags (void)
; Function Irq_ClearGtmIntFlags
.L9:
Irq_ClearGtmIntFlags:	.type	func

; ..\mcal_src\Irq.c	   223  {
; ..\mcal_src\Irq.c	   224    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   225    #if (IRQ_GTM_AEI_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   226    SRC_GTMAEIIRQ.U = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L599:
	st.w	[a15]@los(0xf0039600),d15
.L600:

; ..\mcal_src\Irq.c	   227    #endif
; ..\mcal_src\Irq.c	   228  
; ..\mcal_src\Irq.c	   229    #if (IRQ_GTM_ERR_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   230    SRC_GTMERR.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039770),d15
.L601:

; ..\mcal_src\Irq.c	   231    #endif
; ..\mcal_src\Irq.c	   232  
; ..\mcal_src\Irq.c	   233    #if (IRQ_GTM_TIM0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   234    SRC_GTMTIM00.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039780),d15
.L602:

; ..\mcal_src\Irq.c	   235    SRC_GTMTIM01.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039784),d15
.L603:

; ..\mcal_src\Irq.c	   236    SRC_GTMTIM02.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039788),d15
.L604:

; ..\mcal_src\Irq.c	   237    SRC_GTMTIM03.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003978c),d15
.L605:

; ..\mcal_src\Irq.c	   238    SRC_GTMTIM04.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039790),d15
.L606:

; ..\mcal_src\Irq.c	   239    SRC_GTMTIM05.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039794),d15
.L607:

; ..\mcal_src\Irq.c	   240    SRC_GTMTIM06.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039798),d15
.L608:

; ..\mcal_src\Irq.c	   241    SRC_GTMTIM07.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003979c),d15
.L609:

; ..\mcal_src\Irq.c	   242    #endif
; ..\mcal_src\Irq.c	   243  
; ..\mcal_src\Irq.c	   244    #if (IRQ_GTM_TOM0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   245    SRC_GTMTOM00.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039b80),d15
.L610:

; ..\mcal_src\Irq.c	   246    SRC_GTMTOM01.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039b84),d15
.L611:

; ..\mcal_src\Irq.c	   247    SRC_GTMTOM02.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039b88),d15
.L612:

; ..\mcal_src\Irq.c	   248    SRC_GTMTOM03.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039b8c),d15
.L613:

; ..\mcal_src\Irq.c	   249    SRC_GTMTOM04.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039b90),d15
.L614:

; ..\mcal_src\Irq.c	   250    SRC_GTMTOM05.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039b94),d15
.L615:

; ..\mcal_src\Irq.c	   251    SRC_GTMTOM06.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039b98),d15
.L616:

; ..\mcal_src\Irq.c	   252    SRC_GTMTOM07.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039b9c),d15
.L617:

; ..\mcal_src\Irq.c	   253    #endif
; ..\mcal_src\Irq.c	   254  
; ..\mcal_src\Irq.c	   255    #if (IRQ_GTM_TOM1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   256    SRC_GTMTOM10.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039ba0),d15
.L618:

; ..\mcal_src\Irq.c	   257    SRC_GTMTOM11.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039ba4),d15
.L619:

; ..\mcal_src\Irq.c	   258    SRC_GTMTOM12.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039ba8),d15
.L620:

; ..\mcal_src\Irq.c	   259    SRC_GTMTOM13.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039bac),d15
.L621:

; ..\mcal_src\Irq.c	   260    SRC_GTMTOM14.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039bb0),d15
.L622:

; ..\mcal_src\Irq.c	   261    SRC_GTMTOM15.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039bb4),d15
.L623:

; ..\mcal_src\Irq.c	   262    SRC_GTMTOM16.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039bb8),d15
.L624:

; ..\mcal_src\Irq.c	   263    SRC_GTMTOM17.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0039bbc),d15
.L625:

; ..\mcal_src\Irq.c	   264    #endif
; ..\mcal_src\Irq.c	   265  
; ..\mcal_src\Irq.c	   266  }
	ret
.L395:
	
__Irq_ClearGtmIntFlags_function_end:
	.size	Irq_ClearGtmIntFlags,__Irq_ClearGtmIntFlags_function_end-Irq_ClearGtmIntFlags
.L159:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearGpsrGroupIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	   267  #endif
; ..\mcal_src\Irq.c	   268  
; ..\mcal_src\Irq.c	   269  #if (IRQ_CAN_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   270  static void Irq_ClearCanIntFlags (void)
; ..\mcal_src\Irq.c	   271  {
; ..\mcal_src\Irq.c	   272    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   273    #if (IRQ_CAN0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   274    SRC_CANINT0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   275    #endif
; ..\mcal_src\Irq.c	   276  
; ..\mcal_src\Irq.c	   277    #if (IRQ_CAN1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   278    SRC_CANINT1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   279    #endif
; ..\mcal_src\Irq.c	   280  
; ..\mcal_src\Irq.c	   281    #if (IRQ_CAN2_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   282    SRC_CANINT2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   283    #endif
; ..\mcal_src\Irq.c	   284  
; ..\mcal_src\Irq.c	   285    #if (IRQ_CAN3_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   286    SRC_CANINT3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   287    #endif
; ..\mcal_src\Irq.c	   288  
; ..\mcal_src\Irq.c	   289    #if (IRQ_CAN4_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   290    SRC_CANINT4.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   291    #endif
; ..\mcal_src\Irq.c	   292  
; ..\mcal_src\Irq.c	   293    #if (IRQ_CAN5_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   294    SRC_CANINT5.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   295    #endif
; ..\mcal_src\Irq.c	   296  
; ..\mcal_src\Irq.c	   297    #if (IRQ_CAN6_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   298    SRC_CANINT6.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   299    #endif
; ..\mcal_src\Irq.c	   300  
; ..\mcal_src\Irq.c	   301    #if (IRQ_CAN7_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   302    SRC_CANINT7.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   303    #endif
; ..\mcal_src\Irq.c	   304  
; ..\mcal_src\Irq.c	   305    #if (IRQ_CAN8_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   306    SRC_CANINT8.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   307    #endif
; ..\mcal_src\Irq.c	   308  
; ..\mcal_src\Irq.c	   309    #if (IRQ_CAN9_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   310    SRC_CANINT9.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   311    #endif
; ..\mcal_src\Irq.c	   312  
; ..\mcal_src\Irq.c	   313    #if (IRQ_CAN10_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   314    SRC_CANINT10.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   315    #endif
; ..\mcal_src\Irq.c	   316  
; ..\mcal_src\Irq.c	   317    #if (IRQ_CAN11_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   318    SRC_CANINT11.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   319    #endif
; ..\mcal_src\Irq.c	   320  
; ..\mcal_src\Irq.c	   321    #if (IRQ_CAN12_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   322    SRC_CANINT12.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   323    #endif
; ..\mcal_src\Irq.c	   324  
; ..\mcal_src\Irq.c	   325    #if (IRQ_CAN13_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   326    SRC_CANINT13.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   327    #endif
; ..\mcal_src\Irq.c	   328  
; ..\mcal_src\Irq.c	   329    #if (IRQ_CAN14_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   330    SRC_CANINT14.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   331    #endif
; ..\mcal_src\Irq.c	   332  
; ..\mcal_src\Irq.c	   333    #if (IRQ_CAN15_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   334    SRC_CANINT15.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   335    #endif
; ..\mcal_src\Irq.c	   336  
; ..\mcal_src\Irq.c	   337    #if (IRQ_CAN16_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   338    SRC_CAN1INT0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   339    #endif
; ..\mcal_src\Irq.c	   340  
; ..\mcal_src\Irq.c	   341    #if (IRQ_CAN17_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   342    SRC_CAN1INT1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   343    #endif
; ..\mcal_src\Irq.c	   344  
; ..\mcal_src\Irq.c	   345    #if (IRQ_CAN18_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   346    SRC_CAN1INT2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   347    #endif
; ..\mcal_src\Irq.c	   348  
; ..\mcal_src\Irq.c	   349    #if (IRQ_CAN19_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   350    SRC_CAN1INT3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   351    #endif
; ..\mcal_src\Irq.c	   352  
; ..\mcal_src\Irq.c	   353    #if (IRQ_CAN20_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   354    SRC_CAN1INT4.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   355    #endif
; ..\mcal_src\Irq.c	   356  
; ..\mcal_src\Irq.c	   357    #if (IRQ_CAN21_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   358    SRC_CAN1INT5.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   359    #endif
; ..\mcal_src\Irq.c	   360  
; ..\mcal_src\Irq.c	   361    #if (IRQ_CAN22_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   362    SRC_CAN1INT6.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   363    #endif
; ..\mcal_src\Irq.c	   364  
; ..\mcal_src\Irq.c	   365    #if (IRQ_CAN23_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   366    SRC_CAN1INT7.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   367    #endif
; ..\mcal_src\Irq.c	   368  
; ..\mcal_src\Irq.c	   369  }
; ..\mcal_src\Irq.c	   370  #endif
; ..\mcal_src\Irq.c	   371  
; ..\mcal_src\Irq.c	   372  #if (IRQ_GPSRGROUP_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   373  static void Irq_ClearGpsrGroupIntFlags (void)
; Function Irq_ClearGpsrGroupIntFlags
.L11:
Irq_ClearGpsrGroupIntFlags:	.type	func

; ..\mcal_src\Irq.c	   374  {
; ..\mcal_src\Irq.c	   375    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   376    #if (IRQ_GPSRGROUP0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   377    SRC_GPSR00.U = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L630:
	st.w	[a15]@los(0xf0039000),d15
.L631:

; ..\mcal_src\Irq.c	   378    #endif
; ..\mcal_src\Irq.c	   379  }
	ret
.L396:
	
__Irq_ClearGpsrGroupIntFlags_function_end:
	.size	Irq_ClearGpsrGroupIntFlags,__Irq_ClearGpsrGroupIntFlags_function_end-Irq_ClearGpsrGroupIntFlags
.L164:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearSpiIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	   380  #endif
; ..\mcal_src\Irq.c	   381  
; ..\mcal_src\Irq.c	   382  #if (IRQ_QSPI_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   383  static void Irq_ClearSpiIntFlags (void)
; Function Irq_ClearSpiIntFlags
.L13:
Irq_ClearSpiIntFlags:	.type	func

; ..\mcal_src\Irq.c	   384  {
; ..\mcal_src\Irq.c	   385    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   386    #if (IRQ_QSPI0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   387    SRC_QSPI0TX.U  = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L636:
	st.w	[a15]@los(0xf0038190),d15
.L637:

; ..\mcal_src\Irq.c	   388    SRC_QSPI0RX.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038194),d15
.L638:

; ..\mcal_src\Irq.c	   389    SRC_QSPI0ERR.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038198),d15
.L639:

; ..\mcal_src\Irq.c	   390    SRC_QSPI0PT.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003819c),d15
.L640:

; ..\mcal_src\Irq.c	   391    SRC_QSPI0U.U   = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381a4),d15
.L641:

; ..\mcal_src\Irq.c	   392    #endif
; ..\mcal_src\Irq.c	   393  
; ..\mcal_src\Irq.c	   394    #if (IRQ_QSPI1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   395    SRC_QSPI1TX.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381a8),d15
.L642:

; ..\mcal_src\Irq.c	   396    SRC_QSPI1RX.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381ac),d15
.L643:

; ..\mcal_src\Irq.c	   397    SRC_QSPI1ERR.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381b0),d15
.L644:

; ..\mcal_src\Irq.c	   398    SRC_QSPI1PT.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381b4),d15
.L645:

; ..\mcal_src\Irq.c	   399    SRC_QSPI1U.U   = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381bc),d15
.L646:

; ..\mcal_src\Irq.c	   400    #endif
; ..\mcal_src\Irq.c	   401  
; ..\mcal_src\Irq.c	   402    #if (IRQ_QSPI2_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   403    SRC_QSPI2TX.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381c0),d15
.L647:

; ..\mcal_src\Irq.c	   404    SRC_QSPI2RX.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381c4),d15
.L648:

; ..\mcal_src\Irq.c	   405    SRC_QSPI2ERR.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381c8),d15
.L649:

; ..\mcal_src\Irq.c	   406    SRC_QSPI2PT.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381cc),d15
.L650:

; ..\mcal_src\Irq.c	   407    SRC_QSPI2HC.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381d0),d15
.L651:

; ..\mcal_src\Irq.c	   408    SRC_QSPI2U.U   = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381d4),d15
.L652:

; ..\mcal_src\Irq.c	   409    #endif
; ..\mcal_src\Irq.c	   410  
; ..\mcal_src\Irq.c	   411    #if (IRQ_QSPI3_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   412    SRC_QSPI3TX.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381d8),d15
.L653:

; ..\mcal_src\Irq.c	   413    SRC_QSPI3RX.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381dc),d15
.L654:

; ..\mcal_src\Irq.c	   414    SRC_QSPI3ERR.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381e0),d15
.L655:

; ..\mcal_src\Irq.c	   415    SRC_QSPI3PT.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381e4),d15
.L656:

; ..\mcal_src\Irq.c	   416    SRC_QSPI3HC.U  = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381e8),d15
.L657:

; ..\mcal_src\Irq.c	   417    SRC_QSPI3U.U   = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf00381ec),d15
.L658:

; ..\mcal_src\Irq.c	   418    #endif
; ..\mcal_src\Irq.c	   419  
; ..\mcal_src\Irq.c	   420    #if (IRQ_QSPI4_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   421    SRC_QSPI4TX.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   422    SRC_QSPI4RX.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   423    SRC_QSPI4ERR.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   424    SRC_QSPI4PT.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   425    SRC_QSPI4HC.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   426    SRC_QSPI4U.U   = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   427    #endif
; ..\mcal_src\Irq.c	   428  
; ..\mcal_src\Irq.c	   429    #if (IRQ_QSPI5_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   430    SRC_QSPI5TX.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   431    SRC_QSPI5RX.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   432    SRC_QSPI5ERR.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   433    SRC_QSPI5PT.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   434    SRC_QSPI5HC.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   435    SRC_QSPI5U.U   = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   436    #endif
; ..\mcal_src\Irq.c	   437  
; ..\mcal_src\Irq.c	   438  }
	ret
.L397:
	
__Irq_ClearSpiIntFlags_function_end:
	.size	Irq_ClearSpiIntFlags,__Irq_ClearSpiIntFlags_function_end-Irq_ClearSpiIntFlags
.L169:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearAdcIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	   439  #endif
; ..\mcal_src\Irq.c	   440  
; ..\mcal_src\Irq.c	   441  #if (IRQ_ADC_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   442  static void Irq_ClearAdcIntFlags (void)
; Function Irq_ClearAdcIntFlags
.L15:
Irq_ClearAdcIntFlags:	.type	func

; ..\mcal_src\Irq.c	   443  {
; ..\mcal_src\Irq.c	   444    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   445    #if (IRQ_ADC0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   446    SRC_VADCG0SR0.U = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L663:
	st.w	[a15]@los(0xf0038980),d15
.L664:

; ..\mcal_src\Irq.c	   447    SRC_VADCG0SR1.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038984),d15
.L665:

; ..\mcal_src\Irq.c	   448    SRC_VADCG0SR2.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038988),d15
.L666:

; ..\mcal_src\Irq.c	   449    SRC_VADCG0SR3.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003898c),d15
.L667:

; ..\mcal_src\Irq.c	   450    #endif
; ..\mcal_src\Irq.c	   451  
; ..\mcal_src\Irq.c	   452    #if (IRQ_ADC1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   453    SRC_VADCG1SR0.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038990),d15
.L668:

; ..\mcal_src\Irq.c	   454    SRC_VADCG1SR1.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038994),d15
.L669:

; ..\mcal_src\Irq.c	   455    SRC_VADCG1SR2.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038998),d15
.L670:

; ..\mcal_src\Irq.c	   456    SRC_VADCG1SR3.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003899c),d15
.L671:

; ..\mcal_src\Irq.c	   457    #endif
; ..\mcal_src\Irq.c	   458  
; ..\mcal_src\Irq.c	   459    #if (IRQ_ADC2_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   460    SRC_VADCG2SR0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   461    SRC_VADCG2SR1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   462    SRC_VADCG2SR2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   463    SRC_VADCG2SR3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   464    #endif
; ..\mcal_src\Irq.c	   465  
; ..\mcal_src\Irq.c	   466    #if (IRQ_ADC3_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   467    SRC_VADCG3SR0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   468    SRC_VADCG3SR1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   469    SRC_VADCG3SR2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   470    SRC_VADCG3SR3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   471    #endif
; ..\mcal_src\Irq.c	   472  
; ..\mcal_src\Irq.c	   473    #if (IRQ_ADCCG0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   474    SRC_VADCCG0SR0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   475    SRC_VADCCG0SR1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   476    SRC_VADCCG0SR2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   477    SRC_VADCCG0SR3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   478    #endif
; ..\mcal_src\Irq.c	   479  
; ..\mcal_src\Irq.c	   480  }
	ret
.L398:
	
__Irq_ClearAdcIntFlags_function_end:
	.size	Irq_ClearAdcIntFlags,__Irq_ClearAdcIntFlags_function_end-Irq_ClearAdcIntFlags
.L174:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearDmaIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	   481  #endif
; ..\mcal_src\Irq.c	   482  
; ..\mcal_src\Irq.c	   483  #if (IRQ_FLEXRAY_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   484  static void Irq_ClearFlexrayIntFlags (void)
; ..\mcal_src\Irq.c	   485  {
; ..\mcal_src\Irq.c	   486    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   487    #if (IRQ_FLEXRAY0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   488    SRC_ERAY_ERAY0_INT0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   489    SRC_ERAY_ERAY0_INT1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   490    SRC_ERAY_ERAY0_TINT0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   491    SRC_ERAY_ERAY0_TINT1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   492    SRC_ERAY_ERAY0_NDAT0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   493    SRC_ERAY_ERAY0_NDAT1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   494    SRC_ERAY_ERAY0_MBSC0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   495    SRC_ERAY_ERAY0_MBSC1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   496    SRC_ERAY_ERAY0_OBUSY.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   497    SRC_ERAY_ERAY0_IBUSY.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   498    #endif
; ..\mcal_src\Irq.c	   499  }
; ..\mcal_src\Irq.c	   500  #endif
; ..\mcal_src\Irq.c	   501  
; ..\mcal_src\Irq.c	   502  #if (IRQ_ETH_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   503  static void Irq_ClearEthernetIntFlags (void)
; ..\mcal_src\Irq.c	   504  {
; ..\mcal_src\Irq.c	   505    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   506    SRC_ETH.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   507  }
; ..\mcal_src\Irq.c	   508  #endif
; ..\mcal_src\Irq.c	   509  
; ..\mcal_src\Irq.c	   510  #if (IRQ_DMA_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   511  static void Irq_ClearDmaIntFlags (void)
; Function Irq_ClearDmaIntFlags
.L17:
Irq_ClearDmaIntFlags:	.type	func

; ..\mcal_src\Irq.c	   512  {
; ..\mcal_src\Irq.c	   513    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   514    SRC_DMAERR.U = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L676:
	st.w	[a15]@los(0xf00384f0),d15
.L677:

; ..\mcal_src\Irq.c	   515  
; ..\mcal_src\Irq.c	   516    #if (IRQ_DMA_CH0TO15_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   517    SRC_DMACH0.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038500),d15
.L678:

; ..\mcal_src\Irq.c	   518    SRC_DMACH1.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038504),d15
.L679:

; ..\mcal_src\Irq.c	   519    SRC_DMACH2.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038508),d15
.L680:

; ..\mcal_src\Irq.c	   520    SRC_DMACH3.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003850c),d15
.L681:

; ..\mcal_src\Irq.c	   521    SRC_DMACH4.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038510),d15
.L682:

; ..\mcal_src\Irq.c	   522    SRC_DMACH5.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038514),d15
.L683:

; ..\mcal_src\Irq.c	   523    SRC_DMACH6.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038518),d15
.L684:

; ..\mcal_src\Irq.c	   524    SRC_DMACH7.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003851c),d15
.L685:

; ..\mcal_src\Irq.c	   525    SRC_DMACH8.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038520),d15
.L686:

; ..\mcal_src\Irq.c	   526    SRC_DMACH9.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038524),d15
.L687:

; ..\mcal_src\Irq.c	   527    SRC_DMACH10.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038528),d15
.L688:

; ..\mcal_src\Irq.c	   528    SRC_DMACH11.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003852c),d15
.L689:

; ..\mcal_src\Irq.c	   529    SRC_DMACH12.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038530),d15
.L690:

; ..\mcal_src\Irq.c	   530    SRC_DMACH13.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038534),d15
.L691:

; ..\mcal_src\Irq.c	   531    SRC_DMACH14.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038538),d15
.L692:

; ..\mcal_src\Irq.c	   532    SRC_DMACH15.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf003853c),d15
.L693:

; ..\mcal_src\Irq.c	   533    #endif
; ..\mcal_src\Irq.c	   534  
; ..\mcal_src\Irq.c	   535  }
	ret
.L399:
	
__Irq_ClearDmaIntFlags_function_end:
	.size	Irq_ClearDmaIntFlags,__Irq_ClearDmaIntFlags_function_end-Irq_ClearDmaIntFlags
.L179:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearStmIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	   536  #endif
; ..\mcal_src\Irq.c	   537  
; ..\mcal_src\Irq.c	   538  #if (IRQ_STM_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   539  static void Irq_ClearStmIntFlags (void)
; Function Irq_ClearStmIntFlags
.L19:
Irq_ClearStmIntFlags:	.type	func

; ..\mcal_src\Irq.c	   540  {
; ..\mcal_src\Irq.c	   541    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   542    #if (IRQ_STM0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   543    SRC_STM0SR0.U = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L698:
	st.w	[a15]@los(0xf0038490),d15
.L699:

; ..\mcal_src\Irq.c	   544    SRC_STM0SR1.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038494),d15
.L700:

; ..\mcal_src\Irq.c	   545    #endif
; ..\mcal_src\Irq.c	   546  
; ..\mcal_src\Irq.c	   547  }
	ret
.L400:
	
__Irq_ClearStmIntFlags_function_end:
	.size	Irq_ClearStmIntFlags,__Irq_ClearStmIntFlags_function_end-Irq_ClearStmIntFlags
.L184:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearHsmIntFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Irq.c	   548  #endif
; ..\mcal_src\Irq.c	   549  
; ..\mcal_src\Irq.c	   550  #if (IRQ_SCU_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   551  static void Irq_ClearScuIntFlags (void)
; ..\mcal_src\Irq.c	   552  {
; ..\mcal_src\Irq.c	   553    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   554    SRC_SCUDTS.U  = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   555    SRC_SCUERU0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   556    SRC_SCUERU1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   557    SRC_SCUERU2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   558    SRC_SCUERU3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   559  }
; ..\mcal_src\Irq.c	   560  #endif
; ..\mcal_src\Irq.c	   561  
; ..\mcal_src\Irq.c	   562  #if (IRQ_PMU0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   563  static void Irq_ClearPmuIntFlags (void)
; ..\mcal_src\Irq.c	   564  {
; ..\mcal_src\Irq.c	   565    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   566    #if (IRQ_PMU0_SR0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   567    SRC_PMU00.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   568    #endif
; ..\mcal_src\Irq.c	   569  
; ..\mcal_src\Irq.c	   570    #if (IRQ_PMU0_SR1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   571    SRC_PMU01.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   572    #endif
; ..\mcal_src\Irq.c	   573  }
; ..\mcal_src\Irq.c	   574  #endif
; ..\mcal_src\Irq.c	   575  
; ..\mcal_src\Irq.c	   576  #if (IRQ_SENT_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   577  static void Irq_ClearSentIntFlags (void)
; ..\mcal_src\Irq.c	   578  {
; ..\mcal_src\Irq.c	   579    /* Set CLRR to clear SRR bit and disable SRE bit */
; ..\mcal_src\Irq.c	   580    #if (IRQ_SENT0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   581    SRC_SENT0.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   582    #endif
; ..\mcal_src\Irq.c	   583    #if (IRQ_SENT1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   584    SRC_SENT1.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   585    #endif
; ..\mcal_src\Irq.c	   586    #if (IRQ_SENT2_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   587    SRC_SENT2.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   588    #endif
; ..\mcal_src\Irq.c	   589    #if (IRQ_SENT3_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   590    SRC_SENT3.U = IRQ_DISABLE_CLEAR_SRC;
; ..\mcal_src\Irq.c	   591    #endif
; ..\mcal_src\Irq.c	   592  }
; ..\mcal_src\Irq.c	   593  #endif
; ..\mcal_src\Irq.c	   594  
; ..\mcal_src\Irq.c	   595  #if ((IRQ_HSM0_EXIST == STD_ON) || (IRQ_HSM1_EXIST == STD_ON))
; ..\mcal_src\Irq.c	   596  static void Irq_ClearHsmIntFlags (void)
; Function Irq_ClearHsmIntFlags
.L21:
Irq_ClearHsmIntFlags:	.type	func

; ..\mcal_src\Irq.c	   597  {
; ..\mcal_src\Irq.c	   598    #if (IRQ_HSM0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   599    SRC_HSM0.U = IRQ_DISABLE_CLEAR_SRC;
	fcall	.cocofun_1
.L705:
	st.w	[a15]@los(0xf0038cc0),d15
.L706:

; ..\mcal_src\Irq.c	   600    #endif
; ..\mcal_src\Irq.c	   601    #if (IRQ_HSM1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   602    SRC_HSM1.U = IRQ_DISABLE_CLEAR_SRC;
	st.w	[a15]@los(0xf0038cc4),d15
.L707:

; ..\mcal_src\Irq.c	   603    #endif
; ..\mcal_src\Irq.c	   604  }
	ret
.L401:
	
__Irq_ClearHsmIntFlags_function_end:
	.size	Irq_ClearHsmIntFlags,__Irq_ClearHsmIntFlags_function_end-Irq_ClearHsmIntFlags
.L189:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqAscLin_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqAscLin_Init

; ..\mcal_src\Irq.c	   605  #endif
; ..\mcal_src\Irq.c	   606  
; ..\mcal_src\Irq.c	   607  #endif /*(IFX_MCAL_USED == STD_ON) */
; ..\mcal_src\Irq.c	   608  
; ..\mcal_src\Irq.c	   609  
; ..\mcal_src\Irq.c	   610  /*******************************************************************************
; ..\mcal_src\Irq.c	   611  **                      Global Function Definitions                           **
; ..\mcal_src\Irq.c	   612  *******************************************************************************/
; ..\mcal_src\Irq.c	   613  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Irq.c	   614  /*******************************************************************************
; ..\mcal_src\Irq.c	   615  ** Syntax :  void IrqAscLin_Init(void)                                        **
; ..\mcal_src\Irq.c	   616  **                                                                            **
; ..\mcal_src\Irq.c	   617  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	   618  **                                                                            **
; ..\mcal_src\Irq.c	   619  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	   620  **                                                                            **
; ..\mcal_src\Irq.c	   621  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	   622  **                                                                            **
; ..\mcal_src\Irq.c	   623  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	   624  **                                                                            **
; ..\mcal_src\Irq.c	   625  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	   626  **                                                                            **
; ..\mcal_src\Irq.c	   627  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	   628  **                                                                            **
; ..\mcal_src\Irq.c	   629  ** Description : To set the interrupt priority for various                    **
; ..\mcal_src\Irq.c	   630  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	   631  **                                                                            **
; ..\mcal_src\Irq.c	   632  *******************************************************************************/
; ..\mcal_src\Irq.c	   633  
; ..\mcal_src\Irq.c	   634  void IrqAscLin_Init(void)
; Function IrqAscLin_Init
.L23:
IrqAscLin_Init:	.type	func

; ..\mcal_src\Irq.c	   635  {
; ..\mcal_src\Irq.c	   636    #if (IRQ_ASCLIN_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   637  
; ..\mcal_src\Irq.c	   638    #if (IRQ_ASCLIN0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   639    /* Reserve bit access is ensured for SRC register at all places accessed*/
; ..\mcal_src\Irq.c	   640    IRQ_SFR_MODIFY32(SRC_ASCLIN0TX.U, IRQ_CLEAR_MASK , \ 
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf0038080)
.L402:
	st.w	[a15]@los(0xf0038080),d15
.L196:

; ..\mcal_src\Irq.c	   641                                     (IRQ_ASCLIN0_TX_TOS | IRQ_ASCLIN0_TX_PRIO));
; ..\mcal_src\Irq.c	   642    IRQ_SFR_MODIFY32(SRC_ASCLIN0RX.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf0038084)
.L403:
	st.w	[a15]@los(0xf0038084),d15
.L199:

; ..\mcal_src\Irq.c	   643                                     (IRQ_ASCLIN0_RX_TOS | IRQ_ASCLIN0_RX_PRIO));
; ..\mcal_src\Irq.c	   644    IRQ_SFR_MODIFY32(SRC_ASCLIN0ERR.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf0038088)
.L404:
	st.w	[a15]@los(0xf0038088),d15
.L201:

; ..\mcal_src\Irq.c	   645                                    (IRQ_ASCLIN0_ERR_TOS | IRQ_ASCLIN0_ERR_PRIO));
; ..\mcal_src\Irq.c	   646    #endif
; ..\mcal_src\Irq.c	   647  
; ..\mcal_src\Irq.c	   648    #if (IRQ_ASCLIN1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   649    IRQ_SFR_MODIFY32(SRC_ASCLIN1TX.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf003808c)
.L405:
	st.w	[a15]@los(0xf003808c),d15
.L203:

; ..\mcal_src\Irq.c	   650                                      (IRQ_ASCLIN1_TX_TOS | IRQ_ASCLIN1_TX_PRIO));
; ..\mcal_src\Irq.c	   651    IRQ_SFR_MODIFY32(SRC_ASCLIN1RX.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf0038090)
.L406:
	st.w	[a15]@los(0xf0038090),d15
.L205:

; ..\mcal_src\Irq.c	   652                                      (IRQ_ASCLIN1_RX_TOS | IRQ_ASCLIN1_RX_PRIO));
; ..\mcal_src\Irq.c	   653    IRQ_SFR_MODIFY32(SRC_ASCLIN1ERR.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038094)
.L407:
	st.w	[a15]@los(0xf0038094),d15
.L207:

; ..\mcal_src\Irq.c	   654                                    (IRQ_ASCLIN1_ERR_TOS | IRQ_ASCLIN1_ERR_PRIO));
; ..\mcal_src\Irq.c	   655    #endif
; ..\mcal_src\Irq.c	   656  
; ..\mcal_src\Irq.c	   657    #endif
; ..\mcal_src\Irq.c	   658  
; ..\mcal_src\Irq.c	   659  }
	ret
.L195:
	
__IrqAscLin_Init_function_end:
	.size	IrqAscLin_Init,__IrqAscLin_Init_function_end-IrqAscLin_Init
.L64:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqCcu6_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqCcu6_Init

; ..\mcal_src\Irq.c	   660  
; ..\mcal_src\Irq.c	   661  /*******************************************************************************
; ..\mcal_src\Irq.c	   662  ** Syntax :  void IrqCcu6_Init(void)                                          **
; ..\mcal_src\Irq.c	   663  **                                                                            **
; ..\mcal_src\Irq.c	   664  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	   665  **                                                                            **
; ..\mcal_src\Irq.c	   666  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	   667  **                                                                            **
; ..\mcal_src\Irq.c	   668  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	   669  **                                                                            **
; ..\mcal_src\Irq.c	   670  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	   671  **                                                                            **
; ..\mcal_src\Irq.c	   672  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	   673  **                                                                            **
; ..\mcal_src\Irq.c	   674  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	   675  **                                                                            **
; ..\mcal_src\Irq.c	   676  ** Description : To set the interrupt priority for various                    **
; ..\mcal_src\Irq.c	   677  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	   678  **                                                                            **
; ..\mcal_src\Irq.c	   679  *******************************************************************************/
; ..\mcal_src\Irq.c	   680  
; ..\mcal_src\Irq.c	   681  void IrqCcu6_Init(void)
; Function IrqCcu6_Init
.L25:
IrqCcu6_Init:	.type	func

; ..\mcal_src\Irq.c	   682  {
; ..\mcal_src\Irq.c	   683    #if (IRQ_CCU6_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   684    #if (IRQ_CCU60_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   685    IRQ_SFR_MODIFY32(SRC_CCU60SR0.U,IRQ_CLEAR_MASK, \ 
; ..\mcal_src\Irq.c	   686                                        (IRQ_CCU60_SR0_TOS | IRQ_CCU60_SR0_PRIO));
; ..\mcal_src\Irq.c	   687    IRQ_SFR_MODIFY32(SRC_CCU60SR1.U,IRQ_CLEAR_MASK, \ 
; ..\mcal_src\Irq.c	   688                                        (IRQ_CCU60_SR1_TOS | IRQ_CCU60_SR1_PRIO));
; ..\mcal_src\Irq.c	   689    IRQ_SFR_MODIFY32(SRC_CCU60SR2.U,IRQ_CLEAR_MASK, \ 
; ..\mcal_src\Irq.c	   690                                        (IRQ_CCU60_SR2_TOS | IRQ_CCU60_SR2_PRIO));
; ..\mcal_src\Irq.c	   691    IRQ_SFR_MODIFY32(SRC_CCU60SR3.U,IRQ_CLEAR_MASK,  \ 
; ..\mcal_src\Irq.c	   692                                        (IRQ_CCU60_SR3_TOS | IRQ_CCU60_SR3_PRIO));
; ..\mcal_src\Irq.c	   693    #endif
; ..\mcal_src\Irq.c	   694  
; ..\mcal_src\Irq.c	   695    #if (IRQ_CCU61_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   696    IRQ_SFR_MODIFY32(SRC_CCU61SR0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   697                                       (IRQ_CCU61_SR0_TOS | IRQ_CCU61_SR0_PRIO));
; ..\mcal_src\Irq.c	   698    IRQ_SFR_MODIFY32(SRC_CCU61SR1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   699                                       (IRQ_CCU61_SR1_TOS | IRQ_CCU61_SR1_PRIO));
; ..\mcal_src\Irq.c	   700    IRQ_SFR_MODIFY32(SRC_CCU61SR2.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   701                                       (IRQ_CCU61_SR2_TOS | IRQ_CCU61_SR2_PRIO));
; ..\mcal_src\Irq.c	   702    IRQ_SFR_MODIFY32(SRC_CCU61SR3.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   703                                       (IRQ_CCU61_SR3_TOS | IRQ_CCU61_SR3_PRIO));
; ..\mcal_src\Irq.c	   704    #endif
; ..\mcal_src\Irq.c	   705  
; ..\mcal_src\Irq.c	   706    #endif
; ..\mcal_src\Irq.c	   707  }
	ret
.L209:
	
__IrqCcu6_Init_function_end:
	.size	IrqCcu6_Init,__IrqCcu6_Init_function_end-IrqCcu6_Init
.L69:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqGpt_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqGpt_Init

; ..\mcal_src\Irq.c	   708  /*******************************************************************************
; ..\mcal_src\Irq.c	   709  ** Syntax :  void IrqGpt_Init(void)                                         **
; ..\mcal_src\Irq.c	   710  **                                                                            **
; ..\mcal_src\Irq.c	   711  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	   712  **                                                                            **
; ..\mcal_src\Irq.c	   713  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	   714  **                                                                            **
; ..\mcal_src\Irq.c	   715  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	   716  **                                                                            **
; ..\mcal_src\Irq.c	   717  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	   718  **                                                                            **
; ..\mcal_src\Irq.c	   719  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	   720  **                                                                            **
; ..\mcal_src\Irq.c	   721  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	   722  **                                                                            **
; ..\mcal_src\Irq.c	   723  ** Description : To set the interrupt priority for various                    **
; ..\mcal_src\Irq.c	   724  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	   725  **                                                                            **
; ..\mcal_src\Irq.c	   726  *******************************************************************************/
; ..\mcal_src\Irq.c	   727  
; ..\mcal_src\Irq.c	   728  void IrqGpt_Init(void)
; Function IrqGpt_Init
.L27:
IrqGpt_Init:	.type	func

; ..\mcal_src\Irq.c	   729  {
; ..\mcal_src\Irq.c	   730    #if (IRQ_GPT_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   731  
; ..\mcal_src\Irq.c	   732    #if (IRQ_GPT120_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   733    IRQ_SFR_MODIFY32 (SRC_GPT120CIRQ.U, IRQ_CLEAR_MASK , \ 
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf0038460)
.L408:
	st.w	[a15]@los(0xf0038460),d15
.L217:

; ..\mcal_src\Irq.c	   734                                (IRQ_GPT120_CARPEL_TOS | IRQ_GPT120_CARPEL_PRIO));
; ..\mcal_src\Irq.c	   735    IRQ_SFR_MODIFY32 (SRC_GPT120T2.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038464)
.L409:
	st.w	[a15]@los(0xf0038464),d15
.L219:

; ..\mcal_src\Irq.c	   736                                  (IRQ_GPT120_T2_TOS | IRQ_GPT120_T2_PRIO));
; ..\mcal_src\Irq.c	   737    IRQ_SFR_MODIFY32 (SRC_GPT120T3.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038468)
.L410:
	st.w	[a15]@los(0xf0038468),d15
.L221:

; ..\mcal_src\Irq.c	   738                                  (IRQ_GPT120_T3_TOS | IRQ_GPT120_T3_PRIO));
; ..\mcal_src\Irq.c	   739    IRQ_SFR_MODIFY32 (SRC_GPT120T4.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf003846c)
.L411:
	st.w	[a15]@los(0xf003846c),d15
.L223:

; ..\mcal_src\Irq.c	   740                                  (IRQ_GPT120_T4_TOS | IRQ_GPT120_T4_PRIO));
; ..\mcal_src\Irq.c	   741    IRQ_SFR_MODIFY32 (SRC_GPT120T5.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038470)
.L412:
	st.w	[a15]@los(0xf0038470),d15
.L225:

; ..\mcal_src\Irq.c	   742                                  (IRQ_GPT120_T5_TOS | IRQ_GPT120_T5_PRIO));
; ..\mcal_src\Irq.c	   743    IRQ_SFR_MODIFY32 (SRC_GPT120T6.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038474)
.L413:
	st.w	[a15]@los(0xf0038474),d15
.L227:

; ..\mcal_src\Irq.c	   744                                  (IRQ_GPT120_T6_TOS | IRQ_GPT120_T6_PRIO));
; ..\mcal_src\Irq.c	   745    #endif
; ..\mcal_src\Irq.c	   746  
; ..\mcal_src\Irq.c	   747    #endif
; ..\mcal_src\Irq.c	   748  }
	ret
.L216:
	
__IrqGpt_Init_function_end:
	.size	IrqGpt_Init,__IrqGpt_Init_function_end-IrqGpt_Init
.L84:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqGtm_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqGtm_Init

; ..\mcal_src\Irq.c	   749  /*******************************************************************************
; ..\mcal_src\Irq.c	   750  ** Syntax :  void IrqGtm_Init(void)                                           **
; ..\mcal_src\Irq.c	   751  **                                                                            **
; ..\mcal_src\Irq.c	   752  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	   753  **                                                                            **
; ..\mcal_src\Irq.c	   754  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	   755  **                                                                            **
; ..\mcal_src\Irq.c	   756  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	   757  **                                                                            **
; ..\mcal_src\Irq.c	   758  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	   759  **                                                                            **
; ..\mcal_src\Irq.c	   760  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	   761  **                                                                            **
; ..\mcal_src\Irq.c	   762  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	   763  **                                                                            **
; ..\mcal_src\Irq.c	   764  ** Description : To set the interrupt priority for various                    **
; ..\mcal_src\Irq.c	   765  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	   766  **                                                                            **
; ..\mcal_src\Irq.c	   767  *******************************************************************************/
; ..\mcal_src\Irq.c	   768  void IrqGtm_Init(void)
; Function IrqGtm_Init
.L29:
IrqGtm_Init:	.type	func

; ..\mcal_src\Irq.c	   769  {
; ..\mcal_src\Irq.c	   770    #if (IRQ_GTM_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   771  
; ..\mcal_src\Irq.c	   772    #if (IRQ_GTM_AEI_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   773    IRQ_SFR_MODIFY32 (SRC_GTMAEIIRQ.U, IRQ_CLEAR_MASK , \ 
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf0039600)
.L414:
	st.w	[a15]@los(0xf0039600),d15
.L230:

; ..\mcal_src\Irq.c	   774                                        (IRQ_GTM_AEI_TOS | IRQ_GTM_AEI_PRIO));
; ..\mcal_src\Irq.c	   775    #endif
; ..\mcal_src\Irq.c	   776  
; ..\mcal_src\Irq.c	   777  
; ..\mcal_src\Irq.c	   778    #if (IRQ_GTM_ERR_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   779    IRQ_SFR_MODIFY32 (SRC_GTMERR.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf0039770)
.L415:
	st.w	[a15]@los(0xf0039770),d15
.L232:

; ..\mcal_src\Irq.c	   780                                   (IRQ_GTM_ERR_SR_TOS | IRQ_GTM_ERR_SR_PRIO));
; ..\mcal_src\Irq.c	   781    #endif
; ..\mcal_src\Irq.c	   782  
; ..\mcal_src\Irq.c	   783    #if (IRQ_GTM_TIM0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   784    IRQ_SFR_MODIFY32 (SRC_GTMTIM00.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039780)
.L416:
	st.w	[a15]@los(0xf0039780),d15
.L234:

; ..\mcal_src\Irq.c	   785                                  (IRQ_GTM_TIM0_SR0_TOS | IRQ_GTM_TIM0_SR0_PRIO));
; ..\mcal_src\Irq.c	   786    IRQ_SFR_MODIFY32 (SRC_GTMTIM01.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039784)
.L417:
	st.w	[a15]@los(0xf0039784),d15
.L236:

; ..\mcal_src\Irq.c	   787                                  (IRQ_GTM_TIM0_SR1_TOS | IRQ_GTM_TIM0_SR1_PRIO));
; ..\mcal_src\Irq.c	   788    IRQ_SFR_MODIFY32 (SRC_GTMTIM02.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039788)
.L418:
	st.w	[a15]@los(0xf0039788),d15
.L238:

; ..\mcal_src\Irq.c	   789                                  (IRQ_GTM_TIM0_SR2_TOS | IRQ_GTM_TIM0_SR2_PRIO));
; ..\mcal_src\Irq.c	   790    IRQ_SFR_MODIFY32 (SRC_GTMTIM03.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf003978c)
.L419:
	st.w	[a15]@los(0xf003978c),d15
.L240:

; ..\mcal_src\Irq.c	   791                                  (IRQ_GTM_TIM0_SR3_TOS | IRQ_GTM_TIM0_SR3_PRIO));
; ..\mcal_src\Irq.c	   792    IRQ_SFR_MODIFY32 (SRC_GTMTIM04.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039790)
.L420:
	st.w	[a15]@los(0xf0039790),d15
.L242:

; ..\mcal_src\Irq.c	   793                                  (IRQ_GTM_TIM0_SR4_TOS | IRQ_GTM_TIM0_SR4_PRIO));
; ..\mcal_src\Irq.c	   794    IRQ_SFR_MODIFY32 (SRC_GTMTIM05.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039794)
.L421:
	st.w	[a15]@los(0xf0039794),d15
.L244:

; ..\mcal_src\Irq.c	   795                                  (IRQ_GTM_TIM0_SR5_TOS | IRQ_GTM_TIM0_SR5_PRIO));
; ..\mcal_src\Irq.c	   796    IRQ_SFR_MODIFY32 (SRC_GTMTIM06.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039798)
.L422:
	st.w	[a15]@los(0xf0039798),d15
.L246:

; ..\mcal_src\Irq.c	   797                                  (IRQ_GTM_TIM0_SR6_TOS | IRQ_GTM_TIM0_SR6_PRIO));
; ..\mcal_src\Irq.c	   798    IRQ_SFR_MODIFY32 (SRC_GTMTIM07.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf003979c)
.L423:
	st.w	[a15]@los(0xf003979c),d15
.L248:

; ..\mcal_src\Irq.c	   799                                  (IRQ_GTM_TIM0_SR7_TOS | IRQ_GTM_TIM0_SR7_PRIO));
; ..\mcal_src\Irq.c	   800    #endif
; ..\mcal_src\Irq.c	   801  
; ..\mcal_src\Irq.c	   802  
; ..\mcal_src\Irq.c	   803    #if (IRQ_GTM_TOM0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   804    IRQ_SFR_MODIFY32 (SRC_GTMTOM00.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039b80)
.L424:
	st.w	[a15]@los(0xf0039b80),d15
.L250:

; ..\mcal_src\Irq.c	   805                                  (IRQ_GTM_TOM0_SR0_TOS | IRQ_GTM_TOM0_SR0_PRIO));
; ..\mcal_src\Irq.c	   806    IRQ_SFR_MODIFY32 (SRC_GTMTOM01.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039b84)
.L425:
	st.w	[a15]@los(0xf0039b84),d15
.L252:

; ..\mcal_src\Irq.c	   807                                  (IRQ_GTM_TOM0_SR1_TOS | IRQ_GTM_TOM0_SR1_PRIO));
; ..\mcal_src\Irq.c	   808    IRQ_SFR_MODIFY32 (SRC_GTMTOM02.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039b88)
.L426:
	st.w	[a15]@los(0xf0039b88),d15
.L254:

; ..\mcal_src\Irq.c	   809                                  (IRQ_GTM_TOM0_SR2_TOS | IRQ_GTM_TOM0_SR2_PRIO));
; ..\mcal_src\Irq.c	   810    IRQ_SFR_MODIFY32 (SRC_GTMTOM03.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039b8c)
.L427:
	st.w	[a15]@los(0xf0039b8c),d15
.L256:

; ..\mcal_src\Irq.c	   811                                  (IRQ_GTM_TOM0_SR3_TOS | IRQ_GTM_TOM0_SR3_PRIO));
; ..\mcal_src\Irq.c	   812    IRQ_SFR_MODIFY32 (SRC_GTMTOM04.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039b90)
.L428:
	st.w	[a15]@los(0xf0039b90),d15
.L258:

; ..\mcal_src\Irq.c	   813                                  (IRQ_GTM_TOM0_SR4_TOS | IRQ_GTM_TOM0_SR4_PRIO));
; ..\mcal_src\Irq.c	   814    IRQ_SFR_MODIFY32 (SRC_GTMTOM05.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039b94)
.L429:
	st.w	[a15]@los(0xf0039b94),d15
.L260:

; ..\mcal_src\Irq.c	   815                                  (IRQ_GTM_TOM0_SR5_TOS | IRQ_GTM_TOM0_SR5_PRIO));
; ..\mcal_src\Irq.c	   816    IRQ_SFR_MODIFY32 (SRC_GTMTOM06.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039b98)
.L430:
	st.w	[a15]@los(0xf0039b98),d15
.L262:

; ..\mcal_src\Irq.c	   817                                  (IRQ_GTM_TOM0_SR6_TOS | IRQ_GTM_TOM0_SR6_PRIO));
; ..\mcal_src\Irq.c	   818    IRQ_SFR_MODIFY32 (SRC_GTMTOM07.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039b9c)
.L431:
	st.w	[a15]@los(0xf0039b9c),d15
.L264:

; ..\mcal_src\Irq.c	   819                                  (IRQ_GTM_TOM0_SR7_TOS | IRQ_GTM_TOM0_SR7_PRIO));
; ..\mcal_src\Irq.c	   820    #endif
; ..\mcal_src\Irq.c	   821  
; ..\mcal_src\Irq.c	   822    #if (IRQ_GTM_TOM1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   823    IRQ_SFR_MODIFY32 (SRC_GTMTOM10.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039ba0)
.L432:
	st.w	[a15]@los(0xf0039ba0),d15
.L266:

; ..\mcal_src\Irq.c	   824                                  (IRQ_GTM_TOM1_SR0_TOS | IRQ_GTM_TOM1_SR0_PRIO));
; ..\mcal_src\Irq.c	   825    IRQ_SFR_MODIFY32 (SRC_GTMTOM11.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039ba4)
.L433:
	st.w	[a15]@los(0xf0039ba4),d15
.L268:

; ..\mcal_src\Irq.c	   826                                  (IRQ_GTM_TOM1_SR1_TOS | IRQ_GTM_TOM1_SR1_PRIO));
; ..\mcal_src\Irq.c	   827    IRQ_SFR_MODIFY32 (SRC_GTMTOM12.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039ba8)
.L434:
	st.w	[a15]@los(0xf0039ba8),d15
.L270:

; ..\mcal_src\Irq.c	   828                                  (IRQ_GTM_TOM1_SR2_TOS | IRQ_GTM_TOM1_SR2_PRIO));
; ..\mcal_src\Irq.c	   829    IRQ_SFR_MODIFY32 (SRC_GTMTOM13.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039bac)
.L435:
	st.w	[a15]@los(0xf0039bac),d15
.L272:

; ..\mcal_src\Irq.c	   830                                  (IRQ_GTM_TOM1_SR3_TOS | IRQ_GTM_TOM1_SR3_PRIO));
; ..\mcal_src\Irq.c	   831    IRQ_SFR_MODIFY32 (SRC_GTMTOM14.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039bb0)
.L436:
	st.w	[a15]@los(0xf0039bb0),d15
.L274:

; ..\mcal_src\Irq.c	   832                                  (IRQ_GTM_TOM1_SR4_TOS | IRQ_GTM_TOM1_SR4_PRIO));
; ..\mcal_src\Irq.c	   833    IRQ_SFR_MODIFY32 (SRC_GTMTOM15.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0039bb4)
.L437:
	st.w	[a15]@los(0xf0039bb4),d15
.L276:

; ..\mcal_src\Irq.c	   834                                  (IRQ_GTM_TOM1_SR5_TOS | IRQ_GTM_TOM1_SR5_PRIO));
; ..\mcal_src\Irq.c	   835    IRQ_SFR_MODIFY32 (SRC_GTMTOM16.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf0039bb8)
.L438:
	st.w	[a15]@los(0xf0039bb8),d15
.L278:

; ..\mcal_src\Irq.c	   836                                  (IRQ_GTM_TOM1_SR6_TOS | IRQ_GTM_TOM1_SR6_PRIO));
; ..\mcal_src\Irq.c	   837    IRQ_SFR_MODIFY32 (SRC_GTMTOM17.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf0039bbc)
.L439:
	st.w	[a15]@los(0xf0039bbc),d15
.L280:

; ..\mcal_src\Irq.c	   838                                  (IRQ_GTM_TOM1_SR7_TOS | IRQ_GTM_TOM1_SR7_PRIO));
; ..\mcal_src\Irq.c	   839  
; ..\mcal_src\Irq.c	   840    #endif
; ..\mcal_src\Irq.c	   841  
; ..\mcal_src\Irq.c	   842    #endif
; ..\mcal_src\Irq.c	   843  }
	ret
.L229:
	
__IrqGtm_Init_function_end:
	.size	IrqGtm_Init,__IrqGtm_Init_function_end-IrqGtm_Init
.L89:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqCan_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqCan_Init

; ..\mcal_src\Irq.c	   844  
; ..\mcal_src\Irq.c	   845  /*******************************************************************************
; ..\mcal_src\Irq.c	   846  ** Syntax :  void IrqCan_Init(void)                                           **
; ..\mcal_src\Irq.c	   847  **                                                                            **
; ..\mcal_src\Irq.c	   848  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	   849  **                                                                            **
; ..\mcal_src\Irq.c	   850  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	   851  **                                                                            **
; ..\mcal_src\Irq.c	   852  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	   853  **                                                                            **
; ..\mcal_src\Irq.c	   854  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	   855  **                                                                            **
; ..\mcal_src\Irq.c	   856  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	   857  **                                                                            **
; ..\mcal_src\Irq.c	   858  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	   859  **                                                                            **
; ..\mcal_src\Irq.c	   860  ** Description : To set the interrupt priority for various                    **
; ..\mcal_src\Irq.c	   861  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	   862  **                                                                            **
; ..\mcal_src\Irq.c	   863  *******************************************************************************/
; ..\mcal_src\Irq.c	   864  void IrqCan_Init(void)
; Function IrqCan_Init
.L31:
IrqCan_Init:	.type	func

; ..\mcal_src\Irq.c	   865  {
; ..\mcal_src\Irq.c	   866  
; ..\mcal_src\Irq.c	   867    #if (IRQ_CAN_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   868    /* Interrupt Priority is written to the SRC registers*/
; ..\mcal_src\Irq.c	   869    #if (IRQ_CAN0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   870    IRQ_SFR_MODIFY32 (SRC_CANINT0.U, IRQ_CLEAR_MASK ,  \ 
; ..\mcal_src\Irq.c	   871                                            (IRQ_CAN_SR0_TOS | IRQ_CAN_SR0_PRIO));
; ..\mcal_src\Irq.c	   872    #endif
; ..\mcal_src\Irq.c	   873  
; ..\mcal_src\Irq.c	   874    #if (IRQ_CAN1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   875    IRQ_SFR_MODIFY32 (SRC_CANINT1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   876                                            (IRQ_CAN_SR1_TOS | IRQ_CAN_SR1_PRIO));
; ..\mcal_src\Irq.c	   877    #endif
; ..\mcal_src\Irq.c	   878  
; ..\mcal_src\Irq.c	   879    #if (IRQ_CAN2_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   880    IRQ_SFR_MODIFY32 (SRC_CANINT2.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   881                                            (IRQ_CAN_SR2_TOS | IRQ_CAN_SR2_PRIO));
; ..\mcal_src\Irq.c	   882    #endif
; ..\mcal_src\Irq.c	   883  
; ..\mcal_src\Irq.c	   884    #if (IRQ_CAN3_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   885    IRQ_SFR_MODIFY32 (SRC_CANINT3.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   886                                            (IRQ_CAN_SR3_TOS | IRQ_CAN_SR3_PRIO));
; ..\mcal_src\Irq.c	   887    #endif
; ..\mcal_src\Irq.c	   888  
; ..\mcal_src\Irq.c	   889    #if (IRQ_CAN4_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   890    IRQ_SFR_MODIFY32 (SRC_CANINT4.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   891                                            (IRQ_CAN_SR4_TOS | IRQ_CAN_SR4_PRIO));
; ..\mcal_src\Irq.c	   892    #endif
; ..\mcal_src\Irq.c	   893  
; ..\mcal_src\Irq.c	   894    #if (IRQ_CAN5_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   895    IRQ_SFR_MODIFY32 (SRC_CANINT5.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   896                                           (IRQ_CAN_SR5_TOS | IRQ_CAN_SR5_PRIO));
; ..\mcal_src\Irq.c	   897    #endif
; ..\mcal_src\Irq.c	   898  
; ..\mcal_src\Irq.c	   899    #if (IRQ_CAN6_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   900    IRQ_SFR_MODIFY32 (SRC_CANINT6.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   901                                            (IRQ_CAN_SR6_TOS | IRQ_CAN_SR6_PRIO));
; ..\mcal_src\Irq.c	   902    #endif
; ..\mcal_src\Irq.c	   903  
; ..\mcal_src\Irq.c	   904    #if (IRQ_CAN7_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   905    IRQ_SFR_MODIFY32 (SRC_CANINT7.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   906                                            (IRQ_CAN_SR7_TOS | IRQ_CAN_SR7_PRIO));
; ..\mcal_src\Irq.c	   907    #endif
; ..\mcal_src\Irq.c	   908  
; ..\mcal_src\Irq.c	   909    #if (IRQ_CAN8_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   910    IRQ_SFR_MODIFY32 (SRC_CANINT8.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   911                                            (IRQ_CAN_SR8_TOS | IRQ_CAN_SR8_PRIO));
; ..\mcal_src\Irq.c	   912    #endif
; ..\mcal_src\Irq.c	   913  
; ..\mcal_src\Irq.c	   914    #if (IRQ_CAN9_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   915    IRQ_SFR_MODIFY32 (SRC_CANINT9.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   916                                           (IRQ_CAN_SR9_TOS | IRQ_CAN_SR9_PRIO));
; ..\mcal_src\Irq.c	   917    #endif
; ..\mcal_src\Irq.c	   918  
; ..\mcal_src\Irq.c	   919    #if (IRQ_CAN10_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   920    IRQ_SFR_MODIFY32 (SRC_CANINT10.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   921                                         (IRQ_CAN_SR10_TOS | IRQ_CAN_SR10_PRIO));
; ..\mcal_src\Irq.c	   922    #endif
; ..\mcal_src\Irq.c	   923  
; ..\mcal_src\Irq.c	   924    #if (IRQ_CAN11_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   925    IRQ_SFR_MODIFY32 (SRC_CANINT11.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   926                                          (IRQ_CAN_SR11_TOS | IRQ_CAN_SR11_PRIO));
; ..\mcal_src\Irq.c	   927    #endif
; ..\mcal_src\Irq.c	   928  
; ..\mcal_src\Irq.c	   929    #if (IRQ_CAN12_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   930    IRQ_SFR_MODIFY32 (SRC_CANINT12.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   931                                      (IRQ_CAN_SR12_TOS | IRQ_CAN_SR12_PRIO));
; ..\mcal_src\Irq.c	   932    #endif
; ..\mcal_src\Irq.c	   933  
; ..\mcal_src\Irq.c	   934    #if (IRQ_CAN13_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   935    IRQ_SFR_MODIFY32 (SRC_CANINT13.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   936                                         (IRQ_CAN_SR13_TOS | IRQ_CAN_SR13_PRIO));
; ..\mcal_src\Irq.c	   937    #endif
; ..\mcal_src\Irq.c	   938  
; ..\mcal_src\Irq.c	   939    #if (IRQ_CAN14_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   940    IRQ_SFR_MODIFY32 (SRC_CANINT14.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   941                                         (IRQ_CAN_SR14_TOS | IRQ_CAN_SR14_PRIO));
; ..\mcal_src\Irq.c	   942    #endif
; ..\mcal_src\Irq.c	   943  
; ..\mcal_src\Irq.c	   944    #if (IRQ_CAN15_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   945    IRQ_SFR_MODIFY32 (SRC_CANINT15.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   946                                          (IRQ_CAN_SR15_TOS | IRQ_CAN_SR15_PRIO));
; ..\mcal_src\Irq.c	   947    #endif
; ..\mcal_src\Irq.c	   948  
; ..\mcal_src\Irq.c	   949    #if (IRQ_CAN16_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   950    IRQ_SFR_MODIFY32 (SRC_CAN1INT0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   951                                        (IRQ_CAN_SR16_TOS | IRQ_CAN_SR16_PRIO));
; ..\mcal_src\Irq.c	   952    #endif
; ..\mcal_src\Irq.c	   953  
; ..\mcal_src\Irq.c	   954    #if (IRQ_CAN17_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   955    IRQ_SFR_MODIFY32 (SRC_CAN1INT1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	   956                                         (IRQ_CAN_SR17_TOS | IRQ_CAN_SR17_PRIO));
; ..\mcal_src\Irq.c	   957    #endif
; ..\mcal_src\Irq.c	   958  
; ..\mcal_src\Irq.c	   959    #if (IRQ_CAN18_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   960    IRQ_SFR_MODIFY32 (SRC_CAN1INT2.U, IRQ_CLEAR_MASK ,  \ 
; ..\mcal_src\Irq.c	   961                                          (IRQ_CAN_SR18_TOS | IRQ_CAN_SR18_PRIO));
; ..\mcal_src\Irq.c	   962    #endif
; ..\mcal_src\Irq.c	   963  
; ..\mcal_src\Irq.c	   964    #if (IRQ_CAN19_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   965    IRQ_SFR_MODIFY32 (SRC_CAN1INT3.U, IRQ_CLEAR_MASK ,  \ 
; ..\mcal_src\Irq.c	   966                                          (IRQ_CAN_SR19_TOS | IRQ_CAN_SR19_PRIO));
; ..\mcal_src\Irq.c	   967    #endif
; ..\mcal_src\Irq.c	   968  
; ..\mcal_src\Irq.c	   969    #if (IRQ_CAN20_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   970    IRQ_SFR_MODIFY32 (SRC_CAN1INT4.U, IRQ_CLEAR_MASK ,  \ 
; ..\mcal_src\Irq.c	   971                                    (IRQ_CAN_SR20_TOS | IRQ_CAN_SR20_PRIO));
; ..\mcal_src\Irq.c	   972    #endif
; ..\mcal_src\Irq.c	   973  
; ..\mcal_src\Irq.c	   974    #if (IRQ_CAN21_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   975    IRQ_SFR_MODIFY32 (SRC_CAN1INT5.U, IRQ_CLEAR_MASK ,  \ 
; ..\mcal_src\Irq.c	   976                                        (IRQ_CAN_SR21_TOS | IRQ_CAN_SR21_PRIO));
; ..\mcal_src\Irq.c	   977    #endif
; ..\mcal_src\Irq.c	   978  
; ..\mcal_src\Irq.c	   979    #if (IRQ_CAN22_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   980    IRQ_SFR_MODIFY32 (SRC_CAN1INT6.U, IRQ_CLEAR_MASK ,  \ 
; ..\mcal_src\Irq.c	   981                                          (IRQ_CAN_SR22_TOS | IRQ_CAN_SR22_PRIO));
; ..\mcal_src\Irq.c	   982    #endif
; ..\mcal_src\Irq.c	   983  
; ..\mcal_src\Irq.c	   984    #if (IRQ_CAN23_EXIST == STD_ON)
; ..\mcal_src\Irq.c	   985    IRQ_SFR_MODIFY32 (SRC_CAN1INT7.U, IRQ_CLEAR_MASK ,  \ 
; ..\mcal_src\Irq.c	   986                                        (IRQ_CAN_SR23_TOS | IRQ_CAN_SR23_PRIO));
; ..\mcal_src\Irq.c	   987    #endif
; ..\mcal_src\Irq.c	   988  
; ..\mcal_src\Irq.c	   989    #endif
; ..\mcal_src\Irq.c	   990   }
	ret
.L210:
	
__IrqCan_Init_function_end:
	.size	IrqCan_Init,__IrqCan_Init_function_end-IrqCan_Init
.L74:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqGpsrGroup_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqGpsrGroup_Init

; ..\mcal_src\Irq.c	   991  
; ..\mcal_src\Irq.c	   992  
; ..\mcal_src\Irq.c	   993  /*******************************************************************************
; ..\mcal_src\Irq.c	   994  ** Syntax :  void IrqGpsrGroup_Init(void)                                     **
; ..\mcal_src\Irq.c	   995  **                                                                            **
; ..\mcal_src\Irq.c	   996  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	   997  **                                                                            **
; ..\mcal_src\Irq.c	   998  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	   999  **                                                                            **
; ..\mcal_src\Irq.c	  1000  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1001  **                                                                            **
; ..\mcal_src\Irq.c	  1002  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1003  **                                                                            **
; ..\mcal_src\Irq.c	  1004  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1005  **                                                                            **
; ..\mcal_src\Irq.c	  1006  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1007  **                                                                            **
; ..\mcal_src\Irq.c	  1008  ** Description : To set the interrupt priority for Fls                        **
; ..\mcal_src\Irq.c	  1009  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1010  **                                                                            **
; ..\mcal_src\Irq.c	  1011  *******************************************************************************/
; ..\mcal_src\Irq.c	  1012  void IrqGpsrGroup_Init(void)
; Function IrqGpsrGroup_Init
.L33:
IrqGpsrGroup_Init:	.type	func

; ..\mcal_src\Irq.c	  1013  {
; ..\mcal_src\Irq.c	  1014    #if (IRQ_GPSRGROUP_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1015  
; ..\mcal_src\Irq.c	  1016    #if (IRQ_GPSRGROUP0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1017    IRQ_SFR_MODIFY32 (SRC_GPSR00.U, IRQ_CLEAR_MASK , \ 
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf0039000)
.L440:
	st.w	[a15]@los(0xf0039000),d15
.L283:

; ..\mcal_src\Irq.c	  1018                             (IRQ_GPSRGROUP0_SR0_TOS | IRQ_GPSRGROUP0_SR0_PRIO));
; ..\mcal_src\Irq.c	  1019    #endif
; ..\mcal_src\Irq.c	  1020  
; ..\mcal_src\Irq.c	  1021    #endif
; ..\mcal_src\Irq.c	  1022  }
	ret
.L282:
	
__IrqGpsrGroup_Init_function_end:
	.size	IrqGpsrGroup_Init,__IrqGpsrGroup_Init_function_end-IrqGpsrGroup_Init
.L94:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqSpi_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqSpi_Init

; ..\mcal_src\Irq.c	  1023  
; ..\mcal_src\Irq.c	  1024  /********************************************************************************
; ..\mcal_src\Irq.c	  1025  ** Syntax :  void IrqSpi_Init(void)                                           **
; ..\mcal_src\Irq.c	  1026  **                                                                            **
; ..\mcal_src\Irq.c	  1027  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1028  **                                                                            **
; ..\mcal_src\Irq.c	  1029  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1030  **                                                                            **
; ..\mcal_src\Irq.c	  1031  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1032  **                                                                            **
; ..\mcal_src\Irq.c	  1033  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1034  **                                                                            **
; ..\mcal_src\Irq.c	  1035  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1036  **                                                                            **
; ..\mcal_src\Irq.c	  1037  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1038  **                                                                            **
; ..\mcal_src\Irq.c	  1039  ** Description : To set the interrupt priority for various                    **
; ..\mcal_src\Irq.c	  1040  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1041  **                                                                            **
; ..\mcal_src\Irq.c	  1042  *******************************************************************************/
; ..\mcal_src\Irq.c	  1043  
; ..\mcal_src\Irq.c	  1044  void IrqSpi_Init(void)
; Function IrqSpi_Init
.L35:
IrqSpi_Init:	.type	func

; ..\mcal_src\Irq.c	  1045  {
; ..\mcal_src\Irq.c	  1046  
; ..\mcal_src\Irq.c	  1047    #if (IRQ_QSPI_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1048  
; ..\mcal_src\Irq.c	  1049    #if (IRQ_QSPI0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1050    IRQ_SFR_MODIFY32 (SRC_QSPI0TX.U, IRQ_CLEAR_MASK ,  \ 
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf0038190)
.L441:
	st.w	[a15]@los(0xf0038190),d15
.L287:

; ..\mcal_src\Irq.c	  1051                                         (IRQ_QSPI0_TX_TOS | IRQ_QSPI0_TX_PRIO));
; ..\mcal_src\Irq.c	  1052    IRQ_SFR_MODIFY32 (SRC_QSPI0RX.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf0038194)
.L442:
	st.w	[a15]@los(0xf0038194),d15
.L289:

; ..\mcal_src\Irq.c	  1053                                         (IRQ_QSPI0_RX_TOS | IRQ_QSPI0_RX_PRIO));
; ..\mcal_src\Irq.c	  1054    IRQ_SFR_MODIFY32 (SRC_QSPI0ERR.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038198)
.L443:
	st.w	[a15]@los(0xf0038198),d15
.L291:

; ..\mcal_src\Irq.c	  1055                                       (IRQ_QSPI0_ERR_TOS | IRQ_QSPI0_ERR_PRIO));
; ..\mcal_src\Irq.c	  1056    IRQ_SFR_MODIFY32 (SRC_QSPI0PT.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf003819c)
.L444:
	st.w	[a15]@los(0xf003819c),d15
.L293:

; ..\mcal_src\Irq.c	  1057                                        (IRQ_QSPI0_PT_TOS | IRQ_QSPI0_PT_PRIO));
; ..\mcal_src\Irq.c	  1058    IRQ_SFR_MODIFY32 (SRC_QSPI0U.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf00381a4)
.L445:
	st.w	[a15]@los(0xf00381a4),d15
.L295:

; ..\mcal_src\Irq.c	  1059                                         (IRQ_QSPI0_UD_TOS | IRQ_QSPI0_UD_PRIO));
; ..\mcal_src\Irq.c	  1060    #endif /*IRQ_QSPI0_EXIST == STD_ON*/
; ..\mcal_src\Irq.c	  1061  
; ..\mcal_src\Irq.c	  1062    #if (IRQ_QSPI1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1063    IRQ_SFR_MODIFY32 (SRC_QSPI1TX.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381a8)
.L446:
	insert	d15,d15,#1,#11,#1
	st.w	[a15]@los(0xf00381a8),d15
.L297:

; ..\mcal_src\Irq.c	  1064                                          (IRQ_QSPI1_TX_TOS | IRQ_QSPI1_TX_PRIO));
; ..\mcal_src\Irq.c	  1065    IRQ_SFR_MODIFY32 (SRC_QSPI1RX.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381ac)
.L447:
	insert	d15,d15,#1,#11,#1
	st.w	[a15]@los(0xf00381ac),d15
.L299:

; ..\mcal_src\Irq.c	  1066                                         (IRQ_QSPI1_RX_TOS | IRQ_QSPI1_RX_PRIO));
; ..\mcal_src\Irq.c	  1067    IRQ_SFR_MODIFY32 (SRC_QSPI1ERR.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381b0)
.L448:
	st.w	[a15]@los(0xf00381b0),d15
.L301:

; ..\mcal_src\Irq.c	  1068                                        (IRQ_QSPI1_ERR_TOS | IRQ_QSPI1_ERR_PRIO));
; ..\mcal_src\Irq.c	  1069    IRQ_SFR_MODIFY32 (SRC_QSPI1PT.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf00381b4)
.L449:
	st.w	[a15]@los(0xf00381b4),d15
.L303:

; ..\mcal_src\Irq.c	  1070                                          (IRQ_QSPI1_PT_TOS | IRQ_QSPI1_PT_PRIO));
; ..\mcal_src\Irq.c	  1071    IRQ_SFR_MODIFY32 (SRC_QSPI1U.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf00381bc)
.L450:
	st.w	[a15]@los(0xf00381bc),d15
.L305:

; ..\mcal_src\Irq.c	  1072                                          (IRQ_QSPI1_UD_TOS | IRQ_QSPI1_UD_PRIO));
; ..\mcal_src\Irq.c	  1073    #endif
; ..\mcal_src\Irq.c	  1074  
; ..\mcal_src\Irq.c	  1075    #if (IRQ_QSPI2_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1076    IRQ_SFR_MODIFY32 (SRC_QSPI2TX.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381c0)
.L451:
	st.w	[a15]@los(0xf00381c0),d15
.L307:

; ..\mcal_src\Irq.c	  1077                                       (IRQ_QSPI2_TX_TOS | IRQ_QSPI2_TX_PRIO));
; ..\mcal_src\Irq.c	  1078    IRQ_SFR_MODIFY32 (SRC_QSPI2RX.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf00381c4)
.L452:
	st.w	[a15]@los(0xf00381c4),d15
.L309:

; ..\mcal_src\Irq.c	  1079                                         (IRQ_QSPI2_RX_TOS | IRQ_QSPI2_RX_PRIO));
; ..\mcal_src\Irq.c	  1080    IRQ_SFR_MODIFY32 (SRC_QSPI2ERR.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381c8)
.L453:
	st.w	[a15]@los(0xf00381c8),d15
.L311:

; ..\mcal_src\Irq.c	  1081                                       (IRQ_QSPI2_ERR_TOS | IRQ_QSPI2_ERR_PRIO));
; ..\mcal_src\Irq.c	  1082    IRQ_SFR_MODIFY32 (SRC_QSPI2PT.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381cc)
.L454:
	st.w	[a15]@los(0xf00381cc),d15
.L313:

; ..\mcal_src\Irq.c	  1083                                       (IRQ_QSPI2_PT_TOS | IRQ_QSPI2_PT_PRIO));
; ..\mcal_src\Irq.c	  1084    IRQ_SFR_MODIFY32 (SRC_QSPI2HC.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381d0)
.L455:
	st.w	[a15]@los(0xf00381d0),d15
.L315:

; ..\mcal_src\Irq.c	  1085                                         (IRQ_QSPI2_HC_TOS | IRQ_QSPI2_HC_PRIO));
; ..\mcal_src\Irq.c	  1086    IRQ_SFR_MODIFY32 (SRC_QSPI2U.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf00381d4)
.L456:
	st.w	[a15]@los(0xf00381d4),d15
.L317:

; ..\mcal_src\Irq.c	  1087                                       (IRQ_QSPI2_UD_TOS | IRQ_QSPI2_UD_PRIO));
; ..\mcal_src\Irq.c	  1088    #endif
; ..\mcal_src\Irq.c	  1089  
; ..\mcal_src\Irq.c	  1090    #if (IRQ_QSPI3_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1091    IRQ_SFR_MODIFY32 (SRC_QSPI3TX.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381d8)
.L457:
	st.w	[a15]@los(0xf00381d8),d15
.L319:

; ..\mcal_src\Irq.c	  1092                                        (IRQ_QSPI3_TX_TOS | IRQ_QSPI3_TX_PRIO));
; ..\mcal_src\Irq.c	  1093    IRQ_SFR_MODIFY32 (SRC_QSPI3RX.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381dc)
.L458:
	insert	d15,d15,#1,#11,#1
	st.w	[a15]@los(0xf00381dc),d15
.L321:

; ..\mcal_src\Irq.c	  1094                                      (IRQ_QSPI3_RX_TOS | IRQ_QSPI3_RX_PRIO));
; ..\mcal_src\Irq.c	  1095    IRQ_SFR_MODIFY32 (SRC_QSPI3ERR.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf00381e0)
.L459:
	st.w	[a15]@los(0xf00381e0),d15
.L323:

; ..\mcal_src\Irq.c	  1096                                        (IRQ_QSPI3_ERR_TOS | IRQ_QSPI3_ERR_PRIO));
; ..\mcal_src\Irq.c	  1097    IRQ_SFR_MODIFY32 (SRC_QSPI3PT.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf00381e4)
.L460:
	st.w	[a15]@los(0xf00381e4),d15
.L325:

; ..\mcal_src\Irq.c	  1098                                          (IRQ_QSPI3_PT_TOS | IRQ_QSPI3_PT_PRIO));
; ..\mcal_src\Irq.c	  1099    IRQ_SFR_MODIFY32 (SRC_QSPI3HC.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf00381e8)
.L461:
	st.w	[a15]@los(0xf00381e8),d15
.L327:

; ..\mcal_src\Irq.c	  1100                                          (IRQ_QSPI3_HC_TOS | IRQ_QSPI3_HC_PRIO));
; ..\mcal_src\Irq.c	  1101    IRQ_SFR_MODIFY32 (SRC_QSPI3U.U, IRQ_CLEAR_MASK ,  \ 
	ld.w	d15,[a15]@los(0xf00381ec)
.L462:
	st.w	[a15]@los(0xf00381ec),d15
.L329:

; ..\mcal_src\Irq.c	  1102                                       (IRQ_QSPI3_UD_TOS | IRQ_QSPI3_UD_PRIO));
; ..\mcal_src\Irq.c	  1103    #endif
; ..\mcal_src\Irq.c	  1104  
; ..\mcal_src\Irq.c	  1105    #endif
; ..\mcal_src\Irq.c	  1106  }
	ret
.L286:
	
__IrqSpi_Init_function_end:
	.size	IrqSpi_Init,__IrqSpi_Init_function_end-IrqSpi_Init
.L104:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqAdc_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqAdc_Init

; ..\mcal_src\Irq.c	  1107  
; ..\mcal_src\Irq.c	  1108  /*******************************************************************************
; ..\mcal_src\Irq.c	  1109  ** Syntax :  void IrqAdc_Init(void)                                           **
; ..\mcal_src\Irq.c	  1110  **                                                                            **
; ..\mcal_src\Irq.c	  1111  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1112  **                                                                            **
; ..\mcal_src\Irq.c	  1113  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1114  **                                                                            **
; ..\mcal_src\Irq.c	  1115  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1116  **                                                                            **
; ..\mcal_src\Irq.c	  1117  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1118  **                                                                            **
; ..\mcal_src\Irq.c	  1119  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1120  **                                                                            **
; ..\mcal_src\Irq.c	  1121  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1122  **                                                                            **
; ..\mcal_src\Irq.c	  1123  ** Description : To set the interrupt priority for various                    **
; ..\mcal_src\Irq.c	  1124  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1125  **                                                                            **
; ..\mcal_src\Irq.c	  1126  *******************************************************************************/
; ..\mcal_src\Irq.c	  1127  void IrqAdc_Init(void)
; Function IrqAdc_Init
.L37:
IrqAdc_Init:	.type	func

; ..\mcal_src\Irq.c	  1128  {
; ..\mcal_src\Irq.c	  1129  
; ..\mcal_src\Irq.c	  1130    #if (IRQ_ADC_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1131  
; ..\mcal_src\Irq.c	  1132    #if (IRQ_ADC0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1133    IRQ_SFR_MODIFY32 (SRC_VADCG0SR0.U, IRQ_CLEAR_MASK , \ 
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf0038980)
.L463:
	st.w	[a15]@los(0xf0038980),d15
.L332:

; ..\mcal_src\Irq.c	  1134                                          (IRQ_ADC0_SR0_TOS | IRQ_ADC0_SR0_PRIO));
; ..\mcal_src\Irq.c	  1135    IRQ_SFR_MODIFY32 (SRC_VADCG0SR1.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038984)
.L464:
	st.w	[a15]@los(0xf0038984),d15
.L334:

; ..\mcal_src\Irq.c	  1136                                          (IRQ_ADC0_SR1_TOS | IRQ_ADC0_SR1_PRIO));
; ..\mcal_src\Irq.c	  1137    IRQ_SFR_MODIFY32 (SRC_VADCG0SR2.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038988)
.L465:
	st.w	[a15]@los(0xf0038988),d15
.L336:

; ..\mcal_src\Irq.c	  1138                                          (IRQ_ADC0_SR2_TOS | IRQ_ADC0_SR2_PRIO));
; ..\mcal_src\Irq.c	  1139    IRQ_SFR_MODIFY32 (SRC_VADCG0SR3.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf003898c)
.L466:
	st.w	[a15]@los(0xf003898c),d15
.L338:

; ..\mcal_src\Irq.c	  1140                                         (IRQ_ADC0_SR3_TOS | IRQ_ADC0_SR3_PRIO));
; ..\mcal_src\Irq.c	  1141    #endif
; ..\mcal_src\Irq.c	  1142  
; ..\mcal_src\Irq.c	  1143    #if (IRQ_ADC1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1144    IRQ_SFR_MODIFY32 (SRC_VADCG1SR0.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038990)
.L467:
	st.w	[a15]@los(0xf0038990),d15
.L340:

; ..\mcal_src\Irq.c	  1145                                          (IRQ_ADC1_SR0_TOS | IRQ_ADC1_SR0_PRIO));
; ..\mcal_src\Irq.c	  1146    IRQ_SFR_MODIFY32 (SRC_VADCG1SR1.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038994)
.L468:
	st.w	[a15]@los(0xf0038994),d15
.L342:

; ..\mcal_src\Irq.c	  1147                                          (IRQ_ADC1_SR1_TOS | IRQ_ADC1_SR1_PRIO));
; ..\mcal_src\Irq.c	  1148    IRQ_SFR_MODIFY32 (SRC_VADCG1SR2.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf0038998)
.L469:
	st.w	[a15]@los(0xf0038998),d15
.L344:

; ..\mcal_src\Irq.c	  1149                                          (IRQ_ADC1_SR2_TOS | IRQ_ADC1_SR2_PRIO));
; ..\mcal_src\Irq.c	  1150    IRQ_SFR_MODIFY32 (SRC_VADCG1SR3.U, IRQ_CLEAR_MASK , \ 
	ld.w	d15,[a15]@los(0xf003899c)
.L470:
	st.w	[a15]@los(0xf003899c),d15
.L346:

; ..\mcal_src\Irq.c	  1151                                          (IRQ_ADC1_SR3_TOS | IRQ_ADC1_SR3_PRIO));
; ..\mcal_src\Irq.c	  1152    #endif
; ..\mcal_src\Irq.c	  1153  
; ..\mcal_src\Irq.c	  1154    #if (IRQ_ADC2_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1155    IRQ_SFR_MODIFY32 (SRC_VADCG2SR0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1156                                          (IRQ_ADC2_SR0_TOS | IRQ_ADC2_SR0_PRIO));
; ..\mcal_src\Irq.c	  1157    IRQ_SFR_MODIFY32 (SRC_VADCG2SR1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1158                                          (IRQ_ADC2_SR1_TOS | IRQ_ADC2_SR1_PRIO));
; ..\mcal_src\Irq.c	  1159    IRQ_SFR_MODIFY32 (SRC_VADCG2SR2.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1160                                          (IRQ_ADC2_SR2_TOS | IRQ_ADC2_SR2_PRIO));
; ..\mcal_src\Irq.c	  1161    IRQ_SFR_MODIFY32 (SRC_VADCG2SR3.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1162                                          (IRQ_ADC2_SR3_TOS | IRQ_ADC2_SR3_PRIO));
; ..\mcal_src\Irq.c	  1163    #endif
; ..\mcal_src\Irq.c	  1164  
; ..\mcal_src\Irq.c	  1165    #if (IRQ_ADC3_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1166    IRQ_SFR_MODIFY32 (SRC_VADCG3SR0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1167                                         (IRQ_ADC3_SR0_TOS | IRQ_ADC3_SR0_PRIO));
; ..\mcal_src\Irq.c	  1168    IRQ_SFR_MODIFY32 (SRC_VADCG3SR1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1169                                          (IRQ_ADC3_SR1_TOS | IRQ_ADC3_SR1_PRIO));
; ..\mcal_src\Irq.c	  1170    IRQ_SFR_MODIFY32 (SRC_VADCG3SR2.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1171                                          (IRQ_ADC3_SR2_TOS | IRQ_ADC3_SR2_PRIO));
; ..\mcal_src\Irq.c	  1172    IRQ_SFR_MODIFY32 (SRC_VADCG3SR3.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1173                                         (IRQ_ADC3_SR3_TOS | IRQ_ADC3_SR3_PRIO));
; ..\mcal_src\Irq.c	  1174    #endif
; ..\mcal_src\Irq.c	  1175  
; ..\mcal_src\Irq.c	  1176    #if (IRQ_ADCCG0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1177    IRQ_SFR_MODIFY32 (SRC_VADCCG0SR0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1178                                      (IRQ_ADCCG0_SR0_TOS | IRQ_ADCCG0_SR0_PRIO));
; ..\mcal_src\Irq.c	  1179    IRQ_SFR_MODIFY32 (SRC_VADCCG0SR1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1180                                      (IRQ_ADCCG0_SR1_TOS | IRQ_ADCCG0_SR1_PRIO));
; ..\mcal_src\Irq.c	  1181    IRQ_SFR_MODIFY32 (SRC_VADCCG0SR2.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1182                                      (IRQ_ADCCG0_SR2_TOS | IRQ_ADCCG0_SR2_PRIO));
; ..\mcal_src\Irq.c	  1183    IRQ_SFR_MODIFY32 (SRC_VADCCG0SR3.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1184                                      (IRQ_ADCCG0_SR3_TOS | IRQ_ADCCG0_SR3_PRIO));
; ..\mcal_src\Irq.c	  1185    #endif
; ..\mcal_src\Irq.c	  1186  
; ..\mcal_src\Irq.c	  1187    #endif /* (IRQ_ADC_EXIST == STD_ON) */
; ..\mcal_src\Irq.c	  1188  }
	ret
.L331:
	
__IrqAdc_Init_function_end:
	.size	IrqAdc_Init,__IrqAdc_Init_function_end-IrqAdc_Init
.L109:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqFlexray_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqFlexray_Init

; ..\mcal_src\Irq.c	  1189  
; ..\mcal_src\Irq.c	  1190  
; ..\mcal_src\Irq.c	  1191  
; ..\mcal_src\Irq.c	  1192  
; ..\mcal_src\Irq.c	  1193  
; ..\mcal_src\Irq.c	  1194  /*******************************************************************************
; ..\mcal_src\Irq.c	  1195  ** Syntax :  void IrqEray_Init(void)                                          **
; ..\mcal_src\Irq.c	  1196  **                                                                            **
; ..\mcal_src\Irq.c	  1197  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1198  **                                                                            **
; ..\mcal_src\Irq.c	  1199  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1200  **                                                                            **
; ..\mcal_src\Irq.c	  1201  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1202  **                                                                            **
; ..\mcal_src\Irq.c	  1203  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1204  **                                                                            **
; ..\mcal_src\Irq.c	  1205  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1206  **                                                                            **
; ..\mcal_src\Irq.c	  1207  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1208  **                                                                            **
; ..\mcal_src\Irq.c	  1209  ** Description : To set the interrupt priority for                            **
; ..\mcal_src\Irq.c	  1210  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1211  **                                                                            **
; ..\mcal_src\Irq.c	  1212  *******************************************************************************/
; ..\mcal_src\Irq.c	  1213  
; ..\mcal_src\Irq.c	  1214  void IrqFlexray_Init(void)
; Function IrqFlexray_Init
.L39:
IrqFlexray_Init:	.type	func

; ..\mcal_src\Irq.c	  1215  {
; ..\mcal_src\Irq.c	  1216    #if (IRQ_FLEXRAY_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1217  
; ..\mcal_src\Irq.c	  1218    #if (IRQ_FLEXRAY0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1219    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_INT0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1220                                 (IRQ_FLEXRAY0_SR0_TOS | IRQ_FLEXRAY0_SR0_PRIO));
; ..\mcal_src\Irq.c	  1221    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_INT1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1222                                 (IRQ_FLEXRAY0_SR1_TOS | IRQ_FLEXRAY0_SR1_PRIO));
; ..\mcal_src\Irq.c	  1223    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_TINT0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1224                    (IRQ_FLEXRAY0_TIMER_INT0_TOS | IRQ_FLEXRAY0_TIMER_INT0_PRIO));
; ..\mcal_src\Irq.c	  1225    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_TINT1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1226                    (IRQ_FLEXRAY0_TIMER_INT1_TOS | IRQ_FLEXRAY0_TIMER_INT1_PRIO));
; ..\mcal_src\Irq.c	  1227    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_NDAT0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1228                     (IRQ_FLEXRAY0_NEW_DATA0_TOS | IRQ_FLEXRAY0_NEW_DATA0_PRIO));
; ..\mcal_src\Irq.c	  1229    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_NDAT1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1230                     (IRQ_FLEXRAY0_NEW_DATA1_TOS | IRQ_FLEXRAY0_NEW_DATA1_PRIO));
; ..\mcal_src\Irq.c	  1231    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_MBSC0.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1232                             (IRQ_FLEXRAY0_MBSC0_TOS | IRQ_FLEXRAY0_MBSC0_PRIO));
; ..\mcal_src\Irq.c	  1233    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_MBSC1.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1234                             (IRQ_FLEXRAY0_MBSC1_TOS | IRQ_FLEXRAY0_MBSC1_PRIO));
; ..\mcal_src\Irq.c	  1235    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_OBUSY.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1236                         (IRQ_FLEXRAY0_OB_BUSY_TOS | IRQ_FLEXRAY0_OB_BUSY_PRIO));
; ..\mcal_src\Irq.c	  1237    IRQ_SFR_MODIFY32 (SRC_ERAY_ERAY0_IBUSY.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1238                          (IRQ_FLEXRAY0_IB_BUSY_TOS | IRQ_FLEXRAY0_IB_BUSY_PRIO));
; ..\mcal_src\Irq.c	  1239    #endif
; ..\mcal_src\Irq.c	  1240  
; ..\mcal_src\Irq.c	  1241    #endif
; ..\mcal_src\Irq.c	  1242  }
	ret
.L285:
	
__IrqFlexray_Init_function_end:
	.size	IrqFlexray_Init,__IrqFlexray_Init_function_end-IrqFlexray_Init
.L99:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqEthernet_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqEthernet_Init

; ..\mcal_src\Irq.c	  1243  
; ..\mcal_src\Irq.c	  1244  /*******************************************************************************
; ..\mcal_src\Irq.c	  1245  ** Syntax :  void IrqEthernet_Init(void)                                      **
; ..\mcal_src\Irq.c	  1246  **                                                                            **
; ..\mcal_src\Irq.c	  1247  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1248  **                                                                            **
; ..\mcal_src\Irq.c	  1249  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1250  **                                                                            **
; ..\mcal_src\Irq.c	  1251  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1252  **                                                                            **
; ..\mcal_src\Irq.c	  1253  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1254  **                                                                            **
; ..\mcal_src\Irq.c	  1255  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1256  **                                                                            **
; ..\mcal_src\Irq.c	  1257  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1258  **                                                                            **
; ..\mcal_src\Irq.c	  1259  ** Description : To set the interrupt priority for                            **
; ..\mcal_src\Irq.c	  1260  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1261  **                                                                            **
; ..\mcal_src\Irq.c	  1262  *******************************************************************************/
; ..\mcal_src\Irq.c	  1263  
; ..\mcal_src\Irq.c	  1264  void IrqEthernet_Init(void)
; Function IrqEthernet_Init
.L41:
IrqEthernet_Init:	.type	func

; ..\mcal_src\Irq.c	  1265  {
; ..\mcal_src\Irq.c	  1266    #if (IRQ_ETH_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1267    IRQ_SFR_MODIFY32 (SRC_ETH.U, IRQ_CLEAR_MASK , \ 
; ..\mcal_src\Irq.c	  1268                                              (IRQ_ETH_SR_TOS |IRQ_ETH_SR_PRIO));
; ..\mcal_src\Irq.c	  1269    #endif
; ..\mcal_src\Irq.c	  1270  }
	ret
.L348:
	
__IrqEthernet_Init_function_end:
	.size	IrqEthernet_Init,__IrqEthernet_Init_function_end-IrqEthernet_Init
.L114:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqDma_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqDma_Init

; ..\mcal_src\Irq.c	  1271  
; ..\mcal_src\Irq.c	  1272  /*******************************************************************************
; ..\mcal_src\Irq.c	  1273  ** Syntax :  void IrqDma_Init(void)                                           **
; ..\mcal_src\Irq.c	  1274  **                                                                            **
; ..\mcal_src\Irq.c	  1275  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1276  **                                                                            **
; ..\mcal_src\Irq.c	  1277  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1278  **                                                                            **
; ..\mcal_src\Irq.c	  1279  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1280  **                                                                            **
; ..\mcal_src\Irq.c	  1281  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1282  **                                                                            **
; ..\mcal_src\Irq.c	  1283  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1284  **                                                                            **
; ..\mcal_src\Irq.c	  1285  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1286  **                                                                            **
; ..\mcal_src\Irq.c	  1287  ** Description : To set the interrupt priority for                            **
; ..\mcal_src\Irq.c	  1288  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1289  **                                                                            **
; ..\mcal_src\Irq.c	  1290  *******************************************************************************/
; ..\mcal_src\Irq.c	  1291  
; ..\mcal_src\Irq.c	  1292  void IrqDma_Init(void)
; Function IrqDma_Init
.L43:
IrqDma_Init:	.type	func

; ..\mcal_src\Irq.c	  1293  {
; ..\mcal_src\Irq.c	  1294    #if (IRQ_DMA_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1295  
; ..\mcal_src\Irq.c	  1296    SRC_DMAERR.U |= (IRQ_DMA_ERR_SR_TOS | IRQ_DMA_ERR_SR_PRIO);
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf00384f0)
.L543:
	st.w	[a15]@los(0xf00384f0),d15
.L350:

; ..\mcal_src\Irq.c	  1297  
; ..\mcal_src\Irq.c	  1298    #if (IRQ_DMA_CH0TO15_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1299    IRQ_SFR_MODIFY32 (SRC_DMACH0.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038500)
.L471:
	st.w	[a15]@los(0xf0038500),d15
.L351:

; ..\mcal_src\Irq.c	  1300                            (IRQ_DMA_CHANNEL0_SR_TOS |IRQ_DMA_CHANNEL0_SR_PRIO));
; ..\mcal_src\Irq.c	  1301    IRQ_SFR_MODIFY32 (SRC_DMACH1.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038504)
.L472:
	st.w	[a15]@los(0xf0038504),d15
.L353:

; ..\mcal_src\Irq.c	  1302                            (IRQ_DMA_CHANNEL1_SR_TOS |IRQ_DMA_CHANNEL1_SR_PRIO));
; ..\mcal_src\Irq.c	  1303    IRQ_SFR_MODIFY32 (SRC_DMACH2.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038508)
.L473:
	st.w	[a15]@los(0xf0038508),d15
.L355:

; ..\mcal_src\Irq.c	  1304                            (IRQ_DMA_CHANNEL2_SR_TOS |IRQ_DMA_CHANNEL2_SR_PRIO));
; ..\mcal_src\Irq.c	  1305    IRQ_SFR_MODIFY32 (SRC_DMACH3.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf003850c)
.L474:
	st.w	[a15]@los(0xf003850c),d15
.L357:

; ..\mcal_src\Irq.c	  1306                           (IRQ_DMA_CHANNEL3_SR_TOS |IRQ_DMA_CHANNEL3_SR_PRIO));
; ..\mcal_src\Irq.c	  1307    IRQ_SFR_MODIFY32 (SRC_DMACH4.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038510)
.L475:
	st.w	[a15]@los(0xf0038510),d15
.L359:

; ..\mcal_src\Irq.c	  1308                            (IRQ_DMA_CHANNEL4_SR_TOS |IRQ_DMA_CHANNEL4_SR_PRIO));
; ..\mcal_src\Irq.c	  1309    IRQ_SFR_MODIFY32 (SRC_DMACH5.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038514)
.L476:
	st.w	[a15]@los(0xf0038514),d15
.L361:

; ..\mcal_src\Irq.c	  1310                            (IRQ_DMA_CHANNEL5_SR_TOS |IRQ_DMA_CHANNEL5_SR_PRIO));
; ..\mcal_src\Irq.c	  1311    IRQ_SFR_MODIFY32 (SRC_DMACH6.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038518)
.L477:
	st.w	[a15]@los(0xf0038518),d15
.L363:

; ..\mcal_src\Irq.c	  1312                            (IRQ_DMA_CHANNEL6_SR_TOS |IRQ_DMA_CHANNEL6_SR_PRIO));
; ..\mcal_src\Irq.c	  1313    IRQ_SFR_MODIFY32 (SRC_DMACH7.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf003851c)
.L478:
	st.w	[a15]@los(0xf003851c),d15
.L365:

; ..\mcal_src\Irq.c	  1314                            (IRQ_DMA_CHANNEL7_SR_TOS |IRQ_DMA_CHANNEL7_SR_PRIO));
; ..\mcal_src\Irq.c	  1315    IRQ_SFR_MODIFY32 (SRC_DMACH8.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038520)
.L479:
	st.w	[a15]@los(0xf0038520),d15
.L367:

; ..\mcal_src\Irq.c	  1316                            (IRQ_DMA_CHANNEL8_SR_TOS |IRQ_DMA_CHANNEL8_SR_PRIO));
; ..\mcal_src\Irq.c	  1317    IRQ_SFR_MODIFY32 (SRC_DMACH9.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038524)
.L480:
	st.w	[a15]@los(0xf0038524),d15
.L369:

; ..\mcal_src\Irq.c	  1318                             (IRQ_DMA_CHANNEL9_SR_TOS |IRQ_DMA_CHANNEL9_SR_PRIO));
; ..\mcal_src\Irq.c	  1319    IRQ_SFR_MODIFY32 (SRC_DMACH10.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038528)
.L481:
	st.w	[a15]@los(0xf0038528),d15
.L371:

; ..\mcal_src\Irq.c	  1320                           (IRQ_DMA_CHANNEL10_SR_TOS |IRQ_DMA_CHANNEL10_SR_PRIO));
; ..\mcal_src\Irq.c	  1321    IRQ_SFR_MODIFY32 (SRC_DMACH11.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf003852c)
.L482:
	st.w	[a15]@los(0xf003852c),d15
.L373:

; ..\mcal_src\Irq.c	  1322                           (IRQ_DMA_CHANNEL11_SR_TOS |IRQ_DMA_CHANNEL11_SR_PRIO));
; ..\mcal_src\Irq.c	  1323    IRQ_SFR_MODIFY32 (SRC_DMACH12.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038530)
.L483:
	st.w	[a15]@los(0xf0038530),d15
.L375:

; ..\mcal_src\Irq.c	  1324                           (IRQ_DMA_CHANNEL12_SR_TOS |IRQ_DMA_CHANNEL12_SR_PRIO));
; ..\mcal_src\Irq.c	  1325    IRQ_SFR_MODIFY32 (SRC_DMACH13.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038534)
.L484:
	st.w	[a15]@los(0xf0038534),d15
.L377:

; ..\mcal_src\Irq.c	  1326                          (IRQ_DMA_CHANNEL13_SR_TOS |IRQ_DMA_CHANNEL13_SR_PRIO));
; ..\mcal_src\Irq.c	  1327    IRQ_SFR_MODIFY32 (SRC_DMACH14.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038538)
.L485:
	st.w	[a15]@los(0xf0038538),d15
.L379:

; ..\mcal_src\Irq.c	  1328                           (IRQ_DMA_CHANNEL14_SR_TOS |IRQ_DMA_CHANNEL14_SR_PRIO));
; ..\mcal_src\Irq.c	  1329    IRQ_SFR_MODIFY32 (SRC_DMACH15.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf003853c)
.L486:
	st.w	[a15]@los(0xf003853c),d15
.L381:

; ..\mcal_src\Irq.c	  1330                          (IRQ_DMA_CHANNEL15_SR_TOS |IRQ_DMA_CHANNEL15_SR_PRIO));
; ..\mcal_src\Irq.c	  1331    #endif
; ..\mcal_src\Irq.c	  1332  
; ..\mcal_src\Irq.c	  1333    #endif/*End of IRQ_DMA_EXIST*/
; ..\mcal_src\Irq.c	  1334  }
	ret
.L349:
	
__IrqDma_Init_function_end:
	.size	IrqDma_Init,__IrqDma_Init_function_end-IrqDma_Init
.L119:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqStm_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqStm_Init

; ..\mcal_src\Irq.c	  1335  #endif /* (IFX_MCAL_USED == STD_ON) */
; ..\mcal_src\Irq.c	  1336  /*******************************************************************************
; ..\mcal_src\Irq.c	  1337  ** Syntax :  void IrqStm_Init(void)                                           **
; ..\mcal_src\Irq.c	  1338  **                                                                            **
; ..\mcal_src\Irq.c	  1339  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1340  **                                                                            **
; ..\mcal_src\Irq.c	  1341  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1342  **                                                                            **
; ..\mcal_src\Irq.c	  1343  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1344  **                                                                            **
; ..\mcal_src\Irq.c	  1345  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1346  **                                                                            **
; ..\mcal_src\Irq.c	  1347  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1348  **                                                                            **
; ..\mcal_src\Irq.c	  1349  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1350  **                                                                            **
; ..\mcal_src\Irq.c	  1351  ** Description : To set the interrupt priority for                            **
; ..\mcal_src\Irq.c	  1352  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1353  **                                                                            **
; ..\mcal_src\Irq.c	  1354  *******************************************************************************/
; ..\mcal_src\Irq.c	  1355  
; ..\mcal_src\Irq.c	  1356  void IrqStm_Init(void)
; Function IrqStm_Init
.L45:
IrqStm_Init:	.type	func

; ..\mcal_src\Irq.c	  1357  {
; ..\mcal_src\Irq.c	  1358    #if (IRQ_STM_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1359  
; ..\mcal_src\Irq.c	  1360    #if (IRQ_STM0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1361    IRQ_SFR_MODIFY32 (SRC_STM0SR0.U, IRQ_CLEAR_MASK ,\ 
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf0038490)
.L487:
	st.w	[a15]@los(0xf0038490),d15
.L388:

; ..\mcal_src\Irq.c	  1362                                         (IRQ_STM0_SR0_TOS |IRQ_STM0_SR0_PRIO));
; ..\mcal_src\Irq.c	  1363    IRQ_SFR_MODIFY32 (SRC_STM0SR1.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038494)
.L488:
	st.w	[a15]@los(0xf0038494),d15
.L390:

; ..\mcal_src\Irq.c	  1364                                         (IRQ_STM0_SR1_TOS |IRQ_STM0_SR1_PRIO));
; ..\mcal_src\Irq.c	  1365    #endif
; ..\mcal_src\Irq.c	  1366  
; ..\mcal_src\Irq.c	  1367    #endif
; ..\mcal_src\Irq.c	  1368  }
	ret
.L387:
	
__IrqStm_Init_function_end:
	.size	IrqStm_Init,__IrqStm_Init_function_end-IrqStm_Init
.L144:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqScu_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqScu_Init

; ..\mcal_src\Irq.c	  1369  
; ..\mcal_src\Irq.c	  1370  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Irq.c	  1371  /*******************************************************************************
; ..\mcal_src\Irq.c	  1372  ** Syntax :  void IrqScu_Init(void)                                           **
; ..\mcal_src\Irq.c	  1373  **                                                                            **
; ..\mcal_src\Irq.c	  1374  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1375  **                                                                            **
; ..\mcal_src\Irq.c	  1376  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1377  **                                                                            **
; ..\mcal_src\Irq.c	  1378  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1379  **                                                                            **
; ..\mcal_src\Irq.c	  1380  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1381  **                                                                            **
; ..\mcal_src\Irq.c	  1382  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1383  **                                                                            **
; ..\mcal_src\Irq.c	  1384  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1385  **                                                                            **
; ..\mcal_src\Irq.c	  1386  ** Description : To set the interrupt priority for                            **
; ..\mcal_src\Irq.c	  1387  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1388  **                                                                            **
; ..\mcal_src\Irq.c	  1389  *******************************************************************************/
; ..\mcal_src\Irq.c	  1390  
; ..\mcal_src\Irq.c	  1391  void IrqScu_Init(void)
; Function IrqScu_Init
.L47:
IrqScu_Init:	.type	func

; ..\mcal_src\Irq.c	  1392  {
; ..\mcal_src\Irq.c	  1393    #if (IRQ_SCU_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1394    IRQ_SFR_MODIFY32 (SRC_SCUDTS.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1395                           (IRQ_SCU_DTS_BUSY_SR_TOS |IRQ_SCU_DTS_BUSY_SR_PRIO));
; ..\mcal_src\Irq.c	  1396    IRQ_SFR_MODIFY32 (SRC_SCUERU0.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1397                                    (IRQ_SCU_ERU_SR0_TOS |IRQ_SCU_ERU_SR0_PRIO));
; ..\mcal_src\Irq.c	  1398    IRQ_SFR_MODIFY32 (SRC_SCUERU1.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1399                                    (IRQ_SCU_ERU_SR1_TOS |IRQ_SCU_ERU_SR1_PRIO));
; ..\mcal_src\Irq.c	  1400    IRQ_SFR_MODIFY32 (SRC_SCUERU2.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1401                                     (IRQ_SCU_ERU_SR2_TOS |IRQ_SCU_ERU_SR2_PRIO));
; ..\mcal_src\Irq.c	  1402    IRQ_SFR_MODIFY32 (SRC_SCUERU3.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1403                                     (IRQ_SCU_ERU_SR3_TOS |IRQ_SCU_ERU_SR3_PRIO));
; ..\mcal_src\Irq.c	  1404    #endif
; ..\mcal_src\Irq.c	  1405  
; ..\mcal_src\Irq.c	  1406  
; ..\mcal_src\Irq.c	  1407  }
	ret
.L383:
	
__IrqScu_Init_function_end:
	.size	IrqScu_Init,__IrqScu_Init_function_end-IrqScu_Init
.L124:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqPmu_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqPmu_Init

; ..\mcal_src\Irq.c	  1408  
; ..\mcal_src\Irq.c	  1409  /*******************************************************************************
; ..\mcal_src\Irq.c	  1410  ** Syntax :  void IrqPmu_Init(void)                                           **
; ..\mcal_src\Irq.c	  1411  **                                                                            **
; ..\mcal_src\Irq.c	  1412  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1413  **                                                                            **
; ..\mcal_src\Irq.c	  1414  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1415  **                                                                            **
; ..\mcal_src\Irq.c	  1416  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1417  **                                                                            **
; ..\mcal_src\Irq.c	  1418  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1419  **                                                                            **
; ..\mcal_src\Irq.c	  1420  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1421  **                                                                            **
; ..\mcal_src\Irq.c	  1422  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1423  **                                                                            **
; ..\mcal_src\Irq.c	  1424  ** Description : To set the interrupt priority for                            **
; ..\mcal_src\Irq.c	  1425  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1426  **                                                                            **
; ..\mcal_src\Irq.c	  1427  *******************************************************************************/
; ..\mcal_src\Irq.c	  1428  void IrqPmu_Init(void)
; Function IrqPmu_Init
.L49:
IrqPmu_Init:	.type	func

; ..\mcal_src\Irq.c	  1429  {
; ..\mcal_src\Irq.c	  1430    #if (IRQ_PMU0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1431  
; ..\mcal_src\Irq.c	  1432    #if (IRQ_PMU0_SR0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1433    IRQ_SFR_MODIFY32 (SRC_PMU00.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1434                                       (IRQ_PMU0_SR0_TOS |IRQ_PMU0_SR0_PRIO));
; ..\mcal_src\Irq.c	  1435    #endif
; ..\mcal_src\Irq.c	  1436  
; ..\mcal_src\Irq.c	  1437    #if (IRQ_PMU0_SR1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1438    IRQ_SFR_MODIFY32 (SRC_PMU01.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1439                                       (IRQ_PMU0_SR1_TOS |IRQ_PMU0_SR1_PRIO));
; ..\mcal_src\Irq.c	  1440    #endif
; ..\mcal_src\Irq.c	  1441  
; ..\mcal_src\Irq.c	  1442    #endif
; ..\mcal_src\Irq.c	  1443  }
	ret
.L384:
	
__IrqPmu_Init_function_end:
	.size	IrqPmu_Init,__IrqPmu_Init_function_end-IrqPmu_Init
.L129:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqSent_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqSent_Init

; ..\mcal_src\Irq.c	  1444  
; ..\mcal_src\Irq.c	  1445  
; ..\mcal_src\Irq.c	  1446  /*******************************************************************************
; ..\mcal_src\Irq.c	  1447  ** Syntax :  void IrqSent_Init(void)                                          **
; ..\mcal_src\Irq.c	  1448  **                                                                            **
; ..\mcal_src\Irq.c	  1449  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1450  **                                                                            **
; ..\mcal_src\Irq.c	  1451  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1452  **                                                                            **
; ..\mcal_src\Irq.c	  1453  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1454  **                                                                            **
; ..\mcal_src\Irq.c	  1455  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1456  **                                                                            **
; ..\mcal_src\Irq.c	  1457  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1458  **                                                                            **
; ..\mcal_src\Irq.c	  1459  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1460  **                                                                            **
; ..\mcal_src\Irq.c	  1461  ** Description : To set the interrupt priority for                            **
; ..\mcal_src\Irq.c	  1462  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1463  **                                                                            **
; ..\mcal_src\Irq.c	  1464  *******************************************************************************/
; ..\mcal_src\Irq.c	  1465  
; ..\mcal_src\Irq.c	  1466  void IrqSent_Init(void)
; Function IrqSent_Init
.L51:
IrqSent_Init:	.type	func

; ..\mcal_src\Irq.c	  1467  {
; ..\mcal_src\Irq.c	  1468    #if (IRQ_SENT_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1469  
; ..\mcal_src\Irq.c	  1470    #if (IRQ_SENT0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1471    IRQ_SFR_MODIFY32 (SRC_SENT0.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1472                                         (IRQ_SENT_SR0_TOS |IRQ_SENT_SR0_PRIO));
; ..\mcal_src\Irq.c	  1473    #endif
; ..\mcal_src\Irq.c	  1474  
; ..\mcal_src\Irq.c	  1475    #if (IRQ_SENT1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1476    IRQ_SFR_MODIFY32 (SRC_SENT1.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1477                                         (IRQ_SENT_SR1_TOS |IRQ_SENT_SR1_PRIO));
; ..\mcal_src\Irq.c	  1478    #endif
; ..\mcal_src\Irq.c	  1479  
; ..\mcal_src\Irq.c	  1480    #if (IRQ_SENT2_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1481    IRQ_SFR_MODIFY32 (SRC_SENT2.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1482                                         (IRQ_SENT_SR2_TOS |IRQ_SENT_SR2_PRIO));
; ..\mcal_src\Irq.c	  1483    #endif
; ..\mcal_src\Irq.c	  1484  
; ..\mcal_src\Irq.c	  1485    #if (IRQ_SENT3_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1486    IRQ_SFR_MODIFY32 (SRC_SENT3.U, IRQ_CLEAR_MASK ,\ 
; ..\mcal_src\Irq.c	  1487                                         (IRQ_SENT_SR3_TOS |IRQ_SENT_SR3_PRIO));
; ..\mcal_src\Irq.c	  1488    #endif
; ..\mcal_src\Irq.c	  1489  
; ..\mcal_src\Irq.c	  1490    #endif
; ..\mcal_src\Irq.c	  1491  }
	ret
.L385:
	
__IrqSent_Init_function_end:
	.size	IrqSent_Init,__IrqSent_Init_function_end-IrqSent_Init
.L134:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('IrqHsm_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	IrqHsm_Init

; ..\mcal_src\Irq.c	  1492  
; ..\mcal_src\Irq.c	  1493  /*******************************************************************************
; ..\mcal_src\Irq.c	  1494  ** Syntax :  void IrqHsm_Init(void)                                           **
; ..\mcal_src\Irq.c	  1495  **                                                                            **
; ..\mcal_src\Irq.c	  1496  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1497  **                                                                            **
; ..\mcal_src\Irq.c	  1498  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1499  **                                                                            **
; ..\mcal_src\Irq.c	  1500  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1501  **                                                                            **
; ..\mcal_src\Irq.c	  1502  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1503  **                                                                            **
; ..\mcal_src\Irq.c	  1504  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1505  **                                                                            **
; ..\mcal_src\Irq.c	  1506  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1507  **                                                                            **
; ..\mcal_src\Irq.c	  1508  ** Description : To set the interrupt priority for various                    **
; ..\mcal_src\Irq.c	  1509  **               service nodes according to priority configurartion.          **
; ..\mcal_src\Irq.c	  1510  **                                                                            **
; ..\mcal_src\Irq.c	  1511  *******************************************************************************/
; ..\mcal_src\Irq.c	  1512  void IrqHsm_Init(void)
; Function IrqHsm_Init
.L53:
IrqHsm_Init:	.type	func

; ..\mcal_src\Irq.c	  1513  {
; ..\mcal_src\Irq.c	  1514    /* Interrupt Priority is written to the SRC registers*/
; ..\mcal_src\Irq.c	  1515  
; ..\mcal_src\Irq.c	  1516    #if (IRQ_HSM0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1517    IRQ_SFR_MODIFY32 (SRC_HSM0.U, IRQ_CLEAR_MASK ,\ 
	movh.a	a15,#61444
	ld.w	d15,[a15]@los(0xf0038cc0)
.L489:
	st.w	[a15]@los(0xf0038cc0),d15
.L212:

; ..\mcal_src\Irq.c	  1518                                            (IRQ_HSM_SR0_TOS |IRQ_HSM_SR0_PRIO));
; ..\mcal_src\Irq.c	  1519    #endif
; ..\mcal_src\Irq.c	  1520  
; ..\mcal_src\Irq.c	  1521    #if (IRQ_HSM1_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1522    IRQ_SFR_MODIFY32 (SRC_HSM1.U, IRQ_CLEAR_MASK ,\ 
	ld.w	d15,[a15]@los(0xf0038cc4)
.L490:
	st.w	[a15]@los(0xf0038cc4),d15
.L214:

; ..\mcal_src\Irq.c	  1523                                             (IRQ_HSM_SR1_TOS |IRQ_HSM_SR1_PRIO));
; ..\mcal_src\Irq.c	  1524    #endif
; ..\mcal_src\Irq.c	  1525  }
	ret
.L211:
	
__IrqHsm_Init_function_end:
	.size	IrqHsm_Init,__IrqHsm_Init_function_end-IrqHsm_Init
.L79:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Irq_ClearAllInterruptFlags')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Irq_ClearAllInterruptFlags

; ..\mcal_src\Irq.c	  1526  
; ..\mcal_src\Irq.c	  1527  /*******************************************************************************
; ..\mcal_src\Irq.c	  1528  ** Syntax :  void Irq_ClearAllInterruptFlags(void)                            **
; ..\mcal_src\Irq.c	  1529  **                                                                            **
; ..\mcal_src\Irq.c	  1530  ** Service ID:  none                                                          **
; ..\mcal_src\Irq.c	  1531  **                                                                            **
; ..\mcal_src\Irq.c	  1532  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Irq.c	  1533  **                                                                            **
; ..\mcal_src\Irq.c	  1534  ** Reentrancy:  non reentrant                                                 **
; ..\mcal_src\Irq.c	  1535  **                                                                            **
; ..\mcal_src\Irq.c	  1536  ** Parameters (in): none                                                      **
; ..\mcal_src\Irq.c	  1537  **                                                                            **
; ..\mcal_src\Irq.c	  1538  ** Parameters (out): none                                                     **
; ..\mcal_src\Irq.c	  1539  **                                                                            **
; ..\mcal_src\Irq.c	  1540  ** Return value: none                                                         **
; ..\mcal_src\Irq.c	  1541  **                                                                            **
; ..\mcal_src\Irq.c	  1542  ** Description : To clear all SRR and corresponding SRE bits.This ensures     **
; ..\mcal_src\Irq.c	  1543  ** Standby mode can be entered if no pending interrupts are available.        **
; ..\mcal_src\Irq.c	  1544  **                                                                            **
; ..\mcal_src\Irq.c	  1545  *******************************************************************************/
; ..\mcal_src\Irq.c	  1546  void Irq_ClearAllInterruptFlags(void)
; Function Irq_ClearAllInterruptFlags
.L55:
Irq_ClearAllInterruptFlags:	.type	func

; ..\mcal_src\Irq.c	  1547  {
; ..\mcal_src\Irq.c	  1548    Mcal_DisableAllInterrupts();
	disable
.L560:

; ..\mcal_src\Irq.c	  1549  
; ..\mcal_src\Irq.c	  1550    #if (IRQ_ASCLIN_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1551    Irq_ClearAsclinIntFlags();
	call	Irq_ClearAsclinIntFlags
.L561:

; ..\mcal_src\Irq.c	  1552    #endif
; ..\mcal_src\Irq.c	  1553  
; ..\mcal_src\Irq.c	  1554    #if (IRQ_CCU6_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1555    Irq_ClearCcu6IntFlags();
; ..\mcal_src\Irq.c	  1556    #endif
; ..\mcal_src\Irq.c	  1557  
; ..\mcal_src\Irq.c	  1558    #if (IRQ_GPT_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1559    Irq_ClearGptIntFlags();
	call	Irq_ClearGptIntFlags
.L562:

; ..\mcal_src\Irq.c	  1560    #endif
; ..\mcal_src\Irq.c	  1561  
; ..\mcal_src\Irq.c	  1562    #if (IRQ_GTM_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1563    Irq_ClearGtmIntFlags();
	call	Irq_ClearGtmIntFlags
.L563:

; ..\mcal_src\Irq.c	  1564    #endif
; ..\mcal_src\Irq.c	  1565  
; ..\mcal_src\Irq.c	  1566    #if (IRQ_CAN_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1567    Irq_ClearCanIntFlags();
; ..\mcal_src\Irq.c	  1568    #endif
; ..\mcal_src\Irq.c	  1569  
; ..\mcal_src\Irq.c	  1570    #if (IRQ_GPSRGROUP_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1571    Irq_ClearGpsrGroupIntFlags();
	call	Irq_ClearGpsrGroupIntFlags
.L564:

; ..\mcal_src\Irq.c	  1572    #endif
; ..\mcal_src\Irq.c	  1573  
; ..\mcal_src\Irq.c	  1574    #if (IRQ_QSPI_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1575    Irq_ClearSpiIntFlags();
	call	Irq_ClearSpiIntFlags
.L565:

; ..\mcal_src\Irq.c	  1576    #endif
; ..\mcal_src\Irq.c	  1577  
; ..\mcal_src\Irq.c	  1578    #if (IRQ_ADC_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1579    Irq_ClearAdcIntFlags();
	call	Irq_ClearAdcIntFlags
.L566:

; ..\mcal_src\Irq.c	  1580    #endif
; ..\mcal_src\Irq.c	  1581  
; ..\mcal_src\Irq.c	  1582    #if (IRQ_FLEXRAY_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1583    Irq_ClearFlexrayIntFlags();
; ..\mcal_src\Irq.c	  1584    #endif
; ..\mcal_src\Irq.c	  1585  
; ..\mcal_src\Irq.c	  1586    #if (IRQ_ETH_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1587    Irq_ClearEthernetIntFlags();
; ..\mcal_src\Irq.c	  1588    #endif
; ..\mcal_src\Irq.c	  1589  
; ..\mcal_src\Irq.c	  1590    #if (IRQ_DMA_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1591    Irq_ClearDmaIntFlags();
	call	Irq_ClearDmaIntFlags
.L567:

; ..\mcal_src\Irq.c	  1592    #endif
; ..\mcal_src\Irq.c	  1593  
; ..\mcal_src\Irq.c	  1594    #if (IRQ_STM_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1595    Irq_ClearStmIntFlags();
	call	Irq_ClearStmIntFlags
.L568:

; ..\mcal_src\Irq.c	  1596    #endif
; ..\mcal_src\Irq.c	  1597  
; ..\mcal_src\Irq.c	  1598    #if (IRQ_SCU_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1599    Irq_ClearScuIntFlags();
; ..\mcal_src\Irq.c	  1600    #endif
; ..\mcal_src\Irq.c	  1601  
; ..\mcal_src\Irq.c	  1602    #if (IRQ_PMU0_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1603    Irq_ClearPmuIntFlags();
; ..\mcal_src\Irq.c	  1604    #endif
; ..\mcal_src\Irq.c	  1605  
; ..\mcal_src\Irq.c	  1606    #if (IRQ_SENT_EXIST == STD_ON)
; ..\mcal_src\Irq.c	  1607    Irq_ClearSentIntFlags();
; ..\mcal_src\Irq.c	  1608    #endif
; ..\mcal_src\Irq.c	  1609  
; ..\mcal_src\Irq.c	  1610    #if ((IRQ_HSM0_EXIST == STD_ON) || (IRQ_HSM1_EXIST == STD_ON))
; ..\mcal_src\Irq.c	  1611    Irq_ClearHsmIntFlags();
	j	Irq_ClearHsmIntFlags
.L386:
	
__Irq_ClearAllInterruptFlags_function_end:
	.size	Irq_ClearAllInterruptFlags,__Irq_ClearAllInterruptFlags_function_end-Irq_ClearAllInterruptFlags
.L139:
	; End of function
	
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearAsclinIntFlags'
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearGptIntFlags'
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearGtmIntFlags'
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearGpsrGroupIntFlags'
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearSpiIntFlags'
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearAdcIntFlags'
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearDmaIntFlags'
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearStmIntFlags'
	.calls	'Irq_ClearAllInterruptFlags','Irq_ClearHsmIntFlags'
	.calls	'Irq_ClearAsclinIntFlags','.cocofun_1'
	.calls	'Irq_ClearGptIntFlags','.cocofun_1'
	.calls	'Irq_ClearGtmIntFlags','.cocofun_1'
	.calls	'Irq_ClearGpsrGroupIntFlags','.cocofun_1'
	.calls	'Irq_ClearSpiIntFlags','.cocofun_1'
	.calls	'Irq_ClearAdcIntFlags','.cocofun_1'
	.calls	'Irq_ClearDmaIntFlags','.cocofun_1'
	.calls	'Irq_ClearStmIntFlags','.cocofun_1'
	.calls	'Irq_ClearHsmIntFlags','.cocofun_1'
	.calls	'Irq_ClearAsclinIntFlags','',0
	.calls	'.cocofun_1','',0
	.calls	'Irq_ClearGptIntFlags','',0
	.calls	'Irq_ClearGtmIntFlags','',0
	.calls	'Irq_ClearGpsrGroupIntFlags','',0
	.calls	'Irq_ClearSpiIntFlags','',0
	.calls	'Irq_ClearAdcIntFlags','',0
	.calls	'Irq_ClearDmaIntFlags','',0
	.calls	'Irq_ClearStmIntFlags','',0
	.calls	'Irq_ClearHsmIntFlags','',0
	.calls	'IrqAscLin_Init','',0
	.calls	'IrqCcu6_Init','',0
	.calls	'IrqGpt_Init','',0
	.calls	'IrqGtm_Init','',0
	.calls	'IrqCan_Init','',0
	.calls	'IrqGpsrGroup_Init','',0
	.calls	'IrqSpi_Init','',0
	.calls	'IrqAdc_Init','',0
	.calls	'IrqFlexray_Init','',0
	.calls	'IrqEthernet_Init','',0
	.calls	'IrqDma_Init','',0
	.calls	'IrqStm_Init','',0
	.calls	'IrqScu_Init','',0
	.calls	'IrqPmu_Init','',0
	.calls	'IrqSent_Init','',0
	.calls	'IrqHsm_Init','',0
	.calls	'Irq_ClearAllInterruptFlags','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L57:
	.word	5208
	.half	3
	.word	.L58
	.byte	4
.L56:
	.byte	1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L59
	.byte	2
	.byte	'__disable',0,1,1,1,1
.L197:
	.byte	3
	.byte	'unsigned long int',0,4,7,4
	.byte	'void',0,5
	.word	208
	.byte	6
	.byte	'__prof_adm',0,1,1,1
	.word	214
	.byte	7,1,5
	.word	238
	.byte	6
	.byte	'__codeptr',0,1,1,1
	.word	240
	.byte	8
	.byte	'_Ifx_SRC_SRCR_Bits',0,2,45,16,4,3
	.byte	'unsigned char',0,1,8,9
	.byte	'SRPN',0,1
	.word	287
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	287
	.byte	2,6,2,35,1,9
	.byte	'SRE',0,1
	.word	287
	.byte	1,5,2,35,1,9
	.byte	'TOS',0,1
	.word	287
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	287
	.byte	4,0,2,35,1,9
	.byte	'ECC',0,1
	.word	287
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	287
	.byte	3,0,2,35,2,9
	.byte	'SRR',0,1
	.word	287
	.byte	1,7,2,35,3,9
	.byte	'CLRR',0,1
	.word	287
	.byte	1,6,2,35,3,9
	.byte	'SETR',0,1
	.word	287
	.byte	1,5,2,35,3,9
	.byte	'IOV',0,1
	.word	287
	.byte	1,4,2,35,3,9
	.byte	'IOVCLR',0,1
	.word	287
	.byte	1,3,2,35,3,9
	.byte	'SWS',0,1
	.word	287
	.byte	1,2,2,35,3,9
	.byte	'SWSCLR',0,1
	.word	287
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	287
	.byte	1,0,2,35,3,0,6
	.byte	'Ifx_SRC_SRCR_Bits',0,2,62,3
	.word	263
	.byte	10,2,70,9,4,3
	.byte	'unsigned int',0,4,7,11
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,3
	.byte	'int',0,4,5,11
	.byte	'I',0,4
	.word	628
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	263
	.byte	2,35,0,0,6
	.byte	'Ifx_SRC_SRCR',0,2,75,3
	.word	596
	.byte	8
	.byte	'_Ifx_SRC_ASCLIN',0,2,86,25,12,11
	.byte	'TX',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	596
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	596
	.byte	2,35,8,0,12
	.word	679
	.byte	6
	.byte	'Ifx_SRC_ASCLIN',0,2,91,3
	.word	738
	.byte	8
	.byte	'_Ifx_SRC_BCUSPB',0,2,94,25,4,11
	.byte	'SBSRC',0,4
	.word	596
	.byte	2,35,0,0,12
	.word	766
	.byte	6
	.byte	'Ifx_SRC_BCUSPB',0,2,97,3
	.word	803
	.byte	8
	.byte	'_Ifx_SRC_CAN',0,2,100,25,64,13,64
	.word	596
	.byte	14,15,0,11
	.byte	'INT',0,64
	.word	849
	.byte	2,35,0,0,12
	.word	831
	.byte	6
	.byte	'Ifx_SRC_CAN',0,2,103,3
	.word	872
	.byte	8
	.byte	'_Ifx_SRC_CAN1',0,2,106,25,32,13,32
	.word	596
	.byte	14,7,0,11
	.byte	'INT',0,32
	.word	916
	.byte	2,35,0,0,12
	.word	897
	.byte	6
	.byte	'Ifx_SRC_CAN1',0,2,109,3
	.word	939
	.byte	8
	.byte	'_Ifx_SRC_CCU6',0,2,112,25,16,11
	.byte	'SR0',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	596
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	596
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	596
	.byte	2,35,12,0,12
	.word	965
	.byte	6
	.byte	'Ifx_SRC_CCU6',0,2,118,3
	.word	1037
	.byte	8
	.byte	'_Ifx_SRC_CERBERUS',0,2,121,25,8,13,8
	.word	596
	.byte	14,1,0,11
	.byte	'SR',0,8
	.word	1086
	.byte	2,35,0,0,12
	.word	1063
	.byte	6
	.byte	'Ifx_SRC_CERBERUS',0,2,124,3
	.word	1108
	.byte	8
	.byte	'_Ifx_SRC_CPU',0,2,127,25,32,11
	.byte	'SBSRC',0,4
	.word	596
	.byte	2,35,0,13,28
	.word	287
	.byte	14,27,0,11
	.byte	'reserved_4',0,28
	.word	1171
	.byte	2,35,4,0,12
	.word	1138
	.byte	6
	.byte	'Ifx_SRC_CPU',0,2,131,1,3
	.word	1201
	.byte	8
	.byte	'_Ifx_SRC_DMA',0,2,134,1,25,80,11
	.byte	'ERR',0,4
	.word	596
	.byte	2,35,0,13,12
	.word	287
	.byte	14,11,0,11
	.byte	'reserved_4',0,12
	.word	1259
	.byte	2,35,4,11
	.byte	'CH',0,64
	.word	849
	.byte	2,35,16,0,12
	.word	1227
	.byte	6
	.byte	'Ifx_SRC_DMA',0,2,139,1,3
	.word	1301
	.byte	8
	.byte	'_Ifx_SRC_EMEM',0,2,142,1,25,4,11
	.byte	'SR',0,4
	.word	596
	.byte	2,35,0,0,12
	.word	1327
	.byte	6
	.byte	'Ifx_SRC_EMEM',0,2,145,1,3
	.word	1360
	.byte	8
	.byte	'_Ifx_SRC_ERAY',0,2,148,1,25,80,11
	.byte	'INT',0,8
	.word	1086
	.byte	2,35,0,11
	.byte	'TINT',0,8
	.word	1086
	.byte	2,35,8,11
	.byte	'NDAT',0,8
	.word	1086
	.byte	2,35,16,11
	.byte	'MBSC',0,8
	.word	1086
	.byte	2,35,24,11
	.byte	'OBUSY',0,4
	.word	596
	.byte	2,35,32,11
	.byte	'IBUSY',0,4
	.word	596
	.byte	2,35,36,13,40
	.word	287
	.byte	14,39,0,11
	.byte	'reserved_28',0,40
	.word	1492
	.byte	2,35,40,0,12
	.word	1387
	.byte	6
	.byte	'Ifx_SRC_ERAY',0,2,157,1,3
	.word	1523
	.byte	8
	.byte	'_Ifx_SRC_ETH',0,2,160,1,25,4,11
	.byte	'SR',0,4
	.word	596
	.byte	2,35,0,0,12
	.word	1550
	.byte	6
	.byte	'Ifx_SRC_ETH',0,2,163,1,3
	.word	1582
	.byte	8
	.byte	'_Ifx_SRC_EVR',0,2,166,1,25,8,11
	.byte	'WUT',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'SCDC',0,4
	.word	596
	.byte	2,35,4,0,12
	.word	1608
	.byte	6
	.byte	'Ifx_SRC_EVR',0,2,170,1,3
	.word	1655
	.byte	8
	.byte	'_Ifx_SRC_FFT',0,2,173,1,25,12,11
	.byte	'DONE',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'ERR',0,4
	.word	596
	.byte	2,35,4,11
	.byte	'RFS',0,4
	.word	596
	.byte	2,35,8,0,12
	.word	1681
	.byte	6
	.byte	'Ifx_SRC_FFT',0,2,178,1,3
	.word	1741
	.byte	8
	.byte	'_Ifx_SRC_GPSR',0,2,181,1,25,128,12,11
	.byte	'SR0',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	596
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	596
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	596
	.byte	2,35,12,13,240,11
	.word	287
	.byte	14,239,11,0,11
	.byte	'reserved_10',0,240,11
	.word	1840
	.byte	2,35,16,0,12
	.word	1767
	.byte	6
	.byte	'Ifx_SRC_GPSR',0,2,188,1,3
	.word	1874
	.byte	8
	.byte	'_Ifx_SRC_GPT12',0,2,191,1,25,48,11
	.byte	'CIRQ',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'T2',0,4
	.word	596
	.byte	2,35,4,11
	.byte	'T3',0,4
	.word	596
	.byte	2,35,8,11
	.byte	'T4',0,4
	.word	596
	.byte	2,35,12,11
	.byte	'T5',0,4
	.word	596
	.byte	2,35,16,11
	.byte	'T6',0,4
	.word	596
	.byte	2,35,20,13,24
	.word	287
	.byte	14,23,0,11
	.byte	'reserved_18',0,24
	.word	1996
	.byte	2,35,24,0,12
	.word	1901
	.byte	6
	.byte	'Ifx_SRC_GPT12',0,2,200,1,3
	.word	2027
	.byte	8
	.byte	'_Ifx_SRC_GTM',0,2,203,1,25,192,11,11
	.byte	'AEIIRQ',0,4
	.word	596
	.byte	2,35,0,13,236,2
	.word	287
	.byte	14,235,2,0,11
	.byte	'reserved_4',0,236,2
	.word	2091
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	596
	.byte	3,35,240,2,11
	.byte	'reserved_174',0,12
	.word	1259
	.byte	3,35,244,2,13,32
	.word	916
	.byte	14,0,0,11
	.byte	'TIM',0,32
	.word	2160
	.byte	3,35,128,3,13,224,7
	.word	287
	.byte	14,223,7,0,11
	.byte	'reserved_1A0',0,224,7
	.word	2183
	.byte	3,35,160,3,13,64
	.word	916
	.byte	14,1,0,11
	.byte	'TOM',0,64
	.word	2218
	.byte	3,35,128,11,0,12
	.word	2055
	.byte	6
	.byte	'Ifx_SRC_GTM',0,2,212,1,3
	.word	2242
	.byte	8
	.byte	'_Ifx_SRC_HSM',0,2,215,1,25,8,11
	.byte	'HSM',0,8
	.word	1086
	.byte	2,35,0,0,12
	.word	2268
	.byte	6
	.byte	'Ifx_SRC_HSM',0,2,218,1,3
	.word	2301
	.byte	8
	.byte	'_Ifx_SRC_LMU',0,2,221,1,25,4,11
	.byte	'SR',0,4
	.word	596
	.byte	2,35,0,0,12
	.word	2327
	.byte	6
	.byte	'Ifx_SRC_LMU',0,2,224,1,3
	.word	2359
	.byte	8
	.byte	'_Ifx_SRC_PMU',0,2,227,1,25,4,11
	.byte	'SR',0,4
	.word	596
	.byte	2,35,0,0,12
	.word	2385
	.byte	6
	.byte	'Ifx_SRC_PMU',0,2,230,1,3
	.word	2417
	.byte	8
	.byte	'_Ifx_SRC_QSPI',0,2,233,1,25,24,11
	.byte	'TX',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	596
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	596
	.byte	2,35,8,11
	.byte	'PT',0,4
	.word	596
	.byte	2,35,12,11
	.byte	'HC',0,4
	.word	596
	.byte	2,35,16,11
	.byte	'U',0,4
	.word	596
	.byte	2,35,20,0,12
	.word	2443
	.byte	6
	.byte	'Ifx_SRC_QSPI',0,2,241,1,3
	.word	2536
	.byte	8
	.byte	'_Ifx_SRC_SCU',0,2,244,1,25,20,11
	.byte	'DTS',0,4
	.word	596
	.byte	2,35,0,13,16
	.word	596
	.byte	14,3,0,11
	.byte	'ERU',0,16
	.word	2595
	.byte	2,35,4,0,12
	.word	2563
	.byte	6
	.byte	'Ifx_SRC_SCU',0,2,248,1,3
	.word	2618
	.byte	8
	.byte	'_Ifx_SRC_SENT',0,2,251,1,25,16,11
	.byte	'SR',0,16
	.word	2595
	.byte	2,35,0,0,12
	.word	2644
	.byte	6
	.byte	'Ifx_SRC_SENT',0,2,254,1,3
	.word	2677
	.byte	8
	.byte	'_Ifx_SRC_SMU',0,2,129,2,25,12,13,12
	.word	596
	.byte	14,2,0,11
	.byte	'SR',0,12
	.word	2723
	.byte	2,35,0,0,12
	.word	2704
	.byte	6
	.byte	'Ifx_SRC_SMU',0,2,132,2,3
	.word	2745
	.byte	8
	.byte	'_Ifx_SRC_STM',0,2,135,2,25,96,11
	.byte	'SR0',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	596
	.byte	2,35,4,13,88
	.word	287
	.byte	14,87,0,11
	.byte	'reserved_8',0,88
	.word	2816
	.byte	2,35,8,0,12
	.word	2771
	.byte	6
	.byte	'Ifx_SRC_STM',0,2,140,2,3
	.word	2846
	.byte	8
	.byte	'_Ifx_SRC_VADCCG',0,2,143,2,25,192,2,11
	.byte	'SR0',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	596
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	596
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	596
	.byte	2,35,12,13,176,2
	.word	287
	.byte	14,175,2,0,11
	.byte	'reserved_10',0,176,2
	.word	2947
	.byte	2,35,16,0,12
	.word	2872
	.byte	6
	.byte	'Ifx_SRC_VADCCG',0,2,150,2,3
	.word	2981
	.byte	8
	.byte	'_Ifx_SRC_VADCG',0,2,153,2,25,16,11
	.byte	'SR0',0,4
	.word	596
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	596
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	596
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	596
	.byte	2,35,12,0,12
	.word	3010
	.byte	6
	.byte	'Ifx_SRC_VADCG',0,2,159,2,3
	.word	3084
	.byte	8
	.byte	'_Ifx_SRC_XBAR',0,2,162,2,25,4,11
	.byte	'SRC',0,4
	.word	596
	.byte	2,35,0,0,12
	.word	3112
	.byte	6
	.byte	'Ifx_SRC_XBAR',0,2,165,2,3
	.word	3146
	.byte	8
	.byte	'_Ifx_SRC_GASCLIN',0,2,178,2,25,24,13,24
	.word	679
	.byte	14,1,0,12
	.word	3196
	.byte	11
	.byte	'ASCLIN',0,24
	.word	3205
	.byte	2,35,0,0,12
	.word	3173
	.byte	6
	.byte	'Ifx_SRC_GASCLIN',0,2,181,2,3
	.word	3227
	.byte	8
	.byte	'_Ifx_SRC_GBCU',0,2,184,2,25,4,12
	.word	766
	.byte	11
	.byte	'SPB',0,4
	.word	3277
	.byte	2,35,0,0,12
	.word	3257
	.byte	6
	.byte	'Ifx_SRC_GBCU',0,2,187,2,3
	.word	3296
	.byte	8
	.byte	'_Ifx_SRC_GCAN',0,2,190,2,25,96,13,64
	.word	831
	.byte	14,0,0,12
	.word	3343
	.byte	11
	.byte	'CAN',0,64
	.word	3352
	.byte	2,35,0,13,32
	.word	897
	.byte	14,0,0,12
	.word	3370
	.byte	11
	.byte	'CAN1',0,32
	.word	3379
	.byte	2,35,64,0,12
	.word	3323
	.byte	6
	.byte	'Ifx_SRC_GCAN',0,2,194,2,3
	.word	3399
	.byte	8
	.byte	'_Ifx_SRC_GCCU6',0,2,197,2,25,32,13,32
	.word	965
	.byte	14,1,0,12
	.word	3447
	.byte	11
	.byte	'CCU6',0,32
	.word	3456
	.byte	2,35,0,0,12
	.word	3426
	.byte	6
	.byte	'Ifx_SRC_GCCU6',0,2,200,2,3
	.word	3476
	.byte	8
	.byte	'_Ifx_SRC_GCERBERUS',0,2,203,2,25,8,12
	.word	1063
	.byte	11
	.byte	'CERBERUS',0,8
	.word	3529
	.byte	2,35,0,0,12
	.word	3504
	.byte	6
	.byte	'Ifx_SRC_GCERBERUS',0,2,206,2,3
	.word	3553
	.byte	8
	.byte	'_Ifx_SRC_GCPU',0,2,209,2,25,32,13,32
	.word	1138
	.byte	14,0,0,12
	.word	3605
	.byte	11
	.byte	'CPU',0,32
	.word	3614
	.byte	2,35,0,0,12
	.word	3585
	.byte	6
	.byte	'Ifx_SRC_GCPU',0,2,212,2,3
	.word	3633
	.byte	8
	.byte	'_Ifx_SRC_GDMA',0,2,215,2,25,80,13,80
	.word	1227
	.byte	14,0,0,12
	.word	3680
	.byte	11
	.byte	'DMA',0,80
	.word	3689
	.byte	2,35,0,0,12
	.word	3660
	.byte	6
	.byte	'Ifx_SRC_GDMA',0,2,218,2,3
	.word	3708
	.byte	8
	.byte	'_Ifx_SRC_GEMEM',0,2,221,2,25,4,13,4
	.word	1327
	.byte	14,0,0,12
	.word	3756
	.byte	11
	.byte	'EMEM',0,4
	.word	3765
	.byte	2,35,0,0,12
	.word	3735
	.byte	6
	.byte	'Ifx_SRC_GEMEM',0,2,224,2,3
	.word	3785
	.byte	8
	.byte	'_Ifx_SRC_GERAY',0,2,227,2,25,80,13,80
	.word	1387
	.byte	14,0,0,12
	.word	3834
	.byte	11
	.byte	'ERAY',0,80
	.word	3843
	.byte	2,35,0,0,12
	.word	3813
	.byte	6
	.byte	'Ifx_SRC_GERAY',0,2,230,2,3
	.word	3863
	.byte	8
	.byte	'_Ifx_SRC_GETH',0,2,233,2,25,4,13,4
	.word	1550
	.byte	14,0,0,12
	.word	3911
	.byte	11
	.byte	'ETH',0,4
	.word	3920
	.byte	2,35,0,0,12
	.word	3891
	.byte	6
	.byte	'Ifx_SRC_GETH',0,2,236,2,3
	.word	3939
	.byte	8
	.byte	'_Ifx_SRC_GEVR',0,2,239,2,25,8,13,8
	.word	1608
	.byte	14,0,0,12
	.word	3986
	.byte	11
	.byte	'EVR',0,8
	.word	3995
	.byte	2,35,0,0,12
	.word	3966
	.byte	6
	.byte	'Ifx_SRC_GEVR',0,2,242,2,3
	.word	4014
	.byte	8
	.byte	'_Ifx_SRC_GFFT',0,2,245,2,25,12,13,12
	.word	1681
	.byte	14,0,0,12
	.word	4061
	.byte	11
	.byte	'FFT',0,12
	.word	4070
	.byte	2,35,0,0,12
	.word	4041
	.byte	6
	.byte	'Ifx_SRC_GFFT',0,2,248,2,3
	.word	4089
	.byte	8
	.byte	'_Ifx_SRC_GGPSR',0,2,251,2,25,128,12,13,128,12
	.word	1767
	.byte	14,0,0,12
	.word	4138
	.byte	11
	.byte	'GPSR',0,128,12
	.word	4148
	.byte	2,35,0,0,12
	.word	4116
	.byte	6
	.byte	'Ifx_SRC_GGPSR',0,2,254,2,3
	.word	4169
	.byte	8
	.byte	'_Ifx_SRC_GGPT12',0,2,129,3,25,48,13,48
	.word	1901
	.byte	14,0,0,12
	.word	4219
	.byte	11
	.byte	'GPT12',0,48
	.word	4228
	.byte	2,35,0,0,12
	.word	4197
	.byte	6
	.byte	'Ifx_SRC_GGPT12',0,2,132,3,3
	.word	4249
	.byte	8
	.byte	'_Ifx_SRC_GGTM',0,2,135,3,25,192,11,13,192,11
	.word	2055
	.byte	14,0,0,12
	.word	4299
	.byte	11
	.byte	'GTM',0,192,11
	.word	4309
	.byte	2,35,0,0,12
	.word	4278
	.byte	6
	.byte	'Ifx_SRC_GGTM',0,2,138,3,3
	.word	4329
	.byte	8
	.byte	'_Ifx_SRC_GHSM',0,2,141,3,25,8,13,8
	.word	2268
	.byte	14,0,0,12
	.word	4376
	.byte	11
	.byte	'HSM',0,8
	.word	4385
	.byte	2,35,0,0,12
	.word	4356
	.byte	6
	.byte	'Ifx_SRC_GHSM',0,2,144,3,3
	.word	4404
	.byte	8
	.byte	'_Ifx_SRC_GLMU',0,2,147,3,25,4,13,4
	.word	2327
	.byte	14,0,0,12
	.word	4451
	.byte	11
	.byte	'LMU',0,4
	.word	4460
	.byte	2,35,0,0,12
	.word	4431
	.byte	6
	.byte	'Ifx_SRC_GLMU',0,2,150,3,3
	.word	4479
	.byte	8
	.byte	'_Ifx_SRC_GPMU',0,2,153,3,25,8,13,8
	.word	2385
	.byte	14,1,0,12
	.word	4526
	.byte	11
	.byte	'PMU',0,8
	.word	4535
	.byte	2,35,0,0,12
	.word	4506
	.byte	6
	.byte	'Ifx_SRC_GPMU',0,2,156,3,3
	.word	4554
	.byte	8
	.byte	'_Ifx_SRC_GQSPI',0,2,159,3,25,96,13,96
	.word	2443
	.byte	14,3,0,12
	.word	4602
	.byte	11
	.byte	'QSPI',0,96
	.word	4611
	.byte	2,35,0,0,12
	.word	4581
	.byte	6
	.byte	'Ifx_SRC_GQSPI',0,2,162,3,3
	.word	4631
	.byte	8
	.byte	'_Ifx_SRC_GSCU',0,2,165,3,25,20,12
	.word	2563
	.byte	11
	.byte	'SCU',0,20
	.word	4679
	.byte	2,35,0,0,12
	.word	4659
	.byte	6
	.byte	'Ifx_SRC_GSCU',0,2,168,3,3
	.word	4698
	.byte	8
	.byte	'_Ifx_SRC_GSENT',0,2,171,3,25,16,13,16
	.word	2644
	.byte	14,0,0,12
	.word	4746
	.byte	11
	.byte	'SENT',0,16
	.word	4755
	.byte	2,35,0,0,12
	.word	4725
	.byte	6
	.byte	'Ifx_SRC_GSENT',0,2,174,3,3
	.word	4775
	.byte	8
	.byte	'_Ifx_SRC_GSMU',0,2,177,3,25,12,13,12
	.word	2704
	.byte	14,0,0,12
	.word	4823
	.byte	11
	.byte	'SMU',0,12
	.word	4832
	.byte	2,35,0,0,12
	.word	4803
	.byte	6
	.byte	'Ifx_SRC_GSMU',0,2,180,3,3
	.word	4851
	.byte	8
	.byte	'_Ifx_SRC_GSTM',0,2,183,3,25,96,13,96
	.word	2771
	.byte	14,0,0,12
	.word	4898
	.byte	11
	.byte	'STM',0,96
	.word	4907
	.byte	2,35,0,0,12
	.word	4878
	.byte	6
	.byte	'Ifx_SRC_GSTM',0,2,186,3,3
	.word	4926
	.byte	8
	.byte	'_Ifx_SRC_GVADC',0,2,189,3,25,224,4,13,64
	.word	3010
	.byte	14,3,0,12
	.word	4975
	.byte	11
	.byte	'G',0,64
	.word	4984
	.byte	2,35,0,13,224,1
	.word	287
	.byte	14,223,1,0,11
	.byte	'reserved_40',0,224,1
	.word	5000
	.byte	2,35,64,13,192,2
	.word	2872
	.byte	14,0,0,12
	.word	5033
	.byte	11
	.byte	'CG',0,192,2
	.word	5043
	.byte	3,35,160,2,0,12
	.word	4953
	.byte	6
	.byte	'Ifx_SRC_GVADC',0,2,194,3,3
	.word	5063
	.byte	8
	.byte	'_Ifx_SRC_GXBAR',0,2,197,3,25,4,12
	.word	3112
	.byte	11
	.byte	'XBAR',0,4
	.word	5112
	.byte	2,35,0,0,12
	.word	5091
	.byte	6
	.byte	'Ifx_SRC_GXBAR',0,2,200,3,3
	.word	5132
	.byte	6
	.byte	'uint8',0,3,90,29
	.word	287
	.byte	3
	.byte	'unsigned short int',0,2,7,6
	.byte	'uint16',0,3,92,29
	.word	5174
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L58:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,0,3,8,54,15,39,12,63,12,60,12,0,0,3,36,0,3,8,11,15
	.byte	62,15,0,0,4,59,0,3,8,0,0,5,15,0,73,19,0,0,6,22,0,3,8,58,15,59,15,57,15,73,19,0,0,7,21,0,54,15,0,0,8,19
	.byte	1,3,8,58,15,59,15,57,15,11,15,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,23,1,58,15,59,15,57,15
	.byte	11,15,0,0,11,13,0,3,8,11,15,73,19,56,9,0,0,12,53,0,73,19,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0
	.byte	0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L59:
	.word	.L492-.L491
.L491:
	.half	3
	.word	.L494-.L493
.L493:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0,0
.L494:
.L492:
	.sdecl	'.debug_info',debug,cluster('IrqAscLin_Init')
	.sect	'.debug_info'
.L60:
	.word	386
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L63,.L62
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqAscLin_Init',0,1,250,4,6,1,1,1
	.word	.L23,.L195,.L22
	.byte	4
	.word	.L23,.L195
	.byte	4
	.word	.L23,.L196
	.byte	5
	.byte	'val',0,1,128,5,3
	.word	.L197,.L198
	.byte	0,4
	.word	.L196,.L199
	.byte	5
	.byte	'val',0,1,130,5,3
	.word	.L197,.L200
	.byte	0,4
	.word	.L199,.L201
	.byte	5
	.byte	'val',0,1,132,5,3
	.word	.L197,.L202
	.byte	0,4
	.word	.L201,.L203
	.byte	5
	.byte	'val',0,1,137,5,3
	.word	.L197,.L204
	.byte	0,4
	.word	.L203,.L205
	.byte	5
	.byte	'val',0,1,139,5,3
	.word	.L197,.L206
	.byte	0,4
	.word	.L205,.L207
	.byte	5
	.byte	'val',0,1,141,5,3
	.word	.L197,.L208
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqAscLin_Init')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqAscLin_Init')
	.sect	'.debug_line'
.L62:
	.word	.L496-.L495
.L495:
	.half	3
	.word	.L498-.L497
.L497:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L498:
	.byte	5,3,7,0,5,2
	.word	.L23
	.byte	3,255,4,1,9
	.half	.L196-.L23
	.byte	3,2,1,9
	.half	.L199-.L196
	.byte	3,2,1,9
	.half	.L201-.L199
	.byte	3,5,1,9
	.half	.L203-.L201
	.byte	3,2,1,9
	.half	.L205-.L203
	.byte	3,2,1,5,1,9
	.half	.L207-.L205
	.byte	3,6,1,7,9
	.half	.L64-.L207
	.byte	0,1,1
.L496:
	.sdecl	'.debug_ranges',debug,cluster('IrqAscLin_Init')
	.sect	'.debug_ranges'
.L63:
	.word	-1,.L23,0,.L64-.L23,0,0
	.sdecl	'.debug_info',debug,cluster('IrqCcu6_Init')
	.sect	'.debug_info'
.L65:
	.word	221
	.half	3
	.word	.L66
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L68,.L67
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqCcu6_Init',0,1,169,5,6,1,1,1
	.word	.L25,.L209,.L24
	.byte	4
	.word	.L25,.L209
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqCcu6_Init')
	.sect	'.debug_abbrev'
.L66:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqCcu6_Init')
	.sect	'.debug_line'
.L67:
	.word	.L500-.L499
.L499:
	.half	3
	.word	.L502-.L501
.L501:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L502:
	.byte	5,1,7,0,5,2
	.word	.L25
	.byte	3,194,5,1,7,9
	.half	.L69-.L25
	.byte	0,1,1
.L500:
	.sdecl	'.debug_ranges',debug,cluster('IrqCcu6_Init')
	.sect	'.debug_ranges'
.L68:
	.word	-1,.L25,0,.L69-.L25,0,0
	.sdecl	'.debug_info',debug,cluster('IrqCan_Init')
	.sect	'.debug_info'
.L70:
	.word	220
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L73,.L72
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqCan_Init',0,1,224,6,6,1,1,1
	.word	.L31,.L210,.L30
	.byte	4
	.word	.L31,.L210
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqCan_Init')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqCan_Init')
	.sect	'.debug_line'
.L72:
	.word	.L504-.L503
.L503:
	.half	3
	.word	.L506-.L505
.L505:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L506:
	.byte	5,2,7,0,5,2
	.word	.L31
	.byte	3,221,7,1,7,9
	.half	.L74-.L31
	.byte	0,1,1
.L504:
	.sdecl	'.debug_ranges',debug,cluster('IrqCan_Init')
	.sect	'.debug_ranges'
.L73:
	.word	-1,.L31,0,.L74-.L31,0,0
	.sdecl	'.debug_info',debug,cluster('IrqHsm_Init')
	.sect	'.debug_info'
.L75:
	.word	275
	.half	3
	.word	.L76
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L78,.L77
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqHsm_Init',0,1,232,11,6,1,1,1
	.word	.L53,.L211,.L52
	.byte	4
	.word	.L53,.L211
	.byte	4
	.word	.L53,.L212
	.byte	5
	.byte	'val',0,1,237,11,3
	.word	.L197,.L213
	.byte	0,4
	.word	.L212,.L214
	.byte	5
	.byte	'val',0,1,242,11,3
	.word	.L197,.L215
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqHsm_Init')
	.sect	'.debug_abbrev'
.L76:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqHsm_Init')
	.sect	'.debug_line'
.L77:
	.word	.L508-.L507
.L507:
	.half	3
	.word	.L510-.L509
.L509:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L510:
	.byte	5,3,7,0,5,2
	.word	.L53
	.byte	3,236,11,1,9
	.half	.L212-.L53
	.byte	3,5,1,5,1,9
	.half	.L214-.L212
	.byte	3,3,1,7,9
	.half	.L79-.L214
	.byte	0,1,1
.L508:
	.sdecl	'.debug_ranges',debug,cluster('IrqHsm_Init')
	.sect	'.debug_ranges'
.L78:
	.word	-1,.L53,0,.L79-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('IrqGpt_Init')
	.sect	'.debug_info'
.L80:
	.word	383
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L83,.L82
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqGpt_Init',0,1,216,5,6,1,1,1
	.word	.L27,.L216,.L26
	.byte	4
	.word	.L27,.L216
	.byte	4
	.word	.L27,.L217
	.byte	5
	.byte	'val',0,1,221,5,3
	.word	.L197,.L218
	.byte	0,4
	.word	.L217,.L219
	.byte	5
	.byte	'val',0,1,223,5,3
	.word	.L197,.L220
	.byte	0,4
	.word	.L219,.L221
	.byte	5
	.byte	'val',0,1,225,5,3
	.word	.L197,.L222
	.byte	0,4
	.word	.L221,.L223
	.byte	5
	.byte	'val',0,1,227,5,3
	.word	.L197,.L224
	.byte	0,4
	.word	.L223,.L225
	.byte	5
	.byte	'val',0,1,229,5,3
	.word	.L197,.L226
	.byte	0,4
	.word	.L225,.L227
	.byte	5
	.byte	'val',0,1,231,5,3
	.word	.L197,.L228
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqGpt_Init')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqGpt_Init')
	.sect	'.debug_line'
.L82:
	.word	.L512-.L511
.L511:
	.half	3
	.word	.L514-.L513
.L513:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L514:
	.byte	5,3,7,0,5,2
	.word	.L27
	.byte	3,220,5,1,9
	.half	.L217-.L27
	.byte	3,2,1,9
	.half	.L219-.L217
	.byte	3,2,1,9
	.half	.L221-.L219
	.byte	3,2,1,9
	.half	.L223-.L221
	.byte	3,2,1,9
	.half	.L225-.L223
	.byte	3,2,1,5,1,9
	.half	.L227-.L225
	.byte	3,5,1,7,9
	.half	.L84-.L227
	.byte	0,1,1
.L512:
	.sdecl	'.debug_ranges',debug,cluster('IrqGpt_Init')
	.sect	'.debug_ranges'
.L83:
	.word	-1,.L27,0,.L84-.L27,0,0
	.sdecl	'.debug_info',debug,cluster('IrqGtm_Init')
	.sect	'.debug_info'
.L85:
	.word	923
	.half	3
	.word	.L86
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L88,.L87
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqGtm_Init',0,1,128,6,6,1,1,1
	.word	.L29,.L229,.L28
	.byte	4
	.word	.L29,.L229
	.byte	4
	.word	.L29,.L230
	.byte	5
	.byte	'val',0,1,133,6,3
	.word	.L197,.L231
	.byte	0,4
	.word	.L230,.L232
	.byte	5
	.byte	'val',0,1,139,6,3
	.word	.L197,.L233
	.byte	0,4
	.word	.L232,.L234
	.byte	5
	.byte	'val',0,1,144,6,3
	.word	.L197,.L235
	.byte	0,4
	.word	.L234,.L236
	.byte	5
	.byte	'val',0,1,146,6,3
	.word	.L197,.L237
	.byte	0,4
	.word	.L236,.L238
	.byte	5
	.byte	'val',0,1,148,6,3
	.word	.L197,.L239
	.byte	0,4
	.word	.L238,.L240
	.byte	5
	.byte	'val',0,1,150,6,3
	.word	.L197,.L241
	.byte	0,4
	.word	.L240,.L242
	.byte	5
	.byte	'val',0,1,152,6,3
	.word	.L197,.L243
	.byte	0,4
	.word	.L242,.L244
	.byte	5
	.byte	'val',0,1,154,6,3
	.word	.L197,.L245
	.byte	0,4
	.word	.L244,.L246
	.byte	5
	.byte	'val',0,1,156,6,3
	.word	.L197,.L247
	.byte	0,4
	.word	.L246,.L248
	.byte	5
	.byte	'val',0,1,158,6,3
	.word	.L197,.L249
	.byte	0,4
	.word	.L248,.L250
	.byte	5
	.byte	'val',0,1,164,6,3
	.word	.L197,.L251
	.byte	0,4
	.word	.L250,.L252
	.byte	5
	.byte	'val',0,1,166,6,3
	.word	.L197,.L253
	.byte	0,4
	.word	.L252,.L254
	.byte	5
	.byte	'val',0,1,168,6,3
	.word	.L197,.L255
	.byte	0,4
	.word	.L254,.L256
	.byte	5
	.byte	'val',0,1,170,6,3
	.word	.L197,.L257
	.byte	0,4
	.word	.L256,.L258
	.byte	5
	.byte	'val',0,1,172,6,3
	.word	.L197,.L259
	.byte	0,4
	.word	.L258,.L260
	.byte	5
	.byte	'val',0,1,174,6,3
	.word	.L197,.L261
	.byte	0,4
	.word	.L260,.L262
	.byte	5
	.byte	'val',0,1,176,6,3
	.word	.L197,.L263
	.byte	0,4
	.word	.L262,.L264
	.byte	5
	.byte	'val',0,1,178,6,3
	.word	.L197,.L265
	.byte	0,4
	.word	.L264,.L266
	.byte	5
	.byte	'val',0,1,183,6,3
	.word	.L197,.L267
	.byte	0,4
	.word	.L266,.L268
	.byte	5
	.byte	'val',0,1,185,6,3
	.word	.L197,.L269
	.byte	0,4
	.word	.L268,.L270
	.byte	5
	.byte	'val',0,1,187,6,3
	.word	.L197,.L271
	.byte	0,4
	.word	.L270,.L272
	.byte	5
	.byte	'val',0,1,189,6,3
	.word	.L197,.L273
	.byte	0,4
	.word	.L272,.L274
	.byte	5
	.byte	'val',0,1,191,6,3
	.word	.L197,.L275
	.byte	0,4
	.word	.L274,.L276
	.byte	5
	.byte	'val',0,1,193,6,3
	.word	.L197,.L277
	.byte	0,4
	.word	.L276,.L278
	.byte	5
	.byte	'val',0,1,195,6,3
	.word	.L197,.L279
	.byte	0,4
	.word	.L278,.L280
	.byte	5
	.byte	'val',0,1,197,6,3
	.word	.L197,.L281
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqGtm_Init')
	.sect	'.debug_abbrev'
.L86:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqGtm_Init')
	.sect	'.debug_line'
.L87:
	.word	.L516-.L515
.L515:
	.half	3
	.word	.L518-.L517
.L517:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L518:
	.byte	5,3,7,0,5,2
	.word	.L29
	.byte	3,132,6,1,9
	.half	.L230-.L29
	.byte	3,6,1,9
	.half	.L232-.L230
	.byte	3,5,1,9
	.half	.L234-.L232
	.byte	3,2,1,9
	.half	.L236-.L234
	.byte	3,2,1,9
	.half	.L238-.L236
	.byte	3,2,1,9
	.half	.L240-.L238
	.byte	3,2,1,9
	.half	.L242-.L240
	.byte	3,2,1,9
	.half	.L244-.L242
	.byte	3,2,1,9
	.half	.L246-.L244
	.byte	3,2,1,9
	.half	.L248-.L246
	.byte	3,6,1,9
	.half	.L250-.L248
	.byte	3,2,1,9
	.half	.L252-.L250
	.byte	3,2,1,9
	.half	.L254-.L252
	.byte	3,2,1,9
	.half	.L256-.L254
	.byte	3,2,1,9
	.half	.L258-.L256
	.byte	3,2,1,9
	.half	.L260-.L258
	.byte	3,2,1,9
	.half	.L262-.L260
	.byte	3,2,1,9
	.half	.L264-.L262
	.byte	3,5,1,9
	.half	.L266-.L264
	.byte	3,2,1,9
	.half	.L268-.L266
	.byte	3,2,1,9
	.half	.L270-.L268
	.byte	3,2,1,9
	.half	.L272-.L270
	.byte	3,2,1,9
	.half	.L274-.L272
	.byte	3,2,1,9
	.half	.L276-.L274
	.byte	3,2,1,9
	.half	.L278-.L276
	.byte	3,2,1,5,1,9
	.half	.L280-.L278
	.byte	3,6,1,7,9
	.half	.L89-.L280
	.byte	0,1,1
.L516:
	.sdecl	'.debug_ranges',debug,cluster('IrqGtm_Init')
	.sect	'.debug_ranges'
.L88:
	.word	-1,.L29,0,.L89-.L29,0,0
	.sdecl	'.debug_info',debug,cluster('IrqGpsrGroup_Init')
	.sect	'.debug_info'
.L90:
	.word	254
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L93,.L92
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqGpsrGroup_Init',0,1,244,7,6,1,1,1
	.word	.L33,.L282,.L32
	.byte	4
	.word	.L33,.L282
	.byte	4
	.word	.L33,.L283
	.byte	5
	.byte	'val',0,1,249,7,3
	.word	.L197,.L284
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqGpsrGroup_Init')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqGpsrGroup_Init')
	.sect	'.debug_line'
.L92:
	.word	.L520-.L519
.L519:
	.half	3
	.word	.L522-.L521
.L521:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L522:
	.byte	5,3,7,0,5,2
	.word	.L33
	.byte	3,248,7,1,5,1,9
	.half	.L283-.L33
	.byte	3,5,1,7,9
	.half	.L94-.L283
	.byte	0,1,1
.L520:
	.sdecl	'.debug_ranges',debug,cluster('IrqGpsrGroup_Init')
	.sect	'.debug_ranges'
.L93:
	.word	-1,.L33,0,.L94-.L33,0,0
	.sdecl	'.debug_info',debug,cluster('IrqFlexray_Init')
	.sect	'.debug_info'
.L95:
	.word	224
	.half	3
	.word	.L96
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L98,.L97
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqFlexray_Init',0,1,190,9,6,1,1,1
	.word	.L39,.L285,.L38
	.byte	4
	.word	.L39,.L285
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqFlexray_Init')
	.sect	'.debug_abbrev'
.L96:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqFlexray_Init')
	.sect	'.debug_line'
.L97:
	.word	.L524-.L523
.L523:
	.half	3
	.word	.L526-.L525
.L525:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L526:
	.byte	5,1,7,0,5,2
	.word	.L39
	.byte	3,217,9,1,7,9
	.half	.L99-.L39
	.byte	0,1,1
.L524:
	.sdecl	'.debug_ranges',debug,cluster('IrqFlexray_Init')
	.sect	'.debug_ranges'
.L98:
	.word	-1,.L39,0,.L99-.L39,0,0
	.sdecl	'.debug_info',debug,cluster('IrqSpi_Init')
	.sect	'.debug_info'
.L100:
	.word	815
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L103,.L102
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqSpi_Init',0,1,148,8,6,1,1,1
	.word	.L35,.L286,.L34
	.byte	4
	.word	.L35,.L286
	.byte	4
	.word	.L35,.L287
	.byte	5
	.byte	'val',0,1,154,8,3
	.word	.L197,.L288
	.byte	0,4
	.word	.L287,.L289
	.byte	5
	.byte	'val',0,1,156,8,3
	.word	.L197,.L290
	.byte	0,4
	.word	.L289,.L291
	.byte	5
	.byte	'val',0,1,158,8,3
	.word	.L197,.L292
	.byte	0,4
	.word	.L291,.L293
	.byte	5
	.byte	'val',0,1,160,8,3
	.word	.L197,.L294
	.byte	0,4
	.word	.L293,.L295
	.byte	5
	.byte	'val',0,1,162,8,3
	.word	.L197,.L296
	.byte	0,4
	.word	.L295,.L297
	.byte	5
	.byte	'val',0,1,167,8,3
	.word	.L197,.L298
	.byte	0,4
	.word	.L297,.L299
	.byte	5
	.byte	'val',0,1,169,8,3
	.word	.L197,.L300
	.byte	0,4
	.word	.L299,.L301
	.byte	5
	.byte	'val',0,1,171,8,3
	.word	.L197,.L302
	.byte	0,4
	.word	.L301,.L303
	.byte	5
	.byte	'val',0,1,173,8,3
	.word	.L197,.L304
	.byte	0,4
	.word	.L303,.L305
	.byte	5
	.byte	'val',0,1,175,8,3
	.word	.L197,.L306
	.byte	0,4
	.word	.L305,.L307
	.byte	5
	.byte	'val',0,1,180,8,3
	.word	.L197,.L308
	.byte	0,4
	.word	.L307,.L309
	.byte	5
	.byte	'val',0,1,182,8,3
	.word	.L197,.L310
	.byte	0,4
	.word	.L309,.L311
	.byte	5
	.byte	'val',0,1,184,8,3
	.word	.L197,.L312
	.byte	0,4
	.word	.L311,.L313
	.byte	5
	.byte	'val',0,1,186,8,3
	.word	.L197,.L314
	.byte	0,4
	.word	.L313,.L315
	.byte	5
	.byte	'val',0,1,188,8,3
	.word	.L197,.L316
	.byte	0,4
	.word	.L315,.L317
	.byte	5
	.byte	'val',0,1,190,8,3
	.word	.L197,.L318
	.byte	0,4
	.word	.L317,.L319
	.byte	5
	.byte	'val',0,1,195,8,3
	.word	.L197,.L320
	.byte	0,4
	.word	.L319,.L321
	.byte	5
	.byte	'val',0,1,197,8,3
	.word	.L197,.L322
	.byte	0,4
	.word	.L321,.L323
	.byte	5
	.byte	'val',0,1,199,8,3
	.word	.L197,.L324
	.byte	0,4
	.word	.L323,.L325
	.byte	5
	.byte	'val',0,1,201,8,3
	.word	.L197,.L326
	.byte	0,4
	.word	.L325,.L327
	.byte	5
	.byte	'val',0,1,203,8,3
	.word	.L197,.L328
	.byte	0,4
	.word	.L327,.L329
	.byte	5
	.byte	'val',0,1,205,8,3
	.word	.L197,.L330
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqSpi_Init')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqSpi_Init')
	.sect	'.debug_line'
.L102:
	.word	.L528-.L527
.L527:
	.half	3
	.word	.L530-.L529
.L529:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L530:
	.byte	5,3,7,0,5,2
	.word	.L35
	.byte	3,153,8,1,9
	.half	.L287-.L35
	.byte	3,2,1,9
	.half	.L289-.L287
	.byte	3,2,1,9
	.half	.L291-.L289
	.byte	3,2,1,9
	.half	.L293-.L291
	.byte	3,2,1,9
	.half	.L295-.L293
	.byte	3,5,1,9
	.half	.L297-.L295
	.byte	3,2,1,9
	.half	.L299-.L297
	.byte	3,2,1,9
	.half	.L301-.L299
	.byte	3,2,1,9
	.half	.L303-.L301
	.byte	3,2,1,9
	.half	.L305-.L303
	.byte	3,5,1,9
	.half	.L307-.L305
	.byte	3,2,1,9
	.half	.L309-.L307
	.byte	3,2,1,9
	.half	.L311-.L309
	.byte	3,2,1,9
	.half	.L313-.L311
	.byte	3,2,1,9
	.half	.L315-.L313
	.byte	3,2,1,9
	.half	.L317-.L315
	.byte	3,5,1,9
	.half	.L319-.L317
	.byte	3,2,1,9
	.half	.L321-.L319
	.byte	3,2,1,9
	.half	.L323-.L321
	.byte	3,2,1,9
	.half	.L325-.L323
	.byte	3,2,1,9
	.half	.L327-.L325
	.byte	3,2,1,5,1,9
	.half	.L329-.L327
	.byte	3,5,1,7,9
	.half	.L104-.L329
	.byte	0,1,1
.L528:
	.sdecl	'.debug_ranges',debug,cluster('IrqSpi_Init')
	.sect	'.debug_ranges'
.L103:
	.word	-1,.L35,0,.L104-.L35,0,0
	.sdecl	'.debug_info',debug,cluster('IrqAdc_Init')
	.sect	'.debug_info'
.L105:
	.word	437
	.half	3
	.word	.L106
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L108,.L107
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqAdc_Init',0,1,231,8,6,1,1,1
	.word	.L37,.L331,.L36
	.byte	4
	.word	.L37,.L331
	.byte	4
	.word	.L37,.L332
	.byte	5
	.byte	'val',0,1,237,8,3
	.word	.L197,.L333
	.byte	0,4
	.word	.L332,.L334
	.byte	5
	.byte	'val',0,1,239,8,3
	.word	.L197,.L335
	.byte	0,4
	.word	.L334,.L336
	.byte	5
	.byte	'val',0,1,241,8,3
	.word	.L197,.L337
	.byte	0,4
	.word	.L336,.L338
	.byte	5
	.byte	'val',0,1,243,8,3
	.word	.L197,.L339
	.byte	0,4
	.word	.L338,.L340
	.byte	5
	.byte	'val',0,1,248,8,3
	.word	.L197,.L341
	.byte	0,4
	.word	.L340,.L342
	.byte	5
	.byte	'val',0,1,250,8,3
	.word	.L197,.L343
	.byte	0,4
	.word	.L342,.L344
	.byte	5
	.byte	'val',0,1,252,8,3
	.word	.L197,.L345
	.byte	0,4
	.word	.L344,.L346
	.byte	5
	.byte	'val',0,1,254,8,3
	.word	.L197,.L347
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqAdc_Init')
	.sect	'.debug_abbrev'
.L106:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqAdc_Init')
	.sect	'.debug_line'
.L107:
	.word	.L532-.L531
.L531:
	.half	3
	.word	.L534-.L533
.L533:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L534:
	.byte	5,3,7,0,5,2
	.word	.L37
	.byte	3,236,8,1,9
	.half	.L332-.L37
	.byte	3,2,1,9
	.half	.L334-.L332
	.byte	3,2,1,9
	.half	.L336-.L334
	.byte	3,2,1,9
	.half	.L338-.L336
	.byte	3,5,1,9
	.half	.L340-.L338
	.byte	3,2,1,9
	.half	.L342-.L340
	.byte	3,2,1,9
	.half	.L344-.L342
	.byte	3,2,1,5,1,9
	.half	.L346-.L344
	.byte	3,38,1,7,9
	.half	.L109-.L346
	.byte	0,1,1
.L532:
	.sdecl	'.debug_ranges',debug,cluster('IrqAdc_Init')
	.sect	'.debug_ranges'
.L108:
	.word	-1,.L37,0,.L109-.L37,0,0
	.sdecl	'.debug_info',debug,cluster('IrqEthernet_Init')
	.sect	'.debug_info'
.L110:
	.word	225
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L113,.L112
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqEthernet_Init',0,1,240,9,6,1,1,1
	.word	.L41,.L348,.L40
	.byte	4
	.word	.L41,.L348
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqEthernet_Init')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqEthernet_Init')
	.sect	'.debug_line'
.L112:
	.word	.L536-.L535
.L535:
	.half	3
	.word	.L538-.L537
.L537:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L538:
	.byte	5,1,7,0,5,2
	.word	.L41
	.byte	3,245,9,1,7,9
	.half	.L114-.L41
	.byte	0,1,1
.L536:
	.sdecl	'.debug_ranges',debug,cluster('IrqEthernet_Init')
	.sect	'.debug_ranges'
.L113:
	.word	-1,.L41,0,.L114-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('IrqDma_Init')
	.sect	'.debug_info'
.L115:
	.word	653
	.half	3
	.word	.L116
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L118,.L117
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqDma_Init',0,1,140,10,6,1,1,1
	.word	.L43,.L349,.L42
	.byte	4
	.word	.L43,.L349
	.byte	4
	.word	.L350,.L351
	.byte	5
	.byte	'val',0,1,147,10,3
	.word	.L197,.L352
	.byte	0,4
	.word	.L351,.L353
	.byte	5
	.byte	'val',0,1,149,10,3
	.word	.L197,.L354
	.byte	0,4
	.word	.L353,.L355
	.byte	5
	.byte	'val',0,1,151,10,3
	.word	.L197,.L356
	.byte	0,4
	.word	.L355,.L357
	.byte	5
	.byte	'val',0,1,153,10,3
	.word	.L197,.L358
	.byte	0,4
	.word	.L357,.L359
	.byte	5
	.byte	'val',0,1,155,10,3
	.word	.L197,.L360
	.byte	0,4
	.word	.L359,.L361
	.byte	5
	.byte	'val',0,1,157,10,3
	.word	.L197,.L362
	.byte	0,4
	.word	.L361,.L363
	.byte	5
	.byte	'val',0,1,159,10,3
	.word	.L197,.L364
	.byte	0,4
	.word	.L363,.L365
	.byte	5
	.byte	'val',0,1,161,10,3
	.word	.L197,.L366
	.byte	0,4
	.word	.L365,.L367
	.byte	5
	.byte	'val',0,1,163,10,3
	.word	.L197,.L368
	.byte	0,4
	.word	.L367,.L369
	.byte	5
	.byte	'val',0,1,165,10,3
	.word	.L197,.L370
	.byte	0,4
	.word	.L369,.L371
	.byte	5
	.byte	'val',0,1,167,10,3
	.word	.L197,.L372
	.byte	0,4
	.word	.L371,.L373
	.byte	5
	.byte	'val',0,1,169,10,3
	.word	.L197,.L374
	.byte	0,4
	.word	.L373,.L375
	.byte	5
	.byte	'val',0,1,171,10,3
	.word	.L197,.L376
	.byte	0,4
	.word	.L375,.L377
	.byte	5
	.byte	'val',0,1,173,10,3
	.word	.L197,.L378
	.byte	0,4
	.word	.L377,.L379
	.byte	5
	.byte	'val',0,1,175,10,3
	.word	.L197,.L380
	.byte	0,4
	.word	.L379,.L381
	.byte	5
	.byte	'val',0,1,177,10,3
	.word	.L197,.L382
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqDma_Init')
	.sect	'.debug_abbrev'
.L116:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqDma_Init')
	.sect	'.debug_line'
.L117:
	.word	.L540-.L539
.L539:
	.half	3
	.word	.L542-.L541
.L541:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L542:
	.byte	5,13,7,0,5,2
	.word	.L43
	.byte	3,143,10,1,5,16,9
	.half	.L543-.L43
	.byte	1,5,3,9
	.half	.L350-.L543
	.byte	3,3,1,9
	.half	.L351-.L350
	.byte	3,2,1,9
	.half	.L353-.L351
	.byte	3,2,1,9
	.half	.L355-.L353
	.byte	3,2,1,9
	.half	.L357-.L355
	.byte	3,2,1,9
	.half	.L359-.L357
	.byte	3,2,1,9
	.half	.L361-.L359
	.byte	3,2,1,9
	.half	.L363-.L361
	.byte	3,2,1,9
	.half	.L365-.L363
	.byte	3,2,1,9
	.half	.L367-.L365
	.byte	3,2,1,9
	.half	.L369-.L367
	.byte	3,2,1,9
	.half	.L371-.L369
	.byte	3,2,1,9
	.half	.L373-.L371
	.byte	3,2,1,9
	.half	.L375-.L373
	.byte	3,2,1,9
	.half	.L377-.L375
	.byte	3,2,1,9
	.half	.L379-.L377
	.byte	3,2,1,5,1,9
	.half	.L381-.L379
	.byte	3,5,1,7,9
	.half	.L119-.L381
	.byte	0,1,1
.L540:
	.sdecl	'.debug_ranges',debug,cluster('IrqDma_Init')
	.sect	'.debug_ranges'
.L118:
	.word	-1,.L43,0,.L119-.L43,0,0
	.sdecl	'.debug_info',debug,cluster('IrqScu_Init')
	.sect	'.debug_info'
.L120:
	.word	220
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L123,.L122
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqScu_Init',0,1,239,10,6,1,1,1
	.word	.L47,.L383,.L46
	.byte	4
	.word	.L47,.L383
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqScu_Init')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqScu_Init')
	.sect	'.debug_line'
.L122:
	.word	.L545-.L544
.L544:
	.half	3
	.word	.L547-.L546
.L546:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L547:
	.byte	5,1,7,0,5,2
	.word	.L47
	.byte	3,254,10,1,7,9
	.half	.L124-.L47
	.byte	0,1,1
.L545:
	.sdecl	'.debug_ranges',debug,cluster('IrqScu_Init')
	.sect	'.debug_ranges'
.L123:
	.word	-1,.L47,0,.L124-.L47,0,0
	.sdecl	'.debug_info',debug,cluster('IrqPmu_Init')
	.sect	'.debug_info'
.L125:
	.word	220
	.half	3
	.word	.L126
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L128,.L127
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqPmu_Init',0,1,148,11,6,1,1,1
	.word	.L49,.L384,.L48
	.byte	4
	.word	.L49,.L384
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqPmu_Init')
	.sect	'.debug_abbrev'
.L126:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqPmu_Init')
	.sect	'.debug_line'
.L127:
	.word	.L549-.L548
.L548:
	.half	3
	.word	.L551-.L550
.L550:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L551:
	.byte	5,1,7,0,5,2
	.word	.L49
	.byte	3,162,11,1,7,9
	.half	.L129-.L49
	.byte	0,1,1
.L549:
	.sdecl	'.debug_ranges',debug,cluster('IrqPmu_Init')
	.sect	'.debug_ranges'
.L128:
	.word	-1,.L49,0,.L129-.L49,0,0
	.sdecl	'.debug_info',debug,cluster('IrqSent_Init')
	.sect	'.debug_info'
.L130:
	.word	221
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L133,.L132
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqSent_Init',0,1,186,11,6,1,1,1
	.word	.L51,.L385,.L50
	.byte	4
	.word	.L51,.L385
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqSent_Init')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqSent_Init')
	.sect	'.debug_line'
.L132:
	.word	.L553-.L552
.L552:
	.half	3
	.word	.L555-.L554
.L554:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L555:
	.byte	5,1,7,0,5,2
	.word	.L51
	.byte	3,210,11,1,7,9
	.half	.L134-.L51
	.byte	0,1,1
.L553:
	.sdecl	'.debug_ranges',debug,cluster('IrqSent_Init')
	.sect	'.debug_ranges'
.L133:
	.word	-1,.L51,0,.L134-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearAllInterruptFlags')
	.sect	'.debug_info'
.L135:
	.word	235
	.half	3
	.word	.L136
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L138,.L137
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearAllInterruptFlags',0,1,138,12,6,1,1,1
	.word	.L55,.L386,.L54
	.byte	4
	.word	.L55,.L386
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearAllInterruptFlags')
	.sect	'.debug_abbrev'
.L136:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearAllInterruptFlags')
	.sect	'.debug_line'
.L137:
	.word	.L557-.L556
.L556:
	.half	3
	.word	.L559-.L558
.L558:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L559:
	.byte	5,3,7,0,5,2
	.word	.L55
	.byte	3,139,12,1,5,26,9
	.half	.L560-.L55
	.byte	3,3,1,5,23,9
	.half	.L561-.L560
	.byte	3,8,1,9
	.half	.L562-.L561
	.byte	3,4,1,5,29,9
	.half	.L563-.L562
	.byte	3,8,1,5,23,9
	.half	.L564-.L563
	.byte	3,4,1,9
	.half	.L565-.L564
	.byte	3,4,1,9
	.half	.L566-.L565
	.byte	3,12,1,9
	.half	.L567-.L566
	.byte	3,4,1,9
	.half	.L568-.L567
	.byte	3,16,1,5,1,7,9
	.half	.L139-.L568
	.byte	3,3,0,1,1
.L557:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearAllInterruptFlags')
	.sect	'.debug_ranges'
.L138:
	.word	-1,.L55,0,.L139-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('IrqStm_Init')
	.sect	'.debug_info'
.L140:
	.word	275
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L143,.L142
	.byte	2
	.word	.L56
	.byte	3
	.byte	'IrqStm_Init',0,1,204,10,6,1,1,1
	.word	.L45,.L387,.L44
	.byte	4
	.word	.L45,.L387
	.byte	4
	.word	.L45,.L388
	.byte	5
	.byte	'val',0,1,209,10,3
	.word	.L197,.L389
	.byte	0,4
	.word	.L388,.L390
	.byte	5
	.byte	'val',0,1,211,10,3
	.word	.L197,.L391
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IrqStm_Init')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IrqStm_Init')
	.sect	'.debug_line'
.L142:
	.word	.L570-.L569
.L569:
	.half	3
	.word	.L572-.L571
.L571:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L572:
	.byte	5,3,7,0,5,2
	.word	.L45
	.byte	3,208,10,1,9
	.half	.L388-.L45
	.byte	3,2,1,5,1,9
	.half	.L390-.L388
	.byte	3,5,1,7,9
	.half	.L144-.L390
	.byte	0,1,1
.L570:
	.sdecl	'.debug_ranges',debug,cluster('IrqStm_Init')
	.sect	'.debug_ranges'
.L143:
	.word	-1,.L45,0,.L144-.L45,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearAsclinIntFlags')
	.sect	'.debug_info'
.L145:
	.word	227
	.half	3
	.word	.L146
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L148,.L147
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearAsclinIntFlags',0,1,168,1,13,1,1
	.word	.L3,.L392,.L2
	.byte	4
	.word	.L393
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearAsclinIntFlags')
	.sect	'.debug_abbrev'
.L146:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearAsclinIntFlags')
	.sect	'.debug_line'
.L147:
	.word	.L574-.L573
.L573:
	.half	3
	.word	.L576-.L575
.L575:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L576:
	.byte	5,21,7,0,5,2
	.word	.L3
	.byte	3,171,1,1,5,19,9
	.half	.L577-.L3
	.byte	1,9
	.half	.L578-.L577
	.byte	3,1,1,5,20,9
	.half	.L579-.L578
	.byte	3,1,1,5,19,9
	.half	.L580-.L579
	.byte	3,4,1,9
	.half	.L581-.L580
	.byte	3,1,1,5,20,9
	.half	.L582-.L581
	.byte	3,1,1,5,1,9
	.half	.L583-.L582
	.byte	3,3,1,7,9
	.half	.L149-.L583
	.byte	0,1,1
.L574:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearAsclinIntFlags')
	.sect	'.debug_ranges'
.L148:
	.word	-1,.L3,0,.L149-.L3,0,0
.L393:
	.word	-1,.L3,0,.L392-.L3,-1,.L5,0,.L194-.L5,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearGptIntFlags')
	.sect	'.debug_info'
.L150:
	.word	228
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L153,.L152
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearGptIntFlags',0,1,207,1,13,1,1
	.word	.L7,.L394,.L6
	.byte	4
	.word	.L7,.L394
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearGptIntFlags')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearGptIntFlags')
	.sect	'.debug_line'
.L152:
	.word	.L585-.L584
.L584:
	.half	3
	.word	.L587-.L586
.L586:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L587:
	.byte	5,22,7,0,5,2
	.word	.L7
	.byte	3,210,1,1,5,20,9
	.half	.L588-.L7
	.byte	1,5,18,9
	.half	.L589-.L588
	.byte	3,1,1,9
	.half	.L590-.L589
	.byte	3,1,1,9
	.half	.L591-.L590
	.byte	3,1,1,9
	.half	.L592-.L591
	.byte	3,1,1,9
	.half	.L593-.L592
	.byte	3,1,1,5,1,9
	.half	.L594-.L593
	.byte	3,2,1,7,9
	.half	.L154-.L594
	.byte	0,1,1
.L585:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearGptIntFlags')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L7,0,.L154-.L7,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearGtmIntFlags')
	.sect	'.debug_info'
.L155:
	.word	228
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L158,.L157
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearGtmIntFlags',0,1,222,1,13,1,1
	.word	.L9,.L395,.L8
	.byte	4
	.word	.L9,.L395
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearGtmIntFlags')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearGtmIntFlags')
	.sect	'.debug_line'
.L157:
	.word	.L596-.L595
.L595:
	.half	3
	.word	.L598-.L597
.L597:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L598:
	.byte	5,21,7,0,5,2
	.word	.L9
	.byte	3,225,1,1,5,19,9
	.half	.L599-.L9
	.byte	1,5,16,9
	.half	.L600-.L599
	.byte	3,4,1,5,18,9
	.half	.L601-.L600
	.byte	3,4,1,9
	.half	.L602-.L601
	.byte	3,1,1,9
	.half	.L603-.L602
	.byte	3,1,1,9
	.half	.L604-.L603
	.byte	3,1,1,9
	.half	.L605-.L604
	.byte	3,1,1,9
	.half	.L606-.L605
	.byte	3,1,1,9
	.half	.L607-.L606
	.byte	3,1,1,9
	.half	.L608-.L607
	.byte	3,1,1,9
	.half	.L609-.L608
	.byte	3,4,1,9
	.half	.L610-.L609
	.byte	3,1,1,9
	.half	.L611-.L610
	.byte	3,1,1,9
	.half	.L612-.L611
	.byte	3,1,1,9
	.half	.L613-.L612
	.byte	3,1,1,9
	.half	.L614-.L613
	.byte	3,1,1,9
	.half	.L615-.L614
	.byte	3,1,1,9
	.half	.L616-.L615
	.byte	3,1,1,9
	.half	.L617-.L616
	.byte	3,4,1,9
	.half	.L618-.L617
	.byte	3,1,1,9
	.half	.L619-.L618
	.byte	3,1,1,9
	.half	.L620-.L619
	.byte	3,1,1,9
	.half	.L621-.L620
	.byte	3,1,1,9
	.half	.L622-.L621
	.byte	3,1,1,9
	.half	.L623-.L622
	.byte	3,1,1,9
	.half	.L624-.L623
	.byte	3,1,1,5,1,9
	.half	.L625-.L624
	.byte	3,3,1,7,9
	.half	.L159-.L625
	.byte	0,1,1
.L596:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearGtmIntFlags')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L9,0,.L159-.L9,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearGpsrGroupIntFlags')
	.sect	'.debug_info'
.L160:
	.word	234
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L163,.L162
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearGpsrGroupIntFlags',0,1,245,2,13,1,1
	.word	.L11,.L396,.L10
	.byte	4
	.word	.L11,.L396
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearGpsrGroupIntFlags')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearGpsrGroupIntFlags')
	.sect	'.debug_line'
.L162:
	.word	.L627-.L626
.L626:
	.half	3
	.word	.L629-.L628
.L628:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L629:
	.byte	5,18,7,0,5,2
	.word	.L11
	.byte	3,248,2,1,5,16,9
	.half	.L630-.L11
	.byte	1,5,1,9
	.half	.L631-.L630
	.byte	3,2,1,7,9
	.half	.L164-.L631
	.byte	0,1,1
.L627:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearGpsrGroupIntFlags')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L11,0,.L164-.L11,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearSpiIntFlags')
	.sect	'.debug_info'
.L165:
	.word	228
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L168,.L167
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearSpiIntFlags',0,1,255,2,13,1,1
	.word	.L13,.L397,.L12
	.byte	4
	.word	.L13,.L397
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearSpiIntFlags')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearSpiIntFlags')
	.sect	'.debug_line'
.L167:
	.word	.L633-.L632
.L632:
	.half	3
	.word	.L635-.L634
.L634:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L635:
	.byte	5,20,7,0,5,2
	.word	.L13
	.byte	3,130,3,1,5,18,9
	.half	.L636-.L13
	.byte	1,9
	.half	.L637-.L636
	.byte	3,1,1,9
	.half	.L638-.L637
	.byte	3,1,1,9
	.half	.L639-.L638
	.byte	3,1,1,9
	.half	.L640-.L639
	.byte	3,1,1,9
	.half	.L641-.L640
	.byte	3,4,1,9
	.half	.L642-.L641
	.byte	3,1,1,9
	.half	.L643-.L642
	.byte	3,1,1,9
	.half	.L644-.L643
	.byte	3,1,1,9
	.half	.L645-.L644
	.byte	3,1,1,9
	.half	.L646-.L645
	.byte	3,4,1,9
	.half	.L647-.L646
	.byte	3,1,1,9
	.half	.L648-.L647
	.byte	3,1,1,9
	.half	.L649-.L648
	.byte	3,1,1,9
	.half	.L650-.L649
	.byte	3,1,1,9
	.half	.L651-.L650
	.byte	3,1,1,9
	.half	.L652-.L651
	.byte	3,4,1,9
	.half	.L653-.L652
	.byte	3,1,1,9
	.half	.L654-.L653
	.byte	3,1,1,9
	.half	.L655-.L654
	.byte	3,1,1,9
	.half	.L656-.L655
	.byte	3,1,1,9
	.half	.L657-.L656
	.byte	3,1,1,5,1,9
	.half	.L658-.L657
	.byte	3,21,1,7,9
	.half	.L169-.L658
	.byte	0,1,1
.L633:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearSpiIntFlags')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L13,0,.L169-.L13,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearAdcIntFlags')
	.sect	'.debug_info'
.L170:
	.word	228
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L173,.L172
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearAdcIntFlags',0,1,186,3,13,1,1
	.word	.L15,.L398,.L14
	.byte	4
	.word	.L15,.L398
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearAdcIntFlags')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearAdcIntFlags')
	.sect	'.debug_line'
.L172:
	.word	.L660-.L659
.L659:
	.half	3
	.word	.L662-.L661
.L661:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L662:
	.byte	5,21,7,0,5,2
	.word	.L15
	.byte	3,189,3,1,5,19,9
	.half	.L663-.L15
	.byte	1,9
	.half	.L664-.L663
	.byte	3,1,1,9
	.half	.L665-.L664
	.byte	3,1,1,9
	.half	.L666-.L665
	.byte	3,1,1,9
	.half	.L667-.L666
	.byte	3,4,1,9
	.half	.L668-.L667
	.byte	3,1,1,9
	.half	.L669-.L668
	.byte	3,1,1,9
	.half	.L670-.L669
	.byte	3,1,1,5,1,9
	.half	.L671-.L670
	.byte	3,24,1,7,9
	.half	.L174-.L671
	.byte	0,1,1
.L660:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearAdcIntFlags')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L15,0,.L174-.L15,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearDmaIntFlags')
	.sect	'.debug_info'
.L175:
	.word	228
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L178,.L177
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearDmaIntFlags',0,1,255,3,13,1,1
	.word	.L17,.L399,.L16
	.byte	4
	.word	.L17,.L399
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearDmaIntFlags')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearDmaIntFlags')
	.sect	'.debug_line'
.L177:
	.word	.L673-.L672
.L672:
	.half	3
	.word	.L675-.L674
.L674:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L675:
	.byte	5,18,7,0,5,2
	.word	.L17
	.byte	3,129,4,1,5,16,9
	.half	.L676-.L17
	.byte	1,9
	.half	.L677-.L676
	.byte	3,3,1,9
	.half	.L678-.L677
	.byte	3,1,1,9
	.half	.L679-.L678
	.byte	3,1,1,9
	.half	.L680-.L679
	.byte	3,1,1,9
	.half	.L681-.L680
	.byte	3,1,1,9
	.half	.L682-.L681
	.byte	3,1,1,9
	.half	.L683-.L682
	.byte	3,1,1,9
	.half	.L684-.L683
	.byte	3,1,1,9
	.half	.L685-.L684
	.byte	3,1,1,9
	.half	.L686-.L685
	.byte	3,1,1,5,17,9
	.half	.L687-.L686
	.byte	3,1,1,9
	.half	.L688-.L687
	.byte	3,1,1,9
	.half	.L689-.L688
	.byte	3,1,1,9
	.half	.L690-.L689
	.byte	3,1,1,9
	.half	.L691-.L690
	.byte	3,1,1,9
	.half	.L692-.L691
	.byte	3,1,1,5,1,9
	.half	.L693-.L692
	.byte	3,3,1,7,9
	.half	.L179-.L693
	.byte	0,1,1
.L673:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearDmaIntFlags')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L17,0,.L179-.L17,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearStmIntFlags')
	.sect	'.debug_info'
.L180:
	.word	228
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L183,.L182
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearStmIntFlags',0,1,155,4,13,1,1
	.word	.L19,.L400,.L18
	.byte	4
	.word	.L19,.L400
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearStmIntFlags')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearStmIntFlags')
	.sect	'.debug_line'
.L182:
	.word	.L695-.L694
.L694:
	.half	3
	.word	.L697-.L696
.L696:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L697:
	.byte	5,19,7,0,5,2
	.word	.L19
	.byte	3,158,4,1,5,17,9
	.half	.L698-.L19
	.byte	1,9
	.half	.L699-.L698
	.byte	3,1,1,5,1,9
	.half	.L700-.L699
	.byte	3,3,1,7,9
	.half	.L184-.L700
	.byte	0,1,1
.L695:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearStmIntFlags')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L19,0,.L184-.L19,0,0
	.sdecl	'.debug_info',debug,cluster('Irq_ClearHsmIntFlags')
	.sect	'.debug_info'
.L185:
	.word	228
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L188,.L187
	.byte	2
	.word	.L56
	.byte	3
	.byte	'Irq_ClearHsmIntFlags',0,1,212,4,13,1,1
	.word	.L21,.L401,.L20
	.byte	4
	.word	.L21,.L401
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Irq_ClearHsmIntFlags')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Irq_ClearHsmIntFlags')
	.sect	'.debug_line'
.L187:
	.word	.L702-.L701
.L701:
	.half	3
	.word	.L704-.L703
.L703:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L704:
	.byte	5,16,7,0,5,2
	.word	.L21
	.byte	3,214,4,1,5,14,9
	.half	.L705-.L21
	.byte	1,9
	.half	.L706-.L705
	.byte	3,3,1,5,1,9
	.half	.L707-.L706
	.byte	3,2,1,7,9
	.half	.L189-.L707
	.byte	0,1,1
.L702:
	.sdecl	'.debug_ranges',debug,cluster('Irq_ClearHsmIntFlags')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L21,0,.L189-.L21,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L190:
	.word	207
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'..\\mcal_src\\Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L193,.L192
	.byte	2
	.word	.L56
	.byte	3
	.byte	'.cocofun_1',0,1,168,1,13,1
	.word	.L5,.L194,.L4
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L192:
	.word	.L709-.L708
.L708:
	.half	3
	.word	.L711-.L710
.L710:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Irq.c',0,0,0,0,0
.L711:
	.byte	5,21,7,0,5,2
	.word	.L5
	.byte	3,171,1,1,5,19,1,9
	.half	.L194-.L5
	.byte	0,1,1,5,22,0,5,2
	.word	.L5
	.byte	3,210,1,1,5,20,1,5,19,9
	.half	.L712-.L5
	.byte	3,89,1,7,9
	.half	.L194-.L712
	.byte	0,1,1,5,21,0,5,2
	.word	.L5
	.byte	3,225,1,1,5,19,1,9
	.half	.L712-.L5
	.byte	3,74,1,7,9
	.half	.L194-.L712
	.byte	0,1,1,5,18,0,5,2
	.word	.L5
	.byte	3,248,2,1,5,16,1,5,19,9
	.half	.L712-.L5
	.byte	3,179,126,1,7,9
	.half	.L194-.L712
	.byte	0,1,1,5,20,0,5,2
	.word	.L5
	.byte	3,130,3,1,5,18,1,5,19,9
	.half	.L712-.L5
	.byte	3,169,126,1,7,9
	.half	.L194-.L712
	.byte	0,1,1,5,21,0,5,2
	.word	.L5
	.byte	3,189,3,1,5,19,1,9
	.half	.L712-.L5
	.byte	3,238,125,1,7,9
	.half	.L194-.L712
	.byte	0,1,1,5,18,0,5,2
	.word	.L5
	.byte	3,129,4,1,5,16,1,5,19,9
	.half	.L712-.L5
	.byte	3,170,125,1,7,9
	.half	.L194-.L712
	.byte	0,1,1,5,19,0,5,2
	.word	.L5
	.byte	3,158,4,1,5,17,1,5,19,9
	.half	.L712-.L5
	.byte	3,141,125,1,7,9
	.half	.L194-.L712
	.byte	0,1,1,5,16,0,5,2
	.word	.L5
	.byte	3,214,4,1,5,14,1,5,19,9
	.half	.L712-.L5
	.byte	3,213,124,1,7,9
	.half	.L194-.L712
	.byte	0,1,1
.L709:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L5,0,.L194-.L5,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L194-.L5
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqAdc_Init')
	.sect	'.debug_loc'
.L36:
	.word	-1,.L37,0,.L331-.L37
	.half	2
	.byte	138,0
	.word	0,0
.L337:
	.word	-1,.L37,.L465-.L37,.L466-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L345:
	.word	-1,.L37,.L469-.L37,.L470-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L347:
	.word	-1,.L37,.L470-.L37,.L331-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L339:
	.word	-1,.L37,.L466-.L37,.L467-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L343:
	.word	-1,.L37,.L468-.L37,.L469-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L341:
	.word	-1,.L37,.L467-.L37,.L468-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L335:
	.word	-1,.L37,.L464-.L37,.L465-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L333:
	.word	-1,.L37,.L463-.L37,.L464-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqAscLin_Init')
	.sect	'.debug_loc'
.L22:
	.word	-1,.L23,0,.L195-.L23
	.half	2
	.byte	138,0
	.word	0,0
.L208:
	.word	-1,.L23,.L407-.L23,.L195-.L23
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L198:
	.word	-1,.L23,.L402-.L23,.L403-.L23
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L200:
	.word	-1,.L23,.L403-.L23,.L404-.L23
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L202:
	.word	-1,.L23,.L404-.L23,.L405-.L23
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L206:
	.word	-1,.L23,.L406-.L23,.L407-.L23
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L204:
	.word	-1,.L23,.L405-.L23,.L406-.L23
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqCan_Init')
	.sect	'.debug_loc'
.L30:
	.word	-1,.L31,0,.L210-.L31
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqCcu6_Init')
	.sect	'.debug_loc'
.L24:
	.word	-1,.L25,0,.L209-.L25
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqDma_Init')
	.sect	'.debug_loc'
.L42:
	.word	-1,.L43,0,.L349-.L43
	.half	2
	.byte	138,0
	.word	0,0
.L358:
	.word	-1,.L43,.L474-.L43,.L475-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L360:
	.word	-1,.L43,.L475-.L43,.L476-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L354:
	.word	-1,.L43,.L472-.L43,.L473-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L372:
	.word	-1,.L43,.L481-.L43,.L482-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L382:
	.word	-1,.L43,.L486-.L43,.L349-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L368:
	.word	-1,.L43,.L479-.L43,.L480-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L374:
	.word	-1,.L43,.L482-.L43,.L483-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L364:
	.word	-1,.L43,.L477-.L43,.L478-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L378:
	.word	-1,.L43,.L484-.L43,.L485-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L366:
	.word	-1,.L43,.L478-.L43,.L479-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L376:
	.word	-1,.L43,.L483-.L43,.L484-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L380:
	.word	-1,.L43,.L485-.L43,.L486-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L356:
	.word	-1,.L43,.L473-.L43,.L474-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L362:
	.word	-1,.L43,.L476-.L43,.L477-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L352:
	.word	-1,.L43,.L471-.L43,.L472-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L370:
	.word	-1,.L43,.L480-.L43,.L481-.L43
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqEthernet_Init')
	.sect	'.debug_loc'
.L40:
	.word	-1,.L41,0,.L348-.L41
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqFlexray_Init')
	.sect	'.debug_loc'
.L38:
	.word	-1,.L39,0,.L285-.L39
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqGpsrGroup_Init')
	.sect	'.debug_loc'
.L32:
	.word	-1,.L33,0,.L282-.L33
	.half	2
	.byte	138,0
	.word	0,0
.L284:
	.word	-1,.L33,.L440-.L33,.L282-.L33
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqGpt_Init')
	.sect	'.debug_loc'
.L26:
	.word	-1,.L27,0,.L216-.L27
	.half	2
	.byte	138,0
	.word	0,0
.L228:
	.word	-1,.L27,.L413-.L27,.L216-.L27
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L222:
	.word	-1,.L27,.L410-.L27,.L411-.L27
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L220:
	.word	-1,.L27,.L409-.L27,.L410-.L27
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L224:
	.word	-1,.L27,.L411-.L27,.L412-.L27
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L226:
	.word	-1,.L27,.L412-.L27,.L413-.L27
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L218:
	.word	-1,.L27,.L408-.L27,.L409-.L27
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqGtm_Init')
	.sect	'.debug_loc'
.L28:
	.word	-1,.L29,0,.L229-.L29
	.half	2
	.byte	138,0
	.word	0,0
.L245:
	.word	-1,.L29,.L421-.L29,.L422-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L251:
	.word	-1,.L29,.L424-.L29,.L425-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L247:
	.word	-1,.L29,.L422-.L29,.L423-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L237:
	.word	-1,.L29,.L417-.L29,.L418-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L271:
	.word	-1,.L29,.L434-.L29,.L435-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L243:
	.word	-1,.L29,.L420-.L29,.L421-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L277:
	.word	-1,.L29,.L437-.L29,.L438-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L281:
	.word	-1,.L29,.L439-.L29,.L229-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L255:
	.word	-1,.L29,.L426-.L29,.L427-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L253:
	.word	-1,.L29,.L425-.L29,.L426-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L265:
	.word	-1,.L29,.L431-.L29,.L432-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L241:
	.word	-1,.L29,.L419-.L29,.L420-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L235:
	.word	-1,.L29,.L416-.L29,.L417-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L275:
	.word	-1,.L29,.L436-.L29,.L437-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L259:
	.word	-1,.L29,.L428-.L29,.L429-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L231:
	.word	-1,.L29,.L414-.L29,.L415-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L239:
	.word	-1,.L29,.L418-.L29,.L419-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L279:
	.word	-1,.L29,.L438-.L29,.L439-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L233:
	.word	-1,.L29,.L415-.L29,.L416-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L267:
	.word	-1,.L29,.L432-.L29,.L433-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L257:
	.word	-1,.L29,.L427-.L29,.L428-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L263:
	.word	-1,.L29,.L430-.L29,.L431-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L273:
	.word	-1,.L29,.L435-.L29,.L436-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L249:
	.word	-1,.L29,.L423-.L29,.L424-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L269:
	.word	-1,.L29,.L433-.L29,.L434-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L261:
	.word	-1,.L29,.L429-.L29,.L430-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqHsm_Init')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L211-.L53
	.half	2
	.byte	138,0
	.word	0,0
.L215:
	.word	-1,.L53,.L490-.L53,.L211-.L53
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L213:
	.word	-1,.L53,.L489-.L53,.L490-.L53
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqPmu_Init')
	.sect	'.debug_loc'
.L48:
	.word	-1,.L49,0,.L384-.L49
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqScu_Init')
	.sect	'.debug_loc'
.L46:
	.word	-1,.L47,0,.L383-.L47
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqSent_Init')
	.sect	'.debug_loc'
.L50:
	.word	-1,.L51,0,.L385-.L51
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqSpi_Init')
	.sect	'.debug_loc'
.L34:
	.word	-1,.L35,0,.L286-.L35
	.half	2
	.byte	138,0
	.word	0,0
.L306:
	.word	-1,.L35,.L450-.L35,.L451-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L322:
	.word	-1,.L35,.L321-.L35,.L459-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L320:
	.word	-1,.L35,.L457-.L35,.L458-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L292:
	.word	-1,.L35,.L443-.L35,.L444-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L318:
	.word	-1,.L35,.L456-.L35,.L457-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L288:
	.word	-1,.L35,.L441-.L35,.L442-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L328:
	.word	-1,.L35,.L461-.L35,.L462-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L304:
	.word	-1,.L35,.L449-.L35,.L450-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L310:
	.word	-1,.L35,.L452-.L35,.L453-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L290:
	.word	-1,.L35,.L442-.L35,.L443-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L298:
	.word	-1,.L35,.L297-.L35,.L447-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L302:
	.word	-1,.L35,.L448-.L35,.L449-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L296:
	.word	-1,.L35,.L445-.L35,.L446-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L294:
	.word	-1,.L35,.L444-.L35,.L445-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L330:
	.word	-1,.L35,.L462-.L35,.L286-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L300:
	.word	-1,.L35,.L299-.L35,.L448-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L324:
	.word	-1,.L35,.L459-.L35,.L460-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L308:
	.word	-1,.L35,.L451-.L35,.L452-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L312:
	.word	-1,.L35,.L453-.L35,.L454-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L314:
	.word	-1,.L35,.L454-.L35,.L455-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L316:
	.word	-1,.L35,.L455-.L35,.L456-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L326:
	.word	-1,.L35,.L460-.L35,.L461-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IrqStm_Init')
	.sect	'.debug_loc'
.L44:
	.word	-1,.L45,0,.L387-.L45
	.half	2
	.byte	138,0
	.word	0,0
.L389:
	.word	-1,.L45,.L487-.L45,.L488-.L45
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L391:
	.word	-1,.L45,.L488-.L45,.L387-.L45
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearAdcIntFlags')
	.sect	'.debug_loc'
.L14:
	.word	-1,.L15,0,.L398-.L15
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearAllInterruptFlags')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L386-.L55
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearAsclinIntFlags')
	.sect	'.debug_loc'
.L2:
	.word	-1,.L3,0,.L392-.L3
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearDmaIntFlags')
	.sect	'.debug_loc'
.L16:
	.word	-1,.L17,0,.L399-.L17
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearGpsrGroupIntFlags')
	.sect	'.debug_loc'
.L10:
	.word	-1,.L11,0,.L396-.L11
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearGptIntFlags')
	.sect	'.debug_loc'
.L6:
	.word	-1,.L7,0,.L394-.L7
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearGtmIntFlags')
	.sect	'.debug_loc'
.L8:
	.word	-1,.L9,0,.L395-.L9
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearHsmIntFlags')
	.sect	'.debug_loc'
.L20:
	.word	-1,.L21,0,.L401-.L21
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearSpiIntFlags')
	.sect	'.debug_loc'
.L12:
	.word	-1,.L13,0,.L397-.L13
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Irq_ClearStmIntFlags')
	.sect	'.debug_loc'
.L18:
	.word	-1,.L19,0,.L400-.L19
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L713:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearAsclinIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L3,.L392-.L3
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearGptIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L7,.L394-.L7
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearGtmIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L9,.L395-.L9
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearGpsrGroupIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L11,.L396-.L11
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearSpiIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L13,.L397-.L13
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearAdcIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L15,.L398-.L15
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearDmaIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L17,.L399-.L17
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearStmIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L19,.L400-.L19
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearHsmIntFlags')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L21,.L401-.L21
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqAscLin_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L23,.L195-.L23
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqCcu6_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L25,.L209-.L25
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqGpt_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L27,.L216-.L27
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqGtm_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L29,.L229-.L29
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqCan_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L31,.L210-.L31
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqGpsrGroup_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L33,.L282-.L33
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqSpi_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L35,.L286-.L35
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqAdc_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L37,.L331-.L37
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqFlexray_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L39,.L285-.L39
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqEthernet_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L41,.L348-.L41
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqDma_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L43,.L349-.L43
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqStm_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L45,.L387-.L45
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqScu_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L47,.L383-.L47
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqPmu_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L49,.L384-.L49
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqSent_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L51,.L385-.L51
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IrqHsm_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L713,.L53,.L211-.L53
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Irq_ClearAllInterruptFlags')
	.sect	'.debug_frame'
	.word	12
	.word	.L713,.L55,.L386-.L55
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L714:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L714,.L5,.L194-.L5
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Irq.c	  1612    #endif
; ..\mcal_src\Irq.c	  1613  
; ..\mcal_src\Irq.c	  1614  }
; ..\mcal_src\Irq.c	  1615  #endif /* (IFX_MCAL_USED == STD_ON) */
; ..\mcal_src\Irq.c	  1616  
; ..\mcal_src\Irq.c	  1617  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Irq.c	  1618  #define IRQ_STOP_SEC_CODE
; ..\mcal_src\Irq.c	  1619  #include "MemMap.h"
; ..\mcal_src\Irq.c	  1620  #else
; ..\mcal_src\Irq.c	  1621  #define IFX_IRQ_STOP_SEC_CODE_ASIL_B
; ..\mcal_src\Irq.c	  1622  #include "Ifx_MemMap.h"
; ..\mcal_src\Irq.c	  1623  #endif
; ..\mcal_src\Irq.c	  1624  
; ..\mcal_src\Irq.c	  1625  

	; Module end
