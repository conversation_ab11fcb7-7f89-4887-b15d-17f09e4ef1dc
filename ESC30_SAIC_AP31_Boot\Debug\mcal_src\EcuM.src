	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc27568a -c99 --dep-file=mcal_src\\.EcuM.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\EcuM.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\EcuM.src ..\\mcal_src\\EcuM.c"
	.compiler_name		"ctc"
	.name	"EcuM"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('EcuM_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	EcuM_Init

; ..\mcal_src\EcuM.c	     1  /******************************************************************************
; ..\mcal_src\EcuM.c	     2  **                                                                           **
; ..\mcal_src\EcuM.c	     3  ** Copyright (C) Infineon Technologies (2017)                                **
; ..\mcal_src\EcuM.c	     4  **                                                                           **
; ..\mcal_src\EcuM.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\EcuM.c	     6  **                                                                           **
; ..\mcal_src\EcuM.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\EcuM.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\EcuM.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\EcuM.c	    10  **                                                                           **
; ..\mcal_src\EcuM.c	    11  *******************************************************************************
; ..\mcal_src\EcuM.c	    12  **                                                                           **
; ..\mcal_src\EcuM.c	    13  **  $FILENAME   : EcuM.c $                                                   **
; ..\mcal_src\EcuM.c	    14  **                                                                           **
; ..\mcal_src\EcuM.c	    15  **  $CC VERSION : \main\8 $                                                  **
; ..\mcal_src\EcuM.c	    16  **                                                                           **
; ..\mcal_src\EcuM.c	    17  **  $DATE       : 2017-12-11 $                                               **
; ..\mcal_src\EcuM.c	    18  **                                                                           **
; ..\mcal_src\EcuM.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\EcuM.c	    20  **                                                                           **
; ..\mcal_src\EcuM.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\EcuM.c	    22  **                                                                           **
; ..\mcal_src\EcuM.c	    23  **  DESCRIPTION  : Contains a simple example of ECU State Manager Code       **
; ..\mcal_src\EcuM.c	    24  **                 This file is for Evaluation Purpose Only                  **
; ..\mcal_src\EcuM.c	    25  **                                                                           **
; ..\mcal_src\EcuM.c	    26  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\EcuM.c	    27  **                                                                           **
; ..\mcal_src\EcuM.c	    28  ******************************************************************************/
; ..\mcal_src\EcuM.c	    29  /*******************************************************************************
; ..\mcal_src\EcuM.c	    30  **                      Includes                                              **
; ..\mcal_src\EcuM.c	    31  *******************************************************************************/
; ..\mcal_src\EcuM.c	    32  #include "EcuM.h"
; ..\mcal_src\EcuM.c	    33  #include "EcuM_Cbk.h"
; ..\mcal_src\EcuM.c	    34  #include "Mcu.h"
; ..\mcal_src\EcuM.c	    35  
; ..\mcal_src\EcuM.c	    36  /*******************************************************************************
; ..\mcal_src\EcuM.c	    37  **                      Imported Compiler Switch Check                        **
; ..\mcal_src\EcuM.c	    38  *******************************************************************************/
; ..\mcal_src\EcuM.c	    39  
; ..\mcal_src\EcuM.c	    40  /*******************************************************************************
; ..\mcal_src\EcuM.c	    41  **                      Private Macro Definitions                             **
; ..\mcal_src\EcuM.c	    42  *******************************************************************************/
; ..\mcal_src\EcuM.c	    43  
; ..\mcal_src\EcuM.c	    44  /*******************************************************************************
; ..\mcal_src\EcuM.c	    45  **                      Private Type Definitions                              **
; ..\mcal_src\EcuM.c	    46  *******************************************************************************/
; ..\mcal_src\EcuM.c	    47  
; ..\mcal_src\EcuM.c	    48  /*******************************************************************************
; ..\mcal_src\EcuM.c	    49  **                      Private Function Declarations                         **
; ..\mcal_src\EcuM.c	    50  *******************************************************************************/
; ..\mcal_src\EcuM.c	    51  
; ..\mcal_src\EcuM.c	    52  /*******************************************************************************
; ..\mcal_src\EcuM.c	    53  **                      Global Constant Definitions                           **
; ..\mcal_src\EcuM.c	    54  *******************************************************************************/
; ..\mcal_src\EcuM.c	    55  
; ..\mcal_src\EcuM.c	    56  /*******************************************************************************
; ..\mcal_src\EcuM.c	    57  **                      Global Variable Definitions                           **
; ..\mcal_src\EcuM.c	    58  *******************************************************************************/
; ..\mcal_src\EcuM.c	    59  
; ..\mcal_src\EcuM.c	    60  
; ..\mcal_src\EcuM.c	    61  /*******************************************************************************
; ..\mcal_src\EcuM.c	    62  **                      Private Constant Definitions                          **
; ..\mcal_src\EcuM.c	    63  *******************************************************************************/
; ..\mcal_src\EcuM.c	    64  
; ..\mcal_src\EcuM.c	    65  /*******************************************************************************
; ..\mcal_src\EcuM.c	    66  **                      Private Variable Definitions                          **
; ..\mcal_src\EcuM.c	    67  *******************************************************************************/
; ..\mcal_src\EcuM.c	    68  /*******************************************************************************
; ..\mcal_src\EcuM.c	    69  **                      Global Functon Definitions                            **
; ..\mcal_src\EcuM.c	    70  *******************************************************************************/
; ..\mcal_src\EcuM.c	    71  #define ECUM_START_SEC_CODE
; ..\mcal_src\EcuM.c	    72  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives 
; ..\mcal_src\EcuM.c	    73    is allowed only for MemMap.h*/
; ..\mcal_src\EcuM.c	    74  #include "MemMap.h"
; ..\mcal_src\EcuM.c	    75  
; ..\mcal_src\EcuM.c	    76  /*******************************************************************************
; ..\mcal_src\EcuM.c	    77  ** Syntax           : void EcuM_Init(const EcuM_ConfigType *configptr)        **
; ..\mcal_src\EcuM.c	    78  **                                                                            **
; ..\mcal_src\EcuM.c	    79  ** Service ID       : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	    80  **                                                                            **
; ..\mcal_src\EcuM.c	    81  ** Sync/Async       : Synchronous / Asynchronous                              **
; ..\mcal_src\EcuM.c	    82  **                                                                            **
; ..\mcal_src\EcuM.c	    83  ** Reentrancy       : Non-reentrant / Reentrant                               **
; ..\mcal_src\EcuM.c	    84  **                                                                            **
; ..\mcal_src\EcuM.c	    85  ** Parameters(in)   : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	    86  **                                                                            **
; ..\mcal_src\EcuM.c	    87  ** Parameters (out) : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	    88  **                                                                            **
; ..\mcal_src\EcuM.c	    89  ** Return value     : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	    90  **                                                                            **
; ..\mcal_src\EcuM.c	    91  ** Description      : <Suitable Description>                                  **
; ..\mcal_src\EcuM.c	    92  **                                                                            **                                                                                                                                 **
; ..\mcal_src\EcuM.c	    93  *******************************************************************************/
; ..\mcal_src\EcuM.c	    94  
; ..\mcal_src\EcuM.c	    95  void EcuM_Init(const EcuM_ConfigType *configptr) 
; Function EcuM_Init
.L4:
EcuM_Init:	.type	func
	sub.a	a10,#8
.L47:
	mov.aa	a15,a4
.L49:

; ..\mcal_src\EcuM.c	    96  {
; ..\mcal_src\EcuM.c	    97    volatile uint32 ConfError;
; ..\mcal_src\EcuM.c	    98    
; ..\mcal_src\EcuM.c	    99    ConfError = 0;
	mov	d15,#0
	st.w	[a10],d15
.L75:

; ..\mcal_src\EcuM.c	   100    
; ..\mcal_src\EcuM.c	   101    
; ..\mcal_src\EcuM.c	   102    /* Check Consistency of configuration data */
; ..\mcal_src\EcuM.c	   103    if(EcuM_ConfigConsistencyHash !=  configptr->PreCompileIdentifier) 
	movh.a	a2,#@his(EcuM_ConfigConsistencyHash)
	ld.w	d15,[a2]@los(EcuM_ConfigConsistencyHash)
.L76:
	ld.w	d0,[a15]2
.L77:
	jeq	d15,d0,.L2
.L78:

; ..\mcal_src\EcuM.c	   104    {
; ..\mcal_src\EcuM.c	   105      /* printf("Configuration mismatch!!!\n"); */
; ..\mcal_src\EcuM.c	   106      ConfError=2;
	mov	d15,#2
	st.w	[a10],d15
.L2:

; ..\mcal_src\EcuM.c	   107    }
; ..\mcal_src\EcuM.c	   108    if (ConfError!=0)  
	ld.w	d15,[a10]
.L79:

; ..\mcal_src\EcuM.c	   109    {
; ..\mcal_src\EcuM.c	   110      /* print_f("\nConfiguration Problem\n "); */
; ..\mcal_src\EcuM.c	   111    }
; ..\mcal_src\EcuM.c	   112             
; ..\mcal_src\EcuM.c	   113    /*Initialize DET Module*/
; ..\mcal_src\EcuM.c	   114    /*Det_Init();*/
; ..\mcal_src\EcuM.c	   115    
; ..\mcal_src\EcuM.c	   116    /*Pre-Initialize DEM Module*/
; ..\mcal_src\EcuM.c	   117    /*Dem_PreInit()*/
; ..\mcal_src\EcuM.c	   118  
; ..\mcal_src\EcuM.c	   119    /*Call Driver Init Zero. Initialize drivers before OS Init*/
; ..\mcal_src\EcuM.c	   120    /* no parameters for this function, call modules like DET or 
; ..\mcal_src\EcuM.c	   121       pre compile, LT modules */
; ..\mcal_src\EcuM.c	   122    EcuM_AL_DriverInitZero();
	call	EcuM_AL_DriverInitZero
.L48:

; ..\mcal_src\EcuM.c	   123    
; ..\mcal_src\EcuM.c	   124    /* List one is mandated with MCU configuration */
; ..\mcal_src\EcuM.c	   125    /*Initialize MCU Driver*/
; ..\mcal_src\EcuM.c	   126    #if(MCU_USES_FIXED_ADDR == STD_ON)
; ..\mcal_src\EcuM.c	   127    Mcu_Init(&Mcu_ConfigRoot[0]);
; ..\mcal_src\EcuM.c	   128    #else
; ..\mcal_src\EcuM.c	   129    Mcu_Init(configptr->Mcu_ConfigData);
	ld.a	a4,[a15]8
	call	Mcu_Init
.L80:

; ..\mcal_src\EcuM.c	   130    #endif
; ..\mcal_src\EcuM.c	   131       
; ..\mcal_src\EcuM.c	   132    /*Call Driver Init One. Initialize drivers before OS Init*/
; ..\mcal_src\EcuM.c	   133    EcuM_AL_DriverInitOne(configptr);
	mov.aa	a4,a15
.L50:
	call	EcuM_AL_DriverInitOne
.L51:

; ..\mcal_src\EcuM.c	   134    
; ..\mcal_src\EcuM.c	   135    /*Select Shut Down Target*/
; ..\mcal_src\EcuM.c	   136    
; ..\mcal_src\EcuM.c	   137    /* Call OS */  
; ..\mcal_src\EcuM.c	   138   
; ..\mcal_src\EcuM.c	   139    /*Call Driver Init Two. Initialize drivers after OS Init*/
; ..\mcal_src\EcuM.c	   140    EcuM_AL_DriverInitTwo(configptr);
	mov.aa	a4,a15
.L52:
	call	EcuM_AL_DriverInitTwo
.L53:

; ..\mcal_src\EcuM.c	   141    /*Call Driver Init Three. Initialize drivers after OS Init*/
; ..\mcal_src\EcuM.c	   142    EcuM_AL_DriverInitThree(configptr);
	mov.aa	a4,a15
.L54:
	j	EcuM_AL_DriverInitThree
.L42:
	
__EcuM_Init_function_end:
	.size	EcuM_Init,__EcuM_Init_function_end-EcuM_Init
.L34:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('EcuM_SetWakeupEvent')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	EcuM_SetWakeupEvent

; ..\mcal_src\EcuM.c	   143  
; ..\mcal_src\EcuM.c	   144  
; ..\mcal_src\EcuM.c	   145  
; ..\mcal_src\EcuM.c	   146  
; ..\mcal_src\EcuM.c	   147  
; ..\mcal_src\EcuM.c	   148  }
; ..\mcal_src\EcuM.c	   149  /*******************************************************************************
; ..\mcal_src\EcuM.c	   150  ** Syntax           : void EcuM_SetWakeupEvent(EcuM_WakeupSourceType events)  **
; ..\mcal_src\EcuM.c	   151  **                                                                            **
; ..\mcal_src\EcuM.c	   152  ** Service ID       : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   153  **                                                                            **
; ..\mcal_src\EcuM.c	   154  ** Sync/Async       : Synchronous / Asynchronous                              **
; ..\mcal_src\EcuM.c	   155  **                                                                            **
; ..\mcal_src\EcuM.c	   156  ** Reentrancy       : Non-reentrant / Reentrant                               **
; ..\mcal_src\EcuM.c	   157  **                                                                            **
; ..\mcal_src\EcuM.c	   158  ** Parameters(in)   : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   159  **                                                                            **
; ..\mcal_src\EcuM.c	   160  ** Parameters (out) : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   161  **                                                                            **
; ..\mcal_src\EcuM.c	   162  ** Return value     : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   163  **                                                                            **
; ..\mcal_src\EcuM.c	   164  ** Description      : <Suitable Description>                                  **
; ..\mcal_src\EcuM.c	   165  **                                                                            **                                                                                                                                 **
; ..\mcal_src\EcuM.c	   166  *******************************************************************************/
; ..\mcal_src\EcuM.c	   167  void EcuM_SetWakeupEvent(EcuM_WakeupSourceType WakeupInfo)
; Function EcuM_SetWakeupEvent
.L6:
EcuM_SetWakeupEvent:	.type	func

; ..\mcal_src\EcuM.c	   168  {
; ..\mcal_src\EcuM.c	   169    UNUSED_PARAMETER(WakeupInfo)
; ..\mcal_src\EcuM.c	   170  }
	ret
.L35:
	
__EcuM_SetWakeupEvent_function_end:
	.size	EcuM_SetWakeupEvent,__EcuM_SetWakeupEvent_function_end-EcuM_SetWakeupEvent
.L19:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('EcuM_ValidateWakeupEvent')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	EcuM_ValidateWakeupEvent

; ..\mcal_src\EcuM.c	   171  /*******************************************************************************
; ..\mcal_src\EcuM.c	   172  ** Syntax           : void EcuM_ValidateWakeupEvent                           **
; ..\mcal_src\EcuM.c	   173  **                    (EcuM_WakeupSourceType events)                          **
; ..\mcal_src\EcuM.c	   174  **                                                                            **
; ..\mcal_src\EcuM.c	   175  ** Service ID       : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   176  **                                                                            **
; ..\mcal_src\EcuM.c	   177  ** Sync/Async       : Synchronous / Asynchronous                              **
; ..\mcal_src\EcuM.c	   178  **                                                                            **
; ..\mcal_src\EcuM.c	   179  ** Reentrancy       : Non-reentrant / Reentrant                               **
; ..\mcal_src\EcuM.c	   180  **                                                                            **
; ..\mcal_src\EcuM.c	   181  ** Parameters(in)   : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   182  **                                                                            **
; ..\mcal_src\EcuM.c	   183  ** Parameters (out) : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   184  **                                                                            **
; ..\mcal_src\EcuM.c	   185  ** Return value     : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   186  **                                                                            **
; ..\mcal_src\EcuM.c	   187  ** Description      : <Suitable Description>                                  **
; ..\mcal_src\EcuM.c	   188  **                                                                            **                                                                                                                                 **
; ..\mcal_src\EcuM.c	   189  *******************************************************************************/
; ..\mcal_src\EcuM.c	   190  void EcuM_ValidateWakeupEvent(EcuM_WakeupSourceType events)
; Function EcuM_ValidateWakeupEvent
.L8:
EcuM_ValidateWakeupEvent:	.type	func

; ..\mcal_src\EcuM.c	   191  {
; ..\mcal_src\EcuM.c	   192    UNUSED_PARAMETER(events)
; ..\mcal_src\EcuM.c	   193  }
	ret
.L38:
	
__EcuM_ValidateWakeupEvent_function_end:
	.size	EcuM_ValidateWakeupEvent,__EcuM_ValidateWakeupEvent_function_end-EcuM_ValidateWakeupEvent
.L24:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('EcuM_CheckWakeup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	EcuM_CheckWakeup

; ..\mcal_src\EcuM.c	   194  
; ..\mcal_src\EcuM.c	   195  /*******************************************************************************
; ..\mcal_src\EcuM.c	   196  ** Syntax           : void EcuM_CheckWakeup                                   **
; ..\mcal_src\EcuM.c	   197  **                    (EcuM_WakeupSourceType wakeupSource)                    **
; ..\mcal_src\EcuM.c	   198  **                                                                            **
; ..\mcal_src\EcuM.c	   199  ** Service ID       : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   200  **                                                                            **
; ..\mcal_src\EcuM.c	   201  ** Sync/Async       : Synchronous / Asynchronous                              **
; ..\mcal_src\EcuM.c	   202  **                                                                            **
; ..\mcal_src\EcuM.c	   203  ** Reentrancy       : Non-reentrant / Reentrant                               **
; ..\mcal_src\EcuM.c	   204  **                                                                            **
; ..\mcal_src\EcuM.c	   205  ** Parameters(in)   : wakeupSource                                            **
; ..\mcal_src\EcuM.c	   206  **                                                                            **
; ..\mcal_src\EcuM.c	   207  ** Parameters (out) : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   208  **                                                                            **
; ..\mcal_src\EcuM.c	   209  ** Return value     : None/<Specified>                                        **
; ..\mcal_src\EcuM.c	   210  **                                                                            **
; ..\mcal_src\EcuM.c	   211  ** Description      : <Suitable Description>                                  **
; ..\mcal_src\EcuM.c	   212  **                                                                            **                                                                                                                                 **
; ..\mcal_src\EcuM.c	   213  *******************************************************************************/
; ..\mcal_src\EcuM.c	   214  void EcuM_CheckWakeup(EcuM_WakeupSourceType wakeupSource)
; Function EcuM_CheckWakeup
.L10:
EcuM_CheckWakeup:	.type	func

; ..\mcal_src\EcuM.c	   215  {
; ..\mcal_src\EcuM.c	   216    UNUSED_PARAMETER(wakeupSource)
; ..\mcal_src\EcuM.c	   217  }
	ret
.L40:
	
__EcuM_CheckWakeup_function_end:
	.size	EcuM_CheckWakeup,__EcuM_CheckWakeup_function_end-EcuM_CheckWakeup
.L29:
	; End of function
	
	.calls	'EcuM_Init','EcuM_AL_DriverInitZero'
	.calls	'EcuM_Init','Mcu_Init'
	.calls	'EcuM_Init','EcuM_AL_DriverInitOne'
	.calls	'EcuM_Init','EcuM_AL_DriverInitTwo'
	.calls	'EcuM_Init','EcuM_AL_DriverInitThree'
	.calls	'EcuM_Init','',8
	.calls	'EcuM_SetWakeupEvent','',0
	.calls	'EcuM_ValidateWakeupEvent','',0
	.extern	EcuM_ConfigConsistencyHash
	.extern	EcuM_AL_DriverInitOne
	.extern	EcuM_AL_DriverInitTwo
	.extern	EcuM_AL_DriverInitThree
	.extern	EcuM_AL_DriverInitZero
	.extern	Mcu_Init
	.calls	'EcuM_CheckWakeup','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L12:
	.word	62274
	.half	3
	.word	.L13
	.byte	4
.L11:
	.byte	1
	.byte	'..\\mcal_src\\EcuM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L14
.L36:
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'EcuM_ConfigType_Tag',0,1,140,1,16,40,2
	.byte	'unsigned short int',0,2,7,4
	.word	220
	.byte	5
	.byte	'ConfigurationIdentifier',0,2
	.word	242
	.byte	2,35,0,4
	.word	173
	.byte	5
	.byte	'PreCompileIdentifier',0,4
	.word	280
	.byte	2,35,2,3
	.byte	'Mcu_ConfigType',0,2,189,4,16,28,3
	.byte	'Mcu_ClockCfgType',0,2,236,3,16,80,2
	.byte	'unsigned char',0,1,8,6,8
	.word	359
	.byte	7,7,0,5
	.byte	'K2div',0,8
	.word	376
	.byte	2,35,0,6,32
	.word	173
	.byte	7,7,0,5
	.byte	'K2RampToPllDelayTicks',0,32
	.word	400
	.byte	2,35,8,8,2,247,3,3,4,9
	.byte	'K1div',0,1
	.word	359
	.byte	7,1,2,35,0,9
	.byte	'K3div',0,2
	.word	220
	.byte	7,2,2,35,0,2
	.byte	'unsigned int',0,4,7,9
	.byte	'Ndiv',0,4
	.word	480
	.byte	7,11,2,35,2,9
	.byte	'Pdiv',0,2
	.word	220
	.byte	4,7,2,35,2,9
	.byte	'K2steps',0,1
	.word	359
	.byte	4,3,2,35,3,9
	.byte	'PllMode',0,1
	.word	359
	.byte	1,2,2,35,3,9
	.byte	'Reserved',0,1
	.word	359
	.byte	2,0,2,35,3,0,5
	.byte	'Mcu_ClockDivValues',0,4
	.word	440
	.byte	2,35,40,8,2,132,4,3,4,9
	.byte	'McuErayNDivider',0,1
	.word	359
	.byte	5,3,2,35,0,9
	.byte	'McuErayK2Divider',0,2
	.word	220
	.byte	7,4,2,35,0,9
	.byte	'McuErayK3Divider',0,4
	.word	480
	.byte	7,13,2,35,2,9
	.byte	'McuErayPDivider',0,1
	.word	359
	.byte	4,1,2,35,2,9
	.byte	'Reserved',0,2
	.word	220
	.byte	9,0,2,35,2,0,5
	.byte	'MCU_ErayPllDivValues',0,4
	.word	615
	.byte	2,35,44,5
	.byte	'Ccucon0',0,4
	.word	173
	.byte	2,35,48,5
	.byte	'Ccucon1',0,4
	.word	173
	.byte	2,35,52,5
	.byte	'Ccucon2',0,4
	.word	173
	.byte	2,35,56,5
	.byte	'Ccucon5',0,4
	.word	173
	.byte	2,35,60,5
	.byte	'Ccucon6',0,4
	.word	173
	.byte	2,35,64,5
	.byte	'Ccucon7',0,4
	.word	173
	.byte	2,35,68,5
	.byte	'Ccucon8',0,4
	.word	173
	.byte	2,35,72,5
	.byte	'K2RampToPllDelayConf',0,1
	.word	359
	.byte	2,35,76,0,4
	.word	336
	.byte	10
	.word	932
	.byte	5
	.byte	'ClockCfgPtr',0,4
	.word	937
	.byte	2,35,0,3
	.byte	'Gtm_ConfigType',0,3,192,7,16,12,8,3,230,6,9,64,5
	.byte	'GtmClockEnable',0,4
	.word	173
	.byte	2,35,0,5
	.byte	'GtmCmuClkCnt',0,32
	.word	400
	.byte	2,35,4,5
	.byte	'GtmFxdClkControl',0,4
	.word	173
	.byte	2,35,36,8,3,223,6,9,8,5
	.byte	'CmuEclkNum',0,4
	.word	173
	.byte	2,35,0,5
	.byte	'CmuEclkDen',0,4
	.word	173
	.byte	2,35,4,0,6,24
	.word	1062
	.byte	7,2,0,5
	.byte	'GtmEclk',0,24
	.word	1109
	.byte	2,35,40,0,4
	.word	984
	.byte	10
	.word	1136
	.byte	5
	.byte	'GtmClockSettingPtr',0,4
	.word	1141
	.byte	2,35,0,8,3,249,5,9,36,6,4
	.word	173
	.byte	7,0,0,5
	.byte	'TimInSel',0,4
	.word	1180
	.byte	2,35,0,5
	.byte	'ToutSel',0,32
	.word	400
	.byte	2,35,4,0,4
	.word	1174
	.byte	10
	.word	1225
	.byte	5
	.byte	'GtmPortConfigPtr',0,4
	.word	1230
	.byte	2,35,4,8,3,129,7,9,72,5
	.byte	'GtmModuleSleepEnable',0,1
	.word	359
	.byte	2,35,0,5
	.byte	'GtmGclkNum',0,4
	.word	173
	.byte	2,35,2,5
	.byte	'GtmGclkDen',0,4
	.word	173
	.byte	2,35,6,5
	.byte	'GtmAccessEnable0',0,4
	.word	173
	.byte	2,35,10,5
	.byte	'GtmAccessEnable1',0,4
	.word	173
	.byte	2,35,14,6,2
	.word	220
	.byte	7,0,0,5
	.byte	'GtmTimModuleUsage',0,2
	.word	1389
	.byte	2,35,18,6,1
	.word	359
	.byte	7,0,0,5
	.byte	'GtmTimUsage',0,1
	.word	1425
	.byte	2,35,20,8,3,138,6,11,24,5
	.byte	'TimUsage',0,1
	.word	359
	.byte	2,35,0,5
	.byte	'TimIrqEn',0,1
	.word	359
	.byte	2,35,1,5
	.byte	'TimErrIrqEn',0,1
	.word	359
	.byte	2,35,2,5
	.byte	'TimExtCapSrc',0,1
	.word	359
	.byte	2,35,3,5
	.byte	'TimCtrlValue',0,4
	.word	173
	.byte	2,35,4,8,3,129,6,9,8,5
	.byte	'TimRisingEdgeFilter',0,4
	.word	173
	.byte	2,35,0,5
	.byte	'TimFallingEdgeFilter',0,4
	.word	173
	.byte	2,35,4,0,4
	.word	1562
	.byte	10
	.word	1628
	.byte	5
	.byte	'GtmTimFltPtr',0,4
	.word	1633
	.byte	2,35,8,5
	.byte	'TimCntsValue',0,4
	.word	173
	.byte	2,35,12,5
	.byte	'TimTduValue',0,4
	.word	173
	.byte	2,35,16,5
	.byte	'TimInSrcSel',0,4
	.word	173
	.byte	2,35,20,0,4
	.word	1455
	.byte	10
	.word	1725
	.byte	5
	.byte	'GtmTimConfigPtr',0,4
	.word	1730
	.byte	2,35,24,5
	.byte	'GtmTomTgcUsage',0,1
	.word	1425
	.byte	2,35,28,8,3,189,6,9,12,5
	.byte	'GtmTomIntTrig',0,2
	.word	220
	.byte	2,35,0,5
	.byte	'GtmTomActTb',0,4
	.word	173
	.byte	2,35,2,8,3,177,6,9,16,5
	.byte	'GtmTomUpdateEn',0,2
	.word	220
	.byte	2,35,0,5
	.byte	'GtmTomEndisCtrl',0,2
	.word	220
	.byte	2,35,2,5
	.byte	'GtmTomEndisStat',0,2
	.word	220
	.byte	2,35,4,5
	.byte	'GtmTomOutenCtrl',0,2
	.word	220
	.byte	2,35,6,5
	.byte	'GtmTomOutenStat',0,2
	.word	220
	.byte	2,35,8,5
	.byte	'GtmTomFupd',0,4
	.word	173
	.byte	2,35,10,0,4
	.word	1834
	.byte	10
	.word	1985
	.byte	5
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	1990
	.byte	2,35,8,0,4
	.word	1784
	.byte	10
	.word	2027
	.byte	5
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	2032
	.byte	2,35,32,6,8
	.word	173
	.byte	7,1,0,5
	.byte	'GtmTomModuleUsage',0,8
	.word	2065
	.byte	2,35,36,5
	.byte	'GtmTomUsage',0,4
	.word	1180
	.byte	2,35,44,8,3,211,6,9,12,5
	.byte	'TomUsage',0,1
	.word	359
	.byte	2,35,0,5
	.byte	'GtmTomIrqMode',0,1
	.word	359
	.byte	2,35,1,5
	.byte	'GtmTomControlWord',0,4
	.word	173
	.byte	2,35,2,8,3,199,6,9,12,5
	.byte	'GtmTomIrqEn',0,1
	.word	359
	.byte	2,35,0,5
	.byte	'GtmTomCn0Value',0,2
	.word	220
	.byte	2,35,2,5
	.byte	'GtmTomCm0Value',0,2
	.word	220
	.byte	2,35,4,5
	.byte	'GtmTomCm1Value',0,2
	.word	220
	.byte	2,35,6,5
	.byte	'GtmTomSr0Value',0,2
	.word	220
	.byte	2,35,8,5
	.byte	'GtmTomSr1Value',0,2
	.word	220
	.byte	2,35,10,0,4
	.word	2196
	.byte	10
	.word	2344
	.byte	5
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	2349
	.byte	2,35,8,0,4
	.word	2122
	.byte	10
	.word	2384
	.byte	5
	.byte	'GtmTomConfigPtr',0,4
	.word	2389
	.byte	2,35,48,8,3,154,6,11,40,5
	.byte	'Gtm_TimUsage',0,8
	.word	376
	.byte	2,35,0,6,16
	.word	359
	.byte	7,15,0,6,32
	.word	2447
	.byte	7,1,0,5
	.byte	'Gtm_TomUsage',0,32
	.word	2456
	.byte	2,35,8,0,4
	.word	2419
	.byte	10
	.word	2488
	.byte	5
	.byte	'GtmModUsageConfigPtr',0,4
	.word	2493
	.byte	2,35,52,8,3,240,6,9,4,5
	.byte	'GtmCtrlValue',0,2
	.word	220
	.byte	2,35,0,5
	.byte	'GtmIrqEnable',0,2
	.word	220
	.byte	2,35,2,0,4
	.word	2528
	.byte	10
	.word	2579
	.byte	5
	.byte	'GtmGeneralConfigPtr',0,4
	.word	2584
	.byte	2,35,56,8,3,249,6,9,6,5
	.byte	'TbuChannelCtrl',0,1
	.word	359
	.byte	2,35,0,5
	.byte	'TbuBaseValue',0,4
	.word	173
	.byte	2,35,2,0,4
	.word	2618
	.byte	10
	.word	2671
	.byte	5
	.byte	'GtmTbuConfigPtr',0,4
	.word	2676
	.byte	2,35,60,4
	.word	359
	.byte	10
	.word	2706
	.byte	5
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	2711
	.byte	2,35,64,5
	.byte	'GtmTtcanTriggers',0,2
	.word	1389
	.byte	2,35,68,0,4
	.word	1261
	.byte	10
	.word	2773
	.byte	5
	.byte	'GtmModuleConfigPtr',0,4
	.word	2778
	.byte	2,35,8,0,4
	.word	963
	.byte	10
	.word	2812
	.byte	5
	.byte	'GtmConfigRootPtr',0,4
	.word	2817
	.byte	2,35,4,5
	.byte	'ResetCfg',0,4
	.word	173
	.byte	2,35,8,5
	.byte	'NoOfClockCfg',0,4
	.word	173
	.byte	2,35,12,5
	.byte	'NoOfRamCfg',0,4
	.word	173
	.byte	2,35,16,5
	.byte	'MaxMode',0,4
	.word	173
	.byte	2,35,20,3
	.byte	'Mcu_StandbyModeType',0,2,171,4,16,6,5
	.byte	'PMSWCR0',0,4
	.word	173
	.byte	2,35,0,5
	.byte	'CrcCheckEnable',0,1
	.word	359
	.byte	2,35,4,0,4
	.word	2925
	.byte	10
	.word	2993
	.byte	5
	.byte	'StandbyCfgPtr',0,4
	.word	2998
	.byte	2,35,24,0,4
	.word	315
	.byte	10
	.word	3027
	.byte	5
	.byte	'Mcu_ConfigData',0,4
	.word	3032
	.byte	2,35,8,11
	.byte	'Dio_ConfigType',0,1,149,1,16,1,4
	.word	3061
	.byte	10
	.word	3082
	.byte	5
	.byte	'Dio_ConfigData',0,4
	.word	3087
	.byte	2,35,12,11
	.byte	'Port_ConfigType',0,1,150,1,16,1,4
	.word	3116
	.byte	10
	.word	3138
	.byte	5
	.byte	'Port_ConfigData',0,4
	.word	3143
	.byte	2,35,16,11
	.byte	'Adc_ConfigType',0,1,151,1,16,1,4
	.word	3173
	.byte	10
	.word	3194
	.byte	5
	.byte	'Adc_ConfigData',0,4
	.word	3199
	.byte	2,35,20,11
	.byte	'Uart_ConfigType',0,1,153,1,16,1,4
	.word	3228
	.byte	10
	.word	3250
	.byte	5
	.byte	'Uart_ConfigData',0,4
	.word	3255
	.byte	2,35,24,11
	.byte	'Spi_ConfigType',0,1,154,1,16,1,4
	.word	3285
	.byte	10
	.word	3306
	.byte	5
	.byte	'Spi_ConfigData',0,4
	.word	3311
	.byte	2,35,28,11
	.byte	'Dma_ConfigType',0,1,155,1,16,1,4
	.word	3340
	.byte	10
	.word	3361
	.byte	5
	.byte	'Dma_ConfigData',0,4
	.word	3366
	.byte	2,35,32,4
	.word	359
	.byte	5
	.byte	'LocalConfigData',0,1
	.word	3395
	.byte	2,35,36,0,4
	.word	194
.L43:
	.byte	10
	.word	3426
.L45:
	.byte	12
	.word	173
	.byte	13
	.byte	'EcuM_AL_DriverInitOne',0,4,98,6,1,1,1,1,14
	.byte	'configptr',0,4,98,51
	.word	3431
	.byte	0,13
	.byte	'EcuM_AL_DriverInitTwo',0,4,119,6,1,1,1,1,14
	.byte	'configptr',0,4,119,51
	.word	3431
	.byte	0,13
	.byte	'EcuM_AL_DriverInitThree',0,4,140,1,6,1,1,1,1,14
	.byte	'configptr',0,4,140,1,53
	.word	3431
	.byte	0,15
	.byte	'EcuM_AL_DriverInitZero',0,4,161,1,6,1,1,1,1,13
	.byte	'Mcu_Init',0,2,143,5,13,1,1,1,1,4
	.word	315
	.byte	10
	.word	3642
	.byte	14
	.byte	'ConfigPtr',0,2,143,5,44
	.word	3647
	.byte	0,16
	.byte	'void',0,10
	.word	3672
	.byte	17
	.byte	'__prof_adm',0,5,1,1
	.word	3678
	.byte	18,1,10
	.word	3702
	.byte	17
	.byte	'__codeptr',0,5,1,1
	.word	3704
	.byte	17
	.byte	'uint8',0,6,90,29
	.word	359
	.byte	17
	.byte	'uint16',0,6,92,29
	.word	220
	.byte	17
	.byte	'uint32',0,6,94,29
	.word	173
	.byte	17
	.byte	'boolean',0,6,105,29
	.word	359
	.byte	17
	.byte	'EcuM_ConfigType',0,1,159,1,2
	.word	194
	.byte	4
	.word	173
	.byte	19
	.byte	'EcuM_ConfigConsistencyHash',0,1,186,1,21
	.word	3812
	.byte	1,1,17
	.byte	'EcuM_WakeupSourceType',0,7,45,16
	.word	173
	.byte	17
	.byte	'unsigned_int',0,8,121,22
	.word	480
	.byte	3
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,9,45,16,4,9
	.byte	'EN0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	359
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	359
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	359
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	359
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	359
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	359
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	359
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	359
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	359
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	359
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	359
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	359
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	359
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	359
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	359
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	359
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_ACCEN0_Bits',0,9,79,3
	.word	3906
	.byte	3
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,9,82,16,4,9
	.byte	'reserved_0',0,4
	.word	480
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_SCU_ACCEN1_Bits',0,9,85,3
	.word	4463
	.byte	3
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,9,88,16,4,9
	.byte	'STM0DIS',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'STM1DIS',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'STM2DIS',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,4
	.word	480
	.byte	29,0,2,35,2,0,17
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,9,94,3
	.word	4540
	.byte	3
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,9,97,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	4,4,2,35,0,9
	.byte	'BAUD2DIV',0,1
	.word	359
	.byte	4,0,2,35,0,9
	.byte	'SRIDIV',0,1
	.word	359
	.byte	4,4,2,35,1,9
	.byte	'LPDIV',0,1
	.word	359
	.byte	4,0,2,35,1,9
	.byte	'SPBDIV',0,1
	.word	359
	.byte	4,4,2,35,2,9
	.byte	'FSI2DIV',0,1
	.word	359
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	359
	.byte	2,0,2,35,2,9
	.byte	'FSIDIV',0,1
	.word	359
	.byte	2,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	359
	.byte	2,4,2,35,3,9
	.byte	'CLKSEL',0,1
	.word	359
	.byte	2,2,2,35,3,9
	.byte	'UP',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON0_Bits',0,9,111,3
	.word	4676
	.byte	3
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,9,114,16,4,9
	.byte	'CANDIV',0,1
	.word	359
	.byte	4,4,2,35,0,9
	.byte	'ERAYDIV',0,1
	.word	359
	.byte	4,0,2,35,0,9
	.byte	'STMDIV',0,1
	.word	359
	.byte	4,4,2,35,1,9
	.byte	'GTMDIV',0,1
	.word	359
	.byte	4,0,2,35,1,9
	.byte	'ETHDIV',0,1
	.word	359
	.byte	4,4,2,35,2,9
	.byte	'ASCLINFDIV',0,1
	.word	359
	.byte	4,0,2,35,2,9
	.byte	'ASCLINSDIV',0,1
	.word	359
	.byte	4,4,2,35,3,9
	.byte	'INSEL',0,1
	.word	359
	.byte	2,2,2,35,3,9
	.byte	'UP',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON1_Bits',0,9,126,3
	.word	4958
	.byte	3
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,9,129,1,16,4,9
	.byte	'BBBDIV',0,1
	.word	359
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	480
	.byte	26,2,2,35,2,9
	.byte	'UP',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON2_Bits',0,9,135,1,3
	.word	5196
	.byte	3
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,9,138,1,16,4,9
	.byte	'PLLDIV',0,1
	.word	359
	.byte	6,2,2,35,0,9
	.byte	'PLLSEL',0,1
	.word	359
	.byte	2,0,2,35,0,9
	.byte	'PLLERAYDIV',0,1
	.word	359
	.byte	6,2,2,35,1,9
	.byte	'PLLERAYSEL',0,1
	.word	359
	.byte	2,0,2,35,1,9
	.byte	'SRIDIV',0,1
	.word	359
	.byte	6,2,2,35,2,9
	.byte	'SRISEL',0,1
	.word	359
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	6,2,2,35,3,9
	.byte	'UP',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON3_Bits',0,9,149,1,3
	.word	5324
	.byte	3
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,9,152,1,16,4,9
	.byte	'SPBDIV',0,1
	.word	359
	.byte	6,2,2,35,0,9
	.byte	'SPBSEL',0,1
	.word	359
	.byte	2,0,2,35,0,9
	.byte	'GTMDIV',0,1
	.word	359
	.byte	6,2,2,35,1,9
	.byte	'GTMSEL',0,1
	.word	359
	.byte	2,0,2,35,1,9
	.byte	'STMDIV',0,1
	.word	359
	.byte	6,2,2,35,2,9
	.byte	'STMSEL',0,1
	.word	359
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	6,2,2,35,3,9
	.byte	'UP',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON4_Bits',0,9,163,1,3
	.word	5551
	.byte	3
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,9,166,1,16,4,9
	.byte	'MAXDIV',0,1
	.word	359
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	480
	.byte	26,2,2,35,2,9
	.byte	'UP',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CCUCON5_Bits',0,9,172,1,3
	.word	5770
	.byte	3
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,9,175,1,16,4,9
	.byte	'CPU0DIV',0,1
	.word	359
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	480
	.byte	26,0,2,35,2,0,17
	.byte	'Ifx_SCU_CCUCON6_Bits',0,9,179,1,3
	.word	5898
	.byte	3
	.byte	'_Ifx_SCU_CHIPID_Bits',0,9,182,1,16,4,9
	.byte	'CHREV',0,1
	.word	359
	.byte	6,2,2,35,0,9
	.byte	'CHTEC',0,1
	.word	359
	.byte	2,0,2,35,0,9
	.byte	'CHID',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'EEA',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'UCODE',0,1
	.word	359
	.byte	7,0,2,35,2,9
	.byte	'FSIZE',0,1
	.word	359
	.byte	4,4,2,35,3,9
	.byte	'SP',0,1
	.word	359
	.byte	2,2,2,35,3,9
	.byte	'SEC',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_CHIPID_Bits',0,9,193,1,3
	.word	5998
	.byte	3
	.byte	'_Ifx_SCU_DTSCON_Bits',0,9,196,1,16,4,9
	.byte	'PWD',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'START',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	2,4,2,35,0,9
	.byte	'CAL',0,4
	.word	480
	.byte	22,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	359
	.byte	5,1,2,35,3,9
	.byte	'SLCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_DTSCON_Bits',0,9,204,1,3
	.word	6206
	.byte	3
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,9,207,1,16,4,9
	.byte	'LOWER',0,2
	.word	220
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	359
	.byte	5,1,2,35,1,9
	.byte	'LLU',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'UPPER',0,2
	.word	220
	.byte	10,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	359
	.byte	4,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'UOF',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_DTSLIM_Bits',0,9,216,1,3
	.word	6371
	.byte	3
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,9,219,1,16,4,9
	.byte	'RESULT',0,2
	.word	220
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	359
	.byte	4,2,2,35,1,9
	.byte	'RDY',0,1
	.word	359
	.byte	1,1,2,35,1,9
	.byte	'BUSY',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,9,226,1,3
	.word	6554
	.byte	3
	.byte	'_Ifx_SCU_EICR_Bits',0,9,229,1,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	4,4,2,35,0,9
	.byte	'EXIS0',0,1
	.word	359
	.byte	3,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'FEN0',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'REN0',0,1
	.word	359
	.byte	1,6,2,35,1,9
	.byte	'LDEN0',0,1
	.word	359
	.byte	1,5,2,35,1,9
	.byte	'EIEN0',0,1
	.word	359
	.byte	1,4,2,35,1,9
	.byte	'INP0',0,1
	.word	359
	.byte	3,1,2,35,1,9
	.byte	'reserved_15',0,4
	.word	480
	.byte	5,12,2,35,2,9
	.byte	'EXIS1',0,1
	.word	359
	.byte	3,1,2,35,2,9
	.byte	'reserved_23',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'FEN1',0,1
	.word	359
	.byte	1,7,2,35,3,9
	.byte	'REN1',0,1
	.word	359
	.byte	1,6,2,35,3,9
	.byte	'LDEN1',0,1
	.word	359
	.byte	1,5,2,35,3,9
	.byte	'EIEN1',0,1
	.word	359
	.byte	1,4,2,35,3,9
	.byte	'INP1',0,1
	.word	359
	.byte	3,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EICR_Bits',0,9,248,1,3
	.word	6708
	.byte	3
	.byte	'_Ifx_SCU_EIFR_Bits',0,9,251,1,16,4,9
	.byte	'INTF0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'INTF1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'INTF2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'INTF3',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'INTF4',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'INTF5',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'INTF6',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'INTF7',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	480
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_SCU_EIFR_Bits',0,9,134,2,3
	.word	7072
	.byte	3
	.byte	'_Ifx_SCU_EMSR_Bits',0,9,137,2,16,4,9
	.byte	'POL',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'MODE',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'ENON',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'PSEL',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,2
	.word	220
	.byte	12,0,2,35,0,9
	.byte	'EMSF',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'SEMSF',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	359
	.byte	6,0,2,35,2,9
	.byte	'EMSFM',0,1
	.word	359
	.byte	2,6,2,35,3,9
	.byte	'SEMSFM',0,1
	.word	359
	.byte	2,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	359
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_SCU_EMSR_Bits',0,9,150,2,3
	.word	7283
	.byte	3
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,9,153,2,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	7,1,2,35,0,9
	.byte	'EDCON',0,2
	.word	220
	.byte	2,7,2,35,0,9
	.byte	'reserved_9',0,4
	.word	480
	.byte	23,0,2,35,2,0,17
	.byte	'Ifx_SCU_ESRCFG_Bits',0,9,158,2,3
	.word	7535
	.byte	3
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,9,161,2,16,4,9
	.byte	'ARI',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'ARC',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	480
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_ESROCFG_Bits',0,9,166,2,3
	.word	7653
	.byte	3
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,9,169,2,16,4,9
	.byte	'reserved_0',0,4
	.word	480
	.byte	28,4,2,35,2,9
	.byte	'EVR13OFF',0,1
	.word	359
	.byte	1,3,2,35,3,9
	.byte	'BPEVR13OFF',0,1
	.word	359
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVR13CON_Bits',0,9,176,2,3
	.word	7764
	.byte	3
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,9,179,2,16,4,9
	.byte	'ADC13V',0,1
	.word	359
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'ADCSWDV',0,1
	.word	359
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	7,1,2,35,3,9
	.byte	'VAL',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,9,186,2,3
	.word	7927
	.byte	3
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,9,189,2,16,4,9
	.byte	'EVR13OVMOD',0,1
	.word	359
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	2,4,2,35,0,9
	.byte	'EVR13UVMOD',0,1
	.word	359
	.byte	2,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	220
	.byte	10,0,2,35,0,9
	.byte	'SWDOVMOD',0,1
	.word	359
	.byte	2,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	359
	.byte	2,4,2,35,2,9
	.byte	'SWDUVMOD',0,1
	.word	359
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,2
	.word	220
	.byte	8,2,2,35,2,9
	.byte	'SLCK',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,9,201,2,3
	.word	8089
	.byte	3
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,9,204,2,16,4,9
	.byte	'EVR13OVVAL',0,1
	.word	359
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'SWDOVVAL',0,1
	.word	359
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	6,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVROVMON_Bits',0,9,212,2,3
	.word	8367
	.byte	3
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,9,215,2,16,4,9
	.byte	'reserved_0',0,4
	.word	480
	.byte	28,4,2,35,2,9
	.byte	'RSTSWDOFF',0,1
	.word	359
	.byte	1,3,2,35,3,9
	.byte	'BPRSTSWDOFF',0,1
	.word	359
	.byte	1,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,9,222,2,3
	.word	8546
	.byte	3
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,9,225,2,16,4,9
	.byte	'SD33P',0,1
	.word	359
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	359
	.byte	4,0,2,35,0,9
	.byte	'SD33I',0,1
	.word	359
	.byte	4,4,2,35,1,9
	.byte	'reserved_12',0,4
	.word	480
	.byte	19,1,2,35,2,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,9,232,2,3
	.word	8706
	.byte	3
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,9,235,2,16,4,9
	.byte	'SDFREQSPRD',0,1
	.word	359
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	359
	.byte	4,0,2,35,0,9
	.byte	'TON',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'TOFF',0,1
	.word	359
	.byte	8,0,2,35,2,9
	.byte	'SDSTEP',0,1
	.word	359
	.byte	4,4,2,35,3,9
	.byte	'SYNCDIV',0,1
	.word	359
	.byte	3,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,9,244,2,3
	.word	8867
	.byte	3
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,9,247,2,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	8,0,2,35,0,9
	.byte	'STBS',0,1
	.word	359
	.byte	2,6,2,35,1,9
	.byte	'STSP',0,1
	.word	359
	.byte	2,4,2,35,1,9
	.byte	'NS',0,1
	.word	359
	.byte	2,2,2,35,1,9
	.byte	'OL',0,1
	.word	359
	.byte	1,1,2,35,1,9
	.byte	'PIAD',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'ADCMODE',0,1
	.word	359
	.byte	4,4,2,35,2,9
	.byte	'ADCLPF',0,1
	.word	359
	.byte	2,2,2,35,2,9
	.byte	'ADCLSB',0,1
	.word	359
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'SDLUT',0,1
	.word	359
	.byte	6,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,9,134,3,3
	.word	9059
	.byte	3
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,9,137,3,16,4,9
	.byte	'SDOLCON',0,1
	.word	359
	.byte	7,1,2,35,0,9
	.byte	'MODSEL',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'MODLOW',0,1
	.word	359
	.byte	7,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'SDVOKLVL',0,1
	.word	359
	.byte	6,2,2,35,2,9
	.byte	'MODMAN',0,1
	.word	359
	.byte	2,0,2,35,2,9
	.byte	'MODHIGH',0,1
	.word	359
	.byte	7,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,9,147,3,3
	.word	9355
	.byte	3
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,9,150,3,16,4,9
	.byte	'EVR13',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'OV13',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	2,4,2,35,0,9
	.byte	'OVSWD',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'UV13',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'UVSWD',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	2,6,2,35,1,9
	.byte	'BGPROK',0,1
	.word	359
	.byte	1,5,2,35,1,9
	.byte	'reserved_11',0,1
	.word	359
	.byte	1,4,2,35,1,9
	.byte	'SCMOD',0,1
	.word	359
	.byte	2,2,2,35,1,9
	.byte	'reserved_14',0,4
	.word	480
	.byte	18,0,2,35,2,0,17
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,9,164,3,3
	.word	9570
	.byte	3
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,9,167,3,16,4,9
	.byte	'EVR13UVVAL',0,1
	.word	359
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'SWDUVVAL',0,1
	.word	359
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	6,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,9,175,3,3
	.word	9859
	.byte	3
	.byte	'_Ifx_SCU_EXTCON_Bits',0,9,178,3,16,4,9
	.byte	'EN0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'SEL0',0,1
	.word	359
	.byte	4,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	220
	.byte	10,0,2,35,0,9
	.byte	'EN1',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'NSEL',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'SEL1',0,1
	.word	359
	.byte	4,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	359
	.byte	2,0,2,35,2,9
	.byte	'DIV1',0,1
	.word	359
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_SCU_EXTCON_Bits',0,9,189,3,3
	.word	10038
	.byte	3
	.byte	'_Ifx_SCU_FDR_Bits',0,9,192,3,16,4,9
	.byte	'STEP',0,2
	.word	220
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	359
	.byte	4,2,2,35,1,9
	.byte	'DM',0,1
	.word	359
	.byte	2,0,2,35,1,9
	.byte	'RESULT',0,2
	.word	220
	.byte	10,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	359
	.byte	5,1,2,35,3,9
	.byte	'DISCLK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_FDR_Bits',0,9,200,3,3
	.word	10256
	.byte	3
	.byte	'_Ifx_SCU_FMR_Bits',0,9,203,3,16,4,9
	.byte	'FS0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'FS1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'FS2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'FS3',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'FS4',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'FS5',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'FS6',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'FS7',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'FC0',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'FC1',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'FC2',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'FC3',0,1
	.word	359
	.byte	1,4,2,35,2,9
	.byte	'FC4',0,1
	.word	359
	.byte	1,3,2,35,2,9
	.byte	'FC5',0,1
	.word	359
	.byte	1,2,2,35,2,9
	.byte	'FC6',0,1
	.word	359
	.byte	1,1,2,35,2,9
	.byte	'FC7',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_SCU_FMR_Bits',0,9,223,3,3
	.word	10419
	.byte	3
	.byte	'_Ifx_SCU_ID_Bits',0,9,226,3,16,4,9
	.byte	'MODREV',0,1
	.word	359
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_ID_Bits',0,9,231,3,3
	.word	10755
	.byte	3
	.byte	'_Ifx_SCU_IGCR_Bits',0,9,234,3,16,4,9
	.byte	'IPEN00',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'IPEN01',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'IPEN02',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'IPEN03',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'IPEN04',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'IPEN05',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'IPEN06',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'IPEN07',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	5,3,2,35,1,9
	.byte	'GEEN0',0,1
	.word	359
	.byte	1,2,2,35,1,9
	.byte	'IGP0',0,1
	.word	359
	.byte	2,0,2,35,1,9
	.byte	'IPEN10',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'IPEN11',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'IPEN12',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'IPEN13',0,1
	.word	359
	.byte	1,4,2,35,2,9
	.byte	'IPEN14',0,1
	.word	359
	.byte	1,3,2,35,2,9
	.byte	'IPEN15',0,1
	.word	359
	.byte	1,2,2,35,2,9
	.byte	'IPEN16',0,1
	.word	359
	.byte	1,1,2,35,2,9
	.byte	'IPEN17',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	5,3,2,35,3,9
	.byte	'GEEN1',0,1
	.word	359
	.byte	1,2,2,35,3,9
	.byte	'IGP1',0,1
	.word	359
	.byte	2,0,2,35,3,0,17
	.byte	'Ifx_SCU_IGCR_Bits',0,9,130,4,3
	.word	10862
	.byte	3
	.byte	'_Ifx_SCU_IN_Bits',0,9,133,4,16,4,9
	.byte	'P0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	480
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_IN_Bits',0,9,138,4,3
	.word	11314
	.byte	3
	.byte	'_Ifx_SCU_IOCR_Bits',0,9,141,4,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	4,4,2,35,0,9
	.byte	'PC0',0,1
	.word	359
	.byte	4,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	4,4,2,35,1,9
	.byte	'PC1',0,1
	.word	359
	.byte	4,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_IOCR_Bits',0,9,148,4,3
	.word	11413
	.byte	3
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,9,151,4,16,4,9
	.byte	'LBISTREQ',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'LBISTREQP',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'PATTERNS',0,2
	.word	220
	.byte	14,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,9,157,4,3
	.word	11563
	.byte	3
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,9,160,4,16,4,9
	.byte	'SEED',0,4
	.word	480
	.byte	23,9,2,35,2,9
	.byte	'reserved_23',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'SPLITSH',0,1
	.word	359
	.byte	3,5,2,35,3,9
	.byte	'BODY',0,1
	.word	359
	.byte	1,4,2,35,3,9
	.byte	'LBISTFREQU',0,1
	.word	359
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,9,167,4,3
	.word	11712
	.byte	3
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,9,170,4,16,4,9
	.byte	'SIGNATURE',0,4
	.word	480
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	7,1,2,35,3,9
	.byte	'LBISTDONE',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,9,175,4,3
	.word	11873
	.byte	3
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,9,178,4,16,4,9
	.byte	'reserved_0',0,2
	.word	220
	.byte	16,0,2,35,0,9
	.byte	'LS',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,2
	.word	220
	.byte	14,1,2,35,2,9
	.byte	'LSEN',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_LCLCON0_Bits',0,9,184,4,3
	.word	12003
	.byte	3
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,9,187,4,16,4,9
	.byte	'LCLT0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'LCLT1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	480
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_LCLTEST_Bits',0,9,192,4,3
	.word	12137
	.byte	3
	.byte	'_Ifx_SCU_MANID_Bits',0,9,195,4,16,4,9
	.byte	'DEPT',0,1
	.word	359
	.byte	5,3,2,35,0,9
	.byte	'MANUF',0,2
	.word	220
	.byte	11,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_MANID_Bits',0,9,200,4,3
	.word	12252
	.byte	3
	.byte	'_Ifx_SCU_OMR_Bits',0,9,203,4,16,4,9
	.byte	'PS0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'PS1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,2
	.word	220
	.byte	14,0,2,35,0,9
	.byte	'PCL0',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'PCL1',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	220
	.byte	14,0,2,35,2,0,17
	.byte	'Ifx_SCU_OMR_Bits',0,9,211,4,3
	.word	12363
	.byte	3
	.byte	'_Ifx_SCU_OSCCON_Bits',0,9,214,4,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'PLLLV',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'OSCRES',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'GAINSEL',0,1
	.word	359
	.byte	2,3,2,35,0,9
	.byte	'MODE',0,1
	.word	359
	.byte	2,1,2,35,0,9
	.byte	'SHBY',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'PLLHV',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,1
	.word	359
	.byte	1,6,2,35,1,9
	.byte	'X1D',0,1
	.word	359
	.byte	1,5,2,35,1,9
	.byte	'X1DEN',0,1
	.word	359
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	359
	.byte	4,0,2,35,1,9
	.byte	'OSCVAL',0,1
	.word	359
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	359
	.byte	2,1,2,35,2,9
	.byte	'APREN',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	8,0,2,35,3,0,17
	.byte	'Ifx_SCU_OSCCON_Bits',0,9,231,4,3
	.word	12521
	.byte	3
	.byte	'_Ifx_SCU_OUT_Bits',0,9,234,4,16,4,9
	.byte	'P0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	480
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_OUT_Bits',0,9,239,4,3
	.word	12861
	.byte	3
	.byte	'_Ifx_SCU_OVCCON_Bits',0,9,242,4,16,4,9
	.byte	'CSEL0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'CSEL1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'CSEL2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,2
	.word	220
	.byte	13,0,2,35,0,9
	.byte	'OVSTRT',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'OVSTP',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'DCINVAL',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	359
	.byte	5,0,2,35,2,9
	.byte	'OVCONF',0,1
	.word	359
	.byte	1,7,2,35,3,9
	.byte	'POVCONF',0,1
	.word	359
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	359
	.byte	6,0,2,35,3,0,17
	.byte	'Ifx_SCU_OVCCON_Bits',0,9,255,4,3
	.word	12962
	.byte	3
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,9,130,5,16,4,9
	.byte	'OVEN0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'OVEN1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'OVEN2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,4
	.word	480
	.byte	29,0,2,35,2,0,17
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,9,136,5,3
	.word	13229
	.byte	3
	.byte	'_Ifx_SCU_PDISC_Bits',0,9,139,5,16,4,9
	.byte	'PDIS0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'PDIS1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	480
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_PDISC_Bits',0,9,144,5,3
	.word	13365
	.byte	3
	.byte	'_Ifx_SCU_PDR_Bits',0,9,147,5,16,4,9
	.byte	'PD0',0,1
	.word	359
	.byte	3,5,2,35,0,9
	.byte	'PL0',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'PD1',0,1
	.word	359
	.byte	3,1,2,35,0,9
	.byte	'PL1',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	480
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_SCU_PDR_Bits',0,9,154,5,3
	.word	13476
	.byte	3
	.byte	'_Ifx_SCU_PDRR_Bits',0,9,157,5,16,4,9
	.byte	'PDR0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'PDR1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'PDR2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'PDR3',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'PDR4',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'PDR5',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'PDR6',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'PDR7',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	480
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_SCU_PDRR_Bits',0,9,168,5,3
	.word	13609
	.byte	3
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,9,171,5,16,4,9
	.byte	'VCOBYP',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'VCOPWD',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'MODEN',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'SETFINDIS',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'CLRFINDIS',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'OSCDISCDIS',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	220
	.byte	2,7,2,35,0,9
	.byte	'NDIV',0,1
	.word	359
	.byte	7,0,2,35,1,9
	.byte	'PLLPWD',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'RESLD',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	359
	.byte	5,0,2,35,2,9
	.byte	'PDIV',0,1
	.word	359
	.byte	4,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	359
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_SCU_PLLCON0_Bits',0,9,188,5,3
	.word	13812
	.byte	3
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,9,191,5,16,4,9
	.byte	'K2DIV',0,1
	.word	359
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'K3DIV',0,1
	.word	359
	.byte	7,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'K1DIV',0,1
	.word	359
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	220
	.byte	9,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLCON1_Bits',0,9,199,5,3
	.word	14168
	.byte	3
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,9,202,5,16,4,9
	.byte	'MODCFG',0,2
	.word	220
	.byte	16,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLCON2_Bits',0,9,206,5,3
	.word	14346
	.byte	3
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,9,209,5,16,4,9
	.byte	'VCOBYP',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'VCOPWD',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	2,4,2,35,0,9
	.byte	'SETFINDIS',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'CLRFINDIS',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'OSCDISCDIS',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	220
	.byte	2,7,2,35,0,9
	.byte	'NDIV',0,1
	.word	359
	.byte	5,2,2,35,1,9
	.byte	'reserved_14',0,1
	.word	359
	.byte	2,0,2,35,1,9
	.byte	'PLLPWD',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'RESLD',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	359
	.byte	5,0,2,35,2,9
	.byte	'PDIV',0,1
	.word	359
	.byte	4,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	359
	.byte	4,0,2,35,3,0,17
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,9,226,5,3
	.word	14446
	.byte	3
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,9,229,5,16,4,9
	.byte	'K2DIV',0,1
	.word	359
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'K3DIV',0,1
	.word	359
	.byte	4,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	359
	.byte	4,0,2,35,1,9
	.byte	'K1DIV',0,1
	.word	359
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	220
	.byte	9,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,9,237,5,3
	.word	14816
	.byte	3
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,9,240,5,16,4,9
	.byte	'VCOBYST',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'PWDSTAT',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'VCOLOCK',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'FINDIS',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'K1RDY',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'K2RDY',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	480
	.byte	26,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,9,249,5,3
	.word	15002
	.byte	3
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,9,252,5,16,4,9
	.byte	'VCOBYST',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'VCOLOCK',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'FINDIS',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'K1RDY',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'K2RDY',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'MODRUN',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	480
	.byte	24,0,2,35,2,0,17
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,9,135,6,3
	.word	15200
	.byte	3
	.byte	'_Ifx_SCU_PMCSR_Bits',0,9,138,6,16,4,9
	.byte	'REQSLP',0,1
	.word	359
	.byte	2,6,2,35,0,9
	.byte	'SMUSLP',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	359
	.byte	5,0,2,35,0,9
	.byte	'PMST',0,1
	.word	359
	.byte	3,5,2,35,1,9
	.byte	'reserved_11',0,4
	.word	480
	.byte	21,0,2,35,2,0,17
	.byte	'Ifx_SCU_PMCSR_Bits',0,9,145,6,3
	.word	15433
	.byte	3
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,9,148,6,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'ESR1WKEN',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'PINAWKEN',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'PINBWKEN',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'ESR0DFEN',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'ESR0EDCON',0,1
	.word	359
	.byte	2,1,2,35,0,9
	.byte	'ESR1DFEN',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'ESR1EDCON',0,1
	.word	359
	.byte	2,6,2,35,1,9
	.byte	'PINADFEN',0,1
	.word	359
	.byte	1,5,2,35,1,9
	.byte	'PINAEDCON',0,1
	.word	359
	.byte	2,3,2,35,1,9
	.byte	'PINBDFEN',0,1
	.word	359
	.byte	1,2,2,35,1,9
	.byte	'PINBEDCON',0,1
	.word	359
	.byte	2,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'STBYRAMSEL',0,1
	.word	359
	.byte	2,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	359
	.byte	1,4,2,35,2,9
	.byte	'WUTWKEN',0,1
	.word	359
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	359
	.byte	2,1,2,35,2,9
	.byte	'PORSTDF',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	1,7,2,35,3,9
	.byte	'DCDCSYNC',0,1
	.word	359
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	359
	.byte	3,3,2,35,3,9
	.byte	'ESR0TRIST',0,1
	.word	359
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,9,174,6,3
	.word	15585
	.byte	3
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,9,177,6,16,4,9
	.byte	'reserved_0',0,2
	.word	220
	.byte	12,4,2,35,0,9
	.byte	'IRADIS',0,1
	.word	359
	.byte	1,3,2,35,1,9
	.byte	'reserved_13',0,4
	.word	480
	.byte	14,5,2,35,2,9
	.byte	'STBYEVEN',0,1
	.word	359
	.byte	1,4,2,35,3,9
	.byte	'STBYEV',0,1
	.word	359
	.byte	3,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,9,185,6,3
	.word	16144
	.byte	3
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,9,188,6,16,4,9
	.byte	'WUTREL',0,4
	.word	480
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	4,4,2,35,3,9
	.byte	'WUTDIV',0,1
	.word	359
	.byte	1,3,2,35,3,9
	.byte	'WUTEN',0,1
	.word	359
	.byte	1,2,2,35,3,9
	.byte	'WUTMODE',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,9,196,6,3
	.word	16327
	.byte	3
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,9,199,6,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	2,6,2,35,0,9
	.byte	'ESR1WKP',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'ESR1OVRUN',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'PINAWKP',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'PINAOVRUN',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'PINBWKP',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'PINBOVRUN',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'PORSTDF',0,1
	.word	359
	.byte	1,6,2,35,1,9
	.byte	'HWCFGEVR',0,1
	.word	359
	.byte	3,3,2,35,1,9
	.byte	'STBYRAM',0,1
	.word	359
	.byte	2,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'WUTWKP',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'WUTOVRUN',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'WUTWKEN',0,1
	.word	359
	.byte	1,4,2,35,2,9
	.byte	'ESR1WKEN',0,1
	.word	359
	.byte	1,3,2,35,2,9
	.byte	'PINAWKEN',0,1
	.word	359
	.byte	1,2,2,35,2,9
	.byte	'PINBWKEN',0,1
	.word	359
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	220
	.byte	4,5,2,35,2,9
	.byte	'ESR0TRIST',0,1
	.word	359
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	359
	.byte	1,3,2,35,3,9
	.byte	'WUTEN',0,1
	.word	359
	.byte	1,2,2,35,3,9
	.byte	'WUTMODE',0,1
	.word	359
	.byte	1,1,2,35,3,9
	.byte	'WUTRUN',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,9,226,6,3
	.word	16496
	.byte	3
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,9,229,6,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	2,6,2,35,0,9
	.byte	'ESR1WKPCLR',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'ESR1OVRUNCLR',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'PINAWKPCLR',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'PINAOVRUNCLR',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'PINBWKPCLR',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'PINBOVRUNCLR',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'WUTWKPCLR',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'WUTOVRUNCLR',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	220
	.byte	14,0,2,35,2,0,17
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,9,242,6,3
	.word	17063
	.byte	3
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,9,245,6,16,4,9
	.byte	'WUTCNT',0,4
	.word	480
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	359
	.byte	7,1,2,35,3,9
	.byte	'VAL',0,1
	.word	359
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,9,250,6,3
	.word	17379
	.byte	3
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,9,253,6,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'CLRC',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,2
	.word	220
	.byte	10,4,2,35,0,9
	.byte	'CSS0',0,1
	.word	359
	.byte	1,3,2,35,1,9
	.byte	'CSS1',0,1
	.word	359
	.byte	1,2,2,35,1,9
	.byte	'CSS2',0,1
	.word	359
	.byte	1,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'USRINFO',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_RSTCON2_Bits',0,9,135,7,3
	.word	17498
	.byte	3
	.byte	'_Ifx_SCU_RSTCON_Bits',0,9,138,7,16,4,9
	.byte	'ESR0',0,1
	.word	359
	.byte	2,6,2,35,0,9
	.byte	'ESR1',0,1
	.word	359
	.byte	2,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	359
	.byte	2,2,2,35,0,9
	.byte	'SMU',0,1
	.word	359
	.byte	2,0,2,35,0,9
	.byte	'SW',0,1
	.word	359
	.byte	2,6,2,35,1,9
	.byte	'STM0',0,1
	.word	359
	.byte	2,4,2,35,1,9
	.byte	'STM1',0,1
	.word	359
	.byte	2,2,2,35,1,9
	.byte	'STM2',0,1
	.word	359
	.byte	2,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_RSTCON_Bits',0,9,149,7,3
	.word	17707
	.byte	3
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,9,152,7,16,4,9
	.byte	'ESR0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'ESR1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'SMU',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'SW',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'STM0',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'STM1',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'STM2',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	359
	.byte	8,0,2,35,1,9
	.byte	'PORST',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'CB0',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'CB1',0,1
	.word	359
	.byte	1,4,2,35,2,9
	.byte	'CB3',0,1
	.word	359
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	359
	.byte	2,1,2,35,2,9
	.byte	'EVR13',0,1
	.word	359
	.byte	1,0,2,35,2,9
	.byte	'EVR33',0,1
	.word	359
	.byte	1,7,2,35,3,9
	.byte	'SWD',0,1
	.word	359
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	359
	.byte	2,4,2,35,3,9
	.byte	'STBYR',0,1
	.word	359
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	359
	.byte	3,0,2,35,3,0,17
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,9,175,7,3
	.word	17918
	.byte	3
	.byte	'_Ifx_SCU_SAFECON_Bits',0,9,178,7,16,4,9
	.byte	'HBT',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	480
	.byte	31,0,2,35,2,0,17
	.byte	'Ifx_SCU_SAFECON_Bits',0,9,182,7,3
	.word	18350
	.byte	3
	.byte	'_Ifx_SCU_STSTAT_Bits',0,9,185,7,16,4,9
	.byte	'HWCFG',0,1
	.word	359
	.byte	8,0,2,35,0,9
	.byte	'FTM',0,1
	.word	359
	.byte	7,1,2,35,1,9
	.byte	'MODE',0,1
	.word	359
	.byte	1,0,2,35,1,9
	.byte	'FCBAE',0,1
	.word	359
	.byte	1,7,2,35,2,9
	.byte	'LUDIS',0,1
	.word	359
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	359
	.byte	1,5,2,35,2,9
	.byte	'TRSTL',0,1
	.word	359
	.byte	1,4,2,35,2,9
	.byte	'SPDEN',0,1
	.word	359
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	359
	.byte	3,0,2,35,2,9
	.byte	'RAMINT',0,1
	.word	359
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	359
	.byte	7,0,2,35,3,0,17
	.byte	'Ifx_SCU_STSTAT_Bits',0,9,198,7,3
	.word	18446
	.byte	3
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,9,201,7,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'SWRSTREQ',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	480
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,9,206,7,3
	.word	18706
	.byte	3
	.byte	'_Ifx_SCU_SYSCON_Bits',0,9,209,7,16,4,9
	.byte	'CCTRIG0',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'RAMINTM',0,1
	.word	359
	.byte	2,4,2,35,0,9
	.byte	'SETLUDIS',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	359
	.byte	3,0,2,35,0,9
	.byte	'DATM',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,4
	.word	480
	.byte	23,0,2,35,2,0,17
	.byte	'Ifx_SCU_SYSCON_Bits',0,9,218,7,3
	.word	18831
	.byte	3
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,9,221,7,16,4,9
	.byte	'ESR0T',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	480
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,9,228,7,3
	.word	19028
	.byte	3
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,9,231,7,16,4,9
	.byte	'ESR0T',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	480
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,9,238,7,3
	.word	19181
	.byte	3
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,9,241,7,16,4,9
	.byte	'ESR0T',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	480
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_SCU_TRAPSET_Bits',0,9,248,7,3
	.word	19334
	.byte	3
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,9,251,7,16,4,9
	.byte	'ESR0T',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	480
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,9,130,8,3
	.word	19487
	.byte	3
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,9,133,8,16,4,2
	.byte	'unsigned int',0,4,7,9
	.byte	'ENDINIT',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'LCK',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'PW',0,4
	.word	19674
	.byte	14,16,2,35,0,9
	.byte	'REL',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,9,139,8,3
	.word	19642
	.byte	3
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,9,142,8,16,4,9
	.byte	'reserved_0',0,1
	.word	359
	.byte	2,6,2,35,0,9
	.byte	'IR0',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'DR',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'IR1',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'UR',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'PAR',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'TCR',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'TCTR',0,1
	.word	359
	.byte	7,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,9,154,8,3
	.word	19788
	.byte	3
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,9,157,8,16,4,9
	.byte	'AE',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'OE',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'IS0',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'DS',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'TO',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'IS1',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'US',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'PAS',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'TCS',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'TCT',0,1
	.word	359
	.byte	7,0,2,35,1,9
	.byte	'TIM',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,9,170,8,3
	.word	20026
	.byte	3
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,9,173,8,16,4,9
	.byte	'ENDINIT',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'LCK',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'PW',0,4
	.word	19674
	.byte	14,16,2,35,0,9
	.byte	'REL',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,9,179,8,3
	.word	20249
	.byte	3
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,9,182,8,16,4,9
	.byte	'CLRIRF',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'IR0',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'DR',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'IR1',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'UR',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'PAR',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'TCR',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'TCTR',0,1
	.word	359
	.byte	7,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,9,195,8,3
	.word	20375
	.byte	3
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,9,198,8,16,4,9
	.byte	'AE',0,1
	.word	359
	.byte	1,7,2,35,0,9
	.byte	'OE',0,1
	.word	359
	.byte	1,6,2,35,0,9
	.byte	'IS0',0,1
	.word	359
	.byte	1,5,2,35,0,9
	.byte	'DS',0,1
	.word	359
	.byte	1,4,2,35,0,9
	.byte	'TO',0,1
	.word	359
	.byte	1,3,2,35,0,9
	.byte	'IS1',0,1
	.word	359
	.byte	1,2,2,35,0,9
	.byte	'US',0,1
	.word	359
	.byte	1,1,2,35,0,9
	.byte	'PAS',0,1
	.word	359
	.byte	1,0,2,35,0,9
	.byte	'TCS',0,1
	.word	359
	.byte	1,7,2,35,1,9
	.byte	'TCT',0,1
	.word	359
	.byte	7,0,2,35,1,9
	.byte	'TIM',0,2
	.word	220
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,9,211,8,3
	.word	20627
	.byte	20,9,219,8,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,2
	.byte	'int',0,4,5,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	3906
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ACCEN0',0,9,224,8,3
	.word	20846
	.byte	20,9,227,8,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	4463
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ACCEN1',0,9,232,8,3
	.word	20917
	.byte	20,9,235,8,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	4540
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ARSTDIS',0,9,240,8,3
	.word	20981
	.byte	20,9,243,8,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	4676
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON0',0,9,248,8,3
	.word	21046
	.byte	20,9,251,8,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	4958
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON1',0,9,128,9,3
	.word	21111
	.byte	20,9,131,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	5196
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON2',0,9,136,9,3
	.word	21176
	.byte	20,9,139,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	5324
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON3',0,9,144,9,3
	.word	21241
	.byte	20,9,147,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	5551
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON4',0,9,152,9,3
	.word	21306
	.byte	20,9,155,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	5770
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON5',0,9,160,9,3
	.word	21371
	.byte	20,9,163,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	5898
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CCUCON6',0,9,168,9,3
	.word	21436
	.byte	20,9,171,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	5998
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_CHIPID',0,9,176,9,3
	.word	21501
	.byte	20,9,179,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	6206
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_DTSCON',0,9,184,9,3
	.word	21565
	.byte	20,9,187,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	6371
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_DTSLIM',0,9,192,9,3
	.word	21629
	.byte	20,9,195,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	6554
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_DTSSTAT',0,9,200,9,3
	.word	21693
	.byte	20,9,203,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	6708
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EICR',0,9,208,9,3
	.word	21758
	.byte	20,9,211,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	7072
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EIFR',0,9,216,9,3
	.word	21820
	.byte	20,9,219,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	7283
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EMSR',0,9,224,9,3
	.word	21882
	.byte	20,9,227,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	7535
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ESRCFG',0,9,232,9,3
	.word	21944
	.byte	20,9,235,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	7653
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ESROCFG',0,9,240,9,3
	.word	22008
	.byte	20,9,243,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	7764
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVR13CON',0,9,248,9,3
	.word	22073
	.byte	20,9,251,9,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	7927
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRADCSTAT',0,9,128,10,3
	.word	22139
	.byte	20,9,131,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	8089
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRMONCTRL',0,9,136,10,3
	.word	22207
	.byte	20,9,139,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	8367
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVROVMON',0,9,144,10,3
	.word	22275
	.byte	20,9,147,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	8546
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRRSTCON',0,9,152,10,3
	.word	22341
	.byte	20,9,155,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	8706
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,9,160,10,3
	.word	22408
	.byte	20,9,163,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	8867
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSDCTRL1',0,9,168,10,3
	.word	22477
	.byte	20,9,171,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	9059
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSDCTRL2',0,9,176,10,3
	.word	22545
	.byte	20,9,179,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	9355
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSDCTRL3',0,9,184,10,3
	.word	22613
	.byte	20,9,187,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	9570
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRSTAT',0,9,192,10,3
	.word	22681
	.byte	20,9,195,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	9859
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EVRUVMON',0,9,200,10,3
	.word	22746
	.byte	20,9,203,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	10038
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_EXTCON',0,9,208,10,3
	.word	22812
	.byte	20,9,211,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	10256
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_FDR',0,9,216,10,3
	.word	22876
	.byte	20,9,219,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	10419
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_FMR',0,9,224,10,3
	.word	22937
	.byte	20,9,227,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	10755
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_ID',0,9,232,10,3
	.word	22998
	.byte	20,9,235,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	10862
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_IGCR',0,9,240,10,3
	.word	23058
	.byte	20,9,243,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	11314
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_IN',0,9,248,10,3
	.word	23120
	.byte	20,9,251,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	11413
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_IOCR',0,9,128,11,3
	.word	23180
	.byte	20,9,131,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	11563
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LBISTCTRL0',0,9,136,11,3
	.word	23242
	.byte	20,9,139,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	11712
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LBISTCTRL1',0,9,144,11,3
	.word	23310
	.byte	20,9,147,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	11873
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LBISTCTRL2',0,9,152,11,3
	.word	23378
	.byte	20,9,155,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	12003
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LCLCON0',0,9,160,11,3
	.word	23446
	.byte	20,9,163,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	12137
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_LCLTEST',0,9,168,11,3
	.word	23511
	.byte	20,9,171,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	12252
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_MANID',0,9,176,11,3
	.word	23576
	.byte	20,9,179,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	12363
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OMR',0,9,184,11,3
	.word	23639
	.byte	20,9,187,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	12521
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OSCCON',0,9,192,11,3
	.word	23700
	.byte	20,9,195,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	12861
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OUT',0,9,200,11,3
	.word	23764
	.byte	20,9,203,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	12962
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OVCCON',0,9,208,11,3
	.word	23825
	.byte	20,9,211,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	13229
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_OVCENABLE',0,9,216,11,3
	.word	23889
	.byte	20,9,219,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	13365
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PDISC',0,9,224,11,3
	.word	23956
	.byte	20,9,227,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	13476
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PDR',0,9,232,11,3
	.word	24019
	.byte	20,9,235,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	13609
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PDRR',0,9,240,11,3
	.word	24080
	.byte	20,9,243,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	13812
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLCON0',0,9,248,11,3
	.word	24142
	.byte	20,9,251,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	14168
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLCON1',0,9,128,12,3
	.word	24207
	.byte	20,9,131,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	14346
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLCON2',0,9,136,12,3
	.word	24272
	.byte	20,9,139,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	14446
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLERAYCON0',0,9,144,12,3
	.word	24337
	.byte	20,9,147,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	14816
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLERAYCON1',0,9,152,12,3
	.word	24406
	.byte	20,9,155,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	15002
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLERAYSTAT',0,9,160,12,3
	.word	24475
	.byte	20,9,163,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	15200
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PLLSTAT',0,9,168,12,3
	.word	24544
	.byte	20,9,171,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	15433
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMCSR',0,9,176,12,3
	.word	24609
	.byte	20,9,179,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	15585
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWCR0',0,9,184,12,3
	.word	24672
	.byte	20,9,187,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	16144
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWCR1',0,9,192,12,3
	.word	24737
	.byte	20,9,195,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	16327
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWCR3',0,9,200,12,3
	.word	24802
	.byte	20,9,203,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	16496
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWSTAT',0,9,208,12,3
	.word	24867
	.byte	20,9,211,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	17063
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWSTATCLR',0,9,216,12,3
	.word	24933
	.byte	20,9,219,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	17379
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_PMSWUTCNT',0,9,224,12,3
	.word	25002
	.byte	20,9,227,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	17707
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_RSTCON',0,9,232,12,3
	.word	25069
	.byte	20,9,235,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	17498
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_RSTCON2',0,9,240,12,3
	.word	25133
	.byte	20,9,243,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	17918
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_RSTSTAT',0,9,248,12,3
	.word	25198
	.byte	20,9,251,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	18350
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_SAFECON',0,9,128,13,3
	.word	25263
	.byte	20,9,131,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	18446
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_STSTAT',0,9,136,13,3
	.word	25328
	.byte	20,9,139,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	18706
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_SWRSTCON',0,9,144,13,3
	.word	25392
	.byte	20,9,147,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	18831
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_SYSCON',0,9,152,13,3
	.word	25458
	.byte	20,9,155,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	19028
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_TRAPCLR',0,9,160,13,3
	.word	25522
	.byte	20,9,163,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	19181
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_TRAPDIS',0,9,168,13,3
	.word	25587
	.byte	20,9,171,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	19334
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_TRAPSET',0,9,176,13,3
	.word	25652
	.byte	20,9,179,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	19487
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_TRAPSTAT',0,9,184,13,3
	.word	25717
	.byte	20,9,187,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	19642
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTCPU_CON0',0,9,192,13,3
	.word	25783
	.byte	20,9,195,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	19788
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTCPU_CON1',0,9,200,13,3
	.word	25852
	.byte	20,9,203,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	20026
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTCPU_SR',0,9,208,13,3
	.word	25921
	.byte	20,9,211,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	20249
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTS_CON0',0,9,216,13,3
	.word	25988
	.byte	20,9,219,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	20375
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTS_CON1',0,9,224,13,3
	.word	26055
	.byte	20,9,227,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	20627
	.byte	2,35,0,0,17
	.byte	'Ifx_SCU_WDTS_SR',0,9,232,13,3
	.word	26122
	.byte	3
	.byte	'_Ifx_SCU_WDTCPU',0,9,243,13,25,12,5
	.byte	'CON0',0,4
	.word	25783
	.byte	2,35,0,5
	.byte	'CON1',0,4
	.word	25852
	.byte	2,35,4,5
	.byte	'SR',0,4
	.word	25921
	.byte	2,35,8,0,12
	.word	26187
	.byte	17
	.byte	'Ifx_SCU_WDTCPU',0,9,248,13,3
	.word	26250
	.byte	3
	.byte	'_Ifx_SCU_WDTS',0,9,251,13,25,12,5
	.byte	'CON0',0,4
	.word	25988
	.byte	2,35,0,5
	.byte	'CON1',0,4
	.word	26055
	.byte	2,35,4,5
	.byte	'SR',0,4
	.word	26122
	.byte	2,35,8,0,12
	.word	26279
	.byte	17
	.byte	'Ifx_SCU_WDTS',0,9,128,14,3
	.word	26340
	.byte	3
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,10,45,16,4,9
	.byte	'EN0',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'EN1',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'EN2',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'EN3',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'EN4',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'EN5',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'EN6',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'EN7',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'EN8',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'EN9',0,4
	.word	19674
	.byte	1,22,2,35,0,9
	.byte	'EN10',0,4
	.word	19674
	.byte	1,21,2,35,0,9
	.byte	'EN11',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'EN12',0,4
	.word	19674
	.byte	1,19,2,35,0,9
	.byte	'EN13',0,4
	.word	19674
	.byte	1,18,2,35,0,9
	.byte	'EN14',0,4
	.word	19674
	.byte	1,17,2,35,0,9
	.byte	'EN15',0,4
	.word	19674
	.byte	1,16,2,35,0,9
	.byte	'EN16',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'EN17',0,4
	.word	19674
	.byte	1,14,2,35,0,9
	.byte	'EN18',0,4
	.word	19674
	.byte	1,13,2,35,0,9
	.byte	'EN19',0,4
	.word	19674
	.byte	1,12,2,35,0,9
	.byte	'EN20',0,4
	.word	19674
	.byte	1,11,2,35,0,9
	.byte	'EN21',0,4
	.word	19674
	.byte	1,10,2,35,0,9
	.byte	'EN22',0,4
	.word	19674
	.byte	1,9,2,35,0,9
	.byte	'EN23',0,4
	.word	19674
	.byte	1,8,2,35,0,9
	.byte	'EN24',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'EN25',0,4
	.word	19674
	.byte	1,6,2,35,0,9
	.byte	'EN26',0,4
	.word	19674
	.byte	1,5,2,35,0,9
	.byte	'EN27',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'EN28',0,4
	.word	19674
	.byte	1,3,2,35,0,9
	.byte	'EN29',0,4
	.word	19674
	.byte	1,2,2,35,0,9
	.byte	'EN30',0,4
	.word	19674
	.byte	1,1,2,35,0,9
	.byte	'EN31',0,4
	.word	19674
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_ACCEN0_Bits',0,10,79,3
	.word	26367
	.byte	3
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,10,82,16,4,9
	.byte	'reserved_0',0,4
	.word	19674
	.byte	32,0,2,35,0,0,17
	.byte	'Ifx_GTM_ACCEN1_Bits',0,10,85,3
	.word	26924
	.byte	3
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,10,88,16,4,9
	.byte	'SEL0',0,4
	.word	19674
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	19674
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	19674
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	19674
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,10,95,3
	.word	27001
	.byte	3
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,10,98,16,4,9
	.byte	'SEL0',0,4
	.word	19674
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	19674
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	19674
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	19674
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,10,105,3
	.word	27155
	.byte	3
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,10,108,16,4,9
	.byte	'TO_ADDR',0,4
	.word	19674
	.byte	20,12,2,35,0,9
	.byte	'TO_W1R0',0,4
	.word	19674
	.byte	1,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	19674
	.byte	11,0,2,35,0,0,17
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,10,113,3
	.word	27309
	.byte	3
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,10,116,16,4,9
	.byte	'BRG_MODE',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'MSK_WR_RSP',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	6,24,2,35,0,9
	.byte	'MODE_UP_PGR',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'BUFF_OVL',0,4
	.word	19674
	.byte	1,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'SYNC_INPUT_REG',0,4
	.word	19674
	.byte	1,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	19674
	.byte	3,16,2,35,0,9
	.byte	'BRG_RST',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'reserved_17',0,4
	.word	19674
	.byte	7,8,2,35,0,9
	.byte	'BUFF_DPT',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,10,129,1,3
	.word	27437
	.byte	3
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,10,132,1,16,4,9
	.byte	'NEW_TRAN_PTR',0,4
	.word	19674
	.byte	5,27,2,35,0,9
	.byte	'FIRST_RSP_PTR',0,4
	.word	19674
	.byte	5,22,2,35,0,9
	.byte	'TRAN_IN_PGR',0,4
	.word	19674
	.byte	5,17,2,35,0,9
	.byte	'ABT_TRAN_PGR',0,4
	.word	19674
	.byte	5,12,2,35,0,9
	.byte	'FBC',0,4
	.word	19674
	.byte	6,6,2,35,0,9
	.byte	'RSP_TRAN_RDY',0,4
	.word	19674
	.byte	6,0,2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,10,140,1,3
	.word	27744
	.byte	3
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,10,143,1,16,4,9
	.byte	'TRAN_IN_PGR2',0,4
	.word	19674
	.byte	5,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	19674
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,10,147,1,3
	.word	27946
	.byte	3
	.byte	'_Ifx_GTM_CLC_Bits',0,10,150,1,16,4,9
	.byte	'DISR',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'DISS',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'EDIS',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_CLC_Bits',0,10,157,1,3
	.word	28059
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,10,160,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,10,164,1,3
	.word	28202
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,10,167,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'CLK6_SEL',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	19674
	.byte	7,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,10,172,1,3
	.word	28319
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,10,175,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'CLK7_SEL',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	19674
	.byte	7,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,10,180,1,3
	.word	28454
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,10,183,1,16,4,9
	.byte	'EN_CLK0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'EN_CLK1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'EN_CLK2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'EN_CLK3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'EN_CLK4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'EN_CLK5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'EN_CLK6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'EN_CLK7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'EN_ECLK0',0,4
	.word	19674
	.byte	2,14,2,35,0,9
	.byte	'EN_ECLK1',0,4
	.word	19674
	.byte	2,12,2,35,0,9
	.byte	'EN_ECLK2',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'EN_FXCLK',0,4
	.word	19674
	.byte	2,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,10,198,1,3
	.word	28589
	.byte	3
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,10,201,1,16,4,9
	.byte	'ECLK_DEN',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,10,205,1,3
	.word	28909
	.byte	3
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,10,208,1,16,4,9
	.byte	'ECLK_NUM',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,10,212,1,3
	.word	29021
	.byte	3
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,10,215,1,16,4,9
	.byte	'FXCLK_SEL',0,4
	.word	19674
	.byte	4,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,10,219,1,3
	.word	29133
	.byte	3
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,10,222,1,16,4,9
	.byte	'GCLK_DEN',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,10,226,1,3
	.word	29249
	.byte	3
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,10,229,1,16,4,9
	.byte	'GCLK_NUM',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,10,233,1,3
	.word	29361
	.byte	3
	.byte	'_Ifx_GTM_CTRL_Bits',0,10,236,1,16,4,9
	.byte	'RF_PROT',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'TO_MODE',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'TO_VAL',0,4
	.word	19674
	.byte	5,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	19674
	.byte	23,0,2,35,0,0,17
	.byte	'Ifx_GTM_CTRL_Bits',0,10,243,1,3
	.word	29473
	.byte	3
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,10,246,1,16,4,9
	.byte	'O1SEL_0',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	19674
	.byte	2,29,2,35,0,9
	.byte	'SWAP_0',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'O1F_0',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'O1SEL_1',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'I1SEL_1',0,4
	.word	19674
	.byte	1,22,2,35,0,9
	.byte	'SH_EN_1',0,4
	.word	19674
	.byte	1,21,2,35,0,9
	.byte	'SWAP_1',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'O1F_1',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'O1SEL_2',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'I1SEL_2',0,4
	.word	19674
	.byte	1,14,2,35,0,9
	.byte	'SH_EN_2',0,4
	.word	19674
	.byte	1,13,2,35,0,9
	.byte	'SWAP_2',0,4
	.word	19674
	.byte	1,12,2,35,0,9
	.byte	'O1F_2',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'reserved_22',0,4
	.word	19674
	.byte	2,8,2,35,0,9
	.byte	'O1SEL_3',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'I1SEL_3',0,4
	.word	19674
	.byte	1,6,2,35,0,9
	.byte	'SH_EN_3',0,4
	.word	19674
	.byte	1,5,2,35,0,9
	.byte	'SWAP_3',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'O1F_3',0,4
	.word	19674
	.byte	2,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,10,143,2,3
	.word	29626
	.byte	3
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,10,146,2,16,4,9
	.byte	'POL0_0',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'OC0_0',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'SL0_0',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'DT0_0',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'POL1_0',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'OC1_0',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'SL1_0',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'DT1_0',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'POL0_1',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'OC0_1',0,4
	.word	19674
	.byte	1,22,2,35,0,9
	.byte	'SL0_1',0,4
	.word	19674
	.byte	1,21,2,35,0,9
	.byte	'DT0_1',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'POL1_1',0,4
	.word	19674
	.byte	1,19,2,35,0,9
	.byte	'OC1_1',0,4
	.word	19674
	.byte	1,18,2,35,0,9
	.byte	'SL1_1',0,4
	.word	19674
	.byte	1,17,2,35,0,9
	.byte	'DT1_1',0,4
	.word	19674
	.byte	1,16,2,35,0,9
	.byte	'POL0_2',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'OC0_2',0,4
	.word	19674
	.byte	1,14,2,35,0,9
	.byte	'SL0_2',0,4
	.word	19674
	.byte	1,13,2,35,0,9
	.byte	'DT0_2',0,4
	.word	19674
	.byte	1,12,2,35,0,9
	.byte	'POL1_2',0,4
	.word	19674
	.byte	1,11,2,35,0,9
	.byte	'OC1_2',0,4
	.word	19674
	.byte	1,10,2,35,0,9
	.byte	'SL1_2',0,4
	.word	19674
	.byte	1,9,2,35,0,9
	.byte	'DT1_2',0,4
	.word	19674
	.byte	1,8,2,35,0,9
	.byte	'POL0_3',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'OC0_3',0,4
	.word	19674
	.byte	1,6,2,35,0,9
	.byte	'SL0_3',0,4
	.word	19674
	.byte	1,5,2,35,0,9
	.byte	'DT0_3',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'POL1_3',0,4
	.word	19674
	.byte	1,3,2,35,0,9
	.byte	'OC1_3',0,4
	.word	19674
	.byte	1,2,2,35,0,9
	.byte	'SL1_3',0,4
	.word	19674
	.byte	1,1,2,35,0,9
	.byte	'DT1_3',0,4
	.word	19674
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,10,180,2,3
	.word	30138
	.byte	3
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,10,183,2,16,4,9
	.byte	'POL0_0_SR',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'OC0_0_SR',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'SL0_0_SR',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'DT0_0_SR',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'POL1_0_SR',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'OC1_0_SR',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'SL1_0_SR',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'DT1_0_SR',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'POL0_1_SR',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'OC0_1_SR',0,4
	.word	19674
	.byte	1,22,2,35,0,9
	.byte	'SL0_1_SR',0,4
	.word	19674
	.byte	1,21,2,35,0,9
	.byte	'DT0_1_SR',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'POL1_1_SR',0,4
	.word	19674
	.byte	1,19,2,35,0,9
	.byte	'OC1_1_SR',0,4
	.word	19674
	.byte	1,18,2,35,0,9
	.byte	'SL1_1_SR',0,4
	.word	19674
	.byte	1,17,2,35,0,9
	.byte	'DT1_1_SR',0,4
	.word	19674
	.byte	1,16,2,35,0,9
	.byte	'POL0_2_SR',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'OC0_2_SR',0,4
	.word	19674
	.byte	1,14,2,35,0,9
	.byte	'SL0_2_SR',0,4
	.word	19674
	.byte	1,13,2,35,0,9
	.byte	'DT0_2_SR',0,4
	.word	19674
	.byte	1,12,2,35,0,9
	.byte	'POL1_2_SR',0,4
	.word	19674
	.byte	1,11,2,35,0,9
	.byte	'OC1_2_SR',0,4
	.word	19674
	.byte	1,10,2,35,0,9
	.byte	'SL1_2_SR',0,4
	.word	19674
	.byte	1,9,2,35,0,9
	.byte	'DT1_2_SR',0,4
	.word	19674
	.byte	1,8,2,35,0,9
	.byte	'POL0_3_SR',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'OC0_3_SR',0,4
	.word	19674
	.byte	1,6,2,35,0,9
	.byte	'SL0_3_SR',0,4
	.word	19674
	.byte	1,5,2,35,0,9
	.byte	'DT0_3_SR',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'POL1_3_SR',0,4
	.word	19674
	.byte	1,3,2,35,0,9
	.byte	'OC1_3_SR',0,4
	.word	19674
	.byte	1,2,2,35,0,9
	.byte	'SL1_3_SR',0,4
	.word	19674
	.byte	1,1,2,35,0,9
	.byte	'DT1_3_SR',0,4
	.word	19674
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,10,217,2,3
	.word	30759
	.byte	3
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,10,220,2,16,4,9
	.byte	'CLK_SEL',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'UPD_MODE',0,4
	.word	19674
	.byte	3,25,2,35,0,9
	.byte	'reserved_7',0,4
	.word	19674
	.byte	25,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,10,226,2,3
	.word	31482
	.byte	3
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,10,229,2,16,4,9
	.byte	'RELRISE',0,4
	.word	19674
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	19674
	.byte	6,16,2,35,0,9
	.byte	'RELFALL',0,4
	.word	19674
	.byte	10,6,2,35,0,9
	.byte	'reserved_26',0,4
	.word	19674
	.byte	6,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,10,235,2,3
	.word	31626
	.byte	3
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,10,238,2,16,4,9
	.byte	'RELBLK',0,4
	.word	19674
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	19674
	.byte	6,16,2,35,0,9
	.byte	'PSU_IN_SEL',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'IN_POL',0,4
	.word	19674
	.byte	1,14,2,35,0,9
	.byte	'reserved_18',0,4
	.word	19674
	.byte	2,12,2,35,0,9
	.byte	'SHIFT_SEL',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'reserved_22',0,4
	.word	19674
	.byte	10,0,2,35,0,0,17
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,10,247,2,3
	.word	31775
	.byte	3
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,10,250,2,16,4,9
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,10,129,3,3
	.word	31990
	.byte	3
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,10,132,3,16,4,9
	.byte	'GRSTEN',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'BRIDGE_MODE_RST',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'AEI_IN',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	19674
	.byte	5,24,2,35,0,9
	.byte	'TOM_OUT_RST',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	19674
	.byte	3,20,2,35,0,9
	.byte	'reserved_12',0,4
	.word	19674
	.byte	4,16,2,35,0,9
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'IRQ_MODE_PULSE',0,4
	.word	19674
	.byte	1,14,2,35,0,9
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	19674
	.byte	1,13,2,35,0,9
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	19674
	.byte	1,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	19674
	.byte	12,0,2,35,0,0,17
	.byte	'Ifx_GTM_HW_CONF_Bits',0,10,146,3,3
	.word	32194
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,10,149,3,16,4,9
	.byte	'reserved_0',0,4
	.word	19674
	.byte	4,28,2,35,0,9
	.byte	'AEI_IRQ',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	19674
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,10,154,3,3
	.word	32551
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,10,157,3,16,4,9
	.byte	'TIM0_CH0_IRQ',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'TIM0_CH1_IRQ',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'TIM0_CH2_IRQ',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'TIM0_CH3_IRQ',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'TIM0_CH4_IRQ',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'TIM0_CH5_IRQ',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'TIM0_CH6_IRQ',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'TIM0_CH7_IRQ',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	19674
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,10,168,3,3
	.word	32679
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,10,171,3,16,4,9
	.byte	'TOM0_CH0_IRQ',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'TOM0_CH1_IRQ',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'TOM0_CH2_IRQ',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'TOM0_CH3_IRQ',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'TOM0_CH4_IRQ',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'TOM0_CH5_IRQ',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'TOM0_CH6_IRQ',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'TOM0_CH7_IRQ',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'TOM0_CH8_IRQ',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'TOM0_CH9_IRQ',0,4
	.word	19674
	.byte	1,22,2,35,0,9
	.byte	'TOM0_CH10_IRQ',0,4
	.word	19674
	.byte	1,21,2,35,0,9
	.byte	'TOM0_CH11_IRQ',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'TOM0_CH12_IRQ',0,4
	.word	19674
	.byte	1,19,2,35,0,9
	.byte	'TOM0_CH13_IRQ',0,4
	.word	19674
	.byte	1,18,2,35,0,9
	.byte	'TOM0_CH14_IRQ',0,4
	.word	19674
	.byte	1,17,2,35,0,9
	.byte	'TOM0_CH15_IRQ',0,4
	.word	19674
	.byte	1,16,2,35,0,9
	.byte	'TOM1_CH0_IRQ',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'TOM1_CH1_IRQ',0,4
	.word	19674
	.byte	1,14,2,35,0,9
	.byte	'TOM1_CH2_IRQ',0,4
	.word	19674
	.byte	1,13,2,35,0,9
	.byte	'TOM1_CH3_IRQ',0,4
	.word	19674
	.byte	1,12,2,35,0,9
	.byte	'TOM1_CH4_IRQ',0,4
	.word	19674
	.byte	1,11,2,35,0,9
	.byte	'TOM1_CH5_IRQ',0,4
	.word	19674
	.byte	1,10,2,35,0,9
	.byte	'TOM1_CH6_IRQ',0,4
	.word	19674
	.byte	1,9,2,35,0,9
	.byte	'TOM1_CH7_IRQ',0,4
	.word	19674
	.byte	1,8,2,35,0,9
	.byte	'TOM1_CH8_IRQ',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'TOM1_CH9_IRQ',0,4
	.word	19674
	.byte	1,6,2,35,0,9
	.byte	'TOM1_CH10_IRQ',0,4
	.word	19674
	.byte	1,5,2,35,0,9
	.byte	'TOM1_CH11_IRQ',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'TOM1_CH12_IRQ',0,4
	.word	19674
	.byte	1,3,2,35,0,9
	.byte	'TOM1_CH13_IRQ',0,4
	.word	19674
	.byte	1,2,2,35,0,9
	.byte	'TOM1_CH14_IRQ',0,4
	.word	19674
	.byte	1,1,2,35,0,9
	.byte	'TOM1_CH15_IRQ',0,4
	.word	19674
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,10,205,3,3
	.word	32958
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,10,208,3,16,4,9
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	19674
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,10,219,3,3
	.word	33803
	.byte	3
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,10,222,3,16,4,9
	.byte	'GTM_EIRQ',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	19674
	.byte	3,28,2,35,0,9
	.byte	'TIM0_EIRQ',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	19674
	.byte	27,0,2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,10,228,3,3
	.word	34096
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,10,231,3,16,4,9
	.byte	'SEL0',0,4
	.word	19674
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	19674
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	19674
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	19674
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,10,238,3,3
	.word	34250
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,10,241,3,16,4,9
	.byte	'SEL0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'SEL1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'SEL2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'SEL3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'SEL4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'SEL5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'SEL6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'SEL7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'SEL8',0,4
	.word	19674
	.byte	2,14,2,35,0,9
	.byte	'SEL9',0,4
	.word	19674
	.byte	2,12,2,35,0,9
	.byte	'SEL10',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'SEL11',0,4
	.word	19674
	.byte	2,8,2,35,0,9
	.byte	'SEL12',0,4
	.word	19674
	.byte	2,6,2,35,0,9
	.byte	'SEL13',0,4
	.word	19674
	.byte	2,4,2,35,0,9
	.byte	'SEL14',0,4
	.word	19674
	.byte	2,2,2,35,0,9
	.byte	'SEL15',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,10,131,4,3
	.word	34420
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,10,134,4,16,4,9
	.byte	'CH0SEL',0,4
	.word	19674
	.byte	4,28,2,35,0,9
	.byte	'CH1SEL',0,4
	.word	19674
	.byte	4,24,2,35,0,9
	.byte	'CH2SEL',0,4
	.word	19674
	.byte	4,20,2,35,0,9
	.byte	'CH3SEL',0,4
	.word	19674
	.byte	4,16,2,35,0,9
	.byte	'CH4SEL',0,4
	.word	19674
	.byte	4,12,2,35,0,9
	.byte	'CH5SEL',0,4
	.word	19674
	.byte	4,8,2,35,0,9
	.byte	'CH6SEL',0,4
	.word	19674
	.byte	4,4,2,35,0,9
	.byte	'CH7SEL',0,4
	.word	19674
	.byte	4,0,2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,10,144,4,3
	.word	34761
	.byte	3
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,10,147,4,16,4,9
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,10,154,4,3
	.word	34986
	.byte	3
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,10,157,4,16,4,9
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'TRG_AEI_USP_BE',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,10,164,4,3
	.word	35184
	.byte	3
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,10,167,4,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,10,171,4,3
	.word	35380
	.byte	3
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,10,174,4,16,4,9
	.byte	'AEI_TO_XPT',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,10,181,4,3
	.word	35483
	.byte	3
	.byte	'_Ifx_GTM_KRST0_Bits',0,10,184,4,16,4,9
	.byte	'RST',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'RSTSTAT',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_KRST0_Bits',0,10,189,4,3
	.word	35661
	.byte	3
	.byte	'_Ifx_GTM_KRST1_Bits',0,10,192,4,16,4,9
	.byte	'RST',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	19674
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_GTM_KRST1_Bits',0,10,196,4,3
	.word	35772
	.byte	3
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,10,199,4,16,4,9
	.byte	'CLR',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	19674
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,10,203,4,3
	.word	35864
	.byte	3
	.byte	'_Ifx_GTM_OCS_Bits',0,10,206,4,16,4,9
	.byte	'reserved_0',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'SUS',0,4
	.word	19674
	.byte	4,4,2,35,0,9
	.byte	'SUS_P',0,4
	.word	19674
	.byte	1,3,2,35,0,9
	.byte	'SUSSTA',0,4
	.word	19674
	.byte	1,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_OCS_Bits',0,10,213,4,3
	.word	35960
	.byte	3
	.byte	'_Ifx_GTM_ODA_Bits',0,10,216,4,16,4,9
	.byte	'DDREN',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'DREN',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_ODA_Bits',0,10,221,4,3
	.word	36106
	.byte	3
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,10,224,4,16,4,9
	.byte	'CV',0,4
	.word	19674
	.byte	27,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'CM',0,4
	.word	19674
	.byte	2,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTBU0T_Bits',0,10,230,4,3
	.word	36212
	.byte	3
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,10,233,4,16,4,9
	.byte	'CV',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	4,4,2,35,0,9
	.byte	'EN',0,4
	.word	19674
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	19674
	.byte	3,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTBU1T_Bits',0,10,239,4,3
	.word	36343
	.byte	3
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,10,242,4,16,4,9
	.byte	'CV',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	4,4,2,35,0,9
	.byte	'EN',0,4
	.word	19674
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	19674
	.byte	3,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTBU2T_Bits',0,10,248,4,3
	.word	36474
	.byte	3
	.byte	'_Ifx_GTM_OTSC0_Bits',0,10,251,4,16,4,9
	.byte	'B0LMT',0,4
	.word	19674
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'B0LMI',0,4
	.word	19674
	.byte	4,24,2,35,0,9
	.byte	'B0HMT',0,4
	.word	19674
	.byte	3,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'B0HMI',0,4
	.word	19674
	.byte	4,16,2,35,0,9
	.byte	'B1LMT',0,4
	.word	19674
	.byte	3,13,2,35,0,9
	.byte	'reserved_19',0,4
	.word	19674
	.byte	1,12,2,35,0,9
	.byte	'B1LMI',0,4
	.word	19674
	.byte	4,8,2,35,0,9
	.byte	'B1HMT',0,4
	.word	19674
	.byte	3,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'B1HMI',0,4
	.word	19674
	.byte	4,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTSC0_Bits',0,10,137,5,3
	.word	36605
	.byte	3
	.byte	'_Ifx_GTM_OTSS_Bits',0,10,140,5,16,4,9
	.byte	'OTGB0',0,4
	.word	19674
	.byte	4,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	4,24,2,35,0,9
	.byte	'OTGB1',0,4
	.word	19674
	.byte	4,20,2,35,0,9
	.byte	'reserved_12',0,4
	.word	19674
	.byte	4,16,2,35,0,9
	.byte	'OTGB2',0,4
	.word	19674
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	19674
	.byte	12,0,2,35,0,0,17
	.byte	'Ifx_GTM_OTSS_Bits',0,10,148,5,3
	.word	36887
	.byte	3
	.byte	'_Ifx_GTM_REV_Bits',0,10,151,5,16,4,9
	.byte	'STEP',0,4
	.word	19674
	.byte	8,24,2,35,0,9
	.byte	'NO',0,4
	.word	19674
	.byte	4,20,2,35,0,9
	.byte	'MINOR',0,4
	.word	19674
	.byte	4,16,2,35,0,9
	.byte	'MAJOR',0,4
	.word	19674
	.byte	4,12,2,35,0,9
	.byte	'DEV_CODE0',0,4
	.word	19674
	.byte	4,8,2,35,0,9
	.byte	'DEV_CODE1',0,4
	.word	19674
	.byte	4,4,2,35,0,9
	.byte	'DEV_CODE2',0,4
	.word	19674
	.byte	4,0,2,35,0,0,17
	.byte	'Ifx_GTM_REV_Bits',0,10,160,5,3
	.word	37059
	.byte	3
	.byte	'_Ifx_GTM_RST_Bits',0,10,163,5,16,4,9
	.byte	'RST',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	19674
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_GTM_RST_Bits',0,10,167,5,3
	.word	37237
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,10,170,5,16,4,9
	.byte	'BASE',0,4
	.word	19674
	.byte	27,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	19674
	.byte	5,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,10,174,5,3
	.word	37325
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,10,177,5,16,4,9
	.byte	'LOW_RES',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	19674
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,10,182,5,3
	.word	37433
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,10,185,5,16,4,9
	.byte	'BASE',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,10,189,5,3
	.word	37565
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,10,192,5,16,4,9
	.byte	'CH_MODE',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	19674
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,10,197,5,3
	.word	37673
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,10,200,5,16,4,9
	.byte	'BASE',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,10,204,5,3
	.word	37805
	.byte	3
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,10,207,5,16,4,9
	.byte	'CH_MODE',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	19674
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	19674
	.byte	28,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,10,212,5,3
	.word	37913
	.byte	3
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,10,215,5,16,4,9
	.byte	'ENDIS_CH0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CH1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CH2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	19674
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,10,221,5,3
	.word	38045
	.byte	3
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,10,224,5,16,4,9
	.byte	'SRC_CH0',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'SRC_CH1',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'SRC_CH2',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'SRC_CH3',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'SRC_CH4',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'SRC_CH5',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'SRC_CH6',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'SRC_CH7',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	19674
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,10,235,5,3
	.word	38191
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,10,238,5,16,4,9
	.byte	'CNT',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,10,242,5,3
	.word	38438
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,10,245,5,16,4,9
	.byte	'CNTS',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,10,249,5,3
	.word	38541
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,10,252,5,16,4,9
	.byte	'TIM_EN',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'TIM_MODE',0,4
	.word	19674
	.byte	3,28,2,35,0,9
	.byte	'OSM',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'CICTRL',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'TBU0x_SEL',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'GPR0_SEL',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'GPR1_SEL',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'CNTS_SEL',0,4
	.word	19674
	.byte	1,19,2,35,0,9
	.byte	'DSL',0,4
	.word	19674
	.byte	1,18,2,35,0,9
	.byte	'ISL',0,4
	.word	19674
	.byte	1,17,2,35,0,9
	.byte	'ECNT_RESET',0,4
	.word	19674
	.byte	1,16,2,35,0,9
	.byte	'FLT_EN',0,4
	.word	19674
	.byte	1,15,2,35,0,9
	.byte	'FLT_CNT_FRQ',0,4
	.word	19674
	.byte	2,13,2,35,0,9
	.byte	'EXT_CAP_EN',0,4
	.word	19674
	.byte	1,12,2,35,0,9
	.byte	'FLT_MODE_RE',0,4
	.word	19674
	.byte	1,11,2,35,0,9
	.byte	'FLT_CTR_RE',0,4
	.word	19674
	.byte	1,10,2,35,0,9
	.byte	'FLT_MODE_FE',0,4
	.word	19674
	.byte	1,9,2,35,0,9
	.byte	'FLT_CTR_FE',0,4
	.word	19674
	.byte	1,8,2,35,0,9
	.byte	'CLK_SEL',0,4
	.word	19674
	.byte	3,5,2,35,0,9
	.byte	'FR_ECNT_OFL',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'EGPR0_SEL',0,4
	.word	19674
	.byte	1,3,2,35,0,9
	.byte	'EGPR1_SEL',0,4
	.word	19674
	.byte	1,2,2,35,0,9
	.byte	'TOCTRL',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,10,150,6,3
	.word	38640
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,10,153,6,16,4,9
	.byte	'ECNT',0,4
	.word	19674
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,10,157,6,3
	.word	39188
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,10,160,6,16,4,9
	.byte	'EXT_CAP_SRC',0,4
	.word	19674
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	19674
	.byte	29,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,10,164,6,3
	.word	39294
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,10,167,6,16,4,9
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'TODET_EIRQ_EN',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	19674
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,10,176,6,3
	.word	39408
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,10,179,6,16,4,9
	.byte	'FLT_FE',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,10,183,6,3
	.word	39663
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,10,186,6,16,4,9
	.byte	'FLT_RE',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,10,190,6,3
	.word	39775
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,10,193,6,16,4,9
	.byte	'GPR0',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,10,197,6,3
	.word	39887
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,10,200,6,16,4,9
	.byte	'GPR1',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,10,204,6,3
	.word	39986
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,10,207,6,16,4,9
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'TODET_IRQ_EN',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	19674
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,10,216,6,3
	.word	40085
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,10,219,6,16,4,9
	.byte	'TRG_NEWVAL',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'TRG_ECNTOFL',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'TRG_CNTOFL',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'TRG_GPRzOFL',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'TRG_TODET',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'TRG_GLITCHDET',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	19674
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,10,228,6,3
	.word	40332
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,10,231,6,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,10,235,6,3
	.word	40571
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,10,238,6,16,4,9
	.byte	'NEWVAL',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'TODET',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	19674
	.byte	26,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,10,247,6,3
	.word	40688
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,10,250,6,16,4,9
	.byte	'TO_CNT',0,4
	.word	19674
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	19674
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,10,254,6,3
	.word	40901
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,10,129,7,16,4,9
	.byte	'TOV',0,4
	.word	19674
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	19674
	.byte	20,4,2,35,0,9
	.byte	'TCS',0,4
	.word	19674
	.byte	3,1,2,35,0,9
	.byte	'reserved_31',0,4
	.word	19674
	.byte	1,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,10,135,7,3
	.word	41008
	.byte	3
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,10,138,7,16,4,9
	.byte	'VAL_0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'MODE_0',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'VAL_1',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'MODE_1',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'VAL_2',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'MODE_2',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'VAL_3',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'MODE_3',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'VAL_4',0,4
	.word	19674
	.byte	2,14,2,35,0,9
	.byte	'MODE_4',0,4
	.word	19674
	.byte	2,12,2,35,0,9
	.byte	'VAL_5',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'MODE_5',0,4
	.word	19674
	.byte	2,8,2,35,0,9
	.byte	'VAL_6',0,4
	.word	19674
	.byte	2,6,2,35,0,9
	.byte	'MODE_6',0,4
	.word	19674
	.byte	2,4,2,35,0,9
	.byte	'VAL_7',0,4
	.word	19674
	.byte	2,2,2,35,0,9
	.byte	'MODE_7',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,10,156,7,3
	.word	41150
	.byte	3
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,10,159,7,16,4,9
	.byte	'F_OUT',0,4
	.word	19674
	.byte	8,24,2,35,0,9
	.byte	'F_IN',0,4
	.word	19674
	.byte	8,16,2,35,0,9
	.byte	'TIM_IN',0,4
	.word	19674
	.byte	8,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	19674
	.byte	8,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,10,165,7,3
	.word	41495
	.byte	3
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,10,168,7,16,4,9
	.byte	'RST_CH0',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	19674
	.byte	1,29,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	19674
	.byte	1,28,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	19674
	.byte	1,27,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	19674
	.byte	1,26,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	19674
	.byte	1,25,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	19674
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	19674
	.byte	24,0,2,35,0,0,17
	.byte	'Ifx_GTM_TIM_RST_Bits',0,10,179,7,3
	.word	41636
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,10,182,7,16,4,9
	.byte	'CM0',0,4
	.word	19674
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,10,186,7,3
	.word	41869
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,10,189,7,16,4,9
	.byte	'CM1',0,4
	.word	19674
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,10,193,7,3
	.word	41972
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,10,196,7,16,4,9
	.byte	'CN0',0,4
	.word	19674
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,10,200,7,3
	.word	42075
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,10,203,7,16,4,9
	.byte	'reserved_0',0,4
	.word	19674
	.byte	11,21,2,35,0,9
	.byte	'SL',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'CLK_SRC_SR',0,4
	.word	19674
	.byte	3,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	19674
	.byte	5,12,2,35,0,9
	.byte	'RST_CCU0',0,4
	.word	19674
	.byte	1,11,2,35,0,9
	.byte	'OSM_TRIG',0,4
	.word	19674
	.byte	1,10,2,35,0,9
	.byte	'EXT_TRIG',0,4
	.word	19674
	.byte	1,9,2,35,0,9
	.byte	'EXTTRIGOUT',0,4
	.word	19674
	.byte	1,8,2,35,0,9
	.byte	'TRIGOUT',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	19674
	.byte	1,6,2,35,0,9
	.byte	'OSM',0,4
	.word	19674
	.byte	1,5,2,35,0,9
	.byte	'BITREV',0,4
	.word	19674
	.byte	1,4,2,35,0,9
	.byte	'reserved_28',0,4
	.word	19674
	.byte	4,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,10,218,7,3
	.word	42178
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,10,221,7,16,4,9
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,10,226,7,3
	.word	42506
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,10,229,7,16,4,9
	.byte	'TRG_CCU0TC0',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'TRG_CCU1TC0',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,10,234,7,3
	.word	42649
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,10,237,7,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,10,241,7,3
	.word	42798
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,10,244,7,16,4,9
	.byte	'CCU0TC',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'CCU1TC',0,4
	.word	19674
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	19674
	.byte	30,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,10,249,7,3
	.word	42915
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,10,252,7,16,4,9
	.byte	'SR0',0,4
	.word	19674
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,10,128,8,3
	.word	43052
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,10,131,8,16,4,9
	.byte	'SR1',0,4
	.word	19674
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,10,135,8,3
	.word	43155
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,10,138,8,16,4,9
	.byte	'OL',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	19674
	.byte	31,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,10,142,8,3
	.word	43258
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,10,145,8,16,4,9
	.byte	'ACT_TB',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'TB_TRIG',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'TBU_SEL',0,4
	.word	19674
	.byte	2,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	19674
	.byte	5,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,10,151,8,3
	.word	43361
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,10,154,8,16,4,9
	.byte	'ENDIS_CTRL0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CTRL1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CTRL2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_CTRL3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_CTRL4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_CTRL5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_CTRL6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_CTRL7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,10,165,8,3
	.word	43515
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,10,168,8,16,4,9
	.byte	'ENDIS_STAT0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_STAT1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_STAT2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_STAT3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_STAT4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_STAT5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_STAT6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_STAT7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,10,179,8,3
	.word	43805
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,10,182,8,16,4,9
	.byte	'FUPD_CTRL0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'FUPD_CTRL1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'FUPD_CTRL2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'FUPD_CTRL3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'FUPD_CTRL4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'FUPD_CTRL5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'FUPD_CTRL6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'FUPD_CTRL7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'RSTCN0_CH0',0,4
	.word	19674
	.byte	2,14,2,35,0,9
	.byte	'RSTCN0_CH1',0,4
	.word	19674
	.byte	2,12,2,35,0,9
	.byte	'RSTCN0_CH2',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'RSTCN0_CH3',0,4
	.word	19674
	.byte	2,8,2,35,0,9
	.byte	'RSTCN0_CH4',0,4
	.word	19674
	.byte	2,6,2,35,0,9
	.byte	'RSTCN0_CH5',0,4
	.word	19674
	.byte	2,4,2,35,0,9
	.byte	'RSTCN0_CH6',0,4
	.word	19674
	.byte	2,2,2,35,0,9
	.byte	'RSTCN0_CH7',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,10,200,8,3
	.word	44095
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,10,203,8,16,4,9
	.byte	'HOST_TRIG',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	19674
	.byte	7,24,2,35,0,9
	.byte	'RST_CH0',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	19674
	.byte	1,22,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	19674
	.byte	1,21,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	19674
	.byte	1,19,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	19674
	.byte	1,18,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	19674
	.byte	1,17,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	19674
	.byte	1,16,2,35,0,9
	.byte	'UPEN_CTRL0',0,4
	.word	19674
	.byte	2,14,2,35,0,9
	.byte	'UPEN_CTRL1',0,4
	.word	19674
	.byte	2,12,2,35,0,9
	.byte	'UPEN_CTRL2',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'UPEN_CTRL3',0,4
	.word	19674
	.byte	2,8,2,35,0,9
	.byte	'UPEN_CTRL4',0,4
	.word	19674
	.byte	2,6,2,35,0,9
	.byte	'UPEN_CTRL5',0,4
	.word	19674
	.byte	2,4,2,35,0,9
	.byte	'UPEN_CTRL6',0,4
	.word	19674
	.byte	2,2,2,35,0,9
	.byte	'UPEN_CTRL7',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,10,223,8,3
	.word	44528
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,10,226,8,16,4,9
	.byte	'INT_TRIG0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'INT_TRIG1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'INT_TRIG2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'INT_TRIG3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'INT_TRIG4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'INT_TRIG5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'INT_TRIG6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'INT_TRIG7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,10,237,8,3
	.word	44978
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,10,240,8,16,4,9
	.byte	'OUTEN_CTRL0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_CTRL1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_CTRL2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_CTRL3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_CTRL4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_CTRL5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_CTRL6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_CTRL7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,10,251,8,3
	.word	45248
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,10,254,8,16,4,9
	.byte	'OUTEN_STAT0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_STAT1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_STAT2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_STAT3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_STAT4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_STAT5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_STAT6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_STAT7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,10,137,9,3
	.word	45538
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,10,140,9,16,4,9
	.byte	'ACT_TB',0,4
	.word	19674
	.byte	24,8,2,35,0,9
	.byte	'TB_TRIG',0,4
	.word	19674
	.byte	1,7,2,35,0,9
	.byte	'TBU_SEL',0,4
	.word	19674
	.byte	2,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	19674
	.byte	5,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,10,146,9,3
	.word	45828
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,10,149,9,16,4,9
	.byte	'ENDIS_CTRL0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CTRL1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CTRL2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_CTRL3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_CTRL4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_CTRL5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_CTRL6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_CTRL7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,10,160,9,3
	.word	45982
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,10,163,9,16,4,9
	.byte	'ENDIS_STAT0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_STAT1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_STAT2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_STAT3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_STAT4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_STAT5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_STAT6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_STAT7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,10,174,9,3
	.word	46272
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,10,177,9,16,4,9
	.byte	'FUPD_CTRL0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'FUPD_CTRL1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'FUPD_CTRL2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'FUPD_CTRL3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'FUPD_CTRL4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'FUPD_CTRL5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'FUPD_CTRL6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'FUPD_CTRL7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'RSTCN0_CH0',0,4
	.word	19674
	.byte	2,14,2,35,0,9
	.byte	'RSTCN0_CH1',0,4
	.word	19674
	.byte	2,12,2,35,0,9
	.byte	'RSTCN0_CH2',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'RSTCN0_CH3',0,4
	.word	19674
	.byte	2,8,2,35,0,9
	.byte	'RSTCN0_CH4',0,4
	.word	19674
	.byte	2,6,2,35,0,9
	.byte	'RSTCN0_CH5',0,4
	.word	19674
	.byte	2,4,2,35,0,9
	.byte	'RSTCN0_CH6',0,4
	.word	19674
	.byte	2,2,2,35,0,9
	.byte	'RSTCN0_CH7',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,10,195,9,3
	.word	46562
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,10,198,9,16,4,9
	.byte	'HOST_TRIG',0,4
	.word	19674
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	19674
	.byte	7,24,2,35,0,9
	.byte	'RST_CH0',0,4
	.word	19674
	.byte	1,23,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	19674
	.byte	1,22,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	19674
	.byte	1,21,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	19674
	.byte	1,20,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	19674
	.byte	1,19,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	19674
	.byte	1,18,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	19674
	.byte	1,17,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	19674
	.byte	1,16,2,35,0,9
	.byte	'UPEN_CTRL0',0,4
	.word	19674
	.byte	2,14,2,35,0,9
	.byte	'UPEN_CTRL1',0,4
	.word	19674
	.byte	2,12,2,35,0,9
	.byte	'UPEN_CTRL2',0,4
	.word	19674
	.byte	2,10,2,35,0,9
	.byte	'UPEN_CTRL3',0,4
	.word	19674
	.byte	2,8,2,35,0,9
	.byte	'UPEN_CTRL4',0,4
	.word	19674
	.byte	2,6,2,35,0,9
	.byte	'UPEN_CTRL5',0,4
	.word	19674
	.byte	2,4,2,35,0,9
	.byte	'UPEN_CTRL6',0,4
	.word	19674
	.byte	2,2,2,35,0,9
	.byte	'UPEN_CTRL7',0,4
	.word	19674
	.byte	2,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,10,218,9,3
	.word	46995
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,10,221,9,16,4,9
	.byte	'INT_TRIG0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'INT_TRIG1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'INT_TRIG2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'INT_TRIG3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'INT_TRIG4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'INT_TRIG5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'INT_TRIG6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'INT_TRIG7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,10,232,9,3
	.word	47445
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,10,235,9,16,4,9
	.byte	'OUTEN_CTRL0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_CTRL1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_CTRL2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_CTRL3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_CTRL4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_CTRL5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_CTRL6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_CTRL7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,10,246,9,3
	.word	47715
	.byte	3
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,10,249,9,16,4,9
	.byte	'OUTEN_STAT0',0,4
	.word	19674
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_STAT1',0,4
	.word	19674
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_STAT2',0,4
	.word	19674
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_STAT3',0,4
	.word	19674
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_STAT4',0,4
	.word	19674
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_STAT5',0,4
	.word	19674
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_STAT6',0,4
	.word	19674
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_STAT7',0,4
	.word	19674
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	19674
	.byte	16,0,2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,10,132,10,3
	.word	48005
	.byte	20,10,140,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	26367
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ACCEN0',0,10,145,10,3
	.word	48295
	.byte	20,10,148,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	26924
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ACCEN1',0,10,153,10,3
	.word	48359
	.byte	20,10,156,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	27001
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,10,161,10,3
	.word	48423
	.byte	20,10,164,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	27155
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,10,169,10,3
	.word	48493
	.byte	20,10,172,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	27309
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,10,177,10,3
	.word	48563
	.byte	20,10,180,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	27437
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_MODE',0,10,185,10,3
	.word	48633
	.byte	20,10,188,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	27744
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,10,193,10,3
	.word	48702
	.byte	20,10,196,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	27946
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,10,201,10,3
	.word	48771
	.byte	20,10,204,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	28059
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CLC',0,10,209,10,3
	.word	48840
	.byte	20,10,212,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	28202
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,10,217,10,3
	.word	48901
	.byte	20,10,220,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	28319
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,10,225,10,3
	.word	48974
	.byte	20,10,228,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	28454
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,10,233,10,3
	.word	49046
	.byte	20,10,236,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	28589
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_CLK_EN',0,10,241,10,3
	.word	49118
	.byte	20,10,244,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	28909
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,10,249,10,3
	.word	49186
	.byte	20,10,252,10,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	29021
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,10,129,11,3
	.word	49256
	.byte	20,10,132,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	29133
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,10,137,11,3
	.word	49326
	.byte	20,10,140,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	29249
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,10,145,11,3
	.word	49398
	.byte	20,10,148,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	29361
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,10,153,11,3
	.word	49468
	.byte	20,10,156,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	29473
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_CTRL',0,10,161,11,3
	.word	49538
	.byte	20,10,164,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	29626
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,10,169,11,3
	.word	49600
	.byte	20,10,172,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	30138
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,10,177,11,3
	.word	49670
	.byte	20,10,180,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	30759
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,10,185,11,3
	.word	49740
	.byte	20,10,188,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	31482
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_CTRL',0,10,193,11,3
	.word	49813
	.byte	20,10,196,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	31626
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_DTV_CH',0,10,201,11,3
	.word	49879
	.byte	20,10,204,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	31775
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,10,209,11,3
	.word	49947
	.byte	20,10,212,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	31990
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_EIRQ_EN',0,10,217,11,3
	.word	50016
	.byte	20,10,220,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	32194
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_HW_CONF',0,10,225,11,3
	.word	50081
	.byte	20,10,228,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	32551
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_0',0,10,233,11,3
	.word	50146
	.byte	20,10,236,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	32679
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_2',0,10,241,11,3
	.word	50214
	.byte	20,10,244,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	32958
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_6',0,10,249,11,3
	.word	50282
	.byte	20,10,252,11,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	33803
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,10,129,12,3
	.word	50350
	.byte	20,10,132,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	34096
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,10,137,12,3
	.word	50421
	.byte	20,10,140,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	34250
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,10,145,12,3
	.word	50491
	.byte	20,10,148,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	34420
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,10,153,12,3
	.word	50568
	.byte	20,10,156,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	34761
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,10,161,12,3
	.word	50643
	.byte	20,10,164,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	34986
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_EN',0,10,169,12,3
	.word	50719
	.byte	20,10,172,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	35184
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_FORCINT',0,10,177,12,3
	.word	50783
	.byte	20,10,180,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	35380
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_MODE',0,10,185,12,3
	.word	50852
	.byte	20,10,188,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	35483
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,10,193,12,3
	.word	50918
	.byte	20,10,196,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	35661
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_KRST0',0,10,201,12,3
	.word	50986
	.byte	20,10,204,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	35772
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_KRST1',0,10,209,12,3
	.word	51049
	.byte	20,10,212,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	35864
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_KRSTCLR',0,10,217,12,3
	.word	51112
	.byte	20,10,220,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	35960
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OCS',0,10,225,12,3
	.word	51177
	.byte	20,10,228,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	36106
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_ODA',0,10,233,12,3
	.word	51238
	.byte	20,10,236,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	36212
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTBU0T',0,10,241,12,3
	.word	51299
	.byte	20,10,244,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	36343
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTBU1T',0,10,249,12,3
	.word	51363
	.byte	20,10,252,12,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	36474
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTBU2T',0,10,129,13,3
	.word	51427
	.byte	20,10,132,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	36605
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTSC0',0,10,137,13,3
	.word	51491
	.byte	20,10,140,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	36887
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_OTSS',0,10,145,13,3
	.word	51554
	.byte	20,10,148,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	37059
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_REV',0,10,153,13,3
	.word	51616
	.byte	20,10,156,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	37237
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_RST',0,10,161,13,3
	.word	51677
	.byte	20,10,164,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	37325
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,10,169,13,3
	.word	51738
	.byte	20,10,172,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	37433
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,10,177,13,3
	.word	51808
	.byte	20,10,180,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	37565
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,10,185,13,3
	.word	51878
	.byte	20,10,188,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	37673
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,10,193,13,3
	.word	51948
	.byte	20,10,196,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	37805
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,10,201,13,3
	.word	52018
	.byte	20,10,204,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	37913
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,10,209,13,3
	.word	52088
	.byte	20,10,212,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	38045
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TBU_CHEN',0,10,217,13,3
	.word	52158
	.byte	20,10,220,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	38191
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,10,225,13,3
	.word	52224
	.byte	20,10,228,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	38438
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CNT',0,10,233,13,3
	.word	52296
	.byte	20,10,236,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	38541
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,10,241,13,3
	.word	52364
	.byte	20,10,244,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	38640
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,10,249,13,3
	.word	52433
	.byte	20,10,252,13,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	39188
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,10,129,14,3
	.word	52502
	.byte	20,10,132,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	39294
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,10,137,14,3
	.word	52571
	.byte	20,10,140,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	39408
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,10,145,14,3
	.word	52641
	.byte	20,10,148,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	39663
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,10,153,14,3
	.word	52713
	.byte	20,10,156,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	39775
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,10,161,14,3
	.word	52784
	.byte	20,10,164,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	39887
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,10,169,14,3
	.word	52855
	.byte	20,10,172,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	39986
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,10,177,14,3
	.word	52924
	.byte	20,10,180,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	40085
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,10,185,14,3
	.word	52993
	.byte	20,10,188,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	40332
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,10,193,14,3
	.word	53064
	.byte	20,10,196,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	40571
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,10,201,14,3
	.word	53140
	.byte	20,10,204,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	40688
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,10,209,14,3
	.word	53213
	.byte	20,10,212,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	40901
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,10,217,14,3
	.word	53288
	.byte	20,10,220,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	41008
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,10,225,14,3
	.word	53357
	.byte	20,10,228,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	41150
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_IN_SRC',0,10,233,14,3
	.word	53426
	.byte	20,10,236,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	41495
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_INP_VAL',0,10,241,14,3
	.word	53494
	.byte	20,10,244,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	41636
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TIM_RST',0,10,249,14,3
	.word	53563
	.byte	20,10,252,14,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	41869
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CM0',0,10,129,15,3
	.word	53628
	.byte	20,10,132,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	41972
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CM1',0,10,137,15,3
	.word	53696
	.byte	20,10,140,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	42075
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CN0',0,10,145,15,3
	.word	53764
	.byte	20,10,148,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	42178
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,10,153,15,3
	.word	53832
	.byte	20,10,156,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	42506
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,10,161,15,3
	.word	53901
	.byte	20,10,164,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	42649
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,10,169,15,3
	.word	53972
	.byte	20,10,172,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	42798
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,10,177,15,3
	.word	54048
	.byte	20,10,180,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	42915
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,10,185,15,3
	.word	54121
	.byte	20,10,188,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	43052
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_SR0',0,10,193,15,3
	.word	54196
	.byte	20,10,196,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	43155
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_SR1',0,10,201,15,3
	.word	54264
	.byte	20,10,204,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	43258
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_CH_STAT',0,10,209,15,3
	.word	54332
	.byte	20,10,212,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	43361
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,10,217,15,3
	.word	54401
	.byte	20,10,220,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	43515
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,10,225,15,3
	.word	54474
	.byte	20,10,228,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	43805
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,10,233,15,3
	.word	54551
	.byte	20,10,236,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	44095
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,10,241,15,3
	.word	54628
	.byte	20,10,244,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	44528
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,10,249,15,3
	.word	54704
	.byte	20,10,252,15,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	44978
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,10,129,16,3
	.word	54779
	.byte	20,10,132,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	45248
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,10,137,16,3
	.word	54854
	.byte	20,10,140,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	45538
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,10,145,16,3
	.word	54931
	.byte	20,10,148,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	45828
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,10,153,16,3
	.word	55008
	.byte	20,10,156,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	45982
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,10,161,16,3
	.word	55081
	.byte	20,10,164,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	46272
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,10,169,16,3
	.word	55158
	.byte	20,10,172,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	46562
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,10,177,16,3
	.word	55235
	.byte	20,10,180,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	46995
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,10,185,16,3
	.word	55311
	.byte	20,10,188,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	47445
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,10,193,16,3
	.word	55386
	.byte	20,10,196,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	47715
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,10,201,16,3
	.word	55461
	.byte	20,10,204,16,9,4,5
	.byte	'U',0,4
	.word	480
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	20863
	.byte	2,35,0,5
	.byte	'B',0,4
	.word	48005
	.byte	2,35,0,0,17
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,10,209,16,3
	.word	55538
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,10,220,16,25,4,5
	.byte	'CTRL',0,4
	.word	48901
	.byte	2,35,0,0,12
	.word	55615
	.byte	17
	.byte	'Ifx_GTM_CMU_CLK0_5',0,10,223,16,3
	.word	55656
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_6',0,10,226,16,25,4,5
	.byte	'CTRL',0,4
	.word	48974
	.byte	2,35,0,0,12
	.word	55689
	.byte	17
	.byte	'Ifx_GTM_CMU_CLK_6',0,10,229,16,3
	.word	55729
	.byte	3
	.byte	'_Ifx_GTM_CMU_CLK_7',0,10,232,16,25,4,5
	.byte	'CTRL',0,4
	.word	49046
	.byte	2,35,0,0,12
	.word	55761
	.byte	17
	.byte	'Ifx_GTM_CMU_CLK_7',0,10,235,16,3
	.word	55801
	.byte	3
	.byte	'_Ifx_GTM_CMU_ECLK',0,10,238,16,25,8,5
	.byte	'NUM',0,4
	.word	49256
	.byte	2,35,0,5
	.byte	'DEN',0,4
	.word	49186
	.byte	2,35,4,0,12
	.word	55833
	.byte	17
	.byte	'Ifx_GTM_CMU_ECLK',0,10,242,16,3
	.word	55884
	.byte	3
	.byte	'_Ifx_GTM_CMU_FXCLK',0,10,245,16,25,4,5
	.byte	'CTRL',0,4
	.word	49326
	.byte	2,35,0,0,12
	.word	55915
	.byte	17
	.byte	'Ifx_GTM_CMU_FXCLK',0,10,248,16,3
	.word	55955
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,10,251,16,25,4,5
	.byte	'OUTSEL',0,4
	.word	50491
	.byte	2,35,0,0,12
	.word	55987
	.byte	17
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,10,254,16,3
	.word	56032
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_T',0,10,129,17,25,32,6,32
	.word	50568
	.byte	7,7,0,5
	.byte	'OUTSEL',0,32
	.word	56093
	.byte	2,35,0,0,12
	.word	56067
	.byte	17
	.byte	'Ifx_GTM_INOUTSEL_T',0,10,132,17,3
	.word	56119
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,10,135,17,25,32,5
	.byte	'INSEL',0,4
	.word	50643
	.byte	2,35,0,6,28
	.word	359
	.byte	7,27,0,5
	.byte	'reserved_4',0,28
	.word	56195
	.byte	2,35,4,0,12
	.word	56152
	.byte	17
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,10,139,17,3
	.word	56225
	.byte	3
	.byte	'_Ifx_GTM_TIM_CH',0,10,142,17,25,116,5
	.byte	'GPR0',0,4
	.word	52855
	.byte	2,35,0,5
	.byte	'GPR1',0,4
	.word	52924
	.byte	2,35,4,5
	.byte	'CNT',0,4
	.word	52296
	.byte	2,35,8,5
	.byte	'ECNT',0,4
	.word	52502
	.byte	2,35,12,5
	.byte	'CNTS',0,4
	.word	52364
	.byte	2,35,16,5
	.byte	'TDUC',0,4
	.word	53288
	.byte	2,35,20,5
	.byte	'TDUV',0,4
	.word	53357
	.byte	2,35,24,5
	.byte	'FLT_RE',0,4
	.word	52784
	.byte	2,35,28,5
	.byte	'FLT_FE',0,4
	.word	52713
	.byte	2,35,32,5
	.byte	'CTRL',0,4
	.word	52433
	.byte	2,35,36,5
	.byte	'ECTRL',0,4
	.word	52571
	.byte	2,35,40,5
	.byte	'IRQ_NOTIFY',0,4
	.word	53213
	.byte	2,35,44,5
	.byte	'IRQ_EN',0,4
	.word	52993
	.byte	2,35,48,5
	.byte	'IRQ_FORCINT',0,4
	.word	53064
	.byte	2,35,52,5
	.byte	'IRQ_MODE',0,4
	.word	53140
	.byte	2,35,56,5
	.byte	'EIRQ_EN',0,4
	.word	52641
	.byte	2,35,60,6,52
	.word	359
	.byte	7,51,0,5
	.byte	'reserved_40',0,52
	.word	56532
	.byte	2,35,64,0,12
	.word	56260
	.byte	17
	.byte	'Ifx_GTM_TIM_CH',0,10,161,17,3
	.word	56563
	.byte	3
	.byte	'_Ifx_GTM_TOM_CH',0,10,164,17,25,48,5
	.byte	'CTRL',0,4
	.word	53832
	.byte	2,35,0,5
	.byte	'SR0',0,4
	.word	54196
	.byte	2,35,4,5
	.byte	'SR1',0,4
	.word	54264
	.byte	2,35,8,5
	.byte	'CM0',0,4
	.word	53628
	.byte	2,35,12,5
	.byte	'CM1',0,4
	.word	53696
	.byte	2,35,16,5
	.byte	'CN0',0,4
	.word	53764
	.byte	2,35,20,5
	.byte	'STAT',0,4
	.word	54332
	.byte	2,35,24,5
	.byte	'IRQ_NOTIFY',0,4
	.word	54121
	.byte	2,35,28,5
	.byte	'IRQ_EN',0,4
	.word	53901
	.byte	2,35,32,5
	.byte	'IRQ_FORCINT',0,4
	.word	53972
	.byte	2,35,36,5
	.byte	'IRQ_MODE',0,4
	.word	54048
	.byte	2,35,40,6,4
	.word	359
	.byte	7,3,0,5
	.byte	'reserved_2C',0,4
	.word	56782
	.byte	2,35,44,0,12
	.word	56592
	.byte	17
	.byte	'Ifx_GTM_TOM_CH',0,10,178,17,3
	.word	56813
	.byte	3
	.byte	'_Ifx_GTM_BRIDGE',0,10,191,17,25,12,5
	.byte	'MODE',0,4
	.word	48633
	.byte	2,35,0,5
	.byte	'PTR1',0,4
	.word	48702
	.byte	2,35,4,5
	.byte	'PTR2',0,4
	.word	48771
	.byte	2,35,8,0,12
	.word	56842
	.byte	17
	.byte	'Ifx_GTM_BRIDGE',0,10,196,17,3
	.word	56907
	.byte	3
	.byte	'_Ifx_GTM_CMU',0,10,199,17,25,72,5
	.byte	'CLK_EN',0,4
	.word	49118
	.byte	2,35,0,5
	.byte	'GCLK_NUM',0,4
	.word	49468
	.byte	2,35,4,5
	.byte	'GCLK_DEN',0,4
	.word	49398
	.byte	2,35,8,6,24
	.word	55615
	.byte	7,5,0,12
	.word	57007
	.byte	5
	.byte	'CLK0_5',0,24
	.word	57016
	.byte	2,35,12,12
	.word	55689
	.byte	5
	.byte	'CLK_6',0,4
	.word	57037
	.byte	2,35,36,12
	.word	55761
	.byte	5
	.byte	'CLK_7',0,4
	.word	57057
	.byte	2,35,40,6,24
	.word	55833
	.byte	7,2,0,12
	.word	57077
	.byte	5
	.byte	'ECLK',0,24
	.word	57086
	.byte	2,35,44,12
	.word	55915
	.byte	5
	.byte	'FXCLK',0,4
	.word	57105
	.byte	2,35,68,0,12
	.word	56936
	.byte	17
	.byte	'Ifx_GTM_CMU',0,10,209,17,3
	.word	57126
	.byte	3
	.byte	'_Ifx_GTM_DTM',0,10,212,17,25,36,5
	.byte	'CTRL',0,4
	.word	49813
	.byte	2,35,0,5
	.byte	'CH_CTRL1',0,4
	.word	49600
	.byte	2,35,4,5
	.byte	'CH_CTRL2',0,4
	.word	49670
	.byte	2,35,8,5
	.byte	'CH_CTRL2_SR',0,4
	.word	49740
	.byte	2,35,12,5
	.byte	'PS_CTRL',0,4
	.word	49947
	.byte	2,35,16,6,16
	.word	49879
	.byte	7,3,0,5
	.byte	'DTV_CH',0,16
	.word	57259
	.byte	2,35,20,0,12
	.word	57152
	.byte	17
	.byte	'Ifx_GTM_DTM',0,10,220,17,3
	.word	57285
	.byte	3
	.byte	'_Ifx_GTM_ICM',0,10,223,17,25,60,5
	.byte	'IRQG_0',0,4
	.word	50146
	.byte	2,35,0,5
	.byte	'reserved_4',0,4
	.word	56782
	.byte	2,35,4,5
	.byte	'IRQG_2',0,4
	.word	50214
	.byte	2,35,8,6,12
	.word	359
	.byte	7,11,0,5
	.byte	'reserved_C',0,12
	.word	57382
	.byte	2,35,12,5
	.byte	'IRQG_6',0,4
	.word	50282
	.byte	2,35,24,6,20
	.word	359
	.byte	7,19,0,5
	.byte	'reserved_1C',0,20
	.word	57427
	.byte	2,35,28,5
	.byte	'IRQG_MEI',0,4
	.word	50421
	.byte	2,35,48,5
	.byte	'reserved_34',0,4
	.word	56782
	.byte	2,35,52,5
	.byte	'IRQG_CEI1',0,4
	.word	50350
	.byte	2,35,56,0,12
	.word	57311
	.byte	17
	.byte	'Ifx_GTM_ICM',0,10,234,17,3
	.word	57516
	.byte	3
	.byte	'_Ifx_GTM_INOUTSEL',0,10,237,17,25,148,1,6,32
	.word	56152
	.byte	7,0,0,12
	.word	57567
	.byte	5
	.byte	'TIM',0,32
	.word	57576
	.byte	2,35,0,12
	.word	56067
	.byte	5
	.byte	'T',0,32
	.word	57594
	.byte	2,35,32,6,80
	.word	359
	.byte	7,79,0,5
	.byte	'reserved_40',0,80
	.word	57610
	.byte	2,35,64,12
	.word	55987
	.byte	5
	.byte	'CAN',0,4
	.word	57640
	.byte	3,35,144,1,0,12
	.word	57542
	.byte	17
	.byte	'Ifx_GTM_INOUTSEL',0,10,243,17,3
	.word	57660
	.byte	3
	.byte	'_Ifx_GTM_TBU',0,10,246,17,25,28,5
	.byte	'CHEN',0,4
	.word	52158
	.byte	2,35,0,5
	.byte	'CH0_CTRL',0,4
	.word	51808
	.byte	2,35,4,5
	.byte	'CH0_BASE',0,4
	.word	51738
	.byte	2,35,8,5
	.byte	'CH1_CTRL',0,4
	.word	51948
	.byte	2,35,12,5
	.byte	'CH1_BASE',0,4
	.word	51878
	.byte	2,35,16,5
	.byte	'CH2_CTRL',0,4
	.word	52088
	.byte	2,35,20,5
	.byte	'CH2_BASE',0,4
	.word	52018
	.byte	2,35,24,0,12
	.word	57691
	.byte	17
	.byte	'Ifx_GTM_TBU',0,10,255,17,3
	.word	57833
	.byte	3
	.byte	'_Ifx_GTM_TIM',0,10,130,18,25,128,8,12
	.word	56260
	.byte	5
	.byte	'CH0',0,116
	.word	57879
	.byte	2,35,0,5
	.byte	'INP_VAL',0,4
	.word	53494
	.byte	2,35,116,5
	.byte	'IN_SRC',0,4
	.word	53426
	.byte	2,35,120,5
	.byte	'RST',0,4
	.word	53563
	.byte	2,35,124,12
	.word	56260
	.byte	5
	.byte	'CH1',0,116
	.word	57943
	.byte	3,35,128,1,5
	.byte	'reserved_F4',0,12
	.word	57382
	.byte	3,35,244,1,12
	.word	56260
	.byte	5
	.byte	'CH2',0,116
	.word	57984
	.byte	3,35,128,2,5
	.byte	'reserved_174',0,12
	.word	57382
	.byte	3,35,244,2,12
	.word	56260
	.byte	5
	.byte	'CH3',0,116
	.word	58026
	.byte	3,35,128,3,5
	.byte	'reserved_1F4',0,12
	.word	57382
	.byte	3,35,244,3,12
	.word	56260
	.byte	5
	.byte	'CH4',0,116
	.word	58068
	.byte	3,35,128,4,5
	.byte	'reserved_274',0,12
	.word	57382
	.byte	3,35,244,4,12
	.word	56260
	.byte	5
	.byte	'CH5',0,116
	.word	58110
	.byte	3,35,128,5,5
	.byte	'reserved_2F4',0,12
	.word	57382
	.byte	3,35,244,5,12
	.word	56260
	.byte	5
	.byte	'CH6',0,116
	.word	58152
	.byte	3,35,128,6,5
	.byte	'reserved_374',0,12
	.word	57382
	.byte	3,35,244,6,12
	.word	56260
	.byte	5
	.byte	'CH7',0,116
	.word	58194
	.byte	3,35,128,7,5
	.byte	'reserved_3F4',0,12
	.word	57382
	.byte	3,35,244,7,0,12
	.word	57859
	.byte	17
	.byte	'Ifx_GTM_TIM',0,10,150,18,3
	.word	58237
	.byte	3
	.byte	'_Ifx_GTM_TOM',0,10,153,18,25,128,16,12
	.word	56592
	.byte	5
	.byte	'CH0',0,48
	.word	58283
	.byte	2,35,0,5
	.byte	'TGC0_GLB_CTRL',0,4
	.word	54704
	.byte	2,35,48,5
	.byte	'TGC0_ACT_TB',0,4
	.word	54401
	.byte	2,35,52,5
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	54628
	.byte	2,35,56,5
	.byte	'TGC0_INT_TRIG',0,4
	.word	54779
	.byte	2,35,60,12
	.word	56592
	.byte	5
	.byte	'CH1',0,48
	.word	58392
	.byte	2,35,64,5
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	54474
	.byte	2,35,112,5
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	54551
	.byte	2,35,116,5
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	54854
	.byte	2,35,120,5
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	54931
	.byte	2,35,124,12
	.word	56592
	.byte	5
	.byte	'CH2',0,48
	.word	58510
	.byte	3,35,128,1,6,16
	.word	359
	.byte	7,15,0,5
	.byte	'reserved_B0',0,16
	.word	58529
	.byte	3,35,176,1,12
	.word	56592
	.byte	5
	.byte	'CH3',0,48
	.word	58560
	.byte	3,35,192,1,5
	.byte	'reserved_F0',0,16
	.word	58529
	.byte	3,35,240,1,12
	.word	56592
	.byte	5
	.byte	'CH4',0,48
	.word	58601
	.byte	3,35,128,2,5
	.byte	'reserved_130',0,16
	.word	58529
	.byte	3,35,176,2,12
	.word	56592
	.byte	5
	.byte	'CH5',0,48
	.word	58643
	.byte	3,35,192,2,5
	.byte	'reserved_170',0,16
	.word	58529
	.byte	3,35,240,2,12
	.word	56592
	.byte	5
	.byte	'CH6',0,48
	.word	58685
	.byte	3,35,128,3,5
	.byte	'reserved_1B0',0,16
	.word	58529
	.byte	3,35,176,3,12
	.word	56592
	.byte	5
	.byte	'CH7',0,48
	.word	58727
	.byte	3,35,192,3,5
	.byte	'reserved_1F0',0,16
	.word	58529
	.byte	3,35,240,3,12
	.word	56592
	.byte	5
	.byte	'CH8',0,48
	.word	58769
	.byte	3,35,128,4,5
	.byte	'TGC1_GLB_CTRL',0,4
	.word	55311
	.byte	3,35,176,4,5
	.byte	'TGC1_ACT_TB',0,4
	.word	55008
	.byte	3,35,180,4,5
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	55235
	.byte	3,35,184,4,5
	.byte	'TGC1_INT_TRIG',0,4
	.word	55386
	.byte	3,35,188,4,12
	.word	56592
	.byte	5
	.byte	'CH9',0,48
	.word	58883
	.byte	3,35,192,4,5
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	55081
	.byte	3,35,240,4,5
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	55158
	.byte	3,35,244,4,5
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	55461
	.byte	3,35,248,4,5
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	55538
	.byte	3,35,252,4,12
	.word	56592
	.byte	5
	.byte	'CH10',0,48
	.word	59006
	.byte	3,35,128,5,5
	.byte	'reserved_2B0',0,16
	.word	58529
	.byte	3,35,176,5,12
	.word	56592
	.byte	5
	.byte	'CH11',0,48
	.word	59049
	.byte	3,35,192,5,5
	.byte	'reserved_2F0',0,16
	.word	58529
	.byte	3,35,240,5,12
	.word	56592
	.byte	5
	.byte	'CH12',0,48
	.word	59092
	.byte	3,35,128,6,5
	.byte	'reserved_330',0,16
	.word	58529
	.byte	3,35,176,6,12
	.word	56592
	.byte	5
	.byte	'CH13',0,48
	.word	59135
	.byte	3,35,192,6,5
	.byte	'reserved_370',0,16
	.word	58529
	.byte	3,35,240,6,12
	.word	56592
	.byte	5
	.byte	'CH14',0,48
	.word	59178
	.byte	3,35,128,7,5
	.byte	'reserved_3B0',0,16
	.word	58529
	.byte	3,35,176,7,12
	.word	56592
	.byte	5
	.byte	'CH15',0,48
	.word	59221
	.byte	3,35,192,7,6,144,8
	.word	359
	.byte	7,143,8,0,5
	.byte	'reserved_3F0',0,144,8
	.word	59241
	.byte	3,35,240,7,0,12
	.word	58263
	.byte	17
	.byte	'Ifx_GTM_TOM',0,10,199,18,3
	.word	59277
	.byte	8,3,130,4,20,64,5
	.byte	'CTRL',0,4
	.word	53832
	.byte	2,35,0,5
	.byte	'SR0',0,4
	.word	54196
	.byte	2,35,4,5
	.byte	'SR1',0,4
	.word	54264
	.byte	2,35,8,5
	.byte	'CM0',0,4
	.word	53628
	.byte	2,35,12,5
	.byte	'CM1',0,4
	.word	53696
	.byte	2,35,16,5
	.byte	'CN0',0,4
	.word	53764
	.byte	2,35,20,5
	.byte	'STAT',0,4
	.word	54332
	.byte	2,35,24,5
	.byte	'IRQ_NOTIFY',0,4
	.word	54121
	.byte	2,35,28,5
	.byte	'IRQ_EN',0,4
	.word	53901
	.byte	2,35,32,5
	.byte	'IRQ_FORCINT',0,4
	.word	53972
	.byte	2,35,36,5
	.byte	'IRQ_MODE',0,4
	.word	54048
	.byte	2,35,40,6,20
	.word	359
	.byte	7,19,0,5
	.byte	'reserved_2C',0,20
	.word	59477
	.byte	2,35,44,0,12
	.word	59303
	.byte	17
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,3,155,4,4
	.word	59508
	.byte	8,3,157,4,20,128,4,5
	.byte	'GLB_CTRL',0,4
	.word	54704
	.byte	2,35,0,5
	.byte	'ACT_TB',0,4
	.word	54401
	.byte	2,35,4,5
	.byte	'FUPD_CTRL',0,4
	.word	54628
	.byte	2,35,8,5
	.byte	'INT_TRIG',0,4
	.word	54779
	.byte	2,35,12,6,48
	.word	359
	.byte	7,47,0,5
	.byte	'reserved_tgc0',0,48
	.word	59620
	.byte	2,35,16,5
	.byte	'ENDIS_CTRL',0,4
	.word	54474
	.byte	2,35,64,5
	.byte	'ENDIS_STAT',0,4
	.word	54551
	.byte	2,35,68,5
	.byte	'OUTEN_CTRL',0,4
	.word	54854
	.byte	2,35,72,5
	.byte	'OUTEN_STAT',0,4
	.word	54931
	.byte	2,35,76,6,176,3
	.word	359
	.byte	7,175,3,0,5
	.byte	'reserved_tgc1',0,176,3
	.word	59732
	.byte	2,35,80,0,12
	.word	59542
	.byte	17
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,3,177,4,5
	.word	59768
	.byte	8,3,179,4,20,128,16,5
	.byte	'reserved_tom0',0,48
	.word	59620
	.byte	2,35,0,6,128,8
	.word	59542
	.byte	7,1,0,12
	.word	59833
	.byte	5
	.byte	'TGC',0,128,8
	.word	59843
	.byte	2,35,48,6,208,7
	.word	359
	.byte	7,207,7,0,5
	.byte	'reserved_tgc2',0,208,7
	.word	59862
	.byte	3,35,176,8,0,12
	.word	59803
	.byte	17
	.byte	'Ifx_GTM_TOM_TGCx',0,3,184,4,5
	.word	59899
	.byte	8,3,187,4,20,128,16,6,128,8
	.word	59303
	.byte	7,15,0,12
	.word	59937
	.byte	5
	.byte	'CH',0,128,8
	.word	59947
	.byte	2,35,0,6,128,8
	.word	359
	.byte	7,255,7,0,5
	.byte	'reserved_tom1',0,128,8
	.word	59965
	.byte	3,35,128,8,0,12
	.word	59930
	.byte	17
	.byte	'Ifx_GTM_TOM_CHx',0,3,191,4,5
	.word	60002
	.byte	8,3,212,4,20,128,1,5
	.byte	'CH_GPR0',0,4
	.word	52855
	.byte	2,35,0,5
	.byte	'CH_GPR1',0,4
	.word	52924
	.byte	2,35,4,5
	.byte	'CH_CNT',0,4
	.word	52296
	.byte	2,35,8,5
	.byte	'CH_ECNT',0,4
	.word	52502
	.byte	2,35,12,5
	.byte	'CH_CNTS',0,4
	.word	52364
	.byte	2,35,16,5
	.byte	'CH_TDUC',0,4
	.word	53288
	.byte	2,35,20,5
	.byte	'CH_TDUV',0,4
	.word	53357
	.byte	2,35,24,5
	.byte	'CH_FLT_RE',0,4
	.word	52784
	.byte	2,35,28,5
	.byte	'CH_FLT_FE',0,4
	.word	52713
	.byte	2,35,32,5
	.byte	'CH_CTRL',0,4
	.word	52433
	.byte	2,35,36,5
	.byte	'CH_ECTRL',0,4
	.word	52571
	.byte	2,35,40,5
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	53213
	.byte	2,35,44,5
	.byte	'CH_IRQ_EN',0,4
	.word	52993
	.byte	2,35,48,5
	.byte	'CH_IRQ_FORCINT',0,4
	.word	53064
	.byte	2,35,52,5
	.byte	'CH_IRQ_MODE',0,4
	.word	53140
	.byte	2,35,56,5
	.byte	'CH_EIRQ_EN',0,4
	.word	52641
	.byte	2,35,60,6,64
	.word	359
	.byte	7,63,0,5
	.byte	'reserved_40',0,64
	.word	60337
	.byte	2,35,64,0,12
	.word	60032
	.byte	17
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,3,248,4,4
	.word	60368
	.byte	8,3,250,4,20,8,5
	.byte	'IN_SRC',0,4
	.word	53426
	.byte	2,35,0,5
	.byte	'RST',0,4
	.word	53563
	.byte	2,35,4,0,12
	.word	60402
	.byte	17
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,3,255,4,4
	.word	60438
	.byte	8,3,129,5,21,128,16,6,128,8
	.word	60032
	.byte	7,7,0,12
	.word	60489
	.byte	5
	.byte	'CH',0,128,8
	.word	60499
	.byte	2,35,0,5
	.byte	'reserved_tim1',0,128,8
	.word	59965
	.byte	3,35,128,8,0,12
	.word	60482
	.byte	17
	.byte	'Ifx_GTM_TIM_CHx',0,3,133,5,4
	.word	60543
	.byte	8,3,135,5,20,128,16,6,120
	.word	359
	.byte	7,119,0,5
	.byte	'reserved_tim2',0,120
	.word	60580
	.byte	2,35,0,12
	.word	60402
	.byte	5
	.byte	'IN_SRC_RESET',0,8
	.word	60612
	.byte	2,35,120,6,128,15
	.word	359
	.byte	7,255,14,0,5
	.byte	'reserved_tim3',0,128,15
	.word	60639
	.byte	3,35,128,1,0,12
	.word	60573
	.byte	17
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,3,140,5,4
	.word	60676
	.byte	21,3,174,5,11,1,22
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,22
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,22
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,22
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,22
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,22
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,22
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,22
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,17
	.byte	'Gtm_ConfigurableClockType',0,3,184,5,4
	.word	60714
	.byte	21,3,188,5,11,1,22
	.byte	'GTM_LOW',0,0,22
	.byte	'GTM_HIGH',0,1,0,17
	.byte	'Gtm_OutputLevelType',0,3,192,5,4
	.word	60948
	.byte	21,3,195,5,11,1,22
	.byte	'TOM_GLB_CTRL',0,0,22
	.byte	'TOM_ACT_TB',0,1,22
	.byte	'TOM_FUPD_CTRL',0,2,22
	.byte	'TOM_INT_TRIG',0,3,22
	.byte	'TOM_RESERVED_0',0,4,22
	.byte	'TOM_RESERVED_1',0,5,22
	.byte	'TOM_RESERVED_2',0,6,22
	.byte	'TOM_RESERVED_3',0,7,22
	.byte	'TOM_RESERVED_4',0,8,22
	.byte	'TOM_RESERVED_5',0,9,22
	.byte	'TOM_RESERVED_6',0,10,22
	.byte	'TOM_RESERVED_7',0,11,22
	.byte	'TOM_RESERVED_8',0,12,22
	.byte	'TOM_RESERVED_9',0,13,22
	.byte	'TOM_RESERVED_10',0,14,22
	.byte	'TOM_RESERVED_11',0,15,22
	.byte	'TOM_ENDIS_CTRL',0,16,22
	.byte	'TOM_ENDIS_STAT',0,17,22
	.byte	'TOM_OUTEN_CTRL',0,18,22
	.byte	'TOM_OUTEN_STAT',0,19,0,17
	.byte	'Gtm_TomTimerRegistersType',0,3,217,5,4
	.word	61005
	.byte	8,3,221,5,11,8,5
	.byte	'FltRisingEdge',0,4
	.word	173
	.byte	2,35,0,5
	.byte	'FltFallingEdge',0,4
	.word	173
	.byte	2,35,4,0,17
	.byte	'Gtm_TimFilterType',0,3,225,5,4
	.word	61380
	.byte	17
	.byte	'Gtm_TbuChCtrlType',0,3,230,5,32
	.word	51808
	.byte	17
	.byte	'Gtm_TbuChBaseType',0,3,231,5,32
	.word	51738
	.byte	8,3,233,5,11,8,5
	.byte	'CH_CTRL',0,4
	.word	51808
	.byte	2,35,0,5
	.byte	'CH_BASE',0,4
	.word	51738
	.byte	2,35,4,0,17
	.byte	'Gtm_TbuChType',0,3,237,5,4
	.word	61515
	.byte	17
	.byte	'Gtm_PortConfigType',0,3,253,5,2
	.word	1174
	.byte	17
	.byte	'Gtm_TimFltType',0,3,134,6,2
	.word	1562
	.byte	17
	.byte	'Gtm_TimConfigType',0,3,151,6,4
	.word	1455
	.byte	17
	.byte	'Gtm_ModUsageConfigType',0,3,163,6,4
	.word	2419
	.byte	17
	.byte	'Gtm_TomTgcConfigGroupType',0,3,185,6,2
	.word	1834
	.byte	17
	.byte	'Gtm_TomTgcConfigType',0,3,196,6,2
	.word	1784
	.byte	17
	.byte	'Gtm_TomChannelConfigType',0,3,207,6,2
	.word	2196
	.byte	17
	.byte	'Gtm_TomConfigType',0,3,219,6,2
	.word	2122
	.byte	17
	.byte	'Gtm_ExtClkType',0,3,227,6,2
	.word	1062
	.byte	17
	.byte	'Gtm_ClockSettingType',0,3,236,6,2
	.word	984
	.byte	17
	.byte	'Gtm_GeneralConfigType',0,3,245,6,2
	.word	2528
	.byte	17
	.byte	'Gtm_TbuConfigType',0,3,253,6,2
	.word	2618
	.byte	17
	.byte	'Gtm_ModuleConfigType',0,3,163,7,2
	.word	1261
	.byte	23,1,1,24
	.word	359
	.byte	24
	.word	359
	.byte	24
	.word	359
	.byte	24
	.word	220
	.byte	0,10
	.word	61958
	.byte	17
	.byte	'Gtm_NotificationPtrType',0,3,172,7,16
	.word	61982
	.byte	17
	.byte	'Gtm_ConfigType',0,3,197,7,2
	.word	963
	.byte	17
	.byte	'Mcu_ClockType',0,2,156,3,18
	.word	173
	.byte	17
	.byte	'Mcu_ModeType',0,2,162,3,18
	.word	173
	.byte	17
	.byte	'Mcu_RamSectionType',0,2,168,3,18
	.word	173
	.byte	17
	.byte	'Mcu_RamBaseAdrType',0,2,178,3,18
	.word	3678
	.byte	17
	.byte	'Mcu_RamSizeType',0,2,181,3,17
	.word	173
	.byte	17
	.byte	'Mcu_RamPrstDatType',0,2,184,3,16
	.word	359
	.byte	17
	.byte	'Mcu_ClockCfgType',0,2,162,4,2
	.word	336
	.byte	17
	.byte	'Mcu_StandbyModeType',0,2,175,4,2
	.word	2925
	.byte	17
	.byte	'Mcu_ConfigType',0,2,212,4,2
	.word	315
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,3,8,58,15,59,15,57,15
	.byte	11,15,0,0,4,38,0,73,19,0,0,5,13,0,3,8,11,15,73,19,56,9,0,0,6,1,1,11,15,73,19,0,0,7,33,0,47,15,0,0,8,19
	.byte	1,58,15,59,15,57,15,11,15,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,15,0,73,19,0,0,11,19,0,3
	.byte	8,58,15,59,15,57,15,60,12,0,0,12,53,0,73,19,0,0,13,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,14,5,0,3,8,58,15,59,15,57,15,73,19,0,0,15,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,16
	.byte	59,0,3,8,0,0,17,22,0,3,8,58,15,59,15,57,15,73,19,0,0,18,21,0,54,15,0,0,19,52,0,3,8,58,15,59,15,57,15,73
	.byte	19,63,12,60,12,0,0,20,23,1,58,15,59,15,57,15,11,15,0,0,21,4,1,58,15,59,15,57,15,11,15,0,0,22,40,0,3,8
	.byte	28,13,0,0,23,21,1,54,15,39,12,0,0,24,5,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L14:
	.word	.L56-.L55
.L55:
	.half	3
	.word	.L58-.L57
.L57:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg',0
	.byte	0
	.byte	'EcuM_Cfg.h',0,1,0,0
	.byte	'..\\mcal_src\\Mcu.h',0,0,0,0
	.byte	'..\\mcal_src\\Gtm.h',0,0,0,0
	.byte	'..\\mcal_src\\EcuM.h',0,0,0,0
	.byte	'..\\mcal_src\\EcuM.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\EcuM_Cbk.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_TcLib.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxGtm_regdef.h',0,0,0,0,0
.L58:
.L56:
	.sdecl	'.debug_info',debug,cluster('EcuM_SetWakeupEvent')
	.sect	'.debug_info'
.L15:
	.word	253
	.half	3
	.word	.L16
	.byte	4,1
	.byte	'..\\mcal_src\\EcuM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L18,.L17
	.byte	2
	.word	.L11
	.byte	3
	.byte	'EcuM_SetWakeupEvent',0,1,167,1,6,1,1,1
	.word	.L6,.L35,.L5
	.byte	4
	.byte	'WakeupInfo',0,1,167,1,48
	.word	.L36,.L37
	.byte	5
	.word	.L6,.L35
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_SetWakeupEvent')
	.sect	'.debug_abbrev'
.L16:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EcuM_SetWakeupEvent')
	.sect	'.debug_line'
.L17:
	.word	.L60-.L59
.L59:
	.half	3
	.word	.L62-.L61
.L61:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\EcuM.c',0,0,0,0,0
.L62:
	.byte	5,1,7,0,5,2
	.word	.L6
	.byte	3,169,1,1,7,9
	.half	.L19-.L6
	.byte	0,1,1
.L60:
	.sdecl	'.debug_ranges',debug,cluster('EcuM_SetWakeupEvent')
	.sect	'.debug_ranges'
.L18:
	.word	-1,.L6,0,.L19-.L6,0,0
	.sdecl	'.debug_info',debug,cluster('EcuM_ValidateWakeupEvent')
	.sect	'.debug_info'
.L20:
	.word	254
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\mcal_src\\EcuM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L23,.L22
	.byte	2
	.word	.L11
	.byte	3
	.byte	'EcuM_ValidateWakeupEvent',0,1,190,1,6,1,1,1
	.word	.L8,.L38,.L7
	.byte	4
	.byte	'events',0,1,190,1,53
	.word	.L36,.L39
	.byte	5
	.word	.L8,.L38
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_ValidateWakeupEvent')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EcuM_ValidateWakeupEvent')
	.sect	'.debug_line'
.L22:
	.word	.L64-.L63
.L63:
	.half	3
	.word	.L66-.L65
.L65:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\EcuM.c',0,0,0,0,0
.L66:
	.byte	5,1,7,0,5,2
	.word	.L8
	.byte	3,192,1,1,7,9
	.half	.L24-.L8
	.byte	0,1,1
.L64:
	.sdecl	'.debug_ranges',debug,cluster('EcuM_ValidateWakeupEvent')
	.sect	'.debug_ranges'
.L23:
	.word	-1,.L8,0,.L24-.L8,0,0
	.sdecl	'.debug_info',debug,cluster('EcuM_CheckWakeup')
	.sect	'.debug_info'
.L25:
	.word	252
	.half	3
	.word	.L26
	.byte	4,1
	.byte	'..\\mcal_src\\EcuM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L28,.L27
	.byte	2
	.word	.L11
	.byte	3
	.byte	'EcuM_CheckWakeup',0,1,214,1,6,1,1,1
	.word	.L10,.L40,.L9
	.byte	4
	.byte	'wakeupSource',0,1,214,1,45
	.word	.L36,.L41
	.byte	5
	.word	.L10,.L40
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_CheckWakeup')
	.sect	'.debug_abbrev'
.L26:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('EcuM_CheckWakeup')
	.sect	'.debug_line'
.L27:
	.word	.L68-.L67
.L67:
	.half	3
	.word	.L70-.L69
.L69:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\EcuM.c',0,0,0,0,0
.L70:
	.byte	5,1,7,0,5,2
	.word	.L10
	.byte	3,216,1,1,7,9
	.half	.L29-.L10
	.byte	0,1,1
.L68:
	.sdecl	'.debug_ranges',debug,cluster('EcuM_CheckWakeup')
	.sect	'.debug_ranges'
.L28:
	.word	-1,.L10,0,.L29-.L10,0,0
	.sdecl	'.debug_info',debug,cluster('EcuM_Init')
	.sect	'.debug_info'
.L30:
	.word	263
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'..\\mcal_src\\EcuM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L33,.L32
	.byte	2
	.word	.L11
	.byte	3
	.byte	'EcuM_Init',0,1,95,6,1,1,1
	.word	.L4,.L42,.L3
	.byte	4
	.byte	'configptr',0,1,95,39
	.word	.L43,.L44
	.byte	5
	.word	.L4,.L42
	.byte	6
	.byte	'ConfError',0,1,97,19
	.word	.L45,.L46
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_Init')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('EcuM_Init')
	.sect	'.debug_line'
.L32:
	.word	.L72-.L71
.L71:
	.half	3
	.word	.L74-.L73
.L73:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\EcuM.c',0,0,0,0,0
.L74:
	.byte	5,6,7,0,5,2
	.word	.L4
	.byte	3,222,0,1,5,15,9
	.half	.L49-.L4
	.byte	3,4,1,5,13,1,5,6,9
	.half	.L75-.L49
	.byte	3,4,1,5,46,9
	.half	.L76-.L75
	.byte	1,5,3,9
	.half	.L77-.L76
	.byte	1,5,15,7,9
	.half	.L78-.L77
	.byte	3,3,1,5,14,1,5,7,9
	.half	.L2-.L78
	.byte	3,2,1,5,25,9
	.half	.L79-.L2
	.byte	3,14,1,5,21,9
	.half	.L48-.L79
	.byte	3,7,1,5,25,9
	.half	.L80-.L48
	.byte	3,4,1,9
	.half	.L51-.L80
	.byte	3,7,1,5,27,9
	.half	.L53-.L51
	.byte	3,2,1,5,1,9
	.half	.L34-.L53
	.byte	3,6,0,1,1
.L72:
	.sdecl	'.debug_ranges',debug,cluster('EcuM_Init')
	.sect	'.debug_ranges'
.L33:
	.word	-1,.L4,0,.L34-.L4,0,0
	.sdecl	'.debug_loc',debug,cluster('EcuM_CheckWakeup')
	.sect	'.debug_loc'
.L9:
	.word	-1,.L10,0,.L40-.L10
	.half	2
	.byte	138,0
	.word	0,0
.L41:
	.word	-1,.L10,0,.L40-.L10
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EcuM_Init')
	.sect	'.debug_loc'
.L46:
	.word	-1,.L4,0,.L42-.L4
	.half	2
	.byte	145,120
	.word	0,0
.L3:
	.word	-1,.L4,0,.L47-.L4
	.half	2
	.byte	138,0
	.word	.L47-.L4,.L42-.L4
	.half	2
	.byte	138,8
	.word	.L42-.L4,.L42-.L4
	.half	2
	.byte	138,0
	.word	0,0
.L44:
	.word	-1,.L4,0,.L48-.L4
	.half	1
	.byte	100
	.word	.L49-.L4,.L42-.L4
	.half	1
	.byte	111
	.word	.L50-.L4,.L51-.L4
	.half	1
	.byte	100
	.word	.L52-.L4,.L53-.L4
	.half	1
	.byte	100
	.word	.L54-.L4,.L42-.L4
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EcuM_SetWakeupEvent')
	.sect	'.debug_loc'
.L5:
	.word	-1,.L6,0,.L35-.L6
	.half	2
	.byte	138,0
	.word	0,0
.L37:
	.word	-1,.L6,0,.L35-.L6
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('EcuM_ValidateWakeupEvent')
	.sect	'.debug_loc'
.L7:
	.word	-1,.L8,0,.L38-.L8
	.half	2
	.byte	138,0
	.word	0,0
.L39:
	.word	-1,.L8,0,.L38-.L8
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L81:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('EcuM_Init')
	.sect	'.debug_frame'
	.word	36
	.word	.L81,.L4,.L42-.L4
	.byte	4
	.word	(.L47-.L4)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L42-.L47)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('EcuM_SetWakeupEvent')
	.sect	'.debug_frame'
	.word	24
	.word	.L81,.L6,.L35-.L6
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('EcuM_ValidateWakeupEvent')
	.sect	'.debug_frame'
	.word	24
	.word	.L81,.L8,.L38-.L8
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('EcuM_CheckWakeup')
	.sect	'.debug_frame'
	.word	24
	.word	.L81,.L10,.L40-.L10
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\EcuM.c	   218  #define ECUM_STOP_SEC_CODE
; ..\mcal_src\EcuM.c	   219  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives 
; ..\mcal_src\EcuM.c	   220    is allowed only for MemMap.h*/
; ..\mcal_src\EcuM.c	   221  #include "MemMap.h"

	; Module end
