	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc30792a -c99 --dep-file=mcal_src\\.Spi_Irq.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Spi_Irq.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Spi_Irq.src ..\\mcal_src\\Spi_Irq.c"
	.compiler_name		"ctc"
	.name	"Spi_Irq"

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	57888
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_src\\Spi_Irq.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	176
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	182
	.byte	5,1,3
	.word	206
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	208
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	231
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	262
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	299
	.byte	7
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,3,45,16,4,8
	.byte	'EN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN00_Bits',0,3,79,3
	.word	335
	.byte	7
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,3,82,16,4,6
	.byte	'unsigned int',0,4,7,8
	.byte	'reserved_0',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN01_Bits',0,3,85,3
	.word	894
	.byte	7
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,3,88,16,4,8
	.byte	'EN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN10_Bits',0,3,122,3
	.word	989
	.byte	7
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,3,125,16,4,8
	.byte	'reserved_0',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN11_Bits',0,3,128,1,3
	.word	1548
	.byte	7
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,3,131,1,16,4,8
	.byte	'EN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN20_Bits',0,3,165,1,3
	.word	1628
	.byte	7
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,3,168,1,16,4,8
	.byte	'reserved_0',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN21_Bits',0,3,171,1,3
	.word	2189
	.byte	7
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,3,174,1,16,4,8
	.byte	'EN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN30_Bits',0,3,208,1,3
	.word	2270
	.byte	7
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,3,211,1,16,4,8
	.byte	'reserved_0',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN31_Bits',0,3,214,1,3
	.word	2831
	.byte	7
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,3,217,1,16,4,8
	.byte	'reserved_0',0,2
	.word	262
	.byte	16,0,2,35,0,8
	.byte	'CSER',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'CDER',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	2,4,2,35,2,8
	.byte	'CSPBER',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'CSRIER',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'CRAMER',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'CSLLER',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'CDLLER',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	231
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,3,230,1,3
	.word	2912
	.byte	7
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,3,233,1,16,4,8
	.byte	'reserved_0',0,2
	.word	262
	.byte	16,0,2,35,0,8
	.byte	'ESER',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'EDER',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	6,0,2,35,2,8
	.byte	'ERER',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'ELER',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	231
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_EER_Bits',0,3,243,1,3
	.word	3186
	.byte	7
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,3,246,1,16,4,8
	.byte	'LEC',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'reserved_7',0,2
	.word	262
	.byte	9,0,2,35,0,8
	.byte	'SER',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'DER',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	2,4,2,35,2,8
	.byte	'SPBER',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'SRIER',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'RAMER',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'SLLER',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'DLLER',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	231
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,3,132,2,3
	.word	3400
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,3,135,2,16,4,8
	.byte	'SMF',0,1
	.word	231
	.byte	3,5,2,35,0,8
	.byte	'INCS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'DMF',0,1
	.word	231
	.byte	3,1,2,35,0,8
	.byte	'INCD',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'CBLS',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'CBLD',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'SHCT',0,1
	.word	231
	.byte	4,4,2,35,2,8
	.byte	'SCBE',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'DCBE',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'STAMP',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'ETRL',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'WRPSE',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'WRPDE',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'INTCT',0,1
	.word	231
	.byte	2,4,2,35,3,8
	.byte	'IRDV',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,3,152,2,3
	.word	3684
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,3,155,2,16,4,8
	.byte	'TREL',0,2
	.word	262
	.byte	14,2,2,35,0,8
	.byte	'reserved_14',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'BLKM',0,1
	.word	231
	.byte	3,5,2,35,2,8
	.byte	'RROAT',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'CHMODE',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'CHDW',0,1
	.word	231
	.byte	3,0,2,35,2,8
	.byte	'PATSEL',0,1
	.word	231
	.byte	3,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'PRSEL',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'reserved_29',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'DMAPRIO',0,1
	.word	231
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,3,168,2,3
	.word	3995
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,3,171,2,16,4,8
	.byte	'TCOUNT',0,2
	.word	262
	.byte	14,2,2,35,0,8
	.byte	'reserved_14',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'LXO',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'WRPS',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'WRPD',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'ICH',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'IPM',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'reserved_20',0,1
	.word	231
	.byte	2,2,2,35,2,8
	.byte	'BUFFER',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'FROZEN',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,3,184,2,3
	.word	4268
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,3,187,2,16,4,8
	.byte	'DADR',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,3,190,2,3
	.word	4535
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,3,193,2,16,4,8
	.byte	'RD00',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'RD01',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'RD02',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'RD03',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,3,199,2,3
	.word	4618
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,3,202,2,16,4,8
	.byte	'RD10',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'RD11',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'RD12',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'RD13',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,3,208,2,3
	.word	4745
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,3,211,2,16,4,8
	.byte	'RD20',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'RD21',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'RD22',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'RD23',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,3,217,2,3
	.word	4872
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,3,220,2,16,4,8
	.byte	'RD30',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'RD31',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'RD32',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'RD33',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,3,226,2,3
	.word	4999
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,3,229,2,16,4,8
	.byte	'RD40',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'RD41',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'RD42',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'RD43',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,3,235,2,3
	.word	5126
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,3,238,2,16,4,8
	.byte	'RD50',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'RD51',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'RD52',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'RD53',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,3,244,2,3
	.word	5253
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,3,247,2,16,4,8
	.byte	'RD60',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'RD61',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'RD62',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'RD63',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,3,253,2,3
	.word	5380
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,3,128,3,16,4,8
	.byte	'RD70',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'RD71',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'RD72',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'RD73',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,3,134,3,3
	.word	5507
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,3,137,3,16,4,8
	.byte	'RDCRC',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,3,140,3,3
	.word	5634
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,3,143,3,16,4,8
	.byte	'SADR',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,3,146,3,3
	.word	5720
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,3,149,3,16,4,8
	.byte	'SDCRC',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,3,152,3,3
	.word	5803
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,3,155,3,16,4,8
	.byte	'SHADR',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,3,158,3,3
	.word	5889
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,3,161,3,16,4,8
	.byte	'RS',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	3,4,2,35,0,8
	.byte	'WS',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'reserved_5',0,2
	.word	262
	.byte	11,0,2,35,0,8
	.byte	'CH',0,1
	.word	231
	.byte	7,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	262
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,3,169,3,3
	.word	5975
	.byte	7
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,3,172,3,16,4,8
	.byte	'SMF',0,1
	.word	231
	.byte	3,5,2,35,0,8
	.byte	'INCS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'DMF',0,1
	.word	231
	.byte	3,1,2,35,0,8
	.byte	'INCD',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'CBLS',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'CBLD',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'SHCT',0,1
	.word	231
	.byte	4,4,2,35,2,8
	.byte	'SCBE',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'DCBE',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'STAMP',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'ETRL',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'WRPSE',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'WRPDE',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'INTCT',0,1
	.word	231
	.byte	2,4,2,35,3,8
	.byte	'IRDV',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,3,189,3,3
	.word	6147
	.byte	7
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,3,192,3,16,4,8
	.byte	'TREL',0,2
	.word	262
	.byte	14,2,2,35,0,8
	.byte	'reserved_14',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'BLKM',0,1
	.word	231
	.byte	3,5,2,35,2,8
	.byte	'RROAT',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'CHMODE',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'CHDW',0,1
	.word	231
	.byte	3,0,2,35,2,8
	.byte	'PATSEL',0,1
	.word	231
	.byte	3,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'PRSEL',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'reserved_29',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'DMAPRIO',0,1
	.word	231
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,3,205,3,3
	.word	6450
	.byte	7
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,3,208,3,16,4,8
	.byte	'TCOUNT',0,2
	.word	262
	.byte	14,2,2,35,0,8
	.byte	'reserved_14',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'LXO',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'WRPS',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'WRPD',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'ICH',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'IPM',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'reserved_20',0,1
	.word	231
	.byte	2,2,2,35,2,8
	.byte	'BUFFER',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'FROZEN',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'SWB',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'CWRP',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'CICH',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'SIT',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	3,1,2,35,3,8
	.byte	'SCH',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,3,226,3,3
	.word	6719
	.byte	7
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,3,229,3,16,4,8
	.byte	'DADR',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_DADR_Bits',0,3,232,3,3
	.word	7057
	.byte	7
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,3,235,3,16,4,8
	.byte	'RDCRC',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,3,238,3,3
	.word	7132
	.byte	7
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,3,241,3,16,4,8
	.byte	'SADR',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SADR_Bits',0,3,244,3,3
	.word	7212
	.byte	7
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,3,247,3,16,4,8
	.byte	'SDCRC',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,3,250,3,3
	.word	7287
	.byte	7
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,3,253,3,16,4,8
	.byte	'SHADR',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,3,128,4,3
	.word	7367
	.byte	7
	.byte	'_Ifx_DMA_CLC_Bits',0,3,131,4,16,4,8
	.byte	'DISR',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'DISS',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'EDIS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	921
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_DMA_CLC_Bits',0,3,138,4,3
	.word	7445
	.byte	7
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,3,141,4,16,4,8
	.byte	'SIT',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	921
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_ERRINTR_Bits',0,3,145,4,3
	.word	7588
	.byte	7
	.byte	'_Ifx_DMA_HRR_Bits',0,3,148,4,16,4,8
	.byte	'HRP',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	921
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_DMA_HRR_Bits',0,3,152,4,3
	.word	7684
	.byte	7
	.byte	'_Ifx_DMA_ID_Bits',0,3,155,4,16,4,8
	.byte	'MODREV',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'MODTYPE',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'MODNUMBER',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_DMA_ID_Bits',0,3,160,4,3
	.word	7772
	.byte	7
	.byte	'_Ifx_DMA_MEMCON_Bits',0,3,163,4,16,4,6
	.byte	'unsigned int',0,4,7,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	2,30,2,35,0,8
	.byte	'INTERR',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'RMWERR',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7906
	.byte	1,26,2,35,0,8
	.byte	'DATAERR',0,4
	.word	7906
	.byte	1,25,2,35,0,8
	.byte	'reserved_7',0,4
	.word	7906
	.byte	1,24,2,35,0,8
	.byte	'PMIC',0,4
	.word	7906
	.byte	1,23,2,35,0,8
	.byte	'ERRDIS',0,4
	.word	7906
	.byte	1,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	7906
	.byte	22,0,2,35,0,0,4
	.byte	'Ifx_DMA_MEMCON_Bits',0,3,175,4,3
	.word	7879
	.byte	7
	.byte	'_Ifx_DMA_MODE_Bits',0,3,178,4,16,4,8
	.byte	'MODE',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	921
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_MODE_Bits',0,3,182,4,3
	.word	8152
	.byte	7
	.byte	'_Ifx_DMA_OTSS_Bits',0,3,185,4,16,4,8
	.byte	'TGS',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	3,1,2,35,0,8
	.byte	'BS',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	921
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_DMA_OTSS_Bits',0,3,191,4,3
	.word	8243
	.byte	7
	.byte	'_Ifx_DMA_PRR0_Bits',0,3,194,4,16,4,8
	.byte	'PAT00',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'PAT01',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'PAT02',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'PAT03',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_PRR0_Bits',0,3,200,4,3
	.word	8369
	.byte	7
	.byte	'_Ifx_DMA_PRR1_Bits',0,3,203,4,16,4,8
	.byte	'PAT10',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'PAT11',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'PAT12',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'PAT13',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_PRR1_Bits',0,3,209,4,3
	.word	8490
	.byte	7
	.byte	'_Ifx_DMA_SUSACR_Bits',0,3,212,4,16,4,8
	.byte	'SUSAC',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	921
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_SUSACR_Bits',0,3,216,4,3
	.word	8611
	.byte	7
	.byte	'_Ifx_DMA_SUSENR_Bits',0,3,219,4,16,4,8
	.byte	'SUSEN',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	921
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_SUSENR_Bits',0,3,223,4,3
	.word	8707
	.byte	7
	.byte	'_Ifx_DMA_TIME_Bits',0,3,226,4,16,4,8
	.byte	'COUNT',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_TIME_Bits',0,3,229,4,3
	.word	8803
	.byte	7
	.byte	'_Ifx_DMA_TSR_Bits',0,3,232,4,16,4,8
	.byte	'RST',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'HTRE',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'TRL',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'CH',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'HLTREQ',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'HLTACK',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'reserved_10',0,1
	.word	231
	.byte	6,0,2,35,1,8
	.byte	'ECH',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'DCH',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'CTL',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	5,0,2,35,2,8
	.byte	'HLTCLR',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	231
	.byte	7,0,2,35,3,0,4
	.byte	'Ifx_DMA_TSR_Bits',0,3,248,4,3
	.word	8873
	.byte	9,3,128,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,6
	.byte	'int',0,4,5,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	335
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN00',0,3,133,5,3
	.word	9174
	.byte	9,3,136,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	894
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN01',0,3,141,5,3
	.word	9246
	.byte	9,3,144,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	989
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN10',0,3,149,5,3
	.word	9311
	.byte	9,3,152,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1548
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN11',0,3,157,5,3
	.word	9376
	.byte	9,3,160,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1628
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN20',0,3,165,5,3
	.word	9441
	.byte	9,3,168,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2189
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN21',0,3,173,5,3
	.word	9506
	.byte	9,3,176,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2270
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN30',0,3,181,5,3
	.word	9571
	.byte	9,3,184,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2831
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN31',0,3,189,5,3
	.word	9636
	.byte	9,3,192,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2912
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_CLRE',0,3,197,5,3
	.word	9701
	.byte	9,3,200,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3186
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_EER',0,3,205,5,3
	.word	9767
	.byte	9,3,208,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3400
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ERRSR',0,3,213,5,3
	.word	9832
	.byte	9,3,216,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3684
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,3,221,5,3
	.word	9899
	.byte	9,3,224,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3995
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,3,229,5,3
	.word	9969
	.byte	9,3,232,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4268
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,3,237,5,3
	.word	10038
	.byte	9,3,240,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4535
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_DADR',0,3,245,5,3
	.word	10107
	.byte	9,3,248,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4618
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R0',0,3,253,5,3
	.word	10176
	.byte	9,3,128,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4745
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R1',0,3,133,6,3
	.word	10243
	.byte	9,3,136,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4872
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R2',0,3,141,6,3
	.word	10310
	.byte	9,3,144,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4999
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R3',0,3,149,6,3
	.word	10377
	.byte	9,3,152,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5126
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R4',0,3,157,6,3
	.word	10444
	.byte	9,3,160,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5253
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R5',0,3,165,6,3
	.word	10511
	.byte	9,3,168,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5380
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R6',0,3,173,6,3
	.word	10578
	.byte	9,3,176,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5507
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R7',0,3,181,6,3
	.word	10645
	.byte	9,3,184,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5634
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,3,189,6,3
	.word	10712
	.byte	9,3,192,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5720
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SADR',0,3,197,6,3
	.word	10782
	.byte	9,3,200,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5803
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,3,205,6,3
	.word	10851
	.byte	9,3,208,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5889
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,3,213,6,3
	.word	10921
	.byte	9,3,216,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5975
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SR',0,3,221,6,3
	.word	10991
	.byte	9,3,224,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6147
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_ADICR',0,3,229,6,3
	.word	11058
	.byte	9,3,232,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6450
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_CHCFGR',0,3,237,6,3
	.word	11124
	.byte	9,3,240,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6719
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_CHCSR',0,3,245,6,3
	.word	11191
	.byte	9,3,248,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7057
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_DADR',0,3,253,6,3
	.word	11257
	.byte	9,3,128,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7132
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_RDCRCR',0,3,133,7,3
	.word	11322
	.byte	9,3,136,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7212
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SADR',0,3,141,7,3
	.word	11389
	.byte	9,3,144,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7287
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SDCRCR',0,3,149,7,3
	.word	11454
	.byte	9,3,152,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7367
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SHADR',0,3,157,7,3
	.word	11521
	.byte	9,3,160,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7445
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CLC',0,3,165,7,3
	.word	11587
	.byte	9,3,168,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7588
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ERRINTR',0,3,173,7,3
	.word	11648
	.byte	9,3,176,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7684
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_HRR',0,3,181,7,3
	.word	11713
	.byte	9,3,184,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7772
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ID',0,3,189,7,3
	.word	11774
	.byte	9,3,192,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7879
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_MEMCON',0,3,197,7,3
	.word	11834
	.byte	9,3,200,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8152
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_MODE',0,3,205,7,3
	.word	11898
	.byte	9,3,208,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8243
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_OTSS',0,3,213,7,3
	.word	11960
	.byte	9,3,216,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8369
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_PRR0',0,3,221,7,3
	.word	12022
	.byte	9,3,224,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8490
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_PRR1',0,3,229,7,3
	.word	12084
	.byte	9,3,232,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8611
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_SUSACR',0,3,237,7,3
	.word	12146
	.byte	9,3,240,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8707
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_SUSENR',0,3,245,7,3
	.word	12210
	.byte	9,3,248,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8803
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_TIME',0,3,253,7,3
	.word	12274
	.byte	9,3,128,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8873
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_TSR',0,3,133,8,3
	.word	12336
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME',0,3,144,8,25,112,10
	.byte	'SR',0,4
	.word	10991
	.byte	2,35,0,11,12
	.word	231
	.byte	12,11,0,10
	.byte	'reserved_4',0,12
	.word	12431
	.byte	2,35,4,10
	.byte	'R0',0,4
	.word	10176
	.byte	2,35,16,10
	.byte	'R1',0,4
	.word	10243
	.byte	2,35,20,10
	.byte	'R2',0,4
	.word	10310
	.byte	2,35,24,10
	.byte	'R3',0,4
	.word	10377
	.byte	2,35,28,10
	.byte	'R4',0,4
	.word	10444
	.byte	2,35,32,10
	.byte	'R5',0,4
	.word	10511
	.byte	2,35,36,10
	.byte	'R6',0,4
	.word	10578
	.byte	2,35,40,10
	.byte	'R7',0,4
	.word	10645
	.byte	2,35,44,11,32
	.word	231
	.byte	12,31,0,10
	.byte	'reserved_30',0,32
	.word	12556
	.byte	2,35,48,10
	.byte	'RDCRC',0,4
	.word	10712
	.byte	2,35,80,10
	.byte	'SDCRC',0,4
	.word	10851
	.byte	2,35,84,10
	.byte	'SADR',0,4
	.word	10782
	.byte	2,35,88,10
	.byte	'DADR',0,4
	.word	10107
	.byte	2,35,92,10
	.byte	'ADICR',0,4
	.word	9899
	.byte	2,35,96,10
	.byte	'CHCR',0,4
	.word	9969
	.byte	2,35,100,10
	.byte	'SHADR',0,4
	.word	10921
	.byte	2,35,104,10
	.byte	'CHSR',0,4
	.word	10038
	.byte	2,35,108,0,13
	.word	12397
	.byte	4
	.byte	'Ifx_DMA_BLK_ME',0,3,165,8,3
	.word	12703
	.byte	7
	.byte	'_Ifx_DMA_BLK',0,3,178,8,25,128,1,10
	.byte	'EER',0,4
	.word	9767
	.byte	2,35,0,10
	.byte	'ERRSR',0,4
	.word	9832
	.byte	2,35,4,10
	.byte	'CLRE',0,4
	.word	9701
	.byte	2,35,8,11,4
	.word	231
	.byte	12,3,0,10
	.byte	'reserved_C',0,4
	.word	12794
	.byte	2,35,12,13
	.word	12397
	.byte	10
	.byte	'ME',0,112
	.word	12823
	.byte	2,35,16,0,13
	.word	12732
	.byte	4
	.byte	'Ifx_DMA_BLK',0,3,185,8,3
	.word	12841
	.byte	7
	.byte	'_Ifx_DMA_CH',0,3,188,8,25,32,10
	.byte	'RDCRCR',0,4
	.word	11322
	.byte	2,35,0,10
	.byte	'SDCRCR',0,4
	.word	11454
	.byte	2,35,4,10
	.byte	'SADR',0,4
	.word	11389
	.byte	2,35,8,10
	.byte	'DADR',0,4
	.word	11257
	.byte	2,35,12,10
	.byte	'ADICR',0,4
	.word	11058
	.byte	2,35,16,10
	.byte	'CHCFGR',0,4
	.word	11124
	.byte	2,35,20,10
	.byte	'SHADR',0,4
	.word	11521
	.byte	2,35,24,10
	.byte	'CHCSR',0,4
	.word	11191
	.byte	2,35,28,0,13
	.word	12867
	.byte	4
	.byte	'Ifx_DMA_CH',0,3,198,8,3
	.word	13007
	.byte	7
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,8
	.byte	'EN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	13032
	.byte	7
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,8
	.byte	'reserved_0',0,4
	.word	921
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	13589
	.byte	7
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,8
	.byte	'STM0DIS',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'STM1DIS',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'STM2DIS',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,4
	.word	921
	.byte	29,0,2,35,2,0,4
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	13666
	.byte	7
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'BAUD2DIV',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'SRIDIV',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'LPDIV',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'SPBDIV',0,1
	.word	231
	.byte	4,4,2,35,2,8
	.byte	'FSI2DIV',0,1
	.word	231
	.byte	2,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'FSIDIV',0,1
	.word	231
	.byte	2,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	2,4,2,35,3,8
	.byte	'CLKSEL',0,1
	.word	231
	.byte	2,2,2,35,3,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	13802
	.byte	7
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,8
	.byte	'CANDIV',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'ERAYDIV',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'STMDIV',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'GTMDIV',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'ETHDIV',0,1
	.word	231
	.byte	4,4,2,35,2,8
	.byte	'ASCLINFDIV',0,1
	.word	231
	.byte	4,0,2,35,2,8
	.byte	'ASCLINSDIV',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'INSEL',0,1
	.word	231
	.byte	2,2,2,35,3,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	14084
	.byte	7
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,8
	.byte	'BBBDIV',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	921
	.byte	26,2,2,35,2,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	14322
	.byte	7
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,8
	.byte	'PLLDIV',0,1
	.word	231
	.byte	6,2,2,35,0,8
	.byte	'PLLSEL',0,1
	.word	231
	.byte	2,0,2,35,0,8
	.byte	'PLLERAYDIV',0,1
	.word	231
	.byte	6,2,2,35,1,8
	.byte	'PLLERAYSEL',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'SRIDIV',0,1
	.word	231
	.byte	6,2,2,35,2,8
	.byte	'SRISEL',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,149,1,3
	.word	14450
	.byte	7
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,152,1,16,4,8
	.byte	'SPBDIV',0,1
	.word	231
	.byte	6,2,2,35,0,8
	.byte	'SPBSEL',0,1
	.word	231
	.byte	2,0,2,35,0,8
	.byte	'GTMDIV',0,1
	.word	231
	.byte	6,2,2,35,1,8
	.byte	'GTMSEL',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'STMDIV',0,1
	.word	231
	.byte	6,2,2,35,2,8
	.byte	'STMSEL',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,163,1,3
	.word	14677
	.byte	7
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,166,1,16,4,8
	.byte	'MAXDIV',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	921
	.byte	26,2,2,35,2,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,172,1,3
	.word	14896
	.byte	7
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,175,1,16,4,8
	.byte	'CPU0DIV',0,1
	.word	231
	.byte	6,2,2,35,0,8
	.byte	'reserved_6',0,4
	.word	921
	.byte	26,0,2,35,2,0,4
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,179,1,3
	.word	15024
	.byte	7
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,182,1,16,4,8
	.byte	'CHREV',0,1
	.word	231
	.byte	6,2,2,35,0,8
	.byte	'CHTEC',0,1
	.word	231
	.byte	2,0,2,35,0,8
	.byte	'CHID',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'EEA',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'UCODE',0,1
	.word	231
	.byte	7,0,2,35,2,8
	.byte	'FSIZE',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'SP',0,1
	.word	231
	.byte	2,2,2,35,3,8
	.byte	'SEC',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,193,1,3
	.word	15124
	.byte	7
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,196,1,16,4,8
	.byte	'PWD',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'START',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'CAL',0,4
	.word	921
	.byte	22,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	5,1,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,204,1,3
	.word	15332
	.byte	7
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,207,1,16,4,8
	.byte	'LOWER',0,2
	.word	262
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	231
	.byte	5,1,2,35,1,8
	.byte	'LLU',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'UPPER',0,2
	.word	262
	.byte	10,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	4,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'UOF',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,216,1,3
	.word	15497
	.byte	7
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,219,1,16,4,8
	.byte	'RESULT',0,2
	.word	262
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	231
	.byte	4,2,2,35,1,8
	.byte	'RDY',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'BUSY',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,226,1,3
	.word	15680
	.byte	7
	.byte	'_Ifx_SCU_EICR_Bits',0,4,229,1,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'EXIS0',0,1
	.word	231
	.byte	3,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'FEN0',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'REN0',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'LDEN0',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'EIEN0',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'INP0',0,1
	.word	231
	.byte	3,1,2,35,1,8
	.byte	'reserved_15',0,4
	.word	921
	.byte	5,12,2,35,2,8
	.byte	'EXIS1',0,1
	.word	231
	.byte	3,1,2,35,2,8
	.byte	'reserved_23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'FEN1',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'REN1',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'LDEN1',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'EIEN1',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'INP1',0,1
	.word	231
	.byte	3,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EICR_Bits',0,4,248,1,3
	.word	15834
	.byte	7
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,251,1,16,4,8
	.byte	'INTF0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'INTF1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'INTF2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'INTF3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'INTF4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'INTF5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'INTF6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'INTF7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	921
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_EIFR_Bits',0,4,134,2,3
	.word	16198
	.byte	7
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,137,2,16,4,8
	.byte	'POL',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'MODE',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'ENON',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'PSEL',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,2
	.word	262
	.byte	12,0,2,35,0,8
	.byte	'EMSF',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'SEMSF',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	6,0,2,35,2,8
	.byte	'EMSFM',0,1
	.word	231
	.byte	2,6,2,35,3,8
	.byte	'SEMSFM',0,1
	.word	231
	.byte	2,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_EMSR_Bits',0,4,150,2,3
	.word	16409
	.byte	7
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,153,2,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'EDCON',0,2
	.word	262
	.byte	2,7,2,35,0,8
	.byte	'reserved_9',0,4
	.word	921
	.byte	23,0,2,35,2,0,4
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,158,2,3
	.word	16661
	.byte	7
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,161,2,16,4,8
	.byte	'ARI',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ARC',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	921
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,166,2,3
	.word	16779
	.byte	7
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,169,2,16,4,8
	.byte	'reserved_0',0,4
	.word	921
	.byte	28,4,2,35,2,8
	.byte	'EVR13OFF',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'BPEVR13OFF',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,176,2,3
	.word	16890
	.byte	7
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,179,2,16,4,8
	.byte	'ADC13V',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'ADCSWDV',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	7,1,2,35,3,8
	.byte	'VAL',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,186,2,3
	.word	17053
	.byte	7
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,189,2,16,4,8
	.byte	'EVR13OVMOD',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'EVR13UVMOD',0,1
	.word	231
	.byte	2,2,2,35,0,8
	.byte	'reserved_6',0,2
	.word	262
	.byte	10,0,2,35,0,8
	.byte	'SWDOVMOD',0,1
	.word	231
	.byte	2,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	2,4,2,35,2,8
	.byte	'SWDUVMOD',0,1
	.word	231
	.byte	2,2,2,35,2,8
	.byte	'reserved_22',0,2
	.word	262
	.byte	8,2,2,35,2,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,201,2,3
	.word	17215
	.byte	7
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,204,2,16,4,8
	.byte	'EVR13OVVAL',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'SWDOVVAL',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,212,2,3
	.word	17493
	.byte	7
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,215,2,16,4,8
	.byte	'reserved_0',0,4
	.word	921
	.byte	28,4,2,35,2,8
	.byte	'RSTSWDOFF',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'BPRSTSWDOFF',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,222,2,3
	.word	17672
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,225,2,16,4,8
	.byte	'SD33P',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'SD33I',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,4
	.word	921
	.byte	19,1,2,35,2,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,232,2,3
	.word	17832
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,235,2,16,4,8
	.byte	'SDFREQSPRD',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'TON',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'TOFF',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'SDSTEP',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'SYNCDIV',0,1
	.word	231
	.byte	3,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,244,2,3
	.word	17993
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,247,2,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'STBS',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'STSP',0,1
	.word	231
	.byte	2,4,2,35,1,8
	.byte	'NS',0,1
	.word	231
	.byte	2,2,2,35,1,8
	.byte	'OL',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'PIAD',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'ADCMODE',0,1
	.word	231
	.byte	4,4,2,35,2,8
	.byte	'ADCLPF',0,1
	.word	231
	.byte	2,2,2,35,2,8
	.byte	'ADCLSB',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'reserved_23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'SDLUT',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,134,3,3
	.word	18185
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,137,3,16,4,8
	.byte	'SDOLCON',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'MODSEL',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'MODLOW',0,1
	.word	231
	.byte	7,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'SDVOKLVL',0,1
	.word	231
	.byte	6,2,2,35,2,8
	.byte	'MODMAN',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'MODHIGH',0,1
	.word	231
	.byte	7,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,147,3,3
	.word	18481
	.byte	7
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,150,3,16,4,8
	.byte	'EVR13',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'OV13',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'OVSWD',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'UV13',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'UVSWD',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'BGPROK',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'reserved_11',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'SCMOD',0,1
	.word	231
	.byte	2,2,2,35,1,8
	.byte	'reserved_14',0,4
	.word	921
	.byte	18,0,2,35,2,0,4
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,164,3,3
	.word	18696
	.byte	7
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,167,3,16,4,8
	.byte	'EVR13UVVAL',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'SWDUVVAL',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,175,3,3
	.word	18985
	.byte	7
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,178,3,16,4,8
	.byte	'EN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'SEL0',0,1
	.word	231
	.byte	4,2,2,35,0,8
	.byte	'reserved_6',0,2
	.word	262
	.byte	10,0,2,35,0,8
	.byte	'EN1',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'NSEL',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'SEL1',0,1
	.word	231
	.byte	4,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'DIV1',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,189,3,3
	.word	19164
	.byte	7
	.byte	'_Ifx_SCU_FDR_Bits',0,4,192,3,16,4,8
	.byte	'STEP',0,2
	.word	262
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	231
	.byte	4,2,2,35,1,8
	.byte	'DM',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'RESULT',0,2
	.word	262
	.byte	10,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	5,1,2,35,3,8
	.byte	'DISCLK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_FDR_Bits',0,4,200,3,3
	.word	19382
	.byte	7
	.byte	'_Ifx_SCU_FMR_Bits',0,4,203,3,16,4,8
	.byte	'FS0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'FS1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'FS2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'FS3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'FS4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'FS5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'FS6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'FS7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'FC0',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'FC1',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'FC2',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'FC3',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'FC4',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'FC5',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'FC6',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'FC7',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_FMR_Bits',0,4,223,3,3
	.word	19545
	.byte	7
	.byte	'_Ifx_SCU_ID_Bits',0,4,226,3,16,4,8
	.byte	'MODREV',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'MODTYPE',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'MODNUMBER',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_ID_Bits',0,4,231,3,3
	.word	19881
	.byte	7
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,234,3,16,4,8
	.byte	'IPEN00',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'IPEN01',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'IPEN02',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'IPEN03',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'IPEN04',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IPEN05',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'IPEN06',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'IPEN07',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	5,3,2,35,1,8
	.byte	'GEEN0',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'IGP0',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'IPEN10',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'IPEN11',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'IPEN12',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'IPEN13',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'IPEN14',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'IPEN15',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'IPEN16',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'IPEN17',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	5,3,2,35,3,8
	.byte	'GEEN1',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'IGP1',0,1
	.word	231
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_SCU_IGCR_Bits',0,4,130,4,3
	.word	19988
	.byte	7
	.byte	'_Ifx_SCU_IN_Bits',0,4,133,4,16,4,8
	.byte	'P0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	921
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_IN_Bits',0,4,138,4,3
	.word	20440
	.byte	7
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,141,4,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'PC0',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'PC1',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_IOCR_Bits',0,4,148,4,3
	.word	20539
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,151,4,16,4,8
	.byte	'LBISTREQ',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'LBISTREQP',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'PATTERNS',0,2
	.word	262
	.byte	14,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,157,4,3
	.word	20689
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,160,4,16,4,8
	.byte	'SEED',0,4
	.word	921
	.byte	23,9,2,35,2,8
	.byte	'reserved_23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'SPLITSH',0,1
	.word	231
	.byte	3,5,2,35,3,8
	.byte	'BODY',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'LBISTFREQU',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,167,4,3
	.word	20838
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,170,4,16,4,8
	.byte	'SIGNATURE',0,4
	.word	921
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	7,1,2,35,3,8
	.byte	'LBISTDONE',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,175,4,3
	.word	20999
	.byte	7
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,4,178,4,16,4,8
	.byte	'reserved_0',0,2
	.word	262
	.byte	16,0,2,35,0,8
	.byte	'LS',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,2
	.word	262
	.byte	14,1,2,35,2,8
	.byte	'LSEN',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_LCLCON0_Bits',0,4,184,4,3
	.word	21129
	.byte	7
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,187,4,16,4,8
	.byte	'LCLT0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'LCLT1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	921
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,192,4,3
	.word	21263
	.byte	7
	.byte	'_Ifx_SCU_MANID_Bits',0,4,195,4,16,4,8
	.byte	'DEPT',0,1
	.word	231
	.byte	5,3,2,35,0,8
	.byte	'MANUF',0,2
	.word	262
	.byte	11,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_MANID_Bits',0,4,200,4,3
	.word	21378
	.byte	7
	.byte	'_Ifx_SCU_OMR_Bits',0,4,203,4,16,4,8
	.byte	'PS0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,2
	.word	262
	.byte	14,0,2,35,0,8
	.byte	'PCL0',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,2
	.word	262
	.byte	14,0,2,35,2,0,4
	.byte	'Ifx_SCU_OMR_Bits',0,4,211,4,3
	.word	21489
	.byte	7
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,214,4,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PLLLV',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'OSCRES',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'GAINSEL',0,1
	.word	231
	.byte	2,3,2,35,0,8
	.byte	'MODE',0,1
	.word	231
	.byte	2,1,2,35,0,8
	.byte	'SHBY',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'PLLHV',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'reserved_9',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'X1D',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'X1DEN',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'OSCVAL',0,1
	.word	231
	.byte	5,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	2,1,2,35,2,8
	.byte	'APREN',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,231,4,3
	.word	21647
	.byte	7
	.byte	'_Ifx_SCU_OUT_Bits',0,4,234,4,16,4,8
	.byte	'P0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	921
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_OUT_Bits',0,4,239,4,3
	.word	21987
	.byte	7
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,242,4,16,4,8
	.byte	'CSEL0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'CSEL1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'CSEL2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,2
	.word	262
	.byte	13,0,2,35,0,8
	.byte	'OVSTRT',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'OVSTP',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'DCINVAL',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	5,0,2,35,2,8
	.byte	'OVCONF',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'POVCONF',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	6,0,2,35,3,0,4
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,255,4,3
	.word	22088
	.byte	7
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,130,5,16,4,8
	.byte	'OVEN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'OVEN1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'OVEN2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,4
	.word	921
	.byte	29,0,2,35,2,0,4
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,136,5,3
	.word	22355
	.byte	7
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,139,5,16,4,8
	.byte	'PDIS0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PDIS1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	921
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDISC_Bits',0,4,144,5,3
	.word	22491
	.byte	7
	.byte	'_Ifx_SCU_PDR_Bits',0,4,147,5,16,4,8
	.byte	'PD0',0,1
	.word	231
	.byte	3,5,2,35,0,8
	.byte	'PL0',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'PD1',0,1
	.word	231
	.byte	3,1,2,35,0,8
	.byte	'PL1',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	921
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDR_Bits',0,4,154,5,3
	.word	22602
	.byte	7
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,157,5,16,4,8
	.byte	'PDR0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PDR1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'PDR2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'PDR3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'PDR4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'PDR5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'PDR6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PDR7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	921
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDRR_Bits',0,4,168,5,3
	.word	22735
	.byte	7
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,171,5,16,4,8
	.byte	'VCOBYP',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'VCOPWD',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'MODEN',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'SETFINDIS',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'CLRFINDIS',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'OSCDISCDIS',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'reserved_7',0,2
	.word	262
	.byte	2,7,2,35,0,8
	.byte	'NDIV',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'PLLPWD',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'RESLD',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	5,0,2,35,2,8
	.byte	'PDIV',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,188,5,3
	.word	22938
	.byte	7
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,191,5,16,4,8
	.byte	'K2DIV',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'K3DIV',0,1
	.word	231
	.byte	7,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'K1DIV',0,1
	.word	231
	.byte	7,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	262
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,199,5,3
	.word	23294
	.byte	7
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,202,5,16,4,8
	.byte	'MODCFG',0,2
	.word	262
	.byte	16,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,206,5,3
	.word	23472
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,209,5,16,4,8
	.byte	'VCOBYP',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'VCOPWD',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'SETFINDIS',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'CLRFINDIS',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'OSCDISCDIS',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'reserved_7',0,2
	.word	262
	.byte	2,7,2,35,0,8
	.byte	'NDIV',0,1
	.word	231
	.byte	5,2,2,35,1,8
	.byte	'reserved_14',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'PLLPWD',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'RESLD',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	5,0,2,35,2,8
	.byte	'PDIV',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,226,5,3
	.word	23572
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,229,5,16,4,8
	.byte	'K2DIV',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'K3DIV',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'K1DIV',0,1
	.word	231
	.byte	7,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	262
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,237,5,3
	.word	23942
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,240,5,16,4,8
	.byte	'VCOBYST',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PWDSTAT',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'VCOLOCK',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'FINDIS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'K1RDY',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'K2RDY',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,4
	.word	921
	.byte	26,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,249,5,3
	.word	24128
	.byte	7
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,252,5,16,4,8
	.byte	'VCOBYST',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'VCOLOCK',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'FINDIS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'K1RDY',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'K2RDY',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'MODRUN',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	921
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,135,6,3
	.word	24326
	.byte	7
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,138,6,16,4,8
	.byte	'REQSLP',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'SMUSLP',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,1
	.word	231
	.byte	5,0,2,35,0,8
	.byte	'PMST',0,1
	.word	231
	.byte	3,5,2,35,1,8
	.byte	'reserved_11',0,4
	.word	921
	.byte	21,0,2,35,2,0,4
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,145,6,3
	.word	24559
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,148,6,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1WKEN',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'PINAWKEN',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'PINBWKEN',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'ESR0DFEN',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'ESR0EDCON',0,1
	.word	231
	.byte	2,1,2,35,0,8
	.byte	'ESR1DFEN',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'ESR1EDCON',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'PINADFEN',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'PINAEDCON',0,1
	.word	231
	.byte	2,3,2,35,1,8
	.byte	'PINBDFEN',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'PINBEDCON',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'STBYRAMSEL',0,1
	.word	231
	.byte	2,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'WUTWKEN',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	2,1,2,35,2,8
	.byte	'PORSTDF',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'DCDCSYNC',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	3,3,2,35,3,8
	.byte	'ESR0TRIST',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,174,6,3
	.word	24711
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,177,6,16,4,8
	.byte	'reserved_0',0,2
	.word	262
	.byte	12,4,2,35,0,8
	.byte	'IRADIS',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'reserved_13',0,4
	.word	921
	.byte	14,5,2,35,2,8
	.byte	'STBYEVEN',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'STBYEV',0,1
	.word	231
	.byte	3,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,185,6,3
	.word	25270
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,4,188,6,16,4,8
	.byte	'WUTREL',0,4
	.word	921
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'WUTDIV',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'WUTEN',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'WUTMODE',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,4,196,6,3
	.word	25453
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,199,6,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'ESR1WKP',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'ESR1OVRUN',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'PINAWKP',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'PINAOVRUN',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'PINBWKP',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PINBOVRUN',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'PORSTDF',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'HWCFGEVR',0,1
	.word	231
	.byte	3,3,2,35,1,8
	.byte	'STBYRAM',0,1
	.word	231
	.byte	2,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'WUTWKP',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'WUTOVRUN',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'WUTWKEN',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'ESR1WKEN',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'PINAWKEN',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'PINBWKEN',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	262
	.byte	4,5,2,35,2,8
	.byte	'ESR0TRIST',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'WUTEN',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'WUTMODE',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'WUTRUN',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,226,6,3
	.word	25622
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,229,6,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'ESR1WKPCLR',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'ESR1OVRUNCLR',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'PINAWKPCLR',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'PINAOVRUNCLR',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'PINBWKPCLR',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PINBOVRUNCLR',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'WUTWKPCLR',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'WUTOVRUNCLR',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,2
	.word	262
	.byte	14,0,2,35,2,0,4
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,242,6,3
	.word	26189
	.byte	7
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,4,245,6,16,4,8
	.byte	'WUTCNT',0,4
	.word	921
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	7,1,2,35,3,8
	.byte	'VAL',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,4,250,6,3
	.word	26505
	.byte	7
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,253,6,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'CLRC',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,2
	.word	262
	.byte	10,4,2,35,0,8
	.byte	'CSS0',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'CSS1',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'CSS2',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'USRINFO',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,135,7,3
	.word	26624
	.byte	7
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,138,7,16,4,8
	.byte	'ESR0',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'ESR1',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	2,2,2,35,0,8
	.byte	'SMU',0,1
	.word	231
	.byte	2,0,2,35,0,8
	.byte	'SW',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'STM0',0,1
	.word	231
	.byte	2,4,2,35,1,8
	.byte	'STM1',0,1
	.word	231
	.byte	2,2,2,35,1,8
	.byte	'STM2',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,149,7,3
	.word	26833
	.byte	7
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,152,7,16,4,8
	.byte	'ESR0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMU',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'SW',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'STM0',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'STM1',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'STM2',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'PORST',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'CB0',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'CB1',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'CB3',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	2,1,2,35,2,8
	.byte	'EVR13',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'EVR33',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'SWD',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	2,4,2,35,3,8
	.byte	'STBYR',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'reserved_29',0,1
	.word	231
	.byte	3,0,2,35,3,0,4
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,175,7,3
	.word	27044
	.byte	7
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,178,7,16,4,8
	.byte	'HBT',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	921
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,182,7,3
	.word	27476
	.byte	7
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,185,7,16,4,8
	.byte	'HWCFG',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'FTM',0,1
	.word	231
	.byte	7,1,2,35,1,8
	.byte	'MODE',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'FCBAE',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'LUDIS',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'TRSTL',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'SPDEN',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	3,0,2,35,2,8
	.byte	'RAMINT',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	231
	.byte	7,0,2,35,3,0,4
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,198,7,3
	.word	27572
	.byte	7
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,201,7,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'SWRSTREQ',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	921
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,206,7,3
	.word	27832
	.byte	7
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,209,7,16,4,8
	.byte	'CCTRIG0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'RAMINTM',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'SETLUDIS',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'reserved_5',0,1
	.word	231
	.byte	3,0,2,35,0,8
	.byte	'DATM',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'reserved_9',0,4
	.word	921
	.byte	23,0,2,35,2,0,4
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,218,7,3
	.word	27957
	.byte	7
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,221,7,16,4,8
	.byte	'ESR0T',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	921
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,228,7,3
	.word	28154
	.byte	7
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,231,7,16,4,8
	.byte	'ESR0T',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	921
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,238,7,3
	.word	28307
	.byte	7
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,241,7,16,4,8
	.byte	'ESR0T',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	921
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,248,7,3
	.word	28460
	.byte	7
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,251,7,16,4,8
	.byte	'ESR0T',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	921
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,130,8,3
	.word	28613
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,133,8,16,4,8
	.byte	'ENDINIT',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'LCK',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'PW',0,4
	.word	7906
	.byte	14,16,2,35,0,8
	.byte	'REL',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,139,8,3
	.word	28768
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,142,8,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'IR0',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'DR',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IR1',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'UR',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PAR',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'TCR',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'TCTR',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,154,8,3
	.word	28898
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,157,8,16,4,8
	.byte	'AE',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'OE',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'IS0',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'DS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'TO',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IS1',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'US',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PAS',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'TCS',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'TCT',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'TIM',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,170,8,3
	.word	29136
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,173,8,16,4,8
	.byte	'ENDINIT',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'LCK',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'PW',0,4
	.word	7906
	.byte	14,16,2,35,0,8
	.byte	'REL',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,179,8,3
	.word	29359
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,182,8,16,4,8
	.byte	'CLRIRF',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'IR0',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'DR',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IR1',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'UR',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PAR',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'TCR',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'TCTR',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,195,8,3
	.word	29485
	.byte	7
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,198,8,16,4,8
	.byte	'AE',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'OE',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'IS0',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'DS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'TO',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IS1',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'US',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PAS',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'TCS',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'TCT',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'TIM',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,211,8,3
	.word	29737
	.byte	9,4,219,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13032
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ACCEN0',0,4,224,8,3
	.word	29956
	.byte	9,4,227,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13589
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ACCEN1',0,4,232,8,3
	.word	30020
	.byte	9,4,235,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13666
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ARSTDIS',0,4,240,8,3
	.word	30084
	.byte	9,4,243,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13802
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON0',0,4,248,8,3
	.word	30149
	.byte	9,4,251,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14084
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON1',0,4,128,9,3
	.word	30214
	.byte	9,4,131,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14322
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON2',0,4,136,9,3
	.word	30279
	.byte	9,4,139,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14450
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON3',0,4,144,9,3
	.word	30344
	.byte	9,4,147,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14677
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON4',0,4,152,9,3
	.word	30409
	.byte	9,4,155,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14896
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON5',0,4,160,9,3
	.word	30474
	.byte	9,4,163,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15024
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON6',0,4,168,9,3
	.word	30539
	.byte	9,4,171,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15124
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CHIPID',0,4,176,9,3
	.word	30604
	.byte	9,4,179,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15332
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSCON',0,4,184,9,3
	.word	30668
	.byte	9,4,187,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15497
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSLIM',0,4,192,9,3
	.word	30732
	.byte	9,4,195,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15680
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSSTAT',0,4,200,9,3
	.word	30796
	.byte	9,4,203,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15834
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EICR',0,4,208,9,3
	.word	30861
	.byte	9,4,211,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16198
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EIFR',0,4,216,9,3
	.word	30923
	.byte	9,4,219,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16409
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EMSR',0,4,224,9,3
	.word	30985
	.byte	9,4,227,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16661
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ESRCFG',0,4,232,9,3
	.word	31047
	.byte	9,4,235,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16779
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ESROCFG',0,4,240,9,3
	.word	31111
	.byte	9,4,243,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16890
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVR13CON',0,4,248,9,3
	.word	31176
	.byte	9,4,251,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17053
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,128,10,3
	.word	31242
	.byte	9,4,131,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17215
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,136,10,3
	.word	31310
	.byte	9,4,139,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17493
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVROVMON',0,4,144,10,3
	.word	31378
	.byte	9,4,147,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17672
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRRSTCON',0,4,152,10,3
	.word	31444
	.byte	9,4,155,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17832
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,160,10,3
	.word	31511
	.byte	9,4,163,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17993
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,168,10,3
	.word	31580
	.byte	9,4,171,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18185
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,176,10,3
	.word	31648
	.byte	9,4,179,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18481
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,184,10,3
	.word	31716
	.byte	9,4,187,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18696
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSTAT',0,4,192,10,3
	.word	31784
	.byte	9,4,195,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18985
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRUVMON',0,4,200,10,3
	.word	31849
	.byte	9,4,203,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19164
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EXTCON',0,4,208,10,3
	.word	31915
	.byte	9,4,211,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19382
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_FDR',0,4,216,10,3
	.word	31979
	.byte	9,4,219,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19545
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_FMR',0,4,224,10,3
	.word	32040
	.byte	9,4,227,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19881
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ID',0,4,232,10,3
	.word	32101
	.byte	9,4,235,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19988
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IGCR',0,4,240,10,3
	.word	32161
	.byte	9,4,243,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20440
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IN',0,4,248,10,3
	.word	32223
	.byte	9,4,251,10,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20539
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IOCR',0,4,128,11,3
	.word	32283
	.byte	9,4,131,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20689
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,136,11,3
	.word	32345
	.byte	9,4,139,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20838
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,144,11,3
	.word	32413
	.byte	9,4,147,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20999
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,152,11,3
	.word	32481
	.byte	9,4,155,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21129
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LCLCON0',0,4,160,11,3
	.word	32549
	.byte	9,4,163,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21263
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LCLTEST',0,4,168,11,3
	.word	32614
	.byte	9,4,171,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21378
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_MANID',0,4,176,11,3
	.word	32679
	.byte	9,4,179,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21489
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OMR',0,4,184,11,3
	.word	32742
	.byte	9,4,187,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21647
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OSCCON',0,4,192,11,3
	.word	32803
	.byte	9,4,195,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21987
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OUT',0,4,200,11,3
	.word	32867
	.byte	9,4,203,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22088
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OVCCON',0,4,208,11,3
	.word	32928
	.byte	9,4,211,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22355
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OVCENABLE',0,4,216,11,3
	.word	32992
	.byte	9,4,219,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22491
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDISC',0,4,224,11,3
	.word	33059
	.byte	9,4,227,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22602
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDR',0,4,232,11,3
	.word	33122
	.byte	9,4,235,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22735
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDRR',0,4,240,11,3
	.word	33183
	.byte	9,4,243,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22938
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON0',0,4,248,11,3
	.word	33245
	.byte	9,4,251,11,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23294
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON1',0,4,128,12,3
	.word	33310
	.byte	9,4,131,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23472
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON2',0,4,136,12,3
	.word	33375
	.byte	9,4,139,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23572
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,144,12,3
	.word	33440
	.byte	9,4,147,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23942
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,152,12,3
	.word	33509
	.byte	9,4,155,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24128
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,160,12,3
	.word	33578
	.byte	9,4,163,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24326
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLSTAT',0,4,168,12,3
	.word	33647
	.byte	9,4,171,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24559
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMCSR',0,4,176,12,3
	.word	33712
	.byte	9,4,179,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24711
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR0',0,4,184,12,3
	.word	33775
	.byte	9,4,187,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25270
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR1',0,4,192,12,3
	.word	33840
	.byte	9,4,195,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25453
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR3',0,4,200,12,3
	.word	33905
	.byte	9,4,203,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25622
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWSTAT',0,4,208,12,3
	.word	33970
	.byte	9,4,211,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26189
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,216,12,3
	.word	34036
	.byte	9,4,219,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26505
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWUTCNT',0,4,224,12,3
	.word	34105
	.byte	9,4,227,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26833
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTCON',0,4,232,12,3
	.word	34172
	.byte	9,4,235,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26624
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTCON2',0,4,240,12,3
	.word	34236
	.byte	9,4,243,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27044
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTSTAT',0,4,248,12,3
	.word	34301
	.byte	9,4,251,12,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27476
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SAFECON',0,4,128,13,3
	.word	34366
	.byte	9,4,131,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27572
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_STSTAT',0,4,136,13,3
	.word	34431
	.byte	9,4,139,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27832
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SWRSTCON',0,4,144,13,3
	.word	34495
	.byte	9,4,147,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27957
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SYSCON',0,4,152,13,3
	.word	34561
	.byte	9,4,155,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28154
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPCLR',0,4,160,13,3
	.word	34625
	.byte	9,4,163,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28307
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPDIS',0,4,168,13,3
	.word	34690
	.byte	9,4,171,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28460
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPSET',0,4,176,13,3
	.word	34755
	.byte	9,4,179,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28613
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPSTAT',0,4,184,13,3
	.word	34820
	.byte	9,4,187,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28768
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,192,13,3
	.word	34886
	.byte	9,4,195,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28898
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,200,13,3
	.word	34955
	.byte	9,4,203,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29136
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,208,13,3
	.word	35024
	.byte	9,4,211,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29359
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON0',0,4,216,13,3
	.word	35091
	.byte	9,4,219,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29485
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON1',0,4,224,13,3
	.word	35158
	.byte	9,4,227,13,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29737
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_SR',0,4,232,13,3
	.word	35225
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU',0,4,243,13,25,12,10
	.byte	'CON0',0,4
	.word	34886
	.byte	2,35,0,10
	.byte	'CON1',0,4
	.word	34955
	.byte	2,35,4,10
	.byte	'SR',0,4
	.word	35024
	.byte	2,35,8,0,13
	.word	35290
	.byte	4
	.byte	'Ifx_SCU_WDTCPU',0,4,248,13,3
	.word	35353
	.byte	7
	.byte	'_Ifx_SCU_WDTS',0,4,251,13,25,12,10
	.byte	'CON0',0,4
	.word	35091
	.byte	2,35,0,10
	.byte	'CON1',0,4
	.word	35158
	.byte	2,35,4,10
	.byte	'SR',0,4
	.word	35225
	.byte	2,35,8,0,13
	.word	35382
	.byte	4
	.byte	'Ifx_SCU_WDTS',0,4,128,14,3
	.word	35443
	.byte	7
	.byte	'_Ifx_CPU_A_Bits',0,5,45,16,4,8
	.byte	'ADDR',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_A_Bits',0,5,48,3
	.word	35470
	.byte	7
	.byte	'_Ifx_CPU_BIV_Bits',0,5,51,16,4,8
	.byte	'VSS',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'BIV',0,4
	.word	7906
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_BIV_Bits',0,5,55,3
	.word	35531
	.byte	7
	.byte	'_Ifx_CPU_BTV_Bits',0,5,58,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'BTV',0,4
	.word	7906
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_BTV_Bits',0,5,62,3
	.word	35610
	.byte	7
	.byte	'_Ifx_CPU_CCNT_Bits',0,5,65,16,4,8
	.byte	'CountValue',0,4
	.word	7906
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7906
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_CCNT_Bits',0,5,69,3
	.word	35696
	.byte	7
	.byte	'_Ifx_CPU_CCTRL_Bits',0,5,72,16,4,8
	.byte	'CM',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'CE',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'M1',0,4
	.word	7906
	.byte	3,27,2,35,0,8
	.byte	'M2',0,4
	.word	7906
	.byte	3,24,2,35,0,8
	.byte	'M3',0,4
	.word	7906
	.byte	3,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	7906
	.byte	21,0,2,35,0,0,4
	.byte	'Ifx_CPU_CCTRL_Bits',0,5,80,3
	.word	35785
	.byte	7
	.byte	'_Ifx_CPU_COMPAT_Bits',0,5,83,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'RM',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'SP',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7906
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_COMPAT_Bits',0,5,89,3
	.word	35931
	.byte	7
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,5,92,16,4,8
	.byte	'CORE_ID',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7906
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CORE_ID_Bits',0,5,96,3
	.word	36058
	.byte	7
	.byte	'_Ifx_CPU_CPR_L_Bits',0,5,99,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'LOWBND',0,4
	.word	7906
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPR_L_Bits',0,5,103,3
	.word	36156
	.byte	7
	.byte	'_Ifx_CPU_CPR_U_Bits',0,5,106,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'UPPBND',0,4
	.word	7906
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPR_U_Bits',0,5,110,3
	.word	36249
	.byte	7
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,5,113,16,4,8
	.byte	'MODREV',0,4
	.word	7906
	.byte	8,24,2,35,0,8
	.byte	'MOD_32B',0,4
	.word	7906
	.byte	8,16,2,35,0,8
	.byte	'MOD',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPU_ID_Bits',0,5,118,3
	.word	36342
	.byte	7
	.byte	'_Ifx_CPU_CPXE_Bits',0,5,121,16,4,8
	.byte	'XE',0,4
	.word	7906
	.byte	8,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7906
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPXE_Bits',0,5,125,3
	.word	36449
	.byte	7
	.byte	'_Ifx_CPU_CREVT_Bits',0,5,128,1,16,4,8
	.byte	'EVTA',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'BBM',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'BOD',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'SUSP',0,4
	.word	7906
	.byte	1,26,2,35,0,8
	.byte	'CNT',0,4
	.word	7906
	.byte	2,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7906
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_CREVT_Bits',0,5,136,1,3
	.word	36536
	.byte	7
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,5,139,1,16,4,8
	.byte	'CID',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7906
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CUS_ID_Bits',0,5,143,1,3
	.word	36690
	.byte	7
	.byte	'_Ifx_CPU_D_Bits',0,5,146,1,16,4,8
	.byte	'DATA',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_D_Bits',0,5,149,1,3
	.word	36784
	.byte	7
	.byte	'_Ifx_CPU_DATR_Bits',0,5,152,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'SBE',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	7906
	.byte	5,23,2,35,0,8
	.byte	'CWE',0,4
	.word	7906
	.byte	1,22,2,35,0,8
	.byte	'CFE',0,4
	.word	7906
	.byte	1,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	7906
	.byte	3,18,2,35,0,8
	.byte	'SOE',0,4
	.word	7906
	.byte	1,17,2,35,0,8
	.byte	'SME',0,4
	.word	7906
	.byte	1,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DATR_Bits',0,5,163,1,3
	.word	36847
	.byte	7
	.byte	'_Ifx_CPU_DBGSR_Bits',0,5,166,1,16,4,8
	.byte	'DE',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'HALT',0,4
	.word	7906
	.byte	2,29,2,35,0,8
	.byte	'SIH',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'SUSP',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7906
	.byte	1,26,2,35,0,8
	.byte	'PREVSUSP',0,4
	.word	7906
	.byte	1,25,2,35,0,8
	.byte	'PEVT',0,4
	.word	7906
	.byte	1,24,2,35,0,8
	.byte	'EVTSRC',0,4
	.word	7906
	.byte	5,19,2,35,0,8
	.byte	'reserved_13',0,4
	.word	7906
	.byte	19,0,2,35,0,0,4
	.byte	'Ifx_CPU_DBGSR_Bits',0,5,177,1,3
	.word	37065
	.byte	7
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,5,180,1,16,4,8
	.byte	'DTA',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	7906
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_DBGTCR_Bits',0,5,184,1,3
	.word	37280
	.byte	7
	.byte	'_Ifx_CPU_DCON0_Bits',0,5,187,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'DCBYP',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	7906
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCON0_Bits',0,5,192,1,3
	.word	37374
	.byte	7
	.byte	'_Ifx_CPU_DCON2_Bits',0,5,195,1,16,4,8
	.byte	'DCACHE_SZE',0,4
	.word	7906
	.byte	16,16,2,35,0,8
	.byte	'DSCRATCH_SZE',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCON2_Bits',0,5,199,1,3
	.word	37490
	.byte	7
	.byte	'_Ifx_CPU_DCX_Bits',0,5,202,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	6,26,2,35,0,8
	.byte	'DCXValue',0,4
	.word	7906
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCX_Bits',0,5,206,1,3
	.word	37591
	.byte	7
	.byte	'_Ifx_CPU_DEADD_Bits',0,5,209,1,16,4,8
	.byte	'ERROR_ADDRESS',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_DEADD_Bits',0,5,212,1,3
	.word	37684
	.byte	7
	.byte	'_Ifx_CPU_DIEAR_Bits',0,5,215,1,16,4,8
	.byte	'TA',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_DIEAR_Bits',0,5,218,1,3
	.word	37764
	.byte	7
	.byte	'_Ifx_CPU_DIETR_Bits',0,5,221,1,16,4,8
	.byte	'IED',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'IE_T',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'IE_C',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'IE_S',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'IE_BI',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'E_INFO',0,4
	.word	7906
	.byte	6,21,2,35,0,8
	.byte	'IE_DUAL',0,4
	.word	7906
	.byte	1,20,2,35,0,8
	.byte	'IE_SP',0,4
	.word	7906
	.byte	1,19,2,35,0,8
	.byte	'IE_BS',0,4
	.word	7906
	.byte	1,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	7906
	.byte	18,0,2,35,0,0,4
	.byte	'Ifx_CPU_DIETR_Bits',0,5,233,1,3
	.word	37833
	.byte	7
	.byte	'_Ifx_CPU_DMS_Bits',0,5,236,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'DMSValue',0,4
	.word	7906
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_DMS_Bits',0,5,240,1,3
	.word	38062
	.byte	7
	.byte	'_Ifx_CPU_DPR_L_Bits',0,5,243,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'LOWBND',0,4
	.word	7906
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPR_L_Bits',0,5,247,1,3
	.word	38155
	.byte	7
	.byte	'_Ifx_CPU_DPR_U_Bits',0,5,250,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'UPPBND',0,4
	.word	7906
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPR_U_Bits',0,5,254,1,3
	.word	38250
	.byte	7
	.byte	'_Ifx_CPU_DPRE_Bits',0,5,129,2,16,4,8
	.byte	'RE',0,4
	.word	7906
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPRE_Bits',0,5,133,2,3
	.word	38345
	.byte	7
	.byte	'_Ifx_CPU_DPWE_Bits',0,5,136,2,16,4,8
	.byte	'WE',0,4
	.word	7906
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPWE_Bits',0,5,140,2,3
	.word	38435
	.byte	7
	.byte	'_Ifx_CPU_DSTR_Bits',0,5,143,2,16,4,8
	.byte	'SRE',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'GAE',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'LBE',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7906
	.byte	3,26,2,35,0,8
	.byte	'CRE',0,4
	.word	7906
	.byte	1,25,2,35,0,8
	.byte	'reserved_7',0,4
	.word	7906
	.byte	7,18,2,35,0,8
	.byte	'DTME',0,4
	.word	7906
	.byte	1,17,2,35,0,8
	.byte	'LOE',0,4
	.word	7906
	.byte	1,16,2,35,0,8
	.byte	'SDE',0,4
	.word	7906
	.byte	1,15,2,35,0,8
	.byte	'SCE',0,4
	.word	7906
	.byte	1,14,2,35,0,8
	.byte	'CAC',0,4
	.word	7906
	.byte	1,13,2,35,0,8
	.byte	'MPE',0,4
	.word	7906
	.byte	1,12,2,35,0,8
	.byte	'CLE',0,4
	.word	7906
	.byte	1,11,2,35,0,8
	.byte	'reserved_21',0,4
	.word	7906
	.byte	3,8,2,35,0,8
	.byte	'ALN',0,4
	.word	7906
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	7906
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_CPU_DSTR_Bits',0,5,161,2,3
	.word	38525
	.byte	7
	.byte	'_Ifx_CPU_EXEVT_Bits',0,5,164,2,16,4,8
	.byte	'EVTA',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'BBM',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'BOD',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'SUSP',0,4
	.word	7906
	.byte	1,26,2,35,0,8
	.byte	'CNT',0,4
	.word	7906
	.byte	2,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7906
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_EXEVT_Bits',0,5,172,2,3
	.word	38849
	.byte	7
	.byte	'_Ifx_CPU_FCX_Bits',0,5,175,2,16,4,8
	.byte	'FCXO',0,4
	.word	7906
	.byte	16,16,2,35,0,8
	.byte	'FCXS',0,4
	.word	7906
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	7906
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_FCX_Bits',0,5,180,2,3
	.word	39003
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,5,183,2,16,4,8
	.byte	'TST',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'TCL',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	7906
	.byte	6,24,2,35,0,8
	.byte	'RM',0,4
	.word	7906
	.byte	2,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	7906
	.byte	8,14,2,35,0,8
	.byte	'FXE',0,4
	.word	7906
	.byte	1,13,2,35,0,8
	.byte	'FUE',0,4
	.word	7906
	.byte	1,12,2,35,0,8
	.byte	'FZE',0,4
	.word	7906
	.byte	1,11,2,35,0,8
	.byte	'FVE',0,4
	.word	7906
	.byte	1,10,2,35,0,8
	.byte	'FIE',0,4
	.word	7906
	.byte	1,9,2,35,0,8
	.byte	'reserved_23',0,4
	.word	7906
	.byte	3,6,2,35,0,8
	.byte	'FX',0,4
	.word	7906
	.byte	1,5,2,35,0,8
	.byte	'FU',0,4
	.word	7906
	.byte	1,4,2,35,0,8
	.byte	'FZ',0,4
	.word	7906
	.byte	1,3,2,35,0,8
	.byte	'FV',0,4
	.word	7906
	.byte	1,2,2,35,0,8
	.byte	'FI',0,4
	.word	7906
	.byte	1,1,2,35,0,8
	.byte	'reserved_31',0,4
	.word	7906
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,5,202,2,3
	.word	39109
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,5,205,2,16,4,8
	.byte	'OPC',0,4
	.word	7906
	.byte	8,24,2,35,0,8
	.byte	'FMT',0,4
	.word	7906
	.byte	1,23,2,35,0,8
	.byte	'reserved_9',0,4
	.word	7906
	.byte	7,16,2,35,0,8
	.byte	'DREG',0,4
	.word	7906
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	7906
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,5,212,2,3
	.word	39458
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,5,215,2,16,4,8
	.byte	'PC',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,5,218,2,3
	.word	39618
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,5,221,2,16,4,8
	.byte	'SRC1',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,5,224,2,3
	.word	39699
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,5,227,2,16,4,8
	.byte	'SRC2',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,5,230,2,3
	.word	39786
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,5,233,2,16,4,8
	.byte	'SRC3',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,5,236,2,3
	.word	39873
	.byte	7
	.byte	'_Ifx_CPU_ICNT_Bits',0,5,239,2,16,4,8
	.byte	'CountValue',0,4
	.word	7906
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7906
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_ICNT_Bits',0,5,243,2,3
	.word	39960
	.byte	7
	.byte	'_Ifx_CPU_ICR_Bits',0,5,246,2,16,4,8
	.byte	'CCPN',0,4
	.word	7906
	.byte	10,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	7906
	.byte	5,17,2,35,0,8
	.byte	'IE',0,4
	.word	7906
	.byte	1,16,2,35,0,8
	.byte	'PIPN',0,4
	.word	7906
	.byte	10,6,2,35,0,8
	.byte	'reserved_26',0,4
	.word	7906
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_CPU_ICR_Bits',0,5,253,2,3
	.word	40051
	.byte	7
	.byte	'_Ifx_CPU_ISP_Bits',0,5,128,3,16,4,8
	.byte	'ISP',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_ISP_Bits',0,5,131,3,3
	.word	40194
	.byte	7
	.byte	'_Ifx_CPU_LCX_Bits',0,5,134,3,16,4,8
	.byte	'LCXO',0,4
	.word	7906
	.byte	16,16,2,35,0,8
	.byte	'LCXS',0,4
	.word	7906
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	7906
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_LCX_Bits',0,5,139,3,3
	.word	40260
	.byte	7
	.byte	'_Ifx_CPU_M1CNT_Bits',0,5,142,3,16,4,8
	.byte	'CountValue',0,4
	.word	7906
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7906
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M1CNT_Bits',0,5,146,3,3
	.word	40366
	.byte	7
	.byte	'_Ifx_CPU_M2CNT_Bits',0,5,149,3,16,4,8
	.byte	'CountValue',0,4
	.word	7906
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7906
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M2CNT_Bits',0,5,153,3,3
	.word	40459
	.byte	7
	.byte	'_Ifx_CPU_M3CNT_Bits',0,5,156,3,16,4,8
	.byte	'CountValue',0,4
	.word	7906
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7906
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M3CNT_Bits',0,5,160,3,3
	.word	40552
	.byte	7
	.byte	'_Ifx_CPU_PC_Bits',0,5,163,3,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'PC',0,4
	.word	7906
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_PC_Bits',0,5,167,3,3
	.word	40645
	.byte	7
	.byte	'_Ifx_CPU_PCON0_Bits',0,5,170,3,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'PCBYP',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	7906
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON0_Bits',0,5,175,3,3
	.word	40730
	.byte	7
	.byte	'_Ifx_CPU_PCON1_Bits',0,5,178,3,16,4,8
	.byte	'PCINV',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'PBINV',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	7906
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON1_Bits',0,5,183,3,3
	.word	40846
	.byte	7
	.byte	'_Ifx_CPU_PCON2_Bits',0,5,186,3,16,4,8
	.byte	'PCACHE_SZE',0,4
	.word	7906
	.byte	16,16,2,35,0,8
	.byte	'PSCRATCH_SZE',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON2_Bits',0,5,190,3,3
	.word	40957
	.byte	7
	.byte	'_Ifx_CPU_PCXI_Bits',0,5,193,3,16,4,8
	.byte	'PCXO',0,4
	.word	7906
	.byte	16,16,2,35,0,8
	.byte	'PCXS',0,4
	.word	7906
	.byte	4,12,2,35,0,8
	.byte	'UL',0,4
	.word	7906
	.byte	1,11,2,35,0,8
	.byte	'PIE',0,4
	.word	7906
	.byte	1,10,2,35,0,8
	.byte	'PCPN',0,4
	.word	7906
	.byte	10,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCXI_Bits',0,5,200,3,3
	.word	41058
	.byte	7
	.byte	'_Ifx_CPU_PIEAR_Bits',0,5,203,3,16,4,8
	.byte	'TA',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_PIEAR_Bits',0,5,206,3,3
	.word	41188
	.byte	7
	.byte	'_Ifx_CPU_PIETR_Bits',0,5,209,3,16,4,8
	.byte	'IED',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'IE_T',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'IE_C',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'IE_S',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'IE_BI',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'E_INFO',0,4
	.word	7906
	.byte	6,21,2,35,0,8
	.byte	'IE_DUAL',0,4
	.word	7906
	.byte	1,20,2,35,0,8
	.byte	'IE_SP',0,4
	.word	7906
	.byte	1,19,2,35,0,8
	.byte	'IE_BS',0,4
	.word	7906
	.byte	1,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	7906
	.byte	18,0,2,35,0,0,4
	.byte	'Ifx_CPU_PIETR_Bits',0,5,221,3,3
	.word	41257
	.byte	7
	.byte	'_Ifx_CPU_PMA0_Bits',0,5,224,3,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	13,19,2,35,0,8
	.byte	'DAC',0,4
	.word	7906
	.byte	3,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA0_Bits',0,5,229,3,3
	.word	41486
	.byte	7
	.byte	'_Ifx_CPU_PMA1_Bits',0,5,232,3,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	14,18,2,35,0,8
	.byte	'CAC',0,4
	.word	7906
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA1_Bits',0,5,237,3,3
	.word	41599
	.byte	7
	.byte	'_Ifx_CPU_PMA2_Bits',0,5,240,3,16,4,8
	.byte	'PSI',0,4
	.word	7906
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7906
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA2_Bits',0,5,244,3,3
	.word	41712
	.byte	7
	.byte	'_Ifx_CPU_PSTR_Bits',0,5,247,3,16,4,8
	.byte	'FRE',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'FBE',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7906
	.byte	9,20,2,35,0,8
	.byte	'FPE',0,4
	.word	7906
	.byte	1,19,2,35,0,8
	.byte	'reserved_13',0,4
	.word	7906
	.byte	1,18,2,35,0,8
	.byte	'FME',0,4
	.word	7906
	.byte	1,17,2,35,0,8
	.byte	'reserved_15',0,4
	.word	7906
	.byte	17,0,2,35,0,0,4
	.byte	'Ifx_CPU_PSTR_Bits',0,5,129,4,3
	.word	41803
	.byte	7
	.byte	'_Ifx_CPU_PSW_Bits',0,5,132,4,16,4,8
	.byte	'CDC',0,4
	.word	7906
	.byte	7,25,2,35,0,8
	.byte	'CDE',0,4
	.word	7906
	.byte	1,24,2,35,0,8
	.byte	'GW',0,4
	.word	7906
	.byte	1,23,2,35,0,8
	.byte	'IS',0,4
	.word	7906
	.byte	1,22,2,35,0,8
	.byte	'IO',0,4
	.word	7906
	.byte	2,20,2,35,0,8
	.byte	'PRS',0,4
	.word	7906
	.byte	2,18,2,35,0,8
	.byte	'S',0,4
	.word	7906
	.byte	1,17,2,35,0,8
	.byte	'reserved_15',0,4
	.word	7906
	.byte	12,5,2,35,0,8
	.byte	'SAV',0,4
	.word	7906
	.byte	1,4,2,35,0,8
	.byte	'AV',0,4
	.word	7906
	.byte	1,3,2,35,0,8
	.byte	'SV',0,4
	.word	7906
	.byte	1,2,2,35,0,8
	.byte	'V',0,4
	.word	7906
	.byte	1,1,2,35,0,8
	.byte	'C',0,4
	.word	7906
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_PSW_Bits',0,5,147,4,3
	.word	42006
	.byte	7
	.byte	'_Ifx_CPU_SEGEN_Bits',0,5,150,4,16,4,8
	.byte	'ADFLIP',0,4
	.word	7906
	.byte	8,24,2,35,0,8
	.byte	'ADTYPE',0,4
	.word	7906
	.byte	2,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	7906
	.byte	21,1,2,35,0,8
	.byte	'AE',0,4
	.word	7906
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_SEGEN_Bits',0,5,156,4,3
	.word	42249
	.byte	7
	.byte	'_Ifx_CPU_SMACON_Bits',0,5,159,4,16,4,8
	.byte	'PC',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'PT',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7906
	.byte	5,24,2,35,0,8
	.byte	'DC',0,4
	.word	7906
	.byte	1,23,2,35,0,8
	.byte	'reserved_9',0,4
	.word	7906
	.byte	1,22,2,35,0,8
	.byte	'DT',0,4
	.word	7906
	.byte	1,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	7906
	.byte	13,8,2,35,0,8
	.byte	'IODT',0,4
	.word	7906
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	7906
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_CPU_SMACON_Bits',0,5,171,4,3
	.word	42377
	.byte	7
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,5,174,4,16,4,8
	.byte	'EN',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,5,177,4,3
	.word	42618
	.byte	7
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,5,180,4,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,5,183,4,3
	.word	42701
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,5,186,4,16,4,8
	.byte	'EN',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,5,189,4,3
	.word	42792
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,5,192,4,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,5,195,4,3
	.word	42883
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,5,198,4,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	5,27,2,35,0,8
	.byte	'ADDR',0,4
	.word	7906
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,5,202,4,3
	.word	42982
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,5,205,4,16,4,8
	.byte	'reserved_0',0,4
	.word	7906
	.byte	5,27,2,35,0,8
	.byte	'ADDR',0,4
	.word	7906
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,5,209,4,3
	.word	43089
	.byte	7
	.byte	'_Ifx_CPU_SWEVT_Bits',0,5,212,4,16,4,8
	.byte	'EVTA',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'BBM',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'BOD',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'SUSP',0,4
	.word	7906
	.byte	1,26,2,35,0,8
	.byte	'CNT',0,4
	.word	7906
	.byte	2,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7906
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_SWEVT_Bits',0,5,220,4,3
	.word	43196
	.byte	7
	.byte	'_Ifx_CPU_SYSCON_Bits',0,5,223,4,16,4,8
	.byte	'FCDSF',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'PROTEN',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'TPROTEN',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'IS',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'IT',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7906
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SYSCON_Bits',0,5,231,4,3
	.word	43350
	.byte	7
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,5,234,4,16,4,8
	.byte	'ASI',0,4
	.word	7906
	.byte	5,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7906
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,5,238,4,3
	.word	43511
	.byte	7
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,5,241,4,16,4,8
	.byte	'TEXP0',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'TEXP1',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'TEXP2',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7906
	.byte	13,16,2,35,0,8
	.byte	'TTRAP',0,4
	.word	7906
	.byte	1,15,2,35,0,8
	.byte	'reserved_17',0,4
	.word	7906
	.byte	15,0,2,35,0,0,4
	.byte	'Ifx_CPU_TPS_CON_Bits',0,5,249,4,3
	.word	43609
	.byte	7
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,5,252,4,16,4,8
	.byte	'Timer',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,5,255,4,3
	.word	43781
	.byte	7
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,5,130,5,16,4,8
	.byte	'ADDR',0,4
	.word	7906
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_TR_ADR_Bits',0,5,133,5,3
	.word	43861
	.byte	7
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,5,136,5,16,4,8
	.byte	'EVTA',0,4
	.word	7906
	.byte	3,29,2,35,0,8
	.byte	'BBM',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'BOD',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'SUSP',0,4
	.word	7906
	.byte	1,26,2,35,0,8
	.byte	'CNT',0,4
	.word	7906
	.byte	2,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7906
	.byte	4,20,2,35,0,8
	.byte	'TYP',0,4
	.word	7906
	.byte	1,19,2,35,0,8
	.byte	'RNG',0,4
	.word	7906
	.byte	1,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	7906
	.byte	1,17,2,35,0,8
	.byte	'ASI_EN',0,4
	.word	7906
	.byte	1,16,2,35,0,8
	.byte	'ASI',0,4
	.word	7906
	.byte	5,11,2,35,0,8
	.byte	'reserved_21',0,4
	.word	7906
	.byte	6,5,2,35,0,8
	.byte	'AST',0,4
	.word	7906
	.byte	1,4,2,35,0,8
	.byte	'ALD',0,4
	.word	7906
	.byte	1,3,2,35,0,8
	.byte	'reserved_29',0,4
	.word	7906
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_CPU_TR_EVT_Bits',0,5,153,5,3
	.word	43934
	.byte	7
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,5,156,5,16,4,8
	.byte	'T0',0,4
	.word	7906
	.byte	1,31,2,35,0,8
	.byte	'T1',0,4
	.word	7906
	.byte	1,30,2,35,0,8
	.byte	'T2',0,4
	.word	7906
	.byte	1,29,2,35,0,8
	.byte	'T3',0,4
	.word	7906
	.byte	1,28,2,35,0,8
	.byte	'T4',0,4
	.word	7906
	.byte	1,27,2,35,0,8
	.byte	'T5',0,4
	.word	7906
	.byte	1,26,2,35,0,8
	.byte	'T6',0,4
	.word	7906
	.byte	1,25,2,35,0,8
	.byte	'T7',0,4
	.word	7906
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7906
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,5,167,5,3
	.word	44252
	.byte	9,5,175,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35470
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_A',0,5,180,5,3
	.word	44447
	.byte	9,5,183,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35531
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_BIV',0,5,188,5,3
	.word	44506
	.byte	9,5,191,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35610
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_BTV',0,5,196,5,3
	.word	44567
	.byte	9,5,199,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35696
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CCNT',0,5,204,5,3
	.word	44628
	.byte	9,5,207,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35785
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CCTRL',0,5,212,5,3
	.word	44690
	.byte	9,5,215,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35931
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_COMPAT',0,5,220,5,3
	.word	44753
	.byte	9,5,223,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36058
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CORE_ID',0,5,228,5,3
	.word	44817
	.byte	9,5,231,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36156
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPR_L',0,5,236,5,3
	.word	44882
	.byte	9,5,239,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36249
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPR_U',0,5,244,5,3
	.word	44945
	.byte	9,5,247,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36342
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPU_ID',0,5,252,5,3
	.word	45008
	.byte	9,5,255,5,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36449
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPXE',0,5,132,6,3
	.word	45072
	.byte	9,5,135,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36536
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CREVT',0,5,140,6,3
	.word	45134
	.byte	9,5,143,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36690
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CUS_ID',0,5,148,6,3
	.word	45197
	.byte	9,5,151,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36784
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_D',0,5,156,6,3
	.word	45261
	.byte	9,5,159,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36847
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DATR',0,5,164,6,3
	.word	45320
	.byte	9,5,167,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37065
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DBGSR',0,5,172,6,3
	.word	45382
	.byte	9,5,175,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37280
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DBGTCR',0,5,180,6,3
	.word	45445
	.byte	9,5,183,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37374
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCON0',0,5,188,6,3
	.word	45509
	.byte	9,5,191,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37490
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCON2',0,5,196,6,3
	.word	45572
	.byte	9,5,199,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37591
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCX',0,5,204,6,3
	.word	45635
	.byte	9,5,207,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37684
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DEADD',0,5,212,6,3
	.word	45696
	.byte	9,5,215,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37764
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DIEAR',0,5,220,6,3
	.word	45759
	.byte	9,5,223,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37833
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DIETR',0,5,228,6,3
	.word	45822
	.byte	9,5,231,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38062
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DMS',0,5,236,6,3
	.word	45885
	.byte	9,5,239,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38155
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPR_L',0,5,244,6,3
	.word	45946
	.byte	9,5,247,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38250
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPR_U',0,5,252,6,3
	.word	46009
	.byte	9,5,255,6,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38345
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPRE',0,5,132,7,3
	.word	46072
	.byte	9,5,135,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38435
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPWE',0,5,140,7,3
	.word	46134
	.byte	9,5,143,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38525
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DSTR',0,5,148,7,3
	.word	46196
	.byte	9,5,151,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38849
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_EXEVT',0,5,156,7,3
	.word	46258
	.byte	9,5,159,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39003
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FCX',0,5,164,7,3
	.word	46321
	.byte	9,5,167,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39109
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,5,172,7,3
	.word	46382
	.byte	9,5,175,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39458
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,5,180,7,3
	.word	46452
	.byte	9,5,183,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39618
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,5,188,7,3
	.word	46522
	.byte	9,5,191,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39699
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,5,196,7,3
	.word	46591
	.byte	9,5,199,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39786
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,5,204,7,3
	.word	46662
	.byte	9,5,207,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39873
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,5,212,7,3
	.word	46733
	.byte	9,5,215,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39960
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ICNT',0,5,220,7,3
	.word	46804
	.byte	9,5,223,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40051
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ICR',0,5,228,7,3
	.word	46866
	.byte	9,5,231,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40194
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ISP',0,5,236,7,3
	.word	46927
	.byte	9,5,239,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40260
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_LCX',0,5,244,7,3
	.word	46988
	.byte	9,5,247,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40366
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M1CNT',0,5,252,7,3
	.word	47049
	.byte	9,5,255,7,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40459
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M2CNT',0,5,132,8,3
	.word	47112
	.byte	9,5,135,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40552
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M3CNT',0,5,140,8,3
	.word	47175
	.byte	9,5,143,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40645
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PC',0,5,148,8,3
	.word	47238
	.byte	9,5,151,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40730
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON0',0,5,156,8,3
	.word	47298
	.byte	9,5,159,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40846
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON1',0,5,164,8,3
	.word	47361
	.byte	9,5,167,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40957
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON2',0,5,172,8,3
	.word	47424
	.byte	9,5,175,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41058
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCXI',0,5,180,8,3
	.word	47487
	.byte	9,5,183,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41188
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PIEAR',0,5,188,8,3
	.word	47549
	.byte	9,5,191,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41257
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PIETR',0,5,196,8,3
	.word	47612
	.byte	9,5,199,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41486
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA0',0,5,204,8,3
	.word	47675
	.byte	9,5,207,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41599
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA1',0,5,212,8,3
	.word	47737
	.byte	9,5,215,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41712
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA2',0,5,220,8,3
	.word	47799
	.byte	9,5,223,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41803
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PSTR',0,5,228,8,3
	.word	47861
	.byte	9,5,231,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42006
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PSW',0,5,236,8,3
	.word	47923
	.byte	9,5,239,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42249
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SEGEN',0,5,244,8,3
	.word	47984
	.byte	9,5,247,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42377
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SMACON',0,5,252,8,3
	.word	48047
	.byte	9,5,255,8,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42618
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENA',0,5,132,9,3
	.word	48111
	.byte	9,5,135,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42701
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENB',0,5,140,9,3
	.word	48181
	.byte	9,5,143,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42792
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,5,148,9,3
	.word	48251
	.byte	9,5,151,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42883
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,5,156,9,3
	.word	48325
	.byte	9,5,159,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42982
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,5,164,9,3
	.word	48399
	.byte	9,5,167,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43089
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,5,172,9,3
	.word	48469
	.byte	9,5,175,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43196
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SWEVT',0,5,180,9,3
	.word	48539
	.byte	9,5,183,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43350
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SYSCON',0,5,188,9,3
	.word	48602
	.byte	9,5,191,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43511
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TASK_ASI',0,5,196,9,3
	.word	48666
	.byte	9,5,199,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43609
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TPS_CON',0,5,204,9,3
	.word	48732
	.byte	9,5,207,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43781
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TPS_TIMER',0,5,212,9,3
	.word	48797
	.byte	9,5,215,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43861
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TR_ADR',0,5,220,9,3
	.word	48864
	.byte	9,5,223,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43934
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TR_EVT',0,5,228,9,3
	.word	48928
	.byte	9,5,231,9,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	44252
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TRIG_ACC',0,5,236,9,3
	.word	48992
	.byte	7
	.byte	'_Ifx_CPU_CPR',0,5,247,9,25,8,10
	.byte	'L',0,4
	.word	44882
	.byte	2,35,0,10
	.byte	'U',0,4
	.word	44945
	.byte	2,35,4,0,13
	.word	49058
	.byte	4
	.byte	'Ifx_CPU_CPR',0,5,251,9,3
	.word	49100
	.byte	7
	.byte	'_Ifx_CPU_DPR',0,5,254,9,25,8,10
	.byte	'L',0,4
	.word	45946
	.byte	2,35,0,10
	.byte	'U',0,4
	.word	46009
	.byte	2,35,4,0,13
	.word	49126
	.byte	4
	.byte	'Ifx_CPU_DPR',0,5,130,10,3
	.word	49168
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN',0,5,133,10,25,16,10
	.byte	'LA',0,4
	.word	48399
	.byte	2,35,0,10
	.byte	'UA',0,4
	.word	48469
	.byte	2,35,4,10
	.byte	'ACCENA',0,4
	.word	48251
	.byte	2,35,8,10
	.byte	'ACCENB',0,4
	.word	48325
	.byte	2,35,12,0,13
	.word	49194
	.byte	4
	.byte	'Ifx_CPU_SPROT_RGN',0,5,139,10,3
	.word	49276
	.byte	7
	.byte	'_Ifx_CPU_TPS',0,5,142,10,25,16,10
	.byte	'CON',0,4
	.word	48732
	.byte	2,35,0,11,12
	.word	48797
	.byte	12,2,0,10
	.byte	'TIMER',0,12
	.word	49340
	.byte	2,35,4,0,13
	.word	49308
	.byte	4
	.byte	'Ifx_CPU_TPS',0,5,146,10,3
	.word	49365
	.byte	7
	.byte	'_Ifx_CPU_TR',0,5,149,10,25,8,10
	.byte	'EVT',0,4
	.word	48928
	.byte	2,35,0,10
	.byte	'ADR',0,4
	.word	48864
	.byte	2,35,4,0,13
	.word	49391
	.byte	4
	.byte	'Ifx_CPU_TR',0,5,153,10,3
	.word	49436
	.byte	7
	.byte	'_Ifx_SRC_SRCR_Bits',0,6,45,16,4,8
	.byte	'SRPN',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'SRE',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'TOS',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'ECC',0,1
	.word	231
	.byte	5,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	3,0,2,35,2,8
	.byte	'SRR',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'CLRR',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'SETR',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'IOV',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'IOVCLR',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'SWS',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'SWSCLR',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SRC_SRCR_Bits',0,6,62,3
	.word	49461
	.byte	9,6,70,9,4,10
	.byte	'U',0,4
	.word	921
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9191
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	49461
	.byte	2,35,0,0,4
	.byte	'Ifx_SRC_SRCR',0,6,75,3
	.word	49777
	.byte	7
	.byte	'_Ifx_SRC_ASCLIN',0,6,86,25,12,10
	.byte	'TX',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'RX',0,4
	.word	49777
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	49777
	.byte	2,35,8,0,13
	.word	49837
	.byte	4
	.byte	'Ifx_SRC_ASCLIN',0,6,91,3
	.word	49896
	.byte	7
	.byte	'_Ifx_SRC_BCUSPB',0,6,94,25,4,10
	.byte	'SBSRC',0,4
	.word	49777
	.byte	2,35,0,0,13
	.word	49924
	.byte	4
	.byte	'Ifx_SRC_BCUSPB',0,6,97,3
	.word	49961
	.byte	7
	.byte	'_Ifx_SRC_CAN',0,6,100,25,64,11,64
	.word	49777
	.byte	12,15,0,10
	.byte	'INT',0,64
	.word	50007
	.byte	2,35,0,0,13
	.word	49989
	.byte	4
	.byte	'Ifx_SRC_CAN',0,6,103,3
	.word	50030
	.byte	7
	.byte	'_Ifx_SRC_CAN1',0,6,106,25,32,11,32
	.word	49777
	.byte	12,7,0,10
	.byte	'INT',0,32
	.word	50074
	.byte	2,35,0,0,13
	.word	50055
	.byte	4
	.byte	'Ifx_SRC_CAN1',0,6,109,3
	.word	50097
	.byte	7
	.byte	'_Ifx_SRC_CCU6',0,6,112,25,16,10
	.byte	'SR0',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49777
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	49777
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	49777
	.byte	2,35,12,0,13
	.word	50123
	.byte	4
	.byte	'Ifx_SRC_CCU6',0,6,118,3
	.word	50195
	.byte	7
	.byte	'_Ifx_SRC_CERBERUS',0,6,121,25,8,11,8
	.word	49777
	.byte	12,1,0,10
	.byte	'SR',0,8
	.word	50244
	.byte	2,35,0,0,13
	.word	50221
	.byte	4
	.byte	'Ifx_SRC_CERBERUS',0,6,124,3
	.word	50266
	.byte	7
	.byte	'_Ifx_SRC_CPU',0,6,127,25,32,10
	.byte	'SBSRC',0,4
	.word	49777
	.byte	2,35,0,11,28
	.word	231
	.byte	12,27,0,10
	.byte	'reserved_4',0,28
	.word	50329
	.byte	2,35,4,0,13
	.word	50296
	.byte	4
	.byte	'Ifx_SRC_CPU',0,6,131,1,3
	.word	50359
	.byte	7
	.byte	'_Ifx_SRC_DMA',0,6,134,1,25,80,10
	.byte	'ERR',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'reserved_4',0,12
	.word	12431
	.byte	2,35,4,10
	.byte	'CH',0,64
	.word	50007
	.byte	2,35,16,0,13
	.word	50385
	.byte	4
	.byte	'Ifx_SRC_DMA',0,6,139,1,3
	.word	50450
	.byte	7
	.byte	'_Ifx_SRC_EMEM',0,6,142,1,25,4,10
	.byte	'SR',0,4
	.word	49777
	.byte	2,35,0,0,13
	.word	50476
	.byte	4
	.byte	'Ifx_SRC_EMEM',0,6,145,1,3
	.word	50509
	.byte	7
	.byte	'_Ifx_SRC_ERAY',0,6,148,1,25,80,10
	.byte	'INT',0,8
	.word	50244
	.byte	2,35,0,10
	.byte	'TINT',0,8
	.word	50244
	.byte	2,35,8,10
	.byte	'NDAT',0,8
	.word	50244
	.byte	2,35,16,10
	.byte	'MBSC',0,8
	.word	50244
	.byte	2,35,24,10
	.byte	'OBUSY',0,4
	.word	49777
	.byte	2,35,32,10
	.byte	'IBUSY',0,4
	.word	49777
	.byte	2,35,36,11,40
	.word	231
	.byte	12,39,0,10
	.byte	'reserved_28',0,40
	.word	50641
	.byte	2,35,40,0,13
	.word	50536
	.byte	4
	.byte	'Ifx_SRC_ERAY',0,6,157,1,3
	.word	50672
	.byte	7
	.byte	'_Ifx_SRC_ETH',0,6,160,1,25,4,10
	.byte	'SR',0,4
	.word	49777
	.byte	2,35,0,0,13
	.word	50699
	.byte	4
	.byte	'Ifx_SRC_ETH',0,6,163,1,3
	.word	50731
	.byte	7
	.byte	'_Ifx_SRC_EVR',0,6,166,1,25,8,10
	.byte	'WUT',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'SCDC',0,4
	.word	49777
	.byte	2,35,4,0,13
	.word	50757
	.byte	4
	.byte	'Ifx_SRC_EVR',0,6,170,1,3
	.word	50804
	.byte	7
	.byte	'_Ifx_SRC_FFT',0,6,173,1,25,12,10
	.byte	'DONE',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'ERR',0,4
	.word	49777
	.byte	2,35,4,10
	.byte	'RFS',0,4
	.word	49777
	.byte	2,35,8,0,13
	.word	50830
	.byte	4
	.byte	'Ifx_SRC_FFT',0,6,178,1,3
	.word	50890
	.byte	7
	.byte	'_Ifx_SRC_GPSR',0,6,181,1,25,128,12,10
	.byte	'SR0',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49777
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	49777
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	49777
	.byte	2,35,12,11,240,11
	.word	231
	.byte	12,239,11,0,10
	.byte	'reserved_10',0,240,11
	.word	50989
	.byte	2,35,16,0,13
	.word	50916
	.byte	4
	.byte	'Ifx_SRC_GPSR',0,6,188,1,3
	.word	51023
	.byte	7
	.byte	'_Ifx_SRC_GPT12',0,6,191,1,25,48,10
	.byte	'CIRQ',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'T2',0,4
	.word	49777
	.byte	2,35,4,10
	.byte	'T3',0,4
	.word	49777
	.byte	2,35,8,10
	.byte	'T4',0,4
	.word	49777
	.byte	2,35,12,10
	.byte	'T5',0,4
	.word	49777
	.byte	2,35,16,10
	.byte	'T6',0,4
	.word	49777
	.byte	2,35,20,11,24
	.word	231
	.byte	12,23,0,10
	.byte	'reserved_18',0,24
	.word	51145
	.byte	2,35,24,0,13
	.word	51050
	.byte	4
	.byte	'Ifx_SRC_GPT12',0,6,200,1,3
	.word	51176
	.byte	7
	.byte	'_Ifx_SRC_GTM',0,6,203,1,25,192,11,10
	.byte	'AEIIRQ',0,4
	.word	49777
	.byte	2,35,0,11,236,2
	.word	231
	.byte	12,235,2,0,10
	.byte	'reserved_4',0,236,2
	.word	51240
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	49777
	.byte	3,35,240,2,10
	.byte	'reserved_174',0,12
	.word	12431
	.byte	3,35,244,2,11,32
	.word	50074
	.byte	12,0,0,10
	.byte	'TIM',0,32
	.word	51309
	.byte	3,35,128,3,11,224,7
	.word	231
	.byte	12,223,7,0,10
	.byte	'reserved_1A0',0,224,7
	.word	51332
	.byte	3,35,160,3,11,64
	.word	50074
	.byte	12,1,0,10
	.byte	'TOM',0,64
	.word	51367
	.byte	3,35,128,11,0,13
	.word	51204
	.byte	4
	.byte	'Ifx_SRC_GTM',0,6,212,1,3
	.word	51391
	.byte	7
	.byte	'_Ifx_SRC_HSM',0,6,215,1,25,8,10
	.byte	'HSM',0,8
	.word	50244
	.byte	2,35,0,0,13
	.word	51417
	.byte	4
	.byte	'Ifx_SRC_HSM',0,6,218,1,3
	.word	51450
	.byte	7
	.byte	'_Ifx_SRC_LMU',0,6,221,1,25,4,10
	.byte	'SR',0,4
	.word	49777
	.byte	2,35,0,0,13
	.word	51476
	.byte	4
	.byte	'Ifx_SRC_LMU',0,6,224,1,3
	.word	51508
	.byte	7
	.byte	'_Ifx_SRC_PMU',0,6,227,1,25,4,10
	.byte	'SR',0,4
	.word	49777
	.byte	2,35,0,0,13
	.word	51534
	.byte	4
	.byte	'Ifx_SRC_PMU',0,6,230,1,3
	.word	51566
	.byte	7
	.byte	'_Ifx_SRC_QSPI',0,6,233,1,25,24,10
	.byte	'TX',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'RX',0,4
	.word	49777
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	49777
	.byte	2,35,8,10
	.byte	'PT',0,4
	.word	49777
	.byte	2,35,12,10
	.byte	'HC',0,4
	.word	49777
	.byte	2,35,16,10
	.byte	'U',0,4
	.word	49777
	.byte	2,35,20,0,13
	.word	51592
	.byte	4
	.byte	'Ifx_SRC_QSPI',0,6,241,1,3
	.word	51685
	.byte	7
	.byte	'_Ifx_SRC_SCU',0,6,244,1,25,20,10
	.byte	'DTS',0,4
	.word	49777
	.byte	2,35,0,11,16
	.word	49777
	.byte	12,3,0,10
	.byte	'ERU',0,16
	.word	51744
	.byte	2,35,4,0,13
	.word	51712
	.byte	4
	.byte	'Ifx_SRC_SCU',0,6,248,1,3
	.word	51767
	.byte	7
	.byte	'_Ifx_SRC_SENT',0,6,251,1,25,16,10
	.byte	'SR',0,16
	.word	51744
	.byte	2,35,0,0,13
	.word	51793
	.byte	4
	.byte	'Ifx_SRC_SENT',0,6,254,1,3
	.word	51826
	.byte	7
	.byte	'_Ifx_SRC_SMU',0,6,129,2,25,12,11,12
	.word	49777
	.byte	12,2,0,10
	.byte	'SR',0,12
	.word	51872
	.byte	2,35,0,0,13
	.word	51853
	.byte	4
	.byte	'Ifx_SRC_SMU',0,6,132,2,3
	.word	51894
	.byte	7
	.byte	'_Ifx_SRC_STM',0,6,135,2,25,96,10
	.byte	'SR0',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49777
	.byte	2,35,4,11,88
	.word	231
	.byte	12,87,0,10
	.byte	'reserved_8',0,88
	.word	51965
	.byte	2,35,8,0,13
	.word	51920
	.byte	4
	.byte	'Ifx_SRC_STM',0,6,140,2,3
	.word	51995
	.byte	7
	.byte	'_Ifx_SRC_VADCCG',0,6,143,2,25,192,2,10
	.byte	'SR0',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49777
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	49777
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	49777
	.byte	2,35,12,11,176,2
	.word	231
	.byte	12,175,2,0,10
	.byte	'reserved_10',0,176,2
	.word	52096
	.byte	2,35,16,0,13
	.word	52021
	.byte	4
	.byte	'Ifx_SRC_VADCCG',0,6,150,2,3
	.word	52130
	.byte	7
	.byte	'_Ifx_SRC_VADCG',0,6,153,2,25,16,10
	.byte	'SR0',0,4
	.word	49777
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49777
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	49777
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	49777
	.byte	2,35,12,0,13
	.word	52159
	.byte	4
	.byte	'Ifx_SRC_VADCG',0,6,159,2,3
	.word	52233
	.byte	7
	.byte	'_Ifx_SRC_XBAR',0,6,162,2,25,4,10
	.byte	'SRC',0,4
	.word	49777
	.byte	2,35,0,0,13
	.word	52261
	.byte	4
	.byte	'Ifx_SRC_XBAR',0,6,165,2,3
	.word	52295
	.byte	7
	.byte	'_Ifx_SRC_GASCLIN',0,6,178,2,25,24,11,24
	.word	49837
	.byte	12,1,0,13
	.word	52345
	.byte	10
	.byte	'ASCLIN',0,24
	.word	52354
	.byte	2,35,0,0,13
	.word	52322
	.byte	4
	.byte	'Ifx_SRC_GASCLIN',0,6,181,2,3
	.word	52376
	.byte	7
	.byte	'_Ifx_SRC_GBCU',0,6,184,2,25,4,13
	.word	49924
	.byte	10
	.byte	'SPB',0,4
	.word	52426
	.byte	2,35,0,0,13
	.word	52406
	.byte	4
	.byte	'Ifx_SRC_GBCU',0,6,187,2,3
	.word	52445
	.byte	7
	.byte	'_Ifx_SRC_GCAN',0,6,190,2,25,96,11,64
	.word	49989
	.byte	12,0,0,13
	.word	52492
	.byte	10
	.byte	'CAN',0,64
	.word	52501
	.byte	2,35,0,11,32
	.word	50055
	.byte	12,0,0,13
	.word	52519
	.byte	10
	.byte	'CAN1',0,32
	.word	52528
	.byte	2,35,64,0,13
	.word	52472
	.byte	4
	.byte	'Ifx_SRC_GCAN',0,6,194,2,3
	.word	52548
	.byte	7
	.byte	'_Ifx_SRC_GCCU6',0,6,197,2,25,32,11,32
	.word	50123
	.byte	12,1,0,13
	.word	52596
	.byte	10
	.byte	'CCU6',0,32
	.word	52605
	.byte	2,35,0,0,13
	.word	52575
	.byte	4
	.byte	'Ifx_SRC_GCCU6',0,6,200,2,3
	.word	52625
	.byte	7
	.byte	'_Ifx_SRC_GCERBERUS',0,6,203,2,25,8,13
	.word	50221
	.byte	10
	.byte	'CERBERUS',0,8
	.word	52678
	.byte	2,35,0,0,13
	.word	52653
	.byte	4
	.byte	'Ifx_SRC_GCERBERUS',0,6,206,2,3
	.word	52702
	.byte	7
	.byte	'_Ifx_SRC_GCPU',0,6,209,2,25,32,11,32
	.word	50296
	.byte	12,0,0,13
	.word	52754
	.byte	10
	.byte	'CPU',0,32
	.word	52763
	.byte	2,35,0,0,13
	.word	52734
	.byte	4
	.byte	'Ifx_SRC_GCPU',0,6,212,2,3
	.word	52782
	.byte	7
	.byte	'_Ifx_SRC_GDMA',0,6,215,2,25,80,11,80
	.word	50385
	.byte	12,0,0,13
	.word	52829
	.byte	10
	.byte	'DMA',0,80
	.word	52838
	.byte	2,35,0,0,13
	.word	52809
	.byte	4
	.byte	'Ifx_SRC_GDMA',0,6,218,2,3
	.word	52857
	.byte	7
	.byte	'_Ifx_SRC_GEMEM',0,6,221,2,25,4,11,4
	.word	50476
	.byte	12,0,0,13
	.word	52905
	.byte	10
	.byte	'EMEM',0,4
	.word	52914
	.byte	2,35,0,0,13
	.word	52884
	.byte	4
	.byte	'Ifx_SRC_GEMEM',0,6,224,2,3
	.word	52934
	.byte	7
	.byte	'_Ifx_SRC_GERAY',0,6,227,2,25,80,11,80
	.word	50536
	.byte	12,0,0,13
	.word	52983
	.byte	10
	.byte	'ERAY',0,80
	.word	52992
	.byte	2,35,0,0,13
	.word	52962
	.byte	4
	.byte	'Ifx_SRC_GERAY',0,6,230,2,3
	.word	53012
	.byte	7
	.byte	'_Ifx_SRC_GETH',0,6,233,2,25,4,11,4
	.word	50699
	.byte	12,0,0,13
	.word	53060
	.byte	10
	.byte	'ETH',0,4
	.word	53069
	.byte	2,35,0,0,13
	.word	53040
	.byte	4
	.byte	'Ifx_SRC_GETH',0,6,236,2,3
	.word	53088
	.byte	7
	.byte	'_Ifx_SRC_GEVR',0,6,239,2,25,8,11,8
	.word	50757
	.byte	12,0,0,13
	.word	53135
	.byte	10
	.byte	'EVR',0,8
	.word	53144
	.byte	2,35,0,0,13
	.word	53115
	.byte	4
	.byte	'Ifx_SRC_GEVR',0,6,242,2,3
	.word	53163
	.byte	7
	.byte	'_Ifx_SRC_GFFT',0,6,245,2,25,12,11,12
	.word	50830
	.byte	12,0,0,13
	.word	53210
	.byte	10
	.byte	'FFT',0,12
	.word	53219
	.byte	2,35,0,0,13
	.word	53190
	.byte	4
	.byte	'Ifx_SRC_GFFT',0,6,248,2,3
	.word	53238
	.byte	7
	.byte	'_Ifx_SRC_GGPSR',0,6,251,2,25,128,12,11,128,12
	.word	50916
	.byte	12,0,0,13
	.word	53287
	.byte	10
	.byte	'GPSR',0,128,12
	.word	53297
	.byte	2,35,0,0,13
	.word	53265
	.byte	4
	.byte	'Ifx_SRC_GGPSR',0,6,254,2,3
	.word	53318
	.byte	7
	.byte	'_Ifx_SRC_GGPT12',0,6,129,3,25,48,11,48
	.word	51050
	.byte	12,0,0,13
	.word	53368
	.byte	10
	.byte	'GPT12',0,48
	.word	53377
	.byte	2,35,0,0,13
	.word	53346
	.byte	4
	.byte	'Ifx_SRC_GGPT12',0,6,132,3,3
	.word	53398
	.byte	7
	.byte	'_Ifx_SRC_GGTM',0,6,135,3,25,192,11,11,192,11
	.word	51204
	.byte	12,0,0,13
	.word	53448
	.byte	10
	.byte	'GTM',0,192,11
	.word	53458
	.byte	2,35,0,0,13
	.word	53427
	.byte	4
	.byte	'Ifx_SRC_GGTM',0,6,138,3,3
	.word	53478
	.byte	7
	.byte	'_Ifx_SRC_GHSM',0,6,141,3,25,8,11,8
	.word	51417
	.byte	12,0,0,13
	.word	53525
	.byte	10
	.byte	'HSM',0,8
	.word	53534
	.byte	2,35,0,0,13
	.word	53505
	.byte	4
	.byte	'Ifx_SRC_GHSM',0,6,144,3,3
	.word	53553
	.byte	7
	.byte	'_Ifx_SRC_GLMU',0,6,147,3,25,4,11,4
	.word	51476
	.byte	12,0,0,13
	.word	53600
	.byte	10
	.byte	'LMU',0,4
	.word	53609
	.byte	2,35,0,0,13
	.word	53580
	.byte	4
	.byte	'Ifx_SRC_GLMU',0,6,150,3,3
	.word	53628
	.byte	7
	.byte	'_Ifx_SRC_GPMU',0,6,153,3,25,8,11,8
	.word	51534
	.byte	12,1,0,13
	.word	53675
	.byte	10
	.byte	'PMU',0,8
	.word	53684
	.byte	2,35,0,0,13
	.word	53655
	.byte	4
	.byte	'Ifx_SRC_GPMU',0,6,156,3,3
	.word	53703
	.byte	7
	.byte	'_Ifx_SRC_GQSPI',0,6,159,3,25,96,11,96
	.word	51592
	.byte	12,3,0,13
	.word	53751
	.byte	10
	.byte	'QSPI',0,96
	.word	53760
	.byte	2,35,0,0,13
	.word	53730
	.byte	4
	.byte	'Ifx_SRC_GQSPI',0,6,162,3,3
	.word	53780
	.byte	7
	.byte	'_Ifx_SRC_GSCU',0,6,165,3,25,20,13
	.word	51712
	.byte	10
	.byte	'SCU',0,20
	.word	53828
	.byte	2,35,0,0,13
	.word	53808
	.byte	4
	.byte	'Ifx_SRC_GSCU',0,6,168,3,3
	.word	53847
	.byte	7
	.byte	'_Ifx_SRC_GSENT',0,6,171,3,25,16,11,16
	.word	51793
	.byte	12,0,0,13
	.word	53895
	.byte	10
	.byte	'SENT',0,16
	.word	53904
	.byte	2,35,0,0,13
	.word	53874
	.byte	4
	.byte	'Ifx_SRC_GSENT',0,6,174,3,3
	.word	53924
	.byte	7
	.byte	'_Ifx_SRC_GSMU',0,6,177,3,25,12,11,12
	.word	51853
	.byte	12,0,0,13
	.word	53972
	.byte	10
	.byte	'SMU',0,12
	.word	53981
	.byte	2,35,0,0,13
	.word	53952
	.byte	4
	.byte	'Ifx_SRC_GSMU',0,6,180,3,3
	.word	54000
	.byte	7
	.byte	'_Ifx_SRC_GSTM',0,6,183,3,25,96,11,96
	.word	51920
	.byte	12,0,0,13
	.word	54047
	.byte	10
	.byte	'STM',0,96
	.word	54056
	.byte	2,35,0,0,13
	.word	54027
	.byte	4
	.byte	'Ifx_SRC_GSTM',0,6,186,3,3
	.word	54075
	.byte	7
	.byte	'_Ifx_SRC_GVADC',0,6,189,3,25,224,4,11,64
	.word	52159
	.byte	12,3,0,13
	.word	54124
	.byte	10
	.byte	'G',0,64
	.word	54133
	.byte	2,35,0,11,224,1
	.word	231
	.byte	12,223,1,0,10
	.byte	'reserved_40',0,224,1
	.word	54149
	.byte	2,35,64,11,192,2
	.word	52021
	.byte	12,0,0,13
	.word	54182
	.byte	10
	.byte	'CG',0,192,2
	.word	54192
	.byte	3,35,160,2,0,13
	.word	54102
	.byte	4
	.byte	'Ifx_SRC_GVADC',0,6,194,3,3
	.word	54212
	.byte	7
	.byte	'_Ifx_SRC_GXBAR',0,6,197,3,25,4,13
	.word	52261
	.byte	10
	.byte	'XBAR',0,4
	.word	54261
	.byte	2,35,0,0,13
	.word	54240
	.byte	4
	.byte	'Ifx_SRC_GXBAR',0,6,200,3,3
	.word	54281
	.byte	4
	.byte	'Dma_StatusType',0,7,121,22
	.word	921
	.byte	4
	.byte	'Dma_ErrorStatusType',0,7,141,1,22
	.word	921
	.byte	14,7,147,1,9,1,15
	.byte	'DMA_CHANNEL0',0,0,15
	.byte	'DMA_CHANNEL1',0,1,15
	.byte	'DMA_CHANNEL2',0,2,15
	.byte	'DMA_CHANNEL3',0,3,15
	.byte	'DMA_CHANNEL4',0,4,15
	.byte	'DMA_CHANNEL5',0,5,15
	.byte	'DMA_CHANNEL6',0,6,15
	.byte	'DMA_CHANNEL7',0,7,15
	.byte	'DMA_CHANNEL8',0,8,15
	.byte	'DMA_CHANNEL9',0,9,15
	.byte	'DMA_CHANNEL10',0,10,15
	.byte	'DMA_CHANNEL11',0,11,15
	.byte	'DMA_CHANNEL12',0,12,15
	.byte	'DMA_CHANNEL13',0,13,15
	.byte	'DMA_CHANNEL14',0,14,15
	.byte	'DMA_CHANNEL15',0,15,15
	.byte	'DMA_CHANNEL16',0,16,15
	.byte	'DMA_CHANNEL17',0,17,15
	.byte	'DMA_CHANNEL18',0,18,15
	.byte	'DMA_CHANNEL19',0,19,15
	.byte	'DMA_CHANNEL20',0,20,15
	.byte	'DMA_CHANNEL21',0,21,15
	.byte	'DMA_CHANNEL22',0,22,15
	.byte	'DMA_CHANNEL23',0,23,15
	.byte	'DMA_CHANNEL24',0,24,15
	.byte	'DMA_CHANNEL25',0,25,15
	.byte	'DMA_CHANNEL26',0,26,15
	.byte	'DMA_CHANNEL27',0,27,15
	.byte	'DMA_CHANNEL28',0,28,15
	.byte	'DMA_CHANNEL29',0,29,15
	.byte	'DMA_CHANNEL30',0,30,15
	.byte	'DMA_CHANNEL31',0,31,15
	.byte	'DMA_CHANNEL32',0,32,15
	.byte	'DMA_CHANNEL33',0,33,15
	.byte	'DMA_CHANNEL34',0,34,15
	.byte	'DMA_CHANNEL35',0,35,15
	.byte	'DMA_CHANNEL36',0,36,15
	.byte	'DMA_CHANNEL37',0,37,15
	.byte	'DMA_CHANNEL38',0,38,15
	.byte	'DMA_CHANNEL39',0,39,15
	.byte	'DMA_CHANNEL40',0,40,15
	.byte	'DMA_CHANNEL41',0,41,15
	.byte	'DMA_CHANNEL42',0,42,15
	.byte	'DMA_CHANNEL43',0,43,15
	.byte	'DMA_CHANNEL44',0,44,15
	.byte	'DMA_CHANNEL45',0,45,15
	.byte	'DMA_CHANNEL46',0,46,15
	.byte	'DMA_CHANNEL47',0,47,15
	.byte	'DMA_CHANNEL48',0,48,15
	.byte	'DMA_CHANNEL49',0,49,15
	.byte	'DMA_CHANNEL50',0,50,15
	.byte	'DMA_CHANNEL51',0,51,15
	.byte	'DMA_CHANNEL52',0,52,15
	.byte	'DMA_CHANNEL53',0,53,15
	.byte	'DMA_CHANNEL54',0,54,15
	.byte	'DMA_CHANNEL55',0,55,15
	.byte	'DMA_CHANNEL56',0,56,15
	.byte	'DMA_CHANNEL57',0,57,15
	.byte	'DMA_CHANNEL58',0,58,15
	.byte	'DMA_CHANNEL59',0,59,15
	.byte	'DMA_CHANNEL60',0,60,15
	.byte	'DMA_CHANNEL61',0,61,15
	.byte	'DMA_CHANNEL62',0,62,15
	.byte	'DMA_CHANNEL63',0,63,15
	.byte	'DMA_CHANNEL64',0,192,0,15
	.byte	'DMA_CHANNEL65',0,193,0,15
	.byte	'DMA_CHANNEL66',0,194,0,15
	.byte	'DMA_CHANNEL67',0,195,0,15
	.byte	'DMA_CHANNEL68',0,196,0,15
	.byte	'DMA_CHANNEL69',0,197,0,15
	.byte	'DMA_CHANNEL70',0,198,0,15
	.byte	'DMA_CHANNEL71',0,199,0,15
	.byte	'DMA_CHANNEL72',0,200,0,15
	.byte	'DMA_CHANNEL73',0,201,0,15
	.byte	'DMA_CHANNEL74',0,202,0,15
	.byte	'DMA_CHANNEL75',0,203,0,15
	.byte	'DMA_CHANNEL76',0,204,0,15
	.byte	'DMA_CHANNEL77',0,205,0,15
	.byte	'DMA_CHANNEL78',0,206,0,15
	.byte	'DMA_CHANNEL79',0,207,0,15
	.byte	'DMA_CHANNEL80',0,208,0,15
	.byte	'DMA_CHANNEL81',0,209,0,15
	.byte	'DMA_CHANNEL82',0,210,0,15
	.byte	'DMA_CHANNEL83',0,211,0,15
	.byte	'DMA_CHANNEL84',0,212,0,15
	.byte	'DMA_CHANNEL85',0,213,0,15
	.byte	'DMA_CHANNEL86',0,214,0,15
	.byte	'DMA_CHANNEL87',0,215,0,15
	.byte	'DMA_CHANNEL88',0,216,0,15
	.byte	'DMA_CHANNEL89',0,217,0,15
	.byte	'DMA_CHANNEL90',0,218,0,15
	.byte	'DMA_CHANNEL91',0,219,0,15
	.byte	'DMA_CHANNEL92',0,220,0,15
	.byte	'DMA_CHANNEL93',0,221,0,15
	.byte	'DMA_CHANNEL94',0,222,0,15
	.byte	'DMA_CHANNEL95',0,223,0,15
	.byte	'DMA_CHANNEL96',0,224,0,15
	.byte	'DMA_CHANNEL97',0,225,0,15
	.byte	'DMA_CHANNEL98',0,226,0,15
	.byte	'DMA_CHANNEL99',0,227,0,15
	.byte	'DMA_CHANNEL100',0,228,0,15
	.byte	'DMA_CHANNEL101',0,229,0,15
	.byte	'DMA_CHANNEL102',0,230,0,15
	.byte	'DMA_CHANNEL103',0,231,0,15
	.byte	'DMA_CHANNEL104',0,232,0,15
	.byte	'DMA_CHANNEL105',0,233,0,15
	.byte	'DMA_CHANNEL106',0,234,0,15
	.byte	'DMA_CHANNEL107',0,235,0,15
	.byte	'DMA_CHANNEL108',0,236,0,15
	.byte	'DMA_CHANNEL109',0,237,0,15
	.byte	'DMA_CHANNEL110',0,238,0,15
	.byte	'DMA_CHANNEL111',0,239,0,15
	.byte	'DMA_CHANNEL112',0,240,0,15
	.byte	'DMA_CHANNEL113',0,241,0,15
	.byte	'DMA_CHANNEL114',0,242,0,15
	.byte	'DMA_CHANNEL115',0,243,0,15
	.byte	'DMA_CHANNEL116',0,244,0,15
	.byte	'DMA_CHANNEL117',0,245,0,15
	.byte	'DMA_CHANNEL118',0,246,0,15
	.byte	'DMA_CHANNEL119',0,247,0,15
	.byte	'DMA_CHANNEL120',0,248,0,15
	.byte	'DMA_CHANNEL121',0,249,0,15
	.byte	'DMA_CHANNEL122',0,250,0,15
	.byte	'DMA_CHANNEL123',0,251,0,15
	.byte	'DMA_CHANNEL124',0,252,0,15
	.byte	'DMA_CHANNEL125',0,253,0,15
	.byte	'DMA_CHANNEL126',0,254,0,15
	.byte	'DMA_CHANNEL127',0,255,0,15
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0,4
	.byte	'Dma_ChannelType',0,7,149,2,2
	.word	54361
	.byte	4
	.byte	'Spi_NumberOfDataType',0,8,169,7,16
	.word	262
	.byte	4
	.byte	'Spi_ChannelType',0,8,177,7,15
	.word	231
	.byte	4
	.byte	'Spi_JobType',0,8,185,7,16
	.word	262
	.byte	4
	.byte	'Spi_SequenceType',0,8,193,7,15
	.word	231
	.byte	16,1,1,3
	.word	56648
	.byte	4
	.byte	'Spi_NotifFunctionPtrType',0,8,238,7,15
	.word	56651
	.byte	7
	.byte	'Spi_ChannelConfig',0,8,247,7,16,12,10
	.byte	'DefaultData',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'DataConfig',0,2
	.word	262
	.byte	2,35,4,10
	.byte	'NoOfBuffers',0,2
	.word	262
	.byte	2,35,6,10
	.byte	'ChannelBufferType',0,1
	.word	231
	.byte	2,35,8,0,4
	.byte	'Spi_ChannelConfigType',0,8,144,8,3
	.word	56690
	.byte	4
	.byte	'Spi_DelayConfigType',0,8,162,8,16
	.word	299
	.byte	4
	.byte	'Spi_HWUnitType',0,8,170,8,15
	.word	231
	.byte	7
	.byte	'Spi_JobConfig',0,8,183,8,16,24,10
	.byte	'JobEndNotification',0,4
	.word	56656
	.byte	2,35,0,17
	.word	231
	.byte	3
	.word	56936
	.byte	10
	.byte	'ChannelLinkPtr',0,4
	.word	56941
	.byte	2,35,4,10
	.byte	'BaudRateConfig',0,4
	.word	299
	.byte	2,35,8,10
	.byte	'TimeDelayConfig',0,4
	.word	299
	.byte	2,35,12,10
	.byte	'CSPin',0,2
	.word	262
	.byte	2,35,16,10
	.byte	'CSPolarity',0,1
	.word	231
	.byte	2,35,18,10
	.byte	'ShiftClkConfig',0,1
	.word	231
	.byte	2,35,19,10
	.byte	'JobPriority',0,1
	.word	231
	.byte	2,35,20,10
	.byte	'HwUnit',0,1
	.word	231
	.byte	2,35,21,10
	.byte	'ChannelBasedChipSelect',0,1
	.word	231
	.byte	2,35,22,10
	.byte	'ParitySelection',0,1
	.word	231
	.byte	2,35,23,0,4
	.byte	'Spi_JobConfigType',0,8,241,8,2
	.word	56888
	.byte	3
	.word	56648
	.byte	7
	.byte	'Spi_SequenceConfig',0,8,252,8,16,16,10
	.byte	'SeqEndNotification',0,4
	.word	56656
	.byte	2,35,0,17
	.word	262
	.byte	3
	.word	57258
	.byte	10
	.byte	'JobLinkPtr',0,4
	.word	57263
	.byte	2,35,4,17
	.word	231
	.byte	3
	.word	57288
	.byte	10
	.byte	'SeqSharingJobs',0,4
	.word	57293
	.byte	2,35,8,10
	.byte	'JobsInParamSeq',0,2
	.word	262
	.byte	2,35,12,10
	.byte	'InterruptibleSequence',0,1
	.word	231
	.byte	2,35,14,0,4
	.byte	'Spi_SequenceConfigType',0,8,157,9,2
	.word	57205
	.byte	7
	.byte	'Spi_DmaConfigType',0,8,164,9,16,2,10
	.byte	'TxDmaChannel',0,1
	.word	54361
	.byte	2,35,0,10
	.byte	'RxDmaChannel',0,1
	.word	54361
	.byte	2,35,1,0,4
	.byte	'Spi_DmaConfigType',0,8,168,9,2
	.word	57410
	.byte	7
	.byte	'Spi_HWModuleConfig',0,8,174,9,16,16,17
	.word	299
	.byte	10
	.byte	'HWClkSetting',0,4
	.word	57531
	.byte	2,35,0,17
	.word	299
	.byte	10
	.byte	'HWCSPolaritySetting',0,4
	.word	57558
	.byte	2,35,4,17
	.word	299
	.byte	10
	.byte	'HWPinSetting',0,4
	.word	57592
	.byte	2,35,8,17
	.word	57410
	.byte	3
	.word	57619
	.byte	10
	.byte	'SpiDmaConfigPtr',0,4
	.word	57624
	.byte	2,35,12,0,4
	.byte	'Spi_HWModuleConfigType',0,8,187,9,2
	.word	57506
	.byte	7
	.byte	'Spi_BaudrateEconType',0,8,214,9,16,6,10
	.byte	'EconVal',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'QSPIHwUnit',0,1
	.word	231
	.byte	2,35,4,0,4
	.byte	'Spi_BaudrateEconType',0,8,222,9,2
	.word	57687
	.byte	7
	.byte	'Spi_LastChannelDataType',0,8,161,10,16,8,3
	.word	299
	.byte	10
	.byte	'LastDataPtr',0,4
	.word	57812
	.byte	2,35,0,10
	.byte	'DataWidth',0,2
	.word	262
	.byte	2,35,4,0,4
	.byte	'Spi_LastChannelDataType',0,8,165,10,2
	.word	57782
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,23,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0
	.byte	0,11,1,1,11,15,73,19,0,0,12,33,0,47,15,0,0,13,53,0,73,19,0,0,14,4,1,58,15,59,15,57,15,11,15,0,0,15,40
	.byte	0,3,8,28,13,0,0,16,21,0,54,15,39,12,0,0,17,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc',0
	.byte	0
	.byte	'..\\mcal_src\\Spi_Irq.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'Mcal_DmaLib.h',0,1,0,0
	.byte	'Spi.h',0,2,0,0,0
.L9:
.L7:

; ..\mcal_src\Spi_Irq.c	     1  /******************************************************************************
; ..\mcal_src\Spi_Irq.c	     2  **                                                                           **
; ..\mcal_src\Spi_Irq.c	     3  ** Copyright (C) Infineon Technologies (2015)                                **
; ..\mcal_src\Spi_Irq.c	     4  **                                                                           **
; ..\mcal_src\Spi_Irq.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Spi_Irq.c	     6  **                                                                           **
; ..\mcal_src\Spi_Irq.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Spi_Irq.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Spi_Irq.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Spi_Irq.c	    10  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    11  *******************************************************************************
; ..\mcal_src\Spi_Irq.c	    12  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    13  **  $FILENAME   : Spi_Irq.c $                                                **
; ..\mcal_src\Spi_Irq.c	    14  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    15  **  $CC VERSION : \main\31 $                                                 **
; ..\mcal_src\Spi_Irq.c	    16  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    17  **  $DATE       : 2016-06-27 $                                               **
; ..\mcal_src\Spi_Irq.c	    18  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Spi_Irq.c	    20  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Spi_Irq.c	    22  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    23  **  DESCRIPTION : This file contains                                         **
; ..\mcal_src\Spi_Irq.c	    24  **                - ISRs of SPI Handler driver.                              **
; ..\mcal_src\Spi_Irq.c	    25  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    26  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\Spi_Irq.c	    27  **                                                                           **
; ..\mcal_src\Spi_Irq.c	    28  ******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    29  
; ..\mcal_src\Spi_Irq.c	    30  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    31  **                      Includes                                              **
; ..\mcal_src\Spi_Irq.c	    32  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    33  
; ..\mcal_src\Spi_Irq.c	    34  /* Inclusion of Platform_Types.h and Compiler.h */
; ..\mcal_src\Spi_Irq.c	    35  #include "Std_Types.h"
; ..\mcal_src\Spi_Irq.c	    36  
; ..\mcal_src\Spi_Irq.c	    37  /* MCAL driver utility functions for setting interrupt priority initialization
; ..\mcal_src\Spi_Irq.c	    38      and interrupt category */
; ..\mcal_src\Spi_Irq.c	    39  #include "Irq.h"
; ..\mcal_src\Spi_Irq.c	    40  /* Global functions like Set/Reset END INIT protection bit,
; ..\mcal_src\Spi_Irq.c	    41    Enable/Disable interrupts, Automic write function */
; ..\mcal_src\Spi_Irq.c	    42  #include "Mcal.h"
; ..\mcal_src\Spi_Irq.c	    43  #if (IFX_SAFETLIB_USED == STD_ON)
; ..\mcal_src\Spi_Irq.c	    44  #include "SafeWdgIf_Cfg.h"
; ..\mcal_src\Spi_Irq.c	    45  #ifndef SAFEWDGQSPI_DMAUSED
; ..\mcal_src\Spi_Irq.c	    46  #if defined (SWDG_EXT_TLF_WDG_ONLY) || defined (SWDG_INT_CNR_WDG_EXT_TLF_WINDOW_WDG)
; ..\mcal_src\Spi_Irq.c	    47  extern void SafeWdgQspi_IsrTxRx(  void );
; ..\mcal_src\Spi_Irq.c	    48  #endif
; ..\mcal_src\Spi_Irq.c	    49  #endif
; ..\mcal_src\Spi_Irq.c	    50  #endif
; ..\mcal_src\Spi_Irq.c	    51  
; ..\mcal_src\Spi_Irq.c	    52  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Spi_Irq.c	    53  /* Own header file, this includes own configuration file also */
; ..\mcal_src\Spi_Irq.c	    54  #include "Spi.h"
; ..\mcal_src\Spi_Irq.c	    55  #endif
; ..\mcal_src\Spi_Irq.c	    56  
; ..\mcal_src\Spi_Irq.c	    57  
; ..\mcal_src\Spi_Irq.c	    58  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    59  **                      Imported Compiler Switch Checks                       **
; ..\mcal_src\Spi_Irq.c	    60  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    61  
; ..\mcal_src\Spi_Irq.c	    62  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    63  **                      Private Macro Definitions                             **
; ..\mcal_src\Spi_Irq.c	    64  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    65  
; ..\mcal_src\Spi_Irq.c	    66  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    67  **                      Private Type Definitions                              **
; ..\mcal_src\Spi_Irq.c	    68  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    69  
; ..\mcal_src\Spi_Irq.c	    70  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    71  **                      Private Function Declarations                         **
; ..\mcal_src\Spi_Irq.c	    72  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    73  
; ..\mcal_src\Spi_Irq.c	    74  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    75  **                      Global Constant Definitions                           **
; ..\mcal_src\Spi_Irq.c	    76  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    77  
; ..\mcal_src\Spi_Irq.c	    78  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    79  **                      Global Variable Definitions                           **
; ..\mcal_src\Spi_Irq.c	    80  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    81  
; ..\mcal_src\Spi_Irq.c	    82  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    83  **                      Private Constant Definitions                          **
; ..\mcal_src\Spi_Irq.c	    84  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    85  
; ..\mcal_src\Spi_Irq.c	    86  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    87  **                      Private Variable Definitions                          **
; ..\mcal_src\Spi_Irq.c	    88  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    89  
; ..\mcal_src\Spi_Irq.c	    90  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    91  **                      Private Function Definitions                          **
; ..\mcal_src\Spi_Irq.c	    92  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    93  
; ..\mcal_src\Spi_Irq.c	    94  /*******************************************************************************
; ..\mcal_src\Spi_Irq.c	    95  **                      Global Function Definitions                           **
; ..\mcal_src\Spi_Irq.c	    96  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	    97  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Spi_Irq.c	    98  #define IRQ_START_SEC_CODE
; ..\mcal_src\Spi_Irq.c	    99  #include "MemMap.h"
; ..\mcal_src\Spi_Irq.c	   100  #endif
; ..\mcal_src\Spi_Irq.c	   101  
; ..\mcal_src\Spi_Irq.c	   102  /******************************************************************************
; ..\mcal_src\Spi_Irq.c	   103  ** Syntax : void QSPIXERR_ISR(void)                                          **
; ..\mcal_src\Spi_Irq.c	   104  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   105  ** Service ID:       NA                                                      **
; ..\mcal_src\Spi_Irq.c	   106  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   107  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Spi_Irq.c	   108  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   109  ** Reentrancy:       reentrant                                               **
; ..\mcal_src\Spi_Irq.c	   110  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   111  ** Parameters (in):  none                                                    **
; ..\mcal_src\Spi_Irq.c	   112  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   113  ** Parameters (out): none                                                    **
; ..\mcal_src\Spi_Irq.c	   114  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   115  ** Return value:     none                                                    **
; ..\mcal_src\Spi_Irq.c	   116  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   117  ** Description : Service for Error interrupt of QSPIx                        **
; ..\mcal_src\Spi_Irq.c	   118  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   119  *****************************************************************************/
; ..\mcal_src\Spi_Irq.c	   120  /* Fixed for AI00248112 */
; ..\mcal_src\Spi_Irq.c	   121  #define SPI_QSPI0_ERRISR_ENABLE (STD_OFF)
; ..\mcal_src\Spi_Irq.c	   122  #if ((SPI_QSPI0_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   123    #undef SPI_QSPI0_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   124    #define SPI_QSPI0_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   125  #elif((SPI_QSPI0_USED == STD_ON) && (SPI_SLAVE_ENABLE == STD_ON))
; ..\mcal_src\Spi_Irq.c	   126     #if (SPI_SLAVE_MODULE_INDEX == SPI_QSPI0_INDEX)
; ..\mcal_src\Spi_Irq.c	   127    #undef SPI_QSPI0_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   128    #define SPI_QSPI0_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   129     #endif
; ..\mcal_src\Spi_Irq.c	   130  #endif
; ..\mcal_src\Spi_Irq.c	   131  #if (SPI_QSPI0_ERRISR_ENABLE == STD_ON)
; ..\mcal_src\Spi_Irq.c	   132  /* This node is for QSPI0_ESRC */
; ..\mcal_src\Spi_Irq.c	   133  #if ((IRQ_QSPI0_ERR_PRIO > 0) || (IRQ_QSPI0_ERR_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   134  #if ((IRQ_QSPI0_ERR_PRIO > 0) && (IRQ_QSPI0_ERR_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   135  IFX_INTERRUPT(QSPI0ERR_ISR, 0, IRQ_QSPI0_ERR_PRIO)
; ..\mcal_src\Spi_Irq.c	   136  #elif IRQ_QSPI0_ERR_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   137  ISR(QSPI0ERR_ISR)
; ..\mcal_src\Spi_Irq.c	   138  #endif
; ..\mcal_src\Spi_Irq.c	   139  {
; ..\mcal_src\Spi_Irq.c	   140    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   141  #if (IRQ_QSPI0_ERR_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   142    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   143  #endif
; ..\mcal_src\Spi_Irq.c	   144  
; ..\mcal_src\Spi_Irq.c	   145    /* Call QSPI0 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   146    Spi_IsrQspiError(SPI_QSPI0_INDEX);
; ..\mcal_src\Spi_Irq.c	   147  }
; ..\mcal_src\Spi_Irq.c	   148  #endif
; ..\mcal_src\Spi_Irq.c	   149  #endif
; ..\mcal_src\Spi_Irq.c	   150  
; ..\mcal_src\Spi_Irq.c	   151  #define SPI_QSPI1_ERRISR_ENABLE (STD_OFF)
; ..\mcal_src\Spi_Irq.c	   152  #if ((SPI_QSPI1_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   153    #undef SPI_QSPI1_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   154    #define SPI_QSPI1_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   155  #elif((SPI_QSPI1_USED == STD_ON) && (SPI_SLAVE_ENABLE == STD_ON))
; ..\mcal_src\Spi_Irq.c	   156     #if (SPI_SLAVE_MODULE_INDEX == SPI_QSPI1_INDEX)
; ..\mcal_src\Spi_Irq.c	   157    #undef SPI_QSPI1_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   158    #define SPI_QSPI1_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   159     #endif
; ..\mcal_src\Spi_Irq.c	   160  #endif
; ..\mcal_src\Spi_Irq.c	   161  #if (SPI_QSPI1_ERRISR_ENABLE == STD_ON)
; ..\mcal_src\Spi_Irq.c	   162  /* This node is for QSPI1_ESRC */
; ..\mcal_src\Spi_Irq.c	   163  #if ((IRQ_QSPI1_ERR_PRIO > 0) || (IRQ_QSPI1_ERR_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   164  #if ((IRQ_QSPI1_ERR_PRIO > 0) && (IRQ_QSPI1_ERR_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   165  IFX_INTERRUPT(QSPI1ERR_ISR, 0, IRQ_QSPI1_ERR_PRIO)
; ..\mcal_src\Spi_Irq.c	   166  #elif IRQ_QSPI1_ERR_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   167  ISR(QSPI1ERR_ISR)
; ..\mcal_src\Spi_Irq.c	   168  #endif
; ..\mcal_src\Spi_Irq.c	   169  {
; ..\mcal_src\Spi_Irq.c	   170    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   171  #if (IRQ_QSPI1_ERR_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   172    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   173  #endif
; ..\mcal_src\Spi_Irq.c	   174  
; ..\mcal_src\Spi_Irq.c	   175    /* Call QSPI1 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   176    Spi_IsrQspiError(SPI_QSPI1_INDEX);
; ..\mcal_src\Spi_Irq.c	   177  
; ..\mcal_src\Spi_Irq.c	   178  }
; ..\mcal_src\Spi_Irq.c	   179  #endif
; ..\mcal_src\Spi_Irq.c	   180  #endif
; ..\mcal_src\Spi_Irq.c	   181  
; ..\mcal_src\Spi_Irq.c	   182  #define SPI_QSPI2_ERRISR_ENABLE (STD_OFF)
; ..\mcal_src\Spi_Irq.c	   183  #if ((SPI_QSPI2_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   184    #undef SPI_QSPI2_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   185    #define SPI_QSPI2_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   186  #elif((SPI_QSPI2_USED == STD_ON) && (SPI_SLAVE_ENABLE == STD_ON))
; ..\mcal_src\Spi_Irq.c	   187     #if (SPI_SLAVE_MODULE_INDEX == SPI_QSPI2_INDEX)
; ..\mcal_src\Spi_Irq.c	   188    #undef SPI_QSPI2_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   189    #define SPI_QSPI2_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   190     #endif
; ..\mcal_src\Spi_Irq.c	   191  #endif
; ..\mcal_src\Spi_Irq.c	   192  #if (SPI_QSPI2_ERRISR_ENABLE == STD_ON)
; ..\mcal_src\Spi_Irq.c	   193  /* This node is for QSPI2_ESRC */
; ..\mcal_src\Spi_Irq.c	   194  #if ((IRQ_QSPI2_ERR_PRIO > 0) || (IRQ_QSPI2_ERR_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   195  #if ((IRQ_QSPI2_ERR_PRIO > 0) && (IRQ_QSPI2_ERR_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   196  IFX_INTERRUPT(QSPI2ERR_ISR, 0, IRQ_QSPI2_ERR_PRIO)
; ..\mcal_src\Spi_Irq.c	   197  #elif IRQ_QSPI2_ERR_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   198  ISR(QSPI2ERR_ISR)
; ..\mcal_src\Spi_Irq.c	   199  #endif
; ..\mcal_src\Spi_Irq.c	   200  {
; ..\mcal_src\Spi_Irq.c	   201    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   202  #if (IRQ_QSPI2_ERR_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   203    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   204  #endif
; ..\mcal_src\Spi_Irq.c	   205  
; ..\mcal_src\Spi_Irq.c	   206    /* Call QSPI2 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   207    Spi_IsrQspiError(SPI_QSPI2_INDEX);
; ..\mcal_src\Spi_Irq.c	   208  
; ..\mcal_src\Spi_Irq.c	   209  }
; ..\mcal_src\Spi_Irq.c	   210  #endif
; ..\mcal_src\Spi_Irq.c	   211  #endif
; ..\mcal_src\Spi_Irq.c	   212  
; ..\mcal_src\Spi_Irq.c	   213  #define SPI_QSPI3_ERRISR_ENABLE (STD_OFF)
; ..\mcal_src\Spi_Irq.c	   214  #if ((SPI_QSPI3_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   215    #undef SPI_QSPI3_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   216    #define SPI_QSPI3_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   217  #elif((SPI_QSPI3_USED == STD_ON) && (SPI_SLAVE_ENABLE == STD_ON))
; ..\mcal_src\Spi_Irq.c	   218     #if (SPI_SLAVE_MODULE_INDEX == SPI_QSPI3_INDEX)
; ..\mcal_src\Spi_Irq.c	   219    #undef SPI_QSPI3_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   220    #define SPI_QSPI3_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   221     #endif
; ..\mcal_src\Spi_Irq.c	   222  #endif
; ..\mcal_src\Spi_Irq.c	   223  #if (SPI_QSPI3_ERRISR_ENABLE == STD_ON)
; ..\mcal_src\Spi_Irq.c	   224  /* This node is for QSPI3_ESRC */
; ..\mcal_src\Spi_Irq.c	   225  #if ((IRQ_QSPI3_ERR_PRIO > 0) || (IRQ_QSPI3_ERR_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   226  #if ((IRQ_QSPI3_ERR_PRIO > 0) && (IRQ_QSPI3_ERR_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   227  IFX_INTERRUPT(QSPI3ERR_ISR, 0, IRQ_QSPI3_ERR_PRIO)
; ..\mcal_src\Spi_Irq.c	   228  #elif IRQ_QSPI3_ERR_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   229  ISR(QSPI3ERR_ISR)
; ..\mcal_src\Spi_Irq.c	   230  #endif
; ..\mcal_src\Spi_Irq.c	   231  {
; ..\mcal_src\Spi_Irq.c	   232    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   233  #if (IRQ_QSPI3_ERR_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   234    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   235  #endif
; ..\mcal_src\Spi_Irq.c	   236  
; ..\mcal_src\Spi_Irq.c	   237    /* Call QSPI3 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   238    Spi_IsrQspiError(SPI_QSPI3_INDEX);
; ..\mcal_src\Spi_Irq.c	   239  
; ..\mcal_src\Spi_Irq.c	   240  }
; ..\mcal_src\Spi_Irq.c	   241  #endif
; ..\mcal_src\Spi_Irq.c	   242  #endif
; ..\mcal_src\Spi_Irq.c	   243  
; ..\mcal_src\Spi_Irq.c	   244  #define SPI_QSPI4_ERRISR_ENABLE (STD_OFF)
; ..\mcal_src\Spi_Irq.c	   245  #if ((SPI_QSPI4_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   246    #undef SPI_QSPI4_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   247    #define SPI_QSPI4_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   248  #elif((SPI_QSPI4_USED == STD_ON) && (SPI_SLAVE_ENABLE == STD_ON))
; ..\mcal_src\Spi_Irq.c	   249     #if (SPI_SLAVE_MODULE_INDEX == SPI_QSPI4_INDEX)
; ..\mcal_src\Spi_Irq.c	   250    #undef SPI_QSPI4_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   251    #define SPI_QSPI4_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   252     #endif
; ..\mcal_src\Spi_Irq.c	   253  #endif
; ..\mcal_src\Spi_Irq.c	   254  #if (SPI_QSPI4_ERRISR_ENABLE == STD_ON)
; ..\mcal_src\Spi_Irq.c	   255  /* This node is for QSPI4_ESRC */
; ..\mcal_src\Spi_Irq.c	   256  #if ((IRQ_QSPI4_ERR_PRIO > 0) || (IRQ_QSPI4_ERR_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   257  #if ((IRQ_QSPI4_ERR_PRIO > 0) && (IRQ_QSPI4_ERR_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   258  IFX_INTERRUPT(QSPI4ERR_ISR, 0, IRQ_QSPI4_ERR_PRIO)
; ..\mcal_src\Spi_Irq.c	   259  #elif IRQ_QSPI4_ERR_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   260  ISR(QSPI4ERR_ISR)
; ..\mcal_src\Spi_Irq.c	   261  #endif
; ..\mcal_src\Spi_Irq.c	   262  {
; ..\mcal_src\Spi_Irq.c	   263    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   264  #if (IRQ_QSPI4_ERR_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   265    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   266  #endif
; ..\mcal_src\Spi_Irq.c	   267  
; ..\mcal_src\Spi_Irq.c	   268    /* Call QSPI4 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   269    Spi_IsrQspiError(SPI_QSPI4_INDEX);
; ..\mcal_src\Spi_Irq.c	   270  
; ..\mcal_src\Spi_Irq.c	   271  }
; ..\mcal_src\Spi_Irq.c	   272  #endif
; ..\mcal_src\Spi_Irq.c	   273  #endif
; ..\mcal_src\Spi_Irq.c	   274  
; ..\mcal_src\Spi_Irq.c	   275  #define SPI_QSPI5_ERRISR_ENABLE (STD_OFF)
; ..\mcal_src\Spi_Irq.c	   276  #if ((SPI_QSPI5_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   277    #undef SPI_QSPI5_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   278    #define SPI_QSPI5_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   279  #elif((SPI_QSPI5_USED == STD_ON) && (SPI_SLAVE_ENABLE == STD_ON))
; ..\mcal_src\Spi_Irq.c	   280     #if (SPI_SLAVE_MODULE_INDEX == SPI_QSPI5_INDEX)
; ..\mcal_src\Spi_Irq.c	   281    #undef SPI_QSPI5_ERRISR_ENABLE
; ..\mcal_src\Spi_Irq.c	   282    #define SPI_QSPI5_ERRISR_ENABLE (STD_ON)
; ..\mcal_src\Spi_Irq.c	   283     #endif
; ..\mcal_src\Spi_Irq.c	   284  #endif
; ..\mcal_src\Spi_Irq.c	   285  #if (SPI_QSPI5_ERRISR_ENABLE == STD_ON)
; ..\mcal_src\Spi_Irq.c	   286  /* This node is for QSPI5_ESRC */
; ..\mcal_src\Spi_Irq.c	   287  #if ((IRQ_QSPI5_ERR_PRIO > 0) || (IRQ_QSPI5_ERR_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   288  #if ((IRQ_QSPI5_ERR_PRIO > 0) && (IRQ_QSPI5_ERR_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   289  IFX_INTERRUPT(QSPI5ERR_ISR, 0, IRQ_QSPI5_ERR_PRIO)
; ..\mcal_src\Spi_Irq.c	   290  #elif IRQ_QSPI5_ERR_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   291  ISR(QSPI5ERR_ISR)
; ..\mcal_src\Spi_Irq.c	   292  #endif
; ..\mcal_src\Spi_Irq.c	   293  {
; ..\mcal_src\Spi_Irq.c	   294    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   295  #if (IRQ_QSPI5_ERR_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   296    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   297  #endif
; ..\mcal_src\Spi_Irq.c	   298  
; ..\mcal_src\Spi_Irq.c	   299    /* Call QSPI5 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   300    Spi_IsrQspiError(SPI_QSPI5_INDEX);
; ..\mcal_src\Spi_Irq.c	   301  
; ..\mcal_src\Spi_Irq.c	   302  }
; ..\mcal_src\Spi_Irq.c	   303  #endif
; ..\mcal_src\Spi_Irq.c	   304  #endif
; ..\mcal_src\Spi_Irq.c	   305  
; ..\mcal_src\Spi_Irq.c	   306  
; ..\mcal_src\Spi_Irq.c	   307  /******************************************************************************
; ..\mcal_src\Spi_Irq.c	   308  ** Syntax : void QSPIXPT_ISR(void)                                           **
; ..\mcal_src\Spi_Irq.c	   309  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   310  ** Service ID:       NA                                                      **
; ..\mcal_src\Spi_Irq.c	   311  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   312  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Spi_Irq.c	   313  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   314  ** Reentrancy:       reentrant                                               **
; ..\mcal_src\Spi_Irq.c	   315  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   316  ** Parameters (in):  none                                                    **
; ..\mcal_src\Spi_Irq.c	   317  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   318  ** Parameters (out): none                                                    **
; ..\mcal_src\Spi_Irq.c	   319  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   320  ** Return value:     none                                                    **
; ..\mcal_src\Spi_Irq.c	   321  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   322  ** Description : Service for Phase transition interrupt (PT2) used by QSPIx  **
; ..\mcal_src\Spi_Irq.c	   323  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   324  *****************************************************************************/
; ..\mcal_src\Spi_Irq.c	   325  #if ((SPI_QSPI0_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   326  #if ((IRQ_QSPI0_PT_PRIO > 0) || (IRQ_QSPI0_PT_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   327  #if ((IRQ_QSPI0_PT_PRIO > 0) && (IRQ_QSPI0_PT_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   328  IFX_INTERRUPT(QSPI0PT_ISR, 0, IRQ_QSPI0_PT_PRIO)
; ..\mcal_src\Spi_Irq.c	   329  #elif IRQ_QSPI0_PT_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   330  ISR(QSPI0PT_ISR)
; ..\mcal_src\Spi_Irq.c	   331  #endif
; ..\mcal_src\Spi_Irq.c	   332  {
; ..\mcal_src\Spi_Irq.c	   333    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   334  #if (IRQ_QSPI0_PT_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   335    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   336  #endif
; ..\mcal_src\Spi_Irq.c	   337  
; ..\mcal_src\Spi_Irq.c	   338    /* Call QSPI0 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   339    Spi_IsrQspiPt(SPI_QSPI0_INDEX);
; ..\mcal_src\Spi_Irq.c	   340  }
; ..\mcal_src\Spi_Irq.c	   341  #endif
; ..\mcal_src\Spi_Irq.c	   342  #endif
; ..\mcal_src\Spi_Irq.c	   343  
; ..\mcal_src\Spi_Irq.c	   344  #if ((SPI_QSPI1_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   345  #if ((IRQ_QSPI1_PT_PRIO > 0) || (IRQ_QSPI1_PT_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   346  #if ((IRQ_QSPI1_PT_PRIO > 0) && (IRQ_QSPI1_PT_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   347  IFX_INTERRUPT(QSPI1PT_ISR, 0, IRQ_QSPI1_PT_PRIO)
; ..\mcal_src\Spi_Irq.c	   348  #elif IRQ_QSPI1_PT_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   349  ISR(QSPI1PT_ISR)
; ..\mcal_src\Spi_Irq.c	   350  #endif
; ..\mcal_src\Spi_Irq.c	   351  {
; ..\mcal_src\Spi_Irq.c	   352    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   353  #if (IRQ_QSPI1_PT_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   354    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   355  #endif
; ..\mcal_src\Spi_Irq.c	   356  
; ..\mcal_src\Spi_Irq.c	   357    /* Call QSPI1 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   358    Spi_IsrQspiPt(SPI_QSPI1_INDEX);
; ..\mcal_src\Spi_Irq.c	   359  }
; ..\mcal_src\Spi_Irq.c	   360  #endif
; ..\mcal_src\Spi_Irq.c	   361  #endif
; ..\mcal_src\Spi_Irq.c	   362  
; ..\mcal_src\Spi_Irq.c	   363  #if ((SPI_QSPI2_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   364  #if ((IRQ_QSPI2_PT_PRIO > 0) || (IRQ_QSPI2_PT_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   365  #if ((IRQ_QSPI2_PT_PRIO > 0) && (IRQ_QSPI2_PT_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   366  IFX_INTERRUPT(QSPI2PT_ISR, 0, IRQ_QSPI2_PT_PRIO)
; ..\mcal_src\Spi_Irq.c	   367  #elif IRQ_QSPI2_PT_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   368  ISR(QSPI2PT_ISR)
; ..\mcal_src\Spi_Irq.c	   369  #endif
; ..\mcal_src\Spi_Irq.c	   370  {
; ..\mcal_src\Spi_Irq.c	   371    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   372  #if (IRQ_QSPI2_PT_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   373    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   374  #endif
; ..\mcal_src\Spi_Irq.c	   375  
; ..\mcal_src\Spi_Irq.c	   376    /* Call QSPI2 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   377    Spi_IsrQspiPt(SPI_QSPI2_INDEX);
; ..\mcal_src\Spi_Irq.c	   378  }
; ..\mcal_src\Spi_Irq.c	   379  #endif
; ..\mcal_src\Spi_Irq.c	   380  #endif
; ..\mcal_src\Spi_Irq.c	   381  
; ..\mcal_src\Spi_Irq.c	   382  #if ((SPI_QSPI3_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   383  #if ((IRQ_QSPI3_PT_PRIO > 0) || (IRQ_QSPI3_PT_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   384  #if ((IRQ_QSPI3_PT_PRIO > 0) && (IRQ_QSPI3_PT_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   385  IFX_INTERRUPT(QSPI3PT_ISR, 0, IRQ_QSPI3_PT_PRIO)
; ..\mcal_src\Spi_Irq.c	   386  #elif IRQ_QSPI3_PT_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   387  ISR(QSPI3PT_ISR)
; ..\mcal_src\Spi_Irq.c	   388  #endif
; ..\mcal_src\Spi_Irq.c	   389  {
; ..\mcal_src\Spi_Irq.c	   390    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   391  #if (IRQ_QSPI3_PT_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   392    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   393  #endif
; ..\mcal_src\Spi_Irq.c	   394  
; ..\mcal_src\Spi_Irq.c	   395    /* Call QSPI3 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   396    Spi_IsrQspiPt(SPI_QSPI3_INDEX);
; ..\mcal_src\Spi_Irq.c	   397  }
; ..\mcal_src\Spi_Irq.c	   398  #endif
; ..\mcal_src\Spi_Irq.c	   399  #endif
; ..\mcal_src\Spi_Irq.c	   400  
; ..\mcal_src\Spi_Irq.c	   401  #if ((SPI_QSPI4_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   402  #if ((IRQ_QSPI4_PT_PRIO > 0) || (IRQ_QSPI4_PT_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   403  #if ((IRQ_QSPI4_PT_PRIO > 0) && (IRQ_QSPI4_PT_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   404  IFX_INTERRUPT(QSPI4PT_ISR, 0, IRQ_QSPI4_PT_PRIO)
; ..\mcal_src\Spi_Irq.c	   405  #elif IRQ_QSPI4_PT_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   406  ISR(QSPI4PT_ISR)
; ..\mcal_src\Spi_Irq.c	   407  #endif
; ..\mcal_src\Spi_Irq.c	   408  {
; ..\mcal_src\Spi_Irq.c	   409    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   410  #if (IRQ_QSPI4_PT_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   411    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   412  #endif
; ..\mcal_src\Spi_Irq.c	   413  
; ..\mcal_src\Spi_Irq.c	   414    /* Call QSPI4 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   415    Spi_IsrQspiPt(SPI_QSPI4_INDEX);
; ..\mcal_src\Spi_Irq.c	   416  }
; ..\mcal_src\Spi_Irq.c	   417  #endif
; ..\mcal_src\Spi_Irq.c	   418  #endif
; ..\mcal_src\Spi_Irq.c	   419  
; ..\mcal_src\Spi_Irq.c	   420  #if ((SPI_QSPI5_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   421  #if ((IRQ_QSPI5_PT_PRIO > 0) || (IRQ_QSPI5_PT_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   422  #if ((IRQ_QSPI5_PT_PRIO > 0) && (IRQ_QSPI5_PT_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   423  IFX_INTERRUPT(QSPI5PT_ISR, 0, IRQ_QSPI5_PT_PRIO)
; ..\mcal_src\Spi_Irq.c	   424  #elif IRQ_QSPI5_PT_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   425  ISR(QSPI5PT_ISR)
; ..\mcal_src\Spi_Irq.c	   426  #endif
; ..\mcal_src\Spi_Irq.c	   427  {
; ..\mcal_src\Spi_Irq.c	   428    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   429  #if (IRQ_QSPI5_PT_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   430    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   431  #endif
; ..\mcal_src\Spi_Irq.c	   432  
; ..\mcal_src\Spi_Irq.c	   433    /* Call QSPI5 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   434    Spi_IsrQspiPt(SPI_QSPI5_INDEX);
; ..\mcal_src\Spi_Irq.c	   435  }
; ..\mcal_src\Spi_Irq.c	   436  #endif
; ..\mcal_src\Spi_Irq.c	   437  #endif
; ..\mcal_src\Spi_Irq.c	   438  
; ..\mcal_src\Spi_Irq.c	   439  /******************************************************************************
; ..\mcal_src\Spi_Irq.c	   440  ** Syntax : void QSPIXUD_ISR(void)                                           **
; ..\mcal_src\Spi_Irq.c	   441  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   442  ** Service ID:       NA                                                      **
; ..\mcal_src\Spi_Irq.c	   443  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   444  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Spi_Irq.c	   445  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   446  ** Reentrancy:       reentrant                                               **
; ..\mcal_src\Spi_Irq.c	   447  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   448  ** Parameters (in):  none                                                    **
; ..\mcal_src\Spi_Irq.c	   449  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   450  ** Parameters (out): none                                                    **
; ..\mcal_src\Spi_Irq.c	   451  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   452  ** Return value:     none                                                    **
; ..\mcal_src\Spi_Irq.c	   453  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   454  ** Description : Service for User interrupt (PT1) used by QSPIx              **
; ..\mcal_src\Spi_Irq.c	   455  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   456  *****************************************************************************/
; ..\mcal_src\Spi_Irq.c	   457  #if ((SPI_QSPI0_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   458  #if ((IRQ_QSPI0_UD_PRIO > 0) || (IRQ_QSPI0_UD_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   459  #if ((IRQ_QSPI0_UD_PRIO > 0) && (IRQ_QSPI0_UD_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   460  IFX_INTERRUPT(QSPI0UD_ISR, 0, IRQ_QSPI0_UD_PRIO)
; ..\mcal_src\Spi_Irq.c	   461  #elif IRQ_QSPI0_UD_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   462  ISR(QSPI0UD_ISR)
; ..\mcal_src\Spi_Irq.c	   463  #endif
; ..\mcal_src\Spi_Irq.c	   464  {
; ..\mcal_src\Spi_Irq.c	   465    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   466  #if (IRQ_QSPI0_UD_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   467    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   468  #endif
; ..\mcal_src\Spi_Irq.c	   469  
; ..\mcal_src\Spi_Irq.c	   470    /* Call QSPI0 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   471    Spi_IsrQspiUsr(SPI_QSPI0_INDEX);
; ..\mcal_src\Spi_Irq.c	   472  }
; ..\mcal_src\Spi_Irq.c	   473  #endif
; ..\mcal_src\Spi_Irq.c	   474  #endif
; ..\mcal_src\Spi_Irq.c	   475  
; ..\mcal_src\Spi_Irq.c	   476  #if ((SPI_QSPI1_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   477  #if ((IRQ_QSPI1_UD_PRIO > 0) || (IRQ_QSPI1_UD_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   478  #if ((IRQ_QSPI1_UD_PRIO > 0) && (IRQ_QSPI1_UD_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   479  IFX_INTERRUPT(QSPI1UD_ISR, 0, IRQ_QSPI1_UD_PRIO)
; ..\mcal_src\Spi_Irq.c	   480  #elif IRQ_QSPI1_UD_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   481  ISR(QSPI1UD_ISR)
; ..\mcal_src\Spi_Irq.c	   482  #endif
; ..\mcal_src\Spi_Irq.c	   483  {
; ..\mcal_src\Spi_Irq.c	   484    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   485  #if (IRQ_QSPI1_UD_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   486    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   487  #endif
; ..\mcal_src\Spi_Irq.c	   488  
; ..\mcal_src\Spi_Irq.c	   489    /* Call QSPI1 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   490    Spi_IsrQspiUsr(SPI_QSPI1_INDEX);
; ..\mcal_src\Spi_Irq.c	   491  }
; ..\mcal_src\Spi_Irq.c	   492  #endif
; ..\mcal_src\Spi_Irq.c	   493  #endif
; ..\mcal_src\Spi_Irq.c	   494  
; ..\mcal_src\Spi_Irq.c	   495  #if ((SPI_QSPI2_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   496  #if ((IRQ_QSPI2_UD_PRIO > 0) || (IRQ_QSPI2_UD_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   497  #if ((IRQ_QSPI2_UD_PRIO > 0) && (IRQ_QSPI2_UD_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   498  IFX_INTERRUPT(QSPI2UD_ISR, 0, IRQ_QSPI2_UD_PRIO)
; ..\mcal_src\Spi_Irq.c	   499  #elif IRQ_QSPI2_UD_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   500  ISR(QSPI2UD_ISR)
; ..\mcal_src\Spi_Irq.c	   501  #endif
; ..\mcal_src\Spi_Irq.c	   502  {
; ..\mcal_src\Spi_Irq.c	   503    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   504  #if (IRQ_QSPI2_UD_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   505    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   506  #endif
; ..\mcal_src\Spi_Irq.c	   507  
; ..\mcal_src\Spi_Irq.c	   508    /* Call QSPI2 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   509    Spi_IsrQspiUsr(SPI_QSPI2_INDEX);
; ..\mcal_src\Spi_Irq.c	   510  }
; ..\mcal_src\Spi_Irq.c	   511  #endif
; ..\mcal_src\Spi_Irq.c	   512  #endif
; ..\mcal_src\Spi_Irq.c	   513  
; ..\mcal_src\Spi_Irq.c	   514  #if ((SPI_QSPI3_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   515  #if ((IRQ_QSPI3_UD_PRIO > 0) || (IRQ_QSPI3_UD_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   516  #if ((IRQ_QSPI3_UD_PRIO > 0) && (IRQ_QSPI3_UD_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   517  IFX_INTERRUPT(QSPI3UD_ISR, 0, IRQ_QSPI3_UD_PRIO)
; ..\mcal_src\Spi_Irq.c	   518  #elif IRQ_QSPI3_UD_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   519  ISR(QSPI3UD_ISR)
; ..\mcal_src\Spi_Irq.c	   520  #endif
; ..\mcal_src\Spi_Irq.c	   521  {
; ..\mcal_src\Spi_Irq.c	   522    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   523  #if (IRQ_QSPI3_UD_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   524    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   525  #endif
; ..\mcal_src\Spi_Irq.c	   526  
; ..\mcal_src\Spi_Irq.c	   527    /* Call QSPI3 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   528    Spi_IsrQspiUsr(SPI_QSPI3_INDEX);
; ..\mcal_src\Spi_Irq.c	   529  }
; ..\mcal_src\Spi_Irq.c	   530  #endif
; ..\mcal_src\Spi_Irq.c	   531  #endif
; ..\mcal_src\Spi_Irq.c	   532  
; ..\mcal_src\Spi_Irq.c	   533  #if ((SPI_QSPI4_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   534  #if ((IRQ_QSPI4_UD_PRIO > 0) || (IRQ_QSPI4_UD_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   535  #if ((IRQ_QSPI4_UD_PRIO > 0) && (IRQ_QSPI4_UD_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   536  IFX_INTERRUPT(QSPI4UD_ISR, 0, IRQ_QSPI4_UD_PRIO)
; ..\mcal_src\Spi_Irq.c	   537  #elif IRQ_QSPI4_UD_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   538  ISR(QSPI4UD_ISR)
; ..\mcal_src\Spi_Irq.c	   539  #endif
; ..\mcal_src\Spi_Irq.c	   540  {
; ..\mcal_src\Spi_Irq.c	   541    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   542  #if (IRQ_QSPI4_UD_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   543    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   544  #endif
; ..\mcal_src\Spi_Irq.c	   545  
; ..\mcal_src\Spi_Irq.c	   546    /* Call QSPI4 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   547    Spi_IsrQspiUsr(SPI_QSPI4_INDEX);
; ..\mcal_src\Spi_Irq.c	   548  }
; ..\mcal_src\Spi_Irq.c	   549  #endif
; ..\mcal_src\Spi_Irq.c	   550  #endif
; ..\mcal_src\Spi_Irq.c	   551  
; ..\mcal_src\Spi_Irq.c	   552  #if ((SPI_QSPI5_USED == STD_ON) && (SPI_LEVEL_DELIVERED != 0))
; ..\mcal_src\Spi_Irq.c	   553  #if ((IRQ_QSPI5_UD_PRIO > 0) || (IRQ_QSPI5_UD_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   554  #if ((IRQ_QSPI5_UD_PRIO > 0) && (IRQ_QSPI5_UD_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   555  IFX_INTERRUPT(QSPI5UD_ISR, 0, IRQ_QSPI5_UD_PRIO)
; ..\mcal_src\Spi_Irq.c	   556  #elif IRQ_QSPI5_UD_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   557  ISR(QSPI5UD_ISR)
; ..\mcal_src\Spi_Irq.c	   558  #endif
; ..\mcal_src\Spi_Irq.c	   559  {
; ..\mcal_src\Spi_Irq.c	   560    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   561  #if (IRQ_QSPI5_UD_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   562    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   563  #endif
; ..\mcal_src\Spi_Irq.c	   564  
; ..\mcal_src\Spi_Irq.c	   565    /* Call QSPI5 Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   566    Spi_IsrQspiUsr(SPI_QSPI5_INDEX);
; ..\mcal_src\Spi_Irq.c	   567  }
; ..\mcal_src\Spi_Irq.c	   568  #endif
; ..\mcal_src\Spi_Irq.c	   569  #endif
; ..\mcal_src\Spi_Irq.c	   570  
; ..\mcal_src\Spi_Irq.c	   571  #if (IFX_MCAL_USED == STD_ON)
; ..\mcal_src\Spi_Irq.c	   572  #define IRQ_STOP_SEC_CODE
; ..\mcal_src\Spi_Irq.c	   573  #include "MemMap.h"
; ..\mcal_src\Spi_Irq.c	   574  #endif
; ..\mcal_src\Spi_Irq.c	   575  
; ..\mcal_src\Spi_Irq.c	   576  
; ..\mcal_src\Spi_Irq.c	   577  
; ..\mcal_src\Spi_Irq.c	   578  #if (IFX_SAFETLIB_USED == STD_ON)
; ..\mcal_src\Spi_Irq.c	   579  #define IFX_IRQ_START_SEC_CODE_ASIL_B
; ..\mcal_src\Spi_Irq.c	   580  #include "Ifx_MemMap.h"
; ..\mcal_src\Spi_Irq.c	   581  #endif
; ..\mcal_src\Spi_Irq.c	   582  
; ..\mcal_src\Spi_Irq.c	   583  /******************************************************************************
; ..\mcal_src\Spi_Irq.c	   584  ** Syntax : void QSPIXRX_ISR(void)                                           **
; ..\mcal_src\Spi_Irq.c	   585  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   586  ** Service ID:       NA                                                      **
; ..\mcal_src\Spi_Irq.c	   587  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   588  ** Sync/Async:       Synchronous                                             **
; ..\mcal_src\Spi_Irq.c	   589  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   590  ** Reentrancy:       reentrant                                               **
; ..\mcal_src\Spi_Irq.c	   591  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   592  ** Parameters (in):  none                                                    **
; ..\mcal_src\Spi_Irq.c	   593  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   594  ** Parameters (out): none                                                    **
; ..\mcal_src\Spi_Irq.c	   595  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   596  ** Return value:     none                                                    **
; ..\mcal_src\Spi_Irq.c	   597  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   598  ** Description : Service for Rx  interrupt (RX) used by QSPIx                **
; ..\mcal_src\Spi_Irq.c	   599  **                                                                           **
; ..\mcal_src\Spi_Irq.c	   600  *******************************************************************************/
; ..\mcal_src\Spi_Irq.c	   601  
; ..\mcal_src\Spi_Irq.c	   602  #if (IFX_SAFETLIB_USED == STD_ON)
; ..\mcal_src\Spi_Irq.c	   603  #ifndef SAFEWDGQSPI_DMAUSED
; ..\mcal_src\Spi_Irq.c	   604  #if ((IRQ_QSPI0_RX_PRIO > 0) || (IRQ_QSPI0_RX_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   605  #if ((IRQ_QSPI0_RX_PRIO > 0) && (IRQ_QSPI0_RX_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   606  IFX_INTERRUPT(QSPI0RX_ISR, 0, IRQ_QSPI0_RX_PRIO)
; ..\mcal_src\Spi_Irq.c	   607  #elif IRQ_QSPI0_RX_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   608  ISR(QSPI0RX_ISR)
; ..\mcal_src\Spi_Irq.c	   609  #endif
; ..\mcal_src\Spi_Irq.c	   610  {
; ..\mcal_src\Spi_Irq.c	   611    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   612  #if (IRQ_QSPI0_RX_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   613    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   614  #endif
; ..\mcal_src\Spi_Irq.c	   615  
; ..\mcal_src\Spi_Irq.c	   616      /* Call QSPI0 Rx Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   617  
; ..\mcal_src\Spi_Irq.c	   618    #if defined (SWDG_EXT_TLF_WDG_ONLY) || defined (SWDG_INT_CNR_WDG_EXT_TLF_WINDOW_WDG)
; ..\mcal_src\Spi_Irq.c	   619    SafeWdgQspi_IsrTxRx();
; ..\mcal_src\Spi_Irq.c	   620  
; ..\mcal_src\Spi_Irq.c	   621    #endif
; ..\mcal_src\Spi_Irq.c	   622  }
; ..\mcal_src\Spi_Irq.c	   623  #endif
; ..\mcal_src\Spi_Irq.c	   624  #endif
; ..\mcal_src\Spi_Irq.c	   625  #endif
; ..\mcal_src\Spi_Irq.c	   626  
; ..\mcal_src\Spi_Irq.c	   627  #if ((IFX_SAFETLIB_USED == STD_ON) && (IFX_MCAL_USED == STD_OFF))
; ..\mcal_src\Spi_Irq.c	   628  #ifndef SAFEWDGQSPI_DMAUSED
; ..\mcal_src\Spi_Irq.c	   629  #if ((IRQ_QSPI1_RX_PRIO > 0) || (IRQ_QSPI1_RX_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   630  #if ((IRQ_QSPI1_RX_PRIO > 0) && (IRQ_QSPI1_RX_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   631  IFX_INTERRUPT(QSPI1RX_ISR, 0, IRQ_QSPI1_RX_PRIO)
; ..\mcal_src\Spi_Irq.c	   632  #elif IRQ_QSPI1_RX_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   633  ISR(QSPI1RX_ISR)
; ..\mcal_src\Spi_Irq.c	   634  #endif
; ..\mcal_src\Spi_Irq.c	   635  {
; ..\mcal_src\Spi_Irq.c	   636    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   637  #if (IRQ_QSPI1_RX_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   638    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   639  #endif
; ..\mcal_src\Spi_Irq.c	   640   
; ..\mcal_src\Spi_Irq.c	   641    #if defined (SWDG_EXT_TLF_WDG_ONLY) || defined (SWDG_INT_CNR_WDG_EXT_TLF_WINDOW_WDG)
; ..\mcal_src\Spi_Irq.c	   642    /* Call QSPI1 Rx Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   643    SafeWdgQspi_IsrTxRx();
; ..\mcal_src\Spi_Irq.c	   644    
; ..\mcal_src\Spi_Irq.c	   645    #endif
; ..\mcal_src\Spi_Irq.c	   646  }
; ..\mcal_src\Spi_Irq.c	   647  #endif
; ..\mcal_src\Spi_Irq.c	   648  #endif
; ..\mcal_src\Spi_Irq.c	   649  #endif
; ..\mcal_src\Spi_Irq.c	   650  
; ..\mcal_src\Spi_Irq.c	   651  #if ((IFX_SAFETLIB_USED == STD_ON) && (IFX_MCAL_USED == STD_OFF))
; ..\mcal_src\Spi_Irq.c	   652  #ifndef SAFEWDGQSPI_DMAUSED
; ..\mcal_src\Spi_Irq.c	   653  #if ((IRQ_QSPI2_RX_PRIO > 0) || (IRQ_QSPI2_RX_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   654  #if ((IRQ_QSPI2_RX_PRIO > 0) && (IRQ_QSPI2_RX_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   655  IFX_INTERRUPT(QSPI2RX_ISR, 0, IRQ_QSPI2_RX_PRIO)
; ..\mcal_src\Spi_Irq.c	   656  #elif IRQ_QSPI2_RX_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   657  ISR(QSPI2RX_ISR)
; ..\mcal_src\Spi_Irq.c	   658  #endif
; ..\mcal_src\Spi_Irq.c	   659  {
; ..\mcal_src\Spi_Irq.c	   660    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   661  #if (IRQ_QSPI2_RX_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   662    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   663  #endif
; ..\mcal_src\Spi_Irq.c	   664    
; ..\mcal_src\Spi_Irq.c	   665    #if defined (SWDG_EXT_TLF_WDG_ONLY) || defined (SWDG_INT_CNR_WDG_EXT_TLF_WINDOW_WDG)
; ..\mcal_src\Spi_Irq.c	   666    /* Call QSPI2 Rx Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   667    SafeWdgQspi_IsrTxRx();
; ..\mcal_src\Spi_Irq.c	   668  
; ..\mcal_src\Spi_Irq.c	   669    #endif
; ..\mcal_src\Spi_Irq.c	   670  }
; ..\mcal_src\Spi_Irq.c	   671  #endif
; ..\mcal_src\Spi_Irq.c	   672  #endif
; ..\mcal_src\Spi_Irq.c	   673  #endif
; ..\mcal_src\Spi_Irq.c	   674  
; ..\mcal_src\Spi_Irq.c	   675  #if ((IFX_SAFETLIB_USED == STD_ON) && (IFX_MCAL_USED == STD_OFF))
; ..\mcal_src\Spi_Irq.c	   676  #ifndef SAFEWDGQSPI_DMAUSED
; ..\mcal_src\Spi_Irq.c	   677  #if ((IRQ_QSPI3_RX_PRIO > 0) || (IRQ_QSPI3_RX_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   678  #if ((IRQ_QSPI3_RX_PRIO > 0) && (IRQ_QSPI3_RX_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   679  IFX_INTERRUPT(QSPI3RX_ISR, 0, IRQ_QSPI3_RX_PRIO)
; ..\mcal_src\Spi_Irq.c	   680  #elif IRQ_QSPI3_RX_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   681  ISR(QSPI3RX_ISR)
; ..\mcal_src\Spi_Irq.c	   682  #endif
; ..\mcal_src\Spi_Irq.c	   683  {
; ..\mcal_src\Spi_Irq.c	   684    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   685  #if (IRQ_QSPI3_RX_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   686    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   687  #endif
; ..\mcal_src\Spi_Irq.c	   688   
; ..\mcal_src\Spi_Irq.c	   689    #if defined (SWDG_EXT_TLF_WDG_ONLY) || defined (SWDG_INT_CNR_WDG_EXT_TLF_WINDOW_WDG)
; ..\mcal_src\Spi_Irq.c	   690    /* Call QSPI3 Rx Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   691    SafeWdgQspi_IsrTxRx();
; ..\mcal_src\Spi_Irq.c	   692  
; ..\mcal_src\Spi_Irq.c	   693    #endif
; ..\mcal_src\Spi_Irq.c	   694  }
; ..\mcal_src\Spi_Irq.c	   695  #endif
; ..\mcal_src\Spi_Irq.c	   696  #endif
; ..\mcal_src\Spi_Irq.c	   697  #endif
; ..\mcal_src\Spi_Irq.c	   698  
; ..\mcal_src\Spi_Irq.c	   699  #if ((IFX_SAFETLIB_USED == STD_ON) && (IFX_MCAL_USED == STD_OFF))
; ..\mcal_src\Spi_Irq.c	   700  #ifndef SAFEWDGQSPI_DMAUSED
; ..\mcal_src\Spi_Irq.c	   701  #if ((IRQ_QSPI4_RX_PRIO > 0) || (IRQ_QSPI4_RX_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   702  #if ((IRQ_QSPI4_RX_PRIO > 0) && (IRQ_QSPI4_RX_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   703  IFX_INTERRUPT(QSPI4RX_ISR, 0, IRQ_QSPI4_RX_PRIO)
; ..\mcal_src\Spi_Irq.c	   704  #elif IRQ_QSPI4_RX_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   705  ISR(QSPI4RX_ISR)
; ..\mcal_src\Spi_Irq.c	   706  #endif
; ..\mcal_src\Spi_Irq.c	   707  {
; ..\mcal_src\Spi_Irq.c	   708    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   709  #if (IRQ_QSPI4_RX_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   710    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   711  #endif
; ..\mcal_src\Spi_Irq.c	   712  
; ..\mcal_src\Spi_Irq.c	   713    #if defined (SWDG_EXT_TLF_WDG_ONLY) || defined (SWDG_INT_CNR_WDG_EXT_TLF_WINDOW_WDG)
; ..\mcal_src\Spi_Irq.c	   714    /* Call QSPI4 Rx Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   715    SafeWdgQspi_IsrTxRx();
; ..\mcal_src\Spi_Irq.c	   716  
; ..\mcal_src\Spi_Irq.c	   717    #endif
; ..\mcal_src\Spi_Irq.c	   718  }
; ..\mcal_src\Spi_Irq.c	   719  #endif
; ..\mcal_src\Spi_Irq.c	   720  #endif
; ..\mcal_src\Spi_Irq.c	   721  #endif
; ..\mcal_src\Spi_Irq.c	   722  
; ..\mcal_src\Spi_Irq.c	   723  
; ..\mcal_src\Spi_Irq.c	   724  #if ((IFX_SAFETLIB_USED == STD_ON) && (IFX_MCAL_USED == STD_OFF))
; ..\mcal_src\Spi_Irq.c	   725  #ifndef SAFEWDGQSPI_DMAUSED
; ..\mcal_src\Spi_Irq.c	   726  #if ((IRQ_QSPI5_RX_PRIO > 0) || (IRQ_QSPI5_RX_CAT == IRQ_CAT23))
; ..\mcal_src\Spi_Irq.c	   727  #if ((IRQ_QSPI5_RX_PRIO > 0) && (IRQ_QSPI5_RX_CAT == IRQ_CAT1))
; ..\mcal_src\Spi_Irq.c	   728  IFX_INTERRUPT(QSPI5RX_ISR, 0, IRQ_QSPI5_RX_PRIO)
; ..\mcal_src\Spi_Irq.c	   729  #elif IRQ_QSPI5_RX_CAT == IRQ_CAT23
; ..\mcal_src\Spi_Irq.c	   730  ISR(QSPI5RX_ISR)
; ..\mcal_src\Spi_Irq.c	   731  #endif
; ..\mcal_src\Spi_Irq.c	   732  {
; ..\mcal_src\Spi_Irq.c	   733    /* Enable Global Interrupts */
; ..\mcal_src\Spi_Irq.c	   734  #if (IRQ_QSPI5_RX_CAT == IRQ_CAT1)
; ..\mcal_src\Spi_Irq.c	   735    Mcal_EnableAllInterrupts();
; ..\mcal_src\Spi_Irq.c	   736  #endif
; ..\mcal_src\Spi_Irq.c	   737  
; ..\mcal_src\Spi_Irq.c	   738    #if defined (SWDG_EXT_TLF_WDG_ONLY) || defined (SWDG_INT_CNR_WDG_EXT_TLF_WINDOW_WDG)
; ..\mcal_src\Spi_Irq.c	   739    /* Call QSPI5 Rx Interrupt funtion*/
; ..\mcal_src\Spi_Irq.c	   740    SafeWdgQspi_IsrTxRx();
; ..\mcal_src\Spi_Irq.c	   741    
; ..\mcal_src\Spi_Irq.c	   742    #endif
; ..\mcal_src\Spi_Irq.c	   743  }
; ..\mcal_src\Spi_Irq.c	   744  #endif
; ..\mcal_src\Spi_Irq.c	   745  #endif
; ..\mcal_src\Spi_Irq.c	   746  #endif
; ..\mcal_src\Spi_Irq.c	   747  
; ..\mcal_src\Spi_Irq.c	   748  
; ..\mcal_src\Spi_Irq.c	   749  #if (IFX_SAFETLIB_USED == STD_ON)
; ..\mcal_src\Spi_Irq.c	   750  #define IFX_IRQ_STOP_SEC_CODE_ASIL_B
; ..\mcal_src\Spi_Irq.c	   751  #include "Ifx_MemMap.h"
; ..\mcal_src\Spi_Irq.c	   752  #endif
; ..\mcal_src\Spi_Irq.c	   753  
; ..\mcal_src\Spi_Irq.c	   754  
; ..\mcal_src\Spi_Irq.c	   755  

	; Module end
