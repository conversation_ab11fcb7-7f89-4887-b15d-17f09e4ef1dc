	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc13652a -c99 --dep-file=eeprom\\.MemIf_Cfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\MemIf_Cfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\MemIf_Cfg.src ..\\eeprom\\MemIf_Cfg.c"
	.compiler_name		"ctc"
	.name	"MemIf_Cfg"

	
$TC16X
	
	.sdecl	'.text.MemIf_Cfg.MemIf_Fee_WriteWrapper',code,cluster('MemIf_Fee_WriteWrapper')
	.sect	'.text.MemIf_Cfg.MemIf_Fee_WriteWrapper'
	.align	2
	
	.global	MemIf_Fee_WriteWrapper

; ..\eeprom\MemIf_Cfg.c	     1  /**********************************************************************************************************************
; ..\eeprom\MemIf_Cfg.c	     2   *  COPYRIGHT
; ..\eeprom\MemIf_Cfg.c	     3   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\MemIf_Cfg.c	     4   *  \verbatim
; ..\eeprom\MemIf_Cfg.c	     5   *
; ..\eeprom\MemIf_Cfg.c	     6   *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
; ..\eeprom\MemIf_Cfg.c	     7   *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
; ..\eeprom\MemIf_Cfg.c	     8   *                 All other rights remain with Vector Informatik GmbH.
; ..\eeprom\MemIf_Cfg.c	     9   *  \endverbatim
; ..\eeprom\MemIf_Cfg.c	    10   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\MemIf_Cfg.c	    11   *  LICENSE
; ..\eeprom\MemIf_Cfg.c	    12   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\MemIf_Cfg.c	    13   *            Module: MemIf
; ..\eeprom\MemIf_Cfg.c	    14   *           Program: MSR_Vector_SLP4
; ..\eeprom\MemIf_Cfg.c	    15   *          Customer: DIAS Automotive Electronic Systems Co. Ltd.
; ..\eeprom\MemIf_Cfg.c	    16   *       Expiry Date: Not restricted
; ..\eeprom\MemIf_Cfg.c	    17   *  Ordered Derivat.: Aurix TC234L
; ..\eeprom\MemIf_Cfg.c	    18   *    License Scope : The usage is restricted to CBD1900770_D01
; ..\eeprom\MemIf_Cfg.c	    19   *
; ..\eeprom\MemIf_Cfg.c	    20   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\MemIf_Cfg.c	    21   *  FILE DESCRIPTION
; ..\eeprom\MemIf_Cfg.c	    22   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\MemIf_Cfg.c	    23   *              File: MemIf_Cfg.c
; ..\eeprom\MemIf_Cfg.c	    24   *   Generation Time: 2021-02-02 18:07:15
; ..\eeprom\MemIf_Cfg.c	    25   *           Project: PGM_BswCfg - Version 0.0.0
; ..\eeprom\MemIf_Cfg.c	    26   *          Delivery: CBD1900770_D01
; ..\eeprom\MemIf_Cfg.c	    27   *      Tool Version: DaVinci Configurator  5.20.35
; ..\eeprom\MemIf_Cfg.c	    28   *
; ..\eeprom\MemIf_Cfg.c	    29   *
; ..\eeprom\MemIf_Cfg.c	    30   *********************************************************************************************************************/
; ..\eeprom\MemIf_Cfg.c	    31  
; ..\eeprom\MemIf_Cfg.c	    32      
; ..\eeprom\MemIf_Cfg.c	    33  /**********************************************************************************************************************
; ..\eeprom\MemIf_Cfg.c	    34   *  INCLUDES
; ..\eeprom\MemIf_Cfg.c	    35   *********************************************************************************************************************/
; ..\eeprom\MemIf_Cfg.c	    36  #include "Std_Types.h"
; ..\eeprom\MemIf_Cfg.c	    37  #include "MemIf.h"
; ..\eeprom\MemIf_Cfg.c	    38  
; ..\eeprom\MemIf_Cfg.c	    39  /****  Include of MemHwA Modules  ************************************************************************************/
; ..\eeprom\MemIf_Cfg.c	    40  #include "Fee.h" 
; ..\eeprom\MemIf_Cfg.c	    41  
; ..\eeprom\MemIf_Cfg.c	    42  
; ..\eeprom\MemIf_Cfg.c	    43  
; ..\eeprom\MemIf_Cfg.c	    44  /**********************************************************************************************************************
; ..\eeprom\MemIf_Cfg.c	    45   *  VERSION CHECK
; ..\eeprom\MemIf_Cfg.c	    46   *********************************************************************************************************************/
; ..\eeprom\MemIf_Cfg.c	    47   
; ..\eeprom\MemIf_Cfg.c	    48  #if (   (MEMIF_CFG_MAJOR_VERSION != (5u)) \ 
; ..\eeprom\MemIf_Cfg.c	    49       || (MEMIF_CFG_MINOR_VERSION != (2u)))
; ..\eeprom\MemIf_Cfg.c	    50  # error "Version numbers of MemIf_Cfg.c and MemIf_Cfg.h are inconsistent!"
; ..\eeprom\MemIf_Cfg.c	    51  #endif
; ..\eeprom\MemIf_Cfg.c	    52  
; ..\eeprom\MemIf_Cfg.c	    53  #if (   (MEMIF_SW_MAJOR_VERSION != (3u)) \ 
; ..\eeprom\MemIf_Cfg.c	    54       || (MEMIF_SW_MINOR_VERSION != (4u)))
; ..\eeprom\MemIf_Cfg.c	    55  # error "Version numbers of MemIf_Cfg.c and MemIf.h are inconsistent!"
; ..\eeprom\MemIf_Cfg.c	    56  #endif
; ..\eeprom\MemIf_Cfg.c	    57  
; ..\eeprom\MemIf_Cfg.c	    58  
; ..\eeprom\MemIf_Cfg.c	    59  /**********************************************************************************************************************
; ..\eeprom\MemIf_Cfg.c	    60   *  GLOBAL DATA
; ..\eeprom\MemIf_Cfg.c	    61   *********************************************************************************************************************/
; ..\eeprom\MemIf_Cfg.c	    62   
; ..\eeprom\MemIf_Cfg.c	    63  #ifndef MEMIF_LOCAL /* COV_MEMIF_COMPATIBILITY */
; ..\eeprom\MemIf_Cfg.c	    64  # define MEMIF_LOCAL static
; ..\eeprom\MemIf_Cfg.c	    65  #endif
; ..\eeprom\MemIf_Cfg.c	    66  
; ..\eeprom\MemIf_Cfg.c	    67  #if !defined (MEMIF_LOCAL_INLINE) /* COV_MEMIF_COMPATIBILITY */
; ..\eeprom\MemIf_Cfg.c	    68  # define MEMIF_LOCAL_INLINE LOCAL_INLINE
; ..\eeprom\MemIf_Cfg.c	    69  #endif
; ..\eeprom\MemIf_Cfg.c	    70   
; ..\eeprom\MemIf_Cfg.c	    71  #define MEMIF_START_SEC_CONST_8BIT
; ..\eeprom\MemIf_Cfg.c	    72  #include "MemMap.h"	/* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\MemIf_Cfg.c	    73  
; ..\eeprom\MemIf_Cfg.c	    74  CONST(uint8, MEMIF_CONST) MemIf_NumberOfDevices = MEMIF_NUMBER_OF_DEVICES;
; ..\eeprom\MemIf_Cfg.c	    75  
; ..\eeprom\MemIf_Cfg.c	    76  #define MEMIF_STOP_SEC_CONST_8BIT
; ..\eeprom\MemIf_Cfg.c	    77  #include "MemMap.h"	/* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\MemIf_Cfg.c	    78  
; ..\eeprom\MemIf_Cfg.c	    79  /**********************************************************************************************************************
; ..\eeprom\MemIf_Cfg.c	    80   *  MemHwA Function Pointer Tables
; ..\eeprom\MemIf_Cfg.c	    81   *********************************************************************************************************************/
; ..\eeprom\MemIf_Cfg.c	    82  #define MEMIF_START_SEC_CODE
; ..\eeprom\MemIf_Cfg.c	    83  #include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\MemIf_Cfg.c	    84  
; ..\eeprom\MemIf_Cfg.c	    85  /**-- MemHwA Write Wrapper Functions --**/
; ..\eeprom\MemIf_Cfg.c	    86  MEMIF_LOCAL_INLINE FUNC (Std_ReturnType, MEMIF_PRIVATE_CODE) MemIf_Fee_WriteWrapper(uint16 BlockNumber, MemIf_DataPtr_pu8 DataBufferPtr); 
; ..\eeprom\MemIf_Cfg.c	    87  
; ..\eeprom\MemIf_Cfg.c	    88   
; ..\eeprom\MemIf_Cfg.c	    89  MEMIF_LOCAL_INLINE FUNC (Std_ReturnType, MEMIF_PRIVATE_CODE) MemIf_Fee_WriteWrapper(uint16 BlockNumber, MemIf_DataPtr_pu8 DataBufferPtr) /* PRQA S 3673 */ /* MD_MEMIF_16.7 */ 
; Function MemIf_Fee_WriteWrapper
.L4:
MemIf_Fee_WriteWrapper:	.type	func

; ..\eeprom\MemIf_Cfg.c	    90  { 
; ..\eeprom\MemIf_Cfg.c	    91      return Fee_Write(BlockNumber, DataBufferPtr); /* SBSW_MEMIF_04 */ 
	j	Fee_Write
.L19:
	
__MemIf_Fee_WriteWrapper_function_end:
	.size	MemIf_Fee_WriteWrapper,__MemIf_Fee_WriteWrapper_function_end-MemIf_Fee_WriteWrapper
.L13:
	; End of function
	
	.sdecl	'.rodata.MemIf_Cfg.MemIf_NumberOfDevices',data,rom,cluster('MemIf_NumberOfDevices')
	.sect	'.rodata.MemIf_Cfg.MemIf_NumberOfDevices'
	.global	MemIf_NumberOfDevices
MemIf_NumberOfDevices:	.type	object
	.size	MemIf_NumberOfDevices,1
	.byte	1
	.sdecl	'.rodata.CPU0.Private.DEFAULT_CONST_FAR_UNSPECIFIED',data,rom,cluster('MemIf_MemHwaApis')
	.sect	'.rodata.CPU0.Private.DEFAULT_CONST_FAR_UNSPECIFIED'
	.global	MemIf_MemHwaApis
	.align	4
MemIf_MemHwaApis:	.type	object
	.size	MemIf_MemHwaApis,32
	.word	Fee_Read,MemIf_Fee_WriteWrapper,Fee_EraseImmediateBlock,Fee_InvalidateBlock,Fee_Cancel,Fee_GetStatus,Fee_GetJobResult,Fee_SetMode
	.calls	'__INDIRECT__','Fee_SetMode'
	.calls	'__INDIRECT__','Fee_Read'
	.calls	'__INDIRECT__','Fee_Cancel'
	.calls	'__INDIRECT__','Fee_GetStatus'
	.calls	'__INDIRECT__','Fee_GetJobResult'
	.calls	'__INDIRECT__','Fee_InvalidateBlock'
	.calls	'__INDIRECT__','Fee_EraseImmediateBlock'
	.calls	'__INDIRECT__','MemIf_Fee_WriteWrapper'
	.calls	'MemIf_Fee_WriteWrapper','Fee_Write'
	.extern	Fee_SetMode
	.extern	Fee_Read
	.extern	Fee_Write
	.extern	Fee_Cancel
	.extern	Fee_GetStatus
	.extern	Fee_GetJobResult
	.extern	Fee_InvalidateBlock
	.extern	Fee_EraseImmediateBlock
	.extern	__INDIRECT__
	.calls	'MemIf_Fee_WriteWrapper','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L6:
	.word	5863
	.half	3
	.word	.L7
	.byte	4
.L5:
	.byte	1
	.byte	'..\\eeprom\\MemIf_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L8
	.byte	2
	.byte	'Fee_SetMode',0,1,239,5,13,1,1,1,1,3,2,88,9,1,4
	.byte	'MEMIF_MODE_SLOW',0,0,4
	.byte	'MEMIF_MODE_FAST',0,1,0,5
	.byte	'Mode',0,1,239,5,41
	.word	197
	.byte	0
.L18:
	.byte	6
	.byte	'unsigned char',0,1,8,7
	.byte	'Fee_Read',0,1,138,6,23
	.word	254
	.byte	1,1,1,1
.L20:
	.byte	6
	.byte	'unsigned short int',0,2,7,5
	.byte	'BlockNumber',0,1,138,6,39
	.word	293
	.byte	5
	.byte	'BlockOffset',0,1,139,6,39
	.word	293
	.byte	8
	.word	254
	.byte	5
	.byte	'DataBufferPtr',0,1,140,6,39
	.word	357
	.byte	5
	.byte	'Length',0,1,141,6,39
	.word	293
	.byte	0,7
	.byte	'Fee_Write',0,1,165,6,23
	.word	254
	.byte	1,1,1,1,5
	.byte	'BlockNumber',0,1,165,6,40
	.word	293
	.byte	5
	.byte	'DataBufferPtr',0,1,165,6,60
	.word	357
	.byte	0,9
	.byte	'Fee_Cancel',0,1,189,6,13,1,1,1,1,3,2,59,9,1,4
	.byte	'MEMIF_UNINIT',0,0,4
	.byte	'MEMIF_IDLE',0,1,4
	.byte	'MEMIF_BUSY',0,2,4
	.byte	'MEMIF_BUSY_INTERNAL',0,3,0,10
	.byte	'Fee_GetStatus',0,1,209,6,25
	.word	490
	.byte	1,1,1,1,3,2,72,9,1,4
	.byte	'MEMIF_JOB_OK',0,0,4
	.byte	'MEMIF_JOB_FAILED',0,1,4
	.byte	'MEMIF_JOB_PENDING',0,2,4
	.byte	'MEMIF_JOB_CANCELED',0,3,4
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,4
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,10
	.byte	'Fee_GetJobResult',0,1,231,6,28
	.word	586
	.byte	1,1,1,1,7
	.byte	'Fee_InvalidateBlock',0,1,253,6,23
	.word	254
	.byte	1,1,1,1,5
	.byte	'BlockNumber',0,1,253,6,50
	.word	293
	.byte	0,7
	.byte	'Fee_EraseImmediateBlock',0,1,149,7,23
	.word	254
	.byte	1,1,1,1,5
	.byte	'BlockNumber',0,1,149,7,55
	.word	293
	.byte	0,8
	.word	254
.L22:
	.byte	11
	.byte	'MemIf_DataPtr_pu8',0,2,96,50
	.word	357
	.byte	12
	.byte	'__INDIRECT__',0,3,1,1,1,1,1,13
	.byte	'void',0,8
	.word	911
	.byte	11
	.byte	'__prof_adm',0,3,1,1
	.word	917
	.byte	14,1,8
	.word	941
	.byte	11
	.byte	'__codeptr',0,3,1,1
	.word	943
	.byte	11
	.byte	'uint8',0,4,90,29
	.word	254
	.byte	11
	.byte	'uint16',0,4,92,29
	.word	293
	.byte	6
	.byte	'unsigned long int',0,4,7,11
	.byte	'uint32',0,4,94,29
	.word	995
	.byte	11
	.byte	'boolean',0,4,105,29
	.word	254
	.byte	11
	.byte	'Std_ReturnType',0,5,113,15
	.word	254
	.byte	11
	.byte	'MemIf_StatusType',0,2,65,3
	.word	490
	.byte	11
	.byte	'MemIf_JobResultType',0,2,80,3
	.word	586
	.byte	11
	.byte	'MemIf_ModeType',0,2,92,3
	.word	197
	.byte	15
	.word	254
	.byte	1,1,16
	.word	293
	.byte	16
	.word	293
	.byte	8
	.word	254
	.byte	16
	.word	1163
	.byte	16
	.word	293
	.byte	0,8
	.word	1146
	.byte	11
	.byte	'MemIf_ApiReadType',0,6,120,9
	.word	1179
	.byte	15
	.word	254
	.byte	1,1,16
	.word	293
	.byte	16
	.word	1163
	.byte	0,8
	.word	1210
	.byte	11
	.byte	'MemIf_ApiWriteType',0,6,121,9
	.word	1228
	.byte	15
	.word	254
	.byte	1,1,16
	.word	293
	.byte	0,8
	.word	1260
	.byte	11
	.byte	'MemIf_ApiEraseImmediateBlockType',0,6,122,9
	.word	1273
	.byte	11
	.byte	'MemIf_ApiInvalidateBlockType',0,6,123,9
	.word	1273
	.byte	17,1,1,8
	.word	1356
	.byte	11
	.byte	'MemIf_ApiCancelType',0,6,124,9
	.word	1359
	.byte	18
	.word	490
	.byte	1,1,8
	.word	1392
	.byte	11
	.byte	'MemIf_ApiGetStatusType',0,6,125,9
	.word	1399
	.byte	18
	.word	586
	.byte	1,1,8
	.word	1435
	.byte	11
	.byte	'MemIf_ApiGetJobResultType',0,6,126,9
	.word	1442
	.byte	19,1,1,16
	.word	197
	.byte	0,8
	.word	1481
	.byte	11
	.byte	'MemIf_ApiSetModeType',0,6,127,9
	.word	1490
	.byte	20,6,130,1,9,32,21
	.byte	'Read',0,4
	.word	1184
	.byte	2,35,0,21
	.byte	'Write',0,4
	.word	1233
	.byte	2,35,4,21
	.byte	'EraseImmediateBlock',0,4
	.word	1278
	.byte	2,35,8,21
	.byte	'InvalidateBlock',0,4
	.word	1319
	.byte	2,35,12,21
	.byte	'Cancel',0,4
	.word	1364
	.byte	2,35,16,21
	.byte	'GetStatus',0,4
	.word	1404
	.byte	2,35,20,21
	.byte	'GetJobResult',0,4
	.word	1447
	.byte	2,35,24,21
	.byte	'SetMode',0,4
	.word	1495
	.byte	2,35,28,0,11
	.byte	'MemIf_MemHwAApi_Type',0,6,140,1,3
	.word	1524
	.byte	8
	.word	1146
	.byte	8
	.word	1210
	.byte	8
	.word	1260
	.byte	8
	.word	1260
	.byte	8
	.word	1356
	.byte	8
	.word	1392
	.byte	8
	.word	1435
	.byte	8
	.word	1481
.L24:
	.byte	22
	.word	254
	.byte	23,32
	.word	1524
	.byte	24,0,0
.L25:
	.byte	22
	.word	1763
	.byte	6
	.byte	'unsigned int',0,4,7,11
	.byte	'unsigned_int',0,7,121,22
	.word	1777
	.byte	11
	.byte	'Fls_LengthType',0,8,177,3,16
	.word	995
	.byte	25
	.byte	'Fls_JobStartType',0,8,179,3,16,1,26
	.byte	'Reserved1',0,1
	.word	254
	.byte	1,7,2,35,0,26
	.byte	'Write',0,1
	.word	254
	.byte	1,6,2,35,0,26
	.byte	'Erase',0,1
	.word	254
	.byte	1,5,2,35,0,26
	.byte	'Read',0,1
	.word	254
	.byte	1,4,2,35,0,26
	.byte	'Compare',0,1
	.word	254
	.byte	1,3,2,35,0,26
	.byte	'Reserved2',0,1
	.word	254
	.byte	3,0,2,35,0,0,11
	.byte	'Fls_JobStartType',0,8,187,3,3
	.word	1838
	.byte	11
	.byte	'Fls_17_Pmu_Job_Type',0,8,191,3,15
	.word	254
	.byte	25
	.byte	'Fls_17_Pmu_StateType',0,8,202,3,16,36,21
	.byte	'FlsReadAddress',0,4
	.word	995
	.byte	2,35,0,21
	.byte	'FlsWriteAddress',0,4
	.word	995
	.byte	2,35,4,21
	.byte	'FlsReadLength',0,4
	.word	995
	.byte	2,35,8,21
	.byte	'FlsWriteLength',0,4
	.word	995
	.byte	2,35,12,21
	.byte	'FlsReadBufferPtr',0,4
	.word	357
	.byte	2,35,16,22
	.word	254
	.byte	8
	.word	2177
	.byte	21
	.byte	'FlsWriteBufferPtr',0,4
	.word	2182
	.byte	2,35,20,21
	.byte	'FlsJobResult',0,1
	.word	586
	.byte	2,35,24,21
	.byte	'FlsMode',0,1
	.word	197
	.byte	2,35,25,21
	.byte	'NotifCaller',0,1
	.word	254
	.byte	2,35,26,21
	.byte	'JobStarted',0,1
	.word	1838
	.byte	2,35,27,23,2
	.word	254
	.byte	24,1,0,21
	.byte	'FlsJobType',0,2
	.word	2294
	.byte	2,35,28,21
	.byte	'FlsPver',0,1
	.word	254
	.byte	2,35,30,21
	.byte	'FlsOper',0,1
	.word	254
	.byte	2,35,31,21
	.byte	'FlsTimeoutErr',0,1
	.word	254
	.byte	2,35,32,0,11
	.byte	'Fls_17_Pmu_StateType',0,8,134,4,3
	.word	2028
	.byte	11
	.byte	'Fls_NotifFunctionPtrType',0,8,141,4,16
	.word	1359
	.byte	19,1,1,16
	.word	995
	.byte	16
	.word	995
	.byte	22
	.word	995
	.byte	8
	.word	2458
	.byte	16
	.word	2463
	.byte	16
	.word	254
	.byte	0,8
	.word	2445
	.byte	11
	.byte	'Fls_WriteCmdPtrType',0,8,143,4,16
	.word	2479
	.byte	19,1,1,16
	.word	995
	.byte	0,8
	.word	2513
	.byte	11
	.byte	'Fls_EraseCmdPtrType',0,8,148,4,16
	.word	2522
	.byte	11
	.byte	'Fee_PageType',0,1,136,1,17
	.word	293
	.byte	11
	.byte	'Fee_NotifFunctionPtrType',0,1,146,1,16
	.word	1359
	.byte	25
	.byte	'Fee_Block',0,1,148,1,16,8,26
	.byte	'CycleCountLimit',0,4
	.word	1777
	.byte	24,8,2,35,2,26
	.byte	'FeeImmediateData',0,1
	.word	254
	.byte	8,0,2,35,3,26
	.byte	'BlockNumber',0,2
	.word	293
	.byte	16,0,2,35,4,26
	.byte	'Size',0,2
	.word	293
	.byte	16,0,2,35,6,0,11
	.byte	'Fee_BlockType',0,1,162,1,3
	.word	2612
	.byte	25
	.byte	'Fee_CacheStatus',0,1,170,1,16,2,26
	.byte	'Valid',0,1
	.word	254
	.byte	1,7,2,35,0,26
	.byte	'Consistent',0,1
	.word	254
	.byte	1,6,2,35,0,26
	.byte	'Copied',0,1
	.word	254
	.byte	1,5,2,35,0,26
	.byte	'PrevCopyValid',0,1
	.word	254
	.byte	1,4,2,35,0,26
	.byte	'PrevCopyConsistent',0,1
	.word	254
	.byte	1,3,2,35,0,26
	.byte	'PrevCopyCopied',0,1
	.word	254
	.byte	1,2,2,35,0,26
	.byte	'Reserved',0,2
	.word	293
	.byte	10,0,2,35,0,0,11
	.byte	'Fee_CacheStatusType',0,1,187,1,3
	.word	2746
	.byte	25
	.byte	'Fee_Cache',0,1,189,1,16,8,21
	.byte	'Address',0,4
	.word	995
	.byte	2,35,0,21
	.byte	'BlockNumber',0,2
	.word	293
	.byte	2,35,4,21
	.byte	'Status',0,2
	.word	2746
	.byte	2,35,6,0,11
	.byte	'Fee_CacheType',0,1,204,1,3
	.word	2956
	.byte	25
	.byte	'FeePendReqInfo_Buf',0,1,210,1,16,12,21
	.byte	'DataBufferPtr',0,4
	.word	357
	.byte	2,35,0,21
	.byte	'BlockNumber',0,2
	.word	293
	.byte	2,35,4,21
	.byte	'BlockOffset',0,2
	.word	293
	.byte	2,35,6,21
	.byte	'Length',0,2
	.word	293
	.byte	2,35,8,0,11
	.byte	'Fee_PendReqBufType',0,1,219,1,3
	.word	3050
	.byte	25
	.byte	'FeeStatusFlags_t',0,1,226,1,17,1,26
	.byte	'FeeBlkModified',0,1
	.word	254
	.byte	1,7,2,35,0,26
	.byte	'FeeStartInitGC',0,1
	.word	254
	.byte	1,6,2,35,0,26
	.byte	'FeeCurrSector',0,1
	.word	254
	.byte	1,5,2,35,0,26
	.byte	'FeeInitAPICalled',0,1
	.word	254
	.byte	1,4,2,35,0,26
	.byte	'FeeBlkInvalidStatus',0,1
	.word	254
	.byte	1,3,2,35,0,26
	.byte	'FeeWriteInvldAPICalled',0,1
	.word	254
	.byte	1,2,2,35,0,26
	.byte	'unused',0,1
	.word	254
	.byte	2,0,2,35,0,0,11
	.byte	'Fee_StatusFlagsType',0,1,254,1,3
	.word	3185
	.byte	25
	.byte	'Fee_SectorStatus_t',0,1,133,2,17,1,26
	.byte	'Dirty',0,1
	.word	254
	.byte	1,7,2,35,0,26
	.byte	'Used',0,1
	.word	254
	.byte	1,6,2,35,0,26
	.byte	'unused',0,1
	.word	254
	.byte	6,0,2,35,0,0,11
	.byte	'Fee_SectorStatusType',0,1,142,2,3
	.word	3426
	.byte	25
	.byte	'Fee_SectorInfo_t',0,1,147,2,16,32,21
	.byte	'StateCount',0,4
	.word	995
	.byte	2,35,0,21
	.byte	'UnerasableWLAddr',0,4
	.word	995
	.byte	2,35,4,23,8
	.word	995
	.byte	24,1,0,21
	.byte	'NonZeroWLAddr',0,8
	.word	3602
	.byte	2,35,8,21
	.byte	'NonZeroWLCount',0,4
	.word	995
	.byte	2,35,16,21
	.byte	'StatePageAddr',0,4
	.word	995
	.byte	2,35,20,21
	.byte	'NextFreeWLAddr',0,4
	.word	995
	.byte	2,35,24,21
	.byte	'UnerasableWLCount',0,1
	.word	254
	.byte	2,35,28,21
	.byte	'State',0,1
	.word	254
	.byte	2,35,29,21
	.byte	'Status',0,1
	.word	3426
	.byte	2,35,30,0,11
	.byte	'Fee_SectorInfoType',0,1,177,2,3
	.word	3533
	.byte	25
	.byte	'Fee_LastWrittenBlkInfo_t',0,1,180,2,16,12,21
	.byte	'Addr',0,4
	.word	995
	.byte	2,35,0,21
	.byte	'PageCount',0,2
	.word	293
	.byte	2,35,4,21
	.byte	'BlockNumber',0,2
	.word	293
	.byte	2,35,6,21
	.byte	'Status',0,2
	.word	2746
	.byte	2,35,8,0,11
	.byte	'Fee_LastWrittenBlkInfoType',0,1,190,2,3
	.word	3792
	.byte	25
	.byte	'Fee_GcBlkInfo_t',0,1,192,2,16,12,21
	.byte	'Addr',0,4
	.word	995
	.byte	2,35,0,21
	.byte	'PageCount',0,2
	.word	293
	.byte	2,35,4,21
	.byte	'BlockNumber',0,2
	.word	293
	.byte	2,35,6,21
	.byte	'Consistent',0,1
	.word	254
	.byte	2,35,8,0,11
	.byte	'Fee_GcBlkInfoType',0,1,202,2,3
	.word	3930
	.byte	25
	.byte	'Fee_State_Data_t',0,1,206,2,16,192,21,23,64
	.word	3533
	.byte	24,1,0,21
	.byte	'FeeSectorInfo',0,64
	.word	4078
	.byte	2,35,0,23,128,7
	.word	2956
	.byte	24,111,0,21
	.byte	'FeeBlockInfo',0,128,7
	.word	4110
	.byte	2,35,64,21
	.byte	'FeeLastWrittenBlkInfo',0,12
	.word	3792
	.byte	3,35,192,7,21
	.byte	'FeeGcCurrBlkInfo',0,12
	.word	3930
	.byte	3,35,204,7,21
	.byte	'FeePendReqInfo',0,12
	.word	3050
	.byte	3,35,216,7,23,128,1
	.word	995
	.byte	24,31,0,21
	.byte	'FeeGcLWBGcSrcAddr',0,128,1
	.word	4227
	.byte	3,35,228,7,21
	.byte	'FeeTempArray',0,8
	.word	3602
	.byte	3,35,228,8,21
	.byte	'FeeStateCount',0,4
	.word	995
	.byte	3,35,236,8,23,128,4
	.word	254
	.byte	24,255,3,0,21
	.byte	'FeeReadWriteBuffer',0,128,4
	.word	4313
	.byte	3,35,240,8,21
	.byte	'FeeGcReadWriteBuffer',0,128,4
	.word	4313
	.byte	3,35,240,12,23,248,3
	.word	254
	.byte	24,247,3,0,21
	.byte	'FeeLastWrittenBlkBuffer',0,248,3
	.word	4386
	.byte	3,35,240,16,21
	.byte	'FeeGcDestAddr',0,4
	.word	995
	.byte	3,35,232,20,21
	.byte	'FeeGcSrcAddr',0,4
	.word	995
	.byte	3,35,236,20,21
	.byte	'FeeNextFreePageAddr',0,4
	.word	995
	.byte	3,35,240,20,21
	.byte	'FeeWriteAffectedAddr',0,4
	.word	995
	.byte	3,35,244,20,21
	.byte	'FeeBlockStartAddr',0,4
	.word	995
	.byte	3,35,248,20,21
	.byte	'FeeCurrSectSrcAddr',0,4
	.word	995
	.byte	3,35,252,20,21
	.byte	'FeeUnErasableWLAddrTemp',0,4
	.word	995
	.byte	3,35,128,21,21
	.byte	'FeeUserReadDestPtr',0,4
	.word	357
	.byte	3,35,132,21,21
	.byte	'FeeJobResult',0,1
	.word	586
	.byte	3,35,136,21,21
	.byte	'FeeLastWriteSize',0,4
	.word	995
	.byte	3,35,138,21,21
	.byte	'FeeLastReadSize',0,4
	.word	995
	.byte	3,35,142,21,21
	.byte	'FeeComparedLen',0,2
	.word	293
	.byte	3,35,146,21,21
	.byte	'FeeReadLen',0,2
	.word	293
	.byte	3,35,148,21,21
	.byte	'FeeBlkPageCount',0,2
	.word	293
	.byte	3,35,150,21,21
	.byte	'FeeUserWriteBytesCount',0,2
	.word	293
	.byte	3,35,152,21,21
	.byte	'FeeCurrReqBlockNum',0,2
	.word	293
	.byte	3,35,154,21,21
	.byte	'FeeIntrCurrReqPageCount',0,2
	.word	293
	.byte	3,35,156,21,21
	.byte	'FeeGCCopyIndex',0,2
	.word	293
	.byte	3,35,158,21,21
	.byte	'FeeGCUnconfigBlkCopyIndex',0,2
	.word	293
	.byte	3,35,160,21,21
	.byte	'FeeUnConfigBlockCount',0,2
	.word	293
	.byte	3,35,162,21,21
	.byte	'FeeGcPrevBlockNumber',0,2
	.word	293
	.byte	3,35,164,21,21
	.byte	'FeeGcFirstBlkNumInWL',0,2
	.word	293
	.byte	3,35,166,21,21
	.byte	'FeeStatusFlags',0,1
	.word	3185
	.byte	3,35,168,21,21
	.byte	'FeeLastWrittenBlockDirty',0,1
	.word	254
	.byte	3,35,169,21,21
	.byte	'FeePendReqStatus',0,1
	.word	254
	.byte	3,35,170,21,21
	.byte	'FeeGcState',0,1
	.word	254
	.byte	3,35,171,21,21
	.byte	'FeeGcResumeState',0,1
	.word	254
	.byte	3,35,172,21,21
	.byte	'FeeGcBlkIndexInWL',0,1
	.word	254
	.byte	3,35,173,21,21
	.byte	'FeeInitGCState',0,1
	.word	254
	.byte	3,35,174,21,21
	.byte	'FeePrepDFLASHState',0,1
	.word	254
	.byte	3,35,175,21,21
	.byte	'FeeCacheState',0,1
	.word	254
	.byte	3,35,176,21,21
	.byte	'FeeRepairStep',0,1
	.word	254
	.byte	3,35,177,21,21
	.byte	'FeeWLAffectedType',0,1
	.word	254
	.byte	3,35,178,21,21
	.byte	'FeeIntrJob',0,1
	.word	254
	.byte	3,35,179,21,21
	.byte	'FeeIntrJobStatus',0,1
	.word	254
	.byte	3,35,180,21,21
	.byte	'FeeUserJobStatus',0,1
	.word	254
	.byte	3,35,181,21,21
	.byte	'FeeIntrJobResult',0,1
	.word	254
	.byte	3,35,182,21,21
	.byte	'FeeUserJobResult',0,1
	.word	254
	.byte	3,35,183,21,21
	.byte	'FeeMainJob',0,1
	.word	254
	.byte	3,35,184,21,21
	.byte	'FeeUserJobFailCount',0,1
	.word	254
	.byte	3,35,185,21,21
	.byte	'FeeIntrJobFailCount',0,1
	.word	254
	.byte	3,35,186,21,21
	.byte	'FeeUncfgBlksExceeded',0,1
	.word	254
	.byte	3,35,187,21,21
	.byte	'FeeUnErasableWLCountTemp',0,1
	.word	254
	.byte	3,35,188,21,21
	.byte	'FeeSectorCount',0,1
	.word	254
	.byte	3,35,189,21,21
	.byte	'FeeDisableGCStart',0,1
	.word	254
	.byte	3,35,190,21,0,11
	.byte	'Fee_StateDataType',0,1,155,4,3
	.word	4054
	.byte	25
	.byte	'Fee_Other_Config_t',0,1,157,4,16,1,26
	.byte	'FeeUnconfigBlock',0,1
	.word	254
	.byte	1,7,2,35,0,26
	.byte	'FeeGcRestartPoint',0,1
	.word	254
	.byte	1,6,2,35,0,26
	.byte	'FeeUseEraseSuspend',0,1
	.word	254
	.byte	1,5,2,35,0,26
	.byte	'unused',0,1
	.word	254
	.byte	5,0,2,35,0,0,11
	.byte	'Fee_GCConfigType',0,1,174,4,3
	.word	5709
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,4,1,58,15,59,15,57,15,11,15,0,0,4,40,0,3,8,28,13,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,36,0,3
	.byte	8,11,15,62,15,0,0,7,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,8,15,0,73,19,0,0,9,46
	.byte	0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,10,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63
	.byte	12,60,12,0,0,11,22,0,3,8,58,15,59,15,57,15,73,19,0,0,12,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0
	.byte	0,13,59,0,3,8,0,0,14,21,0,54,15,0,0,15,21,1,73,19,54,15,39,12,0,0,16,5,0,73,19,0,0,17,21,0,54,15,39,12
	.byte	0,0,18,21,0,73,19,54,15,39,12,0,0,19,21,1,54,15,39,12,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,13,0
	.byte	3,8,11,15,73,19,56,9,0,0,22,38,0,73,19,0,0,23,1,1,11,15,73,19,0,0,24,33,0,47,15,0,0,25,19,1,3,8,58,15
	.byte	59,15,57,15,11,15,0,0,26,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L8:
	.word	.L27-.L26
.L26:
	.half	3
	.word	.L29-.L28
.L28:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls',0
	.byte	0
	.byte	'Fee.h',0,1,0,0
	.byte	'MemIf_Types.h',0,2,0,0
	.byte	'..\\eeprom\\MemIf_Cfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,3,0,0
	.byte	'Std_Types.h',0,3,0,0
	.byte	'_Cfg.h',0,2,0,0
	.byte	'Mcal_TcLib.h',0,3,0,0
	.byte	'Fls_17_Pmu.h',0,4,0,0,0
.L29:
.L27:
	.sdecl	'.debug_info',debug,cluster('MemIf_Fee_WriteWrapper')
	.sect	'.debug_info'
.L9:
	.word	288
	.half	3
	.word	.L10
	.byte	4,1
	.byte	'..\\eeprom\\MemIf_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L12,.L11
	.byte	2
	.word	.L5
	.byte	3
	.byte	'MemIf_Fee_WriteWrapper',0,1,89,62
	.word	.L18
	.byte	1,1,1
	.word	.L4,.L19,.L3
	.byte	4
	.byte	'BlockNumber',0,1,89,92
	.word	.L20,.L21
	.byte	4
	.byte	'DataBufferPtr',0,1,89,123
	.word	.L22,.L23
	.byte	5
	.word	.L4,.L19
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_Fee_WriteWrapper')
	.sect	'.debug_abbrev'
.L10:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('MemIf_Fee_WriteWrapper')
	.sect	'.debug_line'
.L11:
	.word	.L31-.L30
.L30:
	.half	3
	.word	.L33-.L32
.L32:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\MemIf_Cfg.c',0,0,0,0,0
.L33:
	.byte	5,35,7,0,5,2
	.word	.L4
	.byte	3,218,0,1,5,1,7,9
	.half	.L13-.L4
	.byte	3,1,0,1,1
.L31:
	.sdecl	'.debug_ranges',debug,cluster('MemIf_Fee_WriteWrapper')
	.sect	'.debug_ranges'
.L12:
	.word	-1,.L4,0,.L13-.L4,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_NumberOfDevices')
	.sect	'.debug_info'
.L14:
	.word	211
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\eeprom\\MemIf_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L5
	.byte	3
	.byte	'MemIf_NumberOfDevices',0,3,74,27
	.word	.L24
	.byte	1,5,3
	.word	MemIf_NumberOfDevices
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_NumberOfDevices')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('MemIf_MemHwaApis')
	.sect	'.debug_info'
.L16:
	.word	206
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'..\\eeprom\\MemIf_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L5
	.byte	3
	.byte	'MemIf_MemHwaApis',0,3,102,42
	.word	.L25
	.byte	1,5,3
	.word	MemIf_MemHwaApis
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('MemIf_MemHwaApis')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('MemIf_Fee_WriteWrapper')
	.sect	'.debug_loc'
.L21:
	.word	-1,.L4,0,.L19-.L4
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L23:
	.word	-1,.L4,0,.L19-.L4
	.half	1
	.byte	100
	.word	0,0
.L3:
	.word	-1,.L4,0,.L19-.L4
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L34:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('MemIf_Fee_WriteWrapper')
	.sect	'.debug_frame'
	.word	12
	.word	.L34,.L4,.L19-.L4

; ..\eeprom\MemIf_Cfg.c	    92  }
; ..\eeprom\MemIf_Cfg.c	    93   
; ..\eeprom\MemIf_Cfg.c	    94  
; ..\eeprom\MemIf_Cfg.c	    95  #define MEMIF_STOP_SEC_CODE
; ..\eeprom\MemIf_Cfg.c	    96  #include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\MemIf_Cfg.c	    97  
; ..\eeprom\MemIf_Cfg.c	    98  #define MEMIF_START_SEC_CONST_32BIT
; ..\eeprom\MemIf_Cfg.c	    99  #include "MemMap.h"	/* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\MemIf_Cfg.c	   100  
; ..\eeprom\MemIf_Cfg.c	   101  /**-- MemHwA Function Pointers --**/
; ..\eeprom\MemIf_Cfg.c	   102  CONST(MemIf_MemHwAApi_Type, MEMIF_CONST) MemIf_MemHwaApis[MEMIF_NUMBER_OF_DEVICES] =
; ..\eeprom\MemIf_Cfg.c	   103  {
; ..\eeprom\MemIf_Cfg.c	   104     /*  Fee  */ {
; ..\eeprom\MemIf_Cfg.c	   105      Fee_Read, 
; ..\eeprom\MemIf_Cfg.c	   106      MemIf_Fee_WriteWrapper, 
; ..\eeprom\MemIf_Cfg.c	   107      Fee_EraseImmediateBlock, 
; ..\eeprom\MemIf_Cfg.c	   108      Fee_InvalidateBlock, 
; ..\eeprom\MemIf_Cfg.c	   109      Fee_Cancel, 
; ..\eeprom\MemIf_Cfg.c	   110      Fee_GetStatus, 
; ..\eeprom\MemIf_Cfg.c	   111      Fee_GetJobResult, 
; ..\eeprom\MemIf_Cfg.c	   112      Fee_SetMode
; ..\eeprom\MemIf_Cfg.c	   113    }
; ..\eeprom\MemIf_Cfg.c	   114  };
; ..\eeprom\MemIf_Cfg.c	   115      
; ..\eeprom\MemIf_Cfg.c	   116  #define MEMIF_STOP_SEC_CONST_32BIT
; ..\eeprom\MemIf_Cfg.c	   117  #include "MemMap.h"	/* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\MemIf_Cfg.c	   118      
; ..\eeprom\MemIf_Cfg.c	   119  /* Justification for module-specific MISRA deviations:     
; ..\eeprom\MemIf_Cfg.c	   120    MD_MEMIF_16.7: rule 16.7
; ..\eeprom\MemIf_Cfg.c	   121        Reason:     Buffer pointer is not declared const in order to support EA/FEE write interfaces with const and non-const buffers.
; ..\eeprom\MemIf_Cfg.c	   122        Risk:       No risk. By using this wrapper functions without const pointers compiler warnings are solved.
; ..\eeprom\MemIf_Cfg.c	   123        Prevention: Program flow has been verified by component tests and review.
; ..\eeprom\MemIf_Cfg.c	   124  */
; ..\eeprom\MemIf_Cfg.c	   125          
; ..\eeprom\MemIf_Cfg.c	   126  /**********************************************************************************************************************
; ..\eeprom\MemIf_Cfg.c	   127   *  END OF FILE: MemIf_Cfg.c
; ..\eeprom\MemIf_Cfg.c	   128   *********************************************************************************************************************/   
; ..\eeprom\MemIf_Cfg.c	   129  

	; Module end
