	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc29768a -c99 --dep-file=mcal_src\\.Det.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Det.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Det.src ..\\mcal_src\\Det.c"
	.compiler_name		"ctc"
	.name	"Det"

	
$TC16X
	
	.sdecl	'.text.Det.Det_ReportError',code,cluster('Det_ReportError')
	.sect	'.text.Det.Det_ReportError'
	.align	2
	
	.global	Det_ReportError

; ..\mcal_src\Det.c	     1  /******************************************************************************
; ..\mcal_src\Det.c	     2  **                                                                           **
; ..\mcal_src\Det.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_src\Det.c	     4  **                                                                           **
; ..\mcal_src\Det.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Det.c	     6  **                                                                           **
; ..\mcal_src\Det.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Det.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Det.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Det.c	    10  **                                                                           **
; ..\mcal_src\Det.c	    11  *******************************************************************************
; ..\mcal_src\Det.c	    12  **                                                                           **
; ..\mcal_src\Det.c	    13  **  $FILENAME   : Det.c $                                                    **
; ..\mcal_src\Det.c	    14  **                                                                           **
; ..\mcal_src\Det.c	    15  **  $CC VERSION : \main\3 $                                                  **
; ..\mcal_src\Det.c	    16  **                                                                           **
; ..\mcal_src\Det.c	    17  **  $DATE       : 2013-06-20 $                                               **
; ..\mcal_src\Det.c	    18  **                                                                           **
; ..\mcal_src\Det.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Det.c	    20  **                                                                           **
; ..\mcal_src\Det.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Det.c	    22  **                                                                           **
; ..\mcal_src\Det.c	    23  **  DESCRIPTION : This file contains stub for Det_ReportError                **
; ..\mcal_src\Det.c	    24  **                                                                           **
; ..\mcal_src\Det.c	    25  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\Det.c	    26  **                                                                           **
; ..\mcal_src\Det.c	    27  ******************************************************************************/
; ..\mcal_src\Det.c	    28  /*******************************************************************************
; ..\mcal_src\Det.c	    29  **                      Includes                                              **
; ..\mcal_src\Det.c	    30  *******************************************************************************/
; ..\mcal_src\Det.c	    31  
; ..\mcal_src\Det.c	    32  /* Inclusion of Platform_Types.h and Compiler.h */
; ..\mcal_src\Det.c	    33  #include "Std_Types.h"
; ..\mcal_src\Det.c	    34  #include "Mcal.h"
; ..\mcal_src\Det.c	    35  #include "Test_Print.h"
; ..\mcal_src\Det.c	    36  #include "Det.h"
; ..\mcal_src\Det.c	    37  
; ..\mcal_src\Det.c	    38  /*******************************************************************************
; ..\mcal_src\Det.c	    39  **                      Imported Compiler Switch Check                        **
; ..\mcal_src\Det.c	    40  *******************************************************************************/
; ..\mcal_src\Det.c	    41  /*******************************************************************************
; ..\mcal_src\Det.c	    42  **                      Private Macro Definitions                             **
; ..\mcal_src\Det.c	    43  *******************************************************************************/
; ..\mcal_src\Det.c	    44  /*******************************************************************************
; ..\mcal_src\Det.c	    45  **                      Private Type Definitions                              **
; ..\mcal_src\Det.c	    46  *******************************************************************************/
; ..\mcal_src\Det.c	    47  /*******************************************************************************
; ..\mcal_src\Det.c	    48  **                      Private Function Declarations                         **
; ..\mcal_src\Det.c	    49  *******************************************************************************/
; ..\mcal_src\Det.c	    50  /*******************************************************************************
; ..\mcal_src\Det.c	    51  **                      Global Constant Definitions                           **
; ..\mcal_src\Det.c	    52  *******************************************************************************/
; ..\mcal_src\Det.c	    53  /*******************************************************************************
; ..\mcal_src\Det.c	    54  **                      Global Variable Definitions                           **
; ..\mcal_src\Det.c	    55  *******************************************************************************/
; ..\mcal_src\Det.c	    56  /*******************************************************************************
; ..\mcal_src\Det.c	    57  **                      Private Constant Definitions                          **
; ..\mcal_src\Det.c	    58  *******************************************************************************/
; ..\mcal_src\Det.c	    59  /*******************************************************************************
; ..\mcal_src\Det.c	    60  **                      Private Variable Definitions                          **
; ..\mcal_src\Det.c	    61  *******************************************************************************/
; ..\mcal_src\Det.c	    62  /*******************************************************************************
; ..\mcal_src\Det.c	    63  **                      Global Function Definitions                           **
; ..\mcal_src\Det.c	    64  *******************************************************************************/
; ..\mcal_src\Det.c	    65  /* Test Stub for Det_ReportError */ 
; ..\mcal_src\Det.c	    66  void Det_ReportError(uint16 ModuleId,uint8 InstanceId,uint8 ApiId,uint8 ErrorId) 
; Function Det_ReportError
.L3:
Det_ReportError:	.type	func

; ..\mcal_src\Det.c	    67  {
; ..\mcal_src\Det.c	    68    print_f("\n DET OCCURED \n ");
	mov	d8,d4
	movh.a	a4,#@his(.1.str)
.L22:
	mov	d15,d5
	lea	a4,[a4]@los(.1.str)
.L23:
	mov	d9,d6
	sub.a	a10,#16
.L20:
	mov	d10,d7
	call	print_f
.L21:

; ..\mcal_src\Det.c	    69    print_f("ModuleId:%d, InstanceId: %d, ApiId:%d, ErrorId:%d"
; ..\mcal_src\Det.c	    70             ,ModuleId, InstanceId, ApiId, ErrorId);
	st.w	[a10],d8
.L32:
	st.w	[a10]4,d15
.L33:
	st.w	[a10]8,d9
.L34:
	st.w	[a10]12,d10
.L35:
	movh.a	a4,#@his(.2.str)
	lea	a4,[a4]@los(.2.str)
	j	print_f
.L13:
	
__Det_ReportError_function_end:
	.size	Det_ReportError,__Det_ReportError_function_end-Det_ReportError
.L12:
	; End of function
	
	.sdecl	'.rodata.Det..1.str',data,rom
	.sect	'.rodata.Det..1.str'
	.align	4
.1.str:	.type	object
	.size	.1.str,17
	.byte	10,32,68,69,84,32,79,67
	.byte	67,85,82,69,68,32,10,32
	.space	1
	.sdecl	'.rodata.Det..2.str',data,rom
	.sect	'.rodata.Det..2.str'
	.align	4
.2.str:	.type	object
	.size	.2.str,50
	.byte	77,111,100,117,108,101,73,100
	.byte	58,37,100,44,32,73,110,115
	.byte	116,97,110,99,101,73,100,58
	.byte	32,37,100,44,32,65,112,105
	.byte	73,100,58,37,100,44,32,69
	.byte	114,114,111,114,73,100,58,37
	.byte	100
	.space	1
	.calls	'Det_ReportError','print_f'
	.extern	print_f
	.calls	'Det_ReportError','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L5:
	.word	404
	.half	3
	.word	.L6
	.byte	4
.L4:
	.byte	1
	.byte	'..\\mcal_src\\Det.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L7
	.byte	2
	.byte	'print_f',0,1,166,1,13,1,1,1,1,3
	.byte	'char',0,1,6,4
	.word	189
	.byte	5
	.word	197
	.byte	6
	.byte	'p_frm',0,1,166,1,33
	.word	202
	.byte	7,1,166,1,40,0
.L14:
	.byte	3
	.byte	'unsigned short int',0,2,7
.L16:
	.byte	3
	.byte	'unsigned char',0,1,8,8
	.byte	'void',0,5
	.word	267
	.byte	9
	.byte	'__prof_adm',0,2,1,1
	.word	273
	.byte	10,1,5
	.word	297
	.byte	9
	.byte	'__codeptr',0,2,1,1
	.word	299
	.byte	9
	.byte	'uint8',0,3,90,29
	.word	250
	.byte	9
	.byte	'uint16',0,3,92,29
	.word	228
	.byte	3
	.byte	'unsigned long int',0,4,7,9
	.byte	'uint32',0,3,94,29
	.word	351
	.byte	9
	.byte	'_iob_flag_t',0,4,75,25
	.word	228
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L6:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,36,0,3,8,11,15,62,15,0,0,4,38,0,73,19,0,0,5,15,0,73,19,0,0,6,5,0,3,8,58,15,59,15,57,15,73,19,0,0,7,24
	.byte	0,58,15,59,15,57,15,0,0,8,59,0,3,8,0,0,9,22,0,3,8,58,15,59,15,57,15,73,19,0,0,10,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L7:
	.word	.L25-.L24
.L24:
	.half	3
	.word	.L27-.L26
.L26:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0,0
	.byte	'..\\mcal_src\\Test_Print.h',0,0,0,0
	.byte	'..\\mcal_src\\Det.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'stdio.h',0,1,0,0,0
.L27:
.L25:
	.sdecl	'.debug_info',debug,cluster('Det_ReportError')
	.sect	'.debug_info'
.L8:
	.word	305
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_src\\Det.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L11,.L10
	.byte	2
	.word	.L4
	.byte	3
	.byte	'Det_ReportError',0,1,66,6,1,1,1
	.word	.L3,.L13,.L2
	.byte	4
	.byte	'ModuleId',0,1,66,29
	.word	.L14,.L15
	.byte	4
	.byte	'InstanceId',0,1,66,44
	.word	.L16,.L17
	.byte	4
	.byte	'ApiId',0,1,66,61
	.word	.L16,.L18
	.byte	4
	.byte	'ErrorId',0,1,66,73
	.word	.L16,.L19
	.byte	5
	.word	.L3,.L13
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Det_ReportError')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Det_ReportError')
	.sect	'.debug_line'
.L10:
	.word	.L29-.L28
.L28:
	.half	3
	.word	.L31-.L30
.L30:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Det.c',0,0,0,0,0
.L31:
	.byte	5,6,7,0,5,2
	.word	.L3
	.byte	3,193,0,1,5,11,3,2,1,5,6,9
	.half	.L22-.L3
	.byte	3,126,1,5,11,3,2,1,5,6,9
	.half	.L23-.L22
	.byte	3,126,1,5,11,9
	.half	.L20-.L23
	.byte	3,2,1,5,13,9
	.half	.L21-.L20
	.byte	3,2,1,5,23,9
	.half	.L32-.L21
	.byte	1,5,35,9
	.half	.L33-.L32
	.byte	1,5,42,9
	.half	.L34-.L33
	.byte	1,5,11,9
	.half	.L35-.L34
	.byte	3,127,1,5,1,9
	.half	.L12-.L35
	.byte	3,3,0,1,1
.L29:
	.sdecl	'.debug_ranges',debug,cluster('Det_ReportError')
	.sect	'.debug_ranges'
.L11:
	.word	-1,.L3,0,.L12-.L3,0,0
	.sdecl	'.debug_loc',debug,cluster('Det_ReportError')
	.sect	'.debug_loc'
.L18:
	.word	-1,.L3,0,.L21-.L3
	.half	5
	.byte	144,35,157,32,0
	.word	.L20-.L3,.L13-.L3
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L2:
	.word	-1,.L3,0,.L20-.L3
	.half	2
	.byte	138,0
	.word	.L20-.L3,.L13-.L3
	.half	2
	.byte	138,16
	.word	.L13-.L3,.L13-.L3
	.half	2
	.byte	138,0
	.word	0,0
.L19:
	.word	-1,.L3,0,.L21-.L3
	.half	5
	.byte	144,35,157,32,32
	.word	.L21-.L3,.L13-.L3
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L17:
	.word	-1,.L3,0,.L21-.L3
	.half	5
	.byte	144,34,157,32,32
	.word	.L23-.L3,.L13-.L3
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L15:
	.word	-1,.L3,0,.L21-.L3
	.half	5
	.byte	144,34,157,32,0
	.word	.L22-.L3,.L13-.L3
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L36:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Det_ReportError')
	.sect	'.debug_frame'
	.word	36
	.word	.L36,.L3,.L13-.L3
	.byte	4
	.word	(.L20-.L3)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L13-.L20)/2
	.byte	19,0,8,26,0,0

; ..\mcal_src\Det.c	    71  
; ..\mcal_src\Det.c	    72  }
; ..\mcal_src\Det.c	    73  /*******************************************************************************
; ..\mcal_src\Det.c	    74  **                      Private Function Definitions                          **
; ..\mcal_src\Det.c	    75  *******************************************************************************/
; ..\mcal_src\Det.c	    76  
; ..\mcal_src\Det.c	    77  

	; Module end
