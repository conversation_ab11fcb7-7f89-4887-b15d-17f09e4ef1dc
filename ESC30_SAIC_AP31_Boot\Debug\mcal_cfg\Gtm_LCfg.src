	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc27644a -c99 --dep-file=mcal_cfg\\.Gtm_LCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\Gtm_LCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\Gtm_LCfg.src ..\\mcal_cfg\\Gtm_LCfg.c"
	.compiler_name		"ctc"
	.name	"Gtm_LCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.DEFAULT_CONST_FAR_UNSPECIFIED',data,rom,cluster('Gtm_kNotifConfig0')
	.sect	'.rodata.CPU0.Private.DEFAULT_CONST_FAR_UNSPECIFIED'
	.global	Gtm_kNotifConfig0
	.align	4
Gtm_kNotifConfig0:	.type	object
	.size	Gtm_kNotifConfig0,40
	.word	EyeQ_ErrInr,CAN1_ErrInr,LP875701_ErrInr,LP87563_ErrInr,ecal_period_1ms,ecal_period_2ms,ecal_period_5ms,ecal_period_10ms
	.word	ecal_period_20ms,ecal_period_50ms
	.calls	'__INDIRECT__','ecal_period_1ms'
	.calls	'__INDIRECT__','ecal_period_2ms'
	.calls	'__INDIRECT__','ecal_period_5ms'
	.calls	'__INDIRECT__','ecal_period_10ms'
	.calls	'__INDIRECT__','ecal_period_20ms'
	.calls	'__INDIRECT__','ecal_period_50ms'
	.calls	'__INDIRECT__','EyeQ_ErrInr'
	.calls	'__INDIRECT__','CAN1_ErrInr'
	.calls	'__INDIRECT__','LP875701_ErrInr'
	.extern	ecal_period_1ms
	.extern	ecal_period_2ms
	.extern	ecal_period_5ms
	.extern	ecal_period_10ms
	.extern	ecal_period_20ms
	.extern	ecal_period_50ms
	.extern	EyeQ_ErrInr
	.extern	CAN1_ErrInr
	.extern	LP875701_ErrInr
	.extern	LP87563_ErrInr
	.extern	__INDIRECT__
	.calls	'__INDIRECT__','LP87563_ErrInr'
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	38899
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\Gtm_LCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'ecal_period_1ms',0,1,109,13,1,1,1,1,3
	.byte	'unsigned char',0,1,8,4
	.byte	'ModuleType',0,1,110,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,110,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,110,44
	.word	201
	.byte	3
	.byte	'unsigned short int',0,2,7,4
	.byte	'IrqNotifVal',0,1,110,61
	.word	272
	.byte	0,2
	.byte	'ecal_period_2ms',0,1,111,13,1,1,1,1,4
	.byte	'ModuleType',0,1,112,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,112,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,112,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,112,61
	.word	272
	.byte	0,2
	.byte	'ecal_period_5ms',0,1,113,13,1,1,1,1,4
	.byte	'ModuleType',0,1,114,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,114,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,114,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,114,61
	.word	272
	.byte	0,2
	.byte	'ecal_period_10ms',0,1,115,13,1,1,1,1,4
	.byte	'ModuleType',0,1,116,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,116,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,116,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,116,61
	.word	272
	.byte	0,2
	.byte	'ecal_period_20ms',0,1,117,13,1,1,1,1,4
	.byte	'ModuleType',0,1,118,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,118,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,118,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,118,61
	.word	272
	.byte	0,2
	.byte	'ecal_period_50ms',0,1,119,13,1,1,1,1,4
	.byte	'ModuleType',0,1,120,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,120,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,120,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,120,61
	.word	272
	.byte	0,2
	.byte	'EyeQ_ErrInr',0,1,121,13,1,1,1,1,4
	.byte	'ModuleType',0,1,122,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,122,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,122,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,122,61
	.word	272
	.byte	0,2
	.byte	'CAN1_ErrInr',0,1,123,13,1,1,1,1,4
	.byte	'ModuleType',0,1,124,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,124,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,124,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,124,61
	.word	272
	.byte	0,2
	.byte	'LP875701_ErrInr',0,1,125,13,1,1,1,1,4
	.byte	'ModuleType',0,1,126,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,126,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,126,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,126,61
	.word	272
	.byte	0,2
	.byte	'LP87563_ErrInr',0,1,127,13,1,1,1,1,4
	.byte	'ModuleType',0,1,128,1,10
	.word	201
	.byte	4
	.byte	'ModuleNo',0,1,128,1,28
	.word	201
	.byte	4
	.byte	'ChannelNo',0,1,128,1,44
	.word	201
	.byte	4
	.byte	'IrqNotifVal',0,1,128,1,61
	.word	272
	.byte	0,5
	.byte	'__INDIRECT__',0,1,1,1,1,1,1,6
	.byte	'void',0,7
	.word	1224
	.byte	8
	.byte	'__prof_adm',0,1,1,1
	.word	1230
	.byte	9,1,7
	.word	1254
	.byte	8
	.byte	'__codeptr',0,1,1,1
	.word	1256
	.byte	8
	.byte	'uint8',0,2,90,29
	.word	201
	.byte	8
	.byte	'uint16',0,2,92,29
	.word	272
	.byte	3
	.byte	'unsigned long int',0,4,7,8
	.byte	'uint32',0,2,94,29
	.word	1308
	.byte	8
	.byte	'boolean',0,2,105,29
	.word	201
	.byte	10
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,3,45,16,4,3
	.byte	'unsigned int',0,4,7,11
	.byte	'EN0',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'EN1',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'EN2',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'EN3',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'EN4',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'EN5',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'EN6',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'EN7',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'EN8',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'EN9',0,4
	.word	1386
	.byte	1,22,2,35,0,11
	.byte	'EN10',0,4
	.word	1386
	.byte	1,21,2,35,0,11
	.byte	'EN11',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'EN12',0,4
	.word	1386
	.byte	1,19,2,35,0,11
	.byte	'EN13',0,4
	.word	1386
	.byte	1,18,2,35,0,11
	.byte	'EN14',0,4
	.word	1386
	.byte	1,17,2,35,0,11
	.byte	'EN15',0,4
	.word	1386
	.byte	1,16,2,35,0,11
	.byte	'EN16',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'EN17',0,4
	.word	1386
	.byte	1,14,2,35,0,11
	.byte	'EN18',0,4
	.word	1386
	.byte	1,13,2,35,0,11
	.byte	'EN19',0,4
	.word	1386
	.byte	1,12,2,35,0,11
	.byte	'EN20',0,4
	.word	1386
	.byte	1,11,2,35,0,11
	.byte	'EN21',0,4
	.word	1386
	.byte	1,10,2,35,0,11
	.byte	'EN22',0,4
	.word	1386
	.byte	1,9,2,35,0,11
	.byte	'EN23',0,4
	.word	1386
	.byte	1,8,2,35,0,11
	.byte	'EN24',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'EN25',0,4
	.word	1386
	.byte	1,6,2,35,0,11
	.byte	'EN26',0,4
	.word	1386
	.byte	1,5,2,35,0,11
	.byte	'EN27',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'EN28',0,4
	.word	1386
	.byte	1,3,2,35,0,11
	.byte	'EN29',0,4
	.word	1386
	.byte	1,2,2,35,0,11
	.byte	'EN30',0,4
	.word	1386
	.byte	1,1,2,35,0,11
	.byte	'EN31',0,4
	.word	1386
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_ACCEN0_Bits',0,3,79,3
	.word	1360
	.byte	10
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,3,82,16,4,11
	.byte	'reserved_0',0,4
	.word	1386
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_GTM_ACCEN1_Bits',0,3,85,3
	.word	1933
	.byte	10
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,3,88,16,4,11
	.byte	'SEL0',0,4
	.word	1386
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	1386
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	1386
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	1386
	.byte	4,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,3,95,3
	.word	2010
	.byte	10
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,3,98,16,4,11
	.byte	'SEL0',0,4
	.word	1386
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	1386
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	1386
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	1386
	.byte	4,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,3,105,3
	.word	2164
	.byte	10
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,3,108,16,4,11
	.byte	'TO_ADDR',0,4
	.word	1386
	.byte	20,12,2,35,0,11
	.byte	'TO_W1R0',0,4
	.word	1386
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	1386
	.byte	11,0,2,35,0,0,8
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,3,113,3
	.word	2318
	.byte	10
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,3,116,16,4,11
	.byte	'BRG_MODE',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'MSK_WR_RSP',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	6,24,2,35,0,11
	.byte	'MODE_UP_PGR',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'BUFF_OVL',0,4
	.word	1386
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'SYNC_INPUT_REG',0,4
	.word	1386
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	1386
	.byte	3,16,2,35,0,11
	.byte	'BRG_RST',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	1386
	.byte	7,8,2,35,0,11
	.byte	'BUFF_DPT',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,3,129,1,3
	.word	2446
	.byte	10
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,3,132,1,16,4,11
	.byte	'NEW_TRAN_PTR',0,4
	.word	1386
	.byte	5,27,2,35,0,11
	.byte	'FIRST_RSP_PTR',0,4
	.word	1386
	.byte	5,22,2,35,0,11
	.byte	'TRAN_IN_PGR',0,4
	.word	1386
	.byte	5,17,2,35,0,11
	.byte	'ABT_TRAN_PGR',0,4
	.word	1386
	.byte	5,12,2,35,0,11
	.byte	'FBC',0,4
	.word	1386
	.byte	6,6,2,35,0,11
	.byte	'RSP_TRAN_RDY',0,4
	.word	1386
	.byte	6,0,2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,3,140,1,3
	.word	2753
	.byte	10
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,3,143,1,16,4,11
	.byte	'TRAN_IN_PGR2',0,4
	.word	1386
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1386
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,3,147,1,3
	.word	2955
	.byte	10
	.byte	'_Ifx_GTM_CLC_Bits',0,3,150,1,16,4,11
	.byte	'DISR',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'DISS',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'EDIS',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_CLC_Bits',0,3,157,1,3
	.word	3068
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,3,160,1,16,4,11
	.byte	'CLK_CNT',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,3,164,1,3
	.word	3211
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,3,167,1,16,4,11
	.byte	'CLK_CNT',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'CLK6_SEL',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1386
	.byte	7,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,3,172,1,3
	.word	3328
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,3,175,1,16,4,11
	.byte	'CLK_CNT',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'CLK7_SEL',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1386
	.byte	7,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,3,180,1,3
	.word	3463
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,3,183,1,16,4,11
	.byte	'EN_CLK0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'EN_CLK1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'EN_CLK2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'EN_CLK3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'EN_CLK4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'EN_CLK5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'EN_CLK6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'EN_CLK7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'EN_ECLK0',0,4
	.word	1386
	.byte	2,14,2,35,0,11
	.byte	'EN_ECLK1',0,4
	.word	1386
	.byte	2,12,2,35,0,11
	.byte	'EN_ECLK2',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'EN_FXCLK',0,4
	.word	1386
	.byte	2,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,3,198,1,3
	.word	3598
	.byte	10
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,3,201,1,16,4,11
	.byte	'ECLK_DEN',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,3,205,1,3
	.word	3918
	.byte	10
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,3,208,1,16,4,11
	.byte	'ECLK_NUM',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,3,212,1,3
	.word	4030
	.byte	10
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,3,215,1,16,4,11
	.byte	'FXCLK_SEL',0,4
	.word	1386
	.byte	4,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,3,219,1,3
	.word	4142
	.byte	10
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,3,222,1,16,4,11
	.byte	'GCLK_DEN',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,3,226,1,3
	.word	4258
	.byte	10
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,3,229,1,16,4,11
	.byte	'GCLK_NUM',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,3,233,1,3
	.word	4370
	.byte	10
	.byte	'_Ifx_GTM_CTRL_Bits',0,3,236,1,16,4,11
	.byte	'RF_PROT',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'TO_MODE',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'TO_VAL',0,4
	.word	1386
	.byte	5,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	1386
	.byte	23,0,2,35,0,0,8
	.byte	'Ifx_GTM_CTRL_Bits',0,3,243,1,3
	.word	4482
	.byte	10
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,3,246,1,16,4,11
	.byte	'O1SEL_0',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1386
	.byte	2,29,2,35,0,11
	.byte	'SWAP_0',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'O1F_0',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'O1SEL_1',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'I1SEL_1',0,4
	.word	1386
	.byte	1,22,2,35,0,11
	.byte	'SH_EN_1',0,4
	.word	1386
	.byte	1,21,2,35,0,11
	.byte	'SWAP_1',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'O1F_1',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'O1SEL_2',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'I1SEL_2',0,4
	.word	1386
	.byte	1,14,2,35,0,11
	.byte	'SH_EN_2',0,4
	.word	1386
	.byte	1,13,2,35,0,11
	.byte	'SWAP_2',0,4
	.word	1386
	.byte	1,12,2,35,0,11
	.byte	'O1F_2',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'reserved_22',0,4
	.word	1386
	.byte	2,8,2,35,0,11
	.byte	'O1SEL_3',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'I1SEL_3',0,4
	.word	1386
	.byte	1,6,2,35,0,11
	.byte	'SH_EN_3',0,4
	.word	1386
	.byte	1,5,2,35,0,11
	.byte	'SWAP_3',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'O1F_3',0,4
	.word	1386
	.byte	2,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,3,143,2,3
	.word	4635
	.byte	10
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,3,146,2,16,4,11
	.byte	'POL0_0',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'OC0_0',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'SL0_0',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'DT0_0',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'POL1_0',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'OC1_0',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'SL1_0',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'DT1_0',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'POL0_1',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'OC0_1',0,4
	.word	1386
	.byte	1,22,2,35,0,11
	.byte	'SL0_1',0,4
	.word	1386
	.byte	1,21,2,35,0,11
	.byte	'DT0_1',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'POL1_1',0,4
	.word	1386
	.byte	1,19,2,35,0,11
	.byte	'OC1_1',0,4
	.word	1386
	.byte	1,18,2,35,0,11
	.byte	'SL1_1',0,4
	.word	1386
	.byte	1,17,2,35,0,11
	.byte	'DT1_1',0,4
	.word	1386
	.byte	1,16,2,35,0,11
	.byte	'POL0_2',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'OC0_2',0,4
	.word	1386
	.byte	1,14,2,35,0,11
	.byte	'SL0_2',0,4
	.word	1386
	.byte	1,13,2,35,0,11
	.byte	'DT0_2',0,4
	.word	1386
	.byte	1,12,2,35,0,11
	.byte	'POL1_2',0,4
	.word	1386
	.byte	1,11,2,35,0,11
	.byte	'OC1_2',0,4
	.word	1386
	.byte	1,10,2,35,0,11
	.byte	'SL1_2',0,4
	.word	1386
	.byte	1,9,2,35,0,11
	.byte	'DT1_2',0,4
	.word	1386
	.byte	1,8,2,35,0,11
	.byte	'POL0_3',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'OC0_3',0,4
	.word	1386
	.byte	1,6,2,35,0,11
	.byte	'SL0_3',0,4
	.word	1386
	.byte	1,5,2,35,0,11
	.byte	'DT0_3',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'POL1_3',0,4
	.word	1386
	.byte	1,3,2,35,0,11
	.byte	'OC1_3',0,4
	.word	1386
	.byte	1,2,2,35,0,11
	.byte	'SL1_3',0,4
	.word	1386
	.byte	1,1,2,35,0,11
	.byte	'DT1_3',0,4
	.word	1386
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,3,180,2,3
	.word	5147
	.byte	10
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,3,183,2,16,4,11
	.byte	'POL0_0_SR',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'OC0_0_SR',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'SL0_0_SR',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'DT0_0_SR',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'POL1_0_SR',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'OC1_0_SR',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'SL1_0_SR',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'DT1_0_SR',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'POL0_1_SR',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'OC0_1_SR',0,4
	.word	1386
	.byte	1,22,2,35,0,11
	.byte	'SL0_1_SR',0,4
	.word	1386
	.byte	1,21,2,35,0,11
	.byte	'DT0_1_SR',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'POL1_1_SR',0,4
	.word	1386
	.byte	1,19,2,35,0,11
	.byte	'OC1_1_SR',0,4
	.word	1386
	.byte	1,18,2,35,0,11
	.byte	'SL1_1_SR',0,4
	.word	1386
	.byte	1,17,2,35,0,11
	.byte	'DT1_1_SR',0,4
	.word	1386
	.byte	1,16,2,35,0,11
	.byte	'POL0_2_SR',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'OC0_2_SR',0,4
	.word	1386
	.byte	1,14,2,35,0,11
	.byte	'SL0_2_SR',0,4
	.word	1386
	.byte	1,13,2,35,0,11
	.byte	'DT0_2_SR',0,4
	.word	1386
	.byte	1,12,2,35,0,11
	.byte	'POL1_2_SR',0,4
	.word	1386
	.byte	1,11,2,35,0,11
	.byte	'OC1_2_SR',0,4
	.word	1386
	.byte	1,10,2,35,0,11
	.byte	'SL1_2_SR',0,4
	.word	1386
	.byte	1,9,2,35,0,11
	.byte	'DT1_2_SR',0,4
	.word	1386
	.byte	1,8,2,35,0,11
	.byte	'POL0_3_SR',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'OC0_3_SR',0,4
	.word	1386
	.byte	1,6,2,35,0,11
	.byte	'SL0_3_SR',0,4
	.word	1386
	.byte	1,5,2,35,0,11
	.byte	'DT0_3_SR',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'POL1_3_SR',0,4
	.word	1386
	.byte	1,3,2,35,0,11
	.byte	'OC1_3_SR',0,4
	.word	1386
	.byte	1,2,2,35,0,11
	.byte	'SL1_3_SR',0,4
	.word	1386
	.byte	1,1,2,35,0,11
	.byte	'DT1_3_SR',0,4
	.word	1386
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,3,217,2,3
	.word	5768
	.byte	10
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,3,220,2,16,4,11
	.byte	'CLK_SEL',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'UPD_MODE',0,4
	.word	1386
	.byte	3,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	1386
	.byte	25,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,3,226,2,3
	.word	6491
	.byte	10
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,3,229,2,16,4,11
	.byte	'RELRISE',0,4
	.word	1386
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1386
	.byte	6,16,2,35,0,11
	.byte	'RELFALL',0,4
	.word	1386
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	1386
	.byte	6,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,3,235,2,3
	.word	6635
	.byte	10
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,3,238,2,16,4,11
	.byte	'RELBLK',0,4
	.word	1386
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	1386
	.byte	6,16,2,35,0,11
	.byte	'PSU_IN_SEL',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'IN_POL',0,4
	.word	1386
	.byte	1,14,2,35,0,11
	.byte	'reserved_18',0,4
	.word	1386
	.byte	2,12,2,35,0,11
	.byte	'SHIFT_SEL',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'reserved_22',0,4
	.word	1386
	.byte	10,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,3,247,2,3
	.word	6784
	.byte	10
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,3,250,2,16,4,11
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,3,129,3,3
	.word	6999
	.byte	10
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,3,132,3,16,4,11
	.byte	'GRSTEN',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'BRIDGE_MODE_RST',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'AEI_IN',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1386
	.byte	5,24,2,35,0,11
	.byte	'TOM_OUT_RST',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	1386
	.byte	3,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	1386
	.byte	4,16,2,35,0,11
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'IRQ_MODE_PULSE',0,4
	.word	1386
	.byte	1,14,2,35,0,11
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	1386
	.byte	1,13,2,35,0,11
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	1386
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1386
	.byte	12,0,2,35,0,0,8
	.byte	'Ifx_GTM_HW_CONF_Bits',0,3,146,3,3
	.word	7203
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,3,149,3,16,4,11
	.byte	'reserved_0',0,4
	.word	1386
	.byte	4,28,2,35,0,11
	.byte	'AEI_IRQ',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1386
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,3,154,3,3
	.word	7560
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,3,157,3,16,4,11
	.byte	'TIM0_CH0_IRQ',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'TIM0_CH1_IRQ',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'TIM0_CH2_IRQ',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'TIM0_CH3_IRQ',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'TIM0_CH4_IRQ',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'TIM0_CH5_IRQ',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'TIM0_CH6_IRQ',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'TIM0_CH7_IRQ',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1386
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,3,168,3,3
	.word	7688
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,3,171,3,16,4,11
	.byte	'TOM0_CH0_IRQ',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'TOM0_CH1_IRQ',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'TOM0_CH2_IRQ',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'TOM0_CH3_IRQ',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'TOM0_CH4_IRQ',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'TOM0_CH5_IRQ',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'TOM0_CH6_IRQ',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'TOM0_CH7_IRQ',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'TOM0_CH8_IRQ',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'TOM0_CH9_IRQ',0,4
	.word	1386
	.byte	1,22,2,35,0,11
	.byte	'TOM0_CH10_IRQ',0,4
	.word	1386
	.byte	1,21,2,35,0,11
	.byte	'TOM0_CH11_IRQ',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'TOM0_CH12_IRQ',0,4
	.word	1386
	.byte	1,19,2,35,0,11
	.byte	'TOM0_CH13_IRQ',0,4
	.word	1386
	.byte	1,18,2,35,0,11
	.byte	'TOM0_CH14_IRQ',0,4
	.word	1386
	.byte	1,17,2,35,0,11
	.byte	'TOM0_CH15_IRQ',0,4
	.word	1386
	.byte	1,16,2,35,0,11
	.byte	'TOM1_CH0_IRQ',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'TOM1_CH1_IRQ',0,4
	.word	1386
	.byte	1,14,2,35,0,11
	.byte	'TOM1_CH2_IRQ',0,4
	.word	1386
	.byte	1,13,2,35,0,11
	.byte	'TOM1_CH3_IRQ',0,4
	.word	1386
	.byte	1,12,2,35,0,11
	.byte	'TOM1_CH4_IRQ',0,4
	.word	1386
	.byte	1,11,2,35,0,11
	.byte	'TOM1_CH5_IRQ',0,4
	.word	1386
	.byte	1,10,2,35,0,11
	.byte	'TOM1_CH6_IRQ',0,4
	.word	1386
	.byte	1,9,2,35,0,11
	.byte	'TOM1_CH7_IRQ',0,4
	.word	1386
	.byte	1,8,2,35,0,11
	.byte	'TOM1_CH8_IRQ',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'TOM1_CH9_IRQ',0,4
	.word	1386
	.byte	1,6,2,35,0,11
	.byte	'TOM1_CH10_IRQ',0,4
	.word	1386
	.byte	1,5,2,35,0,11
	.byte	'TOM1_CH11_IRQ',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'TOM1_CH12_IRQ',0,4
	.word	1386
	.byte	1,3,2,35,0,11
	.byte	'TOM1_CH13_IRQ',0,4
	.word	1386
	.byte	1,2,2,35,0,11
	.byte	'TOM1_CH14_IRQ',0,4
	.word	1386
	.byte	1,1,2,35,0,11
	.byte	'TOM1_CH15_IRQ',0,4
	.word	1386
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,3,205,3,3
	.word	7967
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,3,208,3,16,4,11
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1386
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,3,219,3,3
	.word	8812
	.byte	10
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,3,222,3,16,4,11
	.byte	'GTM_EIRQ',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1386
	.byte	3,28,2,35,0,11
	.byte	'TIM0_EIRQ',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1386
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,3,228,3,3
	.word	9105
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,3,231,3,16,4,11
	.byte	'SEL0',0,4
	.word	1386
	.byte	4,28,2,35,0,11
	.byte	'SEL1',0,4
	.word	1386
	.byte	4,24,2,35,0,11
	.byte	'SEL2',0,4
	.word	1386
	.byte	4,20,2,35,0,11
	.byte	'SEL3',0,4
	.word	1386
	.byte	4,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,3,238,3,3
	.word	9259
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,3,241,3,16,4,11
	.byte	'SEL0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'SEL1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'SEL2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'SEL3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'SEL4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'SEL5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'SEL6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'SEL7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'SEL8',0,4
	.word	1386
	.byte	2,14,2,35,0,11
	.byte	'SEL9',0,4
	.word	1386
	.byte	2,12,2,35,0,11
	.byte	'SEL10',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'SEL11',0,4
	.word	1386
	.byte	2,8,2,35,0,11
	.byte	'SEL12',0,4
	.word	1386
	.byte	2,6,2,35,0,11
	.byte	'SEL13',0,4
	.word	1386
	.byte	2,4,2,35,0,11
	.byte	'SEL14',0,4
	.word	1386
	.byte	2,2,2,35,0,11
	.byte	'SEL15',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,3,131,4,3
	.word	9429
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,3,134,4,16,4,11
	.byte	'CH0SEL',0,4
	.word	1386
	.byte	4,28,2,35,0,11
	.byte	'CH1SEL',0,4
	.word	1386
	.byte	4,24,2,35,0,11
	.byte	'CH2SEL',0,4
	.word	1386
	.byte	4,20,2,35,0,11
	.byte	'CH3SEL',0,4
	.word	1386
	.byte	4,16,2,35,0,11
	.byte	'CH4SEL',0,4
	.word	1386
	.byte	4,12,2,35,0,11
	.byte	'CH5SEL',0,4
	.word	1386
	.byte	4,8,2,35,0,11
	.byte	'CH6SEL',0,4
	.word	1386
	.byte	4,4,2,35,0,11
	.byte	'CH7SEL',0,4
	.word	1386
	.byte	4,0,2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,3,144,4,3
	.word	9770
	.byte	10
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,3,147,4,16,4,11
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,3,154,4,3
	.word	9995
	.byte	10
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,3,157,4,16,4,11
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'TRG_AEI_USP_BE',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,3,164,4,3
	.word	10193
	.byte	10
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,3,167,4,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,3,171,4,3
	.word	10389
	.byte	10
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,3,174,4,16,4,11
	.byte	'AEI_TO_XPT',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'AEI_USP_ADDR',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'AEI_IM_ADDR',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'AEI_USP_BE',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,3,181,4,3
	.word	10492
	.byte	10
	.byte	'_Ifx_GTM_KRST0_Bits',0,3,184,4,16,4,11
	.byte	'RST',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'RSTSTAT',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_KRST0_Bits',0,3,189,4,3
	.word	10670
	.byte	10
	.byte	'_Ifx_GTM_KRST1_Bits',0,3,192,4,16,4,11
	.byte	'RST',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1386
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_GTM_KRST1_Bits',0,3,196,4,3
	.word	10781
	.byte	10
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,3,199,4,16,4,11
	.byte	'CLR',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1386
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,3,203,4,3
	.word	10873
	.byte	10
	.byte	'_Ifx_GTM_OCS_Bits',0,3,206,4,16,4,11
	.byte	'reserved_0',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,4
	.word	1386
	.byte	4,4,2,35,0,11
	.byte	'SUS_P',0,4
	.word	1386
	.byte	1,3,2,35,0,11
	.byte	'SUSSTA',0,4
	.word	1386
	.byte	1,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_OCS_Bits',0,3,213,4,3
	.word	10969
	.byte	10
	.byte	'_Ifx_GTM_ODA_Bits',0,3,216,4,16,4,11
	.byte	'DDREN',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'DREN',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_ODA_Bits',0,3,221,4,3
	.word	11115
	.byte	10
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,3,224,4,16,4,11
	.byte	'CV',0,4
	.word	1386
	.byte	27,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'CM',0,4
	.word	1386
	.byte	2,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTBU0T_Bits',0,3,230,4,3
	.word	11221
	.byte	10
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,3,233,4,16,4,11
	.byte	'CV',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	4,4,2,35,0,11
	.byte	'EN',0,4
	.word	1386
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	1386
	.byte	3,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTBU1T_Bits',0,3,239,4,3
	.word	11352
	.byte	10
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,3,242,4,16,4,11
	.byte	'CV',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	4,4,2,35,0,11
	.byte	'EN',0,4
	.word	1386
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	1386
	.byte	3,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTBU2T_Bits',0,3,248,4,3
	.word	11483
	.byte	10
	.byte	'_Ifx_GTM_OTSC0_Bits',0,3,251,4,16,4,11
	.byte	'B0LMT',0,4
	.word	1386
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'B0LMI',0,4
	.word	1386
	.byte	4,24,2,35,0,11
	.byte	'B0HMT',0,4
	.word	1386
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'B0HMI',0,4
	.word	1386
	.byte	4,16,2,35,0,11
	.byte	'B1LMT',0,4
	.word	1386
	.byte	3,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	1386
	.byte	1,12,2,35,0,11
	.byte	'B1LMI',0,4
	.word	1386
	.byte	4,8,2,35,0,11
	.byte	'B1HMT',0,4
	.word	1386
	.byte	3,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'B1HMI',0,4
	.word	1386
	.byte	4,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTSC0_Bits',0,3,137,5,3
	.word	11614
	.byte	10
	.byte	'_Ifx_GTM_OTSS_Bits',0,3,140,5,16,4,11
	.byte	'OTGB0',0,4
	.word	1386
	.byte	4,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	4,24,2,35,0,11
	.byte	'OTGB1',0,4
	.word	1386
	.byte	4,20,2,35,0,11
	.byte	'reserved_12',0,4
	.word	1386
	.byte	4,16,2,35,0,11
	.byte	'OTGB2',0,4
	.word	1386
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	1386
	.byte	12,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTSS_Bits',0,3,148,5,3
	.word	11896
	.byte	10
	.byte	'_Ifx_GTM_REV_Bits',0,3,151,5,16,4,11
	.byte	'STEP',0,4
	.word	1386
	.byte	8,24,2,35,0,11
	.byte	'NO',0,4
	.word	1386
	.byte	4,20,2,35,0,11
	.byte	'MINOR',0,4
	.word	1386
	.byte	4,16,2,35,0,11
	.byte	'MAJOR',0,4
	.word	1386
	.byte	4,12,2,35,0,11
	.byte	'DEV_CODE0',0,4
	.word	1386
	.byte	4,8,2,35,0,11
	.byte	'DEV_CODE1',0,4
	.word	1386
	.byte	4,4,2,35,0,11
	.byte	'DEV_CODE2',0,4
	.word	1386
	.byte	4,0,2,35,0,0,8
	.byte	'Ifx_GTM_REV_Bits',0,3,160,5,3
	.word	12068
	.byte	10
	.byte	'_Ifx_GTM_RST_Bits',0,3,163,5,16,4,11
	.byte	'RST',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1386
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_GTM_RST_Bits',0,3,167,5,3
	.word	12246
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,3,170,5,16,4,11
	.byte	'BASE',0,4
	.word	1386
	.byte	27,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	1386
	.byte	5,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,3,174,5,3
	.word	12334
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,3,177,5,16,4,11
	.byte	'LOW_RES',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	1386
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,3,182,5,3
	.word	12442
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,3,185,5,16,4,11
	.byte	'BASE',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,3,189,5,3
	.word	12574
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,3,192,5,16,4,11
	.byte	'CH_MODE',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	1386
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,3,197,5,3
	.word	12682
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,3,200,5,16,4,11
	.byte	'BASE',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,3,204,5,3
	.word	12814
	.byte	10
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,3,207,5,16,4,11
	.byte	'CH_MODE',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'CH_CLK_SRC',0,4
	.word	1386
	.byte	3,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	1386
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,3,212,5,3
	.word	12922
	.byte	10
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,3,215,5,16,4,11
	.byte	'ENDIS_CH0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CH1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CH2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	1386
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,3,221,5,3
	.word	13054
	.byte	10
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,3,224,5,16,4,11
	.byte	'SRC_CH0',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'SRC_CH1',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'SRC_CH2',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'SRC_CH3',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'SRC_CH4',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'SRC_CH5',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'SRC_CH6',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'SRC_CH7',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1386
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,3,235,5,3
	.word	13200
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,3,238,5,16,4,11
	.byte	'CNT',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,3,242,5,3
	.word	13447
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,3,245,5,16,4,11
	.byte	'CNTS',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,3,249,5,3
	.word	13550
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,3,252,5,16,4,11
	.byte	'TIM_EN',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'TIM_MODE',0,4
	.word	1386
	.byte	3,28,2,35,0,11
	.byte	'OSM',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'CICTRL',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'TBU0x_SEL',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'GPR0_SEL',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'GPR1_SEL',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'CNTS_SEL',0,4
	.word	1386
	.byte	1,19,2,35,0,11
	.byte	'DSL',0,4
	.word	1386
	.byte	1,18,2,35,0,11
	.byte	'ISL',0,4
	.word	1386
	.byte	1,17,2,35,0,11
	.byte	'ECNT_RESET',0,4
	.word	1386
	.byte	1,16,2,35,0,11
	.byte	'FLT_EN',0,4
	.word	1386
	.byte	1,15,2,35,0,11
	.byte	'FLT_CNT_FRQ',0,4
	.word	1386
	.byte	2,13,2,35,0,11
	.byte	'EXT_CAP_EN',0,4
	.word	1386
	.byte	1,12,2,35,0,11
	.byte	'FLT_MODE_RE',0,4
	.word	1386
	.byte	1,11,2,35,0,11
	.byte	'FLT_CTR_RE',0,4
	.word	1386
	.byte	1,10,2,35,0,11
	.byte	'FLT_MODE_FE',0,4
	.word	1386
	.byte	1,9,2,35,0,11
	.byte	'FLT_CTR_FE',0,4
	.word	1386
	.byte	1,8,2,35,0,11
	.byte	'CLK_SEL',0,4
	.word	1386
	.byte	3,5,2,35,0,11
	.byte	'FR_ECNT_OFL',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'EGPR0_SEL',0,4
	.word	1386
	.byte	1,3,2,35,0,11
	.byte	'EGPR1_SEL',0,4
	.word	1386
	.byte	1,2,2,35,0,11
	.byte	'TOCTRL',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,3,150,6,3
	.word	13649
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,3,153,6,16,4,11
	.byte	'ECNT',0,4
	.word	1386
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,3,157,6,3
	.word	14197
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,3,160,6,16,4,11
	.byte	'EXT_CAP_SRC',0,4
	.word	1386
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	1386
	.byte	29,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,3,164,6,3
	.word	14303
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,3,167,6,16,4,11
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'TODET_EIRQ_EN',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	1386
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,3,176,6,3
	.word	14417
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,3,179,6,16,4,11
	.byte	'FLT_FE',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,3,183,6,3
	.word	14672
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,3,186,6,16,4,11
	.byte	'FLT_RE',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,3,190,6,3
	.word	14784
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,3,193,6,16,4,11
	.byte	'GPR0',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,3,197,6,3
	.word	14896
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,3,200,6,16,4,11
	.byte	'GPR1',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'ECNT',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,3,204,6,3
	.word	14995
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,3,207,6,16,4,11
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'TODET_IRQ_EN',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	1386
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,3,216,6,3
	.word	15094
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,3,219,6,16,4,11
	.byte	'TRG_NEWVAL',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'TRG_ECNTOFL',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'TRG_CNTOFL',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'TRG_GPRzOFL',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'TRG_TODET',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'TRG_GLITCHDET',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	1386
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,3,228,6,3
	.word	15341
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,3,231,6,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,3,235,6,3
	.word	15580
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,3,238,6,16,4,11
	.byte	'NEWVAL',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'ECNTOFL',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'CNTOFL',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'GPRzOFL',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'TODET',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'GLITCHDET',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'reserved_6',0,4
	.word	1386
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,3,247,6,3
	.word	15697
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,3,250,6,16,4,11
	.byte	'TO_CNT',0,4
	.word	1386
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1386
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,3,254,6,3
	.word	15910
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,3,129,7,16,4,11
	.byte	'TOV',0,4
	.word	1386
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1386
	.byte	20,4,2,35,0,11
	.byte	'TCS',0,4
	.word	1386
	.byte	3,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	1386
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,3,135,7,3
	.word	16017
	.byte	10
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,3,138,7,16,4,11
	.byte	'VAL_0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'MODE_0',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'VAL_1',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'MODE_1',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'VAL_2',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'MODE_2',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'VAL_3',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'MODE_3',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'VAL_4',0,4
	.word	1386
	.byte	2,14,2,35,0,11
	.byte	'MODE_4',0,4
	.word	1386
	.byte	2,12,2,35,0,11
	.byte	'VAL_5',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'MODE_5',0,4
	.word	1386
	.byte	2,8,2,35,0,11
	.byte	'VAL_6',0,4
	.word	1386
	.byte	2,6,2,35,0,11
	.byte	'MODE_6',0,4
	.word	1386
	.byte	2,4,2,35,0,11
	.byte	'VAL_7',0,4
	.word	1386
	.byte	2,2,2,35,0,11
	.byte	'MODE_7',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,3,156,7,3
	.word	16159
	.byte	10
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,3,159,7,16,4,11
	.byte	'F_OUT',0,4
	.word	1386
	.byte	8,24,2,35,0,11
	.byte	'F_IN',0,4
	.word	1386
	.byte	8,16,2,35,0,11
	.byte	'TIM_IN',0,4
	.word	1386
	.byte	8,8,2,35,0,11
	.byte	'reserved_24',0,4
	.word	1386
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,3,165,7,3
	.word	16504
	.byte	10
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,3,168,7,16,4,11
	.byte	'RST_CH0',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	1386
	.byte	1,29,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	1386
	.byte	1,28,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	1386
	.byte	1,27,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	1386
	.byte	1,26,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	1386
	.byte	1,25,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	1386
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	1386
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_RST_Bits',0,3,179,7,3
	.word	16645
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,3,182,7,16,4,11
	.byte	'CM0',0,4
	.word	1386
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,3,186,7,3
	.word	16878
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,3,189,7,16,4,11
	.byte	'CM1',0,4
	.word	1386
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,3,193,7,3
	.word	16981
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,3,196,7,16,4,11
	.byte	'CN0',0,4
	.word	1386
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,3,200,7,3
	.word	17084
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,3,203,7,16,4,11
	.byte	'reserved_0',0,4
	.word	1386
	.byte	11,21,2,35,0,11
	.byte	'SL',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'CLK_SRC_SR',0,4
	.word	1386
	.byte	3,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	1386
	.byte	5,12,2,35,0,11
	.byte	'RST_CCU0',0,4
	.word	1386
	.byte	1,11,2,35,0,11
	.byte	'OSM_TRIG',0,4
	.word	1386
	.byte	1,10,2,35,0,11
	.byte	'EXT_TRIG',0,4
	.word	1386
	.byte	1,9,2,35,0,11
	.byte	'EXTTRIGOUT',0,4
	.word	1386
	.byte	1,8,2,35,0,11
	.byte	'TRIGOUT',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	1386
	.byte	1,6,2,35,0,11
	.byte	'OSM',0,4
	.word	1386
	.byte	1,5,2,35,0,11
	.byte	'BITREV',0,4
	.word	1386
	.byte	1,4,2,35,0,11
	.byte	'reserved_28',0,4
	.word	1386
	.byte	4,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,3,218,7,3
	.word	17187
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,3,221,7,16,4,11
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,3,226,7,3
	.word	17515
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,3,229,7,16,4,11
	.byte	'TRG_CCU0TC0',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'TRG_CCU1TC0',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,3,234,7,3
	.word	17658
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,3,237,7,16,4,11
	.byte	'IRQ_MODE',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,3,241,7,3
	.word	17807
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,3,244,7,16,4,11
	.byte	'CCU0TC',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'CCU1TC',0,4
	.word	1386
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	1386
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,3,249,7,3
	.word	17924
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,3,252,7,16,4,11
	.byte	'SR0',0,4
	.word	1386
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,3,128,8,3
	.word	18061
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,3,131,8,16,4,11
	.byte	'SR1',0,4
	.word	1386
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,3,135,8,3
	.word	18164
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,3,138,8,16,4,11
	.byte	'OL',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1386
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,3,142,8,3
	.word	18267
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,3,145,8,16,4,11
	.byte	'ACT_TB',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'TB_TRIG',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'TBU_SEL',0,4
	.word	1386
	.byte	2,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	1386
	.byte	5,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,3,151,8,3
	.word	18370
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,3,154,8,16,4,11
	.byte	'ENDIS_CTRL0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CTRL1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CTRL2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_CTRL3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_CTRL4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_CTRL5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_CTRL6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_CTRL7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,3,165,8,3
	.word	18524
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,3,168,8,16,4,11
	.byte	'ENDIS_STAT0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_STAT1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_STAT2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_STAT3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_STAT4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_STAT5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_STAT6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_STAT7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,3,179,8,3
	.word	18814
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,3,182,8,16,4,11
	.byte	'FUPD_CTRL0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'FUPD_CTRL1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'FUPD_CTRL2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'FUPD_CTRL3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'FUPD_CTRL4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'FUPD_CTRL5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'FUPD_CTRL6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'FUPD_CTRL7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'RSTCN0_CH0',0,4
	.word	1386
	.byte	2,14,2,35,0,11
	.byte	'RSTCN0_CH1',0,4
	.word	1386
	.byte	2,12,2,35,0,11
	.byte	'RSTCN0_CH2',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'RSTCN0_CH3',0,4
	.word	1386
	.byte	2,8,2,35,0,11
	.byte	'RSTCN0_CH4',0,4
	.word	1386
	.byte	2,6,2,35,0,11
	.byte	'RSTCN0_CH5',0,4
	.word	1386
	.byte	2,4,2,35,0,11
	.byte	'RSTCN0_CH6',0,4
	.word	1386
	.byte	2,2,2,35,0,11
	.byte	'RSTCN0_CH7',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,3,200,8,3
	.word	19104
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,3,203,8,16,4,11
	.byte	'HOST_TRIG',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1386
	.byte	7,24,2,35,0,11
	.byte	'RST_CH0',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	1386
	.byte	1,22,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	1386
	.byte	1,21,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	1386
	.byte	1,19,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	1386
	.byte	1,18,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	1386
	.byte	1,17,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	1386
	.byte	1,16,2,35,0,11
	.byte	'UPEN_CTRL0',0,4
	.word	1386
	.byte	2,14,2,35,0,11
	.byte	'UPEN_CTRL1',0,4
	.word	1386
	.byte	2,12,2,35,0,11
	.byte	'UPEN_CTRL2',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'UPEN_CTRL3',0,4
	.word	1386
	.byte	2,8,2,35,0,11
	.byte	'UPEN_CTRL4',0,4
	.word	1386
	.byte	2,6,2,35,0,11
	.byte	'UPEN_CTRL5',0,4
	.word	1386
	.byte	2,4,2,35,0,11
	.byte	'UPEN_CTRL6',0,4
	.word	1386
	.byte	2,2,2,35,0,11
	.byte	'UPEN_CTRL7',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,3,223,8,3
	.word	19537
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,3,226,8,16,4,11
	.byte	'INT_TRIG0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'INT_TRIG1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'INT_TRIG2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'INT_TRIG3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'INT_TRIG4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'INT_TRIG5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'INT_TRIG6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'INT_TRIG7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,3,237,8,3
	.word	19987
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,3,240,8,16,4,11
	.byte	'OUTEN_CTRL0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_CTRL1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_CTRL2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_CTRL3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_CTRL4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_CTRL5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_CTRL6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_CTRL7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,3,251,8,3
	.word	20257
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,3,254,8,16,4,11
	.byte	'OUTEN_STAT0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_STAT1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_STAT2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_STAT3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_STAT4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_STAT5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_STAT6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_STAT7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,3,137,9,3
	.word	20547
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,3,140,9,16,4,11
	.byte	'ACT_TB',0,4
	.word	1386
	.byte	24,8,2,35,0,11
	.byte	'TB_TRIG',0,4
	.word	1386
	.byte	1,7,2,35,0,11
	.byte	'TBU_SEL',0,4
	.word	1386
	.byte	2,5,2,35,0,11
	.byte	'reserved_27',0,4
	.word	1386
	.byte	5,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,3,146,9,3
	.word	20837
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,3,149,9,16,4,11
	.byte	'ENDIS_CTRL0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_CTRL1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_CTRL2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_CTRL3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_CTRL4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_CTRL5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_CTRL6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_CTRL7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,3,160,9,3
	.word	20991
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,3,163,9,16,4,11
	.byte	'ENDIS_STAT0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'ENDIS_STAT1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'ENDIS_STAT2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'ENDIS_STAT3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'ENDIS_STAT4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'ENDIS_STAT5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'ENDIS_STAT6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'ENDIS_STAT7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,3,174,9,3
	.word	21281
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,3,177,9,16,4,11
	.byte	'FUPD_CTRL0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'FUPD_CTRL1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'FUPD_CTRL2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'FUPD_CTRL3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'FUPD_CTRL4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'FUPD_CTRL5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'FUPD_CTRL6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'FUPD_CTRL7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'RSTCN0_CH0',0,4
	.word	1386
	.byte	2,14,2,35,0,11
	.byte	'RSTCN0_CH1',0,4
	.word	1386
	.byte	2,12,2,35,0,11
	.byte	'RSTCN0_CH2',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'RSTCN0_CH3',0,4
	.word	1386
	.byte	2,8,2,35,0,11
	.byte	'RSTCN0_CH4',0,4
	.word	1386
	.byte	2,6,2,35,0,11
	.byte	'RSTCN0_CH5',0,4
	.word	1386
	.byte	2,4,2,35,0,11
	.byte	'RSTCN0_CH6',0,4
	.word	1386
	.byte	2,2,2,35,0,11
	.byte	'RSTCN0_CH7',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,3,195,9,3
	.word	21571
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,3,198,9,16,4,11
	.byte	'HOST_TRIG',0,4
	.word	1386
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	1386
	.byte	7,24,2,35,0,11
	.byte	'RST_CH0',0,4
	.word	1386
	.byte	1,23,2,35,0,11
	.byte	'RST_CH1',0,4
	.word	1386
	.byte	1,22,2,35,0,11
	.byte	'RST_CH2',0,4
	.word	1386
	.byte	1,21,2,35,0,11
	.byte	'RST_CH3',0,4
	.word	1386
	.byte	1,20,2,35,0,11
	.byte	'RST_CH4',0,4
	.word	1386
	.byte	1,19,2,35,0,11
	.byte	'RST_CH5',0,4
	.word	1386
	.byte	1,18,2,35,0,11
	.byte	'RST_CH6',0,4
	.word	1386
	.byte	1,17,2,35,0,11
	.byte	'RST_CH7',0,4
	.word	1386
	.byte	1,16,2,35,0,11
	.byte	'UPEN_CTRL0',0,4
	.word	1386
	.byte	2,14,2,35,0,11
	.byte	'UPEN_CTRL1',0,4
	.word	1386
	.byte	2,12,2,35,0,11
	.byte	'UPEN_CTRL2',0,4
	.word	1386
	.byte	2,10,2,35,0,11
	.byte	'UPEN_CTRL3',0,4
	.word	1386
	.byte	2,8,2,35,0,11
	.byte	'UPEN_CTRL4',0,4
	.word	1386
	.byte	2,6,2,35,0,11
	.byte	'UPEN_CTRL5',0,4
	.word	1386
	.byte	2,4,2,35,0,11
	.byte	'UPEN_CTRL6',0,4
	.word	1386
	.byte	2,2,2,35,0,11
	.byte	'UPEN_CTRL7',0,4
	.word	1386
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,3,218,9,3
	.word	22004
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,3,221,9,16,4,11
	.byte	'INT_TRIG0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'INT_TRIG1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'INT_TRIG2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'INT_TRIG3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'INT_TRIG4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'INT_TRIG5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'INT_TRIG6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'INT_TRIG7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,3,232,9,3
	.word	22454
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,3,235,9,16,4,11
	.byte	'OUTEN_CTRL0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_CTRL1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_CTRL2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_CTRL3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_CTRL4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_CTRL5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_CTRL6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_CTRL7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,3,246,9,3
	.word	22724
	.byte	10
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,3,249,9,16,4,11
	.byte	'OUTEN_STAT0',0,4
	.word	1386
	.byte	2,30,2,35,0,11
	.byte	'OUTEN_STAT1',0,4
	.word	1386
	.byte	2,28,2,35,0,11
	.byte	'OUTEN_STAT2',0,4
	.word	1386
	.byte	2,26,2,35,0,11
	.byte	'OUTEN_STAT3',0,4
	.word	1386
	.byte	2,24,2,35,0,11
	.byte	'OUTEN_STAT4',0,4
	.word	1386
	.byte	2,22,2,35,0,11
	.byte	'OUTEN_STAT5',0,4
	.word	1386
	.byte	2,20,2,35,0,11
	.byte	'OUTEN_STAT6',0,4
	.word	1386
	.byte	2,18,2,35,0,11
	.byte	'OUTEN_STAT7',0,4
	.word	1386
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	1386
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,3,132,10,3
	.word	23014
	.byte	12,3,140,10,9,4,3
	.byte	'unsigned int',0,4,7,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,3
	.byte	'int',0,4,5,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	1360
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ACCEN0',0,3,145,10,3
	.word	23304
	.byte	12,3,148,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	1933
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ACCEN1',0,3,153,10,3
	.word	23391
	.byte	12,3,156,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2010
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,3,161,10,3
	.word	23455
	.byte	12,3,164,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2164
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,3,169,10,3
	.word	23525
	.byte	12,3,172,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2318
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,3,177,10,3
	.word	23595
	.byte	12,3,180,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2446
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_MODE',0,3,185,10,3
	.word	23665
	.byte	12,3,188,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2753
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,3,193,10,3
	.word	23734
	.byte	12,3,196,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2955
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,3,201,10,3
	.word	23803
	.byte	12,3,204,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3068
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CLC',0,3,209,10,3
	.word	23872
	.byte	12,3,212,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3211
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,3,217,10,3
	.word	23933
	.byte	12,3,220,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3328
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,3,225,10,3
	.word	24006
	.byte	12,3,228,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3463
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,3,233,10,3
	.word	24078
	.byte	12,3,236,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3598
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_EN',0,3,241,10,3
	.word	24150
	.byte	12,3,244,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3918
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,3,249,10,3
	.word	24218
	.byte	12,3,252,10,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4030
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,3,129,11,3
	.word	24288
	.byte	12,3,132,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4142
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,3,137,11,3
	.word	24358
	.byte	12,3,140,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4258
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,3,145,11,3
	.word	24430
	.byte	12,3,148,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4370
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,3,153,11,3
	.word	24500
	.byte	12,3,156,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4482
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CTRL',0,3,161,11,3
	.word	24570
	.byte	12,3,164,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4635
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,3,169,11,3
	.word	24632
	.byte	12,3,172,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5147
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,3,177,11,3
	.word	24702
	.byte	12,3,180,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5768
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,3,185,11,3
	.word	24772
	.byte	12,3,188,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6491
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CTRL',0,3,193,11,3
	.word	24845
	.byte	12,3,196,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6635
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_DTV_CH',0,3,201,11,3
	.word	24911
	.byte	12,3,204,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6784
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,3,209,11,3
	.word	24979
	.byte	12,3,212,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6999
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_EIRQ_EN',0,3,217,11,3
	.word	25048
	.byte	12,3,220,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7203
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_HW_CONF',0,3,225,11,3
	.word	25113
	.byte	12,3,228,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7560
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_0',0,3,233,11,3
	.word	25178
	.byte	12,3,236,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7688
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_2',0,3,241,11,3
	.word	25246
	.byte	12,3,244,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7967
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_6',0,3,249,11,3
	.word	25314
	.byte	12,3,252,11,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	8812
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,3,129,12,3
	.word	25382
	.byte	12,3,132,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9105
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,3,137,12,3
	.word	25453
	.byte	12,3,140,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9259
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,3,145,12,3
	.word	25523
	.byte	12,3,148,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9429
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,3,153,12,3
	.word	25600
	.byte	12,3,156,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9770
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,3,161,12,3
	.word	25675
	.byte	12,3,164,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9995
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_EN',0,3,169,12,3
	.word	25751
	.byte	12,3,172,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10193
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_FORCINT',0,3,177,12,3
	.word	25815
	.byte	12,3,180,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10389
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_MODE',0,3,185,12,3
	.word	25884
	.byte	12,3,188,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10492
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,3,193,12,3
	.word	25950
	.byte	12,3,196,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10670
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_KRST0',0,3,201,12,3
	.word	26018
	.byte	12,3,204,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10781
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_KRST1',0,3,209,12,3
	.word	26081
	.byte	12,3,212,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10873
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_KRSTCLR',0,3,217,12,3
	.word	26144
	.byte	12,3,220,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10969
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OCS',0,3,225,12,3
	.word	26209
	.byte	12,3,228,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11115
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ODA',0,3,233,12,3
	.word	26270
	.byte	12,3,236,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11221
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTBU0T',0,3,241,12,3
	.word	26331
	.byte	12,3,244,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11352
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTBU1T',0,3,249,12,3
	.word	26395
	.byte	12,3,252,12,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11483
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTBU2T',0,3,129,13,3
	.word	26459
	.byte	12,3,132,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11614
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTSC0',0,3,137,13,3
	.word	26523
	.byte	12,3,140,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11896
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTSS',0,3,145,13,3
	.word	26586
	.byte	12,3,148,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	12068
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_REV',0,3,153,13,3
	.word	26648
	.byte	12,3,156,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	12246
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_RST',0,3,161,13,3
	.word	26709
	.byte	12,3,164,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	12334
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,3,169,13,3
	.word	26770
	.byte	12,3,172,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	12442
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,3,177,13,3
	.word	26840
	.byte	12,3,180,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	12574
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,3,185,13,3
	.word	26910
	.byte	12,3,188,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	12682
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,3,193,13,3
	.word	26980
	.byte	12,3,196,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	12814
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,3,201,13,3
	.word	27050
	.byte	12,3,204,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	12922
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,3,209,13,3
	.word	27120
	.byte	12,3,212,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	13054
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CHEN',0,3,217,13,3
	.word	27190
	.byte	12,3,220,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	13200
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,3,225,13,3
	.word	27256
	.byte	12,3,228,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	13447
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CNT',0,3,233,13,3
	.word	27328
	.byte	12,3,236,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	13550
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,3,241,13,3
	.word	27396
	.byte	12,3,244,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	13649
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,3,249,13,3
	.word	27465
	.byte	12,3,252,13,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14197
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,3,129,14,3
	.word	27534
	.byte	12,3,132,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14303
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,3,137,14,3
	.word	27603
	.byte	12,3,140,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14417
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,3,145,14,3
	.word	27673
	.byte	12,3,148,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14672
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,3,153,14,3
	.word	27745
	.byte	12,3,156,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14784
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,3,161,14,3
	.word	27816
	.byte	12,3,164,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14896
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,3,169,14,3
	.word	27887
	.byte	12,3,172,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14995
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,3,177,14,3
	.word	27956
	.byte	12,3,180,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15094
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,3,185,14,3
	.word	28025
	.byte	12,3,188,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15341
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,3,193,14,3
	.word	28096
	.byte	12,3,196,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15580
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,3,201,14,3
	.word	28172
	.byte	12,3,204,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15697
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,3,209,14,3
	.word	28245
	.byte	12,3,212,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15910
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,3,217,14,3
	.word	28320
	.byte	12,3,220,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16017
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,3,225,14,3
	.word	28389
	.byte	12,3,228,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16159
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_IN_SRC',0,3,233,14,3
	.word	28458
	.byte	12,3,236,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16504
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_INP_VAL',0,3,241,14,3
	.word	28526
	.byte	12,3,244,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16645
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_RST',0,3,249,14,3
	.word	28595
	.byte	12,3,252,14,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16878
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CM0',0,3,129,15,3
	.word	28660
	.byte	12,3,132,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16981
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CM1',0,3,137,15,3
	.word	28728
	.byte	12,3,140,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17084
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CN0',0,3,145,15,3
	.word	28796
	.byte	12,3,148,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17187
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,3,153,15,3
	.word	28864
	.byte	12,3,156,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17515
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,3,161,15,3
	.word	28933
	.byte	12,3,164,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17658
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,3,169,15,3
	.word	29004
	.byte	12,3,172,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17807
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,3,177,15,3
	.word	29080
	.byte	12,3,180,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17924
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,3,185,15,3
	.word	29153
	.byte	12,3,188,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18061
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_SR0',0,3,193,15,3
	.word	29228
	.byte	12,3,196,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18164
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_SR1',0,3,201,15,3
	.word	29296
	.byte	12,3,204,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18267
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_STAT',0,3,209,15,3
	.word	29364
	.byte	12,3,212,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18370
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,3,217,15,3
	.word	29433
	.byte	12,3,220,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18524
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,3,225,15,3
	.word	29506
	.byte	12,3,228,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18814
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,3,233,15,3
	.word	29583
	.byte	12,3,236,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19104
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,3,241,15,3
	.word	29660
	.byte	12,3,244,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19537
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,3,249,15,3
	.word	29736
	.byte	12,3,252,15,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19987
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,3,129,16,3
	.word	29811
	.byte	12,3,132,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20257
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,3,137,16,3
	.word	29886
	.byte	12,3,140,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20547
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,3,145,16,3
	.word	29963
	.byte	12,3,148,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20837
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,3,153,16,3
	.word	30040
	.byte	12,3,156,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20991
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,3,161,16,3
	.word	30113
	.byte	12,3,164,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21281
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,3,169,16,3
	.word	30190
	.byte	12,3,172,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21571
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,3,177,16,3
	.word	30267
	.byte	12,3,180,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22004
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,3,185,16,3
	.word	30343
	.byte	12,3,188,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22454
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,3,193,16,3
	.word	30418
	.byte	12,3,196,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22724
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,3,201,16,3
	.word	30493
	.byte	12,3,204,16,9,4,13
	.byte	'U',0,4
	.word	23310
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	23337
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23014
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,3,209,16,3
	.word	30570
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,3,220,16,25,4,13
	.byte	'CTRL',0,4
	.word	23933
	.byte	2,35,0,0,14
	.word	30647
	.byte	8
	.byte	'Ifx_GTM_CMU_CLK0_5',0,3,223,16,3
	.word	30688
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_6',0,3,226,16,25,4,13
	.byte	'CTRL',0,4
	.word	24006
	.byte	2,35,0,0,14
	.word	30721
	.byte	8
	.byte	'Ifx_GTM_CMU_CLK_6',0,3,229,16,3
	.word	30761
	.byte	10
	.byte	'_Ifx_GTM_CMU_CLK_7',0,3,232,16,25,4,13
	.byte	'CTRL',0,4
	.word	24078
	.byte	2,35,0,0,14
	.word	30793
	.byte	8
	.byte	'Ifx_GTM_CMU_CLK_7',0,3,235,16,3
	.word	30833
	.byte	10
	.byte	'_Ifx_GTM_CMU_ECLK',0,3,238,16,25,8,13
	.byte	'NUM',0,4
	.word	24288
	.byte	2,35,0,13
	.byte	'DEN',0,4
	.word	24218
	.byte	2,35,4,0,14
	.word	30865
	.byte	8
	.byte	'Ifx_GTM_CMU_ECLK',0,3,242,16,3
	.word	30916
	.byte	10
	.byte	'_Ifx_GTM_CMU_FXCLK',0,3,245,16,25,4,13
	.byte	'CTRL',0,4
	.word	24358
	.byte	2,35,0,0,14
	.word	30947
	.byte	8
	.byte	'Ifx_GTM_CMU_FXCLK',0,3,248,16,3
	.word	30987
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,3,251,16,25,4,13
	.byte	'OUTSEL',0,4
	.word	25523
	.byte	2,35,0,0,14
	.word	31019
	.byte	8
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,3,254,16,3
	.word	31064
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_T',0,3,129,17,25,32,15,32
	.word	25600
	.byte	16,7,0,13
	.byte	'OUTSEL',0,32
	.word	31125
	.byte	2,35,0,0,14
	.word	31099
	.byte	8
	.byte	'Ifx_GTM_INOUTSEL_T',0,3,132,17,3
	.word	31151
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,3,135,17,25,32,13
	.byte	'INSEL',0,4
	.word	25675
	.byte	2,35,0,15,28
	.word	201
	.byte	16,27,0,13
	.byte	'reserved_4',0,28
	.word	31227
	.byte	2,35,4,0,14
	.word	31184
	.byte	8
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,3,139,17,3
	.word	31257
	.byte	10
	.byte	'_Ifx_GTM_TIM_CH',0,3,142,17,25,116,13
	.byte	'GPR0',0,4
	.word	27887
	.byte	2,35,0,13
	.byte	'GPR1',0,4
	.word	27956
	.byte	2,35,4,13
	.byte	'CNT',0,4
	.word	27328
	.byte	2,35,8,13
	.byte	'ECNT',0,4
	.word	27534
	.byte	2,35,12,13
	.byte	'CNTS',0,4
	.word	27396
	.byte	2,35,16,13
	.byte	'TDUC',0,4
	.word	28320
	.byte	2,35,20,13
	.byte	'TDUV',0,4
	.word	28389
	.byte	2,35,24,13
	.byte	'FLT_RE',0,4
	.word	27816
	.byte	2,35,28,13
	.byte	'FLT_FE',0,4
	.word	27745
	.byte	2,35,32,13
	.byte	'CTRL',0,4
	.word	27465
	.byte	2,35,36,13
	.byte	'ECTRL',0,4
	.word	27603
	.byte	2,35,40,13
	.byte	'IRQ_NOTIFY',0,4
	.word	28245
	.byte	2,35,44,13
	.byte	'IRQ_EN',0,4
	.word	28025
	.byte	2,35,48,13
	.byte	'IRQ_FORCINT',0,4
	.word	28096
	.byte	2,35,52,13
	.byte	'IRQ_MODE',0,4
	.word	28172
	.byte	2,35,56,13
	.byte	'EIRQ_EN',0,4
	.word	27673
	.byte	2,35,60,15,52
	.word	201
	.byte	16,51,0,13
	.byte	'reserved_40',0,52
	.word	31564
	.byte	2,35,64,0,14
	.word	31292
	.byte	8
	.byte	'Ifx_GTM_TIM_CH',0,3,161,17,3
	.word	31595
	.byte	10
	.byte	'_Ifx_GTM_TOM_CH',0,3,164,17,25,48,13
	.byte	'CTRL',0,4
	.word	28864
	.byte	2,35,0,13
	.byte	'SR0',0,4
	.word	29228
	.byte	2,35,4,13
	.byte	'SR1',0,4
	.word	29296
	.byte	2,35,8,13
	.byte	'CM0',0,4
	.word	28660
	.byte	2,35,12,13
	.byte	'CM1',0,4
	.word	28728
	.byte	2,35,16,13
	.byte	'CN0',0,4
	.word	28796
	.byte	2,35,20,13
	.byte	'STAT',0,4
	.word	29364
	.byte	2,35,24,13
	.byte	'IRQ_NOTIFY',0,4
	.word	29153
	.byte	2,35,28,13
	.byte	'IRQ_EN',0,4
	.word	28933
	.byte	2,35,32,13
	.byte	'IRQ_FORCINT',0,4
	.word	29004
	.byte	2,35,36,13
	.byte	'IRQ_MODE',0,4
	.word	29080
	.byte	2,35,40,15,4
	.word	201
	.byte	16,3,0,13
	.byte	'reserved_2C',0,4
	.word	31814
	.byte	2,35,44,0,14
	.word	31624
	.byte	8
	.byte	'Ifx_GTM_TOM_CH',0,3,178,17,3
	.word	31845
	.byte	10
	.byte	'_Ifx_GTM_BRIDGE',0,3,191,17,25,12,13
	.byte	'MODE',0,4
	.word	23665
	.byte	2,35,0,13
	.byte	'PTR1',0,4
	.word	23734
	.byte	2,35,4,13
	.byte	'PTR2',0,4
	.word	23803
	.byte	2,35,8,0,14
	.word	31874
	.byte	8
	.byte	'Ifx_GTM_BRIDGE',0,3,196,17,3
	.word	31939
	.byte	10
	.byte	'_Ifx_GTM_CMU',0,3,199,17,25,72,13
	.byte	'CLK_EN',0,4
	.word	24150
	.byte	2,35,0,13
	.byte	'GCLK_NUM',0,4
	.word	24500
	.byte	2,35,4,13
	.byte	'GCLK_DEN',0,4
	.word	24430
	.byte	2,35,8,15,24
	.word	30647
	.byte	16,5,0,14
	.word	32039
	.byte	13
	.byte	'CLK0_5',0,24
	.word	32048
	.byte	2,35,12,14
	.word	30721
	.byte	13
	.byte	'CLK_6',0,4
	.word	32069
	.byte	2,35,36,14
	.word	30793
	.byte	13
	.byte	'CLK_7',0,4
	.word	32089
	.byte	2,35,40,15,24
	.word	30865
	.byte	16,2,0,14
	.word	32109
	.byte	13
	.byte	'ECLK',0,24
	.word	32118
	.byte	2,35,44,14
	.word	30947
	.byte	13
	.byte	'FXCLK',0,4
	.word	32137
	.byte	2,35,68,0,14
	.word	31968
	.byte	8
	.byte	'Ifx_GTM_CMU',0,3,209,17,3
	.word	32158
	.byte	10
	.byte	'_Ifx_GTM_DTM',0,3,212,17,25,36,13
	.byte	'CTRL',0,4
	.word	24845
	.byte	2,35,0,13
	.byte	'CH_CTRL1',0,4
	.word	24632
	.byte	2,35,4,13
	.byte	'CH_CTRL2',0,4
	.word	24702
	.byte	2,35,8,13
	.byte	'CH_CTRL2_SR',0,4
	.word	24772
	.byte	2,35,12,13
	.byte	'PS_CTRL',0,4
	.word	24979
	.byte	2,35,16,15,16
	.word	24911
	.byte	16,3,0,13
	.byte	'DTV_CH',0,16
	.word	32291
	.byte	2,35,20,0,14
	.word	32184
	.byte	8
	.byte	'Ifx_GTM_DTM',0,3,220,17,3
	.word	32317
	.byte	10
	.byte	'_Ifx_GTM_ICM',0,3,223,17,25,60,13
	.byte	'IRQG_0',0,4
	.word	25178
	.byte	2,35,0,13
	.byte	'reserved_4',0,4
	.word	31814
	.byte	2,35,4,13
	.byte	'IRQG_2',0,4
	.word	25246
	.byte	2,35,8,15,12
	.word	201
	.byte	16,11,0,13
	.byte	'reserved_C',0,12
	.word	32414
	.byte	2,35,12,13
	.byte	'IRQG_6',0,4
	.word	25314
	.byte	2,35,24,15,20
	.word	201
	.byte	16,19,0,13
	.byte	'reserved_1C',0,20
	.word	32459
	.byte	2,35,28,13
	.byte	'IRQG_MEI',0,4
	.word	25453
	.byte	2,35,48,13
	.byte	'reserved_34',0,4
	.word	31814
	.byte	2,35,52,13
	.byte	'IRQG_CEI1',0,4
	.word	25382
	.byte	2,35,56,0,14
	.word	32343
	.byte	8
	.byte	'Ifx_GTM_ICM',0,3,234,17,3
	.word	32548
	.byte	10
	.byte	'_Ifx_GTM_INOUTSEL',0,3,237,17,25,148,1,15,32
	.word	31184
	.byte	16,0,0,14
	.word	32599
	.byte	13
	.byte	'TIM',0,32
	.word	32608
	.byte	2,35,0,14
	.word	31099
	.byte	13
	.byte	'T',0,32
	.word	32626
	.byte	2,35,32,15,80
	.word	201
	.byte	16,79,0,13
	.byte	'reserved_40',0,80
	.word	32642
	.byte	2,35,64,14
	.word	31019
	.byte	13
	.byte	'CAN',0,4
	.word	32672
	.byte	3,35,144,1,0,14
	.word	32574
	.byte	8
	.byte	'Ifx_GTM_INOUTSEL',0,3,243,17,3
	.word	32692
	.byte	10
	.byte	'_Ifx_GTM_TBU',0,3,246,17,25,28,13
	.byte	'CHEN',0,4
	.word	27190
	.byte	2,35,0,13
	.byte	'CH0_CTRL',0,4
	.word	26840
	.byte	2,35,4,13
	.byte	'CH0_BASE',0,4
	.word	26770
	.byte	2,35,8,13
	.byte	'CH1_CTRL',0,4
	.word	26980
	.byte	2,35,12,13
	.byte	'CH1_BASE',0,4
	.word	26910
	.byte	2,35,16,13
	.byte	'CH2_CTRL',0,4
	.word	27120
	.byte	2,35,20,13
	.byte	'CH2_BASE',0,4
	.word	27050
	.byte	2,35,24,0,14
	.word	32723
	.byte	8
	.byte	'Ifx_GTM_TBU',0,3,255,17,3
	.word	32865
	.byte	10
	.byte	'_Ifx_GTM_TIM',0,3,130,18,25,128,8,14
	.word	31292
	.byte	13
	.byte	'CH0',0,116
	.word	32911
	.byte	2,35,0,13
	.byte	'INP_VAL',0,4
	.word	28526
	.byte	2,35,116,13
	.byte	'IN_SRC',0,4
	.word	28458
	.byte	2,35,120,13
	.byte	'RST',0,4
	.word	28595
	.byte	2,35,124,14
	.word	31292
	.byte	13
	.byte	'CH1',0,116
	.word	32975
	.byte	3,35,128,1,13
	.byte	'reserved_F4',0,12
	.word	32414
	.byte	3,35,244,1,14
	.word	31292
	.byte	13
	.byte	'CH2',0,116
	.word	33016
	.byte	3,35,128,2,13
	.byte	'reserved_174',0,12
	.word	32414
	.byte	3,35,244,2,14
	.word	31292
	.byte	13
	.byte	'CH3',0,116
	.word	33058
	.byte	3,35,128,3,13
	.byte	'reserved_1F4',0,12
	.word	32414
	.byte	3,35,244,3,14
	.word	31292
	.byte	13
	.byte	'CH4',0,116
	.word	33100
	.byte	3,35,128,4,13
	.byte	'reserved_274',0,12
	.word	32414
	.byte	3,35,244,4,14
	.word	31292
	.byte	13
	.byte	'CH5',0,116
	.word	33142
	.byte	3,35,128,5,13
	.byte	'reserved_2F4',0,12
	.word	32414
	.byte	3,35,244,5,14
	.word	31292
	.byte	13
	.byte	'CH6',0,116
	.word	33184
	.byte	3,35,128,6,13
	.byte	'reserved_374',0,12
	.word	32414
	.byte	3,35,244,6,14
	.word	31292
	.byte	13
	.byte	'CH7',0,116
	.word	33226
	.byte	3,35,128,7,13
	.byte	'reserved_3F4',0,12
	.word	32414
	.byte	3,35,244,7,0,14
	.word	32891
	.byte	8
	.byte	'Ifx_GTM_TIM',0,3,150,18,3
	.word	33269
	.byte	10
	.byte	'_Ifx_GTM_TOM',0,3,153,18,25,128,16,14
	.word	31624
	.byte	13
	.byte	'CH0',0,48
	.word	33315
	.byte	2,35,0,13
	.byte	'TGC0_GLB_CTRL',0,4
	.word	29736
	.byte	2,35,48,13
	.byte	'TGC0_ACT_TB',0,4
	.word	29433
	.byte	2,35,52,13
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	29660
	.byte	2,35,56,13
	.byte	'TGC0_INT_TRIG',0,4
	.word	29811
	.byte	2,35,60,14
	.word	31624
	.byte	13
	.byte	'CH1',0,48
	.word	33424
	.byte	2,35,64,13
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	29506
	.byte	2,35,112,13
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	29583
	.byte	2,35,116,13
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	29886
	.byte	2,35,120,13
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	29963
	.byte	2,35,124,14
	.word	31624
	.byte	13
	.byte	'CH2',0,48
	.word	33542
	.byte	3,35,128,1,15,16
	.word	201
	.byte	16,15,0,13
	.byte	'reserved_B0',0,16
	.word	33561
	.byte	3,35,176,1,14
	.word	31624
	.byte	13
	.byte	'CH3',0,48
	.word	33592
	.byte	3,35,192,1,13
	.byte	'reserved_F0',0,16
	.word	33561
	.byte	3,35,240,1,14
	.word	31624
	.byte	13
	.byte	'CH4',0,48
	.word	33633
	.byte	3,35,128,2,13
	.byte	'reserved_130',0,16
	.word	33561
	.byte	3,35,176,2,14
	.word	31624
	.byte	13
	.byte	'CH5',0,48
	.word	33675
	.byte	3,35,192,2,13
	.byte	'reserved_170',0,16
	.word	33561
	.byte	3,35,240,2,14
	.word	31624
	.byte	13
	.byte	'CH6',0,48
	.word	33717
	.byte	3,35,128,3,13
	.byte	'reserved_1B0',0,16
	.word	33561
	.byte	3,35,176,3,14
	.word	31624
	.byte	13
	.byte	'CH7',0,48
	.word	33759
	.byte	3,35,192,3,13
	.byte	'reserved_1F0',0,16
	.word	33561
	.byte	3,35,240,3,14
	.word	31624
	.byte	13
	.byte	'CH8',0,48
	.word	33801
	.byte	3,35,128,4,13
	.byte	'TGC1_GLB_CTRL',0,4
	.word	30343
	.byte	3,35,176,4,13
	.byte	'TGC1_ACT_TB',0,4
	.word	30040
	.byte	3,35,180,4,13
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	30267
	.byte	3,35,184,4,13
	.byte	'TGC1_INT_TRIG',0,4
	.word	30418
	.byte	3,35,188,4,14
	.word	31624
	.byte	13
	.byte	'CH9',0,48
	.word	33915
	.byte	3,35,192,4,13
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	30113
	.byte	3,35,240,4,13
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	30190
	.byte	3,35,244,4,13
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	30493
	.byte	3,35,248,4,13
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	30570
	.byte	3,35,252,4,14
	.word	31624
	.byte	13
	.byte	'CH10',0,48
	.word	34038
	.byte	3,35,128,5,13
	.byte	'reserved_2B0',0,16
	.word	33561
	.byte	3,35,176,5,14
	.word	31624
	.byte	13
	.byte	'CH11',0,48
	.word	34081
	.byte	3,35,192,5,13
	.byte	'reserved_2F0',0,16
	.word	33561
	.byte	3,35,240,5,14
	.word	31624
	.byte	13
	.byte	'CH12',0,48
	.word	34124
	.byte	3,35,128,6,13
	.byte	'reserved_330',0,16
	.word	33561
	.byte	3,35,176,6,14
	.word	31624
	.byte	13
	.byte	'CH13',0,48
	.word	34167
	.byte	3,35,192,6,13
	.byte	'reserved_370',0,16
	.word	33561
	.byte	3,35,240,6,14
	.word	31624
	.byte	13
	.byte	'CH14',0,48
	.word	34210
	.byte	3,35,128,7,13
	.byte	'reserved_3B0',0,16
	.word	33561
	.byte	3,35,176,7,14
	.word	31624
	.byte	13
	.byte	'CH15',0,48
	.word	34253
	.byte	3,35,192,7,15,144,8
	.word	201
	.byte	16,143,8,0,13
	.byte	'reserved_3F0',0,144,8
	.word	34273
	.byte	3,35,240,7,0,14
	.word	33295
	.byte	8
	.byte	'Ifx_GTM_TOM',0,3,199,18,3
	.word	34309
	.byte	17,4,130,4,20,64,13
	.byte	'CTRL',0,4
	.word	28864
	.byte	2,35,0,13
	.byte	'SR0',0,4
	.word	29228
	.byte	2,35,4,13
	.byte	'SR1',0,4
	.word	29296
	.byte	2,35,8,13
	.byte	'CM0',0,4
	.word	28660
	.byte	2,35,12,13
	.byte	'CM1',0,4
	.word	28728
	.byte	2,35,16,13
	.byte	'CN0',0,4
	.word	28796
	.byte	2,35,20,13
	.byte	'STAT',0,4
	.word	29364
	.byte	2,35,24,13
	.byte	'IRQ_NOTIFY',0,4
	.word	29153
	.byte	2,35,28,13
	.byte	'IRQ_EN',0,4
	.word	28933
	.byte	2,35,32,13
	.byte	'IRQ_FORCINT',0,4
	.word	29004
	.byte	2,35,36,13
	.byte	'IRQ_MODE',0,4
	.word	29080
	.byte	2,35,40,15,20
	.word	201
	.byte	16,19,0,13
	.byte	'reserved_2C',0,20
	.word	34509
	.byte	2,35,44,0,14
	.word	34335
	.byte	8
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,4,155,4,4
	.word	34540
	.byte	17,4,157,4,20,128,4,13
	.byte	'GLB_CTRL',0,4
	.word	29736
	.byte	2,35,0,13
	.byte	'ACT_TB',0,4
	.word	29433
	.byte	2,35,4,13
	.byte	'FUPD_CTRL',0,4
	.word	29660
	.byte	2,35,8,13
	.byte	'INT_TRIG',0,4
	.word	29811
	.byte	2,35,12,15,48
	.word	201
	.byte	16,47,0,13
	.byte	'reserved_tgc0',0,48
	.word	34652
	.byte	2,35,16,13
	.byte	'ENDIS_CTRL',0,4
	.word	29506
	.byte	2,35,64,13
	.byte	'ENDIS_STAT',0,4
	.word	29583
	.byte	2,35,68,13
	.byte	'OUTEN_CTRL',0,4
	.word	29886
	.byte	2,35,72,13
	.byte	'OUTEN_STAT',0,4
	.word	29963
	.byte	2,35,76,15,176,3
	.word	201
	.byte	16,175,3,0,13
	.byte	'reserved_tgc1',0,176,3
	.word	34764
	.byte	2,35,80,0,14
	.word	34574
	.byte	8
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,4,177,4,5
	.word	34800
	.byte	17,4,179,4,20,128,16,13
	.byte	'reserved_tom0',0,48
	.word	34652
	.byte	2,35,0,15,128,8
	.word	34574
	.byte	16,1,0,14
	.word	34865
	.byte	13
	.byte	'TGC',0,128,8
	.word	34875
	.byte	2,35,48,15,208,7
	.word	201
	.byte	16,207,7,0,13
	.byte	'reserved_tgc2',0,208,7
	.word	34894
	.byte	3,35,176,8,0,14
	.word	34835
	.byte	8
	.byte	'Ifx_GTM_TOM_TGCx',0,4,184,4,5
	.word	34931
	.byte	17,4,187,4,20,128,16,15,128,8
	.word	34335
	.byte	16,15,0,14
	.word	34969
	.byte	13
	.byte	'CH',0,128,8
	.word	34979
	.byte	2,35,0,15,128,8
	.word	201
	.byte	16,255,7,0,13
	.byte	'reserved_tom1',0,128,8
	.word	34997
	.byte	3,35,128,8,0,14
	.word	34962
	.byte	8
	.byte	'Ifx_GTM_TOM_CHx',0,4,191,4,5
	.word	35034
	.byte	17,4,212,4,20,128,1,13
	.byte	'CH_GPR0',0,4
	.word	27887
	.byte	2,35,0,13
	.byte	'CH_GPR1',0,4
	.word	27956
	.byte	2,35,4,13
	.byte	'CH_CNT',0,4
	.word	27328
	.byte	2,35,8,13
	.byte	'CH_ECNT',0,4
	.word	27534
	.byte	2,35,12,13
	.byte	'CH_CNTS',0,4
	.word	27396
	.byte	2,35,16,13
	.byte	'CH_TDUC',0,4
	.word	28320
	.byte	2,35,20,13
	.byte	'CH_TDUV',0,4
	.word	28389
	.byte	2,35,24,13
	.byte	'CH_FLT_RE',0,4
	.word	27816
	.byte	2,35,28,13
	.byte	'CH_FLT_FE',0,4
	.word	27745
	.byte	2,35,32,13
	.byte	'CH_CTRL',0,4
	.word	27465
	.byte	2,35,36,13
	.byte	'CH_ECTRL',0,4
	.word	27603
	.byte	2,35,40,13
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	28245
	.byte	2,35,44,13
	.byte	'CH_IRQ_EN',0,4
	.word	28025
	.byte	2,35,48,13
	.byte	'CH_IRQ_FORCINT',0,4
	.word	28096
	.byte	2,35,52,13
	.byte	'CH_IRQ_MODE',0,4
	.word	28172
	.byte	2,35,56,13
	.byte	'CH_EIRQ_EN',0,4
	.word	27673
	.byte	2,35,60,15,64
	.word	201
	.byte	16,63,0,13
	.byte	'reserved_40',0,64
	.word	35369
	.byte	2,35,64,0,14
	.word	35064
	.byte	8
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,4,248,4,4
	.word	35400
	.byte	17,4,250,4,20,8,13
	.byte	'IN_SRC',0,4
	.word	28458
	.byte	2,35,0,13
	.byte	'RST',0,4
	.word	28595
	.byte	2,35,4,0,14
	.word	35434
	.byte	8
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,4,255,4,4
	.word	35470
	.byte	17,4,129,5,21,128,16,15,128,8
	.word	35064
	.byte	16,7,0,14
	.word	35521
	.byte	13
	.byte	'CH',0,128,8
	.word	35531
	.byte	2,35,0,13
	.byte	'reserved_tim1',0,128,8
	.word	34997
	.byte	3,35,128,8,0,14
	.word	35514
	.byte	8
	.byte	'Ifx_GTM_TIM_CHx',0,4,133,5,4
	.word	35575
	.byte	17,4,135,5,20,128,16,15,120
	.word	201
	.byte	16,119,0,13
	.byte	'reserved_tim2',0,120
	.word	35612
	.byte	2,35,0,14
	.word	35434
	.byte	13
	.byte	'IN_SRC_RESET',0,8
	.word	35644
	.byte	2,35,120,15,128,15
	.word	201
	.byte	16,255,14,0,13
	.byte	'reserved_tim3',0,128,15
	.word	35671
	.byte	3,35,128,1,0,14
	.word	35605
	.byte	8
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,4,140,5,4
	.word	35708
	.byte	18,4,174,5,11,1,19
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,19
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,19
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,19
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,19
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,19
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,19
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,19
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,8
	.byte	'Gtm_ConfigurableClockType',0,4,184,5,4
	.word	35746
	.byte	18,4,188,5,11,1,19
	.byte	'GTM_LOW',0,0,19
	.byte	'GTM_HIGH',0,1,0,8
	.byte	'Gtm_OutputLevelType',0,4,192,5,4
	.word	35980
	.byte	18,4,195,5,11,1,19
	.byte	'TOM_GLB_CTRL',0,0,19
	.byte	'TOM_ACT_TB',0,1,19
	.byte	'TOM_FUPD_CTRL',0,2,19
	.byte	'TOM_INT_TRIG',0,3,19
	.byte	'TOM_RESERVED_0',0,4,19
	.byte	'TOM_RESERVED_1',0,5,19
	.byte	'TOM_RESERVED_2',0,6,19
	.byte	'TOM_RESERVED_3',0,7,19
	.byte	'TOM_RESERVED_4',0,8,19
	.byte	'TOM_RESERVED_5',0,9,19
	.byte	'TOM_RESERVED_6',0,10,19
	.byte	'TOM_RESERVED_7',0,11,19
	.byte	'TOM_RESERVED_8',0,12,19
	.byte	'TOM_RESERVED_9',0,13,19
	.byte	'TOM_RESERVED_10',0,14,19
	.byte	'TOM_RESERVED_11',0,15,19
	.byte	'TOM_ENDIS_CTRL',0,16,19
	.byte	'TOM_ENDIS_STAT',0,17,19
	.byte	'TOM_OUTEN_CTRL',0,18,19
	.byte	'TOM_OUTEN_STAT',0,19,0,8
	.byte	'Gtm_TomTimerRegistersType',0,4,217,5,4
	.word	36037
	.byte	17,4,221,5,11,8,13
	.byte	'FltRisingEdge',0,4
	.word	1308
	.byte	2,35,0,13
	.byte	'FltFallingEdge',0,4
	.word	1308
	.byte	2,35,4,0,8
	.byte	'Gtm_TimFilterType',0,4,225,5,4
	.word	36412
	.byte	8
	.byte	'Gtm_TbuChCtrlType',0,4,230,5,32
	.word	26840
	.byte	8
	.byte	'Gtm_TbuChBaseType',0,4,231,5,32
	.word	26770
	.byte	17,4,233,5,11,8,13
	.byte	'CH_CTRL',0,4
	.word	26840
	.byte	2,35,0,13
	.byte	'CH_BASE',0,4
	.word	26770
	.byte	2,35,4,0,8
	.byte	'Gtm_TbuChType',0,4,237,5,4
	.word	36547
	.byte	17,4,249,5,9,36,15,4
	.word	1308
	.byte	16,0,0,13
	.byte	'TimInSel',0,4
	.word	36617
	.byte	2,35,0,15,32
	.word	1308
	.byte	16,7,0,13
	.byte	'ToutSel',0,32
	.word	36644
	.byte	2,35,4,0,8
	.byte	'Gtm_PortConfigType',0,4,253,5,2
	.word	36611
	.byte	17,4,129,6,9,8,13
	.byte	'TimRisingEdgeFilter',0,4
	.word	1308
	.byte	2,35,0,13
	.byte	'TimFallingEdgeFilter',0,4
	.word	1308
	.byte	2,35,4,0,8
	.byte	'Gtm_TimFltType',0,4,134,6,2
	.word	36699
	.byte	17,4,138,6,11,24,13
	.byte	'TimUsage',0,1
	.word	201
	.byte	2,35,0,13
	.byte	'TimIrqEn',0,1
	.word	201
	.byte	2,35,1,13
	.byte	'TimErrIrqEn',0,1
	.word	201
	.byte	2,35,2,13
	.byte	'TimExtCapSrc',0,1
	.word	201
	.byte	2,35,3,13
	.byte	'TimCtrlValue',0,4
	.word	1308
	.byte	2,35,4,20
	.word	36699
	.byte	7
	.word	36896
	.byte	13
	.byte	'GtmTimFltPtr',0,4
	.word	36901
	.byte	2,35,8,13
	.byte	'TimCntsValue',0,4
	.word	1308
	.byte	2,35,12,13
	.byte	'TimTduValue',0,4
	.word	1308
	.byte	2,35,16,13
	.byte	'TimInSrcSel',0,4
	.word	1308
	.byte	2,35,20,0,8
	.byte	'Gtm_TimConfigType',0,4,151,6,4
	.word	36789
	.byte	17,4,154,6,11,40,15,8
	.word	201
	.byte	16,7,0,13
	.byte	'Gtm_TimUsage',0,8
	.word	37026
	.byte	2,35,0,15,16
	.word	201
	.byte	16,15,0,15,32
	.word	37057
	.byte	16,1,0,13
	.byte	'Gtm_TomUsage',0,32
	.word	37066
	.byte	2,35,8,0,8
	.byte	'Gtm_ModUsageConfigType',0,4,163,6,4
	.word	37020
	.byte	17,4,177,6,9,16,13
	.byte	'GtmTomUpdateEn',0,2
	.word	272
	.byte	2,35,0,13
	.byte	'GtmTomEndisCtrl',0,2
	.word	272
	.byte	2,35,2,13
	.byte	'GtmTomEndisStat',0,2
	.word	272
	.byte	2,35,4,13
	.byte	'GtmTomOutenCtrl',0,2
	.word	272
	.byte	2,35,6,13
	.byte	'GtmTomOutenStat',0,2
	.word	272
	.byte	2,35,8,13
	.byte	'GtmTomFupd',0,4
	.word	1308
	.byte	2,35,10,0,8
	.byte	'Gtm_TomTgcConfigGroupType',0,4,185,6,2
	.word	37130
	.byte	17,4,189,6,9,12,13
	.byte	'GtmTomIntTrig',0,2
	.word	272
	.byte	2,35,0,13
	.byte	'GtmTomActTb',0,4
	.word	1308
	.byte	2,35,2,20
	.word	37130
	.byte	7
	.word	37366
	.byte	13
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	37371
	.byte	2,35,8,0,8
	.byte	'Gtm_TomTgcConfigType',0,4,196,6,2
	.word	37316
	.byte	17,4,199,6,9,12,13
	.byte	'GtmTomIrqEn',0,1
	.word	201
	.byte	2,35,0,13
	.byte	'GtmTomCn0Value',0,2
	.word	272
	.byte	2,35,2,13
	.byte	'GtmTomCm0Value',0,2
	.word	272
	.byte	2,35,4,13
	.byte	'GtmTomCm1Value',0,2
	.word	272
	.byte	2,35,6,13
	.byte	'GtmTomSr0Value',0,2
	.word	272
	.byte	2,35,8,13
	.byte	'GtmTomSr1Value',0,2
	.word	272
	.byte	2,35,10,0,8
	.byte	'Gtm_TomChannelConfigType',0,4,207,6,2
	.word	37438
	.byte	17,4,211,6,9,12,13
	.byte	'TomUsage',0,1
	.word	201
	.byte	2,35,0,13
	.byte	'GtmTomIrqMode',0,1
	.word	201
	.byte	2,35,1,13
	.byte	'GtmTomControlWord',0,4
	.word	1308
	.byte	2,35,2,20
	.word	37438
	.byte	7
	.word	37694
	.byte	13
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	37699
	.byte	2,35,8,0,8
	.byte	'Gtm_TomConfigType',0,4,219,6,2
	.word	37620
	.byte	17,4,223,6,9,8,13
	.byte	'CmuEclkNum',0,4
	.word	1308
	.byte	2,35,0,13
	.byte	'CmuEclkDen',0,4
	.word	1308
	.byte	2,35,4,0,8
	.byte	'Gtm_ExtClkType',0,4,227,6,2
	.word	37761
	.byte	17,4,230,6,9,64,13
	.byte	'GtmClockEnable',0,4
	.word	1308
	.byte	2,35,0,13
	.byte	'GtmCmuClkCnt',0,32
	.word	36644
	.byte	2,35,4,13
	.byte	'GtmFxdClkControl',0,4
	.word	1308
	.byte	2,35,36,15,24
	.word	37761
	.byte	16,2,0,13
	.byte	'GtmEclk',0,24
	.word	37910
	.byte	2,35,40,0,8
	.byte	'Gtm_ClockSettingType',0,4,236,6,2
	.word	37832
	.byte	17,4,240,6,9,4,13
	.byte	'GtmCtrlValue',0,2
	.word	272
	.byte	2,35,0,13
	.byte	'GtmIrqEnable',0,2
	.word	272
	.byte	2,35,2,0,8
	.byte	'Gtm_GeneralConfigType',0,4,245,6,2
	.word	37967
	.byte	17,4,249,6,9,6,13
	.byte	'TbuChannelCtrl',0,1
	.word	201
	.byte	2,35,0,13
	.byte	'TbuBaseValue',0,4
	.word	1308
	.byte	2,35,2,0,8
	.byte	'Gtm_TbuConfigType',0,4,253,6,2
	.word	38049
	.byte	17,4,129,7,9,72,13
	.byte	'GtmModuleSleepEnable',0,1
	.word	201
	.byte	2,35,0,13
	.byte	'GtmGclkNum',0,4
	.word	1308
	.byte	2,35,2,13
	.byte	'GtmGclkDen',0,4
	.word	1308
	.byte	2,35,6,13
	.byte	'GtmAccessEnable0',0,4
	.word	1308
	.byte	2,35,10,13
	.byte	'GtmAccessEnable1',0,4
	.word	1308
	.byte	2,35,14,15,2
	.word	272
	.byte	16,0,0,13
	.byte	'GtmTimModuleUsage',0,2
	.word	38257
	.byte	2,35,18,15,1
	.word	201
	.byte	16,0,0,13
	.byte	'GtmTimUsage',0,1
	.word	38293
	.byte	2,35,20,20
	.word	36789
	.byte	7
	.word	38323
	.byte	13
	.byte	'GtmTimConfigPtr',0,4
	.word	38328
	.byte	2,35,24,13
	.byte	'GtmTomTgcUsage',0,1
	.word	38293
	.byte	2,35,28,20
	.word	37316
	.byte	7
	.word	38382
	.byte	13
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	38387
	.byte	2,35,32,15,8
	.word	1308
	.byte	16,1,0,13
	.byte	'GtmTomModuleUsage',0,8
	.word	38420
	.byte	2,35,36,13
	.byte	'GtmTomUsage',0,4
	.word	36617
	.byte	2,35,44,20
	.word	37620
	.byte	7
	.word	38477
	.byte	13
	.byte	'GtmTomConfigPtr',0,4
	.word	38482
	.byte	2,35,48,20
	.word	37020
	.byte	7
	.word	38512
	.byte	13
	.byte	'GtmModUsageConfigPtr',0,4
	.word	38517
	.byte	2,35,52,20
	.word	37967
	.byte	7
	.word	38552
	.byte	13
	.byte	'GtmGeneralConfigPtr',0,4
	.word	38557
	.byte	2,35,56,20
	.word	38049
	.byte	7
	.word	38591
	.byte	13
	.byte	'GtmTbuConfigPtr',0,4
	.word	38596
	.byte	2,35,60,20
	.word	201
	.byte	7
	.word	38626
	.byte	13
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	38631
	.byte	2,35,64,13
	.byte	'GtmTtcanTriggers',0,2
	.word	38257
	.byte	2,35,68,0,8
	.byte	'Gtm_ModuleConfigType',0,4,163,7,2
	.word	38129
	.byte	21,1,1,22
	.word	201
	.byte	22
	.word	201
	.byte	22
	.word	201
	.byte	22
	.word	272
	.byte	0,7
	.word	38723
	.byte	8
	.byte	'Gtm_NotificationPtrType',0,4,172,7,16
	.word	38747
	.byte	17,4,176,7,9,40,15,16
	.word	38752
	.byte	16,3,0,13
	.byte	'TimNotifUsage',0,16
	.word	38791
	.byte	2,35,0,15,24
	.word	38752
	.byte	16,5,0,13
	.byte	'TomNotifUsage',0,24
	.word	38823
	.byte	2,35,16,0,8
	.byte	'Gtm_NotificationConfigType',0,4,184,7,2
	.word	38785
	.byte	7
	.word	38723
.L8:
	.byte	20
	.word	38785
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,36,0,3,8,11,15,62,15,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,46,0,3,8,58,15,59,15,57,15,54,15,63
	.byte	12,60,12,0,0,6,59,0,3,8,0,0,7,15,0,73,19,0,0,8,22,0,3,8,58,15,59,15,57,15,73,19,0,0,9,21,0,54,15,0,0,10
	.byte	19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15
	.byte	57,15,11,15,0,0,13,13,0,3,8,11,15,73,19,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15
	.byte	0,0,17,19,1,58,15,59,15,57,15,11,15,0,0,18,4,1,58,15,59,15,57,15,11,15,0,0,19,40,0,3,8,28,13,0,0,20,38
	.byte	0,73,19,0,0,21,21,1,54,15,39,12,0,0,22,5,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'..\\mcal_cfg\\Gtm_LCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'IfxGtm_regdef.h',0,1,0,0
	.byte	'Gtm.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('Gtm_kNotifConfig0')
	.sect	'.debug_info'
.L6:
	.word	209
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_LCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kNotifConfig0',0,1,141,1,34
	.word	.L8
	.byte	1,5,3
	.word	Gtm_kNotifConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kNotifConfig0')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0

; ..\mcal_cfg\Gtm_LCfg.c	     1  /******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	     2  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_cfg\Gtm_LCfg.c	     4  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	     5  ** All rights reserved.                                                      **
; ..\mcal_cfg\Gtm_LCfg.c	     6  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_cfg\Gtm_LCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_cfg\Gtm_LCfg.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_cfg\Gtm_LCfg.c	    10  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    11  *******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    12  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    13  **  FILENAME  : Gtm_LCfg.c                                                   **
; ..\mcal_cfg\Gtm_LCfg.c	    14  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    15  **  $CC VERSION : \main\dev_tc23x\4 $                                        **
; ..\mcal_cfg\Gtm_LCfg.c	    16  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    17  **  DATE, TIME: 2020-07-10, 14:56:10                                         **
; ..\mcal_cfg\Gtm_LCfg.c	    18  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    19  **  GENERATOR : Build b141014-0350                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    20  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    21  **  AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\mcal_cfg\Gtm_LCfg.c	    22  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    23  **  VENDOR    : Infineon Technologies                                        **
; ..\mcal_cfg\Gtm_LCfg.c	    24  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    25  **  DESCRIPTION  : GTM configuration generated out of ECU configuration      **
; ..\mcal_cfg\Gtm_LCfg.c	    26  **                 file (Mcu.bmd/.xdm)                                       **
; ..\mcal_cfg\Gtm_LCfg.c	    27  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    28  **  TRACEABILITY: 
; ..\mcal_cfg\Gtm_LCfg.c	    29      [cover parentID=SAS_AS4XX_GTM_PR679_1,SAS_AS4XX_GTM_PR679_2,
; ..\mcal_cfg\Gtm_LCfg.c	    30      SAS_AS4XX_GTM_PR679_3]
; ..\mcal_cfg\Gtm_LCfg.c	    31  [/cover]                                                                     **
; ..\mcal_cfg\Gtm_LCfg.c	    32  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_cfg\Gtm_LCfg.c	    33  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    34  ******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	    35  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    36  **                                                                            **
; ..\mcal_cfg\Gtm_LCfg.c	    37  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	    38  /******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    39  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    40  ** Copyright (C) Infineon Technologies (2018)                                **
; ..\mcal_cfg\Gtm_LCfg.c	    41  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    42  ** All rights reserved.                                                      **
; ..\mcal_cfg\Gtm_LCfg.c	    43  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    44  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_cfg\Gtm_LCfg.c	    45  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_cfg\Gtm_LCfg.c	    46  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_cfg\Gtm_LCfg.c	    47  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    48  *******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    49  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    50  **  FILENAME  : Gtm.m                                                        **
; ..\mcal_cfg\Gtm_LCfg.c	    51  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    52  **  $CC VERSION : \main\dev_tc23x\10 $                                       **
; ..\mcal_cfg\Gtm_LCfg.c	    53  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    54  **  DATE, TIME: 2020-07-10, 14:56:10                                         **
; ..\mcal_cfg\Gtm_LCfg.c	    55  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    56  **  GENERATOR : Build b141014-0350                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    57  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    58  **  AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\mcal_cfg\Gtm_LCfg.c	    59  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    60  **  VENDOR    : Infineon Technologies                                        **
; ..\mcal_cfg\Gtm_LCfg.c	    61  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    62  **  DESCRIPTION  : GTM configuration generated out of ECU configuration      **
; ..\mcal_cfg\Gtm_LCfg.c	    63  **                 file (Mcu.bmd/.xdm) for Gtm.m file for TC23x              **
; ..\mcal_cfg\Gtm_LCfg.c	    64  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    65  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_cfg\Gtm_LCfg.c	    66  **                                                                           **
; ..\mcal_cfg\Gtm_LCfg.c	    67  ******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	    68  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    69  **                                                                            **
; ..\mcal_cfg\Gtm_LCfg.c	    70  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	    71  
; ..\mcal_cfg\Gtm_LCfg.c	    72  
; ..\mcal_cfg\Gtm_LCfg.c	    73  
; ..\mcal_cfg\Gtm_LCfg.c	    74  
; ..\mcal_cfg\Gtm_LCfg.c	    75  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    76  **                      Includes                                              **
; ..\mcal_cfg\Gtm_LCfg.c	    77  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	    78  
; ..\mcal_cfg\Gtm_LCfg.c	    79  /* Own header file, this includes own configuration file also */
; ..\mcal_cfg\Gtm_LCfg.c	    80  #include "Gtm.h"
; ..\mcal_cfg\Gtm_LCfg.c	    81  
; ..\mcal_cfg\Gtm_LCfg.c	    82  
; ..\mcal_cfg\Gtm_LCfg.c	    83  
; ..\mcal_cfg\Gtm_LCfg.c	    84  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    85  **                      Global Macro Definitions                              **
; ..\mcal_cfg\Gtm_LCfg.c	    86  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	    87  /* Note:
; ..\mcal_cfg\Gtm_LCfg.c	    88  The user can configure the parameters with the tag Configuration:
; ..\mcal_cfg\Gtm_LCfg.c	    89  The user should not change anything under the tag Configuration Options:
; ..\mcal_cfg\Gtm_LCfg.c	    90  */
; ..\mcal_cfg\Gtm_LCfg.c	    91  
; ..\mcal_cfg\Gtm_LCfg.c	    92  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    93  **                      Private Macro Definitions                             **
; ..\mcal_cfg\Gtm_LCfg.c	    94  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	    95  
; ..\mcal_cfg\Gtm_LCfg.c	    96  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	    97  **                      Private Type Definitions                              **
; ..\mcal_cfg\Gtm_LCfg.c	    98  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	    99  
; ..\mcal_cfg\Gtm_LCfg.c	   100  
; ..\mcal_cfg\Gtm_LCfg.c	   101  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	   102  **                      Private Function Declarations                         **
; ..\mcal_cfg\Gtm_LCfg.c	   103  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	   104  
; ..\mcal_cfg\Gtm_LCfg.c	   105  
; ..\mcal_cfg\Gtm_LCfg.c	   106  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	   107  **                      Global Funtion Declarations                           **
; ..\mcal_cfg\Gtm_LCfg.c	   108  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	   109  extern void ecal_period_1ms
; ..\mcal_cfg\Gtm_LCfg.c	   110    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   111  extern void ecal_period_2ms
; ..\mcal_cfg\Gtm_LCfg.c	   112    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   113  extern void ecal_period_5ms
; ..\mcal_cfg\Gtm_LCfg.c	   114    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   115  extern void ecal_period_10ms
; ..\mcal_cfg\Gtm_LCfg.c	   116    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   117  extern void ecal_period_20ms
; ..\mcal_cfg\Gtm_LCfg.c	   118    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   119  extern void ecal_period_50ms
; ..\mcal_cfg\Gtm_LCfg.c	   120    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   121  extern void EyeQ_ErrInr
; ..\mcal_cfg\Gtm_LCfg.c	   122    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   123  extern void CAN1_ErrInr
; ..\mcal_cfg\Gtm_LCfg.c	   124    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   125  extern void LP875701_ErrInr
; ..\mcal_cfg\Gtm_LCfg.c	   126    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   127  extern void LP87563_ErrInr
; ..\mcal_cfg\Gtm_LCfg.c	   128    (uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal);
; ..\mcal_cfg\Gtm_LCfg.c	   129  /*******************************************************************************
; ..\mcal_cfg\Gtm_LCfg.c	   130  **                      Global Constant Definitions                           **
; ..\mcal_cfg\Gtm_LCfg.c	   131  *******************************************************************************/
; ..\mcal_cfg\Gtm_LCfg.c	   132  
; ..\mcal_cfg\Gtm_LCfg.c	   133  #define GTM_START_SEC_CONST_UNSPECIFIED
; ..\mcal_cfg\Gtm_LCfg.c	   134  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_cfg\Gtm_LCfg.c	   135   allowed only for MemMap.h*/
; ..\mcal_cfg\Gtm_LCfg.c	   136  #include "MemMap.h"
; ..\mcal_cfg\Gtm_LCfg.c	   137  
; ..\mcal_cfg\Gtm_LCfg.c	   138  #ifdef GTM_MOD_CH_CONFIGURED
; ..\mcal_cfg\Gtm_LCfg.c	   139  #if (GTM_MOD_CH_CONFIGURED == (STD_ON))
; ..\mcal_cfg\Gtm_LCfg.c	   140  
; ..\mcal_cfg\Gtm_LCfg.c	   141  const Gtm_NotificationConfigType Gtm_kNotifConfig0 =
; ..\mcal_cfg\Gtm_LCfg.c	   142  {
; ..\mcal_cfg\Gtm_LCfg.c	   143  #if (GTM_NO_OF_TIM_CH_CONF_NOTIF > 0U)
; ..\mcal_cfg\Gtm_LCfg.c	   144  {
; ..\mcal_cfg\Gtm_LCfg.c	   145      /*TIM Notification pointers*/
; ..\mcal_cfg\Gtm_LCfg.c	   146  
; ..\mcal_cfg\Gtm_LCfg.c	   147          &EyeQ_ErrInr,
; ..\mcal_cfg\Gtm_LCfg.c	   148          &CAN1_ErrInr,
; ..\mcal_cfg\Gtm_LCfg.c	   149          &LP875701_ErrInr,
; ..\mcal_cfg\Gtm_LCfg.c	   150          &LP87563_ErrInr,},
; ..\mcal_cfg\Gtm_LCfg.c	   151  #endif 
; ..\mcal_cfg\Gtm_LCfg.c	   152    
; ..\mcal_cfg\Gtm_LCfg.c	   153  #if (GTM_NO_OF_TOM_CH_CONF_NOTIF > 0U)
; ..\mcal_cfg\Gtm_LCfg.c	   154  {    /*TOM Notification pointers*/
; ..\mcal_cfg\Gtm_LCfg.c	   155  
; ..\mcal_cfg\Gtm_LCfg.c	   156          &ecal_period_1ms,
; ..\mcal_cfg\Gtm_LCfg.c	   157          &ecal_period_2ms,
; ..\mcal_cfg\Gtm_LCfg.c	   158          &ecal_period_5ms,
; ..\mcal_cfg\Gtm_LCfg.c	   159          &ecal_period_10ms,
; ..\mcal_cfg\Gtm_LCfg.c	   160          &ecal_period_20ms,
; ..\mcal_cfg\Gtm_LCfg.c	   161          &ecal_period_50ms,
; ..\mcal_cfg\Gtm_LCfg.c	   162    },
; ..\mcal_cfg\Gtm_LCfg.c	   163  #endif
; ..\mcal_cfg\Gtm_LCfg.c	   164  };
; ..\mcal_cfg\Gtm_LCfg.c	   165  #endif 
; ..\mcal_cfg\Gtm_LCfg.c	   166  /*#ifdef GTM_MOD_CH_CONFIGURED*/
; ..\mcal_cfg\Gtm_LCfg.c	   167  #endif 
; ..\mcal_cfg\Gtm_LCfg.c	   168  /*#if (GTM_MOD_CH_CONFIGURED == (STD_ON))*/
; ..\mcal_cfg\Gtm_LCfg.c	   169  
; ..\mcal_cfg\Gtm_LCfg.c	   170  #define GTM_STOP_SEC_CONST_UNSPECIFIED
; ..\mcal_cfg\Gtm_LCfg.c	   171  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_cfg\Gtm_LCfg.c	   172   allowed only for MemMap.h*/
; ..\mcal_cfg\Gtm_LCfg.c	   173  #include "MemMap.h"
; ..\mcal_cfg\Gtm_LCfg.c	   174  
; ..\mcal_cfg\Gtm_LCfg.c	   175  

	; Module end
