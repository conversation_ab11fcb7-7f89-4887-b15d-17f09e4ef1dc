	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc26428a -c99 --dep-file=eeprom\\NvM\\.NvM_Qry.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\NvM\\NvM_Qry.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\NvM\\NvM_Qry.src ..\\eeprom\\NvM\\NvM_Qry.c"
	.compiler_name		"ctc"
	.name	"NvM_Qry"

	
$TC16X
	
	.sdecl	'.text.NvM_Qry.NvM_QryBlockWriteAll',code,cluster('NvM_QryBlockWriteAll')
	.sect	'.text.NvM_Qry.NvM_QryBlockWriteAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	     1  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	     2   *  COPYRIGHT
; ..\eeprom\NvM\NvM_Qry.c	     3   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Qry.c	     4   *  \verbatim
; ..\eeprom\NvM\NvM_Qry.c	     5   *  Copyright (c) 2019 by Vector Informatik GmbH.                                                  All rights reserved.
; ..\eeprom\NvM\NvM_Qry.c	     6   *
; ..\eeprom\NvM\NvM_Qry.c	     7   *                This software is copyright protected and proprietary to Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_Qry.c	     8   *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
; ..\eeprom\NvM\NvM_Qry.c	     9   *                All other rights remain with Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_Qry.c	    10   *  \endverbatim
; ..\eeprom\NvM\NvM_Qry.c	    11   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Qry.c	    12   *  FILE DESCRIPTION
; ..\eeprom\NvM\NvM_Qry.c	    13   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Qry.c	    14   *         File:  NvM_Qry.c
; ..\eeprom\NvM\NvM_Qry.c	    15   *      Project:  MemService_AsrNvM
; ..\eeprom\NvM\NvM_Qry.c	    16   *       Module:  NvM - Submodule Qry (Query functions)
; ..\eeprom\NvM\NvM_Qry.c	    17   *    Generator:  -
; ..\eeprom\NvM\NvM_Qry.c	    18   *
; ..\eeprom\NvM\NvM_Qry.c	    19   *  Description:  This sub-module implements the queries to be executed to evaluate
; ..\eeprom\NvM\NvM_Qry.c	    20   *                the conditions for a state transition
; ..\eeprom\NvM\NvM_Qry.c	    21   *
; ..\eeprom\NvM\NvM_Qry.c	    22   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    23  
; ..\eeprom\NvM\NvM_Qry.c	    24  /* Do not modify this file! */
; ..\eeprom\NvM\NvM_Qry.c	    25  
; ..\eeprom\NvM\NvM_Qry.c	    26  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    27   *  MODULE SWITCH
; ..\eeprom\NvM\NvM_Qry.c	    28   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    29  #define NVM_QRY_SOURCE
; ..\eeprom\NvM\NvM_Qry.c	    30  
; ..\eeprom\NvM\NvM_Qry.c	    31  
; ..\eeprom\NvM\NvM_Qry.c	    32  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    33   *  INCLUDES
; ..\eeprom\NvM\NvM_Qry.c	    34   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    35  #include "Std_Types.h"
; ..\eeprom\NvM\NvM_Qry.c	    36  
; ..\eeprom\NvM\NvM_Qry.c	    37  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    38   *  INCLUDE OF CONFIGURATION (PUBLIC SECTION)
; ..\eeprom\NvM\NvM_Qry.c	    39   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    40  #include "NvM_Cfg.h"
; ..\eeprom\NvM\NvM_Qry.c	    41  #include "NvM_PrivateCfg.h"
; ..\eeprom\NvM\NvM_Qry.c	    42  
; ..\eeprom\NvM\NvM_Qry.c	    43  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    44   *  INCLUDE OF THE CENTRAL INTERNAL INCLUDE
; ..\eeprom\NvM\NvM_Qry.c	    45   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    46  #include "NvM_JobProc.h"
; ..\eeprom\NvM\NvM_Qry.c	    47  
; ..\eeprom\NvM\NvM_Qry.c	    48  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    49   *  MODULE HEADER INCLUDES
; ..\eeprom\NvM\NvM_Qry.c	    50   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    51  #include "NvM_Qry.h"
; ..\eeprom\NvM\NvM_Qry.c	    52  
; ..\eeprom\NvM\NvM_Qry.c	    53  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    54   *  INCLUDE OF THE QUEUE VARIABLES
; ..\eeprom\NvM\NvM_Qry.c	    55   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    56  #include "NvM_Queue.h"
; ..\eeprom\NvM\NvM_Qry.c	    57  
; ..\eeprom\NvM\NvM_Qry.c	    58  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    59   *  INCLUDE OF THE CRC VARIABLES
; ..\eeprom\NvM\NvM_Qry.c	    60   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    61  #include "NvM_Crc.h"
; ..\eeprom\NvM\NvM_Qry.c	    62  
; ..\eeprom\NvM\NvM_Qry.c	    63  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    64   *  INTERNAL MACROS
; ..\eeprom\NvM\NvM_Qry.c	    65   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    66  #ifndef NVM_LOCAL /* COV_NVM_COMPATIBILITY */
; ..\eeprom\NvM\NvM_Qry.c	    67  # define NVM_LOCAL static
; ..\eeprom\NvM\NvM_Qry.c	    68  #endif
; ..\eeprom\NvM\NvM_Qry.c	    69  
; ..\eeprom\NvM\NvM_Qry.c	    70  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    71   *  LOCAL FUNCTION PROTOTYPES
; ..\eeprom\NvM\NvM_Qry.c	    72   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    73  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_Qry.c	    74    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Qry.c	    75  
; ..\eeprom\NvM\NvM_Qry.c	    76  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    77   * NvM_QryBlockWriteAll
; ..\eeprom\NvM\NvM_Qry.c	    78   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    79  /*! \brief Check if the block shall be written during NvM_WriteAll().
; ..\eeprom\NvM\NvM_Qry.c	    80   *  \details Check if the block shall be written during NvM_WriteAll().
; ..\eeprom\NvM\NvM_Qry.c	    81   *  \return TRUE block shall be written during WriteAll
; ..\eeprom\NvM\NvM_Qry.c	    82   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	    83   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	    84   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	    85   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	    86   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	    87   */
; ..\eeprom\NvM\NvM_Qry.c	    88  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryBlockWriteAll(void);
; ..\eeprom\NvM\NvM_Qry.c	    89  
; ..\eeprom\NvM\NvM_Qry.c	    90  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	    91   * NvM_QryCancelWriteAll
; ..\eeprom\NvM\NvM_Qry.c	    92   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	    93  /*! \brief Checks whether a NvM_CancelWriteAll request was setup.
; ..\eeprom\NvM\NvM_Qry.c	    94   *  \details Checks whether a NvM_CancelWriteAll request was setup.
; ..\eeprom\NvM\NvM_Qry.c	    95   *  \return TRUE CancelWriteAll setup
; ..\eeprom\NvM\NvM_Qry.c	    96   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	    97   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	    98   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	    99   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   100   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   101   */
; ..\eeprom\NvM\NvM_Qry.c	   102  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCancelWriteAll(void);
; ..\eeprom\NvM\NvM_Qry.c	   103  
; ..\eeprom\NvM\NvM_Qry.c	   104  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   105  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   106   * NvM_QryWriteAllKilled
; ..\eeprom\NvM\NvM_Qry.c	   107   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   108  /*! \brief Checks whether a NvM_KillWriteAll request was setup.
; ..\eeprom\NvM\NvM_Qry.c	   109   *  \details Checks whether a NvM_KillWriteAll request was setup.
; ..\eeprom\NvM\NvM_Qry.c	   110   *  \return TRUE KillWriteAll setup
; ..\eeprom\NvM\NvM_Qry.c	   111   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   112   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   113   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   114   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   115   *  \config KillWriteAll is enabled
; ..\eeprom\NvM\NvM_Qry.c	   116   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   117   */
; ..\eeprom\NvM\NvM_Qry.c	   118  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryWriteAllKilled(void);
; ..\eeprom\NvM\NvM_Qry.c	   119  #endif /* (NVM_KILL_WRITEALL_API == STD_ON) */
; ..\eeprom\NvM\NvM_Qry.c	   120  
; ..\eeprom\NvM\NvM_Qry.c	   121  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   122   * NvM_QryCrcMatch
; ..\eeprom\NvM\NvM_Qry.c	   123   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   124  /*! \brief Check whether the recalculated Crc matches the read one, in case the is a configured Crc.
; ..\eeprom\NvM\NvM_Qry.c	   125   *  \details Check whether the recalculated Crc matches the read one, in case the is a configured Crc.
; ..\eeprom\NvM\NvM_Qry.c	   126   *  \return TRUE Crc matches
; ..\eeprom\NvM\NvM_Qry.c	   127   *  \return FALSE no Crc configured or the Crc does not match
; ..\eeprom\NvM\NvM_Qry.c	   128   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   129   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   130   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   131   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   132   */
; ..\eeprom\NvM\NvM_Qry.c	   133  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCrcMatch(void);
; ..\eeprom\NvM\NvM_Qry.c	   134  
; ..\eeprom\NvM\NvM_Qry.c	   135  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   136   * NvM_QryCrcBusy
; ..\eeprom\NvM\NvM_Qry.c	   137   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   138  /*! \brief Checks the current block CRC calculation state.
; ..\eeprom\NvM\NvM_Qry.c	   139   *  \details -
; ..\eeprom\NvM\NvM_Qry.c	   140   *  \return TRUE if the CRC is busy, FALSE otherwise.
; ..\eeprom\NvM\NvM_Qry.c	   141   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   142   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   143   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   144   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   145   */
; ..\eeprom\NvM\NvM_Qry.c	   146  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCrcBusy(void);
; ..\eeprom\NvM\NvM_Qry.c	   147  
; ..\eeprom\NvM\NvM_Qry.c	   148  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   149   * NvM_QryDataCopyBusy
; ..\eeprom\NvM\NvM_Qry.c	   150   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   151  /*!
; ..\eeprom\NvM\NvM_Qry.c	   152   * \brief       Checks the current block's ByteCount value.
; ..\eeprom\NvM\NvM_Qry.c	   153   * \details     -
; ..\eeprom\NvM\NvM_Qry.c	   154   * \return      TRUE, if ByteCount > 0, FALSE otherwise.
; ..\eeprom\NvM\NvM_Qry.c	   155   * \context     TASK
; ..\eeprom\NvM\NvM_Qry.c	   156   * \reentrant   FALSE
; ..\eeprom\NvM\NvM_Qry.c	   157   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   158   * \pre         -
; ..\eeprom\NvM\NvM_Qry.c	   159   */
; ..\eeprom\NvM\NvM_Qry.c	   160  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryDataCopyBusy(void);
; ..\eeprom\NvM\NvM_Qry.c	   161  
; ..\eeprom\NvM\NvM_Qry.c	   162  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   163   * NvM_QryLastBlockDone_ReadAll
; ..\eeprom\NvM\NvM_Qry.c	   164   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   165  /*! \brief Check whether the NvM_ReadAll job is finished.
; ..\eeprom\NvM\NvM_Qry.c	   166   *  \details Check whether the NvM_ReadAll job is finished.
; ..\eeprom\NvM\NvM_Qry.c	   167   *  \return TRUE NvM_ReadAll job is finished
; ..\eeprom\NvM\NvM_Qry.c	   168   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   169   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   170   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   171   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   172   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   173   */
; ..\eeprom\NvM\NvM_Qry.c	   174  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryLastBlockDone_ReadAll(void);
; ..\eeprom\NvM\NvM_Qry.c	   175  
; ..\eeprom\NvM\NvM_Qry.c	   176  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   177   * NvM_QryLastBlockDone_WriteAll
; ..\eeprom\NvM\NvM_Qry.c	   178   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   179  /*! \brief Checks whether the NvM_WriteAll job is finished.
; ..\eeprom\NvM\NvM_Qry.c	   180   *  \details Checks whether the NvM_WriteAll job is finished.
; ..\eeprom\NvM\NvM_Qry.c	   181   *  \return TRUE WriteAll job is finished
; ..\eeprom\NvM\NvM_Qry.c	   182   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   183   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   184   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   185   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   186   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   187   */
; ..\eeprom\NvM\NvM_Qry.c	   188  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryLastBlockDone_WriteAll(void);
; ..\eeprom\NvM\NvM_Qry.c	   189  
; ..\eeprom\NvM\NvM_Qry.c	   190  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   191   * NvM_QryLastResultOk
; ..\eeprom\NvM\NvM_Qry.c	   192   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   193  /*! \brief Checks whether the last MemIf job was successful.
; ..\eeprom\NvM\NvM_Qry.c	   194   *  \details Checks whether the last MemIf job was successful.
; ..\eeprom\NvM\NvM_Qry.c	   195   *  \return TRUE last MemIf job was successful
; ..\eeprom\NvM\NvM_Qry.c	   196   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   197   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   198   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   199   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   200   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   201   */
; ..\eeprom\NvM\NvM_Qry.c	   202  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryLastResultOk(void);
; ..\eeprom\NvM\NvM_Qry.c	   203  
; ..\eeprom\NvM\NvM_Qry.c	   204  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   205   * NvM_QryMainFsmRunning
; ..\eeprom\NvM\NvM_Qry.c	   206   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   207  /*! \brief Checks whether the main-FSM is currently pending.
; ..\eeprom\NvM\NvM_Qry.c	   208   *  \details Checks whether the main-FSM is currently pending.
; ..\eeprom\NvM\NvM_Qry.c	   209   *  \return TRUE main-FSM is pending
; ..\eeprom\NvM\NvM_Qry.c	   210   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   211   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   212   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   213   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   214   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   215   */
; ..\eeprom\NvM\NvM_Qry.c	   216  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryMainFsmRunning(void);
; ..\eeprom\NvM\NvM_Qry.c	   217  
; ..\eeprom\NvM\NvM_Qry.c	   218  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   219   * NvM_QryMultiJob
; ..\eeprom\NvM\NvM_Qry.c	   220   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   221  /*! \brief Checks whether a multi block job is requested.
; ..\eeprom\NvM\NvM_Qry.c	   222   *  \details Checks whether a multi block job is requested.
; ..\eeprom\NvM\NvM_Qry.c	   223   *  \return TRUE multi block job is setup
; ..\eeprom\NvM\NvM_Qry.c	   224   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   225   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   226   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   227   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   228   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   229   */
; ..\eeprom\NvM\NvM_Qry.c	   230  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryMultiJob(void);
; ..\eeprom\NvM\NvM_Qry.c	   231  
; ..\eeprom\NvM\NvM_Qry.c	   232  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   233   * NvM_QryNvBusy
; ..\eeprom\NvM\NvM_Qry.c	   234   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   235  /*! \brief Checks whether the underlying module for current job is busy.
; ..\eeprom\NvM\NvM_Qry.c	   236   *  \details we differ between polling mode on and off: in case we are running in polling mode and the corresponding
; ..\eeprom\NvM\NvM_Qry.c	   237   *           underlying module is not busy, we set the current job result, too. In case we are running with polling
; ..\eeprom\NvM\NvM_Qry.c	   238   *           mode off, we set the job to pending - the underlying module has to invoke the corresponding job-end or
; ..\eeprom\NvM\NvM_Qry.c	   239   *           job-error callback to signal a finished job.
; ..\eeprom\NvM\NvM_Qry.c	   240   *  \return TRUE underlying module is busy
; ..\eeprom\NvM\NvM_Qry.c	   241   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   242   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   243   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   244   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   245   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   246   */
; ..\eeprom\NvM\NvM_Qry.c	   247  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryNvBusy(void);
; ..\eeprom\NvM\NvM_Qry.c	   248  
; ..\eeprom\NvM\NvM_Qry.c	   249  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   250   * NvM_QryMemHwaBusy
; ..\eeprom\NvM\NvM_Qry.c	   251   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   252  /*! \brief Check whether the underlying modules are busy.
; ..\eeprom\NvM\NvM_Qry.c	   253   *  \details Check whether the underlying modules are busy.
; ..\eeprom\NvM\NvM_Qry.c	   254   *  \return TRUE at least one underlying module is busy
; ..\eeprom\NvM\NvM_Qry.c	   255   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   256   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   257   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   258   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   259   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   260   */
; ..\eeprom\NvM\NvM_Qry.c	   261  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryMemHwaBusy(void);
; ..\eeprom\NvM\NvM_Qry.c	   262  
; ..\eeprom\NvM\NvM_Qry.c	   263  #if (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   264  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   265   * NvM_QryRamValid
; ..\eeprom\NvM\NvM_Qry.c	   266   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   267  /*! \brief Test if the RAM of the block is still valid or if the block is locked - i.e. Read during ReadAll
; ..\eeprom\NvM\NvM_Qry.c	   268   *  \details Test if the RAM of the block is still valid or if the block is locked - i.e. Read during ReadAll
; ..\eeprom\NvM\NvM_Qry.c	   269   *  \return TRUE RAM is valid and not locked
; ..\eeprom\NvM\NvM_Qry.c	   270   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   271   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   272   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   273   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   274   *  \config SetRamBlockStatus API enabled
; ..\eeprom\NvM\NvM_Qry.c	   275   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   276   */
; ..\eeprom\NvM\NvM_Qry.c	   277  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryRamValid(void);
; ..\eeprom\NvM\NvM_Qry.c	   278  #endif /* (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) */
; ..\eeprom\NvM\NvM_Qry.c	   279  
; ..\eeprom\NvM\NvM_Qry.c	   280  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   281   * NvM_QryRedundantBlock
; ..\eeprom\NvM\NvM_Qry.c	   282   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   283  /*! \brief Test if the current block has been configured redundant, and the redundant NV block is not selected.
; ..\eeprom\NvM\NvM_Qry.c	   284   *  \details Test if the current block has been configured redundant, and the redundant NV block is not selected.
; ..\eeprom\NvM\NvM_Qry.c	   285   *  \return TRUE redundant block
; ..\eeprom\NvM\NvM_Qry.c	   286   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   287   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   288   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   289   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   290   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   291   */
; ..\eeprom\NvM\NvM_Qry.c	   292  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryRedundantBlock(void);
; ..\eeprom\NvM\NvM_Qry.c	   293  
; ..\eeprom\NvM\NvM_Qry.c	   294  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   295   * NvM_QrySkipBlock
; ..\eeprom\NvM\NvM_Qry.c	   296   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   297  /*! \brief Checks whether the block is not selected for NvM_ReadAll-job or it isn't a dataset block (shall be skipped)
; ..\eeprom\NvM\NvM_Qry.c	   298   *  \details Checks whether the block is not selected for NvM_ReadAll-job or it isn't a dataset block (shall be skipped)
; ..\eeprom\NvM\NvM_Qry.c	   299   *  \return TRUE block shall be skipped
; ..\eeprom\NvM\NvM_Qry.c	   300   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   301   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   302   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   303   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   304   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   305   */
; ..\eeprom\NvM\NvM_Qry.c	   306  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QrySkipBlock(void);
; ..\eeprom\NvM\NvM_Qry.c	   307  
; ..\eeprom\NvM\NvM_Qry.c	   308  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   309   * NvM_QrySubFsmRunning
; ..\eeprom\NvM\NvM_Qry.c	   310   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   311  /*! \brief Checks whether the sub-FSM is currently pending.
; ..\eeprom\NvM\NvM_Qry.c	   312   *  \details Checks whether the sub-FSM is currently pending.
; ..\eeprom\NvM\NvM_Qry.c	   313   *  \return TRUE sub-FSM is pending
; ..\eeprom\NvM\NvM_Qry.c	   314   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   315   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   316   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   317   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   318   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   319   */
; ..\eeprom\NvM\NvM_Qry.c	   320  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QrySubFsmRunning(void);
; ..\eeprom\NvM\NvM_Qry.c	   321  
; ..\eeprom\NvM\NvM_Qry.c	   322  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   323   * NvM_QryTrue
; ..\eeprom\NvM\NvM_Qry.c	   324   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   325  /*! \brief Simply returns TRUE.
; ..\eeprom\NvM\NvM_Qry.c	   326   *  \details Simply returns TRUE.
; ..\eeprom\NvM\NvM_Qry.c	   327   *  \return always TRUE
; ..\eeprom\NvM\NvM_Qry.c	   328   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   329   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   330   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   331   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   332   */
; ..\eeprom\NvM\NvM_Qry.c	   333  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryTrue(void);
; ..\eeprom\NvM\NvM_Qry.c	   334  
; ..\eeprom\NvM\NvM_Qry.c	   335  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   336   * NvM_QryWriteBlockOnce
; ..\eeprom\NvM\NvM_Qry.c	   337   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   338  /*! \brief Checks whether the block is a write once block.
; ..\eeprom\NvM\NvM_Qry.c	   339   *  \details Checks whether the block is a write once block.
; ..\eeprom\NvM\NvM_Qry.c	   340   *  \return TRUE in case the current block is write once
; ..\eeprom\NvM\NvM_Qry.c	   341   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   342   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   343   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   344   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   345   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   346   */
; ..\eeprom\NvM\NvM_Qry.c	   347  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryWriteBlockOnce(void);
; ..\eeprom\NvM\NvM_Qry.c	   348  
; ..\eeprom\NvM\NvM_Qry.c	   349  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   350   * NvM_QryWriteRetriesExceeded
; ..\eeprom\NvM\NvM_Qry.c	   351   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   352  /*! \brief Checks whether the current number of write retries do not exceed the allowed boundary.
; ..\eeprom\NvM\NvM_Qry.c	   353   *  \details Checks whether the current number of write retries do not exceed the allowed boundary.
; ..\eeprom\NvM\NvM_Qry.c	   354   *  \return FALSE in case the write retries counter exceeded
; ..\eeprom\NvM\NvM_Qry.c	   355   *  \return TRUE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   356   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   357   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   358   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   359   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   360   */
; ..\eeprom\NvM\NvM_Qry.c	   361  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryWriteRetriesExceeded(void);
; ..\eeprom\NvM\NvM_Qry.c	   362  
; ..\eeprom\NvM\NvM_Qry.c	   363  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   364   * NvM_QryHasRom
; ..\eeprom\NvM\NvM_Qry.c	   365   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   366  /*! \brief Checks whether the current block has defaults (ROM block or Init callback)
; ..\eeprom\NvM\NvM_Qry.c	   367   *  \details Checks whether the current block has defaults (ROM block or Init callback)
; ..\eeprom\NvM\NvM_Qry.c	   368   *  \return FALSE block has no defaults
; ..\eeprom\NvM\NvM_Qry.c	   369   *  \return TRUE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   370   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   371   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   372   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   373   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   374   */
; ..\eeprom\NvM\NvM_Qry.c	   375  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryHasRom(void);
; ..\eeprom\NvM\NvM_Qry.c	   376  
; ..\eeprom\NvM\NvM_Qry.c	   377  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   378   * NvM_QryExtRuntime
; ..\eeprom\NvM\NvM_Qry.c	   379   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   380  /*! \brief Checks whether for current block the extended runtime preparation shall be executed.
; ..\eeprom\NvM\NvM_Qry.c	   381   *  \details Checks whether for current block the extended runtime preparation shall be executed.
; ..\eeprom\NvM\NvM_Qry.c	   382   *  \return FALSE normal runtime preparation shall be executed
; ..\eeprom\NvM\NvM_Qry.c	   383   *  \return TRUE extended runtime preparation shall be executed
; ..\eeprom\NvM\NvM_Qry.c	   384   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   385   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   386   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   387   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   388   */
; ..\eeprom\NvM\NvM_Qry.c	   389  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryExtRuntime(void);
; ..\eeprom\NvM\NvM_Qry.c	   390  
; ..\eeprom\NvM\NvM_Qry.c	   391  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   392  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   393   * NvM_QrySuspendRepairRedundantBlocks
; ..\eeprom\NvM\NvM_Qry.c	   394   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   395  /*!
; ..\eeprom\NvM\NvM_Qry.c	   396   * \brief Checks whether the pending background repair redundant blocks job shall be suspended (non-Destructively) or not.
; ..\eeprom\NvM\NvM_Qry.c	   397   * \details Since the redundant block repairing is a background job, it can be suspended in a non-destructive way 
; ..\eeprom\NvM\NvM_Qry.c	   398   *          by all possible jobs - single or multi block. This query checks whether there is a normal priority job,
; ..\eeprom\NvM\NvM_Qry.c	   399   *          or multi block job (ReadAll or WriteAll). It does not care about the high priority jobs - NvM handles this itself.
; ..\eeprom\NvM\NvM_Qry.c	   400   * \return FALSE 
; ..\eeprom\NvM\NvM_Qry.c	   401   * \return TRUE 
; ..\eeprom\NvM\NvM_Qry.c	   402   * \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   403   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   404   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   405   * \config Repair Redundant Blocks Api is enabled
; ..\eeprom\NvM\NvM_Qry.c	   406   * \pre -
; ..\eeprom\NvM\NvM_Qry.c	   407   */
; ..\eeprom\NvM\NvM_Qry.c	   408  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QrySuspendRepairRedundantBlocks(void);
; ..\eeprom\NvM\NvM_Qry.c	   409  
; ..\eeprom\NvM\NvM_Qry.c	   410  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   411   * NvM_QryRepairRedBlockDefect
; ..\eeprom\NvM\NvM_Qry.c	   412   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   413  /*!
; ..\eeprom\NvM\NvM_Qry.c	   414   * \brief Checks whether current redundant block can and shall be repaired or not.
; ..\eeprom\NvM\NvM_Qry.c	   415   * \details We decide between three situations:
; ..\eeprom\NvM\NvM_Qry.c	   416   *          1. both blocks are valid, nothing to repair
; ..\eeprom\NvM\NvM_Qry.c	   417   *          2. both blocks are defect, NvM cannot repair
; ..\eeprom\NvM\NvM_Qry.c	   418   *          3. only one block is defect, NvM will try to repair the redundancy
; ..\eeprom\NvM\NvM_Qry.c	   419   * \return FALSE block does not have to be repaired or cannot be repaired
; ..\eeprom\NvM\NvM_Qry.c	   420   * \return TRUE  block is defect and shall be repaired
; ..\eeprom\NvM\NvM_Qry.c	   421   * \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   422   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   423   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   424   * \config Repair Redundant Blocks Api is enabled
; ..\eeprom\NvM\NvM_Qry.c	   425   * \pre -
; ..\eeprom\NvM\NvM_Qry.c	   426   */
; ..\eeprom\NvM\NvM_Qry.c	   427  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryRepairRedBlockDefect(void);
; ..\eeprom\NvM\NvM_Qry.c	   428  #endif
; ..\eeprom\NvM\NvM_Qry.c	   429  
; ..\eeprom\NvM\NvM_Qry.c	   430  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   431   * NvM_QryCRCCompMechanismSkipWrite
; ..\eeprom\NvM\NvM_Qry.c	   432   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   433  /*!
; ..\eeprom\NvM\NvM_Qry.c	   434   * \brief Checks whether the block writing shall be skipped because of CRCCompMechanism and clean block.
; ..\eeprom\NvM\NvM_Qry.c	   435   * \details -
; ..\eeprom\NvM\NvM_Qry.c	   436   * \return TRUE  if the block shall be skipped
; ..\eeprom\NvM\NvM_Qry.c	   437   * \return FALSE if the block shall not be skipped
; ..\eeprom\NvM\NvM_Qry.c	   438   * \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   439   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   440   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   441   * \config -
; ..\eeprom\NvM\NvM_Qry.c	   442   * \pre -
; ..\eeprom\NvM\NvM_Qry.c	   443   */
; ..\eeprom\NvM\NvM_Qry.c	   444  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCRCCompMechanismSkipWrite(void);
; ..\eeprom\NvM\NvM_Qry.c	   445  
; ..\eeprom\NvM\NvM_Qry.c	   446  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   447   * NvM_QryPostReadTransform
; ..\eeprom\NvM\NvM_Qry.c	   448   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   449  /*!
; ..\eeprom\NvM\NvM_Qry.c	   450   * \brief       Invokes the post read transformation callback with the read data and returns its result.
; ..\eeprom\NvM\NvM_Qry.c	   451   * \details     Within post read transformation the user can decide whether the data is valid or not - in case it isn't
; ..\eeprom\NvM\NvM_Qry.c	   452   *              NvM shall behave same as in case the CRC does not match or there is no data.
; ..\eeprom\NvM\NvM_Qry.c	   453   * \return      TRUE, if the post read transformation callback returns OK, or is not configured; FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   454   * \pre         -
; ..\eeprom\NvM\NvM_Qry.c	   455   * \context     TASK
; ..\eeprom\NvM\NvM_Qry.c	   456   * \reentrant   FALSE
; ..\eeprom\NvM\NvM_Qry.c	   457   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   458   */
; ..\eeprom\NvM\NvM_Qry.c	   459  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryPostReadTransform(void);
; ..\eeprom\NvM\NvM_Qry.c	   460  
; ..\eeprom\NvM\NvM_Qry.c	   461  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   462   * NvM_QryCsmRetryNecessary
; ..\eeprom\NvM\NvM_Qry.c	   463   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   464  /*! \brief Checks the last CSM job return value and whether the current number of CSM retries do not exceed the allowed boundary.
; ..\eeprom\NvM\NvM_Qry.c	   465   *  \details -
; ..\eeprom\NvM\NvM_Qry.c	   466   *  \return TRUE in case the last CSM job return value was either CRYPTO_E_BUSY or CRYPTO_E_QUEUE_FULL AND retries not exceeded
; ..\eeprom\NvM\NvM_Qry.c	   467   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Qry.c	   468   *  \context TASK
; ..\eeprom\NvM\NvM_Qry.c	   469   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Qry.c	   470   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Qry.c	   471   *  \pre -
; ..\eeprom\NvM\NvM_Qry.c	   472   */
; ..\eeprom\NvM\NvM_Qry.c	   473  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCsmRetryNecessary(void);
; ..\eeprom\NvM\NvM_Qry.c	   474  
; ..\eeprom\NvM\NvM_Qry.c	   475  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_Qry.c	   476    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Qry.c	   477  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   478   *  INTERNAL MODULE GLOBAL VARIABLES
; ..\eeprom\NvM\NvM_Qry.c	   479   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   480  
; ..\eeprom\NvM\NvM_Qry.c	   481  #define NVM_START_SEC_CONST_UNSPECIFIED
; ..\eeprom\NvM\NvM_Qry.c	   482    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Qry.c	   483  
; ..\eeprom\NvM\NvM_Qry.c	   484  /* the query function table, indexed by NvM_StateQueryIdType */
; ..\eeprom\NvM\NvM_Qry.c	   485  CONST(NvM_QryFctPtrType, NVM_PRIVATE_CONST) NvM_QueryTable_ap[(uint32)NVM_QRY_ID_TRUE + 1u] = /* PRQA S 1533 */ /* MD_NvM_8.9 */
; ..\eeprom\NvM\NvM_Qry.c	   486  {
; ..\eeprom\NvM\NvM_Qry.c	   487      NvM_QryBlockWriteAll,
; ..\eeprom\NvM\NvM_Qry.c	   488      NvM_QryCancelWriteAll,
; ..\eeprom\NvM\NvM_Qry.c	   489  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   490      NvM_QryWriteAllKilled,
; ..\eeprom\NvM\NvM_Qry.c	   491  #endif
; ..\eeprom\NvM\NvM_Qry.c	   492      NvM_QryCrcBusy,
; ..\eeprom\NvM\NvM_Qry.c	   493      NvM_QryDataCopyBusy,
; ..\eeprom\NvM\NvM_Qry.c	   494      NvM_QryCrcMatch,
; ..\eeprom\NvM\NvM_Qry.c	   495      NvM_QryLastBlockDone_ReadAll,
; ..\eeprom\NvM\NvM_Qry.c	   496      NvM_QryLastBlockDone_WriteAll,
; ..\eeprom\NvM\NvM_Qry.c	   497      NvM_QryLastResultOk,
; ..\eeprom\NvM\NvM_Qry.c	   498      NvM_QryMainFsmRunning,
; ..\eeprom\NvM\NvM_Qry.c	   499      NvM_QryMultiJob,
; ..\eeprom\NvM\NvM_Qry.c	   500  #if (NVM_API_CONFIG_CLASS_1 != NVM_API_CONFIG_CLASS)
; ..\eeprom\NvM\NvM_Qry.c	   501      NvM_QryNormalPrioJob,
; ..\eeprom\NvM\NvM_Qry.c	   502  # if (NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   503      NvM_QryHighPrioJob,
; ..\eeprom\NvM\NvM_Qry.c	   504  # endif
; ..\eeprom\NvM\NvM_Qry.c	   505  #endif
; ..\eeprom\NvM\NvM_Qry.c	   506      NvM_QryNvBusy,
; ..\eeprom\NvM\NvM_Qry.c	   507      NvM_QryMemHwaBusy,
; ..\eeprom\NvM\NvM_Qry.c	   508  #if  (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   509      NvM_QryRamValid,
; ..\eeprom\NvM\NvM_Qry.c	   510  #endif
; ..\eeprom\NvM\NvM_Qry.c	   511      NvM_QryRedundantBlock,
; ..\eeprom\NvM\NvM_Qry.c	   512      NvM_QrySkipBlock,
; ..\eeprom\NvM\NvM_Qry.c	   513      NvM_QrySubFsmRunning,
; ..\eeprom\NvM\NvM_Qry.c	   514      NvM_QryWriteBlockOnce,
; ..\eeprom\NvM\NvM_Qry.c	   515      NvM_QryWriteRetriesExceeded,
; ..\eeprom\NvM\NvM_Qry.c	   516      NvM_QryHasRom,
; ..\eeprom\NvM\NvM_Qry.c	   517      NvM_QryExtRuntime,
; ..\eeprom\NvM\NvM_Qry.c	   518  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   519      NvM_QrySuspendRepairRedundantBlocks,
; ..\eeprom\NvM\NvM_Qry.c	   520      NvM_QryRepairRedBlockDefect,
; ..\eeprom\NvM\NvM_Qry.c	   521  #endif
; ..\eeprom\NvM\NvM_Qry.c	   522      NvM_QryCRCCompMechanismSkipWrite,
; ..\eeprom\NvM\NvM_Qry.c	   523      NvM_QryPostReadTransform,
; ..\eeprom\NvM\NvM_Qry.c	   524      NvM_QryReadAllKilled,
; ..\eeprom\NvM\NvM_Qry.c	   525      NvM_QrySyncDecrypt,
; ..\eeprom\NvM\NvM_Qry.c	   526      NvM_QrySyncEncrypt,
; ..\eeprom\NvM\NvM_Qry.c	   527      NvM_QryCsmRetryNecessary,
; ..\eeprom\NvM\NvM_Qry.c	   528      NvM_QryTrue
; ..\eeprom\NvM\NvM_Qry.c	   529  };
; ..\eeprom\NvM\NvM_Qry.c	   530  
; ..\eeprom\NvM\NvM_Qry.c	   531  #define NVM_STOP_SEC_CONST_UNSPECIFIED
; ..\eeprom\NvM\NvM_Qry.c	   532    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Qry.c	   533  
; ..\eeprom\NvM\NvM_Qry.c	   534  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   535   *  IMPLEMENTATION
; ..\eeprom\NvM\NvM_Qry.c	   536   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   537  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_Qry.c	   538    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Qry.c	   539  
; ..\eeprom\NvM\NvM_Qry.c	   540  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   541  *  NvM_QryBlockWriteAll
; ..\eeprom\NvM\NvM_Qry.c	   542  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   543  /*!
; ..\eeprom\NvM\NvM_Qry.c	   544   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   545   *
; ..\eeprom\NvM\NvM_Qry.c	   546   *
; ..\eeprom\NvM\NvM_Qry.c	   547   */
; ..\eeprom\NvM\NvM_Qry.c	   548  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryBlockWriteAll(void)
; Function NvM_QryBlockWriteAll
.L56:
NvM_QryBlockWriteAll:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   549  {
; ..\eeprom\NvM\NvM_Qry.c	   550  #if (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   551      return (boolean)(((NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 &
; ..\eeprom\NvM\NvM_Qry.c	   552                 (NVM_WR_PROT_SET | NVM_LOCK_STAT_SET | NVM_STATE_CHANGED_SET | NVM_STATE_VALID_SET)) ==
; ..\eeprom\NvM\NvM_Qry.c	   553                                                (NVM_STATE_CHANGED_SET | NVM_STATE_VALID_SET)) &&
	mov	d2,#0
	fcall	.cocofun_4
.L372:
	ld.a	a2,[a15]4
.L373:
	ld.bu	d15,[a2]2
.L374:
	and	d15,#195
.L375:
	jne	d15,#3,.L2
.L376:

; ..\eeprom\NvM\NvM_Qry.c	   554              ((NvM_CurrentBlockInfo_t.Descriptor_pt->Flags_u8 & NVM_SELECT_BLOCK_FOR_WRITEALL_ON) != 0u));
	ld.a	a15,[a15]
.L377:
	ld.bu	d15,[a15]59
.L378:
	jz.t	d15:5,.L3
.L379:
	mov	d2,#1
.L3:
.L2:

; ..\eeprom\NvM\NvM_Qry.c	   555  #else
; ..\eeprom\NvM\NvM_Qry.c	   556      return (boolean)(((NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 & 
; ..\eeprom\NvM\NvM_Qry.c	   557                                                          (NVM_WR_PROT_SET | NVM_LOCK_STAT_SET)) == 0u) &&
; ..\eeprom\NvM\NvM_Qry.c	   558              ((NvM_CurrentBlockInfo_t.Descriptor_pt->Flags_u8 & NVM_SELECT_BLOCK_FOR_WRITEALL_ON) != 0u));
; ..\eeprom\NvM\NvM_Qry.c	   559  #endif
; ..\eeprom\NvM\NvM_Qry.c	   560  }
	ret
.L299:
	
__NvM_QryBlockWriteAll_function_end:
	.size	NvM_QryBlockWriteAll,__NvM_QryBlockWriteAll_function_end-NvM_QryBlockWriteAll
.L149:
	; End of function
	
	.sdecl	'.text.NvM_Qry..cocofun_4',code,cluster('.cocofun_4')
	.sect	'.text.NvM_Qry..cocofun_4'
	.align	2
; Function .cocofun_4
.L58:
.cocofun_4:	.type	func
; Function body .cocofun_4, coco_iter:1
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t)
.L339:
	lea	a15,[a15]@los(NvM_CurrentBlockInfo_t)
.L568:
	fret
.L289:
	; End of function
	.sdecl	'.text.NvM_Qry.NvM_QryCancelWriteAll',code,cluster('NvM_QryCancelWriteAll')
	.sect	'.text.NvM_Qry.NvM_QryCancelWriteAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   561  
; ..\eeprom\NvM\NvM_Qry.c	   562  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   563  *  NvM_QryCancelWriteAll
; ..\eeprom\NvM\NvM_Qry.c	   564  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   565  /*!
; ..\eeprom\NvM\NvM_Qry.c	   566   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   567   *
; ..\eeprom\NvM\NvM_Qry.c	   568   *
; ..\eeprom\NvM\NvM_Qry.c	   569   */
; ..\eeprom\NvM\NvM_Qry.c	   570  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCancelWriteAll(void)
; Function NvM_QryCancelWriteAll
.L60:
NvM_QryCancelWriteAll:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   571  {
; ..\eeprom\NvM\NvM_Qry.c	   572    return ((NvM_ApiFlags_u8 & NVM_APIFLAG_CANCEL_WR_ALL_SET) != 0u);
	fcall	.cocofun_1
.L384:
	and	d15,#16
.L385:

; ..\eeprom\NvM\NvM_Qry.c	   573  }
	ne	d2,d15,#0
	ret
.L301:
	
__NvM_QryCancelWriteAll_function_end:
	.size	NvM_QryCancelWriteAll,__NvM_QryCancelWriteAll_function_end-NvM_QryCancelWriteAll
.L154:
	; End of function
	
	.sdecl	'.text.NvM_Qry..cocofun_1',code,cluster('.cocofun_1')
	.sect	'.text.NvM_Qry..cocofun_1'
	.align	2
; Function .cocofun_1
.L62:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh.a	a15,#@his(NvM_ApiFlags_u8)
	ld.bu	d15,[a15]@los(NvM_ApiFlags_u8)
.L551:
	fret
.L274:
	; End of function
	.sdecl	'.text.NvM_Qry.NvM_QryWriteAllKilled',code,cluster('NvM_QryWriteAllKilled')
	.sect	'.text.NvM_Qry.NvM_QryWriteAllKilled'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   574  
; ..\eeprom\NvM\NvM_Qry.c	   575  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   576  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   577  *  NvM_QryWriteAllKilled
; ..\eeprom\NvM\NvM_Qry.c	   578  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   579  /*!
; ..\eeprom\NvM\NvM_Qry.c	   580   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   581   *
; ..\eeprom\NvM\NvM_Qry.c	   582   *
; ..\eeprom\NvM\NvM_Qry.c	   583   */
; ..\eeprom\NvM\NvM_Qry.c	   584  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryWriteAllKilled(void)
; Function NvM_QryWriteAllKilled
.L64:
NvM_QryWriteAllKilled:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   585  {
; ..\eeprom\NvM\NvM_Qry.c	   586      return (boolean)((NvM_CurrentJob_t.JobServiceId_t == NVM_INT_FID_WRITE_ALL) && 
	movh.a	a15,#@his(NvM_CurrentJob_t+2)
.L390:
	ld.bu	d15,[a15]@los(NvM_CurrentJob_t+2)
.L391:
	mov	d2,#0
.L392:
	jne	d15,#5,.L6
.L393:

; ..\eeprom\NvM\NvM_Qry.c	   587          ((NvM_ApiFlags_u8 & NVM_APIFLAG_KILL_WR_ALL_SET) != 0u));
	fcall	.cocofun_1
.L394:
	jz.t	d15:5,.L7
.L395:
	mov	d2,#1
.L7:
.L6:

; ..\eeprom\NvM\NvM_Qry.c	   588  }
	ret
.L303:
	
__NvM_QryWriteAllKilled_function_end:
	.size	NvM_QryWriteAllKilled,__NvM_QryWriteAllKilled_function_end-NvM_QryWriteAllKilled
.L159:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryCrcMatch',code,cluster('NvM_QryCrcMatch')
	.sect	'.text.NvM_Qry.NvM_QryCrcMatch'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   589  #endif /* (NVM_KILL_WRITEALL_API == STD_ON) */
; ..\eeprom\NvM\NvM_Qry.c	   590  
; ..\eeprom\NvM\NvM_Qry.c	   591  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   592  *  NvM_QryCrcMatch
; ..\eeprom\NvM\NvM_Qry.c	   593  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   594  /*!
; ..\eeprom\NvM\NvM_Qry.c	   595   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   596   *
; ..\eeprom\NvM\NvM_Qry.c	   597   *
; ..\eeprom\NvM\NvM_Qry.c	   598   */
; ..\eeprom\NvM\NvM_Qry.c	   599  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCrcMatch(void)
; Function NvM_QryCrcMatch
.L66:
NvM_QryCrcMatch:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   600  {
; ..\eeprom\NvM\NvM_Qry.c	   601      return (boolean)NvM_CrcJob_Compare(&NvM_CurrentBlockInfo_t.BlockCrcJob_t); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+16)
.L400:
	lea	a4,[a15]@los(NvM_CurrentBlockInfo_t+16)
	j	NvM_CrcJob_Compare
.L304:
	
__NvM_QryCrcMatch_function_end:
	.size	NvM_QryCrcMatch,__NvM_QryCrcMatch_function_end-NvM_QryCrcMatch
.L164:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryCrcBusy',code,cluster('NvM_QryCrcBusy')
	.sect	'.text.NvM_Qry.NvM_QryCrcBusy'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   602  }
; ..\eeprom\NvM\NvM_Qry.c	   603  
; ..\eeprom\NvM\NvM_Qry.c	   604  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   605  *  NvM_QryCrcBusy
; ..\eeprom\NvM\NvM_Qry.c	   606  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   607  /*!
; ..\eeprom\NvM\NvM_Qry.c	   608   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   609   *
; ..\eeprom\NvM\NvM_Qry.c	   610   *
; ..\eeprom\NvM\NvM_Qry.c	   611   */
; ..\eeprom\NvM\NvM_Qry.c	   612  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCrcBusy(void)
; Function NvM_QryCrcBusy
.L68:
NvM_QryCrcBusy:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   613  {
; ..\eeprom\NvM\NvM_Qry.c	   614      return (boolean)(NvM_CrcJob_isBusy(&NvM_CurrentBlockInfo_t.BlockCrcJob_t));
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+32)
	ld.hu	d15,[a15]@los(NvM_CurrentBlockInfo_t+32)
.L405:

; ..\eeprom\NvM\NvM_Qry.c	   615  }
	ne	d2,d15,#0
	ret
.L305:
	
__NvM_QryCrcBusy_function_end:
	.size	NvM_QryCrcBusy,__NvM_QryCrcBusy_function_end-NvM_QryCrcBusy
.L169:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryDataCopyBusy',code,cluster('NvM_QryDataCopyBusy')
	.sect	'.text.NvM_Qry.NvM_QryDataCopyBusy'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   616  
; ..\eeprom\NvM\NvM_Qry.c	   617  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   618  *  NvM_QryDataCopyBusy
; ..\eeprom\NvM\NvM_Qry.c	   619  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   620  /*!
; ..\eeprom\NvM\NvM_Qry.c	   621   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   622   *
; ..\eeprom\NvM\NvM_Qry.c	   623   *
; ..\eeprom\NvM\NvM_Qry.c	   624   */
; ..\eeprom\NvM\NvM_Qry.c	   625  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryDataCopyBusy(void)
; Function NvM_QryDataCopyBusy
.L70:
NvM_QryDataCopyBusy:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   626  {
; ..\eeprom\NvM\NvM_Qry.c	   627      return (boolean)(NvM_CurrentBlockInfo_t.ByteCount_u16 > 0u);
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+38)
.L410:
	ld.hu	d15,[a15]@los(NvM_CurrentBlockInfo_t+38)
.L411:

; ..\eeprom\NvM\NvM_Qry.c	   628  }
	ne	d2,d15,#0
	ret
.L306:
	
__NvM_QryDataCopyBusy_function_end:
	.size	NvM_QryDataCopyBusy,__NvM_QryDataCopyBusy_function_end-NvM_QryDataCopyBusy
.L174:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryLastBlockDone_ReadAll',code,cluster('NvM_QryLastBlockDone_ReadAll')
	.sect	'.text.NvM_Qry.NvM_QryLastBlockDone_ReadAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   629  
; ..\eeprom\NvM\NvM_Qry.c	   630  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   631  *  NvM_QryLastBlockDone_ReadAll
; ..\eeprom\NvM\NvM_Qry.c	   632  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   633  /*!
; ..\eeprom\NvM\NvM_Qry.c	   634   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   635   *
; ..\eeprom\NvM\NvM_Qry.c	   636   *
; ..\eeprom\NvM\NvM_Qry.c	   637   */
; ..\eeprom\NvM\NvM_Qry.c	   638  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryLastBlockDone_ReadAll(void)
; Function NvM_QryLastBlockDone_ReadAll
.L72:
NvM_QryLastBlockDone_ReadAll:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   639  {
; ..\eeprom\NvM\NvM_Qry.c	   640      return (boolean)(NvM_CurrentJob_t.JobBlockId_t >= NvM_NoOfBlockIds_t);
	movh.a	a15,#@his(NvM_CurrentJob_t)
.L416:
	ld.hu	d15,[a15]@los(NvM_CurrentJob_t)
.L417:
	movh.a	a15,#@his(NvM_NoOfBlockIds_t)
	ld.hu	d0,[a15]@los(NvM_NoOfBlockIds_t)
.L418:

; ..\eeprom\NvM\NvM_Qry.c	   641  }
	ge.u	d2,d15,d0
	ret
.L307:
	
__NvM_QryLastBlockDone_ReadAll_function_end:
	.size	NvM_QryLastBlockDone_ReadAll,__NvM_QryLastBlockDone_ReadAll_function_end-NvM_QryLastBlockDone_ReadAll
.L179:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryLastBlockDone_WriteAll',code,cluster('NvM_QryLastBlockDone_WriteAll')
	.sect	'.text.NvM_Qry.NvM_QryLastBlockDone_WriteAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   642  
; ..\eeprom\NvM\NvM_Qry.c	   643  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   644  *  NvM_QryLastBlockDone_WriteAll
; ..\eeprom\NvM\NvM_Qry.c	   645  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   646  /*!
; ..\eeprom\NvM\NvM_Qry.c	   647   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   648   *
; ..\eeprom\NvM\NvM_Qry.c	   649   *
; ..\eeprom\NvM\NvM_Qry.c	   650   */
; ..\eeprom\NvM\NvM_Qry.c	   651  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryLastBlockDone_WriteAll(void)
; Function NvM_QryLastBlockDone_WriteAll
.L74:
NvM_QryLastBlockDone_WriteAll:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   652  {
; ..\eeprom\NvM\NvM_Qry.c	   653      return (boolean)(NvM_CurrentJob_t.JobBlockId_t == 0u);
	movh.a	a15,#@his(NvM_CurrentJob_t)
.L423:
	ld.hu	d15,[a15]@los(NvM_CurrentJob_t)
.L424:

; ..\eeprom\NvM\NvM_Qry.c	   654  }
	eq	d2,d15,#0
	ret
.L308:
	
__NvM_QryLastBlockDone_WriteAll_function_end:
	.size	NvM_QryLastBlockDone_WriteAll,__NvM_QryLastBlockDone_WriteAll_function_end-NvM_QryLastBlockDone_WriteAll
.L184:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryLastResultOk',code,cluster('NvM_QryLastResultOk')
	.sect	'.text.NvM_Qry.NvM_QryLastResultOk'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   655  
; ..\eeprom\NvM\NvM_Qry.c	   656  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   657  *  NvM_QryLastResultOk
; ..\eeprom\NvM\NvM_Qry.c	   658  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   659  /*!
; ..\eeprom\NvM\NvM_Qry.c	   660   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   661   *
; ..\eeprom\NvM\NvM_Qry.c	   662   *
; ..\eeprom\NvM\NvM_Qry.c	   663   */
; ..\eeprom\NvM\NvM_Qry.c	   664  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryLastResultOk(void)
; Function NvM_QryLastResultOk
.L76:
NvM_QryLastResultOk:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   665  {
; ..\eeprom\NvM\NvM_Qry.c	   666      return (boolean)(NVM_REQ_OK == NvM_CurrentBlockInfo_t.LastResult_t);
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L429:
	ld.bu	d15,[a15]@los(NvM_CurrentBlockInfo_t+40)
.L430:

; ..\eeprom\NvM\NvM_Qry.c	   667  }
	eq	d2,d15,#0
	ret
.L309:
	
__NvM_QryLastResultOk_function_end:
	.size	NvM_QryLastResultOk,__NvM_QryLastResultOk_function_end-NvM_QryLastResultOk
.L189:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryMainFsmRunning',code,cluster('NvM_QryMainFsmRunning')
	.sect	'.text.NvM_Qry.NvM_QryMainFsmRunning'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   668  
; ..\eeprom\NvM\NvM_Qry.c	   669  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   670  *  NvM_QryMainFsmRunning
; ..\eeprom\NvM\NvM_Qry.c	   671  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   672  /*!
; ..\eeprom\NvM\NvM_Qry.c	   673   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   674   *
; ..\eeprom\NvM\NvM_Qry.c	   675   *
; ..\eeprom\NvM\NvM_Qry.c	   676   */
; ..\eeprom\NvM\NvM_Qry.c	   677  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryMainFsmRunning(void)
; Function NvM_QryMainFsmRunning
.L78:
NvM_QryMainFsmRunning:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   678  {
; ..\eeprom\NvM\NvM_Qry.c	   679      return (boolean)(NVM_STATE_FSM_FINISHED != NvM_JobMainState_t);
	movh.a	a15,#@his(NvM_JobMainState_t)
	ld.bu	d15,[a15]@los(NvM_JobMainState_t)
.L435:

; ..\eeprom\NvM\NvM_Qry.c	   680  }
	ne	d2,d15,#32
	ret
.L310:
	
__NvM_QryMainFsmRunning_function_end:
	.size	NvM_QryMainFsmRunning,__NvM_QryMainFsmRunning_function_end-NvM_QryMainFsmRunning
.L194:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryMultiJob',code,cluster('NvM_QryMultiJob')
	.sect	'.text.NvM_Qry.NvM_QryMultiJob'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   681  
; ..\eeprom\NvM\NvM_Qry.c	   682  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   683  *  NvM_QryMultiJob
; ..\eeprom\NvM\NvM_Qry.c	   684  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   685  /*!
; ..\eeprom\NvM\NvM_Qry.c	   686   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   687   *
; ..\eeprom\NvM\NvM_Qry.c	   688   *
; ..\eeprom\NvM\NvM_Qry.c	   689   */
; ..\eeprom\NvM\NvM_Qry.c	   690  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryMultiJob(void)
; Function NvM_QryMultiJob
.L80:
NvM_QryMultiJob:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   691  {
; ..\eeprom\NvM\NvM_Qry.c	   692      return (boolean)((NvM_ApiFlags_u8 &
	fcall	.cocofun_1
.L440:
	and	d15,#6
.L441:

; ..\eeprom\NvM\NvM_Qry.c	   693          (NVM_APIFLAG_WRITE_ALL_SET | NVM_APIFLAG_READ_ALL_SET 
; ..\eeprom\NvM\NvM_Qry.c	   694  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)  /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Qry.c	   695          | NVM_APIFLAG_REPAIR_REDUNDANT_BLOCKS_SET
; ..\eeprom\NvM\NvM_Qry.c	   696  #endif  /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Qry.c	   697          )) != 0u);
; ..\eeprom\NvM\NvM_Qry.c	   698  }
	ne	d2,d15,#0
	ret
.L311:
	
__NvM_QryMultiJob_function_end:
	.size	NvM_QryMultiJob,__NvM_QryMultiJob_function_end-NvM_QryMultiJob
.L199:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryNvBusy',code,cluster('NvM_QryNvBusy')
	.sect	'.text.NvM_Qry.NvM_QryNvBusy'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   699  
; ..\eeprom\NvM\NvM_Qry.c	   700  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   701  *  NvM_QryNvBusy
; ..\eeprom\NvM\NvM_Qry.c	   702  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   703  /*!
; ..\eeprom\NvM\NvM_Qry.c	   704   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   705   *
; ..\eeprom\NvM\NvM_Qry.c	   706   *
; ..\eeprom\NvM\NvM_Qry.c	   707   *
; ..\eeprom\NvM\NvM_Qry.c	   708   *
; ..\eeprom\NvM\NvM_Qry.c	   709   *
; ..\eeprom\NvM\NvM_Qry.c	   710   *
; ..\eeprom\NvM\NvM_Qry.c	   711   *
; ..\eeprom\NvM\NvM_Qry.c	   712   */
; ..\eeprom\NvM\NvM_Qry.c	   713  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryNvBusy(void)
; Function NvM_QryNvBusy
.L82:
NvM_QryNvBusy:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   714  {
; ..\eeprom\NvM\NvM_Qry.c	   715  #if(NVM_POLLING_MODE == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   716      const uint8 deviceId = (uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8;
; ..\eeprom\NvM\NvM_Qry.c	   717      boolean retVal = FALSE;
; ..\eeprom\NvM\NvM_Qry.c	   718  
; ..\eeprom\NvM\NvM_Qry.c	   719      if(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_PENDING)
; ..\eeprom\NvM\NvM_Qry.c	   720      {
; ..\eeprom\NvM\NvM_Qry.c	   721          if (MEMIF_BUSY == MemIf_GetStatus(deviceId))
; ..\eeprom\NvM\NvM_Qry.c	   722          {
; ..\eeprom\NvM\NvM_Qry.c	   723              retVal = TRUE;
; ..\eeprom\NvM\NvM_Qry.c	   724          }
; ..\eeprom\NvM\NvM_Qry.c	   725          else
; ..\eeprom\NvM\NvM_Qry.c	   726          {
; ..\eeprom\NvM\NvM_Qry.c	   727              switch(MemIf_GetJobResult(deviceId))
; ..\eeprom\NvM\NvM_Qry.c	   728              {
; ..\eeprom\NvM\NvM_Qry.c	   729                  case MEMIF_JOB_OK:
; ..\eeprom\NvM\NvM_Qry.c	   730                      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_OK;
; ..\eeprom\NvM\NvM_Qry.c	   731                      break;
; ..\eeprom\NvM\NvM_Qry.c	   732  
; ..\eeprom\NvM\NvM_Qry.c	   733                  case MEMIF_BLOCK_INCONSISTENT:
; ..\eeprom\NvM\NvM_Qry.c	   734                      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_INTEGRITY_FAILED;
; ..\eeprom\NvM\NvM_Qry.c	   735                      break;
; ..\eeprom\NvM\NvM_Qry.c	   736  
; ..\eeprom\NvM\NvM_Qry.c	   737                  case MEMIF_BLOCK_INVALID:
; ..\eeprom\NvM\NvM_Qry.c	   738                      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NV_INVALIDATED;
; ..\eeprom\NvM\NvM_Qry.c	   739                      break;
; ..\eeprom\NvM\NvM_Qry.c	   740  
; ..\eeprom\NvM\NvM_Qry.c	   741                  default:
; ..\eeprom\NvM\NvM_Qry.c	   742                      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Qry.c	   743                      break;
; ..\eeprom\NvM\NvM_Qry.c	   744              }
; ..\eeprom\NvM\NvM_Qry.c	   745          }
; ..\eeprom\NvM\NvM_Qry.c	   746      }
; ..\eeprom\NvM\NvM_Qry.c	   747      return retVal;
; ..\eeprom\NvM\NvM_Qry.c	   748  #else
; ..\eeprom\NvM\NvM_Qry.c	   749      return (boolean)(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_PENDING);
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L446:
	ld.bu	d15,[a15]@los(NvM_CurrentBlockInfo_t+40)
.L447:

; ..\eeprom\NvM\NvM_Qry.c	   750  #endif
; ..\eeprom\NvM\NvM_Qry.c	   751  }
	eq	d2,d15,#2
	ret
.L312:
	
__NvM_QryNvBusy_function_end:
	.size	NvM_QryNvBusy,__NvM_QryNvBusy_function_end-NvM_QryNvBusy
.L204:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryMemHwaBusy',code,cluster('NvM_QryMemHwaBusy')
	.sect	'.text.NvM_Qry.NvM_QryMemHwaBusy'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   752  
; ..\eeprom\NvM\NvM_Qry.c	   753  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   754  *  NvM_QryMemHwaBusy
; ..\eeprom\NvM\NvM_Qry.c	   755  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   756  /*!
; ..\eeprom\NvM\NvM_Qry.c	   757   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   758   *
; ..\eeprom\NvM\NvM_Qry.c	   759   *
; ..\eeprom\NvM\NvM_Qry.c	   760   */
; ..\eeprom\NvM\NvM_Qry.c	   761  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryMemHwaBusy(void)
; Function NvM_QryMemHwaBusy
.L84:
NvM_QryMemHwaBusy:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   762  {
; ..\eeprom\NvM\NvM_Qry.c	   763    const MemIf_StatusType MemHwaStatus = MemIf_GetStatus(MEMIF_BROADCAST_ID);
	mov	d4,#255
	call	MemIf_GetStatus
.L334:

; ..\eeprom\NvM\NvM_Qry.c	   764  
; ..\eeprom\NvM\NvM_Qry.c	   765    return (boolean)((MemHwaStatus == MEMIF_BUSY) || (MemHwaStatus == MEMIF_BUSY_INTERNAL));
	mov	d15,#0
.L452:
	jeq	d2,#2,.L18
.L453:
	jne	d2,#3,.L19
.L18:
	mov	d15,#1
.L19:

; ..\eeprom\NvM\NvM_Qry.c	   766  }
	mov	d2,d15
	ret
.L313:
	
__NvM_QryMemHwaBusy_function_end:
	.size	NvM_QryMemHwaBusy,__NvM_QryMemHwaBusy_function_end-NvM_QryMemHwaBusy
.L209:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryRedundantBlock',code,cluster('NvM_QryRedundantBlock')
	.sect	'.text.NvM_Qry.NvM_QryRedundantBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   767  
; ..\eeprom\NvM\NvM_Qry.c	   768  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   769  *  NvM_QryRedundantBlock
; ..\eeprom\NvM\NvM_Qry.c	   770  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   771  /*!
; ..\eeprom\NvM\NvM_Qry.c	   772   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   773   *
; ..\eeprom\NvM\NvM_Qry.c	   774   *
; ..\eeprom\NvM\NvM_Qry.c	   775   */
; ..\eeprom\NvM\NvM_Qry.c	   776  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryRedundantBlock(void)
; Function NvM_QryRedundantBlock
.L86:
NvM_QryRedundantBlock:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   777  {
; ..\eeprom\NvM\NvM_Qry.c	   778    return (boolean)(
; ..\eeprom\NvM\NvM_Qry.c	   779        ((NvM_CurrentBlockInfo_t.Descriptor_pt->MngmtType_t & NVM_BLOCK_REDUNDANT) != 0u) &&
	mov	d2,#0
	fcall	.cocofun_2
.L469:
	ld.bu	d15,[a2]58
	extr.u	d15,d15,#4,#2
.L470:
	jz.t	d15:0,.L21
.L471:

; ..\eeprom\NvM\NvM_Qry.c	   780        ((NvM_CurrentBlockInfo_t.NvIdentifier_u16 & 0x0001u) == 0u));
	ld.hu	d15,[a15]36
.L472:
	jnz.t	d15:0,.L22
.L473:
	mov	d2,#1
.L22:
.L21:

; ..\eeprom\NvM\NvM_Qry.c	   781  }
	ret
.L317:
	
__NvM_QryRedundantBlock_function_end:
	.size	NvM_QryRedundantBlock,__NvM_QryRedundantBlock_function_end-NvM_QryRedundantBlock
.L219:
	; End of function
	
	.sdecl	'.text.NvM_Qry..cocofun_2',code,cluster('.cocofun_2')
	.sect	'.text.NvM_Qry..cocofun_2'
	.align	2
; Function .cocofun_2
.L88:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	fcall	.cocofun_4
.L340:
	ld.a	a2,[a15]
.L556:
	fret
.L279:
	; End of function
	.sdecl	'.text.NvM_Qry.NvM_QrySkipBlock',code,cluster('NvM_QrySkipBlock')
	.sect	'.text.NvM_Qry.NvM_QrySkipBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   782  
; ..\eeprom\NvM\NvM_Qry.c	   783  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   784  *  NvM_QrySkipBlock
; ..\eeprom\NvM\NvM_Qry.c	   785  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   786  /*!
; ..\eeprom\NvM\NvM_Qry.c	   787   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   788   *
; ..\eeprom\NvM\NvM_Qry.c	   789   *
; ..\eeprom\NvM\NvM_Qry.c	   790   *
; ..\eeprom\NvM\NvM_Qry.c	   791   *
; ..\eeprom\NvM\NvM_Qry.c	   792   *
; ..\eeprom\NvM\NvM_Qry.c	   793   */
; ..\eeprom\NvM\NvM_Qry.c	   794  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QrySkipBlock(void)
; Function NvM_QrySkipBlock
.L90:
NvM_QrySkipBlock:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   795  {
; ..\eeprom\NvM\NvM_Qry.c	   796    return (boolean)(((NvM_CurrentBlockInfo_t.Descriptor_pt->Flags_u8 & NVM_SELECT_BLOCK_FOR_READALL_ON) == 0u)
; ..\eeprom\NvM\NvM_Qry.c	   797  #if (NVM_DYNAMIC_CONFIGURATION == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Qry.c	   798        || ((!NvM_QryHasRom()) && (NvM_QryExtRuntime())) /* PRQA S 3415 */ /* MD_NvM_13.5 */
	fcall	.cocofun_3
.L478:
	jz.t	d15:4,.L24
.L479:
	call	NvM_QryHasRom
.L480:
	jne	d2,#0,.L25
.L481:
	call	NvM_QryExtRuntime
	jeq	d2,#0,.L26
.L24:
	mov	d8,#1
.L26:
.L25:

; ..\eeprom\NvM\NvM_Qry.c	   799  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Qry.c	   800        );
; ..\eeprom\NvM\NvM_Qry.c	   801  }
	mov	d2,d8
	ret
.L319:
	
__NvM_QrySkipBlock_function_end:
	.size	NvM_QrySkipBlock,__NvM_QrySkipBlock_function_end-NvM_QrySkipBlock
.L224:
	; End of function
	
	.sdecl	'.text.NvM_Qry..cocofun_3',code,cluster('.cocofun_3')
	.sect	'.text.NvM_Qry..cocofun_3'
	.align	2
; Function .cocofun_3
.L92:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:0
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t)
.L561:
	ld.a	a15,[a15]@los(NvM_CurrentBlockInfo_t)
.L562:
	mov	d8,#0
.L335:
	ld.bu	d15,[a15]59
.L563:
	fret
.L284:
	; End of function
	.sdecl	'.text.NvM_Qry.NvM_QrySubFsmRunning',code,cluster('NvM_QrySubFsmRunning')
	.sect	'.text.NvM_Qry.NvM_QrySubFsmRunning'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   802  
; ..\eeprom\NvM\NvM_Qry.c	   803  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   804  *  NvM_QrySubFsmRunning
; ..\eeprom\NvM\NvM_Qry.c	   805  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   806  /*!
; ..\eeprom\NvM\NvM_Qry.c	   807   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   808   *
; ..\eeprom\NvM\NvM_Qry.c	   809   *
; ..\eeprom\NvM\NvM_Qry.c	   810   */
; ..\eeprom\NvM\NvM_Qry.c	   811  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QrySubFsmRunning(void)
; Function NvM_QrySubFsmRunning
.L94:
NvM_QrySubFsmRunning:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   812  {
; ..\eeprom\NvM\NvM_Qry.c	   813    return (boolean)(NVM_STATE_FSM_FINISHED != NvM_JobSubState_t);
	movh.a	a15,#@his(NvM_JobSubState_t)
	ld.bu	d15,[a15]@los(NvM_JobSubState_t)
.L486:

; ..\eeprom\NvM\NvM_Qry.c	   814  }
	ne	d2,d15,#32
	ret
.L321:
	
__NvM_QrySubFsmRunning_function_end:
	.size	NvM_QrySubFsmRunning,__NvM_QrySubFsmRunning_function_end-NvM_QrySubFsmRunning
.L229:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryTrue',code,cluster('NvM_QryTrue')
	.sect	'.text.NvM_Qry.NvM_QryTrue'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   815  
; ..\eeprom\NvM\NvM_Qry.c	   816  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   817  *  NvM_QryTrue
; ..\eeprom\NvM\NvM_Qry.c	   818  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   819  /*!
; ..\eeprom\NvM\NvM_Qry.c	   820   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   821   *
; ..\eeprom\NvM\NvM_Qry.c	   822   *
; ..\eeprom\NvM\NvM_Qry.c	   823   */
; ..\eeprom\NvM\NvM_Qry.c	   824  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryTrue(void)
; Function NvM_QryTrue
.L96:
NvM_QryTrue:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   825  {
; ..\eeprom\NvM\NvM_Qry.c	   826    return TRUE;
; ..\eeprom\NvM\NvM_Qry.c	   827  }
	mov	d2,#1
	ret
.L322:
	
__NvM_QryTrue_function_end:
	.size	NvM_QryTrue,__NvM_QryTrue_function_end-NvM_QryTrue
.L234:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryWriteBlockOnce',code,cluster('NvM_QryWriteBlockOnce')
	.sect	'.text.NvM_Qry.NvM_QryWriteBlockOnce'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   828  
; ..\eeprom\NvM\NvM_Qry.c	   829  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   830  *  NvM_QryWriteBlockOnce
; ..\eeprom\NvM\NvM_Qry.c	   831  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   832  /*!
; ..\eeprom\NvM\NvM_Qry.c	   833   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   834   *
; ..\eeprom\NvM\NvM_Qry.c	   835   *
; ..\eeprom\NvM\NvM_Qry.c	   836   *
; ..\eeprom\NvM\NvM_Qry.c	   837   *
; ..\eeprom\NvM\NvM_Qry.c	   838   */
; ..\eeprom\NvM\NvM_Qry.c	   839  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryWriteBlockOnce(void)
; Function NvM_QryWriteBlockOnce
.L98:
NvM_QryWriteBlockOnce:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   840  {
; ..\eeprom\NvM\NvM_Qry.c	   841    boolean writeOnce = FALSE;
	fcall	.cocofun_3
.L336:

; ..\eeprom\NvM\NvM_Qry.c	   842  
; ..\eeprom\NvM\NvM_Qry.c	   843    if ((NvM_CurrentBlockInfo_t.Descriptor_pt->Flags_u8 & NVM_BLOCK_WRITE_BLOCK_ONCE_ON) != 0u)
	jz.t	d15:2,.L30
.L495:

; ..\eeprom\NvM\NvM_Qry.c	   844    {
; ..\eeprom\NvM\NvM_Qry.c	   845  #if(NVM_DYNAMIC_CONFIGURATION == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Qry.c	   846      if (!NvM_QryExtRuntime())
	call	NvM_QryExtRuntime
.L496:

; ..\eeprom\NvM\NvM_Qry.c	   847  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Qry.c	   848      {
; ..\eeprom\NvM\NvM_Qry.c	   849        writeOnce = TRUE;
	sel	d8,d2,d8,#1
.L30:

; ..\eeprom\NvM\NvM_Qry.c	   850      }
; ..\eeprom\NvM\NvM_Qry.c	   851    }
; ..\eeprom\NvM\NvM_Qry.c	   852  
; ..\eeprom\NvM\NvM_Qry.c	   853    return writeOnce;
; ..\eeprom\NvM\NvM_Qry.c	   854  }
	mov	d2,d8
	ret
.L323:
	
__NvM_QryWriteBlockOnce_function_end:
	.size	NvM_QryWriteBlockOnce,__NvM_QryWriteBlockOnce_function_end-NvM_QryWriteBlockOnce
.L239:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryWriteRetriesExceeded',code,cluster('NvM_QryWriteRetriesExceeded')
	.sect	'.text.NvM_Qry.NvM_QryWriteRetriesExceeded'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   855  
; ..\eeprom\NvM\NvM_Qry.c	   856  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   857  *  NvM_QryWriteRetriesExceeded
; ..\eeprom\NvM\NvM_Qry.c	   858  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   859  /*!
; ..\eeprom\NvM\NvM_Qry.c	   860   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   861   *
; ..\eeprom\NvM\NvM_Qry.c	   862   *
; ..\eeprom\NvM\NvM_Qry.c	   863   */
; ..\eeprom\NvM\NvM_Qry.c	   864  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryWriteRetriesExceeded(void)
; Function NvM_QryWriteRetriesExceeded
.L100:
NvM_QryWriteRetriesExceeded:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   865  {
; ..\eeprom\NvM\NvM_Qry.c	   866    /* the initial try is also counted as retry. Therefore the total number of
; ..\eeprom\NvM\NvM_Qry.c	   867         write retries may reach the configured number of attempts. */
; ..\eeprom\NvM\NvM_Qry.c	   868    return (boolean)(NvM_CurrentBlockInfo_t.WriteRetryCounter_u8 > NVM_NOOFWRITEATTEMPTS);
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+41)
.L501:
	ld.bu	d15,[a15]@los(NvM_CurrentBlockInfo_t+41)
.L502:
	mov	d0,#1
.L503:

; ..\eeprom\NvM\NvM_Qry.c	   869  }
	lt.u	d2,d0,d15
	ret
.L325:
	
__NvM_QryWriteRetriesExceeded_function_end:
	.size	NvM_QryWriteRetriesExceeded,__NvM_QryWriteRetriesExceeded_function_end-NvM_QryWriteRetriesExceeded
.L244:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryRamValid',code,cluster('NvM_QryRamValid')
	.sect	'.text.NvM_Qry.NvM_QryRamValid'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   870  
; ..\eeprom\NvM\NvM_Qry.c	   871  #if (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   872  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   873  *  NvM_QryRamValid
; ..\eeprom\NvM\NvM_Qry.c	   874  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   875  /*!
; ..\eeprom\NvM\NvM_Qry.c	   876   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   877   *
; ..\eeprom\NvM\NvM_Qry.c	   878   *
; ..\eeprom\NvM\NvM_Qry.c	   879   *
; ..\eeprom\NvM\NvM_Qry.c	   880   */
; ..\eeprom\NvM\NvM_Qry.c	   881  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryRamValid(void)
; Function NvM_QryRamValid
.L102:
NvM_QryRamValid:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   882  {
; ..\eeprom\NvM\NvM_Qry.c	   883    return (boolean)((NvM_CurrentBlockInfo_t.Descriptor_pt->RamBlockDataAddr_t != NULL_PTR) &&
; ..\eeprom\NvM\NvM_Qry.c	   884        ((NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 & NVM_STATE_VALID_SET) != 0u)
; ..\eeprom\NvM\NvM_Qry.c	   885        && ((NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 & NVM_LOCK_STAT_SET) == 0u));
	mov	d2,#0
	fcall	.cocofun_2
.L458:
	ld.w	d15,[a2]
.L459:
	jeq	d15,#0,.L34
.L460:
	ld.a	a15,[a15]4
.L461:
	ld.bu	d15,[a15]2
.L462:
	jz.t	d15:0,.L35
.L463:
	jnz.t	d15:6,.L36
.L464:
	mov	d2,#1
.L36:
.L35:
.L34:

; ..\eeprom\NvM\NvM_Qry.c	   886  }
	ret
.L316:
	
__NvM_QryRamValid_function_end:
	.size	NvM_QryRamValid,__NvM_QryRamValid_function_end-NvM_QryRamValid
.L214:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryHasRom',code,cluster('NvM_QryHasRom')
	.sect	'.text.NvM_Qry.NvM_QryHasRom'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   887  #endif /* (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) */
; ..\eeprom\NvM\NvM_Qry.c	   888  
; ..\eeprom\NvM\NvM_Qry.c	   889  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   890  *  NvM_QryHasRom
; ..\eeprom\NvM\NvM_Qry.c	   891  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   892  /*!
; ..\eeprom\NvM\NvM_Qry.c	   893   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   894   *
; ..\eeprom\NvM\NvM_Qry.c	   895   *
; ..\eeprom\NvM\NvM_Qry.c	   896   */
; ..\eeprom\NvM\NvM_Qry.c	   897  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryHasRom(void)
; Function NvM_QryHasRom
.L104:
NvM_QryHasRom:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   898  {
; ..\eeprom\NvM\NvM_Qry.c	   899    return (boolean)((NvM_CurrentBlockInfo_t.Descriptor_pt->RomBlockDataAddr_pt != NULL_PTR) ||
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t)
.L508:
	ld.a	a4,[a15]@los(NvM_CurrentBlockInfo_t)
.L509:
	mov	d8,#0
.L510:
	ld.w	d15,[a4]4
.L511:
	jne	d15,#0,.L38
.L512:

; ..\eeprom\NvM\NvM_Qry.c	   900        (NvM_QryIsInitCallbackConfigured(NvM_CurrentBlockInfo_t.Descriptor_pt) == TRUE)); /* PRQA S 3415 */ /* MD_NvM_13.5 */ /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	call	NvM_QryIsInitCallbackConfigured
.L513:
	jeq	d2,#0,.L39
.L38:
	mov	d8,#1
.L39:

; ..\eeprom\NvM\NvM_Qry.c	   901  }
	mov	d2,d8
	ret
.L326:
	
__NvM_QryHasRom_function_end:
	.size	NvM_QryHasRom,__NvM_QryHasRom_function_end-NvM_QryHasRom
.L249:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryExtRuntime',code,cluster('NvM_QryExtRuntime')
	.sect	'.text.NvM_Qry.NvM_QryExtRuntime'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   902  
; ..\eeprom\NvM\NvM_Qry.c	   903  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   904  *  NvM_QryExtRuntime
; ..\eeprom\NvM\NvM_Qry.c	   905  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   906  /*!
; ..\eeprom\NvM\NvM_Qry.c	   907   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   908   *
; ..\eeprom\NvM\NvM_Qry.c	   909   *
; ..\eeprom\NvM\NvM_Qry.c	   910   *
; ..\eeprom\NvM\NvM_Qry.c	   911   */
; ..\eeprom\NvM\NvM_Qry.c	   912  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryExtRuntime(void)
; Function NvM_QryExtRuntime
.L106:
NvM_QryExtRuntime:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   913  {
; ..\eeprom\NvM\NvM_Qry.c	   914  #if (NVM_DYNAMIC_CONFIGURATION == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   915    return (boolean)(((NvM_CurrentBlockInfo_t.InternalFlags_u8 & NVM_INTFLAG_DYN_MISMATCH_SET) != 0u) &&
	mov	d2,#0
	fcall	.cocofun_4
.L518:
	ld.bu	d15,[a15]42
.L519:
	jz.t	d15:5,.L41
.L520:

; ..\eeprom\NvM\NvM_Qry.c	   916        ((NvM_CurrentBlockInfo_t.Descriptor_pt->Flags_u8 & NVM_RESISTANT_TO_CHANGED_SW_ON) == 0u));
	ld.a	a15,[a15]
.L521:
	ld.bu	d15,[a15]59
.L522:
	jnz.t	d15:3,.L42
.L523:
	mov	d2,#1
.L42:
.L41:

; ..\eeprom\NvM\NvM_Qry.c	   917  #else
; ..\eeprom\NvM\NvM_Qry.c	   918    return (boolean)FALSE;
; ..\eeprom\NvM\NvM_Qry.c	   919  #endif
; ..\eeprom\NvM\NvM_Qry.c	   920  }
	ret
.L327:
	
__NvM_QryExtRuntime_function_end:
	.size	NvM_QryExtRuntime,__NvM_QryExtRuntime_function_end-NvM_QryExtRuntime
.L254:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryCRCCompMechanismSkipWrite',code,cluster('NvM_QryCRCCompMechanismSkipWrite')
	.sect	'.text.NvM_Qry.NvM_QryCRCCompMechanismSkipWrite'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	   921  
; ..\eeprom\NvM\NvM_Qry.c	   922  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	   923  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   924   * NvM_QrySuspendRepairRedundantBlocks
; ..\eeprom\NvM\NvM_Qry.c	   925   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   926  /*!
; ..\eeprom\NvM\NvM_Qry.c	   927   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   928   *
; ..\eeprom\NvM\NvM_Qry.c	   929   *
; ..\eeprom\NvM\NvM_Qry.c	   930   *
; ..\eeprom\NvM\NvM_Qry.c	   931   *
; ..\eeprom\NvM\NvM_Qry.c	   932   */
; ..\eeprom\NvM\NvM_Qry.c	   933  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QrySuspendRepairRedundantBlocks(void)
; ..\eeprom\NvM\NvM_Qry.c	   934  {
; ..\eeprom\NvM\NvM_Qry.c	   935    /* we do not have to check the high priority job here - it is done within NvM processing */
; ..\eeprom\NvM\NvM_Qry.c	   936    return (
; ..\eeprom\NvM\NvM_Qry.c	   937  #if (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Qry.c	   938        (NvM_QryNormalPrioJob()) ||
; ..\eeprom\NvM\NvM_Qry.c	   939  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Qry.c	   940        ((NvM_ApiFlags_u8 & NVM_APIFLAG_READ_ALL_SET) != 0u) ||
; ..\eeprom\NvM\NvM_Qry.c	   941        ((NvM_ApiFlags_u8 & NVM_APIFLAG_WRITE_ALL_SET) != 0u));
; ..\eeprom\NvM\NvM_Qry.c	   942  }
; ..\eeprom\NvM\NvM_Qry.c	   943  
; ..\eeprom\NvM\NvM_Qry.c	   944  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   945   * NvM_QryRepairRedBlockDefect
; ..\eeprom\NvM\NvM_Qry.c	   946   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   947  /*!
; ..\eeprom\NvM\NvM_Qry.c	   948   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   949   *
; ..\eeprom\NvM\NvM_Qry.c	   950   *
; ..\eeprom\NvM\NvM_Qry.c	   951   *
; ..\eeprom\NvM\NvM_Qry.c	   952   *
; ..\eeprom\NvM\NvM_Qry.c	   953   *
; ..\eeprom\NvM\NvM_Qry.c	   954   */
; ..\eeprom\NvM\NvM_Qry.c	   955  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryRepairRedBlockDefect(void)
; ..\eeprom\NvM\NvM_Qry.c	   956  {
; ..\eeprom\NvM\NvM_Qry.c	   957    boolean firstBlockOk = ((NvM_RepairRedBlockState.NvBlockState & 0x0Fu) == NVM_NVBLOCK_STATE_UPTODATE);
; ..\eeprom\NvM\NvM_Qry.c	   958    boolean secondBlockOk = (((NvM_RepairRedBlockState.NvBlockState & 0xF0u) >> ((uint8)4u)) == NVM_NVBLOCK_STATE_UPTODATE);
; ..\eeprom\NvM\NvM_Qry.c	   959  
; ..\eeprom\NvM\NvM_Qry.c	   960    /* #10 block is defect in case one sub-block is already marked as defect, no matter whether the block has a Crc or not */
; ..\eeprom\NvM\NvM_Qry.c	   961    boolean blockDefect = (firstBlockOk != secondBlockOk);
; ..\eeprom\NvM\NvM_Qry.c	   962  
; ..\eeprom\NvM\NvM_Qry.c	   963    /* #20 in case both sub-blocks are valid and the block is configured with Crc */
; ..\eeprom\NvM\NvM_Qry.c	   964    if((firstBlockOk && secondBlockOk) && (NvM_CurrentBlockInfo_t.Descriptor_pt->CrcSettings != NVM_BLOCK_USE_CRC_OFF))
; ..\eeprom\NvM\NvM_Qry.c	   965    {
; ..\eeprom\NvM\NvM_Qry.c	   966      /* #21 check whether the Crc values fit to each other - block is defect in case the Crcs differ! */
; ..\eeprom\NvM\NvM_Qry.c	   967      blockDefect = (boolean)(NvM_RepairRedBlockState.CrcBuffer != NvM_CurrentBlockInfo_t.BlockCrcJob_t.CurrentCrcValue);
; ..\eeprom\NvM\NvM_Qry.c	   968  
; ..\eeprom\NvM\NvM_Qry.c	   969      /* #22 for different Crcs we assume the second block is defect - NvM shall repair it (overwrite with first block data) */
; ..\eeprom\NvM\NvM_Qry.c	   970      if(blockDefect == TRUE)
; ..\eeprom\NvM\NvM_Qry.c	   971      {
; ..\eeprom\NvM\NvM_Qry.c	   972        NvM_RepairRedBlockState.NvBlockState |= (NVM_NVBLOCK_STATE_DEFECT << 4);
; ..\eeprom\NvM\NvM_Qry.c	   973      }
; ..\eeprom\NvM\NvM_Qry.c	   974    }
; ..\eeprom\NvM\NvM_Qry.c	   975  
; ..\eeprom\NvM\NvM_Qry.c	   976    return blockDefect;
; ..\eeprom\NvM\NvM_Qry.c	   977  }
; ..\eeprom\NvM\NvM_Qry.c	   978  #endif
; ..\eeprom\NvM\NvM_Qry.c	   979  
; ..\eeprom\NvM\NvM_Qry.c	   980  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	   981   * NvM_QryCRCCompMechanismSkipWrite
; ..\eeprom\NvM\NvM_Qry.c	   982   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	   983  /*!
; ..\eeprom\NvM\NvM_Qry.c	   984   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	   985   *
; ..\eeprom\NvM\NvM_Qry.c	   986   *
; ..\eeprom\NvM\NvM_Qry.c	   987   *
; ..\eeprom\NvM\NvM_Qry.c	   988   *
; ..\eeprom\NvM\NvM_Qry.c	   989   *
; ..\eeprom\NvM\NvM_Qry.c	   990   */
; ..\eeprom\NvM\NvM_Qry.c	   991  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCRCCompMechanismSkipWrite(void)
; Function NvM_QryCRCCompMechanismSkipWrite
.L108:
NvM_QryCRCCompMechanismSkipWrite:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	   992  {
; ..\eeprom\NvM\NvM_Qry.c	   993    boolean skipWrite = FALSE;
; ..\eeprom\NvM\NvM_Qry.c	   994  
; ..\eeprom\NvM\NvM_Qry.c	   995    if(NvM_CurrentBlockInfo_t.Descriptor_pt->CRCCompMechanismCrcAddr_t != NULL_PTR)
	movh.a	a2,#@his(NvM_CurrentBlockInfo_t)
	lea	a2,[a2]@los(NvM_CurrentBlockInfo_t)
.L528:
	ld.a	a15,[a2]
.L529:
	mov	d2,#0
.L337:
	ld.a	a5,[a15]44
.L530:
	jz.a	a5,.L44
.L531:

; ..\eeprom\NvM\NvM_Qry.c	   996    {
; ..\eeprom\NvM\NvM_Qry.c	   997      NvM_CrcJob_ImportBufferedValue(
; ..\eeprom\NvM\NvM_Qry.c	   998          &NvM_CurrentBlockInfo_t.BlockCrcJob_t,
	lea	a15,[a2]16
.L532:

; ..\eeprom\NvM\NvM_Qry.c	   999          NvM_CurrentBlockInfo_t.Descriptor_pt->CRCCompMechanismCrcAddr_t); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	mov.aa	a4,a15
	call	NvM_CrcJob_ImportBufferedValue
.L338:

; ..\eeprom\NvM\NvM_Qry.c	  1000  
; ..\eeprom\NvM\NvM_Qry.c	  1001      skipWrite = NvM_CrcJob_Compare(&NvM_CurrentBlockInfo_t.BlockCrcJob_t); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	mov.aa	a4,a15
	j	NvM_CrcJob_Compare
.L44:

; ..\eeprom\NvM\NvM_Qry.c	  1002    }
; ..\eeprom\NvM\NvM_Qry.c	  1003  
; ..\eeprom\NvM\NvM_Qry.c	  1004    return skipWrite;
; ..\eeprom\NvM\NvM_Qry.c	  1005  }
	ret
.L328:
	
__NvM_QryCRCCompMechanismSkipWrite_function_end:
	.size	NvM_QryCRCCompMechanismSkipWrite,__NvM_QryCRCCompMechanismSkipWrite_function_end-NvM_QryCRCCompMechanismSkipWrite
.L259:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryPostReadTransform',code,cluster('NvM_QryPostReadTransform')
	.sect	'.text.NvM_Qry.NvM_QryPostReadTransform'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	  1006  
; ..\eeprom\NvM\NvM_Qry.c	  1007  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1008   * NvM_QryPostReadTransform
; ..\eeprom\NvM\NvM_Qry.c	  1009   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1010  /*!
; ..\eeprom\NvM\NvM_Qry.c	  1011   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	  1012   *
; ..\eeprom\NvM\NvM_Qry.c	  1013   *
; ..\eeprom\NvM\NvM_Qry.c	  1014   *
; ..\eeprom\NvM\NvM_Qry.c	  1015   */
; ..\eeprom\NvM\NvM_Qry.c	  1016  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryPostReadTransform(void)
; Function NvM_QryPostReadTransform
.L110:
NvM_QryPostReadTransform:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	  1017  {
; ..\eeprom\NvM\NvM_Qry.c	  1018    boolean retVal = TRUE;
; ..\eeprom\NvM\NvM_Qry.c	  1019  
; ..\eeprom\NvM\NvM_Qry.c	  1020    if(NvM_CurrentBlockInfo_t.Descriptor_pt->CbkPostReadTransform != NULL_PTR)
	mov	d2,#1
	fcall	.cocofun_2
.L537:
	ld.a	a5,[a2]36
.L538:
	jz.a	a5,.L46
.L539:

; ..\eeprom\NvM\NvM_Qry.c	  1021    {
; ..\eeprom\NvM\NvM_Qry.c	  1022      retVal = (NvM_CurrentBlockInfo_t.Descriptor_pt->CbkPostReadTransform(
; ..\eeprom\NvM\NvM_Qry.c	  1023          NvM_CurrentJob_t.JobBlockId_t, /* SBSW_NvM_FuncPtrCall_UserCallbacks */
	movh.a	a4,#@his(NvM_CurrentJob_t)
.L540:
	ld.hu	d4,[a4]@los(NvM_CurrentJob_t)
.L541:

; ..\eeprom\NvM\NvM_Qry.c	  1024          NvM_CurrentBlockInfo_t.RamAddr_t, /* PRQA S 0315 */ /* MD_NvM_Dir1.1_CastToVoidPtr */
	ld.a	a4,[a15]8
.L542:

; ..\eeprom\NvM\NvM_Qry.c	  1025          NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16) == E_OK);
	ld.hu	d5,[a2]50
	calli	a5
.L341:
	eq	d2,d2,#0
.L46:

; ..\eeprom\NvM\NvM_Qry.c	  1026    }
; ..\eeprom\NvM\NvM_Qry.c	  1027  
; ..\eeprom\NvM\NvM_Qry.c	  1028    return retVal;
; ..\eeprom\NvM\NvM_Qry.c	  1029  }
	ret
.L330:
	
__NvM_QryPostReadTransform_function_end:
	.size	NvM_QryPostReadTransform,__NvM_QryPostReadTransform_function_end-NvM_QryPostReadTransform
.L264:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryReadAllKilled',code,cluster('NvM_QryReadAllKilled')
	.sect	'.text.NvM_Qry.NvM_QryReadAllKilled'
	.align	2
	
	.global	NvM_QryReadAllKilled

; ..\eeprom\NvM\NvM_Qry.c	  1030  
; ..\eeprom\NvM\NvM_Qry.c	  1031  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1032   *  GLOBAL FUNCTIONS
; ..\eeprom\NvM\NvM_Qry.c	  1033   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1034  
; ..\eeprom\NvM\NvM_Qry.c	  1035  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1036   *  NvM_QryReadAllKilled
; ..\eeprom\NvM\NvM_Qry.c	  1037   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1038  /*!
; ..\eeprom\NvM\NvM_Qry.c	  1039   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	  1040   *
; ..\eeprom\NvM\NvM_Qry.c	  1041   *
; ..\eeprom\NvM\NvM_Qry.c	  1042   */
; ..\eeprom\NvM\NvM_Qry.c	  1043  FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryReadAllKilled(void)
; Function NvM_QryReadAllKilled
.L112:
NvM_QryReadAllKilled:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	  1044  {
; ..\eeprom\NvM\NvM_Qry.c	  1045    return ((NvM_ApiFlags_u8 & NVM_APIFLAG_KILL_READ_ALL_SET) == NVM_APIFLAG_KILL_READ_ALL_SET);
	fcall	.cocofun_1
.L350:
	and	d15,#64
.L351:

; ..\eeprom\NvM\NvM_Qry.c	  1046  }
	ne	d2,d15,#0
	ret
.L293:
	
__NvM_QryReadAllKilled_function_end:
	.size	NvM_QryReadAllKilled,__NvM_QryReadAllKilled_function_end-NvM_QryReadAllKilled
.L129:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryIsInitCallbackConfigured',code,cluster('NvM_QryIsInitCallbackConfigured')
	.sect	'.text.NvM_Qry.NvM_QryIsInitCallbackConfigured'
	.align	2
	
	.global	NvM_QryIsInitCallbackConfigured

; ..\eeprom\NvM\NvM_Qry.c	  1047  
; ..\eeprom\NvM\NvM_Qry.c	  1048  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1049  *  NvM_QryIsInitCallbackConfigured
; ..\eeprom\NvM\NvM_Qry.c	  1050  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1051  /*!
; ..\eeprom\NvM\NvM_Qry.c	  1052   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	  1053   *
; ..\eeprom\NvM\NvM_Qry.c	  1054   *
; ..\eeprom\NvM\NvM_Qry.c	  1055   */
; ..\eeprom\NvM\NvM_Qry.c	  1056  FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryIsInitCallbackConfigured(NvM_BlockDescrPtrType BlockDescriptor)
; Function NvM_QryIsInitCallbackConfigured
.L114:
NvM_QryIsInitCallbackConfigured:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	  1057  {
; ..\eeprom\NvM\NvM_Qry.c	  1058    return ((BlockDescriptor->InitCbkFunc_pt != NULL_PTR) || (BlockDescriptor->InitCbkExtFunc_pt != NULL_PTR));
	ld.w	d15,[a4]8
.L356:
	mov	d2,#0
.L357:
	jne	d15,#0,.L49
.L358:
	ld.w	d15,[a4]12
.L359:
	jeq	d15,#0,.L50
.L49:
	mov	d2,#1
.L50:

; ..\eeprom\NvM\NvM_Qry.c	  1059  }
	ret
.L294:
	
__NvM_QryIsInitCallbackConfigured_function_end:
	.size	NvM_QryIsInitCallbackConfigured,__NvM_QryIsInitCallbackConfigured_function_end-NvM_QryIsInitCallbackConfigured
.L134:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QrySyncDecrypt',code,cluster('NvM_QrySyncDecrypt')
	.sect	'.text.NvM_Qry.NvM_QrySyncDecrypt'
	.align	2
	
	.global	NvM_QrySyncDecrypt

; ..\eeprom\NvM\NvM_Qry.c	  1060  
; ..\eeprom\NvM\NvM_Qry.c	  1061  #if (NVM_USE_CSM == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	  1062  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1063   * NvM_QryIsCipherBlock
; ..\eeprom\NvM\NvM_Qry.c	  1064   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1065  /*!
; ..\eeprom\NvM\NvM_Qry.c	  1066   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	  1067   *
; ..\eeprom\NvM\NvM_Qry.c	  1068   *
; ..\eeprom\NvM\NvM_Qry.c	  1069   */
; ..\eeprom\NvM\NvM_Qry.c	  1070  FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryIsCipherBlock(NvM_BlockDescrPtrType BlockDescriptor)
; ..\eeprom\NvM\NvM_Qry.c	  1071  {
; ..\eeprom\NvM\NvM_Qry.c	  1072    return (BlockDescriptor->NvCryptoReference < NVM_NR_OF_CSM_JOBS);
; ..\eeprom\NvM\NvM_Qry.c	  1073  }
; ..\eeprom\NvM\NvM_Qry.c	  1074  #endif /* (NVM_USE_CSM == STD_ON) */
; ..\eeprom\NvM\NvM_Qry.c	  1075  
; ..\eeprom\NvM\NvM_Qry.c	  1076  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1077   * NvM_QrySyncDecrypt
; ..\eeprom\NvM\NvM_Qry.c	  1078   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1079  /*!
; ..\eeprom\NvM\NvM_Qry.c	  1080   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	  1081   *
; ..\eeprom\NvM\NvM_Qry.c	  1082   *
; ..\eeprom\NvM\NvM_Qry.c	  1083   *
; ..\eeprom\NvM\NvM_Qry.c	  1084   *
; ..\eeprom\NvM\NvM_Qry.c	  1085   *
; ..\eeprom\NvM\NvM_Qry.c	  1086   *
; ..\eeprom\NvM\NvM_Qry.c	  1087   *
; ..\eeprom\NvM\NvM_Qry.c	  1088   *
; ..\eeprom\NvM\NvM_Qry.c	  1089   */
; ..\eeprom\NvM\NvM_Qry.c	  1090  FUNC(boolean, NVM_PRIVATE_CODE) NvM_QrySyncDecrypt(void)
; Function NvM_QrySyncDecrypt
.L116:
NvM_QrySyncDecrypt:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	  1091  {
; ..\eeprom\NvM\NvM_Qry.c	  1092    /* #100 CSM enabled and the current block has a CSM job reference (must be decrypted). */
; ..\eeprom\NvM\NvM_Qry.c	  1093  #if (NVM_USE_CSM == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	  1094      boolean retVal = FALSE;
; ..\eeprom\NvM\NvM_Qry.c	  1095      if(NvM_QryIsCipherBlock(NvM_CurrentBlockInfo_t.Descriptor_pt) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Qry.c	  1096      {
; ..\eeprom\NvM\NvM_Qry.c	  1097        /* #110 Decrypt data and check the result length: only in case the CSM returns E_OK and the length matches
; ..\eeprom\NvM\NvM_Qry.c	  1098         * the configured user data length, the decryption is successful! */
; ..\eeprom\NvM\NvM_Qry.c	  1099        /* Initialize with the expected length for test purposes only, real CSM will overwrite the value with
; ..\eeprom\NvM\NvM_Qry.c	  1100         * actual length of the decrypted data. */
; ..\eeprom\NvM\NvM_Qry.c	  1101        uint32 decryptResultLength = NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16;
; ..\eeprom\NvM\NvM_Qry.c	  1102  
; ..\eeprom\NvM\NvM_Qry.c	  1103        NvM_CurrentBlockInfo_t.CsmJobReturnValue_u8 = Csm_Decrypt(NvM_CsmJobs[NvM_CurrentBlockInfo_t.Descriptor_pt->NvCryptoReference].DecryptJobId, NVM_CSM_OP_MODE, /* SBSW_NvM_FuncCall_PtrParam_Csm */
; ..\eeprom\NvM\NvM_Qry.c	  1104            /* Do not add the CRC length here!!! the NVRAMDataLength stores the length of the ciphered data! */
; ..\eeprom\NvM\NvM_Qry.c	  1105            NvM_CurrentBlockInfo_t.NvRamAddr_t, NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockNVRAMDataLength,
; ..\eeprom\NvM\NvM_Qry.c	  1106            NvM_CurrentBlockInfo_t.RamAddr_t, &decryptResultLength);
; ..\eeprom\NvM\NvM_Qry.c	  1107  
; ..\eeprom\NvM\NvM_Qry.c	  1108        /* #120 Update counter for every CSM job set up */
; ..\eeprom\NvM\NvM_Qry.c	  1109        NvM_CurrentBlockInfo_t.CsmJobRetryCounter_u8++;
; ..\eeprom\NvM\NvM_Qry.c	  1110  
; ..\eeprom\NvM\NvM_Qry.c	  1111        if(NvM_CurrentBlockInfo_t.CsmJobReturnValue_u8 == E_OK)
; ..\eeprom\NvM\NvM_Qry.c	  1112        {
; ..\eeprom\NvM\NvM_Qry.c	  1113            if(decryptResultLength == NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16)
; ..\eeprom\NvM\NvM_Qry.c	  1114            {
; ..\eeprom\NvM\NvM_Qry.c	  1115                /* #121 Successful decryption, user buffer holds the encrypted data. */
; ..\eeprom\NvM\NvM_Qry.c	  1116                retVal = TRUE;
; ..\eeprom\NvM\NvM_Qry.c	  1117            }
; ..\eeprom\NvM\NvM_Qry.c	  1118        }
; ..\eeprom\NvM\NvM_Qry.c	  1119      }
; ..\eeprom\NvM\NvM_Qry.c	  1120      /* #200 CSM disabled, or the passed block does not have any CSM job reference (shall not be decrypted) -> return TRUE,
; ..\eeprom\NvM\NvM_Qry.c	  1121       * everything is fine, not touching the data. */
; ..\eeprom\NvM\NvM_Qry.c	  1122      else
; ..\eeprom\NvM\NvM_Qry.c	  1123      {
; ..\eeprom\NvM\NvM_Qry.c	  1124          retVal = TRUE;
; ..\eeprom\NvM\NvM_Qry.c	  1125      }
; ..\eeprom\NvM\NvM_Qry.c	  1126      return retVal;
; ..\eeprom\NvM\NvM_Qry.c	  1127  #else
; ..\eeprom\NvM\NvM_Qry.c	  1128      return TRUE;
; ..\eeprom\NvM\NvM_Qry.c	  1129  #endif
; ..\eeprom\NvM\NvM_Qry.c	  1130  }
	mov	d2,#1
	ret
.L297:
	
__NvM_QrySyncDecrypt_function_end:
	.size	NvM_QrySyncDecrypt,__NvM_QrySyncDecrypt_function_end-NvM_QrySyncDecrypt
.L139:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QrySyncEncrypt',code,cluster('NvM_QrySyncEncrypt')
	.sect	'.text.NvM_Qry.NvM_QrySyncEncrypt'
	.align	2
	
	.global	NvM_QrySyncEncrypt

; ..\eeprom\NvM\NvM_Qry.c	  1131  
; ..\eeprom\NvM\NvM_Qry.c	  1132  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1133   * NvM_QrySyncEncrypt
; ..\eeprom\NvM\NvM_Qry.c	  1134   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1135  /*!
; ..\eeprom\NvM\NvM_Qry.c	  1136   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	  1137   *
; ..\eeprom\NvM\NvM_Qry.c	  1138   *
; ..\eeprom\NvM\NvM_Qry.c	  1139   *
; ..\eeprom\NvM\NvM_Qry.c	  1140   *
; ..\eeprom\NvM\NvM_Qry.c	  1141   *
; ..\eeprom\NvM\NvM_Qry.c	  1142   *
; ..\eeprom\NvM\NvM_Qry.c	  1143   *
; ..\eeprom\NvM\NvM_Qry.c	  1144   *
; ..\eeprom\NvM\NvM_Qry.c	  1145   */
; ..\eeprom\NvM\NvM_Qry.c	  1146  FUNC(boolean, NVM_PRIVATE_CODE) NvM_QrySyncEncrypt(void)
; Function NvM_QrySyncEncrypt
.L118:
NvM_QrySyncEncrypt:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	  1147  {
; ..\eeprom\NvM\NvM_Qry.c	  1148    /* #100 CSM enabled and the current block has a CSM job reference (must be encrypted). */
; ..\eeprom\NvM\NvM_Qry.c	  1149  #if (NVM_USE_CSM == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	  1150    boolean retVal = FALSE;
; ..\eeprom\NvM\NvM_Qry.c	  1151    if(NvM_QryIsCipherBlock(NvM_CurrentBlockInfo_t.Descriptor_pt) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Qry.c	  1152    {
; ..\eeprom\NvM\NvM_Qry.c	  1153      /* #110 Encrypt data and check the result length: only in case the CSM returns E_OK and the length matches
; ..\eeprom\NvM\NvM_Qry.c	  1154       * the configured NV RAM data length, the encryption is successful! */
; ..\eeprom\NvM\NvM_Qry.c	  1155      /* Initialize with the expected length for test purposes only, real CSM will overwrite the value with
; ..\eeprom\NvM\NvM_Qry.c	  1156       * actual length of the encrypted data. */
; ..\eeprom\NvM\NvM_Qry.c	  1157      uint32 encryptResultLength = NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockNVRAMDataLength;
; ..\eeprom\NvM\NvM_Qry.c	  1158  
; ..\eeprom\NvM\NvM_Qry.c	  1159      NvM_CurrentBlockInfo_t.CsmJobReturnValue_u8 = Csm_Encrypt(NvM_CsmJobs[NvM_CurrentBlockInfo_t.Descriptor_pt->NvCryptoReference].EncryptJobId, NVM_CSM_OP_MODE, /* SBSW_NvM_FuncCall_PtrParam_Csm */
; ..\eeprom\NvM\NvM_Qry.c	  1160              /* Do not add the CRC length here!!! the NvBlockLength stores the number of bytes to cipher! */
; ..\eeprom\NvM\NvM_Qry.c	  1161              NvM_CurrentBlockInfo_t.RamAddr_t, NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16,
; ..\eeprom\NvM\NvM_Qry.c	  1162              NvM_CurrentBlockInfo_t.NvRamAddr_t, &encryptResultLength);
; ..\eeprom\NvM\NvM_Qry.c	  1163  
; ..\eeprom\NvM\NvM_Qry.c	  1164      /* #120 Update counter for every CSM job set up */
; ..\eeprom\NvM\NvM_Qry.c	  1165      NvM_CurrentBlockInfo_t.CsmJobRetryCounter_u8++;
; ..\eeprom\NvM\NvM_Qry.c	  1166  
; ..\eeprom\NvM\NvM_Qry.c	  1167      if(NvM_CurrentBlockInfo_t.CsmJobReturnValue_u8 == E_OK)
; ..\eeprom\NvM\NvM_Qry.c	  1168      {
; ..\eeprom\NvM\NvM_Qry.c	  1169          if(encryptResultLength == NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockNVRAMDataLength)
; ..\eeprom\NvM\NvM_Qry.c	  1170          {
; ..\eeprom\NvM\NvM_Qry.c	  1171              /* #121 Successful encryption, internal cipher data buffer holds the encrypted data. */
; ..\eeprom\NvM\NvM_Qry.c	  1172              retVal = TRUE;
; ..\eeprom\NvM\NvM_Qry.c	  1173          }
; ..\eeprom\NvM\NvM_Qry.c	  1174      }
; ..\eeprom\NvM\NvM_Qry.c	  1175    }
; ..\eeprom\NvM\NvM_Qry.c	  1176    /* #200 CSM disabled, or the passed block does not have any CSM job reference (shall not be encrypted) -> return TRUE,
; ..\eeprom\NvM\NvM_Qry.c	  1177     * everything is fine, not touching the data. */
; ..\eeprom\NvM\NvM_Qry.c	  1178    else
; ..\eeprom\NvM\NvM_Qry.c	  1179    {
; ..\eeprom\NvM\NvM_Qry.c	  1180        retVal = TRUE;
; ..\eeprom\NvM\NvM_Qry.c	  1181    }
; ..\eeprom\NvM\NvM_Qry.c	  1182    return retVal;
; ..\eeprom\NvM\NvM_Qry.c	  1183  #else
; ..\eeprom\NvM\NvM_Qry.c	  1184      return TRUE;
; ..\eeprom\NvM\NvM_Qry.c	  1185  #endif
; ..\eeprom\NvM\NvM_Qry.c	  1186  }
	mov	d2,#1
	ret
.L298:
	
__NvM_QrySyncEncrypt_function_end:
	.size	NvM_QrySyncEncrypt,__NvM_QrySyncEncrypt_function_end-NvM_QrySyncEncrypt
.L144:
	; End of function
	
	.sdecl	'.text.NvM_Qry.NvM_QryCsmRetryNecessary',code,cluster('NvM_QryCsmRetryNecessary')
	.sect	'.text.NvM_Qry.NvM_QryCsmRetryNecessary'
	.align	2
	

; ..\eeprom\NvM\NvM_Qry.c	  1187  
; ..\eeprom\NvM\NvM_Qry.c	  1188  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1189  * NvM_QryCsmRetryNecessary
; ..\eeprom\NvM\NvM_Qry.c	  1190  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1191  /*!
; ..\eeprom\NvM\NvM_Qry.c	  1192   * Internal comment removed.
; ..\eeprom\NvM\NvM_Qry.c	  1193   *
; ..\eeprom\NvM\NvM_Qry.c	  1194   *
; ..\eeprom\NvM\NvM_Qry.c	  1195   *
; ..\eeprom\NvM\NvM_Qry.c	  1196   */
; ..\eeprom\NvM\NvM_Qry.c	  1197  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryCsmRetryNecessary(void)
; Function NvM_QryCsmRetryNecessary
.L120:
NvM_QryCsmRetryNecessary:	.type	func

; ..\eeprom\NvM\NvM_Qry.c	  1198  {
; ..\eeprom\NvM\NvM_Qry.c	  1199    /* #10 CSM enabled and the current block has a CSM job reference*/
; ..\eeprom\NvM\NvM_Qry.c	  1200  #if (NVM_USE_CSM == STD_ON)
; ..\eeprom\NvM\NvM_Qry.c	  1201      /* the initial try is also counted as retry. Therefore the total number of
; ..\eeprom\NvM\NvM_Qry.c	  1202         CSM retries is incremented. */
; ..\eeprom\NvM\NvM_Qry.c	  1203      return (boolean)(
; ..\eeprom\NvM\NvM_Qry.c	  1204          ((NvM_CurrentBlockInfo_t.CsmJobReturnValue_u8 == CRYPTO_E_BUSY)
; ..\eeprom\NvM\NvM_Qry.c	  1205              || (NvM_CurrentBlockInfo_t.CsmJobReturnValue_u8 == CRYPTO_E_QUEUE_FULL)
; ..\eeprom\NvM\NvM_Qry.c	  1206          )
; ..\eeprom\NvM\NvM_Qry.c	  1207          && (NvM_CurrentBlockInfo_t.CsmJobRetryCounter_u8 < (NVM_CSM_RETRY_COUNT + 1u))); /* COV_NVM_CSM_RETRY_ZERO */
; ..\eeprom\NvM\NvM_Qry.c	  1208  #else
; ..\eeprom\NvM\NvM_Qry.c	  1209      return FALSE;
; ..\eeprom\NvM\NvM_Qry.c	  1210  #endif
; ..\eeprom\NvM\NvM_Qry.c	  1211  }
	mov	d2,#0
	ret
.L332:
	
__NvM_QryCsmRetryNecessary_function_end:
	.size	NvM_QryCsmRetryNecessary,__NvM_QryCsmRetryNecessary_function_end-NvM_QryCsmRetryNecessary
.L269:
	; End of function
	
	.sdecl	'.rodata.NvM_Qry.NvM_QueryTable_ap',data,rom,cluster('NvM_QueryTable_ap')
	.sect	'.rodata.NvM_Qry.NvM_QueryTable_ap'
	.global	NvM_QueryTable_ap
	.align	4
NvM_QueryTable_ap:	.type	object
	.size	NvM_QueryTable_ap,116
	.word	NvM_QryBlockWriteAll,NvM_QryCancelWriteAll,NvM_QryWriteAllKilled,NvM_QryCrcBusy,NvM_QryDataCopyBusy,NvM_QryCrcMatch,NvM_QryLastBlockDone_ReadAll,NvM_QryLastBlockDone_WriteAll
	.word	NvM_QryLastResultOk,NvM_QryMainFsmRunning,NvM_QryMultiJob,NvM_QryNormalPrioJob,NvM_QryNvBusy,NvM_QryMemHwaBusy,NvM_QryRamValid,NvM_QryRedundantBlock
	.word	NvM_QrySkipBlock,NvM_QrySubFsmRunning,NvM_QryWriteBlockOnce,NvM_QryWriteRetriesExceeded,NvM_QryHasRom,NvM_QryExtRuntime,NvM_QryCRCCompMechanismSkipWrite,NvM_QryPostReadTransform
	.word	NvM_QryReadAllKilled,NvM_QrySyncDecrypt,NvM_QrySyncEncrypt,NvM_QryCsmRetryNecessary
	.word	NvM_QryTrue
	.calls	'__INDIRECT__','NvM_QryReadAllKilled'
	.calls	'__INDIRECT__','NvM_QrySyncDecrypt'
	.calls	'__INDIRECT__','NvM_QrySyncEncrypt'
	.calls	'__INDIRECT__','NvM_QryNormalPrioJob'
	.calls	'__INDIRECT__','NvM_QryBlockWriteAll'
	.calls	'__INDIRECT__','NvM_QryCancelWriteAll'
	.calls	'__INDIRECT__','NvM_QryWriteAllKilled'
	.calls	'__INDIRECT__','NvM_QryCrcMatch'
	.calls	'__INDIRECT__','NvM_QryCrcBusy'
	.calls	'__INDIRECT__','NvM_QryDataCopyBusy'
	.calls	'__INDIRECT__','NvM_QryLastBlockDone_ReadAll'
	.calls	'__INDIRECT__','NvM_QryLastBlockDone_WriteAll'
	.calls	'__INDIRECT__','NvM_QryLastResultOk'
	.calls	'__INDIRECT__','NvM_QryMainFsmRunning'
	.calls	'__INDIRECT__','NvM_QryMultiJob'
	.calls	'__INDIRECT__','NvM_QryNvBusy'
	.calls	'__INDIRECT__','NvM_QryMemHwaBusy'
	.calls	'__INDIRECT__','NvM_QryRamValid'
	.calls	'__INDIRECT__','NvM_QryRedundantBlock'
	.calls	'__INDIRECT__','NvM_QrySkipBlock'
	.calls	'__INDIRECT__','NvM_QrySubFsmRunning'
	.calls	'__INDIRECT__','NvM_QryTrue'
	.calls	'__INDIRECT__','NvM_QryWriteBlockOnce'
	.calls	'__INDIRECT__','NvM_QryWriteRetriesExceeded'
	.calls	'__INDIRECT__','NvM_QryHasRom'
	.calls	'__INDIRECT__','NvM_QryExtRuntime'
	.calls	'__INDIRECT__','NvM_QryCRCCompMechanismSkipWrite'
	.calls	'__INDIRECT__','NvM_QryPostReadTransform'
	.calls	'__INDIRECT__','NvM_QryCsmRetryNecessary'
	.calls	'NvM_QryCrcMatch','NvM_CrcJob_Compare'
	.calls	'NvM_QryMemHwaBusy','MemIf_GetStatus'
	.calls	'NvM_QrySkipBlock','NvM_QryHasRom'
	.calls	'NvM_QrySkipBlock','NvM_QryExtRuntime'
	.calls	'NvM_QryWriteBlockOnce','NvM_QryExtRuntime'
	.calls	'NvM_QryHasRom','NvM_QryIsInitCallbackConfigured'
	.calls	'NvM_QryCRCCompMechanismSkipWrite','NvM_CrcJob_ImportBufferedValue'
	.calls	'NvM_QryCRCCompMechanismSkipWrite','NvM_CrcJob_Compare'
	.calls	'NvM_QryPostReadTransform','__INDIRECT__'
	.calls	'NvM_QryBlockWriteAll','.cocofun_4'
	.calls	'NvM_QryCancelWriteAll','.cocofun_1'
	.calls	'NvM_QryWriteAllKilled','.cocofun_1'
	.calls	'NvM_QryMultiJob','.cocofun_1'
	.calls	'NvM_QryRedundantBlock','.cocofun_2'
	.calls	'.cocofun_2','.cocofun_4'
	.calls	'NvM_QrySkipBlock','.cocofun_3'
	.calls	'NvM_QryWriteBlockOnce','.cocofun_3'
	.calls	'NvM_QryRamValid','.cocofun_2'
	.calls	'NvM_QryExtRuntime','.cocofun_4'
	.calls	'NvM_QryPostReadTransform','.cocofun_2'
	.calls	'NvM_QryReadAllKilled','.cocofun_1'
	.calls	'NvM_QryBlockWriteAll','',0
	.calls	'.cocofun_4','',0
	.calls	'NvM_QryCancelWriteAll','',0
	.calls	'.cocofun_1','',0
	.calls	'NvM_QryWriteAllKilled','',0
	.calls	'NvM_QryCrcMatch','',0
	.calls	'NvM_QryCrcBusy','',0
	.calls	'NvM_QryDataCopyBusy','',0
	.calls	'NvM_QryLastBlockDone_ReadAll','',0
	.calls	'NvM_QryLastBlockDone_WriteAll','',0
	.calls	'NvM_QryLastResultOk','',0
	.calls	'NvM_QryMainFsmRunning','',0
	.calls	'NvM_QryMultiJob','',0
	.calls	'NvM_QryNvBusy','',0
	.calls	'NvM_QryMemHwaBusy','',0
	.calls	'NvM_QryRedundantBlock','',0
	.calls	'.cocofun_2','',0
	.calls	'NvM_QrySkipBlock','',0
	.calls	'.cocofun_3','',0
	.calls	'NvM_QrySubFsmRunning','',0
	.calls	'NvM_QryTrue','',0
	.calls	'NvM_QryWriteBlockOnce','',0
	.calls	'NvM_QryWriteRetriesExceeded','',0
	.calls	'NvM_QryRamValid','',0
	.calls	'NvM_QryHasRom','',0
	.calls	'NvM_QryExtRuntime','',0
	.calls	'NvM_QryCRCCompMechanismSkipWrite','',0
	.calls	'NvM_QryPostReadTransform','',0
	.calls	'NvM_QryReadAllKilled','',0
	.calls	'NvM_QryIsInitCallbackConfigured','',0
	.calls	'NvM_QrySyncDecrypt','',0
	.calls	'NvM_QrySyncEncrypt','',0
	.extern	NvM_NoOfBlockIds_t
	.extern	MemIf_GetStatus
	.extern	NvM_CrcJob_Compare
	.extern	NvM_CrcJob_ImportBufferedValue
	.extern	NvM_JobMainState_t
	.extern	NvM_JobSubState_t
	.extern	NvM_ApiFlags_u8
	.extern	NvM_CurrentJob_t
	.extern	NvM_CurrentBlockInfo_t
	.extern	NvM_QryNormalPrioJob
	.extern	__INDIRECT__
	.calls	'NvM_QryCsmRetryNecessary','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L122:
	.word	9693
	.half	3
	.word	.L123
	.byte	4
.L121:
	.byte	1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L124
	.byte	2,2,59,9,1,3
	.byte	'MEMIF_UNINIT',0,0,3
	.byte	'MEMIF_IDLE',0,1,3
	.byte	'MEMIF_BUSY',0,2,3
	.byte	'MEMIF_BUSY_INTERNAL',0,3,0,4
	.byte	'MemIf_GetStatus',0,1,231,1,36
	.word	178
	.byte	1,1,1,1
.L292:
	.byte	5
	.byte	'unsigned char',0,1,8,6
	.byte	'DeviceIndex',0,1,231,1,58
	.word	276
	.byte	0,7,3,196,1,9,64,8
	.word	276
	.byte	9
	.byte	'NvM_RamAddressType',0,3,161,1,48
	.word	321
	.byte	10
	.byte	'RamBlockDataAddr_t',0,4
	.word	326
	.byte	2,35,0,11
	.word	276
	.byte	8
	.word	382
	.byte	9
	.byte	'NvM_RomAddressType',0,3,164,1,50
	.word	387
	.byte	10
	.byte	'RomBlockDataAddr_pt',0,4
	.word	392
	.byte	2,35,4,12
	.word	276
	.byte	1,1,8
	.word	449
	.byte	9
	.byte	'NvM_InitCbkPtrType',0,3,142,1,9
	.word	456
	.byte	10
	.byte	'InitCbkFunc_pt',0,4
	.word	461
	.byte	2,35,8,13
	.word	276
	.byte	1,1,5
	.byte	'unsigned short int',0,2,7,14
	.word	520
	.byte	15
	.byte	'void',0,8
	.word	547
	.byte	14
	.word	553
	.byte	14
	.word	520
	.byte	0,8
	.word	513
	.byte	9
	.byte	'NvM_InitCbkExtPtrType',0,3,145,1,9
	.word	569
	.byte	10
	.byte	'InitCbkExtFunc_pt',0,4
	.word	574
	.byte	2,35,12,13
	.word	276
	.byte	1,1,14
	.word	276
	.byte	14
	.word	276
	.byte	0,8
	.word	632
	.byte	9
	.byte	'NvM_JobEndCbkPtrType',0,3,134,1,9
	.word	650
	.byte	10
	.byte	'JobEndCbkFunc_pt',0,4
	.word	655
	.byte	2,35,16,13
	.word	276
	.byte	1,1,14
	.word	520
	.byte	14
	.word	276
	.byte	14
	.word	276
	.byte	0,8
	.word	711
	.byte	9
	.byte	'NvM_JobEndCbkExtPtrType',0,3,137,1,9
	.word	734
	.byte	10
	.byte	'JobEndCbkExtFunc_pt',0,4
	.word	739
	.byte	2,35,20,13
	.word	276
	.byte	1,1,11
	.word	547
	.byte	8
	.word	808
	.byte	14
	.word	813
	.byte	0,8
	.word	801
	.byte	9
	.byte	'NvM_ReadRamFromNvMCbkPtrType',0,3,149,1,9
	.word	824
	.byte	10
	.byte	'CbkGetMirrorFunc_pt',0,4
	.word	829
	.byte	2,35,24,13
	.word	276
	.byte	1,1,14
	.word	553
	.byte	0,8
	.word	896
	.byte	9
	.byte	'NvM_WriteRamToNvMCbkPtrType',0,3,148,1,9
	.word	909
	.byte	10
	.byte	'CbkSetMirrorFunc_pt',0,4
	.word	914
	.byte	2,35,28,16,1,1,14
	.word	520
	.byte	14
	.word	553
	.byte	14
	.word	520
	.byte	0,8
	.word	980
	.byte	9
	.byte	'NvM_PreWriteTransformCbkPtrType',0,3,152,1,9
	.word	999
	.byte	10
	.byte	'CbkPreWriteTransform',0,4
	.word	1004
	.byte	2,35,32,9
	.byte	'NvM_PostReadTransformCbkPtrType',0,3,153,1,9
	.word	569
	.byte	10
	.byte	'CbkPostReadTransform',0,4
	.word	1075
	.byte	2,35,36,9
	.byte	'NvM_RamCrcAddressType',0,3,170,1,51
	.word	321
	.byte	10
	.byte	'RamBlockCrcAddr_t',0,4
	.word	1146
	.byte	2,35,40,10
	.byte	'CRCCompMechanismCrcAddr_t',0,4
	.word	1146
	.byte	2,35,44,10
	.byte	'NvIdentifier_u16',0,2
	.word	520
	.byte	2,35,48,10
	.byte	'NvBlockLength_u16',0,2
	.word	520
	.byte	2,35,50,10
	.byte	'NvCryptoReference',0,1
	.word	276
	.byte	2,35,52,10
	.byte	'NvBlockNVRAMDataLength',0,2
	.word	520
	.byte	2,35,54,17
	.byte	'NvBlockCount_u8',0,1
	.word	276
	.byte	8,0,2,35,56,17
	.byte	'BlockPrio_u8',0,1
	.word	276
	.byte	8,0,2,35,57,17
	.byte	'DeviceId_u8',0,1
	.word	276
	.byte	4,4,2,35,58,17
	.byte	'MngmtType_t',0,1
	.word	276
	.byte	2,2,2,35,58,17
	.byte	'CrcSettings',0,1
	.word	276
	.byte	2,0,2,35,58,17
	.byte	'Flags_u8',0,1
	.word	276
	.byte	8,0,2,35,59,17
	.byte	'NotifyBswM',0,1
	.word	276
	.byte	1,7,2,35,60,0,11
	.word	315
	.byte	8
	.word	1514
	.byte	8
	.word	276
	.byte	11
	.word	276
	.byte	8
	.word	1529
	.byte	8
	.word	449
	.byte	8
	.word	513
	.byte	8
	.word	632
	.byte	8
	.word	711
	.byte	8
	.word	801
	.byte	8
	.word	896
	.byte	8
	.word	980
	.byte	8
	.word	513
	.byte	8
	.word	276
	.byte	11
	.word	315
	.byte	8
	.word	1584
.L295:
	.byte	9
	.byte	'NvM_BlockDescrPtrType',0,3,240,1,71
	.word	1589
	.byte	4
	.byte	'NvM_CrcJob_Compare',0,4,227,1,40
	.word	276
	.byte	1,1,1,1,18
	.byte	'NvM_CrcJobStruct',0,4,91,16,20,5
	.byte	'unsigned long int',0,4,7,10
	.byte	'CurrentCrcValue',0,4
	.word	1679
	.byte	2,35,0,9
	.byte	'NvM_ConstRamAddressType',0,3,162,1,50
	.word	387
	.byte	10
	.byte	'RamData_pt',0,4
	.word	1725
	.byte	2,35,4,9
	.byte	'NvM_CrcBufferPtrType',0,4,68,59
	.word	321
	.byte	10
	.byte	'CrcBuffer',0,4
	.word	1778
	.byte	2,35,8,18
	.byte	'NvM_CrcHandlerClass',0,4,79,8,20,16,1,1,11
	.word	276
	.byte	8
	.word	1854
	.byte	14
	.word	1859
	.byte	14
	.word	520
	.byte	8
	.word	1679
	.byte	14
	.word	1874
	.byte	0,8
	.word	1851
	.byte	9
	.byte	'NvM_CrcCalculateFPtr',0,4,74,9
	.word	1885
	.byte	10
	.byte	'calc',0,4
	.word	1890
	.byte	2,35,0,13
	.word	276
	.byte	1,1,14
	.word	1859
	.byte	14
	.word	1859
	.byte	0,8
	.word	1933
	.byte	9
	.byte	'NvM_CrcCompareFPtr',0,4,75,9
	.word	1951
	.byte	10
	.byte	'compare',0,4
	.word	1956
	.byte	2,35,4,16,1,1,8
	.word	276
	.byte	14
	.word	2003
	.byte	14
	.word	1859
	.byte	0,8
	.word	2000
	.byte	9
	.byte	'NvM_CrcCopyToBufferFPtr',0,4,76,9
	.word	2019
	.byte	10
	.byte	'copyToBuffer',0,4
	.word	2024
	.byte	2,35,8,10
	.byte	'initialCrcValue',0,4
	.word	1679
	.byte	2,35,12,10
	.byte	'crcLength',0,1
	.word	276
	.byte	2,35,16,0,11
	.word	1826
	.byte	8
	.word	2123
	.byte	9
	.byte	'NvM_CrcHandlerClassConstPtr',0,4,88,75
	.word	2128
	.byte	10
	.byte	'HandlerInstance_pt',0,4
	.word	2133
	.byte	2,35,12,10
	.byte	'RemainingLength_u16',0,2
	.word	520
	.byte	2,35,16,0,11
	.word	1657
	.byte	8
	.word	2227
	.byte	9
	.byte	'NvM_CrcJobConstPtrType',0,4,101,62
	.word	2232
	.byte	6
	.byte	'Self',0,4,227,1,82
	.word	2237
	.byte	0,11
	.word	1657
	.byte	8
	.word	2283
	.byte	11
	.word	276
	.byte	8
	.word	2293
	.byte	8
	.word	276
	.byte	11
	.word	1826
	.byte	8
	.word	2308
	.byte	8
	.word	1851
	.byte	8
	.word	1933
	.byte	8
	.word	2000
	.byte	19
	.byte	'NvM_CrcJob_ImportBufferedValue',0,4,129,2,37,1,1,1,1,6
	.byte	'Self',0,4,129,2,91
	.word	2237
	.byte	9
	.byte	'NvM_CrcBufferConstPtrType',0,4,69,61
	.word	387
	.byte	6
	.byte	'SrcPtr',0,4,129,2,123
	.word	2387
	.byte	0,11
	.word	276
	.byte	8
	.word	2438
	.byte	20
	.byte	'NvM_QryNormalPrioJob',0,5,120,40
	.word	276
	.byte	1,1,1,1
.L314:
	.byte	11
	.word	178
	.byte	21
	.byte	'__INDIRECT__',0,6,1,1,1,1,1,9
	.byte	'__prof_adm',0,6,1,1
	.word	553
	.byte	22,1,8
	.word	2525
	.byte	9
	.byte	'__codeptr',0,6,1,1
	.word	2527
	.byte	9
	.byte	'uint8',0,7,90,29
	.word	276
	.byte	5
	.byte	'short int',0,2,5,9
	.byte	'sint16',0,7,91,29
	.word	2564
	.byte	9
	.byte	'uint16',0,7,92,29
	.word	520
	.byte	9
	.byte	'uint32',0,7,94,29
	.word	1679
	.byte	9
	.byte	'boolean',0,7,105,29
	.word	276
	.byte	5
	.byte	'unsigned long long int',0,8,7,9
	.byte	'uint64',0,7,130,1,30
	.word	2638
	.byte	9
	.byte	'Std_ReturnType',0,8,113,15
	.word	276
	.byte	9
	.byte	'PduLengthType',0,9,76,22
	.word	520
	.byte	9
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,10,112,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,10,115,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,10,118,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,10,121,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,10,124,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,10,136,1,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,10,139,1,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,10,148,1,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,10,151,1,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,10,141,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,10,144,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,10,147,3,16
	.word	520
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,10,150,3,16
	.word	520
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,10,153,3,16
	.word	520
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,10,156,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,10,159,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,10,162,3,16
	.word	520
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,10,165,3,16
	.word	520
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,10,180,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,10,183,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,10,186,3,16
	.word	1679
	.byte	9
	.byte	'IdtAppCom_MainPwrFltRsn',0,10,192,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGMDiags',0,10,195,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGMFltRsn',0,10,198,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGMSts',0,10,201,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGMSwSts',0,10,207,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,10,210,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,10,213,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,10,216,3,16
	.word	520
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,10,219,3,16
	.word	520
	.byte	9
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,10,225,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,10,228,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_RednPwrFltRsn',0,10,231,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,10,234,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_SHWAIndSts',0,10,237,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_SHWASysFltSts',0,10,240,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_SHWASysMsg',0,10,243,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,10,246,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_SHWASysSts',0,10,249,3,15
	.word	276
	.byte	9
	.byte	'IdtAppCom_SHWASysTakeOver',0,10,252,3,15
	.word	276
	.byte	9
	.byte	'NvM_BlockIdType',0,10,227,5,16
	.word	520
	.byte	9
	.byte	'NvM_RequestResultType',0,10,207,6,15
	.word	276
	.byte	9
	.byte	'NvM_ServiceIdType',0,10,231,6,15
	.word	276
	.byte	5
	.byte	'unsigned int',0,4,7,9
	.byte	'Rte_BitType',0,10,230,7,22
	.word	4365
	.byte	11
	.word	520
	.byte	23
	.byte	'NvM_NoOfBlockIds_t',0,11,246,1,40
	.word	4402
	.byte	1,1,9
	.byte	'MemIf_StatusType',0,2,65,3
	.word	178
	.byte	2,2,72,9,1,3
	.byte	'MEMIF_JOB_OK',0,0,3
	.byte	'MEMIF_JOB_FAILED',0,1,3
	.byte	'MEMIF_JOB_PENDING',0,2,3
	.byte	'MEMIF_JOB_CANCELED',0,3,3
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,3
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,9
	.byte	'MemIf_JobResultType',0,2,80,3
	.word	4462
	.byte	13
	.word	276
	.byte	1,1,14
	.word	520
	.byte	14
	.word	520
	.byte	14
	.word	2003
	.byte	14
	.word	520
	.byte	0,8
	.word	4620
	.byte	9
	.byte	'MemIf_ApiReadType',0,12,120,9
	.word	4648
	.byte	13
	.word	276
	.byte	1,1,14
	.word	520
	.byte	14
	.word	2003
	.byte	0,8
	.word	4679
	.byte	9
	.byte	'MemIf_ApiWriteType',0,12,121,9
	.word	4697
	.byte	13
	.word	276
	.byte	1,1,14
	.word	520
	.byte	0,8
	.word	4729
	.byte	9
	.byte	'MemIf_ApiEraseImmediateBlockType',0,12,122,9
	.word	4742
	.byte	9
	.byte	'MemIf_ApiInvalidateBlockType',0,12,123,9
	.word	4742
	.byte	24,1,1,8
	.word	4825
	.byte	9
	.byte	'MemIf_ApiCancelType',0,12,124,9
	.word	4828
	.byte	12
	.word	178
	.byte	1,1,8
	.word	4861
	.byte	9
	.byte	'MemIf_ApiGetStatusType',0,12,125,9
	.word	4868
	.byte	12
	.word	4462
	.byte	1,1,8
	.word	4904
	.byte	9
	.byte	'MemIf_ApiGetJobResultType',0,12,126,9
	.word	4911
	.byte	16,1,1,2,2,88,9,1,3
	.byte	'MEMIF_MODE_SLOW',0,0,3
	.byte	'MEMIF_MODE_FAST',0,1,0,14
	.word	4953
	.byte	0,8
	.word	4950
	.byte	9
	.byte	'MemIf_ApiSetModeType',0,12,127,9
	.word	5001
	.byte	9
	.byte	'NvM_BitFieldType',0,3,113,22
	.word	4365
	.byte	9
	.byte	'NvM_CrcType',0,3,116,26
	.word	4365
	.byte	7,3,124,9,4,10
	.byte	'NvDataIndex_t',0,1
	.word	276
	.byte	2,35,0,10
	.byte	'NvRamErrorStatus_u8',0,1
	.word	276
	.byte	2,35,1,10
	.byte	'NvRamAttributes_u8',0,1
	.word	276
	.byte	2,35,2,0,9
	.byte	'NvM_RamMngmtAreaType',0,3,129,1,3
	.word	5080
	.byte	8
	.word	5080
	.byte	9
	.byte	'NvM_RamMngmtPtrType',0,3,131,1,65
	.word	5196
	.byte	9
	.byte	'NvM_BlockDescriptorType',0,3,223,1,3
	.word	315
	.byte	9
	.byte	'NvM_CsmJobIdType',0,3,231,1,16
	.word	1679
	.byte	2,3,243,1,9,1,3
	.byte	'NVM_INT_FID_WRITE_BLOCK',0,0,3
	.byte	'NVM_INT_FID_READ_BLOCK',0,1,3
	.byte	'NVM_INT_FID_RESTORE_DEFAULTS',0,2,3
	.byte	'NVM_INT_FID_INVALIDATE_NV_BLOCK',0,3,3
	.byte	'NVM_INT_FID_ERASE_BLOCK',0,4,3
	.byte	'NVM_INT_FID_WRITE_ALL',0,5,3
	.byte	'NVM_INT_FID_READ_ALL',0,6,3
	.byte	'NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS',0,7,3
	.byte	'NVM_INT_FID_NO_JOB_PENDING',0,8,0,9
	.byte	'NvM_InternalServiceIdType',0,3,254,1,3
	.word	5289
	.byte	9
	.byte	'NvM_QueueEntryRefType',0,3,129,2,15
	.word	276
	.byte	2,13,42,9,1,3
	.byte	'NVM_ACT_ID_SetInitialAttr',0,0,3
	.byte	'NVM_ACT_ID_InitMainFsm',0,1,3
	.byte	'NVM_ACT_ID_InitBlock',0,2,3
	.byte	'NVM_ACT_ID_InitReadAll',0,3,3
	.byte	'NVM_ACT_ID_InitReadBlockSubFsm',0,4,3
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaultsSubFsm',0,5,3
	.byte	'NVM_ACT_ID_InitWriteAll',0,6,3
	.byte	'NVM_ACT_ID_InitWriteBlock',0,7,3
	.byte	'NVM_ACT_ID_InitWriteBlockFsm',0,8,3
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaults',0,9,3
	.byte	'NVM_ACT_ID_FinishMainJob',0,10,3
	.byte	'NVM_ACT_ID_KillWritAll',0,11,3
	.byte	'NVM_ACT_ID_FinishBlock',0,12,3
	.byte	'NVM_ACT_ID_InitNextBlockReadAll',0,13,3
	.byte	'NVM_ACT_ID_InitNextBlockWriteAll',0,14,3
	.byte	'NVM_ACT_ID_FinishCfgIdCheck',0,15,3
	.byte	'NVM_ACT_ID_FinishReadBlock',0,16,3
	.byte	'NVM_ACT_ID_FinishWriteBlock',0,17,3
	.byte	'NVM_ACT_ID_FinishEraseBlock',0,18,3
	.byte	'NVM_ACT_ID_EraseNvBlock',0,19,3
	.byte	'NVM_ACT_ID_InvalidateNvBlock',0,20,3
	.byte	'NVM_ACT_ID_ProcessCrc',0,21,3
	.byte	'NVM_ACT_ID_WriteNvBlock',0,22,3
	.byte	'NVM_ACT_ID_ReadNvBlock',0,23,3
	.byte	'NVM_ACT_ID_ProcessCrcRead',0,24,3
	.byte	'NVM_ACT_ID_ReadCopyData',0,25,3
	.byte	'NVM_ACT_ID_RestoreRomDefaults',0,26,3
	.byte	'NVM_ACT_ID_FinishRestoreRomDefaults',0,27,3
	.byte	'NVM_ACT_ID_TestBlockBlank',0,28,3
	.byte	'NVM_ACT_ID_ValidateRam',0,29,3
	.byte	'NVM_ACT_ID_SetupRedundant',0,30,3
	.byte	'NVM_ACT_ID_SetupOther',0,31,3
	.byte	'NVM_ACT_ID_UpdateNvState',0,32,3
	.byte	'NVM_ACT_ID_SetReqIntegrityFailed',0,33,3
	.byte	'NVM_ACT_ID_SetReqSkipped',0,34,3
	.byte	'NVM_ACT_ID_SetReqNotOk',0,35,3
	.byte	'NVM_ACT_ID_SetReqOk',0,36,3
	.byte	'NVM_ACT_ID_SetBlockPendingWriteAll',0,37,3
	.byte	'NVM_ACT_ID_CopyNvDataToBuf',0,38,3
	.byte	'NVM_ACT_ID_GetMultiBlockJob',0,39,3
	.byte	'NVM_ACT_ID_CancelNV',0,40,3
	.byte	'NVM_ACT_ID_KillSubFsm',0,41,3
	.byte	'NVM_ACT_ID_FinishReadBlockAndSetSkipped',0,42,3
	.byte	'NVM_ACT_ID_GetNormalPrioJob',0,43,3
	.byte	'NVM_ACT_ID_Wait',0,44,3
	.byte	'NVM_ACT_ID_Nop',0,45,0,9
	.byte	'NvM_StateActionIdType',0,13,110,3
	.word	5618
	.byte	2,14,48,9,1,3
	.byte	'NVM_QRY_ID_BLK_WRITE_ALL',0,0,3
	.byte	'NVM_QRY_ID_CANCEL_WRITE_ALL',0,1,3
	.byte	'NVM_QR_ID_WRITEALL_KILLED',0,2,3
	.byte	'NVM_QRY_ID_CRC_BUSY',0,3,3
	.byte	'NVM_QRY_ID_DATA_COPY_BUSY',0,4,3
	.byte	'NVM_QRY_ID_CRC_MATCH',0,5,3
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_READALL',0,6,3
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_WRITEALL',0,7,3
	.byte	'NVM_QRY_ID_LAST_RESULT_OK',0,8,3
	.byte	'NVM_QRY_ID_MAIN_FSM_RUNNING',0,9,3
	.byte	'NVM_QRY_ID_MULTI_BLK_JOB',0,10,3
	.byte	'NVM_QRY_ID_NORMAL_PRIO_JOB',0,11,3
	.byte	'NVM_QRY_ID_NV_BUSY',0,12,3
	.byte	'NVM_QRY_ID_MEMHWA_BUSY',0,13,3
	.byte	'NVM_QRY_ID_RAM_VALID',0,14,3
	.byte	'NVM_QRY_ID_REDUNDANT_BLOCK',0,15,3
	.byte	'NVM_QRY_ID_SKIP_BLOCK',0,16,3
	.byte	'NVM_QRY_ID_SUB_FSM_RUNNING',0,17,3
	.byte	'NVM_QRY_ID_WRITE_BLOCK_ONCE',0,18,3
	.byte	'NVM_QRY_ID_WRITE_RETRIES_EXCEEDED',0,19,3
	.byte	'NVM_QRY_ID_HAS_ROM',0,20,3
	.byte	'NVM_QRY_ID_EXT_RUNTIME',0,21,3
	.byte	'NvM_QRY_CRC_COMP_MECHANISM_SKIPWRITE',0,22,3
	.byte	'NVM_QRY_POST_READ_TRANSFORM',0,23,3
	.byte	'NVM_QRY_READALL_KILLED',0,24,3
	.byte	'NVM_QRY_SYNCDECRYPT',0,25,3
	.byte	'NVM_QRY_SYNCENCRYPT',0,26,3
	.byte	'NVM_QRY_CSM_RETRIES_NECESSARY',0,27,3
	.byte	'NVM_QRY_ID_TRUE',0,28,0,9
	.byte	'NvM_StateQueryIdType',0,14,93,3
	.word	6966
	.byte	8
	.word	449
	.byte	9
	.byte	'NvM_QryFctPtrType',0,14,102,9
	.word	7799
	.byte	25,116
	.word	7804
	.byte	26,28,0
.L333:
	.byte	11
	.word	7830
	.byte	8
	.word	449
	.byte	9
	.byte	'NvM_CrcJobType',0,4,98,3
	.word	1657
	.byte	7,15,57,9,8,10
	.byte	'JobBlockId_t',0,2
	.word	520
	.byte	2,35,0,10
	.byte	'JobServiceId_t',0,1
	.word	5289
	.byte	2,35,2,10
	.byte	'RamAddr_t',0,4
	.word	326
	.byte	2,35,4,0,9
	.byte	'NvM_JobType',0,15,62,3
	.word	7872
	.byte	7,15,66,9,44,10
	.byte	'Descriptor_pt',0,4
	.word	1594
	.byte	2,35,0,10
	.byte	'Mngmt_pt',0,4
	.word	5201
	.byte	2,35,4,10
	.byte	'RamAddr_t',0,4
	.word	326
	.byte	2,35,8,10
	.byte	'NvRamAddr_t',0,4
	.word	326
	.byte	2,35,12,10
	.byte	'BlockCrcJob_t',0,20
	.word	1657
	.byte	2,35,16,10
	.byte	'NvIdentifier_u16',0,2
	.word	520
	.byte	2,35,36,10
	.byte	'ByteCount_u16',0,2
	.word	520
	.byte	2,35,38,10
	.byte	'LastResult_t',0,1
	.word	276
	.byte	2,35,40,10
	.byte	'WriteRetryCounter_u8',0,1
	.word	276
	.byte	2,35,41,10
	.byte	'InternalFlags_u8',0,1
	.word	276
	.byte	2,35,42,10
	.byte	'NvState_u8',0,1
	.word	276
	.byte	2,35,43,0,9
	.byte	'NvM_BlockInfoType',0,15,83,3
	.word	7963
	.byte	8
	.word	5080
	.byte	2,15,96,9,1,3
	.byte	'NVM_STATE_UNINIT',0,0,3
	.byte	'NVM_STATE_IDLE',0,1,3
	.byte	'NVM_STATE_NORMAL_PRIO_JOB',0,2,3
	.byte	'NVM_STATE_MULTI_BLOCK_JOB',0,3,3
	.byte	'NVM_STATE_READ_READ_DATA',0,4,3
	.byte	'NVM_STATE_READ_DATA_VALIDATION',0,5,3
	.byte	'NVM_STATE_READ_CMP_CRC',0,6,3
	.byte	'NVM_STATE_READ_IMPL_RECOV',0,7,3
	.byte	'NVM_STATE_READ_LOAD_ROM',0,8,3
	.byte	'NVM_STATE_READ_FINALIZE',0,9,3
	.byte	'NVM_STATE_WRITE_INITIAL',0,10,3
	.byte	'NVM_STATE_WRITE_CRCCALC',0,11,3
	.byte	'NVM_STATE_WRITE_CRCCOMPMECHANISM',0,12,3
	.byte	'NVM_STATE_WRITE_TEST_PRI_READ',0,13,3
	.byte	'NVM_STATE_WRITE_TEST_SEC_READ',0,14,3
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_1',0,15,3
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_2',0,16,3
	.byte	'NVM_STATE_RESTORE_LOAD_ROM',0,17,3
	.byte	'NVM_STATE_INVALIDATING_BLOCK',0,18,3
	.byte	'NVM_STATE_ERASE_ERASE_BLOCK',0,19,3
	.byte	'NVM_STATE_READALL_PROC_CONFIG_ID',0,20,3
	.byte	'NVM_STATE_READALL_PROC_RAM_BLOCK',0,21,3
	.byte	'NVM_STATE_READALL_CHK_SKIP',0,22,3
	.byte	'NVM_STATE_READALL_KILLED',0,23,3
	.byte	'NVM_STATE_READALL_WR_ONCE_PROT',0,24,3
	.byte	'NVM_STATE_READALL_CHK_RAM_VALIDITY',0,25,3
	.byte	'NVM_STATE_READALL_READ_NV',0,26,3
	.byte	'NVM_STATE_READALL_LOAD_DEFAULTS',0,27,3
	.byte	'NVM_STATE_READALL_READABILITY_CHECK',0,28,3
	.byte	'NVM_STATE_WRITEALL_PROC_BLOCK',0,29,3
	.byte	'NVM_STATE_WRITEALL_WRITE_FSM',0,30,3
	.byte	'NVM_STATE_WRITEALL_WAIT_MEMHWA',0,31,3
	.byte	'NVM_STATE_FSM_FINISHED',0,32,0,9
	.byte	'NvM_StateIdType',0,15,215,1,3
	.word	8251
	.byte	7,15,228,1,9,2,10
	.byte	'ExitHandler_t',0,1
	.word	5618
	.byte	2,35,0,10
	.byte	'EntryHandler_t',0,1
	.word	5618
	.byte	2,35,1,0,9
	.byte	'NvM_StateChangeActionsType',0,15,232,1,3
	.word	9261
	.byte	7,15,236,1,9,6,25,2
	.word	6966
	.byte	26,1,0,10
	.byte	'Queries_at',0,2
	.word	9357
	.byte	2,35,0,10
	.byte	'Actions_t',0,2
	.word	9261
	.byte	2,35,2,10
	.byte	'NextState_t',0,1
	.word	8251
	.byte	2,35,4,0,9
	.byte	'NvM_StateChangeIfDescrType',0,15,241,1,3
	.word	9351
	.byte	7,15,243,1,9,4,10
	.byte	'Actions_t',0,2
	.word	9261
	.byte	2,35,0,10
	.byte	'NextState_t',0,1
	.word	8251
	.byte	2,35,2,0,9
	.byte	'NvM_StateChangeElseDescrType',0,15,247,1,3
	.word	9463
	.byte	23
	.byte	'NvM_JobMainState_t',0,15,137,2,44
	.word	8251
	.byte	1,1,23
	.byte	'NvM_JobSubState_t',0,15,138,2,44
	.word	8251
	.byte	1,1,23
	.byte	'NvM_ApiFlags_u8',0,15,143,2,34
	.word	276
	.byte	1,1,23
	.byte	'NvM_CurrentJob_t',0,15,160,2,43
	.word	7872
	.byte	1,1,23
	.byte	'NvM_CurrentBlockInfo_t',0,15,161,2,49
	.word	7963
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L123:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,4,1,58,15,59,15,57,15,11,15,0,0,3,40,0,3,8,28,13,0,0,4
	.byte	46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,5,36,0,3,8,11,15,62,15,0,0,6,5,0,3,8,58,15
	.byte	59,15,57,15,73,19,0,0,7,19,1,58,15,59,15,57,15,11,15,0,0,8,15,0,73,19,0,0,9,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,10,13,0,3,8,11,15,73,19,56,9,0,0,11,38,0,73,19,0,0,12,21,0,73,19,54,15,39,12,0,0,13,21,1,73
	.byte	19,54,15,39,12,0,0,14,5,0,73,19,0,0,15,59,0,3,8,0,0,16,21,1,54,15,39,12,0,0,17,13,0,3,8,11,15,73,19,13
	.byte	15,12,15,56,9,0,0,18,19,1,3,8,58,15,59,15,57,15,11,15,0,0,19,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63
	.byte	12,60,12,0,0,20,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,21,46,0,3,8,58,15,59,15,57
	.byte	15,54,15,63,12,60,12,0,0,22,21,0,54,15,0,0,23,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,24,21,0
	.byte	54,15,39,12,0,0,25,1,1,11,15,73,19,0,0,26,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L124:
	.word	.L343-.L342
.L342:
	.half	3
	.word	.L345-.L344
.L344:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	0
	.byte	'MemIf.h',0,1,0,0
	.byte	'MemIf_Types.h',0,1,0,0
	.byte	'_PrivateCfg.h',0,2,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0
	.byte	'Platform_Types.h',0,3,0,0
	.byte	'Std_Types.h',0,3,0,0
	.byte	'ComStack_Types.h',0,3,0,0
	.byte	'Rte_Type.h',0,4,0,0
	.byte	'_Cfg.h',0,2,0,0
	.byte	'_Cfg.h',0,1,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.h',0,0,0,0,0
.L345:
.L343:
	.sdecl	'.debug_info',debug,cluster('NvM_QryReadAllKilled')
	.sect	'.debug_info'
.L125:
	.word	239
	.half	3
	.word	.L126
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L128,.L127
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryReadAllKilled',0,1,147,8,33
	.word	.L292
	.byte	1,1,1
	.word	.L112,.L293,.L111
	.byte	4
	.word	.L112,.L293
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryReadAllKilled')
	.sect	'.debug_abbrev'
.L126:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryReadAllKilled')
	.sect	'.debug_line'
.L127:
	.word	.L347-.L346
.L346:
	.half	3
	.word	.L349-.L348
.L348:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L349:
	.byte	5,12,7,0,5,2
	.word	.L112
	.byte	3,148,8,1,5,28,9
	.half	.L350-.L112
	.byte	1,5,1,9
	.half	.L351-.L350
	.byte	3,1,1,7,9
	.half	.L129-.L351
	.byte	0,1,1
.L347:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryReadAllKilled')
	.sect	'.debug_ranges'
.L128:
	.word	-1,.L112,0,.L129-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryIsInitCallbackConfigured')
	.sect	'.debug_info'
.L130:
	.word	279
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L133,.L132
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryIsInitCallbackConfigured',0,1,160,8,33
	.word	.L292
	.byte	1,1,1
	.word	.L114,.L294,.L113
	.byte	4
	.byte	'BlockDescriptor',0,1,160,8,87
	.word	.L295,.L296
	.byte	5
	.word	.L114,.L294
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryIsInitCallbackConfigured')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryIsInitCallbackConfigured')
	.sect	'.debug_line'
.L132:
	.word	.L353-.L352
.L352:
	.half	3
	.word	.L355-.L354
.L354:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L355:
	.byte	5,27,7,0,5,2
	.word	.L114
	.byte	3,161,8,1,5,57,9
	.half	.L356-.L114
	.byte	1,5,11,9
	.half	.L357-.L356
	.byte	1,5,76,7,9
	.half	.L358-.L357
	.byte	1,5,96,9
	.half	.L359-.L358
	.byte	1,5,57,7,9
	.half	.L49-.L359
	.byte	1,5,1,9
	.half	.L50-.L49
	.byte	3,1,1,7,9
	.half	.L134-.L50
	.byte	0,1,1
.L353:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryIsInitCallbackConfigured')
	.sect	'.debug_ranges'
.L133:
	.word	-1,.L114,0,.L134-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QrySyncDecrypt')
	.sect	'.debug_info'
.L135:
	.word	237
	.half	3
	.word	.L136
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L138,.L137
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QrySyncDecrypt',0,1,194,8,33
	.word	.L292
	.byte	1,1,1
	.word	.L116,.L297,.L115
	.byte	4
	.word	.L116,.L297
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QrySyncDecrypt')
	.sect	'.debug_abbrev'
.L136:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QrySyncDecrypt')
	.sect	'.debug_line'
.L137:
	.word	.L361-.L360
.L360:
	.half	3
	.word	.L363-.L362
.L362:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L363:
	.byte	5,12,7,0,5,2
	.word	.L116
	.byte	3,231,8,1,5,1,3,2,1,7,9
	.half	.L139-.L116
	.byte	0,1,1
.L361:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QrySyncDecrypt')
	.sect	'.debug_ranges'
.L138:
	.word	-1,.L116,0,.L139-.L116,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QrySyncEncrypt')
	.sect	'.debug_info'
.L140:
	.word	237
	.half	3
	.word	.L141
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L143,.L142
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QrySyncEncrypt',0,1,250,8,33
	.word	.L292
	.byte	1,1,1
	.word	.L118,.L298,.L117
	.byte	4
	.word	.L118,.L298
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QrySyncEncrypt')
	.sect	'.debug_abbrev'
.L141:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QrySyncEncrypt')
	.sect	'.debug_line'
.L142:
	.word	.L365-.L364
.L364:
	.half	3
	.word	.L367-.L366
.L366:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L367:
	.byte	5,12,7,0,5,2
	.word	.L118
	.byte	3,159,9,1,5,1,3,2,1,7,9
	.half	.L144-.L118
	.byte	0,1,1
.L365:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QrySyncEncrypt')
	.sect	'.debug_ranges'
.L143:
	.word	-1,.L118,0,.L144-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryBlockWriteAll')
	.sect	'.debug_info'
.L145:
	.word	234
	.half	3
	.word	.L146
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L148,.L147
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryBlockWriteAll',0,1,164,4,43
	.word	.L292
	.byte	1,1
	.word	.L56,.L299,.L55
	.byte	4
	.word	.L300
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryBlockWriteAll')
	.sect	'.debug_abbrev'
.L146:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryBlockWriteAll')
	.sect	'.debug_line'
.L147:
	.word	.L369-.L368
.L368:
	.half	3
	.word	.L371-.L370
.L370:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L371:
	.byte	5,94,7,0,5,2
	.word	.L56
	.byte	3,168,4,1,5,24,3,126,1,5,46,9
	.half	.L372-.L56
	.byte	1,5,55,9
	.half	.L373-.L372
	.byte	1,5,76,9
	.half	.L374-.L373
	.byte	1,5,22,9
	.half	.L375-.L374
	.byte	1,5,37,7,9
	.half	.L376-.L375
	.byte	3,3,1,5,51,9
	.half	.L377-.L376
	.byte	1,5,98,9
	.half	.L378-.L377
	.byte	1,5,94,7,9
	.half	.L379-.L378
	.byte	3,127,1,5,1,9
	.half	.L2-.L379
	.byte	3,7,1,7,9
	.half	.L149-.L2
	.byte	0,1,1
.L369:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryBlockWriteAll')
	.sect	'.debug_ranges'
.L148:
	.word	-1,.L56,0,.L149-.L56,0,0
.L300:
	.word	-1,.L56,0,.L299-.L56,-1,.L58,0,.L289-.L58,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryCancelWriteAll')
	.sect	'.debug_info'
.L150:
	.word	235
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L153,.L152
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryCancelWriteAll',0,1,186,4,43
	.word	.L292
	.byte	1,1
	.word	.L60,.L301,.L59
	.byte	4
	.word	.L302
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryCancelWriteAll')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryCancelWriteAll')
	.sect	'.debug_line'
.L152:
	.word	.L381-.L380
.L380:
	.half	3
	.word	.L383-.L382
.L382:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L383:
	.byte	5,12,7,0,5,2
	.word	.L60
	.byte	3,187,4,1,5,28,9
	.half	.L384-.L60
	.byte	1,5,61,9
	.half	.L385-.L384
	.byte	1,5,1,3,1,1,7,9
	.half	.L154-.L385
	.byte	0,1,1
.L381:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryCancelWriteAll')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L60,0,.L154-.L60,0,0
.L302:
	.word	-1,.L60,0,.L301-.L60,-1,.L62,0,.L274-.L62,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryWriteAllKilled')
	.sect	'.debug_info'
.L155:
	.word	239
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L158,.L157
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryWriteAllKilled',0,1,200,4,43
	.word	.L292
	.byte	1,1
	.word	.L64,.L303,.L63
	.byte	4
	.word	.L64,.L303
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryWriteAllKilled')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryWriteAllKilled')
	.sect	'.debug_line'
.L157:
	.word	.L387-.L386
.L386:
	.half	3
	.word	.L389-.L388
.L388:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L389:
	.byte	5,23,7,0,5,2
	.word	.L64
	.byte	3,201,4,1,5,39,9
	.half	.L390-.L64
	.byte	1,5,81,9
	.half	.L391-.L390
	.byte	1,5,22,9
	.half	.L392-.L391
	.byte	1,5,11,7,9
	.half	.L393-.L392
	.byte	3,1,1,5,58,9
	.half	.L394-.L393
	.byte	1,5,81,7,9
	.half	.L395-.L394
	.byte	3,127,1,5,1,9
	.half	.L6-.L395
	.byte	3,2,1,7,9
	.half	.L159-.L6
	.byte	0,1,1
.L387:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryWriteAllKilled')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L64,0,.L159-.L64,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryCrcMatch')
	.sect	'.debug_info'
.L160:
	.word	233
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L163,.L162
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryCrcMatch',0,1,215,4,43
	.word	.L292
	.byte	1,1
	.word	.L66,.L304,.L65
	.byte	4
	.word	.L66,.L304
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryCrcMatch')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryCrcMatch')
	.sect	'.debug_line'
.L162:
	.word	.L397-.L396
.L396:
	.half	3
	.word	.L399-.L398
.L398:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L399:
	.byte	5,41,7,0,5,2
	.word	.L66
	.byte	3,216,4,1,5,63,9
	.half	.L400-.L66
	.byte	1,5,1,9
	.half	.L164-.L400
	.byte	3,1,0,1,1
.L397:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryCrcMatch')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L66,0,.L164-.L66,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryCrcBusy')
	.sect	'.debug_info'
.L165:
	.word	232
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L168,.L167
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryCrcBusy',0,1,228,4,43
	.word	.L292
	.byte	1,1
	.word	.L68,.L305,.L67
	.byte	4
	.word	.L68,.L305
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryCrcBusy')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryCrcBusy')
	.sect	'.debug_line'
.L167:
	.word	.L402-.L401
.L401:
	.half	3
	.word	.L404-.L403
.L403:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L404:
	.byte	5,22,7,0,5,2
	.word	.L68
	.byte	3,229,4,1,5,1,9
	.half	.L405-.L68
	.byte	3,1,1,7,9
	.half	.L169-.L405
	.byte	0,1,1
.L402:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryCrcBusy')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L68,0,.L169-.L68,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryDataCopyBusy')
	.sect	'.debug_info'
.L170:
	.word	237
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L173,.L172
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryDataCopyBusy',0,1,241,4,43
	.word	.L292
	.byte	1,1
	.word	.L70,.L306,.L69
	.byte	4
	.word	.L70,.L306
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryDataCopyBusy')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryDataCopyBusy')
	.sect	'.debug_line'
.L172:
	.word	.L407-.L406
.L406:
	.half	3
	.word	.L409-.L408
.L408:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L409:
	.byte	5,22,7,0,5,2
	.word	.L70
	.byte	3,242,4,1,5,44,9
	.half	.L410-.L70
	.byte	1,5,59,9
	.half	.L411-.L410
	.byte	1,5,1,3,1,1,7,9
	.half	.L174-.L411
	.byte	0,1,1
.L407:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryDataCopyBusy')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L70,0,.L174-.L70,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryLastBlockDone_ReadAll')
	.sect	'.debug_info'
.L175:
	.word	246
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L178,.L177
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryLastBlockDone_ReadAll',0,1,254,4,43
	.word	.L292
	.byte	1,1
	.word	.L72,.L307,.L71
	.byte	4
	.word	.L72,.L307
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryLastBlockDone_ReadAll')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryLastBlockDone_ReadAll')
	.sect	'.debug_line'
.L177:
	.word	.L413-.L412
.L412:
	.half	3
	.word	.L415-.L414
.L414:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L415:
	.byte	5,22,7,0,5,2
	.word	.L72
	.byte	3,255,4,1,5,38,9
	.half	.L416-.L72
	.byte	1,5,55,9
	.half	.L417-.L416
	.byte	1,5,52,9
	.half	.L418-.L417
	.byte	1,5,1,3,1,1,7,9
	.half	.L179-.L418
	.byte	0,1,1
.L413:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryLastBlockDone_ReadAll')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L72,0,.L179-.L72,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryLastBlockDone_WriteAll')
	.sect	'.debug_info'
.L180:
	.word	247
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L183,.L182
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryLastBlockDone_WriteAll',0,1,139,5,43
	.word	.L292
	.byte	1,1
	.word	.L74,.L308,.L73
	.byte	4
	.word	.L74,.L308
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryLastBlockDone_WriteAll')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryLastBlockDone_WriteAll')
	.sect	'.debug_line'
.L182:
	.word	.L420-.L419
.L419:
	.half	3
	.word	.L422-.L421
.L421:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L422:
	.byte	5,22,7,0,5,2
	.word	.L74
	.byte	3,140,5,1,5,38,9
	.half	.L423-.L74
	.byte	1,5,52,9
	.half	.L424-.L423
	.byte	1,5,1,3,1,1,7,9
	.half	.L184-.L424
	.byte	0,1,1
.L420:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryLastBlockDone_WriteAll')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L74,0,.L184-.L74,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryLastResultOk')
	.sect	'.debug_info'
.L185:
	.word	237
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L188,.L187
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryLastResultOk',0,1,152,5,43
	.word	.L292
	.byte	1,1
	.word	.L76,.L309,.L75
	.byte	4
	.word	.L76,.L309
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryLastResultOk')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryLastResultOk')
	.sect	'.debug_line'
.L187:
	.word	.L426-.L425
.L425:
	.half	3
	.word	.L428-.L427
.L427:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L428:
	.byte	5,36,7,0,5,2
	.word	.L76
	.byte	3,153,5,1,5,58,9
	.half	.L429-.L76
	.byte	1,5,33,9
	.half	.L430-.L429
	.byte	1,5,1,3,1,1,7,9
	.half	.L189-.L430
	.byte	0,1,1
.L426:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryLastResultOk')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L76,0,.L189-.L76,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryMainFsmRunning')
	.sect	'.debug_info'
.L190:
	.word	239
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L193,.L192
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryMainFsmRunning',0,1,165,5,43
	.word	.L292
	.byte	1,1
	.word	.L78,.L310,.L77
	.byte	4
	.word	.L78,.L310
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryMainFsmRunning')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryMainFsmRunning')
	.sect	'.debug_line'
.L192:
	.word	.L432-.L431
.L431:
	.half	3
	.word	.L434-.L433
.L433:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L434:
	.byte	5,48,7,0,5,2
	.word	.L78
	.byte	3,166,5,1,5,45,9
	.half	.L435-.L78
	.byte	1,5,1,3,1,1,7,9
	.half	.L194-.L435
	.byte	0,1,1
.L432:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryMainFsmRunning')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L78,0,.L194-.L78,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryMultiJob')
	.sect	'.debug_info'
.L195:
	.word	233
	.half	3
	.word	.L196
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L198,.L197
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryMultiJob',0,1,178,5,43
	.word	.L292
	.byte	1,1
	.word	.L80,.L311,.L79
	.byte	4
	.word	.L80,.L311
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryMultiJob')
	.sect	'.debug_abbrev'
.L196:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryMultiJob')
	.sect	'.debug_line'
.L197:
	.word	.L437-.L436
.L436:
	.half	3
	.word	.L439-.L438
.L438:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L439:
	.byte	5,23,7,0,5,2
	.word	.L80
	.byte	3,179,5,1,5,39,9
	.half	.L440-.L80
	.byte	1,5,12,9
	.half	.L441-.L440
	.byte	3,5,1,5,1,3,1,1,7,9
	.half	.L199-.L441
	.byte	0,1,1
.L437:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryMultiJob')
	.sect	'.debug_ranges'
.L198:
	.word	-1,.L80,0,.L199-.L80,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryNvBusy')
	.sect	'.debug_info'
.L200:
	.word	231
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L203,.L202
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryNvBusy',0,1,201,5,43
	.word	.L292
	.byte	1,1
	.word	.L82,.L312,.L81
	.byte	4
	.word	.L82,.L312
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryNvBusy')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryNvBusy')
	.sect	'.debug_line'
.L202:
	.word	.L443-.L442
.L442:
	.half	3
	.word	.L445-.L444
.L444:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L445:
	.byte	5,22,7,0,5,2
	.word	.L82
	.byte	3,236,5,1,5,44,9
	.half	.L446-.L82
	.byte	1,5,58,9
	.half	.L447-.L446
	.byte	1,5,1,3,2,1,7,9
	.half	.L204-.L447
	.byte	0,1,1
.L443:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryNvBusy')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L82,0,.L204-.L82,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryMemHwaBusy')
	.sect	'.debug_info'
.L205:
	.word	262
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L208,.L207
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryMemHwaBusy',0,1,249,5,43
	.word	.L292
	.byte	1,1
	.word	.L84,.L313,.L83
	.byte	4
	.word	.L84,.L313
	.byte	5
	.byte	'MemHwaStatus',0,1,251,5,26
	.word	.L314,.L315
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryMemHwaBusy')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryMemHwaBusy')
	.sect	'.debug_line'
.L207:
	.word	.L449-.L448
.L448:
	.half	3
	.word	.L451-.L450
.L450:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L451:
	.byte	5,57,7,0,5,2
	.word	.L84
	.byte	3,250,5,1,5,49,9
	.half	.L334-.L84
	.byte	3,2,1,5,20,9
	.half	.L452-.L334
	.byte	1,5,66,7,9
	.half	.L453-.L452
	.byte	1,5,49,7,9
	.half	.L18-.L453
	.byte	1,5,3,9
	.half	.L19-.L18
	.byte	1,5,1,3,1,1,7,9
	.half	.L209-.L19
	.byte	0,1,1
.L449:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryMemHwaBusy')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L84,0,.L209-.L84,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryRamValid')
	.sect	'.debug_info'
.L210:
	.word	233
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L213,.L212
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryRamValid',0,1,241,6,43
	.word	.L292
	.byte	1,1
	.word	.L102,.L316,.L101
	.byte	4
	.word	.L102,.L316
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryRamValid')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryRamValid')
	.sect	'.debug_line'
.L212:
	.word	.L455-.L454
.L454:
	.half	3
	.word	.L457-.L456
.L456:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L457:
	.byte	5,7,7,0,5,2
	.word	.L102
	.byte	3,244,6,1,5,21,3,126,1,5,57,9
	.half	.L458-.L102
	.byte	1,5,20,9
	.half	.L459-.L458
	.byte	1,5,31,7,9
	.half	.L460-.L459
	.byte	3,1,1,5,40,9
	.half	.L461-.L460
	.byte	1,5,84,9
	.half	.L462-.L461
	.byte	1,5,85,7,9
	.half	.L463-.L462
	.byte	3,1,1,5,7,7,9
	.half	.L464-.L463
	.byte	1,5,1,9
	.half	.L34-.L464
	.byte	3,1,1,7,9
	.half	.L214-.L34
	.byte	0,1,1
.L455:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryRamValid')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L102,0,.L214-.L102,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryRedundantBlock')
	.sect	'.debug_info'
.L215:
	.word	235
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L218,.L217
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryRedundantBlock',0,1,136,6,43
	.word	.L292
	.byte	1,1
	.word	.L86,.L317,.L85
	.byte	4
	.word	.L318
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryRedundantBlock')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryRedundantBlock')
	.sect	'.debug_line'
.L217:
	.word	.L466-.L465
.L465:
	.half	3
	.word	.L468-.L467
.L467:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L468:
	.byte	5,89,7,0,5,2
	.word	.L86
	.byte	3,138,6,1,5,9,1,5,45,9
	.half	.L469-.L86
	.byte	1,5,7,9
	.half	.L470-.L469
	.byte	1,5,31,7,9
	.half	.L471-.L470
	.byte	3,1,1,5,60,9
	.half	.L472-.L471
	.byte	1,5,89,7,9
	.half	.L473-.L472
	.byte	3,127,1,5,1,9
	.half	.L21-.L473
	.byte	3,2,1,7,9
	.half	.L219-.L21
	.byte	0,1,1
.L466:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryRedundantBlock')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L86,0,.L219-.L86,0,0
.L318:
	.word	-1,.L86,0,.L317-.L86,-1,.L88,0,.L279-.L88,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QrySkipBlock')
	.sect	'.debug_info'
.L220:
	.word	230
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L223,.L222
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QrySkipBlock',0,1,154,6,43
	.word	.L292
	.byte	1,1
	.word	.L90,.L319,.L89
	.byte	4
	.word	.L320
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QrySkipBlock')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QrySkipBlock')
	.sect	'.debug_line'
.L222:
	.word	.L475-.L474
.L474:
	.half	3
	.word	.L477-.L476
.L476:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L477:
	.byte	5,7,7,0,5,2
	.word	.L90
	.byte	3,157,6,1,5,20,9
	.half	.L478-.L90
	.byte	3,126,1,5,26,7,9
	.half	.L479-.L478
	.byte	3,2,1,5,13,9
	.half	.L480-.L479
	.byte	1,5,51,7,9
	.half	.L481-.L480
	.byte	1,5,7,9
	.half	.L24-.L481
	.byte	1,5,3,9
	.half	.L25-.L24
	.byte	3,126,1,5,1,3,5,1,7,9
	.half	.L224-.L25
	.byte	0,1,1
.L475:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QrySkipBlock')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L90,0,.L224-.L90,0,0
.L320:
	.word	-1,.L90,0,.L319-.L90,-1,.L92,0,.L284-.L92,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QrySubFsmRunning')
	.sect	'.debug_info'
.L225:
	.word	238
	.half	3
	.word	.L226
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L228,.L227
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QrySubFsmRunning',0,1,171,6,43
	.word	.L292
	.byte	1,1
	.word	.L94,.L321,.L93
	.byte	4
	.word	.L94,.L321
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QrySubFsmRunning')
	.sect	'.debug_abbrev'
.L226:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QrySubFsmRunning')
	.sect	'.debug_line'
.L227:
	.word	.L483-.L482
.L482:
	.half	3
	.word	.L485-.L484
.L484:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L485:
	.byte	5,46,7,0,5,2
	.word	.L94
	.byte	3,172,6,1,5,43,9
	.half	.L486-.L94
	.byte	1,5,1,3,1,1,7,9
	.half	.L229-.L486
	.byte	0,1,1
.L483:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QrySubFsmRunning')
	.sect	'.debug_ranges'
.L228:
	.word	-1,.L94,0,.L229-.L94,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryTrue')
	.sect	'.debug_info'
.L230:
	.word	229
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L233,.L232
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryTrue',0,1,184,6,43
	.word	.L292
	.byte	1,1
	.word	.L96,.L322,.L95
	.byte	4
	.word	.L96,.L322
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryTrue')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryTrue')
	.sect	'.debug_line'
.L232:
	.word	.L488-.L487
.L487:
	.half	3
	.word	.L490-.L489
.L489:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L490:
	.byte	5,10,7,0,5,2
	.word	.L96
	.byte	3,185,6,1,5,1,3,1,1,7,9
	.half	.L234-.L96
	.byte	0,1,1
.L488:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryTrue')
	.sect	'.debug_ranges'
.L233:
	.word	-1,.L96,0,.L234-.L96,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryWriteBlockOnce')
	.sect	'.debug_info'
.L235:
	.word	263
	.half	3
	.word	.L236
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L238,.L237
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryWriteBlockOnce',0,1,199,6,43
	.word	.L292
	.byte	1,1
	.word	.L98,.L323,.L97
	.byte	4
	.word	.L98,.L323
	.byte	5
	.byte	'writeOnce',0,1,201,6,11
	.word	.L292,.L324
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryWriteBlockOnce')
	.sect	'.debug_abbrev'
.L236:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryWriteBlockOnce')
	.sect	'.debug_line'
.L237:
	.word	.L492-.L491
.L491:
	.half	3
	.word	.L494-.L493
.L493:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L494:
	.byte	5,21,7,0,5,2
	.word	.L98
	.byte	3,200,6,1,5,3,9
	.half	.L336-.L98
	.byte	3,2,1,5,27,7,9
	.half	.L495-.L336
	.byte	3,3,1,5,17,9
	.half	.L496-.L495
	.byte	3,3,1,5,3,9
	.half	.L30-.L496
	.byte	3,4,1,5,1,3,1,1,7,9
	.half	.L239-.L30
	.byte	0,1,1
.L492:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryWriteBlockOnce')
	.sect	'.debug_ranges'
.L238:
	.word	-1,.L98,0,.L239-.L98,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryWriteRetriesExceeded')
	.sect	'.debug_info'
.L240:
	.word	245
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L243,.L242
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryWriteRetriesExceeded',0,1,224,6,43
	.word	.L292
	.byte	1,1
	.word	.L100,.L325,.L99
	.byte	4
	.word	.L100,.L325
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryWriteRetriesExceeded')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryWriteRetriesExceeded')
	.sect	'.debug_line'
.L242:
	.word	.L498-.L497
.L497:
	.half	3
	.word	.L500-.L499
.L499:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L500:
	.byte	5,20,7,0,5,2
	.word	.L100
	.byte	3,227,6,1,5,42,9
	.half	.L501-.L100
	.byte	1,5,66,9
	.half	.L502-.L501
	.byte	1,5,64,9
	.half	.L503-.L502
	.byte	1,5,1,3,1,1,7,9
	.half	.L244-.L503
	.byte	0,1,1
.L498:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryWriteRetriesExceeded')
	.sect	'.debug_ranges'
.L243:
	.word	-1,.L100,0,.L244-.L100,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryHasRom')
	.sect	'.debug_info'
.L245:
	.word	231
	.half	3
	.word	.L246
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L248,.L247
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryHasRom',0,1,129,7,43
	.word	.L292
	.byte	1,1
	.word	.L104,.L326,.L103
	.byte	4
	.word	.L104,.L326
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryHasRom')
	.sect	'.debug_abbrev'
.L246:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryHasRom')
	.sect	'.debug_line'
.L247:
	.word	.L505-.L504
.L504:
	.half	3
	.word	.L507-.L506
.L506:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L507:
	.byte	5,21,7,0,5,2
	.word	.L104
	.byte	3,130,7,1,5,43,9
	.half	.L508-.L104
	.byte	1,5,92,9
	.half	.L509-.L508
	.byte	1,5,57,9
	.half	.L510-.L509
	.byte	1,5,20,9
	.half	.L511-.L510
	.byte	1,5,62,7,9
	.half	.L512-.L511
	.byte	3,1,1,5,78,9
	.half	.L513-.L512
	.byte	1,5,92,7,9
	.half	.L38-.L513
	.byte	3,127,1,5,3,9
	.half	.L39-.L38
	.byte	1,5,1,3,2,1,7,9
	.half	.L249-.L39
	.byte	0,1,1
.L505:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryHasRom')
	.sect	'.debug_ranges'
.L248:
	.word	-1,.L104,0,.L249-.L104,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryExtRuntime')
	.sect	'.debug_info'
.L250:
	.word	235
	.half	3
	.word	.L251
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L253,.L252
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryExtRuntime',0,1,144,7,43
	.word	.L292
	.byte	1,1
	.word	.L106,.L327,.L105
	.byte	4
	.word	.L106,.L327
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryExtRuntime')
	.sect	'.debug_abbrev'
.L251:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryExtRuntime')
	.sect	'.debug_line'
.L252:
	.word	.L515-.L514
.L514:
	.half	3
	.word	.L517-.L516
.L516:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L517:
	.byte	5,101,7,0,5,2
	.word	.L106
	.byte	3,146,7,1,5,22,1,5,44,9
	.half	.L518-.L106
	.byte	1,5,20,9
	.half	.L519-.L518
	.byte	1,5,31,7,9
	.half	.L520-.L519
	.byte	3,1,1,5,45,9
	.half	.L521-.L520
	.byte	1,5,90,9
	.half	.L522-.L521
	.byte	1,5,101,7,9
	.half	.L523-.L522
	.byte	3,127,1,5,1,9
	.half	.L41-.L523
	.byte	3,5,1,7,9
	.half	.L254-.L41
	.byte	0,1,1
.L515:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryExtRuntime')
	.sect	'.debug_ranges'
.L253:
	.word	-1,.L106,0,.L254-.L106,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryCRCCompMechanismSkipWrite')
	.sect	'.debug_info'
.L255:
	.word	274
	.half	3
	.word	.L256
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L258,.L257
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryCRCCompMechanismSkipWrite',0,1,223,7,43
	.word	.L292
	.byte	1,1
	.word	.L108,.L328,.L107
	.byte	4
	.word	.L108,.L328
	.byte	5
	.byte	'skipWrite',0,1,225,7,11
	.word	.L292,.L329
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryCRCCompMechanismSkipWrite')
	.sect	'.debug_abbrev'
.L256:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryCRCCompMechanismSkipWrite')
	.sect	'.debug_line'
.L257:
	.word	.L525-.L524
.L524:
	.half	3
	.word	.L527-.L526
.L526:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L527:
	.byte	5,6,7,0,5,2
	.word	.L108
	.byte	3,226,7,1,5,28,9
	.half	.L528-.L108
	.byte	1,5,21,9
	.half	.L529-.L528
	.byte	3,126,1,5,42,9
	.half	.L337-.L529
	.byte	3,2,1,5,3,9
	.half	.L530-.L337
	.byte	1,5,32,7,9
	.half	.L531-.L530
	.byte	3,3,1,5,45,9
	.half	.L532-.L531
	.byte	3,1,1,5,59,9
	.half	.L338-.L532
	.byte	3,2,1,5,1,9
	.half	.L44-.L338
	.byte	3,4,1,7,9
	.half	.L259-.L44
	.byte	0,1,1
.L525:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryCRCCompMechanismSkipWrite')
	.sect	'.debug_ranges'
.L258:
	.word	-1,.L108,0,.L259-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryPostReadTransform')
	.sect	'.debug_info'
.L260:
	.word	263
	.half	3
	.word	.L261
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L263,.L262
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryPostReadTransform',0,1,248,7,43
	.word	.L292
	.byte	1,1
	.word	.L110,.L330,.L109
	.byte	4
	.word	.L110,.L330
	.byte	5
	.byte	'retVal',0,1,250,7,11
	.word	.L292,.L331
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryPostReadTransform')
	.sect	'.debug_abbrev'
.L261:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryPostReadTransform')
	.sect	'.debug_line'
.L262:
	.word	.L534-.L533
.L533:
	.half	3
	.word	.L536-.L535
.L535:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L536:
	.byte	5,18,7,0,5,2
	.word	.L110
	.byte	3,249,7,1,5,6,3,2,1,5,42,9
	.half	.L537-.L110
	.byte	1,5,3,9
	.half	.L538-.L537
	.byte	1,5,9,7,9
	.half	.L539-.L538
	.byte	3,3,1,5,25,9
	.half	.L540-.L539
	.byte	1,5,31,9
	.half	.L541-.L540
	.byte	3,1,1,5,45,9
	.half	.L542-.L541
	.byte	3,1,1,5,66,9
	.half	.L341-.L542
	.byte	1,5,1,9
	.half	.L46-.L341
	.byte	3,4,1,7,9
	.half	.L264-.L46
	.byte	0,1,1
.L534:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryPostReadTransform')
	.sect	'.debug_ranges'
.L263:
	.word	-1,.L110,0,.L264-.L110,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryCsmRetryNecessary')
	.sect	'.debug_info'
.L265:
	.word	242
	.half	3
	.word	.L266
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L268,.L267
	.byte	2
	.word	.L121
	.byte	3
	.byte	'NvM_QryCsmRetryNecessary',0,1,173,9,43
	.word	.L292
	.byte	1,1
	.word	.L120,.L332,.L119
	.byte	4
	.word	.L120,.L332
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryCsmRetryNecessary')
	.sect	'.debug_abbrev'
.L266:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryCsmRetryNecessary')
	.sect	'.debug_line'
.L267:
	.word	.L544-.L543
.L543:
	.half	3
	.word	.L546-.L545
.L545:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L546:
	.byte	5,12,7,0,5,2
	.word	.L120
	.byte	3,184,9,1,5,1,3,2,1,7,9
	.half	.L269-.L120
	.byte	0,1,1
.L544:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryCsmRetryNecessary')
	.sect	'.debug_ranges'
.L268:
	.word	-1,.L120,0,.L269-.L120,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L270:
	.word	213
	.half	3
	.word	.L271
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L273,.L272
	.byte	2
	.word	.L121
	.byte	3
	.byte	'.cocofun_1',0,1,186,4,43,1
	.word	.L62,.L274,.L61
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L271:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L272:
	.word	.L548-.L547
.L547:
	.half	3
	.word	.L550-.L549
.L549:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L550:
	.byte	5,12,7,0,5,2
	.word	.L62
	.byte	3,187,4,1,9
	.half	.L274-.L62
	.byte	0,1,1,5,11,0,5,2
	.word	.L62
	.byte	3,202,4,1,5,12,9
	.half	.L551-.L62
	.byte	3,113,1,7,9
	.half	.L274-.L551
	.byte	0,1,1,5,23,0,5,2
	.word	.L62
	.byte	3,179,5,1,5,12,9
	.half	.L551-.L62
	.byte	3,136,127,1,7,9
	.half	.L274-.L551
	.byte	0,1,1,5,12,0,5,2
	.word	.L62
	.byte	3,148,8,1,9
	.half	.L551-.L62
	.byte	3,167,124,1,7,9
	.half	.L274-.L551
	.byte	0,1,1
.L548:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L273:
	.word	-1,.L62,0,.L274-.L62,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L275:
	.word	213
	.half	3
	.word	.L276
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L278,.L277
	.byte	2
	.word	.L121
	.byte	3
	.byte	'.cocofun_2',0,1,136,6,43,1
	.word	.L88,.L279,.L87
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L276:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L277:
	.word	.L553-.L552
.L552:
	.half	3
	.word	.L555-.L554
.L554:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L555:
	.byte	5,9,7,0,5,2
	.word	.L88
	.byte	3,138,6,1,5,31,9
	.half	.L340-.L88
	.byte	1,9
	.half	.L279-.L340
	.byte	0,1,1,5,9,0,5,2
	.word	.L88
	.byte	3,138,6,1,5,43,9
	.half	.L340-.L88
	.byte	3,232,0,1,5,31,9
	.half	.L556-.L340
	.byte	3,152,127,1,7,9
	.half	.L279-.L556
	.byte	0,1,1,5,9,0,5,2
	.word	.L88
	.byte	3,138,6,1,5,28,9
	.half	.L340-.L88
	.byte	3,241,1,1,5,31,9
	.half	.L556-.L340
	.byte	3,143,126,1,7,9
	.half	.L279-.L556
	.byte	0,1,1
.L553:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L278:
	.word	-1,.L88,0,.L279-.L88,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L280:
	.word	213
	.half	3
	.word	.L281
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L283,.L282
	.byte	2
	.word	.L121
	.byte	3
	.byte	'.cocofun_3',0,1,154,6,43,1
	.word	.L92,.L284,.L91
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L281:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L282:
	.word	.L558-.L557
.L557:
	.half	3
	.word	.L560-.L559
.L559:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L560:
	.byte	5,22,7,0,5,2
	.word	.L92
	.byte	3,155,6,1,5,44,9
	.half	.L561-.L92
	.byte	1,5,7,9
	.half	.L562-.L561
	.byte	3,2,1,5,58,9
	.half	.L335-.L562
	.byte	3,126,1,9
	.half	.L284-.L335
	.byte	0,1,1,5,8,0,5,2
	.word	.L92
	.byte	3,202,6,1,5,30,9
	.half	.L561-.L92
	.byte	1,5,21,9
	.half	.L562-.L561
	.byte	3,126,1,5,44,9
	.half	.L335-.L562
	.byte	3,2,1,5,58,9
	.half	.L563-.L335
	.byte	3,81,1,7,9
	.half	.L284-.L563
	.byte	0,1,1
.L558:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L283:
	.word	-1,.L92,0,.L284-.L92,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_4')
	.sect	'.debug_info'
.L285:
	.word	213
	.half	3
	.word	.L286
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L288,.L287
	.byte	2
	.word	.L121
	.byte	3
	.byte	'.cocofun_4',0,1,164,4,43,1
	.word	.L58,.L289,.L57
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_4')
	.sect	'.debug_abbrev'
.L286:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_4')
	.sect	'.debug_line'
.L287:
	.word	.L565-.L564
.L564:
	.half	3
	.word	.L567-.L566
.L566:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0,0,0,0,0
.L567:
	.byte	5,24,7,0,5,2
	.word	.L58
	.byte	3,166,4,1,9
	.half	.L289-.L58
	.byte	0,1,1,5,9,0,5,2
	.word	.L58
	.byte	3,138,6,1,5,24,9
	.half	.L568-.L58
	.byte	3,156,126,1,7,9
	.half	.L289-.L568
	.byte	0,1,1,5,21,0,5,2
	.word	.L58
	.byte	3,242,6,1,5,24,9
	.half	.L568-.L58
	.byte	3,180,125,1,7,9
	.half	.L289-.L568
	.byte	0,1,1,5,6,0,5,2
	.word	.L58
	.byte	3,251,7,1,5,24,9
	.half	.L568-.L58
	.byte	3,171,124,1,7,9
	.half	.L289-.L568
	.byte	0,1,1,5,22,0,5,2
	.word	.L58
	.byte	3,146,7,1,5,24,9
	.half	.L568-.L58
	.byte	3,148,125,1,7,9
	.half	.L289-.L568
	.byte	0,1,1
.L565:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_4')
	.sect	'.debug_ranges'
.L288:
	.word	-1,.L58,0,.L289-.L58,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QueryTable_ap')
	.sect	'.debug_info'
.L290:
	.word	210
	.half	3
	.word	.L291
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Qry.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L121
	.byte	3
	.byte	'NvM_QueryTable_ap',0,6,229,3,45
	.word	.L333
	.byte	1,5,3
	.word	NvM_QueryTable_ap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QueryTable_ap')
	.sect	'.debug_abbrev'
.L291:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L61:
	.word	-1,.L62,0,.L274-.L62
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L87:
	.word	-1,.L88,0,.L279-.L88
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L91:
	.word	-1,.L92,0,.L284-.L92
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_4')
	.sect	'.debug_loc'
.L57:
	.word	-1,.L58,0,.L289-.L58
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryBlockWriteAll')
	.sect	'.debug_loc'
.L55:
	.word	-1,.L56,0,.L299-.L56
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryCRCCompMechanismSkipWrite')
	.sect	'.debug_loc'
.L107:
	.word	-1,.L108,0,.L328-.L108
	.half	2
	.byte	138,0
	.word	0,0
.L329:
	.word	-1,.L108,.L337-.L108,.L338-.L108
	.half	5
	.byte	144,33,157,32,0
	.word	.L44-.L108,.L328-.L108
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryCancelWriteAll')
	.sect	'.debug_loc'
.L59:
	.word	-1,.L60,0,.L301-.L60
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryCrcBusy')
	.sect	'.debug_loc'
.L67:
	.word	-1,.L68,0,.L305-.L68
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryCrcMatch')
	.sect	'.debug_loc'
.L65:
	.word	-1,.L66,0,.L304-.L66
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryCsmRetryNecessary')
	.sect	'.debug_loc'
.L119:
	.word	-1,.L120,0,.L332-.L120
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryDataCopyBusy')
	.sect	'.debug_loc'
.L69:
	.word	-1,.L70,0,.L306-.L70
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryExtRuntime')
	.sect	'.debug_loc'
.L105:
	.word	-1,.L106,0,.L327-.L106
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryHasRom')
	.sect	'.debug_loc'
.L103:
	.word	-1,.L104,0,.L326-.L104
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryIsInitCallbackConfigured')
	.sect	'.debug_loc'
.L296:
	.word	-1,.L114,0,.L294-.L114
	.half	1
	.byte	100
	.word	0,0
.L113:
	.word	-1,.L114,0,.L294-.L114
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryLastBlockDone_ReadAll')
	.sect	'.debug_loc'
.L71:
	.word	-1,.L72,0,.L307-.L72
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryLastBlockDone_WriteAll')
	.sect	'.debug_loc'
.L73:
	.word	-1,.L74,0,.L308-.L74
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryLastResultOk')
	.sect	'.debug_loc'
.L75:
	.word	-1,.L76,0,.L309-.L76
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryMainFsmRunning')
	.sect	'.debug_loc'
.L77:
	.word	-1,.L78,0,.L310-.L78
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryMemHwaBusy')
	.sect	'.debug_loc'
.L315:
	.word	-1,.L84,.L334-.L84,.L313-.L84
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L83:
	.word	-1,.L84,0,.L313-.L84
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryMultiJob')
	.sect	'.debug_loc'
.L79:
	.word	-1,.L80,0,.L311-.L80
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryNvBusy')
	.sect	'.debug_loc'
.L81:
	.word	-1,.L82,0,.L312-.L82
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryPostReadTransform')
	.sect	'.debug_loc'
.L109:
	.word	-1,.L110,0,.L330-.L110
	.half	2
	.byte	138,0
	.word	0,0
.L331:
	.word	-1,.L110,.L339-.L110,.L289-.L110
	.half	5
	.byte	144,33,157,32,0
	.word	.L340-.L110,.L279-.L110
	.half	5
	.byte	144,33,157,32,0
	.word	0,.L341-.L110
	.half	5
	.byte	144,33,157,32,0
	.word	.L46-.L110,.L330-.L110
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryRamValid')
	.sect	'.debug_loc'
.L101:
	.word	-1,.L102,0,.L316-.L102
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryReadAllKilled')
	.sect	'.debug_loc'
.L111:
	.word	-1,.L112,0,.L293-.L112
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryRedundantBlock')
	.sect	'.debug_loc'
.L85:
	.word	-1,.L86,0,.L317-.L86
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QrySkipBlock')
	.sect	'.debug_loc'
.L89:
	.word	-1,.L90,0,.L319-.L90
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QrySubFsmRunning')
	.sect	'.debug_loc'
.L93:
	.word	-1,.L94,0,.L321-.L94
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QrySyncDecrypt')
	.sect	'.debug_loc'
.L115:
	.word	-1,.L116,0,.L297-.L116
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QrySyncEncrypt')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L118,0,.L298-.L118
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryTrue')
	.sect	'.debug_loc'
.L95:
	.word	-1,.L96,0,.L322-.L96
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryWriteAllKilled')
	.sect	'.debug_loc'
.L63:
	.word	-1,.L64,0,.L303-.L64
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryWriteBlockOnce')
	.sect	'.debug_loc'
.L97:
	.word	-1,.L98,0,.L323-.L98
	.half	2
	.byte	138,0
	.word	0,0
.L324:
	.word	-1,.L98,.L335-.L98,.L284-.L98
	.half	5
	.byte	144,36,157,32,0
	.word	.L336-.L98,.L323-.L98
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryWriteRetriesExceeded')
	.sect	'.debug_loc'
.L99:
	.word	-1,.L100,0,.L325-.L100
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L569:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('NvM_QryBlockWriteAll')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L56,.L299-.L56
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_QryCancelWriteAll')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L60,.L301-.L60
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryWriteAllKilled')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L64,.L303-.L64
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryCrcMatch')
	.sect	'.debug_frame'
	.word	12
	.word	.L569,.L66,.L304-.L66
	.sdecl	'.debug_frame',debug,cluster('NvM_QryCrcBusy')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L68,.L305-.L68
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryDataCopyBusy')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L70,.L306-.L70
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryLastBlockDone_ReadAll')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L72,.L307-.L72
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryLastBlockDone_WriteAll')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L74,.L308-.L74
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryLastResultOk')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L76,.L309-.L76
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryMainFsmRunning')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L78,.L310-.L78
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryMultiJob')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L80,.L311-.L80
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryNvBusy')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L82,.L312-.L82
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryMemHwaBusy')
	.sect	'.debug_frame'
	.word	12
	.word	.L569,.L84,.L313-.L84
	.sdecl	'.debug_frame',debug,cluster('NvM_QryRedundantBlock')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L86,.L317-.L86
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_QrySkipBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L569,.L90,.L319-.L90
	.sdecl	'.debug_frame',debug,cluster('NvM_QrySubFsmRunning')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L94,.L321-.L94
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryTrue')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L96,.L322-.L96
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryWriteBlockOnce')
	.sect	'.debug_frame'
	.word	12
	.word	.L569,.L98,.L323-.L98
	.sdecl	'.debug_frame',debug,cluster('NvM_QryWriteRetriesExceeded')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L100,.L325-.L100
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryRamValid')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L102,.L316-.L102
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_QryHasRom')
	.sect	'.debug_frame'
	.word	12
	.word	.L569,.L104,.L326-.L104
	.sdecl	'.debug_frame',debug,cluster('NvM_QryExtRuntime')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L106,.L327-.L106
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryCRCCompMechanismSkipWrite')
	.sect	'.debug_frame'
	.word	12
	.word	.L569,.L108,.L328-.L108
	.sdecl	'.debug_frame',debug,cluster('NvM_QryPostReadTransform')
	.sect	'.debug_frame'
	.word	12
	.word	.L569,.L110,.L330-.L110
	.sdecl	'.debug_frame',debug,cluster('NvM_QryReadAllKilled')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L112,.L293-.L112
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryIsInitCallbackConfigured')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L114,.L294-.L114
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_QrySyncDecrypt')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L116,.L297-.L116
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QrySyncEncrypt')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L118,.L298-.L118
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QryCsmRetryNecessary')
	.sect	'.debug_frame'
	.word	24
	.word	.L569,.L120,.L332-.L120
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L570:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_4')
	.sect	'.debug_frame'
	.word	24
	.word	.L570,.L58,.L289-.L58
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L570,.L62,.L274-.L62
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L570,.L88,.L279-.L88
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L570,.L92,.L284-.L92
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\eeprom\NvM\NvM_Qry.c	  1212  
; ..\eeprom\NvM\NvM_Qry.c	  1213  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_Qry.c	  1214    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Qry.c	  1215  
; ..\eeprom\NvM\NvM_Qry.c	  1216  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Qry.c	  1217   *  END OF FILE: NvM_Qry.c
; ..\eeprom\NvM\NvM_Qry.c	  1218   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Qry.c	  1219  

	; Module end
