	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc6748a -c99 --dep-file=Vss\\.Vss.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=Vss\\Vss.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o Vss\\Vss.src ..\\Vss\\Vss.c"
	.compiler_name		"ctc"
	.name	"Vss"

	
$TC16X
	
	.sdecl	'.text.Vss.Vss_Init',code,cluster('Vss_Init')
	.sect	'.text.Vss.Vss_Init'
	.align	2
	
	.global	Vss_Init

; ..\Vss\Vss.c	     1  /*******************************************************************************
; ..\Vss\Vss.c	     2   * Copyright (C) 2023 Technomous. All rights reserved         *
; ..\Vss\Vss.c	     3   ******************************************************************************/
; ..\Vss\Vss.c	     4  /**
; ..\Vss\Vss.c	     5   *  \file
; ..\Vss\Vss.c	     6   *      Vss.c
; ..\Vss\Vss.c	     7   *  \brief
; ..\Vss\Vss.c	     8   *      Vss basic function.
; ..\Vss\Vss.c	     9   *	\author
; ..\Vss\Vss.c	    10   *		Ou Hengyue
; ..\Vss\Vss.c	    11   */
; ..\Vss\Vss.c	    12  
; ..\Vss\Vss.c	    13  #include "Vss.h"
; ..\Vss\Vss.c	    14  #include "VSS_RW.h"
; ..\Vss\Vss.c	    15  #include "VssApiIndirect.h"
; ..\Vss\Vss.c	    16  #include "Appl.h"
; ..\Vss\Vss.c	    17  
; ..\Vss\Vss.c	    18  static Vss_Bypass_Flag = 1;
; ..\Vss\Vss.c	    19  
; ..\Vss\Vss.c	    20  Std_ReturnType Vss_Init(void)
; Function Vss_Init
.L9:
Vss_Init:	.type	func

; ..\Vss\VssApiIndirect.h	     1  /***************************************************
; ..\Vss\VssApiIndirect.h	     2   * Introduction: VSS涓氬姟鎺ュ彛
; ..\Vss\VssApiIndirect.h	     3   * author: wujialin
; ..\Vss\VssApiIndirect.h	     4   * date created: 2020-2-1
; ..\Vss\VssApiIndirect.h	     5   * date modified: 2021-4-21
; ..\Vss\VssApiIndirect.h	     6   * version: V1.4
; ..\Vss\VssApiIndirect.h	     7   * recently modified by: wujialin
; ..\Vss\VssApiIndirect.h	     8   *
; ..\Vss\VssApiIndirect.h	     9   * Copyright (C) 2017, 2021, Thinktech, Inc.
; ..\Vss\VssApiIndirect.h	    10   ***************************************************/
; ..\Vss\VssApiIndirect.h	    11  #ifndef __VSS_API_H__
; ..\Vss\VssApiIndirect.h	    12  #define __VSS_API_H__
; ..\Vss\VssApiIndirect.h	    13  
; ..\Vss\VssApiIndirect.h	    14  #include <stdio.h>
; ..\Vss\VssApiIndirect.h	    15  #include <stdint.h>
; ..\Vss\VssApiIndirect.h	    16  
; ..\Vss\VssApiIndirect.h	    17  
; ..\Vss\VssApiIndirect.h	    18  /*---functions---*/
; ..\Vss\VssApiIndirect.h	    19  #ifdef __cplusplus
; ..\Vss\VssApiIndirect.h	    20  extern "C" {
; ..\Vss\VssApiIndirect.h	    21  #endif
; ..\Vss\VssApiIndirect.h	    22  
; ..\Vss\VssApiIndirect.h	    23  #include "vsstype.h"
; ..\Vss\VssApiIndirect.h	    24  
; ..\Vss\VssApiIndirect.h	    25  
; ..\Vss\VssApiIndirect.h	    26  enum ALG_TYPE {
; ..\Vss\VssApiIndirect.h	    27  	ALG_GJ = 1,	
; ..\Vss\VssApiIndirect.h	    28  	ALG_GM,
; ..\Vss\VssApiIndirect.h	    29  };
; ..\Vss\VssApiIndirect.h	    30  
; ..\Vss\VssApiIndirect.h	    31  enum ENV_TYPE {
; ..\Vss\VssApiIndirect.h	    32  	ENV_QA = 1,
; ..\Vss\VssApiIndirect.h	    33  	ENV_PP,
; ..\Vss\VssApiIndirect.h	    34  	ENV_P,
; ..\Vss\VssApiIndirect.h	    35  };
; ..\Vss\VssApiIndirect.h	    36  
; ..\Vss\VssApiIndirect.h	    37  enum{
; ..\Vss\VssApiIndirect.h	    38  	CERT_TYPE_ROOT = 0,
; ..\Vss\VssApiIndirect.h	    39  	CERT_TYPE_USR,
; ..\Vss\VssApiIndirect.h	    40  };
; ..\Vss\VssApiIndirect.h	    41  
; ..\Vss\VssApiIndirect.h	    42  #define ROOT_CERT_SIZE  144
; ..\Vss\VssApiIndirect.h	    43  #define USER_CERT_SIZE  176
; ..\Vss\VssApiIndirect.h	    44  #define ECU_CERT_SIZE   164
; ..\Vss\VssApiIndirect.h	    45  
; ..\Vss\VssApiIndirect.h	    46  enum CERT_TYPE {
; ..\Vss\VssApiIndirect.h	    47    CERTTYPE_ROOT    = 0x10,
; ..\Vss\VssApiIndirect.h	    48    CERTTYPE_ECU     = 0x20,
; ..\Vss\VssApiIndirect.h	    49    CERTTYPE_USR     = 0x30,
; ..\Vss\VssApiIndirect.h	    50    CERTTYPE_USRREQ  = 0x31,
; ..\Vss\VssApiIndirect.h	    51  };
; ..\Vss\VssApiIndirect.h	    52  
; ..\Vss\VssApiIndirect.h	    53  #define INNER_KEY_MODE 0
; ..\Vss\VssApiIndirect.h	    54  #define OUTER_KEY_MODE 1
; ..\Vss\VssApiIndirect.h	    55  #define CALC_ENC 0
; ..\Vss\VssApiIndirect.h	    56  #define CALC_DEC 1
; ..\Vss\VssApiIndirect.h	    57  #define PAD_NO_FORCE 0
; ..\Vss\VssApiIndirect.h	    58  #define PAD_FORCE 1
; ..\Vss\VssApiIndirect.h	    59  #define HASH_NO_CALC 0
; ..\Vss\VssApiIndirect.h	    60  #define HASH_CALC 1
; ..\Vss\VssApiIndirect.h	    61  
; ..\Vss\VssApiIndirect.h	    62  /* Function Table Base Address */
; ..\Vss\VssApiIndirect.h	    63  #define VSSAPI_FUNC_TABLE_BASE          0x80048000//0xA007A800
; ..\Vss\VssApiIndirect.h	    64  
; ..\Vss\VssApiIndirect.h	    65  /* Function Index In Func Table */
; ..\Vss\VssApiIndirect.h	    66  enum{
; ..\Vss\VssApiIndirect.h	    67  	VSSAPI_VssAESCMac = 0,
; ..\Vss\VssApiIndirect.h	    68  	VSSAPI_VssAESCMacIndex,
; ..\Vss\VssApiIndirect.h	    69  	VSSAPI_VssAESCalc,
; ..\Vss\VssApiIndirect.h	    70  	VSSAPI_VssAESCalcIndex,
; ..\Vss\VssApiIndirect.h	    71  	VSSAPI_VssAESMac,
; ..\Vss\VssApiIndirect.h	    72  	VSSAPI_VssAsymGenKey,
; ..\Vss\VssApiIndirect.h	    73  	VSSAPI_VssAsymGenKeyIndex,
; ..\Vss\VssApiIndirect.h	    74  	VSSAPI_VssAsymmCalc,
; ..\Vss\VssApiIndirect.h	    75  	VSSAPI_VssAsymmCalcIndex,
; ..\Vss\VssApiIndirect.h	    76  	VSSAPI_VssCMac,
; ..\Vss\VssApiIndirect.h	    77  	VSSAPI_VssCMacIndex,
; ..\Vss\VssApiIndirect.h	    78  	VSSAPI_VssCalcEcu,
; ..\Vss\VssApiIndirect.h	    79  	VSSAPI_VssCalcFinishHmac,
; ..\Vss\VssApiIndirect.h	    80  	VSSAPI_VssCertExport,
; ..\Vss\VssApiIndirect.h	    81  	VSSAPI_VssCertImport,
; ..\Vss\VssApiIndirect.h	    82  	VSSAPI_VssCertPkEnc,
; ..\Vss\VssApiIndirect.h	    83  	VSSAPI_VssChipRead,
; ..\Vss\VssApiIndirect.h	    84  	VSSAPI_VssChipWrite,
; ..\Vss\VssApiIndirect.h	    85  	VSSAPI_VssCryptoInit,
; ..\Vss\VssApiIndirect.h	    86  	VSSAPI_VssECCCalc,
; ..\Vss\VssApiIndirect.h	    87  	VSSAPI_VssECCCalcIndex,
; ..\Vss\VssApiIndirect.h	    88  	VSSAPI_VssECCGenKey,
; ..\Vss\VssApiIndirect.h	    89  	VSSAPI_VssECCGenKeyIndex,
; ..\Vss\VssApiIndirect.h	    90  	VSSAPI_VssECCSign,
; ..\Vss\VssApiIndirect.h	    91  	VSSAPI_VssECCSignIndex,
; ..\Vss\VssApiIndirect.h	    92  	VSSAPI_VssECCVerify,
; ..\Vss\VssApiIndirect.h	    93  	VSSAPI_VssExportAtKey,
; ..\Vss\VssApiIndirect.h	    94  	VSSAPI_VssExportAtPin,
; ..\Vss\VssApiIndirect.h	    95  	VSSAPI_VssExportKey,
; ..\Vss\VssApiIndirect.h	    96  	VSSAPI_VssExportSessKey,
; ..\Vss\VssApiIndirect.h	    97  	VSSAPI_VssGenCertReq,
; ..\Vss\VssApiIndirect.h	    98  	VSSAPI_VssGenRandom,
; ..\Vss\VssApiIndirect.h	    99  	VSSAPI_VssGenSessionKey,
; ..\Vss\VssApiIndirect.h	   100  	VSSAPI_VssGenerateKeyByCode,
; ..\Vss\VssApiIndirect.h	   101  	VSSAPI_VssGetAlgFlag,
; ..\Vss\VssApiIndirect.h	   102  	VSSAPI_VssGetCertInfo,
; ..\Vss\VssApiIndirect.h	   103  	VSSAPI_VssGetChipID,
; ..\Vss\VssApiIndirect.h	   104  	VSSAPI_VssGetWroteFlag,
; ..\Vss\VssApiIndirect.h	   105  	VSSAPI_VssHMAC,
; ..\Vss\VssApiIndirect.h	   106  	VSSAPI_VssHash,
; ..\Vss\VssApiIndirect.h	   107  	VSSAPI_VssHashFinal,
; ..\Vss\VssApiIndirect.h	   108  	VSSAPI_VssHashInit,
; ..\Vss\VssApiIndirect.h	   109  	VSSAPI_VssHashUpdate,
; ..\Vss\VssApiIndirect.h	   110  	VSSAPI_VssImportSessKey,
; ..\Vss\VssApiIndirect.h	   111  	VSSAPI_VssKeyCodeFeedback,
; ..\Vss\VssApiIndirect.h	   112  	VSSAPI_VssMac,
; ..\Vss\VssApiIndirect.h	   113  	VSSAPI_VssResetSessionKey,
; ..\Vss\VssApiIndirect.h	   114  	VSSAPI_VssSM2Calc,
; ..\Vss\VssApiIndirect.h	   115  	VSSAPI_VssSM2CalcIndex,
; ..\Vss\VssApiIndirect.h	   116  	VSSAPI_VssSM2GenKey,
; ..\Vss\VssApiIndirect.h	   117  	VSSAPI_VssSM2GenKeyIndex,
; ..\Vss\VssApiIndirect.h	   118  	VSSAPI_VssSM2Sign,
; ..\Vss\VssApiIndirect.h	   119  	VSSAPI_VssSM2SignIndex,
; ..\Vss\VssApiIndirect.h	   120  	VSSAPI_VssSM2Verify,
; ..\Vss\VssApiIndirect.h	   121  	VSSAPI_VssSM4CMac,
; ..\Vss\VssApiIndirect.h	   122  	VSSAPI_VssSM4CMacIndex,
; ..\Vss\VssApiIndirect.h	   123  	VSSAPI_VssSM4Calc,
; ..\Vss\VssApiIndirect.h	   124  	VSSAPI_VssSM4CalcIndex,
; ..\Vss\VssApiIndirect.h	   125  	VSSAPI_VssSM4Mac,
; ..\Vss\VssApiIndirect.h	   126  	VSSAPI_VssSetKeyActive,
; ..\Vss\VssApiIndirect.h	   127  	VSSAPI_VssSetWroteFlag,
; ..\Vss\VssApiIndirect.h	   128  	VSSAPI_VssSign,
; ..\Vss\VssApiIndirect.h	   129  	VSSAPI_VssSignData,
; ..\Vss\VssApiIndirect.h	   130  	VSSAPI_VssSignIndex,
; ..\Vss\VssApiIndirect.h	   131  	VSSAPI_VssSignVerify,
; ..\Vss\VssApiIndirect.h	   132  	VSSAPI_VssStSymmCalc,
; ..\Vss\VssApiIndirect.h	   133  	VSSAPI_VssSymmCalc,
; ..\Vss\VssApiIndirect.h	   134  	VSSAPI_VssSymmCalcIndex,
; ..\Vss\VssApiIndirect.h	   135  	VSSAPI_VssUpdateEnvironment,
; ..\Vss\VssApiIndirect.h	   136  	VSSAPI_VssUpdateMasterKey,
; ..\Vss\VssApiIndirect.h	   137  	VSSAPI_VssVerifyCert,
; ..\Vss\VssApiIndirect.h	   138  	VSSAPI_VssVerifyCertValid,
; ..\Vss\VssApiIndirect.h	   139  	VSSAPI_VssVerifyEcuSign,
; ..\Vss\VssApiIndirect.h	   140  	VSSAPI_VssVerifyToolCert,
; ..\Vss\VssApiIndirect.h	   141  	VSSAPI_VssZucCalc,
; ..\Vss\VssApiIndirect.h	   142  	VSSAPI_VssZucCalcData,
; ..\Vss\VssApiIndirect.h	   143  	VSSAPI_VssZucSetKey,
; ..\Vss\VssApiIndirect.h	   144  	VSSAPI_VssGetChipVersion,
; ..\Vss\VssApiIndirect.h	   145  	VSSAPI_VssGetChipState,
; ..\Vss\VssApiIndirect.h	   146  	VSSAPI_VssGetVersion,
; ..\Vss\VssApiIndirect.h	   147  	VSSAPI_VssVerifySignCertValid,
; ..\Vss\VssApiIndirect.h	   148  	VSSAPI_VssGetEnvironment,
; ..\Vss\VssApiIndirect.h	   149  	VSSAPI_VssGetKeyActive,
; ..\Vss\VssApiIndirect.h	   150  
; ..\Vss\VssApiIndirect.h	   151  	VSSAPI_FUN_COUNT
; ..\Vss\VssApiIndirect.h	   152  };
; ..\Vss\VssApiIndirect.h	   153  
; ..\Vss\VssApiIndirect.h	   154  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   155  鎽樿: 绠楁硶搴撳垵濮嬪寲锛岄�氳繃璇诲彇FLASH鎸囧畾鐨勭畻娉曟潵鍒濆鍖栫郴缁熺畻娉曟爣璇嗭紝榛樿绠楁硶涓哄浗瀵嗙畻娉�
; ..\Vss\VssApiIndirect.h	   156     闇�瑕侀�氳繃璇ユ柟娉曞皢FLASH璇诲啓鐨勬帴鍙ｆ寚閽堟敞鍐屽埌VSS涓紝VSS鍙互閫氳繃璇ユ帴鍙ｈ鍐橣LASH涓寚瀹氱殑瀵嗛挜瀛樺偍鍖哄煙銆�
; ..\Vss\VssApiIndirect.h	   157     鐢变簬闈炲绉扮畻娉曠殑杩愮畻鏃堕棿杈冮暱锛堜緥濡傚畬鏁磋蒋绠楁硶楠岀鍙兘闇�瑕�500ms锛夛紝杩欏氨闇�瑕佺畻娉曞唴閮ㄨ繍绠楁椂鍒嗘澶嶄綅WDT鐨勬椂閽燂紝鍚﹀垯浼氬鑷碝CU澶嶄綅銆�
; ..\Vss\VssApiIndirect.h	   158  
; ..\Vss\VssApiIndirect.h	   159  鍙傛暟: type[in]    --鎸囧畾绠楁硶搴撶殑杩愮畻绫诲瀷锛�0-杞畻娉曪紱1-M300鑺墖
; ..\Vss\VssApiIndirect.h	   160  	group[in]  --棰勭暀鍙傛暟锛屾き鍦嗘洸鐜嘔D锛�0-NIST prime256v1; 1-brainpoolP256r1
; ..\Vss\VssApiIndirect.h	   161             鐩墠鍙敮鎸�0-NIST prime256v1鏇茬巼
; ..\Vss\VssApiIndirect.h	   162       flashCb[in]  --璇诲啓FLASH瀵嗛挜瀛樺偍鍖哄煙鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
; ..\Vss\VssApiIndirect.h	   163       wdtCb[in]   --澶嶄綅鐪嬮棬鐙楄鏃跺櫒鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
; ..\Vss\VssApiIndirect.h	   164  
; ..\Vss\VssApiIndirect.h	   165  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   166          0x04    --杩愮畻绫诲瀷涓嶆槸鎸囧畾鐨勬暟鍊�
; ..\Vss\VssApiIndirect.h	   167          0x1C    --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   168  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   169  static inline vss_uint32 VssCryptoInit(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb)
; ..\Vss\VssApiIndirect.h	   170  {
; ..\Vss\VssApiIndirect.h	   171      vss_uint32 (*api_func_raw)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb) = 0;
; ..\Vss\VssApiIndirect.h	   172  
; ..\Vss\VssApiIndirect.h	   173      api_func_raw = (vss_uint32 (*)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCryptoInit));
	movh.a	a15,#32773
	ld.w	d15,[a15]@los(0x80048048)
.L72:

; ..\Vss\VssApiIndirect.h	   174      return api_func_raw(type, group, flashCb, wdtCb);
	mov	d4,#0

; ..\Vss\Vss.c	    21  {
; ..\Vss\Vss.c	    22      uint8 vsnChkResult[32];
; ..\Vss\Vss.c	    23      Std_ReturnType ret;
; ..\Vss\Vss.c	    24      uint32 vss_flag;
; ..\Vss\Vss.c	    25  
; ..\Vss\Vss.c	    26     // Appl_Memcpy((uint8*)0x7003B000, (uint8*)0xA0118000, 4096);
; ..\Vss\Vss.c	    27      VssCryptoInit(0, 0, flash_cb, NULL);
	movh.a	a4,#@his(flash_cb)
.L73:

; ..\Vss\VssApiIndirect.h	     1  /***************************************************
; ..\Vss\VssApiIndirect.h	     2   * Introduction: VSS涓氬姟鎺ュ彛
; ..\Vss\VssApiIndirect.h	     3   * author: wujialin
; ..\Vss\VssApiIndirect.h	     4   * date created: 2020-2-1
; ..\Vss\VssApiIndirect.h	     5   * date modified: 2021-4-21
; ..\Vss\VssApiIndirect.h	     6   * version: V1.4
; ..\Vss\VssApiIndirect.h	     7   * recently modified by: wujialin
; ..\Vss\VssApiIndirect.h	     8   *
; ..\Vss\VssApiIndirect.h	     9   * Copyright (C) 2017, 2021, Thinktech, Inc.
; ..\Vss\VssApiIndirect.h	    10   ***************************************************/
; ..\Vss\VssApiIndirect.h	    11  #ifndef __VSS_API_H__
; ..\Vss\VssApiIndirect.h	    12  #define __VSS_API_H__
; ..\Vss\VssApiIndirect.h	    13  
; ..\Vss\VssApiIndirect.h	    14  #include <stdio.h>
; ..\Vss\VssApiIndirect.h	    15  #include <stdint.h>
; ..\Vss\VssApiIndirect.h	    16  
; ..\Vss\VssApiIndirect.h	    17  
; ..\Vss\VssApiIndirect.h	    18  /*---functions---*/
; ..\Vss\VssApiIndirect.h	    19  #ifdef __cplusplus
; ..\Vss\VssApiIndirect.h	    20  extern "C" {
; ..\Vss\VssApiIndirect.h	    21  #endif
; ..\Vss\VssApiIndirect.h	    22  
; ..\Vss\VssApiIndirect.h	    23  #include "vsstype.h"
; ..\Vss\VssApiIndirect.h	    24  
; ..\Vss\VssApiIndirect.h	    25  
; ..\Vss\VssApiIndirect.h	    26  enum ALG_TYPE {
; ..\Vss\VssApiIndirect.h	    27  	ALG_GJ = 1,	
; ..\Vss\VssApiIndirect.h	    28  	ALG_GM,
; ..\Vss\VssApiIndirect.h	    29  };
; ..\Vss\VssApiIndirect.h	    30  
; ..\Vss\VssApiIndirect.h	    31  enum ENV_TYPE {
; ..\Vss\VssApiIndirect.h	    32  	ENV_QA = 1,
; ..\Vss\VssApiIndirect.h	    33  	ENV_PP,
; ..\Vss\VssApiIndirect.h	    34  	ENV_P,
; ..\Vss\VssApiIndirect.h	    35  };
; ..\Vss\VssApiIndirect.h	    36  
; ..\Vss\VssApiIndirect.h	    37  enum{
; ..\Vss\VssApiIndirect.h	    38  	CERT_TYPE_ROOT = 0,
; ..\Vss\VssApiIndirect.h	    39  	CERT_TYPE_USR,
; ..\Vss\VssApiIndirect.h	    40  };
; ..\Vss\VssApiIndirect.h	    41  
; ..\Vss\VssApiIndirect.h	    42  #define ROOT_CERT_SIZE  144
; ..\Vss\VssApiIndirect.h	    43  #define USER_CERT_SIZE  176
; ..\Vss\VssApiIndirect.h	    44  #define ECU_CERT_SIZE   164
; ..\Vss\VssApiIndirect.h	    45  
; ..\Vss\VssApiIndirect.h	    46  enum CERT_TYPE {
; ..\Vss\VssApiIndirect.h	    47    CERTTYPE_ROOT    = 0x10,
; ..\Vss\VssApiIndirect.h	    48    CERTTYPE_ECU     = 0x20,
; ..\Vss\VssApiIndirect.h	    49    CERTTYPE_USR     = 0x30,
; ..\Vss\VssApiIndirect.h	    50    CERTTYPE_USRREQ  = 0x31,
; ..\Vss\VssApiIndirect.h	    51  };
; ..\Vss\VssApiIndirect.h	    52  
; ..\Vss\VssApiIndirect.h	    53  #define INNER_KEY_MODE 0
; ..\Vss\VssApiIndirect.h	    54  #define OUTER_KEY_MODE 1
; ..\Vss\VssApiIndirect.h	    55  #define CALC_ENC 0
; ..\Vss\VssApiIndirect.h	    56  #define CALC_DEC 1
; ..\Vss\VssApiIndirect.h	    57  #define PAD_NO_FORCE 0
; ..\Vss\VssApiIndirect.h	    58  #define PAD_FORCE 1
; ..\Vss\VssApiIndirect.h	    59  #define HASH_NO_CALC 0
; ..\Vss\VssApiIndirect.h	    60  #define HASH_CALC 1
; ..\Vss\VssApiIndirect.h	    61  
; ..\Vss\VssApiIndirect.h	    62  /* Function Table Base Address */
; ..\Vss\VssApiIndirect.h	    63  #define VSSAPI_FUNC_TABLE_BASE          0x80048000//0xA007A800
; ..\Vss\VssApiIndirect.h	    64  
; ..\Vss\VssApiIndirect.h	    65  /* Function Index In Func Table */
; ..\Vss\VssApiIndirect.h	    66  enum{
; ..\Vss\VssApiIndirect.h	    67  	VSSAPI_VssAESCMac = 0,
; ..\Vss\VssApiIndirect.h	    68  	VSSAPI_VssAESCMacIndex,
; ..\Vss\VssApiIndirect.h	    69  	VSSAPI_VssAESCalc,
; ..\Vss\VssApiIndirect.h	    70  	VSSAPI_VssAESCalcIndex,
; ..\Vss\VssApiIndirect.h	    71  	VSSAPI_VssAESMac,
; ..\Vss\VssApiIndirect.h	    72  	VSSAPI_VssAsymGenKey,
; ..\Vss\VssApiIndirect.h	    73  	VSSAPI_VssAsymGenKeyIndex,
; ..\Vss\VssApiIndirect.h	    74  	VSSAPI_VssAsymmCalc,
; ..\Vss\VssApiIndirect.h	    75  	VSSAPI_VssAsymmCalcIndex,
; ..\Vss\VssApiIndirect.h	    76  	VSSAPI_VssCMac,
; ..\Vss\VssApiIndirect.h	    77  	VSSAPI_VssCMacIndex,
; ..\Vss\VssApiIndirect.h	    78  	VSSAPI_VssCalcEcu,
; ..\Vss\VssApiIndirect.h	    79  	VSSAPI_VssCalcFinishHmac,
; ..\Vss\VssApiIndirect.h	    80  	VSSAPI_VssCertExport,
; ..\Vss\VssApiIndirect.h	    81  	VSSAPI_VssCertImport,
; ..\Vss\VssApiIndirect.h	    82  	VSSAPI_VssCertPkEnc,
; ..\Vss\VssApiIndirect.h	    83  	VSSAPI_VssChipRead,
; ..\Vss\VssApiIndirect.h	    84  	VSSAPI_VssChipWrite,
; ..\Vss\VssApiIndirect.h	    85  	VSSAPI_VssCryptoInit,
; ..\Vss\VssApiIndirect.h	    86  	VSSAPI_VssECCCalc,
; ..\Vss\VssApiIndirect.h	    87  	VSSAPI_VssECCCalcIndex,
; ..\Vss\VssApiIndirect.h	    88  	VSSAPI_VssECCGenKey,
; ..\Vss\VssApiIndirect.h	    89  	VSSAPI_VssECCGenKeyIndex,
; ..\Vss\VssApiIndirect.h	    90  	VSSAPI_VssECCSign,
; ..\Vss\VssApiIndirect.h	    91  	VSSAPI_VssECCSignIndex,
; ..\Vss\VssApiIndirect.h	    92  	VSSAPI_VssECCVerify,
; ..\Vss\VssApiIndirect.h	    93  	VSSAPI_VssExportAtKey,
; ..\Vss\VssApiIndirect.h	    94  	VSSAPI_VssExportAtPin,
; ..\Vss\VssApiIndirect.h	    95  	VSSAPI_VssExportKey,
; ..\Vss\VssApiIndirect.h	    96  	VSSAPI_VssExportSessKey,
; ..\Vss\VssApiIndirect.h	    97  	VSSAPI_VssGenCertReq,
; ..\Vss\VssApiIndirect.h	    98  	VSSAPI_VssGenRandom,
; ..\Vss\VssApiIndirect.h	    99  	VSSAPI_VssGenSessionKey,
; ..\Vss\VssApiIndirect.h	   100  	VSSAPI_VssGenerateKeyByCode,
; ..\Vss\VssApiIndirect.h	   101  	VSSAPI_VssGetAlgFlag,
; ..\Vss\VssApiIndirect.h	   102  	VSSAPI_VssGetCertInfo,
; ..\Vss\VssApiIndirect.h	   103  	VSSAPI_VssGetChipID,
; ..\Vss\VssApiIndirect.h	   104  	VSSAPI_VssGetWroteFlag,
; ..\Vss\VssApiIndirect.h	   105  	VSSAPI_VssHMAC,
; ..\Vss\VssApiIndirect.h	   106  	VSSAPI_VssHash,
; ..\Vss\VssApiIndirect.h	   107  	VSSAPI_VssHashFinal,
; ..\Vss\VssApiIndirect.h	   108  	VSSAPI_VssHashInit,
; ..\Vss\VssApiIndirect.h	   109  	VSSAPI_VssHashUpdate,
; ..\Vss\VssApiIndirect.h	   110  	VSSAPI_VssImportSessKey,
; ..\Vss\VssApiIndirect.h	   111  	VSSAPI_VssKeyCodeFeedback,
; ..\Vss\VssApiIndirect.h	   112  	VSSAPI_VssMac,
; ..\Vss\VssApiIndirect.h	   113  	VSSAPI_VssResetSessionKey,
; ..\Vss\VssApiIndirect.h	   114  	VSSAPI_VssSM2Calc,
; ..\Vss\VssApiIndirect.h	   115  	VSSAPI_VssSM2CalcIndex,
; ..\Vss\VssApiIndirect.h	   116  	VSSAPI_VssSM2GenKey,
; ..\Vss\VssApiIndirect.h	   117  	VSSAPI_VssSM2GenKeyIndex,
; ..\Vss\VssApiIndirect.h	   118  	VSSAPI_VssSM2Sign,
; ..\Vss\VssApiIndirect.h	   119  	VSSAPI_VssSM2SignIndex,
; ..\Vss\VssApiIndirect.h	   120  	VSSAPI_VssSM2Verify,
; ..\Vss\VssApiIndirect.h	   121  	VSSAPI_VssSM4CMac,
; ..\Vss\VssApiIndirect.h	   122  	VSSAPI_VssSM4CMacIndex,
; ..\Vss\VssApiIndirect.h	   123  	VSSAPI_VssSM4Calc,
; ..\Vss\VssApiIndirect.h	   124  	VSSAPI_VssSM4CalcIndex,
; ..\Vss\VssApiIndirect.h	   125  	VSSAPI_VssSM4Mac,
; ..\Vss\VssApiIndirect.h	   126  	VSSAPI_VssSetKeyActive,
; ..\Vss\VssApiIndirect.h	   127  	VSSAPI_VssSetWroteFlag,
; ..\Vss\VssApiIndirect.h	   128  	VSSAPI_VssSign,
; ..\Vss\VssApiIndirect.h	   129  	VSSAPI_VssSignData,
; ..\Vss\VssApiIndirect.h	   130  	VSSAPI_VssSignIndex,
; ..\Vss\VssApiIndirect.h	   131  	VSSAPI_VssSignVerify,
; ..\Vss\VssApiIndirect.h	   132  	VSSAPI_VssStSymmCalc,
; ..\Vss\VssApiIndirect.h	   133  	VSSAPI_VssSymmCalc,
; ..\Vss\VssApiIndirect.h	   134  	VSSAPI_VssSymmCalcIndex,
; ..\Vss\VssApiIndirect.h	   135  	VSSAPI_VssUpdateEnvironment,
; ..\Vss\VssApiIndirect.h	   136  	VSSAPI_VssUpdateMasterKey,
; ..\Vss\VssApiIndirect.h	   137  	VSSAPI_VssVerifyCert,
; ..\Vss\VssApiIndirect.h	   138  	VSSAPI_VssVerifyCertValid,
; ..\Vss\VssApiIndirect.h	   139  	VSSAPI_VssVerifyEcuSign,
; ..\Vss\VssApiIndirect.h	   140  	VSSAPI_VssVerifyToolCert,
; ..\Vss\VssApiIndirect.h	   141  	VSSAPI_VssZucCalc,
; ..\Vss\VssApiIndirect.h	   142  	VSSAPI_VssZucCalcData,
; ..\Vss\VssApiIndirect.h	   143  	VSSAPI_VssZucSetKey,
; ..\Vss\VssApiIndirect.h	   144  	VSSAPI_VssGetChipVersion,
; ..\Vss\VssApiIndirect.h	   145  	VSSAPI_VssGetChipState,
; ..\Vss\VssApiIndirect.h	   146  	VSSAPI_VssGetVersion,
; ..\Vss\VssApiIndirect.h	   147  	VSSAPI_VssVerifySignCertValid,
; ..\Vss\VssApiIndirect.h	   148  	VSSAPI_VssGetEnvironment,
; ..\Vss\VssApiIndirect.h	   149  	VSSAPI_VssGetKeyActive,
; ..\Vss\VssApiIndirect.h	   150  
; ..\Vss\VssApiIndirect.h	   151  	VSSAPI_FUN_COUNT
; ..\Vss\VssApiIndirect.h	   152  };
; ..\Vss\VssApiIndirect.h	   153  
; ..\Vss\VssApiIndirect.h	   154  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   155  鎽樿: 绠楁硶搴撳垵濮嬪寲锛岄�氳繃璇诲彇FLASH鎸囧畾鐨勭畻娉曟潵鍒濆鍖栫郴缁熺畻娉曟爣璇嗭紝榛樿绠楁硶涓哄浗瀵嗙畻娉�
; ..\Vss\VssApiIndirect.h	   156     闇�瑕侀�氳繃璇ユ柟娉曞皢FLASH璇诲啓鐨勬帴鍙ｆ寚閽堟敞鍐屽埌VSS涓紝VSS鍙互閫氳繃璇ユ帴鍙ｈ鍐橣LASH涓寚瀹氱殑瀵嗛挜瀛樺偍鍖哄煙銆�
; ..\Vss\VssApiIndirect.h	   157     鐢变簬闈炲绉扮畻娉曠殑杩愮畻鏃堕棿杈冮暱锛堜緥濡傚畬鏁磋蒋绠楁硶楠岀鍙兘闇�瑕�500ms锛夛紝杩欏氨闇�瑕佺畻娉曞唴閮ㄨ繍绠楁椂鍒嗘澶嶄綅WDT鐨勬椂閽燂紝鍚﹀垯浼氬鑷碝CU澶嶄綅銆�
; ..\Vss\VssApiIndirect.h	   158  
; ..\Vss\VssApiIndirect.h	   159  鍙傛暟: type[in]    --鎸囧畾绠楁硶搴撶殑杩愮畻绫诲瀷锛�0-杞畻娉曪紱1-M300鑺墖
; ..\Vss\VssApiIndirect.h	   160  	group[in]  --棰勭暀鍙傛暟锛屾き鍦嗘洸鐜嘔D锛�0-NIST prime256v1; 1-brainpoolP256r1
; ..\Vss\VssApiIndirect.h	   161             鐩墠鍙敮鎸�0-NIST prime256v1鏇茬巼
; ..\Vss\VssApiIndirect.h	   162       flashCb[in]  --璇诲啓FLASH瀵嗛挜瀛樺偍鍖哄煙鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
; ..\Vss\VssApiIndirect.h	   163       wdtCb[in]   --澶嶄綅鐪嬮棬鐙楄鏃跺櫒鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
; ..\Vss\VssApiIndirect.h	   164  
; ..\Vss\VssApiIndirect.h	   165  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   166          0x04    --杩愮畻绫诲瀷涓嶆槸鎸囧畾鐨勬暟鍊�
; ..\Vss\VssApiIndirect.h	   167          0x1C    --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   168  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   169  static inline vss_uint32 VssCryptoInit(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb)
; ..\Vss\VssApiIndirect.h	   170  {
; ..\Vss\VssApiIndirect.h	   171      vss_uint32 (*api_func_raw)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb) = 0;
; ..\Vss\VssApiIndirect.h	   172  
; ..\Vss\VssApiIndirect.h	   173      api_func_raw = (vss_uint32 (*)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCryptoInit));
	mov.a	a15,d15
.L74:

; ..\Vss\VssApiIndirect.h	   174      return api_func_raw(type, group, flashCb, wdtCb);
	mov	d5,d4
	lea	a4,[a4]@los(flash_cb)
.L36:
	sub.a	a10,#40
.L47:
	mov.a	a5,#0
	calli	a15
.L48:

; ..\Vss\VssApiIndirect.h	     1  /***************************************************
; ..\Vss\VssApiIndirect.h	     2   * Introduction: VSS涓氬姟鎺ュ彛
; ..\Vss\VssApiIndirect.h	     3   * author: wujialin
; ..\Vss\VssApiIndirect.h	     4   * date created: 2020-2-1
; ..\Vss\VssApiIndirect.h	     5   * date modified: 2021-4-21
; ..\Vss\VssApiIndirect.h	     6   * version: V1.4
; ..\Vss\VssApiIndirect.h	     7   * recently modified by: wujialin
; ..\Vss\VssApiIndirect.h	     8   *
; ..\Vss\VssApiIndirect.h	     9   * Copyright (C) 2017, 2021, Thinktech, Inc.
; ..\Vss\VssApiIndirect.h	    10   ***************************************************/
; ..\Vss\VssApiIndirect.h	    11  #ifndef __VSS_API_H__
; ..\Vss\VssApiIndirect.h	    12  #define __VSS_API_H__
; ..\Vss\VssApiIndirect.h	    13  
; ..\Vss\VssApiIndirect.h	    14  #include <stdio.h>
; ..\Vss\VssApiIndirect.h	    15  #include <stdint.h>
; ..\Vss\VssApiIndirect.h	    16  
; ..\Vss\VssApiIndirect.h	    17  
; ..\Vss\VssApiIndirect.h	    18  /*---functions---*/
; ..\Vss\VssApiIndirect.h	    19  #ifdef __cplusplus
; ..\Vss\VssApiIndirect.h	    20  extern "C" {
; ..\Vss\VssApiIndirect.h	    21  #endif
; ..\Vss\VssApiIndirect.h	    22  
; ..\Vss\VssApiIndirect.h	    23  #include "vsstype.h"
; ..\Vss\VssApiIndirect.h	    24  
; ..\Vss\VssApiIndirect.h	    25  
; ..\Vss\VssApiIndirect.h	    26  enum ALG_TYPE {
; ..\Vss\VssApiIndirect.h	    27  	ALG_GJ = 1,	
; ..\Vss\VssApiIndirect.h	    28  	ALG_GM,
; ..\Vss\VssApiIndirect.h	    29  };
; ..\Vss\VssApiIndirect.h	    30  
; ..\Vss\VssApiIndirect.h	    31  enum ENV_TYPE {
; ..\Vss\VssApiIndirect.h	    32  	ENV_QA = 1,
; ..\Vss\VssApiIndirect.h	    33  	ENV_PP,
; ..\Vss\VssApiIndirect.h	    34  	ENV_P,
; ..\Vss\VssApiIndirect.h	    35  };
; ..\Vss\VssApiIndirect.h	    36  
; ..\Vss\VssApiIndirect.h	    37  enum{
; ..\Vss\VssApiIndirect.h	    38  	CERT_TYPE_ROOT = 0,
; ..\Vss\VssApiIndirect.h	    39  	CERT_TYPE_USR,
; ..\Vss\VssApiIndirect.h	    40  };
; ..\Vss\VssApiIndirect.h	    41  
; ..\Vss\VssApiIndirect.h	    42  #define ROOT_CERT_SIZE  144
; ..\Vss\VssApiIndirect.h	    43  #define USER_CERT_SIZE  176
; ..\Vss\VssApiIndirect.h	    44  #define ECU_CERT_SIZE   164
; ..\Vss\VssApiIndirect.h	    45  
; ..\Vss\VssApiIndirect.h	    46  enum CERT_TYPE {
; ..\Vss\VssApiIndirect.h	    47    CERTTYPE_ROOT    = 0x10,
; ..\Vss\VssApiIndirect.h	    48    CERTTYPE_ECU     = 0x20,
; ..\Vss\VssApiIndirect.h	    49    CERTTYPE_USR     = 0x30,
; ..\Vss\VssApiIndirect.h	    50    CERTTYPE_USRREQ  = 0x31,
; ..\Vss\VssApiIndirect.h	    51  };
; ..\Vss\VssApiIndirect.h	    52  
; ..\Vss\VssApiIndirect.h	    53  #define INNER_KEY_MODE 0
; ..\Vss\VssApiIndirect.h	    54  #define OUTER_KEY_MODE 1
; ..\Vss\VssApiIndirect.h	    55  #define CALC_ENC 0
; ..\Vss\VssApiIndirect.h	    56  #define CALC_DEC 1
; ..\Vss\VssApiIndirect.h	    57  #define PAD_NO_FORCE 0
; ..\Vss\VssApiIndirect.h	    58  #define PAD_FORCE 1
; ..\Vss\VssApiIndirect.h	    59  #define HASH_NO_CALC 0
; ..\Vss\VssApiIndirect.h	    60  #define HASH_CALC 1
; ..\Vss\VssApiIndirect.h	    61  
; ..\Vss\VssApiIndirect.h	    62  /* Function Table Base Address */
; ..\Vss\VssApiIndirect.h	    63  #define VSSAPI_FUNC_TABLE_BASE          0x80048000//0xA007A800
; ..\Vss\VssApiIndirect.h	    64  
; ..\Vss\VssApiIndirect.h	    65  /* Function Index In Func Table */
; ..\Vss\VssApiIndirect.h	    66  enum{
; ..\Vss\VssApiIndirect.h	    67  	VSSAPI_VssAESCMac = 0,
; ..\Vss\VssApiIndirect.h	    68  	VSSAPI_VssAESCMacIndex,
; ..\Vss\VssApiIndirect.h	    69  	VSSAPI_VssAESCalc,
; ..\Vss\VssApiIndirect.h	    70  	VSSAPI_VssAESCalcIndex,
; ..\Vss\VssApiIndirect.h	    71  	VSSAPI_VssAESMac,
; ..\Vss\VssApiIndirect.h	    72  	VSSAPI_VssAsymGenKey,
; ..\Vss\VssApiIndirect.h	    73  	VSSAPI_VssAsymGenKeyIndex,
; ..\Vss\VssApiIndirect.h	    74  	VSSAPI_VssAsymmCalc,
; ..\Vss\VssApiIndirect.h	    75  	VSSAPI_VssAsymmCalcIndex,
; ..\Vss\VssApiIndirect.h	    76  	VSSAPI_VssCMac,
; ..\Vss\VssApiIndirect.h	    77  	VSSAPI_VssCMacIndex,
; ..\Vss\VssApiIndirect.h	    78  	VSSAPI_VssCalcEcu,
; ..\Vss\VssApiIndirect.h	    79  	VSSAPI_VssCalcFinishHmac,
; ..\Vss\VssApiIndirect.h	    80  	VSSAPI_VssCertExport,
; ..\Vss\VssApiIndirect.h	    81  	VSSAPI_VssCertImport,
; ..\Vss\VssApiIndirect.h	    82  	VSSAPI_VssCertPkEnc,
; ..\Vss\VssApiIndirect.h	    83  	VSSAPI_VssChipRead,
; ..\Vss\VssApiIndirect.h	    84  	VSSAPI_VssChipWrite,
; ..\Vss\VssApiIndirect.h	    85  	VSSAPI_VssCryptoInit,
; ..\Vss\VssApiIndirect.h	    86  	VSSAPI_VssECCCalc,
; ..\Vss\VssApiIndirect.h	    87  	VSSAPI_VssECCCalcIndex,
; ..\Vss\VssApiIndirect.h	    88  	VSSAPI_VssECCGenKey,
; ..\Vss\VssApiIndirect.h	    89  	VSSAPI_VssECCGenKeyIndex,
; ..\Vss\VssApiIndirect.h	    90  	VSSAPI_VssECCSign,
; ..\Vss\VssApiIndirect.h	    91  	VSSAPI_VssECCSignIndex,
; ..\Vss\VssApiIndirect.h	    92  	VSSAPI_VssECCVerify,
; ..\Vss\VssApiIndirect.h	    93  	VSSAPI_VssExportAtKey,
; ..\Vss\VssApiIndirect.h	    94  	VSSAPI_VssExportAtPin,
; ..\Vss\VssApiIndirect.h	    95  	VSSAPI_VssExportKey,
; ..\Vss\VssApiIndirect.h	    96  	VSSAPI_VssExportSessKey,
; ..\Vss\VssApiIndirect.h	    97  	VSSAPI_VssGenCertReq,
; ..\Vss\VssApiIndirect.h	    98  	VSSAPI_VssGenRandom,
; ..\Vss\VssApiIndirect.h	    99  	VSSAPI_VssGenSessionKey,
; ..\Vss\VssApiIndirect.h	   100  	VSSAPI_VssGenerateKeyByCode,
; ..\Vss\VssApiIndirect.h	   101  	VSSAPI_VssGetAlgFlag,
; ..\Vss\VssApiIndirect.h	   102  	VSSAPI_VssGetCertInfo,
; ..\Vss\VssApiIndirect.h	   103  	VSSAPI_VssGetChipID,
; ..\Vss\VssApiIndirect.h	   104  	VSSAPI_VssGetWroteFlag,
; ..\Vss\VssApiIndirect.h	   105  	VSSAPI_VssHMAC,
; ..\Vss\VssApiIndirect.h	   106  	VSSAPI_VssHash,
; ..\Vss\VssApiIndirect.h	   107  	VSSAPI_VssHashFinal,
; ..\Vss\VssApiIndirect.h	   108  	VSSAPI_VssHashInit,
; ..\Vss\VssApiIndirect.h	   109  	VSSAPI_VssHashUpdate,
; ..\Vss\VssApiIndirect.h	   110  	VSSAPI_VssImportSessKey,
; ..\Vss\VssApiIndirect.h	   111  	VSSAPI_VssKeyCodeFeedback,
; ..\Vss\VssApiIndirect.h	   112  	VSSAPI_VssMac,
; ..\Vss\VssApiIndirect.h	   113  	VSSAPI_VssResetSessionKey,
; ..\Vss\VssApiIndirect.h	   114  	VSSAPI_VssSM2Calc,
; ..\Vss\VssApiIndirect.h	   115  	VSSAPI_VssSM2CalcIndex,
; ..\Vss\VssApiIndirect.h	   116  	VSSAPI_VssSM2GenKey,
; ..\Vss\VssApiIndirect.h	   117  	VSSAPI_VssSM2GenKeyIndex,
; ..\Vss\VssApiIndirect.h	   118  	VSSAPI_VssSM2Sign,
; ..\Vss\VssApiIndirect.h	   119  	VSSAPI_VssSM2SignIndex,
; ..\Vss\VssApiIndirect.h	   120  	VSSAPI_VssSM2Verify,
; ..\Vss\VssApiIndirect.h	   121  	VSSAPI_VssSM4CMac,
; ..\Vss\VssApiIndirect.h	   122  	VSSAPI_VssSM4CMacIndex,
; ..\Vss\VssApiIndirect.h	   123  	VSSAPI_VssSM4Calc,
; ..\Vss\VssApiIndirect.h	   124  	VSSAPI_VssSM4CalcIndex,
; ..\Vss\VssApiIndirect.h	   125  	VSSAPI_VssSM4Mac,
; ..\Vss\VssApiIndirect.h	   126  	VSSAPI_VssSetKeyActive,
; ..\Vss\VssApiIndirect.h	   127  	VSSAPI_VssSetWroteFlag,
; ..\Vss\VssApiIndirect.h	   128  	VSSAPI_VssSign,
; ..\Vss\VssApiIndirect.h	   129  	VSSAPI_VssSignData,
; ..\Vss\VssApiIndirect.h	   130  	VSSAPI_VssSignIndex,
; ..\Vss\VssApiIndirect.h	   131  	VSSAPI_VssSignVerify,
; ..\Vss\VssApiIndirect.h	   132  	VSSAPI_VssStSymmCalc,
; ..\Vss\VssApiIndirect.h	   133  	VSSAPI_VssSymmCalc,
; ..\Vss\VssApiIndirect.h	   134  	VSSAPI_VssSymmCalcIndex,
; ..\Vss\VssApiIndirect.h	   135  	VSSAPI_VssUpdateEnvironment,
; ..\Vss\VssApiIndirect.h	   136  	VSSAPI_VssUpdateMasterKey,
; ..\Vss\VssApiIndirect.h	   137  	VSSAPI_VssVerifyCert,
; ..\Vss\VssApiIndirect.h	   138  	VSSAPI_VssVerifyCertValid,
; ..\Vss\VssApiIndirect.h	   139  	VSSAPI_VssVerifyEcuSign,
; ..\Vss\VssApiIndirect.h	   140  	VSSAPI_VssVerifyToolCert,
; ..\Vss\VssApiIndirect.h	   141  	VSSAPI_VssZucCalc,
; ..\Vss\VssApiIndirect.h	   142  	VSSAPI_VssZucCalcData,
; ..\Vss\VssApiIndirect.h	   143  	VSSAPI_VssZucSetKey,
; ..\Vss\VssApiIndirect.h	   144  	VSSAPI_VssGetChipVersion,
; ..\Vss\VssApiIndirect.h	   145  	VSSAPI_VssGetChipState,
; ..\Vss\VssApiIndirect.h	   146  	VSSAPI_VssGetVersion,
; ..\Vss\VssApiIndirect.h	   147  	VSSAPI_VssVerifySignCertValid,
; ..\Vss\VssApiIndirect.h	   148  	VSSAPI_VssGetEnvironment,
; ..\Vss\VssApiIndirect.h	   149  	VSSAPI_VssGetKeyActive,
; ..\Vss\VssApiIndirect.h	   150  
; ..\Vss\VssApiIndirect.h	   151  	VSSAPI_FUN_COUNT
; ..\Vss\VssApiIndirect.h	   152  };
; ..\Vss\VssApiIndirect.h	   153  
; ..\Vss\VssApiIndirect.h	   154  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   155  鎽樿: 绠楁硶搴撳垵濮嬪寲锛岄�氳繃璇诲彇FLASH鎸囧畾鐨勭畻娉曟潵鍒濆鍖栫郴缁熺畻娉曟爣璇嗭紝榛樿绠楁硶涓哄浗瀵嗙畻娉�
; ..\Vss\VssApiIndirect.h	   156     闇�瑕侀�氳繃璇ユ柟娉曞皢FLASH璇诲啓鐨勬帴鍙ｆ寚閽堟敞鍐屽埌VSS涓紝VSS鍙互閫氳繃璇ユ帴鍙ｈ鍐橣LASH涓寚瀹氱殑瀵嗛挜瀛樺偍鍖哄煙銆�
; ..\Vss\VssApiIndirect.h	   157     鐢变簬闈炲绉扮畻娉曠殑杩愮畻鏃堕棿杈冮暱锛堜緥濡傚畬鏁磋蒋绠楁硶楠岀鍙兘闇�瑕�500ms锛夛紝杩欏氨闇�瑕佺畻娉曞唴閮ㄨ繍绠楁椂鍒嗘澶嶄綅WDT鐨勬椂閽燂紝鍚﹀垯浼氬鑷碝CU澶嶄綅銆�
; ..\Vss\VssApiIndirect.h	   158  
; ..\Vss\VssApiIndirect.h	   159  鍙傛暟: type[in]    --鎸囧畾绠楁硶搴撶殑杩愮畻绫诲瀷锛�0-杞畻娉曪紱1-M300鑺墖
; ..\Vss\VssApiIndirect.h	   160  	group[in]  --棰勭暀鍙傛暟锛屾き鍦嗘洸鐜嘔D锛�0-NIST prime256v1; 1-brainpoolP256r1
; ..\Vss\VssApiIndirect.h	   161             鐩墠鍙敮鎸�0-NIST prime256v1鏇茬巼
; ..\Vss\VssApiIndirect.h	   162       flashCb[in]  --璇诲啓FLASH瀵嗛挜瀛樺偍鍖哄煙鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
; ..\Vss\VssApiIndirect.h	   163       wdtCb[in]   --澶嶄綅鐪嬮棬鐙楄鏃跺櫒鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
; ..\Vss\VssApiIndirect.h	   164  
; ..\Vss\VssApiIndirect.h	   165  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   166          0x04    --杩愮畻绫诲瀷涓嶆槸鎸囧畾鐨勬暟鍊�
; ..\Vss\VssApiIndirect.h	   167          0x1C    --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   168  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   169  static inline vss_uint32 VssCryptoInit(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb)
; ..\Vss\VssApiIndirect.h	   170  {
; ..\Vss\VssApiIndirect.h	   171      vss_uint32 (*api_func_raw)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb) = 0;
; ..\Vss\VssApiIndirect.h	   172  
; ..\Vss\VssApiIndirect.h	   173      api_func_raw = (vss_uint32 (*)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCryptoInit));
; ..\Vss\VssApiIndirect.h	   174      return api_func_raw(type, group, flashCb, wdtCb);
; ..\Vss\VssApiIndirect.h	   175  }
; ..\Vss\VssApiIndirect.h	   176  
; ..\Vss\VssApiIndirect.h	   177  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   178  鎽樿: 闅忔満鐢熸垚SM2闈炲绉板瘑閽ュ骞跺鍑烘帴鍙�
; ..\Vss\VssApiIndirect.h	   179  
; ..\Vss\VssApiIndirect.h	   180  鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   181        szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   182        szSK[out]     --绉侀挜SK锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   183  
; ..\Vss\VssApiIndirect.h	   184  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   185          0x04    --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   186          0x08    --璁＄畻澶辫触
; ..\Vss\VssApiIndirect.h	   187          0x18    --绠楁硶涓嶆敮鎸�
; ..\Vss\VssApiIndirect.h	   188  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   189  static inline vss_uint32 VssSM2GenKey(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK)
; ..\Vss\VssApiIndirect.h	   190  {
; ..\Vss\VssApiIndirect.h	   191      vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK) = 0;
; ..\Vss\VssApiIndirect.h	   192  
; ..\Vss\VssApiIndirect.h	   193      api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2GenKey));
; ..\Vss\VssApiIndirect.h	   194      return api_func_raw(szX, szY, szSK);
; ..\Vss\VssApiIndirect.h	   195  }
; ..\Vss\VssApiIndirect.h	   196  
; ..\Vss\VssApiIndirect.h	   197  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   198  鎽樿: 闅忔満鐢熸垚SM2闈炲绉板瘑閽ュ骞跺瓨鍌ㄦ帴鍙�
; ..\Vss\VssApiIndirect.h	   199  
; ..\Vss\VssApiIndirect.h	   200  鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   201        szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   202  
; ..\Vss\VssApiIndirect.h	   203  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   204          0x04    --szX鎴杝zY浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   205          0x08    --璁＄畻澶辫触
; ..\Vss\VssApiIndirect.h	   206          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   207          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   208  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   209  static inline vss_uint32 VssSM2GenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
; ..\Vss\VssApiIndirect.h	   210  {
; ..\Vss\VssApiIndirect.h	   211      vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY) = 0;
; ..\Vss\VssApiIndirect.h	   212  
; ..\Vss\VssApiIndirect.h	   213      api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2GenKeyIndex));
; ..\Vss\VssApiIndirect.h	   214      return api_func_raw(szX, szY);
; ..\Vss\VssApiIndirect.h	   215  }
; ..\Vss\VssApiIndirect.h	   216  
; ..\Vss\VssApiIndirect.h	   217  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   218  鎽樿: 浠ヨ緭鍏ョ殑杞﹁締瀹夊叏鐮佷綔涓虹瀛愶紝鏍规嵁閰嶇疆鏂囦欢浜х敓绯诲垪鐨勫绉板瘑閽�
; ..\Vss\VssApiIndirect.h	   219  
; ..\Vss\VssApiIndirect.h	   220  鍙傛暟: len[in]     --杞﹁締瀹夊叏鐮侀暱搴︼紝鍥哄畾32
; ..\Vss\VssApiIndirect.h	   221       code[in]    --杞﹁締瀹夊叏鐮侊紝鍥哄畾32瀛楄妭锛岀敱绗�32瀛楄妭鎸囧畾绠楁硶锛屽綋璇ユ暟涓哄伓鏁版椂鎸囧畾涓哄浗瀵嗙畻娉曪紝涓哄鏁版椂鎸囧畾涓哄浗闄呯畻娉曪紙鍗砤lg=code[31]&1; 0-鍥藉瘑锛�1-鍥介檯锛�
; ..\Vss\VssApiIndirect.h	   222       szKeyIdList[out] --鐢熸垚鎴愬姛鐨勫瘑閽D娓呭崟锛屼緥濡傗��1,2,5鈥�
; ..\Vss\VssApiIndirect.h	   223       AutoSetWroteFlag[in] -璁�1鍦ㄥ姛鑳藉畬鎴愬悗鑷姩缃畐rote Flag涓�1锛涜0涓嶅仛浠讳綍鎿嶄綔
; ..\Vss\VssApiIndirect.h	   224  
; ..\Vss\VssApiIndirect.h	   225  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   226          0x04    --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   227          0x12    --瀹夊叏鐮侀暱搴︿笉涓�32
; ..\Vss\VssApiIndirect.h	   228          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   229          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   230          0x21    --鐮佸崟宸茬粡琚敓鎴愯繃
; ..\Vss\VssApiIndirect.h	   231  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   232  static inline vss_uint32 VssGenerateKeyByCode(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag)
; ..\Vss\VssApiIndirect.h	   233  {
; ..\Vss\VssApiIndirect.h	   234      vss_uint32 (*api_func_raw)(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag) = 0;
; ..\Vss\VssApiIndirect.h	   235  
; ..\Vss\VssApiIndirect.h	   236      api_func_raw = (vss_uint32 (*)(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenerateKeyByCode));
; ..\Vss\VssApiIndirect.h	   237      return api_func_raw(len, code, szKeyIdList, AutoSetWroteFlag);
; ..\Vss\VssApiIndirect.h	   238  }
; ..\Vss\VssApiIndirect.h	   239  
; ..\Vss\VssApiIndirect.h	   240  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   241  鎽樿: 闅忔満鐢熸垚ECC闈炲绉板瘑閽ュ骞跺鍑烘帴鍙�
; ..\Vss\VssApiIndirect.h	   242  
; ..\Vss\VssApiIndirect.h	   243  鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   244        szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   245        szSK[out]     --绉侀挜SK锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   246  
; ..\Vss\VssApiIndirect.h	   247  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   248          0x04    --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   249          0x08    --璁＄畻澶辫触
; ..\Vss\VssApiIndirect.h	   250          0x18    --绠楁硶涓嶆敮鎸�
; ..\Vss\VssApiIndirect.h	   251  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   252  static inline vss_uint32 VssECCGenKey(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK)
; ..\Vss\VssApiIndirect.h	   253  {
; ..\Vss\VssApiIndirect.h	   254      vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK) = 0;
; ..\Vss\VssApiIndirect.h	   255  
; ..\Vss\VssApiIndirect.h	   256      api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCGenKey));
; ..\Vss\VssApiIndirect.h	   257      return api_func_raw(szX, szY, szSK);
; ..\Vss\VssApiIndirect.h	   258  }
; ..\Vss\VssApiIndirect.h	   259  
; ..\Vss\VssApiIndirect.h	   260  
; ..\Vss\VssApiIndirect.h	   261  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   262  鎽樿: 闅忔満鐢熸垚ECC闈炲绉板瘑閽ュ骞跺瓨鍌ㄦ帴鍙�
; ..\Vss\VssApiIndirect.h	   263  
; ..\Vss\VssApiIndirect.h	   264  鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   265        szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   266  
; ..\Vss\VssApiIndirect.h	   267  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   268          0x04    --szX鎴杝zY浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   269          0x08    --璁＄畻澶辫触
; ..\Vss\VssApiIndirect.h	   270          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   271          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   272  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   273  static inline vss_uint32 VssECCGenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
; ..\Vss\VssApiIndirect.h	   274  {
; ..\Vss\VssApiIndirect.h	   275      vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY) = 0;
; ..\Vss\VssApiIndirect.h	   276  
; ..\Vss\VssApiIndirect.h	   277      api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCGenKeyIndex));
; ..\Vss\VssApiIndirect.h	   278      return api_func_raw(szX, szY);
; ..\Vss\VssApiIndirect.h	   279  }
; ..\Vss\VssApiIndirect.h	   280  
; ..\Vss\VssApiIndirect.h	   281  /********************************************************************************
; ..\Vss\VssApiIndirect.h	   282  鎽樿: 璇佷功瀵煎叆
; ..\Vss\VssApiIndirect.h	   283  
; ..\Vss\VssApiIndirect.h	   284  鍙傛暟:nCertType[in]    --璇佷功绫诲瀷: 0:鏍硅瘉涔�;1:韬唤璇佷功
; ..\Vss\VssApiIndirect.h	   285  	nLen[in]         --璇佷功闀垮害
; ..\Vss\VssApiIndirect.h	   286  	szCert[in]        --璇佷功鏁版嵁
; ..\Vss\VssApiIndirect.h	   287  
; ..\Vss\VssApiIndirect.h	   288  杩斿洖鍊硷細0         --鎴愬姛
; ..\Vss\VssApiIndirect.h	   289          0x04      --浼犲叆绌烘寚閽堟垨璇佷功绫诲瀷涓嶄负0-1
; ..\Vss\VssApiIndirect.h	   290          0x12      --璇佷功闀垮害闈炴硶锛屾牴璇佷功144锛岃韩浠借瘉涔�176
; ..\Vss\VssApiIndirect.h	   291          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   292          0x1D      --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   293  ********************************************************************************/
; ..\Vss\VssApiIndirect.h	   294  static inline vss_uint32 VssCertImport(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert)
; ..\Vss\VssApiIndirect.h	   295  {
; ..\Vss\VssApiIndirect.h	   296      vss_uint32 (*api_func_raw)(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert) = 0;
; ..\Vss\VssApiIndirect.h	   297  
; ..\Vss\VssApiIndirect.h	   298      api_func_raw = (vss_uint32 (*)(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertImport));
; ..\Vss\VssApiIndirect.h	   299      return api_func_raw(nCertType, nLen, szCert);
; ..\Vss\VssApiIndirect.h	   300  }
; ..\Vss\VssApiIndirect.h	   301  
; ..\Vss\VssApiIndirect.h	   302  /********************************************************************************
; ..\Vss\VssApiIndirect.h	   303  鎽樿: 璇佷功璇诲彇
; ..\Vss\VssApiIndirect.h	   304  鍙傛暟: nCertType[in]    --璇佷功绫诲瀷: 0:鏍硅瘉涔�;1:韬唤璇佷功
; ..\Vss\VssApiIndirect.h	   305  nCertLen[out]    --瀵煎嚭璇佷功闀垮害
; ..\Vss\VssApiIndirect.h	   306  szCert[out]      --瀵煎嚭璇佷功鏁版嵁
; ..\Vss\VssApiIndirect.h	   307  	info[out]         --杈撳嚭16瀛楄妭鎸囧畾淇℃伅鏍煎紡
; ..\Vss\VssApiIndirect.h	   308                       璇佷功绫诲瀷  4B
; ..\Vss\VssApiIndirect.h	   309                       鏈夋晥鏃ユ湡  4B 鏍煎紡涓篩YMMDD
; ..\Vss\VssApiIndirect.h	   310                       璇佷功搴忓彿  4B
; ..\Vss\VssApiIndirect.h	   311                       璇佷功灞炴��  4B [绠楁硶锛屾牴绱㈠紩锛岄鐣欙紝棰勭暀]
; ..\Vss\VssApiIndirect.h	   312                                 绠楁硶锛�0-鍥藉瘑锛�1-鍥介檯
; ..\Vss\VssApiIndirect.h	   313                                 鏍圭储寮曪細0x10-娴嬭瘯鍥藉瘑鏍�
; ..\Vss\VssApiIndirect.h	   314                                         0x11-娴嬭瘯鍥介檯鏍�
; ..\Vss\VssApiIndirect.h	   315                                         0x20-棰勭敓浜у浗瀵嗘牴
; ..\Vss\VssApiIndirect.h	   316                                         0x21-棰勭敓浜у浗闄呮牴
; ..\Vss\VssApiIndirect.h	   317                                         0x30-鐢熶骇鍥藉瘑鏍�
; ..\Vss\VssApiIndirect.h	   318                                         0x31-鐢熶骇鍥介檯鏍�
; ..\Vss\VssApiIndirect.h	   319  
; ..\Vss\VssApiIndirect.h	   320  杩斿洖鍊硷細0         --鎴愬姛
; ..\Vss\VssApiIndirect.h	   321          0x04      --浼犲叆绌烘寚閽堟垨璇佷功绫诲瀷涓嶄负0-1
; ..\Vss\VssApiIndirect.h	   322          0x0E      --璇佷功涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   323          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   324          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   325  ********************************************************************************/
; ..\Vss\VssApiIndirect.h	   326  static inline vss_uint32 VssCertExport(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info)
; ..\Vss\VssApiIndirect.h	   327  {
; ..\Vss\VssApiIndirect.h	   328      vss_uint32 (*api_func_raw)(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info) = 0;
; ..\Vss\VssApiIndirect.h	   329  
; ..\Vss\VssApiIndirect.h	   330      api_func_raw = (vss_uint32 (*)(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertExport));
; ..\Vss\VssApiIndirect.h	   331      return api_func_raw(nCertType, nCertLen, szCert, info);
; ..\Vss\VssApiIndirect.h	   332  }
; ..\Vss\VssApiIndirect.h	   333  
; ..\Vss\VssApiIndirect.h	   334  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   335  鎽樿: 瀵煎叆鏄庢枃浼氳瘽瀵嗛挜
; ..\Vss\VssApiIndirect.h	   336  
; ..\Vss\VssApiIndirect.h	   337  鍙傛暟:nKeylen[in]  --瀵嗛挜鐨勯暱搴︼紝瀹氶暱16
; ..\Vss\VssApiIndirect.h	   338      szKey[in]    --瀵嗛挜鍊�
; ..\Vss\VssApiIndirect.h	   339  
; ..\Vss\VssApiIndirect.h	   340  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   341          0x04    --瀵嗛挜涓虹┖鎸囬拡
; ..\Vss\VssApiIndirect.h	   342          0x19    --瀵嗛挜闀垮害涓嶄负16
; ..\Vss\VssApiIndirect.h	   343          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   344          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   345  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   346  static inline vss_uint32 VssImportSessKey(vss_uint32 nKeyLen, vss_uint8* szKey)
; ..\Vss\VssApiIndirect.h	   347  {
; ..\Vss\VssApiIndirect.h	   348      vss_uint32 (*api_func_raw)(vss_uint32 nKeyLen, vss_uint8* szKey) = 0;
; ..\Vss\VssApiIndirect.h	   349  
; ..\Vss\VssApiIndirect.h	   350      api_func_raw = (vss_uint32 (*)(vss_uint32 nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssImportSessKey));
; ..\Vss\VssApiIndirect.h	   351      return api_func_raw(nKeyLen, szKey);
; ..\Vss\VssApiIndirect.h	   352  }
; ..\Vss\VssApiIndirect.h	   353  
; ..\Vss\VssApiIndirect.h	   354  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   355  鎽樿: 璇诲彇浼氳瘽瀵嗛挜鏄庢枃
; ..\Vss\VssApiIndirect.h	   356  
; ..\Vss\VssApiIndirect.h	   357  鍙傛暟: index[in]    --瀵嗛挜瀛樺偍鐨勭储寮曞彿锛�0-褰撳墠閫氫俊瀵嗛挜锛�1-澶囦唤閫氫俊瀵嗛挜
; ..\Vss\VssApiIndirect.h	   358      nKeylen[out]  --瀵嗛挜鐨勯暱搴�
; ..\Vss\VssApiIndirect.h	   359      szKey[out]    --瀵嗛挜鍊�
; ..\Vss\VssApiIndirect.h	   360  
; ..\Vss\VssApiIndirect.h	   361  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   362          0x04    --瀵嗛挜闀垮害鎴栧瘑閽ュ�间负绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   363          0x05    --鎸囧畾瀵嗛挜涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   364          0x0F    --瀵嗛挜绱㈠紩鍙烽潪娉曪紝涓嶄负0-1
; ..\Vss\VssApiIndirect.h	   365          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   366          0x1C    --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   367  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   368  static inline vss_uint32 VssExportSessKey(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey)
; ..\Vss\VssApiIndirect.h	   369  {
; ..\Vss\VssApiIndirect.h	   370      vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey) = 0;
; ..\Vss\VssApiIndirect.h	   371  
; ..\Vss\VssApiIndirect.h	   372      api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportSessKey));
; ..\Vss\VssApiIndirect.h	   373      return api_func_raw(index, nKeyLen, szKey);
; ..\Vss\VssApiIndirect.h	   374  }
; ..\Vss\VssApiIndirect.h	   375  
; ..\Vss\VssApiIndirect.h	   376  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   377  鎽樿: 淇敼瀵嗛挜鐨勬縺娲诲睘鎬�
; ..\Vss\VssApiIndirect.h	   378  
; ..\Vss\VssApiIndirect.h	   379  鍙傛暟: keyId[in]   --瀵嗛挜ID锛堝彧閽堝5-8鍙烽�氫俊瀵嗛挜鏈夋晥锛�
; ..\Vss\VssApiIndirect.h	   380       valid[in]    --鍙敤鏍囪瘑: 0-涓嶅彲鐢紱1-鍙敤
; ..\Vss\VssApiIndirect.h	   381  
; ..\Vss\VssApiIndirect.h	   382  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   383          0x04    --鍙敤鏍囪瘑涓嶆槸鎸囧畾鍊�
; ..\Vss\VssApiIndirect.h	   384          0x05    --瀵嗛挜涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   385          0x0F    --瀵嗛挜ID闈炴硶锛屼笉涓�5-8
; ..\Vss\VssApiIndirect.h	   386          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   387          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   388  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   389  static inline vss_uint32 VssSetKeyActive(vss_uint32 keyId, vss_uint32 valid)
; ..\Vss\VssApiIndirect.h	   390  {
; ..\Vss\VssApiIndirect.h	   391      vss_uint32 (*api_func_raw)(vss_uint32 keyId, vss_uint32 valid) = 0;
; ..\Vss\VssApiIndirect.h	   392  
; ..\Vss\VssApiIndirect.h	   393      api_func_raw = (vss_uint32 (*)(vss_uint32 keyId, vss_uint32 valid))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSetKeyActive));
; ..\Vss\VssApiIndirect.h	   394      return api_func_raw(keyId, valid);
; ..\Vss\VssApiIndirect.h	   395  }
; ..\Vss\VssApiIndirect.h	   396  
; ..\Vss\VssApiIndirect.h	   397  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   398  鎽樿: 璇诲彇闃茬洍瀵嗛挜鏄庢枃锛岄槻鐩楀瘑閽ヨ嚦澶氫粎涓�鏉℃湁鏁堬紝濡傛灉瀛樺湪闃茬洍瀵嗛挜杩斿洖瀵瑰簲瀵嗛挜锛屽鏋滀笉瀛樺湪杩斿洖05--瀵嗛挜涓嶅彲鐢�
; ..\Vss\VssApiIndirect.h	   399  
; ..\Vss\VssApiIndirect.h	   400  鍙傛暟:index[in]	  --瀵嗛挜绱㈠紩锛�0-3
; ..\Vss\VssApiIndirect.h	   401  	nKeyLen[out]  --瀵嗛挜鐨勯暱搴�
; ..\Vss\VssApiIndirect.h	   402      szKey[out]    --瀵嗛挜鍊�
; ..\Vss\VssApiIndirect.h	   403  
; ..\Vss\VssApiIndirect.h	   404  杩斿洖鍊硷細0        --鎴愬姛
; ..\Vss\VssApiIndirect.h	   405          0x04     --瀵嗛挜鎴栧瘑閽ラ暱搴︿紶鍏ョ┖鎸囬拡
; ..\Vss\VssApiIndirect.h	   406          0x05     --鎸囧畾瀵嗛挜涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   407          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   408          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   409  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   410  static inline vss_uint32 VssExportAtKey(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey)
; ..\Vss\VssApiIndirect.h	   411  {
; ..\Vss\VssApiIndirect.h	   412      vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey) = 0;
; ..\Vss\VssApiIndirect.h	   413  
; ..\Vss\VssApiIndirect.h	   414      api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportAtKey));
; ..\Vss\VssApiIndirect.h	   415      return api_func_raw(index, nKeyLen, szKey);
; ..\Vss\VssApiIndirect.h	   416  }
; ..\Vss\VssApiIndirect.h	   417  
; ..\Vss\VssApiIndirect.h	   418  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   419  鎽樿: 璇诲彇闃茬洍PIN鏄庢枃
; ..\Vss\VssApiIndirect.h	   420  
; ..\Vss\VssApiIndirect.h	   421  鍙傛暟: nPinLen[out]  --PIN鐨勯暱搴�
; ..\Vss\VssApiIndirect.h	   422      szPin[out]    --PIN鍊�
; ..\Vss\VssApiIndirect.h	   423  
; ..\Vss\VssApiIndirect.h	   424  杩斿洖鍊硷細0        --鎴愬姛
; ..\Vss\VssApiIndirect.h	   425          0x04     --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   426          0x05     --鎸囧畾瀵嗛挜涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   427          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   428          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   429  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   430  static inline vss_uint32 VssExportAtPin(vss_uint32* nPinLen, vss_uint8* szPin)
; ..\Vss\VssApiIndirect.h	   431  {
; ..\Vss\VssApiIndirect.h	   432      vss_uint32 (*api_func_raw)(vss_uint32* nPinLen, vss_uint8* szPin) = 0;
; ..\Vss\VssApiIndirect.h	   433  
; ..\Vss\VssApiIndirect.h	   434      api_func_raw = (vss_uint32 (*)(vss_uint32* nPinLen, vss_uint8* szPin))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportAtPin));
; ..\Vss\VssApiIndirect.h	   435      return api_func_raw(nPinLen, szPin);
; ..\Vss\VssApiIndirect.h	   436  }
; ..\Vss\VssApiIndirect.h	   437  
; ..\Vss\VssApiIndirect.h	   438  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   439  鎽樿: 鍙嶉杞﹁締瀹夊叏鐮佽緭鍏ユ儏鍐�
; ..\Vss\VssApiIndirect.h	   440  
; ..\Vss\VssApiIndirect.h	   441  鍙傛暟: szFeedback[out]    --杞﹁締瀹夊叏鐮佸弽棣堝�硷紝瀹氶暱32瀛楄妭
; ..\Vss\VssApiIndirect.h	   442                          濡傛灉32瀛楄妭杞﹁締瀹夊叏鐮佹病缁欏畨鍏ㄧ畻娉曞寘杈撳叆杩囷紝璇ユ帴鍙ｅ悙鍑哄�间负鍏‵F
; ..\Vss\VssApiIndirect.h	   443  濡傛灉32瀛楄妭杞﹁締瀹夊叏鐮佸凡缁忚緭鍏ュ畨鍏ㄧ畻娉曞寘锛屽唴瀹瑰涓嬶細
; ..\Vss\VssApiIndirect.h	   444  鍥藉瘑绠楁硶锛氳鎺ュ彛鍚愬嚭鍊间负鍏�00
; ..\Vss\VssApiIndirect.h	   445  鍥介檯绠楁硶锛氳鎺ュ彛鍚愬嚭鍊间负鍓�31瀛楄妭鍏�00锛屾渶鍚庝竴瀛楄妭0x01
; ..\Vss\VssApiIndirect.h	   446  
; ..\Vss\VssApiIndirect.h	   447  杩斿洖鍊硷細0        --鎴愬姛
; ..\Vss\VssApiIndirect.h	   448          0x04     --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   449          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   450          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   451  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   452  static inline vss_uint32 VssKeyCodeFeedback(vss_uint8* szFeedback)
; ..\Vss\VssApiIndirect.h	   453  {
; ..\Vss\VssApiIndirect.h	   454      vss_uint32 (*api_func_raw)(vss_uint8* szFeedback) = 0;
; ..\Vss\VssApiIndirect.h	   455  
; ..\Vss\VssApiIndirect.h	   456      api_func_raw = (vss_uint32 (*)(vss_uint8* szFeedback))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssKeyCodeFeedback));
	movh.a	a15,#32773
	ld.w	d15,[a15]@los(0x800480b0)
.L75:
	mov.a	a15,d15
.L76:

; ..\Vss\Vss.c	    28      ret = VssKeyCodeFeedback(&vsnChkResult);
	mov.aa	a4,a10
	calli	a15
.L50:
	extr.u	d2,d2,#0,#8
.L62:

; ..\Vss\Vss.c	    29      
; ..\Vss\Vss.c	    30      // VssGenerateKeyByCode(32,vsn_code,idList,0);
; ..\Vss\Vss.c	    31      if(0u == ret)
	jne	d2,#0,.L2
.L77:

; ..\Vss\Vss.c	    32      {
; ..\Vss\Vss.c	    33          if((0x00u == vsnChkResult[30]))
	ld.bu	d15,[a10]30
.L78:
	jne	d15,#0,.L3
.L55:

; ..\Vss\VssApiIndirect.h	     1  /***************************************************
; ..\Vss\VssApiIndirect.h	     2   * Introduction: VSS涓氬姟鎺ュ彛
; ..\Vss\VssApiIndirect.h	     3   * author: wujialin
; ..\Vss\VssApiIndirect.h	     4   * date created: 2020-2-1
; ..\Vss\VssApiIndirect.h	     5   * date modified: 2021-4-21
; ..\Vss\VssApiIndirect.h	     6   * version: V1.4
; ..\Vss\VssApiIndirect.h	     7   * recently modified by: wujialin
; ..\Vss\VssApiIndirect.h	     8   *
; ..\Vss\VssApiIndirect.h	     9   * Copyright (C) 2017, 2021, Thinktech, Inc.
; ..\Vss\VssApiIndirect.h	    10   ***************************************************/
; ..\Vss\VssApiIndirect.h	    11  #ifndef __VSS_API_H__
; ..\Vss\VssApiIndirect.h	    12  #define __VSS_API_H__
; ..\Vss\VssApiIndirect.h	    13  
; ..\Vss\VssApiIndirect.h	    14  #include <stdio.h>
; ..\Vss\VssApiIndirect.h	    15  #include <stdint.h>
; ..\Vss\VssApiIndirect.h	    16  
; ..\Vss\VssApiIndirect.h	    17  
; ..\Vss\VssApiIndirect.h	    18  /*---functions---*/
; ..\Vss\VssApiIndirect.h	    19  #ifdef __cplusplus
; ..\Vss\VssApiIndirect.h	    20  extern "C" {
; ..\Vss\VssApiIndirect.h	    21  #endif
; ..\Vss\VssApiIndirect.h	    22  
; ..\Vss\VssApiIndirect.h	    23  #include "vsstype.h"
; ..\Vss\VssApiIndirect.h	    24  
; ..\Vss\VssApiIndirect.h	    25  
; ..\Vss\VssApiIndirect.h	    26  enum ALG_TYPE {
; ..\Vss\VssApiIndirect.h	    27  	ALG_GJ = 1,	
; ..\Vss\VssApiIndirect.h	    28  	ALG_GM,
; ..\Vss\VssApiIndirect.h	    29  };
; ..\Vss\VssApiIndirect.h	    30  
; ..\Vss\VssApiIndirect.h	    31  enum ENV_TYPE {
; ..\Vss\VssApiIndirect.h	    32  	ENV_QA = 1,
; ..\Vss\VssApiIndirect.h	    33  	ENV_PP,
; ..\Vss\VssApiIndirect.h	    34  	ENV_P,
; ..\Vss\VssApiIndirect.h	    35  };
; ..\Vss\VssApiIndirect.h	    36  
; ..\Vss\VssApiIndirect.h	    37  enum{
; ..\Vss\VssApiIndirect.h	    38  	CERT_TYPE_ROOT = 0,
; ..\Vss\VssApiIndirect.h	    39  	CERT_TYPE_USR,
; ..\Vss\VssApiIndirect.h	    40  };
; ..\Vss\VssApiIndirect.h	    41  
; ..\Vss\VssApiIndirect.h	    42  #define ROOT_CERT_SIZE  144
; ..\Vss\VssApiIndirect.h	    43  #define USER_CERT_SIZE  176
; ..\Vss\VssApiIndirect.h	    44  #define ECU_CERT_SIZE   164
; ..\Vss\VssApiIndirect.h	    45  
; ..\Vss\VssApiIndirect.h	    46  enum CERT_TYPE {
; ..\Vss\VssApiIndirect.h	    47    CERTTYPE_ROOT    = 0x10,
; ..\Vss\VssApiIndirect.h	    48    CERTTYPE_ECU     = 0x20,
; ..\Vss\VssApiIndirect.h	    49    CERTTYPE_USR     = 0x30,
; ..\Vss\VssApiIndirect.h	    50    CERTTYPE_USRREQ  = 0x31,
; ..\Vss\VssApiIndirect.h	    51  };
; ..\Vss\VssApiIndirect.h	    52  
; ..\Vss\VssApiIndirect.h	    53  #define INNER_KEY_MODE 0
; ..\Vss\VssApiIndirect.h	    54  #define OUTER_KEY_MODE 1
; ..\Vss\VssApiIndirect.h	    55  #define CALC_ENC 0
; ..\Vss\VssApiIndirect.h	    56  #define CALC_DEC 1
; ..\Vss\VssApiIndirect.h	    57  #define PAD_NO_FORCE 0
; ..\Vss\VssApiIndirect.h	    58  #define PAD_FORCE 1
; ..\Vss\VssApiIndirect.h	    59  #define HASH_NO_CALC 0
; ..\Vss\VssApiIndirect.h	    60  #define HASH_CALC 1
; ..\Vss\VssApiIndirect.h	    61  
; ..\Vss\VssApiIndirect.h	    62  /* Function Table Base Address */
; ..\Vss\VssApiIndirect.h	    63  #define VSSAPI_FUNC_TABLE_BASE          0x80048000//0xA007A800
; ..\Vss\VssApiIndirect.h	    64  
; ..\Vss\VssApiIndirect.h	    65  /* Function Index In Func Table */
; ..\Vss\VssApiIndirect.h	    66  enum{
; ..\Vss\VssApiIndirect.h	    67  	VSSAPI_VssAESCMac = 0,
; ..\Vss\VssApiIndirect.h	    68  	VSSAPI_VssAESCMacIndex,
; ..\Vss\VssApiIndirect.h	    69  	VSSAPI_VssAESCalc,
; ..\Vss\VssApiIndirect.h	    70  	VSSAPI_VssAESCalcIndex,
; ..\Vss\VssApiIndirect.h	    71  	VSSAPI_VssAESMac,
; ..\Vss\VssApiIndirect.h	    72  	VSSAPI_VssAsymGenKey,
; ..\Vss\VssApiIndirect.h	    73  	VSSAPI_VssAsymGenKeyIndex,
; ..\Vss\VssApiIndirect.h	    74  	VSSAPI_VssAsymmCalc,
; ..\Vss\VssApiIndirect.h	    75  	VSSAPI_VssAsymmCalcIndex,
; ..\Vss\VssApiIndirect.h	    76  	VSSAPI_VssCMac,
; ..\Vss\VssApiIndirect.h	    77  	VSSAPI_VssCMacIndex,
; ..\Vss\VssApiIndirect.h	    78  	VSSAPI_VssCalcEcu,
; ..\Vss\VssApiIndirect.h	    79  	VSSAPI_VssCalcFinishHmac,
; ..\Vss\VssApiIndirect.h	    80  	VSSAPI_VssCertExport,
; ..\Vss\VssApiIndirect.h	    81  	VSSAPI_VssCertImport,
; ..\Vss\VssApiIndirect.h	    82  	VSSAPI_VssCertPkEnc,
; ..\Vss\VssApiIndirect.h	    83  	VSSAPI_VssChipRead,
; ..\Vss\VssApiIndirect.h	    84  	VSSAPI_VssChipWrite,
; ..\Vss\VssApiIndirect.h	    85  	VSSAPI_VssCryptoInit,
; ..\Vss\VssApiIndirect.h	    86  	VSSAPI_VssECCCalc,
; ..\Vss\VssApiIndirect.h	    87  	VSSAPI_VssECCCalcIndex,
; ..\Vss\VssApiIndirect.h	    88  	VSSAPI_VssECCGenKey,
; ..\Vss\VssApiIndirect.h	    89  	VSSAPI_VssECCGenKeyIndex,
; ..\Vss\VssApiIndirect.h	    90  	VSSAPI_VssECCSign,
; ..\Vss\VssApiIndirect.h	    91  	VSSAPI_VssECCSignIndex,
; ..\Vss\VssApiIndirect.h	    92  	VSSAPI_VssECCVerify,
; ..\Vss\VssApiIndirect.h	    93  	VSSAPI_VssExportAtKey,
; ..\Vss\VssApiIndirect.h	    94  	VSSAPI_VssExportAtPin,
; ..\Vss\VssApiIndirect.h	    95  	VSSAPI_VssExportKey,
; ..\Vss\VssApiIndirect.h	    96  	VSSAPI_VssExportSessKey,
; ..\Vss\VssApiIndirect.h	    97  	VSSAPI_VssGenCertReq,
; ..\Vss\VssApiIndirect.h	    98  	VSSAPI_VssGenRandom,
; ..\Vss\VssApiIndirect.h	    99  	VSSAPI_VssGenSessionKey,
; ..\Vss\VssApiIndirect.h	   100  	VSSAPI_VssGenerateKeyByCode,
; ..\Vss\VssApiIndirect.h	   101  	VSSAPI_VssGetAlgFlag,
; ..\Vss\VssApiIndirect.h	   102  	VSSAPI_VssGetCertInfo,
; ..\Vss\VssApiIndirect.h	   103  	VSSAPI_VssGetChipID,
; ..\Vss\VssApiIndirect.h	   104  	VSSAPI_VssGetWroteFlag,
; ..\Vss\VssApiIndirect.h	   105  	VSSAPI_VssHMAC,
; ..\Vss\VssApiIndirect.h	   106  	VSSAPI_VssHash,
; ..\Vss\VssApiIndirect.h	   107  	VSSAPI_VssHashFinal,
; ..\Vss\VssApiIndirect.h	   108  	VSSAPI_VssHashInit,
; ..\Vss\VssApiIndirect.h	   109  	VSSAPI_VssHashUpdate,
; ..\Vss\VssApiIndirect.h	   110  	VSSAPI_VssImportSessKey,
; ..\Vss\VssApiIndirect.h	   111  	VSSAPI_VssKeyCodeFeedback,
; ..\Vss\VssApiIndirect.h	   112  	VSSAPI_VssMac,
; ..\Vss\VssApiIndirect.h	   113  	VSSAPI_VssResetSessionKey,
; ..\Vss\VssApiIndirect.h	   114  	VSSAPI_VssSM2Calc,
; ..\Vss\VssApiIndirect.h	   115  	VSSAPI_VssSM2CalcIndex,
; ..\Vss\VssApiIndirect.h	   116  	VSSAPI_VssSM2GenKey,
; ..\Vss\VssApiIndirect.h	   117  	VSSAPI_VssSM2GenKeyIndex,
; ..\Vss\VssApiIndirect.h	   118  	VSSAPI_VssSM2Sign,
; ..\Vss\VssApiIndirect.h	   119  	VSSAPI_VssSM2SignIndex,
; ..\Vss\VssApiIndirect.h	   120  	VSSAPI_VssSM2Verify,
; ..\Vss\VssApiIndirect.h	   121  	VSSAPI_VssSM4CMac,
; ..\Vss\VssApiIndirect.h	   122  	VSSAPI_VssSM4CMacIndex,
; ..\Vss\VssApiIndirect.h	   123  	VSSAPI_VssSM4Calc,
; ..\Vss\VssApiIndirect.h	   124  	VSSAPI_VssSM4CalcIndex,
; ..\Vss\VssApiIndirect.h	   125  	VSSAPI_VssSM4Mac,
; ..\Vss\VssApiIndirect.h	   126  	VSSAPI_VssSetKeyActive,
; ..\Vss\VssApiIndirect.h	   127  	VSSAPI_VssSetWroteFlag,
; ..\Vss\VssApiIndirect.h	   128  	VSSAPI_VssSign,
; ..\Vss\VssApiIndirect.h	   129  	VSSAPI_VssSignData,
; ..\Vss\VssApiIndirect.h	   130  	VSSAPI_VssSignIndex,
; ..\Vss\VssApiIndirect.h	   131  	VSSAPI_VssSignVerify,
; ..\Vss\VssApiIndirect.h	   132  	VSSAPI_VssStSymmCalc,
; ..\Vss\VssApiIndirect.h	   133  	VSSAPI_VssSymmCalc,
; ..\Vss\VssApiIndirect.h	   134  	VSSAPI_VssSymmCalcIndex,
; ..\Vss\VssApiIndirect.h	   135  	VSSAPI_VssUpdateEnvironment,
; ..\Vss\VssApiIndirect.h	   136  	VSSAPI_VssUpdateMasterKey,
; ..\Vss\VssApiIndirect.h	   137  	VSSAPI_VssVerifyCert,
; ..\Vss\VssApiIndirect.h	   138  	VSSAPI_VssVerifyCertValid,
; ..\Vss\VssApiIndirect.h	   139  	VSSAPI_VssVerifyEcuSign,
; ..\Vss\VssApiIndirect.h	   140  	VSSAPI_VssVerifyToolCert,
; ..\Vss\VssApiIndirect.h	   141  	VSSAPI_VssZucCalc,
; ..\Vss\VssApiIndirect.h	   142  	VSSAPI_VssZucCalcData,
; ..\Vss\VssApiIndirect.h	   143  	VSSAPI_VssZucSetKey,
; ..\Vss\VssApiIndirect.h	   144  	VSSAPI_VssGetChipVersion,
; ..\Vss\VssApiIndirect.h	   145  	VSSAPI_VssGetChipState,
; ..\Vss\VssApiIndirect.h	   146  	VSSAPI_VssGetVersion,
; ..\Vss\VssApiIndirect.h	   147  	VSSAPI_VssVerifySignCertValid,
; ..\Vss\VssApiIndirect.h	   148  	VSSAPI_VssGetEnvironment,
; ..\Vss\VssApiIndirect.h	   149  	VSSAPI_VssGetKeyActive,
; ..\Vss\VssApiIndirect.h	   150  
; ..\Vss\VssApiIndirect.h	   151  	VSSAPI_FUN_COUNT
; ..\Vss\VssApiIndirect.h	   152  };
; ..\Vss\VssApiIndirect.h	   153  
; ..\Vss\VssApiIndirect.h	   154  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   155  鎽樿: 绠楁硶搴撳垵濮嬪寲锛岄�氳繃璇诲彇FLASH鎸囧畾鐨勭畻娉曟潵鍒濆鍖栫郴缁熺畻娉曟爣璇嗭紝榛樿绠楁硶涓哄浗瀵嗙畻娉�
; ..\Vss\VssApiIndirect.h	   156     闇�瑕侀�氳繃璇ユ柟娉曞皢FLASH璇诲啓鐨勬帴鍙ｆ寚閽堟敞鍐屽埌VSS涓紝VSS鍙互閫氳繃璇ユ帴鍙ｈ鍐橣LASH涓寚瀹氱殑瀵嗛挜瀛樺偍鍖哄煙銆�
; ..\Vss\VssApiIndirect.h	   157     鐢变簬闈炲绉扮畻娉曠殑杩愮畻鏃堕棿杈冮暱锛堜緥濡傚畬鏁磋蒋绠楁硶楠岀鍙兘闇�瑕�500ms锛夛紝杩欏氨闇�瑕佺畻娉曞唴閮ㄨ繍绠楁椂鍒嗘澶嶄綅WDT鐨勬椂閽燂紝鍚﹀垯浼氬鑷碝CU澶嶄綅銆�
; ..\Vss\VssApiIndirect.h	   158  
; ..\Vss\VssApiIndirect.h	   159  鍙傛暟: type[in]    --鎸囧畾绠楁硶搴撶殑杩愮畻绫诲瀷锛�0-杞畻娉曪紱1-M300鑺墖
; ..\Vss\VssApiIndirect.h	   160  	group[in]  --棰勭暀鍙傛暟锛屾き鍦嗘洸鐜嘔D锛�0-NIST prime256v1; 1-brainpoolP256r1
; ..\Vss\VssApiIndirect.h	   161             鐩墠鍙敮鎸�0-NIST prime256v1鏇茬巼
; ..\Vss\VssApiIndirect.h	   162       flashCb[in]  --璇诲啓FLASH瀵嗛挜瀛樺偍鍖哄煙鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
; ..\Vss\VssApiIndirect.h	   163       wdtCb[in]   --澶嶄綅鐪嬮棬鐙楄鏃跺櫒鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
; ..\Vss\VssApiIndirect.h	   164  
; ..\Vss\VssApiIndirect.h	   165  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   166          0x04    --杩愮畻绫诲瀷涓嶆槸鎸囧畾鐨勬暟鍊�
; ..\Vss\VssApiIndirect.h	   167          0x1C    --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   168  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   169  static inline vss_uint32 VssCryptoInit(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb)
; ..\Vss\VssApiIndirect.h	   170  {
; ..\Vss\VssApiIndirect.h	   171      vss_uint32 (*api_func_raw)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb) = 0;
; ..\Vss\VssApiIndirect.h	   172  
; ..\Vss\VssApiIndirect.h	   173      api_func_raw = (vss_uint32 (*)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCryptoInit));
; ..\Vss\VssApiIndirect.h	   174      return api_func_raw(type, group, flashCb, wdtCb);
; ..\Vss\VssApiIndirect.h	   175  }
; ..\Vss\VssApiIndirect.h	   176  
; ..\Vss\VssApiIndirect.h	   177  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   178  鎽樿: 闅忔満鐢熸垚SM2闈炲绉板瘑閽ュ骞跺鍑烘帴鍙�
; ..\Vss\VssApiIndirect.h	   179  
; ..\Vss\VssApiIndirect.h	   180  鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   181        szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   182        szSK[out]     --绉侀挜SK锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   183  
; ..\Vss\VssApiIndirect.h	   184  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   185          0x04    --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   186          0x08    --璁＄畻澶辫触
; ..\Vss\VssApiIndirect.h	   187          0x18    --绠楁硶涓嶆敮鎸�
; ..\Vss\VssApiIndirect.h	   188  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   189  static inline vss_uint32 VssSM2GenKey(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK)
; ..\Vss\VssApiIndirect.h	   190  {
; ..\Vss\VssApiIndirect.h	   191      vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK) = 0;
; ..\Vss\VssApiIndirect.h	   192  
; ..\Vss\VssApiIndirect.h	   193      api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2GenKey));
; ..\Vss\VssApiIndirect.h	   194      return api_func_raw(szX, szY, szSK);
; ..\Vss\VssApiIndirect.h	   195  }
; ..\Vss\VssApiIndirect.h	   196  
; ..\Vss\VssApiIndirect.h	   197  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   198  鎽樿: 闅忔満鐢熸垚SM2闈炲绉板瘑閽ュ骞跺瓨鍌ㄦ帴鍙�
; ..\Vss\VssApiIndirect.h	   199  
; ..\Vss\VssApiIndirect.h	   200  鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   201        szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   202  
; ..\Vss\VssApiIndirect.h	   203  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   204          0x04    --szX鎴杝zY浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   205          0x08    --璁＄畻澶辫触
; ..\Vss\VssApiIndirect.h	   206          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   207          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   208  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   209  static inline vss_uint32 VssSM2GenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
; ..\Vss\VssApiIndirect.h	   210  {
; ..\Vss\VssApiIndirect.h	   211      vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY) = 0;
; ..\Vss\VssApiIndirect.h	   212  
; ..\Vss\VssApiIndirect.h	   213      api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2GenKeyIndex));
; ..\Vss\VssApiIndirect.h	   214      return api_func_raw(szX, szY);
; ..\Vss\VssApiIndirect.h	   215  }
; ..\Vss\VssApiIndirect.h	   216  
; ..\Vss\VssApiIndirect.h	   217  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   218  鎽樿: 浠ヨ緭鍏ョ殑杞﹁締瀹夊叏鐮佷綔涓虹瀛愶紝鏍规嵁閰嶇疆鏂囦欢浜х敓绯诲垪鐨勫绉板瘑閽�
; ..\Vss\VssApiIndirect.h	   219  
; ..\Vss\VssApiIndirect.h	   220  鍙傛暟: len[in]     --杞﹁締瀹夊叏鐮侀暱搴︼紝鍥哄畾32
; ..\Vss\VssApiIndirect.h	   221       code[in]    --杞﹁締瀹夊叏鐮侊紝鍥哄畾32瀛楄妭锛岀敱绗�32瀛楄妭鎸囧畾绠楁硶锛屽綋璇ユ暟涓哄伓鏁版椂鎸囧畾涓哄浗瀵嗙畻娉曪紝涓哄鏁版椂鎸囧畾涓哄浗闄呯畻娉曪紙鍗砤lg=code[31]&1; 0-鍥藉瘑锛�1-鍥介檯锛�
; ..\Vss\VssApiIndirect.h	   222       szKeyIdList[out] --鐢熸垚鎴愬姛鐨勫瘑閽D娓呭崟锛屼緥濡傗��1,2,5鈥�
; ..\Vss\VssApiIndirect.h	   223       AutoSetWroteFlag[in] -璁�1鍦ㄥ姛鑳藉畬鎴愬悗鑷姩缃畐rote Flag涓�1锛涜0涓嶅仛浠讳綍鎿嶄綔
; ..\Vss\VssApiIndirect.h	   224  
; ..\Vss\VssApiIndirect.h	   225  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   226          0x04    --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   227          0x12    --瀹夊叏鐮侀暱搴︿笉涓�32
; ..\Vss\VssApiIndirect.h	   228          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   229          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   230          0x21    --鐮佸崟宸茬粡琚敓鎴愯繃
; ..\Vss\VssApiIndirect.h	   231  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   232  static inline vss_uint32 VssGenerateKeyByCode(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag)
; ..\Vss\VssApiIndirect.h	   233  {
; ..\Vss\VssApiIndirect.h	   234      vss_uint32 (*api_func_raw)(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag) = 0;
; ..\Vss\VssApiIndirect.h	   235  
; ..\Vss\VssApiIndirect.h	   236      api_func_raw = (vss_uint32 (*)(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenerateKeyByCode));
; ..\Vss\VssApiIndirect.h	   237      return api_func_raw(len, code, szKeyIdList, AutoSetWroteFlag);
; ..\Vss\VssApiIndirect.h	   238  }
; ..\Vss\VssApiIndirect.h	   239  
; ..\Vss\VssApiIndirect.h	   240  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   241  鎽樿: 闅忔満鐢熸垚ECC闈炲绉板瘑閽ュ骞跺鍑烘帴鍙�
; ..\Vss\VssApiIndirect.h	   242  
; ..\Vss\VssApiIndirect.h	   243  鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   244        szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   245        szSK[out]     --绉侀挜SK锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   246  
; ..\Vss\VssApiIndirect.h	   247  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   248          0x04    --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   249          0x08    --璁＄畻澶辫触
; ..\Vss\VssApiIndirect.h	   250          0x18    --绠楁硶涓嶆敮鎸�
; ..\Vss\VssApiIndirect.h	   251  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   252  static inline vss_uint32 VssECCGenKey(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK)
; ..\Vss\VssApiIndirect.h	   253  {
; ..\Vss\VssApiIndirect.h	   254      vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK) = 0;
; ..\Vss\VssApiIndirect.h	   255  
; ..\Vss\VssApiIndirect.h	   256      api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCGenKey));
; ..\Vss\VssApiIndirect.h	   257      return api_func_raw(szX, szY, szSK);
; ..\Vss\VssApiIndirect.h	   258  }
; ..\Vss\VssApiIndirect.h	   259  
; ..\Vss\VssApiIndirect.h	   260  
; ..\Vss\VssApiIndirect.h	   261  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   262  鎽樿: 闅忔満鐢熸垚ECC闈炲绉板瘑閽ュ骞跺瓨鍌ㄦ帴鍙�
; ..\Vss\VssApiIndirect.h	   263  
; ..\Vss\VssApiIndirect.h	   264  鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   265        szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
; ..\Vss\VssApiIndirect.h	   266  
; ..\Vss\VssApiIndirect.h	   267  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   268          0x04    --szX鎴杝zY浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   269          0x08    --璁＄畻澶辫触
; ..\Vss\VssApiIndirect.h	   270          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   271          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   272  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   273  static inline vss_uint32 VssECCGenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
; ..\Vss\VssApiIndirect.h	   274  {
; ..\Vss\VssApiIndirect.h	   275      vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY) = 0;
; ..\Vss\VssApiIndirect.h	   276  
; ..\Vss\VssApiIndirect.h	   277      api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCGenKeyIndex));
; ..\Vss\VssApiIndirect.h	   278      return api_func_raw(szX, szY);
; ..\Vss\VssApiIndirect.h	   279  }
; ..\Vss\VssApiIndirect.h	   280  
; ..\Vss\VssApiIndirect.h	   281  /********************************************************************************
; ..\Vss\VssApiIndirect.h	   282  鎽樿: 璇佷功瀵煎叆
; ..\Vss\VssApiIndirect.h	   283  
; ..\Vss\VssApiIndirect.h	   284  鍙傛暟:nCertType[in]    --璇佷功绫诲瀷: 0:鏍硅瘉涔�;1:韬唤璇佷功
; ..\Vss\VssApiIndirect.h	   285  	nLen[in]         --璇佷功闀垮害
; ..\Vss\VssApiIndirect.h	   286  	szCert[in]        --璇佷功鏁版嵁
; ..\Vss\VssApiIndirect.h	   287  
; ..\Vss\VssApiIndirect.h	   288  杩斿洖鍊硷細0         --鎴愬姛
; ..\Vss\VssApiIndirect.h	   289          0x04      --浼犲叆绌烘寚閽堟垨璇佷功绫诲瀷涓嶄负0-1
; ..\Vss\VssApiIndirect.h	   290          0x12      --璇佷功闀垮害闈炴硶锛屾牴璇佷功144锛岃韩浠借瘉涔�176
; ..\Vss\VssApiIndirect.h	   291          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   292          0x1D      --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   293  ********************************************************************************/
; ..\Vss\VssApiIndirect.h	   294  static inline vss_uint32 VssCertImport(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert)
; ..\Vss\VssApiIndirect.h	   295  {
; ..\Vss\VssApiIndirect.h	   296      vss_uint32 (*api_func_raw)(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert) = 0;
; ..\Vss\VssApiIndirect.h	   297  
; ..\Vss\VssApiIndirect.h	   298      api_func_raw = (vss_uint32 (*)(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertImport));
; ..\Vss\VssApiIndirect.h	   299      return api_func_raw(nCertType, nLen, szCert);
; ..\Vss\VssApiIndirect.h	   300  }
; ..\Vss\VssApiIndirect.h	   301  
; ..\Vss\VssApiIndirect.h	   302  /********************************************************************************
; ..\Vss\VssApiIndirect.h	   303  鎽樿: 璇佷功璇诲彇
; ..\Vss\VssApiIndirect.h	   304  鍙傛暟: nCertType[in]    --璇佷功绫诲瀷: 0:鏍硅瘉涔�;1:韬唤璇佷功
; ..\Vss\VssApiIndirect.h	   305  nCertLen[out]    --瀵煎嚭璇佷功闀垮害
; ..\Vss\VssApiIndirect.h	   306  szCert[out]      --瀵煎嚭璇佷功鏁版嵁
; ..\Vss\VssApiIndirect.h	   307  	info[out]         --杈撳嚭16瀛楄妭鎸囧畾淇℃伅鏍煎紡
; ..\Vss\VssApiIndirect.h	   308                       璇佷功绫诲瀷  4B
; ..\Vss\VssApiIndirect.h	   309                       鏈夋晥鏃ユ湡  4B 鏍煎紡涓篩YMMDD
; ..\Vss\VssApiIndirect.h	   310                       璇佷功搴忓彿  4B
; ..\Vss\VssApiIndirect.h	   311                       璇佷功灞炴��  4B [绠楁硶锛屾牴绱㈠紩锛岄鐣欙紝棰勭暀]
; ..\Vss\VssApiIndirect.h	   312                                 绠楁硶锛�0-鍥藉瘑锛�1-鍥介檯
; ..\Vss\VssApiIndirect.h	   313                                 鏍圭储寮曪細0x10-娴嬭瘯鍥藉瘑鏍�
; ..\Vss\VssApiIndirect.h	   314                                         0x11-娴嬭瘯鍥介檯鏍�
; ..\Vss\VssApiIndirect.h	   315                                         0x20-棰勭敓浜у浗瀵嗘牴
; ..\Vss\VssApiIndirect.h	   316                                         0x21-棰勭敓浜у浗闄呮牴
; ..\Vss\VssApiIndirect.h	   317                                         0x30-鐢熶骇鍥藉瘑鏍�
; ..\Vss\VssApiIndirect.h	   318                                         0x31-鐢熶骇鍥介檯鏍�
; ..\Vss\VssApiIndirect.h	   319  
; ..\Vss\VssApiIndirect.h	   320  杩斿洖鍊硷細0         --鎴愬姛
; ..\Vss\VssApiIndirect.h	   321          0x04      --浼犲叆绌烘寚閽堟垨璇佷功绫诲瀷涓嶄负0-1
; ..\Vss\VssApiIndirect.h	   322          0x0E      --璇佷功涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   323          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   324          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   325  ********************************************************************************/
; ..\Vss\VssApiIndirect.h	   326  static inline vss_uint32 VssCertExport(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info)
; ..\Vss\VssApiIndirect.h	   327  {
; ..\Vss\VssApiIndirect.h	   328      vss_uint32 (*api_func_raw)(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info) = 0;
; ..\Vss\VssApiIndirect.h	   329  
; ..\Vss\VssApiIndirect.h	   330      api_func_raw = (vss_uint32 (*)(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertExport));
; ..\Vss\VssApiIndirect.h	   331      return api_func_raw(nCertType, nCertLen, szCert, info);
; ..\Vss\VssApiIndirect.h	   332  }
; ..\Vss\VssApiIndirect.h	   333  
; ..\Vss\VssApiIndirect.h	   334  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   335  鎽樿: 瀵煎叆鏄庢枃浼氳瘽瀵嗛挜
; ..\Vss\VssApiIndirect.h	   336  
; ..\Vss\VssApiIndirect.h	   337  鍙傛暟:nKeylen[in]  --瀵嗛挜鐨勯暱搴︼紝瀹氶暱16
; ..\Vss\VssApiIndirect.h	   338      szKey[in]    --瀵嗛挜鍊�
; ..\Vss\VssApiIndirect.h	   339  
; ..\Vss\VssApiIndirect.h	   340  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   341          0x04    --瀵嗛挜涓虹┖鎸囬拡
; ..\Vss\VssApiIndirect.h	   342          0x19    --瀵嗛挜闀垮害涓嶄负16
; ..\Vss\VssApiIndirect.h	   343          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   344          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   345  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   346  static inline vss_uint32 VssImportSessKey(vss_uint32 nKeyLen, vss_uint8* szKey)
; ..\Vss\VssApiIndirect.h	   347  {
; ..\Vss\VssApiIndirect.h	   348      vss_uint32 (*api_func_raw)(vss_uint32 nKeyLen, vss_uint8* szKey) = 0;
; ..\Vss\VssApiIndirect.h	   349  
; ..\Vss\VssApiIndirect.h	   350      api_func_raw = (vss_uint32 (*)(vss_uint32 nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssImportSessKey));
; ..\Vss\VssApiIndirect.h	   351      return api_func_raw(nKeyLen, szKey);
; ..\Vss\VssApiIndirect.h	   352  }
; ..\Vss\VssApiIndirect.h	   353  
; ..\Vss\VssApiIndirect.h	   354  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   355  鎽樿: 璇诲彇浼氳瘽瀵嗛挜鏄庢枃
; ..\Vss\VssApiIndirect.h	   356  
; ..\Vss\VssApiIndirect.h	   357  鍙傛暟: index[in]    --瀵嗛挜瀛樺偍鐨勭储寮曞彿锛�0-褰撳墠閫氫俊瀵嗛挜锛�1-澶囦唤閫氫俊瀵嗛挜
; ..\Vss\VssApiIndirect.h	   358      nKeylen[out]  --瀵嗛挜鐨勯暱搴�
; ..\Vss\VssApiIndirect.h	   359      szKey[out]    --瀵嗛挜鍊�
; ..\Vss\VssApiIndirect.h	   360  
; ..\Vss\VssApiIndirect.h	   361  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   362          0x04    --瀵嗛挜闀垮害鎴栧瘑閽ュ�间负绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   363          0x05    --鎸囧畾瀵嗛挜涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   364          0x0F    --瀵嗛挜绱㈠紩鍙烽潪娉曪紝涓嶄负0-1
; ..\Vss\VssApiIndirect.h	   365          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   366          0x1C    --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   367  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   368  static inline vss_uint32 VssExportSessKey(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey)
; ..\Vss\VssApiIndirect.h	   369  {
; ..\Vss\VssApiIndirect.h	   370      vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey) = 0;
; ..\Vss\VssApiIndirect.h	   371  
; ..\Vss\VssApiIndirect.h	   372      api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportSessKey));
; ..\Vss\VssApiIndirect.h	   373      return api_func_raw(index, nKeyLen, szKey);
; ..\Vss\VssApiIndirect.h	   374  }
; ..\Vss\VssApiIndirect.h	   375  
; ..\Vss\VssApiIndirect.h	   376  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   377  鎽樿: 淇敼瀵嗛挜鐨勬縺娲诲睘鎬�
; ..\Vss\VssApiIndirect.h	   378  
; ..\Vss\VssApiIndirect.h	   379  鍙傛暟: keyId[in]   --瀵嗛挜ID锛堝彧閽堝5-8鍙烽�氫俊瀵嗛挜鏈夋晥锛�
; ..\Vss\VssApiIndirect.h	   380       valid[in]    --鍙敤鏍囪瘑: 0-涓嶅彲鐢紱1-鍙敤
; ..\Vss\VssApiIndirect.h	   381  
; ..\Vss\VssApiIndirect.h	   382  杩斿洖鍊硷細0       --鎴愬姛
; ..\Vss\VssApiIndirect.h	   383          0x04    --鍙敤鏍囪瘑涓嶆槸鎸囧畾鍊�
; ..\Vss\VssApiIndirect.h	   384          0x05    --瀵嗛挜涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   385          0x0F    --瀵嗛挜ID闈炴硶锛屼笉涓�5-8
; ..\Vss\VssApiIndirect.h	   386          0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   387          0x1D    --鍐欑紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   388  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   389  static inline vss_uint32 VssSetKeyActive(vss_uint32 keyId, vss_uint32 valid)
; ..\Vss\VssApiIndirect.h	   390  {
; ..\Vss\VssApiIndirect.h	   391      vss_uint32 (*api_func_raw)(vss_uint32 keyId, vss_uint32 valid) = 0;
; ..\Vss\VssApiIndirect.h	   392  
; ..\Vss\VssApiIndirect.h	   393      api_func_raw = (vss_uint32 (*)(vss_uint32 keyId, vss_uint32 valid))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSetKeyActive));
; ..\Vss\VssApiIndirect.h	   394      return api_func_raw(keyId, valid);
; ..\Vss\VssApiIndirect.h	   395  }
; ..\Vss\VssApiIndirect.h	   396  
; ..\Vss\VssApiIndirect.h	   397  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   398  鎽樿: 璇诲彇闃茬洍瀵嗛挜鏄庢枃锛岄槻鐩楀瘑閽ヨ嚦澶氫粎涓�鏉℃湁鏁堬紝濡傛灉瀛樺湪闃茬洍瀵嗛挜杩斿洖瀵瑰簲瀵嗛挜锛屽鏋滀笉瀛樺湪杩斿洖05--瀵嗛挜涓嶅彲鐢�
; ..\Vss\VssApiIndirect.h	   399  
; ..\Vss\VssApiIndirect.h	   400  鍙傛暟:index[in]	  --瀵嗛挜绱㈠紩锛�0-3
; ..\Vss\VssApiIndirect.h	   401  	nKeyLen[out]  --瀵嗛挜鐨勯暱搴�
; ..\Vss\VssApiIndirect.h	   402      szKey[out]    --瀵嗛挜鍊�
; ..\Vss\VssApiIndirect.h	   403  
; ..\Vss\VssApiIndirect.h	   404  杩斿洖鍊硷細0        --鎴愬姛
; ..\Vss\VssApiIndirect.h	   405          0x04     --瀵嗛挜鎴栧瘑閽ラ暱搴︿紶鍏ョ┖鎸囬拡
; ..\Vss\VssApiIndirect.h	   406          0x05     --鎸囧畾瀵嗛挜涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   407          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   408          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   409  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   410  static inline vss_uint32 VssExportAtKey(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey)
; ..\Vss\VssApiIndirect.h	   411  {
; ..\Vss\VssApiIndirect.h	   412      vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey) = 0;
; ..\Vss\VssApiIndirect.h	   413  
; ..\Vss\VssApiIndirect.h	   414      api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportAtKey));
; ..\Vss\VssApiIndirect.h	   415      return api_func_raw(index, nKeyLen, szKey);
; ..\Vss\VssApiIndirect.h	   416  }
; ..\Vss\VssApiIndirect.h	   417  
; ..\Vss\VssApiIndirect.h	   418  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   419  鎽樿: 璇诲彇闃茬洍PIN鏄庢枃
; ..\Vss\VssApiIndirect.h	   420  
; ..\Vss\VssApiIndirect.h	   421  鍙傛暟: nPinLen[out]  --PIN鐨勯暱搴�
; ..\Vss\VssApiIndirect.h	   422      szPin[out]    --PIN鍊�
; ..\Vss\VssApiIndirect.h	   423  
; ..\Vss\VssApiIndirect.h	   424  杩斿洖鍊硷細0        --鎴愬姛
; ..\Vss\VssApiIndirect.h	   425          0x04     --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   426          0x05     --鎸囧畾瀵嗛挜涓嶅瓨鍦�
; ..\Vss\VssApiIndirect.h	   427          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   428          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   429  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   430  static inline vss_uint32 VssExportAtPin(vss_uint32* nPinLen, vss_uint8* szPin)
; ..\Vss\VssApiIndirect.h	   431  {
; ..\Vss\VssApiIndirect.h	   432      vss_uint32 (*api_func_raw)(vss_uint32* nPinLen, vss_uint8* szPin) = 0;
; ..\Vss\VssApiIndirect.h	   433  
; ..\Vss\VssApiIndirect.h	   434      api_func_raw = (vss_uint32 (*)(vss_uint32* nPinLen, vss_uint8* szPin))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportAtPin));
; ..\Vss\VssApiIndirect.h	   435      return api_func_raw(nPinLen, szPin);
; ..\Vss\VssApiIndirect.h	   436  }
; ..\Vss\VssApiIndirect.h	   437  
; ..\Vss\VssApiIndirect.h	   438  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   439  鎽樿: 鍙嶉杞﹁締瀹夊叏鐮佽緭鍏ユ儏鍐�
; ..\Vss\VssApiIndirect.h	   440  
; ..\Vss\VssApiIndirect.h	   441  鍙傛暟: szFeedback[out]    --杞﹁締瀹夊叏鐮佸弽棣堝�硷紝瀹氶暱32瀛楄妭
; ..\Vss\VssApiIndirect.h	   442                          濡傛灉32瀛楄妭杞﹁締瀹夊叏鐮佹病缁欏畨鍏ㄧ畻娉曞寘杈撳叆杩囷紝璇ユ帴鍙ｅ悙鍑哄�间负鍏‵F
; ..\Vss\VssApiIndirect.h	   443  濡傛灉32瀛楄妭杞﹁締瀹夊叏鐮佸凡缁忚緭鍏ュ畨鍏ㄧ畻娉曞寘锛屽唴瀹瑰涓嬶細
; ..\Vss\VssApiIndirect.h	   444  鍥藉瘑绠楁硶锛氳鎺ュ彛鍚愬嚭鍊间负鍏�00
; ..\Vss\VssApiIndirect.h	   445  鍥介檯绠楁硶锛氳鎺ュ彛鍚愬嚭鍊间负鍓�31瀛楄妭鍏�00锛屾渶鍚庝竴瀛楄妭0x01
; ..\Vss\VssApiIndirect.h	   446  
; ..\Vss\VssApiIndirect.h	   447  杩斿洖鍊硷細0        --鎴愬姛
; ..\Vss\VssApiIndirect.h	   448          0x04     --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   449          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   450          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   451  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   452  static inline vss_uint32 VssKeyCodeFeedback(vss_uint8* szFeedback)
; ..\Vss\VssApiIndirect.h	   453  {
; ..\Vss\VssApiIndirect.h	   454      vss_uint32 (*api_func_raw)(vss_uint8* szFeedback) = 0;
; ..\Vss\VssApiIndirect.h	   455  
; ..\Vss\VssApiIndirect.h	   456      api_func_raw = (vss_uint32 (*)(vss_uint8* szFeedback))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssKeyCodeFeedback));
; ..\Vss\VssApiIndirect.h	   457      return api_func_raw(szFeedback);
; ..\Vss\VssApiIndirect.h	   458  }
; ..\Vss\VssApiIndirect.h	   459  
; ..\Vss\VssApiIndirect.h	   460  /******************************************************************************
; ..\Vss\VssApiIndirect.h	   461  鎽樿: 鏍规嵁杈撳叆鐨勭爜鍗曠殑鏈�鍚庝竴浣嶆潵鍒ゆ柇鍥藉瘑/鍥介檯绠楁硶鏍囪瘑锛屾湰鎺ュ彛杩斿洖绠楁硶鏍囪瘑
; ..\Vss\VssApiIndirect.h	   462        濡傛灉杞﹁締瀹夊叏鐮佹湭杈撳叆锛屽垯杩斿洖鈥�2-鍥藉瘑绠楁硶鈥�
; ..\Vss\VssApiIndirect.h	   463  
; ..\Vss\VssApiIndirect.h	   464  鍙傛暟: nAlgFlag[out]    --绠楁硶鏍囪瘑: 1-鍥介檯绠楁硶锛�2-鍥藉瘑绠楁硶
; ..\Vss\VssApiIndirect.h	   465                       
; ..\Vss\VssApiIndirect.h	   466  杩斿洖鍊硷細0        --鎴愬姛
; ..\Vss\VssApiIndirect.h	   467          0x04     --浼犲叆绌烘寚閽�
; ..\Vss\VssApiIndirect.h	   468          0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
; ..\Vss\VssApiIndirect.h	   469          0x1C      --璇荤紦瀛樻暟鎹け璐�
; ..\Vss\VssApiIndirect.h	   470  ******************************************************************************/
; ..\Vss\VssApiIndirect.h	   471  static inline vss_uint32 VssGetAlgFlag(vss_uint32* nAlgFlag)
; ..\Vss\VssApiIndirect.h	   472  {
; ..\Vss\VssApiIndirect.h	   473      vss_uint32 (*api_func_raw)(vss_uint32* nAlgFlag) = 0;
; ..\Vss\VssApiIndirect.h	   474  
; ..\Vss\VssApiIndirect.h	   475      api_func_raw = (vss_uint32 (*)(vss_uint32* nAlgFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetAlgFlag));
	movh.a	a15,#32773
	ld.w	d15,[a15]@los(0x80048088)
.L79:
	mov.a	a15,d15
.L80:

; ..\Vss\Vss.c	    34          {
; ..\Vss\Vss.c	    35              ret = VssGetAlgFlag(&vss_flag);
	lea	a4,[a10]32
	calli	a15
.L56:
	extr.u	d2,d2,#0,#8
.L63:

; ..\Vss\Vss.c	    36              if((0u == ret) && (1u == vss_flag))
	jne	d2,#0,.L4
.L81:
	ld.w	d15,[a10]32
.L82:
	jne	d15,#1,.L5
.L83:

; ..\Vss\Vss.c	    37              {
; ..\Vss\Vss.c	    38                  Vss_Bypass_Flag = 0;
	movh.a	a15,#@his(Vss_Bypass_Flag)
.L84:
	mov	d15,#0
	st.w	[a15]@los(Vss_Bypass_Flag),d15
.L5:
.L4:
.L3:
.L2:

; ..\Vss\Vss.c	    39              }
; ..\Vss\Vss.c	    40          }
; ..\Vss\Vss.c	    41      }
; ..\Vss\Vss.c	    42  
; ..\Vss\Vss.c	    43      return ret;
; ..\Vss\Vss.c	    44  }
	ret
.L29:
	
__Vss_Init_function_end:
	.size	Vss_Init,__Vss_Init_function_end-Vss_Init
.L20:
	; End of function
	
	.sdecl	'.text.Vss.Vss_GetBypassFlag',code,cluster('Vss_GetBypassFlag')
	.sect	'.text.Vss.Vss_GetBypassFlag'
	.align	2
	
	.global	Vss_GetBypassFlag

; ..\Vss\Vss.c	    45  
; ..\Vss\Vss.c	    46  uint8 Vss_GetBypassFlag(void)
; Function Vss_GetBypassFlag
.L11:
Vss_GetBypassFlag:	.type	func

; ..\Vss\Vss.c	    47  {
; ..\Vss\Vss.c	    48      return Vss_Bypass_Flag;
	movh.a	a15,#@his(Vss_Bypass_Flag)
	ld.w	d15,[a15]@los(Vss_Bypass_Flag)
.L89:

; ..\Vss\Vss.c	    49  }
	extr.u	d2,d15,#0,#8
	ret
.L60:
	
__Vss_GetBypassFlag_function_end:
	.size	Vss_GetBypassFlag,__Vss_GetBypassFlag_function_end-Vss_GetBypassFlag
.L25:
	; End of function
	
	.sdecl	'.data.Vss.Vss_Bypass_Flag',data,cluster('Vss_Bypass_Flag')
	.sect	'.data.Vss.Vss_Bypass_Flag'
	.align	4
Vss_Bypass_Flag:	.type	object
	.size	Vss_Bypass_Flag,4
	.word	1
	.calls	'__INDIRECT__','flash_cb'
	.calls	'Vss_Init','__INDIRECT__'
	.calls	'Vss_Init','',40
	.extern	flash_cb
	.extern	__INDIRECT__
	.calls	'Vss_GetBypassFlag','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L13:
	.word	7757
	.half	3
	.word	.L14
	.byte	4
.L12:
	.byte	1
	.byte	'..\\Vss\\Vss.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L15
.L33:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L35:
	.byte	3
	.byte	'VssCryptoInit',0,3,1,169,1,26
	.word	167
	.byte	1,1
.L37:
	.byte	4
	.byte	'type',0,1,169,1,51
	.word	167
.L39:
	.byte	4
	.byte	'group',0,1,169,1,68
	.word	167
	.byte	5
	.word	167
	.byte	1,1,6
	.word	167
	.byte	6
	.word	167
.L28:
	.byte	2
	.byte	'unsigned char',0,1,8,7
	.word	260
	.byte	6
	.word	277
	.byte	6
	.word	167
	.byte	0,7
	.word	243
.L41:
	.byte	4
	.byte	'flashCb',0,1,169,1,88
	.word	293
	.byte	8
	.word	167
	.byte	1,1,7
	.word	315
.L43:
	.byte	4
	.byte	'wdtCb',0,1,169,1,109
	.word	322
.L45:
	.byte	9,0
.L49:
	.byte	3
	.byte	'VssKeyCodeFeedback',0,3,1,196,3,26
	.word	167
	.byte	1,1,7
	.word	260
.L51:
	.byte	4
	.byte	'szFeedback',0,1,196,3,56
	.word	375
.L53:
	.byte	9,0
.L54:
	.byte	3
	.byte	'VssGetAlgFlag',0,3,1,215,3,26
	.word	167
	.byte	1,1,7
	.word	167
.L57:
	.byte	4
	.byte	'nAlgFlag',0,1,215,3,52
	.word	428
.L59:
	.byte	9,0
.L30:
	.byte	10,32
	.word	260
	.byte	11,31,0,12
	.byte	'flash_cb',0,2,6,8
	.word	167
	.byte	1,1,1,1,4
	.byte	'rwflag',0,2,6,24
	.word	167
	.byte	4
	.byte	'offset',0,2,6,39
	.word	167
	.byte	7
	.word	260
	.byte	4
	.byte	'buf',0,2,6,54
	.word	513
	.byte	4
	.byte	'size_buff',0,2,6,66
	.word	167
	.byte	0,13
	.word	188
	.byte	14
	.word	214
	.byte	14
	.word	228
	.byte	14
	.word	298
	.byte	14
	.word	327
	.byte	9,0,13
	.word	344
	.byte	14
	.word	380
	.byte	9,0,13
	.word	402
	.byte	14
	.word	433
	.byte	9,0,15
	.byte	'__INDIRECT__',0,3,1,1,1,1,1,16
	.byte	'void',0,7
	.word	620
	.byte	17
	.byte	'__prof_adm',0,3,1,1
	.word	626
	.byte	18,1,7
	.word	650
	.byte	17
	.byte	'__codeptr',0,3,1,1
	.word	652
	.byte	17
	.byte	'uint8',0,4,90,29
	.word	260
	.byte	2
	.byte	'short int',0,2,5,17
	.byte	'sint16',0,4,91,29
	.word	689
	.byte	2
	.byte	'unsigned short int',0,2,7,17
	.byte	'uint16',0,4,92,29
	.word	717
	.byte	17
	.byte	'uint32',0,4,94,29
	.word	167
	.byte	17
	.byte	'boolean',0,4,105,29
	.word	260
	.byte	2
	.byte	'unsigned long long int',0,8,7,17
	.byte	'uint64',0,4,130,1,30
	.word	785
	.byte	17
	.byte	'Std_ReturnType',0,5,113,15
	.word	260
	.byte	17
	.byte	'PduLengthType',0,6,76,22
	.word	717
	.byte	17
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,7,112,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,7,115,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,7,118,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,7,121,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,7,124,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,7,136,1,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,7,139,1,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,7,148,1,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,7,151,1,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,7,141,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,7,144,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,7,147,3,16
	.word	717
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,7,150,3,16
	.word	717
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,7,153,3,16
	.word	717
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,7,156,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,7,159,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,7,162,3,16
	.word	717
	.byte	17
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,7,165,3,16
	.word	717
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,7,180,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,7,183,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,7,186,3,16
	.word	167
	.byte	17
	.byte	'IdtAppCom_MainPwrFltRsn',0,7,192,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGMDiags',0,7,195,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGMFltRsn',0,7,198,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGMSts',0,7,201,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGMSwSts',0,7,207,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,7,210,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,7,213,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,7,216,3,16
	.word	717
	.byte	17
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,7,219,3,16
	.word	717
	.byte	17
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,7,225,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,7,228,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_RednPwrFltRsn',0,7,231,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,7,234,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_SHWAIndSts',0,7,237,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_SHWASysFltSts',0,7,240,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_SHWASysMsg',0,7,243,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,7,246,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_SHWASysSts',0,7,249,3,15
	.word	260
	.byte	17
	.byte	'IdtAppCom_SHWASysTakeOver',0,7,252,3,15
	.word	260
	.byte	2
	.byte	'unsigned int',0,4,7,17
	.byte	'Rte_BitType',0,7,230,7,22
	.word	2429
	.byte	17
	.byte	'_iob_flag_t',0,8,75,25
	.word	717
	.byte	17
	.byte	'vss_uint8',0,9,8,24
	.word	260
	.byte	2
	.byte	'char',0,1,6,17
	.byte	'vss_char8',0,9,10,17
	.word	2504
	.byte	17
	.byte	'vss_uint32',0,9,13,24
	.word	167
	.byte	17
	.byte	'vss_uint64',0,9,17,28
	.word	785
	.byte	17
	.byte	'vss_ulong',0,9,18,24
	.word	167
	.byte	17
	.byte	'BYTE',0,9,21,22
	.word	260
	.byte	17
	.byte	'WORD',0,9,22,22
	.word	167
	.byte	17
	.byte	'SM3_WORD_T',0,9,25,22
	.word	167
	.byte	19,9,27,9,168,1,20
	.byte	'm_size',0,4
	.word	167
	.byte	2,35,0,10,128,1
	.word	260
	.byte	11,127,0,20
	.byte	'remain',0,128,1
	.word	2653
	.byte	2,35,4,20
	.byte	'r_len',0,4
	.word	167
	.byte	3,35,132,1,10,32
	.word	167
	.byte	11,7,0,20
	.byte	'iv',0,32
	.word	2696
	.byte	3,35,136,1,0,17
	.byte	'SM3_CTX_T',0,9,32,3
	.word	2631
	.byte	19,9,34,9,108,10,64
	.word	260
	.byte	11,63,0,20
	.byte	'data',0,64
	.word	2742
	.byte	2,35,0,20
	.byte	'datalen',0,4
	.word	167
	.byte	2,35,64,20
	.byte	'bitlen',0,8
	.word	785
	.byte	2,35,68,10,32
	.word	167
	.byte	11,7,0,20
	.byte	'state',0,32
	.word	2798
	.byte	2,35,76,0,17
	.byte	'SHA256_CTX',0,9,39,3
	.word	2737
	.byte	19,9,41,9,8,10,8
	.word	167
	.byte	11,1,0,20
	.byte	'k',0,8
	.word	2847
	.byte	2,35,0,0,17
	.byte	'TZucKey',0,9,44,3
	.word	2842
	.byte	21,9,46,9,168,1,20
	.byte	'shactx',0,108
	.word	2737
	.byte	2,35,0,20
	.byte	'sm3ctx',0,168,1
	.word	2631
	.byte	2,35,0,0,17
	.byte	'THashCtx',0,9,50,2
	.word	2884
	.byte	22
	.word	167
	.byte	1,6
	.word	167
	.byte	6
	.word	167
	.byte	6
	.word	277
	.byte	6
	.word	167
	.byte	0,17
	.byte	'flash_io_cb',0,9,56,20
	.word	2941
	.byte	23
	.word	167
	.byte	1,17
	.byte	'wdt_rst_cb',0,9,58,20
	.word	2988
	.byte	21,10,52,9,164,1,10,164,1
	.word	260
	.byte	11,163,1,0,20
	.byte	'datas',0,164,1
	.word	3019
	.byte	2,35,0,19,10,55,5,164,1,20
	.byte	'certFmt',0,1
	.word	260
	.byte	2,35,0,10,8
	.word	260
	.byte	11,7,0,20
	.byte	'pModNum',0,8
	.word	3069
	.byte	2,35,1,10,16
	.word	260
	.byte	11,15,0,20
	.byte	'customPars',0,16
	.word	3095
	.byte	2,35,9,10,3
	.word	260
	.byte	11,2,0,20
	.byte	'certFailDate',0,3
	.word	3124
	.byte	2,35,25,10,4
	.word	260
	.byte	11,3,0,20
	.byte	'certSequenceNum',0,4
	.word	3155
	.byte	2,35,28,20
	.byte	'signAlgoFlg',0,1
	.word	260
	.byte	2,35,32,20
	.byte	'pubKeyCurPar',0,1
	.word	260
	.byte	2,35,33,20
	.byte	'hashAlgoFlg',0,1
	.word	260
	.byte	2,35,34,20
	.byte	'pubKeyIdx',0,1
	.word	260
	.byte	2,35,35,10,64
	.word	260
	.byte	11,63,0,20
	.byte	'certPubKey',0,64
	.word	3272
	.byte	2,35,36,20
	.byte	'certSigner',0,64
	.word	3272
	.byte	2,35,100,0,20
	.byte	'parameters',0,164,1
	.word	3046
	.byte	2,35,0,0,17
	.byte	'Secure_SignerInfoType',0,10,68,3
	.word	3013
	.byte	24,11,72,9,1,25
	.byte	'INTERNAL_FLS',0,0,25
	.byte	'EXTERNAL_FLS',0,1,0,17
	.byte	'FL_FlashType',0,11,76,2
	.word	3374
	.byte	24,11,78,9,1,25
	.byte	'NO_CRC',0,0,25
	.byte	'LAST_ADDR',0,1,25
	.byte	'HEAD_ADDR',0,2,0,17
	.byte	'FL_CrcAddrType',0,11,83,2
	.word	3431
	.byte	19,11,108,9,8,20
	.byte	'address',0,4
	.word	167
	.byte	2,35,0,20
	.byte	'length',0,4
	.word	167
	.byte	2,35,4,0,17
	.byte	'FL_SegmentInfoType',0,11,116,3
	.word	3493
	.byte	19,11,129,1,9,20,20
	.byte	'blkValid',0,1
	.word	260
	.byte	2,35,0,20
	.byte	'blkProgAttempt',0,2
	.word	717
	.byte	2,35,2,20
	.byte	'blkChecksum',0,4
	.word	167
	.byte	2,35,4,10,9
	.word	260
	.byte	11,8,0,20
	.byte	'fingerPrint',0,9
	.word	3628
	.byte	2,35,8,0,17
	.byte	'FL_blockInfoType',0,11,139,1,3
	.word	3559
	.byte	26
	.byte	'_Ifx_STM_ACCEN0_Bits',0,12,45,16,4,27
	.byte	'EN0',0,1
	.word	260
	.byte	1,7,2,35,0,27
	.byte	'EN1',0,1
	.word	260
	.byte	1,6,2,35,0,27
	.byte	'EN2',0,1
	.word	260
	.byte	1,5,2,35,0,27
	.byte	'EN3',0,1
	.word	260
	.byte	1,4,2,35,0,27
	.byte	'EN4',0,1
	.word	260
	.byte	1,3,2,35,0,27
	.byte	'EN5',0,1
	.word	260
	.byte	1,2,2,35,0,27
	.byte	'EN6',0,1
	.word	260
	.byte	1,1,2,35,0,27
	.byte	'EN7',0,1
	.word	260
	.byte	1,0,2,35,0,27
	.byte	'EN8',0,1
	.word	260
	.byte	1,7,2,35,1,27
	.byte	'EN9',0,1
	.word	260
	.byte	1,6,2,35,1,27
	.byte	'EN10',0,1
	.word	260
	.byte	1,5,2,35,1,27
	.byte	'EN11',0,1
	.word	260
	.byte	1,4,2,35,1,27
	.byte	'EN12',0,1
	.word	260
	.byte	1,3,2,35,1,27
	.byte	'EN13',0,1
	.word	260
	.byte	1,2,2,35,1,27
	.byte	'EN14',0,1
	.word	260
	.byte	1,1,2,35,1,27
	.byte	'EN15',0,1
	.word	260
	.byte	1,0,2,35,1,27
	.byte	'EN16',0,1
	.word	260
	.byte	1,7,2,35,2,27
	.byte	'EN17',0,1
	.word	260
	.byte	1,6,2,35,2,27
	.byte	'EN18',0,1
	.word	260
	.byte	1,5,2,35,2,27
	.byte	'EN19',0,1
	.word	260
	.byte	1,4,2,35,2,27
	.byte	'EN20',0,1
	.word	260
	.byte	1,3,2,35,2,27
	.byte	'EN21',0,1
	.word	260
	.byte	1,2,2,35,2,27
	.byte	'EN22',0,1
	.word	260
	.byte	1,1,2,35,2,27
	.byte	'EN23',0,1
	.word	260
	.byte	1,0,2,35,2,27
	.byte	'EN24',0,1
	.word	260
	.byte	1,7,2,35,3,27
	.byte	'EN25',0,1
	.word	260
	.byte	1,6,2,35,3,27
	.byte	'EN26',0,1
	.word	260
	.byte	1,5,2,35,3,27
	.byte	'EN27',0,1
	.word	260
	.byte	1,4,2,35,3,27
	.byte	'EN28',0,1
	.word	260
	.byte	1,3,2,35,3,27
	.byte	'EN29',0,1
	.word	260
	.byte	1,2,2,35,3,27
	.byte	'EN30',0,1
	.word	260
	.byte	1,1,2,35,3,27
	.byte	'EN31',0,1
	.word	260
	.byte	1,0,2,35,3,0,17
	.byte	'Ifx_STM_ACCEN0_Bits',0,12,79,3
	.word	3685
	.byte	26
	.byte	'_Ifx_STM_ACCEN1_Bits',0,12,82,16,4,27
	.byte	'reserved_0',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_ACCEN1_Bits',0,12,85,3
	.word	4242
	.byte	26
	.byte	'_Ifx_STM_CAP_Bits',0,12,88,16,4,27
	.byte	'STMCAP63_32',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_CAP_Bits',0,12,91,3
	.word	4319
	.byte	26
	.byte	'_Ifx_STM_CAPSV_Bits',0,12,94,16,4,27
	.byte	'STMCAP63_32',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_CAPSV_Bits',0,12,97,3
	.word	4391
	.byte	26
	.byte	'_Ifx_STM_CLC_Bits',0,12,100,16,4,27
	.byte	'DISR',0,1
	.word	260
	.byte	1,7,2,35,0,27
	.byte	'DISS',0,1
	.word	260
	.byte	1,6,2,35,0,27
	.byte	'reserved_2',0,1
	.word	260
	.byte	1,5,2,35,0,27
	.byte	'EDIS',0,1
	.word	260
	.byte	1,4,2,35,0,27
	.byte	'reserved_4',0,4
	.word	2429
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_STM_CLC_Bits',0,12,107,3
	.word	4467
	.byte	26
	.byte	'_Ifx_STM_CMCON_Bits',0,12,110,16,4,27
	.byte	'MSIZE0',0,1
	.word	260
	.byte	5,3,2,35,0,27
	.byte	'reserved_5',0,1
	.word	260
	.byte	3,0,2,35,0,27
	.byte	'MSTART0',0,1
	.word	260
	.byte	5,3,2,35,1,27
	.byte	'reserved_13',0,1
	.word	260
	.byte	3,0,2,35,1,27
	.byte	'MSIZE1',0,1
	.word	260
	.byte	5,3,2,35,2,27
	.byte	'reserved_21',0,1
	.word	260
	.byte	3,0,2,35,2,27
	.byte	'MSTART1',0,1
	.word	260
	.byte	5,3,2,35,3,27
	.byte	'reserved_29',0,1
	.word	260
	.byte	3,0,2,35,3,0,17
	.byte	'Ifx_STM_CMCON_Bits',0,12,120,3
	.word	4608
	.byte	26
	.byte	'_Ifx_STM_CMP_Bits',0,12,123,16,4,27
	.byte	'CMPVAL',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_CMP_Bits',0,12,126,3
	.word	4826
	.byte	26
	.byte	'_Ifx_STM_ICR_Bits',0,12,129,1,16,4,27
	.byte	'CMP0EN',0,1
	.word	260
	.byte	1,7,2,35,0,27
	.byte	'CMP0IR',0,1
	.word	260
	.byte	1,6,2,35,0,27
	.byte	'CMP0OS',0,1
	.word	260
	.byte	1,5,2,35,0,27
	.byte	'reserved_3',0,1
	.word	260
	.byte	1,4,2,35,0,27
	.byte	'CMP1EN',0,1
	.word	260
	.byte	1,3,2,35,0,27
	.byte	'CMP1IR',0,1
	.word	260
	.byte	1,2,2,35,0,27
	.byte	'CMP1OS',0,1
	.word	260
	.byte	1,1,2,35,0,27
	.byte	'reserved_7',0,4
	.word	2429
	.byte	25,0,2,35,2,0,17
	.byte	'Ifx_STM_ICR_Bits',0,12,139,1,3
	.word	4893
	.byte	26
	.byte	'_Ifx_STM_ID_Bits',0,12,142,1,16,4,27
	.byte	'MODREV',0,1
	.word	260
	.byte	8,0,2,35,0,27
	.byte	'MODTYPE',0,1
	.word	260
	.byte	8,0,2,35,1,27
	.byte	'MODNUMBER',0,2
	.word	717
	.byte	16,0,2,35,2,0,17
	.byte	'Ifx_STM_ID_Bits',0,12,147,1,3
	.word	5096
	.byte	26
	.byte	'_Ifx_STM_ISCR_Bits',0,12,150,1,16,4,27
	.byte	'CMP0IRR',0,1
	.word	260
	.byte	1,7,2,35,0,27
	.byte	'CMP0IRS',0,1
	.word	260
	.byte	1,6,2,35,0,27
	.byte	'CMP1IRR',0,1
	.word	260
	.byte	1,5,2,35,0,27
	.byte	'CMP1IRS',0,1
	.word	260
	.byte	1,4,2,35,0,27
	.byte	'reserved_4',0,4
	.word	2429
	.byte	28,0,2,35,2,0,17
	.byte	'Ifx_STM_ISCR_Bits',0,12,157,1,3
	.word	5203
	.byte	26
	.byte	'_Ifx_STM_KRST0_Bits',0,12,160,1,16,4,27
	.byte	'RST',0,1
	.word	260
	.byte	1,7,2,35,0,27
	.byte	'RSTSTAT',0,1
	.word	260
	.byte	1,6,2,35,0,27
	.byte	'reserved_2',0,4
	.word	2429
	.byte	30,0,2,35,2,0,17
	.byte	'Ifx_STM_KRST0_Bits',0,12,165,1,3
	.word	5354
	.byte	26
	.byte	'_Ifx_STM_KRST1_Bits',0,12,168,1,16,4,27
	.byte	'RST',0,1
	.word	260
	.byte	1,7,2,35,0,27
	.byte	'reserved_1',0,4
	.word	2429
	.byte	31,0,2,35,2,0,17
	.byte	'Ifx_STM_KRST1_Bits',0,12,172,1,3
	.word	5465
	.byte	26
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,12,175,1,16,4,27
	.byte	'CLR',0,1
	.word	260
	.byte	1,7,2,35,0,27
	.byte	'reserved_1',0,4
	.word	2429
	.byte	31,0,2,35,2,0,17
	.byte	'Ifx_STM_KRSTCLR_Bits',0,12,179,1,3
	.word	5557
	.byte	26
	.byte	'_Ifx_STM_OCS_Bits',0,12,182,1,16,4,27
	.byte	'reserved_0',0,4
	.word	2429
	.byte	24,8,2,35,2,27
	.byte	'SUS',0,1
	.word	260
	.byte	4,4,2,35,3,27
	.byte	'SUS_P',0,1
	.word	260
	.byte	1,3,2,35,3,27
	.byte	'SUSSTA',0,1
	.word	260
	.byte	1,2,2,35,3,27
	.byte	'reserved_30',0,1
	.word	260
	.byte	2,0,2,35,3,0,17
	.byte	'Ifx_STM_OCS_Bits',0,12,189,1,3
	.word	5653
	.byte	26
	.byte	'_Ifx_STM_TIM0_Bits',0,12,192,1,16,4,27
	.byte	'STM31_0',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_TIM0_Bits',0,12,195,1,3
	.word	5799
	.byte	26
	.byte	'_Ifx_STM_TIM0SV_Bits',0,12,198,1,16,4,27
	.byte	'STM31_0',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_TIM0SV_Bits',0,12,201,1,3
	.word	5871
	.byte	26
	.byte	'_Ifx_STM_TIM1_Bits',0,12,204,1,16,4,27
	.byte	'STM35_4',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_TIM1_Bits',0,12,207,1,3
	.word	5947
	.byte	26
	.byte	'_Ifx_STM_TIM2_Bits',0,12,210,1,16,4,27
	.byte	'STM39_8',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_TIM2_Bits',0,12,213,1,3
	.word	6019
	.byte	26
	.byte	'_Ifx_STM_TIM3_Bits',0,12,216,1,16,4,27
	.byte	'STM43_12',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_TIM3_Bits',0,12,219,1,3
	.word	6091
	.byte	26
	.byte	'_Ifx_STM_TIM4_Bits',0,12,222,1,16,4,27
	.byte	'STM47_16',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_TIM4_Bits',0,12,225,1,3
	.word	6164
	.byte	26
	.byte	'_Ifx_STM_TIM5_Bits',0,12,228,1,16,4,27
	.byte	'STM51_20',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_TIM5_Bits',0,12,231,1,3
	.word	6237
	.byte	26
	.byte	'_Ifx_STM_TIM6_Bits',0,12,234,1,16,4,27
	.byte	'STM63_32',0,4
	.word	2429
	.byte	32,0,2,35,2,0,17
	.byte	'Ifx_STM_TIM6_Bits',0,12,237,1,3
	.word	6310
	.byte	21,12,245,1,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0
.L61:
	.byte	2
	.byte	'int',0,4,5,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	3685
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_ACCEN0',0,12,250,1,3
	.word	6383
	.byte	21,12,253,1,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	4242
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_ACCEN1',0,12,130,2,3
	.word	6454
	.byte	21,12,133,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	4319
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_CAP',0,12,138,2,3
	.word	6518
	.byte	21,12,141,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	4391
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_CAPSV',0,12,146,2,3
	.word	6579
	.byte	21,12,149,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	4467
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_CLC',0,12,154,2,3
	.word	6642
	.byte	21,12,157,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	4608
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_CMCON',0,12,162,2,3
	.word	6703
	.byte	21,12,165,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	4826
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_CMP',0,12,170,2,3
	.word	6766
	.byte	21,12,173,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	4893
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_ICR',0,12,178,2,3
	.word	6827
	.byte	21,12,181,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5096
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_ID',0,12,186,2,3
	.word	6888
	.byte	21,12,189,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5203
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_ISCR',0,12,194,2,3
	.word	6948
	.byte	21,12,197,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5354
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_KRST0',0,12,202,2,3
	.word	7010
	.byte	21,12,205,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5465
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_KRST1',0,12,210,2,3
	.word	7073
	.byte	21,12,213,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5557
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_KRSTCLR',0,12,218,2,3
	.word	7136
	.byte	21,12,221,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5653
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_OCS',0,12,226,2,3
	.word	7201
	.byte	21,12,229,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5799
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_TIM0',0,12,234,2,3
	.word	7262
	.byte	21,12,237,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5871
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_TIM0SV',0,12,242,2,3
	.word	7324
	.byte	21,12,245,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	5947
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_TIM1',0,12,250,2,3
	.word	7388
	.byte	21,12,253,2,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	6019
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_TIM2',0,12,130,3,3
	.word	7450
	.byte	21,12,133,3,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	6091
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_TIM3',0,12,138,3,3
	.word	7512
	.byte	21,12,141,3,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	6164
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_TIM4',0,12,146,3,3
	.word	7574
	.byte	21,12,149,3,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	6237
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_TIM5',0,12,154,3,3
	.word	7636
	.byte	21,12,157,3,9,4,20
	.byte	'U',0,4
	.word	2429
	.byte	2,35,0,20
	.byte	'I',0,4
	.word	6400
	.byte	2,35,0,20
	.byte	'B',0,4
	.word	6310
	.byte	2,35,0,0,17
	.byte	'Ifx_STM_TIM6',0,12,162,3,3
	.word	7698
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L14:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,32,13,58,15,59,15
	.byte	57,15,73,19,54,15,39,12,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,21,1,73,19,54,15,39,12,0,0,6,5,0,73
	.byte	19,0,0,7,15,0,73,19,0,0,8,21,0,73,19,54,15,39,12,0,0,9,11,0,0,0,10,1,1,11,15,73,19,0,0,11,33,0,47,15,0
	.byte	0,12,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,13,46,1,49,19,0,0,14,5,0,49,19,0,0,15
	.byte	46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,16,59,0,3,8,0,0,17,22,0,3,8,58,15,59,15,57,15,73,19,0
	.byte	0,18,21,0,54,15,0,0,19,19,1,58,15,59,15,57,15,11,15,0,0,20,13,0,3,8,11,15,73,19,56,9,0,0,21,23,1,58,15
	.byte	59,15,57,15,11,15,0,0,22,21,1,73,19,39,12,0,0,23,21,0,73,19,39,12,0,0,24,4,1,58,15,59,15,57,15,11,15,0
	.byte	0,25,40,0,3,8,28,13,0,0,26,19,1,3,8,58,15,59,15,57,15,11,15,0,0,27,13,0,3,8,11,15,73,19,13,15,12,15,56
	.byte	9,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L15:
	.word	.L65-.L64
.L64:
	.half	3
	.word	.L67-.L66
.L66:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash',0
	.byte	0
	.byte	'..\\Vss\\VssApiIndirect.h',0,0,0,0
	.byte	'..\\Vss\\VSS_RW.h',0,0,0,0
	.byte	'..\\Vss\\Vss.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Std_Types.h',0,1,0,0
	.byte	'ComStack_Types.h',0,1,0,0
	.byte	'Rte_Type.h',0,2,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'..\\Vss\\vsstype.h',0,0,0,0
	.byte	'Secure_Types.h',0,4,0,0
	.byte	'FL.h',0,5,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0,0
.L67:
.L65:
	.sdecl	'.debug_info',debug,cluster('Vss_Init')
	.sect	'.debug_info'
.L16:
	.word	459
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'..\\Vss\\Vss.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L19,.L18
	.byte	2
	.word	.L12
	.byte	3
	.byte	'Vss_Init',0,1,20,16
	.word	.L28
	.byte	1,1,1
	.word	.L9,.L29,.L8
	.byte	4
	.word	.L9,.L29
	.byte	5
	.byte	'vsnChkResult',0,1,22,11
	.word	.L30,.L31
	.byte	5
	.byte	'ret',0,1,23,20
	.word	.L28,.L32
	.byte	5
	.byte	'vss_flag',0,1,24,12
	.word	.L33,.L34
	.byte	6
	.word	.L35,.L9,.L36
	.byte	7
	.word	.L37,.L38
	.byte	7
	.word	.L39,.L40
	.byte	7
	.word	.L41,.L42
	.byte	7
	.word	.L43,.L44
	.byte	8
	.word	.L45,.L46
	.byte	0,6
	.word	.L35,.L47,.L48
	.byte	7
	.word	.L37,.L38
	.byte	7
	.word	.L39,.L40
	.byte	7
	.word	.L41,.L42
	.byte	7
	.word	.L43,.L44
	.byte	0,6
	.word	.L49,.L48,.L50
	.byte	7
	.word	.L51,.L52
	.byte	9
	.word	.L53,.L48,.L50
	.byte	0,6
	.word	.L54,.L55,.L56
	.byte	7
	.word	.L57,.L58
	.byte	9
	.word	.L59,.L55,.L56
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Vss_Init')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6
	.byte	29,1,49,16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,85,6,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Vss_Init')
	.sect	'.debug_line'
.L18:
	.word	.L69-.L68
.L68:
	.half	3
	.word	.L71-.L70
.L70:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Vss\\Vss.c',0,0,0,0
	.byte	'..\\Vss\\VssApiIndirect.h',0,0,0,0,0
.L71:
	.byte	4,2,5,113,7,0,5,2
	.word	.L9
	.byte	3,172,1,1,5,25,9
	.half	.L72-.L9
	.byte	3,1,1,4,1,3,237,126,1,4,2,5,20,9
	.half	.L73-.L72
	.byte	3,146,1,1,5,31,9
	.half	.L74-.L73
	.byte	3,1,1,4,1,5,25,3,237,126,1,5,16,9
	.half	.L36-.L74
	.byte	3,121,1,5,35,9
	.half	.L47-.L36
	.byte	3,7,1,4,2,5,60,9
	.half	.L48-.L47
	.byte	3,173,3,1,5,20,9
	.half	.L75-.L48
	.byte	1,4,1,5,31,9
	.half	.L76-.L75
	.byte	3,212,124,1,5,29,9
	.half	.L50-.L76
	.byte	1,5,5,9
	.half	.L62-.L50
	.byte	3,3,1,5,34,7,9
	.half	.L77-.L62
	.byte	3,2,1,5,9,9
	.half	.L78-.L77
	.byte	1,4,2,5,59,7,9
	.half	.L55-.L78
	.byte	3,186,3,1,5,20,9
	.half	.L79-.L55
	.byte	1,4,1,5,34,9
	.half	.L80-.L79
	.byte	3,200,124,1,5,32,9
	.half	.L56-.L80
	.byte	1,5,16,9
	.half	.L63-.L56
	.byte	3,1,1,5,38,7,9
	.half	.L81-.L63
	.byte	1,5,35,9
	.half	.L82-.L81
	.byte	1,5,17,7,9
	.half	.L83-.L82
	.byte	3,2,1,5,35,9
	.half	.L84-.L83
	.byte	1,5,33,1,5,1,9
	.half	.L2-.L84
	.byte	3,6,1,7,9
	.half	.L20-.L2
	.byte	0,1,1
.L69:
	.sdecl	'.debug_ranges',debug,cluster('Vss_Init')
	.sect	'.debug_ranges'
.L19:
	.word	-1,.L9,0,.L20-.L9,0,0
.L46:
	.word	-1,.L9,0,.L36-.L9,.L47-.L9,.L48-.L9,0,0
	.sdecl	'.debug_info',debug,cluster('Vss_GetBypassFlag')
	.sect	'.debug_info'
.L21:
	.word	224
	.half	3
	.word	.L22
	.byte	4,1
	.byte	'..\\Vss\\Vss.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L24,.L23
	.byte	2
	.word	.L12
	.byte	3
	.byte	'Vss_GetBypassFlag',0,1,46,7
	.word	.L28
	.byte	1,1,1
	.word	.L11,.L60,.L10
	.byte	4
	.word	.L11,.L60
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Vss_GetBypassFlag')
	.sect	'.debug_abbrev'
.L22:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Vss_GetBypassFlag')
	.sect	'.debug_line'
.L23:
	.word	.L86-.L85
.L85:
	.half	3
	.word	.L88-.L87
.L87:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Vss\\Vss.c',0,0,0,0,0
.L88:
	.byte	5,12,7,0,5,2
	.word	.L11
	.byte	3,47,1,5,1,9
	.half	.L89-.L11
	.byte	3,1,1,7,9
	.half	.L25-.L89
	.byte	0,1,1
.L86:
	.sdecl	'.debug_ranges',debug,cluster('Vss_GetBypassFlag')
	.sect	'.debug_ranges'
.L24:
	.word	-1,.L11,0,.L25-.L11,0,0
	.sdecl	'.debug_info',debug,cluster('Vss_Bypass_Flag')
	.sect	'.debug_info'
.L26:
	.word	195
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'..\\Vss\\Vss.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L12
	.byte	3
	.byte	'Vss_Bypass_Flag',0,3,18,8
	.word	.L61
	.byte	5,3
	.word	Vss_Bypass_Flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Vss_Bypass_Flag')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('Vss_GetBypassFlag')
	.sect	'.debug_loc'
.L10:
	.word	-1,.L11,0,.L60-.L11
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Vss_Init')
	.sect	'.debug_loc'
.L8:
	.word	-1,.L9,0,.L47-.L9
	.half	2
	.byte	138,0
	.word	.L47-.L9,.L29-.L9
	.half	2
	.byte	138,40
	.word	.L29-.L9,.L29-.L9
	.half	2
	.byte	138,0
	.word	0,0
.L42:
	.word	0,0
.L40:
	.word	0,0
.L58:
	.word	0,0
.L32:
	.word	-1,.L9,.L62-.L9,.L56-.L9
	.half	5
	.byte	144,33,157,32,0
	.word	.L63-.L9,.L29-.L9
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L52:
	.word	0,0
.L38:
	.word	0,0
.L31:
	.word	-1,.L9,0,.L29-.L9
	.half	2
	.byte	145,88
	.word	0,0
.L34:
	.word	-1,.L9,0,.L29-.L9
	.half	2
	.byte	145,120
	.word	0,0
.L44:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L90:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Vss_Init')
	.sect	'.debug_frame'
	.word	36
	.word	.L90,.L9,.L29-.L9
	.byte	4
	.word	(.L47-.L9)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L29-.L47)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Vss_GetBypassFlag')
	.sect	'.debug_frame'
	.word	24
	.word	.L90,.L11,.L60-.L11
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\Vss\Vss.c	    50  
; ..\Vss\Vss.c	    51  
; ..\Vss\Vss.c	    52  
; ..\Vss\Vss.c	    53  

	; Module end
