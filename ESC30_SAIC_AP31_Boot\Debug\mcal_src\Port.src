	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc9936a -c99 --dep-file=mcal_src\\.Port.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Port.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Port.src ..\\mcal_src\\Port.c"
	.compiler_name		"ctc"
	.name	"Port"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Port_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Port_Init

; ..\mcal_src\Port.c	     1  /*******************************************************************************
; ..\mcal_src\Port.c	     2  **                                                                            **
; ..\mcal_src\Port.c	     3  ** Copyright (C) Infineon Technologies (2018)                                 **
; ..\mcal_src\Port.c	     4  **                                                                            **
; ..\mcal_src\Port.c	     5  ** All rights reserved.                                                       **
; ..\mcal_src\Port.c	     6  **                                                                            **
; ..\mcal_src\Port.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Port.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Port.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Port.c	    10  **                                                                            **
; ..\mcal_src\Port.c	    11  ********************************************************************************
; ..\mcal_src\Port.c	    12  **                                                                            **
; ..\mcal_src\Port.c	    13  **  $FILENAME   : Port.c $                                                   **
; ..\mcal_src\Port.c	    14  **                                                                            **
; ..\mcal_src\Port.c	    15  **  $CC VERSION : \main\dev_tc23x_as4.0.3\27 $                               **
; ..\mcal_src\Port.c	    16  **                                                                            **
; ..\mcal_src\Port.c	    17  **  $DATE       : 2018-06-19 $                                               **
; ..\mcal_src\Port.c	    18  **                                                                            **
; ..\mcal_src\Port.c	    19  **  AUTHOR       : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Port.c	    20  **                                                                            **
; ..\mcal_src\Port.c	    21  **  VENDOR       : Infineon Technologies                                      **
; ..\mcal_src\Port.c	    22  **                                                                            **
; ..\mcal_src\Port.c	    23  **  DESCRIPTION  : This file contains                                         **
; ..\mcal_src\Port.c	    24  **                 - functionality of Port driver.                            **
; ..\mcal_src\Port.c	    25  **                                                                            **
; ..\mcal_src\Port.c	    26  **  MAY BE CHANGED BY USER [Yes/No]: No                                       **
; ..\mcal_src\Port.c	    27  **                                                                            **
; ..\mcal_src\Port.c	    28  *******************************************************************************/
; ..\mcal_src\Port.c	    29  /*  TRACEABILITY : [cover parentID=DS_AS_PORT107_PORT146_4,DS_NAS_PORT_PR127,
; ..\mcal_src\Port.c	    30                      SAS_NAS_PORT_PR913,DS_AS40X_PORT114,DS_NAS_PORT_PR131,
; ..\mcal_src\Port.c	    31                      DS_NAS_PORT_PR228,DS_AS_PORT107_PORT146,SAS_NAS_ALL_PR1652,
; ..\mcal_src\Port.c	    32                      SAS_AS_PORT080_PORT108_PORT130_PORT131_PORT133_PORT208,
; ..\mcal_src\Port.c	    33                      SAS_NAS_PORT_PR734,SAS_NAS_ALL_PR749,
; ..\mcal_src\Port.c	    34                      SAS_NAS_ALL_PR630_PR631,
; ..\mcal_src\Port.c	    35                      SAS_NAS_ALL_PR70]
; ..\mcal_src\Port.c	    36                     [/cover]                                                   */
; ..\mcal_src\Port.c	    37  /*******************************************************************************
; ..\mcal_src\Port.c	    38  **                      Includes                                              **
; ..\mcal_src\Port.c	    39  *******************************************************************************/
; ..\mcal_src\Port.c	    40  
; ..\mcal_src\Port.c	    41  /* Inclusion of Tasking sfr file */
; ..\mcal_src\Port.c	    42  
; ..\mcal_src\Port.c	    43  #include "IfxPort_reg.h"
; ..\mcal_src\Port.c	    44  
; ..\mcal_src\Port.c	    45  /* Global functions like Set/Reset END INIT protection bit,
; ..\mcal_src\Port.c	    46    Enable/Disable interrupts, Automic write function */
; ..\mcal_src\Port.c	    47  #include "Mcal.h"
; ..\mcal_src\Port.c	    48  
; ..\mcal_src\Port.c	    49  /* Own header file, this includes own configuration file also */
; ..\mcal_src\Port.c	    50  /* PORT080: Inclusion structure */
; ..\mcal_src\Port.c	    51  #include "Port.h"
; ..\mcal_src\Port.c	    52  
; ..\mcal_src\Port.c	    53  /*AS and SW version Specific include file */
; ..\mcal_src\Port.c	    54  #include "Port_Ver.h"
; ..\mcal_src\Port.c	    55  
; ..\mcal_src\Port.c	    56  /* Inclusion from Safety Error */
; ..\mcal_src\Port.c	    57  #if (PORT_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Port.c	    58  #include "SafetyReport.h"
; ..\mcal_src\Port.c	    59  #endif
; ..\mcal_src\Port.c	    60  
; ..\mcal_src\Port.c	    61  /*******************************************************************************
; ..\mcal_src\Port.c	    62  **                      Private Macro Definitions                             **
; ..\mcal_src\Port.c	    63  *******************************************************************************/
; ..\mcal_src\Port.c	    64  
; ..\mcal_src\Port.c	    65  /*
; ..\mcal_src\Port.c	    66    Offset definitions for data in Port configuration
; ..\mcal_src\Port.c	    67  */
; ..\mcal_src\Port.c	    68  /* Initial Control data offset */
; ..\mcal_src\Port.c	    69  #define PORT_DATA_OFS_CTL      (0U)
; ..\mcal_src\Port.c	    70  /* Initial level data offset */
; ..\mcal_src\Port.c	    71  #define PORT_DATA_OFS_LEVEL    (4U)
; ..\mcal_src\Port.c	    72  
; ..\mcal_src\Port.c	    73  /* If pin mode changeable is enabled */
; ..\mcal_src\Port.c	    74  #if (PORT_SET_PIN_MODE_API == STD_ON)
; ..\mcal_src\Port.c	    75  /* Pin mode changeable or not offset */
; ..\mcal_src\Port.c	    76  #define PORT_DATA_OFS_MODE     (7U)
; ..\mcal_src\Port.c	    77  
; ..\mcal_src\Port.c	    78  #endif /* (PORT_SET_PIN_MODE_API) */
; ..\mcal_src\Port.c	    79  
; ..\mcal_src\Port.c	    80  /* If Direction changeable is enabled */
; ..\mcal_src\Port.c	    81  #if (PORT_SET_PIN_DIRECTION_API == STD_ON)
; ..\mcal_src\Port.c	    82  /* If pin mode changeable is enabled */
; ..\mcal_src\Port.c	    83  #if (PORT_SET_PIN_MODE_API == STD_ON)
; ..\mcal_src\Port.c	    84  /* Pin direction data offset */
; ..\mcal_src\Port.c	    85  #define PORT_DATA_OFS_DIR      (8U)
; ..\mcal_src\Port.c	    86  /* Control data 2 offset */
; ..\mcal_src\Port.c	    87  #define PORT_DATA_OFS_CTL2     (9U)
; ..\mcal_src\Port.c	    88  
; ..\mcal_src\Port.c	    89  #else
; ..\mcal_src\Port.c	    90  /* Pin direction data offset */
; ..\mcal_src\Port.c	    91  #define PORT_DATA_OFS_DIR      (7U)
; ..\mcal_src\Port.c	    92  /* Control data 2 offset */
; ..\mcal_src\Port.c	    93  #define PORT_DATA_OFS_CTL2     (8U)
; ..\mcal_src\Port.c	    94  
; ..\mcal_src\Port.c	    95  #endif /* (PORT_SET_PIN_MODE_API) */
; ..\mcal_src\Port.c	    96  #endif /* (PORT_SET_PIN_DIRECTION_API) */
; ..\mcal_src\Port.c	    97  
; ..\mcal_src\Port.c	    98  #if((PORT_SET_PIN_DIRECTION_API == STD_ON) || (PORT_SET_PIN_MODE_API == STD_ON))
; ..\mcal_src\Port.c	    99  /* Mask to get direction info from Pin control value */
; ..\mcal_src\Port.c	   100  #define PORT_DIR_MSK           (0x80U)
; ..\mcal_src\Port.c	   101  #endif
; ..\mcal_src\Port.c	   102  
; ..\mcal_src\Port.c	   103  /* IOCR0 register offset in Ifx_P */
; ..\mcal_src\Port.c	   104  #define PORT_IOCR0_REG_OFFSET  (4U)
; ..\mcal_src\Port.c	   105  
; ..\mcal_src\Port.c	   106  /* PCSR register offset in Ifx_P */
; ..\mcal_src\Port.c	   107  #define PORT_PCSR_REG_OFFSET   (0x19U)
; ..\mcal_src\Port.c	   108  
; ..\mcal_src\Port.c	   109  #if(PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	   110  /* Status to indicate that PORT is initialized */
; ..\mcal_src\Port.c	   111  #define PORT_INITIALIZED  ((uint8)1)
; ..\mcal_src\Port.c	   112  #endif  /*(PORT_DEV_ERROR_DETECT == STD_ON)*/
; ..\mcal_src\Port.c	   113  
; ..\mcal_src\Port.c	   114  /* Before Setting the Iocr register, To Check the Port number below
; ..\mcal_src\Port.c	   115     macro's are used */
; ..\mcal_src\Port.c	   116  
; ..\mcal_src\Port.c	   117  #define PORT_PIN_0_3           (0x000F)
; ..\mcal_src\Port.c	   118  #define PORT_PIN_4_7           (0x00F0)
; ..\mcal_src\Port.c	   119  #define PORT_PIN_8_11          (0x0F00)
; ..\mcal_src\Port.c	   120  #define PORT_PIN_12_15         (0xF000)
; ..\mcal_src\Port.c	   121  
; ..\mcal_src\Port.c	   122  /* 8-bit mask value from 8th to 15th bit */
; ..\mcal_src\Port.c	   123  #define PORT_PDR1_MASK         (0xFF00U)
; ..\mcal_src\Port.c	   124  
; ..\mcal_src\Port.c	   125  #if((PORT_SET_PIN_DIRECTION_API == STD_ON) || (PORT_SET_PIN_MODE_API == STD_ON))
; ..\mcal_src\Port.c	   126  /* offset value where the port number is present */
; ..\mcal_src\Port.c	   127  #define PORT_NUMBER_OFFSET     (4U)
; ..\mcal_src\Port.c	   128  
; ..\mcal_src\Port.c	   129  /* lower 8-bit mask value in hex to get the port number */
; ..\mcal_src\Port.c	   130  #define PORT_NUM_LOW8_MASK     (0x000000FFU)
; ..\mcal_src\Port.c	   131  
; ..\mcal_src\Port.c	   132  /* lower 4-bit mask value in hex to get the pin number of a port */
; ..\mcal_src\Port.c	   133  #define PORT_PIN_LOW4_MASK     (0x0FU)
; ..\mcal_src\Port.c	   134  
; ..\mcal_src\Port.c	   135  #endif
; ..\mcal_src\Port.c	   136  
; ..\mcal_src\Port.c	   137  /* lower 16-bit mask value in Hex */
; ..\mcal_src\Port.c	   138  #define PORT_LOWER_16BIT_MASK  (0x0000FFFFU)
; ..\mcal_src\Port.c	   139  
; ..\mcal_src\Port.c	   140  /* 16-bit shift value */
; ..\mcal_src\Port.c	   141  #define PORT_16BIT_SHIFTVALUE  (16U)
; ..\mcal_src\Port.c	   142  #define PORT_NUM_EIGHT         (8U)
; ..\mcal_src\Port.c	   143  #define PORT_NUM_FOUR          (4U)
; ..\mcal_src\Port.c	   144  
; ..\mcal_src\Port.c	   145  /* general constants */
; ..\mcal_src\Port.c	   146  #define PORT_CONSTANT_16       (16U)
; ..\mcal_src\Port.c	   147  #define PORT_CONSTANT_10       (10U)
; ..\mcal_src\Port.c	   148  #define PORT_NUMBER_32         (32U)
; ..\mcal_src\Port.c	   149  #define PORT_NUMBER_31         (31U)
; ..\mcal_src\Port.c	   150  #define PORT_CONSTANT_0x01     (0x01U)
; ..\mcal_src\Port.c	   151  
; ..\mcal_src\Port.c	   152  
; ..\mcal_src\Port.c	   153  #if (PORT_SET_PIN_MODE_API == STD_ON)
; ..\mcal_src\Port.c	   154  #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	   155  
; ..\mcal_src\Port.c	   156  /* Available Inverted Port Mask */
; ..\mcal_src\Port.c	   157  #define AVAILABLE_MODE_MASK    ((Port_PinModeType)(0xC7U))
; ..\mcal_src\Port.c	   158  
; ..\mcal_src\Port.c	   159  #endif /*(PORT_SET_PIN_MODE_API==STD_ON) */
; ..\mcal_src\Port.c	   160  #endif
; ..\mcal_src\Port.c	   161  /* ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))*/
; ..\mcal_src\Port.c	   162  
; ..\mcal_src\Port.c	   163  /* Maximum port pin number */
; ..\mcal_src\Port.c	   164  #define PORT_PIN_MAX_NUMBER    (15U)
; ..\mcal_src\Port.c	   165  
; ..\mcal_src\Port.c	   166  /* Mcal Safety ENDINIT Timeout value */
; ..\mcal_src\Port.c	   167  #define PORT_ENDINIT_TIMEOUT   (150000U)
; ..\mcal_src\Port.c	   168  
; ..\mcal_src\Port.c	   169  /* Mask for valid bits */
; ..\mcal_src\Port.c	   170  #define PORT_IOCR0_PIN_SHIFT_MASK             (0x000000F8U)
; ..\mcal_src\Port.c	   171  
; ..\mcal_src\Port.c	   172  /*IFX_MISRA_RULE_19_07_STATUS=To support user modification of OS protected
; ..\mcal_src\Port.c	   173    calls definition, it is declared as a function like macro*/
; ..\mcal_src\Port.c	   174  #define PORT_IOCR0_BIT_SHIFT_COUNT(Pin)       (PORT_NUM_EIGHT * \ 
; ..\mcal_src\Port.c	   175                                                ((Pin) % (PORT_NUM_FOUR)))
; ..\mcal_src\Port.c	   176  
; ..\mcal_src\Port.c	   177  /*IFX_MISRA_RULE_19_07_STATUS=To support user modification of OS protected
; ..\mcal_src\Port.c	   178    calls definition, it is declared as a function like macro*/
; ..\mcal_src\Port.c	   179  #define PORT_PIN_IOCR0_SETMASK(Pin)      ((uint32)PORT_IOCR0_PIN_SHIFT_MASK << \ 
; ..\mcal_src\Port.c	   180                                                 PORT_IOCR0_BIT_SHIFT_COUNT(Pin))
; ..\mcal_src\Port.c	   181  
; ..\mcal_src\Port.c	   182  /*IFX_MISRA_RULE_19_07_STATUS=To support user modification of OS protected
; ..\mcal_src\Port.c	   183    calls definition, it is declared as a function like macro*/
; ..\mcal_src\Port.c	   184  #define PORT_PIN_IOCR0_CLEARMASK(Pin)         (~PORT_PIN_IOCR0_SETMASK(Pin))
; ..\mcal_src\Port.c	   185  
; ..\mcal_src\Port.c	   186  #if (PORT_SET_PIN_MODE_API == STD_ON)
; ..\mcal_src\Port.c	   187  #define PORT_IOCR0_MODE_SHIFT_MASK            (0x00000038U)  
; ..\mcal_src\Port.c	   188  /*IFX_MISRA_RULE_19_07_STATUS=To support user modification of OS protected
; ..\mcal_src\Port.c	   189    calls definition, it is declared as a function like macro*/
; ..\mcal_src\Port.c	   190  #define PORT_IOCR0_MODE_CLEARMASK(Pin) (~((uint32)PORT_IOCR0_MODE_SHIFT_MASK \ 
; ..\mcal_src\Port.c	   191                                          << PORT_IOCR0_BIT_SHIFT_COUNT(Pin)))
; ..\mcal_src\Port.c	   192  #endif /* (PORT_SET_PIN_MODE_API == STD_ON)*/
; ..\mcal_src\Port.c	   193  
; ..\mcal_src\Port.c	   194  /*******************************************************************************
; ..\mcal_src\Port.c	   195  **                   Function like macro definitions                          **
; ..\mcal_src\Port.c	   196  *******************************************************************************/
; ..\mcal_src\Port.c	   197  
; ..\mcal_src\Port.c	   198  /*******************************************************************************
; ..\mcal_src\Port.c	   199  **                      Private Type Definitions                              **
; ..\mcal_src\Port.c	   200  *******************************************************************************/
; ..\mcal_src\Port.c	   201  
; ..\mcal_src\Port.c	   202  /*******************************************************************************
; ..\mcal_src\Port.c	   203  **                      Private Function Declarations                         **
; ..\mcal_src\Port.c	   204  *******************************************************************************/
; ..\mcal_src\Port.c	   205  
; ..\mcal_src\Port.c	   206  #define PORT_START_SEC_CODE
; ..\mcal_src\Port.c	   207  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Port.c	   208  #include "MemMap.h"
; ..\mcal_src\Port.c	   209  
; ..\mcal_src\Port.c	   210  /*INLINE function to initialize Port module*/
; ..\mcal_src\Port.c	   211  IFX_LOCAL_INLINE void Port_lIOInit(void);
; ..\mcal_src\Port.c	   212  
; ..\mcal_src\Port.c	   213  /*INLINE function to initialize Port module*/
; ..\mcal_src\Port.c	   214  IFX_LOCAL_INLINE void Port_lPDRInit(void);
; ..\mcal_src\Port.c	   215  
; ..\mcal_src\Port.c	   216  /*INLINE function to extract the Address of Px_OUT register */
; ..\mcal_src\Port.c	   217  IFX_LOCAL_INLINE Ifx_P *Port_lAdr(uint32 PortNumber);
; ..\mcal_src\Port.c	   218  
; ..\mcal_src\Port.c	   219  /*INLINE function to to check if the port
; ..\mcal_src\Port.c	   220    is available or not for the microcontroller */
; ..\mcal_src\Port.c	   221  IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable31(uint32 Port);
; ..\mcal_src\Port.c	   222  
; ..\mcal_src\Port.c	   223  /*INLINE function to check if the port
; ..\mcal_src\Port.c	   224    is available or not for the microcontroller */
; ..\mcal_src\Port.c	   225  IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable40(uint32 Port);
; ..\mcal_src\Port.c	   226  
; ..\mcal_src\Port.c	   227  /*INLINE function to check if the port is
; ..\mcal_src\Port.c	   228    available or not for the microcontroller    */
; ..\mcal_src\Port.c	   229  IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable(uint32 Port);
; ..\mcal_src\Port.c	   230  
; ..\mcal_src\Port.c	   231  /*INLINE function to check if the port is read only or it is writable */
; ..\mcal_src\Port.c	   232  IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly31(uint32 Port);
; ..\mcal_src\Port.c	   233  
; ..\mcal_src\Port.c	   234  /*INLINE function to check if the port is read only or it is writable */
; ..\mcal_src\Port.c	   235  IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly40(uint32 Port);
; ..\mcal_src\Port.c	   236  
; ..\mcal_src\Port.c	   237  /*INLINE function to check if the port is read only or it is writable*/
; ..\mcal_src\Port.c	   238  IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly(uint32 Port);
; ..\mcal_src\Port.c	   239  
; ..\mcal_src\Port.c	   240  /*INLINE function to check if the port pin is
; ..\mcal_src\Port.c	   241    available or not for the microcontroller      */
; ..\mcal_src\Port.c	   242  IFX_LOCAL_INLINE uint16 Port_lIsPinAvailable(uint32 Port,uint32 Pin);
; ..\mcal_src\Port.c	   243  
; ..\mcal_src\Port.c	   244  /*INLINE function to check if any of the
; ..\mcal_src\Port.c	   245    portpin 8-15 is available or not for the micro*/
; ..\mcal_src\Port.c	   246  IFX_LOCAL_INLINE uint16 Port_lIsPortPdr1Available(uint32 Port);
; ..\mcal_src\Port.c	   247  
; ..\mcal_src\Port.c	   248  /*INLINE function to check if the IOCRx register for the port
; ..\mcal_src\Port.c	   249     is available or not for the micro */
; ..\mcal_src\Port.c	   250  IFX_LOCAL_INLINE uint16 Port_lIsPortIocrAvailable(uint32 Port,uint16 Pin);
; ..\mcal_src\Port.c	   251  
; ..\mcal_src\Port.c	   252  #if (PORT_SET_PIN_MODE_API == STD_ON)
; ..\mcal_src\Port.c	   253  #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	   254  
; ..\mcal_src\Port.c	   255  /*INLINE function to check the error
; ..\mcal_src\Port.c	   256    in the Port_SetPinMode Api  */
; ..\mcal_src\Port.c	   257  IFX_LOCAL_INLINE uint32 Port_lModeErrChk
; ..\mcal_src\Port.c	   258  (
; ..\mcal_src\Port.c	   259    uint8 Dir, Port_PinModeType Mode, uint32 ConfigIndex, uint32 PinNumber
; ..\mcal_src\Port.c	   260  );
; ..\mcal_src\Port.c	   261  IFX_LOCAL_INLINE uint8 Port_lChkPin
; ..\mcal_src\Port.c	   262  (
; ..\mcal_src\Port.c	   263    Port_PinType Pin,uint32 PortNo,uint32 PinNo
; ..\mcal_src\Port.c	   264  );
; ..\mcal_src\Port.c	   265  #endif
; ..\mcal_src\Port.c	   266  /*(PORT_SET_PIN_MODE_API==STD_ON) */
; ..\mcal_src\Port.c	   267  #endif
; ..\mcal_src\Port.c	   268  /* ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))*/
; ..\mcal_src\Port.c	   269  
; ..\mcal_src\Port.c	   270  /*INLINE function for OMR register value
; ..\mcal_src\Port.c	   271    to set the configured initial level      */
; ..\mcal_src\Port.c	   272  IFX_LOCAL_INLINE uint32 Port_lPinLevel(uint32 Level);
; ..\mcal_src\Port.c	   273  
; ..\mcal_src\Port.c	   274  #if((PORT_SET_PIN_DIRECTION_API == STD_ON) || (PORT_SET_PIN_MODE_API == STD_ON))
; ..\mcal_src\Port.c	   275  /*INLINE function to extract port number Port_PinType data */
; ..\mcal_src\Port.c	   276  IFX_LOCAL_INLINE uint32 Port_lNumber(Port_PinType Pin);
; ..\mcal_src\Port.c	   277  
; ..\mcal_src\Port.c	   278  /*INLINE function to extract pin number from Port_PinType data*/
; ..\mcal_src\Port.c	   279  IFX_LOCAL_INLINE uint32 Port_lPinNumber(Port_PinType Pin);
; ..\mcal_src\Port.c	   280  #endif
; ..\mcal_src\Port.c	   281  
; ..\mcal_src\Port.c	   282  /*INLINE function to to check if the port supports PCSR
; ..\mcal_src\Port.c	   283     for the microcontroller */
; ..\mcal_src\Port.c	   284  IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable31(uint32 Port);
; ..\mcal_src\Port.c	   285  
; ..\mcal_src\Port.c	   286  /*INLINE function to to check if the port supports PCSR
; ..\mcal_src\Port.c	   287     for the microcontroller */
; ..\mcal_src\Port.c	   288  IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable40(uint32 Port);
; ..\mcal_src\Port.c	   289  
; ..\mcal_src\Port.c	   290  /*INLINE function to to check if the port supports PCSR
; ..\mcal_src\Port.c	   291     for the microcontroller */
; ..\mcal_src\Port.c	   292  IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable(uint32 Port);
; ..\mcal_src\Port.c	   293  
; ..\mcal_src\Port.c	   294  #if (PORT_INIT_CHECK_API == STD_ON)
; ..\mcal_src\Port.c	   295  IFX_LOCAL_INLINE uint8 Port_lChkIocr(uint32 PortNo,const uint32 *DataPtr);
; ..\mcal_src\Port.c	   296  IFX_LOCAL_INLINE uint8 Port_lChkPDR(uint32 PortNo,uint32 Data_PDR0,
; ..\mcal_src\Port.c	   297                                     uint32 Data_PDR1);
; ..\mcal_src\Port.c	   298  #endif
; ..\mcal_src\Port.c	   299  
; ..\mcal_src\Port.c	   300  #define PORT_STOP_SEC_CODE
; ..\mcal_src\Port.c	   301  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Port.c	   302  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   303   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   304  #include "MemMap.h"
; ..\mcal_src\Port.c	   305  
; ..\mcal_src\Port.c	   306  /*******************************************************************************
; ..\mcal_src\Port.c	   307  **                      Global Constant Definitions                           **
; ..\mcal_src\Port.c	   308  *******************************************************************************/
; ..\mcal_src\Port.c	   309  
; ..\mcal_src\Port.c	   310  
; ..\mcal_src\Port.c	   311  /*******************************************************************************
; ..\mcal_src\Port.c	   312  **                      Global Variable Definitions                           **
; ..\mcal_src\Port.c	   313  *******************************************************************************/
; ..\mcal_src\Port.c	   314  
; ..\mcal_src\Port.c	   315  /*******************************************************************************
; ..\mcal_src\Port.c	   316  **                      Private Constant Definitions                          **
; ..\mcal_src\Port.c	   317  *******************************************************************************/
; ..\mcal_src\Port.c	   318  /* Mapping of 16 bit constant */
; ..\mcal_src\Port.c	   319  #define PORT_START_SEC_CONST_16BIT
; ..\mcal_src\Port.c	   320  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   321   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   322  #include "MemMap.h"
; ..\mcal_src\Port.c	   323  
; ..\mcal_src\Port.c	   324  /* Total pin count for each port, This constant is used during DET check */
; ..\mcal_src\Port.c	   325  static const uint16 Port_kAvailablePins[] =
; ..\mcal_src\Port.c	   326  {
; ..\mcal_src\Port.c	   327    PORT_AVAILABLE_PINS_PORT0,
; ..\mcal_src\Port.c	   328    PORT_AVAILABLE_PINS_PORT1,
; ..\mcal_src\Port.c	   329    PORT_AVAILABLE_PINS_PORT2,
; ..\mcal_src\Port.c	   330    PORT_AVAILABLE_PINS_PORT3,
; ..\mcal_src\Port.c	   331    PORT_AVAILABLE_PINS_PORT4,
; ..\mcal_src\Port.c	   332    PORT_AVAILABLE_PINS_PORT5,
; ..\mcal_src\Port.c	   333    PORT_AVAILABLE_PINS_PORT6,
; ..\mcal_src\Port.c	   334    PORT_AVAILABLE_PINS_PORT7,
; ..\mcal_src\Port.c	   335    PORT_AVAILABLE_PINS_PORT8,
; ..\mcal_src\Port.c	   336    PORT_AVAILABLE_PINS_PORT9,
; ..\mcal_src\Port.c	   337    PORT_AVAILABLE_PINS_PORT10,
; ..\mcal_src\Port.c	   338    PORT_AVAILABLE_PINS_PORT11,
; ..\mcal_src\Port.c	   339    PORT_AVAILABLE_PINS_PORT12,
; ..\mcal_src\Port.c	   340    PORT_AVAILABLE_PINS_PORT13,
; ..\mcal_src\Port.c	   341    PORT_AVAILABLE_PINS_PORT14,
; ..\mcal_src\Port.c	   342    PORT_AVAILABLE_PINS_PORT15,
; ..\mcal_src\Port.c	   343    PORT_AVAILABLE_PINS_PORT16,
; ..\mcal_src\Port.c	   344    PORT_AVAILABLE_PINS_PORT17,
; ..\mcal_src\Port.c	   345    PORT_AVAILABLE_PINS_PORT18,
; ..\mcal_src\Port.c	   346    PORT_AVAILABLE_PINS_PORT19,
; ..\mcal_src\Port.c	   347    PORT_AVAILABLE_PINS_PORT20,
; ..\mcal_src\Port.c	   348    PORT_AVAILABLE_PINS_PORT21,
; ..\mcal_src\Port.c	   349    PORT_AVAILABLE_PINS_PORT22,
; ..\mcal_src\Port.c	   350    PORT_AVAILABLE_PINS_PORT23,
; ..\mcal_src\Port.c	   351    PORT_AVAILABLE_PINS_PORT24,
; ..\mcal_src\Port.c	   352    PORT_AVAILABLE_PINS_PORT25,
; ..\mcal_src\Port.c	   353    PORT_AVAILABLE_PINS_PORT26,
; ..\mcal_src\Port.c	   354    PORT_AVAILABLE_PINS_PORT27,
; ..\mcal_src\Port.c	   355    PORT_AVAILABLE_PINS_PORT28,
; ..\mcal_src\Port.c	   356    PORT_AVAILABLE_PINS_PORT29,
; ..\mcal_src\Port.c	   357    PORT_AVAILABLE_PINS_PORT30,
; ..\mcal_src\Port.c	   358    PORT_AVAILABLE_PINS_PORT31,
; ..\mcal_src\Port.c	   359    PORT_AVAILABLE_PINS_PORT32,
; ..\mcal_src\Port.c	   360    PORT_AVAILABLE_PINS_PORT33,
; ..\mcal_src\Port.c	   361    PORT_AVAILABLE_PINS_PORT34,
; ..\mcal_src\Port.c	   362    PORT_AVAILABLE_PINS_PORT35,
; ..\mcal_src\Port.c	   363    PORT_AVAILABLE_PINS_PORT36,
; ..\mcal_src\Port.c	   364    PORT_AVAILABLE_PINS_PORT37,
; ..\mcal_src\Port.c	   365    PORT_AVAILABLE_PINS_PORT38,
; ..\mcal_src\Port.c	   366    PORT_AVAILABLE_PINS_PORT39,
; ..\mcal_src\Port.c	   367    PORT_AVAILABLE_PINS_PORT40,
; ..\mcal_src\Port.c	   368    PORT_AVAILABLE_PINS_PORT41
; ..\mcal_src\Port.c	   369  };
; ..\mcal_src\Port.c	   370  
; ..\mcal_src\Port.c	   371  #define PORT_STOP_SEC_CONST_16BIT
; ..\mcal_src\Port.c	   372  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   373   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   374  #include "MemMap.h"
; ..\mcal_src\Port.c	   375  /*******************************************************************************
; ..\mcal_src\Port.c	   376  **                      Private Variable Definitions                          **
; ..\mcal_src\Port.c	   377  *******************************************************************************/
; ..\mcal_src\Port.c	   378  #if(PORT_PB_FIXEDADDR == STD_OFF)
; ..\mcal_src\Port.c	   379  #define PORT_START_SEC_VAR_32BIT
; ..\mcal_src\Port.c	   380  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   381   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   382  #include "MemMap.h"
; ..\mcal_src\Port.c	   383  
; ..\mcal_src\Port.c	   384  /* To store the Port driver configuration pointer */
; ..\mcal_src\Port.c	   385  static  const Port_ConfigType  *Port_kConfigPtr;
; ..\mcal_src\Port.c	   386  
; ..\mcal_src\Port.c	   387  #define PORT_STOP_SEC_VAR_32BIT
; ..\mcal_src\Port.c	   388  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   389   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   390  #include "MemMap.h"
; ..\mcal_src\Port.c	   391  
; ..\mcal_src\Port.c	   392  #endif /*(PORT_PB_FIXEDADDR == STD_OFF) */
; ..\mcal_src\Port.c	   393  
; ..\mcal_src\Port.c	   394  #if(PORT_PB_FIXEDADDR == STD_ON)
; ..\mcal_src\Port.c	   395  #define PORT_START_SEC_CONST_32BIT
; ..\mcal_src\Port.c	   396  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   397   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   398  #include "MemMap.h"
; ..\mcal_src\Port.c	   399  
; ..\mcal_src\Port.c	   400  /* To store the Port driver configuration pointer */
; ..\mcal_src\Port.c	   401  static  const Port_ConfigType * const Port_kConfigPtr = &Port_ConfigRoot[0];
; ..\mcal_src\Port.c	   402  
; ..\mcal_src\Port.c	   403  
; ..\mcal_src\Port.c	   404  #define PORT_STOP_SEC_CONST_32BIT
; ..\mcal_src\Port.c	   405  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   406   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   407  #include "MemMap.h"
; ..\mcal_src\Port.c	   408  #endif /*(PORT_PB_FIXEDADDR == STD_ON) */
; ..\mcal_src\Port.c	   409  
; ..\mcal_src\Port.c	   410  
; ..\mcal_src\Port.c	   411  #if(PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	   412  /* Start 8 bit variable section */
; ..\mcal_src\Port.c	   413  #define PORT_START_SEC_VAR_8BIT
; ..\mcal_src\Port.c	   414  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   415   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   416  #include "MemMap.h"
; ..\mcal_src\Port.c	   417  /* Init Status Variable. It has to be initialized to "0U" after every reset as
; ..\mcal_src\Port.c	   418     0 represents the deinitialized state */
; ..\mcal_src\Port.c	   419  static uint8 Port_InitStatus;
; ..\mcal_src\Port.c	   420  /* Stop 8 bit variable section */
; ..\mcal_src\Port.c	   421  #define PORT_STOP_SEC_VAR_8BIT
; ..\mcal_src\Port.c	   422  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   423   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   424  #include "MemMap.h"
; ..\mcal_src\Port.c	   425  #endif  /*(PORT_DEV_ERROR_DETECT == STD_ON)*/
; ..\mcal_src\Port.c	   426  /*******************************************************************************
; ..\mcal_src\Port.c	   427  **                      Private Function Definitions                          **
; ..\mcal_src\Port.c	   428  *******************************************************************************/
; ..\mcal_src\Port.c	   429  
; ..\mcal_src\Port.c	   430  
; ..\mcal_src\Port.c	   431  /*******************************************************************************
; ..\mcal_src\Port.c	   432  **                      Global Function Definitions                           **
; ..\mcal_src\Port.c	   433  *******************************************************************************/
; ..\mcal_src\Port.c	   434  /* Mapping the code */
; ..\mcal_src\Port.c	   435  #define PORT_START_SEC_CODE
; ..\mcal_src\Port.c	   436  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	   437   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	   438  #include "MemMap.h"
; ..\mcal_src\Port.c	   439  
; ..\mcal_src\Port.c	   440  /*******************************************************************************
; ..\mcal_src\Port.c	   441  ** Traceability     : [cover parentID=DS_AS_PORT140,DS_AS_PORT003,
; ..\mcal_src\Port.c	   442                          DS_AS_PORT101_PORT211_1,DS_AS_PORT107_PORT146_1,
; ..\mcal_src\Port.c	   443                          DS_AS_PORT107_PORT146_4,DS_AS4XX_PORT087_1,
; ..\mcal_src\Port.c	   444                          DS_AS_PORT105,
; ..\mcal_src\Port.c	   445                          DS_AS_PORT148,DS_AS_PORT005,DS_AS_PORT078,
; ..\mcal_src\Port.c	   446                          DS_AS3XX_PORT087_1,DS_AS_PORT077,DS_AS_PORT082,
; ..\mcal_src\Port.c	   447                          SAS_MCAL_PORT_0402,SAS_MCAL_PORT_0415,
; ..\mcal_src\Port.c	   448                          DS_MCAL_PORT_0415,DS_MCAL_PORT_0417,
; ..\mcal_src\Port.c	   449                          DS_MCAL_PORT_0402,DS_AS_PORT107_PORT146_4]            **
; ..\mcal_src\Port.c	   450  **                                                                            **
; ..\mcal_src\Port.c	   451  ** Syntax           : void Port_Init                                          **
; ..\mcal_src\Port.c	   452  **                    (                                                       **
; ..\mcal_src\Port.c	   453  **                      const Port_ConfigType * ConfigPtr                     **
; ..\mcal_src\Port.c	   454  **                    )                                                       **
; ..\mcal_src\Port.c	   455  ** [/cover]                                                                   **
; ..\mcal_src\Port.c	   456  **                                                                            **
; ..\mcal_src\Port.c	   457  ** Service ID       : 0x00                                                    **
; ..\mcal_src\Port.c	   458  **                                                                            **
; ..\mcal_src\Port.c	   459  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	   460  **                                                                            **
; ..\mcal_src\Port.c	   461  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Port.c	   462  **                                                                            **
; ..\mcal_src\Port.c	   463  ** Parameters(in)   : const Port_ConfigType * ConfigPtr - Pointer to          **
; ..\mcal_src\Port.c	   464  **                    PORT configuration                                      **
; ..\mcal_src\Port.c	   465  **                                                                            **
; ..\mcal_src\Port.c	   466  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	   467  **                                                                            **
; ..\mcal_src\Port.c	   468  ** Return value     : none                                                    **
; ..\mcal_src\Port.c	   469  **                                                                            **
; ..\mcal_src\Port.c	   470  ** Description      : This function:                                          **
; ..\mcal_src\Port.c	   471  **   - PORT041: PORT042: Initializes all the ports as per configuration       **
; ..\mcal_src\Port.c	   472  **   - PORT001: This API initializes the whole port structure of MCU          **
; ..\mcal_src\Port.c	   473  **   - PORT003: This API can be used to initialize and reinitialize the       **
; ..\mcal_src\Port.c	   474  **     port with different configuration                                      **
; ..\mcal_src\Port.c	   475  **   - PORT078: This function should be called first in order to initialize   **
; ..\mcal_src\Port.c	   476  **     the port for use                                                       **
; ..\mcal_src\Port.c	   477  **                                                                            **
; ..\mcal_src\Port.c	   478  *******************************************************************************/
; ..\mcal_src\Port.c	   479  void Port_Init ( const Port_ConfigType * ConfigPtr )
; Function Port_Init
.L32:
Port_Init:	.type	func

; ..\mcal_src\Port.c	   480  {
; ..\mcal_src\Port.c	   481    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	   482    uint8 ErrStatus;
; ..\mcal_src\Port.c	   483    ErrStatus = 0U;
; ..\mcal_src\Port.c	   484  
; ..\mcal_src\Port.c	   485    #if(PORT_PB_FIXEDADDR == STD_OFF)
; ..\mcal_src\Port.c	   486  
; ..\mcal_src\Port.c	   487    if (ConfigPtr == NULL_PTR)
; ..\mcal_src\Port.c	   488    {
; ..\mcal_src\Port.c	   489  
; ..\mcal_src\Port.c	   490      /* Report PORT_E_PARAM_CONFIG DET */
; ..\mcal_src\Port.c	   491      #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	   492      Det_ReportError((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	   493          PORT_SID_INIT,PORT_E_PARAM_CONFIG);
; ..\mcal_src\Port.c	   494      #endif
; ..\mcal_src\Port.c	   495      /* PORT087: Skip the API functionality and return from the API */
; ..\mcal_src\Port.c	   496  
; ..\mcal_src\Port.c	   497      /* Report a safety error */
; ..\mcal_src\Port.c	   498      #if (PORT_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Port.c	   499      SafeMcal_ReportError ((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	   500                       PORT_SID_INIT,PORT_E_PARAM_CONFIG);
; ..\mcal_src\Port.c	   501      #endif
; ..\mcal_src\Port.c	   502  
; ..\mcal_src\Port.c	   503      ErrStatus = 1U;
; ..\mcal_src\Port.c	   504    }
; ..\mcal_src\Port.c	   505    #else
; ..\mcal_src\Port.c	   506    if (ConfigPtr != Port_kConfigPtr)
; ..\mcal_src\Port.c	   507    {
; ..\mcal_src\Port.c	   508  
; ..\mcal_src\Port.c	   509      /* Report PORT_E_PARAM_CONFIG DET */
; ..\mcal_src\Port.c	   510      #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	   511      Det_ReportError((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	   512        PORT_SID_INIT,PORT_E_PARAM_CONFIG);
; ..\mcal_src\Port.c	   513      #endif
; ..\mcal_src\Port.c	   514      /* PORT087: Skip the API functionality and return from the API */
; ..\mcal_src\Port.c	   515  
; ..\mcal_src\Port.c	   516      /* Report a safety error */
; ..\mcal_src\Port.c	   517      #if (PORT_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Port.c	   518      SafeMcal_ReportError ((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	   519                       PORT_SID_INIT,PORT_E_PARAM_CONFIG);
; ..\mcal_src\Port.c	   520      #endif
; ..\mcal_src\Port.c	   521  
; ..\mcal_src\Port.c	   522      ErrStatus = 1U;
; ..\mcal_src\Port.c	   523    }
; ..\mcal_src\Port.c	   524    #endif /*(PORT_PB_FIXEDADDR == STD_ON)*/
; ..\mcal_src\Port.c	   525  
; ..\mcal_src\Port.c	   526    #if (PORT_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Port.c	   527    if ((ErrStatus != 1U) &&
; ..\mcal_src\Port.c	   528        (ConfigPtr->PortMarker != ((uint32)PORT_MODULE_ID << 16U)))
; ..\mcal_src\Port.c	   529    {
; ..\mcal_src\Port.c	   530      /* report to upper layer */
; ..\mcal_src\Port.c	   531      SafeMcal_ReportError ((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	   532                       PORT_SID_INIT,PORT_E_PARAM_CONFIG);
; ..\mcal_src\Port.c	   533      ErrStatus = 1U;
; ..\mcal_src\Port.c	   534  
; ..\mcal_src\Port.c	   535    }
; ..\mcal_src\Port.c	   536    #endif
; ..\mcal_src\Port.c	   537    /*End Of PORT_SAFETY_ENABLE*/
; ..\mcal_src\Port.c	   538    #endif
; ..\mcal_src\Port.c	   539    /*(PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON)*/
; ..\mcal_src\Port.c	   540  
; ..\mcal_src\Port.c	   541  
; ..\mcal_src\Port.c	   542    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	   543    if (ErrStatus == 0U)
; ..\mcal_src\Port.c	   544    #endif
; ..\mcal_src\Port.c	   545    {
; ..\mcal_src\Port.c	   546      /* PORT002: The function Port_Init() shall also initialize all variables
; ..\mcal_src\Port.c	   547         used by the PORT driver module to an initial state.
; ..\mcal_src\Port.c	   548         There are no such variables in the port module
; ..\mcal_src\Port.c	   549      */
; ..\mcal_src\Port.c	   550      #if(PORT_PB_FIXEDADDR == STD_OFF)
; ..\mcal_src\Port.c	   551        Port_kConfigPtr = ConfigPtr;
	movh.a	a15,#@his(Port_kConfigPtr)
.L71:

; ..\mcal_src\Port.c	   552      #else
; ..\mcal_src\Port.c	   553        /* To remove GNU warning of unused parameter ConfigPtr */
; ..\mcal_src\Port.c	   554        UNUSED_PARAMETER(ConfigPtr)
; ..\mcal_src\Port.c	   555      #endif  /*(PORT_PB_FIXEDADDR == STD_OFF)*/
; ..\mcal_src\Port.c	   556  
; ..\mcal_src\Port.c	   557      /* Initialise General Purpose I/O Ports and Peripheral I/O Lines */
; ..\mcal_src\Port.c	   558      Port_lIOInit();
; ..\mcal_src\Port.c	   559  
; ..\mcal_src\Port.c	   560      #if(PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	   561      /* Set Status to indicate that initialization is done */
; ..\mcal_src\Port.c	   562      Port_InitStatus = PORT_INITIALIZED;
; ..\mcal_src\Port.c	   563      #endif
; ..\mcal_src\Port.c	   564    }
; ..\mcal_src\Port.c	   565  }/* Port_Init */
; ..\mcal_src\Port.c	   566  
; ..\mcal_src\Port.c	   567  
; ..\mcal_src\Port.c	   568  #if (PORT_INIT_CHECK_API == STD_ON)
; ..\mcal_src\Port.c	   569  /*******************************************************************************
; ..\mcal_src\Port.c	   570  ** Traceability     : [cover parentID=SAS_MCAL_PORT_0402,
; ..\mcal_src\Port.c	   571                        SAS_MCAL_PORT_0405,SAS_MCAL_PORT_0406,
; ..\mcal_src\Port.c	   572                        SAS_MCAL_PORT_0431,
; ..\mcal_src\Port.c	   573                        DS_MCAL_PORT_0403,DS_MCAL_PORT_0403_1,
; ..\mcal_src\Port.c	   574                        DS_MCAL_PORT_0404,DS_MCAL_PORT_0405,
; ..\mcal_src\Port.c	   575                        DS_MCAL_PORT_0406,DS_MCAL_PORT_0417,
; ..\mcal_src\Port.c	   576                        DS_NAS_PORT_PR123]                                      **
; ..\mcal_src\Port.c	   577  **                                                                            **
; ..\mcal_src\Port.c	   578  ** Syntax           : Std_ReturnType Port_InitCheck                           **
; ..\mcal_src\Port.c	   579  **                    (                                                       **
; ..\mcal_src\Port.c	   580  **                      const Port_ConfigType * ConfigPtr                     **
; ..\mcal_src\Port.c	   581  **                    )                                                       **
; ..\mcal_src\Port.c	   582  ** [/cover]                                                                   **
; ..\mcal_src\Port.c	   583  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	   584  **                                                                            **
; ..\mcal_src\Port.c	   585  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	   586  **                                                                            **
; ..\mcal_src\Port.c	   587  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Port.c	   588  **                                                                            **
; ..\mcal_src\Port.c	   589  ** Parameters(in)   : const Port_ConfigType * ConfigPtr - Pointer to          **
; ..\mcal_src\Port.c	   590  **                    PORT configuration                                      **
; ..\mcal_src\Port.c	   591  **                                                                            **
; ..\mcal_src\Port.c	   592  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	   593  **                                                                            **
; ..\mcal_src\Port.c	   594  ** Return value     : Std_ReturnType                                          **
; ..\mcal_src\Port.c	   595  **                                                                            **
; ..\mcal_src\Port.c	   596  ** Description      : This function checks Port SFR's and Global              **
; ..\mcal_src\Port.c	   597  **                      variables initialized by Port_Init Api                **
; ..\mcal_src\Port.c	   598  **                    This API is available only if PortInitCheckApi is TRUE  **
; ..\mcal_src\Port.c	   599  *******************************************************************************/
; ..\mcal_src\Port.c	   600  Std_ReturnType Port_InitCheck ( const Port_ConfigType * ConfigPtr )
; ..\mcal_src\Port.c	   601  {
; ..\mcal_src\Port.c	   602  
; ..\mcal_src\Port.c	   603    uint8 ErrStatus;
; ..\mcal_src\Port.c	   604    uint8 ErrPdr;
; ..\mcal_src\Port.c	   605    uint32 PortNumber;
; ..\mcal_src\Port.c	   606    /* Index to identify the port configuration information
; ..\mcal_src\Port.c	   607    from the configuration array  */
; ..\mcal_src\Port.c	   608    uint8 ConfigIndex;
; ..\mcal_src\Port.c	   609    Ifx_P  *PortAddressPtr;
; ..\mcal_src\Port.c	   610  
; ..\mcal_src\Port.c	   611    const uint32 *DataPtr;
; ..\mcal_src\Port.c	   612    const Port_n_ConfigType *ConfigDataPtr;
; ..\mcal_src\Port.c	   613  
; ..\mcal_src\Port.c	   614    const uint32  *PCSRDataPtr;
; ..\mcal_src\Port.c	   615    volatile uint32  *PCSRRegPtr;
; ..\mcal_src\Port.c	   616  
; ..\mcal_src\Port.c	   617    uint8 LevelFlag;
; ..\mcal_src\Port.c	   618    uint8 IocrFlag;
; ..\mcal_src\Port.c	   619  
; ..\mcal_src\Port.c	   620  
; ..\mcal_src\Port.c	   621    uint8 PCSRFlag;
; ..\mcal_src\Port.c	   622  
; ..\mcal_src\Port.c	   623    ErrStatus = E_OK;
; ..\mcal_src\Port.c	   624    ConfigIndex = 0U;
; ..\mcal_src\Port.c	   625    LevelFlag = 1U;
; ..\mcal_src\Port.c	   626    PCSRFlag = 1U;
; ..\mcal_src\Port.c	   627  
; ..\mcal_src\Port.c	   628  
; ..\mcal_src\Port.c	   629    if (Port_kConfigPtr != ConfigPtr)
; ..\mcal_src\Port.c	   630    {
; ..\mcal_src\Port.c	   631  
; ..\mcal_src\Port.c	   632       ErrStatus = E_NOT_OK;
; ..\mcal_src\Port.c	   633    }
; ..\mcal_src\Port.c	   634    else
; ..\mcal_src\Port.c	   635    {
; ..\mcal_src\Port.c	   636  
; ..\mcal_src\Port.c	   637      PCSRDataPtr = (const uint32*)(const void*)
; ..\mcal_src\Port.c	   638                                 (Port_kConfigPtr->Port_PCSRConfigTypePtr);
; ..\mcal_src\Port.c	   639  
; ..\mcal_src\Port.c	   640      for (PortNumber = 0U; PortNumber <= PORT_MAX_NUMBER ; PortNumber++)
; ..\mcal_src\Port.c	   641      {
; ..\mcal_src\Port.c	   642  
; ..\mcal_src\Port.c	   643        /* Parameter Port is checked for validity */
; ..\mcal_src\Port.c	   644        if(Port_lIsPortAvailable(PortNumber) != 0U)
; ..\mcal_src\Port.c	   645        {
; ..\mcal_src\Port.c	   646  
; ..\mcal_src\Port.c	   647          /* Check PDR Registers Initialization */
; ..\mcal_src\Port.c	   648  
; ..\mcal_src\Port.c	   649          /* Port driver strength is configured in Port PDR0 register */
; ..\mcal_src\Port.c	   650  
; ..\mcal_src\Port.c	   651          PortAddressPtr = Port_lAdr(PortNumber);
; ..\mcal_src\Port.c	   652  
; ..\mcal_src\Port.c	   653          /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	   654          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	   655          /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic used
; ..\mcal_src\Port.c	   656          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	   657          ErrPdr = Port_lChkPDR(PortNumber,
; ..\mcal_src\Port.c	   658          Port_kConfigPtr->PortConfigSetPtr[ConfigIndex].DriverStrength0,
; ..\mcal_src\Port.c	   659          Port_kConfigPtr->PortConfigSetPtr[ConfigIndex].DriverStrength1);
; ..\mcal_src\Port.c	   660  
; ..\mcal_src\Port.c	   661          if (ErrPdr == 0U)
; ..\mcal_src\Port.c	   662          {
; ..\mcal_src\Port.c	   663            /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	   664          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	   665            ConfigDataPtr = (Port_kConfigPtr->PortConfigSetPtr) + ConfigIndex ;
; ..\mcal_src\Port.c	   666  
; ..\mcal_src\Port.c	   667            /* Address of each port configuration */
; ..\mcal_src\Port.c	   668            DataPtr = (const uint32 *)(const void*)(ConfigDataPtr);
; ..\mcal_src\Port.c	   669  
; ..\mcal_src\Port.c	   670  
; ..\mcal_src\Port.c	   671            /* Check Port Level Registers Initialization */
; ..\mcal_src\Port.c	   672  
; ..\mcal_src\Port.c	   673            if(Port_lIsPortReadOnly(PortNumber) == 0U)
; ..\mcal_src\Port.c	   674            {
; ..\mcal_src\Port.c	   675              /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	   676                to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	   677              if(PORT_SFR_INIT_USER_MODE_READ32(PortAddressPtr->OUT.U) == \ 
; ..\mcal_src\Port.c	   678                                               (*(DataPtr + PORT_DATA_OFS_LEVEL)))
; ..\mcal_src\Port.c	   679              {
; ..\mcal_src\Port.c	   680                LevelFlag = 0U;
; ..\mcal_src\Port.c	   681              }
; ..\mcal_src\Port.c	   682            }
; ..\mcal_src\Port.c	   683            else
; ..\mcal_src\Port.c	   684            {
; ..\mcal_src\Port.c	   685               LevelFlag = 0U;
; ..\mcal_src\Port.c	   686            }
; ..\mcal_src\Port.c	   687  
; ..\mcal_src\Port.c	   688            if (LevelFlag == 0U)
; ..\mcal_src\Port.c	   689            {
; ..\mcal_src\Port.c	   690  
; ..\mcal_src\Port.c	   691               /* Check IOCR0,4,8,12 Registers Initialization */
; ..\mcal_src\Port.c	   692  
; ..\mcal_src\Port.c	   693               IocrFlag = Port_lChkIocr(PortNumber,DataPtr);
; ..\mcal_src\Port.c	   694  
; ..\mcal_src\Port.c	   695               if (IocrFlag == 0U)
; ..\mcal_src\Port.c	   696               {
; ..\mcal_src\Port.c	   697                 /* Check Port  PCSR Registers Initialization */
; ..\mcal_src\Port.c	   698                   if(Port_lIsPortPCSRAvailable(PortNumber) != 0U)
; ..\mcal_src\Port.c	   699                   {
; ..\mcal_src\Port.c	   700                     /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	   701                       to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	   702                     PCSRRegPtr = ((volatile uint32*)
; ..\mcal_src\Port.c	   703                                   (volatile void*)PortAddressPtr +
; ..\mcal_src\Port.c	   704                                    PORT_PCSR_REG_OFFSET);
; ..\mcal_src\Port.c	   705                     if (PORT_SFR_INIT_USER_MODE_READ32(*PCSRRegPtr)\ 
; ..\mcal_src\Port.c	   706                                                                  == *PCSRDataPtr)
; ..\mcal_src\Port.c	   707                     {
; ..\mcal_src\Port.c	   708                       PCSRFlag = 0U;
; ..\mcal_src\Port.c	   709                     }
; ..\mcal_src\Port.c	   710                     /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	   711                       due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	   712                     PCSRDataPtr++;
; ..\mcal_src\Port.c	   713                   }
; ..\mcal_src\Port.c	   714                   /* PCSR is not available then make PCSRFlag as FALSE */
; ..\mcal_src\Port.c	   715                   else
; ..\mcal_src\Port.c	   716                   {
; ..\mcal_src\Port.c	   717                      PCSRFlag = 0U;
; ..\mcal_src\Port.c	   718                   }
; ..\mcal_src\Port.c	   719               }
; ..\mcal_src\Port.c	   720            }
; ..\mcal_src\Port.c	   721          }
; ..\mcal_src\Port.c	   722          /* If  PCSRFlag as True, then return as error */
; ..\mcal_src\Port.c	   723          if(PCSRFlag != 0U)
; ..\mcal_src\Port.c	   724          {
; ..\mcal_src\Port.c	   725             ErrStatus = E_NOT_OK;
; ..\mcal_src\Port.c	   726             break;
; ..\mcal_src\Port.c	   727          }
; ..\mcal_src\Port.c	   728       ConfigIndex++;
; ..\mcal_src\Port.c	   729       }
; ..\mcal_src\Port.c	   730  
; ..\mcal_src\Port.c	   731       LevelFlag = 1U;
; ..\mcal_src\Port.c	   732       PCSRFlag = 1U;
; ..\mcal_src\Port.c	   733  
; ..\mcal_src\Port.c	   734       if(PortNumber == PORT_MAX_NUMBER)
; ..\mcal_src\Port.c	   735       {
; ..\mcal_src\Port.c	   736           /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	   737             to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	   738         if (PORT_SFR_INIT_USER_MODE_READ32(P40_PDISC.U) != \ 
; ..\mcal_src\Port.c	   739                                                   Port_kConfigPtr->PDiscSet[0U])
; ..\mcal_src\Port.c	   740         {
; ..\mcal_src\Port.c	   741           ErrStatus = E_NOT_OK;
; ..\mcal_src\Port.c	   742         }
; ..\mcal_src\Port.c	   743           /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	   744             to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	   745         if (PORT_SFR_INIT_USER_MODE_READ32(P41_PDISC.U) != \ 
; ..\mcal_src\Port.c	   746                                                   Port_kConfigPtr->PDiscSet[1U])
; ..\mcal_src\Port.c	   747         {
; ..\mcal_src\Port.c	   748           ErrStatus = E_NOT_OK;
; ..\mcal_src\Port.c	   749         }
; ..\mcal_src\Port.c	   750       }
; ..\mcal_src\Port.c	   751      } /* For loop */
; ..\mcal_src\Port.c	   752    } /* Else */
; ..\mcal_src\Port.c	   753    return(ErrStatus);
; ..\mcal_src\Port.c	   754  }
; ..\mcal_src\Port.c	   755  /*******************************************************************************
; ..\mcal_src\Port.c	   756  ** Syntax           : IFX_LOCAL_INLINE uint8 Port_lChkPDR                     **
; ..\mcal_src\Port.c	   757  **                    (                                                       **
; ..\mcal_src\Port.c	   758  **                      uint32 PortNo,uint32 Data_PDR0, uint32 Data_PDR1      **
; ..\mcal_src\Port.c	   759  **                    )                                                       **
; ..\mcal_src\Port.c	   760  **                                                                            **
; ..\mcal_src\Port.c	   761  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	   762  **                                                                            **
; ..\mcal_src\Port.c	   763  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	   764  **                                                                            **
; ..\mcal_src\Port.c	   765  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	   766  **                                                                            **
; ..\mcal_src\Port.c	   767  ** Parameters (in)  : uint32 PortNo - Port number whose  PDR to be checked    **
; ..\mcal_src\Port.c	   768  **                    uint32 Data_PDR0 - Data of DriverStrength0              **
; ..\mcal_src\Port.c	   769  **                    uint32 Data_PDR1 - Data of DriverStrength1              **
; ..\mcal_src\Port.c	   770  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	   771  **                                                                            **
; ..\mcal_src\Port.c	   772  ** Return value     : uint8                                                   **
; ..\mcal_src\Port.c	   773  **                    0- No error if PDR value is correct                     **
; ..\mcal_src\Port.c	   774  **                    1- Error if PDR value is not correct                    **
; ..\mcal_src\Port.c	   775  **                                                                            **
; ..\mcal_src\Port.c	   776  ** Description      :                                                         **
; ..\mcal_src\Port.c	   777  ** - The function like macro is to check if configured  Port PDR value is     **
; ..\mcal_src\Port.c	   778  **    same as read PDR value                                                  **
; ..\mcal_src\Port.c	   779  *******************************************************************************/
; ..\mcal_src\Port.c	   780  IFX_LOCAL_INLINE uint8 Port_lChkPDR(uint32 PortNo,uint32 Data_PDR0,
; ..\mcal_src\Port.c	   781                                     uint32 Data_PDR1)
; ..\mcal_src\Port.c	   782  {
; ..\mcal_src\Port.c	   783    uint8 ErrPdr = 0U;
; ..\mcal_src\Port.c	   784    uint32 PortReadOnly;
; ..\mcal_src\Port.c	   785    Ifx_P  *PortAddressPtr;
; ..\mcal_src\Port.c	   786  
; ..\mcal_src\Port.c	   787    PortAddressPtr = Port_lAdr(PortNo);
; ..\mcal_src\Port.c	   788    PortReadOnly = Port_lIsPortReadOnly(PortNo);
; ..\mcal_src\Port.c	   789  
; ..\mcal_src\Port.c	   790    if((PortNo < PORT_MAX_NUMBER) && (PortReadOnly == 0U))
; ..\mcal_src\Port.c	   791    {
; ..\mcal_src\Port.c	   792      if(PORT_SFR_INIT_USER_MODE_READ32(PortAddressPtr->PDR0.U) != Data_PDR0)
; ..\mcal_src\Port.c	   793      {
; ..\mcal_src\Port.c	   794        ErrPdr = 1U;
; ..\mcal_src\Port.c	   795      }
; ..\mcal_src\Port.c	   796  
; ..\mcal_src\Port.c	   797      if(Port_lIsPortPdr1Available(PortNo) != 0U)
; ..\mcal_src\Port.c	   798      {
; ..\mcal_src\Port.c	   799  
; ..\mcal_src\Port.c	   800        if(PORT_SFR_INIT_USER_MODE_READ32(PortAddressPtr->PDR1.U) != Data_PDR1)
; ..\mcal_src\Port.c	   801        {
; ..\mcal_src\Port.c	   802          ErrPdr = 1U;
; ..\mcal_src\Port.c	   803        }
; ..\mcal_src\Port.c	   804      }
; ..\mcal_src\Port.c	   805    }
; ..\mcal_src\Port.c	   806    return(ErrPdr);
; ..\mcal_src\Port.c	   807  }
; ..\mcal_src\Port.c	   808  /*******************************************************************************
; ..\mcal_src\Port.c	   809  ** Syntax           : IFX_LOCAL_INLINE uint8 Port_lChkIocr                    **
; ..\mcal_src\Port.c	   810  **                    (                                                       **
; ..\mcal_src\Port.c	   811  **                      uint32 PortNo,const uint32 *DataPtr                   **
; ..\mcal_src\Port.c	   812  **                    )                                                       **
; ..\mcal_src\Port.c	   813  **                                                                            **
; ..\mcal_src\Port.c	   814  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	   815  **                                                                            **
; ..\mcal_src\Port.c	   816  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	   817  **                                                                            **
; ..\mcal_src\Port.c	   818  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	   819  **                                                                            **
; ..\mcal_src\Port.c	   820  ** Parameters (in)  : uint32 PortNo - Port number whose Iocr to be checked    **
; ..\mcal_src\Port.c	   821  **                    const uint32 *DataPtr - Data read from Iocr register    **
; ..\mcal_src\Port.c	   822  **                                                                            **
; ..\mcal_src\Port.c	   823  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	   824  **                                                                            **
; ..\mcal_src\Port.c	   825  ** Return value     : uint8                                                   **
; ..\mcal_src\Port.c	   826  **                    0 -No error if Iocr register value is correct           **
; ..\mcal_src\Port.c	   827  **                    1 -Error if Iocr register  value is not correct         **
; ..\mcal_src\Port.c	   828  **                                                                            **
; ..\mcal_src\Port.c	   829  ** Description      :                                                         **
; ..\mcal_src\Port.c	   830  ** - The function like macro is to check if configured  Port Iocr value is    **
; ..\mcal_src\Port.c	   831  **    same as read Iocr value                                                 **
; ..\mcal_src\Port.c	   832  *******************************************************************************/
; ..\mcal_src\Port.c	   833  IFX_LOCAL_INLINE uint8 Port_lChkIocr(uint32 PortNo,const uint32 *DataPtr)
; ..\mcal_src\Port.c	   834  {
; ..\mcal_src\Port.c	   835    uint8 RetVal;
; ..\mcal_src\Port.c	   836    Ifx_P  *PortAddressPtr;
; ..\mcal_src\Port.c	   837    uint32 Index;
; ..\mcal_src\Port.c	   838    uint16 PinAvailable;
; ..\mcal_src\Port.c	   839    uint32 PinMask;
; ..\mcal_src\Port.c	   840    uint8 PinPosition;
; ..\mcal_src\Port.c	   841  
; ..\mcal_src\Port.c	   842    RetVal = 0U;
; ..\mcal_src\Port.c	   843  
; ..\mcal_src\Port.c	   844    PortAddressPtr = Port_lAdr(PortNo);
; ..\mcal_src\Port.c	   845  
; ..\mcal_src\Port.c	   846    if(Port_lIsPortIocrAvailable(PortNo,(uint16)PORT_PIN_0_3)!= 0U)
; ..\mcal_src\Port.c	   847    {
; ..\mcal_src\Port.c	   848      PinPosition = 0U;
; ..\mcal_src\Port.c	   849      PinMask = 0U;
; ..\mcal_src\Port.c	   850      for(Index = 0U; Index<4U ; Index++)
; ..\mcal_src\Port.c	   851      {
; ..\mcal_src\Port.c	   852  
; ..\mcal_src\Port.c	   853         PinAvailable  = Port_lIsPinAvailable(PortNo,Index);
; ..\mcal_src\Port.c	   854  
; ..\mcal_src\Port.c	   855         if (PinAvailable)
; ..\mcal_src\Port.c	   856         {
; ..\mcal_src\Port.c	   857           PinMask |= ((uint32)0xF8U << PinPosition);
; ..\mcal_src\Port.c	   858         }
; ..\mcal_src\Port.c	   859  
; ..\mcal_src\Port.c	   860        PinPosition += 8U;
; ..\mcal_src\Port.c	   861      }
; ..\mcal_src\Port.c	   862      if((PORT_SFR_INIT_USER_MODE_READ32(PortAddressPtr->IOCR0.U) & PinMask) != \ 
; ..\mcal_src\Port.c	   863                                                          ((*DataPtr) & PinMask))
; ..\mcal_src\Port.c	   864      {
; ..\mcal_src\Port.c	   865        RetVal = 1U;
; ..\mcal_src\Port.c	   866      }
; ..\mcal_src\Port.c	   867    }
; ..\mcal_src\Port.c	   868    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	   869     due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	   870    DataPtr++;
; ..\mcal_src\Port.c	   871  
; ..\mcal_src\Port.c	   872    if(Port_lIsPortIocrAvailable(PortNo,(uint16)PORT_PIN_4_7)!= 0U)
; ..\mcal_src\Port.c	   873    {
; ..\mcal_src\Port.c	   874      PinPosition = 0U;
; ..\mcal_src\Port.c	   875      PinMask = 0U;
; ..\mcal_src\Port.c	   876      for(Index = 4U; Index<8U ; Index++)
; ..\mcal_src\Port.c	   877      {
; ..\mcal_src\Port.c	   878  
; ..\mcal_src\Port.c	   879         PinAvailable  = Port_lIsPinAvailable(PortNo,Index);
; ..\mcal_src\Port.c	   880  
; ..\mcal_src\Port.c	   881         if (PinAvailable)
; ..\mcal_src\Port.c	   882         {
; ..\mcal_src\Port.c	   883           PinMask |= ((uint32)0xF8U << PinPosition);
; ..\mcal_src\Port.c	   884         }
; ..\mcal_src\Port.c	   885        PinPosition += 8U;
; ..\mcal_src\Port.c	   886      }
; ..\mcal_src\Port.c	   887      if((PORT_SFR_INIT_USER_MODE_READ32(PortAddressPtr->IOCR4.U) & PinMask) != \ 
; ..\mcal_src\Port.c	   888                                                          ((*DataPtr) & PinMask))
; ..\mcal_src\Port.c	   889      {
; ..\mcal_src\Port.c	   890        RetVal = 1U;
; ..\mcal_src\Port.c	   891      }
; ..\mcal_src\Port.c	   892    }
; ..\mcal_src\Port.c	   893    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	   894     due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	   895    DataPtr++;
; ..\mcal_src\Port.c	   896  
; ..\mcal_src\Port.c	   897    if(Port_lIsPortIocrAvailable(PortNo,(uint16)PORT_PIN_8_11)!= 0U)
; ..\mcal_src\Port.c	   898    {
; ..\mcal_src\Port.c	   899      PinPosition = 0U;
; ..\mcal_src\Port.c	   900      PinMask = 0U;
; ..\mcal_src\Port.c	   901      for(Index = 8U; Index<12U ; Index++)
; ..\mcal_src\Port.c	   902      {
; ..\mcal_src\Port.c	   903  
; ..\mcal_src\Port.c	   904         PinAvailable  = Port_lIsPinAvailable(PortNo,Index);
; ..\mcal_src\Port.c	   905  
; ..\mcal_src\Port.c	   906         if (PinAvailable)
; ..\mcal_src\Port.c	   907         {
; ..\mcal_src\Port.c	   908           PinMask |= ((uint32)0xF8U << PinPosition);
; ..\mcal_src\Port.c	   909         }
; ..\mcal_src\Port.c	   910        PinPosition += 8U;
; ..\mcal_src\Port.c	   911      }
; ..\mcal_src\Port.c	   912  
; ..\mcal_src\Port.c	   913      if((PORT_SFR_INIT_USER_MODE_READ32(PortAddressPtr->IOCR8.U) & PinMask) != \ 
; ..\mcal_src\Port.c	   914                                                          ((*DataPtr) & PinMask))
; ..\mcal_src\Port.c	   915      {
; ..\mcal_src\Port.c	   916        RetVal = 1U;
; ..\mcal_src\Port.c	   917      }
; ..\mcal_src\Port.c	   918    }
; ..\mcal_src\Port.c	   919    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	   920      due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	   921    DataPtr++;
; ..\mcal_src\Port.c	   922  
; ..\mcal_src\Port.c	   923    if(Port_lIsPortIocrAvailable(PortNo,(uint16)PORT_PIN_12_15)!= 0U)
; ..\mcal_src\Port.c	   924    {
; ..\mcal_src\Port.c	   925      PinPosition = 0U;
; ..\mcal_src\Port.c	   926      PinMask = 0U;
; ..\mcal_src\Port.c	   927      for(Index = 12U; Index<16U ; Index++)
; ..\mcal_src\Port.c	   928      {
; ..\mcal_src\Port.c	   929  
; ..\mcal_src\Port.c	   930         PinAvailable  = Port_lIsPinAvailable(PortNo,Index);
; ..\mcal_src\Port.c	   931  
; ..\mcal_src\Port.c	   932         if (PinAvailable)
; ..\mcal_src\Port.c	   933         {
; ..\mcal_src\Port.c	   934           PinMask |= ((uint32)0xF8U << PinPosition);
; ..\mcal_src\Port.c	   935         }
; ..\mcal_src\Port.c	   936        PinPosition += 8U;
; ..\mcal_src\Port.c	   937      }
; ..\mcal_src\Port.c	   938      if((PORT_SFR_INIT_USER_MODE_READ32(PortAddressPtr->IOCR12.U) & PinMask) != \ 
; ..\mcal_src\Port.c	   939                                                          ((*DataPtr) & PinMask))
; ..\mcal_src\Port.c	   940      {
; ..\mcal_src\Port.c	   941        RetVal = 1U;
; ..\mcal_src\Port.c	   942      }
; ..\mcal_src\Port.c	   943    }
; ..\mcal_src\Port.c	   944  
; ..\mcal_src\Port.c	   945    return(RetVal);
; ..\mcal_src\Port.c	   946  }
; ..\mcal_src\Port.c	   947  
; ..\mcal_src\Port.c	   948  
; ..\mcal_src\Port.c	   949  #endif
; ..\mcal_src\Port.c	   950  
; ..\mcal_src\Port.c	   951  /* Enable / Disable the use of the function */
; ..\mcal_src\Port.c	   952  #if (PORT_SET_PIN_DIRECTION_API == STD_ON)
; ..\mcal_src\Port.c	   953  /*******************************************************************************
; ..\mcal_src\Port.c	   954  ** Traceability     : [cover parentID=DS_AS_PORT141,DS_AS_PORT086,
; ..\mcal_src\Port.c	   955                         DS_AS_PORT075_1,
; ..\mcal_src\Port.c	   956                         DS_MCAL_PORT_0416,DS_AS_PORT063,DS_AS_PORT137,
; ..\mcal_src\Port.c	   957                         DS_AS_PORT101_PORT211_2,DS_AS_PORT107_PORT146_2,
; ..\mcal_src\Port.c	   958                         DS_AS4XX_PORT087_2,DS_AS3XX_PORT087_2,DS_AS_PORT054,
; ..\mcal_src\Port.c	   959                         DS_AS_PORT213,
; ..\mcal_src\Port.c	   960                         DS_AS_PORT138,DS_AS_PORT077,DS_NAS_PORT_PR912_1,
; ..\mcal_src\Port.c	   961                         SAS_MCAL_PORT_0415,
; ..\mcal_src\Port.c	   962                         DS_MCAL_PORT_0417, DS_AS_PORT107_PORT146_4]            **
; ..\mcal_src\Port.c	   963  **                                                                            **
; ..\mcal_src\Port.c	   964  ** Syntax           : void Port_SetPinDirection                               **
; ..\mcal_src\Port.c	   965  **                    (                                                       **
; ..\mcal_src\Port.c	   966  **                      Port_PinType Pin,                                     **
; ..\mcal_src\Port.c	   967  **                      Port_PinDirectionType Direction                       **
; ..\mcal_src\Port.c	   968  **                    )                                                       **
; ..\mcal_src\Port.c	   969  ** [/cover]                                                                   **
; ..\mcal_src\Port.c	   970  **                                                                            **
; ..\mcal_src\Port.c	   971  ** Service ID       : 0x01                                                    **
; ..\mcal_src\Port.c	   972  **                                                                            **
; ..\mcal_src\Port.c	   973  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	   974  **                                                                            **
; ..\mcal_src\Port.c	   975  ** Reentrancy       : Re-entrant                                              **
; ..\mcal_src\Port.c	   976  **                    (Reentrant for pins of different port or pins assigned  **
; ..\mcal_src\Port.c	   977  **                     to different IOCR register of a same port)             **
; ..\mcal_src\Port.c	   978  **                                                                            **
; ..\mcal_src\Port.c	   979  ** Parameters (in)  : Port_PinType Pin - port pin ID whose direction has      **
; ..\mcal_src\Port.c	   980  **                    to be set                                               **
; ..\mcal_src\Port.c	   981  **                    Port_PinDirectionType Direction - port pin direction    **
; ..\mcal_src\Port.c	   982                        to be set                                               **
; ..\mcal_src\Port.c	   983  **                                                                            **
; ..\mcal_src\Port.c	   984  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	   985  **                                                                            **
; ..\mcal_src\Port.c	   986  ** Return value     : none                                                    **
; ..\mcal_src\Port.c	   987  **                                                                            **
; ..\mcal_src\Port.c	   988  ** Description      : This function:                                          **
; ..\mcal_src\Port.c	   989  **  - PORT063: sets the port pin direction during runtime                     **
; ..\mcal_src\Port.c	   990  **  - PORT059: This functionality is available for parameter Pin              **
; ..\mcal_src\Port.c	   991  **     whose direction is configured as changeable during run time            **
; ..\mcal_src\Port.c	   992  **  - PORT086: This function is only available if the pre-compiler switch     **
; ..\mcal_src\Port.c	   993  **    PORT_SET_PIN_DIRECTION_API is set STD_ON.                               **
; ..\mcal_src\Port.c	   994  **                                                                            **
; ..\mcal_src\Port.c	   995  *******************************************************************************/
; ..\mcal_src\Port.c	   996  void Port_SetPinDirection(Port_PinType Pin, Port_PinDirectionType Direction)
; ..\mcal_src\Port.c	   997  {
; ..\mcal_src\Port.c	   998    /* Each Port Number for the hardware unit  */
; ..\mcal_src\Port.c	   999    uint32               PortNumber;
; ..\mcal_src\Port.c	  1000    uint32               PinNumber;
; ..\mcal_src\Port.c	  1001    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1002    uint8                ErrStatus;
; ..\mcal_src\Port.c	  1003    #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1004    uint32               PortAvailable;
; ..\mcal_src\Port.c	  1005    uint32               PortReadOnly;
; ..\mcal_src\Port.c	  1006    uint32               PinAvailable;
; ..\mcal_src\Port.c	  1007    #endif /*PORT_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Port.c	  1008    #endif /*PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Port.c	  1009    uint32               ConfigIndex;
; ..\mcal_src\Port.c	  1010    uint32               Index;
; ..\mcal_src\Port.c	  1011    volatile uint32      *IocrRegPtr;
; ..\mcal_src\Port.c	  1012    const uint32         *DataPtr;
; ..\mcal_src\Port.c	  1013    Ifx_P                *PortAddressPtr;
; ..\mcal_src\Port.c	  1014    const uint8          *IocrDataPtr;
; ..\mcal_src\Port.c	  1015  
; ..\mcal_src\Port.c	  1016    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1017    ErrStatus = 0U;
; ..\mcal_src\Port.c	  1018    #endif
; ..\mcal_src\Port.c	  1019  
; ..\mcal_src\Port.c	  1020    #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1021    /* Check for DET: PORT_E_UNINIT */
; ..\mcal_src\Port.c	  1022    if (Port_InitStatus != PORT_INITIALIZED)
; ..\mcal_src\Port.c	  1023    {
; ..\mcal_src\Port.c	  1024      /* Report PORT_E_UNINIT DET if Port initialisation is not done */
; ..\mcal_src\Port.c	  1025      Det_ReportError((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1026          PORT_SID_SETPINDIRECTION,PORT_E_UNINIT);
; ..\mcal_src\Port.c	  1027  
; ..\mcal_src\Port.c	  1028      /* PORT087: Skip the API functionality and return from the API */
; ..\mcal_src\Port.c	  1029    }
; ..\mcal_src\Port.c	  1030    else
; ..\mcal_src\Port.c	  1031    #endif /* PORT_DEV_ERROR_DETECT */
; ..\mcal_src\Port.c	  1032    {
; ..\mcal_src\Port.c	  1033      /* Extract the port number and pin number from the Pin Symbolic ID */
; ..\mcal_src\Port.c	  1034      PortNumber = (uint32)Port_lNumber(Pin);
; ..\mcal_src\Port.c	  1035      PinNumber  = (uint32)Port_lPinNumber(Pin);
; ..\mcal_src\Port.c	  1036  
; ..\mcal_src\Port.c	  1037      #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1038      /* PORT077: Function parameters are checked in the order in which they are
; ..\mcal_src\Port.c	  1039         passed.*/
; ..\mcal_src\Port.c	  1040      /* PORT087: Check if Pin is valid */
; ..\mcal_src\Port.c	  1041  
; ..\mcal_src\Port.c	  1042      #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1043      PortAvailable = Port_lIsPortAvailable(PortNumber);
; ..\mcal_src\Port.c	  1044      PortReadOnly  = Port_lIsPortReadOnly(PortNumber);
; ..\mcal_src\Port.c	  1045  
; ..\mcal_src\Port.c	  1046      PinAvailable = 0U;
; ..\mcal_src\Port.c	  1047      if( PortAvailable != 0U )
; ..\mcal_src\Port.c	  1048      {
; ..\mcal_src\Port.c	  1049        PinAvailable  = Port_lIsPinAvailable(PortNumber,PinNumber);
; ..\mcal_src\Port.c	  1050      }
; ..\mcal_src\Port.c	  1051  
; ..\mcal_src\Port.c	  1052      if ( (Pin > PORT_MAX_PIN_ID) ||
; ..\mcal_src\Port.c	  1053           ((PortReadOnly != 0U) || (PinAvailable == 0U))
; ..\mcal_src\Port.c	  1054         )
; ..\mcal_src\Port.c	  1055      {
; ..\mcal_src\Port.c	  1056  
; ..\mcal_src\Port.c	  1057        /*Report PORT_E_PARAM_PIN DET if the pin is out of range */
; ..\mcal_src\Port.c	  1058        Det_ReportError((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1059                  PORT_SID_SETPINDIRECTION,PORT_E_PARAM_PIN);
; ..\mcal_src\Port.c	  1060  
; ..\mcal_src\Port.c	  1061        ErrStatus = 1U;
; ..\mcal_src\Port.c	  1062      }
; ..\mcal_src\Port.c	  1063      #endif
; ..\mcal_src\Port.c	  1064  
; ..\mcal_src\Port.c	  1065      #if (PORT_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Port.c	  1066      if(Pin > PORT_MAX_PIN_ID)
; ..\mcal_src\Port.c	  1067      {
; ..\mcal_src\Port.c	  1068        /*Report Safety Error if the pin is out of range */
; ..\mcal_src\Port.c	  1069        SafeMcal_ReportError ((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1070           PORT_SID_SETPINDIRECTION,PORT_E_PARAM_PIN);
; ..\mcal_src\Port.c	  1071  
; ..\mcal_src\Port.c	  1072        ErrStatus = 1U;
; ..\mcal_src\Port.c	  1073  
; ..\mcal_src\Port.c	  1074      }
; ..\mcal_src\Port.c	  1075  
; ..\mcal_src\Port.c	  1076      if((Direction != PORT_PIN_IN) && (Direction != PORT_PIN_OUT))
; ..\mcal_src\Port.c	  1077      {
; ..\mcal_src\Port.c	  1078        /*Report Safety Error if the direction is out of range */
; ..\mcal_src\Port.c	  1079        SafeMcal_ReportError ((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1080                        PORT_SID_SETPINDIRECTION,PORT_E_PARAM_DIRECTION);
; ..\mcal_src\Port.c	  1081  
; ..\mcal_src\Port.c	  1082        ErrStatus = 1U;
; ..\mcal_src\Port.c	  1083  
; ..\mcal_src\Port.c	  1084      }
; ..\mcal_src\Port.c	  1085      #endif
; ..\mcal_src\Port.c	  1086      #endif /* PORT_DEV_ERROR_DETECT and PORT_SAFETY_ENABLE */
; ..\mcal_src\Port.c	  1087  
; ..\mcal_src\Port.c	  1088  
; ..\mcal_src\Port.c	  1089      #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1090      if (ErrStatus == 0U)
; ..\mcal_src\Port.c	  1091      #endif
; ..\mcal_src\Port.c	  1092      {
; ..\mcal_src\Port.c	  1093        ConfigIndex = 0U;
; ..\mcal_src\Port.c	  1094        for(Index = 0U;Index < PortNumber;Index++)
; ..\mcal_src\Port.c	  1095        {
; ..\mcal_src\Port.c	  1096          if(Port_lIsPortAvailable(Index) != 0U)
; ..\mcal_src\Port.c	  1097          {
; ..\mcal_src\Port.c	  1098            ConfigIndex++; /* to identify the Index of configuration*/
; ..\mcal_src\Port.c	  1099          }
; ..\mcal_src\Port.c	  1100        }
; ..\mcal_src\Port.c	  1101  
; ..\mcal_src\Port.c	  1102        /* Get the config data location for specified Port */
; ..\mcal_src\Port.c	  1103        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1104            due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1105        DataPtr =(const uint32*)(const void*)(
; ..\mcal_src\Port.c	  1106                  (Port_kConfigPtr->PortConfigSetPtr)+ConfigIndex);
; ..\mcal_src\Port.c	  1107  
; ..\mcal_src\Port.c	  1108        /*parameter Pin is valid, check for pin direction changeable DET error */
; ..\mcal_src\Port.c	  1109        /*
; ..\mcal_src\Port.c	  1110        PORT059: PORT087: Check if the direction is configured as changeable or
; ..\mcal_src\Port.c	  1111        not
; ..\mcal_src\Port.c	  1112        */
; ..\mcal_src\Port.c	  1113        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1114          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1115        if ( ((*(DataPtr + PORT_DATA_OFS_DIR)) & ((uint32)0x01U << PinNumber))
; ..\mcal_src\Port.c	  1116               == PORT_PIN_DIR_NOT_CHANGEABLE
; ..\mcal_src\Port.c	  1117                )
; ..\mcal_src\Port.c	  1118        {
; ..\mcal_src\Port.c	  1119         #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1120           /* Report PORT_E_DIRECTION_UNCHANGEABLE DET if pin is configured for
; ..\mcal_src\Port.c	  1121              direction not changeable
; ..\mcal_src\Port.c	  1122           */
; ..\mcal_src\Port.c	  1123           Det_ReportError(
; ..\mcal_src\Port.c	  1124             (uint16)PORT_MODULE_ID,
; ..\mcal_src\Port.c	  1125             PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1126             PORT_SID_SETPINDIRECTION,
; ..\mcal_src\Port.c	  1127             PORT_E_DIRECTION_UNCHANGEABLE);
; ..\mcal_src\Port.c	  1128         #endif /* PORT_DEV_ERROR_DETECT */
; ..\mcal_src\Port.c	  1129        }
; ..\mcal_src\Port.c	  1130        else
; ..\mcal_src\Port.c	  1131        {
; ..\mcal_src\Port.c	  1132          /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1133            due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	  1134          IocrDataPtr = (const uint8*)(const void*)(DataPtr + PORT_DATA_OFS_CTL);
; ..\mcal_src\Port.c	  1135  
; ..\mcal_src\Port.c	  1136          /* Get Port Address */
; ..\mcal_src\Port.c	  1137          PortAddressPtr = Port_lAdr(PortNumber);
; ..\mcal_src\Port.c	  1138          /* Get the IOCR0 register address of particular port */
; ..\mcal_src\Port.c	  1139          /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1140            to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1141          IocrRegPtr = ((volatile uint32*)(volatile void*) \ 
; ..\mcal_src\Port.c	  1142                                 PortAddressPtr + PORT_IOCR0_REG_OFFSET);
; ..\mcal_src\Port.c	  1143  
; ..\mcal_src\Port.c	  1144          /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1145             due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1146          if((*(IocrDataPtr + PinNumber) & (uint8)PORT_DIR_MSK)==(uint8)Direction)
; ..\mcal_src\Port.c	  1147          {
; ..\mcal_src\Port.c	  1148            /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1149             due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	  1150            /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1151              to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1152            PORT_SFR_RUNTIME_USER_MODE_MODIFY32((*(IocrRegPtr + \ 
; ..\mcal_src\Port.c	  1153              (PinNumber / PORT_NUM_FOUR))),PORT_PIN_IOCR0_CLEARMASK(PinNumber), \ 
; ..\mcal_src\Port.c	  1154                                     ((uint32)(*(IocrDataPtr + PinNumber)) << \ 
; ..\mcal_src\Port.c	  1155                                       (PORT_IOCR0_BIT_SHIFT_COUNT(PinNumber))))
; ..\mcal_src\Port.c	  1156          }
; ..\mcal_src\Port.c	  1157          else
; ..\mcal_src\Port.c	  1158          {
; ..\mcal_src\Port.c	  1159            /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1160               to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1161            /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1162              due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1163            /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1164              due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1165            PORT_SFR_RUNTIME_USER_MODE_MODIFY32((*(IocrRegPtr + \ 
; ..\mcal_src\Port.c	  1166            (PinNumber / PORT_NUM_FOUR))),PORT_PIN_IOCR0_CLEARMASK(PinNumber), \ 
; ..\mcal_src\Port.c	  1167                                             (*((DataPtr + PORT_DATA_OFS_CTL2) \ 
; ..\mcal_src\Port.c	  1168             + (PinNumber / PORT_NUM_FOUR)) & PORT_PIN_IOCR0_SETMASK(PinNumber)))
; ..\mcal_src\Port.c	  1169          }
; ..\mcal_src\Port.c	  1170        }
; ..\mcal_src\Port.c	  1171      }
; ..\mcal_src\Port.c	  1172    }
; ..\mcal_src\Port.c	  1173  } /* Port_SetPinDirection */
; ..\mcal_src\Port.c	  1174  #endif /* Direction changes allowed / Port_SetPinDirection API is ON */
; ..\mcal_src\Port.c	  1175  
; ..\mcal_src\Port.c	  1176  /*******************************************************************************
; ..\mcal_src\Port.c	  1177  ** Traceability     : [cover parentID=DS_AS_PORT142,DS_AS_PORT075_2,
; ..\mcal_src\Port.c	  1178                          DS_AS_PORT101_PORT211_3,DS_AS_PORT107_PORT146_3,
; ..\mcal_src\Port.c	  1179                          DS_AS4XX_PORT087_3,DS_AS3XX_PORT087_3,
; ..\mcal_src\Port.c	  1180                          DS_AS_PORT060_PORT061_PORT066,
; ..\mcal_src\Port.c	  1181                          DS_AS_PORT213,
; ..\mcal_src\Port.c	  1182                          DS_AS_PORT107_PORT146_4]          **
; ..\mcal_src\Port.c	  1183  **                                                                            **
; ..\mcal_src\Port.c	  1184  ** Syntax           : void Port_RefreshPortDirection ( void )                 **
; ..\mcal_src\Port.c	  1185  **                                                                            **
; ..\mcal_src\Port.c	  1186  ** [/cover]                                                                   **
; ..\mcal_src\Port.c	  1187  **                                                                            **
; ..\mcal_src\Port.c	  1188  ** Service ID       : 0x02                                                    **
; ..\mcal_src\Port.c	  1189  **                                                                            **
; ..\mcal_src\Port.c	  1190  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1191  **                                                                            **
; ..\mcal_src\Port.c	  1192  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Port.c	  1193  **                                                                            **
; ..\mcal_src\Port.c	  1194  ** Parameters (in)  : none                                                    **
; ..\mcal_src\Port.c	  1195  **                                                                            **
; ..\mcal_src\Port.c	  1196  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	  1197  **                                                                            **
; ..\mcal_src\Port.c	  1198  ** Return value     : none                                                    **
; ..\mcal_src\Port.c	  1199  **                                                                            **
; ..\mcal_src\Port.c	  1200  ** Description      : This function:                                          **
; ..\mcal_src\Port.c	  1201  **   - PORT060: PORT061: PORT066: refreshes the direction for all the port    **
; ..\mcal_src\Port.c	  1202  **     pins that are configured as direction not changeable during run time   **
; ..\mcal_src\Port.c	  1203  **                                                                            **
; ..\mcal_src\Port.c	  1204  *******************************************************************************/
; ..\mcal_src\Port.c	  1205  void Port_RefreshPortDirection(void)
; ..\mcal_src\Port.c	  1206  {
; ..\mcal_src\Port.c	  1207    uint32                LoopCtr;
; ..\mcal_src\Port.c	  1208    /* Each Port Number for the hardware unit  */
; ..\mcal_src\Port.c	  1209    uint32                PortNumber;
; ..\mcal_src\Port.c	  1210    uint32                ConfigIndex;
; ..\mcal_src\Port.c	  1211    #if (PORT_SET_PIN_DIRECTION_API == STD_ON)
; ..\mcal_src\Port.c	  1212    /* Direction changeable allowed */
; ..\mcal_src\Port.c	  1213    uint32                DirectionData;
; ..\mcal_src\Port.c	  1214    uint32                PinPos;
; ..\mcal_src\Port.c	  1215    #endif /* PORT_SET_PIN_DIRECTION_API */
; ..\mcal_src\Port.c	  1216    const uint32          *DataPtr;
; ..\mcal_src\Port.c	  1217    const uint8           *IocrDataPtr;
; ..\mcal_src\Port.c	  1218    volatile uint32       *IocrRegPtr;
; ..\mcal_src\Port.c	  1219    Ifx_P *PortAddressPtr;
; ..\mcal_src\Port.c	  1220  
; ..\mcal_src\Port.c	  1221    #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1222    /* Check for DET: PORT_E_UNINIT */
; ..\mcal_src\Port.c	  1223    if (Port_InitStatus != PORT_INITIALIZED)
; ..\mcal_src\Port.c	  1224    {
; ..\mcal_src\Port.c	  1225      /* Report PORT_E_UNINIT DET if Port initialisation is not done
; ..\mcal_src\Port.c	  1226      */
; ..\mcal_src\Port.c	  1227     Det_ReportError(
; ..\mcal_src\Port.c	  1228        (uint16)PORT_MODULE_ID,
; ..\mcal_src\Port.c	  1229        PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1230        PORT_SID_REFRESHPORTDIR,
; ..\mcal_src\Port.c	  1231        PORT_E_UNINIT);
; ..\mcal_src\Port.c	  1232      /* PORT087: Skip the API functionality and return from the API */
; ..\mcal_src\Port.c	  1233    }
; ..\mcal_src\Port.c	  1234    else
; ..\mcal_src\Port.c	  1235    #endif /* (PORT_DEV_ERROR_DETECT) == STD_ON*/
; ..\mcal_src\Port.c	  1236    {
; ..\mcal_src\Port.c	  1237     /* Loop from Port 0 till last Port */
; ..\mcal_src\Port.c	  1238     PortNumber = 0U;
; ..\mcal_src\Port.c	  1239     ConfigIndex = 0U;
; ..\mcal_src\Port.c	  1240     do
; ..\mcal_src\Port.c	  1241     {
; ..\mcal_src\Port.c	  1242       if(Port_lIsPortAvailable(PortNumber) != 0U)
; ..\mcal_src\Port.c	  1243       {
; ..\mcal_src\Port.c	  1244         /* Pointer to the data for the port */
; ..\mcal_src\Port.c	  1245         /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1246          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1247         DataPtr = (const uint32*)(const void*)
; ..\mcal_src\Port.c	  1248                   ((Port_kConfigPtr->PortConfigSetPtr) + ConfigIndex);
; ..\mcal_src\Port.c	  1249  
; ..\mcal_src\Port.c	  1250         #if (PORT_SET_PIN_DIRECTION_API == STD_ON)
; ..\mcal_src\Port.c	  1251         /* Direction changeable allowed */
; ..\mcal_src\Port.c	  1252         /* Get the direction changeable configuration for all the port pins */
; ..\mcal_src\Port.c	  1253         /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1254          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1255         DirectionData = *(DataPtr + PORT_DATA_OFS_DIR);
; ..\mcal_src\Port.c	  1256         #endif /* PORT_SET_PIN_DIRECTION_API == STD_ON*/
; ..\mcal_src\Port.c	  1257  
; ..\mcal_src\Port.c	  1258         /* Pointer to control data for the first pin */
; ..\mcal_src\Port.c	  1259         /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1260          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1261         IocrDataPtr = (const uint8*)(const void*)(DataPtr + PORT_DATA_OFS_CTL);
; ..\mcal_src\Port.c	  1262  
; ..\mcal_src\Port.c	  1263         /* Pointer to control register for the first pin */
; ..\mcal_src\Port.c	  1264         /* Get Port Address */
; ..\mcal_src\Port.c	  1265         PortAddressPtr = Port_lAdr(PortNumber);
; ..\mcal_src\Port.c	  1266         /* Get the IOCR0 register address of particular port */
; ..\mcal_src\Port.c	  1267         /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1268            to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1269         IocrRegPtr = ((volatile uint32 *)(volatile void*)PortAddressPtr + \ 
; ..\mcal_src\Port.c	  1270                                                 PORT_IOCR0_REG_OFFSET);
; ..\mcal_src\Port.c	  1271  
; ..\mcal_src\Port.c	  1272         /* Start from the first pin */
; ..\mcal_src\Port.c	  1273         #if (PORT_SET_PIN_DIRECTION_API == STD_ON)
; ..\mcal_src\Port.c	  1274         PinPos = 0x01U;
; ..\mcal_src\Port.c	  1275         #endif /* PORT_SET_PIN_DIRECTION_API */
; ..\mcal_src\Port.c	  1276  
; ..\mcal_src\Port.c	  1277         LoopCtr = 0U;
; ..\mcal_src\Port.c	  1278         do
; ..\mcal_src\Port.c	  1279         {
; ..\mcal_src\Port.c	  1280          if(Port_lIsPinAvailable(PortNumber,LoopCtr) != 0U)
; ..\mcal_src\Port.c	  1281          {
; ..\mcal_src\Port.c	  1282            #if (PORT_SET_PIN_DIRECTION_API == STD_ON)
; ..\mcal_src\Port.c	  1283            /* Direction changeable is enabled */
; ..\mcal_src\Port.c	  1284            if ((DirectionData & PinPos) == PORT_PIN_DIR_NOT_CHANGEABLE)
; ..\mcal_src\Port.c	  1285            {
; ..\mcal_src\Port.c	  1286              /* Direction of the pin cannot be changed during run time */
; ..\mcal_src\Port.c	  1287              /* Write the default value to the control bit fields of the Pin */
; ..\mcal_src\Port.c	  1288              PORT_SFR_RUNTIME_USER_MODE_MODIFY32(*IocrRegPtr, \ 
; ..\mcal_src\Port.c	  1289                   PORT_PIN_IOCR0_CLEARMASK(LoopCtr),((uint32)(*IocrDataPtr) << \ 
; ..\mcal_src\Port.c	  1290                                           PORT_IOCR0_BIT_SHIFT_COUNT(LoopCtr)))
; ..\mcal_src\Port.c	  1291            }
; ..\mcal_src\Port.c	  1292            #else
; ..\mcal_src\Port.c	  1293              /* Direction changeable is disabled */
; ..\mcal_src\Port.c	  1294              /* Write the default value to the control bit fields of the Pin */
; ..\mcal_src\Port.c	  1295              PORT_SFR_RUNTIME_USER_MODE_MODIFY32(*IocrRegPtr, \ 
; ..\mcal_src\Port.c	  1296                   PORT_PIN_IOCR0_CLEARMASK(LoopCtr),((uint32)(*IocrDataPtr) << \ 
; ..\mcal_src\Port.c	  1297                                           PORT_IOCR0_BIT_SHIFT_COUNT(LoopCtr)))
; ..\mcal_src\Port.c	  1298            #endif /* PORT_SET_PIN_DIRECTION_API */
; ..\mcal_src\Port.c	  1299          }
; ..\mcal_src\Port.c	  1300  
; ..\mcal_src\Port.c	  1301          /* Loop for each pin in the port */
; ..\mcal_src\Port.c	  1302          LoopCtr++;
; ..\mcal_src\Port.c	  1303  
; ..\mcal_src\Port.c	  1304          if((uint32)(LoopCtr % PORT_NUM_FOUR) == 0U)
; ..\mcal_src\Port.c	  1305          {
; ..\mcal_src\Port.c	  1306            /* Increment to point to the next data set */
; ..\mcal_src\Port.c	  1307            /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1308            due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	  1309            IocrRegPtr++;
; ..\mcal_src\Port.c	  1310          }
; ..\mcal_src\Port.c	  1311          /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1312          due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	  1313          IocrDataPtr++;
; ..\mcal_src\Port.c	  1314  
; ..\mcal_src\Port.c	  1315          #if (PORT_SET_PIN_DIRECTION_API == STD_ON)
; ..\mcal_src\Port.c	  1316          /* Shift the pin position to next pin */
; ..\mcal_src\Port.c	  1317          PinPos = PinPos << 1U;
; ..\mcal_src\Port.c	  1318          #endif /* PORT_SET_PIN_DIRECTION_API */
; ..\mcal_src\Port.c	  1319  
; ..\mcal_src\Port.c	  1320         } while (LoopCtr <= PORT_PIN_MAX_NUMBER );
; ..\mcal_src\Port.c	  1321  
; ..\mcal_src\Port.c	  1322        ConfigIndex++;
; ..\mcal_src\Port.c	  1323       }
; ..\mcal_src\Port.c	  1324      PortNumber++;
; ..\mcal_src\Port.c	  1325     } while (PortNumber <= PORT_MAX_NUMBER); /* Loop for all the ports */
; ..\mcal_src\Port.c	  1326    }
; ..\mcal_src\Port.c	  1327  }/* Port_RefreshPortDirection */
; ..\mcal_src\Port.c	  1328  
; ..\mcal_src\Port.c	  1329  /* Enable / Disable the use of the function */
; ..\mcal_src\Port.c	  1330  #if (PORT_SET_PIN_MODE_API == STD_ON)
; ..\mcal_src\Port.c	  1331  /*******************************************************************************
; ..\mcal_src\Port.c	  1332  ** Traceability     : [cover parentID=DS_AS_PORT145,DS_AS_PORT125,
; ..\mcal_src\Port.c	  1333                          DS_AS_PORT128,DS_AS_PORT101_PORT211_5,
; ..\mcal_src\Port.c	  1334                          DS_AS_PORT107_PORT146_5,DS_AS4XX_PORT087_5,
; ..\mcal_src\Port.c	  1335                          DS_AS3XX_PORT087_5,
; ..\mcal_src\Port.c	  1336                          DS_AS4XX_PORT223,DS_AS_PORT213,DS_AS_PORT077,
; ..\mcal_src\Port.c	  1337                          DS_NAS_PORT_PR912_3,
; ..\mcal_src\Port.c	  1338                          SAS_MCAL_PORT_0415,
; ..\mcal_src\Port.c	  1339                          DS_MCAL_PORT_0417]                  **
; ..\mcal_src\Port.c	  1340  **                                                                            **
; ..\mcal_src\Port.c	  1341  ** Syntax           : void Port_SetPinMode                                    **
; ..\mcal_src\Port.c	  1342  **                    (                                                       **
; ..\mcal_src\Port.c	  1343  **                      Port_PinType Pin,                                     **
; ..\mcal_src\Port.c	  1344  **                      Port_PinModeType Mode                                 **
; ..\mcal_src\Port.c	  1345  **                    )                                                       **
; ..\mcal_src\Port.c	  1346  ** [/cover]                                                                   **
; ..\mcal_src\Port.c	  1347  **                                                                            **
; ..\mcal_src\Port.c	  1348  ** Service ID       : 0x04                                                    **
; ..\mcal_src\Port.c	  1349  **                                                                            **
; ..\mcal_src\Port.c	  1350  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1351  **                                                                            **
; ..\mcal_src\Port.c	  1352  ** Reentrancy       : Re-entrant                                              **
; ..\mcal_src\Port.c	  1353  **                    (Reentrant for pins of different port or pins assigned  **
; ..\mcal_src\Port.c	  1354  **                     to different IOCR register of a same port              **
; ..\mcal_src\Port.c	  1355  **                                                                            **
; ..\mcal_src\Port.c	  1356  ** Parameters (in)  : Port_PinType Pin - port pin ID whose mode has to be set **
; ..\mcal_src\Port.c	  1357  **                    Port_PinModeType Mode - port pin mode to be set         **
; ..\mcal_src\Port.c	  1358  **                                                                            **
; ..\mcal_src\Port.c	  1359  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	  1360  **                                                                            **
; ..\mcal_src\Port.c	  1361  ** Return value     : none                                                    **
; ..\mcal_src\Port.c	  1362  **                                                                            **
; ..\mcal_src\Port.c	  1363  ** Description      : This function:                                          **
; ..\mcal_src\Port.c	  1364  **   - PORT125: Sets the port pin mode during runtime.                        **
; ..\mcal_src\Port.c	  1365  **   - This function is available if the PORT_SET_PIN_MODE_API is set ON      **
; ..\mcal_src\Port.c	  1366  **                                                                            **
; ..\mcal_src\Port.c	  1367  *******************************************************************************/
; ..\mcal_src\Port.c	  1368  void Port_SetPinMode(Port_PinType Pin, Port_PinModeType Mode)
; ..\mcal_src\Port.c	  1369  {
; ..\mcal_src\Port.c	  1370    /* Each Port Number for the hardware unit  */
; ..\mcal_src\Port.c	  1371    uint32           PortNumber;
; ..\mcal_src\Port.c	  1372    uint32           PinNumber;
; ..\mcal_src\Port.c	  1373    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1374    uint32           ErrMode;
; ..\mcal_src\Port.c	  1375    uint8            ErrStatus;
; ..\mcal_src\Port.c	  1376    uint8            Direction;
; ..\mcal_src\Port.c	  1377    uint32           ConfigIndex;
; ..\mcal_src\Port.c	  1378    uint32           Index;
; ..\mcal_src\Port.c	  1379    const uint32     *DataPtr;
; ..\mcal_src\Port.c	  1380    #endif
; ..\mcal_src\Port.c	  1381    volatile uint32  *IocrRegPtr;
; ..\mcal_src\Port.c	  1382    Ifx_P            *PortAddressPtr;
; ..\mcal_src\Port.c	  1383  
; ..\mcal_src\Port.c	  1384    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1385    IocrRegPtr = NULL_PTR;
; ..\mcal_src\Port.c	  1386    ConfigIndex = 0U;
; ..\mcal_src\Port.c	  1387    #endif
; ..\mcal_src\Port.c	  1388  
; ..\mcal_src\Port.c	  1389    /* Extract the port number and pin number from the Pin Symbolic ID */
; ..\mcal_src\Port.c	  1390    PortNumber = (uint32)Port_lNumber(Pin);
; ..\mcal_src\Port.c	  1391    PinNumber  = (uint32)Port_lPinNumber(Pin);
; ..\mcal_src\Port.c	  1392  
; ..\mcal_src\Port.c	  1393    #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1394    if (Port_InitStatus != PORT_INITIALIZED)
; ..\mcal_src\Port.c	  1395    {
; ..\mcal_src\Port.c	  1396      /* Report PORT_E_UNINIT DET if Port initialisation is not done
; ..\mcal_src\Port.c	  1397      */
; ..\mcal_src\Port.c	  1398      Det_ReportError((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1399        PORT_SID_SETPINMODE,PORT_E_UNINIT);
; ..\mcal_src\Port.c	  1400  
; ..\mcal_src\Port.c	  1401      ErrStatus = 1U;
; ..\mcal_src\Port.c	  1402    }
; ..\mcal_src\Port.c	  1403    else
; ..\mcal_src\Port.c	  1404    #endif /* PORT_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Port.c	  1405    {
; ..\mcal_src\Port.c	  1406      #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1407      /* PORT077: Function parameters are checked in the order in which they are
; ..\mcal_src\Port.c	  1408                  passed.
; ..\mcal_src\Port.c	  1409      */
; ..\mcal_src\Port.c	  1410      /* PORT087: Check if Pin is valid */
; ..\mcal_src\Port.c	  1411  
; ..\mcal_src\Port.c	  1412      ErrStatus = Port_lChkPin(Pin,PortNumber,PinNumber);
; ..\mcal_src\Port.c	  1413      #endif
; ..\mcal_src\Port.c	  1414  
; ..\mcal_src\Port.c	  1415      #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1416      if (ErrStatus == 0U)
; ..\mcal_src\Port.c	  1417      #endif
; ..\mcal_src\Port.c	  1418      {
; ..\mcal_src\Port.c	  1419        /* Get Port Address */
; ..\mcal_src\Port.c	  1420        PortAddressPtr = Port_lAdr(PortNumber);
; ..\mcal_src\Port.c	  1421        /* Get the IOCR0 register address of particular port */
; ..\mcal_src\Port.c	  1422        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1423            to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1424        IocrRegPtr = ((volatile uint32*)\ 
; ..\mcal_src\Port.c	  1425                       (volatile void*)PortAddressPtr + PORT_IOCR0_REG_OFFSET);
; ..\mcal_src\Port.c	  1426  
; ..\mcal_src\Port.c	  1427        #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1428        for(Index = 0U; Index < PortNumber;Index++)
; ..\mcal_src\Port.c	  1429        {
; ..\mcal_src\Port.c	  1430          if(Port_lIsPortAvailable(Index) != 0U)
; ..\mcal_src\Port.c	  1431          {
; ..\mcal_src\Port.c	  1432            ConfigIndex++; /* to identify the Index of configuration*/
; ..\mcal_src\Port.c	  1433          }
; ..\mcal_src\Port.c	  1434        }
; ..\mcal_src\Port.c	  1435  
; ..\mcal_src\Port.c	  1436          /* Get the config data location for specified Port */
; ..\mcal_src\Port.c	  1437        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1438          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1439        DataPtr =(const uint32*)(const void*)
; ..\mcal_src\Port.c	  1440                 ((Port_kConfigPtr->PortConfigSetPtr)+ConfigIndex);
; ..\mcal_src\Port.c	  1441  
; ..\mcal_src\Port.c	  1442        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1443          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1444        if ((((*(DataPtr + PORT_DATA_OFS_MODE)) & ((uint32)0x01U << PinNumber))
; ..\mcal_src\Port.c	  1445              == PORT_PIN_MODE_NOT_CHANGEABLE)
; ..\mcal_src\Port.c	  1446           )
; ..\mcal_src\Port.c	  1447        {
; ..\mcal_src\Port.c	  1448          /*PORT223: Report PORT_E_MODE_UNCHANGEABLE DET if the pin is configured
; ..\mcal_src\Port.c	  1449           as mode not changeable */
; ..\mcal_src\Port.c	  1450          #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1451          Det_ReportError((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1452            PORT_SID_SETPINMODE,PORT_E_MODE_UNCHANGEABLE);
; ..\mcal_src\Port.c	  1453          #endif
; ..\mcal_src\Port.c	  1454  
; ..\mcal_src\Port.c	  1455          ErrStatus = 1U;
; ..\mcal_src\Port.c	  1456        }
; ..\mcal_src\Port.c	  1457        else
; ..\mcal_src\Port.c	  1458        {
; ..\mcal_src\Port.c	  1459  
; ..\mcal_src\Port.c	  1460          /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1461            to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1462          Direction = (uint8)((PORT_SFR_RUNTIME_USER_MODE_READ32(*(IocrRegPtr + \ 
; ..\mcal_src\Port.c	  1463             (PinNumber / PORT_NUM_FOUR))) & PORT_PIN_IOCR0_SETMASK(PinNumber)) \ 
; ..\mcal_src\Port.c	  1464                                       >> PORT_IOCR0_BIT_SHIFT_COUNT(PinNumber));
; ..\mcal_src\Port.c	  1465  
; ..\mcal_src\Port.c	  1466          ErrMode = Port_lModeErrChk(Direction, Mode, ConfigIndex, PinNumber);
; ..\mcal_src\Port.c	  1467  
; ..\mcal_src\Port.c	  1468          if (ErrMode == 1U)
; ..\mcal_src\Port.c	  1469          {
; ..\mcal_src\Port.c	  1470            #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1471            /* Report PORT_E_PARAM_INVALID_MODE DET if pin mode is not valid */
; ..\mcal_src\Port.c	  1472            Det_ReportError((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1473              PORT_SID_SETPINMODE,PORT_E_PARAM_INVALID_MODE);
; ..\mcal_src\Port.c	  1474            #endif
; ..\mcal_src\Port.c	  1475  
; ..\mcal_src\Port.c	  1476  
; ..\mcal_src\Port.c	  1477            /*Report Safety Error if the Mode is out of range */
; ..\mcal_src\Port.c	  1478            #if (PORT_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Port.c	  1479            SafeMcal_ReportError ((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1480               PORT_SID_SETPINMODE,PORT_E_PARAM_INVALID_MODE);
; ..\mcal_src\Port.c	  1481            #endif
; ..\mcal_src\Port.c	  1482  
; ..\mcal_src\Port.c	  1483            ErrStatus = 1U;
; ..\mcal_src\Port.c	  1484  
; ..\mcal_src\Port.c	  1485          }
; ..\mcal_src\Port.c	  1486        }
; ..\mcal_src\Port.c	  1487        #endif
; ..\mcal_src\Port.c	  1488      }
; ..\mcal_src\Port.c	  1489    }
; ..\mcal_src\Port.c	  1490  
; ..\mcal_src\Port.c	  1491    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1492    if (ErrStatus == 0U)
; ..\mcal_src\Port.c	  1493    #endif
; ..\mcal_src\Port.c	  1494    {
; ..\mcal_src\Port.c	  1495      /* Set the new mode in IOCR register */
; ..\mcal_src\Port.c	  1496      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1497        to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1498      PORT_SFR_RUNTIME_USER_MODE_MODIFY32((*(IocrRegPtr + \ 
; ..\mcal_src\Port.c	  1499             (PinNumber / PORT_NUM_FOUR))),PORT_IOCR0_MODE_CLEARMASK(PinNumber), \ 
; ..\mcal_src\Port.c	  1500                          ((uint32)Mode << PORT_IOCR0_BIT_SHIFT_COUNT(PinNumber)))
; ..\mcal_src\Port.c	  1501    }
; ..\mcal_src\Port.c	  1502  }/* Port_SetPinMode */
; ..\mcal_src\Port.c	  1503  
; ..\mcal_src\Port.c	  1504  #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1505  /*******************************************************************************
; ..\mcal_src\Port.c	  1506  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lModeErrChk                **
; ..\mcal_src\Port.c	  1507  **                    (                                                       **
; ..\mcal_src\Port.c	  1508  **                      uint8 Dir,                                            **
; ..\mcal_src\Port.c	  1509  **                      Port_PinModeType Mode,                                **
; ..\mcal_src\Port.c	  1510  **                      uint32 ConfigIndex,                                   **
; ..\mcal_src\Port.c	  1511  **                      uint32 PinNumber                                      **
; ..\mcal_src\Port.c	  1512  **                    )                                                       **
; ..\mcal_src\Port.c	  1513  **                                                                            **
; ..\mcal_src\Port.c	  1514  ** Service ID       : NA                                                      **
; ..\mcal_src\Port.c	  1515  **                                                                            **
; ..\mcal_src\Port.c	  1516  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1517  **                                                                            **
; ..\mcal_src\Port.c	  1518  ** Reentrancy       : Re-entrant                                              **
; ..\mcal_src\Port.c	  1519  **                                                                            **
; ..\mcal_src\Port.c	  1520  ** Parameters (in)  : uint8 Dir - Current direction of a pin                  **
; ..\mcal_src\Port.c	  1521  **                    Port_PinModeType Mode - port pin mode to be set         **
; ..\mcal_src\Port.c	  1522  **                    uint32 ConfigIndex - Array Index                        **
; ..\mcal_src\Port.c	  1523  **                    uint32 PinNumber - port pin number                      **
; ..\mcal_src\Port.c	  1524  **                                                                            **
; ..\mcal_src\Port.c	  1525  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	  1526  **                                                                            **
; ..\mcal_src\Port.c	  1527  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  1528  **                    0 -No error in the pin mode set                         **
; ..\mcal_src\Port.c	  1529  **                    1 -Error in the pin mode set                            **
; ..\mcal_src\Port.c	  1530  **                                                                            **
; ..\mcal_src\Port.c	  1531  ** Description      : This function:                                          **
; ..\mcal_src\Port.c	  1532  **   - Checks for the error.                                                  **
; ..\mcal_src\Port.c	  1533  **   - This function is available if the PORT_SET_PIN_MODE_API is set ON      **
; ..\mcal_src\Port.c	  1534  **                                                                            **
; ..\mcal_src\Port.c	  1535  *******************************************************************************/
; ..\mcal_src\Port.c	  1536  IFX_LOCAL_INLINE uint32 Port_lModeErrChk
; ..\mcal_src\Port.c	  1537  (
; ..\mcal_src\Port.c	  1538    uint8 Dir, Port_PinModeType Mode, uint32 ConfigIndex, uint32 PinNumber
; ..\mcal_src\Port.c	  1539  )
; ..\mcal_src\Port.c	  1540  {
; ..\mcal_src\Port.c	  1541    uint32 ErrStatus;
; ..\mcal_src\Port.c	  1542    uint32 Position;        /* Variables for determining if the specified*/
; ..\mcal_src\Port.c	  1543    uint32 Mode_supported;  /* mode is supported or not */  
; ..\mcal_src\Port.c	  1544  
; ..\mcal_src\Port.c	  1545    ErrStatus = 0U;
; ..\mcal_src\Port.c	  1546  
; ..\mcal_src\Port.c	  1547    /* parameter Pin is valid, check for the pin valid mode */
; ..\mcal_src\Port.c	  1548    if ((Dir & (uint8)PORT_DIR_MSK) == (uint8)PORT_PIN_IN)
; ..\mcal_src\Port.c	  1549    {
; ..\mcal_src\Port.c	  1550      if (Mode != (Port_PinModeType)PORT_PIN_MODE_GPIO)
; ..\mcal_src\Port.c	  1551      {
; ..\mcal_src\Port.c	  1552        ErrStatus = 1U;
; ..\mcal_src\Port.c	  1553      }
; ..\mcal_src\Port.c	  1554    }
; ..\mcal_src\Port.c	  1555    else if ( (Mode & AVAILABLE_MODE_MASK) != 0U)
; ..\mcal_src\Port.c	  1556    {
; ..\mcal_src\Port.c	  1557      ErrStatus = 1U;
; ..\mcal_src\Port.c	  1558    }
; ..\mcal_src\Port.c	  1559    else
; ..\mcal_src\Port.c	  1560    {
; ..\mcal_src\Port.c	  1561      Position = Mode >> PORT_IOCR_PC_POS;
; ..\mcal_src\Port.c	  1562      Mode_supported = (uint32)1U << Position;
; ..\mcal_src\Port.c	  1563  
; ..\mcal_src\Port.c	  1564      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1565        due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1566      if(((uint32)(Port_kConfigPtr->
; ..\mcal_src\Port.c	  1567                    PortPinHwSupportedModes[ConfigIndex].U[PinNumber]) &
; ..\mcal_src\Port.c	  1568            Mode_supported ) == 0U
; ..\mcal_src\Port.c	  1569        )
; ..\mcal_src\Port.c	  1570      {
; ..\mcal_src\Port.c	  1571        ErrStatus = 1U;
; ..\mcal_src\Port.c	  1572      }
; ..\mcal_src\Port.c	  1573    }
; ..\mcal_src\Port.c	  1574  
; ..\mcal_src\Port.c	  1575    return(ErrStatus);
; ..\mcal_src\Port.c	  1576  }
; ..\mcal_src\Port.c	  1577  /*******************************************************************************
; ..\mcal_src\Port.c	  1578  ** Syntax           : IFX_LOCAL_INLINE uint8 Port_lChkPin                     **
; ..\mcal_src\Port.c	  1579  **                    (                                                       **
; ..\mcal_src\Port.c	  1580  **                      Port_PinType Pin,                                     **
; ..\mcal_src\Port.c	  1581  **                      uint32 PortNo,                                        **
; ..\mcal_src\Port.c	  1582  **                      uint32 PinNo                                          **
; ..\mcal_src\Port.c	  1583  **                    )                                                       **
; ..\mcal_src\Port.c	  1584  **                                                                            **
; ..\mcal_src\Port.c	  1585  ** Service ID       : NA                                                      **
; ..\mcal_src\Port.c	  1586  **                                                                            **
; ..\mcal_src\Port.c	  1587  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1588  **                                                                            **
; ..\mcal_src\Port.c	  1589  ** Reentrancy       : Re-entrant                                              **
; ..\mcal_src\Port.c	  1590  **                                                                            **
; ..\mcal_src\Port.c	  1591  ** Parameters (in)  : Port_PinType pin - pin ID whose mode has to be set      **
; ..\mcal_src\Port.c	  1592  **                    uint32 PinNo - port pin number                          **
; ..\mcal_src\Port.c	  1593  **                    uint32 PortNo - port number                             **
; ..\mcal_src\Port.c	  1594  **                                                                            **
; ..\mcal_src\Port.c	  1595  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	  1596  **                                                                            **
; ..\mcal_src\Port.c	  1597  ** Return value     : uint8                                                   **
; ..\mcal_src\Port.c	  1598  **                    0 - No error if portpin is available                    **
; ..\mcal_src\Port.c	  1599  **                    1 - Error if portpin is not available                   **
; ..\mcal_src\Port.c	  1600  **                                                                            **
; ..\mcal_src\Port.c	  1601  ** Description      : This function:                                          **
; ..\mcal_src\Port.c	  1602  **   - Checks for the error.                                                  **
; ..\mcal_src\Port.c	  1603  **   - This function is available if the PORT_SET_PIN_MODE_API is set ON      **
; ..\mcal_src\Port.c	  1604  **                                                                            **
; ..\mcal_src\Port.c	  1605  *******************************************************************************/
; ..\mcal_src\Port.c	  1606  IFX_LOCAL_INLINE uint8 Port_lChkPin(Port_PinType Pin,uint32 PortNo,uint32 PinNo)
; ..\mcal_src\Port.c	  1607  {
; ..\mcal_src\Port.c	  1608    uint8 RetVal;
; ..\mcal_src\Port.c	  1609    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1610    uint32 PortReadOnly;
; ..\mcal_src\Port.c	  1611    uint32 PinAvailable;
; ..\mcal_src\Port.c	  1612    uint32 PortAvailable;
; ..\mcal_src\Port.c	  1613    #endif
; ..\mcal_src\Port.c	  1614  
; ..\mcal_src\Port.c	  1615    RetVal = 0U;
; ..\mcal_src\Port.c	  1616    #if ((PORT_DEV_ERROR_DETECT == STD_OFF) && (PORT_SAFETY_ENABLE == STD_OFF))
; ..\mcal_src\Port.c	  1617    UNUSED_PARAMETER(PortNo)
; ..\mcal_src\Port.c	  1618    UNUSED_PARAMETER(PinNo)
; ..\mcal_src\Port.c	  1619    #endif
; ..\mcal_src\Port.c	  1620  
; ..\mcal_src\Port.c	  1621    #if ((PORT_DEV_ERROR_DETECT == STD_ON) || (PORT_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Port.c	  1622  
; ..\mcal_src\Port.c	  1623    PortAvailable = Port_lIsPortAvailable(PortNo);
; ..\mcal_src\Port.c	  1624    PortReadOnly  = Port_lIsPortReadOnly(PortNo);
; ..\mcal_src\Port.c	  1625  
; ..\mcal_src\Port.c	  1626    if( PortAvailable != 0U )
; ..\mcal_src\Port.c	  1627    {
; ..\mcal_src\Port.c	  1628      PinAvailable  = Port_lIsPinAvailable(PortNo,PinNo);
; ..\mcal_src\Port.c	  1629    }
; ..\mcal_src\Port.c	  1630    else
; ..\mcal_src\Port.c	  1631    {
; ..\mcal_src\Port.c	  1632      /* If Port is not available, then Pin also Not available */
; ..\mcal_src\Port.c	  1633      PinAvailable = 0U;
; ..\mcal_src\Port.c	  1634    }
; ..\mcal_src\Port.c	  1635  
; ..\mcal_src\Port.c	  1636  
; ..\mcal_src\Port.c	  1637    if ( (Pin > PORT_MAX_PIN_ID) ||
; ..\mcal_src\Port.c	  1638          ((PortReadOnly != 0U) || (PinAvailable == 0U))
; ..\mcal_src\Port.c	  1639       )
; ..\mcal_src\Port.c	  1640    {
; ..\mcal_src\Port.c	  1641    #if (PORT_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Port.c	  1642      /*Report PORT_E_PARAM_PIN DET if the pin is out of range */
; ..\mcal_src\Port.c	  1643      Det_ReportError((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1644        PORT_SID_SETPINMODE,PORT_E_PARAM_PIN);
; ..\mcal_src\Port.c	  1645  
; ..\mcal_src\Port.c	  1646    #endif
; ..\mcal_src\Port.c	  1647  
; ..\mcal_src\Port.c	  1648    #if (PORT_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Port.c	  1649      /*Report Safety Error if the pin is out of range */
; ..\mcal_src\Port.c	  1650      SafeMcal_ReportError ((uint16)PORT_MODULE_ID,PORT_INSTANCE_ID,
; ..\mcal_src\Port.c	  1651          PORT_SID_SETPINMODE,PORT_E_PARAM_PIN);
; ..\mcal_src\Port.c	  1652  
; ..\mcal_src\Port.c	  1653    #endif
; ..\mcal_src\Port.c	  1654  
; ..\mcal_src\Port.c	  1655    RetVal = 1U;
; ..\mcal_src\Port.c	  1656    }
; ..\mcal_src\Port.c	  1657  
; ..\mcal_src\Port.c	  1658    #endif
; ..\mcal_src\Port.c	  1659  
; ..\mcal_src\Port.c	  1660   return(RetVal);
; ..\mcal_src\Port.c	  1661  
; ..\mcal_src\Port.c	  1662  }
; ..\mcal_src\Port.c	  1663  #endif /* (PORT_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Port.c	  1664  #endif /* (PORT_SET_PIN_MODE_API == STD_ON) */
; ..\mcal_src\Port.c	  1665  /*******************************************************************************
; ..\mcal_src\Port.c	  1666  ** Traceability     : [cover parentID=DS_AS_PORT043_PORT055,
; ..\mcal_src\Port.c	  1667                         DS_AS_PORT001_PORT002_PORT041,DS_AS_PORT214,
; ..\mcal_src\Port.c	  1668                         DS_AS_PORT042]                                         **
; ..\mcal_src\Port.c	  1669  **                                                                            **
; ..\mcal_src\Port.c	  1670  ** Syntax           : IFX_LOCAL_INLINE void Port_lIOInit(void)                **
; ..\mcal_src\Port.c	  1671  **                                                                            **
; ..\mcal_src\Port.c	  1672  ** [/cover]                                                                   **
; ..\mcal_src\Port.c	  1673  **                                                                            **
; ..\mcal_src\Port.c	  1674  ** Service ID       : NA                                                      **
; ..\mcal_src\Port.c	  1675  **                                                                            **
; ..\mcal_src\Port.c	  1676  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1677  **                                                                            **
; ..\mcal_src\Port.c	  1678  ** Reentrancy       : None Re-entrant                                         **
; ..\mcal_src\Port.c	  1679  **                                                                            **
; ..\mcal_src\Port.c	  1680  ** Parameters (in)  : none                                                    **
; ..\mcal_src\Port.c	  1681  **                                                                            **
; ..\mcal_src\Port.c	  1682  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	  1683  **                                                                            **
; ..\mcal_src\Port.c	  1684  ** Return value     : none                                                    **
; ..\mcal_src\Port.c	  1685  **                                                                            **
; ..\mcal_src\Port.c	  1686  ** Description      : This function:                                          **
; ..\mcal_src\Port.c	  1687  **   - INLINE function to initialize Port registers                           **
; ..\mcal_src\Port.c	  1688  **                                                                            **
; ..\mcal_src\Port.c	  1689  *******************************************************************************/
; ..\mcal_src\Port.c	  1690  IFX_LOCAL_INLINE void Port_lIOInit(void)
; ..\mcal_src\Port.c	  1691  {
; ..\mcal_src\Port.c	  1692    const uint32            *DataPtr;
; ..\mcal_src\Port.c	  1693    const Port_n_ConfigType *ConfigDataPtr;
; ..\mcal_src\Port.c	  1694    /* Each Port Number for the hardware unit */
; ..\mcal_src\Port.c	  1695    uint32                   PortNumber;
; ..\mcal_src\Port.c	  1696    /* Each Port level for the hardware unit */
; ..\mcal_src\Port.c	  1697    uint32                   PortLevel;
; ..\mcal_src\Port.c	  1698    /* Index to identify the port configuration information
; ..\mcal_src\Port.c	  1699    from the configuration array  */
; ..\mcal_src\Port.c	  1700    uint32                   ConfigIndex;
; ..\mcal_src\Port.c	  1701    Ifx_P                   *PortAddressPtr;
; ..\mcal_src\Port.c	  1702    const uint32            *PCSRDataPtr;
; ..\mcal_src\Port.c	  1703    volatile uint32         *PCSRRegPtr;
; ..\mcal_src\Port.c	  1704  
; ..\mcal_src\Port.c	  1705    /* Function call to initialize PDR registers */
; ..\mcal_src\Port.c	  1706    Port_lPDRInit();
; ..\mcal_src\Port.c	  1707  
; ..\mcal_src\Port.c	  1708    ConfigIndex = 0U;
; ..\mcal_src\Port.c	  1709  
; ..\mcal_src\Port.c	  1710  
; ..\mcal_src\Port.c	  1711    PCSRDataPtr = (const uint32*)(const void*)
; ..\mcal_src\Port.c	  1712                                 (Port_kConfigPtr->Port_PCSRConfigTypePtr);
; ..\mcal_src\Port.c	  1713  
; ..\mcal_src\Port.c	  1714    /* writing P_OUT and IOCR registers */
; ..\mcal_src\Port.c	  1715    for (PortNumber = 0U; PortNumber <= PORT_MAX_NUMBER ; PortNumber++)
; ..\mcal_src\Port.c	  1716    {
; ..\mcal_src\Port.c	  1717  
; ..\mcal_src\Port.c	  1718      if(Port_lIsPortAvailable(PortNumber) != 0U)
; ..\mcal_src\Port.c	  1719      {
; ..\mcal_src\Port.c	  1720        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1721          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1722        ConfigDataPtr = (Port_kConfigPtr->PortConfigSetPtr) + ConfigIndex ;
; ..\mcal_src\Port.c	  1723  
; ..\mcal_src\Port.c	  1724        /* Address of each port configuration */
; ..\mcal_src\Port.c	  1725        DataPtr = (const uint32 *)(const void*)(ConfigDataPtr);
; ..\mcal_src\Port.c	  1726  
; ..\mcal_src\Port.c	  1727        PortAddressPtr = Port_lAdr(PortNumber);
; ..\mcal_src\Port.c	  1728  
; ..\mcal_src\Port.c	  1729        /*
; ..\mcal_src\Port.c	  1730         PORT043: PORT055: OUT register is written before IOCR bit field to avoid
; ..\mcal_src\Port.c	  1731         the glitches on the pin
; ..\mcal_src\Port.c	  1732        */
; ..\mcal_src\Port.c	  1733        if(Port_lIsPortReadOnly(PortNumber) == 0U)
; ..\mcal_src\Port.c	  1734        {
; ..\mcal_src\Port.c	  1735          /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1736            to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1737          PortLevel = (*(DataPtr + PORT_DATA_OFS_LEVEL));
; ..\mcal_src\Port.c	  1738          PORT_SFR_INIT_USER_MODE_WRITE32(
; ..\mcal_src\Port.c	  1739                            PortAddressPtr->OMR.U, Port_lPinLevel(PortLevel));
; ..\mcal_src\Port.c	  1740        }
; ..\mcal_src\Port.c	  1741  
; ..\mcal_src\Port.c	  1742        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_0_3)!= 0U)
; ..\mcal_src\Port.c	  1743        {
; ..\mcal_src\Port.c	  1744          PORT_SFR_INIT_USER_MODE_WRITE32(PortAddressPtr->IOCR0.U, *DataPtr);
; ..\mcal_src\Port.c	  1745        }
; ..\mcal_src\Port.c	  1746        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1747            due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	  1748        DataPtr++;
; ..\mcal_src\Port.c	  1749  
; ..\mcal_src\Port.c	  1750        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_4_7) != 0U)
; ..\mcal_src\Port.c	  1751        {
; ..\mcal_src\Port.c	  1752          PORT_SFR_INIT_USER_MODE_WRITE32(PortAddressPtr->IOCR4.U, *DataPtr);
; ..\mcal_src\Port.c	  1753        }
; ..\mcal_src\Port.c	  1754        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1755            due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	  1756        DataPtr++;
; ..\mcal_src\Port.c	  1757  
; ..\mcal_src\Port.c	  1758        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_8_11)!= 0U)
; ..\mcal_src\Port.c	  1759        {
; ..\mcal_src\Port.c	  1760          PORT_SFR_INIT_USER_MODE_WRITE32(PortAddressPtr->IOCR8.U, *DataPtr);
; ..\mcal_src\Port.c	  1761        }
; ..\mcal_src\Port.c	  1762        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1763          due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	  1764        DataPtr++;
; ..\mcal_src\Port.c	  1765  
; ..\mcal_src\Port.c	  1766        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_12_15)!= 0U)
; ..\mcal_src\Port.c	  1767        {
; ..\mcal_src\Port.c	  1768          PORT_SFR_INIT_USER_MODE_WRITE32(PortAddressPtr->IOCR12.U, *DataPtr);
; ..\mcal_src\Port.c	  1769        }
; ..\mcal_src\Port.c	  1770  
; ..\mcal_src\Port.c	  1771  
; ..\mcal_src\Port.c	  1772        if(Port_lIsPortPCSRAvailable(PortNumber) != 0U)
; ..\mcal_src\Port.c	  1773        {
; ..\mcal_src\Port.c	  1774           /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1775            to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1776           PCSRRegPtr = ((volatile uint32*)(volatile void*)PortAddressPtr +
; ..\mcal_src\Port.c	  1777                                           PORT_PCSR_REG_OFFSET);
; ..\mcal_src\Port.c	  1778           PORT_SFR_INIT_RESETSAFETYENDINIT_TIMED(PORT_ENDINIT_TIMEOUT);
; ..\mcal_src\Port.c	  1779           /* PCSRn */
; ..\mcal_src\Port.c	  1780           PORT_SFR_INIT_WRITE32((*PCSRRegPtr),(*PCSRDataPtr));
; ..\mcal_src\Port.c	  1781           PORT_SFR_INIT_SETSAFETYENDINIT_TIMED();
; ..\mcal_src\Port.c	  1782  
; ..\mcal_src\Port.c	  1783           /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1784            due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Port.c	  1785           PCSRDataPtr++;
; ..\mcal_src\Port.c	  1786        }
; ..\mcal_src\Port.c	  1787  
; ..\mcal_src\Port.c	  1788        ConfigIndex++;
; ..\mcal_src\Port.c	  1789      }
; ..\mcal_src\Port.c	  1790    } /* For loop */
; ..\mcal_src\Port.c	  1791  
; ..\mcal_src\Port.c	  1792    /* Initialize PORT 40 Pad Disable control register */
; ..\mcal_src\Port.c	  1793    /* Reset endinit protect */
; ..\mcal_src\Port.c	  1794    PORT_SFR_INIT_RESETENDINIT();
; ..\mcal_src\Port.c	  1795  
; ..\mcal_src\Port.c	  1796    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1797      to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1798    PORT_SFR_INIT_WRITE32(P40_PDISC.U,Port_kConfigPtr->PDiscSet[0U]);
; ..\mcal_src\Port.c	  1799  
; ..\mcal_src\Port.c	  1800    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1801      to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1802    PORT_SFR_INIT_WRITE32(P41_PDISC.U,Port_kConfigPtr->PDiscSet[1U]);
; ..\mcal_src\Port.c	  1803  
; ..\mcal_src\Port.c	  1804    /* Set endinit protect */
; ..\mcal_src\Port.c	  1805    PORT_SFR_INIT_SETENDINIT();
; ..\mcal_src\Port.c	  1806  
; ..\mcal_src\Port.c	  1807  }
; ..\mcal_src\Port.c	  1808  /*******************************************************************************
; ..\mcal_src\Port.c	  1809  ** Syntax           : IFX_LOCAL_INLINE void Port_lPDRInit(void)               **
; ..\mcal_src\Port.c	  1810  **                                                                            **
; ..\mcal_src\Port.c	  1811  ** Service ID       : NA                                                      **
; ..\mcal_src\Port.c	  1812  **                                                                            **
; ..\mcal_src\Port.c	  1813  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1814  **                                                                            **
; ..\mcal_src\Port.c	  1815  ** Reentrancy       : None Re-entrant                                         **
; ..\mcal_src\Port.c	  1816  **                                                                            **
; ..\mcal_src\Port.c	  1817  ** Parameters (in)  : none                                                    **
; ..\mcal_src\Port.c	  1818  **                                                                            **
; ..\mcal_src\Port.c	  1819  ** Parameters (out) : none                                                    **
; ..\mcal_src\Port.c	  1820  **                                                                            **
; ..\mcal_src\Port.c	  1821  ** Return value     : none                                                    **
; ..\mcal_src\Port.c	  1822  **                                                                            **
; ..\mcal_src\Port.c	  1823  ** Description      : This function:                                          **
; ..\mcal_src\Port.c	  1824  **   - INLINE function to initialize Port PDR registers                       **
; ..\mcal_src\Port.c	  1825  **                                                                            **
; ..\mcal_src\Port.c	  1826  *******************************************************************************/
; ..\mcal_src\Port.c	  1827  IFX_LOCAL_INLINE void Port_lPDRInit(void)
; ..\mcal_src\Port.c	  1828  {
; ..\mcal_src\Port.c	  1829    /* Each Port Number for the hardware unit  */
; ..\mcal_src\Port.c	  1830    uint32                  PortNumber;
; ..\mcal_src\Port.c	  1831    /* Index to identify the port configuration information
; ..\mcal_src\Port.c	  1832    from the configuration array  */
; ..\mcal_src\Port.c	  1833    uint32                  ConfigIndex;
; ..\mcal_src\Port.c	  1834    uint32                  PortReadOnly;
; ..\mcal_src\Port.c	  1835    Ifx_P            *PortAddressPtr;
; ..\mcal_src\Port.c	  1836  
; ..\mcal_src\Port.c	  1837    ConfigIndex = 0U;
	mov	d8,#0
	lea	a15,[a15]@los(Port_kConfigPtr)
.L148:

; ..\mcal_src\Port.c	  1838    /* Reset endinit protect */
; ..\mcal_src\Port.c	  1839  
; ..\mcal_src\Port.c	  1840    /* Port driver strength is configured in Port PDR0 register */
; ..\mcal_src\Port.c	  1841    for (PortNumber = 0U; PortNumber < PORT_MAX_NUMBER ; PortNumber++)
	mov	d9,d8
	sub.a	a10,#8
.L146:

; ..\mcal_src\Port.c	  1842    {
; ..\mcal_src\Port.c	  1843      /*Check if the port is Analog port for Write operation*/
; ..\mcal_src\Port.c	  1844      PortReadOnly = Port_lIsPortReadOnly(PortNumber);
; ..\mcal_src\Port.c	  1845      /* Parameter Port is checked for validity */
; ..\mcal_src\Port.c	  1846      if ((Port_lIsPortAvailable(PortNumber) != 0U) && (PortReadOnly == 0U))
; ..\mcal_src\Port.c	  1847      {
; ..\mcal_src\Port.c	  1848        PORT_SFR_INIT_RESETENDINIT();
; ..\mcal_src\Port.c	  1849        PortAddressPtr = Port_lAdr(PortNumber);
; ..\mcal_src\Port.c	  1850        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1851          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1852        PORT_SFR_INIT_WRITE32((PortAddressPtr->PDR0.U),\ 
; ..\mcal_src\Port.c	  1853          Port_kConfigPtr->PortConfigSetPtr[ConfigIndex].DriverStrength0);
; ..\mcal_src\Port.c	  1854  
; ..\mcal_src\Port.c	  1855        if(Port_lIsPortPdr1Available(PortNumber) != 0U)
; ..\mcal_src\Port.c	  1856        {
; ..\mcal_src\Port.c	  1857          PortAddressPtr = Port_lAdr(PortNumber);
; ..\mcal_src\Port.c	  1858          /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Port.c	  1859          due to PBConfigStructure and is within allowed range*/
; ..\mcal_src\Port.c	  1860          PORT_SFR_INIT_WRITE32((PortAddressPtr->PDR1.U),\ 
; ..\mcal_src\Port.c	  1861                 Port_kConfigPtr->PortConfigSetPtr[ConfigIndex].DriverStrength1);
; ..\mcal_src\Port.c	  1862        }
; ..\mcal_src\Port.c	  1863        PORT_SFR_INIT_SETENDINIT();
; ..\mcal_src\Port.c	  1864        ConfigIndex++;
; ..\mcal_src\Port.c	  1865      }
; ..\mcal_src\Port.c	  1866    }/* For loop */
; ..\mcal_src\Port.c	  1867  }
; ..\mcal_src\Port.c	  1868  /*******************************************************************************
; ..\mcal_src\Port.c	  1869  ** Syntax           : IFX_LOCAL_INLINE Ifx_P * Port_lAdr                      **
; ..\mcal_src\Port.c	  1870  **                    (                                                       **
; ..\mcal_src\Port.c	  1871  **                      uint32 PortNumber                                     **
; ..\mcal_src\Port.c	  1872  **                    )                                                       **
; ..\mcal_src\Port.c	  1873  **                                                                            **
; ..\mcal_src\Port.c	  1874  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  1875  **                                                                            **
; ..\mcal_src\Port.c	  1876  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1877  **                                                                            **
; ..\mcal_src\Port.c	  1878  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  1879  **                                                                            **
; ..\mcal_src\Port.c	  1880  ** Parameters (in)  : uint32 PortNumber                                       **
; ..\mcal_src\Port.c	  1881  **                                                                            **
; ..\mcal_src\Port.c	  1882  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  1883  **                                                                            **
; ..\mcal_src\Port.c	  1884  ** Return value     : Ifx_P *RetVal -Return the port adresses.                **
; ..\mcal_src\Port.c	  1885  **                                                                            **
; ..\mcal_src\Port.c	  1886  ** Description      :                                                         **
; ..\mcal_src\Port.c	  1887  ** - The function like macro is to extract the Address of Px_OUT              **
; ..\mcal_src\Port.c	  1888  **   register.                                                                **
; ..\mcal_src\Port.c	  1889  *******************************************************************************/
; ..\mcal_src\Port.c	  1890  IFX_LOCAL_INLINE Ifx_P * Port_lAdr(uint32 PortNumber)
; ..\mcal_src\Port.c	  1891  {
; ..\mcal_src\Port.c	  1892    Ifx_P *RetVal;
; ..\mcal_src\Port.c	  1893  
; ..\mcal_src\Port.c	  1894    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Port.c	  1895      to efficiently access the SFRs of PORT*/
; ..\mcal_src\Port.c	  1896    RetVal = ( ((Ifx_P *)(void *)&P00_OUT +
; ..\mcal_src\Port.c	  1897                (((PortNumber / PORT_CONSTANT_10) * PORT_CONSTANT_16) +
; ..\mcal_src\Port.c	  1898                (PortNumber % PORT_CONSTANT_10)))
; ..\mcal_src\Port.c	  1899             );
; ..\mcal_src\Port.c	  1900    return(RetVal);
; ..\mcal_src\Port.c	  1901  }
; ..\mcal_src\Port.c	  1902  
; ..\mcal_src\Port.c	  1903  
; ..\mcal_src\Port.c	  1904  /*******************************************************************************
; ..\mcal_src\Port.c	  1905  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable31         **
; ..\mcal_src\Port.c	  1906  **                    (                                                       **
; ..\mcal_src\Port.c	  1907  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  1908  **                    )                                                       **
; ..\mcal_src\Port.c	  1909  **                                                                            **
; ..\mcal_src\Port.c	  1910  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  1911  **                                                                            **
; ..\mcal_src\Port.c	  1912  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1913  **                                                                            **
; ..\mcal_src\Port.c	  1914  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  1915  **                                                                            **
; ..\mcal_src\Port.c	  1916  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  1917  **                                                                            **
; ..\mcal_src\Port.c	  1918  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  1919  **                                                                            **
; ..\mcal_src\Port.c	  1920  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  1921  **                    RetVal - Value which denotes whether the port is        **
; ..\mcal_src\Port.c	  1922  **                    available or not                                        **
; ..\mcal_src\Port.c	  1923  **                                                                            **
; ..\mcal_src\Port.c	  1924  ** Description      :                                                         **
; ..\mcal_src\Port.c	  1925  ** - The function like macro is to check if the port is available or not      **
; ..\mcal_src\Port.c	  1926  **   for the microcontroller.                                                 **
; ..\mcal_src\Port.c	  1927  *******************************************************************************/
; ..\mcal_src\Port.c	  1928  IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable31(uint32 Port)
; ..\mcal_src\Port.c	  1929  {
; ..\mcal_src\Port.c	  1930    uint32 RetVal;
; ..\mcal_src\Port.c	  1931  
; ..\mcal_src\Port.c	  1932    RetVal = ( ((uint32)(PORT_CONSTANT_0x01) << (Port)) &
; ..\mcal_src\Port.c	  1933               ((uint32)PORTS_AVAILABLE_00_31)
; ..\mcal_src\Port.c	  1934             );
; ..\mcal_src\Port.c	  1935    return(RetVal);
; ..\mcal_src\Port.c	  1936  }
; ..\mcal_src\Port.c	  1937  
; ..\mcal_src\Port.c	  1938  
; ..\mcal_src\Port.c	  1939  /*******************************************************************************
; ..\mcal_src\Port.c	  1940  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable40         **
; ..\mcal_src\Port.c	  1941  **                    (                                                       **
; ..\mcal_src\Port.c	  1942  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  1943  **                    )                                                       **
; ..\mcal_src\Port.c	  1944  **                                                                            **
; ..\mcal_src\Port.c	  1945  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  1946  **                                                                            **
; ..\mcal_src\Port.c	  1947  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1948  **                                                                            **
; ..\mcal_src\Port.c	  1949  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  1950  **                                                                            **
; ..\mcal_src\Port.c	  1951  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  1952  **                                                                            **
; ..\mcal_src\Port.c	  1953  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  1954  **                                                                            **
; ..\mcal_src\Port.c	  1955  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  1956  **                    RetVal - Value which denotes whether the port is        **
; ..\mcal_src\Port.c	  1957  **                    available or not                                        **
; ..\mcal_src\Port.c	  1958  **                                                                            **
; ..\mcal_src\Port.c	  1959  ** Description      :                                                         **
; ..\mcal_src\Port.c	  1960  ** - The function like macro is to check if the port is available or not      **
; ..\mcal_src\Port.c	  1961  **   for the microcontroller.                                                 **
; ..\mcal_src\Port.c	  1962  *******************************************************************************/
; ..\mcal_src\Port.c	  1963  IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable40(uint32 Port)
; ..\mcal_src\Port.c	  1964  {
; ..\mcal_src\Port.c	  1965    uint32 RetVal;
; ..\mcal_src\Port.c	  1966  
; ..\mcal_src\Port.c	  1967    RetVal = ( ((uint32)(PORT_CONSTANT_0x01) << (Port - PORT_NUMBER_32)) &
; ..\mcal_src\Port.c	  1968               ((uint32)PORTS_AVAILABLE_32_63)
; ..\mcal_src\Port.c	  1969             );
; ..\mcal_src\Port.c	  1970    return(RetVal);
; ..\mcal_src\Port.c	  1971  }
; ..\mcal_src\Port.c	  1972  
; ..\mcal_src\Port.c	  1973  
; ..\mcal_src\Port.c	  1974  /*******************************************************************************
; ..\mcal_src\Port.c	  1975  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable           **
; ..\mcal_src\Port.c	  1976  **                    (                                                       **
; ..\mcal_src\Port.c	  1977  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  1978  **                    )                                                       **
; ..\mcal_src\Port.c	  1979  **                                                                            **
; ..\mcal_src\Port.c	  1980  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  1981  **                                                                            **
; ..\mcal_src\Port.c	  1982  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  1983  **                                                                            **
; ..\mcal_src\Port.c	  1984  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  1985  **                                                                            **
; ..\mcal_src\Port.c	  1986  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  1987  **                                                                            **
; ..\mcal_src\Port.c	  1988  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  1989  **                                                                            **
; ..\mcal_src\Port.c	  1990  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  1991  **                   RetVal - Value which denotes whether the port is         **
; ..\mcal_src\Port.c	  1992  **                   available or not.                                        **
; ..\mcal_src\Port.c	  1993  **                                                                            **
; ..\mcal_src\Port.c	  1994  ** Description      :                                                         **
; ..\mcal_src\Port.c	  1995  ** - The function like macro is to check if the port is read only or          **
; ..\mcal_src\Port.c	  1996  **   it is writable.                                                          **
; ..\mcal_src\Port.c	  1997  *******************************************************************************/
; ..\mcal_src\Port.c	  1998  IFX_LOCAL_INLINE uint32 Port_lIsPortAvailable(uint32 Port)
; ..\mcal_src\Port.c	  1999  {
; ..\mcal_src\Port.c	  2000    uint32 RetVal;
; ..\mcal_src\Port.c	  2001  
; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :
; ..\mcal_src\Port.c	  2003              (Port_lIsPortAvailable31(Port))
; ..\mcal_src\Port.c	  2004             );
; ..\mcal_src\Port.c	  2005    return(RetVal);
; ..\mcal_src\Port.c	  2006  }
; ..\mcal_src\Port.c	  2007  
; ..\mcal_src\Port.c	  2008  /*******************************************************************************
; ..\mcal_src\Port.c	  2009  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly31          **
; ..\mcal_src\Port.c	  2010  **                    (                                                       **
; ..\mcal_src\Port.c	  2011  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  2012  **                    )                                                       **
; ..\mcal_src\Port.c	  2013  **                                                                            **
; ..\mcal_src\Port.c	  2014  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2015  **                                                                            **
; ..\mcal_src\Port.c	  2016  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2017  **                                                                            **
; ..\mcal_src\Port.c	  2018  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2019  **                                                                            **
; ..\mcal_src\Port.c	  2020  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  2021  **                                                                            **
; ..\mcal_src\Port.c	  2022  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2023  **                                                                            **
; ..\mcal_src\Port.c	  2024  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  2025  **                    RetValue -Value which denotes whether the Port          **
; ..\mcal_src\Port.c	  2026  **                    is read only or not                                     **
; ..\mcal_src\Port.c	  2027  **                                                                            **
; ..\mcal_src\Port.c	  2028  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2029  ** - The function like macro is to check if the port is read only or          **
; ..\mcal_src\Port.c	  2030  **   it is writable.                                                          **
; ..\mcal_src\Port.c	  2031  *******************************************************************************/
; ..\mcal_src\Port.c	  2032  IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly31(uint32 Port)
; ..\mcal_src\Port.c	  2033  {
; ..\mcal_src\Port.c	  2034    uint32 RetVal;
; ..\mcal_src\Port.c	  2035  
; ..\mcal_src\Port.c	  2036    RetVal = ( ((uint32)(PORT_CONSTANT_0x01) << (Port)) &
; ..\mcal_src\Port.c	  2037               ((uint32)PORTS_READONLY_00_31)
; ..\mcal_src\Port.c	  2038              );
; ..\mcal_src\Port.c	  2039    return(RetVal);
; ..\mcal_src\Port.c	  2040  }
; ..\mcal_src\Port.c	  2041  
; ..\mcal_src\Port.c	  2042  /*******************************************************************************
; ..\mcal_src\Port.c	  2043  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly40          **
; ..\mcal_src\Port.c	  2044  **                    (                                                       **
; ..\mcal_src\Port.c	  2045  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  2046  **                    )                                                       **
; ..\mcal_src\Port.c	  2047  **                                                                            **
; ..\mcal_src\Port.c	  2048  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2049  **                                                                            **
; ..\mcal_src\Port.c	  2050  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2051  **                                                                            **
; ..\mcal_src\Port.c	  2052  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2053  **                                                                            **
; ..\mcal_src\Port.c	  2054  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  2055  **                                                                            **
; ..\mcal_src\Port.c	  2056  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2057  **                                                                            **
; ..\mcal_src\Port.c	  2058  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  2059  **                    RetValue -Value which denotes whether the Port          **
; ..\mcal_src\Port.c	  2060  **                    is read only or not                                     **
; ..\mcal_src\Port.c	  2061  **                                                                            **
; ..\mcal_src\Port.c	  2062  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2063  ** - The function like macro is to check if the port is read only or          **
; ..\mcal_src\Port.c	  2064  **   it is writable.                                                          **
; ..\mcal_src\Port.c	  2065  *******************************************************************************/
; ..\mcal_src\Port.c	  2066  IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly40(uint32 Port)
; ..\mcal_src\Port.c	  2067  {
; ..\mcal_src\Port.c	  2068    uint32 RetVal;
; ..\mcal_src\Port.c	  2069  
; ..\mcal_src\Port.c	  2070    RetVal = ( ((uint32)(PORT_CONSTANT_0x01) << (Port - PORT_NUMBER_32)) &
; ..\mcal_src\Port.c	  2071               ((uint32)PORTS_READONLY_32_63)
; ..\mcal_src\Port.c	  2072              );
; ..\mcal_src\Port.c	  2073    return(RetVal);
; ..\mcal_src\Port.c	  2074  }
; ..\mcal_src\Port.c	  2075  
; ..\mcal_src\Port.c	  2076  /*******************************************************************************
; ..\mcal_src\Port.c	  2077  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly            **
; ..\mcal_src\Port.c	  2078  **                    (                                                       **
; ..\mcal_src\Port.c	  2079  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  2080  **                    )                                                       **
; ..\mcal_src\Port.c	  2081  **                                                                            **
; ..\mcal_src\Port.c	  2082  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2083  **                                                                            **
; ..\mcal_src\Port.c	  2084  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2085  **                                                                            **
; ..\mcal_src\Port.c	  2086  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2087  **                                                                            **
; ..\mcal_src\Port.c	  2088  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  2089  **                                                                            **
; ..\mcal_src\Port.c	  2090  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2091  **                                                                            **
; ..\mcal_src\Port.c	  2092  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  2093  **                    RetValue -Value which denotes whether the Port          **
; ..\mcal_src\Port.c	  2094  **                    is read only or not                                     **
; ..\mcal_src\Port.c	  2095  **                                                                            **
; ..\mcal_src\Port.c	  2096  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2097  ** - The function like macro is to check if the port is read only or          **
; ..\mcal_src\Port.c	  2098  **   it is writable.                                                          **
; ..\mcal_src\Port.c	  2099  *******************************************************************************/
; ..\mcal_src\Port.c	  2100  IFX_LOCAL_INLINE uint32 Port_lIsPortReadOnly(uint32 Port)
; ..\mcal_src\Port.c	  2101  {
; ..\mcal_src\Port.c	  2102    uint32 RetVal;
; ..\mcal_src\Port.c	  2103  
; ..\mcal_src\Port.c	  2104    RetVal = ((Port <= PORT_NUMBER_31) ? (Port_lIsPortReadOnly31(Port)) :
; ..\mcal_src\Port.c	  2105                                           (Port_lIsPortReadOnly40(Port))
; ..\mcal_src\Port.c	  2106              );
; ..\mcal_src\Port.c	  2107    return(RetVal);
; ..\mcal_src\Port.c	  2108  }
; ..\mcal_src\Port.c	  2109  
; ..\mcal_src\Port.c	  2110  /*******************************************************************************
; ..\mcal_src\Port.c	  2111  ** Syntax           : IFX_LOCAL_INLINE uint16 Port_lIsPinAvailable            **
; ..\mcal_src\Port.c	  2112  **                    (                                                       **
; ..\mcal_src\Port.c	  2113  **                      uint32 Port,uint32 Pin                                **
; ..\mcal_src\Port.c	  2114  **                    )                                                       **
; ..\mcal_src\Port.c	  2115  **                                                                            **
; ..\mcal_src\Port.c	  2116  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2117  **                                                                            **
; ..\mcal_src\Port.c	  2118  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2119  **                                                                            **
; ..\mcal_src\Port.c	  2120  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2121  **                                                                            **
; ..\mcal_src\Port.c	  2122  ** Parameters (in)  : uint32 Port,uint32 Pin - Port and Pin to be checked     **
; ..\mcal_src\Port.c	  2123  **                                                                            **
; ..\mcal_src\Port.c	  2124  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2125  **                                                                            **
; ..\mcal_src\Port.c	  2126  ** Return value     : uint16                                                  **
; ..\mcal_src\Port.c	  2127  **                    RetVal -Value which denotes whether the Pin is present  **
; ..\mcal_src\Port.c	  2128  **                    on the Port or not.                                     **
; ..\mcal_src\Port.c	  2129  **                                                                            **
; ..\mcal_src\Port.c	  2130  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2131  ** - The function like macro is to check if the port pin is available or not  **
; ..\mcal_src\Port.c	  2132  **   for the microcontroller.                                                 **
; ..\mcal_src\Port.c	  2133  *******************************************************************************/
; ..\mcal_src\Port.c	  2134  IFX_LOCAL_INLINE uint16 Port_lIsPinAvailable(uint32 Port,uint32 Pin)
; ..\mcal_src\Port.c	  2135  {
; ..\mcal_src\Port.c	  2136    uint16 RetVal;
; ..\mcal_src\Port.c	  2137  
; ..\mcal_src\Port.c	  2138    RetVal = (uint16)( ((uint32)PORT_CONSTANT_0x01 << (Pin)) &
; ..\mcal_src\Port.c	  2139                        (Port_kAvailablePins[(Port)])
; ..\mcal_src\Port.c	  2140                     );
; ..\mcal_src\Port.c	  2141    return(RetVal);
; ..\mcal_src\Port.c	  2142  }
; ..\mcal_src\Port.c	  2143  
; ..\mcal_src\Port.c	  2144  /*******************************************************************************
; ..\mcal_src\Port.c	  2145  ** Syntax           : IFX_LOCAL_INLINE uint16 Port_lIsPortPdr1Available       **
; ..\mcal_src\Port.c	  2146  **                    (                                                       **
; ..\mcal_src\Port.c	  2147  **                     uint32 Port                                            **
; ..\mcal_src\Port.c	  2148  **                    )                                                       **
; ..\mcal_src\Port.c	  2149  **                                                                            **
; ..\mcal_src\Port.c	  2150  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2151  **                                                                            **
; ..\mcal_src\Port.c	  2152  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2153  **                                                                            **
; ..\mcal_src\Port.c	  2154  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2155  **                                                                            **
; ..\mcal_src\Port.c	  2156  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  2157  **                                                                            **
; ..\mcal_src\Port.c	  2158  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2159  **                                                                            **
; ..\mcal_src\Port.c	  2160  ** Return value     : uint16                                                  **
; ..\mcal_src\Port.c	  2161  **                    RetValue -Value which denotes whether PDR1 register     **
; ..\mcal_src\Port.c	  2162  **                    present for the port or not                             **
; ..\mcal_src\Port.c	  2163  **                                                                            **
; ..\mcal_src\Port.c	  2164  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2165  ** - The function like macro is to check if any of the port pin 8 - 15        **
; ..\mcal_src\Port.c	  2166  **   is available or not for the microcontroller.                             **
; ..\mcal_src\Port.c	  2167  *******************************************************************************/
; ..\mcal_src\Port.c	  2168  IFX_LOCAL_INLINE uint16 Port_lIsPortPdr1Available(uint32 Port)
; ..\mcal_src\Port.c	  2169  {
; ..\mcal_src\Port.c	  2170    uint16 RetVal;
; ..\mcal_src\Port.c	  2171  
; ..\mcal_src\Port.c	  2172    RetVal = (uint16)(((uint32)(PORT_PDR1_MASK)) & (Port_kAvailablePins[(Port)]));
	movh.a	a12,#@his(Port_kAvailablePins)
.L72:
	st.a	[a10],a15
.L75:
	lea	a12,[a12]@los(Port_kAvailablePins)
.L76:
	st.a	[a15],a4
.L77:
	mov.aa	a13,a12
.L168:

; ..\mcal_src\Port.c	  1841    for (PortNumber = 0U; PortNumber < PORT_MAX_NUMBER ; PortNumber++)      (inlined)
	lea	a14,40
.L2:
	mov	d15,#31
.L169:

; ..\mcal_src\Port.c	  2104    RetVal = ((Port <= PORT_NUMBER_31) ? (Port_lIsPortReadOnly31(Port)) :      (inlined)
	jlt.u	d15,d9,.L3
.L170:
	mov	d0,#0

; ..\mcal_src\Port.c	  2104    RetVal = ((Port <= PORT_NUMBER_31) ? (Port_lIsPortReadOnly31(Port)) :      (inlined)
	j	.L4
.L3:
	mov	d0,#1
.L171:
	add	d15,d9,#-32
.L172:
	sh	d0,d0,d15
.L173:
	mov	d15,#768
.L174:
	and	d0,d15

; ..\mcal_src\Port.c	  2104    RetVal = ((Port <= PORT_NUMBER_31) ? (Port_lIsPortReadOnly31(Port)) :      (inlined)
.L4:
	mov	d15,#31
.L175:

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
	jge.u	d15,d9,.L5
.L176:
	mov	d15,#1
.L177:
	add	d1,d9,#-32
.L178:
	sh	d15,d15,d1
.L179:
	mov	d1,#774
.L180:
	and	d15,d1

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
	j	.L6
.L5:
	mov	d15,#1
.L181:
	sh	d1,d15,d9
	fcall	.cocofun_2
.L182:
	and	d15,d1

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
.L6:

; ..\mcal_src\Port.c	  1846      if ((Port_lIsPortAvailable(PortNumber) != 0U) && (PortReadOnly == 0U))      (inlined)
	jeq	d15,#0,.L7
.L97:
	jne	d0,#0,.L8
.L98:
	call	Mcal_ResetENDINIT
.L104:
	fcall	.cocofun_1
.L183:
	addsc.a	a2,a15,d15,#0
.L105:
	ld.a	a15,[a10]
	mul	d15,d8,#28
.L184:
	mov.u	d1,#65280
.L185:
	ld.a	a4,[a15]
	ld.a	a15,[a4]
	addsc.a	a15,a15,d15,#0
	ld.w	d0,[a15]20
	st.w	[a2]64,d0
.L186:
	ld.hu	d0,[a13]0
.L187:
	and	d0,d1
.L188:

; ..\mcal_src\Port.c	  1855        if(Port_lIsPortPdr1Available(PortNumber) != 0U)      (inlined)
	jeq	d0,#0,.L9
.L189:
	ld.a	a15,[a4]
	addsc.a	a15,a15,d15,#0
	ld.w	d15,[a15]24
	st.w	[a2]68,d15

; ..\mcal_src\Port.c	  1855        if(Port_lIsPortPdr1Available(PortNumber) != 0U)      (inlined)
.L9:
	call	Mcal_SetENDINIT
.L190:
	add	d8,#1

; ..\mcal_src\Port.c	  1846      if ((Port_lIsPortAvailable(PortNumber) != 0U) && (PortReadOnly == 0U))      (inlined)
.L8:
.L7:
	add	d9,#1
	add.a	a13,#2

; ..\mcal_src\Port.c	  1841    for (PortNumber = 0U; PortNumber < PORT_MAX_NUMBER ; PortNumber++)      (inlined)
	loop	a14,.L2
.L89:
	ld.a	a15,[a10]
.L191:
	mov	d8,#0

; ..\mcal_src\Port.c	  1715    for (PortNumber = 0U; PortNumber <= PORT_MAX_NUMBER ; PortNumber++)      (inlined)
	lea	a14,41
.L149:
	mov	d9,d8
.L150:
	ld.a	a15,[a15]
.L192:
	ld.a	a13,[a15]8

; ..\mcal_src\Port.c	  1715    for (PortNumber = 0U; PortNumber <= PORT_MAX_NUMBER ; PortNumber++)      (inlined)
.L10:
	mov	d15,#31
.L193:

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
	jge.u	d15,d9,.L11
.L194:
	mov	d15,#1
.L195:
	add	d0,d9,#-32
.L196:
	sh	d15,d15,d0
.L197:
	mov	d0,#774
.L198:
	and	d15,d0

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
	j	.L12
.L11:
	mov	d15,#1
.L199:
	sh	d0,d15,d9
	fcall	.cocofun_2
.L151:
	and	d15,d0

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
.L12:

; ..\mcal_src\Port.c	  1718      if(Port_lIsPortAvailable(PortNumber) != 0U)      (inlined)
	jeq	d15,#0,.L13
.L109:
	ld.a	a15,[a10]
.L200:
	mul	d15,d8,#28
.L201:
	ld.a	a15,[a15]
.L202:
	ld.a	a15,[a15]
.L203:
	addsc.a	a2,a15,d15,#0
.L111:
	fcall	.cocofun_1
.L152:
	addsc.a	a15,a15,d15,#0
.L112:
	mov	d15,#31
.L204:

; ..\mcal_src\Port.c	  2104    RetVal = ((Port <= PORT_NUMBER_31) ? (Port_lIsPortReadOnly31(Port)) :      (inlined)
	jlt.u	d15,d9,.L14
.L205:
	mov	d15,#0

; ..\mcal_src\Port.c	  2104    RetVal = ((Port <= PORT_NUMBER_31) ? (Port_lIsPortReadOnly31(Port)) :      (inlined)
	j	.L15
.L14:
	mov	d15,#1
.L206:
	add	d0,d9,#-32
.L207:
	sh	d0,d15,d0
.L208:
	mov	d15,#768
.L209:
	and	d15,d0

; ..\mcal_src\Port.c	  2104    RetVal = ((Port <= PORT_NUMBER_31) ? (Port_lIsPortReadOnly31(Port)) :      (inlined)
.L15:

; ..\mcal_src\Port.c	  1733        if(Port_lIsPortReadOnly(PortNumber) == 0U)      (inlined)
	jne	d15,#0,.L16
.L114:
	ld.w	d15,[a2]16
.L155:

; ..\mcal_src\Port.c	  2173    return(RetVal);
; ..\mcal_src\Port.c	  2174  }
; ..\mcal_src\Port.c	  2175  
; ..\mcal_src\Port.c	  2176  /*******************************************************************************
; ..\mcal_src\Port.c	  2177  ** Syntax           : IFX_LOCAL_INLINE uint16 Port_lIsPortIocrAvailable       **
; ..\mcal_src\Port.c	  2178  **                    (                                                       **
; ..\mcal_src\Port.c	  2179  **                      uint32 Port,uint16 Pin                                **
; ..\mcal_src\Port.c	  2180  **                    )                                                       **
; ..\mcal_src\Port.c	  2181  **                                                                            **
; ..\mcal_src\Port.c	  2182  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2183  **                                                                            **
; ..\mcal_src\Port.c	  2184  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2185  **                                                                            **
; ..\mcal_src\Port.c	  2186  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2187  **                                                                            **
; ..\mcal_src\Port.c	  2188  ** Parameters (in)  : uint32 Port,uint16 Pin - Port and Pin to be checked     **
; ..\mcal_src\Port.c	  2189  **                                                                            **
; ..\mcal_src\Port.c	  2190  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2191  **                                                                            **
; ..\mcal_src\Port.c	  2192  ** Return value     : uint16                                                  **
; ..\mcal_src\Port.c	  2193  **                    RetValue -Value which denotes whether Iocr register     **
; ..\mcal_src\Port.c	  2194  **                    is available or not for the microcontroller             **
; ..\mcal_src\Port.c	  2195  **                                                                            **
; ..\mcal_src\Port.c	  2196  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2197  ** - The function like macro is to check if the IOCRx register for the port   **
; ..\mcal_src\Port.c	  2198  **   is available or not for the microcontroller.                             **
; ..\mcal_src\Port.c	  2199  *******************************************************************************/
; ..\mcal_src\Port.c	  2200  IFX_LOCAL_INLINE uint16 Port_lIsPortIocrAvailable(uint32 Port,uint16 Pin)
; ..\mcal_src\Port.c	  2201  {
; ..\mcal_src\Port.c	  2202    uint16 RetVal;
; ..\mcal_src\Port.c	  2203  
; ..\mcal_src\Port.c	  2204    RetVal = (uint16)( ((uint32)(Pin)) & (Port_kAvailablePins[(Port)]) );
; ..\mcal_src\Port.c	  2205    return(RetVal);
; ..\mcal_src\Port.c	  2206  }
; ..\mcal_src\Port.c	  2207  
; ..\mcal_src\Port.c	  2208  #if((PORT_SET_PIN_DIRECTION_API == STD_ON) || (PORT_SET_PIN_MODE_API == STD_ON))
; ..\mcal_src\Port.c	  2209  /*******************************************************************************
; ..\mcal_src\Port.c	  2210  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lNumber                    **
; ..\mcal_src\Port.c	  2211  **                    (                                                       **
; ..\mcal_src\Port.c	  2212  **                      Port_PinType pin                                      **
; ..\mcal_src\Port.c	  2213  **                    )                                                       **
; ..\mcal_src\Port.c	  2214  **                                                                            **
; ..\mcal_src\Port.c	  2215  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2216  **                                                                            **
; ..\mcal_src\Port.c	  2217  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2218  **                                                                            **
; ..\mcal_src\Port.c	  2219  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2220  **                                                                            **
; ..\mcal_src\Port.c	  2221  ** Parameters (in)  : Port_PinType Pin                                        **
; ..\mcal_src\Port.c	  2222  **                                                                            **
; ..\mcal_src\Port.c	  2223  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2224  **                                                                            **
; ..\mcal_src\Port.c	  2225  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  2226  **                    RetValue -Value which denotes port number extracted     **
; ..\mcal_src\Port.c	  2227  **                    from pin symbolic ID                                    **
; ..\mcal_src\Port.c	  2228  **                                                                            **
; ..\mcal_src\Port.c	  2229  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2230  ** - Defines to extract port number Port_PinType data, bit4 to bit 11 (8 bit) **
; ..\mcal_src\Port.c	  2231  **   represents the Port Number, to get that value Right shifted              **
; ..\mcal_src\Port.c	  2232  **   by 4 and Mask with 0xFF                                                  **
; ..\mcal_src\Port.c	  2233  *******************************************************************************/
; ..\mcal_src\Port.c	  2234  IFX_LOCAL_INLINE uint32 Port_lNumber(Port_PinType Pin)
; ..\mcal_src\Port.c	  2235  {
; ..\mcal_src\Port.c	  2236     uint32 RetVal;
; ..\mcal_src\Port.c	  2237  
; ..\mcal_src\Port.c	  2238     RetVal = (((Pin) >> PORT_NUMBER_OFFSET) & PORT_NUM_LOW8_MASK);
; ..\mcal_src\Port.c	  2239     return(RetVal);
; ..\mcal_src\Port.c	  2240  }
; ..\mcal_src\Port.c	  2241  
; ..\mcal_src\Port.c	  2242  /*******************************************************************************
; ..\mcal_src\Port.c	  2243  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lPinNumber                 **
; ..\mcal_src\Port.c	  2244  **                    (                                                       **
; ..\mcal_src\Port.c	  2245  **                      Port_PinType pin                                      **
; ..\mcal_src\Port.c	  2246  **                    )                                                       **
; ..\mcal_src\Port.c	  2247  **                                                                            **
; ..\mcal_src\Port.c	  2248  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2249  **                                                                            **
; ..\mcal_src\Port.c	  2250  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2251  **                                                                            **
; ..\mcal_src\Port.c	  2252  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2253  **                                                                            **
; ..\mcal_src\Port.c	  2254  ** Parameters (in)  : Port_PinType Pin                                        **
; ..\mcal_src\Port.c	  2255  **                                                                            **
; ..\mcal_src\Port.c	  2256  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2257  **                                                                            **
; ..\mcal_src\Port.c	  2258  * Return value     : uint32                                                   **
; ..\mcal_src\Port.c	  2259  **                   RetValue -Value which denotes pin number extracted       **
; ..\mcal_src\Port.c	  2260  **                   from pin symbolic ID                                     **
; ..\mcal_src\Port.c	  2261  **                                                                            **
; ..\mcal_src\Port.c	  2262  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2263  **  -Defines to extract pin number from Port_PinType data                     **
; ..\mcal_src\Port.c	  2264  **   least significant 4-bit represents the Pin Number, to get that           **
; ..\mcal_src\Port.c	  2265  **   value Masked with 0x0F                                                   **
; ..\mcal_src\Port.c	  2266  *******************************************************************************/
; ..\mcal_src\Port.c	  2267  IFX_LOCAL_INLINE uint32 Port_lPinNumber(Port_PinType Pin)
; ..\mcal_src\Port.c	  2268  {
; ..\mcal_src\Port.c	  2269     uint32 RetVal;
; ..\mcal_src\Port.c	  2270  
; ..\mcal_src\Port.c	  2271     RetVal = ((Pin) & PORT_PIN_LOW4_MASK);
; ..\mcal_src\Port.c	  2272  
; ..\mcal_src\Port.c	  2273     return(RetVal);
; ..\mcal_src\Port.c	  2274  }
; ..\mcal_src\Port.c	  2275  #endif
; ..\mcal_src\Port.c	  2276  
; ..\mcal_src\Port.c	  2277  /*******************************************************************************
; ..\mcal_src\Port.c	  2278  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lPinLevel                  **
; ..\mcal_src\Port.c	  2279  **                    (                                                       **
; ..\mcal_src\Port.c	  2280  **                      uint32 level                                          **
; ..\mcal_src\Port.c	  2281  **                    )                                                       **
; ..\mcal_src\Port.c	  2282  **                                                                            **
; ..\mcal_src\Port.c	  2283  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2284  **                                                                            **
; ..\mcal_src\Port.c	  2285  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2286  **                                                                            **
; ..\mcal_src\Port.c	  2287  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2288  **                                                                            **
; ..\mcal_src\Port.c	  2289  ** Parameters (in)  : uint32 Level                                            **
; ..\mcal_src\Port.c	  2290  **                                                                            **
; ..\mcal_src\Port.c	  2291  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2292  **                                                                            **
; ..\mcal_src\Port.c	  2293  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  2294  **                    RetVal - Value to be set for the OMR register           **
; ..\mcal_src\Port.c	  2295  **                                                                            **
; ..\mcal_src\Port.c	  2296  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2297  ** -OMR register value to set the configured initial level                    **
; ..\mcal_src\Port.c	  2298  **  To get the port pin level Masked with 0x0000FFFFU and left shited by 16   **
; ..\mcal_src\Port.c	  2299  *******************************************************************************/
; ..\mcal_src\Port.c	  2300  IFX_LOCAL_INLINE uint32 Port_lPinLevel(uint32 Level)
; ..\mcal_src\Port.c	  2301  {
; ..\mcal_src\Port.c	  2302    uint32 RetVal;
; ..\mcal_src\Port.c	  2303  
; ..\mcal_src\Port.c	  2304    RetVal = ((((~(Level)) & PORT_LOWER_16BIT_MASK) << PORT_16BIT_SHIFTVALUE) |
	mov	d0,#-1
	xor	d0,d15
.L210:
	insert	d0,d0,#0,#16,#16
.L211:
	sh	d0,d0,#16
.L212:
	or	d0,d15
	st.w	[a15]4,d0

; ..\mcal_src\Port.c	  1733        if(Port_lIsPortReadOnly(PortNumber) == 0U)      (inlined)
.L16:
	ld.hu	d0,[a12]0
.L213:
	and	d15,d0,#15
.L116:

; ..\mcal_src\Port.c	  1742        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_0_3)!= 0U)      (inlined)
	jeq	d15,#0,.L17
.L214:
	ld.w	d15,[a2]
	st.w	[a15]16,d15

; ..\mcal_src\Port.c	  1742        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_0_3)!= 0U)      (inlined)
.L17:
	and	d15,d0,#240
	add.a	a2,#4
.L122:

; ..\mcal_src\Port.c	  1750        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_4_7) != 0U)      (inlined)
	jeq	d15,#0,.L18
.L215:
	ld.w	d15,[a2]
	st.w	[a15]20,d15

; ..\mcal_src\Port.c	  1750        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_4_7) != 0U)      (inlined)
.L18:
	mov	d15,#3840
	add.a	a2,#4
.L216:
	and	d15,d0
.L123:

; ..\mcal_src\Port.c	  1758        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_8_11)!= 0U)      (inlined)
	jeq	d15,#0,.L19
.L217:
	ld.w	d15,[a2]
	st.w	[a15]24,d15

; ..\mcal_src\Port.c	  1758        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_8_11)!= 0U)      (inlined)
.L19:
	mov.u	d15,#61440
	add.a	a2,#4
.L218:
	and	d0,d15
.L124:

; ..\mcal_src\Port.c	  1766        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_12_15)!= 0U)      (inlined)
	jeq	d0,#0,.L20
.L219:
	ld.w	d15,[a2]
	st.w	[a15]28,d15

; ..\mcal_src\Port.c	  1766        if(Port_lIsPortIocrAvailable(PortNumber,(uint16)PORT_PIN_12_15)!= 0U)      (inlined)
.L20:

; ..\mcal_src\Port.c	  2305                (Level)
; ..\mcal_src\Port.c	  2306             );
; ..\mcal_src\Port.c	  2307  
; ..\mcal_src\Port.c	  2308    return(RetVal);
; ..\mcal_src\Port.c	  2309  }
; ..\mcal_src\Port.c	  2310  
; ..\mcal_src\Port.c	  2311  
; ..\mcal_src\Port.c	  2312  /*******************************************************************************
; ..\mcal_src\Port.c	  2313  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable31     **
; ..\mcal_src\Port.c	  2314  **                    (                                                       **
; ..\mcal_src\Port.c	  2315  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  2316  **                    )                                                       **
; ..\mcal_src\Port.c	  2317  **                                                                            **
; ..\mcal_src\Port.c	  2318  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2319  **                                                                            **
; ..\mcal_src\Port.c	  2320  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2321  **                                                                            **
; ..\mcal_src\Port.c	  2322  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2323  **                                                                            **
; ..\mcal_src\Port.c	  2324  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  2325  **                                                                            **
; ..\mcal_src\Port.c	  2326  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2327  **                                                                            **
; ..\mcal_src\Port.c	  2328  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  2329  **                    RetValue -Value which denotes whether Port supports PCSR**
; ..\mcal_src\Port.c	  2330  **                                                                            **
; ..\mcal_src\Port.c	  2331  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2332  ** - The function like macro is to check if the port supports PCSR            **
; ..\mcal_src\Port.c	  2333  **   for the microcontroller.                                                 **
; ..\mcal_src\Port.c	  2334  *******************************************************************************/
; ..\mcal_src\Port.c	  2335  IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable31(uint32 Port)
; ..\mcal_src\Port.c	  2336  {
; ..\mcal_src\Port.c	  2337    uint32 RetVal;
; ..\mcal_src\Port.c	  2338  
; ..\mcal_src\Port.c	  2339    RetVal = ( ((uint32)(PORT_CONSTANT_0x01) << (Port)) &
; ..\mcal_src\Port.c	  2340               ((uint32)PORTS_PCSR_00_31)
; ..\mcal_src\Port.c	  2341             );
; ..\mcal_src\Port.c	  2342    return(RetVal);
; ..\mcal_src\Port.c	  2343  }
; ..\mcal_src\Port.c	  2344  
; ..\mcal_src\Port.c	  2345  /*******************************************************************************
; ..\mcal_src\Port.c	  2346  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable40     **
; ..\mcal_src\Port.c	  2347  **                    (                                                       **
; ..\mcal_src\Port.c	  2348  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  2349  **                    )                                                       **
; ..\mcal_src\Port.c	  2350  **                                                                            **
; ..\mcal_src\Port.c	  2351  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2352  **                                                                            **
; ..\mcal_src\Port.c	  2353  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2354  **                                                                            **
; ..\mcal_src\Port.c	  2355  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2356  **                                                                            **
; ..\mcal_src\Port.c	  2357  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  2358  **                                                                            **
; ..\mcal_src\Port.c	  2359  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2360  **                                                                            **
; ..\mcal_src\Port.c	  2361  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  2362  **                    RetValue -Value which denotes whether Port supports PCSR**
; ..\mcal_src\Port.c	  2363  **                                                                            **
; ..\mcal_src\Port.c	  2364  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2365  ** - The function like macro is to check if the port supports PCSR            **
; ..\mcal_src\Port.c	  2366  **   for the microcontroller.                                                 **
; ..\mcal_src\Port.c	  2367  *******************************************************************************/
; ..\mcal_src\Port.c	  2368  IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable40(uint32 Port)
; ..\mcal_src\Port.c	  2369  {
; ..\mcal_src\Port.c	  2370    uint32 RetVal;
; ..\mcal_src\Port.c	  2371  
; ..\mcal_src\Port.c	  2372    RetVal = ( ((uint32)(PORT_CONSTANT_0x01) << (Port - PORT_NUMBER_32)) &
; ..\mcal_src\Port.c	  2373               ((uint32)PORTS_PCSR_32_63)
; ..\mcal_src\Port.c	  2374             );
; ..\mcal_src\Port.c	  2375    return(RetVal);
; ..\mcal_src\Port.c	  2376  }
; ..\mcal_src\Port.c	  2377  
; ..\mcal_src\Port.c	  2378  /*******************************************************************************
; ..\mcal_src\Port.c	  2379  ** Syntax           : IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable       **
; ..\mcal_src\Port.c	  2380  **                    (                                                       **
; ..\mcal_src\Port.c	  2381  **                      uint32 Port                                           **
; ..\mcal_src\Port.c	  2382  **                    )                                                       **
; ..\mcal_src\Port.c	  2383  **                                                                            **
; ..\mcal_src\Port.c	  2384  ** Service ID       : None                                                    **
; ..\mcal_src\Port.c	  2385  **                                                                            **
; ..\mcal_src\Port.c	  2386  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Port.c	  2387  **                                                                            **
; ..\mcal_src\Port.c	  2388  ** Reentrancy       : Non reentrant                                           **
; ..\mcal_src\Port.c	  2389  **                                                                            **
; ..\mcal_src\Port.c	  2390  ** Parameters (in)  : uint32 Port - Port to be checked                        **
; ..\mcal_src\Port.c	  2391  **                                                                            **
; ..\mcal_src\Port.c	  2392  ** Parameters (out) : None                                                    **
; ..\mcal_src\Port.c	  2393  **                                                                            **
; ..\mcal_src\Port.c	  2394  ** Return value     : uint32                                                  **
; ..\mcal_src\Port.c	  2395  **                    RetValue -Value which denotes whether Port supports PCSR**
; ..\mcal_src\Port.c	  2396  **                                                                            **
; ..\mcal_src\Port.c	  2397  ** Description      :                                                         **
; ..\mcal_src\Port.c	  2398  ** - The function like macro is to check if the port supports PCSR            **
; ..\mcal_src\Port.c	  2399  **   for the microcontroller.                                                 **
; ..\mcal_src\Port.c	  2400  *******************************************************************************/
; ..\mcal_src\Port.c	  2401  IFX_LOCAL_INLINE uint32 Port_lIsPortPCSRAvailable(uint32 Port)
; ..\mcal_src\Port.c	  2402  {
; ..\mcal_src\Port.c	  2403    uint32 RetVal;
; ..\mcal_src\Port.c	  2404  
; ..\mcal_src\Port.c	  2405    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortPCSRAvailable40(Port)) :
	mov	d15,#31
.L220:

; ..\mcal_src\Port.c	  2405    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortPCSRAvailable40(Port)) :      (inlined)
	jge.u	d15,d9,.L21
.L221:
	mov	d15,#1
.L222:
	add	d0,d9,#-32
.L223:
	sh	d15,d15,d0
.L224:
	mov	d0,#768
.L225:
	and	d15,d0

; ..\mcal_src\Port.c	  2405    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortPCSRAvailable40(Port)) :      (inlined)
	j	.L22
.L21:
	mov	d15,#0

; ..\mcal_src\Port.c	  2405    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortPCSRAvailable40(Port)) :      (inlined)
.L22:

; ..\mcal_src\Port.c	  1772        if(Port_lIsPortPCSRAvailable(PortNumber) != 0U)      (inlined)
	jeq	d15,#0,.L23
.L126:
	mov	d4,#9375
	sh	d4,#4
	call	Mcal_ResetSafetyENDINIT_Timed
.L153:
	ld.w	d15,[a13+]
	st.w	[a15]100,d15
.L226:
	call	Mcal_SetSafetyENDINIT_Timed

; ..\mcal_src\Port.c	  1772        if(Port_lIsPortPCSRAvailable(PortNumber) != 0U)      (inlined)
.L23:
	add	d8,#1

; ..\mcal_src\Port.c	  1718      if(Port_lIsPortAvailable(PortNumber) != 0U)      (inlined)
.L13:
	add	d9,#1
	add.a	a12,#2

; ..\mcal_src\Port.c	  1715    for (PortNumber = 0U; PortNumber <= PORT_MAX_NUMBER ; PortNumber++)      (inlined)
	loop	a14,.L10
.L227:
	call	Mcal_ResetENDINIT
.L228:
	ld.a	a15,[a10]
	ld.a	a15,[a15]
	ld.a	a2,[+a15]4
	ld.hu	d15,[a2]0
	movh.a	a2,#61444
	st.w	[a2]@los(0xf003e060),d15
.L229:
	ld.a	a15,[a15]
	ld.hu	d15,[a15]2
	st.w	[a2]@los(0xf003e160),d15
.L230:
	j	Mcal_SetENDINIT
.L67:
	
__Port_Init_function_end:
	.size	Port_Init,__Port_Init_function_end-Port_Init
.L47:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_2')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_2
.L34:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	mov.u	d15,#60421
.L147:
	addih	d15,d15,#240
	fret
.L62:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_1')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_1
.L36:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	mov	d15,#10
	movh.a	a15,#61444
.L265:
	div.u	e0,d9,d15
	lea	a15,[a15]@los(0xf003a000)
.L266:
	sh	d15,d0,#4
.L267:
	add	d15,d1
	sh	d15,d15,#8
	fret
.L57:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Port_RefreshPortDirection')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Port_RefreshPortDirection
; Function Port_RefreshPortDirection
.L38:
Port_RefreshPortDirection:	.type	func
	mov	d2,#0
	movh.a	a15,#@his(Port_kAvailablePins)
.L156:
	mov	d3,d2
	movh.a	a2,#@his(Port_kConfigPtr)
.L139:
	mov	d5,#-1
	lea	a15,[a15]@los(Port_kAvailablePins)
.L140:
	ld.a	a2,[a2]@los(Port_kConfigPtr)
.L235:
	lea	a4,41
.L24:
	mov	d15,#31
.L236:

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
	jge.u	d15,d2,.L25
.L237:
	mov	d15,#1
.L238:
	add	d0,d2,#-32
.L239:
	sh	d15,d15,d0
.L240:
	mov	d0,#774
.L241:
	and	d15,d0

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
	j	.L26
.L25:
	mov	d15,#1
.L242:
	sh	d0,d15,d2
	fcall	.cocofun_2
.L243:
	and	d15,d0

; ..\mcal_src\Port.c	  2002    RetVal = ((Port > PORT_NUMBER_31) ? (Port_lIsPortAvailable40(Port)) :      (inlined)
.L26:
	jeq	d15,#0,.L27
.L143:
	mul	d15,d3,#28
	ld.a	a5,[a2]
.L244:
	movh.a	a6,#61444
	lea	a6,[a6]@los(0xf003a000)
.L245:
	addsc.a	a5,a5,d15,#0
.L157:
	mov	d15,#10
.L246:
	div.u	e0,d2,d15
.L247:
	mov	d6,#0
.L158:
	sh	d15,d0,#4
.L248:
	add	d1,d15
	sh	d15,d1,#8
.L249:
	addsc.a	a6,a6,d15,#0
.L250:
	lea	a6,[a6]16
.L28:
	mov	d0,#1
.L251:
	sh	d0,d0,d6
.L252:
	extr.u	d0,d0,#0,#16
	ld.hu	d1,[a15]0
.L253:
	and	d0,d1
.L254:
	jeq	d0,#0,.L29
.L141:
	and	d15,d6,#3
	ld.w	d1,[a6]
	sh	d0,d15,#3
	mov	d15,#248
	sh	d4,d15,d0
	ld.bu	d15,[a5]
	xor	d4,d5
	and	d1,d4
	sh	d15,d15,d0
.L159:
	or	d1,d15
	st.w	[a6],d1
.L29:
	add	d6,#1
.L255:
	and	d15,d6,#3
.L256:
	jne	d15,#0,.L30
.L257:
	add.a	a6,#4
.L30:
	mov	d15,#15
	add.a	a5,#1
.L258:
	jge.u	d15,d6,.L28
.L259:
	add	d3,#1
.L27:
	add	d2,#1
	add.a	a15,#2
	loop	a4,.L24
.L260:
	ret
.L130:
	
__Port_RefreshPortDirection_function_end:
	.size	Port_RefreshPortDirection,__Port_RefreshPortDirection_function_end-Port_RefreshPortDirection
.L52:
	; End of function
	
	.sdecl	'.rodata.CPU0.Private.DEFAULT_CONST_16BIT',data,rom,cluster('Port_kAvailablePins')
	.sect	'.rodata.CPU0.Private.DEFAULT_CONST_16BIT'
	.align	4
Port_kAvailablePins:	.type	object
	.size	Port_kAvailablePins,84
	.half	5119
	.space	2
	.half	511
	.space	14
	.half	110,8012
	.space	2
	.half	15,511,511
	.space	8
	.half	32717,252,31,2
	.space	18
	.half	8191,15
	.space	10
	.half	4095,4095
	.sdecl	'.bss.CPU0.Private.DEFAULT_RAM_32BIT',data,cluster('Port_kConfigPtr')
	.sect	'.bss.CPU0.Private.DEFAULT_RAM_32BIT'
	.align	4
Port_kConfigPtr:	.type	object
	.size	Port_kConfigPtr,4
	.space	4
	.calls	'Port_Init','Mcal_ResetENDINIT'
	.calls	'Port_Init','Mcal_SetENDINIT'
	.calls	'Port_Init','Mcal_ResetSafetyENDINIT_Timed'
	.calls	'Port_Init','Mcal_SetSafetyENDINIT_Timed'
	.calls	'Port_Init','.cocofun_2'
	.calls	'Port_Init','.cocofun_1'
	.calls	'Port_RefreshPortDirection','.cocofun_2'
	.calls	'Port_Init','',8
	.calls	'.cocofun_2','',0
	.calls	'.cocofun_1','',0
	.extern	Mcal_ResetENDINIT
	.extern	Mcal_SetENDINIT
	.extern	Mcal_ResetSafetyENDINIT_Timed
	.extern	Mcal_SetSafetyENDINIT_Timed
	.calls	'Port_RefreshPortDirection','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L40:
	.word	11196
	.half	3
	.word	.L41
	.byte	4
.L39:
	.byte	1
	.byte	'..\\mcal_src\\Port.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L42
.L70:
	.byte	2
	.byte	'Port_lIOInit',0,3,1,154,13,23,1,1
.L73:
	.byte	3,4,4,4,4,4,4,4,4,4,4,0,0
.L86:
	.byte	2
	.byte	'Port_lPDRInit',0,3,1,163,14,23,1,1
.L87:
	.byte	3,4,4,4,4,4,0,0,5
	.byte	'_Ifx_P',0,2,159,5,25,128,2,6,2,239,4,9,4,7
	.byte	'unsigned int',0,4,7,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,7
	.byte	'int',0,4,5,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OUT_Bits',0,2,231,2,16,4,7
	.byte	'unsigned char',0,1,8,9
	.byte	'P0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'P2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'P3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'P4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'P5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'P6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'P7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'P8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'P9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'P10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'P11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'P12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'P13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'P14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'P15',0,1
	.word	324
	.byte	1,0,2,35,1,7
	.byte	'unsigned short int',0,2,7,9
	.byte	'reserved_16',0,2
	.word	571
	.byte	16,0,2,35,2,0,8
	.byte	'B',0,4
	.word	302
	.byte	2,35,0,0,8
	.byte	'OUT',0,4
	.word	251
	.byte	2,35,0,6,2,191,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMR_Bits',0,2,129,2,16,4,9
	.byte	'PS0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'PS1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'PS2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'PS3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'PS4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'PS5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'PS6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'PS7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'PS8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'PS9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'PS10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'PS11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'PS12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'PS13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'PS14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'PS15',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'PCL0',0,1
	.word	324
	.byte	1,7,2,35,2,9
	.byte	'PCL1',0,1
	.word	324
	.byte	1,6,2,35,2,9
	.byte	'PCL2',0,1
	.word	324
	.byte	1,5,2,35,2,9
	.byte	'PCL3',0,1
	.word	324
	.byte	1,4,2,35,2,9
	.byte	'PCL4',0,1
	.word	324
	.byte	1,3,2,35,2,9
	.byte	'PCL5',0,1
	.word	324
	.byte	1,2,2,35,2,9
	.byte	'PCL6',0,1
	.word	324
	.byte	1,1,2,35,2,9
	.byte	'PCL7',0,1
	.word	324
	.byte	1,0,2,35,2,9
	.byte	'PCL8',0,1
	.word	324
	.byte	1,7,2,35,3,9
	.byte	'PCL9',0,1
	.word	324
	.byte	1,6,2,35,3,9
	.byte	'PCL10',0,1
	.word	324
	.byte	1,5,2,35,3,9
	.byte	'PCL11',0,1
	.word	324
	.byte	1,4,2,35,3,9
	.byte	'PCL12',0,1
	.word	324
	.byte	1,3,2,35,3,9
	.byte	'PCL13',0,1
	.word	324
	.byte	1,2,2,35,3,9
	.byte	'PCL14',0,1
	.word	324
	.byte	1,1,2,35,3,9
	.byte	'PCL15',0,1
	.word	324
	.byte	1,0,2,35,3,0,8
	.byte	'B',0,4
	.word	670
	.byte	2,35,0,0,8
	.byte	'OMR',0,4
	.word	642
	.byte	2,35,4,6,2,231,3,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_ID_Bits',0,2,110,16,4,9
	.byte	'MODREV',0,1
	.word	324
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	324
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	571
	.byte	16,0,2,35,2,0,8
	.byte	'B',0,4
	.word	1254
	.byte	2,35,0,0,8
	.byte	'ID',0,4
	.word	1226
	.byte	2,35,8,10,4
	.word	324
	.byte	11,3,0,8
	.byte	'reserved_C',0,4
	.word	1357
	.byte	2,35,12,6,2,247,3,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_IOCR0_Bits',0,2,140,1,16,4,9
	.byte	'reserved_0',0,1
	.word	324
	.byte	3,5,2,35,0,9
	.byte	'PC0',0,1
	.word	324
	.byte	5,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	324
	.byte	3,5,2,35,1,9
	.byte	'PC1',0,1
	.word	324
	.byte	5,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	324
	.byte	3,5,2,35,2,9
	.byte	'PC2',0,1
	.word	324
	.byte	5,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	324
	.byte	3,5,2,35,3,9
	.byte	'PC3',0,1
	.word	324
	.byte	5,0,2,35,3,0,8
	.byte	'B',0,4
	.word	1414
	.byte	2,35,0,0,8
	.byte	'IOCR0',0,4
	.word	1386
	.byte	2,35,16,6,2,135,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_IOCR4_Bits',0,2,166,1,16,4,9
	.byte	'reserved_0',0,1
	.word	324
	.byte	3,5,2,35,0,9
	.byte	'PC4',0,1
	.word	324
	.byte	5,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	324
	.byte	3,5,2,35,1,9
	.byte	'PC5',0,1
	.word	324
	.byte	5,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	324
	.byte	3,5,2,35,2,9
	.byte	'PC6',0,1
	.word	324
	.byte	5,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	324
	.byte	3,5,2,35,3,9
	.byte	'PC7',0,1
	.word	324
	.byte	5,0,2,35,3,0,8
	.byte	'B',0,4
	.word	1644
	.byte	2,35,0,0,8
	.byte	'IOCR4',0,4
	.word	1616
	.byte	2,35,20,6,2,143,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_IOCR8_Bits',0,2,179,1,16,4,9
	.byte	'reserved_0',0,1
	.word	324
	.byte	3,5,2,35,0,9
	.byte	'PC8',0,1
	.word	324
	.byte	5,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	324
	.byte	3,5,2,35,1,9
	.byte	'PC9',0,1
	.word	324
	.byte	5,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	324
	.byte	3,5,2,35,2,9
	.byte	'PC10',0,1
	.word	324
	.byte	5,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	324
	.byte	3,5,2,35,3,9
	.byte	'PC11',0,1
	.word	324
	.byte	5,0,2,35,3,0,8
	.byte	'B',0,4
	.word	1874
	.byte	2,35,0,0,8
	.byte	'IOCR8',0,4
	.word	1846
	.byte	2,35,24,6,2,255,3,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_IOCR12_Bits',0,2,153,1,16,4,9
	.byte	'reserved_0',0,1
	.word	324
	.byte	3,5,2,35,0,9
	.byte	'PC12',0,1
	.word	324
	.byte	5,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	324
	.byte	3,5,2,35,1,9
	.byte	'PC13',0,1
	.word	324
	.byte	5,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	324
	.byte	3,5,2,35,2,9
	.byte	'PC14',0,1
	.word	324
	.byte	5,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	324
	.byte	3,5,2,35,3,9
	.byte	'PC15',0,1
	.word	324
	.byte	5,0,2,35,3,0,8
	.byte	'B',0,4
	.word	2106
	.byte	2,35,0,0,8
	.byte	'IOCR12',0,4
	.word	2078
	.byte	2,35,28,8
	.byte	'reserved_20',0,4
	.word	1357
	.byte	2,35,32,6,2,239,3,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_IN_Bits',0,2,118,16,4,9
	.byte	'P0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'P2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'P3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'P4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'P5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'P6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'P7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'P8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'P9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'P10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'P11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'P12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'P13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'P14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'P15',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	571
	.byte	16,0,2,35,2,0,8
	.byte	'B',0,4
	.word	2363
	.byte	2,35,0,0,8
	.byte	'IN',0,4
	.word	2335
	.byte	2,35,36,10,24
	.word	324
	.byte	11,23,0,8
	.byte	'reserved_28',0,24
	.word	2661
	.byte	2,35,40,6,2,135,5,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_PDR0_Bits',0,2,160,3,16,4,9
	.byte	'PD0',0,1
	.word	324
	.byte	3,5,2,35,0,9
	.byte	'PL0',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'PD1',0,1
	.word	324
	.byte	3,1,2,35,0,9
	.byte	'PL1',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'PD2',0,1
	.word	324
	.byte	3,5,2,35,1,9
	.byte	'PL2',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'PD3',0,1
	.word	324
	.byte	3,1,2,35,1,9
	.byte	'PL3',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'PD4',0,1
	.word	324
	.byte	3,5,2,35,2,9
	.byte	'PL4',0,1
	.word	324
	.byte	1,4,2,35,2,9
	.byte	'PD5',0,1
	.word	324
	.byte	3,1,2,35,2,9
	.byte	'PL5',0,1
	.word	324
	.byte	1,0,2,35,2,9
	.byte	'PD6',0,1
	.word	324
	.byte	3,5,2,35,3,9
	.byte	'PL6',0,1
	.word	324
	.byte	1,4,2,35,3,9
	.byte	'PD7',0,1
	.word	324
	.byte	3,1,2,35,3,9
	.byte	'PL7',0,1
	.word	324
	.byte	1,0,2,35,3,0,8
	.byte	'B',0,4
	.word	2719
	.byte	2,35,0,0,8
	.byte	'PDR0',0,4
	.word	2691
	.byte	2,35,64,6,2,143,5,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_PDR1_Bits',0,2,181,3,16,4,9
	.byte	'PD8',0,1
	.word	324
	.byte	3,5,2,35,0,9
	.byte	'PL8',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'PD9',0,1
	.word	324
	.byte	3,1,2,35,0,9
	.byte	'PL9',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'PD10',0,1
	.word	324
	.byte	3,5,2,35,1,9
	.byte	'PL10',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'PD11',0,1
	.word	324
	.byte	3,1,2,35,1,9
	.byte	'PL11',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'PD12',0,1
	.word	324
	.byte	3,5,2,35,2,9
	.byte	'PL12',0,1
	.word	324
	.byte	1,4,2,35,2,9
	.byte	'PD13',0,1
	.word	324
	.byte	3,1,2,35,2,9
	.byte	'PL13',0,1
	.word	324
	.byte	1,0,2,35,2,9
	.byte	'PD14',0,1
	.word	324
	.byte	3,5,2,35,3,9
	.byte	'PL14',0,1
	.word	324
	.byte	1,4,2,35,3,9
	.byte	'PD15',0,1
	.word	324
	.byte	3,1,2,35,3,9
	.byte	'PL15',0,1
	.word	324
	.byte	1,0,2,35,3,0,8
	.byte	'B',0,4
	.word	3037
	.byte	2,35,0,0,8
	.byte	'PDR1',0,4
	.word	3009
	.byte	2,35,68,10,8
	.word	324
	.byte	11,7,0,8
	.byte	'reserved_48',0,8
	.word	3339
	.byte	2,35,72,6,2,223,3,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_ESR_Bits',0,2,88,16,4,9
	.byte	'EN0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	571
	.byte	16,0,2,35,2,0,8
	.byte	'B',0,4
	.word	3397
	.byte	2,35,0,0,8
	.byte	'ESR',0,4
	.word	3369
	.byte	2,35,80,10,12
	.word	324
	.byte	11,11,0,8
	.byte	'reserved_54',0,12
	.word	3713
	.byte	2,35,84,6,2,255,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_PDISC_Bits',0,2,138,3,16,4,9
	.byte	'PDIS0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'PDIS1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'PDIS2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'PDIS3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'PDIS4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'PDIS5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'PDIS6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'PDIS7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'PDIS8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'PDIS9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'PDIS10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'PDIS11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'PDIS12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'PDIS13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'PDIS14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'PDIS15',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	571
	.byte	16,0,2,35,2,0,8
	.byte	'B',0,4
	.word	3771
	.byte	2,35,0,0,8
	.byte	'PDISC',0,4
	.word	3743
	.byte	2,35,96,6,2,247,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_PCSR_Bits',0,2,253,2,16,4,9
	.byte	'reserved_0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'SEL1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'SEL2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,2
	.word	571
	.byte	6,7,2,35,0,9
	.byte	'SEL9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'SEL10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'reserved_11',0,4
	.word	257
	.byte	20,1,2,35,2,9
	.byte	'LCK',0,1
	.word	324
	.byte	1,0,2,35,3,0,8
	.byte	'B',0,4
	.word	4152
	.byte	2,35,0,0,8
	.byte	'PCSR',0,4
	.word	4124
	.byte	2,35,100,8
	.byte	'reserved_64',0,8
	.word	3339
	.byte	2,35,104,6,2,207,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMSR0_Bits',0,2,166,2,16,4,9
	.byte	'PS0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'PS1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'PS2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'PS3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	257
	.byte	28,0,2,35,2,0,8
	.byte	'B',0,4
	.word	4398
	.byte	2,35,0,0,8
	.byte	'OMSR0',0,4
	.word	4370
	.byte	2,35,112,6,2,223,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMSR4_Bits',0,2,187,2,16,4,9
	.byte	'reserved_0',0,1
	.word	324
	.byte	4,4,2,35,0,9
	.byte	'PS4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'PS5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'PS6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'PS7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	257
	.byte	24,0,2,35,2,0,8
	.byte	'B',0,4
	.word	4560
	.byte	2,35,0,0,8
	.byte	'OMSR4',0,4
	.word	4532
	.byte	2,35,116,6,2,231,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMSR8_Bits',0,2,198,2,16,4,9
	.byte	'reserved_0',0,1
	.word	324
	.byte	8,0,2,35,0,9
	.byte	'PS8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'PS9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'PS10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'PS11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,4
	.word	257
	.byte	20,0,2,35,2,0,8
	.byte	'B',0,4
	.word	4744
	.byte	2,35,0,0,8
	.byte	'OMSR8',0,4
	.word	4716
	.byte	2,35,120,6,2,215,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMSR12_Bits',0,2,176,2,16,4,9
	.byte	'reserved_0',0,2
	.word	571
	.byte	12,4,2,35,0,9
	.byte	'PS12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'PS13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'PS14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'PS15',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	571
	.byte	16,0,2,35,2,0,8
	.byte	'B',0,4
	.word	4931
	.byte	2,35,0,0,8
	.byte	'OMSR12',0,4
	.word	4903
	.byte	2,35,124,6,2,159,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMCR0_Bits',0,2,192,1,16,4,9
	.byte	'reserved_0',0,2
	.word	571
	.byte	16,0,2,35,0,9
	.byte	'PCL0',0,1
	.word	324
	.byte	1,7,2,35,2,9
	.byte	'PCL1',0,1
	.word	324
	.byte	1,6,2,35,2,9
	.byte	'PCL2',0,1
	.word	324
	.byte	1,5,2,35,2,9
	.byte	'PCL3',0,1
	.word	324
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,2
	.word	571
	.byte	12,0,2,35,2,0,8
	.byte	'B',0,4
	.word	5122
	.byte	2,35,0,0,8
	.byte	'OMCR0',0,4
	.word	5094
	.byte	3,35,128,1,6,2,175,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMCR4_Bits',0,2,213,1,16,4,9
	.byte	'reserved_0',0,4
	.word	257
	.byte	20,12,2,35,2,9
	.byte	'PCL4',0,1
	.word	324
	.byte	1,3,2,35,2,9
	.byte	'PCL5',0,1
	.word	324
	.byte	1,2,2,35,2,9
	.byte	'PCL6',0,1
	.word	324
	.byte	1,1,2,35,2,9
	.byte	'PCL7',0,1
	.word	324
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	324
	.byte	8,0,2,35,3,0,8
	.byte	'B',0,4
	.word	5312
	.byte	2,35,0,0,8
	.byte	'OMCR4',0,4
	.word	5284
	.byte	3,35,132,1,6,2,183,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMCR8_Bits',0,2,224,1,16,4,9
	.byte	'reserved_0',0,4
	.word	257
	.byte	24,8,2,35,2,9
	.byte	'PCL8',0,1
	.word	324
	.byte	1,7,2,35,3,9
	.byte	'PCL9',0,1
	.word	324
	.byte	1,6,2,35,3,9
	.byte	'PCL10',0,1
	.word	324
	.byte	1,5,2,35,3,9
	.byte	'PCL11',0,1
	.word	324
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	324
	.byte	4,0,2,35,3,0,8
	.byte	'B',0,4
	.word	5502
	.byte	2,35,0,0,8
	.byte	'OMCR8',0,4
	.word	5474
	.byte	3,35,136,1,6,2,167,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMCR12_Bits',0,2,203,1,16,4,9
	.byte	'reserved_0',0,4
	.word	257
	.byte	28,4,2,35,2,9
	.byte	'PCL12',0,1
	.word	324
	.byte	1,3,2,35,3,9
	.byte	'PCL13',0,1
	.word	324
	.byte	1,2,2,35,3,9
	.byte	'PCL14',0,1
	.word	324
	.byte	1,1,2,35,3,9
	.byte	'PCL15',0,1
	.word	324
	.byte	1,0,2,35,3,0,8
	.byte	'B',0,4
	.word	5694
	.byte	2,35,0,0,8
	.byte	'OMCR12',0,4
	.word	5666
	.byte	3,35,140,1,6,2,199,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMSR_Bits',0,2,209,2,16,4,9
	.byte	'PS0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'PS1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'PS2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'PS3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'PS4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'PS5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'PS6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'PS7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'PS8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'PS9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'PS10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'PS11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'PS12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'PS13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'PS14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'PS15',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	571
	.byte	16,0,2,35,2,0,8
	.byte	'B',0,4
	.word	5867
	.byte	2,35,0,0,8
	.byte	'OMSR',0,4
	.word	5839
	.byte	3,35,144,1,6,2,151,4,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_OMCR_Bits',0,2,235,1,16,4,9
	.byte	'reserved_0',0,2
	.word	571
	.byte	16,0,2,35,0,9
	.byte	'PCL0',0,1
	.word	324
	.byte	1,7,2,35,2,9
	.byte	'PCL1',0,1
	.word	324
	.byte	1,6,2,35,2,9
	.byte	'PCL2',0,1
	.word	324
	.byte	1,5,2,35,2,9
	.byte	'PCL3',0,1
	.word	324
	.byte	1,4,2,35,2,9
	.byte	'PCL4',0,1
	.word	324
	.byte	1,3,2,35,2,9
	.byte	'PCL5',0,1
	.word	324
	.byte	1,2,2,35,2,9
	.byte	'PCL6',0,1
	.word	324
	.byte	1,1,2,35,2,9
	.byte	'PCL7',0,1
	.word	324
	.byte	1,0,2,35,2,9
	.byte	'PCL8',0,1
	.word	324
	.byte	1,7,2,35,3,9
	.byte	'PCL9',0,1
	.word	324
	.byte	1,6,2,35,3,9
	.byte	'PCL10',0,1
	.word	324
	.byte	1,5,2,35,3,9
	.byte	'PCL11',0,1
	.word	324
	.byte	1,4,2,35,3,9
	.byte	'PCL12',0,1
	.word	324
	.byte	1,3,2,35,3,9
	.byte	'PCL13',0,1
	.word	324
	.byte	1,2,2,35,3,9
	.byte	'PCL14',0,1
	.word	324
	.byte	1,1,2,35,3,9
	.byte	'PCL15',0,1
	.word	324
	.byte	1,0,2,35,3,0,8
	.byte	'B',0,4
	.word	6215
	.byte	2,35,0,0,8
	.byte	'OMCR',0,4
	.word	6187
	.byte	3,35,148,1,10,96
	.word	324
	.byte	11,95,0,8
	.byte	'reserved_98',0,96
	.word	6550
	.byte	3,35,152,1,6,2,215,3,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_ACCEN1_Bits',0,2,82,16,4,9
	.byte	'reserved_0',0,4
	.word	257
	.byte	32,0,2,35,2,0,8
	.byte	'B',0,4
	.word	6609
	.byte	2,35,0,0,8
	.byte	'ACCEN1',0,4
	.word	6581
	.byte	3,35,248,1,6,2,207,3,9,4,8
	.byte	'U',0,4
	.word	257
	.byte	2,35,0,8
	.byte	'I',0,4
	.word	284
	.byte	2,35,0,5
	.byte	'_Ifx_P_ACCEN0_Bits',0,2,45,16,4,9
	.byte	'EN0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	324
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	324
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	324
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	324
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	324
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	324
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	324
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	324
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	324
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	324
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	324
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	324
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	324
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	324
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	324
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	324
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	324
	.byte	1,0,2,35,3,0,8
	.byte	'B',0,4
	.word	6713
	.byte	2,35,0,0,8
	.byte	'ACCEN0',0,4
	.word	6685
	.byte	3,35,252,1,0,12
	.word	237
	.byte	13
	.word	7270
.L103:
	.byte	14
	.byte	'Port_lAdr',0,3,1,226,14,26
	.word	7275
	.byte	1,1
.L82:
	.byte	7
	.byte	'unsigned long int',0,4,7
.L106:
	.byte	15
	.byte	'PortNumber',0,1,226,14,43
	.word	7302
.L108:
	.byte	4,0,14
	.byte	'Port_lIsPortAvailable31',0,3,1,136,15,25
	.word	7302
	.byte	1,1,15
	.byte	'Port',0,1,136,15,56
	.word	7302
	.byte	4,0,14
	.byte	'Port_lIsPortAvailable40',0,3,1,171,15,25
	.word	7302
	.byte	1,1,15
	.byte	'Port',0,1,171,15,56
	.word	7302
	.byte	4,0
.L99:
	.byte	14
	.byte	'Port_lIsPortAvailable',0,3,1,206,15,25
	.word	7302
	.byte	1,1
.L100:
	.byte	15
	.byte	'Port',0,1,206,15,54
	.word	7302
.L102:
	.byte	3,4,4,0,0,14
	.byte	'Port_lIsPortReadOnly40',0,3,1,146,16,25
	.word	7302
	.byte	1,1,15
	.byte	'Port',0,1,146,16,55
	.word	7302
	.byte	4,0
.L92:
	.byte	14
	.byte	'Port_lIsPortReadOnly',0,3,1,180,16,25
	.word	7302
	.byte	1,1
.L93:
	.byte	15
	.byte	'Port',0,1,180,16,53
	.word	7302
.L95:
	.byte	3,4,0,0,14
	.byte	'Port_lIsPinAvailable',0,3,1,214,16,25
	.word	571
	.byte	1,1,15
	.byte	'Port',0,1,214,16,53
	.word	7302
	.byte	15
	.byte	'Pin',0,1,214,16,65
	.word	7302
	.byte	4,0,14
	.byte	'Port_lIsPortPdr1Available',0,3,1,248,16,25
	.word	571
	.byte	1,1,15
	.byte	'Port',0,1,248,16,58
	.word	7302
	.byte	4,0
.L115:
	.byte	14
	.byte	'Port_lIsPortIocrAvailable',0,3,1,152,17,25
	.word	571
	.byte	1,1
.L117:
	.byte	15
	.byte	'Port',0,1,152,17,58
	.word	7302
.L119:
	.byte	15
	.byte	'Pin',0,1,152,17,70
	.word	571
.L121:
	.byte	4,0,14
	.byte	'Port_lPinLevel',0,3,1,252,17,25
	.word	7302
	.byte	1,1,15
	.byte	'Level',0,1,252,17,47
	.word	7302
	.byte	4,0,14
	.byte	'Port_lIsPortPCSRAvailable40',0,3,1,192,18,25
	.word	7302
	.byte	1,1,15
	.byte	'Port',0,1,192,18,60
	.word	7302
	.byte	4,0
.L125:
	.byte	14
	.byte	'Port_lIsPortPCSRAvailable',0,3,1,225,18,25
	.word	7302
	.byte	1,1
.L127:
	.byte	15
	.byte	'Port',0,1,225,18,58
	.word	7302
.L129:
	.byte	3,4,0,0,16
	.byte	'Mcal_ResetENDINIT',0,3,119,13,1,1,1,1,16
	.byte	'Mcal_SetENDINIT',0,3,146,1,13,1,1,1,1,17
	.byte	'Mcal_ResetSafetyENDINIT_Timed',0,3,190,2,13,1,1,1,1,15
	.byte	'TimeOut',0,3,190,2,50
	.word	7302
	.byte	0,16
	.byte	'Mcal_SetSafetyENDINIT_Timed',0,3,214,2,13,1,1,1,1,5
	.byte	'Port_ConfigType',0,4,176,3,16,12,5
	.byte	'Port_n_ConfigType',0,4,140,3,16,28,18
	.byte	'Port_n_ControlType',0,4,206,2,15,16,19,4,208,2,3,16,8
	.byte	'PC0',0,1
	.word	324
	.byte	2,35,0,8
	.byte	'PC1',0,1
	.word	324
	.byte	2,35,1,8
	.byte	'PC2',0,1
	.word	324
	.byte	2,35,2,8
	.byte	'PC3',0,1
	.word	324
	.byte	2,35,3,8
	.byte	'PC4',0,1
	.word	324
	.byte	2,35,4,8
	.byte	'PC5',0,1
	.word	324
	.byte	2,35,5,8
	.byte	'PC6',0,1
	.word	324
	.byte	2,35,6,8
	.byte	'PC7',0,1
	.word	324
	.byte	2,35,7,8
	.byte	'PC8',0,1
	.word	324
	.byte	2,35,8,8
	.byte	'PC9',0,1
	.word	324
	.byte	2,35,9,8
	.byte	'PC10',0,1
	.word	324
	.byte	2,35,10,8
	.byte	'PC11',0,1
	.word	324
	.byte	2,35,11,8
	.byte	'PC12',0,1
	.word	324
	.byte	2,35,12,8
	.byte	'PC13',0,1
	.word	324
	.byte	2,35,13,8
	.byte	'PC14',0,1
	.word	324
	.byte	2,35,14,8
	.byte	'PC15',0,1
	.word	324
	.byte	2,35,15,0,8
	.byte	'B',0,16
	.word	8159
	.byte	2,35,0,10,16
	.word	7302
	.byte	11,3,0,8
	.byte	'U',0,16
	.word	8391
	.byte	2,35,0,0,8
	.byte	'PinControl',0,16
	.word	8134
	.byte	2,35,0,18
	.byte	'Port_n_PinType',0,4,176,2,15,4,19,4,178,2,3,2,9
	.byte	'P0',0,1
	.word	324
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	324
	.byte	1,6,2,35,0,9
	.byte	'P2',0,1
	.word	324
	.byte	1,5,2,35,0,9
	.byte	'P3',0,1
	.word	324
	.byte	1,4,2,35,0,9
	.byte	'P4',0,1
	.word	324
	.byte	1,3,2,35,0,9
	.byte	'P5',0,1
	.word	324
	.byte	1,2,2,35,0,9
	.byte	'P6',0,1
	.word	324
	.byte	1,1,2,35,0,9
	.byte	'P7',0,1
	.word	324
	.byte	1,0,2,35,0,9
	.byte	'P8',0,1
	.word	324
	.byte	1,7,2,35,1,9
	.byte	'P9',0,1
	.word	324
	.byte	1,6,2,35,1,9
	.byte	'P10',0,1
	.word	324
	.byte	1,5,2,35,1,9
	.byte	'P11',0,1
	.word	324
	.byte	1,4,2,35,1,9
	.byte	'P12',0,1
	.word	324
	.byte	1,3,2,35,1,9
	.byte	'P13',0,1
	.word	324
	.byte	1,2,2,35,1,9
	.byte	'P14',0,1
	.word	324
	.byte	1,1,2,35,1,9
	.byte	'P15',0,1
	.word	324
	.byte	1,0,2,35,1,0,8
	.byte	'B',0,2
	.word	8453
	.byte	2,35,0,8
	.byte	'U',0,4
	.word	7302
	.byte	2,35,0,0,8
	.byte	'PinLevel',0,4
	.word	8432
	.byte	2,35,16,8
	.byte	'DriverStrength0',0,4
	.word	7302
	.byte	2,35,20,8
	.byte	'DriverStrength1',0,4
	.word	7302
	.byte	2,35,24,0,20
	.word	8110
.L80:
	.byte	13
	.word	8782
	.byte	8
	.byte	'PortConfigSetPtr',0,4
	.word	8787
	.byte	2,35,0,20
	.word	571
	.byte	13
	.word	8818
	.byte	8
	.byte	'PDiscSet',0,4
	.word	8823
	.byte	2,35,4,20
	.word	7302
	.byte	13
	.word	8846
	.byte	8
	.byte	'Port_PCSRConfigTypePtr',0,4
	.word	8851
	.byte	2,35,8,0,20
	.word	8088
.L68:
	.byte	13
	.word	8889
	.byte	20
	.word	7302
.L78:
	.byte	13
	.word	8899
	.byte	20
	.word	324
.L134:
	.byte	13
	.word	8909
	.byte	12
	.word	7302
.L136:
	.byte	13
	.word	8919
	.byte	21
	.word	173
	.byte	3,22
	.word	7720
	.byte	23
	.word	7758
	.byte	23
	.word	7772
	.byte	24
	.word	7785
	.byte	0,4,22
	.word	7720
	.byte	23
	.word	7758
	.byte	23
	.word	7772
	.byte	24
	.word	7785
	.byte	0,4,22
	.word	7720
	.byte	23
	.word	7758
	.byte	23
	.word	7772
	.byte	24
	.word	7785
	.byte	0,4,22
	.word	7720
	.byte	23
	.word	7758
	.byte	23
	.word	7772
	.byte	24
	.word	7785
	.byte	0,4,22
	.word	7787
	.byte	23
	.word	7814
	.byte	24
	.word	7829
	.byte	0,4,22
	.word	7280
	.byte	23
	.word	7323
	.byte	24
	.word	7343
	.byte	0,4,22
	.word	7553
	.byte	23
	.word	7586
	.byte	25
	.word	7600
	.byte	22
	.word	7502
	.byte	23
	.word	7537
	.byte	24
	.word	7551
	.byte	0,24
	.word	7601
	.byte	0,0,4,22
	.word	7887
	.byte	23
	.word	7925
	.byte	25
	.word	7939
	.byte	22
	.word	7831
	.byte	23
	.word	7871
	.byte	24
	.word	7885
	.byte	0,24
	.word	7940
	.byte	0,0,4,22
	.word	7449
	.byte	23
	.word	7483
	.byte	25
	.word	7497
	.byte	22
	.word	7345
	.byte	23
	.word	7381
	.byte	24
	.word	7395
	.byte	0,24
	.word	7498
	.byte	22
	.word	7397
	.byte	23
	.word	7433
	.byte	24
	.word	7447
	.byte	0,24
	.word	7499
	.byte	0,0,4,22
	.word	207
	.byte	25
	.word	229
	.byte	22
	.word	7666
	.byte	23
	.word	7704
	.byte	24
	.word	7718
	.byte	0,24
	.word	230
	.byte	22
	.word	7280
	.byte	23
	.word	7323
	.byte	24
	.word	7343
	.byte	0,24
	.word	231
	.byte	22
	.word	7280
	.byte	23
	.word	7323
	.byte	24
	.word	7343
	.byte	0,24
	.word	232
	.byte	22
	.word	7553
	.byte	23
	.word	7586
	.byte	25
	.word	7600
	.byte	22
	.word	7502
	.byte	23
	.word	7537
	.byte	24
	.word	7551
	.byte	0,24
	.word	7601
	.byte	0,0,24
	.word	233
	.byte	22
	.word	7449
	.byte	23
	.word	7483
	.byte	25
	.word	7497
	.byte	22
	.word	7345
	.byte	23
	.word	7381
	.byte	24
	.word	7395
	.byte	0,24
	.word	7498
	.byte	22
	.word	7397
	.byte	23
	.word	7433
	.byte	24
	.word	7447
	.byte	0,24
	.word	7499
	.byte	0,0,24
	.word	234
	.byte	0,0,4,0,0,21
	.word	207
	.byte	3,22
	.word	7666
	.byte	23
	.word	7704
	.byte	24
	.word	7718
	.byte	0,4,22
	.word	7280
	.byte	23
	.word	7323
	.byte	24
	.word	7343
	.byte	0,4,22
	.word	7280
	.byte	23
	.word	7323
	.byte	24
	.word	7343
	.byte	0,4,22
	.word	7553
	.byte	23
	.word	7586
	.byte	25
	.word	7600
	.byte	22
	.word	7502
	.byte	23
	.word	7537
	.byte	24
	.word	7551
	.byte	0,24
	.word	7601
	.byte	0,0,4,22
	.word	7449
	.byte	23
	.word	7483
	.byte	25
	.word	7497
	.byte	22
	.word	7345
	.byte	23
	.word	7381
	.byte	24
	.word	7395
	.byte	0,24
	.word	7498
	.byte	22
	.word	7397
	.byte	23
	.word	7433
	.byte	24
	.word	7447
	.byte	0,24
	.word	7499
	.byte	0,0,4,0,0,21
	.word	7280
	.byte	23
	.word	7323
	.byte	4,0,21
	.word	7345
	.byte	23
	.word	7381
	.byte	4,0,21
	.word	7397
	.byte	23
	.word	7433
	.byte	4,0,21
	.word	7449
	.byte	23
	.word	7483
	.byte	3,22
	.word	7345
	.byte	23
	.word	7381
	.byte	24
	.word	7395
	.byte	0,4,22
	.word	7397
	.byte	23
	.word	7433
	.byte	24
	.word	7447
	.byte	0,4,0,0,21
	.word	7502
	.byte	23
	.word	7537
	.byte	4,0,21
	.word	7553
	.byte	23
	.word	7586
	.byte	3,22
	.word	7502
	.byte	23
	.word	7537
	.byte	24
	.word	7551
	.byte	0,4,0,0,21
	.word	7604
	.byte	23
	.word	7637
	.byte	23
	.word	7651
	.byte	4,0,21
	.word	7666
	.byte	23
	.word	7704
	.byte	4,0,21
	.word	7720
	.byte	23
	.word	7758
	.byte	23
	.word	7772
	.byte	4,0,21
	.word	7787
	.byte	23
	.word	7814
	.byte	4,0,21
	.word	7831
	.byte	23
	.word	7871
	.byte	4,0,21
	.word	7887
	.byte	23
	.word	7925
	.byte	3,22
	.word	7831
	.byte	23
	.word	7871
	.byte	24
	.word	7885
	.byte	0,4,0,0,26
	.byte	'void',0,13
	.word	9763
	.byte	27
	.byte	'__prof_adm',0,1,1,1
	.word	9769
	.byte	28,1,13
	.word	9793
	.byte	27
	.byte	'__codeptr',0,1,1,1
	.word	9795
	.byte	27
	.byte	'Ifx_P_ACCEN0_Bits',0,2,79,3
	.word	6713
	.byte	27
	.byte	'Ifx_P_ACCEN1_Bits',0,2,85,3
	.word	6609
	.byte	27
	.byte	'Ifx_P_ESR_Bits',0,2,107,3
	.word	3397
	.byte	27
	.byte	'Ifx_P_ID_Bits',0,2,115,3
	.word	1254
	.byte	27
	.byte	'Ifx_P_IN_Bits',0,2,137,1,3
	.word	2363
	.byte	27
	.byte	'Ifx_P_IOCR0_Bits',0,2,150,1,3
	.word	1414
	.byte	27
	.byte	'Ifx_P_IOCR12_Bits',0,2,163,1,3
	.word	2106
	.byte	27
	.byte	'Ifx_P_IOCR4_Bits',0,2,176,1,3
	.word	1644
	.byte	27
	.byte	'Ifx_P_IOCR8_Bits',0,2,189,1,3
	.word	1874
	.byte	27
	.byte	'Ifx_P_OMCR0_Bits',0,2,200,1,3
	.word	5122
	.byte	27
	.byte	'Ifx_P_OMCR12_Bits',0,2,210,1,3
	.word	5694
	.byte	27
	.byte	'Ifx_P_OMCR4_Bits',0,2,221,1,3
	.word	5312
	.byte	27
	.byte	'Ifx_P_OMCR8_Bits',0,2,232,1,3
	.word	5502
	.byte	27
	.byte	'Ifx_P_OMCR_Bits',0,2,254,1,3
	.word	6215
	.byte	27
	.byte	'Ifx_P_OMR_Bits',0,2,163,2,3
	.word	670
	.byte	27
	.byte	'Ifx_P_OMSR0_Bits',0,2,173,2,3
	.word	4398
	.byte	27
	.byte	'Ifx_P_OMSR12_Bits',0,2,184,2,3
	.word	4931
	.byte	27
	.byte	'Ifx_P_OMSR4_Bits',0,2,195,2,3
	.word	4560
	.byte	27
	.byte	'Ifx_P_OMSR8_Bits',0,2,206,2,3
	.word	4744
	.byte	27
	.byte	'Ifx_P_OMSR_Bits',0,2,228,2,3
	.word	5867
	.byte	27
	.byte	'Ifx_P_OUT_Bits',0,2,250,2,3
	.word	302
	.byte	27
	.byte	'Ifx_P_PCSR_Bits',0,2,135,3,3
	.word	4152
	.byte	27
	.byte	'Ifx_P_PDISC_Bits',0,2,157,3,3
	.word	3771
	.byte	27
	.byte	'Ifx_P_PDR0_Bits',0,2,178,3,3
	.word	2719
	.byte	27
	.byte	'Ifx_P_PDR1_Bits',0,2,199,3,3
	.word	3037
	.byte	27
	.byte	'Ifx_P_ACCEN0',0,2,212,3,3
	.word	6685
	.byte	27
	.byte	'Ifx_P_ACCEN1',0,2,220,3,3
	.word	6581
	.byte	27
	.byte	'Ifx_P_ESR',0,2,228,3,3
	.word	3369
	.byte	27
	.byte	'Ifx_P_ID',0,2,236,3,3
	.word	1226
	.byte	27
	.byte	'Ifx_P_IN',0,2,244,3,3
	.word	2335
	.byte	27
	.byte	'Ifx_P_IOCR0',0,2,252,3,3
	.word	1386
	.byte	27
	.byte	'Ifx_P_IOCR12',0,2,132,4,3
	.word	2078
	.byte	27
	.byte	'Ifx_P_IOCR4',0,2,140,4,3
	.word	1616
	.byte	27
	.byte	'Ifx_P_IOCR8',0,2,148,4,3
	.word	1846
	.byte	27
	.byte	'Ifx_P_OMCR',0,2,156,4,3
	.word	6187
	.byte	27
	.byte	'Ifx_P_OMCR0',0,2,164,4,3
	.word	5094
	.byte	27
	.byte	'Ifx_P_OMCR12',0,2,172,4,3
	.word	5666
	.byte	27
	.byte	'Ifx_P_OMCR4',0,2,180,4,3
	.word	5284
	.byte	27
	.byte	'Ifx_P_OMCR8',0,2,188,4,3
	.word	5474
	.byte	27
	.byte	'Ifx_P_OMR',0,2,196,4,3
	.word	642
	.byte	27
	.byte	'Ifx_P_OMSR',0,2,204,4,3
	.word	5839
	.byte	27
	.byte	'Ifx_P_OMSR0',0,2,212,4,3
	.word	4370
	.byte	27
	.byte	'Ifx_P_OMSR12',0,2,220,4,3
	.word	4903
	.byte	27
	.byte	'Ifx_P_OMSR4',0,2,228,4,3
	.word	4532
	.byte	27
	.byte	'Ifx_P_OMSR8',0,2,236,4,3
	.word	4716
	.byte	27
	.byte	'Ifx_P_OUT',0,2,244,4,3
	.word	251
	.byte	27
	.byte	'Ifx_P_PCSR',0,2,252,4,3
	.word	4124
	.byte	27
	.byte	'Ifx_P_PDISC',0,2,132,5,3
	.word	3743
	.byte	27
	.byte	'Ifx_P_PDR0',0,2,140,5,3
	.word	2691
	.byte	27
	.byte	'Ifx_P_PDR1',0,2,148,5,3
	.word	3009
	.byte	12
	.word	237
	.byte	27
	.byte	'Ifx_P',0,2,193,5,3
	.word	10965
	.byte	27
	.byte	'uint8',0,5,90,29
	.word	324
	.byte	27
	.byte	'uint16',0,5,92,29
	.word	571
	.byte	27
	.byte	'uint32',0,5,94,29
	.word	7302
	.byte	27
	.byte	'unsigned_int',0,6,121,22
	.word	257
	.byte	27
	.byte	'Port_n_PinType',0,4,203,2,3
	.word	8432
	.byte	27
	.byte	'Port_n_ControlType',0,4,233,2,2
	.word	8134
	.byte	27
	.byte	'Port_n_ConfigType',0,4,164,3,2
	.word	8110
	.byte	27
	.byte	'Port_n_PCSRConfigType',0,4,167,3,16
	.word	7302
	.byte	27
	.byte	'Port_ConfigType',0,4,195,3,2
	.word	8088
	.byte	10,84
	.word	571
	.byte	11,41,0
.L145:
	.byte	20
	.word	11185
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,1,3,8,32,13,58,15,59,15,57,15,54,15,39,12,0,0,3,11
	.byte	1,0,0,4,11,0,0,0,5,19,1,3,8,58,15,59,15,57,15,11,15,0,0,6,23,1,58,15,59,15,57,15,11,15,0,0,7,36,0,3,8
	.byte	11,15,62,15,0,0,8,13,0,3,8,11,15,73,19,56,9,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,1,1,11
	.byte	15,73,19,0,0,11,33,0,47,15,0,0,12,53,0,73,19,0,0,13,15,0,73,19,0,0,14,46,1,3,8,32,13,58,15,59,15,57,15
	.byte	73,19,54,15,39,12,0,0,15,5,0,3,8,58,15,59,15,57,15,73,19,0,0,16,46,0,3,8,58,15,59,15,57,15,54,15,39,12
	.byte	63,12,60,12,0,0,17,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,18,23,1,3,8,58,15,59,15,57,15
	.byte	11,15,0,0,19,19,1,58,15,59,15,57,15,11,15,0,0,20,38,0,73,19,0,0,21,46,1,49,19,0,0,22,29,1,49,19,0,0,23
	.byte	5,0,49,19,0,0,24,11,0,49,19,0,0,25,11,1,49,19,0,0,26,59,0,3,8,0,0,27,22,0,3,8,58,15,59,15,57,15,73,19
	.byte	0,0,28,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L42:
	.word	.L161-.L160
.L160:
	.half	3
	.word	.L163-.L162
.L162:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Port.c',0,0,0,0
	.byte	'..\\mcal_src\\IfxPort_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_WdgLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Port.h',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_TcLib.h',0,0,0,0,0
.L163:
.L161:
	.sdecl	'.debug_info',debug,cluster('Port_Init')
	.sect	'.debug_info'
.L43:
	.word	957
	.half	3
	.word	.L44
	.byte	4,1
	.byte	'..\\mcal_src\\Port.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L46,.L45
	.byte	2
	.word	.L39
	.byte	3
	.byte	'Port_Init',0,1,223,3,6,1,1,1
	.word	.L32,.L67,.L31
	.byte	4
	.byte	'ConfigPtr',0,1,223,3,42
	.word	.L68,.L69
	.byte	5
	.word	.L32,.L67
	.byte	6
	.word	.L70,.L71,.L72
	.byte	7
	.word	.L73,.L74
	.byte	8
	.byte	'DataPtr',0,1,156,13,28
	.word	.L78,.L79
	.byte	8
	.byte	'ConfigDataPtr',0,1,157,13,28
	.word	.L80,.L81
	.byte	8
	.byte	'PortNumber',0,1,159,13,28
	.word	.L82,.L83
	.byte	8
	.byte	'ConfigIndex',0,1,164,13,28
	.word	.L82,.L84
	.byte	8
	.byte	'PCSRDataPtr',0,1,166,13,28
	.word	.L78,.L85
	.byte	6
	.word	.L86,.L71,.L72
	.byte	7
	.word	.L87,.L88
	.byte	8
	.byte	'PortNumber',0,1,166,14,27
	.word	.L82,.L90
	.byte	8
	.byte	'ConfigIndex',0,1,169,14,27
	.word	.L82,.L91
	.byte	6
	.word	.L92,.L2,.L4
	.byte	9
	.word	.L93,.L94
	.byte	10
	.word	.L95,.L96
	.byte	0,6
	.word	.L99,.L4,.L97
	.byte	9
	.word	.L100,.L101
	.byte	11
	.word	.L102,.L4,.L97
	.byte	0,6
	.word	.L92,.L97,.L98
	.byte	9
	.word	.L93,.L94
	.byte	0,6
	.word	.L103,.L104,.L105
	.byte	9
	.word	.L106,.L107
	.byte	11
	.word	.L108,.L104,.L105
	.byte	0,0,0,12
	.word	.L86,.L75,.L76
	.byte	12
	.word	.L86,.L77,.L89
	.byte	6
	.word	.L99,.L10,.L109
	.byte	9
	.word	.L100,.L101
	.byte	10
	.word	.L102,.L110
	.byte	0,6
	.word	.L103,.L111,.L112
	.byte	9
	.word	.L106,.L107
	.byte	10
	.word	.L108,.L113
	.byte	0,6
	.word	.L92,.L112,.L114
	.byte	9
	.word	.L93,.L94
	.byte	11
	.word	.L95,.L112,.L114
	.byte	0,6
	.word	.L115,.L16,.L116
	.byte	9
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L120
	.byte	11
	.word	.L121,.L16,.L116
	.byte	0,6
	.word	.L115,.L17,.L122
	.byte	9
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L120
	.byte	11
	.word	.L121,.L17,.L122
	.byte	0,6
	.word	.L115,.L18,.L123
	.byte	9
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L120
	.byte	11
	.word	.L121,.L18,.L123
	.byte	0,6
	.word	.L115,.L19,.L124
	.byte	9
	.word	.L117,.L118
	.byte	9
	.word	.L119,.L120
	.byte	11
	.word	.L121,.L19,.L124
	.byte	0,6
	.word	.L125,.L20,.L126
	.byte	9
	.word	.L127,.L128
	.byte	11
	.word	.L129,.L20,.L126
	.byte	0,0,0,12
	.word	.L70,.L75,.L76
	.byte	12
	.word	.L70,.L77,.L67
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Port_Init')
	.sect	'.debug_abbrev'
.L44:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,11,1,49,16,85,6,0,0,8,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,9,5,0,49,16,2,6,0,0
	.byte	10,11,0,49,16,85,6,0,0,11,11,0,49,16,17,1,18,1,0,0,12,29,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Port_Init')
	.sect	'.debug_line'
.L45:
	.word	.L165-.L164
.L164:
	.half	3
	.word	.L167-.L166
.L166:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Port.c',0,0,0,0,0
.L167:
	.byte	5,7,7,0,5,2
	.word	.L32
	.byte	3,166,4,1,5,15,9
	.half	.L71-.L32
	.byte	3,134,10,1,5,7,3,250,117,1,5,19,9
	.half	.L148-.L71
	.byte	3,138,10,1,5,6,3,174,117,1,5,51,9
	.half	.L146-.L148
	.byte	3,157,13,1,5,7,9
	.half	.L72-.L146
	.byte	3,171,115,1,5,51,9
	.half	.L75-.L72
	.byte	3,213,12,1,5,23,9
	.half	.L76-.L75
	.byte	3,171,115,1,5,68,9
	.half	.L77-.L76
	.byte	3,138,10,1,5,54,9
	.half	.L168-.L77
	.byte	1,5,22,9
	.half	.L2-.L168
	.byte	3,135,2,1,5,13,9
	.half	.L169-.L2
	.byte	1,5,38,7,9
	.half	.L170-.L169
	.byte	1,5,71,1,5,15,9
	.half	.L3-.L170
	.byte	3,94,1,5,53,9
	.half	.L171-.L3
	.byte	1,5,44,9
	.half	.L172-.L171
	.byte	1,5,15,9
	.half	.L173-.L172
	.byte	3,1,1,5,72,9
	.half	.L174-.L173
	.byte	3,127,1,5,21,9
	.half	.L4-.L174
	.byte	3,188,127,1,5,13,9
	.half	.L175-.L4
	.byte	1,5,15,7,9
	.half	.L176-.L175
	.byte	3,93,1,5,53,9
	.half	.L177-.L176
	.byte	1,5,44,9
	.half	.L178-.L177
	.byte	1,5,15,9
	.half	.L179-.L178
	.byte	3,1,1,5,72,9
	.half	.L180-.L179
	.byte	3,127,1,5,71,3,35,1,5,15,9
	.half	.L5-.L180
	.byte	3,186,127,1,5,44,9
	.half	.L181-.L5
	.byte	1,5,15,3,1,1,5,55,9
	.half	.L182-.L181
	.byte	3,127,1,5,9,9
	.half	.L6-.L182
	.byte	3,170,127,1,5,68,7,9
	.half	.L97-.L6
	.byte	1,5,7,7,9
	.half	.L98-.L97
	.byte	3,2,1,5,15,9
	.half	.L104-.L98
	.byte	3,48,1,5,41,9
	.half	.L183-.L104
	.byte	1,5,7,9
	.half	.L105-.L183
	.byte	3,84,1,5,22,9
	.half	.L184-.L105
	.byte	3,192,2,1,5,7,9
	.half	.L185-.L184
	.byte	3,192,125,1,5,70,9
	.half	.L186-.L185
	.byte	3,192,2,1,5,48,9
	.half	.L187-.L186
	.byte	1,5,7,9
	.half	.L188-.L187
	.byte	3,195,125,1,5,9,7,9
	.half	.L189-.L188
	.byte	3,5,1,5,7,9
	.half	.L9-.L189
	.byte	3,3,1,5,18,9
	.half	.L190-.L9
	.byte	3,1,1,5,66,9
	.half	.L7-.L190
	.byte	3,105,1,5,54,1,5,33,7,9
	.half	.L89-.L7
	.byte	3,255,126,1,5,15,9
	.half	.L191-.L89
	.byte	3,124,1,5,55,3,7,1,5,19,9
	.half	.L149-.L191
	.byte	1,5,33,9
	.half	.L150-.L149
	.byte	3,125,1,5,48,9
	.half	.L192-.L150
	.byte	1,5,21,9
	.half	.L10-.L192
	.byte	3,162,2,1,5,13,9
	.half	.L193-.L10
	.byte	1,5,15,7,9
	.half	.L194-.L193
	.byte	3,93,1,5,53,9
	.half	.L195-.L194
	.byte	1,5,44,9
	.half	.L196-.L195
	.byte	1,5,15,9
	.half	.L197-.L196
	.byte	3,1,1,5,72,9
	.half	.L198-.L197
	.byte	3,127,1,5,71,3,35,1,5,15,9
	.half	.L11-.L198
	.byte	3,186,127,1,5,44,9
	.half	.L199-.L11
	.byte	1,5,15,3,1,1,5,55,9
	.half	.L151-.L199
	.byte	3,127,1,5,5,9
	.half	.L12-.L151
	.byte	3,170,126,1,5,24,7,9
	.half	.L109-.L12
	.byte	3,4,1,5,59,9
	.half	.L200-.L109
	.byte	1,5,24,9
	.half	.L201-.L200
	.byte	1,5,39,9
	.half	.L202-.L201
	.byte	1,5,59,9
	.half	.L203-.L202
	.byte	1,5,15,9
	.half	.L111-.L203
	.byte	3,174,1,1,5,41,9
	.half	.L152-.L111
	.byte	1,5,22,9
	.half	.L112-.L152
	.byte	3,208,1,1,5,13,9
	.half	.L204-.L112
	.byte	1,5,38,7,9
	.half	.L205-.L204
	.byte	1,5,71,1,5,15,9
	.half	.L14-.L205
	.byte	3,94,1,5,53,9
	.half	.L206-.L14
	.byte	1,5,44,9
	.half	.L207-.L206
	.byte	1,5,15,9
	.half	.L208-.L207
	.byte	3,1,1,5,72,9
	.half	.L209-.L208
	.byte	3,127,1,5,7,9
	.half	.L15-.L209
	.byte	3,175,125,1,5,22,7,9
	.half	.L114-.L15
	.byte	3,4,1,5,16,9
	.half	.L155-.L114
	.byte	3,183,4,1,5,26,9
	.half	.L210-.L155
	.byte	1,5,51,9
	.half	.L211-.L210
	.byte	1,5,77,9
	.half	.L212-.L211
	.byte	1,5,9,3,202,123,1,5,60,9
	.half	.L16-.L212
	.byte	3,210,3,1,5,38,9
	.half	.L213-.L16
	.byte	1,5,7,9
	.half	.L116-.L213
	.byte	3,178,124,1,5,9,7,9
	.half	.L214-.L116
	.byte	3,2,1,5,38,9
	.half	.L17-.L214
	.byte	3,204,3,1,5,14,3,184,124,1,5,7,9
	.half	.L122-.L17
	.byte	3,2,1,5,9,7,9
	.half	.L215-.L122
	.byte	3,2,1,5,23,9
	.half	.L18-.L215
	.byte	3,196,3,1,5,14,3,192,124,1,5,38,9
	.half	.L216-.L18
	.byte	3,192,3,1,5,7,9
	.half	.L123-.L216
	.byte	3,194,124,1,5,9,7,9
	.half	.L217-.L123
	.byte	3,2,1,5,23,9
	.half	.L19-.L217
	.byte	3,188,3,1,5,14,3,200,124,1,5,38,9
	.half	.L218-.L19
	.byte	3,184,3,1,5,7,9
	.half	.L124-.L218
	.byte	3,202,124,1,5,9,7,9
	.half	.L219-.L124
	.byte	3,2,1,5,21,9
	.half	.L20-.L219
	.byte	3,253,4,1,5,13,9
	.half	.L220-.L20
	.byte	1,5,15,7,9
	.half	.L221-.L220
	.byte	3,95,1,5,53,9
	.half	.L222-.L221
	.byte	1,5,44,9
	.half	.L223-.L222
	.byte	1,5,15,9
	.half	.L224-.L223
	.byte	3,1,1,5,72,9
	.half	.L225-.L224
	.byte	3,127,1,5,75,3,33,1,5,37,9
	.half	.L21-.L225
	.byte	1,5,7,9
	.half	.L22-.L21
	.byte	3,135,123,1,5,10,7,9
	.half	.L126-.L22
	.byte	3,6,1,9
	.half	.L153-.L126
	.byte	3,2,1,9
	.half	.L226-.L153
	.byte	3,1,1,5,18,9
	.half	.L23-.L226
	.byte	3,7,1,5,67,9
	.half	.L13-.L23
	.byte	3,183,127,1,5,55,1,5,3,7,9
	.half	.L227-.L13
	.byte	3,207,0,1,9
	.half	.L228-.L227
	.byte	3,4,1,9
	.half	.L229-.L228
	.byte	3,4,1,9
	.half	.L230-.L229
	.byte	3,3,1,5,1,7,9
	.half	.L47-.L230
	.byte	3,168,118,0,1,1
.L165:
	.sdecl	'.debug_ranges',debug,cluster('Port_Init')
	.sect	'.debug_ranges'
.L46:
	.word	-1,.L32,0,.L47-.L32,0,0
.L74:
	.word	-1,.L32,.L71-.L32,.L72-.L32,.L75-.L32,.L76-.L32,.L77-.L32,.L67-.L32,0,0
.L88:
	.word	-1,.L32,.L71-.L32,.L72-.L32,.L75-.L32,.L76-.L32,.L77-.L32,.L89-.L32,0,0
.L96:
	.word	-1,.L32,.L2-.L32,.L4-.L32,.L97-.L32,.L98-.L32,0,0
.L110:
	.word	-1,.L32,.L10-.L32,.L109-.L32,-1,.L34,0,.L62-.L34,0,0
.L113:
	.word	-1,.L32,.L111-.L32,.L112-.L32,-1,.L36,0,.L57-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('Port_RefreshPortDirection')
	.sect	'.debug_info'
.L48:
	.word	414
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'..\\mcal_src\\Port.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L51,.L50
	.byte	2
	.word	.L39
	.byte	3
	.byte	'Port_RefreshPortDirection',0,1,181,9,6,1,1,1
	.word	.L38,.L130,.L37
	.byte	4
	.word	.L38,.L130
	.byte	5
	.byte	'LoopCtr',0,1,183,9,25
	.word	.L82,.L131
	.byte	5
	.byte	'PortNumber',0,1,185,9,25
	.word	.L82,.L132
	.byte	5
	.byte	'ConfigIndex',0,1,186,9,25
	.word	.L82,.L133
	.byte	5
	.byte	'IocrDataPtr',0,1,193,9,26
	.word	.L134,.L135
	.byte	5
	.byte	'IocrRegPtr',0,1,194,9,26
	.word	.L136,.L137
	.byte	6
	.word	.L138
	.byte	5
	.byte	'val',0,1,143,10,13
	.word	.L82,.L142
	.byte	0,7
	.word	.L99,.L24,.L143
	.byte	8
	.word	.L100,.L144
	.byte	9
	.word	.L102,.L24,.L143
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Port_RefreshPortDirection')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,11,1
	.byte	85,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Port_RefreshPortDirection')
	.sect	'.debug_line'
.L50:
	.word	.L232-.L231
.L231:
	.half	3
	.word	.L234-.L233
.L233:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Port.c',0,0,0,0,0
.L234:
	.byte	5,15,7,0,5,2
	.word	.L38
	.byte	3,213,9,1,5,24,3,133,7,1,5,16,9
	.half	.L156-.L38
	.byte	3,252,120,1,5,20,3,9,1,5,13,9
	.half	.L139-.L156
	.byte	3,47,1,5,24,3,204,6,1,5,20,9
	.half	.L140-.L139
	.byte	3,133,121,1,5,43,9
	.half	.L235-.L140
	.byte	3,205,0,1,5,21,9
	.half	.L24-.L235
	.byte	3,165,5,1,5,13,9
	.half	.L236-.L24
	.byte	1,5,15,7,9
	.half	.L237-.L236
	.byte	3,93,1,5,53,9
	.half	.L238-.L237
	.byte	1,5,44,9
	.half	.L239-.L238
	.byte	1,5,15,9
	.half	.L240-.L239
	.byte	3,1,1,5,72,9
	.half	.L241-.L240
	.byte	3,127,1,5,71,3,35,1,5,15,9
	.half	.L25-.L241
	.byte	3,186,127,1,5,44,9
	.half	.L242-.L25
	.byte	1,5,15,3,1,1,5,55,9
	.half	.L243-.L242
	.byte	3,127,1,5,6,9
	.half	.L26-.L243
	.byte	3,206,122,1,5,55,7,9
	.half	.L143-.L26
	.byte	3,6,1,5,35,1,5,15,9
	.half	.L244-.L143
	.byte	3,136,5,1,5,55,9
	.half	.L245-.L244
	.byte	3,248,122,1,5,29,9
	.half	.L157-.L245
	.byte	3,138,5,1,5,27,9
	.half	.L246-.L157
	.byte	1,5,16,9
	.half	.L247-.L246
	.byte	3,147,123,1,5,29,9
	.half	.L158-.L247
	.byte	3,236,4,1,5,69,9
	.half	.L248-.L158
	.byte	1,5,41,9
	.half	.L249-.L248
	.byte	3,127,1,5,72,9
	.half	.L250-.L249
	.byte	3,141,123,1,5,23,9
	.half	.L28-.L250
	.byte	3,229,6,1,5,50,9
	.half	.L251-.L28
	.byte	1,5,43,9
	.half	.L252-.L251
	.byte	3,1,1,5,60,9
	.half	.L253-.L252
	.byte	3,127,1,5,9,9
	.half	.L254-.L253
	.byte	3,166,121,1,5,13,7,9
	.half	.L141-.L254
	.byte	3,15,1,5,16,9
	.half	.L29-.L141
	.byte	3,7,1,5,21,9
	.half	.L255-.L29
	.byte	3,2,1,5,9,9
	.half	.L256-.L255
	.byte	1,5,21,7,9
	.half	.L257-.L256
	.byte	3,5,1,5,28,9
	.half	.L30-.L257
	.byte	3,11,1,5,20,3,121,1,5,49,9
	.half	.L258-.L30
	.byte	3,7,1,5,18,7,9
	.half	.L259-.L258
	.byte	3,2,1,5,15,9
	.half	.L27-.L259
	.byte	3,2,1,5,43,3,1,1,5,1,7,9
	.half	.L260-.L27
	.byte	3,2,1,7,9
	.half	.L52-.L260
	.byte	0,1,1
.L232:
	.sdecl	'.debug_ranges',debug,cluster('Port_RefreshPortDirection')
	.sect	'.debug_ranges'
.L51:
	.word	-1,.L38,0,.L52-.L38,0,0
.L138:
	.word	-1,.L38,.L139-.L38,.L140-.L38,.L141-.L38,.L29-.L38,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L53:
	.word	208
	.half	3
	.word	.L54
	.byte	4,1
	.byte	'..\\mcal_src\\Port.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L56,.L55
	.byte	2
	.word	.L39
	.byte	3
	.byte	'.cocofun_1',0,1,223,3,6,1
	.word	.L36,.L57,.L35
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L54:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L55:
	.word	.L262-.L261
.L261:
	.half	3
	.word	.L264-.L263
.L263:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Port.c',0,0,0,0,0
.L264:
	.byte	5,29,7,0,5,2
	.word	.L36
	.byte	3,233,14,1,5,15,3,126,1,5,27,9
	.half	.L265-.L36
	.byte	3,2,1,5,15,3,126,1,5,29,9
	.half	.L266-.L265
	.byte	3,1,1,5,69,9
	.half	.L267-.L266
	.byte	1,9
	.half	.L57-.L267
	.byte	0,1,1,5,29,0,5,2
	.word	.L36
	.byte	3,233,14,1,5,15,3,126,1,5,27,9
	.half	.L265-.L36
	.byte	3,2,1,5,15,3,126,1,5,29,9
	.half	.L266-.L265
	.byte	3,1,1,5,69,9
	.half	.L267-.L266
	.byte	1,9
	.half	.L57-.L267
	.byte	0,1,1
.L262:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L56:
	.word	-1,.L36,0,.L57-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L58:
	.word	208
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'..\\mcal_src\\Port.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L61,.L60
	.byte	2
	.word	.L39
	.byte	3
	.byte	'.cocofun_2',0,1,223,3,6,1
	.word	.L34,.L62,.L33
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L60:
	.word	.L269-.L268
.L268:
	.half	3
	.word	.L271-.L270
.L270:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Port.c',0,0,0,0,0
.L271:
	.byte	5,15,7,0,5,2
	.word	.L34
	.byte	3,140,15,1,9
	.half	.L62-.L34
	.byte	0,1,1,5,15,0,5,2
	.word	.L34
	.byte	3,140,15,1,9
	.half	.L62-.L34
	.byte	0,1,1,5,15,0,5,2
	.word	.L34
	.byte	3,140,15,1,9
	.half	.L62-.L34
	.byte	0,1,1
.L269:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L61:
	.word	-1,.L34,0,.L62-.L34,0,0
	.sdecl	'.debug_info',debug,cluster('Port_kAvailablePins')
	.sect	'.debug_info'
.L63:
	.word	206
	.half	3
	.word	.L64
	.byte	4,1
	.byte	'..\\mcal_src\\Port.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L39
	.byte	3
	.byte	'Port_kAvailablePins',0,1,197,2,21
	.word	.L145
	.byte	5,3
	.word	Port_kAvailablePins
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Port_kAvailablePins')
	.sect	'.debug_abbrev'
.L64:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Port_kConfigPtr')
	.sect	'.debug_info'
.L65:
	.word	202
	.half	3
	.word	.L66
	.byte	4,1
	.byte	'..\\mcal_src\\Port.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L39
	.byte	3
	.byte	'Port_kConfigPtr',0,1,129,3,33
	.word	.L68
	.byte	5,3
	.word	Port_kConfigPtr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Port_kConfigPtr')
	.sect	'.debug_abbrev'
.L66:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L35:
	.word	-1,.L36,0,.L57-.L36
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L33:
	.word	-1,.L34,0,.L62-.L34
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Port_Init')
	.sect	'.debug_loc'
.L81:
	.word	-1,.L32,.L111-.L32,.L152-.L32
	.half	1
	.byte	98
	.word	.L36-.L32,.L57-.L32
	.half	1
	.byte	98
	.word	.L112-.L32,.L153-.L32
	.half	1
	.byte	98
	.word	0,0
.L91:
	.word	-1,.L32,.L147-.L32,.L62-.L32
	.half	5
	.byte	144,36,157,32,0
	.word	.L36-.L32,.L57-.L32
	.half	5
	.byte	144,36,157,32,0
	.word	.L148-.L32,.L149-.L32
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L84:
	.word	-1,.L32,.L149-.L32,.L151-.L32
	.half	5
	.byte	144,36,157,32,0
	.word	.L147-.L32,.L62-.L32
	.half	5
	.byte	144,36,157,32,0
	.word	.L12-.L32,.L152-.L32
	.half	5
	.byte	144,36,157,32,0
	.word	.L36-.L32,.L57-.L32
	.half	5
	.byte	144,36,157,32,0
	.word	.L112-.L32,.L67-.L32
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L69:
	.word	-1,.L32,0,.L2-.L32
	.half	1
	.byte	100
	.word	0,0
.L79:
	.word	0,0
.L154:
	.word	-1,.L32,.L155-.L32,.L16-.L32
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L85:
	.word	-1,.L32,.L10-.L32,.L151-.L32
	.half	1
	.byte	109
	.word	.L147-.L32,.L62-.L32
	.half	1
	.byte	109
	.word	.L12-.L32,.L152-.L32
	.half	1
	.byte	109
	.word	.L36-.L32,.L57-.L32
	.half	1
	.byte	109
	.word	.L112-.L32,.L67-.L32
	.half	1
	.byte	109
	.word	0,0
.L120:
	.word	0,0
.L94:
	.word	0,0
.L118:
	.word	0,0
.L128:
	.word	0,0
.L101:
	.word	0,0
.L107:
	.word	0,0
.L83:
	.word	-1,.L32,.L150-.L32,.L151-.L32
	.half	5
	.byte	144,36,157,32,32
	.word	.L147-.L32,.L62-.L32
	.half	5
	.byte	144,36,157,32,32
	.word	.L12-.L32,.L152-.L32
	.half	5
	.byte	144,36,157,32,32
	.word	.L36-.L32,.L57-.L32
	.half	5
	.byte	144,36,157,32,32
	.word	.L112-.L32,.L67-.L32
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L90:
	.word	-1,.L32,.L147-.L32,.L62-.L32
	.half	5
	.byte	144,36,157,32,32
	.word	.L36-.L32,.L57-.L32
	.half	5
	.byte	144,36,157,32,32
	.word	.L146-.L32,.L150-.L32
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L31:
	.word	-1,.L32,0,.L146-.L32
	.half	2
	.byte	138,0
	.word	.L146-.L32,.L67-.L32
	.half	2
	.byte	138,8
	.word	.L67-.L32,.L67-.L32
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Port_RefreshPortDirection')
	.sect	'.debug_loc'
.L133:
	.word	-1,.L38,.L147-.L38,.L62-.L38
	.half	5
	.byte	144,33,157,32,32
	.word	.L139-.L38,.L130-.L38
	.half	5
	.byte	144,33,157,32,32
	.word	0,0
.L135:
	.word	-1,.L38,.L157-.L38,.L27-.L38
	.half	1
	.byte	101
	.word	0,0
.L137:
	.word	-1,.L38,.L28-.L38,.L27-.L38
	.half	1
	.byte	102
	.word	0,0
.L131:
	.word	-1,.L38,.L158-.L38,.L27-.L38
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L144:
	.word	0,0
.L132:
	.word	-1,.L38,.L147-.L38,.L62-.L38
	.half	5
	.byte	144,33,157,32,0
	.word	.L156-.L38,.L130-.L38
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L37:
	.word	-1,.L38,0,.L130-.L38
	.half	2
	.byte	138,0
	.word	0,0
.L142:
	.word	-1,.L38,.L159-.L38,.L29-.L38
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L272:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Port_Init')
	.sect	'.debug_frame'
	.word	36
	.word	.L272,.L32,.L67-.L32
	.byte	4
	.word	(.L146-.L32)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L67-.L146)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Port_RefreshPortDirection')
	.sect	'.debug_frame'
	.word	16
	.word	.L272,.L38,.L130-.L38
	.byte	8,19,8,23
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L273:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L273,.L34,.L62-.L34
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L273,.L36,.L57-.L36
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Port.c	  2406              (Port_lIsPortPCSRAvailable31(Port))
; ..\mcal_src\Port.c	  2407             );
; ..\mcal_src\Port.c	  2408    return(RetVal);
; ..\mcal_src\Port.c	  2409  }
; ..\mcal_src\Port.c	  2410  /*Memory Map of the PORT Code*/
; ..\mcal_src\Port.c	  2411  #define PORT_STOP_SEC_CODE
; ..\mcal_src\Port.c	  2412  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Port.c	  2413   allowed only for MemMap.h*/
; ..\mcal_src\Port.c	  2414  #include "MemMap.h"

	; Module end
