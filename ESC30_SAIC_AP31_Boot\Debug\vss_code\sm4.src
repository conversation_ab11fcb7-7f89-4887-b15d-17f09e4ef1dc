	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc28128a -c99 --dep-file=vss_code\\.sm4.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=vss_code\\sm4.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o vss_code\\sm4.src ..\\vss_code\\sm4.c"
	.compiler_name		"ctc"
	.name	"sm4"

	
$TC16X
	
	.sdecl	'.text.vss_api_code',code,cluster('GET_ULONG_BE')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GET_ULONG_BE

; ..\vss_code\sm4.c	     1  #include "sm4.h"
; ..\vss_code\sm4.c	     2  #include "vsscommon.h"
; ..\vss_code\sm4.c	     3  
; ..\vss_code\sm4.c	     4  
; ..\vss_code\sm4.c	     5  #pragma section code "vss_api_code" 
; ..\vss_code\sm4.c	     6  
; ..\vss_code\sm4.c	     7  extern const vss_uint8 sm4_SboxTable[16][16];
; ..\vss_code\sm4.c	     8  /* System parameter */
; ..\vss_code\sm4.c	     9  extern const vss_ulong sm4_FK[4];
; ..\vss_code\sm4.c	    10  /* fixed parameter */
; ..\vss_code\sm4.c	    11  extern const vss_ulong sm4_CK[32];
; ..\vss_code\sm4.c	    12  
; ..\vss_code\sm4.c	    13  /*
; ..\vss_code\sm4.c	    14   * 32-bit integer manipulation macros (big endian)
; ..\vss_code\sm4.c	    15   */
; ..\vss_code\sm4.c	    16  vss_ulong GET_ULONG_BE(vss_uint8* b, int i)
; Function GET_ULONG_BE
.L19:
GET_ULONG_BE:	.type	func

; ..\vss_code\sm4.c	    17  {	
; ..\vss_code\sm4.c	    18  	vss_ulong n;
; ..\vss_code\sm4.c	    19  
; ..\vss_code\sm4.c	    20  	n = ((vss_ulong)*(b+i) << 24 ) | 
; ..\vss_code\sm4.c	    21  	((vss_ulong)*(b+i+1) << 16 ) | 
; ..\vss_code\sm4.c	    22  	((vss_ulong)*(b+i+2) << 8 )| 
; ..\vss_code\sm4.c	    23  	((vss_ulong)*(b+i+3));	
	addsc.a	a15,a4,d4,#0
.L270:
	ld.bu	d15,[a15]
.L271:
	sh	d0,d15,#24
	ld.bu	d15,[a15]1
.L272:
	sh	d15,d15,#16
.L273:
	or	d0,d15
	ld.bu	d15,[a15]2
.L274:
	sh	d15,d15,#8
.L275:
	or	d0,d15
	ld.bu	d15,[a15]3
.L276:

; ..\vss_code\sm4.c	    24  	return n;
; ..\vss_code\sm4.c	    25  }
	or	d2,d0,d15
	ret
.L120:
	
__GET_ULONG_BE_function_end:
	.size	GET_ULONG_BE,__GET_ULONG_BE_function_end-GET_ULONG_BE
.L57:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('PUT_ULONG_BE')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	PUT_ULONG_BE

; ..\vss_code\sm4.c	    26  
; ..\vss_code\sm4.c	    27  void PUT_ULONG_BE(vss_ulong n, vss_uint8* b, int i)                         
; Function PUT_ULONG_BE
.L21:
PUT_ULONG_BE:	.type	func

; ..\vss_code\sm4.c	    28  {
; ..\vss_code\sm4.c	    29  	b[i]       = (vss_uint8) ( (n) >> 24 );
	addsc.a	a15,a4,d5,#0
.L260:
	sh	d15,d4,#-24
.L261:
	st.b	[a15],d15
.L262:

; ..\vss_code\sm4.c	    30  	b[i + 1] = (vss_uint8) ( (n) >> 16 );
	sh	d15,d4,#-16
	st.b	[a15]1,d15
.L263:

; ..\vss_code\sm4.c	    31  	b[i + 2] = (vss_uint8) ( (n) >>  8 );
	sh	d15,d4,#-8
	st.b	[a15]2,d15
.L264:

; ..\vss_code\sm4.c	    32  	b[i + 3] = (vss_uint8) ( (n)       );
	st.b	[a15]3,d4
.L265:

; ..\vss_code\sm4.c	    33  }
	ret
.L113:
	
__PUT_ULONG_BE_function_end:
	.size	PUT_ULONG_BE,__PUT_ULONG_BE_function_end-PUT_ULONG_BE
.L52:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SWAP')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SWAP

; ..\vss_code\sm4.c	    34  
; ..\vss_code\sm4.c	    35  /*
; ..\vss_code\sm4.c	    36   *rotate shift left marco definition
; ..\vss_code\sm4.c	    37   *
; ..\vss_code\sm4.c	    38   */
; ..\vss_code\sm4.c	    39  #define  SHL(x,n) (((x) & 0xFFFFFFFFUL) << n)
; ..\vss_code\sm4.c	    40  #define ROTL(x,n) (SHL((x),n) | ((x) >> (32 - n)))
; ..\vss_code\sm4.c	    41  
; ..\vss_code\sm4.c	    42  void SWAP(vss_ulong* a, vss_ulong* b) 
; Function SWAP
.L23:
SWAP:	.type	func

; ..\vss_code\sm4.c	    43  { 
; ..\vss_code\sm4.c	    44  	vss_ulong t = *a;
	ld.w	d15,[a4]
.L184:

; ..\vss_code\sm4.c	    45  	*a = *b; 
	ld.w	d0,[a5]
.L281:
	st.w	[a4],d0
.L282:

; ..\vss_code\sm4.c	    46  	*b = t;
	st.w	[a5],d15
.L283:

; ..\vss_code\sm4.c	    47  }
	ret
.L123:
	
__SWAP_function_end:
	.size	SWAP,__SWAP_function_end-SWAP
.L62:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4Sbox')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm4.c	    48  
; ..\vss_code\sm4.c	    49  /*
; ..\vss_code\sm4.c	    50   * Expanded SM4 S-boxes
; ..\vss_code\sm4.c	    51    Sbox table: 8bits input convert to 8 bits output*/
; ..\vss_code\sm4.c	    52   
; ..\vss_code\sm4.c	    53  
; ..\vss_code\sm4.c	    54  
; ..\vss_code\sm4.c	    55  
; ..\vss_code\sm4.c	    56  /*
; ..\vss_code\sm4.c	    57   * private function:
; ..\vss_code\sm4.c	    58   * look up in sm4_SboxTable and get the related value.
; ..\vss_code\sm4.c	    59   * args:    [in] inch: 0x00~0xFF (8 bits unsigned value).
; ..\vss_code\sm4.c	    60   */
; ..\vss_code\sm4.c	    61  static vss_uint8 sm4Sbox(vss_uint8 inch)
; Function sm4Sbox
.L25:
sm4Sbox:	.type	func

; ..\vss_code\sm4.c	    62  {
; ..\vss_code\sm4.c	    63      const vss_uint8 *pTable = (const vss_uint8 *)sm4_SboxTable;
	movh.a	a15,#@his(sm4_SboxTable)
	lea	a15,[a15]@los(sm4_SboxTable)
.L312:

; ..\vss_code\sm4.c	    64      vss_uint8 retVal = pTable[inch];
	addsc.a	a15,a15,d4,#0
	ld.bu	d2,[a15]
.L313:

; ..\vss_code\sm4.c	    65      
; ..\vss_code\sm4.c	    66      return retVal;
; ..\vss_code\sm4.c	    67  }
	ret
.L151:
	
__sm4Sbox_function_end:
	.size	sm4Sbox,__sm4Sbox_function_end-sm4Sbox
.L87:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4Lt')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm4.c	    68  
; ..\vss_code\sm4.c	    69  /*
; ..\vss_code\sm4.c	    70   * private F(Lt) function:
; ..\vss_code\sm4.c	    71   * "T algorithm" == "L algorithm" + "t algorithm".
; ..\vss_code\sm4.c	    72   * args:    [in] a: a is a 32 bits unsigned value;
; ..\vss_code\sm4.c	    73   * return: c: c is calculated with line algorithm "L" and nonline algorithm "t"
; ..\vss_code\sm4.c	    74   */
; ..\vss_code\sm4.c	    75  static vss_ulong sm4Lt(vss_ulong ka)
; Function sm4Lt
.L27:
sm4Lt:	.type	func
	sub.a	a10,#8
.L185:

; ..\vss_code\sm4.c	    76  {
; ..\vss_code\sm4.c	    77  	vss_ulong bb = 0;
; ..\vss_code\sm4.c	    78  	vss_ulong c = 0;
; ..\vss_code\sm4.c	    79  	vss_uint8 a[4];
; ..\vss_code\sm4.c	    80  	vss_uint8 b[4];
; ..\vss_code\sm4.c	    81  	
; ..\vss_code\sm4.c	    82  	PUT_ULONG_BE(ka,a,0);
	mov.aa	a4,a10
.L318:
	mov	d5,#0
	call	PUT_ULONG_BE
.L186:

; ..\vss_code\sm4.c	    83  	b[0] = sm4Sbox(a[0]);
	ld.bu	d4,[a10]
	call	sm4Sbox
.L319:
	st.b	[a10]4,d2
.L320:

; ..\vss_code\sm4.c	    84  	b[1] = sm4Sbox(a[1]);
	ld.bu	d4,[a10]1
	call	sm4Sbox
.L321:
	st.b	[a10]5,d2
.L322:

; ..\vss_code\sm4.c	    85  	b[2] = sm4Sbox(a[2]);
	ld.bu	d4,[a10]2
	call	sm4Sbox
.L323:
	st.b	[a10]6,d2
.L324:

; ..\vss_code\sm4.c	    86  	b[3] = sm4Sbox(a[3]);
	ld.bu	d4,[a10]3
	call	sm4Sbox
.L325:
	st.b	[a10]7,d2
.L326:

; ..\vss_code\sm4.c	    87  	bb = GET_ULONG_BE(b,0);
	lea	a4,[a10]4
.L327:
	mov	d4,#0
	call	GET_ULONG_BE
.L187:

; ..\vss_code\sm4.c	    88  	c =bb^(ROTL(bb, 2))^(ROTL(bb, 10))^(ROTL(bb, 18))^(ROTL(bb, 24));
	sh	d15,d2,#2
	sh	d0,d2,#-30
	or	d15,d0
.L328:
	xor	d15,d2
.L329:
	sh	d0,d2,#10
	sh	d1,d2,#-22
	or	d0,d1
.L330:
	xor	d15,d0
.L331:
	sh	d0,d2,#18
	sh	d1,d2,#-14
	or	d0,d1
.L332:
	xor	d15,d0
.L333:
	sh	d0,d2,#24
	sh	d2,#-8
.L188:
	or	d0,d2
.L334:

; ..\vss_code\sm4.c	    89  	
; ..\vss_code\sm4.c	    90      return c;
; ..\vss_code\sm4.c	    91  }
	xor	d2,d15,d0
	ret
.L153:
	
__sm4Lt_function_end:
	.size	sm4Lt,__sm4Lt_function_end-sm4Lt
.L92:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4F')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm4.c	    92  
; ..\vss_code\sm4.c	    93  /*
; ..\vss_code\sm4.c	    94   * private F function:
; ..\vss_code\sm4.c	    95   * Calculating and getting encryption/decryption contents.
; ..\vss_code\sm4.c	    96   * args:    [in] x0: original contents;
; ..\vss_code\sm4.c	    97   * args:    [in] x1: original contents;
; ..\vss_code\sm4.c	    98   * args:    [in] x2: original contents;
; ..\vss_code\sm4.c	    99   * args:    [in] x3: original contents;
; ..\vss_code\sm4.c	   100   * args:    [in] rk: encryption/decryption key;
; ..\vss_code\sm4.c	   101   * return the contents of encryption/decryption contents.
; ..\vss_code\sm4.c	   102   */
; ..\vss_code\sm4.c	   103  static vss_ulong sm4F(vss_ulong x0, vss_ulong x1, vss_ulong x2, vss_ulong x3, vss_ulong rk)
; Function sm4F
.L29:
sm4F:	.type	func
	mov	d15,d4
	ld.w	d4,[a10]
.L189:

; ..\vss_code\sm4.c	   104  {
; ..\vss_code\sm4.c	   105      return (x0^sm4Lt(x1^x2^x3^rk));
	xor	d5,d6
.L190:
	xor	d5,d7
.L339:
	xor	d4,d5
	call	sm4Lt
.L191:

; ..\vss_code\sm4.c	   106  }
	xor	d2,d15
	ret
.L159:
	
__sm4F_function_end:
	.size	sm4F,__sm4F_function_end-sm4F
.L97:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4CalciRK')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm4.c	   107  
; ..\vss_code\sm4.c	   108  
; ..\vss_code\sm4.c	   109  /* private function:
; ..\vss_code\sm4.c	   110   * Calculating round encryption key.
; ..\vss_code\sm4.c	   111   * args:    [in] a: a is a 32 bits unsigned value;
; ..\vss_code\sm4.c	   112   * return: sk[i]: i{0,1,2,3,...31}.
; ..\vss_code\sm4.c	   113   */
; ..\vss_code\sm4.c	   114  static vss_ulong sm4CalciRK(vss_ulong ka)
; Function sm4CalciRK
.L31:
sm4CalciRK:	.type	func
	sub.a	a10,#8
.L192:

; ..\vss_code\sm4.c	   115  {
; ..\vss_code\sm4.c	   116  	vss_ulong bb = 0;
; ..\vss_code\sm4.c	   117  	vss_ulong rk = 0;
; ..\vss_code\sm4.c	   118  	vss_uint8 a[4];
; ..\vss_code\sm4.c	   119  	vss_uint8 b[4];
; ..\vss_code\sm4.c	   120  
; ..\vss_code\sm4.c	   121  	PUT_ULONG_BE(ka,a,0);
	mov.aa	a4,a10
.L344:
	mov	d5,#0
	call	PUT_ULONG_BE
.L193:

; ..\vss_code\sm4.c	   122  	b[0] = sm4Sbox(a[0]);
	ld.bu	d4,[a10]
	call	sm4Sbox
.L345:
	st.b	[a10]4,d2
.L346:

; ..\vss_code\sm4.c	   123  	b[1] = sm4Sbox(a[1]);
	ld.bu	d4,[a10]1
	call	sm4Sbox
.L347:
	st.b	[a10]5,d2
.L348:

; ..\vss_code\sm4.c	   124  	b[2] = sm4Sbox(a[2]);
	ld.bu	d4,[a10]2
	call	sm4Sbox
.L349:
	st.b	[a10]6,d2
.L350:

; ..\vss_code\sm4.c	   125  	b[3] = sm4Sbox(a[3]);
	ld.bu	d4,[a10]3
	call	sm4Sbox
.L351:
	st.b	[a10]7,d2
.L352:

; ..\vss_code\sm4.c	   126  	bb = GET_ULONG_BE(b,0);
	lea	a4,[a10]4
.L353:
	mov	d4,#0
	call	GET_ULONG_BE
.L194:

; ..\vss_code\sm4.c	   127  	rk = bb^(ROTL(bb, 13))^(ROTL(bb, 23));
	sh	d15,d2,#13
	sh	d0,d2,#-19
	or	d15,d0
.L354:
	xor	d15,d2
.L355:
	sh	d0,d2,#23
	sh	d1,d2,#-9
	or	d0,d1
.L356:

; ..\vss_code\sm4.c	   128  	return rk;
; ..\vss_code\sm4.c	   129  }
	xor	d2,d15,d0
	ret
.L165:
	
__sm4CalciRK_function_end:
	.size	sm4CalciRK,__sm4CalciRK_function_end-sm4CalciRK
.L102:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4_setkey')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm4.c	   130  
; ..\vss_code\sm4.c	   131  static void sm4_setkey( vss_ulong SK[32], vss_uint8 key[16] )
; Function sm4_setkey
.L33:
sm4_setkey:	.type	func

; ..\vss_code\sm4.c	   132  {
; ..\vss_code\sm4.c	   133  	vss_ulong MK[4];
; ..\vss_code\sm4.c	   134  	vss_ulong k[36];
; ..\vss_code\sm4.c	   135  	vss_ulong i = 0;
	mov	d8,#0
	mov.aa	a15,a5
.L197:

; ..\vss_code\sm4.c	   136  	
; ..\vss_code\sm4.c	   137  	MK[0] = GET_ULONG_BE(key, 0 );
	mov	d4,d8
	sub.a	a10,#160
.L195:
	mov.aa	a12,a4
.L200:
	mov.aa	a4,a15
.L196:
	call	GET_ULONG_BE
.L199:
	st.w	[a10],d2
.L361:

; ..\vss_code\sm4.c	   138  	MK[1] = GET_ULONG_BE(key, 4 );
	mov	d4,#4
	mov.aa	a4,a15
.L201:
	call	GET_ULONG_BE
.L202:
	st.w	[a10]4,d2
.L362:

; ..\vss_code\sm4.c	   139  	MK[2] = GET_ULONG_BE(key, 8 );
	mov	d4,#8
	mov.aa	a4,a15
.L203:
	call	GET_ULONG_BE
.L204:
	st.w	[a10]8,d2
.L363:

; ..\vss_code\sm4.c	   140  	MK[3] = GET_ULONG_BE(key, 12 );
	mov	d4,#12
	mov.aa	a4,a15
.L205:
	call	GET_ULONG_BE
.L206:
	st.w	[a10]12,d2
.L364:

; ..\vss_code\sm4.c	   141  	k[0] = MK[0]^sm4_FK[0];
	movh.a	a15,#@his(sm4_FK)
.L198:
	lea	a15,[a15]@los(sm4_FK)
.L365:
	ld.w	d0,[a10]
.L366:
	ld.w	d1,[a15]
.L367:

; ..\vss_code\sm4.c	   142  	k[1] = MK[1]^sm4_FK[1];
; ..\vss_code\sm4.c	   143  	k[2] = MK[2]^sm4_FK[2];
; ..\vss_code\sm4.c	   144  	k[3] = MK[3]^sm4_FK[3];
; ..\vss_code\sm4.c	   145  	for(; i<32; i++)
	lea	a13,31
.L368:
	xor	d0,d1
	st.w	[a10]16,d0
.L369:
	ld.w	d0,[a10]4
.L370:
	ld.w	d15,[a15]4
.L371:
	xor	d0,d15
	st.w	[a10]20,d0
.L372:
	ld.w	d0,[a10]8
.L373:
	ld.w	d15,[a15]8
.L374:
	xor	d0,d15
	st.w	[a10]24,d0
.L375:
	ld.w	d15,[a15]12
.L376:
	xor	d2,d15
	st.w	[a10]28,d2
.L7:

; ..\vss_code\sm4.c	   146  	{
; ..\vss_code\sm4.c	   147  		k[i+4] = k[i] ^ (sm4CalciRK(k[i+1]^k[i+2]^k[i+3]^sm4_CK[i]));
	addsc.a	a2,a10,d8,#2
	lea	a15,[a2]16
.L377:
	ld.w	d9,[a2]16
.L378:
	ld.w	d0,[a2]20
.L379:
	movh.a	a2,#@his(sm4_CK)
	lea	a2,[a2]@los(sm4_CK)
.L380:
	addsc.a	a2,a2,d8,#2
.L381:
	ld.w	d15,[a15]8
.L382:
	ld.w	d4,[a2]
.L383:
	xor	d0,d15
	ld.w	d15,[a15]12
.L384:
	xor	d0,d15
.L385:
	xor	d4,d0
	call	sm4CalciRK
.L386:
	xor	d9,d2
	st.w	[a15]16,d9
.L387:

; ..\vss_code\sm4.c	   148  		SK[i] = k[i+4];
	addsc.a	a15,a12,d8,#2
.L388:
	add	d8,#1
.L389:
	st.w	[a15],d9
	loop	a13,.L7
.L390:

; ..\vss_code\sm4.c	   149  	}
; ..\vss_code\sm4.c	   150  }
	ret
.L170:
	
__sm4_setkey_function_end:
	.size	sm4_setkey,__sm4_setkey_function_end-sm4_setkey
.L107:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4_one_round')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm4.c	   151  
; ..\vss_code\sm4.c	   152  /*
; ..\vss_code\sm4.c	   153   * SM4 standard one round processing
; ..\vss_code\sm4.c	   154   *
; ..\vss_code\sm4.c	   155   */
; ..\vss_code\sm4.c	   156  static void sm4_one_round( vss_ulong sm4_sk[32],
; Function sm4_one_round
.L35:
sm4_one_round:	.type	func

; ..\vss_code\sm4.c	   157                      vss_uint8 input[16],
; ..\vss_code\sm4.c	   158                      vss_uint8 output[16] )
; ..\vss_code\sm4.c	   159  {
; ..\vss_code\sm4.c	   160  	vss_ulong i = 0;
	mov	d15,#0
	sub.a	a10,#152
.L207:

; ..\vss_code\sm4.c	   161  	vss_ulong ulbuf[36];
; ..\vss_code\sm4.c	   162  
; ..\vss_code\sm4.c	   163  	mem_set8(ulbuf, 0, 36 * 4);
	mov	d4,d15
	mov.aa	a13,a4
.L210:
	mov	d5,#144
	lea	a4,[a10]4
.L209:
	mov.aa	a15,a5
.L211:
	mov.aa	a14,a6
.L212:
	call	mem_set8
.L208:

; ..\vss_code\sm4.c	   164  	ulbuf[0] = GET_ULONG_BE(input, 0 );
	mov	d4,#0
	mov.aa	a4,a15
.L213:
	call	GET_ULONG_BE
.L214:
	st.w	[a10]4,d2
.L395:

; ..\vss_code\sm4.c	   165  	ulbuf[1] = GET_ULONG_BE(input, 4 );
	mov	d4,#4
	mov.aa	a4,a15
.L215:
	call	GET_ULONG_BE
.L216:
	st.w	[a10]8,d2
.L396:

; ..\vss_code\sm4.c	   166  	ulbuf[2] = GET_ULONG_BE(input, 8 );
	mov	d4,#8
	mov.aa	a4,a15
.L217:
	call	GET_ULONG_BE
.L218:
	st.w	[a10]12,d2
.L397:

; ..\vss_code\sm4.c	   167  	ulbuf[3] = GET_ULONG_BE(input, 12 );
	mov	d4,#12
	mov.aa	a4,a15
.L219:
	call	GET_ULONG_BE
.L220:
	st.w	[a10]16,d2
.L398:

; ..\vss_code\sm4.c	   168  	while(i<32)
	lea	a12,31
.L8:

; ..\vss_code\sm4.c	   169  	{
; ..\vss_code\sm4.c	   170  		ulbuf[i+4] = sm4F(ulbuf[i], ulbuf[i+1], ulbuf[i+2], ulbuf[i+3], sm4_sk[i]);
	addsc.a	a4,a13,d15,#2
.L399:
	addsc.a	a15,a10,d15,#2
.L400:
	ld.w	d0,[a4]
	st.w	[a10],d0
.L401:
	ld.w	d4,[a15]4
.L402:
	ld.w	d5,[a15]8
.L403:
	ld.w	d6,[a15]12
.L404:
	ld.w	d7,[a15]16
	call	sm4F
.L405:

; ..\vss_code\sm4.c	   171  		i++;
	add	d15,#1
	st.w	[a15]20,d2
	loop	a12,.L8
.L406:

; ..\vss_code\sm4.c	   172  	}
; ..\vss_code\sm4.c	   173  	PUT_ULONG_BE(ulbuf[35],output,0);
	ld.w	d4,[a10]144
.L407:
	mov	d5,#0
	mov.aa	a4,a14
.L221:
	call	PUT_ULONG_BE
.L222:

; ..\vss_code\sm4.c	   174  	PUT_ULONG_BE(ulbuf[34],output,4);
	ld.w	d4,[a10]140
.L408:
	mov	d5,#4
	mov.aa	a4,a14
.L223:
	call	PUT_ULONG_BE
.L224:

; ..\vss_code\sm4.c	   175  	PUT_ULONG_BE(ulbuf[33],output,8);
	ld.w	d4,[a10]136
.L409:
	mov	d5,#8
	mov.aa	a4,a14
.L225:
	call	PUT_ULONG_BE
.L226:

; ..\vss_code\sm4.c	   176  	PUT_ULONG_BE(ulbuf[32],output,12);
	ld.w	d4,[a10]132
.L410:
	mov	d5,#12
	mov.aa	a4,a14
.L227:
	j	PUT_ULONG_BE
.L178:
	
__sm4_one_round_function_end:
	.size	sm4_one_round,__sm4_one_round_function_end-sm4_one_round
.L112:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4_setkey_enc')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	sm4_setkey_enc

; ..\vss_code\sm4.c	   177  }
; ..\vss_code\sm4.c	   178  
; ..\vss_code\sm4.c	   179  /*
; ..\vss_code\sm4.c	   180   * SM4 key schedule (128-bit, encryption)
; ..\vss_code\sm4.c	   181   */
; ..\vss_code\sm4.c	   182   void sm4_setkey_enc( sm4_context *ctx, vss_uint8 key[16] )
; Function sm4_setkey_enc
.L37:
sm4_setkey_enc:	.type	func

; ..\vss_code\sm4.c	   183  {
; ..\vss_code\sm4.c	   184  	ctx->mode = TT_SM4_ENCRYPT;
	mov	d15,#1
	st.w	[a4+],d15
.L288:

; ..\vss_code\sm4.c	   185  	sm4_setkey( ctx->sk, key );
	j	sm4_setkey
.L128:
	
__sm4_setkey_enc_function_end:
	.size	sm4_setkey_enc,__sm4_setkey_enc_function_end-sm4_setkey_enc
.L67:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4_setkey_dec')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	sm4_setkey_dec

; ..\vss_code\sm4.c	   186  }
; ..\vss_code\sm4.c	   187  
; ..\vss_code\sm4.c	   188  /*
; ..\vss_code\sm4.c	   189   * SM4 key schedule (128-bit, decryption)
; ..\vss_code\sm4.c	   190   */
; ..\vss_code\sm4.c	   191   void sm4_setkey_dec( sm4_context *ctx, vss_uint8 key[16] )
; Function sm4_setkey_dec
.L39:
sm4_setkey_dec:	.type	func
	mov.aa	a15,a4
.L230:

; ..\vss_code\sm4.c	   192  {
; ..\vss_code\sm4.c	   193  	vss_uint32 i;
; ..\vss_code\sm4.c	   194  	ctx->mode = TT_SM4_ENCRYPT;
	mov	d15,#1
	st.w	[a15],d15
.L293:

; ..\vss_code\sm4.c	   195  	sm4_setkey( ctx->sk, key );
	lea	a4,[a15]4
.L228:
	call	sm4_setkey
.L229:

; ..\vss_code\sm4.c	   196  	for( i = 0; i < 16; i ++ )
; ..\vss_code\sm4.c	   197  	{
; ..\vss_code\sm4.c	   198  		SWAP( &ctx->sk[ i ], &ctx->sk[ 31-i] );
	mov	d8,#0
	lea	a12,[a15]4
.L231:
	mov.a	a13,#15
.L9:
	rsub	d15,d8,#0
	mov.aa	a4,a12
	addsc.a	a2,a15,d15,#2
	lea	a5,[a2]128
	call	SWAP
.L294:
	add	d8,#1
	add.a	a12,#4
	loop	a13,.L9
.L295:

; ..\vss_code\sm4.c	   199  	}
; ..\vss_code\sm4.c	   200  }
	ret
.L132:
	
__sm4_setkey_dec_function_end:
	.size	sm4_setkey_dec,__sm4_setkey_dec_function_end-sm4_setkey_dec
.L72:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4_crypt_ecb')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	sm4_crypt_ecb

; ..\vss_code\sm4.c	   201  
; ..\vss_code\sm4.c	   202  
; ..\vss_code\sm4.c	   203  /*
; ..\vss_code\sm4.c	   204   * SM4-ECB block encryption/decryption
; ..\vss_code\sm4.c	   205   */
; ..\vss_code\sm4.c	   206  
; ..\vss_code\sm4.c	   207   void sm4_crypt_ecb( sm4_context *ctx,
; Function sm4_crypt_ecb
.L41:
sm4_crypt_ecb:	.type	func
	mov	d8,d5
	mov.aa	a15,a4
.L232:

; ..\vss_code\sm4.c	   208  				   vss_uint32 mode,
; ..\vss_code\sm4.c	   209  				   vss_uint32 length,
; ..\vss_code\sm4.c	   210  				   vss_uint8 *input,
; ..\vss_code\sm4.c	   211                     vss_uint8 *output)
; ..\vss_code\sm4.c	   212  {
; ..\vss_code\sm4.c	   213  
; ..\vss_code\sm4.c	   214  	if(length%16 != 0)
	and	d15,d8,#15
	mov.aa	a12,a5
.L233:
	mov.aa	a13,a6
.L234:
	jne	d15,#0,.L10
.L300:

; ..\vss_code\sm4.c	   215  	{
; ..\vss_code\sm4.c	   216  		return;
; ..\vss_code\sm4.c	   217  	}
; ..\vss_code\sm4.c	   218  
; ..\vss_code\sm4.c	   219  
; ..\vss_code\sm4.c	   220  	while( length > 0 )
	j	.L11
.L12:

; ..\vss_code\sm4.c	   221  	{
; ..\vss_code\sm4.c	   222  		sm4_one_round( ctx->sk, input, output );
	lea	a4,[a15]4
.L301:
	mov.aa	a5,a12
.L235:
	mov.aa	a6,a13
.L237:
	call	sm4_one_round
.L236:

; ..\vss_code\sm4.c	   223  		input  += 16;
; ..\vss_code\sm4.c	   224  		output += 16;
; ..\vss_code\sm4.c	   225  		length -= 16;
	add	d8,d8,#-16
	lea	a12,[a12]16
.L302:
	lea	a13,[a13]16
.L11:
	jne	d8,#0,.L12

; ..\vss_code\sm4.c	   226  	}
; ..\vss_code\sm4.c	   227  }
.L10:
	ret
.L136:
	
__sm4_crypt_ecb_function_end:
	.size	sm4_crypt_ecb,__sm4_crypt_ecb_function_end-sm4_crypt_ecb
.L77:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm4_ecb_encrypt_new')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	sm4_ecb_encrypt_new

; ..\vss_code\sm4.c	   228  #if 0
; ..\vss_code\sm4.c	   229  /*
; ..\vss_code\sm4.c	   230   * SM4-CBC buffer encryption/decryption
; ..\vss_code\sm4.c	   231   */
; ..\vss_code\sm4.c	   232   void sm4_crypt_cbc( sm4_context *ctx,
; ..\vss_code\sm4.c	   233                      vss_uint32 mode,
; ..\vss_code\sm4.c	   234                      vss_uint32 length,
; ..\vss_code\sm4.c	   235                      vss_uint8 iv[16],
; ..\vss_code\sm4.c	   236                      vss_uint8 *input,
; ..\vss_code\sm4.c	   237                      vss_uint8 *output )
; ..\vss_code\sm4.c	   238  {
; ..\vss_code\sm4.c	   239  	vss_uint32 i;
; ..\vss_code\sm4.c	   240  	vss_uint8 temp[16];
; ..\vss_code\sm4.c	   241  
; ..\vss_code\sm4.c	   242  	if((input == VSS_NULL)||(output == VSS_NULL))
; ..\vss_code\sm4.c	   243  	{
; ..\vss_code\sm4.c	   244  		return;
; ..\vss_code\sm4.c	   245  	}	
; ..\vss_code\sm4.c	   246  
; ..\vss_code\sm4.c	   247  	if(length%16 != 0)
; ..\vss_code\sm4.c	   248  	{
; ..\vss_code\sm4.c	   249  		return;
; ..\vss_code\sm4.c	   250  	}
; ..\vss_code\sm4.c	   251  
; ..\vss_code\sm4.c	   252  	if( mode == TT_SM4_ENCRYPT )
; ..\vss_code\sm4.c	   253  	{
; ..\vss_code\sm4.c	   254  		while( length > 0 )
; ..\vss_code\sm4.c	   255  		{
; ..\vss_code\sm4.c	   256  			for( i = 0; i < 16; i++ )
; ..\vss_code\sm4.c	   257  				output[i] =  input[i] ^ iv[i] ;
; ..\vss_code\sm4.c	   258  
; ..\vss_code\sm4.c	   259  			sm4_one_round( ctx->sk, output, output );
; ..\vss_code\sm4.c	   260  			mem_cpy8( iv, output, 16 );
; ..\vss_code\sm4.c	   261  
; ..\vss_code\sm4.c	   262  			input  += 16;
; ..\vss_code\sm4.c	   263  			output += 16;
; ..\vss_code\sm4.c	   264  			length -= 16;
; ..\vss_code\sm4.c	   265  		}
; ..\vss_code\sm4.c	   266  	}
; ..\vss_code\sm4.c	   267  	else /* SM4_DECRYPT */
; ..\vss_code\sm4.c	   268  	{
; ..\vss_code\sm4.c	   269  		while( length > 0 )
; ..\vss_code\sm4.c	   270  		{
; ..\vss_code\sm4.c	   271  			mem_cpy8( temp, input, 16 );
; ..\vss_code\sm4.c	   272  			sm4_one_round( ctx->sk, input, output );
; ..\vss_code\sm4.c	   273  
; ..\vss_code\sm4.c	   274  			for( i = 0; i < 16; i++ )
; ..\vss_code\sm4.c	   275  				output[i] =  output[i] ^ iv[i] ;
; ..\vss_code\sm4.c	   276  
; ..\vss_code\sm4.c	   277  			mem_cpy8( iv, temp, 16 );
; ..\vss_code\sm4.c	   278  
; ..\vss_code\sm4.c	   279  			input  += 16;
; ..\vss_code\sm4.c	   280  			output += 16;
; ..\vss_code\sm4.c	   281  			length -= 16;
; ..\vss_code\sm4.c	   282  		}
; ..\vss_code\sm4.c	   283  	}
; ..\vss_code\sm4.c	   284  }
; ..\vss_code\sm4.c	   285  #endif
; ..\vss_code\sm4.c	   286  vss_uint32 sm4_ecb_encrypt_new(vss_uint8 *in, vss_uint8 *out, vss_uint32 length,  vss_uint8 *key,vss_uint32 enc)
; Function sm4_ecb_encrypt_new
.L43:
sm4_ecb_encrypt_new:	.type	func
	mov	e8,d5,d4
	mov.aa	a15,a4
.L242:
	mov.aa	a12,a5
.L243:
	sub.a	a10,#136
.L238:

; ..\vss_code\sm4.c	   287  {
; ..\vss_code\sm4.c	   288  	sm4_context ctx;
; ..\vss_code\sm4.c	   289  	
; ..\vss_code\sm4.c	   290  	if (enc == TT_SM4_ENCRYPT)
	jne	d9,#1,.L13
.L244:

; ..\vss_code\sm4.c	   291  	{
; ..\vss_code\sm4.c	   292  		sm4_setkey_enc(&ctx,key);
	mov.aa	a4,a10
.L240:
	mov.aa	a5,a6
.L241:
	call	sm4_setkey_enc
.L239:
	j	.L14
.L13:

; ..\vss_code\sm4.c	   293  	}
; ..\vss_code\sm4.c	   294  	else
; ..\vss_code\sm4.c	   295  	{
; ..\vss_code\sm4.c	   296  		sm4_setkey_dec(&ctx,key);
	mov.aa	a4,a10
.L245:
	mov.aa	a5,a6
.L246:
	call	sm4_setkey_dec
.L14:

; ..\vss_code\sm4.c	   297  	}
; ..\vss_code\sm4.c	   298  
; ..\vss_code\sm4.c	   299  	
; ..\vss_code\sm4.c	   300  	if (length % 16 != 0)
	and	d15,d8,#15
.L247:
	jeq	d15,#0,.L15
.L307:

; ..\vss_code\sm4.c	   301  	{
; ..\vss_code\sm4.c	   302  		return 1;
; ..\vss_code\sm4.c	   303  	}
; ..\vss_code\sm4.c	   304  	sm4_crypt_ecb(&ctx,enc,length,in,out);
; ..\vss_code\sm4.c	   305  	return 0;
; ..\vss_code\sm4.c	   306  }
	mov	d2,#1
	ret
.L15:
	mov.aa	a4,a10
.L248:
	mov	e4,d8,d9
	mov.aa	a5,a15
.L249:
	mov.aa	a6,a12
.L251:
	call	sm4_crypt_ecb
.L250:
	mov	d2,#0
	ret
.L142:
	
__sm4_ecb_encrypt_new_function_end:
	.size	sm4_ecb_encrypt_new,__sm4_ecb_encrypt_new_function_end-sm4_ecb_encrypt_new
.L82:
	; End of function
	
	.calls	'sm4Lt','PUT_ULONG_BE'
	.calls	'sm4Lt','sm4Sbox'
	.calls	'sm4Lt','GET_ULONG_BE'
	.calls	'sm4F','sm4Lt'
	.calls	'sm4CalciRK','PUT_ULONG_BE'
	.calls	'sm4CalciRK','sm4Sbox'
	.calls	'sm4CalciRK','GET_ULONG_BE'
	.calls	'sm4_setkey','GET_ULONG_BE'
	.calls	'sm4_setkey','sm4CalciRK'
	.calls	'sm4_one_round','mem_set8'
	.calls	'sm4_one_round','GET_ULONG_BE'
	.calls	'sm4_one_round','sm4F'
	.calls	'sm4_one_round','PUT_ULONG_BE'
	.calls	'sm4_setkey_enc','sm4_setkey'
	.calls	'sm4_setkey_dec','sm4_setkey'
	.calls	'sm4_setkey_dec','SWAP'
	.calls	'sm4_crypt_ecb','sm4_one_round'
	.calls	'sm4_ecb_encrypt_new','sm4_setkey_enc'
	.calls	'sm4_ecb_encrypt_new','sm4_setkey_dec'
	.calls	'sm4_ecb_encrypt_new','sm4_crypt_ecb'
	.calls	'GET_ULONG_BE','',0
	.calls	'PUT_ULONG_BE','',0
	.calls	'SWAP','',0
	.calls	'sm4Sbox','',0
	.calls	'sm4Lt','',8
	.calls	'sm4F','',0
	.calls	'sm4CalciRK','',8
	.calls	'sm4_setkey','',160
	.calls	'sm4_one_round','',152
	.calls	'sm4_setkey_enc','',0
	.calls	'sm4_setkey_dec','',0
	.calls	'sm4_crypt_ecb','',0
	.extern	mem_set8
	.extern	sm4_SboxTable
	.extern	sm4_FK
	.extern	sm4_CK
	.calls	'sm4_ecb_encrypt_new','',136
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L45:
	.word	900
	.half	3
	.word	.L46
	.byte	4
.L44:
	.byte	1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L47
.L114:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L150:
	.byte	2
	.byte	'unsigned char',0,1,8
.L116:
	.byte	3
	.word	193
.L118:
	.byte	2
	.byte	'int',0,4,5
.L124:
	.byte	3
	.word	172
.L148:
	.byte	4,1,18,9,132,1,5
	.byte	'mode',0,4
	.word	172
	.byte	2,35,0,6,128,1
	.word	172
	.byte	7,31,0,5
	.byte	'sk',0,128,1
	.word	247
	.byte	2,35,4,0
.L129:
	.byte	3
	.word	227
	.byte	8
	.byte	'mem_set8',0,2,17,6,1,1,1,1,9
	.byte	'result',0,2,17,26
	.word	210
	.byte	10
	.word	193
	.byte	9
	.byte	'content',0,2,17,50
	.word	308
	.byte	9
	.byte	'len',0,2,17,70
	.word	172
	.byte	0
.L156:
	.byte	6,4
	.word	193
	.byte	7,3,0
.L173:
	.byte	6,16
	.word	172
	.byte	7,3,0
.L175:
	.byte	6,144,1
	.word	172
	.byte	7,35,0,11
	.byte	'void',0,3
	.word	370
	.byte	12
	.byte	'__prof_adm',0,3,1,1
	.word	376
	.byte	13,1,3
	.word	400
	.byte	12
	.byte	'__codeptr',0,3,1,1
	.word	402
	.byte	12
	.byte	'vss_uint8',0,4,8,24
	.word	193
	.byte	12
	.byte	'vss_uint32',0,4,13,24
	.word	172
	.byte	2
	.byte	'unsigned long long int',0,8,7,12
	.byte	'vss_uint64',0,4,17,28
	.word	462
	.byte	12
	.byte	'vss_ulong',0,4,18,24
	.word	172
	.byte	12
	.byte	'BYTE',0,4,21,22
	.word	193
	.byte	12
	.byte	'WORD',0,4,22,22
	.word	172
	.byte	12
	.byte	'SM3_WORD_T',0,4,25,22
	.word	172
	.byte	4,4,27,9,168,1,5
	.byte	'm_size',0,4
	.word	172
	.byte	2,35,0,6,128,1
	.word	193
	.byte	7,127,0,5
	.byte	'remain',0,128,1
	.word	592
	.byte	2,35,4,5
	.byte	'r_len',0,4
	.word	172
	.byte	3,35,132,1,6,32
	.word	172
	.byte	7,7,0,5
	.byte	'iv',0,32
	.word	635
	.byte	3,35,136,1,0,12
	.byte	'SM3_CTX_T',0,4,32,3
	.word	570
	.byte	4,4,34,9,108,6,64
	.word	193
	.byte	7,63,0,5
	.byte	'data',0,64
	.word	681
	.byte	2,35,0,5
	.byte	'datalen',0,4
	.word	172
	.byte	2,35,64,5
	.byte	'bitlen',0,8
	.word	462
	.byte	2,35,68,6,32
	.word	172
	.byte	7,7,0,5
	.byte	'state',0,32
	.word	737
	.byte	2,35,76,0,12
	.byte	'SHA256_CTX',0,4,39,3
	.word	676
	.byte	12
	.byte	'sm4_context',0,1,23,1
	.word	227
	.byte	6,16
	.word	193
	.byte	7,15,0,6,128,2
	.word	801
	.byte	7,15,0,10
	.word	810
	.byte	14
	.byte	'sm4_SboxTable',0,3,7,24
	.word	820
	.byte	1,1,10
	.word	351
	.byte	14
	.byte	'sm4_FK',0,3,9,24
	.word	849
	.byte	1,1,6,128,1
	.word	172
	.byte	7,31,0,10
	.word	871
	.byte	14
	.byte	'sm4_CK',0,3,11,24
	.word	881
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L46:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,15,0,73,19,0,0,4,19,1,58,15
	.byte	59,15,57,15,11,15,0,0,5,13,0,3,8,11,15,73,19,56,9,0,0,6,1,1,11,15,73,19,0,0,7,33,0,47,15,0,0,8,46,1,3
	.byte	8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,9,5,0,3,8,58,15,59,15,57,15,73,19,0,0,10,38,0,73,19,0
	.byte	0,11,59,0,3,8,0,0,12,22,0,3,8,58,15,59,15,57,15,73,19,0,0,13,21,0,54,15,0,0,14,52,0,3,8,58,15,59,15,57
	.byte	15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L47:
	.word	.L253-.L252
.L252:
	.half	3
	.word	.L255-.L254
.L254:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.h',0,0,0,0
	.byte	'..\\vss_code\\vsscommon.h',0,0,0,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0
	.byte	'..\\vss_code\\vsstype.h',0,0,0,0,0
.L255:
.L253:
	.sdecl	'.debug_info',debug,cluster('PUT_ULONG_BE')
	.sect	'.debug_info'
.L48:
	.word	262
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L51,.L50
	.byte	2
	.word	.L44
	.byte	3
	.byte	'PUT_ULONG_BE',0,1,27,6,1,1,1
	.word	.L21,.L113,.L20
	.byte	4
	.byte	'n',0,1,27,29
	.word	.L114,.L115
	.byte	4
	.byte	'b',0,1,27,43
	.word	.L116,.L117
	.byte	4
	.byte	'i',0,1,27,50
	.word	.L118,.L119
	.byte	5
	.word	.L21,.L113
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('PUT_ULONG_BE')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('PUT_ULONG_BE')
	.sect	'.debug_line'
.L50:
	.word	.L257-.L256
.L256:
	.half	3
	.word	.L259-.L258
.L258:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L259:
	.byte	5,3,7,0,5,2
	.word	.L21
	.byte	3,28,1,5,33,9
	.half	.L260-.L21
	.byte	1,5,13,9
	.half	.L261-.L260
	.byte	1,5,31,9
	.half	.L262-.L261
	.byte	3,1,1,5,11,1,5,31,9
	.half	.L263-.L262
	.byte	3,1,1,5,11,1,9
	.half	.L264-.L263
	.byte	3,1,1,5,1,9
	.half	.L265-.L264
	.byte	3,1,1,7,9
	.half	.L52-.L265
	.byte	0,1,1
.L257:
	.sdecl	'.debug_ranges',debug,cluster('PUT_ULONG_BE')
	.sect	'.debug_ranges'
.L51:
	.word	-1,.L21,0,.L52-.L21,0,0
	.sdecl	'.debug_info',debug,cluster('GET_ULONG_BE')
	.sect	'.debug_info'
.L53:
	.word	252
	.half	3
	.word	.L54
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L56,.L55
	.byte	2
	.word	.L44
	.byte	3
	.byte	'GET_ULONG_BE',0,1,16,11
	.word	.L114
	.byte	1,1,1
	.word	.L19,.L120,.L18
	.byte	4
	.byte	'b',0,1,16,35
	.word	.L116,.L121
	.byte	4
	.byte	'i',0,1,16,42
	.word	.L118,.L122
	.byte	5
	.word	.L19,.L120
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('GET_ULONG_BE')
	.sect	'.debug_abbrev'
.L54:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('GET_ULONG_BE')
	.sect	'.debug_line'
.L55:
	.word	.L267-.L266
.L266:
	.half	3
	.word	.L269-.L268
.L268:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L269:
	.byte	5,17,7,0,5,2
	.word	.L19
	.byte	3,22,1,5,18,9
	.half	.L270-.L19
	.byte	3,125,1,5,25,9
	.half	.L271-.L270
	.byte	1,5,14,3,1,1,5,23,9
	.half	.L272-.L271
	.byte	1,5,33,9
	.half	.L273-.L272
	.byte	3,127,1,5,14,3,2,1,5,23,9
	.half	.L274-.L273
	.byte	1,5,31,9
	.half	.L275-.L274
	.byte	3,127,1,5,14,3,2,1,5,29,9
	.half	.L276-.L275
	.byte	3,127,1,5,1,3,3,1,7,9
	.half	.L57-.L276
	.byte	0,1,1
.L267:
	.sdecl	'.debug_ranges',debug,cluster('GET_ULONG_BE')
	.sect	'.debug_ranges'
.L56:
	.word	-1,.L19,0,.L57-.L19,0,0
	.sdecl	'.debug_info',debug,cluster('SWAP')
	.sect	'.debug_info'
.L58:
	.word	255
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L61,.L60
	.byte	2
	.word	.L44
	.byte	3
	.byte	'SWAP',0,1,42,6,1,1,1
	.word	.L23,.L123,.L22
	.byte	4
	.byte	'a',0,1,42,22
	.word	.L124,.L125
	.byte	4
	.byte	'b',0,1,42,36
	.word	.L124,.L126
	.byte	5
	.word	.L23,.L123
	.byte	6
	.byte	't',0,1,44,12
	.word	.L114,.L127
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SWAP')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SWAP')
	.sect	'.debug_line'
.L60:
	.word	.L278-.L277
.L277:
	.half	3
	.word	.L280-.L279
.L279:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L280:
	.byte	5,16,7,0,5,2
	.word	.L23
	.byte	3,43,1,5,7,9
	.half	.L184-.L23
	.byte	3,1,1,5,5,9
	.half	.L281-.L184
	.byte	1,9
	.half	.L282-.L281
	.byte	3,1,1,5,1,9
	.half	.L283-.L282
	.byte	3,1,1,7,9
	.half	.L62-.L283
	.byte	0,1,1
.L278:
	.sdecl	'.debug_ranges',debug,cluster('SWAP')
	.sect	'.debug_ranges'
.L61:
	.word	-1,.L23,0,.L62-.L23,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_setkey_enc')
	.sect	'.debug_info'
.L63:
	.word	257
	.half	3
	.word	.L64
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L66,.L65
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4_setkey_enc',0,1,182,1,7,1,1,1
	.word	.L37,.L128,.L36
	.byte	4
	.byte	'ctx',0,1,182,1,36
	.word	.L129,.L130
	.byte	4
	.byte	'key',0,1,182,1,51
	.word	.L116,.L131
	.byte	5
	.word	.L37,.L128
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_setkey_enc')
	.sect	'.debug_abbrev'
.L64:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4_setkey_enc')
	.sect	'.debug_line'
.L65:
	.word	.L285-.L284
.L284:
	.half	3
	.word	.L287-.L286
.L286:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L287:
	.byte	5,14,7,0,5,2
	.word	.L37
	.byte	3,183,1,1,5,12,1,5,23,9
	.half	.L288-.L37
	.byte	3,1,1,5,1,7,9
	.half	.L67-.L288
	.byte	3,1,0,1,1
.L285:
	.sdecl	'.debug_ranges',debug,cluster('sm4_setkey_enc')
	.sect	'.debug_ranges'
.L66:
	.word	-1,.L37,0,.L67-.L37,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_setkey_dec')
	.sect	'.debug_info'
.L68:
	.word	273
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L71,.L70
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4_setkey_dec',0,1,191,1,7,1,1,1
	.word	.L39,.L132,.L38
	.byte	4
	.byte	'ctx',0,1,191,1,36
	.word	.L129,.L133
	.byte	4
	.byte	'key',0,1,191,1,51
	.word	.L116,.L134
	.byte	5
	.word	.L39,.L132
	.byte	6
	.byte	'i',0,1,193,1,13
	.word	.L114,.L135
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_setkey_dec')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4_setkey_dec')
	.sect	'.debug_line'
.L70:
	.word	.L290-.L289
.L289:
	.half	3
	.word	.L292-.L291
.L291:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L292:
	.byte	5,7,7,0,5,2
	.word	.L39
	.byte	3,190,1,1,5,14,9
	.half	.L230-.L39
	.byte	3,3,1,5,12,1,5,17,9
	.half	.L293-.L230
	.byte	3,1,1,5,23,9
	.half	.L228-.L293
	.byte	1,5,9,9
	.half	.L229-.L228
	.byte	3,1,1,5,13,3,2,1,5,20,9
	.half	.L231-.L229
	.byte	3,126,1,5,28,9
	.half	.L9-.L231
	.byte	3,2,1,5,24,9
	.half	.L294-.L9
	.byte	3,126,1,5,20,1,5,1,7,9
	.half	.L295-.L294
	.byte	3,4,1,7,9
	.half	.L72-.L295
	.byte	0,1,1
.L290:
	.sdecl	'.debug_ranges',debug,cluster('sm4_setkey_dec')
	.sect	'.debug_ranges'
.L71:
	.word	-1,.L39,0,.L72-.L39,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_crypt_ecb')
	.sect	'.debug_info'
.L73:
	.word	316
	.half	3
	.word	.L74
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L76,.L75
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4_crypt_ecb',0,1,207,1,7,1,1,1
	.word	.L41,.L136,.L40
	.byte	4
	.byte	'ctx',0,1,207,1,35
	.word	.L129,.L137
	.byte	4
	.byte	'mode',0,1,208,1,19
	.word	.L114,.L138
	.byte	4
	.byte	'length',0,1,209,1,19
	.word	.L114,.L139
	.byte	4
	.byte	'input',0,1,210,1,19
	.word	.L116,.L140
	.byte	4
	.byte	'output',0,1,211,1,31
	.word	.L116,.L141
	.byte	5
	.word	.L41,.L136
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_crypt_ecb')
	.sect	'.debug_abbrev'
.L74:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4_crypt_ecb')
	.sect	'.debug_line'
.L75:
	.word	.L297-.L296
.L296:
	.half	3
	.word	.L299-.L298
.L298:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L299:
	.byte	5,7,7,0,5,2
	.word	.L41
	.byte	3,206,1,1,5,5,9
	.half	.L232-.L41
	.byte	3,7,1,5,7,3,121,1,5,2,9
	.half	.L234-.L232
	.byte	3,7,1,5,20,7,9
	.half	.L300-.L234
	.byte	3,6,1,5,21,9
	.half	.L12-.L300
	.byte	3,2,1,5,34,9
	.half	.L301-.L12
	.byte	1,5,10,9
	.half	.L236-.L301
	.byte	3,3,1,3,126,1,9
	.half	.L302-.L236
	.byte	3,1,1,5,20,9
	.half	.L11-.L302
	.byte	3,124,1,5,1,7,9
	.half	.L10-.L11
	.byte	3,7,1,7,9
	.half	.L77-.L10
	.byte	0,1,1
.L297:
	.sdecl	'.debug_ranges',debug,cluster('sm4_crypt_ecb')
	.sect	'.debug_ranges'
.L76:
	.word	-1,.L41,0,.L77-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_ecb_encrypt_new')
	.sect	'.debug_info'
.L78:
	.word	337
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L81,.L80
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4_ecb_encrypt_new',0,1,158,2,12
	.word	.L114
	.byte	1,1,1
	.word	.L43,.L142,.L42
	.byte	4
	.byte	'in',0,1,158,2,43
	.word	.L116,.L143
	.byte	4
	.byte	'out',0,1,158,2,58
	.word	.L116,.L144
	.byte	4
	.byte	'length',0,1,158,2,74
	.word	.L114,.L145
	.byte	4
	.byte	'key',0,1,158,2,94
	.word	.L116,.L146
	.byte	4
	.byte	'enc',0,1,158,2,109
	.word	.L114,.L147
	.byte	5
	.word	.L43,.L142
	.byte	6
	.byte	'ctx',0,1,160,2,14
	.word	.L148,.L149
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_ecb_encrypt_new')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4_ecb_encrypt_new')
	.sect	'.debug_line'
.L80:
	.word	.L304-.L303
.L303:
	.half	3
	.word	.L306-.L305
.L305:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L306:
	.byte	5,12,7,0,5,2
	.word	.L43
	.byte	3,157,2,1,5,2,9
	.half	.L238-.L43
	.byte	3,4,1,5,19,7,9
	.half	.L244-.L238
	.byte	3,2,1,5,23,9
	.half	.L240-.L244
	.byte	1,5,27,9
	.half	.L239-.L240
	.byte	1,5,19,9
	.half	.L13-.L239
	.byte	3,4,1,5,23,9
	.half	.L245-.L13
	.byte	1,5,6,9
	.half	.L14-.L245
	.byte	3,4,1,5,2,9
	.half	.L247-.L14
	.byte	1,5,10,7,9
	.half	.L307-.L247
	.byte	3,2,1,5,1,3,4,1,5,17,7,9
	.half	.L15-.L307
	.byte	3,126,1,5,35,9
	.half	.L248-.L15
	.byte	1,5,9,9
	.half	.L250-.L248
	.byte	3,1,1,5,1,3,1,1,7,9
	.half	.L82-.L250
	.byte	0,1,1
.L304:
	.sdecl	'.debug_ranges',debug,cluster('sm4_ecb_encrypt_new')
	.sect	'.debug_ranges'
.L81:
	.word	-1,.L43,0,.L82-.L43,0,0
	.sdecl	'.debug_info',debug,cluster('sm4Sbox')
	.sect	'.debug_info'
.L83:
	.word	235
	.half	3
	.word	.L84
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L86,.L85
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4Sbox',0,1,61,18
	.word	.L150
	.byte	1,1
	.word	.L25,.L151,.L24
	.byte	4
	.byte	'inch',0,1,61,36
	.word	.L150,.L152
	.byte	5
	.word	.L25,.L151
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4Sbox')
	.sect	'.debug_abbrev'
.L84:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4Sbox')
	.sect	'.debug_line'
.L85:
	.word	.L309-.L308
.L308:
	.half	3
	.word	.L311-.L310
.L310:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L311:
	.byte	5,50,7,0,5,2
	.word	.L25
	.byte	3,62,1,5,30,9
	.half	.L312-.L25
	.byte	3,1,1,5,1,9
	.half	.L313-.L312
	.byte	3,3,1,7,9
	.half	.L87-.L313
	.byte	0,1,1
.L309:
	.sdecl	'.debug_ranges',debug,cluster('sm4Sbox')
	.sect	'.debug_ranges'
.L86:
	.word	-1,.L25,0,.L87-.L25,0,0
	.sdecl	'.debug_info',debug,cluster('sm4Lt')
	.sect	'.debug_info'
.L88:
	.word	275
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L91,.L90
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4Lt',0,1,75,18
	.word	.L114
	.byte	1,1
	.word	.L27,.L153,.L26
	.byte	4
	.byte	'ka',0,1,75,34
	.word	.L114,.L154
	.byte	5
	.word	.L27,.L153
	.byte	6
	.byte	'bb',0,1,77,12
	.word	.L114,.L155
	.byte	6
	.byte	'a',0,1,79,12
	.word	.L156,.L157
	.byte	6
	.byte	'b',0,1,80,12
	.word	.L156,.L158
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4Lt')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4Lt')
	.sect	'.debug_line'
.L90:
	.word	.L315-.L314
.L314:
	.half	3
	.word	.L317-.L316
.L316:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L317:
	.byte	5,18,7,0,5,2
	.word	.L27
	.byte	3,202,0,1,9
	.half	.L185-.L27
	.byte	3,7,1,5,20,9
	.half	.L318-.L185
	.byte	1,5,18,9
	.half	.L186-.L318
	.byte	3,1,1,5,7,9
	.half	.L319-.L186
	.byte	1,5,18,9
	.half	.L320-.L319
	.byte	3,1,1,5,7,9
	.half	.L321-.L320
	.byte	1,5,18,9
	.half	.L322-.L321
	.byte	3,1,1,5,7,9
	.half	.L323-.L322
	.byte	1,5,18,9
	.half	.L324-.L323
	.byte	3,1,1,5,7,9
	.half	.L325-.L324
	.byte	1,5,20,9
	.half	.L326-.L325
	.byte	3,1,1,5,22,9
	.half	.L327-.L326
	.byte	1,5,9,9
	.half	.L187-.L327
	.byte	3,1,1,5,7,9
	.half	.L328-.L187
	.byte	1,5,23,9
	.half	.L329-.L328
	.byte	1,5,21,9
	.half	.L330-.L329
	.byte	1,5,38,9
	.half	.L331-.L330
	.byte	1,5,36,9
	.half	.L332-.L331
	.byte	1,5,53,9
	.half	.L333-.L332
	.byte	1,5,51,9
	.half	.L334-.L333
	.byte	1,5,1,3,3,1,7,9
	.half	.L92-.L334
	.byte	0,1,1
.L315:
	.sdecl	'.debug_ranges',debug,cluster('sm4Lt')
	.sect	'.debug_ranges'
.L91:
	.word	-1,.L27,0,.L92-.L27,0,0
	.sdecl	'.debug_info',debug,cluster('sm4F')
	.sect	'.debug_info'
.L93:
	.word	290
	.half	3
	.word	.L94
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L96,.L95
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4F',0,1,103,18
	.word	.L114
	.byte	1,1
	.word	.L29,.L159,.L28
	.byte	4
	.byte	'x0',0,1,103,33
	.word	.L114,.L160
	.byte	4
	.byte	'x1',0,1,103,47
	.word	.L114,.L161
	.byte	4
	.byte	'x2',0,1,103,61
	.word	.L114,.L162
	.byte	4
	.byte	'x3',0,1,103,75
	.word	.L114,.L163
	.byte	4
	.byte	'rk',0,1,103,89
	.word	.L114,.L164
	.byte	5
	.word	.L29,.L159
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4F')
	.sect	'.debug_abbrev'
.L94:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4F')
	.sect	'.debug_line'
.L95:
	.word	.L336-.L335
.L335:
	.half	3
	.word	.L338-.L337
.L337:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L338:
	.byte	5,18,7,0,5,2
	.word	.L29
	.byte	3,230,0,1,5,24,9
	.half	.L189-.L29
	.byte	3,2,1,5,27,9
	.half	.L190-.L189
	.byte	1,5,30,9
	.half	.L339-.L190
	.byte	1,5,15,9
	.half	.L191-.L339
	.byte	1,5,1,3,1,1,7,9
	.half	.L97-.L191
	.byte	0,1,1
.L336:
	.sdecl	'.debug_ranges',debug,cluster('sm4F')
	.sect	'.debug_ranges'
.L96:
	.word	-1,.L29,0,.L97-.L29,0,0
	.sdecl	'.debug_info',debug,cluster('sm4CalciRK')
	.sect	'.debug_info'
.L98:
	.word	280
	.half	3
	.word	.L99
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L101,.L100
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4CalciRK',0,1,114,18
	.word	.L114
	.byte	1,1
	.word	.L31,.L165,.L30
	.byte	4
	.byte	'ka',0,1,114,39
	.word	.L114,.L166
	.byte	5
	.word	.L31,.L165
	.byte	6
	.byte	'bb',0,1,116,12
	.word	.L114,.L167
	.byte	6
	.byte	'a',0,1,118,12
	.word	.L156,.L168
	.byte	6
	.byte	'b',0,1,119,12
	.word	.L156,.L169
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4CalciRK')
	.sect	'.debug_abbrev'
.L99:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4CalciRK')
	.sect	'.debug_line'
.L100:
	.word	.L341-.L340
.L340:
	.half	3
	.word	.L343-.L342
.L342:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L343:
	.byte	5,18,7,0,5,2
	.word	.L31
	.byte	3,241,0,1,9
	.half	.L192-.L31
	.byte	3,7,1,5,20,9
	.half	.L344-.L192
	.byte	1,5,18,9
	.half	.L193-.L344
	.byte	3,1,1,5,7,9
	.half	.L345-.L193
	.byte	1,5,18,9
	.half	.L346-.L345
	.byte	3,1,1,5,7,9
	.half	.L347-.L346
	.byte	1,5,18,9
	.half	.L348-.L347
	.byte	3,1,1,5,7,9
	.half	.L349-.L348
	.byte	1,5,18,9
	.half	.L350-.L349
	.byte	3,1,1,5,7,9
	.half	.L351-.L350
	.byte	1,5,20,9
	.half	.L352-.L351
	.byte	3,1,1,5,22,9
	.half	.L353-.L352
	.byte	1,5,11,9
	.half	.L194-.L353
	.byte	3,1,1,5,9,9
	.half	.L354-.L194
	.byte	1,5,26,9
	.half	.L355-.L354
	.byte	1,5,24,9
	.half	.L356-.L355
	.byte	1,5,1,3,2,1,7,9
	.half	.L102-.L356
	.byte	0,1,1
.L341:
	.sdecl	'.debug_ranges',debug,cluster('sm4CalciRK')
	.sect	'.debug_ranges'
.L101:
	.word	-1,.L31,0,.L102-.L31,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_setkey')
	.sect	'.debug_info'
.L103:
	.word	298
	.half	3
	.word	.L104
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L106,.L105
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4_setkey',0,1,131,1,13,1,1
	.word	.L33,.L170,.L32
	.byte	4
	.byte	'SK',0,1,131,1,35
	.word	.L124,.L171
	.byte	4
	.byte	'key',0,1,131,1,53
	.word	.L116,.L172
	.byte	5
	.word	.L33,.L170
	.byte	6
	.byte	'MK',0,1,133,1,12
	.word	.L173,.L174
	.byte	6
	.byte	'k',0,1,134,1,12
	.word	.L175,.L176
	.byte	6
	.byte	'i',0,1,135,1,12
	.word	.L114,.L177
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_setkey')
	.sect	'.debug_abbrev'
.L104:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4_setkey')
	.sect	'.debug_line'
.L105:
	.word	.L358-.L357
.L357:
	.half	3
	.word	.L360-.L359
.L359:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L360:
	.byte	5,14,7,0,5,2
	.word	.L33
	.byte	3,134,1,1,5,13,3,124,1,5,28,9
	.half	.L197-.L33
	.byte	3,6,1,5,13,3,122,1,5,28,9
	.half	.L200-.L197
	.byte	3,6,1,5,8,9
	.half	.L199-.L200
	.byte	1,5,28,9
	.half	.L361-.L199
	.byte	3,1,1,5,8,9
	.half	.L202-.L361
	.byte	1,5,28,9
	.half	.L362-.L202
	.byte	3,1,1,5,8,9
	.half	.L204-.L362
	.byte	1,5,28,9
	.half	.L363-.L204
	.byte	3,1,1,5,8,9
	.half	.L206-.L363
	.byte	1,5,15,9
	.half	.L364-.L206
	.byte	3,1,1,5,11,9
	.half	.L365-.L364
	.byte	1,5,21,9
	.half	.L366-.L365
	.byte	1,5,12,9
	.half	.L367-.L366
	.byte	3,4,1,5,14,9
	.half	.L368-.L367
	.byte	3,124,1,5,7,1,5,11,9
	.half	.L369-.L368
	.byte	3,1,1,5,21,9
	.half	.L370-.L369
	.byte	1,5,14,9
	.half	.L371-.L370
	.byte	1,5,7,1,5,11,9
	.half	.L372-.L371
	.byte	3,1,1,5,21,9
	.half	.L373-.L372
	.byte	1,5,14,9
	.half	.L374-.L373
	.byte	1,5,7,1,5,21,9
	.half	.L375-.L374
	.byte	3,1,1,5,14,9
	.half	.L376-.L375
	.byte	1,5,7,1,5,4,9
	.half	.L7-.L376
	.byte	3,3,1,5,13,9
	.half	.L377-.L7
	.byte	1,5,32,9
	.half	.L378-.L377
	.byte	1,5,52,9
	.half	.L379-.L378
	.byte	1,5,58,9
	.half	.L380-.L379
	.byte	1,5,39,9
	.half	.L381-.L380
	.byte	1,5,58,9
	.half	.L382-.L381
	.byte	1,5,37,9
	.half	.L383-.L382
	.byte	1,5,46,1,5,44,9
	.half	.L384-.L383
	.byte	1,5,51,9
	.half	.L385-.L384
	.byte	1,5,17,9
	.half	.L386-.L385
	.byte	1,5,10,1,5,5,9
	.half	.L387-.L386
	.byte	3,1,1,5,15,9
	.half	.L388-.L387
	.byte	3,125,1,5,9,9
	.half	.L389-.L388
	.byte	3,3,1,5,12,3,125,1,5,1,7,9
	.half	.L390-.L389
	.byte	3,5,1,7,9
	.half	.L107-.L390
	.byte	0,1,1
.L358:
	.sdecl	'.debug_ranges',debug,cluster('sm4_setkey')
	.sect	'.debug_ranges'
.L106:
	.word	-1,.L33,0,.L107-.L33,0,0
	.sdecl	'.debug_info',debug,cluster('sm4_one_round')
	.sect	'.debug_info'
.L108:
	.word	315
	.half	3
	.word	.L109
	.byte	4,1
	.byte	'..\\vss_code\\sm4.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L111,.L110
	.byte	2
	.word	.L44
	.byte	3
	.byte	'sm4_one_round',0,1,156,1,13,1,1
	.word	.L35,.L178,.L34
	.byte	4
	.byte	'sm4_sk',0,1,156,1,38
	.word	.L124,.L179
	.byte	4
	.byte	'input',0,1,157,1,31
	.word	.L116,.L180
	.byte	4
	.byte	'output',0,1,158,1,31
	.word	.L116,.L181
	.byte	5
	.word	.L35,.L178
	.byte	6
	.byte	'i',0,1,160,1,12
	.word	.L114,.L182
	.byte	6
	.byte	'ulbuf',0,1,161,1,12
	.word	.L175,.L183
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm4_one_round')
	.sect	'.debug_abbrev'
.L109:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm4_one_round')
	.sect	'.debug_line'
.L110:
	.word	.L392-.L391
.L391:
	.half	3
	.word	.L394-.L393
.L393:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm4.c',0,0,0,0,0
.L394:
	.byte	5,14,7,0,5,2
	.word	.L35
	.byte	3,159,1,1,5,13,3,124,1,5,18,9
	.half	.L207-.L35
	.byte	3,7,1,5,13,3,121,1,5,24,9
	.half	.L210-.L207
	.byte	3,7,1,5,11,1,5,13,9
	.half	.L209-.L210
	.byte	3,121,1,5,24,9
	.half	.L212-.L209
	.byte	3,7,1,5,33,9
	.half	.L208-.L212
	.byte	3,1,1,5,11,9
	.half	.L214-.L208
	.byte	1,5,33,9
	.half	.L395-.L214
	.byte	3,1,1,5,11,9
	.half	.L216-.L395
	.byte	1,5,33,9
	.half	.L396-.L216
	.byte	3,1,1,5,11,9
	.half	.L218-.L396
	.byte	1,5,33,9
	.half	.L397-.L218
	.byte	3,1,1,5,11,9
	.half	.L220-.L397
	.byte	1,5,12,9
	.half	.L398-.L220
	.byte	3,1,1,5,73,9
	.half	.L8-.L398
	.byte	3,2,1,5,60,9
	.half	.L399-.L8
	.byte	1,5,73,9
	.half	.L400-.L399
	.byte	1,5,26,9
	.half	.L401-.L400
	.byte	1,5,36,9
	.half	.L402-.L401
	.byte	1,5,48,9
	.half	.L403-.L402
	.byte	1,5,60,9
	.half	.L404-.L403
	.byte	1,5,4,9
	.half	.L405-.L404
	.byte	3,1,1,5,14,3,127,1,5,12,3,126,1,5,20,7,9
	.half	.L406-.L405
	.byte	3,5,1,5,32,9
	.half	.L407-.L406
	.byte	1,5,20,9
	.half	.L222-.L407
	.byte	3,1,1,5,32,9
	.half	.L408-.L222
	.byte	1,5,20,9
	.half	.L224-.L408
	.byte	3,1,1,5,32,9
	.half	.L409-.L224
	.byte	1,5,20,9
	.half	.L226-.L409
	.byte	3,1,1,5,32,9
	.half	.L410-.L226
	.byte	1,5,1,9
	.half	.L112-.L410
	.byte	3,1,0,1,1
.L392:
	.sdecl	'.debug_ranges',debug,cluster('sm4_one_round')
	.sect	'.debug_ranges'
.L111:
	.word	-1,.L35,0,.L112-.L35,0,0
	.sdecl	'.debug_loc',debug,cluster('GET_ULONG_BE')
	.sect	'.debug_loc'
.L18:
	.word	-1,.L19,0,.L120-.L19
	.half	2
	.byte	138,0
	.word	0,0
.L121:
	.word	-1,.L19,0,.L120-.L19
	.half	1
	.byte	100
	.word	0,0
.L122:
	.word	-1,.L19,0,.L120-.L19
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('PUT_ULONG_BE')
	.sect	'.debug_loc'
.L20:
	.word	-1,.L21,0,.L113-.L21
	.half	2
	.byte	138,0
	.word	0,0
.L117:
	.word	-1,.L21,0,.L113-.L21
	.half	1
	.byte	100
	.word	0,0
.L119:
	.word	-1,.L21,0,.L113-.L21
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L115:
	.word	-1,.L21,0,.L113-.L21
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SWAP')
	.sect	'.debug_loc'
.L22:
	.word	-1,.L23,0,.L123-.L23
	.half	2
	.byte	138,0
	.word	0,0
.L125:
	.word	-1,.L23,0,.L123-.L23
	.half	1
	.byte	100
	.word	0,0
.L126:
	.word	-1,.L23,0,.L123-.L23
	.half	1
	.byte	101
	.word	0,0
.L127:
	.word	-1,.L23,.L184-.L23,.L123-.L23
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4CalciRK')
	.sect	'.debug_loc'
.L168:
	.word	-1,.L31,0,.L165-.L31
	.half	2
	.byte	145,120
	.word	0,0
.L169:
	.word	-1,.L31,0,.L165-.L31
	.half	2
	.byte	145,124
	.word	0,0
.L167:
	.word	-1,.L31,.L194-.L31,.L165-.L31
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L166:
	.word	-1,.L31,0,.L193-.L31
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L30:
	.word	-1,.L31,0,.L192-.L31
	.half	2
	.byte	138,0
	.word	.L192-.L31,.L165-.L31
	.half	2
	.byte	138,8
	.word	.L165-.L31,.L165-.L31
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4F')
	.sect	'.debug_loc'
.L164:
	.word	-1,.L29,0,.L159-.L29
	.half	2
	.byte	145,0
	.word	0,.L159-.L29
	.half	2
	.byte	145,0
	.word	.L189-.L29,.L191-.L29
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L28:
	.word	-1,.L29,0,.L159-.L29
	.half	2
	.byte	138,0
	.word	0,0
.L160:
	.word	-1,.L29,0,.L189-.L29
	.half	5
	.byte	144,34,157,32,0
	.word	.L189-.L29,.L159-.L29
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L161:
	.word	-1,.L29,0,.L190-.L29
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L162:
	.word	-1,.L29,0,.L191-.L29
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L163:
	.word	-1,.L29,0,.L191-.L29
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4Lt')
	.sect	'.debug_loc'
.L157:
	.word	-1,.L27,0,.L153-.L27
	.half	2
	.byte	145,120
	.word	0,0
.L158:
	.word	-1,.L27,0,.L153-.L27
	.half	2
	.byte	145,124
	.word	0,0
.L155:
	.word	-1,.L27,.L187-.L27,.L188-.L27
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L154:
	.word	-1,.L27,0,.L186-.L27
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L26:
	.word	-1,.L27,0,.L185-.L27
	.half	2
	.byte	138,0
	.word	.L185-.L27,.L153-.L27
	.half	2
	.byte	138,8
	.word	.L153-.L27,.L153-.L27
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4Sbox')
	.sect	'.debug_loc'
.L152:
	.word	-1,.L25,0,.L151-.L25
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L24:
	.word	-1,.L25,0,.L151-.L25
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4_crypt_ecb')
	.sect	'.debug_loc'
.L137:
	.word	-1,.L41,0,.L12-.L41
	.half	1
	.byte	100
	.word	.L232-.L41,.L136-.L41
	.half	1
	.byte	111
	.word	0,0
.L140:
	.word	-1,.L41,0,.L12-.L41
	.half	1
	.byte	101
	.word	.L233-.L41,.L136-.L41
	.half	1
	.byte	108
	.word	.L235-.L41,.L236-.L41
	.half	1
	.byte	101
	.word	0,0
.L139:
	.word	-1,.L41,0,.L12-.L41
	.half	5
	.byte	144,34,157,32,32
	.word	.L232-.L41,.L136-.L41
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L138:
	.word	-1,.L41,0,.L12-.L41
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L141:
	.word	-1,.L41,0,.L12-.L41
	.half	1
	.byte	102
	.word	.L234-.L41,.L136-.L41
	.half	1
	.byte	109
	.word	.L237-.L41,.L236-.L41
	.half	1
	.byte	102
	.word	0,0
.L40:
	.word	-1,.L41,0,.L136-.L41
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4_ecb_encrypt_new')
	.sect	'.debug_loc'
.L149:
	.word	-1,.L43,0,.L142-.L43
	.half	3
	.byte	145,248,126
	.word	0,0
.L147:
	.word	-1,.L43,0,.L239-.L43
	.half	5
	.byte	144,34,157,32,32
	.word	.L238-.L43,.L244-.L43
	.half	5
	.byte	144,36,157,32,32
	.word	.L13-.L43,.L14-.L43
	.half	5
	.byte	144,34,157,32,32
	.word	.L248-.L43,.L249-.L43
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L143:
	.word	-1,.L43,0,.L240-.L43
	.half	1
	.byte	100
	.word	.L242-.L43,.L142-.L43
	.half	1
	.byte	111
	.word	.L13-.L43,.L245-.L43
	.half	1
	.byte	100
	.word	.L249-.L43,.L250-.L43
	.half	1
	.byte	101
	.word	0,0
.L146:
	.word	-1,.L43,0,.L239-.L43
	.half	1
	.byte	102
	.word	.L241-.L43,.L239-.L43
	.half	1
	.byte	101
	.word	.L13-.L43,.L14-.L43
	.half	1
	.byte	102
	.word	.L246-.L43,.L14-.L43
	.half	1
	.byte	101
	.word	0,0
.L145:
	.word	-1,.L43,0,.L239-.L43
	.half	5
	.byte	144,34,157,32,0
	.word	.L13-.L43,.L14-.L43
	.half	5
	.byte	144,34,157,32,0
	.word	.L14-.L43,.L247-.L43
	.half	5
	.byte	144,36,157,32,0
	.word	.L248-.L43,.L249-.L43
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L144:
	.word	-1,.L43,0,.L241-.L43
	.half	1
	.byte	101
	.word	.L243-.L43,.L142-.L43
	.half	1
	.byte	108
	.word	.L13-.L43,.L246-.L43
	.half	1
	.byte	101
	.word	.L251-.L43,.L250-.L43
	.half	1
	.byte	102
	.word	0,0
.L42:
	.word	-1,.L43,0,.L238-.L43
	.half	2
	.byte	138,0
	.word	.L238-.L43,.L15-.L43
	.half	3
	.byte	138,136,1
	.word	.L15-.L43,.L15-.L43
	.half	2
	.byte	138,0
	.word	.L15-.L43,.L142-.L43
	.half	3
	.byte	138,136,1
	.word	.L142-.L43,.L142-.L43
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4_one_round')
	.sect	'.debug_loc'
.L182:
	.word	-1,.L35,.L207-.L35,.L178-.L35
	.half	5
	.byte	144,39,157,32,32
	.word	.L210-.L35,.L208-.L35
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L180:
	.word	-1,.L35,0,.L208-.L35
	.half	1
	.byte	101
	.word	.L211-.L35,.L8-.L35
	.half	1
	.byte	111
	.word	.L213-.L35,.L214-.L35
	.half	1
	.byte	100
	.word	.L215-.L35,.L216-.L35
	.half	1
	.byte	100
	.word	.L217-.L35,.L218-.L35
	.half	1
	.byte	100
	.word	.L219-.L35,.L220-.L35
	.half	1
	.byte	100
	.word	0,0
.L181:
	.word	-1,.L35,0,.L208-.L35
	.half	1
	.byte	102
	.word	.L212-.L35,.L178-.L35
	.half	1
	.byte	110
	.word	.L221-.L35,.L222-.L35
	.half	1
	.byte	100
	.word	.L223-.L35,.L224-.L35
	.half	1
	.byte	100
	.word	.L225-.L35,.L226-.L35
	.half	1
	.byte	100
	.word	.L227-.L35,.L178-.L35
	.half	1
	.byte	100
	.word	0,0
.L34:
	.word	-1,.L35,0,.L207-.L35
	.half	2
	.byte	138,0
	.word	.L207-.L35,.L178-.L35
	.half	3
	.byte	138,152,1
	.word	.L178-.L35,.L178-.L35
	.half	2
	.byte	138,0
	.word	0,0
.L179:
	.word	-1,.L35,0,.L209-.L35
	.half	1
	.byte	100
	.word	.L210-.L35,.L178-.L35
	.half	1
	.byte	109
	.word	0,0
.L183:
	.word	-1,.L35,0,.L178-.L35
	.half	3
	.byte	145,236,126
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4_setkey')
	.sect	'.debug_loc'
.L174:
	.word	-1,.L33,0,.L170-.L33
	.half	3
	.byte	145,224,126
	.word	0,0
.L171:
	.word	-1,.L33,0,.L196-.L33
	.half	1
	.byte	100
	.word	.L200-.L33,.L170-.L33
	.half	1
	.byte	108
	.word	0,0
.L177:
	.word	-1,.L33,.L197-.L33,.L170-.L33
	.half	5
	.byte	144,36,157,32,0
	.word	.L195-.L33,.L199-.L33
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L176:
	.word	-1,.L33,0,.L170-.L33
	.half	3
	.byte	145,240,126
	.word	0,0
.L172:
	.word	-1,.L33,0,.L196-.L33
	.half	1
	.byte	101
	.word	.L197-.L33,.L198-.L33
	.half	1
	.byte	111
	.word	.L196-.L33,.L199-.L33
	.half	1
	.byte	100
	.word	.L201-.L33,.L202-.L33
	.half	1
	.byte	100
	.word	.L203-.L33,.L204-.L33
	.half	1
	.byte	100
	.word	.L205-.L33,.L206-.L33
	.half	1
	.byte	100
	.word	0,0
.L32:
	.word	-1,.L33,0,.L195-.L33
	.half	2
	.byte	138,0
	.word	.L195-.L33,.L170-.L33
	.half	3
	.byte	138,160,1
	.word	.L170-.L33,.L170-.L33
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4_setkey_dec')
	.sect	'.debug_loc'
.L133:
	.word	-1,.L39,0,.L228-.L39
	.half	1
	.byte	100
	.word	.L230-.L39,.L132-.L39
	.half	1
	.byte	111
	.word	0,0
.L135:
	.word	-1,.L39,.L231-.L39,.L132-.L39
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L134:
	.word	-1,.L39,0,.L229-.L39
	.half	1
	.byte	101
	.word	0,0
.L38:
	.word	-1,.L39,0,.L132-.L39
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm4_setkey_enc')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L37,0,.L128-.L37
	.half	1
	.byte	100
	.word	0,0
.L131:
	.word	-1,.L37,0,.L128-.L37
	.half	1
	.byte	101
	.word	0,0
.L36:
	.word	-1,.L37,0,.L128-.L37
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L411:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('GET_ULONG_BE')
	.sect	'.debug_frame'
	.word	24
	.word	.L411,.L19,.L120-.L19
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('PUT_ULONG_BE')
	.sect	'.debug_frame'
	.word	24
	.word	.L411,.L21,.L113-.L21
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('SWAP')
	.sect	'.debug_frame'
	.word	20
	.word	.L411,.L23,.L123-.L23
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('sm4Sbox')
	.sect	'.debug_frame'
	.word	24
	.word	.L411,.L25,.L151-.L25
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('sm4Lt')
	.sect	'.debug_frame'
	.word	36
	.word	.L411,.L27,.L153-.L27
	.byte	4
	.word	(.L185-.L27)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L153-.L185)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('sm4F')
	.sect	'.debug_frame'
	.word	12
	.word	.L411,.L29,.L159-.L29
	.sdecl	'.debug_frame',debug,cluster('sm4CalciRK')
	.sect	'.debug_frame'
	.word	36
	.word	.L411,.L31,.L165-.L31
	.byte	4
	.word	(.L192-.L31)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L165-.L192)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('sm4_setkey')
	.sect	'.debug_frame'
	.word	36
	.word	.L411,.L33,.L170-.L33
	.byte	4
	.word	(.L195-.L33)/2
	.byte	19,160,1,22,26,4,19,138,160,1,4
	.word	(.L170-.L195)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('sm4_one_round')
	.sect	'.debug_frame'
	.word	36
	.word	.L411,.L35,.L178-.L35
	.byte	4
	.word	(.L207-.L35)/2
	.byte	19,152,1,22,26,4,19,138,152,1,4
	.word	(.L178-.L207)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('sm4_setkey_enc')
	.sect	'.debug_frame'
	.word	12
	.word	.L411,.L37,.L128-.L37
	.sdecl	'.debug_frame',debug,cluster('sm4_setkey_dec')
	.sect	'.debug_frame'
	.word	12
	.word	.L411,.L39,.L132-.L39
	.sdecl	'.debug_frame',debug,cluster('sm4_crypt_ecb')
	.sect	'.debug_frame'
	.word	12
	.word	.L411,.L41,.L136-.L41
	.sdecl	'.debug_frame',debug,cluster('sm4_ecb_encrypt_new')
	.sect	'.debug_frame'
	.word	56
	.word	.L411,.L43,.L142-.L43
	.byte	4
	.word	(.L238-.L43)/2
	.byte	19,136,1,22,26,4,19,138,136,1,4
	.word	(.L15-.L238)/2
	.byte	19,0,8,26,19,136,1,22,26,4,19,138,136,1,4
	.word	(.L142-.L15)/2
	.byte	19,0,8,26,0

; ..\vss_code\sm4.c	   307  #if 0
; ..\vss_code\sm4.c	   308  vss_uint32 sm4_cbc_encrypt_new(vss_uint8 *iv, vss_uint8 *in, vss_uint8 *out, vss_uint32 length,  vss_uint8 *key,vss_uint32 enc)
; ..\vss_code\sm4.c	   309  {
; ..\vss_code\sm4.c	   310  	sm4_context ctx;
; ..\vss_code\sm4.c	   311  	
; ..\vss_code\sm4.c	   312  	if (enc == TT_SM4_ENCRYPT)
; ..\vss_code\sm4.c	   313  	{
; ..\vss_code\sm4.c	   314  		sm4_setkey_enc(&ctx,key);
; ..\vss_code\sm4.c	   315  	}
; ..\vss_code\sm4.c	   316  	else
; ..\vss_code\sm4.c	   317  	{
; ..\vss_code\sm4.c	   318  		sm4_setkey_dec(&ctx,key);
; ..\vss_code\sm4.c	   319  	}
; ..\vss_code\sm4.c	   320  	
; ..\vss_code\sm4.c	   321  	if (length % 16 != 0)
; ..\vss_code\sm4.c	   322  	{
; ..\vss_code\sm4.c	   323  		return 1;
; ..\vss_code\sm4.c	   324  	}
; ..\vss_code\sm4.c	   325  	
; ..\vss_code\sm4.c	   326  	sm4_crypt_cbc(&ctx,enc,length,iv,in,out);
; ..\vss_code\sm4.c	   327  	return 0;
; ..\vss_code\sm4.c	   328  }
; ..\vss_code\sm4.c	   329  #endif
; ..\vss_code\sm4.c	   330  
; ..\vss_code\sm4.c	   331  
; ..\vss_code\sm4.c	   332  #pragma section code restore
; ..\vss_code\sm4.c	   333  
; ..\vss_code\sm4.c	   334  

	; Module end
