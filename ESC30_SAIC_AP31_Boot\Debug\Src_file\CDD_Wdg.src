	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc28084a -c99 --dep-file=Src_file\\.CDD_Wdg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=Src_file\\CDD_Wdg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o Src_file\\CDD_Wdg.src ..\\Src_file\\CDD_Wdg.c"
	.compiler_name		"ctc"
	.name	"CDD_Wdg"

	
$TC16X
	
	.sdecl	'.text.CDD_Wdg.CDD_Wdg_EN',code,cluster('CDD_Wdg_EN')
	.sect	'.text.CDD_Wdg.CDD_Wdg_EN'
	.align	2
	
	.global	CDD_Wdg_EN

; ..\Src_file\CDD_Wdg.c	     1  /*
; ..\Src_file\CDD_Wdg.c	     2   * CDD_Wdg.c
; ..\Src_file\CDD_Wdg.c	     3   *
; ..\Src_file\CDD_Wdg.c	     4   *  Created on: 2019-8-12
; ..\Src_file\CDD_Wdg.c	     5   *      Author: gaoguanlong
; ..\Src_file\CDD_Wdg.c	     6   */
; ..\Src_file\CDD_Wdg.c	     7  #include "Std_Types.h"
; ..\Src_file\CDD_Wdg.c	     8  #include "Port.h"
; ..\Src_file\CDD_Wdg.c	     9  #include "Dio.h"
; ..\Src_file\CDD_Wdg.c	    10  #include "CDD_Wdg.h"
; ..\Src_file\CDD_Wdg.c	    11  
; ..\Src_file\CDD_Wdg.c	    12  #include "Std_Types.h"
; ..\Src_file\CDD_Wdg.c	    13  #include "IfxScu_reg.h"
; ..\Src_file\CDD_Wdg.c	    14  #include "IfxCpu_reg.h"
; ..\Src_file\CDD_Wdg.c	    15  /* Inclusion of EcuM.h */
; ..\Src_file\CDD_Wdg.c	    16  #include "EcuM.h"
; ..\Src_file\CDD_Wdg.c	    17  #include "Test_Print.h"
; ..\Src_file\CDD_Wdg.c	    18  #include "Test_Time.h"
; ..\Src_file\CDD_Wdg.c	    19  
; ..\Src_file\CDD_Wdg.c	    20  uint8 Wdg_Wdg1DemoStatusVar;
; ..\Src_file\CDD_Wdg.c	    21  
; ..\Src_file\CDD_Wdg.c	    22  void CDD_Wdg_EN(void)
; Function CDD_Wdg_EN
.L3:
CDD_Wdg_EN:	.type	func

; ..\Src_file\CDD_Wdg.c	    23  {
; ..\Src_file\CDD_Wdg.c	    24  	Dio_WriteChannel(DIO_CHANNEL_20_8,1);     //使能看门狗
	mov	d4,#328
.L33:
	mov	d5,#1
	j	Dio_WriteChannel
.L22:
	
__CDD_Wdg_EN_function_end:
	.size	CDD_Wdg_EN,__CDD_Wdg_EN_function_end-CDD_Wdg_EN
.L14:
	; End of function
	
	.sdecl	'.text.CDD_Wdg.CDD_Wdg_Dis',code,cluster('CDD_Wdg_Dis')
	.sect	'.text.CDD_Wdg.CDD_Wdg_Dis'
	.align	2
	
	.global	CDD_Wdg_Dis

; ..\Src_file\CDD_Wdg.c	    25  }
; ..\Src_file\CDD_Wdg.c	    26  
; ..\Src_file\CDD_Wdg.c	    27  void CDD_Wdg_Dis(void)
; Function CDD_Wdg_Dis
.L5:
CDD_Wdg_Dis:	.type	func

; ..\Src_file\CDD_Wdg.c	    28  {
; ..\Src_file\CDD_Wdg.c	    29  	Dio_WriteChannel(DIO_CHANNEL_20_8,STD_LOW);     //关闭看门狗
	mov	d4,#328
.L38:
	mov	d5,#0
	j	Dio_WriteChannel
.L23:
	
__CDD_Wdg_Dis_function_end:
	.size	CDD_Wdg_Dis,__CDD_Wdg_Dis_function_end-CDD_Wdg_Dis
.L19:
	; End of function
	
	.sdecl	'.bss.CDD_Wdg.Wdg_Wdg1DemoStatusVar',data,cluster('Wdg_Wdg1DemoStatusVar')
	.sect	'.bss.CDD_Wdg.Wdg_Wdg1DemoStatusVar'
	.global	Wdg_Wdg1DemoStatusVar
Wdg_Wdg1DemoStatusVar:	.type	object
	.size	Wdg_Wdg1DemoStatusVar,1
	.space	1
	.calls	'CDD_Wdg_EN','Dio_WriteChannel'
	.calls	'CDD_Wdg_Dis','Dio_WriteChannel'
	.calls	'CDD_Wdg_EN','',0
	.extern	Dio_WriteChannel
	.calls	'CDD_Wdg_Dis','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L7:
	.word	45536
	.half	3
	.word	.L8
	.byte	4
.L6:
	.byte	1
	.byte	'..\\Src_file\\CDD_Wdg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L9
	.byte	2
	.byte	'Dio_WriteChannel',0,1,232,3,13,1,1,1,1,3
	.byte	'unsigned short int',0,2,7,4
	.byte	'ChannelId',0,1,234,3,19
	.word	202
.L24:
	.byte	3
	.byte	'unsigned char',0,1,8,4
	.byte	'Level',0,1,235,3,17
	.word	243
	.byte	0,5
	.byte	'void',0,6
	.word	276
	.byte	7
	.byte	'__prof_adm',0,2,1,1
	.word	282
	.byte	8,1,6
	.word	306
	.byte	7
	.byte	'__codeptr',0,2,1,1
	.word	308
	.byte	7
	.byte	'uint8',0,3,90,29
	.word	243
	.byte	7
	.byte	'uint16',0,3,92,29
	.word	202
	.byte	3
	.byte	'unsigned long int',0,4,7,7
	.byte	'uint32',0,3,94,29
	.word	360
	.byte	3
	.byte	'unsigned int',0,4,7,7
	.byte	'unsigned_int',0,4,121,22
	.word	396
	.byte	9
	.byte	'Port_n_PinType',0,5,176,2,15,4,10,5,178,2,3,2,11
	.byte	'P0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	243
	.byte	1,0,2,35,1,0,12
	.byte	'B',0,2
	.word	454
	.byte	2,35,0,12
	.byte	'U',0,4
	.word	360
	.byte	2,35,0,0,7
	.byte	'Port_n_PinType',0,5,203,2,3
	.word	433
	.byte	9
	.byte	'Port_n_ControlType',0,5,206,2,15,16,10,5,208,2,3,16,12
	.byte	'PC0',0,1
	.word	243
	.byte	2,35,0,12
	.byte	'PC1',0,1
	.word	243
	.byte	2,35,1,12
	.byte	'PC2',0,1
	.word	243
	.byte	2,35,2,12
	.byte	'PC3',0,1
	.word	243
	.byte	2,35,3,12
	.byte	'PC4',0,1
	.word	243
	.byte	2,35,4,12
	.byte	'PC5',0,1
	.word	243
	.byte	2,35,5,12
	.byte	'PC6',0,1
	.word	243
	.byte	2,35,6,12
	.byte	'PC7',0,1
	.word	243
	.byte	2,35,7,12
	.byte	'PC8',0,1
	.word	243
	.byte	2,35,8,12
	.byte	'PC9',0,1
	.word	243
	.byte	2,35,9,12
	.byte	'PC10',0,1
	.word	243
	.byte	2,35,10,12
	.byte	'PC11',0,1
	.word	243
	.byte	2,35,11,12
	.byte	'PC12',0,1
	.word	243
	.byte	2,35,12,12
	.byte	'PC13',0,1
	.word	243
	.byte	2,35,13,12
	.byte	'PC14',0,1
	.word	243
	.byte	2,35,14,12
	.byte	'PC15',0,1
	.word	243
	.byte	2,35,15,0,12
	.byte	'B',0,16
	.word	763
	.byte	2,35,0,13,16
	.word	360
	.byte	14,3,0,12
	.byte	'U',0,16
	.word	995
	.byte	2,35,0,0,7
	.byte	'Port_n_ControlType',0,5,233,2,2
	.word	738
	.byte	15
	.byte	'Port_n_ConfigType',0,5,140,3,16,28,12
	.byte	'PinControl',0,16
	.word	738
	.byte	2,35,0,12
	.byte	'PinLevel',0,4
	.word	433
	.byte	2,35,16,12
	.byte	'DriverStrength0',0,4
	.word	360
	.byte	2,35,20,12
	.byte	'DriverStrength1',0,4
	.word	360
	.byte	2,35,24,0,7
	.byte	'Port_n_ConfigType',0,5,164,3,2
	.word	1044
	.byte	7
	.byte	'Port_n_PCSRConfigType',0,5,167,3,16
	.word	360
	.byte	15
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	243
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	243
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	1215
	.byte	15
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	396
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	1768
	.byte	15
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	1841
	.byte	15
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	243
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2155
	.byte	15
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	2256
	.byte	15
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	243
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	243
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	243
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	243
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	243
	.byte	5,0,2,35,3,0,7
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2553
	.byte	15
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	243
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	243
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	243
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	243
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	243
	.byte	5,0,2,35,3,0,7
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	2754
	.byte	15
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	243
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	243
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	243
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	243
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	243
	.byte	5,0,2,35,3,0,7
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2961
	.byte	15
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	243
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	243
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	243
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	243
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	243
	.byte	5,0,2,35,3,0,7
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	3162
	.byte	15
	.byte	'_Ifx_P_OMCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,2
	.word	202
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	202
	.byte	12,0,2,35,2,0,7
	.byte	'Ifx_P_OMCR0_Bits',0,6,200,1,3
	.word	3365
	.byte	15
	.byte	'_Ifx_P_OMCR12_Bits',0,6,203,1,16,4,11
	.byte	'reserved_0',0,4
	.word	396
	.byte	28,4,2,35,2,11
	.byte	'PCL12',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_P_OMCR12_Bits',0,6,210,1,3
	.word	3525
	.byte	15
	.byte	'_Ifx_P_OMCR4_Bits',0,6,213,1,16,4,11
	.byte	'reserved_0',0,4
	.word	396
	.byte	20,12,2,35,2,11
	.byte	'PCL4',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	243
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_P_OMCR4_Bits',0,6,221,1,3
	.word	3668
	.byte	15
	.byte	'_Ifx_P_OMCR8_Bits',0,6,224,1,16,4,11
	.byte	'reserved_0',0,4
	.word	396
	.byte	24,8,2,35,2,11
	.byte	'PCL8',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	243
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	243
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_P_OMCR8_Bits',0,6,232,1,3
	.word	3828
	.byte	15
	.byte	'_Ifx_P_OMCR_Bits',0,6,235,1,16,4,11
	.byte	'reserved_0',0,2
	.word	202
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	243
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	243
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_P_OMCR_Bits',0,6,254,1,3
	.word	3990
	.byte	15
	.byte	'_Ifx_P_OMR_Bits',0,6,129,2,16,4,11
	.byte	'PS0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	243
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	243
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_P_OMR_Bits',0,6,163,2,3
	.word	4323
	.byte	15
	.byte	'_Ifx_P_OMSR0_Bits',0,6,166,2,16,4,11
	.byte	'PS0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	396
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_P_OMSR0_Bits',0,6,173,2,3
	.word	4878
	.byte	15
	.byte	'_Ifx_P_OMSR12_Bits',0,6,176,2,16,4,11
	.byte	'reserved_0',0,2
	.word	202
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_P_OMSR12_Bits',0,6,184,2,3
	.word	5011
	.byte	15
	.byte	'_Ifx_P_OMSR4_Bits',0,6,187,2,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	396
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_P_OMSR4_Bits',0,6,195,2,3
	.word	5173
	.byte	15
	.byte	'_Ifx_P_OMSR8_Bits',0,6,198,2,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	396
	.byte	20,0,2,35,2,0,7
	.byte	'Ifx_P_OMSR8_Bits',0,6,206,2,3
	.word	5328
	.byte	15
	.byte	'_Ifx_P_OMSR_Bits',0,6,209,2,16,4,11
	.byte	'PS0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_P_OMSR_Bits',0,6,228,2,3
	.word	5486
	.byte	15
	.byte	'_Ifx_P_OUT_Bits',0,6,231,2,16,4,11
	.byte	'P0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_P_OUT_Bits',0,6,250,2,3
	.word	5804
	.byte	15
	.byte	'_Ifx_P_PCSR_Bits',0,6,253,2,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	202
	.byte	6,7,2,35,0,11
	.byte	'SEL9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	396
	.byte	20,1,2,35,2,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_P_PCSR_Bits',0,6,135,3,3
	.word	6104
	.byte	15
	.byte	'_Ifx_P_PDISC_Bits',0,6,138,3,16,4,11
	.byte	'PDIS0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_P_PDISC_Bits',0,6,157,3,3
	.word	6300
	.byte	15
	.byte	'_Ifx_P_PDR0_Bits',0,6,160,3,16,4,11
	.byte	'PD0',0,1
	.word	243
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	243
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	243
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	243
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	243
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	243
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	243
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	243
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_P_PDR0_Bits',0,6,178,3,3
	.word	6652
	.byte	15
	.byte	'_Ifx_P_PDR1_Bits',0,6,181,3,16,4,11
	.byte	'PD8',0,1
	.word	243
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	243
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	243
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	243
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	243
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	243
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	243
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	243
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_P_PDR1_Bits',0,6,199,3,3
	.word	6941
	.byte	16,6,207,3,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,3
	.byte	'int',0,4,5,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	1215
	.byte	2,35,0,0,7
	.byte	'Ifx_P_ACCEN0',0,6,212,3,3
	.word	7242
	.byte	16,6,215,3,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	1768
	.byte	2,35,0,0,7
	.byte	'Ifx_P_ACCEN1',0,6,220,3,3
	.word	7311
	.byte	16,6,223,3,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	1841
	.byte	2,35,0,0,7
	.byte	'Ifx_P_ESR',0,6,228,3,3
	.word	7373
	.byte	16,6,231,3,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	2155
	.byte	2,35,0,0,7
	.byte	'Ifx_P_ID',0,6,236,3,3
	.word	7432
	.byte	16,6,239,3,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	2256
	.byte	2,35,0,0,7
	.byte	'Ifx_P_IN',0,6,244,3,3
	.word	7490
	.byte	16,6,247,3,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	2553
	.byte	2,35,0,0,7
	.byte	'Ifx_P_IOCR0',0,6,252,3,3
	.word	7548
	.byte	16,6,255,3,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	2754
	.byte	2,35,0,0,7
	.byte	'Ifx_P_IOCR12',0,6,132,4,3
	.word	7609
	.byte	16,6,135,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	2961
	.byte	2,35,0,0,7
	.byte	'Ifx_P_IOCR4',0,6,140,4,3
	.word	7671
	.byte	16,6,143,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3162
	.byte	2,35,0,0,7
	.byte	'Ifx_P_IOCR8',0,6,148,4,3
	.word	7732
	.byte	16,6,151,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3990
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMCR',0,6,156,4,3
	.word	7793
	.byte	16,6,159,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3365
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMCR0',0,6,164,4,3
	.word	7853
	.byte	16,6,167,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3525
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMCR12',0,6,172,4,3
	.word	7914
	.byte	16,6,175,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3668
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMCR4',0,6,180,4,3
	.word	7976
	.byte	16,6,183,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	3828
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMCR8',0,6,188,4,3
	.word	8037
	.byte	16,6,191,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	4323
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMR',0,6,196,4,3
	.word	8098
	.byte	16,6,199,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5486
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMSR',0,6,204,4,3
	.word	8157
	.byte	16,6,207,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	4878
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMSR0',0,6,212,4,3
	.word	8217
	.byte	16,6,215,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5011
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMSR12',0,6,220,4,3
	.word	8278
	.byte	16,6,223,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5173
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMSR4',0,6,228,4,3
	.word	8340
	.byte	16,6,231,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5328
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OMSR8',0,6,236,4,3
	.word	8401
	.byte	16,6,239,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	5804
	.byte	2,35,0,0,7
	.byte	'Ifx_P_OUT',0,6,244,4,3
	.word	8462
	.byte	16,6,247,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	6104
	.byte	2,35,0,0,7
	.byte	'Ifx_P_PCSR',0,6,252,4,3
	.word	8521
	.byte	16,6,255,4,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	6300
	.byte	2,35,0,0,7
	.byte	'Ifx_P_PDISC',0,6,132,5,3
	.word	8581
	.byte	16,6,135,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	6652
	.byte	2,35,0,0,7
	.byte	'Ifx_P_PDR0',0,6,140,5,3
	.word	8642
	.byte	16,6,143,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	6941
	.byte	2,35,0,0,7
	.byte	'Ifx_P_PDR1',0,6,148,5,3
	.word	8702
	.byte	7
	.byte	'Dio_LevelType',0,1,237,1,17
	.word	243
	.byte	7
	.byte	'Dio_ChannelType',0,1,243,1,17
	.word	202
	.byte	7
	.byte	'Dio_PortType',0,1,249,1,17
	.word	243
	.byte	7
	.byte	'Dio_PortLevelType',0,1,252,1,17
	.word	202
	.byte	15
	.byte	'Dio_ChannelGroupType',0,1,129,2,16,4,12
	.byte	'mask',0,2
	.word	202
	.byte	2,35,0,12
	.byte	'offset',0,1
	.word	243
	.byte	2,35,2,12
	.byte	'port',0,1
	.word	243
	.byte	2,35,3,0,7
	.byte	'Dio_ChannelGroupType',0,1,137,2,3
	.word	8859
	.byte	15
	.byte	'Dio_PortChannelIdType',0,1,139,2,16,8,12
	.byte	'Dio_PortIdConfig',0,4
	.word	360
	.byte	2,35,0,12
	.byte	'Dio_ChannelConfig',0,4
	.word	360
	.byte	2,35,4,0,7
	.byte	'Dio_PortChannelIdType',0,1,145,2,2
	.word	8961
	.byte	15
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,7,45,16,4,11
	.byte	'EN0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	243
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	243
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_ACCEN0_Bits',0,7,79,3
	.word	9074
	.byte	15
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,7,82,16,4,11
	.byte	'reserved_0',0,4
	.word	396
	.byte	32,0,2,35,2,0,7
	.byte	'Ifx_SCU_ACCEN1_Bits',0,7,85,3
	.word	9631
	.byte	15
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,7,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	396
	.byte	29,0,2,35,2,0,7
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,7,94,3
	.word	9708
	.byte	15
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,7,97,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	243
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	243
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	243
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	243
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	243
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	243
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	243
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	243
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	243
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON0_Bits',0,7,111,3
	.word	9844
	.byte	15
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,7,114,16,4,11
	.byte	'CANDIV',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	243
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	243
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	243
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	243
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	243
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	243
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	243
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON1_Bits',0,7,126,3
	.word	10126
	.byte	15
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,7,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	396
	.byte	26,2,2,35,2,11
	.byte	'UP',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON2_Bits',0,7,135,1,3
	.word	10364
	.byte	15
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,7,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	243
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	243
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	243
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	243
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	243
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	243
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	6,2,2,35,3,11
	.byte	'UP',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON3_Bits',0,7,149,1,3
	.word	10492
	.byte	15
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,7,152,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	243
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	243
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	243
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	243
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	243
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	243
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	6,2,2,35,3,11
	.byte	'UP',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON4_Bits',0,7,163,1,3
	.word	10719
	.byte	15
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,7,166,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	396
	.byte	26,2,2,35,2,11
	.byte	'UP',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CCUCON5_Bits',0,7,172,1,3
	.word	10938
	.byte	15
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,7,175,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	243
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	396
	.byte	26,0,2,35,2,0,7
	.byte	'Ifx_SCU_CCUCON6_Bits',0,7,179,1,3
	.word	11066
	.byte	15
	.byte	'_Ifx_SCU_CHIPID_Bits',0,7,182,1,16,4,11
	.byte	'CHREV',0,1
	.word	243
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	243
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	243
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	243
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	243
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_CHIPID_Bits',0,7,193,1,3
	.word	11166
	.byte	15
	.byte	'_Ifx_SCU_DTSCON_Bits',0,7,196,1,16,4,11
	.byte	'PWD',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	396
	.byte	22,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	243
	.byte	5,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_DTSCON_Bits',0,7,204,1,3
	.word	11374
	.byte	15
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,7,207,1,16,4,11
	.byte	'LOWER',0,2
	.word	202
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	243
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	202
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	243
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_DTSLIM_Bits',0,7,216,1,3
	.word	11539
	.byte	15
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,7,219,1,16,4,11
	.byte	'RESULT',0,2
	.word	202
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	243
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,7,226,1,3
	.word	11722
	.byte	15
	.byte	'_Ifx_SCU_EICR_Bits',0,7,229,1,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	243
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	243
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	396
	.byte	5,12,2,35,2,11
	.byte	'EXIS1',0,1
	.word	243
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	243
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	243
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EICR_Bits',0,7,248,1,3
	.word	11876
	.byte	15
	.byte	'_Ifx_SCU_EIFR_Bits',0,7,251,1,16,4,11
	.byte	'INTF0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	396
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_SCU_EIFR_Bits',0,7,134,2,3
	.word	12240
	.byte	15
	.byte	'_Ifx_SCU_EMSR_Bits',0,7,137,2,16,4,11
	.byte	'POL',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	202
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	243
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	243
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	243
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	243
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_SCU_EMSR_Bits',0,7,150,2,3
	.word	12451
	.byte	15
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,7,153,2,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	202
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	396
	.byte	23,0,2,35,2,0,7
	.byte	'Ifx_SCU_ESRCFG_Bits',0,7,158,2,3
	.word	12703
	.byte	15
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,7,161,2,16,4,11
	.byte	'ARI',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	396
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_ESROCFG_Bits',0,7,166,2,3
	.word	12821
	.byte	15
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,7,169,2,16,4,11
	.byte	'reserved_0',0,4
	.word	396
	.byte	28,4,2,35,2,11
	.byte	'EVR13OFF',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVR13CON_Bits',0,7,176,2,3
	.word	12932
	.byte	15
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,7,179,2,16,4,11
	.byte	'ADC13V',0,1
	.word	243
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	243
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,7,186,2,3
	.word	13095
	.byte	15
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,7,189,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	243
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	243
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	202
	.byte	10,0,2,35,0,11
	.byte	'SWDOVMOD',0,1
	.word	243
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	243
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	243
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	202
	.byte	8,2,2,35,2,11
	.byte	'SLCK',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,7,201,2,3
	.word	13257
	.byte	15
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,7,204,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	243
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	243
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	6,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVROVMON_Bits',0,7,212,2,3
	.word	13535
	.byte	15
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,7,215,2,16,4,11
	.byte	'reserved_0',0,4
	.word	396
	.byte	28,4,2,35,2,11
	.byte	'RSTSWDOFF',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,7,222,2,3
	.word	13714
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,7,225,2,16,4,11
	.byte	'SD33P',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	243
	.byte	4,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	243
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	396
	.byte	19,1,2,35,2,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,7,232,2,3
	.word	13874
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,7,235,2,16,4,11
	.byte	'SDFREQSPRD',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	243
	.byte	4,0,2,35,0,11
	.byte	'TON',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'TOFF',0,1
	.word	243
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	243
	.byte	4,4,2,35,3,11
	.byte	'SYNCDIV',0,1
	.word	243
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,7,244,2,3
	.word	14035
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,7,247,2,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	8,0,2,35,0,11
	.byte	'STBS',0,1
	.word	243
	.byte	2,6,2,35,1,11
	.byte	'STSP',0,1
	.word	243
	.byte	2,4,2,35,1,11
	.byte	'NS',0,1
	.word	243
	.byte	2,2,2,35,1,11
	.byte	'OL',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'PIAD',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'ADCMODE',0,1
	.word	243
	.byte	4,4,2,35,2,11
	.byte	'ADCLPF',0,1
	.word	243
	.byte	2,2,2,35,2,11
	.byte	'ADCLSB',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	243
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,7,134,3,3
	.word	14227
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,7,137,3,16,4,11
	.byte	'SDOLCON',0,1
	.word	243
	.byte	7,1,2,35,0,11
	.byte	'MODSEL',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'MODLOW',0,1
	.word	243
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	243
	.byte	6,2,2,35,2,11
	.byte	'MODMAN',0,1
	.word	243
	.byte	2,0,2,35,2,11
	.byte	'MODHIGH',0,1
	.word	243
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,7,147,3,3
	.word	14523
	.byte	15
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,7,150,3,16,4,11
	.byte	'EVR13',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	2,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	2,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'SCMOD',0,1
	.word	243
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	396
	.byte	18,0,2,35,2,0,7
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,7,164,3,3
	.word	14738
	.byte	15
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,7,167,3,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	243
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	243
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	6,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,7,175,3,3
	.word	15027
	.byte	15
	.byte	'_Ifx_SCU_EXTCON_Bits',0,7,178,3,16,4,11
	.byte	'EN0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	243
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	202
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	243
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	243
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	243
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_SCU_EXTCON_Bits',0,7,189,3,3
	.word	15206
	.byte	15
	.byte	'_Ifx_SCU_FDR_Bits',0,7,192,3,16,4,11
	.byte	'STEP',0,2
	.word	202
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	243
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	243
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	202
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	243
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_FDR_Bits',0,7,200,3,3
	.word	15424
	.byte	15
	.byte	'_Ifx_SCU_FMR_Bits',0,7,203,3,16,4,11
	.byte	'FS0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	243
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_SCU_FMR_Bits',0,7,223,3,3
	.word	15587
	.byte	15
	.byte	'_Ifx_SCU_ID_Bits',0,7,226,3,16,4,11
	.byte	'MODREV',0,1
	.word	243
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_ID_Bits',0,7,231,3,3
	.word	15923
	.byte	15
	.byte	'_Ifx_SCU_IGCR_Bits',0,7,234,3,16,4,11
	.byte	'IPEN00',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	243
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	243
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	243
	.byte	2,0,2,35,3,0,7
	.byte	'Ifx_SCU_IGCR_Bits',0,7,130,4,3
	.word	16030
	.byte	15
	.byte	'_Ifx_SCU_IN_Bits',0,7,133,4,16,4,11
	.byte	'P0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	396
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_IN_Bits',0,7,138,4,3
	.word	16482
	.byte	15
	.byte	'_Ifx_SCU_IOCR_Bits',0,7,141,4,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	243
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	243
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_IOCR_Bits',0,7,148,4,3
	.word	16581
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,7,151,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	202
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,7,157,4,3
	.word	16731
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,7,160,4,16,4,11
	.byte	'SEED',0,4
	.word	396
	.byte	23,9,2,35,2,11
	.byte	'reserved_23',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	243
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	243
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,7,167,4,3
	.word	16880
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,7,170,4,16,4,11
	.byte	'SIGNATURE',0,4
	.word	396
	.byte	24,8,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,7,175,4,3
	.word	17041
	.byte	15
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,7,178,4,16,4,11
	.byte	'reserved_0',0,2
	.word	202
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	202
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_LCLCON0_Bits',0,7,184,4,3
	.word	17171
	.byte	15
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,7,187,4,16,4,11
	.byte	'LCLT0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	396
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_LCLTEST_Bits',0,7,192,4,3
	.word	17305
	.byte	15
	.byte	'_Ifx_SCU_MANID_Bits',0,7,195,4,16,4,11
	.byte	'DEPT',0,1
	.word	243
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	202
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_MANID_Bits',0,7,200,4,3
	.word	17420
	.byte	15
	.byte	'_Ifx_SCU_OMR_Bits',0,7,203,4,16,4,11
	.byte	'PS0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	202
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	202
	.byte	14,0,2,35,2,0,7
	.byte	'Ifx_SCU_OMR_Bits',0,7,211,4,3
	.word	17531
	.byte	15
	.byte	'_Ifx_SCU_OSCCON_Bits',0,7,214,4,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	243
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	243
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	243
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	243
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	243
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	243
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	8,0,2,35,3,0,7
	.byte	'Ifx_SCU_OSCCON_Bits',0,7,231,4,3
	.word	17689
	.byte	15
	.byte	'_Ifx_SCU_OUT_Bits',0,7,234,4,16,4,11
	.byte	'P0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	396
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_OUT_Bits',0,7,239,4,3
	.word	18029
	.byte	15
	.byte	'_Ifx_SCU_OVCCON_Bits',0,7,242,4,16,4,11
	.byte	'CSEL0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	202
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	243
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	243
	.byte	6,0,2,35,3,0,7
	.byte	'Ifx_SCU_OVCCON_Bits',0,7,255,4,3
	.word	18130
	.byte	15
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,7,130,5,16,4,11
	.byte	'OVEN0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	396
	.byte	29,0,2,35,2,0,7
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,7,136,5,3
	.word	18397
	.byte	15
	.byte	'_Ifx_SCU_PDISC_Bits',0,7,139,5,16,4,11
	.byte	'PDIS0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	396
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_PDISC_Bits',0,7,144,5,3
	.word	18533
	.byte	15
	.byte	'_Ifx_SCU_PDR_Bits',0,7,147,5,16,4,11
	.byte	'PD0',0,1
	.word	243
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	243
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	396
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_SCU_PDR_Bits',0,7,154,5,3
	.word	18644
	.byte	15
	.byte	'_Ifx_SCU_PDRR_Bits',0,7,157,5,16,4,11
	.byte	'PDR0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	396
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_SCU_PDRR_Bits',0,7,168,5,3
	.word	18777
	.byte	15
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,7,171,5,16,4,11
	.byte	'VCOBYP',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	202
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	243
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	243
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	243
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	243
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_SCU_PLLCON0_Bits',0,7,188,5,3
	.word	18980
	.byte	15
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,7,191,5,16,4,11
	.byte	'K2DIV',0,1
	.word	243
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	243
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	243
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	202
	.byte	9,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLCON1_Bits',0,7,199,5,3
	.word	19336
	.byte	15
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,7,202,5,16,4,11
	.byte	'MODCFG',0,2
	.word	202
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLCON2_Bits',0,7,206,5,3
	.word	19514
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,7,209,5,16,4,11
	.byte	'VCOBYP',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	202
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	243
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	243
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	243
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	243
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	243
	.byte	4,0,2,35,3,0,7
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,7,226,5,3
	.word	19614
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,7,229,5,16,4,11
	.byte	'K2DIV',0,1
	.word	243
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	243
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	243
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	243
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	202
	.byte	9,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,7,237,5,3
	.word	19984
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,7,240,5,16,4,11
	.byte	'VCOBYST',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	396
	.byte	26,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,7,249,5,3
	.word	20170
	.byte	15
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,7,252,5,16,4,11
	.byte	'VCOBYST',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	396
	.byte	24,0,2,35,2,0,7
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,7,135,6,3
	.word	20368
	.byte	15
	.byte	'_Ifx_SCU_PMCSR_Bits',0,7,138,6,16,4,11
	.byte	'REQSLP',0,1
	.word	243
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	243
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	243
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	396
	.byte	21,0,2,35,2,0,7
	.byte	'Ifx_SCU_PMCSR_Bits',0,7,145,6,3
	.word	20601
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,7,148,6,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	243
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	243
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	243
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	243
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	243
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	243
	.byte	2,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'WUTWKEN',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	243
	.byte	2,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	243
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,7,174,6,3
	.word	20753
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,7,177,6,16,4,11
	.byte	'reserved_0',0,2
	.word	202
	.byte	12,4,2,35,0,11
	.byte	'IRADIS',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	396
	.byte	14,5,2,35,2,11
	.byte	'STBYEVEN',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	243
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,7,185,6,3
	.word	21312
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,7,188,6,16,4,11
	.byte	'WUTREL',0,4
	.word	396
	.byte	24,8,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	4,4,2,35,3,11
	.byte	'WUTDIV',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'WUTEN',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'WUTMODE',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,7,196,6,3
	.word	21495
	.byte	15
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,7,199,6,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	243
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	243
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	243
	.byte	2,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'WUTWKP',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'WUTOVRUN',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'WUTWKEN',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	243
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	243
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	202
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	243
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'WUTEN',0,1
	.word	243
	.byte	1,2,2,35,3,11
	.byte	'WUTMODE',0,1
	.word	243
	.byte	1,1,2,35,3,11
	.byte	'WUTRUN',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,7,226,6,3
	.word	21664
	.byte	15
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,7,229,6,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'WUTWKPCLR',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'WUTOVRUNCLR',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	202
	.byte	14,0,2,35,2,0,7
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,7,242,6,3
	.word	22231
	.byte	15
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,7,245,6,16,4,11
	.byte	'WUTCNT',0,4
	.word	396
	.byte	24,8,2,35,2,11
	.byte	'reserved_24',0,1
	.word	243
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	243
	.byte	1,0,2,35,3,0,7
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,7,250,6,3
	.word	22547
	.byte	15
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,7,253,6,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	202
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	243
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	243
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	243
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_RSTCON2_Bits',0,7,135,7,3
	.word	22666
	.byte	15
	.byte	'_Ifx_SCU_RSTCON_Bits',0,7,138,7,16,4,11
	.byte	'ESR0',0,1
	.word	243
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	243
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	243
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	243
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	243
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	243
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	243
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	243
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_RSTCON_Bits',0,7,149,7,3
	.word	22875
	.byte	15
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,7,152,7,16,4,11
	.byte	'ESR0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	243
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	243
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	243
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	243
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	243
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	243
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	243
	.byte	3,0,2,35,3,0,7
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,7,175,7,3
	.word	23086
	.byte	15
	.byte	'_Ifx_SCU_SAFECON_Bits',0,7,178,7,16,4,11
	.byte	'HBT',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	396
	.byte	31,0,2,35,2,0,7
	.byte	'Ifx_SCU_SAFECON_Bits',0,7,182,7,3
	.word	23518
	.byte	15
	.byte	'_Ifx_SCU_STSTAT_Bits',0,7,185,7,16,4,11
	.byte	'HWCFG',0,1
	.word	243
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	243
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	243
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	243
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	243
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	243
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	243
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	243
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	243
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	243
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	243
	.byte	7,0,2,35,3,0,7
	.byte	'Ifx_SCU_STSTAT_Bits',0,7,198,7,3
	.word	23614
	.byte	15
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,7,201,7,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	396
	.byte	30,0,2,35,2,0,7
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,7,206,7,3
	.word	23874
	.byte	15
	.byte	'_Ifx_SCU_SYSCON_Bits',0,7,209,7,16,4,11
	.byte	'CCTRIG0',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	243
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	243
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	396
	.byte	23,0,2,35,2,0,7
	.byte	'Ifx_SCU_SYSCON_Bits',0,7,218,7,3
	.word	23999
	.byte	15
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,7,221,7,16,4,11
	.byte	'ESR0T',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	396
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,7,228,7,3
	.word	24196
	.byte	15
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,7,231,7,16,4,11
	.byte	'ESR0T',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	396
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,7,238,7,3
	.word	24349
	.byte	15
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,7,241,7,16,4,11
	.byte	'ESR0T',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	396
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_SCU_TRAPSET_Bits',0,7,248,7,3
	.word	24502
	.byte	15
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,7,251,7,16,4,11
	.byte	'ESR0T',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	396
	.byte	28,0,2,35,2,0,7
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,7,130,8,3
	.word	24655
	.byte	15
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,7,133,8,16,4,3
	.byte	'unsigned int',0,4,7,11
	.byte	'ENDINIT',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	24842
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,7,139,8,3
	.word	24810
	.byte	15
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,7,142,8,16,4,11
	.byte	'reserved_0',0,1
	.word	243
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	243
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,7,154,8,3
	.word	24956
	.byte	15
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,7,157,8,16,4,11
	.byte	'AE',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	243
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,7,170,8,3
	.word	25194
	.byte	15
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,7,173,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	24842
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,7,179,8,3
	.word	25417
	.byte	15
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,7,182,8,16,4,11
	.byte	'CLRIRF',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	243
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,7,195,8,3
	.word	25543
	.byte	15
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,7,198,8,16,4,11
	.byte	'AE',0,1
	.word	243
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	243
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	243
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	243
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	243
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	243
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	243
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	243
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	243
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	243
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	202
	.byte	16,0,2,35,2,0,7
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,7,211,8,3
	.word	25795
	.byte	16,7,219,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	9074
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ACCEN0',0,7,224,8,3
	.word	26014
	.byte	16,7,227,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	9631
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ACCEN1',0,7,232,8,3
	.word	26078
	.byte	16,7,235,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	9708
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ARSTDIS',0,7,240,8,3
	.word	26142
	.byte	16,7,243,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	9844
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON0',0,7,248,8,3
	.word	26207
	.byte	16,7,251,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	10126
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON1',0,7,128,9,3
	.word	26272
	.byte	16,7,131,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	10364
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON2',0,7,136,9,3
	.word	26337
	.byte	16,7,139,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	10492
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON3',0,7,144,9,3
	.word	26402
	.byte	16,7,147,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	10719
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON4',0,7,152,9,3
	.word	26467
	.byte	16,7,155,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	10938
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON5',0,7,160,9,3
	.word	26532
	.byte	16,7,163,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	11066
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CCUCON6',0,7,168,9,3
	.word	26597
	.byte	16,7,171,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	11166
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_CHIPID',0,7,176,9,3
	.word	26662
	.byte	16,7,179,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	11374
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_DTSCON',0,7,184,9,3
	.word	26726
	.byte	16,7,187,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	11539
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_DTSLIM',0,7,192,9,3
	.word	26790
	.byte	16,7,195,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	11722
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_DTSSTAT',0,7,200,9,3
	.word	26854
	.byte	16,7,203,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	11876
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EICR',0,7,208,9,3
	.word	26919
	.byte	16,7,211,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	12240
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EIFR',0,7,216,9,3
	.word	26981
	.byte	16,7,219,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	12451
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EMSR',0,7,224,9,3
	.word	27043
	.byte	16,7,227,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	12703
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ESRCFG',0,7,232,9,3
	.word	27105
	.byte	16,7,235,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	12821
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ESROCFG',0,7,240,9,3
	.word	27169
	.byte	16,7,243,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	12932
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVR13CON',0,7,248,9,3
	.word	27234
	.byte	16,7,251,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13095
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRADCSTAT',0,7,128,10,3
	.word	27300
	.byte	16,7,131,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13257
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRMONCTRL',0,7,136,10,3
	.word	27368
	.byte	16,7,139,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13535
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVROVMON',0,7,144,10,3
	.word	27436
	.byte	16,7,147,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13714
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRRSTCON',0,7,152,10,3
	.word	27502
	.byte	16,7,155,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	13874
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,7,160,10,3
	.word	27569
	.byte	16,7,163,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14035
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSDCTRL1',0,7,168,10,3
	.word	27638
	.byte	16,7,171,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14227
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSDCTRL2',0,7,176,10,3
	.word	27706
	.byte	16,7,179,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14523
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSDCTRL3',0,7,184,10,3
	.word	27774
	.byte	16,7,187,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	14738
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRSTAT',0,7,192,10,3
	.word	27842
	.byte	16,7,195,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15027
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EVRUVMON',0,7,200,10,3
	.word	27907
	.byte	16,7,203,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15206
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_EXTCON',0,7,208,10,3
	.word	27973
	.byte	16,7,211,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15424
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_FDR',0,7,216,10,3
	.word	28037
	.byte	16,7,219,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15587
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_FMR',0,7,224,10,3
	.word	28098
	.byte	16,7,227,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	15923
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_ID',0,7,232,10,3
	.word	28159
	.byte	16,7,235,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16030
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_IGCR',0,7,240,10,3
	.word	28219
	.byte	16,7,243,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16482
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_IN',0,7,248,10,3
	.word	28281
	.byte	16,7,251,10,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16581
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_IOCR',0,7,128,11,3
	.word	28341
	.byte	16,7,131,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16731
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LBISTCTRL0',0,7,136,11,3
	.word	28403
	.byte	16,7,139,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	16880
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LBISTCTRL1',0,7,144,11,3
	.word	28471
	.byte	16,7,147,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17041
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LBISTCTRL2',0,7,152,11,3
	.word	28539
	.byte	16,7,155,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17171
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LCLCON0',0,7,160,11,3
	.word	28607
	.byte	16,7,163,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17305
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_LCLTEST',0,7,168,11,3
	.word	28672
	.byte	16,7,171,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17420
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_MANID',0,7,176,11,3
	.word	28737
	.byte	16,7,179,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17531
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OMR',0,7,184,11,3
	.word	28800
	.byte	16,7,187,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	17689
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OSCCON',0,7,192,11,3
	.word	28861
	.byte	16,7,195,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18029
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OUT',0,7,200,11,3
	.word	28925
	.byte	16,7,203,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18130
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OVCCON',0,7,208,11,3
	.word	28986
	.byte	16,7,211,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18397
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_OVCENABLE',0,7,216,11,3
	.word	29050
	.byte	16,7,219,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18533
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PDISC',0,7,224,11,3
	.word	29117
	.byte	16,7,227,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18644
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PDR',0,7,232,11,3
	.word	29180
	.byte	16,7,235,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18777
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PDRR',0,7,240,11,3
	.word	29241
	.byte	16,7,243,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	18980
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLCON0',0,7,248,11,3
	.word	29303
	.byte	16,7,251,11,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19336
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLCON1',0,7,128,12,3
	.word	29368
	.byte	16,7,131,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19514
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLCON2',0,7,136,12,3
	.word	29433
	.byte	16,7,139,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19614
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLERAYCON0',0,7,144,12,3
	.word	29498
	.byte	16,7,147,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	19984
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLERAYCON1',0,7,152,12,3
	.word	29567
	.byte	16,7,155,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20170
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLERAYSTAT',0,7,160,12,3
	.word	29636
	.byte	16,7,163,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20368
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PLLSTAT',0,7,168,12,3
	.word	29705
	.byte	16,7,171,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20601
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMCSR',0,7,176,12,3
	.word	29770
	.byte	16,7,179,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	20753
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWCR0',0,7,184,12,3
	.word	29833
	.byte	16,7,187,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21312
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWCR1',0,7,192,12,3
	.word	29898
	.byte	16,7,195,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21495
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWCR3',0,7,200,12,3
	.word	29963
	.byte	16,7,203,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	21664
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWSTAT',0,7,208,12,3
	.word	30028
	.byte	16,7,211,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22231
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWSTATCLR',0,7,216,12,3
	.word	30094
	.byte	16,7,219,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22547
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_PMSWUTCNT',0,7,224,12,3
	.word	30163
	.byte	16,7,227,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22875
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_RSTCON',0,7,232,12,3
	.word	30230
	.byte	16,7,235,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	22666
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_RSTCON2',0,7,240,12,3
	.word	30294
	.byte	16,7,243,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23086
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_RSTSTAT',0,7,248,12,3
	.word	30359
	.byte	16,7,251,12,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23518
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_SAFECON',0,7,128,13,3
	.word	30424
	.byte	16,7,131,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23614
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_STSTAT',0,7,136,13,3
	.word	30489
	.byte	16,7,139,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23874
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_SWRSTCON',0,7,144,13,3
	.word	30553
	.byte	16,7,147,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	23999
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_SYSCON',0,7,152,13,3
	.word	30619
	.byte	16,7,155,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24196
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_TRAPCLR',0,7,160,13,3
	.word	30683
	.byte	16,7,163,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24349
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_TRAPDIS',0,7,168,13,3
	.word	30748
	.byte	16,7,171,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24502
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_TRAPSET',0,7,176,13,3
	.word	30813
	.byte	16,7,179,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24655
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_TRAPSTAT',0,7,184,13,3
	.word	30878
	.byte	16,7,187,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24810
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTCPU_CON0',0,7,192,13,3
	.word	30944
	.byte	16,7,195,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	24956
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTCPU_CON1',0,7,200,13,3
	.word	31013
	.byte	16,7,203,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	25194
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTCPU_SR',0,7,208,13,3
	.word	31082
	.byte	16,7,211,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	25417
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTS_CON0',0,7,216,13,3
	.word	31149
	.byte	16,7,219,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	25543
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTS_CON1',0,7,224,13,3
	.word	31216
	.byte	16,7,227,13,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	25795
	.byte	2,35,0,0,7
	.byte	'Ifx_SCU_WDTS_SR',0,7,232,13,3
	.word	31283
	.byte	15
	.byte	'_Ifx_SCU_WDTCPU',0,7,243,13,25,12,12
	.byte	'CON0',0,4
	.word	30944
	.byte	2,35,0,12
	.byte	'CON1',0,4
	.word	31013
	.byte	2,35,4,12
	.byte	'SR',0,4
	.word	31082
	.byte	2,35,8,0,17
	.word	31348
	.byte	7
	.byte	'Ifx_SCU_WDTCPU',0,7,248,13,3
	.word	31411
	.byte	15
	.byte	'_Ifx_SCU_WDTS',0,7,251,13,25,12,12
	.byte	'CON0',0,4
	.word	31149
	.byte	2,35,0,12
	.byte	'CON1',0,4
	.word	31216
	.byte	2,35,4,12
	.byte	'SR',0,4
	.word	31283
	.byte	2,35,8,0,17
	.word	31440
	.byte	7
	.byte	'Ifx_SCU_WDTS',0,7,128,14,3
	.word	31501
	.byte	15
	.byte	'_Ifx_CPU_A_Bits',0,8,45,16,4,11
	.byte	'ADDR',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_A_Bits',0,8,48,3
	.word	31528
	.byte	15
	.byte	'_Ifx_CPU_BIV_Bits',0,8,51,16,4,11
	.byte	'VSS',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	24842
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_BIV_Bits',0,8,55,3
	.word	31589
	.byte	15
	.byte	'_Ifx_CPU_BTV_Bits',0,8,58,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	24842
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_BTV_Bits',0,8,62,3
	.word	31668
	.byte	15
	.byte	'_Ifx_CPU_CCNT_Bits',0,8,65,16,4,11
	.byte	'CountValue',0,4
	.word	24842
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	24842
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_CCNT_Bits',0,8,69,3
	.word	31754
	.byte	15
	.byte	'_Ifx_CPU_CCTRL_Bits',0,8,72,16,4,11
	.byte	'CM',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	24842
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	24842
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	24842
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	24842
	.byte	21,0,2,35,0,0,7
	.byte	'Ifx_CPU_CCTRL_Bits',0,8,80,3
	.word	31843
	.byte	15
	.byte	'_Ifx_CPU_COMPAT_Bits',0,8,83,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	24842
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_COMPAT_Bits',0,8,89,3
	.word	31989
	.byte	15
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,8,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	24842
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_CORE_ID_Bits',0,8,96,3
	.word	32116
	.byte	15
	.byte	'_Ifx_CPU_CPR_L_Bits',0,8,99,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	24842
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_CPR_L_Bits',0,8,103,3
	.word	32214
	.byte	15
	.byte	'_Ifx_CPU_CPR_U_Bits',0,8,106,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	24842
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_CPR_U_Bits',0,8,110,3
	.word	32307
	.byte	15
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,8,113,16,4,11
	.byte	'MODREV',0,4
	.word	24842
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	24842
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_CPU_ID_Bits',0,8,118,3
	.word	32400
	.byte	15
	.byte	'_Ifx_CPU_CPXE_Bits',0,8,121,16,4,11
	.byte	'XE',0,4
	.word	24842
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	24842
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_CPXE_Bits',0,8,125,3
	.word	32507
	.byte	15
	.byte	'_Ifx_CPU_CREVT_Bits',0,8,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	24842
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	24842
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	24842
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_CREVT_Bits',0,8,136,1,3
	.word	32594
	.byte	15
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,8,139,1,16,4,11
	.byte	'CID',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	24842
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_CUS_ID_Bits',0,8,143,1,3
	.word	32748
	.byte	15
	.byte	'_Ifx_CPU_D_Bits',0,8,146,1,16,4,11
	.byte	'DATA',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_D_Bits',0,8,149,1,3
	.word	32842
	.byte	15
	.byte	'_Ifx_CPU_DATR_Bits',0,8,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	24842
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	24842
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	24842
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	24842
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	24842
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	24842
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_DATR_Bits',0,8,163,1,3
	.word	32905
	.byte	15
	.byte	'_Ifx_CPU_DBGSR_Bits',0,8,166,1,16,4,11
	.byte	'DE',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	24842
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	24842
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	24842
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	24842
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	24842
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	24842
	.byte	19,0,2,35,0,0,7
	.byte	'Ifx_CPU_DBGSR_Bits',0,8,177,1,3
	.word	33123
	.byte	15
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,8,180,1,16,4,11
	.byte	'DTA',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	24842
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_DBGTCR_Bits',0,8,184,1,3
	.word	33338
	.byte	15
	.byte	'_Ifx_CPU_DCON0_Bits',0,8,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	24842
	.byte	30,0,2,35,0,0,7
	.byte	'Ifx_CPU_DCON0_Bits',0,8,192,1,3
	.word	33432
	.byte	15
	.byte	'_Ifx_CPU_DCON2_Bits',0,8,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	24842
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_DCON2_Bits',0,8,199,1,3
	.word	33548
	.byte	15
	.byte	'_Ifx_CPU_DCX_Bits',0,8,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	24842
	.byte	26,0,2,35,0,0,7
	.byte	'Ifx_CPU_DCX_Bits',0,8,206,1,3
	.word	33649
	.byte	15
	.byte	'_Ifx_CPU_DEADD_Bits',0,8,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_DEADD_Bits',0,8,212,1,3
	.word	33742
	.byte	15
	.byte	'_Ifx_CPU_DIEAR_Bits',0,8,215,1,16,4,11
	.byte	'TA',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_DIEAR_Bits',0,8,218,1,3
	.word	33822
	.byte	15
	.byte	'_Ifx_CPU_DIETR_Bits',0,8,221,1,16,4,11
	.byte	'IED',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	24842
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	24842
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	24842
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	24842
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	24842
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	24842
	.byte	18,0,2,35,0,0,7
	.byte	'Ifx_CPU_DIETR_Bits',0,8,233,1,3
	.word	33891
	.byte	15
	.byte	'_Ifx_CPU_DMS_Bits',0,8,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	24842
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_DMS_Bits',0,8,240,1,3
	.word	34120
	.byte	15
	.byte	'_Ifx_CPU_DPR_L_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	24842
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_DPR_L_Bits',0,8,247,1,3
	.word	34213
	.byte	15
	.byte	'_Ifx_CPU_DPR_U_Bits',0,8,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	24842
	.byte	29,0,2,35,0,0,7
	.byte	'Ifx_CPU_DPR_U_Bits',0,8,254,1,3
	.word	34308
	.byte	15
	.byte	'_Ifx_CPU_DPRE_Bits',0,8,129,2,16,4,11
	.byte	'RE',0,4
	.word	24842
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_DPRE_Bits',0,8,133,2,3
	.word	34403
	.byte	15
	.byte	'_Ifx_CPU_DPWE_Bits',0,8,136,2,16,4,11
	.byte	'WE',0,4
	.word	24842
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_DPWE_Bits',0,8,140,2,3
	.word	34493
	.byte	15
	.byte	'_Ifx_CPU_DSTR_Bits',0,8,143,2,16,4,11
	.byte	'SRE',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	24842
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	24842
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	24842
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	24842
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	24842
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	24842
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	24842
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	24842
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	24842
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	24842
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	24842
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	24842
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	24842
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	24842
	.byte	7,0,2,35,0,0,7
	.byte	'Ifx_CPU_DSTR_Bits',0,8,161,2,3
	.word	34583
	.byte	15
	.byte	'_Ifx_CPU_EXEVT_Bits',0,8,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	24842
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	24842
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	24842
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_EXEVT_Bits',0,8,172,2,3
	.word	34907
	.byte	15
	.byte	'_Ifx_CPU_FCX_Bits',0,8,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	24842
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	24842
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	24842
	.byte	12,0,2,35,0,0,7
	.byte	'Ifx_CPU_FCX_Bits',0,8,180,2,3
	.word	35061
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,8,183,2,16,4,11
	.byte	'TST',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	24842
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	24842
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	24842
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	24842
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	24842
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	24842
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	24842
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	24842
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	24842
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	24842
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	24842
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	24842
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	24842
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	24842
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	24842
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,8,202,2,3
	.word	35167
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,8,205,2,16,4,11
	.byte	'OPC',0,4
	.word	24842
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	24842
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	24842
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	24842
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	24842
	.byte	12,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,8,212,2,3
	.word	35516
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,8,215,2,16,4,11
	.byte	'PC',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,8,218,2,3
	.word	35676
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,8,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,8,224,2,3
	.word	35757
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,8,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,8,230,2,3
	.word	35844
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,8,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,8,236,2,3
	.word	35931
	.byte	15
	.byte	'_Ifx_CPU_ICNT_Bits',0,8,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	24842
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	24842
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_ICNT_Bits',0,8,243,2,3
	.word	36018
	.byte	15
	.byte	'_Ifx_CPU_ICR_Bits',0,8,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	24842
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	24842
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	24842
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	24842
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	24842
	.byte	6,0,2,35,0,0,7
	.byte	'Ifx_CPU_ICR_Bits',0,8,253,2,3
	.word	36109
	.byte	15
	.byte	'_Ifx_CPU_ISP_Bits',0,8,128,3,16,4,11
	.byte	'ISP',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_ISP_Bits',0,8,131,3,3
	.word	36252
	.byte	15
	.byte	'_Ifx_CPU_LCX_Bits',0,8,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	24842
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	24842
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	24842
	.byte	12,0,2,35,0,0,7
	.byte	'Ifx_CPU_LCX_Bits',0,8,139,3,3
	.word	36318
	.byte	15
	.byte	'_Ifx_CPU_M1CNT_Bits',0,8,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	24842
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	24842
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_M1CNT_Bits',0,8,146,3,3
	.word	36424
	.byte	15
	.byte	'_Ifx_CPU_M2CNT_Bits',0,8,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	24842
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	24842
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_M2CNT_Bits',0,8,153,3,3
	.word	36517
	.byte	15
	.byte	'_Ifx_CPU_M3CNT_Bits',0,8,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	24842
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	24842
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_M3CNT_Bits',0,8,160,3,3
	.word	36610
	.byte	15
	.byte	'_Ifx_CPU_PC_Bits',0,8,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	24842
	.byte	31,0,2,35,0,0,7
	.byte	'Ifx_CPU_PC_Bits',0,8,167,3,3
	.word	36703
	.byte	15
	.byte	'_Ifx_CPU_PCON0_Bits',0,8,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	24842
	.byte	30,0,2,35,0,0,7
	.byte	'Ifx_CPU_PCON0_Bits',0,8,175,3,3
	.word	36788
	.byte	15
	.byte	'_Ifx_CPU_PCON1_Bits',0,8,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	24842
	.byte	30,0,2,35,0,0,7
	.byte	'Ifx_CPU_PCON1_Bits',0,8,183,3,3
	.word	36904
	.byte	15
	.byte	'_Ifx_CPU_PCON2_Bits',0,8,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	24842
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_PCON2_Bits',0,8,190,3,3
	.word	37015
	.byte	15
	.byte	'_Ifx_CPU_PCXI_Bits',0,8,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	24842
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	24842
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	24842
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	24842
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	24842
	.byte	10,0,2,35,0,0,7
	.byte	'Ifx_CPU_PCXI_Bits',0,8,200,3,3
	.word	37116
	.byte	15
	.byte	'_Ifx_CPU_PIEAR_Bits',0,8,203,3,16,4,11
	.byte	'TA',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_PIEAR_Bits',0,8,206,3,3
	.word	37246
	.byte	15
	.byte	'_Ifx_CPU_PIETR_Bits',0,8,209,3,16,4,11
	.byte	'IED',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	24842
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	24842
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	24842
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	24842
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	24842
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	24842
	.byte	18,0,2,35,0,0,7
	.byte	'Ifx_CPU_PIETR_Bits',0,8,221,3,3
	.word	37315
	.byte	15
	.byte	'_Ifx_CPU_PMA0_Bits',0,8,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	24842
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_PMA0_Bits',0,8,229,3,3
	.word	37544
	.byte	15
	.byte	'_Ifx_CPU_PMA1_Bits',0,8,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	24842
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_PMA1_Bits',0,8,237,3,3
	.word	37657
	.byte	15
	.byte	'_Ifx_CPU_PMA2_Bits',0,8,240,3,16,4,11
	.byte	'PSI',0,4
	.word	24842
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	24842
	.byte	16,0,2,35,0,0,7
	.byte	'Ifx_CPU_PMA2_Bits',0,8,244,3,3
	.word	37770
	.byte	15
	.byte	'_Ifx_CPU_PSTR_Bits',0,8,247,3,16,4,11
	.byte	'FRE',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	24842
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	24842
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	24842
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	24842
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	24842
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	24842
	.byte	17,0,2,35,0,0,7
	.byte	'Ifx_CPU_PSTR_Bits',0,8,129,4,3
	.word	37861
	.byte	15
	.byte	'_Ifx_CPU_PSW_Bits',0,8,132,4,16,4,11
	.byte	'CDC',0,4
	.word	24842
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	24842
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	24842
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	24842
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	24842
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	24842
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	24842
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	24842
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	24842
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	24842
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	24842
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	24842
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	24842
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_PSW_Bits',0,8,147,4,3
	.word	38064
	.byte	15
	.byte	'_Ifx_CPU_SEGEN_Bits',0,8,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	24842
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	24842
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	24842
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	24842
	.byte	1,0,2,35,0,0,7
	.byte	'Ifx_CPU_SEGEN_Bits',0,8,156,4,3
	.word	38307
	.byte	15
	.byte	'_Ifx_CPU_SMACON_Bits',0,8,159,4,16,4,11
	.byte	'PC',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	24842
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	24842
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	24842
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	24842
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	24842
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	24842
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	24842
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	24842
	.byte	7,0,2,35,0,0,7
	.byte	'Ifx_CPU_SMACON_Bits',0,8,171,4,3
	.word	38435
	.byte	15
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,8,174,4,16,4,11
	.byte	'EN',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,8,177,4,3
	.word	38676
	.byte	15
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,8,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,8,183,4,3
	.word	38759
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,8,186,4,16,4,11
	.byte	'EN',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,8,189,4,3
	.word	38850
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,8,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,8,195,4,3
	.word	38941
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,8,198,4,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	5,27,2,35,0,11
	.byte	'ADDR',0,4
	.word	24842
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,8,202,4,3
	.word	39040
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,8,205,4,16,4,11
	.byte	'reserved_0',0,4
	.word	24842
	.byte	5,27,2,35,0,11
	.byte	'ADDR',0,4
	.word	24842
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,8,209,4,3
	.word	39147
	.byte	15
	.byte	'_Ifx_CPU_SWEVT_Bits',0,8,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	24842
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	24842
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	24842
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_SWEVT_Bits',0,8,220,4,3
	.word	39254
	.byte	15
	.byte	'_Ifx_CPU_SYSCON_Bits',0,8,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	24842
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	24842
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_SYSCON_Bits',0,8,231,4,3
	.word	39408
	.byte	15
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,8,234,4,16,4,11
	.byte	'ASI',0,4
	.word	24842
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	24842
	.byte	27,0,2,35,0,0,7
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,8,238,4,3
	.word	39569
	.byte	15
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,8,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	24842
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	24842
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	24842
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	24842
	.byte	15,0,2,35,0,0,7
	.byte	'Ifx_CPU_TPS_CON_Bits',0,8,249,4,3
	.word	39667
	.byte	15
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,8,252,4,16,4,11
	.byte	'Timer',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,8,255,4,3
	.word	39839
	.byte	15
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,8,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	24842
	.byte	32,0,2,35,0,0,7
	.byte	'Ifx_CPU_TR_ADR_Bits',0,8,133,5,3
	.word	39919
	.byte	15
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,8,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	24842
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	24842
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	24842
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	24842
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	24842
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	24842
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	24842
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	24842
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	24842
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	24842
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	24842
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	24842
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	24842
	.byte	3,0,2,35,0,0,7
	.byte	'Ifx_CPU_TR_EVT_Bits',0,8,153,5,3
	.word	39992
	.byte	15
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,8,156,5,16,4,11
	.byte	'T0',0,4
	.word	24842
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	24842
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	24842
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	24842
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	24842
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	24842
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	24842
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	24842
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	24842
	.byte	24,0,2,35,0,0,7
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,8,167,5,3
	.word	40310
	.byte	16,8,175,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	31528
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_A',0,8,180,5,3
	.word	40505
	.byte	16,8,183,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	31589
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_BIV',0,8,188,5,3
	.word	40564
	.byte	16,8,191,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	31668
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_BTV',0,8,196,5,3
	.word	40625
	.byte	16,8,199,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	31754
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CCNT',0,8,204,5,3
	.word	40686
	.byte	16,8,207,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	31843
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CCTRL',0,8,212,5,3
	.word	40748
	.byte	16,8,215,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	31989
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_COMPAT',0,8,220,5,3
	.word	40811
	.byte	16,8,223,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32116
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CORE_ID',0,8,228,5,3
	.word	40875
	.byte	16,8,231,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32214
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CPR_L',0,8,236,5,3
	.word	40940
	.byte	16,8,239,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32307
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CPR_U',0,8,244,5,3
	.word	41003
	.byte	16,8,247,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32400
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CPU_ID',0,8,252,5,3
	.word	41066
	.byte	16,8,255,5,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32507
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CPXE',0,8,132,6,3
	.word	41130
	.byte	16,8,135,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32594
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CREVT',0,8,140,6,3
	.word	41192
	.byte	16,8,143,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32748
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_CUS_ID',0,8,148,6,3
	.word	41255
	.byte	16,8,151,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32842
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_D',0,8,156,6,3
	.word	41319
	.byte	16,8,159,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	32905
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DATR',0,8,164,6,3
	.word	41378
	.byte	16,8,167,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	33123
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DBGSR',0,8,172,6,3
	.word	41440
	.byte	16,8,175,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	33338
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DBGTCR',0,8,180,6,3
	.word	41503
	.byte	16,8,183,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	33432
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DCON0',0,8,188,6,3
	.word	41567
	.byte	16,8,191,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	33548
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DCON2',0,8,196,6,3
	.word	41630
	.byte	16,8,199,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	33649
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DCX',0,8,204,6,3
	.word	41693
	.byte	16,8,207,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	33742
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DEADD',0,8,212,6,3
	.word	41754
	.byte	16,8,215,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	33822
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DIEAR',0,8,220,6,3
	.word	41817
	.byte	16,8,223,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	33891
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DIETR',0,8,228,6,3
	.word	41880
	.byte	16,8,231,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	34120
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DMS',0,8,236,6,3
	.word	41943
	.byte	16,8,239,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	34213
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DPR_L',0,8,244,6,3
	.word	42004
	.byte	16,8,247,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	34308
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DPR_U',0,8,252,6,3
	.word	42067
	.byte	16,8,255,6,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	34403
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DPRE',0,8,132,7,3
	.word	42130
	.byte	16,8,135,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	34493
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DPWE',0,8,140,7,3
	.word	42192
	.byte	16,8,143,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	34583
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_DSTR',0,8,148,7,3
	.word	42254
	.byte	16,8,151,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	34907
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_EXEVT',0,8,156,7,3
	.word	42316
	.byte	16,8,159,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35061
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FCX',0,8,164,7,3
	.word	42379
	.byte	16,8,167,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35167
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,8,172,7,3
	.word	42440
	.byte	16,8,175,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35516
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,8,180,7,3
	.word	42510
	.byte	16,8,183,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35676
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,8,188,7,3
	.word	42580
	.byte	16,8,191,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35757
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,8,196,7,3
	.word	42649
	.byte	16,8,199,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35844
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,8,204,7,3
	.word	42720
	.byte	16,8,207,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	35931
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,8,212,7,3
	.word	42791
	.byte	16,8,215,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36018
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_ICNT',0,8,220,7,3
	.word	42862
	.byte	16,8,223,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36109
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_ICR',0,8,228,7,3
	.word	42924
	.byte	16,8,231,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36252
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_ISP',0,8,236,7,3
	.word	42985
	.byte	16,8,239,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36318
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_LCX',0,8,244,7,3
	.word	43046
	.byte	16,8,247,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36424
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_M1CNT',0,8,252,7,3
	.word	43107
	.byte	16,8,255,7,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36517
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_M2CNT',0,8,132,8,3
	.word	43170
	.byte	16,8,135,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36610
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_M3CNT',0,8,140,8,3
	.word	43233
	.byte	16,8,143,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36703
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PC',0,8,148,8,3
	.word	43296
	.byte	16,8,151,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36788
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PCON0',0,8,156,8,3
	.word	43356
	.byte	16,8,159,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	36904
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PCON1',0,8,164,8,3
	.word	43419
	.byte	16,8,167,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37015
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PCON2',0,8,172,8,3
	.word	43482
	.byte	16,8,175,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37116
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PCXI',0,8,180,8,3
	.word	43545
	.byte	16,8,183,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37246
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PIEAR',0,8,188,8,3
	.word	43607
	.byte	16,8,191,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37315
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PIETR',0,8,196,8,3
	.word	43670
	.byte	16,8,199,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37544
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PMA0',0,8,204,8,3
	.word	43733
	.byte	16,8,207,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37657
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PMA1',0,8,212,8,3
	.word	43795
	.byte	16,8,215,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37770
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PMA2',0,8,220,8,3
	.word	43857
	.byte	16,8,223,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	37861
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PSTR',0,8,228,8,3
	.word	43919
	.byte	16,8,231,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38064
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_PSW',0,8,236,8,3
	.word	43981
	.byte	16,8,239,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38307
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SEGEN',0,8,244,8,3
	.word	44042
	.byte	16,8,247,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38435
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SMACON',0,8,252,8,3
	.word	44105
	.byte	16,8,255,8,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38676
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_ACCENA',0,8,132,9,3
	.word	44169
	.byte	16,8,135,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38759
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_ACCENB',0,8,140,9,3
	.word	44239
	.byte	16,8,143,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38850
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,8,148,9,3
	.word	44309
	.byte	16,8,151,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	38941
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,8,156,9,3
	.word	44383
	.byte	16,8,159,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39040
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,8,164,9,3
	.word	44457
	.byte	16,8,167,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39147
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,8,172,9,3
	.word	44527
	.byte	16,8,175,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39254
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SWEVT',0,8,180,9,3
	.word	44597
	.byte	16,8,183,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39408
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_SYSCON',0,8,188,9,3
	.word	44660
	.byte	16,8,191,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39569
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TASK_ASI',0,8,196,9,3
	.word	44724
	.byte	16,8,199,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39667
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TPS_CON',0,8,204,9,3
	.word	44790
	.byte	16,8,207,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39839
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TPS_TIMER',0,8,212,9,3
	.word	44855
	.byte	16,8,215,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39919
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TR_ADR',0,8,220,9,3
	.word	44922
	.byte	16,8,223,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	39992
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TR_EVT',0,8,228,9,3
	.word	44986
	.byte	16,8,231,9,9,4,12
	.byte	'U',0,4
	.word	396
	.byte	2,35,0,12
	.byte	'I',0,4
	.word	7259
	.byte	2,35,0,12
	.byte	'B',0,4
	.word	40310
	.byte	2,35,0,0,7
	.byte	'Ifx_CPU_TRIG_ACC',0,8,236,9,3
	.word	45050
	.byte	15
	.byte	'_Ifx_CPU_CPR',0,8,247,9,25,8,12
	.byte	'L',0,4
	.word	40940
	.byte	2,35,0,12
	.byte	'U',0,4
	.word	41003
	.byte	2,35,4,0,17
	.word	45116
	.byte	7
	.byte	'Ifx_CPU_CPR',0,8,251,9,3
	.word	45158
	.byte	15
	.byte	'_Ifx_CPU_DPR',0,8,254,9,25,8,12
	.byte	'L',0,4
	.word	42004
	.byte	2,35,0,12
	.byte	'U',0,4
	.word	42067
	.byte	2,35,4,0,17
	.word	45184
	.byte	7
	.byte	'Ifx_CPU_DPR',0,8,130,10,3
	.word	45226
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN',0,8,133,10,25,16,12
	.byte	'LA',0,4
	.word	44457
	.byte	2,35,0,12
	.byte	'UA',0,4
	.word	44527
	.byte	2,35,4,12
	.byte	'ACCENA',0,4
	.word	44309
	.byte	2,35,8,12
	.byte	'ACCENB',0,4
	.word	44383
	.byte	2,35,12,0,17
	.word	45252
	.byte	7
	.byte	'Ifx_CPU_SPROT_RGN',0,8,139,10,3
	.word	45334
	.byte	15
	.byte	'_Ifx_CPU_TPS',0,8,142,10,25,16,12
	.byte	'CON',0,4
	.word	44790
	.byte	2,35,0,13,12
	.word	44855
	.byte	14,2,0,12
	.byte	'TIMER',0,12
	.word	45398
	.byte	2,35,4,0,17
	.word	45366
	.byte	7
	.byte	'Ifx_CPU_TPS',0,8,146,10,3
	.word	45423
	.byte	15
	.byte	'_Ifx_CPU_TR',0,8,149,10,25,8,12
	.byte	'EVT',0,4
	.word	44986
	.byte	2,35,0,12
	.byte	'ADR',0,4
	.word	44922
	.byte	2,35,4,0,17
	.word	45449
	.byte	7
	.byte	'Ifx_CPU_TR',0,8,153,10,3
	.word	45494
	.byte	7
	.byte	'_iob_flag_t',0,9,75,25
	.word	202
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L8:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,36,0,3,8,11,15,62,15,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,59,0,3,8,0,0,6,15,0,73,19,0,0,7,22
	.byte	0,3,8,58,15,59,15,57,15,73,19,0,0,8,21,0,54,15,0,0,9,23,1,3,8,58,15,59,15,57,15,11,15,0,0,10,19,1,58,15
	.byte	59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,13,0,3,8,11,15,73,19,56,9,0,0,13
	.byte	1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,19,1,3,8,58,15,59,15,57,15,11,15,0,0,16,23,1,58,15,59,15,57,15
	.byte	11,15,0,0,17,53,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L9:
	.word	.L26-.L25
.L25:
	.half	3
	.word	.L28-.L27
.L27:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0,0
	.byte	'Dio.h',0,1,0,0
	.byte	'..\\Src_file\\CDD_Wdg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Mcal_TcLib.h',0,1,0,0
	.byte	'Port.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'stdio.h',0,2,0,0,0
.L28:
.L26:
	.sdecl	'.debug_info',debug,cluster('CDD_Wdg_EN')
	.sect	'.debug_info'
.L10:
	.word	222
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\Src_file\\CDD_Wdg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L13,.L12
	.byte	2
	.word	.L6
	.byte	3
	.byte	'CDD_Wdg_EN',0,1,22,6,1,1,1
	.word	.L3,.L22,.L2
	.byte	4
	.word	.L3,.L22
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CDD_Wdg_EN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CDD_Wdg_EN')
	.sect	'.debug_line'
.L12:
	.word	.L30-.L29
.L29:
	.half	3
	.word	.L32-.L31
.L31:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_Wdg.c',0,0,0,0,0
.L32:
	.byte	5,19,7,0,5,2
	.word	.L3
	.byte	3,23,1,5,36,9
	.half	.L33-.L3
	.byte	1,5,1,9
	.half	.L14-.L33
	.byte	3,1,0,1,1
.L30:
	.sdecl	'.debug_ranges',debug,cluster('CDD_Wdg_EN')
	.sect	'.debug_ranges'
.L13:
	.word	-1,.L3,0,.L14-.L3,0,0
	.sdecl	'.debug_info',debug,cluster('CDD_Wdg_Dis')
	.sect	'.debug_info'
.L15:
	.word	223
	.half	3
	.word	.L16
	.byte	4,1
	.byte	'..\\Src_file\\CDD_Wdg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L18,.L17
	.byte	2
	.word	.L6
	.byte	3
	.byte	'CDD_Wdg_Dis',0,1,27,6,1,1,1
	.word	.L5,.L23,.L4
	.byte	4
	.word	.L5,.L23
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CDD_Wdg_Dis')
	.sect	'.debug_abbrev'
.L16:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CDD_Wdg_Dis')
	.sect	'.debug_line'
.L17:
	.word	.L35-.L34
.L34:
	.half	3
	.word	.L37-.L36
.L36:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_Wdg.c',0,0,0,0,0
.L37:
	.byte	5,19,7,0,5,2
	.word	.L5
	.byte	3,28,1,5,36,9
	.half	.L38-.L5
	.byte	1,5,1,9
	.half	.L19-.L38
	.byte	3,1,0,1,1
.L35:
	.sdecl	'.debug_ranges',debug,cluster('CDD_Wdg_Dis')
	.sect	'.debug_ranges'
.L18:
	.word	-1,.L5,0,.L19-.L5,0,0
	.sdecl	'.debug_info',debug,cluster('Wdg_Wdg1DemoStatusVar')
	.sect	'.debug_info'
.L20:
	.word	211
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\Src_file\\CDD_Wdg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L6
	.byte	3
	.byte	'Wdg_Wdg1DemoStatusVar',0,2,20,7
	.word	.L24
	.byte	1,5,3
	.word	Wdg_Wdg1DemoStatusVar
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Wdg_Wdg1DemoStatusVar')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('CDD_Wdg_Dis')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L23-.L5
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CDD_Wdg_EN')
	.sect	'.debug_loc'
.L2:
	.word	-1,.L3,0,.L22-.L3
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L39:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('CDD_Wdg_EN')
	.sect	'.debug_frame'
	.word	12
	.word	.L39,.L3,.L22-.L3
	.sdecl	'.debug_frame',debug,cluster('CDD_Wdg_Dis')
	.sect	'.debug_frame'
	.word	12
	.word	.L39,.L5,.L23-.L5

; ..\Src_file\CDD_Wdg.c	    30  }

	; Module end
