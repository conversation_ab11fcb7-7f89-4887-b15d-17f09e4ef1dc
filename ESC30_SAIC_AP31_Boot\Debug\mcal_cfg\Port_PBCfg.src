	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc10016a -c99 --dep-file=mcal_cfg\\.Port_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\Port_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\Port_PBCfg.src ..\\mcal_cfg\\Port_PBCfg.c"
	.compiler_name		"ctc"
	.name	"Port_PBCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Port_kConfiguration0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Port_kConfiguration0:	.type	object
	.size	Port_kConfiguration0,420
	.byte	128,128
	.space	2
	.byte	152
	.space	2
	.byte	128,128,128,16,16,136,16,16
	.byte	16
	.space	4
	.word	131106,131072
	.byte	168
	.space	1
	.byte	128,128,152
	.space	1
	.byte	152,152
	.space	1
	.byte	16,16,16,16
	.byte	16,16,16
	.space	4
	.word	570556418
	.space	4
	.byte	16
	.space	1
	.byte	152,152,16
	.space	2
	.byte	16,16,16,16,16,16,16,16
	.byte	16
	.space	4
	.word	8704
	.space	4
	.byte	16,16,128,128
	.byte	16,16
	.space	1
	.byte	16,128,128,128,160,128,16,16
	.byte	16
	.space	8
	.word	8224
	.byte	128,128,128,128,16,16,16,16
	.byte	16,16,16,16,16,16,16,16
	.space	12
	.byte	128
	.space	4
	.byte	128
	.space	1
	.byte	128,128,16,16,16,16,16,16
	.byte	16
	.space	12
	.byte	128,128,152,152
	.space	1
	.byte	152
	.space	1
	.byte	128
	.space	1
	.byte	16,16,16,16
	.byte	16,16,16
	.space	4
	.word	2105856
	.space	5
	.byte	16
	.space	1
	.byte	128,16,16,128,128,128,128,152
	.byte	128
	.space	1
	.byte	168,152,16
	.space	8
	.word	35652096
	.byte	16,16
	.space	3
	.byte	128
	.space	1
	.byte	16,16,16,16,16,16,16,16
	.byte	16
	.space	12
	.byte	128,128,128,128,128,16,16,16
	.byte	16,16,16,16,16,16,16,16
	.space	12
	.byte	16,128,16,16,16,16,16,16
	.byte	16,16,16,16,16,16,16,16
	.space	12
	.byte	128,16,136,128
	.byte	128
	.space	1
	.byte	128
	.space	1
	.byte	128
	.space	1
	.byte	136
	.space	1
	.byte	128,16,16,16
	.space	1
	.byte	4
	.space	10
	.byte	16,128,128,136,16,16,16,16
	.byte	16,16,16,16,16,16,16,16
	.byte	8
	.space	23
	.byte	16,16,16,16
	.byte	64
	.space	23
	.byte	16,16,16,16
	.space	12
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Port_DiscSet0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Port_DiscSet0:	.type	object
	.size	Port_DiscSet0,4
	.half	3064,3199
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Port_kPCSRConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Port_kPCSRConfig0:	.type	object
	.size	Port_kPCSRConfig0,8
	.space	8
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Port_ConfigRoot')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.global	Port_ConfigRoot
	.align	4
Port_ConfigRoot:	.type	object
	.size	Port_ConfigRoot,12
	.word	Port_kConfiguration0,Port_DiscSet0,Port_kPCSRConfig0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1340
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\Port_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	179
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	185
	.byte	5,1,3
	.word	209
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	211
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	234
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	265
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	302
	.byte	6
	.byte	'unsigned int',0,4,7,4
	.byte	'unsigned_int',0,3,121,22
	.word	338
	.byte	7
	.byte	'Port_n_PinType',0,4,176,2,15,4,8,4,178,2,3,2,9
	.byte	'P0',0,1
	.word	234
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	234
	.byte	1,6,2,35,0,9
	.byte	'P2',0,1
	.word	234
	.byte	1,5,2,35,0,9
	.byte	'P3',0,1
	.word	234
	.byte	1,4,2,35,0,9
	.byte	'P4',0,1
	.word	234
	.byte	1,3,2,35,0,9
	.byte	'P5',0,1
	.word	234
	.byte	1,2,2,35,0,9
	.byte	'P6',0,1
	.word	234
	.byte	1,1,2,35,0,9
	.byte	'P7',0,1
	.word	234
	.byte	1,0,2,35,0,9
	.byte	'P8',0,1
	.word	234
	.byte	1,7,2,35,1,9
	.byte	'P9',0,1
	.word	234
	.byte	1,6,2,35,1,9
	.byte	'P10',0,1
	.word	234
	.byte	1,5,2,35,1,9
	.byte	'P11',0,1
	.word	234
	.byte	1,4,2,35,1,9
	.byte	'P12',0,1
	.word	234
	.byte	1,3,2,35,1,9
	.byte	'P13',0,1
	.word	234
	.byte	1,2,2,35,1,9
	.byte	'P14',0,1
	.word	234
	.byte	1,1,2,35,1,9
	.byte	'P15',0,1
	.word	234
	.byte	1,0,2,35,1,0,10
	.byte	'B',0,2
	.word	396
	.byte	2,35,0,10
	.byte	'U',0,4
	.word	302
	.byte	2,35,0,0,4
	.byte	'Port_n_PinType',0,4,203,2,3
	.word	375
	.byte	7
	.byte	'Port_n_ControlType',0,4,206,2,15,16,8,4,208,2,3,16,10
	.byte	'PC0',0,1
	.word	234
	.byte	2,35,0,10
	.byte	'PC1',0,1
	.word	234
	.byte	2,35,1,10
	.byte	'PC2',0,1
	.word	234
	.byte	2,35,2,10
	.byte	'PC3',0,1
	.word	234
	.byte	2,35,3,10
	.byte	'PC4',0,1
	.word	234
	.byte	2,35,4,10
	.byte	'PC5',0,1
	.word	234
	.byte	2,35,5,10
	.byte	'PC6',0,1
	.word	234
	.byte	2,35,6,10
	.byte	'PC7',0,1
	.word	234
	.byte	2,35,7,10
	.byte	'PC8',0,1
	.word	234
	.byte	2,35,8,10
	.byte	'PC9',0,1
	.word	234
	.byte	2,35,9,10
	.byte	'PC10',0,1
	.word	234
	.byte	2,35,10,10
	.byte	'PC11',0,1
	.word	234
	.byte	2,35,11,10
	.byte	'PC12',0,1
	.word	234
	.byte	2,35,12,10
	.byte	'PC13',0,1
	.word	234
	.byte	2,35,13,10
	.byte	'PC14',0,1
	.word	234
	.byte	2,35,14,10
	.byte	'PC15',0,1
	.word	234
	.byte	2,35,15,0,10
	.byte	'B',0,16
	.word	705
	.byte	2,35,0,11,16
	.word	302
	.byte	12,3,0,10
	.byte	'U',0,16
	.word	937
	.byte	2,35,0,0,4
	.byte	'Port_n_ControlType',0,4,233,2,2
	.word	680
	.byte	13
	.byte	'Port_n_ConfigType',0,4,140,3,16,28,10
	.byte	'PinControl',0,16
	.word	680
	.byte	2,35,0,10
	.byte	'PinLevel',0,4
	.word	375
	.byte	2,35,16,10
	.byte	'DriverStrength0',0,4
	.word	302
	.byte	2,35,20,10
	.byte	'DriverStrength1',0,4
	.word	302
	.byte	2,35,24,0,4
	.byte	'Port_n_ConfigType',0,4,164,3,2
	.word	986
	.byte	4
	.byte	'Port_n_PCSRConfigType',0,4,167,3,16
	.word	302
	.byte	13
	.byte	'Port_ConfigType',0,4,176,3,16,12,14
	.word	986
	.byte	3
	.word	1179
	.byte	10
	.byte	'PortConfigSetPtr',0,4
	.word	1184
	.byte	2,35,0,14
	.word	265
	.byte	3
	.word	1215
	.byte	10
	.byte	'PDiscSet',0,4
	.word	1220
	.byte	2,35,4,14
	.word	302
	.byte	3
	.word	1243
	.byte	10
	.byte	'Port_PCSRConfigTypePtr',0,4
	.word	1248
	.byte	2,35,8,0,11,12
	.word	1157
	.byte	12,0,0
.L14:
	.byte	14
	.word	1286
	.byte	11,164,3
	.word	986
	.byte	12,14,0
.L15:
	.byte	14
	.word	1300
	.byte	11,4
	.word	265
	.byte	12,1,0
.L16:
	.byte	14
	.word	1315
	.byte	11,8
	.word	302
	.byte	12,1,0
.L17:
	.byte	14
	.word	1329
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,23,1,3,8,58,15,59,15,57,15,11,15,0,0,8,19,1
	.byte	58,15,59,15,57,15,11,15,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,13,0,3,8,11,15,73,19,56,9,0
	.byte	0,11,1,1,11,15,73,19,0,0,12,33,0,47,15,0,0,13,19,1,3,8,58,15,59,15,57,15,11,15,0,0,14,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L19-.L18
.L18:
	.half	3
	.word	.L21-.L20
.L20:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'..\\mcal_cfg\\Port_PBCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Mcal_TcLib.h',0,1,0,0
	.byte	'Port.h',0,1,0,0,0
.L21:
.L19:
	.sdecl	'.debug_info',debug,cluster('Port_ConfigRoot')
	.sect	'.debug_info'
.L6:
	.word	209
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\Port_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Port_ConfigRoot',0,1,135,10,23
	.word	.L14
	.byte	1,5,3
	.word	Port_ConfigRoot
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Port_ConfigRoot')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Port_kConfiguration0')
	.sect	'.debug_info'
.L8:
	.word	213
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_cfg\\Port_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Port_kConfiguration0',0,1,187,1,32
	.word	.L15
	.byte	5,3
	.word	Port_kConfiguration0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Port_kConfiguration0')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Port_DiscSet0')
	.sect	'.debug_info'
.L10:
	.word	206
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\mcal_cfg\\Port_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Port_DiscSet0',0,1,172,9,21
	.word	.L16
	.byte	5,3
	.word	Port_DiscSet0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Port_DiscSet0')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Port_kPCSRConfig0')
	.sect	'.debug_info'
.L12:
	.word	210
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\mcal_cfg\\Port_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Port_kPCSRConfig0',0,1,215,9,36
	.word	.L17
	.byte	5,3
	.word	Port_kPCSRConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Port_kPCSRConfig0')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0

; ..\mcal_cfg\Port_PBCfg.c	     1  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	     2  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_cfg\Port_PBCfg.c	     4  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	     5  ** All rights reserved.                                                       **
; ..\mcal_cfg\Port_PBCfg.c	     6  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_cfg\Port_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_cfg\Port_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_cfg\Port_PBCfg.c	    10  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    11  ********************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	    12  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    13  **  FILENAME  : Port_PBCfg.c                                                  **
; ..\mcal_cfg\Port_PBCfg.c	    14  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    15  **  $CC VERSION : \main\dev_tc23x\7 $                                        **
; ..\mcal_cfg\Port_PBCfg.c	    16  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    17  **  DATE, TIME: 2025-02-19, 14:27:52                                      **
; ..\mcal_cfg\Port_PBCfg.c	    18  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    19  **  GENERATOR : Build b141014-0350                                          **
; ..\mcal_cfg\Port_PBCfg.c	    20  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    21  **  AUTHOR    : DL-AUTOSAR-Engineering                                        **
; ..\mcal_cfg\Port_PBCfg.c	    22  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    23  **  VENDOR    : Infineon Technologies                                         **
; ..\mcal_cfg\Port_PBCfg.c	    24  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    25  **  DESCRIPTION  : Port configuration generated from ECU 
; ..\mcal_cfg\Port_PBCfg.c	    26                     configuration file ( Port.bmd)                             **
; ..\mcal_cfg\Port_PBCfg.c	    27  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    28  **  MAY BE CHANGED BY USER [yes/no]: No                                       **
; ..\mcal_cfg\Port_PBCfg.c	    29  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    30  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	    31  
; ..\mcal_cfg\Port_PBCfg.c	    32   
; ..\mcal_cfg\Port_PBCfg.c	    33  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	    34  **                      Includes                                              **
; ..\mcal_cfg\Port_PBCfg.c	    35  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	    36  /* Inclusion of Port Header file */
; ..\mcal_cfg\Port_PBCfg.c	    37  #include "Port.h"
; ..\mcal_cfg\Port_PBCfg.c	    38  
; ..\mcal_cfg\Port_PBCfg.c	    39  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	    40  **                      Private Macro Definitions                             **
; ..\mcal_cfg\Port_PBCfg.c	    41  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	    42  
; ..\mcal_cfg\Port_PBCfg.c	    43  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	    44  ** Name             : Portx_lPdrConfig                                        **
; ..\mcal_cfg\Port_PBCfg.c	    45  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    46  ** Description      : Macro to configure Portx Pdr register parameters        **
; ..\mcal_cfg\Port_PBCfg.c	    47  **                                                                            **
; ..\mcal_cfg\Port_PBCfg.c	    48  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	    49  /*IFX_MISRA_RULE_19_07_STATUS=Function like macros used for simplification  of 
; ..\mcal_cfg\Port_PBCfg.c	    50          configuration generation and increase the readability of config file*/
; ..\mcal_cfg\Port_PBCfg.c	    51  /* Macro definition for PORT pad drive control register Pn_PDR0 */
; ..\mcal_cfg\Port_PBCfg.c	    52  #define  Portx_lPdrConfig1(Pd0,Pd1,Pd2,Pd3,Pd4,Pd5,Pd6,Pd7)                    \ 
; ..\mcal_cfg\Port_PBCfg.c	    53           ( ((uint32)(Pd7) << (uint32)28) | ((uint32)(Pd6) << (uint32)24) |     \ 
; ..\mcal_cfg\Port_PBCfg.c	    54             ((uint32)(Pd5) << (uint32)20) | ((uint32)(Pd4) << (uint32)16) |     \ 
; ..\mcal_cfg\Port_PBCfg.c	    55             ((uint32)(Pd3) << (uint32)12)|  ((uint32)(Pd2) << (uint32)8)  |     \ 
; ..\mcal_cfg\Port_PBCfg.c	    56             ((uint32)(Pd1) << (uint32)4) | (uint32)(Pd0)                        \ 
; ..\mcal_cfg\Port_PBCfg.c	    57           )
; ..\mcal_cfg\Port_PBCfg.c	    58  /*IFX_MISRA_RULE_19_07_STATUS=Function like macros used for simplification  of 
; ..\mcal_cfg\Port_PBCfg.c	    59          configuration generation and increase the readability of config file*/
; ..\mcal_cfg\Port_PBCfg.c	    60  /* Macro definition for PORT pad drive control register Pn_PDR1 */
; ..\mcal_cfg\Port_PBCfg.c	    61  #define  Portx_lPdrConfig2(Pd8,Pd9,Pd10,Pd11,Pd12,Pd13,Pd14,Pd15)              \ 
; ..\mcal_cfg\Port_PBCfg.c	    62           ( ((uint32)(Pd15) << (uint32)28) | ((uint32)(Pd14) << (uint32)24) |   \ 
; ..\mcal_cfg\Port_PBCfg.c	    63             ((uint32)(Pd13) << (uint32)20) | ((uint32)(Pd12) << (uint32)16) |   \ 
; ..\mcal_cfg\Port_PBCfg.c	    64             ((uint32)(Pd11) << (uint32)12)|  ((uint32)(Pd10) << (uint32)8)  |   \ 
; ..\mcal_cfg\Port_PBCfg.c	    65             ((uint32)(Pd9) << (uint32)4) | (uint32)(Pd8)                        \ 
; ..\mcal_cfg\Port_PBCfg.c	    66           )                  
; ..\mcal_cfg\Port_PBCfg.c	    67   /*IFX_MISRA_RULE_19_07_STATUS=Function like macros used for simplification  of 
; ..\mcal_cfg\Port_PBCfg.c	    68          configuration generation and increase the readability of config file*/        
; ..\mcal_cfg\Port_PBCfg.c	    69  /* Macro definition for PORT Pad Disable Control Register */
; ..\mcal_cfg\Port_PBCfg.c	    70  #define Port_lDiscSet(b0,b1,b2,b3,b4,b5,b6,b7,b8,b9,b10,b11,b12,b13,b14,b15)   \ 
; ..\mcal_cfg\Port_PBCfg.c	    71    ((uint16)(                                                                    \ 
; ..\mcal_cfg\Port_PBCfg.c	    72              (uint32)(b0) | ((uint32)(b1) << (uint32)1) |                       \ 
; ..\mcal_cfg\Port_PBCfg.c	    73              ((uint32)(b2) << (uint32)2) | ((uint32)(b3) << (uint32)3) |        \ 
; ..\mcal_cfg\Port_PBCfg.c	    74              ((uint32)(b4) << (uint32)4) | ((uint32)(b5) << (uint32)5) |        \ 
; ..\mcal_cfg\Port_PBCfg.c	    75              ((uint32)(b6) << (uint32)6) | ((uint32)(b7) << (uint32)7) |        \ 
; ..\mcal_cfg\Port_PBCfg.c	    76              ((uint32)(b8) << (uint32)8) | ((uint32)(b9) << (uint32)9) |        \ 
; ..\mcal_cfg\Port_PBCfg.c	    77              ((uint32)(b10) << (uint32)10) | ((uint32)(b11) << (uint32)11) |    \ 
; ..\mcal_cfg\Port_PBCfg.c	    78              ((uint32)(b12) << (uint32)12) | ((uint32)(b13)<< (uint32)13) |     \ 
; ..\mcal_cfg\Port_PBCfg.c	    79              ((uint32)(b14) << (uint32)14) | ((uint32)(b15) << (uint32)15)      \ 
; ..\mcal_cfg\Port_PBCfg.c	    80             ))
; ..\mcal_cfg\Port_PBCfg.c	    81  
; ..\mcal_cfg\Port_PBCfg.c	    82  /*IFX_MISRA_RULE_19_07_STATUS=Function like macros used for simplification  of 
; ..\mcal_cfg\Port_PBCfg.c	    83          configuration generation and increase the readability of config file*/
; ..\mcal_cfg\Port_PBCfg.c	    84  /* Macro definition for PORT PCSR register */
; ..\mcal_cfg\Port_PBCfg.c	    85  #define Port_lPcsr(b0,b1,b2,b3,b4,b5,b6,b7,b8,b9,b10,b11,b12,b13,b14,b15)      \ 
; ..\mcal_cfg\Port_PBCfg.c	    86    ((uint32)(                                                                    \ 
; ..\mcal_cfg\Port_PBCfg.c	    87              ((uint32)(b0)) | ((uint32)(b1) << (uint32)1) |                     \ 
; ..\mcal_cfg\Port_PBCfg.c	    88              ((uint32)(b2) << (uint32)2) | ((uint32)(b3) << (uint32)3) |        \ 
; ..\mcal_cfg\Port_PBCfg.c	    89              ((uint32)(b4) << (uint32)4) | ((uint32)(b5) << (uint32)5) |        \ 
; ..\mcal_cfg\Port_PBCfg.c	    90              ((uint32)(b6) << (uint32)6) | ((uint32)(b7) << (uint32)7) |        \ 
; ..\mcal_cfg\Port_PBCfg.c	    91              ((uint32)(b8) << (uint32)8) | ((uint32)(b9) << (uint32)9) |        \ 
; ..\mcal_cfg\Port_PBCfg.c	    92              ((uint32)(b10) << (uint32)10) | ((uint32)(b11) << (uint32)11) |    \ 
; ..\mcal_cfg\Port_PBCfg.c	    93              ((uint32)(b12) << (uint32)12) | ((uint32)(b13) << (uint32)13) |    \ 
; ..\mcal_cfg\Port_PBCfg.c	    94              ((uint32)(b14) << (uint32)14) | ((uint32)(b15) << (uint32)15)      \ 
; ..\mcal_cfg\Port_PBCfg.c	    95             ))
; ..\mcal_cfg\Port_PBCfg.c	    96  
; ..\mcal_cfg\Port_PBCfg.c	    97  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	    98  **                      Global Constant Definitions                           **
; ..\mcal_cfg\Port_PBCfg.c	    99  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	   100  /* MISRA RULE 87 VIOLATION: Inclusion of MemMap.h in between the code can't 
; ..\mcal_cfg\Port_PBCfg.c	   101     be avoided as it is required for mapping global variables, constants 
; ..\mcal_cfg\Port_PBCfg.c	   102     and code
; ..\mcal_cfg\Port_PBCfg.c	   103  */ 
; ..\mcal_cfg\Port_PBCfg.c	   104  /*lint -esym( 960, 87 ) Note 960: Violates MISRA Required Rule 87, 
; ..\mcal_cfg\Port_PBCfg.c	   105                         only preprocessor statements and comments before 
; ..\mcal_cfg\Port_PBCfg.c	   106                         '#include MemMap.h'
; ..\mcal_cfg\Port_PBCfg.c	   107  */
; ..\mcal_cfg\Port_PBCfg.c	   108  /*lint -e537 Warning 537: Repeated include file MemMap.h */
; ..\mcal_cfg\Port_PBCfg.c	   109  
; ..\mcal_cfg\Port_PBCfg.c	   110  
; ..\mcal_cfg\Port_PBCfg.c	   111  #define PORT_START_SEC_POSTBUILDCFG
; ..\mcal_cfg\Port_PBCfg.c	   112  #include "MemMap.h"
; ..\mcal_cfg\Port_PBCfg.c	   113  
; ..\mcal_cfg\Port_PBCfg.c	   114  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	   115  **                      Configuration Options                                 **
; ..\mcal_cfg\Port_PBCfg.c	   116  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	   117  /*
; ..\mcal_cfg\Port_PBCfg.c	   118                       Container : PortPinConfiguration
; ..\mcal_cfg\Port_PBCfg.c	   119  */
; ..\mcal_cfg\Port_PBCfg.c	   120  
; ..\mcal_cfg\Port_PBCfg.c	   121  /* 
; ..\mcal_cfg\Port_PBCfg.c	   122  Configuration Options: Physical pin level
; ..\mcal_cfg\Port_PBCfg.c	   123  -LOW  (Low Volatage Level)
; ..\mcal_cfg\Port_PBCfg.c	   124  -HIGH (High Voltage Level) 
; ..\mcal_cfg\Port_PBCfg.c	   125  */
; ..\mcal_cfg\Port_PBCfg.c	   126  #define  PORT_PIN_LOW                       (0x00U)
; ..\mcal_cfg\Port_PBCfg.c	   127  #define  PORT_PIN_HIGH                      (0x01U)
; ..\mcal_cfg\Port_PBCfg.c	   128  
; ..\mcal_cfg\Port_PBCfg.c	   129  /* Macros to define pin Default Input Output control value */
; ..\mcal_cfg\Port_PBCfg.c	   130  #define  PORT_PIN_DEFAULT                   (0x10U)
; ..\mcal_cfg\Port_PBCfg.c	   131  
; ..\mcal_cfg\Port_PBCfg.c	   132  /* 
; ..\mcal_cfg\Port_PBCfg.c	   133  Configuration Options: Pin input characteristics 
; ..\mcal_cfg\Port_PBCfg.c	   134  -NO PULL
; ..\mcal_cfg\Port_PBCfg.c	   135  -PULL DOWN
; ..\mcal_cfg\Port_PBCfg.c	   136  -PULL UP
; ..\mcal_cfg\Port_PBCfg.c	   137  */
; ..\mcal_cfg\Port_PBCfg.c	   138  #define  PORT_PIN_IN_NO_PULL                (0x00U)
; ..\mcal_cfg\Port_PBCfg.c	   139  #define  PORT_PIN_IN_PULL_DOWN              (0x08U)
; ..\mcal_cfg\Port_PBCfg.c	   140  #define  PORT_PIN_IN_PULL_UP                (0x10U)
; ..\mcal_cfg\Port_PBCfg.c	   141  
; ..\mcal_cfg\Port_PBCfg.c	   142  /* 
; ..\mcal_cfg\Port_PBCfg.c	   143  Configuration Options: Pin output characteristics 
; ..\mcal_cfg\Port_PBCfg.c	   144  -PUSHPULL
; ..\mcal_cfg\Port_PBCfg.c	   145  -OPENDRAIN
; ..\mcal_cfg\Port_PBCfg.c	   146  */
; ..\mcal_cfg\Port_PBCfg.c	   147  #define  PORT_PIN_OUT_PUSHPULL              (0x00U)
; ..\mcal_cfg\Port_PBCfg.c	   148  #define  PORT_PIN_OUT_OPENDRAIN             (0x40U)
; ..\mcal_cfg\Port_PBCfg.c	   149  
; ..\mcal_cfg\Port_PBCfg.c	   150  /*
; ..\mcal_cfg\Port_PBCfg.c	   151  Configuration Options: Pin Pad Level  
; ..\mcal_cfg\Port_PBCfg.c	   152  */
; ..\mcal_cfg\Port_PBCfg.c	   153  #define  PORT_PDR_CMOS_AUTOMOTIVE_LEVEL     (0x0U)
; ..\mcal_cfg\Port_PBCfg.c	   154  
; ..\mcal_cfg\Port_PBCfg.c	   155  /*
; ..\mcal_cfg\Port_PBCfg.c	   156  Configuration Options: Pin driver strength
; ..\mcal_cfg\Port_PBCfg.c	   157  */
; ..\mcal_cfg\Port_PBCfg.c	   158  #define  PORT_CMOS_SPEED_GRADE1             (0x0U)
; ..\mcal_cfg\Port_PBCfg.c	   159  #define  PORT_CMOS_SPEED_GRADE2             (0x1U)
; ..\mcal_cfg\Port_PBCfg.c	   160  #define  PORT_CMOS_SPEED_GRADE3             (0x2U)
; ..\mcal_cfg\Port_PBCfg.c	   161  #define  PORT_CMOS_SPEED_GRADE4             (0x3U)
; ..\mcal_cfg\Port_PBCfg.c	   162  
; ..\mcal_cfg\Port_PBCfg.c	   163  /* Pin driver strength value for the non available pins*/
; ..\mcal_cfg\Port_PBCfg.c	   164  #define  PORT_PIN_PAD_LEVEL_DEFAULT         (0x0U)
; ..\mcal_cfg\Port_PBCfg.c	   165  
; ..\mcal_cfg\Port_PBCfg.c	   166  /* Configuration Options: Pin drive strength, for class Adc type pad    */
; ..\mcal_cfg\Port_PBCfg.c	   167  #define  PORT_PDR_ADC                       (0x1U)
; ..\mcal_cfg\Port_PBCfg.c	   168  
; ..\mcal_cfg\Port_PBCfg.c	   169  /*
; ..\mcal_cfg\Port_PBCfg.c	   170    Some of the Pins doesn't have driver strength, To access the same
; ..\mcal_cfg\Port_PBCfg.c	   171    pin this null value macro is introduced.
; ..\mcal_cfg\Port_PBCfg.c	   172  */
; ..\mcal_cfg\Port_PBCfg.c	   173  #define  PORT_PDR_ZERO                      (0x0U)
; ..\mcal_cfg\Port_PBCfg.c	   174  
; ..\mcal_cfg\Port_PBCfg.c	   175  /* For the Px_DISC register value set */
; ..\mcal_cfg\Port_PBCfg.c	   176  #define  PORT_PIN_ANALOG_INPUT_ENABLE       (0x1U)
; ..\mcal_cfg\Port_PBCfg.c	   177  #define  PORT_PIN_ANALOG_INPUT_DISABLE      (0x0U)
; ..\mcal_cfg\Port_PBCfg.c	   178  
; ..\mcal_cfg\Port_PBCfg.c	   179  /* For PCSR register */
; ..\mcal_cfg\Port_PBCfg.c	   180  #define PORT_PCSR_DEFAULT                   (0x0U)
; ..\mcal_cfg\Port_PBCfg.c	   181  #define PORT_PCSR_ENABLE                    (0x1U)
; ..\mcal_cfg\Port_PBCfg.c	   182  #define PORT_PCSR_DISABLE                   (0x0U)
; ..\mcal_cfg\Port_PBCfg.c	   183  
; ..\mcal_cfg\Port_PBCfg.c	   184  /******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	   185  
; ..\mcal_cfg\Port_PBCfg.c	   186  
; ..\mcal_cfg\Port_PBCfg.c	   187  static const Port_n_ConfigType Port_kConfiguration0[] = 
; ..\mcal_cfg\Port_PBCfg.c	   188  {
; ..\mcal_cfg\Port_PBCfg.c	   189  /*                              Port0                       */
; ..\mcal_cfg\Port_PBCfg.c	   190    {
; ..\mcal_cfg\Port_PBCfg.c	   191      {
; ..\mcal_cfg\Port_PBCfg.c	   192        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   193        {
; ..\mcal_cfg\Port_PBCfg.c	   194           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   195           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   196           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   197           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   198           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   199           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   200           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   201           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   202           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   203           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   204           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   205           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   206           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT1),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   207           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   208           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   209           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   210        }
; ..\mcal_cfg\Port_PBCfg.c	   211      },
; ..\mcal_cfg\Port_PBCfg.c	   212      {
; ..\mcal_cfg\Port_PBCfg.c	   213        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   214        {
; ..\mcal_cfg\Port_PBCfg.c	   215           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   216           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   217           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   218           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   219           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   220           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   221           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   222           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   223           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   224           PORT_PIN_LOW,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   225           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   226           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   227           PORT_PIN_LOW,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   228           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   229           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   230           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   231        }
; ..\mcal_cfg\Port_PBCfg.c	   232      },      
; ..\mcal_cfg\Port_PBCfg.c	   233      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   234      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   235           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   236           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   237           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   238           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   239           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   240           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   241           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   242           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   243                       ),
; ..\mcal_cfg\Port_PBCfg.c	   244      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   245      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   246           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   247           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   248           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   249           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   250           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   251           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   252           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   253           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   254                       )
; ..\mcal_cfg\Port_PBCfg.c	   255     },
; ..\mcal_cfg\Port_PBCfg.c	   256  /*                              Port2                       */
; ..\mcal_cfg\Port_PBCfg.c	   257    {
; ..\mcal_cfg\Port_PBCfg.c	   258      {
; ..\mcal_cfg\Port_PBCfg.c	   259        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   260        {
; ..\mcal_cfg\Port_PBCfg.c	   261           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT5),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   262           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   263           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   264           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   265           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   266           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   267           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   268           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   269           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   270           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   271           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   272           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   273           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   274           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   275           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   276           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   277        }
; ..\mcal_cfg\Port_PBCfg.c	   278      },
; ..\mcal_cfg\Port_PBCfg.c	   279      {
; ..\mcal_cfg\Port_PBCfg.c	   280        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   281        {
; ..\mcal_cfg\Port_PBCfg.c	   282           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   283           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   284           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   285           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   286           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   287           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   288           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   289           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   290           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   291           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   292           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   293           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   294           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   295           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   296           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   297           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   298        }
; ..\mcal_cfg\Port_PBCfg.c	   299      },      
; ..\mcal_cfg\Port_PBCfg.c	   300      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   301      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   302           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   303           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   304           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   305           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   306           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   307           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   308           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   309           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   310                       ),
; ..\mcal_cfg\Port_PBCfg.c	   311      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   312      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   313           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   314           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   315           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   316           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   317           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   318           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   319           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   320           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   321                       )
; ..\mcal_cfg\Port_PBCfg.c	   322     },
; ..\mcal_cfg\Port_PBCfg.c	   323  /*                              Port10                       */
; ..\mcal_cfg\Port_PBCfg.c	   324    {
; ..\mcal_cfg\Port_PBCfg.c	   325      {
; ..\mcal_cfg\Port_PBCfg.c	   326        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   327        {
; ..\mcal_cfg\Port_PBCfg.c	   328           (PORT_PIN_DEFAULT),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   329           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   330           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   331           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   332           (PORT_PIN_DEFAULT),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   333           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   334           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   335           (PORT_PIN_DEFAULT),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   336           (PORT_PIN_DEFAULT),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   337           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   338           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   339           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   340           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   341           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   342           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   343           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   344        }
; ..\mcal_cfg\Port_PBCfg.c	   345      },
; ..\mcal_cfg\Port_PBCfg.c	   346      {
; ..\mcal_cfg\Port_PBCfg.c	   347        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   348        {
; ..\mcal_cfg\Port_PBCfg.c	   349           0U,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   350           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   351           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   352           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   353           0U,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   354           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   355           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   356           0U,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   357           0U,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   358           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   359           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   360           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   361           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   362           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   363           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   364           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   365        }
; ..\mcal_cfg\Port_PBCfg.c	   366      },      
; ..\mcal_cfg\Port_PBCfg.c	   367      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   368      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   369           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   370           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   371           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   372           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   373           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   374           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   375           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   376           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   377                       ),
; ..\mcal_cfg\Port_PBCfg.c	   378      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   379      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   380           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   381           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   382           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   383           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   384           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   385           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   386           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   387           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   388                       )
; ..\mcal_cfg\Port_PBCfg.c	   389     },
; ..\mcal_cfg\Port_PBCfg.c	   390  /*                              Port11                       */
; ..\mcal_cfg\Port_PBCfg.c	   391    {
; ..\mcal_cfg\Port_PBCfg.c	   392      {
; ..\mcal_cfg\Port_PBCfg.c	   393        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   394        {
; ..\mcal_cfg\Port_PBCfg.c	   395           (PORT_PIN_DEFAULT),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   396           (PORT_PIN_DEFAULT),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   397           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   398           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   399           (PORT_PIN_DEFAULT),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   400           (PORT_PIN_DEFAULT),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   401           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   402           (PORT_PIN_DEFAULT),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   403           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   404           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   405           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   406           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT4),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   407           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   408           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   409           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   410           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   411        }
; ..\mcal_cfg\Port_PBCfg.c	   412      },
; ..\mcal_cfg\Port_PBCfg.c	   413      {
; ..\mcal_cfg\Port_PBCfg.c	   414        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   415        {
; ..\mcal_cfg\Port_PBCfg.c	   416           0U,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   417           0U,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   418           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   419           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   420           0U,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   421           0U,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   422           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   423           0U,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   424           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   425           PORT_PIN_LOW,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   426           PORT_PIN_LOW,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   427           PORT_PIN_LOW,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   428           PORT_PIN_LOW,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   429           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   430           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   431           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   432        }
; ..\mcal_cfg\Port_PBCfg.c	   433      },      
; ..\mcal_cfg\Port_PBCfg.c	   434      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   435      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   436           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   437           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   438           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   439           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   440           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   441           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   442           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   443           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   444                       ),
; ..\mcal_cfg\Port_PBCfg.c	   445      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   446      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   447           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   448           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   449           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   450           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   451           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   452           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   453           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   454           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   455                       )
; ..\mcal_cfg\Port_PBCfg.c	   456     },
; ..\mcal_cfg\Port_PBCfg.c	   457  /*                              Port13                       */
; ..\mcal_cfg\Port_PBCfg.c	   458    {
; ..\mcal_cfg\Port_PBCfg.c	   459      {
; ..\mcal_cfg\Port_PBCfg.c	   460        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   461        {
; ..\mcal_cfg\Port_PBCfg.c	   462           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   463           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   464           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   465           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   466           (PORT_PIN_DEFAULT),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   467           (PORT_PIN_DEFAULT),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   468           (PORT_PIN_DEFAULT),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   469           (PORT_PIN_DEFAULT),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   470           (PORT_PIN_DEFAULT),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   471           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   472           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   473           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   474           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   475           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   476           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   477           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   478        }
; ..\mcal_cfg\Port_PBCfg.c	   479      },
; ..\mcal_cfg\Port_PBCfg.c	   480      {
; ..\mcal_cfg\Port_PBCfg.c	   481        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   482        {
; ..\mcal_cfg\Port_PBCfg.c	   483           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   484           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   485           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   486           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   487           0U,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   488           0U,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   489           0U,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   490           0U,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   491           0U,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   492           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   493           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   494           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   495           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   496           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   497           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   498           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   499        }
; ..\mcal_cfg\Port_PBCfg.c	   500      },      
; ..\mcal_cfg\Port_PBCfg.c	   501      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   502      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   503           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   504           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   505           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   506           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   507           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   508           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   509           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   510           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   511                       ),
; ..\mcal_cfg\Port_PBCfg.c	   512      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   513      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   514           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   515           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   516           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   517           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   518           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   519           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   520           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   521           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   522                       )
; ..\mcal_cfg\Port_PBCfg.c	   523     },
; ..\mcal_cfg\Port_PBCfg.c	   524  /*                              Port14                       */
; ..\mcal_cfg\Port_PBCfg.c	   525    {
; ..\mcal_cfg\Port_PBCfg.c	   526      {
; ..\mcal_cfg\Port_PBCfg.c	   527        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   528        {
; ..\mcal_cfg\Port_PBCfg.c	   529           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   530           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   531           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   532           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   533           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   534           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   535           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   536           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   537           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   538           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   539           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   540           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   541           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   542           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   543           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   544           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   545        }
; ..\mcal_cfg\Port_PBCfg.c	   546      },
; ..\mcal_cfg\Port_PBCfg.c	   547      {
; ..\mcal_cfg\Port_PBCfg.c	   548        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   549        {
; ..\mcal_cfg\Port_PBCfg.c	   550           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   551           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   552           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   553           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   554           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   555           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   556           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   557           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   558           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   559           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   560           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   561           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   562           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   563           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   564           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   565           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   566        }
; ..\mcal_cfg\Port_PBCfg.c	   567      },      
; ..\mcal_cfg\Port_PBCfg.c	   568      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   569      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   570           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   571           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   572           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   573           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   574           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   575           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   576           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   577           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   578                       ),
; ..\mcal_cfg\Port_PBCfg.c	   579      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   580      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   581           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   582           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   583           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   584           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   585           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   586           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   587           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   588           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   589                       )
; ..\mcal_cfg\Port_PBCfg.c	   590     },
; ..\mcal_cfg\Port_PBCfg.c	   591  /*                              Port15                       */
; ..\mcal_cfg\Port_PBCfg.c	   592    {
; ..\mcal_cfg\Port_PBCfg.c	   593      {
; ..\mcal_cfg\Port_PBCfg.c	   594        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   595        {
; ..\mcal_cfg\Port_PBCfg.c	   596           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   597           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   598           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   599           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   600           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   601           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   602           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   603           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   604           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   605           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   606           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   607           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   608           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   609           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   610           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   611           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   612        }
; ..\mcal_cfg\Port_PBCfg.c	   613      },
; ..\mcal_cfg\Port_PBCfg.c	   614      {
; ..\mcal_cfg\Port_PBCfg.c	   615        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   616        {
; ..\mcal_cfg\Port_PBCfg.c	   617           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   618           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   619           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   620           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   621           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   622           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   623           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   624           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   625           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   626           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   627           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   628           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   629           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   630           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   631           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   632           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   633        }
; ..\mcal_cfg\Port_PBCfg.c	   634      },      
; ..\mcal_cfg\Port_PBCfg.c	   635      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   636      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   637           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   638           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   639           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   640           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   641           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   642           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   643           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   644           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   645                       ),
; ..\mcal_cfg\Port_PBCfg.c	   646      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   647      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   648           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   649           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   650           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   651           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   652           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   653           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   654           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   655           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   656                       )
; ..\mcal_cfg\Port_PBCfg.c	   657     },
; ..\mcal_cfg\Port_PBCfg.c	   658  /*                              Port20                       */
; ..\mcal_cfg\Port_PBCfg.c	   659    {
; ..\mcal_cfg\Port_PBCfg.c	   660      {
; ..\mcal_cfg\Port_PBCfg.c	   661        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   662        {
; ..\mcal_cfg\Port_PBCfg.c	   663           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   664           (PORT_PIN_DEFAULT),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   665           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   666           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   667           (PORT_PIN_DEFAULT),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   668           (PORT_PIN_DEFAULT),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   669           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   670           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   671           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   672           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   673           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   674           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   675           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   676           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT5),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   677           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT3),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   678           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   679        }
; ..\mcal_cfg\Port_PBCfg.c	   680      },
; ..\mcal_cfg\Port_PBCfg.c	   681      {
; ..\mcal_cfg\Port_PBCfg.c	   682        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   683        {
; ..\mcal_cfg\Port_PBCfg.c	   684           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   685           0U,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   686           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   687           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   688           0U,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   689           0U,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   690           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   691           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   692           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   693           PORT_PIN_LOW,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   694           PORT_PIN_LOW,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   695           PORT_PIN_LOW,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   696           PORT_PIN_LOW,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   697           PORT_PIN_LOW,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   698           PORT_PIN_LOW,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   699           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   700        }
; ..\mcal_cfg\Port_PBCfg.c	   701      },      
; ..\mcal_cfg\Port_PBCfg.c	   702      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   703      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   704           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   705           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   706           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   707           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   708           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   709           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   710           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   711           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   712                       ),
; ..\mcal_cfg\Port_PBCfg.c	   713      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   714      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   715           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   716           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   717           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   718           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   719           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   720           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   721           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE3),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   722           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   723                       )
; ..\mcal_cfg\Port_PBCfg.c	   724     },
; ..\mcal_cfg\Port_PBCfg.c	   725  /*                              Port21                       */
; ..\mcal_cfg\Port_PBCfg.c	   726    {
; ..\mcal_cfg\Port_PBCfg.c	   727      {
; ..\mcal_cfg\Port_PBCfg.c	   728        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   729        {
; ..\mcal_cfg\Port_PBCfg.c	   730           (PORT_PIN_DEFAULT),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   731           (PORT_PIN_DEFAULT),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   732           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   733           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   734           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   735           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   736           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   737           ((uint8)PORT_PIN_IN | PORT_PIN_IN_PULL_UP | PORT_PIN_MODE_GPIO),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   738           (PORT_PIN_DEFAULT),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   739           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   740           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   741           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   742           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   743           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   744           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   745           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   746        }
; ..\mcal_cfg\Port_PBCfg.c	   747      },
; ..\mcal_cfg\Port_PBCfg.c	   748      {
; ..\mcal_cfg\Port_PBCfg.c	   749        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   750        {
; ..\mcal_cfg\Port_PBCfg.c	   751           0U,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   752           0U,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   753           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   754           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   755           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   756           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   757           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   758           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   759           0U,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   760           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   761           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   762           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   763           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   764           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   765           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   766           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   767        }
; ..\mcal_cfg\Port_PBCfg.c	   768      },      
; ..\mcal_cfg\Port_PBCfg.c	   769      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   770      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   771           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   772           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   773           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   774           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   775           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   776           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   777           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   778           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   779                       ),
; ..\mcal_cfg\Port_PBCfg.c	   780      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   781      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   782           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   783           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   784           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   785           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   786           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   787           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   788           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   789           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   790                       )
; ..\mcal_cfg\Port_PBCfg.c	   791     },
; ..\mcal_cfg\Port_PBCfg.c	   792  /*                              Port15                       */
; ..\mcal_cfg\Port_PBCfg.c	   793    {
; ..\mcal_cfg\Port_PBCfg.c	   794      {
; ..\mcal_cfg\Port_PBCfg.c	   795        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   796        {
; ..\mcal_cfg\Port_PBCfg.c	   797           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   798           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   799           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   800           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   801           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   802           (PORT_PIN_DEFAULT),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   803           (PORT_PIN_DEFAULT),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   804           (PORT_PIN_DEFAULT),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   805           (PORT_PIN_DEFAULT),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   806           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   807           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   808           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   809           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   810           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   811           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   812           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   813        }
; ..\mcal_cfg\Port_PBCfg.c	   814      },
; ..\mcal_cfg\Port_PBCfg.c	   815      {
; ..\mcal_cfg\Port_PBCfg.c	   816        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   817        {
; ..\mcal_cfg\Port_PBCfg.c	   818           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   819           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   820           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   821           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   822           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   823           0U,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   824           0U,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   825           0U,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   826           0U,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   827           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   828           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   829           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   830           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   831           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   832           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   833           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   834        }
; ..\mcal_cfg\Port_PBCfg.c	   835      },      
; ..\mcal_cfg\Port_PBCfg.c	   836      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   837      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   838           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   839           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   840           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   841           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   842           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   843           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   844           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   845           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   846                       ),
; ..\mcal_cfg\Port_PBCfg.c	   847      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   848      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   849           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   850           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   851           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   852           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   853           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   854           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   855           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   856           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   857                       )
; ..\mcal_cfg\Port_PBCfg.c	   858     },
; ..\mcal_cfg\Port_PBCfg.c	   859  /*                              Port23                       */
; ..\mcal_cfg\Port_PBCfg.c	   860    {
; ..\mcal_cfg\Port_PBCfg.c	   861      {
; ..\mcal_cfg\Port_PBCfg.c	   862        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   863        {
; ..\mcal_cfg\Port_PBCfg.c	   864           (PORT_PIN_DEFAULT),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   865           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   866           (PORT_PIN_DEFAULT),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   867           (PORT_PIN_DEFAULT),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   868           (PORT_PIN_DEFAULT),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   869           (PORT_PIN_DEFAULT),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   870           (PORT_PIN_DEFAULT),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   871           (PORT_PIN_DEFAULT),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   872           (PORT_PIN_DEFAULT),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   873           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   874           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   875           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   876           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   877           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   878           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   879           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   880        }
; ..\mcal_cfg\Port_PBCfg.c	   881      },
; ..\mcal_cfg\Port_PBCfg.c	   882      {
; ..\mcal_cfg\Port_PBCfg.c	   883        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   884        {
; ..\mcal_cfg\Port_PBCfg.c	   885           0U,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   886           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   887           0U,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   888           0U,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   889           0U,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   890           0U,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   891           0U,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   892           0U,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   893           0U,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   894           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   895           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   896           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   897           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   898           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   899           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   900           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   901        }
; ..\mcal_cfg\Port_PBCfg.c	   902      },      
; ..\mcal_cfg\Port_PBCfg.c	   903      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   904      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   905           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   906           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   907           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   908           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   909           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   910           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   911           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   912           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   913                       ),
; ..\mcal_cfg\Port_PBCfg.c	   914      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   915      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   916           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   917           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   918           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   919           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   920           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   921           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   922           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   923           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   924                       )
; ..\mcal_cfg\Port_PBCfg.c	   925     },
; ..\mcal_cfg\Port_PBCfg.c	   926  /*                              Port33                       */
; ..\mcal_cfg\Port_PBCfg.c	   927    {
; ..\mcal_cfg\Port_PBCfg.c	   928      {
; ..\mcal_cfg\Port_PBCfg.c	   929        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   930        {
; ..\mcal_cfg\Port_PBCfg.c	   931           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   932           ((uint8)PORT_PIN_IN | PORT_PIN_IN_PULL_UP | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	   933           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT1),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	   934           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	   935           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	   936           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	   937           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	   938           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	   939           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	   940           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	   941           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT1),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	   942           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	   943           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	   944           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	   945           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	   946           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	   947        }
; ..\mcal_cfg\Port_PBCfg.c	   948      },
; ..\mcal_cfg\Port_PBCfg.c	   949      {
; ..\mcal_cfg\Port_PBCfg.c	   950        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	   951        {
; ..\mcal_cfg\Port_PBCfg.c	   952           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   953           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   954           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   955           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   956           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   957           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   958           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   959           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   960           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   961           PORT_PIN_LOW,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   962           PORT_PIN_HIGH,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   963           PORT_PIN_LOW,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   964           PORT_PIN_LOW,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   965           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   966           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   967           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   968        }
; ..\mcal_cfg\Port_PBCfg.c	   969      },      
; ..\mcal_cfg\Port_PBCfg.c	   970      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   971      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	   972           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	   973           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	   974           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	   975           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	   976           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	   977           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	   978           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	   979           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	   980                       ),
; ..\mcal_cfg\Port_PBCfg.c	   981      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	   982      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	   983           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	   984           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	   985           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	   986           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	   987           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	   988           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	   989           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	   990           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	   991                       )
; ..\mcal_cfg\Port_PBCfg.c	   992     },
; ..\mcal_cfg\Port_PBCfg.c	   993  /*                              Port34                       */
; ..\mcal_cfg\Port_PBCfg.c	   994    {
; ..\mcal_cfg\Port_PBCfg.c	   995      {
; ..\mcal_cfg\Port_PBCfg.c	   996        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	   997        {
; ..\mcal_cfg\Port_PBCfg.c	   998           (PORT_PIN_DEFAULT),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	   999           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	  1000           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	  1001           ((uint8)PORT_PIN_OUT | PORT_PIN_OUT_PUSHPULL | PORT_PIN_MODE_ALT1),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	  1002           (PORT_PIN_DEFAULT),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	  1003           (PORT_PIN_DEFAULT),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	  1004           (PORT_PIN_DEFAULT),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	  1005           (PORT_PIN_DEFAULT),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	  1006           (PORT_PIN_DEFAULT),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	  1007           (PORT_PIN_DEFAULT),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	  1008           (PORT_PIN_DEFAULT),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	  1009           (PORT_PIN_DEFAULT),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	  1010           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	  1011           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	  1012           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	  1013           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	  1014        }
; ..\mcal_cfg\Port_PBCfg.c	  1015      },
; ..\mcal_cfg\Port_PBCfg.c	  1016      {
; ..\mcal_cfg\Port_PBCfg.c	  1017        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1018        {
; ..\mcal_cfg\Port_PBCfg.c	  1019           0U,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1020           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1021           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1022           PORT_PIN_HIGH,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1023           0U,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1024           0U,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1025           0U,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1026           0U,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1027           0U,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1028           0U,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1029           0U,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1030           0U,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1031           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1032           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1033           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1034           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1035        }
; ..\mcal_cfg\Port_PBCfg.c	  1036      },      
; ..\mcal_cfg\Port_PBCfg.c	  1037      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1038      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	  1039           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1040           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1041           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1042           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1043           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1044           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1045           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1046           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1047                       ),
; ..\mcal_cfg\Port_PBCfg.c	  1048      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1049      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	  1050           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1051           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1052           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1053           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1054           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1055           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1056           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1057           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1058                       )
; ..\mcal_cfg\Port_PBCfg.c	  1059     },
; ..\mcal_cfg\Port_PBCfg.c	  1060  /*                              Port40                       */
; ..\mcal_cfg\Port_PBCfg.c	  1061    {
; ..\mcal_cfg\Port_PBCfg.c	  1062      {
; ..\mcal_cfg\Port_PBCfg.c	  1063        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1064        {
; ..\mcal_cfg\Port_PBCfg.c	  1065           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	  1066           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	  1067           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	  1068           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	  1069           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	  1070           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	  1071           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	  1072           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	  1073           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	  1074           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	  1075           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	  1076           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	  1077           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	  1078           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	  1079           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	  1080           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	  1081        }
; ..\mcal_cfg\Port_PBCfg.c	  1082      },
; ..\mcal_cfg\Port_PBCfg.c	  1083      {
; ..\mcal_cfg\Port_PBCfg.c	  1084        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1085        {
; ..\mcal_cfg\Port_PBCfg.c	  1086           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1087           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1088           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1089           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1090           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1091           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1092           PORT_PIN_HIGH,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1093           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1094           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1095           PORT_PIN_LOW,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1096           PORT_PIN_LOW,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1097           PORT_PIN_LOW,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1098           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1099           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1100           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1101           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1102        }
; ..\mcal_cfg\Port_PBCfg.c	  1103      },      
; ..\mcal_cfg\Port_PBCfg.c	  1104      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1105      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	  1106           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1107           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1108           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1109           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1110           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1111           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1112           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1113           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1114                       ),
; ..\mcal_cfg\Port_PBCfg.c	  1115      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1116      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	  1117           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1118           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1119           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1120           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1121           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1122           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1123           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1124           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1125                       )
; ..\mcal_cfg\Port_PBCfg.c	  1126     },
; ..\mcal_cfg\Port_PBCfg.c	  1127  /*                              Port41                       */
; ..\mcal_cfg\Port_PBCfg.c	  1128    {
; ..\mcal_cfg\Port_PBCfg.c	  1129      {
; ..\mcal_cfg\Port_PBCfg.c	  1130        /* Port pins direction, characteristics and mode configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1131        {
; ..\mcal_cfg\Port_PBCfg.c	  1132           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 0*/
; ..\mcal_cfg\Port_PBCfg.c	  1133           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 1*/
; ..\mcal_cfg\Port_PBCfg.c	  1134           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 2*/
; ..\mcal_cfg\Port_PBCfg.c	  1135           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 3*/
; ..\mcal_cfg\Port_PBCfg.c	  1136           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 4*/
; ..\mcal_cfg\Port_PBCfg.c	  1137           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 5*/
; ..\mcal_cfg\Port_PBCfg.c	  1138           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 6*/
; ..\mcal_cfg\Port_PBCfg.c	  1139           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 7*/
; ..\mcal_cfg\Port_PBCfg.c	  1140           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 8*/
; ..\mcal_cfg\Port_PBCfg.c	  1141           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 9*/
; ..\mcal_cfg\Port_PBCfg.c	  1142           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 10*/
; ..\mcal_cfg\Port_PBCfg.c	  1143           ((uint8)PORT_PIN_IN | PORT_PIN_IN_NO_PULL | PORT_PIN_MODE_GPIO),/*Pin 11*/
; ..\mcal_cfg\Port_PBCfg.c	  1144           (PORT_PIN_DEFAULT),/*Pin 12*/
; ..\mcal_cfg\Port_PBCfg.c	  1145           (PORT_PIN_DEFAULT),/*Pin 13*/
; ..\mcal_cfg\Port_PBCfg.c	  1146           (PORT_PIN_DEFAULT),/*Pin 14*/
; ..\mcal_cfg\Port_PBCfg.c	  1147           (PORT_PIN_DEFAULT) /*Pin 15*/ 
; ..\mcal_cfg\Port_PBCfg.c	  1148        }
; ..\mcal_cfg\Port_PBCfg.c	  1149      },
; ..\mcal_cfg\Port_PBCfg.c	  1150      {
; ..\mcal_cfg\Port_PBCfg.c	  1151        /* Port pins initial level configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1152        {
; ..\mcal_cfg\Port_PBCfg.c	  1153           PORT_PIN_LOW,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1154           PORT_PIN_LOW,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1155           PORT_PIN_LOW,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1156           PORT_PIN_LOW,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1157           PORT_PIN_LOW,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1158           PORT_PIN_LOW,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1159           PORT_PIN_LOW,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1160           PORT_PIN_LOW,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1161           PORT_PIN_LOW,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1162           PORT_PIN_LOW,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1163           PORT_PIN_LOW,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1164           PORT_PIN_LOW,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1165           0U,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1166           0U,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1167           0U,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1168           0U /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1169        }
; ..\mcal_cfg\Port_PBCfg.c	  1170      },      
; ..\mcal_cfg\Port_PBCfg.c	  1171      /* Port pins drive strength1 configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1172      Portx_lPdrConfig1(
; ..\mcal_cfg\Port_PBCfg.c	  1173           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1174           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1175           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1176           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1177           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1178           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1179           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1180           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1) /* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1181                       ),
; ..\mcal_cfg\Port_PBCfg.c	  1182      /* Port pins drive strength2 configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1183      Portx_lPdrConfig2(
; ..\mcal_cfg\Port_PBCfg.c	  1184           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1185           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1186           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1187           (PORT_PDR_CMOS_AUTOMOTIVE_LEVEL | PORT_CMOS_SPEED_GRADE1),/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1188           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1189           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1190           (PORT_PIN_PAD_LEVEL_DEFAULT),/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1191           (PORT_PIN_PAD_LEVEL_DEFAULT) /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1192                       )
; ..\mcal_cfg\Port_PBCfg.c	  1193     }
; ..\mcal_cfg\Port_PBCfg.c	  1194  };
; ..\mcal_cfg\Port_PBCfg.c	  1195  
; ..\mcal_cfg\Port_PBCfg.c	  1196  static const uint16 Port_DiscSet0[] = 
; ..\mcal_cfg\Port_PBCfg.c	  1197  {     
; ..\mcal_cfg\Port_PBCfg.c	  1198  /*                              Port40                       */
; ..\mcal_cfg\Port_PBCfg.c	  1199     Port_lDiscSet(
; ..\mcal_cfg\Port_PBCfg.c	  1200                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1201                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1202                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1203                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1204                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1205                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1206                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1207                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1208                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1209                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1210                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1211                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1212                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1213                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1214                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1215                   PORT_PIN_ANALOG_INPUT_DISABLE /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1216                  ),
; ..\mcal_cfg\Port_PBCfg.c	  1217  /*                              Port41                       */
; ..\mcal_cfg\Port_PBCfg.c	  1218     Port_lDiscSet(
; ..\mcal_cfg\Port_PBCfg.c	  1219                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 0 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1220                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 1 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1221                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 2 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1222                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 3 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1223                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 4 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1224                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 5 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1225                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 6 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1226                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 7 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1227                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 8 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1228                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 9 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1229                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 10 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1230                   PORT_PIN_ANALOG_INPUT_ENABLE,/* Pin 11 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1231                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 12 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1232                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 13 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1233                   PORT_PIN_ANALOG_INPUT_DISABLE,/* Pin 14 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1234                   PORT_PIN_ANALOG_INPUT_DISABLE /* Pin 15 */ 
; ..\mcal_cfg\Port_PBCfg.c	  1235                  )
; ..\mcal_cfg\Port_PBCfg.c	  1236  };
; ..\mcal_cfg\Port_PBCfg.c	  1237  
; ..\mcal_cfg\Port_PBCfg.c	  1238  /* PCSR configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1239  static const Port_n_PCSRConfigType Port_kPCSRConfig0[] = 
; ..\mcal_cfg\Port_PBCfg.c	  1240  {
; ..\mcal_cfg\Port_PBCfg.c	  1241  
; ..\mcal_cfg\Port_PBCfg.c	  1242                 /*           Port40            */
; ..\mcal_cfg\Port_PBCfg.c	  1243     Port_lPcsr(
; ..\mcal_cfg\Port_PBCfg.c	  1244                 PORT_PCSR_DEFAULT,  /*Pin0*/
; ..\mcal_cfg\Port_PBCfg.c	  1245                 PORT_PCSR_DISABLE,  /*Pin1*/
; ..\mcal_cfg\Port_PBCfg.c	  1246                 PORT_PCSR_DISABLE,  /*Pin2*/
; ..\mcal_cfg\Port_PBCfg.c	  1247                 PORT_PCSR_DEFAULT,  /*Pin3*/
; ..\mcal_cfg\Port_PBCfg.c	  1248                 PORT_PCSR_DEFAULT,  /*Pin4*/
; ..\mcal_cfg\Port_PBCfg.c	  1249                 PORT_PCSR_DEFAULT,  /*Pin5*/
; ..\mcal_cfg\Port_PBCfg.c	  1250                 PORT_PCSR_DEFAULT,  /*Pin6*/
; ..\mcal_cfg\Port_PBCfg.c	  1251                 PORT_PCSR_DEFAULT,  /*Pin7*/
; ..\mcal_cfg\Port_PBCfg.c	  1252                 PORT_PCSR_DEFAULT,  /*Pin8*/
; ..\mcal_cfg\Port_PBCfg.c	  1253                 PORT_PCSR_DISABLE,  /*Pin9*/
; ..\mcal_cfg\Port_PBCfg.c	  1254                 PORT_PCSR_DISABLE,  /*Pin10*/
; ..\mcal_cfg\Port_PBCfg.c	  1255                 PORT_PCSR_DEFAULT,  /*Pin11*/
; ..\mcal_cfg\Port_PBCfg.c	  1256                 PORT_PCSR_DEFAULT,  /*Pin12*/
; ..\mcal_cfg\Port_PBCfg.c	  1257                 PORT_PCSR_DEFAULT,  /*Pin13*/
; ..\mcal_cfg\Port_PBCfg.c	  1258                 PORT_PCSR_DEFAULT,  /*Pin14*/
; ..\mcal_cfg\Port_PBCfg.c	  1259                 PORT_PCSR_DEFAULT   /*Pin15*/
; ..\mcal_cfg\Port_PBCfg.c	  1260                ),
; ..\mcal_cfg\Port_PBCfg.c	  1261  
; ..\mcal_cfg\Port_PBCfg.c	  1262                 /*           Port41            */
; ..\mcal_cfg\Port_PBCfg.c	  1263     Port_lPcsr(
; ..\mcal_cfg\Port_PBCfg.c	  1264                 PORT_PCSR_DEFAULT,  /*Pin0*/
; ..\mcal_cfg\Port_PBCfg.c	  1265                 PORT_PCSR_DISABLE,  /*Pin1*/
; ..\mcal_cfg\Port_PBCfg.c	  1266                 PORT_PCSR_DISABLE,  /*Pin2*/
; ..\mcal_cfg\Port_PBCfg.c	  1267                 PORT_PCSR_DEFAULT,  /*Pin3*/
; ..\mcal_cfg\Port_PBCfg.c	  1268                 PORT_PCSR_DEFAULT,  /*Pin4*/
; ..\mcal_cfg\Port_PBCfg.c	  1269                 PORT_PCSR_DEFAULT,  /*Pin5*/
; ..\mcal_cfg\Port_PBCfg.c	  1270                 PORT_PCSR_DEFAULT,  /*Pin6*/
; ..\mcal_cfg\Port_PBCfg.c	  1271                 PORT_PCSR_DEFAULT,  /*Pin7*/
; ..\mcal_cfg\Port_PBCfg.c	  1272                 PORT_PCSR_DEFAULT,  /*Pin8*/
; ..\mcal_cfg\Port_PBCfg.c	  1273                 PORT_PCSR_DISABLE,  /*Pin9*/
; ..\mcal_cfg\Port_PBCfg.c	  1274                 PORT_PCSR_DISABLE,  /*Pin10*/
; ..\mcal_cfg\Port_PBCfg.c	  1275                 PORT_PCSR_DEFAULT,  /*Pin11*/
; ..\mcal_cfg\Port_PBCfg.c	  1276                 PORT_PCSR_DEFAULT,  /*Pin12*/
; ..\mcal_cfg\Port_PBCfg.c	  1277                 PORT_PCSR_DEFAULT,  /*Pin13*/
; ..\mcal_cfg\Port_PBCfg.c	  1278                 PORT_PCSR_DEFAULT,  /*Pin14*/
; ..\mcal_cfg\Port_PBCfg.c	  1279                 PORT_PCSR_DEFAULT   /*Pin15*/
; ..\mcal_cfg\Port_PBCfg.c	  1280                )
; ..\mcal_cfg\Port_PBCfg.c	  1281  };
; ..\mcal_cfg\Port_PBCfg.c	  1282  
; ..\mcal_cfg\Port_PBCfg.c	  1283  
; ..\mcal_cfg\Port_PBCfg.c	  1284  
; ..\mcal_cfg\Port_PBCfg.c	  1285  
; ..\mcal_cfg\Port_PBCfg.c	  1286  
; ..\mcal_cfg\Port_PBCfg.c	  1287  const Port_ConfigType Port_ConfigRoot[1] =
; ..\mcal_cfg\Port_PBCfg.c	  1288  {
; ..\mcal_cfg\Port_PBCfg.c	  1289  
; ..\mcal_cfg\Port_PBCfg.c	  1290    {
; ..\mcal_cfg\Port_PBCfg.c	  1291          /* Port Configuration set 0 */
; ..\mcal_cfg\Port_PBCfg.c	  1292      &Port_kConfiguration0[0],
; ..\mcal_cfg\Port_PBCfg.c	  1293      /* Port 40-th and 41-th Disc configuration set 0 */
; ..\mcal_cfg\Port_PBCfg.c	  1294      &Port_DiscSet0[0],
; ..\mcal_cfg\Port_PBCfg.c	  1295  
; ..\mcal_cfg\Port_PBCfg.c	  1296  
; ..\mcal_cfg\Port_PBCfg.c	  1297      /* PCSR Configuration */
; ..\mcal_cfg\Port_PBCfg.c	  1298      &Port_kPCSRConfig0[0]
; ..\mcal_cfg\Port_PBCfg.c	  1299    }
; ..\mcal_cfg\Port_PBCfg.c	  1300  };
; ..\mcal_cfg\Port_PBCfg.c	  1301  
; ..\mcal_cfg\Port_PBCfg.c	  1302  #define PORT_STOP_SEC_POSTBUILDCFG
; ..\mcal_cfg\Port_PBCfg.c	  1303  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is 
; ..\mcal_cfg\Port_PBCfg.c	  1304   allowed only for MemMap.h*/
; ..\mcal_cfg\Port_PBCfg.c	  1305  #include "MemMap.h"
; ..\mcal_cfg\Port_PBCfg.c	  1306  
; ..\mcal_cfg\Port_PBCfg.c	  1307  
; ..\mcal_cfg\Port_PBCfg.c	  1308  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	  1309  **                      Global Variable Definitions                           **
; ..\mcal_cfg\Port_PBCfg.c	  1310  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	  1311  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	  1312  **                      Private Constant Definitions                          **
; ..\mcal_cfg\Port_PBCfg.c	  1313  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	  1314  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	  1315  **                      Private Variable Definitions                          **
; ..\mcal_cfg\Port_PBCfg.c	  1316  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	  1317  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	  1318  **                      Global Function Definitions                           **
; ..\mcal_cfg\Port_PBCfg.c	  1319  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	  1320  /*******************************************************************************
; ..\mcal_cfg\Port_PBCfg.c	  1321  **                      Private Function Definitions                          **
; ..\mcal_cfg\Port_PBCfg.c	  1322  *******************************************************************************/
; ..\mcal_cfg\Port_PBCfg.c	  1323  
; ..\mcal_cfg\Port_PBCfg.c	  1324  

	; Module end
