	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc27644a -c99 --dep-file=mcal_src\\integration_general\\src\\.Dma_Callout.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\integration_general\\src\\Dma_Callout.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\integration_general\\src\\Dma_Callout.src ..\\mcal_src\\integration_general\\src\\Dma_Callout.c"
	.compiler_name		"ctc"
	.name	"Dma_Callout"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dma_ErrorCallOut')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dma_ErrorCallOut

; ..\mcal_src\integration_general\src\Dma_Callout.c	     1  /*******************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	     2  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_src\integration_general\src\Dma_Callout.c	     4  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	     5  ** All rights reserved.                                                       **
; ..\mcal_src\integration_general\src\Dma_Callout.c	     6  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\integration_general\src\Dma_Callout.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\integration_general\src\Dma_Callout.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    10  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    11  ********************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	    12  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    13  **  $FILENAME  : Dma_Callout.c                                                **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    14  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    15  **  $CC VERSION : \main\3 $                                                  **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    16  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    17  **  $DATE      : 2013-06-26 $                                                 **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    18  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    19  **  AUTHOR    : DL-AUTOSAR-Engineering                                        **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    20  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    21  **  VENDOR    : Infineon Technologies                                         **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    22  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    23  **  DESCRIPTION  : This file contains                                         **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    24  **                 - Implementation of DMA call out functions                 **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    25  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    26  **  MAY BE CHANGED BY USER [yes/no]: yes                                      **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    27  **                                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    28  *******************************************************************************/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    29  /*  TRACEABILITY :                                                            **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    30  **                                                                            */
; ..\mcal_src\integration_general\src\Dma_Callout.c	    31  /*******************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	    32  **                      Includes                                              **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    33  *******************************************************************************/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    34    /* Inclusion of Dma.h header file */
; ..\mcal_src\integration_general\src\Dma_Callout.c	    35  #include "Dma_Callout.h"
; ..\mcal_src\integration_general\src\Dma_Callout.c	    36  /*******************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	    37  **                      Imported Compiler Switch Checks                       **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    38  *******************************************************************************/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    39  
; ..\mcal_src\integration_general\src\Dma_Callout.c	    40  /*******************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	    41  **                      Private Macro Definitions                             **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    42  *******************************************************************************/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    43  
; ..\mcal_src\integration_general\src\Dma_Callout.c	    44  /*******************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	    45  **                      Private Type Definitions                              **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    46  *******************************************************************************/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    47  
; ..\mcal_src\integration_general\src\Dma_Callout.c	    48  /*******************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	    49  **                      Private Function Declarations                         **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    50  *******************************************************************************/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    51  /*Memory Map of the DMA Code*/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    52  #define DMA_START_SEC_CODE
; ..\mcal_src\integration_general\src\Dma_Callout.c	    53  #include "MemMap.h"
; ..\mcal_src\integration_general\src\Dma_Callout.c	    54  
; ..\mcal_src\integration_general\src\Dma_Callout.c	    55  /*******************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	    56  **                      Global Constant Definitions                           **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    57  *******************************************************************************/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    58  
; ..\mcal_src\integration_general\src\Dma_Callout.c	    59  /*******************************************************************************
; ..\mcal_src\integration_general\src\Dma_Callout.c	    60  **                      Global Variable Definitions                           **
; ..\mcal_src\integration_general\src\Dma_Callout.c	    61  *******************************************************************************/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    62  void Dma_ErrorCallOut(uint8 Channel, uint8 Error)
; Function Dma_ErrorCallOut
.L3:
Dma_ErrorCallOut:	.type	func

; ..\mcal_src\integration_general\src\Dma_Callout.c	    63  {
; ..\mcal_src\integration_general\src\Dma_Callout.c	    64  	UNUSED_PARAMETER(Channel)
; ..\mcal_src\integration_general\src\Dma_Callout.c	    65  	UNUSED_PARAMETER(Error)
; ..\mcal_src\integration_general\src\Dma_Callout.c	    66  	/*User Code Begins Here*/
; ..\mcal_src\integration_general\src\Dma_Callout.c	    67  }
	ret
.L13:
	
__Dma_ErrorCallOut_function_end:
	.size	Dma_ErrorCallOut,__Dma_ErrorCallOut_function_end-Dma_ErrorCallOut
.L12:
	; End of function
	
	.calls	'Dma_ErrorCallOut','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L5:
	.word	1595
	.half	3
	.word	.L6
	.byte	4
.L4:
	.byte	1
	.byte	'..\\mcal_src\\integration_general\\src\\Dma_Callout.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L7
.L14:
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'void',0,4
	.word	221
	.byte	5
	.byte	'__prof_adm',0,1,1,1
	.word	227
	.byte	6,1,4
	.word	251
	.byte	5
	.byte	'__codeptr',0,1,1,1
	.word	253
	.byte	5
	.byte	'uint8',0,2,90,29
	.word	204
	.byte	2
	.byte	'unsigned short int',0,2,7,5
	.byte	'uint16',0,2,92,29
	.word	290
	.byte	2
	.byte	'unsigned long int',0,4,7,5
	.byte	'uint32',0,2,94,29
	.word	327
	.byte	2
	.byte	'unsigned int',0,4,7,5
	.byte	'unsigned_int',0,3,121,22
	.word	363
	.byte	7,4,215,3,9,1,8
	.byte	'DMA_CH_INCREMENT_DIR_NEG',0,0,8
	.byte	'DMA_CH_INCREMENT_DIR_POS',0,1,0,5
	.byte	'Dma_ChIncrementDirectionType',0,4,219,3,3
	.word	400
	.byte	7,4,226,3,9,1,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_1',0,0,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_2',0,1,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_4',0,2,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_8',0,3,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_16',0,4,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_32',0,5,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_64',0,6,8
	.byte	'DMA_CH_ADDR_MOD_FACTOR_128',0,7,0,5
	.byte	'Dma_ChAddressModOffsetType',0,4,236,3,3
	.word	499
	.byte	7,4,243,3,9,1,8
	.byte	'DMA_CH_CIRC_BUFF_DISABLE',0,0,8
	.byte	'DMA_CH_CIRC_BUFF_ENABLE',0,1,0,5
	.byte	'Dma_ChCircularBuffEnType',0,4,247,3,3
	.word	763
	.byte	7,4,254,3,9,1,8
	.byte	'DMA_CH_WRAP_DISABLE',0,0,8
	.byte	'DMA_CH_WRAP_ENABLE',0,1,0,5
	.byte	'Dma_ChWrapEnType',0,4,130,4,3
	.word	857
	.byte	7,4,133,4,9,1,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_1',0,0,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_2',0,1,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_4',0,2,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_8',0,3,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_16',0,4,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_32',0,5,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_64',0,6,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_128',0,7,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_256',0,8,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_512',0,9,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_1KB',0,10,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_2KB',0,11,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_4KB',0,12,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_8KB',0,13,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_16KB',0,14,8
	.byte	'DMA_CH_CIRC_BUFF_LEN_32KB',0,15,0,5
	.byte	'Dma_ChCircBuffSizeType',0,4,151,4,3
	.word	933
	.byte	9
	.byte	'Dma_ChannelConfigType',0,4,174,4,16,12,10
	.byte	'DmaChannelConfig',0,4
	.word	327
	.byte	2,35,0,10
	.byte	'DmaAddrIntControl',0,4
	.word	327
	.byte	2,35,4,10
	.byte	'DmaHwResourceMode',0,1
	.word	204
	.byte	2,35,8,10
	.byte	'DmaChannelHwPartitionConfig',0,1
	.word	204
	.byte	2,35,9,10
	.byte	'DmaChannelNumber',0,1
	.word	204
	.byte	2,35,10,0,5
	.byte	'Dma_ChannelConfigType',0,4,181,4,2
	.word	1395
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L6:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,59,0,3,8,0,0,4,15,0,73,19,0
	.byte	0,5,22,0,3,8,58,15,59,15,57,15,73,19,0,0,6,21,0,54,15,0,0,7,4,1,58,15,59,15,57,15,11,15,0,0,8,40,0,3,8
	.byte	28,13,0,0,9,19,1,3,8,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L7:
	.word	.L18-.L17
.L17:
	.half	3
	.word	.L20-.L19
.L19:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc',0
	.byte	0
	.byte	'..\\mcal_src\\integration_general\\src\\Dma_Callout.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Mcal_TcLib.h',0,1,0,0
	.byte	'Dma.h',0,2,0,0,0
.L20:
.L18:
	.sdecl	'.debug_info',debug,cluster('Dma_ErrorCallOut')
	.sect	'.debug_info'
.L8:
	.word	294
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_src\\integration_general\\src\\Dma_Callout.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L11,.L10
	.byte	2
	.word	.L4
	.byte	3
	.byte	'Dma_ErrorCallOut',0,1,62,6,1,1,1
	.word	.L3,.L13,.L2
	.byte	4
	.byte	'Channel',0,1,62,29
	.word	.L14,.L15
	.byte	4
	.byte	'Error',0,1,62,44
	.word	.L14,.L16
	.byte	5
	.word	.L3,.L13
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dma_ErrorCallOut')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dma_ErrorCallOut')
	.sect	'.debug_line'
.L10:
	.word	.L22-.L21
.L21:
	.half	3
	.word	.L24-.L23
.L23:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\integration_general\\src\\Dma_Callout.c',0,0,0,0,0
.L24:
	.byte	5,1,7,0,5,2
	.word	.L3
	.byte	3,194,0,1,7,9
	.half	.L12-.L3
	.byte	0,1,1
.L22:
	.sdecl	'.debug_ranges',debug,cluster('Dma_ErrorCallOut')
	.sect	'.debug_ranges'
.L11:
	.word	-1,.L3,0,.L12-.L3,0,0
	.sdecl	'.debug_loc',debug,cluster('Dma_ErrorCallOut')
	.sect	'.debug_loc'
.L15:
	.word	-1,.L3,0,.L13-.L3
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L2:
	.word	-1,.L3,0,.L13-.L3
	.half	2
	.byte	138,0
	.word	0,0
.L16:
	.word	-1,.L3,0,.L13-.L3
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L25:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Dma_ErrorCallOut')
	.sect	'.debug_frame'
	.word	24
	.word	.L25,.L3,.L13-.L3
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\integration_general\src\Dma_Callout.c	    68  #define DMA_STOP_SEC_CODE
; ..\mcal_src\integration_general\src\Dma_Callout.c	    68  #include "MemMap.h"

	; Module end
