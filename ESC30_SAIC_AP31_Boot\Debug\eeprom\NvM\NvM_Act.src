	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc28048a -c99 --dep-file=eeprom\\NvM\\.NvM_Act.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\NvM\\NvM_Act.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\NvM\\NvM_Act.src ..\\eeprom\\NvM\\NvM_Act.c"
	.compiler_name		"ctc"
	.name	"NvM_Act"

	
$TC16X
	
	.sdecl	'.text.NvM_Act.NvM_ActEraseNvBlock',code,cluster('NvM_ActEraseNvBlock')
	.sect	'.text.NvM_Act.NvM_ActEraseNvBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	     1  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	     2   *  COPYRIGHT
; ..\eeprom\NvM\NvM_Act.c	     3   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Act.c	     4   *  \verbatim
; ..\eeprom\NvM\NvM_Act.c	     5   *  Copyright (c) 2019 by Vector Informatik GmbH.                                                  All rights reserved.
; ..\eeprom\NvM\NvM_Act.c	     6   * 
; ..\eeprom\NvM\NvM_Act.c	     7   *                This software is copyright protected and proprietary to Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_Act.c	     8   *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
; ..\eeprom\NvM\NvM_Act.c	     9   *                All other rights remain with Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_Act.c	    10   *  \endverbatim
; ..\eeprom\NvM\NvM_Act.c	    11   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Act.c	    12   *  FILE DESCRIPTION
; ..\eeprom\NvM\NvM_Act.c	    13   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Act.c	    14   *         File:  NvM_Act.c
; ..\eeprom\NvM\NvM_Act.c	    15   *      Project:  MemService_AsrNvM
; ..\eeprom\NvM\NvM_Act.c	    16   *       Module:  NvM - Submodule Act (Actions)
; ..\eeprom\NvM\NvM_Act.c	    17   *    Generator:  -
; ..\eeprom\NvM\NvM_Act.c	    18   *
; ..\eeprom\NvM\NvM_Act.c	    19   *  Description:  This sub-module implements all state machines' action handlers
; ..\eeprom\NvM\NvM_Act.c	    20   *                to be executed upon state transitions
; ..\eeprom\NvM\NvM_Act.c	    21   *
; ..\eeprom\NvM\NvM_Act.c	    22   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    23  
; ..\eeprom\NvM\NvM_Act.c	    24  /* Do not modify this file! */
; ..\eeprom\NvM\NvM_Act.c	    25  
; ..\eeprom\NvM\NvM_Act.c	    26  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    27   *  MODULE SWITCH
; ..\eeprom\NvM\NvM_Act.c	    28   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    29  #define NVM_ACT_SOURCE
; ..\eeprom\NvM\NvM_Act.c	    30  
; ..\eeprom\NvM\NvM_Act.c	    31  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    32   *  INCLUDES
; ..\eeprom\NvM\NvM_Act.c	    33   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    34  #include "Std_Types.h"
; ..\eeprom\NvM\NvM_Act.c	    35  
; ..\eeprom\NvM\NvM_Act.c	    36  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    37   *  INCLUDE OF CONFIGURATION (ALL SECTIONS)
; ..\eeprom\NvM\NvM_Act.c	    38   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    39  #include "NvM_Cfg.h"
; ..\eeprom\NvM\NvM_Act.c	    40  #include "NvM_PrivateCfg.h"
; ..\eeprom\NvM\NvM_Act.c	    41  
; ..\eeprom\NvM\NvM_Act.c	    42  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    43   *  INCLUDE OF THE CENTRAL INTERNAL INCLUDE
; ..\eeprom\NvM\NvM_Act.c	    44   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    45  #include "NvM_JobProc.h"
; ..\eeprom\NvM\NvM_Act.c	    46  
; ..\eeprom\NvM\NvM_Act.c	    47  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    48   *  MODULE HEADER INCLUDES
; ..\eeprom\NvM\NvM_Act.c	    49   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    50  #include "NvM_Act.h"
; ..\eeprom\NvM\NvM_Act.c	    51  
; ..\eeprom\NvM\NvM_Act.c	    52  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    53   *  INCLUDE OF THE QUEUE VARIABLES
; ..\eeprom\NvM\NvM_Act.c	    54   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    55  #include "NvM_Queue.h"
; ..\eeprom\NvM\NvM_Act.c	    56  
; ..\eeprom\NvM\NvM_Act.c	    57  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    58   *  INCLUDE OF THE CRC VARIABLES
; ..\eeprom\NvM\NvM_Act.c	    59   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    60  #include "NvM_Crc.h"
; ..\eeprom\NvM\NvM_Act.c	    61  
; ..\eeprom\NvM\NvM_Act.c	    62  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    63   *  INTERNAL MACROS
; ..\eeprom\NvM\NvM_Act.c	    64   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    65  /* define the maximum number of bytes to by copied by NVM MainFunction.                             *
; ..\eeprom\NvM\NvM_Act.c	    66   * We want to limit maximum run-time of NvM_MainFunction (make it independent from block-size).     *
; ..\eeprom\NvM\NvM_Act.c	    67   * It is derived from NvM_NoOfCrcBytes_u16, however, the factor was arbitrarily chosen.             */
; ..\eeprom\NvM\NvM_Act.c	    68  #define NVM_MAX_DATA_COPY_BYTES (NvM_NoOfCrcBytes_u16 << 2)
; ..\eeprom\NvM\NvM_Act.c	    69  
; ..\eeprom\NvM\NvM_Act.c	    70  /* extracts primary NvBlock state from NvState */
; ..\eeprom\NvM\NvM_Act.c	    71  /* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
; ..\eeprom\NvM\NvM_Act.c	    72  #define NVM_EXTRACT_NVSTATE_PRI(NvState)    (((NvState) >> NVM_PRI_NVBLOCK_STATE_SHIFT) & NVM_NVBLOCK_STATE_BIT_MASK)
; ..\eeprom\NvM\NvM_Act.c	    73  /* extracts secondary NvBlock state from NvState */
; ..\eeprom\NvM\NvM_Act.c	    74  /* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
; ..\eeprom\NvM\NvM_Act.c	    75  #define NVM_EXTRACT_NVSTATE_SEC(NvState)    (((NvState) >> NVM_SEC_NVBLOCK_STATE_SHIFT) & NVM_NVBLOCK_STATE_BIT_MASK)
; ..\eeprom\NvM\NvM_Act.c	    76  
; ..\eeprom\NvM\NvM_Act.c	    77  #ifndef NVM_LOCAL /* COV_NVM_COMPATIBILITY */
; ..\eeprom\NvM\NvM_Act.c	    78  # define NVM_LOCAL static
; ..\eeprom\NvM\NvM_Act.c	    79  #endif
; ..\eeprom\NvM\NvM_Act.c	    80  
; ..\eeprom\NvM\NvM_Act.c	    81  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    82   *  LOCAL DATA PROTOTYPES
; ..\eeprom\NvM\NvM_Act.c	    83   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    84  #define NVM_START_SEC_VAR_NOINIT_8
; ..\eeprom\NvM\NvM_Act.c	    85    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Act.c	    86  
; ..\eeprom\NvM\NvM_Act.c	    87  /* create a test buffer of size 1 byte for testing readability of a block */
; ..\eeprom\NvM\NvM_Act.c	    88  NVM_LOCAL VAR(uint8, NVM_PRIVATE_DATA) NvM_TestBuffer_u8; /* PRQA S 3218 */ /* MD_NvM_8.9_TestBuffer */
; ..\eeprom\NvM\NvM_Act.c	    89  
; ..\eeprom\NvM\NvM_Act.c	    90  #define NVM_STOP_SEC_VAR_NOINIT_8
; ..\eeprom\NvM\NvM_Act.c	    91    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Act.c	    92  
; ..\eeprom\NvM\NvM_Act.c	    93  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	    94   *  INTERNAL FORWARD DECLARATIONS
; ..\eeprom\NvM\NvM_Act.c	    95   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	    96  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_Act.c	    97    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Act.c	    98  
; ..\eeprom\NvM\NvM_Act.c	    99  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   100   *  ACTION HANDLER FUNCTIONS
; ..\eeprom\NvM\NvM_Act.c	   101   *  They are PRIVATE, i.e. they will be called only
; ..\eeprom\NvM\NvM_Act.c	   102   *  inside the NVM. Normally this means "from same segment".
; ..\eeprom\NvM\NvM_Act.c	   103   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   104  
; ..\eeprom\NvM\NvM_Act.c	   105  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   106   * NvM_ActSetInitialAttr
; ..\eeprom\NvM\NvM_Act.c	   107   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   108  /*! \brief Set block to write protected if the block is configured with write once true.
; ..\eeprom\NvM\NvM_Act.c	   109   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   110   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   111   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   112   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   113   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   114   */
; ..\eeprom\NvM\NvM_Act.c	   115  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetInitialAttr(void);
; ..\eeprom\NvM\NvM_Act.c	   116  
; ..\eeprom\NvM\NvM_Act.c	   117  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   118   * NvM_ActInitMainFsm
; ..\eeprom\NvM\NvM_Act.c	   119   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   120  /*! \brief Initialize the main state machine.
; ..\eeprom\NvM\NvM_Act.c	   121   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   122   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   123   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   124   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   125   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   126   */
; ..\eeprom\NvM\NvM_Act.c	   127  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitMainFsm(void);
; ..\eeprom\NvM\NvM_Act.c	   128  
; ..\eeprom\NvM\NvM_Act.c	   129  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   130   * NvM_ActInitBlock
; ..\eeprom\NvM\NvM_Act.c	   131   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   132  /*! \brief Initialize the next block to be processed.
; ..\eeprom\NvM\NvM_Act.c	   133   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   134   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   135   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   136   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   137   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   138   */
; ..\eeprom\NvM\NvM_Act.c	   139  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   140  
; ..\eeprom\NvM\NvM_Act.c	   141  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   142   * NvM_ActInitReadAll
; ..\eeprom\NvM\NvM_Act.c	   143   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   144  /*! \brief Initialize the ReadAll job.
; ..\eeprom\NvM\NvM_Act.c	   145   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   146   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   147   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   148   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   149   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   150   */
; ..\eeprom\NvM\NvM_Act.c	   151  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitReadAll(void);
; ..\eeprom\NvM\NvM_Act.c	   152  
; ..\eeprom\NvM\NvM_Act.c	   153  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   154   * NvM_ActInitReadBlockSubFsm
; ..\eeprom\NvM\NvM_Act.c	   155   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   156  /*! \brief Initialize the ReadBlock state machine.
; ..\eeprom\NvM\NvM_Act.c	   157   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   158   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   159   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   160   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   161   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   162   */
; ..\eeprom\NvM\NvM_Act.c	   163  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitReadBlockSubFsm(void);
; ..\eeprom\NvM\NvM_Act.c	   164  
; ..\eeprom\NvM\NvM_Act.c	   165  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   166   * NvM_ActInitRestoreBlockDefaultsSubFsm
; ..\eeprom\NvM\NvM_Act.c	   167   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   168  /*!
; ..\eeprom\NvM\NvM_Act.c	   169   * \brief Initializes the RestoreRomDefaults state machine.
; ..\eeprom\NvM\NvM_Act.c	   170   * \details -
; ..\eeprom\NvM\NvM_Act.c	   171   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   172   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   173   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   174   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   175   */
; ..\eeprom\NvM\NvM_Act.c	   176  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitRestoreBlockDefaultsSubFsm(void);
; ..\eeprom\NvM\NvM_Act.c	   177  
; ..\eeprom\NvM\NvM_Act.c	   178  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   179   * NvM_ActInitWriteAll
; ..\eeprom\NvM\NvM_Act.c	   180   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   181  /*! \brief Initialize the WriteAll job.
; ..\eeprom\NvM\NvM_Act.c	   182   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   183   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   184   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   185   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   186   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   187   */
; ..\eeprom\NvM\NvM_Act.c	   188  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitWriteAll(void);
; ..\eeprom\NvM\NvM_Act.c	   189  
; ..\eeprom\NvM\NvM_Act.c	   190  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   191   * NvM_ActInitWriteBlock
; ..\eeprom\NvM\NvM_Act.c	   192   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   193  /*! \brief Initialize the current pending block for a WriteBlock job.
; ..\eeprom\NvM\NvM_Act.c	   194   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   195   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   196   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   197   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   198   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   199   */
; ..\eeprom\NvM\NvM_Act.c	   200  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitWriteBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   201  
; ..\eeprom\NvM\NvM_Act.c	   202  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   203   * NvM_ActInitWriteBlockFsm
; ..\eeprom\NvM\NvM_Act.c	   204   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   205  /*! \brief Initialize the WriteBlock FSM as Sub-State Machine
; ..\eeprom\NvM\NvM_Act.c	   206   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   207   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   208   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   209   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   210   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   211   */
; ..\eeprom\NvM\NvM_Act.c	   212  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitWriteBlockFsm(void);
; ..\eeprom\NvM\NvM_Act.c	   213  
; ..\eeprom\NvM\NvM_Act.c	   214  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   215   * NvM_ActInitRestoreBlockDefaults
; ..\eeprom\NvM\NvM_Act.c	   216   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   217  /*! \brief Initialize current pending block for a RestoreBlockDefaults job.
; ..\eeprom\NvM\NvM_Act.c	   218   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   219   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   220   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   221   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   222   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   223   */
; ..\eeprom\NvM\NvM_Act.c	   224  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitRestoreBlockDefaults(void);
; ..\eeprom\NvM\NvM_Act.c	   225  
; ..\eeprom\NvM\NvM_Act.c	   226  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   227   * NvM_ActFinishMainJob
; ..\eeprom\NvM\NvM_Act.c	   228   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   229  /*! \brief Finish current pending job - multi block or single block.
; ..\eeprom\NvM\NvM_Act.c	   230   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   231   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   232   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   233   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   234   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   235   */
; ..\eeprom\NvM\NvM_Act.c	   236  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishMainJob(void);
; ..\eeprom\NvM\NvM_Act.c	   237  
; ..\eeprom\NvM\NvM_Act.c	   238  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	   239  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   240   * NvM_ActKillWriteAll
; ..\eeprom\NvM\NvM_Act.c	   241   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   242  /*! \brief Kill current pending WriteAll-job
; ..\eeprom\NvM\NvM_Act.c	   243   *  \details Finalizes sub FSMs, cleans up internal variables, doesn't set any job result (was set in NvM_KillWritAll API)
; ..\eeprom\NvM\NvM_Act.c	   244   *           Precondition: only called, if QryWriteAllKilled was true - we don't need to check again.
; ..\eeprom\NvM\NvM_Act.c	   245   *           Must be called from Main FSM => because it terminates all SubFsms.
; ..\eeprom\NvM\NvM_Act.c	   246   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   247   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   248   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   249   *  \config KillWriteAll API is enabled
; ..\eeprom\NvM\NvM_Act.c	   250   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   251   */
; ..\eeprom\NvM\NvM_Act.c	   252  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActKillWriteAll(void);
; ..\eeprom\NvM\NvM_Act.c	   253  #endif /* (NVM_KILL_WRITEALL_API == STD_ON) */
; ..\eeprom\NvM\NvM_Act.c	   254  
; ..\eeprom\NvM\NvM_Act.c	   255  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   256   * NvM_ActFinishBlock
; ..\eeprom\NvM\NvM_Act.c	   257   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   258  /*! \brief Finish current pending job.
; ..\eeprom\NvM\NvM_Act.c	   259   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   260   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   261   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   262   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   263   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   264   */
; ..\eeprom\NvM\NvM_Act.c	   265  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   266  
; ..\eeprom\NvM\NvM_Act.c	   267  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   268   * NvM_ActInitNextBlockReadAll
; ..\eeprom\NvM\NvM_Act.c	   269   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   270  /*! \brief Initialize next block to be read during ReadAll job.
; ..\eeprom\NvM\NvM_Act.c	   271   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   272   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   273   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   274   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   275   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   276   */
; ..\eeprom\NvM\NvM_Act.c	   277  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitNextBlockReadAll(void);
; ..\eeprom\NvM\NvM_Act.c	   278  
; ..\eeprom\NvM\NvM_Act.c	   279  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   280   * NvM_ActInitNextBlockWriteAll
; ..\eeprom\NvM\NvM_Act.c	   281   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   282  /*! \brief Initialize next block to be written during WriteAll job.
; ..\eeprom\NvM\NvM_Act.c	   283   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   284   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   285   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   286   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   287   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   288   */
; ..\eeprom\NvM\NvM_Act.c	   289  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitNextBlockWriteAll(void);
; ..\eeprom\NvM\NvM_Act.c	   290  
; ..\eeprom\NvM\NvM_Act.c	   291  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   292   * NvM_ActFinishCfgIdCheck
; ..\eeprom\NvM\NvM_Act.c	   293   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   294  /*! \brief Finalize Block 1 and Configuration Id check during ReadAll processing.
; ..\eeprom\NvM\NvM_Act.c	   295   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   296   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   297   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   298   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   299   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   300   *  \trace CREQ-754
; ..\eeprom\NvM\NvM_Act.c	   301   */
; ..\eeprom\NvM\NvM_Act.c	   302  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishCfgIdCheck(void);
; ..\eeprom\NvM\NvM_Act.c	   303  
; ..\eeprom\NvM\NvM_Act.c	   304  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   305   * NvM_ActFinishReadBlock
; ..\eeprom\NvM\NvM_Act.c	   306   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   307  /*! \brief Finish current ReadBlock job.
; ..\eeprom\NvM\NvM_Act.c	   308   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   309   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   310   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   311   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   312   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   313   */
; ..\eeprom\NvM\NvM_Act.c	   314  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishReadBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   315  
; ..\eeprom\NvM\NvM_Act.c	   316  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   317   * NvM_ActFinishWriteBlock
; ..\eeprom\NvM\NvM_Act.c	   318   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   319  /*! \brief Finish current WriteBlock job.
; ..\eeprom\NvM\NvM_Act.c	   320   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   321   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   322   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   323   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   324   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   325   */
; ..\eeprom\NvM\NvM_Act.c	   326  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishWriteBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   327  
; ..\eeprom\NvM\NvM_Act.c	   328  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM_Act.c	   329  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   330   * NvM_ActFinishEraseBlock
; ..\eeprom\NvM\NvM_Act.c	   331   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   332  /*! \brief Finish current Erase or Invalidate job.
; ..\eeprom\NvM\NvM_Act.c	   333   *  \details Finishes block processing, evaluates result and reports error to DEM, if processing failed.
; ..\eeprom\NvM\NvM_Act.c	   334   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   335   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   336   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   337   *  \config NvM Configuration Class is set to 3
; ..\eeprom\NvM\NvM_Act.c	   338   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   339   */
; ..\eeprom\NvM\NvM_Act.c	   340  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishEraseBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   341  
; ..\eeprom\NvM\NvM_Act.c	   342  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   343   * NvM_ActEraseNvBlock
; ..\eeprom\NvM\NvM_Act.c	   344   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   345  /*! \brief Process Erase job.
; ..\eeprom\NvM\NvM_Act.c	   346   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   347   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   348   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   349   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   350   *  \config NvM Configuration Class is set to 3
; ..\eeprom\NvM\NvM_Act.c	   351   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   352   */
; ..\eeprom\NvM\NvM_Act.c	   353  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActEraseNvBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   354  
; ..\eeprom\NvM\NvM_Act.c	   355  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   356   * NvM_ActInvalidateNvBlock
; ..\eeprom\NvM\NvM_Act.c	   357   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   358  /*! \brief Process Invalidate job.
; ..\eeprom\NvM\NvM_Act.c	   359   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   360   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   361   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   362   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   363   *  \config NvM Configuration Class is set to 3
; ..\eeprom\NvM\NvM_Act.c	   364   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   365   */
; ..\eeprom\NvM\NvM_Act.c	   366  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInvalidateNvBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   367  #endif /* (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3) */
; ..\eeprom\NvM\NvM_Act.c	   368  
; ..\eeprom\NvM\NvM_Act.c	   369  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   370   * NvM_ActProcessCrc
; ..\eeprom\NvM\NvM_Act.c	   371   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   372  /*! \brief Process Crc calculation.
; ..\eeprom\NvM\NvM_Act.c	   373   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   374   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   375   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   376   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   377   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   378   */
; ..\eeprom\NvM\NvM_Act.c	   379  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActProcessCrc(void);
; ..\eeprom\NvM\NvM_Act.c	   380  
; ..\eeprom\NvM\NvM_Act.c	   381  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   382   * NvM_ActWriteNvBlock
; ..\eeprom\NvM\NvM_Act.c	   383   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   384  /*! \brief Process WriteBlock job.
; ..\eeprom\NvM\NvM_Act.c	   385   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   386   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   387   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   388   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   389   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   390   */
; ..\eeprom\NvM\NvM_Act.c	   391  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActWriteNvBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   392  
; ..\eeprom\NvM\NvM_Act.c	   393  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   394   * NvM_ActReadNvBlock
; ..\eeprom\NvM\NvM_Act.c	   395   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   396  /*! \brief Process ReadBlock job.
; ..\eeprom\NvM\NvM_Act.c	   397   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   398   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   399   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   400   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   401   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   402   */
; ..\eeprom\NvM\NvM_Act.c	   403  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActReadNvBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   404  
; ..\eeprom\NvM\NvM_Act.c	   405  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   406   * NvM_ActProcessCrcRead
; ..\eeprom\NvM\NvM_Act.c	   407   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   408  /*! \brief Trigger Crc calculation.
; ..\eeprom\NvM\NvM_Act.c	   409   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   410   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   411   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   412   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   413   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   414   */
; ..\eeprom\NvM\NvM_Act.c	   415  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActProcessCrcRead(void);
; ..\eeprom\NvM\NvM_Act.c	   416  
; ..\eeprom\NvM\NvM_Act.c	   417  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   418   * NvM_ActReadCopyData
; ..\eeprom\NvM\NvM_Act.c	   419   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   420  /*!
; ..\eeprom\NvM\NvM_Act.c	   421   * \brief       Copy the read data to target.
; ..\eeprom\NvM\NvM_Act.c	   422   * \details     Copy from internal buffer to the user buffer, invoke explicit synchronization - what ever necessary.
; ..\eeprom\NvM\NvM_Act.c	   423   * \context     TASK
; ..\eeprom\NvM\NvM_Act.c	   424   * \reentrant   FALSE
; ..\eeprom\NvM\NvM_Act.c	   425   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   426   * \pre         -
; ..\eeprom\NvM\NvM_Act.c	   427   */
; ..\eeprom\NvM\NvM_Act.c	   428  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActReadCopyData(void);
; ..\eeprom\NvM\NvM_Act.c	   429  
; ..\eeprom\NvM\NvM_Act.c	   430  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   431   * NvM_ActRestoreRomDefaults
; ..\eeprom\NvM\NvM_Act.c	   432   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   433  /*! \brief Process RestoreBlockDefaults job.
; ..\eeprom\NvM\NvM_Act.c	   434   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   435   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   436   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   437   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   438   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   439   */
; ..\eeprom\NvM\NvM_Act.c	   440  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRestoreRomDefaults(void);
; ..\eeprom\NvM\NvM_Act.c	   441  
; ..\eeprom\NvM\NvM_Act.c	   442  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   443   * NvM_ActFinishRestoreRomDefaults
; ..\eeprom\NvM\NvM_Act.c	   444   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   445  /*! \brief Finish RestoreBlockDefaults job.
; ..\eeprom\NvM\NvM_Act.c	   446   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   447   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   448   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   449   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   450   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   451   */
; ..\eeprom\NvM\NvM_Act.c	   452  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishRestoreRomDefaults(void);
; ..\eeprom\NvM\NvM_Act.c	   453  
; ..\eeprom\NvM\NvM_Act.c	   454  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   455   * NvM_ActTestBlockBlank
; ..\eeprom\NvM\NvM_Act.c	   456   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   457  /*! \brief Check whether the current pending block can be read or not.
; ..\eeprom\NvM\NvM_Act.c	   458   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   459   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   460   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   461   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   462   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   463   */
; ..\eeprom\NvM\NvM_Act.c	   464  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActTestBlockBlank(void);
; ..\eeprom\NvM\NvM_Act.c	   465  
; ..\eeprom\NvM\NvM_Act.c	   466  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   467   * NvM_ActValidateRam
; ..\eeprom\NvM\NvM_Act.c	   468   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   469  /*! \brief If current RAM Block is permanent, mark it valid and unchanged.
; ..\eeprom\NvM\NvM_Act.c	   470   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   471   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   472   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   473   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   474   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   475   */
; ..\eeprom\NvM\NvM_Act.c	   476  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActValidateRam(void);
; ..\eeprom\NvM\NvM_Act.c	   477  
; ..\eeprom\NvM\NvM_Act.c	   478  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   479   * NvM_ActSetupRedundant
; ..\eeprom\NvM\NvM_Act.c	   480   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   481  /*! \brief Setup Redundant NV Block and reset write retries.
; ..\eeprom\NvM\NvM_Act.c	   482   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   483   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   484   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   485   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   486   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   487   */
; ..\eeprom\NvM\NvM_Act.c	   488  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetupRedundant(void);
; ..\eeprom\NvM\NvM_Act.c	   489  
; ..\eeprom\NvM\NvM_Act.c	   490  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   491   * NvM_ActSetupOther
; ..\eeprom\NvM\NvM_Act.c	   492   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   493  /*! \brief Setup the other NV Block at redundant blocks and reset write retries
; ..\eeprom\NvM\NvM_Act.c	   494   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   495   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   496   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   497   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   498   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   499   */
; ..\eeprom\NvM\NvM_Act.c	   500  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetupOther(void);
; ..\eeprom\NvM\NvM_Act.c	   501  
; ..\eeprom\NvM\NvM_Act.c	   502  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   503   * NvM_ActUpdateNvState
; ..\eeprom\NvM\NvM_Act.c	   504   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   505  /*! \brief Updates NvState dependent on last result and marks the other NvBlock as active, if it is a redundant block.
; ..\eeprom\NvM\NvM_Act.c	   506   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   507   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   508   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   509   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   510   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   511   */
; ..\eeprom\NvM\NvM_Act.c	   512  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActUpdateNvState(void);
; ..\eeprom\NvM\NvM_Act.c	   513  
; ..\eeprom\NvM\NvM_Act.c	   514  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   515   * NvM_ActSetReqIntegrityFailed
; ..\eeprom\NvM\NvM_Act.c	   516   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   517  /*! \brief Set current pending block's result to integrity failed.
; ..\eeprom\NvM\NvM_Act.c	   518   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   519   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   520   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   521   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   522   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   523   */
; ..\eeprom\NvM\NvM_Act.c	   524  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetReqIntegrityFailed(void);
; ..\eeprom\NvM\NvM_Act.c	   525  
; ..\eeprom\NvM\NvM_Act.c	   526  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   527   * NvM_ActSetReqSkipped
; ..\eeprom\NvM\NvM_Act.c	   528   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   529  /*! \brief Set current pending block's result to skipped.
; ..\eeprom\NvM\NvM_Act.c	   530   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   531   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   532   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   533   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   534   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   535   */
; ..\eeprom\NvM\NvM_Act.c	   536  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetReqSkipped(void);
; ..\eeprom\NvM\NvM_Act.c	   537  
; ..\eeprom\NvM\NvM_Act.c	   538  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   539   * NvM_ActSetReqNotOk
; ..\eeprom\NvM\NvM_Act.c	   540   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   541  /*! \brief Set current pending block's result to NOT_OK.
; ..\eeprom\NvM\NvM_Act.c	   542   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   543   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   544   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   545   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   546   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   547   */
; ..\eeprom\NvM\NvM_Act.c	   548  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetReqNotOk(void);
; ..\eeprom\NvM\NvM_Act.c	   549  
; ..\eeprom\NvM\NvM_Act.c	   550  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   551   * NvM_SetBlockPendingWriteAll
; ..\eeprom\NvM\NvM_Act.c	   552   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   553  /*! \brief Set current block to pending and invoke notifications.
; ..\eeprom\NvM\NvM_Act.c	   554   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   555   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   556   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   557   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   558   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   559   */
; ..\eeprom\NvM\NvM_Act.c	   560  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_SetBlockPendingWriteAll(void);
; ..\eeprom\NvM\NvM_Act.c	   561  
; ..\eeprom\NvM\NvM_Act.c	   562  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   563   * NvM_ActWait
; ..\eeprom\NvM\NvM_Act.c	   564   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   565  /*! \brief Set wait flag.
; ..\eeprom\NvM\NvM_Act.c	   566   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   567   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   568   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   569   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   570   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   571   */
; ..\eeprom\NvM\NvM_Act.c	   572  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActWait(void);
; ..\eeprom\NvM\NvM_Act.c	   573  
; ..\eeprom\NvM\NvM_Act.c	   574  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   575   * NvM_ActNop
; ..\eeprom\NvM\NvM_Act.c	   576   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   577  /*! \brief Do nothing, just return.
; ..\eeprom\NvM\NvM_Act.c	   578   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   579   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   580   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   581   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   582   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   583   */
; ..\eeprom\NvM\NvM_Act.c	   584  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActNop(void);
; ..\eeprom\NvM\NvM_Act.c	   585  
; ..\eeprom\NvM\NvM_Act.c	   586  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   587   * NvM_ActGetMultiBlockJob
; ..\eeprom\NvM\NvM_Act.c	   588   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   589  /*! \brief Setup a multi block job - ReadAll or WriteAll - depending on requests.
; ..\eeprom\NvM\NvM_Act.c	   590   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   591   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   592   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   593   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   594   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   595   */
; ..\eeprom\NvM\NvM_Act.c	   596  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActGetMultiBlockJob(void);
; ..\eeprom\NvM\NvM_Act.c	   597  
; ..\eeprom\NvM\NvM_Act.c	   598  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   599   * NvM_ActCopyNvDataToBuf
; ..\eeprom\NvM\NvM_Act.c	   600   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   601  /*! \brief Prepares data for CRC calculation and writing.
; ..\eeprom\NvM\NvM_Act.c	   602   *  \details Takes care about data creation (explicit synchronization, copy to internal buffer) and about the pre write
; ..\eeprom\NvM\NvM_Act.c	   603   *           data transformation handling.
; ..\eeprom\NvM\NvM_Act.c	   604   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   605   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   606   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   607   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   608   */
; ..\eeprom\NvM\NvM_Act.c	   609  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActCopyNvDataToBuf(void);
; ..\eeprom\NvM\NvM_Act.c	   610  
; ..\eeprom\NvM\NvM_Act.c	   611  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   612   * NvM_ActKillSubFsm
; ..\eeprom\NvM\NvM_Act.c	   613   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   614  /*!
; ..\eeprom\NvM\NvM_Act.c	   615   * \brief Resets the sub state machine to NVM_STATE_FSM_FINISHED (kills the state machine immediately).
; ..\eeprom\NvM\NvM_Act.c	   616   * \details -
; ..\eeprom\NvM\NvM_Act.c	   617   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   618   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   619   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   620   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   621   */
; ..\eeprom\NvM\NvM_Act.c	   622  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActKillSubFsm(void);
; ..\eeprom\NvM\NvM_Act.c	   623  
; ..\eeprom\NvM\NvM_Act.c	   624  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	   625  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   626   * NvM_ActRepairRedBlocksInit
; ..\eeprom\NvM\NvM_Act.c	   627   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   628  /*!
; ..\eeprom\NvM\NvM_Act.c	   629   * \brief Initialize the Repair Redundant Blocks job.
; ..\eeprom\NvM\NvM_Act.c	   630   * \details -
; ..\eeprom\NvM\NvM_Act.c	   631   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   632   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   633   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   634   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   635   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   636   */
; ..\eeprom\NvM\NvM_Act.c	   637  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksInit(void);
; ..\eeprom\NvM\NvM_Act.c	   638  
; ..\eeprom\NvM\NvM_Act.c	   639  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   640   * NvM_ActRepairRedBlocksInitNext
; ..\eeprom\NvM\NvM_Act.c	   641   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   642  /*!
; ..\eeprom\NvM\NvM_Act.c	   643   * \brief Initialize next redundant block to check whether it has to be repaired.
; ..\eeprom\NvM\NvM_Act.c	   644   * \details Search for the next redundant block within current configuration and sets the next block Id to check.
; ..\eeprom\NvM\NvM_Act.c	   645   *          In case there is no redundant block any more the repair job will terminate.
; ..\eeprom\NvM\NvM_Act.c	   646   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   647   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   648   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   649   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   650   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   651   */
; ..\eeprom\NvM\NvM_Act.c	   652  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksInitNext(void);
; ..\eeprom\NvM\NvM_Act.c	   653  
; ..\eeprom\NvM\NvM_Act.c	   654  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   655   * NVM_ActRepairRedBlocksInitBlock
; ..\eeprom\NvM\NvM_Act.c	   656   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   657   /*!
; ..\eeprom\NvM\NvM_Act.c	   658   * \brief Initialize the repair job for current block Id.
; ..\eeprom\NvM\NvM_Act.c	   659   * \details -
; ..\eeprom\NvM\NvM_Act.c	   660   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   661   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   662   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   663   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   664   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   665   */
; ..\eeprom\NvM\NvM_Act.c	   666  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NVM_ActRepairRedBlocksInitBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   667  
; ..\eeprom\NvM\NvM_Act.c	   668  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   669   * NvM_ActRepairRedBlockReadCheck
; ..\eeprom\NvM\NvM_Act.c	   670   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   671  /*!
; ..\eeprom\NvM\NvM_Act.c	   672   * \brief Setups the read request to underlying modules for currently processing block.
; ..\eeprom\NvM\NvM_Act.c	   673   * \details -
; ..\eeprom\NvM\NvM_Act.c	   674   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   675   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   676   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   677   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   678   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   679   */
; ..\eeprom\NvM\NvM_Act.c	   680  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlockReadCheck(void);
; ..\eeprom\NvM\NvM_Act.c	   681  
; ..\eeprom\NvM\NvM_Act.c	   682  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   683   * NvM_ActRepairRedBlockFinishReadCheck
; ..\eeprom\NvM\NvM_Act.c	   684   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   685  /*!
; ..\eeprom\NvM\NvM_Act.c	   686   * \brief Setup a read request to underlying modules for a sub-block to check its validity.
; ..\eeprom\NvM\NvM_Act.c	   687   * \details The NvM differs between two possible validity checks: for blocks with and without Crc. For blocks with Crc
; ..\eeprom\NvM\NvM_Act.c	   688   *          the Crc has to be checked to ensure block validity, for blocks without Crc a readable block is a valid block.
; ..\eeprom\NvM\NvM_Act.c	   689   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   690   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   691   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   692   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   693   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   694   */
; ..\eeprom\NvM\NvM_Act.c	   695  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlockFinishReadCheck(void);
; ..\eeprom\NvM\NvM_Act.c	   696  
; ..\eeprom\NvM\NvM_Act.c	   697  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   698   * NvM_ActRepairRedBlocksReadValid
; ..\eeprom\NvM\NvM_Act.c	   699   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   700  /*!
; ..\eeprom\NvM\NvM_Act.c	   701   * \brief Read the sub-block which is marked as valid.
; ..\eeprom\NvM\NvM_Act.c	   702   * \details -
; ..\eeprom\NvM\NvM_Act.c	   703   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   704   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   705   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   706   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   707   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   708   */
; ..\eeprom\NvM\NvM_Act.c	   709  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksReadValid(void);
; ..\eeprom\NvM\NvM_Act.c	   710  
; ..\eeprom\NvM\NvM_Act.c	   711  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   712   * NvM_ActRepairRedBlockWriteInvalid
; ..\eeprom\NvM\NvM_Act.c	   713   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   714  /*!
; ..\eeprom\NvM\NvM_Act.c	   715   * \brief Writes the previously read data to the sub-block which is marked as invalid.
; ..\eeprom\NvM\NvM_Act.c	   716   * \details -
; ..\eeprom\NvM\NvM_Act.c	   717   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   718   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   719   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   720   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   721   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   722   */
; ..\eeprom\NvM\NvM_Act.c	   723  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlockWriteInvalid(void);
; ..\eeprom\NvM\NvM_Act.c	   724  
; ..\eeprom\NvM\NvM_Act.c	   725  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   726   * NvM_ActRepairRedBlocksFinishBlock
; ..\eeprom\NvM\NvM_Act.c	   727   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   728  /*!
; ..\eeprom\NvM\NvM_Act.c	   729   * \brief Finish the repair job for current NvM block.
; ..\eeprom\NvM\NvM_Act.c	   730   * \details The function decides whether a DEM error shall be reported or not, depending on successful or failed restoring.
; ..\eeprom\NvM\NvM_Act.c	   731   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   732   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   733   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   734   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   735   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   736   */
; ..\eeprom\NvM\NvM_Act.c	   737  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksFinishBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   738  
; ..\eeprom\NvM\NvM_Act.c	   739  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   740   * NvM_ActRepairRedBlocksFinish
; ..\eeprom\NvM\NvM_Act.c	   741   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   742  /*!
; ..\eeprom\NvM\NvM_Act.c	   743   * \brief Reset used internal variable and finish the repair redundant block job.
; ..\eeprom\NvM\NvM_Act.c	   744   * \details -
; ..\eeprom\NvM\NvM_Act.c	   745   * \config RepairRedundantBlocks API is enabled
; ..\eeprom\NvM\NvM_Act.c	   746   * \context TASK
; ..\eeprom\NvM\NvM_Act.c	   747   * \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   748   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   749   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   750   */
; ..\eeprom\NvM\NvM_Act.c	   751  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksFinish(void);
; ..\eeprom\NvM\NvM_Act.c	   752  #endif /* (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON) */
; ..\eeprom\NvM\NvM_Act.c	   753  
; ..\eeprom\NvM\NvM_Act.c	   754  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   755  *  INTERNAL HELPER FUNCTIONS
; ..\eeprom\NvM\NvM_Act.c	   756  *  They are PRIVATE, i.e. they will be called only
; ..\eeprom\NvM\NvM_Act.c	   757  *  inside the NVM. Normally this means "from same segment".
; ..\eeprom\NvM\NvM_Act.c	   758  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   759  
; ..\eeprom\NvM\NvM_Act.c	   760  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   761   * NvM_UpdateConfigIdBlock
; ..\eeprom\NvM\NvM_Act.c	   762   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   763  /*! \brief Updates ConfigId (in RAM block), marks Block as VALID and CHANGED (to be written during NvM_WriteAll) and
; ..\eeprom\NvM\NvM_Act.c	   764   *         triggers RAM Crc-Recalculation.
; ..\eeprom\NvM\NvM_Act.c	   765   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   766   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   767   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   768   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   769   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   770   */
; ..\eeprom\NvM\NvM_Act.c	   771  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_UpdateConfigIdBlock(void);
; ..\eeprom\NvM\NvM_Act.c	   772  
; ..\eeprom\NvM\NvM_Act.c	   773  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   774   * NvM_InternalCopyData
; ..\eeprom\NvM\NvM_Act.c	   775   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   776  /*! \brief Copy specific number of data bytes from source to destination.
; ..\eeprom\NvM\NvM_Act.c	   777   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   778   *  \param[in] info_pt
; ..\eeprom\NvM\NvM_Act.c	   779   *  \param[in,out] destPtr
; ..\eeprom\NvM\NvM_Act.c	   780   *  \param[in] srcPtr
; ..\eeprom\NvM\NvM_Act.c	   781   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   782   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   783   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   784   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   785   */
; ..\eeprom\NvM\NvM_Act.c	   786  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_InternalCopyData
; ..\eeprom\NvM\NvM_Act.c	   787      (NvM_BlockInfoPtrType info_pt, NvM_RamAddressType destPtr, NvM_ConstRamAddressType srcPtr);
; ..\eeprom\NvM\NvM_Act.c	   788  
; ..\eeprom\NvM\NvM_Act.c	   789  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   790   * NvM_InternalCopyBufferedData
; ..\eeprom\NvM\NvM_Act.c	   791   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   792  /*! \brief If configured, the function will the explicit synchronization mechanism, if not configured the data will be
; ..\eeprom\NvM\NvM_Act.c	   793   *         copied from internal buffer to Ram (permanent or non-permanent).
; ..\eeprom\NvM\NvM_Act.c	   794   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   795   *  \param[in] info_pt
; ..\eeprom\NvM\NvM_Act.c	   796   *  \param[in] srcPtr
; ..\eeprom\NvM\NvM_Act.c	   797   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   798   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   799   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   800   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   801   */
; ..\eeprom\NvM\NvM_Act.c	   802  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_InternalCopyBufferedData
; ..\eeprom\NvM\NvM_Act.c	   803      (NvM_BlockInfoPtrType info_pt, NvM_ConstRamAddressType srcPtr);
; ..\eeprom\NvM\NvM_Act.c	   804  
; ..\eeprom\NvM\NvM_Act.c	   805  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   806   * NvM_IntCreateNvState
; ..\eeprom\NvM\NvM_Act.c	   807   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   808  /*! \brief Create current NvState and marks the other one active.
; ..\eeprom\NvM\NvM_Act.c	   809   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   810   *  \param[in] NvState
; ..\eeprom\NvM\NvM_Act.c	   811   *  \param[in,out] NewState
; ..\eeprom\NvM\NvM_Act.c	   812   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   813   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   814   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   815   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   816   */
; ..\eeprom\NvM\NvM_Act.c	   817  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_IntCreateNvState(P2VAR(uint8, AUTOMATIC, NVM_PRIVATE_DATA) NvState, uint8 NewState);
; ..\eeprom\NvM\NvM_Act.c	   818  
; ..\eeprom\NvM\NvM_Act.c	   819  #if(NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	   820  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   821   * NvM_IsWriteAllAndKilled
; ..\eeprom\NvM\NvM_Act.c	   822   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   823  /*! \brief Check whether a WriteAll and KillWriteAll requests were setup.
; ..\eeprom\NvM\NvM_Act.c	   824   *  \details -
; ..\eeprom\NvM\NvM_Act.c	   825   *  \param[in] currServiceId
; ..\eeprom\NvM\NvM_Act.c	   826   *  \param[in] currApiFlag
; ..\eeprom\NvM\NvM_Act.c	   827   *  \return TRUE in case the given service Id references WriteAll job and KillWriteAll flag is set in given API flag
; ..\eeprom\NvM\NvM_Act.c	   828   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_Act.c	   829   *  \config KillWriteAll is enabled
; ..\eeprom\NvM\NvM_Act.c	   830   *  \context TASK
; ..\eeprom\NvM\NvM_Act.c	   831   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Act.c	   832   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   833   *  \pre -
; ..\eeprom\NvM\NvM_Act.c	   834   */
; ..\eeprom\NvM\NvM_Act.c	   835  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_IsWriteAllAndKilled(const NvM_InternalServiceIdType currServiceId, const uint8 currApiFlag);
; ..\eeprom\NvM\NvM_Act.c	   836  #endif /* (NVM_KILL_WRITEALL_API == STD_ON) */
; ..\eeprom\NvM\NvM_Act.c	   837  
; ..\eeprom\NvM\NvM_Act.c	   838  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   839   * NvM_IntUpdateCurrentBlockCRCCompareData
; ..\eeprom\NvM\NvM_Act.c	   840   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   841  /*!
; ..\eeprom\NvM\NvM_Act.c	   842   * \brief       Updates or invalidates the current CRCCompareMechanism CRC buffer depending on the given job result.
; ..\eeprom\NvM\NvM_Act.c	   843   * \details     -
; ..\eeprom\NvM\NvM_Act.c	   844   * \param[in]   result - OK=store calculated CRC; NOT_OK=invalidate CRC
; ..\eeprom\NvM\NvM_Act.c	   845   * \context     TASK
; ..\eeprom\NvM\NvM_Act.c	   846   * \reentrant   FALSE
; ..\eeprom\NvM\NvM_Act.c	   847   * \synchronous TRUE
; ..\eeprom\NvM\NvM_Act.c	   848   * \pre -
; ..\eeprom\NvM\NvM_Act.c	   849   */
; ..\eeprom\NvM\NvM_Act.c	   850  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_IntUpdateCurrentBlockCRCCompareData(const NvM_RequestResultType result);
; ..\eeprom\NvM\NvM_Act.c	   851  
; ..\eeprom\NvM\NvM_Act.c	   852  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_Act.c	   853    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Act.c	   854  
; ..\eeprom\NvM\NvM_Act.c	   855  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   856   *  INTERNAL MODULE GLOBAL VARIABLES
; ..\eeprom\NvM\NvM_Act.c	   857   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   858  
; ..\eeprom\NvM\NvM_Act.c	   859  #define NVM_START_SEC_CONST_UNSPECIFIED
; ..\eeprom\NvM\NvM_Act.c	   860    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Act.c	   861  
; ..\eeprom\NvM\NvM_Act.c	   862  /* function pointer table storing all actions called from NvM's state machine
; ..\eeprom\NvM\NvM_Act.c	   863   * (state machine accesses the actions via indexes) */
; ..\eeprom\NvM\NvM_Act.c	   864  CONST(NvM_ActFctPtrType, NVM_PRIVATE_CONST) NvM_ActionTable_ap[(uint32)NVM_ACT_ID_Nop + 1u] =
; ..\eeprom\NvM\NvM_Act.c	   865  {
; ..\eeprom\NvM\NvM_Act.c	   866      NvM_ActSetInitialAttr,
; ..\eeprom\NvM\NvM_Act.c	   867      NvM_ActInitMainFsm,
; ..\eeprom\NvM\NvM_Act.c	   868      NvM_ActInitBlock,
; ..\eeprom\NvM\NvM_Act.c	   869      NvM_ActInitReadAll,
; ..\eeprom\NvM\NvM_Act.c	   870      NvM_ActInitReadBlockSubFsm,
; ..\eeprom\NvM\NvM_Act.c	   871      NvM_ActInitRestoreBlockDefaultsSubFsm,
; ..\eeprom\NvM\NvM_Act.c	   872      NvM_ActInitWriteAll,
; ..\eeprom\NvM\NvM_Act.c	   873      NvM_ActInitWriteBlock,
; ..\eeprom\NvM\NvM_Act.c	   874      NvM_ActInitWriteBlockFsm,
; ..\eeprom\NvM\NvM_Act.c	   875      NvM_ActInitRestoreBlockDefaults,
; ..\eeprom\NvM\NvM_Act.c	   876      NvM_ActFinishMainJob,
; ..\eeprom\NvM\NvM_Act.c	   877  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	   878      NvM_ActKillWriteAll,
; ..\eeprom\NvM\NvM_Act.c	   879  #endif
; ..\eeprom\NvM\NvM_Act.c	   880      NvM_ActFinishBlock,
; ..\eeprom\NvM\NvM_Act.c	   881      NvM_ActInitNextBlockReadAll,
; ..\eeprom\NvM\NvM_Act.c	   882      NvM_ActInitNextBlockWriteAll,
; ..\eeprom\NvM\NvM_Act.c	   883      NvM_ActFinishCfgIdCheck,
; ..\eeprom\NvM\NvM_Act.c	   884      NvM_ActFinishReadBlock,
; ..\eeprom\NvM\NvM_Act.c	   885      NvM_ActFinishWriteBlock,
; ..\eeprom\NvM\NvM_Act.c	   886  
; ..\eeprom\NvM\NvM_Act.c	   887  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM_Act.c	   888      NvM_ActFinishEraseBlock,
; ..\eeprom\NvM\NvM_Act.c	   889      NvM_ActEraseNvBlock,
; ..\eeprom\NvM\NvM_Act.c	   890      NvM_ActInvalidateNvBlock,
; ..\eeprom\NvM\NvM_Act.c	   891  #endif
; ..\eeprom\NvM\NvM_Act.c	   892      NvM_ActProcessCrc,
; ..\eeprom\NvM\NvM_Act.c	   893      NvM_ActWriteNvBlock,
; ..\eeprom\NvM\NvM_Act.c	   894      NvM_ActReadNvBlock,
; ..\eeprom\NvM\NvM_Act.c	   895      NvM_ActProcessCrcRead,
; ..\eeprom\NvM\NvM_Act.c	   896      NvM_ActReadCopyData,
; ..\eeprom\NvM\NvM_Act.c	   897      NvM_ActRestoreRomDefaults,
; ..\eeprom\NvM\NvM_Act.c	   898      NvM_ActFinishRestoreRomDefaults,
; ..\eeprom\NvM\NvM_Act.c	   899      NvM_ActTestBlockBlank,
; ..\eeprom\NvM\NvM_Act.c	   900      NvM_ActValidateRam,
; ..\eeprom\NvM\NvM_Act.c	   901      NvM_ActSetupRedundant,
; ..\eeprom\NvM\NvM_Act.c	   902      NvM_ActSetupOther,
; ..\eeprom\NvM\NvM_Act.c	   903      NvM_ActUpdateNvState,
; ..\eeprom\NvM\NvM_Act.c	   904      NvM_ActSetReqIntegrityFailed,
; ..\eeprom\NvM\NvM_Act.c	   905      NvM_ActSetReqSkipped,
; ..\eeprom\NvM\NvM_Act.c	   906      NvM_ActSetReqNotOk,
; ..\eeprom\NvM\NvM_Act.c	   907      NvM_ActSetReqOk,
; ..\eeprom\NvM\NvM_Act.c	   908      NvM_SetBlockPendingWriteAll,
; ..\eeprom\NvM\NvM_Act.c	   909      NvM_ActCopyNvDataToBuf,
; ..\eeprom\NvM\NvM_Act.c	   910      NvM_ActGetMultiBlockJob,
; ..\eeprom\NvM\NvM_Act.c	   911      NvM_ActCancelNV,
; ..\eeprom\NvM\NvM_Act.c	   912      NvM_ActKillSubFsm,
; ..\eeprom\NvM\NvM_Act.c	   913      NvM_ActFinishReadBlockAndSetSkipped,
; ..\eeprom\NvM\NvM_Act.c	   914  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	   915      NvM_ActRepairRedBlocksInit,
; ..\eeprom\NvM\NvM_Act.c	   916      NvM_ActRepairRedBlocksInitNext,
; ..\eeprom\NvM\NvM_Act.c	   917      NvM_ActRepairRedBlockReadCheck,
; ..\eeprom\NvM\NvM_Act.c	   918      NvM_ActRepairRedBlockFinishReadCheck,
; ..\eeprom\NvM\NvM_Act.c	   919      NvM_ActRepairRedBlocksReadValid,
; ..\eeprom\NvM\NvM_Act.c	   920      NvM_ActRepairRedBlockWriteInvalid,
; ..\eeprom\NvM\NvM_Act.c	   921      NvM_ActRepairRedBlocksFinishBlock,
; ..\eeprom\NvM\NvM_Act.c	   922      NvM_ActRepairRedBlocksFinish,
; ..\eeprom\NvM\NvM_Act.c	   923  #endif
; ..\eeprom\NvM\NvM_Act.c	   924  #if (NVM_API_CONFIG_CLASS != NVM_API_CONFIG_CLASS_1)
; ..\eeprom\NvM\NvM_Act.c	   925      NvM_ActGetNormalPrioJob,
; ..\eeprom\NvM\NvM_Act.c	   926  # if (NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	   927          NvM_ActGetHighPrioJob,
; ..\eeprom\NvM\NvM_Act.c	   928          NvM_ActQueueFreeLastJob,
; ..\eeprom\NvM\NvM_Act.c	   929  # endif
; ..\eeprom\NvM\NvM_Act.c	   930  #endif
; ..\eeprom\NvM\NvM_Act.c	   931      NvM_ActWait,
; ..\eeprom\NvM\NvM_Act.c	   932      NvM_ActNop
; ..\eeprom\NvM\NvM_Act.c	   933  };
; ..\eeprom\NvM\NvM_Act.c	   934  
; ..\eeprom\NvM\NvM_Act.c	   935  #define NVM_STOP_SEC_CONST_UNSPECIFIED
; ..\eeprom\NvM\NvM_Act.c	   936    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Act.c	   937  
; ..\eeprom\NvM\NvM_Act.c	   938  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   939   *  IMPLEMENTATION
; ..\eeprom\NvM\NvM_Act.c	   940   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   941  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_Act.c	   942    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Act.c	   943  
; ..\eeprom\NvM\NvM_Act.c	   944  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM_Act.c	   945  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   946  *  NvM_ActEraseNvBlock
; ..\eeprom\NvM\NvM_Act.c	   947  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   948  /*!
; ..\eeprom\NvM\NvM_Act.c	   949   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	   950   *
; ..\eeprom\NvM\NvM_Act.c	   951   *
; ..\eeprom\NvM\NvM_Act.c	   952   *
; ..\eeprom\NvM\NvM_Act.c	   953   *
; ..\eeprom\NvM\NvM_Act.c	   954   *
; ..\eeprom\NvM\NvM_Act.c	   955   */
; ..\eeprom\NvM\NvM_Act.c	   956  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActEraseNvBlock(void)
; Function NvM_ActEraseNvBlock
.L111:
NvM_ActEraseNvBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	   957  {
; ..\eeprom\NvM\NvM_Act.c	   958      if (E_OK != MemIf_EraseImmediateBlock((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8,
; ..\eeprom\NvM\NvM_Act.c	   959                                            NvM_CurrentBlockInfo_t.NvIdentifier_u16))
	fcall	.cocofun_1
.L1093:
	call	MemIf_EraseImmediateBlock
.L1094:
	fcall	.cocofun_5
.L1095:

; ..\eeprom\NvM\NvM_Act.c	   960      {
; ..\eeprom\NvM\NvM_Act.c	   961          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Act.c	   962      }
; ..\eeprom\NvM\NvM_Act.c	   963      else
; ..\eeprom\NvM\NvM_Act.c	   964      {
; ..\eeprom\NvM\NvM_Act.c	   965          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
; ..\eeprom\NvM\NvM_Act.c	   966      }
; ..\eeprom\NvM\NvM_Act.c	   967  }
	ret
.L641:
	
__NvM_ActEraseNvBlock_function_end:
	.size	NvM_ActEraseNvBlock,__NvM_ActEraseNvBlock_function_end-NvM_ActEraseNvBlock
.L365:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_5',code,cluster('.cocofun_5')
	.sect	'.text.NvM_Act..cocofun_5'
	.align	2
; Function .cocofun_5
.L113:
.cocofun_5:	.type	func
; Function body .cocofun_5, coco_iter:0
	eq	d15,d2,#0
.L1433:
	add	d15,#1
	st.b	[a15]40,d15
.L1434:
	fret
.L530:
	; End of function
	.sdecl	'.text.NvM_Act..cocofun_1',code,cluster('.cocofun_1')
	.sect	'.text.NvM_Act..cocofun_1'
	.align	2
; Function .cocofun_1
.L115:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	fcall	.cocofun_14
.L1405:
	ld.a	a2,[a15]
.L1406:
	ld.hu	d5,[a15]36
.L1407:
	ld.bu	d15,[a2]58
.L1408:
	and	d4,d15,#15
	fret
.L510:
	; End of function
	.sdecl	'.text.NvM_Act..cocofun_14',code,cluster('.cocofun_14')
	.sect	'.text.NvM_Act..cocofun_14'
	.align	2
; Function .cocofun_14
.L117:
.cocofun_14:	.type	func
; Function body .cocofun_14, coco_iter:1
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t)
	lea	a15,[a15]@los(NvM_CurrentBlockInfo_t)
.L1488:
	fret
.L575:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActFinishBlock',code,cluster('NvM_ActFinishBlock')
	.sect	'.text.NvM_Act.NvM_ActFinishBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	   968  #endif /* (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3) */
; ..\eeprom\NvM\NvM_Act.c	   969  
; ..\eeprom\NvM\NvM_Act.c	   970  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	   971  *  NvM_ActFinishBlock
; ..\eeprom\NvM\NvM_Act.c	   972  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	   973  /*!
; ..\eeprom\NvM\NvM_Act.c	   974   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	   975   *
; ..\eeprom\NvM\NvM_Act.c	   976   *
; ..\eeprom\NvM\NvM_Act.c	   977   *
; ..\eeprom\NvM\NvM_Act.c	   978   *
; ..\eeprom\NvM\NvM_Act.c	   979   */
; ..\eeprom\NvM\NvM_Act.c	   980  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishBlock(void)
; Function NvM_ActFinishBlock
.L119:
NvM_ActFinishBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	   981  {
; ..\eeprom\NvM\NvM_Act.c	   982  #if(NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	   983      /* set result only, if KillWriteAll was not called; otherwise it was set by KillWriteAll */
; ..\eeprom\NvM\NvM_Act.c	   984      if(!NvM_IsWriteAllAndKilled(NvM_CurrentJob_t.JobServiceId_t, NvM_ApiFlags_u8)) /* COV_NVM_KILLWRITEALL */
	fcall	.cocofun_6
.L967:
	mov.aa	a13,a12
	ld.bu	d4,[+a13]2
.L968:
	movh.a	a2,#@his(NvM_ApiFlags_u8)
	ld.bu	d5,[a2]@los(NvM_ApiFlags_u8)
	call	NvM_IsWriteAllAndKilled
.L969:
	jne	d2,#0,.L2
.L970:

; ..\eeprom\NvM\NvM_Act.c	   985      {
; ..\eeprom\NvM\NvM_Act.c	   986          NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamErrorStatus_u8 = NvM_CurrentBlockInfo_t.LastResult_t; /* SBSW_NvM_Access_CurrBlockInfo */
	movh.a	a2,#@his(NvM_CurrentBlockInfo_t)
	lea	a2,[a2]@los(NvM_CurrentBlockInfo_t)
.L971:
	ld.a	a15,[a2]4
.L972:
	ld.bu	d15,[a2]40
.L973:
	st.b	[a15]1,d15
.L2:

; ..\eeprom\NvM\NvM_Act.c	   987      }
; ..\eeprom\NvM\NvM_Act.c	   988  
; ..\eeprom\NvM\NvM_Act.c	   989  #else
; ..\eeprom\NvM\NvM_Act.c	   990      NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamErrorStatus_u8 = NvM_CurrentBlockInfo_t.LastResult_t; /* SBSW_NvM_Access_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	   991  #endif
; ..\eeprom\NvM\NvM_Act.c	   992  
; ..\eeprom\NvM\NvM_Act.c	   993      /* call block notification(s), except the block was skipped during WriteAll (see SREQ SREQ00024689) */
; ..\eeprom\NvM\NvM_Act.c	   994      if((NvM_CurrentJob_t.JobServiceId_t != NVM_INT_FID_WRITE_ALL) || (NvM_CurrentBlockInfo_t.LastResult_t != NVM_REQ_BLOCK_SKIPPED))
	ld.bu	d15,[a13]
.L974:
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L975:
	lea	a2,[a15]@los(NvM_CurrentBlockInfo_t+40)
.L976:
	jne	d15,#5,.L3
.L977:
	ld.bu	d0,[a2]
.L978:
	jeq	d0,#4,.L4
.L3:

; ..\eeprom\NvM\NvM_Act.c	   995      {
; ..\eeprom\NvM\NvM_Act.c	   996          NvM_BlockNotification(NvM_CurrentJob_t.JobBlockId_t, 
	ld.hu	d4,[a12]0
.L979:

; ..\eeprom\NvM\NvM_Act.c	   997                                NvM_IntServiceDescrTable_at[NvM_CurrentJob_t.JobServiceId_t].PublicFid_t,
	fcall	.cocofun_7
.L980:
	ld.bu	d5,[a15]2
.L981:

; ..\eeprom\NvM\NvM_Act.c	   998                                NvM_CurrentBlockInfo_t.LastResult_t);
	ld.bu	d6,[a2]
	call	NvM_BlockNotification
.L4:

; ..\eeprom\NvM\NvM_Act.c	   999      }
; ..\eeprom\NvM\NvM_Act.c	  1000  
; ..\eeprom\NvM\NvM_Act.c	  1001      /* #30 reset current job's RamBlock, which is important for ReadAll and WriteAll */
; ..\eeprom\NvM\NvM_Act.c	  1002      NvM_CurrentJob_t.RamAddr_t = NULL_PTR;
	mov.a	a15,#0
.L982:
	st.a	[a12]4,a15
.L983:

; ..\eeprom\NvM\NvM_Act.c	  1003  }
	ret
.L619:
	
__NvM_ActFinishBlock_function_end:
	.size	NvM_ActFinishBlock,__NvM_ActFinishBlock_function_end-NvM_ActFinishBlock
.L330:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_7',code,cluster('.cocofun_7')
	.sect	'.text.NvM_Act..cocofun_7'
	.align	2
; Function .cocofun_7
.L121:
.cocofun_7:	.type	func
; Function body .cocofun_7, coco_iter:0
	movh.a	a15,#@his(NvM_IntServiceDescrTable_at)
	lea	a15,[a15]@los(NvM_IntServiceDescrTable_at)
.L1444:
	addsc.a	a15,a15,d15,#2
.L1445:
	fret
.L540:
	; End of function
	.sdecl	'.text.NvM_Act..cocofun_6',code,cluster('.cocofun_6')
	.sect	'.text.NvM_Act..cocofun_6'
	.align	2
; Function .cocofun_6
.L123:
.cocofun_6:	.type	func
; Function body .cocofun_6, coco_iter:0
	movh.a	a12,#@his(NvM_CurrentJob_t)
	lea	a12,[a12]@los(NvM_CurrentJob_t)
.L1439:
	fret
.L535:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActInitNextBlockReadAll',code,cluster('NvM_ActInitNextBlockReadAll')
	.sect	'.text.NvM_Act.NvM_ActInitNextBlockReadAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1004  
; ..\eeprom\NvM\NvM_Act.c	  1005  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1006  *  NvM_ActInitNextBlockReadAll
; ..\eeprom\NvM\NvM_Act.c	  1007  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1008  /*!
; ..\eeprom\NvM\NvM_Act.c	  1009   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1010   *
; ..\eeprom\NvM\NvM_Act.c	  1011   *
; ..\eeprom\NvM\NvM_Act.c	  1012   *
; ..\eeprom\NvM\NvM_Act.c	  1013   *
; ..\eeprom\NvM\NvM_Act.c	  1014   *
; ..\eeprom\NvM\NvM_Act.c	  1015   *
; ..\eeprom\NvM\NvM_Act.c	  1016   *
; ..\eeprom\NvM\NvM_Act.c	  1017   */
; ..\eeprom\NvM\NvM_Act.c	  1018  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitNextBlockReadAll(void)
; Function NvM_ActInitNextBlockReadAll
.L125:
NvM_ActInitNextBlockReadAll:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1019  {
; ..\eeprom\NvM\NvM_Act.c	  1020      NvM_ActFinishBlock();
	call	NvM_ActFinishBlock
.L988:

; ..\eeprom\NvM\NvM_Act.c	  1021      NvM_CurrentJob_t.JobBlockId_t++;
	fcall	.cocofun_6
.L989:
	ld.hu	d15,[a12]0
.L990:

; ..\eeprom\NvM\NvM_Act.c	  1022      if(NvM_CurrentJob_t.JobBlockId_t < NvM_NoOfBlockIds_t)
	movh.a	a15,#@his(NvM_NoOfBlockIds_t)
.L991:
	add	d15,#1
	st.h	[a12],d15
.L992:
	ld.hu	d15,[a12]0
.L993:
	ld.hu	d0,[a15]@los(NvM_NoOfBlockIds_t)
.L994:
	jge.u	d15,d0,.L5
.L995:

; ..\eeprom\NvM\NvM_Act.c	  1023      {
; ..\eeprom\NvM\NvM_Act.c	  1024          NvM_ActInitBlock();
	call	NvM_ActInitBlock
.L996:

; ..\eeprom\NvM\NvM_Act.c	  1025          NvM_CrcJob_Create(&NvM_CurrentBlockInfo_t.BlockCrcJob_t, NvM_CurrentJob_t.JobBlockId_t, /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  1026                             NvM_CurrentBlockInfo_t.Descriptor_pt->RamBlockDataAddr_t,
; ..\eeprom\NvM\NvM_Act.c	  1027                             NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockNVRAMDataLength);
	fcall	.cocofun_8
.L997:
	ld.a	a2,[a13]
.L998:
	lea	a4,[a13]16
.L999:
	ld.hu	d4,[a12]0
.L1000:
	ld.a	a5,[a2]
.L1001:
	ld.hu	d5,[a2]54
	call	NvM_CrcJob_Create
.L1002:

; ..\eeprom\NvM\NvM_Act.c	  1028  
; ..\eeprom\NvM\NvM_Act.c	  1029  #if(NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1030      /* Re-assign CRC buffer to Block's configured CRC buffer. It works, even if CRC buffer is NULL_PTR
; ..\eeprom\NvM\NvM_Act.c	  1031       * (i.e. no CRC configured), because in this case the CRC job is designed to prevent from using
; ..\eeprom\NvM\NvM_Act.c	  1032       * (dereferencing) this pointer. */
; ..\eeprom\NvM\NvM_Act.c	  1033      NvM_CrcJob_ReassignBuffer(&NvM_CurrentBlockInfo_t.BlockCrcJob_t, /* SBSW_NvM_AccessPtr_CrcReassignBuffer */
	ld.a	a15,[a13]
	ld.a	a15,[a15]40
	st.a	[a13]24,a15
.L5:

; ..\eeprom\NvM\NvM_Act.c	  1034                      NvM_CurrentBlockInfo_t.Descriptor_pt->RamBlockCrcAddr_t);
; ..\eeprom\NvM\NvM_Act.c	  1035  #endif
; ..\eeprom\NvM\NvM_Act.c	  1036      }
; ..\eeprom\NvM\NvM_Act.c	  1037  }
	ret
.L621:
	
__NvM_ActInitNextBlockReadAll_function_end:
	.size	NvM_ActInitNextBlockReadAll,__NvM_ActInitNextBlockReadAll_function_end-NvM_ActInitNextBlockReadAll
.L335:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_8',code,cluster('.cocofun_8')
	.sect	'.text.NvM_Act..cocofun_8'
	.align	2
; Function .cocofun_8
.L127:
.cocofun_8:	.type	func
; Function body .cocofun_8, coco_iter:0
	movh.a	a13,#@his(NvM_CurrentBlockInfo_t)
	lea	a13,[a13]@los(NvM_CurrentBlockInfo_t)
.L1450:
	fret
.L545:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActInitNextBlockWriteAll',code,cluster('NvM_ActInitNextBlockWriteAll')
	.sect	'.text.NvM_Act.NvM_ActInitNextBlockWriteAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1038  
; ..\eeprom\NvM\NvM_Act.c	  1039  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1040  *  NvM_ActInitNextBlockWriteAll
; ..\eeprom\NvM\NvM_Act.c	  1041  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1042  /*!
; ..\eeprom\NvM\NvM_Act.c	  1043   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1044   *
; ..\eeprom\NvM\NvM_Act.c	  1045   *
; ..\eeprom\NvM\NvM_Act.c	  1046   *
; ..\eeprom\NvM\NvM_Act.c	  1047   *
; ..\eeprom\NvM\NvM_Act.c	  1048   */
; ..\eeprom\NvM\NvM_Act.c	  1049  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitNextBlockWriteAll(void)
; Function NvM_ActInitNextBlockWriteAll
.L129:
NvM_ActInitNextBlockWriteAll:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1050  {
; ..\eeprom\NvM\NvM_Act.c	  1051      NvM_ActFinishBlock();
	call	NvM_ActFinishBlock
.L1007:

; ..\eeprom\NvM\NvM_Act.c	  1052  
; ..\eeprom\NvM\NvM_Act.c	  1053      --NvM_CurrentJob_t.JobBlockId_t; /* Check whether decrement is valid has already been done */
	movh.a	a15,#@his(NvM_CurrentJob_t)
.L1008:
	ld.hu	d15,[a15]@los(NvM_CurrentJob_t)
.L1009:
	add	d15,#-1
	st.h	[a15]@los(NvM_CurrentJob_t),d15
.L1010:

; ..\eeprom\NvM\NvM_Act.c	  1054  
; ..\eeprom\NvM\NvM_Act.c	  1055      NvM_ActInitBlock(); /* Initialize internal data; it simplifies access to Block information below */
	j	NvM_ActInitBlock
.L623:
	
__NvM_ActInitNextBlockWriteAll_function_end:
	.size	NvM_ActInitNextBlockWriteAll,__NvM_ActInitNextBlockWriteAll_function_end-NvM_ActInitNextBlockWriteAll
.L340:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActFinishMainJob',code,cluster('NvM_ActFinishMainJob')
	.sect	'.text.NvM_Act.NvM_ActFinishMainJob'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1056  }
; ..\eeprom\NvM\NvM_Act.c	  1057  
; ..\eeprom\NvM\NvM_Act.c	  1058  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1059  *  NvM_ActFinishMainJob
; ..\eeprom\NvM\NvM_Act.c	  1060  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1061  /*!
; ..\eeprom\NvM\NvM_Act.c	  1062   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1063   *
; ..\eeprom\NvM\NvM_Act.c	  1064   *
; ..\eeprom\NvM\NvM_Act.c	  1065   *
; ..\eeprom\NvM\NvM_Act.c	  1066   *
; ..\eeprom\NvM\NvM_Act.c	  1067   *
; ..\eeprom\NvM\NvM_Act.c	  1068   *
; ..\eeprom\NvM\NvM_Act.c	  1069   *
; ..\eeprom\NvM\NvM_Act.c	  1070   *
; ..\eeprom\NvM\NvM_Act.c	  1071   *
; ..\eeprom\NvM\NvM_Act.c	  1072   *
; ..\eeprom\NvM\NvM_Act.c	  1073   */
; ..\eeprom\NvM\NvM_Act.c	  1074  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishMainJob(void)
; Function NvM_ActFinishMainJob
.L131:
NvM_ActFinishMainJob:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1075  {
; ..\eeprom\NvM\NvM_Act.c	  1076      const NvM_InternalServiceIdType currSrvId = NvM_CurrentJob_t.JobServiceId_t;
	fcall	.cocofun_6
.L930:
	ld.bu	d15,[+a12]2
.L709:

; ..\eeprom\NvM\NvM_Act.c	  1077  
; ..\eeprom\NvM\NvM_Act.c	  1078  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Act.c	  1079      if(currSrvId < NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS)
; ..\eeprom\NvM\NvM_Act.c	  1080  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Act.c	  1081      {
; ..\eeprom\NvM\NvM_Act.c	  1082          if((currSrvId == NVM_INT_FID_READ_ALL) || (currSrvId == NVM_INT_FID_WRITE_ALL)) /* COV_NVM_APICFGCLASS */
	jeq	d15,#6,.L6
.L931:
	jne	d15,#5,.L7
.L6:

; ..\eeprom\NvM\NvM_Act.c	  1083          {
; ..\eeprom\NvM\NvM_Act.c	  1084              NvM_RequestResultType JobResult;
; ..\eeprom\NvM\NvM_Act.c	  1085  
; ..\eeprom\NvM\NvM_Act.c	  1086              if((currSrvId == NVM_INT_FID_WRITE_ALL) && ((NvM_ApiFlags_u8 & NVM_APIFLAG_CANCEL_WR_ALL_SET) != 0u))
	jne	d15,#5,.L8
.L932:
	movh.a	a13,#@his(NvM_ApiFlags_u8)
	lea	a13,[a13]@los(NvM_ApiFlags_u8)
	ld.bu	d0,[a13]
.L933:
	jnz.t	d0:4,.L9
.L8:
	movh.a	a13,#@his(NvM_ApiFlags_u8)
	lea	a13,[a13]@los(NvM_ApiFlags_u8)
.L934:

; ..\eeprom\NvM\NvM_Act.c	  1087              {
; ..\eeprom\NvM\NvM_Act.c	  1088                  JobResult = NVM_REQ_CANCELED;
; ..\eeprom\NvM\NvM_Act.c	  1089              }
; ..\eeprom\NvM\NvM_Act.c	  1090              /* PRQA S 3415 1 */ /* MD_NvM_13.5_ReadAllAndKillReadAll */
; ..\eeprom\NvM\NvM_Act.c	  1091              else if((currSrvId == NVM_INT_FID_READ_ALL) && (NvM_QryReadAllKilled() == TRUE))
	jne	d15,#6,.L10
.L935:
	call	NvM_QryReadAllKilled
.L936:
	jne	d2,#1,.L11
.L9:

; ..\eeprom\NvM\NvM_Act.c	  1092              {
; ..\eeprom\NvM\NvM_Act.c	  1093                  JobResult = NVM_REQ_CANCELED;
	mov	d5,#6
	j	.L12
.L11:
.L10:

; ..\eeprom\NvM\NvM_Act.c	  1094              }
; ..\eeprom\NvM\NvM_Act.c	  1095              else if((NvM_CurrentBlockInfo_t.InternalFlags_u8 & NVM_INTFLAG_ERROR_SET) != 0u)
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+42)
.L937:
	ld.bu	d0,[a15]@los(NvM_CurrentBlockInfo_t+42)
.L938:
	and	d0,d0,#16
.L939:
	ne	d5,d0,#0
.L12:

; ..\eeprom\NvM\NvM_Act.c	  1096              {
; ..\eeprom\NvM\NvM_Act.c	  1097                  JobResult = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Act.c	  1098              }
; ..\eeprom\NvM\NvM_Act.c	  1099              else
; ..\eeprom\NvM\NvM_Act.c	  1100              {
; ..\eeprom\NvM\NvM_Act.c	  1101                  JobResult = NVM_REQ_OK;
; ..\eeprom\NvM\NvM_Act.c	  1102              }
; ..\eeprom\NvM\NvM_Act.c	  1103  
; ..\eeprom\NvM\NvM_Act.c	  1104              NvM_BlockMngmtArea_at[0].NvRamErrorStatus_u8 = JobResult; /* SBSW_NvM_AccessBlockManagementArea */
	movh.a	a15,#@his(NvM_BlockMngmtArea_at+1)
.L940:
	st.b	[a15]@los(NvM_BlockMngmtArea_at+1),d5
.L941:

; ..\eeprom\NvM\NvM_Act.c	  1105  
; ..\eeprom\NvM\NvM_Act.c	  1106              /* Internal Callback encapsulation - it is not a function pointer */
; ..\eeprom\NvM\NvM_Act.c	  1107              NvM_MultiBlockCbk(NvM_IntServiceDescrTable_at[currSrvId].PublicFid_t, JobResult);
	fcall	.cocofun_7
.L942:
	ld.bu	d4,[a15]2
.L943:
	call	NvM_MultiBlockCbk
.L708:

; ..\eeprom\NvM\NvM_Act.c	  1108  
; ..\eeprom\NvM\NvM_Act.c	  1109              NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L944:

; ..\eeprom\NvM\NvM_Act.c	  1110  
; ..\eeprom\NvM\NvM_Act.c	  1111              NvM_ApiFlags_u8 &= (NVM_APIFLAG_READ_ALL_CL & NVM_APIFLAG_WRITE_ALL_CL);
	ld.bu	d15,[a13]
.L710:
	and	d15,#249
	st.b	[a13],d15
.L945:

; ..\eeprom\NvM\NvM_Act.c	  1112  
; ..\eeprom\NvM\NvM_Act.c	  1113              NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L946:

; ..\eeprom\NvM\NvM_Act.c	  1114  
; ..\eeprom\NvM\NvM_Act.c	  1115  
; ..\eeprom\NvM\NvM_Act.c	  1116      #if(NVM_DRV_MODE_SWITCH == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1117              MemIf_SetMode(MEMIF_MODE_SLOW);
	mov	d4,#0
	call	MemIf_SetMode
.L616:
	j	.L13
.L7:

; ..\eeprom\NvM\NvM_Act.c	  1118      #endif
; ..\eeprom\NvM\NvM_Act.c	  1119          }
; ..\eeprom\NvM\NvM_Act.c	  1120          else
; ..\eeprom\NvM\NvM_Act.c	  1121          {
; ..\eeprom\NvM\NvM_Act.c	  1122              NvM_ActFinishBlock();
	call	NvM_ActFinishBlock
.L13:

; ..\eeprom\NvM\NvM_Act.c	  1123          }
; ..\eeprom\NvM\NvM_Act.c	  1124      }
; ..\eeprom\NvM\NvM_Act.c	  1125  
; ..\eeprom\NvM\NvM_Act.c	  1126      NvM_CurrentJob_t.JobServiceId_t = NVM_INT_FID_NO_JOB_PENDING;
	mov	d15,#8
	st.b	[a12],d15
.L947:

; ..\eeprom\NvM\NvM_Act.c	  1127  } /* PRQA S 6080 */ /* MD_MSR_STMIF */
	ret
.L613:
	
__NvM_ActFinishMainJob_function_end:
	.size	NvM_ActFinishMainJob,__NvM_ActFinishMainJob_function_end-NvM_ActFinishMainJob
.L320:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActKillWriteAll',code,cluster('NvM_ActKillWriteAll')
	.sect	'.text.NvM_Act.NvM_ActKillWriteAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1128  
; ..\eeprom\NvM\NvM_Act.c	  1129  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1130  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1131  *  NvM_ActKillWriteAll
; ..\eeprom\NvM\NvM_Act.c	  1132  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1133  /*!
; ..\eeprom\NvM\NvM_Act.c	  1134   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1135   *
; ..\eeprom\NvM\NvM_Act.c	  1136   *
; ..\eeprom\NvM\NvM_Act.c	  1137   *
; ..\eeprom\NvM\NvM_Act.c	  1138   *
; ..\eeprom\NvM\NvM_Act.c	  1139   *
; ..\eeprom\NvM\NvM_Act.c	  1140   *
; ..\eeprom\NvM\NvM_Act.c	  1141   */
; ..\eeprom\NvM\NvM_Act.c	  1142  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActKillWriteAll(void)
; Function NvM_ActKillWriteAll
.L133:
NvM_ActKillWriteAll:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1143  {
; ..\eeprom\NvM\NvM_Act.c	  1144  #if(NVM_DRV_MODE_SWITCH == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1145      MemIf_SetMode(MEMIF_MODE_SLOW);
	mov	d4,#0
	call	MemIf_SetMode
.L952:

; ..\eeprom\NvM\NvM_Act.c	  1146  #endif
; ..\eeprom\NvM\NvM_Act.c	  1147      NvM_ActCancelNV();
	call	NvM_ActCancelNV
.L953:

; ..\eeprom\NvM\NvM_Act.c	  1148  
; ..\eeprom\NvM\NvM_Act.c	  1149      NvM_CurrentJob_t.JobServiceId_t = NVM_INT_FID_NO_JOB_PENDING;
	movh.a	a15,#@his(NvM_CurrentJob_t+2)
.L954:
	mov	d15,#8
	st.b	[a15]@los(NvM_CurrentJob_t+2),d15
.L955:

; ..\eeprom\NvM\NvM_Act.c	  1150  
; ..\eeprom\NvM\NvM_Act.c	  1151      /* Kill sub-FSMs */
; ..\eeprom\NvM\NvM_Act.c	  1152      NvM_JobMainState_t = NVM_STATE_FSM_FINISHED;
	movh.a	a15,#@his(NvM_JobMainState_t)
.L956:
	mov	d15,#32
	st.b	[a15]@los(NvM_JobMainState_t),d15
.L957:

; ..\eeprom\NvM\NvM_Act.c	  1153      NvM_JobSubState_t = NVM_STATE_FSM_FINISHED;
	movh.a	a15,#@his(NvM_JobSubState_t)
.L958:
	st.b	[a15]@los(NvM_JobSubState_t),d15
.L959:

; ..\eeprom\NvM\NvM_Act.c	  1154  
; ..\eeprom\NvM\NvM_Act.c	  1155      NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L960:

; ..\eeprom\NvM\NvM_Act.c	  1156      /* Note that (theoretically) ReadAll as well as WriteAll might already have been requested.
; ..\eeprom\NvM\NvM_Act.c	  1157       * => NvM_GetStatus(0, ...) delivers "CANCELLED", since the calling NvM_KillWriteAll */
; ..\eeprom\NvM\NvM_Act.c	  1158      NvM_ApiFlags_u8 &= (NVM_APIFLAG_CANCEL_WR_ALL_CL & NVM_APIFLAG_KILL_WR_ALL_CL);
	movh.a	a15,#@his(NvM_ApiFlags_u8)
	ld.bu	d15,[a15]@los(NvM_ApiFlags_u8)
.L961:
	and	d15,#207
	st.b	[a15]@los(NvM_ApiFlags_u8),d15
.L962:

; ..\eeprom\NvM\NvM_Act.c	  1159  
; ..\eeprom\NvM\NvM_Act.c	  1160      NvM_ExitCriticalSection();
	j	NvM_ExitCriticalSection
.L618:
	
__NvM_ActKillWriteAll_function_end:
	.size	NvM_ActKillWriteAll,__NvM_ActKillWriteAll_function_end-NvM_ActKillWriteAll
.L325:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActFinishReadBlock',code,cluster('NvM_ActFinishReadBlock')
	.sect	'.text.NvM_Act.NvM_ActFinishReadBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1161  }
; ..\eeprom\NvM\NvM_Act.c	  1162  #endif /* (NVM_KILL_WRITEALL_API == STD_ON) */
; ..\eeprom\NvM\NvM_Act.c	  1163  
; ..\eeprom\NvM\NvM_Act.c	  1164  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1165  *  NvM_ActFinishReadBlock
; ..\eeprom\NvM\NvM_Act.c	  1166  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1167  /*!
; ..\eeprom\NvM\NvM_Act.c	  1168   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1169   *
; ..\eeprom\NvM\NvM_Act.c	  1170   *
; ..\eeprom\NvM\NvM_Act.c	  1171   *
; ..\eeprom\NvM\NvM_Act.c	  1172   *
; ..\eeprom\NvM\NvM_Act.c	  1173   *
; ..\eeprom\NvM\NvM_Act.c	  1174   *
; ..\eeprom\NvM\NvM_Act.c	  1175   *
; ..\eeprom\NvM\NvM_Act.c	  1176   *
; ..\eeprom\NvM\NvM_Act.c	  1177   *
; ..\eeprom\NvM\NvM_Act.c	  1178   *
; ..\eeprom\NvM\NvM_Act.c	  1179   *
; ..\eeprom\NvM\NvM_Act.c	  1180   *
; ..\eeprom\NvM\NvM_Act.c	  1181   */
; ..\eeprom\NvM\NvM_Act.c	  1182  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishReadBlock(void)
; Function NvM_ActFinishReadBlock
.L135:
NvM_ActFinishReadBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1183  {
; ..\eeprom\NvM\NvM_Act.c	  1184      const NvM_RamMngmtPtrType MngmtPtr = NvM_CurrentBlockInfo_t.Mngmt_pt;
	fcall	.cocofun_8
.L1034:
	ld.a	a15,[a13]4
.L711:

; ..\eeprom\NvM\NvM_Act.c	  1185      const NvM_BlockDescrPtrType descr_pt = NvM_CurrentBlockInfo_t.Descriptor_pt;
	ld.a	a14,[a13]
.L714:

; ..\eeprom\NvM\NvM_Act.c	  1186      /* updates NvState for current block and changes active block */
; ..\eeprom\NvM\NvM_Act.c	  1187      NvM_ActUpdateNvState();
	call	NvM_ActUpdateNvState
.L1035:

; ..\eeprom\NvM\NvM_Act.c	  1188  
; ..\eeprom\NvM\NvM_Act.c	  1189      if(((descr_pt->MngmtType_t & NVM_BLOCK_REDUNDANT) != 0u)    &&
	ld.bu	d15,[a14]58
	extr.u	d15,d15,#4,#2
.L1036:
	jz.t	d15:0,.L14
.L1037:

; ..\eeprom\NvM\NvM_Act.c	  1190         (NVM_EXTRACT_NVSTATE_PRI(NvM_CurrentBlockInfo_t.NvState_u8) != NVM_NVBLOCK_STATE_OUTDATED) &&
	ld.bu	d0,[a13]43
	sha	d15,d0,#-5
	and	d15,#3
.L1038:
	jeq	d15,#1,.L15
.L1039:

; ..\eeprom\NvM\NvM_Act.c	  1191         (NVM_EXTRACT_NVSTATE_SEC(NvM_CurrentBlockInfo_t.NvState_u8) == NVM_NVBLOCK_STATE_OUTDATED))
	sha	d0,#-3
	and	d15,d0,#3
.L1040:
	jne	d15,#1,.L16
.L1041:

; ..\eeprom\NvM\NvM_Act.c	  1192      {
; ..\eeprom\NvM\NvM_Act.c	  1193          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
	mov	d15,#1
	st.b	[a13]40,d15
.L16:
.L15:
.L14:

; ..\eeprom\NvM\NvM_Act.c	  1194      }
; ..\eeprom\NvM\NvM_Act.c	  1195  
; ..\eeprom\NvM\NvM_Act.c	  1196      if((descr_pt->Flags_u8 & NVM_BLOCK_WRITE_BLOCK_ONCE_ON) == NVM_BLOCK_WRITE_BLOCK_ONCE_ON)
	ld.bu	d15,[a14]59
.L1042:
	lea	a12,[a13]40
.L1043:
	jz.t	d15:2,.L17
.L1044:

; ..\eeprom\NvM\NvM_Act.c	  1197      {
; ..\eeprom\NvM\NvM_Act.c	  1198          if(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_OK)
	ld.bu	d0,[a12]
.L1045:

; ..\eeprom\NvM\NvM_Act.c	  1199          {
; ..\eeprom\NvM\NvM_Act.c	  1200              MngmtPtr->NvRamAttributes_u8 |= NVM_WR_PROT_SET; /* SBSW_NvM_Access_CurrBlockInfo */
	ld.bu	d15,[a15]2
.L1046:
	jne	d0,#0,.L18
.L1047:
	or	d15,#128
	j	.L19
.L18:

; ..\eeprom\NvM\NvM_Act.c	  1201          }
; ..\eeprom\NvM\NvM_Act.c	  1202          else
; ..\eeprom\NvM\NvM_Act.c	  1203          {
; ..\eeprom\NvM\NvM_Act.c	  1204              MngmtPtr->NvRamAttributes_u8 &= NVM_WR_PROT_CL; /* SBSW_NvM_Access_CurrBlockInfo */
	and	d15,#127
.L19:
	st.b	[a15]2,d15
.L17:

; ..\eeprom\NvM\NvM_Act.c	  1205          }
; ..\eeprom\NvM\NvM_Act.c	  1206      }
; ..\eeprom\NvM\NvM_Act.c	  1207  
; ..\eeprom\NvM\NvM_Act.c	  1208      NvM_IntUpdateCurrentBlockCRCCompareData(NvM_CurrentBlockInfo_t.LastResult_t);
	ld.bu	d4,[a12]
	call	NvM_IntUpdateCurrentBlockCRCCompareData
.L1048:

; ..\eeprom\NvM\NvM_Act.c	  1209  
; ..\eeprom\NvM\NvM_Act.c	  1210      if(NvM_CurrentBlockInfo_t.LastResult_t != NVM_REQ_OK)
	ld.bu	d15,[a12]
.L1049:
	jeq	d15,#0,.L20
.L1050:

; ..\eeprom\NvM\NvM_Act.c	  1211      {
; ..\eeprom\NvM\NvM_Act.c	  1212          NvM_CurrentBlockInfo_t.InternalFlags_u8 |= NVM_INTFLAG_ERROR_SET;
	ld.bu	d15,[a13]42
.L1051:
	or	d15,#16
	st.b	[a13]42,d15
.L20:

; ..\eeprom\NvM\NvM_Act.c	  1213  
; ..\eeprom\NvM\NvM_Act.c	  1214          if(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_NOT_OK)
; ..\eeprom\NvM\NvM_Act.c	  1215          {
; ..\eeprom\NvM\NvM_Act.c	  1216              NvM_DemReportErrorReqFailed();
; ..\eeprom\NvM\NvM_Act.c	  1217          }
; ..\eeprom\NvM\NvM_Act.c	  1218          else if(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_INTEGRITY_FAILED)
; ..\eeprom\NvM\NvM_Act.c	  1219          {
; ..\eeprom\NvM\NvM_Act.c	  1220              NvM_DemReportErrorIntegrityFailed();
; ..\eeprom\NvM\NvM_Act.c	  1221          }
; ..\eeprom\NvM\NvM_Act.c	  1222          else
; ..\eeprom\NvM\NvM_Act.c	  1223          {
; ..\eeprom\NvM\NvM_Act.c	  1224              /* Nothing to do */
; ..\eeprom\NvM\NvM_Act.c	  1225          }
; ..\eeprom\NvM\NvM_Act.c	  1226      }
; ..\eeprom\NvM\NvM_Act.c	  1227  
; ..\eeprom\NvM\NvM_Act.c	  1228  #if(NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1229      /* Save new CRC (RAM CRC), if requested RAM block is permanent (configured) RAM block */
; ..\eeprom\NvM\NvM_Act.c	  1230      if((NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_OK) &&
	ld.bu	d15,[a12]
.L1052:
	jne	d15,#0,.L21
.L1053:

; ..\eeprom\NvM\NvM_Act.c	  1231         (NvM_CurrentJob_t.RamAddr_t == NULL_PTR)) /* COV_NVM_APICFGCLASS */
	fcall	.cocofun_13
.L712:
	jne	d15,#0,.L22
.L1054:

; ..\eeprom\NvM\NvM_Act.c	  1232      { /* Export function checks whether block has CRC AND CRC buffer (no "RAM CRC") */
; ..\eeprom\NvM\NvM_Act.c	  1233          NvM_CrcJob_ExportBufferedValue(&NvM_CurrentBlockInfo_t.BlockCrcJob_t, descr_pt->RamBlockCrcAddr_t); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	ld.a	a5,[a14]40
.L1055:
	lea	a4,[a13]16
.L1056:
	j	NvM_CrcJob_ExportBufferedValue
.L22:
.L21:

; ..\eeprom\NvM\NvM_Act.c	  1234      }
; ..\eeprom\NvM\NvM_Act.c	  1235  #endif
; ..\eeprom\NvM\NvM_Act.c	  1236  }
	ret
.L625:
	
__NvM_ActFinishReadBlock_function_end:
	.size	NvM_ActFinishReadBlock,__NvM_ActFinishReadBlock_function_end-NvM_ActFinishReadBlock
.L350:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_13',code,cluster('.cocofun_13')
	.sect	'.text.NvM_Act..cocofun_13'
	.align	2
; Function .cocofun_13
.L137:
.cocofun_13:	.type	func
; Function body .cocofun_13, coco_iter:0
	movh.a	a15,#@his(NvM_CurrentJob_t+4)
.L713:
	ld.w	d15,[a15]@los(NvM_CurrentJob_t+4)
.L1483:
	fret
.L570:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActFinishReadBlockAndSetSkipped',code,cluster('NvM_ActFinishReadBlockAndSetSkipped')
	.sect	'.text.NvM_Act.NvM_ActFinishReadBlockAndSetSkipped'
	.align	2
	
	.global	NvM_ActFinishReadBlockAndSetSkipped

; ..\eeprom\NvM\NvM_Act.c	  1237  
; ..\eeprom\NvM\NvM_Act.c	  1238  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1239  *  NvM_ActFinishReadBlockAndSetSkipped
; ..\eeprom\NvM\NvM_Act.c	  1240  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1241  /*!
; ..\eeprom\NvM\NvM_Act.c	  1242   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1243   *
; ..\eeprom\NvM\NvM_Act.c	  1244   *
; ..\eeprom\NvM\NvM_Act.c	  1245   *
; ..\eeprom\NvM\NvM_Act.c	  1246   */
; ..\eeprom\NvM\NvM_Act.c	  1247  FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishReadBlockAndSetSkipped(void)
; Function NvM_ActFinishReadBlockAndSetSkipped
.L139:
NvM_ActFinishReadBlockAndSetSkipped:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1248  {
; ..\eeprom\NvM\NvM_Act.c	  1249      NvM_ActFinishReadBlock();
	call	NvM_ActFinishReadBlock
.L773:

; ..\eeprom\NvM\NvM_Act.c	  1250      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_BLOCK_SKIPPED;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L774:
	mov	d15,#4
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+40),d15
.L775:

; ..\eeprom\NvM\NvM_Act.c	  1251  }
	ret
.L582:
	
__NvM_ActFinishReadBlockAndSetSkipped_function_end:
	.size	NvM_ActFinishReadBlockAndSetSkipped,__NvM_ActFinishReadBlockAndSetSkipped_function_end-NvM_ActFinishReadBlockAndSetSkipped
.L255:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActFinishWriteBlock',code,cluster('NvM_ActFinishWriteBlock')
	.sect	'.text.NvM_Act.NvM_ActFinishWriteBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1252  
; ..\eeprom\NvM\NvM_Act.c	  1253  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1254  *  NvM_ActFinishWriteBlock
; ..\eeprom\NvM\NvM_Act.c	  1255  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1256  /*!
; ..\eeprom\NvM\NvM_Act.c	  1257   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1258   *
; ..\eeprom\NvM\NvM_Act.c	  1259   *
; ..\eeprom\NvM\NvM_Act.c	  1260   *
; ..\eeprom\NvM\NvM_Act.c	  1261   *
; ..\eeprom\NvM\NvM_Act.c	  1262   *
; ..\eeprom\NvM\NvM_Act.c	  1263   *
; ..\eeprom\NvM\NvM_Act.c	  1264   *
; ..\eeprom\NvM\NvM_Act.c	  1265   *
; ..\eeprom\NvM\NvM_Act.c	  1266   *
; ..\eeprom\NvM\NvM_Act.c	  1267   *
; ..\eeprom\NvM\NvM_Act.c	  1268   *
; ..\eeprom\NvM\NvM_Act.c	  1269   *
; ..\eeprom\NvM\NvM_Act.c	  1270   *
; ..\eeprom\NvM\NvM_Act.c	  1271   *
; ..\eeprom\NvM\NvM_Act.c	  1272   *
; ..\eeprom\NvM\NvM_Act.c	  1273   */
; ..\eeprom\NvM\NvM_Act.c	  1274  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishWriteBlock(void)
; Function NvM_ActFinishWriteBlock
.L141:
NvM_ActFinishWriteBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1275  {
; ..\eeprom\NvM\NvM_Act.c	  1276      const NvM_RamMngmtPtrType MngmtPtr = NvM_CurrentBlockInfo_t.Mngmt_pt;
	fcall	.cocofun_8
.L1061:
	ld.a	a15,[a13]4
.L715:

; ..\eeprom\NvM\NvM_Act.c	  1277  
; ..\eeprom\NvM\NvM_Act.c	  1278      NvM_ActUpdateNvState();
	call	NvM_ActUpdateNvState
.L1062:

; ..\eeprom\NvM\NvM_Act.c	  1279  
; ..\eeprom\NvM\NvM_Act.c	  1280      if((NvM_CurrentBlockInfo_t.Descriptor_pt->MngmtType_t & NVM_BLOCK_REDUNDANT) != 0u)
	ld.a	a2,[a13]
.L635:

; ..\eeprom\NvM\NvM_Act.c	  1281      {
; ..\eeprom\NvM\NvM_Act.c	  1282          boolean firstBlockDefect = (boolean)(NVM_EXTRACT_NVSTATE_PRI(NvM_CurrentBlockInfo_t.NvState_u8) != NVM_NVBLOCK_STATE_UPTODATE);
; ..\eeprom\NvM\NvM_Act.c	  1283          boolean secondBlockDefect = (boolean)(NVM_EXTRACT_NVSTATE_SEC(NvM_CurrentBlockInfo_t.NvState_u8) != NVM_NVBLOCK_STATE_UPTODATE);
; ..\eeprom\NvM\NvM_Act.c	  1284  
; ..\eeprom\NvM\NvM_Act.c	  1285          /* both blocks are defect, request failed, DEM error will be reported later */
; ..\eeprom\NvM\NvM_Act.c	  1286          if(firstBlockDefect && secondBlockDefect)
; ..\eeprom\NvM\NvM_Act.c	  1287          {
; ..\eeprom\NvM\NvM_Act.c	  1288              NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
	lea	a12,[a13]40
.L636:
	ld.bu	d15,[a2]58
	extr.u	d15,d15,#4,#2
.L1063:
	jz.t	d15:0,.L23
.L637:
	ld.bu	d0,[a13]43
	sha	d15,d0,#-5
	and	d15,#3
.L1064:
	ne	d1,d15,#0
.L716:
	sha	d0,#-3
	and	d15,d0,#3
.L1065:
	ne	d15,d15,#0
.L717:
	jeq	d1,#0,.L24
.L1066:
	jeq	d15,#0,.L25
.L1067:
	mov	d0,#1
	j	.L26
.L25:
.L24:

; ..\eeprom\NvM\NvM_Act.c	  1289          }
; ..\eeprom\NvM\NvM_Act.c	  1290          /* at least one block is OK */
; ..\eeprom\NvM\NvM_Act.c	  1291          else
; ..\eeprom\NvM\NvM_Act.c	  1292          {
; ..\eeprom\NvM\NvM_Act.c	  1293              NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_OK;
	mov	d0,#0
.L26:

; ..\eeprom\NvM\NvM_Act.c	  1294              /* one block is OK, other isn't -> block isn't redundantly stored within NV RAM */
; ..\eeprom\NvM\NvM_Act.c	  1295              if(firstBlockDefect != secondBlockDefect)
; ..\eeprom\NvM\NvM_Act.c	  1296              {
; ..\eeprom\NvM\NvM_Act.c	  1297                  NvM_DemReportErrorLossOfRedundancy();
; ..\eeprom\NvM\NvM_Act.c	  1298              }
; ..\eeprom\NvM\NvM_Act.c	  1299          }
; ..\eeprom\NvM\NvM_Act.c	  1300          /* one or both blocks defect -> use negative result to invalidate CRCCompareMechanism data, otherwise update */
; ..\eeprom\NvM\NvM_Act.c	  1301          NvM_IntUpdateCurrentBlockCRCCompareData((firstBlockDefect || secondBlockDefect) ? NVM_REQ_NOT_OK : NVM_REQ_OK);
	st.b	[a12],d0
	jne	d1,#0,.L27
.L1068:
	jeq	d15,#0,.L28
.L27:
	mov	d4,#1
	j	.L30
.L28:
	mov	d4,#0
	j	.L30
.L23:

; ..\eeprom\NvM\NvM_Act.c	  1302      }
; ..\eeprom\NvM\NvM_Act.c	  1303      else
; ..\eeprom\NvM\NvM_Act.c	  1304      {
; ..\eeprom\NvM\NvM_Act.c	  1305          NvM_IntUpdateCurrentBlockCRCCompareData(NvM_CurrentBlockInfo_t.LastResult_t);
	ld.bu	d4,[a12]
.L30:
	call	NvM_IntUpdateCurrentBlockCRCCompareData
.L1069:

; ..\eeprom\NvM\NvM_Act.c	  1306      }
; ..\eeprom\NvM\NvM_Act.c	  1307  
; ..\eeprom\NvM\NvM_Act.c	  1308      if(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_OK)
	ld.bu	d15,[a12]
.L1070:
	jne	d15,#0,.L31
.L1071:

; ..\eeprom\NvM\NvM_Act.c	  1309      {
; ..\eeprom\NvM\NvM_Act.c	  1310          /* mark permanent(!) Block as UNCHANGED */
; ..\eeprom\NvM\NvM_Act.c	  1311          if(NvM_CurrentJob_t.RamAddr_t == NULL_PTR) /* COV_NVM_APICFGCLASS */
	movh.a	a2,#@his(NvM_CurrentJob_t+4)
.L1072:
	ld.w	d15,[a2]@los(NvM_CurrentJob_t+4)
.L1073:
	jne	d15,#0,.L32
.L1074:

; ..\eeprom\NvM\NvM_Act.c	  1312          {
; ..\eeprom\NvM\NvM_Act.c	  1313              MngmtPtr->NvRamAttributes_u8 &= NVM_STATE_CHANGED_CL; /* SBSW_NvM_Access_CurrBlockInfo */
	ld.bu	d15,[a15]2
.L1075:
	and	d15,#253
	st.b	[a15]2,d15
.L32:

; ..\eeprom\NvM\NvM_Act.c	  1314          }
; ..\eeprom\NvM\NvM_Act.c	  1315  
; ..\eeprom\NvM\NvM_Act.c	  1316          if((NvM_CurrentBlockInfo_t.Descriptor_pt->Flags_u8 & NVM_BLOCK_WRITE_BLOCK_ONCE_ON) != 0u)
	ld.a	a2,[a13]
.L1076:
	ld.bu	d15,[a2]59
.L1077:
	jz.t	d15:2,.L33
.L1078:

; ..\eeprom\NvM\NvM_Act.c	  1317          {
; ..\eeprom\NvM\NvM_Act.c	  1318              MngmtPtr->NvRamAttributes_u8 |= NVM_WR_PROT_SET; /* SBSW_NvM_Access_CurrBlockInfo */
	ld.bu	d15,[a15]2
.L1079:
	or	d15,#128
	st.b	[a15]2,d15
.L33:

; ..\eeprom\NvM\NvM_Act.c	  1319          }
; ..\eeprom\NvM\NvM_Act.c	  1320      }
; ..\eeprom\NvM\NvM_Act.c	  1321      else
; ..\eeprom\NvM\NvM_Act.c	  1322      {   /* remember error condition to deliver the correct result for ReadAll/WriteAll */
; ..\eeprom\NvM\NvM_Act.c	  1323          NvM_CurrentBlockInfo_t.InternalFlags_u8 |= NVM_INTFLAG_ERROR_SET;
; ..\eeprom\NvM\NvM_Act.c	  1324          NvM_DemReportErrorReqFailed();
; ..\eeprom\NvM\NvM_Act.c	  1325      }
; ..\eeprom\NvM\NvM_Act.c	  1326  }
	ret
.L31:
	ld.bu	d15,[a13]42
.L1080:
	or	d15,#16
	st.b	[a13]42,d15
.L1081:
	ret
.L631:
	
__NvM_ActFinishWriteBlock_function_end:
	.size	NvM_ActFinishWriteBlock,__NvM_ActFinishWriteBlock_function_end-NvM_ActFinishWriteBlock
.L355:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActInitBlock',code,cluster('NvM_ActInitBlock')
	.sect	'.text.NvM_Act.NvM_ActInitBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1327  
; ..\eeprom\NvM\NvM_Act.c	  1328  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1329  *  NvM_ActInitBlock
; ..\eeprom\NvM\NvM_Act.c	  1330  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1331  /*!
; ..\eeprom\NvM\NvM_Act.c	  1332   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1333   *
; ..\eeprom\NvM\NvM_Act.c	  1334   *
; ..\eeprom\NvM\NvM_Act.c	  1335   *
; ..\eeprom\NvM\NvM_Act.c	  1336   *
; ..\eeprom\NvM\NvM_Act.c	  1337   *
; ..\eeprom\NvM\NvM_Act.c	  1338   *
; ..\eeprom\NvM\NvM_Act.c	  1339   *
; ..\eeprom\NvM\NvM_Act.c	  1340   *
; ..\eeprom\NvM\NvM_Act.c	  1341   *
; ..\eeprom\NvM\NvM_Act.c	  1342   *
; ..\eeprom\NvM\NvM_Act.c	  1343   *
; ..\eeprom\NvM\NvM_Act.c	  1344   *
; ..\eeprom\NvM\NvM_Act.c	  1345   *
; ..\eeprom\NvM\NvM_Act.c	  1346   *
; ..\eeprom\NvM\NvM_Act.c	  1347   */
; ..\eeprom\NvM\NvM_Act.c	  1348  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitBlock(void)
; Function NvM_ActInitBlock
.L143:
NvM_ActInitBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1349  {
; ..\eeprom\NvM\NvM_Act.c	  1350      /* Just to get a shorter notation ... */
; ..\eeprom\NvM\NvM_Act.c	  1351      const NvM_BlockIdType orgBlockId = NVM_BLOCK_FROM_DCM_ID(NvM_CurrentJob_t.JobBlockId_t);
	movh.a	a15,#@his(NvM_CurrentJob_t)
	lea	a15,[a15]@los(NvM_CurrentJob_t)
	ld.hu	d0,[a15]0
.L822:

; ..\eeprom\NvM\NvM_Act.c	  1352      const NvM_BlockDescrPtrType DescrPtr = &NvM_BlockDescriptorTable_at[orgBlockId];
	movh.a	a2,#@his(NvM_BlockDescriptorTable_at)
.L823:
	insert	d15,d0,#0,#15,#17
	lea	a2,[a2]@los(NvM_BlockDescriptorTable_at)
.L718:
	sha	d1,d15,#6
.L824:
	addsc.a	a5,a2,d1,#0
.L720:

; ..\eeprom\NvM\NvM_Act.c	  1353  
; ..\eeprom\NvM\NvM_Act.c	  1354      NvM_CurrentBlockInfo_t.Mngmt_pt =
; ..\eeprom\NvM\NvM_Act.c	  1355          ((NvM_CurrentJob_t.JobBlockId_t == orgBlockId) ? (&NvM_BlockMngmtArea_at[orgBlockId]) : (&NvM_DcmBlockMngmt_t)); /* COV_NVM_APICFGCLASS */
	jne	d15,d0,.L35
.L825:
	movh.a	a2,#@his(NvM_BlockMngmtArea_at)
	lea	a2,[a2]@los(NvM_BlockMngmtArea_at)
.L826:
	addsc.a	a2,a2,d15,#2
.L827:
	j	.L36
.L35:
	movh.a	a2,#@his(NvM_DcmBlockMngmt_t)
	lea	a2,[a2]@los(NvM_DcmBlockMngmt_t)
.L36:
	movh.a	a6,#@his(NvM_CurrentBlockInfo_t)
	lea	a6,[a6]@los(NvM_CurrentBlockInfo_t)
.L828:
	mov.aa	a4,a6
.L829:
	st.a	[+a4]4,a2
.L830:

; ..\eeprom\NvM\NvM_Act.c	  1356      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_OK;
	mov	d15,#0
	st.b	[a6]40,d15
.L719:

; ..\eeprom\NvM\NvM_Act.c	  1357      NvM_CurrentBlockInfo_t.Descriptor_pt = DescrPtr;
	st.a	[a6],a5
.L831:

; ..\eeprom\NvM\NvM_Act.c	  1358      NvM_CurrentBlockInfo_t.NvIdentifier_u16 = DescrPtr->NvIdentifier_u16;
	lea	a2,[a6]36
.L832:
	ld.hu	d15,[a5]48
.L833:
	st.h	[a2],d15
.L834:

; ..\eeprom\NvM\NvM_Act.c	  1359      /* For blocks of MngmtType DATASET or DATASET_ROM the DataIndex has to be added to NvIdentifier. */
; ..\eeprom\NvM\NvM_Act.c	  1360      if((DescrPtr->MngmtType_t & NVM_BLOCK_DATASET) != 0u)
	ld.bu	d15,[a5]58
	extr.u	d15,d15,#4,#2
.L835:
	jz.t	d15:1,.L37
.L836:

; ..\eeprom\NvM\NvM_Act.c	  1361      {
; ..\eeprom\NvM\NvM_Act.c	  1362          NvM_CurrentBlockInfo_t.NvIdentifier_u16 |= NvM_CurrentBlockInfo_t.Mngmt_pt->NvDataIndex_t;
	ld.a	a4,[a4]
.L837:
	ld.hu	d15,[a6]36
.L838:
	ld.bu	d0,[a4]
.L839:
	or	d15,d0
	st.h	[a2],d15
.L37:

; ..\eeprom\NvM\NvM_Act.c	  1363      }
; ..\eeprom\NvM\NvM_Act.c	  1364      NvM_CurrentBlockInfo_t.ByteCount_u16 = 0u;
	mov	d15,#0
	st.h	[a6]38,d15
.L840:

; ..\eeprom\NvM\NvM_Act.c	  1365  
; ..\eeprom\NvM\NvM_Act.c	  1366  #if(NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1367      /* Initially, we assume the internal buffer to be used. */
; ..\eeprom\NvM\NvM_Act.c	  1368      NvM_CurrentBlockInfo_t.RamAddr_t = NvM_InternalBuffer_au8;
	lea	a2,[a6]8
.L841:
	movh.a	a4,#@his(NvM_InternalBuffer_au8)
	lea	a4,[a4]@los(NvM_InternalBuffer_au8)
.L842:
	st.a	[a2],a4
.L843:

; ..\eeprom\NvM\NvM_Act.c	  1369      /* Only without CRC we have to decide whether to directly read to or write from RAM Block. */
; ..\eeprom\NvM\NvM_Act.c	  1370      if(DescrPtr->CrcSettings == NVM_BLOCK_USE_CRC_OFF)
	ld.bu	d15,[a5]58
	extr.u	d15,d15,#6,#2
.L844:
	jne	d15,#0,.L38
.L845:

; ..\eeprom\NvM\NvM_Act.c	  1371      {
; ..\eeprom\NvM\NvM_Act.c	  1372          if(NvM_CurrentJob_t.RamAddr_t != NULL_PTR) /* COV_NVM_APICFGCLASS */
	ld.a	a4,[a15]4
.L846:
	jz.a	a4,.L39
.L847:

; ..\eeprom\NvM\NvM_Act.c	  1373          {
; ..\eeprom\NvM\NvM_Act.c	  1374              NvM_CurrentBlockInfo_t.RamAddr_t = NvM_CurrentJob_t.RamAddr_t;
	st.a	[a2],a4
.L848:
	j	.L40
.L39:

; ..\eeprom\NvM\NvM_Act.c	  1375          }
; ..\eeprom\NvM\NvM_Act.c	  1376          /* Do not have to check both callbacks - it is only allowed to configure one or even both. */
; ..\eeprom\NvM\NvM_Act.c	  1377          else if(DescrPtr->CbkGetMirrorFunc_pt == NULL_PTR)
	ld.w	d15,[a5]24
.L849:
	jne	d15,#0,.L41
.L850:

; ..\eeprom\NvM\NvM_Act.c	  1378          {
; ..\eeprom\NvM\NvM_Act.c	  1379              NvM_CurrentBlockInfo_t.RamAddr_t = DescrPtr->RamBlockDataAddr_t;
	ld.a	a15,[a5]
.L851:
	st.a	[a2],a15
.L41:
.L40:
.L38:

; ..\eeprom\NvM\NvM_Act.c	  1380          }
; ..\eeprom\NvM\NvM_Act.c	  1381          else /* Explicit synch mechanism (not overriden by passing a pointer along with the request): */
; ..\eeprom\NvM\NvM_Act.c	  1382          {
; ..\eeprom\NvM\NvM_Act.c	  1383              /* => use internal buffer -> already assigned. */
; ..\eeprom\NvM\NvM_Act.c	  1384          }
; ..\eeprom\NvM\NvM_Act.c	  1385      }
; ..\eeprom\NvM\NvM_Act.c	  1386  #else
; ..\eeprom\NvM\NvM_Act.c	  1387      NvM_CurrentBlockInfo_t.RamAddr_t =
; ..\eeprom\NvM\NvM_Act.c	  1388          (NvM_CurrentJob_t.RamAddr_t != NULL_PTR) ? NvM_CurrentJob_t.RamAddr_t : DescrPtr->RamBlockDataAddr_t;
; ..\eeprom\NvM\NvM_Act.c	  1389  #endif
; ..\eeprom\NvM\NvM_Act.c	  1390  
; ..\eeprom\NvM\NvM_Act.c	  1391  #if(NVM_USE_CSM == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1392      if(NvM_QryIsCipherBlock(NvM_CurrentBlockInfo_t.Descriptor_pt) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  1393      {
; ..\eeprom\NvM\NvM_Act.c	  1394          NvM_CurrentBlockInfo_t.NvRamAddr_t = NvM_CipheredDataBuffer;
; ..\eeprom\NvM\NvM_Act.c	  1395      }
; ..\eeprom\NvM\NvM_Act.c	  1396      else
; ..\eeprom\NvM\NvM_Act.c	  1397  #endif
; ..\eeprom\NvM\NvM_Act.c	  1398      {
; ..\eeprom\NvM\NvM_Act.c	  1399          NvM_CurrentBlockInfo_t.NvRamAddr_t = NvM_CurrentBlockInfo_t.RamAddr_t;
	ld.a	a15,[a2]
.L852:
	st.a	[a6]12,a15
.L853:

; ..\eeprom\NvM\NvM_Act.c	  1400      }
; ..\eeprom\NvM\NvM_Act.c	  1401  
; ..\eeprom\NvM\NvM_Act.c	  1402      if((DescrPtr->MngmtType_t & NVM_BLOCK_REDUNDANT) != 0u)
	ld.bu	d15,[a5]58
	extr.u	d15,d15,#4,#2
.L854:
	jz.t	d15:0,.L42
.L855:

; ..\eeprom\NvM\NvM_Act.c	  1403      {    
; ..\eeprom\NvM\NvM_Act.c	  1404          NvM_CurrentBlockInfo_t.NvState_u8 = NVM_NVBLOCK_STATE_PRI_ACTIVE;
; ..\eeprom\NvM\NvM_Act.c	  1405          NvM_IntCreateNvState(&NvM_CurrentBlockInfo_t.NvState_u8, NVM_NVBLOCK_STATE_UNKNOWN); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	mov	d4,#3
	lea	a4,[a6]43
.L856:
	mov	d15,#127
	st.b	[a4],d15
.L857:
	j	NvM_IntCreateNvState
.L42:

; ..\eeprom\NvM\NvM_Act.c	  1406      }
; ..\eeprom\NvM\NvM_Act.c	  1407  }
	ret
.L597:
	
__NvM_ActInitBlock_function_end:
	.size	NvM_ActInitBlock,__NvM_ActInitBlock_function_end-NvM_ActInitBlock
.L280:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActInitMainFsm',code,cluster('NvM_ActInitMainFsm')
	.sect	'.text.NvM_Act.NvM_ActInitMainFsm'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1408  
; ..\eeprom\NvM\NvM_Act.c	  1409  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1410  *  NvM_ActInitMainFsm
; ..\eeprom\NvM\NvM_Act.c	  1411  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1412  /*!
; ..\eeprom\NvM\NvM_Act.c	  1413   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1414   *
; ..\eeprom\NvM\NvM_Act.c	  1415   *
; ..\eeprom\NvM\NvM_Act.c	  1416   *
; ..\eeprom\NvM\NvM_Act.c	  1417   *
; ..\eeprom\NvM\NvM_Act.c	  1418   */
; ..\eeprom\NvM\NvM_Act.c	  1419  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitMainFsm(void)
; Function NvM_ActInitMainFsm
.L145:
NvM_ActInitMainFsm:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1420  {
; ..\eeprom\NvM\NvM_Act.c	  1421      NvM_ActInitBlock();
	call	NvM_ActInitBlock
.L807:

; ..\eeprom\NvM\NvM_Act.c	  1422      NvM_JobMainState_t = NvM_IntServiceDescrTable_at[NvM_CurrentJob_t.JobServiceId_t].InitialState_t;
	movh.a	a15,#@his(NvM_CurrentJob_t+2)
.L808:
	ld.bu	d15,[a15]@los(NvM_CurrentJob_t+2)
.L809:
	fcall	.cocofun_7
.L810:
	movh.a	a2,#@his(NvM_JobMainState_t)
.L811:
	ld.bu	d15,[a15]1
.L812:
	st.b	[a2]@los(NvM_JobMainState_t),d15
.L813:

; ..\eeprom\NvM\NvM_Act.c	  1423      /* NvM uses only the JobMainState at this point, the JobSubState remains unused -> FSM_FINISHED.
; ..\eeprom\NvM\NvM_Act.c	  1424       * Normally the JobSubState is already set correctly, but e.g. in case of a WriteAll cancel via an immediate
; ..\eeprom\NvM\NvM_Act.c	  1425       * priority block, the JobSubState remains in a real state and processes the states - increases runtime and may
; ..\eeprom\NvM\NvM_Act.c	  1426       * lead to problems - ensure the JobSubState does nothing! */
; ..\eeprom\NvM\NvM_Act.c	  1427      NvM_JobSubState_t = NVM_STATE_FSM_FINISHED;
	movh.a	a2,#@his(NvM_JobSubState_t)
.L814:
	mov	d15,#32
	st.b	[a2]@los(NvM_JobSubState_t),d15
.L815:

; ..\eeprom\NvM\NvM_Act.c	  1428      NvM_ActionTable_ap[NvM_IntServiceDescrTable_at[NvM_CurrentJob_t.JobServiceId_t].InitialActionId](); /* SBSW_NvM_FuncPtrCall_QueryAction */
	ld.bu	d15,[a15]
.L816:
	fcall	.cocofun_2
.L817:
	ji	a15
.L595:
	
__NvM_ActInitMainFsm_function_end:
	.size	NvM_ActInitMainFsm,__NvM_ActInitMainFsm_function_end-NvM_ActInitMainFsm
.L275:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_2',code,cluster('.cocofun_2')
	.sect	'.text.NvM_Act..cocofun_2'
	.align	2
; Function .cocofun_2
.L147:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	movh.a	a15,#@his(NvM_ActionTable_ap)
	lea	a15,[a15]@los(NvM_ActionTable_ap)
.L1413:
	addsc.a	a15,a15,d15,#2
	ld.a	a15,[a15]
.L1414:
	fret
.L515:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActInitReadAll',code,cluster('NvM_ActInitReadAll')
	.sect	'.text.NvM_Act.NvM_ActInitReadAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1429  }
; ..\eeprom\NvM\NvM_Act.c	  1430  
; ..\eeprom\NvM\NvM_Act.c	  1431  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1432  *  NvM_ActInitReadAll
; ..\eeprom\NvM\NvM_Act.c	  1433  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1434  /*!
; ..\eeprom\NvM\NvM_Act.c	  1435   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1436   *
; ..\eeprom\NvM\NvM_Act.c	  1437   *
; ..\eeprom\NvM\NvM_Act.c	  1438   *
; ..\eeprom\NvM\NvM_Act.c	  1439   *
; ..\eeprom\NvM\NvM_Act.c	  1440   *
; ..\eeprom\NvM\NvM_Act.c	  1441   *
; ..\eeprom\NvM\NvM_Act.c	  1442   */
; ..\eeprom\NvM\NvM_Act.c	  1443  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitReadAll(void)
; Function NvM_ActInitReadAll
.L149:
NvM_ActInitReadAll:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1444  {
; ..\eeprom\NvM\NvM_Act.c	  1445      NvM_BlockIdType currBlockId = NvM_NoOfBlockIds_t;
	movh.a	a15,#@his(NvM_NoOfBlockIds_t)
	ld.hu	d15,[a15]@los(NvM_NoOfBlockIds_t)
.L721:

; ..\eeprom\NvM\NvM_Act.c	  1446  
; ..\eeprom\NvM\NvM_Act.c	  1447      /* clear Error and Dynamic Mismatch flags */
; ..\eeprom\NvM\NvM_Act.c	  1448      NvM_CurrentBlockInfo_t.InternalFlags_u8 &= NVM_INTFLAG_ERROR_CL & NVM_INTFLAG_DYN_MISMATCH_CL;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+42)
.L862:
	ld.bu	d0,[a15]@los(NvM_CurrentBlockInfo_t+42)
.L863:
	mov	d8,#2
.L864:
	and	d0,d0,#207
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+42),d0
.L865:

; ..\eeprom\NvM\NvM_Act.c	  1449  
; ..\eeprom\NvM\NvM_Act.c	  1450      do
; ..\eeprom\NvM\NvM_Act.c	  1451      {
; ..\eeprom\NvM\NvM_Act.c	  1452          currBlockId--;
; ..\eeprom\NvM\NvM_Act.c	  1453          NvM_BlockMngmtArea_at[currBlockId].NvRamErrorStatus_u8 = NVM_REQ_PENDING; /* SBSW_NvM_AccessBlockManagementArea */
	movh.a	a15,#@his(NvM_BlockMngmtArea_at)
	lea	a15,[a15]@los(NvM_BlockMngmtArea_at)
.L866:
	addsc.a	a15,a15,d15,#2

; ..\eeprom\NvM\NvM_Act.c	  1454          NvM_BlockNotification(currBlockId, NVM_READ_ALL, NVM_REQ_PENDING);
; ..\eeprom\NvM\NvM_Act.c	  1455          
; ..\eeprom\NvM\NvM_Act.c	  1456      } while(currBlockId > 0u); /* blockId 0 does not need to be set to PENDING, already done */
.L43:
	add	d15,#-1
	add.a	a15,#-4
.L722:
	extr.u	d15,d15,#0,#16
	st.b	[a15]1,d8
.L723:
	mov	d6,d8
.L867:
	mov	d5,#12
.L868:
	mov	d4,d15
	call	NvM_BlockNotification
.L869:
	jne	d15,#0,.L43
.L870:

; ..\eeprom\NvM\NvM_Act.c	  1457  
; ..\eeprom\NvM\NvM_Act.c	  1458      NvM_ActSetInitialAttr();
	call	NvM_ActSetInitialAttr
.L871:

; ..\eeprom\NvM\NvM_Act.c	  1459  
; ..\eeprom\NvM\NvM_Act.c	  1460  #if(NVM_DRV_MODE_SWITCH == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1461      MemIf_SetMode(MEMIF_MODE_FAST);
	mov	d4,#1
	call	MemIf_SetMode
.L872:

; ..\eeprom\NvM\NvM_Act.c	  1462  #endif
; ..\eeprom\NvM\NvM_Act.c	  1463      /* #50 start reading the configuration block */
; ..\eeprom\NvM\NvM_Act.c	  1464      NvM_ActInitReadBlockSubFsm();
	j	NvM_ActInitReadBlockSubFsm
.L602:
	
__NvM_ActInitReadAll_function_end:
	.size	NvM_ActInitReadAll,__NvM_ActInitReadAll_function_end-NvM_ActInitReadAll
.L285:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActFinishCfgIdCheck',code,cluster('NvM_ActFinishCfgIdCheck')
	.sect	'.text.NvM_Act.NvM_ActFinishCfgIdCheck'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1465  }
; ..\eeprom\NvM\NvM_Act.c	  1466  
; ..\eeprom\NvM\NvM_Act.c	  1467  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1468  *  NvM_ActFinishCfgIdCheck
; ..\eeprom\NvM\NvM_Act.c	  1469  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1470  /*!
; ..\eeprom\NvM\NvM_Act.c	  1471   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1472   *
; ..\eeprom\NvM\NvM_Act.c	  1473   *
; ..\eeprom\NvM\NvM_Act.c	  1474   *
; ..\eeprom\NvM\NvM_Act.c	  1475   *
; ..\eeprom\NvM\NvM_Act.c	  1476   *
; ..\eeprom\NvM\NvM_Act.c	  1477   *
; ..\eeprom\NvM\NvM_Act.c	  1478   *
; ..\eeprom\NvM\NvM_Act.c	  1479   *
; ..\eeprom\NvM\NvM_Act.c	  1480   *
; ..\eeprom\NvM\NvM_Act.c	  1481   */
; ..\eeprom\NvM\NvM_Act.c	  1482  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishCfgIdCheck(void)
; Function NvM_ActFinishCfgIdCheck
.L151:
NvM_ActFinishCfgIdCheck:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1483  {
; ..\eeprom\NvM\NvM_Act.c	  1484      /* clear Config ID Mismatch Flag */
; ..\eeprom\NvM\NvM_Act.c	  1485      NvM_CurrentBlockInfo_t.InternalFlags_u8 &= NVM_INTFLAG_DYN_MISMATCH_CL;
	fcall	.cocofun_14
.L1015:
	lea	a2,[a15]42
	ld.bu	d15,[a2]
.L1016:
	and	d15,#223
	st.b	[a2],d15
.L1017:

; ..\eeprom\NvM\NvM_Act.c	  1486  
; ..\eeprom\NvM\NvM_Act.c	  1487      if(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_OK)
	lea	a4,[a15]40
	ld.bu	d15,[a4]
.L1018:
	jne	d15,#0,.L44
.L1019:

; ..\eeprom\NvM\NvM_Act.c	  1488      {
; ..\eeprom\NvM\NvM_Act.c	  1489          if((NvM_CompiledConfigId_t.Bytes_au8[0] != NvM_CurrentBlockInfo_t.RamAddr_t[0]) ||
	ld.a	a15,[a15]8
.L1020:
	movh.a	a5,#@his(NvM_CompiledConfigId_t)
	lea	a5,[a5]@los(NvM_CompiledConfigId_t)
.L1021:
	ld.bu	d15,[a5]
.L1022:
	ld.bu	d0,[a15]
.L1023:
	jne	d15,d0,.L45
.L1024:

; ..\eeprom\NvM\NvM_Act.c	  1490             (NvM_CompiledConfigId_t.Bytes_au8[1] != NvM_CurrentBlockInfo_t.RamAddr_t[1]))
	ld.bu	d15,[a5]1
.L1025:
	ld.bu	d0,[a15]1
.L1026:
	jeq	d15,d0,.L46
.L45:

; ..\eeprom\NvM\NvM_Act.c	  1491          {
; ..\eeprom\NvM\NvM_Act.c	  1492              NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
	mov	d15,#1
	j	.L47
.L46:

; ..\eeprom\NvM\NvM_Act.c	  1493  
; ..\eeprom\NvM\NvM_Act.c	  1494  #if (NVM_DYNAMIC_CONFIGURATION == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1495              NvM_CurrentBlockInfo_t.InternalFlags_u8 |= NVM_INTFLAG_DYN_MISMATCH_SET;
; ..\eeprom\NvM\NvM_Act.c	  1496  
; ..\eeprom\NvM\NvM_Act.c	  1497              NvM_UpdateConfigIdBlock();
; ..\eeprom\NvM\NvM_Act.c	  1498  #endif
; ..\eeprom\NvM\NvM_Act.c	  1499          }
; ..\eeprom\NvM\NvM_Act.c	  1500      }
; ..\eeprom\NvM\NvM_Act.c	  1501      else if(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_NV_INVALIDATED)
; ..\eeprom\NvM\NvM_Act.c	  1502      {
; ..\eeprom\NvM\NvM_Act.c	  1503          NvM_UpdateConfigIdBlock();
; ..\eeprom\NvM\NvM_Act.c	  1504      }
; ..\eeprom\NvM\NvM_Act.c	  1505      else /* any other negative result */
; ..\eeprom\NvM\NvM_Act.c	  1506      {
; ..\eeprom\NvM\NvM_Act.c	  1507          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_INTEGRITY_FAILED;
; ..\eeprom\NvM\NvM_Act.c	  1508  #if (NVM_DYNAMIC_CONFIGURATION == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1509          NvM_CurrentBlockInfo_t.InternalFlags_u8 |= NVM_INTFLAG_DYN_MISMATCH_SET;
; ..\eeprom\NvM\NvM_Act.c	  1510  
; ..\eeprom\NvM\NvM_Act.c	  1511          NvM_UpdateConfigIdBlock();
; ..\eeprom\NvM\NvM_Act.c	  1512  #endif
; ..\eeprom\NvM\NvM_Act.c	  1513      }
; ..\eeprom\NvM\NvM_Act.c	  1514  }
	ret
.L44:
	jeq	d15,#5,.L48
.L1027:
	mov	d15,#3
.L47:
	st.b	[a4],d15
.L1028:
	ld.bu	d15,[a2]
.L1029:
	or	d15,#32
	st.b	[a2],d15
.L48:
	j	NvM_UpdateConfigIdBlock
.L624:
	
__NvM_ActFinishCfgIdCheck_function_end:
	.size	NvM_ActFinishCfgIdCheck,__NvM_ActFinishCfgIdCheck_function_end-NvM_ActFinishCfgIdCheck
.L345:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActInitWriteAll',code,cluster('NvM_ActInitWriteAll')
	.sect	'.text.NvM_Act.NvM_ActInitWriteAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1515  
; ..\eeprom\NvM\NvM_Act.c	  1516  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1517  *  NvM_ActInitWriteAll
; ..\eeprom\NvM\NvM_Act.c	  1518  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1519  /*!
; ..\eeprom\NvM\NvM_Act.c	  1520   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1521   *
; ..\eeprom\NvM\NvM_Act.c	  1522   *
; ..\eeprom\NvM\NvM_Act.c	  1523   *
; ..\eeprom\NvM\NvM_Act.c	  1524   */
; ..\eeprom\NvM\NvM_Act.c	  1525  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitWriteAll(void)
; Function NvM_ActInitWriteAll
.L153:
NvM_ActInitWriteAll:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1526  {
; ..\eeprom\NvM\NvM_Act.c	  1527      /* clear Error flag */
; ..\eeprom\NvM\NvM_Act.c	  1528      NvM_CurrentBlockInfo_t.InternalFlags_u8 &= NVM_INTFLAG_ERROR_CL;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+42)
.L895:

; ..\eeprom\NvM\NvM_Act.c	  1529  
; ..\eeprom\NvM\NvM_Act.c	  1530  #if(NVM_DRV_MODE_SWITCH == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1531      MemIf_SetMode(MEMIF_MODE_FAST);
	mov	d4,#1
	ld.bu	d15,[a15]@los(NvM_CurrentBlockInfo_t+42)
.L896:
	and	d15,#239
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+42),d15
.L897:
	j	MemIf_SetMode
.L607:
	
__NvM_ActInitWriteAll_function_end:
	.size	NvM_ActInitWriteAll,__NvM_ActInitWriteAll_function_end-NvM_ActInitWriteAll
.L300:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActInitWriteBlock',code,cluster('NvM_ActInitWriteBlock')
	.sect	'.text.NvM_Act.NvM_ActInitWriteBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1532  #endif
; ..\eeprom\NvM\NvM_Act.c	  1533  }
; ..\eeprom\NvM\NvM_Act.c	  1534  
; ..\eeprom\NvM\NvM_Act.c	  1535  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1536  *  NvM_ActInitWriteBlock
; ..\eeprom\NvM\NvM_Act.c	  1537  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1538  /*!
; ..\eeprom\NvM\NvM_Act.c	  1539   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1540   *
; ..\eeprom\NvM\NvM_Act.c	  1541   *
; ..\eeprom\NvM\NvM_Act.c	  1542   *
; ..\eeprom\NvM\NvM_Act.c	  1543   *
; ..\eeprom\NvM\NvM_Act.c	  1544   *
; ..\eeprom\NvM\NvM_Act.c	  1545   *
; ..\eeprom\NvM\NvM_Act.c	  1546   */
; ..\eeprom\NvM\NvM_Act.c	  1547  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitWriteBlock(void)
; Function NvM_ActInitWriteBlock
.L155:
NvM_ActInitWriteBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1548  {
; ..\eeprom\NvM\NvM_Act.c	  1549      NvM_CurrentBlockInfo_t.WriteRetryCounter_u8 = 0u;
	fcall	.cocofun_11
.L902:

; ..\eeprom\NvM\NvM_Act.c	  1550  #if (NVM_USE_CSM == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1551      NvM_CurrentBlockInfo_t.CsmJobRetryCounter_u8 = 0u;
; ..\eeprom\NvM\NvM_Act.c	  1552  #endif
; ..\eeprom\NvM\NvM_Act.c	  1553  
; ..\eeprom\NvM\NvM_Act.c	  1554      if((NvM_CurrentBlockInfo_t.Descriptor_pt->MngmtType_t & NVM_BLOCK_REDUNDANT) != 0u)
	ld.a	a2,[a15]
.L903:
	ld.bu	d15,[a2]58
	extr.u	d15,d15,#4,#2
.L904:
	jz.t	d15:0,.L49
.L905:

; ..\eeprom\NvM\NvM_Act.c	  1555      {
; ..\eeprom\NvM\NvM_Act.c	  1556          NvM_ActTestBlockBlank();
	call	NvM_ActTestBlockBlank
.L49:

; ..\eeprom\NvM\NvM_Act.c	  1557      }
; ..\eeprom\NvM\NvM_Act.c	  1558  
; ..\eeprom\NvM\NvM_Act.c	  1559  #if(NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1560      NvM_CurrentBlockInfo_t.ByteCount_u16 = NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16;
	ld.a	a2,[a15]
.L906:
	ld.hu	d15,[a2]50
.L907:
	st.h	[a15]38,d15
.L908:

; ..\eeprom\NvM\NvM_Act.c	  1561  #endif
; ..\eeprom\NvM\NvM_Act.c	  1562      /* Byte Count may remain zero; and this is okay, since no copying will be necessary.
; ..\eeprom\NvM\NvM_Act.c	  1563       * In fact NvM_ActCopyNvDataToBuf won't even use it... */
; ..\eeprom\NvM\NvM_Act.c	  1564      NvM_CrcJob_Create(&NvM_CurrentBlockInfo_t.BlockCrcJob_t,
	fcall	.cocofun_12
.L909:

; ..\eeprom\NvM\NvM_Act.c	  1565                        NvM_CurrentJob_t.JobBlockId_t,
; ..\eeprom\NvM\NvM_Act.c	  1566                        NvM_CurrentBlockInfo_t.NvRamAddr_t,
; ..\eeprom\NvM\NvM_Act.c	  1567                        NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockNVRAMDataLength); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	ld.a	a15,[a15]
.L910:
	ld.hu	d5,[a15]54
	call	NvM_CrcJob_Create
.L911:

; ..\eeprom\NvM\NvM_Act.c	  1568  
; ..\eeprom\NvM\NvM_Act.c	  1569      NvM_ActCopyNvDataToBuf();
	j	NvM_ActCopyNvDataToBuf
.L608:
	
__NvM_ActInitWriteBlock_function_end:
	.size	NvM_ActInitWriteBlock,__NvM_ActInitWriteBlock_function_end-NvM_ActInitWriteBlock
.L305:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_12',code,cluster('.cocofun_12')
	.sect	'.text.NvM_Act..cocofun_12'
	.align	2
; Function .cocofun_12
.L157:
.cocofun_12:	.type	func
; Function body .cocofun_12, coco_iter:0
	lea	a4,[a15]16
.L1475:
	movh.a	a2,#@his(NvM_CurrentJob_t)
.L1476:
	ld.hu	d4,[a2]@los(NvM_CurrentJob_t)
.L1477:
	ld.a	a5,[a15]12
.L1478:
	fret
.L565:
	; End of function
	.sdecl	'.text.NvM_Act..cocofun_11',code,cluster('.cocofun_11')
	.sect	'.text.NvM_Act..cocofun_11'
	.align	2
; Function .cocofun_11
.L159:
.cocofun_11:	.type	func
; Function body .cocofun_11, coco_iter:0
	fcall	.cocofun_14
.L1469:
	mov	d15,#0
	st.b	[a15]41,d15
.L1470:
	fret
.L560:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActFinishEraseBlock',code,cluster('NvM_ActFinishEraseBlock')
	.sect	'.text.NvM_Act.NvM_ActFinishEraseBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1570  }
; ..\eeprom\NvM\NvM_Act.c	  1571  
; ..\eeprom\NvM\NvM_Act.c	  1572  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM_Act.c	  1573  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1574  *  NvM_ActFinishEraseBlock
; ..\eeprom\NvM\NvM_Act.c	  1575  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1576  /*!
; ..\eeprom\NvM\NvM_Act.c	  1577   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1578   *
; ..\eeprom\NvM\NvM_Act.c	  1579   *
; ..\eeprom\NvM\NvM_Act.c	  1580   *
; ..\eeprom\NvM\NvM_Act.c	  1581   *
; ..\eeprom\NvM\NvM_Act.c	  1582   *
; ..\eeprom\NvM\NvM_Act.c	  1583   *
; ..\eeprom\NvM\NvM_Act.c	  1584   */
; ..\eeprom\NvM\NvM_Act.c	  1585  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishEraseBlock(void)
; Function NvM_ActFinishEraseBlock
.L161:
NvM_ActFinishEraseBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1586  {
; ..\eeprom\NvM\NvM_Act.c	  1587      if(NvM_CurrentBlockInfo_t.LastResult_t != NVM_REQ_OK)
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L1086:
	lea	a15,[a15]@los(NvM_CurrentBlockInfo_t+40)
	ld.bu	d15,[a15]
.L1087:
	jeq	d15,#0,.L50
.L1088:

; ..\eeprom\NvM\NvM_Act.c	  1588      {
; ..\eeprom\NvM\NvM_Act.c	  1589          NvM_DemReportErrorReqFailed();
; ..\eeprom\NvM\NvM_Act.c	  1590          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
	mov	d15,#1
	st.b	[a15],d15
.L50:

; ..\eeprom\NvM\NvM_Act.c	  1591      }
; ..\eeprom\NvM\NvM_Act.c	  1592  
; ..\eeprom\NvM\NvM_Act.c	  1593      NvM_IntUpdateCurrentBlockCRCCompareData(NVM_REQ_NOT_OK);
	mov	d4,#1
	j	NvM_IntUpdateCurrentBlockCRCCompareData
.L640:
	
__NvM_ActFinishEraseBlock_function_end:
	.size	NvM_ActFinishEraseBlock,__NvM_ActFinishEraseBlock_function_end-NvM_ActFinishEraseBlock
.L360:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActInitReadBlockSubFsm',code,cluster('NvM_ActInitReadBlockSubFsm')
	.sect	'.text.NvM_Act.NvM_ActInitReadBlockSubFsm'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1594  }
; ..\eeprom\NvM\NvM_Act.c	  1595  #endif /* (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3) */
; ..\eeprom\NvM\NvM_Act.c	  1596  
; ..\eeprom\NvM\NvM_Act.c	  1597  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1598  *  NvM_ActInitReadBlockSubFsm
; ..\eeprom\NvM\NvM_Act.c	  1599  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1600  /*!
; ..\eeprom\NvM\NvM_Act.c	  1601   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1602   *
; ..\eeprom\NvM\NvM_Act.c	  1603   *
; ..\eeprom\NvM\NvM_Act.c	  1604   *
; ..\eeprom\NvM\NvM_Act.c	  1605   */
; ..\eeprom\NvM\NvM_Act.c	  1606  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitReadBlockSubFsm(void)
; Function NvM_ActInitReadBlockSubFsm
.L163:
NvM_ActInitReadBlockSubFsm:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1607  {
; ..\eeprom\NvM\NvM_Act.c	  1608      NvM_JobSubState_t = NvM_IntServiceDescrTable_at[NVM_INT_FID_READ_BLOCK].InitialState_t;
	fcall	.cocofun_3
.L877:
	ld.bu	d15,[a15]5
.L878:
	st.b	[a2]@los(NvM_JobSubState_t),d15
.L879:

; ..\eeprom\NvM\NvM_Act.c	  1609      NvM_ActionTable_ap[NvM_IntServiceDescrTable_at[NVM_INT_FID_READ_BLOCK].InitialActionId](); /* SBSW_NvM_FuncPtrCall_QueryAction */
	ld.bu	d15,[a15]4
.L880:
	fcall	.cocofun_2
.L881:
	ji	a15
.L604:
	
__NvM_ActInitReadBlockSubFsm_function_end:
	.size	NvM_ActInitReadBlockSubFsm,__NvM_ActInitReadBlockSubFsm_function_end-NvM_ActInitReadBlockSubFsm
.L290:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_3',code,cluster('.cocofun_3')
	.sect	'.text.NvM_Act..cocofun_3'
	.align	2
; Function .cocofun_3
.L165:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:0
	movh.a	a15,#@his(NvM_IntServiceDescrTable_at)
	lea	a15,[a15]@los(NvM_IntServiceDescrTable_at)
.L1419:
	movh.a	a2,#@his(NvM_JobSubState_t)
.L1420:
	fret
.L520:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActInitRestoreBlockDefaultsSubFsm',code,cluster('NvM_ActInitRestoreBlockDefaultsSubFsm')
	.sect	'.text.NvM_Act.NvM_ActInitRestoreBlockDefaultsSubFsm'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1610  }
; ..\eeprom\NvM\NvM_Act.c	  1611  
; ..\eeprom\NvM\NvM_Act.c	  1612  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1613  *  NvM_ActInitRestoreBlockDefaultsSubFsm
; ..\eeprom\NvM\NvM_Act.c	  1614  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1615  /*!
; ..\eeprom\NvM\NvM_Act.c	  1616   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1617   *
; ..\eeprom\NvM\NvM_Act.c	  1618   *
; ..\eeprom\NvM\NvM_Act.c	  1619   *
; ..\eeprom\NvM\NvM_Act.c	  1620   */
; ..\eeprom\NvM\NvM_Act.c	  1621  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitRestoreBlockDefaultsSubFsm(void)
; Function NvM_ActInitRestoreBlockDefaultsSubFsm
.L167:
NvM_ActInitRestoreBlockDefaultsSubFsm:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1622  {
; ..\eeprom\NvM\NvM_Act.c	  1623      NvM_JobSubState_t = NvM_IntServiceDescrTable_at[NVM_INT_FID_RESTORE_DEFAULTS].InitialState_t;
	fcall	.cocofun_3
.L886:
	ld.bu	d15,[a15]9
.L887:
	st.b	[a2]@los(NvM_JobSubState_t),d15
.L888:

; ..\eeprom\NvM\NvM_Act.c	  1624      NvM_ActionTable_ap[NvM_IntServiceDescrTable_at[NVM_INT_FID_RESTORE_DEFAULTS].InitialActionId](); /* SBSW_NvM_FuncPtrCall_QueryAction */
	ld.bu	d15,[a15]8
.L889:
	fcall	.cocofun_2
.L890:
	ji	a15
.L606:
	
__NvM_ActInitRestoreBlockDefaultsSubFsm_function_end:
	.size	NvM_ActInitRestoreBlockDefaultsSubFsm,__NvM_ActInitRestoreBlockDefaultsSubFsm_function_end-NvM_ActInitRestoreBlockDefaultsSubFsm
.L295:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActInitWriteBlockFsm',code,cluster('NvM_ActInitWriteBlockFsm')
	.sect	'.text.NvM_Act.NvM_ActInitWriteBlockFsm'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1625  }
; ..\eeprom\NvM\NvM_Act.c	  1626  
; ..\eeprom\NvM\NvM_Act.c	  1627  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1628  *  NvM_ActInitWriteBlockFsm
; ..\eeprom\NvM\NvM_Act.c	  1629  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1630  /*!
; ..\eeprom\NvM\NvM_Act.c	  1631   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1632   *
; ..\eeprom\NvM\NvM_Act.c	  1633   *
; ..\eeprom\NvM\NvM_Act.c	  1634   *
; ..\eeprom\NvM\NvM_Act.c	  1635   *
; ..\eeprom\NvM\NvM_Act.c	  1636   */
; ..\eeprom\NvM\NvM_Act.c	  1637  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitWriteBlockFsm(void)
; Function NvM_ActInitWriteBlockFsm
.L169:
NvM_ActInitWriteBlockFsm:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1638  {
; ..\eeprom\NvM\NvM_Act.c	  1639      const NvM_StateActionIdType InitialActId = NvM_IntServiceDescrTable_at[NVM_INT_FID_WRITE_BLOCK].InitialActionId;
; ..\eeprom\NvM\NvM_Act.c	  1640  
; ..\eeprom\NvM\NvM_Act.c	  1641      NvM_JobSubState_t = NvM_IntServiceDescrTable_at[NVM_INT_FID_WRITE_BLOCK].InitialState_t;
	fcall	.cocofun_3
.L916:
	ld.bu	d15,[a15]1
.L917:
	st.b	[a2]@los(NvM_JobSubState_t),d15
.L918:
	ld.bu	d15,[a15]
.L919:

; ..\eeprom\NvM\NvM_Act.c	  1642      NvM_ActionTable_ap[InitialActId](); /* SBSW_NvM_FuncPtrCall_QueryAction */
	fcall	.cocofun_2
.L920:
	ji	a15
.L610:
	
__NvM_ActInitWriteBlockFsm_function_end:
	.size	NvM_ActInitWriteBlockFsm,__NvM_ActInitWriteBlockFsm_function_end-NvM_ActInitWriteBlockFsm
.L310:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActInitRestoreBlockDefaults',code,cluster('NvM_ActInitRestoreBlockDefaults')
	.sect	'.text.NvM_Act.NvM_ActInitRestoreBlockDefaults'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1643  }
; ..\eeprom\NvM\NvM_Act.c	  1644  
; ..\eeprom\NvM\NvM_Act.c	  1645  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1646  *  NvM_ActInitRestoreBlockDefaults
; ..\eeprom\NvM\NvM_Act.c	  1647  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1648  /*!
; ..\eeprom\NvM\NvM_Act.c	  1649   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1650   *
; ..\eeprom\NvM\NvM_Act.c	  1651   *
; ..\eeprom\NvM\NvM_Act.c	  1652   */
; ..\eeprom\NvM\NvM_Act.c	  1653  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInitRestoreBlockDefaults(void)
; Function NvM_ActInitRestoreBlockDefaults
.L171:
NvM_ActInitRestoreBlockDefaults:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1654  {
; ..\eeprom\NvM\NvM_Act.c	  1655      /* that's the only necessary initialization                          *
; ..\eeprom\NvM\NvM_Act.c	  1656       * processing is done within state machine - nothing else to do here */
; ..\eeprom\NvM\NvM_Act.c	  1657      NvM_CurrentBlockInfo_t.ByteCount_u16 = NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16;
	fcall	.cocofun_4
.L925:

; ..\eeprom\NvM\NvM_Act.c	  1658  }
	ret
.L611:
	
__NvM_ActInitRestoreBlockDefaults_function_end:
	.size	NvM_ActInitRestoreBlockDefaults,__NvM_ActInitRestoreBlockDefaults_function_end-NvM_ActInitRestoreBlockDefaults
.L315:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_4',code,cluster('.cocofun_4')
	.sect	'.text.NvM_Act..cocofun_4'
	.align	2
; Function .cocofun_4
.L173:
.cocofun_4:	.type	func
; Function body .cocofun_4, coco_iter:0
	fcall	.cocofun_14
.L1425:
	ld.a	a2,[a15]
.L1426:
	ld.hu	d15,[a2]50
.L1427:
	st.h	[a15]38,d15
.L1428:
	fret
.L525:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActInvalidateNvBlock',code,cluster('NvM_ActInvalidateNvBlock')
	.sect	'.text.NvM_Act.NvM_ActInvalidateNvBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1659  
; ..\eeprom\NvM\NvM_Act.c	  1660  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM_Act.c	  1661  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1662  *  NvM_ActInvalidateNvBlock
; ..\eeprom\NvM\NvM_Act.c	  1663  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1664  /*!
; ..\eeprom\NvM\NvM_Act.c	  1665   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1666   *
; ..\eeprom\NvM\NvM_Act.c	  1667   *
; ..\eeprom\NvM\NvM_Act.c	  1668   *
; ..\eeprom\NvM\NvM_Act.c	  1669   */
; ..\eeprom\NvM\NvM_Act.c	  1670  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActInvalidateNvBlock(void)
; Function NvM_ActInvalidateNvBlock
.L175:
NvM_ActInvalidateNvBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1671  {
; ..\eeprom\NvM\NvM_Act.c	  1672      if (E_OK != MemIf_InvalidateBlock((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8,
; ..\eeprom\NvM\NvM_Act.c	  1673                                        NvM_CurrentBlockInfo_t.NvIdentifier_u16))
	fcall	.cocofun_1
.L1100:
	call	MemIf_InvalidateBlock
.L1101:
	fcall	.cocofun_5
.L1102:

; ..\eeprom\NvM\NvM_Act.c	  1674      {
; ..\eeprom\NvM\NvM_Act.c	  1675          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Act.c	  1676      }
; ..\eeprom\NvM\NvM_Act.c	  1677      else
; ..\eeprom\NvM\NvM_Act.c	  1678      {
; ..\eeprom\NvM\NvM_Act.c	  1679          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
; ..\eeprom\NvM\NvM_Act.c	  1680      }
; ..\eeprom\NvM\NvM_Act.c	  1681  }
	ret
.L643:
	
__NvM_ActInvalidateNvBlock_function_end:
	.size	NvM_ActInvalidateNvBlock,__NvM_ActInvalidateNvBlock_function_end-NvM_ActInvalidateNvBlock
.L370:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActNop',code,cluster('NvM_ActNop')
	.sect	'.text.NvM_Act.NvM_ActNop'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1682  #endif /* (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3) */
; ..\eeprom\NvM\NvM_Act.c	  1683  
; ..\eeprom\NvM\NvM_Act.c	  1684  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1685  *  NvM_ActNop
; ..\eeprom\NvM\NvM_Act.c	  1686  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1687  /*!
; ..\eeprom\NvM\NvM_Act.c	  1688   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1689   *
; ..\eeprom\NvM\NvM_Act.c	  1690   *
; ..\eeprom\NvM\NvM_Act.c	  1691   */
; ..\eeprom\NvM\NvM_Act.c	  1692  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActNop(void)
; Function NvM_ActNop
.L177:
NvM_ActNop:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1693  {
; ..\eeprom\NvM\NvM_Act.c	  1694      return;
; ..\eeprom\NvM\NvM_Act.c	  1695  }
	ret
.L668:
	
__NvM_ActNop_function_end:
	.size	NvM_ActNop,__NvM_ActNop_function_end-NvM_ActNop
.L460:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActWriteNvBlock',code,cluster('NvM_ActWriteNvBlock')
	.sect	'.text.NvM_Act.NvM_ActWriteNvBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1696  
; ..\eeprom\NvM\NvM_Act.c	  1697  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1698  *  NvM_ActWriteNvBlock
; ..\eeprom\NvM\NvM_Act.c	  1699  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1700  /*!
; ..\eeprom\NvM\NvM_Act.c	  1701   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1702   *
; ..\eeprom\NvM\NvM_Act.c	  1703   *
; ..\eeprom\NvM\NvM_Act.c	  1704   *
; ..\eeprom\NvM\NvM_Act.c	  1705   *
; ..\eeprom\NvM\NvM_Act.c	  1706   *
; ..\eeprom\NvM\NvM_Act.c	  1707   *
; ..\eeprom\NvM\NvM_Act.c	  1708   *
; ..\eeprom\NvM\NvM_Act.c	  1709   *
; ..\eeprom\NvM\NvM_Act.c	  1710   *
; ..\eeprom\NvM\NvM_Act.c	  1711   *
; ..\eeprom\NvM\NvM_Act.c	  1712   *
; ..\eeprom\NvM\NvM_Act.c	  1713   *
; ..\eeprom\NvM\NvM_Act.c	  1714   */
; ..\eeprom\NvM\NvM_Act.c	  1715  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActWriteNvBlock(void)
; Function NvM_ActWriteNvBlock
.L179:
NvM_ActWriteNvBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1716  {
; ..\eeprom\NvM\NvM_Act.c	  1717      const NvM_RamAddressType src_pt = NvM_CurrentBlockInfo_t.NvRamAddr_t;
	movh.a	a12,#@his(NvM_CurrentBlockInfo_t)
	lea	a12,[a12]@los(NvM_CurrentBlockInfo_t)
.L1112:

; ..\eeprom\NvM\NvM_Act.c	  1718      Std_ReturnType retValMemIf;
; ..\eeprom\NvM\NvM_Act.c	  1719  
; ..\eeprom\NvM\NvM_Act.c	  1720      NvM_CurrentBlockInfo_t.WriteRetryCounter_u8++;
	ld.bu	d15,[a12]41
.L1113:

; ..\eeprom\NvM\NvM_Act.c	  1721      /* Invalidate CRCCompareMechanism data to avoid following situation:
; ..\eeprom\NvM\NvM_Act.c	  1722       * block is up to date with NV RAM, but CRC does not fit -> copy the CRC to internal CRC buffer -> reset -> startup ->
; ..\eeprom\NvM\NvM_Act.c	  1723       * block is up to date AND the CRC matches the data -> NvM won't write -> RAM does not match NV RAM */
; ..\eeprom\NvM\NvM_Act.c	  1724      NvM_IntUpdateCurrentBlockCRCCompareData(NVM_REQ_NOT_OK);
	mov	d4,#1
	ld.a	a13,[a12]12
.L724:
	add	d15,#1
	st.b	[a12]41,d15
.L1114:
	call	NvM_IntUpdateCurrentBlockCRCCompareData
.L1115:

; ..\eeprom\NvM\NvM_Act.c	  1725      /* we have to append CRC value to data buffer (for redundant blocks it is okay to do it twice. * 
; ..\eeprom\NvM\NvM_Act.c	  1726       * Additionally these operations are allowed, even if no CRC was actually configured            */
; ..\eeprom\NvM\NvM_Act.c	  1727      NvM_CrcJob_CopyToBuffer(&NvM_CurrentBlockInfo_t.BlockCrcJob_t); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	lea	a14,[a12]16
	mov.aa	a4,a14
	call	NvM_CrcJob_CopyToBuffer
.L1116:

; ..\eeprom\NvM\NvM_Act.c	  1728  
; ..\eeprom\NvM\NvM_Act.c	  1729  #if(NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1730      /* If we're processing the configured permanent RAM block, we have to copy the CRC into block's Calc Ram Block CRC buffer.
; ..\eeprom\NvM\NvM_Act.c	  1731       * The most recent call to NvM_CrcJob_CopyToBuffer copied the CRC value into the internal buffer */
; ..\eeprom\NvM\NvM_Act.c	  1732      if(NvM_CurrentJob_t.RamAddr_t == NULL_PTR) /* COV_NVM_APICFGCLASS */
	movh.a	a15,#@his(NvM_CurrentJob_t)
	lea	a15,[a15]@los(NvM_CurrentJob_t)
.L1117:
	ld.w	d15,[a15]4
.L1118:
	jne	d15,#0,.L51
.L1119:

; ..\eeprom\NvM\NvM_Act.c	  1733      {
; ..\eeprom\NvM\NvM_Act.c	  1734          NvM_CrcJob_ExportBufferedValue(&NvM_CurrentBlockInfo_t.BlockCrcJob_t, /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  1735                                          NvM_CurrentBlockInfo_t.Descriptor_pt->RamBlockCrcAddr_t);
	ld.a	a2,[a12]
.L1120:
	mov.aa	a4,a14
	ld.a	a5,[a2]40
	call	NvM_CrcJob_ExportBufferedValue
.L51:

; ..\eeprom\NvM\NvM_Act.c	  1736      }
; ..\eeprom\NvM\NvM_Act.c	  1737  #endif
; ..\eeprom\NvM\NvM_Act.c	  1738  
; ..\eeprom\NvM\NvM_Act.c	  1739  #if(NVM_KILL_WRITEALL_API == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Act.c	  1740      /* Prevent from issuing a write job to lower layer, if KillWriteAll was requested.
; ..\eeprom\NvM\NvM_Act.c	  1741       *  Critical section, because EcuM (caller of NvM_KilWriteAll)
; ..\eeprom\NvM\NvM_Act.c	  1742       *                    might run in a task which preempted our MainFunction!! */
; ..\eeprom\NvM\NvM_Act.c	  1743      NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L1121:

; ..\eeprom\NvM\NvM_Act.c	  1744  
; ..\eeprom\NvM\NvM_Act.c	  1745      if(NvM_IsWriteAllAndKilled(NvM_CurrentJob_t.JobServiceId_t, NvM_ApiFlags_u8)) /* COV_NVM_KILLWRITEALL */
; ..\eeprom\NvM\NvM_Act.c	  1746      {
; ..\eeprom\NvM\NvM_Act.c	  1747          /* Result is quite irrelevant, but PENDING prevents from write retry handling */
; ..\eeprom\NvM\NvM_Act.c	  1748          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
; ..\eeprom\NvM\NvM_Act.c	  1749      }
; ..\eeprom\NvM\NvM_Act.c	  1750      else
; ..\eeprom\NvM\NvM_Act.c	  1751  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Act.c	  1752      {
; ..\eeprom\NvM\NvM_Act.c	  1753          retValMemIf = MemIf_Write((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8, /* SBSW_NvM_FuncCall_PtrParam_MemIf */
; ..\eeprom\NvM\NvM_Act.c	  1754                                                          NvM_CurrentBlockInfo_t.NvIdentifier_u16, src_pt);
; ..\eeprom\NvM\NvM_Act.c	  1755          if(E_OK == retValMemIf)
; ..\eeprom\NvM\NvM_Act.c	  1756          {
; ..\eeprom\NvM\NvM_Act.c	  1757            NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
	lea	a14,[a12]40
.L1122:
	ld.bu	d4,[a15]2
.L1123:
	movh.a	a15,#@his(NvM_ApiFlags_u8)
	ld.bu	d5,[a15]@los(NvM_ApiFlags_u8)
	call	NvM_IsWriteAllAndKilled
.L1124:
	jne	d2,#0,.L52
.L1125:
	ld.a	a15,[a12]
.L1126:
	ld.hu	d5,[a12]36
.L1127:
	mov.aa	a4,a13
.L725:
	ld.bu	d15,[a15]58
.L1128:
	and	d4,d15,#15
	call	MemIf_Write
.L726:
	jne	d2,#0,.L53
.L52:
	mov	d15,#2
	j	.L54
.L53:

; ..\eeprom\NvM\NvM_Act.c	  1758          }
; ..\eeprom\NvM\NvM_Act.c	  1759          else
; ..\eeprom\NvM\NvM_Act.c	  1760          {
; ..\eeprom\NvM\NvM_Act.c	  1761            NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
	mov	d15,#1
.L54:
	st.b	[a14],d15
.L1129:

; ..\eeprom\NvM\NvM_Act.c	  1762          }
; ..\eeprom\NvM\NvM_Act.c	  1763      }
; ..\eeprom\NvM\NvM_Act.c	  1764  
; ..\eeprom\NvM\NvM_Act.c	  1765  #if(NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1766      NvM_ExitCriticalSection();
	j	NvM_ExitCriticalSection
.L645:
	
__NvM_ActWriteNvBlock_function_end:
	.size	NvM_ActWriteNvBlock,__NvM_ActWriteNvBlock_function_end-NvM_ActWriteNvBlock
.L380:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActReadNvBlock',code,cluster('NvM_ActReadNvBlock')
	.sect	'.text.NvM_Act.NvM_ActReadNvBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1767  #endif
; ..\eeprom\NvM\NvM_Act.c	  1768  }
; ..\eeprom\NvM\NvM_Act.c	  1769  
; ..\eeprom\NvM\NvM_Act.c	  1770  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1771  *  NvM_ActReadNvBlock
; ..\eeprom\NvM\NvM_Act.c	  1772  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1773  /*!
; ..\eeprom\NvM\NvM_Act.c	  1774   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1775   *
; ..\eeprom\NvM\NvM_Act.c	  1776   *
; ..\eeprom\NvM\NvM_Act.c	  1777   *
; ..\eeprom\NvM\NvM_Act.c	  1778   *
; ..\eeprom\NvM\NvM_Act.c	  1779   *
; ..\eeprom\NvM\NvM_Act.c	  1780   *
; ..\eeprom\NvM\NvM_Act.c	  1781   *
; ..\eeprom\NvM\NvM_Act.c	  1782   */
; ..\eeprom\NvM\NvM_Act.c	  1783  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActReadNvBlock(void)
; Function NvM_ActReadNvBlock
.L181:
NvM_ActReadNvBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1784  {
; ..\eeprom\NvM\NvM_Act.c	  1785      const NvM_BlockDescrPtrType descr_pt = NvM_CurrentBlockInfo_t.Descriptor_pt;
	fcall	.cocofun_14
.L1134:
	ld.a	a12,[a15]
.L727:

; ..\eeprom\NvM\NvM_Act.c	  1786  
; ..\eeprom\NvM\NvM_Act.c	  1787  #if (NVM_USE_CSM == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1788      NvM_CurrentBlockInfo_t.CsmJobRetryCounter_u8 = 0u;
; ..\eeprom\NvM\NvM_Act.c	  1789  #endif
; ..\eeprom\NvM\NvM_Act.c	  1790  
; ..\eeprom\NvM\NvM_Act.c	  1791      /* Re-init CRC calc job, for two purposes:
; ..\eeprom\NvM\NvM_Act.c	  1792       * - if we are processing the secondary NV block of a redundant NVRAM Block, CRC calculation must be restarted
; ..\eeprom\NvM\NvM_Act.c	  1793       * - if we are actually processing a CRC block - the internal buffer will be the CRC source.
; ..\eeprom\NvM\NvM_Act.c	  1794       * (if we're processing a non-CRC block, it does not matter - it won't be used)
; ..\eeprom\NvM\NvM_Act.c	  1795       * First purpose is the reason to re-init CRC job also in case internal buffering was disabled. */
; ..\eeprom\NvM\NvM_Act.c	  1796      NvM_CrcJob_Create(&NvM_CurrentBlockInfo_t.BlockCrcJob_t, NvM_CurrentJob_t.JobBlockId_t,
	fcall	.cocofun_12
.L1135:

; ..\eeprom\NvM\NvM_Act.c	  1797          NvM_CurrentBlockInfo_t.NvRamAddr_t, descr_pt->NvBlockNVRAMDataLength); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	ld.hu	d5,[a12]54
	call	NvM_CrcJob_Create
.L1136:

; ..\eeprom\NvM\NvM_Act.c	  1798      /* Byte Count for Data Copy (after CRC calculation). 
; ..\eeprom\NvM\NvM_Act.c	  1799       * We'll go through copy, even if NVM_CRC_INT_BUFFER == STD_OFF (CRC mismatch -> restore ROM defaults */
; ..\eeprom\NvM\NvM_Act.c	  1800      NvM_CurrentBlockInfo_t.ByteCount_u16 = descr_pt->NvBlockLength_u16;
	ld.hu	d15,[a12]50
.L1137:
	st.h	[a15]38,d15
.L1138:

; ..\eeprom\NvM\NvM_Act.c	  1801      
; ..\eeprom\NvM\NvM_Act.c	  1802      if (E_OK != MemIf_Read((uint8)descr_pt->DeviceId_u8, /* SBSW_NvM_FuncCall_PtrParam_MemIf */
; ..\eeprom\NvM\NvM_Act.c	  1803          NvM_CurrentBlockInfo_t.NvIdentifier_u16, 0u, NvM_CurrentBlockInfo_t.NvRamAddr_t,
	mov	d6,#0
.L1139:

; ..\eeprom\NvM\NvM_Act.c	  1804          (uint16)(descr_pt->NvBlockNVRAMDataLength + NvM_CrcJob_GetCrcLength(&NvM_CurrentBlockInfo_t.BlockCrcJob_t))))
	ld.a	a2,[a15]28
.L1140:
	ld.bu	d15,[a12]58
.L1141:
	ld.hu	d5,[a15]36
.L1142:
	and	d4,d15,#15
	ld.hu	d15,[a12]54
.L1143:
	ld.bu	d0,[a2]16
.L1144:
	ld.a	a4,[a15]12
.L1145:
	add	d15,d0
.L1146:
	extr.u	d7,d15,#0,#16
	call	MemIf_Read
.L728:
	fcall	.cocofun_5
.L729:

; ..\eeprom\NvM\NvM_Act.c	  1805      {
; ..\eeprom\NvM\NvM_Act.c	  1806          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Act.c	  1807      }
; ..\eeprom\NvM\NvM_Act.c	  1808      else
; ..\eeprom\NvM\NvM_Act.c	  1809      {
; ..\eeprom\NvM\NvM_Act.c	  1810          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
; ..\eeprom\NvM\NvM_Act.c	  1811      }
; ..\eeprom\NvM\NvM_Act.c	  1812  }
	ret
.L649:
	
__NvM_ActReadNvBlock_function_end:
	.size	NvM_ActReadNvBlock,__NvM_ActReadNvBlock_function_end-NvM_ActReadNvBlock
.L385:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActProcessCrcRead',code,cluster('NvM_ActProcessCrcRead')
	.sect	'.text.NvM_Act.NvM_ActProcessCrcRead'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1813  
; ..\eeprom\NvM\NvM_Act.c	  1814  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1815  *  NvM_ActProcessCrcRead
; ..\eeprom\NvM\NvM_Act.c	  1816  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1817  /*!
; ..\eeprom\NvM\NvM_Act.c	  1818   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1819   *
; ..\eeprom\NvM\NvM_Act.c	  1820   *
; ..\eeprom\NvM\NvM_Act.c	  1821   */
; ..\eeprom\NvM\NvM_Act.c	  1822  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActProcessCrcRead(void)
; Function NvM_ActProcessCrcRead
.L183:
NvM_ActProcessCrcRead:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1823  {
; ..\eeprom\NvM\NvM_Act.c	  1824      NvM_CrcJob_Process(&(NvM_CurrentBlockInfo_t.BlockCrcJob_t), NvM_NoOfCrcBytes_u16); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	fcall	.cocofun_9
.L1151:
	j	NvM_CrcJob_Process
.L652:
	
__NvM_ActProcessCrcRead_function_end:
	.size	NvM_ActProcessCrcRead,__NvM_ActProcessCrcRead_function_end-NvM_ActProcessCrcRead
.L390:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_9',code,cluster('.cocofun_9')
	.sect	'.text.NvM_Act..cocofun_9'
	.align	2
; Function .cocofun_9
.L185:
.cocofun_9:	.type	func
; Function body .cocofun_9, coco_iter:0
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+16)
.L1455:
	lea	a4,[a15]@los(NvM_CurrentBlockInfo_t+16)
.L1456:
	movh.a	a15,#@his(NvM_NoOfCrcBytes_u16)
	ld.hu	d4,[a15]@los(NvM_NoOfCrcBytes_u16)
.L1457:
	fret
.L550:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActReadCopyData',code,cluster('NvM_ActReadCopyData')
	.sect	'.text.NvM_Act.NvM_ActReadCopyData'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1825  }
; ..\eeprom\NvM\NvM_Act.c	  1826  
; ..\eeprom\NvM\NvM_Act.c	  1827  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1828  *  NvM_ActReadCopyData
; ..\eeprom\NvM\NvM_Act.c	  1829  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1830  /*!
; ..\eeprom\NvM\NvM_Act.c	  1831   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1832   *
; ..\eeprom\NvM\NvM_Act.c	  1833   *
; ..\eeprom\NvM\NvM_Act.c	  1834   */
; ..\eeprom\NvM\NvM_Act.c	  1835  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActReadCopyData(void)
; Function NvM_ActReadCopyData
.L187:
NvM_ActReadCopyData:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1836  {
; ..\eeprom\NvM\NvM_Act.c	  1837      /* #10 Copy data, if internal CRC buffers are enabled, nothing to do otherwise. */
; ..\eeprom\NvM\NvM_Act.c	  1838  #if(NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1839      NvM_InternalCopyBufferedData(&NvM_CurrentBlockInfo_t, NvM_CurrentBlockInfo_t.RamAddr_t); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	movh.a	a4,#@his(NvM_CurrentBlockInfo_t)
	lea	a4,[a4]@los(NvM_CurrentBlockInfo_t)
.L1156:
	ld.a	a5,[a4]8
	j	NvM_InternalCopyBufferedData
.L654:
	
__NvM_ActReadCopyData_function_end:
	.size	NvM_ActReadCopyData,__NvM_ActReadCopyData_function_end-NvM_ActReadCopyData
.L395:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_InternalCopyBufferedData',code,cluster('NvM_InternalCopyBufferedData')
	.sect	'.text.NvM_Act.NvM_InternalCopyBufferedData'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1840  #else
; ..\eeprom\NvM\NvM_Act.c	  1841      NvM_CurrentBlockInfo_t.ByteCount_u16 = 0u;
; ..\eeprom\NvM\NvM_Act.c	  1842  #endif
; ..\eeprom\NvM\NvM_Act.c	  1843  }
; ..\eeprom\NvM\NvM_Act.c	  1844  
; ..\eeprom\NvM\NvM_Act.c	  1845  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1846  *  NvM_InternalCopyBufferedData
; ..\eeprom\NvM\NvM_Act.c	  1847  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1848  /*!
; ..\eeprom\NvM\NvM_Act.c	  1849   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1850   *
; ..\eeprom\NvM\NvM_Act.c	  1851   *
; ..\eeprom\NvM\NvM_Act.c	  1852   *
; ..\eeprom\NvM\NvM_Act.c	  1853   *
; ..\eeprom\NvM\NvM_Act.c	  1854   *
; ..\eeprom\NvM\NvM_Act.c	  1855   *
; ..\eeprom\NvM\NvM_Act.c	  1856   */
; ..\eeprom\NvM\NvM_Act.c	  1857  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_InternalCopyBufferedData(NvM_BlockInfoPtrType info_pt, NvM_ConstRamAddressType srcPtr)
; Function NvM_InternalCopyBufferedData
.L189:
NvM_InternalCopyBufferedData:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1858  {
; ..\eeprom\NvM\NvM_Act.c	  1859  #if(NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  1860      const NvM_BlockDescrPtrType descr_pt = NvM_CurrentBlockInfo_t.Descriptor_pt;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t)
.L1358:
	ld.a	a15,[a15]@los(NvM_CurrentBlockInfo_t)
.L732:
	mov.aa	a12,a4
.L733:
	mov.aa	a6,a5
.L734:

; ..\eeprom\NvM\NvM_Act.c	  1861  
; ..\eeprom\NvM\NvM_Act.c	  1862      /* Mirror Cbk Function shall only be used, if no temporary RAM block was assigned with request */
; ..\eeprom\NvM\NvM_Act.c	  1863      if((descr_pt->CbkGetMirrorFunc_pt != NULL_PTR) && (NvM_CurrentJob_t.RamAddr_t == NULL_PTR)) /* COV_NVM_APICFGCLASS */
	ld.a	a2,[a15]24
.L1359:
	jz.a	a2,.L55
.L1360:
	movh.a	a4,#@his(NvM_CurrentJob_t+4)
.L730:
	ld.a	a4,[a4]@los(NvM_CurrentJob_t+4)
.L1361:
	jnz.a	a4,.L56
.L1362:

; ..\eeprom\NvM\NvM_Act.c	  1864      { /* client copies on its own -> call callback NvMReadRamBlockFromNvM */
; ..\eeprom\NvM\NvM_Act.c	  1865          /* PRQA S 0315 1 */ /* MD_NvM_Dir1.1_CastToVoidPtr */
; ..\eeprom\NvM\NvM_Act.c	  1866          if(descr_pt->CbkGetMirrorFunc_pt(srcPtr) == E_OK) /* SBSW_NvM_FuncPtrCall_UserCallbacks */
	mov.aa	a4,a6
.L731:
	calli	a2
.L735:
	jne	d2,#0,.L57
.L1363:
	j	.L58
.L56:
.L55:
	movh.a	a2,#@his(NvM_CurrentJob_t+4)
.L1364:
	ld.a	a5,[a2]@los(NvM_CurrentJob_t+4)
.L692:

; ..\eeprom\NvM\NvM_Act.c	  1867          {
; ..\eeprom\NvM\NvM_Act.c	  1868              info_pt->ByteCount_u16 = 0u; /* SBSW_NvM_Access_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  1869          }
; ..\eeprom\NvM\NvM_Act.c	  1870      }
; ..\eeprom\NvM\NvM_Act.c	  1871      else
; ..\eeprom\NvM\NvM_Act.c	  1872      {
; ..\eeprom\NvM\NvM_Act.c	  1873          const NvM_RamAddressType destPtr = (NvM_CurrentJob_t.RamAddr_t != NULL_PTR) ?
	jnz.a	a5,.L60
.L1365:

; ..\eeprom\NvM\NvM_Act.c	  1874              NvM_CurrentJob_t.RamAddr_t : descr_pt->RamBlockDataAddr_t; /* COV_NVM_APICFGCLASS */
	ld.a	a5,[a15]
.L60:

; ..\eeprom\NvM\NvM_Act.c	  1875          if(srcPtr != destPtr)
	jeq.a	a6,a5,.L61
.L1366:

; ..\eeprom\NvM\NvM_Act.c	  1876          { /* we copy */
; ..\eeprom\NvM\NvM_Act.c	  1877              NvM_InternalCopyData(info_pt, destPtr, srcPtr); /* SBSW_NvM_FuncCall_PtrParam_InternalCopyData */
	mov.aa	a4,a12
.L736:
	j	NvM_InternalCopyData
.L61:
.L58:

; ..\eeprom\NvM\NvM_Act.c	  1878          }
; ..\eeprom\NvM\NvM_Act.c	  1879          else
; ..\eeprom\NvM\NvM_Act.c	  1880          { /* don't copy, at all */
; ..\eeprom\NvM\NvM_Act.c	  1881              info_pt->ByteCount_u16 = 0u; /* SBSW_NvM_Access_CurrBlockInfo */
	mov	d15,#0
	st.h	[a12]38,d15
.L57:

; ..\eeprom\NvM\NvM_Act.c	  1882          }
; ..\eeprom\NvM\NvM_Act.c	  1883      }
; ..\eeprom\NvM\NvM_Act.c	  1884  #else
; ..\eeprom\NvM\NvM_Act.c	  1885      NvM_InternalCopyData(info_pt, info_pt->RamAddr_t, srcPtr); /* SBSW_NvM_FuncCall_PtrParam_InternalCopyData */
; ..\eeprom\NvM\NvM_Act.c	  1886  #endif
; ..\eeprom\NvM\NvM_Act.c	  1887  }
	ret
.L687:
	
__NvM_InternalCopyBufferedData_function_end:
	.size	NvM_InternalCopyBufferedData,__NvM_InternalCopyBufferedData_function_end-NvM_InternalCopyBufferedData
.L490:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActRestoreRomDefaults',code,cluster('NvM_ActRestoreRomDefaults')
	.sect	'.text.NvM_Act.NvM_ActRestoreRomDefaults'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1888  
; ..\eeprom\NvM\NvM_Act.c	  1889  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1890  *  NvM_ActRestoreRomDefaults
; ..\eeprom\NvM\NvM_Act.c	  1891  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1892  /*!
; ..\eeprom\NvM\NvM_Act.c	  1893   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1894   *
; ..\eeprom\NvM\NvM_Act.c	  1895   *
; ..\eeprom\NvM\NvM_Act.c	  1896   *
; ..\eeprom\NvM\NvM_Act.c	  1897   *
; ..\eeprom\NvM\NvM_Act.c	  1898   *
; ..\eeprom\NvM\NvM_Act.c	  1899   *
; ..\eeprom\NvM\NvM_Act.c	  1900   *
; ..\eeprom\NvM\NvM_Act.c	  1901   *
; ..\eeprom\NvM\NvM_Act.c	  1902   *
; ..\eeprom\NvM\NvM_Act.c	  1903   */
; ..\eeprom\NvM\NvM_Act.c	  1904  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRestoreRomDefaults(void)
; Function NvM_ActRestoreRomDefaults
.L191:
NvM_ActRestoreRomDefaults:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1905  {
; ..\eeprom\NvM\NvM_Act.c	  1906    /* #100 create a dummy Crc recalculation job */
; ..\eeprom\NvM\NvM_Act.c	  1907    NvM_CrcJob_Create(&NvM_CurrentBlockInfo_t.BlockCrcJob_t, NvM_CurrentJob_t.JobBlockId_t,
	fcall	.cocofun_6
.L1161:

; ..\eeprom\NvM\NvM_Act.c	  1908        NULL_PTR, NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	fcall	.cocofun_8
.L1162:
	ld.a	a2,[a13]
.L1163:
	lea	a4,[a13]16
.L1164:
	ld.hu	d4,[a12]0
.L1165:
	mov.a	a5,#0
.L1166:
	ld.hu	d5,[a2]50
	call	NvM_CrcJob_Create
.L1167:

; ..\eeprom\NvM\NvM_Act.c	  1909    /* #200 block has Rom */
; ..\eeprom\NvM\NvM_Act.c	  1910    if(NvM_CurrentBlockInfo_t.Descriptor_pt->RomBlockDataAddr_pt != NULL_PTR)
	ld.a	a4,[a13]
.L1168:
	ld.a	a5,[a4]4
.L1169:
	jz.a	a5,.L63
.L1170:

; ..\eeprom\NvM\NvM_Act.c	  1911    {
; ..\eeprom\NvM\NvM_Act.c	  1912      /* #210 copy data */
; ..\eeprom\NvM\NvM_Act.c	  1913      NvM_InternalCopyBufferedData(&NvM_CurrentBlockInfo_t, /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  1914          NvM_CurrentBlockInfo_t.Descriptor_pt->RomBlockDataAddr_pt);
	mov.aa	a4,a13
	call	NvM_InternalCopyBufferedData
.L1171:
	j	.L64
.L63:

; ..\eeprom\NvM\NvM_Act.c	  1915    }
; ..\eeprom\NvM\NvM_Act.c	  1916    else
; ..\eeprom\NvM\NvM_Act.c	  1917    {
; ..\eeprom\NvM\NvM_Act.c	  1918      /* #300 block has no Rom, block has init block callback */
; ..\eeprom\NvM\NvM_Act.c	  1919      if (NvM_QryIsInitCallbackConfigured(NvM_CurrentBlockInfo_t.Descriptor_pt) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	call	NvM_QryIsInitCallbackConfigured
.L1172:
	jne	d2,#1,.L65
.L1173:

; ..\eeprom\NvM\NvM_Act.c	  1920      {
; ..\eeprom\NvM\NvM_Act.c	  1921        /* #310 callback invoking is allowed for current block and current job */
; ..\eeprom\NvM\NvM_Act.c	  1922        if(!((NvM_CurrentJob_t.JobServiceId_t == NVM_INT_FID_READ_ALL) &&
	ld.bu	d15,[a12]2
.L1174:
	jne	d15,#6,.L66
.L1175:

; ..\eeprom\NvM\NvM_Act.c	  1923            ((NvM_CurrentBlockInfo_t.Descriptor_pt->Flags_u8 & NVM_CBK_DURING_READALL_ON) != NVM_CBK_DURING_READALL_ON))) /* COV_NVM_APICFGCLASS */
	ld.a	a2,[a13]
.L1176:
	ld.bu	d15,[a2]59
.L1177:
	jz.t	d15:6,.L67
.L66:
	ld.a	a15,[a13]
.L1178:

; ..\eeprom\NvM\NvM_Act.c	  1924        {
; ..\eeprom\NvM\NvM_Act.c	  1925          /* #311 invoke configured init block callback */
; ..\eeprom\NvM\NvM_Act.c	  1926          if (NvM_CurrentBlockInfo_t.Descriptor_pt->InitCbkExtFunc_pt != NULL_PTR)
	ld.a	a2,[a15]12
.L1179:
	jz.a	a2,.L68
.L1180:

; ..\eeprom\NvM\NvM_Act.c	  1927          {
; ..\eeprom\NvM\NvM_Act.c	  1928            /* Direct invocation of extended callback.
; ..\eeprom\NvM\NvM_Act.c	  1929             * RamAddr_t is ensured to be valid while initialization (NvM_ActInitBlock).
; ..\eeprom\NvM\NvM_Act.c	  1930             *
; ..\eeprom\NvM\NvM_Act.c	  1931             * Note: This code does not consider the configuration possibility of the explicit synchronization feature
; ..\eeprom\NvM\NvM_Act.c	  1932             *       "NvMBlockUseSyncMechanism". See TechRef for further information.
; ..\eeprom\NvM\NvM_Act.c	  1933             */
; ..\eeprom\NvM\NvM_Act.c	  1934            (void)NvM_CurrentBlockInfo_t.Descriptor_pt->InitCbkExtFunc_pt( /* SBSW_NvM_FuncPtrCall_UserCallbacks */
; ..\eeprom\NvM\NvM_Act.c	  1935                NvM_CurrentJob_t.JobBlockId_t,
	ld.hu	d4,[a12]0
.L1181:

; ..\eeprom\NvM\NvM_Act.c	  1936                NvM_CurrentBlockInfo_t.RamAddr_t, /* PRQA S 0315 */ /* MD_NvM_Dir1.1_CastToVoidPtr */
	ld.a	a4,[a13]8
.L1182:

; ..\eeprom\NvM\NvM_Act.c	  1937                NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16);
	ld.hu	d5,[a15]50
	calli	a2
.L1183:
	j	.L69
.L68:

; ..\eeprom\NvM\NvM_Act.c	  1938          }
; ..\eeprom\NvM\NvM_Act.c	  1939          else
; ..\eeprom\NvM\NvM_Act.c	  1940          {
; ..\eeprom\NvM\NvM_Act.c	  1941            (void)NvM_CurrentBlockInfo_t.Descriptor_pt->InitCbkFunc_pt(); /* SBSW_NvM_FuncPtrCall_UserCallbacks */
	ld.a	a15,[a15]8
.L1184:
	calli	a15
.L69:
.L67:
.L65:

; ..\eeprom\NvM\NvM_Act.c	  1942          }
; ..\eeprom\NvM\NvM_Act.c	  1943        }
; ..\eeprom\NvM\NvM_Act.c	  1944      }
; ..\eeprom\NvM\NvM_Act.c	  1945      /* #400 restoring done, reset ByteCount */
; ..\eeprom\NvM\NvM_Act.c	  1946      NvM_CurrentBlockInfo_t.ByteCount_u16 = 0u;
	mov	d15,#0
	st.h	[a13]38,d15
.L64:

; ..\eeprom\NvM\NvM_Act.c	  1947    }
; ..\eeprom\NvM\NvM_Act.c	  1948    /* #500 set wait flag */
; ..\eeprom\NvM\NvM_Act.c	  1949    NvM_ActWait();
	j	NvM_ActWait
.L655:
	
__NvM_ActRestoreRomDefaults_function_end:
	.size	NvM_ActRestoreRomDefaults,__NvM_ActRestoreRomDefaults_function_end-NvM_ActRestoreRomDefaults
.L400:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActFinishRestoreRomDefaults',code,cluster('NvM_ActFinishRestoreRomDefaults')
	.sect	'.text.NvM_Act.NvM_ActFinishRestoreRomDefaults'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1950  }
; ..\eeprom\NvM\NvM_Act.c	  1951  
; ..\eeprom\NvM\NvM_Act.c	  1952  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1953  *  NvM_ActFinishRestoreRomDefaults
; ..\eeprom\NvM\NvM_Act.c	  1954  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1955  /*!
; ..\eeprom\NvM\NvM_Act.c	  1956   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1957   *
; ..\eeprom\NvM\NvM_Act.c	  1958   *
; ..\eeprom\NvM\NvM_Act.c	  1959   *
; ..\eeprom\NvM\NvM_Act.c	  1960   *
; ..\eeprom\NvM\NvM_Act.c	  1961   *
; ..\eeprom\NvM\NvM_Act.c	  1962   *
; ..\eeprom\NvM\NvM_Act.c	  1963   */
; ..\eeprom\NvM\NvM_Act.c	  1964  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActFinishRestoreRomDefaults(void)
; Function NvM_ActFinishRestoreRomDefaults
.L193:
NvM_ActFinishRestoreRomDefaults:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1965  {
; ..\eeprom\NvM\NvM_Act.c	  1966      /* #10 in case current pending block has a permanent Ram and default data (permanent ROM or initialization callback) */
; ..\eeprom\NvM\NvM_Act.c	  1967      if((NvM_CurrentJob_t.RamAddr_t == NULL_PTR) && (
	fcall	.cocofun_13
.L1189:
	jne	d15,#0,.L70
.L1190:

; ..\eeprom\NvM\NvM_Act.c	  1968           (NvM_CurrentBlockInfo_t.Descriptor_pt->RomBlockDataAddr_pt != NULL_PTR) ||
	fcall	.cocofun_14
.L1191:
	ld.a	a4,[a15]
.L1192:
	ld.w	d15,[a4]4
.L1193:
	jne	d15,#0,.L71
.L1194:

; ..\eeprom\NvM\NvM_Act.c	  1969           (NvM_QryIsInitCallbackConfigured(NvM_CurrentBlockInfo_t.Descriptor_pt) == TRUE))) /* PRQA S 3415 */ /* MD_NvM_13.5 */ /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */ /* COV_NVM_APICFGCLASS */
	call	NvM_QryIsInitCallbackConfigured
.L1195:
	jne	d2,#1,.L72
.L71:

; ..\eeprom\NvM\NvM_Act.c	  1970      {
; ..\eeprom\NvM\NvM_Act.c	  1971          NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 |= (NVM_STATE_VALID_SET | NVM_STATE_CHANGED_SET); /* SBSW_NvM_Access_CurrBlockInfo */
	ld.a	a15,[a15]4
.L1196:
	ld.bu	d15,[+a15]2
.L1197:
	or	d15,#3
	st.b	[a15],d15
.L72:
.L70:

; ..\eeprom\NvM\NvM_Act.c	  1972  
; ..\eeprom\NvM\NvM_Act.c	  1973  #if((NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) && (NVM_CALC_RAM_CRC_USED == STD_ON))
; ..\eeprom\NvM\NvM_Act.c	  1974          NvM_CrcQueueJob(NvM_CurrentJob_t.JobBlockId_t);
; ..\eeprom\NvM\NvM_Act.c	  1975  #endif
; ..\eeprom\NvM\NvM_Act.c	  1976      }
; ..\eeprom\NvM\NvM_Act.c	  1977  
; ..\eeprom\NvM\NvM_Act.c	  1978      NvM_IntUpdateCurrentBlockCRCCompareData(NVM_REQ_NOT_OK);
	mov	d4,#1
	j	NvM_IntUpdateCurrentBlockCRCCompareData
.L656:
	
__NvM_ActFinishRestoreRomDefaults_function_end:
	.size	NvM_ActFinishRestoreRomDefaults,__NvM_ActFinishRestoreRomDefaults_function_end-NvM_ActFinishRestoreRomDefaults
.L405:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActSetInitialAttr',code,cluster('NvM_ActSetInitialAttr')
	.sect	'.text.NvM_Act.NvM_ActSetInitialAttr'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  1979  }
; ..\eeprom\NvM\NvM_Act.c	  1980  
; ..\eeprom\NvM\NvM_Act.c	  1981  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  1982  *  NvM_ActSetInitialAttr
; ..\eeprom\NvM\NvM_Act.c	  1983  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  1984  /*!
; ..\eeprom\NvM\NvM_Act.c	  1985   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  1986   *
; ..\eeprom\NvM\NvM_Act.c	  1987   *
; ..\eeprom\NvM\NvM_Act.c	  1988   */
; ..\eeprom\NvM\NvM_Act.c	  1989  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetInitialAttr(void)
; Function NvM_ActSetInitialAttr
.L195:
NvM_ActSetInitialAttr:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  1990  {
; ..\eeprom\NvM\NvM_Act.c	  1991      uint8 attribs_u8loc;
; ..\eeprom\NvM\NvM_Act.c	  1992  
; ..\eeprom\NvM\NvM_Act.c	  1993      /* set the write protection attribute if NVM_BLOCK_WRITE_PROT is TRUE */
; ..\eeprom\NvM\NvM_Act.c	  1994      if ((NvM_CurrentBlockInfo_t.Descriptor_pt->Flags_u8 & NVM_BLOCK_WRITE_PROT_ON) != 0u)
	fcall	.cocofun_14
.L796:

; ..\eeprom\NvM\NvM_Act.c	  1995      {
; ..\eeprom\NvM\NvM_Act.c	  1996          /* set the write protection (we don't need to set one bit, since it is
; ..\eeprom\NvM\NvM_Act.c	  1997           * the only value that shall be set, all other bits are zero)
; ..\eeprom\NvM\NvM_Act.c	  1998           */
; ..\eeprom\NvM\NvM_Act.c	  1999           attribs_u8loc = NVM_WR_PROT_SET;
; ..\eeprom\NvM\NvM_Act.c	  2000      }
; ..\eeprom\NvM\NvM_Act.c	  2001      else
; ..\eeprom\NvM\NvM_Act.c	  2002      {
; ..\eeprom\NvM\NvM_Act.c	  2003          /* clear all bits */
; ..\eeprom\NvM\NvM_Act.c	  2004            attribs_u8loc = 0u;
; ..\eeprom\NvM\NvM_Act.c	  2005      }
; ..\eeprom\NvM\NvM_Act.c	  2006  
; ..\eeprom\NvM\NvM_Act.c	  2007      /* Overwrite all attribute flags with configured write protection setting. */
; ..\eeprom\NvM\NvM_Act.c	  2008      NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 = attribs_u8loc; /* SBSW_NvM_Access_CurrBlockInfo */
	ld.a	a2,[a15]4
.L797:
	ld.a	a15,[a15]
.L798:
	ld.bu	d15,[a15]59
.L799:
	and	d15,#2
.L800:
	ne	d15,d15,#0
.L801:
	sha	d15,#7
	st.b	[a2]2,d15
.L802:

; ..\eeprom\NvM\NvM_Act.c	  2009  }
	ret
.L594:
	
__NvM_ActSetInitialAttr_function_end:
	.size	NvM_ActSetInitialAttr,__NvM_ActSetInitialAttr_function_end-NvM_ActSetInitialAttr
.L270:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActSetReqIntegrityFailed',code,cluster('NvM_ActSetReqIntegrityFailed')
	.sect	'.text.NvM_Act.NvM_ActSetReqIntegrityFailed'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2010  
; ..\eeprom\NvM\NvM_Act.c	  2011  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2012  *  NvM_ActSetReqIntegrityFailed
; ..\eeprom\NvM\NvM_Act.c	  2013  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2014  /*!
; ..\eeprom\NvM\NvM_Act.c	  2015   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2016   *
; ..\eeprom\NvM\NvM_Act.c	  2017   *
; ..\eeprom\NvM\NvM_Act.c	  2018   */
; ..\eeprom\NvM\NvM_Act.c	  2019  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetReqIntegrityFailed(void)
; Function NvM_ActSetReqIntegrityFailed
.L197:
NvM_ActSetReqIntegrityFailed:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2020  {
; ..\eeprom\NvM\NvM_Act.c	  2021      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_INTEGRITY_FAILED;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L1249:
	mov	d15,#3
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+40),d15
.L1250:

; ..\eeprom\NvM\NvM_Act.c	  2022  }
	ret
.L663:
	
__NvM_ActSetReqIntegrityFailed_function_end:
	.size	NvM_ActSetReqIntegrityFailed,__NvM_ActSetReqIntegrityFailed_function_end-NvM_ActSetReqIntegrityFailed
.L435:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActSetReqSkipped',code,cluster('NvM_ActSetReqSkipped')
	.sect	'.text.NvM_Act.NvM_ActSetReqSkipped'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2023  
; ..\eeprom\NvM\NvM_Act.c	  2024  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2025  *  NvM_ActSetReqSkipped
; ..\eeprom\NvM\NvM_Act.c	  2026  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2027  /*!
; ..\eeprom\NvM\NvM_Act.c	  2028   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2029   *
; ..\eeprom\NvM\NvM_Act.c	  2030   *
; ..\eeprom\NvM\NvM_Act.c	  2031   */
; ..\eeprom\NvM\NvM_Act.c	  2032  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetReqSkipped(void)
; Function NvM_ActSetReqSkipped
.L199:
NvM_ActSetReqSkipped:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2033  {
; ..\eeprom\NvM\NvM_Act.c	  2034      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_BLOCK_SKIPPED;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L1255:
	mov	d15,#4
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+40),d15
.L1256:

; ..\eeprom\NvM\NvM_Act.c	  2035  }
	ret
.L664:
	
__NvM_ActSetReqSkipped_function_end:
	.size	NvM_ActSetReqSkipped,__NvM_ActSetReqSkipped_function_end-NvM_ActSetReqSkipped
.L440:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActSetReqNotOk',code,cluster('NvM_ActSetReqNotOk')
	.sect	'.text.NvM_Act.NvM_ActSetReqNotOk'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2036  
; ..\eeprom\NvM\NvM_Act.c	  2037  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2038  *  NvM_ActSetReqNotOk
; ..\eeprom\NvM\NvM_Act.c	  2039  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2040  /*!
; ..\eeprom\NvM\NvM_Act.c	  2041   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2042   *
; ..\eeprom\NvM\NvM_Act.c	  2043   *
; ..\eeprom\NvM\NvM_Act.c	  2044   */
; ..\eeprom\NvM\NvM_Act.c	  2045  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetReqNotOk(void)
; Function NvM_ActSetReqNotOk
.L201:
NvM_ActSetReqNotOk:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2046  {
; ..\eeprom\NvM\NvM_Act.c	  2047    NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L1261:
	mov	d15,#1
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+40),d15
.L1262:

; ..\eeprom\NvM\NvM_Act.c	  2048  }
	ret
.L665:
	
__NvM_ActSetReqNotOk_function_end:
	.size	NvM_ActSetReqNotOk,__NvM_ActSetReqNotOk_function_end-NvM_ActSetReqNotOk
.L445:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActSetReqOk',code,cluster('NvM_ActSetReqOk')
	.sect	'.text.NvM_Act.NvM_ActSetReqOk'
	.align	2
	
	.global	NvM_ActSetReqOk

; ..\eeprom\NvM\NvM_Act.c	  2049  
; ..\eeprom\NvM\NvM_Act.c	  2050  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2051  *  NvM_ActSetReqOk
; ..\eeprom\NvM\NvM_Act.c	  2052  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2053  /*!
; ..\eeprom\NvM\NvM_Act.c	  2054   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2055   *
; ..\eeprom\NvM\NvM_Act.c	  2056   *
; ..\eeprom\NvM\NvM_Act.c	  2057   */
; ..\eeprom\NvM\NvM_Act.c	  2058  FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetReqOk(void)
; Function NvM_ActSetReqOk
.L203:
NvM_ActSetReqOk:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2059  {
; ..\eeprom\NvM\NvM_Act.c	  2060      NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_OK;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+40)
.L780:
	mov	d15,#0
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+40),d15
.L781:

; ..\eeprom\NvM\NvM_Act.c	  2061  }
	ret
.L583:
	
__NvM_ActSetReqOk_function_end:
	.size	NvM_ActSetReqOk,__NvM_ActSetReqOk_function_end-NvM_ActSetReqOk
.L260:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_SetBlockPendingWriteAll',code,cluster('NvM_SetBlockPendingWriteAll')
	.sect	'.text.NvM_Act.NvM_SetBlockPendingWriteAll'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2062  
; ..\eeprom\NvM\NvM_Act.c	  2063  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2064  *  NvM_SetBlockPendingWriteAll
; ..\eeprom\NvM\NvM_Act.c	  2065  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2066  /*!
; ..\eeprom\NvM\NvM_Act.c	  2067   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2068   *
; ..\eeprom\NvM\NvM_Act.c	  2069   *
; ..\eeprom\NvM\NvM_Act.c	  2070   *
; ..\eeprom\NvM\NvM_Act.c	  2071   */
; ..\eeprom\NvM\NvM_Act.c	  2072  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_SetBlockPendingWriteAll(void)
; Function NvM_SetBlockPendingWriteAll
.L205:
NvM_SetBlockPendingWriteAll:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2073  {
; ..\eeprom\NvM\NvM_Act.c	  2074      NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamErrorStatus_u8 = NVM_REQ_PENDING; /* SBSW_NvM_Access_CurrBlockInfo */
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+4)
.L1267:
	ld.a	a15,[a15]@los(NvM_CurrentBlockInfo_t+4)
.L1268:
	mov	d6,#2
.L1269:
	st.b	[a15]1,d6
.L1270:

; ..\eeprom\NvM\NvM_Act.c	  2075      NvM_BlockNotification(NvM_CurrentJob_t.JobBlockId_t, NVM_WRITE_ALL, NVM_REQ_PENDING);
	movh.a	a15,#@his(NvM_CurrentJob_t)
.L1271:
	ld.hu	d4,[a15]@los(NvM_CurrentJob_t)
.L1272:
	mov	d5,#13
.L1273:
	j	NvM_BlockNotification
.L666:
	
__NvM_SetBlockPendingWriteAll_function_end:
	.size	NvM_SetBlockPendingWriteAll,__NvM_SetBlockPendingWriteAll_function_end-NvM_SetBlockPendingWriteAll
.L450:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActSetupRedundant',code,cluster('NvM_ActSetupRedundant')
	.sect	'.text.NvM_Act.NvM_ActSetupRedundant'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2076  }
; ..\eeprom\NvM\NvM_Act.c	  2077  
; ..\eeprom\NvM\NvM_Act.c	  2078  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2079  *  NvM_ActSetupRedundant
; ..\eeprom\NvM\NvM_Act.c	  2080  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2081  /*!
; ..\eeprom\NvM\NvM_Act.c	  2082   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2083   *
; ..\eeprom\NvM\NvM_Act.c	  2084   *
; ..\eeprom\NvM\NvM_Act.c	  2085   *
; ..\eeprom\NvM\NvM_Act.c	  2086   *
; ..\eeprom\NvM\NvM_Act.c	  2087   */
; ..\eeprom\NvM\NvM_Act.c	  2088  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetupRedundant(void)
; Function NvM_ActSetupRedundant
.L207:
NvM_ActSetupRedundant:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2089  {
; ..\eeprom\NvM\NvM_Act.c	  2090      /* updates NvState for current block and changes active block */
; ..\eeprom\NvM\NvM_Act.c	  2091      NvM_ActUpdateNvState();
	call	NvM_ActUpdateNvState
.L1222:

; ..\eeprom\NvM\NvM_Act.c	  2092  
; ..\eeprom\NvM\NvM_Act.c	  2093      NvM_CurrentBlockInfo_t.NvIdentifier_u16 |= 0x01u;
	fcall	.cocofun_14
.L1223:
	ld.hu	d15,[a15]36
.L1224:
	or	d15,#1
	st.h	[a15]36,d15
.L1225:

; ..\eeprom\NvM\NvM_Act.c	  2094  
; ..\eeprom\NvM\NvM_Act.c	  2095      NvM_CurrentBlockInfo_t.WriteRetryCounter_u8 = 0u;
	mov	d15,#0
	st.b	[a15]41,d15
.L1226:

; ..\eeprom\NvM\NvM_Act.c	  2096  }
	ret
.L659:
	
__NvM_ActSetupRedundant_function_end:
	.size	NvM_ActSetupRedundant,__NvM_ActSetupRedundant_function_end-NvM_ActSetupRedundant
.L420:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActSetupOther',code,cluster('NvM_ActSetupOther')
	.sect	'.text.NvM_Act.NvM_ActSetupOther'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2097  
; ..\eeprom\NvM\NvM_Act.c	  2098  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2099  *  NvM_ActSetupOther
; ..\eeprom\NvM\NvM_Act.c	  2100  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2101  /*!
; ..\eeprom\NvM\NvM_Act.c	  2102   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2103   *
; ..\eeprom\NvM\NvM_Act.c	  2104   *
; ..\eeprom\NvM\NvM_Act.c	  2105   *
; ..\eeprom\NvM\NvM_Act.c	  2106   *
; ..\eeprom\NvM\NvM_Act.c	  2107   */
; ..\eeprom\NvM\NvM_Act.c	  2108  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActSetupOther(void)
; Function NvM_ActSetupOther
.L209:
NvM_ActSetupOther:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2109  {
; ..\eeprom\NvM\NvM_Act.c	  2110       /* reset retry counter */
; ..\eeprom\NvM\NvM_Act.c	  2111      NvM_CurrentBlockInfo_t.WriteRetryCounter_u8 = 0u;
	fcall	.cocofun_11
.L1231:

; ..\eeprom\NvM\NvM_Act.c	  2112  
; ..\eeprom\NvM\NvM_Act.c	  2113      /* activate the inactive NvBlock */
; ..\eeprom\NvM\NvM_Act.c	  2114      NvM_CurrentBlockInfo_t.NvIdentifier_u16 ^= 0x0001u;
	ld.hu	d15,[a15]36
.L1232:
	xor	d15,d15,#1
	st.h	[a15]36,d15
.L1233:

; ..\eeprom\NvM\NvM_Act.c	  2115      
; ..\eeprom\NvM\NvM_Act.c	  2116      NvM_ActUpdateNvState();
	j	NvM_ActUpdateNvState
.L660:
	
__NvM_ActSetupOther_function_end:
	.size	NvM_ActSetupOther,__NvM_ActSetupOther_function_end-NvM_ActSetupOther
.L425:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActUpdateNvState',code,cluster('NvM_ActUpdateNvState')
	.sect	'.text.NvM_Act.NvM_ActUpdateNvState'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2117  }
; ..\eeprom\NvM\NvM_Act.c	  2118  
; ..\eeprom\NvM\NvM_Act.c	  2119  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2120  *  NvM_ActUpdateNvState
; ..\eeprom\NvM\NvM_Act.c	  2121  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2122  /*!
; ..\eeprom\NvM\NvM_Act.c	  2123   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2124   *
; ..\eeprom\NvM\NvM_Act.c	  2125   *
; ..\eeprom\NvM\NvM_Act.c	  2126   *
; ..\eeprom\NvM\NvM_Act.c	  2127   *
; ..\eeprom\NvM\NvM_Act.c	  2128   */
; ..\eeprom\NvM\NvM_Act.c	  2129  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActUpdateNvState(void)
; Function NvM_ActUpdateNvState
.L211:
NvM_ActUpdateNvState:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2130  {
; ..\eeprom\NvM\NvM_Act.c	  2131      uint8 NvBlockState_u8;
; ..\eeprom\NvM\NvM_Act.c	  2132      
; ..\eeprom\NvM\NvM_Act.c	  2133      switch (NvM_CurrentBlockInfo_t.LastResult_t)
	fcall	.cocofun_14
.L1238:
	ld.bu	d15,[a15]40
.L1239:

; ..\eeprom\NvM\NvM_Act.c	  2134      {
; ..\eeprom\NvM\NvM_Act.c	  2135          case (NVM_REQ_OK):
	jeq	d15,#0,.L73
.L1240:

; ..\eeprom\NvM\NvM_Act.c	  2136              /* set NvBlock state uptodate */
; ..\eeprom\NvM\NvM_Act.c	  2137              NvBlockState_u8 = NVM_NVBLOCK_STATE_UPTODATE;
; ..\eeprom\NvM\NvM_Act.c	  2138              break;
; ..\eeprom\NvM\NvM_Act.c	  2139  
; ..\eeprom\NvM\NvM_Act.c	  2140          case (NVM_REQ_NV_INVALIDATED):
	jeq	d15,#5,.L74
.L1241:
	j	.L75
.L73:
	mov	d4,#0
	j	.L76
.L74:

; ..\eeprom\NvM\NvM_Act.c	  2141              /* set NvBlock state outdated */
; ..\eeprom\NvM\NvM_Act.c	  2142              NvBlockState_u8 = NVM_NVBLOCK_STATE_OUTDATED;
; ..\eeprom\NvM\NvM_Act.c	  2143              break;
	mov	d4,#1
	j	.L77

; ..\eeprom\NvM\NvM_Act.c	  2144  
; ..\eeprom\NvM\NvM_Act.c	  2145          default:
.L75:

; ..\eeprom\NvM\NvM_Act.c	  2146              /* set NvBlock state defect */
; ..\eeprom\NvM\NvM_Act.c	  2147              NvBlockState_u8 = NVM_NVBLOCK_STATE_DEFECT;
	mov	d4,#2

; ..\eeprom\NvM\NvM_Act.c	  2148              break;
; ..\eeprom\NvM\NvM_Act.c	  2149      }
; ..\eeprom\NvM\NvM_Act.c	  2150  
; ..\eeprom\NvM\NvM_Act.c	  2151      /* save NvBlock state */
; ..\eeprom\NvM\NvM_Act.c	  2152      NvM_IntCreateNvState(&NvM_CurrentBlockInfo_t.NvState_u8, NvBlockState_u8); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
.L77:
.L76:
	lea	a15,[a15]43
.L1242:
	mov.aa	a4,a15
	call	NvM_IntCreateNvState
.L737:

; ..\eeprom\NvM\NvM_Act.c	  2153  
; ..\eeprom\NvM\NvM_Act.c	  2154      /* mark the other NvBlock active */
; ..\eeprom\NvM\NvM_Act.c	  2155      NvM_CurrentBlockInfo_t.NvState_u8 ^= NVM_NVBLOCK_STATE_SEC_ACTIVE;
	ld.bu	d15,[a15]
.L1243:
	xor	d15,d15,#128
	st.b	[a15],d15
.L1244:

; ..\eeprom\NvM\NvM_Act.c	  2156  }
	ret
.L661:
	
__NvM_ActUpdateNvState_function_end:
	.size	NvM_ActUpdateNvState,__NvM_ActUpdateNvState_function_end-NvM_ActUpdateNvState
.L430:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_IntCreateNvState',code,cluster('NvM_IntCreateNvState')
	.sect	'.text.NvM_Act.NvM_IntCreateNvState'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2157  
; ..\eeprom\NvM\NvM_Act.c	  2158  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2159  *  NvM_IntCreateNvState
; ..\eeprom\NvM\NvM_Act.c	  2160  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2161  /*!
; ..\eeprom\NvM\NvM_Act.c	  2162   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2163   *
; ..\eeprom\NvM\NvM_Act.c	  2164   *
; ..\eeprom\NvM\NvM_Act.c	  2165   *
; ..\eeprom\NvM\NvM_Act.c	  2166   */
; ..\eeprom\NvM\NvM_Act.c	  2167  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_IntCreateNvState(P2VAR(uint8, AUTOMATIC, NVM_PRIVATE_DATA) NvState, uint8 NewState)
; Function NvM_IntCreateNvState
.L213:
NvM_IntCreateNvState:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2168  {
; ..\eeprom\NvM\NvM_Act.c	  2169      uint8 shift;
; ..\eeprom\NvM\NvM_Act.c	  2170  
; ..\eeprom\NvM\NvM_Act.c	  2171      /* determine shift factor */
; ..\eeprom\NvM\NvM_Act.c	  2172      if((*NvState & NVM_NVBLOCK_STATE_SEC_ACTIVE) != 0u)
	ld.bu	d0,[a4]
.L1371:

; ..\eeprom\NvM\NvM_Act.c	  2173      {
; ..\eeprom\NvM\NvM_Act.c	  2174          shift = NVM_SEC_NVBLOCK_STATE_SHIFT;        
; ..\eeprom\NvM\NvM_Act.c	  2175      }
; ..\eeprom\NvM\NvM_Act.c	  2176      else
; ..\eeprom\NvM\NvM_Act.c	  2177      {
; ..\eeprom\NvM\NvM_Act.c	  2178          shift = NVM_PRI_NVBLOCK_STATE_SHIFT;        
; ..\eeprom\NvM\NvM_Act.c	  2179      }
; ..\eeprom\NvM\NvM_Act.c	  2180  
; ..\eeprom\NvM\NvM_Act.c	  2181      /* #10 set state of current NvBlock to 0x00 */
; ..\eeprom\NvM\NvM_Act.c	  2182      *NvState &= (NVM_NVBLOCK_STATE_UNKNOWN << shift) ^ 0xFFu; /* SBSW_NvM_Access_CurrBlockInfo */
	mov	d1,#3
.L1372:
	and	d15,d0,#128
	eq	d15,d15,#0
.L1373:
	sha	d15,#1
.L739:
	add	d15,#3
.L1374:
	sh	d1,d1,d15
.L1375:
	xor	d1,d1,#255
.L1376:
	and	d0,d1
.L1377:

; ..\eeprom\NvM\NvM_Act.c	  2183  
; ..\eeprom\NvM\NvM_Act.c	  2184      /* #20 set new state */
; ..\eeprom\NvM\NvM_Act.c	  2185      *NvState |= (uint8)(NewState << shift); /* SBSW_NvM_Access_CurrBlockInfo */
	sha	d4,d4,d15
.L738:

; ..\eeprom\NvM\NvM_Act.c	  2186  }
	or	d0,d4
	st.b	[a4],d0
	ret
.L693:
	
__NvM_IntCreateNvState_function_end:
	.size	NvM_IntCreateNvState,__NvM_IntCreateNvState_function_end-NvM_IntCreateNvState
.L495:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActGetMultiBlockJob',code,cluster('NvM_ActGetMultiBlockJob')
	.sect	'.text.NvM_Act.NvM_ActGetMultiBlockJob'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2187  
; ..\eeprom\NvM\NvM_Act.c	  2188  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2189  *  NvM_ActGetMultiBlockJob
; ..\eeprom\NvM\NvM_Act.c	  2190  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2191  /*!
; ..\eeprom\NvM\NvM_Act.c	  2192   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2193   *
; ..\eeprom\NvM\NvM_Act.c	  2194   *
; ..\eeprom\NvM\NvM_Act.c	  2195   *
; ..\eeprom\NvM\NvM_Act.c	  2196   *
; ..\eeprom\NvM\NvM_Act.c	  2197   *
; ..\eeprom\NvM\NvM_Act.c	  2198   *
; ..\eeprom\NvM\NvM_Act.c	  2199   *
; ..\eeprom\NvM\NvM_Act.c	  2200   */
; ..\eeprom\NvM\NvM_Act.c	  2201  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActGetMultiBlockJob(void)
; Function NvM_ActGetMultiBlockJob
.L215:
NvM_ActGetMultiBlockJob:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2202   {
; ..\eeprom\NvM\NvM_Act.c	  2203      const uint8 multiJobFlags = NvM_ApiFlags_u8;
	movh.a	a15,#@his(NvM_ApiFlags_u8)
	ld.bu	d15,[a15]@los(NvM_ApiFlags_u8)
.L740:

; ..\eeprom\NvM\NvM_Act.c	  2204  
; ..\eeprom\NvM\NvM_Act.c	  2205      NvM_CurrentJob_t.JobBlockId_t   = 0x00u;
; ..\eeprom\NvM\NvM_Act.c	  2206      NvM_CurrentJob_t.RamAddr_t      = NULL_PTR; /* this is essential for further processing! */
	mov	d0,#0
	movh.a	a2,#@his(NvM_CurrentJob_t)
	lea	a2,[a2]@los(NvM_CurrentJob_t)
.L1289:
	mov.a	a15,#0
.L1290:
	st.a	[a2]4,a15
.L1291:

; ..\eeprom\NvM\NvM_Act.c	  2207  
; ..\eeprom\NvM\NvM_Act.c	  2208      if ((multiJobFlags & NVM_APIFLAG_WRITE_ALL_SET) != 0u)
; ..\eeprom\NvM\NvM_Act.c	  2209      {
; ..\eeprom\NvM\NvM_Act.c	  2210          NvM_CurrentJob_t.JobServiceId_t = NVM_INT_FID_WRITE_ALL;
	mov.aa	a15,a2
	ld.bu	d1,[+a15]2
.L1292:
	jz.t	d15:1,.L78
.L1293:

; ..\eeprom\NvM\NvM_Act.c	  2211          /* WriteAll: begin with last block and end with configuration block */
; ..\eeprom\NvM\NvM_Act.c	  2212          NvM_CurrentJob_t.JobBlockId_t = (NvM_NoOfBlockIds_t - 1u);
	movh.a	a4,#@his(NvM_NoOfBlockIds_t)
	ld.hu	d15,[a4]@los(NvM_NoOfBlockIds_t)
.L741:
	mov	d1,#5
.L1294:
	add	d15,#-1
.L1295:
	extr.u	d0,d15,#0,#16
	j	.L79
.L78:

; ..\eeprom\NvM\NvM_Act.c	  2213      }
; ..\eeprom\NvM\NvM_Act.c	  2214      else if ((multiJobFlags & NVM_APIFLAG_READ_ALL_SET) != 0u) /* COV_NVM_MISRA */
	jz.t	d15:2,.L80
.L1296:

; ..\eeprom\NvM\NvM_Act.c	  2215      {
; ..\eeprom\NvM\NvM_Act.c	  2216          NvM_CurrentJob_t.JobServiceId_t = NVM_INT_FID_READ_ALL;
	mov	d1,#6
.L1297:

; ..\eeprom\NvM\NvM_Act.c	  2217          /* ReadAll: begin with configuration block and end with last block */
; ..\eeprom\NvM\NvM_Act.c	  2218          NvM_CurrentJob_t.JobBlockId_t = 1u;
	mov	d0,#1
.L80:
.L79:

; ..\eeprom\NvM\NvM_Act.c	  2219      }
; ..\eeprom\NvM\NvM_Act.c	  2220  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Act.c	  2221      else if ((multiJobFlags & NVM_APIFLAG_REPAIR_REDUNDANT_BLOCKS_SET) != 0u) /* COV_NVM_MISRA */
; ..\eeprom\NvM\NvM_Act.c	  2222      {
; ..\eeprom\NvM\NvM_Act.c	  2223          NvM_CurrentJob_t.JobServiceId_t = NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS;
; ..\eeprom\NvM\NvM_Act.c	  2224      }
; ..\eeprom\NvM\NvM_Act.c	  2225  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Act.c	  2226      else
; ..\eeprom\NvM\NvM_Act.c	  2227      {
; ..\eeprom\NvM\NvM_Act.c	  2228          /* Nothing to do, should not happen, since this function will be called, if
; ..\eeprom\NvM\NvM_Act.c	  2229           * NvM_QryHighMultiJob returned true --> one flag must be set */
; ..\eeprom\NvM\NvM_Act.c	  2230      }
; ..\eeprom\NvM\NvM_Act.c	  2231   }
	st.b	[a15],d1
	st.h	[a2],d0
	ret
.L669:
	
__NvM_ActGetMultiBlockJob_function_end:
	.size	NvM_ActGetMultiBlockJob,__NvM_ActGetMultiBlockJob_function_end-NvM_ActGetMultiBlockJob
.L465:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActTestBlockBlank',code,cluster('NvM_ActTestBlockBlank')
	.sect	'.text.NvM_Act.NvM_ActTestBlockBlank'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2232  
; ..\eeprom\NvM\NvM_Act.c	  2233  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2234  *  NvM_ActTestBlockBlank
; ..\eeprom\NvM\NvM_Act.c	  2235  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2236  /*!
; ..\eeprom\NvM\NvM_Act.c	  2237   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2238   *
; ..\eeprom\NvM\NvM_Act.c	  2239   *
; ..\eeprom\NvM\NvM_Act.c	  2240   *
; ..\eeprom\NvM\NvM_Act.c	  2241   */
; ..\eeprom\NvM\NvM_Act.c	  2242  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActTestBlockBlank(void)
; Function NvM_ActTestBlockBlank
.L217:
NvM_ActTestBlockBlank:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2243  {
; ..\eeprom\NvM\NvM_Act.c	  2244      /* read one byte of the block to a temp buffer to see if the nv
; ..\eeprom\NvM\NvM_Act.c	  2245       * is still empty. 
; ..\eeprom\NvM\NvM_Act.c	  2246       */
; ..\eeprom\NvM\NvM_Act.c	  2247      if(E_OK == MemIf_Read((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8, /* SBSW_NvM_FuncCall_PtrParam_MemIf */
; ..\eeprom\NvM\NvM_Act.c	  2248          NvM_CurrentBlockInfo_t.NvIdentifier_u16, 0u, &NvM_TestBuffer_u8, 1u))
	fcall	.cocofun_1
.L1202:
	mov	d6,#0
	movh.a	a4,#@his(NvM_TestBuffer_u8)
	lea	a4,[a4]@los(NvM_TestBuffer_u8)
.L1203:
	mov	d7,#1
	call	MemIf_Read
.L1204:
	fcall	.cocofun_5
.L1205:

; ..\eeprom\NvM\NvM_Act.c	  2249      {
; ..\eeprom\NvM\NvM_Act.c	  2250          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
; ..\eeprom\NvM\NvM_Act.c	  2251      }
; ..\eeprom\NvM\NvM_Act.c	  2252      else
; ..\eeprom\NvM\NvM_Act.c	  2253      {
; ..\eeprom\NvM\NvM_Act.c	  2254          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Act.c	  2255      }
; ..\eeprom\NvM\NvM_Act.c	  2256  }
	ret
.L657:
	
__NvM_ActTestBlockBlank_function_end:
	.size	NvM_ActTestBlockBlank,__NvM_ActTestBlockBlank_function_end-NvM_ActTestBlockBlank
.L410:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActValidateRam',code,cluster('NvM_ActValidateRam')
	.sect	'.text.NvM_Act.NvM_ActValidateRam'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2257  
; ..\eeprom\NvM\NvM_Act.c	  2258  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2259  *  NvM_ActValidateRam
; ..\eeprom\NvM\NvM_Act.c	  2260  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2261  /*!
; ..\eeprom\NvM\NvM_Act.c	  2262   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2263   *
; ..\eeprom\NvM\NvM_Act.c	  2264   *
; ..\eeprom\NvM\NvM_Act.c	  2265   *
; ..\eeprom\NvM\NvM_Act.c	  2266   */
; ..\eeprom\NvM\NvM_Act.c	  2267  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActValidateRam(void)
; Function NvM_ActValidateRam
.L219:
NvM_ActValidateRam:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2268  {
; ..\eeprom\NvM\NvM_Act.c	  2269      /* Are we operating on the permanent RAM block? (NULL_PTR implies permanent RAM block or Mirror Cbk!) */
; ..\eeprom\NvM\NvM_Act.c	  2270      if(NvM_CurrentJob_t.RamAddr_t == NULL_PTR) /* COV_NVM_APICFGCLASS */
	fcall	.cocofun_13
.L1210:
	jne	d15,#0,.L81
.L1211:

; ..\eeprom\NvM\NvM_Act.c	  2271      {
; ..\eeprom\NvM\NvM_Act.c	  2272  #if (NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  2273          /* only make valid, do not change the CHANGED flag) */
; ..\eeprom\NvM\NvM_Act.c	  2274          NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 |= NVM_STATE_VALID_SET; /* SBSW_NvM_Access_CurrBlockInfo */
	fcall	.cocofun_14
.L1212:
	ld.a	a2,[+a15]4
.L1213:
	ld.bu	d15,[+a2]2
.L1214:
	or	d15,#1
	st.b	[a2],d15
.L1215:

; ..\eeprom\NvM\NvM_Act.c	  2275          NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 &= NVM_STATE_CHANGED_CL; /* SBSW_NvM_Access_CurrBlockInfo */
	ld.a	a15,[a15]
.L1216:
	ld.bu	d15,[+a15]2
.L1217:
	and	d15,#253
	st.b	[a15],d15
.L81:

; ..\eeprom\NvM\NvM_Act.c	  2276  #else
; ..\eeprom\NvM\NvM_Act.c	  2277          /* make valid AND changed, because the application cannot do it. */
; ..\eeprom\NvM\NvM_Act.c	  2278          NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 |= (NVM_STATE_VALID_SET | NVM_STATE_CHANGED_SET); /* SBSW_NvM_Access_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  2279  #endif
; ..\eeprom\NvM\NvM_Act.c	  2280      }
; ..\eeprom\NvM\NvM_Act.c	  2281  }
	ret
.L658:
	
__NvM_ActValidateRam_function_end:
	.size	NvM_ActValidateRam,__NvM_ActValidateRam_function_end-NvM_ActValidateRam
.L415:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActWait',code,cluster('NvM_ActWait')
	.sect	'.text.NvM_Act.NvM_ActWait'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2282  
; ..\eeprom\NvM\NvM_Act.c	  2283  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2284  *  NvM_ActWait
; ..\eeprom\NvM\NvM_Act.c	  2285  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2286  /*!
; ..\eeprom\NvM\NvM_Act.c	  2287   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2288   *
; ..\eeprom\NvM\NvM_Act.c	  2289   *
; ..\eeprom\NvM\NvM_Act.c	  2290   */
; ..\eeprom\NvM\NvM_Act.c	  2291  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActWait(void)
; Function NvM_ActWait
.L221:
NvM_ActWait:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2292  {
; ..\eeprom\NvM\NvM_Act.c	  2293      NvM_CurrentBlockInfo_t.InternalFlags_u8 |= NVM_INTFLAG_WAIT_SET;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+42)
.L1278:
	ld.bu	d15,[a15]@los(NvM_CurrentBlockInfo_t+42)
.L1279:
	or	d15,#1
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+42),d15
.L1280:

; ..\eeprom\NvM\NvM_Act.c	  2294  }
	ret
.L667:
	
__NvM_ActWait_function_end:
	.size	NvM_ActWait,__NvM_ActWait_function_end-NvM_ActWait
.L455:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActCopyNvDataToBuf',code,cluster('NvM_ActCopyNvDataToBuf')
	.sect	'.text.NvM_Act.NvM_ActCopyNvDataToBuf'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2295  
; ..\eeprom\NvM\NvM_Act.c	  2296  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2297  *  NvM_ActCopyNvDataToBuf
; ..\eeprom\NvM\NvM_Act.c	  2298  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2299  /*!
; ..\eeprom\NvM\NvM_Act.c	  2300   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2301   *
; ..\eeprom\NvM\NvM_Act.c	  2302   *
; ..\eeprom\NvM\NvM_Act.c	  2303   *
; ..\eeprom\NvM\NvM_Act.c	  2304   *
; ..\eeprom\NvM\NvM_Act.c	  2305   *
; ..\eeprom\NvM\NvM_Act.c	  2306   */
; ..\eeprom\NvM\NvM_Act.c	  2307  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActCopyNvDataToBuf(void)
; Function NvM_ActCopyNvDataToBuf
.L223:
NvM_ActCopyNvDataToBuf:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2308  {
; ..\eeprom\NvM\NvM_Act.c	  2309  #if(NVM_CRC_INT_BUFFER == STD_ON) /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Act.c	  2310      /* Pointer in BlockCrcJob_t points to internal buffer so that Crc is calculated later about data in internal buffer. */
; ..\eeprom\NvM\NvM_Act.c	  2311      const NvM_BlockDescrPtrType descr_pt = NvM_CurrentBlockInfo_t.Descriptor_pt;  
	movh.a	a12,#@his(NvM_CurrentBlockInfo_t)
	lea	a12,[a12]@los(NvM_CurrentBlockInfo_t)
.L1302:
	ld.a	a15,[a12]
.L1303:

; ..\eeprom\NvM\NvM_Act.c	  2312  
; ..\eeprom\NvM\NvM_Act.c	  2313      /* #10 Explicit synchronization enabled -> get data to write from user. */
; ..\eeprom\NvM\NvM_Act.c	  2314      if((descr_pt->CbkSetMirrorFunc_pt != NULL_PTR) && (NvM_CurrentJob_t.RamAddr_t == NULL_PTR)) /* COV_NVM_APICFGCLASS */
	ld.a	a2,[a15]28
.L1304:
	jz.a	a2,.L82
.L1305:
	movh.a	a4,#@his(NvM_CurrentJob_t+4)
.L1306:
	ld.a	a4,[a4]@los(NvM_CurrentJob_t+4)
.L1307:
	jnz.a	a4,.L83
.L1308:

; ..\eeprom\NvM\NvM_Act.c	  2315      {
; ..\eeprom\NvM\NvM_Act.c	  2316          /* PRQA S 0315 1 */ /* MD_NvM_Dir1.1_CastToVoidPtr */
; ..\eeprom\NvM\NvM_Act.c	  2317          if(descr_pt->CbkSetMirrorFunc_pt(NvM_InternalBuffer_au8) == E_OK) /* SBSW_NvM_FuncPtrCall_UserCallbacks */
	movh.a	a4,#@his(NvM_InternalBuffer_au8)
	lea	a4,[a4]@los(NvM_InternalBuffer_au8)
	calli	a2
.L1309:
	jne	d2,#0,.L84
.L1310:
	j	.L85
.L83:
.L82:

; ..\eeprom\NvM\NvM_Act.c	  2318          {
; ..\eeprom\NvM\NvM_Act.c	  2319              NvM_CurrentBlockInfo_t.ByteCount_u16 = 0u;
; ..\eeprom\NvM\NvM_Act.c	  2320          }
; ..\eeprom\NvM\NvM_Act.c	  2321      }
; ..\eeprom\NvM\NvM_Act.c	  2322      /* #20 No explicit synchronization but CRC enabled -> copy the RAM data to the internal buffer. */
; ..\eeprom\NvM\NvM_Act.c	  2323      else if(descr_pt->CrcSettings > NVM_BLOCK_USE_CRC_OFF)
	ld.bu	d15,[a15]58
	extr.u	d15,d15,#6,#2
.L1311:
	jeq	d15,#0,.L86
.L673:

; ..\eeprom\NvM\NvM_Act.c	  2324      {
; ..\eeprom\NvM\NvM_Act.c	  2325          const NvM_RamAddressType ramAddr = (NvM_CurrentJob_t.RamAddr_t != NULL_PTR) ?
	movh.a	a2,#@his(NvM_CurrentJob_t+4)
.L1312:
	ld.a	a6,[a2]@los(NvM_CurrentJob_t+4)
.L1313:
	jnz.a	a6,.L88
.L1314:

; ..\eeprom\NvM\NvM_Act.c	  2326              NvM_CurrentJob_t.RamAddr_t : NvM_CurrentBlockInfo_t.Descriptor_pt->RamBlockDataAddr_t; /* COV_NVM_APICFGCLASS */
	ld.a	a6,[a15]
.L88:

; ..\eeprom\NvM\NvM_Act.c	  2327  
; ..\eeprom\NvM\NvM_Act.c	  2328          NvM_InternalCopyData(&NvM_CurrentBlockInfo_t,  NvM_CurrentBlockInfo_t.RamAddr_t, ramAddr); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	ld.a	a5,[a12]8
.L1315:
	mov.aa	a4,a12
	call	NvM_InternalCopyData
.L674:
	j	.L89
.L86:
.L85:

; ..\eeprom\NvM\NvM_Act.c	  2329      }
; ..\eeprom\NvM\NvM_Act.c	  2330      /* #30 No explicit synchronization and no CRC, nothing to do. */
; ..\eeprom\NvM\NvM_Act.c	  2331      else
; ..\eeprom\NvM\NvM_Act.c	  2332      {
; ..\eeprom\NvM\NvM_Act.c	  2333          NvM_CurrentBlockInfo_t.ByteCount_u16 = 0u;
	mov	d15,#0
	st.h	[a12]38,d15
.L89:
.L84:

; ..\eeprom\NvM\NvM_Act.c	  2334      }
; ..\eeprom\NvM\NvM_Act.c	  2335  #endif /* ECO_IGNORE_LINE */
; ..\eeprom\NvM\NvM_Act.c	  2336      /* #40 Data preparation done and pre write transformation enabled -> pass the data to the transformation callback. */
; ..\eeprom\NvM\NvM_Act.c	  2337      if(
; ..\eeprom\NvM\NvM_Act.c	  2338  #if(NVM_CRC_INT_BUFFER == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  2339          /* ByteCount not relevant in case the internal buffers are disabled -> no copy */
; ..\eeprom\NvM\NvM_Act.c	  2340          (NvM_CurrentBlockInfo_t.ByteCount_u16 == 0u) &&
	ld.hu	d15,[a12]38
.L1316:
	jne	d15,#0,.L90
.L1317:

; ..\eeprom\NvM\NvM_Act.c	  2341  #endif
; ..\eeprom\NvM\NvM_Act.c	  2342              (NvM_CurrentBlockInfo_t.Descriptor_pt->CbkPreWriteTransform != NULL_PTR))
	ld.a	a15,[a12]
.L1318:
	ld.a	a2,[a15]32
.L1319:
	jz.a	a2,.L91
.L1320:

; ..\eeprom\NvM\NvM_Act.c	  2343      {
; ..\eeprom\NvM\NvM_Act.c	  2344          NvM_CurrentBlockInfo_t.Descriptor_pt->CbkPreWriteTransform(NvM_CurrentJob_t.JobBlockId_t, /* SBSW_NvM_FuncPtrCall_UserCallbacks */
	movh.a	a4,#@his(NvM_CurrentJob_t)
.L1321:
	ld.hu	d4,[a4]@los(NvM_CurrentJob_t)
.L1322:

; ..\eeprom\NvM\NvM_Act.c	  2345              /* PRQA S 0315 1 */ /* MD_NvM_Dir1.1_CastToVoidPtr */
; ..\eeprom\NvM\NvM_Act.c	  2346              NvM_CurrentBlockInfo_t.RamAddr_t, NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16);
	ld.a	a4,[a12]8
.L1323:
	ld.hu	d5,[a15]50
	ji	a2
.L91:
.L90:

; ..\eeprom\NvM\NvM_Act.c	  2347      }
; ..\eeprom\NvM\NvM_Act.c	  2348  }
	ret
.L672:
	
__NvM_ActCopyNvDataToBuf_function_end:
	.size	NvM_ActCopyNvDataToBuf,__NvM_ActCopyNvDataToBuf_function_end-NvM_ActCopyNvDataToBuf
.L470:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActCancelNV',code,cluster('NvM_ActCancelNV')
	.sect	'.text.NvM_Act.NvM_ActCancelNV'
	.align	2
	
	.global	NvM_ActCancelNV

; ..\eeprom\NvM\NvM_Act.c	  2349  
; ..\eeprom\NvM\NvM_Act.c	  2350  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2351   * NvM_ActCancelNV
; ..\eeprom\NvM\NvM_Act.c	  2352   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2353  /*!
; ..\eeprom\NvM\NvM_Act.c	  2354   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2355   *
; ..\eeprom\NvM\NvM_Act.c	  2356   *
; ..\eeprom\NvM\NvM_Act.c	  2357   *
; ..\eeprom\NvM\NvM_Act.c	  2358   */
; ..\eeprom\NvM\NvM_Act.c	  2359  FUNC(void, NVM_PRIVATE_CODE) NvM_ActCancelNV(void)
; Function NvM_ActCancelNV
.L225:
NvM_ActCancelNV:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2360  {
; ..\eeprom\NvM\NvM_Act.c	  2361      /* Hint: the BUSY check is necessary because canceling in idle state leads to a DET! */
; ..\eeprom\NvM\NvM_Act.c	  2362      if(MemIf_GetStatus((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8) == MEMIF_BUSY)
	fcall	.cocofun_10
.L765:
	call	MemIf_GetStatus
.L766:
	jne	d2,#2,.L92
.L767:

; ..\eeprom\NvM\NvM_Act.c	  2363      {
; ..\eeprom\NvM\NvM_Act.c	  2364          MemIf_Cancel((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8);
	fcall	.cocofun_10
.L768:
	j	MemIf_Cancel
.L92:

; ..\eeprom\NvM\NvM_Act.c	  2365      }
; ..\eeprom\NvM\NvM_Act.c	  2366  }
	ret
.L580:
	
__NvM_ActCancelNV_function_end:
	.size	NvM_ActCancelNV,__NvM_ActCancelNV_function_end-NvM_ActCancelNV
.L250:
	; End of function
	
	.sdecl	'.text.NvM_Act..cocofun_10',code,cluster('.cocofun_10')
	.sect	'.text.NvM_Act..cocofun_10'
	.align	2
; Function .cocofun_10
.L227:
.cocofun_10:	.type	func
; Function body .cocofun_10, coco_iter:0
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t)
.L1462:
	ld.a	a15,[a15]@los(NvM_CurrentBlockInfo_t)
.L1463:
	ld.bu	d15,[a15]58
.L1464:
	and	d4,d15,#15
	fret
.L555:
	; End of function
	.sdecl	'.text.NvM_Act.NvM_ActKillSubFsm',code,cluster('NvM_ActKillSubFsm')
	.sect	'.text.NvM_Act.NvM_ActKillSubFsm'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2367  
; ..\eeprom\NvM\NvM_Act.c	  2368  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2369   * NvM_ActKillSubFsm
; ..\eeprom\NvM\NvM_Act.c	  2370   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2371  /*!
; ..\eeprom\NvM\NvM_Act.c	  2372   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2373   *
; ..\eeprom\NvM\NvM_Act.c	  2374   *
; ..\eeprom\NvM\NvM_Act.c	  2375   */
; ..\eeprom\NvM\NvM_Act.c	  2376  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActKillSubFsm(void)
; Function NvM_ActKillSubFsm
.L229:
NvM_ActKillSubFsm:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2377  {
; ..\eeprom\NvM\NvM_Act.c	  2378      NvM_JobSubState_t = NVM_STATE_FSM_FINISHED;
	movh.a	a15,#@his(NvM_JobSubState_t)
.L1328:
	mov	d15,#32
	st.b	[a15]@los(NvM_JobSubState_t),d15
.L1329:

; ..\eeprom\NvM\NvM_Act.c	  2379  }
	ret
.L675:
	
__NvM_ActKillSubFsm_function_end:
	.size	NvM_ActKillSubFsm,__NvM_ActKillSubFsm_function_end-NvM_ActKillSubFsm
.L475:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_ActProcessCrc',code,cluster('NvM_ActProcessCrc')
	.sect	'.text.NvM_Act.NvM_ActProcessCrc'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2380  
; ..\eeprom\NvM\NvM_Act.c	  2381  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  2382  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2383   * NvM_ActRepairRedBlocksInit
; ..\eeprom\NvM\NvM_Act.c	  2384   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2385  /*!
; ..\eeprom\NvM\NvM_Act.c	  2386   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2387   *
; ..\eeprom\NvM\NvM_Act.c	  2388   *
; ..\eeprom\NvM\NvM_Act.c	  2389   *
; ..\eeprom\NvM\NvM_Act.c	  2390   */
; ..\eeprom\NvM\NvM_Act.c	  2391  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksInit(void)
; ..\eeprom\NvM\NvM_Act.c	  2392  {
; ..\eeprom\NvM\NvM_Act.c	  2393      /* use the latest suspended block id or the configuration block to begin repairing with */
; ..\eeprom\NvM\NvM_Act.c	  2394      NvM_CurrentJob_t.JobBlockId_t = (NvM_RepairRedBlockState.CurrentBlockId == 0u) ? 1u : NvM_RepairRedBlockState.CurrentBlockId;
; ..\eeprom\NvM\NvM_Act.c	  2395  
; ..\eeprom\NvM\NvM_Act.c	  2396      NVM_ActRepairRedBlocksInitBlock();
; ..\eeprom\NvM\NvM_Act.c	  2397  }
; ..\eeprom\NvM\NvM_Act.c	  2398  
; ..\eeprom\NvM\NvM_Act.c	  2399  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2400   * NvM_ActRepairRedBlocksInitNext
; ..\eeprom\NvM\NvM_Act.c	  2401   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2402  /*!
; ..\eeprom\NvM\NvM_Act.c	  2403   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2404   *
; ..\eeprom\NvM\NvM_Act.c	  2405   *
; ..\eeprom\NvM\NvM_Act.c	  2406   *
; ..\eeprom\NvM\NvM_Act.c	  2407   */
; ..\eeprom\NvM\NvM_Act.c	  2408  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksInitNext(void)
; ..\eeprom\NvM\NvM_Act.c	  2409  {
; ..\eeprom\NvM\NvM_Act.c	  2410      /* search to last block for the next redundant block */
; ..\eeprom\NvM\NvM_Act.c	  2411      do
; ..\eeprom\NvM\NvM_Act.c	  2412      {
; ..\eeprom\NvM\NvM_Act.c	  2413          NvM_CurrentJob_t.JobBlockId_t++;
; ..\eeprom\NvM\NvM_Act.c	  2414      }while((NvM_CurrentJob_t.JobBlockId_t < NvM_NoOfBlockIds_t) &&
; ..\eeprom\NvM\NvM_Act.c	  2415          (NvM_BlockDescriptorTable_at[NvM_CurrentJob_t.JobBlockId_t].MngmtType_t != NVM_BLOCK_REDUNDANT));
; ..\eeprom\NvM\NvM_Act.c	  2416  
; ..\eeprom\NvM\NvM_Act.c	  2417      /* only initialize the block in case it exists */
; ..\eeprom\NvM\NvM_Act.c	  2418      if(NvM_CurrentJob_t.JobBlockId_t < NvM_NoOfBlockIds_t)
; ..\eeprom\NvM\NvM_Act.c	  2419      {
; ..\eeprom\NvM\NvM_Act.c	  2420          NVM_ActRepairRedBlocksInitBlock();
; ..\eeprom\NvM\NvM_Act.c	  2421      }
; ..\eeprom\NvM\NvM_Act.c	  2422  }
; ..\eeprom\NvM\NvM_Act.c	  2423  
; ..\eeprom\NvM\NvM_Act.c	  2424  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2425   * NVM_ActRepairRedBlocksInitBlock
; ..\eeprom\NvM\NvM_Act.c	  2426   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2427   /*!
; ..\eeprom\NvM\NvM_Act.c	  2428   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2429   *
; ..\eeprom\NvM\NvM_Act.c	  2430   *
; ..\eeprom\NvM\NvM_Act.c	  2431   *
; ..\eeprom\NvM\NvM_Act.c	  2432   *
; ..\eeprom\NvM\NvM_Act.c	  2433   */
; ..\eeprom\NvM\NvM_Act.c	  2434  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NVM_ActRepairRedBlocksInitBlock(void)
; ..\eeprom\NvM\NvM_Act.c	  2435  {
; ..\eeprom\NvM\NvM_Act.c	  2436      const NvM_BlockIdType orgBlockId = NVM_BLOCK_FROM_DCM_ID(NvM_CurrentJob_t.JobBlockId_t);
; ..\eeprom\NvM\NvM_Act.c	  2437      const NvM_BlockDescrPtrType DescrPtr = &NvM_BlockDescriptorTable_at[orgBlockId];
; ..\eeprom\NvM\NvM_Act.c	  2438      /* #10 Store current block's information. */
; ..\eeprom\NvM\NvM_Act.c	  2439      NvM_CurrentBlockInfo_t.Mngmt_pt = &NvM_BlockMngmtArea_at[orgBlockId];
; ..\eeprom\NvM\NvM_Act.c	  2440      NvM_CurrentBlockInfo_t.Descriptor_pt = DescrPtr;
; ..\eeprom\NvM\NvM_Act.c	  2441      NvM_CurrentBlockInfo_t.NvIdentifier_u16 = DescrPtr->NvIdentifier_u16;
; ..\eeprom\NvM\NvM_Act.c	  2442      NvM_CurrentBlockInfo_t.ByteCount_u16 = 0u;
; ..\eeprom\NvM\NvM_Act.c	  2443      /* Use:
; ..\eeprom\NvM\NvM_Act.c	  2444       * CipheredDataBuffer: if data ciphering is enabled and the block stores ciphered data,
; ..\eeprom\NvM\NvM_Act.c	  2445       * InternalBuffer: if data ciphering is disabled or the block stores plain data.
; ..\eeprom\NvM\NvM_Act.c	  2446       * This ensures the used buffer is large enough to store the current block (ciphered data
; ..\eeprom\NvM\NvM_Act.c	  2447       * may be > or < internal buffer length!).
; ..\eeprom\NvM\NvM_Act.c	  2448       * The RamAddr will never be used! */
; ..\eeprom\NvM\NvM_Act.c	  2449  #if(NVM_USE_CSM == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  2450      if(NvM_QryIsCipherBlock(NvM_CurrentBlockInfo_t.Descriptor_pt) == TRUE) /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  2451      {
; ..\eeprom\NvM\NvM_Act.c	  2452          NvM_CurrentBlockInfo_t.NvRamAddr_t = NvM_CipheredDataBuffer;
; ..\eeprom\NvM\NvM_Act.c	  2453          NvM_CurrentBlockInfo_t.RamAddr_t = NvM_CipheredDataBuffer;
; ..\eeprom\NvM\NvM_Act.c	  2454      }
; ..\eeprom\NvM\NvM_Act.c	  2455      else
; ..\eeprom\NvM\NvM_Act.c	  2456  #endif
; ..\eeprom\NvM\NvM_Act.c	  2457      {
; ..\eeprom\NvM\NvM_Act.c	  2458          NvM_CurrentBlockInfo_t.NvRamAddr_t = NvM_InternalBuffer_au8;
; ..\eeprom\NvM\NvM_Act.c	  2459          NvM_CurrentBlockInfo_t.RamAddr_t = NvM_InternalBuffer_au8;
; ..\eeprom\NvM\NvM_Act.c	  2460      }
; ..\eeprom\NvM\NvM_Act.c	  2461      /* #30 Setup repair redundant block job. */
; ..\eeprom\NvM\NvM_Act.c	  2462      NvM_RepairRedBlockState.CurrentBlockId = NvM_CurrentJob_t.JobBlockId_t;
; ..\eeprom\NvM\NvM_Act.c	  2463      NvM_RepairRedBlockState.NvBlockState = NVM_NVBLOCK_STATE_UPTODATE;
; ..\eeprom\NvM\NvM_Act.c	  2464      NvM_RepairRedBlockState.CrcBuffer = 0u;
; ..\eeprom\NvM\NvM_Act.c	  2465  }
; ..\eeprom\NvM\NvM_Act.c	  2466  
; ..\eeprom\NvM\NvM_Act.c	  2467  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2468   * NvM_ActRepairRedBlockReadCheck
; ..\eeprom\NvM\NvM_Act.c	  2469   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2470  /*!
; ..\eeprom\NvM\NvM_Act.c	  2471   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2472   *
; ..\eeprom\NvM\NvM_Act.c	  2473   *
; ..\eeprom\NvM\NvM_Act.c	  2474   *
; ..\eeprom\NvM\NvM_Act.c	  2475   *
; ..\eeprom\NvM\NvM_Act.c	  2476   *
; ..\eeprom\NvM\NvM_Act.c	  2477   *
; ..\eeprom\NvM\NvM_Act.c	  2478   *
; ..\eeprom\NvM\NvM_Act.c	  2479   *
; ..\eeprom\NvM\NvM_Act.c	  2480   */
; ..\eeprom\NvM\NvM_Act.c	  2481  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlockReadCheck(void)
; ..\eeprom\NvM\NvM_Act.c	  2482  {
; ..\eeprom\NvM\NvM_Act.c	  2483      const NvM_BlockDescrPtrType descr_pt = NvM_CurrentBlockInfo_t.Descriptor_pt;
; ..\eeprom\NvM\NvM_Act.c	  2484      uint16 blockLength;
; ..\eeprom\NvM\NvM_Act.c	  2485      NvM_RamAddressType readDest_pt;
; ..\eeprom\NvM\NvM_Act.c	  2486  
; ..\eeprom\NvM\NvM_Act.c	  2487      /* #10 for blocks without Crc */
; ..\eeprom\NvM\NvM_Act.c	  2488      if(descr_pt->CrcSettings == NVM_BLOCK_USE_CRC_OFF)
; ..\eeprom\NvM\NvM_Act.c	  2489      {
; ..\eeprom\NvM\NvM_Act.c	  2490          blockLength = 1;
; ..\eeprom\NvM\NvM_Act.c	  2491          readDest_pt = &NvM_TestBuffer_u8;
; ..\eeprom\NvM\NvM_Act.c	  2492      }
; ..\eeprom\NvM\NvM_Act.c	  2493      /* #20 for blocks with Crc */
; ..\eeprom\NvM\NvM_Act.c	  2494      else
; ..\eeprom\NvM\NvM_Act.c	  2495      {
; ..\eeprom\NvM\NvM_Act.c	  2496          /* #21 use internal buffer */
; ..\eeprom\NvM\NvM_Act.c	  2497          readDest_pt = NvM_CurrentBlockInfo_t.NvRamAddr_t;
; ..\eeprom\NvM\NvM_Act.c	  2498          /* #22 create Crc job */
; ..\eeprom\NvM\NvM_Act.c	  2499          NvM_CrcJob_Create(&NvM_CurrentBlockInfo_t.BlockCrcJob_t, NvM_CurrentJob_t.JobBlockId_t,
; ..\eeprom\NvM\NvM_Act.c	  2500              readDest_pt, NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockNVRAMDataLength); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  2501          /* NvM_CurrentBlockInfo_t.ByteCount_u16 = descr_pt->NvBlockLength_u16; */
; ..\eeprom\NvM\NvM_Act.c	  2502          /* #23 adjust local block length to read the whole data */
; ..\eeprom\NvM\NvM_Act.c	  2503          blockLength = descr_pt->NvBlockNVRAMDataLength + NvM_CrcJob_GetCrcLength(&NvM_CurrentBlockInfo_t.BlockCrcJob_t);
; ..\eeprom\NvM\NvM_Act.c	  2504      }
; ..\eeprom\NvM\NvM_Act.c	  2505  
; ..\eeprom\NvM\NvM_Act.c	  2506      /* #30 setup read job */
; ..\eeprom\NvM\NvM_Act.c	  2507      if (MemIf_Read((uint8)descr_pt->DeviceId_u8, NvM_CurrentBlockInfo_t.NvIdentifier_u16, 0u, readDest_pt, /* SBSW_NvM_FuncCall_PtrParam_MemIf */
; ..\eeprom\NvM\NvM_Act.c	  2508          (uint16)blockLength) == E_OK)
; ..\eeprom\NvM\NvM_Act.c	  2509      {
; ..\eeprom\NvM\NvM_Act.c	  2510          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
; ..\eeprom\NvM\NvM_Act.c	  2511      }
; ..\eeprom\NvM\NvM_Act.c	  2512      else
; ..\eeprom\NvM\NvM_Act.c	  2513      {
; ..\eeprom\NvM\NvM_Act.c	  2514          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Act.c	  2515      }
; ..\eeprom\NvM\NvM_Act.c	  2516  }
; ..\eeprom\NvM\NvM_Act.c	  2517  
; ..\eeprom\NvM\NvM_Act.c	  2518  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2519   * NvM_ActRepairRedBlockFinishReadCheck
; ..\eeprom\NvM\NvM_Act.c	  2520   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2521  /*!
; ..\eeprom\NvM\NvM_Act.c	  2522   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2523   *
; ..\eeprom\NvM\NvM_Act.c	  2524   *
; ..\eeprom\NvM\NvM_Act.c	  2525   *
; ..\eeprom\NvM\NvM_Act.c	  2526   *
; ..\eeprom\NvM\NvM_Act.c	  2527   *
; ..\eeprom\NvM\NvM_Act.c	  2528   *
; ..\eeprom\NvM\NvM_Act.c	  2529   */
; ..\eeprom\NvM\NvM_Act.c	  2530  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlockFinishReadCheck(void)
; ..\eeprom\NvM\NvM_Act.c	  2531  {
; ..\eeprom\NvM\NvM_Act.c	  2532      uint8 stateShift =
; ..\eeprom\NvM\NvM_Act.c	  2533          (NvM_CurrentBlockInfo_t.Descriptor_pt->NvIdentifier_u16 == NvM_CurrentBlockInfo_t.NvIdentifier_u16) ? 0u : 4u;
; ..\eeprom\NvM\NvM_Act.c	  2534  
; ..\eeprom\NvM\NvM_Act.c	  2535      boolean blockValid = FALSE;
; ..\eeprom\NvM\NvM_Act.c	  2536  
; ..\eeprom\NvM\NvM_Act.c	  2537      if(NvM_CurrentBlockInfo_t.LastResult_t == NVM_REQ_OK)
; ..\eeprom\NvM\NvM_Act.c	  2538      {
; ..\eeprom\NvM\NvM_Act.c	  2539          if(NvM_CurrentBlockInfo_t.Descriptor_pt->CrcSettings != NVM_BLOCK_USE_CRC_OFF)
; ..\eeprom\NvM\NvM_Act.c	  2540          {
; ..\eeprom\NvM\NvM_Act.c	  2541              /* block has Crc we have to check it to validate the block */
; ..\eeprom\NvM\NvM_Act.c	  2542              blockValid = NvM_CrcJob_Compare(&NvM_CurrentBlockInfo_t.BlockCrcJob_t); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
; ..\eeprom\NvM\NvM_Act.c	  2543  
; ..\eeprom\NvM\NvM_Act.c	  2544              /* store Crc only for the first block */
; ..\eeprom\NvM\NvM_Act.c	  2545              if(stateShift == 0u)
; ..\eeprom\NvM\NvM_Act.c	  2546              {
; ..\eeprom\NvM\NvM_Act.c	  2547                  /* copy Crc to job internal buffer to be used to compare with second block's Crc later */
; ..\eeprom\NvM\NvM_Act.c	  2548                  NvM_RepairRedBlockState.CrcBuffer = NvM_CurrentBlockInfo_t.BlockCrcJob_t.CurrentCrcValue;
; ..\eeprom\NvM\NvM_Act.c	  2549              }
; ..\eeprom\NvM\NvM_Act.c	  2550          }
; ..\eeprom\NvM\NvM_Act.c	  2551          else
; ..\eeprom\NvM\NvM_Act.c	  2552          {
; ..\eeprom\NvM\NvM_Act.c	  2553              /* no Crc to check, block readable == valid */
; ..\eeprom\NvM\NvM_Act.c	  2554              blockValid = TRUE;
; ..\eeprom\NvM\NvM_Act.c	  2555          }
; ..\eeprom\NvM\NvM_Act.c	  2556      }
; ..\eeprom\NvM\NvM_Act.c	  2557  
; ..\eeprom\NvM\NvM_Act.c	  2558      /* #20 set block to defect or valid depending on previous check result */
; ..\eeprom\NvM\NvM_Act.c	  2559      NvM_RepairRedBlockState.NvBlockState |= blockValid ?
; ..\eeprom\NvM\NvM_Act.c	  2560          (NVM_NVBLOCK_STATE_UPTODATE << stateShift) : (NVM_NVBLOCK_STATE_DEFECT << stateShift);
; ..\eeprom\NvM\NvM_Act.c	  2561  
; ..\eeprom\NvM\NvM_Act.c	  2562      /* #30 prepare job for next sub-block (important only for first sub-block to read the second one) */
; ..\eeprom\NvM\NvM_Act.c	  2563      NvM_CurrentBlockInfo_t.NvIdentifier_u16 |= 1u;
; ..\eeprom\NvM\NvM_Act.c	  2564  }
; ..\eeprom\NvM\NvM_Act.c	  2565  
; ..\eeprom\NvM\NvM_Act.c	  2566  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2567   * NvM_ActRepairRedBlocksReadValid
; ..\eeprom\NvM\NvM_Act.c	  2568   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2569  /*!
; ..\eeprom\NvM\NvM_Act.c	  2570   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2571   *
; ..\eeprom\NvM\NvM_Act.c	  2572   *
; ..\eeprom\NvM\NvM_Act.c	  2573   *
; ..\eeprom\NvM\NvM_Act.c	  2574   */
; ..\eeprom\NvM\NvM_Act.c	  2575  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksReadValid(void)
; ..\eeprom\NvM\NvM_Act.c	  2576  {
; ..\eeprom\NvM\NvM_Act.c	  2577      /* we do not have to check whether only one block is defect - this is done previously - this function
; ..\eeprom\NvM\NvM_Act.c	  2578       * shall not be invoked in case BOTH blocks are defect or valid!  */
; ..\eeprom\NvM\NvM_Act.c	  2579  
; ..\eeprom\NvM\NvM_Act.c	  2580      uint8 crcNrOfBytes = 0u;
; ..\eeprom\NvM\NvM_Act.c	  2581  
; ..\eeprom\NvM\NvM_Act.c	  2582      /* first block is defect, read second block */
; ..\eeprom\NvM\NvM_Act.c	  2583      if((NvM_RepairRedBlockState.NvBlockState & 0x0Fu) == NVM_NVBLOCK_STATE_DEFECT)
; ..\eeprom\NvM\NvM_Act.c	  2584      {
; ..\eeprom\NvM\NvM_Act.c	  2585          NvM_CurrentBlockInfo_t.NvIdentifier_u16 = NvM_CurrentBlockInfo_t.Descriptor_pt->NvIdentifier_u16 + 1u;
; ..\eeprom\NvM\NvM_Act.c	  2586      }
; ..\eeprom\NvM\NvM_Act.c	  2587      /* second block is defect, read first block */
; ..\eeprom\NvM\NvM_Act.c	  2588      else
; ..\eeprom\NvM\NvM_Act.c	  2589      {
; ..\eeprom\NvM\NvM_Act.c	  2590          NvM_CurrentBlockInfo_t.NvIdentifier_u16 = NvM_CurrentBlockInfo_t.Descriptor_pt->NvIdentifier_u16;
; ..\eeprom\NvM\NvM_Act.c	  2591      }
; ..\eeprom\NvM\NvM_Act.c	  2592  
; ..\eeprom\NvM\NvM_Act.c	  2593      if(NvM_CurrentBlockInfo_t.Descriptor_pt->CrcSettings != NVM_BLOCK_USE_CRC_OFF)
; ..\eeprom\NvM\NvM_Act.c	  2594      {
; ..\eeprom\NvM\NvM_Act.c	  2595          crcNrOfBytes = NvM_CrcJob_GetCrcLength(&NvM_CurrentBlockInfo_t.BlockCrcJob_t);
; ..\eeprom\NvM\NvM_Act.c	  2596      }
; ..\eeprom\NvM\NvM_Act.c	  2597  
; ..\eeprom\NvM\NvM_Act.c	  2598      /* read valid block */
; ..\eeprom\NvM\NvM_Act.c	  2599      if (MemIf_Read((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8, NvM_CurrentBlockInfo_t.NvIdentifier_u16, /* SBSW_NvM_FuncCall_PtrParam_MemIf */
; ..\eeprom\NvM\NvM_Act.c	  2600          0u, NvM_CurrentBlockInfo_t.NvRamAddr_t, (uint16)(NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockNVRAMDataLength + crcNrOfBytes))
; ..\eeprom\NvM\NvM_Act.c	  2601          == E_OK)
; ..\eeprom\NvM\NvM_Act.c	  2602      {
; ..\eeprom\NvM\NvM_Act.c	  2603          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
; ..\eeprom\NvM\NvM_Act.c	  2604      }
; ..\eeprom\NvM\NvM_Act.c	  2605      else
; ..\eeprom\NvM\NvM_Act.c	  2606      {
; ..\eeprom\NvM\NvM_Act.c	  2607          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Act.c	  2608      }
; ..\eeprom\NvM\NvM_Act.c	  2609  }
; ..\eeprom\NvM\NvM_Act.c	  2610  
; ..\eeprom\NvM\NvM_Act.c	  2611  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2612   * NvM_ActRepairRedBlockWriteInvalid
; ..\eeprom\NvM\NvM_Act.c	  2613   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2614  /*!
; ..\eeprom\NvM\NvM_Act.c	  2615   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2616   *
; ..\eeprom\NvM\NvM_Act.c	  2617   *
; ..\eeprom\NvM\NvM_Act.c	  2618   *
; ..\eeprom\NvM\NvM_Act.c	  2619   */
; ..\eeprom\NvM\NvM_Act.c	  2620  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlockWriteInvalid(void)
; ..\eeprom\NvM\NvM_Act.c	  2621  {
; ..\eeprom\NvM\NvM_Act.c	  2622      /* we do not have to check whether only one block is defect - this is done previously - this function
; ..\eeprom\NvM\NvM_Act.c	  2623       * shall not be invoked in case both blocks are defect or valid!  */
; ..\eeprom\NvM\NvM_Act.c	  2624  
; ..\eeprom\NvM\NvM_Act.c	  2625      /* first block is defect, write the first block */
; ..\eeprom\NvM\NvM_Act.c	  2626      if((NvM_RepairRedBlockState.NvBlockState & 0x0Fu) == NVM_NVBLOCK_STATE_DEFECT)
; ..\eeprom\NvM\NvM_Act.c	  2627      {
; ..\eeprom\NvM\NvM_Act.c	  2628          NvM_CurrentBlockInfo_t.NvIdentifier_u16 = NvM_CurrentBlockInfo_t.Descriptor_pt->NvIdentifier_u16;
; ..\eeprom\NvM\NvM_Act.c	  2629      }
; ..\eeprom\NvM\NvM_Act.c	  2630      /* second block is defect, write the second block */
; ..\eeprom\NvM\NvM_Act.c	  2631      else
; ..\eeprom\NvM\NvM_Act.c	  2632      {
; ..\eeprom\NvM\NvM_Act.c	  2633          NvM_CurrentBlockInfo_t.NvIdentifier_u16 = NvM_CurrentBlockInfo_t.Descriptor_pt->NvIdentifier_u16 + 1u;
; ..\eeprom\NvM\NvM_Act.c	  2634      }
; ..\eeprom\NvM\NvM_Act.c	  2635  
; ..\eeprom\NvM\NvM_Act.c	  2636      /* write defect block */
; ..\eeprom\NvM\NvM_Act.c	  2637      if(MemIf_Write((uint8)NvM_CurrentBlockInfo_t.Descriptor_pt->DeviceId_u8, /* SBSW_NvM_FuncCall_PtrParam_MemIf */
; ..\eeprom\NvM\NvM_Act.c	  2638          NvM_CurrentBlockInfo_t.NvIdentifier_u16, NvM_CurrentBlockInfo_t.NvRamAddr_t) == E_OK)
; ..\eeprom\NvM\NvM_Act.c	  2639      {
; ..\eeprom\NvM\NvM_Act.c	  2640          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_PENDING;
; ..\eeprom\NvM\NvM_Act.c	  2641      }
; ..\eeprom\NvM\NvM_Act.c	  2642      else
; ..\eeprom\NvM\NvM_Act.c	  2643      {
; ..\eeprom\NvM\NvM_Act.c	  2644          NvM_CurrentBlockInfo_t.LastResult_t = NVM_REQ_NOT_OK;
; ..\eeprom\NvM\NvM_Act.c	  2645      }
; ..\eeprom\NvM\NvM_Act.c	  2646  }
; ..\eeprom\NvM\NvM_Act.c	  2647  
; ..\eeprom\NvM\NvM_Act.c	  2648  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2649   * NvM_ActRepairRedBlocksFinishBlock
; ..\eeprom\NvM\NvM_Act.c	  2650   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2651  /*!
; ..\eeprom\NvM\NvM_Act.c	  2652   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2653   *
; ..\eeprom\NvM\NvM_Act.c	  2654   *
; ..\eeprom\NvM\NvM_Act.c	  2655   */
; ..\eeprom\NvM\NvM_Act.c	  2656  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksFinishBlock(void)
; ..\eeprom\NvM\NvM_Act.c	  2657  {
; ..\eeprom\NvM\NvM_Act.c	  2658      /* NvM tried to repair a redundant block via overwriting the defect one with valid one */
; ..\eeprom\NvM\NvM_Act.c	  2659      /* in case the overwriting wasn't successful, the redundancy is lost and we report the corresponding DEM error */
; ..\eeprom\NvM\NvM_Act.c	  2660      if(NvM_CurrentBlockInfo_t.LastResult_t != NVM_REQ_OK)
; ..\eeprom\NvM\NvM_Act.c	  2661      {
; ..\eeprom\NvM\NvM_Act.c	  2662          NvM_DemReportErrorLossOfRedundancy();
; ..\eeprom\NvM\NvM_Act.c	  2663      }
; ..\eeprom\NvM\NvM_Act.c	  2664  
; ..\eeprom\NvM\NvM_Act.c	  2665      /* do not change any internal states or results here! redundant block repair is an background job without results! */
; ..\eeprom\NvM\NvM_Act.c	  2666  }
; ..\eeprom\NvM\NvM_Act.c	  2667  
; ..\eeprom\NvM\NvM_Act.c	  2668  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2669   * NvM_ActRepairRedBlocksFinish
; ..\eeprom\NvM\NvM_Act.c	  2670   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2671  /*!
; ..\eeprom\NvM\NvM_Act.c	  2672   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2673   *
; ..\eeprom\NvM\NvM_Act.c	  2674   *
; ..\eeprom\NvM\NvM_Act.c	  2675   *
; ..\eeprom\NvM\NvM_Act.c	  2676   */
; ..\eeprom\NvM\NvM_Act.c	  2677  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActRepairRedBlocksFinish(void)
; ..\eeprom\NvM\NvM_Act.c	  2678  {
; ..\eeprom\NvM\NvM_Act.c	  2679      NvM_RepairRedBlockState.CurrentBlockId = 0u;
; ..\eeprom\NvM\NvM_Act.c	  2680      NvM_RepairRedBlockState.CrcBuffer = 0u;
; ..\eeprom\NvM\NvM_Act.c	  2681      NvM_RepairRedBlockState.NvBlockState = NVM_NVBLOCK_STATE_UPTODATE;
; ..\eeprom\NvM\NvM_Act.c	  2682  
; ..\eeprom\NvM\NvM_Act.c	  2683      /* clear job flag */
; ..\eeprom\NvM\NvM_Act.c	  2684      NvM_ApiFlags_u8 &= NVM_APIFLAG_REPAIR_REDUNDANT_BLOCKS_CL;
; ..\eeprom\NvM\NvM_Act.c	  2685  }
; ..\eeprom\NvM\NvM_Act.c	  2686  #endif /* (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON) */
; ..\eeprom\NvM\NvM_Act.c	  2687  
; ..\eeprom\NvM\NvM_Act.c	  2688  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2689  *  NvM_ActProcessCrc
; ..\eeprom\NvM\NvM_Act.c	  2690  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2691  /*!
; ..\eeprom\NvM\NvM_Act.c	  2692   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2693   *
; ..\eeprom\NvM\NvM_Act.c	  2694   *
; ..\eeprom\NvM\NvM_Act.c	  2695   */
; ..\eeprom\NvM\NvM_Act.c	  2696  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_ActProcessCrc(void)
; Function NvM_ActProcessCrc
.L231:
NvM_ActProcessCrc:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2697  {
; ..\eeprom\NvM\NvM_Act.c	  2698      NvM_CrcJob_Process(&NvM_CurrentBlockInfo_t.BlockCrcJob_t, NvM_NoOfCrcBytes_u16); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	fcall	.cocofun_9
.L1107:
	j	NvM_CrcJob_Process
.L644:
	
__NvM_ActProcessCrc_function_end:
	.size	NvM_ActProcessCrc,__NvM_ActProcessCrc_function_end-NvM_ActProcessCrc
.L375:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_UpdateConfigIdBlock',code,cluster('NvM_UpdateConfigIdBlock')
	.sect	'.text.NvM_Act.NvM_UpdateConfigIdBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2699  }
; ..\eeprom\NvM\NvM_Act.c	  2700  
; ..\eeprom\NvM\NvM_Act.c	  2701  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2702  *  NvM_UpdateConfigIdBlock
; ..\eeprom\NvM\NvM_Act.c	  2703  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2704  /*!
; ..\eeprom\NvM\NvM_Act.c	  2705   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2706   *
; ..\eeprom\NvM\NvM_Act.c	  2707   *
; ..\eeprom\NvM\NvM_Act.c	  2708   *
; ..\eeprom\NvM\NvM_Act.c	  2709   */
; ..\eeprom\NvM\NvM_Act.c	  2710  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_UpdateConfigIdBlock(void)
; Function NvM_UpdateConfigIdBlock
.L233:
NvM_UpdateConfigIdBlock:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2711  {
; ..\eeprom\NvM\NvM_Act.c	  2712      NvM_CurrentBlockInfo_t.ByteCount_u16 = NvM_CurrentBlockInfo_t.Descriptor_pt->NvBlockLength_u16;
	fcall	.cocofun_4
.L1334:

; ..\eeprom\NvM\NvM_Act.c	  2713  
; ..\eeprom\NvM\NvM_Act.c	  2714      NvM_InternalCopyBufferedData(&NvM_CurrentBlockInfo_t, NvM_CompiledConfigId_t.Bytes_au8); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	movh.a	a5,#@his(NvM_CompiledConfigId_t)
	lea	a5,[a5]@los(NvM_CompiledConfigId_t)
.L1335:
	mov.aa	a4,a15
	call	NvM_InternalCopyBufferedData
.L1336:

; ..\eeprom\NvM\NvM_Act.c	  2715  
; ..\eeprom\NvM\NvM_Act.c	  2716      NvM_CurrentBlockInfo_t.Mngmt_pt->NvRamAttributes_u8 |= (NVM_STATE_CHANGED_SET | NVM_STATE_VALID_SET); /* SBSW_NvM_Access_CurrBlockInfo */
	ld.a	a15,[a15]4
.L1337:
	ld.bu	d15,[+a15]2
.L1338:
	or	d15,#3
	st.b	[a15],d15
.L1339:

; ..\eeprom\NvM\NvM_Act.c	  2717  }
	ret
.L676:
	
__NvM_UpdateConfigIdBlock_function_end:
	.size	NvM_UpdateConfigIdBlock,__NvM_UpdateConfigIdBlock_function_end-NvM_UpdateConfigIdBlock
.L480:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_InternalCopyData',code,cluster('NvM_InternalCopyData')
	.sect	'.text.NvM_Act.NvM_InternalCopyData'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2718  
; ..\eeprom\NvM\NvM_Act.c	  2719  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2720  *  NvM_InternalCopyData
; ..\eeprom\NvM\NvM_Act.c	  2721  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2722  /*!
; ..\eeprom\NvM\NvM_Act.c	  2723   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2724   *
; ..\eeprom\NvM\NvM_Act.c	  2725   *
; ..\eeprom\NvM\NvM_Act.c	  2726   *
; ..\eeprom\NvM\NvM_Act.c	  2727   *
; ..\eeprom\NvM\NvM_Act.c	  2728   */
; ..\eeprom\NvM\NvM_Act.c	  2729  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_InternalCopyData(
; Function NvM_InternalCopyData
.L235:
NvM_InternalCopyData:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2730      NvM_BlockInfoPtrType info_pt, NvM_RamAddressType destPtr, NvM_ConstRamAddressType srcPtr)
; ..\eeprom\NvM\NvM_Act.c	  2731  {
; ..\eeprom\NvM\NvM_Act.c	  2732      /* #10 Calculate the length to copy: one step or the remaining bytes (< one step). */
; ..\eeprom\NvM\NvM_Act.c	  2733      uint16 length = (info_pt->ByteCount_u16 < NVM_MAX_DATA_COPY_BYTES) ?    
	movh.a	a15,#@his(NvM_NoOfCrcBytes_u16)
	ld.hu	d15,[a15]@los(NvM_NoOfCrcBytes_u16)
.L1344:
	ld.hu	d0,[a4]38
.L1345:
	sha	d15,#2
.L1346:
	min	d15,d0,d15
.L1347:
	extr.u	d15,d15,#0,#16
.L742:

; ..\eeprom\NvM\NvM_Act.c	  2734                       info_pt->ByteCount_u16 : NVM_MAX_DATA_COPY_BYTES;
; ..\eeprom\NvM\NvM_Act.c	  2735      /* #20 Decrease the remaining length count by the length to copy. */
; ..\eeprom\NvM\NvM_Act.c	  2736      info_pt->ByteCount_u16 -= length; /* SBSW_NvM_Access_CurrBlockInfo */
	sub	d0,d15
	st.h	[a4]38,d0
.L685:

; ..\eeprom\NvM\NvM_Act.c	  2737      /* #30 Copy the data from source to destination starting with the next byte to copy and the calculated length. */
; ..\eeprom\NvM\NvM_Act.c	  2738      {
; ..\eeprom\NvM\NvM_Act.c	  2739          /* Just a short "hack" to avoid calculation of the actual position within the loop. */
; ..\eeprom\NvM\NvM_Act.c	  2740          NvM_RamAddressType currDestPtr = &destPtr[info_pt->ByteCount_u16];
; ..\eeprom\NvM\NvM_Act.c	  2741          NvM_ConstRamAddressType currSrcPtr = &srcPtr[info_pt->ByteCount_u16];
	ld.hu	d0,[a4]38
.L1348:
	addsc.a	a15,a6,d0,#0
.L1349:
	addsc.a	a2,a5,d0,#0
.L1350:

; ..\eeprom\NvM\NvM_Act.c	  2742  
; ..\eeprom\NvM\NvM_Act.c	  2743          while(length > 0u)
; ..\eeprom\NvM\NvM_Act.c	  2744          {
; ..\eeprom\NvM\NvM_Act.c	  2745              --length;
; ..\eeprom\NvM\NvM_Act.c	  2746              currDestPtr[length] = currSrcPtr[length]; /* SBSW_NvM_AccessArray_InternalCopyData */
	addsc.a	a15,a15,d15,#0
.L1351:
	addsc.a	a2,a2,d15,#0
.L1352:
	j	.L93
.L94:
	add	d15,#-1
	ld.bu	d0,[+a15]-1
.L1353:
	st.b	[+a2]-1,d0
.L93:
	jne	d15,#0,.L94
.L686:

; ..\eeprom\NvM\NvM_Act.c	  2747          }
; ..\eeprom\NvM\NvM_Act.c	  2748      }
; ..\eeprom\NvM\NvM_Act.c	  2749  }
	ret
.L677:
	
__NvM_InternalCopyData_function_end:
	.size	NvM_InternalCopyData,__NvM_InternalCopyData_function_end-NvM_InternalCopyData
.L485:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_IsWriteAllAndKilled',code,cluster('NvM_IsWriteAllAndKilled')
	.sect	'.text.NvM_Act.NvM_IsWriteAllAndKilled'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2750  
; ..\eeprom\NvM\NvM_Act.c	  2751  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  2752  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2753  *  NvM_IsWriteAllAndKilled
; ..\eeprom\NvM\NvM_Act.c	  2754  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2755  /*!
; ..\eeprom\NvM\NvM_Act.c	  2756   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2757   *
; ..\eeprom\NvM\NvM_Act.c	  2758   *
; ..\eeprom\NvM\NvM_Act.c	  2759   */
; ..\eeprom\NvM\NvM_Act.c	  2760  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_IsWriteAllAndKilled(const NvM_InternalServiceIdType currServiceId, const uint8 currApiFlag)
; Function NvM_IsWriteAllAndKilled
.L237:
NvM_IsWriteAllAndKilled:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2761  {
; ..\eeprom\NvM\NvM_Act.c	  2762      return (boolean)((currServiceId == NVM_INT_FID_WRITE_ALL) && ((currApiFlag & NVM_APIFLAG_KILL_WR_ALL_SET) != 0u)); /* COV_NVM_KILLWRITEALL */
	mov	d2,#0
.L1382:
	jne	d4,#5,.L95
.L1383:
	jz.t	d5:5,.L96
.L1384:
	mov	d2,#1
.L96:
.L95:

; ..\eeprom\NvM\NvM_Act.c	  2763  }
	ret
.L698:
	
__NvM_IsWriteAllAndKilled_function_end:
	.size	NvM_IsWriteAllAndKilled,__NvM_IsWriteAllAndKilled_function_end-NvM_IsWriteAllAndKilled
.L500:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_IntUpdateCurrentBlockCRCCompareData',code,cluster('NvM_IntUpdateCurrentBlockCRCCompareData')
	.sect	'.text.NvM_Act.NvM_IntUpdateCurrentBlockCRCCompareData'
	.align	2
	

; ..\eeprom\NvM\NvM_Act.c	  2764  #endif /* (NVM_KILL_WRITEALL_API == STD_ON) */
; ..\eeprom\NvM\NvM_Act.c	  2765  
; ..\eeprom\NvM\NvM_Act.c	  2766  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2767   * NvM_IntUpdateCurrentBlockCRCCompareData
; ..\eeprom\NvM\NvM_Act.c	  2768   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2769  /*!
; ..\eeprom\NvM\NvM_Act.c	  2770   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2771   *
; ..\eeprom\NvM\NvM_Act.c	  2772   *
; ..\eeprom\NvM\NvM_Act.c	  2773   *
; ..\eeprom\NvM\NvM_Act.c	  2774   *
; ..\eeprom\NvM\NvM_Act.c	  2775   */
; ..\eeprom\NvM\NvM_Act.c	  2776  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_IntUpdateCurrentBlockCRCCompareData(const NvM_RequestResultType result)
; Function NvM_IntUpdateCurrentBlockCRCCompareData
.L239:
NvM_IntUpdateCurrentBlockCRCCompareData:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2777  {
; ..\eeprom\NvM\NvM_Act.c	  2778    if (NvM_CurrentBlockInfo_t.Descriptor_pt->CRCCompMechanismCrcAddr_t != NULL_PTR)
	movh.a	a2,#@his(NvM_CurrentBlockInfo_t)
	lea	a2,[a2]@los(NvM_CurrentBlockInfo_t)
.L1389:
	ld.a	a15,[a2]
.L1390:
	ld.a	a5,[a15]44
.L1391:
	jz.a	a5,.L98
.L1392:

; ..\eeprom\NvM\NvM_Act.c	  2779    {
; ..\eeprom\NvM\NvM_Act.c	  2780      if (result == NVM_REQ_OK)
	jne	d4,#0,.L99
.L1393:

; ..\eeprom\NvM\NvM_Act.c	  2781      {
; ..\eeprom\NvM\NvM_Act.c	  2782        /* Update CRCCompareMechanism data */
; ..\eeprom\NvM\NvM_Act.c	  2783        NvM_CrcJob_ExportBufferedValue(
; ..\eeprom\NvM\NvM_Act.c	  2784            &NvM_CurrentBlockInfo_t.BlockCrcJob_t,
	lea	a4,[a2]16
.L1394:

; ..\eeprom\NvM\NvM_Act.c	  2785            NvM_CurrentBlockInfo_t.Descriptor_pt->CRCCompMechanismCrcAddr_t); /* SBSW_NvM_FuncCall_PtrParam_CurrBlockInfo */
	j	NvM_CrcJob_ExportBufferedValue
.L99:

; ..\eeprom\NvM\NvM_Act.c	  2786      }
; ..\eeprom\NvM\NvM_Act.c	  2787      else
; ..\eeprom\NvM\NvM_Act.c	  2788      {
; ..\eeprom\NvM\NvM_Act.c	  2789        /* Invalidate CRCCompareMechanism data */
; ..\eeprom\NvM\NvM_Act.c	  2790        uint8 i;
; ..\eeprom\NvM\NvM_Act.c	  2791        for (i = 0u; i < NvM_CrcJob_GetCrcLength(&NvM_CurrentBlockInfo_t.BlockCrcJob_t); i++)
	mov	d15,#0
	lea	a4,[a2]28
.L743:
	j	.L101
.L102:

; ..\eeprom\NvM\NvM_Act.c	  2792        {
; ..\eeprom\NvM\NvM_Act.c	  2793          NvM_CurrentBlockInfo_t.Descriptor_pt->CRCCompMechanismCrcAddr_t[i] = 0xFFu; /* SBSW_NvM_AccessArray_CrcBuffers */
	ld.a	a15,[a2]
.L1395:
	mov	d0,#255
.L1396:
	ld.a	a15,[a15]44
.L1397:
	addsc.a	a15,a15,d15,#0
.L1398:
	add	d15,#1
.L744:
	st.b	[a15],d0
.L1399:
	extr.u	d15,d15,#0,#8
.L101:
	ld.a	a15,[a4]
	ld.bu	d0,[a15]16
.L1400:
	jlt.u	d15,d0,.L102
.L98:

; ..\eeprom\NvM\NvM_Act.c	  2794        }
; ..\eeprom\NvM\NvM_Act.c	  2795      }
; ..\eeprom\NvM\NvM_Act.c	  2796    }
; ..\eeprom\NvM\NvM_Act.c	  2797  }
	ret
.L703:
	
__NvM_IntUpdateCurrentBlockCRCCompareData_function_end:
	.size	NvM_IntUpdateCurrentBlockCRCCompareData,__NvM_IntUpdateCurrentBlockCRCCompareData_function_end-NvM_IntUpdateCurrentBlockCRCCompareData
.L505:
	; End of function
	
	.sdecl	'.text.NvM_Act.NvM_BlockNotification',code,cluster('NvM_BlockNotification')
	.sect	'.text.NvM_Act.NvM_BlockNotification'
	.align	2
	
	.global	NvM_BlockNotification

; ..\eeprom\NvM\NvM_Act.c	  2798  
; ..\eeprom\NvM\NvM_Act.c	  2799  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2800  *  NvM_BlockNotification
; ..\eeprom\NvM\NvM_Act.c	  2801  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2802  /*!
; ..\eeprom\NvM\NvM_Act.c	  2803   * Internal comment removed.
; ..\eeprom\NvM\NvM_Act.c	  2804   *
; ..\eeprom\NvM\NvM_Act.c	  2805   *
; ..\eeprom\NvM\NvM_Act.c	  2806   *
; ..\eeprom\NvM\NvM_Act.c	  2807   *
; ..\eeprom\NvM\NvM_Act.c	  2808   *
; ..\eeprom\NvM\NvM_Act.c	  2809   *
; ..\eeprom\NvM\NvM_Act.c	  2810   *
; ..\eeprom\NvM\NvM_Act.c	  2811   *
; ..\eeprom\NvM\NvM_Act.c	  2812   *
; ..\eeprom\NvM\NvM_Act.c	  2813   */
; ..\eeprom\NvM\NvM_Act.c	  2814  void NvM_BlockNotification(NvM_BlockIdType BlockId, NvM_ServiceIdType ServiceId, NvM_RequestResultType JobResult)
; Function NvM_BlockNotification
.L241:
NvM_BlockNotification:	.type	func

; ..\eeprom\NvM\NvM_Act.c	  2815  {
; ..\eeprom\NvM\NvM_Act.c	  2816    /* The complete function body is designed to be optimized away by the compiler, if it is not needed    *
; ..\eeprom\NvM\NvM_Act.c	  2817     * If the used macros are empty, the compiler may decide to remove code because it would contain       *
; ..\eeprom\NvM\NvM_Act.c	  2818     * empty execution blocks (it does not matter whether conditions were TRUE or FALSE                    */
; ..\eeprom\NvM\NvM_Act.c	  2819    const NvM_BlockIdType orgBlockId = NVM_BLOCK_FROM_DCM_ID(BlockId);
; ..\eeprom\NvM\NvM_Act.c	  2820    const NvM_BlockDescrPtrType blockDescriptorPtr = &NvM_BlockDescriptorTable_at[orgBlockId];
	mov	e8,d5,d4
	movh.a	a15,#@his(NvM_BlockDescriptorTable_at)
.L786:
	mov	d10,d6
	lea	a15,[a15]@los(NvM_BlockDescriptorTable_at)
.L745:
	insert	d0,d8,#0,#15,#17
.L746:
	sha	d15,d0,#6
.L787:
	addsc.a	a15,a15,d15,#0
.L747:

; ..\eeprom\NvM\NvM_Act.c	  2821    /* #100 only for normal blocks (no DCM blocks) */
; ..\eeprom\NvM\NvM_Act.c	  2822    if (orgBlockId == BlockId)
	jne	d0,d8,.L103
.L748:

; ..\eeprom\NvM\NvM_Act.c	  2823    {
; ..\eeprom\NvM\NvM_Act.c	  2824      /* #110 only when the job is done (result != NVM_REQ_PENDING) and
; ..\eeprom\NvM\NvM_Act.c	  2825       * current job is not WriteAll (do not invoke any callback during WriteAll) and
; ..\eeprom\NvM\NvM_Act.c	  2826       * for ReadAll as current job the callback invoking is enabled */
; ..\eeprom\NvM\NvM_Act.c	  2827      if( (JobResult != NVM_REQ_PENDING) &&
	jeq	d10,#2,.L104
.L788:

; ..\eeprom\NvM\NvM_Act.c	  2828          (ServiceId != NVM_WRITE_ALL) &&
	mov	d15,#13
.L749:
	jeq	d15,d9,.L105
.L750:

; ..\eeprom\NvM\NvM_Act.c	  2829          (!((ServiceId == NVM_READ_ALL) && ((blockDescriptorPtr->Flags_u8 & NVM_CBK_DURING_READALL_ON) != NVM_CBK_DURING_READALL_ON))))
	mov	d15,#12
.L751:
	jne	d15,d9,.L106
.L752:
	ld.bu	d15,[a15]59
.L789:
	jz.t	d15:6,.L107
.L106:

; ..\eeprom\NvM\NvM_Act.c	  2830      {
; ..\eeprom\NvM\NvM_Act.c	  2831        /* #111 invoke single block callback if configured */
; ..\eeprom\NvM\NvM_Act.c	  2832        if (blockDescriptorPtr->JobEndCbkFunc_pt != NULL_PTR)
	ld.a	a2,[a15]16
.L790:
	jz.a	a2,.L108
.L753:

; ..\eeprom\NvM\NvM_Act.c	  2833        {
; ..\eeprom\NvM\NvM_Act.c	  2834          (void)blockDescriptorPtr->JobEndCbkFunc_pt(ServiceId, JobResult); /* SBSW_NvM_FuncPtrCall_UserCallbacks */
	mov	e4,d10,d9
	calli	a2
.L108:

; ..\eeprom\NvM\NvM_Act.c	  2835        }
; ..\eeprom\NvM\NvM_Act.c	  2836        /* #112 invoke extended single block callback if configured */
; ..\eeprom\NvM\NvM_Act.c	  2837        if (blockDescriptorPtr->JobEndCbkExtFunc_pt != NULL_PTR)
	ld.a	a15,[a15]20
.L791:
	jz.a	a15,.L109
.L754:

; ..\eeprom\NvM\NvM_Act.c	  2838        {
; ..\eeprom\NvM\NvM_Act.c	  2839          (void)blockDescriptorPtr->JobEndCbkExtFunc_pt(BlockId, ServiceId, JobResult); /* SBSW_NvM_FuncPtrCall_UserCallbacks */
	mov	e4,d9,d8
.L755:
	mov	d6,d10
.L756:
	ji	a15
.L109:
.L107:
.L105:
.L104:
.L103:

; ..\eeprom\NvM\NvM_Act.c	  2840        }
; ..\eeprom\NvM\NvM_Act.c	  2841      }
; ..\eeprom\NvM\NvM_Act.c	  2842      /* #120 block has enabled BswM callback */
; ..\eeprom\NvM\NvM_Act.c	  2843      if(blockDescriptorPtr->NotifyBswM == STD_ON)
; ..\eeprom\NvM\NvM_Act.c	  2844      {
; ..\eeprom\NvM\NvM_Act.c	  2845        /* #121 invoke BswM callback */
; ..\eeprom\NvM\NvM_Act.c	  2846        NvM_invokeCurrentBlockMode(BlockId, JobResult);
; ..\eeprom\NvM\NvM_Act.c	  2847      }
; ..\eeprom\NvM\NvM_Act.c	  2848    }
; ..\eeprom\NvM\NvM_Act.c	  2849  }
	ret
.L584:
	
__NvM_BlockNotification_function_end:
	.size	NvM_BlockNotification,__NvM_BlockNotification_function_end-NvM_BlockNotification
.L265:
	; End of function
	
	.sdecl	'.bss.NvM_Act.NvM_TestBuffer_u8',data,cluster('NvM_TestBuffer_u8')
	.sect	'.bss.NvM_Act.NvM_TestBuffer_u8'
NvM_TestBuffer_u8:	.type	object
	.size	NvM_TestBuffer_u8,1
	.space	1
	.sdecl	'.rodata.NvM_Act.NvM_ActionTable_ap',data,rom,cluster('NvM_ActionTable_ap')
	.sect	'.rodata.NvM_Act.NvM_ActionTable_ap'
	.global	NvM_ActionTable_ap
	.align	4
NvM_ActionTable_ap:	.type	object
	.size	NvM_ActionTable_ap,184
	.word	NvM_ActSetInitialAttr,NvM_ActInitMainFsm,NvM_ActInitBlock,NvM_ActInitReadAll,NvM_ActInitReadBlockSubFsm,NvM_ActInitRestoreBlockDefaultsSubFsm,NvM_ActInitWriteAll,NvM_ActInitWriteBlock
	.word	NvM_ActInitWriteBlockFsm,NvM_ActInitRestoreBlockDefaults,NvM_ActFinishMainJob,NvM_ActKillWriteAll,NvM_ActFinishBlock,NvM_ActInitNextBlockReadAll,NvM_ActInitNextBlockWriteAll,NvM_ActFinishCfgIdCheck
	.word	NvM_ActFinishReadBlock,NvM_ActFinishWriteBlock,NvM_ActFinishEraseBlock,NvM_ActEraseNvBlock,NvM_ActInvalidateNvBlock,NvM_ActProcessCrc,NvM_ActWriteNvBlock,NvM_ActReadNvBlock
	.word	NvM_ActProcessCrcRead,NvM_ActReadCopyData,NvM_ActRestoreRomDefaults,NvM_ActFinishRestoreRomDefaults,NvM_ActTestBlockBlank,NvM_ActValidateRam,NvM_ActSetupRedundant,NvM_ActSetupOther
	.word	NvM_ActUpdateNvState,NvM_ActSetReqIntegrityFailed,NvM_ActSetReqSkipped,NvM_ActSetReqNotOk,NvM_ActSetReqOk,NvM_SetBlockPendingWriteAll,NvM_ActCopyNvDataToBuf,NvM_ActGetMultiBlockJob
	.word	NvM_ActCancelNV,NvM_ActKillSubFsm,NvM_ActFinishReadBlockAndSetSkipped,NvM_ActGetNormalPrioJob
	.word	NvM_ActWait,NvM_ActNop
	.calls	'__INDIRECT__','NvM_ActCancelNV'
	.calls	'__INDIRECT__','NvM_ActFinishReadBlockAndSetSkipped'
	.calls	'__INDIRECT__','NvM_ActSetReqOk'
	.calls	'__INDIRECT__','NvM_ActGetNormalPrioJob'
	.calls	'__INDIRECT__','NvM_ActSetInitialAttr'
	.calls	'__INDIRECT__','NvM_ActInitMainFsm'
	.calls	'__INDIRECT__','NvM_ActInitBlock'
	.calls	'__INDIRECT__','NvM_ActInitReadAll'
	.calls	'__INDIRECT__','NvM_ActInitReadBlockSubFsm'
	.calls	'__INDIRECT__','NvM_ActInitRestoreBlockDefaultsSubFsm'
	.calls	'__INDIRECT__','NvM_ActInitWriteAll'
	.calls	'__INDIRECT__','NvM_ActInitWriteBlock'
	.calls	'__INDIRECT__','NvM_ActInitWriteBlockFsm'
	.calls	'__INDIRECT__','NvM_ActInitRestoreBlockDefaults'
	.calls	'__INDIRECT__','NvM_ActFinishMainJob'
	.calls	'__INDIRECT__','NvM_ActKillWriteAll'
	.calls	'__INDIRECT__','NvM_ActFinishBlock'
	.calls	'__INDIRECT__','NvM_ActInitNextBlockReadAll'
	.calls	'__INDIRECT__','NvM_ActInitNextBlockWriteAll'
	.calls	'__INDIRECT__','NvM_ActFinishCfgIdCheck'
	.calls	'__INDIRECT__','NvM_ActFinishReadBlock'
	.calls	'__INDIRECT__','NvM_ActFinishWriteBlock'
	.calls	'__INDIRECT__','NvM_ActFinishEraseBlock'
	.calls	'__INDIRECT__','NvM_ActEraseNvBlock'
	.calls	'__INDIRECT__','NvM_ActInvalidateNvBlock'
	.calls	'__INDIRECT__','NvM_ActProcessCrc'
	.calls	'__INDIRECT__','NvM_ActWriteNvBlock'
	.calls	'__INDIRECT__','NvM_ActReadNvBlock'
	.calls	'__INDIRECT__','NvM_ActProcessCrcRead'
	.calls	'__INDIRECT__','NvM_ActReadCopyData'
	.calls	'__INDIRECT__','NvM_ActRestoreRomDefaults'
	.calls	'__INDIRECT__','NvM_ActFinishRestoreRomDefaults'
	.calls	'__INDIRECT__','NvM_ActTestBlockBlank'
	.calls	'__INDIRECT__','NvM_ActValidateRam'
	.calls	'__INDIRECT__','NvM_ActSetupRedundant'
	.calls	'__INDIRECT__','NvM_ActSetupOther'
	.calls	'__INDIRECT__','NvM_ActUpdateNvState'
	.calls	'__INDIRECT__','NvM_ActSetReqIntegrityFailed'
	.calls	'__INDIRECT__','NvM_ActSetReqSkipped'
	.calls	'__INDIRECT__','NvM_ActSetReqNotOk'
	.calls	'__INDIRECT__','NvM_SetBlockPendingWriteAll'
	.calls	'__INDIRECT__','NvM_ActWait'
	.calls	'__INDIRECT__','NvM_ActNop'
	.calls	'__INDIRECT__','NvM_ActGetMultiBlockJob'
	.calls	'__INDIRECT__','NvM_ActCopyNvDataToBuf'
	.calls	'__INDIRECT__','NvM_ActKillSubFsm'
	.calls	'NvM_ActEraseNvBlock','MemIf_EraseImmediateBlock'
	.calls	'NvM_ActFinishBlock','NvM_IsWriteAllAndKilled'
	.calls	'NvM_ActFinishBlock','NvM_BlockNotification'
	.calls	'NvM_ActInitNextBlockReadAll','NvM_ActFinishBlock'
	.calls	'NvM_ActInitNextBlockReadAll','NvM_ActInitBlock'
	.calls	'NvM_ActInitNextBlockReadAll','NvM_CrcJob_Create'
	.calls	'NvM_ActInitNextBlockWriteAll','NvM_ActFinishBlock'
	.calls	'NvM_ActInitNextBlockWriteAll','NvM_ActInitBlock'
	.calls	'NvM_ActFinishMainJob','NvM_QryReadAllKilled'
	.calls	'NvM_ActFinishMainJob','NvM_MultiBlockCbk'
	.calls	'NvM_ActFinishMainJob','NvM_EnterCriticalSection'
	.calls	'NvM_ActFinishMainJob','NvM_ExitCriticalSection'
	.calls	'NvM_ActFinishMainJob','MemIf_SetMode'
	.calls	'NvM_ActFinishMainJob','NvM_ActFinishBlock'
	.calls	'NvM_ActKillWriteAll','MemIf_SetMode'
	.calls	'NvM_ActKillWriteAll','NvM_ActCancelNV'
	.calls	'NvM_ActKillWriteAll','NvM_EnterCriticalSection'
	.calls	'NvM_ActKillWriteAll','NvM_ExitCriticalSection'
	.calls	'NvM_ActFinishReadBlock','NvM_ActUpdateNvState'
	.calls	'NvM_ActFinishReadBlock','NvM_IntUpdateCurrentBlockCRCCompareData'
	.calls	'NvM_ActFinishReadBlock','NvM_CrcJob_ExportBufferedValue'
	.calls	'NvM_ActFinishReadBlockAndSetSkipped','NvM_ActFinishReadBlock'
	.calls	'NvM_ActFinishWriteBlock','NvM_ActUpdateNvState'
	.calls	'NvM_ActFinishWriteBlock','NvM_IntUpdateCurrentBlockCRCCompareData'
	.calls	'NvM_ActInitBlock','NvM_IntCreateNvState'
	.calls	'NvM_ActInitMainFsm','NvM_ActInitBlock'
	.calls	'NvM_ActInitMainFsm','__INDIRECT__'
	.calls	'NvM_ActInitReadAll','NvM_BlockNotification'
	.calls	'NvM_ActInitReadAll','NvM_ActSetInitialAttr'
	.calls	'NvM_ActInitReadAll','MemIf_SetMode'
	.calls	'NvM_ActInitReadAll','NvM_ActInitReadBlockSubFsm'
	.calls	'NvM_ActFinishCfgIdCheck','NvM_UpdateConfigIdBlock'
	.calls	'NvM_ActInitWriteAll','MemIf_SetMode'
	.calls	'NvM_ActInitWriteBlock','NvM_ActTestBlockBlank'
	.calls	'NvM_ActInitWriteBlock','NvM_CrcJob_Create'
	.calls	'NvM_ActInitWriteBlock','NvM_ActCopyNvDataToBuf'
	.calls	'NvM_ActFinishEraseBlock','NvM_IntUpdateCurrentBlockCRCCompareData'
	.calls	'NvM_ActInitReadBlockSubFsm','__INDIRECT__'
	.calls	'NvM_ActInitRestoreBlockDefaultsSubFsm','__INDIRECT__'
	.calls	'NvM_ActInitWriteBlockFsm','__INDIRECT__'
	.calls	'NvM_ActInvalidateNvBlock','MemIf_InvalidateBlock'
	.calls	'NvM_ActWriteNvBlock','NvM_IntUpdateCurrentBlockCRCCompareData'
	.calls	'NvM_ActWriteNvBlock','NvM_CrcJob_CopyToBuffer'
	.calls	'NvM_ActWriteNvBlock','NvM_CrcJob_ExportBufferedValue'
	.calls	'NvM_ActWriteNvBlock','NvM_EnterCriticalSection'
	.calls	'NvM_ActWriteNvBlock','NvM_IsWriteAllAndKilled'
	.calls	'NvM_ActWriteNvBlock','MemIf_Write'
	.calls	'NvM_ActWriteNvBlock','NvM_ExitCriticalSection'
	.calls	'NvM_ActReadNvBlock','NvM_CrcJob_Create'
	.calls	'NvM_ActReadNvBlock','MemIf_Read'
	.calls	'NvM_ActProcessCrcRead','NvM_CrcJob_Process'
	.calls	'NvM_ActReadCopyData','NvM_InternalCopyBufferedData'
	.calls	'NvM_InternalCopyBufferedData','__INDIRECT__'
	.calls	'NvM_InternalCopyBufferedData','NvM_InternalCopyData'
	.calls	'NvM_ActRestoreRomDefaults','NvM_CrcJob_Create'
	.calls	'NvM_ActRestoreRomDefaults','NvM_InternalCopyBufferedData'
	.calls	'NvM_ActRestoreRomDefaults','NvM_QryIsInitCallbackConfigured'
	.calls	'NvM_ActRestoreRomDefaults','__INDIRECT__'
	.calls	'NvM_ActRestoreRomDefaults','NvM_ActWait'
	.calls	'NvM_ActFinishRestoreRomDefaults','NvM_QryIsInitCallbackConfigured'
	.calls	'NvM_ActFinishRestoreRomDefaults','NvM_IntUpdateCurrentBlockCRCCompareData'
	.calls	'NvM_SetBlockPendingWriteAll','NvM_BlockNotification'
	.calls	'NvM_ActSetupRedundant','NvM_ActUpdateNvState'
	.calls	'NvM_ActSetupOther','NvM_ActUpdateNvState'
	.calls	'NvM_ActUpdateNvState','NvM_IntCreateNvState'
	.calls	'NvM_ActTestBlockBlank','MemIf_Read'
	.calls	'NvM_ActCopyNvDataToBuf','__INDIRECT__'
	.calls	'NvM_ActCopyNvDataToBuf','NvM_InternalCopyData'
	.calls	'NvM_ActCancelNV','MemIf_GetStatus'
	.calls	'NvM_ActCancelNV','MemIf_Cancel'
	.calls	'NvM_ActProcessCrc','NvM_CrcJob_Process'
	.calls	'NvM_UpdateConfigIdBlock','NvM_InternalCopyBufferedData'
	.calls	'NvM_IntUpdateCurrentBlockCRCCompareData','NvM_CrcJob_ExportBufferedValue'
	.calls	'NvM_BlockNotification','__INDIRECT__'
	.calls	'NvM_ActEraseNvBlock','.cocofun_1'
	.calls	'NvM_ActEraseNvBlock','.cocofun_5'
	.calls	'.cocofun_1','.cocofun_14'
	.calls	'NvM_ActFinishBlock','.cocofun_6'
	.calls	'NvM_ActFinishBlock','.cocofun_7'
	.calls	'NvM_ActInitNextBlockReadAll','.cocofun_6'
	.calls	'NvM_ActInitNextBlockReadAll','.cocofun_8'
	.calls	'NvM_ActFinishMainJob','.cocofun_6'
	.calls	'NvM_ActFinishMainJob','.cocofun_7'
	.calls	'NvM_ActFinishReadBlock','.cocofun_8'
	.calls	'NvM_ActFinishReadBlock','.cocofun_13'
	.calls	'NvM_ActFinishWriteBlock','.cocofun_8'
	.calls	'NvM_ActInitMainFsm','.cocofun_7'
	.calls	'NvM_ActInitMainFsm','.cocofun_2'
	.calls	'NvM_ActFinishCfgIdCheck','.cocofun_14'
	.calls	'NvM_ActInitWriteBlock','.cocofun_11'
	.calls	'NvM_ActInitWriteBlock','.cocofun_12'
	.calls	'.cocofun_11','.cocofun_14'
	.calls	'NvM_ActInitReadBlockSubFsm','.cocofun_3'
	.calls	'NvM_ActInitReadBlockSubFsm','.cocofun_2'
	.calls	'NvM_ActInitRestoreBlockDefaultsSubFsm','.cocofun_3'
	.calls	'NvM_ActInitRestoreBlockDefaultsSubFsm','.cocofun_2'
	.calls	'NvM_ActInitWriteBlockFsm','.cocofun_3'
	.calls	'NvM_ActInitWriteBlockFsm','.cocofun_2'
	.calls	'NvM_ActInitRestoreBlockDefaults','.cocofun_4'
	.calls	'.cocofun_4','.cocofun_14'
	.calls	'NvM_ActInvalidateNvBlock','.cocofun_1'
	.calls	'NvM_ActInvalidateNvBlock','.cocofun_5'
	.calls	'NvM_ActReadNvBlock','.cocofun_14'
	.calls	'NvM_ActReadNvBlock','.cocofun_12'
	.calls	'NvM_ActReadNvBlock','.cocofun_5'
	.calls	'NvM_ActProcessCrcRead','.cocofun_9'
	.calls	'NvM_ActRestoreRomDefaults','.cocofun_6'
	.calls	'NvM_ActRestoreRomDefaults','.cocofun_8'
	.calls	'NvM_ActFinishRestoreRomDefaults','.cocofun_13'
	.calls	'NvM_ActFinishRestoreRomDefaults','.cocofun_14'
	.calls	'NvM_ActSetInitialAttr','.cocofun_14'
	.calls	'NvM_ActSetupRedundant','.cocofun_14'
	.calls	'NvM_ActSetupOther','.cocofun_11'
	.calls	'NvM_ActUpdateNvState','.cocofun_14'
	.calls	'NvM_ActTestBlockBlank','.cocofun_1'
	.calls	'NvM_ActTestBlockBlank','.cocofun_5'
	.calls	'NvM_ActValidateRam','.cocofun_13'
	.calls	'NvM_ActValidateRam','.cocofun_14'
	.calls	'NvM_ActCancelNV','.cocofun_10'
	.calls	'NvM_ActProcessCrc','.cocofun_9'
	.calls	'NvM_UpdateConfigIdBlock','.cocofun_4'
	.calls	'NvM_ActEraseNvBlock','',0
	.calls	'.cocofun_5','',0
	.calls	'.cocofun_1','',0
	.calls	'.cocofun_14','',0
	.calls	'NvM_ActFinishBlock','',0
	.calls	'.cocofun_7','',0
	.calls	'.cocofun_6','',0
	.calls	'NvM_ActInitNextBlockReadAll','',0
	.calls	'.cocofun_8','',0
	.calls	'NvM_ActInitNextBlockWriteAll','',0
	.calls	'NvM_ActFinishMainJob','',0
	.calls	'NvM_ActKillWriteAll','',0
	.calls	'NvM_ActFinishReadBlock','',0
	.calls	'.cocofun_13','',0
	.calls	'NvM_ActFinishReadBlockAndSetSkipped','',0
	.calls	'NvM_ActFinishWriteBlock','',0
	.calls	'NvM_ActInitBlock','',0
	.calls	'NvM_ActInitMainFsm','',0
	.calls	'.cocofun_2','',0
	.calls	'NvM_ActInitReadAll','',0
	.calls	'NvM_ActFinishCfgIdCheck','',0
	.calls	'NvM_ActInitWriteAll','',0
	.calls	'NvM_ActInitWriteBlock','',0
	.calls	'.cocofun_12','',0
	.calls	'.cocofun_11','',0
	.calls	'NvM_ActFinishEraseBlock','',0
	.calls	'NvM_ActInitReadBlockSubFsm','',0
	.calls	'.cocofun_3','',0
	.calls	'NvM_ActInitRestoreBlockDefaultsSubFsm','',0
	.calls	'NvM_ActInitWriteBlockFsm','',0
	.calls	'NvM_ActInitRestoreBlockDefaults','',0
	.calls	'.cocofun_4','',0
	.calls	'NvM_ActInvalidateNvBlock','',0
	.calls	'NvM_ActNop','',0
	.calls	'NvM_ActWriteNvBlock','',0
	.calls	'NvM_ActReadNvBlock','',0
	.calls	'NvM_ActProcessCrcRead','',0
	.calls	'.cocofun_9','',0
	.calls	'NvM_ActReadCopyData','',0
	.calls	'NvM_InternalCopyBufferedData','',0
	.calls	'NvM_ActRestoreRomDefaults','',0
	.calls	'NvM_ActFinishRestoreRomDefaults','',0
	.calls	'NvM_ActSetInitialAttr','',0
	.calls	'NvM_ActSetReqIntegrityFailed','',0
	.calls	'NvM_ActSetReqSkipped','',0
	.calls	'NvM_ActSetReqNotOk','',0
	.calls	'NvM_ActSetReqOk','',0
	.calls	'NvM_SetBlockPendingWriteAll','',0
	.calls	'NvM_ActSetupRedundant','',0
	.calls	'NvM_ActSetupOther','',0
	.calls	'NvM_ActUpdateNvState','',0
	.calls	'NvM_IntCreateNvState','',0
	.calls	'NvM_ActGetMultiBlockJob','',0
	.calls	'NvM_ActTestBlockBlank','',0
	.calls	'NvM_ActValidateRam','',0
	.calls	'NvM_ActWait','',0
	.calls	'NvM_ActCopyNvDataToBuf','',0
	.calls	'NvM_ActCancelNV','',0
	.calls	'.cocofun_10','',0
	.calls	'NvM_ActKillSubFsm','',0
	.calls	'NvM_ActProcessCrc','',0
	.calls	'NvM_UpdateConfigIdBlock','',0
	.calls	'NvM_InternalCopyData','',0
	.calls	'NvM_IsWriteAllAndKilled','',0
	.calls	'NvM_IntUpdateCurrentBlockCRCCompareData','',0
	.extern	NvM_NoOfBlockIds_t
	.extern	NvM_CompiledConfigId_t
	.extern	MemIf_Read
	.extern	MemIf_Write
	.extern	MemIf_InvalidateBlock
	.extern	MemIf_EraseImmediateBlock
	.extern	MemIf_Cancel
	.extern	MemIf_GetStatus
	.extern	MemIf_SetMode
	.extern	NvM_EnterCriticalSection
	.extern	NvM_ExitCriticalSection
	.extern	NvM_MultiBlockCbk
	.extern	NvM_InternalBuffer_au8
	.extern	NvM_NoOfCrcBytes_u16
	.extern	NvM_BlockDescriptorTable_at
	.extern	NvM_BlockMngmtArea_at
	.extern	NvM_DcmBlockMngmt_t
	.extern	NvM_QryReadAllKilled
	.extern	NvM_QryIsInitCallbackConfigured
	.extern	NvM_CrcJob_Create
	.extern	NvM_CrcJob_Process
	.extern	NvM_CrcJob_CopyToBuffer
	.extern	NvM_CrcJob_ExportBufferedValue
	.extern	NvM_JobMainState_t
	.extern	NvM_JobSubState_t
	.extern	NvM_ApiFlags_u8
	.extern	NvM_CurrentJob_t
	.extern	NvM_CurrentBlockInfo_t
	.extern	NvM_IntServiceDescrTable_at
	.extern	NvM_ActGetNormalPrioJob
	.extern	__INDIRECT__
	.calls	'NvM_BlockNotification','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L243:
	.word	11197
	.half	3
	.word	.L244
	.byte	4
.L242:
	.byte	1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L245
.L587:
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'MemIf_Read',0,1,138,1,34
	.word	178
	.byte	1,1,1,1,4
	.byte	'DeviceIndex',0,1,138,1,51
	.word	178
.L585:
	.byte	2
	.byte	'unsigned short int',0,2,7,4
	.byte	'BlockNumber',0,1,138,1,71
	.word	240
	.byte	4
	.byte	'BlockOffset',0,1,138,1,91
	.word	240
.L694:
	.byte	5
	.word	178
	.byte	6
	.byte	'MemIf_DataPtr_pu8',0,2,96,50
	.word	304
	.byte	4
	.byte	'DataBufferPtr',0,1,138,1,122
	.word	309
	.byte	4
	.byte	'Length',0,1,138,1,144,1
	.word	240
	.byte	0,5
	.word	178
	.byte	3
	.byte	'MemIf_Write',0,1,158,1,34
	.word	178
	.byte	1,1,1,1,4
	.byte	'DeviceIndex',0,1,158,1,52
	.word	178
	.byte	4
	.byte	'BlockNumber',0,1,158,1,72
	.word	240
	.byte	4
	.byte	'DataBufferPtr',0,1,158,1,103
	.word	309
	.byte	0,3
	.byte	'MemIf_InvalidateBlock',0,1,177,1,34
	.word	178
	.byte	1,1,1,1,4
	.byte	'DeviceIndex',0,1,177,1,63
	.word	178
	.byte	4
	.byte	'BlockNumber',0,1,177,1,83
	.word	240
	.byte	0,3
	.byte	'MemIf_EraseImmediateBlock',0,1,196,1,34
	.word	178
	.byte	1,1,1,1,4
	.byte	'DeviceIndex',0,1,196,1,66
	.word	178
	.byte	4
	.byte	'BlockNumber',0,1,196,1,86
	.word	240
	.byte	0,7
	.byte	'MemIf_Cancel',0,1,213,1,24,1,1,1,1,4
	.byte	'DeviceIndex',0,1,213,1,43
	.word	178
	.byte	0,8,2,59,9,1,9
	.byte	'MEMIF_UNINIT',0,0,9
	.byte	'MEMIF_IDLE',0,1,9
	.byte	'MEMIF_BUSY',0,2,9
	.byte	'MEMIF_BUSY_INTERNAL',0,3,0,3
	.byte	'MemIf_GetStatus',0,1,231,1,36
	.word	676
	.byte	1,1,1,1,4
	.byte	'DeviceIndex',0,1,231,1,58
	.word	178
	.byte	0,7
	.byte	'MemIf_SetMode',0,1,157,2,24,1,1,1,1,8,2,88,9,1,9
	.byte	'MEMIF_MODE_SLOW',0,0,9
	.byte	'MEMIF_MODE_FAST',0,1,0,4
	.byte	'Mode',0,1,157,2,53
	.word	819
	.byte	0,10
	.byte	'NvM_EnterCriticalSection',0,3,192,2,37,1,1,1,1,10
	.byte	'NvM_ExitCriticalSection',0,3,204,2,37,1,1,1,1,7
	.byte	'NvM_MultiBlockCbk',0,3,218,2,37,1,1,1,1,4
	.byte	'ServiceId',0,3,218,2,73
	.word	178
	.byte	4
	.byte	'JobResult',0,3,218,2,106
	.word	178
	.byte	0
.L590:
	.byte	11
	.word	240
	.byte	12,3,196,1,9,64
.L680:
	.byte	6
	.byte	'NvM_RamAddressType',0,3,161,1,48
	.word	304
	.byte	13
	.byte	'RamBlockDataAddr_t',0,4
	.word	1020
	.byte	2,35,0,11
	.word	178
	.byte	5
	.word	1076
	.byte	6
	.byte	'NvM_RomAddressType',0,3,164,1,50
	.word	1081
	.byte	13
	.byte	'RomBlockDataAddr_pt',0,4
	.word	1086
	.byte	2,35,4,14
	.word	178
	.byte	1,1,5
	.word	1143
	.byte	6
	.byte	'NvM_InitCbkPtrType',0,3,142,1,9
	.word	1150
	.byte	13
	.byte	'InitCbkFunc_pt',0,4
	.word	1155
	.byte	2,35,8,15
	.word	178
	.byte	1,1,16
	.word	240
	.byte	17
	.byte	'void',0,5
	.word	1219
	.byte	16
	.word	1225
	.byte	16
	.word	240
	.byte	0,5
	.word	1207
	.byte	6
	.byte	'NvM_InitCbkExtPtrType',0,3,145,1,9
	.word	1241
	.byte	13
	.byte	'InitCbkExtFunc_pt',0,4
	.word	1246
	.byte	2,35,12,15
	.word	178
	.byte	1,1,16
	.word	178
	.byte	16
	.word	178
	.byte	0,5
	.word	1304
	.byte	6
	.byte	'NvM_JobEndCbkPtrType',0,3,134,1,9
	.word	1322
	.byte	13
	.byte	'JobEndCbkFunc_pt',0,4
	.word	1327
	.byte	2,35,16,15
	.word	178
	.byte	1,1,16
	.word	240
	.byte	16
	.word	178
	.byte	16
	.word	178
	.byte	0,5
	.word	1383
	.byte	6
	.byte	'NvM_JobEndCbkExtPtrType',0,3,137,1,9
	.word	1406
	.byte	13
	.byte	'JobEndCbkExtFunc_pt',0,4
	.word	1411
	.byte	2,35,20,15
	.word	178
	.byte	1,1,11
	.word	1219
	.byte	5
	.word	1480
	.byte	16
	.word	1485
	.byte	0,5
	.word	1473
	.byte	6
	.byte	'NvM_ReadRamFromNvMCbkPtrType',0,3,149,1,9
	.word	1496
	.byte	13
	.byte	'CbkGetMirrorFunc_pt',0,4
	.word	1501
	.byte	2,35,24,15
	.word	178
	.byte	1,1,16
	.word	1225
	.byte	0,5
	.word	1568
	.byte	6
	.byte	'NvM_WriteRamToNvMCbkPtrType',0,3,148,1,9
	.word	1581
	.byte	13
	.byte	'CbkSetMirrorFunc_pt',0,4
	.word	1586
	.byte	2,35,28,18,1,1,16
	.word	240
	.byte	16
	.word	1225
	.byte	16
	.word	240
	.byte	0,5
	.word	1652
	.byte	6
	.byte	'NvM_PreWriteTransformCbkPtrType',0,3,152,1,9
	.word	1671
	.byte	13
	.byte	'CbkPreWriteTransform',0,4
	.word	1676
	.byte	2,35,32,6
	.byte	'NvM_PostReadTransformCbkPtrType',0,3,153,1,9
	.word	1241
	.byte	13
	.byte	'CbkPostReadTransform',0,4
	.word	1747
	.byte	2,35,36,6
	.byte	'NvM_RamCrcAddressType',0,3,170,1,51
	.word	304
	.byte	13
	.byte	'RamBlockCrcAddr_t',0,4
	.word	1818
	.byte	2,35,40,13
	.byte	'CRCCompMechanismCrcAddr_t',0,4
	.word	1818
	.byte	2,35,44,13
	.byte	'NvIdentifier_u16',0,2
	.word	240
	.byte	2,35,48,13
	.byte	'NvBlockLength_u16',0,2
	.word	240
	.byte	2,35,50,13
	.byte	'NvCryptoReference',0,1
	.word	178
	.byte	2,35,52,13
	.byte	'NvBlockNVRAMDataLength',0,2
	.word	240
	.byte	2,35,54,19
	.byte	'NvBlockCount_u8',0,1
	.word	178
	.byte	8,0,2,35,56,19
	.byte	'BlockPrio_u8',0,1
	.word	178
	.byte	8,0,2,35,57,19
	.byte	'DeviceId_u8',0,1
	.word	178
	.byte	4,4,2,35,58,19
	.byte	'MngmtType_t',0,1
	.word	178
	.byte	2,2,2,35,58,19
	.byte	'CrcSettings',0,1
	.word	178
	.byte	2,0,2,35,58,19
	.byte	'Flags_u8',0,1
	.word	178
	.byte	8,0,2,35,59,19
	.byte	'NotifyBswM',0,1
	.word	178
	.byte	1,7,2,35,60,0,11
	.word	1014
	.byte	5
	.word	2186
	.byte	6
	.byte	'NvM_BlockDescrPtrType',0,3,240,1,71
	.word	2191
.L592:
	.byte	11
	.word	2196
	.byte	11
	.word	1014
	.byte	5
	.word	2232
	.byte	5
	.word	178
	.byte	11
	.word	178
	.byte	5
	.word	2247
	.byte	5
	.word	1143
	.byte	5
	.word	1207
	.byte	5
	.word	1304
	.byte	5
	.word	1383
	.byte	5
	.word	1473
	.byte	5
	.word	1568
	.byte	5
	.word	1652
	.byte	5
	.word	1207
	.byte	5
	.word	178
	.byte	20
	.byte	'NvM_QryReadAllKilled',0,4,133,1,33
	.word	178
	.byte	1,1,1,1,3
	.byte	'NvM_QryIsInitCallbackConfigured',0,4,147,1,40
	.word	178
	.byte	1,1,1,1,4
	.byte	'BlockDescriptor',0,4,147,1,94
	.word	2196
	.byte	0,7
	.byte	'NvM_CrcJob_Create',0,5,183,1,37,1,1,1,1,21
	.byte	'NvM_CrcJobStruct',0,5,91,16,20,2
	.byte	'unsigned long int',0,4,7,13
	.byte	'CurrentCrcValue',0,4
	.word	2456
	.byte	2,35,0
.L682:
	.byte	6
	.byte	'NvM_ConstRamAddressType',0,3,162,1,50
	.word	1081
	.byte	13
	.byte	'RamData_pt',0,4
	.word	2502
	.byte	2,35,4,6
	.byte	'NvM_CrcBufferPtrType',0,5,68,59
	.word	304
	.byte	13
	.byte	'CrcBuffer',0,4
	.word	2555
	.byte	2,35,8,21
	.byte	'NvM_CrcHandlerClass',0,5,79,8,20,18,1,1,11
	.word	178
	.byte	5
	.word	2631
	.byte	16
	.word	2636
	.byte	16
	.word	240
	.byte	5
	.word	2456
	.byte	16
	.word	2651
	.byte	0,5
	.word	2628
	.byte	6
	.byte	'NvM_CrcCalculateFPtr',0,5,74,9
	.word	2662
	.byte	13
	.byte	'calc',0,4
	.word	2667
	.byte	2,35,0,15
	.word	178
	.byte	1,1,16
	.word	2636
	.byte	16
	.word	2636
	.byte	0,5
	.word	2710
	.byte	6
	.byte	'NvM_CrcCompareFPtr',0,5,75,9
	.word	2728
	.byte	13
	.byte	'compare',0,4
	.word	2733
	.byte	2,35,4,18,1,1,5
	.word	178
	.byte	16
	.word	2780
	.byte	16
	.word	2636
	.byte	0,5
	.word	2777
	.byte	6
	.byte	'NvM_CrcCopyToBufferFPtr',0,5,76,9
	.word	2796
	.byte	13
	.byte	'copyToBuffer',0,4
	.word	2801
	.byte	2,35,8,13
	.byte	'initialCrcValue',0,4
	.word	2456
	.byte	2,35,12,13
	.byte	'crcLength',0,1
	.word	178
	.byte	2,35,16,0,11
	.word	2603
	.byte	5
	.word	2900
	.byte	6
	.byte	'NvM_CrcHandlerClassConstPtr',0,5,88,75
	.word	2905
	.byte	13
	.byte	'HandlerInstance_pt',0,4
	.word	2910
	.byte	2,35,12,13
	.byte	'RemainingLength_u16',0,2
	.word	240
	.byte	2,35,16,0,5
	.word	2434
	.byte	6
	.byte	'NvM_CrcJobPtrType',0,5,100,60
	.word	3004
	.byte	4
	.byte	'Self',0,5,183,1,73
	.word	3009
	.byte	4
	.byte	'BlockId',0,5,183,1,95
	.word	240
	.byte	4
	.byte	'RamDataPtr',0,5,184,1,28
	.word	1020
	.byte	4
	.byte	'DataLength',0,5,184,1,47
	.word	240
	.byte	0,5
	.word	2434
	.byte	11
	.word	178
	.byte	5
	.word	3112
	.byte	5
	.word	178
	.byte	11
	.word	2603
	.byte	5
	.word	3127
	.byte	5
	.word	2628
	.byte	5
	.word	2710
	.byte	5
	.word	2777
	.byte	7
	.byte	'NvM_CrcJob_Process',0,5,199,1,37,1,1,1,1,4
	.byte	'Self',0,5,199,1,74
	.word	3009
	.byte	4
	.byte	'ProcessLength',0,5,199,1,87
	.word	240
	.byte	0,7
	.byte	'NvM_CrcJob_CopyToBuffer',0,5,212,1,37,1,1,1,1,11
	.word	2434
	.byte	5
	.word	3251
	.byte	6
	.byte	'NvM_CrcJobConstPtrType',0,5,101,62
	.word	3256
	.byte	4
	.byte	'Self',0,5,212,1,84
	.word	3261
	.byte	0,11
	.word	2434
	.byte	5
	.word	3307
	.byte	7
	.byte	'NvM_CrcJob_ExportBufferedValue',0,5,242,1,37,1,1,1,1,4
	.byte	'Self',0,5,242,1,91
	.word	3261
	.byte	4
	.byte	'DestPtr',0,5,242,1,118
	.word	2555
	.byte	0,10
	.byte	'NvM_ActGetNormalPrioJob',0,6,135,1,37,1,1,1,1
.L598:
	.byte	11
	.word	240
.L600:
	.byte	11
	.word	2196
	.byte	8,3,243,1,9,1,9
	.byte	'NVM_INT_FID_WRITE_BLOCK',0,0,9
	.byte	'NVM_INT_FID_READ_BLOCK',0,1,9
	.byte	'NVM_INT_FID_RESTORE_DEFAULTS',0,2,9
	.byte	'NVM_INT_FID_INVALIDATE_NV_BLOCK',0,3,9
	.byte	'NVM_INT_FID_ERASE_BLOCK',0,4,9
	.byte	'NVM_INT_FID_WRITE_ALL',0,5,9
	.byte	'NVM_INT_FID_READ_ALL',0,6,9
	.byte	'NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS',0,7,9
	.byte	'NVM_INT_FID_NO_JOB_PENDING',0,8,0
.L614:
	.byte	11
	.word	3432
	.byte	12,3,124,9,4,13
	.byte	'NvDataIndex_t',0,1
	.word	178
	.byte	2,35,0,13
	.byte	'NvRamErrorStatus_u8',0,1
	.word	178
	.byte	2,35,1,13
	.byte	'NvRamAttributes_u8',0,1
	.word	178
	.byte	2,35,2,0,5
	.word	3700
	.byte	6
	.byte	'NvM_RamMngmtPtrType',0,3,131,1,65
	.word	3786
.L627:
	.byte	11
	.word	3791
	.byte	5
	.word	3700
.L629:
	.byte	11
	.word	2196
.L632:
	.byte	11
	.word	3791
.L646:
	.byte	11
	.word	1020
.L650:
	.byte	11
	.word	2196
.L670:
	.byte	11
	.word	178
	.byte	12,7,66,9,44,13
	.byte	'Descriptor_pt',0,4
	.word	2196
	.byte	2,35,0,13
	.byte	'Mngmt_pt',0,4
	.word	3791
	.byte	2,35,4,13
	.byte	'RamAddr_t',0,4
	.word	1020
	.byte	2,35,8,13
	.byte	'NvRamAddr_t',0,4
	.word	1020
	.byte	2,35,12,13
	.byte	'BlockCrcJob_t',0,20
	.word	2434
	.byte	2,35,16,13
	.byte	'NvIdentifier_u16',0,2
	.word	240
	.byte	2,35,36,13
	.byte	'ByteCount_u16',0,2
	.word	240
	.byte	2,35,38,13
	.byte	'LastResult_t',0,1
	.word	178
	.byte	2,35,40,13
	.byte	'WriteRetryCounter_u8',0,1
	.word	178
	.byte	2,35,41,13
	.byte	'InternalFlags_u8',0,1
	.word	178
	.byte	2,35,42,13
	.byte	'NvState_u8',0,1
	.word	178
	.byte	2,35,43,0,5
	.word	3855
	.byte	5
	.word	3855
.L678:
	.byte	6
	.byte	'NvM_BlockInfoPtrType',0,7,85,60
	.word	4117
.L690:
	.byte	11
	.word	2196
.L699:
	.byte	11
	.word	3432
.L701:
	.byte	11
	.word	178
.L704:
	.byte	11
	.word	178
	.byte	22
	.byte	'__INDIRECT__',0,8,1,1,1,1,1,6
	.byte	'__prof_adm',0,8,1,1
	.word	1225
	.byte	23,1,5
	.word	4210
	.byte	6
	.byte	'__codeptr',0,8,1,1
	.word	4212
	.byte	6
	.byte	'uint8',0,9,90,29
	.word	178
	.byte	2
	.byte	'short int',0,2,5,6
	.byte	'sint16',0,9,91,29
	.word	4249
	.byte	6
	.byte	'uint16',0,9,92,29
	.word	240
	.byte	6
	.byte	'uint32',0,9,94,29
	.word	2456
	.byte	6
	.byte	'boolean',0,9,105,29
	.word	178
	.byte	2
	.byte	'unsigned long long int',0,8,7,6
	.byte	'uint64',0,9,130,1,30
	.word	4323
	.byte	6
	.byte	'Std_ReturnType',0,10,113,15
	.word	178
	.byte	6
	.byte	'PduLengthType',0,11,76,22
	.word	240
	.byte	6
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,12,112,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,12,115,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,12,118,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,12,121,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,12,124,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,12,136,1,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,12,139,1,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,12,148,1,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,12,151,1,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,12,141,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,12,144,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,12,147,3,16
	.word	240
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,12,150,3,16
	.word	240
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,12,153,3,16
	.word	240
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,12,156,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,12,159,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,12,162,3,16
	.word	240
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,12,165,3,16
	.word	240
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,12,180,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,12,183,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,12,186,3,16
	.word	2456
	.byte	6
	.byte	'IdtAppCom_MainPwrFltRsn',0,12,192,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGMDiags',0,12,195,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGMFltRsn',0,12,198,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGMSts',0,12,201,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGMSwSts',0,12,207,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,12,210,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,12,213,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,12,216,3,16
	.word	240
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,12,219,3,16
	.word	240
	.byte	6
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,12,225,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,12,228,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_RednPwrFltRsn',0,12,231,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,12,234,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_SHWAIndSts',0,12,237,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_SHWASysFltSts',0,12,240,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_SHWASysMsg',0,12,243,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,12,246,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_SHWASysSts',0,12,249,3,15
	.word	178
	.byte	6
	.byte	'IdtAppCom_SHWASysTakeOver',0,12,252,3,15
	.word	178
	.byte	6
	.byte	'NvM_BlockIdType',0,12,227,5,16
	.word	240
	.byte	6
	.byte	'NvM_RequestResultType',0,12,207,6,15
	.word	178
	.byte	6
	.byte	'NvM_ServiceIdType',0,12,231,6,15
	.word	178
	.byte	2
	.byte	'unsigned int',0,4,7,6
	.byte	'Rte_BitType',0,12,230,7,22
	.word	6050
	.byte	24,13,63,9,2,13
	.byte	'Word_u16',0,2
	.word	240
	.byte	2,35,0,25,2
	.word	178
	.byte	26,1,0,13
	.byte	'Bytes_au8',0,2
	.word	6110
	.byte	2,35,0,0,6
	.byte	'NvM_CompiledConfigIdType',0,13,67,3
	.word	6087
	.byte	11
	.word	240
	.byte	27
	.byte	'NvM_NoOfBlockIds_t',0,13,246,1,40
	.word	6172
	.byte	1,1,11
	.word	6087
	.byte	27
	.byte	'NvM_CompiledConfigId_t',0,13,253,1,58
	.word	6207
	.byte	1,1,6
	.byte	'MemIf_StatusType',0,2,65,3
	.word	676
	.byte	8,2,72,9,1,9
	.byte	'MEMIF_JOB_OK',0,0,9
	.byte	'MEMIF_JOB_FAILED',0,1,9
	.byte	'MEMIF_JOB_PENDING',0,2,9
	.byte	'MEMIF_JOB_CANCELED',0,3,9
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,9
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,6
	.byte	'MemIf_JobResultType',0,2,80,3
	.word	6271
	.byte	6
	.byte	'MemIf_ModeType',0,2,92,3
	.word	819
	.byte	15
	.word	178
	.byte	1,1,16
	.word	240
	.byte	16
	.word	240
	.byte	16
	.word	2780
	.byte	16
	.word	240
	.byte	0,5
	.word	6452
	.byte	6
	.byte	'MemIf_ApiReadType',0,14,120,9
	.word	6480
	.byte	15
	.word	178
	.byte	1,1,16
	.word	240
	.byte	16
	.word	2780
	.byte	0,5
	.word	6511
	.byte	6
	.byte	'MemIf_ApiWriteType',0,14,121,9
	.word	6529
	.byte	15
	.word	178
	.byte	1,1,16
	.word	240
	.byte	0,5
	.word	6561
	.byte	6
	.byte	'MemIf_ApiEraseImmediateBlockType',0,14,122,9
	.word	6574
	.byte	6
	.byte	'MemIf_ApiInvalidateBlockType',0,14,123,9
	.word	6574
	.byte	28,1,1,5
	.word	6657
	.byte	6
	.byte	'MemIf_ApiCancelType',0,14,124,9
	.word	6660
	.byte	14
	.word	676
	.byte	1,1,5
	.word	6693
	.byte	6
	.byte	'MemIf_ApiGetStatusType',0,14,125,9
	.word	6700
	.byte	14
	.word	6271
	.byte	1,1,5
	.word	6736
	.byte	6
	.byte	'MemIf_ApiGetJobResultType',0,14,126,9
	.word	6743
	.byte	18,1,1,16
	.word	819
	.byte	0,5
	.word	6782
	.byte	6
	.byte	'MemIf_ApiSetModeType',0,14,127,9
	.word	6791
	.byte	6
	.byte	'NvM_BitFieldType',0,3,113,22
	.word	6050
	.byte	6
	.byte	'NvM_CrcType',0,3,116,26
	.word	6050
	.byte	6
	.byte	'NvM_RamMngmtAreaType',0,3,129,1,3
	.word	3700
	.byte	6
	.byte	'NvM_BlockDescriptorType',0,3,223,1,3
	.word	1014
	.byte	6
	.byte	'NvM_CsmJobIdType',0,3,231,1,16
	.word	2456
	.byte	6
	.byte	'NvM_InternalServiceIdType',0,3,254,1,3
	.word	3432
	.byte	6
	.byte	'NvM_QueueEntryRefType',0,3,129,2,15
	.word	178
	.byte	29
	.word	178
	.byte	30,0,27
	.byte	'NvM_InternalBuffer_au8',0,3,228,2,37
	.word	7025
	.byte	1,1,11
	.word	240
	.byte	27
	.byte	'NvM_NoOfCrcBytes_u16',0,3,252,2,40
	.word	7066
	.byte	1,1,29
	.word	1014
	.byte	30,0,11
	.word	7103
	.byte	27
	.byte	'NvM_BlockDescriptorTable_at',0,3,151,3,57
	.word	7110
	.byte	1,1,29
	.word	3700
	.byte	30,0,27
	.byte	'NvM_BlockMngmtArea_at',0,3,160,3,51
	.word	7154
	.byte	1,1,27
	.byte	'NvM_DcmBlockMngmt_t',0,3,163,3,51
	.word	3700
	.byte	1,1,8,15,42,9,1,9
	.byte	'NVM_ACT_ID_SetInitialAttr',0,0,9
	.byte	'NVM_ACT_ID_InitMainFsm',0,1,9
	.byte	'NVM_ACT_ID_InitBlock',0,2,9
	.byte	'NVM_ACT_ID_InitReadAll',0,3,9
	.byte	'NVM_ACT_ID_InitReadBlockSubFsm',0,4,9
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaultsSubFsm',0,5,9
	.byte	'NVM_ACT_ID_InitWriteAll',0,6,9
	.byte	'NVM_ACT_ID_InitWriteBlock',0,7,9
	.byte	'NVM_ACT_ID_InitWriteBlockFsm',0,8,9
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaults',0,9,9
	.byte	'NVM_ACT_ID_FinishMainJob',0,10,9
	.byte	'NVM_ACT_ID_KillWritAll',0,11,9
	.byte	'NVM_ACT_ID_FinishBlock',0,12,9
	.byte	'NVM_ACT_ID_InitNextBlockReadAll',0,13,9
	.byte	'NVM_ACT_ID_InitNextBlockWriteAll',0,14,9
	.byte	'NVM_ACT_ID_FinishCfgIdCheck',0,15,9
	.byte	'NVM_ACT_ID_FinishReadBlock',0,16,9
	.byte	'NVM_ACT_ID_FinishWriteBlock',0,17,9
	.byte	'NVM_ACT_ID_FinishEraseBlock',0,18,9
	.byte	'NVM_ACT_ID_EraseNvBlock',0,19,9
	.byte	'NVM_ACT_ID_InvalidateNvBlock',0,20,9
	.byte	'NVM_ACT_ID_ProcessCrc',0,21,9
	.byte	'NVM_ACT_ID_WriteNvBlock',0,22,9
	.byte	'NVM_ACT_ID_ReadNvBlock',0,23,9
	.byte	'NVM_ACT_ID_ProcessCrcRead',0,24,9
	.byte	'NVM_ACT_ID_ReadCopyData',0,25,9
	.byte	'NVM_ACT_ID_RestoreRomDefaults',0,26,9
	.byte	'NVM_ACT_ID_FinishRestoreRomDefaults',0,27,9
	.byte	'NVM_ACT_ID_TestBlockBlank',0,28,9
	.byte	'NVM_ACT_ID_ValidateRam',0,29,9
	.byte	'NVM_ACT_ID_SetupRedundant',0,30,9
	.byte	'NVM_ACT_ID_SetupOther',0,31,9
	.byte	'NVM_ACT_ID_UpdateNvState',0,32,9
	.byte	'NVM_ACT_ID_SetReqIntegrityFailed',0,33,9
	.byte	'NVM_ACT_ID_SetReqSkipped',0,34,9
	.byte	'NVM_ACT_ID_SetReqNotOk',0,35,9
	.byte	'NVM_ACT_ID_SetReqOk',0,36,9
	.byte	'NVM_ACT_ID_SetBlockPendingWriteAll',0,37,9
	.byte	'NVM_ACT_ID_CopyNvDataToBuf',0,38,9
	.byte	'NVM_ACT_ID_GetMultiBlockJob',0,39,9
	.byte	'NVM_ACT_ID_CancelNV',0,40,9
	.byte	'NVM_ACT_ID_KillSubFsm',0,41,9
	.byte	'NVM_ACT_ID_FinishReadBlockAndSetSkipped',0,42,9
	.byte	'NVM_ACT_ID_GetNormalPrioJob',0,43,9
	.byte	'NVM_ACT_ID_Wait',0,44,9
	.byte	'NVM_ACT_ID_Nop',0,45,0,6
	.byte	'NvM_StateActionIdType',0,15,110,3
	.word	7225
	.byte	6
	.byte	'NvM_ActFctPtrType',0,15,113,9
	.word	6660
	.byte	25,184,1
	.word	8573
	.byte	26,45,0
.L707:
	.byte	11
	.word	8599
	.byte	5
	.word	6657
	.byte	8,4,48,9,1,9
	.byte	'NVM_QRY_ID_BLK_WRITE_ALL',0,0,9
	.byte	'NVM_QRY_ID_CANCEL_WRITE_ALL',0,1,9
	.byte	'NVM_QR_ID_WRITEALL_KILLED',0,2,9
	.byte	'NVM_QRY_ID_CRC_BUSY',0,3,9
	.byte	'NVM_QRY_ID_DATA_COPY_BUSY',0,4,9
	.byte	'NVM_QRY_ID_CRC_MATCH',0,5,9
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_READALL',0,6,9
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_WRITEALL',0,7,9
	.byte	'NVM_QRY_ID_LAST_RESULT_OK',0,8,9
	.byte	'NVM_QRY_ID_MAIN_FSM_RUNNING',0,9,9
	.byte	'NVM_QRY_ID_MULTI_BLK_JOB',0,10,9
	.byte	'NVM_QRY_ID_NORMAL_PRIO_JOB',0,11,9
	.byte	'NVM_QRY_ID_NV_BUSY',0,12,9
	.byte	'NVM_QRY_ID_MEMHWA_BUSY',0,13,9
	.byte	'NVM_QRY_ID_RAM_VALID',0,14,9
	.byte	'NVM_QRY_ID_REDUNDANT_BLOCK',0,15,9
	.byte	'NVM_QRY_ID_SKIP_BLOCK',0,16,9
	.byte	'NVM_QRY_ID_SUB_FSM_RUNNING',0,17,9
	.byte	'NVM_QRY_ID_WRITE_BLOCK_ONCE',0,18,9
	.byte	'NVM_QRY_ID_WRITE_RETRIES_EXCEEDED',0,19,9
	.byte	'NVM_QRY_ID_HAS_ROM',0,20,9
	.byte	'NVM_QRY_ID_EXT_RUNTIME',0,21,9
	.byte	'NvM_QRY_CRC_COMP_MECHANISM_SKIPWRITE',0,22,9
	.byte	'NVM_QRY_POST_READ_TRANSFORM',0,23,9
	.byte	'NVM_QRY_READALL_KILLED',0,24,9
	.byte	'NVM_QRY_SYNCDECRYPT',0,25,9
	.byte	'NVM_QRY_SYNCENCRYPT',0,26,9
	.byte	'NVM_QRY_CSM_RETRIES_NECESSARY',0,27,9
	.byte	'NVM_QRY_ID_TRUE',0,28,0,6
	.byte	'NvM_StateQueryIdType',0,4,93,3
	.word	8619
	.byte	6
	.byte	'NvM_CrcJobType',0,5,98,3
	.word	2434
	.byte	12,7,57,9,8,13
	.byte	'JobBlockId_t',0,2
	.word	240
	.byte	2,35,0,13
	.byte	'JobServiceId_t',0,1
	.word	3432
	.byte	2,35,2,13
	.byte	'RamAddr_t',0,4
	.word	1020
	.byte	2,35,4,0,6
	.byte	'NvM_JobType',0,7,62,3
	.word	9475
	.byte	6
	.byte	'NvM_BlockInfoType',0,7,83,3
	.word	3855
	.byte	8,7,96,9,1,9
	.byte	'NVM_STATE_UNINIT',0,0,9
	.byte	'NVM_STATE_IDLE',0,1,9
	.byte	'NVM_STATE_NORMAL_PRIO_JOB',0,2,9
	.byte	'NVM_STATE_MULTI_BLOCK_JOB',0,3,9
	.byte	'NVM_STATE_READ_READ_DATA',0,4,9
	.byte	'NVM_STATE_READ_DATA_VALIDATION',0,5,9
	.byte	'NVM_STATE_READ_CMP_CRC',0,6,9
	.byte	'NVM_STATE_READ_IMPL_RECOV',0,7,9
	.byte	'NVM_STATE_READ_LOAD_ROM',0,8,9
	.byte	'NVM_STATE_READ_FINALIZE',0,9,9
	.byte	'NVM_STATE_WRITE_INITIAL',0,10,9
	.byte	'NVM_STATE_WRITE_CRCCALC',0,11,9
	.byte	'NVM_STATE_WRITE_CRCCOMPMECHANISM',0,12,9
	.byte	'NVM_STATE_WRITE_TEST_PRI_READ',0,13,9
	.byte	'NVM_STATE_WRITE_TEST_SEC_READ',0,14,9
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_1',0,15,9
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_2',0,16,9
	.byte	'NVM_STATE_RESTORE_LOAD_ROM',0,17,9
	.byte	'NVM_STATE_INVALIDATING_BLOCK',0,18,9
	.byte	'NVM_STATE_ERASE_ERASE_BLOCK',0,19,9
	.byte	'NVM_STATE_READALL_PROC_CONFIG_ID',0,20,9
	.byte	'NVM_STATE_READALL_PROC_RAM_BLOCK',0,21,9
	.byte	'NVM_STATE_READALL_CHK_SKIP',0,22,9
	.byte	'NVM_STATE_READALL_KILLED',0,23,9
	.byte	'NVM_STATE_READALL_WR_ONCE_PROT',0,24,9
	.byte	'NVM_STATE_READALL_CHK_RAM_VALIDITY',0,25,9
	.byte	'NVM_STATE_READALL_READ_NV',0,26,9
	.byte	'NVM_STATE_READALL_LOAD_DEFAULTS',0,27,9
	.byte	'NVM_STATE_READALL_READABILITY_CHECK',0,28,9
	.byte	'NVM_STATE_WRITEALL_PROC_BLOCK',0,29,9
	.byte	'NVM_STATE_WRITEALL_WRITE_FSM',0,30,9
	.byte	'NVM_STATE_WRITEALL_WAIT_MEMHWA',0,31,9
	.byte	'NVM_STATE_FSM_FINISHED',0,32,0,6
	.byte	'NvM_StateIdType',0,7,215,1,3
	.word	9592
	.byte	12,7,217,1,9,4,13
	.byte	'InitialActionId',0,1
	.word	7225
	.byte	2,35,0,13
	.byte	'InitialState_t',0,1
	.word	9592
	.byte	2,35,1,13
	.byte	'PublicFid_t',0,1
	.word	178
	.byte	2,35,2,0,6
	.byte	'NvM_IntServiceDescrType',0,7,222,1,3
	.word	10602
	.byte	12,7,228,1,9,2,13
	.byte	'ExitHandler_t',0,1
	.word	7225
	.byte	2,35,0,13
	.byte	'EntryHandler_t',0,1
	.word	7225
	.byte	2,35,1,0,6
	.byte	'NvM_StateChangeActionsType',0,7,232,1,3
	.word	10712
	.byte	12,7,236,1,9,6,25,2
	.word	8619
	.byte	26,1,0,13
	.byte	'Queries_at',0,2
	.word	10808
	.byte	2,35,0,13
	.byte	'Actions_t',0,2
	.word	10712
	.byte	2,35,2,13
	.byte	'NextState_t',0,1
	.word	9592
	.byte	2,35,4,0,6
	.byte	'NvM_StateChangeIfDescrType',0,7,241,1,3
	.word	10802
	.byte	12,7,243,1,9,4,13
	.byte	'Actions_t',0,2
	.word	10712
	.byte	2,35,0,13
	.byte	'NextState_t',0,1
	.word	9592
	.byte	2,35,2,0,6
	.byte	'NvM_StateChangeElseDescrType',0,7,247,1,3
	.word	10914
	.byte	27
	.byte	'NvM_JobMainState_t',0,7,137,2,44
	.word	9592
	.byte	1,1,27
	.byte	'NvM_JobSubState_t',0,7,138,2,44
	.word	9592
	.byte	1,1,27
	.byte	'NvM_ApiFlags_u8',0,7,143,2,34
	.word	178
	.byte	1,1,27
	.byte	'NvM_CurrentJob_t',0,7,160,2,43
	.word	9475
	.byte	1,1,27
	.byte	'NvM_CurrentBlockInfo_t',0,7,161,2,49
	.word	3855
	.byte	1,1,25,36
	.word	10602
	.byte	26,8,0,11
	.word	11147
	.byte	27
	.byte	'NvM_IntServiceDescrTable_at',0,7,174,2,58
	.word	11156
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L244:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,58,15,59,15,57,15
	.byte	73,19,54,15,39,12,63,12,60,12,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,15,0,73,19,0,0,6,22,0,3,8,58
	.byte	15,59,15,57,15,73,19,0,0,7,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,8,4,1,58,15,59,15,57
	.byte	15,11,15,0,0,9,40,0,3,8,28,13,0,0,10,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,11,38,0,73
	.byte	19,0,0,12,19,1,58,15,59,15,57,15,11,15,0,0,13,13,0,3,8,11,15,73,19,56,9,0,0,14,21,0,73,19,54,15,39,12
	.byte	0,0,15,21,1,73,19,54,15,39,12,0,0,16,5,0,73,19,0,0,17,59,0,3,8,0,0,18,21,1,54,15,39,12,0,0,19,13,0,3,8
	.byte	11,15,73,19,13,15,12,15,56,9,0,0,20,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,21,19
	.byte	1,3,8,58,15,59,15,57,15,11,15,0,0,22,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,23,21,0,54,15,0
	.byte	0,24,23,1,58,15,59,15,57,15,11,15,0,0,25,1,1,11,15,73,19,0,0,26,33,0,47,15,0,0,27,52,0,3,8,58,15,59,15
	.byte	57,15,73,19,63,12,60,12,0,0,28,21,0,54,15,39,12,0,0,29,1,1,73,19,0,0,30,33,0,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L245:
	.word	.L758-.L757
.L757:
	.half	3
	.word	.L760-.L759
.L759:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	0
	.byte	'MemIf.h',0,1,0,0
	.byte	'MemIf_Types.h',0,1,0,0
	.byte	'_PrivateCfg.h',0,2,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0
	.byte	'Platform_Types.h',0,3,0,0
	.byte	'Std_Types.h',0,3,0,0
	.byte	'ComStack_Types.h',0,3,0,0
	.byte	'Rte_Type.h',0,4,0,0
	.byte	'_Cfg.h',0,2,0,0
	.byte	'_Cfg.h',0,1,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.h',0,0,0,0,0
.L760:
.L758:
	.sdecl	'.debug_info',debug,cluster('NvM_ActCancelNV')
	.sect	'.debug_info'
.L246:
	.word	226
	.half	3
	.word	.L247
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L249,.L248
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActCancelNV',0,1,183,18,30,1,1,1
	.word	.L225,.L580,.L224
	.byte	4
	.word	.L581
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActCancelNV')
	.sect	'.debug_abbrev'
.L247:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActCancelNV')
	.sect	'.debug_line'
.L248:
	.word	.L762-.L761
.L761:
	.half	3
	.word	.L764-.L763
.L763:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L764:
	.byte	5,31,7,0,5,2
	.word	.L225
	.byte	3,185,18,1,5,24,9
	.half	.L765-.L225
	.byte	1,5,5,9
	.half	.L766-.L765
	.byte	1,5,29,7,9
	.half	.L767-.L766
	.byte	3,2,1,5,22,9
	.half	.L768-.L767
	.byte	1,5,1,7,9
	.half	.L92-.L768
	.byte	3,2,1,7,9
	.half	.L250-.L92
	.byte	0,1,1
.L762:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActCancelNV')
	.sect	'.debug_ranges'
.L249:
	.word	-1,.L225,0,.L250-.L225,0,0
.L581:
	.word	-1,.L225,0,.L580-.L225,-1,.L227,0,.L555-.L227,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActFinishReadBlockAndSetSkipped')
	.sect	'.debug_info'
.L251:
	.word	250
	.half	3
	.word	.L252
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L254,.L253
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActFinishReadBlockAndSetSkipped',0,1,223,9,30,1,1,1
	.word	.L139,.L582,.L138
	.byte	4
	.word	.L139,.L582
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActFinishReadBlockAndSetSkipped')
	.sect	'.debug_abbrev'
.L252:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActFinishReadBlockAndSetSkipped')
	.sect	'.debug_line'
.L253:
	.word	.L770-.L769
.L769:
	.half	3
	.word	.L772-.L771
.L771:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L772:
	.byte	5,27,7,0,5,2
	.word	.L139
	.byte	3,224,9,1,5,5,9
	.half	.L773-.L139
	.byte	3,1,1,5,43,9
	.half	.L774-.L773
	.byte	1,5,41,1,5,1,9
	.half	.L775-.L774
	.byte	3,1,1,7,9
	.half	.L255-.L775
	.byte	0,1,1
.L770:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActFinishReadBlockAndSetSkipped')
	.sect	'.debug_ranges'
.L254:
	.word	-1,.L139,0,.L255-.L139,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActSetReqOk')
	.sect	'.debug_info'
.L256:
	.word	230
	.half	3
	.word	.L257
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L259,.L258
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActSetReqOk',0,1,138,16,30,1,1,1
	.word	.L203,.L583,.L202
	.byte	4
	.word	.L203,.L583
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActSetReqOk')
	.sect	'.debug_abbrev'
.L257:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActSetReqOk')
	.sect	'.debug_line'
.L258:
	.word	.L777-.L776
.L776:
	.half	3
	.word	.L779-.L778
.L778:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L779:
	.byte	5,5,7,0,5,2
	.word	.L203
	.byte	3,139,16,1,5,43,9
	.half	.L780-.L203
	.byte	1,5,41,1,5,1,9
	.half	.L781-.L780
	.byte	3,1,1,7,9
	.half	.L260-.L781
	.byte	0,1,1
.L777:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActSetReqOk')
	.sect	'.debug_ranges'
.L259:
	.word	-1,.L203,0,.L260-.L203,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_BlockNotification')
	.sect	'.debug_info'
.L261:
	.word	360
	.half	3
	.word	.L262
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L264,.L263
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_BlockNotification',0,1,254,21,6,1,1,1
	.word	.L241,.L584,.L240
	.byte	4
	.byte	'BlockId',0,1,254,21,44
	.word	.L585,.L586
	.byte	4
	.byte	'ServiceId',0,1,254,21,71
	.word	.L587,.L588
	.byte	4
	.byte	'JobResult',0,1,254,21,104
	.word	.L587,.L589
	.byte	5
	.word	.L241,.L584
	.byte	6
	.byte	'orgBlockId',0,1,131,22,25
	.word	.L590,.L591
	.byte	6
	.byte	'blockDescriptorPtr',0,1,132,22,31
	.word	.L592,.L593
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_BlockNotification')
	.sect	'.debug_abbrev'
.L262:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_BlockNotification')
	.sect	'.debug_line'
.L263:
	.word	.L783-.L782
.L782:
	.half	3
	.word	.L785-.L784
.L784:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L785:
	.byte	5,6,7,0,5,2
	.word	.L241
	.byte	3,253,21,1,5,53,3,6,1,5,6,9
	.half	.L786-.L241
	.byte	3,122,1,5,53,3,6,1,5,38,9
	.half	.L745-.L786
	.byte	3,127,1,5,81,9
	.half	.L746-.L745
	.byte	3,1,1,5,80,9
	.half	.L787-.L746
	.byte	1,5,3,9
	.half	.L747-.L787
	.byte	3,2,1,5,9,7,9
	.half	.L748-.L747
	.byte	3,5,1,5,23,7,9
	.half	.L788-.L748
	.byte	3,1,1,5,20,9
	.half	.L749-.L788
	.byte	1,5,26,7,9
	.half	.L750-.L749
	.byte	3,1,1,5,12,9
	.half	.L751-.L750
	.byte	1,5,63,7,9
	.half	.L752-.L751
	.byte	1,5,103,9
	.half	.L789-.L752
	.byte	1,5,29,7,9
	.half	.L106-.L789
	.byte	3,3,1,5,7,9
	.half	.L790-.L106
	.byte	1,5,63,7,9
	.half	.L753-.L790
	.byte	3,2,1,5,29,9
	.half	.L108-.L753
	.byte	3,3,1,5,7,9
	.half	.L791-.L108
	.byte	1,5,75,7,9
	.half	.L754-.L791
	.byte	3,2,1,5,1,9
	.half	.L103-.L754
	.byte	3,10,1,7,9
	.half	.L265-.L103
	.byte	0,1,1
.L783:
	.sdecl	'.debug_ranges',debug,cluster('NvM_BlockNotification')
	.sect	'.debug_ranges'
.L264:
	.word	-1,.L241,0,.L265-.L241,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActSetInitialAttr')
	.sect	'.debug_info'
.L266:
	.word	235
	.half	3
	.word	.L267
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L269,.L268
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActSetInitialAttr',0,1,197,15,40,1,1
	.word	.L195,.L594,.L194
	.byte	4
	.word	.L195,.L594
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActSetInitialAttr')
	.sect	'.debug_abbrev'
.L267:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActSetInitialAttr')
	.sect	'.debug_line'
.L268:
	.word	.L793-.L792
.L792:
	.half	3
	.word	.L795-.L794
.L794:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L795:
	.byte	5,10,7,0,5,2
	.word	.L195
	.byte	3,201,15,1,5,27,9
	.half	.L796-.L195
	.byte	3,14,1,5,32,9
	.half	.L797-.L796
	.byte	3,114,1,5,46,9
	.half	.L798-.L797
	.byte	1,5,57,9
	.half	.L799-.L798
	.byte	1,5,84,9
	.half	.L800-.L799
	.byte	1,5,57,9
	.half	.L801-.L800
	.byte	3,14,1,5,1,9
	.half	.L802-.L801
	.byte	3,1,1,7,9
	.half	.L270-.L802
	.byte	0,1,1
.L793:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActSetInitialAttr')
	.sect	'.debug_ranges'
.L269:
	.word	-1,.L195,0,.L270-.L195,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitMainFsm')
	.sect	'.debug_info'
.L271:
	.word	228
	.half	3
	.word	.L272
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L274,.L273
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitMainFsm',0,1,139,11,40,1,1
	.word	.L145,.L595,.L144
	.byte	4
	.word	.L596
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitMainFsm')
	.sect	'.debug_abbrev'
.L272:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitMainFsm')
	.sect	'.debug_line'
.L273:
	.word	.L804-.L803
.L803:
	.half	3
	.word	.L806-.L805
.L805:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L806:
	.byte	5,21,7,0,5,2
	.word	.L145
	.byte	3,140,11,1,5,54,9
	.half	.L807-.L145
	.byte	3,1,1,5,70,9
	.half	.L808-.L807
	.byte	1,5,26,9
	.half	.L809-.L808
	.byte	1,5,5,9
	.half	.L810-.L809
	.byte	1,5,86,9
	.half	.L811-.L810
	.byte	1,5,24,9
	.half	.L812-.L811
	.byte	1,5,5,9
	.half	.L813-.L812
	.byte	3,5,1,5,25,9
	.half	.L814-.L813
	.byte	1,5,23,1,5,84,9
	.half	.L815-.L814
	.byte	3,1,1,5,5,9
	.half	.L816-.L815
	.byte	1,5,101,9
	.half	.L817-.L816
	.byte	1,5,1,7,9
	.half	.L275-.L817
	.byte	3,1,0,1,1
.L804:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitMainFsm')
	.sect	'.debug_ranges'
.L274:
	.word	-1,.L145,0,.L275-.L145,0,0
.L596:
	.word	-1,.L145,0,.L595-.L145,-1,.L147,0,.L515-.L147,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitBlock')
	.sect	'.debug_info'
.L276:
	.word	277
	.half	3
	.word	.L277
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L279,.L278
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitBlock',0,1,196,10,40,1,1
	.word	.L143,.L597,.L142
	.byte	4
	.word	.L143,.L597
	.byte	5
	.byte	'orgBlockId',0,1,199,10,27
	.word	.L598,.L599
	.byte	5
	.byte	'DescrPtr',0,1,200,10,33
	.word	.L600,.L601
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitBlock')
	.sect	'.debug_abbrev'
.L277:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitBlock')
	.sect	'.debug_line'
.L278:
	.word	.L819-.L818
.L818:
	.half	3
	.word	.L821-.L820
.L820:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L821:
	.byte	5,40,7,0,5,2
	.word	.L143
	.byte	3,198,10,1,5,45,9
	.half	.L822-.L143
	.byte	3,1,1,5,40,9
	.half	.L823-.L822
	.byte	3,127,1,5,45,3,1,1,5,73,9
	.half	.L718-.L823
	.byte	1,5,72,9
	.half	.L824-.L718
	.byte	1,5,10,9
	.half	.L720-.L824
	.byte	3,3,1,5,60,7,9
	.half	.L825-.L720
	.byte	1,5,81,9
	.half	.L826-.L825
	.byte	1,5,95,9
	.half	.L827-.L826
	.byte	1,5,99,9
	.half	.L35-.L827
	.byte	1,5,5,9
	.half	.L36-.L35
	.byte	3,127,1,5,27,9
	.half	.L828-.L36
	.byte	1,5,37,9
	.half	.L829-.L828
	.byte	1,5,43,9
	.half	.L830-.L829
	.byte	3,2,1,5,41,1,5,42,9
	.half	.L719-.L830
	.byte	3,1,1,5,27,9
	.half	.L831-.L719
	.byte	3,1,1,5,55,9
	.half	.L832-.L831
	.byte	1,5,45,9
	.half	.L833-.L832
	.byte	1,5,17,9
	.half	.L834-.L833
	.byte	3,2,1,5,5,9
	.half	.L835-.L834
	.byte	1,5,74,7,9
	.half	.L836-.L835
	.byte	3,2,1,5,31,9
	.half	.L837-.L836
	.byte	1,5,83,9
	.half	.L838-.L837
	.byte	1,5,49,9
	.half	.L839-.L838
	.byte	1,5,44,9
	.half	.L37-.L839
	.byte	3,2,1,5,42,1,5,27,9
	.half	.L840-.L37
	.byte	3,4,1,5,40,9
	.half	.L841-.L840
	.byte	1,5,38,9
	.half	.L842-.L841
	.byte	1,5,16,9
	.half	.L843-.L842
	.byte	3,2,1,5,5,9
	.half	.L844-.L843
	.byte	1,5,28,7,9
	.half	.L845-.L844
	.byte	3,2,1,5,9,9
	.half	.L846-.L845
	.byte	1,5,46,7,9
	.half	.L847-.L846
	.byte	3,2,1,5,74,9
	.half	.L848-.L847
	.byte	1,5,25,9
	.half	.L39-.L848
	.byte	3,3,1,5,14,9
	.half	.L849-.L39
	.byte	1,5,56,7,9
	.half	.L850-.L849
	.byte	3,2,1,5,46,9
	.half	.L851-.L850
	.byte	1,5,68,9
	.half	.L38-.L851
	.byte	3,20,1,5,44,9
	.half	.L852-.L38
	.byte	1,5,17,9
	.half	.L853-.L852
	.byte	3,3,1,5,5,9
	.half	.L854-.L853
	.byte	1,5,66,7,9
	.half	.L855-.L854
	.byte	3,3,1,5,31,3,127,1,5,45,9
	.half	.L856-.L855
	.byte	1,5,43,1,5,66,9
	.half	.L857-.L856
	.byte	3,1,1,5,1,7,9
	.half	.L42-.L857
	.byte	3,2,1,7,9
	.half	.L280-.L42
	.byte	0,1,1
.L819:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitBlock')
	.sect	'.debug_ranges'
.L279:
	.word	-1,.L143,0,.L280-.L143,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitReadAll')
	.sect	'.debug_info'
.L281:
	.word	258
	.half	3
	.word	.L282
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L284,.L283
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitReadAll',0,1,163,11,40,1,1
	.word	.L149,.L602,.L148
	.byte	4
	.word	.L149,.L602
	.byte	5
	.byte	'currBlockId',0,1,165,11,21
	.word	.L585,.L603
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitReadAll')
	.sect	'.debug_abbrev'
.L282:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitReadAll')
	.sect	'.debug_line'
.L283:
	.word	.L859-.L858
.L858:
	.half	3
	.word	.L861-.L860
.L860:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L861:
	.byte	5,35,7,0,5,2
	.word	.L149
	.byte	3,164,11,1,5,5,9
	.half	.L721-.L149
	.byte	3,3,1,5,27,9
	.half	.L862-.L721
	.byte	1,5,33,9
	.half	.L863-.L862
	.byte	3,125,1,5,45,9
	.half	.L864-.L863
	.byte	3,3,1,5,9,9
	.half	.L865-.L864
	.byte	3,5,1,5,30,9
	.half	.L866-.L865
	.byte	1,5,20,9
	.half	.L43-.L866
	.byte	3,127,1,5,64,9
	.half	.L722-.L43
	.byte	3,1,1,5,58,9
	.half	.L723-.L722
	.byte	3,1,1,5,44,9
	.half	.L867-.L723
	.byte	1,5,58,9
	.half	.L868-.L867
	.byte	1,5,30,9
	.half	.L869-.L868
	.byte	3,2,1,5,26,7,9
	.half	.L870-.L869
	.byte	3,2,1,5,19,9
	.half	.L871-.L870
	.byte	3,3,1,5,31,9
	.half	.L872-.L871
	.byte	3,3,1,5,1,7,9
	.half	.L285-.L872
	.byte	3,1,0,1,1
.L859:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitReadAll')
	.sect	'.debug_ranges'
.L284:
	.word	-1,.L149,0,.L285-.L149,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitReadBlockSubFsm')
	.sect	'.debug_info'
.L286:
	.word	236
	.half	3
	.word	.L287
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L289,.L288
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitReadBlockSubFsm',0,1,198,12,40,1,1
	.word	.L163,.L604,.L162
	.byte	4
	.word	.L605
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitReadBlockSubFsm')
	.sect	'.debug_abbrev'
.L287:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitReadBlockSubFsm')
	.sect	'.debug_line'
.L288:
	.word	.L874-.L873
.L873:
	.half	3
	.word	.L876-.L875
.L875:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L876:
	.byte	5,25,7,0,5,2
	.word	.L163
	.byte	3,199,12,1,5,76,9
	.half	.L877-.L163
	.byte	1,5,23,9
	.half	.L878-.L877
	.byte	1,5,75,9
	.half	.L879-.L878
	.byte	3,1,1,5,5,9
	.half	.L880-.L879
	.byte	1,5,92,9
	.half	.L881-.L880
	.byte	1,5,1,7,9
	.half	.L290-.L881
	.byte	3,1,0,1,1
.L874:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitReadBlockSubFsm')
	.sect	'.debug_ranges'
.L289:
	.word	-1,.L163,0,.L290-.L163,0,0
.L605:
	.word	-1,.L163,0,.L604-.L163,-1,.L165,0,.L520-.L165,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitRestoreBlockDefaultsSubFsm')
	.sect	'.debug_info'
.L291:
	.word	251
	.half	3
	.word	.L292
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L294,.L293
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitRestoreBlockDefaultsSubFsm',0,1,213,12,40,1,1
	.word	.L167,.L606,.L166
	.byte	4
	.word	.L167,.L606
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitRestoreBlockDefaultsSubFsm')
	.sect	'.debug_abbrev'
.L292:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitRestoreBlockDefaultsSubFsm')
	.sect	'.debug_line'
.L293:
	.word	.L883-.L882
.L882:
	.half	3
	.word	.L885-.L884
.L884:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L885:
	.byte	5,25,7,0,5,2
	.word	.L167
	.byte	3,214,12,1,5,82,9
	.half	.L886-.L167
	.byte	1,5,23,9
	.half	.L887-.L886
	.byte	1,5,81,9
	.half	.L888-.L887
	.byte	3,1,1,5,5,9
	.half	.L889-.L888
	.byte	1,5,98,9
	.half	.L890-.L889
	.byte	1,5,1,7,9
	.half	.L295-.L890
	.byte	3,1,0,1,1
.L883:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitRestoreBlockDefaultsSubFsm')
	.sect	'.debug_ranges'
.L294:
	.word	-1,.L167,0,.L295-.L167,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitWriteAll')
	.sect	'.debug_info'
.L296:
	.word	233
	.half	3
	.word	.L297
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L299,.L298
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitWriteAll',0,1,245,11,40,1,1
	.word	.L153,.L607,.L152
	.byte	4
	.word	.L153,.L607
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitWriteAll')
	.sect	'.debug_abbrev'
.L297:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitWriteAll')
	.sect	'.debug_line'
.L298:
	.word	.L892-.L891
.L891:
	.half	3
	.word	.L894-.L893
.L893:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L894:
	.byte	5,5,7,0,5,2
	.word	.L153
	.byte	3,247,11,1,5,19,9
	.half	.L895-.L153
	.byte	3,3,1,5,27,3,125,1,5,45,9
	.half	.L896-.L895
	.byte	1,5,19,9
	.half	.L897-.L896
	.byte	3,3,1,5,1,7,9
	.half	.L300-.L897
	.byte	3,2,0,1,1
.L892:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitWriteAll')
	.sect	'.debug_ranges'
.L299:
	.word	-1,.L153,0,.L300-.L153,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitWriteBlock')
	.sect	'.debug_info'
.L301:
	.word	231
	.half	3
	.word	.L302
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L304,.L303
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitWriteBlock',0,1,139,12,40,1,1
	.word	.L155,.L608,.L154
	.byte	4
	.word	.L609
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitWriteBlock')
	.sect	'.debug_abbrev'
.L302:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitWriteBlock')
	.sect	'.debug_line'
.L303:
	.word	.L899-.L898
.L898:
	.half	3
	.word	.L901-.L900
.L900:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L901:
	.byte	5,5,7,0,5,2
	.word	.L155
	.byte	3,140,12,1,5,31,9
	.half	.L902-.L155
	.byte	3,5,1,5,45,9
	.half	.L903-.L902
	.byte	1,5,5,9
	.half	.L904-.L903
	.byte	1,5,30,7,9
	.half	.L905-.L904
	.byte	3,2,1,5,66,9
	.half	.L49-.L905
	.byte	3,4,1,5,80,9
	.half	.L906-.L49
	.byte	1,5,42,9
	.half	.L907-.L906
	.byte	1,5,46,9
	.half	.L908-.L907
	.byte	3,4,1,5,45,9
	.half	.L909-.L908
	.byte	3,3,1,5,59,9
	.half	.L910-.L909
	.byte	1,5,27,9
	.half	.L911-.L910
	.byte	3,2,1,5,1,7,9
	.half	.L305-.L911
	.byte	3,1,0,1,1
.L899:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitWriteBlock')
	.sect	'.debug_ranges'
.L304:
	.word	-1,.L155,0,.L305-.L155,0,0
.L609:
	.word	-1,.L155,0,.L608-.L155,-1,.L157,0,.L565-.L157,-1,.L159,0,.L560-.L159,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitWriteBlockFsm')
	.sect	'.debug_info'
.L306:
	.word	238
	.half	3
	.word	.L307
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L309,.L308
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitWriteBlockFsm',0,1,229,12,40,1,1
	.word	.L169,.L610,.L168
	.byte	4
	.word	.L169,.L610
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitWriteBlockFsm')
	.sect	'.debug_abbrev'
.L307:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitWriteBlockFsm')
	.sect	'.debug_line'
.L308:
	.word	.L913-.L912
.L912:
	.half	3
	.word	.L915-.L914
.L914:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L915:
	.byte	5,25,7,0,5,2
	.word	.L169
	.byte	3,232,12,1,5,77,9
	.half	.L916-.L169
	.byte	1,5,23,9
	.half	.L917-.L916
	.byte	1,5,100,9
	.half	.L918-.L917
	.byte	3,126,1,5,5,9
	.half	.L919-.L918
	.byte	3,3,1,5,37,9
	.half	.L920-.L919
	.byte	1,5,1,7,9
	.half	.L310-.L920
	.byte	3,1,0,1,1
.L913:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitWriteBlockFsm')
	.sect	'.debug_ranges'
.L309:
	.word	-1,.L169,0,.L310-.L169,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitRestoreBlockDefaults')
	.sect	'.debug_info'
.L311:
	.word	241
	.half	3
	.word	.L312
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L314,.L313
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitRestoreBlockDefaults',0,1,245,12,40,1,1
	.word	.L171,.L611,.L170
	.byte	4
	.word	.L612
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitRestoreBlockDefaults')
	.sect	'.debug_abbrev'
.L312:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitRestoreBlockDefaults')
	.sect	'.debug_line'
.L313:
	.word	.L922-.L921
.L921:
	.half	3
	.word	.L924-.L923
.L923:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L924:
	.byte	5,44,7,0,5,2
	.word	.L171
	.byte	3,248,12,1,5,1,9
	.half	.L925-.L171
	.byte	3,1,1,7,9
	.half	.L315-.L925
	.byte	0,1,1
.L922:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitRestoreBlockDefaults')
	.sect	'.debug_ranges'
.L314:
	.word	-1,.L171,0,.L315-.L171,0,0
.L612:
	.word	-1,.L171,0,.L611-.L171,-1,.L173,0,.L525-.L173,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActFinishMainJob')
	.sect	'.debug_info'
.L316:
	.word	291
	.half	3
	.word	.L317
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L319,.L318
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActFinishMainJob',0,1,178,8,40,1,1
	.word	.L131,.L613,.L130
	.byte	4
	.word	.L131,.L613
	.byte	5
	.byte	'currSrvId',0,1,180,8,37
	.word	.L614,.L615
	.byte	4
	.word	.L6,.L616
	.byte	5
	.byte	'JobResult',0,1,188,8,35
	.word	.L587,.L617
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActFinishMainJob')
	.sect	'.debug_abbrev'
.L317:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActFinishMainJob')
	.sect	'.debug_line'
.L318:
	.word	.L927-.L926
.L926:
	.half	3
	.word	.L929-.L928
.L928:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L929:
	.byte	5,49,7,0,5,2
	.word	.L131
	.byte	3,179,8,1,5,65,9
	.half	.L930-.L131
	.byte	1,5,12,9
	.half	.L709-.L930
	.byte	3,6,1,5,62,7,9
	.half	.L931-.L709
	.byte	1,5,16,7,9
	.half	.L6-.L931
	.byte	3,4,1,5,58,7,9
	.half	.L932-.L6
	.byte	1,5,107,9
	.half	.L933-.L932
	.byte	1,5,58,7,9
	.half	.L8-.L933
	.byte	1,5,21,9
	.half	.L934-.L8
	.byte	3,5,1,5,81,7,9
	.half	.L935-.L934
	.byte	1,5,84,9
	.half	.L936-.L935
	.byte	1,5,27,7,9
	.half	.L9-.L936
	.byte	3,2,1,5,45,1,5,22,9
	.half	.L10-.L9
	.byte	3,2,1,5,44,9
	.half	.L937-.L10
	.byte	1,5,62,9
	.half	.L938-.L937
	.byte	1,5,87,9
	.half	.L939-.L938
	.byte	1,5,13,9
	.half	.L12-.L939
	.byte	3,9,1,5,58,9
	.half	.L940-.L12
	.byte	1,5,31,9
	.half	.L941-.L940
	.byte	3,3,1,5,69,9
	.half	.L942-.L941
	.byte	1,5,83,9
	.half	.L943-.L942
	.byte	1,5,37,9
	.half	.L708-.L943
	.byte	3,2,1,5,13,9
	.half	.L944-.L708
	.byte	3,2,1,5,29,9
	.half	.L710-.L944
	.byte	1,5,36,9
	.half	.L945-.L710
	.byte	3,2,1,5,27,9
	.half	.L946-.L945
	.byte	3,4,1,5,69,9
	.half	.L616-.L946
	.byte	3,115,1,5,31,9
	.half	.L7-.L616
	.byte	3,18,1,5,39,9
	.half	.L13-.L7
	.byte	3,4,1,5,37,1,5,1,9
	.half	.L947-.L13
	.byte	3,1,1,7,9
	.half	.L320-.L947
	.byte	0,1,1
.L927:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActFinishMainJob')
	.sect	'.debug_ranges'
.L319:
	.word	-1,.L131,0,.L320-.L131,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActKillWriteAll')
	.sect	'.debug_info'
.L321:
	.word	233
	.half	3
	.word	.L322
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L324,.L323
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActKillWriteAll',0,1,246,8,40,1,1
	.word	.L133,.L618,.L132
	.byte	4
	.word	.L133,.L618
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActKillWriteAll')
	.sect	'.debug_abbrev'
.L322:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActKillWriteAll')
	.sect	'.debug_line'
.L323:
	.word	.L949-.L948
.L948:
	.half	3
	.word	.L951-.L950
.L950:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L951:
	.byte	5,19,7,0,5,2
	.word	.L133
	.byte	3,248,8,1,5,20,9
	.half	.L952-.L133
	.byte	3,2,1,5,5,9
	.half	.L953-.L952
	.byte	3,2,1,5,39,9
	.half	.L954-.L953
	.byte	1,5,37,1,5,5,9
	.half	.L955-.L954
	.byte	3,3,1,5,26,9
	.half	.L956-.L955
	.byte	1,5,24,1,5,5,9
	.half	.L957-.L956
	.byte	3,1,1,5,23,9
	.half	.L958-.L957
	.byte	1,5,29,9
	.half	.L959-.L958
	.byte	3,2,1,5,5,9
	.half	.L960-.L959
	.byte	3,3,1,5,21,9
	.half	.L961-.L960
	.byte	1,5,28,9
	.half	.L962-.L961
	.byte	3,2,1,5,1,7,9
	.half	.L325-.L962
	.byte	3,1,0,1,1
.L949:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActKillWriteAll')
	.sect	'.debug_ranges'
.L324:
	.word	-1,.L133,0,.L325-.L133,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActFinishBlock')
	.sect	'.debug_info'
.L326:
	.word	228
	.half	3
	.word	.L327
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L329,.L328
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActFinishBlock',0,1,212,7,40,1,1
	.word	.L119,.L619,.L118
	.byte	4
	.word	.L620
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActFinishBlock')
	.sect	'.debug_abbrev'
.L327:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActFinishBlock')
	.sect	'.debug_line'
.L328:
	.word	.L964-.L963
.L963:
	.half	3
	.word	.L966-.L965
.L965:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L966:
	.byte	5,33,7,0,5,2
	.word	.L119
	.byte	3,215,7,1,5,49,9
	.half	.L967-.L119
	.byte	1,5,66,9
	.half	.L968-.L967
	.byte	1,5,9,9
	.half	.L969-.L968
	.byte	1,5,64,7,9
	.half	.L970-.L969
	.byte	3,2,1,5,31,9
	.half	.L971-.L970
	.byte	1,5,86,9
	.half	.L972-.L971
	.byte	1,5,62,9
	.half	.L973-.L972
	.byte	1,5,25,9
	.half	.L2-.L973
	.byte	3,8,1,5,64,9
	.half	.L974-.L2
	.byte	3,120,1,5,86,9
	.half	.L975-.L974
	.byte	1,5,8,9
	.half	.L976-.L975
	.byte	3,8,1,5,93,7,9
	.half	.L977-.L976
	.byte	1,5,107,9
	.half	.L978-.L977
	.byte	1,5,47,7,9
	.half	.L3-.L978
	.byte	3,2,1,5,31,9
	.half	.L979-.L3
	.byte	3,1,1,5,91,9
	.half	.L980-.L979
	.byte	1,5,53,9
	.half	.L981-.L980
	.byte	3,1,1,5,34,9
	.half	.L4-.L981
	.byte	3,4,1,5,32,9
	.half	.L982-.L4
	.byte	1,5,1,9
	.half	.L983-.L982
	.byte	3,1,1,7,9
	.half	.L330-.L983
	.byte	0,1,1
.L964:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActFinishBlock')
	.sect	'.debug_ranges'
.L329:
	.word	-1,.L119,0,.L330-.L119,0,0
.L620:
	.word	-1,.L119,0,.L619-.L119,-1,.L121,0,.L540-.L121,-1,.L123,0,.L535-.L123,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitNextBlockReadAll')
	.sect	'.debug_info'
.L331:
	.word	237
	.half	3
	.word	.L332
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L334,.L333
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitNextBlockReadAll',0,1,250,7,40,1,1
	.word	.L125,.L621,.L124
	.byte	4
	.word	.L622
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitNextBlockReadAll')
	.sect	'.debug_abbrev'
.L332:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitNextBlockReadAll')
	.sect	'.debug_line'
.L333:
	.word	.L985-.L984
.L984:
	.half	3
	.word	.L987-.L986
.L986:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L987:
	.byte	5,23,7,0,5,2
	.word	.L125
	.byte	3,251,7,1,5,5,9
	.half	.L988-.L125
	.byte	3,1,1,5,21,9
	.half	.L989-.L988
	.byte	1,5,40,9
	.half	.L990-.L989
	.byte	3,1,1,5,34,9
	.half	.L991-.L990
	.byte	3,127,1,5,24,9
	.half	.L992-.L991
	.byte	3,1,1,5,40,9
	.half	.L993-.L992
	.byte	1,5,5,9
	.half	.L994-.L993
	.byte	1,5,25,7,9
	.half	.L995-.L994
	.byte	3,2,1,5,28,9
	.half	.L996-.L995
	.byte	3,3,1,5,50,9
	.half	.L997-.L996
	.byte	1,9
	.half	.L998-.L997
	.byte	3,126,1,5,82,9
	.half	.L999-.L998
	.byte	1,5,64,9
	.half	.L1000-.L999
	.byte	3,1,1,9
	.half	.L1001-.L1000
	.byte	3,1,1,5,5,9
	.half	.L1002-.L1001
	.byte	3,6,1,5,1,9
	.half	.L5-.L1002
	.byte	3,4,1,7,9
	.half	.L335-.L5
	.byte	0,1,1
.L985:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitNextBlockReadAll')
	.sect	'.debug_ranges'
.L334:
	.word	-1,.L125,0,.L335-.L125,0,0
.L622:
	.word	-1,.L125,0,.L621-.L125,-1,.L127,0,.L545-.L127,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInitNextBlockWriteAll')
	.sect	'.debug_info'
.L336:
	.word	242
	.half	3
	.word	.L337
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L339,.L338
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInitNextBlockWriteAll',0,1,153,8,40,1,1
	.word	.L129,.L623,.L128
	.byte	4
	.word	.L129,.L623
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInitNextBlockWriteAll')
	.sect	'.debug_abbrev'
.L337:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInitNextBlockWriteAll')
	.sect	'.debug_line'
.L338:
	.word	.L1004-.L1003
.L1003:
	.half	3
	.word	.L1006-.L1005
.L1005:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1006:
	.byte	5,23,7,0,5,2
	.word	.L129
	.byte	3,154,8,1,5,7,9
	.half	.L1007-.L129
	.byte	3,2,1,5,23,9
	.half	.L1008-.L1007
	.byte	1,5,5,9
	.half	.L1009-.L1008
	.byte	1,5,21,9
	.half	.L1010-.L1009
	.byte	3,2,1,5,1,7,9
	.half	.L340-.L1010
	.byte	3,1,0,1,1
.L1004:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInitNextBlockWriteAll')
	.sect	'.debug_ranges'
.L339:
	.word	-1,.L129,0,.L340-.L129,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActFinishCfgIdCheck')
	.sect	'.debug_info'
.L341:
	.word	237
	.half	3
	.word	.L342
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L344,.L343
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActFinishCfgIdCheck',0,1,202,11,40,1,1
	.word	.L151,.L624,.L150
	.byte	4
	.word	.L151,.L624
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActFinishCfgIdCheck')
	.sect	'.debug_abbrev'
.L342:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActFinishCfgIdCheck')
	.sect	'.debug_line'
.L343:
	.word	.L1012-.L1011
.L1011:
	.half	3
	.word	.L1014-.L1013
.L1013:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1014:
	.byte	5,5,7,0,5,2
	.word	.L151
	.byte	3,204,11,1,5,27,9
	.half	.L1015-.L151
	.byte	1,5,45,9
	.half	.L1016-.L1015
	.byte	1,5,30,9
	.half	.L1017-.L1016
	.byte	3,2,1,5,5,9
	.half	.L1018-.L1017
	.byte	1,5,74,7,9
	.half	.L1019-.L1018
	.byte	3,2,1,5,13,9
	.half	.L1020-.L1019
	.byte	1,5,45,9
	.half	.L1021-.L1020
	.byte	1,5,84,9
	.half	.L1022-.L1021
	.byte	1,5,12,9
	.half	.L1023-.L1022
	.byte	1,5,45,7,9
	.half	.L1024-.L1023
	.byte	3,1,1,5,84,9
	.half	.L1025-.L1024
	.byte	1,5,49,9
	.half	.L1026-.L1025
	.byte	1,5,51,7,9
	.half	.L45-.L1026
	.byte	3,2,1,5,9,3,125,1,5,1,9
	.half	.L46-.L45
	.byte	3,25,1,5,10,7,9
	.half	.L44-.L46
	.byte	3,115,1,5,47,7,9
	.half	.L1027-.L44
	.byte	3,6,1,5,45,9
	.half	.L47-.L1027
	.byte	1,5,31,9
	.half	.L1028-.L47
	.byte	3,2,1,5,49,9
	.half	.L1029-.L1028
	.byte	1,5,32,9
	.half	.L48-.L1029
	.byte	3,2,1,5,1,7,9
	.half	.L345-.L48
	.byte	3,3,0,1,1
.L1012:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActFinishCfgIdCheck')
	.sect	'.debug_ranges'
.L344:
	.word	-1,.L151,0,.L345-.L151,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActFinishReadBlock')
	.sect	'.debug_info'
.L346:
	.word	277
	.half	3
	.word	.L347
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L349,.L348
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActFinishReadBlock',0,1,158,9,40,1,1
	.word	.L135,.L625,.L134
	.byte	4
	.word	.L626
	.byte	5
	.byte	'MngmtPtr',0,1,160,9,31
	.word	.L627,.L628
	.byte	5
	.byte	'descr_pt',0,1,161,9,33
	.word	.L629,.L630
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActFinishReadBlock')
	.sect	'.debug_abbrev'
.L347:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActFinishReadBlock')
	.sect	'.debug_line'
.L348:
	.word	.L1031-.L1030
.L1030:
	.half	3
	.word	.L1033-.L1032
.L1032:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1033:
	.byte	5,42,7,0,5,2
	.word	.L135
	.byte	3,159,9,1,5,64,9
	.half	.L1034-.L135
	.byte	1,5,66,9
	.half	.L711-.L1034
	.byte	3,1,1,5,25,9
	.half	.L714-.L711
	.byte	3,2,1,5,18,9
	.half	.L1035-.L714
	.byte	3,2,1,5,8,9
	.half	.L1036-.L1035
	.byte	1,5,9,7,9
	.half	.L1037-.L1036
	.byte	3,1,1,5,68,9
	.half	.L1038-.L1037
	.byte	1,5,9,7,9
	.half	.L1039-.L1038
	.byte	3,1,1,5,68,9
	.half	.L1040-.L1039
	.byte	1,5,47,7,9
	.half	.L1041-.L1040
	.byte	3,2,1,5,45,1,5,17,9
	.half	.L14-.L1041
	.byte	3,3,1,5,31,9
	.half	.L1042-.L14
	.byte	3,125,1,5,5,9
	.half	.L1043-.L1042
	.byte	3,3,1,5,34,7,9
	.half	.L1044-.L1043
	.byte	3,2,1,5,21,9
	.half	.L1045-.L1044
	.byte	3,2,1,5,9,9
	.half	.L1046-.L1045
	.byte	3,126,1,5,42,7,9
	.half	.L1047-.L1046
	.byte	3,2,1,5,60,1,5,42,9
	.half	.L18-.L1047
	.byte	3,4,1,5,67,9
	.half	.L17-.L18
	.byte	3,4,1,5,30,9
	.half	.L1048-.L17
	.byte	3,2,1,5,5,9
	.half	.L1049-.L1048
	.byte	1,5,31,7,9
	.half	.L1050-.L1049
	.byte	3,2,1,5,49,9
	.half	.L1051-.L1050
	.byte	1,5,31,9
	.half	.L20-.L1051
	.byte	3,18,1,5,8,9
	.half	.L1052-.L20
	.byte	1,5,9,7,9
	.half	.L1053-.L1052
	.byte	3,1,1,5,36,9
	.half	.L712-.L1053
	.byte	1,5,87,7,9
	.half	.L1054-.L712
	.byte	3,2,1,5,63,9
	.half	.L1055-.L1054
	.byte	1,5,87,9
	.half	.L1056-.L1055
	.byte	1,5,1,7,9
	.half	.L21-.L1056
	.byte	3,3,1,7,9
	.half	.L350-.L21
	.byte	0,1,1
.L1031:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActFinishReadBlock')
	.sect	'.debug_ranges'
.L349:
	.word	-1,.L135,0,.L350-.L135,0,0
.L626:
	.word	-1,.L135,0,.L625-.L135,-1,.L137,0,.L570-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActFinishWriteBlock')
	.sect	'.debug_info'
.L351:
	.word	327
	.half	3
	.word	.L352
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L354,.L353
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActFinishWriteBlock',0,1,250,9,40,1,1
	.word	.L141,.L631,.L140
	.byte	4
	.word	.L141,.L631
	.byte	5
	.byte	'MngmtPtr',0,1,252,9,31
	.word	.L632,.L633
	.byte	6
	.word	.L634
	.byte	5
	.byte	'firstBlockDefect',0,1,130,10,17
	.word	.L587,.L638
	.byte	5
	.byte	'secondBlockDefect',0,1,131,10,17
	.word	.L587,.L639
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActFinishWriteBlock')
	.sect	'.debug_abbrev'
.L352:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,11,1,85,6,0
	.byte	0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActFinishWriteBlock')
	.sect	'.debug_line'
.L353:
	.word	.L1058-.L1057
.L1057:
	.half	3
	.word	.L1060-.L1059
.L1059:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1060:
	.byte	5,42,7,0,5,2
	.word	.L141
	.byte	3,251,9,1,5,64,9
	.half	.L1061-.L141
	.byte	1,5,25,9
	.half	.L715-.L1061
	.byte	3,2,1,5,31,9
	.half	.L1062-.L715
	.byte	3,2,1,5,35,9
	.half	.L635-.L1062
	.byte	3,8,1,5,45,9
	.half	.L636-.L635
	.byte	3,120,1,5,5,9
	.half	.L1063-.L636
	.byte	1,5,46,7,9
	.half	.L637-.L1063
	.byte	3,2,1,5,105,9
	.half	.L1064-.L637
	.byte	1,5,47,9
	.half	.L716-.L1064
	.byte	3,1,1,5,106,9
	.half	.L1065-.L716
	.byte	1,5,12,9
	.half	.L717-.L1065
	.byte	3,3,1,5,32,7,9
	.half	.L1066-.L717
	.byte	1,5,49,7,9
	.half	.L1067-.L1066
	.byte	3,2,1,5,65,1,5,49,9
	.half	.L24-.L1067
	.byte	3,5,1,5,50,9
	.half	.L26-.L24
	.byte	3,8,1,5,70,9
	.half	.L1068-.L26
	.byte	1,5,89,7,9
	.half	.L27-.L1068
	.byte	1,5,106,1,5,89,9
	.half	.L28-.L27
	.byte	1,5,119,1,5,71,9
	.half	.L23-.L28
	.byte	3,4,1,5,30,9
	.half	.L1069-.L23
	.byte	3,3,1,5,5,9
	.half	.L1070-.L1069
	.byte	1,5,12,7,9
	.half	.L1071-.L1070
	.byte	3,3,1,5,28,9
	.half	.L1072-.L1071
	.byte	1,5,9,9
	.half	.L1073-.L1072
	.byte	1,5,21,7,9
	.half	.L1074-.L1073
	.byte	3,2,1,5,42,9
	.half	.L1075-.L1074
	.byte	1,5,35,9
	.half	.L32-.L1075
	.byte	3,3,1,5,49,9
	.half	.L1076-.L32
	.byte	1,5,9,9
	.half	.L1077-.L1076
	.byte	1,5,21,7,9
	.half	.L1078-.L1077
	.byte	3,2,1,5,42,9
	.half	.L1079-.L1078
	.byte	1,5,1,9
	.half	.L33-.L1079
	.byte	3,8,1,5,31,7,9
	.half	.L31-.L33
	.byte	3,125,1,5,49,9
	.half	.L1080-.L31
	.byte	1,5,1,9
	.half	.L1081-.L1080
	.byte	3,3,1,7,9
	.half	.L355-.L1081
	.byte	0,1,1
.L1058:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActFinishWriteBlock')
	.sect	'.debug_ranges'
.L354:
	.word	-1,.L141,0,.L355-.L141,0,0
.L634:
	.word	-1,.L141,.L635-.L141,.L636-.L141,.L637-.L141,.L23-.L141,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActFinishEraseBlock')
	.sect	'.debug_info'
.L356:
	.word	237
	.half	3
	.word	.L357
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L359,.L358
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActFinishEraseBlock',0,1,177,12,40,1,1
	.word	.L161,.L640,.L160
	.byte	4
	.word	.L161,.L640
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActFinishEraseBlock')
	.sect	'.debug_abbrev'
.L357:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActFinishEraseBlock')
	.sect	'.debug_line'
.L358:
	.word	.L1083-.L1082
.L1082:
	.half	3
	.word	.L1085-.L1084
.L1084:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1085:
	.byte	5,8,7,0,5,2
	.word	.L161
	.byte	3,178,12,1,5,30,9
	.half	.L1086-.L161
	.byte	1,5,5,9
	.half	.L1087-.L1086
	.byte	1,5,47,7,9
	.half	.L1088-.L1087
	.byte	3,3,1,5,45,1,9
	.half	.L50-.L1088
	.byte	3,3,1,5,1,9
	.half	.L360-.L50
	.byte	3,1,0,1,1
.L1083:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActFinishEraseBlock')
	.sect	'.debug_ranges'
.L359:
	.word	-1,.L161,0,.L360-.L161,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActEraseNvBlock')
	.sect	'.debug_info'
.L361:
	.word	229
	.half	3
	.word	.L362
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L364,.L363
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActEraseNvBlock',0,1,188,7,40,1,1
	.word	.L111,.L641,.L110
	.byte	4
	.word	.L642
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActEraseNvBlock')
	.sect	'.debug_abbrev'
.L362:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActEraseNvBlock')
	.sect	'.debug_line'
.L363:
	.word	.L1090-.L1089
.L1089:
	.half	3
	.word	.L1092-.L1091
.L1091:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1092:
	.byte	5,43,7,0,5,2
	.word	.L111
	.byte	3,190,7,1,5,65,9
	.half	.L1093-.L111
	.byte	1,5,42,9
	.half	.L1094-.L1093
	.byte	3,127,1,5,1,9
	.half	.L1095-.L1094
	.byte	3,9,1,7,9
	.half	.L365-.L1095
	.byte	0,1,1
.L1090:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActEraseNvBlock')
	.sect	'.debug_ranges'
.L364:
	.word	-1,.L111,0,.L365-.L111,0,0
.L642:
	.word	-1,.L111,0,.L641-.L111,-1,.L113,0,.L530-.L113,-1,.L115,0,.L510-.L115,-1,.L117,0,.L575-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActInvalidateNvBlock')
	.sect	'.debug_info'
.L366:
	.word	238
	.half	3
	.word	.L367
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L369,.L368
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActInvalidateNvBlock',0,1,134,13,40,1,1
	.word	.L175,.L643,.L174
	.byte	4
	.word	.L175,.L643
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActInvalidateNvBlock')
	.sect	'.debug_abbrev'
.L367:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActInvalidateNvBlock')
	.sect	'.debug_line'
.L368:
	.word	.L1097-.L1096
.L1096:
	.half	3
	.word	.L1099-.L1098
.L1098:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1099:
	.byte	5,39,7,0,5,2
	.word	.L175
	.byte	3,136,13,1,5,61,9
	.half	.L1100-.L175
	.byte	1,5,38,9
	.half	.L1101-.L1100
	.byte	3,127,1,5,1,9
	.half	.L1102-.L1101
	.byte	3,9,1,7,9
	.half	.L370-.L1102
	.byte	0,1,1
.L1097:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActInvalidateNvBlock')
	.sect	'.debug_ranges'
.L369:
	.word	-1,.L175,0,.L370-.L175,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActProcessCrc')
	.sect	'.debug_info'
.L371:
	.word	231
	.half	3
	.word	.L372
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L374,.L373
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActProcessCrc',0,1,136,21,40,1,1
	.word	.L231,.L644,.L230
	.byte	4
	.word	.L231,.L644
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActProcessCrc')
	.sect	'.debug_abbrev'
.L372:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActProcessCrc')
	.sect	'.debug_line'
.L373:
	.word	.L1104-.L1103
.L1103:
	.half	3
	.word	.L1106-.L1105
.L1105:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1106:
	.byte	5,25,7,0,5,2
	.word	.L231
	.byte	3,137,21,1,5,63,9
	.half	.L1107-.L231
	.byte	1,5,1,7,9
	.half	.L375-.L1107
	.byte	3,1,0,1,1
.L1104:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActProcessCrc')
	.sect	'.debug_ranges'
.L374:
	.word	-1,.L231,0,.L375-.L231,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActWriteNvBlock')
	.sect	'.debug_info'
.L376:
	.word	279
	.half	3
	.word	.L377
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L379,.L378
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActWriteNvBlock',0,1,179,13,40,1,1
	.word	.L179,.L645,.L178
	.byte	4
	.word	.L179,.L645
	.byte	5
	.byte	'src_pt',0,1,181,13,30
	.word	.L646,.L647
	.byte	5
	.byte	'retValMemIf',0,1,182,13,20
	.word	.L587,.L648
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActWriteNvBlock')
	.sect	'.debug_abbrev'
.L377:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActWriteNvBlock')
	.sect	'.debug_line'
.L378:
	.word	.L1109-.L1108
.L1108:
	.half	3
	.word	.L1111-.L1110
.L1110:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1111:
	.byte	5,39,7,0,5,2
	.word	.L179
	.byte	3,180,13,1,5,27,9
	.half	.L1112-.L179
	.byte	3,3,1,5,45,9
	.half	.L1113-.L1112
	.byte	3,4,1,5,61,3,121,1,5,48,9
	.half	.L724-.L1113
	.byte	3,3,1,5,45,9
	.half	.L1114-.L724
	.byte	3,4,1,5,52,9
	.half	.L1115-.L1114
	.byte	3,3,1,5,8,9
	.half	.L1116-.L1115
	.byte	3,5,1,5,24,9
	.half	.L1117-.L1116
	.byte	1,5,5,9
	.half	.L1118-.L1117
	.byte	1,5,63,7,9
	.half	.L1119-.L1118
	.byte	3,3,1,5,77,9
	.half	.L1120-.L1119
	.byte	1,5,29,9
	.half	.L51-.L1120
	.byte	3,8,1,5,33,9
	.half	.L1121-.L51
	.byte	3,14,1,5,48,9
	.half	.L1122-.L1121
	.byte	3,116,1,5,65,9
	.half	.L1123-.L1122
	.byte	1,5,5,9
	.half	.L1124-.L1123
	.byte	1,5,64,7,9
	.half	.L1125-.L1124
	.byte	3,8,1,5,79,9
	.half	.L1126-.L1125
	.byte	3,1,1,5,98,9
	.half	.L1127-.L1126
	.byte	1,5,78,9
	.half	.L725-.L1127
	.byte	3,127,1,5,98,9
	.half	.L1128-.L725
	.byte	3,1,1,5,9,9
	.half	.L726-.L1128
	.byte	3,1,1,5,49,7,9
	.half	.L52-.L726
	.byte	3,2,1,5,64,1,5,49,9
	.half	.L53-.L52
	.byte	3,4,1,5,47,9
	.half	.L54-.L53
	.byte	1,5,28,9
	.half	.L1129-.L54
	.byte	3,5,1,5,1,7,9
	.half	.L380-.L1129
	.byte	3,2,0,1,1
.L1109:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActWriteNvBlock')
	.sect	'.debug_ranges'
.L379:
	.word	-1,.L179,0,.L380-.L179,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActReadNvBlock')
	.sect	'.debug_info'
.L381:
	.word	255
	.half	3
	.word	.L382
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L384,.L383
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActReadNvBlock',0,1,247,13,40,1,1
	.word	.L181,.L649,.L180
	.byte	4
	.word	.L181,.L649
	.byte	5
	.byte	'descr_pt',0,1,249,13,33
	.word	.L650,.L651
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActReadNvBlock')
	.sect	'.debug_abbrev'
.L382:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActReadNvBlock')
	.sect	'.debug_line'
.L383:
	.word	.L1131-.L1130
.L1130:
	.half	3
	.word	.L1133-.L1132
.L1132:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1133:
	.byte	5,44,7,0,5,2
	.word	.L181
	.byte	3,248,13,1,5,66,9
	.half	.L1134-.L181
	.byte	1,5,46,9
	.half	.L727-.L1134
	.byte	3,11,1,5,53,9
	.half	.L1135-.L727
	.byte	3,1,1,5,52,9
	.half	.L1136-.L1135
	.byte	3,3,1,5,42,9
	.half	.L1137-.L1136
	.byte	1,5,50,9
	.half	.L1138-.L1137
	.byte	3,3,1,5,53,9
	.half	.L1139-.L1138
	.byte	3,1,1,5,43,9
	.half	.L1140-.L1139
	.byte	3,126,1,5,31,9
	.half	.L1141-.L1140
	.byte	3,1,1,5,43,9
	.half	.L1142-.L1141
	.byte	3,127,1,5,26,3,2,1,5,53,9
	.half	.L1143-.L1142
	.byte	1,5,76,9
	.half	.L1144-.L1143
	.byte	3,127,1,5,51,9
	.half	.L1145-.L1144
	.byte	3,1,1,5,9,9
	.half	.L1146-.L1145
	.byte	1,5,27,9
	.half	.L728-.L1146
	.byte	3,126,1,5,1,9
	.half	.L729-.L728
	.byte	3,10,1,7,9
	.half	.L385-.L729
	.byte	0,1,1
.L1131:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActReadNvBlock')
	.sect	'.debug_ranges'
.L384:
	.word	-1,.L181,0,.L385-.L181,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActProcessCrcRead')
	.sect	'.debug_info'
.L386:
	.word	231
	.half	3
	.word	.L387
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L389,.L388
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActProcessCrcRead',0,1,158,14,40,1,1
	.word	.L183,.L652,.L182
	.byte	4
	.word	.L653
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActProcessCrcRead')
	.sect	'.debug_abbrev'
.L387:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActProcessCrcRead')
	.sect	'.debug_line'
.L388:
	.word	.L1148-.L1147
.L1147:
	.half	3
	.word	.L1150-.L1149
.L1149:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1150:
	.byte	5,26,7,0,5,2
	.word	.L183
	.byte	3,159,14,1,5,65,9
	.half	.L1151-.L183
	.byte	1,5,1,7,9
	.half	.L390-.L1151
	.byte	3,1,0,1,1
.L1148:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActProcessCrcRead')
	.sect	'.debug_ranges'
.L389:
	.word	-1,.L183,0,.L390-.L183,0,0
.L653:
	.word	-1,.L183,0,.L652-.L183,-1,.L185,0,.L550-.L185,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActReadCopyData')
	.sect	'.debug_info'
.L391:
	.word	233
	.half	3
	.word	.L392
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L394,.L393
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActReadCopyData',0,1,171,14,40,1,1
	.word	.L187,.L654,.L186
	.byte	4
	.word	.L187,.L654
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActReadCopyData')
	.sect	'.debug_abbrev'
.L392:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActReadCopyData')
	.sect	'.debug_line'
.L393:
	.word	.L1153-.L1152
.L1152:
	.half	3
	.word	.L1155-.L1154
.L1154:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1155:
	.byte	5,59,7,0,5,2
	.word	.L187
	.byte	3,174,14,1,5,81,9
	.half	.L1156-.L187
	.byte	1,5,1,9
	.half	.L395-.L1156
	.byte	3,4,0,1,1
.L1153:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActReadCopyData')
	.sect	'.debug_ranges'
.L394:
	.word	-1,.L187,0,.L395-.L187,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActRestoreRomDefaults')
	.sect	'.debug_info'
.L396:
	.word	239
	.half	3
	.word	.L397
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L399,.L398
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActRestoreRomDefaults',0,1,240,14,40,1,1
	.word	.L191,.L655,.L190
	.byte	4
	.word	.L191,.L655
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActRestoreRomDefaults')
	.sect	'.debug_abbrev'
.L397:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActRestoreRomDefaults')
	.sect	'.debug_line'
.L398:
	.word	.L1158-.L1157
.L1157:
	.half	3
	.word	.L1160-.L1159
.L1159:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1160:
	.byte	5,60,7,0,5,2
	.word	.L191
	.byte	3,242,14,1,5,17,9
	.half	.L1161-.L191
	.byte	3,1,1,5,39,9
	.half	.L1162-.L1161
	.byte	1,5,44,9
	.half	.L1163-.L1162
	.byte	3,127,1,5,76,9
	.half	.L1164-.L1163
	.byte	1,5,7,9
	.half	.L1165-.L1164
	.byte	3,1,1,5,53,9
	.half	.L1166-.L1165
	.byte	1,5,28,9
	.half	.L1167-.L1166
	.byte	3,2,1,5,42,9
	.half	.L1168-.L1167
	.byte	1,5,3,9
	.half	.L1169-.L1168
	.byte	1,5,45,7,9
	.half	.L1170-.L1169
	.byte	3,4,1,5,67,9
	.half	.L1171-.L1170
	.byte	1,5,63,9
	.half	.L63-.L1171
	.byte	3,5,1,5,5,9
	.half	.L1172-.L63
	.byte	1,5,29,7,9
	.half	.L1173-.L1172
	.byte	3,3,1,5,12,9
	.half	.L1174-.L1173
	.byte	1,5,35,7,9
	.half	.L1175-.L1174
	.byte	3,1,1,5,49,9
	.half	.L1176-.L1175
	.byte	1,5,89,9
	.half	.L1177-.L1176
	.byte	1,5,28,7,9
	.half	.L66-.L1177
	.byte	3,115,1,5,49,9
	.half	.L1178-.L66
	.byte	3,16,1,5,9,9
	.half	.L1179-.L1178
	.byte	1,5,31,7,9
	.half	.L1180-.L1179
	.byte	3,9,1,5,37,9
	.half	.L1181-.L1180
	.byte	3,1,1,5,51,9
	.half	.L1182-.L1181
	.byte	3,1,1,5,71,9
	.half	.L1183-.L1182
	.byte	1,5,53,9
	.half	.L68-.L1183
	.byte	3,4,1,5,69,9
	.half	.L1184-.L68
	.byte	1,5,44,9
	.half	.L65-.L1184
	.byte	3,5,1,5,42,1,5,14,9
	.half	.L64-.L65
	.byte	3,3,1,5,1,7,9
	.half	.L400-.L64
	.byte	3,1,0,1,1
.L1158:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActRestoreRomDefaults')
	.sect	'.debug_ranges'
.L399:
	.word	-1,.L191,0,.L400-.L191,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActFinishRestoreRomDefaults')
	.sect	'.debug_info'
.L401:
	.word	245
	.half	3
	.word	.L402
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L404,.L403
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActFinishRestoreRomDefaults',0,1,172,15,40,1,1
	.word	.L193,.L656,.L192
	.byte	4
	.word	.L193,.L656
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActFinishRestoreRomDefaults')
	.sect	'.debug_abbrev'
.L402:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActFinishRestoreRomDefaults')
	.sect	'.debug_line'
.L403:
	.word	.L1186-.L1185
.L1185:
	.half	3
	.word	.L1188-.L1187
.L1187:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1188:
	.byte	5,9,7,0,5,2
	.word	.L193
	.byte	3,174,15,1,5,8,9
	.half	.L1189-.L193
	.byte	1,5,11,7,9
	.half	.L1190-.L1189
	.byte	3,1,1,5,33,9
	.half	.L1191-.L1190
	.byte	1,5,47,9
	.half	.L1192-.L1191
	.byte	1,5,10,9
	.half	.L1193-.L1192
	.byte	1,5,65,7,9
	.half	.L1194-.L1193
	.byte	3,1,1,5,81,9
	.half	.L1195-.L1194
	.byte	1,5,31,7,9
	.half	.L71-.L1195
	.byte	3,2,1,5,40,9
	.half	.L1196-.L71
	.byte	1,5,61,9
	.half	.L1197-.L1196
	.byte	1,5,45,9
	.half	.L70-.L1197
	.byte	3,7,1,5,1,9
	.half	.L405-.L70
	.byte	3,1,0,1,1
.L1186:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActFinishRestoreRomDefaults')
	.sect	'.debug_ranges'
.L404:
	.word	-1,.L193,0,.L405-.L193,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActTestBlockBlank')
	.sect	'.debug_info'
.L406:
	.word	235
	.half	3
	.word	.L407
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L409,.L408
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActTestBlockBlank',0,1,194,17,40,1,1
	.word	.L217,.L657,.L216
	.byte	4
	.word	.L217,.L657
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActTestBlockBlank')
	.sect	'.debug_abbrev'
.L407:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActTestBlockBlank')
	.sect	'.debug_line'
.L408:
	.word	.L1199-.L1198
.L1198:
	.half	3
	.word	.L1201-.L1200
.L1200:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1201:
	.byte	5,9,7,0,5,2
	.word	.L217
	.byte	3,199,17,1,5,50,9
	.half	.L1202-.L217
	.byte	1,5,55,1,5,74,9
	.half	.L1203-.L1202
	.byte	1,5,13,9
	.half	.L1204-.L1203
	.byte	3,127,1,5,1,9
	.half	.L1205-.L1204
	.byte	3,9,1,7,9
	.half	.L410-.L1205
	.byte	0,1,1
.L1199:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActTestBlockBlank')
	.sect	'.debug_ranges'
.L409:
	.word	-1,.L217,0,.L410-.L217,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActValidateRam')
	.sect	'.debug_info'
.L411:
	.word	232
	.half	3
	.word	.L412
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L414,.L413
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActValidateRam',0,1,219,17,40,1,1
	.word	.L219,.L658,.L218
	.byte	4
	.word	.L219,.L658
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActValidateRam')
	.sect	'.debug_abbrev'
.L412:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActValidateRam')
	.sect	'.debug_line'
.L413:
	.word	.L1207-.L1206
.L1206:
	.half	3
	.word	.L1209-.L1208
.L1208:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1209:
	.byte	5,8,7,0,5,2
	.word	.L219
	.byte	3,221,17,1,5,5,9
	.half	.L1210-.L219
	.byte	1,5,9,7,9
	.half	.L1211-.L1210
	.byte	3,4,1,5,31,9
	.half	.L1212-.L1211
	.byte	1,5,40,9
	.half	.L1213-.L1212
	.byte	1,5,61,9
	.half	.L1214-.L1213
	.byte	1,5,31,9
	.half	.L1215-.L1214
	.byte	3,1,1,5,40,9
	.half	.L1216-.L1215
	.byte	1,5,61,9
	.half	.L1217-.L1216
	.byte	1,5,1,9
	.half	.L81-.L1217
	.byte	3,6,1,7,9
	.half	.L415-.L81
	.byte	0,1,1
.L1207:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActValidateRam')
	.sect	'.debug_ranges'
.L414:
	.word	-1,.L219,0,.L415-.L219,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActSetupRedundant')
	.sect	'.debug_info'
.L416:
	.word	235
	.half	3
	.word	.L417
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L419,.L418
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActSetupRedundant',0,1,168,16,40,1,1
	.word	.L207,.L659,.L206
	.byte	4
	.word	.L207,.L659
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActSetupRedundant')
	.sect	'.debug_abbrev'
.L417:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActSetupRedundant')
	.sect	'.debug_line'
.L418:
	.word	.L1219-.L1218
.L1218:
	.half	3
	.word	.L1221-.L1220
.L1220:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1221:
	.byte	5,25,7,0,5,2
	.word	.L207
	.byte	3,170,16,1,5,5,9
	.half	.L1222-.L207
	.byte	3,2,1,5,27,9
	.half	.L1223-.L1222
	.byte	1,5,45,9
	.half	.L1224-.L1223
	.byte	1,5,51,9
	.half	.L1225-.L1224
	.byte	3,2,1,5,49,1,5,1,9
	.half	.L1226-.L1225
	.byte	3,1,1,7,9
	.half	.L420-.L1226
	.byte	0,1,1
.L1219:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActSetupRedundant')
	.sect	'.debug_ranges'
.L419:
	.word	-1,.L207,0,.L420-.L207,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActSetupOther')
	.sect	'.debug_info'
.L421:
	.word	231
	.half	3
	.word	.L422
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L424,.L423
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActSetupOther',0,1,188,16,40,1,1
	.word	.L209,.L660,.L208
	.byte	4
	.word	.L209,.L660
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActSetupOther')
	.sect	'.debug_abbrev'
.L422:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActSetupOther')
	.sect	'.debug_line'
.L423:
	.word	.L1228-.L1227
.L1227:
	.half	3
	.word	.L1230-.L1229
.L1229:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1230:
	.byte	5,5,7,0,5,2
	.word	.L209
	.byte	3,190,16,1,5,27,9
	.half	.L1231-.L209
	.byte	3,3,1,5,45,9
	.half	.L1232-.L1231
	.byte	1,5,25,9
	.half	.L1233-.L1232
	.byte	3,2,1,5,1,7,9
	.half	.L425-.L1233
	.byte	3,1,0,1,1
.L1228:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActSetupOther')
	.sect	'.debug_ranges'
.L424:
	.word	-1,.L209,0,.L425-.L209,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActUpdateNvState')
	.sect	'.debug_info'
.L426:
	.word	264
	.half	3
	.word	.L427
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L429,.L428
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActUpdateNvState',0,1,209,16,40,1,1
	.word	.L211,.L661,.L210
	.byte	4
	.word	.L211,.L661
	.byte	5
	.byte	'NvBlockState_u8',0,1,211,16,11
	.word	.L587,.L662
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActUpdateNvState')
	.sect	'.debug_abbrev'
.L427:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActUpdateNvState')
	.sect	'.debug_line'
.L428:
	.word	.L1235-.L1234
.L1234:
	.half	3
	.word	.L1237-.L1236
.L1236:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1237:
	.byte	5,13,7,0,5,2
	.word	.L211
	.byte	3,212,16,1,5,35,9
	.half	.L1238-.L211
	.byte	1,5,15,9
	.half	.L1239-.L1238
	.byte	3,2,1,7,9
	.half	.L1240-.L1239
	.byte	3,5,1,7,9
	.half	.L1241-.L1240
	.byte	1,5,29,9
	.half	.L73-.L1241
	.byte	3,125,1,5,13,3,1,1,5,29,9
	.half	.L74-.L73
	.byte	3,4,1,5,13,3,1,1,5,29,9
	.half	.L75-.L74
	.byte	3,4,1,5,49,9
	.half	.L76-.L75
	.byte	3,5,1,5,62,9
	.half	.L1242-.L76
	.byte	1,5,27,9
	.half	.L737-.L1242
	.byte	3,3,1,5,39,9
	.half	.L1243-.L737
	.byte	1,5,1,9
	.half	.L1244-.L1243
	.byte	3,1,1,7,9
	.half	.L430-.L1244
	.byte	0,1,1
.L1235:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActUpdateNvState')
	.sect	'.debug_ranges'
.L429:
	.word	-1,.L211,0,.L430-.L211,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActSetReqIntegrityFailed')
	.sect	'.debug_info'
.L431:
	.word	242
	.half	3
	.word	.L432
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L434,.L433
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActSetReqIntegrityFailed',0,1,227,15,40,1,1
	.word	.L197,.L663,.L196
	.byte	4
	.word	.L197,.L663
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActSetReqIntegrityFailed')
	.sect	'.debug_abbrev'
.L432:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActSetReqIntegrityFailed')
	.sect	'.debug_line'
.L433:
	.word	.L1246-.L1245
.L1245:
	.half	3
	.word	.L1248-.L1247
.L1247:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1248:
	.byte	5,5,7,0,5,2
	.word	.L197
	.byte	3,228,15,1,5,43,9
	.half	.L1249-.L197
	.byte	1,5,41,1,5,1,9
	.half	.L1250-.L1249
	.byte	3,1,1,7,9
	.half	.L435-.L1250
	.byte	0,1,1
.L1246:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActSetReqIntegrityFailed')
	.sect	'.debug_ranges'
.L434:
	.word	-1,.L197,0,.L435-.L197,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActSetReqSkipped')
	.sect	'.debug_info'
.L436:
	.word	234
	.half	3
	.word	.L437
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L439,.L438
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActSetReqSkipped',0,1,240,15,40,1,1
	.word	.L199,.L664,.L198
	.byte	4
	.word	.L199,.L664
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActSetReqSkipped')
	.sect	'.debug_abbrev'
.L437:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActSetReqSkipped')
	.sect	'.debug_line'
.L438:
	.word	.L1252-.L1251
.L1251:
	.half	3
	.word	.L1254-.L1253
.L1253:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1254:
	.byte	5,5,7,0,5,2
	.word	.L199
	.byte	3,241,15,1,5,43,9
	.half	.L1255-.L199
	.byte	1,5,41,1,5,1,9
	.half	.L1256-.L1255
	.byte	3,1,1,7,9
	.half	.L440-.L1256
	.byte	0,1,1
.L1252:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActSetReqSkipped')
	.sect	'.debug_ranges'
.L439:
	.word	-1,.L199,0,.L440-.L199,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActSetReqNotOk')
	.sect	'.debug_info'
.L441:
	.word	232
	.half	3
	.word	.L442
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L444,.L443
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActSetReqNotOk',0,1,253,15,40,1,1
	.word	.L201,.L665,.L200
	.byte	4
	.word	.L201,.L665
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActSetReqNotOk')
	.sect	'.debug_abbrev'
.L442:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActSetReqNotOk')
	.sect	'.debug_line'
.L443:
	.word	.L1258-.L1257
.L1257:
	.half	3
	.word	.L1260-.L1259
.L1259:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1260:
	.byte	5,3,7,0,5,2
	.word	.L201
	.byte	3,254,15,1,5,41,9
	.half	.L1261-.L201
	.byte	1,5,39,1,5,1,9
	.half	.L1262-.L1261
	.byte	3,1,1,7,9
	.half	.L445-.L1262
	.byte	0,1,1
.L1258:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActSetReqNotOk')
	.sect	'.debug_ranges'
.L444:
	.word	-1,.L201,0,.L445-.L201,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_SetBlockPendingWriteAll')
	.sect	'.debug_info'
.L446:
	.word	241
	.half	3
	.word	.L447
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L449,.L448
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_SetBlockPendingWriteAll',0,1,152,16,40,1,1
	.word	.L205,.L666,.L204
	.byte	4
	.word	.L205,.L666
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_SetBlockPendingWriteAll')
	.sect	'.debug_abbrev'
.L447:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_SetBlockPendingWriteAll')
	.sect	'.debug_line'
.L448:
	.word	.L1264-.L1263
.L1263:
	.half	3
	.word	.L1266-.L1265
.L1265:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1266:
	.byte	5,5,7,0,5,2
	.word	.L205
	.byte	3,153,16,1,5,27,9
	.half	.L1267-.L205
	.byte	1,5,60,9
	.half	.L1268-.L1267
	.byte	1,5,58,9
	.half	.L1269-.L1268
	.byte	1,5,27,9
	.half	.L1270-.L1269
	.byte	3,1,1,5,43,9
	.half	.L1271-.L1270
	.byte	1,5,58,9
	.half	.L1272-.L1271
	.byte	1,5,73,9
	.half	.L1273-.L1272
	.byte	1,5,1,7,9
	.half	.L450-.L1273
	.byte	3,1,0,1,1
.L1264:
	.sdecl	'.debug_ranges',debug,cluster('NvM_SetBlockPendingWriteAll')
	.sect	'.debug_ranges'
.L449:
	.word	-1,.L205,0,.L450-.L205,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActWait')
	.sect	'.debug_info'
.L451:
	.word	225
	.half	3
	.word	.L452
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L454,.L453
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActWait',0,1,243,17,40,1,1
	.word	.L221,.L667,.L220
	.byte	4
	.word	.L221,.L667
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActWait')
	.sect	'.debug_abbrev'
.L452:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActWait')
	.sect	'.debug_line'
.L453:
	.word	.L1275-.L1274
.L1274:
	.half	3
	.word	.L1277-.L1276
.L1276:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1277:
	.byte	5,5,7,0,5,2
	.word	.L221
	.byte	3,244,17,1,5,27,9
	.half	.L1278-.L221
	.byte	1,5,45,9
	.half	.L1279-.L1278
	.byte	1,5,1,9
	.half	.L1280-.L1279
	.byte	3,1,1,7,9
	.half	.L455-.L1280
	.byte	0,1,1
.L1275:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActWait')
	.sect	'.debug_ranges'
.L454:
	.word	-1,.L221,0,.L455-.L221,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActNop')
	.sect	'.debug_info'
.L456:
	.word	224
	.half	3
	.word	.L457
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L459,.L458
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActNop',0,1,156,13,40,1,1
	.word	.L177,.L668,.L176
	.byte	4
	.word	.L177,.L668
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActNop')
	.sect	'.debug_abbrev'
.L457:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActNop')
	.sect	'.debug_line'
.L458:
	.word	.L1282-.L1281
.L1281:
	.half	3
	.word	.L1284-.L1283
.L1283:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1284:
	.byte	5,1,7,0,5,2
	.word	.L177
	.byte	3,158,13,1,7,9
	.half	.L460-.L177
	.byte	0,1,1
.L1282:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActNop')
	.sect	'.debug_ranges'
.L459:
	.word	-1,.L177,0,.L460-.L177,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActGetMultiBlockJob')
	.sect	'.debug_info'
.L461:
	.word	265
	.half	3
	.word	.L462
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L464,.L463
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActGetMultiBlockJob',0,1,153,17,40,1,1
	.word	.L215,.L669,.L214
	.byte	4
	.word	.L215,.L669
	.byte	5
	.byte	'multiJobFlags',0,1,155,17,17
	.word	.L670,.L671
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActGetMultiBlockJob')
	.sect	'.debug_abbrev'
.L462:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActGetMultiBlockJob')
	.sect	'.debug_line'
.L463:
	.word	.L1286-.L1285
.L1285:
	.half	3
	.word	.L1288-.L1287
.L1287:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1288:
	.byte	5,33,7,0,5,2
	.word	.L215
	.byte	3,154,17,1,5,37,9
	.half	.L740-.L215
	.byte	3,2,1,5,5,3,1,1,5,39,9
	.half	.L1289-.L740
	.byte	1,5,37,9
	.half	.L1290-.L1289
	.byte	1,5,25,9
	.half	.L1291-.L1290
	.byte	3,4,1,5,5,9
	.half	.L1292-.L1291
	.byte	3,126,1,5,42,7,9
	.half	.L1293-.L1292
	.byte	3,4,1,5,41,9
	.half	.L741-.L1293
	.byte	3,126,1,5,61,9
	.half	.L1294-.L741
	.byte	3,2,1,5,64,9
	.half	.L1295-.L1294
	.byte	3,126,1,5,10,9
	.half	.L78-.L1295
	.byte	3,4,1,5,41,7,9
	.half	.L1296-.L78
	.byte	3,2,1,5,39,9
	.half	.L1297-.L1296
	.byte	3,2,1,5,2,9
	.half	.L79-.L1297
	.byte	3,13,1,9
	.half	.L465-.L79
	.byte	0,1,1
.L1286:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActGetMultiBlockJob')
	.sect	'.debug_ranges'
.L464:
	.word	-1,.L215,0,.L465-.L215,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActCopyNvDataToBuf')
	.sect	'.debug_info'
.L466:
	.word	246
	.half	3
	.word	.L467
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L469,.L468
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActCopyNvDataToBuf',0,1,131,18,40,1,1
	.word	.L223,.L672,.L222
	.byte	4
	.word	.L223,.L672
	.byte	5
	.word	.L673,.L674
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActCopyNvDataToBuf')
	.sect	'.debug_abbrev'
.L467:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActCopyNvDataToBuf')
	.sect	'.debug_line'
.L468:
	.word	.L1299-.L1298
.L1298:
	.half	3
	.word	.L1301-.L1300
.L1300:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1301:
	.byte	5,44,7,0,5,2
	.word	.L223
	.byte	3,134,18,1,5,66,9
	.half	.L1302-.L223
	.byte	1,5,17,9
	.half	.L1303-.L1302
	.byte	3,3,1,5,8,9
	.half	.L1304-.L1303
	.byte	1,5,56,7,9
	.half	.L1305-.L1304
	.byte	1,5,72,9
	.half	.L1306-.L1305
	.byte	1,5,83,9
	.half	.L1307-.L1306
	.byte	1,5,42,7,9
	.half	.L1308-.L1307
	.byte	3,3,1,5,9,9
	.half	.L1309-.L1308
	.byte	1,7,9
	.half	.L1310-.L1309
	.byte	1,5,21,9
	.half	.L82-.L1310
	.byte	3,6,1,5,10,9
	.half	.L1311-.L82
	.byte	1,5,45,7,9
	.half	.L673-.L1311
	.byte	3,2,1,5,61,9
	.half	.L1312-.L673
	.byte	1,5,44,9
	.half	.L1313-.L1312
	.byte	1,5,78,7,9
	.half	.L1314-.L1313
	.byte	3,1,1,9
	.half	.L88-.L1314
	.byte	3,2,1,5,90,9
	.half	.L1315-.L88
	.byte	1,5,42,9
	.half	.L674-.L1315
	.byte	3,125,1,5,48,9
	.half	.L85-.L674
	.byte	3,8,1,5,46,1,5,32,9
	.half	.L84-.L85
	.byte	3,7,1,5,9,9
	.half	.L1316-.L84
	.byte	1,5,36,7,9
	.half	.L1317-.L1316
	.byte	3,2,1,5,50,9
	.half	.L1318-.L1317
	.byte	1,5,73,9
	.half	.L1319-.L1318
	.byte	1,5,68,7,9
	.half	.L1320-.L1319
	.byte	3,2,1,5,84,9
	.half	.L1321-.L1320
	.byte	1,5,35,9
	.half	.L1322-.L1321
	.byte	3,2,1,5,83,9
	.half	.L1323-.L1322
	.byte	1,5,1,9
	.half	.L90-.L1323
	.byte	3,2,1,7,9
	.half	.L470-.L90
	.byte	0,1,1
.L1299:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActCopyNvDataToBuf')
	.sect	'.debug_ranges'
.L469:
	.word	-1,.L223,0,.L470-.L223,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActKillSubFsm')
	.sect	'.debug_info'
.L471:
	.word	231
	.half	3
	.word	.L472
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L474,.L473
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_ActKillSubFsm',0,1,200,18,40,1,1
	.word	.L229,.L675,.L228
	.byte	4
	.word	.L229,.L675
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActKillSubFsm')
	.sect	'.debug_abbrev'
.L472:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActKillSubFsm')
	.sect	'.debug_line'
.L473:
	.word	.L1325-.L1324
.L1324:
	.half	3
	.word	.L1327-.L1326
.L1326:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1327:
	.byte	5,5,7,0,5,2
	.word	.L229
	.byte	3,201,18,1,5,25,9
	.half	.L1328-.L229
	.byte	1,5,23,1,5,1,9
	.half	.L1329-.L1328
	.byte	3,1,1,7,9
	.half	.L475-.L1329
	.byte	0,1,1
.L1325:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActKillSubFsm')
	.sect	'.debug_ranges'
.L474:
	.word	-1,.L229,0,.L475-.L229,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_UpdateConfigIdBlock')
	.sect	'.debug_info'
.L476:
	.word	237
	.half	3
	.word	.L477
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L479,.L478
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_UpdateConfigIdBlock',0,1,150,21,40,1,1
	.word	.L233,.L676,.L232
	.byte	4
	.word	.L233,.L676
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_UpdateConfigIdBlock')
	.sect	'.debug_abbrev'
.L477:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_UpdateConfigIdBlock')
	.sect	'.debug_line'
.L478:
	.word	.L1331-.L1330
.L1330:
	.half	3
	.word	.L1333-.L1332
.L1332:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1333:
	.byte	5,44,7,0,5,2
	.word	.L233
	.byte	3,151,21,1,5,59,9
	.half	.L1334-.L233
	.byte	3,2,1,5,81,9
	.half	.L1335-.L1334
	.byte	1,5,27,9
	.half	.L1336-.L1335
	.byte	3,2,1,5,36,9
	.half	.L1337-.L1336
	.byte	1,5,57,9
	.half	.L1338-.L1337
	.byte	1,5,1,9
	.half	.L1339-.L1338
	.byte	3,1,1,7,9
	.half	.L480-.L1339
	.byte	0,1,1
.L1331:
	.sdecl	'.debug_ranges',debug,cluster('NvM_UpdateConfigIdBlock')
	.sect	'.debug_ranges'
.L479:
	.word	-1,.L233,0,.L480-.L233,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_InternalCopyData')
	.sect	'.debug_info'
.L481:
	.word	326
	.half	3
	.word	.L482
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L484,.L483
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_InternalCopyData',0,1,169,21,40,1,1
	.word	.L235,.L677,.L234
	.byte	4
	.byte	'info_pt',0,1,170,21,26
	.word	.L678,.L679
	.byte	4
	.byte	'destPtr',0,1,170,21,54
	.word	.L680,.L681
	.byte	4
	.byte	'srcPtr',0,1,170,21,87
	.word	.L682,.L683
	.byte	5
	.word	.L235,.L677
	.byte	6
	.byte	'length',0,1,173,21,12
	.word	.L585,.L684
	.byte	7
	.word	.L685,.L686
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_InternalCopyData')
	.sect	'.debug_abbrev'
.L482:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_InternalCopyData')
	.sect	'.debug_line'
.L483:
	.word	.L1341-.L1340
.L1340:
	.half	3
	.word	.L1343-.L1342
.L1342:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1343:
	.byte	5,47,7,0,5,2
	.word	.L235
	.byte	3,172,21,1,5,29,9
	.half	.L1344-.L235
	.byte	1,5,47,9
	.half	.L1345-.L1344
	.byte	1,5,45,9
	.half	.L1346-.L1345
	.byte	1,5,72,9
	.half	.L1347-.L1346
	.byte	1,5,28,9
	.half	.L742-.L1347
	.byte	3,3,1,5,61,9
	.half	.L685-.L742
	.byte	3,5,1,5,53,9
	.half	.L1348-.L685
	.byte	1,5,50,9
	.half	.L1349-.L1348
	.byte	3,127,1,5,45,9
	.half	.L1350-.L1349
	.byte	3,6,1,5,24,9
	.half	.L1351-.L1350
	.byte	1,5,26,9
	.half	.L1352-.L1351
	.byte	3,125,1,5,13,9
	.half	.L94-.L1352
	.byte	3,2,1,5,45,3,1,1,5,33,9
	.half	.L1353-.L94
	.byte	1,5,26,9
	.half	.L93-.L1353
	.byte	3,125,1,5,1,7,9
	.half	.L686-.L93
	.byte	3,6,1,7,9
	.half	.L485-.L686
	.byte	0,1,1
.L1341:
	.sdecl	'.debug_ranges',debug,cluster('NvM_InternalCopyData')
	.sect	'.debug_ranges'
.L484:
	.word	-1,.L235,0,.L485-.L235,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_InternalCopyBufferedData')
	.sect	'.debug_info'
.L486:
	.word	315
	.half	3
	.word	.L487
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L489,.L488
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_InternalCopyBufferedData',0,1,193,14,40,1,1
	.word	.L189,.L687,.L188
	.byte	4
	.byte	'info_pt',0,1,193,14,90
	.word	.L678,.L688
	.byte	4
	.byte	'srcPtr',0,1,193,14,123
	.word	.L682,.L689
	.byte	5
	.word	.L189,.L687
	.byte	6
	.byte	'descr_pt',0,1,196,14,33
	.word	.L690,.L691
	.byte	7
	.word	.L692,.L57
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_InternalCopyBufferedData')
	.sect	'.debug_abbrev'
.L487:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_InternalCopyBufferedData')
	.sect	'.debug_line'
.L488:
	.word	.L1355-.L1354
.L1354:
	.half	3
	.word	.L1357-.L1356
.L1356:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1357:
	.byte	5,44,7,0,5,2
	.word	.L189
	.byte	3,195,14,1,5,66,9
	.half	.L1358-.L189
	.byte	1,5,40,9
	.half	.L732-.L1358
	.byte	3,125,1,5,17,9
	.half	.L734-.L732
	.byte	3,6,1,5,8,9
	.half	.L1359-.L734
	.byte	1,5,56,7,9
	.half	.L1360-.L1359
	.byte	1,5,72,9
	.half	.L730-.L1360
	.byte	1,5,83,9
	.half	.L1361-.L730
	.byte	1,5,42,7,9
	.half	.L1362-.L1361
	.byte	3,3,1,5,9,9
	.half	.L735-.L1362
	.byte	1,7,9
	.half	.L1363-.L735
	.byte	1,5,56,9
	.half	.L55-.L1363
	.byte	3,125,1,5,72,9
	.half	.L1364-.L55
	.byte	1,5,44,9
	.half	.L692-.L1364
	.byte	3,10,1,5,50,7,9
	.half	.L1365-.L692
	.byte	3,1,1,5,9,9
	.half	.L60-.L1365
	.byte	3,1,1,5,52,7,9
	.half	.L1366-.L60
	.byte	3,2,1,5,38,9
	.half	.L58-.L1366
	.byte	3,4,1,5,36,1,5,1,9
	.half	.L57-.L58
	.byte	3,6,1,7,9
	.half	.L490-.L57
	.byte	0,1,1
.L1355:
	.sdecl	'.debug_ranges',debug,cluster('NvM_InternalCopyBufferedData')
	.sect	'.debug_ranges'
.L489:
	.word	-1,.L189,0,.L490-.L189,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_IntCreateNvState')
	.sect	'.debug_info'
.L491:
	.word	297
	.half	3
	.word	.L492
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L494,.L493
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_IntCreateNvState',0,1,247,16,40,1,1
	.word	.L213,.L693,.L212
	.byte	4
	.byte	'NvState',0,1,247,16,103
	.word	.L694,.L695
	.byte	4
	.byte	'NewState',0,1,247,16,118
	.word	.L587,.L696
	.byte	5
	.word	.L213,.L693
	.byte	6
	.byte	'shift',0,1,249,16,11
	.word	.L587,.L697
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_IntCreateNvState')
	.sect	'.debug_abbrev'
.L492:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_IntCreateNvState')
	.sect	'.debug_line'
.L493:
	.word	.L1368-.L1367
.L1367:
	.half	3
	.word	.L1370-.L1369
.L1369:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1370:
	.byte	5,10,7,0,5,2
	.word	.L213
	.byte	3,251,16,1,5,18,9
	.half	.L1371-.L213
	.byte	3,10,1,9
	.half	.L1372-.L1371
	.byte	3,118,1,5,50,9
	.half	.L1373-.L1372
	.byte	1,5,17,9
	.half	.L739-.L1373
	.byte	3,2,1,5,44,9
	.half	.L1374-.L739
	.byte	3,8,1,5,54,9
	.half	.L1375-.L1374
	.byte	1,5,14,9
	.half	.L1376-.L1375
	.byte	1,5,34,9
	.half	.L1377-.L1376
	.byte	3,3,1,5,14,9
	.half	.L738-.L1377
	.byte	1,5,1,3,1,1,9
	.half	.L495-.L738
	.byte	0,1,1
.L1368:
	.sdecl	'.debug_ranges',debug,cluster('NvM_IntCreateNvState')
	.sect	'.debug_ranges'
.L494:
	.word	-1,.L213,0,.L495-.L213,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_IsWriteAllAndKilled')
	.sect	'.debug_info'
.L496:
	.word	293
	.half	3
	.word	.L497
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L499,.L498
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_IsWriteAllAndKilled',0,1,200,21,43
	.word	.L587
	.byte	1,1
	.word	.L237,.L698,.L236
	.byte	4
	.byte	'currServiceId',0,1,200,21,99
	.word	.L699,.L700
	.byte	4
	.byte	'currApiFlag',0,1,200,21,126
	.word	.L701,.L702
	.byte	5
	.word	.L237,.L698
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_IsWriteAllAndKilled')
	.sect	'.debug_abbrev'
.L497:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_IsWriteAllAndKilled')
	.sect	'.debug_line'
.L498:
	.word	.L1379-.L1378
.L1378:
	.half	3
	.word	.L1381-.L1380
.L1380:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1381:
	.byte	5,63,7,0,5,2
	.word	.L237
	.byte	3,201,21,1,5,22,9
	.half	.L1382-.L237
	.byte	1,5,111,7,9
	.half	.L1383-.L1382
	.byte	1,5,63,7,9
	.half	.L1384-.L1383
	.byte	1,5,1,9
	.half	.L95-.L1384
	.byte	3,1,1,7,9
	.half	.L500-.L95
	.byte	0,1,1
.L1379:
	.sdecl	'.debug_ranges',debug,cluster('NvM_IsWriteAllAndKilled')
	.sect	'.debug_ranges'
.L499:
	.word	-1,.L237,0,.L500-.L237,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_IntUpdateCurrentBlockCRCCompareData')
	.sect	'.debug_info'
.L501:
	.word	299
	.half	3
	.word	.L502
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L504,.L503
	.byte	2
	.word	.L242
	.byte	3
	.byte	'NvM_IntUpdateCurrentBlockCRCCompareData',0,1,216,21,40,1,1
	.word	.L239,.L703,.L238
	.byte	4
	.byte	'result',0,1,216,21,108
	.word	.L704,.L705
	.byte	5
	.word	.L239,.L703
	.byte	5
	.word	.L99,.L98
	.byte	6
	.byte	'i',0,1,230,21,13
	.word	.L587,.L706
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_IntUpdateCurrentBlockCRCCompareData')
	.sect	'.debug_abbrev'
.L502:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_IntUpdateCurrentBlockCRCCompareData')
	.sect	'.debug_line'
.L503:
	.word	.L1386-.L1385
.L1385:
	.half	3
	.word	.L1388-.L1387
.L1387:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1388:
	.byte	5,7,7,0,5,2
	.word	.L239
	.byte	3,217,21,1,5,29,9
	.half	.L1389-.L239
	.byte	1,5,43,9
	.half	.L1390-.L1389
	.byte	1,5,3,9
	.half	.L1391-.L1390
	.byte	1,5,5,7,9
	.half	.L1392-.L1391
	.byte	3,2,1,5,34,7,9
	.half	.L1393-.L1392
	.byte	3,4,1,5,47,9
	.half	.L1394-.L1393
	.byte	3,1,1,5,14,7,9
	.half	.L99-.L1394
	.byte	3,6,1,5,24,1,5,86,9
	.half	.L743-.L99
	.byte	1,5,31,9
	.half	.L102-.L743
	.byte	3,2,1,5,78,9
	.half	.L1395-.L102
	.byte	1,5,45,9
	.half	.L1396-.L1395
	.byte	1,5,72,9
	.half	.L1397-.L1396
	.byte	1,5,89,9
	.half	.L1398-.L1397
	.byte	3,126,1,5,76,9
	.half	.L744-.L1398
	.byte	3,2,1,5,89,9
	.half	.L1399-.L744
	.byte	3,126,1,5,24,9
	.half	.L101-.L1399
	.byte	1,5,86,9
	.half	.L1400-.L101
	.byte	1,5,1,7,9
	.half	.L98-.L1400
	.byte	3,6,1,7,9
	.half	.L505-.L98
	.byte	0,1,1
.L1386:
	.sdecl	'.debug_ranges',debug,cluster('NvM_IntUpdateCurrentBlockCRCCompareData')
	.sect	'.debug_ranges'
.L504:
	.word	-1,.L239,0,.L505-.L239,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L506:
	.word	213
	.half	3
	.word	.L507
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L509,.L508
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_1',0,1,188,7,40,1
	.word	.L115,.L510,.L114
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L507:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L508:
	.word	.L1402-.L1401
.L1401:
	.half	3
	.word	.L1404-.L1403
.L1403:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1404:
	.byte	5,43,7,0,5,2
	.word	.L115
	.byte	3,190,7,1,5,72,9
	.half	.L1405-.L115
	.byte	3,127,1,5,65,9
	.half	.L1406-.L1405
	.byte	3,1,1,5,86,9
	.half	.L1407-.L1406
	.byte	3,127,1,5,65,9
	.half	.L1408-.L1407
	.byte	3,1,1,7,9
	.half	.L510-.L1408
	.byte	0,1,1,5,43,0,5,2
	.word	.L115
	.byte	3,190,7,1,5,68,9
	.half	.L1405-.L115
	.byte	3,201,5,1,5,61,9
	.half	.L1406-.L1405
	.byte	3,1,1,5,82,9
	.half	.L1407-.L1406
	.byte	3,127,1,5,65,9
	.half	.L1408-.L1407
	.byte	3,183,122,1,7,9
	.half	.L510-.L1408
	.byte	0,1,1,5,43,0,5,2
	.word	.L115
	.byte	3,190,7,1,5,56,9
	.half	.L1405-.L115
	.byte	3,136,10,1,5,31,9
	.half	.L1406-.L1405
	.byte	3,1,1,5,70,9
	.half	.L1407-.L1406
	.byte	3,127,1,5,65,9
	.half	.L1408-.L1407
	.byte	3,248,117,1,7,9
	.half	.L510-.L1408
	.byte	0,1,1
.L1402:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L509:
	.word	-1,.L115,0,.L510-.L115,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L511:
	.word	213
	.half	3
	.word	.L512
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L514,.L513
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_2',0,1,139,11,40,1
	.word	.L147,.L515,.L146
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L512:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L513:
	.word	.L1410-.L1409
.L1409:
	.half	3
	.word	.L1412-.L1411
.L1411:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1412:
	.byte	5,5,7,0,5,2
	.word	.L147
	.byte	3,147,11,1,5,23,9
	.half	.L1413-.L147
	.byte	1,9
	.half	.L515-.L1413
	.byte	0,1,1,5,5,0,5,2
	.word	.L147
	.byte	3,200,12,1,5,23,9
	.half	.L1413-.L147
	.byte	1,9
	.half	.L1414-.L1413
	.byte	3,203,126,1,7,9
	.half	.L515-.L1414
	.byte	0,1,1,5,5,0,5,2
	.word	.L147
	.byte	3,215,12,1,5,23,9
	.half	.L1413-.L147
	.byte	1,9
	.half	.L1414-.L1413
	.byte	3,188,126,1,7,9
	.half	.L515-.L1414
	.byte	0,1,1,5,5,0,5,2
	.word	.L147
	.byte	3,233,12,1,5,23,9
	.half	.L1413-.L147
	.byte	1,9
	.half	.L1414-.L1413
	.byte	3,170,126,1,7,9
	.half	.L515-.L1414
	.byte	0,1,1
.L1410:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L514:
	.word	-1,.L147,0,.L515-.L147,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L516:
	.word	213
	.half	3
	.word	.L517
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L519,.L518
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_3',0,1,198,12,40,1
	.word	.L165,.L520,.L164
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L517:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L518:
	.word	.L1416-.L1415
.L1415:
	.half	3
	.word	.L1418-.L1417
.L1417:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1418:
	.byte	5,25,7,0,5,2
	.word	.L165
	.byte	3,199,12,1,5,5,9
	.half	.L1419-.L165
	.byte	1,9
	.half	.L520-.L1419
	.byte	0,1,1,5,25,0,5,2
	.word	.L165
	.byte	3,214,12,1,5,5,9
	.half	.L1419-.L165
	.byte	1,9
	.half	.L1420-.L1419
	.byte	3,113,1,7,9
	.half	.L520-.L1420
	.byte	0,1,1,5,25,0,5,2
	.word	.L165
	.byte	3,232,12,1,5,5,9
	.half	.L1419-.L165
	.byte	1,9
	.half	.L1420-.L1419
	.byte	3,95,1,7,9
	.half	.L520-.L1420
	.byte	0,1,1
.L1416:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L519:
	.word	-1,.L165,0,.L520-.L165,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_4')
	.sect	'.debug_info'
.L521:
	.word	213
	.half	3
	.word	.L522
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L524,.L523
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_4',0,1,245,12,40,1
	.word	.L173,.L525,.L172
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_4')
	.sect	'.debug_abbrev'
.L522:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_4')
	.sect	'.debug_line'
.L523:
	.word	.L1422-.L1421
.L1421:
	.half	3
	.word	.L1424-.L1423
.L1423:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1424:
	.byte	5,44,7,0,5,2
	.word	.L173
	.byte	3,248,12,1,5,66,9
	.half	.L1425-.L173
	.byte	1,5,80,9
	.half	.L1426-.L1425
	.byte	1,5,42,9
	.half	.L1427-.L1426
	.byte	1,9
	.half	.L525-.L1427
	.byte	0,1,1,5,44,0,5,2
	.word	.L173
	.byte	3,248,12,1,5,66,9
	.half	.L1425-.L173
	.byte	3,159,8,1,5,80,9
	.half	.L1426-.L1425
	.byte	1,5,42,9
	.half	.L1427-.L1426
	.byte	1,9
	.half	.L1428-.L1427
	.byte	3,225,119,1,7,9
	.half	.L525-.L1428
	.byte	0,1,1
.L1422:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_4')
	.sect	'.debug_ranges'
.L524:
	.word	-1,.L173,0,.L525-.L173,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_5')
	.sect	'.debug_info'
.L526:
	.word	213
	.half	3
	.word	.L527
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L529,.L528
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_5',0,1,188,7,40,1
	.word	.L113,.L530,.L112
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_5')
	.sect	'.debug_abbrev'
.L527:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_5')
	.sect	'.debug_line'
.L528:
	.word	.L1430-.L1429
.L1429:
	.half	3
	.word	.L1432-.L1431
.L1431:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1432:
	.byte	5,42,7,0,5,2
	.word	.L113
	.byte	3,189,7,1,5,47,9
	.half	.L1433-.L113
	.byte	3,3,1,5,45,1,9
	.half	.L530-.L1433
	.byte	0,1,1,5,38,0,5,2
	.word	.L113
	.byte	3,135,13,1,5,47,9
	.half	.L1433-.L113
	.byte	3,3,1,5,45,1,9
	.half	.L1434-.L1433
	.byte	3,182,122,1,7,9
	.half	.L530-.L1434
	.byte	0,1,1,5,27,0,5,2
	.word	.L113
	.byte	3,137,14,1,5,47,9
	.half	.L1433-.L113
	.byte	3,4,1,5,45,1,9
	.half	.L1434-.L1433
	.byte	3,179,121,1,7,9
	.half	.L530-.L1434
	.byte	0,1,1,5,13,0,5,2
	.word	.L113
	.byte	3,198,17,1,5,47,9
	.half	.L1433-.L113
	.byte	3,7,1,5,45,3,124,1,9
	.half	.L1434-.L1433
	.byte	3,247,117,1,7,9
	.half	.L530-.L1434
	.byte	0,1,1
.L1430:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_5')
	.sect	'.debug_ranges'
.L529:
	.word	-1,.L113,0,.L530-.L113,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_6')
	.sect	'.debug_info'
.L531:
	.word	213
	.half	3
	.word	.L532
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L534,.L533
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_6',0,1,212,7,40,1
	.word	.L123,.L535,.L122
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_6')
	.sect	'.debug_abbrev'
.L532:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_6')
	.sect	'.debug_line'
.L533:
	.word	.L1436-.L1435
.L1435:
	.half	3
	.word	.L1438-.L1437
.L1437:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1438:
	.byte	5,33,7,0,5,2
	.word	.L123
	.byte	3,215,7,1,9
	.half	.L535-.L123
	.byte	0,1,1,5,5,0,5,2
	.word	.L123
	.byte	3,252,7,1,5,33,9
	.half	.L1439-.L123
	.byte	3,91,1,7,9
	.half	.L535-.L1439
	.byte	0,1,1,5,49,0,5,2
	.word	.L123
	.byte	3,179,8,1,5,33,9
	.half	.L1439-.L123
	.byte	3,164,127,1,7,9
	.half	.L535-.L1439
	.byte	0,1,1,5,60,0,5,2
	.word	.L123
	.byte	3,242,14,1,5,33,9
	.half	.L1439-.L123
	.byte	3,229,120,1,7,9
	.half	.L535-.L1439
	.byte	0,1,1
.L1436:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_6')
	.sect	'.debug_ranges'
.L534:
	.word	-1,.L123,0,.L535-.L123,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_7')
	.sect	'.debug_info'
.L536:
	.word	213
	.half	3
	.word	.L537
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L539,.L538
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_7',0,1,212,7,40,1
	.word	.L121,.L540,.L120
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_7')
	.sect	'.debug_abbrev'
.L537:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_7')
	.sect	'.debug_line'
.L538:
	.word	.L1441-.L1440
.L1440:
	.half	3
	.word	.L1443-.L1442
.L1442:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1443:
	.byte	5,31,7,0,5,2
	.word	.L121
	.byte	3,228,7,1,5,58,9
	.half	.L1444-.L121
	.byte	1,9
	.half	.L540-.L1444
	.byte	0,1,1,5,31,0,5,2
	.word	.L121
	.byte	3,210,8,1,5,58,9
	.half	.L1444-.L121
	.byte	1,9
	.half	.L1445-.L1444
	.byte	3,146,127,1,7,9
	.half	.L540-.L1445
	.byte	0,1,1,5,26,0,5,2
	.word	.L121
	.byte	3,141,11,1,5,53,9
	.half	.L1444-.L121
	.byte	1,5,58,9
	.half	.L1445-.L1444
	.byte	3,215,124,1,7,9
	.half	.L540-.L1445
	.byte	0,1,1
.L1441:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_7')
	.sect	'.debug_ranges'
.L539:
	.word	-1,.L121,0,.L540-.L121,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_8')
	.sect	'.debug_info'
.L541:
	.word	213
	.half	3
	.word	.L542
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L544,.L543
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_8',0,1,250,7,40,1
	.word	.L127,.L545,.L126
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_8')
	.sect	'.debug_abbrev'
.L542:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_8')
	.sect	'.debug_line'
.L543:
	.word	.L1447-.L1446
.L1446:
	.half	3
	.word	.L1449-.L1448
.L1448:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1449:
	.byte	5,28,7,0,5,2
	.word	.L127
	.byte	3,130,8,1,9
	.half	.L545-.L127
	.byte	0,1,1,5,42,0,5,2
	.word	.L127
	.byte	3,159,9,1,5,28,9
	.half	.L1450-.L127
	.byte	3,227,126,1,7,9
	.half	.L545-.L1450
	.byte	0,1,1,5,42,0,5,2
	.word	.L127
	.byte	3,251,9,1,5,28,9
	.half	.L1450-.L127
	.byte	3,135,126,1,7,9
	.half	.L545-.L1450
	.byte	0,1,1,5,17,0,5,2
	.word	.L127
	.byte	3,243,14,1,5,28,9
	.half	.L1450-.L127
	.byte	3,143,121,1,7,9
	.half	.L545-.L1450
	.byte	0,1,1
.L1447:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_8')
	.sect	'.debug_ranges'
.L544:
	.word	-1,.L127,0,.L545-.L127,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_9')
	.sect	'.debug_info'
.L546:
	.word	213
	.half	3
	.word	.L547
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L549,.L548
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_9',0,1,158,14,40,1
	.word	.L185,.L550,.L184
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_9')
	.sect	'.debug_abbrev'
.L547:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_9')
	.sect	'.debug_line'
.L548:
	.word	.L1452-.L1451
.L1451:
	.half	3
	.word	.L1454-.L1453
.L1453:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1454:
	.byte	5,26,7,0,5,2
	.word	.L185
	.byte	3,159,14,1,5,48,9
	.half	.L1455-.L185
	.byte	1,5,65,9
	.half	.L1456-.L1455
	.byte	1,9
	.half	.L550-.L1456
	.byte	0,1,1,5,25,0,5,2
	.word	.L185
	.byte	3,137,21,1,5,47,9
	.half	.L1455-.L185
	.byte	1,5,63,9
	.half	.L1456-.L1455
	.byte	1,5,65,9
	.half	.L1457-.L1456
	.byte	3,150,121,1,7,9
	.half	.L550-.L1457
	.byte	0,1,1
.L1452:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_9')
	.sect	'.debug_ranges'
.L549:
	.word	-1,.L185,0,.L550-.L185,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_10')
	.sect	'.debug_info'
.L551:
	.word	214
	.half	3
	.word	.L552
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L554,.L553
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_10',0,1,183,18,30,1
	.word	.L227,.L555,.L226
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_10')
	.sect	'.debug_abbrev'
.L552:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_10')
	.sect	'.debug_line'
.L553:
	.word	.L1459-.L1458
.L1458:
	.half	3
	.word	.L1461-.L1460
.L1460:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1461:
	.byte	5,31,7,0,5,2
	.word	.L227
	.byte	3,185,18,1,5,53,9
	.half	.L1462-.L227
	.byte	1,5,67,9
	.half	.L1463-.L1462
	.byte	1,9
	.half	.L555-.L1463
	.byte	0,1,1,5,29,0,5,2
	.word	.L227
	.byte	3,187,18,1,5,51,9
	.half	.L1462-.L227
	.byte	1,5,65,9
	.half	.L1463-.L1462
	.byte	1,5,67,9
	.half	.L1464-.L1463
	.byte	3,126,1,7,9
	.half	.L555-.L1464
	.byte	0,1,1
.L1459:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_10')
	.sect	'.debug_ranges'
.L554:
	.word	-1,.L227,0,.L555-.L227,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_11')
	.sect	'.debug_info'
.L556:
	.word	214
	.half	3
	.word	.L557
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L559,.L558
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_11',0,1,139,12,40,1
	.word	.L159,.L560,.L158
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_11')
	.sect	'.debug_abbrev'
.L557:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_11')
	.sect	'.debug_line'
.L558:
	.word	.L1466-.L1465
.L1465:
	.half	3
	.word	.L1468-.L1467
.L1467:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1468:
	.byte	5,5,7,0,5,2
	.word	.L159
	.byte	3,140,12,1,5,51,9
	.half	.L1469-.L159
	.byte	1,5,49,1,9
	.half	.L560-.L1469
	.byte	0,1,1,5,5,0,5,2
	.word	.L159
	.byte	3,140,12,1,5,51,9
	.half	.L1469-.L159
	.byte	3,178,4,1,5,49,1,9
	.half	.L1470-.L1469
	.byte	3,206,123,1,7,9
	.half	.L560-.L1470
	.byte	0,1,1
.L1466:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_11')
	.sect	'.debug_ranges'
.L559:
	.word	-1,.L159,0,.L560-.L159,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_12')
	.sect	'.debug_info'
.L561:
	.word	214
	.half	3
	.word	.L562
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L564,.L563
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_12',0,1,139,12,40,1
	.word	.L157,.L565,.L156
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_12')
	.sect	'.debug_abbrev'
.L562:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_12')
	.sect	'.debug_line'
.L563:
	.word	.L1472-.L1471
.L1471:
	.half	3
	.word	.L1474-.L1473
.L1473:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1474:
	.byte	5,46,7,0,5,2
	.word	.L157
	.byte	3,155,12,1,5,23,9
	.half	.L1475-.L157
	.byte	3,1,1,5,39,9
	.half	.L1476-.L1475
	.byte	1,5,45,9
	.half	.L1477-.L1476
	.byte	3,1,1,9
	.half	.L565-.L1477
	.byte	0,1,1,5,46,0,5,2
	.word	.L157
	.byte	3,131,14,1,5,62,9
	.half	.L1475-.L157
	.byte	1,5,78,9
	.half	.L1476-.L1475
	.byte	1,5,31,9
	.half	.L1477-.L1476
	.byte	3,1,1,5,45,9
	.half	.L1478-.L1477
	.byte	3,153,126,1,7,9
	.half	.L565-.L1478
	.byte	0,1,1
.L1472:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_12')
	.sect	'.debug_ranges'
.L564:
	.word	-1,.L157,0,.L565-.L157,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_13')
	.sect	'.debug_info'
.L566:
	.word	214
	.half	3
	.word	.L567
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L569,.L568
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_13',0,1,158,9,40,1
	.word	.L137,.L570,.L136
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_13')
	.sect	'.debug_abbrev'
.L567:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_13')
	.sect	'.debug_line'
.L568:
	.word	.L1480-.L1479
.L1479:
	.half	3
	.word	.L1482-.L1481
.L1481:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1482:
	.byte	5,9,7,0,5,2
	.word	.L137
	.byte	3,206,9,1,5,25,9
	.half	.L713-.L137
	.byte	1,9
	.half	.L570-.L713
	.byte	0,1,1,5,9,0,5,2
	.word	.L137
	.byte	3,174,15,1,5,25,9
	.half	.L713-.L137
	.byte	1,9
	.half	.L1483-.L713
	.byte	3,160,122,1,7,9
	.half	.L570-.L1483
	.byte	0,1,1,5,8,0,5,2
	.word	.L137
	.byte	3,221,17,1,5,24,9
	.half	.L713-.L137
	.byte	1,5,25,9
	.half	.L1483-.L713
	.byte	3,241,119,1,7,9
	.half	.L570-.L1483
	.byte	0,1,1
.L1480:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_13')
	.sect	'.debug_ranges'
.L569:
	.word	-1,.L137,0,.L570-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_14')
	.sect	'.debug_info'
.L571:
	.word	214
	.half	3
	.word	.L572
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L574,.L573
	.byte	2
	.word	.L242
	.byte	3
	.byte	'.cocofun_14',0,1,188,7,40,1
	.word	.L117,.L575,.L116
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_14')
	.sect	'.debug_abbrev'
.L572:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_14')
	.sect	'.debug_line'
.L573:
	.word	.L1485-.L1484
.L1484:
	.half	3
	.word	.L1487-.L1486
.L1486:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0,0,0,0,0
.L1487:
	.byte	5,43,7,0,5,2
	.word	.L117
	.byte	3,190,7,1,9
	.half	.L575-.L117
	.byte	0,1,1,5,39,0,5,2
	.word	.L117
	.byte	3,136,13,1,5,43,9
	.half	.L1488-.L117
	.byte	3,182,122,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,9,0,5,2
	.word	.L117
	.byte	3,199,17,1,5,43,9
	.half	.L1488-.L117
	.byte	3,247,117,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,5,0,5,2
	.word	.L117
	.byte	3,204,11,1,5,43,9
	.half	.L1488-.L117
	.byte	3,242,123,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,5,0,5,2
	.word	.L117
	.byte	3,140,12,1,5,43,9
	.half	.L1488-.L117
	.byte	3,178,123,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,5,0,5,2
	.word	.L117
	.byte	3,190,16,1,5,43,9
	.half	.L1488-.L117
	.byte	3,128,119,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,44,0,5,2
	.word	.L117
	.byte	3,248,12,1,5,43,9
	.half	.L1488-.L117
	.byte	3,198,122,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,44,0,5,2
	.word	.L117
	.byte	3,151,21,1,5,43,9
	.half	.L1488-.L117
	.byte	3,167,114,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,44,0,5,2
	.word	.L117
	.byte	3,248,13,1,5,43,9
	.half	.L1488-.L117
	.byte	3,198,121,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,11,0,5,2
	.word	.L117
	.byte	3,175,15,1,5,43,9
	.half	.L1488-.L117
	.byte	3,143,120,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,10,0,5,2
	.word	.L117
	.byte	3,201,15,1,5,43,9
	.half	.L1488-.L117
	.byte	3,245,119,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,5,0,5,2
	.word	.L117
	.byte	3,172,16,1,5,43,9
	.half	.L1488-.L117
	.byte	3,146,119,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,13,0,5,2
	.word	.L117
	.byte	3,212,16,1,5,43,9
	.half	.L1488-.L117
	.byte	3,234,118,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1,5,9,0,5,2
	.word	.L117
	.byte	3,225,17,1,5,43,9
	.half	.L1488-.L117
	.byte	3,221,117,1,7,9
	.half	.L575-.L1488
	.byte	0,1,1
.L1485:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_14')
	.sect	'.debug_ranges'
.L574:
	.word	-1,.L117,0,.L575-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActionTable_ap')
	.sect	'.debug_info'
.L576:
	.word	211
	.half	3
	.word	.L577
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L242
	.byte	3
	.byte	'NvM_ActionTable_ap',0,8,224,6,45
	.word	.L707
	.byte	1,5,3
	.word	NvM_ActionTable_ap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActionTable_ap')
	.sect	'.debug_abbrev'
.L577:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_TestBuffer_u8')
	.sect	'.debug_info'
.L578:
	.word	208
	.half	3
	.word	.L579
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Act.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L242
	.byte	3
	.byte	'NvM_TestBuffer_u8',0,8,88,40
	.word	.L587
	.byte	5,3
	.word	NvM_TestBuffer_u8
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_TestBuffer_u8')
	.sect	'.debug_abbrev'
.L579:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L114:
	.word	-1,.L115,0,.L510-.L115
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_10')
	.sect	'.debug_loc'
.L226:
	.word	-1,.L227,0,.L555-.L227
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_11')
	.sect	'.debug_loc'
.L158:
	.word	-1,.L159,0,.L560-.L159
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_12')
	.sect	'.debug_loc'
.L156:
	.word	-1,.L157,0,.L565-.L157
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_13')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L137,0,.L570-.L137
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_14')
	.sect	'.debug_loc'
.L116:
	.word	-1,.L117,0,.L575-.L117
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L146:
	.word	-1,.L147,0,.L515-.L147
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L164:
	.word	-1,.L165,0,.L520-.L165
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_4')
	.sect	'.debug_loc'
.L172:
	.word	-1,.L173,0,.L525-.L173
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_5')
	.sect	'.debug_loc'
.L112:
	.word	-1,.L113,0,.L530-.L113
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_6')
	.sect	'.debug_loc'
.L122:
	.word	-1,.L123,0,.L535-.L123
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_7')
	.sect	'.debug_loc'
.L120:
	.word	-1,.L121,0,.L540-.L121
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_8')
	.sect	'.debug_loc'
.L126:
	.word	-1,.L127,0,.L545-.L127
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_9')
	.sect	'.debug_loc'
.L184:
	.word	-1,.L185,0,.L550-.L185
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActCancelNV')
	.sect	'.debug_loc'
.L224:
	.word	-1,.L225,0,.L580-.L225
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActCopyNvDataToBuf')
	.sect	'.debug_loc'
.L222:
	.word	-1,.L223,0,.L672-.L223
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActEraseNvBlock')
	.sect	'.debug_loc'
.L110:
	.word	-1,.L111,0,.L641-.L111
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActFinishBlock')
	.sect	'.debug_loc'
.L118:
	.word	-1,.L119,0,.L619-.L119
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActFinishCfgIdCheck')
	.sect	'.debug_loc'
.L150:
	.word	-1,.L151,0,.L624-.L151
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActFinishEraseBlock')
	.sect	'.debug_loc'
.L160:
	.word	-1,.L161,0,.L640-.L161
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActFinishMainJob')
	.sect	'.debug_loc'
.L617:
	.word	-1,.L131,.L121-.L131,.L540-.L131
	.half	5
	.byte	144,34,157,32,32
	.word	.L12-.L131,.L708-.L131
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L130:
	.word	-1,.L131,0,.L613-.L131
	.half	2
	.byte	138,0
	.word	0,0
.L615:
	.word	-1,.L131,.L121-.L131,.L540-.L131
	.half	5
	.byte	144,39,157,32,32
	.word	.L709-.L131,.L710-.L131
	.half	5
	.byte	144,39,157,32,32
	.word	.L7-.L131,.L13-.L131
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActFinishReadBlock')
	.sect	'.debug_loc'
.L628:
	.word	-1,.L135,.L711-.L135,.L712-.L135
	.half	1
	.byte	111
	.word	.L137-.L135,.L713-.L135
	.half	1
	.byte	111
	.word	0,0
.L134:
	.word	-1,.L135,0,.L625-.L135
	.half	2
	.byte	138,0
	.word	0,0
.L630:
	.word	-1,.L135,.L137-.L135,.L570-.L135
	.half	1
	.byte	110
	.word	.L714-.L135,.L625-.L135
	.half	1
	.byte	110
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActFinishReadBlockAndSetSkipped')
	.sect	'.debug_loc'
.L138:
	.word	-1,.L139,0,.L582-.L139
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActFinishRestoreRomDefaults')
	.sect	'.debug_loc'
.L192:
	.word	-1,.L193,0,.L656-.L193
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActFinishWriteBlock')
	.sect	'.debug_loc'
.L633:
	.word	-1,.L141,.L715-.L141,.L631-.L141
	.half	1
	.byte	111
	.word	0,0
.L140:
	.word	-1,.L141,0,.L631-.L141
	.half	2
	.byte	138,0
	.word	0,0
.L638:
	.word	-1,.L141,.L716-.L141,.L23-.L141
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L639:
	.word	-1,.L141,.L717-.L141,.L23-.L141
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActGetMultiBlockJob')
	.sect	'.debug_loc'
.L214:
	.word	-1,.L215,0,.L669-.L215
	.half	2
	.byte	138,0
	.word	0,0
.L671:
	.word	-1,.L215,.L740-.L215,.L741-.L215
	.half	5
	.byte	144,39,157,32,32
	.word	.L78-.L215,.L79-.L215
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitBlock')
	.sect	'.debug_loc'
.L601:
	.word	-1,.L143,.L720-.L143,.L597-.L143
	.half	1
	.byte	101
	.word	0,0
.L142:
	.word	-1,.L143,0,.L597-.L143
	.half	2
	.byte	138,0
	.word	0,0
.L599:
	.word	-1,.L143,.L718-.L143,.L719-.L143
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitMainFsm')
	.sect	'.debug_loc'
.L144:
	.word	-1,.L145,0,.L595-.L145
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitNextBlockReadAll')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L125,0,.L621-.L125
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitNextBlockWriteAll')
	.sect	'.debug_loc'
.L128:
	.word	-1,.L129,0,.L623-.L129
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitReadAll')
	.sect	'.debug_loc'
.L148:
	.word	-1,.L149,0,.L602-.L149
	.half	2
	.byte	138,0
	.word	0,0
.L603:
	.word	-1,.L149,.L721-.L149,.L722-.L149
	.half	5
	.byte	144,39,157,32,32
	.word	.L723-.L149,.L602-.L149
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitReadBlockSubFsm')
	.sect	'.debug_loc'
.L162:
	.word	-1,.L163,0,.L604-.L163
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitRestoreBlockDefaults')
	.sect	'.debug_loc'
.L170:
	.word	-1,.L171,0,.L611-.L171
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitRestoreBlockDefaultsSubFsm')
	.sect	'.debug_loc'
.L166:
	.word	-1,.L167,0,.L606-.L167
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitWriteAll')
	.sect	'.debug_loc'
.L152:
	.word	-1,.L153,0,.L607-.L153
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitWriteBlock')
	.sect	'.debug_loc'
.L154:
	.word	-1,.L155,0,.L608-.L155
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInitWriteBlockFsm')
	.sect	'.debug_loc'
.L168:
	.word	-1,.L169,0,.L610-.L169
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActInvalidateNvBlock')
	.sect	'.debug_loc'
.L174:
	.word	-1,.L175,0,.L643-.L175
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActKillSubFsm')
	.sect	'.debug_loc'
.L228:
	.word	-1,.L229,0,.L675-.L229
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActKillWriteAll')
	.sect	'.debug_loc'
.L132:
	.word	-1,.L133,0,.L618-.L133
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActNop')
	.sect	'.debug_loc'
.L176:
	.word	-1,.L177,0,.L668-.L177
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActProcessCrc')
	.sect	'.debug_loc'
.L230:
	.word	-1,.L231,0,.L644-.L231
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActProcessCrcRead')
	.sect	'.debug_loc'
.L182:
	.word	-1,.L183,0,.L652-.L183
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActReadCopyData')
	.sect	'.debug_loc'
.L186:
	.word	-1,.L187,0,.L654-.L187
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActReadNvBlock')
	.sect	'.debug_loc'
.L180:
	.word	-1,.L181,0,.L649-.L181
	.half	2
	.byte	138,0
	.word	0,0
.L651:
	.word	-1,.L181,.L157-.L181,.L565-.L181
	.half	1
	.byte	108
	.word	.L727-.L181,.L728-.L181
	.half	1
	.byte	108
	.word	.L113-.L181,.L530-.L181
	.half	1
	.byte	108
	.word	.L729-.L181,.L649-.L181
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActRestoreRomDefaults')
	.sect	'.debug_loc'
.L190:
	.word	-1,.L191,0,.L655-.L191
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActSetInitialAttr')
	.sect	'.debug_loc'
.L194:
	.word	-1,.L195,0,.L594-.L195
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActSetReqIntegrityFailed')
	.sect	'.debug_loc'
.L196:
	.word	-1,.L197,0,.L663-.L197
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActSetReqNotOk')
	.sect	'.debug_loc'
.L200:
	.word	-1,.L201,0,.L665-.L201
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActSetReqOk')
	.sect	'.debug_loc'
.L202:
	.word	-1,.L203,0,.L583-.L203
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActSetReqSkipped')
	.sect	'.debug_loc'
.L198:
	.word	-1,.L199,0,.L664-.L199
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActSetupOther')
	.sect	'.debug_loc'
.L208:
	.word	-1,.L209,0,.L660-.L209
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActSetupRedundant')
	.sect	'.debug_loc'
.L206:
	.word	-1,.L207,0,.L659-.L207
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActTestBlockBlank')
	.sect	'.debug_loc'
.L216:
	.word	-1,.L217,0,.L657-.L217
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActUpdateNvState')
	.sect	'.debug_loc'
.L662:
	.word	-1,.L211,.L76-.L211,.L737-.L211
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L210:
	.word	-1,.L211,0,.L661-.L211
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActValidateRam')
	.sect	'.debug_loc'
.L218:
	.word	-1,.L219,0,.L658-.L219
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActWait')
	.sect	'.debug_loc'
.L220:
	.word	-1,.L221,0,.L667-.L221
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActWriteNvBlock')
	.sect	'.debug_loc'
.L178:
	.word	-1,.L179,0,.L645-.L179
	.half	2
	.byte	138,0
	.word	0,0
.L648:
	.word	-1,.L179,.L726-.L179,.L52-.L179
	.half	5
	.byte	144,33,157,32,0
	.word	.L53-.L179,.L54-.L179
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L647:
	.word	-1,.L179,.L724-.L179,.L645-.L179
	.half	1
	.byte	109
	.word	.L725-.L179,.L726-.L179
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_BlockNotification')
	.sect	'.debug_loc'
.L586:
	.word	-1,.L241,0,.L108-.L241
	.half	5
	.byte	144,34,157,32,0
	.word	.L745-.L241,.L746-.L241
	.half	5
	.byte	144,36,157,32,0
	.word	.L747-.L241,.L748-.L241
	.half	5
	.byte	144,36,157,32,0
	.word	.L754-.L241,.L755-.L241
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L589:
	.word	-1,.L241,0,.L108-.L241
	.half	5
	.byte	144,35,157,32,0
	.word	.L745-.L241,.L584-.L241
	.half	5
	.byte	144,37,157,32,0
	.word	.L756-.L241,.L103-.L241
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L240:
	.word	-1,.L241,0,.L584-.L241
	.half	2
	.byte	138,0
	.word	0,0
.L588:
	.word	-1,.L241,0,.L108-.L241
	.half	5
	.byte	144,34,157,32,32
	.word	.L749-.L241,.L750-.L241
	.half	5
	.byte	144,36,157,32,32
	.word	.L751-.L241,.L752-.L241
	.half	5
	.byte	144,36,157,32,32
	.word	.L753-.L241,.L108-.L241
	.half	5
	.byte	144,36,157,32,32
	.word	.L754-.L241,.L755-.L241
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L593:
	.word	-1,.L241,.L747-.L241,.L584-.L241
	.half	1
	.byte	111
	.word	0,0
.L591:
	.word	-1,.L241,.L746-.L241,.L108-.L241
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_IntCreateNvState')
	.sect	'.debug_loc'
.L696:
	.word	-1,.L213,0,.L738-.L213
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L212:
	.word	-1,.L213,0,.L693-.L213
	.half	2
	.byte	138,0
	.word	0,0
.L695:
	.word	-1,.L213,0,.L693-.L213
	.half	1
	.byte	100
	.word	0,0
.L697:
	.word	-1,.L213,.L739-.L213,.L693-.L213
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_IntUpdateCurrentBlockCRCCompareData')
	.sect	'.debug_loc'
.L238:
	.word	-1,.L239,0,.L703-.L239
	.half	2
	.byte	138,0
	.word	0,0
.L706:
	.word	-1,.L239,.L743-.L239,.L744-.L239
	.half	5
	.byte	144,39,157,32,32
	.word	.L101-.L239,.L98-.L239
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L705:
	.word	-1,.L239,0,.L703-.L239
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_InternalCopyBufferedData')
	.sect	'.debug_loc'
.L188:
	.word	-1,.L189,0,.L687-.L189
	.half	2
	.byte	138,0
	.word	0,0
.L691:
	.word	-1,.L189,.L732-.L189,.L687-.L189
	.half	1
	.byte	111
	.word	0,0
.L688:
	.word	-1,.L189,0,.L730-.L189
	.half	1
	.byte	100
	.word	.L733-.L189,.L687-.L189
	.half	1
	.byte	108
	.word	.L736-.L189,.L58-.L189
	.half	1
	.byte	100
	.word	0,0
.L689:
	.word	-1,.L189,0,.L731-.L189
	.half	1
	.byte	101
	.word	.L734-.L189,.L735-.L189
	.half	1
	.byte	102
	.word	.L731-.L189,.L735-.L189
	.half	1
	.byte	100
	.word	.L55-.L189,.L58-.L189
	.half	1
	.byte	102
	.word	.L55-.L189,.L692-.L189
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_InternalCopyData')
	.sect	'.debug_loc'
.L234:
	.word	-1,.L235,0,.L677-.L235
	.half	2
	.byte	138,0
	.word	0,0
.L681:
	.word	-1,.L235,0,.L677-.L235
	.half	1
	.byte	101
	.word	0,0
.L679:
	.word	-1,.L235,0,.L677-.L235
	.half	1
	.byte	100
	.word	0,0
.L684:
	.word	-1,.L235,.L742-.L235,.L677-.L235
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L683:
	.word	-1,.L235,0,.L677-.L235
	.half	1
	.byte	102
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_IsWriteAllAndKilled')
	.sect	'.debug_loc'
.L236:
	.word	-1,.L237,0,.L698-.L237
	.half	2
	.byte	138,0
	.word	0,0
.L702:
	.word	-1,.L237,0,.L698-.L237
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L700:
	.word	-1,.L237,0,.L698-.L237
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_SetBlockPendingWriteAll')
	.sect	'.debug_loc'
.L204:
	.word	-1,.L205,0,.L666-.L205
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_UpdateConfigIdBlock')
	.sect	'.debug_loc'
.L232:
	.word	-1,.L233,0,.L676-.L233
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1489:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('NvM_ActEraseNvBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L111,.L641-.L111
	.sdecl	'.debug_frame',debug,cluster('NvM_ActFinishBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L119,.L619-.L119
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitNextBlockReadAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L125,.L621-.L125
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitNextBlockWriteAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L129,.L623-.L129
	.sdecl	'.debug_frame',debug,cluster('NvM_ActFinishMainJob')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L131,.L613-.L131
	.sdecl	'.debug_frame',debug,cluster('NvM_ActKillWriteAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L133,.L618-.L133
	.sdecl	'.debug_frame',debug,cluster('NvM_ActFinishReadBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L135,.L625-.L135
	.sdecl	'.debug_frame',debug,cluster('NvM_ActFinishReadBlockAndSetSkipped')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L139,.L582-.L139
	.sdecl	'.debug_frame',debug,cluster('NvM_ActFinishWriteBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L141,.L631-.L141
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L143,.L597-.L143
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitMainFsm')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L145,.L595-.L145
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitReadAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L149,.L602-.L149
	.sdecl	'.debug_frame',debug,cluster('NvM_ActFinishCfgIdCheck')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L151,.L624-.L151
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitWriteAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L153,.L607-.L153
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitWriteBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L155,.L608-.L155
	.sdecl	'.debug_frame',debug,cluster('NvM_ActFinishEraseBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L161,.L640-.L161
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitReadBlockSubFsm')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L163,.L604-.L163
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitRestoreBlockDefaultsSubFsm')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L167,.L606-.L167
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitWriteBlockFsm')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L169,.L610-.L169
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInitRestoreBlockDefaults')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L171,.L611-.L171
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_ActInvalidateNvBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L175,.L643-.L175
	.sdecl	'.debug_frame',debug,cluster('NvM_ActNop')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L177,.L668-.L177
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_ActWriteNvBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L179,.L645-.L179
	.sdecl	'.debug_frame',debug,cluster('NvM_ActReadNvBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L181,.L649-.L181
	.sdecl	'.debug_frame',debug,cluster('NvM_ActProcessCrcRead')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L183,.L652-.L183
	.sdecl	'.debug_frame',debug,cluster('NvM_ActReadCopyData')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L187,.L654-.L187
	.sdecl	'.debug_frame',debug,cluster('NvM_InternalCopyBufferedData')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L189,.L687-.L189
	.sdecl	'.debug_frame',debug,cluster('NvM_ActRestoreRomDefaults')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L191,.L655-.L191
	.sdecl	'.debug_frame',debug,cluster('NvM_ActFinishRestoreRomDefaults')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L193,.L656-.L193
	.sdecl	'.debug_frame',debug,cluster('NvM_ActSetInitialAttr')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L195,.L594-.L195
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_ActSetReqIntegrityFailed')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L197,.L663-.L197
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_ActSetReqSkipped')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L199,.L664-.L199
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_ActSetReqNotOk')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L201,.L665-.L201
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_ActSetReqOk')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L203,.L583-.L203
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_SetBlockPendingWriteAll')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L205,.L666-.L205
	.sdecl	'.debug_frame',debug,cluster('NvM_ActSetupRedundant')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L207,.L659-.L207
	.sdecl	'.debug_frame',debug,cluster('NvM_ActSetupOther')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L209,.L660-.L209
	.sdecl	'.debug_frame',debug,cluster('NvM_ActUpdateNvState')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L211,.L661-.L211
	.sdecl	'.debug_frame',debug,cluster('NvM_IntCreateNvState')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L213,.L693-.L213
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_ActGetMultiBlockJob')
	.sect	'.debug_frame'
	.word	20
	.word	.L1489,.L215,.L669-.L215
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_ActTestBlockBlank')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L217,.L657-.L217
	.sdecl	'.debug_frame',debug,cluster('NvM_ActValidateRam')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L219,.L658-.L219
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_ActWait')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L221,.L667-.L221
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_ActCopyNvDataToBuf')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L223,.L672-.L223
	.sdecl	'.debug_frame',debug,cluster('NvM_ActCancelNV')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L225,.L580-.L225
	.sdecl	'.debug_frame',debug,cluster('NvM_ActKillSubFsm')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L229,.L675-.L229
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_ActProcessCrc')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L231,.L644-.L231
	.sdecl	'.debug_frame',debug,cluster('NvM_UpdateConfigIdBlock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L233,.L676-.L233
	.sdecl	'.debug_frame',debug,cluster('NvM_InternalCopyData')
	.sect	'.debug_frame'
	.word	16
	.word	.L1489,.L235,.L677-.L235
	.byte	8,19,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_IsWriteAllAndKilled')
	.sect	'.debug_frame'
	.word	24
	.word	.L1489,.L237,.L698-.L237
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_IntUpdateCurrentBlockCRCCompareData')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L239,.L703-.L239
	.sdecl	'.debug_frame',debug,cluster('NvM_BlockNotification')
	.sect	'.debug_frame'
	.word	12
	.word	.L1489,.L241,.L584-.L241
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1490:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_5')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L113,.L530-.L113
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L115,.L510-.L115
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_14')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L117,.L575-.L117
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_7')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L121,.L540-.L121
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_6')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L123,.L535-.L123
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_8')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L127,.L545-.L127
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_13')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L137,.L570-.L137
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L147,.L515-.L147
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_12')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L157,.L565-.L157
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_11')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L159,.L560-.L159
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L165,.L520-.L165
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_4')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L173,.L525-.L173
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_9')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L185,.L550-.L185
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_10')
	.sect	'.debug_frame'
	.word	24
	.word	.L1490,.L227,.L555-.L227
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\eeprom\NvM\NvM_Act.c	  2850  
; ..\eeprom\NvM\NvM_Act.c	  2851  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_Act.c	  2852    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Act.c	  2853  
; ..\eeprom\NvM\NvM_Act.c	  2854  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Act.c	  2855   *  END OF FILE: NvM_Act.c
; ..\eeprom\NvM\NvM_Act.c	  2856   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Act.c	  2857  

	; Module end
