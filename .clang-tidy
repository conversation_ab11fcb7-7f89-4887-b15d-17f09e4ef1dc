---
Checks: >
  -*,
  bugprone-*,
  cert-*,
  clang-analyzer-*,
  misc-*,
  readability-*,
  -misc-unused-parameters,
  -readability-magic-numbers,
  -cppcoreguidelines-avoid-non-const-global-variables

CheckOptions:
  - key: readability-identifier-naming.VariableCase
    value: camelCase
  - key: readability-identifier-naming.FunctionCase  
    value: CamelCase
  - key: readability-identifier-naming.ConstantCase
    value: UPPER_CASE
  - key: misc-unused-parameters.StrictMode
    value: true

WarningsAsErrors: ''
HeaderFilterRegex: '.*'
FormatStyle: none
