	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc26060a -c99 --dep-file=mcal_src\\.Mcu_Crc.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Mcu_Crc.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Mcu_Crc.src ..\\mcal_src\\Mcu_Crc.c"
	.compiler_name		"ctc"
	.name	"Mcu_Crc"

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	61212
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_src\\Mcu_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	176
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	182
	.byte	5,1,3
	.word	206
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	208
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	231
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	262
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	299
	.byte	4
	.byte	'boolean',0,2,105,29
	.word	231
	.byte	6
	.byte	'unsigned int',0,4,7,4
	.byte	'unsigned_int',0,3,121,22
	.word	351
	.byte	7
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,8
	.byte	'EN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	388
	.byte	7
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,8
	.byte	'reserved_0',0,4
	.word	351
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	945
	.byte	7
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,8
	.byte	'STM0DIS',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'STM1DIS',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'STM2DIS',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,4
	.word	351
	.byte	29,0,2,35,2,0,4
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	1022
	.byte	7
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'BAUD2DIV',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'SRIDIV',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'LPDIV',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'SPBDIV',0,1
	.word	231
	.byte	4,4,2,35,2,8
	.byte	'FSI2DIV',0,1
	.word	231
	.byte	2,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'FSIDIV',0,1
	.word	231
	.byte	2,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	2,4,2,35,3,8
	.byte	'CLKSEL',0,1
	.word	231
	.byte	2,2,2,35,3,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	1158
	.byte	7
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,8
	.byte	'CANDIV',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'ERAYDIV',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'STMDIV',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'GTMDIV',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'ETHDIV',0,1
	.word	231
	.byte	4,4,2,35,2,8
	.byte	'ASCLINFDIV',0,1
	.word	231
	.byte	4,0,2,35,2,8
	.byte	'ASCLINSDIV',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'INSEL',0,1
	.word	231
	.byte	2,2,2,35,3,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	1440
	.byte	7
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,8
	.byte	'BBBDIV',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	351
	.byte	26,2,2,35,2,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	1678
	.byte	7
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,8
	.byte	'PLLDIV',0,1
	.word	231
	.byte	6,2,2,35,0,8
	.byte	'PLLSEL',0,1
	.word	231
	.byte	2,0,2,35,0,8
	.byte	'PLLERAYDIV',0,1
	.word	231
	.byte	6,2,2,35,1,8
	.byte	'PLLERAYSEL',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'SRIDIV',0,1
	.word	231
	.byte	6,2,2,35,2,8
	.byte	'SRISEL',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,149,1,3
	.word	1806
	.byte	7
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,152,1,16,4,8
	.byte	'SPBDIV',0,1
	.word	231
	.byte	6,2,2,35,0,8
	.byte	'SPBSEL',0,1
	.word	231
	.byte	2,0,2,35,0,8
	.byte	'GTMDIV',0,1
	.word	231
	.byte	6,2,2,35,1,8
	.byte	'GTMSEL',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'STMDIV',0,1
	.word	231
	.byte	6,2,2,35,2,8
	.byte	'STMSEL',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,163,1,3
	.word	2033
	.byte	7
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,166,1,16,4,8
	.byte	'MAXDIV',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	351
	.byte	26,2,2,35,2,8
	.byte	'UP',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,172,1,3
	.word	2252
	.byte	7
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,175,1,16,4,8
	.byte	'CPU0DIV',0,1
	.word	231
	.byte	6,2,2,35,0,8
	.byte	'reserved_6',0,4
	.word	351
	.byte	26,0,2,35,2,0,4
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,179,1,3
	.word	2380
	.byte	7
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,182,1,16,4,8
	.byte	'CHREV',0,1
	.word	231
	.byte	6,2,2,35,0,8
	.byte	'CHTEC',0,1
	.word	231
	.byte	2,0,2,35,0,8
	.byte	'CHID',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'EEA',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'UCODE',0,1
	.word	231
	.byte	7,0,2,35,2,8
	.byte	'FSIZE',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'SP',0,1
	.word	231
	.byte	2,2,2,35,3,8
	.byte	'SEC',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,193,1,3
	.word	2480
	.byte	7
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,196,1,16,4,8
	.byte	'PWD',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'START',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'CAL',0,4
	.word	351
	.byte	22,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	5,1,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,204,1,3
	.word	2688
	.byte	7
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,207,1,16,4,8
	.byte	'LOWER',0,2
	.word	262
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	231
	.byte	5,1,2,35,1,8
	.byte	'LLU',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'UPPER',0,2
	.word	262
	.byte	10,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	4,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'UOF',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,216,1,3
	.word	2853
	.byte	7
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,219,1,16,4,8
	.byte	'RESULT',0,2
	.word	262
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	231
	.byte	4,2,2,35,1,8
	.byte	'RDY',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'BUSY',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,226,1,3
	.word	3036
	.byte	7
	.byte	'_Ifx_SCU_EICR_Bits',0,4,229,1,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'EXIS0',0,1
	.word	231
	.byte	3,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'FEN0',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'REN0',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'LDEN0',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'EIEN0',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'INP0',0,1
	.word	231
	.byte	3,1,2,35,1,8
	.byte	'reserved_15',0,4
	.word	351
	.byte	5,12,2,35,2,8
	.byte	'EXIS1',0,1
	.word	231
	.byte	3,1,2,35,2,8
	.byte	'reserved_23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'FEN1',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'REN1',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'LDEN1',0,1
	.word	231
	.byte	1,5,2,35,3,8
	.byte	'EIEN1',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'INP1',0,1
	.word	231
	.byte	3,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EICR_Bits',0,4,248,1,3
	.word	3190
	.byte	7
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,251,1,16,4,8
	.byte	'INTF0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'INTF1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'INTF2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'INTF3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'INTF4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'INTF5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'INTF6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'INTF7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	351
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_EIFR_Bits',0,4,134,2,3
	.word	3554
	.byte	7
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,137,2,16,4,8
	.byte	'POL',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'MODE',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'ENON',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'PSEL',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,2
	.word	262
	.byte	12,0,2,35,0,8
	.byte	'EMSF',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'SEMSF',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	6,0,2,35,2,8
	.byte	'EMSFM',0,1
	.word	231
	.byte	2,6,2,35,3,8
	.byte	'SEMSFM',0,1
	.word	231
	.byte	2,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_EMSR_Bits',0,4,150,2,3
	.word	3765
	.byte	7
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,153,2,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'EDCON',0,2
	.word	262
	.byte	2,7,2,35,0,8
	.byte	'reserved_9',0,4
	.word	351
	.byte	23,0,2,35,2,0,4
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,158,2,3
	.word	4017
	.byte	7
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,161,2,16,4,8
	.byte	'ARI',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ARC',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	351
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,166,2,3
	.word	4135
	.byte	7
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,169,2,16,4,8
	.byte	'reserved_0',0,4
	.word	351
	.byte	28,4,2,35,2,8
	.byte	'EVR13OFF',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'BPEVR13OFF',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,176,2,3
	.word	4246
	.byte	7
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,179,2,16,4,8
	.byte	'ADC13V',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'ADCSWDV',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	7,1,2,35,3,8
	.byte	'VAL',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,186,2,3
	.word	4409
	.byte	7
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,189,2,16,4,8
	.byte	'EVR13OVMOD',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'EVR13UVMOD',0,1
	.word	231
	.byte	2,2,2,35,0,8
	.byte	'reserved_6',0,2
	.word	262
	.byte	10,0,2,35,0,8
	.byte	'SWDOVMOD',0,1
	.word	231
	.byte	2,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	2,4,2,35,2,8
	.byte	'SWDUVMOD',0,1
	.word	231
	.byte	2,2,2,35,2,8
	.byte	'reserved_22',0,2
	.word	262
	.byte	8,2,2,35,2,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,201,2,3
	.word	4571
	.byte	7
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,204,2,16,4,8
	.byte	'EVR13OVVAL',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'SWDOVVAL',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,212,2,3
	.word	4849
	.byte	7
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,215,2,16,4,8
	.byte	'reserved_0',0,4
	.word	351
	.byte	28,4,2,35,2,8
	.byte	'RSTSWDOFF',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'BPRSTSWDOFF',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,222,2,3
	.word	5028
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,225,2,16,4,8
	.byte	'SD33P',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'SD33I',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,4
	.word	351
	.byte	19,1,2,35,2,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,232,2,3
	.word	5188
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,235,2,16,4,8
	.byte	'SDFREQSPRD',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'TON',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'TOFF',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'SDSTEP',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'SYNCDIV',0,1
	.word	231
	.byte	3,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,244,2,3
	.word	5349
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,247,2,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'STBS',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'STSP',0,1
	.word	231
	.byte	2,4,2,35,1,8
	.byte	'NS',0,1
	.word	231
	.byte	2,2,2,35,1,8
	.byte	'OL',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'PIAD',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'ADCMODE',0,1
	.word	231
	.byte	4,4,2,35,2,8
	.byte	'ADCLPF',0,1
	.word	231
	.byte	2,2,2,35,2,8
	.byte	'ADCLSB',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'reserved_23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'SDLUT',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,134,3,3
	.word	5541
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,137,3,16,4,8
	.byte	'SDOLCON',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'MODSEL',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'MODLOW',0,1
	.word	231
	.byte	7,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'SDVOKLVL',0,1
	.word	231
	.byte	6,2,2,35,2,8
	.byte	'MODMAN',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'MODHIGH',0,1
	.word	231
	.byte	7,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,147,3,3
	.word	5837
	.byte	7
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,150,3,16,4,8
	.byte	'EVR13',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'OV13',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'OVSWD',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'UV13',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'UVSWD',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'BGPROK',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'reserved_11',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'SCMOD',0,1
	.word	231
	.byte	2,2,2,35,1,8
	.byte	'reserved_14',0,4
	.word	351
	.byte	18,0,2,35,2,0,4
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,164,3,3
	.word	6052
	.byte	7
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,167,3,16,4,8
	.byte	'EVR13UVVAL',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'SWDUVVAL',0,1
	.word	231
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	6,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,175,3,3
	.word	6341
	.byte	7
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,178,3,16,4,8
	.byte	'EN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'SEL0',0,1
	.word	231
	.byte	4,2,2,35,0,8
	.byte	'reserved_6',0,2
	.word	262
	.byte	10,0,2,35,0,8
	.byte	'EN1',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'NSEL',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'SEL1',0,1
	.word	231
	.byte	4,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	231
	.byte	2,0,2,35,2,8
	.byte	'DIV1',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,189,3,3
	.word	6520
	.byte	7
	.byte	'_Ifx_SCU_FDR_Bits',0,4,192,3,16,4,8
	.byte	'STEP',0,2
	.word	262
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	231
	.byte	4,2,2,35,1,8
	.byte	'DM',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'RESULT',0,2
	.word	262
	.byte	10,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	5,1,2,35,3,8
	.byte	'DISCLK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_FDR_Bits',0,4,200,3,3
	.word	6738
	.byte	7
	.byte	'_Ifx_SCU_FMR_Bits',0,4,203,3,16,4,8
	.byte	'FS0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'FS1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'FS2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'FS3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'FS4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'FS5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'FS6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'FS7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'FC0',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'FC1',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'FC2',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'FC3',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'FC4',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'FC5',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'FC6',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'FC7',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_FMR_Bits',0,4,223,3,3
	.word	6901
	.byte	7
	.byte	'_Ifx_SCU_ID_Bits',0,4,226,3,16,4,8
	.byte	'MODREV',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'MODTYPE',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'MODNUMBER',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_ID_Bits',0,4,231,3,3
	.word	7237
	.byte	7
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,234,3,16,4,8
	.byte	'IPEN00',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'IPEN01',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'IPEN02',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'IPEN03',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'IPEN04',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IPEN05',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'IPEN06',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'IPEN07',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	5,3,2,35,1,8
	.byte	'GEEN0',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'IGP0',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'IPEN10',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'IPEN11',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'IPEN12',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'IPEN13',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'IPEN14',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'IPEN15',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'IPEN16',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'IPEN17',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	5,3,2,35,3,8
	.byte	'GEEN1',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'IGP1',0,1
	.word	231
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_SCU_IGCR_Bits',0,4,130,4,3
	.word	7344
	.byte	7
	.byte	'_Ifx_SCU_IN_Bits',0,4,133,4,16,4,8
	.byte	'P0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	351
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_IN_Bits',0,4,138,4,3
	.word	7796
	.byte	7
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,141,4,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	4,4,2,35,0,8
	.byte	'PC0',0,1
	.word	231
	.byte	4,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'PC1',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_IOCR_Bits',0,4,148,4,3
	.word	7895
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,151,4,16,4,8
	.byte	'LBISTREQ',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'LBISTREQP',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'PATTERNS',0,2
	.word	262
	.byte	14,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,157,4,3
	.word	8045
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,160,4,16,4,8
	.byte	'SEED',0,4
	.word	351
	.byte	23,9,2,35,2,8
	.byte	'reserved_23',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'SPLITSH',0,1
	.word	231
	.byte	3,5,2,35,3,8
	.byte	'BODY',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'LBISTFREQU',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,167,4,3
	.word	8194
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,170,4,16,4,8
	.byte	'SIGNATURE',0,4
	.word	351
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	7,1,2,35,3,8
	.byte	'LBISTDONE',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,175,4,3
	.word	8355
	.byte	7
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,4,178,4,16,4,8
	.byte	'reserved_0',0,2
	.word	262
	.byte	16,0,2,35,0,8
	.byte	'LS',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,2
	.word	262
	.byte	14,1,2,35,2,8
	.byte	'LSEN',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_LCLCON0_Bits',0,4,184,4,3
	.word	8485
	.byte	7
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,187,4,16,4,8
	.byte	'LCLT0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'LCLT1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	351
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,192,4,3
	.word	8619
	.byte	7
	.byte	'_Ifx_SCU_MANID_Bits',0,4,195,4,16,4,8
	.byte	'DEPT',0,1
	.word	231
	.byte	5,3,2,35,0,8
	.byte	'MANUF',0,2
	.word	262
	.byte	11,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_MANID_Bits',0,4,200,4,3
	.word	8734
	.byte	7
	.byte	'_Ifx_SCU_OMR_Bits',0,4,203,4,16,4,8
	.byte	'PS0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,2
	.word	262
	.byte	14,0,2,35,0,8
	.byte	'PCL0',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,2
	.word	262
	.byte	14,0,2,35,2,0,4
	.byte	'Ifx_SCU_OMR_Bits',0,4,211,4,3
	.word	8845
	.byte	7
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,214,4,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PLLLV',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'OSCRES',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'GAINSEL',0,1
	.word	231
	.byte	2,3,2,35,0,8
	.byte	'MODE',0,1
	.word	231
	.byte	2,1,2,35,0,8
	.byte	'SHBY',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'PLLHV',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'reserved_9',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'X1D',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'X1DEN',0,1
	.word	231
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'OSCVAL',0,1
	.word	231
	.byte	5,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	2,1,2,35,2,8
	.byte	'APREN',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,231,4,3
	.word	9003
	.byte	7
	.byte	'_Ifx_SCU_OUT_Bits',0,4,234,4,16,4,8
	.byte	'P0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	351
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_OUT_Bits',0,4,239,4,3
	.word	9343
	.byte	7
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,242,4,16,4,8
	.byte	'CSEL0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'CSEL1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'CSEL2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,2
	.word	262
	.byte	13,0,2,35,0,8
	.byte	'OVSTRT',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'OVSTP',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'DCINVAL',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	5,0,2,35,2,8
	.byte	'OVCONF',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'POVCONF',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	6,0,2,35,3,0,4
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,255,4,3
	.word	9444
	.byte	7
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,130,5,16,4,8
	.byte	'OVEN0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'OVEN1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'OVEN2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,4
	.word	351
	.byte	29,0,2,35,2,0,4
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,136,5,3
	.word	9711
	.byte	7
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,139,5,16,4,8
	.byte	'PDIS0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PDIS1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	351
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDISC_Bits',0,4,144,5,3
	.word	9847
	.byte	7
	.byte	'_Ifx_SCU_PDR_Bits',0,4,147,5,16,4,8
	.byte	'PD0',0,1
	.word	231
	.byte	3,5,2,35,0,8
	.byte	'PL0',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'PD1',0,1
	.word	231
	.byte	3,1,2,35,0,8
	.byte	'PL1',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	351
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDR_Bits',0,4,154,5,3
	.word	9958
	.byte	7
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,157,5,16,4,8
	.byte	'PDR0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PDR1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'PDR2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'PDR3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'PDR4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'PDR5',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'PDR6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PDR7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	351
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDRR_Bits',0,4,168,5,3
	.word	10091
	.byte	7
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,171,5,16,4,8
	.byte	'VCOBYP',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'VCOPWD',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'MODEN',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'SETFINDIS',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'CLRFINDIS',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'OSCDISCDIS',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'reserved_7',0,2
	.word	262
	.byte	2,7,2,35,0,8
	.byte	'NDIV',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'PLLPWD',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'RESLD',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	5,0,2,35,2,8
	.byte	'PDIV',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,188,5,3
	.word	10294
	.byte	7
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,191,5,16,4,8
	.byte	'K2DIV',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'K3DIV',0,1
	.word	231
	.byte	7,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'K1DIV',0,1
	.word	231
	.byte	7,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	262
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,199,5,3
	.word	10650
	.byte	7
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,202,5,16,4,8
	.byte	'MODCFG',0,2
	.word	262
	.byte	16,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,206,5,3
	.word	10828
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,209,5,16,4,8
	.byte	'VCOBYP',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'VCOPWD',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'SETFINDIS',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'CLRFINDIS',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'OSCDISCDIS',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'reserved_7',0,2
	.word	262
	.byte	2,7,2,35,0,8
	.byte	'NDIV',0,1
	.word	231
	.byte	5,2,2,35,1,8
	.byte	'reserved_14',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'PLLPWD',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'RESLD',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	5,0,2,35,2,8
	.byte	'PDIV',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,226,5,3
	.word	10928
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,229,5,16,4,8
	.byte	'K2DIV',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'K3DIV',0,1
	.word	231
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	231
	.byte	4,0,2,35,1,8
	.byte	'K1DIV',0,1
	.word	231
	.byte	7,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	262
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,237,5,3
	.word	11298
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,240,5,16,4,8
	.byte	'VCOBYST',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'PWDSTAT',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'VCOLOCK',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'FINDIS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'K1RDY',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'K2RDY',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,4
	.word	351
	.byte	26,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,249,5,3
	.word	11484
	.byte	7
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,252,5,16,4,8
	.byte	'VCOBYST',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'VCOLOCK',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'FINDIS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'K1RDY',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'K2RDY',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'MODRUN',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	351
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,135,6,3
	.word	11682
	.byte	7
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,138,6,16,4,8
	.byte	'REQSLP',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'SMUSLP',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,1
	.word	231
	.byte	5,0,2,35,0,8
	.byte	'PMST',0,1
	.word	231
	.byte	3,5,2,35,1,8
	.byte	'reserved_11',0,4
	.word	351
	.byte	21,0,2,35,2,0,4
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,145,6,3
	.word	11915
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,148,6,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1WKEN',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'PINAWKEN',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'PINBWKEN',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'ESR0DFEN',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'ESR0EDCON',0,1
	.word	231
	.byte	2,1,2,35,0,8
	.byte	'ESR1DFEN',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'ESR1EDCON',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'PINADFEN',0,1
	.word	231
	.byte	1,5,2,35,1,8
	.byte	'PINAEDCON',0,1
	.word	231
	.byte	2,3,2,35,1,8
	.byte	'PINBDFEN',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'PINBEDCON',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'STBYRAMSEL',0,1
	.word	231
	.byte	2,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'WUTWKEN',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	2,1,2,35,2,8
	.byte	'PORSTDF',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'DCDCSYNC',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	3,3,2,35,3,8
	.byte	'ESR0TRIST',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,174,6,3
	.word	12067
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,177,6,16,4,8
	.byte	'reserved_0',0,2
	.word	262
	.byte	12,4,2,35,0,8
	.byte	'IRADIS',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'reserved_13',0,4
	.word	351
	.byte	14,5,2,35,2,8
	.byte	'STBYEVEN',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'STBYEV',0,1
	.word	231
	.byte	3,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,185,6,3
	.word	12626
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,4,188,6,16,4,8
	.byte	'WUTREL',0,4
	.word	351
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	4,4,2,35,3,8
	.byte	'WUTDIV',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'WUTEN',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'WUTMODE',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,4,196,6,3
	.word	12809
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,199,6,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'ESR1WKP',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'ESR1OVRUN',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'PINAWKP',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'PINAOVRUN',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'PINBWKP',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PINBOVRUN',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'PORSTDF',0,1
	.word	231
	.byte	1,6,2,35,1,8
	.byte	'HWCFGEVR',0,1
	.word	231
	.byte	3,3,2,35,1,8
	.byte	'STBYRAM',0,1
	.word	231
	.byte	2,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'WUTWKP',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'WUTOVRUN',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'WUTWKEN',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'ESR1WKEN',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'PINAWKEN',0,1
	.word	231
	.byte	1,2,2,35,2,8
	.byte	'PINBWKEN',0,1
	.word	231
	.byte	1,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	262
	.byte	4,5,2,35,2,8
	.byte	'ESR0TRIST',0,1
	.word	231
	.byte	1,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'WUTEN',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'WUTMODE',0,1
	.word	231
	.byte	1,1,2,35,3,8
	.byte	'WUTRUN',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,226,6,3
	.word	12978
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,229,6,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'ESR1WKPCLR',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'ESR1OVRUNCLR',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'PINAWKPCLR',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'PINAOVRUNCLR',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'PINBWKPCLR',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PINBOVRUNCLR',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'WUTWKPCLR',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'WUTOVRUNCLR',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,2
	.word	262
	.byte	14,0,2,35,2,0,4
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,242,6,3
	.word	13545
	.byte	7
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,4,245,6,16,4,8
	.byte	'WUTCNT',0,4
	.word	351
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	231
	.byte	7,1,2,35,3,8
	.byte	'VAL',0,1
	.word	231
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,4,250,6,3
	.word	13861
	.byte	7
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,253,6,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'CLRC',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,2
	.word	262
	.byte	10,4,2,35,0,8
	.byte	'CSS0',0,1
	.word	231
	.byte	1,3,2,35,1,8
	.byte	'CSS1',0,1
	.word	231
	.byte	1,2,2,35,1,8
	.byte	'CSS2',0,1
	.word	231
	.byte	1,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'USRINFO',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,135,7,3
	.word	13980
	.byte	7
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,138,7,16,4,8
	.byte	'ESR0',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'ESR1',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	2,2,2,35,0,8
	.byte	'SMU',0,1
	.word	231
	.byte	2,0,2,35,0,8
	.byte	'SW',0,1
	.word	231
	.byte	2,6,2,35,1,8
	.byte	'STM0',0,1
	.word	231
	.byte	2,4,2,35,1,8
	.byte	'STM1',0,1
	.word	231
	.byte	2,2,2,35,1,8
	.byte	'STM2',0,1
	.word	231
	.byte	2,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,149,7,3
	.word	14189
	.byte	7
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,152,7,16,4,8
	.byte	'ESR0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMU',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'SW',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'STM0',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'STM1',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'STM2',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	231
	.byte	8,0,2,35,1,8
	.byte	'PORST',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'CB0',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'CB1',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'CB3',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	2,1,2,35,2,8
	.byte	'EVR13',0,1
	.word	231
	.byte	1,0,2,35,2,8
	.byte	'EVR33',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'SWD',0,1
	.word	231
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	231
	.byte	2,4,2,35,3,8
	.byte	'STBYR',0,1
	.word	231
	.byte	1,3,2,35,3,8
	.byte	'reserved_29',0,1
	.word	231
	.byte	3,0,2,35,3,0,4
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,175,7,3
	.word	14400
	.byte	7
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,178,7,16,4,8
	.byte	'HBT',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	351
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,182,7,3
	.word	14832
	.byte	7
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,185,7,16,4,8
	.byte	'HWCFG',0,1
	.word	231
	.byte	8,0,2,35,0,8
	.byte	'FTM',0,1
	.word	231
	.byte	7,1,2,35,1,8
	.byte	'MODE',0,1
	.word	231
	.byte	1,0,2,35,1,8
	.byte	'FCBAE',0,1
	.word	231
	.byte	1,7,2,35,2,8
	.byte	'LUDIS',0,1
	.word	231
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	231
	.byte	1,5,2,35,2,8
	.byte	'TRSTL',0,1
	.word	231
	.byte	1,4,2,35,2,8
	.byte	'SPDEN',0,1
	.word	231
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	231
	.byte	3,0,2,35,2,8
	.byte	'RAMINT',0,1
	.word	231
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	231
	.byte	7,0,2,35,3,0,4
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,198,7,3
	.word	14928
	.byte	7
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,201,7,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'SWRSTREQ',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	351
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,206,7,3
	.word	15188
	.byte	7
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,209,7,16,4,8
	.byte	'CCTRIG0',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'RAMINTM',0,1
	.word	231
	.byte	2,4,2,35,0,8
	.byte	'SETLUDIS',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'reserved_5',0,1
	.word	231
	.byte	3,0,2,35,0,8
	.byte	'DATM',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'reserved_9',0,4
	.word	351
	.byte	23,0,2,35,2,0,4
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,218,7,3
	.word	15313
	.byte	7
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,221,7,16,4,8
	.byte	'ESR0T',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	351
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,228,7,3
	.word	15510
	.byte	7
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,231,7,16,4,8
	.byte	'ESR0T',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	351
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,238,7,3
	.word	15663
	.byte	7
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,241,7,16,4,8
	.byte	'ESR0T',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	351
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,248,7,3
	.word	15816
	.byte	7
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,251,7,16,4,8
	.byte	'ESR0T',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	351
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,130,8,3
	.word	15969
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,133,8,16,4,6
	.byte	'unsigned int',0,4,7,8
	.byte	'ENDINIT',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'LCK',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'PW',0,4
	.word	16156
	.byte	14,16,2,35,0,8
	.byte	'REL',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,139,8,3
	.word	16124
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,142,8,16,4,8
	.byte	'reserved_0',0,1
	.word	231
	.byte	2,6,2,35,0,8
	.byte	'IR0',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'DR',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IR1',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'UR',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PAR',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'TCR',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'TCTR',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,154,8,3
	.word	16270
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,157,8,16,4,8
	.byte	'AE',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'OE',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'IS0',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'DS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'TO',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IS1',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'US',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PAS',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'TCS',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'TCT',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'TIM',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,170,8,3
	.word	16508
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,173,8,16,4,8
	.byte	'ENDINIT',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'LCK',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'PW',0,4
	.word	16156
	.byte	14,16,2,35,0,8
	.byte	'REL',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,179,8,3
	.word	16731
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,182,8,16,4,8
	.byte	'CLRIRF',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'IR0',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'DR',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IR1',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'UR',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PAR',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'TCR',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'TCTR',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,195,8,3
	.word	16857
	.byte	7
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,198,8,16,4,8
	.byte	'AE',0,1
	.word	231
	.byte	1,7,2,35,0,8
	.byte	'OE',0,1
	.word	231
	.byte	1,6,2,35,0,8
	.byte	'IS0',0,1
	.word	231
	.byte	1,5,2,35,0,8
	.byte	'DS',0,1
	.word	231
	.byte	1,4,2,35,0,8
	.byte	'TO',0,1
	.word	231
	.byte	1,3,2,35,0,8
	.byte	'IS1',0,1
	.word	231
	.byte	1,2,2,35,0,8
	.byte	'US',0,1
	.word	231
	.byte	1,1,2,35,0,8
	.byte	'PAS',0,1
	.word	231
	.byte	1,0,2,35,0,8
	.byte	'TCS',0,1
	.word	231
	.byte	1,7,2,35,1,8
	.byte	'TCT',0,1
	.word	231
	.byte	7,0,2,35,1,8
	.byte	'TIM',0,2
	.word	262
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,211,8,3
	.word	17109
	.byte	9,4,219,8,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,6
	.byte	'int',0,4,5,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	388
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ACCEN0',0,4,224,8,3
	.word	17328
	.byte	9,4,227,8,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	945
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ACCEN1',0,4,232,8,3
	.word	17399
	.byte	9,4,235,8,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1022
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ARSTDIS',0,4,240,8,3
	.word	17463
	.byte	9,4,243,8,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1158
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON0',0,4,248,8,3
	.word	17528
	.byte	9,4,251,8,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1440
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON1',0,4,128,9,3
	.word	17593
	.byte	9,4,131,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1678
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON2',0,4,136,9,3
	.word	17658
	.byte	9,4,139,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1806
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON3',0,4,144,9,3
	.word	17723
	.byte	9,4,147,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2033
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON4',0,4,152,9,3
	.word	17788
	.byte	9,4,155,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2252
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON5',0,4,160,9,3
	.word	17853
	.byte	9,4,163,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2380
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON6',0,4,168,9,3
	.word	17918
	.byte	9,4,171,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2480
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CHIPID',0,4,176,9,3
	.word	17983
	.byte	9,4,179,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2688
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSCON',0,4,184,9,3
	.word	18047
	.byte	9,4,187,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2853
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSLIM',0,4,192,9,3
	.word	18111
	.byte	9,4,195,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3036
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSSTAT',0,4,200,9,3
	.word	18175
	.byte	9,4,203,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3190
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EICR',0,4,208,9,3
	.word	18240
	.byte	9,4,211,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3554
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EIFR',0,4,216,9,3
	.word	18302
	.byte	9,4,219,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3765
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EMSR',0,4,224,9,3
	.word	18364
	.byte	9,4,227,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4017
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ESRCFG',0,4,232,9,3
	.word	18426
	.byte	9,4,235,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4135
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ESROCFG',0,4,240,9,3
	.word	18490
	.byte	9,4,243,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4246
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVR13CON',0,4,248,9,3
	.word	18555
	.byte	9,4,251,9,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4409
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,128,10,3
	.word	18621
	.byte	9,4,131,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4571
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,136,10,3
	.word	18689
	.byte	9,4,139,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4849
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVROVMON',0,4,144,10,3
	.word	18757
	.byte	9,4,147,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5028
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRRSTCON',0,4,152,10,3
	.word	18823
	.byte	9,4,155,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5188
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,160,10,3
	.word	18890
	.byte	9,4,163,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5349
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,168,10,3
	.word	18959
	.byte	9,4,171,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5541
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,176,10,3
	.word	19027
	.byte	9,4,179,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5837
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,184,10,3
	.word	19095
	.byte	9,4,187,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6052
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSTAT',0,4,192,10,3
	.word	19163
	.byte	9,4,195,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6341
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRUVMON',0,4,200,10,3
	.word	19228
	.byte	9,4,203,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6520
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EXTCON',0,4,208,10,3
	.word	19294
	.byte	9,4,211,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6738
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_FDR',0,4,216,10,3
	.word	19358
	.byte	9,4,219,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6901
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_FMR',0,4,224,10,3
	.word	19419
	.byte	9,4,227,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7237
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ID',0,4,232,10,3
	.word	19480
	.byte	9,4,235,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7344
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IGCR',0,4,240,10,3
	.word	19540
	.byte	9,4,243,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7796
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IN',0,4,248,10,3
	.word	19602
	.byte	9,4,251,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7895
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IOCR',0,4,128,11,3
	.word	19662
	.byte	9,4,131,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8045
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,136,11,3
	.word	19724
	.byte	9,4,139,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8194
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,144,11,3
	.word	19792
	.byte	9,4,147,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8355
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,152,11,3
	.word	19860
	.byte	9,4,155,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8485
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LCLCON0',0,4,160,11,3
	.word	19928
	.byte	9,4,163,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8619
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LCLTEST',0,4,168,11,3
	.word	19993
	.byte	9,4,171,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8734
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_MANID',0,4,176,11,3
	.word	20058
	.byte	9,4,179,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8845
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OMR',0,4,184,11,3
	.word	20121
	.byte	9,4,187,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9003
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OSCCON',0,4,192,11,3
	.word	20182
	.byte	9,4,195,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9343
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OUT',0,4,200,11,3
	.word	20246
	.byte	9,4,203,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9444
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OVCCON',0,4,208,11,3
	.word	20307
	.byte	9,4,211,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9711
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OVCENABLE',0,4,216,11,3
	.word	20371
	.byte	9,4,219,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9847
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDISC',0,4,224,11,3
	.word	20438
	.byte	9,4,227,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9958
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDR',0,4,232,11,3
	.word	20501
	.byte	9,4,235,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10091
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDRR',0,4,240,11,3
	.word	20562
	.byte	9,4,243,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10294
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON0',0,4,248,11,3
	.word	20624
	.byte	9,4,251,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10650
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON1',0,4,128,12,3
	.word	20689
	.byte	9,4,131,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10828
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON2',0,4,136,12,3
	.word	20754
	.byte	9,4,139,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10928
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,144,12,3
	.word	20819
	.byte	9,4,147,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11298
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,152,12,3
	.word	20888
	.byte	9,4,155,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11484
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,160,12,3
	.word	20957
	.byte	9,4,163,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11682
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLSTAT',0,4,168,12,3
	.word	21026
	.byte	9,4,171,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11915
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMCSR',0,4,176,12,3
	.word	21091
	.byte	9,4,179,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12067
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR0',0,4,184,12,3
	.word	21154
	.byte	9,4,187,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12626
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR1',0,4,192,12,3
	.word	21219
	.byte	9,4,195,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12809
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR3',0,4,200,12,3
	.word	21284
	.byte	9,4,203,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12978
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWSTAT',0,4,208,12,3
	.word	21349
	.byte	9,4,211,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13545
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,216,12,3
	.word	21415
	.byte	9,4,219,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13861
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWUTCNT',0,4,224,12,3
	.word	21484
	.byte	9,4,227,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14189
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTCON',0,4,232,12,3
	.word	21551
	.byte	9,4,235,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13980
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTCON2',0,4,240,12,3
	.word	21615
	.byte	9,4,243,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14400
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTSTAT',0,4,248,12,3
	.word	21680
	.byte	9,4,251,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14832
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SAFECON',0,4,128,13,3
	.word	21745
	.byte	9,4,131,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14928
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_STSTAT',0,4,136,13,3
	.word	21810
	.byte	9,4,139,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15188
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SWRSTCON',0,4,144,13,3
	.word	21874
	.byte	9,4,147,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15313
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SYSCON',0,4,152,13,3
	.word	21940
	.byte	9,4,155,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15510
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPCLR',0,4,160,13,3
	.word	22004
	.byte	9,4,163,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15663
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPDIS',0,4,168,13,3
	.word	22069
	.byte	9,4,171,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15816
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPSET',0,4,176,13,3
	.word	22134
	.byte	9,4,179,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15969
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPSTAT',0,4,184,13,3
	.word	22199
	.byte	9,4,187,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16124
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,192,13,3
	.word	22265
	.byte	9,4,195,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16270
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,200,13,3
	.word	22334
	.byte	9,4,203,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16508
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,208,13,3
	.word	22403
	.byte	9,4,211,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16731
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON0',0,4,216,13,3
	.word	22470
	.byte	9,4,219,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16857
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON1',0,4,224,13,3
	.word	22537
	.byte	9,4,227,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17109
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_SR',0,4,232,13,3
	.word	22604
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU',0,4,243,13,25,12,10
	.byte	'CON0',0,4
	.word	22265
	.byte	2,35,0,10
	.byte	'CON1',0,4
	.word	22334
	.byte	2,35,4,10
	.byte	'SR',0,4
	.word	22403
	.byte	2,35,8,0,11
	.word	22669
	.byte	4
	.byte	'Ifx_SCU_WDTCPU',0,4,248,13,3
	.word	22732
	.byte	7
	.byte	'_Ifx_SCU_WDTS',0,4,251,13,25,12,10
	.byte	'CON0',0,4
	.word	22470
	.byte	2,35,0,10
	.byte	'CON1',0,4
	.word	22537
	.byte	2,35,4,10
	.byte	'SR',0,4
	.word	22604
	.byte	2,35,8,0,11
	.word	22761
	.byte	4
	.byte	'Ifx_SCU_WDTS',0,4,128,14,3
	.word	22822
	.byte	7
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,5,45,16,4,8
	.byte	'EN0',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'EN1',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'EN2',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'EN3',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'EN4',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'EN5',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'EN6',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'EN7',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'EN8',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'EN9',0,4
	.word	16156
	.byte	1,22,2,35,0,8
	.byte	'EN10',0,4
	.word	16156
	.byte	1,21,2,35,0,8
	.byte	'EN11',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'EN12',0,4
	.word	16156
	.byte	1,19,2,35,0,8
	.byte	'EN13',0,4
	.word	16156
	.byte	1,18,2,35,0,8
	.byte	'EN14',0,4
	.word	16156
	.byte	1,17,2,35,0,8
	.byte	'EN15',0,4
	.word	16156
	.byte	1,16,2,35,0,8
	.byte	'EN16',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'EN17',0,4
	.word	16156
	.byte	1,14,2,35,0,8
	.byte	'EN18',0,4
	.word	16156
	.byte	1,13,2,35,0,8
	.byte	'EN19',0,4
	.word	16156
	.byte	1,12,2,35,0,8
	.byte	'EN20',0,4
	.word	16156
	.byte	1,11,2,35,0,8
	.byte	'EN21',0,4
	.word	16156
	.byte	1,10,2,35,0,8
	.byte	'EN22',0,4
	.word	16156
	.byte	1,9,2,35,0,8
	.byte	'EN23',0,4
	.word	16156
	.byte	1,8,2,35,0,8
	.byte	'EN24',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'EN25',0,4
	.word	16156
	.byte	1,6,2,35,0,8
	.byte	'EN26',0,4
	.word	16156
	.byte	1,5,2,35,0,8
	.byte	'EN27',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'EN28',0,4
	.word	16156
	.byte	1,3,2,35,0,8
	.byte	'EN29',0,4
	.word	16156
	.byte	1,2,2,35,0,8
	.byte	'EN30',0,4
	.word	16156
	.byte	1,1,2,35,0,8
	.byte	'EN31',0,4
	.word	16156
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN0_Bits',0,5,79,3
	.word	22849
	.byte	7
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,5,82,16,4,8
	.byte	'reserved_0',0,4
	.word	16156
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN1_Bits',0,5,85,3
	.word	23406
	.byte	7
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,5,88,16,4,8
	.byte	'SEL0',0,4
	.word	16156
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	16156
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	16156
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	16156
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,5,95,3
	.word	23483
	.byte	7
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,5,98,16,4,8
	.byte	'SEL0',0,4
	.word	16156
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	16156
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	16156
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	16156
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,5,105,3
	.word	23637
	.byte	7
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,5,108,16,4,8
	.byte	'TO_ADDR',0,4
	.word	16156
	.byte	20,12,2,35,0,8
	.byte	'TO_W1R0',0,4
	.word	16156
	.byte	1,11,2,35,0,8
	.byte	'reserved_21',0,4
	.word	16156
	.byte	11,0,2,35,0,0,4
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,5,113,3
	.word	23791
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,5,116,16,4,8
	.byte	'BRG_MODE',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'MSK_WR_RSP',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	6,24,2,35,0,8
	.byte	'MODE_UP_PGR',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'BUFF_OVL',0,4
	.word	16156
	.byte	1,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'SYNC_INPUT_REG',0,4
	.word	16156
	.byte	1,19,2,35,0,8
	.byte	'reserved_13',0,4
	.word	16156
	.byte	3,16,2,35,0,8
	.byte	'BRG_RST',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'reserved_17',0,4
	.word	16156
	.byte	7,8,2,35,0,8
	.byte	'BUFF_DPT',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,5,129,1,3
	.word	23919
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,5,132,1,16,4,8
	.byte	'NEW_TRAN_PTR',0,4
	.word	16156
	.byte	5,27,2,35,0,8
	.byte	'FIRST_RSP_PTR',0,4
	.word	16156
	.byte	5,22,2,35,0,8
	.byte	'TRAN_IN_PGR',0,4
	.word	16156
	.byte	5,17,2,35,0,8
	.byte	'ABT_TRAN_PGR',0,4
	.word	16156
	.byte	5,12,2,35,0,8
	.byte	'FBC',0,4
	.word	16156
	.byte	6,6,2,35,0,8
	.byte	'RSP_TRAN_RDY',0,4
	.word	16156
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,5,140,1,3
	.word	24226
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,5,143,1,16,4,8
	.byte	'TRAN_IN_PGR2',0,4
	.word	16156
	.byte	5,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	16156
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,5,147,1,3
	.word	24428
	.byte	7
	.byte	'_Ifx_GTM_CLC_Bits',0,5,150,1,16,4,8
	.byte	'DISR',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'DISS',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'EDIS',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_CLC_Bits',0,5,157,1,3
	.word	24541
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,5,160,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,5,164,1,3
	.word	24684
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,5,167,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'CLK6_SEL',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	16156
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,5,172,1,3
	.word	24801
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,5,175,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'CLK7_SEL',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	16156
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,5,180,1,3
	.word	24936
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,5,183,1,16,4,8
	.byte	'EN_CLK0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'EN_CLK1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'EN_CLK2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'EN_CLK3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'EN_CLK4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'EN_CLK5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'EN_CLK6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'EN_CLK7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'EN_ECLK0',0,4
	.word	16156
	.byte	2,14,2,35,0,8
	.byte	'EN_ECLK1',0,4
	.word	16156
	.byte	2,12,2,35,0,8
	.byte	'EN_ECLK2',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'EN_FXCLK',0,4
	.word	16156
	.byte	2,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,5,198,1,3
	.word	25071
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,5,201,1,16,4,8
	.byte	'ECLK_DEN',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,5,205,1,3
	.word	25391
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,5,208,1,16,4,8
	.byte	'ECLK_NUM',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,5,212,1,3
	.word	25503
	.byte	7
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,5,215,1,16,4,8
	.byte	'FXCLK_SEL',0,4
	.word	16156
	.byte	4,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,5,219,1,3
	.word	25615
	.byte	7
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,5,222,1,16,4,8
	.byte	'GCLK_DEN',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,5,226,1,3
	.word	25731
	.byte	7
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,5,229,1,16,4,8
	.byte	'GCLK_NUM',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,5,233,1,3
	.word	25843
	.byte	7
	.byte	'_Ifx_GTM_CTRL_Bits',0,5,236,1,16,4,8
	.byte	'RF_PROT',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'TO_MODE',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'TO_VAL',0,4
	.word	16156
	.byte	5,23,2,35,0,8
	.byte	'reserved_9',0,4
	.word	16156
	.byte	23,0,2,35,0,0,4
	.byte	'Ifx_GTM_CTRL_Bits',0,5,243,1,3
	.word	25955
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,5,246,1,16,4,8
	.byte	'O1SEL_0',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	16156
	.byte	2,29,2,35,0,8
	.byte	'SWAP_0',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'O1F_0',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'O1SEL_1',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'I1SEL_1',0,4
	.word	16156
	.byte	1,22,2,35,0,8
	.byte	'SH_EN_1',0,4
	.word	16156
	.byte	1,21,2,35,0,8
	.byte	'SWAP_1',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'O1F_1',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'O1SEL_2',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'I1SEL_2',0,4
	.word	16156
	.byte	1,14,2,35,0,8
	.byte	'SH_EN_2',0,4
	.word	16156
	.byte	1,13,2,35,0,8
	.byte	'SWAP_2',0,4
	.word	16156
	.byte	1,12,2,35,0,8
	.byte	'O1F_2',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'reserved_22',0,4
	.word	16156
	.byte	2,8,2,35,0,8
	.byte	'O1SEL_3',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'I1SEL_3',0,4
	.word	16156
	.byte	1,6,2,35,0,8
	.byte	'SH_EN_3',0,4
	.word	16156
	.byte	1,5,2,35,0,8
	.byte	'SWAP_3',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'O1F_3',0,4
	.word	16156
	.byte	2,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,5,143,2,3
	.word	26108
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,5,146,2,16,4,8
	.byte	'POL0_0',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'OC0_0',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'SL0_0',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'DT0_0',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'POL1_0',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'OC1_0',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'SL1_0',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'DT1_0',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'POL0_1',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'OC0_1',0,4
	.word	16156
	.byte	1,22,2,35,0,8
	.byte	'SL0_1',0,4
	.word	16156
	.byte	1,21,2,35,0,8
	.byte	'DT0_1',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'POL1_1',0,4
	.word	16156
	.byte	1,19,2,35,0,8
	.byte	'OC1_1',0,4
	.word	16156
	.byte	1,18,2,35,0,8
	.byte	'SL1_1',0,4
	.word	16156
	.byte	1,17,2,35,0,8
	.byte	'DT1_1',0,4
	.word	16156
	.byte	1,16,2,35,0,8
	.byte	'POL0_2',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'OC0_2',0,4
	.word	16156
	.byte	1,14,2,35,0,8
	.byte	'SL0_2',0,4
	.word	16156
	.byte	1,13,2,35,0,8
	.byte	'DT0_2',0,4
	.word	16156
	.byte	1,12,2,35,0,8
	.byte	'POL1_2',0,4
	.word	16156
	.byte	1,11,2,35,0,8
	.byte	'OC1_2',0,4
	.word	16156
	.byte	1,10,2,35,0,8
	.byte	'SL1_2',0,4
	.word	16156
	.byte	1,9,2,35,0,8
	.byte	'DT1_2',0,4
	.word	16156
	.byte	1,8,2,35,0,8
	.byte	'POL0_3',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'OC0_3',0,4
	.word	16156
	.byte	1,6,2,35,0,8
	.byte	'SL0_3',0,4
	.word	16156
	.byte	1,5,2,35,0,8
	.byte	'DT0_3',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'POL1_3',0,4
	.word	16156
	.byte	1,3,2,35,0,8
	.byte	'OC1_3',0,4
	.word	16156
	.byte	1,2,2,35,0,8
	.byte	'SL1_3',0,4
	.word	16156
	.byte	1,1,2,35,0,8
	.byte	'DT1_3',0,4
	.word	16156
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,5,180,2,3
	.word	26620
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,5,183,2,16,4,8
	.byte	'POL0_0_SR',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'OC0_0_SR',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'SL0_0_SR',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'DT0_0_SR',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'POL1_0_SR',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'OC1_0_SR',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'SL1_0_SR',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'DT1_0_SR',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'POL0_1_SR',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'OC0_1_SR',0,4
	.word	16156
	.byte	1,22,2,35,0,8
	.byte	'SL0_1_SR',0,4
	.word	16156
	.byte	1,21,2,35,0,8
	.byte	'DT0_1_SR',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'POL1_1_SR',0,4
	.word	16156
	.byte	1,19,2,35,0,8
	.byte	'OC1_1_SR',0,4
	.word	16156
	.byte	1,18,2,35,0,8
	.byte	'SL1_1_SR',0,4
	.word	16156
	.byte	1,17,2,35,0,8
	.byte	'DT1_1_SR',0,4
	.word	16156
	.byte	1,16,2,35,0,8
	.byte	'POL0_2_SR',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'OC0_2_SR',0,4
	.word	16156
	.byte	1,14,2,35,0,8
	.byte	'SL0_2_SR',0,4
	.word	16156
	.byte	1,13,2,35,0,8
	.byte	'DT0_2_SR',0,4
	.word	16156
	.byte	1,12,2,35,0,8
	.byte	'POL1_2_SR',0,4
	.word	16156
	.byte	1,11,2,35,0,8
	.byte	'OC1_2_SR',0,4
	.word	16156
	.byte	1,10,2,35,0,8
	.byte	'SL1_2_SR',0,4
	.word	16156
	.byte	1,9,2,35,0,8
	.byte	'DT1_2_SR',0,4
	.word	16156
	.byte	1,8,2,35,0,8
	.byte	'POL0_3_SR',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'OC0_3_SR',0,4
	.word	16156
	.byte	1,6,2,35,0,8
	.byte	'SL0_3_SR',0,4
	.word	16156
	.byte	1,5,2,35,0,8
	.byte	'DT0_3_SR',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'POL1_3_SR',0,4
	.word	16156
	.byte	1,3,2,35,0,8
	.byte	'OC1_3_SR',0,4
	.word	16156
	.byte	1,2,2,35,0,8
	.byte	'SL1_3_SR',0,4
	.word	16156
	.byte	1,1,2,35,0,8
	.byte	'DT1_3_SR',0,4
	.word	16156
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,5,217,2,3
	.word	27241
	.byte	7
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,5,220,2,16,4,8
	.byte	'CLK_SEL',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'UPD_MODE',0,4
	.word	16156
	.byte	3,25,2,35,0,8
	.byte	'reserved_7',0,4
	.word	16156
	.byte	25,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,5,226,2,3
	.word	27964
	.byte	7
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,5,229,2,16,4,8
	.byte	'RELRISE',0,4
	.word	16156
	.byte	10,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	16156
	.byte	6,16,2,35,0,8
	.byte	'RELFALL',0,4
	.word	16156
	.byte	10,6,2,35,0,8
	.byte	'reserved_26',0,4
	.word	16156
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,5,235,2,3
	.word	28108
	.byte	7
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,5,238,2,16,4,8
	.byte	'RELBLK',0,4
	.word	16156
	.byte	10,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	16156
	.byte	6,16,2,35,0,8
	.byte	'PSU_IN_SEL',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'IN_POL',0,4
	.word	16156
	.byte	1,14,2,35,0,8
	.byte	'reserved_18',0,4
	.word	16156
	.byte	2,12,2,35,0,8
	.byte	'SHIFT_SEL',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'reserved_22',0,4
	.word	16156
	.byte	10,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,5,247,2,3
	.word	28257
	.byte	7
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,5,250,2,16,4,8
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,5,129,3,3
	.word	28472
	.byte	7
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,5,132,3,16,4,8
	.byte	'GRSTEN',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'BRIDGE_MODE_RST',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'AEI_IN',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	16156
	.byte	5,24,2,35,0,8
	.byte	'TOM_OUT_RST',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	16156
	.byte	3,20,2,35,0,8
	.byte	'reserved_12',0,4
	.word	16156
	.byte	4,16,2,35,0,8
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'IRQ_MODE_PULSE',0,4
	.word	16156
	.byte	1,14,2,35,0,8
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	16156
	.byte	1,13,2,35,0,8
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	16156
	.byte	1,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	16156
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_GTM_HW_CONF_Bits',0,5,146,3,3
	.word	28676
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,5,149,3,16,4,8
	.byte	'reserved_0',0,4
	.word	16156
	.byte	4,28,2,35,0,8
	.byte	'AEI_IRQ',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	16156
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,5,154,3,3
	.word	29033
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,5,157,3,16,4,8
	.byte	'TIM0_CH0_IRQ',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'TIM0_CH1_IRQ',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'TIM0_CH2_IRQ',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'TIM0_CH3_IRQ',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'TIM0_CH4_IRQ',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'TIM0_CH5_IRQ',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'TIM0_CH6_IRQ',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'TIM0_CH7_IRQ',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	16156
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,5,168,3,3
	.word	29161
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,5,171,3,16,4,8
	.byte	'TOM0_CH0_IRQ',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'TOM0_CH1_IRQ',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'TOM0_CH2_IRQ',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'TOM0_CH3_IRQ',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'TOM0_CH4_IRQ',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'TOM0_CH5_IRQ',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'TOM0_CH6_IRQ',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'TOM0_CH7_IRQ',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'TOM0_CH8_IRQ',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'TOM0_CH9_IRQ',0,4
	.word	16156
	.byte	1,22,2,35,0,8
	.byte	'TOM0_CH10_IRQ',0,4
	.word	16156
	.byte	1,21,2,35,0,8
	.byte	'TOM0_CH11_IRQ',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'TOM0_CH12_IRQ',0,4
	.word	16156
	.byte	1,19,2,35,0,8
	.byte	'TOM0_CH13_IRQ',0,4
	.word	16156
	.byte	1,18,2,35,0,8
	.byte	'TOM0_CH14_IRQ',0,4
	.word	16156
	.byte	1,17,2,35,0,8
	.byte	'TOM0_CH15_IRQ',0,4
	.word	16156
	.byte	1,16,2,35,0,8
	.byte	'TOM1_CH0_IRQ',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'TOM1_CH1_IRQ',0,4
	.word	16156
	.byte	1,14,2,35,0,8
	.byte	'TOM1_CH2_IRQ',0,4
	.word	16156
	.byte	1,13,2,35,0,8
	.byte	'TOM1_CH3_IRQ',0,4
	.word	16156
	.byte	1,12,2,35,0,8
	.byte	'TOM1_CH4_IRQ',0,4
	.word	16156
	.byte	1,11,2,35,0,8
	.byte	'TOM1_CH5_IRQ',0,4
	.word	16156
	.byte	1,10,2,35,0,8
	.byte	'TOM1_CH6_IRQ',0,4
	.word	16156
	.byte	1,9,2,35,0,8
	.byte	'TOM1_CH7_IRQ',0,4
	.word	16156
	.byte	1,8,2,35,0,8
	.byte	'TOM1_CH8_IRQ',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'TOM1_CH9_IRQ',0,4
	.word	16156
	.byte	1,6,2,35,0,8
	.byte	'TOM1_CH10_IRQ',0,4
	.word	16156
	.byte	1,5,2,35,0,8
	.byte	'TOM1_CH11_IRQ',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'TOM1_CH12_IRQ',0,4
	.word	16156
	.byte	1,3,2,35,0,8
	.byte	'TOM1_CH13_IRQ',0,4
	.word	16156
	.byte	1,2,2,35,0,8
	.byte	'TOM1_CH14_IRQ',0,4
	.word	16156
	.byte	1,1,2,35,0,8
	.byte	'TOM1_CH15_IRQ',0,4
	.word	16156
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,5,205,3,3
	.word	29440
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,5,208,3,16,4,8
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	16156
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,5,219,3,3
	.word	30285
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,5,222,3,16,4,8
	.byte	'GTM_EIRQ',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	16156
	.byte	3,28,2,35,0,8
	.byte	'TIM0_EIRQ',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	16156
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,5,228,3,3
	.word	30578
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,5,231,3,16,4,8
	.byte	'SEL0',0,4
	.word	16156
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	16156
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	16156
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	16156
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,5,238,3,3
	.word	30732
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,5,241,3,16,4,8
	.byte	'SEL0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'SEL1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'SEL2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'SEL3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'SEL4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'SEL5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'SEL6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'SEL7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'SEL8',0,4
	.word	16156
	.byte	2,14,2,35,0,8
	.byte	'SEL9',0,4
	.word	16156
	.byte	2,12,2,35,0,8
	.byte	'SEL10',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'SEL11',0,4
	.word	16156
	.byte	2,8,2,35,0,8
	.byte	'SEL12',0,4
	.word	16156
	.byte	2,6,2,35,0,8
	.byte	'SEL13',0,4
	.word	16156
	.byte	2,4,2,35,0,8
	.byte	'SEL14',0,4
	.word	16156
	.byte	2,2,2,35,0,8
	.byte	'SEL15',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,5,131,4,3
	.word	30902
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,5,134,4,16,4,8
	.byte	'CH0SEL',0,4
	.word	16156
	.byte	4,28,2,35,0,8
	.byte	'CH1SEL',0,4
	.word	16156
	.byte	4,24,2,35,0,8
	.byte	'CH2SEL',0,4
	.word	16156
	.byte	4,20,2,35,0,8
	.byte	'CH3SEL',0,4
	.word	16156
	.byte	4,16,2,35,0,8
	.byte	'CH4SEL',0,4
	.word	16156
	.byte	4,12,2,35,0,8
	.byte	'CH5SEL',0,4
	.word	16156
	.byte	4,8,2,35,0,8
	.byte	'CH6SEL',0,4
	.word	16156
	.byte	4,4,2,35,0,8
	.byte	'CH7SEL',0,4
	.word	16156
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,5,144,4,3
	.word	31243
	.byte	7
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,5,147,4,16,4,8
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,5,154,4,3
	.word	31468
	.byte	7
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,5,157,4,16,4,8
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'TRG_AEI_USP_BE',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,5,164,4,3
	.word	31666
	.byte	7
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,5,167,4,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,5,171,4,3
	.word	31862
	.byte	7
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,5,174,4,16,4,8
	.byte	'AEI_TO_XPT',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,5,181,4,3
	.word	31965
	.byte	7
	.byte	'_Ifx_GTM_KRST0_Bits',0,5,184,4,16,4,8
	.byte	'RST',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'RSTSTAT',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRST0_Bits',0,5,189,4,3
	.word	32143
	.byte	7
	.byte	'_Ifx_GTM_KRST1_Bits',0,5,192,4,16,4,8
	.byte	'RST',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	16156
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRST1_Bits',0,5,196,4,3
	.word	32254
	.byte	7
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,5,199,4,16,4,8
	.byte	'CLR',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	16156
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,5,203,4,3
	.word	32346
	.byte	7
	.byte	'_Ifx_GTM_OCS_Bits',0,5,206,4,16,4,8
	.byte	'reserved_0',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'SUS',0,4
	.word	16156
	.byte	4,4,2,35,0,8
	.byte	'SUS_P',0,4
	.word	16156
	.byte	1,3,2,35,0,8
	.byte	'SUSSTA',0,4
	.word	16156
	.byte	1,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_OCS_Bits',0,5,213,4,3
	.word	32442
	.byte	7
	.byte	'_Ifx_GTM_ODA_Bits',0,5,216,4,16,4,8
	.byte	'DDREN',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'DREN',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_ODA_Bits',0,5,221,4,3
	.word	32588
	.byte	7
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,5,224,4,16,4,8
	.byte	'CV',0,4
	.word	16156
	.byte	27,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'CM',0,4
	.word	16156
	.byte	2,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU0T_Bits',0,5,230,4,3
	.word	32694
	.byte	7
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,5,233,4,16,4,8
	.byte	'CV',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	4,4,2,35,0,8
	.byte	'EN',0,4
	.word	16156
	.byte	1,3,2,35,0,8
	.byte	'reserved_29',0,4
	.word	16156
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU1T_Bits',0,5,239,4,3
	.word	32825
	.byte	7
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,5,242,4,16,4,8
	.byte	'CV',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	4,4,2,35,0,8
	.byte	'EN',0,4
	.word	16156
	.byte	1,3,2,35,0,8
	.byte	'reserved_29',0,4
	.word	16156
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU2T_Bits',0,5,248,4,3
	.word	32956
	.byte	7
	.byte	'_Ifx_GTM_OTSC0_Bits',0,5,251,4,16,4,8
	.byte	'B0LMT',0,4
	.word	16156
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'B0LMI',0,4
	.word	16156
	.byte	4,24,2,35,0,8
	.byte	'B0HMT',0,4
	.word	16156
	.byte	3,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'B0HMI',0,4
	.word	16156
	.byte	4,16,2,35,0,8
	.byte	'B1LMT',0,4
	.word	16156
	.byte	3,13,2,35,0,8
	.byte	'reserved_19',0,4
	.word	16156
	.byte	1,12,2,35,0,8
	.byte	'B1LMI',0,4
	.word	16156
	.byte	4,8,2,35,0,8
	.byte	'B1HMT',0,4
	.word	16156
	.byte	3,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'B1HMI',0,4
	.word	16156
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTSC0_Bits',0,5,137,5,3
	.word	33087
	.byte	7
	.byte	'_Ifx_GTM_OTSS_Bits',0,5,140,5,16,4,8
	.byte	'OTGB0',0,4
	.word	16156
	.byte	4,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	4,24,2,35,0,8
	.byte	'OTGB1',0,4
	.word	16156
	.byte	4,20,2,35,0,8
	.byte	'reserved_12',0,4
	.word	16156
	.byte	4,16,2,35,0,8
	.byte	'OTGB2',0,4
	.word	16156
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	16156
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTSS_Bits',0,5,148,5,3
	.word	33369
	.byte	7
	.byte	'_Ifx_GTM_REV_Bits',0,5,151,5,16,4,8
	.byte	'STEP',0,4
	.word	16156
	.byte	8,24,2,35,0,8
	.byte	'NO',0,4
	.word	16156
	.byte	4,20,2,35,0,8
	.byte	'MINOR',0,4
	.word	16156
	.byte	4,16,2,35,0,8
	.byte	'MAJOR',0,4
	.word	16156
	.byte	4,12,2,35,0,8
	.byte	'DEV_CODE0',0,4
	.word	16156
	.byte	4,8,2,35,0,8
	.byte	'DEV_CODE1',0,4
	.word	16156
	.byte	4,4,2,35,0,8
	.byte	'DEV_CODE2',0,4
	.word	16156
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_REV_Bits',0,5,160,5,3
	.word	33541
	.byte	7
	.byte	'_Ifx_GTM_RST_Bits',0,5,163,5,16,4,8
	.byte	'RST',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	16156
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_RST_Bits',0,5,167,5,3
	.word	33719
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,5,170,5,16,4,8
	.byte	'BASE',0,4
	.word	16156
	.byte	27,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	16156
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,5,174,5,3
	.word	33807
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,5,177,5,16,4,8
	.byte	'LOW_RES',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	16156
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,5,182,5,3
	.word	33915
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,5,185,5,16,4,8
	.byte	'BASE',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,5,189,5,3
	.word	34047
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,5,192,5,16,4,8
	.byte	'CH_MODE',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	16156
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,5,197,5,3
	.word	34155
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,5,200,5,16,4,8
	.byte	'BASE',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,5,204,5,3
	.word	34287
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,5,207,5,16,4,8
	.byte	'CH_MODE',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	16156
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	16156
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,5,212,5,3
	.word	34395
	.byte	7
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,5,215,5,16,4,8
	.byte	'ENDIS_CH0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CH1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CH2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	16156
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,5,221,5,3
	.word	34527
	.byte	7
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,5,224,5,16,4,8
	.byte	'SRC_CH0',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'SRC_CH1',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'SRC_CH2',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'SRC_CH3',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'SRC_CH4',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'SRC_CH5',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'SRC_CH6',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'SRC_CH7',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	16156
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,5,235,5,3
	.word	34673
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,5,238,5,16,4,8
	.byte	'CNT',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,5,242,5,3
	.word	34920
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,5,245,5,16,4,8
	.byte	'CNTS',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,5,249,5,3
	.word	35023
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,5,252,5,16,4,8
	.byte	'TIM_EN',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'TIM_MODE',0,4
	.word	16156
	.byte	3,28,2,35,0,8
	.byte	'OSM',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'CICTRL',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'TBU0x_SEL',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'GPR0_SEL',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'GPR1_SEL',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'CNTS_SEL',0,4
	.word	16156
	.byte	1,19,2,35,0,8
	.byte	'DSL',0,4
	.word	16156
	.byte	1,18,2,35,0,8
	.byte	'ISL',0,4
	.word	16156
	.byte	1,17,2,35,0,8
	.byte	'ECNT_RESET',0,4
	.word	16156
	.byte	1,16,2,35,0,8
	.byte	'FLT_EN',0,4
	.word	16156
	.byte	1,15,2,35,0,8
	.byte	'FLT_CNT_FRQ',0,4
	.word	16156
	.byte	2,13,2,35,0,8
	.byte	'EXT_CAP_EN',0,4
	.word	16156
	.byte	1,12,2,35,0,8
	.byte	'FLT_MODE_RE',0,4
	.word	16156
	.byte	1,11,2,35,0,8
	.byte	'FLT_CTR_RE',0,4
	.word	16156
	.byte	1,10,2,35,0,8
	.byte	'FLT_MODE_FE',0,4
	.word	16156
	.byte	1,9,2,35,0,8
	.byte	'FLT_CTR_FE',0,4
	.word	16156
	.byte	1,8,2,35,0,8
	.byte	'CLK_SEL',0,4
	.word	16156
	.byte	3,5,2,35,0,8
	.byte	'FR_ECNT_OFL',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'EGPR0_SEL',0,4
	.word	16156
	.byte	1,3,2,35,0,8
	.byte	'EGPR1_SEL',0,4
	.word	16156
	.byte	1,2,2,35,0,8
	.byte	'TOCTRL',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,5,150,6,3
	.word	35122
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,5,153,6,16,4,8
	.byte	'ECNT',0,4
	.word	16156
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,5,157,6,3
	.word	35670
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,5,160,6,16,4,8
	.byte	'EXT_CAP_SRC',0,4
	.word	16156
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	16156
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,5,164,6,3
	.word	35776
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,5,167,6,16,4,8
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'TODET_EIRQ_EN',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	16156
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,5,176,6,3
	.word	35890
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,5,179,6,16,4,8
	.byte	'FLT_FE',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,5,183,6,3
	.word	36145
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,5,186,6,16,4,8
	.byte	'FLT_RE',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,5,190,6,3
	.word	36257
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,5,193,6,16,4,8
	.byte	'GPR0',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,5,197,6,3
	.word	36369
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,5,200,6,16,4,8
	.byte	'GPR1',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,5,204,6,3
	.word	36468
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,5,207,6,16,4,8
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'TODET_IRQ_EN',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	16156
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,5,216,6,3
	.word	36567
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,5,219,6,16,4,8
	.byte	'TRG_NEWVAL',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'TRG_ECNTOFL',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'TRG_CNTOFL',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'TRG_GPRzOFL',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'TRG_TODET',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'TRG_GLITCHDET',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	16156
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,5,228,6,3
	.word	36814
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,5,231,6,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,5,235,6,3
	.word	37053
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,5,238,6,16,4,8
	.byte	'NEWVAL',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'TODET',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	16156
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,5,247,6,3
	.word	37170
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,5,250,6,16,4,8
	.byte	'TO_CNT',0,4
	.word	16156
	.byte	8,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	16156
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,5,254,6,3
	.word	37383
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,5,129,7,16,4,8
	.byte	'TOV',0,4
	.word	16156
	.byte	8,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	16156
	.byte	20,4,2,35,0,8
	.byte	'TCS',0,4
	.word	16156
	.byte	3,1,2,35,0,8
	.byte	'reserved_31',0,4
	.word	16156
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,5,135,7,3
	.word	37490
	.byte	7
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,5,138,7,16,4,8
	.byte	'VAL_0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'MODE_0',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'VAL_1',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'MODE_1',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'VAL_2',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'MODE_2',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'VAL_3',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'MODE_3',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'VAL_4',0,4
	.word	16156
	.byte	2,14,2,35,0,8
	.byte	'MODE_4',0,4
	.word	16156
	.byte	2,12,2,35,0,8
	.byte	'VAL_5',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'MODE_5',0,4
	.word	16156
	.byte	2,8,2,35,0,8
	.byte	'VAL_6',0,4
	.word	16156
	.byte	2,6,2,35,0,8
	.byte	'MODE_6',0,4
	.word	16156
	.byte	2,4,2,35,0,8
	.byte	'VAL_7',0,4
	.word	16156
	.byte	2,2,2,35,0,8
	.byte	'MODE_7',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,5,156,7,3
	.word	37632
	.byte	7
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,5,159,7,16,4,8
	.byte	'F_OUT',0,4
	.word	16156
	.byte	8,24,2,35,0,8
	.byte	'F_IN',0,4
	.word	16156
	.byte	8,16,2,35,0,8
	.byte	'TIM_IN',0,4
	.word	16156
	.byte	8,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	16156
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,5,165,7,3
	.word	37977
	.byte	7
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,5,168,7,16,4,8
	.byte	'RST_CH0',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	16156
	.byte	1,29,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	16156
	.byte	1,28,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	16156
	.byte	1,27,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	16156
	.byte	1,26,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	16156
	.byte	1,25,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	16156
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	16156
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_RST_Bits',0,5,179,7,3
	.word	38118
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,5,182,7,16,4,8
	.byte	'CM0',0,4
	.word	16156
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,5,186,7,3
	.word	38351
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,5,189,7,16,4,8
	.byte	'CM1',0,4
	.word	16156
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,5,193,7,3
	.word	38454
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,5,196,7,16,4,8
	.byte	'CN0',0,4
	.word	16156
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,5,200,7,3
	.word	38557
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,5,203,7,16,4,8
	.byte	'reserved_0',0,4
	.word	16156
	.byte	11,21,2,35,0,8
	.byte	'SL',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'CLK_SRC_SR',0,4
	.word	16156
	.byte	3,17,2,35,0,8
	.byte	'reserved_15',0,4
	.word	16156
	.byte	5,12,2,35,0,8
	.byte	'RST_CCU0',0,4
	.word	16156
	.byte	1,11,2,35,0,8
	.byte	'OSM_TRIG',0,4
	.word	16156
	.byte	1,10,2,35,0,8
	.byte	'EXT_TRIG',0,4
	.word	16156
	.byte	1,9,2,35,0,8
	.byte	'EXTTRIGOUT',0,4
	.word	16156
	.byte	1,8,2,35,0,8
	.byte	'TRIGOUT',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	16156
	.byte	1,6,2,35,0,8
	.byte	'OSM',0,4
	.word	16156
	.byte	1,5,2,35,0,8
	.byte	'BITREV',0,4
	.word	16156
	.byte	1,4,2,35,0,8
	.byte	'reserved_28',0,4
	.word	16156
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,5,218,7,3
	.word	38660
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,5,221,7,16,4,8
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,5,226,7,3
	.word	38988
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,5,229,7,16,4,8
	.byte	'TRG_CCU0TC0',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'TRG_CCU1TC0',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,5,234,7,3
	.word	39131
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,5,237,7,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,5,241,7,3
	.word	39280
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,5,244,7,16,4,8
	.byte	'CCU0TC',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'CCU1TC',0,4
	.word	16156
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	16156
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,5,249,7,3
	.word	39397
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,5,252,7,16,4,8
	.byte	'SR0',0,4
	.word	16156
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,5,128,8,3
	.word	39534
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,5,131,8,16,4,8
	.byte	'SR1',0,4
	.word	16156
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,5,135,8,3
	.word	39637
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,5,138,8,16,4,8
	.byte	'OL',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	16156
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,5,142,8,3
	.word	39740
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,5,145,8,16,4,8
	.byte	'ACT_TB',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'TB_TRIG',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'TBU_SEL',0,4
	.word	16156
	.byte	2,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	16156
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,5,151,8,3
	.word	39843
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,5,154,8,16,4,8
	.byte	'ENDIS_CTRL0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CTRL1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CTRL2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_CTRL3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_CTRL4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_CTRL5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_CTRL6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_CTRL7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,5,165,8,3
	.word	39997
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,5,168,8,16,4,8
	.byte	'ENDIS_STAT0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_STAT1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_STAT2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_STAT3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_STAT4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_STAT5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_STAT6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_STAT7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,5,179,8,3
	.word	40287
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,5,182,8,16,4,8
	.byte	'FUPD_CTRL0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'FUPD_CTRL1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'FUPD_CTRL2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'FUPD_CTRL3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'FUPD_CTRL4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'FUPD_CTRL5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'FUPD_CTRL6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'FUPD_CTRL7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'RSTCN0_CH0',0,4
	.word	16156
	.byte	2,14,2,35,0,8
	.byte	'RSTCN0_CH1',0,4
	.word	16156
	.byte	2,12,2,35,0,8
	.byte	'RSTCN0_CH2',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'RSTCN0_CH3',0,4
	.word	16156
	.byte	2,8,2,35,0,8
	.byte	'RSTCN0_CH4',0,4
	.word	16156
	.byte	2,6,2,35,0,8
	.byte	'RSTCN0_CH5',0,4
	.word	16156
	.byte	2,4,2,35,0,8
	.byte	'RSTCN0_CH6',0,4
	.word	16156
	.byte	2,2,2,35,0,8
	.byte	'RSTCN0_CH7',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,5,200,8,3
	.word	40577
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,5,203,8,16,4,8
	.byte	'HOST_TRIG',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	16156
	.byte	7,24,2,35,0,8
	.byte	'RST_CH0',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	16156
	.byte	1,22,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	16156
	.byte	1,21,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	16156
	.byte	1,19,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	16156
	.byte	1,18,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	16156
	.byte	1,17,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	16156
	.byte	1,16,2,35,0,8
	.byte	'UPEN_CTRL0',0,4
	.word	16156
	.byte	2,14,2,35,0,8
	.byte	'UPEN_CTRL1',0,4
	.word	16156
	.byte	2,12,2,35,0,8
	.byte	'UPEN_CTRL2',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'UPEN_CTRL3',0,4
	.word	16156
	.byte	2,8,2,35,0,8
	.byte	'UPEN_CTRL4',0,4
	.word	16156
	.byte	2,6,2,35,0,8
	.byte	'UPEN_CTRL5',0,4
	.word	16156
	.byte	2,4,2,35,0,8
	.byte	'UPEN_CTRL6',0,4
	.word	16156
	.byte	2,2,2,35,0,8
	.byte	'UPEN_CTRL7',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,5,223,8,3
	.word	41010
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,5,226,8,16,4,8
	.byte	'INT_TRIG0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'INT_TRIG1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'INT_TRIG2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'INT_TRIG3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'INT_TRIG4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'INT_TRIG5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'INT_TRIG6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'INT_TRIG7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,5,237,8,3
	.word	41460
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,5,240,8,16,4,8
	.byte	'OUTEN_CTRL0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_CTRL1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_CTRL2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_CTRL3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_CTRL4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_CTRL5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_CTRL6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_CTRL7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,5,251,8,3
	.word	41730
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,5,254,8,16,4,8
	.byte	'OUTEN_STAT0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_STAT1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_STAT2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_STAT3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_STAT4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_STAT5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_STAT6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_STAT7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,5,137,9,3
	.word	42020
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,5,140,9,16,4,8
	.byte	'ACT_TB',0,4
	.word	16156
	.byte	24,8,2,35,0,8
	.byte	'TB_TRIG',0,4
	.word	16156
	.byte	1,7,2,35,0,8
	.byte	'TBU_SEL',0,4
	.word	16156
	.byte	2,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	16156
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,5,146,9,3
	.word	42310
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,5,149,9,16,4,8
	.byte	'ENDIS_CTRL0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CTRL1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CTRL2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_CTRL3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_CTRL4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_CTRL5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_CTRL6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_CTRL7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,5,160,9,3
	.word	42464
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,5,163,9,16,4,8
	.byte	'ENDIS_STAT0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_STAT1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_STAT2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_STAT3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_STAT4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_STAT5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_STAT6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_STAT7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,5,174,9,3
	.word	42754
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,5,177,9,16,4,8
	.byte	'FUPD_CTRL0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'FUPD_CTRL1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'FUPD_CTRL2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'FUPD_CTRL3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'FUPD_CTRL4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'FUPD_CTRL5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'FUPD_CTRL6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'FUPD_CTRL7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'RSTCN0_CH0',0,4
	.word	16156
	.byte	2,14,2,35,0,8
	.byte	'RSTCN0_CH1',0,4
	.word	16156
	.byte	2,12,2,35,0,8
	.byte	'RSTCN0_CH2',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'RSTCN0_CH3',0,4
	.word	16156
	.byte	2,8,2,35,0,8
	.byte	'RSTCN0_CH4',0,4
	.word	16156
	.byte	2,6,2,35,0,8
	.byte	'RSTCN0_CH5',0,4
	.word	16156
	.byte	2,4,2,35,0,8
	.byte	'RSTCN0_CH6',0,4
	.word	16156
	.byte	2,2,2,35,0,8
	.byte	'RSTCN0_CH7',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,5,195,9,3
	.word	43044
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,5,198,9,16,4,8
	.byte	'HOST_TRIG',0,4
	.word	16156
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	16156
	.byte	7,24,2,35,0,8
	.byte	'RST_CH0',0,4
	.word	16156
	.byte	1,23,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	16156
	.byte	1,22,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	16156
	.byte	1,21,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	16156
	.byte	1,20,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	16156
	.byte	1,19,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	16156
	.byte	1,18,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	16156
	.byte	1,17,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	16156
	.byte	1,16,2,35,0,8
	.byte	'UPEN_CTRL0',0,4
	.word	16156
	.byte	2,14,2,35,0,8
	.byte	'UPEN_CTRL1',0,4
	.word	16156
	.byte	2,12,2,35,0,8
	.byte	'UPEN_CTRL2',0,4
	.word	16156
	.byte	2,10,2,35,0,8
	.byte	'UPEN_CTRL3',0,4
	.word	16156
	.byte	2,8,2,35,0,8
	.byte	'UPEN_CTRL4',0,4
	.word	16156
	.byte	2,6,2,35,0,8
	.byte	'UPEN_CTRL5',0,4
	.word	16156
	.byte	2,4,2,35,0,8
	.byte	'UPEN_CTRL6',0,4
	.word	16156
	.byte	2,2,2,35,0,8
	.byte	'UPEN_CTRL7',0,4
	.word	16156
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,5,218,9,3
	.word	43477
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,5,221,9,16,4,8
	.byte	'INT_TRIG0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'INT_TRIG1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'INT_TRIG2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'INT_TRIG3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'INT_TRIG4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'INT_TRIG5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'INT_TRIG6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'INT_TRIG7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,5,232,9,3
	.word	43927
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,5,235,9,16,4,8
	.byte	'OUTEN_CTRL0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_CTRL1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_CTRL2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_CTRL3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_CTRL4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_CTRL5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_CTRL6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_CTRL7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,5,246,9,3
	.word	44197
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,5,249,9,16,4,8
	.byte	'OUTEN_STAT0',0,4
	.word	16156
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_STAT1',0,4
	.word	16156
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_STAT2',0,4
	.word	16156
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_STAT3',0,4
	.word	16156
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_STAT4',0,4
	.word	16156
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_STAT5',0,4
	.word	16156
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_STAT6',0,4
	.word	16156
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_STAT7',0,4
	.word	16156
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	16156
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,5,132,10,3
	.word	44487
	.byte	9,5,140,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22849
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN0',0,5,145,10,3
	.word	44777
	.byte	9,5,148,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23406
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN1',0,5,153,10,3
	.word	44841
	.byte	9,5,156,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23483
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,5,161,10,3
	.word	44905
	.byte	9,5,164,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23637
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,5,169,10,3
	.word	44975
	.byte	9,5,172,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23791
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,5,177,10,3
	.word	45045
	.byte	9,5,180,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23919
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_MODE',0,5,185,10,3
	.word	45115
	.byte	9,5,188,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24226
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,5,193,10,3
	.word	45184
	.byte	9,5,196,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24428
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,5,201,10,3
	.word	45253
	.byte	9,5,204,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24541
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CLC',0,5,209,10,3
	.word	45322
	.byte	9,5,212,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24684
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,5,217,10,3
	.word	45383
	.byte	9,5,220,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24801
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,5,225,10,3
	.word	45456
	.byte	9,5,228,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24936
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,5,233,10,3
	.word	45528
	.byte	9,5,236,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25071
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_EN',0,5,241,10,3
	.word	45600
	.byte	9,5,244,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25391
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,5,249,10,3
	.word	45668
	.byte	9,5,252,10,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25503
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,5,129,11,3
	.word	45738
	.byte	9,5,132,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25615
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,5,137,11,3
	.word	45808
	.byte	9,5,140,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25731
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,5,145,11,3
	.word	45880
	.byte	9,5,148,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25843
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,5,153,11,3
	.word	45950
	.byte	9,5,156,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25955
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CTRL',0,5,161,11,3
	.word	46020
	.byte	9,5,164,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26108
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,5,169,11,3
	.word	46082
	.byte	9,5,172,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26620
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,5,177,11,3
	.word	46152
	.byte	9,5,180,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27241
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,5,185,11,3
	.word	46222
	.byte	9,5,188,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27964
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CTRL',0,5,193,11,3
	.word	46295
	.byte	9,5,196,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28108
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_DTV_CH',0,5,201,11,3
	.word	46361
	.byte	9,5,204,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28257
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,5,209,11,3
	.word	46429
	.byte	9,5,212,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28472
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_EIRQ_EN',0,5,217,11,3
	.word	46498
	.byte	9,5,220,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28676
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_HW_CONF',0,5,225,11,3
	.word	46563
	.byte	9,5,228,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29033
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_0',0,5,233,11,3
	.word	46628
	.byte	9,5,236,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29161
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_2',0,5,241,11,3
	.word	46696
	.byte	9,5,244,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29440
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_6',0,5,249,11,3
	.word	46764
	.byte	9,5,252,11,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	30285
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,5,129,12,3
	.word	46832
	.byte	9,5,132,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	30578
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,5,137,12,3
	.word	46903
	.byte	9,5,140,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	30732
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,5,145,12,3
	.word	46973
	.byte	9,5,148,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	30902
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,5,153,12,3
	.word	47050
	.byte	9,5,156,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	31243
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,5,161,12,3
	.word	47125
	.byte	9,5,164,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	31468
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_EN',0,5,169,12,3
	.word	47201
	.byte	9,5,172,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	31666
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_FORCINT',0,5,177,12,3
	.word	47265
	.byte	9,5,180,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	31862
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_MODE',0,5,185,12,3
	.word	47334
	.byte	9,5,188,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	31965
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,5,193,12,3
	.word	47400
	.byte	9,5,196,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	32143
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRST0',0,5,201,12,3
	.word	47468
	.byte	9,5,204,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	32254
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRST1',0,5,209,12,3
	.word	47531
	.byte	9,5,212,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	32346
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRSTCLR',0,5,217,12,3
	.word	47594
	.byte	9,5,220,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	32442
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OCS',0,5,225,12,3
	.word	47659
	.byte	9,5,228,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	32588
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ODA',0,5,233,12,3
	.word	47720
	.byte	9,5,236,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	32694
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU0T',0,5,241,12,3
	.word	47781
	.byte	9,5,244,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	32825
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU1T',0,5,249,12,3
	.word	47845
	.byte	9,5,252,12,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	32956
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU2T',0,5,129,13,3
	.word	47909
	.byte	9,5,132,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	33087
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTSC0',0,5,137,13,3
	.word	47973
	.byte	9,5,140,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	33369
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTSS',0,5,145,13,3
	.word	48036
	.byte	9,5,148,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	33541
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_REV',0,5,153,13,3
	.word	48098
	.byte	9,5,156,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	33719
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_RST',0,5,161,13,3
	.word	48159
	.byte	9,5,164,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	33807
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,5,169,13,3
	.word	48220
	.byte	9,5,172,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	33915
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,5,177,13,3
	.word	48290
	.byte	9,5,180,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	34047
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,5,185,13,3
	.word	48360
	.byte	9,5,188,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	34155
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,5,193,13,3
	.word	48430
	.byte	9,5,196,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	34287
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,5,201,13,3
	.word	48500
	.byte	9,5,204,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	34395
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,5,209,13,3
	.word	48570
	.byte	9,5,212,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	34527
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CHEN',0,5,217,13,3
	.word	48640
	.byte	9,5,220,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	34673
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,5,225,13,3
	.word	48706
	.byte	9,5,228,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	34920
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNT',0,5,233,13,3
	.word	48778
	.byte	9,5,236,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35023
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,5,241,13,3
	.word	48846
	.byte	9,5,244,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35122
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,5,249,13,3
	.word	48915
	.byte	9,5,252,13,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35670
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,5,129,14,3
	.word	48984
	.byte	9,5,132,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35776
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,5,137,14,3
	.word	49053
	.byte	9,5,140,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35890
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,5,145,14,3
	.word	49123
	.byte	9,5,148,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36145
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,5,153,14,3
	.word	49195
	.byte	9,5,156,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36257
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,5,161,14,3
	.word	49266
	.byte	9,5,164,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36369
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,5,169,14,3
	.word	49337
	.byte	9,5,172,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36468
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,5,177,14,3
	.word	49406
	.byte	9,5,180,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36567
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,5,185,14,3
	.word	49475
	.byte	9,5,188,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36814
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,5,193,14,3
	.word	49546
	.byte	9,5,196,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37053
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,5,201,14,3
	.word	49622
	.byte	9,5,204,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37170
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,5,209,14,3
	.word	49695
	.byte	9,5,212,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37383
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,5,217,14,3
	.word	49770
	.byte	9,5,220,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37490
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,5,225,14,3
	.word	49839
	.byte	9,5,228,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37632
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_IN_SRC',0,5,233,14,3
	.word	49908
	.byte	9,5,236,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37977
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_INP_VAL',0,5,241,14,3
	.word	49976
	.byte	9,5,244,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38118
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_RST',0,5,249,14,3
	.word	50045
	.byte	9,5,252,14,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38351
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM0',0,5,129,15,3
	.word	50110
	.byte	9,5,132,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38454
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM1',0,5,137,15,3
	.word	50178
	.byte	9,5,140,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38557
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CN0',0,5,145,15,3
	.word	50246
	.byte	9,5,148,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38660
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,5,153,15,3
	.word	50314
	.byte	9,5,156,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38988
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,5,161,15,3
	.word	50383
	.byte	9,5,164,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39131
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,5,169,15,3
	.word	50454
	.byte	9,5,172,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39280
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,5,177,15,3
	.word	50530
	.byte	9,5,180,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39397
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,5,185,15,3
	.word	50603
	.byte	9,5,188,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39534
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR0',0,5,193,15,3
	.word	50678
	.byte	9,5,196,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39637
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR1',0,5,201,15,3
	.word	50746
	.byte	9,5,204,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39740
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_STAT',0,5,209,15,3
	.word	50814
	.byte	9,5,212,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39843
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,5,217,15,3
	.word	50883
	.byte	9,5,220,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39997
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,5,225,15,3
	.word	50956
	.byte	9,5,228,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40287
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,5,233,15,3
	.word	51033
	.byte	9,5,236,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40577
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,5,241,15,3
	.word	51110
	.byte	9,5,244,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41010
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,5,249,15,3
	.word	51186
	.byte	9,5,252,15,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41460
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,5,129,16,3
	.word	51261
	.byte	9,5,132,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41730
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,5,137,16,3
	.word	51336
	.byte	9,5,140,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42020
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,5,145,16,3
	.word	51413
	.byte	9,5,148,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42310
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,5,153,16,3
	.word	51490
	.byte	9,5,156,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42464
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,5,161,16,3
	.word	51563
	.byte	9,5,164,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42754
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,5,169,16,3
	.word	51640
	.byte	9,5,172,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43044
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,5,177,16,3
	.word	51717
	.byte	9,5,180,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43477
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,5,185,16,3
	.word	51793
	.byte	9,5,188,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43927
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,5,193,16,3
	.word	51868
	.byte	9,5,196,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	44197
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,5,201,16,3
	.word	51943
	.byte	9,5,204,16,9,4,10
	.byte	'U',0,4
	.word	351
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	17345
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	44487
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,5,209,16,3
	.word	52020
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,5,220,16,25,4,10
	.byte	'CTRL',0,4
	.word	45383
	.byte	2,35,0,0,11
	.word	52097
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK0_5',0,5,223,16,3
	.word	52138
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_6',0,5,226,16,25,4,10
	.byte	'CTRL',0,4
	.word	45456
	.byte	2,35,0,0,11
	.word	52171
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK_6',0,5,229,16,3
	.word	52211
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_7',0,5,232,16,25,4,10
	.byte	'CTRL',0,4
	.word	45528
	.byte	2,35,0,0,11
	.word	52243
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK_7',0,5,235,16,3
	.word	52283
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK',0,5,238,16,25,8,10
	.byte	'NUM',0,4
	.word	45738
	.byte	2,35,0,10
	.byte	'DEN',0,4
	.word	45668
	.byte	2,35,4,0,11
	.word	52315
	.byte	4
	.byte	'Ifx_GTM_CMU_ECLK',0,5,242,16,3
	.word	52366
	.byte	7
	.byte	'_Ifx_GTM_CMU_FXCLK',0,5,245,16,25,4,10
	.byte	'CTRL',0,4
	.word	45808
	.byte	2,35,0,0,11
	.word	52397
	.byte	4
	.byte	'Ifx_GTM_CMU_FXCLK',0,5,248,16,3
	.word	52437
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,5,251,16,25,4,10
	.byte	'OUTSEL',0,4
	.word	46973
	.byte	2,35,0,0,11
	.word	52469
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,5,254,16,3
	.word	52514
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_T',0,5,129,17,25,32,12,32
	.word	47050
	.byte	13,7,0,10
	.byte	'OUTSEL',0,32
	.word	52575
	.byte	2,35,0,0,11
	.word	52549
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_T',0,5,132,17,3
	.word	52601
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,5,135,17,25,32,10
	.byte	'INSEL',0,4
	.word	47125
	.byte	2,35,0,12,28
	.word	231
	.byte	13,27,0,10
	.byte	'reserved_4',0,28
	.word	52677
	.byte	2,35,4,0,11
	.word	52634
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,5,139,17,3
	.word	52707
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH',0,5,142,17,25,116,10
	.byte	'GPR0',0,4
	.word	49337
	.byte	2,35,0,10
	.byte	'GPR1',0,4
	.word	49406
	.byte	2,35,4,10
	.byte	'CNT',0,4
	.word	48778
	.byte	2,35,8,10
	.byte	'ECNT',0,4
	.word	48984
	.byte	2,35,12,10
	.byte	'CNTS',0,4
	.word	48846
	.byte	2,35,16,10
	.byte	'TDUC',0,4
	.word	49770
	.byte	2,35,20,10
	.byte	'TDUV',0,4
	.word	49839
	.byte	2,35,24,10
	.byte	'FLT_RE',0,4
	.word	49266
	.byte	2,35,28,10
	.byte	'FLT_FE',0,4
	.word	49195
	.byte	2,35,32,10
	.byte	'CTRL',0,4
	.word	48915
	.byte	2,35,36,10
	.byte	'ECTRL',0,4
	.word	49053
	.byte	2,35,40,10
	.byte	'IRQ_NOTIFY',0,4
	.word	49695
	.byte	2,35,44,10
	.byte	'IRQ_EN',0,4
	.word	49475
	.byte	2,35,48,10
	.byte	'IRQ_FORCINT',0,4
	.word	49546
	.byte	2,35,52,10
	.byte	'IRQ_MODE',0,4
	.word	49622
	.byte	2,35,56,10
	.byte	'EIRQ_EN',0,4
	.word	49123
	.byte	2,35,60,12,52
	.word	231
	.byte	13,51,0,10
	.byte	'reserved_40',0,52
	.word	53014
	.byte	2,35,64,0,11
	.word	52742
	.byte	4
	.byte	'Ifx_GTM_TIM_CH',0,5,161,17,3
	.word	53045
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH',0,5,164,17,25,48,10
	.byte	'CTRL',0,4
	.word	50314
	.byte	2,35,0,10
	.byte	'SR0',0,4
	.word	50678
	.byte	2,35,4,10
	.byte	'SR1',0,4
	.word	50746
	.byte	2,35,8,10
	.byte	'CM0',0,4
	.word	50110
	.byte	2,35,12,10
	.byte	'CM1',0,4
	.word	50178
	.byte	2,35,16,10
	.byte	'CN0',0,4
	.word	50246
	.byte	2,35,20,10
	.byte	'STAT',0,4
	.word	50814
	.byte	2,35,24,10
	.byte	'IRQ_NOTIFY',0,4
	.word	50603
	.byte	2,35,28,10
	.byte	'IRQ_EN',0,4
	.word	50383
	.byte	2,35,32,10
	.byte	'IRQ_FORCINT',0,4
	.word	50454
	.byte	2,35,36,10
	.byte	'IRQ_MODE',0,4
	.word	50530
	.byte	2,35,40,12,4
	.word	231
	.byte	13,3,0,10
	.byte	'reserved_2C',0,4
	.word	53264
	.byte	2,35,44,0,11
	.word	53074
	.byte	4
	.byte	'Ifx_GTM_TOM_CH',0,5,178,17,3
	.word	53295
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE',0,5,191,17,25,12,10
	.byte	'MODE',0,4
	.word	45115
	.byte	2,35,0,10
	.byte	'PTR1',0,4
	.word	45184
	.byte	2,35,4,10
	.byte	'PTR2',0,4
	.word	45253
	.byte	2,35,8,0,11
	.word	53324
	.byte	4
	.byte	'Ifx_GTM_BRIDGE',0,5,196,17,3
	.word	53389
	.byte	7
	.byte	'_Ifx_GTM_CMU',0,5,199,17,25,72,10
	.byte	'CLK_EN',0,4
	.word	45600
	.byte	2,35,0,10
	.byte	'GCLK_NUM',0,4
	.word	45950
	.byte	2,35,4,10
	.byte	'GCLK_DEN',0,4
	.word	45880
	.byte	2,35,8,12,24
	.word	52097
	.byte	13,5,0,11
	.word	53489
	.byte	10
	.byte	'CLK0_5',0,24
	.word	53498
	.byte	2,35,12,11
	.word	52171
	.byte	10
	.byte	'CLK_6',0,4
	.word	53519
	.byte	2,35,36,11
	.word	52243
	.byte	10
	.byte	'CLK_7',0,4
	.word	53539
	.byte	2,35,40,12,24
	.word	52315
	.byte	13,2,0,11
	.word	53559
	.byte	10
	.byte	'ECLK',0,24
	.word	53568
	.byte	2,35,44,11
	.word	52397
	.byte	10
	.byte	'FXCLK',0,4
	.word	53587
	.byte	2,35,68,0,11
	.word	53418
	.byte	4
	.byte	'Ifx_GTM_CMU',0,5,209,17,3
	.word	53608
	.byte	7
	.byte	'_Ifx_GTM_DTM',0,5,212,17,25,36,10
	.byte	'CTRL',0,4
	.word	46295
	.byte	2,35,0,10
	.byte	'CH_CTRL1',0,4
	.word	46082
	.byte	2,35,4,10
	.byte	'CH_CTRL2',0,4
	.word	46152
	.byte	2,35,8,10
	.byte	'CH_CTRL2_SR',0,4
	.word	46222
	.byte	2,35,12,10
	.byte	'PS_CTRL',0,4
	.word	46429
	.byte	2,35,16,12,16
	.word	46361
	.byte	13,3,0,10
	.byte	'DTV_CH',0,16
	.word	53741
	.byte	2,35,20,0,11
	.word	53634
	.byte	4
	.byte	'Ifx_GTM_DTM',0,5,220,17,3
	.word	53767
	.byte	7
	.byte	'_Ifx_GTM_ICM',0,5,223,17,25,60,10
	.byte	'IRQG_0',0,4
	.word	46628
	.byte	2,35,0,10
	.byte	'reserved_4',0,4
	.word	53264
	.byte	2,35,4,10
	.byte	'IRQG_2',0,4
	.word	46696
	.byte	2,35,8,12,12
	.word	231
	.byte	13,11,0,10
	.byte	'reserved_C',0,12
	.word	53864
	.byte	2,35,12,10
	.byte	'IRQG_6',0,4
	.word	46764
	.byte	2,35,24,12,20
	.word	231
	.byte	13,19,0,10
	.byte	'reserved_1C',0,20
	.word	53909
	.byte	2,35,28,10
	.byte	'IRQG_MEI',0,4
	.word	46903
	.byte	2,35,48,10
	.byte	'reserved_34',0,4
	.word	53264
	.byte	2,35,52,10
	.byte	'IRQG_CEI1',0,4
	.word	46832
	.byte	2,35,56,0,11
	.word	53793
	.byte	4
	.byte	'Ifx_GTM_ICM',0,5,234,17,3
	.word	53998
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL',0,5,237,17,25,148,1,12,32
	.word	52634
	.byte	13,0,0,11
	.word	54049
	.byte	10
	.byte	'TIM',0,32
	.word	54058
	.byte	2,35,0,11
	.word	52549
	.byte	10
	.byte	'T',0,32
	.word	54076
	.byte	2,35,32,12,80
	.word	231
	.byte	13,79,0,10
	.byte	'reserved_40',0,80
	.word	54092
	.byte	2,35,64,11
	.word	52469
	.byte	10
	.byte	'CAN',0,4
	.word	54122
	.byte	3,35,144,1,0,11
	.word	54024
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL',0,5,243,17,3
	.word	54142
	.byte	7
	.byte	'_Ifx_GTM_TBU',0,5,246,17,25,28,10
	.byte	'CHEN',0,4
	.word	48640
	.byte	2,35,0,10
	.byte	'CH0_CTRL',0,4
	.word	48290
	.byte	2,35,4,10
	.byte	'CH0_BASE',0,4
	.word	48220
	.byte	2,35,8,10
	.byte	'CH1_CTRL',0,4
	.word	48430
	.byte	2,35,12,10
	.byte	'CH1_BASE',0,4
	.word	48360
	.byte	2,35,16,10
	.byte	'CH2_CTRL',0,4
	.word	48570
	.byte	2,35,20,10
	.byte	'CH2_BASE',0,4
	.word	48500
	.byte	2,35,24,0,11
	.word	54173
	.byte	4
	.byte	'Ifx_GTM_TBU',0,5,255,17,3
	.word	54315
	.byte	7
	.byte	'_Ifx_GTM_TIM',0,5,130,18,25,128,8,11
	.word	52742
	.byte	10
	.byte	'CH0',0,116
	.word	54361
	.byte	2,35,0,10
	.byte	'INP_VAL',0,4
	.word	49976
	.byte	2,35,116,10
	.byte	'IN_SRC',0,4
	.word	49908
	.byte	2,35,120,10
	.byte	'RST',0,4
	.word	50045
	.byte	2,35,124,11
	.word	52742
	.byte	10
	.byte	'CH1',0,116
	.word	54425
	.byte	3,35,128,1,10
	.byte	'reserved_F4',0,12
	.word	53864
	.byte	3,35,244,1,11
	.word	52742
	.byte	10
	.byte	'CH2',0,116
	.word	54466
	.byte	3,35,128,2,10
	.byte	'reserved_174',0,12
	.word	53864
	.byte	3,35,244,2,11
	.word	52742
	.byte	10
	.byte	'CH3',0,116
	.word	54508
	.byte	3,35,128,3,10
	.byte	'reserved_1F4',0,12
	.word	53864
	.byte	3,35,244,3,11
	.word	52742
	.byte	10
	.byte	'CH4',0,116
	.word	54550
	.byte	3,35,128,4,10
	.byte	'reserved_274',0,12
	.word	53864
	.byte	3,35,244,4,11
	.word	52742
	.byte	10
	.byte	'CH5',0,116
	.word	54592
	.byte	3,35,128,5,10
	.byte	'reserved_2F4',0,12
	.word	53864
	.byte	3,35,244,5,11
	.word	52742
	.byte	10
	.byte	'CH6',0,116
	.word	54634
	.byte	3,35,128,6,10
	.byte	'reserved_374',0,12
	.word	53864
	.byte	3,35,244,6,11
	.word	52742
	.byte	10
	.byte	'CH7',0,116
	.word	54676
	.byte	3,35,128,7,10
	.byte	'reserved_3F4',0,12
	.word	53864
	.byte	3,35,244,7,0,11
	.word	54341
	.byte	4
	.byte	'Ifx_GTM_TIM',0,5,150,18,3
	.word	54719
	.byte	7
	.byte	'_Ifx_GTM_TOM',0,5,153,18,25,128,16,11
	.word	53074
	.byte	10
	.byte	'CH0',0,48
	.word	54765
	.byte	2,35,0,10
	.byte	'TGC0_GLB_CTRL',0,4
	.word	51186
	.byte	2,35,48,10
	.byte	'TGC0_ACT_TB',0,4
	.word	50883
	.byte	2,35,52,10
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	51110
	.byte	2,35,56,10
	.byte	'TGC0_INT_TRIG',0,4
	.word	51261
	.byte	2,35,60,11
	.word	53074
	.byte	10
	.byte	'CH1',0,48
	.word	54874
	.byte	2,35,64,10
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	50956
	.byte	2,35,112,10
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	51033
	.byte	2,35,116,10
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	51336
	.byte	2,35,120,10
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	51413
	.byte	2,35,124,11
	.word	53074
	.byte	10
	.byte	'CH2',0,48
	.word	54992
	.byte	3,35,128,1,12,16
	.word	231
	.byte	13,15,0,10
	.byte	'reserved_B0',0,16
	.word	55011
	.byte	3,35,176,1,11
	.word	53074
	.byte	10
	.byte	'CH3',0,48
	.word	55042
	.byte	3,35,192,1,10
	.byte	'reserved_F0',0,16
	.word	55011
	.byte	3,35,240,1,11
	.word	53074
	.byte	10
	.byte	'CH4',0,48
	.word	55083
	.byte	3,35,128,2,10
	.byte	'reserved_130',0,16
	.word	55011
	.byte	3,35,176,2,11
	.word	53074
	.byte	10
	.byte	'CH5',0,48
	.word	55125
	.byte	3,35,192,2,10
	.byte	'reserved_170',0,16
	.word	55011
	.byte	3,35,240,2,11
	.word	53074
	.byte	10
	.byte	'CH6',0,48
	.word	55167
	.byte	3,35,128,3,10
	.byte	'reserved_1B0',0,16
	.word	55011
	.byte	3,35,176,3,11
	.word	53074
	.byte	10
	.byte	'CH7',0,48
	.word	55209
	.byte	3,35,192,3,10
	.byte	'reserved_1F0',0,16
	.word	55011
	.byte	3,35,240,3,11
	.word	53074
	.byte	10
	.byte	'CH8',0,48
	.word	55251
	.byte	3,35,128,4,10
	.byte	'TGC1_GLB_CTRL',0,4
	.word	51793
	.byte	3,35,176,4,10
	.byte	'TGC1_ACT_TB',0,4
	.word	51490
	.byte	3,35,180,4,10
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	51717
	.byte	3,35,184,4,10
	.byte	'TGC1_INT_TRIG',0,4
	.word	51868
	.byte	3,35,188,4,11
	.word	53074
	.byte	10
	.byte	'CH9',0,48
	.word	55365
	.byte	3,35,192,4,10
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	51563
	.byte	3,35,240,4,10
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	51640
	.byte	3,35,244,4,10
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	51943
	.byte	3,35,248,4,10
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	52020
	.byte	3,35,252,4,11
	.word	53074
	.byte	10
	.byte	'CH10',0,48
	.word	55488
	.byte	3,35,128,5,10
	.byte	'reserved_2B0',0,16
	.word	55011
	.byte	3,35,176,5,11
	.word	53074
	.byte	10
	.byte	'CH11',0,48
	.word	55531
	.byte	3,35,192,5,10
	.byte	'reserved_2F0',0,16
	.word	55011
	.byte	3,35,240,5,11
	.word	53074
	.byte	10
	.byte	'CH12',0,48
	.word	55574
	.byte	3,35,128,6,10
	.byte	'reserved_330',0,16
	.word	55011
	.byte	3,35,176,6,11
	.word	53074
	.byte	10
	.byte	'CH13',0,48
	.word	55617
	.byte	3,35,192,6,10
	.byte	'reserved_370',0,16
	.word	55011
	.byte	3,35,240,6,11
	.word	53074
	.byte	10
	.byte	'CH14',0,48
	.word	55660
	.byte	3,35,128,7,10
	.byte	'reserved_3B0',0,16
	.word	55011
	.byte	3,35,176,7,11
	.word	53074
	.byte	10
	.byte	'CH15',0,48
	.word	55703
	.byte	3,35,192,7,12,144,8
	.word	231
	.byte	13,143,8,0,10
	.byte	'reserved_3F0',0,144,8
	.word	55723
	.byte	3,35,240,7,0,11
	.word	54745
	.byte	4
	.byte	'Ifx_GTM_TOM',0,5,199,18,3
	.word	55759
	.byte	14,6,130,4,20,64,10
	.byte	'CTRL',0,4
	.word	50314
	.byte	2,35,0,10
	.byte	'SR0',0,4
	.word	50678
	.byte	2,35,4,10
	.byte	'SR1',0,4
	.word	50746
	.byte	2,35,8,10
	.byte	'CM0',0,4
	.word	50110
	.byte	2,35,12,10
	.byte	'CM1',0,4
	.word	50178
	.byte	2,35,16,10
	.byte	'CN0',0,4
	.word	50246
	.byte	2,35,20,10
	.byte	'STAT',0,4
	.word	50814
	.byte	2,35,24,10
	.byte	'IRQ_NOTIFY',0,4
	.word	50603
	.byte	2,35,28,10
	.byte	'IRQ_EN',0,4
	.word	50383
	.byte	2,35,32,10
	.byte	'IRQ_FORCINT',0,4
	.word	50454
	.byte	2,35,36,10
	.byte	'IRQ_MODE',0,4
	.word	50530
	.byte	2,35,40,12,20
	.word	231
	.byte	13,19,0,10
	.byte	'reserved_2C',0,20
	.word	55959
	.byte	2,35,44,0,11
	.word	55785
	.byte	4
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,6,155,4,4
	.word	55990
	.byte	14,6,157,4,20,128,4,10
	.byte	'GLB_CTRL',0,4
	.word	51186
	.byte	2,35,0,10
	.byte	'ACT_TB',0,4
	.word	50883
	.byte	2,35,4,10
	.byte	'FUPD_CTRL',0,4
	.word	51110
	.byte	2,35,8,10
	.byte	'INT_TRIG',0,4
	.word	51261
	.byte	2,35,12,12,48
	.word	231
	.byte	13,47,0,10
	.byte	'reserved_tgc0',0,48
	.word	56102
	.byte	2,35,16,10
	.byte	'ENDIS_CTRL',0,4
	.word	50956
	.byte	2,35,64,10
	.byte	'ENDIS_STAT',0,4
	.word	51033
	.byte	2,35,68,10
	.byte	'OUTEN_CTRL',0,4
	.word	51336
	.byte	2,35,72,10
	.byte	'OUTEN_STAT',0,4
	.word	51413
	.byte	2,35,76,12,176,3
	.word	231
	.byte	13,175,3,0,10
	.byte	'reserved_tgc1',0,176,3
	.word	56214
	.byte	2,35,80,0,11
	.word	56024
	.byte	4
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,6,177,4,5
	.word	56250
	.byte	14,6,179,4,20,128,16,10
	.byte	'reserved_tom0',0,48
	.word	56102
	.byte	2,35,0,12,128,8
	.word	56024
	.byte	13,1,0,11
	.word	56315
	.byte	10
	.byte	'TGC',0,128,8
	.word	56325
	.byte	2,35,48,12,208,7
	.word	231
	.byte	13,207,7,0,10
	.byte	'reserved_tgc2',0,208,7
	.word	56344
	.byte	3,35,176,8,0,11
	.word	56285
	.byte	4
	.byte	'Ifx_GTM_TOM_TGCx',0,6,184,4,5
	.word	56381
	.byte	14,6,187,4,20,128,16,12,128,8
	.word	55785
	.byte	13,15,0,11
	.word	56419
	.byte	10
	.byte	'CH',0,128,8
	.word	56429
	.byte	2,35,0,12,128,8
	.word	231
	.byte	13,255,7,0,10
	.byte	'reserved_tom1',0,128,8
	.word	56447
	.byte	3,35,128,8,0,11
	.word	56412
	.byte	4
	.byte	'Ifx_GTM_TOM_CHx',0,6,191,4,5
	.word	56484
	.byte	14,6,212,4,20,128,1,10
	.byte	'CH_GPR0',0,4
	.word	49337
	.byte	2,35,0,10
	.byte	'CH_GPR1',0,4
	.word	49406
	.byte	2,35,4,10
	.byte	'CH_CNT',0,4
	.word	48778
	.byte	2,35,8,10
	.byte	'CH_ECNT',0,4
	.word	48984
	.byte	2,35,12,10
	.byte	'CH_CNTS',0,4
	.word	48846
	.byte	2,35,16,10
	.byte	'CH_TDUC',0,4
	.word	49770
	.byte	2,35,20,10
	.byte	'CH_TDUV',0,4
	.word	49839
	.byte	2,35,24,10
	.byte	'CH_FLT_RE',0,4
	.word	49266
	.byte	2,35,28,10
	.byte	'CH_FLT_FE',0,4
	.word	49195
	.byte	2,35,32,10
	.byte	'CH_CTRL',0,4
	.word	48915
	.byte	2,35,36,10
	.byte	'CH_ECTRL',0,4
	.word	49053
	.byte	2,35,40,10
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	49695
	.byte	2,35,44,10
	.byte	'CH_IRQ_EN',0,4
	.word	49475
	.byte	2,35,48,10
	.byte	'CH_IRQ_FORCINT',0,4
	.word	49546
	.byte	2,35,52,10
	.byte	'CH_IRQ_MODE',0,4
	.word	49622
	.byte	2,35,56,10
	.byte	'CH_EIRQ_EN',0,4
	.word	49123
	.byte	2,35,60,12,64
	.word	231
	.byte	13,63,0,10
	.byte	'reserved_40',0,64
	.word	56819
	.byte	2,35,64,0,11
	.word	56514
	.byte	4
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,6,248,4,4
	.word	56850
	.byte	14,6,250,4,20,8,10
	.byte	'IN_SRC',0,4
	.word	49908
	.byte	2,35,0,10
	.byte	'RST',0,4
	.word	50045
	.byte	2,35,4,0,11
	.word	56884
	.byte	4
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,6,255,4,4
	.word	56920
	.byte	14,6,129,5,21,128,16,12,128,8
	.word	56514
	.byte	13,7,0,11
	.word	56971
	.byte	10
	.byte	'CH',0,128,8
	.word	56981
	.byte	2,35,0,10
	.byte	'reserved_tim1',0,128,8
	.word	56447
	.byte	3,35,128,8,0,11
	.word	56964
	.byte	4
	.byte	'Ifx_GTM_TIM_CHx',0,6,133,5,4
	.word	57025
	.byte	14,6,135,5,20,128,16,12,120
	.word	231
	.byte	13,119,0,10
	.byte	'reserved_tim2',0,120
	.word	57062
	.byte	2,35,0,11
	.word	56884
	.byte	10
	.byte	'IN_SRC_RESET',0,8
	.word	57094
	.byte	2,35,120,12,128,15
	.word	231
	.byte	13,255,14,0,10
	.byte	'reserved_tim3',0,128,15
	.word	57121
	.byte	3,35,128,1,0,11
	.word	57055
	.byte	4
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,6,140,5,4
	.word	57158
	.byte	15,6,174,5,11,1,16
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,16
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,16
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,16
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,16
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,16
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,16
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,16
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,4
	.byte	'Gtm_ConfigurableClockType',0,6,184,5,4
	.word	57196
	.byte	15,6,188,5,11,1,16
	.byte	'GTM_LOW',0,0,16
	.byte	'GTM_HIGH',0,1,0,4
	.byte	'Gtm_OutputLevelType',0,6,192,5,4
	.word	57430
	.byte	15,6,195,5,11,1,16
	.byte	'TOM_GLB_CTRL',0,0,16
	.byte	'TOM_ACT_TB',0,1,16
	.byte	'TOM_FUPD_CTRL',0,2,16
	.byte	'TOM_INT_TRIG',0,3,16
	.byte	'TOM_RESERVED_0',0,4,16
	.byte	'TOM_RESERVED_1',0,5,16
	.byte	'TOM_RESERVED_2',0,6,16
	.byte	'TOM_RESERVED_3',0,7,16
	.byte	'TOM_RESERVED_4',0,8,16
	.byte	'TOM_RESERVED_5',0,9,16
	.byte	'TOM_RESERVED_6',0,10,16
	.byte	'TOM_RESERVED_7',0,11,16
	.byte	'TOM_RESERVED_8',0,12,16
	.byte	'TOM_RESERVED_9',0,13,16
	.byte	'TOM_RESERVED_10',0,14,16
	.byte	'TOM_RESERVED_11',0,15,16
	.byte	'TOM_ENDIS_CTRL',0,16,16
	.byte	'TOM_ENDIS_STAT',0,17,16
	.byte	'TOM_OUTEN_CTRL',0,18,16
	.byte	'TOM_OUTEN_STAT',0,19,0,4
	.byte	'Gtm_TomTimerRegistersType',0,6,217,5,4
	.word	57487
	.byte	14,6,221,5,11,8,10
	.byte	'FltRisingEdge',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'FltFallingEdge',0,4
	.word	299
	.byte	2,35,4,0,4
	.byte	'Gtm_TimFilterType',0,6,225,5,4
	.word	57862
	.byte	4
	.byte	'Gtm_TbuChCtrlType',0,6,230,5,32
	.word	48290
	.byte	4
	.byte	'Gtm_TbuChBaseType',0,6,231,5,32
	.word	48220
	.byte	14,6,233,5,11,8,10
	.byte	'CH_CTRL',0,4
	.word	48290
	.byte	2,35,0,10
	.byte	'CH_BASE',0,4
	.word	48220
	.byte	2,35,4,0,4
	.byte	'Gtm_TbuChType',0,6,237,5,4
	.word	57997
	.byte	14,6,249,5,9,36,12,4
	.word	299
	.byte	13,0,0,10
	.byte	'TimInSel',0,4
	.word	58067
	.byte	2,35,0,12,32
	.word	299
	.byte	13,7,0,10
	.byte	'ToutSel',0,32
	.word	58094
	.byte	2,35,4,0,4
	.byte	'Gtm_PortConfigType',0,6,253,5,2
	.word	58061
	.byte	14,6,129,6,9,8,10
	.byte	'TimRisingEdgeFilter',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'TimFallingEdgeFilter',0,4
	.word	299
	.byte	2,35,4,0,4
	.byte	'Gtm_TimFltType',0,6,134,6,2
	.word	58149
	.byte	14,6,138,6,11,24,10
	.byte	'TimUsage',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'TimIrqEn',0,1
	.word	231
	.byte	2,35,1,10
	.byte	'TimErrIrqEn',0,1
	.word	231
	.byte	2,35,2,10
	.byte	'TimExtCapSrc',0,1
	.word	231
	.byte	2,35,3,10
	.byte	'TimCtrlValue',0,4
	.word	299
	.byte	2,35,4,17
	.word	58149
	.byte	3
	.word	58346
	.byte	10
	.byte	'GtmTimFltPtr',0,4
	.word	58351
	.byte	2,35,8,10
	.byte	'TimCntsValue',0,4
	.word	299
	.byte	2,35,12,10
	.byte	'TimTduValue',0,4
	.word	299
	.byte	2,35,16,10
	.byte	'TimInSrcSel',0,4
	.word	299
	.byte	2,35,20,0,4
	.byte	'Gtm_TimConfigType',0,6,151,6,4
	.word	58239
	.byte	14,6,154,6,11,40,12,8
	.word	231
	.byte	13,7,0,10
	.byte	'Gtm_TimUsage',0,8
	.word	58476
	.byte	2,35,0,12,16
	.word	231
	.byte	13,15,0,12,32
	.word	58507
	.byte	13,1,0,10
	.byte	'Gtm_TomUsage',0,32
	.word	58516
	.byte	2,35,8,0,4
	.byte	'Gtm_ModUsageConfigType',0,6,163,6,4
	.word	58470
	.byte	14,6,177,6,9,16,10
	.byte	'GtmTomUpdateEn',0,2
	.word	262
	.byte	2,35,0,10
	.byte	'GtmTomEndisCtrl',0,2
	.word	262
	.byte	2,35,2,10
	.byte	'GtmTomEndisStat',0,2
	.word	262
	.byte	2,35,4,10
	.byte	'GtmTomOutenCtrl',0,2
	.word	262
	.byte	2,35,6,10
	.byte	'GtmTomOutenStat',0,2
	.word	262
	.byte	2,35,8,10
	.byte	'GtmTomFupd',0,4
	.word	299
	.byte	2,35,10,0,4
	.byte	'Gtm_TomTgcConfigGroupType',0,6,185,6,2
	.word	58580
	.byte	14,6,189,6,9,12,10
	.byte	'GtmTomIntTrig',0,2
	.word	262
	.byte	2,35,0,10
	.byte	'GtmTomActTb',0,4
	.word	299
	.byte	2,35,2,17
	.word	58580
	.byte	3
	.word	58816
	.byte	10
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	58821
	.byte	2,35,8,0,4
	.byte	'Gtm_TomTgcConfigType',0,6,196,6,2
	.word	58766
	.byte	14,6,199,6,9,12,10
	.byte	'GtmTomIrqEn',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'GtmTomCn0Value',0,2
	.word	262
	.byte	2,35,2,10
	.byte	'GtmTomCm0Value',0,2
	.word	262
	.byte	2,35,4,10
	.byte	'GtmTomCm1Value',0,2
	.word	262
	.byte	2,35,6,10
	.byte	'GtmTomSr0Value',0,2
	.word	262
	.byte	2,35,8,10
	.byte	'GtmTomSr1Value',0,2
	.word	262
	.byte	2,35,10,0,4
	.byte	'Gtm_TomChannelConfigType',0,6,207,6,2
	.word	58888
	.byte	14,6,211,6,9,12,10
	.byte	'TomUsage',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'GtmTomIrqMode',0,1
	.word	231
	.byte	2,35,1,10
	.byte	'GtmTomControlWord',0,4
	.word	299
	.byte	2,35,2,17
	.word	58888
	.byte	3
	.word	59144
	.byte	10
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	59149
	.byte	2,35,8,0,4
	.byte	'Gtm_TomConfigType',0,6,219,6,2
	.word	59070
	.byte	14,6,223,6,9,8,10
	.byte	'CmuEclkNum',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'CmuEclkDen',0,4
	.word	299
	.byte	2,35,4,0,4
	.byte	'Gtm_ExtClkType',0,6,227,6,2
	.word	59211
	.byte	14,6,230,6,9,64,10
	.byte	'GtmClockEnable',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'GtmCmuClkCnt',0,32
	.word	58094
	.byte	2,35,4,10
	.byte	'GtmFxdClkControl',0,4
	.word	299
	.byte	2,35,36,12,24
	.word	59211
	.byte	13,2,0,10
	.byte	'GtmEclk',0,24
	.word	59360
	.byte	2,35,40,0,4
	.byte	'Gtm_ClockSettingType',0,6,236,6,2
	.word	59282
	.byte	14,6,240,6,9,4,10
	.byte	'GtmCtrlValue',0,2
	.word	262
	.byte	2,35,0,10
	.byte	'GtmIrqEnable',0,2
	.word	262
	.byte	2,35,2,0,4
	.byte	'Gtm_GeneralConfigType',0,6,245,6,2
	.word	59417
	.byte	14,6,249,6,9,6,10
	.byte	'TbuChannelCtrl',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'TbuBaseValue',0,4
	.word	299
	.byte	2,35,2,0,4
	.byte	'Gtm_TbuConfigType',0,6,253,6,2
	.word	59499
	.byte	14,6,129,7,9,72,10
	.byte	'GtmModuleSleepEnable',0,1
	.word	231
	.byte	2,35,0,10
	.byte	'GtmGclkNum',0,4
	.word	299
	.byte	2,35,2,10
	.byte	'GtmGclkDen',0,4
	.word	299
	.byte	2,35,6,10
	.byte	'GtmAccessEnable0',0,4
	.word	299
	.byte	2,35,10,10
	.byte	'GtmAccessEnable1',0,4
	.word	299
	.byte	2,35,14,12,2
	.word	262
	.byte	13,0,0,10
	.byte	'GtmTimModuleUsage',0,2
	.word	59707
	.byte	2,35,18,12,1
	.word	231
	.byte	13,0,0,10
	.byte	'GtmTimUsage',0,1
	.word	59743
	.byte	2,35,20,17
	.word	58239
	.byte	3
	.word	59773
	.byte	10
	.byte	'GtmTimConfigPtr',0,4
	.word	59778
	.byte	2,35,24,10
	.byte	'GtmTomTgcUsage',0,1
	.word	59743
	.byte	2,35,28,17
	.word	58766
	.byte	3
	.word	59832
	.byte	10
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	59837
	.byte	2,35,32,12,8
	.word	299
	.byte	13,1,0,10
	.byte	'GtmTomModuleUsage',0,8
	.word	59870
	.byte	2,35,36,10
	.byte	'GtmTomUsage',0,4
	.word	58067
	.byte	2,35,44,17
	.word	59070
	.byte	3
	.word	59927
	.byte	10
	.byte	'GtmTomConfigPtr',0,4
	.word	59932
	.byte	2,35,48,17
	.word	58470
	.byte	3
	.word	59962
	.byte	10
	.byte	'GtmModUsageConfigPtr',0,4
	.word	59967
	.byte	2,35,52,17
	.word	59417
	.byte	3
	.word	60002
	.byte	10
	.byte	'GtmGeneralConfigPtr',0,4
	.word	60007
	.byte	2,35,56,17
	.word	59499
	.byte	3
	.word	60041
	.byte	10
	.byte	'GtmTbuConfigPtr',0,4
	.word	60046
	.byte	2,35,60,17
	.word	231
	.byte	3
	.word	60076
	.byte	10
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	60081
	.byte	2,35,64,10
	.byte	'GtmTtcanTriggers',0,2
	.word	59707
	.byte	2,35,68,0,4
	.byte	'Gtm_ModuleConfigType',0,6,163,7,2
	.word	59579
	.byte	18,1,1,19
	.word	231
	.byte	19
	.word	231
	.byte	19
	.word	231
	.byte	19
	.word	262
	.byte	0,3
	.word	60173
	.byte	4
	.byte	'Gtm_NotificationPtrType',0,6,172,7,16
	.word	60197
	.byte	7
	.byte	'Gtm_ConfigType',0,6,192,7,16,12,17
	.word	59282
	.byte	3
	.word	60256
	.byte	10
	.byte	'GtmClockSettingPtr',0,4
	.word	60261
	.byte	2,35,0,17
	.word	58061
	.byte	3
	.word	60294
	.byte	10
	.byte	'GtmPortConfigPtr',0,4
	.word	60299
	.byte	2,35,4,17
	.word	59579
	.byte	3
	.word	60330
	.byte	10
	.byte	'GtmModuleConfigPtr',0,4
	.word	60335
	.byte	2,35,8,0,4
	.byte	'Gtm_ConfigType',0,6,197,7,2
	.word	60235
	.byte	4
	.byte	'Mcu_ClockType',0,7,156,3,18
	.word	299
	.byte	4
	.byte	'Mcu_ModeType',0,7,162,3,18
	.word	299
	.byte	4
	.byte	'Mcu_RamSectionType',0,7,168,3,18
	.word	299
	.byte	4
	.byte	'Mcu_RamBaseAdrType',0,7,178,3,18
	.word	182
	.byte	4
	.byte	'Mcu_RamSizeType',0,7,181,3,17
	.word	299
	.byte	4
	.byte	'Mcu_RamPrstDatType',0,7,184,3,16
	.word	231
	.byte	7
	.byte	'Mcu_ClockCfgType',0,7,236,3,16,80,10
	.byte	'K2div',0,8
	.word	58476
	.byte	2,35,0,10
	.byte	'K2RampToPllDelayTicks',0,32
	.word	58094
	.byte	2,35,8,14,7,247,3,3,4,8
	.byte	'K1div',0,1
	.word	231
	.byte	7,1,2,35,0,8
	.byte	'K3div',0,2
	.word	262
	.byte	7,2,2,35,0,8
	.byte	'Ndiv',0,4
	.word	351
	.byte	7,11,2,35,2,8
	.byte	'Pdiv',0,2
	.word	262
	.byte	4,7,2,35,2,8
	.byte	'K2steps',0,1
	.word	231
	.byte	4,3,2,35,3,8
	.byte	'PllMode',0,1
	.word	231
	.byte	1,2,2,35,3,8
	.byte	'Reserved',0,1
	.word	231
	.byte	2,0,2,35,3,0,10
	.byte	'Mcu_ClockDivValues',0,4
	.word	60616
	.byte	2,35,40,14,7,132,4,3,4,8
	.byte	'McuErayNDivider',0,1
	.word	231
	.byte	5,3,2,35,0,8
	.byte	'McuErayK2Divider',0,2
	.word	262
	.byte	7,4,2,35,0,8
	.byte	'McuErayK3Divider',0,4
	.word	351
	.byte	7,13,2,35,2,8
	.byte	'McuErayPDivider',0,1
	.word	231
	.byte	4,1,2,35,2,8
	.byte	'Reserved',0,2
	.word	262
	.byte	9,0,2,35,2,0,10
	.byte	'MCU_ErayPllDivValues',0,4
	.word	60775
	.byte	2,35,44,10
	.byte	'Ccucon0',0,4
	.word	299
	.byte	2,35,48,10
	.byte	'Ccucon1',0,4
	.word	299
	.byte	2,35,52,10
	.byte	'Ccucon2',0,4
	.word	299
	.byte	2,35,56,10
	.byte	'Ccucon5',0,4
	.word	299
	.byte	2,35,60,10
	.byte	'Ccucon6',0,4
	.word	299
	.byte	2,35,64,10
	.byte	'Ccucon7',0,4
	.word	299
	.byte	2,35,68,10
	.byte	'Ccucon8',0,4
	.word	299
	.byte	2,35,72,10
	.byte	'K2RampToPllDelayConf',0,1
	.word	231
	.byte	2,35,76,0,4
	.byte	'Mcu_ClockCfgType',0,7,162,4,2
	.word	60547
	.byte	7
	.byte	'Mcu_StandbyModeType',0,7,171,4,16,6,10
	.byte	'PMSWCR0',0,4
	.word	299
	.byte	2,35,0,10
	.byte	'CrcCheckEnable',0,1
	.word	231
	.byte	2,35,4,0,4
	.byte	'Mcu_StandbyModeType',0,7,175,4,2
	.word	61118
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,23,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0
	.byte	0,11,53,0,73,19,0,0,12,1,1,11,15,73,19,0,0,13,33,0,47,15,0,0,14,19,1,58,15,59,15,57,15,11,15,0,0,15,4
	.byte	1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0,17,38,0,73,19,0,0,18,21,1,54,15,39,12,0,0,19,5,0,73
	.byte	19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcu_Crc.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_TcLib.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxGtm_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Gtm.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcu.h',0,0,0,0,0
.L9:
.L7:

; ..\mcal_src\Mcu_Crc.c	     1  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	     2  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_src\Mcu_Crc.c	     4  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	     5  ** All rights reserved.                                                       **
; ..\mcal_src\Mcu_Crc.c	     6  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Mcu_Crc.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Mcu_Crc.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Mcu_Crc.c	    10  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    11  ********************************************************************************
; ..\mcal_src\Mcu_Crc.c	    12  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    13  **  $FILENAME   : Mcu_Crc.c $                                                **
; ..\mcal_src\Mcu_Crc.c	    14  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    15  **  $CC VERSION : \main\dev_tc23x\5 $                                        **
; ..\mcal_src\Mcu_Crc.c	    16  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    17  **  $DATE       : 2015-08-13 $                                               **
; ..\mcal_src\Mcu_Crc.c	    18  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                      **
; ..\mcal_src\Mcu_Crc.c	    20  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    21  **  VENDOR      : Infineon Technologies                                       **
; ..\mcal_src\Mcu_Crc.c	    22  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    23  **  DESCRIPTION : This file contains basic initialization of CRC(FCE) module. **
; ..\mcal_src\Mcu_Crc.c	    24  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    25  **  MAY BE CHANGED BY USER [yes/no]: No                                       **
; ..\mcal_src\Mcu_Crc.c	    26  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    27  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	    28  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	    29  **                      Includes                                              **
; ..\mcal_src\Mcu_Crc.c	    30  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	    31  
; ..\mcal_src\Mcu_Crc.c	    32  #include "Mcu.h"
; ..\mcal_src\Mcu_Crc.c	    33  #include "Mcu_Local.h"
; ..\mcal_src\Mcu_Crc.c	    34  
; ..\mcal_src\Mcu_Crc.c	    35  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	    36  **                      Global Constant Definitions                           **
; ..\mcal_src\Mcu_Crc.c	    37  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	    38  
; ..\mcal_src\Mcu_Crc.c	    39  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	    40  **                      Global Variable Definitions                           **
; ..\mcal_src\Mcu_Crc.c	    41  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	    42  
; ..\mcal_src\Mcu_Crc.c	    43  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	    44  **                      Private Constant Definitions                          **
; ..\mcal_src\Mcu_Crc.c	    45  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	    46  
; ..\mcal_src\Mcu_Crc.c	    47  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	    48  **                      Private Variable Definitions                          **
; ..\mcal_src\Mcu_Crc.c	    49  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	    50  
; ..\mcal_src\Mcu_Crc.c	    51  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	    52  **                      Global Function Definitions                           **
; ..\mcal_src\Mcu_Crc.c	    53  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	    54  
; ..\mcal_src\Mcu_Crc.c	    55  #if (MCU_CRC_HW_USED == STD_ON)  
; ..\mcal_src\Mcu_Crc.c	    56  
; ..\mcal_src\Mcu_Crc.c	    57  /*Memory Map of the MCU Code*/
; ..\mcal_src\Mcu_Crc.c	    58  #define MCU_START_SEC_CODE
; ..\mcal_src\Mcu_Crc.c	    59  #include "MemMap.h"
; ..\mcal_src\Mcu_Crc.c	    60  
; ..\mcal_src\Mcu_Crc.c	    61  
; ..\mcal_src\Mcu_Crc.c	    62  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	    63  ** Syntax : Std_ReturnType Mcu_lCrcInit (void)                                **
; ..\mcal_src\Mcu_Crc.c	    64  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    65  ** Service ID:    None                                                        **
; ..\mcal_src\Mcu_Crc.c	    66  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    67  ** Sync/Async:    Synchronous                                                 **
; ..\mcal_src\Mcu_Crc.c	    68  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    69  ** Reentrancy:    Non-reentrant                                               **
; ..\mcal_src\Mcu_Crc.c	    70  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    71  ** Parameters (in):   None                                                    **
; ..\mcal_src\Mcu_Crc.c	    72  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    73  ** Parameters (out):  None                                                    **
; ..\mcal_src\Mcu_Crc.c	    74  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    75  ** Return value:      E_OK                                                    **
; ..\mcal_src\Mcu_Crc.c	    76  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    77  ** Description :  This service shall initialize CRC HW if present.            **
; ..\mcal_src\Mcu_Crc.c	    78  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    79  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	    80  Std_ReturnType Mcu_lCrcInit(void)
; ..\mcal_src\Mcu_Crc.c	    81  {
; ..\mcal_src\Mcu_Crc.c	    82    return(E_OK);
; ..\mcal_src\Mcu_Crc.c	    83  }/*End of Mcu_lCrcInit()*/
; ..\mcal_src\Mcu_Crc.c	    84  
; ..\mcal_src\Mcu_Crc.c	    85  #if (MCU_DEINIT_API == STD_ON)
; ..\mcal_src\Mcu_Crc.c	    86  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	    87  ** Syntax : void Mcu_lCrcDeInit (void)                                        **
; ..\mcal_src\Mcu_Crc.c	    88  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    89  ** Service ID:    None                                                        **
; ..\mcal_src\Mcu_Crc.c	    90  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    91  ** Sync/Async:    Synchronous                                                 **
; ..\mcal_src\Mcu_Crc.c	    92  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    93  ** Reentrancy:    Non-reentrant                                               **
; ..\mcal_src\Mcu_Crc.c	    94  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    95  ** Parameters (in):   None                                                    **
; ..\mcal_src\Mcu_Crc.c	    96  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    97  ** Parameters (out):  None                                                    **
; ..\mcal_src\Mcu_Crc.c	    98  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	    99  ** Return value:      None                                                    **
; ..\mcal_src\Mcu_Crc.c	   100  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   101  ** Description :  This service shall de-initialize FCE and
; ..\mcal_src\Mcu_Crc.c	   102                    remove the clock connection                                 **
; ..\mcal_src\Mcu_Crc.c	   103  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   104  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	   105  void Mcu_lCrcDeInit(void)
; ..\mcal_src\Mcu_Crc.c	   106  {
; ..\mcal_src\Mcu_Crc.c	   107  }/*End of Mcu_lCrcDeInit()*/
; ..\mcal_src\Mcu_Crc.c	   108  #endif
; ..\mcal_src\Mcu_Crc.c	   109  
; ..\mcal_src\Mcu_Crc.c	   110  #if (MCU_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Mcu_Crc.c	   111  /*******************************************************************************
; ..\mcal_src\Mcu_Crc.c	   112  ** Syntax : Std_ReturnType Mcu_lCrcInitCheck (void)                           **
; ..\mcal_src\Mcu_Crc.c	   113  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   114  ** Service ID:    None                                                        **
; ..\mcal_src\Mcu_Crc.c	   115  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   116  ** Sync/Async:    Synchronous                                                 **
; ..\mcal_src\Mcu_Crc.c	   117  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   118  ** Reentrancy:    Non-reentrant                                               **
; ..\mcal_src\Mcu_Crc.c	   119  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   120  ** Parameters (in):   None                                                    **
; ..\mcal_src\Mcu_Crc.c	   121  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   122  ** Parameters (out):  None                                                    **
; ..\mcal_src\Mcu_Crc.c	   123  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   124  ** Return value    :    E_OK - if initialization comparison is success        **
; ..\mcal_src\Mcu_Crc.c	   125  **                      E_NOT_OK - if initialization comparison fails         **
; ..\mcal_src\Mcu_Crc.c	   126  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   127  ** Description :  This service shall verify the CRC specific                  **
; ..\mcal_src\Mcu_Crc.c	   128  **                initialization done by MCU.                                 **
; ..\mcal_src\Mcu_Crc.c	   129  **                                                                            **
; ..\mcal_src\Mcu_Crc.c	   130  *******************************************************************************/
; ..\mcal_src\Mcu_Crc.c	   131  Std_ReturnType Mcu_lCrcInitCheck(void)
; ..\mcal_src\Mcu_Crc.c	   132  { 
; ..\mcal_src\Mcu_Crc.c	   133    return E_OK;
; ..\mcal_src\Mcu_Crc.c	   134  }/*End of Mcu_lCrcInitCheck()*/
; ..\mcal_src\Mcu_Crc.c	   135  
; ..\mcal_src\Mcu_Crc.c	   136  #endif /*End Of MCU_SAFETY_ENABLE*/
; ..\mcal_src\Mcu_Crc.c	   137  
; ..\mcal_src\Mcu_Crc.c	   138  #define MCU_STOP_SEC_CODE
; ..\mcal_src\Mcu_Crc.c	   139  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Mcu_Crc.c	   140   is allowed only for MemMap.h*/
; ..\mcal_src\Mcu_Crc.c	   141  #include "MemMap.h"
; ..\mcal_src\Mcu_Crc.c	   142  
; ..\mcal_src\Mcu_Crc.c	   143  #endif /*#if (MCU_CRC_HW_USED == STD_ON) */

	; Module end
