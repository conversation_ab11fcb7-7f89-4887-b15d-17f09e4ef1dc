	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc20736a -c99 --dep-file=vss_code\\.vssvar.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=vss_code\\vssvar.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o vss_code\\vssvar.src ..\\vss_code\\vssvar.c"
	.compiler_name		"ctc"
	.name	"vssvar"

	
$TC16X
	
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssBnMem72')
	.sect	'.vss_api_data_ROM'
	.global	g_vssBnMem72
	.align	4
g_vssBnMem72:	.type	object
	.size	g_vssBnMem72,432
	.space	432
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssBnMem32')
	.sect	'.vss_api_data_ROM'
	.global	g_vssBnMem32
	.align	4
g_vssBnMem32:	.type	object
	.size	g_vssBnMem32,384
	.space	384
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssCflagMem72')
	.sect	'.vss_api_data_ROM'
	.global	g_vssCflagMem72
	.align	4
g_vssCflagMem72:	.type	object
	.size	g_vssCflagMem72,6
	.space	6
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssCflagMem32')
	.sect	'.vss_api_data_ROM'
	.global	g_vssCflagMem32
	.align	4
g_vssCflagMem32:	.type	object
	.size	g_vssCflagMem32,12
	.space	12
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_bn_dp')
	.sect	'.vss_api_data_ROM'
	.global	vss_bn_dp
	.align	4
vss_bn_dp:	.type	object
	.size	vss_bn_dp,1024
	.space	1024
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_op1')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_op1
	.align	4
vss_mc_op1:	.type	object
	.size	vss_mc_op1,12
	.word	1
	.space	8
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_op2')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_op2
	.align	4
vss_mc_op2:	.type	object
	.size	vss_mc_op2,12
	.word	1
	.space	8
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_opp')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_opp
	.align	4
vss_mc_opp:	.type	object
	.size	vss_mc_opp,12
	.word	1
	.space	8
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_len')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_len
	.align	4
vss_mc_len:	.type	object
	.size	vss_mc_len,4
	.space	4
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_res')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_res
	.align	4
vss_mc_res:	.type	object
	.size	vss_mc_res,12
	.word	1
	.space	4
	.word	vss_bn_dp
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_len_tmp1')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_len_tmp1
	.align	4
vss_mc_len_tmp1:	.type	object
	.size	vss_mc_len_tmp1,4
	.space	4
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_tmp1')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_tmp1
	.align	4
vss_mc_tmp1:	.type	object
	.size	vss_mc_tmp1,12
	.word	1
	.space	4
	.word	vss_bn_dp+256
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_len_tmp2')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_len_tmp2
	.align	4
vss_mc_len_tmp2:	.type	object
	.size	vss_mc_len_tmp2,4
	.space	4
	.sdecl	'.vss_api_data_ROM',data,cluster('vss_mc_tmp2')
	.sect	'.vss_api_data_ROM'
	.global	vss_mc_tmp2
	.align	4
vss_mc_tmp2:	.type	object
	.size	vss_mc_tmp2,12
	.word	1
	.space	4
	.word	vss_bn_dp+512
	.sdecl	'.vss_api_data_ROM',data,cluster('k')
	.sect	'.vss_api_data_ROM'
	.global	k
	.align	4
k:	.type	object
	.size	k,130
	.space	130
	.sdecl	'.vss_api_data_ROM',data,cluster('RR_x')
	.sect	'.vss_api_data_ROM'
	.global	RR_x
	.align	4
RR_x:	.type	object
	.size	RR_x,32
	.space	32
	.sdecl	'.vss_api_data_ROM',data,cluster('RR_y')
	.sect	'.vss_api_data_ROM'
	.global	RR_y
	.align	4
RR_y:	.type	object
	.size	RR_y,32
	.space	32
	.sdecl	'.vss_api_data_ROM',data,cluster('RR_z')
	.sect	'.vss_api_data_ROM'
	.global	RR_z
	.align	4
RR_z:	.type	object
	.size	RR_z,32
	.space	32
	.sdecl	'.vss_api_data_ROM',data,cluster('sm2_p_data')
	.sect	'.vss_api_data_ROM'
	.global	sm2_p_data
	.align	4
sm2_p_data:	.type	object
	.size	sm2_p_data,32
	.word	-1,-1
	.space	4
	.word	-1,-1,-1,-1
	.word	-2
	.sdecl	'.vss_api_data_ROM',data,cluster('sm2_p')
	.sect	'.vss_api_data_ROM'
	.global	sm2_p
	.align	4
sm2_p:	.type	object
	.size	sm2_p,12
	.word	1,8,sm2_p_data
	.sdecl	'.vss_api_data_ROM',data,cluster('sm2_kdf_len')
	.sect	'.vss_api_data_ROM'
	.global	sm2_kdf_len
sm2_kdf_len:	.type	object
	.size	sm2_kdf_len,1
	.space	1
	.sdecl	'.vss_api_data_ROM',data,cluster('nist256_p_data')
	.sect	'.vss_api_data_ROM'
	.global	nist256_p_data
	.align	4
nist256_p_data:	.type	object
	.size	nist256_p_data,32
	.word	-1,-1,-1
	.space	12
	.word	1,-1
	.sdecl	'.vss_api_data_ROM',data,cluster('nist256_p')
	.sect	'.vss_api_data_ROM'
	.global	nist256_p
	.align	4
nist256_p:	.type	object
	.size	nist256_p,12
	.word	1,8,nist256_p_data
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssRandSeed')
	.sect	'.vss_api_data_ROM'
	.global	g_vssRandSeed
	.align	4
g_vssRandSeed:	.type	object
	.size	g_vssRandSeed,32
	.byte	35,84,223,171
	.byte	137
	.space	1
	.byte	18,98,191,67,132,39,1,237
	.byte	188,187,255,254,223,119,37,131
	.byte	113,86,144,16,73,202,167,216
	.byte	177,221
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssLastRand')
	.sect	'.vss_api_data_ROM'
	.global	g_vssLastRand
g_vssLastRand:	.type	object
	.size	g_vssLastRand,1
	.space	1
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssIocb')
	.sect	'.vss_api_data_ROM'
	.global	g_vssIocb
	.align	4
g_vssIocb:	.type	object
	.size	g_vssIocb,4
	.space	4
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssWdtcb')
	.sect	'.vss_api_data_ROM'
	.global	g_vssWdtcb
	.align	4
g_vssWdtcb:	.type	object
	.size	g_vssWdtcb,4
	.space	4
	.sdecl	'.vss_api_data_ROM',data,cluster('g_cVssAlg')
	.sect	'.vss_api_data_ROM'
	.global	g_cVssAlg
g_cVssAlg:	.type	object
	.size	g_cVssAlg,1
	.byte	1
	.sdecl	'.vss_api_data_ROM',data,cluster('g_cVssEnv')
	.sect	'.vss_api_data_ROM'
	.global	g_cVssEnv
g_cVssEnv:	.type	object
	.size	g_cVssEnv,1
	.byte	1
	.sdecl	'.vss_api_data_ROM',data,cluster('g_cVssChip')
	.sect	'.vss_api_data_ROM'
	.global	g_cVssChip
g_cVssChip:	.type	object
	.size	g_cVssChip,1
	.space	1
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssRootCert')
	.sect	'.vss_api_data_ROM'
	.global	g_vssRootCert
	.align	4
g_vssRootCert:	.type	object
	.size	g_vssRootCert,144
	.space	144
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssUserCert')
	.sect	'.vss_api_data_ROM'
	.global	g_vssUserCert
	.align	4
g_vssUserCert:	.type	object
	.size	g_vssUserCert,176
	.space	176
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssAsymmKey')
	.sect	'.vss_api_data_ROM'
	.global	g_vssAsymmKey
	.align	4
g_vssAsymmKey:	.type	object
	.size	g_vssAsymmKey,96
	.space	96
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssSymmKey')
	.sect	'.vss_api_data_ROM'
	.global	g_vssSymmKey
	.align	4
g_vssSymmKey:	.type	object
	.size	g_vssSymmKey,448
	.space	448
	.sdecl	'.vss_api_data_ROM',data,cluster('g_vssSessKey')
	.sect	'.vss_api_data_ROM'
	.global	g_vssSessKey
	.align	4
g_vssSessKey:	.type	object
	.size	g_vssSessKey,64
	.space	64
	.sdecl	'.vss_api_data_ROM',data,cluster('g_someip_io_cb')
	.sect	'.vss_api_data_ROM'
	.global	g_someip_io_cb
	.align	4
g_someip_io_cb:	.type	object
	.size	g_someip_io_cb,4
	.space	4
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1269
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	175
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	181
	.byte	5,1,3
	.word	205
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	207
.L98:
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'vss_uint8',0,2,8,24
	.word	230
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'vss_uint32',0,2,13,24
	.word	265
	.byte	6
	.byte	'long int',0,4,5,4
	.byte	'vss_slong',0,2,15,17
	.word	305
	.byte	6
	.byte	'unsigned long long int',0,8,7,4
	.byte	'vss_uint64',0,2,17,28
	.word	335
	.byte	4
	.byte	'vss_ulong',0,2,18,24
	.word	265
	.byte	4
	.byte	'BYTE',0,2,21,22
	.word	230
	.byte	4
	.byte	'WORD',0,2,22,22
	.word	265
	.byte	4
	.byte	'vss_size',0,2,24,21
	.word	265
	.byte	4
	.byte	'SM3_WORD_T',0,2,25,22
	.word	265
	.byte	7,2,27,9,168,1,8
	.byte	'm_size',0,4
	.word	265
	.byte	2,35,0,9,128,1
	.word	230
	.byte	10,127,0,8
	.byte	'remain',0,128,1
	.word	482
	.byte	2,35,4,8
	.byte	'r_len',0,4
	.word	265
	.byte	3,35,132,1,9,32
	.word	265
	.byte	10,7,0,8
	.byte	'iv',0,32
	.word	525
	.byte	3,35,136,1,0,4
	.byte	'SM3_CTX_T',0,2,32,3
	.word	460
	.byte	7,2,34,9,108,9,64
	.word	230
	.byte	10,63,0,8
	.byte	'data',0,64
	.word	571
	.byte	2,35,0,8
	.byte	'datalen',0,4
	.word	265
	.byte	2,35,64,8
	.byte	'bitlen',0,8
	.word	335
	.byte	2,35,68,9,32
	.word	265
	.byte	10,7,0,8
	.byte	'state',0,32
	.word	627
	.byte	2,35,76,0,4
	.byte	'SHA256_CTX',0,2,39,3
	.word	566
	.byte	11
	.word	265
	.byte	1,12
	.word	265
	.byte	12
	.word	265
	.byte	3
	.word	230
	.byte	12
	.word	687
	.byte	12
	.word	265
	.byte	0,4
	.byte	'flash_io_cb',0,2,56,20
	.word	671
	.byte	13
	.word	265
	.byte	1,4
	.byte	'wdt_rst_cb',0,2,58,20
	.word	723
	.byte	11
	.word	265
	.byte	1,12
	.word	265
	.byte	12
	.word	687
	.byte	12
	.word	265
	.byte	12
	.word	265
	.byte	0,4
	.byte	'someip_io_cb',0,2,72,20
	.word	748
	.byte	6
	.byte	'unsigned int',0,4,7,4
	.byte	'mbedtls_mpi_uint',0,3,109,22
	.word	796
.L97:
	.byte	14
	.byte	'mbedtls_mpi',0,3,123,16,12,8
	.byte	's',0,4
	.word	305
	.byte	2,35,0,8
	.byte	'n',0,4
	.word	265
	.byte	2,35,4,3
	.word	796
	.byte	8
	.byte	'p',0,4
	.word	876
	.byte	2,35,8,0,4
	.byte	'mbedtls_mpi',0,3,129,1,1
	.word	837
	.byte	9,72
	.word	230
	.byte	10,71,0,9,176,3
	.word	914
	.byte	10,5,0
.L78:
	.byte	15
	.word	923
.L99:
	.byte	9,32
	.word	230
	.byte	10,31,0,9,128,3
	.word	938
	.byte	10,11,0
.L79:
	.byte	15
	.word	947
	.byte	9,6
	.word	230
	.byte	10,5,0
.L80:
	.byte	15
	.word	962
	.byte	9,12
	.word	230
	.byte	10,11,0
.L81:
	.byte	15
	.word	976
	.byte	9,128,2
	.word	796
	.byte	10,63,0
.L82:
	.byte	9,128,8
	.word	990
	.byte	10,3,0
.L83:
	.byte	15
	.word	837
.L84:
	.byte	15
	.word	837
.L85:
	.byte	15
	.word	837
.L86:
	.byte	15
	.word	265
.L87:
	.byte	15
	.word	837
.L88:
	.byte	15
	.word	265
.L89:
	.byte	15
	.word	837
.L90:
	.byte	15
	.word	265
.L91:
	.byte	15
	.word	837
	.byte	9,130,1
	.word	230
	.byte	10,129,1,0
.L92:
	.byte	15
	.word	1055
	.byte	9,32
	.word	265
	.byte	10,7,0
.L93:
	.byte	15
	.word	1071
.L94:
	.byte	15
	.word	1071
.L95:
	.byte	15
	.word	1071
.L96:
	.byte	9,32
	.word	796
	.byte	10,7,0,16
	.word	265
	.byte	1,1,12
	.word	265
	.byte	12
	.word	265
	.byte	12
	.word	687
	.byte	12
	.word	265
	.byte	0
.L100:
	.byte	3
	.word	1104
	.byte	17
	.word	265
	.byte	1,1
.L101:
	.byte	3
	.word	1137
.L102:
	.byte	15
	.word	230
.L103:
	.byte	15
	.word	230
.L104:
	.byte	15
	.word	230
	.byte	9,144,1
	.word	230
	.byte	10,143,1,0
.L105:
	.byte	15
	.word	1164
	.byte	9,176,1
	.word	230
	.byte	10,175,1,0
.L106:
	.byte	15
	.word	1180
	.byte	9,96
	.word	230
	.byte	10,95,0
.L107:
	.byte	15
	.word	1196
	.byte	9,192,3
	.word	938
	.byte	10,13,0
.L108:
	.byte	15
	.word	1210
	.byte	9,64
	.word	938
	.byte	10,1,0
.L109:
	.byte	15
	.word	1225
	.byte	16
	.word	265
	.byte	1,1,12
	.word	265
	.byte	12
	.word	687
	.byte	12
	.word	265
	.byte	12
	.word	265
	.byte	0
.L110:
	.byte	3
	.word	1239
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,58,15,59,15,57,15,11,15,0,0,8,13,0,3,8
	.byte	11,15,73,19,56,9,0,0,9,1,1,11,15,73,19,0,0,10,33,0,47,15,0,0,11,21,1,73,19,39,12,0,0,12,5,0,73,19,0,0
	.byte	13,21,0,73,19,39,12,0,0,14,19,1,3,8,58,15,59,15,57,15,11,15,0,0,15,53,0,73,19,0,0,16,21,1,73,19,54,15
	.byte	39,12,0,0,17,21,0,73,19,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L112-.L111
.L111:
	.half	3
	.word	.L114-.L113
.L113:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vssvar.c',0,0,0,0
	.byte	'..\\vss_code\\vsstype.h',0,0,0,0
	.byte	'..\\vss_code\\bignum.h',0,0,0,0,0
.L114:
.L112:
	.sdecl	'.debug_info',debug,cluster('g_vssBnMem72')
	.sect	'.debug_info'
.L6:
	.word	201
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssBnMem72',0,1,17,29
	.word	.L78
	.byte	1,5,3
	.word	g_vssBnMem72
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssBnMem72')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssBnMem32')
	.sect	'.debug_info'
.L8:
	.word	201
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssBnMem32',0,1,18,29
	.word	.L79
	.byte	1,5,3
	.word	g_vssBnMem32
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssBnMem32')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssCflagMem72')
	.sect	'.debug_info'
.L10:
	.word	204
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssCflagMem72',0,1,19,29
	.word	.L80
	.byte	1,5,3
	.word	g_vssCflagMem72
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssCflagMem72')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssCflagMem32')
	.sect	'.debug_info'
.L12:
	.word	204
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssCflagMem32',0,1,20,29
	.word	.L81
	.byte	1,5,3
	.word	g_vssCflagMem32
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssCflagMem32')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_bn_dp')
	.sect	'.debug_info'
.L14:
	.word	198
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_bn_dp',0,1,21,27
	.word	.L82
	.byte	1,5,3
	.word	vss_bn_dp
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_bn_dp')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_op1')
	.sect	'.debug_info'
.L16:
	.word	199
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_op1',0,1,23,31
	.word	.L83
	.byte	1,5,3
	.word	vss_mc_op1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_op1')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_op2')
	.sect	'.debug_info'
.L18:
	.word	199
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_op2',0,1,24,31
	.word	.L84
	.byte	1,5,3
	.word	vss_mc_op2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_op2')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_opp')
	.sect	'.debug_info'
.L20:
	.word	199
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_opp',0,1,25,31
	.word	.L85
	.byte	1,5,3
	.word	vss_mc_opp
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_opp')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_len')
	.sect	'.debug_info'
.L22:
	.word	199
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_len',0,1,26,30
	.word	.L86
	.byte	1,5,3
	.word	vss_mc_len
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_len')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_res')
	.sect	'.debug_info'
.L24:
	.word	199
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_res',0,1,27,31
	.word	.L87
	.byte	1,5,3
	.word	vss_mc_res
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_res')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_len_tmp1')
	.sect	'.debug_info'
.L26:
	.word	204
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_len_tmp1',0,1,28,30
	.word	.L88
	.byte	1,5,3
	.word	vss_mc_len_tmp1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_len_tmp1')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_tmp1')
	.sect	'.debug_info'
.L28:
	.word	200
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_tmp1',0,1,29,31
	.word	.L89
	.byte	1,5,3
	.word	vss_mc_tmp1
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_tmp1')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_len_tmp2')
	.sect	'.debug_info'
.L30:
	.word	204
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_len_tmp2',0,1,30,30
	.word	.L90
	.byte	1,5,3
	.word	vss_mc_len_tmp2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_len_tmp2')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('vss_mc_tmp2')
	.sect	'.debug_info'
.L32:
	.word	200
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'vss_mc_tmp2',0,1,31,31
	.word	.L91
	.byte	1,5,3
	.word	vss_mc_tmp2
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('vss_mc_tmp2')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('k')
	.sect	'.debug_info'
.L34:
	.word	190
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'k',0,1,33,29
	.word	.L92
	.byte	1,5,3
	.word	k
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('k')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('RR_x')
	.sect	'.debug_info'
.L36:
	.word	193
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'RR_x',0,1,34,30
	.word	.L93
	.byte	1,5,3
	.word	RR_x
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('RR_x')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('RR_y')
	.sect	'.debug_info'
.L38:
	.word	193
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'RR_y',0,1,35,30
	.word	.L94
	.byte	1,5,3
	.word	RR_y
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('RR_y')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('RR_z')
	.sect	'.debug_info'
.L40:
	.word	193
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'RR_z',0,1,36,30
	.word	.L95
	.byte	1,5,3
	.word	RR_z
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('RR_z')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_p_data')
	.sect	'.debug_info'
.L42:
	.word	199
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_p_data',0,1,38,27
	.word	.L96
	.byte	1,5,3
	.word	sm2_p_data
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_p_data')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_p')
	.sect	'.debug_info'
.L44:
	.word	194
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_p',0,1,48,22
	.word	.L97
	.byte	1,5,3
	.word	sm2_p
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_p')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('sm2_kdf_len')
	.sect	'.debug_info'
.L46:
	.word	200
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'sm2_kdf_len',0,1,49,20
	.word	.L98
	.byte	1,5,3
	.word	sm2_kdf_len
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('sm2_kdf_len')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('nist256_p_data')
	.sect	'.debug_info'
.L48:
	.word	203
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'nist256_p_data',0,1,51,27
	.word	.L96
	.byte	1,5,3
	.word	nist256_p_data
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('nist256_p_data')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('nist256_p')
	.sect	'.debug_info'
.L50:
	.word	198
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'nist256_p',0,1,61,22
	.word	.L97
	.byte	1,5,3
	.word	nist256_p
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('nist256_p')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssRandSeed')
	.sect	'.debug_info'
.L52:
	.word	202
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssRandSeed',0,1,64,20
	.word	.L99
	.byte	1,5,3
	.word	g_vssRandSeed
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssRandSeed')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssLastRand')
	.sect	'.debug_info'
.L54:
	.word	202
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssLastRand',0,1,70,20
	.word	.L98
	.byte	1,5,3
	.word	g_vssLastRand
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssLastRand')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssIocb')
	.sect	'.debug_info'
.L56:
	.word	198
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssIocb',0,1,73,23
	.word	.L100
	.byte	1,5,3
	.word	g_vssIocb
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssIocb')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssWdtcb')
	.sect	'.debug_info'
.L58:
	.word	199
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssWdtcb',0,1,74,22
	.word	.L101
	.byte	1,5,3
	.word	g_vssWdtcb
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssWdtcb')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_cVssAlg')
	.sect	'.debug_info'
.L60:
	.word	198
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_cVssAlg',0,1,75,29
	.word	.L102
	.byte	1,5,3
	.word	g_cVssAlg
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_cVssAlg')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_cVssEnv')
	.sect	'.debug_info'
.L62:
	.word	198
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_cVssEnv',0,1,76,29
	.word	.L103
	.byte	1,5,3
	.word	g_cVssEnv
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_cVssEnv')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_cVssChip')
	.sect	'.debug_info'
.L64:
	.word	199
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_cVssChip',0,1,77,29
	.word	.L104
	.byte	1,5,3
	.word	g_cVssChip
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_cVssChip')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssRootCert')
	.sect	'.debug_info'
.L66:
	.word	202
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssRootCert',0,1,79,29
	.word	.L105
	.byte	1,5,3
	.word	g_vssRootCert
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssRootCert')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssUserCert')
	.sect	'.debug_info'
.L68:
	.word	202
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssUserCert',0,1,80,29
	.word	.L106
	.byte	1,5,3
	.word	g_vssUserCert
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssUserCert')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssAsymmKey')
	.sect	'.debug_info'
.L70:
	.word	202
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssAsymmKey',0,1,81,29
	.word	.L107
	.byte	1,5,3
	.word	g_vssAsymmKey
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssAsymmKey')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssSymmKey')
	.sect	'.debug_info'
.L72:
	.word	201
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssSymmKey',0,1,82,29
	.word	.L108
	.byte	1,5,3
	.word	g_vssSymmKey
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssSymmKey')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_vssSessKey')
	.sect	'.debug_info'
.L74:
	.word	201
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_vssSessKey',0,1,83,29
	.word	.L109
	.byte	1,5,3
	.word	g_vssSessKey
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_vssSessKey')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('g_someip_io_cb')
	.sect	'.debug_info'
.L76:
	.word	203
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'..\\vss_code\\vssvar.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'g_someip_io_cb',0,1,85,24
	.word	.L110
	.byte	1,5,3
	.word	g_someip_io_cb
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('g_someip_io_cb')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0

; ..\vss_code\vssvar.c	     1  
; ..\vss_code\vssvar.c	     2  #include "bignum.h"
; ..\vss_code\vssvar.c	     3  #include "vsstype.h"
; ..\vss_code\vssvar.c	     4  #include "vsskeym.h"
; ..\vss_code\vssvar.c	     5  #include "vssconf.h"
; ..\vss_code\vssvar.c	     6  #include "vssapi.h"
; ..\vss_code\vssvar.c	     7  
; ..\vss_code\vssvar.c	     8  #define VSS_RAM          __attribute__((section(".vss_api_data_ROM"), used))
; ..\vss_code\vssvar.c	     9  
; ..\vss_code\vssvar.c	    10  
; ..\vss_code\vssvar.c	    11  
; ..\vss_code\vssvar.c	    12  
; ..\vss_code\vssvar.c	    13  #pragma section code "vss_api_data_ROM"
; ..\vss_code\vssvar.c	    14  
; ..\vss_code\vssvar.c	    15  #define VSS_CACHE72_NUM 6
; ..\vss_code\vssvar.c	    16  #define VSS_CACHE32_NUM 12
; ..\vss_code\vssvar.c	    17   VSS_RAM volatile vss_uint8 g_vssBnMem72[VSS_CACHE72_NUM][72] = {0};
; ..\vss_code\vssvar.c	    18   VSS_RAM volatile vss_uint8 g_vssBnMem32[VSS_CACHE32_NUM][32] = {0};
; ..\vss_code\vssvar.c	    19   VSS_RAM volatile vss_uint8 g_vssCflagMem72[VSS_CACHE72_NUM] = {0};
; ..\vss_code\vssvar.c	    20   VSS_RAM volatile vss_uint8 g_vssCflagMem32[VSS_CACHE32_NUM] = {0};
; ..\vss_code\vssvar.c	    21   VSS_RAM mbedtls_mpi_uint vss_bn_dp[4][64] = {0};
; ..\vss_code\vssvar.c	    22  
; ..\vss_code\vssvar.c	    23   VSS_RAM volatile mbedtls_mpi vss_mc_op1 = {1,0,VSS_NULL};
; ..\vss_code\vssvar.c	    24   VSS_RAM volatile mbedtls_mpi vss_mc_op2 = {1,0,VSS_NULL};
; ..\vss_code\vssvar.c	    25   VSS_RAM volatile mbedtls_mpi vss_mc_opp = {1,0,VSS_NULL};
; ..\vss_code\vssvar.c	    26   VSS_RAM volatile vss_uint32 vss_mc_len = 0;
; ..\vss_code\vssvar.c	    27   VSS_RAM volatile mbedtls_mpi vss_mc_res = {1,0,vss_bn_dp[0]};
; ..\vss_code\vssvar.c	    28   VSS_RAM volatile vss_uint32 vss_mc_len_tmp1 = 0;
; ..\vss_code\vssvar.c	    29   VSS_RAM volatile mbedtls_mpi vss_mc_tmp1 = {1,0,vss_bn_dp[1]};
; ..\vss_code\vssvar.c	    30   VSS_RAM volatile vss_uint32 vss_mc_len_tmp2 = 0;
; ..\vss_code\vssvar.c	    31   VSS_RAM volatile mbedtls_mpi vss_mc_tmp2 = {1,0,vss_bn_dp[2]};
; ..\vss_code\vssvar.c	    32  
; ..\vss_code\vssvar.c	    33   VSS_RAM volatile vss_uint8 k[129 + 1];
; ..\vss_code\vssvar.c	    34   VSS_RAM volatile vss_uint32 RR_x[8];
; ..\vss_code\vssvar.c	    35   VSS_RAM volatile vss_uint32 RR_y[8];
; ..\vss_code\vssvar.c	    36   VSS_RAM volatile vss_uint32 RR_z[8];
; ..\vss_code\vssvar.c	    37  
; ..\vss_code\vssvar.c	    38   VSS_RAM mbedtls_mpi_uint sm2_p_data[8] = {
; ..\vss_code\vssvar.c	    39  	0xFFFFFFFF,
; ..\vss_code\vssvar.c	    40  	0xFFFFFFFF,
; ..\vss_code\vssvar.c	    41  	0x00000000,
; ..\vss_code\vssvar.c	    42  	0xFFFFFFFF, 
; ..\vss_code\vssvar.c	    43  	0xFFFFFFFF, 
; ..\vss_code\vssvar.c	    44  	0xFFFFFFFF, 
; ..\vss_code\vssvar.c	    45  	0xFFFFFFFF,
; ..\vss_code\vssvar.c	    46  	0xFFFFFFFE
; ..\vss_code\vssvar.c	    47  };
; ..\vss_code\vssvar.c	    48   VSS_RAM mbedtls_mpi sm2_p= {1,8,&sm2_p_data[0]};
; ..\vss_code\vssvar.c	    49   VSS_RAM vss_uint8 sm2_kdf_len = 0;
; ..\vss_code\vssvar.c	    50  
; ..\vss_code\vssvar.c	    51   VSS_RAM mbedtls_mpi_uint nist256_p_data[8] = {
; ..\vss_code\vssvar.c	    52  	0xFFFFFFFF,
; ..\vss_code\vssvar.c	    53  	0xFFFFFFFF,
; ..\vss_code\vssvar.c	    54  	0xFFFFFFFF,
; ..\vss_code\vssvar.c	    55  	0x00000000, 
; ..\vss_code\vssvar.c	    56  	0x00000000, 
; ..\vss_code\vssvar.c	    57  	0x00000000, 
; ..\vss_code\vssvar.c	    58  	0x00000001,
; ..\vss_code\vssvar.c	    59  	0xFFFFFFFF
; ..\vss_code\vssvar.c	    60  };
; ..\vss_code\vssvar.c	    61   VSS_RAM mbedtls_mpi nist256_p= {1,8,&nist256_p_data[0]};
; ..\vss_code\vssvar.c	    62  
; ..\vss_code\vssvar.c	    63  
; ..\vss_code\vssvar.c	    64   VSS_RAM vss_uint8 g_vssRandSeed[32] = {
; ..\vss_code\vssvar.c	    65  	0x23, 0x54, 0xDF, 0xAB, 0x89, 0x00, 0x12, 0x62, 
; ..\vss_code\vssvar.c	    66  	0xBF, 0x43, 0x84, 0x27, 0x01, 0xED, 0xBC, 0xBB, 
; ..\vss_code\vssvar.c	    67  	0xFF, 0xFE, 0xDF, 0x77, 0x25, 0x83, 0x71, 0x56, 
; ..\vss_code\vssvar.c	    68  	0x90, 0x10, 0x49, 0xCA, 0xA7, 0xD8, 0xB1, 0xDD, 
; ..\vss_code\vssvar.c	    69  };
; ..\vss_code\vssvar.c	    70   VSS_RAM vss_uint8 g_vssLastRand = 0;
; ..\vss_code\vssvar.c	    71  
; ..\vss_code\vssvar.c	    72  
; ..\vss_code\vssvar.c	    73   VSS_RAM flash_io_cb* g_vssIocb = VSS_NULL;
; ..\vss_code\vssvar.c	    74   VSS_RAM wdt_rst_cb* g_vssWdtcb = VSS_NULL;
; ..\vss_code\vssvar.c	    75   VSS_RAM volatile vss_uint8 g_cVssAlg = 1;
; ..\vss_code\vssvar.c	    76   VSS_RAM volatile vss_uint8 g_cVssEnv = 1;
; ..\vss_code\vssvar.c	    77   VSS_RAM volatile vss_uint8 g_cVssChip = 0;
; ..\vss_code\vssvar.c	    78  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vssvar.c	    79   VSS_RAM volatile vss_uint8 g_vssRootCert[ROOT_CERT_SIZE] = {0};
; ..\vss_code\vssvar.c	    80   VSS_RAM volatile vss_uint8 g_vssUserCert[USER_CERT_SIZE] = {0};
; ..\vss_code\vssvar.c	    81   VSS_RAM volatile vss_uint8 g_vssAsymmKey[ASYMM_KEY_LEN] = {0};
; ..\vss_code\vssvar.c	    82   VSS_RAM volatile vss_uint8 g_vssSymmKey[SYMM_KEY_NUM][SYMM_CIPHER_LEN] = {0};
; ..\vss_code\vssvar.c	    83   VSS_RAM volatile vss_uint8 g_vssSessKey[SESS_KEY_NUM][SYMM_CIPHER_LEN] = {0};
; ..\vss_code\vssvar.c	    84  #endif
; ..\vss_code\vssvar.c	    85   VSS_RAM someip_io_cb* g_someip_io_cb = VSS_NULL;
; ..\vss_code\vssvar.c	    86  
; ..\vss_code\vssvar.c	    87  
; ..\vss_code\vssvar.c	    88  #pragma section code restore
; ..\vss_code\vssvar.c	    89  
; ..\vss_code\vssvar.c	    90  

	; Module end
