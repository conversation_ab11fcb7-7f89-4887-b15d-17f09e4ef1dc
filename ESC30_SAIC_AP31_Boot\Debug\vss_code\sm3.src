	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc11644a -c99 --dep-file=vss_code\\.sm3.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=vss_code\\sm3.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o vss_code\\sm3.src ..\\vss_code\\sm3.c"
	.compiler_name		"ctc"
	.name	"sm3"

	
$TC16X
	
	.sdecl	'.text.vss_api_code',code,cluster('T')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	T

; ..\vss_code\sm3.c	     1  
; ..\vss_code\sm3.c	     2  #include "sm3.h"
; ..\vss_code\sm3.c	     3  #include "vsscommon.h"
; ..\vss_code\sm3.c	     4  
; ..\vss_code\sm3.c	     5  
; ..\vss_code\sm3.c	     6  
; ..\vss_code\sm3.c	     7  #pragma section code "vss_api_code" 
; ..\vss_code\sm3.c	     8  
; ..\vss_code\sm3.c	     9  /***************************************************************************************
; ..\vss_code\sm3.c	    10   *
; ..\vss_code\sm3.c	    11   *	��1����: 
; ..\vss_code\sm3.c	    12   *	��ݡ�SM3�����Ӵ��㷨��ʵ�ָ��½ڶ�����㷨�߼�
; ..\vss_code\sm3.c	    13   *
; ..\vss_code\sm3.c	    14   *	!!!! ע�⣬��ݹ淶����������еġ��֡�Ϊ32λ��4�ֽڣ�������ͨ��x86�ܹ������е�16λ��2�ֽڣ�
; ..\vss_code\sm3.c	    15   *
; ..\vss_code\sm3.c	    16   ***************************************************************************************/
; ..\vss_code\sm3.c	    17  #if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U)) 
; ..\vss_code\sm3.c	    18  
; ..\vss_code\sm3.c	    19  #else
; ..\vss_code\sm3.c	    20  	#ifndef __LITTLE_ENDIAN
; ..\vss_code\sm3.c	    21  		#define __LITTLE_ENDIAN
; ..\vss_code\sm3.c	    22  	#endif
; ..\vss_code\sm3.c	    23  #endif
; ..\vss_code\sm3.c	    24  
; ..\vss_code\sm3.c	    25  #if (defined (_ENABLE_MIZAR_SM3_)&&(_ENABLE_MIZAR_SM3_ == 1U)) || \ 
; ..\vss_code\sm3.c	    26  	(defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
; ..\vss_code\sm3.c	    27  
; ..\vss_code\sm3.c	    28  /*	4.1 ��ʼֵ			*/
; ..\vss_code\sm3.c	    29  extern const SM3_WORD_T SM3_IV[8];
; ..\vss_code\sm3.c	    30  
; ..\vss_code\sm3.c	    31  /*	4.2 ����			*/
; ..\vss_code\sm3.c	    32  SM3_WORD_T T(vss_uint32 j)
; Function T
.L45:
T:	.type	func

; ..\vss_code\sm3.c	    33  {
; ..\vss_code\sm3.c	    34  	if(j <= 15)
	mov	d15,#15
.L365:
	jlt.u	d15,d4,.L2
.L366:

; ..\vss_code\sm3.c	    35  	{ 
; ..\vss_code\sm3.c	    36  		return 0x79cc4519; 
	mov	d2,#17689
.L367:

; ..\vss_code\sm3.c	    37  	}
; ..\vss_code\sm3.c	    38  	else{ 
; ..\vss_code\sm3.c	    39  		if((j >= 16) && (j <= 63)) 
; ..\vss_code\sm3.c	    40  		{ 
; ..\vss_code\sm3.c	    41  			return 0x7a879d8a; 
; ..\vss_code\sm3.c	    42  		}else {
; ..\vss_code\sm3.c	    43  			return 0;
; ..\vss_code\sm3.c	    44  		}
; ..\vss_code\sm3.c	    45  	}
; ..\vss_code\sm3.c	    46  }
	addih	d2,d2,#31180
	ret
.L2:
	mov	d15,#63
.L368:
	lt.u	d15,d15,d4
.L369:
	mov.u	d2,#40330
	addih	d2,d2,#31367
.L370:
	cmov	d2,d15,#0
	ret
.L168:
	
__T_function_end:
	.size	T,__T_function_end-T
.L100:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('FF')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	FF

; ..\vss_code\sm3.c	    47  
; ..\vss_code\sm3.c	    48  /*	4.3 �������Ӻ���	*/
; ..\vss_code\sm3.c	    49  #define FG(x,y,z)		(x ^ y ^ z)
; ..\vss_code\sm3.c	    50  #define F1(x,y,z)		((x & y) | (y & z) | (x & z))
; ..\vss_code\sm3.c	    51  #define G1(x,y,z)		((x & y) | (~x & z))
; ..\vss_code\sm3.c	    52  
; ..\vss_code\sm3.c	    53  SM3_WORD_T FF(SM3_WORD_T j, SM3_WORD_T x, SM3_WORD_T y, SM3_WORD_T z)
; Function FF
.L47:
FF:	.type	func

; ..\vss_code\sm3.c	    54  {
; ..\vss_code\sm3.c	    55  	if(j <= 15){
	mov	d15,#15
.L375:
	jlt.u	d15,d4,.L7
.L376:

; ..\vss_code\sm3.c	    56  		return FG(x,y,z);
	xor	d5,d6
.L231:

; ..\vss_code\sm3.c	    57  	}else{
; ..\vss_code\sm3.c	    58  		if(j >= 16 && j <= 63){	
; ..\vss_code\sm3.c	    59  			return F1(x,y,z);
; ..\vss_code\sm3.c	    60  		}else{
; ..\vss_code\sm3.c	    61  			return 0;
; ..\vss_code\sm3.c	    62  		}
; ..\vss_code\sm3.c	    63  	}
; ..\vss_code\sm3.c	    64  }
	xor	d2,d5,d7
	ret
.L7:
	mov	d15,#63
.L377:
	jlt.u	d15,d4,.L9
.L378:
	or	d15,d5,d7
	and	d6,d15
.L232:
	and	d5,d7
.L233:
	or	d2,d6,d5
	ret
.L9:
	mov	d2,#0
	ret
.L170:
	
__FF_function_end:
	.size	FF,__FF_function_end-FF
.L105:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('GG')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GG

; ..\vss_code\sm3.c	    65  
; ..\vss_code\sm3.c	    66  SM3_WORD_T GG(SM3_WORD_T j, SM3_WORD_T x, SM3_WORD_T y, SM3_WORD_T z)
; Function GG
.L49:
GG:	.type	func

; ..\vss_code\sm3.c	    67  {
; ..\vss_code\sm3.c	    68  	if( j <= 15){
	mov	d15,#15
.L383:
	jlt.u	d15,d4,.L12
.L384:

; ..\vss_code\sm3.c	    69  		return FG(x,y,z);
	xor	d5,d6
.L234:

; ..\vss_code\sm3.c	    70  	}else{
; ..\vss_code\sm3.c	    71  		if(j >= 16 && j <= 63){	
; ..\vss_code\sm3.c	    72  			 return  G1(x,y,z);
; ..\vss_code\sm3.c	    73  		}else{
; ..\vss_code\sm3.c	    74  			return 0;
; ..\vss_code\sm3.c	    75  		}
; ..\vss_code\sm3.c	    76  	}
; ..\vss_code\sm3.c	    77  }
	xor	d2,d5,d7
	ret
.L12:
	mov	d15,#63
.L385:
	jlt.u	d15,d4,.L14
.L386:
	and	d6,d5
.L235:
	mov	d15,#-1
	xor	d5,d15
.L236:
	and	d5,d7
.L387:
	or	d2,d6,d5
	ret
.L14:
	mov	d2,#0
	ret
.L175:
	
__GG_function_end:
	.size	GG,__GG_function_end-GG
.L110:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_lshift')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm3.c	    78  
; ..\vss_code\sm3.c	    79  /*	4.4 �û�����P0		*/
; ..\vss_code\sm3.c	    80  #define P0(x)			(x ^ (sm3_lshift(x,9))  ^ (sm3_lshift(x,17)))
; ..\vss_code\sm3.c	    81  #define P1(x)			(x ^ (sm3_lshift(x,15)) ^ (sm3_lshift(x,23)))
; ..\vss_code\sm3.c	    82  
; ..\vss_code\sm3.c	    83  
; ..\vss_code\sm3.c	    84  
; ..\vss_code\sm3.c	    85  
; ..\vss_code\sm3.c	    86  /*	��Ե��ֵ�ѭ������	*/
; ..\vss_code\sm3.c	    87  static SM3_WORD_T sm3_lshift(SM3_WORD_T num, vss_uint32 bits)
; Function sm3_lshift
.L51:
sm3_lshift:	.type	func

; ..\vss_code\sm3.c	    88  {
; ..\vss_code\sm3.c	    89  	SM3_WORD_T rt;
; ..\vss_code\sm3.c	    90  	while(bits > 32)	bits -= 32;
	mov	d15,#32
.L392:
	lt.u	d15,d15,d5
	sh	d15,#5
.L393:
	sub	d5,d15
.L394:

; ..\vss_code\sm3.c	    91  	rt = (num >> (32 - bits) | (num << bits));
	rsub	d15,d5,#32
.L395:
	rsub	d15,#0
	sh	d2,d4,d15
.L396:
	sh	d4,d4,d5
.L237:

; ..\vss_code\sm3.c	    92  	return rt;
; ..\vss_code\sm3.c	    93  }
	or	d2,d4
	ret
.L180:
	
__sm3_lshift_function_end:
	.size	sm3_lshift,__sm3_lshift_function_end-sm3_lshift
.L115:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_rot')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm3.c	    94  
; ..\vss_code\sm3.c	    95  /*	��Ե��ֵĴ�С��ת��	*/
; ..\vss_code\sm3.c	    96  #ifdef __LITTLE_ENDIAN
; ..\vss_code\sm3.c	    97  static SM3_WORD_T sm3_rot(SM3_WORD_T num)
; Function sm3_rot
.L53:
sm3_rot:	.type	func
	sub.a	a10,#8
.L238:
	st.w	[a10],d4
.L401:

; ..\vss_code\sm3.c	    98  {
; ..\vss_code\sm3.c	    99  #ifdef __LITTLE_ENDIAN
; ..\vss_code\sm3.c	   100  	vss_uint32 i = 0;
; ..\vss_code\sm3.c	   101  	SM3_WORD_T num_b = 0;
	mov	d15,#0
	st.w	[a10]4,d15
.L402:

; ..\vss_code\sm3.c	   102  
; ..\vss_code\sm3.c	   103  	vss_uint8 *ln = (vss_uint8 *)(&num);
; ..\vss_code\sm3.c	   104  	vss_uint8 *bn = (vss_uint8 *)(&num_b);
; ..\vss_code\sm3.c	   105  
; ..\vss_code\sm3.c	   106  	for (i = 0; i < 4; i++)
; ..\vss_code\sm3.c	   107  	{
; ..\vss_code\sm3.c	   108  		bn[i] = ln[3-i];
	lea	a15,[a10]3
.L403:
	lea	a2,[a10]4
.L404:
	mov.a	a4,#3
.L18:
	ld.bu	d15,[a15+]-1
.L405:
	st.b	[a2+],d15
	loop	a4,.L18
.L406:

; ..\vss_code\sm3.c	   109  	}
; ..\vss_code\sm3.c	   110  
; ..\vss_code\sm3.c	   111  	return num_b;
	ld.w	d2,[a10]4
.L407:

; ..\vss_code\sm3.c	   112  #else
; ..\vss_code\sm3.c	   113  	return num;
; ..\vss_code\sm3.c	   114  #endif /* __LITTLE_ENDIAN */
; ..\vss_code\sm3.c	   115  }
	ret
.L183:
	
__sm3_rot_function_end:
	.size	sm3_rot,__sm3_rot_function_end-sm3_rot
.L120:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_rot_r')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm3.c	   116  #endif /* __LITTLE_ENDIAN */
; ..\vss_code\sm3.c	   117  
; ..\vss_code\sm3.c	   118  /*	���������Ĵ�С��ת��	*/
; ..\vss_code\sm3.c	   119  static void sm3_rot_r(const SM3_WORD_T* in, vss_uint32 count, SM3_WORD_T* out)
; Function sm3_rot_r
.L55:
sm3_rot_r:	.type	func

; ..\vss_code\sm3.c	   120  {
; ..\vss_code\sm3.c	   121  #ifdef __LITTLE_ENDIAN
; ..\vss_code\sm3.c	   122  	vss_uint32 i = 0;
; ..\vss_code\sm3.c	   123  	for (i = 0; i < count; i++) {
	mov	d15,d4
	mov.aa	a15,a4
.L239:
	mov	d8,#0
	mov.aa	a12,a5
.L20:

; ..\vss_code\sm3.c	   124  		out[i] = sm3_rot(in[i]);
	ld.w	d4,[a15+]
	call	sm3_rot
.L412:
	add	d8,#1
	st.w	[a12+],d2
.L413:
	jlt.u	d8,d15,.L20
.L414:

; ..\vss_code\sm3.c	   125  	}
; ..\vss_code\sm3.c	   126  #else
; ..\vss_code\sm3.c	   127  	mem_cpy8(out, in, count * 4);
; ..\vss_code\sm3.c	   128  #endif /* __LITTLE_ENDIAN */
; ..\vss_code\sm3.c	   129  }
	ret
.L187:
	
__sm3_rot_r_function_end:
	.size	sm3_rot_r,__sm3_rot_r_function_end-sm3_rot_r
.L125:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_padding')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm3.c	   130  
; ..\vss_code\sm3.c	   131  /**
; ..\vss_code\sm3.c	   132   *	5.2 ���
; ..\vss_code\sm3.c	   133   *	
; ..\vss_code\sm3.c	   134   *	������Ϣm�ĳ���Ϊmbits���ء����Ƚ�����1��ӵ���Ϣ��ĩβ�������k��0��
; ..\vss_code\sm3.c	   135   *	k������mbits + 1 + k = 448mod512 ����С�ķǸ�����
; ..\vss_code\sm3.c	   136   *	Ȼ�������һ��64λ���ش����ñ��ش��ǳ���mbits�Ķ����Ʊ�ʾ��
; ..\vss_code\sm3.c	   137   *	�������Ϣm'�ı��س���Ϊ512�ı���
; ..\vss_code\sm3.c	   138   *
; ..\vss_code\sm3.c	   139   *	��1������Ϣ01100001 01100010 01100011���䳤��mbits=24��k=448-1-24=443�������õ����ش���
; ..\vss_code\sm3.c	   140   *	                             {423����0}  {64����}
; ..\vss_code\sm3.c	   141   *	01100001 01100010 01100011 1 00......00  00 ... 000011000
; ..\vss_code\sm3.c	   142   *	                                         {24�Ķ����Ʊ�ʾ}
; ..\vss_code\sm3.c	   143   *
; ..\vss_code\sm3.c	   144   *	��2������Ϣ01100001 01100010 01100011���䳤��mbits=440��k=448-1-440=7�������õ����ش���
; ..\vss_code\sm3.c	   145   *	                             {7����0}    {64����}
; ..\vss_code\sm3.c	   146   *	01100001 01100010 01100011 1 00......00  00 ... 110111000
; ..\vss_code\sm3.c	   147   *	                                         {440�Ķ����Ʊ�ʾ}
; ..\vss_code\sm3.c	   148   *
; ..\vss_code\sm3.c	   149   *	��2������Ϣ00000000 ........ 00000000���䳤��mbits=504��k=512+448-1-504=455�������õ����ش���
; ..\vss_code\sm3.c	   150   *	                             {455����0}  {64����}
; ..\vss_code\sm3.c	   151   *	00000000 ........ 00000000 1 00......00  00 ... 111111000
; ..\vss_code\sm3.c	   152   *	                                         {504�Ķ����Ʊ�ʾ}
; ..\vss_code\sm3.c	   153   */
; ..\vss_code\sm3.c	   154   
; ..\vss_code\sm3.c	   155  static vss_uint32 sm3_padding(vss_uint32 m_bytes, vss_uint8* out)
; Function sm3_padding
.L57:
sm3_padding:	.type	func

; ..\vss_code\sm3.c	   156  {
; ..\vss_code\sm3.c	   157  	vss_uint32 k = 0;
; ..\vss_code\sm3.c	   158  	vss_uint32 m_bits = m_bytes * 8;
	sh	d8,d4,#3
	mov.aa	a15,a4
.L241:

; ..\vss_code\sm3.c	   159  	vss_uint32 mod_bits = m_bits % 512;
	and	d15,d8,#511
.L243:

; ..\vss_code\sm3.c	   160  	vss_uint8 *p = VSS_NULL;
; ..\vss_code\sm3.c	   161  	
; ..\vss_code\sm3.c	   162  	/*	�������k���ȣ�k = 448mod512 - 1 - mod_bits������Ϊm_bitsΪ8���������k����Ϊ0	*/
; ..\vss_code\sm3.c	   163  	if (mod_bits <= 447) {
	mov	d0,#447
.L419:
	jge.u	d0,d15,.L22
.L420:

; ..\vss_code\sm3.c	   164  		k = 447 - mod_bits;
; ..\vss_code\sm3.c	   165  	}
; ..\vss_code\sm3.c	   166  	else  {
; ..\vss_code\sm3.c	   167  		k = 512 + 447 - mod_bits;
	mov	d0,#959
.L22:

; ..\vss_code\sm3.c	   168  	}
; ..\vss_code\sm3.c	   169  	
; ..\vss_code\sm3.c	   170  	/*	���δָ���������ֻ���㳤�ȣ��ֽڣ�������	*/
; ..\vss_code\sm3.c	   171  	if (VSS_NULL == out) {
; ..\vss_code\sm3.c	   172  		return (m_bits + 1 + k + 64)/8;
; ..\vss_code\sm3.c	   173  	}
; ..\vss_code\sm3.c	   174  
; ..\vss_code\sm3.c	   175  	p = out;
; ..\vss_code\sm3.c	   176  
; ..\vss_code\sm3.c	   177  	/*	��Ϊ���Ǵ����m_bits����8�ı�����������ֱ����0x80�������1�������	*/
; ..\vss_code\sm3.c	   178  	*p = 0x80;
; ..\vss_code\sm3.c	   179  	p++;
	sub	d0,d15
	lea	a12,[a15]1
.L246:
	mov	d15,#128
	st.b	[a15],d15
.L244:

; ..\vss_code\sm3.c	   180  
; ..\vss_code\sm3.c	   181  	/*	�ٲ���(k/8)�ֽ�0		*/
; ..\vss_code\sm3.c	   182  	if ( (k/8) > 0 ) {
	sh	d15,d0,#-3
.L421:
	jeq	d15,#0,.L23
.L422:

; ..\vss_code\sm3.c	   183  		mem_set8(p, 0, k/8);
	mov	d4,#0
	mov.aa	a4,a12
.L240:
	mov	d5,d15
	call	mem_set8
.L245:

; ..\vss_code\sm3.c	   184  		p += k/8;
	addsc.a	a12,a12,d15,#0
.L23:

; ..\vss_code\sm3.c	   185  	}
; ..\vss_code\sm3.c	   186  
; ..\vss_code\sm3.c	   187  	/*	�ٲ���8�ֽ�(64����)���ȣ���m_bytesΪ32λ����£�ǰ4�ֽڹ̶�Ϊ0	*/
; ..\vss_code\sm3.c	   188  	mem_set8(p, 0, 4);
	mov	d4,#0
	mov.aa	a4,a12
.L247:
	mov	d5,#4
	call	mem_set8
.L248:

; ..\vss_code\sm3.c	   189  	p += 4;
	add.a	a12,#4
.L423:

; ..\vss_code\sm3.c	   190  
; ..\vss_code\sm3.c	   191  	*p++ = (vss_uint8)((m_bits & 0xFF000000) >> 24);
	sh	d15,d8,#-24
	st.b	[a12+],d15
.L424:

; ..\vss_code\sm3.c	   192  	*p++ = (vss_uint8)((m_bits & 0x00FF0000) >> 16);
	sh	d15,d8,#-16
	st.b	[a12+],d15
.L425:

; ..\vss_code\sm3.c	   193  	*p++ = (vss_uint8)((m_bits & 0x0000FF00) >> 8);
	sh	d15,d8,#-8
	st.b	[a12+],d15
.L426:

; ..\vss_code\sm3.c	   194  	*p++ = (vss_uint8)((m_bits & 0x000000FF));
	st.b	[a12+],d8
.L427:

; ..\vss_code\sm3.c	   195  
; ..\vss_code\sm3.c	   196  	/*	�����������Ϣ���ȣ��ֽڣ�����ֵӦ����64�ֽ�(512����)������	*/
; ..\vss_code\sm3.c	   197  	return p - out;
	sub.a	a15,a12,a15
.L242:
	mov.d	d2,a15
.L428:

; ..\vss_code\sm3.c	   198  }
	ret
.L194:
	
__sm3_padding_function_end:
	.size	sm3_padding,__sm3_padding_function_end-sm3_padding
.L130:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_extend')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm3.c	   199  
; ..\vss_code\sm3.c	   200  /**
; ..\vss_code\sm3.c	   201   *	5.3.2 ��Ϣ��չ
; ..\vss_code\sm3.c	   202   *
; ..\vss_code\sm3.c	   203   *	����Ϣ����B(i)�����·�����չ���132����W0, W1, ...W67, W��0, W��1, ...W��63
; ..\vss_code\sm3.c	   204   *	a)	����Ϣ����B(i)����Ϊ16����W0, W1, ...W15
; ..\vss_code\sm3.c	   205   *	b)	FOR j=16 TO 67
; ..\vss_code\sm3.c	   206   *			Wj = P1(Wj-16 ? Wj-9 ? (Wj-3 <<< 15)) ? (Wj-13 <<< 7) ? Wj-6
; ..\vss_code\sm3.c	   207   *		ENDFOR
; ..\vss_code\sm3.c	   208   *	c)	FOR j=0 TO 63
; ..\vss_code\sm3.c	   209   *			W��j = Wj ? Wj+4
; ..\vss_code\sm3.c	   210   *		ENDFOR
; ..\vss_code\sm3.c	   211   *	
; ..\vss_code\sm3.c	   212   *	ע1��?  ��ʾ32�����������
; ..\vss_code\sm3.c	   213   *	     <<< ��ʾѭ������k��������
; ..\vss_code\sm3.c	   214   */
; ..\vss_code\sm3.c	   215  static void sm3_extend(const vss_uint8 *b, SM3_WORD_T *w)
; Function sm3_extend
.L59:
sm3_extend:	.type	func
	mov.aa	a15,a5
.L250:

; ..\vss_code\sm3.c	   216  {
; ..\vss_code\sm3.c	   217  	vss_uint32 i = 0;
; ..\vss_code\sm3.c	   218  	vss_uint32 j = 0;
; ..\vss_code\sm3.c	   219  
; ..\vss_code\sm3.c	   220  	/*	b�ĳ���Ӧ�ù̶�Ϊ16���֣�Ҳ��64�ֽ�	*/
; ..\vss_code\sm3.c	   221  	sm3_rot_r((const SM3_WORD_T *)b, 16, w);
	mov	d4,#16
	mov.aa	a5,a15
	call	sm3_rot_r
.L249:

; ..\vss_code\sm3.c	   222  	
; ..\vss_code\sm3.c	   223  	for (i = 16; i < 68; i++) {
	mov	d15,#16
	lea	a12,51
.L25:

; ..\vss_code\sm3.c	   224  		w[i] = P1((w[i - 16]) ^ (w[i - 9]) ^ (sm3_lshift(w[i - 3],15))) ^ (sm3_lshift(w[i - 13],7))  ^ w[i - 6];
	addsc.a	a13,a15,d15,#2
	mov	d5,#15
	ld.w	d8,[a13]-64
	ld.w	d0,[a13]-36
	ld.w	d4,[a13]-12
	xor	d8,d0
	call	sm3_lshift
	xor	d8,d2
	mov	d5,#15
	mov	d4,d8
	call	sm3_lshift
	xor	d9,d8,d2
	mov	d5,#23
	mov	d4,d8
	call	sm3_lshift
.L433:
	addsc.a	a14,a15,d15,#2
.L434:
	xor	d9,d2
.L435:
	ld.w	d4,[a14]-52
.L436:
	mov	d5,#7
	call	sm3_lshift
.L437:
	xor	d9,d2
	ld.w	d0,[a13]-24
.L438:
	add	d15,#1
.L439:
	xor	d9,d0
	st.w	[a14],d9
	loop	a12,.L25
.L440:

; ..\vss_code\sm3.c	   225  	}
; ..\vss_code\sm3.c	   226  
; ..\vss_code\sm3.c	   227   	for (j = 0; j < 64; j++)
	lea	a2,63
.L26:

; ..\vss_code\sm3.c	   228   	{
; ..\vss_code\sm3.c	   229   		w[68 + j] = w[j] ^ w[j + 4];
	ld.w	d0,[a15]
.L441:
	ld.w	d15,[a15]16
.L442:
	xor	d0,d15
	st.w	[a15]272,d0
.L443:
	add.a	a15,#4
	loop	a2,.L26
.L444:

; ..\vss_code\sm3.c	   230   	}
; ..\vss_code\sm3.c	   231  }
	ret
.L201:
	
__sm3_extend_function_end:
	.size	sm3_extend,__sm3_extend_function_end-sm3_extend
.L135:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_compress')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm3.c	   232  
; ..\vss_code\sm3.c	   233  /**
; ..\vss_code\sm3.c	   234   *	5.3.2 ѹ������
; ..\vss_code\sm3.c	   235   *	��A,B,C,D,E,F,G,HΪ�ּĴ���, SS1,SS2,TT1,TT2Ϊ�м����,
; ..\vss_code\sm3.c	   236   *	ѹ������V(i+1) = CF(V(i),B(i)), 0 <= i <= n-1��
; ..\vss_code\sm3.c	   237   *
; ..\vss_code\sm3.c	   238   *	�������������£�
; ..\vss_code\sm3.c	   239   *	ABCDEFGH = V(i)
; ..\vss_code\sm3.c	   240   *	FOR j=0 TO 63
; ..\vss_code\sm3.c	   241   *		SS1 = ((A <<< 12) + E + (Tj <<< j)) <<< 7
; ..\vss_code\sm3.c	   242   *		SS2 = SS1 ? (A <<< 12)
; ..\vss_code\sm3.c	   243   *		TT1 = FFj(A,B,C) + D + SS2 +W��j
; ..\vss_code\sm3.c	   244   *		TT2 = GGj(E,F,G) + H + SS1 +Wj
; ..\vss_code\sm3.c	   245   *		D = C
; ..\vss_code\sm3.c	   246   *		C = B <<< 9
; ..\vss_code\sm3.c	   247   *		B = A
; ..\vss_code\sm3.c	   248   *		A = TT1
; ..\vss_code\sm3.c	   249   *		H = G
; ..\vss_code\sm3.c	   250   *		G = F <<< 19
; ..\vss_code\sm3.c	   251   *		F = E
; ..\vss_code\sm3.c	   252   *		E = P0(TT2)
; ..\vss_code\sm3.c	   253   *	ENDFOR
; ..\vss_code\sm3.c	   254   *	
; ..\vss_code\sm3.c	   255   *	V(i+1) = ABCDEFGH ? V(i)
; ..\vss_code\sm3.c	   256   *
; ..\vss_code\sm3.c	   257   *	ע1��?  ��ʾ32�����������
; ..\vss_code\sm3.c	   258   *	     <<< ��ʾѭ������k��������
; ..\vss_code\sm3.c	   259   *	ע2���ֵĴ洢Ϊ���(big-endian)��ʽ��
; ..\vss_code\sm3.c	   260   */
; ..\vss_code\sm3.c	   261  static void sm3_compress(SM3_WORD_T *v, SM3_WORD_T *w)
; Function sm3_compress
.L61:
sm3_compress:	.type	func
	sub.a	a10,#48
.L251:
	st.a	[a10]36,a4
.L254:
	st.a	[a10]32,a5
.L255:

; ..\vss_code\sm3.c	   262  {
; ..\vss_code\sm3.c	   263  	/*	v��vi���̶�Ϊ8���֣�Ҳ��32�ֽ�	*/
; ..\vss_code\sm3.c	   264  	SM3_WORD_T vi[8];
; ..\vss_code\sm3.c	   265  	SM3_WORD_T *A = vi;
; ..\vss_code\sm3.c	   266  	SM3_WORD_T *B = vi+1;
; ..\vss_code\sm3.c	   267  	SM3_WORD_T *C = vi+2;
; ..\vss_code\sm3.c	   268  	SM3_WORD_T *D = vi+3;
; ..\vss_code\sm3.c	   269  	SM3_WORD_T *E = vi+4;
	lea	a12,[a10]16
.L256:

; ..\vss_code\sm3.c	   270  	SM3_WORD_T *F = vi+5;
	lea	a13,[a10]20
.L257:

; ..\vss_code\sm3.c	   271  	SM3_WORD_T *G = vi+6;
	lea	a14,[a10]24
.L258:

; ..\vss_code\sm3.c	   272  	SM3_WORD_T *H = vi+7;
; ..\vss_code\sm3.c	   273  	
; ..\vss_code\sm3.c	   274  	SM3_WORD_T SS1 = 0;
; ..\vss_code\sm3.c	   275  	SM3_WORD_T SS2 = 0;
; ..\vss_code\sm3.c	   276  	SM3_WORD_T TT1 = 0;
; ..\vss_code\sm3.c	   277  	SM3_WORD_T TT2 = 0;
; ..\vss_code\sm3.c	   278  
; ..\vss_code\sm3.c	   279  	vss_uint32 j = 0;
; ..\vss_code\sm3.c	   280  	
; ..\vss_code\sm3.c	   281  	/*	ABCDEFGH = V(i)	*/
; ..\vss_code\sm3.c	   282  	mem_cpy8(vi, v, 32);
	mov.aa	a4,a10
.L252:
	mov	d4,#32
	ld.a	a5,[a10]36
.L253:
	call	mem_cpy8
.L259:

; ..\vss_code\sm3.c	   283  	
; ..\vss_code\sm3.c	   284  	for (j = 0; j <= 63; j++) {
	mov	d10,#0
	lea	a15,63
.L260:
	st.a	[a10]40,a15
.L27:

; ..\vss_code\sm3.c	   285  		/*		SS1 = ((A <<< 12) + E + (Tj <<< j)) <<< 7	*/
; ..\vss_code\sm3.c	   286  		SS1 = sm3_lshift(sm3_lshift(*A, 12) + (*E) + sm3_lshift(T(j), j), 7);
	mov	d4,d10
	call	T
.L449:
	mov	d8,d2
	ld.w	d11,[a12]
.L450:
	ld.w	d9,[a10]
.L451:
	mov	d5,#12
	mov	d4,d9
	call	sm3_lshift
.L452:
	mov	d12,d2
.L453:
	add	d15,d12,d11
.L454:
	mov	e4,d10,d8
	call	sm3_lshift
.L455:
	add	d4,d15,d2
.L456:
	mov	d5,#7
	call	sm3_lshift
.L261:

; ..\vss_code\sm3.c	   287  		
; ..\vss_code\sm3.c	   288  		/*		SS2 = SS1 ? (A <<< 12)						*/
; ..\vss_code\sm3.c	   289  		SS2 = SS1 ^ sm3_lshift(*A, 12);
; ..\vss_code\sm3.c	   290  		
; ..\vss_code\sm3.c	   291  		/*		TT1 = FFj(A,B,C) + D + SS2 +W��j			*/
; ..\vss_code\sm3.c	   292  		TT1 = FF(j, *A, *B, *C) + (*D) + SS2 + w[68+j];
	mov	e4,d9,d10
	ld.w	d8,[a10]8
.L457:
	mov	d15,d2
	ld.w	d6,[a10]4
.L263:
	mov	d7,d8
	call	FF
.L262:
	ld.a	a15,[a10]32
.L265:
	ld.w	d0,[a10]12
.L458:

; ..\vss_code\sm3.c	   293  
; ..\vss_code\sm3.c	   294  		/*		TT2 = GGj(E,F,G) + H + SS1 +Wj				*/
; ..\vss_code\sm3.c	   295  		TT2 = GG(j, *E, *F, *G) + (*H) + SS1 + w[j];
	xor	d12,d15
	ld.w	d6,[a13]
.L459:
	add	d2,d0
	ld.w	d0,[a15]272
.L460:
	add	d2,d12
	ld.w	d7,[a14]
	mov	e4,d11,d10
.L461:
	add	d9,d2,d0
	call	GG
.L267:
	ld.w	d0,[a10]28
.L462:
	ld.a	a15,[a10]32
.L463:
	add	d0,d2
.L464:
	add	d0,d15
.L465:
	ld.w	d15,[a15+]
.L264:
	st.a	[a10]32,a15
.L466:

; ..\vss_code\sm3.c	   296  		
; ..\vss_code\sm3.c	   297  		/*		D = C										*/
; ..\vss_code\sm3.c	   298  		*D = *C;
	st.w	[a10]12,d8
.L268:
	add	d15,d0
.L467:

; ..\vss_code\sm3.c	   299  		
; ..\vss_code\sm3.c	   300  		/*		C = B <<< 9									*/
; ..\vss_code\sm3.c	   301  		*C = sm3_lshift(*B, 9);
	ld.w	d4,[a10]4
.L468:
	mov	d5,#9
	call	sm3_lshift
.L469:
	st.w	[a10]8,d2
.L470:

; ..\vss_code\sm3.c	   302  
; ..\vss_code\sm3.c	   303  		/*		B = A										*/
; ..\vss_code\sm3.c	   304  		*B = *A;
; ..\vss_code\sm3.c	   305  		
; ..\vss_code\sm3.c	   306  		/*		A = TT1										*/
; ..\vss_code\sm3.c	   307  		*A = TT1;
; ..\vss_code\sm3.c	   308  		
; ..\vss_code\sm3.c	   309  		/*		H = G										*/
; ..\vss_code\sm3.c	   310  		*H = *G;
; ..\vss_code\sm3.c	   311  		
; ..\vss_code\sm3.c	   312  		/*		G = F <<< 19								*/
; ..\vss_code\sm3.c	   313  		*G = sm3_lshift(*F, 19);
	mov	d5,#19
.L471:
	ld.w	d0,[a10]
.L472:
	st.w	[a10]4,d0
.L473:
	st.w	[a10],d9
.L474:
	ld.w	d0,[a14]
.L475:
	st.w	[a10]28,d0
.L476:
	ld.w	d4,[a13]
.L477:
	call	sm3_lshift
.L478:
	st.w	[a14],d2
.L479:

; ..\vss_code\sm3.c	   314  		
; ..\vss_code\sm3.c	   315  		/*		F = E										*/
; ..\vss_code\sm3.c	   316  		*F = *E;
; ..\vss_code\sm3.c	   317  
; ..\vss_code\sm3.c	   318  		/*		E = P0(TT2)									*/
; ..\vss_code\sm3.c	   319  		*E = P0(TT2);
	mov	d4,d15
.L269:
	ld.w	d0,[a12]
.L480:
	mov	d5,#9
	st.w	[a13],d0
.L481:
	call	sm3_lshift
.L270:
	xor	d8,d15,d2
	mov	d5,#17
	mov	d4,d15
	call	sm3_lshift
.L482:
	xor	d8,d2
	st.w	[a12],d8
.L483:
	add	d10,#1
.L484:
	ld.a	a15,[a10]40
.L266:
	add.a	a15,#-1
	st.a	[a10]40,a15
	add.a	a15,#1
	loop	a15,.L27
.L485:

; ..\vss_code\sm3.c	   320  	}
; ..\vss_code\sm3.c	   321  
; ..\vss_code\sm3.c	   322  	/*	V(i+1) = ABCDEFGH ? V(i)	*/
; ..\vss_code\sm3.c	   323  	for (j = 0; j < 8; j++) {
	ld.a	a15,[a10]36
.L271:

; ..\vss_code\sm3.c	   324  		v[j] = v[j] ^ vi[j];
	mov.aa	a2,a10
.L486:
	mov.a	a4,#7
.L28:
	ld.w	d15,[a15]
.L487:
	ld.w	d0,[a2+]
.L488:
	xor	d15,d0
	st.w	[a15+],d15
	loop	a4,.L28
.L489:

; ..\vss_code\sm3.c	   325  	}
; ..\vss_code\sm3.c	   326  }
	ret
.L206:
	
__sm3_compress_function_end:
	.size	sm3_compress,__sm3_compress_function_end-sm3_compress
.L140:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_loop')
	.sect	'.text.vss_api_code'
	.align	2
	

; ..\vss_code\sm3.c	   327  
; ..\vss_code\sm3.c	   328  
; ..\vss_code\sm3.c	   329  /**
; ..\vss_code\sm3.c	   330   *	5.3.1 �����
; ..\vss_code\sm3.c	   331   *	���������Ϣm�䰴64�ֽڣ�512���أ����з��飺m�� = B(0)B(1)...B(n-1)
; ..\vss_code\sm3.c	   332   *	����n=m_size/512��
; ..\vss_code\sm3.c	   333   *	��m�䰴���з�ʽ���
; ..\vss_code\sm3.c	   334   *
; ..\vss_code\sm3.c	   335   *	FOR i=0 TO n-1
; ..\vss_code\sm3.c	   336   *		V(i+1) = CF(V(i), B(i))
; ..\vss_code\sm3.c	   337   *	ENDFOR
; ..\vss_code\sm3.c	   338   *
; ..\vss_code\sm3.c	   339   *	����CF��ѹ������V(0)Ϊ256���س�ʼֵIV��B(i)Ϊ�������Ϣ���飬���ѹ���Ľ��ΪV(n)�� 
; ..\vss_code\sm3.c	   340   */
; ..\vss_code\sm3.c	   341  static vss_uint32 sm3_loop(const vss_uint8 *m, vss_uint32 m_bytes, SM3_WORD_T *iv)
; Function sm3_loop
.L63:
sm3_loop:	.type	func
	lea	a10,[a10]-528
.L272:
	mov.aa	a12,a5
.L273:

; ..\vss_code\sm3.c	   342  {
; ..\vss_code\sm3.c	   343  	vss_uint32 left_bytes = m_bytes;
; ..\vss_code\sm3.c	   344  	const vss_uint8 *b = m;
	mov	d8,d4
	mov.aa	a13,a4
.L274:

; ..\vss_code\sm3.c	   345  	vss_uint32 i = 0;
; ..\vss_code\sm3.c	   346  	
; ..\vss_code\sm3.c	   347  	/*	�������Ϣ����(132����)	*/
; ..\vss_code\sm3.c	   348  	SM3_WORD_T w[132] = {0};
	mov	d9,#0
	movh.a	a15,#@his(.1.ini)
.L275:
	lea	a15,[a15]@los(.1.ini)
.L494:
	mov.aa	a2,a10
	lea	a4,131
.L29:
	ld.w	d15,[a15+]
	st.w	[a2+],d15
	loop	a4,.L29
.L495:

; ..\vss_code\sm3.c	   349  
; ..\vss_code\sm3.c	   350  	while (left_bytes > 0) {
; ..\vss_code\sm3.c	   351  
; ..\vss_code\sm3.c	   352  		if (left_bytes < 64) {
	mov	d15,#64
	j	.L30
.L31:
	jge.u	d8,d15,.L32
.L496:

; ..\vss_code\sm3.c	   353  			/*	���������Ӧ�ó���	*/
; ..\vss_code\sm3.c	   354  			return 0;
; ..\vss_code\sm3.c	   355  		}
; ..\vss_code\sm3.c	   356  
; ..\vss_code\sm3.c	   357  		sm3_extend(b, w);
; ..\vss_code\sm3.c	   358  
; ..\vss_code\sm3.c	   359  		sm3_compress(iv, w);
; ..\vss_code\sm3.c	   360  	
; ..\vss_code\sm3.c	   361  		left_bytes -= 64;
; ..\vss_code\sm3.c	   362  		b += 64;
; ..\vss_code\sm3.c	   363  		
; ..\vss_code\sm3.c	   364  		i++;
; ..\vss_code\sm3.c	   365  	}	
; ..\vss_code\sm3.c	   366  
; ..\vss_code\sm3.c	   367  	/*	���ص�����	*/
; ..\vss_code\sm3.c	   368  	return i;
; ..\vss_code\sm3.c	   369  }
	mov	d2,#0
	ret
.L32:
	mov.aa	a5,a10
	mov.aa	a4,a13
.L276:
	call	sm3_extend
.L277:
	mov.aa	a5,a10
	mov.aa	a4,a12
.L278:
	call	sm3_compress
.L279:
	add	d8,d8,#-64
	lea	a13,[a13]64
.L497:
	add	d9,#1
.L30:
	jne	d8,#0,.L31
.L498:
	mov	d2,d9
	ret
.L222:
	
__sm3_loop_function_end:
	.size	sm3_loop,__sm3_loop_function_end-sm3_loop
.L145:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_init')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	sm3_init

; ..\vss_code\sm3.c	   370  
; ..\vss_code\sm3.c	   371  
; ..\vss_code\sm3.c	   372  
; ..\vss_code\sm3.c	   373  /***************************************************************************************
; ..\vss_code\sm3.c	   374   *
; ..\vss_code\sm3.c	   375   *	��2����: 
; ..\vss_code\sm3.c	   376   *	���㷨ԭ�Ӳ�����װΪ3��ʽ(init/update/final)�ĵ��ú���
; ..\vss_code\sm3.c	   377   *
; ..\vss_code\sm3.c	   378   ***************************************************************************************/
; ..\vss_code\sm3.c	   379  
; ..\vss_code\sm3.c	   380   vss_uint32 sm3_init(SM3_CTX_T *ctx)
; Function sm3_init
.L65:
sm3_init:	.type	func

; ..\vss_code\sm3.c	   381  {
; ..\vss_code\sm3.c	   382  	mem_set8(ctx, 0, 168);
	mov	d4,#0
	mov.aa	a15,a4
.L281:
	mov	d5,#168
	mov.aa	a4,a15
	call	mem_set8
.L280:

; ..\vss_code\sm3.c	   383  
; ..\vss_code\sm3.c	   384  	mem_cpy8(ctx->iv, SM3_IV, 32);
	lea	a4,[a15]136
.L316:
	movh.a	a5,#@his(SM3_IV)
	lea	a5,[a5]@los(SM3_IV)
.L317:
	mov	d4,#32
	call	mem_cpy8
.L318:

; ..\vss_code\sm3.c	   385  
; ..\vss_code\sm3.c	   386  	return 1;
; ..\vss_code\sm3.c	   387  }
	mov	d2,#1
	ret
.L147:
	
__sm3_init_function_end:
	.size	sm3_init,__sm3_init_function_end-sm3_init
.L80:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_update')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	sm3_update

; ..\vss_code\sm3.c	   388  
; ..\vss_code\sm3.c	   389   vss_uint32 sm3_update(SM3_CTX_T *ctx, vss_uint8 *m, vss_uint32 m_bytes)
; Function sm3_update
.L67:
sm3_update:	.type	func
	mov.aa	a15,a4
.L285:

; ..\vss_code\sm3.c	   390  {
; ..\vss_code\sm3.c	   391  	vss_uint32 pm_len = 0;
; ..\vss_code\sm3.c	   392  	const vss_uint8 *pm = VSS_NULL;
; ..\vss_code\sm3.c	   393  
; ..\vss_code\sm3.c	   394  	/*	��¼��ݳ��ȣ������������padding	*/
; ..\vss_code\sm3.c	   395  	ctx->m_size += m_bytes;
	mov	d8,d4
	ld.w	d0,[a15]
.L286:
	mov.aa	a12,a5
.L288:
	add	d0,d8
	st.w	[a15],d0
.L323:

; ..\vss_code\sm3.c	   396  
; ..\vss_code\sm3.c	   397  	if ( ctx->r_len && (ctx->r_len + m_bytes) >= 64 ) {
	ld.w	d15,[a15]132
.L324:
	jeq	d15,#0,.L36
.L325:
	add	d0,d15,d8
.L326:
	mov	d1,#64
.L327:
	jlt.u	d0,d1,.L37
.L328:

; ..\vss_code\sm3.c	   398  
; ..\vss_code\sm3.c	   399  		/*	������ʣ����ݣ��ҿ��Ժ���������һ���µĿ飬�����ƴ�Ӳ�����	*/
; ..\vss_code\sm3.c	   400  		mem_cpy8(ctx->remain + ctx->r_len, m, 64 - ctx->r_len);
	lea	a2,[a15]4
.L329:
	addsc.a	a4,a2,d15,#0
.L282:
	rsub	d4,d15,#64
	mov.aa	a5,a12
.L284:
	call	mem_cpy8
.L283:

; ..\vss_code\sm3.c	   401  		sm3_loop(ctx->remain, 64, ctx->iv);
	lea	a4,[a15]4
.L330:
	mov	d4,#64
	lea	a5,[a15]136
	call	sm3_loop
.L331:

; ..\vss_code\sm3.c	   402  
; ..\vss_code\sm3.c	   403  		/*	�ƶ�m��ָ�룬���ݼ�m_bytes		*/
; ..\vss_code\sm3.c	   404  		m += (64 - ctx->r_len);
	ld.w	d0,[a15]132
.L332:

; ..\vss_code\sm3.c	   405  		m_bytes -= (64 - ctx->r_len);
; ..\vss_code\sm3.c	   406  
; ..\vss_code\sm3.c	   407  		/*	ʣ�������0						*/
; ..\vss_code\sm3.c	   408  		mem_set8(ctx->remain, 0, 128);
	mov	d4,#0
	lea	a4,[a15]4
.L333:
	rsub	d15,d0,#64
.L334:
	addsc.a	a12,a12,d15,#0
.L335:
	sub	d8,d15
.L336:
	mov	d5,#128
	call	mem_set8
.L337:

; ..\vss_code\sm3.c	   409  		ctx->r_len = 0;
	mov	d15,#0
	st.w	[a15]132,d15
.L37:
.L36:

; ..\vss_code\sm3.c	   410  	}
; ..\vss_code\sm3.c	   411  	
; ..\vss_code\sm3.c	   412  	if (ctx->r_len) {
	ld.w	d15,[a15]132
.L338:
	jeq	d15,#0,.L38
.L339:

; ..\vss_code\sm3.c	   413  
; ..\vss_code\sm3.c	   414  		/*	ʣ����ݺ��������Ȼ���������һ���µĿ飬ֻ�ܽ�����ݼ���浽remain��	*/
; ..\vss_code\sm3.c	   415  		mem_cpy8(ctx->remain + ctx->r_len, m, m_bytes);
	lea	a2,[a15]4
.L340:
	addsc.a	a4,a2,d15,#0
.L341:

; ..\vss_code\sm3.c	   416  		ctx->r_len += m_bytes;
	ld.w	d15,[a15]132
.L342:
	mov	d4,d8
	mov.aa	a5,a12
.L289:
	add	d8,d15
	j	.L39
.L38:

; ..\vss_code\sm3.c	   417  	}
; ..\vss_code\sm3.c	   418  	else {
; ..\vss_code\sm3.c	   419  
; ..\vss_code\sm3.c	   420  		/*	ֻ������뵽�鳤�ȵ���ݣ�����ı�����ctx->remain�����������	*/
; ..\vss_code\sm3.c	   421  		pm = m;
; ..\vss_code\sm3.c	   422  		pm_len = m_bytes - (m_bytes % 64);
	and	d15,d8,#63
.L343:
	sub	d15,d8,d15
.L290:

; ..\vss_code\sm3.c	   423  			
; ..\vss_code\sm3.c	   424  		if (pm_len) {
	jeq	d15,#0,.L40
.L344:

; ..\vss_code\sm3.c	   425  			sm3_loop(pm, pm_len, ctx->iv);
	lea	a5,[a15]136
	mov.aa	a4,a12
.L291:
	mov	d4,d15
	call	sm3_loop
.L40:

; ..\vss_code\sm3.c	   426  		}
; ..\vss_code\sm3.c	   427  
; ..\vss_code\sm3.c	   428  		/*	����ʣ����ݵ�remain��	*/
; ..\vss_code\sm3.c	   429  		if (m_bytes > pm_len) {
	jge.u	d15,d8,.L41
.L345:

; ..\vss_code\sm3.c	   430  			mem_cpy8(ctx->remain, pm + pm_len, (m_bytes - pm_len));
	sub	d8,d15
	lea	a4,[a15]4
.L287:
	addsc.a	a5,a12,d15,#0
.L346:
	mov	d4,d8
.L39:
	call	mem_cpy8
.L347:

; ..\vss_code\sm3.c	   431  			ctx->r_len = (m_bytes - pm_len);
	st.w	[a15]132,d8
.L41:

; ..\vss_code\sm3.c	   432  		}
; ..\vss_code\sm3.c	   433  	}
; ..\vss_code\sm3.c	   434  
; ..\vss_code\sm3.c	   435  	return 1;
; ..\vss_code\sm3.c	   436  }
	mov	d2,#1
	ret
.L150:
	
__sm3_update_function_end:
	.size	sm3_update,__sm3_update_function_end-sm3_update
.L85:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('sm3_final')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	sm3_final

; ..\vss_code\sm3.c	   437  
; ..\vss_code\sm3.c	   438  vss_uint32 sm3_final(vss_uint8 *dgst, SM3_CTX_T *ctx)
; Function sm3_final
.L69:
sm3_final:	.type	func
	mov.aa	a15,a4
.L294:
	mov.aa	a12,a5
.L295:

; ..\vss_code\sm3.c	   439  {
; ..\vss_code\sm3.c	   440  	vss_uint32 pm_len = 0;
; ..\vss_code\sm3.c	   441  	
; ..\vss_code\sm3.c	   442  	pm_len = sm3_padding(ctx->m_size, ctx->remain + ctx->r_len);
; ..\vss_code\sm3.c	   443  	pm_len += ctx->r_len;
	ld.w	d15,[a12]132
.L352:
	ld.w	d4,[a12]
.L353:
	lea	a2,[a12]4
.L354:
	addsc.a	a4,a2,d15,#0
.L293:
	call	sm3_padding
.L292:

; ..\vss_code\sm3.c	   444  
; ..\vss_code\sm3.c	   445  	sm3_loop(ctx->remain, pm_len, ctx->iv);
	add	d4,d2,d15
	lea	a4,[a12]4
.L296:
	lea	a5,[a12]136
	call	sm3_loop
.L297:

; ..\vss_code\sm3.c	   446  
; ..\vss_code\sm3.c	   447  	sm3_rot_r(ctx->iv, 8, (SM3_WORD_T*)dgst);
	lea	a4,[a12]136
.L355:
	mov	d4,#8
	mov.aa	a5,a15
.L298:
	call	sm3_rot_r
.L299:

; ..\vss_code\sm3.c	   448  
; ..\vss_code\sm3.c	   449  	
; ..\vss_code\sm3.c	   450  	return 32;
; ..\vss_code\sm3.c	   451  }
	mov	d2,#32
	ret
.L158:
	
__sm3_final_function_end:
	.size	sm3_final,__sm3_final_function_end-sm3_final
.L90:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mizar_sm3')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mizar_sm3

; ..\vss_code\sm3.c	   452  
; ..\vss_code\sm3.c	   453  void mizar_sm3(vss_uint8 *msg, vss_size msglen, vss_uint8 *dgst)
; Function mizar_sm3
.L71:
mizar_sm3:	.type	func
	sub.a	a10,#168
.L300:
	mov.aa	a15,a4
.L303:
	mov	d15,d4
	mov.aa	a12,a5
.L304:

; ..\vss_code\sm3.c	   454  {
; ..\vss_code\sm3.c	   455  	SM3_CTX_T ctx;
; ..\vss_code\sm3.c	   456  	sm3_init(&ctx);
	mov.aa	a4,a10
.L302:
	call	sm3_init
.L301:

; ..\vss_code\sm3.c	   457  	sm3_update(&ctx, msg, msglen);
	mov.aa	a4,a10
.L360:
	mov.aa	a5,a15
.L305:
	mov	d4,d15
	call	sm3_update
.L306:

; ..\vss_code\sm3.c	   458  	sm3_final(dgst, &ctx);
	mov.aa	a5,a10
	mov.aa	a4,a12
.L307:
	j	sm3_final
.L162:
	
__mizar_sm3_function_end:
	.size	mizar_sm3,__mizar_sm3_function_end-mizar_sm3
.L95:
	; End of function
	
	.sdecl	'.rodata.sm3..1.ini',data,rom
	.sect	'.rodata.sm3..1.ini'
	.align	4
.1.ini:	.type	object
	.size	.1.ini,528
	.space	528
	.calls	'sm3_rot_r','sm3_rot'
	.calls	'sm3_padding','mem_set8'
	.calls	'sm3_extend','sm3_rot_r'
	.calls	'sm3_extend','sm3_lshift'
	.calls	'sm3_compress','mem_cpy8'
	.calls	'sm3_compress','T'
	.calls	'sm3_compress','sm3_lshift'
	.calls	'sm3_compress','FF'
	.calls	'sm3_compress','GG'
	.calls	'sm3_loop','sm3_extend'
	.calls	'sm3_loop','sm3_compress'
	.calls	'sm3_init','mem_set8'
	.calls	'sm3_init','mem_cpy8'
	.calls	'sm3_update','mem_cpy8'
	.calls	'sm3_update','sm3_loop'
	.calls	'sm3_update','mem_set8'
	.calls	'sm3_final','sm3_padding'
	.calls	'sm3_final','sm3_loop'
	.calls	'sm3_final','sm3_rot_r'
	.calls	'mizar_sm3','sm3_init'
	.calls	'mizar_sm3','sm3_update'
	.calls	'mizar_sm3','sm3_final'
	.calls	'T','',0
	.calls	'FF','',0
	.calls	'GG','',0
	.calls	'sm3_lshift','',0
	.calls	'sm3_rot','',8
	.calls	'sm3_rot_r','',0
	.calls	'sm3_padding','',0
	.calls	'sm3_extend','',0
	.calls	'sm3_compress','',48
	.calls	'sm3_loop','',528
	.calls	'sm3_init','',0
	.calls	'sm3_update','',0
	.calls	'sm3_final','',0
	.extern	mem_set8
	.extern	mem_cpy8
	.extern	SM3_IV
	.calls	'mizar_sm3','',168
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L73:
	.word	830
	.half	3
	.word	.L74
	.byte	4
.L72:
	.byte	1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L75
.L146:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L166:
	.byte	3,1,27,9,168,1,4
	.byte	'm_size',0,4
	.word	172
	.byte	2,35,0,2
	.byte	'unsigned char',0,1,8,5,128,1
	.word	215
	.byte	6,127,0,4
	.byte	'remain',0,128,1
	.word	232
	.byte	2,35,4,4
	.byte	'r_len',0,4
	.word	172
	.byte	3,35,132,1
.L209:
	.byte	5,32
	.word	172
	.byte	6,7,0,4
	.byte	'iv',0,32
	.word	275
	.byte	3,35,136,1,0
.L148:
	.byte	7
	.word	193
.L152:
	.byte	7
	.word	215
	.byte	8
	.word	215
.L156:
	.byte	7
	.word	308
	.byte	9
	.byte	'mem_set8',0,2,17,6,1,1,1,1,10
	.byte	'result',0,2,17,26
	.word	303
	.byte	8
	.word	215
	.byte	10
	.byte	'content',0,2,17,50
	.word	350
	.byte	10
	.byte	'len',0,2,17,70
	.word	172
	.byte	0,9
	.byte	'mem_cpy8',0,2,19,6,1,1,1,1,10
	.byte	'result',0,2,19,26
	.word	303
	.byte	10
	.byte	'content',0,2,19,51
	.word	313
	.byte	10
	.byte	'len',0,2,19,71
	.word	172
	.byte	0,8
	.word	172
.L188:
	.byte	7
	.word	445
.L191:
	.byte	7
	.word	172
.L229:
	.byte	5,144,4
	.word	172
	.byte	6,131,1,0,11
	.byte	'void',0,7
	.word	471
	.byte	12
	.byte	'__prof_adm',0,3,1,1
	.word	477
	.byte	13,1,7
	.word	501
	.byte	12
	.byte	'__codeptr',0,3,1,1
	.word	503
	.byte	12
	.byte	'vss_uint8',0,1,8,24
	.word	215
	.byte	12
	.byte	'vss_uint32',0,1,13,24
	.word	172
	.byte	2
	.byte	'unsigned long long int',0,8,7,12
	.byte	'vss_uint64',0,1,17,28
	.word	563
	.byte	12
	.byte	'vss_ulong',0,1,18,24
	.word	172
	.byte	12
	.byte	'BYTE',0,1,21,22
	.word	215
	.byte	12
	.byte	'WORD',0,1,22,22
	.word	172
	.byte	12
	.byte	'vss_size',0,1,24,21
	.word	172
	.byte	12
	.byte	'SM3_WORD_T',0,1,25,22
	.word	172
	.byte	12
	.byte	'SM3_CTX_T',0,1,32,3
	.word	193
	.byte	3,1,34,9,108,5,64
	.word	215
	.byte	6,63,0,4
	.byte	'data',0,64
	.word	711
	.byte	2,35,0,4
	.byte	'datalen',0,4
	.word	172
	.byte	2,35,64,4
	.byte	'bitlen',0,8
	.word	563
	.byte	2,35,68,5,32
	.word	172
	.byte	6,7,0,4
	.byte	'state',0,32
	.word	767
	.byte	2,35,76,0,12
	.byte	'SHA256_CTX',0,1,39,3
	.word	706
	.byte	8
	.word	275
	.byte	14
	.byte	'SM3_IV',0,3,29,25
	.word	811
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L74:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,11,15,73,19,56,9,0,0,5,1,1,11,15,73,19,0,0,6,33,0,47,15,0,0,7,15,0,73,19,0,0,8,38,0,73
	.byte	19,0,0,9,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,10,5,0,3,8,58,15,59,15,57,15,73,19,0,0
	.byte	11,59,0,3,8,0,0,12,22,0,3,8,58,15,59,15,57,15,73,19,0,0,13,21,0,54,15,0,0,14,52,0,3,8,58,15,59,15,57,15
	.byte	73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L75:
	.word	.L309-.L308
.L308:
	.half	3
	.word	.L311-.L310
.L310:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsstype.h',0,0,0,0
	.byte	'..\\vss_code\\vsscommon.h',0,0,0,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L311:
.L309:
	.sdecl	'.debug_info',debug,cluster('sm3_init')
	.sect	'.debug_info'
.L76:
	.word	238
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L79,.L78
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_init',0,1,252,2,13
	.word	.L146
	.byte	1,1,1
	.word	.L65,.L147,.L64
	.byte	4
	.byte	'ctx',0,1,252,2,33
	.word	.L148,.L149
	.byte	5
	.word	.L65,.L147
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_init')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_init')
	.sect	'.debug_line'
.L78:
	.word	.L313-.L312
.L312:
	.half	3
	.word	.L315-.L314
.L314:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L315:
	.byte	5,16,7,0,5,2
	.word	.L65
	.byte	3,253,2,1,5,13,3,126,1,5,19,9
	.half	.L281-.L65
	.byte	3,2,1,5,14,9
	.half	.L280-.L281
	.byte	3,2,1,5,20,9
	.half	.L316-.L280
	.byte	1,5,28,9
	.half	.L317-.L316
	.byte	1,5,9,9
	.half	.L318-.L317
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L80-.L318
	.byte	0,1,1
.L313:
	.sdecl	'.debug_ranges',debug,cluster('sm3_init')
	.sect	'.debug_ranges'
.L79:
	.word	-1,.L65,0,.L80-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_update')
	.sect	'.debug_info'
.L81:
	.word	313
	.half	3
	.word	.L82
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L84,.L83
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_update',0,1,133,3,13
	.word	.L146
	.byte	1,1,1
	.word	.L67,.L150,.L66
	.byte	4
	.byte	'ctx',0,1,133,3,35
	.word	.L148,.L151
	.byte	4
	.byte	'm',0,1,133,3,51
	.word	.L152,.L153
	.byte	4
	.byte	'm_bytes',0,1,133,3,65
	.word	.L146,.L154
	.byte	5
	.word	.L67,.L150
	.byte	6
	.byte	'pm_len',0,1,135,3,13
	.word	.L146,.L155
	.byte	6
	.byte	'pm',0,1,136,3,19
	.word	.L156,.L157
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_update')
	.sect	'.debug_abbrev'
.L82:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_update')
	.sect	'.debug_line'
.L83:
	.word	.L320-.L319
.L319:
	.half	3
	.word	.L322-.L321
.L321:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L322:
	.byte	5,13,7,0,5,2
	.word	.L67
	.byte	3,132,3,1,5,5,9
	.half	.L285-.L67
	.byte	3,6,1,5,13,9
	.half	.L286-.L285
	.byte	3,122,1,5,14,9
	.half	.L288-.L286
	.byte	3,6,1,5,10,9
	.half	.L323-.L288
	.byte	3,2,1,5,7,9
	.half	.L324-.L323
	.byte	1,5,33,7,9
	.half	.L325-.L324
	.byte	1,5,47,9
	.half	.L326-.L325
	.byte	1,5,44,9
	.half	.L327-.L326
	.byte	1,5,15,7,9
	.half	.L328-.L327
	.byte	3,3,1,5,24,9
	.half	.L329-.L328
	.byte	1,5,44,9
	.half	.L282-.L329
	.byte	1,5,15,9
	.half	.L283-.L282
	.byte	3,1,1,5,25,9
	.half	.L330-.L283
	.byte	1,5,32,1,5,17,9
	.half	.L331-.L330
	.byte	3,3,1,5,25,9
	.half	.L332-.L331
	.byte	3,4,1,5,15,1,5,12,9
	.half	.L333-.L332
	.byte	3,124,1,5,5,9
	.half	.L334-.L333
	.byte	1,5,11,9
	.half	.L335-.L334
	.byte	3,1,1,5,28,9
	.half	.L336-.L335
	.byte	3,3,1,5,16,9
	.half	.L337-.L336
	.byte	3,1,1,5,14,1,5,9,9
	.half	.L36-.L337
	.byte	3,3,1,5,2,9
	.half	.L338-.L36
	.byte	1,5,15,7,9
	.half	.L339-.L338
	.byte	3,3,1,5,24,9
	.half	.L340-.L339
	.byte	1,5,6,9
	.half	.L341-.L340
	.byte	3,1,1,5,41,9
	.half	.L342-.L341
	.byte	3,127,1,5,14,9
	.half	.L289-.L342
	.byte	3,1,1,5,49,3,127,1,5,23,9
	.half	.L38-.L289
	.byte	3,7,1,5,20,9
	.half	.L343-.L38
	.byte	1,5,3,9
	.half	.L290-.L343
	.byte	3,2,1,5,28,7,9
	.half	.L344-.L290
	.byte	3,1,1,5,3,9
	.half	.L40-.L344
	.byte	3,4,1,5,48,7,9
	.half	.L345-.L40
	.byte	3,1,1,5,16,1,5,29,9
	.half	.L287-.L345
	.byte	1,5,48,9
	.half	.L346-.L287
	.byte	1,5,15,9
	.half	.L347-.L346
	.byte	3,1,1,5,9,9
	.half	.L41-.L347
	.byte	3,4,1,5,1,3,1,1,7,9
	.half	.L85-.L41
	.byte	0,1,1
.L320:
	.sdecl	'.debug_ranges',debug,cluster('sm3_update')
	.sect	'.debug_ranges'
.L84:
	.word	-1,.L67,0,.L85-.L67,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_final')
	.sect	'.debug_info'
.L86:
	.word	278
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L89,.L88
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_final',0,1,182,3,12
	.word	.L146
	.byte	1,1,1
	.word	.L69,.L158,.L68
	.byte	4
	.byte	'dgst',0,1,182,3,33
	.word	.L152,.L159
	.byte	4
	.byte	'ctx',0,1,182,3,50
	.word	.L148,.L160
	.byte	5
	.word	.L69,.L158
	.byte	6
	.byte	'pm_len',0,1,184,3,13
	.word	.L146,.L161
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_final')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_final')
	.sect	'.debug_line'
.L88:
	.word	.L349-.L348
.L348:
	.half	3
	.word	.L351-.L350
.L350:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L351:
	.byte	5,12,7,0,5,2
	.word	.L69
	.byte	3,181,3,1,5,15,9
	.half	.L295-.L69
	.byte	3,5,1,5,26,9
	.half	.L352-.L295
	.byte	3,127,1,5,39,9
	.half	.L353-.L352
	.byte	1,5,48,9
	.half	.L354-.L353
	.byte	1,5,9,9
	.half	.L292-.L354
	.byte	3,1,1,5,14,3,2,1,5,35,9
	.half	.L296-.L292
	.byte	1,5,15,9
	.half	.L297-.L296
	.byte	3,2,1,5,21,9
	.half	.L355-.L297
	.byte	1,5,24,1,5,9,9
	.half	.L299-.L355
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L90-.L299
	.byte	0,1,1
.L349:
	.sdecl	'.debug_ranges',debug,cluster('sm3_final')
	.sect	'.debug_ranges'
.L89:
	.word	-1,.L69,0,.L90-.L69,0,0
	.sdecl	'.debug_info',debug,cluster('mizar_sm3')
	.sect	'.debug_info'
.L91:
	.word	291
	.half	3
	.word	.L92
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L94,.L93
	.byte	2
	.word	.L72
	.byte	3
	.byte	'mizar_sm3',0,1,197,3,6,1,1,1
	.word	.L71,.L162,.L70
	.byte	4
	.byte	'msg',0,1,197,3,27
	.word	.L152,.L163
	.byte	4
	.byte	'msglen',0,1,197,3,41
	.word	.L146,.L164
	.byte	4
	.byte	'dgst',0,1,197,3,60
	.word	.L152,.L165
	.byte	5
	.word	.L71,.L162
	.byte	6
	.byte	'ctx',0,1,199,3,12
	.word	.L166,.L167
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mizar_sm3')
	.sect	'.debug_abbrev'
.L92:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mizar_sm3')
	.sect	'.debug_line'
.L93:
	.word	.L357-.L356
.L356:
	.half	3
	.word	.L359-.L358
.L358:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L359:
	.byte	5,6,7,0,5,2
	.word	.L71
	.byte	3,196,3,1,5,12,9
	.half	.L304-.L71
	.byte	3,3,1,5,14,9
	.half	.L301-.L304
	.byte	3,1,1,5,24,9
	.half	.L360-.L301
	.byte	1,5,19,9
	.half	.L306-.L360
	.byte	3,1,1,5,1,9
	.half	.L95-.L306
	.byte	3,1,0,1,1
.L357:
	.sdecl	'.debug_ranges',debug,cluster('mizar_sm3')
	.sect	'.debug_ranges'
.L94:
	.word	-1,.L71,0,.L95-.L71,0,0
	.sdecl	'.debug_info',debug,cluster('T')
	.sect	'.debug_info'
.L96:
	.word	227
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L99,.L98
	.byte	2
	.word	.L72
	.byte	3
	.byte	'T',0,1,32,12
	.word	.L146
	.byte	1,1,1
	.word	.L45,.L168,.L44
	.byte	4
	.byte	'j',0,1,32,25
	.word	.L146,.L169
	.byte	5
	.word	.L45,.L168
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('T')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('T')
	.sect	'.debug_line'
.L98:
	.word	.L362-.L361
.L361:
	.half	3
	.word	.L364-.L363
.L363:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L364:
	.byte	5,10,7,0,5,2
	.word	.L45
	.byte	3,33,1,5,2,9
	.half	.L365-.L45
	.byte	1,5,10,7,9
	.half	.L366-.L365
	.byte	3,2,1,5,1,9
	.half	.L367-.L366
	.byte	3,10,1,5,25,7,9
	.half	.L2-.L367
	.byte	3,121,1,5,22,9
	.half	.L368-.L2
	.byte	1,5,11,9
	.half	.L369-.L368
	.byte	3,2,1,5,1,9
	.half	.L370-.L369
	.byte	3,5,1,7,9
	.half	.L100-.L370
	.byte	0,1,1
.L362:
	.sdecl	'.debug_ranges',debug,cluster('T')
	.sect	'.debug_ranges'
.L99:
	.word	-1,.L45,0,.L100-.L45,0,0
	.sdecl	'.debug_info',debug,cluster('FF')
	.sect	'.debug_info'
.L101:
	.word	270
	.half	3
	.word	.L102
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L104,.L103
	.byte	2
	.word	.L72
	.byte	3
	.byte	'FF',0,1,53,12
	.word	.L146
	.byte	1,1,1
	.word	.L47,.L170,.L46
	.byte	4
	.byte	'j',0,1,53,26
	.word	.L146,.L171
	.byte	4
	.byte	'x',0,1,53,40
	.word	.L146,.L172
	.byte	4
	.byte	'y',0,1,53,54
	.word	.L146,.L173
	.byte	4
	.byte	'z',0,1,53,68
	.word	.L146,.L174
	.byte	5
	.word	.L47,.L170
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FF')
	.sect	'.debug_abbrev'
.L102:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FF')
	.sect	'.debug_line'
.L103:
	.word	.L372-.L371
.L371:
	.half	3
	.word	.L374-.L373
.L373:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L374:
	.byte	5,10,7,0,5,2
	.word	.L47
	.byte	3,54,1,5,2,9
	.half	.L375-.L47
	.byte	1,5,10,7,9
	.half	.L376-.L375
	.byte	3,1,1,5,1,9
	.half	.L231-.L376
	.byte	3,8,1,5,22,7,9
	.half	.L7-.L231
	.byte	3,122,1,5,19,9
	.half	.L377-.L7
	.byte	1,5,11,7,9
	.half	.L378-.L377
	.byte	3,1,1,5,1,9
	.half	.L233-.L378
	.byte	3,5,1,5,11,7,9
	.half	.L9-.L233
	.byte	3,125,1,5,1,3,3,1,7,9
	.half	.L105-.L9
	.byte	0,1,1
.L372:
	.sdecl	'.debug_ranges',debug,cluster('FF')
	.sect	'.debug_ranges'
.L104:
	.word	-1,.L47,0,.L105-.L47,0,0
	.sdecl	'.debug_info',debug,cluster('GG')
	.sect	'.debug_info'
.L106:
	.word	270
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L109,.L108
	.byte	2
	.word	.L72
	.byte	3
	.byte	'GG',0,1,66,12
	.word	.L146
	.byte	1,1,1
	.word	.L49,.L175,.L48
	.byte	4
	.byte	'j',0,1,66,26
	.word	.L146,.L176
	.byte	4
	.byte	'x',0,1,66,40
	.word	.L146,.L177
	.byte	4
	.byte	'y',0,1,66,54
	.word	.L146,.L178
	.byte	4
	.byte	'z',0,1,66,68
	.word	.L146,.L179
	.byte	5
	.word	.L49,.L175
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('GG')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('GG')
	.sect	'.debug_line'
.L108:
	.word	.L380-.L379
.L379:
	.half	3
	.word	.L382-.L381
.L381:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L382:
	.byte	5,11,7,0,5,2
	.word	.L49
	.byte	3,195,0,1,5,2,9
	.half	.L383-.L49
	.byte	1,5,10,7,9
	.half	.L384-.L383
	.byte	3,1,1,5,1,9
	.half	.L234-.L384
	.byte	3,8,1,5,22,7,9
	.half	.L12-.L234
	.byte	3,122,1,5,19,9
	.half	.L385-.L12
	.byte	1,5,13,7,9
	.half	.L386-.L385
	.byte	3,1,1,5,1,9
	.half	.L387-.L386
	.byte	3,5,1,5,11,7,9
	.half	.L14-.L387
	.byte	3,125,1,5,1,3,3,1,7,9
	.half	.L110-.L14
	.byte	0,1,1
.L380:
	.sdecl	'.debug_ranges',debug,cluster('GG')
	.sect	'.debug_ranges'
.L109:
	.word	-1,.L49,0,.L110-.L49,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_lshift')
	.sect	'.debug_info'
.L111:
	.word	254
	.half	3
	.word	.L112
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L114,.L113
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_lshift',0,1,87,19
	.word	.L146
	.byte	1,1
	.word	.L51,.L180,.L50
	.byte	4
	.byte	'num',0,1,87,41
	.word	.L146,.L181
	.byte	4
	.byte	'bits',0,1,87,57
	.word	.L146,.L182
	.byte	5
	.word	.L51,.L180
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_lshift')
	.sect	'.debug_abbrev'
.L112:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_lshift')
	.sect	'.debug_line'
.L113:
	.word	.L389-.L388
.L388:
	.half	3
	.word	.L391-.L390
.L390:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L391:
	.byte	5,15,7,0,5,2
	.word	.L51
	.byte	3,217,0,1,5,13,9
	.half	.L392-.L51
	.byte	1,5,24,9
	.half	.L393-.L392
	.byte	1,5,19,9
	.half	.L394-.L393
	.byte	3,1,1,5,12,9
	.half	.L395-.L394
	.byte	1,5,34,9
	.half	.L396-.L395
	.byte	1,5,27,9
	.half	.L237-.L396
	.byte	1,5,1,3,2,1,7,9
	.half	.L115-.L237
	.byte	0,1,1
.L389:
	.sdecl	'.debug_ranges',debug,cluster('sm3_lshift')
	.sect	'.debug_ranges'
.L114:
	.word	-1,.L51,0,.L115-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_rot')
	.sect	'.debug_info'
.L116:
	.word	267
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L119,.L118
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_rot',0,1,97,19
	.word	.L146
	.byte	1,1
	.word	.L53,.L183,.L52
	.byte	4
	.byte	'num',0,1,97,38
	.word	.L146,.L184
	.byte	5
	.word	.L53,.L183
	.byte	6
	.byte	'i',0,1,100,13
	.word	.L146,.L185
	.byte	6
	.byte	'num_b',0,1,101,13
	.word	.L146,.L186
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_rot')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_rot')
	.sect	'.debug_line'
.L118:
	.word	.L398-.L397
.L397:
	.half	3
	.word	.L400-.L399
.L399:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L400:
	.byte	5,19,7,0,5,2
	.word	.L53
	.byte	3,224,0,1,5,21,9
	.half	.L401-.L53
	.byte	3,4,1,5,19,1,5,13,9
	.half	.L402-.L401
	.byte	3,7,1,5,33,9
	.half	.L403-.L402
	.byte	3,124,1,5,19,9
	.half	.L404-.L403
	.byte	3,2,1,5,13,9
	.half	.L18-.L404
	.byte	3,2,1,5,9,9
	.half	.L405-.L18
	.byte	1,5,19,3,126,1,5,9,7,9
	.half	.L406-.L405
	.byte	3,5,1,5,1,9
	.half	.L407-.L406
	.byte	3,4,1,7,9
	.half	.L120-.L407
	.byte	0,1,1
.L398:
	.sdecl	'.debug_ranges',debug,cluster('sm3_rot')
	.sect	'.debug_ranges'
.L119:
	.word	-1,.L53,0,.L120-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_rot_r')
	.sect	'.debug_info'
.L121:
	.word	280
	.half	3
	.word	.L122
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L124,.L123
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_rot_r',0,1,119,13,1,1
	.word	.L55,.L187,.L54
	.byte	4
	.byte	'in',0,1,119,41
	.word	.L188,.L189
	.byte	4
	.byte	'count',0,1,119,56
	.word	.L146,.L190
	.byte	4
	.byte	'out',0,1,119,75
	.word	.L191,.L192
	.byte	5
	.word	.L55,.L187
	.byte	6
	.byte	'i',0,1,122,13
	.word	.L146,.L193
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_rot_r')
	.sect	'.debug_abbrev'
.L122:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_rot_r')
	.sect	'.debug_line'
.L123:
	.word	.L409-.L408
.L408:
	.half	3
	.word	.L411-.L410
.L410:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L411:
	.byte	5,13,7,0,5,2
	.word	.L55
	.byte	3,246,0,1,5,28,3,4,1,5,9,9
	.half	.L239-.L55
	.byte	1,5,28,1,5,22,9
	.half	.L20-.L239
	.byte	3,1,1,5,26,9
	.half	.L412-.L20
	.byte	3,127,1,5,10,3,1,1,5,23,9
	.half	.L413-.L412
	.byte	3,127,1,5,1,7,9
	.half	.L414-.L413
	.byte	3,6,1,7,9
	.half	.L125-.L414
	.byte	0,1,1
.L409:
	.sdecl	'.debug_ranges',debug,cluster('sm3_rot_r')
	.sect	'.debug_ranges'
.L124:
	.word	-1,.L55,0,.L125-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_padding')
	.sect	'.debug_info'
.L126:
	.word	334
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L129,.L128
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_padding',0,1,155,1,19
	.word	.L146
	.byte	1,1
	.word	.L57,.L194,.L56
	.byte	4
	.byte	'm_bytes',0,1,155,1,42
	.word	.L146,.L195
	.byte	4
	.byte	'out',0,1,155,1,62
	.word	.L152,.L196
	.byte	5
	.word	.L57,.L194
	.byte	6
	.byte	'k',0,1,157,1,13
	.word	.L146,.L197
	.byte	6
	.byte	'm_bits',0,1,158,1,13
	.word	.L146,.L198
	.byte	6
	.byte	'mod_bits',0,1,159,1,13
	.word	.L146,.L199
	.byte	6
	.byte	'p',0,1,160,1,13
	.word	.L152,.L200
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_padding')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_padding')
	.sect	'.debug_line'
.L128:
	.word	.L416-.L415
.L415:
	.half	3
	.word	.L418-.L417
.L417:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L418:
	.byte	5,22,7,0,5,2
	.word	.L57
	.byte	3,157,1,1,5,19,3,125,1,5,24,9
	.half	.L241-.L57
	.byte	3,4,1,5,18,9
	.half	.L243-.L241
	.byte	3,4,1,5,2,9
	.half	.L419-.L243
	.byte	1,5,11,7,9
	.half	.L420-.L419
	.byte	3,4,1,5,17,9
	.half	.L22-.L420
	.byte	1,5,3,3,12,1,5,7,9
	.half	.L246-.L22
	.byte	3,127,1,5,5,1,5,8,9
	.half	.L244-.L246
	.byte	3,4,1,5,2,9
	.half	.L421-.L244
	.byte	1,5,15,7,9
	.half	.L422-.L421
	.byte	3,1,1,5,18,1,5,5,9
	.half	.L245-.L422
	.byte	3,1,1,5,14,9
	.half	.L23-.L245
	.byte	3,4,1,5,17,1,5,4,9
	.half	.L248-.L23
	.byte	3,1,1,5,43,9
	.half	.L423-.L248
	.byte	3,2,1,5,7,1,5,43,9
	.half	.L424-.L423
	.byte	3,1,1,5,7,1,5,43,9
	.half	.L425-.L424
	.byte	3,1,1,5,7,1,9
	.half	.L426-.L425
	.byte	3,1,1,5,11,9
	.half	.L427-.L426
	.byte	3,3,1,5,1,9
	.half	.L428-.L427
	.byte	3,1,1,7,9
	.half	.L130-.L428
	.byte	0,1,1
.L416:
	.sdecl	'.debug_ranges',debug,cluster('sm3_padding')
	.sect	'.debug_ranges'
.L129:
	.word	-1,.L57,0,.L130-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_extend')
	.sect	'.debug_info'
.L131:
	.word	279
	.half	3
	.word	.L132
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L134,.L133
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_extend',0,1,215,1,13,1,1
	.word	.L59,.L201,.L58
	.byte	4
	.byte	'b',0,1,215,1,41
	.word	.L156,.L202
	.byte	4
	.byte	'w',0,1,215,1,56
	.word	.L191,.L203
	.byte	5
	.word	.L59,.L201
	.byte	6
	.byte	'i',0,1,217,1,13
	.word	.L146,.L204
	.byte	6
	.byte	'j',0,1,218,1,13
	.word	.L146,.L205
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_extend')
	.sect	'.debug_abbrev'
.L132:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_extend')
	.sect	'.debug_line'
.L133:
	.word	.L430-.L429
.L429:
	.half	3
	.word	.L432-.L431
.L431:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L432:
	.byte	5,13,7,0,5,2
	.word	.L59
	.byte	3,214,1,1,5,35,9
	.half	.L250-.L59
	.byte	3,6,1,5,39,1,5,9,9
	.half	.L249-.L250
	.byte	3,2,1,5,21,1,5,10,9
	.half	.L25-.L249
	.byte	3,1,1,5,4,9
	.half	.L433-.L25
	.byte	1,5,10,9
	.half	.L434-.L433
	.byte	1,5,82,9
	.half	.L435-.L434
	.byte	1,5,91,9
	.half	.L436-.L435
	.byte	1,5,67,9
	.half	.L437-.L436
	.byte	1,5,99,1,5,24,9
	.half	.L438-.L437
	.byte	3,127,1,5,96,9
	.half	.L439-.L438
	.byte	3,1,1,5,8,1,5,21,3,127,1,7,9
	.half	.L440-.L439
	.byte	3,4,1,5,17,9
	.half	.L26-.L440
	.byte	3,2,1,5,24,9
	.half	.L441-.L26
	.byte	1,5,21,9
	.half	.L442-.L441
	.byte	1,5,14,1,5,24,9
	.half	.L443-.L442
	.byte	3,126,1,5,21,1,5,1,7,9
	.half	.L444-.L443
	.byte	3,4,1,7,9
	.half	.L135-.L444
	.byte	0,1,1
.L430:
	.sdecl	'.debug_ranges',debug,cluster('sm3_extend')
	.sect	'.debug_ranges'
.L134:
	.word	-1,.L59,0,.L135-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_compress')
	.sect	'.debug_info'
.L136:
	.word	438
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L139,.L138
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_compress',0,1,133,2,13,1,1
	.word	.L61,.L206,.L60
	.byte	4
	.byte	'v',0,1,133,2,38
	.word	.L191,.L207
	.byte	4
	.byte	'w',0,1,133,2,53
	.word	.L191,.L208
	.byte	5
	.word	.L61,.L206
	.byte	6
	.byte	'vi',0,1,136,2,13
	.word	.L209,.L210
	.byte	6
	.byte	'B',0,1,138,2,14
	.word	.L191,.L211
	.byte	6
	.byte	'C',0,1,139,2,14
	.word	.L191,.L212
	.byte	6
	.byte	'D',0,1,140,2,14
	.word	.L191,.L213
	.byte	6
	.byte	'E',0,1,141,2,14
	.word	.L191,.L214
	.byte	6
	.byte	'F',0,1,142,2,14
	.word	.L191,.L215
	.byte	6
	.byte	'G',0,1,143,2,14
	.word	.L191,.L216
	.byte	6
	.byte	'H',0,1,144,2,14
	.word	.L191,.L217
	.byte	6
	.byte	'SS1',0,1,146,2,13
	.word	.L146,.L218
	.byte	6
	.byte	'TT1',0,1,148,2,13
	.word	.L146,.L219
	.byte	6
	.byte	'TT2',0,1,149,2,13
	.word	.L146,.L220
	.byte	6
	.byte	'j',0,1,151,2,13
	.word	.L146,.L221
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_compress')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_compress')
	.sect	'.debug_line'
.L138:
	.word	.L446-.L445
.L445:
	.half	3
	.word	.L448-.L447
.L447:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L448:
	.byte	5,13,7,0,5,2
	.word	.L61
	.byte	3,132,2,1,5,20,9
	.half	.L255-.L61
	.byte	3,8,1,9
	.half	.L256-.L255
	.byte	3,1,1,9
	.half	.L257-.L256
	.byte	3,1,1,5,11,9
	.half	.L258-.L257
	.byte	3,11,1,5,18,9
	.half	.L252-.L258
	.byte	1,5,9,9
	.half	.L259-.L252
	.byte	3,2,1,5,21,1,5,61,9
	.half	.L27-.L259
	.byte	3,2,1,5,60,9
	.half	.L449-.L27
	.byte	1,5,42,1,5,31,9
	.half	.L450-.L449
	.byte	1,5,35,9
	.half	.L451-.L450
	.byte	1,5,30,9
	.half	.L452-.L451
	.byte	1,5,39,9
	.half	.L453-.L452
	.byte	1,5,65,9
	.half	.L454-.L453
	.byte	1,5,46,9
	.half	.L455-.L454
	.byte	1,5,69,9
	.half	.L456-.L455
	.byte	1,5,23,9
	.half	.L261-.L456
	.byte	3,6,1,5,7,9
	.half	.L457-.L261
	.byte	3,122,1,5,19,3,6,1,5,23,9
	.half	.L263-.L457
	.byte	1,5,43,9
	.half	.L262-.L263
	.byte	1,5,30,9
	.half	.L265-.L262
	.byte	1,5,13,9
	.half	.L458-.L265
	.byte	3,125,1,5,19,3,6,1,5,27,9
	.half	.L459-.L458
	.byte	3,125,1,5,43,1,5,34,9
	.half	.L460-.L459
	.byte	1,5,23,3,3,1,5,40,9
	.half	.L461-.L460
	.byte	3,125,1,5,23,3,3,1,5,30,9
	.half	.L267-.L461
	.byte	1,5,43,9
	.half	.L462-.L267
	.byte	1,5,27,9
	.half	.L463-.L462
	.byte	1,5,34,9
	.half	.L464-.L463
	.byte	1,5,43,9
	.half	.L465-.L464
	.byte	1,5,6,9
	.half	.L466-.L465
	.byte	3,3,1,5,40,9
	.half	.L268-.L466
	.byte	3,125,1,5,19,9
	.half	.L467-.L268
	.byte	3,6,1,5,23,9
	.half	.L468-.L467
	.byte	1,5,6,9
	.half	.L469-.L468
	.byte	1,5,23,9
	.half	.L470-.L469
	.byte	3,12,1,5,8,9
	.half	.L471-.L470
	.byte	3,119,1,5,6,9
	.half	.L472-.L471
	.byte	1,9
	.half	.L473-.L472
	.byte	3,3,1,5,8,9
	.half	.L474-.L473
	.byte	3,3,1,5,6,9
	.half	.L475-.L474
	.byte	1,5,19,9
	.half	.L476-.L475
	.byte	3,3,1,5,23,9
	.half	.L477-.L476
	.byte	1,5,6,9
	.half	.L478-.L477
	.byte	1,5,8,9
	.half	.L479-.L478
	.byte	3,6,1,9
	.half	.L269-.L479
	.byte	3,125,1,9
	.half	.L480-.L269
	.byte	3,3,1,5,6,3,125,1,5,8,9
	.half	.L481-.L480
	.byte	3,3,1,5,6,9
	.half	.L482-.L481
	.byte	1,5,24,9
	.half	.L483-.L482
	.byte	3,93,1,5,21,9
	.half	.L484-.L483
	.byte	1,5,24,9
	.half	.L485-.L484
	.byte	3,39,1,5,17,9
	.half	.L271-.L485
	.byte	3,1,1,5,19,9
	.half	.L486-.L271
	.byte	3,127,1,5,11,9
	.half	.L28-.L486
	.byte	3,1,1,5,19,9
	.half	.L487-.L28
	.byte	1,5,15,9
	.half	.L488-.L487
	.byte	1,5,8,1,5,19,3,127,1,5,1,7,9
	.half	.L489-.L488
	.byte	3,3,1,7,9
	.half	.L140-.L489
	.byte	0,1,1
.L446:
	.sdecl	'.debug_ranges',debug,cluster('sm3_compress')
	.sect	'.debug_ranges'
.L139:
	.word	-1,.L61,0,.L140-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('sm3_loop')
	.sect	'.debug_info'
.L141:
	.word	342
	.half	3
	.word	.L142
	.byte	4,1
	.byte	'..\\vss_code\\sm3.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L144,.L143
	.byte	2
	.word	.L72
	.byte	3
	.byte	'sm3_loop',0,1,213,2,19
	.word	.L146
	.byte	1,1
	.word	.L63,.L222,.L62
	.byte	4
	.byte	'm',0,1,213,2,45
	.word	.L156,.L223
	.byte	4
	.byte	'm_bytes',0,1,213,2,59
	.word	.L146,.L224
	.byte	4
	.byte	'iv',0,1,213,2,80
	.word	.L191,.L225
	.byte	5
	.word	.L63,.L222
	.byte	6
	.byte	'left_bytes',0,1,215,2,13
	.word	.L146,.L226
	.byte	6
	.byte	'b',0,1,216,2,19
	.word	.L156,.L227
	.byte	6
	.byte	'i',0,1,217,2,13
	.word	.L146,.L228
	.byte	6
	.byte	'w',0,1,220,2,13
	.word	.L229,.L230
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('sm3_loop')
	.sect	'.debug_abbrev'
.L142:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('sm3_loop')
	.sect	'.debug_line'
.L143:
	.word	.L491-.L490
.L490:
	.half	3
	.word	.L493-.L492
.L492:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\sm3.c',0,0,0,0,0
.L493:
	.byte	5,19,7,0,5,2
	.word	.L63
	.byte	3,212,2,1,5,24,9
	.half	.L273-.L63
	.byte	3,2,1,5,21,3,1,1,5,15,9
	.half	.L274-.L273
	.byte	3,1,1,5,22,3,3,1,5,20,9
	.half	.L494-.L274
	.byte	1,9
	.half	.L495-.L494
	.byte	3,4,1,5,23,3,126,1,5,3,9
	.half	.L31-.L495
	.byte	3,2,1,5,11,7,9
	.half	.L496-.L31
	.byte	3,2,1,5,1,3,15,1,5,17,7,9
	.half	.L32-.L496
	.byte	3,116,1,5,20,9
	.half	.L277-.L32
	.byte	3,2,1,5,14,9
	.half	.L279-.L277
	.byte	3,2,1,5,5,3,1,1,5,4,9
	.half	.L497-.L279
	.byte	3,2,1,5,23,9
	.half	.L30-.L497
	.byte	3,114,1,5,2,7,9
	.half	.L498-.L30
	.byte	3,18,1,5,1,3,1,1,7,9
	.half	.L145-.L498
	.byte	0,1,1
.L491:
	.sdecl	'.debug_ranges',debug,cluster('sm3_loop')
	.sect	'.debug_ranges'
.L144:
	.word	-1,.L63,0,.L145-.L63,0,0
	.sdecl	'.debug_loc',debug,cluster('FF')
	.sect	'.debug_loc'
.L46:
	.word	-1,.L47,0,.L170-.L47
	.half	2
	.byte	138,0
	.word	0,0
.L171:
	.word	-1,.L47,0,.L170-.L47
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L172:
	.word	-1,.L47,0,.L231-.L47
	.half	5
	.byte	144,34,157,32,32
	.word	.L7-.L47,.L233-.L47
	.half	5
	.byte	144,34,157,32,32
	.word	.L9-.L47,.L170-.L47
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L173:
	.word	-1,.L47,0,.L232-.L47
	.half	5
	.byte	144,35,157,32,0
	.word	.L9-.L47,.L170-.L47
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L174:
	.word	-1,.L47,0,.L170-.L47
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GG')
	.sect	'.debug_loc'
.L48:
	.word	-1,.L49,0,.L175-.L49
	.half	2
	.byte	138,0
	.word	0,0
.L176:
	.word	-1,.L49,0,.L175-.L49
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L177:
	.word	-1,.L49,0,.L234-.L49
	.half	5
	.byte	144,34,157,32,32
	.word	.L12-.L49,.L236-.L49
	.half	5
	.byte	144,34,157,32,32
	.word	.L14-.L49,.L175-.L49
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L178:
	.word	-1,.L49,0,.L235-.L49
	.half	5
	.byte	144,35,157,32,0
	.word	.L14-.L49,.L175-.L49
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L179:
	.word	-1,.L49,0,.L175-.L49
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('T')
	.sect	'.debug_loc'
.L44:
	.word	-1,.L45,0,.L168-.L45
	.half	2
	.byte	138,0
	.word	0,0
.L169:
	.word	-1,.L45,0,.L168-.L45
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mizar_sm3')
	.sect	'.debug_loc'
.L167:
	.word	-1,.L71,0,.L162-.L71
	.half	3
	.byte	145,216,126
	.word	0,0
.L165:
	.word	-1,.L71,0,.L301-.L71
	.half	1
	.byte	101
	.word	.L304-.L71,.L162-.L71
	.half	1
	.byte	108
	.word	.L307-.L71,.L162-.L71
	.half	1
	.byte	100
	.word	0,0
.L70:
	.word	-1,.L71,0,.L300-.L71
	.half	2
	.byte	138,0
	.word	.L300-.L71,.L162-.L71
	.half	3
	.byte	138,168,1
	.word	.L162-.L71,.L162-.L71
	.half	2
	.byte	138,0
	.word	0,0
.L163:
	.word	-1,.L71,0,.L302-.L71
	.half	1
	.byte	100
	.word	.L303-.L71,.L162-.L71
	.half	1
	.byte	111
	.word	.L305-.L71,.L306-.L71
	.half	1
	.byte	101
	.word	0,0
.L164:
	.word	-1,.L71,0,.L301-.L71
	.half	5
	.byte	144,34,157,32,0
	.word	.L304-.L71,.L162-.L71
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_compress')
	.sect	'.debug_loc'
.L211:
	.word	0,0
.L212:
	.word	0,0
.L213:
	.word	0,0
.L214:
	.word	-1,.L61,.L256-.L61,.L206-.L61
	.half	1
	.byte	108
	.word	0,0
.L215:
	.word	-1,.L61,.L257-.L61,.L206-.L61
	.half	1
	.byte	109
	.word	0,0
.L216:
	.word	-1,.L61,.L258-.L61,.L206-.L61
	.half	1
	.byte	110
	.word	0,0
.L217:
	.word	0,0
.L218:
	.word	-1,.L61,.L261-.L61,.L262-.L61
	.half	5
	.byte	144,33,157,32,0
	.word	.L263-.L61,.L264-.L61
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L219:
	.word	-1,.L61,.L267-.L61,.L206-.L61
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L220:
	.word	-1,.L61,.L268-.L61,.L28-.L61
	.half	5
	.byte	144,39,157,32,32
	.word	.L269-.L61,.L270-.L61
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L221:
	.word	-1,.L61,.L260-.L61,.L206-.L61
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L60:
	.word	-1,.L61,0,.L251-.L61
	.half	2
	.byte	138,0
	.word	.L251-.L61,.L206-.L61
	.half	2
	.byte	138,48
	.word	.L206-.L61,.L206-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L207:
	.word	-1,.L61,0,.L252-.L61
	.half	1
	.byte	100
	.word	.L254-.L61,.L206-.L61
	.half	2
	.byte	145,116
	.word	.L253-.L61,.L259-.L61
	.half	1
	.byte	101
	.word	.L271-.L61,.L206-.L61
	.half	1
	.byte	111
	.word	0,0
.L210:
	.word	-1,.L61,0,.L206-.L61
	.half	2
	.byte	145,80
	.word	0,0
.L208:
	.word	-1,.L61,0,.L253-.L61
	.half	1
	.byte	101
	.word	.L255-.L61,.L206-.L61
	.half	2
	.byte	145,112
	.word	.L265-.L61,.L266-.L61
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_extend')
	.sect	'.debug_loc'
.L202:
	.word	-1,.L59,0,.L249-.L59
	.half	1
	.byte	100
	.word	0,0
.L204:
	.word	-1,.L59,.L25-.L59,.L26-.L59
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L205:
	.word	0,0
.L58:
	.word	-1,.L59,0,.L201-.L59
	.half	2
	.byte	138,0
	.word	0,0
.L203:
	.word	-1,.L59,0,.L249-.L59
	.half	1
	.byte	101
	.word	.L250-.L59,.L201-.L59
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_final')
	.sect	'.debug_loc'
.L160:
	.word	-1,.L69,0,.L292-.L69
	.half	1
	.byte	101
	.word	.L295-.L69,.L158-.L69
	.half	1
	.byte	108
	.word	0,0
.L159:
	.word	-1,.L69,0,.L293-.L69
	.half	1
	.byte	100
	.word	.L294-.L69,.L158-.L69
	.half	1
	.byte	111
	.word	.L298-.L69,.L299-.L69
	.half	1
	.byte	101
	.word	0,0
.L161:
	.word	-1,.L69,.L296-.L69,.L297-.L69
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L68:
	.word	-1,.L69,0,.L158-.L69
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_init')
	.sect	'.debug_loc'
.L149:
	.word	-1,.L65,0,.L280-.L65
	.half	1
	.byte	100
	.word	.L281-.L65,.L147-.L65
	.half	1
	.byte	111
	.word	0,0
.L64:
	.word	-1,.L65,0,.L147-.L65
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_loop')
	.sect	'.debug_loc'
.L227:
	.word	-1,.L63,.L274-.L63,.L222-.L63
	.half	1
	.byte	109
	.word	.L276-.L63,.L277-.L63
	.half	1
	.byte	100
	.word	0,0
.L228:
	.word	-1,.L63,.L275-.L63,.L222-.L63
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L225:
	.word	-1,.L63,0,.L31-.L63
	.half	1
	.byte	101
	.word	.L273-.L63,.L222-.L63
	.half	1
	.byte	108
	.word	.L278-.L63,.L279-.L63
	.half	1
	.byte	100
	.word	0,0
.L226:
	.word	-1,.L63,.L274-.L63,.L222-.L63
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L223:
	.word	-1,.L63,0,.L29-.L63
	.half	1
	.byte	100
	.word	0,0
.L224:
	.word	-1,.L63,0,.L31-.L63
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L62:
	.word	-1,.L63,0,.L272-.L63
	.half	2
	.byte	138,0
	.word	.L272-.L63,.L32-.L63
	.half	3
	.byte	138,144,4
	.word	.L32-.L63,.L32-.L63
	.half	2
	.byte	138,0
	.word	.L32-.L63,.L222-.L63
	.half	3
	.byte	138,144,4
	.word	.L222-.L63,.L222-.L63
	.half	2
	.byte	138,0
	.word	0,0
.L230:
	.word	-1,.L63,0,.L222-.L63
	.half	3
	.byte	145,240,123
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_lshift')
	.sect	'.debug_loc'
.L182:
	.word	-1,.L51,0,.L180-.L51
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L181:
	.word	-1,.L51,0,.L237-.L51
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L50:
	.word	-1,.L51,0,.L180-.L51
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_padding')
	.sect	'.debug_loc'
.L197:
	.word	-1,.L57,.L22-.L57,.L245-.L57
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L198:
	.word	-1,.L57,.L241-.L57,.L194-.L57
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L195:
	.word	-1,.L57,0,.L240-.L57
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L199:
	.word	-1,.L57,.L243-.L57,.L244-.L57
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L196:
	.word	-1,.L57,0,.L240-.L57
	.half	1
	.byte	100
	.word	.L241-.L57,.L242-.L57
	.half	1
	.byte	111
	.word	0,0
.L200:
	.word	-1,.L57,.L246-.L57,.L194-.L57
	.half	1
	.byte	108
	.word	.L240-.L57,.L245-.L57
	.half	1
	.byte	100
	.word	.L247-.L57,.L248-.L57
	.half	1
	.byte	100
	.word	0,0
.L56:
	.word	-1,.L57,0,.L194-.L57
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_rot')
	.sect	'.debug_loc'
.L185:
	.word	0,0
.L184:
	.word	-1,.L53,0,.L183-.L53
	.half	2
	.byte	145,120
	.word	0,.L183-.L53
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L186:
	.word	-1,.L53,0,.L183-.L53
	.half	2
	.byte	145,124
	.word	0,0
.L52:
	.word	-1,.L53,0,.L238-.L53
	.half	2
	.byte	138,0
	.word	.L238-.L53,.L183-.L53
	.half	2
	.byte	138,8
	.word	.L183-.L53,.L183-.L53
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_rot_r')
	.sect	'.debug_loc'
.L190:
	.word	-1,.L55,0,.L20-.L55
	.half	5
	.byte	144,34,157,32,0
	.word	.L239-.L55,.L187-.L55
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L193:
	.word	-1,.L55,.L20-.L55,.L187-.L55
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L189:
	.word	-1,.L55,0,.L20-.L55
	.half	1
	.byte	100
	.word	0,0
.L192:
	.word	-1,.L55,0,.L20-.L55
	.half	1
	.byte	101
	.word	0,0
.L54:
	.word	-1,.L55,0,.L187-.L55
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sm3_update')
	.sect	'.debug_loc'
.L151:
	.word	-1,.L67,0,.L282-.L67
	.half	1
	.byte	100
	.word	.L285-.L67,.L150-.L67
	.half	1
	.byte	111
	.word	0,0
.L153:
	.word	-1,.L67,0,.L283-.L67
	.half	1
	.byte	101
	.word	.L288-.L67,.L150-.L67
	.half	1
	.byte	108
	.word	.L289-.L67,.L38-.L67
	.half	1
	.byte	101
	.word	.L291-.L67,.L40-.L67
	.half	1
	.byte	100
	.word	0,0
.L154:
	.word	-1,.L67,0,.L284-.L67
	.half	5
	.byte	144,34,157,32,0
	.word	.L286-.L67,.L287-.L67
	.half	5
	.byte	144,36,157,32,0
	.word	.L289-.L67,.L38-.L67
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L157:
	.word	0,0
.L155:
	.word	-1,.L67,.L290-.L67,.L39-.L67
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L66:
	.word	-1,.L67,0,.L150-.L67
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L499:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('T')
	.sect	'.debug_frame'
	.word	24
	.word	.L499,.L45,.L168-.L45
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FF')
	.sect	'.debug_frame'
	.word	24
	.word	.L499,.L47,.L170-.L47
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('GG')
	.sect	'.debug_frame'
	.word	24
	.word	.L499,.L49,.L175-.L49
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('sm3_lshift')
	.sect	'.debug_frame'
	.word	24
	.word	.L499,.L51,.L180-.L51
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('sm3_rot')
	.sect	'.debug_frame'
	.word	44
	.word	.L499,.L53,.L183-.L53
	.byte	8,19,8,21,8,22,8,23,4
	.word	(.L238-.L53)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L183-.L238)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('sm3_rot_r')
	.sect	'.debug_frame'
	.word	12
	.word	.L499,.L55,.L187-.L55
	.sdecl	'.debug_frame',debug,cluster('sm3_padding')
	.sect	'.debug_frame'
	.word	12
	.word	.L499,.L57,.L194-.L57
	.sdecl	'.debug_frame',debug,cluster('sm3_extend')
	.sect	'.debug_frame'
	.word	12
	.word	.L499,.L59,.L201-.L59
	.sdecl	'.debug_frame',debug,cluster('sm3_compress')
	.sect	'.debug_frame'
	.word	36
	.word	.L499,.L61,.L206-.L61
	.byte	4
	.word	(.L251-.L61)/2
	.byte	19,48,22,26,3,19,138,48,4
	.word	(.L206-.L251)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('sm3_loop')
	.sect	'.debug_frame'
	.word	56
	.word	.L499,.L63,.L222-.L63
	.byte	4
	.word	(.L272-.L63)/2
	.byte	19,144,4,22,26,4,19,138,144,4,4
	.word	(.L32-.L272)/2
	.byte	19,0,8,26,19,144,4,22,26,4,19,138,144,4,4
	.word	(.L222-.L32)/2
	.byte	19,0,8,26,0
	.sdecl	'.debug_frame',debug,cluster('sm3_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L499,.L65,.L147-.L65
	.sdecl	'.debug_frame',debug,cluster('sm3_update')
	.sect	'.debug_frame'
	.word	12
	.word	.L499,.L67,.L150-.L67
	.sdecl	'.debug_frame',debug,cluster('sm3_final')
	.sect	'.debug_frame'
	.word	12
	.word	.L499,.L69,.L158-.L69
	.sdecl	'.debug_frame',debug,cluster('mizar_sm3')
	.sect	'.debug_frame'
	.word	36
	.word	.L499,.L71,.L162-.L71
	.byte	4
	.word	(.L300-.L71)/2
	.byte	19,168,1,22,26,4,19,138,168,1,4
	.word	(.L162-.L300)/2
	.byte	19,0,8,26

; ..\vss_code\sm3.c	   459  }
; ..\vss_code\sm3.c	   460  
; ..\vss_code\sm3.c	   461  
; ..\vss_code\sm3.c	   462  /*	SM3ժҪ�Ľ���Ϊ32�ֽڣ�256���أ�	*/
; ..\vss_code\sm3.c	   463  #define SM3_DIGEST_LENGTH	(32)
; ..\vss_code\sm3.c	   464  
; ..\vss_code\sm3.c	   465  /*	SM3������Ϣ�Ŀ鳤��Ϊ64�ֽڣ�512���أ������ֵ�ڼ���HMACʱ���õ�	*/
; ..\vss_code\sm3.c	   466  #define SM3_CBLOCK			(64)
; ..\vss_code\sm3.c	   467  
; ..\vss_code\sm3.c	   468  #endif
; ..\vss_code\sm3.c	   469  
; ..\vss_code\sm3.c	   470  #pragma section code restore
; ..\vss_code\sm3.c	   471  
; ..\vss_code\sm3.c	   472  

	; Module end
