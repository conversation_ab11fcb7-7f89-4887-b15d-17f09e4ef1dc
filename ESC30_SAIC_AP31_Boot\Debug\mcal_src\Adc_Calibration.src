	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc31592a -c99 --dep-file=mcal_src\\.Adc_Calibration.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Adc_Calibration.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Adc_Calibration.src ..\\mcal_src\\Adc_Calibration.c"
	.compiler_name		"ctc"
	.name	"Adc_Calibration"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Adc_17_GetStartupCalStatus')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Adc_17_GetStartupCalStatus

; ..\mcal_src\Adc_Calibration.c	     1  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	     2  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	     3  ** Copyright (C) Infineon Technologies (2014)                                 **
; ..\mcal_src\Adc_Calibration.c	     4  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	     5  ** All rights reserved.                                                       **
; ..\mcal_src\Adc_Calibration.c	     6  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Adc_Calibration.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Adc_Calibration.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Adc_Calibration.c	    10  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    11  ********************************************************************************
; ..\mcal_src\Adc_Calibration.c	    12  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    13  **   $FILENAME   : Adc_Calibration.c $                                        **
; ..\mcal_src\Adc_Calibration.c	    14  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    15  **   $CC VERSION : \main\dev_tc23x\6 $                                        **
; ..\mcal_src\Adc_Calibration.c	    16  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    17  **   $DATE       : 2016-03-29 $                                               **
; ..\mcal_src\Adc_Calibration.c	    18  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    19  **   AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Adc_Calibration.c	    20  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    21  **   VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Adc_Calibration.c	    22  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    23  **   DESCRIPTION : This file contains                                         **
; ..\mcal_src\Adc_Calibration.c	    24  **                 - calibration function of Adc.                             **
; ..\mcal_src\Adc_Calibration.c	    25  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    26  **   MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Adc_Calibration.c	    27  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    28  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    29  
; ..\mcal_src\Adc_Calibration.c	    30  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    31  **                      Includes                                              **
; ..\mcal_src\Adc_Calibration.c	    32  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    33  /* Inclusion of Adc_Utility.h */
; ..\mcal_src\Adc_Calibration.c	    34  #include "Adc_Utility.h"
; ..\mcal_src\Adc_Calibration.c	    35  
; ..\mcal_src\Adc_Calibration.c	    36  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    37  **                      Private Macro Definitions                             **
; ..\mcal_src\Adc_Calibration.c	    38  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    39    /* Macros to read CAL and CALS Bitfield of ARBCFG sfr */
; ..\mcal_src\Adc_Calibration.c	    40  #define ADC_CAL_CALS_MASK       (0x30000000U)
; ..\mcal_src\Adc_Calibration.c	    41  #define ADC_CAL_CALS_VALUE      (0x20000000U)
; ..\mcal_src\Adc_Calibration.c	    42  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    43  **                      Private Type Definitions                              **
; ..\mcal_src\Adc_Calibration.c	    44  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    45  
; ..\mcal_src\Adc_Calibration.c	    46  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    47  **                      Private Function Declarations                         **
; ..\mcal_src\Adc_Calibration.c	    48  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    49  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    50  **                      Global Constant Definitions                           **
; ..\mcal_src\Adc_Calibration.c	    51  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    52  
; ..\mcal_src\Adc_Calibration.c	    53  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    54  **                      Global Variable Definitions                           **
; ..\mcal_src\Adc_Calibration.c	    55  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    56  
; ..\mcal_src\Adc_Calibration.c	    57  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    58  **                      Private Constant Definitions                          **
; ..\mcal_src\Adc_Calibration.c	    59  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    60  
; ..\mcal_src\Adc_Calibration.c	    61  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    62  **                      Private Variable Definitions                          **
; ..\mcal_src\Adc_Calibration.c	    63  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    64  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    65  **                      Global Function Definitions                           **
; ..\mcal_src\Adc_Calibration.c	    66  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    67  
; ..\mcal_src\Adc_Calibration.c	    68  #define ADC_START_SEC_CODE
; ..\mcal_src\Adc_Calibration.c	    69  #include "MemMap.h"
; ..\mcal_src\Adc_Calibration.c	    70  /*******************************************************************************
; ..\mcal_src\Adc_Calibration.c	    71  ** Traceabilty      :[cover parentID=DS_NAS_HE2_ADC_PR1784,
; ..\mcal_src\Adc_Calibration.c	    72  DS_NAS_EP_ADC_PR1784]                                                         **
; ..\mcal_src\Adc_Calibration.c	    73  ** Syntax           : Std_ReturnType Adc_17_GetStartupCalStatus               **
; ..\mcal_src\Adc_Calibration.c	    74  **                    (                                                       **
; ..\mcal_src\Adc_Calibration.c	    75  **                       void                                                 **
; ..\mcal_src\Adc_Calibration.c	    76  **                    )                                                       **
; ..\mcal_src\Adc_Calibration.c	    77  **[/cover]                                                                    **
; ..\mcal_src\Adc_Calibration.c	    78  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    79  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Calibration.c	    80  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    81  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Calibration.c	    82  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    83  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Calibration.c	    84  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    85  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Calibration.c	    86  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    87  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Calibration.c	    88  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    89  ** Return value     : E_OK: Startup calibration is completed                  **
; ..\mcal_src\Adc_Calibration.c	    90  **                    E_NOT_OK: Startup calibration is ongoing                **
; ..\mcal_src\Adc_Calibration.c	    91  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    92  ** Description      : Api checks the end of startup calibration               **
; ..\mcal_src\Adc_Calibration.c	    93  **                                                                            **
; ..\mcal_src\Adc_Calibration.c	    94  *******************************************************************************/
; ..\mcal_src\Adc_Calibration.c	    95  Std_ReturnType Adc_17_GetStartupCalStatus(void)
; Function Adc_17_GetStartupCalStatus
.L9:
Adc_17_GetStartupCalStatus:	.type	func

; ..\mcal_src\Adc_Calibration.c	    96  {
; ..\mcal_src\Adc_Calibration.c	    97    Std_ReturnType Status;
; ..\mcal_src\Adc_Calibration.c	    98    uint8          LoopCtr;
; ..\mcal_src\Adc_Calibration.c	    99  
; ..\mcal_src\Adc_Calibration.c	   100    Status = E_OK;
	mov	d2,#0

; ..\mcal_src\Adc_Utility.h	     1  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	     2  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_src\Adc_Utility.h	     4  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     5  ** All rights reserved.                                                       **
; ..\mcal_src\Adc_Utility.h	     6  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Adc_Utility.h	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Adc_Utility.h	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Adc_Utility.h	    10  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    11  ********************************************************************************
; ..\mcal_src\Adc_Utility.h	    12  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    13  **   $FILENAME   : Adc_Utility.h $                                            **
; ..\mcal_src\Adc_Utility.h	    14  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    15  **   $CC VERSION : \main\16 $                                                 **
; ..\mcal_src\Adc_Utility.h	    16  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    17  **   $DATE       : 2017-04-06 $                                               **
; ..\mcal_src\Adc_Utility.h	    18  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    19  **   AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Adc_Utility.h	    20  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    21  **   VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Adc_Utility.h	    22  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    23  **   DESCRIPTION : This file contains                                         **
; ..\mcal_src\Adc_Utility.h	    24  **                 - functionality of Adc driver.                             **
; ..\mcal_src\Adc_Utility.h	    25  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    26  **   MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Adc_Utility.h	    27  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    28  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    29  
; ..\mcal_src\Adc_Utility.h	    30  #ifndef ADC_UTILITY_H
; ..\mcal_src\Adc_Utility.h	    31  #define ADC_UTILITY_H
; ..\mcal_src\Adc_Utility.h	    32  
; ..\mcal_src\Adc_Utility.h	    33  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	    34  **                      Includes                                              **
; ..\mcal_src\Adc_Utility.h	    35  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    36  /* Import the Hw handle functions */
; ..\mcal_src\Adc_Utility.h	    37  #include "Adc_HwHandle.h"
; ..\mcal_src\Adc_Utility.h	    38  #if (ADC_GROUP_EMUX_SCAN == STD_ON)
; ..\mcal_src\Adc_Utility.h	    39  /* Import DMA channel information */
; ..\mcal_src\Adc_Utility.h	    40  #include "Mcal_DmaLib.h"
; ..\mcal_src\Adc_Utility.h	    41  #endif /* (ADC_GROUP_EMUX_SCAN == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    42  /* Import the Conversion handle functions */
; ..\mcal_src\Adc_Utility.h	    43  #include "Adc_ConvHandle.h"
; ..\mcal_src\Adc_Utility.h	    44  
; ..\mcal_src\Adc_Utility.h	    45  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	    46  **                      Global Macro Definitions                              **
; ..\mcal_src\Adc_Utility.h	    47  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    48  /* Invalid values for Global variables */
; ..\mcal_src\Adc_Utility.h	    49  #if (ADC_RESULT_HANDLING_MODE == ADC_NON_AUTOSAR)
; ..\mcal_src\Adc_Utility.h	    50  
; ..\mcal_src\Adc_Utility.h	    51  #define ADC_INVALID_CHANNEL              ((Adc_ChannelType)0xFF)
; ..\mcal_src\Adc_Utility.h	    52  
; ..\mcal_src\Adc_Utility.h	    53  #endif /* (ADC_RESULT_HANDLING_MODE == ADC_NON_AUTOSAR) */
; ..\mcal_src\Adc_Utility.h	    54  
; ..\mcal_src\Adc_Utility.h	    55  #define ADC_INVALID_PRIORITY             ((Adc_GroupPriorityType)0xFF)
; ..\mcal_src\Adc_Utility.h	    56  
; ..\mcal_src\Adc_Utility.h	    57  /* Flag value to disable interrupt service */
; ..\mcal_src\Adc_Utility.h	    58  #define ADC_NO_SERVICE                   ((uint8)0x01)
; ..\mcal_src\Adc_Utility.h	    59  
; ..\mcal_src\Adc_Utility.h	    60  /*
; ..\mcal_src\Adc_Utility.h	    61    Macros to define the Position of channel configuration parameters
; ..\mcal_src\Adc_Utility.h	    62  */
; ..\mcal_src\Adc_Utility.h	    63  #define ADC_POS_CH_CFG_INT_CH       (16U)
; ..\mcal_src\Adc_Utility.h	    64  
; ..\mcal_src\Adc_Utility.h	    65  /* Status of group in scheduler either in No priority with Queue or
; ..\mcal_src\Adc_Utility.h	    66     HW SW Priority
; ..\mcal_src\Adc_Utility.h	    67  */
; ..\mcal_src\Adc_Utility.h	    68  #define ADC_START_CONVERSION        ((uint32)0x01)
; ..\mcal_src\Adc_Utility.h	    69  
; ..\mcal_src\Adc_Utility.h	    70  #if (ADC_ENABLE_QUEUING == STD_ON)
; ..\mcal_src\Adc_Utility.h	    71  #define ADC_STOP_CONVERSION         ((uint32)0x01)
; ..\mcal_src\Adc_Utility.h	    72  #endif /* (ADC_ENABLE_QUEUING == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    73  
; ..\mcal_src\Adc_Utility.h	    74  /* SW priority considered */
; ..\mcal_src\Adc_Utility.h	    75  #if (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW)
; ..\mcal_src\Adc_Utility.h	    76  
; ..\mcal_src\Adc_Utility.h	    77  /* Macro for Maximum Group Possible */
; ..\mcal_src\Adc_Utility.h	    78  #define ADC_MAX_GROUP_POSSIBLE      ((uint32)32)
; ..\mcal_src\Adc_Utility.h	    79  
; ..\mcal_src\Adc_Utility.h	    80  #endif /* (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW) */
; ..\mcal_src\Adc_Utility.h	    81  
; ..\mcal_src\Adc_Utility.h	    82  /* Group ID which is invalid */
; ..\mcal_src\Adc_Utility.h	    83  #define ADC_INVALID_GROUP_ID        ((Adc_GroupType)0xFF)
; ..\mcal_src\Adc_Utility.h	    84  
; ..\mcal_src\Adc_Utility.h	    85  #if (ADC_GROUP_EMUX_SCAN == STD_ON)
; ..\mcal_src\Adc_Utility.h	    86  
; ..\mcal_src\Adc_Utility.h	    87  /* DMA Related macros */
; ..\mcal_src\Adc_Utility.h	    88  
; ..\mcal_src\Adc_Utility.h	    89  /* Dma usage flag set */
; ..\mcal_src\Adc_Utility.h	    90  #define ADC_DMA_IN_USE              ((uint8)1)
; ..\mcal_src\Adc_Utility.h	    91  
; ..\mcal_src\Adc_Utility.h	    92  /* Dma usage flag reset */
; ..\mcal_src\Adc_Utility.h	    93  #define ADC_DMA_NOT_IN_USE          ((uint8)0)
; ..\mcal_src\Adc_Utility.h	    94  
; ..\mcal_src\Adc_Utility.h	    95  #endif /* (ADC_GROUP_EMUX_SCAN == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    96  
; ..\mcal_src\Adc_Utility.h	    97  #if (ADC_USE_EMUX == STD_ON)
; ..\mcal_src\Adc_Utility.h	    98  #define ADC_POS_EMUX_ENABLE    (3U)
; ..\mcal_src\Adc_Utility.h	    99  #endif /* (ADC_USE_EMUX == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   100  
; ..\mcal_src\Adc_Utility.h	   101  /*
; ..\mcal_src\Adc_Utility.h	   102  Get ADC Kernel number from Group Id. Bit 5 , 6 , 7 and 8 will give the
; ..\mcal_src\Adc_Utility.h	   103  5 - start from bit number 5
; ..\mcal_src\Adc_Utility.h	   104  4 - no. of bits to extract
; ..\mcal_src\Adc_Utility.h	   105  */
; ..\mcal_src\Adc_Utility.h	   106  #define ADC_KERNEL_NUM_START_BIT    (5U)
; ..\mcal_src\Adc_Utility.h	   107  #define ADC_KERNEL_NUM_OF_BITS      (4U)
; ..\mcal_src\Adc_Utility.h	   108  
; ..\mcal_src\Adc_Utility.h	   109  /*
; ..\mcal_src\Adc_Utility.h	   110  Get ADC Kernel Group Id. Bit 0 to 4 will give the Kernel Group Id
; ..\mcal_src\Adc_Utility.h	   111  */
; ..\mcal_src\Adc_Utility.h	   112  #define ADC_KERNEL_GROUP_ID_MASK    (0x1FU)
; ..\mcal_src\Adc_Utility.h	   113  
; ..\mcal_src\Adc_Utility.h	   114  /*
; ..\mcal_src\Adc_Utility.h	   115  Get ADC Kernel Channel number. Bit 12 to 15 will give the channel number
; ..\mcal_src\Adc_Utility.h	   116  */
; ..\mcal_src\Adc_Utility.h	   117  #define ADC_CHANNEL_ID_MASK         (0x0FU)
; ..\mcal_src\Adc_Utility.h	   118  
; ..\mcal_src\Adc_Utility.h	   119  #if ((ADC_HW_TRIGGER_API == STD_ON) && (ADC_TIMER_TRIGG == STD_ON))
; ..\mcal_src\Adc_Utility.h	   120  /*
; ..\mcal_src\Adc_Utility.h	   121  Get Group for HW trigger source type (Timer or External Request)
; ..\mcal_src\Adc_Utility.h	   122  4U - From bit number 4 to be extracted
; ..\mcal_src\Adc_Utility.h	   123  1U - 1 bit to be extracted
; ..\mcal_src\Adc_Utility.h	   124  */
; ..\mcal_src\Adc_Utility.h	   125  #define ADC_TIMER_ENABLE_START_BIT   (4U)
; ..\mcal_src\Adc_Utility.h	   126  #define ADC_TIMER_ENABLE_NUM_OF_BITS (1U)
; ..\mcal_src\Adc_Utility.h	   127  
; ..\mcal_src\Adc_Utility.h	   128  #endif /*(ADC_HW_TRIGGER_API == STD_ON && ADC_TIMER_TRIGG == STD_ON)*/
; ..\mcal_src\Adc_Utility.h	   129  
; ..\mcal_src\Adc_Utility.h	   130  /*
; ..\mcal_src\Adc_Utility.h	   131  Get ENGT value of Request source from user configured hw data
; ..\mcal_src\Adc_Utility.h	   132  5U - From bit number 5 to be extracted
; ..\mcal_src\Adc_Utility.h	   133  2U - 2 bits to be extracted
; ..\mcal_src\Adc_Utility.h	   134  */
; ..\mcal_src\Adc_Utility.h	   135  #define ADC_ENGT_START_BIT          (5U)
; ..\mcal_src\Adc_Utility.h	   136  #define ADC_ENGT_NUM_OF_BITS        (2U)
; ..\mcal_src\Adc_Utility.h	   137  
; ..\mcal_src\Adc_Utility.h	   138  #if ((ADC_HW_TRIGGER_API == STD_ON) && ((ADC_ERU_TRIGG == STD_ON) || \ 
; ..\mcal_src\Adc_Utility.h	   139                                                    (ADC_ERU_GATING == STD_ON)) )
; ..\mcal_src\Adc_Utility.h	   140  /* Get Flag for Eru trigger/Gate type */
; ..\mcal_src\Adc_Utility.h	   141  #define ADC_ERU_SELECT_NUM_OF_BITS  (1U)
; ..\mcal_src\Adc_Utility.h	   142  /* Get Eru Unit used for trigger */
; ..\mcal_src\Adc_Utility.h	   143  #define ADC_ERU_UNIT_NUM_OF_BITS    (3U)
; ..\mcal_src\Adc_Utility.h	   144  
; ..\mcal_src\Adc_Utility.h	   145  #endif /* (ADC_HW_TRIGGER_API == STD_ON && ADC_ERU_TRIGG == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   146  
; ..\mcal_src\Adc_Utility.h	   147  #if (ADC_USE_EMUX == STD_ON)
; ..\mcal_src\Adc_Utility.h	   148  /* Get Emux Select value from EmuxData */
; ..\mcal_src\Adc_Utility.h	   149  #define ADC_EMUX_SELECT_MASK        (0x07U)
; ..\mcal_src\Adc_Utility.h	   150  
; ..\mcal_src\Adc_Utility.h	   151  /* Get Emux enabled or not from EmuxData */
; ..\mcal_src\Adc_Utility.h	   152  #define ADC_EMUX_ENABLE_NUM_OF_BITS (1U)
; ..\mcal_src\Adc_Utility.h	   153  
; ..\mcal_src\Adc_Utility.h	   154  #endif /* (ADC_USE_EMUX == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   155  
; ..\mcal_src\Adc_Utility.h	   156  #if (ADC_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Adc_Utility.h	   157  /* Status to indicate that ADC is initialized */
; ..\mcal_src\Adc_Utility.h	   158  #define ADC_INITIALIZED  ((uint8)1)
; ..\mcal_src\Adc_Utility.h	   159  #endif /* (ADC_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   160  
; ..\mcal_src\Adc_Utility.h	   161  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   162  **                      Global Type Definitions                               **
; ..\mcal_src\Adc_Utility.h	   163  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   164  
; ..\mcal_src\Adc_Utility.h	   165  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   166  **                      Global Constant Declarations                          **
; ..\mcal_src\Adc_Utility.h	   167  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   168  
; ..\mcal_src\Adc_Utility.h	   169  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   170  **                      Global Variable Declarations                          **
; ..\mcal_src\Adc_Utility.h	   171  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   172  #define ADC_START_SEC_VAR_8BIT
; ..\mcal_src\Adc_Utility.h	   173  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   174  
; ..\mcal_src\Adc_Utility.h	   175  #if (ADC_REQSRC2 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Utility.h	   176  /*IFX_MISRA_RULE_08_08_STATUS=Adc_BgndScanData is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   177   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   178  extern uint8 Adc_BgndScanData;
; ..\mcal_src\Adc_Utility.h	   179  /*IFX_MISRA_RULE_08_08_STATUS=Adc_BgndGrpCnt is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   180   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   181  extern uint8 Adc_BgndGrpCnt;
; ..\mcal_src\Adc_Utility.h	   182  #endif /* (ADC_REQSRC2 == ADC_REQSRC_USED) */
; ..\mcal_src\Adc_Utility.h	   183  
; ..\mcal_src\Adc_Utility.h	   184  #if (ADC_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Adc_Utility.h	   185  /*IFX_MISRA_RULE_08_08_STATUS=Adc_InitStatus is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   186   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   187  extern uint8 Adc_InitStatus;
; ..\mcal_src\Adc_Utility.h	   188  #endif /* (ADC_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   189  
; ..\mcal_src\Adc_Utility.h	   190  #define ADC_STOP_SEC_VAR_8BIT
; ..\mcal_src\Adc_Utility.h	   191  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   192  
; ..\mcal_src\Adc_Utility.h	   193  #if (ADC_PB_FIXED_ADDRESS == STD_OFF)
; ..\mcal_src\Adc_Utility.h	   194  #define ADC_START_SEC_VAR_32BIT
; ..\mcal_src\Adc_Utility.h	   195  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   196  
; ..\mcal_src\Adc_Utility.h	   197  /* To store the Adc driver configuration pointer */
; ..\mcal_src\Adc_Utility.h	   198  /*IFX_MISRA_RULE_08_08_STATUS=Adc_kConfigPtr is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   199   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   200  extern const Adc_ConfigType  *Adc_kConfigPtr;
; ..\mcal_src\Adc_Utility.h	   201  
; ..\mcal_src\Adc_Utility.h	   202  #define ADC_STOP_SEC_VAR_32BIT
; ..\mcal_src\Adc_Utility.h	   203  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   204  #endif /*(ADC_PB_FIXED_ADDRESS == STD_OFF)*/
; ..\mcal_src\Adc_Utility.h	   205  
; ..\mcal_src\Adc_Utility.h	   206  #if(ADC_PB_FIXED_ADDRESS == STD_ON)
; ..\mcal_src\Adc_Utility.h	   207  #define ADC_START_SEC_CONST_32BIT
; ..\mcal_src\Adc_Utility.h	   208  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   209  
; ..\mcal_src\Adc_Utility.h	   210  /* To store the Adc driver configuration pointer */
; ..\mcal_src\Adc_Utility.h	   211  extern const Adc_ConfigType * const Adc_kConfigPtr;
; ..\mcal_src\Adc_Utility.h	   212  
; ..\mcal_src\Adc_Utility.h	   213  #define ADC_STOP_SEC_CONST_32BIT
; ..\mcal_src\Adc_Utility.h	   214  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   215  #endif /*(ADC_PB_FIXED_ADDRESS == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   216  
; ..\mcal_src\Adc_Utility.h	   217  #if ( (ADC_QM_KERNEL_USED_COUNT != 0U ) && (ADC_REQSRC2 == ADC_REQSRC_USED) )
; ..\mcal_src\Adc_Utility.h	   218  #define ADC_START_SEC_VAR_UNSPECIFIED
; ..\mcal_src\Adc_Utility.h	   219  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   220  
; ..\mcal_src\Adc_Utility.h	   221  /* data variable for QM signals */
; ..\mcal_src\Adc_Utility.h	   222  /*IFX_MISRA_RULE_08_08_STATUS=Adc_QmSignal is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   223   is defined in Adc_ConvHandle.c. This violation is reported incorrectly by
; ..\mcal_src\Adc_Utility.h	   224   PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   225  extern Adc_GlobalDataType Adc_QmSignal[ADC_QM_KERNEL_USED_COUNT];
; ..\mcal_src\Adc_Utility.h	   226  
; ..\mcal_src\Adc_Utility.h	   227  #define ADC_STOP_SEC_VAR_UNSPECIFIED
; ..\mcal_src\Adc_Utility.h	   228  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   229  #endif /* (ADC_QM_KERNEL_USED_COUNT != 0U )&&(ADC_REQSRC2 == ADC_REQSRC_USED) */
; ..\mcal_src\Adc_Utility.h	   230  
; ..\mcal_src\Adc_Utility.h	   231  
; ..\mcal_src\Adc_Utility.h	   232  #define ADC_START_SEC_CODE
; ..\mcal_src\Adc_Utility.h	   233  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   234  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   235  **                      Global Function Declarations                          **
; ..\mcal_src\Adc_Utility.h	   236  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   237  
; ..\mcal_src\Adc_Utility.h	   238  /* Enable/Disable the use of the function */
; ..\mcal_src\Adc_Utility.h	   239  #if (ADC_ENABLE_START_STOP_GROUP_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   240  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   241  ** Syntax           : extern void Adc_lSchmEnterStartGroup                    **
; ..\mcal_src\Adc_Utility.h	   242  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   243  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   244  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   245  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   246  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   247  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   248  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   249  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   250  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   251  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   252  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   253  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   254  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   255  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   256  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   257  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   258  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   259  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   260  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   261  extern void Adc_lSchmEnterStartGroup(void);
; ..\mcal_src\Adc_Utility.h	   262  
; ..\mcal_src\Adc_Utility.h	   263  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   264  ** Syntax           : extern void Adc_lSchmExitStartGroup                     **
; ..\mcal_src\Adc_Utility.h	   265  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   266  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   267  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   268  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   269  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   270  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   271  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   272  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   273  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   274  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   275  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   276  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   277  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   278  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   279  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   280  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   281  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   282  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   283  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   284  extern void Adc_lSchmExitStartGroup(void);
; ..\mcal_src\Adc_Utility.h	   285  
; ..\mcal_src\Adc_Utility.h	   286  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   287  ** Syntax           : extern void Adc_lSchmEnterStopGroup                     **
; ..\mcal_src\Adc_Utility.h	   288  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   289  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   290  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   291  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   292  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   293  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   294  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   295  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   296  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   297  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   298  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   299  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   300  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   301  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   302  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   303  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   304  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   305  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   306  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   307  extern void Adc_lSchmEnterStopGroup(void);
; ..\mcal_src\Adc_Utility.h	   308  
; ..\mcal_src\Adc_Utility.h	   309  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   310  ** Syntax           : extern void Adc_lSchmExitStopGroup                      **
; ..\mcal_src\Adc_Utility.h	   311  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   312  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   313  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   314  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   315  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   316  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   317  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   318  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   319  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   320  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   321  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   322  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   323  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   324  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   325  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   326  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   327  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   328  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   329  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   330  extern void Adc_lSchmExitStopGroup(void);
; ..\mcal_src\Adc_Utility.h	   331  
; ..\mcal_src\Adc_Utility.h	   332  #endif /* (ADC_ENABLE_START_STOP_GROUP_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   333  
; ..\mcal_src\Adc_Utility.h	   334  /* Enable/Disable the use of the function */
; ..\mcal_src\Adc_Utility.h	   335  #if (ADC_HW_TRIGGER_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   336  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   337  ** Syntax           : extern void Adc_lSchmEnterEnableHwTrig                  **
; ..\mcal_src\Adc_Utility.h	   338  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   339  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   340  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   341  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   342  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   343  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   344  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   345  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   346  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   347  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   348  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   349  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   350  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   351  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   352  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   353  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   354  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   355  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   356  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   357  extern void Adc_lSchmEnterEnableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   358  
; ..\mcal_src\Adc_Utility.h	   359  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   360  ** Syntax           : extern void Adc_lSchmExitEnableHwTrig                   **
; ..\mcal_src\Adc_Utility.h	   361  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   362  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   363  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   364  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   365  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   366  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   367  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   368  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   369  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   370  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   371  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   372  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   373  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   374  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   375  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   376  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   377  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   378  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   379  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   380  extern void Adc_lSchmExitEnableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   381  
; ..\mcal_src\Adc_Utility.h	   382  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   383  ** Syntax           : extern void Adc_lSchmEnterDisableHwTrig                 **
; ..\mcal_src\Adc_Utility.h	   384  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   385  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   386  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   387  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   388  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   389  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   390  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   391  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   392  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   393  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   394  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   395  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   396  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   397  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   398  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   399  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   400  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   401  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   402  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   403  extern void Adc_lSchmEnterDisableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   404  
; ..\mcal_src\Adc_Utility.h	   405  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   406  ** Syntax           : extern void Adc_lSchmExitDisableHwTrig                  **
; ..\mcal_src\Adc_Utility.h	   407  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   408  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   409  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   410  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   411  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   412  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   413  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   414  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   415  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   416  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   417  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   418  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   419  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   420  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   421  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   422  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   423  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   424  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   425  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   426  extern void Adc_lSchmExitDisableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   427  
; ..\mcal_src\Adc_Utility.h	   428  #endif /* (ADC_HW_TRIGGER_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   429  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   430  ** Syntax           : extern void Adc_lSchmEnterGetGrpStatus                  **
; ..\mcal_src\Adc_Utility.h	   431  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   432  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   433  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   434  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   435  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   436  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   437  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   438  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   439  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   440  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   441  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   442  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   443  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   444  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   445  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   446  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   447  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   448  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   449  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   450  extern void Adc_lSchmEnterGetGrpStatus(void);
; ..\mcal_src\Adc_Utility.h	   451  
; ..\mcal_src\Adc_Utility.h	   452  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   453  ** Syntax           : extern void Adc_lSchmExitGetGrpStatus                   **
; ..\mcal_src\Adc_Utility.h	   454  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   455  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   456  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   457  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   458  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   459  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   460  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   461  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   462  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   463  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   464  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   465  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   466  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   467  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   468  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   469  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   470  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   471  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   472  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   473  extern void Adc_lSchmExitGetGrpStatus(void);
; ..\mcal_src\Adc_Utility.h	   474  
; ..\mcal_src\Adc_Utility.h	   475  #if (ADC_RESULT_HANDLING_MODE == ADC_AUTOSAR)
; ..\mcal_src\Adc_Utility.h	   476  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   477  ** Syntax           : extern void Adc_lSchmEnterGetStreamLastPtr              **
; ..\mcal_src\Adc_Utility.h	   478  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   479  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   480  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   481  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   482  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   483  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   484  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   485  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   486  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   487  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   488  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   489  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   490  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   491  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   492  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   493  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   494  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   495  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   496  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   497  extern void Adc_lSchmEnterGetStreamLastPtr(void);
; ..\mcal_src\Adc_Utility.h	   498  
; ..\mcal_src\Adc_Utility.h	   499  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   500  ** Syntax           : extern void Adc_lSchmExitGetStreamLastPtr               **
; ..\mcal_src\Adc_Utility.h	   501  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   502  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   503  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   504  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   505  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   506  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   507  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   508  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   509  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   510  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   511  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   512  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   513  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   514  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   515  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   516  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   517  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   518  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   519  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   520  extern void Adc_lSchmExitGetStreamLastPtr(void);
; ..\mcal_src\Adc_Utility.h	   521  
; ..\mcal_src\Adc_Utility.h	   522  #if (ADC_READ_GROUP_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   523  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   524  ** Syntax           : extern void Adc_lSchmEnterReadGroup                     **
; ..\mcal_src\Adc_Utility.h	   525  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   526  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   527  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   528  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   529  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   530  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   531  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   532  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   533  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   534  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   535  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   536  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   537  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   538  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   539  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   540  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   541  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   542  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   543  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   544  extern void Adc_lSchmEnterReadGroup(void);
; ..\mcal_src\Adc_Utility.h	   545  
; ..\mcal_src\Adc_Utility.h	   546  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   547  ** Syntax           : extern void Adc_lSchmExitReadGroup                      **
; ..\mcal_src\Adc_Utility.h	   548  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   549  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   550  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   551  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   552  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   553  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   554  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   555  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   556  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   557  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   558  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   559  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   560  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   561  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   562  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   563  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   564  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   565  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   566  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   567  extern void Adc_lSchmExitReadGroup(void);
; ..\mcal_src\Adc_Utility.h	   568  
; ..\mcal_src\Adc_Utility.h	   569  #endif /* (ADC_READ_GROUP_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   570  #endif /* (ADC_RESULT_HANDLING_MODE == ADC_AUTOSAR) */
; ..\mcal_src\Adc_Utility.h	   571  
; ..\mcal_src\Adc_Utility.h	   572  /* Queue Enable is STD_ON only during No priority */
; ..\mcal_src\Adc_Utility.h	   573  #if (ADC_ENABLE_QUEUING == STD_ON)
; ..\mcal_src\Adc_Utility.h	   574  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   575  ** Syntax           : extern void Adc_lSchmEnterPopQueue                      **
; ..\mcal_src\Adc_Utility.h	   576  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   577  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   578  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   579  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   580  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   581  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   582  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   583  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   584  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   585  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   586  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   587  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   588  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   589  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   590  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   591  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   592  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   593  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   594  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   595  extern void Adc_lSchmEnterPopQueue(void);
; ..\mcal_src\Adc_Utility.h	   596  
; ..\mcal_src\Adc_Utility.h	   597  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   598  ** Syntax           : extern void Adc_lSchmExitPopQueue                       **
; ..\mcal_src\Adc_Utility.h	   599  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   600  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   601  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   602  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   603  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   604  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   605  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   606  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   607  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   608  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   609  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   610  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   611  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   612  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   613  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   614  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   615  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   616  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   617  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   618  extern void Adc_lSchmExitPopQueue(void);
; ..\mcal_src\Adc_Utility.h	   619  
; ..\mcal_src\Adc_Utility.h	   620  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   621  ** Syntax           : extern void Adc_lSchmEnterPushQueue                     **
; ..\mcal_src\Adc_Utility.h	   622  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   623  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   624  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   625  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   626  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   627  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   628  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   629  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   630  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   631  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   632  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   633  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   634  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   635  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   636  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   637  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   638  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   639  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   640  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   641  extern void Adc_lSchmEnterPushQueue(void);
; ..\mcal_src\Adc_Utility.h	   642  
; ..\mcal_src\Adc_Utility.h	   643  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   644  ** Syntax           : extern void Adc_lSchmExitPushQueue                      **
; ..\mcal_src\Adc_Utility.h	   645  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   646  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   647  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   648  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   649  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   650  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   651  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   652  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   653  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   654  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   655  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   656  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   657  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   658  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   659  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   660  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   661  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   662  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   663  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   664  extern void Adc_lSchmExitPushQueue(void);
; ..\mcal_src\Adc_Utility.h	   665  #endif /* (ADC_ENABLE_QUEUING == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   666  
; ..\mcal_src\Adc_Utility.h	   667  /* Full priority considered */
; ..\mcal_src\Adc_Utility.h	   668  #if (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW)
; ..\mcal_src\Adc_Utility.h	   669  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   670  ** Syntax           : extern void Adc_lSchmEnterScheduleNext                  **
; ..\mcal_src\Adc_Utility.h	   671  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   672  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   673  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   674  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   675  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   676  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   677  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   678  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   679  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   680  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   681  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   682  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   683  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   684  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   685  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   686  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   687  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   688  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   689  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   690  extern void Adc_lSchmEnterScheduleNext(void);
; ..\mcal_src\Adc_Utility.h	   691  
; ..\mcal_src\Adc_Utility.h	   692  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   693  ** Syntax           : extern void Adc_lSchmExitScheduleNext                   **
; ..\mcal_src\Adc_Utility.h	   694  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   695  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   696  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   697  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   698  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   699  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   700  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   701  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   702  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   703  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   704  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   705  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   706  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   707  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   708  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   709  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   710  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   711  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   712  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   713  extern void Adc_lSchmExitScheduleNext(void);
; ..\mcal_src\Adc_Utility.h	   714  
; ..\mcal_src\Adc_Utility.h	   715  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   716  ** Syntax           : extern void Adc_lSchmEnterScheduleStart                 **
; ..\mcal_src\Adc_Utility.h	   717  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   718  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   719  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   720  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   721  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   722  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   723  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   724  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   725  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   726  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   727  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   728  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   729  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   730  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   731  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   732  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   733  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   734  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   735  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   736  extern void Adc_lSchmEnterScheduleStart(void);
; ..\mcal_src\Adc_Utility.h	   737  
; ..\mcal_src\Adc_Utility.h	   738  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   739  ** Syntax           : extern void Adc_lSchmExitScheduleStart                  **
; ..\mcal_src\Adc_Utility.h	   740  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   741  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   742  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   743  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   744  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   745  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   746  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   747  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   748  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   749  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   750  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   751  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   752  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   753  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   754  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   755  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   756  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   757  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   758  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   759  extern void Adc_lSchmExitScheduleStart(void);
; ..\mcal_src\Adc_Utility.h	   760  
; ..\mcal_src\Adc_Utility.h	   761  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   762  ** Syntax           : extern void Adc_lSchmEnterScheduleStop                  **
; ..\mcal_src\Adc_Utility.h	   763  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   764  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   765  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   766  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   767  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   768  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   769  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   770  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   771  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   772  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   773  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   774  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   775  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   776  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   777  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   778  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   779  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   780  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   781  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   782  extern void Adc_lSchmEnterScheduleStop(void);
; ..\mcal_src\Adc_Utility.h	   783  
; ..\mcal_src\Adc_Utility.h	   784  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   785  ** Syntax           : extern void Adc_lSchmExitScheduleStop                   **
; ..\mcal_src\Adc_Utility.h	   786  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   787  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   788  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   789  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   790  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   791  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   792  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   793  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   794  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   795  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   796  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   797  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   798  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   799  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   800  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   801  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   802  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   803  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   804  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   805  extern void Adc_lSchmExitScheduleStop(void);
; ..\mcal_src\Adc_Utility.h	   806  
; ..\mcal_src\Adc_Utility.h	   807  #endif /* (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW) */
; ..\mcal_src\Adc_Utility.h	   808  
; ..\mcal_src\Adc_Utility.h	   809  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   810  **                      Global Inline Function Definitions                    **
; ..\mcal_src\Adc_Utility.h	   811  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   812  
; ..\mcal_src\Adc_Utility.h	   813  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   814  ** Syntax           : _IFXEXTERN_ IFX_INLINE uint16 Adc_lKernelConfigured     **
; ..\mcal_src\Adc_Utility.h	   815  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   816  **                      uint8 Kernel                                          **
; ..\mcal_src\Adc_Utility.h	   817  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   818  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   819  ** Service ID       : None                                                    **
; ..\mcal_src\Adc_Utility.h	   820  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   821  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   822  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   823  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   824  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   825  ** Parameters (in)  : Kernel - Adc Kernel to be checked                       **
; ..\mcal_src\Adc_Utility.h	   826  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   827  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   828  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   829  ** Return value     : RetVal                                                  **
; ..\mcal_src\Adc_Utility.h	   830  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   831  ** Description      :                                                         **
; ..\mcal_src\Adc_Utility.h	   832  ** - The function is to check if the Adc kernel is configured or not          **
; ..\mcal_src\Adc_Utility.h	   833  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   834  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   835  /*IFX_MISRA_RULE_08_05_STATUS=Allowed for inline functions defined in header
; ..\mcal_src\Adc_Utility.h	   836   files*/
; ..\mcal_src\Adc_Utility.h	   837  _IFXEXTERN_ IFX_INLINE uint16 Adc_lKernelConfigured(uint8 Kernel)
; ..\mcal_src\Adc_Utility.h	   838  {
; ..\mcal_src\Adc_Utility.h	   839    /*IFX_MISRA_RULE_08_05_STATUS=Allowed for inline functions defined in header
; ..\mcal_src\Adc_Utility.h	   840     files*/
; ..\mcal_src\Adc_Utility.h	   841    uint16 RetVal;
; ..\mcal_src\Adc_Utility.h	   842  
; ..\mcal_src\Adc_Utility.h	   843    RetVal = (uint16)( (uint16)((1UL) << (Kernel)) & ((uint16)ADC_USED_KERNELS) );
; ..\mcal_src\Adc_Utility.h	   844  
; ..\mcal_src\Adc_Utility.h	   845    if (RetVal != 0U)
; ..\mcal_src\Adc_Utility.h	   846    {
; ..\mcal_src\Adc_Utility.h	   847      /* UTP AI00133878 */
; ..\mcal_src\Adc_Utility.h	   848      if (Adc_kConfigPtr->CfgPtr[Kernel] == NULL_PTR)
	movh.a	a15,#@his(Adc_kConfigPtr)
.L30:

; ..\mcal_src\Adc_Calibration.c	   101    
; ..\mcal_src\Adc_Calibration.c	   102    LoopCtr = 0U;
	mov	d0,d2

; ..\mcal_src\Adc_Utility.h	     1  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	     2  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_src\Adc_Utility.h	     4  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     5  ** All rights reserved.                                                       **
; ..\mcal_src\Adc_Utility.h	     6  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Adc_Utility.h	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Adc_Utility.h	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Adc_Utility.h	    10  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    11  ********************************************************************************
; ..\mcal_src\Adc_Utility.h	    12  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    13  **   $FILENAME   : Adc_Utility.h $                                            **
; ..\mcal_src\Adc_Utility.h	    14  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    15  **   $CC VERSION : \main\16 $                                                 **
; ..\mcal_src\Adc_Utility.h	    16  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    17  **   $DATE       : 2017-04-06 $                                               **
; ..\mcal_src\Adc_Utility.h	    18  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    19  **   AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Adc_Utility.h	    20  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    21  **   VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Adc_Utility.h	    22  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    23  **   DESCRIPTION : This file contains                                         **
; ..\mcal_src\Adc_Utility.h	    24  **                 - functionality of Adc driver.                             **
; ..\mcal_src\Adc_Utility.h	    25  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    26  **   MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Adc_Utility.h	    27  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    28  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    29  
; ..\mcal_src\Adc_Utility.h	    30  #ifndef ADC_UTILITY_H
; ..\mcal_src\Adc_Utility.h	    31  #define ADC_UTILITY_H
; ..\mcal_src\Adc_Utility.h	    32  
; ..\mcal_src\Adc_Utility.h	    33  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	    34  **                      Includes                                              **
; ..\mcal_src\Adc_Utility.h	    35  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    36  /* Import the Hw handle functions */
; ..\mcal_src\Adc_Utility.h	    37  #include "Adc_HwHandle.h"
; ..\mcal_src\Adc_Utility.h	    38  #if (ADC_GROUP_EMUX_SCAN == STD_ON)
; ..\mcal_src\Adc_Utility.h	    39  /* Import DMA channel information */
; ..\mcal_src\Adc_Utility.h	    40  #include "Mcal_DmaLib.h"
; ..\mcal_src\Adc_Utility.h	    41  #endif /* (ADC_GROUP_EMUX_SCAN == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    42  /* Import the Conversion handle functions */
; ..\mcal_src\Adc_Utility.h	    43  #include "Adc_ConvHandle.h"
; ..\mcal_src\Adc_Utility.h	    44  
; ..\mcal_src\Adc_Utility.h	    45  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	    46  **                      Global Macro Definitions                              **
; ..\mcal_src\Adc_Utility.h	    47  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    48  /* Invalid values for Global variables */
; ..\mcal_src\Adc_Utility.h	    49  #if (ADC_RESULT_HANDLING_MODE == ADC_NON_AUTOSAR)
; ..\mcal_src\Adc_Utility.h	    50  
; ..\mcal_src\Adc_Utility.h	    51  #define ADC_INVALID_CHANNEL              ((Adc_ChannelType)0xFF)
; ..\mcal_src\Adc_Utility.h	    52  
; ..\mcal_src\Adc_Utility.h	    53  #endif /* (ADC_RESULT_HANDLING_MODE == ADC_NON_AUTOSAR) */
; ..\mcal_src\Adc_Utility.h	    54  
; ..\mcal_src\Adc_Utility.h	    55  #define ADC_INVALID_PRIORITY             ((Adc_GroupPriorityType)0xFF)
; ..\mcal_src\Adc_Utility.h	    56  
; ..\mcal_src\Adc_Utility.h	    57  /* Flag value to disable interrupt service */
; ..\mcal_src\Adc_Utility.h	    58  #define ADC_NO_SERVICE                   ((uint8)0x01)
; ..\mcal_src\Adc_Utility.h	    59  
; ..\mcal_src\Adc_Utility.h	    60  /*
; ..\mcal_src\Adc_Utility.h	    61    Macros to define the Position of channel configuration parameters
; ..\mcal_src\Adc_Utility.h	    62  */
; ..\mcal_src\Adc_Utility.h	    63  #define ADC_POS_CH_CFG_INT_CH       (16U)
; ..\mcal_src\Adc_Utility.h	    64  
; ..\mcal_src\Adc_Utility.h	    65  /* Status of group in scheduler either in No priority with Queue or
; ..\mcal_src\Adc_Utility.h	    66     HW SW Priority
; ..\mcal_src\Adc_Utility.h	    67  */
; ..\mcal_src\Adc_Utility.h	    68  #define ADC_START_CONVERSION        ((uint32)0x01)
; ..\mcal_src\Adc_Utility.h	    69  
; ..\mcal_src\Adc_Utility.h	    70  #if (ADC_ENABLE_QUEUING == STD_ON)
; ..\mcal_src\Adc_Utility.h	    71  #define ADC_STOP_CONVERSION         ((uint32)0x01)
; ..\mcal_src\Adc_Utility.h	    72  #endif /* (ADC_ENABLE_QUEUING == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    73  
; ..\mcal_src\Adc_Utility.h	    74  /* SW priority considered */
; ..\mcal_src\Adc_Utility.h	    75  #if (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW)
; ..\mcal_src\Adc_Utility.h	    76  
; ..\mcal_src\Adc_Utility.h	    77  /* Macro for Maximum Group Possible */
; ..\mcal_src\Adc_Utility.h	    78  #define ADC_MAX_GROUP_POSSIBLE      ((uint32)32)
; ..\mcal_src\Adc_Utility.h	    79  
; ..\mcal_src\Adc_Utility.h	    80  #endif /* (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW) */
; ..\mcal_src\Adc_Utility.h	    81  
; ..\mcal_src\Adc_Utility.h	    82  /* Group ID which is invalid */
; ..\mcal_src\Adc_Utility.h	    83  #define ADC_INVALID_GROUP_ID        ((Adc_GroupType)0xFF)
; ..\mcal_src\Adc_Utility.h	    84  
; ..\mcal_src\Adc_Utility.h	    85  #if (ADC_GROUP_EMUX_SCAN == STD_ON)
; ..\mcal_src\Adc_Utility.h	    86  
; ..\mcal_src\Adc_Utility.h	    87  /* DMA Related macros */
; ..\mcal_src\Adc_Utility.h	    88  
; ..\mcal_src\Adc_Utility.h	    89  /* Dma usage flag set */
; ..\mcal_src\Adc_Utility.h	    90  #define ADC_DMA_IN_USE              ((uint8)1)
; ..\mcal_src\Adc_Utility.h	    91  
; ..\mcal_src\Adc_Utility.h	    92  /* Dma usage flag reset */
; ..\mcal_src\Adc_Utility.h	    93  #define ADC_DMA_NOT_IN_USE          ((uint8)0)
; ..\mcal_src\Adc_Utility.h	    94  
; ..\mcal_src\Adc_Utility.h	    95  #endif /* (ADC_GROUP_EMUX_SCAN == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    96  
; ..\mcal_src\Adc_Utility.h	    97  #if (ADC_USE_EMUX == STD_ON)
; ..\mcal_src\Adc_Utility.h	    98  #define ADC_POS_EMUX_ENABLE    (3U)
; ..\mcal_src\Adc_Utility.h	    99  #endif /* (ADC_USE_EMUX == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   100  
; ..\mcal_src\Adc_Utility.h	   101  /*
; ..\mcal_src\Adc_Utility.h	   102  Get ADC Kernel number from Group Id. Bit 5 , 6 , 7 and 8 will give the
; ..\mcal_src\Adc_Utility.h	   103  5 - start from bit number 5
; ..\mcal_src\Adc_Utility.h	   104  4 - no. of bits to extract
; ..\mcal_src\Adc_Utility.h	   105  */
; ..\mcal_src\Adc_Utility.h	   106  #define ADC_KERNEL_NUM_START_BIT    (5U)
; ..\mcal_src\Adc_Utility.h	   107  #define ADC_KERNEL_NUM_OF_BITS      (4U)
; ..\mcal_src\Adc_Utility.h	   108  
; ..\mcal_src\Adc_Utility.h	   109  /*
; ..\mcal_src\Adc_Utility.h	   110  Get ADC Kernel Group Id. Bit 0 to 4 will give the Kernel Group Id
; ..\mcal_src\Adc_Utility.h	   111  */
; ..\mcal_src\Adc_Utility.h	   112  #define ADC_KERNEL_GROUP_ID_MASK    (0x1FU)
; ..\mcal_src\Adc_Utility.h	   113  
; ..\mcal_src\Adc_Utility.h	   114  /*
; ..\mcal_src\Adc_Utility.h	   115  Get ADC Kernel Channel number. Bit 12 to 15 will give the channel number
; ..\mcal_src\Adc_Utility.h	   116  */
; ..\mcal_src\Adc_Utility.h	   117  #define ADC_CHANNEL_ID_MASK         (0x0FU)
; ..\mcal_src\Adc_Utility.h	   118  
; ..\mcal_src\Adc_Utility.h	   119  #if ((ADC_HW_TRIGGER_API == STD_ON) && (ADC_TIMER_TRIGG == STD_ON))
; ..\mcal_src\Adc_Utility.h	   120  /*
; ..\mcal_src\Adc_Utility.h	   121  Get Group for HW trigger source type (Timer or External Request)
; ..\mcal_src\Adc_Utility.h	   122  4U - From bit number 4 to be extracted
; ..\mcal_src\Adc_Utility.h	   123  1U - 1 bit to be extracted
; ..\mcal_src\Adc_Utility.h	   124  */
; ..\mcal_src\Adc_Utility.h	   125  #define ADC_TIMER_ENABLE_START_BIT   (4U)
; ..\mcal_src\Adc_Utility.h	   126  #define ADC_TIMER_ENABLE_NUM_OF_BITS (1U)
; ..\mcal_src\Adc_Utility.h	   127  
; ..\mcal_src\Adc_Utility.h	   128  #endif /*(ADC_HW_TRIGGER_API == STD_ON && ADC_TIMER_TRIGG == STD_ON)*/
; ..\mcal_src\Adc_Utility.h	   129  
; ..\mcal_src\Adc_Utility.h	   130  /*
; ..\mcal_src\Adc_Utility.h	   131  Get ENGT value of Request source from user configured hw data
; ..\mcal_src\Adc_Utility.h	   132  5U - From bit number 5 to be extracted
; ..\mcal_src\Adc_Utility.h	   133  2U - 2 bits to be extracted
; ..\mcal_src\Adc_Utility.h	   134  */
; ..\mcal_src\Adc_Utility.h	   135  #define ADC_ENGT_START_BIT          (5U)
; ..\mcal_src\Adc_Utility.h	   136  #define ADC_ENGT_NUM_OF_BITS        (2U)
; ..\mcal_src\Adc_Utility.h	   137  
; ..\mcal_src\Adc_Utility.h	   138  #if ((ADC_HW_TRIGGER_API == STD_ON) && ((ADC_ERU_TRIGG == STD_ON) || \ 
; ..\mcal_src\Adc_Utility.h	   139                                                    (ADC_ERU_GATING == STD_ON)) )
; ..\mcal_src\Adc_Utility.h	   140  /* Get Flag for Eru trigger/Gate type */
; ..\mcal_src\Adc_Utility.h	   141  #define ADC_ERU_SELECT_NUM_OF_BITS  (1U)
; ..\mcal_src\Adc_Utility.h	   142  /* Get Eru Unit used for trigger */
; ..\mcal_src\Adc_Utility.h	   143  #define ADC_ERU_UNIT_NUM_OF_BITS    (3U)
; ..\mcal_src\Adc_Utility.h	   144  
; ..\mcal_src\Adc_Utility.h	   145  #endif /* (ADC_HW_TRIGGER_API == STD_ON && ADC_ERU_TRIGG == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   146  
; ..\mcal_src\Adc_Utility.h	   147  #if (ADC_USE_EMUX == STD_ON)
; ..\mcal_src\Adc_Utility.h	   148  /* Get Emux Select value from EmuxData */
; ..\mcal_src\Adc_Utility.h	   149  #define ADC_EMUX_SELECT_MASK        (0x07U)
; ..\mcal_src\Adc_Utility.h	   150  
; ..\mcal_src\Adc_Utility.h	   151  /* Get Emux enabled or not from EmuxData */
; ..\mcal_src\Adc_Utility.h	   152  #define ADC_EMUX_ENABLE_NUM_OF_BITS (1U)
; ..\mcal_src\Adc_Utility.h	   153  
; ..\mcal_src\Adc_Utility.h	   154  #endif /* (ADC_USE_EMUX == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   155  
; ..\mcal_src\Adc_Utility.h	   156  #if (ADC_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Adc_Utility.h	   157  /* Status to indicate that ADC is initialized */
; ..\mcal_src\Adc_Utility.h	   158  #define ADC_INITIALIZED  ((uint8)1)
; ..\mcal_src\Adc_Utility.h	   159  #endif /* (ADC_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   160  
; ..\mcal_src\Adc_Utility.h	   161  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   162  **                      Global Type Definitions                               **
; ..\mcal_src\Adc_Utility.h	   163  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   164  
; ..\mcal_src\Adc_Utility.h	   165  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   166  **                      Global Constant Declarations                          **
; ..\mcal_src\Adc_Utility.h	   167  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   168  
; ..\mcal_src\Adc_Utility.h	   169  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   170  **                      Global Variable Declarations                          **
; ..\mcal_src\Adc_Utility.h	   171  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   172  #define ADC_START_SEC_VAR_8BIT
; ..\mcal_src\Adc_Utility.h	   173  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   174  
; ..\mcal_src\Adc_Utility.h	   175  #if (ADC_REQSRC2 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Utility.h	   176  /*IFX_MISRA_RULE_08_08_STATUS=Adc_BgndScanData is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   177   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   178  extern uint8 Adc_BgndScanData;
; ..\mcal_src\Adc_Utility.h	   179  /*IFX_MISRA_RULE_08_08_STATUS=Adc_BgndGrpCnt is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   180   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   181  extern uint8 Adc_BgndGrpCnt;
; ..\mcal_src\Adc_Utility.h	   182  #endif /* (ADC_REQSRC2 == ADC_REQSRC_USED) */
; ..\mcal_src\Adc_Utility.h	   183  
; ..\mcal_src\Adc_Utility.h	   184  #if (ADC_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Adc_Utility.h	   185  /*IFX_MISRA_RULE_08_08_STATUS=Adc_InitStatus is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   186   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   187  extern uint8 Adc_InitStatus;
; ..\mcal_src\Adc_Utility.h	   188  #endif /* (ADC_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   189  
; ..\mcal_src\Adc_Utility.h	   190  #define ADC_STOP_SEC_VAR_8BIT
; ..\mcal_src\Adc_Utility.h	   191  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   192  
; ..\mcal_src\Adc_Utility.h	   193  #if (ADC_PB_FIXED_ADDRESS == STD_OFF)
; ..\mcal_src\Adc_Utility.h	   194  #define ADC_START_SEC_VAR_32BIT
; ..\mcal_src\Adc_Utility.h	   195  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   196  
; ..\mcal_src\Adc_Utility.h	   197  /* To store the Adc driver configuration pointer */
; ..\mcal_src\Adc_Utility.h	   198  /*IFX_MISRA_RULE_08_08_STATUS=Adc_kConfigPtr is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   199   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   200  extern const Adc_ConfigType  *Adc_kConfigPtr;
; ..\mcal_src\Adc_Utility.h	   201  
; ..\mcal_src\Adc_Utility.h	   202  #define ADC_STOP_SEC_VAR_32BIT
; ..\mcal_src\Adc_Utility.h	   203  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   204  #endif /*(ADC_PB_FIXED_ADDRESS == STD_OFF)*/
; ..\mcal_src\Adc_Utility.h	   205  
; ..\mcal_src\Adc_Utility.h	   206  #if(ADC_PB_FIXED_ADDRESS == STD_ON)
; ..\mcal_src\Adc_Utility.h	   207  #define ADC_START_SEC_CONST_32BIT
; ..\mcal_src\Adc_Utility.h	   208  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   209  
; ..\mcal_src\Adc_Utility.h	   210  /* To store the Adc driver configuration pointer */
; ..\mcal_src\Adc_Utility.h	   211  extern const Adc_ConfigType * const Adc_kConfigPtr;
; ..\mcal_src\Adc_Utility.h	   212  
; ..\mcal_src\Adc_Utility.h	   213  #define ADC_STOP_SEC_CONST_32BIT
; ..\mcal_src\Adc_Utility.h	   214  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   215  #endif /*(ADC_PB_FIXED_ADDRESS == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   216  
; ..\mcal_src\Adc_Utility.h	   217  #if ( (ADC_QM_KERNEL_USED_COUNT != 0U ) && (ADC_REQSRC2 == ADC_REQSRC_USED) )
; ..\mcal_src\Adc_Utility.h	   218  #define ADC_START_SEC_VAR_UNSPECIFIED
; ..\mcal_src\Adc_Utility.h	   219  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   220  
; ..\mcal_src\Adc_Utility.h	   221  /* data variable for QM signals */
; ..\mcal_src\Adc_Utility.h	   222  /*IFX_MISRA_RULE_08_08_STATUS=Adc_QmSignal is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   223   is defined in Adc_ConvHandle.c. This violation is reported incorrectly by
; ..\mcal_src\Adc_Utility.h	   224   PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   225  extern Adc_GlobalDataType Adc_QmSignal[ADC_QM_KERNEL_USED_COUNT];
; ..\mcal_src\Adc_Utility.h	   226  
; ..\mcal_src\Adc_Utility.h	   227  #define ADC_STOP_SEC_VAR_UNSPECIFIED
; ..\mcal_src\Adc_Utility.h	   228  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   229  #endif /* (ADC_QM_KERNEL_USED_COUNT != 0U )&&(ADC_REQSRC2 == ADC_REQSRC_USED) */
; ..\mcal_src\Adc_Utility.h	   230  
; ..\mcal_src\Adc_Utility.h	   231  
; ..\mcal_src\Adc_Utility.h	   232  #define ADC_START_SEC_CODE
; ..\mcal_src\Adc_Utility.h	   233  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   234  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   235  **                      Global Function Declarations                          **
; ..\mcal_src\Adc_Utility.h	   236  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   237  
; ..\mcal_src\Adc_Utility.h	   238  /* Enable/Disable the use of the function */
; ..\mcal_src\Adc_Utility.h	   239  #if (ADC_ENABLE_START_STOP_GROUP_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   240  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   241  ** Syntax           : extern void Adc_lSchmEnterStartGroup                    **
; ..\mcal_src\Adc_Utility.h	   242  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   243  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   244  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   245  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   246  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   247  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   248  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   249  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   250  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   251  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   252  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   253  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   254  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   255  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   256  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   257  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   258  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   259  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   260  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   261  extern void Adc_lSchmEnterStartGroup(void);
; ..\mcal_src\Adc_Utility.h	   262  
; ..\mcal_src\Adc_Utility.h	   263  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   264  ** Syntax           : extern void Adc_lSchmExitStartGroup                     **
; ..\mcal_src\Adc_Utility.h	   265  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   266  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   267  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   268  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   269  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   270  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   271  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   272  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   273  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   274  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   275  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   276  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   277  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   278  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   279  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   280  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   281  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   282  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   283  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   284  extern void Adc_lSchmExitStartGroup(void);
; ..\mcal_src\Adc_Utility.h	   285  
; ..\mcal_src\Adc_Utility.h	   286  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   287  ** Syntax           : extern void Adc_lSchmEnterStopGroup                     **
; ..\mcal_src\Adc_Utility.h	   288  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   289  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   290  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   291  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   292  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   293  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   294  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   295  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   296  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   297  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   298  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   299  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   300  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   301  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   302  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   303  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   304  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   305  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   306  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   307  extern void Adc_lSchmEnterStopGroup(void);
; ..\mcal_src\Adc_Utility.h	   308  
; ..\mcal_src\Adc_Utility.h	   309  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   310  ** Syntax           : extern void Adc_lSchmExitStopGroup                      **
; ..\mcal_src\Adc_Utility.h	   311  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   312  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   313  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   314  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   315  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   316  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   317  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   318  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   319  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   320  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   321  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   322  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   323  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   324  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   325  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   326  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   327  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   328  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   329  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   330  extern void Adc_lSchmExitStopGroup(void);
; ..\mcal_src\Adc_Utility.h	   331  
; ..\mcal_src\Adc_Utility.h	   332  #endif /* (ADC_ENABLE_START_STOP_GROUP_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   333  
; ..\mcal_src\Adc_Utility.h	   334  /* Enable/Disable the use of the function */
; ..\mcal_src\Adc_Utility.h	   335  #if (ADC_HW_TRIGGER_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   336  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   337  ** Syntax           : extern void Adc_lSchmEnterEnableHwTrig                  **
; ..\mcal_src\Adc_Utility.h	   338  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   339  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   340  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   341  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   342  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   343  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   344  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   345  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   346  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   347  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   348  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   349  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   350  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   351  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   352  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   353  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   354  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   355  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   356  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   357  extern void Adc_lSchmEnterEnableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   358  
; ..\mcal_src\Adc_Utility.h	   359  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   360  ** Syntax           : extern void Adc_lSchmExitEnableHwTrig                   **
; ..\mcal_src\Adc_Utility.h	   361  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   362  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   363  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   364  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   365  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   366  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   367  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   368  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   369  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   370  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   371  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   372  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   373  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   374  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   375  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   376  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   377  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   378  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   379  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   380  extern void Adc_lSchmExitEnableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   381  
; ..\mcal_src\Adc_Utility.h	   382  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   383  ** Syntax           : extern void Adc_lSchmEnterDisableHwTrig                 **
; ..\mcal_src\Adc_Utility.h	   384  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   385  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   386  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   387  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   388  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   389  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   390  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   391  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   392  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   393  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   394  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   395  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   396  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   397  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   398  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   399  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   400  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   401  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   402  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   403  extern void Adc_lSchmEnterDisableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   404  
; ..\mcal_src\Adc_Utility.h	   405  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   406  ** Syntax           : extern void Adc_lSchmExitDisableHwTrig                  **
; ..\mcal_src\Adc_Utility.h	   407  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   408  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   409  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   410  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   411  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   412  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   413  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   414  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   415  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   416  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   417  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   418  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   419  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   420  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   421  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   422  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   423  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   424  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   425  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   426  extern void Adc_lSchmExitDisableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   427  
; ..\mcal_src\Adc_Utility.h	   428  #endif /* (ADC_HW_TRIGGER_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   429  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   430  ** Syntax           : extern void Adc_lSchmEnterGetGrpStatus                  **
; ..\mcal_src\Adc_Utility.h	   431  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   432  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   433  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   434  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   435  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   436  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   437  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   438  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   439  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   440  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   441  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   442  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   443  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   444  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   445  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   446  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   447  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   448  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   449  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   450  extern void Adc_lSchmEnterGetGrpStatus(void);
; ..\mcal_src\Adc_Utility.h	   451  
; ..\mcal_src\Adc_Utility.h	   452  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   453  ** Syntax           : extern void Adc_lSchmExitGetGrpStatus                   **
; ..\mcal_src\Adc_Utility.h	   454  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   455  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   456  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   457  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   458  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   459  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   460  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   461  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   462  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   463  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   464  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   465  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   466  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   467  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   468  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   469  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   470  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   471  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   472  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   473  extern void Adc_lSchmExitGetGrpStatus(void);
; ..\mcal_src\Adc_Utility.h	   474  
; ..\mcal_src\Adc_Utility.h	   475  #if (ADC_RESULT_HANDLING_MODE == ADC_AUTOSAR)
; ..\mcal_src\Adc_Utility.h	   476  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   477  ** Syntax           : extern void Adc_lSchmEnterGetStreamLastPtr              **
; ..\mcal_src\Adc_Utility.h	   478  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   479  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   480  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   481  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   482  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   483  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   484  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   485  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   486  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   487  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   488  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   489  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   490  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   491  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   492  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   493  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   494  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   495  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   496  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   497  extern void Adc_lSchmEnterGetStreamLastPtr(void);
; ..\mcal_src\Adc_Utility.h	   498  
; ..\mcal_src\Adc_Utility.h	   499  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   500  ** Syntax           : extern void Adc_lSchmExitGetStreamLastPtr               **
; ..\mcal_src\Adc_Utility.h	   501  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   502  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   503  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   504  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   505  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   506  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   507  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   508  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   509  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   510  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   511  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   512  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   513  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   514  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   515  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   516  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   517  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   518  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   519  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   520  extern void Adc_lSchmExitGetStreamLastPtr(void);
; ..\mcal_src\Adc_Utility.h	   521  
; ..\mcal_src\Adc_Utility.h	   522  #if (ADC_READ_GROUP_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   523  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   524  ** Syntax           : extern void Adc_lSchmEnterReadGroup                     **
; ..\mcal_src\Adc_Utility.h	   525  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   526  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   527  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   528  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   529  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   530  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   531  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   532  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   533  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   534  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   535  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   536  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   537  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   538  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   539  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   540  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   541  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   542  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   543  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   544  extern void Adc_lSchmEnterReadGroup(void);
; ..\mcal_src\Adc_Utility.h	   545  
; ..\mcal_src\Adc_Utility.h	   546  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   547  ** Syntax           : extern void Adc_lSchmExitReadGroup                      **
; ..\mcal_src\Adc_Utility.h	   548  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   549  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   550  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   551  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   552  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   553  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   554  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   555  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   556  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   557  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   558  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   559  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   560  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   561  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   562  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   563  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   564  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   565  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   566  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   567  extern void Adc_lSchmExitReadGroup(void);
; ..\mcal_src\Adc_Utility.h	   568  
; ..\mcal_src\Adc_Utility.h	   569  #endif /* (ADC_READ_GROUP_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   570  #endif /* (ADC_RESULT_HANDLING_MODE == ADC_AUTOSAR) */
; ..\mcal_src\Adc_Utility.h	   571  
; ..\mcal_src\Adc_Utility.h	   572  /* Queue Enable is STD_ON only during No priority */
; ..\mcal_src\Adc_Utility.h	   573  #if (ADC_ENABLE_QUEUING == STD_ON)
; ..\mcal_src\Adc_Utility.h	   574  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   575  ** Syntax           : extern void Adc_lSchmEnterPopQueue                      **
; ..\mcal_src\Adc_Utility.h	   576  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   577  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   578  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   579  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   580  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   581  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   582  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   583  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   584  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   585  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   586  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   587  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   588  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   589  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   590  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   591  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   592  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   593  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   594  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   595  extern void Adc_lSchmEnterPopQueue(void);
; ..\mcal_src\Adc_Utility.h	   596  
; ..\mcal_src\Adc_Utility.h	   597  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   598  ** Syntax           : extern void Adc_lSchmExitPopQueue                       **
; ..\mcal_src\Adc_Utility.h	   599  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   600  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   601  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   602  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   603  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   604  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   605  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   606  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   607  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   608  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   609  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   610  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   611  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   612  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   613  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   614  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   615  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   616  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   617  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   618  extern void Adc_lSchmExitPopQueue(void);
; ..\mcal_src\Adc_Utility.h	   619  
; ..\mcal_src\Adc_Utility.h	   620  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   621  ** Syntax           : extern void Adc_lSchmEnterPushQueue                     **
; ..\mcal_src\Adc_Utility.h	   622  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   623  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   624  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   625  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   626  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   627  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   628  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   629  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   630  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   631  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   632  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   633  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   634  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   635  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   636  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   637  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   638  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   639  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   640  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   641  extern void Adc_lSchmEnterPushQueue(void);
; ..\mcal_src\Adc_Utility.h	   642  
; ..\mcal_src\Adc_Utility.h	   643  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   644  ** Syntax           : extern void Adc_lSchmExitPushQueue                      **
; ..\mcal_src\Adc_Utility.h	   645  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   646  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   647  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   648  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   649  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   650  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   651  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   652  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   653  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   654  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   655  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   656  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   657  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   658  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   659  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   660  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   661  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   662  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   663  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   664  extern void Adc_lSchmExitPushQueue(void);
; ..\mcal_src\Adc_Utility.h	   665  #endif /* (ADC_ENABLE_QUEUING == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   666  
; ..\mcal_src\Adc_Utility.h	   667  /* Full priority considered */
; ..\mcal_src\Adc_Utility.h	   668  #if (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW)
; ..\mcal_src\Adc_Utility.h	   669  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   670  ** Syntax           : extern void Adc_lSchmEnterScheduleNext                  **
; ..\mcal_src\Adc_Utility.h	   671  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   672  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   673  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   674  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   675  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   676  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   677  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   678  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   679  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   680  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   681  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   682  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   683  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   684  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   685  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   686  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   687  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   688  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   689  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   690  extern void Adc_lSchmEnterScheduleNext(void);
; ..\mcal_src\Adc_Utility.h	   691  
; ..\mcal_src\Adc_Utility.h	   692  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   693  ** Syntax           : extern void Adc_lSchmExitScheduleNext                   **
; ..\mcal_src\Adc_Utility.h	   694  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   695  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   696  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   697  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   698  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   699  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   700  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   701  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   702  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   703  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   704  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   705  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   706  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   707  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   708  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   709  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   710  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   711  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   712  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   713  extern void Adc_lSchmExitScheduleNext(void);
; ..\mcal_src\Adc_Utility.h	   714  
; ..\mcal_src\Adc_Utility.h	   715  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   716  ** Syntax           : extern void Adc_lSchmEnterScheduleStart                 **
; ..\mcal_src\Adc_Utility.h	   717  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   718  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   719  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   720  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   721  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   722  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   723  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   724  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   725  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   726  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   727  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   728  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   729  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   730  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   731  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   732  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   733  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   734  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   735  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   736  extern void Adc_lSchmEnterScheduleStart(void);
; ..\mcal_src\Adc_Utility.h	   737  
; ..\mcal_src\Adc_Utility.h	   738  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   739  ** Syntax           : extern void Adc_lSchmExitScheduleStart                  **
; ..\mcal_src\Adc_Utility.h	   740  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   741  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   742  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   743  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   744  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   745  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   746  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   747  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   748  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   749  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   750  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   751  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   752  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   753  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   754  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   755  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   756  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   757  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   758  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   759  extern void Adc_lSchmExitScheduleStart(void);
; ..\mcal_src\Adc_Utility.h	   760  
; ..\mcal_src\Adc_Utility.h	   761  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   762  ** Syntax           : extern void Adc_lSchmEnterScheduleStop                  **
; ..\mcal_src\Adc_Utility.h	   763  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   764  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   765  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   766  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   767  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   768  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   769  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   770  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   771  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   772  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   773  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   774  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   775  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   776  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   777  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   778  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   779  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   780  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   781  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   782  extern void Adc_lSchmEnterScheduleStop(void);
; ..\mcal_src\Adc_Utility.h	   783  
; ..\mcal_src\Adc_Utility.h	   784  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   785  ** Syntax           : extern void Adc_lSchmExitScheduleStop                   **
; ..\mcal_src\Adc_Utility.h	   786  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   787  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   788  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   789  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   790  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   791  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   792  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   793  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   794  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   795  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   796  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   797  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   798  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   799  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   800  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   801  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   802  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   803  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   804  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   805  extern void Adc_lSchmExitScheduleStop(void);
; ..\mcal_src\Adc_Utility.h	   806  
; ..\mcal_src\Adc_Utility.h	   807  #endif /* (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW) */
; ..\mcal_src\Adc_Utility.h	   808  
; ..\mcal_src\Adc_Utility.h	   809  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   810  **                      Global Inline Function Definitions                    **
; ..\mcal_src\Adc_Utility.h	   811  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   812  
; ..\mcal_src\Adc_Utility.h	   813  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   814  ** Syntax           : _IFXEXTERN_ IFX_INLINE uint16 Adc_lKernelConfigured     **
; ..\mcal_src\Adc_Utility.h	   815  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   816  **                      uint8 Kernel                                          **
; ..\mcal_src\Adc_Utility.h	   817  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   818  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   819  ** Service ID       : None                                                    **
; ..\mcal_src\Adc_Utility.h	   820  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   821  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   822  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   823  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   824  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   825  ** Parameters (in)  : Kernel - Adc Kernel to be checked                       **
; ..\mcal_src\Adc_Utility.h	   826  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   827  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   828  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   829  ** Return value     : RetVal                                                  **
; ..\mcal_src\Adc_Utility.h	   830  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   831  ** Description      :                                                         **
; ..\mcal_src\Adc_Utility.h	   832  ** - The function is to check if the Adc kernel is configured or not          **
; ..\mcal_src\Adc_Utility.h	   833  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   834  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   835  /*IFX_MISRA_RULE_08_05_STATUS=Allowed for inline functions defined in header
; ..\mcal_src\Adc_Utility.h	   836   files*/
; ..\mcal_src\Adc_Utility.h	   837  _IFXEXTERN_ IFX_INLINE uint16 Adc_lKernelConfigured(uint8 Kernel)
; ..\mcal_src\Adc_Utility.h	   838  {
; ..\mcal_src\Adc_Utility.h	   839    /*IFX_MISRA_RULE_08_05_STATUS=Allowed for inline functions defined in header
; ..\mcal_src\Adc_Utility.h	   840     files*/
; ..\mcal_src\Adc_Utility.h	   841    uint16 RetVal;
; ..\mcal_src\Adc_Utility.h	   842  
; ..\mcal_src\Adc_Utility.h	   843    RetVal = (uint16)( (uint16)((1UL) << (Kernel)) & ((uint16)ADC_USED_KERNELS) );
; ..\mcal_src\Adc_Utility.h	   844  
; ..\mcal_src\Adc_Utility.h	   845    if (RetVal != 0U)
; ..\mcal_src\Adc_Utility.h	   846    {
; ..\mcal_src\Adc_Utility.h	   847      /* UTP AI00133878 */
; ..\mcal_src\Adc_Utility.h	   848      if (Adc_kConfigPtr->CfgPtr[Kernel] == NULL_PTR)
	ld.a	a15,[a15]@los(Adc_kConfigPtr)
.L31:

; ..\mcal_src\Adc_Calibration.c	   103    do
; ..\mcal_src\Adc_Calibration.c	   104    {
; ..\mcal_src\Adc_Calibration.c	   105      if(Adc_lKernelConfigured(LoopCtr) != 0U)
; ..\mcal_src\Adc_Calibration.c	   106      {
; ..\mcal_src\Adc_Calibration.c	   107        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to efficiently
; ..\mcal_src\Adc_Calibration.c	   108        access the SFRs of multiple ADC kernels.*/
; ..\mcal_src\Adc_Calibration.c	   109        if ( ( (ADC_SFR_RUNTIME_USER_MODE_READ32(ADC_MODULE[LoopCtr].ARBCFG.U)) &\ 
; ..\mcal_src\Adc_Calibration.c	   110                (ADC_CAL_CALS_MASK) ) != ADC_CAL_CALS_VALUE )
; ..\mcal_src\Adc_Calibration.c	   111        {
; ..\mcal_src\Adc_Calibration.c	   112          Status = E_NOT_OK;
; ..\mcal_src\Adc_Calibration.c	   113        }
; ..\mcal_src\Adc_Calibration.c	   114      }
; ..\mcal_src\Adc_Calibration.c	   115    
; ..\mcal_src\Adc_Calibration.c	   116      LoopCtr++;
; ..\mcal_src\Adc_Calibration.c	   117      
; ..\mcal_src\Adc_Calibration.c	   118    } while (LoopCtr < ADC_MAX_KERNELS);
	mov.a	a2,#1
.L2:

; ..\mcal_src\Adc_Utility.h	     1  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	     2  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_src\Adc_Utility.h	     4  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     5  ** All rights reserved.                                                       **
; ..\mcal_src\Adc_Utility.h	     6  **                                                                            **
; ..\mcal_src\Adc_Utility.h	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\Adc_Utility.h	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\Adc_Utility.h	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\Adc_Utility.h	    10  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    11  ********************************************************************************
; ..\mcal_src\Adc_Utility.h	    12  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    13  **   $FILENAME   : Adc_Utility.h $                                            **
; ..\mcal_src\Adc_Utility.h	    14  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    15  **   $CC VERSION : \main\16 $                                                 **
; ..\mcal_src\Adc_Utility.h	    16  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    17  **   $DATE       : 2017-04-06 $                                               **
; ..\mcal_src\Adc_Utility.h	    18  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    19  **   AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Adc_Utility.h	    20  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    21  **   VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Adc_Utility.h	    22  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    23  **   DESCRIPTION : This file contains                                         **
; ..\mcal_src\Adc_Utility.h	    24  **                 - functionality of Adc driver.                             **
; ..\mcal_src\Adc_Utility.h	    25  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    26  **   MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Adc_Utility.h	    27  **                                                                            **
; ..\mcal_src\Adc_Utility.h	    28  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    29  
; ..\mcal_src\Adc_Utility.h	    30  #ifndef ADC_UTILITY_H
; ..\mcal_src\Adc_Utility.h	    31  #define ADC_UTILITY_H
; ..\mcal_src\Adc_Utility.h	    32  
; ..\mcal_src\Adc_Utility.h	    33  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	    34  **                      Includes                                              **
; ..\mcal_src\Adc_Utility.h	    35  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    36  /* Import the Hw handle functions */
; ..\mcal_src\Adc_Utility.h	    37  #include "Adc_HwHandle.h"
; ..\mcal_src\Adc_Utility.h	    38  #if (ADC_GROUP_EMUX_SCAN == STD_ON)
; ..\mcal_src\Adc_Utility.h	    39  /* Import DMA channel information */
; ..\mcal_src\Adc_Utility.h	    40  #include "Mcal_DmaLib.h"
; ..\mcal_src\Adc_Utility.h	    41  #endif /* (ADC_GROUP_EMUX_SCAN == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    42  /* Import the Conversion handle functions */
; ..\mcal_src\Adc_Utility.h	    43  #include "Adc_ConvHandle.h"
; ..\mcal_src\Adc_Utility.h	    44  
; ..\mcal_src\Adc_Utility.h	    45  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	    46  **                      Global Macro Definitions                              **
; ..\mcal_src\Adc_Utility.h	    47  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	    48  /* Invalid values for Global variables */
; ..\mcal_src\Adc_Utility.h	    49  #if (ADC_RESULT_HANDLING_MODE == ADC_NON_AUTOSAR)
; ..\mcal_src\Adc_Utility.h	    50  
; ..\mcal_src\Adc_Utility.h	    51  #define ADC_INVALID_CHANNEL              ((Adc_ChannelType)0xFF)
; ..\mcal_src\Adc_Utility.h	    52  
; ..\mcal_src\Adc_Utility.h	    53  #endif /* (ADC_RESULT_HANDLING_MODE == ADC_NON_AUTOSAR) */
; ..\mcal_src\Adc_Utility.h	    54  
; ..\mcal_src\Adc_Utility.h	    55  #define ADC_INVALID_PRIORITY             ((Adc_GroupPriorityType)0xFF)
; ..\mcal_src\Adc_Utility.h	    56  
; ..\mcal_src\Adc_Utility.h	    57  /* Flag value to disable interrupt service */
; ..\mcal_src\Adc_Utility.h	    58  #define ADC_NO_SERVICE                   ((uint8)0x01)
; ..\mcal_src\Adc_Utility.h	    59  
; ..\mcal_src\Adc_Utility.h	    60  /*
; ..\mcal_src\Adc_Utility.h	    61    Macros to define the Position of channel configuration parameters
; ..\mcal_src\Adc_Utility.h	    62  */
; ..\mcal_src\Adc_Utility.h	    63  #define ADC_POS_CH_CFG_INT_CH       (16U)
; ..\mcal_src\Adc_Utility.h	    64  
; ..\mcal_src\Adc_Utility.h	    65  /* Status of group in scheduler either in No priority with Queue or
; ..\mcal_src\Adc_Utility.h	    66     HW SW Priority
; ..\mcal_src\Adc_Utility.h	    67  */
; ..\mcal_src\Adc_Utility.h	    68  #define ADC_START_CONVERSION        ((uint32)0x01)
; ..\mcal_src\Adc_Utility.h	    69  
; ..\mcal_src\Adc_Utility.h	    70  #if (ADC_ENABLE_QUEUING == STD_ON)
; ..\mcal_src\Adc_Utility.h	    71  #define ADC_STOP_CONVERSION         ((uint32)0x01)
; ..\mcal_src\Adc_Utility.h	    72  #endif /* (ADC_ENABLE_QUEUING == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    73  
; ..\mcal_src\Adc_Utility.h	    74  /* SW priority considered */
; ..\mcal_src\Adc_Utility.h	    75  #if (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW)
; ..\mcal_src\Adc_Utility.h	    76  
; ..\mcal_src\Adc_Utility.h	    77  /* Macro for Maximum Group Possible */
; ..\mcal_src\Adc_Utility.h	    78  #define ADC_MAX_GROUP_POSSIBLE      ((uint32)32)
; ..\mcal_src\Adc_Utility.h	    79  
; ..\mcal_src\Adc_Utility.h	    80  #endif /* (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW) */
; ..\mcal_src\Adc_Utility.h	    81  
; ..\mcal_src\Adc_Utility.h	    82  /* Group ID which is invalid */
; ..\mcal_src\Adc_Utility.h	    83  #define ADC_INVALID_GROUP_ID        ((Adc_GroupType)0xFF)
; ..\mcal_src\Adc_Utility.h	    84  
; ..\mcal_src\Adc_Utility.h	    85  #if (ADC_GROUP_EMUX_SCAN == STD_ON)
; ..\mcal_src\Adc_Utility.h	    86  
; ..\mcal_src\Adc_Utility.h	    87  /* DMA Related macros */
; ..\mcal_src\Adc_Utility.h	    88  
; ..\mcal_src\Adc_Utility.h	    89  /* Dma usage flag set */
; ..\mcal_src\Adc_Utility.h	    90  #define ADC_DMA_IN_USE              ((uint8)1)
; ..\mcal_src\Adc_Utility.h	    91  
; ..\mcal_src\Adc_Utility.h	    92  /* Dma usage flag reset */
; ..\mcal_src\Adc_Utility.h	    93  #define ADC_DMA_NOT_IN_USE          ((uint8)0)
; ..\mcal_src\Adc_Utility.h	    94  
; ..\mcal_src\Adc_Utility.h	    95  #endif /* (ADC_GROUP_EMUX_SCAN == STD_ON) */
; ..\mcal_src\Adc_Utility.h	    96  
; ..\mcal_src\Adc_Utility.h	    97  #if (ADC_USE_EMUX == STD_ON)
; ..\mcal_src\Adc_Utility.h	    98  #define ADC_POS_EMUX_ENABLE    (3U)
; ..\mcal_src\Adc_Utility.h	    99  #endif /* (ADC_USE_EMUX == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   100  
; ..\mcal_src\Adc_Utility.h	   101  /*
; ..\mcal_src\Adc_Utility.h	   102  Get ADC Kernel number from Group Id. Bit 5 , 6 , 7 and 8 will give the
; ..\mcal_src\Adc_Utility.h	   103  5 - start from bit number 5
; ..\mcal_src\Adc_Utility.h	   104  4 - no. of bits to extract
; ..\mcal_src\Adc_Utility.h	   105  */
; ..\mcal_src\Adc_Utility.h	   106  #define ADC_KERNEL_NUM_START_BIT    (5U)
; ..\mcal_src\Adc_Utility.h	   107  #define ADC_KERNEL_NUM_OF_BITS      (4U)
; ..\mcal_src\Adc_Utility.h	   108  
; ..\mcal_src\Adc_Utility.h	   109  /*
; ..\mcal_src\Adc_Utility.h	   110  Get ADC Kernel Group Id. Bit 0 to 4 will give the Kernel Group Id
; ..\mcal_src\Adc_Utility.h	   111  */
; ..\mcal_src\Adc_Utility.h	   112  #define ADC_KERNEL_GROUP_ID_MASK    (0x1FU)
; ..\mcal_src\Adc_Utility.h	   113  
; ..\mcal_src\Adc_Utility.h	   114  /*
; ..\mcal_src\Adc_Utility.h	   115  Get ADC Kernel Channel number. Bit 12 to 15 will give the channel number
; ..\mcal_src\Adc_Utility.h	   116  */
; ..\mcal_src\Adc_Utility.h	   117  #define ADC_CHANNEL_ID_MASK         (0x0FU)
; ..\mcal_src\Adc_Utility.h	   118  
; ..\mcal_src\Adc_Utility.h	   119  #if ((ADC_HW_TRIGGER_API == STD_ON) && (ADC_TIMER_TRIGG == STD_ON))
; ..\mcal_src\Adc_Utility.h	   120  /*
; ..\mcal_src\Adc_Utility.h	   121  Get Group for HW trigger source type (Timer or External Request)
; ..\mcal_src\Adc_Utility.h	   122  4U - From bit number 4 to be extracted
; ..\mcal_src\Adc_Utility.h	   123  1U - 1 bit to be extracted
; ..\mcal_src\Adc_Utility.h	   124  */
; ..\mcal_src\Adc_Utility.h	   125  #define ADC_TIMER_ENABLE_START_BIT   (4U)
; ..\mcal_src\Adc_Utility.h	   126  #define ADC_TIMER_ENABLE_NUM_OF_BITS (1U)
; ..\mcal_src\Adc_Utility.h	   127  
; ..\mcal_src\Adc_Utility.h	   128  #endif /*(ADC_HW_TRIGGER_API == STD_ON && ADC_TIMER_TRIGG == STD_ON)*/
; ..\mcal_src\Adc_Utility.h	   129  
; ..\mcal_src\Adc_Utility.h	   130  /*
; ..\mcal_src\Adc_Utility.h	   131  Get ENGT value of Request source from user configured hw data
; ..\mcal_src\Adc_Utility.h	   132  5U - From bit number 5 to be extracted
; ..\mcal_src\Adc_Utility.h	   133  2U - 2 bits to be extracted
; ..\mcal_src\Adc_Utility.h	   134  */
; ..\mcal_src\Adc_Utility.h	   135  #define ADC_ENGT_START_BIT          (5U)
; ..\mcal_src\Adc_Utility.h	   136  #define ADC_ENGT_NUM_OF_BITS        (2U)
; ..\mcal_src\Adc_Utility.h	   137  
; ..\mcal_src\Adc_Utility.h	   138  #if ((ADC_HW_TRIGGER_API == STD_ON) && ((ADC_ERU_TRIGG == STD_ON) || \ 
; ..\mcal_src\Adc_Utility.h	   139                                                    (ADC_ERU_GATING == STD_ON)) )
; ..\mcal_src\Adc_Utility.h	   140  /* Get Flag for Eru trigger/Gate type */
; ..\mcal_src\Adc_Utility.h	   141  #define ADC_ERU_SELECT_NUM_OF_BITS  (1U)
; ..\mcal_src\Adc_Utility.h	   142  /* Get Eru Unit used for trigger */
; ..\mcal_src\Adc_Utility.h	   143  #define ADC_ERU_UNIT_NUM_OF_BITS    (3U)
; ..\mcal_src\Adc_Utility.h	   144  
; ..\mcal_src\Adc_Utility.h	   145  #endif /* (ADC_HW_TRIGGER_API == STD_ON && ADC_ERU_TRIGG == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   146  
; ..\mcal_src\Adc_Utility.h	   147  #if (ADC_USE_EMUX == STD_ON)
; ..\mcal_src\Adc_Utility.h	   148  /* Get Emux Select value from EmuxData */
; ..\mcal_src\Adc_Utility.h	   149  #define ADC_EMUX_SELECT_MASK        (0x07U)
; ..\mcal_src\Adc_Utility.h	   150  
; ..\mcal_src\Adc_Utility.h	   151  /* Get Emux enabled or not from EmuxData */
; ..\mcal_src\Adc_Utility.h	   152  #define ADC_EMUX_ENABLE_NUM_OF_BITS (1U)
; ..\mcal_src\Adc_Utility.h	   153  
; ..\mcal_src\Adc_Utility.h	   154  #endif /* (ADC_USE_EMUX == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   155  
; ..\mcal_src\Adc_Utility.h	   156  #if (ADC_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Adc_Utility.h	   157  /* Status to indicate that ADC is initialized */
; ..\mcal_src\Adc_Utility.h	   158  #define ADC_INITIALIZED  ((uint8)1)
; ..\mcal_src\Adc_Utility.h	   159  #endif /* (ADC_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   160  
; ..\mcal_src\Adc_Utility.h	   161  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   162  **                      Global Type Definitions                               **
; ..\mcal_src\Adc_Utility.h	   163  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   164  
; ..\mcal_src\Adc_Utility.h	   165  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   166  **                      Global Constant Declarations                          **
; ..\mcal_src\Adc_Utility.h	   167  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   168  
; ..\mcal_src\Adc_Utility.h	   169  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   170  **                      Global Variable Declarations                          **
; ..\mcal_src\Adc_Utility.h	   171  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   172  #define ADC_START_SEC_VAR_8BIT
; ..\mcal_src\Adc_Utility.h	   173  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   174  
; ..\mcal_src\Adc_Utility.h	   175  #if (ADC_REQSRC2 == ADC_REQSRC_USED)
; ..\mcal_src\Adc_Utility.h	   176  /*IFX_MISRA_RULE_08_08_STATUS=Adc_BgndScanData is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   177   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   178  extern uint8 Adc_BgndScanData;
; ..\mcal_src\Adc_Utility.h	   179  /*IFX_MISRA_RULE_08_08_STATUS=Adc_BgndGrpCnt is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   180   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   181  extern uint8 Adc_BgndGrpCnt;
; ..\mcal_src\Adc_Utility.h	   182  #endif /* (ADC_REQSRC2 == ADC_REQSRC_USED) */
; ..\mcal_src\Adc_Utility.h	   183  
; ..\mcal_src\Adc_Utility.h	   184  #if (ADC_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Adc_Utility.h	   185  /*IFX_MISRA_RULE_08_08_STATUS=Adc_InitStatus is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   186   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   187  extern uint8 Adc_InitStatus;
; ..\mcal_src\Adc_Utility.h	   188  #endif /* (ADC_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   189  
; ..\mcal_src\Adc_Utility.h	   190  #define ADC_STOP_SEC_VAR_8BIT
; ..\mcal_src\Adc_Utility.h	   191  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   192  
; ..\mcal_src\Adc_Utility.h	   193  #if (ADC_PB_FIXED_ADDRESS == STD_OFF)
; ..\mcal_src\Adc_Utility.h	   194  #define ADC_START_SEC_VAR_32BIT
; ..\mcal_src\Adc_Utility.h	   195  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   196  
; ..\mcal_src\Adc_Utility.h	   197  /* To store the Adc driver configuration pointer */
; ..\mcal_src\Adc_Utility.h	   198  /*IFX_MISRA_RULE_08_08_STATUS=Adc_kConfigPtr is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   199   is defined in Adc.c. This violation is reported incorrectly by PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   200  extern const Adc_ConfigType  *Adc_kConfigPtr;
; ..\mcal_src\Adc_Utility.h	   201  
; ..\mcal_src\Adc_Utility.h	   202  #define ADC_STOP_SEC_VAR_32BIT
; ..\mcal_src\Adc_Utility.h	   203  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   204  #endif /*(ADC_PB_FIXED_ADDRESS == STD_OFF)*/
; ..\mcal_src\Adc_Utility.h	   205  
; ..\mcal_src\Adc_Utility.h	   206  #if(ADC_PB_FIXED_ADDRESS == STD_ON)
; ..\mcal_src\Adc_Utility.h	   207  #define ADC_START_SEC_CONST_32BIT
; ..\mcal_src\Adc_Utility.h	   208  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   209  
; ..\mcal_src\Adc_Utility.h	   210  /* To store the Adc driver configuration pointer */
; ..\mcal_src\Adc_Utility.h	   211  extern const Adc_ConfigType * const Adc_kConfigPtr;
; ..\mcal_src\Adc_Utility.h	   212  
; ..\mcal_src\Adc_Utility.h	   213  #define ADC_STOP_SEC_CONST_32BIT
; ..\mcal_src\Adc_Utility.h	   214  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   215  #endif /*(ADC_PB_FIXED_ADDRESS == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   216  
; ..\mcal_src\Adc_Utility.h	   217  #if ( (ADC_QM_KERNEL_USED_COUNT != 0U ) && (ADC_REQSRC2 == ADC_REQSRC_USED) )
; ..\mcal_src\Adc_Utility.h	   218  #define ADC_START_SEC_VAR_UNSPECIFIED
; ..\mcal_src\Adc_Utility.h	   219  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   220  
; ..\mcal_src\Adc_Utility.h	   221  /* data variable for QM signals */
; ..\mcal_src\Adc_Utility.h	   222  /*IFX_MISRA_RULE_08_08_STATUS=Adc_QmSignal is declared extern only here and
; ..\mcal_src\Adc_Utility.h	   223   is defined in Adc_ConvHandle.c. This violation is reported incorrectly by
; ..\mcal_src\Adc_Utility.h	   224   PC-lint tool*/
; ..\mcal_src\Adc_Utility.h	   225  extern Adc_GlobalDataType Adc_QmSignal[ADC_QM_KERNEL_USED_COUNT];
; ..\mcal_src\Adc_Utility.h	   226  
; ..\mcal_src\Adc_Utility.h	   227  #define ADC_STOP_SEC_VAR_UNSPECIFIED
; ..\mcal_src\Adc_Utility.h	   228  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   229  #endif /* (ADC_QM_KERNEL_USED_COUNT != 0U )&&(ADC_REQSRC2 == ADC_REQSRC_USED) */
; ..\mcal_src\Adc_Utility.h	   230  
; ..\mcal_src\Adc_Utility.h	   231  
; ..\mcal_src\Adc_Utility.h	   232  #define ADC_START_SEC_CODE
; ..\mcal_src\Adc_Utility.h	   233  #include "MemMap.h"
; ..\mcal_src\Adc_Utility.h	   234  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   235  **                      Global Function Declarations                          **
; ..\mcal_src\Adc_Utility.h	   236  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   237  
; ..\mcal_src\Adc_Utility.h	   238  /* Enable/Disable the use of the function */
; ..\mcal_src\Adc_Utility.h	   239  #if (ADC_ENABLE_START_STOP_GROUP_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   240  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   241  ** Syntax           : extern void Adc_lSchmEnterStartGroup                    **
; ..\mcal_src\Adc_Utility.h	   242  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   243  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   244  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   245  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   246  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   247  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   248  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   249  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   250  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   251  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   252  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   253  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   254  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   255  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   256  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   257  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   258  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   259  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   260  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   261  extern void Adc_lSchmEnterStartGroup(void);
; ..\mcal_src\Adc_Utility.h	   262  
; ..\mcal_src\Adc_Utility.h	   263  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   264  ** Syntax           : extern void Adc_lSchmExitStartGroup                     **
; ..\mcal_src\Adc_Utility.h	   265  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   266  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   267  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   268  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   269  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   270  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   271  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   272  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   273  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   274  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   275  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   276  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   277  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   278  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   279  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   280  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   281  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   282  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   283  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   284  extern void Adc_lSchmExitStartGroup(void);
; ..\mcal_src\Adc_Utility.h	   285  
; ..\mcal_src\Adc_Utility.h	   286  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   287  ** Syntax           : extern void Adc_lSchmEnterStopGroup                     **
; ..\mcal_src\Adc_Utility.h	   288  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   289  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   290  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   291  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   292  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   293  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   294  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   295  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   296  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   297  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   298  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   299  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   300  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   301  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   302  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   303  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   304  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   305  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   306  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   307  extern void Adc_lSchmEnterStopGroup(void);
; ..\mcal_src\Adc_Utility.h	   308  
; ..\mcal_src\Adc_Utility.h	   309  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   310  ** Syntax           : extern void Adc_lSchmExitStopGroup                      **
; ..\mcal_src\Adc_Utility.h	   311  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   312  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   313  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   314  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   315  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   316  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   317  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   318  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   319  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   320  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   321  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   322  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   323  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   324  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   325  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   326  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   327  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   328  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   329  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   330  extern void Adc_lSchmExitStopGroup(void);
; ..\mcal_src\Adc_Utility.h	   331  
; ..\mcal_src\Adc_Utility.h	   332  #endif /* (ADC_ENABLE_START_STOP_GROUP_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   333  
; ..\mcal_src\Adc_Utility.h	   334  /* Enable/Disable the use of the function */
; ..\mcal_src\Adc_Utility.h	   335  #if (ADC_HW_TRIGGER_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   336  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   337  ** Syntax           : extern void Adc_lSchmEnterEnableHwTrig                  **
; ..\mcal_src\Adc_Utility.h	   338  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   339  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   340  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   341  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   342  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   343  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   344  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   345  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   346  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   347  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   348  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   349  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   350  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   351  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   352  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   353  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   354  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   355  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   356  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   357  extern void Adc_lSchmEnterEnableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   358  
; ..\mcal_src\Adc_Utility.h	   359  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   360  ** Syntax           : extern void Adc_lSchmExitEnableHwTrig                   **
; ..\mcal_src\Adc_Utility.h	   361  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   362  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   363  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   364  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   365  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   366  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   367  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   368  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   369  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   370  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   371  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   372  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   373  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   374  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   375  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   376  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   377  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   378  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   379  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   380  extern void Adc_lSchmExitEnableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   381  
; ..\mcal_src\Adc_Utility.h	   382  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   383  ** Syntax           : extern void Adc_lSchmEnterDisableHwTrig                 **
; ..\mcal_src\Adc_Utility.h	   384  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   385  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   386  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   387  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   388  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   389  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   390  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   391  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   392  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   393  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   394  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   395  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   396  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   397  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   398  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   399  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   400  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   401  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   402  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   403  extern void Adc_lSchmEnterDisableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   404  
; ..\mcal_src\Adc_Utility.h	   405  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   406  ** Syntax           : extern void Adc_lSchmExitDisableHwTrig                  **
; ..\mcal_src\Adc_Utility.h	   407  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   408  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   409  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   410  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   411  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   412  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   413  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   414  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   415  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   416  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   417  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   418  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   419  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   420  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   421  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   422  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   423  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   424  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   425  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   426  extern void Adc_lSchmExitDisableHwTrig(void);
; ..\mcal_src\Adc_Utility.h	   427  
; ..\mcal_src\Adc_Utility.h	   428  #endif /* (ADC_HW_TRIGGER_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   429  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   430  ** Syntax           : extern void Adc_lSchmEnterGetGrpStatus                  **
; ..\mcal_src\Adc_Utility.h	   431  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   432  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   433  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   434  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   435  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   436  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   437  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   438  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   439  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   440  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   441  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   442  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   443  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   444  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   445  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   446  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   447  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   448  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   449  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   450  extern void Adc_lSchmEnterGetGrpStatus(void);
; ..\mcal_src\Adc_Utility.h	   451  
; ..\mcal_src\Adc_Utility.h	   452  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   453  ** Syntax           : extern void Adc_lSchmExitGetGrpStatus                   **
; ..\mcal_src\Adc_Utility.h	   454  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   455  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   456  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   457  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   458  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   459  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   460  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   461  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   462  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   463  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   464  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   465  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   466  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   467  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   468  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   469  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   470  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   471  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   472  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   473  extern void Adc_lSchmExitGetGrpStatus(void);
; ..\mcal_src\Adc_Utility.h	   474  
; ..\mcal_src\Adc_Utility.h	   475  #if (ADC_RESULT_HANDLING_MODE == ADC_AUTOSAR)
; ..\mcal_src\Adc_Utility.h	   476  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   477  ** Syntax           : extern void Adc_lSchmEnterGetStreamLastPtr              **
; ..\mcal_src\Adc_Utility.h	   478  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   479  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   480  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   481  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   482  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   483  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   484  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   485  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   486  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   487  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   488  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   489  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   490  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   491  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   492  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   493  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   494  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   495  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   496  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   497  extern void Adc_lSchmEnterGetStreamLastPtr(void);
; ..\mcal_src\Adc_Utility.h	   498  
; ..\mcal_src\Adc_Utility.h	   499  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   500  ** Syntax           : extern void Adc_lSchmExitGetStreamLastPtr               **
; ..\mcal_src\Adc_Utility.h	   501  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   502  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   503  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   504  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   505  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   506  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   507  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   508  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   509  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   510  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   511  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   512  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   513  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   514  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   515  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   516  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   517  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   518  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   519  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   520  extern void Adc_lSchmExitGetStreamLastPtr(void);
; ..\mcal_src\Adc_Utility.h	   521  
; ..\mcal_src\Adc_Utility.h	   522  #if (ADC_READ_GROUP_API == STD_ON)
; ..\mcal_src\Adc_Utility.h	   523  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   524  ** Syntax           : extern void Adc_lSchmEnterReadGroup                     **
; ..\mcal_src\Adc_Utility.h	   525  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   526  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   527  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   528  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   529  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   530  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   531  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   532  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   533  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   534  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   535  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   536  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   537  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   538  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   539  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   540  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   541  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   542  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   543  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   544  extern void Adc_lSchmEnterReadGroup(void);
; ..\mcal_src\Adc_Utility.h	   545  
; ..\mcal_src\Adc_Utility.h	   546  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   547  ** Syntax           : extern void Adc_lSchmExitReadGroup                      **
; ..\mcal_src\Adc_Utility.h	   548  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   549  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   550  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   551  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   552  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   553  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   554  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   555  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   556  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   557  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   558  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   559  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   560  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   561  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   562  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   563  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   564  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   565  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   566  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   567  extern void Adc_lSchmExitReadGroup(void);
; ..\mcal_src\Adc_Utility.h	   568  
; ..\mcal_src\Adc_Utility.h	   569  #endif /* (ADC_READ_GROUP_API == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   570  #endif /* (ADC_RESULT_HANDLING_MODE == ADC_AUTOSAR) */
; ..\mcal_src\Adc_Utility.h	   571  
; ..\mcal_src\Adc_Utility.h	   572  /* Queue Enable is STD_ON only during No priority */
; ..\mcal_src\Adc_Utility.h	   573  #if (ADC_ENABLE_QUEUING == STD_ON)
; ..\mcal_src\Adc_Utility.h	   574  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   575  ** Syntax           : extern void Adc_lSchmEnterPopQueue                      **
; ..\mcal_src\Adc_Utility.h	   576  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   577  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   578  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   579  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   580  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   581  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   582  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   583  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   584  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   585  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   586  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   587  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   588  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   589  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   590  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   591  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   592  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   593  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   594  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   595  extern void Adc_lSchmEnterPopQueue(void);
; ..\mcal_src\Adc_Utility.h	   596  
; ..\mcal_src\Adc_Utility.h	   597  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   598  ** Syntax           : extern void Adc_lSchmExitPopQueue                       **
; ..\mcal_src\Adc_Utility.h	   599  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   600  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   601  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   602  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   603  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   604  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   605  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   606  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   607  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   608  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   609  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   610  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   611  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   612  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   613  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   614  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   615  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   616  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   617  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   618  extern void Adc_lSchmExitPopQueue(void);
; ..\mcal_src\Adc_Utility.h	   619  
; ..\mcal_src\Adc_Utility.h	   620  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   621  ** Syntax           : extern void Adc_lSchmEnterPushQueue                     **
; ..\mcal_src\Adc_Utility.h	   622  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   623  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   624  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   625  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   626  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   627  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   628  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   629  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   630  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   631  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   632  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   633  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   634  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   635  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   636  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   637  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   638  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   639  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   640  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   641  extern void Adc_lSchmEnterPushQueue(void);
; ..\mcal_src\Adc_Utility.h	   642  
; ..\mcal_src\Adc_Utility.h	   643  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   644  ** Syntax           : extern void Adc_lSchmExitPushQueue                      **
; ..\mcal_src\Adc_Utility.h	   645  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   646  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   647  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   648  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   649  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   650  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   651  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   652  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   653  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   654  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   655  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   656  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   657  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   658  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   659  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   660  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   661  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   662  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   663  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   664  extern void Adc_lSchmExitPushQueue(void);
; ..\mcal_src\Adc_Utility.h	   665  #endif /* (ADC_ENABLE_QUEUING == STD_ON) */
; ..\mcal_src\Adc_Utility.h	   666  
; ..\mcal_src\Adc_Utility.h	   667  /* Full priority considered */
; ..\mcal_src\Adc_Utility.h	   668  #if (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW)
; ..\mcal_src\Adc_Utility.h	   669  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   670  ** Syntax           : extern void Adc_lSchmEnterScheduleNext                  **
; ..\mcal_src\Adc_Utility.h	   671  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   672  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   673  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   674  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   675  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   676  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   677  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   678  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   679  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   680  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   681  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   682  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   683  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   684  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   685  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   686  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   687  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   688  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   689  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   690  extern void Adc_lSchmEnterScheduleNext(void);
; ..\mcal_src\Adc_Utility.h	   691  
; ..\mcal_src\Adc_Utility.h	   692  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   693  ** Syntax           : extern void Adc_lSchmExitScheduleNext                   **
; ..\mcal_src\Adc_Utility.h	   694  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   695  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   696  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   697  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   698  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   699  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   700  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   701  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   702  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   703  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   704  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   705  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   706  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   707  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   708  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   709  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   710  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   711  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   712  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   713  extern void Adc_lSchmExitScheduleNext(void);
; ..\mcal_src\Adc_Utility.h	   714  
; ..\mcal_src\Adc_Utility.h	   715  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   716  ** Syntax           : extern void Adc_lSchmEnterScheduleStart                 **
; ..\mcal_src\Adc_Utility.h	   717  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   718  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   719  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   720  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   721  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   722  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   723  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   724  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   725  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   726  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   727  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   728  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   729  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   730  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   731  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   732  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   733  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   734  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   735  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   736  extern void Adc_lSchmEnterScheduleStart(void);
; ..\mcal_src\Adc_Utility.h	   737  
; ..\mcal_src\Adc_Utility.h	   738  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   739  ** Syntax           : extern void Adc_lSchmExitScheduleStart                  **
; ..\mcal_src\Adc_Utility.h	   740  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   741  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   742  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   743  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   744  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   745  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   746  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   747  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   748  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   749  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   750  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   751  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   752  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   753  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   754  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   755  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   756  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   757  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   758  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   759  extern void Adc_lSchmExitScheduleStart(void);
; ..\mcal_src\Adc_Utility.h	   760  
; ..\mcal_src\Adc_Utility.h	   761  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   762  ** Syntax           : extern void Adc_lSchmEnterScheduleStop                  **
; ..\mcal_src\Adc_Utility.h	   763  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   764  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   765  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   766  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   767  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   768  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   769  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   770  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   771  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   772  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   773  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   774  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   775  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   776  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   777  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   778  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   779  ** Description      : Calls the SchM function to suspend the interrupts       **
; ..\mcal_src\Adc_Utility.h	   780  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   781  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   782  extern void Adc_lSchmEnterScheduleStop(void);
; ..\mcal_src\Adc_Utility.h	   783  
; ..\mcal_src\Adc_Utility.h	   784  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   785  ** Syntax           : extern void Adc_lSchmExitScheduleStop                   **
; ..\mcal_src\Adc_Utility.h	   786  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   787  **                       void                                                 **
; ..\mcal_src\Adc_Utility.h	   788  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   789  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   790  ** Service ID       : NA                                                      **
; ..\mcal_src\Adc_Utility.h	   791  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   792  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   793  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   794  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   795  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   796  ** Parameters(in)   : None                                                    **
; ..\mcal_src\Adc_Utility.h	   797  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   798  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   799  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   800  ** Return value     : None                                                    **
; ..\mcal_src\Adc_Utility.h	   801  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   802  ** Description      : Calls the SchM function to resume the interrupts        **
; ..\mcal_src\Adc_Utility.h	   803  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   804  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   805  extern void Adc_lSchmExitScheduleStop(void);
; ..\mcal_src\Adc_Utility.h	   806  
; ..\mcal_src\Adc_Utility.h	   807  #endif /* (ADC_PRIORITY_IMPLEMENTATION == ADC_PRIORITY_HW_SW) */
; ..\mcal_src\Adc_Utility.h	   808  
; ..\mcal_src\Adc_Utility.h	   809  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   810  **                      Global Inline Function Definitions                    **
; ..\mcal_src\Adc_Utility.h	   811  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   812  
; ..\mcal_src\Adc_Utility.h	   813  /*******************************************************************************
; ..\mcal_src\Adc_Utility.h	   814  ** Syntax           : _IFXEXTERN_ IFX_INLINE uint16 Adc_lKernelConfigured     **
; ..\mcal_src\Adc_Utility.h	   815  **                    (                                                       **
; ..\mcal_src\Adc_Utility.h	   816  **                      uint8 Kernel                                          **
; ..\mcal_src\Adc_Utility.h	   817  **                    )                                                       **
; ..\mcal_src\Adc_Utility.h	   818  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   819  ** Service ID       : None                                                    **
; ..\mcal_src\Adc_Utility.h	   820  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   821  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Adc_Utility.h	   822  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   823  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Adc_Utility.h	   824  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   825  ** Parameters (in)  : Kernel - Adc Kernel to be checked                       **
; ..\mcal_src\Adc_Utility.h	   826  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   827  ** Parameters (out) : None                                                    **
; ..\mcal_src\Adc_Utility.h	   828  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   829  ** Return value     : RetVal                                                  **
; ..\mcal_src\Adc_Utility.h	   830  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   831  ** Description      :                                                         **
; ..\mcal_src\Adc_Utility.h	   832  ** - The function is to check if the Adc kernel is configured or not          **
; ..\mcal_src\Adc_Utility.h	   833  **                                                                            **
; ..\mcal_src\Adc_Utility.h	   834  *******************************************************************************/
; ..\mcal_src\Adc_Utility.h	   835  /*IFX_MISRA_RULE_08_05_STATUS=Allowed for inline functions defined in header
; ..\mcal_src\Adc_Utility.h	   836   files*/
; ..\mcal_src\Adc_Utility.h	   837  _IFXEXTERN_ IFX_INLINE uint16 Adc_lKernelConfigured(uint8 Kernel)
; ..\mcal_src\Adc_Utility.h	   838  {
; ..\mcal_src\Adc_Utility.h	   839    /*IFX_MISRA_RULE_08_05_STATUS=Allowed for inline functions defined in header
; ..\mcal_src\Adc_Utility.h	   840     files*/
; ..\mcal_src\Adc_Utility.h	   841    uint16 RetVal;
; ..\mcal_src\Adc_Utility.h	   842  
; ..\mcal_src\Adc_Utility.h	   843    RetVal = (uint16)( (uint16)((1UL) << (Kernel)) & ((uint16)ADC_USED_KERNELS) );
	mov	d15,#1
.L42:
	sh	d15,d15,d0
.L32:
	and	d15,#3
.L43:

; ..\mcal_src\Adc_Utility.h	   845    if (RetVal != 0U)      (inlined)
	jeq	d15,#0,.L3
.L44:

; ..\mcal_src\Adc_Utility.h	   844  
; ..\mcal_src\Adc_Utility.h	   845    if (RetVal != 0U)
; ..\mcal_src\Adc_Utility.h	   846    {
; ..\mcal_src\Adc_Utility.h	   847      /* UTP AI00133878 */
; ..\mcal_src\Adc_Utility.h	   848      if (Adc_kConfigPtr->CfgPtr[Kernel] == NULL_PTR)
	ld.w	d1,[a15]
.L45:

; ..\mcal_src\Adc_Utility.h	   849      {
; ..\mcal_src\Adc_Utility.h	   850        RetVal = 0U;
	sel	d15,d1,d15,#0

; ..\mcal_src\Adc_Utility.h	   845    if (RetVal != 0U)      (inlined)
.L3:
	jeq	d15,#0,.L5
.L24:
	sha	d15,d0,#10
	movh.a	a4,#61442
.L33:
	lea	a4,[a4]@los(0xf0020480)
	addsc.a	a4,a4,d15,#0
.L46:
	movh	d1,#12288
.L47:
	ld.w	d15,[a4]
.L48:
	and	d15,d1
.L49:
	movh	d1,#8192
.L50:
	eq	d15,d15,d1
.L51:
	cmovn	d2,d15,#1
.L5:
	add	d0,#1
	add.a	a15,#4
	loop	a2,.L2
.L52:

; ..\mcal_src\Adc_Calibration.c	   119  
; ..\mcal_src\Adc_Calibration.c	   120    return (Status);
; ..\mcal_src\Adc_Calibration.c	   121  } /* Adc_17_GetStartupCalStatus */
	ret
.L20:
	
__Adc_17_GetStartupCalStatus_function_end:
	.size	Adc_17_GetStartupCalStatus,__Adc_17_GetStartupCalStatus_function_end-Adc_17_GetStartupCalStatus
.L18:
	; End of function
	
	.extern	Adc_kConfigPtr
	.calls	'Adc_17_GetStartupCalStatus','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L11:
	.word	51672
	.half	3
	.word	.L12
	.byte	4
.L10:
	.byte	1
	.byte	'..\\mcal_src\\Adc_Calibration.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L13
.L28:
	.byte	2
	.byte	'unsigned short int',0,2,7
.L23:
	.byte	3
	.byte	'Adc_lKernelConfigured',0,3,1,197,6,31
	.word	184
	.byte	1,1
.L19:
	.byte	2
	.byte	'unsigned char',0,1,8
.L25:
	.byte	4
	.byte	'Kernel',0,1,197,6,59
	.word	240
.L27:
	.byte	5,0,6
	.word	206
	.byte	7
	.word	257
	.byte	5,0,8
	.byte	'void',0,9
	.word	287
	.byte	10
	.byte	'__prof_adm',0,2,1,1
	.word	293
	.byte	11,1,9
	.word	317
	.byte	10
	.byte	'__codeptr',0,2,1,1
	.word	319
	.byte	12
	.byte	'_Ifx_VADC_ACCEN0_Bits',0,3,49,16,4,13
	.byte	'EN0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	240
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	240
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	240
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_ACCEN0_Bits',0,3,83,3
	.word	342
	.byte	12
	.byte	'_Ifx_VADC_ACCPROT0_Bits',0,3,86,16,4,13
	.byte	'APC0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'APC1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'APC2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'APC3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,2
	.word	184
	.byte	11,1,2,35,0,13
	.byte	'APEM',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'API0',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'API1',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'API2',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'API3',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,2
	.word	184
	.byte	11,1,2,35,2,13
	.byte	'APGC',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_ACCPROT0_Bits',0,3,100,3
	.word	901
	.byte	12
	.byte	'_Ifx_VADC_ACCPROT1_Bits',0,3,103,16,4,13
	.byte	'APS0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'APS1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'APS2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'APS3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,2
	.word	184
	.byte	11,1,2,35,0,13
	.byte	'APTF',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'APR0',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'APR1',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'APR2',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'APR3',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,2
	.word	184
	.byte	12,0,2,35,2,0,10
	.byte	'Ifx_VADC_ACCPROT1_Bits',0,3,116,3
	.word	1167
	.byte	12
	.byte	'_Ifx_VADC_BRSCTRL_Bits',0,3,119,16,4,13
	.byte	'SRCRESREG',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'XTSEL',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'XTLVL',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'XTMODE',0,1
	.word	240
	.byte	2,1,2,35,1,13
	.byte	'XTWC',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'GTSEL',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'GTLVL',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	2,1,2,35,2,13
	.byte	'GTWC',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_VADC_BRSCTRL_Bits',0,3,132,1,3
	.word	1417
	.byte	12
	.byte	'_Ifx_VADC_BRSMR_Bits',0,3,135,1,16,4,13
	.byte	'ENGT',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'ENTR',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'ENSI',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'SCAN',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'LDM',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'REQGT',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'CLRPND',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'LDEV',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'reserved_10',0,1
	.word	240
	.byte	6,0,2,35,1,13
	.byte	'RPTDIS',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,2
	.word	184
	.byte	15,0,2,35,2,0,10
	.byte	'Ifx_VADC_BRSMR_Bits',0,3,149,1,3
	.word	1684
	.byte	12
	.byte	'_Ifx_VADC_BRSPND_Bits',0,3,152,1,16,4,2
	.byte	'unsigned int',0,4,7,13
	.byte	'CHPNDGy',0,4
	.word	1985
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_VADC_BRSPND_Bits',0,3,155,1,3
	.word	1957
	.byte	12
	.byte	'_Ifx_VADC_BRSSEL_Bits',0,3,158,1,16,4,13
	.byte	'CHSELGy',0,4
	.word	1985
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_VADC_BRSSEL_Bits',0,3,161,1,3
	.word	2051
	.byte	12
	.byte	'_Ifx_VADC_CLC_Bits',0,3,164,1,16,4,13
	.byte	'DISR',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'DISS',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'EDIS',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_VADC_CLC_Bits',0,3,171,1,3
	.word	2129
	.byte	12
	.byte	'_Ifx_VADC_EMUXSEL_Bits',0,3,174,1,16,4,13
	.byte	'EMUXGRP0',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'EMUXGRP1',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	1985
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_VADC_EMUXSEL_Bits',0,3,179,1,3
	.word	2274
	.byte	12
	.byte	'_Ifx_VADC_G_ALIAS_Bits',0,3,182,1,16,4,13
	.byte	'ALIAS0',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'reserved_5',0,1
	.word	240
	.byte	3,0,2,35,0,13
	.byte	'ALIAS1',0,1
	.word	240
	.byte	5,3,2,35,1,13
	.byte	'reserved_13',0,4
	.word	1985
	.byte	19,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_ALIAS_Bits',0,3,188,1,3
	.word	2397
	.byte	12
	.byte	'_Ifx_VADC_G_ARBCFG_Bits',0,3,191,1,16,4,13
	.byte	'ANONC',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'ARBRND',0,1
	.word	240
	.byte	2,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'ARBM',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'ANONS',0,1
	.word	240
	.byte	2,6,2,35,2,13
	.byte	'CSRC',0,1
	.word	240
	.byte	2,4,2,35,2,13
	.byte	'CHNR',0,2
	.word	184
	.byte	5,7,2,35,2,13
	.byte	'SYNRUN',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	2,4,2,35,3,13
	.byte	'CAL',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'CALS',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'BUSY',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'SAMPLE',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_ARBCFG_Bits',0,3,208,1,3
	.word	2539
	.byte	12
	.byte	'_Ifx_VADC_G_ARBPR_Bits',0,3,211,1,16,4,13
	.byte	'PRIO0',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'CSM0',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'PRIO1',0,1
	.word	240
	.byte	2,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'CSM1',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'PRIO2',0,1
	.word	240
	.byte	2,6,2,35,1,13
	.byte	'reserved_10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'CSM2',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'PRIO3',0,1
	.word	240
	.byte	2,2,2,35,1,13
	.byte	'reserved_14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'CSM3',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,1
	.word	240
	.byte	8,0,2,35,2,13
	.byte	'ASEN0',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'ASEN1',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'ASEN2',0,1
	.word	240
	.byte	1,5,2,35,3,13
	.byte	'ASEN3',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_ARBPR_Bits',0,3,231,1,3
	.word	2874
	.byte	12
	.byte	'_Ifx_VADC_G_ASCTRL_Bits',0,3,234,1,16,4,13
	.byte	'SRCRESREG',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'XTSEL',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'XTLVL',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'XTMODE',0,1
	.word	240
	.byte	2,1,2,35,1,13
	.byte	'XTWC',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'GTSEL',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'GTLVL',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	2,1,2,35,2,13
	.byte	'GTWC',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'TMEN',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	240
	.byte	2,1,2,35,3,13
	.byte	'TMWC',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_ASCTRL_Bits',0,3,250,1,3
	.word	3271
	.byte	12
	.byte	'_Ifx_VADC_G_ASMR_Bits',0,3,253,1,16,4,13
	.byte	'ENGT',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'ENTR',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'ENSI',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'SCAN',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'LDM',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'REQGT',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'CLRPND',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'LDEV',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'reserved_10',0,1
	.word	240
	.byte	6,0,2,35,1,13
	.byte	'RPTDIS',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,2
	.word	184
	.byte	15,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_ASMR_Bits',0,3,139,2,3
	.word	3596
	.byte	12
	.byte	'_Ifx_VADC_G_ASPND_Bits',0,3,142,2,16,4,13
	.byte	'CHPND',0,4
	.word	1985
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_ASPND_Bits',0,3,145,2,3
	.word	3871
	.byte	12
	.byte	'_Ifx_VADC_G_ASSEL_Bits',0,3,148,2,16,4,13
	.byte	'CHSEL',0,4
	.word	1985
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_ASSEL_Bits',0,3,151,2,3
	.word	3949
	.byte	12
	.byte	'_Ifx_VADC_G_BFL_Bits',0,3,154,2,16,4,13
	.byte	'BFL0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'BFL1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'BFL2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'BFL3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'BFA0',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'BFA1',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'BFA2',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'BFA3',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'BFI0',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'BFI1',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'BFI2',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'BFI3',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,2
	.word	184
	.byte	12,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_BFL_Bits',0,3,171,2,3
	.word	4027
	.byte	12
	.byte	'_Ifx_VADC_G_BFLC_Bits',0,3,174,2,16,4,13
	.byte	'BFM0',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'BFM1',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'BFM2',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'BFM3',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_BFLC_Bits',0,3,181,2,3
	.word	4344
	.byte	12
	.byte	'_Ifx_VADC_G_BFLNP_Bits',0,3,184,2,16,4,13
	.byte	'BFL0NP',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'BFL1NP',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'BFL2NP',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'BFL3NP',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_BFLNP_Bits',0,3,191,2,3
	.word	4490
	.byte	12
	.byte	'_Ifx_VADC_G_BFLS_Bits',0,3,194,2,16,4,13
	.byte	'BFC0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'BFC1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'BFC2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'BFC3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,2
	.word	184
	.byte	12,0,2,35,0,13
	.byte	'BFS0',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'BFS1',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'BFS2',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'BFS3',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,2
	.word	184
	.byte	12,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_BFLS_Bits',0,3,206,2,3
	.word	4646
	.byte	12
	.byte	'_Ifx_VADC_G_BOUND_Bits',0,3,209,2,16,4,13
	.byte	'BOUNDARY0',0,2
	.word	184
	.byte	12,4,2,35,0,13
	.byte	'reserved_12',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'BOUNDARY1',0,2
	.word	184
	.byte	12,4,2,35,2,13
	.byte	'reserved_28',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_BOUND_Bits',0,3,215,2,3
	.word	4878
	.byte	12
	.byte	'_Ifx_VADC_G_CEFCLR_Bits',0,3,218,2,16,4,13
	.byte	'CEV0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'CEV1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'CEV2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'CEV3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'CEV4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'CEV5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'CEV6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'CEV7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'CEV8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'CEV9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'CEV10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'CEV11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'CEV12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'CEV13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'CEV14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'CEV15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_CEFCLR_Bits',0,3,237,2,3
	.word	5027
	.byte	12
	.byte	'_Ifx_VADC_G_CEFLAG_Bits',0,3,240,2,16,4,13
	.byte	'CEV0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'CEV1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'CEV2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'CEV3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'CEV4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'CEV5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'CEV6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'CEV7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'CEV8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'CEV9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'CEV10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'CEV11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'CEV12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'CEV13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'CEV14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'CEV15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_CEFLAG_Bits',0,3,131,3,3
	.word	5375
	.byte	12
	.byte	'_Ifx_VADC_G_CEVNP0_Bits',0,3,134,3,16,4,13
	.byte	'CEV0NP',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'CEV1NP',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'CEV2NP',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'CEV3NP',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'CEV4NP',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'CEV5NP',0,1
	.word	240
	.byte	4,0,2,35,2,13
	.byte	'CEV6NP',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'CEV7NP',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_CEVNP0_Bits',0,3,144,3,3
	.word	5723
	.byte	12
	.byte	'_Ifx_VADC_G_CEVNP1_Bits',0,3,147,3,16,4,13
	.byte	'CEV8NP',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'CEV9NP',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'CEV10NP',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'CEV11NP',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'CEV12NP',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'CEV13NP',0,1
	.word	240
	.byte	4,0,2,35,2,13
	.byte	'CEV14NP',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'CEV15NP',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_CEVNP1_Bits',0,3,157,3,3
	.word	5930
	.byte	12
	.byte	'_Ifx_VADC_G_CHASS_Bits',0,3,160,3,16,4,13
	.byte	'ASSCH0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ASSCH1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'ASSCH2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'ASSCH3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'ASSCH4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'ASSCH5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'ASSCH6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'ASSCH7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'ASSCH8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'ASSCH9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'ASSCH10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'ASSCH11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'ASSCH12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'ASSCH13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'ASSCH14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'ASSCH15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'ASSCH16',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'ASSCH17',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'ASSCH18',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'ASSCH19',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'ASSCH20',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'ASSCH21',0,1
	.word	240
	.byte	1,2,2,35,2,13
	.byte	'ASSCH22',0,1
	.word	240
	.byte	1,1,2,35,2,13
	.byte	'ASSCH23',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'ASSCH24',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'ASSCH25',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'ASSCH26',0,1
	.word	240
	.byte	1,5,2,35,3,13
	.byte	'ASSCH27',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'ASSCH28',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'ASSCH29',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'ASSCH30',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'ASSCH31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_CHASS_Bits',0,3,194,3,3
	.word	6143
	.byte	12
	.byte	'_Ifx_VADC_G_CHCTR_Bits',0,3,197,3,16,4,13
	.byte	'ICLSEL',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'BNDSELL',0,1
	.word	240
	.byte	2,2,2,35,0,13
	.byte	'BNDSELU',0,1
	.word	240
	.byte	2,0,2,35,0,13
	.byte	'CHEVMODE',0,1
	.word	240
	.byte	2,6,2,35,1,13
	.byte	'SYNC',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'REFSEL',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'BNDSELX',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'RESREG',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'RESTBS',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'RESPOS',0,1
	.word	240
	.byte	1,2,2,35,2,13
	.byte	'reserved_22',0,2
	.word	184
	.byte	6,4,2,35,2,13
	.byte	'BWDCH',0,1
	.word	240
	.byte	2,2,2,35,3,13
	.byte	'BWDEN',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_CHCTR_Bits',0,3,214,3,3
	.word	6802
	.byte	12
	.byte	'_Ifx_VADC_G_EMUXCTR_Bits',0,3,217,3,16,4,13
	.byte	'EMUXSET',0,1
	.word	240
	.byte	3,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	240
	.byte	5,0,2,35,0,13
	.byte	'EMUXACT',0,1
	.word	240
	.byte	3,5,2,35,1,13
	.byte	'reserved_11',0,1
	.word	240
	.byte	5,0,2,35,1,13
	.byte	'EMUXCH',0,2
	.word	184
	.byte	10,6,2,35,2,13
	.byte	'EMUXMODE',0,1
	.word	240
	.byte	2,4,2,35,3,13
	.byte	'EMXCOD',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'EMXST',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'EMXCSS',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'EMXWC',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_EMUXCTR_Bits',0,3,229,3,3
	.word	7148
	.byte	12
	.byte	'_Ifx_VADC_G_Q0R0_Bits',0,3,232,3,16,4,13
	.byte	'REQCHNR',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'RF',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'ENSI',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'EXTR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'V',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,4
	.word	1985
	.byte	23,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_Q0R0_Bits',0,3,240,3,3
	.word	7404
	.byte	12
	.byte	'_Ifx_VADC_G_Q0R3_Bits',0,3,243,3,16,4,13
	.byte	'REQCHNR',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'RF',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'ENSI',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'EXTR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'V',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'PDD',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'MDPD',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'MDPU',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'CDEN',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'CDSEL',0,1
	.word	240
	.byte	2,1,2,35,1,13
	.byte	'reserved_15',0,4
	.word	1985
	.byte	17,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_Q0R3_Bits',0,3,128,4,3
	.word	7563
	.byte	12
	.byte	'_Ifx_VADC_G_QBUR0_Bits',0,3,131,4,16,4,13
	.byte	'REQCHNR',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'RF',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'ENSI',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'EXTR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'V',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,4
	.word	1985
	.byte	23,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_QBUR0_Bits',0,3,139,4,3
	.word	7803
	.byte	12
	.byte	'_Ifx_VADC_G_QBUR3_Bits',0,3,142,4,16,4,13
	.byte	'REQCHNR',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'RF',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'ENSI',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'EXTR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'V',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'PDD',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'MDPD',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'MDPU',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'CDEN',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'CDSEL',0,1
	.word	240
	.byte	2,1,2,35,1,13
	.byte	'reserved_15',0,4
	.word	1985
	.byte	17,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_QBUR3_Bits',0,3,155,4,3
	.word	7964
	.byte	12
	.byte	'_Ifx_VADC_G_QCTRL0_Bits',0,3,158,4,16,4,13
	.byte	'SRCRESREG',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'XTSEL',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'XTLVL',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'XTMODE',0,1
	.word	240
	.byte	2,1,2,35,1,13
	.byte	'XTWC',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'GTSEL',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'GTLVL',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	2,1,2,35,2,13
	.byte	'GTWC',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'TMEN',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	240
	.byte	2,1,2,35,3,13
	.byte	'TMWC',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_QCTRL0_Bits',0,3,174,4,3
	.word	8206
	.byte	12
	.byte	'_Ifx_VADC_G_QCTRL3_Bits',0,3,177,4,16,4,13
	.byte	'SRCRESREG',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'XTSEL',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'XTLVL',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'XTMODE',0,1
	.word	240
	.byte	2,1,2,35,1,13
	.byte	'XTWC',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'GTSEL',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'GTLVL',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	2,1,2,35,2,13
	.byte	'GTWC',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'TMEN',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	240
	.byte	2,1,2,35,3,13
	.byte	'TMWC',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_QCTRL3_Bits',0,3,193,4,3
	.word	8531
	.byte	12
	.byte	'_Ifx_VADC_G_QINR0_Bits',0,3,196,4,16,4,13
	.byte	'REQCHNR',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'RF',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'ENSI',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'EXTR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	1985
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_QINR0_Bits',0,3,203,4,3
	.word	8856
	.byte	12
	.byte	'_Ifx_VADC_G_QINR3_Bits',0,3,206,4,16,4,13
	.byte	'REQCHNR',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'RF',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'ENSI',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'EXTR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'PDD',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'MDPD',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'MDPU',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'CDEN',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'CDSEL',0,1
	.word	240
	.byte	2,1,2,35,1,13
	.byte	'reserved_15',0,4
	.word	1985
	.byte	17,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_QINR3_Bits',0,3,219,4,3
	.word	9004
	.byte	12
	.byte	'_Ifx_VADC_G_QMR0_Bits',0,3,222,4,16,4,13
	.byte	'ENGT',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'ENTR',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	240
	.byte	5,0,2,35,0,13
	.byte	'CLRV',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'TREV',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'FLUSH',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'CEV',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'RPTDIS',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,2
	.word	184
	.byte	15,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_QMR0_Bits',0,3,234,4,3
	.word	9255
	.byte	12
	.byte	'_Ifx_VADC_G_QMR3_Bits',0,3,237,4,16,4,13
	.byte	'ENGT',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'ENTR',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	240
	.byte	5,0,2,35,0,13
	.byte	'CLRV',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'TREV',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'FLUSH',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'CEV',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'RPTDIS',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,2
	.word	184
	.byte	15,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_QMR3_Bits',0,3,249,4,3
	.word	9496
	.byte	12
	.byte	'_Ifx_VADC_G_QSR0_Bits',0,3,252,4,16,4,13
	.byte	'FILL',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'EMPTY',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'REQGT',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'EV',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,4
	.word	1985
	.byte	23,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_QSR0_Bits',0,3,133,5,3
	.word	9737
	.byte	12
	.byte	'_Ifx_VADC_G_QSR3_Bits',0,3,136,5,16,4,13
	.byte	'FILL',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'EMPTY',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'REQGT',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'EV',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,4
	.word	1985
	.byte	23,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_QSR3_Bits',0,3,145,5,3
	.word	9926
	.byte	12
	.byte	'_Ifx_VADC_G_RCR_Bits',0,3,148,5,16,4,13
	.byte	'reserved_0',0,2
	.word	184
	.byte	16,0,2,35,0,13
	.byte	'DRCTR',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'DMM',0,1
	.word	240
	.byte	2,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	240
	.byte	2,0,2,35,2,13
	.byte	'WFR',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'FEN',0,1
	.word	240
	.byte	2,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	240
	.byte	4,1,2,35,3,13
	.byte	'SRGEN',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_RCR_Bits',0,3,158,5,3
	.word	10115
	.byte	12
	.byte	'_Ifx_VADC_G_REFCLR_Bits',0,3,161,5,16,4,13
	.byte	'REV0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'REV1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'REV2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'REV3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'REV4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'REV5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'REV6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'REV7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'REV8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'REV9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'REV10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'REV11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'REV12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'REV13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'REV14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'REV15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_REFCLR_Bits',0,3,180,5,3
	.word	10319
	.byte	12
	.byte	'_Ifx_VADC_G_REFLAG_Bits',0,3,183,5,16,4,13
	.byte	'REV0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'REV1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'REV2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'REV3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'REV4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'REV5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'REV6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'REV7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'REV8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'REV9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'REV10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'REV11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'REV12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'REV13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'REV14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'REV15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_REFLAG_Bits',0,3,202,5,3
	.word	10667
	.byte	12
	.byte	'_Ifx_VADC_G_RES_Bits',0,3,205,5,16,4,13
	.byte	'RESULT',0,2
	.word	184
	.byte	16,0,2,35,0,13
	.byte	'DRC',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'CHNR',0,2
	.word	184
	.byte	5,7,2,35,2,13
	.byte	'EMUX',0,1
	.word	240
	.byte	3,4,2,35,3,13
	.byte	'CRS',0,1
	.word	240
	.byte	2,2,2,35,3,13
	.byte	'FCR',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'VF',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_RES_Bits',0,3,214,5,3
	.word	11015
	.byte	12
	.byte	'_Ifx_VADC_G_RESD_Bits',0,3,217,5,16,4,13
	.byte	'RESULT',0,2
	.word	184
	.byte	16,0,2,35,0,13
	.byte	'DRC',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'CHNR',0,2
	.word	184
	.byte	5,7,2,35,2,13
	.byte	'EMUX',0,1
	.word	240
	.byte	3,4,2,35,3,13
	.byte	'CRS',0,1
	.word	240
	.byte	2,2,2,35,3,13
	.byte	'FCR',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'VF',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_RESD_Bits',0,3,226,5,3
	.word	11181
	.byte	12
	.byte	'_Ifx_VADC_G_REVNP0_Bits',0,3,229,5,16,4,13
	.byte	'REV0NP',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'REV1NP',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'REV2NP',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'REV3NP',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'REV4NP',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'REV5NP',0,1
	.word	240
	.byte	4,0,2,35,2,13
	.byte	'REV6NP',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'REV7NP',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_REVNP0_Bits',0,3,239,5,3
	.word	11349
	.byte	12
	.byte	'_Ifx_VADC_G_REVNP1_Bits',0,3,242,5,16,4,13
	.byte	'REV8NP',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'REV9NP',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'REV10NP',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'REV11NP',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'REV12NP',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'REV13NP',0,1
	.word	240
	.byte	4,0,2,35,2,13
	.byte	'REV14NP',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'REV15NP',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_REVNP1_Bits',0,3,252,5,3
	.word	11556
	.byte	12
	.byte	'_Ifx_VADC_G_RRASS_Bits',0,3,255,5,16,4,13
	.byte	'ASSRR0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ASSRR1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'ASSRR2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'ASSRR3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'ASSRR4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'ASSRR5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'ASSRR6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'ASSRR7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'ASSRR8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'ASSRR9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'ASSRR10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'ASSRR11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'ASSRR12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'ASSRR13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'ASSRR14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'ASSRR15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_RRASS_Bits',0,3,146,6,3
	.word	11769
	.byte	12
	.byte	'_Ifx_VADC_G_SEFCLR_Bits',0,3,149,6,16,4,13
	.byte	'SEV0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'SEV1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'SEV3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_SEFCLR_Bits',0,3,156,6,3
	.word	12147
	.byte	12
	.byte	'_Ifx_VADC_G_SEFLAG_Bits',0,3,159,6,16,4,13
	.byte	'SEV0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'SEV1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'SEV3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_SEFLAG_Bits',0,3,166,6,3
	.word	12302
	.byte	12
	.byte	'_Ifx_VADC_G_SEVNP_Bits',0,3,169,6,16,4,13
	.byte	'SEV0NP',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'SEV1NP',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'SEV3NP',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_SEVNP_Bits',0,3,176,6,3
	.word	12457
	.byte	12
	.byte	'_Ifx_VADC_G_SRACT_Bits',0,3,179,6,16,4,13
	.byte	'AGSR0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'AGSR1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'AGSR2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'AGSR3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'ASSR0',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'ASSR1',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'ASSR2',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'ASSR3',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,4
	.word	1985
	.byte	20,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_SRACT_Bits',0,3,191,6,3
	.word	12617
	.byte	12
	.byte	'_Ifx_VADC_G_SYNCTR_Bits',0,3,194,6,16,4,13
	.byte	'STSEL',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'EVALR1',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'EVALR2',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'EVALR3',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'reserved_7',0,4
	.word	1985
	.byte	25,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_SYNCTR_Bits',0,3,202,6,3
	.word	12859
	.byte	12
	.byte	'_Ifx_VADC_G_TRCTR_Bits',0,3,205,6,16,4,13
	.byte	'TSC',0,1
	.word	240
	.byte	6,2,2,35,0,13
	.byte	'reserved_6',0,2
	.word	184
	.byte	8,2,2,35,0,13
	.byte	'Q3ACT',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'OV',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'TSCSET',0,1
	.word	240
	.byte	6,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	240
	.byte	2,0,2,35,2,13
	.byte	'ITSEL',0,1
	.word	240
	.byte	2,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	2,4,2,35,3,13
	.byte	'SRDIS',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	240
	.byte	2,1,2,35,3,13
	.byte	'COV',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_G_TRCTR_Bits',0,3,218,6,3
	.word	13037
	.byte	12
	.byte	'_Ifx_VADC_G_VFR_Bits',0,3,221,6,16,4,13
	.byte	'VF0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'VF1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'VF2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'VF3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'VF4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'VF5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'VF6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'VF7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'VF8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'VF9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'VF10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'VF11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'VF12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'VF13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'VF14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'VF15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_G_VFR_Bits',0,3,240,6,3
	.word	13302
	.byte	12
	.byte	'_Ifx_VADC_GLOBBOUND_Bits',0,3,243,6,16,4,13
	.byte	'BOUNDARY0',0,2
	.word	184
	.byte	12,4,2,35,0,13
	.byte	'reserved_12',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'BOUNDARY1',0,2
	.word	184
	.byte	12,4,2,35,2,13
	.byte	'reserved_28',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_VADC_GLOBBOUND_Bits',0,3,249,6,3
	.word	13628
	.byte	12
	.byte	'_Ifx_VADC_GLOBCFG_Bits',0,3,252,6,16,4,13
	.byte	'DIVA',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'reserved_5',0,1
	.word	240
	.byte	2,1,2,35,0,13
	.byte	'DCMSB',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'DIVD',0,1
	.word	240
	.byte	2,6,2,35,1,13
	.byte	'reserved_10',0,1
	.word	240
	.byte	2,4,2,35,1,13
	.byte	'REFPC',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'reserved_13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'LOSUP',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'DIVWC',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'DPCAL0',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'DPCAL1',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'DPCAL2',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'DPCAL3',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,2
	.word	184
	.byte	11,1,2,35,2,13
	.byte	'SUCAL',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_GLOBCFG_Bits',0,3,141,7,3
	.word	13781
	.byte	12
	.byte	'_Ifx_VADC_GLOBEFLAG_Bits',0,3,144,7,16,4,13
	.byte	'SEVGLB',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	240
	.byte	7,0,2,35,0,13
	.byte	'REVGLB',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,1
	.word	240
	.byte	7,0,2,35,1,13
	.byte	'SEVGLBCLR',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	240
	.byte	7,0,2,35,2,13
	.byte	'REVGLBCLR',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	240
	.byte	7,0,2,35,3,0,10
	.byte	'Ifx_VADC_GLOBEFLAG_Bits',0,3,154,7,3
	.word	14122
	.byte	12
	.byte	'_Ifx_VADC_GLOBEVNP_Bits',0,3,157,7,16,4,13
	.byte	'SEV0NP',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,2
	.word	184
	.byte	12,0,2,35,0,13
	.byte	'REV0NP',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'reserved_20',0,2
	.word	184
	.byte	12,0,2,35,2,0,10
	.byte	'Ifx_VADC_GLOBEVNP_Bits',0,3,163,7,3
	.word	14355
	.byte	12
	.byte	'_Ifx_VADC_GLOBRCR_Bits',0,3,166,7,16,4,13
	.byte	'reserved_0',0,2
	.word	184
	.byte	16,0,2,35,0,13
	.byte	'DRCTR',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'reserved_20',0,1
	.word	240
	.byte	4,0,2,35,2,13
	.byte	'WFR',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	240
	.byte	6,1,2,35,3,13
	.byte	'SRGEN',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_GLOBRCR_Bits',0,3,174,7,3
	.word	14499
	.byte	12
	.byte	'_Ifx_VADC_GLOBRES_Bits',0,3,177,7,16,4,13
	.byte	'RESULT',0,2
	.word	184
	.byte	16,0,2,35,0,13
	.byte	'GNR',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'CHNR',0,2
	.word	184
	.byte	5,7,2,35,2,13
	.byte	'EMUX',0,1
	.word	240
	.byte	3,4,2,35,3,13
	.byte	'CRS',0,1
	.word	240
	.byte	2,2,2,35,3,13
	.byte	'FCR',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'VF',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_GLOBRES_Bits',0,3,186,7,3
	.word	14677
	.byte	12
	.byte	'_Ifx_VADC_GLOBRESD_Bits',0,3,189,7,16,4,13
	.byte	'RESULT',0,2
	.word	184
	.byte	16,0,2,35,0,13
	.byte	'GNR',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'CHNR',0,2
	.word	184
	.byte	5,7,2,35,2,13
	.byte	'EMUX',0,1
	.word	240
	.byte	3,4,2,35,3,13
	.byte	'CRS',0,1
	.word	240
	.byte	2,2,2,35,3,13
	.byte	'FCR',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'VF',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_GLOBRESD_Bits',0,3,198,7,3
	.word	14847
	.byte	12
	.byte	'_Ifx_VADC_GLOBTE_Bits',0,3,201,7,16,4,13
	.byte	'TFEG0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'TFEG1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	1985
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_VADC_GLOBTE_Bits',0,3,206,7,3
	.word	15019
	.byte	12
	.byte	'_Ifx_VADC_GLOBTF_Bits',0,3,209,7,16,4,13
	.byte	'CDCH',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'CDGR',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'CDEN',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'CDSEL',0,1
	.word	240
	.byte	2,5,2,35,1,13
	.byte	'reserved_11',0,1
	.word	240
	.byte	4,1,2,35,1,13
	.byte	'CDWC',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'PDD',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'MDPD',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'MDPU',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	240
	.byte	4,1,2,35,2,13
	.byte	'MDWC',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	6,2,2,35,3,13
	.byte	'RCEN',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'RCWC',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_VADC_GLOBTF_Bits',0,3,225,7,3
	.word	15134
	.byte	12
	.byte	'_Ifx_VADC_ICLASS_Bits',0,3,228,7,16,4,13
	.byte	'STCS',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'reserved_5',0,1
	.word	240
	.byte	3,0,2,35,0,13
	.byte	'CMS',0,1
	.word	240
	.byte	3,5,2,35,1,13
	.byte	'reserved_11',0,1
	.word	240
	.byte	5,0,2,35,1,13
	.byte	'STCE',0,1
	.word	240
	.byte	5,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	3,0,2,35,2,13
	.byte	'CME',0,1
	.word	240
	.byte	3,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	240
	.byte	5,0,2,35,3,0,10
	.byte	'Ifx_VADC_ICLASS_Bits',0,3,238,7,3
	.word	15438
	.byte	12
	.byte	'_Ifx_VADC_ID_Bits',0,3,241,7,16,4,13
	.byte	'MODREV',0,1
	.word	240
	.byte	8,0,2,35,0,13
	.byte	'MODTYPE',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'MODNUMBER',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_VADC_ID_Bits',0,3,246,7,3
	.word	15650
	.byte	12
	.byte	'_Ifx_VADC_KRST0_Bits',0,3,249,7,16,4,13
	.byte	'RST',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'RSTSTAT',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	1985
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_VADC_KRST0_Bits',0,3,254,7,3
	.word	15759
	.byte	12
	.byte	'_Ifx_VADC_KRST1_Bits',0,3,129,8,16,4,13
	.byte	'RST',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	1985
	.byte	31,0,2,35,2,0,10
	.byte	'Ifx_VADC_KRST1_Bits',0,3,133,8,3
	.word	15872
	.byte	12
	.byte	'_Ifx_VADC_KRSTCLR_Bits',0,3,136,8,16,4,13
	.byte	'CLR',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	1985
	.byte	31,0,2,35,2,0,10
	.byte	'Ifx_VADC_KRSTCLR_Bits',0,3,140,8,3
	.word	15966
	.byte	12
	.byte	'_Ifx_VADC_OCS_Bits',0,3,143,8,16,4,13
	.byte	'TGS',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'TGB',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'TG_P',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	20,8,2,35,2,13
	.byte	'SUS',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'SUS_P',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'SUSSTA',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	240
	.byte	2,0,2,35,3,0,10
	.byte	'Ifx_VADC_OCS_Bits',0,3,153,8,3
	.word	16064
	.byte	14,3,161,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,2
	.byte	'int',0,4,5,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	342
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_ACCEN0',0,3,166,8,3
	.word	16258
	.byte	14,3,169,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	901
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_ACCPROT0',0,3,174,8,3
	.word	16330
	.byte	14,3,177,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1167
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_ACCPROT1',0,3,182,8,3
	.word	16397
	.byte	14,3,185,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1417
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_BRSCTRL',0,3,190,8,3
	.word	16464
	.byte	14,3,193,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1684
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_BRSMR',0,3,198,8,3
	.word	16530
	.byte	14,3,201,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1957
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_BRSPND',0,3,206,8,3
	.word	16594
	.byte	14,3,209,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2051
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_BRSSEL',0,3,214,8,3
	.word	16659
	.byte	14,3,217,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2129
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_CLC',0,3,222,8,3
	.word	16724
	.byte	14,3,225,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2274
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_EMUXSEL',0,3,230,8,3
	.word	16786
	.byte	14,3,233,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2397
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_ALIAS',0,3,238,8,3
	.word	16852
	.byte	14,3,241,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2539
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_ARBCFG',0,3,246,8,3
	.word	16918
	.byte	14,3,249,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2874
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_ARBPR',0,3,254,8,3
	.word	16985
	.byte	14,3,129,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3271
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_ASCTRL',0,3,134,9,3
	.word	17051
	.byte	14,3,137,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3596
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_ASMR',0,3,142,9,3
	.word	17118
	.byte	14,3,145,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3871
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_ASPND',0,3,150,9,3
	.word	17183
	.byte	14,3,153,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3949
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_ASSEL',0,3,158,9,3
	.word	17249
	.byte	14,3,161,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4027
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_BFL',0,3,166,9,3
	.word	17315
	.byte	14,3,169,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4344
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_BFLC',0,3,174,9,3
	.word	17379
	.byte	14,3,177,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4490
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_BFLNP',0,3,182,9,3
	.word	17444
	.byte	14,3,185,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4646
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_BFLS',0,3,190,9,3
	.word	17510
	.byte	14,3,193,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4878
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_BOUND',0,3,198,9,3
	.word	17575
	.byte	14,3,201,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5027
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_CEFCLR',0,3,206,9,3
	.word	17641
	.byte	14,3,209,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5375
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_CEFLAG',0,3,214,9,3
	.word	17708
	.byte	14,3,217,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5723
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_CEVNP0',0,3,222,9,3
	.word	17775
	.byte	14,3,225,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5930
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_CEVNP1',0,3,230,9,3
	.word	17842
	.byte	14,3,233,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6143
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_CHASS',0,3,238,9,3
	.word	17909
	.byte	14,3,241,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6802
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_CHCTR',0,3,246,9,3
	.word	17975
	.byte	14,3,249,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7148
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_EMUXCTR',0,3,254,9,3
	.word	18041
	.byte	14,3,129,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7404
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_Q0R0',0,3,134,10,3
	.word	18109
	.byte	14,3,137,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7563
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_Q0R3',0,3,142,10,3
	.word	18174
	.byte	14,3,145,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7803
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QBUR0',0,3,150,10,3
	.word	18239
	.byte	14,3,153,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7964
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QBUR3',0,3,158,10,3
	.word	18305
	.byte	14,3,161,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8206
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QCTRL0',0,3,166,10,3
	.word	18371
	.byte	14,3,169,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8531
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QCTRL3',0,3,174,10,3
	.word	18438
	.byte	14,3,177,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8856
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QINR0',0,3,182,10,3
	.word	18505
	.byte	14,3,185,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	9004
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QINR3',0,3,190,10,3
	.word	18571
	.byte	14,3,193,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	9255
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QMR0',0,3,198,10,3
	.word	18637
	.byte	14,3,201,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	9496
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QMR3',0,3,206,10,3
	.word	18702
	.byte	14,3,209,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	9737
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QSR0',0,3,214,10,3
	.word	18767
	.byte	14,3,217,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	9926
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_QSR3',0,3,222,10,3
	.word	18832
	.byte	14,3,225,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	10115
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_RCR',0,3,230,10,3
	.word	18897
	.byte	14,3,233,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	10319
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_REFCLR',0,3,238,10,3
	.word	18961
	.byte	14,3,241,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	10667
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_REFLAG',0,3,246,10,3
	.word	19028
	.byte	14,3,249,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	11015
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_RES',0,3,254,10,3
	.word	19095
	.byte	14,3,129,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	11181
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_RESD',0,3,134,11,3
	.word	19159
	.byte	14,3,137,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	11349
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_REVNP0',0,3,142,11,3
	.word	19224
	.byte	14,3,145,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	11556
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_REVNP1',0,3,150,11,3
	.word	19291
	.byte	14,3,153,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	11769
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_RRASS',0,3,158,11,3
	.word	19358
	.byte	14,3,161,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	12147
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_SEFCLR',0,3,166,11,3
	.word	19424
	.byte	14,3,169,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	12302
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_SEFLAG',0,3,174,11,3
	.word	19491
	.byte	14,3,177,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	12457
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_SEVNP',0,3,182,11,3
	.word	19558
	.byte	14,3,185,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	12617
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_SRACT',0,3,190,11,3
	.word	19624
	.byte	14,3,193,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	12859
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_SYNCTR',0,3,198,11,3
	.word	19690
	.byte	14,3,201,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	13037
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_TRCTR',0,3,206,11,3
	.word	19757
	.byte	14,3,209,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	13302
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_G_VFR',0,3,214,11,3
	.word	19823
	.byte	14,3,217,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	13628
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBBOUND',0,3,222,11,3
	.word	19887
	.byte	14,3,225,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	13781
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBCFG',0,3,230,11,3
	.word	19955
	.byte	14,3,233,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	14122
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBEFLAG',0,3,238,11,3
	.word	20021
	.byte	14,3,241,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	14355
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBEVNP',0,3,246,11,3
	.word	20089
	.byte	14,3,249,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	14499
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBRCR',0,3,254,11,3
	.word	20156
	.byte	14,3,129,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	14677
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBRES',0,3,134,12,3
	.word	20222
	.byte	14,3,137,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	14847
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBRESD',0,3,142,12,3
	.word	20288
	.byte	14,3,145,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15019
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBTE',0,3,150,12,3
	.word	20355
	.byte	14,3,153,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15134
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_GLOBTF',0,3,158,12,3
	.word	20420
	.byte	14,3,161,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15438
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_ICLASS',0,3,166,12,3
	.word	20485
	.byte	14,3,169,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15650
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_ID',0,3,174,12,3
	.word	20550
	.byte	14,3,177,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15759
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_KRST0',0,3,182,12,3
	.word	20611
	.byte	14,3,185,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15872
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_KRST1',0,3,190,12,3
	.word	20675
	.byte	14,3,193,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15966
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_KRSTCLR',0,3,198,12,3
	.word	20739
	.byte	14,3,201,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	16064
	.byte	2,35,0,0,10
	.byte	'Ifx_VADC_OCS',0,3,206,12,3
	.word	20805
	.byte	12
	.byte	'_Ifx_VADC_G',0,3,217,12,25,128,8,15
	.byte	'ARBCFG',0,4
	.word	16918
	.byte	2,35,0,15
	.byte	'ARBPR',0,4
	.word	16985
	.byte	2,35,4,15
	.byte	'CHASS',0,4
	.word	17909
	.byte	2,35,8,15
	.byte	'RRASS',0,4
	.word	19358
	.byte	2,35,12,16,16
	.word	240
	.byte	17,15,0,15
	.byte	'reserved_10',0,16
	.word	20947
	.byte	2,35,16,16,8
	.word	20485
	.byte	17,1,0,15
	.byte	'ICLASS',0,8
	.word	20977
	.byte	2,35,32,16,8
	.word	240
	.byte	17,7,0,15
	.byte	'reserved_28',0,8
	.word	21002
	.byte	2,35,40,15
	.byte	'ALIAS',0,4
	.word	16852
	.byte	2,35,48,16,4
	.word	240
	.byte	17,3,0,15
	.byte	'reserved_34',0,4
	.word	21047
	.byte	2,35,52,15
	.byte	'BOUND',0,4
	.word	17575
	.byte	2,35,56,15
	.byte	'reserved_3C',0,4
	.word	21047
	.byte	2,35,60,15
	.byte	'SYNCTR',0,4
	.word	19690
	.byte	2,35,64,15
	.byte	'reserved_44',0,4
	.word	21047
	.byte	2,35,68,15
	.byte	'BFL',0,4
	.word	17315
	.byte	2,35,72,15
	.byte	'BFLS',0,4
	.word	17510
	.byte	2,35,76,15
	.byte	'BFLC',0,4
	.word	17379
	.byte	2,35,80,15
	.byte	'BFLNP',0,4
	.word	17444
	.byte	2,35,84,16,40
	.word	240
	.byte	17,39,0,15
	.byte	'reserved_58',0,40
	.word	21206
	.byte	2,35,88,15
	.byte	'QCTRL0',0,4
	.word	18371
	.byte	3,35,128,1,15
	.byte	'QMR0',0,4
	.word	18637
	.byte	3,35,132,1,15
	.byte	'QSR0',0,4
	.word	18767
	.byte	3,35,136,1,15
	.byte	'Q0R0',0,4
	.word	18109
	.byte	3,35,140,1,14,3,241,12,5,4,15
	.byte	'QBUR0',0,4
	.word	18239
	.byte	2,35,0,15
	.byte	'QINR0',0,4
	.word	18505
	.byte	2,35,0,0,18,4
	.word	21298
	.byte	3,35,144,1,16,12
	.word	240
	.byte	17,11,0,15
	.byte	'reserved_94',0,12
	.word	21345
	.byte	3,35,148,1,15
	.byte	'ASCTRL',0,4
	.word	17051
	.byte	3,35,160,1,15
	.byte	'ASMR',0,4
	.word	17118
	.byte	3,35,164,1,15
	.byte	'ASSEL',0,4
	.word	17249
	.byte	3,35,168,1,15
	.byte	'ASPND',0,4
	.word	17183
	.byte	3,35,172,1,15
	.byte	'reserved_B0',0,16
	.word	20947
	.byte	3,35,176,1,15
	.byte	'QCTRL3',0,4
	.word	18438
	.byte	3,35,192,1,15
	.byte	'QMR3',0,4
	.word	18702
	.byte	3,35,196,1,15
	.byte	'QSR3',0,4
	.word	18832
	.byte	3,35,200,1,15
	.byte	'Q0R3',0,4
	.word	18174
	.byte	3,35,204,1,14,3,129,13,5,4,15
	.byte	'QBUR3',0,4
	.word	18305
	.byte	2,35,0,15
	.byte	'QINR3',0,4
	.word	18571
	.byte	2,35,0,0,18,4
	.word	21524
	.byte	3,35,208,1,15
	.byte	'TRCTR',0,4
	.word	19757
	.byte	3,35,212,1,15
	.byte	'reserved_D8',0,40
	.word	21206
	.byte	3,35,216,1,15
	.byte	'CEFLAG',0,4
	.word	17708
	.byte	3,35,128,2,15
	.byte	'REFLAG',0,4
	.word	19028
	.byte	3,35,132,2,15
	.byte	'SEFLAG',0,4
	.word	19491
	.byte	3,35,136,2,15
	.byte	'reserved_10C',0,4
	.word	21047
	.byte	3,35,140,2,15
	.byte	'CEFCLR',0,4
	.word	17641
	.byte	3,35,144,2,15
	.byte	'REFCLR',0,4
	.word	18961
	.byte	3,35,148,2,15
	.byte	'SEFCLR',0,4
	.word	19424
	.byte	3,35,152,2,15
	.byte	'reserved_11C',0,4
	.word	21047
	.byte	3,35,156,2,15
	.byte	'CEVNP0',0,4
	.word	17775
	.byte	3,35,160,2,15
	.byte	'CEVNP1',0,4
	.word	17842
	.byte	3,35,164,2,15
	.byte	'reserved_128',0,8
	.word	21002
	.byte	3,35,168,2,15
	.byte	'REVNP0',0,4
	.word	19224
	.byte	3,35,176,2,15
	.byte	'REVNP1',0,4
	.word	19291
	.byte	3,35,180,2,15
	.byte	'reserved_138',0,8
	.word	21002
	.byte	3,35,184,2,15
	.byte	'SEVNP',0,4
	.word	19558
	.byte	3,35,192,2,15
	.byte	'reserved_144',0,4
	.word	21047
	.byte	3,35,196,2,15
	.byte	'SRACT',0,4
	.word	19624
	.byte	3,35,200,2,16,36
	.word	240
	.byte	17,35,0,15
	.byte	'reserved_14C',0,36
	.word	21926
	.byte	3,35,204,2,15
	.byte	'EMUXCTR',0,4
	.word	18041
	.byte	3,35,240,2,15
	.byte	'reserved_174',0,4
	.word	21047
	.byte	3,35,244,2,15
	.byte	'VFR',0,4
	.word	19823
	.byte	3,35,248,2,15
	.byte	'reserved_17C',0,4
	.word	21047
	.byte	3,35,252,2,16,48
	.word	17975
	.byte	17,11,0,15
	.byte	'CHCTR',0,48
	.word	22036
	.byte	3,35,128,3,16,80
	.word	240
	.byte	17,79,0,15
	.byte	'reserved_1B0',0,80
	.word	22061
	.byte	3,35,176,3,16,64
	.word	18897
	.byte	17,15,0,15
	.byte	'RCR',0,64
	.word	22093
	.byte	3,35,128,4,16,64
	.word	240
	.byte	17,63,0,15
	.byte	'reserved_240',0,64
	.word	22116
	.byte	3,35,192,4,16,64
	.word	19095
	.byte	17,15,0,15
	.byte	'RES',0,64
	.word	22148
	.byte	3,35,128,5,15
	.byte	'reserved_2C0',0,64
	.word	22116
	.byte	3,35,192,5,16,64
	.word	19159
	.byte	17,15,0,15
	.byte	'RESD',0,64
	.word	22194
	.byte	3,35,128,6,16,192,1
	.word	240
	.byte	17,191,1,0,15
	.byte	'reserved_340',0,192,1
	.word	22218
	.byte	3,35,192,6,0,19
	.word	20867
	.byte	10
	.byte	'Ifx_VADC_G',0,3,167,13,3
	.word	22254
	.byte	12
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,13
	.byte	'SRPN',0,1
	.word	240
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	2,6,2,35,1,13
	.byte	'SRE',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'TOS',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'ECC',0,1
	.word	240
	.byte	5,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	3,0,2,35,2,13
	.byte	'SRR',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'CLRR',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'SETR',0,1
	.word	240
	.byte	1,5,2,35,3,13
	.byte	'IOV',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'IOVCLR',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'SWS',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'SWSCLR',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	22279
	.byte	14,4,70,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22279
	.byte	2,35,0,0,10
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	22595
	.byte	12
	.byte	'_Ifx_SRC_ASCLIN',0,4,86,25,12,15
	.byte	'TX',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'RX',0,4
	.word	22595
	.byte	2,35,4,15
	.byte	'ERR',0,4
	.word	22595
	.byte	2,35,8,0,19
	.word	22655
	.byte	10
	.byte	'Ifx_SRC_ASCLIN',0,4,91,3
	.word	22714
	.byte	12
	.byte	'_Ifx_SRC_BCUSPB',0,4,94,25,4,15
	.byte	'SBSRC',0,4
	.word	22595
	.byte	2,35,0,0,19
	.word	22742
	.byte	10
	.byte	'Ifx_SRC_BCUSPB',0,4,97,3
	.word	22779
	.byte	12
	.byte	'_Ifx_SRC_CAN',0,4,100,25,64,16,64
	.word	22595
	.byte	17,15,0,15
	.byte	'INT',0,64
	.word	22825
	.byte	2,35,0,0,19
	.word	22807
	.byte	10
	.byte	'Ifx_SRC_CAN',0,4,103,3
	.word	22848
	.byte	12
	.byte	'_Ifx_SRC_CAN1',0,4,106,25,32,16,32
	.word	22595
	.byte	17,7,0,15
	.byte	'INT',0,32
	.word	22892
	.byte	2,35,0,0,19
	.word	22873
	.byte	10
	.byte	'Ifx_SRC_CAN1',0,4,109,3
	.word	22915
	.byte	12
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,15
	.byte	'SR0',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	22595
	.byte	2,35,4,15
	.byte	'SR2',0,4
	.word	22595
	.byte	2,35,8,15
	.byte	'SR3',0,4
	.word	22595
	.byte	2,35,12,0,19
	.word	22941
	.byte	10
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	23013
	.byte	12
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,16,8
	.word	22595
	.byte	17,1,0,15
	.byte	'SR',0,8
	.word	23062
	.byte	2,35,0,0,19
	.word	23039
	.byte	10
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	23084
	.byte	12
	.byte	'_Ifx_SRC_CPU',0,4,127,25,32,15
	.byte	'SBSRC',0,4
	.word	22595
	.byte	2,35,0,16,28
	.word	240
	.byte	17,27,0,15
	.byte	'reserved_4',0,28
	.word	23147
	.byte	2,35,4,0,19
	.word	23114
	.byte	10
	.byte	'Ifx_SRC_CPU',0,4,131,1,3
	.word	23177
	.byte	12
	.byte	'_Ifx_SRC_DMA',0,4,134,1,25,80,15
	.byte	'ERR',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'reserved_4',0,12
	.word	21345
	.byte	2,35,4,15
	.byte	'CH',0,64
	.word	22825
	.byte	2,35,16,0,19
	.word	23203
	.byte	10
	.byte	'Ifx_SRC_DMA',0,4,139,1,3
	.word	23268
	.byte	12
	.byte	'_Ifx_SRC_EMEM',0,4,142,1,25,4,15
	.byte	'SR',0,4
	.word	22595
	.byte	2,35,0,0,19
	.word	23294
	.byte	10
	.byte	'Ifx_SRC_EMEM',0,4,145,1,3
	.word	23327
	.byte	12
	.byte	'_Ifx_SRC_ERAY',0,4,148,1,25,80,15
	.byte	'INT',0,8
	.word	23062
	.byte	2,35,0,15
	.byte	'TINT',0,8
	.word	23062
	.byte	2,35,8,15
	.byte	'NDAT',0,8
	.word	23062
	.byte	2,35,16,15
	.byte	'MBSC',0,8
	.word	23062
	.byte	2,35,24,15
	.byte	'OBUSY',0,4
	.word	22595
	.byte	2,35,32,15
	.byte	'IBUSY',0,4
	.word	22595
	.byte	2,35,36,15
	.byte	'reserved_28',0,40
	.word	21206
	.byte	2,35,40,0,19
	.word	23354
	.byte	10
	.byte	'Ifx_SRC_ERAY',0,4,157,1,3
	.word	23481
	.byte	12
	.byte	'_Ifx_SRC_ETH',0,4,160,1,25,4,15
	.byte	'SR',0,4
	.word	22595
	.byte	2,35,0,0,19
	.word	23508
	.byte	10
	.byte	'Ifx_SRC_ETH',0,4,163,1,3
	.word	23540
	.byte	12
	.byte	'_Ifx_SRC_EVR',0,4,166,1,25,8,15
	.byte	'WUT',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'SCDC',0,4
	.word	22595
	.byte	2,35,4,0,19
	.word	23566
	.byte	10
	.byte	'Ifx_SRC_EVR',0,4,170,1,3
	.word	23613
	.byte	12
	.byte	'_Ifx_SRC_FFT',0,4,173,1,25,12,15
	.byte	'DONE',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'ERR',0,4
	.word	22595
	.byte	2,35,4,15
	.byte	'RFS',0,4
	.word	22595
	.byte	2,35,8,0,19
	.word	23639
	.byte	10
	.byte	'Ifx_SRC_FFT',0,4,178,1,3
	.word	23699
	.byte	12
	.byte	'_Ifx_SRC_GPSR',0,4,181,1,25,128,12,15
	.byte	'SR0',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	22595
	.byte	2,35,4,15
	.byte	'SR2',0,4
	.word	22595
	.byte	2,35,8,15
	.byte	'SR3',0,4
	.word	22595
	.byte	2,35,12,16,240,11
	.word	240
	.byte	17,239,11,0,15
	.byte	'reserved_10',0,240,11
	.word	23798
	.byte	2,35,16,0,19
	.word	23725
	.byte	10
	.byte	'Ifx_SRC_GPSR',0,4,188,1,3
	.word	23832
	.byte	12
	.byte	'_Ifx_SRC_GPT12',0,4,191,1,25,48,15
	.byte	'CIRQ',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'T2',0,4
	.word	22595
	.byte	2,35,4,15
	.byte	'T3',0,4
	.word	22595
	.byte	2,35,8,15
	.byte	'T4',0,4
	.word	22595
	.byte	2,35,12,15
	.byte	'T5',0,4
	.word	22595
	.byte	2,35,16,15
	.byte	'T6',0,4
	.word	22595
	.byte	2,35,20,16,24
	.word	240
	.byte	17,23,0,15
	.byte	'reserved_18',0,24
	.word	23954
	.byte	2,35,24,0,19
	.word	23859
	.byte	10
	.byte	'Ifx_SRC_GPT12',0,4,200,1,3
	.word	23985
	.byte	12
	.byte	'_Ifx_SRC_GTM',0,4,203,1,25,192,11,15
	.byte	'AEIIRQ',0,4
	.word	22595
	.byte	2,35,0,16,236,2
	.word	240
	.byte	17,235,2,0,15
	.byte	'reserved_4',0,236,2
	.word	24049
	.byte	2,35,4,15
	.byte	'ERR',0,4
	.word	22595
	.byte	3,35,240,2,15
	.byte	'reserved_174',0,12
	.word	21345
	.byte	3,35,244,2,16,32
	.word	22892
	.byte	17,0,0,15
	.byte	'TIM',0,32
	.word	24118
	.byte	3,35,128,3,16,224,7
	.word	240
	.byte	17,223,7,0,15
	.byte	'reserved_1A0',0,224,7
	.word	24141
	.byte	3,35,160,3,16,64
	.word	22892
	.byte	17,1,0,15
	.byte	'TOM',0,64
	.word	24176
	.byte	3,35,128,11,0,19
	.word	24013
	.byte	10
	.byte	'Ifx_SRC_GTM',0,4,212,1,3
	.word	24200
	.byte	12
	.byte	'_Ifx_SRC_HSM',0,4,215,1,25,8,15
	.byte	'HSM',0,8
	.word	23062
	.byte	2,35,0,0,19
	.word	24226
	.byte	10
	.byte	'Ifx_SRC_HSM',0,4,218,1,3
	.word	24259
	.byte	12
	.byte	'_Ifx_SRC_LMU',0,4,221,1,25,4,15
	.byte	'SR',0,4
	.word	22595
	.byte	2,35,0,0,19
	.word	24285
	.byte	10
	.byte	'Ifx_SRC_LMU',0,4,224,1,3
	.word	24317
	.byte	12
	.byte	'_Ifx_SRC_PMU',0,4,227,1,25,4,15
	.byte	'SR',0,4
	.word	22595
	.byte	2,35,0,0,19
	.word	24343
	.byte	10
	.byte	'Ifx_SRC_PMU',0,4,230,1,3
	.word	24375
	.byte	12
	.byte	'_Ifx_SRC_QSPI',0,4,233,1,25,24,15
	.byte	'TX',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'RX',0,4
	.word	22595
	.byte	2,35,4,15
	.byte	'ERR',0,4
	.word	22595
	.byte	2,35,8,15
	.byte	'PT',0,4
	.word	22595
	.byte	2,35,12,15
	.byte	'HC',0,4
	.word	22595
	.byte	2,35,16,15
	.byte	'U',0,4
	.word	22595
	.byte	2,35,20,0,19
	.word	24401
	.byte	10
	.byte	'Ifx_SRC_QSPI',0,4,241,1,3
	.word	24494
	.byte	12
	.byte	'_Ifx_SRC_SCU',0,4,244,1,25,20,15
	.byte	'DTS',0,4
	.word	22595
	.byte	2,35,0,16,16
	.word	22595
	.byte	17,3,0,15
	.byte	'ERU',0,16
	.word	24553
	.byte	2,35,4,0,19
	.word	24521
	.byte	10
	.byte	'Ifx_SRC_SCU',0,4,248,1,3
	.word	24576
	.byte	12
	.byte	'_Ifx_SRC_SENT',0,4,251,1,25,16,15
	.byte	'SR',0,16
	.word	24553
	.byte	2,35,0,0,19
	.word	24602
	.byte	10
	.byte	'Ifx_SRC_SENT',0,4,254,1,3
	.word	24635
	.byte	12
	.byte	'_Ifx_SRC_SMU',0,4,129,2,25,12,16,12
	.word	22595
	.byte	17,2,0,15
	.byte	'SR',0,12
	.word	24681
	.byte	2,35,0,0,19
	.word	24662
	.byte	10
	.byte	'Ifx_SRC_SMU',0,4,132,2,3
	.word	24703
	.byte	12
	.byte	'_Ifx_SRC_STM',0,4,135,2,25,96,15
	.byte	'SR0',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	22595
	.byte	2,35,4,16,88
	.word	240
	.byte	17,87,0,15
	.byte	'reserved_8',0,88
	.word	24774
	.byte	2,35,8,0,19
	.word	24729
	.byte	10
	.byte	'Ifx_SRC_STM',0,4,140,2,3
	.word	24804
	.byte	12
	.byte	'_Ifx_SRC_VADCCG',0,4,143,2,25,192,2,15
	.byte	'SR0',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	22595
	.byte	2,35,4,15
	.byte	'SR2',0,4
	.word	22595
	.byte	2,35,8,15
	.byte	'SR3',0,4
	.word	22595
	.byte	2,35,12,16,176,2
	.word	240
	.byte	17,175,2,0,15
	.byte	'reserved_10',0,176,2
	.word	24905
	.byte	2,35,16,0,19
	.word	24830
	.byte	10
	.byte	'Ifx_SRC_VADCCG',0,4,150,2,3
	.word	24939
	.byte	12
	.byte	'_Ifx_SRC_VADCG',0,4,153,2,25,16,15
	.byte	'SR0',0,4
	.word	22595
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	22595
	.byte	2,35,4,15
	.byte	'SR2',0,4
	.word	22595
	.byte	2,35,8,15
	.byte	'SR3',0,4
	.word	22595
	.byte	2,35,12,0,19
	.word	24968
	.byte	10
	.byte	'Ifx_SRC_VADCG',0,4,159,2,3
	.word	25042
	.byte	12
	.byte	'_Ifx_SRC_XBAR',0,4,162,2,25,4,15
	.byte	'SRC',0,4
	.word	22595
	.byte	2,35,0,0,19
	.word	25070
	.byte	10
	.byte	'Ifx_SRC_XBAR',0,4,165,2,3
	.word	25104
	.byte	12
	.byte	'_Ifx_SRC_GASCLIN',0,4,178,2,25,24,16,24
	.word	22655
	.byte	17,1,0,19
	.word	25154
	.byte	15
	.byte	'ASCLIN',0,24
	.word	25163
	.byte	2,35,0,0,19
	.word	25131
	.byte	10
	.byte	'Ifx_SRC_GASCLIN',0,4,181,2,3
	.word	25185
	.byte	12
	.byte	'_Ifx_SRC_GBCU',0,4,184,2,25,4,19
	.word	22742
	.byte	15
	.byte	'SPB',0,4
	.word	25235
	.byte	2,35,0,0,19
	.word	25215
	.byte	10
	.byte	'Ifx_SRC_GBCU',0,4,187,2,3
	.word	25254
	.byte	12
	.byte	'_Ifx_SRC_GCAN',0,4,190,2,25,96,16,64
	.word	22807
	.byte	17,0,0,19
	.word	25301
	.byte	15
	.byte	'CAN',0,64
	.word	25310
	.byte	2,35,0,16,32
	.word	22873
	.byte	17,0,0,19
	.word	25328
	.byte	15
	.byte	'CAN1',0,32
	.word	25337
	.byte	2,35,64,0,19
	.word	25281
	.byte	10
	.byte	'Ifx_SRC_GCAN',0,4,194,2,3
	.word	25357
	.byte	12
	.byte	'_Ifx_SRC_GCCU6',0,4,197,2,25,32,16,32
	.word	22941
	.byte	17,1,0,19
	.word	25405
	.byte	15
	.byte	'CCU6',0,32
	.word	25414
	.byte	2,35,0,0,19
	.word	25384
	.byte	10
	.byte	'Ifx_SRC_GCCU6',0,4,200,2,3
	.word	25434
	.byte	12
	.byte	'_Ifx_SRC_GCERBERUS',0,4,203,2,25,8,19
	.word	23039
	.byte	15
	.byte	'CERBERUS',0,8
	.word	25487
	.byte	2,35,0,0,19
	.word	25462
	.byte	10
	.byte	'Ifx_SRC_GCERBERUS',0,4,206,2,3
	.word	25511
	.byte	12
	.byte	'_Ifx_SRC_GCPU',0,4,209,2,25,32,16,32
	.word	23114
	.byte	17,0,0,19
	.word	25563
	.byte	15
	.byte	'CPU',0,32
	.word	25572
	.byte	2,35,0,0,19
	.word	25543
	.byte	10
	.byte	'Ifx_SRC_GCPU',0,4,212,2,3
	.word	25591
	.byte	12
	.byte	'_Ifx_SRC_GDMA',0,4,215,2,25,80,16,80
	.word	23203
	.byte	17,0,0,19
	.word	25638
	.byte	15
	.byte	'DMA',0,80
	.word	25647
	.byte	2,35,0,0,19
	.word	25618
	.byte	10
	.byte	'Ifx_SRC_GDMA',0,4,218,2,3
	.word	25666
	.byte	12
	.byte	'_Ifx_SRC_GEMEM',0,4,221,2,25,4,16,4
	.word	23294
	.byte	17,0,0,19
	.word	25714
	.byte	15
	.byte	'EMEM',0,4
	.word	25723
	.byte	2,35,0,0,19
	.word	25693
	.byte	10
	.byte	'Ifx_SRC_GEMEM',0,4,224,2,3
	.word	25743
	.byte	12
	.byte	'_Ifx_SRC_GERAY',0,4,227,2,25,80,16,80
	.word	23354
	.byte	17,0,0,19
	.word	25792
	.byte	15
	.byte	'ERAY',0,80
	.word	25801
	.byte	2,35,0,0,19
	.word	25771
	.byte	10
	.byte	'Ifx_SRC_GERAY',0,4,230,2,3
	.word	25821
	.byte	12
	.byte	'_Ifx_SRC_GETH',0,4,233,2,25,4,16,4
	.word	23508
	.byte	17,0,0,19
	.word	25869
	.byte	15
	.byte	'ETH',0,4
	.word	25878
	.byte	2,35,0,0,19
	.word	25849
	.byte	10
	.byte	'Ifx_SRC_GETH',0,4,236,2,3
	.word	25897
	.byte	12
	.byte	'_Ifx_SRC_GEVR',0,4,239,2,25,8,16,8
	.word	23566
	.byte	17,0,0,19
	.word	25944
	.byte	15
	.byte	'EVR',0,8
	.word	25953
	.byte	2,35,0,0,19
	.word	25924
	.byte	10
	.byte	'Ifx_SRC_GEVR',0,4,242,2,3
	.word	25972
	.byte	12
	.byte	'_Ifx_SRC_GFFT',0,4,245,2,25,12,16,12
	.word	23639
	.byte	17,0,0,19
	.word	26019
	.byte	15
	.byte	'FFT',0,12
	.word	26028
	.byte	2,35,0,0,19
	.word	25999
	.byte	10
	.byte	'Ifx_SRC_GFFT',0,4,248,2,3
	.word	26047
	.byte	12
	.byte	'_Ifx_SRC_GGPSR',0,4,251,2,25,128,12,16,128,12
	.word	23725
	.byte	17,0,0,19
	.word	26096
	.byte	15
	.byte	'GPSR',0,128,12
	.word	26106
	.byte	2,35,0,0,19
	.word	26074
	.byte	10
	.byte	'Ifx_SRC_GGPSR',0,4,254,2,3
	.word	26127
	.byte	12
	.byte	'_Ifx_SRC_GGPT12',0,4,129,3,25,48,16,48
	.word	23859
	.byte	17,0,0,19
	.word	26177
	.byte	15
	.byte	'GPT12',0,48
	.word	26186
	.byte	2,35,0,0,19
	.word	26155
	.byte	10
	.byte	'Ifx_SRC_GGPT12',0,4,132,3,3
	.word	26207
	.byte	12
	.byte	'_Ifx_SRC_GGTM',0,4,135,3,25,192,11,16,192,11
	.word	24013
	.byte	17,0,0,19
	.word	26257
	.byte	15
	.byte	'GTM',0,192,11
	.word	26267
	.byte	2,35,0,0,19
	.word	26236
	.byte	10
	.byte	'Ifx_SRC_GGTM',0,4,138,3,3
	.word	26287
	.byte	12
	.byte	'_Ifx_SRC_GHSM',0,4,141,3,25,8,16,8
	.word	24226
	.byte	17,0,0,19
	.word	26334
	.byte	15
	.byte	'HSM',0,8
	.word	26343
	.byte	2,35,0,0,19
	.word	26314
	.byte	10
	.byte	'Ifx_SRC_GHSM',0,4,144,3,3
	.word	26362
	.byte	12
	.byte	'_Ifx_SRC_GLMU',0,4,147,3,25,4,16,4
	.word	24285
	.byte	17,0,0,19
	.word	26409
	.byte	15
	.byte	'LMU',0,4
	.word	26418
	.byte	2,35,0,0,19
	.word	26389
	.byte	10
	.byte	'Ifx_SRC_GLMU',0,4,150,3,3
	.word	26437
	.byte	12
	.byte	'_Ifx_SRC_GPMU',0,4,153,3,25,8,16,8
	.word	24343
	.byte	17,1,0,19
	.word	26484
	.byte	15
	.byte	'PMU',0,8
	.word	26493
	.byte	2,35,0,0,19
	.word	26464
	.byte	10
	.byte	'Ifx_SRC_GPMU',0,4,156,3,3
	.word	26512
	.byte	12
	.byte	'_Ifx_SRC_GQSPI',0,4,159,3,25,96,16,96
	.word	24401
	.byte	17,3,0,19
	.word	26560
	.byte	15
	.byte	'QSPI',0,96
	.word	26569
	.byte	2,35,0,0,19
	.word	26539
	.byte	10
	.byte	'Ifx_SRC_GQSPI',0,4,162,3,3
	.word	26589
	.byte	12
	.byte	'_Ifx_SRC_GSCU',0,4,165,3,25,20,19
	.word	24521
	.byte	15
	.byte	'SCU',0,20
	.word	26637
	.byte	2,35,0,0,19
	.word	26617
	.byte	10
	.byte	'Ifx_SRC_GSCU',0,4,168,3,3
	.word	26656
	.byte	12
	.byte	'_Ifx_SRC_GSENT',0,4,171,3,25,16,16,16
	.word	24602
	.byte	17,0,0,19
	.word	26704
	.byte	15
	.byte	'SENT',0,16
	.word	26713
	.byte	2,35,0,0,19
	.word	26683
	.byte	10
	.byte	'Ifx_SRC_GSENT',0,4,174,3,3
	.word	26733
	.byte	12
	.byte	'_Ifx_SRC_GSMU',0,4,177,3,25,12,16,12
	.word	24662
	.byte	17,0,0,19
	.word	26781
	.byte	15
	.byte	'SMU',0,12
	.word	26790
	.byte	2,35,0,0,19
	.word	26761
	.byte	10
	.byte	'Ifx_SRC_GSMU',0,4,180,3,3
	.word	26809
	.byte	12
	.byte	'_Ifx_SRC_GSTM',0,4,183,3,25,96,16,96
	.word	24729
	.byte	17,0,0,19
	.word	26856
	.byte	15
	.byte	'STM',0,96
	.word	26865
	.byte	2,35,0,0,19
	.word	26836
	.byte	10
	.byte	'Ifx_SRC_GSTM',0,4,186,3,3
	.word	26884
	.byte	12
	.byte	'_Ifx_SRC_GVADC',0,4,189,3,25,224,4,16,64
	.word	24968
	.byte	17,3,0,19
	.word	26933
	.byte	15
	.byte	'G',0,64
	.word	26942
	.byte	2,35,0,16,224,1
	.word	240
	.byte	17,223,1,0,15
	.byte	'reserved_40',0,224,1
	.word	26958
	.byte	2,35,64,16,192,2
	.word	24830
	.byte	17,0,0,19
	.word	26991
	.byte	15
	.byte	'CG',0,192,2
	.word	27001
	.byte	3,35,160,2,0,19
	.word	26911
	.byte	10
	.byte	'Ifx_SRC_GVADC',0,4,194,3,3
	.word	27021
	.byte	12
	.byte	'_Ifx_SRC_GXBAR',0,4,197,3,25,4,19
	.word	25070
	.byte	15
	.byte	'XBAR',0,4
	.word	27070
	.byte	2,35,0,0,19
	.word	27049
	.byte	10
	.byte	'Ifx_SRC_GXBAR',0,4,200,3,3
	.word	27090
	.byte	12
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,5,45,16,4,13
	.byte	'EN0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	240
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	240
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	240
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_ACCEN0_Bits',0,5,79,3
	.word	27118
	.byte	12
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,5,82,16,4,13
	.byte	'reserved_0',0,4
	.word	1985
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_SCU_ACCEN1_Bits',0,5,85,3
	.word	27675
	.byte	12
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,5,88,16,4,13
	.byte	'STM0DIS',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'STM1DIS',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'STM2DIS',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,4
	.word	1985
	.byte	29,0,2,35,2,0,10
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,5,94,3
	.word	27752
	.byte	12
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,5,97,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'BAUD2DIV',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'SRIDIV',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'LPDIV',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'SPBDIV',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'FSI2DIV',0,1
	.word	240
	.byte	2,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	240
	.byte	2,0,2,35,2,13
	.byte	'FSIDIV',0,1
	.word	240
	.byte	2,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	2,4,2,35,3,13
	.byte	'CLKSEL',0,1
	.word	240
	.byte	2,2,2,35,3,13
	.byte	'UP',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON0_Bits',0,5,111,3
	.word	27888
	.byte	12
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,5,114,16,4,13
	.byte	'CANDIV',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'ERAYDIV',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'STMDIV',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'GTMDIV',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'ETHDIV',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'ASCLINFDIV',0,1
	.word	240
	.byte	4,0,2,35,2,13
	.byte	'ASCLINSDIV',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'INSEL',0,1
	.word	240
	.byte	2,2,2,35,3,13
	.byte	'UP',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON1_Bits',0,5,126,3
	.word	28170
	.byte	12
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,5,129,1,16,4,13
	.byte	'BBBDIV',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	26,2,2,35,2,13
	.byte	'UP',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON2_Bits',0,5,135,1,3
	.word	28408
	.byte	12
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,5,138,1,16,4,13
	.byte	'PLLDIV',0,1
	.word	240
	.byte	6,2,2,35,0,13
	.byte	'PLLSEL',0,1
	.word	240
	.byte	2,0,2,35,0,13
	.byte	'PLLERAYDIV',0,1
	.word	240
	.byte	6,2,2,35,1,13
	.byte	'PLLERAYSEL',0,1
	.word	240
	.byte	2,0,2,35,1,13
	.byte	'SRIDIV',0,1
	.word	240
	.byte	6,2,2,35,2,13
	.byte	'SRISEL',0,1
	.word	240
	.byte	2,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	6,2,2,35,3,13
	.byte	'UP',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON3_Bits',0,5,149,1,3
	.word	28536
	.byte	12
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,5,152,1,16,4,13
	.byte	'SPBDIV',0,1
	.word	240
	.byte	6,2,2,35,0,13
	.byte	'SPBSEL',0,1
	.word	240
	.byte	2,0,2,35,0,13
	.byte	'GTMDIV',0,1
	.word	240
	.byte	6,2,2,35,1,13
	.byte	'GTMSEL',0,1
	.word	240
	.byte	2,0,2,35,1,13
	.byte	'STMDIV',0,1
	.word	240
	.byte	6,2,2,35,2,13
	.byte	'STMSEL',0,1
	.word	240
	.byte	2,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	6,2,2,35,3,13
	.byte	'UP',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON4_Bits',0,5,163,1,3
	.word	28763
	.byte	12
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,5,166,1,16,4,13
	.byte	'MAXDIV',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	26,2,2,35,2,13
	.byte	'UP',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON5_Bits',0,5,172,1,3
	.word	28982
	.byte	12
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,5,175,1,16,4,13
	.byte	'CPU0DIV',0,1
	.word	240
	.byte	6,2,2,35,0,13
	.byte	'reserved_6',0,4
	.word	1985
	.byte	26,0,2,35,2,0,10
	.byte	'Ifx_SCU_CCUCON6_Bits',0,5,179,1,3
	.word	29110
	.byte	12
	.byte	'_Ifx_SCU_CHIPID_Bits',0,5,182,1,16,4,13
	.byte	'CHREV',0,1
	.word	240
	.byte	6,2,2,35,0,13
	.byte	'CHTEC',0,1
	.word	240
	.byte	2,0,2,35,0,13
	.byte	'CHID',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'EEA',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'UCODE',0,1
	.word	240
	.byte	7,0,2,35,2,13
	.byte	'FSIZE',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'SP',0,1
	.word	240
	.byte	2,2,2,35,3,13
	.byte	'SEC',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CHIPID_Bits',0,5,193,1,3
	.word	29210
	.byte	12
	.byte	'_Ifx_SCU_DTSCON_Bits',0,5,196,1,16,4,13
	.byte	'PWD',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'START',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'CAL',0,4
	.word	1985
	.byte	22,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	5,1,2,35,3,13
	.byte	'SLCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_DTSCON_Bits',0,5,204,1,3
	.word	29418
	.byte	12
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,5,207,1,16,4,13
	.byte	'LOWER',0,2
	.word	184
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	240
	.byte	5,1,2,35,1,13
	.byte	'LLU',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'UPPER',0,2
	.word	184
	.byte	10,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	4,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'UOF',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_DTSLIM_Bits',0,5,216,1,3
	.word	29583
	.byte	12
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,5,219,1,16,4,13
	.byte	'RESULT',0,2
	.word	184
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	240
	.byte	4,2,2,35,1,13
	.byte	'RDY',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'BUSY',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,5,226,1,3
	.word	29766
	.byte	12
	.byte	'_Ifx_SCU_EICR_Bits',0,5,229,1,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'EXIS0',0,1
	.word	240
	.byte	3,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'FEN0',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'REN0',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'LDEN0',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'EIEN0',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'INP0',0,1
	.word	240
	.byte	3,1,2,35,1,13
	.byte	'reserved_15',0,4
	.word	1985
	.byte	5,12,2,35,2,13
	.byte	'EXIS1',0,1
	.word	240
	.byte	3,1,2,35,2,13
	.byte	'reserved_23',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'FEN1',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'REN1',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'LDEN1',0,1
	.word	240
	.byte	1,5,2,35,3,13
	.byte	'EIEN1',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'INP1',0,1
	.word	240
	.byte	3,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EICR_Bits',0,5,248,1,3
	.word	29920
	.byte	12
	.byte	'_Ifx_SCU_EIFR_Bits',0,5,251,1,16,4,13
	.byte	'INTF0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'INTF1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'INTF2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'INTF3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'INTF4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'INTF5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'INTF6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'INTF7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	1985
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_SCU_EIFR_Bits',0,5,134,2,3
	.word	30284
	.byte	12
	.byte	'_Ifx_SCU_EMSR_Bits',0,5,137,2,16,4,13
	.byte	'POL',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'MODE',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'ENON',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'PSEL',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,2
	.word	184
	.byte	12,0,2,35,0,13
	.byte	'EMSF',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'SEMSF',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	240
	.byte	6,0,2,35,2,13
	.byte	'EMSFM',0,1
	.word	240
	.byte	2,6,2,35,3,13
	.byte	'SEMSFM',0,1
	.word	240
	.byte	2,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_SCU_EMSR_Bits',0,5,150,2,3
	.word	30495
	.byte	12
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,5,153,2,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	7,1,2,35,0,13
	.byte	'EDCON',0,2
	.word	184
	.byte	2,7,2,35,0,13
	.byte	'reserved_9',0,4
	.word	1985
	.byte	23,0,2,35,2,0,10
	.byte	'Ifx_SCU_ESRCFG_Bits',0,5,158,2,3
	.word	30747
	.byte	12
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,5,161,2,16,4,13
	.byte	'ARI',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ARC',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	1985
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_ESROCFG_Bits',0,5,166,2,3
	.word	30865
	.byte	12
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,5,169,2,16,4,13
	.byte	'reserved_0',0,4
	.word	1985
	.byte	28,4,2,35,2,13
	.byte	'EVR13OFF',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'BPEVR13OFF',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVR13CON_Bits',0,5,176,2,3
	.word	30976
	.byte	12
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,5,179,2,16,4,13
	.byte	'ADC13V',0,1
	.word	240
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'ADCSWDV',0,1
	.word	240
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	7,1,2,35,3,13
	.byte	'VAL',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,5,186,2,3
	.word	31139
	.byte	12
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,5,189,2,16,4,13
	.byte	'EVR13OVMOD',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'EVR13UVMOD',0,1
	.word	240
	.byte	2,2,2,35,0,13
	.byte	'reserved_6',0,2
	.word	184
	.byte	10,0,2,35,0,13
	.byte	'SWDOVMOD',0,1
	.word	240
	.byte	2,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	240
	.byte	2,4,2,35,2,13
	.byte	'SWDUVMOD',0,1
	.word	240
	.byte	2,2,2,35,2,13
	.byte	'reserved_22',0,2
	.word	184
	.byte	8,2,2,35,2,13
	.byte	'SLCK',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,5,201,2,3
	.word	31301
	.byte	12
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,5,204,2,16,4,13
	.byte	'EVR13OVVAL',0,1
	.word	240
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'SWDOVVAL',0,1
	.word	240
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	6,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVROVMON_Bits',0,5,212,2,3
	.word	31579
	.byte	12
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,5,215,2,16,4,13
	.byte	'reserved_0',0,4
	.word	1985
	.byte	28,4,2,35,2,13
	.byte	'RSTSWDOFF',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'BPRSTSWDOFF',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,5,222,2,3
	.word	31758
	.byte	12
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,5,225,2,16,4,13
	.byte	'SD33P',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'SD33I',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'reserved_12',0,4
	.word	1985
	.byte	19,1,2,35,2,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,5,232,2,3
	.word	31918
	.byte	12
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,5,235,2,16,4,13
	.byte	'SDFREQSPRD',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'TON',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'TOFF',0,1
	.word	240
	.byte	8,0,2,35,2,13
	.byte	'SDSTEP',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'SYNCDIV',0,1
	.word	240
	.byte	3,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,5,244,2,3
	.word	32079
	.byte	12
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,5,247,2,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	8,0,2,35,0,13
	.byte	'STBS',0,1
	.word	240
	.byte	2,6,2,35,1,13
	.byte	'STSP',0,1
	.word	240
	.byte	2,4,2,35,1,13
	.byte	'NS',0,1
	.word	240
	.byte	2,2,2,35,1,13
	.byte	'OL',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'PIAD',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'ADCMODE',0,1
	.word	240
	.byte	4,4,2,35,2,13
	.byte	'ADCLPF',0,1
	.word	240
	.byte	2,2,2,35,2,13
	.byte	'ADCLSB',0,1
	.word	240
	.byte	1,1,2,35,2,13
	.byte	'reserved_23',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'SDLUT',0,1
	.word	240
	.byte	6,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,5,134,3,3
	.word	32271
	.byte	12
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,5,137,3,16,4,13
	.byte	'SDOLCON',0,1
	.word	240
	.byte	7,1,2,35,0,13
	.byte	'MODSEL',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'MODLOW',0,1
	.word	240
	.byte	7,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'SDVOKLVL',0,1
	.word	240
	.byte	6,2,2,35,2,13
	.byte	'MODMAN',0,1
	.word	240
	.byte	2,0,2,35,2,13
	.byte	'MODHIGH',0,1
	.word	240
	.byte	7,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,5,147,3,3
	.word	32567
	.byte	12
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,5,150,3,16,4,13
	.byte	'EVR13',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'OV13',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'OVSWD',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'UV13',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'UVSWD',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	2,6,2,35,1,13
	.byte	'BGPROK',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'reserved_11',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'SCMOD',0,1
	.word	240
	.byte	2,2,2,35,1,13
	.byte	'reserved_14',0,4
	.word	1985
	.byte	18,0,2,35,2,0,10
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,5,164,3,3
	.word	32782
	.byte	12
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,5,167,3,16,4,13
	.byte	'EVR13UVVAL',0,1
	.word	240
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'SWDUVVAL',0,1
	.word	240
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	6,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,5,175,3,3
	.word	33071
	.byte	12
	.byte	'_Ifx_SCU_EXTCON_Bits',0,5,178,3,16,4,13
	.byte	'EN0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'SEL0',0,1
	.word	240
	.byte	4,2,2,35,0,13
	.byte	'reserved_6',0,2
	.word	184
	.byte	10,0,2,35,0,13
	.byte	'EN1',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'NSEL',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'SEL1',0,1
	.word	240
	.byte	4,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	240
	.byte	2,0,2,35,2,13
	.byte	'DIV1',0,1
	.word	240
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_SCU_EXTCON_Bits',0,5,189,3,3
	.word	33250
	.byte	12
	.byte	'_Ifx_SCU_FDR_Bits',0,5,192,3,16,4,13
	.byte	'STEP',0,2
	.word	184
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	240
	.byte	4,2,2,35,1,13
	.byte	'DM',0,1
	.word	240
	.byte	2,0,2,35,1,13
	.byte	'RESULT',0,2
	.word	184
	.byte	10,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	5,1,2,35,3,13
	.byte	'DISCLK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_FDR_Bits',0,5,200,3,3
	.word	33468
	.byte	12
	.byte	'_Ifx_SCU_FMR_Bits',0,5,203,3,16,4,13
	.byte	'FS0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'FS1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'FS2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'FS3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'FS4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'FS5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'FS6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'FS7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'FC0',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'FC1',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'FC2',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'FC3',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'FC4',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'FC5',0,1
	.word	240
	.byte	1,2,2,35,2,13
	.byte	'FC6',0,1
	.word	240
	.byte	1,1,2,35,2,13
	.byte	'FC7',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_SCU_FMR_Bits',0,5,223,3,3
	.word	33631
	.byte	12
	.byte	'_Ifx_SCU_ID_Bits',0,5,226,3,16,4,13
	.byte	'MODREV',0,1
	.word	240
	.byte	8,0,2,35,0,13
	.byte	'MODTYPE',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'MODNUMBER',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_ID_Bits',0,5,231,3,3
	.word	33967
	.byte	12
	.byte	'_Ifx_SCU_IGCR_Bits',0,5,234,3,16,4,13
	.byte	'IPEN00',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'IPEN01',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'IPEN02',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'IPEN03',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'IPEN04',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'IPEN05',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'IPEN06',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'IPEN07',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	5,3,2,35,1,13
	.byte	'GEEN0',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'IGP0',0,1
	.word	240
	.byte	2,0,2,35,1,13
	.byte	'IPEN10',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'IPEN11',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'IPEN12',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'IPEN13',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'IPEN14',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'IPEN15',0,1
	.word	240
	.byte	1,2,2,35,2,13
	.byte	'IPEN16',0,1
	.word	240
	.byte	1,1,2,35,2,13
	.byte	'IPEN17',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	5,3,2,35,3,13
	.byte	'GEEN1',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'IGP1',0,1
	.word	240
	.byte	2,0,2,35,3,0,10
	.byte	'Ifx_SCU_IGCR_Bits',0,5,130,4,3
	.word	34074
	.byte	12
	.byte	'_Ifx_SCU_IN_Bits',0,5,133,4,16,4,13
	.byte	'P0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	1985
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_IN_Bits',0,5,138,4,3
	.word	34526
	.byte	12
	.byte	'_Ifx_SCU_IOCR_Bits',0,5,141,4,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	4,4,2,35,0,13
	.byte	'PC0',0,1
	.word	240
	.byte	4,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'PC1',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_IOCR_Bits',0,5,148,4,3
	.word	34625
	.byte	12
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,5,151,4,16,4,13
	.byte	'LBISTREQ',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'LBISTREQP',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'PATTERNS',0,2
	.word	184
	.byte	14,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,5,157,4,3
	.word	34775
	.byte	12
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,5,160,4,16,4,13
	.byte	'SEED',0,4
	.word	1985
	.byte	23,9,2,35,2,13
	.byte	'reserved_23',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'SPLITSH',0,1
	.word	240
	.byte	3,5,2,35,3,13
	.byte	'BODY',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'LBISTFREQU',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,5,167,4,3
	.word	34924
	.byte	12
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,5,170,4,16,4,13
	.byte	'SIGNATURE',0,4
	.word	1985
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	7,1,2,35,3,13
	.byte	'LBISTDONE',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,5,175,4,3
	.word	35085
	.byte	12
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,5,178,4,16,4,13
	.byte	'reserved_0',0,2
	.word	184
	.byte	16,0,2,35,0,13
	.byte	'LS',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,2
	.word	184
	.byte	14,1,2,35,2,13
	.byte	'LSEN',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_LCLCON0_Bits',0,5,184,4,3
	.word	35215
	.byte	12
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,5,187,4,16,4,13
	.byte	'LCLT0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'LCLT1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	1985
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_LCLTEST_Bits',0,5,192,4,3
	.word	35349
	.byte	12
	.byte	'_Ifx_SCU_MANID_Bits',0,5,195,4,16,4,13
	.byte	'DEPT',0,1
	.word	240
	.byte	5,3,2,35,0,13
	.byte	'MANUF',0,2
	.word	184
	.byte	11,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_MANID_Bits',0,5,200,4,3
	.word	35464
	.byte	12
	.byte	'_Ifx_SCU_OMR_Bits',0,5,203,4,16,4,13
	.byte	'PS0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'PS1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,2
	.word	184
	.byte	14,0,2,35,0,13
	.byte	'PCL0',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'PCL1',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,2
	.word	184
	.byte	14,0,2,35,2,0,10
	.byte	'Ifx_SCU_OMR_Bits',0,5,211,4,3
	.word	35575
	.byte	12
	.byte	'_Ifx_SCU_OSCCON_Bits',0,5,214,4,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'PLLLV',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'OSCRES',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'GAINSEL',0,1
	.word	240
	.byte	2,3,2,35,0,13
	.byte	'MODE',0,1
	.word	240
	.byte	2,1,2,35,0,13
	.byte	'SHBY',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'PLLHV',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'X1D',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'X1DEN',0,1
	.word	240
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'OSCVAL',0,1
	.word	240
	.byte	5,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	2,1,2,35,2,13
	.byte	'APREN',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_SCU_OSCCON_Bits',0,5,231,4,3
	.word	35733
	.byte	12
	.byte	'_Ifx_SCU_OUT_Bits',0,5,234,4,16,4,13
	.byte	'P0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	1985
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_OUT_Bits',0,5,239,4,3
	.word	36073
	.byte	12
	.byte	'_Ifx_SCU_OVCCON_Bits',0,5,242,4,16,4,13
	.byte	'CSEL0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'CSEL1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'CSEL2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,2
	.word	184
	.byte	13,0,2,35,0,13
	.byte	'OVSTRT',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'OVSTP',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'DCINVAL',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	240
	.byte	5,0,2,35,2,13
	.byte	'OVCONF',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'POVCONF',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	6,0,2,35,3,0,10
	.byte	'Ifx_SCU_OVCCON_Bits',0,5,255,4,3
	.word	36174
	.byte	12
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,5,130,5,16,4,13
	.byte	'OVEN0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'OVEN1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'OVEN2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,4
	.word	1985
	.byte	29,0,2,35,2,0,10
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,5,136,5,3
	.word	36441
	.byte	12
	.byte	'_Ifx_SCU_PDISC_Bits',0,5,139,5,16,4,13
	.byte	'PDIS0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'PDIS1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	1985
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_PDISC_Bits',0,5,144,5,3
	.word	36577
	.byte	12
	.byte	'_Ifx_SCU_PDR_Bits',0,5,147,5,16,4,13
	.byte	'PD0',0,1
	.word	240
	.byte	3,5,2,35,0,13
	.byte	'PL0',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'PD1',0,1
	.word	240
	.byte	3,1,2,35,0,13
	.byte	'PL1',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	1985
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_SCU_PDR_Bits',0,5,154,5,3
	.word	36688
	.byte	12
	.byte	'_Ifx_SCU_PDRR_Bits',0,5,157,5,16,4,13
	.byte	'PDR0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'PDR1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'PDR2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'PDR3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'PDR4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'PDR5',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'PDR6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'PDR7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	1985
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_SCU_PDRR_Bits',0,5,168,5,3
	.word	36821
	.byte	12
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,5,171,5,16,4,13
	.byte	'VCOBYP',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'VCOPWD',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'MODEN',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'SETFINDIS',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'CLRFINDIS',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'OSCDISCDIS',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'reserved_7',0,2
	.word	184
	.byte	2,7,2,35,0,13
	.byte	'NDIV',0,1
	.word	240
	.byte	7,0,2,35,1,13
	.byte	'PLLPWD',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'RESLD',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	240
	.byte	5,0,2,35,2,13
	.byte	'PDIV',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_SCU_PLLCON0_Bits',0,5,188,5,3
	.word	37024
	.byte	12
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,5,191,5,16,4,13
	.byte	'K2DIV',0,1
	.word	240
	.byte	7,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'K3DIV',0,1
	.word	240
	.byte	7,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'K1DIV',0,1
	.word	240
	.byte	7,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	184
	.byte	9,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLCON1_Bits',0,5,199,5,3
	.word	37380
	.byte	12
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,5,202,5,16,4,13
	.byte	'MODCFG',0,2
	.word	184
	.byte	16,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLCON2_Bits',0,5,206,5,3
	.word	37558
	.byte	12
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,5,209,5,16,4,13
	.byte	'VCOBYP',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'VCOPWD',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'SETFINDIS',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'CLRFINDIS',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'OSCDISCDIS',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'reserved_7',0,2
	.word	184
	.byte	2,7,2,35,0,13
	.byte	'NDIV',0,1
	.word	240
	.byte	5,2,2,35,1,13
	.byte	'reserved_14',0,1
	.word	240
	.byte	2,0,2,35,1,13
	.byte	'PLLPWD',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'RESLD',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	240
	.byte	5,0,2,35,2,13
	.byte	'PDIV',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	240
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,5,226,5,3
	.word	37658
	.byte	12
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,5,229,5,16,4,13
	.byte	'K2DIV',0,1
	.word	240
	.byte	7,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'K3DIV',0,1
	.word	240
	.byte	4,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	240
	.byte	4,0,2,35,1,13
	.byte	'K1DIV',0,1
	.word	240
	.byte	7,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	184
	.byte	9,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,5,237,5,3
	.word	38028
	.byte	12
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,5,240,5,16,4,13
	.byte	'VCOBYST',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'PWDSTAT',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'VCOLOCK',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'FINDIS',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'K1RDY',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'K2RDY',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,4
	.word	1985
	.byte	26,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,5,249,5,3
	.word	38214
	.byte	12
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,5,252,5,16,4,13
	.byte	'VCOBYST',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'VCOLOCK',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'FINDIS',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'K1RDY',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'K2RDY',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'MODRUN',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	1985
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,5,135,6,3
	.word	38412
	.byte	12
	.byte	'_Ifx_SCU_PMCSR_Bits',0,5,138,6,16,4,13
	.byte	'REQSLP',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'SMUSLP',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	240
	.byte	5,0,2,35,0,13
	.byte	'PMST',0,1
	.word	240
	.byte	3,5,2,35,1,13
	.byte	'reserved_11',0,4
	.word	1985
	.byte	21,0,2,35,2,0,10
	.byte	'Ifx_SCU_PMCSR_Bits',0,5,145,6,3
	.word	38645
	.byte	12
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,5,148,6,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ESR1WKEN',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'PINAWKEN',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'PINBWKEN',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'ESR0DFEN',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'ESR0EDCON',0,1
	.word	240
	.byte	2,1,2,35,0,13
	.byte	'ESR1DFEN',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'ESR1EDCON',0,1
	.word	240
	.byte	2,6,2,35,1,13
	.byte	'PINADFEN',0,1
	.word	240
	.byte	1,5,2,35,1,13
	.byte	'PINAEDCON',0,1
	.word	240
	.byte	2,3,2,35,1,13
	.byte	'PINBDFEN',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'PINBEDCON',0,1
	.word	240
	.byte	2,0,2,35,1,13
	.byte	'reserved_16',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'STBYRAMSEL',0,1
	.word	240
	.byte	2,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'WUTWKEN',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	2,1,2,35,2,13
	.byte	'PORSTDF',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'DCDCSYNC',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	3,3,2,35,3,13
	.byte	'ESR0TRIST',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,5,174,6,3
	.word	38797
	.byte	12
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,5,177,6,16,4,13
	.byte	'reserved_0',0,2
	.word	184
	.byte	12,4,2,35,0,13
	.byte	'IRADIS',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'reserved_13',0,4
	.word	1985
	.byte	14,5,2,35,2,13
	.byte	'STBYEVEN',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'STBYEV',0,1
	.word	240
	.byte	3,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,5,185,6,3
	.word	39356
	.byte	12
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,5,188,6,16,4,13
	.byte	'WUTREL',0,4
	.word	1985
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	4,4,2,35,3,13
	.byte	'WUTDIV',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'WUTEN',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'WUTMODE',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,5,196,6,3
	.word	39539
	.byte	12
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,5,199,6,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'ESR1WKP',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'ESR1OVRUN',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'PINAWKP',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'PINAOVRUN',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'PINBWKP',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'PINBOVRUN',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'PORSTDF',0,1
	.word	240
	.byte	1,6,2,35,1,13
	.byte	'HWCFGEVR',0,1
	.word	240
	.byte	3,3,2,35,1,13
	.byte	'STBYRAM',0,1
	.word	240
	.byte	2,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'WUTWKP',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'WUTOVRUN',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'WUTWKEN',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'ESR1WKEN',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'PINAWKEN',0,1
	.word	240
	.byte	1,2,2,35,2,13
	.byte	'PINBWKEN',0,1
	.word	240
	.byte	1,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	184
	.byte	4,5,2,35,2,13
	.byte	'ESR0TRIST',0,1
	.word	240
	.byte	1,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'WUTEN',0,1
	.word	240
	.byte	1,2,2,35,3,13
	.byte	'WUTMODE',0,1
	.word	240
	.byte	1,1,2,35,3,13
	.byte	'WUTRUN',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,5,226,6,3
	.word	39708
	.byte	12
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,5,229,6,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'ESR1WKPCLR',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'ESR1OVRUNCLR',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'PINAWKPCLR',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'PINAOVRUNCLR',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'PINBWKPCLR',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'PINBOVRUNCLR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'WUTWKPCLR',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'WUTOVRUNCLR',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,2
	.word	184
	.byte	14,0,2,35,2,0,10
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,5,242,6,3
	.word	40275
	.byte	12
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,5,245,6,16,4,13
	.byte	'WUTCNT',0,4
	.word	1985
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	240
	.byte	7,1,2,35,3,13
	.byte	'VAL',0,1
	.word	240
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,5,250,6,3
	.word	40591
	.byte	12
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,5,253,6,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'CLRC',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,2
	.word	184
	.byte	10,4,2,35,0,13
	.byte	'CSS0',0,1
	.word	240
	.byte	1,3,2,35,1,13
	.byte	'CSS1',0,1
	.word	240
	.byte	1,2,2,35,1,13
	.byte	'CSS2',0,1
	.word	240
	.byte	1,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'USRINFO',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_RSTCON2_Bits',0,5,135,7,3
	.word	40710
	.byte	12
	.byte	'_Ifx_SCU_RSTCON_Bits',0,5,138,7,16,4,13
	.byte	'ESR0',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'ESR1',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	2,2,2,35,0,13
	.byte	'SMU',0,1
	.word	240
	.byte	2,0,2,35,0,13
	.byte	'SW',0,1
	.word	240
	.byte	2,6,2,35,1,13
	.byte	'STM0',0,1
	.word	240
	.byte	2,4,2,35,1,13
	.byte	'STM1',0,1
	.word	240
	.byte	2,2,2,35,1,13
	.byte	'STM2',0,1
	.word	240
	.byte	2,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_RSTCON_Bits',0,5,149,7,3
	.word	40919
	.byte	12
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,5,152,7,16,4,13
	.byte	'ESR0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ESR1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'SMU',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'SW',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'STM0',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'STM1',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'STM2',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	240
	.byte	8,0,2,35,1,13
	.byte	'PORST',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'CB0',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'CB1',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'CB3',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	2,1,2,35,2,13
	.byte	'EVR13',0,1
	.word	240
	.byte	1,0,2,35,2,13
	.byte	'EVR33',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'SWD',0,1
	.word	240
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	240
	.byte	2,4,2,35,3,13
	.byte	'STBYR',0,1
	.word	240
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	240
	.byte	3,0,2,35,3,0,10
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,5,175,7,3
	.word	41130
	.byte	12
	.byte	'_Ifx_SCU_SAFECON_Bits',0,5,178,7,16,4,13
	.byte	'HBT',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	1985
	.byte	31,0,2,35,2,0,10
	.byte	'Ifx_SCU_SAFECON_Bits',0,5,182,7,3
	.word	41562
	.byte	12
	.byte	'_Ifx_SCU_STSTAT_Bits',0,5,185,7,16,4,13
	.byte	'HWCFG',0,1
	.word	240
	.byte	8,0,2,35,0,13
	.byte	'FTM',0,1
	.word	240
	.byte	7,1,2,35,1,13
	.byte	'MODE',0,1
	.word	240
	.byte	1,0,2,35,1,13
	.byte	'FCBAE',0,1
	.word	240
	.byte	1,7,2,35,2,13
	.byte	'LUDIS',0,1
	.word	240
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	240
	.byte	1,5,2,35,2,13
	.byte	'TRSTL',0,1
	.word	240
	.byte	1,4,2,35,2,13
	.byte	'SPDEN',0,1
	.word	240
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	240
	.byte	3,0,2,35,2,13
	.byte	'RAMINT',0,1
	.word	240
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	240
	.byte	7,0,2,35,3,0,10
	.byte	'Ifx_SCU_STSTAT_Bits',0,5,198,7,3
	.word	41658
	.byte	12
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,5,201,7,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'SWRSTREQ',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	1985
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,5,206,7,3
	.word	41918
	.byte	12
	.byte	'_Ifx_SCU_SYSCON_Bits',0,5,209,7,16,4,13
	.byte	'CCTRIG0',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'RAMINTM',0,1
	.word	240
	.byte	2,4,2,35,0,13
	.byte	'SETLUDIS',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'reserved_5',0,1
	.word	240
	.byte	3,0,2,35,0,13
	.byte	'DATM',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,4
	.word	1985
	.byte	23,0,2,35,2,0,10
	.byte	'Ifx_SCU_SYSCON_Bits',0,5,218,7,3
	.word	42043
	.byte	12
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,5,221,7,16,4,13
	.byte	'ESR0T',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,5,228,7,3
	.word	42240
	.byte	12
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,5,231,7,16,4,13
	.byte	'ESR0T',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,5,238,7,3
	.word	42393
	.byte	12
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,5,241,7,16,4,13
	.byte	'ESR0T',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_SCU_TRAPSET_Bits',0,5,248,7,3
	.word	42546
	.byte	12
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,5,251,7,16,4,13
	.byte	'ESR0T',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	1985
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,5,130,8,3
	.word	42699
	.byte	12
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,5,133,8,16,4,2
	.byte	'unsigned int',0,4,7,13
	.byte	'ENDINIT',0,4
	.word	42886
	.byte	1,31,2,35,0,13
	.byte	'LCK',0,4
	.word	42886
	.byte	1,30,2,35,0,13
	.byte	'PW',0,4
	.word	42886
	.byte	14,16,2,35,0,13
	.byte	'REL',0,4
	.word	42886
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,5,139,8,3
	.word	42854
	.byte	12
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,5,142,8,16,4,13
	.byte	'reserved_0',0,1
	.word	240
	.byte	2,6,2,35,0,13
	.byte	'IR0',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'DR',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'IR1',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'UR',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'PAR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'TCR',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'TCTR',0,1
	.word	240
	.byte	7,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,5,154,8,3
	.word	43000
	.byte	12
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,5,157,8,16,4,13
	.byte	'AE',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'OE',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'IS0',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'DS',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'TO',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'IS1',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'US',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'PAS',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'TCS',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'TCT',0,1
	.word	240
	.byte	7,0,2,35,1,13
	.byte	'TIM',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,5,170,8,3
	.word	43238
	.byte	12
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,5,173,8,16,4,13
	.byte	'ENDINIT',0,4
	.word	42886
	.byte	1,31,2,35,0,13
	.byte	'LCK',0,4
	.word	42886
	.byte	1,30,2,35,0,13
	.byte	'PW',0,4
	.word	42886
	.byte	14,16,2,35,0,13
	.byte	'REL',0,4
	.word	42886
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,5,179,8,3
	.word	43461
	.byte	12
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,5,182,8,16,4,13
	.byte	'CLRIRF',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'IR0',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'DR',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'IR1',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'UR',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'PAR',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'TCR',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'TCTR',0,1
	.word	240
	.byte	7,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,5,195,8,3
	.word	43587
	.byte	12
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,5,198,8,16,4,13
	.byte	'AE',0,1
	.word	240
	.byte	1,7,2,35,0,13
	.byte	'OE',0,1
	.word	240
	.byte	1,6,2,35,0,13
	.byte	'IS0',0,1
	.word	240
	.byte	1,5,2,35,0,13
	.byte	'DS',0,1
	.word	240
	.byte	1,4,2,35,0,13
	.byte	'TO',0,1
	.word	240
	.byte	1,3,2,35,0,13
	.byte	'IS1',0,1
	.word	240
	.byte	1,2,2,35,0,13
	.byte	'US',0,1
	.word	240
	.byte	1,1,2,35,0,13
	.byte	'PAS',0,1
	.word	240
	.byte	1,0,2,35,0,13
	.byte	'TCS',0,1
	.word	240
	.byte	1,7,2,35,1,13
	.byte	'TCT',0,1
	.word	240
	.byte	7,0,2,35,1,13
	.byte	'TIM',0,2
	.word	184
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,5,211,8,3
	.word	43839
	.byte	14,5,219,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	27118
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ACCEN0',0,5,224,8,3
	.word	44058
	.byte	14,5,227,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	27675
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ACCEN1',0,5,232,8,3
	.word	44122
	.byte	14,5,235,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	27752
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ARSTDIS',0,5,240,8,3
	.word	44186
	.byte	14,5,243,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	27888
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON0',0,5,248,8,3
	.word	44251
	.byte	14,5,251,8,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28170
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON1',0,5,128,9,3
	.word	44316
	.byte	14,5,131,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28408
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON2',0,5,136,9,3
	.word	44381
	.byte	14,5,139,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28536
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON3',0,5,144,9,3
	.word	44446
	.byte	14,5,147,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28763
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON4',0,5,152,9,3
	.word	44511
	.byte	14,5,155,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28982
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON5',0,5,160,9,3
	.word	44576
	.byte	14,5,163,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29110
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON6',0,5,168,9,3
	.word	44641
	.byte	14,5,171,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29210
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CHIPID',0,5,176,9,3
	.word	44706
	.byte	14,5,179,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29418
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_DTSCON',0,5,184,9,3
	.word	44770
	.byte	14,5,187,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29583
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_DTSLIM',0,5,192,9,3
	.word	44834
	.byte	14,5,195,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29766
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_DTSSTAT',0,5,200,9,3
	.word	44898
	.byte	14,5,203,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29920
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EICR',0,5,208,9,3
	.word	44963
	.byte	14,5,211,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30284
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EIFR',0,5,216,9,3
	.word	45025
	.byte	14,5,219,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30495
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EMSR',0,5,224,9,3
	.word	45087
	.byte	14,5,227,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30747
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ESRCFG',0,5,232,9,3
	.word	45149
	.byte	14,5,235,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30865
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ESROCFG',0,5,240,9,3
	.word	45213
	.byte	14,5,243,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30976
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVR13CON',0,5,248,9,3
	.word	45278
	.byte	14,5,251,9,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31139
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRADCSTAT',0,5,128,10,3
	.word	45344
	.byte	14,5,131,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31301
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRMONCTRL',0,5,136,10,3
	.word	45412
	.byte	14,5,139,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31579
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVROVMON',0,5,144,10,3
	.word	45480
	.byte	14,5,147,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31758
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRRSTCON',0,5,152,10,3
	.word	45546
	.byte	14,5,155,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31918
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,5,160,10,3
	.word	45613
	.byte	14,5,163,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	32079
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSDCTRL1',0,5,168,10,3
	.word	45682
	.byte	14,5,171,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	32271
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSDCTRL2',0,5,176,10,3
	.word	45750
	.byte	14,5,179,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	32567
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSDCTRL3',0,5,184,10,3
	.word	45818
	.byte	14,5,187,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	32782
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSTAT',0,5,192,10,3
	.word	45886
	.byte	14,5,195,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33071
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRUVMON',0,5,200,10,3
	.word	45951
	.byte	14,5,203,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33250
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EXTCON',0,5,208,10,3
	.word	46017
	.byte	14,5,211,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33468
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_FDR',0,5,216,10,3
	.word	46081
	.byte	14,5,219,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33631
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_FMR',0,5,224,10,3
	.word	46142
	.byte	14,5,227,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33967
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ID',0,5,232,10,3
	.word	46203
	.byte	14,5,235,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34074
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_IGCR',0,5,240,10,3
	.word	46263
	.byte	14,5,243,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34526
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_IN',0,5,248,10,3
	.word	46325
	.byte	14,5,251,10,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34625
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_IOCR',0,5,128,11,3
	.word	46385
	.byte	14,5,131,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34775
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LBISTCTRL0',0,5,136,11,3
	.word	46447
	.byte	14,5,139,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34924
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LBISTCTRL1',0,5,144,11,3
	.word	46515
	.byte	14,5,147,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35085
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LBISTCTRL2',0,5,152,11,3
	.word	46583
	.byte	14,5,155,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35215
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LCLCON0',0,5,160,11,3
	.word	46651
	.byte	14,5,163,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35349
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LCLTEST',0,5,168,11,3
	.word	46716
	.byte	14,5,171,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35464
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_MANID',0,5,176,11,3
	.word	46781
	.byte	14,5,179,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35575
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OMR',0,5,184,11,3
	.word	46844
	.byte	14,5,187,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35733
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OSCCON',0,5,192,11,3
	.word	46905
	.byte	14,5,195,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36073
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OUT',0,5,200,11,3
	.word	46969
	.byte	14,5,203,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36174
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OVCCON',0,5,208,11,3
	.word	47030
	.byte	14,5,211,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36441
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OVCENABLE',0,5,216,11,3
	.word	47094
	.byte	14,5,219,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36577
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PDISC',0,5,224,11,3
	.word	47161
	.byte	14,5,227,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36688
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PDR',0,5,232,11,3
	.word	47224
	.byte	14,5,235,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36821
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PDRR',0,5,240,11,3
	.word	47285
	.byte	14,5,243,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	37024
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLCON0',0,5,248,11,3
	.word	47347
	.byte	14,5,251,11,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	37380
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLCON1',0,5,128,12,3
	.word	47412
	.byte	14,5,131,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	37558
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLCON2',0,5,136,12,3
	.word	47477
	.byte	14,5,139,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	37658
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLERAYCON0',0,5,144,12,3
	.word	47542
	.byte	14,5,147,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38028
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLERAYCON1',0,5,152,12,3
	.word	47611
	.byte	14,5,155,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38214
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLERAYSTAT',0,5,160,12,3
	.word	47680
	.byte	14,5,163,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38412
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLSTAT',0,5,168,12,3
	.word	47749
	.byte	14,5,171,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38645
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMCSR',0,5,176,12,3
	.word	47814
	.byte	14,5,179,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38797
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWCR0',0,5,184,12,3
	.word	47877
	.byte	14,5,187,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	39356
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWCR1',0,5,192,12,3
	.word	47942
	.byte	14,5,195,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	39539
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWCR3',0,5,200,12,3
	.word	48007
	.byte	14,5,203,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	39708
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWSTAT',0,5,208,12,3
	.word	48072
	.byte	14,5,211,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	40275
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWSTATCLR',0,5,216,12,3
	.word	48138
	.byte	14,5,219,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	40591
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWUTCNT',0,5,224,12,3
	.word	48207
	.byte	14,5,227,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	40919
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_RSTCON',0,5,232,12,3
	.word	48274
	.byte	14,5,235,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	40710
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_RSTCON2',0,5,240,12,3
	.word	48338
	.byte	14,5,243,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	41130
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_RSTSTAT',0,5,248,12,3
	.word	48403
	.byte	14,5,251,12,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	41562
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_SAFECON',0,5,128,13,3
	.word	48468
	.byte	14,5,131,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	41658
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_STSTAT',0,5,136,13,3
	.word	48533
	.byte	14,5,139,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	41918
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_SWRSTCON',0,5,144,13,3
	.word	48597
	.byte	14,5,147,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42043
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_SYSCON',0,5,152,13,3
	.word	48663
	.byte	14,5,155,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42240
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_TRAPCLR',0,5,160,13,3
	.word	48727
	.byte	14,5,163,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42393
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_TRAPDIS',0,5,168,13,3
	.word	48792
	.byte	14,5,171,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42546
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_TRAPSET',0,5,176,13,3
	.word	48857
	.byte	14,5,179,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42699
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_TRAPSTAT',0,5,184,13,3
	.word	48922
	.byte	14,5,187,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42854
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTCPU_CON0',0,5,192,13,3
	.word	48988
	.byte	14,5,195,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43000
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTCPU_CON1',0,5,200,13,3
	.word	49057
	.byte	14,5,203,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43238
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTCPU_SR',0,5,208,13,3
	.word	49126
	.byte	14,5,211,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43461
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTS_CON0',0,5,216,13,3
	.word	49193
	.byte	14,5,219,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43587
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTS_CON1',0,5,224,13,3
	.word	49260
	.byte	14,5,227,13,9,4,15
	.byte	'U',0,4
	.word	1985
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	16275
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43839
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTS_SR',0,5,232,13,3
	.word	49327
	.byte	12
	.byte	'_Ifx_SCU_WDTCPU',0,5,243,13,25,12,15
	.byte	'CON0',0,4
	.word	48988
	.byte	2,35,0,15
	.byte	'CON1',0,4
	.word	49057
	.byte	2,35,4,15
	.byte	'SR',0,4
	.word	49126
	.byte	2,35,8,0,19
	.word	49392
	.byte	10
	.byte	'Ifx_SCU_WDTCPU',0,5,248,13,3
	.word	49455
	.byte	12
	.byte	'_Ifx_SCU_WDTS',0,5,251,13,25,12,15
	.byte	'CON0',0,4
	.word	49193
	.byte	2,35,0,15
	.byte	'CON1',0,4
	.word	49260
	.byte	2,35,4,15
	.byte	'SR',0,4
	.word	49327
	.byte	2,35,8,0,19
	.word	49484
	.byte	10
	.byte	'Ifx_SCU_WDTS',0,5,128,14,3
	.word	49545
	.byte	10
	.byte	'uint8',0,6,90,29
	.word	240
	.byte	10
	.byte	'uint16',0,6,92,29
	.word	184
	.byte	2
	.byte	'unsigned long int',0,4,7,10
	.byte	'uint32',0,6,94,29
	.word	49601
	.byte	10
	.byte	'Std_ReturnType',0,7,113,15
	.word	240
	.byte	10
	.byte	'Adc_ChannelType',0,8,227,2,15
	.word	240
	.byte	10
	.byte	'Adc_GroupType',0,8,233,2,16
	.word	184
	.byte	10
	.byte	'Adc_ValueGroupType',0,8,247,2,16
	.word	184
	.byte	10
	.byte	'Adc_PrescaleType',0,8,144,3,16
	.word	49601
	.byte	10
	.byte	'Adc_StatusType',0,8,192,3,15
	.word	240
	.byte	10
	.byte	'Adc_TriggerSourceType',0,8,200,3,15
	.word	240
	.byte	10
	.byte	'Adc_GroupConvModeType',0,8,208,3,15
	.word	240
	.byte	10
	.byte	'Adc_GroupPriorityType',0,8,215,3,15
	.word	240
	.byte	10
	.byte	'Adc_GroupDefType',0,8,130,4,25
	.word	240
	.byte	10
	.byte	'Adc_StreamNumSampleType',0,8,135,4,15
	.word	240
	.byte	10
	.byte	'Adc_StreamBufferModeType',0,8,148,4,15
	.word	240
	.byte	10
	.byte	'Adc_TriggSrcArbLevelType',0,8,179,4,16
	.word	49601
	.byte	12
	.byte	'Adc_TriggSrcData',0,8,205,4,16,4,15
	.byte	'GrpId',0,2
	.word	184
	.byte	2,35,0,15
	.byte	'GrpPriority',0,1
	.word	240
	.byte	2,35,2,15
	.byte	'IsrDoNothing',0,1
	.word	240
	.byte	2,35,3,0,10
	.byte	'Adc_TriggSrcDataType',0,8,223,4,2
	.word	50006
	.byte	12
	.byte	'Adc_GroupData',0,8,232,4,16,32,16,16
	.word	240
	.byte	17,15,0,15
	.byte	'GrpChannels',0,16
	.word	50138
	.byte	2,35,0,16,16
	.word	240
	.byte	17,15,0,15
	.byte	'GrpChannelRes',0,16
	.word	50168
	.byte	2,35,16,0,10
	.byte	'Adc_GroupDataType',0,8,250,4,2
	.word	50118
	.byte	12
	.byte	'Adc_GlobalData',0,8,157,5,16,164,1,9
	.word	184
	.byte	16,4
	.word	50250
	.byte	17,0,0,15
	.byte	'GroupResultBuffer',0,4
	.word	50255
	.byte	2,35,0,15
	.byte	'GroupStatus',0,4
	.word	49601
	.byte	2,35,4,15
	.byte	'GroupResultStatus',0,4
	.word	49601
	.byte	2,35,8,15
	.byte	'GrpBufferEndResultStatus',0,4
	.word	49601
	.byte	2,35,12,16,16
	.word	50006
	.byte	17,3,0,15
	.byte	'TriggSrcData',0,16
	.word	50373
	.byte	2,35,16,16,128,1
	.word	50118
	.byte	17,3,0,15
	.byte	'RsGroupData',0,128,1
	.word	50404
	.byte	2,35,32,16,1
	.word	240
	.byte	17,0,0,15
	.byte	'NumValidConRes',0,1
	.word	50436
	.byte	3,35,160,1,0,10
	.byte	'Adc_GlobalDataType',0,8,255,5,2
	.word	50228
	.byte	12
	.byte	'Adc_HwUnitCfgType',0,8,138,6,16,20,15
	.byte	'ArbitrationLength',0,4
	.word	49601
	.byte	2,35,0,15
	.byte	'TriggSrcArbLevel',0,4
	.word	49601
	.byte	2,35,4,16,8
	.word	49601
	.byte	17,1,0,15
	.byte	'KernelInputClass',0,8
	.word	50576
	.byte	2,35,8,15
	.byte	'SyncConvMode',0,1
	.word	240
	.byte	2,35,16,15
	.byte	'SlaveReady',0,1
	.word	240
	.byte	2,35,17,15
	.byte	'DmaChannel',0,1
	.word	240
	.byte	2,35,18,0,10
	.byte	'Adc_HwUnitCfgType',0,8,179,6,2
	.word	50499
	.byte	12
	.byte	'Adc_GroupCfgType',0,8,182,6,16,12,20
	.word	240
	.byte	9
	.word	50724
	.byte	15
	.byte	'GroupDefinition',0,4
	.word	50729
	.byte	2,35,0,15
	.byte	'IntChMask',0,2
	.word	184
	.byte	2,35,4,15
	.byte	'TriggSrc',0,1
	.word	240
	.byte	2,35,6,15
	.byte	'GrpRequestSrc',0,1
	.word	240
	.byte	2,35,7,15
	.byte	'ConvMode',0,1
	.word	240
	.byte	2,35,8,15
	.byte	'NumSamples',0,1
	.word	240
	.byte	2,35,9,15
	.byte	'StreamBufferMode',0,1
	.word	240
	.byte	2,35,10,0,10
	.byte	'Adc_GroupCfgType',0,8,129,7,3
	.word	50701
	.byte	12
	.byte	'Adc_ChannelCfgType',0,8,136,7,17,28,15
	.byte	'AdcChConfigValue',0,4
	.word	49601
	.byte	2,35,0,15
	.byte	'AdcChResAccumulation',0,4
	.word	49601
	.byte	2,35,4,15
	.byte	'AdcIsChLimitChkEnabled',0,4
	.word	49601
	.byte	2,35,8,15
	.byte	'AdcLimitChkRange',0,4
	.word	49601
	.byte	2,35,12,15
	.byte	'AdcLimitChkBnd',0,4
	.word	49601
	.byte	2,35,16,15
	.byte	'AdcChBndSelxValue',0,4
	.word	49601
	.byte	2,35,20,15
	.byte	'AdcSyncChannel',0,1
	.word	240
	.byte	2,35,24,0,10
	.byte	'Adc_ChannelCfgType',0,8,160,7,3
	.word	50910
	.byte	12
	.byte	'Adc_KernelConfigType',0,8,163,7,17,16,20
	.word	50499
	.byte	9
	.word	51180
	.byte	15
	.byte	'HwCfgPtr',0,4
	.word	51185
	.byte	2,35,0,20
	.word	50910
	.byte	9
	.word	51208
	.byte	15
	.byte	'ChCfgPtr',0,4
	.word	51213
	.byte	2,35,4,20
	.word	50701
	.byte	9
	.word	51236
	.byte	15
	.byte	'GrpCfgPtr',0,4
	.word	51241
	.byte	2,35,8,15
	.byte	'TotCfgGrps',0,1
	.word	240
	.byte	2,35,12,0,10
	.byte	'Adc_KernelConfigType',0,8,183,7,3
	.word	51153
	.byte	12
	.byte	'Adc_GlobalCfgType',0,8,186,7,17,28,15
	.byte	'ClkPrescale',0,4
	.word	49601
	.byte	2,35,0,15
	.byte	'GlobInputClass',0,8
	.word	50576
	.byte	2,35,4,15
	.byte	'PostCalEnable',0,4
	.word	49601
	.byte	2,35,12,15
	.byte	'LowPowerSupply',0,4
	.word	49601
	.byte	2,35,16,15
	.byte	'RefPrechargeCtrl',0,4
	.word	49601
	.byte	2,35,20,15
	.byte	'OperationMode',0,1
	.word	240
	.byte	2,35,24,0,10
	.byte	'Adc_GlobalCfgType',0,8,244,7,3
	.word	51316
	.byte	12
	.byte	'Adc_ConfigType',0,8,247,7,17,16,20
	.word	51153
	.byte	9
	.word	51530
	.byte	16,8
	.word	51535
	.byte	17,1,0,15
	.byte	'CfgPtr',0,8
	.word	51540
	.byte	2,35,0,20
	.word	51316
	.byte	9
	.word	51565
	.byte	15
	.byte	'GlobCfgPtr',0,4
	.word	51570
	.byte	2,35,8,15
	.byte	'SleepMode',0,1
	.word	240
	.byte	2,35,12,0,10
	.byte	'Adc_ConfigType',0,8,134,8,3
	.word	51509
	.byte	20
	.word	51509
	.byte	9
	.word	51639
	.byte	21
	.byte	'Adc_kConfigPtr',0,1,200,1,31
	.word	51644
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L12:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,32,13,58,15,59,15
	.byte	57,15,73,19,54,15,39,12,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,11,0,0,0,6,46,1,49,19,0,0,7,5,0,49
	.byte	19,0,0,8,59,0,3,8,0,0,9,15,0,73,19,0,0,10,22,0,3,8,58,15,59,15,57,15,73,19,0,0,11,21,0,54,15,0,0,12,19
	.byte	1,3,8,58,15,59,15,57,15,11,15,0,0,13,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,14,23,1,58,15,59,15,57
	.byte	15,11,15,0,0,15,13,0,3,8,11,15,73,19,56,9,0,0,16,1,1,11,15,73,19,0,0,17,33,0,47,15,0,0,18,13,0,11,15,73
	.byte	19,56,9,0,0,19,53,0,73,19,0,0,20,38,0,73,19,0,0,21,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L13:
	.word	.L35-.L34
.L34:
	.half	3
	.word	.L37-.L36
.L36:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Utility.h',0,0,0,0
	.byte	'..\\mcal_src\\Adc_Calibration.c',0,0,0,0
	.byte	'..\\mcal_src\\IfxVadc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Std_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Adc.h',0,0,0,0,0
.L37:
.L35:
	.sdecl	'.debug_info',debug,cluster('Adc_17_GetStartupCalStatus')
	.sect	'.debug_info'
.L14:
	.word	347
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\mcal_src\\Adc_Calibration.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L17,.L16
	.byte	2
	.word	.L10
	.byte	3
	.byte	'Adc_17_GetStartupCalStatus',0,1,95,16
	.word	.L19
	.byte	1,1,1
	.word	.L9,.L20,.L8
	.byte	4
	.word	.L9,.L20
	.byte	5
	.byte	'Status',0,1,97,18
	.word	.L19,.L21
	.byte	5
	.byte	'LoopCtr',0,1,98,18
	.word	.L19,.L22
	.byte	6
	.word	.L23,.L2,.L24
	.byte	7
	.word	.L25,.L26
	.byte	8
	.word	.L27,.L2,.L24
	.byte	5
	.byte	'RetVal',0,2,201,6,10
	.word	.L28,.L29
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Adc_17_GetStartupCalStatus')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6
	.byte	29,1,49,16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Adc_17_GetStartupCalStatus')
	.sect	'.debug_line'
.L16:
	.word	.L39-.L38
.L38:
	.half	3
	.word	.L41-.L40
.L40:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Adc_Calibration.c',0,0,0,0
	.byte	'..\\mcal_src\\Adc_Utility.h',0,0,0,0,0
.L41:
	.byte	5,10,7,0,5,2
	.word	.L9
	.byte	3,227,0,1,4,2,5,9,3,236,5,1,4,1,5,11,9
	.half	.L30-.L9
	.byte	3,150,122,1,4,2,5,9,3,234,5,1,4,1,5,38,9
	.half	.L31-.L30
	.byte	3,166,122,1,4,2,5,32,9
	.half	.L2-.L31
	.byte	3,213,5,1,5,37,9
	.half	.L42-.L2
	.byte	1,5,50,9
	.half	.L32-.L42
	.byte	1,5,3,9
	.half	.L43-.L32
	.byte	3,2,1,5,31,7,9
	.half	.L44-.L43
	.byte	3,3,1,5,14,9
	.half	.L45-.L44
	.byte	3,2,1,4,1,5,5,9
	.half	.L3-.L45
	.byte	3,151,122,1,5,15,7,9
	.half	.L24-.L3
	.byte	3,4,1,5,16,9
	.half	.L46-.L24
	.byte	3,1,1,5,15,9
	.half	.L47-.L46
	.byte	3,127,1,5,79,9
	.half	.L48-.L47
	.byte	1,5,40,9
	.half	.L49-.L48
	.byte	3,1,1,5,7,9
	.half	.L50-.L49
	.byte	3,127,1,5,16,9
	.half	.L51-.L50
	.byte	3,3,1,5,12,9
	.half	.L5-.L51
	.byte	3,4,1,5,38,3,2,1,5,1,7,9
	.half	.L52-.L5
	.byte	3,3,1,7,9
	.half	.L18-.L52
	.byte	0,1,1
.L39:
	.sdecl	'.debug_ranges',debug,cluster('Adc_17_GetStartupCalStatus')
	.sect	'.debug_ranges'
.L17:
	.word	-1,.L9,0,.L18-.L9,0,0
	.sdecl	'.debug_loc',debug,cluster('Adc_17_GetStartupCalStatus')
	.sect	'.debug_loc'
.L8:
	.word	-1,.L9,0,.L20-.L9
	.half	2
	.byte	138,0
	.word	0,0
.L26:
	.word	0,0
.L22:
	.word	-1,.L9,.L31-.L9,.L20-.L9
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L29:
	.word	-1,.L9,.L32-.L9,.L33-.L9
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L21:
	.word	-1,.L9,.L30-.L9,.L20-.L9
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L53:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Adc_17_GetStartupCalStatus')
	.sect	'.debug_frame'
	.word	20
	.word	.L53,.L9,.L20-.L9
	.byte	8,19,8,21,8,22,8,23

; ..\mcal_src\Adc_Calibration.c	   122  
; ..\mcal_src\Adc_Calibration.c	   123  #define ADC_STOP_SEC_CODE
; ..\mcal_src\Adc_Calibration.c	   124  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Adc_Calibration.c	   125   is allowed only for MemMap.h*/
; ..\mcal_src\Adc_Calibration.c	   126  #include "MemMap.h"

	; Module end
