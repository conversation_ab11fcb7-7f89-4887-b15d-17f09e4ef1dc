{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {}, "path": "C:/msys64/mingw64/bin/gcc.exe", "version": ""}, "language": "ASM", "sourceFileExtensions": ["s", "S", "asm"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/13.2.0/include", "C:/msys64/mingw64/include", "C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed"], "linkDirectories": ["C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/13.2.0", "C:/msys64/mingw64/lib/gcc", "C:/msys64/mingw64/x86_64-w64-mingw32/lib", "C:/msys64/mingw64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["mingw32", "gcc", "moldname", "mingwex", "kernel32", "pthread", "advapi32", "shell32", "user32", "kernel32", "mingw32", "gcc", "moldname", "mingwex", "kernel32"]}, "path": "C:/msys64/mingw64/bin/gcc.exe", "version": "13.2.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"implicit": {}, "path": "C:/msys64/mingw64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}