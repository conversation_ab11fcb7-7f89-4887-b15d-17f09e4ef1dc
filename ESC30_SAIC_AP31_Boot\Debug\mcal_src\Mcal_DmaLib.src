	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc6372a -c99 --dep-file=mcal_src\\.Mcal_DmaLib.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Mcal_DmaLib.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Mcal_DmaLib.src ..\\mcal_src\\Mcal_DmaLib.c"
	.compiler_name		"ctc"
	.name	"Mcal_DmaLib"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Mcal_DmaEnableIntr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Mcal_DmaEnableIntr

; ..\mcal_src\Mcal_DmaLib.c	     1  /******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	     2  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_src\Mcal_DmaLib.c	     4  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Mcal_DmaLib.c	     6  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Mcal_DmaLib.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Mcal_DmaLib.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Mcal_DmaLib.c	    10  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    11  *******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	    12  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    13  **  $FILENAME   : Mcal_DmaLib.c $                                            **
; ..\mcal_src\Mcal_DmaLib.c	    14  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    15  **  $CC VERSION : \main\26 $                                                 **
; ..\mcal_src\Mcal_DmaLib.c	    16  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    17  **  $DATE       : 2016-08-04 $                                               **
; ..\mcal_src\Mcal_DmaLib.c	    18  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Mcal_DmaLib.c	    20  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Mcal_DmaLib.c	    22  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    23  **  DESCRIPTION : This file contains Mcal Dma library routines               **
; ..\mcal_src\Mcal_DmaLib.c	    24  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    25  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Mcal_DmaLib.c	    26  **                                                                           **
; ..\mcal_src\Mcal_DmaLib.c	    27  ******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	    28  /*******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	    29  **  TRACEABILITY : [cover parentID= SAS_NAS_ALL_PR455,SAS_NAS_ALL_PR128,
; ..\mcal_src\Mcal_DmaLib.c	    30  SAS_NAS_ALL_PR70,SAS_NAS_ALL_PR1652,SAS_NAS_ALL_PR630_PR631,SAS_NAS_ALL_PR470,
; ..\mcal_src\Mcal_DmaLib.c	    31  DS_NAS_MCALLIB_PR131,DS_NAS_MCALLIB_PR115]
; ..\mcal_src\Mcal_DmaLib.c	    32                     [/cover]
; ..\mcal_src\Mcal_DmaLib.c	    33  *******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	    34  /*******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	    35  **                      Includes                                              **
; ..\mcal_src\Mcal_DmaLib.c	    36  *******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	    37  #include "Std_Types.h"
; ..\mcal_src\Mcal_DmaLib.c	    38  #include "IfxDma_reg.h"
; ..\mcal_src\Mcal_DmaLib.c	    39  #include "IfxScu_reg.h"
; ..\mcal_src\Mcal_DmaLib.c	    40  #include "IfxCpu_reg.h"
; ..\mcal_src\Mcal_DmaLib.c	    41  #include "IfxSrc_reg.h"
; ..\mcal_src\Mcal_DmaLib.c	    42  /* Own header file */
; ..\mcal_src\Mcal_DmaLib.c	    43  #include "Mcal.h"
; ..\mcal_src\Mcal_DmaLib.c	    44  #include "Mcal_TcLib.h"
; ..\mcal_src\Mcal_DmaLib.c	    45  #include "Mcal_WdgLib.h"
; ..\mcal_src\Mcal_DmaLib.c	    46  #include "Mcal_DmaLib.h"
; ..\mcal_src\Mcal_DmaLib.c	    47  
; ..\mcal_src\Mcal_DmaLib.c	    48  /*******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	    49  **                      File Inclusion Check                                  **
; ..\mcal_src\Mcal_DmaLib.c	    50  *******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	    51  #ifndef MCAL_SW_MAJOR_VERSION
; ..\mcal_src\Mcal_DmaLib.c	    52  #error "MCAL_SW_MAJOR_VERSION is not defined. "
; ..\mcal_src\Mcal_DmaLib.c	    53  #endif
; ..\mcal_src\Mcal_DmaLib.c	    54  
; ..\mcal_src\Mcal_DmaLib.c	    55  #ifndef MCAL_SW_MINOR_VERSION
; ..\mcal_src\Mcal_DmaLib.c	    56    #error "MCAL_SW_MINOR_VERSION is not defined. "
; ..\mcal_src\Mcal_DmaLib.c	    57  #endif
; ..\mcal_src\Mcal_DmaLib.c	    58  
; ..\mcal_src\Mcal_DmaLib.c	    59  #ifndef MCAL_SW_PATCH_VERSION
; ..\mcal_src\Mcal_DmaLib.c	    60  #error "MCAL_SW_PATCH_VERSION is not defined. "
; ..\mcal_src\Mcal_DmaLib.c	    61  #endif
; ..\mcal_src\Mcal_DmaLib.c	    62  
; ..\mcal_src\Mcal_DmaLib.c	    63  #if ( MCAL_SW_MAJOR_VERSION != 1 )
; ..\mcal_src\Mcal_DmaLib.c	    64    #error "MCAL_SW_MAJOR_VERSION does not match. "
; ..\mcal_src\Mcal_DmaLib.c	    65  #endif
; ..\mcal_src\Mcal_DmaLib.c	    66  
; ..\mcal_src\Mcal_DmaLib.c	    67  #if ( MCAL_SW_MINOR_VERSION != 0 )
; ..\mcal_src\Mcal_DmaLib.c	    68    #error "MCAL_SW_MINOR_VERSION does not match. "
; ..\mcal_src\Mcal_DmaLib.c	    69  #endif
; ..\mcal_src\Mcal_DmaLib.c	    70  
; ..\mcal_src\Mcal_DmaLib.c	    71  
; ..\mcal_src\Mcal_DmaLib.c	    72  /*******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	    73  **                      Global Variable Definitions                           **
; ..\mcal_src\Mcal_DmaLib.c	    74  *******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	    75  
; ..\mcal_src\Mcal_DmaLib.c	    76  
; ..\mcal_src\Mcal_DmaLib.c	    77  /*******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	    78  **                      Global Function Definitions                           **
; ..\mcal_src\Mcal_DmaLib.c	    79  *******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	    80  
; ..\mcal_src\Mcal_DmaLib.c	    81  /*Memory Map of the Code*/
; ..\mcal_src\Mcal_DmaLib.c	    82  #define MCAL_DMALIB_START_SEC_CODE
; ..\mcal_src\Mcal_DmaLib.c	    83  /*
; ..\mcal_src\Mcal_DmaLib.c	    84    Allows to map variables, constants and code of modules to individual
; ..\mcal_src\Mcal_DmaLib.c	    85    memory sections.
; ..\mcal_src\Mcal_DmaLib.c	    86  */
; ..\mcal_src\Mcal_DmaLib.c	    87  #include "MemMap.h"
; ..\mcal_src\Mcal_DmaLib.c	    88  
; ..\mcal_src\Mcal_DmaLib.c	    89  /*******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	    90  ** Traceability     : [cover parentID=SAS_NAS_MCALLIB_PR122_7,
; ..\mcal_src\Mcal_DmaLib.c	    91  SAS_NAS_MCALLIB_PR472]                                         [/cover]       **
; ..\mcal_src\Mcal_DmaLib.c	    92  ** Syntax           : void Mcal_DmaEnableIntr                                 **
; ..\mcal_src\Mcal_DmaLib.c	    93  ** (                                                                          **
; ..\mcal_src\Mcal_DmaLib.c	    94  **   uint32 Module                                                            **
; ..\mcal_src\Mcal_DmaLib.c	    95  ** )                                                                          **
; ..\mcal_src\Mcal_DmaLib.c	    96  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	    97  ** Service ID       : NA                                                      **
; ..\mcal_src\Mcal_DmaLib.c	    98  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	    99  ** Sync/Async       : Asynchronous                                            **
; ..\mcal_src\Mcal_DmaLib.c	   100  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   101  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Mcal_DmaLib.c	   102  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   103  ** Parameters (in)  : Module - Hw module no.                                  **
; ..\mcal_src\Mcal_DmaLib.c	   104  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   105  ** Parameters (out) : None                                                    **
; ..\mcal_src\Mcal_DmaLib.c	   106  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   107  ** Return value     : None.                                                   **
; ..\mcal_src\Mcal_DmaLib.c	   108  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   109  ** Description      : Function to Enable Dma Hw channel Interrupt             **
; ..\mcal_src\Mcal_DmaLib.c	   110  *******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	   111  void  Mcal_DmaEnableIntr(Dma_ChannelType Channel)
; Function Mcal_DmaEnableIntr
.L19:
Mcal_DmaEnableIntr:	.type	func

; ..\mcal_src\Mcal_DmaLib.c	   112  {
; ..\mcal_src\Mcal_DmaLib.c	   113    volatile uint32 *lAddress;
; ..\mcal_src\Mcal_DmaLib.c	   114  
; ..\mcal_src\Mcal_DmaLib.c	   115    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to efficiently
; ..\mcal_src\Mcal_DmaLib.c	   116    access the SFRs of multiple DMA channels*/
; ..\mcal_src\Mcal_DmaLib.c	   117    lAddress = (volatile uint32 *)(volatile void *)(SRC_DMACH0ADDR + Channel);
	fcall	.cocofun_1
.L56:

; ..\mcal_src\Mcal_DmaLib.c	   118  
; ..\mcal_src\Mcal_DmaLib.c	   119    /* Set SRC.SRE and SRC.CLRR */
; ..\mcal_src\Mcal_DmaLib.c	   120    MCAL_REG_MODIFY32(*lAddress,(SRC_CLRR_SRE_CLRMSK),(SRC_CLRR_SRE_SETMSK))
	mov.u	d0,#64511
	addih	d0,d0,#32255
	and	d15,d0
	mov	d0,#1024
	addih	d0,d0,#512
.L77:
	or	d15,d0
	st.w	[a15],d15
.L57:

; ..\mcal_src\Mcal_DmaLib.c	   121    
; ..\mcal_src\Mcal_DmaLib.c	   122  }
	ret
.L50:
	
__Mcal_DmaEnableIntr_function_end:
	.size	Mcal_DmaEnableIntr,__Mcal_DmaEnableIntr_function_end-Mcal_DmaEnableIntr
.L34:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_1')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_1
.L21:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh.a	a15,#61444
	lea	a15,[a15]@los(0xf0038500)
.L113:
	addsc.a	a15,a15,d4,#2
.L76:
	ld.w	d15,[a15]
.L114:
	fret
.L49:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Mcal_DmaDisableIntr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Mcal_DmaDisableIntr

; ..\mcal_src\Mcal_DmaLib.c	   123  
; ..\mcal_src\Mcal_DmaLib.c	   124  /*******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	   125  ** Traceability     : [cover parentID=SAS_NAS_MCALLIB_PR122_8,
; ..\mcal_src\Mcal_DmaLib.c	   126  SAS_NAS_MCALLIB_PR472]                                                [/cover]**
; ..\mcal_src\Mcal_DmaLib.c	   127  ** Syntax           :  void Mcal_DmaDisableIntr                               **
; ..\mcal_src\Mcal_DmaLib.c	   128  ** (                                                                          **
; ..\mcal_src\Mcal_DmaLib.c	   129  **   uint32 Module                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   130  ** )                                                                          **
; ..\mcal_src\Mcal_DmaLib.c	   131  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   132  ** Service ID       : NA                                                      **
; ..\mcal_src\Mcal_DmaLib.c	   133  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   134  ** Sync/Async       : Asynchronous                                            **
; ..\mcal_src\Mcal_DmaLib.c	   135  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   136  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Mcal_DmaLib.c	   137  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   138  ** Parameters (in)  : Module - Hw module no.                                  **
; ..\mcal_src\Mcal_DmaLib.c	   139  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   140  ** Parameters (out) : None                                                    **
; ..\mcal_src\Mcal_DmaLib.c	   141  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   142  ** Return value     : None.                                                   **
; ..\mcal_src\Mcal_DmaLib.c	   143  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   144  ** Description      : Function to Enable Dma Hw channel Interrupt             **
; ..\mcal_src\Mcal_DmaLib.c	   145  *******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	   146  void Mcal_DmaDisableIntr(Dma_ChannelType Channel)
; Function Mcal_DmaDisableIntr
.L23:
Mcal_DmaDisableIntr:	.type	func

; ..\mcal_src\Mcal_DmaLib.c	   147  {
; ..\mcal_src\Mcal_DmaLib.c	   148    volatile uint32 *lAddress;
; ..\mcal_src\Mcal_DmaLib.c	   149  
; ..\mcal_src\Mcal_DmaLib.c	   150    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to efficiently
; ..\mcal_src\Mcal_DmaLib.c	   151    access the SFRs of multiple DMA channels*/
; ..\mcal_src\Mcal_DmaLib.c	   152    lAddress = (volatile uint32 *)(volatile void *)(SRC_DMACH0ADDR + Channel);
	fcall	.cocofun_1
.L78:

; ..\mcal_src\Mcal_DmaLib.c	   153    /* Check if SRE is enabled */
; ..\mcal_src\Mcal_DmaLib.c	   154    if(((*lAddress)&((uint32)IFX_SRC_SRCR_SRE_MSK<<IFX_SRC_SRCR_SRE_OFF))==\ 
	jz.t	d15:10,.L2
.L63:

; ..\mcal_src\Mcal_DmaLib.c	   155                 ((uint32)IFX_SRC_SRCR_SRE_MSK<<IFX_SRC_SRCR_SRE_OFF))
; ..\mcal_src\Mcal_DmaLib.c	   156    {
; ..\mcal_src\Mcal_DmaLib.c	   157      /* Set SRC.IOVCLR, SRC.SWSCLR, SRC.CLRR  Clear SRC.SRE */
; ..\mcal_src\Mcal_DmaLib.c	   158      MCAL_REG_MODIFY32(*lAddress,SRC_IOVCLR_SWSCLR_CLRR_SRE_CLRMSK,\ 
	mov.u	d0,#64511
	ld.w	d15,[a15]
	addih	d0,d0,#44543
	and	d15,d0
	movh	d0,#20992
.L79:
	or	d15,d0
	st.w	[a15],d15
.L2:

; ..\mcal_src\Mcal_DmaLib.c	   159                             SRC_IOVCLR_SWSCLR_CLRR_SRE_SETMSK)
; ..\mcal_src\Mcal_DmaLib.c	   160    }
; ..\mcal_src\Mcal_DmaLib.c	   161  }
	ret
.L60:
	
__Mcal_DmaDisableIntr_function_end:
	.size	Mcal_DmaDisableIntr,__Mcal_DmaDisableIntr_function_end-Mcal_DmaDisableIntr
.L39:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Mcal_DmaCfgNoOfMovesPerTransfer')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Mcal_DmaCfgNoOfMovesPerTransfer

; ..\mcal_src\Mcal_DmaLib.c	   162  
; ..\mcal_src\Mcal_DmaLib.c	   163  /*******************************************************************************
; ..\mcal_src\Mcal_DmaLib.c	   164  ** Traceability     : [cover parentID=SAS_NAS_MCALLIB_PR75_4,
; ..\mcal_src\Mcal_DmaLib.c	   165  SAS_NAS_MCALLIB_PR472]                                               [/cover] **
; ..\mcal_src\Mcal_DmaLib.c	   166  ** Syntax           :  void Mcal_DmaCfgNoOfMovesPerTransfer                   **
; ..\mcal_src\Mcal_DmaLib.c	   167  ** (                                                                          **
; ..\mcal_src\Mcal_DmaLib.c	   168  **   Dma_ChannelType Channel, uint8 NoOfMoves                                 **
; ..\mcal_src\Mcal_DmaLib.c	   169  ** )                                                                          **
; ..\mcal_src\Mcal_DmaLib.c	   170  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   171  ** Service ID       : NA                                                      **
; ..\mcal_src\Mcal_DmaLib.c	   172  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   173  ** Sync/Async       : Asynchronous                                            **
; ..\mcal_src\Mcal_DmaLib.c	   174  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   175  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Mcal_DmaLib.c	   176  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   177  ** Parameters (in)  : Channel - Dma channel no.                               **
; ..\mcal_src\Mcal_DmaLib.c	   178  **                    NoOfMoves - No of Dma Moves (logical no)                **
; ..\mcal_src\Mcal_DmaLib.c	   179  ** Parameters (out) : None                                                    **
; ..\mcal_src\Mcal_DmaLib.c	   180  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   181  ** Return value     : None.                                                   **
; ..\mcal_src\Mcal_DmaLib.c	   182  **                                                                            **
; ..\mcal_src\Mcal_DmaLib.c	   183  ** Description      : Function to configure the No of Dma moves per transfer  **
; ..\mcal_src\Mcal_DmaLib.c	   184  *******************************************************************************/
; ..\mcal_src\Mcal_DmaLib.c	   185  void Mcal_DmaCfgNoOfMovesPerTransfer(Dma_ChannelType Channel, uint8 NoOfMoves)
; Function Mcal_DmaCfgNoOfMovesPerTransfer
.L25:
Mcal_DmaCfgNoOfMovesPerTransfer:	.type	func

; ..\mcal_src\Mcal_DmaLib.c	   186  {
; ..\mcal_src\Mcal_DmaLib.c	   187    volatile uint32 *lAddress;
; ..\mcal_src\Mcal_DmaLib.c	   188    uint32 lSetMask,lClearMask;
; ..\mcal_src\Mcal_DmaLib.c	   189    uint8 BlkmVal;
; ..\mcal_src\Mcal_DmaLib.c	   190  
; ..\mcal_src\Mcal_DmaLib.c	   191    switch(NoOfMoves)
; ..\mcal_src\Mcal_DmaLib.c	   192    {
; ..\mcal_src\Mcal_DmaLib.c	   193      case 1:
; ..\mcal_src\Mcal_DmaLib.c	   194      {
; ..\mcal_src\Mcal_DmaLib.c	   195        BlkmVal = 0U;
; ..\mcal_src\Mcal_DmaLib.c	   196        break;
; ..\mcal_src\Mcal_DmaLib.c	   197      }
; ..\mcal_src\Mcal_DmaLib.c	   198  
; ..\mcal_src\Mcal_DmaLib.c	   199      case 2:
	jeq	d5,#2,.L3
.L100:

; ..\mcal_src\Mcal_DmaLib.c	   200      {
; ..\mcal_src\Mcal_DmaLib.c	   201        BlkmVal = 1U;
; ..\mcal_src\Mcal_DmaLib.c	   202        break;
; ..\mcal_src\Mcal_DmaLib.c	   203      }
; ..\mcal_src\Mcal_DmaLib.c	   204  
; ..\mcal_src\Mcal_DmaLib.c	   205      case 4:
; ..\mcal_src\Mcal_DmaLib.c	   206      {
; ..\mcal_src\Mcal_DmaLib.c	   207        BlkmVal = 2U;
; ..\mcal_src\Mcal_DmaLib.c	   208        break;
; ..\mcal_src\Mcal_DmaLib.c	   209      }
; ..\mcal_src\Mcal_DmaLib.c	   210  
; ..\mcal_src\Mcal_DmaLib.c	   211      case 8:
; ..\mcal_src\Mcal_DmaLib.c	   212      {
; ..\mcal_src\Mcal_DmaLib.c	   213        BlkmVal = 3U;
; ..\mcal_src\Mcal_DmaLib.c	   214        break;
; ..\mcal_src\Mcal_DmaLib.c	   215      }
; ..\mcal_src\Mcal_DmaLib.c	   216  
; ..\mcal_src\Mcal_DmaLib.c	   217      case 16:
; ..\mcal_src\Mcal_DmaLib.c	   218      {
; ..\mcal_src\Mcal_DmaLib.c	   219        BlkmVal = 4U;
; ..\mcal_src\Mcal_DmaLib.c	   220        break;
; ..\mcal_src\Mcal_DmaLib.c	   221      }
; ..\mcal_src\Mcal_DmaLib.c	   222      case 3:
	jeq	d5,#3,.L4
.L101:
	jeq	d5,#4,.L5
.L102:

; ..\mcal_src\Mcal_DmaLib.c	   223      {
; ..\mcal_src\Mcal_DmaLib.c	   224        BlkmVal = 5U;
; ..\mcal_src\Mcal_DmaLib.c	   225        break;
; ..\mcal_src\Mcal_DmaLib.c	   226      }
; ..\mcal_src\Mcal_DmaLib.c	   227      case 5:
	jeq	d5,#5,.L6
.L103:
	mov	d15,#8
	jeq	d15,d5,.L7
.L104:

; ..\mcal_src\Mcal_DmaLib.c	   228      {
; ..\mcal_src\Mcal_DmaLib.c	   229        BlkmVal = 6U;
; ..\mcal_src\Mcal_DmaLib.c	   230        break;
; ..\mcal_src\Mcal_DmaLib.c	   231      }
; ..\mcal_src\Mcal_DmaLib.c	   232      case 9:
	mov	d15,#9
	jeq	d15,d5,.L8
.L105:
	mov	d15,#16
	jeq	d15,d5,.L9
	j	.L10
.L3:
	mov	d15,#1
	j	.L11
.L5:
	mov	d15,#2
	j	.L12
.L7:
	mov	d15,#3
	j	.L13
.L9:
	mov	d15,#4
	j	.L14
.L4:
	mov	d15,#5
	j	.L15
.L6:
	mov	d15,#6
	j	.L16
.L8:

; ..\mcal_src\Mcal_DmaLib.c	   233      {
; ..\mcal_src\Mcal_DmaLib.c	   234        BlkmVal = 7U;
; ..\mcal_src\Mcal_DmaLib.c	   235        break;
	mov	d15,#7
	j	.L17

; ..\mcal_src\Mcal_DmaLib.c	   236      }
; ..\mcal_src\Mcal_DmaLib.c	   237      default:
.L10:

; ..\mcal_src\Mcal_DmaLib.c	   238      {
; ..\mcal_src\Mcal_DmaLib.c	   239        BlkmVal = 0U;
	mov	d15,#0

; ..\mcal_src\Mcal_DmaLib.c	   240        break;
; ..\mcal_src\Mcal_DmaLib.c	   241      }
; ..\mcal_src\Mcal_DmaLib.c	   242    }
; ..\mcal_src\Mcal_DmaLib.c	   243    lSetMask=Mcal_GetSetMask((uint32)BlkmVal,IFX_DMA_CH_CHCFGR_BLKM_MSK,\ 
.L17:
.L16:
.L15:
.L14:
.L13:
.L12:
.L11:

; ..\mcal_src\Mcal_DmaLib.c	   244                                             IFX_DMA_CH_CHCFGR_BLKM_OFF);
; ..\mcal_src\Mcal_DmaLib.c	   245    lClearMask=Mcal_GetClearMask((uint32)BlkmVal,IFX_DMA_CH_CHCFGR_BLKM_MSK,\ 
; ..\mcal_src\Mcal_DmaLib.c	   246                                                 IFX_DMA_CH_CHCFGR_BLKM_OFF);
; ..\mcal_src\Mcal_DmaLib.c	   247    lAddress = (volatile uint32 *)(volatile void *)\ 
; ..\mcal_src\Mcal_DmaLib.c	   248                             (&MODULE_DMA.CH[Channel].CHCFGR.U);
	mov	d1,#-1
	movh.a	a15,#61441
.L106:
	sha	d4,#5
	lea	a15,[a15]@los(0xf0012000)
.L80:
	addsc.a	a15,a15,d4,#0
.L107:
	sh	d0,d15,#16
.L82:
	xor	d15,d1
.L81:
	and	d15,#7
.L108:

; ..\mcal_src\Mcal_DmaLib.c	   249  
; ..\mcal_src\Mcal_DmaLib.c	   250    MCAL_REG_MODIFY32(*lAddress,(~lClearMask),lSetMask)
	sh	d2,d15,#16
	ld.w	d15,[a15]20
.L73:
	xor	d1,d2
	and	d15,d1
.L83:
	or	d0,d15
	st.w	[a15]20,d0
.L74:

; ..\mcal_src\Mcal_DmaLib.c	   251  }
	ret
.L65:
	
__Mcal_DmaCfgNoOfMovesPerTransfer_function_end:
	.size	Mcal_DmaCfgNoOfMovesPerTransfer,__Mcal_DmaCfgNoOfMovesPerTransfer_function_end-Mcal_DmaCfgNoOfMovesPerTransfer
.L44:
	; End of function
	
	.calls	'Mcal_DmaEnableIntr','.cocofun_1'
	.calls	'Mcal_DmaDisableIntr','.cocofun_1'
	.calls	'Mcal_DmaEnableIntr','',0
	.calls	'.cocofun_1','',0
	.calls	'Mcal_DmaDisableIntr','',0
	.calls	'Mcal_DmaCfgNoOfMovesPerTransfer','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L27:
	.word	56557
	.half	3
	.word	.L28
	.byte	4
.L26:
	.byte	1
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L29
.L51:
	.byte	2,1,147,1,9,1,3
	.byte	'DMA_CHANNEL0',0,0,3
	.byte	'DMA_CHANNEL1',0,1,3
	.byte	'DMA_CHANNEL2',0,2,3
	.byte	'DMA_CHANNEL3',0,3,3
	.byte	'DMA_CHANNEL4',0,4,3
	.byte	'DMA_CHANNEL5',0,5,3
	.byte	'DMA_CHANNEL6',0,6,3
	.byte	'DMA_CHANNEL7',0,7,3
	.byte	'DMA_CHANNEL8',0,8,3
	.byte	'DMA_CHANNEL9',0,9,3
	.byte	'DMA_CHANNEL10',0,10,3
	.byte	'DMA_CHANNEL11',0,11,3
	.byte	'DMA_CHANNEL12',0,12,3
	.byte	'DMA_CHANNEL13',0,13,3
	.byte	'DMA_CHANNEL14',0,14,3
	.byte	'DMA_CHANNEL15',0,15,3
	.byte	'DMA_CHANNEL16',0,16,3
	.byte	'DMA_CHANNEL17',0,17,3
	.byte	'DMA_CHANNEL18',0,18,3
	.byte	'DMA_CHANNEL19',0,19,3
	.byte	'DMA_CHANNEL20',0,20,3
	.byte	'DMA_CHANNEL21',0,21,3
	.byte	'DMA_CHANNEL22',0,22,3
	.byte	'DMA_CHANNEL23',0,23,3
	.byte	'DMA_CHANNEL24',0,24,3
	.byte	'DMA_CHANNEL25',0,25,3
	.byte	'DMA_CHANNEL26',0,26,3
	.byte	'DMA_CHANNEL27',0,27,3
	.byte	'DMA_CHANNEL28',0,28,3
	.byte	'DMA_CHANNEL29',0,29,3
	.byte	'DMA_CHANNEL30',0,30,3
	.byte	'DMA_CHANNEL31',0,31,3
	.byte	'DMA_CHANNEL32',0,32,3
	.byte	'DMA_CHANNEL33',0,33,3
	.byte	'DMA_CHANNEL34',0,34,3
	.byte	'DMA_CHANNEL35',0,35,3
	.byte	'DMA_CHANNEL36',0,36,3
	.byte	'DMA_CHANNEL37',0,37,3
	.byte	'DMA_CHANNEL38',0,38,3
	.byte	'DMA_CHANNEL39',0,39,3
	.byte	'DMA_CHANNEL40',0,40,3
	.byte	'DMA_CHANNEL41',0,41,3
	.byte	'DMA_CHANNEL42',0,42,3
	.byte	'DMA_CHANNEL43',0,43,3
	.byte	'DMA_CHANNEL44',0,44,3
	.byte	'DMA_CHANNEL45',0,45,3
	.byte	'DMA_CHANNEL46',0,46,3
	.byte	'DMA_CHANNEL47',0,47,3
	.byte	'DMA_CHANNEL48',0,48,3
	.byte	'DMA_CHANNEL49',0,49,3
	.byte	'DMA_CHANNEL50',0,50,3
	.byte	'DMA_CHANNEL51',0,51,3
	.byte	'DMA_CHANNEL52',0,52,3
	.byte	'DMA_CHANNEL53',0,53,3
	.byte	'DMA_CHANNEL54',0,54,3
	.byte	'DMA_CHANNEL55',0,55,3
	.byte	'DMA_CHANNEL56',0,56,3
	.byte	'DMA_CHANNEL57',0,57,3
	.byte	'DMA_CHANNEL58',0,58,3
	.byte	'DMA_CHANNEL59',0,59,3
	.byte	'DMA_CHANNEL60',0,60,3
	.byte	'DMA_CHANNEL61',0,61,3
	.byte	'DMA_CHANNEL62',0,62,3
	.byte	'DMA_CHANNEL63',0,63,3
	.byte	'DMA_CHANNEL64',0,192,0,3
	.byte	'DMA_CHANNEL65',0,193,0,3
	.byte	'DMA_CHANNEL66',0,194,0,3
	.byte	'DMA_CHANNEL67',0,195,0,3
	.byte	'DMA_CHANNEL68',0,196,0,3
	.byte	'DMA_CHANNEL69',0,197,0,3
	.byte	'DMA_CHANNEL70',0,198,0,3
	.byte	'DMA_CHANNEL71',0,199,0,3
	.byte	'DMA_CHANNEL72',0,200,0,3
	.byte	'DMA_CHANNEL73',0,201,0,3
	.byte	'DMA_CHANNEL74',0,202,0,3
	.byte	'DMA_CHANNEL75',0,203,0,3
	.byte	'DMA_CHANNEL76',0,204,0,3
	.byte	'DMA_CHANNEL77',0,205,0,3
	.byte	'DMA_CHANNEL78',0,206,0,3
	.byte	'DMA_CHANNEL79',0,207,0,3
	.byte	'DMA_CHANNEL80',0,208,0,3
	.byte	'DMA_CHANNEL81',0,209,0,3
	.byte	'DMA_CHANNEL82',0,210,0,3
	.byte	'DMA_CHANNEL83',0,211,0,3
	.byte	'DMA_CHANNEL84',0,212,0,3
	.byte	'DMA_CHANNEL85',0,213,0,3
	.byte	'DMA_CHANNEL86',0,214,0,3
	.byte	'DMA_CHANNEL87',0,215,0,3
	.byte	'DMA_CHANNEL88',0,216,0,3
	.byte	'DMA_CHANNEL89',0,217,0,3
	.byte	'DMA_CHANNEL90',0,218,0,3
	.byte	'DMA_CHANNEL91',0,219,0,3
	.byte	'DMA_CHANNEL92',0,220,0,3
	.byte	'DMA_CHANNEL93',0,221,0,3
	.byte	'DMA_CHANNEL94',0,222,0,3
	.byte	'DMA_CHANNEL95',0,223,0,3
	.byte	'DMA_CHANNEL96',0,224,0,3
	.byte	'DMA_CHANNEL97',0,225,0,3
	.byte	'DMA_CHANNEL98',0,226,0,3
	.byte	'DMA_CHANNEL99',0,227,0,3
	.byte	'DMA_CHANNEL100',0,228,0,3
	.byte	'DMA_CHANNEL101',0,229,0,3
	.byte	'DMA_CHANNEL102',0,230,0,3
	.byte	'DMA_CHANNEL103',0,231,0,3
	.byte	'DMA_CHANNEL104',0,232,0,3
	.byte	'DMA_CHANNEL105',0,233,0,3
	.byte	'DMA_CHANNEL106',0,234,0,3
	.byte	'DMA_CHANNEL107',0,235,0,3
	.byte	'DMA_CHANNEL108',0,236,0,3
	.byte	'DMA_CHANNEL109',0,237,0,3
	.byte	'DMA_CHANNEL110',0,238,0,3
	.byte	'DMA_CHANNEL111',0,239,0,3
	.byte	'DMA_CHANNEL112',0,240,0,3
	.byte	'DMA_CHANNEL113',0,241,0,3
	.byte	'DMA_CHANNEL114',0,242,0,3
	.byte	'DMA_CHANNEL115',0,243,0,3
	.byte	'DMA_CHANNEL116',0,244,0,3
	.byte	'DMA_CHANNEL117',0,245,0,3
	.byte	'DMA_CHANNEL118',0,246,0,3
	.byte	'DMA_CHANNEL119',0,247,0,3
	.byte	'DMA_CHANNEL120',0,248,0,3
	.byte	'DMA_CHANNEL121',0,249,0,3
	.byte	'DMA_CHANNEL122',0,250,0,3
	.byte	'DMA_CHANNEL123',0,251,0,3
	.byte	'DMA_CHANNEL124',0,252,0,3
	.byte	'DMA_CHANNEL125',0,253,0,3
	.byte	'DMA_CHANNEL126',0,254,0,3
	.byte	'DMA_CHANNEL127',0,255,0,3
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0
.L58:
	.byte	4
	.byte	'unsigned long int',0,4,7,5
	.word	2340
.L54:
	.byte	6
	.word	2361
.L67:
	.byte	4
	.byte	'unsigned char',0,1,8,7
	.byte	'void',0,6
	.word	2388
	.byte	8
	.byte	'__prof_adm',0,2,1,1
	.word	2394
	.byte	9,1,6
	.word	2418
	.byte	8
	.byte	'__codeptr',0,2,1,1
	.word	2420
	.byte	8
	.byte	'uint8',0,3,90,29
	.word	2371
	.byte	4
	.byte	'unsigned short int',0,2,7,8
	.byte	'uint16',0,3,92,29
	.word	2457
	.byte	8
	.byte	'uint32',0,3,94,29
	.word	2340
	.byte	10
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	2371
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	2371
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_DMA_ACCEN00_Bits',0,4,79,3
	.word	2509
	.byte	10
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,4,82,16,4,4
	.byte	'unsigned int',0,4,7,11
	.byte	'reserved_0',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_ACCEN01_Bits',0,4,85,3
	.word	3068
	.byte	10
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	2371
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	2371
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_DMA_ACCEN10_Bits',0,4,122,3
	.word	3163
	.byte	10
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,4,125,16,4,11
	.byte	'reserved_0',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_ACCEN11_Bits',0,4,128,1,3
	.word	3722
	.byte	10
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,4,131,1,16,4,11
	.byte	'EN0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	2371
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	2371
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_DMA_ACCEN20_Bits',0,4,165,1,3
	.word	3802
	.byte	10
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,4,168,1,16,4,11
	.byte	'reserved_0',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_ACCEN21_Bits',0,4,171,1,3
	.word	4363
	.byte	10
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,4,174,1,16,4,11
	.byte	'EN0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	2371
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	2371
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_DMA_ACCEN30_Bits',0,4,208,1,3
	.word	4444
	.byte	10
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,4,211,1,16,4,11
	.byte	'reserved_0',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_ACCEN31_Bits',0,4,214,1,3
	.word	5005
	.byte	10
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,4,217,1,16,4,11
	.byte	'reserved_0',0,2
	.word	2457
	.byte	16,0,2,35,0,11
	.byte	'CSER',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'CDER',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	2371
	.byte	2,4,2,35,2,11
	.byte	'CSPBER',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'CSRIER',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	2371
	.byte	2,0,2,35,2,11
	.byte	'CRAMER',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'CSLLER',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'CDLLER',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	2371
	.byte	5,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,4,230,1,3
	.word	5086
	.byte	10
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,4,233,1,16,4,11
	.byte	'reserved_0',0,2
	.word	2457
	.byte	16,0,2,35,0,11
	.byte	'ESER',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'EDER',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	2371
	.byte	6,0,2,35,2,11
	.byte	'ERER',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'ELER',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	2371
	.byte	5,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_EER_Bits',0,4,243,1,3
	.word	5360
	.byte	10
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,4,246,1,16,4,11
	.byte	'LEC',0,1
	.word	2371
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	2457
	.byte	9,0,2,35,0,11
	.byte	'SER',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'DER',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	2371
	.byte	2,4,2,35,2,11
	.byte	'SPBER',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'SRIER',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	2371
	.byte	2,0,2,35,2,11
	.byte	'RAMER',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'SLLER',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'DLLER',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	2371
	.byte	5,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,4,132,2,3
	.word	5574
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,4,135,2,16,4,11
	.byte	'SMF',0,1
	.word	2371
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	2371
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	2371
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	2371
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	2371
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	2371
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	2371
	.byte	4,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,4,152,2,3
	.word	5858
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,4,155,2,16,4,11
	.byte	'TREL',0,2
	.word	2457
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	2371
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	2371
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	2371
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	2371
	.byte	2,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,4,168,2,3
	.word	6169
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,4,171,2,16,4,11
	.byte	'TCOUNT',0,2
	.word	2457
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	2371
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,4,184,2,3
	.word	6442
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,4,187,2,16,4,11
	.byte	'DADR',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,4,190,2,3
	.word	6709
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,4,193,2,16,4,11
	.byte	'RD00',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'RD01',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'RD02',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'RD03',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,4,199,2,3
	.word	6792
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,4,202,2,16,4,11
	.byte	'RD10',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'RD11',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'RD12',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'RD13',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,4,208,2,3
	.word	6919
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,4,211,2,16,4,11
	.byte	'RD20',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'RD21',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'RD22',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'RD23',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,4,217,2,3
	.word	7046
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,4,220,2,16,4,11
	.byte	'RD30',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'RD31',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'RD32',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'RD33',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,4,226,2,3
	.word	7173
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,4,229,2,16,4,11
	.byte	'RD40',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'RD41',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'RD42',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'RD43',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,4,235,2,3
	.word	7300
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,4,238,2,16,4,11
	.byte	'RD50',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'RD51',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'RD52',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'RD53',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,4,244,2,3
	.word	7427
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,4,247,2,16,4,11
	.byte	'RD60',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'RD61',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'RD62',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'RD63',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,4,253,2,3
	.word	7554
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,4,128,3,16,4,11
	.byte	'RD70',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'RD71',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'RD72',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'RD73',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,4,134,3,3
	.word	7681
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,4,137,3,16,4,11
	.byte	'RDCRC',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,4,140,3,3
	.word	7808
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,4,143,3,16,4,11
	.byte	'SADR',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,4,146,3,3
	.word	7894
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,4,149,3,16,4,11
	.byte	'SDCRC',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,4,152,3,3
	.word	7977
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,4,155,3,16,4,11
	.byte	'SHADR',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,4,158,3,3
	.word	8063
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,4,161,3,16,4,11
	.byte	'RS',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	2371
	.byte	3,4,2,35,0,11
	.byte	'WS',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	2457
	.byte	11,0,2,35,0,11
	.byte	'CH',0,1
	.word	2371
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	2457
	.byte	9,0,2,35,2,0,8
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,4,169,3,3
	.word	8149
	.byte	10
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,4,172,3,16,4,11
	.byte	'SMF',0,1
	.word	2371
	.byte	3,5,2,35,0,11
	.byte	'INCS',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'DMF',0,1
	.word	2371
	.byte	3,1,2,35,0,11
	.byte	'INCD',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'CBLS',0,1
	.word	2371
	.byte	4,4,2,35,1,11
	.byte	'CBLD',0,1
	.word	2371
	.byte	4,0,2,35,1,11
	.byte	'SHCT',0,1
	.word	2371
	.byte	4,4,2,35,2,11
	.byte	'SCBE',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'DCBE',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'STAMP',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'ETRL',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'WRPSE',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'WRPDE',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'INTCT',0,1
	.word	2371
	.byte	2,4,2,35,3,11
	.byte	'IRDV',0,1
	.word	2371
	.byte	4,0,2,35,3,0,8
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,4,189,3,3
	.word	8321
	.byte	10
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,4,192,3,16,4,11
	.byte	'TREL',0,2
	.word	2457
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'BLKM',0,1
	.word	2371
	.byte	3,5,2,35,2,11
	.byte	'RROAT',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'CHMODE',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'CHDW',0,1
	.word	2371
	.byte	3,0,2,35,2,11
	.byte	'PATSEL',0,1
	.word	2371
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'PRSEL',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'DMAPRIO',0,1
	.word	2371
	.byte	2,0,2,35,3,0,8
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,4,205,3,3
	.word	8624
	.byte	10
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,4,208,3,16,4,11
	.byte	'TCOUNT',0,2
	.word	2457
	.byte	14,2,2,35,0,11
	.byte	'reserved_14',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'LXO',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'WRPS',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'WRPD',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'ICH',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'IPM',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	2371
	.byte	2,2,2,35,2,11
	.byte	'BUFFER',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'FROZEN',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'SWB',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'CWRP',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'CICH',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'SIT',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	2371
	.byte	3,1,2,35,3,11
	.byte	'SCH',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,4,226,3,3
	.word	8893
	.byte	10
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,4,229,3,16,4,11
	.byte	'DADR',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_CH_DADR_Bits',0,4,232,3,3
	.word	9231
	.byte	10
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,4,235,3,16,4,11
	.byte	'RDCRC',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,4,238,3,3
	.word	9306
	.byte	10
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,4,241,3,16,4,11
	.byte	'SADR',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_CH_SADR_Bits',0,4,244,3,3
	.word	9386
	.byte	10
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,4,247,3,16,4,11
	.byte	'SDCRC',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,4,250,3,3
	.word	9461
	.byte	10
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,4,253,3,16,4,11
	.byte	'SHADR',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,4,128,4,3
	.word	9541
	.byte	10
	.byte	'_Ifx_DMA_CLC_Bits',0,4,131,4,16,4,11
	.byte	'DISR',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	3095
	.byte	28,0,2,35,2,0,8
	.byte	'Ifx_DMA_CLC_Bits',0,4,138,4,3
	.word	9619
	.byte	10
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,4,141,4,16,4,11
	.byte	'SIT',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	3095
	.byte	31,0,2,35,2,0,8
	.byte	'Ifx_DMA_ERRINTR_Bits',0,4,145,4,3
	.word	9762
	.byte	10
	.byte	'_Ifx_DMA_HRR_Bits',0,4,148,4,16,4,11
	.byte	'HRP',0,1
	.word	2371
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	3095
	.byte	30,0,2,35,2,0,8
	.byte	'Ifx_DMA_HRR_Bits',0,4,152,4,3
	.word	9858
	.byte	10
	.byte	'_Ifx_DMA_ID_Bits',0,4,155,4,16,4,11
	.byte	'MODREV',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_DMA_ID_Bits',0,4,160,4,3
	.word	9946
	.byte	10
	.byte	'_Ifx_DMA_MEMCON_Bits',0,4,163,4,16,4,4
	.byte	'unsigned int',0,4,7,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	2,30,2,35,0,11
	.byte	'INTERR',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'RMWERR',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	10080
	.byte	1,26,2,35,0,11
	.byte	'DATAERR',0,4
	.word	10080
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	10080
	.byte	1,24,2,35,0,11
	.byte	'PMIC',0,4
	.word	10080
	.byte	1,23,2,35,0,11
	.byte	'ERRDIS',0,4
	.word	10080
	.byte	1,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	10080
	.byte	22,0,2,35,0,0,8
	.byte	'Ifx_DMA_MEMCON_Bits',0,4,175,4,3
	.word	10053
	.byte	10
	.byte	'_Ifx_DMA_MODE_Bits',0,4,178,4,16,4,11
	.byte	'MODE',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	3095
	.byte	31,0,2,35,2,0,8
	.byte	'Ifx_DMA_MODE_Bits',0,4,182,4,3
	.word	10326
	.byte	10
	.byte	'_Ifx_DMA_OTSS_Bits',0,4,185,4,16,4,11
	.byte	'TGS',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	2371
	.byte	3,1,2,35,0,11
	.byte	'BS',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	3095
	.byte	24,0,2,35,2,0,8
	.byte	'Ifx_DMA_OTSS_Bits',0,4,191,4,3
	.word	10417
	.byte	10
	.byte	'_Ifx_DMA_PRR0_Bits',0,4,194,4,16,4,11
	.byte	'PAT00',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'PAT01',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'PAT02',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'PAT03',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_PRR0_Bits',0,4,200,4,3
	.word	10543
	.byte	10
	.byte	'_Ifx_DMA_PRR1_Bits',0,4,203,4,16,4,11
	.byte	'PAT10',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'PAT11',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'PAT12',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'PAT13',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_DMA_PRR1_Bits',0,4,209,4,3
	.word	10664
	.byte	10
	.byte	'_Ifx_DMA_SUSACR_Bits',0,4,212,4,16,4,11
	.byte	'SUSAC',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	3095
	.byte	31,0,2,35,2,0,8
	.byte	'Ifx_DMA_SUSACR_Bits',0,4,216,4,3
	.word	10785
	.byte	10
	.byte	'_Ifx_DMA_SUSENR_Bits',0,4,219,4,16,4,11
	.byte	'SUSEN',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	3095
	.byte	31,0,2,35,2,0,8
	.byte	'Ifx_DMA_SUSENR_Bits',0,4,223,4,3
	.word	10881
	.byte	10
	.byte	'_Ifx_DMA_TIME_Bits',0,4,226,4,16,4,11
	.byte	'COUNT',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_DMA_TIME_Bits',0,4,229,4,3
	.word	10977
	.byte	10
	.byte	'_Ifx_DMA_TSR_Bits',0,4,232,4,16,4,11
	.byte	'RST',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'HTRE',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'TRL',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'CH',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	2371
	.byte	4,0,2,35,0,11
	.byte	'HLTREQ',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'HLTACK',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	2371
	.byte	6,0,2,35,1,11
	.byte	'ECH',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'DCH',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'CTL',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	2371
	.byte	5,0,2,35,2,11
	.byte	'HLTCLR',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	2371
	.byte	7,0,2,35,3,0,8
	.byte	'Ifx_DMA_TSR_Bits',0,4,248,4,3
	.word	11047
	.byte	12,4,128,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,4
	.byte	'int',0,4,5,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2509
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ACCEN00',0,4,133,5,3
	.word	11348
	.byte	12,4,136,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3068
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ACCEN01',0,4,141,5,3
	.word	11420
	.byte	12,4,144,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3163
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ACCEN10',0,4,149,5,3
	.word	11485
	.byte	12,4,152,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3722
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ACCEN11',0,4,157,5,3
	.word	11550
	.byte	12,4,160,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3802
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ACCEN20',0,4,165,5,3
	.word	11615
	.byte	12,4,168,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4363
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ACCEN21',0,4,173,5,3
	.word	11680
	.byte	12,4,176,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4444
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ACCEN30',0,4,181,5,3
	.word	11745
	.byte	12,4,184,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5005
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ACCEN31',0,4,189,5,3
	.word	11810
	.byte	12,4,192,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5086
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_CLRE',0,4,197,5,3
	.word	11875
	.byte	12,4,200,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5360
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_EER',0,4,205,5,3
	.word	11941
	.byte	12,4,208,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5574
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ERRSR',0,4,213,5,3
	.word	12006
	.byte	12,4,216,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5858
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,4,221,5,3
	.word	12073
	.byte	12,4,224,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6169
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,4,229,5,3
	.word	12143
	.byte	12,4,232,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6442
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,4,237,5,3
	.word	12212
	.byte	12,4,240,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6709
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_DADR',0,4,245,5,3
	.word	12281
	.byte	12,4,248,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6792
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_R0',0,4,253,5,3
	.word	12350
	.byte	12,4,128,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6919
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_R1',0,4,133,6,3
	.word	12417
	.byte	12,4,136,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7046
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_R2',0,4,141,6,3
	.word	12484
	.byte	12,4,144,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7173
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_R3',0,4,149,6,3
	.word	12551
	.byte	12,4,152,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7300
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_R4',0,4,157,6,3
	.word	12618
	.byte	12,4,160,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7427
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_R5',0,4,165,6,3
	.word	12685
	.byte	12,4,168,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7554
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_R6',0,4,173,6,3
	.word	12752
	.byte	12,4,176,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7681
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_R7',0,4,181,6,3
	.word	12819
	.byte	12,4,184,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7808
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,4,189,6,3
	.word	12886
	.byte	12,4,192,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7894
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_SADR',0,4,197,6,3
	.word	12956
	.byte	12,4,200,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7977
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,4,205,6,3
	.word	13025
	.byte	12,4,208,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	8063
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,4,213,6,3
	.word	13095
	.byte	12,4,216,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	8149
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_BLK_ME_SR',0,4,221,6,3
	.word	13165
	.byte	12,4,224,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	8321
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CH_ADICR',0,4,229,6,3
	.word	13232
	.byte	12,4,232,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	8624
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CH_CHCFGR',0,4,237,6,3
	.word	13298
	.byte	12,4,240,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	8893
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CH_CHCSR',0,4,245,6,3
	.word	13365
	.byte	12,4,248,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9231
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CH_DADR',0,4,253,6,3
	.word	13431
	.byte	12,4,128,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9306
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CH_RDCRCR',0,4,133,7,3
	.word	13496
	.byte	12,4,136,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9386
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CH_SADR',0,4,141,7,3
	.word	13563
	.byte	12,4,144,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9461
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CH_SDCRCR',0,4,149,7,3
	.word	13628
	.byte	12,4,152,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9541
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CH_SHADR',0,4,157,7,3
	.word	13695
	.byte	12,4,160,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9619
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_CLC',0,4,165,7,3
	.word	13761
	.byte	12,4,168,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9762
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ERRINTR',0,4,173,7,3
	.word	13822
	.byte	12,4,176,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9858
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_HRR',0,4,181,7,3
	.word	13887
	.byte	12,4,184,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9946
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_ID',0,4,189,7,3
	.word	13948
	.byte	12,4,192,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10053
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_MEMCON',0,4,197,7,3
	.word	14008
	.byte	12,4,200,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10326
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_MODE',0,4,205,7,3
	.word	14072
	.byte	12,4,208,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10417
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_OTSS',0,4,213,7,3
	.word	14134
	.byte	12,4,216,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10543
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_PRR0',0,4,221,7,3
	.word	14196
	.byte	12,4,224,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10664
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_PRR1',0,4,229,7,3
	.word	14258
	.byte	12,4,232,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10785
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_SUSACR',0,4,237,7,3
	.word	14320
	.byte	12,4,240,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10881
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_SUSENR',0,4,245,7,3
	.word	14384
	.byte	12,4,248,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10977
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_TIME',0,4,253,7,3
	.word	14448
	.byte	12,4,128,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11047
	.byte	2,35,0,0,8
	.byte	'Ifx_DMA_TSR',0,4,133,8,3
	.word	14510
	.byte	10
	.byte	'_Ifx_DMA_BLK_ME',0,4,144,8,25,112,13
	.byte	'SR',0,4
	.word	13165
	.byte	2,35,0,14,12
	.word	2371
	.byte	15,11,0,13
	.byte	'reserved_4',0,12
	.word	14605
	.byte	2,35,4,13
	.byte	'R0',0,4
	.word	12350
	.byte	2,35,16,13
	.byte	'R1',0,4
	.word	12417
	.byte	2,35,20,13
	.byte	'R2',0,4
	.word	12484
	.byte	2,35,24,13
	.byte	'R3',0,4
	.word	12551
	.byte	2,35,28,13
	.byte	'R4',0,4
	.word	12618
	.byte	2,35,32,13
	.byte	'R5',0,4
	.word	12685
	.byte	2,35,36,13
	.byte	'R6',0,4
	.word	12752
	.byte	2,35,40,13
	.byte	'R7',0,4
	.word	12819
	.byte	2,35,44,14,32
	.word	2371
	.byte	15,31,0,13
	.byte	'reserved_30',0,32
	.word	14730
	.byte	2,35,48,13
	.byte	'RDCRC',0,4
	.word	12886
	.byte	2,35,80,13
	.byte	'SDCRC',0,4
	.word	13025
	.byte	2,35,84,13
	.byte	'SADR',0,4
	.word	12956
	.byte	2,35,88,13
	.byte	'DADR',0,4
	.word	12281
	.byte	2,35,92,13
	.byte	'ADICR',0,4
	.word	12073
	.byte	2,35,96,13
	.byte	'CHCR',0,4
	.word	12143
	.byte	2,35,100,13
	.byte	'SHADR',0,4
	.word	13095
	.byte	2,35,104,13
	.byte	'CHSR',0,4
	.word	12212
	.byte	2,35,108,0,5
	.word	14571
	.byte	8
	.byte	'Ifx_DMA_BLK_ME',0,4,165,8,3
	.word	14877
	.byte	10
	.byte	'_Ifx_DMA_BLK',0,4,178,8,25,128,1,13
	.byte	'EER',0,4
	.word	11941
	.byte	2,35,0,13
	.byte	'ERRSR',0,4
	.word	12006
	.byte	2,35,4,13
	.byte	'CLRE',0,4
	.word	11875
	.byte	2,35,8,14,4
	.word	2371
	.byte	15,3,0,13
	.byte	'reserved_C',0,4
	.word	14968
	.byte	2,35,12,5
	.word	14571
	.byte	13
	.byte	'ME',0,112
	.word	14997
	.byte	2,35,16,0,5
	.word	14906
	.byte	8
	.byte	'Ifx_DMA_BLK',0,4,185,8,3
	.word	15015
	.byte	10
	.byte	'_Ifx_DMA_CH',0,4,188,8,25,32,13
	.byte	'RDCRCR',0,4
	.word	13496
	.byte	2,35,0,13
	.byte	'SDCRCR',0,4
	.word	13628
	.byte	2,35,4,13
	.byte	'SADR',0,4
	.word	13563
	.byte	2,35,8,13
	.byte	'DADR',0,4
	.word	13431
	.byte	2,35,12,13
	.byte	'ADICR',0,4
	.word	13232
	.byte	2,35,16,13
	.byte	'CHCFGR',0,4
	.word	13298
	.byte	2,35,20,13
	.byte	'SHADR',0,4
	.word	13695
	.byte	2,35,24,13
	.byte	'CHCSR',0,4
	.word	13365
	.byte	2,35,28,0,5
	.word	15041
	.byte	8
	.byte	'Ifx_DMA_CH',0,4,198,8,3
	.word	15181
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,5,45,16,4,11
	.byte	'EN0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	2371
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	2371
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_ACCEN0_Bits',0,5,79,3
	.word	15206
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,5,82,16,4,11
	.byte	'reserved_0',0,4
	.word	3095
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_SCU_ACCEN1_Bits',0,5,85,3
	.word	15763
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,5,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	3095
	.byte	29,0,2,35,2,0,8
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,5,94,3
	.word	15840
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,5,97,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	2371
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	2371
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	2371
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	2371
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	2371
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	2371
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	2371
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	2371
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	2371
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_CCUCON0_Bits',0,5,111,3
	.word	15976
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,5,114,16,4,11
	.byte	'CANDIV',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	2371
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	2371
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	2371
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	2371
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	2371
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	2371
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	2371
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_CCUCON1_Bits',0,5,126,3
	.word	16258
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,5,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	3095
	.byte	26,2,2,35,2,11
	.byte	'UP',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_CCUCON2_Bits',0,5,135,1,3
	.word	16496
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,5,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	2371
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	2371
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	2371
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	2371
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	2371
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	6,2,2,35,3,11
	.byte	'UP',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_CCUCON3_Bits',0,5,149,1,3
	.word	16624
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,5,152,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	2371
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	2371
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	2371
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	2371
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	2371
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	6,2,2,35,3,11
	.byte	'UP',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_CCUCON4_Bits',0,5,163,1,3
	.word	16851
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,5,166,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	3095
	.byte	26,2,2,35,2,11
	.byte	'UP',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_CCUCON5_Bits',0,5,172,1,3
	.word	17070
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,5,175,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	2371
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	3095
	.byte	26,0,2,35,2,0,8
	.byte	'Ifx_SCU_CCUCON6_Bits',0,5,179,1,3
	.word	17198
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,5,182,1,16,4,11
	.byte	'CHREV',0,1
	.word	2371
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	2371
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	2371
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	2371
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	2371
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_CHIPID_Bits',0,5,193,1,3
	.word	17298
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,5,196,1,16,4,11
	.byte	'PWD',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	3095
	.byte	22,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	2371
	.byte	5,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_DTSCON_Bits',0,5,204,1,3
	.word	17506
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,5,207,1,16,4,11
	.byte	'LOWER',0,2
	.word	2457
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	2371
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	2457
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	2371
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_DTSLIM_Bits',0,5,216,1,3
	.word	17671
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,5,219,1,16,4,11
	.byte	'RESULT',0,2
	.word	2457
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	2371
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,5,226,1,3
	.word	17854
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,5,229,1,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	2371
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	2371
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	3095
	.byte	5,12,2,35,2,11
	.byte	'EXIS1',0,1
	.word	2371
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	2371
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EICR_Bits',0,5,248,1,3
	.word	18008
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,5,251,1,16,4,11
	.byte	'INTF0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	3095
	.byte	24,0,2,35,2,0,8
	.byte	'Ifx_SCU_EIFR_Bits',0,5,134,2,3
	.word	18372
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,5,137,2,16,4,11
	.byte	'POL',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	2457
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	2371
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	2371
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	2371
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	2371
	.byte	4,0,2,35,3,0,8
	.byte	'Ifx_SCU_EMSR_Bits',0,5,150,2,3
	.word	18583
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,5,153,2,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	2457
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	3095
	.byte	23,0,2,35,2,0,8
	.byte	'Ifx_SCU_ESRCFG_Bits',0,5,158,2,3
	.word	18835
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,5,161,2,16,4,11
	.byte	'ARI',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	3095
	.byte	30,0,2,35,2,0,8
	.byte	'Ifx_SCU_ESROCFG_Bits',0,5,166,2,3
	.word	18953
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,5,169,2,16,4,11
	.byte	'reserved_0',0,4
	.word	3095
	.byte	28,4,2,35,2,11
	.byte	'EVR13OFF',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVR13CON_Bits',0,5,176,2,3
	.word	19064
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,5,179,2,16,4,11
	.byte	'ADC13V',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,5,186,2,3
	.word	19227
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,5,189,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	2371
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	2371
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	2457
	.byte	10,0,2,35,0,11
	.byte	'SWDOVMOD',0,1
	.word	2371
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	2371
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	2371
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	2457
	.byte	8,2,2,35,2,11
	.byte	'SLCK',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,5,201,2,3
	.word	19389
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,5,204,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	6,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVROVMON_Bits',0,5,212,2,3
	.word	19667
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,5,215,2,16,4,11
	.byte	'reserved_0',0,4
	.word	3095
	.byte	28,4,2,35,2,11
	.byte	'RSTSWDOFF',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,5,222,2,3
	.word	19846
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,5,225,2,16,4,11
	.byte	'SD33P',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	2371
	.byte	4,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	2371
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	3095
	.byte	19,1,2,35,2,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,5,232,2,3
	.word	20006
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,5,235,2,16,4,11
	.byte	'SDFREQSPRD',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	2371
	.byte	4,0,2,35,0,11
	.byte	'TON',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'TOFF',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	2371
	.byte	4,4,2,35,3,11
	.byte	'SYNCDIV',0,1
	.word	2371
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,5,244,2,3
	.word	20167
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,5,247,2,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'STBS',0,1
	.word	2371
	.byte	2,6,2,35,1,11
	.byte	'STSP',0,1
	.word	2371
	.byte	2,4,2,35,1,11
	.byte	'NS',0,1
	.word	2371
	.byte	2,2,2,35,1,11
	.byte	'OL',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'PIAD',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'ADCMODE',0,1
	.word	2371
	.byte	4,4,2,35,2,11
	.byte	'ADCLPF',0,1
	.word	2371
	.byte	2,2,2,35,2,11
	.byte	'ADCLSB',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	2371
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,5,134,3,3
	.word	20359
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,5,137,3,16,4,11
	.byte	'SDOLCON',0,1
	.word	2371
	.byte	7,1,2,35,0,11
	.byte	'MODSEL',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'MODLOW',0,1
	.word	2371
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	2371
	.byte	6,2,2,35,2,11
	.byte	'MODMAN',0,1
	.word	2371
	.byte	2,0,2,35,2,11
	.byte	'MODHIGH',0,1
	.word	2371
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,5,147,3,3
	.word	20655
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,5,150,3,16,4,11
	.byte	'EVR13',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	2,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	2,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'SCMOD',0,1
	.word	2371
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	3095
	.byte	18,0,2,35,2,0,8
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,5,164,3,3
	.word	20870
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,5,167,3,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	2371
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	6,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,5,175,3,3
	.word	21159
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,5,178,3,16,4,11
	.byte	'EN0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	2371
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	2457
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	2371
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	2371
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_SCU_EXTCON_Bits',0,5,189,3,3
	.word	21338
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,5,192,3,16,4,11
	.byte	'STEP',0,2
	.word	2457
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	2371
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	2457
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	2371
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_FDR_Bits',0,5,200,3,3
	.word	21556
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,5,203,3,16,4,11
	.byte	'FS0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_SCU_FMR_Bits',0,5,223,3,3
	.word	21719
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,5,226,3,16,4,11
	.byte	'MODREV',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_ID_Bits',0,5,231,3,3
	.word	22055
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,5,234,3,16,4,11
	.byte	'IPEN00',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	2371
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	2371
	.byte	2,0,2,35,3,0,8
	.byte	'Ifx_SCU_IGCR_Bits',0,5,130,4,3
	.word	22162
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,5,133,4,16,4,11
	.byte	'P0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	3095
	.byte	30,0,2,35,2,0,8
	.byte	'Ifx_SCU_IN_Bits',0,5,138,4,3
	.word	22614
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,5,141,4,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	2371
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	2371
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_IOCR_Bits',0,5,148,4,3
	.word	22713
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,5,151,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	2457
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,5,157,4,3
	.word	22863
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,5,160,4,16,4,11
	.byte	'SEED',0,4
	.word	3095
	.byte	23,9,2,35,2,11
	.byte	'reserved_23',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	2371
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	2371
	.byte	4,0,2,35,3,0,8
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,5,167,4,3
	.word	23012
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,5,170,4,16,4,11
	.byte	'SIGNATURE',0,4
	.word	3095
	.byte	24,8,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,5,175,4,3
	.word	23173
	.byte	10
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,5,178,4,16,4,11
	.byte	'reserved_0',0,2
	.word	2457
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	2457
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_LCLCON0_Bits',0,5,184,4,3
	.word	23303
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,5,187,4,16,4,11
	.byte	'LCLT0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	3095
	.byte	30,0,2,35,2,0,8
	.byte	'Ifx_SCU_LCLTEST_Bits',0,5,192,4,3
	.word	23437
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,5,195,4,16,4,11
	.byte	'DEPT',0,1
	.word	2371
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	2457
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_MANID_Bits',0,5,200,4,3
	.word	23552
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,5,203,4,16,4,11
	.byte	'PS0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	2457
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	2457
	.byte	14,0,2,35,2,0,8
	.byte	'Ifx_SCU_OMR_Bits',0,5,211,4,3
	.word	23663
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,5,214,4,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	2371
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	2371
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	2371
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	2371
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	2371
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_SCU_OSCCON_Bits',0,5,231,4,3
	.word	23821
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,5,234,4,16,4,11
	.byte	'P0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	3095
	.byte	30,0,2,35,2,0,8
	.byte	'Ifx_SCU_OUT_Bits',0,5,239,4,3
	.word	24161
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,5,242,4,16,4,11
	.byte	'CSEL0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	2457
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	2371
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	2371
	.byte	6,0,2,35,3,0,8
	.byte	'Ifx_SCU_OVCCON_Bits',0,5,255,4,3
	.word	24262
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,5,130,5,16,4,11
	.byte	'OVEN0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	3095
	.byte	29,0,2,35,2,0,8
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,5,136,5,3
	.word	24529
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,5,139,5,16,4,11
	.byte	'PDIS0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	3095
	.byte	30,0,2,35,2,0,8
	.byte	'Ifx_SCU_PDISC_Bits',0,5,144,5,3
	.word	24665
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,5,147,5,16,4,11
	.byte	'PD0',0,1
	.word	2371
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	2371
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	3095
	.byte	24,0,2,35,2,0,8
	.byte	'Ifx_SCU_PDR_Bits',0,5,154,5,3
	.word	24776
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,5,157,5,16,4,11
	.byte	'PDR0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	3095
	.byte	24,0,2,35,2,0,8
	.byte	'Ifx_SCU_PDRR_Bits',0,5,168,5,3
	.word	24909
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,5,171,5,16,4,11
	.byte	'VCOBYP',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	2457
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	2371
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	2371
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	2371
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	2371
	.byte	4,0,2,35,3,0,8
	.byte	'Ifx_SCU_PLLCON0_Bits',0,5,188,5,3
	.word	25112
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,5,191,5,16,4,11
	.byte	'K2DIV',0,1
	.word	2371
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	2371
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	2371
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	2457
	.byte	9,0,2,35,2,0,8
	.byte	'Ifx_SCU_PLLCON1_Bits',0,5,199,5,3
	.word	25468
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,5,202,5,16,4,11
	.byte	'MODCFG',0,2
	.word	2457
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_PLLCON2_Bits',0,5,206,5,3
	.word	25646
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,5,209,5,16,4,11
	.byte	'VCOBYP',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	2457
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	2371
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	2371
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	2371
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	2371
	.byte	4,0,2,35,3,0,8
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,5,226,5,3
	.word	25746
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,5,229,5,16,4,11
	.byte	'K2DIV',0,1
	.word	2371
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	2371
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	2371
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	2371
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	2457
	.byte	9,0,2,35,2,0,8
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,5,237,5,3
	.word	26116
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,5,240,5,16,4,11
	.byte	'VCOBYST',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	3095
	.byte	26,0,2,35,2,0,8
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,5,249,5,3
	.word	26302
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,5,252,5,16,4,11
	.byte	'VCOBYST',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	3095
	.byte	24,0,2,35,2,0,8
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,5,135,6,3
	.word	26500
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,5,138,6,16,4,11
	.byte	'REQSLP',0,1
	.word	2371
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	2371
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	2371
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	3095
	.byte	21,0,2,35,2,0,8
	.byte	'Ifx_SCU_PMCSR_Bits',0,5,145,6,3
	.word	26733
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,5,148,6,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	2371
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	2371
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	2371
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	2371
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	2371
	.byte	2,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'WUTWKEN',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	2371
	.byte	2,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	2371
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,5,174,6,3
	.word	26885
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,5,177,6,16,4,11
	.byte	'reserved_0',0,2
	.word	2457
	.byte	12,4,2,35,0,11
	.byte	'IRADIS',0,1
	.word	2371
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	3095
	.byte	14,5,2,35,2,11
	.byte	'STBYEVEN',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	2371
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,5,185,6,3
	.word	27444
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,5,188,6,16,4,11
	.byte	'WUTREL',0,4
	.word	3095
	.byte	24,8,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	4,4,2,35,3,11
	.byte	'WUTDIV',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'WUTEN',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'WUTMODE',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,5,196,6,3
	.word	27627
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,5,199,6,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	2371
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	2371
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	2371
	.byte	2,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'WUTWKP',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'WUTOVRUN',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'WUTWKEN',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	2371
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	2371
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	2457
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'WUTEN',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'WUTMODE',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'WUTRUN',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,5,226,6,3
	.word	27796
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,5,229,6,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'WUTWKPCLR',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'WUTOVRUNCLR',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	2457
	.byte	14,0,2,35,2,0,8
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,5,242,6,3
	.word	28363
	.byte	10
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,5,245,6,16,4,11
	.byte	'WUTCNT',0,4
	.word	3095
	.byte	24,8,2,35,2,11
	.byte	'reserved_24',0,1
	.word	2371
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,5,250,6,3
	.word	28679
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,5,253,6,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	2457
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	2371
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	2371
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	2371
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_RSTCON2_Bits',0,5,135,7,3
	.word	28798
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,5,138,7,16,4,11
	.byte	'ESR0',0,1
	.word	2371
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	2371
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	2371
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	2371
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	2371
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	2371
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	2371
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	2371
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_RSTCON_Bits',0,5,149,7,3
	.word	29007
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,5,152,7,16,4,11
	.byte	'ESR0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	2371
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	2371
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	2371
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	2371
	.byte	3,0,2,35,3,0,8
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,5,175,7,3
	.word	29218
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,5,178,7,16,4,11
	.byte	'HBT',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	3095
	.byte	31,0,2,35,2,0,8
	.byte	'Ifx_SCU_SAFECON_Bits',0,5,182,7,3
	.word	29650
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,5,185,7,16,4,11
	.byte	'HWCFG',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	2371
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	2371
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	2371
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	2371
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	2371
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	2371
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	2371
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	2371
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	2371
	.byte	7,0,2,35,3,0,8
	.byte	'Ifx_SCU_STSTAT_Bits',0,5,198,7,3
	.word	29746
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,5,201,7,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	3095
	.byte	30,0,2,35,2,0,8
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,5,206,7,3
	.word	30006
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,5,209,7,16,4,11
	.byte	'CCTRIG0',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	2371
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	2371
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	3095
	.byte	23,0,2,35,2,0,8
	.byte	'Ifx_SCU_SYSCON_Bits',0,5,218,7,3
	.word	30131
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,5,221,7,16,4,11
	.byte	'ESR0T',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	3095
	.byte	28,0,2,35,2,0,8
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,5,228,7,3
	.word	30328
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,5,231,7,16,4,11
	.byte	'ESR0T',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	3095
	.byte	28,0,2,35,2,0,8
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,5,238,7,3
	.word	30481
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,5,241,7,16,4,11
	.byte	'ESR0T',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	3095
	.byte	28,0,2,35,2,0,8
	.byte	'Ifx_SCU_TRAPSET_Bits',0,5,248,7,3
	.word	30634
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,5,251,7,16,4,11
	.byte	'ESR0T',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	3095
	.byte	28,0,2,35,2,0,8
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,5,130,8,3
	.word	30787
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,5,133,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	10080
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,5,139,8,3
	.word	30942
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,5,142,8,16,4,11
	.byte	'reserved_0',0,1
	.word	2371
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	2371
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,5,154,8,3
	.word	31072
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,5,157,8,16,4,11
	.byte	'AE',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	2371
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,5,170,8,3
	.word	31310
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,5,173,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	10080
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,5,179,8,3
	.word	31533
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,5,182,8,16,4,11
	.byte	'CLRIRF',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	2371
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,5,195,8,3
	.word	31659
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,5,198,8,16,4,11
	.byte	'AE',0,1
	.word	2371
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	2371
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	2371
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	2371
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	2371
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	2371
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	2371
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	2371
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	2371
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	2371
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	2457
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,5,211,8,3
	.word	31911
	.byte	12,5,219,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15206
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_ACCEN0',0,5,224,8,3
	.word	32130
	.byte	12,5,227,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15763
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_ACCEN1',0,5,232,8,3
	.word	32194
	.byte	12,5,235,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15840
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_ARSTDIS',0,5,240,8,3
	.word	32258
	.byte	12,5,243,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15976
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_CCUCON0',0,5,248,8,3
	.word	32323
	.byte	12,5,251,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16258
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_CCUCON1',0,5,128,9,3
	.word	32388
	.byte	12,5,131,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16496
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_CCUCON2',0,5,136,9,3
	.word	32453
	.byte	12,5,139,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16624
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_CCUCON3',0,5,144,9,3
	.word	32518
	.byte	12,5,147,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16851
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_CCUCON4',0,5,152,9,3
	.word	32583
	.byte	12,5,155,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17070
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_CCUCON5',0,5,160,9,3
	.word	32648
	.byte	12,5,163,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17198
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_CCUCON6',0,5,168,9,3
	.word	32713
	.byte	12,5,171,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17298
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_CHIPID',0,5,176,9,3
	.word	32778
	.byte	12,5,179,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17506
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_DTSCON',0,5,184,9,3
	.word	32842
	.byte	12,5,187,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17671
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_DTSLIM',0,5,192,9,3
	.word	32906
	.byte	12,5,195,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17854
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_DTSSTAT',0,5,200,9,3
	.word	32970
	.byte	12,5,203,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18008
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EICR',0,5,208,9,3
	.word	33035
	.byte	12,5,211,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18372
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EIFR',0,5,216,9,3
	.word	33097
	.byte	12,5,219,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18583
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EMSR',0,5,224,9,3
	.word	33159
	.byte	12,5,227,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18835
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_ESRCFG',0,5,232,9,3
	.word	33221
	.byte	12,5,235,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18953
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_ESROCFG',0,5,240,9,3
	.word	33285
	.byte	12,5,243,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19064
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVR13CON',0,5,248,9,3
	.word	33350
	.byte	12,5,251,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19227
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRADCSTAT',0,5,128,10,3
	.word	33416
	.byte	12,5,131,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19389
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRMONCTRL',0,5,136,10,3
	.word	33484
	.byte	12,5,139,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19667
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVROVMON',0,5,144,10,3
	.word	33552
	.byte	12,5,147,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19846
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRRSTCON',0,5,152,10,3
	.word	33618
	.byte	12,5,155,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20006
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,5,160,10,3
	.word	33685
	.byte	12,5,163,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20167
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRSDCTRL1',0,5,168,10,3
	.word	33754
	.byte	12,5,171,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20359
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRSDCTRL2',0,5,176,10,3
	.word	33822
	.byte	12,5,179,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20655
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRSDCTRL3',0,5,184,10,3
	.word	33890
	.byte	12,5,187,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20870
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRSTAT',0,5,192,10,3
	.word	33958
	.byte	12,5,195,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21159
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EVRUVMON',0,5,200,10,3
	.word	34023
	.byte	12,5,203,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21338
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_EXTCON',0,5,208,10,3
	.word	34089
	.byte	12,5,211,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21556
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_FDR',0,5,216,10,3
	.word	34153
	.byte	12,5,219,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21719
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_FMR',0,5,224,10,3
	.word	34214
	.byte	12,5,227,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22055
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_ID',0,5,232,10,3
	.word	34275
	.byte	12,5,235,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22162
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_IGCR',0,5,240,10,3
	.word	34335
	.byte	12,5,243,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22614
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_IN',0,5,248,10,3
	.word	34397
	.byte	12,5,251,10,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22713
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_IOCR',0,5,128,11,3
	.word	34457
	.byte	12,5,131,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22863
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_LBISTCTRL0',0,5,136,11,3
	.word	34519
	.byte	12,5,139,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23012
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_LBISTCTRL1',0,5,144,11,3
	.word	34587
	.byte	12,5,147,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23173
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_LBISTCTRL2',0,5,152,11,3
	.word	34655
	.byte	12,5,155,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23303
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_LCLCON0',0,5,160,11,3
	.word	34723
	.byte	12,5,163,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23437
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_LCLTEST',0,5,168,11,3
	.word	34788
	.byte	12,5,171,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23552
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_MANID',0,5,176,11,3
	.word	34853
	.byte	12,5,179,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23663
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_OMR',0,5,184,11,3
	.word	34916
	.byte	12,5,187,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23821
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_OSCCON',0,5,192,11,3
	.word	34977
	.byte	12,5,195,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24161
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_OUT',0,5,200,11,3
	.word	35041
	.byte	12,5,203,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24262
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_OVCCON',0,5,208,11,3
	.word	35102
	.byte	12,5,211,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24529
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_OVCENABLE',0,5,216,11,3
	.word	35166
	.byte	12,5,219,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24665
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PDISC',0,5,224,11,3
	.word	35233
	.byte	12,5,227,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24776
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PDR',0,5,232,11,3
	.word	35296
	.byte	12,5,235,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24909
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PDRR',0,5,240,11,3
	.word	35357
	.byte	12,5,243,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	25112
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PLLCON0',0,5,248,11,3
	.word	35419
	.byte	12,5,251,11,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	25468
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PLLCON1',0,5,128,12,3
	.word	35484
	.byte	12,5,131,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	25646
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PLLCON2',0,5,136,12,3
	.word	35549
	.byte	12,5,139,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	25746
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PLLERAYCON0',0,5,144,12,3
	.word	35614
	.byte	12,5,147,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26116
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PLLERAYCON1',0,5,152,12,3
	.word	35683
	.byte	12,5,155,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26302
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PLLERAYSTAT',0,5,160,12,3
	.word	35752
	.byte	12,5,163,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26500
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PLLSTAT',0,5,168,12,3
	.word	35821
	.byte	12,5,171,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26733
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PMCSR',0,5,176,12,3
	.word	35886
	.byte	12,5,179,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26885
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PMSWCR0',0,5,184,12,3
	.word	35949
	.byte	12,5,187,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	27444
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PMSWCR1',0,5,192,12,3
	.word	36014
	.byte	12,5,195,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	27627
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PMSWCR3',0,5,200,12,3
	.word	36079
	.byte	12,5,203,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	27796
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PMSWSTAT',0,5,208,12,3
	.word	36144
	.byte	12,5,211,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28363
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PMSWSTATCLR',0,5,216,12,3
	.word	36210
	.byte	12,5,219,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28679
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_PMSWUTCNT',0,5,224,12,3
	.word	36279
	.byte	12,5,227,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29007
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_RSTCON',0,5,232,12,3
	.word	36346
	.byte	12,5,235,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28798
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_RSTCON2',0,5,240,12,3
	.word	36410
	.byte	12,5,243,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29218
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_RSTSTAT',0,5,248,12,3
	.word	36475
	.byte	12,5,251,12,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29650
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_SAFECON',0,5,128,13,3
	.word	36540
	.byte	12,5,131,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29746
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_STSTAT',0,5,136,13,3
	.word	36605
	.byte	12,5,139,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30006
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_SWRSTCON',0,5,144,13,3
	.word	36669
	.byte	12,5,147,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30131
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_SYSCON',0,5,152,13,3
	.word	36735
	.byte	12,5,155,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30328
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_TRAPCLR',0,5,160,13,3
	.word	36799
	.byte	12,5,163,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30481
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_TRAPDIS',0,5,168,13,3
	.word	36864
	.byte	12,5,171,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30634
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_TRAPSET',0,5,176,13,3
	.word	36929
	.byte	12,5,179,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30787
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_TRAPSTAT',0,5,184,13,3
	.word	36994
	.byte	12,5,187,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30942
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_WDTCPU_CON0',0,5,192,13,3
	.word	37060
	.byte	12,5,195,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	31072
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_WDTCPU_CON1',0,5,200,13,3
	.word	37129
	.byte	12,5,203,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	31310
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_WDTCPU_SR',0,5,208,13,3
	.word	37198
	.byte	12,5,211,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	31533
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_WDTS_CON0',0,5,216,13,3
	.word	37265
	.byte	12,5,219,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	31659
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_WDTS_CON1',0,5,224,13,3
	.word	37332
	.byte	12,5,227,13,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	31911
	.byte	2,35,0,0,8
	.byte	'Ifx_SCU_WDTS_SR',0,5,232,13,3
	.word	37399
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,5,243,13,25,12,13
	.byte	'CON0',0,4
	.word	37060
	.byte	2,35,0,13
	.byte	'CON1',0,4
	.word	37129
	.byte	2,35,4,13
	.byte	'SR',0,4
	.word	37198
	.byte	2,35,8,0,5
	.word	37464
	.byte	8
	.byte	'Ifx_SCU_WDTCPU',0,5,248,13,3
	.word	37527
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,5,251,13,25,12,13
	.byte	'CON0',0,4
	.word	37265
	.byte	2,35,0,13
	.byte	'CON1',0,4
	.word	37332
	.byte	2,35,4,13
	.byte	'SR',0,4
	.word	37399
	.byte	2,35,8,0,5
	.word	37556
	.byte	8
	.byte	'Ifx_SCU_WDTS',0,5,128,14,3
	.word	37617
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,6,45,16,4,11
	.byte	'ADDR',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_A_Bits',0,6,48,3
	.word	37644
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,6,51,16,4,11
	.byte	'VSS',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	10080
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_CPU_BIV_Bits',0,6,55,3
	.word	37705
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,6,58,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	10080
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_CPU_BTV_Bits',0,6,62,3
	.word	37784
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,6,65,16,4,11
	.byte	'CountValue',0,4
	.word	10080
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	10080
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_CPU_CCNT_Bits',0,6,69,3
	.word	37870
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,6,72,16,4,11
	.byte	'CM',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	10080
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	10080
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	10080
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	10080
	.byte	21,0,2,35,0,0,8
	.byte	'Ifx_CPU_CCTRL_Bits',0,6,80,3
	.word	37959
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,6,83,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	10080
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_CPU_COMPAT_Bits',0,6,89,3
	.word	38105
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,6,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	10080
	.byte	29,0,2,35,0,0,8
	.byte	'Ifx_CPU_CORE_ID_Bits',0,6,96,3
	.word	38232
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,6,99,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	10080
	.byte	29,0,2,35,0,0,8
	.byte	'Ifx_CPU_CPR_L_Bits',0,6,103,3
	.word	38330
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,6,106,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	10080
	.byte	29,0,2,35,0,0,8
	.byte	'Ifx_CPU_CPR_U_Bits',0,6,110,3
	.word	38423
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,6,113,16,4,11
	.byte	'MODREV',0,4
	.word	10080
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	10080
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_CPU_ID_Bits',0,6,118,3
	.word	38516
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,6,121,16,4,11
	.byte	'XE',0,4
	.word	10080
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	10080
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_CPU_CPXE_Bits',0,6,125,3
	.word	38623
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,6,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	10080
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	10080
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	10080
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_CPU_CREVT_Bits',0,6,136,1,3
	.word	38710
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,6,139,1,16,4,11
	.byte	'CID',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	10080
	.byte	29,0,2,35,0,0,8
	.byte	'Ifx_CPU_CUS_ID_Bits',0,6,143,1,3
	.word	38864
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,6,146,1,16,4,11
	.byte	'DATA',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_D_Bits',0,6,149,1,3
	.word	38958
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,6,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	10080
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	10080
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	10080
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	10080
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	10080
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	10080
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_DATR_Bits',0,6,163,1,3
	.word	39021
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,6,166,1,16,4,11
	.byte	'DE',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	10080
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	10080
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	10080
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	10080
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	10080
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	10080
	.byte	19,0,2,35,0,0,8
	.byte	'Ifx_CPU_DBGSR_Bits',0,6,177,1,3
	.word	39239
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,6,180,1,16,4,11
	.byte	'DTA',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	10080
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_CPU_DBGTCR_Bits',0,6,184,1,3
	.word	39454
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,6,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	10080
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_CPU_DCON0_Bits',0,6,192,1,3
	.word	39548
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,6,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	10080
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_DCON2_Bits',0,6,199,1,3
	.word	39664
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,6,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	10080
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_CPU_DCX_Bits',0,6,206,1,3
	.word	39765
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,6,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_DEADD_Bits',0,6,212,1,3
	.word	39858
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,6,215,1,16,4,11
	.byte	'TA',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_DIEAR_Bits',0,6,218,1,3
	.word	39938
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,6,221,1,16,4,11
	.byte	'IED',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	10080
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	10080
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	10080
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	10080
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	10080
	.byte	18,0,2,35,0,0,8
	.byte	'Ifx_CPU_DIETR_Bits',0,6,233,1,3
	.word	40007
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,6,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	10080
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_CPU_DMS_Bits',0,6,240,1,3
	.word	40236
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	10080
	.byte	29,0,2,35,0,0,8
	.byte	'Ifx_CPU_DPR_L_Bits',0,6,247,1,3
	.word	40329
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,6,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	10080
	.byte	29,0,2,35,0,0,8
	.byte	'Ifx_CPU_DPR_U_Bits',0,6,254,1,3
	.word	40424
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,6,129,2,16,4,11
	.byte	'RE',0,4
	.word	10080
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_DPRE_Bits',0,6,133,2,3
	.word	40519
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,6,136,2,16,4,11
	.byte	'WE',0,4
	.word	10080
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_DPWE_Bits',0,6,140,2,3
	.word	40609
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,6,143,2,16,4,11
	.byte	'SRE',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	10080
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	10080
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	10080
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	10080
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	10080
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	10080
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	10080
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	10080
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	10080
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	10080
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	10080
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	10080
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	10080
	.byte	7,0,2,35,0,0,8
	.byte	'Ifx_CPU_DSTR_Bits',0,6,161,2,3
	.word	40699
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,6,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	10080
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	10080
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	10080
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_CPU_EXEVT_Bits',0,6,172,2,3
	.word	41023
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,6,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	10080
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	10080
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	10080
	.byte	12,0,2,35,0,0,8
	.byte	'Ifx_CPU_FCX_Bits',0,6,180,2,3
	.word	41177
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,6,183,2,16,4,11
	.byte	'TST',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	10080
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	10080
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	10080
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	10080
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	10080
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	10080
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	10080
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	10080
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	10080
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	10080
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	10080
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	10080
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	10080
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	10080
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	10080
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,6,202,2,3
	.word	41283
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,205,2,16,4,11
	.byte	'OPC',0,4
	.word	10080
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	10080
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	10080
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	10080
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	10080
	.byte	12,0,2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,212,2,3
	.word	41632
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,6,215,2,16,4,11
	.byte	'PC',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,6,218,2,3
	.word	41792
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,224,2,3
	.word	41873
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,230,2,3
	.word	41960
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,236,2,3
	.word	42047
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,6,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	10080
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	10080
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_CPU_ICNT_Bits',0,6,243,2,3
	.word	42134
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,6,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	10080
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	10080
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	10080
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	10080
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	10080
	.byte	6,0,2,35,0,0,8
	.byte	'Ifx_CPU_ICR_Bits',0,6,253,2,3
	.word	42225
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,6,128,3,16,4,11
	.byte	'ISP',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_ISP_Bits',0,6,131,3,3
	.word	42368
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,6,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	10080
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	10080
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	10080
	.byte	12,0,2,35,0,0,8
	.byte	'Ifx_CPU_LCX_Bits',0,6,139,3,3
	.word	42434
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,6,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	10080
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	10080
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_CPU_M1CNT_Bits',0,6,146,3,3
	.word	42540
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,6,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	10080
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	10080
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_CPU_M2CNT_Bits',0,6,153,3,3
	.word	42633
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,6,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	10080
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	10080
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_CPU_M3CNT_Bits',0,6,160,3,3
	.word	42726
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,6,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	10080
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_CPU_PC_Bits',0,6,167,3,3
	.word	42819
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,6,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	10080
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_CPU_PCON0_Bits',0,6,175,3,3
	.word	42904
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,6,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	10080
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_CPU_PCON1_Bits',0,6,183,3,3
	.word	43020
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,6,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	10080
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_PCON2_Bits',0,6,190,3,3
	.word	43131
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,6,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	10080
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	10080
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	10080
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	10080
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	10080
	.byte	10,0,2,35,0,0,8
	.byte	'Ifx_CPU_PCXI_Bits',0,6,200,3,3
	.word	43232
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,6,203,3,16,4,11
	.byte	'TA',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_PIEAR_Bits',0,6,206,3,3
	.word	43362
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,6,209,3,16,4,11
	.byte	'IED',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	10080
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	10080
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	10080
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	10080
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	10080
	.byte	18,0,2,35,0,0,8
	.byte	'Ifx_CPU_PIETR_Bits',0,6,221,3,3
	.word	43431
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,6,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	10080
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_PMA0_Bits',0,6,229,3,3
	.word	43660
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,6,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	10080
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_PMA1_Bits',0,6,237,3,3
	.word	43773
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,6,240,3,16,4,11
	.byte	'PSI',0,4
	.word	10080
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	10080
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_CPU_PMA2_Bits',0,6,244,3,3
	.word	43886
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,6,247,3,16,4,11
	.byte	'FRE',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	10080
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	10080
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	10080
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	10080
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	10080
	.byte	17,0,2,35,0,0,8
	.byte	'Ifx_CPU_PSTR_Bits',0,6,129,4,3
	.word	43977
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,6,132,4,16,4,11
	.byte	'CDC',0,4
	.word	10080
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	10080
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	10080
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	10080
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	10080
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	10080
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	10080
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	10080
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	10080
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	10080
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	10080
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	10080
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	10080
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_CPU_PSW_Bits',0,6,147,4,3
	.word	44180
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,6,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	10080
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	10080
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	10080
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	10080
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_CPU_SEGEN_Bits',0,6,156,4,3
	.word	44423
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,6,159,4,16,4,11
	.byte	'PC',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	10080
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	10080
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	10080
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	10080
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	10080
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	10080
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	10080
	.byte	7,0,2,35,0,0,8
	.byte	'Ifx_CPU_SMACON_Bits',0,6,171,4,3
	.word	44551
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,6,174,4,16,4,11
	.byte	'EN',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,6,177,4,3
	.word	44792
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,6,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,6,183,4,3
	.word	44875
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,186,4,16,4,11
	.byte	'EN',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,189,4,3
	.word	44966
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,195,4,3
	.word	45057
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,6,198,4,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	5,27,2,35,0,11
	.byte	'ADDR',0,4
	.word	10080
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,6,202,4,3
	.word	45156
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,6,205,4,16,4,11
	.byte	'reserved_0',0,4
	.word	10080
	.byte	5,27,2,35,0,11
	.byte	'ADDR',0,4
	.word	10080
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,6,209,4,3
	.word	45263
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,6,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	10080
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	10080
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	10080
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_CPU_SWEVT_Bits',0,6,220,4,3
	.word	45370
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,6,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	10080
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_CPU_SYSCON_Bits',0,6,231,4,3
	.word	45524
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,6,234,4,16,4,11
	.byte	'ASI',0,4
	.word	10080
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	10080
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,6,238,4,3
	.word	45685
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,6,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	10080
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	10080
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	10080
	.byte	15,0,2,35,0,0,8
	.byte	'Ifx_CPU_TPS_CON_Bits',0,6,249,4,3
	.word	45783
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,6,252,4,16,4,11
	.byte	'Timer',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,6,255,4,3
	.word	45955
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,6,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	10080
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_CPU_TR_ADR_Bits',0,6,133,5,3
	.word	46035
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,6,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	10080
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	10080
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	10080
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	10080
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	10080
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	10080
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	10080
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	10080
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	10080
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	10080
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	10080
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	10080
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	10080
	.byte	3,0,2,35,0,0,8
	.byte	'Ifx_CPU_TR_EVT_Bits',0,6,153,5,3
	.word	46108
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,6,156,5,16,4,11
	.byte	'T0',0,4
	.word	10080
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	10080
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	10080
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	10080
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	10080
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	10080
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	10080
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	10080
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	10080
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,6,167,5,3
	.word	46426
	.byte	12,6,175,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	37644
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_A',0,6,180,5,3
	.word	46621
	.byte	12,6,183,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	37705
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_BIV',0,6,188,5,3
	.word	46680
	.byte	12,6,191,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	37784
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_BTV',0,6,196,5,3
	.word	46741
	.byte	12,6,199,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	37870
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CCNT',0,6,204,5,3
	.word	46802
	.byte	12,6,207,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	37959
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CCTRL',0,6,212,5,3
	.word	46864
	.byte	12,6,215,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38105
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_COMPAT',0,6,220,5,3
	.word	46927
	.byte	12,6,223,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38232
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CORE_ID',0,6,228,5,3
	.word	46991
	.byte	12,6,231,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38330
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CPR_L',0,6,236,5,3
	.word	47056
	.byte	12,6,239,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38423
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CPR_U',0,6,244,5,3
	.word	47119
	.byte	12,6,247,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38516
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CPU_ID',0,6,252,5,3
	.word	47182
	.byte	12,6,255,5,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38623
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CPXE',0,6,132,6,3
	.word	47246
	.byte	12,6,135,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38710
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CREVT',0,6,140,6,3
	.word	47308
	.byte	12,6,143,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38864
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_CUS_ID',0,6,148,6,3
	.word	47371
	.byte	12,6,151,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	38958
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_D',0,6,156,6,3
	.word	47435
	.byte	12,6,159,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	39021
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DATR',0,6,164,6,3
	.word	47494
	.byte	12,6,167,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	39239
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DBGSR',0,6,172,6,3
	.word	47556
	.byte	12,6,175,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	39454
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DBGTCR',0,6,180,6,3
	.word	47619
	.byte	12,6,183,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	39548
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DCON0',0,6,188,6,3
	.word	47683
	.byte	12,6,191,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	39664
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DCON2',0,6,196,6,3
	.word	47746
	.byte	12,6,199,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	39765
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DCX',0,6,204,6,3
	.word	47809
	.byte	12,6,207,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	39858
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DEADD',0,6,212,6,3
	.word	47870
	.byte	12,6,215,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	39938
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DIEAR',0,6,220,6,3
	.word	47933
	.byte	12,6,223,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	40007
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DIETR',0,6,228,6,3
	.word	47996
	.byte	12,6,231,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	40236
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DMS',0,6,236,6,3
	.word	48059
	.byte	12,6,239,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	40329
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DPR_L',0,6,244,6,3
	.word	48120
	.byte	12,6,247,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	40424
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DPR_U',0,6,252,6,3
	.word	48183
	.byte	12,6,255,6,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	40519
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DPRE',0,6,132,7,3
	.word	48246
	.byte	12,6,135,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	40609
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DPWE',0,6,140,7,3
	.word	48308
	.byte	12,6,143,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	40699
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_DSTR',0,6,148,7,3
	.word	48370
	.byte	12,6,151,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	41023
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_EXEVT',0,6,156,7,3
	.word	48432
	.byte	12,6,159,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	41177
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_FCX',0,6,164,7,3
	.word	48495
	.byte	12,6,167,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	41283
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,6,172,7,3
	.word	48556
	.byte	12,6,175,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	41632
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,6,180,7,3
	.word	48626
	.byte	12,6,183,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	41792
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,6,188,7,3
	.word	48696
	.byte	12,6,191,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	41873
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,6,196,7,3
	.word	48765
	.byte	12,6,199,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	41960
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,6,204,7,3
	.word	48836
	.byte	12,6,207,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42047
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,6,212,7,3
	.word	48907
	.byte	12,6,215,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42134
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_ICNT',0,6,220,7,3
	.word	48978
	.byte	12,6,223,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42225
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_ICR',0,6,228,7,3
	.word	49040
	.byte	12,6,231,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42368
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_ISP',0,6,236,7,3
	.word	49101
	.byte	12,6,239,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42434
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_LCX',0,6,244,7,3
	.word	49162
	.byte	12,6,247,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42540
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_M1CNT',0,6,252,7,3
	.word	49223
	.byte	12,6,255,7,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42633
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_M2CNT',0,6,132,8,3
	.word	49286
	.byte	12,6,135,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42726
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_M3CNT',0,6,140,8,3
	.word	49349
	.byte	12,6,143,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42819
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PC',0,6,148,8,3
	.word	49412
	.byte	12,6,151,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	42904
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PCON0',0,6,156,8,3
	.word	49472
	.byte	12,6,159,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43020
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PCON1',0,6,164,8,3
	.word	49535
	.byte	12,6,167,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43131
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PCON2',0,6,172,8,3
	.word	49598
	.byte	12,6,175,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43232
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PCXI',0,6,180,8,3
	.word	49661
	.byte	12,6,183,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43362
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PIEAR',0,6,188,8,3
	.word	49723
	.byte	12,6,191,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43431
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PIETR',0,6,196,8,3
	.word	49786
	.byte	12,6,199,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43660
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PMA0',0,6,204,8,3
	.word	49849
	.byte	12,6,207,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43773
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PMA1',0,6,212,8,3
	.word	49911
	.byte	12,6,215,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43886
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PMA2',0,6,220,8,3
	.word	49973
	.byte	12,6,223,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	43977
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PSTR',0,6,228,8,3
	.word	50035
	.byte	12,6,231,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	44180
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_PSW',0,6,236,8,3
	.word	50097
	.byte	12,6,239,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	44423
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SEGEN',0,6,244,8,3
	.word	50158
	.byte	12,6,247,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	44551
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SMACON',0,6,252,8,3
	.word	50221
	.byte	12,6,255,8,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	44792
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_ACCENA',0,6,132,9,3
	.word	50285
	.byte	12,6,135,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	44875
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_ACCENB',0,6,140,9,3
	.word	50355
	.byte	12,6,143,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	44966
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,6,148,9,3
	.word	50425
	.byte	12,6,151,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	45057
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,6,156,9,3
	.word	50499
	.byte	12,6,159,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	45156
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,6,164,9,3
	.word	50573
	.byte	12,6,167,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	45263
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,6,172,9,3
	.word	50643
	.byte	12,6,175,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	45370
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SWEVT',0,6,180,9,3
	.word	50713
	.byte	12,6,183,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	45524
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_SYSCON',0,6,188,9,3
	.word	50776
	.byte	12,6,191,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	45685
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_TASK_ASI',0,6,196,9,3
	.word	50840
	.byte	12,6,199,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	45783
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_TPS_CON',0,6,204,9,3
	.word	50906
	.byte	12,6,207,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	45955
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_TPS_TIMER',0,6,212,9,3
	.word	50971
	.byte	12,6,215,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	46035
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_TR_ADR',0,6,220,9,3
	.word	51038
	.byte	12,6,223,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	46108
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_TR_EVT',0,6,228,9,3
	.word	51102
	.byte	12,6,231,9,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	46426
	.byte	2,35,0,0,8
	.byte	'Ifx_CPU_TRIG_ACC',0,6,236,9,3
	.word	51166
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,6,247,9,25,8,13
	.byte	'L',0,4
	.word	47056
	.byte	2,35,0,13
	.byte	'U',0,4
	.word	47119
	.byte	2,35,4,0,5
	.word	51232
	.byte	8
	.byte	'Ifx_CPU_CPR',0,6,251,9,3
	.word	51274
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,6,254,9,25,8,13
	.byte	'L',0,4
	.word	48120
	.byte	2,35,0,13
	.byte	'U',0,4
	.word	48183
	.byte	2,35,4,0,5
	.word	51300
	.byte	8
	.byte	'Ifx_CPU_DPR',0,6,130,10,3
	.word	51342
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,6,133,10,25,16,13
	.byte	'LA',0,4
	.word	50573
	.byte	2,35,0,13
	.byte	'UA',0,4
	.word	50643
	.byte	2,35,4,13
	.byte	'ACCENA',0,4
	.word	50425
	.byte	2,35,8,13
	.byte	'ACCENB',0,4
	.word	50499
	.byte	2,35,12,0,5
	.word	51368
	.byte	8
	.byte	'Ifx_CPU_SPROT_RGN',0,6,139,10,3
	.word	51450
	.byte	10
	.byte	'_Ifx_CPU_TPS',0,6,142,10,25,16,13
	.byte	'CON',0,4
	.word	50906
	.byte	2,35,0,14,12
	.word	50971
	.byte	15,2,0,13
	.byte	'TIMER',0,12
	.word	51514
	.byte	2,35,4,0,5
	.word	51482
	.byte	8
	.byte	'Ifx_CPU_TPS',0,6,146,10,3
	.word	51539
	.byte	10
	.byte	'_Ifx_CPU_TR',0,6,149,10,25,8,13
	.byte	'EVT',0,4
	.word	51102
	.byte	2,35,0,13
	.byte	'ADR',0,4
	.word	51038
	.byte	2,35,4,0,5
	.word	51565
	.byte	8
	.byte	'Ifx_CPU_TR',0,6,153,10,3
	.word	51610
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,7,45,16,4,11
	.byte	'SRPN',0,1
	.word	2371
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	2371
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	2371
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	2371
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	2371
	.byte	4,0,2,35,1,11
	.byte	'ECC',0,1
	.word	2371
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	2371
	.byte	3,0,2,35,2,11
	.byte	'SRR',0,1
	.word	2371
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	2371
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	2371
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	2371
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	2371
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	2371
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	2371
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	2371
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_SRC_SRCR_Bits',0,7,62,3
	.word	51635
	.byte	12,7,70,9,4,13
	.byte	'U',0,4
	.word	3095
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	11365
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	51635
	.byte	2,35,0,0,8
	.byte	'Ifx_SRC_SRCR',0,7,75,3
	.word	51951
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,7,86,25,12,13
	.byte	'TX',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'RX',0,4
	.word	51951
	.byte	2,35,4,13
	.byte	'ERR',0,4
	.word	51951
	.byte	2,35,8,0,5
	.word	52011
	.byte	8
	.byte	'Ifx_SRC_ASCLIN',0,7,91,3
	.word	52070
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,7,94,25,4,13
	.byte	'SBSRC',0,4
	.word	51951
	.byte	2,35,0,0,5
	.word	52098
	.byte	8
	.byte	'Ifx_SRC_BCUSPB',0,7,97,3
	.word	52135
	.byte	10
	.byte	'_Ifx_SRC_CAN',0,7,100,25,64,14,64
	.word	51951
	.byte	15,15,0,13
	.byte	'INT',0,64
	.word	52181
	.byte	2,35,0,0,5
	.word	52163
	.byte	8
	.byte	'Ifx_SRC_CAN',0,7,103,3
	.word	52204
	.byte	10
	.byte	'_Ifx_SRC_CAN1',0,7,106,25,32,14,32
	.word	51951
	.byte	15,7,0,13
	.byte	'INT',0,32
	.word	52248
	.byte	2,35,0,0,5
	.word	52229
	.byte	8
	.byte	'Ifx_SRC_CAN1',0,7,109,3
	.word	52271
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,7,112,25,16,13
	.byte	'SR0',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'SR1',0,4
	.word	51951
	.byte	2,35,4,13
	.byte	'SR2',0,4
	.word	51951
	.byte	2,35,8,13
	.byte	'SR3',0,4
	.word	51951
	.byte	2,35,12,0,5
	.word	52297
	.byte	8
	.byte	'Ifx_SRC_CCU6',0,7,118,3
	.word	52369
	.byte	10
	.byte	'_Ifx_SRC_CERBERUS',0,7,121,25,8,14,8
	.word	51951
	.byte	15,1,0,13
	.byte	'SR',0,8
	.word	52418
	.byte	2,35,0,0,5
	.word	52395
	.byte	8
	.byte	'Ifx_SRC_CERBERUS',0,7,124,3
	.word	52440
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,7,127,25,32,13
	.byte	'SBSRC',0,4
	.word	51951
	.byte	2,35,0,14,28
	.word	2371
	.byte	15,27,0,13
	.byte	'reserved_4',0,28
	.word	52503
	.byte	2,35,4,0,5
	.word	52470
	.byte	8
	.byte	'Ifx_SRC_CPU',0,7,131,1,3
	.word	52533
	.byte	10
	.byte	'_Ifx_SRC_DMA',0,7,134,1,25,80,13
	.byte	'ERR',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'reserved_4',0,12
	.word	14605
	.byte	2,35,4,13
	.byte	'CH',0,64
	.word	52181
	.byte	2,35,16,0,5
	.word	52559
	.byte	8
	.byte	'Ifx_SRC_DMA',0,7,139,1,3
	.word	52624
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,7,142,1,25,4,13
	.byte	'SR',0,4
	.word	51951
	.byte	2,35,0,0,5
	.word	52650
	.byte	8
	.byte	'Ifx_SRC_EMEM',0,7,145,1,3
	.word	52683
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,7,148,1,25,80,13
	.byte	'INT',0,8
	.word	52418
	.byte	2,35,0,13
	.byte	'TINT',0,8
	.word	52418
	.byte	2,35,8,13
	.byte	'NDAT',0,8
	.word	52418
	.byte	2,35,16,13
	.byte	'MBSC',0,8
	.word	52418
	.byte	2,35,24,13
	.byte	'OBUSY',0,4
	.word	51951
	.byte	2,35,32,13
	.byte	'IBUSY',0,4
	.word	51951
	.byte	2,35,36,14,40
	.word	2371
	.byte	15,39,0,13
	.byte	'reserved_28',0,40
	.word	52815
	.byte	2,35,40,0,5
	.word	52710
	.byte	8
	.byte	'Ifx_SRC_ERAY',0,7,157,1,3
	.word	52846
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,7,160,1,25,4,13
	.byte	'SR',0,4
	.word	51951
	.byte	2,35,0,0,5
	.word	52873
	.byte	8
	.byte	'Ifx_SRC_ETH',0,7,163,1,3
	.word	52905
	.byte	10
	.byte	'_Ifx_SRC_EVR',0,7,166,1,25,8,13
	.byte	'WUT',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'SCDC',0,4
	.word	51951
	.byte	2,35,4,0,5
	.word	52931
	.byte	8
	.byte	'Ifx_SRC_EVR',0,7,170,1,3
	.word	52978
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,7,173,1,25,12,13
	.byte	'DONE',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'ERR',0,4
	.word	51951
	.byte	2,35,4,13
	.byte	'RFS',0,4
	.word	51951
	.byte	2,35,8,0,5
	.word	53004
	.byte	8
	.byte	'Ifx_SRC_FFT',0,7,178,1,3
	.word	53064
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,7,181,1,25,128,12,13
	.byte	'SR0',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'SR1',0,4
	.word	51951
	.byte	2,35,4,13
	.byte	'SR2',0,4
	.word	51951
	.byte	2,35,8,13
	.byte	'SR3',0,4
	.word	51951
	.byte	2,35,12,14,240,11
	.word	2371
	.byte	15,239,11,0,13
	.byte	'reserved_10',0,240,11
	.word	53163
	.byte	2,35,16,0,5
	.word	53090
	.byte	8
	.byte	'Ifx_SRC_GPSR',0,7,188,1,3
	.word	53197
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,7,191,1,25,48,13
	.byte	'CIRQ',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'T2',0,4
	.word	51951
	.byte	2,35,4,13
	.byte	'T3',0,4
	.word	51951
	.byte	2,35,8,13
	.byte	'T4',0,4
	.word	51951
	.byte	2,35,12,13
	.byte	'T5',0,4
	.word	51951
	.byte	2,35,16,13
	.byte	'T6',0,4
	.word	51951
	.byte	2,35,20,14,24
	.word	2371
	.byte	15,23,0,13
	.byte	'reserved_18',0,24
	.word	53319
	.byte	2,35,24,0,5
	.word	53224
	.byte	8
	.byte	'Ifx_SRC_GPT12',0,7,200,1,3
	.word	53350
	.byte	10
	.byte	'_Ifx_SRC_GTM',0,7,203,1,25,192,11,13
	.byte	'AEIIRQ',0,4
	.word	51951
	.byte	2,35,0,14,236,2
	.word	2371
	.byte	15,235,2,0,13
	.byte	'reserved_4',0,236,2
	.word	53414
	.byte	2,35,4,13
	.byte	'ERR',0,4
	.word	51951
	.byte	3,35,240,2,13
	.byte	'reserved_174',0,12
	.word	14605
	.byte	3,35,244,2,14,32
	.word	52248
	.byte	15,0,0,13
	.byte	'TIM',0,32
	.word	53483
	.byte	3,35,128,3,14,224,7
	.word	2371
	.byte	15,223,7,0,13
	.byte	'reserved_1A0',0,224,7
	.word	53506
	.byte	3,35,160,3,14,64
	.word	52248
	.byte	15,1,0,13
	.byte	'TOM',0,64
	.word	53541
	.byte	3,35,128,11,0,5
	.word	53378
	.byte	8
	.byte	'Ifx_SRC_GTM',0,7,212,1,3
	.word	53565
	.byte	10
	.byte	'_Ifx_SRC_HSM',0,7,215,1,25,8,13
	.byte	'HSM',0,8
	.word	52418
	.byte	2,35,0,0,5
	.word	53591
	.byte	8
	.byte	'Ifx_SRC_HSM',0,7,218,1,3
	.word	53624
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,7,221,1,25,4,13
	.byte	'SR',0,4
	.word	51951
	.byte	2,35,0,0,5
	.word	53650
	.byte	8
	.byte	'Ifx_SRC_LMU',0,7,224,1,3
	.word	53682
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,7,227,1,25,4,13
	.byte	'SR',0,4
	.word	51951
	.byte	2,35,0,0,5
	.word	53708
	.byte	8
	.byte	'Ifx_SRC_PMU',0,7,230,1,3
	.word	53740
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,7,233,1,25,24,13
	.byte	'TX',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'RX',0,4
	.word	51951
	.byte	2,35,4,13
	.byte	'ERR',0,4
	.word	51951
	.byte	2,35,8,13
	.byte	'PT',0,4
	.word	51951
	.byte	2,35,12,13
	.byte	'HC',0,4
	.word	51951
	.byte	2,35,16,13
	.byte	'U',0,4
	.word	51951
	.byte	2,35,20,0,5
	.word	53766
	.byte	8
	.byte	'Ifx_SRC_QSPI',0,7,241,1,3
	.word	53859
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,7,244,1,25,20,13
	.byte	'DTS',0,4
	.word	51951
	.byte	2,35,0,14,16
	.word	51951
	.byte	15,3,0,13
	.byte	'ERU',0,16
	.word	53918
	.byte	2,35,4,0,5
	.word	53886
	.byte	8
	.byte	'Ifx_SRC_SCU',0,7,248,1,3
	.word	53941
	.byte	10
	.byte	'_Ifx_SRC_SENT',0,7,251,1,25,16,13
	.byte	'SR',0,16
	.word	53918
	.byte	2,35,0,0,5
	.word	53967
	.byte	8
	.byte	'Ifx_SRC_SENT',0,7,254,1,3
	.word	54000
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,7,129,2,25,12,14,12
	.word	51951
	.byte	15,2,0,13
	.byte	'SR',0,12
	.word	54046
	.byte	2,35,0,0,5
	.word	54027
	.byte	8
	.byte	'Ifx_SRC_SMU',0,7,132,2,3
	.word	54068
	.byte	10
	.byte	'_Ifx_SRC_STM',0,7,135,2,25,96,13
	.byte	'SR0',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'SR1',0,4
	.word	51951
	.byte	2,35,4,14,88
	.word	2371
	.byte	15,87,0,13
	.byte	'reserved_8',0,88
	.word	54139
	.byte	2,35,8,0,5
	.word	54094
	.byte	8
	.byte	'Ifx_SRC_STM',0,7,140,2,3
	.word	54169
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,7,143,2,25,192,2,13
	.byte	'SR0',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'SR1',0,4
	.word	51951
	.byte	2,35,4,13
	.byte	'SR2',0,4
	.word	51951
	.byte	2,35,8,13
	.byte	'SR3',0,4
	.word	51951
	.byte	2,35,12,14,176,2
	.word	2371
	.byte	15,175,2,0,13
	.byte	'reserved_10',0,176,2
	.word	54270
	.byte	2,35,16,0,5
	.word	54195
	.byte	8
	.byte	'Ifx_SRC_VADCCG',0,7,150,2,3
	.word	54304
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,7,153,2,25,16,13
	.byte	'SR0',0,4
	.word	51951
	.byte	2,35,0,13
	.byte	'SR1',0,4
	.word	51951
	.byte	2,35,4,13
	.byte	'SR2',0,4
	.word	51951
	.byte	2,35,8,13
	.byte	'SR3',0,4
	.word	51951
	.byte	2,35,12,0,5
	.word	54333
	.byte	8
	.byte	'Ifx_SRC_VADCG',0,7,159,2,3
	.word	54407
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,7,162,2,25,4,13
	.byte	'SRC',0,4
	.word	51951
	.byte	2,35,0,0,5
	.word	54435
	.byte	8
	.byte	'Ifx_SRC_XBAR',0,7,165,2,3
	.word	54469
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,7,178,2,25,24,14,24
	.word	52011
	.byte	15,1,0,5
	.word	54519
	.byte	13
	.byte	'ASCLIN',0,24
	.word	54528
	.byte	2,35,0,0,5
	.word	54496
	.byte	8
	.byte	'Ifx_SRC_GASCLIN',0,7,181,2,3
	.word	54550
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,7,184,2,25,4,5
	.word	52098
	.byte	13
	.byte	'SPB',0,4
	.word	54600
	.byte	2,35,0,0,5
	.word	54580
	.byte	8
	.byte	'Ifx_SRC_GBCU',0,7,187,2,3
	.word	54619
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,7,190,2,25,96,14,64
	.word	52163
	.byte	15,0,0,5
	.word	54666
	.byte	13
	.byte	'CAN',0,64
	.word	54675
	.byte	2,35,0,14,32
	.word	52229
	.byte	15,0,0,5
	.word	54693
	.byte	13
	.byte	'CAN1',0,32
	.word	54702
	.byte	2,35,64,0,5
	.word	54646
	.byte	8
	.byte	'Ifx_SRC_GCAN',0,7,194,2,3
	.word	54722
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,7,197,2,25,32,14,32
	.word	52297
	.byte	15,1,0,5
	.word	54770
	.byte	13
	.byte	'CCU6',0,32
	.word	54779
	.byte	2,35,0,0,5
	.word	54749
	.byte	8
	.byte	'Ifx_SRC_GCCU6',0,7,200,2,3
	.word	54799
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,7,203,2,25,8,5
	.word	52395
	.byte	13
	.byte	'CERBERUS',0,8
	.word	54852
	.byte	2,35,0,0,5
	.word	54827
	.byte	8
	.byte	'Ifx_SRC_GCERBERUS',0,7,206,2,3
	.word	54876
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,7,209,2,25,32,14,32
	.word	52470
	.byte	15,0,0,5
	.word	54928
	.byte	13
	.byte	'CPU',0,32
	.word	54937
	.byte	2,35,0,0,5
	.word	54908
	.byte	8
	.byte	'Ifx_SRC_GCPU',0,7,212,2,3
	.word	54956
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,7,215,2,25,80,14,80
	.word	52559
	.byte	15,0,0,5
	.word	55003
	.byte	13
	.byte	'DMA',0,80
	.word	55012
	.byte	2,35,0,0,5
	.word	54983
	.byte	8
	.byte	'Ifx_SRC_GDMA',0,7,218,2,3
	.word	55031
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,7,221,2,25,4,14,4
	.word	52650
	.byte	15,0,0,5
	.word	55079
	.byte	13
	.byte	'EMEM',0,4
	.word	55088
	.byte	2,35,0,0,5
	.word	55058
	.byte	8
	.byte	'Ifx_SRC_GEMEM',0,7,224,2,3
	.word	55108
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,7,227,2,25,80,14,80
	.word	52710
	.byte	15,0,0,5
	.word	55157
	.byte	13
	.byte	'ERAY',0,80
	.word	55166
	.byte	2,35,0,0,5
	.word	55136
	.byte	8
	.byte	'Ifx_SRC_GERAY',0,7,230,2,3
	.word	55186
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,7,233,2,25,4,14,4
	.word	52873
	.byte	15,0,0,5
	.word	55234
	.byte	13
	.byte	'ETH',0,4
	.word	55243
	.byte	2,35,0,0,5
	.word	55214
	.byte	8
	.byte	'Ifx_SRC_GETH',0,7,236,2,3
	.word	55262
	.byte	10
	.byte	'_Ifx_SRC_GEVR',0,7,239,2,25,8,14,8
	.word	52931
	.byte	15,0,0,5
	.word	55309
	.byte	13
	.byte	'EVR',0,8
	.word	55318
	.byte	2,35,0,0,5
	.word	55289
	.byte	8
	.byte	'Ifx_SRC_GEVR',0,7,242,2,3
	.word	55337
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,7,245,2,25,12,14,12
	.word	53004
	.byte	15,0,0,5
	.word	55384
	.byte	13
	.byte	'FFT',0,12
	.word	55393
	.byte	2,35,0,0,5
	.word	55364
	.byte	8
	.byte	'Ifx_SRC_GFFT',0,7,248,2,3
	.word	55412
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,7,251,2,25,128,12,14,128,12
	.word	53090
	.byte	15,0,0,5
	.word	55461
	.byte	13
	.byte	'GPSR',0,128,12
	.word	55471
	.byte	2,35,0,0,5
	.word	55439
	.byte	8
	.byte	'Ifx_SRC_GGPSR',0,7,254,2,3
	.word	55492
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,7,129,3,25,48,14,48
	.word	53224
	.byte	15,0,0,5
	.word	55542
	.byte	13
	.byte	'GPT12',0,48
	.word	55551
	.byte	2,35,0,0,5
	.word	55520
	.byte	8
	.byte	'Ifx_SRC_GGPT12',0,7,132,3,3
	.word	55572
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,7,135,3,25,192,11,14,192,11
	.word	53378
	.byte	15,0,0,5
	.word	55622
	.byte	13
	.byte	'GTM',0,192,11
	.word	55632
	.byte	2,35,0,0,5
	.word	55601
	.byte	8
	.byte	'Ifx_SRC_GGTM',0,7,138,3,3
	.word	55652
	.byte	10
	.byte	'_Ifx_SRC_GHSM',0,7,141,3,25,8,14,8
	.word	53591
	.byte	15,0,0,5
	.word	55699
	.byte	13
	.byte	'HSM',0,8
	.word	55708
	.byte	2,35,0,0,5
	.word	55679
	.byte	8
	.byte	'Ifx_SRC_GHSM',0,7,144,3,3
	.word	55727
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,7,147,3,25,4,14,4
	.word	53650
	.byte	15,0,0,5
	.word	55774
	.byte	13
	.byte	'LMU',0,4
	.word	55783
	.byte	2,35,0,0,5
	.word	55754
	.byte	8
	.byte	'Ifx_SRC_GLMU',0,7,150,3,3
	.word	55802
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,7,153,3,25,8,14,8
	.word	53708
	.byte	15,1,0,5
	.word	55849
	.byte	13
	.byte	'PMU',0,8
	.word	55858
	.byte	2,35,0,0,5
	.word	55829
	.byte	8
	.byte	'Ifx_SRC_GPMU',0,7,156,3,3
	.word	55877
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,7,159,3,25,96,14,96
	.word	53766
	.byte	15,3,0,5
	.word	55925
	.byte	13
	.byte	'QSPI',0,96
	.word	55934
	.byte	2,35,0,0,5
	.word	55904
	.byte	8
	.byte	'Ifx_SRC_GQSPI',0,7,162,3,3
	.word	55954
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,7,165,3,25,20,5
	.word	53886
	.byte	13
	.byte	'SCU',0,20
	.word	56002
	.byte	2,35,0,0,5
	.word	55982
	.byte	8
	.byte	'Ifx_SRC_GSCU',0,7,168,3,3
	.word	56021
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,7,171,3,25,16,14,16
	.word	53967
	.byte	15,0,0,5
	.word	56069
	.byte	13
	.byte	'SENT',0,16
	.word	56078
	.byte	2,35,0,0,5
	.word	56048
	.byte	8
	.byte	'Ifx_SRC_GSENT',0,7,174,3,3
	.word	56098
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,7,177,3,25,12,14,12
	.word	54027
	.byte	15,0,0,5
	.word	56146
	.byte	13
	.byte	'SMU',0,12
	.word	56155
	.byte	2,35,0,0,5
	.word	56126
	.byte	8
	.byte	'Ifx_SRC_GSMU',0,7,180,3,3
	.word	56174
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,7,183,3,25,96,14,96
	.word	54094
	.byte	15,0,0,5
	.word	56221
	.byte	13
	.byte	'STM',0,96
	.word	56230
	.byte	2,35,0,0,5
	.word	56201
	.byte	8
	.byte	'Ifx_SRC_GSTM',0,7,186,3,3
	.word	56249
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,7,189,3,25,224,4,14,64
	.word	54333
	.byte	15,3,0,5
	.word	56298
	.byte	13
	.byte	'G',0,64
	.word	56307
	.byte	2,35,0,14,224,1
	.word	2371
	.byte	15,223,1,0,13
	.byte	'reserved_40',0,224,1
	.word	56323
	.byte	2,35,64,14,192,2
	.word	54195
	.byte	15,0,0,5
	.word	56356
	.byte	13
	.byte	'CG',0,192,2
	.word	56366
	.byte	3,35,160,2,0,5
	.word	56276
	.byte	8
	.byte	'Ifx_SRC_GVADC',0,7,194,3,3
	.word	56386
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,7,197,3,25,4,5
	.word	54435
	.byte	13
	.byte	'XBAR',0,4
	.word	56435
	.byte	2,35,0,0,5
	.word	56414
	.byte	8
	.byte	'Ifx_SRC_GXBAR',0,7,200,3,3
	.word	56455
	.byte	8
	.byte	'Dma_StatusType',0,1,121,22
	.word	3095
	.byte	8
	.byte	'Dma_ErrorStatusType',0,1,141,1,22
	.word	3095
	.byte	8
	.byte	'Dma_ChannelType',0,1,149,2,2
	.word	180
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L28:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,4,1,58,15,59,15,57,15,11,15,0,0,3,40,0,3,8,28,13,0,0,4
	.byte	36,0,3,8,11,15,62,15,0,0,5,53,0,73,19,0,0,6,15,0,73,19,0,0,7,59,0,3,8,0,0,8,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,9,21,0,54,15,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15
	.byte	56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13,0,3,8,11,15,73,19,56,9,0,0,14,1,1,11,15,73,19,0,0,15
	.byte	33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L29:
	.word	.L85-.L84
.L84:
	.half	3
	.word	.L87-.L86
.L86:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxDma_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxCpu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0,0
.L87:
.L85:
	.sdecl	'.debug_info',debug,cluster('Mcal_DmaEnableIntr')
	.sect	'.debug_info'
.L30:
	.word	298
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L33,.L32
	.byte	2
	.word	.L26
	.byte	3
	.byte	'Mcal_DmaEnableIntr',0,1,111,7,1,1,1
	.word	.L19,.L50,.L18
	.byte	4
	.byte	'Channel',0,1,111,42
	.word	.L51,.L52
	.byte	5
	.word	.L53
	.byte	6
	.byte	'lAddress',0,1,113,20
	.word	.L54,.L55
	.byte	7
	.word	.L56,.L57
	.byte	6
	.byte	'val',0,1,120,3
	.word	.L58,.L59
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Mcal_DmaEnableIntr')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,11,1,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Mcal_DmaEnableIntr')
	.sect	'.debug_line'
.L32:
	.word	.L89-.L88
.L88:
	.half	3
	.word	.L91-.L90
.L90:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0,0,0,0,0
.L91:
	.byte	5,51,7,0,5,2
	.word	.L19
	.byte	3,244,0,1,5,3,9
	.half	.L56-.L19
	.byte	3,3,1,5,1,9
	.half	.L57-.L56
	.byte	3,2,1,7,9
	.half	.L34-.L57
	.byte	0,1,1
.L89:
	.sdecl	'.debug_ranges',debug,cluster('Mcal_DmaEnableIntr')
	.sect	'.debug_ranges'
.L33:
	.word	-1,.L19,0,.L34-.L19,0,0
.L53:
	.word	-1,.L19,0,.L50-.L19,-1,.L21,0,.L49-.L21,0,0
	.sdecl	'.debug_info',debug,cluster('Mcal_DmaDisableIntr')
	.sect	'.debug_info'
.L35:
	.word	307
	.half	3
	.word	.L36
	.byte	4,1
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L38,.L37
	.byte	2
	.word	.L26
	.byte	3
	.byte	'Mcal_DmaDisableIntr',0,1,146,1,6,1,1,1
	.word	.L23,.L60,.L22
	.byte	4
	.byte	'Channel',0,1,146,1,42
	.word	.L51,.L61
	.byte	5
	.word	.L23,.L60
	.byte	6
	.byte	'lAddress',0,1,148,1,20
	.word	.L54,.L62
	.byte	5
	.word	.L63,.L2
	.byte	6
	.byte	'val',0,1,158,1,5
	.word	.L58,.L64
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Mcal_DmaDisableIntr')
	.sect	'.debug_abbrev'
.L36:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Mcal_DmaDisableIntr')
	.sect	'.debug_line'
.L37:
	.word	.L93-.L92
.L92:
	.half	3
	.word	.L95-.L94
.L94:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0,0,0,0,0
.L95:
	.byte	5,51,7,0,5,2
	.word	.L23
	.byte	3,151,1,1,5,3,9
	.half	.L78-.L23
	.byte	3,2,1,5,5,7,9
	.half	.L63-.L78
	.byte	3,4,1,5,1,9
	.half	.L2-.L63
	.byte	3,3,1,7,9
	.half	.L39-.L2
	.byte	0,1,1
.L93:
	.sdecl	'.debug_ranges',debug,cluster('Mcal_DmaDisableIntr')
	.sect	'.debug_ranges'
.L38:
	.word	-1,.L23,0,.L39-.L23,0,0
	.sdecl	'.debug_info',debug,cluster('Mcal_DmaCfgNoOfMovesPerTransfer')
	.sect	'.debug_info'
.L40:
	.word	409
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L43,.L42
	.byte	2
	.word	.L26
	.byte	3
	.byte	'Mcal_DmaCfgNoOfMovesPerTransfer',0,1,185,1,6,1,1,1
	.word	.L25,.L65,.L24
	.byte	4
	.byte	'Channel',0,1,185,1,54
	.word	.L51,.L66
	.byte	4
	.byte	'NoOfMoves',0,1,185,1,69
	.word	.L67,.L68
	.byte	5
	.word	.L25,.L65
	.byte	6
	.byte	'lAddress',0,1,187,1,20
	.word	.L54,.L69
	.byte	6
	.byte	'lSetMask',0,1,188,1,10
	.word	.L58,.L70
	.byte	6
	.byte	'lClearMask',0,1,188,1,19
	.word	.L58,.L71
	.byte	6
	.byte	'BlkmVal',0,1,189,1,9
	.word	.L67,.L72
	.byte	5
	.word	.L73,.L74
	.byte	6
	.byte	'val',0,1,250,1,3
	.word	.L58,.L75
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Mcal_DmaCfgNoOfMovesPerTransfer')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Mcal_DmaCfgNoOfMovesPerTransfer')
	.sect	'.debug_line'
.L42:
	.word	.L97-.L96
.L96:
	.half	3
	.word	.L99-.L98
.L98:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0,0,0,0,0
.L99:
	.byte	5,10,7,0,5,2
	.word	.L25
	.byte	3,198,1,1,7,9
	.half	.L100-.L25
	.byte	3,23,1,7,9
	.half	.L101-.L100
	.byte	3,111,1,7,9
	.half	.L102-.L101
	.byte	3,22,1,7,9
	.half	.L103-.L102
	.byte	3,112,1,9
	.half	.L104-.L103
	.byte	3,21,1,9
	.half	.L105-.L104
	.byte	3,113,1,5,15,9
	.half	.L3-.L105
	.byte	3,112,1,5,7,3,1,1,5,15,9
	.half	.L5-.L3
	.byte	3,5,1,5,7,3,1,1,5,15,9
	.half	.L7-.L5
	.byte	3,5,1,5,7,3,1,1,5,15,9
	.half	.L9-.L7
	.byte	3,5,1,5,7,3,1,1,5,15,9
	.half	.L4-.L9
	.byte	3,4,1,5,7,3,1,1,5,15,9
	.half	.L6-.L4
	.byte	3,4,1,5,7,3,1,1,5,15,9
	.half	.L8-.L6
	.byte	3,4,1,5,7,3,1,1,5,15,9
	.half	.L10-.L8
	.byte	3,4,1,5,14,9
	.half	.L11-.L10
	.byte	3,6,1,5,40,3,3,1,5,44,9
	.half	.L106-.L11
	.byte	1,5,40,1,5,43,9
	.half	.L80-.L106
	.byte	1,5,12,9
	.half	.L107-.L80
	.byte	3,123,1,5,14,9
	.half	.L82-.L107
	.byte	3,2,1,5,3,9
	.half	.L108-.L82
	.byte	3,5,1,5,1,9
	.half	.L74-.L108
	.byte	3,1,1,7,9
	.half	.L44-.L74
	.byte	0,1,1
.L97:
	.sdecl	'.debug_ranges',debug,cluster('Mcal_DmaCfgNoOfMovesPerTransfer')
	.sect	'.debug_ranges'
.L43:
	.word	-1,.L25,0,.L44-.L25,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L45:
	.word	214
	.half	3
	.word	.L46
	.byte	4,1
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L48,.L47
	.byte	2
	.word	.L26
	.byte	3
	.byte	'.cocofun_1',0,1,111,7,1
	.word	.L21,.L49,.L20
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L46:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L47:
	.word	.L110-.L109
.L109:
	.half	3
	.word	.L112-.L111
.L111:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.c',0,0,0,0,0
.L112:
	.byte	5,51,7,0,5,2
	.word	.L21
	.byte	3,244,0,1,5,66,9
	.half	.L113-.L21
	.byte	1,5,3,9
	.half	.L76-.L113
	.byte	3,3,1,9
	.half	.L49-.L76
	.byte	0,1,1,5,51,0,5,2
	.word	.L21
	.byte	3,151,1,1,5,66,9
	.half	.L113-.L21
	.byte	1,5,8,9
	.half	.L76-.L113
	.byte	3,2,1,5,3,9
	.half	.L114-.L76
	.byte	3,94,1,7,9
	.half	.L49-.L114
	.byte	0,1,1
.L110:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L48:
	.word	-1,.L21,0,.L49-.L21,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L20:
	.word	-1,.L21,0,.L49-.L21
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Mcal_DmaCfgNoOfMovesPerTransfer')
	.sect	'.debug_loc'
.L72:
	.word	-1,.L25,.L11-.L25,.L81-.L25
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L66:
	.word	-1,.L25,0,.L80-.L25
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L24:
	.word	-1,.L25,0,.L65-.L25
	.half	2
	.byte	138,0
	.word	0,0
.L68:
	.word	-1,.L25,0,.L65-.L25
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L69:
	.word	0,0
.L71:
	.word	-1,.L25,.L73-.L25,.L65-.L25
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L70:
	.word	-1,.L25,.L82-.L25,.L74-.L25
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L75:
	.word	-1,.L25,.L83-.L25,.L65-.L25
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Mcal_DmaDisableIntr')
	.sect	'.debug_loc'
.L61:
	.word	-1,.L23,.L21-.L23,.L49-.L23
	.half	5
	.byte	144,34,157,32,0
	.word	.L78-.L23,.L60-.L23
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L22:
	.word	-1,.L23,0,.L60-.L23
	.half	2
	.byte	138,0
	.word	0,0
.L62:
	.word	-1,.L23,.L76-.L23,.L49-.L23
	.half	1
	.byte	111
	.word	.L78-.L23,.L60-.L23
	.half	1
	.byte	111
	.word	0,0
.L64:
	.word	-1,.L23,.L79-.L23,.L2-.L23
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Mcal_DmaEnableIntr')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L19,.L21-.L19,.L49-.L19
	.half	5
	.byte	144,34,157,32,0
	.word	.L56-.L19,.L50-.L19
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L18:
	.word	-1,.L19,0,.L50-.L19
	.half	2
	.byte	138,0
	.word	0,0
.L55:
	.word	-1,.L19,.L76-.L19,.L49-.L19
	.half	1
	.byte	111
	.word	.L56-.L19,.L50-.L19
	.half	1
	.byte	111
	.word	0,0
.L59:
	.word	-1,.L19,.L77-.L19,.L50-.L19
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L115:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Mcal_DmaEnableIntr')
	.sect	'.debug_frame'
	.word	24
	.word	.L115,.L19,.L50-.L19
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Mcal_DmaDisableIntr')
	.sect	'.debug_frame'
	.word	24
	.word	.L115,.L23,.L60-.L23
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Mcal_DmaCfgNoOfMovesPerTransfer')
	.sect	'.debug_frame'
	.word	24
	.word	.L115,.L25,.L65-.L25
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L116:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L116,.L21,.L49-.L21
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Mcal_DmaLib.c	   252  
; ..\mcal_src\Mcal_DmaLib.c	   253  /*Memory Map of the Code*/
; ..\mcal_src\Mcal_DmaLib.c	   254  #define MCAL_DMALIB_STOP_SEC_CODE
; ..\mcal_src\Mcal_DmaLib.c	   255  /*
; ..\mcal_src\Mcal_DmaLib.c	   256    Allows to map variables, constants and code of modules to individual
; ..\mcal_src\Mcal_DmaLib.c	   257    memory sections.
; ..\mcal_src\Mcal_DmaLib.c	   258  */
; ..\mcal_src\Mcal_DmaLib.c	   259  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Mcal_DmaLib.c	   260   allowed only for MemMap.h*/
; ..\mcal_src\Mcal_DmaLib.c	   261  #include "MemMap.h"

	; Module end
