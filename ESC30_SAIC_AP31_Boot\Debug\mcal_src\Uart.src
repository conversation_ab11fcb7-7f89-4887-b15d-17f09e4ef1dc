	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc31160a -c99 --dep-file=mcal_src\\.Uart.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Uart.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Uart.src ..\\mcal_src\\Uart.c"
	.compiler_name		"ctc"
	.name	"Uart"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Uart_Init

; ..\mcal_src\Uart.c	     1  /******************************************************************************
; ..\mcal_src\Uart.c	     2  **                                                                           **
; ..\mcal_src\Uart.c	     3  ** Copyright (C) Infineon Technologies (2015)                                **
; ..\mcal_src\Uart.c	     4  **                                                                           **
; ..\mcal_src\Uart.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Uart.c	     6  **                                                                           **
; ..\mcal_src\Uart.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Uart.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Uart.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Uart.c	    10  **                                                                           **
; ..\mcal_src\Uart.c	    11  *******************************************************************************
; ..\mcal_src\Uart.c	    12  **                                                                           **
; ..\mcal_src\Uart.c	    13  **  $FILENAME   : Uart.c $                                                   **
; ..\mcal_src\Uart.c	    14  **                                                                           **
; ..\mcal_src\Uart.c	    15  **  $CC VERSION : \main\74 $                                                 **
; ..\mcal_src\Uart.c	    16  **                                                                           **
; ..\mcal_src\Uart.c	    17  **  $DATE       : 2016-08-01 $                                               **
; ..\mcal_src\Uart.c	    18  **                                                                           **
; ..\mcal_src\Uart.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Uart.c	    20  **                                                                           **
; ..\mcal_src\Uart.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Uart.c	    22  **                                                                           **
; ..\mcal_src\Uart.c	    23  **  DESCRIPTION : This file contains                                         **
; ..\mcal_src\Uart.c	    24  **                functionality of UART driver                               **
; ..\mcal_src\Uart.c	    25  **                                                                           **
; ..\mcal_src\Uart.c	    26  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Uart.c	    27  **                                                                           **
; ..\mcal_src\Uart.c	    28  ******************************************************************************/
; ..\mcal_src\Uart.c	    29  /*******************************************************************************
; ..\mcal_src\Uart.c	    30     Traceability   : [cover parentID=
; ..\mcal_src\Uart.c	    31     SAS_NAS_ALL_PR748,SAS_NAS_ALL_PR749,
; ..\mcal_src\Uart.c	    32     SAS_NAS_ALL_PR102,SAS_NAS_ALL_PR470,SAS_NAS_ALL_PR471,
; ..\mcal_src\Uart.c	    33     SAS_NAS_ALL_PR70,SAS_NAS_ALL_PR1652]
; ..\mcal_src\Uart.c	    34     [/cover]
; ..\mcal_src\Uart.c	    35  *******************************************************************************/
; ..\mcal_src\Uart.c	    36  /*******************************************************************************
; ..\mcal_src\Uart.c	    37  **                      Includes                                              **
; ..\mcal_src\Uart.c	    38  *******************************************************************************/
; ..\mcal_src\Uart.c	    39  /* Inclusion of sfr file */
; ..\mcal_src\Uart.c	    40  #include "IfxAsclin_reg.h"
; ..\mcal_src\Uart.c	    41  #include "IfxSrc_reg.h"
; ..\mcal_src\Uart.c	    42  
; ..\mcal_src\Uart.c	    43  /* Own header file, this includes own configuration file also */
; ..\mcal_src\Uart.c	    44  /* Inclusion structure */
; ..\mcal_src\Uart.c	    45  #include "Uart.h"
; ..\mcal_src\Uart.c	    46  
; ..\mcal_src\Uart.c	    47  /*******************************************************************************
; ..\mcal_src\Uart.c	    48  **                      Imported Compiler Switch Check                        **
; ..\mcal_src\Uart.c	    49  *******************************************************************************/
; ..\mcal_src\Uart.c	    50  /*************** SW Version Checks ***************/
; ..\mcal_src\Uart.c	    51  #ifndef UART_SW_MAJOR_VERSION
; ..\mcal_src\Uart.c	    52    #error "UART_SW_MAJOR_VERSION is not defined."
; ..\mcal_src\Uart.c	    53  #endif
; ..\mcal_src\Uart.c	    54  
; ..\mcal_src\Uart.c	    55  #ifndef UART_SW_MINOR_VERSION
; ..\mcal_src\Uart.c	    56    #error "UART_SW_MINOR_VERSION is not defined."
; ..\mcal_src\Uart.c	    57  #endif
; ..\mcal_src\Uart.c	    58  
; ..\mcal_src\Uart.c	    59  #ifndef UART_SW_PATCH_VERSION
; ..\mcal_src\Uart.c	    60    #error "UART_SW_PATCH_VERSION is not defined."
; ..\mcal_src\Uart.c	    61  #endif
; ..\mcal_src\Uart.c	    62  
; ..\mcal_src\Uart.c	    63  /* Check for Correct inclusion of headers */
; ..\mcal_src\Uart.c	    64  #if ( UART_SW_MAJOR_VERSION != 1U )
; ..\mcal_src\Uart.c	    65    #error "UART_SW_MAJOR_VERSION does not match."
; ..\mcal_src\Uart.c	    66  #endif
; ..\mcal_src\Uart.c	    67  #if ( UART_SW_MINOR_VERSION != 3U )
; ..\mcal_src\Uart.c	    68    #error "UART_SW_MINOR_VERSION does not match."
; ..\mcal_src\Uart.c	    69  #endif
; ..\mcal_src\Uart.c	    70  
; ..\mcal_src\Uart.c	    71  /*******************************************************************************
; ..\mcal_src\Uart.c	    72  **                      Private Macro Definitions                             **
; ..\mcal_src\Uart.c	    73  *******************************************************************************/
; ..\mcal_src\Uart.c	    74  /* ASCLIN Register Start Address Mapping */
; ..\mcal_src\Uart.c	    75  #define UART_HW_MODULE                    (&MODULE_ASCLIN0)
; ..\mcal_src\Uart.c	    76  
; ..\mcal_src\Uart.c	    77  #define UART_CSRREG_CLKSEL_NOCLK          (0U)  /* No Clock  */
; ..\mcal_src\Uart.c	    78  #define UART_FRAMECONREG_INIT_MODE        (0U)  /* ASCLIN INIT mode */
; ..\mcal_src\Uart.c	    79  #define UART_FRAMECONREG_ASC_MODE         (0x00010000U)  /* ASCLIN UART mode */
; ..\mcal_src\Uart.c	    80  #define UART_CSRREG_CLKSEL_CLC            (8U)  /* Clk source = fASCLINF */
; ..\mcal_src\Uart.c	    81  
; ..\mcal_src\Uart.c	    82  /* Digital Glitch Filter Disabled */
; ..\mcal_src\Uart.c	    83  #define UART_IOCRREG_DEPTH_VAL            (0U)
; ..\mcal_src\Uart.c	    84  
; ..\mcal_src\Uart.c	    85  #define UART_BITCONREG_SM_VAL             (1U)
; ..\mcal_src\Uart.c	    86  
; ..\mcal_src\Uart.c	    87  #define UART_ENABLE_BIT                   (1U)
; ..\mcal_src\Uart.c	    88  #define UART_DISABLE_BIT                  (0U)
; ..\mcal_src\Uart.c	    89  
; ..\mcal_src\Uart.c	    90  #define UART_INVALID_CHANNEL              (0xFFU)
; ..\mcal_src\Uart.c	    91  
; ..\mcal_src\Uart.c	    92  /* Configure Inlet width and outlet width for FIFO
; ..\mcal_src\Uart.c	    93  depending on the Datalength configured for the Channels */
; ..\mcal_src\Uart.c	    94  
; ..\mcal_src\Uart.c	    95  #if (UART_NINEBITS_USED == STD_ON)
; ..\mcal_src\Uart.c	    96  #define UART_TXFIFOCONREG_INW_VAL         (2U)
; ..\mcal_src\Uart.c	    97  #define UART_RXFIFOCONREG_OUTW_VAL        (2U)
; ..\mcal_src\Uart.c	    98  #define UART_STEPSIZE                     (2U)
; ..\mcal_src\Uart.c	    99  #else
; ..\mcal_src\Uart.c	   100  #define UART_TXFIFOCONREG_INW_VAL         (1U)
; ..\mcal_src\Uart.c	   101  #define UART_RXFIFOCONREG_OUTW_VAL        (1U)
; ..\mcal_src\Uart.c	   102  #define UART_STEPSIZE                     (1U)
; ..\mcal_src\Uart.c	   103  #endif
; ..\mcal_src\Uart.c	   104  
; ..\mcal_src\Uart.c	   105  #define UART_ZERO_U                       (0U)
; ..\mcal_src\Uart.c	   106  #define UART_ONE_U                        (1U)
; ..\mcal_src\Uart.c	   107  
; ..\mcal_src\Uart.c	   108  #define UART_BIT_RESET                    (0U)
; ..\mcal_src\Uart.c	   109  #define UART_BIT_SET                      (1U)
; ..\mcal_src\Uart.c	   110  
; ..\mcal_src\Uart.c	   111  #define UART_TX_LOCK_IDX                  (0U)
; ..\mcal_src\Uart.c	   112  #define UART_RX_LOCK_IDX                  (1U)
; ..\mcal_src\Uart.c	   113  
; ..\mcal_src\Uart.c	   114  #define UART_NINEBIT_DATLEN               (9U)
; ..\mcal_src\Uart.c	   115  
; ..\mcal_src\Uart.c	   116  /*Rx FIFO Buffer Int level is set to Store 16 bytes*/
; ..\mcal_src\Uart.c	   117  #define UART_RX_FIFO_INT_LEVEL_VAL        (15U)
; ..\mcal_src\Uart.c	   118  
; ..\mcal_src\Uart.c	   119  /* SRC register for ASCLIN are offset by a below value  0x0C div 4 */
; ..\mcal_src\Uart.c	   120  #define UART_SRC_ADDROFFSET               (0x03U)
; ..\mcal_src\Uart.c	   121  
; ..\mcal_src\Uart.c	   122  /* Disable ASCLIN Module clock */
; ..\mcal_src\Uart.c	   123  #define UART_DISABLE_ASCLIN_MODULE        (0x00000001U)
; ..\mcal_src\Uart.c	   124  
; ..\mcal_src\Uart.c	   125  #define UART_TIMEOUT_DURATION             (0xFFU)
; ..\mcal_src\Uart.c	   126  
; ..\mcal_src\Uart.c	   127  #define UART_KRST_TIMEOUT                 (0x100U)
; ..\mcal_src\Uart.c	   128  
; ..\mcal_src\Uart.c	   129  /* Set and Clear Mask to Disable Interrupts */
; ..\mcal_src\Uart.c	   130  #define UART_SET_MASK_DISABLE_INTR        (0x02000000U)
; ..\mcal_src\Uart.c	   131  #define UART_CLR_MASK_DISABLE_INTR        (0x80000400U)
; ..\mcal_src\Uart.c	   132  
; ..\mcal_src\Uart.c	   133  /* Set and Clear Mask to Clear Interrupts */
; ..\mcal_src\Uart.c	   134  #define UART_SET_MASK_CLR_INTR            (0x52000000U)
; ..\mcal_src\Uart.c	   135  #define UART_CLR_MASK_CLR_INTR            (0x80000000U)
; ..\mcal_src\Uart.c	   136  
; ..\mcal_src\Uart.c	   137  /* Set and Clear Mask to Enable Interrupts */
; ..\mcal_src\Uart.c	   138  #define UART_SET_MASK_ENABLE_INTR         (0x00000400U)
; ..\mcal_src\Uart.c	   139  #define UART_CLR_MASK_ENABLE_INTR         (0x80000000U)
; ..\mcal_src\Uart.c	   140  
; ..\mcal_src\Uart.c	   141  /* SRC register addresses */
; ..\mcal_src\Uart.c	   142  #define UART_SRC_ASCLIN0TXADDR  ((volatile Ifx_SRC_SRCR_Bits  *) \ 
; ..\mcal_src\Uart.c	   143  (void *)&(SRC_ASCLIN0TX))
; ..\mcal_src\Uart.c	   144  #define UART_SRC_ASCLIN0RXADDR  ((volatile Ifx_SRC_SRCR_Bits  *) \ 
; ..\mcal_src\Uart.c	   145  (void *)&(SRC_ASCLIN0RX))
; ..\mcal_src\Uart.c	   146  #define UART_SRC_ASCLIN0ERRADDR ((volatile Ifx_SRC_SRCR_Bits  *) \ 
; ..\mcal_src\Uart.c	   147  (void *)&(SRC_ASCLIN0ERR))
; ..\mcal_src\Uart.c	   148  
; ..\mcal_src\Uart.c	   149  /*******************************************************************************
; ..\mcal_src\Uart.c	   150  **                      Private Type Definitions                              **
; ..\mcal_src\Uart.c	   151  *******************************************************************************/
; ..\mcal_src\Uart.c	   152  
; ..\mcal_src\Uart.c	   153  /*******************************************************************************
; ..\mcal_src\Uart.c	   154  *                     Private Function Declarations                            *
; ..\mcal_src\Uart.c	   155  *******************************************************************************/
; ..\mcal_src\Uart.c	   156  /*Memory Map of the UART Code*/
; ..\mcal_src\Uart.c	   157  #define UART_START_SEC_CODE
; ..\mcal_src\Uart.c	   158  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   159   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   160  #include "MemMap.h"
; ..\mcal_src\Uart.c	   161  
; ..\mcal_src\Uart.c	   162  IFX_LOCAL_INLINE uint8 Uart_lHwInitClcReg(uint8 HwUnit, uint32 Value);
; ..\mcal_src\Uart.c	   163  
; ..\mcal_src\Uart.c	   164  #if ((UART_RESET_SFR_INIT == STD_ON) || (UART_DEINIT_API == STD_ON))
; ..\mcal_src\Uart.c	   165  IFX_LOCAL_INLINE void Uart_lHwInitKernelRegInit(uint8 HwUnit);
; ..\mcal_src\Uart.c	   166  IFX_LOCAL_INLINE void Uart_lHwInitKernelRegDeInit(uint8 HwUnit);
; ..\mcal_src\Uart.c	   167  #endif
; ..\mcal_src\Uart.c	   168  
; ..\mcal_src\Uart.c	   169  static void Uart_lHwInit(uint8 HwUnit,\ 
; ..\mcal_src\Uart.c	   170                                       const Uart_ChannelType* ChannelConfigPtr);
; ..\mcal_src\Uart.c	   171  #if ((UART_ABORT_READ_API == STD_ON) || (UART_ABORT_WRITE_API == STD_ON))
; ..\mcal_src\Uart.c	   172  static void Uart_lHwInitRuntime(uint8 HwUnit,\ 
; ..\mcal_src\Uart.c	   173                                        const Uart_ChannelType* ChannelConfigPtr);
; ..\mcal_src\Uart.c	   174  #endif
; ..\mcal_src\Uart.c	   175  static void Uart_lRead(Ifx_ASCLIN* HwModulePtr,Uart_ChannelIdType Channel);
; ..\mcal_src\Uart.c	   176  static void Uart_lWrite(Ifx_ASCLIN* HwModulePtr,Uart_ChannelIdType Channel);
; ..\mcal_src\Uart.c	   177  
; ..\mcal_src\Uart.c	   178  static void Uart_lEnableReadInterrupts(Ifx_ASCLIN* HwModulePtr);
; ..\mcal_src\Uart.c	   179  static void Uart_lEnableWriteInterrupts(Ifx_ASCLIN* HwModulePtr,\ 
; ..\mcal_src\Uart.c	   180                                                               uint8 ApiAccessId);
; ..\mcal_src\Uart.c	   181  
; ..\mcal_src\Uart.c	   182  /* Functions to clear flags for read and write functions */
; ..\mcal_src\Uart.c	   183  static void Uart_lClearWriteInterrupts(Ifx_ASCLIN* HwModulePtr,\ 
; ..\mcal_src\Uart.c	   184                                                               uint8 ApiAccessId);
; ..\mcal_src\Uart.c	   185  static void Uart_lClearReadInterrupts(Ifx_ASCLIN* HwModulePtr,\ 
; ..\mcal_src\Uart.c	   186                                                               uint8 ApiAccessId);
; ..\mcal_src\Uart.c	   187  
; ..\mcal_src\Uart.c	   188  #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	   189  static Uart_ReturnType Uart_lChannelCheck(\ 
; ..\mcal_src\Uart.c	   190                               Uart_ChannelIdType Channel,uint8 ApiId);
; ..\mcal_src\Uart.c	   191  #endif
; ..\mcal_src\Uart.c	   192  
; ..\mcal_src\Uart.c	   193  /* Functions to enable SRE bit of corresponding hardware*/
; ..\mcal_src\Uart.c	   194  static void  Uart_lHwEnableAscLinTxIntr(volatile uint8 HwUnit);
; ..\mcal_src\Uart.c	   195  static void  Uart_lHwEnableAscLinRxIntr(volatile uint8 HwUnit);
; ..\mcal_src\Uart.c	   196  static void  Uart_lHwEnableAscLinErrIntr(volatile uint8 HwUnit);
; ..\mcal_src\Uart.c	   197  
; ..\mcal_src\Uart.c	   198  /* Functions to disable SRE bit of curresponding hardware*/
; ..\mcal_src\Uart.c	   199  static void  Uart_lHwDisableAscLinTxIntr(volatile uint8 HwUnit, \ 
; ..\mcal_src\Uart.c	   200                                                               uint8 ApiAccessId);
; ..\mcal_src\Uart.c	   201  static void  Uart_lHwDisableAscLinRxIntr(volatile uint8 HwUnit, \ 
; ..\mcal_src\Uart.c	   202                                                               uint8 ApiAccessId);
; ..\mcal_src\Uart.c	   203  static void  Uart_lHwDisableAscLinErrIntr(volatile uint8 HwUnit, \ 
; ..\mcal_src\Uart.c	   204                                                               uint8 ApiAccessId);
; ..\mcal_src\Uart.c	   205  
; ..\mcal_src\Uart.c	   206  #define UART_STOP_SEC_CODE
; ..\mcal_src\Uart.c	   207  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   208   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   209  #include "MemMap.h"
; ..\mcal_src\Uart.c	   210  
; ..\mcal_src\Uart.c	   211  /*******************************************************************************
; ..\mcal_src\Uart.c	   212  **                      Global Constant Definitions                           **
; ..\mcal_src\Uart.c	   213  *******************************************************************************/
; ..\mcal_src\Uart.c	   214  #if (UART_PB_FIXEDADDR == STD_ON)
; ..\mcal_src\Uart.c	   215  
; ..\mcal_src\Uart.c	   216  #define UART_START_SEC_CONST_32BIT
; ..\mcal_src\Uart.c	   217  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   218   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   219  #include "MemMap.h"
; ..\mcal_src\Uart.c	   220  
; ..\mcal_src\Uart.c	   221  /* UART Config Set Pointer */
; ..\mcal_src\Uart.c	   222  static const Uart_ConfigType* const Uart_kConfigPtr = &Uart_ConfigRoot[0];
; ..\mcal_src\Uart.c	   223  
; ..\mcal_src\Uart.c	   224  #define UART_STOP_SEC_CONST_32BIT
; ..\mcal_src\Uart.c	   225  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   226   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   227  #include "MemMap.h"
; ..\mcal_src\Uart.c	   228  
; ..\mcal_src\Uart.c	   229  #endif /*(UART_PB_FIXEDADDR == STD_ON)*/
; ..\mcal_src\Uart.c	   230  
; ..\mcal_src\Uart.c	   231  
; ..\mcal_src\Uart.c	   232  #if (UART_PB_FIXEDADDR == STD_OFF)
; ..\mcal_src\Uart.c	   233  
; ..\mcal_src\Uart.c	   234  #define UART_START_SEC_VAR_32BIT
; ..\mcal_src\Uart.c	   235  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   236   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   237  #include "MemMap.h"
; ..\mcal_src\Uart.c	   238  
; ..\mcal_src\Uart.c	   239  static const Uart_ConfigType* Uart_kConfigPtr;
; ..\mcal_src\Uart.c	   240  
; ..\mcal_src\Uart.c	   241  #define UART_STOP_SEC_VAR_32BIT
; ..\mcal_src\Uart.c	   242  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   243   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   244  #include "MemMap.h"
; ..\mcal_src\Uart.c	   245  
; ..\mcal_src\Uart.c	   246  #endif /*(UART_PB_FIXEDADDR == OFF)*/
; ..\mcal_src\Uart.c	   247  
; ..\mcal_src\Uart.c	   248  /*******************************************************************************
; ..\mcal_src\Uart.c	   249  **                      Global Variable Definitions                           **
; ..\mcal_src\Uart.c	   250  *******************************************************************************/
; ..\mcal_src\Uart.c	   251  
; ..\mcal_src\Uart.c	   252  /*[cover parentID=DS_NAS_UART_PR115]Global and Static variable[/cover]*/
; ..\mcal_src\Uart.c	   253  
; ..\mcal_src\Uart.c	   254  #define UART_START_SEC_VAR_INIT_8BIT
; ..\mcal_src\Uart.c	   255  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   256   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   257  #include "MemMap.h"
; ..\mcal_src\Uart.c	   258  
; ..\mcal_src\Uart.c	   259  /* Mapping of ASCLIN HW Units Used to the respective logical UART Channel Ids*/
; ..\mcal_src\Uart.c	   260  static Uart_ChannelIdType Uart_BusChannelMap[UART_MAX_HW_UNIT]=\ 
; ..\mcal_src\Uart.c	   261  {UART_INVALID_CHANNEL};
; ..\mcal_src\Uart.c	   262  
; ..\mcal_src\Uart.c	   263  /* Variable used to check Uart Initialization Status */
; ..\mcal_src\Uart.c	   264  #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	   265  static Uart_StateType Uart_InitStatus = UART_UNINITIALISED;
; ..\mcal_src\Uart.c	   266  #endif
; ..\mcal_src\Uart.c	   267  
; ..\mcal_src\Uart.c	   268  #define UART_STOP_SEC_VAR_INIT_8BIT
; ..\mcal_src\Uart.c	   269  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   270   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   271  #include "MemMap.h"
; ..\mcal_src\Uart.c	   272  
; ..\mcal_src\Uart.c	   273  
; ..\mcal_src\Uart.c	   274  #define UART_START_SEC_VAR_UNSPECIFIED
; ..\mcal_src\Uart.c	   275  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   276   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   277  #include "MemMap.h"
; ..\mcal_src\Uart.c	   278  
; ..\mcal_src\Uart.c	   279  #if (UART_DEBUG_SUPPORT == STD_ON)
; ..\mcal_src\Uart.c	   280  /*IFX_MISRA_RULE_08_10_STATUS=Variable Uart_ChannelInfo is declared as
; ..\mcal_src\Uart.c	   281   extern in Uart_Dbg.h, which will be included application for
; ..\mcal_src\Uart.c	   282   debugging*/
; ..\mcal_src\Uart.c	   283  Uart_ChannelInfoType  Uart_ChannelInfo[UART_MAXIMUM_CHANNEL];
; ..\mcal_src\Uart.c	   284  #else
; ..\mcal_src\Uart.c	   285  static Uart_ChannelInfoType Uart_ChannelInfo[UART_MAXIMUM_CHANNEL];
; ..\mcal_src\Uart.c	   286  #endif
; ..\mcal_src\Uart.c	   287  
; ..\mcal_src\Uart.c	   288  #define UART_STOP_SEC_VAR_UNSPECIFIED
; ..\mcal_src\Uart.c	   289  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   290   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   291  #include "MemMap.h"
; ..\mcal_src\Uart.c	   292  
; ..\mcal_src\Uart.c	   293  #define UART_START_SEC_VAR_32BIT
; ..\mcal_src\Uart.c	   294  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   295   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   296  #include "MemMap.h"
; ..\mcal_src\Uart.c	   297  
; ..\mcal_src\Uart.c	   298  /*Resource Protection Flags for UART Channel Resource(using binary semaphore)*/
; ..\mcal_src\Uart.c	   299  static uint32 Uart_ChLock[2][UART_MAXIMUM_CHANNEL];
; ..\mcal_src\Uart.c	   300  
; ..\mcal_src\Uart.c	   301  #ifdef IFX_UART_DEBUG
; ..\mcal_src\Uart.c	   302  extern volatile uint32 TestUart_DebugMask01;
; ..\mcal_src\Uart.c	   303  extern volatile uint32 TestUart_DebugMask02;
; ..\mcal_src\Uart.c	   304  #endif
; ..\mcal_src\Uart.c	   305  
; ..\mcal_src\Uart.c	   306  #define UART_STOP_SEC_VAR_32BIT
; ..\mcal_src\Uart.c	   307  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   308   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   309  #include "MemMap.h"
; ..\mcal_src\Uart.c	   310  
; ..\mcal_src\Uart.c	   311  /*******************************************************************************
; ..\mcal_src\Uart.c	   312  **                      Private Constant Definitions                          **
; ..\mcal_src\Uart.c	   313  *******************************************************************************/
; ..\mcal_src\Uart.c	   314  
; ..\mcal_src\Uart.c	   315  /*******************************************************************************
; ..\mcal_src\Uart.c	   316  **                      Private Variable Definitions                          **
; ..\mcal_src\Uart.c	   317  *******************************************************************************/
; ..\mcal_src\Uart.c	   318  
; ..\mcal_src\Uart.c	   319  /*******************************************************************************
; ..\mcal_src\Uart.c	   320  **                      Global Function Definitions                           **
; ..\mcal_src\Uart.c	   321  *******************************************************************************/
; ..\mcal_src\Uart.c	   322  
; ..\mcal_src\Uart.c	   323  /*Memory Map of the UART Code*/
; ..\mcal_src\Uart.c	   324  #define UART_START_SEC_CODE
; ..\mcal_src\Uart.c	   325  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	   326   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	   327  #include "MemMap.h"
; ..\mcal_src\Uart.c	   328  
; ..\mcal_src\Uart.c	   329  
; ..\mcal_src\Uart.c	   330  /*******************************************************************************
; ..\mcal_src\Uart.c	   331  ** Traceability : [cover parentID=DS_NAS_UART_PR1569,DS_NAS_HE2_UART_PR3018,  **
; ..\mcal_src\Uart.c	   332  **                DS_NAS_EP_UART_PR3018,DS_NAS_UART_PR63_6][/cover]           **
; ..\mcal_src\Uart.c	   333  **                                                                            **
; ..\mcal_src\Uart.c	   334  ** Syntax : void Uart_Init( const Uart_ConfigType* ConfigPtr )                **
; ..\mcal_src\Uart.c	   335  **    [/cover]                                                                **
; ..\mcal_src\Uart.c	   336  ** Service ID:  0x00                                                          **
; ..\mcal_src\Uart.c	   337  **                                                                            **
; ..\mcal_src\Uart.c	   338  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	   339  **                                                                            **
; ..\mcal_src\Uart.c	   340  ** Reentrancy:  Non reentrant                                                 **
; ..\mcal_src\Uart.c	   341  **                                                                            **
; ..\mcal_src\Uart.c	   342  ** Parameters (in) :  ConfigPtr - Pointer to Uart driver configuration set    **
; ..\mcal_src\Uart.c	   343  **                                                                            **
; ..\mcal_src\Uart.c	   344  ** Parameters (out):  None                                                    **
; ..\mcal_src\Uart.c	   345  **                                                                            **
; ..\mcal_src\Uart.c	   346  ** Return value    :  None                                                    **
; ..\mcal_src\Uart.c	   347  **                                                                            **
; ..\mcal_src\Uart.c	   348  ** Description : Driver Module Initialization function.                       **
; ..\mcal_src\Uart.c	   349  ** Service for UART initialization. The Initialization function shall         **
; ..\mcal_src\Uart.c	   350  ** initialize all common relevant registers of UART channels with the values  **
; ..\mcal_src\Uart.c	   351  ** of the structure referenced by the parameter ConfigPtr.                    **
; ..\mcal_src\Uart.c	   352  *******************************************************************************/
; ..\mcal_src\Uart.c	   353  void Uart_Init(const Uart_ConfigType* ConfigPtr)
; Function Uart_Init
.L81:
Uart_Init:	.type	func

; ..\mcal_src\Uart.c	   354  {
; ..\mcal_src\Uart.c	   355    const Uart_ChannelType *ChannelConfigPtr;
; ..\mcal_src\Uart.c	   356    uint8 ModuleNo,Chan;
; ..\mcal_src\Uart.c	   357    uint8 MaxChannel,ClkDisableChk,ClkFailureChk ;
; ..\mcal_src\Uart.c	   358  
; ..\mcal_src\Uart.c	   359    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	   360    Std_ReturnType ReturnStatus;
; ..\mcal_src\Uart.c	   361    ReturnStatus = E_OK;
; ..\mcal_src\Uart.c	   362    #endif
; ..\mcal_src\Uart.c	   363  
; ..\mcal_src\Uart.c	   364    #if (UART_DEV_ERROR_DETECT == STD_ON) /* if DET detection is switched On */
; ..\mcal_src\Uart.c	   365     #if (UART_PB_FIXEDADDR == STD_OFF)
; ..\mcal_src\Uart.c	   366     if (ConfigPtr == NULL_PTR)
; ..\mcal_src\Uart.c	   367     {
; ..\mcal_src\Uart.c	   368       /* If the config pointer is equal to null pointer report to DET */
; ..\mcal_src\Uart.c	   369       Det_ReportError(
; ..\mcal_src\Uart.c	   370                       UART_MODULE_ID,
; ..\mcal_src\Uart.c	   371                       UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	   372                       UART_SID_INIT,
; ..\mcal_src\Uart.c	   373                       UART_E_PARAM_POINTER
; ..\mcal_src\Uart.c	   374                      );
; ..\mcal_src\Uart.c	   375       ReturnStatus = E_NOT_OK;
; ..\mcal_src\Uart.c	   376     }
; ..\mcal_src\Uart.c	   377     #else
; ..\mcal_src\Uart.c	   378     if (Uart_kConfigPtr != ConfigPtr)
; ..\mcal_src\Uart.c	   379     {
; ..\mcal_src\Uart.c	   380       /* In case of PB Fixed address if the Uart_kConfigPtr not equal to
; ..\mcal_src\Uart.c	   381       ConfigPtr then report to DET */
; ..\mcal_src\Uart.c	   382       Det_ReportError(
; ..\mcal_src\Uart.c	   383                       UART_MODULE_ID,
; ..\mcal_src\Uart.c	   384                       UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	   385                       UART_SID_INIT,
; ..\mcal_src\Uart.c	   386                       UART_E_INVALID_POINTER
; ..\mcal_src\Uart.c	   387                      );
; ..\mcal_src\Uart.c	   388       ReturnStatus = E_NOT_OK;
; ..\mcal_src\Uart.c	   389     }
; ..\mcal_src\Uart.c	   390     #endif/*(UART_PB_FIXEDADDR == STD_ON)*/
; ..\mcal_src\Uart.c	   391  
; ..\mcal_src\Uart.c	   392     else if (Uart_InitStatus == UART_INITIALISED)
; ..\mcal_src\Uart.c	   393     {
; ..\mcal_src\Uart.c	   394       /* If Uart is already initialsed then report to DET */
; ..\mcal_src\Uart.c	   395       Det_ReportError(
; ..\mcal_src\Uart.c	   396                       UART_MODULE_ID,
; ..\mcal_src\Uart.c	   397                       UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	   398                       UART_SID_INIT,
; ..\mcal_src\Uart.c	   399                       UART_E_STATE_TRANSITION
; ..\mcal_src\Uart.c	   400                      );
; ..\mcal_src\Uart.c	   401       ReturnStatus = E_NOT_OK;
; ..\mcal_src\Uart.c	   402     }
; ..\mcal_src\Uart.c	   403     else
; ..\mcal_src\Uart.c	   404     {
; ..\mcal_src\Uart.c	   405       /* Do Nothing */
; ..\mcal_src\Uart.c	   406     }
; ..\mcal_src\Uart.c	   407    #endif  /* (UART_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Uart.c	   408  
; ..\mcal_src\Uart.c	   409    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	   410    if (ReturnStatus == E_OK)
; ..\mcal_src\Uart.c	   411    #endif
; ..\mcal_src\Uart.c	   412    {
; ..\mcal_src\Uart.c	   413      MaxChannel = ConfigPtr->NoOfChannels;
; ..\mcal_src\Uart.c	   414  
; ..\mcal_src\Uart.c	   415      ClkFailureChk = UART_ZERO_U;
	mov	d9,#0
	sub.a	a10,#8
.L611:

; ..\mcal_src\Uart.c	   416  
; ..\mcal_src\Uart.c	   417      /* Enable the ASCLIN hardware unit/s for all configured Channels */
; ..\mcal_src\Uart.c	   418      for (Chan = UART_ZERO_U; Chan < MaxChannel; Chan++)
	mov	d10,d9
	st.a	[a10],a4
.L615:
	ld.bu	d8,[a4]4
.L619:

; ..\mcal_src\Uart.c	   419      {
; ..\mcal_src\Uart.c	   420        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	   421         ChannelConfigPtr to access the Configuration of a particular Channel*/
; ..\mcal_src\Uart.c	   422        ChannelConfigPtr = &(ConfigPtr->ChannelConfigPtr[Chan]);
; ..\mcal_src\Uart.c	   423        /* Extract the HW Module */
; ..\mcal_src\Uart.c	   424        ModuleNo = ChannelConfigPtr->HwModule;
; ..\mcal_src\Uart.c	   425  
; ..\mcal_src\Uart.c	   426        /* Enable the ASCLIN module and also set the EDIS bit (Sleep setting) */
; ..\mcal_src\Uart.c	   427        ClkDisableChk = Uart_lHwInitClcReg(ModuleNo, UART_ASCLIN_CLC);
; ..\mcal_src\Uart.c	   428  
; ..\mcal_src\Uart.c	   429        if (UART_ZERO_U == ClkDisableChk)
; ..\mcal_src\Uart.c	   430        {
; ..\mcal_src\Uart.c	   431          #if (UART_RESET_SFR_INIT == STD_ON)
; ..\mcal_src\Uart.c	   432          /* Reset ASCLIN Kernel  */
; ..\mcal_src\Uart.c	   433          Uart_lHwInitKernelRegInit(ModuleNo);
; ..\mcal_src\Uart.c	   434          #endif
; ..\mcal_src\Uart.c	   435  
; ..\mcal_src\Uart.c	   436          /* Initialise the Uart Hardware. */
; ..\mcal_src\Uart.c	   437          Uart_lHwInit(ModuleNo, ChannelConfigPtr);
; ..\mcal_src\Uart.c	   438  
; ..\mcal_src\Uart.c	   439        /* Initialize the ChannelInfo containig UART Channel to HW Unit Mapping*/
; ..\mcal_src\Uart.c	   440          Uart_ChannelInfo[Chan].Uart_AssignedHW = ModuleNo;
	fcall	.cocofun_13
.L776:

; ..\mcal_src\Uart.c	   441  
; ..\mcal_src\Uart.c	   442          /* Init Uart_BusChannelMap Array */
; ..\mcal_src\Uart.c	   443          Uart_BusChannelMap[ModuleNo] = Chan;
; ..\mcal_src\Uart.c	   444  
; ..\mcal_src\Uart.c	   445          /* Init UART Channel Tx and Rx State variables */
; ..\mcal_src\Uart.c	   446          Uart_ChannelInfo[Chan].Uart_TxState = UART_INITIALISED;
; ..\mcal_src\Uart.c	   447          Uart_ChannelInfo[Chan].Uart_RxState = UART_INITIALISED;
; ..\mcal_src\Uart.c	   448  
; ..\mcal_src\Uart.c	   449          /* Init Uart Channel Tx/Rx Resource Lock */
; ..\mcal_src\Uart.c	   450          Uart_ChLock[UART_TX_LOCK_IDX][Chan] = UART_ZERO_U;
	movh.a	a12,#@his(Uart_ChLock)
	lea	a12,[a12]@los(Uart_ChLock)
.L777:
	j	.L2
.L3:
	ld.a	a2,[a10]
.L612:
	ld.a	a2,[a2]
.L778:
	sha	d15,d10,#5
.L779:
	addsc.a	a13,a2,d15,#0
.L617:
	ld.bu	d11,[a13]23
.L370:

; ..\mcal_src\Uart.c	   451          Uart_ChLock[UART_RX_LOCK_IDX][Chan] = UART_ZERO_U;
; ..\mcal_src\Uart.c	   452        }
; ..\mcal_src\Uart.c	   453        else
; ..\mcal_src\Uart.c	   454        {
; ..\mcal_src\Uart.c	   455          ClkFailureChk++;
; ..\mcal_src\Uart.c	   456  
; ..\mcal_src\Uart.c	   457        /* Set Uart_AssignedHW to Invalid Channel Id */
; ..\mcal_src\Uart.c	   458          Uart_ChannelInfo[Chan].Uart_AssignedHW = UART_INVALID_CHANNEL;
; ..\mcal_src\Uart.c	   459  
; ..\mcal_src\Uart.c	   460          /* Set Uart_BusChannelMap as Invalid Channel Id */
; ..\mcal_src\Uart.c	   461          Uart_BusChannelMap[ModuleNo] = UART_INVALID_CHANNEL;
; ..\mcal_src\Uart.c	   462  
; ..\mcal_src\Uart.c	   463         /* Set UART Channel Tx and Rx State as Uninitialised*/
; ..\mcal_src\Uart.c	   464          Uart_ChannelInfo[Chan].Uart_TxState = UART_UNINITIALISED;
; ..\mcal_src\Uart.c	   465          Uart_ChannelInfo[Chan].Uart_RxState = UART_UNINITIALISED;
; ..\mcal_src\Uart.c	   466        }
; ..\mcal_src\Uart.c	   467      }
; ..\mcal_src\Uart.c	   468  
; ..\mcal_src\Uart.c	   469      if(UART_ZERO_U == ClkFailureChk)
; ..\mcal_src\Uart.c	   470      {
; ..\mcal_src\Uart.c	   471        /* Store ConfigPtr for use by APIs*/
; ..\mcal_src\Uart.c	   472        #if (UART_PB_FIXEDADDR == STD_OFF)
; ..\mcal_src\Uart.c	   473        Uart_kConfigPtr = ConfigPtr;
; ..\mcal_src\Uart.c	   474        #endif    /*(UART_PB_FIXEDADDR == OFF)*/
; ..\mcal_src\Uart.c	   475  
; ..\mcal_src\Uart.c	   476        #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	   477         /* Store UART driver initailization status */
; ..\mcal_src\Uart.c	   478        Uart_InitStatus = UART_INITIALISED;
; ..\mcal_src\Uart.c	   479        #endif/*(UART_DEV_ERROR_DETECT == STD_ON)*/
; ..\mcal_src\Uart.c	   480      }
; ..\mcal_src\Uart.c	   481      else
; ..\mcal_src\Uart.c	   482      {
; ..\mcal_src\Uart.c	   483        #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	   484         /* Store UART driver Status */
; ..\mcal_src\Uart.c	   485        Uart_InitStatus = UART_UNINITIALISED;
; ..\mcal_src\Uart.c	   486        #endif/*(UART_DEV_ERROR_DETECT == STD_ON)*/
; ..\mcal_src\Uart.c	   487      }
; ..\mcal_src\Uart.c	   488    }
; ..\mcal_src\Uart.c	   489    return ;
; ..\mcal_src\Uart.c	   490  }
; ..\mcal_src\Uart.c	   491  
; ..\mcal_src\Uart.c	   492  #if ((UART_RESET_SFR_INIT == STD_ON) || (UART_DEINIT_API == STD_ON))
; ..\mcal_src\Uart.c	   493  /*******************************************************************************
; ..\mcal_src\Uart.c	   494  ** Syntax           : IFX_LOCAL_INLINE void Uart_lHwInitKernelRegInit         **
; ..\mcal_src\Uart.c	   495  **                      (uint8 HwUnit)                                        **
; ..\mcal_src\Uart.c	   496  **                                                                            **
; ..\mcal_src\Uart.c	   497  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	   498  **                                                                            **
; ..\mcal_src\Uart.c	   499  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	   500  **                                                                            **
; ..\mcal_src\Uart.c	   501  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	   502  **                                                                            **
; ..\mcal_src\Uart.c	   503  ** Parameters (in)  : HwUnit   : ASCLIN Hardware module no                    **
; ..\mcal_src\Uart.c	   504  **                                                                            **
; ..\mcal_src\Uart.c	   505  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	   506  **                                                                            **
; ..\mcal_src\Uart.c	   507  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	   508  **                                                                            **
; ..\mcal_src\Uart.c	   509  ** Description      : This function resets the Kernel                         **
; ..\mcal_src\Uart.c	   510  *******************************************************************************/
; ..\mcal_src\Uart.c	   511  IFX_LOCAL_INLINE void Uart_lHwInitKernelRegInit(uint8 HwUnit)
; ..\mcal_src\Uart.c	   512  {
; ..\mcal_src\Uart.c	   513    volatile uint32 Readback;
; ..\mcal_src\Uart.c	   514    uint32 WaitCount,RstStatus;
; ..\mcal_src\Uart.c	   515    WaitCount = UART_KRST_TIMEOUT;
; ..\mcal_src\Uart.c	   516    /* Reset End Init Protection to access regsiters */
; ..\mcal_src\Uart.c	   517    UART_SFR_INIT_RESETENDINIT();
; ..\mcal_src\Uart.c	   518    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   519     efficiently access the SFRs of multiple ASCLINKernels.*/
; ..\mcal_src\Uart.c	   520    UART_SFR_INIT_MODIFY32(UART_HW_MODULE[HwUnit].KRST0.U,\ 
; ..\mcal_src\Uart.c	   521                                       UART_KRST0_RST_CLEARMASK,UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	   522    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   523     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   524    Readback = UART_SFR_INIT_USER_MODE_READ32(UART_HW_MODULE[HwUnit].KRST0.U);
; ..\mcal_src\Uart.c	   525    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   526     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   527    UART_SFR_INIT_MODIFY32(UART_HW_MODULE[HwUnit].KRST1.U,\ 
; ..\mcal_src\Uart.c	   528                 UART_KRST1_RST_CLEARMASK,UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	   529    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   530     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   531    Readback = UART_SFR_INIT_USER_MODE_READ32(UART_HW_MODULE[HwUnit].KRST1.U);
; ..\mcal_src\Uart.c	   532    /* Set End Init Protection */
; ..\mcal_src\Uart.c	   533    UART_SFR_INIT_SETENDINIT();
; ..\mcal_src\Uart.c	   534    do
; ..\mcal_src\Uart.c	   535    {
; ..\mcal_src\Uart.c	   536      WaitCount--;
; ..\mcal_src\Uart.c	   537      #ifdef IFX_UART_DEBUG
; ..\mcal_src\Uart.c	   538      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   539      efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   540      RstStatus =(uint32)(((UART_SFR_INIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	   541            (UART_HW_MODULE[HwUnit].KRST0.U) & UART_KRST0_RSTSTAT_MASK) >>\ 
; ..\mcal_src\Uart.c	   542               UART_KRST0_RSTSTAT_BITPOS) & TestUart_DebugMask01);
; ..\mcal_src\Uart.c	   543      #else
; ..\mcal_src\Uart.c	   544      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   545      efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   546      RstStatus =(uint32)((UART_SFR_INIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	   547             (UART_HW_MODULE[HwUnit].KRST0.U) & UART_KRST0_RSTSTAT_MASK) >>\ 
; ..\mcal_src\Uart.c	   548                                                     UART_KRST0_RSTSTAT_BITPOS);
; ..\mcal_src\Uart.c	   549      #endif
; ..\mcal_src\Uart.c	   550    }
; ..\mcal_src\Uart.c	   551    while ((RstStatus == UART_ZERO_U) && (WaitCount > UART_ZERO_U));
; ..\mcal_src\Uart.c	   552    /* Reset End Init Protection to access regsiters */
; ..\mcal_src\Uart.c	   553    UART_SFR_INIT_RESETENDINIT();
; ..\mcal_src\Uart.c	   554    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   555     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   556    UART_SFR_INIT_MODIFY32(UART_HW_MODULE[HwUnit].KRSTCLR.U,\ 
; ..\mcal_src\Uart.c	   557                                    UART_KRSTCLR_CLR_CLEARMASK,UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	   558    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   559     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   560    Readback = UART_SFR_INIT_USER_MODE_READ32(UART_HW_MODULE[HwUnit].KRSTCLR.U);
; ..\mcal_src\Uart.c	   561    /* Set End Init Protection */
; ..\mcal_src\Uart.c	   562    UART_SFR_INIT_SETENDINIT();
; ..\mcal_src\Uart.c	   563    UNUSED_PARAMETER(Readback)
; ..\mcal_src\Uart.c	   564  }
; ..\mcal_src\Uart.c	   565  /*******************************************************************************
; ..\mcal_src\Uart.c	   566  ** Syntax           : IFX_LOCAL_INLINE void Uart_lHwInitKernelRegDeInit       **
; ..\mcal_src\Uart.c	   567  **                      (uint8 HwUnit)                                        **
; ..\mcal_src\Uart.c	   568  **                                                                            **
; ..\mcal_src\Uart.c	   569  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	   570  **                                                                            **
; ..\mcal_src\Uart.c	   571  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	   572  **                                                                            **
; ..\mcal_src\Uart.c	   573  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	   574  **                                                                            **
; ..\mcal_src\Uart.c	   575  ** Parameters (in)  : HwUnit   : ASCLIN Hardware module no                    **
; ..\mcal_src\Uart.c	   576  **                                                                            **
; ..\mcal_src\Uart.c	   577  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	   578  **                                                                            **
; ..\mcal_src\Uart.c	   579  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	   580  **                                                                            **
; ..\mcal_src\Uart.c	   581  ** Description      : This function resets the Kernel                         **
; ..\mcal_src\Uart.c	   582  *******************************************************************************/
; ..\mcal_src\Uart.c	   583  IFX_LOCAL_INLINE void Uart_lHwInitKernelRegDeInit(uint8 HwUnit)
; ..\mcal_src\Uart.c	   584  {
; ..\mcal_src\Uart.c	   585    volatile uint32 Readback;
; ..\mcal_src\Uart.c	   586    uint32 WaitCount,RstStatus;
; ..\mcal_src\Uart.c	   587    WaitCount = UART_KRST_TIMEOUT;
; ..\mcal_src\Uart.c	   588    /* Reset End Init Protection to access regsiters */
; ..\mcal_src\Uart.c	   589    UART_SFR_DEINIT_RESETENDINIT();
; ..\mcal_src\Uart.c	   590    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   591     efficiently access the SFRs of multiple ASCLINKernels.*/
; ..\mcal_src\Uart.c	   592    UART_SFR_DEINIT_MODIFY32(UART_HW_MODULE[HwUnit].KRST0.U,\ 
; ..\mcal_src\Uart.c	   593                                       UART_KRST0_RST_CLEARMASK,UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	   594    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   595     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   596    Readback = UART_SFR_DEINIT_USER_MODE_READ32(UART_HW_MODULE[HwUnit].KRST0.U);
; ..\mcal_src\Uart.c	   597    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   598     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   599    UART_SFR_DEINIT_MODIFY32(UART_HW_MODULE[HwUnit].KRST1.U,\ 
; ..\mcal_src\Uart.c	   600                 UART_KRST1_RST_CLEARMASK,UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	   601    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   602     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   603    Readback = UART_SFR_DEINIT_USER_MODE_READ32(UART_HW_MODULE[HwUnit].KRST1.U);
; ..\mcal_src\Uart.c	   604    /* Set End Init Protection */
; ..\mcal_src\Uart.c	   605    UART_SFR_DEINIT_SETENDINIT();
; ..\mcal_src\Uart.c	   606    do
; ..\mcal_src\Uart.c	   607    {
; ..\mcal_src\Uart.c	   608      WaitCount--;
; ..\mcal_src\Uart.c	   609      #ifdef IFX_UART_DEBUG
; ..\mcal_src\Uart.c	   610      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   611      efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   612      RstStatus =(uint32)(((UART_SFR_DEINIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	   613            (UART_HW_MODULE[HwUnit].KRST0.U) & UART_KRST0_RSTSTAT_MASK) >>\ 
; ..\mcal_src\Uart.c	   614               UART_KRST0_RSTSTAT_BITPOS) & TestUart_DebugMask01);
; ..\mcal_src\Uart.c	   615      #else
; ..\mcal_src\Uart.c	   616      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   617      efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   618      RstStatus =(uint32)((UART_SFR_DEINIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	   619             (UART_HW_MODULE[HwUnit].KRST0.U) & UART_KRST0_RSTSTAT_MASK) >>\ 
; ..\mcal_src\Uart.c	   620                                                     UART_KRST0_RSTSTAT_BITPOS);
; ..\mcal_src\Uart.c	   621      #endif
; ..\mcal_src\Uart.c	   622    }
; ..\mcal_src\Uart.c	   623    while ((RstStatus == UART_ZERO_U) && (WaitCount > UART_ZERO_U));
; ..\mcal_src\Uart.c	   624    /* Reset End Init Protection to access regsiters */
; ..\mcal_src\Uart.c	   625    UART_SFR_DEINIT_RESETENDINIT();
; ..\mcal_src\Uart.c	   626    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   627     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   628    UART_SFR_DEINIT_MODIFY32(UART_HW_MODULE[HwUnit].KRSTCLR.U,\ 
; ..\mcal_src\Uart.c	   629                                    UART_KRSTCLR_CLR_CLEARMASK,UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	   630    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to
; ..\mcal_src\Uart.c	   631     efficiently access the SFRs of multiple ASCLIN kernels.*/
; ..\mcal_src\Uart.c	   632    Readback = UART_SFR_DEINIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	   633                                             (UART_HW_MODULE[HwUnit].KRSTCLR.U);
; ..\mcal_src\Uart.c	   634    /* Set End Init Protection */
; ..\mcal_src\Uart.c	   635    UART_SFR_DEINIT_SETENDINIT();
; ..\mcal_src\Uart.c	   636    UNUSED_PARAMETER(Readback)
; ..\mcal_src\Uart.c	   637  }
; ..\mcal_src\Uart.c	   638  #endif
; ..\mcal_src\Uart.c	   639  
; ..\mcal_src\Uart.c	   640  /*******************************************************************************
; ..\mcal_src\Uart.c	   641  ** Syntax           : IFX_LOCAL_INLINE uint8 Uart_lHwInitClcReg               **
; ..\mcal_src\Uart.c	   642  **                      (uint8 HwUnit, uint32 Value)                          **
; ..\mcal_src\Uart.c	   643  **                                                                            **
; ..\mcal_src\Uart.c	   644  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	   645  **                                                                            **
; ..\mcal_src\Uart.c	   646  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	   647  **                                                                            **
; ..\mcal_src\Uart.c	   648  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	   649  **                                                                            **
; ..\mcal_src\Uart.c	   650  ** Parameters (in)  : HwUnit   : ASCLIN Hardware module no                    **
; ..\mcal_src\Uart.c	   651  **                  : Value    : Value to configure CLC Reg                   **
; ..\mcal_src\Uart.c	   652  **                                                                            **
; ..\mcal_src\Uart.c	   653  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	   654  **                                                                            **
; ..\mcal_src\Uart.c	   655  ** Return value     : 0 (Module Clk Enabled)/ 1 (Module Clk Disabled)         **
; ..\mcal_src\Uart.c	   656  **                                                                            **
; ..\mcal_src\Uart.c	   657  ** Description      : This function sets the clc register with given value    **
; ..\mcal_src\Uart.c	   658  *******************************************************************************/
; ..\mcal_src\Uart.c	   659  IFX_LOCAL_INLINE uint8 Uart_lHwInitClcReg(uint8 HwUnit, uint32 Value)
; ..\mcal_src\Uart.c	   660  {
; ..\mcal_src\Uart.c	   661    uint8 ModClkStatus;
; ..\mcal_src\Uart.c	   662  
; ..\mcal_src\Uart.c	   663    /* Reset End Init Protection */
; ..\mcal_src\Uart.c	   664    UART_SFR_INIT_RESETENDINIT();
	call	Mcal_ResetENDINIT
.L613:

; ..\mcal_src\Uart.c	   665  
; ..\mcal_src\Uart.c	   666    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	   667     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	   668    UART_SFR_INIT_WRITE32(UART_HW_MODULE[HwUnit].CLC.U,Value);
	sha	d15,d11,#8
	lea	a2,0xf0000600
	addsc.a	a14,a2,d15,#0
	mov	d15,#8
	st.w	[a14],d15
.L780:

; ..\mcal_src\Uart.c	   669  
; ..\mcal_src\Uart.c	   670    /* Set End Init Protection */
; ..\mcal_src\Uart.c	   671    UART_SFR_INIT_SETENDINIT();
	call	Mcal_SetENDINIT
.L781:

; ..\mcal_src\Uart.c	   672  
; ..\mcal_src\Uart.c	   673  /* Check the status bit DISS for Checking Mod Clk Disable Status*/
; ..\mcal_src\Uart.c	   674  #ifdef IFX_UART_DEBUG
; ..\mcal_src\Uart.c	   675    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	   676     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	   677    ModClkStatus = (uint8)(((UART_SFR_INIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	   678                   (UART_HW_MODULE[HwUnit].CLC.U) & UART_CLC_DISS_MASK) >>\ 
; ..\mcal_src\Uart.c	   679                   UART_CLC_DISS_BITPOS) | TestUart_DebugMask02);
; ..\mcal_src\Uart.c	   680  #else
; ..\mcal_src\Uart.c	   681    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	   682     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	   683    ModClkStatus = (uint8)((UART_SFR_INIT_USER_MODE_READ32\ 
	ld.w	d0,[a14]
.L782:

; ..\mcal_src\Uart.c	   684    (UART_HW_MODULE[HwUnit].CLC.U) & UART_CLC_DISS_MASK) >> UART_CLC_DISS_BITPOS);
	extr.u	d15,d0,#1,#8
.L620:
	and	d15,#1
	fcall	.cocofun_14
.L371:
	addsc.a	a14,a2,d11,#0
.L378:
	jne	d15,#0,.L4
.L379:
	mov	d4,d11
	mov.aa	a4,a13
.L622:
	call	Uart_lHwInit
.L623:
	st.b	[a15]18,d11
.L783:
	st.b	[a14],d10
.L784:
	mov	d15,#1
	st.b	[a15]8,d15
.L621:
	st.b	[a15]9,d15
.L785:
	mov	d15,#0
	st.w	[a12],d15
.L786:
	st.w	[a12]4,d15
.L787:
	j	.L5
.L4:
	mov	d15,#255
	st.b	[a15]18,d15
.L624:
	st.b	[a14],d15
.L788:
	mov	d15,#0
	st.b	[a15]8,d15
.L789:
	add	d9,#1
	st.b	[a15]9,d15
.L618:
	extr.u	d9,d9,#0,#8
.L5:
	add	d10,#1
	lea	a15,[a15]20
.L616:
	extr.u	d10,d10,#0,#8
	add.a	a12,#4
.L2:
	jlt.u	d10,d8,.L3
.L790:
	jne	d9,#0,.L6
.L791:
	movh.a	a15,#@his(Uart_kConfigPtr)
.L792:
	ld.a	a2,[a10]
.L625:
	st.a	[a15]@los(Uart_kConfigPtr),a2
.L6:
	ret
.L358:
	
__Uart_Init_function_end:
	.size	Uart_Init,__Uart_Init_function_end-Uart_Init
.L164:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_14')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_14
.L83:
.cocofun_14:	.type	func
; Function body .cocofun_14, coco_iter:1
	movh.a	a2,#@his(Uart_BusChannelMap)
.L614:
	lea	a2,[a2]@los(Uart_BusChannelMap)
.L1136:
	fret
.L329:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_13')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_13
.L85:
.cocofun_13:	.type	func
; Function body .cocofun_13, coco_iter:1
	movh.a	a15,#@his(Uart_ChannelInfo)
.L663:
	lea	a15,[a15]@los(Uart_ChannelInfo)
.L1131:
	fret
.L324:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lHwInit')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	   685  #endif
; ..\mcal_src\Uart.c	   686  
; ..\mcal_src\Uart.c	   687    return ModClkStatus;
; ..\mcal_src\Uart.c	   688  }
; ..\mcal_src\Uart.c	   689  /*******************************************************************************
; ..\mcal_src\Uart.c	   690  ** Traceability :                                                             **
; ..\mcal_src\Uart.c	   691  ** Syntax           : static void Uart_lHwInit                                **
; ..\mcal_src\Uart.c	   692  **                     (uint8 HwUnit,Uart_ChannelConfigType* ChannelConfigPtr)**
; ..\mcal_src\Uart.c	   693  **                                                                            **
; ..\mcal_src\Uart.c	   694  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	   695  **                                                                            **
; ..\mcal_src\Uart.c	   696  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	   697  **                                                                            **
; ..\mcal_src\Uart.c	   698  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	   699  **                                                                            **
; ..\mcal_src\Uart.c	   700  ** Parameters (in)  : HwUnit   : ASCLIN Hardware module no                    **
; ..\mcal_src\Uart.c	   701  **                ChannelConfigPtr : Channel configuration pointer            **
; ..\mcal_src\Uart.c	   702  **                                                                            **
; ..\mcal_src\Uart.c	   703  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	   704  **                                                                            **
; ..\mcal_src\Uart.c	   705  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	   706  **                                                                            **
; ..\mcal_src\Uart.c	   707  ** Description      : This function initialises the ASCLIN Hw module          **
; ..\mcal_src\Uart.c	   708  *******************************************************************************/
; ..\mcal_src\Uart.c	   709  static void Uart_lHwInit(uint8 HwUnit,const Uart_ChannelType* ChannelConfigPtr)
; Function Uart_lHwInit
.L87:
Uart_lHwInit:	.type	func

; ..\mcal_src\Uart.c	   710  {
; ..\mcal_src\Uart.c	   711    Ifx_ASCLIN*  HwModulePtr;
; ..\mcal_src\Uart.c	   712    uint32 TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	   713  
; ..\mcal_src\Uart.c	   714    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	   715     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	   716    HwModulePtr = &(UART_HW_MODULE[HwUnit]);
	sha	d15,d4,#8
	lea	a15,0xf0000600
.L948:
	addsc.a	a15,a15,d15,#0
.L626:
	mov	d0,#255
.L461:

; ..\mcal_src\Uart.c	   717    /* Disable the Input Clock source */
; ..\mcal_src\Uart.c	   718    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
	ld.w	d15,[a15]76
	insert	d15,d15,#0,#0,#5
	st.w	[a15]76,d15
.L462:

; ..\mcal_src\Uart.c	   719                             UART_CSR_CLKSEL_CLEARMASK,UART_CSRREG_CLKSEL_NOCLK)
; ..\mcal_src\Uart.c	   720    /* provide delay of  TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	   721    /* Wait TW or poll for CSR.CON = 0 */
; ..\mcal_src\Uart.c	   722    while ((((UART_SFR_INIT_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	   723            UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) != UART_BIT_RESET) &&\ 
; ..\mcal_src\Uart.c	   724                                                              (TimeOutCount > 0U))
	j	.L7
.L8:

; ..\mcal_src\Uart.c	   725    {
; ..\mcal_src\Uart.c	   726      TimeOutCount-- ;
	add	d0,#-1
.L7:
	ld.w	d15,[a15]76
.L949:
	sh	d15,d15,#-31
.L950:
	jeq	d15,#0,.L9
.L951:
	jne	d0,#0,.L8
.L9:

; ..\mcal_src\Uart.c	   727    }
; ..\mcal_src\Uart.c	   728  
; ..\mcal_src\Uart.c	   729    TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	   730    /* Change to INIT mode */
; ..\mcal_src\Uart.c	   731    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
	ld.w	d15,[a15]24
.L465:
	mov	d0,#255
.L466:
	insert	d15,d15,#0,#16,#2
	st.w	[a15]24,d15
.L467:

; ..\mcal_src\Uart.c	   732                         UART_FRAMECON_MODE_CLEARMASK,UART_FRAMECONREG_INIT_MODE)
; ..\mcal_src\Uart.c	   733    /* Connect the Clock source */
; ..\mcal_src\Uart.c	   734    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
	fcall	.cocofun_7
.L470:

; ..\mcal_src\Uart.c	   735                               UART_CSR_CLKSEL_CLEARMASK,UART_CSRREG_CLKSEL_CLC)
; ..\mcal_src\Uart.c	   736    /* provide delay of TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	   737    /*Wait TW or poll for CSR.CON = 1*/
; ..\mcal_src\Uart.c	   738    while ((((UART_SFR_INIT_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	   739              UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) !=\ 
; ..\mcal_src\Uart.c	   740                              UART_BIT_SET) && (TimeOutCount > 0U))
	j	.L10
.L11:

; ..\mcal_src\Uart.c	   741    {
; ..\mcal_src\Uart.c	   742       TimeOutCount-- ;
	add	d0,#-1
.L10:
	ld.w	d15,[a15]76
.L952:
	sh	d15,d15,#-31
.L953:
	jne	d15,#0,.L12
.L954:
	jne	d0,#0,.L11
.L12:

; ..\mcal_src\Uart.c	   743    }
; ..\mcal_src\Uart.c	   744  
; ..\mcal_src\Uart.c	   745    TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	   746    /* Disable the Clock source. */
; ..\mcal_src\Uart.c	   747    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
	mov	d15,#255
	ld.w	d0,[a15]76
.L472:
	insert	d0,d0,#0,#0,#5
	st.w	[a15]76,d0
.L473:

; ..\mcal_src\Uart.c	   748                            UART_CSR_CLKSEL_CLEARMASK,UART_CSRREG_CLKSEL_NOCLK)
; ..\mcal_src\Uart.c	   749    /* provide delay of  TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	   750    /*Wait TW or poll for CSR.CON = 0*/
; ..\mcal_src\Uart.c	   751    while ((((UART_SFR_INIT_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	   752           UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) != UART_BIT_RESET) &&\ 
; ..\mcal_src\Uart.c	   753                                                              (TimeOutCount > 0U))
	j	.L13
.L14:

; ..\mcal_src\Uart.c	   754    {
; ..\mcal_src\Uart.c	   755       TimeOutCount-- ;
	add	d15,#-1
.L13:
	ld.w	d0,[a15]76
.L955:
	sh	d0,d0,#-31
.L956:
	jeq	d0,#0,.L15
.L957:
	jne	d15,#0,.L14
.L15:

; ..\mcal_src\Uart.c	   756    }
; ..\mcal_src\Uart.c	   757    /* Change to ASC mode */
; ..\mcal_src\Uart.c	   758     UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
	ld.w	d15,[a15]24
.L476:

; ..\mcal_src\Uart.c	   759                         UART_FRAMECON_MODE_CLEARMASK,UART_FRAMECONREG_ASC_MODE)
; ..\mcal_src\Uart.c	   760  
; ..\mcal_src\Uart.c	   761    /* Configure the Baudrate parameters */
; ..\mcal_src\Uart.c	   762    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->BRG.U,\ 
; ..\mcal_src\Uart.c	   763                                      UART_BRG_NUMERATOR_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   764          ((uint32)ChannelConfigPtr->HwBrgNumerator << UART_BRG_NUMERATOR_BITPOS))
; ..\mcal_src\Uart.c	   765    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->BRG.U,\ 
; ..\mcal_src\Uart.c	   766                                         UART_BRG_DENOMINATOR_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   767                                             ChannelConfigPtr->HwBrgDenominator)
; ..\mcal_src\Uart.c	   768    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->BITCON.U,\ 
; ..\mcal_src\Uart.c	   769                                            UART_BITCON_PRESCALER_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   770                                            ChannelConfigPtr->HwBitconPrescalar)
; ..\mcal_src\Uart.c	   771    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->BITCON.U,\ 
; ..\mcal_src\Uart.c	   772                  UART_BITCON_OVERSAMPLING_CLEARMASK,((uint32)\ 
; ..\mcal_src\Uart.c	   773       ChannelConfigPtr->HwBitconOversampling << UART_BITCON_OVERSAMPLING_BITPOS))
; ..\mcal_src\Uart.c	   774  
; ..\mcal_src\Uart.c	   775    /* Digital Glitch Filter = OFF */
; ..\mcal_src\Uart.c	   776    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
; ..\mcal_src\Uart.c	   777                               UART_IOCR_DEPTH_CLEARMASK,UART_IOCRREG_DEPTH_VAL)
; ..\mcal_src\Uart.c	   778  
; ..\mcal_src\Uart.c	   779    /* Configure CTS */
; ..\mcal_src\Uart.c	   780    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
; ..\mcal_src\Uart.c	   781          UART_IOCR_CTSEN_CLEARMASK, ((uint32)ChannelConfigPtr->CtsEnable <<\ 
; ..\mcal_src\Uart.c	   782                                                          UART_IOCR_CTSEN_BITPOS))
; ..\mcal_src\Uart.c	   783    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
; ..\mcal_src\Uart.c	   784        UART_IOCR_RCPOL_CLEARMASK, ((uint32)ChannelConfigPtr->CtsPolarity <<\ 
; ..\mcal_src\Uart.c	   785                                                          UART_IOCR_RCPOL_BITPOS))
; ..\mcal_src\Uart.c	   786  
; ..\mcal_src\Uart.c	   787    /* Configure Sample mode(3 Bit), Sample point, Parity, Collision detection */
; ..\mcal_src\Uart.c	   788    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->BITCON.U,\ 
; ..\mcal_src\Uart.c	   789                 UART_BITCON_SM_CLEARMASK, ((uint32)UART_BITCONREG_SM_VAL <<\ 
; ..\mcal_src\Uart.c	   790                                                           UART_BITCON_SM_BITPOS))
; ..\mcal_src\Uart.c	   791    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->BITCON.U,\ 
	movh	d1,#256
.L477:
	insert	d15,d15,#1,#16,#2
	st.w	[a15]24,d15
.L478:
	ld.w	d15,[a15]32
.L629:
	ld.hu	d0,[a4]16
	insert	d15,d15,#0,#16,#12
	sh	d0,d0,#16
.L630:
	or	d15,d0
	st.w	[a15]32,d15
.L484:
	ld.w	d15,[a15]32
.L631:
	ld.hu	d0,[a4]18
	insert	d15,d15,#0,#0,#12
.L632:
	or	d15,d0
	st.w	[a15]32,d15
.L486:
	ld.w	d15,[a15]20
.L633:
	ld.hu	d0,[a4]20
	insert	d15,d15,#0,#0,#12
.L634:
	or	d15,d0
	st.w	[a15]20,d15
.L488:
	ld.w	d15,[a15]20
.L635:
	ld.bu	d0,[a4]22
	insert	d15,d15,#0,#16,#4
	sh	d0,d0,#16
.L636:
	or	d15,d0
	st.w	[a15]20,d15
.L490:
	ld.w	d15,[a15]4
.L637:
	insert	d15,d15,#0,#4,#6
	st.w	[a15]4,d15
.L492:
	ld.w	d15,[a15]4
.L638:
	ld.bu	d0,[a4]29
	insert	d15,d15,#0,#29,#1
	sh	d0,d0,#29
.L639:
	or	d15,d0
	st.w	[a15]4,d15
.L494:
	ld.w	d15,[a15]4
.L640:
	ld.bu	d0,[a4]30
	insert	d15,d15,#0,#25,#1
	sh	d0,d0,#25
.L641:
	or	d15,d0
	st.w	[a15]4,d15
.L496:
	ld.w	d15,[a15]20
.L642:
	insert	d15,d15,#1,#31,#1
	st.w	[a15]20,d15
.L481:
	ld.w	d15,[a15]20
.L643:
	ld.bu	d0,[a4]22
	insert	d15,d15,#0,#24,#4
	sh	d0,#-1
	sh	d0,d0,#24
	add	d0,d1
.L644:
	or	d15,d0
	st.w	[a15]20,d15
.L482:

; ..\mcal_src\Uart.c	   792                                    UART_BITCON_SAMPLEPOINT_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   793      (uint32)((((uint32)(ChannelConfigPtr->HwBitconOversampling)>>UART_ONE_U)+\ 
; ..\mcal_src\Uart.c	   794                                   UART_ONE_U) << UART_BITCON_SAMPLEPOINT_BITPOS))
; ..\mcal_src\Uart.c	   795  
; ..\mcal_src\Uart.c	   796    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
	ld.w	d15,[a15]24
.L645:
	ld.bu	d0,[a4]27
	insert	d15,d15,#0,#30,#1
	sh	d0,d0,#30
.L646:
	or	d15,d0
	st.w	[a15]24,d15
.L499:

; ..\mcal_src\Uart.c	   797          UART_FRAMECON_PEN_CLEARMASK, ((uint32)ChannelConfigPtr->ParityEnable <<\ 
; ..\mcal_src\Uart.c	   798                                                        UART_FRAMECON_PEN_BITPOS))
; ..\mcal_src\Uart.c	   799    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
	ld.w	d15,[a15]24
.L647:
	ld.bu	d0,[a4]28
	insert	d15,d15,#0,#31,#1
	sh	d0,d0,#31
.L648:
	or	d15,d0
	st.w	[a15]24,d15
.L501:

; ..\mcal_src\Uart.c	   800              UART_FRAMECON_ODD_CLEARMASK, ((uint32)ChannelConfigPtr->Parity <<\ 
; ..\mcal_src\Uart.c	   801                                                        UART_FRAMECON_ODD_BITPOS))
; ..\mcal_src\Uart.c	   802    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
	ld.w	d15,[a15]24
.L649:
	insert	d15,d15,#1,#6,#3
	st.w	[a15]24,d15
.L503:

; ..\mcal_src\Uart.c	   803          UART_FRAMECON_IDLE_CLEARMASK, UART_BIT_SET << UART_FRAMECON_IDLE_BITPOS)
; ..\mcal_src\Uart.c	   804  
; ..\mcal_src\Uart.c	   805    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
	ld.w	d15,[a15]24
.L650:
	ld.bu	d0,[a4]24
	insert	d15,d15,#0,#9,#3
	sh	d0,d0,#9
.L651:
	or	d15,d0
	st.w	[a15]24,d15
.L505:

; ..\mcal_src\Uart.c	   806         UART_FRAMECON_STOP_CLEARMASK, ((uint32)ChannelConfigPtr->StopBits <<\ 
; ..\mcal_src\Uart.c	   807                                                       UART_FRAMECON_STOP_BITPOS))
; ..\mcal_src\Uart.c	   808  
; ..\mcal_src\Uart.c	   809    /* Configure RX inlet, TX outlet width/s(8/16 bit) based on UartDataLength*/
; ..\mcal_src\Uart.c	   810    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
	ld.w	d15,[a15]12
.L652:
	insert	d15,d15,#1,#6,#2
	st.w	[a15]12,d15
.L507:

; ..\mcal_src\Uart.c	   811                   UART_TXFIFOCON_INW_CLEARMASK, UART_TXFIFOCONREG_INW_VAL <<\ 
; ..\mcal_src\Uart.c	   812                                                        UART_TXFIFOCON_INW_BITPOS)
; ..\mcal_src\Uart.c	   813    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
	ld.w	d15,[a15]16
.L653:
	insert	d15,d15,#1,#6,#2
	st.w	[a15]16,d15
.L509:

; ..\mcal_src\Uart.c	   814                UART_RXFIFOCON_OUTW_CLEARMASK, UART_RXFIFOCONREG_OUTW_VAL <<\ 
; ..\mcal_src\Uart.c	   815                                                       UART_RXFIFOCON_OUTW_BITPOS)
; ..\mcal_src\Uart.c	   816  
; ..\mcal_src\Uart.c	   817    /*Data length Defines the number of bits in a character*/
; ..\mcal_src\Uart.c	   818    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->DATCON.U,\ 
	ld.w	d15,[a15]28
.L654:
	ld.bu	d0,[a4]25
	insert	d15,d15,#0,#0,#4
	add	d0,#-1
.L655:
	or	d15,d0
	st.w	[a15]28,d15
.L511:

; ..\mcal_src\Uart.c	   819                                     UART_DATCON_DATLEN_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   820                             ((uint32)ChannelConfigPtr->DataLength - UART_ONE_U))
; ..\mcal_src\Uart.c	   821  
; ..\mcal_src\Uart.c	   822    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
	ld.w	d15,[a15]4
.L656:
	insert	d15,d15,#0,#28,#1
	st.w	[a15]4,d15
.L513:

; ..\mcal_src\Uart.c	   823                                     UART_IOCR_LB_CLEARMASK, UART_DISABLE_BIT)
; ..\mcal_src\Uart.c	   824    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
	ld.w	d15,[a15]4
.L657:
	ld.bu	d0,[a4]26
	insert	d15,d15,#0,#0,#3
.L658:
	or	d15,d0
	st.w	[a15]4,d15
.L515:

; ..\mcal_src\Uart.c	   825                     UART_IOCR_ALTI_CLEARMASK, ChannelConfigPtr->RxPinSelection)
; ..\mcal_src\Uart.c	   826  
; ..\mcal_src\Uart.c	   827    /* Select the Clock source */
; ..\mcal_src\Uart.c	   828    UART_SFR_INIT_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
	fcall	.cocofun_7
.L517:

; ..\mcal_src\Uart.c	   829                               UART_CSR_CLKSEL_CLEARMASK, UART_CSRREG_CLKSEL_CLC)
; ..\mcal_src\Uart.c	   830  
; ..\mcal_src\Uart.c	   831    TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	   832    /* provide delay of TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	   833    /*Wait TW or poll for CSR.CON = 1*/
; ..\mcal_src\Uart.c	   834    while ((((UART_SFR_INIT_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	   835               UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) !=UART_BIT_SET) &&\ 
; ..\mcal_src\Uart.c	   836                                                              (TimeOutCount > 0U))
	mov	d15,#255
	j	.L16
.L17:

; ..\mcal_src\Uart.c	   837    {
; ..\mcal_src\Uart.c	   838       TimeOutCount-- ;
	add	d15,#-1
.L16:
	ld.w	d0,[a15]76
.L958:
	sh	d0,d0,#-31
.L959:
	jne	d0,#0,.L18
.L960:
	jne	d15,#0,.L17
.L18:

; ..\mcal_src\Uart.c	   839    }
; ..\mcal_src\Uart.c	   840  }
	ret
.L456:
	
__Uart_lHwInit_function_end:
	.size	Uart_lHwInit,__Uart_lHwInit_function_end-Uart_lHwInit
.L199:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_7')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_7
.L89:
.cocofun_7:	.type	func
; Function body .cocofun_7, coco_iter:0
	ld.w	d15,[a15]76
.L628:
	insert	d15,d15,#8,#0,#5
	st.w	[a15]76,d15
.L627:
	fret
.L294:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_Read')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Uart_Read

; ..\mcal_src\Uart.c	   841  #if ((UART_ABORT_READ_API == STD_ON) || (UART_ABORT_WRITE_API == STD_ON))
; ..\mcal_src\Uart.c	   842  /*******************************************************************************
; ..\mcal_src\Uart.c	   843  ** Traceability :                                                             **
; ..\mcal_src\Uart.c	   844  ** Syntax           : static void Uart_lHwInitRuntime                         **
; ..\mcal_src\Uart.c	   845  **                     (uint8 HwUnit,Uart_ChannelConfigType* ChannelConfigPtr)**
; ..\mcal_src\Uart.c	   846  **                                                                            **
; ..\mcal_src\Uart.c	   847  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	   848  **                                                                            **
; ..\mcal_src\Uart.c	   849  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	   850  **                                                                            **
; ..\mcal_src\Uart.c	   851  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	   852  **                                                                            **
; ..\mcal_src\Uart.c	   853  ** Parameters (in)  : HwUnit   : ASCLIN Hardware module no                    **
; ..\mcal_src\Uart.c	   854  **                ChannelConfigPtr : Channel configuration pointer            **
; ..\mcal_src\Uart.c	   855  **                                                                            **
; ..\mcal_src\Uart.c	   856  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	   857  **                                                                            **
; ..\mcal_src\Uart.c	   858  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	   859  **                                                                            **
; ..\mcal_src\Uart.c	   860  ** Description      : This function initialises the ASCLIN Hw module          **
; ..\mcal_src\Uart.c	   861  *******************************************************************************/
; ..\mcal_src\Uart.c	   862  static void Uart_lHwInitRuntime(uint8 HwUnit,\ 
; ..\mcal_src\Uart.c	   863                                         const Uart_ChannelType* ChannelConfigPtr)
; ..\mcal_src\Uart.c	   864  {
; ..\mcal_src\Uart.c	   865    Ifx_ASCLIN*  HwModulePtr;
; ..\mcal_src\Uart.c	   866    uint32 TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	   867  
; ..\mcal_src\Uart.c	   868    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	   869     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	   870    HwModulePtr = &(UART_HW_MODULE[HwUnit]);
; ..\mcal_src\Uart.c	   871     /* Disable the Input Clock source */
; ..\mcal_src\Uart.c	   872      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
; ..\mcal_src\Uart.c	   873                             UART_CSR_CLKSEL_CLEARMASK,UART_CSRREG_CLKSEL_NOCLK)
; ..\mcal_src\Uart.c	   874      /* provide delay of  TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	   875      /* Wait TW or poll for CSR.CON = 0 */
; ..\mcal_src\Uart.c	   876      while ((((UART_SFR_RUNTIME_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	   877            UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) != UART_BIT_RESET) &&\ 
; ..\mcal_src\Uart.c	   878                                                              (TimeOutCount > 0U))
; ..\mcal_src\Uart.c	   879      {
; ..\mcal_src\Uart.c	   880        TimeOutCount-- ;
; ..\mcal_src\Uart.c	   881      }
; ..\mcal_src\Uart.c	   882  
; ..\mcal_src\Uart.c	   883      TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	   884    /* Change to INIT mode */
; ..\mcal_src\Uart.c	   885    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
; ..\mcal_src\Uart.c	   886                         UART_FRAMECON_MODE_CLEARMASK,UART_FRAMECONREG_INIT_MODE)
; ..\mcal_src\Uart.c	   887    /* Connect the Clock source */
; ..\mcal_src\Uart.c	   888    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
; ..\mcal_src\Uart.c	   889                               UART_CSR_CLKSEL_CLEARMASK,UART_CSRREG_CLKSEL_CLC)
; ..\mcal_src\Uart.c	   890    /* provide delay of TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	   891    /*Wait TW or poll for CSR.CON = 1*/
; ..\mcal_src\Uart.c	   892    while ((((UART_SFR_RUNTIME_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	   893              UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) !=\ 
; ..\mcal_src\Uart.c	   894                              UART_BIT_SET) && (TimeOutCount > 0U))
; ..\mcal_src\Uart.c	   895    {
; ..\mcal_src\Uart.c	   896       TimeOutCount-- ;
; ..\mcal_src\Uart.c	   897    }
; ..\mcal_src\Uart.c	   898  
; ..\mcal_src\Uart.c	   899    TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	   900    /* Disable the Clock source. */
; ..\mcal_src\Uart.c	   901    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
; ..\mcal_src\Uart.c	   902                            UART_CSR_CLKSEL_CLEARMASK,UART_CSRREG_CLKSEL_NOCLK)
; ..\mcal_src\Uart.c	   903    /* provide delay of  TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	   904    /*Wait TW or poll for CSR.CON = 0*/
; ..\mcal_src\Uart.c	   905    while ((((UART_SFR_RUNTIME_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	   906           UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) != UART_BIT_RESET) &&\ 
; ..\mcal_src\Uart.c	   907                                                              (TimeOutCount > 0U))
; ..\mcal_src\Uart.c	   908    {
; ..\mcal_src\Uart.c	   909       TimeOutCount-- ;
; ..\mcal_src\Uart.c	   910    }
; ..\mcal_src\Uart.c	   911    /* Change to ASC mode */
; ..\mcal_src\Uart.c	   912    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
; ..\mcal_src\Uart.c	   913                         UART_FRAMECON_MODE_CLEARMASK,UART_FRAMECONREG_ASC_MODE)
; ..\mcal_src\Uart.c	   914  
; ..\mcal_src\Uart.c	   915    /* Configure the Baudrate parameters */
; ..\mcal_src\Uart.c	   916    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->BRG.U,\ 
; ..\mcal_src\Uart.c	   917                                      UART_BRG_NUMERATOR_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   918          ((uint32)ChannelConfigPtr->HwBrgNumerator << UART_BRG_NUMERATOR_BITPOS))
; ..\mcal_src\Uart.c	   919    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->BRG.U,\ 
; ..\mcal_src\Uart.c	   920                                         UART_BRG_DENOMINATOR_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   921                                             ChannelConfigPtr->HwBrgDenominator)
; ..\mcal_src\Uart.c	   922    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->BITCON.U,\ 
; ..\mcal_src\Uart.c	   923                                            UART_BITCON_PRESCALER_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   924                                            ChannelConfigPtr->HwBitconPrescalar)
; ..\mcal_src\Uart.c	   925    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->BITCON.U,\ 
; ..\mcal_src\Uart.c	   926                  UART_BITCON_OVERSAMPLING_CLEARMASK,((uint32)\ 
; ..\mcal_src\Uart.c	   927       ChannelConfigPtr->HwBitconOversampling << UART_BITCON_OVERSAMPLING_BITPOS))
; ..\mcal_src\Uart.c	   928  
; ..\mcal_src\Uart.c	   929    /* Digital Glitch Filter = OFF */
; ..\mcal_src\Uart.c	   930    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
; ..\mcal_src\Uart.c	   931                               UART_IOCR_DEPTH_CLEARMASK,UART_IOCRREG_DEPTH_VAL)
; ..\mcal_src\Uart.c	   932  
; ..\mcal_src\Uart.c	   933    /* Configure CTS */
; ..\mcal_src\Uart.c	   934    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
; ..\mcal_src\Uart.c	   935          UART_IOCR_CTSEN_CLEARMASK, ((uint32)ChannelConfigPtr->CtsEnable <<\ 
; ..\mcal_src\Uart.c	   936                                                          UART_IOCR_CTSEN_BITPOS))
; ..\mcal_src\Uart.c	   937    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
; ..\mcal_src\Uart.c	   938        UART_IOCR_RCPOL_CLEARMASK, ((uint32)ChannelConfigPtr->CtsPolarity <<\ 
; ..\mcal_src\Uart.c	   939                                                          UART_IOCR_RCPOL_BITPOS))
; ..\mcal_src\Uart.c	   940  
; ..\mcal_src\Uart.c	   941    /* Configure Sample mode(3 Bit), Sample point, Parity, Collision detection */
; ..\mcal_src\Uart.c	   942    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->BITCON.U,\ 
; ..\mcal_src\Uart.c	   943                 UART_BITCON_SM_CLEARMASK, ((uint32)UART_BITCONREG_SM_VAL <<\ 
; ..\mcal_src\Uart.c	   944                                                           UART_BITCON_SM_BITPOS))
; ..\mcal_src\Uart.c	   945    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->BITCON.U,\ 
; ..\mcal_src\Uart.c	   946                                    UART_BITCON_SAMPLEPOINT_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   947      (uint32)((((uint32)(ChannelConfigPtr->HwBitconOversampling)>>UART_ONE_U)+\ 
; ..\mcal_src\Uart.c	   948                                   UART_ONE_U) << UART_BITCON_SAMPLEPOINT_BITPOS))
; ..\mcal_src\Uart.c	   949  
; ..\mcal_src\Uart.c	   950    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
; ..\mcal_src\Uart.c	   951          UART_FRAMECON_PEN_CLEARMASK, ((uint32)ChannelConfigPtr->ParityEnable <<\ 
; ..\mcal_src\Uart.c	   952                                                        UART_FRAMECON_PEN_BITPOS))
; ..\mcal_src\Uart.c	   953    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
; ..\mcal_src\Uart.c	   954              UART_FRAMECON_ODD_CLEARMASK, ((uint32)ChannelConfigPtr->Parity <<\ 
; ..\mcal_src\Uart.c	   955                                                        UART_FRAMECON_ODD_BITPOS))
; ..\mcal_src\Uart.c	   956    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
; ..\mcal_src\Uart.c	   957          UART_FRAMECON_IDLE_CLEARMASK, UART_BIT_SET << UART_FRAMECON_IDLE_BITPOS)
; ..\mcal_src\Uart.c	   958  
; ..\mcal_src\Uart.c	   959    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
; ..\mcal_src\Uart.c	   960         UART_FRAMECON_STOP_CLEARMASK, ((uint32)ChannelConfigPtr->StopBits <<\ 
; ..\mcal_src\Uart.c	   961                                                       UART_FRAMECON_STOP_BITPOS))
; ..\mcal_src\Uart.c	   962  
; ..\mcal_src\Uart.c	   963    /* Configure RX inlet, TX outlet width/s(8/16 bit) based on UartDataLength*/
; ..\mcal_src\Uart.c	   964    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	   965                   UART_TXFIFOCON_INW_CLEARMASK, UART_TXFIFOCONREG_INW_VAL <<\ 
; ..\mcal_src\Uart.c	   966                                                        UART_TXFIFOCON_INW_BITPOS)
; ..\mcal_src\Uart.c	   967    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	   968                UART_RXFIFOCON_OUTW_CLEARMASK, UART_RXFIFOCONREG_OUTW_VAL <<\ 
; ..\mcal_src\Uart.c	   969                                                       UART_RXFIFOCON_OUTW_BITPOS)
; ..\mcal_src\Uart.c	   970  
; ..\mcal_src\Uart.c	   971    /*Data length Defines the number of bits in a character*/
; ..\mcal_src\Uart.c	   972    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->DATCON.U,\ 
; ..\mcal_src\Uart.c	   973                                     UART_DATCON_DATLEN_CLEARMASK,\ 
; ..\mcal_src\Uart.c	   974                             ((uint32)ChannelConfigPtr->DataLength - UART_ONE_U))
; ..\mcal_src\Uart.c	   975  
; ..\mcal_src\Uart.c	   976    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
; ..\mcal_src\Uart.c	   977                                     UART_IOCR_LB_CLEARMASK, UART_DISABLE_BIT)
; ..\mcal_src\Uart.c	   978    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->IOCR.U,\ 
; ..\mcal_src\Uart.c	   979                     UART_IOCR_ALTI_CLEARMASK, ChannelConfigPtr->RxPinSelection)
; ..\mcal_src\Uart.c	   980  
; ..\mcal_src\Uart.c	   981    /* Select the Clock source */
; ..\mcal_src\Uart.c	   982    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
; ..\mcal_src\Uart.c	   983                               UART_CSR_CLKSEL_CLEARMASK, UART_CSRREG_CLKSEL_CLC)
; ..\mcal_src\Uart.c	   984  
; ..\mcal_src\Uart.c	   985    TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	   986    /* provide delay of TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	   987    /*Wait TW or poll for CSR.CON = 1*/
; ..\mcal_src\Uart.c	   988    while ((((UART_SFR_RUNTIME_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	   989               UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) !=UART_BIT_SET) &&\ 
; ..\mcal_src\Uart.c	   990                                                              (TimeOutCount > 0U))
; ..\mcal_src\Uart.c	   991    {
; ..\mcal_src\Uart.c	   992       TimeOutCount-- ;
; ..\mcal_src\Uart.c	   993    }
; ..\mcal_src\Uart.c	   994  }
; ..\mcal_src\Uart.c	   995  #endif
; ..\mcal_src\Uart.c	   996  #if (UART_DEINIT_API == STD_ON)
; ..\mcal_src\Uart.c	   997  /*******************************************************************************
; ..\mcal_src\Uart.c	   998  ** Traceability : [cover parentID=DS_NAS_UART_PR1570,DS_NAS_UART_PR63_7]      **
; ..\mcal_src\Uart.c	   999  **
; ..\mcal_src\Uart.c	  1000  ** Syntax : void Uart_DeInit( void )                                          **
; ..\mcal_src\Uart.c	  1001  **          [/cover]                                                          **
; ..\mcal_src\Uart.c	  1002  ** Service ID:  0x01                                                          **
; ..\mcal_src\Uart.c	  1003  **                                                                            **
; ..\mcal_src\Uart.c	  1004  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  1005  **                                                                            **
; ..\mcal_src\Uart.c	  1006  ** Reentrancy:  Non reentrant                                                 **
; ..\mcal_src\Uart.c	  1007  **                                                                            **
; ..\mcal_src\Uart.c	  1008  ** Parameters (in) :  None                                                    **
; ..\mcal_src\Uart.c	  1009  **                                                                            **
; ..\mcal_src\Uart.c	  1010  ** Parameters (out):  None                                                    **
; ..\mcal_src\Uart.c	  1011  **                                                                            **
; ..\mcal_src\Uart.c	  1012  ** Return value    :  None                                                    **
; ..\mcal_src\Uart.c	  1013  **                                                                            **
; ..\mcal_src\Uart.c	  1014  ** Description : Driver Module DeInitialization function.                     **
; ..\mcal_src\Uart.c	  1015  ** Service for UART de initialization.                                        **
; ..\mcal_src\Uart.c	  1016  *******************************************************************************/
; ..\mcal_src\Uart.c	  1017  void Uart_DeInit(void)
; ..\mcal_src\Uart.c	  1018  {
; ..\mcal_src\Uart.c	  1019    Ifx_ASCLIN*  HwModulePtr;
; ..\mcal_src\Uart.c	  1020    const Uart_ChannelType   *ChannelConfigPtr;
; ..\mcal_src\Uart.c	  1021    uint32 TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	  1022    uint8 ModuleNo,Chan;
; ..\mcal_src\Uart.c	  1023    uint8 MaxChannel;
; ..\mcal_src\Uart.c	  1024  
; ..\mcal_src\Uart.c	  1025    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  1026     Std_ReturnType ReturnStatus;
; ..\mcal_src\Uart.c	  1027     ReturnStatus = E_OK;
; ..\mcal_src\Uart.c	  1028    #endif
; ..\mcal_src\Uart.c	  1029  
; ..\mcal_src\Uart.c	  1030    #if (UART_DEV_ERROR_DETECT == STD_ON) /* if DET detection is switched On */
; ..\mcal_src\Uart.c	  1031    if (Uart_InitStatus == UART_UNINITIALISED)
; ..\mcal_src\Uart.c	  1032     {
; ..\mcal_src\Uart.c	  1033       /* If Uart is already unintialised Report to  DET */
; ..\mcal_src\Uart.c	  1034       Det_ReportError(
; ..\mcal_src\Uart.c	  1035                        UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1036                        UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1037                        UART_SID_DEINIT,
; ..\mcal_src\Uart.c	  1038                        UART_E_UNINIT
; ..\mcal_src\Uart.c	  1039                       );
; ..\mcal_src\Uart.c	  1040       ReturnStatus = E_NOT_OK;
; ..\mcal_src\Uart.c	  1041     }
; ..\mcal_src\Uart.c	  1042    #endif  /* (_DEV_ERROR_DETECT == STD_ON) */
; ..\mcal_src\Uart.c	  1043  
; ..\mcal_src\Uart.c	  1044    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  1045    if (ReturnStatus == E_OK)
; ..\mcal_src\Uart.c	  1046    #endif
; ..\mcal_src\Uart.c	  1047    {
; ..\mcal_src\Uart.c	  1048      MaxChannel = Uart_kConfigPtr->NoOfChannels;
; ..\mcal_src\Uart.c	  1049  
; ..\mcal_src\Uart.c	  1050      for (Chan = UART_ZERO_U; Chan < MaxChannel; Chan++)
; ..\mcal_src\Uart.c	  1051      {
; ..\mcal_src\Uart.c	  1052        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1053         ChannelConfigPtr to access the Configuration of a particular Channel*/
; ..\mcal_src\Uart.c	  1054        ChannelConfigPtr = &(Uart_kConfigPtr->ChannelConfigPtr[Chan]);
; ..\mcal_src\Uart.c	  1055        /* Extract the Hw module */
; ..\mcal_src\Uart.c	  1056        ModuleNo = ChannelConfigPtr->HwModule;
; ..\mcal_src\Uart.c	  1057        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1058         UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	  1059        HwModulePtr = &(UART_HW_MODULE[ModuleNo]);
; ..\mcal_src\Uart.c	  1060  
; ..\mcal_src\Uart.c	  1061        /* Disable the Clock source */
; ..\mcal_src\Uart.c	  1062        UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->CSR.U,\ 
; ..\mcal_src\Uart.c	  1063                             UART_CSR_CLKSEL_CLEARMASK, UART_CSRREG_CLKSEL_NOCLK)
; ..\mcal_src\Uart.c	  1064  
; ..\mcal_src\Uart.c	  1065        TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	  1066        /* provide delay of  TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	  1067        /*Wait TW or poll for CSR.CON = 0*/
; ..\mcal_src\Uart.c	  1068        while ((((UART_SFR_DEINIT_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	  1069             UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) !=UART_BIT_RESET) &&\ 
; ..\mcal_src\Uart.c	  1070                                                              (TimeOutCount > 0U))
; ..\mcal_src\Uart.c	  1071        {
; ..\mcal_src\Uart.c	  1072          TimeOutCount-- ;
; ..\mcal_src\Uart.c	  1073        }
; ..\mcal_src\Uart.c	  1074  
; ..\mcal_src\Uart.c	  1075        /* Change to  INIT mode */
; ..\mcal_src\Uart.c	  1076        UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
; ..\mcal_src\Uart.c	  1077                      UART_FRAMECON_MODE_CLEARMASK, UART_FRAMECONREG_INIT_MODE)
; ..\mcal_src\Uart.c	  1078        /* Connect the Clock source */
; ..\mcal_src\Uart.c	  1079        UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->FRAMECON.U,\ 
; ..\mcal_src\Uart.c	  1080                             UART_CSR_CLKSEL_CLEARMASK, UART_CSRREG_CLKSEL_CLC)
; ..\mcal_src\Uart.c	  1081  
; ..\mcal_src\Uart.c	  1082        TimeOutCount = UART_TIMEOUT_DURATION;
; ..\mcal_src\Uart.c	  1083        /* provide delay of TW >= 4 * (1/fA) + 2 * (1/fCLC) clock cycles */
; ..\mcal_src\Uart.c	  1084        /* Wait TW or poll for CSR.CON = 1 */
; ..\mcal_src\Uart.c	  1085        while ((((UART_SFR_DEINIT_USER_MODE_READ32(HwModulePtr->CSR.U) &\ 
; ..\mcal_src\Uart.c	  1086               UART_CSR_CON_MASK) >> UART_CSR_CON_BITPOS) !=UART_BIT_SET) && \ 
; ..\mcal_src\Uart.c	  1087                                                              (TimeOutCount > 0U))
; ..\mcal_src\Uart.c	  1088        {
; ..\mcal_src\Uart.c	  1089          TimeOutCount-- ;
; ..\mcal_src\Uart.c	  1090        }
; ..\mcal_src\Uart.c	  1091  
; ..\mcal_src\Uart.c	  1092        /* Init the Uart_BusChannelMap Array */
; ..\mcal_src\Uart.c	  1093        Uart_BusChannelMap[ModuleNo] = UART_INVALID_CHANNEL;
; ..\mcal_src\Uart.c	  1094  
; ..\mcal_src\Uart.c	  1095        /* Reset the channel Info*/
; ..\mcal_src\Uart.c	  1096        Uart_ChannelInfo[Chan].Uart_TxState = UART_UNINITIALISED;
; ..\mcal_src\Uart.c	  1097        Uart_ChannelInfo[Chan].Uart_RxState = UART_UNINITIALISED;
; ..\mcal_src\Uart.c	  1098        Uart_ChannelInfo[Chan].Uart_TxDataLeft = UART_ZERO_U;
; ..\mcal_src\Uart.c	  1099        Uart_ChannelInfo[Chan].Uart_RxDataLeft = UART_ZERO_U;
; ..\mcal_src\Uart.c	  1100        Uart_ChannelInfo[Chan].Uart_TotalDataTxd= UART_ZERO_U;
; ..\mcal_src\Uart.c	  1101        Uart_ChannelInfo[Chan].Uart_TotalDataRxd= UART_ZERO_U;
; ..\mcal_src\Uart.c	  1102        Uart_ChannelInfo[Chan].Uart_TxBuffPtr = NULL_PTR;
; ..\mcal_src\Uart.c	  1103        Uart_ChannelInfo[Chan].Uart_RxBuffPtr = NULL_PTR;
; ..\mcal_src\Uart.c	  1104        Uart_ChannelInfo[Chan].Uart_TxDataCopyCntr = UART_ZERO_U;
; ..\mcal_src\Uart.c	  1105        Uart_ChannelInfo[Chan].Uart_AssignedHW = UART_INVALID_CHANNEL;
; ..\mcal_src\Uart.c	  1106  
; ..\mcal_src\Uart.c	  1107        /* Reset the Uart Channel Tx/Rx Resource Lock */
; ..\mcal_src\Uart.c	  1108        Uart_ChLock[UART_TX_LOCK_IDX][Chan] = UART_ZERO_U;
; ..\mcal_src\Uart.c	  1109        Uart_ChLock[UART_RX_LOCK_IDX][Chan] = UART_ZERO_U;
; ..\mcal_src\Uart.c	  1110  
; ..\mcal_src\Uart.c	  1111        /* Clear the read and write Interrupts*/
; ..\mcal_src\Uart.c	  1112        Uart_lClearReadInterrupts(HwModulePtr, UART_DEINIT_ACCESS);
; ..\mcal_src\Uart.c	  1113        Uart_lClearWriteInterrupts(HwModulePtr, UART_DEINIT_ACCESS);
; ..\mcal_src\Uart.c	  1114  
; ..\mcal_src\Uart.c	  1115        /* Disable Tx,Rx and Err Interrupts*/
; ..\mcal_src\Uart.c	  1116        Uart_lHwDisableAscLinTxIntr(ModuleNo, UART_DEINIT_ACCESS);
; ..\mcal_src\Uart.c	  1117        Uart_lHwDisableAscLinRxIntr(ModuleNo, UART_DEINIT_ACCESS);
; ..\mcal_src\Uart.c	  1118        Uart_lHwDisableAscLinErrIntr(ModuleNo, UART_DEINIT_ACCESS);
; ..\mcal_src\Uart.c	  1119  
; ..\mcal_src\Uart.c	  1120        /* Reset ASCLIN Kernel  */
; ..\mcal_src\Uart.c	  1121        Uart_lHwInitKernelRegDeInit(ModuleNo);
; ..\mcal_src\Uart.c	  1122  
; ..\mcal_src\Uart.c	  1123        /* Reset End Init Protection */
; ..\mcal_src\Uart.c	  1124        UART_SFR_DEINIT_RESETENDINIT();
; ..\mcal_src\Uart.c	  1125  
; ..\mcal_src\Uart.c	  1126        /* Disable the ASCLIN module */
; ..\mcal_src\Uart.c	  1127        UART_SFR_DEINIT_WRITE32(HwModulePtr->CLC.U, UART_DISABLE_ASCLIN_MODULE);
; ..\mcal_src\Uart.c	  1128  
; ..\mcal_src\Uart.c	  1129        /* Set End Init Protection */
; ..\mcal_src\Uart.c	  1130        UART_SFR_DEINIT_SETENDINIT();
; ..\mcal_src\Uart.c	  1131      }
; ..\mcal_src\Uart.c	  1132  
; ..\mcal_src\Uart.c	  1133      #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  1134       /* Store UART driver initailization status */
; ..\mcal_src\Uart.c	  1135      Uart_InitStatus = UART_UNINITIALISED;
; ..\mcal_src\Uart.c	  1136      #endif/*(UART_DEV_ERROR_DETECT == STD_ON)*/
; ..\mcal_src\Uart.c	  1137    }
; ..\mcal_src\Uart.c	  1138    return ;
; ..\mcal_src\Uart.c	  1139  }
; ..\mcal_src\Uart.c	  1140  #endif
; ..\mcal_src\Uart.c	  1141  /*******************************************************************************
; ..\mcal_src\Uart.c	  1142  ** Traceability : [cover parentID=DS_NAS_UART_PR1571,DS_NAS_UART_PR63_8]
; ..\mcal_src\Uart.c	  1143  **
; ..\mcal_src\Uart.c	  1144  ** Syntax : Uart_ReturnType Uart_Read                                         **
; ..\mcal_src\Uart.c	  1145  **  (                                                                         **
; ..\mcal_src\Uart.c	  1146  **    Uart_ChannelIdType channel,Uart_MemPtrType MemPtr,Uart_SizeType Size    **
; ..\mcal_src\Uart.c	  1147  **  )                                                                         **
; ..\mcal_src\Uart.c	  1148  **                    [/cover]                                                **
; ..\mcal_src\Uart.c	  1149  ** Service ID:  0x02                                                          **
; ..\mcal_src\Uart.c	  1150  **                                                                            **
; ..\mcal_src\Uart.c	  1151  ** Sync/Async:  Asynchronous                                                  **
; ..\mcal_src\Uart.c	  1152  **                                                                            **
; ..\mcal_src\Uart.c	  1153  ** Reentrancy:  Reentrant(Not for same channel)                               **
; ..\mcal_src\Uart.c	  1154  **                                                                            **
; ..\mcal_src\Uart.c	  1155  ** Parameters (in): Channel - Uart channel to be addressed .                  **
; ..\mcal_src\Uart.c	  1156  **                  MemPtr - Pointer to location where data needs to be stored**
; ..\mcal_src\Uart.c	  1157  **                  Size - No of data that needs to be recieved               **
; ..\mcal_src\Uart.c	  1158  **                                                                            **
; ..\mcal_src\Uart.c	  1159  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1160  **                                                                            **
; ..\mcal_src\Uart.c	  1161  ** Return value    : UART_OK - Read operation was initiaited successfully     **
; ..\mcal_src\Uart.c	  1162  **                   UART_NOT_OK - Read operation couldn't be initiated       **
; ..\mcal_src\Uart.c	  1163  **                   due to developement errors                               **
; ..\mcal_src\Uart.c	  1164  **                   UART_IS_BUSY - Uart channel is busy with other           **
; ..\mcal_src\Uart.c	  1165  **                   read operation                                           **
; ..\mcal_src\Uart.c	  1166  **                                                                            **
; ..\mcal_src\Uart.c	  1167  ** Description : Api to configure the given UART Channel for reception of the **
; ..\mcal_src\Uart.c	  1168  **       specified number of data bytes and the memory location (App Rx Buff) **
; ..\mcal_src\Uart.c	  1169  **        to be used to store the received data                               **
; ..\mcal_src\Uart.c	  1170  *******************************************************************************/
; ..\mcal_src\Uart.c	  1171  
; ..\mcal_src\Uart.c	  1172  Uart_ReturnType Uart_Read\ 
; Function Uart_Read
.L91:
Uart_Read:	.type	func

; ..\mcal_src\Uart.c	  1173           (Uart_ChannelIdType Channel,Uart_MemPtrType MemPtr,Uart_SizeType Size)
; ..\mcal_src\Uart.c	  1174  {
; ..\mcal_src\Uart.c	  1175    Ifx_ASCLIN*  HwModulePtr;
; ..\mcal_src\Uart.c	  1176    Uart_ReturnType Retvalue = UART_OK;
; ..\mcal_src\Uart.c	  1177    uint8 HwUnit;
; ..\mcal_src\Uart.c	  1178    uint8 TempIntLevel;
; ..\mcal_src\Uart.c	  1179  
; ..\mcal_src\Uart.c	  1180   #if (UART_DEV_ERROR_DETECT != STD_ON)
; ..\mcal_src\Uart.c	  1181    uint32 ChRxLock;
; ..\mcal_src\Uart.c	  1182   #endif
; ..\mcal_src\Uart.c	  1183  
; ..\mcal_src\Uart.c	  1184    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  1185    if (Uart_InitStatus == UART_UNINITIALISED)
; ..\mcal_src\Uart.c	  1186    {
; ..\mcal_src\Uart.c	  1187      /*If Uart is not initialised then  Report to  DET */
; ..\mcal_src\Uart.c	  1188      Det_ReportError(
; ..\mcal_src\Uart.c	  1189                      UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1190                      UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1191                      UART_SID_READ,
; ..\mcal_src\Uart.c	  1192                      UART_E_UNINIT
; ..\mcal_src\Uart.c	  1193                     );
; ..\mcal_src\Uart.c	  1194      Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  1195    }
; ..\mcal_src\Uart.c	  1196    else if (MemPtr == NULL_PTR)
; ..\mcal_src\Uart.c	  1197    {
; ..\mcal_src\Uart.c	  1198      /*If memory pointer is equal to null pointer Report to  DET */
; ..\mcal_src\Uart.c	  1199      Det_ReportError(
; ..\mcal_src\Uart.c	  1200                      UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1201                      UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1202                      UART_SID_READ,
; ..\mcal_src\Uart.c	  1203                      UART_E_PARAM_POINTER
; ..\mcal_src\Uart.c	  1204                     );
; ..\mcal_src\Uart.c	  1205      Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  1206    }
; ..\mcal_src\Uart.c	  1207    else if (Size == UART_ZERO_U )
; ..\mcal_src\Uart.c	  1208    {
; ..\mcal_src\Uart.c	  1209      /* If the size is equal to zero Report to  DET */
; ..\mcal_src\Uart.c	  1210      Det_ReportError(
; ..\mcal_src\Uart.c	  1211                      UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1212                      UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1213                      UART_SID_READ,
; ..\mcal_src\Uart.c	  1214                      UART_E_INVALID_SIZE
; ..\mcal_src\Uart.c	  1215                     );
; ..\mcal_src\Uart.c	  1216      Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  1217    }
; ..\mcal_src\Uart.c	  1218    else
; ..\mcal_src\Uart.c	  1219    {
; ..\mcal_src\Uart.c	  1220      /* If the channelId is Invalid Report to DET */
; ..\mcal_src\Uart.c	  1221      Retvalue = Uart_lChannelCheck(Channel,UART_SID_READ);
; ..\mcal_src\Uart.c	  1222  
; ..\mcal_src\Uart.c	  1223      if (Retvalue == UART_OK)
; ..\mcal_src\Uart.c	  1224      {
; ..\mcal_src\Uart.c	  1225        /* If the channel is busy then Report to DET */
; ..\mcal_src\Uart.c	  1226        if (Uart_ChannelInfo[Channel].Uart_RxState != UART_INITIALISED)
; ..\mcal_src\Uart.c	  1227        {
; ..\mcal_src\Uart.c	  1228          /*If channel is busy then Report to  DET */
; ..\mcal_src\Uart.c	  1229          Det_ReportError(
; ..\mcal_src\Uart.c	  1230                          UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1231                          UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1232                          UART_SID_READ,
; ..\mcal_src\Uart.c	  1233                          UART_E_STATE_TRANSITION
; ..\mcal_src\Uart.c	  1234                          );
; ..\mcal_src\Uart.c	  1235          Retvalue = UART_IS_BUSY;
; ..\mcal_src\Uart.c	  1236        }
; ..\mcal_src\Uart.c	  1237      }
; ..\mcal_src\Uart.c	  1238    }
; ..\mcal_src\Uart.c	  1239    #endif
; ..\mcal_src\Uart.c	  1240    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  1241    if (Retvalue == UART_OK)
; ..\mcal_src\Uart.c	  1242    #else
; ..\mcal_src\Uart.c	  1243     /* The Uart Read operation is already started for the channel. No other
; ..\mcal_src\Uart.c	  1244        operation can be started on this Channel. This is implemented using a
; ..\mcal_src\Uart.c	  1245        binary semaphore mechanism */
; ..\mcal_src\Uart.c	  1246     ChRxLock = Mcal_LockResource(&Uart_ChLock[UART_RX_LOCK_IDX][Channel]);
	mov	d15,d4
	movh.a	a15,#@his(Uart_ChLock)
.L661:
	lea	a15,[a15]@los(Uart_ChLock)
.L797:
	mov	d8,d5
	add.a	a15,#4
.L666:
	mov	d9,#0
	mov.aa	a12,a4
.L665:
	addsc.a	a4,a15,d15,#2
.L660:
	call	Mcal_LockResource
.L659:

; ..\mcal_src\Uart.c	  1247  
; ..\mcal_src\Uart.c	  1248     /* No other Uart Read operation is in progress on the same Channel */
; ..\mcal_src\Uart.c	  1249     if (ChRxLock == MCAL_RESOURCE_BUSY)
	jne	d2,#1,.L19
.L798:

; ..\mcal_src\Uart.c	  1250     {
; ..\mcal_src\Uart.c	  1251          Retvalue = UART_IS_BUSY;
	mov	d9,#2
	j	.L20
.L19:

; ..\mcal_src\Uart.c	  1252     }
; ..\mcal_src\Uart.c	  1253     else
; ..\mcal_src\Uart.c	  1254    #endif
; ..\mcal_src\Uart.c	  1255    {
; ..\mcal_src\Uart.c	  1256      /* Update Uart_ChannelInfo for Read Operation */
; ..\mcal_src\Uart.c	  1257      Uart_ChannelInfo[Channel].Uart_RxState= UART_OPERATION_IN_PROGRESS;
	mul	d15,d15,#20
	fcall	.cocofun_13
.L662:
	addsc.a	a2,a15,d15,#0
.L799:
	mov	d15,#2
.L800:
	st.b	[a2]9,d15
.L801:

; ..\mcal_src\Uart.c	  1258      Uart_ChannelInfo[Channel].Uart_RxBuffPtr= MemPtr;
	st.a	[a2],a12
.L802:

; ..\mcal_src\Uart.c	  1259      Uart_ChannelInfo[Channel].Uart_RxDataLeft = Size;
	st.h	[a2]12,d8
.L803:

; ..\mcal_src\Uart.c	  1260      Uart_ChannelInfo[Channel].Uart_TotalDataRxd =UART_ZERO_U;
	mov	d15,#0
	st.h	[a2]16,d15
.L804:

; ..\mcal_src\Uart.c	  1261  
; ..\mcal_src\Uart.c	  1262      HwUnit = Uart_ChannelInfo[Channel].Uart_AssignedHW;
	ld.bu	d8,[a2]18
.L667:

; ..\mcal_src\Uart.c	  1263    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1264      UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	  1265      HwModulePtr= &(UART_HW_MODULE[HwUnit]);
	lea	a15,0xf0000600
.L805:
	sha	d15,d8,#8
.L806:
	addsc.a	a15,a15,d15,#0
.L395:

; ..\mcal_src\Uart.c	  1266  
; ..\mcal_src\Uart.c	  1267      /* Configure RX Outlet Width */
; ..\mcal_src\Uart.c	  1268      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
	ld.w	d15,[a15]16
	insert	d15,d15,#1,#6,#2
	st.w	[a15]16,d15
.L396:

; ..\mcal_src\Uart.c	  1269                UART_RXFIFOCON_OUTW_CLEARMASK, UART_RXFIFOCONREG_OUTW_VAL <<\ 
; ..\mcal_src\Uart.c	  1270                                                      UART_RXFIFOCON_OUTW_BITPOS)
; ..\mcal_src\Uart.c	  1271  
; ..\mcal_src\Uart.c	  1272      if(Uart_ChannelInfo[Channel].Uart_RxDataLeft < UART_BUFFER_SIZE)
	ld.hu	d0,[a2]12
.L807:
	mov	d15,#16
.L668:
	jge.u	d0,d15,.L21
.L808:

; ..\mcal_src\Uart.c	  1273      {
; ..\mcal_src\Uart.c	  1274        /*Set RX FIFO Interrupt level according to Uart Size*/
; ..\mcal_src\Uart.c	  1275        TempIntLevel = (uint8)Uart_ChannelInfo[Channel].Uart_RxDataLeft\ 
; ..\mcal_src\Uart.c	  1276                                    - (uint8)(UART_ONE_U);
	add	d0,#-1
.L809:

; ..\mcal_src\Uart.c	  1277        UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
	extr.u	d0,d0,#0,#8
	ld.w	d15,[a15]16
.L398:
	insert	d15,d15,#0,#8,#4
.L810:
	sh	d0,d0,#8
	j	.L22
.L21:

; ..\mcal_src\Uart.c	  1278                  UART_RXFIFOCON_INTLEVEL_CLEARMASK,(uint32)(TempIntLevel) << \ 
; ..\mcal_src\Uart.c	  1279                                                   UART_RXFIFOCON_INTLEVEL_BITPOS)
; ..\mcal_src\Uart.c	  1280      }
; ..\mcal_src\Uart.c	  1281      else
; ..\mcal_src\Uart.c	  1282      {
; ..\mcal_src\Uart.c	  1283        /*Set RX FIFO Interrupt level to RX FIFO level*/
; ..\mcal_src\Uart.c	  1284        UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
	ld.w	d15,[a15]16
	mov	d0,#3840
.L22:
	or	d15,d0
	st.w	[a15]16,d15
.L400:

; ..\mcal_src\Uart.c	  1285                                   UART_RXFIFOCON_INTLEVEL_CLEARMASK,\ 
; ..\mcal_src\Uart.c	  1286           ((uint32)UART_RX_FIFO_INT_LEVEL_VAL << UART_RXFIFOCON_INTLEVEL_BITPOS))
; ..\mcal_src\Uart.c	  1287      }
; ..\mcal_src\Uart.c	  1288  
; ..\mcal_src\Uart.c	  1289       /*Flush the Rx FIFO*/
; ..\mcal_src\Uart.c	  1290      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  1291                               UART_RXFIFOCON_FLUSH_CLEARMASK, UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	  1292  
; ..\mcal_src\Uart.c	  1293      /*Enable Rx(RxFIFO level) and Err(Parity,framing,rx underflow) Interrupts*/
; ..\mcal_src\Uart.c	  1294      Uart_lEnableReadInterrupts(HwModulePtr);
	mov.aa	a4,a15
.L402:
	ld.w	d15,[a15]16
.L669:
	or	d15,#1
	st.w	[a15]16,d15
.L403:
	call	Uart_lEnableReadInterrupts
.L664:

; ..\mcal_src\Uart.c	  1295  
; ..\mcal_src\Uart.c	  1296       /*Enable Error and Rx Interrupts*/
; ..\mcal_src\Uart.c	  1297      Uart_lHwEnableAscLinRxIntr(HwUnit);
	mov	d4,d8
	call	Uart_lHwEnableAscLinRxIntr
.L811:

; ..\mcal_src\Uart.c	  1298      Uart_lHwEnableAscLinErrIntr(HwUnit);
	mov	d4,d8
	call	Uart_lHwEnableAscLinErrIntr
.L405:

; ..\mcal_src\Uart.c	  1299  
; ..\mcal_src\Uart.c	  1300       /*Enable Receiver and filling of RxFIFO*/
; ..\mcal_src\Uart.c	  1301      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
	ld.w	d15,[a15]16
.L670:
	or	d15,#2
	st.w	[a15]16,d15
.L20:

; ..\mcal_src\Uart.c	  1302            UART_RXFIFOCON_ENI_CLEARMASK, UART_ONE_U << UART_RXFIFOCON_ENI_BITPOS)
; ..\mcal_src\Uart.c	  1303    }
; ..\mcal_src\Uart.c	  1304    return Retvalue;
; ..\mcal_src\Uart.c	  1305  }
	mov	d2,d9
	ret
.L382:
	
__Uart_Read_function_end:
	.size	Uart_Read,__Uart_Read_function_end-Uart_Read
.L169:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lRead')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  1306  
; ..\mcal_src\Uart.c	  1307  
; ..\mcal_src\Uart.c	  1308  /*******************************************************************************
; ..\mcal_src\Uart.c	  1309  ** Syntax : static void Uart_lRead                                            **
; ..\mcal_src\Uart.c	  1310  **  (                                                                         **
; ..\mcal_src\Uart.c	  1311  **    Ifx_ASCLIN* HwModulePtr,Uart_ChannelIdType Channel                      **
; ..\mcal_src\Uart.c	  1312  **  )                                                                         **
; ..\mcal_src\Uart.c	  1313  **                                                                            **
; ..\mcal_src\Uart.c	  1314  ** Service ID: NA                                                             **
; ..\mcal_src\Uart.c	  1315  **                                                                            **
; ..\mcal_src\Uart.c	  1316  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  1317  **                                                                            **
; ..\mcal_src\Uart.c	  1318  ** Reentrancy:   Reentrant(Not for same channel)                              **
; ..\mcal_src\Uart.c	  1319  **                                                                            **
; ..\mcal_src\Uart.c	  1320  ** Parameters (in): Channel - Uart channel to be addressed                    **
; ..\mcal_src\Uart.c	  1321  **                  HwModulePtr - Asclin Hardware Pointer                     **
; ..\mcal_src\Uart.c	  1322  **                                                                            **
; ..\mcal_src\Uart.c	  1323  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1324  **                                                                            **
; ..\mcal_src\Uart.c	  1325  ** Return value    : None                                                     **
; ..\mcal_src\Uart.c	  1326  **                                                                            **
; ..\mcal_src\Uart.c	  1327  ** Description : local function to copy the data from the RX FIFO buffer to   **
; ..\mcal_src\Uart.c	  1328  **               the user specified memory location(App Buffer)               **
; ..\mcal_src\Uart.c	  1329  *******************************************************************************/
; ..\mcal_src\Uart.c	  1330  static void Uart_lRead(Ifx_ASCLIN* HwModulePtr,Uart_ChannelIdType Channel)
; Function Uart_lRead
.L93:
Uart_lRead:	.type	func

; ..\mcal_src\Uart.c	  1331  {
; ..\mcal_src\Uart.c	  1332    Uart_MemPtrType BuffPtr;
; ..\mcal_src\Uart.c	  1333    uint16 Read_Count;
; ..\mcal_src\Uart.c	  1334    uint8 TempIntLevel;
; ..\mcal_src\Uart.c	  1335    uint8 UartDatalen;
; ..\mcal_src\Uart.c	  1336  
; ..\mcal_src\Uart.c	  1337    BuffPtr =  Uart_ChannelInfo[Channel].Uart_RxBuffPtr;
	fcall	.cocofun_4
.L671:

; ..\mcal_src\Uart.c	  1338  
; ..\mcal_src\Uart.c	  1339    if (Uart_ChannelInfo[Channel].Uart_RxDataLeft <= UART_BUFFER_SIZE)
	ld.hu	d15,[a15]12
.L965:

; ..\mcal_src\Uart.c	  1340    {
; ..\mcal_src\Uart.c	  1341      /*Set Read byte Count equal to the amount of data Remaining */
; ..\mcal_src\Uart.c	  1342      Read_Count = Uart_ChannelInfo[Channel].Uart_RxDataLeft;
; ..\mcal_src\Uart.c	  1343    }
; ..\mcal_src\Uart.c	  1344    else
; ..\mcal_src\Uart.c	  1345    {
; ..\mcal_src\Uart.c	  1346      /*Set Read byte Count equal to RX FIFO Buffer Size*/
; ..\mcal_src\Uart.c	  1347      Read_Count = UART_BUFFER_SIZE;
; ..\mcal_src\Uart.c	  1348    }
; ..\mcal_src\Uart.c	  1349  
; ..\mcal_src\Uart.c	  1350    while (Read_Count > UART_ZERO_U)
	mov	d2,#0
	ld.a	a2,[a15]
.L672:
	min.u	d0,d15,#16
	j	.L24
.L25:

; ..\mcal_src\Uart.c	  1351    {
; ..\mcal_src\Uart.c	  1352      UartDatalen =(uint8)((UART_SFR_RUNTIME_USER_MODE_READ32\ 
	ld.w	d15,[a4]28
.L966:

; ..\mcal_src\Uart.c	  1353      (HwModulePtr->DATCON.U) & UART_DATCON_DATLEN_MASK) + UART_ONE_U);
	and	d15,#15
.L967:

; ..\mcal_src\Uart.c	  1354  
; ..\mcal_src\Uart.c	  1355      /*Check if the data to be read is the last one and datalen is 7/8*/
; ..\mcal_src\Uart.c	  1356      if ((Uart_ChannelInfo[Channel].Uart_RxDataLeft == UART_ONE_U) &&\ 
	add	d1,d15,#1
	ld.hu	d15,[a15]12
.L674:
	jne	d15,#1,.L26
.L968:

; ..\mcal_src\Uart.c	  1357                              (UartDatalen != UART_NINEBIT_DATLEN))
	mov	d15,#9
.L969:
	jeq	d15,d1,.L27
.L527:

; ..\mcal_src\Uart.c	  1358      {
; ..\mcal_src\Uart.c	  1359        UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
	ld.w	d15,[a4]16
	insert	d15,d15,#1,#6,#2
	st.w	[a4]16,d15
.L528:

; ..\mcal_src\Uart.c	  1360          UART_RXFIFOCON_OUTW_CLEARMASK, UART_ONE_U << UART_RXFIFOCON_OUTW_BITPOS)
; ..\mcal_src\Uart.c	  1361  
; ..\mcal_src\Uart.c	  1362        *BuffPtr = (Uart_MemType)UART_SFR_RUNTIME_USER_MODE_READ32\ 
	ld.w	d0,[a4]72
.L673:
	st.b	[a2+],d0
.L970:

; ..\mcal_src\Uart.c	  1363                                           (HwModulePtr->RXDATA.U);
; ..\mcal_src\Uart.c	  1364        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1365          BuffPtr to access next element of Application Data Buffer*/
; ..\mcal_src\Uart.c	  1366        BuffPtr++;
; ..\mcal_src\Uart.c	  1367  
; ..\mcal_src\Uart.c	  1368        /*Complete data is copied so set the Rx FIFO Interrupt level to Zero*/
; ..\mcal_src\Uart.c	  1369        UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  1370                                UART_RXFIFOCON_INTLEVEL_CLEARMASK, UART_ZERO_U)
; ..\mcal_src\Uart.c	  1371  
; ..\mcal_src\Uart.c	  1372        /*Complete data is copied so set Read count to Zero*/
; ..\mcal_src\Uart.c	  1373        Read_Count = UART_ZERO_U;
	mov	d0,#0
.L530:
	ld.w	d15,[a4]16
.L676:
	insert	d15,d15,#0,#8,#4
	st.w	[a4]16,d15
.L531:

; ..\mcal_src\Uart.c	  1374  
; ..\mcal_src\Uart.c	  1375        Uart_ChannelInfo[Channel].Uart_RxDataLeft = UART_ZERO_U;
	st.h	[a15]12,d2
.L971:
	j	.L28
.L27:
.L26:

; ..\mcal_src\Uart.c	  1376        Uart_ChannelInfo[Channel].Uart_TotalDataRxd += UART_ONE_U;
; ..\mcal_src\Uart.c	  1377      }
; ..\mcal_src\Uart.c	  1378      else
; ..\mcal_src\Uart.c	  1379      {
; ..\mcal_src\Uart.c	  1380        *BuffPtr = (Uart_MemType)UART_SFR_RUNTIME_USER_MODE_READ32\ 
	ld.w	d1,[a4]72
.L675:
	st.b	[a2+],d1
.L972:

; ..\mcal_src\Uart.c	  1381                                                  (HwModulePtr->RXDATA.U);
; ..\mcal_src\Uart.c	  1382        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1383          BuffPtr to access next element of Application Data Buffer*/
; ..\mcal_src\Uart.c	  1384        BuffPtr++;
; ..\mcal_src\Uart.c	  1385  
; ..\mcal_src\Uart.c	  1386        /*Reduce the Read count by step size(i.e. One Data Count)*/
; ..\mcal_src\Uart.c	  1387        Read_Count = Read_Count - UART_STEPSIZE;
	add	d0,#-1
.L677:

; ..\mcal_src\Uart.c	  1388        /*Update Uart_ChannelInfo for total data received and data
; ..\mcal_src\Uart.c	  1389         bytes remaining to be received*/
; ..\mcal_src\Uart.c	  1390        Uart_ChannelInfo[Channel].Uart_RxDataLeft -= UART_STEPSIZE;
	ld.hu	d1,[a15]12
.L973:
	extr.u	d0,d0,#0,#16
.L678:
	add	d15,d1,#-1
	st.h	[a15]12,d15
.L28:

; ..\mcal_src\Uart.c	  1391        Uart_ChannelInfo[Channel].Uart_TotalDataRxd += UART_STEPSIZE;
	ld.hu	d15,[a15]16
.L974:
	add	d15,#1
	st.h	[a15]16,d15
.L24:
	jne	d0,#0,.L25
.L975:

; ..\mcal_src\Uart.c	  1392      }
; ..\mcal_src\Uart.c	  1393    }
; ..\mcal_src\Uart.c	  1394  
; ..\mcal_src\Uart.c	  1395    if(Uart_ChannelInfo[Channel].Uart_RxDataLeft < UART_BUFFER_SIZE)
	ld.hu	d15,[a15]12
.L976:
	mov	d0,#16
.L679:
	jge.u	d15,d0,.L29
.L977:

; ..\mcal_src\Uart.c	  1396    {
; ..\mcal_src\Uart.c	  1397      if (Uart_ChannelInfo[Channel].Uart_RxDataLeft > UART_ZERO_U)
	jeq	d15,#0,.L30
.L978:

; ..\mcal_src\Uart.c	  1398      {
; ..\mcal_src\Uart.c	  1399        /*Set RX FIFO Interrupt level according to the amount of data remainig*/
; ..\mcal_src\Uart.c	  1400        TempIntLevel = (uint8)Uart_ChannelInfo[Channel].Uart_RxDataLeft\ 
; ..\mcal_src\Uart.c	  1401                                                    - ((uint8)UART_ONE_U);
; ..\mcal_src\Uart.c	  1402        UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
	add	d0,d15,#-1
	ld.w	d15,[a4]16
.L533:
	sh	d0,d0,#8
.L680:

; ..\mcal_src\Uart.c	  1403                   UART_RXFIFOCON_INTLEVEL_CLEARMASK, (uint32)TempIntLevel << \ 
; ..\mcal_src\Uart.c	  1404                                                   UART_RXFIFOCON_INTLEVEL_BITPOS)
; ..\mcal_src\Uart.c	  1405      }
; ..\mcal_src\Uart.c	  1406      else
	insert	d15,d15,#0,#8,#4
	j	.L31
.L29:

; ..\mcal_src\Uart.c	  1407      {
; ..\mcal_src\Uart.c	  1408       /* Do Nothing */
; ..\mcal_src\Uart.c	  1409      }
; ..\mcal_src\Uart.c	  1410    }
; ..\mcal_src\Uart.c	  1411    else
; ..\mcal_src\Uart.c	  1412    {
; ..\mcal_src\Uart.c	  1413      /*Set the RX FIFO Interrupt level to RX FIFO Buffer Size*/
; ..\mcal_src\Uart.c	  1414       UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
	ld.w	d15,[a4]16
	mov	d0,#3840
.L31:
	or	d15,d0
	st.w	[a4]16,d15
.L30:

; ..\mcal_src\Uart.c	  1415                                        UART_RXFIFOCON_INTLEVEL_CLEARMASK,\ 
; ..\mcal_src\Uart.c	  1416           ((uint32)UART_RX_FIFO_INT_LEVEL_VAL << UART_RXFIFOCON_INTLEVEL_BITPOS))
; ..\mcal_src\Uart.c	  1417    }
; ..\mcal_src\Uart.c	  1418      /*Update Uart_ChannelInfo for Memptr*/
; ..\mcal_src\Uart.c	  1419    Uart_ChannelInfo[Channel].Uart_RxBuffPtr = BuffPtr;
	st.a	[a15],a2
.L979:

; ..\mcal_src\Uart.c	  1420  
; ..\mcal_src\Uart.c	  1421  }
	ret
.L519:
	
__Uart_lRead_function_end:
	.size	Uart_lRead,__Uart_lRead_function_end-Uart_lRead
.L204:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_4')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_4
.L95:
.cocofun_4:	.type	func
; Function body .cocofun_4, coco_iter:0
	mul	d15,d4,#20
	fcall	.cocofun_13
.L1088:
	addsc.a	a15,a15,d15,#0
.L1089:
	fret
.L279:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lEnableReadInterrupts')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  1422  
; ..\mcal_src\Uart.c	  1423  /*******************************************************************************
; ..\mcal_src\Uart.c	  1424  ** Syntax : static void Uart_lEnableReadInterrupts (Ifx_ASCLIN* HwModulePtr)  **
; ..\mcal_src\Uart.c	  1425  **                                                                            **
; ..\mcal_src\Uart.c	  1426  ** Service ID:  NA                                                            **
; ..\mcal_src\Uart.c	  1427  **                                                                            **
; ..\mcal_src\Uart.c	  1428  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  1429  **                                                                            **
; ..\mcal_src\Uart.c	  1430  ** Reentrancy:      reentrant                                                 **
; ..\mcal_src\Uart.c	  1431  **                                                                            **
; ..\mcal_src\Uart.c	  1432  ** Parameters (in): HwModulePtr - Asclin Hardware Pointer                     **
; ..\mcal_src\Uart.c	  1433  **                                                                            **
; ..\mcal_src\Uart.c	  1434  **                                                                            **
; ..\mcal_src\Uart.c	  1435  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1436  **                                                                            **
; ..\mcal_src\Uart.c	  1437  ** Return value    : None                                                     **
; ..\mcal_src\Uart.c	  1438  **                                                                            **
; ..\mcal_src\Uart.c	  1439  ** Description : Local function to Enable Rx and Err Interrupts in read fnt   **
; ..\mcal_src\Uart.c	  1440  **                                                                            **
; ..\mcal_src\Uart.c	  1441  *******************************************************************************/
; ..\mcal_src\Uart.c	  1442  static void Uart_lEnableReadInterrupts(Ifx_ASCLIN* HwModulePtr)
; Function Uart_lEnableReadInterrupts
.L97:
Uart_lEnableReadInterrupts:	.type	func

; ..\mcal_src\Uart.c	  1443  {
; ..\mcal_src\Uart.c	  1444    /*Enable Parity, Framing, RxFIFO Underflow and RxFIFO Level Interrupts*/
; ..\mcal_src\Uart.c	  1445    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSENABLE.U,\ 
	ld.w	d15,[a4]64
	insert	d15,d15,#1,#16,#1
.L681:
	fcall	.cocofun_5
.L548:

; ..\mcal_src\Uart.c	  1446                 UART_FLAGSENABLE_PEE_CLEARMASK,((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1447                                                     UART_FLAGSENABLE_PEE_BITPOS))
; ..\mcal_src\Uart.c	  1448  
; ..\mcal_src\Uart.c	  1449    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSENABLE.U,\ 
	insert	d15,d15,#1,#18,#1
.L682:
	fcall	.cocofun_5
.L550:

; ..\mcal_src\Uart.c	  1450                UART_FLAGSENABLE_FEE_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1451                                                     UART_FLAGSENABLE_FEE_BITPOS))
; ..\mcal_src\Uart.c	  1452  
; ..\mcal_src\Uart.c	  1453    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSENABLE.U,\ 
	insert	d15,d15,#1,#28,#1
.L683:
	fcall	.cocofun_5
.L552:

; ..\mcal_src\Uart.c	  1454                UART_FLAGSENABLE_RFLE_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1455                                                    UART_FLAGSENABLE_RFLE_BITPOS))
; ..\mcal_src\Uart.c	  1456  
; ..\mcal_src\Uart.c	  1457    UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSENABLE.U,\ 
	insert	d15,d15,#1,#27,#1
	st.w	[a4]64,d15
.L554:

; ..\mcal_src\Uart.c	  1458                UART_FLAGSENABLE_RFUE_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1459                                                    UART_FLAGSENABLE_RFUE_BITPOS))
; ..\mcal_src\Uart.c	  1460  }
	ret
.L545:
	
__Uart_lEnableReadInterrupts_function_end:
	.size	Uart_lEnableReadInterrupts,__Uart_lEnableReadInterrupts_function_end-Uart_lEnableReadInterrupts
.L214:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_5')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_5
.L99:
.cocofun_5:	.type	func
; Function body .cocofun_5, coco_iter:0
	st.w	[a4]64,d15
.L1094:
	ld.w	d15,[a4]64
.L1095:
	fret
.L284:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lClearReadInterrupts')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  1461  
; ..\mcal_src\Uart.c	  1462  /*******************************************************************************
; ..\mcal_src\Uart.c	  1463  ** Syntax : static void Uart_lClearReadInterrupts (Ifx_ASCLIN* HwModulePtr,   **
; ..\mcal_src\Uart.c	  1464                                                           uint8 ApiAccessId)   **
; ..\mcal_src\Uart.c	  1465  **                                                                            **
; ..\mcal_src\Uart.c	  1466  ** Service ID:  NA                                                            **
; ..\mcal_src\Uart.c	  1467  **                                                                            **
; ..\mcal_src\Uart.c	  1468  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  1469  **                                                                            **
; ..\mcal_src\Uart.c	  1470  ** Reentrancy:      reentrant                                                 **
; ..\mcal_src\Uart.c	  1471  **                                                                            **
; ..\mcal_src\Uart.c	  1472  ** Parameters (in): HwModulePtr - Asclin Hardware Pointer                     **
; ..\mcal_src\Uart.c	  1473  **                  ApiAccessId - API Access type                             **
; ..\mcal_src\Uart.c	  1474  **                                                                            **
; ..\mcal_src\Uart.c	  1475  **                                                                            **
; ..\mcal_src\Uart.c	  1476  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1477  **                                                                            **
; ..\mcal_src\Uart.c	  1478  ** Return value    : None                                                     **
; ..\mcal_src\Uart.c	  1479  **                                                                            **
; ..\mcal_src\Uart.c	  1480  ** Description : Local function to Disable  Rx and Err Interrupts in read fnt **
; ..\mcal_src\Uart.c	  1481  **                                                                            **
; ..\mcal_src\Uart.c	  1482  *******************************************************************************/
; ..\mcal_src\Uart.c	  1483  static void Uart_lClearReadInterrupts(Ifx_ASCLIN* HwModulePtr,\ 
; Function Uart_lClearReadInterrupts
.L101:
Uart_lClearReadInterrupts:	.type	func

; ..\mcal_src\Uart.c	  1484                                                                uint8 ApiAccessId)
; ..\mcal_src\Uart.c	  1485  {
; ..\mcal_src\Uart.c	  1486    #if (UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  1487    #if (UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  1488    if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  1489    {
; ..\mcal_src\Uart.c	  1490      /*Clear Parity,Framing, RxFIFO Underflow and RxFIFO Level Interrupts*/
; ..\mcal_src\Uart.c	  1491      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1492                 UART_FLAGSCLEAR_PEC_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1493                                                      UART_FLAGSCLEAR_PEC_BITPOS))
; ..\mcal_src\Uart.c	  1494  
; ..\mcal_src\Uart.c	  1495      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1496                  UART_FLAGSCLEAR_FEC_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1497                                                      UART_FLAGSCLEAR_FEC_BITPOS))
; ..\mcal_src\Uart.c	  1498  
; ..\mcal_src\Uart.c	  1499      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1500                 UART_FLAGSCLEAR_RFLC_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1501                                                     UART_FLAGSCLEAR_RFLC_BITPOS))
; ..\mcal_src\Uart.c	  1502  
; ..\mcal_src\Uart.c	  1503      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1504                 UART_FLAGSCLEAR_RFUC_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1505                                                     UART_FLAGSCLEAR_RFUC_BITPOS))
; ..\mcal_src\Uart.c	  1506    }
; ..\mcal_src\Uart.c	  1507    else
; ..\mcal_src\Uart.c	  1508    #endif /*(UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  1509    #if (UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  1510    if (ApiAccessId == UART_DEINIT_ACCESS)
; ..\mcal_src\Uart.c	  1511    {
; ..\mcal_src\Uart.c	  1512      /*Clear Parity,Framing, RxFIFO Underflow and RxFIFO Level Interrupts*/
; ..\mcal_src\Uart.c	  1513      UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1514                 UART_FLAGSCLEAR_PEC_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1515                                                      UART_FLAGSCLEAR_PEC_BITPOS))
; ..\mcal_src\Uart.c	  1516  
; ..\mcal_src\Uart.c	  1517      UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1518                  UART_FLAGSCLEAR_FEC_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1519                                                      UART_FLAGSCLEAR_FEC_BITPOS))
; ..\mcal_src\Uart.c	  1520  
; ..\mcal_src\Uart.c	  1521      UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1522                 UART_FLAGSCLEAR_RFLC_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1523                                                     UART_FLAGSCLEAR_RFLC_BITPOS))
; ..\mcal_src\Uart.c	  1524  
; ..\mcal_src\Uart.c	  1525      UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1526                 UART_FLAGSCLEAR_RFUC_CLEARMASK, ((uint32)UART_ENABLE_BIT << \ 
; ..\mcal_src\Uart.c	  1527                                                     UART_FLAGSCLEAR_RFUC_BITPOS))
; ..\mcal_src\Uart.c	  1528    }
; ..\mcal_src\Uart.c	  1529    else
; ..\mcal_src\Uart.c	  1530    #endif /*(UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  1531    #endif /*(UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  1532    {
; ..\mcal_src\Uart.c	  1533      /*Clear Parity,Framing, RxFIFO Underflow and RxFIFO Level Interrupts*/
; ..\mcal_src\Uart.c	  1534      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a4]60
.L1030:

; ..\mcal_src\Uart.c	  1535              UART_FLAGSCLEAR_PEC_CLEARMASK) | ((unsigned_int)UART_ENABLE_BIT << \ 
	insert	d15,d15,#1,#16,#1
	st.w	[a4]60,d15
.L1031:

; ..\mcal_src\Uart.c	  1536                                                     UART_FLAGSCLEAR_PEC_BITPOS));
; ..\mcal_src\Uart.c	  1537  
; ..\mcal_src\Uart.c	  1538      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a4]60
.L1032:

; ..\mcal_src\Uart.c	  1539              UART_FLAGSCLEAR_FEC_CLEARMASK) | ((unsigned_int)UART_ENABLE_BIT << \ 
	insert	d15,d15,#1,#18,#1
	st.w	[a4]60,d15
.L1033:

; ..\mcal_src\Uart.c	  1540                                                     UART_FLAGSCLEAR_FEC_BITPOS));
; ..\mcal_src\Uart.c	  1541  
; ..\mcal_src\Uart.c	  1542      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a4]60
.L1034:

; ..\mcal_src\Uart.c	  1543             UART_FLAGSCLEAR_RFLC_CLEARMASK) | ((unsigned_int)UART_ENABLE_BIT << \ 
	insert	d15,d15,#1,#28,#1
	st.w	[a4]60,d15
.L1035:

; ..\mcal_src\Uart.c	  1544                                                    UART_FLAGSCLEAR_RFLC_BITPOS));
; ..\mcal_src\Uart.c	  1545  
; ..\mcal_src\Uart.c	  1546      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a4]60
.L1036:

; ..\mcal_src\Uart.c	  1547             UART_FLAGSCLEAR_RFUC_CLEARMASK) | ((unsigned_int)UART_ENABLE_BIT << \ 
	insert	d15,d15,#1,#27,#1
	st.w	[a4]60,d15
.L1037:

; ..\mcal_src\Uart.c	  1548                                                    UART_FLAGSCLEAR_RFUC_BITPOS));
; ..\mcal_src\Uart.c	  1549    }
; ..\mcal_src\Uart.c	  1550    #if ((UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_OFF) || \ 
; ..\mcal_src\Uart.c	  1551        ((UART_USER_MODE_RUNTIME_API_ENABLE == STD_OFF) && \ 
; ..\mcal_src\Uart.c	  1552        (UART_USER_MODE_DEINIT_API_ENABLE == STD_OFF)))
; ..\mcal_src\Uart.c	  1553        UNUSED_PARAMETER(ApiAccessId)
; ..\mcal_src\Uart.c	  1554        #endif
; ..\mcal_src\Uart.c	  1555  }
	ret
.L562:
	
__Uart_lClearReadInterrupts_function_end:
	.size	Uart_lClearReadInterrupts,__Uart_lClearReadInterrupts_function_end-Uart_lClearReadInterrupts
.L229:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_Write')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Uart_Write

; ..\mcal_src\Uart.c	  1556  
; ..\mcal_src\Uart.c	  1557  
; ..\mcal_src\Uart.c	  1558  /*******************************************************************************
; ..\mcal_src\Uart.c	  1559  ** Traceability : [cover parentID=DS_NAS_UART_PR1572,DS_NAS_UART_PR63_9]      **
; ..\mcal_src\Uart.c	  1560  ** Syntax : Uart_ReturnType Uart_Write                                        **
; ..\mcal_src\Uart.c	  1561  **  (                                                                         **
; ..\mcal_src\Uart.c	  1562  **    Uart_ChannelIdType channel,Uart_MemPtrType MemPtr,Uart_SizeType Size    **
; ..\mcal_src\Uart.c	  1563  **  )                                                                         **
; ..\mcal_src\Uart.c	  1564  **                    [/cover]                                                **
; ..\mcal_src\Uart.c	  1565  ** Service ID:  0x03                                                          **
; ..\mcal_src\Uart.c	  1566  **                                                                            **
; ..\mcal_src\Uart.c	  1567  ** Sync/Async:  Asynchronous                                                  **
; ..\mcal_src\Uart.c	  1568  **                                                                            **
; ..\mcal_src\Uart.c	  1569  ** Reentrancy:  Reentrant  (Not for the same channel)                         **
; ..\mcal_src\Uart.c	  1570  **                                                                            **
; ..\mcal_src\Uart.c	  1571  ** Parameters (in): Channel - Uart channel to be addressed                    **
; ..\mcal_src\Uart.c	  1572  **                  MemPtr - Pointer to location where transmit data is stored**
; ..\mcal_src\Uart.c	  1573  **                  Size - No. of data bytes that needs to be transmitted     **
; ..\mcal_src\Uart.c	  1574  **                                                                            **
; ..\mcal_src\Uart.c	  1575  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1576  **                                                                            **
; ..\mcal_src\Uart.c	  1577  ** Return value    : UART_OK - Write operation was initiaited successfully    **
; ..\mcal_src\Uart.c	  1578  **                   UART_NOT_OK - Write operation couldn't performed         **
; ..\mcal_src\Uart.c	  1579  **                                 due to developement error                  **
; ..\mcal_src\Uart.c	  1580  **                   UART_IS_BUSY - Uart channel is busy with other           **
; ..\mcal_src\Uart.c	  1581  **                                  write operation                           **
; ..\mcal_src\Uart.c	  1582  **                                                                            **
; ..\mcal_src\Uart.c	  1583  ** Description : Api to transmit data from user memory location(App Buffer)   **
; ..\mcal_src\Uart.c	  1584  **               on to the given channel                                      **
; ..\mcal_src\Uart.c	  1585  **                                                                            **
; ..\mcal_src\Uart.c	  1586  *******************************************************************************/
; ..\mcal_src\Uart.c	  1587  Uart_ReturnType Uart_Write\ 
; Function Uart_Write
.L103:
Uart_Write:	.type	func
	mov	d8,d4
	mov.aa	a12,a4
.L685:
	mov	d9,d5
.L688:

; ..\mcal_src\Uart.c	  1588            (Uart_ChannelIdType Channel,Uart_MemPtrType MemPtr,Uart_SizeType Size)
; ..\mcal_src\Uart.c	  1589  {
; ..\mcal_src\Uart.c	  1590    const Uart_ChannelType *ChannelConfigPtr;
; ..\mcal_src\Uart.c	  1591    Ifx_ASCLIN* HwModulePtr;
; ..\mcal_src\Uart.c	  1592    Uart_ReturnType Retvalue = UART_OK;
; ..\mcal_src\Uart.c	  1593    uint8 HwUnit;
; ..\mcal_src\Uart.c	  1594  
; ..\mcal_src\Uart.c	  1595   #if (UART_DEV_ERROR_DETECT != STD_ON)
; ..\mcal_src\Uart.c	  1596    uint32 ChTxLock;
; ..\mcal_src\Uart.c	  1597   #endif
; ..\mcal_src\Uart.c	  1598  
; ..\mcal_src\Uart.c	  1599     #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  1600  
; ..\mcal_src\Uart.c	  1601     if (Uart_InitStatus == UART_UNINITIALISED)
; ..\mcal_src\Uart.c	  1602     {
; ..\mcal_src\Uart.c	  1603       /* If Uart is not initialised then report the DET */
; ..\mcal_src\Uart.c	  1604       Det_ReportError(
; ..\mcal_src\Uart.c	  1605                       UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1606                       UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1607                       UART_SID_WRITE,
; ..\mcal_src\Uart.c	  1608                       UART_E_UNINIT
; ..\mcal_src\Uart.c	  1609                      );
; ..\mcal_src\Uart.c	  1610       Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  1611     }
; ..\mcal_src\Uart.c	  1612     else if (MemPtr == NULL_PTR)
; ..\mcal_src\Uart.c	  1613     {
; ..\mcal_src\Uart.c	  1614       /* If the memory pointer passed is a Null Pointer report the DET */
; ..\mcal_src\Uart.c	  1615       Det_ReportError(
; ..\mcal_src\Uart.c	  1616                       UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1617                       UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1618                       UART_SID_WRITE,
; ..\mcal_src\Uart.c	  1619                       UART_E_PARAM_POINTER
; ..\mcal_src\Uart.c	  1620                      );
; ..\mcal_src\Uart.c	  1621       Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  1622     }
; ..\mcal_src\Uart.c	  1623     else if (Size == UART_ZERO_U )
; ..\mcal_src\Uart.c	  1624     {
; ..\mcal_src\Uart.c	  1625       /* If the parameter Size is equal to zero Report to  DET */
; ..\mcal_src\Uart.c	  1626       Det_ReportError(
; ..\mcal_src\Uart.c	  1627                       UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1628                       UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1629                       UART_SID_WRITE,
; ..\mcal_src\Uart.c	  1630                       UART_E_INVALID_SIZE
; ..\mcal_src\Uart.c	  1631                      );
; ..\mcal_src\Uart.c	  1632       Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  1633     }
; ..\mcal_src\Uart.c	  1634     else
; ..\mcal_src\Uart.c	  1635     {
; ..\mcal_src\Uart.c	  1636       /* If the channelId is Invalid Report to DET */
; ..\mcal_src\Uart.c	  1637       Retvalue = Uart_lChannelCheck(Channel,UART_SID_WRITE);
; ..\mcal_src\Uart.c	  1638  
; ..\mcal_src\Uart.c	  1639       if (Retvalue == UART_OK)
; ..\mcal_src\Uart.c	  1640       {
; ..\mcal_src\Uart.c	  1641         if (Uart_ChannelInfo[Channel].Uart_TxState != UART_INITIALISED)
; ..\mcal_src\Uart.c	  1642         {
; ..\mcal_src\Uart.c	  1643          /* If the channel is Busy(Read/Write on-going) Report to  DET */
; ..\mcal_src\Uart.c	  1644          Det_ReportError(
; ..\mcal_src\Uart.c	  1645                          UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1646                          UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1647                          UART_SID_WRITE,
; ..\mcal_src\Uart.c	  1648                          UART_E_STATE_TRANSITION
; ..\mcal_src\Uart.c	  1649                         );
; ..\mcal_src\Uart.c	  1650          Retvalue = UART_IS_BUSY;
; ..\mcal_src\Uart.c	  1651         }
; ..\mcal_src\Uart.c	  1652       }
; ..\mcal_src\Uart.c	  1653     }
; ..\mcal_src\Uart.c	  1654    #endif
; ..\mcal_src\Uart.c	  1655  
; ..\mcal_src\Uart.c	  1656    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  1657     if (Retvalue == UART_OK)
; ..\mcal_src\Uart.c	  1658    #else
; ..\mcal_src\Uart.c	  1659     /* The Uart Write operation is already started for the channel. No other
; ..\mcal_src\Uart.c	  1660        operation can be started on this Channel. This is implemented using a
; ..\mcal_src\Uart.c	  1661        binary semaphore mechanism*/
; ..\mcal_src\Uart.c	  1662     ChTxLock = Mcal_LockResource(&Uart_ChLock[UART_TX_LOCK_IDX][Channel]);
	mov	d10,#0
	fcall	.cocofun_8
.L816:
	addsc.a	a4,a2,d8,#2
.L687:
	call	Mcal_LockResource
.L686:

; ..\mcal_src\Uart.c	  1663  
; ..\mcal_src\Uart.c	  1664     /* No other Uart Write operation is in progress */
; ..\mcal_src\Uart.c	  1665     if (ChTxLock == MCAL_RESOURCE_BUSY)
	jne	d2,#1,.L32
.L817:

; ..\mcal_src\Uart.c	  1666     {
; ..\mcal_src\Uart.c	  1667          Retvalue = UART_IS_BUSY;
	mov	d10,#2
	j	.L33
.L32:

; ..\mcal_src\Uart.c	  1668     }
; ..\mcal_src\Uart.c	  1669     else
; ..\mcal_src\Uart.c	  1670    #endif
; ..\mcal_src\Uart.c	  1671     {
; ..\mcal_src\Uart.c	  1672     /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1673       ChannelConfigPtr to access the Configuration of Channel passed*/
; ..\mcal_src\Uart.c	  1674     ChannelConfigPtr = &(Uart_kConfigPtr->ChannelConfigPtr[Channel]);
	fcall	.cocofun_9
.L690:
	ld.a	a15,[a2]
.L818:
	sha	d15,d8,#5
.L819:
	addsc.a	a2,a15,d15,#0
.L692:

; ..\mcal_src\Uart.c	  1675      /*Update driver state in Uart_ChannelInfo as Write Operation*/
; ..\mcal_src\Uart.c	  1676     Uart_ChannelInfo[Channel].Uart_TxState= UART_OPERATION_IN_PROGRESS;
	fcall	.cocofun_10
	addsc.a	a4,a15,d15,#0
.L820:

; ..\mcal_src\Uart.c	  1677  
; ..\mcal_src\Uart.c	  1678     /* Extract asssigned HWUnit module ptr */
; ..\mcal_src\Uart.c	  1679     HwUnit = Uart_ChannelInfo[Channel].Uart_AssignedHW;
; ..\mcal_src\Uart.c	  1680    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1681     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	  1682     HwModulePtr= &(UART_HW_MODULE[HwUnit]);
	mov	d15,#2
	lea	a15,0xf0000600
.L821:
	st.b	[a4]8,d15
.L822:

; ..\mcal_src\Uart.c	  1683  
; ..\mcal_src\Uart.c	  1684     /* Set TX FIFO Inlet width */
; ..\mcal_src\Uart.c	  1685     UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  1686                UART_TXFIFOCON_INW_CLEARMASK, UART_TXFIFOCONREG_INW_VAL << \ 
; ..\mcal_src\Uart.c	  1687                                                        UART_TXFIFOCON_INW_BITPOS)
; ..\mcal_src\Uart.c	  1688     /* Set TX Datalength */
; ..\mcal_src\Uart.c	  1689     UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->DATCON.U,\ 
; ..\mcal_src\Uart.c	  1690                                         UART_DATCON_DATLEN_CLEARMASK,\ 
; ..\mcal_src\Uart.c	  1691                           ((uint32)ChannelConfigPtr->DataLength - UART_ONE_U))
; ..\mcal_src\Uart.c	  1692  
; ..\mcal_src\Uart.c	  1693     /*Update Tx BuffPtr,size in Uart_ChannelInfo for Write Operation*/
; ..\mcal_src\Uart.c	  1694     Uart_ChannelInfo[Channel].Uart_TxBuffPtr= MemPtr;
; ..\mcal_src\Uart.c	  1695     Uart_ChannelInfo[Channel].Uart_TxDataLeft = Size;
; ..\mcal_src\Uart.c	  1696     Uart_ChannelInfo[Channel].Uart_TotalDataTxd = UART_ZERO_U;
; ..\mcal_src\Uart.c	  1697  
; ..\mcal_src\Uart.c	  1698     /* Flush TX FIFO */
; ..\mcal_src\Uart.c	  1699      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  1700                                UART_TXFIFOCON_FLUSH_CLEARMASK, UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	  1701  
; ..\mcal_src\Uart.c	  1702     /*local function to copy the Tx data to FIFO*/
; ..\mcal_src\Uart.c	  1703     Uart_lWrite(HwModulePtr,Channel);
	mov	d4,d8
.L694:
	ld.bu	d11,[a4]18
.L695:
	sha	d15,d11,#8
.L823:
	addsc.a	a15,a15,d15,#0
.L417:
	ld.w	d15,[a15]12
	insert	d15,d15,#1,#6,#2
	st.w	[a15]12,d15
.L418:
	ld.w	d15,[a15]28
.L696:
	ld.bu	d0,[a2]25
	insert	d15,d15,#0,#0,#4
	add	d0,#-1
.L697:
	or	d15,d0
	st.w	[a15]28,d15
.L420:
	st.a	[a4]4,a12
.L824:
	mov	d15,#0
	st.h	[a4]10,d9
.L698:
	lea	a12,[a4]14
.L693:
	st.h	[a12],d15
.L825:
	mov.aa	a4,a15
.L422:
	ld.w	d15,[a15]12
.L699:
	or	d15,#1
	st.w	[a15]12,d15
.L423:
	call	Uart_lWrite
.L691:

; ..\mcal_src\Uart.c	  1704  
; ..\mcal_src\Uart.c	  1705     /*Check if only one data needs to be txd*/
; ..\mcal_src\Uart.c	  1706     if (Size == UART_ONE_U )
	jne	d9,#1,.L34
.L426:

; ..\mcal_src\Uart.c	  1707     {
; ..\mcal_src\Uart.c	  1708       /*Clear the Tx Complete flag*/
; ..\mcal_src\Uart.c	  1709       UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
	fcall	.cocofun_6
.L427:

; ..\mcal_src\Uart.c	  1710                                            UART_FLAGSCLEAR_TCC_CLEARMASK,\ 
; ..\mcal_src\Uart.c	  1711                          ((uint32)UART_ENABLE_BIT << UART_FLAGSCLEAR_TCC_BITPOS))
; ..\mcal_src\Uart.c	  1712       /*Enable the Tx Complete flag*/
; ..\mcal_src\Uart.c	  1713       UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSENABLE.U,\ 
	fcall	.cocofun_11
.L430:

; ..\mcal_src\Uart.c	  1714                                           UART_FLAGSENABLE_TCE_CLEARMASK,\ 
; ..\mcal_src\Uart.c	  1715                         ((uint32)UART_ENABLE_BIT << UART_FLAGSENABLE_TCE_BITPOS))
; ..\mcal_src\Uart.c	  1716       Uart_ChannelInfo[Channel].Uart_TotalDataTxd = UART_ONE_U;
	mov	d15,#1
	st.h	[a12],d15
.L704:
	j	.L35
.L34:

; ..\mcal_src\Uart.c	  1717     }
; ..\mcal_src\Uart.c	  1718     else
; ..\mcal_src\Uart.c	  1719     {
; ..\mcal_src\Uart.c	  1720       /*Enable Tx and Err Interrupts*/
; ..\mcal_src\Uart.c	  1721       Uart_lEnableWriteInterrupts(HwModulePtr, UART_RUNTIME_ACCESS);
	mov	d4,#3
	mov.aa	a4,a15
.L705:
	call	Uart_lEnableWriteInterrupts
.L706:

; ..\mcal_src\Uart.c	  1722       Uart_lHwEnableAscLinTxIntr(HwUnit);
	mov	d4,d11
	call	Uart_lHwEnableAscLinTxIntr
.L35:

; ..\mcal_src\Uart.c	  1723     }
; ..\mcal_src\Uart.c	  1724  
; ..\mcal_src\Uart.c	  1725     Uart_lHwEnableAscLinErrIntr(HwUnit);
	mov	d4,d11
	call	Uart_lHwEnableAscLinErrIntr
.L432:

; ..\mcal_src\Uart.c	  1726  
; ..\mcal_src\Uart.c	  1727     /*Enable the Tx FIFO*/
; ..\mcal_src\Uart.c	  1728     UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
	ld.w	d15,[a15]12
.L707:
	or	d15,#2
	st.w	[a15]12,d15
.L33:

; ..\mcal_src\Uart.c	  1729       UART_TXFIFOCON_ENO_CLEARMASK, UART_ENABLE_BIT << UART_TXFIFOCON_ENO_BITPOS)
; ..\mcal_src\Uart.c	  1730    }
; ..\mcal_src\Uart.c	  1731    return Retvalue;
; ..\mcal_src\Uart.c	  1732  }
	mov	d2,d10
	ret
.L407:
	
__Uart_Write_function_end:
	.size	Uart_Write,__Uart_Write_function_end-Uart_Write
.L174:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_11')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_11
.L105:
.cocofun_11:	.type	func
; Function body .cocofun_11, coco_iter:0
	ld.w	d15,[a15]64
.L703:
	insert	d15,d15,#1,#17,#1
	st.w	[a15]64,d15
.L702:
	fret
.L314:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_10')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_10
.L107:
.cocofun_10:	.type	func
; Function body .cocofun_10, coco_iter:0
	mul	d15,d8,#20
	fcall	.cocofun_13
.L689:
	fret
.L309:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_9')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_9
.L109:
.cocofun_9:	.type	func
; Function body .cocofun_9, coco_iter:0
	movh.a	a2,#@his(Uart_kConfigPtr)
	ld.a	a2,[a2]@los(Uart_kConfigPtr)
.L1113:
	fret
.L304:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_8')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_8
.L111:
.cocofun_8:	.type	func
; Function body .cocofun_8, coco_iter:0
	movh.a	a2,#@his(Uart_ChLock)
.L684:
	lea	a2,[a2]@los(Uart_ChLock)
.L1108:
	fret
.L299:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_6')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_6
.L113:
.cocofun_6:	.type	func
; Function body .cocofun_6, coco_iter:0
	ld.w	d15,[a15]60
.L701:
	insert	d15,d15,#1,#17,#1
	st.w	[a15]60,d15
.L700:
	fret
.L289:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lWrite')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  1733  /*******************************************************************************
; ..\mcal_src\Uart.c	  1734  ** Syntax : static void Uart_lWrite                                           **
; ..\mcal_src\Uart.c	  1735  **  (                                                                         **
; ..\mcal_src\Uart.c	  1736  **    Ifx_ASCLIN* HwModulePtr,Uart_ChannelIdType Channel                      **
; ..\mcal_src\Uart.c	  1737  **  )                                                                         **
; ..\mcal_src\Uart.c	  1738  **                                                                            **
; ..\mcal_src\Uart.c	  1739  ** Service ID:  NA                                                            **
; ..\mcal_src\Uart.c	  1740  **                                                                            **
; ..\mcal_src\Uart.c	  1741  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  1742  **                                                                            **
; ..\mcal_src\Uart.c	  1743  ** Reentrancy:      reentrant                                                 **
; ..\mcal_src\Uart.c	  1744  **                                                                            **
; ..\mcal_src\Uart.c	  1745  ** Parameters (in): Channel - Uart channel to be addressed                    **
; ..\mcal_src\Uart.c	  1746  **                  HwModulePtr - Asclin Hardware Pointer                     **
; ..\mcal_src\Uart.c	  1747  **                                                                            **
; ..\mcal_src\Uart.c	  1748  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1749  **                                                                            **
; ..\mcal_src\Uart.c	  1750  ** Return value    : None                                                     **
; ..\mcal_src\Uart.c	  1751  **                                                                            **
; ..\mcal_src\Uart.c	  1752  ** Description : local function to copy the transmit data from specified      **
; ..\mcal_src\Uart.c	  1753  **               memory location(App buffer) to TX FIFO Buffer                **
; ..\mcal_src\Uart.c	  1754  *******************************************************************************/
; ..\mcal_src\Uart.c	  1755  static void Uart_lWrite(Ifx_ASCLIN* HwModulePtr,Uart_ChannelIdType Channel)
; Function Uart_lWrite
.L115:
Uart_lWrite:	.type	func

; ..\mcal_src\Uart.c	  1756  {
; ..\mcal_src\Uart.c	  1757    Uart_MemPtrType BuffPtr;
; ..\mcal_src\Uart.c	  1758    uint16 Write_Count =UART_ZERO_U;
; ..\mcal_src\Uart.c	  1759    uint8 Uartdata = UART_ZERO_U;
; ..\mcal_src\Uart.c	  1760    uint8 UartDatalen ;
; ..\mcal_src\Uart.c	  1761  
; ..\mcal_src\Uart.c	  1762    BuffPtr = Uart_ChannelInfo[Channel].Uart_TxBuffPtr;
	fcall	.cocofun_4
.L708:
	mov.aa	a2,a15
	ld.a	a5,[+a2]4
.L709:

; ..\mcal_src\Uart.c	  1763    Uart_ChannelInfo[Channel].Uart_TxDataCopyCntr = UART_ZERO_U;
	mov	d15,#0
	st.b	[a15]19,d15
.L984:

; ..\mcal_src\Uart.c	  1764  
; ..\mcal_src\Uart.c	  1765    if (Uart_ChannelInfo[Channel].Uart_TxDataLeft == UART_STEPSIZE)
	ld.hu	d15,[a15]10
.L985:
	jne	d15,#1,.L37
.L986:

; ..\mcal_src\Uart.c	  1766    {
; ..\mcal_src\Uart.c	  1767       /*Set the Write byte Count to copy the last data*/
; ..\mcal_src\Uart.c	  1768      Write_Count = UART_STEPSIZE;
	mov	d0,#1
	j	.L38
.L37:

; ..\mcal_src\Uart.c	  1769    }
; ..\mcal_src\Uart.c	  1770    else if (Uart_ChannelInfo[Channel].Uart_TxDataLeft <= UART_BUFFER_SIZE)
	mov	d0,#16
.L987:
	jlt.u	d0,d15,.L39
.L988:

; ..\mcal_src\Uart.c	  1771    {
; ..\mcal_src\Uart.c	  1772      /*Set the Write byte Count to copy the Data left minus last data*/
; ..\mcal_src\Uart.c	  1773      Write_Count = (Uart_ChannelInfo[Channel].Uart_TxDataLeft - UART_STEPSIZE);
	add	d15,#-1
.L989:
	extr.u	d0,d15,#0,#16
	j	.L40
.L39:

; ..\mcal_src\Uart.c	  1774    }
; ..\mcal_src\Uart.c	  1775    else
; ..\mcal_src\Uart.c	  1776    {
; ..\mcal_src\Uart.c	  1777     /*Set the data copy  byte Count to Tx Fifo Buff Size */
; ..\mcal_src\Uart.c	  1778      Write_Count = UART_BUFFER_SIZE;
	mov	d0,#16
.L40:
.L38:

; ..\mcal_src\Uart.c	  1779    }
; ..\mcal_src\Uart.c	  1780  
; ..\mcal_src\Uart.c	  1781    while (Write_Count > UART_ZERO_U)
	mov	d2,#0
	j	.L41
.L42:

; ..\mcal_src\Uart.c	  1782    {
; ..\mcal_src\Uart.c	  1783       UartDatalen = ((uint8)(UART_SFR_RUNTIME_USER_MODE_READ32\ 
	ld.w	d15,[a4]28
.L990:

; ..\mcal_src\Uart.c	  1784                 (HwModulePtr->DATCON.U) & UART_DATCON_DATLEN_MASK) + UART_ONE_U);
	and	d15,#15
.L991:

; ..\mcal_src\Uart.c	  1785  
; ..\mcal_src\Uart.c	  1786     /*Check if the data to Write is the last one and datalen is 7/8*/
; ..\mcal_src\Uart.c	  1787       if ((Uart_ChannelInfo[Channel].Uart_TxDataLeft == UART_ONE_U) &&\ 
	add	d1,d15,#1
	ld.hu	d15,[a15]10
.L711:
	jne	d15,#1,.L43
.L992:

; ..\mcal_src\Uart.c	  1788                          (UartDatalen !=UART_NINEBIT_DATLEN))
	mov	d15,#9
.L993:
	jeq	d15,d1,.L44
.L542:

; ..\mcal_src\Uart.c	  1789       {
; ..\mcal_src\Uart.c	  1790         /* Set TX FIFO Inlet width to One as one byte TX data is there*/
; ..\mcal_src\Uart.c	  1791         UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
	ld.w	d15,[a4]12
	insert	d15,d15,#1,#6,#2
	st.w	[a4]12,d15
.L543:

; ..\mcal_src\Uart.c	  1792           UART_TXFIFOCON_INW_CLEARMASK, UART_ONE_U << UART_TXFIFOCON_INW_BITPOS)
; ..\mcal_src\Uart.c	  1793  
; ..\mcal_src\Uart.c	  1794         /*Copy the data from the mem to Tx FIFO buffer*/
; ..\mcal_src\Uart.c	  1795         Uartdata = *((uint8*)(void*)BuffPtr) ;
	ld.bu	d0,[a5+]
.L710:

; ..\mcal_src\Uart.c	  1796         UART_SFR_RUNTIME_USER_MODE_WRITE32(HwModulePtr->TXDATA.U, Uartdata);
	st.w	[a4]68,d0
.L994:

; ..\mcal_src\Uart.c	  1797         /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1798          BuffPtr to access next element of Application Data Buffer*/
; ..\mcal_src\Uart.c	  1799         BuffPtr++;
; ..\mcal_src\Uart.c	  1800  
; ..\mcal_src\Uart.c	  1801       /*Reset Data Copy counter and Tx Data left cntr to one as Tx is complete*/
; ..\mcal_src\Uart.c	  1802         Uart_ChannelInfo[Channel].Uart_TxDataLeft = UART_ZERO_U;
	st.h	[a15]10,d2
.L995:

; ..\mcal_src\Uart.c	  1803         Uart_ChannelInfo[Channel].Uart_TxDataCopyCntr += UART_ONE_U;
	ld.bu	d0,[a15]19
.L996:
	add	d0,#1
	st.b	[a15]19,d0
.L997:

; ..\mcal_src\Uart.c	  1804  
; ..\mcal_src\Uart.c	  1805         /* Reset Write count as Tx is complete*/
; ..\mcal_src\Uart.c	  1806         Write_Count = UART_ZERO_U;
	mov	d0,#0
	j	.L45
.L44:
.L43:

; ..\mcal_src\Uart.c	  1807       }
; ..\mcal_src\Uart.c	  1808       else
; ..\mcal_src\Uart.c	  1809       {
; ..\mcal_src\Uart.c	  1810         /*Copy the data from the mem to Tx FIFO buffer*/
; ..\mcal_src\Uart.c	  1811         UART_SFR_RUNTIME_USER_MODE_WRITE32(HwModulePtr->TXDATA.U, *( BuffPtr));
	ld.bu	d1,[a5+]
.L712:
	st.w	[a4]68,d1
.L998:

; ..\mcal_src\Uart.c	  1812         /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  1813          BuffPtr to access next element of Application Data Buffer*/
; ..\mcal_src\Uart.c	  1814         BuffPtr++;
; ..\mcal_src\Uart.c	  1815  
; ..\mcal_src\Uart.c	  1816     /*Decrement TxData Left Cntr and increment TxData Copy cntr by step size*/
; ..\mcal_src\Uart.c	  1817         Uart_ChannelInfo[Channel].Uart_TxDataLeft -= UART_STEPSIZE;
; ..\mcal_src\Uart.c	  1818         Uart_ChannelInfo[Channel].Uart_TxDataCopyCntr += UART_STEPSIZE;
; ..\mcal_src\Uart.c	  1819  
; ..\mcal_src\Uart.c	  1820         /*Decrement the Data copy counter by step size(i.e. one Data Count)*/
; ..\mcal_src\Uart.c	  1821         Write_Count = Write_Count - UART_STEPSIZE;
	add	d0,#-1
.L713:
	ld.hu	d1,[a15]10
.L999:
	extr.u	d0,d0,#0,#16
.L714:
	add	d15,d1,#-1
	st.h	[a15]10,d15
.L1000:
	ld.bu	d1,[a15]19
.L1001:
	add	d1,#1
	st.b	[a15]19,d1
.L45:
.L41:
	jne	d0,#0,.L42
.L1002:

; ..\mcal_src\Uart.c	  1822       }
; ..\mcal_src\Uart.c	  1823    }
; ..\mcal_src\Uart.c	  1824  
; ..\mcal_src\Uart.c	  1825    Uart_ChannelInfo[Channel].Uart_TxBuffPtr = BuffPtr;
	st.a	[a2],a5
.L1003:

; ..\mcal_src\Uart.c	  1826    return ;
; ..\mcal_src\Uart.c	  1827  }
	ret
.L536:
	
__Uart_lWrite_function_end:
	.size	Uart_lWrite,__Uart_lWrite_function_end-Uart_lWrite
.L209:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lEnableWriteInterrupts')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  1828  
; ..\mcal_src\Uart.c	  1829  /*******************************************************************************
; ..\mcal_src\Uart.c	  1830  ** Syntax : static void Uart_lEnableWriteInterrupts                           **
; ..\mcal_src\Uart.c	  1831  **                   ( Ifx_ASCLIN* HwModulePtr,uint8 ApiAccessId)             **
; ..\mcal_src\Uart.c	  1832  **                                                                            **
; ..\mcal_src\Uart.c	  1833  ** Service ID:  NA                                                            **
; ..\mcal_src\Uart.c	  1834  **                                                                            **
; ..\mcal_src\Uart.c	  1835  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  1836  **                                                                            **
; ..\mcal_src\Uart.c	  1837  ** Reentrancy:      reentrant                                                 **
; ..\mcal_src\Uart.c	  1838  **                                                                            **
; ..\mcal_src\Uart.c	  1839  ** Parameters (in):   HwModulePtr - Asclin Hardware Pointer                   **
; ..\mcal_src\Uart.c	  1840  **                :   ApiAccessId - API Access type                           **
; ..\mcal_src\Uart.c	  1841  **                                                                            **
; ..\mcal_src\Uart.c	  1842  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1843  **                                                                            **
; ..\mcal_src\Uart.c	  1844  ** Return value    : None                                                     **
; ..\mcal_src\Uart.c	  1845  **                                                                            **
; ..\mcal_src\Uart.c	  1846  ** Description : Local function to Enable Interrupts for Write Operation      **
; ..\mcal_src\Uart.c	  1847  **                                                                            **
; ..\mcal_src\Uart.c	  1848  *******************************************************************************/
; ..\mcal_src\Uart.c	  1849  static void Uart_lEnableWriteInterrupts(Ifx_ASCLIN* HwModulePtr,\ 
; Function Uart_lEnableWriteInterrupts
.L117:
Uart_lEnableWriteInterrupts:	.type	func

; ..\mcal_src\Uart.c	  1850                                                               uint8 ApiAccessId)
; ..\mcal_src\Uart.c	  1851  {
; ..\mcal_src\Uart.c	  1852  #if ((UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON) && \ 
; ..\mcal_src\Uart.c	  1853                                   (UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON))
; ..\mcal_src\Uart.c	  1854    if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  1855    {
; ..\mcal_src\Uart.c	  1856      /*Set Tx FIFO Int level and Enable Tx FIFO overflow and Level flags*/
; ..\mcal_src\Uart.c	  1857      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  1858                                 UART_TXFIFOCON_INTLEVEL_CLEARMASK, UART_ZERO_U)
; ..\mcal_src\Uart.c	  1859  
; ..\mcal_src\Uart.c	  1860      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSENABLE.U,\ 
; ..\mcal_src\Uart.c	  1861                                         UART_FLAGSENABLE_TFLE_CLEARMASK,\ 
; ..\mcal_src\Uart.c	  1862                        ((uint32)UART_ENABLE_BIT << UART_FLAGSENABLE_TFLE_BITPOS))
; ..\mcal_src\Uart.c	  1863  
; ..\mcal_src\Uart.c	  1864      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSENABLE.U,\ 
; ..\mcal_src\Uart.c	  1865                                          UART_FLAGSENABLE_TFOE_CLEARMASK,\ 
; ..\mcal_src\Uart.c	  1866                        ((uint32)UART_ENABLE_BIT << UART_FLAGSENABLE_TFOE_BITPOS))
; ..\mcal_src\Uart.c	  1867    }
; ..\mcal_src\Uart.c	  1868    else
; ..\mcal_src\Uart.c	  1869  #endif
; ..\mcal_src\Uart.c	  1870    {
; ..\mcal_src\Uart.c	  1871      /*Set Tx FIFO Int level and Enable Tx FIFO overflow and Level flags*/
; ..\mcal_src\Uart.c	  1872      HwModulePtr->TXFIFOCON.U = (unsigned_int)((HwModulePtr->TXFIFOCON.U &\ 
	ld.w	d15,[a4]12
.L1012:
	insert	d15,d15,#0,#8,#4
	st.w	[a4]12,d15
.L1013:

; ..\mcal_src\Uart.c	  1873                                UART_TXFIFOCON_INTLEVEL_CLEARMASK) | UART_ZERO_U);
; ..\mcal_src\Uart.c	  1874  
; ..\mcal_src\Uart.c	  1875      HwModulePtr->FLAGSENABLE.U = (unsigned_int)((HwModulePtr->FLAGSENABLE.U &\ 
	ld.w	d15,[a4]64
.L1014:

; ..\mcal_src\Uart.c	  1876                                         UART_FLAGSENABLE_TFLE_CLEARMASK) |\ 
	insert	d15,d15,#1,#31,#1
.L1015:
	fcall	.cocofun_5
.L1016:

; ..\mcal_src\Uart.c	  1877                 ((unsigned_int)UART_ENABLE_BIT << UART_FLAGSENABLE_TFLE_BITPOS));
; ..\mcal_src\Uart.c	  1878  
; ..\mcal_src\Uart.c	  1879      HwModulePtr->FLAGSENABLE.U = (unsigned_int)((HwModulePtr->FLAGSENABLE.U &\ 
; ..\mcal_src\Uart.c	  1880                                          UART_FLAGSENABLE_TFOE_CLEARMASK) |\ 
	insert	d15,d15,#1,#30,#1
	st.w	[a4]64,d15
.L1017:

; ..\mcal_src\Uart.c	  1881                 ((unsigned_int)UART_ENABLE_BIT << UART_FLAGSENABLE_TFOE_BITPOS));
; ..\mcal_src\Uart.c	  1882    }
; ..\mcal_src\Uart.c	  1883  #if ((UART_USER_MODE_RUNTIME_API_ENABLE == STD_OFF) || \ 
; ..\mcal_src\Uart.c	  1884                                 (UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_OFF))
; ..\mcal_src\Uart.c	  1885      UNUSED_PARAMETER(ApiAccessId)
; ..\mcal_src\Uart.c	  1886  #endif
; ..\mcal_src\Uart.c	  1887  }
	ret
.L556:
	
__Uart_lEnableWriteInterrupts_function_end:
	.size	Uart_lEnableWriteInterrupts,__Uart_lEnableWriteInterrupts_function_end-Uart_lEnableWriteInterrupts
.L219:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lClearWriteInterrupts')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  1888  /*******************************************************************************
; ..\mcal_src\Uart.c	  1889  ** Syntax : static void Uart_lClearWriteInterrupts                            **
; ..\mcal_src\Uart.c	  1890  **                   ( Ifx_ASCLIN* HwModulePtr,uint8 ApiAccessId)             **
; ..\mcal_src\Uart.c	  1891  **                                                                            **
; ..\mcal_src\Uart.c	  1892  ** Service ID:  NA                                                            **
; ..\mcal_src\Uart.c	  1893  **                                                                            **
; ..\mcal_src\Uart.c	  1894  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  1895  **                                                                            **
; ..\mcal_src\Uart.c	  1896  ** Reentrancy:      reentrant                                                 **
; ..\mcal_src\Uart.c	  1897  **                                                                            **
; ..\mcal_src\Uart.c	  1898  ** Parameters (in):   HwModulePtr - Asclin Hardware Pointer                   **
; ..\mcal_src\Uart.c	  1899  **                    ApiAccessId - Describes the API access type             **
; ..\mcal_src\Uart.c	  1900  **                                                                            **
; ..\mcal_src\Uart.c	  1901  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1902  **                                                                            **
; ..\mcal_src\Uart.c	  1903  ** Return value    : None                                                     **
; ..\mcal_src\Uart.c	  1904  **                                                                            **
; ..\mcal_src\Uart.c	  1905  ** Description : Local function to Disable Interrupts for Write operation     **
; ..\mcal_src\Uart.c	  1906  **                                                                            **
; ..\mcal_src\Uart.c	  1907  *******************************************************************************/
; ..\mcal_src\Uart.c	  1908  static void Uart_lClearWriteInterrupts(Ifx_ASCLIN* HwModulePtr,\ 
; Function Uart_lClearWriteInterrupts
.L119:
Uart_lClearWriteInterrupts:	.type	func

; ..\mcal_src\Uart.c	  1909                                                                uint8 ApiAccessId)
; ..\mcal_src\Uart.c	  1910  {
; ..\mcal_src\Uart.c	  1911    #if (UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  1912    #if (UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  1913    if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  1914    {
; ..\mcal_src\Uart.c	  1915      /*Clear Tx FIFO overflow and Level flags*/
; ..\mcal_src\Uart.c	  1916      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1917                     UART_FLAGSCLEAR_TFLC_CLEARMASK,(uint32)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  1918                                                      UART_FLAGSCLEAR_TFLC_BITPOS)
; ..\mcal_src\Uart.c	  1919      
; ..\mcal_src\Uart.c	  1920      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1921                   UART_FLAGSCLEAR_TFOC_CLEARMASK,((uint32)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  1922                                                     UART_FLAGSCLEAR_TFOC_BITPOS))
; ..\mcal_src\Uart.c	  1923    }
; ..\mcal_src\Uart.c	  1924    else
; ..\mcal_src\Uart.c	  1925    #endif /*(UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  1926    #if (UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  1927    if (ApiAccessId == UART_DEINIT_ACCESS)  
; ..\mcal_src\Uart.c	  1928    {  
; ..\mcal_src\Uart.c	  1929      /*Clear Tx FIFO overflow and Level flags*/
; ..\mcal_src\Uart.c	  1930      UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1931                     UART_FLAGSCLEAR_TFLC_CLEARMASK,(uint32)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  1932                                                     UART_FLAGSCLEAR_TFLC_BITPOS)
; ..\mcal_src\Uart.c	  1933    
; ..\mcal_src\Uart.c	  1934      UART_SFR_DEINIT_USER_MODE_MODIFY32(HwModulePtr->FLAGSCLEAR.U,\ 
; ..\mcal_src\Uart.c	  1935                   UART_FLAGSCLEAR_TFOC_CLEARMASK,((uint32)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  1936                                                    UART_FLAGSCLEAR_TFOC_BITPOS))
; ..\mcal_src\Uart.c	  1937    }
; ..\mcal_src\Uart.c	  1938    else
; ..\mcal_src\Uart.c	  1939    #endif /*(UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  1940    #endif /*(UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  1941    {
; ..\mcal_src\Uart.c	  1942      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a4]60
.L1022:

; ..\mcal_src\Uart.c	  1943            UART_FLAGSCLEAR_TFLC_CLEARMASK) | ((unsigned_int)UART_ENABLE_BIT <<\ 
	insert	d15,d15,#1,#31,#1
	st.w	[a4]60,d15
.L1023:

; ..\mcal_src\Uart.c	  1944                                                   UART_FLAGSCLEAR_TFLC_BITPOS));
; ..\mcal_src\Uart.c	  1945      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a4]60
.L1024:

; ..\mcal_src\Uart.c	  1946           UART_FLAGSCLEAR_TFOC_CLEARMASK) | ((unsigned_int)UART_ENABLE_BIT <<\ 
	insert	d15,d15,#1,#30,#1
	st.w	[a4]60,d15
.L1025:

; ..\mcal_src\Uart.c	  1947                                                    UART_FLAGSCLEAR_TFOC_BITPOS));
; ..\mcal_src\Uart.c	  1948    }
; ..\mcal_src\Uart.c	  1949    #if((UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_OFF) || \ 
; ..\mcal_src\Uart.c	  1950     ((UART_USER_MODE_RUNTIME_API_ENABLE == STD_OFF) && \ 
; ..\mcal_src\Uart.c	  1951     (UART_USER_MODE_DEINIT_API_ENABLE == STD_OFF)))
; ..\mcal_src\Uart.c	  1952     UNUSED_PARAMETER(ApiAccessId)
; ..\mcal_src\Uart.c	  1953     #endif
; ..\mcal_src\Uart.c	  1954  }
	ret
.L559:
	
__Uart_lClearWriteInterrupts_function_end:
	.size	Uart_lClearWriteInterrupts,__Uart_lClearWriteInterrupts_function_end-Uart_lClearWriteInterrupts
.L224:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_GetStatus')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Uart_GetStatus

; ..\mcal_src\Uart.c	  1955  
; ..\mcal_src\Uart.c	  1956   #if (UART_ABORT_READ_API == STD_ON)
; ..\mcal_src\Uart.c	  1957  /*******************************************************************************
; ..\mcal_src\Uart.c	  1958  ** Traceability : [cover parentID=DS_NAS_UART_PR1573,DS_NAS_UART_PR63_10]     **
; ..\mcal_src\Uart.c	  1959  ** Syntax : Uart_SizeType Uart_AbortRead( Uart_ChannelIdType Channel )        **
; ..\mcal_src\Uart.c	  1960  **              [/cover]                                                      **
; ..\mcal_src\Uart.c	  1961  ** Service ID:  0x04                                                          **
; ..\mcal_src\Uart.c	  1962  **                                                                            **
; ..\mcal_src\Uart.c	  1963  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  1964  **                                                                            **
; ..\mcal_src\Uart.c	  1965  ** Reentrancy:  Reentrant  (Not for the same channel)                         **
; ..\mcal_src\Uart.c	  1966  **                                                                            **
; ..\mcal_src\Uart.c	  1967  ** Parameters (in): Channel - Uart channel to be addressed                    **
; ..\mcal_src\Uart.c	  1968  **                                                                            **
; ..\mcal_src\Uart.c	  1969  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  1970  **                                                                            **
; ..\mcal_src\Uart.c	  1971  ** Return value  : UartRetSize - Number of bytes that have been successfully  **
; ..\mcal_src\Uart.c	  1972  **                               recieved and stored to the memory location   **
; ..\mcal_src\Uart.c	  1973  **                               before the read operation was aborted        **
; ..\mcal_src\Uart.c	  1974  ** Description : Api to Abort the read operation on the specified channel     **
; ..\mcal_src\Uart.c	  1975  **                                                                            **
; ..\mcal_src\Uart.c	  1976  *******************************************************************************/
; ..\mcal_src\Uart.c	  1977  Uart_SizeType Uart_AbortRead(Uart_ChannelIdType  Channel)
; ..\mcal_src\Uart.c	  1978  {
; ..\mcal_src\Uart.c	  1979    Ifx_ASCLIN* HwModulePtr;
; ..\mcal_src\Uart.c	  1980    const Uart_ChannelType *ChannelConfigPtr;
; ..\mcal_src\Uart.c	  1981    Uart_SizeType UartRetSize = UART_ZERO_U ;
; ..\mcal_src\Uart.c	  1982    uint8 HwUnit;
; ..\mcal_src\Uart.c	  1983  
; ..\mcal_src\Uart.c	  1984    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  1985    Uart_ReturnType Retvalue = UART_OK;
; ..\mcal_src\Uart.c	  1986  
; ..\mcal_src\Uart.c	  1987    if (Uart_InitStatus == UART_UNINITIALISED)
; ..\mcal_src\Uart.c	  1988    {
; ..\mcal_src\Uart.c	  1989      /* If uart is not uninitialised Report to  DET */
; ..\mcal_src\Uart.c	  1990      Det_ReportError(
; ..\mcal_src\Uart.c	  1991                      UART_MODULE_ID,
; ..\mcal_src\Uart.c	  1992                      UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  1993                      UART_SID_ABORT_READ,
; ..\mcal_src\Uart.c	  1994                      UART_E_UNINIT
; ..\mcal_src\Uart.c	  1995                     );
; ..\mcal_src\Uart.c	  1996      Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  1997      UartRetSize = UART_ZERO_U ;
; ..\mcal_src\Uart.c	  1998    }
; ..\mcal_src\Uart.c	  1999    else
; ..\mcal_src\Uart.c	  2000    {
; ..\mcal_src\Uart.c	  2001      /* If the channelId is Invalid Report to DET */
; ..\mcal_src\Uart.c	  2002      Retvalue = Uart_lChannelCheck(Channel,UART_SID_ABORT_READ);
; ..\mcal_src\Uart.c	  2003      UartRetSize = UART_ZERO_U ;
; ..\mcal_src\Uart.c	  2004    }
; ..\mcal_src\Uart.c	  2005    #endif
; ..\mcal_src\Uart.c	  2006  
; ..\mcal_src\Uart.c	  2007    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  2008    if (Retvalue == UART_OK)
; ..\mcal_src\Uart.c	  2009    #else
; ..\mcal_src\Uart.c	  2010     /* Release the lock so that New Uart Read Operation can be started*/
; ..\mcal_src\Uart.c	  2011    Mcal_UnlockResource(&Uart_ChLock[UART_RX_LOCK_IDX][Channel]);
; ..\mcal_src\Uart.c	  2012    #endif
; ..\mcal_src\Uart.c	  2013    {
; ..\mcal_src\Uart.c	  2014      HwUnit = Uart_ChannelInfo[Channel].Uart_AssignedHW;
; ..\mcal_src\Uart.c	  2015    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2016     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	  2017      HwModulePtr= &(UART_HW_MODULE[HwUnit]);
; ..\mcal_src\Uart.c	  2018  
; ..\mcal_src\Uart.c	  2019      /*Disable RxFIFO and flush It*/
; ..\mcal_src\Uart.c	  2020      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  2021                               UART_RXFIFOCON_ENI_CLEARMASK, UART_DISABLE_BIT)
; ..\mcal_src\Uart.c	  2022      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->RXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  2023                            UART_RXFIFOCON_FLUSH_CLEARMASK, UART_ENABLE_BIT)
; ..\mcal_src\Uart.c	  2024  
; ..\mcal_src\Uart.c	  2025      /*Clear Rx overflow and level Interrupts*/
; ..\mcal_src\Uart.c	  2026      Uart_lClearReadInterrupts(HwModulePtr, UART_RUNTIME_ACCESS);
; ..\mcal_src\Uart.c	  2027  
; ..\mcal_src\Uart.c	  2028      /*Disable Rx and Err Interrupts*/
; ..\mcal_src\Uart.c	  2029      Uart_lHwDisableAscLinRxIntr(HwUnit, UART_RUNTIME_ACCESS);
; ..\mcal_src\Uart.c	  2030      Uart_lHwDisableAscLinErrIntr(HwUnit, UART_RUNTIME_ACCESS);
; ..\mcal_src\Uart.c	  2031  
; ..\mcal_src\Uart.c	  2032      /*Return Total Data Rxd before Read operation was Aborted */
; ..\mcal_src\Uart.c	  2033      UartRetSize = Uart_ChannelInfo[Channel].Uart_TotalDataRxd;
; ..\mcal_src\Uart.c	  2034  
; ..\mcal_src\Uart.c	  2035     /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2036       ChannelConfigPtr to access the Configuration of Channel passed*/
; ..\mcal_src\Uart.c	  2037      ChannelConfigPtr = &(Uart_kConfigPtr->ChannelConfigPtr[Channel]);
; ..\mcal_src\Uart.c	  2038     /*Reinitialise the HW Unit*/
; ..\mcal_src\Uart.c	  2039      Uart_lHwInitRuntime(HwUnit, ChannelConfigPtr);
; ..\mcal_src\Uart.c	  2040  
; ..\mcal_src\Uart.c	  2041      /*Reset Uart_ChannelInfo*/
; ..\mcal_src\Uart.c	  2042      Uart_ChannelInfo[Channel].Uart_RxState = UART_INITIALISED;
; ..\mcal_src\Uart.c	  2043      Uart_ChannelInfo[Channel].Uart_RxBuffPtr = NULL_PTR;
; ..\mcal_src\Uart.c	  2044  
; ..\mcal_src\Uart.c	  2045      /*Reset Data Rxd Counter*/
; ..\mcal_src\Uart.c	  2046      Uart_ChannelInfo[Channel].Uart_TotalDataRxd = UART_ZERO_U;
; ..\mcal_src\Uart.c	  2047  
; ..\mcal_src\Uart.c	  2048      if(ChannelConfigPtr->UartNotif.UartAbortReceiveNotifPtr != NULL_PTR)
; ..\mcal_src\Uart.c	  2049      {
; ..\mcal_src\Uart.c	  2050        /*Call Abort Receive Notification Function*/
; ..\mcal_src\Uart.c	  2051        ChannelConfigPtr->UartNotif.UartAbortReceiveNotifPtr(UART_NO_ERR);
; ..\mcal_src\Uart.c	  2052      }
; ..\mcal_src\Uart.c	  2053    }
; ..\mcal_src\Uart.c	  2054    return UartRetSize ;
; ..\mcal_src\Uart.c	  2055  }
; ..\mcal_src\Uart.c	  2056  #endif
; ..\mcal_src\Uart.c	  2057  
; ..\mcal_src\Uart.c	  2058  #if (UART_ABORT_WRITE_API == STD_ON)
; ..\mcal_src\Uart.c	  2059  /*******************************************************************************
; ..\mcal_src\Uart.c	  2060  ** Traceability : [cover parentID=DS_NAS_UART_PR1574,DS_NAS_UART_PR63_11]     **
; ..\mcal_src\Uart.c	  2061  ** Syntax : Uart_SizeType Uart_AbortWrite( Uart_ChannelIdType Channel )       **
; ..\mcal_src\Uart.c	  2062  **            [/cover]                                                        **
; ..\mcal_src\Uart.c	  2063  ** Service ID:  0x05                                                          **
; ..\mcal_src\Uart.c	  2064  **                                                                            **
; ..\mcal_src\Uart.c	  2065  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  2066  **                                                                            **
; ..\mcal_src\Uart.c	  2067  ** Reentrancy:  Reentrant  (Not for the same channel)                         **
; ..\mcal_src\Uart.c	  2068  **                                                                            **
; ..\mcal_src\Uart.c	  2069  ** Parameters (in): Channel - Uart channel to be addressed                    **
; ..\mcal_src\Uart.c	  2070  **                                                                            **
; ..\mcal_src\Uart.c	  2071  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  2072  **                                                                            **
; ..\mcal_src\Uart.c	  2073  ** Return value  : UartRetSize - Number of bytes that have been successfully  **
; ..\mcal_src\Uart.c	  2074  **                       transmitted before the write operation was aborted   **
; ..\mcal_src\Uart.c	  2075  **                                                                            **
; ..\mcal_src\Uart.c	  2076  ** Description : Api to Abort Transmission of data on the specified channel   **
; ..\mcal_src\Uart.c	  2077  **                                                                            **
; ..\mcal_src\Uart.c	  2078  *******************************************************************************/
; ..\mcal_src\Uart.c	  2079  Uart_SizeType Uart_AbortWrite(Uart_ChannelIdType Channel)
; ..\mcal_src\Uart.c	  2080  {
; ..\mcal_src\Uart.c	  2081    Ifx_ASCLIN* HwModulePtr;
; ..\mcal_src\Uart.c	  2082    const Uart_ChannelType *ChannelConfigPtr;
; ..\mcal_src\Uart.c	  2083    Uart_SizeType UartRetSize = UART_ZERO_U;
; ..\mcal_src\Uart.c	  2084    uint16 tempsum;
; ..\mcal_src\Uart.c	  2085    uint8 HwUnit;
; ..\mcal_src\Uart.c	  2086  
; ..\mcal_src\Uart.c	  2087     #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  2088     Uart_ReturnType Retvalue;
; ..\mcal_src\Uart.c	  2089     #endif
; ..\mcal_src\Uart.c	  2090  
; ..\mcal_src\Uart.c	  2091    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  2092    if (Uart_InitStatus == UART_UNINITIALISED)
; ..\mcal_src\Uart.c	  2093    {
; ..\mcal_src\Uart.c	  2094      /* Report to  DET */
; ..\mcal_src\Uart.c	  2095      Det_ReportError(
; ..\mcal_src\Uart.c	  2096                      UART_MODULE_ID,
; ..\mcal_src\Uart.c	  2097                      UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  2098                      UART_SID_ABORT_WRITE,
; ..\mcal_src\Uart.c	  2099                      UART_E_UNINIT
; ..\mcal_src\Uart.c	  2100                     );
; ..\mcal_src\Uart.c	  2101      Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  2102      UartRetSize = UART_ZERO_U;
; ..\mcal_src\Uart.c	  2103    }
; ..\mcal_src\Uart.c	  2104    else
; ..\mcal_src\Uart.c	  2105    {
; ..\mcal_src\Uart.c	  2106      /* If the channelId is Invalid Report to DET */
; ..\mcal_src\Uart.c	  2107      Retvalue = Uart_lChannelCheck(Channel,UART_SID_ABORT_WRITE);
; ..\mcal_src\Uart.c	  2108      UartRetSize = UART_ZERO_U;
; ..\mcal_src\Uart.c	  2109    }
; ..\mcal_src\Uart.c	  2110  
; ..\mcal_src\Uart.c	  2111    if (Retvalue == UART_OK)
; ..\mcal_src\Uart.c	  2112    #else
; ..\mcal_src\Uart.c	  2113     /* Release the lock so that New Uart Write Operation can be started*/
; ..\mcal_src\Uart.c	  2114    Mcal_UnlockResource(&Uart_ChLock[UART_TX_LOCK_IDX][Channel]);
; ..\mcal_src\Uart.c	  2115    #endif
; ..\mcal_src\Uart.c	  2116    {
; ..\mcal_src\Uart.c	  2117  
; ..\mcal_src\Uart.c	  2118     /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2119       ChannelConfigPtr to access the Configuration of Channel passed*/
; ..\mcal_src\Uart.c	  2120      ChannelConfigPtr = &(Uart_kConfigPtr->ChannelConfigPtr[Channel]);
; ..\mcal_src\Uart.c	  2121      HwUnit = Uart_ChannelInfo[Channel].Uart_AssignedHW;
; ..\mcal_src\Uart.c	  2122    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2123     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	  2124      HwModulePtr= &(UART_HW_MODULE[HwUnit]);
; ..\mcal_src\Uart.c	  2125  
; ..\mcal_src\Uart.c	  2126      /*Disable TxFIFO*/
; ..\mcal_src\Uart.c	  2127      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  2128                                UART_TXFIFOCON_ENO_CLEARMASK, UART_DISABLE_BIT)
; ..\mcal_src\Uart.c	  2129  
; ..\mcal_src\Uart.c	  2130     if((Uart_ChannelInfo[Channel].Uart_TxState == UART_INITIALISED) &&\ 
; ..\mcal_src\Uart.c	  2131           (Uart_ChannelInfo[Channel].Uart_TxDataLeft == UART_ZERO_U))
; ..\mcal_src\Uart.c	  2132      {
; ..\mcal_src\Uart.c	  2133         /*Reset Txd Data Fill to zero*/
; ..\mcal_src\Uart.c	  2134         Uart_ChannelInfo[Channel].Uart_TxDataCopyCntr = UART_ZERO_U;
; ..\mcal_src\Uart.c	  2135      }
; ..\mcal_src\Uart.c	  2136  
; ..\mcal_src\Uart.c	  2137      /*Return the Count of Data Txd before the write operation was aborted*/
; ..\mcal_src\Uart.c	  2138      tempsum =(uint16)Uart_ChannelInfo[Channel].Uart_TxDataCopyCntr -\ 
; ..\mcal_src\Uart.c	  2139          ((uint16)((UART_SFR_RUNTIME_USER_MODE_READ32(HwModulePtr->TXFIFOCON.U)\ 
; ..\mcal_src\Uart.c	  2140                     & UART_TXFIFOCON_FILL_MASK) >> UART_TXFIFOCON_FILL_BITPOS));
; ..\mcal_src\Uart.c	  2141      Uart_ChannelInfo[Channel].Uart_TotalDataTxd += tempsum;
; ..\mcal_src\Uart.c	  2142  
; ..\mcal_src\Uart.c	  2143      UartRetSize = Uart_ChannelInfo[Channel].Uart_TotalDataTxd;
; ..\mcal_src\Uart.c	  2144  
; ..\mcal_src\Uart.c	  2145     /*Flush TxFIFO*/
; ..\mcal_src\Uart.c	  2146      UART_SFR_RUNTIME_USER_MODE_MODIFY32(HwModulePtr->TXFIFOCON.U,\ 
; ..\mcal_src\Uart.c	  2147                               UART_TXFIFOCON_FLUSH_CLEARMASK, UART_ENABLE_BIT )
; ..\mcal_src\Uart.c	  2148  
; ..\mcal_src\Uart.c	  2149     /*Clear Tx overflow and level Interrupts*/
; ..\mcal_src\Uart.c	  2150      Uart_lClearWriteInterrupts(HwModulePtr, UART_RUNTIME_ACCESS);
; ..\mcal_src\Uart.c	  2151  
; ..\mcal_src\Uart.c	  2152     /*Disable Tx and Err Interrupts*/
; ..\mcal_src\Uart.c	  2153      Uart_lHwDisableAscLinTxIntr(HwUnit, UART_RUNTIME_ACCESS);
; ..\mcal_src\Uart.c	  2154      Uart_lHwDisableAscLinErrIntr(HwUnit, UART_RUNTIME_ACCESS);
; ..\mcal_src\Uart.c	  2155  
; ..\mcal_src\Uart.c	  2156     /*Reinitialise the HW Unit*/
; ..\mcal_src\Uart.c	  2157      Uart_lHwInitRuntime(HwUnit, ChannelConfigPtr);
; ..\mcal_src\Uart.c	  2158  
; ..\mcal_src\Uart.c	  2159     /*Reset Uart_ChannelInfo*/
; ..\mcal_src\Uart.c	  2160      Uart_ChannelInfo[Channel].Uart_TxBuffPtr = NULL_PTR;
; ..\mcal_src\Uart.c	  2161      Uart_ChannelInfo[Channel].Uart_TxState = UART_INITIALISED;
; ..\mcal_src\Uart.c	  2162      Uart_ChannelInfo[Channel].Uart_TotalDataTxd = UART_ZERO_U;
; ..\mcal_src\Uart.c	  2163      Uart_ChannelInfo[Channel].Uart_TxDataCopyCntr = UART_ZERO_U;
; ..\mcal_src\Uart.c	  2164  
; ..\mcal_src\Uart.c	  2165      if(ChannelConfigPtr->UartNotif.UartAbortTransmitNotifPtr != NULL_PTR)
; ..\mcal_src\Uart.c	  2166      {
; ..\mcal_src\Uart.c	  2167        /*Call Abort Transmit Notification Function*/
; ..\mcal_src\Uart.c	  2168        ChannelConfigPtr->UartNotif.UartAbortTransmitNotifPtr(UART_NO_ERR);
; ..\mcal_src\Uart.c	  2169      }
; ..\mcal_src\Uart.c	  2170     }
; ..\mcal_src\Uart.c	  2171    return UartRetSize;
; ..\mcal_src\Uart.c	  2172  }
; ..\mcal_src\Uart.c	  2173  #endif
; ..\mcal_src\Uart.c	  2174  
; ..\mcal_src\Uart.c	  2175  /*******************************************************************************
; ..\mcal_src\Uart.c	  2176  ** Traceability : [cover parentID=DS_NAS_UART_PR1575,DS_NAS_UART_PR63_12]     **
; ..\mcal_src\Uart.c	  2177  ** Syntax : Uart_StatusType Uart_GetStatus(Uart_ChannelIdType Channel)        **
; ..\mcal_src\Uart.c	  2178  **            [/cover]                                                        **
; ..\mcal_src\Uart.c	  2179  ** Service ID:      0x06                                                      **
; ..\mcal_src\Uart.c	  2180  **                                                                            **
; ..\mcal_src\Uart.c	  2181  ** Sync/Async:      Synchronous                                               **
; ..\mcal_src\Uart.c	  2182  **                                                                            **
; ..\mcal_src\Uart.c	  2183  ** Reentrancy:      Re-entrant                                                **
; ..\mcal_src\Uart.c	  2184  **                                                                            **
; ..\mcal_src\Uart.c	  2185  ** Parameters (in): Channel    : Uart channel to be addressed                 **
; ..\mcal_src\Uart.c	  2186  **                                                                            **
; ..\mcal_src\Uart.c	  2187  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  2188  **                                                                            **
; ..\mcal_src\Uart.c	  2189  ** Return value:                                                              **
; ..\mcal_src\Uart.c	  2190  **                  UART_BUSY : Uart channel is busy with transmission        **
; ..\mcal_src\Uart.c	  2191  **                                    or Reception operation                  **
; ..\mcal_src\Uart.c	  2192  **                  UART_IDLE : Uart channel is currently free to use         **
; ..\mcal_src\Uart.c	  2193  **                                                                            **
; ..\mcal_src\Uart.c	  2194  **                  UART_UNINIT: Uart channel is not uninitialised            **
; ..\mcal_src\Uart.c	  2195  **                                                                            **
; ..\mcal_src\Uart.c	  2196  ** Description :    Indicates the current driver status for the UART channel  **
; ..\mcal_src\Uart.c	  2197  **                                                                            **
; ..\mcal_src\Uart.c	  2198  *******************************************************************************/
; ..\mcal_src\Uart.c	  2199  Uart_StatusType Uart_GetStatus(Uart_ChannelIdType Channel)
; Function Uart_GetStatus
.L121:
Uart_GetStatus:	.type	func

; ..\mcal_src\Uart.c	  2200  {
; ..\mcal_src\Uart.c	  2201    Uart_StatusType UartDriverState = UART_UNINIT;
; ..\mcal_src\Uart.c	  2202  
; ..\mcal_src\Uart.c	  2203    #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  2204    Uart_ReturnType Retvalue = UART_OK;
; ..\mcal_src\Uart.c	  2205  
; ..\mcal_src\Uart.c	  2206    /*Check for driver initialisation */
; ..\mcal_src\Uart.c	  2207    if (Uart_InitStatus == UART_UNINITIALISED)
; ..\mcal_src\Uart.c	  2208    {
; ..\mcal_src\Uart.c	  2209      Det_ReportError(
; ..\mcal_src\Uart.c	  2210                      UART_MODULE_ID,
; ..\mcal_src\Uart.c	  2211                      UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  2212                      UART_SID_GETSTATUS,
; ..\mcal_src\Uart.c	  2213                      UART_E_UNINIT
; ..\mcal_src\Uart.c	  2214                     );
; ..\mcal_src\Uart.c	  2215  
; ..\mcal_src\Uart.c	  2216      Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  2217  
; ..\mcal_src\Uart.c	  2218      UartDriverState = UART_UNINIT;
; ..\mcal_src\Uart.c	  2219    }
; ..\mcal_src\Uart.c	  2220    else
; ..\mcal_src\Uart.c	  2221    {
; ..\mcal_src\Uart.c	  2222       /* If the channelId is Invalid Report to DET */
; ..\mcal_src\Uart.c	  2223      Retvalue = Uart_lChannelCheck(Channel,UART_SID_GETSTATUS);
; ..\mcal_src\Uart.c	  2224  
; ..\mcal_src\Uart.c	  2225      UartDriverState = UART_BUSY;
; ..\mcal_src\Uart.c	  2226    }
; ..\mcal_src\Uart.c	  2227  
; ..\mcal_src\Uart.c	  2228    if (Retvalue == UART_OK)
; ..\mcal_src\Uart.c	  2229    #endif
; ..\mcal_src\Uart.c	  2230    {
; ..\mcal_src\Uart.c	  2231     /*Return state as BUSY if channel is busy with Read or Write Operation*/
; ..\mcal_src\Uart.c	  2232  
; ..\mcal_src\Uart.c	  2233      if((Uart_ChannelInfo[Channel].Uart_TxState == UART_OPERATION_IN_PROGRESS) \ 
	fcall	.cocofun_4
.L715:
	ld.bu	d15,[a15]8
.L830:
	jeq	d15,#2,.L46
.L831:

; ..\mcal_src\Uart.c	  2234       ||( Uart_ChannelInfo[Channel].Uart_RxState == UART_OPERATION_IN_PROGRESS))
	ld.bu	d0,[a15]9
.L832:
	jne	d0,#2,.L47
.L46:

; ..\mcal_src\Uart.c	  2235      {
; ..\mcal_src\Uart.c	  2236        UartDriverState = UART_BUSY;
; ..\mcal_src\Uart.c	  2237      }
; ..\mcal_src\Uart.c	  2238      else if (( Uart_ChannelInfo[Channel].Uart_TxState == UART_INITIALISED)\ 
; ..\mcal_src\Uart.c	  2239       &&( Uart_ChannelInfo[Channel].Uart_RxState == UART_INITIALISED))
; ..\mcal_src\Uart.c	  2240      {
; ..\mcal_src\Uart.c	  2241        UartDriverState = UART_IDLE;
; ..\mcal_src\Uart.c	  2242      }
; ..\mcal_src\Uart.c	  2243      else
; ..\mcal_src\Uart.c	  2244      {
; ..\mcal_src\Uart.c	  2245        UartDriverState = UART_UNINIT;
; ..\mcal_src\Uart.c	  2246      }
; ..\mcal_src\Uart.c	  2247    }
; ..\mcal_src\Uart.c	  2248  
; ..\mcal_src\Uart.c	  2249    return UartDriverState;
; ..\mcal_src\Uart.c	  2250  }
	mov	d2,#2
	ret
.L47:
	jne	d15,#1,.L49
.L833:
	jne	d0,#1,.L50
.L834:
	mov	d2,#0
	ret
.L50:
.L49:
	mov	d2,#1
	ret
.L435:
	
__Uart_GetStatus_function_end:
	.size	Uart_GetStatus,__Uart_GetStatus_function_end-Uart_GetStatus
.L179:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_IsrReceive')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Uart_IsrReceive

; ..\mcal_src\Uart.c	  2251  
; ..\mcal_src\Uart.c	  2252  
; ..\mcal_src\Uart.c	  2253  /*******************************************************************************
; ..\mcal_src\Uart.c	  2254  ** Syntax :          void Uart_IsrReceive( uint8  HwUnit)                     **
; ..\mcal_src\Uart.c	  2255  **                                                                            **
; ..\mcal_src\Uart.c	  2256  ** Service ID:       NA                                                       **
; ..\mcal_src\Uart.c	  2257  **                                                                            **
; ..\mcal_src\Uart.c	  2258  ** Sync/Async:       Asynchronous                                             **
; ..\mcal_src\Uart.c	  2259  **                                                                            **
; ..\mcal_src\Uart.c	  2260  ** Reentrancy:  Reentrant  (Not for the same HW Unit)                         **
; ..\mcal_src\Uart.c	  2261  **                                                                            **
; ..\mcal_src\Uart.c	  2262  ** Parameters (in):  HwUnit : Represents ASCLIN hw module number              **
; ..\mcal_src\Uart.c	  2263  **                                                                            **
; ..\mcal_src\Uart.c	  2264  ** Parameters (out): none                                                     **
; ..\mcal_src\Uart.c	  2265  **                                                                            **
; ..\mcal_src\Uart.c	  2266  ** Return value:     none                                                     **
; ..\mcal_src\Uart.c	  2267  **                                                                            **
; ..\mcal_src\Uart.c	  2268  ** Description :     This ISR will be called whenever  data                   **
; ..\mcal_src\Uart.c	  2269  **                   is completely received by the ASCLIN w/o any errors      **
; ..\mcal_src\Uart.c	  2270  *******************************************************************************/
; ..\mcal_src\Uart.c	  2271  void Uart_IsrReceive(uint8  HwUnit)
; Function Uart_IsrReceive
.L123:
Uart_IsrReceive:	.type	func
	fcall	.cocofun_3
.L839:

; ..\mcal_src\Uart.c	  2272  {
; ..\mcal_src\Uart.c	  2273    const Uart_ChannelType *ChannelConfigPtr;
; ..\mcal_src\Uart.c	  2274    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2275     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	  2276    Ifx_ASCLIN* HwModulePtr= &(UART_HW_MODULE[HwUnit]);
; ..\mcal_src\Uart.c	  2277    Uart_ChannelIdType Channel;
; ..\mcal_src\Uart.c	  2278  
; ..\mcal_src\Uart.c	  2279    /* Extract the Channel */
; ..\mcal_src\Uart.c	  2280    Channel = Uart_BusChannelMap[HwUnit];
; ..\mcal_src\Uart.c	  2281    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2282      ChannelConfigPtr to access the Configuration of Channel passed*/
; ..\mcal_src\Uart.c	  2283    ChannelConfigPtr = &(Uart_kConfigPtr->ChannelConfigPtr[Channel]);
	fcall	.cocofun_9
.L719:
	ld.a	a2,[a2]
.L840:

; ..\mcal_src\Uart.c	  2284  
; ..\mcal_src\Uart.c	  2285    /* Local fnt to read the Data from Rx FIFO */
; ..\mcal_src\Uart.c	  2286    Uart_lRead(HwModulePtr,Channel);
	sha	d15,d9,#5
	mov.aa	a4,a15
.L721:
	mov	d4,d9
.L720:
	addsc.a	a12,a2,d15,#0
.L727:
	call	Uart_lRead
.L841:

; ..\mcal_src\Uart.c	  2287  
; ..\mcal_src\Uart.c	  2288    if (Uart_ChannelInfo[Channel].Uart_RxDataLeft == UART_ZERO_U)
	fcall	.cocofun_12
.L722:
	addsc.a	a13,a2,d15,#0
.L842:
	ld.hu	d15,[a13]12
.L843:
	jne	d15,#0,.L53
.L844:

; ..\mcal_src\Uart.c	  2289    {
; ..\mcal_src\Uart.c	  2290      /*Flush RxFIFO*/
; ..\mcal_src\Uart.c	  2291      HwModulePtr->RXFIFOCON.U = ((HwModulePtr->RXFIFOCON.U &\ 
	ld.w	d15,[a15]16
.L845:

; ..\mcal_src\Uart.c	  2292                              UART_RXFIFOCON_FLUSH_CLEARMASK) | UART_ENABLE_BIT);
; ..\mcal_src\Uart.c	  2293  
; ..\mcal_src\Uart.c	  2294      /*Clear Rx Overflow and Level Interrupts*/
; ..\mcal_src\Uart.c	  2295      Uart_lClearReadInterrupts(HwModulePtr, UART_ISR_ACCESS);
	mov	d4,#0
	mov.aa	a4,a15
.L723:
	or	d15,#1
	st.w	[a15]16,d15
.L846:
	call	Uart_lClearReadInterrupts
.L724:

; ..\mcal_src\Uart.c	  2296  
; ..\mcal_src\Uart.c	  2297      /*Disable Rx FIFO*/
; ..\mcal_src\Uart.c	  2298      HwModulePtr->RXFIFOCON.U = ((HwModulePtr->RXFIFOCON.U &\ 
; ..\mcal_src\Uart.c	  2299                              UART_RXFIFOCON_ENI_CLEARMASK) | UART_DISABLE_BIT);
; ..\mcal_src\Uart.c	  2300  
; ..\mcal_src\Uart.c	  2301      /*Disable Rx Interrupt*/
; ..\mcal_src\Uart.c	  2302      Uart_lHwDisableAscLinRxIntr(HwUnit, UART_ISR_ACCESS);
	mov	d4,d8
	ld.w	d15,[a15]16
.L725:
	mov	d5,#0
.L847:
	insert	d15,d15,#0,#1,#1
	st.w	[a15]16,d15
.L848:
	call	Uart_lHwDisableAscLinRxIntr
.L849:

; ..\mcal_src\Uart.c	  2303  
; ..\mcal_src\Uart.c	  2304      /* Release the lock so that other Uart Read Operation can be started*/
; ..\mcal_src\Uart.c	  2305      Mcal_UnlockResource(&Uart_ChLock[UART_RX_LOCK_IDX][Channel]);
	fcall	.cocofun_8
.L726:
	add.a	a2,#4
.L850:
	addsc.a	a4,a2,d9,#2
	call	Mcal_UnlockResource
.L851:

; ..\mcal_src\Uart.c	  2306  
; ..\mcal_src\Uart.c	  2307      Uart_ChannelInfo[Channel].Uart_RxBuffPtr = NULL_PTR;
	mov.a	a15,#0
.L728:
	st.a	[a13],a15
.L852:

; ..\mcal_src\Uart.c	  2308      Uart_ChannelInfo[Channel].Uart_RxState = UART_INITIALISED;
	mov	d15,#1
	st.b	[a13]9,d15
.L853:

; ..\mcal_src\Uart.c	  2309  
; ..\mcal_src\Uart.c	  2310      if(ChannelConfigPtr->UartNotif.UartReceiveNotifPtr != NULL_PTR)
	ld.a	a15,[a12]4
.L854:
	jz.a	a15,.L54
.L855:

; ..\mcal_src\Uart.c	  2311      {
; ..\mcal_src\Uart.c	  2312        /*Call the Receive Notification Function*/
; ..\mcal_src\Uart.c	  2313        ChannelConfigPtr->UartNotif.UartReceiveNotifPtr(UART_NO_ERR);
	mov	d4,#0
	ji	a15
.L54:
.L53:

; ..\mcal_src\Uart.c	  2314      }
; ..\mcal_src\Uart.c	  2315      else
; ..\mcal_src\Uart.c	  2316      {
; ..\mcal_src\Uart.c	  2317       /* Do Nothing */
; ..\mcal_src\Uart.c	  2318      }
; ..\mcal_src\Uart.c	  2319    }
; ..\mcal_src\Uart.c	  2320    return ;
; ..\mcal_src\Uart.c	  2321  }
	ret
.L438:
	
__Uart_IsrReceive_function_end:
	.size	Uart_IsrReceive,__Uart_IsrReceive_function_end-Uart_IsrReceive
.L184:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_12')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_12
.L125:
.cocofun_12:	.type	func
; Function body .cocofun_12, coco_iter:0
	mul	d15,d9,#20
	movh.a	a2,#@his(Uart_ChannelInfo)
	lea	a2,[a2]@los(Uart_ChannelInfo)
.L1126:
	fret
.L319:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_3')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_3
.L127:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:0
	mov	d8,d4
	lea	a15,0xf0000600
.L717:
	sha	d15,d8,#8
.L1082:
	addsc.a	a15,a15,d15,#0
.L716:
	fcall	.cocofun_14
.L1083:
	addsc.a	a2,a2,d8,#0
	ld.bu	d9,[a2]
.L718:
	fret
.L274:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_IsrTransmit')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Uart_IsrTransmit

; ..\mcal_src\Uart.c	  2322  
; ..\mcal_src\Uart.c	  2323  /*******************************************************************************
; ..\mcal_src\Uart.c	  2324  ** Syntax :          void Uart_IsrTransmit( uint8  HwUnit)                    **
; ..\mcal_src\Uart.c	  2325  **                                                                            **
; ..\mcal_src\Uart.c	  2326  ** Service ID:       NA                                                       **
; ..\mcal_src\Uart.c	  2327  **                                                                            **
; ..\mcal_src\Uart.c	  2328  ** Sync/Async:       Asynchronous                                             **
; ..\mcal_src\Uart.c	  2329  **                                                                            **
; ..\mcal_src\Uart.c	  2330  ** Reentrancy:  Reentrant  (Not for the same HW Unit)                         **
; ..\mcal_src\Uart.c	  2331  **                                                                            **
; ..\mcal_src\Uart.c	  2332  ** Parameters (in):  HwUnit : Represents Hw Module number                     **
; ..\mcal_src\Uart.c	  2333  **                                                                            **
; ..\mcal_src\Uart.c	  2334  ** Parameters (out): none                                                     **
; ..\mcal_src\Uart.c	  2335  **                                                                            **
; ..\mcal_src\Uart.c	  2336  ** Return value:     none                                                     **
; ..\mcal_src\Uart.c	  2337  **                                                                            **
; ..\mcal_src\Uart.c	  2338  ** Description :     This ISR will be called whenever the data                **
; ..\mcal_src\Uart.c	  2339  **                   is successfully transmitted by the ASCLIN w/o any errors **
; ..\mcal_src\Uart.c	  2340  **                                                                            **
; ..\mcal_src\Uart.c	  2341  *******************************************************************************/
; ..\mcal_src\Uart.c	  2342  void Uart_IsrTransmit(uint8  HwUnit)
; Function Uart_IsrTransmit
.L129:
Uart_IsrTransmit:	.type	func
	fcall	.cocofun_3
.L860:

; ..\mcal_src\Uart.c	  2343  {
; ..\mcal_src\Uart.c	  2344    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2345     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	  2346    Ifx_ASCLIN* HwModulePtr = &(UART_HW_MODULE[HwUnit]);
; ..\mcal_src\Uart.c	  2347    uint16 Tempsum;
; ..\mcal_src\Uart.c	  2348    Uart_ChannelIdType Channel;
; ..\mcal_src\Uart.c	  2349  
; ..\mcal_src\Uart.c	  2350    /* Extract the Channel */
; ..\mcal_src\Uart.c	  2351    Channel = Uart_BusChannelMap[HwUnit];
; ..\mcal_src\Uart.c	  2352    Tempsum = (uint16)(Uart_ChannelInfo[Channel].Uart_TxDataCopyCntr -\ 
	fcall	.cocofun_12
.L731:
	addsc.a	a12,a2,d15,#0
.L861:

; ..\mcal_src\Uart.c	  2353       ((uint16) ((HwModulePtr->TXFIFOCON.U & UART_TXFIFOCON_FILL_MASK) >>\ 
	ld.w	d15,[a15]12
.L862:
	lea	a13,[a12]19
.L863:
	extr.u	d1,d15,#16,#5
	ld.bu	d0,[a13]
.L864:

; ..\mcal_src\Uart.c	  2354                                                    UART_TXFIFOCON_FILL_BITPOS)));
; ..\mcal_src\Uart.c	  2355  
; ..\mcal_src\Uart.c	  2356    Uart_ChannelInfo[Channel].Uart_TotalDataTxd += Tempsum;
	ld.hu	d15,[a12]14
.L865:
	sub	d0,d1
.L866:
	extr.u	d0,d0,#0,#16
.L729:
	add	d15,d0
	st.h	[a12]14,d15
.L867:

; ..\mcal_src\Uart.c	  2357  
; ..\mcal_src\Uart.c	  2358    if ( Uart_ChannelInfo[Channel].Uart_TxDataLeft == UART_ZERO_U)
	ld.hu	d0,[a12]10
.L730:
	jne	d0,#0,.L55
.L868:

; ..\mcal_src\Uart.c	  2359     {
; ..\mcal_src\Uart.c	  2360       /*Clear the transmission complete flag*/
; ..\mcal_src\Uart.c	  2361       HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &
	fcall	.cocofun_6
.L869:

; ..\mcal_src\Uart.c	  2362              UART_FLAGSCLEAR_TCC_MASK) | ((unsigned_int)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  2363                                                     UART_FLAGSCLEAR_TCC_BITPOS));
; ..\mcal_src\Uart.c	  2364  
; ..\mcal_src\Uart.c	  2365       /*Clear all write interrupts */
; ..\mcal_src\Uart.c	  2366       Uart_lClearWriteInterrupts(HwModulePtr, UART_ISR_ACCESS);
	mov	d4,#0
	mov.aa	a4,a15
.L733:
	call	Uart_lClearWriteInterrupts
.L734:

; ..\mcal_src\Uart.c	  2367  
; ..\mcal_src\Uart.c	  2368       /*Disable the Tx Overflow and Level Interrupt*/
; ..\mcal_src\Uart.c	  2369       HwModulePtr->FLAGSENABLE.U = ((HwModulePtr->FLAGSENABLE.U &\ 
	ld.w	d15,[a15]64
.L870:

; ..\mcal_src\Uart.c	  2370                              UART_FLAGSENABLE_TFLE_MASK) | UART_DISABLE_BIT);
; ..\mcal_src\Uart.c	  2371  
; ..\mcal_src\Uart.c	  2372       HwModulePtr->FLAGSENABLE.U = ((HwModulePtr->FLAGSENABLE.U\ 
; ..\mcal_src\Uart.c	  2373                              & UART_FLAGSENABLE_TFOE_MASK) | UART_DISABLE_BIT);
; ..\mcal_src\Uart.c	  2374  
; ..\mcal_src\Uart.c	  2375       /*Disable Tx Interrupts*/
; ..\mcal_src\Uart.c	  2376       Uart_lHwDisableAscLinTxIntr(HwUnit, UART_ISR_ACCESS);
	mov	d4,d8
.L735:
	insert	d15,d15,#0,#31,#1
	st.w	[a15]64,d15
.L871:
	mov	d5,#0
.L872:
	ld.w	d15,[a15]64
.L873:
	insert	d15,d15,#0,#30,#1
	st.w	[a15]64,d15
.L874:
	call	Uart_lHwDisableAscLinTxIntr
.L736:

; ..\mcal_src\Uart.c	  2377  
; ..\mcal_src\Uart.c	  2378       Uart_ChannelInfo[Channel].Uart_TxBuffPtr = NULL_PTR;
	mov.a	a15,#0
.L732:
	st.a	[a12]4,a15
.L875:

; ..\mcal_src\Uart.c	  2379       Uart_ChannelInfo[Channel].Uart_TxDataCopyCntr = UART_ZERO_U;
	mov	d15,#0
	st.b	[a13],d15
.L876:

; ..\mcal_src\Uart.c	  2380     }
; ..\mcal_src\Uart.c	  2381     else
; ..\mcal_src\Uart.c	  2382     {
; ..\mcal_src\Uart.c	  2383       /*Clear the transmission complete flag*/
; ..\mcal_src\Uart.c	  2384       HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &
; ..\mcal_src\Uart.c	  2385              UART_FLAGSCLEAR_TCC_MASK) | ((unsigned_int)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  2386                                                     UART_FLAGSCLEAR_TCC_BITPOS));
; ..\mcal_src\Uart.c	  2387  
; ..\mcal_src\Uart.c	  2388       if(Uart_ChannelInfo[Channel].Uart_TxDataLeft == UART_STEPSIZE)
; ..\mcal_src\Uart.c	  2389       {
; ..\mcal_src\Uart.c	  2390         /* Enable the transmission complete interrupt
; ..\mcal_src\Uart.c	  2391         for the last data transmission*/
; ..\mcal_src\Uart.c	  2392         HwModulePtr->FLAGSENABLE.U = (unsigned_int)((HwModulePtr->FLAGSENABLE.U &
; ..\mcal_src\Uart.c	  2393             UART_FLAGSENABLE_TCE_MASK) | ((unsigned_int)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  2394                                                     UART_FLAGSCLEAR_TCC_BITPOS));
; ..\mcal_src\Uart.c	  2395       }
; ..\mcal_src\Uart.c	  2396  
; ..\mcal_src\Uart.c	  2397       /*Disable TxFIFO*/
; ..\mcal_src\Uart.c	  2398       HwModulePtr->TXFIFOCON.U = ((HwModulePtr->TXFIFOCON.U &\ 
; ..\mcal_src\Uart.c	  2399                                   UART_TXFIFOCON_ENO_MASK) | UART_DISABLE_BIT);
; ..\mcal_src\Uart.c	  2400  
; ..\mcal_src\Uart.c	  2401       /*Clear Tx Overflow and Level Interrupt*/
; ..\mcal_src\Uart.c	  2402       Uart_lClearWriteInterrupts(HwModulePtr, UART_ISR_ACCESS);
; ..\mcal_src\Uart.c	  2403  
; ..\mcal_src\Uart.c	  2404       /* Write the Data to Tx FIFO */
; ..\mcal_src\Uart.c	  2405       Uart_lWrite(HwModulePtr,Channel);
; ..\mcal_src\Uart.c	  2406  
; ..\mcal_src\Uart.c	  2407       /*Enable the Tx Overflow and Level Interrupt*/
; ..\mcal_src\Uart.c	  2408       Uart_lEnableWriteInterrupts(HwModulePtr, UART_ISR_ACCESS);
; ..\mcal_src\Uart.c	  2409  
; ..\mcal_src\Uart.c	  2410       /*Enable TxFIFO*/
; ..\mcal_src\Uart.c	  2411       HwModulePtr->TXFIFOCON.U = (unsigned_int)((HwModulePtr->TXFIFOCON.U &\ 
; ..\mcal_src\Uart.c	  2412                UART_TXFIFOCON_ENO_MASK) | ((unsigned_int)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  2413                                                      UART_TXFIFOCON_ENO_BITPOS));
; ..\mcal_src\Uart.c	  2414     }
; ..\mcal_src\Uart.c	  2415    return ;
; ..\mcal_src\Uart.c	  2416  }
	ret
.L55:
	fcall	.cocofun_6
.L877:
	jne	d0,#1,.L57
.L878:
	fcall	.cocofun_11
.L57:
	ld.w	d15,[a15]12
.L879:
	mov	d4,#0
	mov.aa	a4,a15
.L737:
	insert	d15,d15,#0,#1,#1
	st.w	[a15]12,d15
.L880:
	call	Uart_lClearWriteInterrupts
.L738:
	mov.aa	a4,a15
.L739:
	mov	d4,d9
	call	Uart_lWrite
.L740:
	mov	d4,#0
	mov.aa	a4,a15
.L741:
	call	Uart_lEnableWriteInterrupts
.L742:
	ld.w	d15,[a15]12
.L881:
	or	d15,#2
	st.w	[a15]12,d15
.L882:
	ret
.L444:
	
__Uart_IsrTransmit_function_end:
	.size	Uart_IsrTransmit,__Uart_IsrTransmit_function_end-Uart_IsrTransmit
.L189:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_IsrError')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Uart_IsrError

; ..\mcal_src\Uart.c	  2417  
; ..\mcal_src\Uart.c	  2418  /*******************************************************************************
; ..\mcal_src\Uart.c	  2419  ** Syntax :          void Uart_IsrError( uint8  HwUnit)                       **
; ..\mcal_src\Uart.c	  2420  **                                                                            **
; ..\mcal_src\Uart.c	  2421  ** Service ID:       NA                                                       **
; ..\mcal_src\Uart.c	  2422  **                                                                            **
; ..\mcal_src\Uart.c	  2423  ** Sync/Async:       Asynchronous                                             **
; ..\mcal_src\Uart.c	  2424  **                                                                            **
; ..\mcal_src\Uart.c	  2425  ** Reentrancy:  Reentrant  (Not for the same HW Unit)                         **
; ..\mcal_src\Uart.c	  2426  **                                                                            **
; ..\mcal_src\Uart.c	  2427  ** Parameters (in):  HwUnit : Represents Hw Module number                     **
; ..\mcal_src\Uart.c	  2428  **                                                                            **
; ..\mcal_src\Uart.c	  2429  **                                                                            **
; ..\mcal_src\Uart.c	  2430  ** Parameters (out): none                                                     **
; ..\mcal_src\Uart.c	  2431  **                                                                            **
; ..\mcal_src\Uart.c	  2432  ** Return value:     none                                                     **
; ..\mcal_src\Uart.c	  2433  **                                                                            **
; ..\mcal_src\Uart.c	  2434  ** Description :     This ISR will be called whenever there is data           **
; ..\mcal_src\Uart.c	  2435  **                   transmission or reception error on ASCLINx HW Unit       **
; ..\mcal_src\Uart.c	  2436  *******************************************************************************/
; ..\mcal_src\Uart.c	  2437  void Uart_IsrError(uint8 HwUnit)
; Function Uart_IsrError
.L131:
Uart_IsrError:	.type	func

; ..\mcal_src\Uart.c	  2438  {
; ..\mcal_src\Uart.c	  2439    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2440     UART_HW_MODULE to access the SFRs of a particular HW Unit*/
; ..\mcal_src\Uart.c	  2441    Ifx_ASCLIN* HwModulePtr= &(UART_HW_MODULE[HwUnit]);
	sha	d15,d4,#8
	lea	a15,0xf0000600
.L887:
	addsc.a	a15,a15,d15,#0
.L745:

; ..\mcal_src\Uart.c	  2442    const Uart_ChannelType *ChannelConfigPtr;
; ..\mcal_src\Uart.c	  2443    Uart_ErrorIdType ErrId = UART_NO_ERR;
; ..\mcal_src\Uart.c	  2444    uint8 Channel = Uart_BusChannelMap[HwUnit];
	mov	d9,#0
	fcall	.cocofun_14
.L888:
	addsc.a	a2,a2,d4,#0
	ld.bu	d8,[a2]
.L743:

; ..\mcal_src\Uart.c	  2445  
; ..\mcal_src\Uart.c	  2446    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2447      ChannelConfigPtr to access the Configuration of Channel passed*/
; ..\mcal_src\Uart.c	  2448    ChannelConfigPtr = &(Uart_kConfigPtr->ChannelConfigPtr[Channel]);
	fcall	.cocofun_9
.L889:
	ld.a	a2,[a2]
.L890:
	sha	d15,d8,#5
.L891:
	addsc.a	a12,a2,d15,#0
.L746:

; ..\mcal_src\Uart.c	  2449  
; ..\mcal_src\Uart.c	  2450    /* Check parity error*/
; ..\mcal_src\Uart.c	  2451    if (((HwModulePtr->FLAGS.U & UART_FLAGS_PE_MASK) >> UART_FLAGS_PE_BITPOS) ==\ 
	ld.w	d15,[a15]52
.L892:
	jz.t	d15:16,.L58
.L893:

; ..\mcal_src\Uart.c	  2452                                                                      UART_ONE_U)
; ..\mcal_src\Uart.c	  2453    {
; ..\mcal_src\Uart.c	  2454      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a15]60
.L894:

; ..\mcal_src\Uart.c	  2455                   UART_FLAGSCLEAR_PEC_MASK) | ((unsigned_int)UART_ONE_U <<\ 
; ..\mcal_src\Uart.c	  2456                                                     UART_FLAGSCLEAR_PEC_BITPOS));
; ..\mcal_src\Uart.c	  2457      ErrId = UART_PARITY_ERR;
	mov	d9,#1
.L895:
	insert	d15,d15,#1,#16,#1
	st.w	[a15]60,d15
.L896:
	j	.L59
.L58:

; ..\mcal_src\Uart.c	  2458    }
; ..\mcal_src\Uart.c	  2459    /* Check framing error*/
; ..\mcal_src\Uart.c	  2460    else if(((HwModulePtr->FLAGS.U & UART_FLAGS_FE_MASK) >> UART_FLAGS_FE_BITPOS)\ 
	ld.w	d15,[a15]52
.L897:
	jz.t	d15:18,.L60
.L898:

; ..\mcal_src\Uart.c	  2461                                                                    == UART_ONE_U)
; ..\mcal_src\Uart.c	  2462    {
; ..\mcal_src\Uart.c	  2463      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a15]60
.L899:

; ..\mcal_src\Uart.c	  2464                   UART_FLAGSCLEAR_FEC_MASK) | ((unsigned_int)UART_ONE_U <<\ 
; ..\mcal_src\Uart.c	  2465                                                     UART_FLAGSCLEAR_FEC_BITPOS));
; ..\mcal_src\Uart.c	  2466      ErrId = UART_FRAME_ERR;
	mov	d9,#2
.L900:
	insert	d15,d15,#1,#18,#1
	st.w	[a15]60,d15
.L901:
	j	.L61
.L60:

; ..\mcal_src\Uart.c	  2467    }
; ..\mcal_src\Uart.c	  2468     /* Check TxFIFO Overflow error*/
; ..\mcal_src\Uart.c	  2469    else if(((HwModulePtr->FLAGS.U & UART_FLAGS_TFO_MASK) >>\ 
	ld.w	d15,[a15]52
.L902:
	jz.t	d15:30,.L62
.L903:

; ..\mcal_src\Uart.c	  2470                                             UART_FLAGS_TFO_BITPOS) == UART_ONE_U)
; ..\mcal_src\Uart.c	  2471    {
; ..\mcal_src\Uart.c	  2472      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a15]60
.L904:

; ..\mcal_src\Uart.c	  2473                  UART_FLAGSCLEAR_TFOC_MASK) | ((unsigned_int)UART_ONE_U <<\ 
; ..\mcal_src\Uart.c	  2474                                                  UART_FLAGSCLEAR_TFOC_BITPOS));
; ..\mcal_src\Uart.c	  2475      ErrId = UART_TXOVERFLOW_ERR;
	mov	d9,#3
.L905:
	insert	d15,d15,#1,#30,#1
	st.w	[a15]60,d15
.L906:
	j	.L63
.L62:

; ..\mcal_src\Uart.c	  2476    }
; ..\mcal_src\Uart.c	  2477      /* Check RxFIFO Overflow error*/
; ..\mcal_src\Uart.c	  2478    else if(((HwModulePtr->FLAGS.U & UART_FLAGS_RFO_MASK) >>\ 
	ld.w	d15,[a15]52
.L907:
	jz.t	d15:26,.L64
.L908:

; ..\mcal_src\Uart.c	  2479                                             UART_FLAGS_RFO_BITPOS) == UART_ONE_U)
; ..\mcal_src\Uart.c	  2480    {
; ..\mcal_src\Uart.c	  2481      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a15]60
.L909:

; ..\mcal_src\Uart.c	  2482                  UART_FLAGSCLEAR_RFOC_MASK) | ((unsigned_int)UART_ONE_U <<\ 
; ..\mcal_src\Uart.c	  2483                                                    UART_FLAGSCLEAR_RFOC_BITPOS));
; ..\mcal_src\Uart.c	  2484      ErrId = UART_RXOVERFLOW_ERR;
	mov	d9,#4
.L910:
	insert	d15,d15,#1,#26,#1
	st.w	[a15]60,d15
.L911:
	j	.L65
.L64:

; ..\mcal_src\Uart.c	  2485    }
; ..\mcal_src\Uart.c	  2486     /* Check RxFIFO Underflow error*/
; ..\mcal_src\Uart.c	  2487    else if(((HwModulePtr->FLAGS.U & UART_FLAGS_RFU_MASK) >> \ 
	ld.w	d15,[a15]52
.L912:
	jz.t	d15:27,.L66
.L913:

; ..\mcal_src\Uart.c	  2488                                             UART_FLAGS_RFU_BITPOS) == UART_ONE_U)
; ..\mcal_src\Uart.c	  2489    {
; ..\mcal_src\Uart.c	  2490      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	ld.w	d15,[a15]60
.L914:

; ..\mcal_src\Uart.c	  2491                  UART_FLAGSCLEAR_RFUC_MASK) | ((unsigned_int)UART_ONE_U <<\ 
; ..\mcal_src\Uart.c	  2492                                                    UART_FLAGSCLEAR_RFUC_BITPOS));
; ..\mcal_src\Uart.c	  2493      ErrId = UART_RXUNDERFLOW_ERR;
	mov	d9,#5
.L915:
	insert	d15,d15,#1,#27,#1
	st.w	[a15]60,d15
.L916:
	j	.L67
.L66:

; ..\mcal_src\Uart.c	  2494    }
; ..\mcal_src\Uart.c	  2495      /* Check Tx Complete */
; ..\mcal_src\Uart.c	  2496    else if(((HwModulePtr->FLAGS.U & UART_FLAGS_TC_MASK) >> UART_FLAGS_TC_BITPOS)\ 
	ld.w	d15,[a15]52
.L917:
	jz.t	d15:17,.L68
.L918:

; ..\mcal_src\Uart.c	  2497                                                                  == UART_ONE_U)
; ..\mcal_src\Uart.c	  2498    {
; ..\mcal_src\Uart.c	  2499      /* Disable TxFIFO */
; ..\mcal_src\Uart.c	  2500      HwModulePtr->TXFIFOCON.U = ((HwModulePtr->TXFIFOCON.U &\ 
	ld.w	d15,[a15]12
.L919:
	insert	d15,d15,#0,#1,#1
	st.w	[a15]12,d15
.L744:

; ..\mcal_src\Uart.c	  2501                                  UART_TXFIFOCON_ENO_MASK) | UART_DISABLE_BIT);
; ..\mcal_src\Uart.c	  2502  
; ..\mcal_src\Uart.c	  2503      /* Clear Transmit complete */
; ..\mcal_src\Uart.c	  2504      HwModulePtr->FLAGSCLEAR.U = (unsigned_int)((HwModulePtr->FLAGSCLEAR.U &\ 
	fcall	.cocofun_6
.L747:

; ..\mcal_src\Uart.c	  2505              UART_FLAGSCLEAR_TCC_MASK) | ((unsigned_int)UART_ENABLE_BIT <<\ 
; ..\mcal_src\Uart.c	  2506                                                     UART_FLAGSCLEAR_TCC_BITPOS));
; ..\mcal_src\Uart.c	  2507  
; ..\mcal_src\Uart.c	  2508      ErrId = UART_NO_ERR;
; ..\mcal_src\Uart.c	  2509  
; ..\mcal_src\Uart.c	  2510      /* Disable Tx complete interrupt */
; ..\mcal_src\Uart.c	  2511      HwModulePtr->FLAGSENABLE.U = ((HwModulePtr->FLAGSENABLE.U &\ 
	ld.w	d15,[a15]64
.L920:
	mov	d9,#0
.L921:
	insert	d15,d15,#0,#17,#1
	st.w	[a15]64,d15
.L922:

; ..\mcal_src\Uart.c	  2512                                 UART_FLAGSENABLE_TCE_MASK) | UART_DISABLE_BIT);
; ..\mcal_src\Uart.c	  2513  
; ..\mcal_src\Uart.c	  2514      /* Flush TxFIFO */
; ..\mcal_src\Uart.c	  2515      HwModulePtr->TXFIFOCON.U = ((HwModulePtr->TXFIFOCON.U &\ 
; ..\mcal_src\Uart.c	  2516                                 UART_TXFIFOCON_FLUSH_MASK) | UART_ENABLE_BIT);
; ..\mcal_src\Uart.c	  2517  
; ..\mcal_src\Uart.c	  2518      /* Disable Err interrupts */
; ..\mcal_src\Uart.c	  2519      Uart_lHwDisableAscLinErrIntr(HwUnit, UART_ISR_ACCESS);
	mov	d5,d9
.L748:
	ld.w	d15,[a15]12
.L923:
	or	d15,#1
	st.w	[a15]12,d15
.L924:
	call	Uart_lHwDisableAscLinErrIntr
.L68:
.L67:
.L65:
.L63:
.L61:
.L59:

; ..\mcal_src\Uart.c	  2520    }
; ..\mcal_src\Uart.c	  2521    else
; ..\mcal_src\Uart.c	  2522    {
; ..\mcal_src\Uart.c	  2523       /* Do Nothing */
; ..\mcal_src\Uart.c	  2524    }
; ..\mcal_src\Uart.c	  2525  
; ..\mcal_src\Uart.c	  2526    /*Call Notification fnt*/
; ..\mcal_src\Uart.c	  2527    if ((ErrId == UART_PARITY_ERR)||(ErrId == UART_FRAME_ERR)\ 
	jeq	d9,#1,.L69
.L925:
	jeq	d9,#2,.L70
.L926:

; ..\mcal_src\Uart.c	  2528     ||(ErrId == UART_RXOVERFLOW_ERR)||(ErrId == UART_RXUNDERFLOW_ERR))
	jeq	d9,#4,.L71
.L927:
	jne	d9,#5,.L72
.L71:
.L70:
.L69:

; ..\mcal_src\Uart.c	  2529    {
; ..\mcal_src\Uart.c	  2530      /* Release the lock so that other Uart Read Operation can be started*/
; ..\mcal_src\Uart.c	  2531      Mcal_UnlockResource(&Uart_ChLock[UART_RX_LOCK_IDX][Channel]);
	fcall	.cocofun_8
.L928:
	add.a	a2,#4
.L929:
	addsc.a	a4,a2,d8,#2
	call	Mcal_UnlockResource
.L930:

; ..\mcal_src\Uart.c	  2532  
; ..\mcal_src\Uart.c	  2533      Uart_ChannelInfo[Channel].Uart_RxState = UART_INITIALISED;
	mul	d15,d8,#20
	movh.a	a2,#@his(Uart_ChannelInfo)
	lea	a2,[a2]@los(Uart_ChannelInfo)
.L931:
	addsc.a	a15,a2,d15,#0
.L749:
	mov	d15,#1
.L932:
	st.b	[a15]9,d15
.L933:

; ..\mcal_src\Uart.c	  2534  
; ..\mcal_src\Uart.c	  2535      if(ChannelConfigPtr->UartNotif.UartReceiveNotifPtr != NULL_PTR)
	ld.a	a15,[a12]4
.L934:
	jz.a	a15,.L73
.L935:

; ..\mcal_src\Uart.c	  2536      {
; ..\mcal_src\Uart.c	  2537        /*Call Receive Notification Function*/
; ..\mcal_src\Uart.c	  2538        ChannelConfigPtr->UartNotif.UartReceiveNotifPtr(ErrId);
	mov	d4,d9
	calli	a15
.L73:
.L72:

; ..\mcal_src\Uart.c	  2539      }
; ..\mcal_src\Uart.c	  2540    }
; ..\mcal_src\Uart.c	  2541    else
; ..\mcal_src\Uart.c	  2542    {
; ..\mcal_src\Uart.c	  2543       /* Do Nothing */
; ..\mcal_src\Uart.c	  2544    }
; ..\mcal_src\Uart.c	  2545  
; ..\mcal_src\Uart.c	  2546    if ((ErrId == UART_TXOVERFLOW_ERR)||(ErrId == UART_NO_ERR))
	jeq	d9,#3,.L74
.L936:
	jne	d9,#0,.L75
.L74:

; ..\mcal_src\Uart.c	  2547    {
; ..\mcal_src\Uart.c	  2548      /* Release the lock so that other Uart Write Operation can be started*/
; ..\mcal_src\Uart.c	  2549      Mcal_UnlockResource(&Uart_ChLock[UART_TX_LOCK_IDX][Channel]);
	movh.a	a15,#@his(Uart_ChLock)
	lea	a15,[a15]@los(Uart_ChLock)
.L937:
	addsc.a	a4,a15,d8,#2
	call	Mcal_UnlockResource
.L938:

; ..\mcal_src\Uart.c	  2550  
; ..\mcal_src\Uart.c	  2551      Uart_ChannelInfo[Channel].Uart_TxState = UART_INITIALISED;
	fcall	.cocofun_10
	addsc.a	a15,a15,d15,#0
.L939:
	mov	d15,#1
.L940:
	st.b	[a15]8,d15
.L941:

; ..\mcal_src\Uart.c	  2552  
; ..\mcal_src\Uart.c	  2553      if(ChannelConfigPtr->UartNotif.UartTransmitNotifPtr != NULL_PTR)
	ld.a	a15,[a12]
.L942:
	jz.a	a15,.L76
.L943:

; ..\mcal_src\Uart.c	  2554      {
; ..\mcal_src\Uart.c	  2555         /*Call Transmit Notification Function*/
; ..\mcal_src\Uart.c	  2556        ChannelConfigPtr->UartNotif.UartTransmitNotifPtr(ErrId);
	mov	d4,d9
.L750:
	ji	a15
.L76:
.L75:

; ..\mcal_src\Uart.c	  2557      }
; ..\mcal_src\Uart.c	  2558    }
; ..\mcal_src\Uart.c	  2559    else
; ..\mcal_src\Uart.c	  2560    {
; ..\mcal_src\Uart.c	  2561       /* Do Nothing */
; ..\mcal_src\Uart.c	  2562    }
; ..\mcal_src\Uart.c	  2563  
; ..\mcal_src\Uart.c	  2564  }
	ret
.L449:
	
__Uart_IsrError_function_end:
	.size	Uart_IsrError,__Uart_IsrError_function_end-Uart_IsrError
.L194:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lHwDisableAscLinErrIntr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  2565  /*******************************************************************************
; ..\mcal_src\Uart.c	  2566  ** Syntax : static Uart_ReturnType Uart_lChannelCheck                         **
; ..\mcal_src\Uart.c	  2567  **  (                                                                         **
; ..\mcal_src\Uart.c	  2568  **    Uart_ChannelIdType Channel,uint8 ApiId                                  **
; ..\mcal_src\Uart.c	  2569  **  )                                                                         **
; ..\mcal_src\Uart.c	  2570  **                                                                            **
; ..\mcal_src\Uart.c	  2571  ** Service ID:    NA                                                          **
; ..\mcal_src\Uart.c	  2572  **                                                                            **
; ..\mcal_src\Uart.c	  2573  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Uart.c	  2574  **                                                                            **
; ..\mcal_src\Uart.c	  2575  ** Reentrancy:      reentrant                                                 **
; ..\mcal_src\Uart.c	  2576  **                                                                            **
; ..\mcal_src\Uart.c	  2577  ** Parameters (in): Channel - Uart channel to be addressed                    **
; ..\mcal_src\Uart.c	  2578  **                  ApiId - Service id of the Api which calls this function   **
; ..\mcal_src\Uart.c	  2579  **                                                                            **
; ..\mcal_src\Uart.c	  2580  ** Parameters (out): None                                                     **
; ..\mcal_src\Uart.c	  2581  **                                                                            **
; ..\mcal_src\Uart.c	  2582  ** Return value    : UART_OK / UART_NOT_OK                                    **
; ..\mcal_src\Uart.c	  2583  **                                                                            **
; ..\mcal_src\Uart.c	  2584  ** Description : Local function to check whether channel is valid or not      **
; ..\mcal_src\Uart.c	  2585  **                                                                            **
; ..\mcal_src\Uart.c	  2586  *******************************************************************************/
; ..\mcal_src\Uart.c	  2587  #if (UART_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Uart.c	  2588  static Uart_ReturnType Uart_lChannelCheck(\ 
; ..\mcal_src\Uart.c	  2589                                         Uart_ChannelIdType Channel,uint8 ApiId)
; ..\mcal_src\Uart.c	  2590  {
; ..\mcal_src\Uart.c	  2591    uint8 MaxChannel;
; ..\mcal_src\Uart.c	  2592    Uart_ReturnType Retvalue;
; ..\mcal_src\Uart.c	  2593  
; ..\mcal_src\Uart.c	  2594    MaxChannel = Uart_kConfigPtr->NoOfChannels;
; ..\mcal_src\Uart.c	  2595  
; ..\mcal_src\Uart.c	  2596    if(Channel < MaxChannel)
; ..\mcal_src\Uart.c	  2597    {
; ..\mcal_src\Uart.c	  2598      Retvalue = UART_OK;
; ..\mcal_src\Uart.c	  2599    }
; ..\mcal_src\Uart.c	  2600    else
; ..\mcal_src\Uart.c	  2601    {
; ..\mcal_src\Uart.c	  2602      /* If the channelid is invalid Report to  DET */
; ..\mcal_src\Uart.c	  2603  
; ..\mcal_src\Uart.c	  2604      Det_ReportError(
; ..\mcal_src\Uart.c	  2605                      UART_MODULE_ID,
; ..\mcal_src\Uart.c	  2606                      UART_MODULE_INSTANCE,
; ..\mcal_src\Uart.c	  2607                      ApiId,
; ..\mcal_src\Uart.c	  2608                      UART_E_INVALID_CHANNEL
; ..\mcal_src\Uart.c	  2609                     );
; ..\mcal_src\Uart.c	  2610  
; ..\mcal_src\Uart.c	  2611      Retvalue = UART_NOT_OK;
; ..\mcal_src\Uart.c	  2612    }
; ..\mcal_src\Uart.c	  2613  
; ..\mcal_src\Uart.c	  2614    return Retvalue;
; ..\mcal_src\Uart.c	  2615  }
; ..\mcal_src\Uart.c	  2616  #endif
; ..\mcal_src\Uart.c	  2617  
; ..\mcal_src\Uart.c	  2618  
; ..\mcal_src\Uart.c	  2619  /*******************************************************************************
; ..\mcal_src\Uart.c	  2620  ** Syntax           :  static void Uart_lHwDisableAscLinErrIntr               **
; ..\mcal_src\Uart.c	  2621  **                      (uint8 HwUnit, uint8 ApiAccessId)                     **
; ..\mcal_src\Uart.c	  2622  **                                                                            **
; ..\mcal_src\Uart.c	  2623  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	  2624  **                                                                            **
; ..\mcal_src\Uart.c	  2625  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	  2626  **                                                                            **
; ..\mcal_src\Uart.c	  2627  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	  2628  **                                                                            **
; ..\mcal_src\Uart.c	  2629  ** Parameters (in)  : HwUnit        : ASCLIN Hardware module no               **
; ..\mcal_src\Uart.c	  2630                      : ApiAccessId   : API access type                         **
; ..\mcal_src\Uart.c	  2631  **                                                                            **
; ..\mcal_src\Uart.c	  2632  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	  2633  **                                                                            **
; ..\mcal_src\Uart.c	  2634  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	  2635  **                                                                            **
; ..\mcal_src\Uart.c	  2636  ** Description      : This function clears and disables the  Err              **
; ..\mcal_src\Uart.c	  2637  **                    interrupts in SRC registers.                            **
; ..\mcal_src\Uart.c	  2638  *******************************************************************************/
; ..\mcal_src\Uart.c	  2639  static void Uart_lHwDisableAscLinErrIntr(volatile uint8 HwUnit,\ 
; Function Uart_lHwDisableAscLinErrIntr
.L133:
Uart_lHwDisableAscLinErrIntr:	.type	func

; ..\mcal_src\Uart.c	  2640                                                                uint8 ApiAccessId)
; ..\mcal_src\Uart.c	  2641  {
; ..\mcal_src\Uart.c	  2642    /* Ifx_SRC_SRCR_Bits IntrSrc; */
; ..\mcal_src\Uart.c	  2643    uint32 Offset,IntrSrc;
; ..\mcal_src\Uart.c	  2644  
; ..\mcal_src\Uart.c	  2645    Offset = (uint32)HwUnit * UART_SRC_ADDROFFSET ;
	fcall	.cocofun_15
.L1064:

; ..\mcal_src\Uart.c	  2646    #if (UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2647    #if (UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2648    if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  2649    {
; ..\mcal_src\Uart.c	  2650      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2651      UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2652      IntrSrc = UART_SFR_RUNTIME_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	  2653                                            (*(UART_SRC_ASCLIN0ERRADDR + Offset));
; ..\mcal_src\Uart.c	  2654    }
; ..\mcal_src\Uart.c	  2655    else
; ..\mcal_src\Uart.c	  2656    #endif /*(UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2657    #if (UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2658    if (ApiAccessId == UART_DEINIT_ACCESS)
; ..\mcal_src\Uart.c	  2659    {
; ..\mcal_src\Uart.c	  2660      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2661      UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2662      IntrSrc = UART_SFR_DEINIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	  2663                                            (*(UART_SRC_ASCLIN0ERRADDR + Offset));
; ..\mcal_src\Uart.c	  2664    }
; ..\mcal_src\Uart.c	  2665    else
; ..\mcal_src\Uart.c	  2666    #endif /*(UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2667    #endif /*(UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2668    {
; ..\mcal_src\Uart.c	  2669      /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2670        UART_SRC_ASCLINxERRADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2671      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2672      UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2673      IntrSrc = (*((volatile uint32 *)(void *)\ 
; ..\mcal_src\Uart.c	  2674                                           (UART_SRC_ASCLIN0ERRADDR  + Offset)));
	fcall	.cocofun_2
.L755:

; ..\mcal_src\Uart.c	  2675    }
; ..\mcal_src\Uart.c	  2676  
; ..\mcal_src\Uart.c	  2677    if (((IntrSrc & UART_SET_MASK_ENABLE_INTR)>>10U) == UART_BIT_SET)
; ..\mcal_src\Uart.c	  2678    {
; ..\mcal_src\Uart.c	  2679      #if (UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2680      if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  2681      {
; ..\mcal_src\Uart.c	  2682        /*Reserve bit access is ensured for SRC_ASCLIN0ERR register*/
; ..\mcal_src\Uart.c	  2683        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2684        UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2685        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2686        UART_SRC_ASCLINxERRADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2687        UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)\ 
; ..\mcal_src\Uart.c	  2688        (void *)(UART_SRC_ASCLIN0ERRADDR + Offset)), ~UART_CLR_MASK_DISABLE_INTR,\ 
; ..\mcal_src\Uart.c	  2689                                                 UART_SET_MASK_DISABLE_INTR)
; ..\mcal_src\Uart.c	  2690      }
; ..\mcal_src\Uart.c	  2691      else
; ..\mcal_src\Uart.c	  2692      #endif /*(UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2693      #if (UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2694      if (ApiAccessId == UART_DEINIT_ACCESS)
; ..\mcal_src\Uart.c	  2695      {
; ..\mcal_src\Uart.c	  2696        /*Reserve bit access is ensured for SRC_ASCLIN0ERR register*/
; ..\mcal_src\Uart.c	  2697        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2698        UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2699        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2700        UART_SRC_ASCLINxERRADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2701        UART_SFR_DEINIT_MODIFY32(*((volatile uint32 *)\ 
; ..\mcal_src\Uart.c	  2702        (void *)(UART_SRC_ASCLIN0ERRADDR + Offset)), ~UART_CLR_MASK_DISABLE_INTR,\ 
; ..\mcal_src\Uart.c	  2703                                                 UART_SET_MASK_DISABLE_INTR)
; ..\mcal_src\Uart.c	  2704      }
; ..\mcal_src\Uart.c	  2705      else
; ..\mcal_src\Uart.c	  2706      #endif /*(UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2707      {
; ..\mcal_src\Uart.c	  2708        /*Reserve bit access is ensured for SRC_ASCLIN0ERR register*/
; ..\mcal_src\Uart.c	  2709        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2710        UART_SRC_ASCLINxERRADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2711        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2712        UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2713        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2714        UART_SRC_ASCLINxERRADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2715        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2716        UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2717        *((volatile uint32 *)(void *)(UART_SRC_ASCLIN0ERRADDR + Offset)) = \ 
; ..\mcal_src\Uart.c	  2718          (*((volatile uint32 *)(void *)(UART_SRC_ASCLIN0ERRADDR + Offset)) & \ 
; ..\mcal_src\Uart.c	  2719                     (~UART_CLR_MASK_DISABLE_INTR)) | UART_SET_MASK_DISABLE_INTR;
; ..\mcal_src\Uart.c	  2720  
; ..\mcal_src\Uart.c	  2721        UNUSED_PARAMETER(ApiAccessId)
; ..\mcal_src\Uart.c	  2722      }
; ..\mcal_src\Uart.c	  2723    }
; ..\mcal_src\Uart.c	  2724  }
	ret
.L601:
	
__Uart_lHwDisableAscLinErrIntr_function_end:
	.size	Uart_lHwDisableAscLinErrIntr,__Uart_lHwDisableAscLinErrIntr_function_end-Uart_lHwDisableAscLinErrIntr
.L259:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_15')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_15
.L135:
.cocofun_15:	.type	func
; Function body .cocofun_15, coco_iter:1
	fcall	.cocofun_18
.L752:
	lea	a15,[a15]@los(0xf0038088)
.L1141:
	fret
.L334:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_18')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_18
.L137:
.cocofun_18:	.type	func
; Function body .cocofun_18, coco_iter:2
	mul	d15,d4,#3
	movh.a	a15,#61444
.L751:
	fret
.L349:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_2')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_2
.L139:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	addsc.a	a15,a15,d15,#2
.L1074:
	ld.w	d15,[a15]
.L753:
	jz.t	d15:10,.L77
.L1075:
	mov.u	d0,#64511
	ld.w	d15,[a15]
.L754:
	addih	d0,d0,#32767
.L1076:
	and	d15,d0
.L1077:
	insert	d15,d15,#1,#25,#1
	st.w	[a15],d15
.L77:
	fret
.L269:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lHwDisableAscLinRxIntr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  2725  
; ..\mcal_src\Uart.c	  2726  /*******************************************************************************
; ..\mcal_src\Uart.c	  2727  ** Syntax           :    static void Uart_lHwDisableAscLinRxIntr              **
; ..\mcal_src\Uart.c	  2728  **                      (uint8 HwUnit, uint8 ApiAccessId)                     **
; ..\mcal_src\Uart.c	  2729  **                                                                            **
; ..\mcal_src\Uart.c	  2730  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	  2731  **                                                                            **
; ..\mcal_src\Uart.c	  2732  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	  2733  **                                                                            **
; ..\mcal_src\Uart.c	  2734  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	  2735  **                                                                            **
; ..\mcal_src\Uart.c	  2736  ** Parameters (in)  : HwUnit        : ASCLIN Hardware module no               **
; ..\mcal_src\Uart.c	  2737  **                  : ApiAccessId   : API access type                         **
; ..\mcal_src\Uart.c	  2738  **                                                                            **
; ..\mcal_src\Uart.c	  2739  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	  2740  **                                                                            **
; ..\mcal_src\Uart.c	  2741  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	  2742  **                                                                            **
; ..\mcal_src\Uart.c	  2743  ** Description      : This function clears and disables the   RX              **
; ..\mcal_src\Uart.c	  2744  **                    interrupts in SRC registers.                            **
; ..\mcal_src\Uart.c	  2745  *******************************************************************************/
; ..\mcal_src\Uart.c	  2746  static void Uart_lHwDisableAscLinRxIntr(volatile uint8 HwUnit,\ 
; Function Uart_lHwDisableAscLinRxIntr
.L141:
Uart_lHwDisableAscLinRxIntr:	.type	func

; ..\mcal_src\Uart.c	  2747                                                                uint8 ApiAccessId)
; ..\mcal_src\Uart.c	  2748  {
; ..\mcal_src\Uart.c	  2749    /*Ifx_SRC_SRCR_Bits IntrSrc;*/
; ..\mcal_src\Uart.c	  2750    uint32 Offset,IntrSrc;
; ..\mcal_src\Uart.c	  2751  
; ..\mcal_src\Uart.c	  2752    Offset = (uint32)HwUnit * UART_SRC_ADDROFFSET ;
	fcall	.cocofun_16
.L1059:

; ..\mcal_src\Uart.c	  2753    #if (UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2754    #if (UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2755    if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  2756    {
; ..\mcal_src\Uart.c	  2757      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2758      UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2759      IntrSrc = UART_SFR_RUNTIME_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	  2760                                            (*(UART_SRC_ASCLIN0RXADDR + Offset));
; ..\mcal_src\Uart.c	  2761    }
; ..\mcal_src\Uart.c	  2762    else
; ..\mcal_src\Uart.c	  2763    #endif /*(UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2764    #if (UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2765    if (ApiAccessId == UART_DEINIT_ACCESS)
; ..\mcal_src\Uart.c	  2766    {
; ..\mcal_src\Uart.c	  2767      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2768      UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2769      IntrSrc = UART_SFR_DEINIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	  2770                                      (*(UART_SRC_ASCLIN0RXADDR + Offset));
; ..\mcal_src\Uart.c	  2771    }
; ..\mcal_src\Uart.c	  2772    else
; ..\mcal_src\Uart.c	  2773    #endif /*(UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2774    #endif /*(UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2775    {
; ..\mcal_src\Uart.c	  2776      /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2777        UART_SRC_ASCLINxERRADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2778      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2779      UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2780      IntrSrc =(*((volatile uint32 *)(void *)(UART_SRC_ASCLIN0RXADDR  + Offset)));
	fcall	.cocofun_2
.L757:

; ..\mcal_src\Uart.c	  2781    }
; ..\mcal_src\Uart.c	  2782  
; ..\mcal_src\Uart.c	  2783    if (((IntrSrc & UART_SET_MASK_ENABLE_INTR)>>10U) == UART_BIT_SET)
; ..\mcal_src\Uart.c	  2784    {
; ..\mcal_src\Uart.c	  2785      #if (UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2786      if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  2787      {
; ..\mcal_src\Uart.c	  2788        /*Reserve bit access is ensured for SRC_ASCLIN0ERR register*/
; ..\mcal_src\Uart.c	  2789        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2790        UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2791        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2792        UART_SRC_ASCLINxRXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2793        UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)\ 
; ..\mcal_src\Uart.c	  2794        (void *)(UART_SRC_ASCLIN0RXADDR + Offset)), ~UART_CLR_MASK_DISABLE_INTR,\ 
; ..\mcal_src\Uart.c	  2795                                                 UART_SET_MASK_DISABLE_INTR)
; ..\mcal_src\Uart.c	  2796      }
; ..\mcal_src\Uart.c	  2797      else
; ..\mcal_src\Uart.c	  2798      #endif /*(UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2799      #if (UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2800      if (ApiAccessId == UART_DEINIT_ACCESS)
; ..\mcal_src\Uart.c	  2801      {
; ..\mcal_src\Uart.c	  2802        /*Reserve bit access is ensured for SRC_ASCLIN0ERR register*/
; ..\mcal_src\Uart.c	  2803        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2804        UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2805        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2806        UART_SRC_ASCLINxRXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2807        UART_SFR_DEINIT_MODIFY32(*((volatile uint32 *)\ 
; ..\mcal_src\Uart.c	  2808        (void *)(UART_SRC_ASCLIN0RXADDR + Offset)), ~UART_CLR_MASK_DISABLE_INTR,\ 
; ..\mcal_src\Uart.c	  2809                                                 UART_SET_MASK_DISABLE_INTR)
; ..\mcal_src\Uart.c	  2810      }
; ..\mcal_src\Uart.c	  2811      else
; ..\mcal_src\Uart.c	  2812      #endif /*(UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2813      {
; ..\mcal_src\Uart.c	  2814        /*Reserve bit access is ensured for SRC_ASCLIN0ERR register*/
; ..\mcal_src\Uart.c	  2815        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2816        UART_SRC_ASCLINxRXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2817        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2818        UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2819        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2820        UART_SRC_ASCLINxRXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2821        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2822        UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2823        *((volatile uint32 *)\ 
; ..\mcal_src\Uart.c	  2824         (void *)(UART_SRC_ASCLIN0RXADDR + Offset)) = (*((volatile uint32 *)\ 
; ..\mcal_src\Uart.c	  2825         (void *)(UART_SRC_ASCLIN0RXADDR + Offset)) &\ 
; ..\mcal_src\Uart.c	  2826                      (~UART_CLR_MASK_DISABLE_INTR)) | UART_SET_MASK_DISABLE_INTR;
; ..\mcal_src\Uart.c	  2827  
; ..\mcal_src\Uart.c	  2828        UNUSED_PARAMETER(ApiAccessId)
; ..\mcal_src\Uart.c	  2829      }
; ..\mcal_src\Uart.c	  2830    }
; ..\mcal_src\Uart.c	  2831  }
	ret
.L594:
	
__Uart_lHwDisableAscLinRxIntr_function_end:
	.size	Uart_lHwDisableAscLinRxIntr,__Uart_lHwDisableAscLinRxIntr_function_end-Uart_lHwDisableAscLinRxIntr
.L254:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_16')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_16
.L143:
.cocofun_16:	.type	func
; Function body .cocofun_16, coco_iter:1
	fcall	.cocofun_18
.L756:
	lea	a15,[a15]@los(0xf0038084)
.L1146:
	fret
.L339:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lHwDisableAscLinTxIntr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  2832  /*******************************************************************************
; ..\mcal_src\Uart.c	  2833  ** Syntax           :  static void Uart_lHwDisableAscLinTxIntr                **
; ..\mcal_src\Uart.c	  2834  **                      (uint8 HwUnit, uint8 ApiAccessId)                     **
; ..\mcal_src\Uart.c	  2835  **                                                                            **
; ..\mcal_src\Uart.c	  2836  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	  2837  **                                                                            **
; ..\mcal_src\Uart.c	  2838  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	  2839  **                                                                            **
; ..\mcal_src\Uart.c	  2840  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	  2841  **                                                                            **
; ..\mcal_src\Uart.c	  2842  ** Parameters (in)  : HwUnit        : ASCLIN Hardware module no               **
; ..\mcal_src\Uart.c	  2843  **                  : ApiAccessId   : API access type                         **
; ..\mcal_src\Uart.c	  2844  **                                                                            **
; ..\mcal_src\Uart.c	  2845  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	  2846  **                                                                            **
; ..\mcal_src\Uart.c	  2847  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	  2848  **                                                                            **
; ..\mcal_src\Uart.c	  2849  ** Description      : This function clears and disables the TX                **
; ..\mcal_src\Uart.c	  2850  **                    interrupts in SRC registers.                            **
; ..\mcal_src\Uart.c	  2851  *******************************************************************************/
; ..\mcal_src\Uart.c	  2852  static void Uart_lHwDisableAscLinTxIntr(volatile uint8 HwUnit,uint8 ApiAccessId)
; Function Uart_lHwDisableAscLinTxIntr
.L145:
Uart_lHwDisableAscLinTxIntr:	.type	func

; ..\mcal_src\Uart.c	  2853  {
; ..\mcal_src\Uart.c	  2854    /*Ifx_SRC_SRCR_Bits IntrSrc;*/
; ..\mcal_src\Uart.c	  2855    uint32 Offset,IntrSrc;
; ..\mcal_src\Uart.c	  2856  
; ..\mcal_src\Uart.c	  2857    Offset = (uint32)HwUnit * UART_SRC_ADDROFFSET ;
	fcall	.cocofun_17
.L1054:

; ..\mcal_src\Uart.c	  2858    #if (UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2859    #if (UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2860    if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  2861    {
; ..\mcal_src\Uart.c	  2862      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2863       UART_SRC_ASCLIN0TXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2864      IntrSrc = UART_SFR_RUNTIME_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	  2865                                      (*(UART_SRC_ASCLIN0TXADDR + Offset));
; ..\mcal_src\Uart.c	  2866    }
; ..\mcal_src\Uart.c	  2867    else
; ..\mcal_src\Uart.c	  2868    #endif /*(UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2869    #if (UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2870    if (ApiAccessId == UART_DEINIT_ACCESS)
; ..\mcal_src\Uart.c	  2871    {
; ..\mcal_src\Uart.c	  2872      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2873      UART_SRC_ASCLIN0TXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2874      IntrSrc = UART_SFR_DEINIT_USER_MODE_READ32\ 
; ..\mcal_src\Uart.c	  2875                                            (*(UART_SRC_ASCLIN0TXADDR + Offset));
; ..\mcal_src\Uart.c	  2876    }
; ..\mcal_src\Uart.c	  2877    else
; ..\mcal_src\Uart.c	  2878    #endif /*(UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2879    #endif /*(UART_RUNNING_IN_USER_0_MODE_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2880    {
; ..\mcal_src\Uart.c	  2881      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2882      UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2883      /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2884      UART_SRC_ASCLINxRXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2885      IntrSrc = (*((volatile uint32 *)(void *)(UART_SRC_ASCLIN0TXADDR + Offset)));
	fcall	.cocofun_2
.L759:

; ..\mcal_src\Uart.c	  2886    }
; ..\mcal_src\Uart.c	  2887  
; ..\mcal_src\Uart.c	  2888  
; ..\mcal_src\Uart.c	  2889    if (((IntrSrc & UART_SET_MASK_ENABLE_INTR)>>10U) == UART_BIT_SET)
; ..\mcal_src\Uart.c	  2890    {
; ..\mcal_src\Uart.c	  2891      #if (UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2892      if (ApiAccessId == UART_RUNTIME_ACCESS)
; ..\mcal_src\Uart.c	  2893      {
; ..\mcal_src\Uart.c	  2894        /*Reserve bit access is ensured for SRC_ASCLIN0TX register*/
; ..\mcal_src\Uart.c	  2895        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2896        UART_SRC_ASCLIN0TXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2897        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2898        UART_SRC_ASCLINxTXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2899        UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)(void *)\ 
; ..\mcal_src\Uart.c	  2900                  (UART_SRC_ASCLIN0TXADDR + Offset)),~UART_CLR_MASK_DISABLE_INTR,\ 
; ..\mcal_src\Uart.c	  2901                                                       UART_SET_MASK_DISABLE_INTR)
; ..\mcal_src\Uart.c	  2902      }
; ..\mcal_src\Uart.c	  2903      else
; ..\mcal_src\Uart.c	  2904      #endif /*(UART_USER_MODE_RUNTIME_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2905      #if (UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Uart.c	  2906      if (ApiAccessId == UART_DEINIT_ACCESS)
; ..\mcal_src\Uart.c	  2907      {
; ..\mcal_src\Uart.c	  2908        /*Reserve bit access is ensured for SRC_ASCLIN0TX register*/
; ..\mcal_src\Uart.c	  2909        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2910        UART_SRC_ASCLIN0TXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2911        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2912        UART_SRC_ASCLINxTXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2913        UART_SFR_DEINIT_MODIFY32(*((volatile uint32 *)(void *)\ 
; ..\mcal_src\Uart.c	  2914                  (UART_SRC_ASCLIN0TXADDR + Offset)),~UART_CLR_MASK_DISABLE_INTR,\ 
; ..\mcal_src\Uart.c	  2915                                                       UART_SET_MASK_DISABLE_INTR)
; ..\mcal_src\Uart.c	  2916      }
; ..\mcal_src\Uart.c	  2917      else
; ..\mcal_src\Uart.c	  2918      #endif /*(UART_USER_MODE_DEINIT_API_ENABLE == STD_ON)*/
; ..\mcal_src\Uart.c	  2919      {
; ..\mcal_src\Uart.c	  2920        /*Reserve bit access is ensured for SRC_ASCLIN0TX register*/
; ..\mcal_src\Uart.c	  2921        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2922        UART_SRC_ASCLINxTXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2923        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2924        UART_SRC_ASCLIN0TXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2925        /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2926        UART_SRC_ASCLINxTXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2927        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2928        UART_SRC_ASCLIN0TXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2929        *((volatile uint32 *)\ 
; ..\mcal_src\Uart.c	  2930         (void *)(UART_SRC_ASCLIN0TXADDR + Offset)) = (*((volatile uint32 *)\ 
; ..\mcal_src\Uart.c	  2931         (void *)(UART_SRC_ASCLIN0TXADDR + Offset)) &\ 
; ..\mcal_src\Uart.c	  2932                      (~UART_CLR_MASK_DISABLE_INTR)) | UART_SET_MASK_DISABLE_INTR;
; ..\mcal_src\Uart.c	  2933  
; ..\mcal_src\Uart.c	  2934        UNUSED_PARAMETER(ApiAccessId)
; ..\mcal_src\Uart.c	  2935      }
; ..\mcal_src\Uart.c	  2936    }
; ..\mcal_src\Uart.c	  2937  }
	ret
.L587:
	
__Uart_lHwDisableAscLinTxIntr_function_end:
	.size	Uart_lHwDisableAscLinTxIntr,__Uart_lHwDisableAscLinTxIntr_function_end-Uart_lHwDisableAscLinTxIntr
.L249:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_17')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_17
.L147:
.cocofun_17:	.type	func
; Function body .cocofun_17, coco_iter:1
	fcall	.cocofun_18
.L758:
	lea	a15,[a15]@los(0xf0038080)
.L1151:
	fret
.L344:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lHwEnableAscLinErrIntr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  2938  
; ..\mcal_src\Uart.c	  2939  /*******************************************************************************
; ..\mcal_src\Uart.c	  2940  ** Syntax           : static void Uart_lHwEnableAscLinErrIntr                 **
; ..\mcal_src\Uart.c	  2941  **                      (uint8 HwUnit)                                        **
; ..\mcal_src\Uart.c	  2942  **                                                                            **
; ..\mcal_src\Uart.c	  2943  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	  2944  **                                                                            **
; ..\mcal_src\Uart.c	  2945  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	  2946  **                                                                            **
; ..\mcal_src\Uart.c	  2947  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	  2948  **                                                                            **
; ..\mcal_src\Uart.c	  2949  ** Parameters (in)  : HwUnit   : ASCLIN Hardware module no                    **
; ..\mcal_src\Uart.c	  2950  **                                                                            **
; ..\mcal_src\Uart.c	  2951  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	  2952  **                                                                            **
; ..\mcal_src\Uart.c	  2953  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	  2954  **                                                                            **
; ..\mcal_src\Uart.c	  2955  ** Description      : This function clears and enables the  Err               **
; ..\mcal_src\Uart.c	  2956  **                    interrupts in SRC registers.                            **
; ..\mcal_src\Uart.c	  2957  *******************************************************************************/
; ..\mcal_src\Uart.c	  2958  static void Uart_lHwEnableAscLinErrIntr(volatile uint8 HwUnit)
; Function Uart_lHwEnableAscLinErrIntr
.L149:
Uart_lHwEnableAscLinErrIntr:	.type	func

; ..\mcal_src\Uart.c	  2959  {
; ..\mcal_src\Uart.c	  2960    uint32 Offset;
; ..\mcal_src\Uart.c	  2961  
; ..\mcal_src\Uart.c	  2962    Offset = (uint32)HwUnit * UART_SRC_ADDROFFSET ;
	fcall	.cocofun_15
.L584:

; ..\mcal_src\Uart.c	  2963  
; ..\mcal_src\Uart.c	  2964    /*Reserve bit access is ensured for SRC_ASCLIN0ERR register*/
; ..\mcal_src\Uart.c	  2965    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2966    UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2967    /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2968    UART_SRC_ASCLINxERRADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2969    UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)(void *)\ 
	fcall	.cocofun_1
.L585:

; ..\mcal_src\Uart.c	  2970                   (UART_SRC_ASCLIN0ERRADDR + Offset)), ~UART_CLR_MASK_CLR_INTR,\ 
; ..\mcal_src\Uart.c	  2971                                                      UART_SET_MASK_CLR_INTR)
; ..\mcal_src\Uart.c	  2972  
; ..\mcal_src\Uart.c	  2973    /*Reserve bit access is ensured for SRC_ASCLIN0ERR register*/
; ..\mcal_src\Uart.c	  2974    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  2975    UART_SRC_ASCLIN0ERRADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  2976    /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  2977    UART_SRC_ASCLINxERRADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  2978    UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)(void *)\ 
; ..\mcal_src\Uart.c	  2979                 (UART_SRC_ASCLIN0ERRADDR + Offset)), ~UART_CLR_MASK_ENABLE_INTR,\ 
; ..\mcal_src\Uart.c	  2980                                                      UART_SET_MASK_ENABLE_INTR)
; ..\mcal_src\Uart.c	  2981  
; ..\mcal_src\Uart.c	  2982  }
	ret
.L579:
	
__Uart_lHwEnableAscLinErrIntr_function_end:
	.size	Uart_lHwEnableAscLinErrIntr,__Uart_lHwEnableAscLinErrIntr_function_end-Uart_lHwEnableAscLinErrIntr
.L244:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_1')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_1
.L151:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	addsc.a	a15,a15,d15,#2
	ld.w	d15,[a15]
.L761:
	insert	d15,d15,#0,#31,#1
	movh	d0,#20992
.L762:
	or	d15,d0
	st.w	[a15],d15
.L1069:
	ld.w	d15,[a15]
.L763:
	insert	d15,d15,#0,#31,#1
.L766:
	insert	d15,d15,#1,#10,#1
	st.w	[a15],d15
.L760:
	fret
.L264:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lHwEnableAscLinRxIntr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  2983  
; ..\mcal_src\Uart.c	  2984  
; ..\mcal_src\Uart.c	  2985  /*******************************************************************************
; ..\mcal_src\Uart.c	  2986  ** Syntax           : static void Uart_lHwEnableAscLinRxIntr                  **
; ..\mcal_src\Uart.c	  2987  **                      (uint8 HwUnit)                                        **
; ..\mcal_src\Uart.c	  2988  **                                                                            **
; ..\mcal_src\Uart.c	  2989  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	  2990  **                                                                            **
; ..\mcal_src\Uart.c	  2991  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	  2992  **                                                                            **
; ..\mcal_src\Uart.c	  2993  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	  2994  **                                                                            **
; ..\mcal_src\Uart.c	  2995  ** Parameters (in)  : HwUnit   : ASCLIN Hardware module no                    **
; ..\mcal_src\Uart.c	  2996  **                                                                            **
; ..\mcal_src\Uart.c	  2997  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	  2998  **                                                                            **
; ..\mcal_src\Uart.c	  2999  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	  3000  **                                                                            **
; ..\mcal_src\Uart.c	  3001  ** Description      : This function clears and enables the  RX                **
; ..\mcal_src\Uart.c	  3002  **                    interrupts in SRC registers.                            **
; ..\mcal_src\Uart.c	  3003  *******************************************************************************/
; ..\mcal_src\Uart.c	  3004  static void Uart_lHwEnableAscLinRxIntr(volatile uint8 HwUnit)
; Function Uart_lHwEnableAscLinRxIntr
.L153:
Uart_lHwEnableAscLinRxIntr:	.type	func

; ..\mcal_src\Uart.c	  3005  {
; ..\mcal_src\Uart.c	  3006    uint32 Offset;
; ..\mcal_src\Uart.c	  3007  
; ..\mcal_src\Uart.c	  3008    Offset = (uint32)HwUnit * UART_SRC_ADDROFFSET ;
	fcall	.cocofun_16
.L576:

; ..\mcal_src\Uart.c	  3009  
; ..\mcal_src\Uart.c	  3010    /*Reserve bit access is ensured for SRC_ASCLIN0RX register*/
; ..\mcal_src\Uart.c	  3011    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  3012    UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  3013    /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  3014    UART_SRC_ASCLINxRXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  3015    UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)(void *)\ 
	fcall	.cocofun_1
.L577:

; ..\mcal_src\Uart.c	  3016                   (UART_SRC_ASCLIN0RXADDR + Offset)), ~UART_CLR_MASK_CLR_INTR,\ 
; ..\mcal_src\Uart.c	  3017                                                       UART_SET_MASK_CLR_INTR)
; ..\mcal_src\Uart.c	  3018  
; ..\mcal_src\Uart.c	  3019  
; ..\mcal_src\Uart.c	  3020    /*Reserve bit access is ensured for SRC_ASCLIN0RX register*/
; ..\mcal_src\Uart.c	  3021    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  3022    UART_SRC_ASCLIN0RXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  3023    /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  3024    UART_SRC_ASCLINxRXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  3025    UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)(void *)\ 
; ..\mcal_src\Uart.c	  3026                (UART_SRC_ASCLIN0RXADDR + Offset)), ~UART_CLR_MASK_ENABLE_INTR,\ 
; ..\mcal_src\Uart.c	  3027                                                       UART_SET_MASK_ENABLE_INTR)
; ..\mcal_src\Uart.c	  3028  
; ..\mcal_src\Uart.c	  3029  }
	ret
.L572:
	
__Uart_lHwEnableAscLinRxIntr_function_end:
	.size	Uart_lHwEnableAscLinRxIntr,__Uart_lHwEnableAscLinRxIntr_function_end-Uart_lHwEnableAscLinRxIntr
.L239:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Uart_lHwEnableAscLinTxIntr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	

; ..\mcal_src\Uart.c	  3030  /*******************************************************************************
; ..\mcal_src\Uart.c	  3031  ** Syntax           : static void Uart_lHwEnableAscLinTxIntr                  **
; ..\mcal_src\Uart.c	  3032  **                      (uint8 HwUnit)                                        **
; ..\mcal_src\Uart.c	  3033  **                                                                            **
; ..\mcal_src\Uart.c	  3034  ** Service ID       : NA                                                      **
; ..\mcal_src\Uart.c	  3035  **                                                                            **
; ..\mcal_src\Uart.c	  3036  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Uart.c	  3037  **                                                                            **
; ..\mcal_src\Uart.c	  3038  ** Reentrancy       : NA                                                      **
; ..\mcal_src\Uart.c	  3039  **                                                                            **
; ..\mcal_src\Uart.c	  3040  ** Parameters (in)  : HwUnit   : ASCLIN Hardware module no                    **
; ..\mcal_src\Uart.c	  3041  **                                                                            **
; ..\mcal_src\Uart.c	  3042  ** Parameters (out) : None                                                    **
; ..\mcal_src\Uart.c	  3043  **                                                                            **
; ..\mcal_src\Uart.c	  3044  ** Return value     : None                                                    **
; ..\mcal_src\Uart.c	  3045  **                                                                            **
; ..\mcal_src\Uart.c	  3046  ** Description      : This function clears and enables the  TX                **
; ..\mcal_src\Uart.c	  3047  **                    interrupts in SRC registers.                            **
; ..\mcal_src\Uart.c	  3048  *******************************************************************************/
; ..\mcal_src\Uart.c	  3049  static void Uart_lHwEnableAscLinTxIntr(volatile uint8 HwUnit)
; Function Uart_lHwEnableAscLinTxIntr
.L155:
Uart_lHwEnableAscLinTxIntr:	.type	func

; ..\mcal_src\Uart.c	  3050  {
; ..\mcal_src\Uart.c	  3051    uint32 Offset;
; ..\mcal_src\Uart.c	  3052  
; ..\mcal_src\Uart.c	  3053    Offset = (uint32)HwUnit * UART_SRC_ADDROFFSET ;
	fcall	.cocofun_17
.L569:

; ..\mcal_src\Uart.c	  3054  
; ..\mcal_src\Uart.c	  3055    /*Reserve bit access is ensured for SRC_ASCLIN0TX register*/
; ..\mcal_src\Uart.c	  3056    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  3057     UART_SRC_ASCLIN0TXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  3058    /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  3059      UART_SRC_ASCLINxTXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  3060    UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)(void *)\ 
	fcall	.cocofun_1
.L570:

; ..\mcal_src\Uart.c	  3061                 (UART_SRC_ASCLIN0TXADDR + Offset)), ~UART_CLR_MASK_CLR_INTR,\ 
; ..\mcal_src\Uart.c	  3062                                                          UART_SET_MASK_CLR_INTR)
; ..\mcal_src\Uart.c	  3063  
; ..\mcal_src\Uart.c	  3064    /*Reserve bit access is ensured for SRC_ASCLIN0TX register*/
; ..\mcal_src\Uart.c	  3065    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is done on
; ..\mcal_src\Uart.c	  3066     UART_SRC_ASCLIN0TXADDR to access the SRC register for HW Unit passed*/
; ..\mcal_src\Uart.c	  3067    /*IFX_MISRA_RULE_11_05_STATUS=Attempts to cast away volatile from
; ..\mcal_src\Uart.c	  3068     UART_SRC_ASCLINxTXADDR to cast it for SFR access*/
; ..\mcal_src\Uart.c	  3069    UART_SFR_RUNTIME_MODIFY32(*((volatile uint32 *)(void *)\ 
; ..\mcal_src\Uart.c	  3070                (UART_SRC_ASCLIN0TXADDR + Offset)), ~UART_CLR_MASK_ENABLE_INTR,\ 
; ..\mcal_src\Uart.c	  3071                                                      UART_SET_MASK_ENABLE_INTR)
; ..\mcal_src\Uart.c	  3072  }
	ret
.L565:
	
__Uart_lHwEnableAscLinTxIntr_function_end:
	.size	Uart_lHwEnableAscLinTxIntr,__Uart_lHwEnableAscLinTxIntr_function_end-Uart_lHwEnableAscLinTxIntr
.L234:
	; End of function
	
	.sdecl	'.bss.CPU0.Private.DEFAULT_RAM_32BIT',data,cluster('Uart_kConfigPtr')
	.sect	'.bss.CPU0.Private.DEFAULT_RAM_32BIT'
	.align	4
Uart_kConfigPtr:	.type	object
	.size	Uart_kConfigPtr,4
	.space	4
	.sdecl	'.data.CPU0.Private.DEFAULT_RAM_INIT_8BIT',data,cluster('Uart_BusChannelMap')
	.sect	'.data.CPU0.Private.DEFAULT_RAM_INIT_8BIT'
Uart_BusChannelMap:	.type	object
	.size	Uart_BusChannelMap,2
	.byte	255
	.space	1
	.sdecl	'.bss.CPU0.Private.DEFAULT_RAM_UNSPECIFIED',data,cluster('Uart_ChannelInfo')
	.sect	'.bss.CPU0.Private.DEFAULT_RAM_UNSPECIFIED'
	.align	4
Uart_ChannelInfo:	.type	object
	.size	Uart_ChannelInfo,20
	.space	20
	.sdecl	'.bss.CPU0.Private.DEFAULT_RAM_32BIT',data,cluster('Uart_ChLock')
	.sect	'.bss.CPU0.Private.DEFAULT_RAM_32BIT'
	.align	4
Uart_ChLock:	.type	object
	.size	Uart_ChLock,8
	.space	8
	.calls	'Uart_Init','Mcal_ResetENDINIT'
	.calls	'Uart_Init','Mcal_SetENDINIT'
	.calls	'Uart_Init','Uart_lHwInit'
	.calls	'Uart_Read','Mcal_LockResource'
	.calls	'Uart_Read','Uart_lEnableReadInterrupts'
	.calls	'Uart_Read','Uart_lHwEnableAscLinRxIntr'
	.calls	'Uart_Read','Uart_lHwEnableAscLinErrIntr'
	.calls	'Uart_Write','Mcal_LockResource'
	.calls	'Uart_Write','Uart_lWrite'
	.calls	'Uart_Write','Uart_lEnableWriteInterrupts'
	.calls	'Uart_Write','Uart_lHwEnableAscLinTxIntr'
	.calls	'Uart_Write','Uart_lHwEnableAscLinErrIntr'
	.calls	'Uart_IsrReceive','Uart_lRead'
	.calls	'Uart_IsrReceive','Uart_lClearReadInterrupts'
	.calls	'Uart_IsrReceive','Uart_lHwDisableAscLinRxIntr'
	.calls	'Uart_IsrReceive','Mcal_UnlockResource'
	.calls	'Uart_IsrReceive','__INDIRECT__'
	.calls	'Uart_IsrTransmit','Uart_lClearWriteInterrupts'
	.calls	'Uart_IsrTransmit','Uart_lHwDisableAscLinTxIntr'
	.calls	'Uart_IsrTransmit','Uart_lWrite'
	.calls	'Uart_IsrTransmit','Uart_lEnableWriteInterrupts'
	.calls	'Uart_IsrError','Uart_lHwDisableAscLinErrIntr'
	.calls	'Uart_IsrError','Mcal_UnlockResource'
	.calls	'Uart_IsrError','__INDIRECT__'
	.calls	'Uart_Init','.cocofun_13'
	.calls	'Uart_Init','.cocofun_14'
	.calls	'Uart_lHwInit','.cocofun_7'
	.calls	'Uart_Read','.cocofun_13'
	.calls	'Uart_lRead','.cocofun_4'
	.calls	'.cocofun_4','.cocofun_13'
	.calls	'Uart_lEnableReadInterrupts','.cocofun_5'
	.calls	'Uart_Write','.cocofun_8'
	.calls	'Uart_Write','.cocofun_9'
	.calls	'Uart_Write','.cocofun_10'
	.calls	'Uart_Write','.cocofun_6'
	.calls	'Uart_Write','.cocofun_11'
	.calls	'.cocofun_10','.cocofun_13'
	.calls	'Uart_lWrite','.cocofun_4'
	.calls	'Uart_lEnableWriteInterrupts','.cocofun_5'
	.calls	'Uart_GetStatus','.cocofun_4'
	.calls	'Uart_IsrReceive','.cocofun_3'
	.calls	'Uart_IsrReceive','.cocofun_9'
	.calls	'Uart_IsrReceive','.cocofun_12'
	.calls	'Uart_IsrReceive','.cocofun_8'
	.calls	'.cocofun_3','.cocofun_14'
	.calls	'Uart_IsrTransmit','.cocofun_3'
	.calls	'Uart_IsrTransmit','.cocofun_12'
	.calls	'Uart_IsrTransmit','.cocofun_6'
	.calls	'Uart_IsrTransmit','.cocofun_11'
	.calls	'Uart_IsrError','.cocofun_14'
	.calls	'Uart_IsrError','.cocofun_9'
	.calls	'Uart_IsrError','.cocofun_6'
	.calls	'Uart_IsrError','.cocofun_8'
	.calls	'Uart_IsrError','.cocofun_10'
	.calls	'Uart_lHwDisableAscLinErrIntr','.cocofun_15'
	.calls	'Uart_lHwDisableAscLinErrIntr','.cocofun_2'
	.calls	'.cocofun_15','.cocofun_18'
	.calls	'Uart_lHwDisableAscLinRxIntr','.cocofun_16'
	.calls	'Uart_lHwDisableAscLinRxIntr','.cocofun_2'
	.calls	'.cocofun_16','.cocofun_18'
	.calls	'Uart_lHwDisableAscLinTxIntr','.cocofun_17'
	.calls	'Uart_lHwDisableAscLinTxIntr','.cocofun_2'
	.calls	'.cocofun_17','.cocofun_18'
	.calls	'Uart_lHwEnableAscLinErrIntr','.cocofun_15'
	.calls	'Uart_lHwEnableAscLinErrIntr','.cocofun_1'
	.calls	'Uart_lHwEnableAscLinRxIntr','.cocofun_16'
	.calls	'Uart_lHwEnableAscLinRxIntr','.cocofun_1'
	.calls	'Uart_lHwEnableAscLinTxIntr','.cocofun_17'
	.calls	'Uart_lHwEnableAscLinTxIntr','.cocofun_1'
	.calls	'Uart_Init','',8
	.calls	'.cocofun_14','',0
	.calls	'.cocofun_13','',0
	.calls	'Uart_lHwInit','',0
	.calls	'.cocofun_7','',0
	.calls	'Uart_Read','',0
	.calls	'Uart_lRead','',0
	.calls	'.cocofun_4','',0
	.calls	'Uart_lEnableReadInterrupts','',0
	.calls	'.cocofun_5','',0
	.calls	'Uart_lClearReadInterrupts','',0
	.calls	'Uart_Write','',0
	.calls	'.cocofun_11','',0
	.calls	'.cocofun_10','',0
	.calls	'.cocofun_9','',0
	.calls	'.cocofun_8','',0
	.calls	'.cocofun_6','',0
	.calls	'Uart_lWrite','',0
	.calls	'Uart_lEnableWriteInterrupts','',0
	.calls	'Uart_lClearWriteInterrupts','',0
	.calls	'Uart_GetStatus','',0
	.calls	'Uart_IsrReceive','',0
	.calls	'.cocofun_12','',0
	.calls	'.cocofun_3','',0
	.calls	'Uart_IsrTransmit','',0
	.calls	'Uart_IsrError','',0
	.calls	'Uart_lHwDisableAscLinErrIntr','',0
	.calls	'.cocofun_15','',0
	.calls	'.cocofun_18','',0
	.calls	'.cocofun_2','',0
	.calls	'Uart_lHwDisableAscLinRxIntr','',0
	.calls	'.cocofun_16','',0
	.calls	'Uart_lHwDisableAscLinTxIntr','',0
	.calls	'.cocofun_17','',0
	.calls	'Uart_lHwEnableAscLinErrIntr','',0
	.calls	'.cocofun_1','',0
	.calls	'Uart_lHwEnableAscLinRxIntr','',0
	.extern	Mcal_LockResource
	.extern	Mcal_UnlockResource
	.extern	Mcal_ResetENDINIT
	.extern	Mcal_SetENDINIT
	.extern	__INDIRECT__
	.calls	'Uart_lHwEnableAscLinTxIntr','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L157:
	.word	15223
	.half	3
	.word	.L158
	.byte	4
.L156:
	.byte	1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L159
.L364:
	.byte	2
	.byte	'unsigned char',0,1,8
.L369:
	.byte	3
	.byte	'Uart_lHwInitClcReg',0,3,1,147,5,24
	.word	173
	.byte	1,1
.L372:
	.byte	4
	.byte	'HwUnit',0,1,147,5,49
	.word	173
.L393:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L374:
	.byte	4
	.byte	'Value',0,1,147,5,64
	.word	237
.L376:
	.byte	5,0,6
	.byte	'Mcal_LockResource',0,2,235,2,15
	.word	237
	.byte	1,1,1,1,7
	.word	237
	.byte	4
	.byte	'ResourcePtr',0,2,235,2,41
	.word	306
	.byte	0,8
	.byte	'Mcal_UnlockResource',0,2,254,2,14,1,1,1,1,4
	.byte	'ResourcePtr',0,2,254,2,42
	.word	306
	.byte	0,9
	.byte	'Mcal_ResetENDINIT',0,3,119,13,1,1,1,1,9
	.byte	'Mcal_SetENDINIT',0,3,146,1,13,1,1,1,1,10
	.byte	'Uart_ConfigType',0,4,189,4,16,8,10
	.byte	'Uart_ChannelType',0,4,155,4,16,32,10
	.byte	'UartNotifType',0,4,142,4,16,16,11,1,1
.L453:
	.byte	12
	.byte	'Uart_ErrorIdType',0,4,253,3,14,1,13
	.byte	'UART_NO_ERR',0,0,13
	.byte	'UART_PARITY_ERR',0,1,13
	.byte	'UART_FRAME_ERR',0,2,13
	.byte	'UART_TXOVERFLOW_ERR',0,3,13
	.byte	'UART_RXOVERFLOW_ERR',0,4,13
	.byte	'UART_RXUNDERFLOW_ERR',0,5,0,14
	.word	503
	.byte	0,7
	.word	500
	.byte	15
	.byte	'Uart_NotificationPtrType',0,4,135,4,15
	.word	649
	.byte	16
	.byte	'UartTransmitNotifPtr',0,4
	.word	654
	.byte	2,35,0,16
	.byte	'UartReceiveNotifPtr',0,4
	.word	654
	.byte	2,35,4,16
	.byte	'UartAbortTransmitNotifPtr',0,4
	.word	654
	.byte	2,35,8,16
	.byte	'UartAbortReceiveNotifPtr',0,4
	.word	654
	.byte	2,35,12,0,16
	.byte	'UartNotif',0,16
	.word	480
	.byte	2,35,0
.L386:
	.byte	2
	.byte	'unsigned short int',0,2,7,16
	.byte	'HwBrgNumerator',0,2
	.word	836
	.byte	2,35,16,16
	.byte	'HwBrgDenominator',0,2
	.word	836
	.byte	2,35,18,16
	.byte	'HwBitconPrescalar',0,2
	.word	836
	.byte	2,35,20,16
	.byte	'HwBitconOversampling',0,1
	.word	173
	.byte	2,35,22,16
	.byte	'HwModule',0,1
	.word	173
	.byte	2,35,23,16
	.byte	'StopBits',0,1
	.word	173
	.byte	2,35,24,16
	.byte	'DataLength',0,1
	.word	173
	.byte	2,35,25,16
	.byte	'RxPinSelection',0,1
	.word	173
	.byte	2,35,26,16
	.byte	'ParityEnable',0,1
	.word	173
	.byte	2,35,27,16
	.byte	'Parity',0,1
	.word	173
	.byte	2,35,28,16
	.byte	'CtsEnable',0,1
	.word	173
	.byte	2,35,29,16
	.byte	'CtsPolarity',0,1
	.word	173
	.byte	2,35,30,0,17
	.word	457
.L362:
	.byte	7
	.word	1124
	.byte	16
	.byte	'ChannelConfigPtr',0,4
	.word	1129
	.byte	2,35,0,16
	.byte	'NoOfChannels',0,1
	.word	173
	.byte	2,35,4,0,17
	.word	435
.L359:
	.byte	7
	.word	1183
	.byte	7
	.word	500
.L381:
	.byte	12
	.byte	'Uart_ReturnType',0,4,203,4,14,1,13
	.byte	'UART_OK',0,0,13
	.byte	'UART_NOT_OK',0,1,13
	.byte	'UART_IS_BUSY',0,2,0,7
	.word	173
	.byte	7
	.word	173
.L384:
	.byte	15
	.byte	'Uart_MemPtrType',0,4,231,3,16
	.word	1265
	.byte	10
	.byte	'_Ifx_ASCLIN',0,5,153,5,25,128,2,18,5,207,3,9,4,2
	.byte	'unsigned int',0,4,7,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,2
	.byte	'int',0,4,5,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,5,118,16,4,19
	.byte	'DISR',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'DISS',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'reserved_2',0,1
	.word	173
	.byte	1,5,2,35,0,19
	.byte	'EDIS',0,1
	.word	173
	.byte	1,4,2,35,0,19
	.byte	'reserved_4',0,4
	.word	1320
	.byte	28,0,2,35,2,0,16
	.byte	'B',0,4
	.word	1365
	.byte	2,35,0,0,16
	.byte	'CLC',0,4
	.word	1314
	.byte	2,35,0,18,5,151,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,5,169,2,16,4,19
	.byte	'ALTI',0,1
	.word	173
	.byte	3,5,2,35,0,19
	.byte	'reserved_3',0,1
	.word	173
	.byte	1,4,2,35,0,19
	.byte	'DEPTH',0,2
	.word	836
	.byte	6,6,2,35,0,19
	.byte	'reserved_10',0,1
	.word	173
	.byte	6,0,2,35,1,19
	.byte	'CTS',0,1
	.word	173
	.byte	2,6,2,35,2,19
	.byte	'reserved_18',0,2
	.word	836
	.byte	7,7,2,35,2,19
	.byte	'RCPOL',0,1
	.word	173
	.byte	1,6,2,35,3,19
	.byte	'CPOL',0,1
	.word	173
	.byte	1,5,2,35,3,19
	.byte	'SPOL',0,1
	.word	173
	.byte	1,4,2,35,3,19
	.byte	'LB',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'CTSEN',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'RXM',0,1
	.word	173
	.byte	1,1,2,35,3,19
	.byte	'TXM',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	1537
	.byte	2,35,0,0,16
	.byte	'IOCR',0,4
	.word	1509
	.byte	2,35,4,18,5,143,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,5,161,2,16,4,19
	.byte	'MODREV',0,1
	.word	173
	.byte	8,0,2,35,0,19
	.byte	'MODTYPE',0,1
	.word	173
	.byte	8,0,2,35,1,19
	.byte	'MODNUMBER',0,2
	.word	836
	.byte	16,0,2,35,2,0,16
	.byte	'B',0,4
	.word	1846
	.byte	2,35,0,0,16
	.byte	'ID',0,4
	.word	1818
	.byte	2,35,8,18,5,247,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,5,149,3,16,4,19
	.byte	'FLUSH',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'ENO',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'reserved_2',0,1
	.word	173
	.byte	4,2,2,35,0,19
	.byte	'INW',0,1
	.word	173
	.byte	2,0,2,35,0,19
	.byte	'INTLEVEL',0,1
	.word	173
	.byte	4,4,2,35,1,19
	.byte	'reserved_12',0,1
	.word	173
	.byte	4,0,2,35,1,19
	.byte	'FILL',0,1
	.word	173
	.byte	5,3,2,35,2,19
	.byte	'reserved_21',0,2
	.word	836
	.byte	11,0,2,35,2,0,16
	.byte	'B',0,4
	.word	1983
	.byte	2,35,0,0,16
	.byte	'TXFIFOCON',0,4
	.word	1955
	.byte	2,35,12,18,5,231,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,5,129,3,16,4,19
	.byte	'FLUSH',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'ENI',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'reserved_2',0,1
	.word	173
	.byte	4,2,2,35,0,19
	.byte	'OUTW',0,1
	.word	173
	.byte	2,0,2,35,0,19
	.byte	'INTLEVEL',0,1
	.word	173
	.byte	4,4,2,35,1,19
	.byte	'reserved_12',0,1
	.word	173
	.byte	4,0,2,35,1,19
	.byte	'FILL',0,1
	.word	173
	.byte	5,3,2,35,2,19
	.byte	'reserved_21',0,2
	.word	836
	.byte	10,1,2,35,2,19
	.byte	'BUF',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	2227
	.byte	2,35,0,0,16
	.byte	'RXFIFOCON',0,4
	.word	2199
	.byte	2,35,16,18,5,183,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,5,88,16,4,19
	.byte	'PRESCALER',0,2
	.word	836
	.byte	12,4,2,35,0,19
	.byte	'reserved_12',0,1
	.word	173
	.byte	4,0,2,35,1,19
	.byte	'OVERSAMPLING',0,1
	.word	173
	.byte	4,4,2,35,2,19
	.byte	'reserved_20',0,1
	.word	173
	.byte	4,0,2,35,2,19
	.byte	'SAMPLEPOINT',0,1
	.word	173
	.byte	4,4,2,35,3,19
	.byte	'reserved_28',0,1
	.word	173
	.byte	3,1,2,35,3,19
	.byte	'SM',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	2487
	.byte	2,35,0,0,16
	.byte	'BITCON',0,4
	.word	2459
	.byte	2,35,20,18,5,135,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,5,145,2,16,4,19
	.byte	'reserved_0',0,1
	.word	173
	.byte	6,2,2,35,0,19
	.byte	'IDLE',0,2
	.word	836
	.byte	3,7,2,35,0,19
	.byte	'STOP',0,1
	.word	173
	.byte	3,4,2,35,1,19
	.byte	'LEAD',0,1
	.word	173
	.byte	3,1,2,35,1,19
	.byte	'reserved_15',0,1
	.word	173
	.byte	1,0,2,35,1,19
	.byte	'MODE',0,1
	.word	173
	.byte	2,6,2,35,2,19
	.byte	'reserved_18',0,2
	.word	836
	.byte	10,4,2,35,2,19
	.byte	'MSB',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'CEN',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'PEN',0,1
	.word	173
	.byte	1,1,2,35,3,19
	.byte	'ODD',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	2724
	.byte	2,35,0,0,16
	.byte	'FRAMECON',0,4
	.word	2696
	.byte	2,35,24,18,5,223,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,5,136,1,16,4,19
	.byte	'DATLEN',0,1
	.word	173
	.byte	4,4,2,35,0,19
	.byte	'reserved_4',0,2
	.word	836
	.byte	9,3,2,35,0,19
	.byte	'HO',0,1
	.word	173
	.byte	1,2,2,35,1,19
	.byte	'RM',0,1
	.word	173
	.byte	1,1,2,35,1,19
	.byte	'CSM',0,1
	.word	173
	.byte	1,0,2,35,1,19
	.byte	'RESPONSE',0,1
	.word	173
	.byte	8,0,2,35,2,19
	.byte	'reserved_24',0,1
	.word	173
	.byte	8,0,2,35,3,0,16
	.byte	'B',0,4
	.word	3007
	.byte	2,35,0,0,16
	.byte	'DATCON',0,4
	.word	2979
	.byte	2,35,28,18,5,199,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,5,109,16,4,19
	.byte	'DENOMINATOR',0,2
	.word	836
	.byte	12,4,2,35,0,19
	.byte	'reserved_12',0,1
	.word	173
	.byte	4,0,2,35,1,19
	.byte	'NUMERATOR',0,2
	.word	836
	.byte	12,4,2,35,2,19
	.byte	'reserved_28',0,1
	.word	173
	.byte	4,0,2,35,3,0,16
	.byte	'B',0,4
	.word	3220
	.byte	2,35,0,0,16
	.byte	'BRG',0,4
	.word	3192
	.byte	2,35,32,18,5,191,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,5,100,16,4,19
	.byte	'LOWERLIMIT',0,1
	.word	173
	.byte	8,0,2,35,0,19
	.byte	'UPPERLIMIT',0,1
	.word	173
	.byte	8,0,2,35,1,19
	.byte	'MEASURED',0,2
	.word	836
	.byte	12,4,2,35,2,19
	.byte	'reserved_28',0,1
	.word	173
	.byte	4,0,2,35,3,0,16
	.byte	'B',0,4
	.word	3390
	.byte	2,35,0,0,16
	.byte	'BRD',0,4
	.word	3362
	.byte	2,35,36,10
	.byte	'_Ifx_ASCLIN_LIN',0,5,135,5,25,12,18,5,191,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,5,216,2,16,4,19
	.byte	'reserved_0',0,4
	.word	1320
	.byte	23,9,2,35,2,19
	.byte	'CSI',0,1
	.word	173
	.byte	1,0,2,35,2,19
	.byte	'reserved_24',0,1
	.word	173
	.byte	1,7,2,35,3,19
	.byte	'CSEN',0,1
	.word	173
	.byte	1,6,2,35,3,19
	.byte	'MS',0,1
	.word	173
	.byte	1,5,2,35,3,19
	.byte	'ABD',0,1
	.word	173
	.byte	1,4,2,35,3,19
	.byte	'reserved_28',0,1
	.word	173
	.byte	4,0,2,35,3,0,16
	.byte	'B',0,4
	.word	3579
	.byte	2,35,0,0,16
	.byte	'CON',0,4
	.word	3551
	.byte	2,35,0,18,5,183,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,5,209,2,16,4,19
	.byte	'BREAK',0,1
	.word	173
	.byte	6,2,2,35,0,19
	.byte	'reserved_6',0,4
	.word	1320
	.byte	26,0,2,35,2,0,16
	.byte	'B',0,4
	.word	3792
	.byte	2,35,0,0,16
	.byte	'BTIMER',0,4
	.word	3764
	.byte	2,35,4,18,5,199,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,5,228,2,16,4,19
	.byte	'HEADER',0,1
	.word	173
	.byte	8,0,2,35,0,19
	.byte	'reserved_8',0,4
	.word	1320
	.byte	24,0,2,35,2,0,16
	.byte	'B',0,4
	.word	3922
	.byte	2,35,0,0,16
	.byte	'HTIMER',0,4
	.word	3894
	.byte	2,35,8,0,20
	.word	3529
	.byte	16
	.byte	'LIN',0,12
	.word	4026
	.byte	2,35,40,18,5,231,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,5,148,1,16,4,19
	.byte	'TH',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'TR',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'RH',0,1
	.word	173
	.byte	1,5,2,35,0,19
	.byte	'RR',0,1
	.word	173
	.byte	1,4,2,35,0,19
	.byte	'reserved_4',0,1
	.word	173
	.byte	1,3,2,35,0,19
	.byte	'FED',0,1
	.word	173
	.byte	1,2,2,35,0,19
	.byte	'RED',0,1
	.word	173
	.byte	1,1,2,35,0,19
	.byte	'reserved_7',0,2
	.word	836
	.byte	6,3,2,35,0,19
	.byte	'TWRQ',0,1
	.word	173
	.byte	1,2,2,35,1,19
	.byte	'THRQ',0,1
	.word	173
	.byte	1,1,2,35,1,19
	.byte	'TRRQ',0,1
	.word	173
	.byte	1,0,2,35,1,19
	.byte	'PE',0,1
	.word	173
	.byte	1,7,2,35,2,19
	.byte	'TC',0,1
	.word	173
	.byte	1,6,2,35,2,19
	.byte	'FE',0,1
	.word	173
	.byte	1,5,2,35,2,19
	.byte	'HT',0,1
	.word	173
	.byte	1,4,2,35,2,19
	.byte	'RT',0,1
	.word	173
	.byte	1,3,2,35,2,19
	.byte	'BD',0,1
	.word	173
	.byte	1,2,2,35,2,19
	.byte	'LP',0,1
	.word	173
	.byte	1,1,2,35,2,19
	.byte	'LA',0,1
	.word	173
	.byte	1,0,2,35,2,19
	.byte	'LC',0,1
	.word	173
	.byte	1,7,2,35,3,19
	.byte	'CE',0,1
	.word	173
	.byte	1,6,2,35,3,19
	.byte	'RFO',0,1
	.word	173
	.byte	1,5,2,35,3,19
	.byte	'RFU',0,1
	.word	173
	.byte	1,4,2,35,3,19
	.byte	'RFL',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'reserved_29',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'TFO',0,1
	.word	173
	.byte	1,1,2,35,3,19
	.byte	'TFL',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	4072
	.byte	2,35,0,0,16
	.byte	'FLAGS',0,4
	.word	4044
	.byte	2,35,52,18,5,255,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,5,241,1,16,4,19
	.byte	'THS',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'TRS',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'RHS',0,1
	.word	173
	.byte	1,5,2,35,0,19
	.byte	'RRS',0,1
	.word	173
	.byte	1,4,2,35,0,19
	.byte	'reserved_4',0,1
	.word	173
	.byte	1,3,2,35,0,19
	.byte	'FEDS',0,1
	.word	173
	.byte	1,2,2,35,0,19
	.byte	'REDS',0,1
	.word	173
	.byte	1,1,2,35,0,19
	.byte	'reserved_7',0,2
	.word	836
	.byte	6,3,2,35,0,19
	.byte	'TWRQS',0,1
	.word	173
	.byte	1,2,2,35,1,19
	.byte	'THRQS',0,1
	.word	173
	.byte	1,1,2,35,1,19
	.byte	'TRRQS',0,1
	.word	173
	.byte	1,0,2,35,1,19
	.byte	'PES',0,1
	.word	173
	.byte	1,7,2,35,2,19
	.byte	'TCS',0,1
	.word	173
	.byte	1,6,2,35,2,19
	.byte	'FES',0,1
	.word	173
	.byte	1,5,2,35,2,19
	.byte	'HTS',0,1
	.word	173
	.byte	1,4,2,35,2,19
	.byte	'RTS',0,1
	.word	173
	.byte	1,3,2,35,2,19
	.byte	'BDS',0,1
	.word	173
	.byte	1,2,2,35,2,19
	.byte	'LPS',0,1
	.word	173
	.byte	1,1,2,35,2,19
	.byte	'LAS',0,1
	.word	173
	.byte	1,0,2,35,2,19
	.byte	'LCS',0,1
	.word	173
	.byte	1,7,2,35,3,19
	.byte	'CES',0,1
	.word	173
	.byte	1,6,2,35,3,19
	.byte	'RFOS',0,1
	.word	173
	.byte	1,5,2,35,3,19
	.byte	'RFUS',0,1
	.word	173
	.byte	1,4,2,35,3,19
	.byte	'RFLS',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'reserved_29',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'TFOS',0,1
	.word	173
	.byte	1,1,2,35,3,19
	.byte	'TFLS',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	4573
	.byte	2,35,0,0,16
	.byte	'FLAGSSET',0,4
	.word	4545
	.byte	2,35,56,18,5,239,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,5,180,1,16,4,19
	.byte	'THC',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'TRC',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'RHC',0,1
	.word	173
	.byte	1,5,2,35,0,19
	.byte	'RRC',0,1
	.word	173
	.byte	1,4,2,35,0,19
	.byte	'reserved_4',0,1
	.word	173
	.byte	1,3,2,35,0,19
	.byte	'FEDC',0,1
	.word	173
	.byte	1,2,2,35,0,19
	.byte	'REDC',0,1
	.word	173
	.byte	1,1,2,35,0,19
	.byte	'reserved_7',0,2
	.word	836
	.byte	6,3,2,35,0,19
	.byte	'TWRQC',0,1
	.word	173
	.byte	1,2,2,35,1,19
	.byte	'THRQC',0,1
	.word	173
	.byte	1,1,2,35,1,19
	.byte	'TRRQC',0,1
	.word	173
	.byte	1,0,2,35,1,19
	.byte	'PEC',0,1
	.word	173
	.byte	1,7,2,35,2,19
	.byte	'TCC',0,1
	.word	173
	.byte	1,6,2,35,2,19
	.byte	'FEC',0,1
	.word	173
	.byte	1,5,2,35,2,19
	.byte	'HTC',0,1
	.word	173
	.byte	1,4,2,35,2,19
	.byte	'RTC',0,1
	.word	173
	.byte	1,3,2,35,2,19
	.byte	'BDC',0,1
	.word	173
	.byte	1,2,2,35,2,19
	.byte	'LPC',0,1
	.word	173
	.byte	1,1,2,35,2,19
	.byte	'LAC',0,1
	.word	173
	.byte	1,0,2,35,2,19
	.byte	'LCC',0,1
	.word	173
	.byte	1,7,2,35,3,19
	.byte	'CEC',0,1
	.word	173
	.byte	1,6,2,35,3,19
	.byte	'RFOC',0,1
	.word	173
	.byte	1,5,2,35,3,19
	.byte	'RFUC',0,1
	.word	173
	.byte	1,4,2,35,3,19
	.byte	'RFLC',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'reserved_29',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'TFOC',0,1
	.word	173
	.byte	1,1,2,35,3,19
	.byte	'TFLC',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	5104
	.byte	2,35,0,0,16
	.byte	'FLAGSCLEAR',0,4
	.word	5076
	.byte	2,35,60,18,5,247,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,5,212,1,16,4,19
	.byte	'THE',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'TRE',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'RHE',0,1
	.word	173
	.byte	1,5,2,35,0,19
	.byte	'RRE',0,1
	.word	173
	.byte	1,4,2,35,0,19
	.byte	'reserved_4',0,1
	.word	173
	.byte	1,3,2,35,0,19
	.byte	'FEDE',0,1
	.word	173
	.byte	1,2,2,35,0,19
	.byte	'REDE',0,1
	.word	173
	.byte	1,1,2,35,0,19
	.byte	'reserved_7',0,2
	.word	836
	.byte	9,0,2,35,0,19
	.byte	'PEE',0,1
	.word	173
	.byte	1,7,2,35,2,19
	.byte	'TCE',0,1
	.word	173
	.byte	1,6,2,35,2,19
	.byte	'FEE',0,1
	.word	173
	.byte	1,5,2,35,2,19
	.byte	'HTE',0,1
	.word	173
	.byte	1,4,2,35,2,19
	.byte	'RTE',0,1
	.word	173
	.byte	1,3,2,35,2,19
	.byte	'BDE',0,1
	.word	173
	.byte	1,2,2,35,2,19
	.byte	'LPE',0,1
	.word	173
	.byte	1,1,2,35,2,19
	.byte	'ABE',0,1
	.word	173
	.byte	1,0,2,35,2,19
	.byte	'LCE',0,1
	.word	173
	.byte	1,7,2,35,3,19
	.byte	'CEE',0,1
	.word	173
	.byte	1,6,2,35,3,19
	.byte	'RFOE',0,1
	.word	173
	.byte	1,5,2,35,3,19
	.byte	'RFUE',0,1
	.word	173
	.byte	1,4,2,35,3,19
	.byte	'RFLE',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'reserved_29',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'TFOE',0,1
	.word	173
	.byte	1,1,2,35,3,19
	.byte	'TFLE',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	5639
	.byte	2,35,0,0,16
	.byte	'FLAGSENABLE',0,4
	.word	5611
	.byte	2,35,64,18,5,239,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,5,143,3,16,4,19
	.byte	'DATA',0,4
	.word	1320
	.byte	32,0,2,35,2,0,16
	.byte	'B',0,4
	.word	6125
	.byte	2,35,0,0,16
	.byte	'TXDATA',0,4
	.word	6097
	.byte	2,35,68,18,5,215,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,5,245,2,16,4,19
	.byte	'DATA',0,4
	.word	1320
	.byte	32,0,2,35,2,0,16
	.byte	'B',0,4
	.word	6228
	.byte	2,35,0,0,16
	.byte	'RXDATA',0,4
	.word	6200
	.byte	2,35,72,18,5,215,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,5,128,1,16,4,19
	.byte	'CLKSEL',0,1
	.word	173
	.byte	5,3,2,35,0,19
	.byte	'reserved_5',0,4
	.word	1320
	.byte	26,1,2,35,2,19
	.byte	'CON',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	6331
	.byte	2,35,0,0,16
	.byte	'CSR',0,4
	.word	6303
	.byte	2,35,76,18,5,223,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,5,251,2,16,4,19
	.byte	'DATA',0,4
	.word	1320
	.byte	32,0,2,35,2,0,16
	.byte	'B',0,4
	.word	6467
	.byte	2,35,0,0,16
	.byte	'RXDATAD',0,4
	.word	6439
	.byte	2,35,80,21,148,1
	.word	173
	.byte	22,147,1,0,16
	.byte	'reserved_54',0,148,1
	.word	6544
	.byte	2,35,84,18,5,207,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,5,235,2,16,4,19
	.byte	'reserved_0',0,4
	.word	1320
	.byte	24,8,2,35,2,19
	.byte	'SUS',0,1
	.word	173
	.byte	4,4,2,35,3,19
	.byte	'SUS_P',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'SUSSTA',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'reserved_30',0,1
	.word	173
	.byte	2,0,2,35,3,0,16
	.byte	'B',0,4
	.word	6605
	.byte	2,35,0,0,16
	.byte	'OCS',0,4
	.word	6577
	.byte	3,35,232,1,18,5,175,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,5,202,2,16,4,19
	.byte	'CLR',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'reserved_1',0,4
	.word	1320
	.byte	31,0,2,35,2,0,16
	.byte	'B',0,4
	.word	6782
	.byte	2,35,0,0,16
	.byte	'KRSTCLR',0,4
	.word	6754
	.byte	3,35,236,1,18,5,167,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,5,195,2,16,4,19
	.byte	'RST',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'reserved_1',0,4
	.word	1320
	.byte	31,0,2,35,2,0,16
	.byte	'B',0,4
	.word	6909
	.byte	2,35,0,0,16
	.byte	'KRST1',0,4
	.word	6881
	.byte	3,35,240,1,18,5,159,4,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,5,187,2,16,4,19
	.byte	'RST',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'RSTSTAT',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'reserved_2',0,4
	.word	1320
	.byte	30,0,2,35,2,0,16
	.byte	'B',0,4
	.word	7032
	.byte	2,35,0,0,16
	.byte	'KRST0',0,4
	.word	7004
	.byte	3,35,244,1,18,5,175,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,5,82,16,4,19
	.byte	'reserved_0',0,4
	.word	1320
	.byte	32,0,2,35,2,0,16
	.byte	'B',0,4
	.word	7174
	.byte	2,35,0,0,16
	.byte	'ACCEN1',0,4
	.word	7146
	.byte	3,35,248,1,18,5,167,3,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,5,45,16,4,19
	.byte	'EN0',0,1
	.word	173
	.byte	1,7,2,35,0,19
	.byte	'EN1',0,1
	.word	173
	.byte	1,6,2,35,0,19
	.byte	'EN2',0,1
	.word	173
	.byte	1,5,2,35,0,19
	.byte	'EN3',0,1
	.word	173
	.byte	1,4,2,35,0,19
	.byte	'EN4',0,1
	.word	173
	.byte	1,3,2,35,0,19
	.byte	'EN5',0,1
	.word	173
	.byte	1,2,2,35,0,19
	.byte	'EN6',0,1
	.word	173
	.byte	1,1,2,35,0,19
	.byte	'EN7',0,1
	.word	173
	.byte	1,0,2,35,0,19
	.byte	'EN8',0,1
	.word	173
	.byte	1,7,2,35,1,19
	.byte	'EN9',0,1
	.word	173
	.byte	1,6,2,35,1,19
	.byte	'EN10',0,1
	.word	173
	.byte	1,5,2,35,1,19
	.byte	'EN11',0,1
	.word	173
	.byte	1,4,2,35,1,19
	.byte	'EN12',0,1
	.word	173
	.byte	1,3,2,35,1,19
	.byte	'EN13',0,1
	.word	173
	.byte	1,2,2,35,1,19
	.byte	'EN14',0,1
	.word	173
	.byte	1,1,2,35,1,19
	.byte	'EN15',0,1
	.word	173
	.byte	1,0,2,35,1,19
	.byte	'EN16',0,1
	.word	173
	.byte	1,7,2,35,2,19
	.byte	'EN17',0,1
	.word	173
	.byte	1,6,2,35,2,19
	.byte	'EN18',0,1
	.word	173
	.byte	1,5,2,35,2,19
	.byte	'EN19',0,1
	.word	173
	.byte	1,4,2,35,2,19
	.byte	'EN20',0,1
	.word	173
	.byte	1,3,2,35,2,19
	.byte	'EN21',0,1
	.word	173
	.byte	1,2,2,35,2,19
	.byte	'EN22',0,1
	.word	173
	.byte	1,1,2,35,2,19
	.byte	'EN23',0,1
	.word	173
	.byte	1,0,2,35,2,19
	.byte	'EN24',0,1
	.word	173
	.byte	1,7,2,35,3,19
	.byte	'EN25',0,1
	.word	173
	.byte	1,6,2,35,3,19
	.byte	'EN26',0,1
	.word	173
	.byte	1,5,2,35,3,19
	.byte	'EN27',0,1
	.word	173
	.byte	1,4,2,35,3,19
	.byte	'EN28',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'EN29',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'EN30',0,1
	.word	173
	.byte	1,1,2,35,3,19
	.byte	'EN31',0,1
	.word	173
	.byte	1,0,2,35,3,0,16
	.byte	'B',0,4
	.word	7283
	.byte	2,35,0,0,16
	.byte	'ACCEN0',0,4
	.word	7255
	.byte	3,35,252,1,0,20
	.word	1295
.L388:
	.byte	7
	.word	7845
.L434:
	.byte	12
	.byte	'Uart_StatusType',0,4,215,4,14,1,13
	.byte	'UART_IDLE',0,0,13
	.byte	'UART_UNINIT',0,1,13
	.byte	'UART_BUSY',0,2,0,23
	.word	190
	.byte	24
	.word	221
	.byte	24
	.word	258
	.byte	5,0
.L566:
	.byte	20
	.word	173
.L573:
	.byte	20
	.word	173
.L580:
	.byte	20
	.word	173
.L588:
	.byte	20
	.word	173
.L595:
	.byte	20
	.word	173
.L602:
	.byte	20
	.word	173
	.byte	25
	.byte	'__INDIRECT__',0,1,1,1,1,1,1,26
	.byte	'void',0,7
	.word	7983
	.byte	15
	.byte	'__prof_adm',0,1,1,1
	.word	7989
	.byte	27,1,7
	.word	8013
	.byte	15
	.byte	'__codeptr',0,1,1,1
	.word	8015
	.byte	15
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,5,79,3
	.word	7283
	.byte	15
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,5,85,3
	.word	7174
	.byte	15
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,5,97,3
	.word	2487
	.byte	15
	.byte	'Ifx_ASCLIN_BRD_Bits',0,5,106,3
	.word	3390
	.byte	15
	.byte	'Ifx_ASCLIN_BRG_Bits',0,5,115,3
	.word	3220
	.byte	15
	.byte	'Ifx_ASCLIN_CLC_Bits',0,5,125,3
	.word	1365
	.byte	15
	.byte	'Ifx_ASCLIN_CSR_Bits',0,5,133,1,3
	.word	6331
	.byte	15
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,5,145,1,3
	.word	3007
	.byte	15
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,5,177,1,3
	.word	4072
	.byte	15
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,5,209,1,3
	.word	5104
	.byte	15
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,5,238,1,3
	.word	5639
	.byte	15
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,5,142,2,3
	.word	4573
	.byte	15
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,5,158,2,3
	.word	2724
	.byte	15
	.byte	'Ifx_ASCLIN_ID_Bits',0,5,166,2,3
	.word	1846
	.byte	15
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,5,184,2,3
	.word	1537
	.byte	15
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,5,192,2,3
	.word	7032
	.byte	15
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,5,199,2,3
	.word	6909
	.byte	15
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,5,206,2,3
	.word	6782
	.byte	15
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,5,213,2,3
	.word	3792
	.byte	15
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,5,225,2,3
	.word	3579
	.byte	15
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,5,232,2,3
	.word	3922
	.byte	15
	.byte	'Ifx_ASCLIN_OCS_Bits',0,5,242,2,3
	.word	6605
	.byte	15
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,5,248,2,3
	.word	6228
	.byte	15
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,5,254,2,3
	.word	6467
	.byte	15
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,5,140,3,3
	.word	2227
	.byte	15
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,5,146,3,3
	.word	6125
	.byte	15
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,5,159,3,3
	.word	1983
	.byte	15
	.byte	'Ifx_ASCLIN_ACCEN0',0,5,172,3,3
	.word	7255
	.byte	15
	.byte	'Ifx_ASCLIN_ACCEN1',0,5,180,3,3
	.word	7146
	.byte	15
	.byte	'Ifx_ASCLIN_BITCON',0,5,188,3,3
	.word	2459
	.byte	15
	.byte	'Ifx_ASCLIN_BRD',0,5,196,3,3
	.word	3362
	.byte	15
	.byte	'Ifx_ASCLIN_BRG',0,5,204,3,3
	.word	3192
	.byte	15
	.byte	'Ifx_ASCLIN_CLC',0,5,212,3,3
	.word	1314
	.byte	15
	.byte	'Ifx_ASCLIN_CSR',0,5,220,3,3
	.word	6303
	.byte	15
	.byte	'Ifx_ASCLIN_DATCON',0,5,228,3,3
	.word	2979
	.byte	15
	.byte	'Ifx_ASCLIN_FLAGS',0,5,236,3,3
	.word	4044
	.byte	15
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,5,244,3,3
	.word	5076
	.byte	15
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,5,252,3,3
	.word	5611
	.byte	15
	.byte	'Ifx_ASCLIN_FLAGSSET',0,5,132,4,3
	.word	4545
	.byte	15
	.byte	'Ifx_ASCLIN_FRAMECON',0,5,140,4,3
	.word	2696
	.byte	15
	.byte	'Ifx_ASCLIN_ID',0,5,148,4,3
	.word	1818
	.byte	15
	.byte	'Ifx_ASCLIN_IOCR',0,5,156,4,3
	.word	1509
	.byte	15
	.byte	'Ifx_ASCLIN_KRST0',0,5,164,4,3
	.word	7004
	.byte	15
	.byte	'Ifx_ASCLIN_KRST1',0,5,172,4,3
	.word	6881
	.byte	15
	.byte	'Ifx_ASCLIN_KRSTCLR',0,5,180,4,3
	.word	6754
	.byte	15
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,5,188,4,3
	.word	3764
	.byte	15
	.byte	'Ifx_ASCLIN_LIN_CON',0,5,196,4,3
	.word	3551
	.byte	15
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,5,204,4,3
	.word	3894
	.byte	15
	.byte	'Ifx_ASCLIN_OCS',0,5,212,4,3
	.word	6577
	.byte	15
	.byte	'Ifx_ASCLIN_RXDATA',0,5,220,4,3
	.word	6200
	.byte	15
	.byte	'Ifx_ASCLIN_RXDATAD',0,5,228,4,3
	.word	6439
	.byte	15
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,5,236,4,3
	.word	2199
	.byte	15
	.byte	'Ifx_ASCLIN_TXDATA',0,5,244,4,3
	.word	6097
	.byte	15
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,5,252,4,3
	.word	1955
	.byte	20
	.word	3529
	.byte	15
	.byte	'Ifx_ASCLIN_LIN',0,5,140,5,3
	.word	9637
	.byte	20
	.word	1295
	.byte	15
	.byte	'Ifx_ASCLIN',0,5,181,5,3
	.word	9666
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,6,45,16,4,19
	.byte	'SRPN',0,1
	.word	173
	.byte	8,0,2,35,0,19
	.byte	'reserved_8',0,1
	.word	173
	.byte	2,6,2,35,1,19
	.byte	'SRE',0,1
	.word	173
	.byte	1,5,2,35,1,19
	.byte	'TOS',0,1
	.word	173
	.byte	1,4,2,35,1,19
	.byte	'reserved_12',0,1
	.word	173
	.byte	4,0,2,35,1,19
	.byte	'ECC',0,1
	.word	173
	.byte	5,3,2,35,2,19
	.byte	'reserved_21',0,1
	.word	173
	.byte	3,0,2,35,2,19
	.byte	'SRR',0,1
	.word	173
	.byte	1,7,2,35,3,19
	.byte	'CLRR',0,1
	.word	173
	.byte	1,6,2,35,3,19
	.byte	'SETR',0,1
	.word	173
	.byte	1,5,2,35,3,19
	.byte	'IOV',0,1
	.word	173
	.byte	1,4,2,35,3,19
	.byte	'IOVCLR',0,1
	.word	173
	.byte	1,3,2,35,3,19
	.byte	'SWS',0,1
	.word	173
	.byte	1,2,2,35,3,19
	.byte	'SWSCLR',0,1
	.word	173
	.byte	1,1,2,35,3,19
	.byte	'reserved_31',0,1
	.word	173
	.byte	1,0,2,35,3,0,15
	.byte	'Ifx_SRC_SRCR_Bits',0,6,62,3
	.word	9691
	.byte	18,6,70,9,4,16
	.byte	'U',0,4
	.word	1320
	.byte	2,35,0,16
	.byte	'I',0,4
	.word	1347
	.byte	2,35,0,16
	.byte	'B',0,4
	.word	9691
	.byte	2,35,0,0,15
	.byte	'Ifx_SRC_SRCR',0,6,75,3
	.word	10007
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,6,86,25,12,16
	.byte	'TX',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'RX',0,4
	.word	10007
	.byte	2,35,4,16
	.byte	'ERR',0,4
	.word	10007
	.byte	2,35,8,0,20
	.word	10067
	.byte	15
	.byte	'Ifx_SRC_ASCLIN',0,6,91,3
	.word	10126
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,6,94,25,4,16
	.byte	'SBSRC',0,4
	.word	10007
	.byte	2,35,0,0,20
	.word	10154
	.byte	15
	.byte	'Ifx_SRC_BCUSPB',0,6,97,3
	.word	10191
	.byte	10
	.byte	'_Ifx_SRC_CAN',0,6,100,25,64,21,64
	.word	10007
	.byte	22,15,0,16
	.byte	'INT',0,64
	.word	10237
	.byte	2,35,0,0,20
	.word	10219
	.byte	15
	.byte	'Ifx_SRC_CAN',0,6,103,3
	.word	10260
	.byte	10
	.byte	'_Ifx_SRC_CAN1',0,6,106,25,32,21,32
	.word	10007
	.byte	22,7,0,16
	.byte	'INT',0,32
	.word	10304
	.byte	2,35,0,0,20
	.word	10285
	.byte	15
	.byte	'Ifx_SRC_CAN1',0,6,109,3
	.word	10327
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,6,112,25,16,16
	.byte	'SR0',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'SR1',0,4
	.word	10007
	.byte	2,35,4,16
	.byte	'SR2',0,4
	.word	10007
	.byte	2,35,8,16
	.byte	'SR3',0,4
	.word	10007
	.byte	2,35,12,0,20
	.word	10353
	.byte	15
	.byte	'Ifx_SRC_CCU6',0,6,118,3
	.word	10425
	.byte	10
	.byte	'_Ifx_SRC_CERBERUS',0,6,121,25,8,21,8
	.word	10007
	.byte	22,1,0,16
	.byte	'SR',0,8
	.word	10474
	.byte	2,35,0,0,20
	.word	10451
	.byte	15
	.byte	'Ifx_SRC_CERBERUS',0,6,124,3
	.word	10496
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,6,127,25,32,16
	.byte	'SBSRC',0,4
	.word	10007
	.byte	2,35,0,21,28
	.word	173
	.byte	22,27,0,16
	.byte	'reserved_4',0,28
	.word	10559
	.byte	2,35,4,0,20
	.word	10526
	.byte	15
	.byte	'Ifx_SRC_CPU',0,6,131,1,3
	.word	10589
	.byte	10
	.byte	'_Ifx_SRC_DMA',0,6,134,1,25,80,16
	.byte	'ERR',0,4
	.word	10007
	.byte	2,35,0,21,12
	.word	173
	.byte	22,11,0,16
	.byte	'reserved_4',0,12
	.word	10647
	.byte	2,35,4,16
	.byte	'CH',0,64
	.word	10237
	.byte	2,35,16,0,20
	.word	10615
	.byte	15
	.byte	'Ifx_SRC_DMA',0,6,139,1,3
	.word	10689
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,6,142,1,25,4,16
	.byte	'SR',0,4
	.word	10007
	.byte	2,35,0,0,20
	.word	10715
	.byte	15
	.byte	'Ifx_SRC_EMEM',0,6,145,1,3
	.word	10748
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,6,148,1,25,80,16
	.byte	'INT',0,8
	.word	10474
	.byte	2,35,0,16
	.byte	'TINT',0,8
	.word	10474
	.byte	2,35,8,16
	.byte	'NDAT',0,8
	.word	10474
	.byte	2,35,16,16
	.byte	'MBSC',0,8
	.word	10474
	.byte	2,35,24,16
	.byte	'OBUSY',0,4
	.word	10007
	.byte	2,35,32,16
	.byte	'IBUSY',0,4
	.word	10007
	.byte	2,35,36,21,40
	.word	173
	.byte	22,39,0,16
	.byte	'reserved_28',0,40
	.word	10880
	.byte	2,35,40,0,20
	.word	10775
	.byte	15
	.byte	'Ifx_SRC_ERAY',0,6,157,1,3
	.word	10911
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,6,160,1,25,4,16
	.byte	'SR',0,4
	.word	10007
	.byte	2,35,0,0,20
	.word	10938
	.byte	15
	.byte	'Ifx_SRC_ETH',0,6,163,1,3
	.word	10970
	.byte	10
	.byte	'_Ifx_SRC_EVR',0,6,166,1,25,8,16
	.byte	'WUT',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'SCDC',0,4
	.word	10007
	.byte	2,35,4,0,20
	.word	10996
	.byte	15
	.byte	'Ifx_SRC_EVR',0,6,170,1,3
	.word	11043
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,6,173,1,25,12,16
	.byte	'DONE',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'ERR',0,4
	.word	10007
	.byte	2,35,4,16
	.byte	'RFS',0,4
	.word	10007
	.byte	2,35,8,0,20
	.word	11069
	.byte	15
	.byte	'Ifx_SRC_FFT',0,6,178,1,3
	.word	11129
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,6,181,1,25,128,12,16
	.byte	'SR0',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'SR1',0,4
	.word	10007
	.byte	2,35,4,16
	.byte	'SR2',0,4
	.word	10007
	.byte	2,35,8,16
	.byte	'SR3',0,4
	.word	10007
	.byte	2,35,12,21,240,11
	.word	173
	.byte	22,239,11,0,16
	.byte	'reserved_10',0,240,11
	.word	11228
	.byte	2,35,16,0,20
	.word	11155
	.byte	15
	.byte	'Ifx_SRC_GPSR',0,6,188,1,3
	.word	11262
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,6,191,1,25,48,16
	.byte	'CIRQ',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'T2',0,4
	.word	10007
	.byte	2,35,4,16
	.byte	'T3',0,4
	.word	10007
	.byte	2,35,8,16
	.byte	'T4',0,4
	.word	10007
	.byte	2,35,12,16
	.byte	'T5',0,4
	.word	10007
	.byte	2,35,16,16
	.byte	'T6',0,4
	.word	10007
	.byte	2,35,20,21,24
	.word	173
	.byte	22,23,0,16
	.byte	'reserved_18',0,24
	.word	11384
	.byte	2,35,24,0,20
	.word	11289
	.byte	15
	.byte	'Ifx_SRC_GPT12',0,6,200,1,3
	.word	11415
	.byte	10
	.byte	'_Ifx_SRC_GTM',0,6,203,1,25,192,11,16
	.byte	'AEIIRQ',0,4
	.word	10007
	.byte	2,35,0,21,236,2
	.word	173
	.byte	22,235,2,0,16
	.byte	'reserved_4',0,236,2
	.word	11479
	.byte	2,35,4,16
	.byte	'ERR',0,4
	.word	10007
	.byte	3,35,240,2,16
	.byte	'reserved_174',0,12
	.word	10647
	.byte	3,35,244,2,21,32
	.word	10304
	.byte	22,0,0,16
	.byte	'TIM',0,32
	.word	11548
	.byte	3,35,128,3,21,224,7
	.word	173
	.byte	22,223,7,0,16
	.byte	'reserved_1A0',0,224,7
	.word	11571
	.byte	3,35,160,3,21,64
	.word	10304
	.byte	22,1,0,16
	.byte	'TOM',0,64
	.word	11606
	.byte	3,35,128,11,0,20
	.word	11443
	.byte	15
	.byte	'Ifx_SRC_GTM',0,6,212,1,3
	.word	11630
	.byte	10
	.byte	'_Ifx_SRC_HSM',0,6,215,1,25,8,16
	.byte	'HSM',0,8
	.word	10474
	.byte	2,35,0,0,20
	.word	11656
	.byte	15
	.byte	'Ifx_SRC_HSM',0,6,218,1,3
	.word	11689
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,6,221,1,25,4,16
	.byte	'SR',0,4
	.word	10007
	.byte	2,35,0,0,20
	.word	11715
	.byte	15
	.byte	'Ifx_SRC_LMU',0,6,224,1,3
	.word	11747
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,6,227,1,25,4,16
	.byte	'SR',0,4
	.word	10007
	.byte	2,35,0,0,20
	.word	11773
	.byte	15
	.byte	'Ifx_SRC_PMU',0,6,230,1,3
	.word	11805
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,6,233,1,25,24,16
	.byte	'TX',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'RX',0,4
	.word	10007
	.byte	2,35,4,16
	.byte	'ERR',0,4
	.word	10007
	.byte	2,35,8,16
	.byte	'PT',0,4
	.word	10007
	.byte	2,35,12,16
	.byte	'HC',0,4
	.word	10007
	.byte	2,35,16,16
	.byte	'U',0,4
	.word	10007
	.byte	2,35,20,0,20
	.word	11831
	.byte	15
	.byte	'Ifx_SRC_QSPI',0,6,241,1,3
	.word	11924
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,6,244,1,25,20,16
	.byte	'DTS',0,4
	.word	10007
	.byte	2,35,0,21,16
	.word	10007
	.byte	22,3,0,16
	.byte	'ERU',0,16
	.word	11983
	.byte	2,35,4,0,20
	.word	11951
	.byte	15
	.byte	'Ifx_SRC_SCU',0,6,248,1,3
	.word	12006
	.byte	10
	.byte	'_Ifx_SRC_SENT',0,6,251,1,25,16,16
	.byte	'SR',0,16
	.word	11983
	.byte	2,35,0,0,20
	.word	12032
	.byte	15
	.byte	'Ifx_SRC_SENT',0,6,254,1,3
	.word	12065
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,6,129,2,25,12,21,12
	.word	10007
	.byte	22,2,0,16
	.byte	'SR',0,12
	.word	12111
	.byte	2,35,0,0,20
	.word	12092
	.byte	15
	.byte	'Ifx_SRC_SMU',0,6,132,2,3
	.word	12133
	.byte	10
	.byte	'_Ifx_SRC_STM',0,6,135,2,25,96,16
	.byte	'SR0',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'SR1',0,4
	.word	10007
	.byte	2,35,4,21,88
	.word	173
	.byte	22,87,0,16
	.byte	'reserved_8',0,88
	.word	12204
	.byte	2,35,8,0,20
	.word	12159
	.byte	15
	.byte	'Ifx_SRC_STM',0,6,140,2,3
	.word	12234
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,6,143,2,25,192,2,16
	.byte	'SR0',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'SR1',0,4
	.word	10007
	.byte	2,35,4,16
	.byte	'SR2',0,4
	.word	10007
	.byte	2,35,8,16
	.byte	'SR3',0,4
	.word	10007
	.byte	2,35,12,21,176,2
	.word	173
	.byte	22,175,2,0,16
	.byte	'reserved_10',0,176,2
	.word	12335
	.byte	2,35,16,0,20
	.word	12260
	.byte	15
	.byte	'Ifx_SRC_VADCCG',0,6,150,2,3
	.word	12369
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,6,153,2,25,16,16
	.byte	'SR0',0,4
	.word	10007
	.byte	2,35,0,16
	.byte	'SR1',0,4
	.word	10007
	.byte	2,35,4,16
	.byte	'SR2',0,4
	.word	10007
	.byte	2,35,8,16
	.byte	'SR3',0,4
	.word	10007
	.byte	2,35,12,0,20
	.word	12398
	.byte	15
	.byte	'Ifx_SRC_VADCG',0,6,159,2,3
	.word	12472
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,6,162,2,25,4,16
	.byte	'SRC',0,4
	.word	10007
	.byte	2,35,0,0,20
	.word	12500
	.byte	15
	.byte	'Ifx_SRC_XBAR',0,6,165,2,3
	.word	12534
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,6,178,2,25,24,21,24
	.word	10067
	.byte	22,1,0,20
	.word	12584
	.byte	16
	.byte	'ASCLIN',0,24
	.word	12593
	.byte	2,35,0,0,20
	.word	12561
	.byte	15
	.byte	'Ifx_SRC_GASCLIN',0,6,181,2,3
	.word	12615
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,6,184,2,25,4,20
	.word	10154
	.byte	16
	.byte	'SPB',0,4
	.word	12665
	.byte	2,35,0,0,20
	.word	12645
	.byte	15
	.byte	'Ifx_SRC_GBCU',0,6,187,2,3
	.word	12684
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,6,190,2,25,96,21,64
	.word	10219
	.byte	22,0,0,20
	.word	12731
	.byte	16
	.byte	'CAN',0,64
	.word	12740
	.byte	2,35,0,21,32
	.word	10285
	.byte	22,0,0,20
	.word	12758
	.byte	16
	.byte	'CAN1',0,32
	.word	12767
	.byte	2,35,64,0,20
	.word	12711
	.byte	15
	.byte	'Ifx_SRC_GCAN',0,6,194,2,3
	.word	12787
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,6,197,2,25,32,21,32
	.word	10353
	.byte	22,1,0,20
	.word	12835
	.byte	16
	.byte	'CCU6',0,32
	.word	12844
	.byte	2,35,0,0,20
	.word	12814
	.byte	15
	.byte	'Ifx_SRC_GCCU6',0,6,200,2,3
	.word	12864
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,6,203,2,25,8,20
	.word	10451
	.byte	16
	.byte	'CERBERUS',0,8
	.word	12917
	.byte	2,35,0,0,20
	.word	12892
	.byte	15
	.byte	'Ifx_SRC_GCERBERUS',0,6,206,2,3
	.word	12941
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,6,209,2,25,32,21,32
	.word	10526
	.byte	22,0,0,20
	.word	12993
	.byte	16
	.byte	'CPU',0,32
	.word	13002
	.byte	2,35,0,0,20
	.word	12973
	.byte	15
	.byte	'Ifx_SRC_GCPU',0,6,212,2,3
	.word	13021
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,6,215,2,25,80,21,80
	.word	10615
	.byte	22,0,0,20
	.word	13068
	.byte	16
	.byte	'DMA',0,80
	.word	13077
	.byte	2,35,0,0,20
	.word	13048
	.byte	15
	.byte	'Ifx_SRC_GDMA',0,6,218,2,3
	.word	13096
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,6,221,2,25,4,21,4
	.word	10715
	.byte	22,0,0,20
	.word	13144
	.byte	16
	.byte	'EMEM',0,4
	.word	13153
	.byte	2,35,0,0,20
	.word	13123
	.byte	15
	.byte	'Ifx_SRC_GEMEM',0,6,224,2,3
	.word	13173
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,6,227,2,25,80,21,80
	.word	10775
	.byte	22,0,0,20
	.word	13222
	.byte	16
	.byte	'ERAY',0,80
	.word	13231
	.byte	2,35,0,0,20
	.word	13201
	.byte	15
	.byte	'Ifx_SRC_GERAY',0,6,230,2,3
	.word	13251
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,6,233,2,25,4,21,4
	.word	10938
	.byte	22,0,0,20
	.word	13299
	.byte	16
	.byte	'ETH',0,4
	.word	13308
	.byte	2,35,0,0,20
	.word	13279
	.byte	15
	.byte	'Ifx_SRC_GETH',0,6,236,2,3
	.word	13327
	.byte	10
	.byte	'_Ifx_SRC_GEVR',0,6,239,2,25,8,21,8
	.word	10996
	.byte	22,0,0,20
	.word	13374
	.byte	16
	.byte	'EVR',0,8
	.word	13383
	.byte	2,35,0,0,20
	.word	13354
	.byte	15
	.byte	'Ifx_SRC_GEVR',0,6,242,2,3
	.word	13402
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,6,245,2,25,12,21,12
	.word	11069
	.byte	22,0,0,20
	.word	13449
	.byte	16
	.byte	'FFT',0,12
	.word	13458
	.byte	2,35,0,0,20
	.word	13429
	.byte	15
	.byte	'Ifx_SRC_GFFT',0,6,248,2,3
	.word	13477
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,6,251,2,25,128,12,21,128,12
	.word	11155
	.byte	22,0,0,20
	.word	13526
	.byte	16
	.byte	'GPSR',0,128,12
	.word	13536
	.byte	2,35,0,0,20
	.word	13504
	.byte	15
	.byte	'Ifx_SRC_GGPSR',0,6,254,2,3
	.word	13557
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,6,129,3,25,48,21,48
	.word	11289
	.byte	22,0,0,20
	.word	13607
	.byte	16
	.byte	'GPT12',0,48
	.word	13616
	.byte	2,35,0,0,20
	.word	13585
	.byte	15
	.byte	'Ifx_SRC_GGPT12',0,6,132,3,3
	.word	13637
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,6,135,3,25,192,11,21,192,11
	.word	11443
	.byte	22,0,0,20
	.word	13687
	.byte	16
	.byte	'GTM',0,192,11
	.word	13697
	.byte	2,35,0,0,20
	.word	13666
	.byte	15
	.byte	'Ifx_SRC_GGTM',0,6,138,3,3
	.word	13717
	.byte	10
	.byte	'_Ifx_SRC_GHSM',0,6,141,3,25,8,21,8
	.word	11656
	.byte	22,0,0,20
	.word	13764
	.byte	16
	.byte	'HSM',0,8
	.word	13773
	.byte	2,35,0,0,20
	.word	13744
	.byte	15
	.byte	'Ifx_SRC_GHSM',0,6,144,3,3
	.word	13792
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,6,147,3,25,4,21,4
	.word	11715
	.byte	22,0,0,20
	.word	13839
	.byte	16
	.byte	'LMU',0,4
	.word	13848
	.byte	2,35,0,0,20
	.word	13819
	.byte	15
	.byte	'Ifx_SRC_GLMU',0,6,150,3,3
	.word	13867
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,6,153,3,25,8,21,8
	.word	11773
	.byte	22,1,0,20
	.word	13914
	.byte	16
	.byte	'PMU',0,8
	.word	13923
	.byte	2,35,0,0,20
	.word	13894
	.byte	15
	.byte	'Ifx_SRC_GPMU',0,6,156,3,3
	.word	13942
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,6,159,3,25,96,21,96
	.word	11831
	.byte	22,3,0,20
	.word	13990
	.byte	16
	.byte	'QSPI',0,96
	.word	13999
	.byte	2,35,0,0,20
	.word	13969
	.byte	15
	.byte	'Ifx_SRC_GQSPI',0,6,162,3,3
	.word	14019
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,6,165,3,25,20,20
	.word	11951
	.byte	16
	.byte	'SCU',0,20
	.word	14067
	.byte	2,35,0,0,20
	.word	14047
	.byte	15
	.byte	'Ifx_SRC_GSCU',0,6,168,3,3
	.word	14086
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,6,171,3,25,16,21,16
	.word	12032
	.byte	22,0,0,20
	.word	14134
	.byte	16
	.byte	'SENT',0,16
	.word	14143
	.byte	2,35,0,0,20
	.word	14113
	.byte	15
	.byte	'Ifx_SRC_GSENT',0,6,174,3,3
	.word	14163
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,6,177,3,25,12,21,12
	.word	12092
	.byte	22,0,0,20
	.word	14211
	.byte	16
	.byte	'SMU',0,12
	.word	14220
	.byte	2,35,0,0,20
	.word	14191
	.byte	15
	.byte	'Ifx_SRC_GSMU',0,6,180,3,3
	.word	14239
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,6,183,3,25,96,21,96
	.word	12159
	.byte	22,0,0,20
	.word	14286
	.byte	16
	.byte	'STM',0,96
	.word	14295
	.byte	2,35,0,0,20
	.word	14266
	.byte	15
	.byte	'Ifx_SRC_GSTM',0,6,186,3,3
	.word	14314
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,6,189,3,25,224,4,21,64
	.word	12398
	.byte	22,3,0,20
	.word	14363
	.byte	16
	.byte	'G',0,64
	.word	14372
	.byte	2,35,0,21,224,1
	.word	173
	.byte	22,223,1,0,16
	.byte	'reserved_40',0,224,1
	.word	14388
	.byte	2,35,64,21,192,2
	.word	12260
	.byte	22,0,0,20
	.word	14421
	.byte	16
	.byte	'CG',0,192,2
	.word	14431
	.byte	3,35,160,2,0,20
	.word	14341
	.byte	15
	.byte	'Ifx_SRC_GVADC',0,6,194,3,3
	.word	14451
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,6,197,3,25,4,20
	.word	12500
	.byte	16
	.byte	'XBAR',0,4
	.word	14500
	.byte	2,35,0,0,20
	.word	14479
	.byte	15
	.byte	'Ifx_SRC_GXBAR',0,6,200,3,3
	.word	14520
	.byte	15
	.byte	'uint8',0,7,90,29
	.word	173
	.byte	15
	.byte	'uint16',0,7,92,29
	.word	836
	.byte	15
	.byte	'uint32',0,7,94,29
	.word	237
	.byte	15
	.byte	'Uart_SizeType',0,4,240,3,16
	.word	836
	.byte	15
	.byte	'Uart_ChannelIdType',0,4,246,3,15
	.word	173
	.byte	15
	.byte	'UartNotifType',0,4,148,4,2
	.word	480
	.byte	15
	.byte	'Uart_ChannelType',0,4,183,4,2
	.word	457
	.byte	15
	.byte	'Uart_ConfigType',0,4,195,4,2
	.word	435
	.byte	15
	.byte	'Uart_ReturnType',0,4,208,4,2
	.word	1198
	.byte	15
	.byte	'Uart_StatusType',0,4,220,4,2
	.word	7855
	.byte	12
	.byte	'Uart_StateType',0,4,228,4,14,1,13
	.byte	'UART_UNINITIALISED',0,0,13
	.byte	'UART_INITIALISED',0,1,13
	.byte	'UART_OPERATION_IN_PROGRESS',0,2,0,15
	.byte	'Uart_StateType',0,4,233,4,2
	.word	14767
	.byte	10
	.byte	'Uart_ChannelInfoType',0,4,240,4,16,20,16
	.byte	'Uart_RxBuffPtr',0,4
	.word	1270
	.byte	2,35,0,16
	.byte	'Uart_TxBuffPtr',0,4
	.word	1270
	.byte	2,35,4,16
	.byte	'Uart_TxState',0,1
	.word	14767
	.byte	2,35,8,16
	.byte	'Uart_RxState',0,1
	.word	14767
	.byte	2,35,9,16
	.byte	'Uart_TxDataLeft',0,2
	.word	836
	.byte	2,35,10,16
	.byte	'Uart_RxDataLeft',0,2
	.word	836
	.byte	2,35,12,16
	.byte	'Uart_TotalDataTxd',0,2
	.word	836
	.byte	2,35,14,16
	.byte	'Uart_TotalDataRxd',0,2
	.word	836
	.byte	2,35,16,16
	.byte	'Uart_AssignedHW',0,1
	.word	173
	.byte	2,35,18,16
	.byte	'Uart_TxDataCopyCntr',0,1
	.word	173
	.byte	2,35,19,0,15
	.byte	'Uart_ChannelInfoType',0,4,252,4,3
	.word	14882
.L608:
	.byte	21,2
	.word	173
	.byte	22,1,0
.L609:
	.byte	21,20
	.word	14882
	.byte	22,0,0,21,4
	.word	237
	.byte	22,0,0
.L610:
	.byte	21,8
	.word	15208
	.byte	22,1,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L158:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,32,13,58,15,59,15
	.byte	57,15,73,19,54,15,39,12,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,11,0,0,0,6,46,1,3,8,58,15,59,15,57
	.byte	15,73,19,54,15,39,12,63,12,60,12,0,0,7,15,0,73,19,0,0,8,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60
	.byte	12,0,0,9,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0
	.byte	0,11,21,1,54,15,39,12,0,0,12,4,1,3,8,58,15,59,15,57,15,11,15,0,0,13,40,0,3,8,28,13,0,0,14,5,0,73,19,0
	.byte	0,15,22,0,3,8,58,15,59,15,57,15,73,19,0,0,16,13,0,3,8,11,15,73,19,56,9,0,0,17,38,0,73,19,0,0,18,23,1,58
	.byte	15,59,15,57,15,11,15,0,0,19,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,20,53,0,73,19,0,0,21,1,1,11,15,73
	.byte	19,0,0,22,33,0,47,15,0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0,25,46,0,3,8,58,15,59,15,57,15,54,15,63,12
	.byte	60,12,0,0,26,59,0,3,8,0,0,27,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L159:
	.word	.L769-.L768
.L768:
	.half	3
	.word	.L771-.L770
.L770:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_TcLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_WdgLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Uart.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxAsclin_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0,0
.L771:
.L769:
	.sdecl	'.debug_info',debug,cluster('Uart_Init')
	.sect	'.debug_info'
.L160:
	.word	460
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L163,.L162
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_Init',0,1,225,2,6,1,1,1
	.word	.L81,.L358,.L80
	.byte	4
	.byte	'ConfigPtr',0,1,225,2,39
	.word	.L359,.L360
	.byte	5
	.word	.L361
	.byte	6
	.byte	'ChannelConfigPtr',0,1,227,2,27
	.word	.L362,.L363
	.byte	6
	.byte	'ModuleNo',0,1,228,2,9
	.word	.L364,.L365
	.byte	6
	.byte	'Chan',0,1,228,2,18
	.word	.L364,.L366
	.byte	6
	.byte	'MaxChannel',0,1,229,2,9
	.word	.L364,.L367
	.byte	6
	.byte	'ClkFailureChk',0,1,229,2,34
	.word	.L364,.L368
	.byte	7
	.word	.L369,.L370,.L371
	.byte	8
	.word	.L372,.L373
	.byte	8
	.word	.L374,.L375
	.byte	9
	.word	.L376,.L377
	.byte	6
	.byte	'ModClkStatus',0,1,149,5,9
	.word	.L364,.L380
	.byte	0,0,7
	.word	.L369,.L378,.L379
	.byte	8
	.word	.L372,.L373
	.byte	8
	.word	.L374,.L375
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_Init')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_Init')
	.sect	'.debug_line'
.L162:
	.word	.L773-.L772
.L772:
	.half	3
	.word	.L775-.L774
.L774:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L775:
	.byte	5,19,7,0,5,2
	.word	.L81
	.byte	3,158,3,1,5,6,3,66,1,5,15,9
	.half	.L611-.L81
	.byte	3,193,0,1,5,6,3,191,127,1,5,27,9
	.half	.L615-.L611
	.byte	3,60,1,5,9,9
	.half	.L619-.L615
	.byte	3,27,1,9
	.half	.L776-.L619
	.byte	3,10,1,5,47,9
	.half	.L777-.L776
	.byte	3,96,1,5,37,9
	.half	.L3-.L777
	.byte	3,4,1,5,56,9
	.half	.L778-.L3
	.byte	1,5,55,9
	.half	.L779-.L778
	.byte	1,5,34,9
	.half	.L617-.L779
	.byte	3,2,1,5,3,9
	.half	.L370-.L617
	.byte	3,240,1,1,9
	.half	.L613-.L370
	.byte	3,4,1,9
	.half	.L780-.L613
	.byte	3,3,1,5,27,9
	.half	.L781-.L780
	.byte	3,12,1,5,56,9
	.half	.L782-.L781
	.byte	3,1,1,5,34,9
	.half	.L620-.L782
	.byte	1,5,9,3,143,126,1,5,27,9
	.half	.L371-.L620
	.byte	1,5,7,9
	.half	.L378-.L371
	.byte	3,114,1,5,32,7,9
	.half	.L379-.L378
	.byte	3,8,1,5,48,9
	.half	.L623-.L379
	.byte	3,3,1,5,38,9
	.half	.L783-.L623
	.byte	3,3,1,5,47,9
	.half	.L784-.L783
	.byte	3,3,1,5,45,1,9
	.half	.L621-.L784
	.byte	3,1,1,5,47,9
	.half	.L785-.L621
	.byte	3,3,1,5,45,1,9
	.half	.L786-.L785
	.byte	3,1,1,5,49,9
	.half	.L787-.L786
	.byte	3,114,1,5,50,9
	.half	.L4-.L787
	.byte	3,21,1,5,48,1,5,38,9
	.half	.L624-.L4
	.byte	3,3,1,5,47,9
	.half	.L788-.L624
	.byte	3,3,1,5,45,1,5,22,9
	.half	.L789-.L788
	.byte	3,119,1,5,45,3,10,1,5,22,9
	.half	.L618-.L789
	.byte	3,118,1,5,53,9
	.half	.L5-.L618
	.byte	3,91,1,5,47,9
	.half	.L2-.L5
	.byte	1,5,5,7,9
	.half	.L790-.L2
	.byte	3,51,1,5,7,7,9
	.half	.L791-.L790
	.byte	3,4,1,5,23,9
	.half	.L792-.L791
	.byte	1,5,1,9
	.half	.L6-.L792
	.byte	3,17,1,7,9
	.half	.L164-.L6
	.byte	0,1,1
.L773:
	.sdecl	'.debug_ranges',debug,cluster('Uart_Init')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L81,0,.L164-.L81,0,0
.L361:
	.word	-1,.L81,0,.L358-.L81,-1,.L83,0,.L329-.L83,-1,.L85,0,.L324-.L85,0,0
.L377:
	.word	-1,.L81,.L370-.L81,.L371-.L81,.L378-.L81,.L379-.L81,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_Read')
	.sect	'.debug_info'
.L165:
	.word	533
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L168,.L167
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_Read',0,1,148,9,17
	.word	.L381
	.byte	1,1,1
	.word	.L91,.L382,.L90
	.byte	4
	.byte	'Channel',0,1,149,9,30
	.word	.L364,.L383
	.byte	4
	.byte	'MemPtr',0,1,149,9,54
	.word	.L384,.L385
	.byte	4
	.byte	'Size',0,1,149,9,75
	.word	.L386,.L387
	.byte	5
	.word	.L91,.L382
	.byte	6
	.byte	'HwModulePtr',0,1,151,9,16
	.word	.L388,.L389
	.byte	6
	.byte	'Retvalue',0,1,152,9,19
	.word	.L381,.L390
	.byte	6
	.byte	'HwUnit',0,1,153,9,9
	.word	.L364,.L391
	.byte	6
	.byte	'TempIntLevel',0,1,154,9,9
	.word	.L364,.L392
	.byte	6
	.byte	'ChRxLock',0,1,157,9,10
	.word	.L393,.L394
	.byte	5
	.word	.L395,.L396
	.byte	6
	.byte	'val',0,1,244,9,5
	.word	.L393,.L397
	.byte	0,5
	.word	.L398,.L21
	.byte	6
	.byte	'val',0,1,253,9,7
	.word	.L393,.L399
	.byte	0,5
	.word	.L21,.L400
	.byte	6
	.byte	'val',0,1,132,10,7
	.word	.L393,.L401
	.byte	0,5
	.word	.L402,.L403
	.byte	6
	.byte	'val',0,1,138,10,5
	.word	.L393,.L404
	.byte	0,5
	.word	.L405,.L20
	.byte	6
	.byte	'val',0,1,149,10,5
	.word	.L393,.L406
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_Read')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_Read')
	.sect	'.debug_line'
.L167:
	.word	.L794-.L793
.L793:
	.half	3
	.word	.L796-.L795
.L795:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L796:
	.byte	5,17,7,0,5,2
	.word	.L91
	.byte	3,147,9,1,5,34,3,202,0,1,5,17,9
	.half	.L797-.L91
	.byte	3,182,127,1,5,45,3,202,0,1,5,28,9
	.half	.L666-.L797
	.byte	3,186,127,1,5,17,3,124,1,5,63,9
	.half	.L665-.L666
	.byte	3,202,0,1,5,4,9
	.half	.L659-.L665
	.byte	3,3,1,5,18,7,9
	.half	.L798-.L659
	.byte	3,2,1,5,32,1,5,21,9
	.half	.L19-.L798
	.byte	3,6,1,5,5,1,5,21,9
	.half	.L662-.L19
	.byte	1,5,45,9
	.half	.L799-.L662
	.byte	1,5,43,9
	.half	.L800-.L799
	.byte	1,5,45,9
	.half	.L801-.L800
	.byte	3,1,1,5,47,9
	.half	.L802-.L801
	.byte	3,1,1,5,50,9
	.half	.L803-.L802
	.byte	3,1,1,5,49,1,5,39,9
	.half	.L804-.L803
	.byte	3,2,1,5,20,9
	.half	.L667-.L804
	.byte	3,3,1,5,35,9
	.half	.L805-.L667
	.byte	1,5,34,9
	.half	.L806-.L805
	.byte	1,5,5,9
	.half	.L395-.L806
	.byte	3,3,1,5,33,9
	.half	.L396-.L395
	.byte	3,4,1,5,52,9
	.half	.L807-.L396
	.byte	1,5,5,9
	.half	.L668-.L807
	.byte	1,5,35,7,9
	.half	.L808-.L668
	.byte	3,4,1,5,7,9
	.half	.L809-.L808
	.byte	3,1,1,5,56,9
	.half	.L810-.L809
	.byte	3,127,1,5,7,9
	.half	.L21-.L810
	.byte	3,8,1,5,32,9
	.half	.L400-.L21
	.byte	3,10,1,5,5,9
	.half	.L402-.L400
	.byte	3,124,1,5,32,9
	.half	.L403-.L402
	.byte	3,4,1,9
	.half	.L664-.L403
	.byte	3,3,1,5,33,9
	.half	.L811-.L664
	.byte	3,1,1,5,5,9
	.half	.L405-.L811
	.byte	3,3,1,5,3,9
	.half	.L20-.L405
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L169-.L20
	.byte	0,1,1
.L794:
	.sdecl	'.debug_ranges',debug,cluster('Uart_Read')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L91,0,.L169-.L91,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_Write')
	.sect	'.debug_info'
.L170:
	.word	553
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L173,.L172
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_Write',0,1,179,12,17
	.word	.L381
	.byte	1,1,1
	.word	.L103,.L407,.L102
	.byte	4
	.byte	'Channel',0,1,180,12,31
	.word	.L364,.L408
	.byte	4
	.byte	'MemPtr',0,1,180,12,55
	.word	.L384,.L409
	.byte	4
	.byte	'Size',0,1,180,12,76
	.word	.L386,.L410
	.byte	5
	.word	.L411
	.byte	6
	.byte	'ChannelConfigPtr',0,1,182,12,27
	.word	.L362,.L412
	.byte	6
	.byte	'HwModulePtr',0,1,183,12,15
	.word	.L388,.L413
	.byte	6
	.byte	'Retvalue',0,1,184,12,19
	.word	.L381,.L414
	.byte	6
	.byte	'HwUnit',0,1,185,12,9
	.word	.L364,.L415
	.byte	6
	.byte	'ChTxLock',0,1,188,12,10
	.word	.L393,.L416
	.byte	7
	.word	.L417,.L418
	.byte	6
	.byte	'val',0,1,149,13,4
	.word	.L393,.L419
	.byte	0,7
	.word	.L418,.L420
	.byte	6
	.byte	'val',0,1,153,13,4
	.word	.L393,.L421
	.byte	0,7
	.word	.L422,.L423
	.byte	6
	.byte	'val',0,1,163,13,5
	.word	.L393,.L424
	.byte	0,5
	.word	.L425
	.byte	6
	.byte	'val',0,1,173,13,6
	.word	.L393,.L428
	.byte	0,5
	.word	.L429
	.byte	6
	.byte	'val',0,1,177,13,6
	.word	.L393,.L431
	.byte	0,7
	.word	.L432,.L33
	.byte	6
	.byte	'val',0,1,192,13,4
	.word	.L393,.L433
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_Write')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,11,1,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_Write')
	.sect	'.debug_line'
.L172:
	.word	.L813-.L812
.L812:
	.half	3
	.word	.L815-.L814
.L814:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L815:
	.byte	5,17,7,0,5,2
	.word	.L103
	.byte	3,178,12,1,5,28,9
	.half	.L688-.L103
	.byte	3,5,1,5,34,3,198,0,1,5,63,9
	.half	.L816-.L688
	.byte	1,5,4,9
	.half	.L686-.L816
	.byte	3,3,1,5,18,7,9
	.half	.L817-.L686
	.byte	3,2,1,5,32,1,5,25,9
	.half	.L32-.L817
	.byte	3,7,1,5,40,9
	.half	.L690-.L32
	.byte	1,5,59,9
	.half	.L818-.L690
	.byte	1,5,58,9
	.half	.L819-.L818
	.byte	1,5,20,9
	.half	.L692-.L819
	.byte	3,2,1,5,44,9
	.half	.L820-.L692
	.byte	1,5,19,3,6,1,5,42,9
	.half	.L821-.L820
	.byte	3,122,1,5,28,9
	.half	.L822-.L821
	.byte	3,27,1,5,38,9
	.half	.L694-.L822
	.byte	3,104,1,5,34,9
	.half	.L695-.L694
	.byte	3,3,1,5,33,9
	.half	.L823-.L695
	.byte	1,5,4,9
	.half	.L417-.L823
	.byte	3,3,1,9
	.half	.L418-.L417
	.byte	3,4,1,5,44,9
	.half	.L420-.L418
	.byte	3,5,1,5,50,9
	.half	.L824-.L420
	.byte	3,2,1,5,46,3,127,1,5,29,9
	.half	.L698-.L824
	.byte	3,1,1,5,48,9
	.half	.L693-.L698
	.byte	1,5,28,9
	.half	.L825-.L693
	.byte	3,7,1,5,5,9
	.half	.L422-.L825
	.byte	3,124,1,5,28,9
	.half	.L423-.L422
	.byte	3,4,1,5,4,9
	.half	.L691-.L423
	.byte	3,3,1,5,6,7,9
	.half	.L426-.L691
	.byte	3,3,1,9
	.half	.L427-.L426
	.byte	3,4,1,5,52,9
	.half	.L430-.L427
	.byte	3,3,1,5,50,1,5,6,9
	.half	.L704-.L430
	.byte	3,121,1,5,47,9
	.half	.L34-.L704
	.byte	3,12,1,5,33,9
	.half	.L706-.L34
	.byte	3,1,1,5,32,9
	.half	.L35-.L706
	.byte	3,3,1,5,4,9
	.half	.L432-.L35
	.byte	3,3,1,5,3,9
	.half	.L33-.L432
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L174-.L33
	.byte	0,1,1
.L813:
	.sdecl	'.debug_ranges',debug,cluster('Uart_Write')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L103,0,.L174-.L103,0,0
.L411:
	.word	-1,.L103,0,.L407-.L103,-1,.L107,0,.L309-.L107,-1,.L109,0,.L304-.L109,-1,.L111,0,.L299-.L111,0,0
.L425:
	.word	-1,.L103,.L426-.L103,.L427-.L103,-1,.L113,0,.L289-.L113,0,0
.L429:
	.word	-1,.L103,.L427-.L103,.L430-.L103,-1,.L105,0,.L314-.L105,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_GetStatus')
	.sect	'.debug_info'
.L175:
	.word	279
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L178,.L177
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_GetStatus',0,1,151,17,17
	.word	.L434
	.byte	1,1,1
	.word	.L121,.L435,.L120
	.byte	4
	.byte	'Channel',0,1,151,17,51
	.word	.L364,.L436
	.byte	5
	.word	.L121,.L435
	.byte	6
	.byte	'UartDriverState',0,1,153,17,19
	.word	.L434,.L437
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_GetStatus')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_GetStatus')
	.sect	'.debug_line'
.L177:
	.word	.L827-.L826
.L826:
	.half	3
	.word	.L829-.L828
.L828:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L829:
	.byte	5,25,7,0,5,2
	.word	.L121
	.byte	3,184,17,1,5,34,9
	.half	.L715-.L121
	.byte	1,5,8,9
	.half	.L830-.L715
	.byte	1,5,35,7,9
	.half	.L831-.L830
	.byte	3,1,1,5,49,9
	.half	.L832-.L831
	.byte	1,5,23,7,9
	.half	.L46-.L832
	.byte	3,2,1,5,1,3,14,1,5,14,7,9
	.half	.L47-.L46
	.byte	3,116,1,5,49,7,9
	.half	.L833-.L47
	.byte	3,1,1,5,23,7,9
	.half	.L834-.L833
	.byte	3,2,1,5,1,3,9,1,5,23,7,9
	.half	.L49-.L834
	.byte	3,123,1,5,1,3,5,1,7,9
	.half	.L179-.L49
	.byte	0,1,1
.L827:
	.sdecl	'.debug_ranges',debug,cluster('Uart_GetStatus')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L121,0,.L179-.L121,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_IsrReceive')
	.sect	'.debug_info'
.L180:
	.word	318
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L183,.L182
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_IsrReceive',0,1,223,17,6,1,1,1
	.word	.L123,.L438,.L122
	.byte	4
	.byte	'HwUnit',0,1,223,17,29
	.word	.L364,.L439
	.byte	5
	.word	.L440
	.byte	6
	.byte	'ChannelConfigPtr',0,1,225,17,27
	.word	.L362,.L441
	.byte	6
	.byte	'HwModulePtr',0,1,228,17,15
	.word	.L388,.L442
	.byte	6
	.byte	'Channel',0,1,229,17,22
	.word	.L364,.L443
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_IsrReceive')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_IsrReceive')
	.sect	'.debug_line'
.L182:
	.word	.L836-.L835
.L835:
	.half	3
	.word	.L838-.L837
.L837:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L838:
	.byte	5,6,7,0,5,2
	.word	.L123
	.byte	3,222,17,1,5,24,9
	.half	.L839-.L123
	.byte	3,12,1,5,39,9
	.half	.L719-.L839
	.byte	1,5,58,9
	.half	.L840-.L719
	.byte	1,5,26,3,3,1,5,57,9
	.half	.L720-.L840
	.byte	3,125,1,5,26,9
	.half	.L727-.L720
	.byte	3,3,1,5,23,9
	.half	.L841-.L727
	.byte	3,2,1,5,32,9
	.half	.L842-.L841
	.byte	1,5,3,9
	.half	.L843-.L842
	.byte	1,5,56,7,9
	.half	.L844-.L843
	.byte	3,3,1,5,44,9
	.half	.L845-.L844
	.byte	3,4,1,5,61,9
	.half	.L723-.L845
	.byte	3,125,1,5,30,3,127,1,5,44,9
	.half	.L846-.L723
	.byte	3,4,1,5,41,9
	.half	.L724-.L846
	.byte	3,7,1,5,56,3,124,1,5,41,9
	.half	.L725-.L724
	.byte	3,4,1,5,59,9
	.half	.L847-.L725
	.byte	3,124,1,5,30,1,5,41,9
	.half	.L848-.L847
	.byte	3,4,1,5,26,9
	.half	.L849-.L848
	.byte	3,3,1,5,37,9
	.half	.L726-.L849
	.byte	1,5,55,9
	.half	.L850-.L726
	.byte	1,5,48,9
	.half	.L851-.L850
	.byte	3,2,1,5,46,9
	.half	.L728-.L851
	.byte	1,9
	.half	.L852-.L728
	.byte	3,1,1,5,44,1,5,35,9
	.half	.L853-.L852
	.byte	3,2,1,5,5,9
	.half	.L854-.L853
	.byte	1,5,55,7,9
	.half	.L855-.L854
	.byte	3,3,1,5,1,9
	.half	.L53-.L855
	.byte	3,8,1,7,9
	.half	.L184-.L53
	.byte	0,1,1
.L836:
	.sdecl	'.debug_ranges',debug,cluster('Uart_IsrReceive')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L123,0,.L184-.L123,0,0
.L440:
	.word	-1,.L123,0,.L438-.L123,-1,.L125,0,.L319-.L125,-1,.L127,0,.L274-.L127,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_IsrTransmit')
	.sect	'.debug_info'
.L185:
	.word	314
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L188,.L187
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_IsrTransmit',0,1,166,18,6,1,1,1
	.word	.L129,.L444,.L128
	.byte	4
	.byte	'HwUnit',0,1,166,18,30
	.word	.L364,.L445
	.byte	5
	.word	.L129,.L444
	.byte	6
	.byte	'HwModulePtr',0,1,170,18,15
	.word	.L388,.L446
	.byte	6
	.byte	'Tempsum',0,1,171,18,10
	.word	.L386,.L447
	.byte	6
	.byte	'Channel',0,1,172,18,22
	.word	.L364,.L448
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_IsrTransmit')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_IsrTransmit')
	.sect	'.debug_line'
.L187:
	.word	.L857-.L856
.L856:
	.half	3
	.word	.L859-.L858
.L858:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L859:
	.byte	5,6,7,0,5,2
	.word	.L129
	.byte	3,165,18,1,5,38,9
	.half	.L860-.L129
	.byte	3,10,1,5,40,9
	.half	.L861-.L860
	.byte	3,1,1,5,47,9
	.half	.L862-.L861
	.byte	3,127,1,5,43,9
	.half	.L863-.L862
	.byte	3,1,1,5,47,3,127,1,5,28,9
	.half	.L864-.L863
	.byte	3,4,1,5,68,9
	.half	.L865-.L864
	.byte	3,124,1,5,13,9
	.half	.L866-.L865
	.byte	1,5,47,9
	.half	.L729-.L866
	.byte	3,4,1,5,33,9
	.half	.L867-.L729
	.byte	3,2,1,5,3,9
	.half	.L730-.L867
	.byte	1,5,73,7,9
	.half	.L868-.L730
	.byte	3,3,1,5,46,9
	.half	.L869-.L868
	.byte	3,5,1,5,61,9
	.half	.L734-.L869
	.byte	3,3,1,5,42,9
	.half	.L870-.L734
	.byte	3,7,1,5,64,9
	.half	.L735-.L870
	.byte	3,121,1,5,33,1,5,42,9
	.half	.L871-.L735
	.byte	3,7,1,5,61,9
	.half	.L872-.L871
	.byte	3,124,1,5,29,9
	.half	.L873-.L872
	.byte	3,1,1,5,33,3,127,1,5,42,9
	.half	.L874-.L873
	.byte	3,4,1,5,49,9
	.half	.L736-.L874
	.byte	3,2,1,5,47,9
	.half	.L732-.L736
	.byte	1,5,54,9
	.half	.L875-.L732
	.byte	3,1,1,5,52,1,5,1,9
	.half	.L876-.L875
	.byte	3,37,1,5,73,7,9
	.half	.L55-.L876
	.byte	3,96,1,5,6,9
	.half	.L877-.L55
	.byte	3,4,1,5,77,7,9
	.half	.L878-.L877
	.byte	3,4,1,5,57,9
	.half	.L57-.L878
	.byte	3,6,1,5,46,9
	.half	.L879-.L57
	.byte	3,4,1,5,60,9
	.half	.L737-.L879
	.byte	3,124,1,5,31,1,5,46,9
	.half	.L880-.L737
	.byte	3,4,1,5,30,9
	.half	.L738-.L880
	.byte	3,3,1,5,47,9
	.half	.L740-.L738
	.byte	3,3,1,5,71,9
	.half	.L742-.L740
	.byte	3,3,1,5,40,9
	.half	.L881-.L742
	.byte	3,1,1,5,31,3,127,1,5,1,9
	.half	.L882-.L881
	.byte	3,5,1,7,9
	.half	.L189-.L882
	.byte	0,1,1
.L857:
	.sdecl	'.debug_ranges',debug,cluster('Uart_IsrTransmit')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L129,0,.L189-.L129,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_IsrError')
	.sect	'.debug_info'
.L190:
	.word	339
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L193,.L192
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_IsrError',0,1,133,19,6,1,1,1
	.word	.L131,.L449,.L130
	.byte	4
	.byte	'HwUnit',0,1,133,19,26
	.word	.L364,.L450
	.byte	5
	.word	.L131,.L449
	.byte	6
	.byte	'HwModulePtr',0,1,137,19,15
	.word	.L388,.L451
	.byte	6
	.byte	'ChannelConfigPtr',0,1,138,19,27
	.word	.L362,.L452
	.byte	6
	.byte	'ErrId',0,1,139,19,20
	.word	.L453,.L454
	.byte	6
	.byte	'Channel',0,1,140,19,9
	.word	.L364,.L455
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_IsrError')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_IsrError')
	.sect	'.debug_line'
.L192:
	.word	.L884-.L883
.L883:
	.half	3
	.word	.L886-.L885
.L885:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L886:
	.byte	5,45,7,0,5,2
	.word	.L131
	.byte	3,136,19,1,5,30,1,5,44,9
	.half	.L887-.L131
	.byte	1,5,26,9
	.half	.L745-.L887
	.byte	3,2,1,5,19,3,1,1,5,37,9
	.half	.L888-.L745
	.byte	1,5,24,9
	.half	.L743-.L888
	.byte	3,4,1,5,39,9
	.half	.L889-.L743
	.byte	1,5,58,9
	.half	.L890-.L889
	.byte	1,5,57,9
	.half	.L891-.L890
	.byte	1,5,27,9
	.half	.L746-.L891
	.byte	3,3,1,5,3,9
	.half	.L892-.L746
	.byte	1,5,72,7,9
	.half	.L893-.L892
	.byte	3,3,1,5,11,9
	.half	.L894-.L893
	.byte	3,3,1,5,44,9
	.half	.L895-.L894
	.byte	3,126,1,5,31,3,127,1,5,80,9
	.half	.L896-.L895
	.byte	3,2,1,5,31,9
	.half	.L58-.L896
	.byte	3,4,1,5,8,9
	.half	.L897-.L58
	.byte	1,5,72,7,9
	.half	.L898-.L897
	.byte	3,3,1,5,11,9
	.half	.L899-.L898
	.byte	3,3,1,5,44,9
	.half	.L900-.L899
	.byte	3,126,1,5,31,3,127,1,5,80,9
	.half	.L901-.L900
	.byte	3,2,1,5,31,9
	.half	.L60-.L901
	.byte	3,4,1,5,8,9
	.half	.L902-.L60
	.byte	1,5,72,7,9
	.half	.L903-.L902
	.byte	3,3,1,5,11,9
	.half	.L904-.L903
	.byte	3,3,1,5,44,9
	.half	.L905-.L904
	.byte	3,126,1,5,31,3,127,1,5,78,9
	.half	.L906-.L905
	.byte	3,2,1,5,31,9
	.half	.L62-.L906
	.byte	3,4,1,5,8,9
	.half	.L907-.L62
	.byte	1,5,72,7,9
	.half	.L908-.L907
	.byte	3,3,1,5,11,9
	.half	.L909-.L908
	.byte	3,3,1,5,44,9
	.half	.L910-.L909
	.byte	3,126,1,5,31,3,127,1,5,80,9
	.half	.L911-.L910
	.byte	3,2,1,5,31,9
	.half	.L64-.L911
	.byte	3,4,1,5,8,9
	.half	.L912-.L64
	.byte	1,5,72,7,9
	.half	.L913-.L912
	.byte	3,3,1,5,11,9
	.half	.L914-.L913
	.byte	3,3,1,5,44,9
	.half	.L915-.L914
	.byte	3,126,1,5,31,3,127,1,5,80,9
	.half	.L916-.L915
	.byte	3,2,1,5,31,9
	.half	.L66-.L916
	.byte	3,4,1,5,8,9
	.half	.L917-.L66
	.byte	1,5,56,7,9
	.half	.L918-.L917
	.byte	3,4,1,5,59,9
	.half	.L919-.L918
	.byte	1,5,30,1,5,72,9
	.half	.L744-.L919
	.byte	3,4,1,5,60,9
	.half	.L747-.L744
	.byte	3,7,1,5,11,9
	.half	.L920-.L747
	.byte	3,125,1,5,63,9
	.half	.L921-.L920
	.byte	3,3,1,5,32,1,5,42,9
	.half	.L922-.L921
	.byte	3,8,1,5,56,9
	.half	.L748-.L922
	.byte	3,124,1,5,59,9
	.half	.L923-.L748
	.byte	3,1,1,5,30,3,127,1,5,42,9
	.half	.L924-.L923
	.byte	3,4,1,5,7,9
	.half	.L59-.L924
	.byte	3,8,1,5,42,7,9
	.half	.L925-.L59
	.byte	1,5,13,7,9
	.half	.L926-.L925
	.byte	3,1,1,5,45,7,9
	.half	.L927-.L926
	.byte	1,5,26,7,9
	.half	.L69-.L927
	.byte	3,3,1,5,37,9
	.half	.L928-.L69
	.byte	1,5,55,9
	.half	.L929-.L928
	.byte	1,5,21,9
	.half	.L930-.L929
	.byte	3,2,1,5,5,1,5,21,9
	.half	.L931-.L930
	.byte	1,5,46,9
	.half	.L749-.L931
	.byte	1,5,44,9
	.half	.L932-.L749
	.byte	1,5,35,9
	.half	.L933-.L932
	.byte	3,2,1,5,5,9
	.half	.L934-.L933
	.byte	1,5,55,7,9
	.half	.L935-.L934
	.byte	3,3,1,5,7,9
	.half	.L72-.L935
	.byte	3,8,1,5,46,7,9
	.half	.L936-.L72
	.byte	1,5,26,7,9
	.half	.L74-.L936
	.byte	3,3,1,5,55,9
	.half	.L937-.L74
	.byte	1,5,21,9
	.half	.L938-.L937
	.byte	3,2,1,5,46,9
	.half	.L939-.L938
	.byte	1,5,44,9
	.half	.L940-.L939
	.byte	1,5,35,9
	.half	.L941-.L940
	.byte	3,2,1,5,5,9
	.half	.L942-.L941
	.byte	1,5,56,7,9
	.half	.L943-.L942
	.byte	3,3,1,5,1,9
	.half	.L75-.L943
	.byte	3,8,1,7,9
	.half	.L194-.L75
	.byte	0,1,1
.L884:
	.sdecl	'.debug_ranges',debug,cluster('Uart_IsrError')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L131,0,.L194-.L131,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lHwInit')
	.sect	'.debug_info'
.L195:
	.word	955
	.half	3
	.word	.L196
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L198,.L197
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lHwInit',0,1,197,5,13,1,1
	.word	.L87,.L456,.L86
	.byte	4
	.byte	'HwUnit',0,1,197,5,32
	.word	.L364,.L457
	.byte	4
	.byte	'ChannelConfigPtr',0,1,197,5,63
	.word	.L362,.L458
	.byte	5
	.word	.L87,.L456
	.byte	6
	.byte	'HwModulePtr',0,1,199,5,16
	.word	.L388,.L459
	.byte	6
	.byte	'TimeOutCount',0,1,200,5,10
	.word	.L393,.L460
	.byte	5
	.word	.L461,.L462
	.byte	6
	.byte	'val',0,1,206,5,3
	.word	.L393,.L463
	.byte	0,7
	.word	.L464
	.byte	6
	.byte	'val',0,1,219,5,3
	.word	.L393,.L468
	.byte	0,7
	.word	.L469
	.byte	6
	.byte	'val',0,1,222,5,3
	.word	.L393,.L471
	.byte	0,5
	.word	.L472,.L473
	.byte	6
	.byte	'val',0,1,235,5,3
	.word	.L393,.L474
	.byte	0,7
	.word	.L475
	.byte	6
	.byte	'val',0,1,246,5,4
	.word	.L393,.L479
	.byte	0,7
	.word	.L480
	.byte	6
	.byte	'val',0,1,151,6,3
	.word	.L393,.L483
	.byte	0,5
	.word	.L478,.L484
	.byte	6
	.byte	'val',0,1,250,5,3
	.word	.L393,.L485
	.byte	0,5
	.word	.L484,.L486
	.byte	6
	.byte	'val',0,1,253,5,3
	.word	.L393,.L487
	.byte	0,5
	.word	.L486,.L488
	.byte	6
	.byte	'val',0,1,128,6,3
	.word	.L393,.L489
	.byte	0,5
	.word	.L488,.L490
	.byte	6
	.byte	'val',0,1,131,6,3
	.word	.L393,.L491
	.byte	0,5
	.word	.L490,.L492
	.byte	6
	.byte	'val',0,1,136,6,3
	.word	.L393,.L493
	.byte	0,5
	.word	.L492,.L494
	.byte	6
	.byte	'val',0,1,140,6,3
	.word	.L393,.L495
	.byte	0,5
	.word	.L494,.L496
	.byte	6
	.byte	'val',0,1,143,6,3
	.word	.L393,.L497
	.byte	0,5
	.word	.L496,.L481
	.byte	6
	.byte	'val',0,1,148,6,3
	.word	.L393,.L498
	.byte	0,5
	.word	.L482,.L499
	.byte	6
	.byte	'val',0,1,156,6,3
	.word	.L393,.L500
	.byte	0,5
	.word	.L499,.L501
	.byte	6
	.byte	'val',0,1,159,6,3
	.word	.L393,.L502
	.byte	0,5
	.word	.L501,.L503
	.byte	6
	.byte	'val',0,1,162,6,3
	.word	.L393,.L504
	.byte	0,5
	.word	.L503,.L505
	.byte	6
	.byte	'val',0,1,165,6,3
	.word	.L393,.L506
	.byte	0,5
	.word	.L505,.L507
	.byte	6
	.byte	'val',0,1,170,6,3
	.word	.L393,.L508
	.byte	0,5
	.word	.L507,.L509
	.byte	6
	.byte	'val',0,1,173,6,3
	.word	.L393,.L510
	.byte	0,5
	.word	.L509,.L511
	.byte	6
	.byte	'val',0,1,178,6,3
	.word	.L393,.L512
	.byte	0,5
	.word	.L511,.L513
	.byte	6
	.byte	'val',0,1,182,6,3
	.word	.L393,.L514
	.byte	0,5
	.word	.L513,.L515
	.byte	6
	.byte	'val',0,1,184,6,3
	.word	.L393,.L516
	.byte	0,5
	.word	.L515,.L517
	.byte	6
	.byte	'val',0,1,188,6,3
	.word	.L393,.L518
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lHwInit')
	.sect	'.debug_abbrev'
.L196:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,11,1,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lHwInit')
	.sect	'.debug_line'
.L197:
	.word	.L945-.L944
.L944:
	.half	3
	.word	.L947-.L946
.L946:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L947:
	.byte	5,34,7,0,5,2
	.word	.L87
	.byte	3,203,5,1,5,19,1,5,33,9
	.half	.L948-.L87
	.byte	1,5,23,9
	.half	.L626-.L948
	.byte	3,124,1,5,3,9
	.half	.L461-.L626
	.byte	3,6,1,5,80,9
	.half	.L462-.L461
	.byte	3,6,1,5,17,9
	.half	.L8-.L462
	.byte	3,2,1,5,13,9
	.half	.L7-.L8
	.byte	3,124,1,5,30,9
	.half	.L949-.L7
	.byte	3,1,1,5,10,9
	.half	.L950-.L949
	.byte	3,127,1,5,75,7,9
	.half	.L951-.L950
	.byte	3,2,1,5,3,7,9
	.half	.L9-.L951
	.byte	3,7,1,5,16,9
	.half	.L465-.L9
	.byte	3,126,1,5,3,9
	.half	.L466-.L465
	.byte	3,2,1,9
	.half	.L467-.L466
	.byte	3,3,1,5,65,9
	.half	.L470-.L467
	.byte	3,6,1,5,18,9
	.half	.L11-.L470
	.byte	3,2,1,5,13,9
	.half	.L10-.L11
	.byte	3,124,1,5,32,9
	.half	.L952-.L10
	.byte	3,1,1,5,10,9
	.half	.L953-.L952
	.byte	3,127,1,5,60,7,9
	.half	.L954-.L953
	.byte	3,2,1,5,16,7,9
	.half	.L12-.L954
	.byte	3,5,1,5,3,3,2,1,5,80,9
	.half	.L473-.L12
	.byte	3,6,1,5,18,9
	.half	.L14-.L473
	.byte	3,2,1,5,13,9
	.half	.L13-.L14
	.byte	3,124,1,5,29,9
	.half	.L955-.L13
	.byte	3,1,1,5,10,9
	.half	.L956-.L955
	.byte	3,127,1,5,75,7,9
	.half	.L957-.L956
	.byte	3,2,1,5,4,7,9
	.half	.L15-.L957
	.byte	3,5,1,5,3,9
	.half	.L476-.L15
	.byte	3,33,1,5,4,9
	.half	.L477-.L476
	.byte	3,95,1,5,3,9
	.half	.L478-.L477
	.byte	3,4,1,9
	.half	.L484-.L478
	.byte	3,3,1,9
	.half	.L486-.L484
	.byte	3,3,1,9
	.half	.L488-.L486
	.byte	3,3,1,9
	.half	.L490-.L488
	.byte	3,5,1,9
	.half	.L492-.L490
	.byte	3,4,1,9
	.half	.L494-.L492
	.byte	3,3,1,9
	.half	.L496-.L494
	.byte	3,5,1,9
	.half	.L481-.L496
	.byte	3,3,1,9
	.half	.L482-.L481
	.byte	3,5,1,9
	.half	.L499-.L482
	.byte	3,3,1,9
	.half	.L501-.L499
	.byte	3,3,1,9
	.half	.L503-.L501
	.byte	3,3,1,9
	.half	.L505-.L503
	.byte	3,5,1,9
	.half	.L507-.L505
	.byte	3,3,1,9
	.half	.L509-.L507
	.byte	3,5,1,9
	.half	.L511-.L509
	.byte	3,4,1,9
	.half	.L513-.L511
	.byte	3,2,1,9
	.half	.L515-.L513
	.byte	3,4,1,5,16,9
	.half	.L517-.L515
	.byte	3,3,1,5,80,3,5,1,5,18,9
	.half	.L17-.L517
	.byte	3,2,1,5,13,9
	.half	.L16-.L17
	.byte	3,124,1,5,33,9
	.half	.L958-.L16
	.byte	3,1,1,5,10,9
	.half	.L959-.L958
	.byte	3,127,1,5,75,7,9
	.half	.L960-.L959
	.byte	3,2,1,5,1,7,9
	.half	.L18-.L960
	.byte	3,4,1,7,9
	.half	.L199-.L18
	.byte	0,1,1
.L945:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lHwInit')
	.sect	'.debug_ranges'
.L198:
	.word	-1,.L87,0,.L199-.L87,0,0
.L464:
	.word	-1,.L87,.L9-.L87,.L465-.L87,.L466-.L87,.L467-.L87,0,0
.L469:
	.word	-1,.L87,.L467-.L87,.L470-.L87,-1,.L89,0,.L294-.L89,0,0
.L475:
	.word	-1,.L87,.L15-.L87,.L476-.L87,.L477-.L87,.L478-.L87,0,0
.L480:
	.word	-1,.L87,.L476-.L87,.L477-.L87,.L481-.L87,.L482-.L87,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lRead')
	.sect	'.debug_info'
.L200:
	.word	466
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L203,.L202
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lRead',0,1,178,10,13,1,1
	.word	.L93,.L519,.L92
	.byte	4
	.byte	'HwModulePtr',0,1,178,10,36
	.word	.L388,.L520
	.byte	4
	.byte	'Channel',0,1,178,10,67
	.word	.L364,.L521
	.byte	5
	.word	.L522
	.byte	6
	.byte	'BuffPtr',0,1,180,10,19
	.word	.L384,.L523
	.byte	6
	.byte	'Read_Count',0,1,181,10,10
	.word	.L386,.L524
	.byte	6
	.byte	'TempIntLevel',0,1,182,10,9
	.word	.L364,.L525
	.byte	6
	.byte	'UartDatalen',0,1,183,10,9
	.word	.L364,.L526
	.byte	7
	.word	.L527,.L528
	.byte	6
	.byte	'val',0,1,207,10,7
	.word	.L393,.L529
	.byte	0,7
	.word	.L530,.L531
	.byte	6
	.byte	'val',0,1,217,10,7
	.word	.L393,.L532
	.byte	0,7
	.word	.L533,.L29
	.byte	6
	.byte	'val',0,1,250,10,7
	.word	.L393,.L534
	.byte	0,7
	.word	.L29,.L30
	.byte	6
	.byte	'val',0,1,134,11,6
	.word	.L393,.L535
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lRead')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58,15,59
	.byte	15,57,15,73,16,2,6,0,0,7,11,1,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lRead')
	.sect	'.debug_line'
.L202:
	.word	.L962-.L961
.L961:
	.half	3
	.word	.L964-.L963
.L963:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L964:
	.byte	5,30,7,0,5,2
	.word	.L93
	.byte	3,184,10,1,5,32,9
	.half	.L671-.L93
	.byte	3,2,1,5,21,9
	.half	.L965-.L671
	.byte	3,11,1,5,39,3,115,1,5,49,9
	.half	.L672-.L965
	.byte	3,2,1,5,34,3,11,1,5,27,9
	.half	.L25-.L672
	.byte	3,2,1,5,29,9
	.half	.L966-.L25
	.byte	3,1,1,5,56,9
	.half	.L967-.L966
	.byte	1,5,35,3,3,1,5,9,9
	.half	.L674-.L967
	.byte	1,5,45,7,9
	.half	.L968-.L674
	.byte	3,1,1,5,42,9
	.half	.L969-.L968
	.byte	1,5,7,7,9
	.half	.L527-.L969
	.byte	3,2,1,5,32,9
	.half	.L528-.L527
	.byte	3,3,1,5,16,9
	.half	.L673-.L528
	.byte	1,5,18,9
	.half	.L970-.L673
	.byte	3,11,1,5,7,9
	.half	.L530-.L970
	.byte	3,124,1,5,49,9
	.half	.L531-.L530
	.byte	3,6,1,5,7,9
	.half	.L971-.L531
	.byte	3,112,1,5,32,9
	.half	.L26-.L971
	.byte	3,21,1,5,16,9
	.half	.L675-.L26
	.byte	1,5,31,9
	.half	.L972-.L675
	.byte	3,7,1,5,32,9
	.half	.L677-.L972
	.byte	3,3,1,5,31,9
	.half	.L973-.L677
	.byte	3,125,1,5,49,9
	.half	.L678-.L973
	.byte	3,3,1,5,32,9
	.half	.L28-.L678
	.byte	3,1,1,5,51,9
	.half	.L974-.L28
	.byte	1,5,34,9
	.half	.L24-.L974
	.byte	3,87,1,5,31,7,9
	.half	.L975-.L24
	.byte	3,45,1,5,50,9
	.half	.L976-.L975
	.byte	1,5,3,9
	.half	.L679-.L976
	.byte	1,5,5,7,9
	.half	.L977-.L679
	.byte	3,2,1,5,51,7,9
	.half	.L978-.L977
	.byte	3,4,1,5,7,3,1,1,5,5,9
	.half	.L680-.L978
	.byte	3,4,1,5,6,9
	.half	.L29-.L680
	.byte	3,8,1,5,44,9
	.half	.L30-.L29
	.byte	3,5,1,5,1,9
	.half	.L979-.L30
	.byte	3,2,1,7,9
	.half	.L204-.L979
	.byte	0,1,1
.L962:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lRead')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L93,0,.L204-.L93,0,0
.L522:
	.word	-1,.L93,0,.L519-.L93,-1,.L95,0,.L279-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lWrite')
	.sect	'.debug_info'
.L205:
	.word	365
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L208,.L207
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lWrite',0,1,219,13,13,1,1
	.word	.L115,.L536,.L114
	.byte	4
	.byte	'HwModulePtr',0,1,219,13,37
	.word	.L388,.L537
	.byte	4
	.byte	'Channel',0,1,219,13,68
	.word	.L364,.L538
	.byte	5
	.word	.L115,.L536
	.byte	6
	.byte	'BuffPtr',0,1,221,13,19
	.word	.L384,.L539
	.byte	6
	.byte	'Write_Count',0,1,222,13,10
	.word	.L386,.L540
	.byte	6
	.byte	'UartDatalen',0,1,224,13,9
	.word	.L364,.L541
	.byte	5
	.word	.L542,.L543
	.byte	6
	.byte	'val',0,1,255,13,8
	.word	.L393,.L544
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lWrite')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lWrite')
	.sect	'.debug_line'
.L207:
	.word	.L981-.L980
.L980:
	.half	3
	.word	.L983-.L982
.L982:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L983:
	.byte	5,29,7,0,5,2
	.word	.L115
	.byte	3,225,13,1,5,38,9
	.half	.L708-.L115
	.byte	1,5,51,9
	.half	.L709-.L708
	.byte	3,1,1,5,49,1,5,32,9
	.half	.L984-.L709
	.byte	3,2,1,5,3,9
	.half	.L985-.L984
	.byte	1,5,17,7,9
	.half	.L986-.L985
	.byte	3,3,1,5,32,1,5,57,9
	.half	.L37-.L986
	.byte	3,2,1,5,8,9
	.half	.L987-.L37
	.byte	1,5,62,7,9
	.half	.L988-.L987
	.byte	3,3,1,5,78,9
	.half	.L989-.L988
	.byte	1,5,17,9
	.half	.L39-.L989
	.byte	3,5,1,5,22,9
	.half	.L38-.L39
	.byte	3,3,1,5,35,1,5,29,9
	.half	.L42-.L38
	.byte	3,2,1,5,40,9
	.half	.L990-.L42
	.byte	3,1,1,5,67,9
	.half	.L991-.L990
	.byte	1,5,36,3,3,1,5,10,9
	.half	.L711-.L991
	.byte	1,5,40,7,9
	.half	.L992-.L711
	.byte	3,1,1,5,38,9
	.half	.L993-.L992
	.byte	1,5,8,7,9
	.half	.L542-.L993
	.byte	3,3,1,5,19,9
	.half	.L543-.L542
	.byte	3,4,1,5,8,9
	.half	.L710-.L543
	.byte	3,1,1,5,50,9
	.half	.L994-.L710
	.byte	3,6,1,5,33,9
	.half	.L995-.L994
	.byte	3,1,1,5,54,9
	.half	.L996-.L995
	.byte	1,5,20,9
	.half	.L997-.L996
	.byte	3,3,1,5,8,3,113,1,9
	.half	.L43-.L997
	.byte	3,20,1,5,34,9
	.half	.L998-.L43
	.byte	3,10,1,5,33,9
	.half	.L713-.L998
	.byte	3,124,1,5,34,9
	.half	.L999-.L713
	.byte	3,4,1,5,50,9
	.half	.L714-.L999
	.byte	3,124,1,5,33,9
	.half	.L1000-.L714
	.byte	3,1,1,5,54,9
	.half	.L1001-.L1000
	.byte	1,5,35,9
	.half	.L41-.L1001
	.byte	3,91,1,5,44,7,9
	.half	.L1002-.L41
	.byte	3,44,1,5,1,9
	.half	.L1003-.L1002
	.byte	3,2,1,7,9
	.half	.L209-.L1003
	.byte	0,1,1
.L981:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lWrite')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L115,0,.L209-.L115,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lEnableReadInterrupts')
	.sect	'.debug_info'
.L210:
	.word	365
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L213,.L212
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lEnableReadInterrupts',0,1,162,11,13,1,1
	.word	.L97,.L545,.L96
	.byte	4
	.byte	'HwModulePtr',0,1,162,11,52
	.word	.L388,.L546
	.byte	5
	.word	.L97,.L545
	.byte	6
	.word	.L547
	.byte	7
	.byte	'val',0,1,165,11,3
	.word	.L393,.L549
	.byte	0,5
	.word	.L548,.L550
	.byte	7
	.byte	'val',0,1,169,11,3
	.word	.L393,.L551
	.byte	0,5
	.word	.L550,.L552
	.byte	7
	.byte	'val',0,1,173,11,3
	.word	.L393,.L553
	.byte	0,5
	.word	.L552,.L554
	.byte	7
	.byte	'val',0,1,177,11,3
	.word	.L393,.L555
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lEnableReadInterrupts')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,11,1,85,6,0
	.byte	0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lEnableReadInterrupts')
	.sect	'.debug_line'
.L212:
	.word	.L1005-.L1004
.L1004:
	.half	3
	.word	.L1007-.L1006
.L1006:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1007:
	.byte	5,3,7,0,5,2
	.word	.L97
	.byte	3,164,11,1,9
	.half	.L548-.L97
	.byte	3,4,1,9
	.half	.L550-.L548
	.byte	3,4,1,9
	.half	.L552-.L550
	.byte	3,4,1,5,1,9
	.half	.L554-.L552
	.byte	3,3,1,7,9
	.half	.L214-.L554
	.byte	0,1,1
.L1005:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lEnableReadInterrupts')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L97,0,.L214-.L97,0,0
.L547:
	.word	-1,.L97,0,.L548-.L97,-1,.L99,0,.L284-.L99,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lEnableWriteInterrupts')
	.sect	'.debug_info'
.L215:
	.word	286
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L218,.L217
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lEnableWriteInterrupts',0,1,185,14,13,1,1
	.word	.L117,.L556,.L116
	.byte	4
	.byte	'HwModulePtr',0,1,185,14,53
	.word	.L388,.L557
	.byte	4
	.byte	'ApiAccessId',0,1,186,14,68
	.word	.L364,.L558
	.byte	5
	.word	.L117,.L556
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lEnableWriteInterrupts')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lEnableWriteInterrupts')
	.sect	'.debug_line'
.L217:
	.word	.L1009-.L1008
.L1008:
	.half	3
	.word	.L1011-.L1010
.L1010:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1011:
	.byte	5,70,7,0,5,2
	.word	.L117
	.byte	3,207,14,1,5,73,9
	.half	.L1012-.L117
	.byte	1,5,30,1,5,74,9
	.half	.L1013-.L1012
	.byte	3,3,1,5,73,9
	.half	.L1014-.L1013
	.byte	3,1,1,5,32,9
	.half	.L1015-.L1014
	.byte	3,127,1,5,74,9
	.half	.L1016-.L1015
	.byte	3,5,1,5,32,3,127,1,5,1,9
	.half	.L1017-.L1016
	.byte	3,8,1,7,9
	.half	.L219-.L1017
	.byte	0,1,1
.L1009:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lEnableWriteInterrupts')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L117,0,.L219-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lClearWriteInterrupts')
	.sect	'.debug_info'
.L220:
	.word	285
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L223,.L222
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lClearWriteInterrupts',0,1,244,14,13,1,1
	.word	.L119,.L559,.L118
	.byte	4
	.byte	'HwModulePtr',0,1,244,14,52
	.word	.L388,.L560
	.byte	4
	.byte	'ApiAccessId',0,1,245,14,69
	.word	.L364,.L561
	.byte	5
	.word	.L119,.L559
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lClearWriteInterrupts')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lClearWriteInterrupts')
	.sect	'.debug_line'
.L222:
	.word	.L1019-.L1018
.L1018:
	.half	3
	.word	.L1021-.L1020
.L1020:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1021:
	.byte	5,72,7,0,5,2
	.word	.L119
	.byte	3,149,15,1,5,43,9
	.half	.L1022-.L119
	.byte	3,1,1,5,31,3,127,1,5,72,9
	.half	.L1023-.L1022
	.byte	3,3,1,5,42,9
	.half	.L1024-.L1023
	.byte	3,1,1,5,31,3,127,1,5,1,9
	.half	.L1025-.L1024
	.byte	3,9,1,7,9
	.half	.L224-.L1025
	.byte	0,1,1
.L1019:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lClearWriteInterrupts')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L119,0,.L224-.L119,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lClearReadInterrupts')
	.sect	'.debug_info'
.L225:
	.word	284
	.half	3
	.word	.L226
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L228,.L227
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lClearReadInterrupts',0,1,203,11,13,1,1
	.word	.L101,.L562,.L100
	.byte	4
	.byte	'HwModulePtr',0,1,203,11,51
	.word	.L388,.L563
	.byte	4
	.byte	'ApiAccessId',0,1,204,11,69
	.word	.L364,.L564
	.byte	5
	.word	.L101,.L562
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lClearReadInterrupts')
	.sect	'.debug_abbrev'
.L226:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lClearReadInterrupts')
	.sect	'.debug_line'
.L227:
	.word	.L1027-.L1026
.L1026:
	.half	3
	.word	.L1029-.L1028
.L1028:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1029:
	.byte	5,72,7,0,5,2
	.word	.L101
	.byte	3,253,11,1,5,44,9
	.half	.L1030-.L101
	.byte	3,1,1,5,31,3,127,1,5,72,9
	.half	.L1031-.L1030
	.byte	3,4,1,5,44,9
	.half	.L1032-.L1031
	.byte	3,1,1,5,31,3,127,1,5,72,9
	.half	.L1033-.L1032
	.byte	3,4,1,5,44,9
	.half	.L1034-.L1033
	.byte	3,1,1,5,31,3,127,1,5,72,9
	.half	.L1035-.L1034
	.byte	3,4,1,5,44,9
	.half	.L1036-.L1035
	.byte	3,1,1,5,31,3,127,1,5,1,9
	.half	.L1037-.L1036
	.byte	3,9,1,7,9
	.half	.L229-.L1037
	.byte	0,1,1
.L1027:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lClearReadInterrupts')
	.sect	'.debug_ranges'
.L228:
	.word	-1,.L101,0,.L229-.L101,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lHwEnableAscLinTxIntr')
	.sect	'.debug_info'
.L230:
	.word	303
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L233,.L232
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lHwEnableAscLinTxIntr',0,1,233,23,13,1,1
	.word	.L155,.L565,.L154
	.byte	4
	.byte	'HwUnit',0,1,233,23,55
	.word	.L566,.L567
	.byte	5
	.word	.L155,.L565
	.byte	6
	.byte	'Offset',0,1,235,23,10
	.word	.L393,.L568
	.byte	5
	.word	.L569,.L570
	.byte	6
	.byte	'val',0,1,244,23,3
	.word	.L393,.L571
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lHwEnableAscLinTxIntr')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lHwEnableAscLinTxIntr')
	.sect	'.debug_line'
.L232:
	.word	.L1039-.L1038
.L1038:
	.half	3
	.word	.L1041-.L1040
.L1040:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1041:
	.byte	5,27,7,0,5,2
	.word	.L155
	.byte	3,236,23,1,5,3,9
	.half	.L569-.L155
	.byte	3,7,1,5,1,9
	.half	.L570-.L569
	.byte	3,12,1,7,9
	.half	.L234-.L570
	.byte	0,1,1
.L1039:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lHwEnableAscLinTxIntr')
	.sect	'.debug_ranges'
.L233:
	.word	-1,.L155,0,.L234-.L155,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lHwEnableAscLinRxIntr')
	.sect	'.debug_info'
.L235:
	.word	303
	.half	3
	.word	.L236
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L238,.L237
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lHwEnableAscLinRxIntr',0,1,188,23,13,1,1
	.word	.L153,.L572,.L152
	.byte	4
	.byte	'HwUnit',0,1,188,23,55
	.word	.L573,.L574
	.byte	5
	.word	.L153,.L572
	.byte	6
	.byte	'Offset',0,1,190,23,10
	.word	.L393,.L575
	.byte	5
	.word	.L576,.L577
	.byte	6
	.byte	'val',0,1,199,23,3
	.word	.L393,.L578
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lHwEnableAscLinRxIntr')
	.sect	'.debug_abbrev'
.L236:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lHwEnableAscLinRxIntr')
	.sect	'.debug_line'
.L237:
	.word	.L1043-.L1042
.L1042:
	.half	3
	.word	.L1045-.L1044
.L1044:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1045:
	.byte	5,27,7,0,5,2
	.word	.L153
	.byte	3,191,23,1,5,3,9
	.half	.L576-.L153
	.byte	3,7,1,5,1,9
	.half	.L577-.L576
	.byte	3,14,1,7,9
	.half	.L239-.L577
	.byte	0,1,1
.L1043:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lHwEnableAscLinRxIntr')
	.sect	'.debug_ranges'
.L238:
	.word	-1,.L153,0,.L239-.L153,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lHwEnableAscLinErrIntr')
	.sect	'.debug_info'
.L240:
	.word	300
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L243,.L242
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lHwEnableAscLinErrIntr',0,1,142,23,13,1,1
	.word	.L149,.L579,.L148
	.byte	4
	.byte	'HwUnit',0,1,142,23,56
	.word	.L580,.L581
	.byte	5
	.word	.L149,.L579
	.byte	6
	.byte	'Offset',0,1,144,23,10
	.word	.L393,.L582
	.byte	7
	.word	.L583
	.byte	6
	.byte	'val',0,1,153,23,3
	.word	.L393,.L586
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lHwEnableAscLinErrIntr')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,11,1,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lHwEnableAscLinErrIntr')
	.sect	'.debug_line'
.L242:
	.word	.L1047-.L1046
.L1046:
	.half	3
	.word	.L1049-.L1048
.L1048:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1049:
	.byte	5,27,7,0,5,2
	.word	.L149
	.byte	3,145,23,1,5,3,9
	.half	.L584-.L149
	.byte	3,7,1,5,1,9
	.half	.L585-.L584
	.byte	3,13,1,7,9
	.half	.L244-.L585
	.byte	0,1,1
.L1047:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lHwEnableAscLinErrIntr')
	.sect	'.debug_ranges'
.L243:
	.word	-1,.L149,0,.L244-.L149,0,0
.L583:
	.word	-1,.L149,.L584-.L149,.L585-.L149,-1,.L151,0,.L264-.L151,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lHwDisableAscLinTxIntr')
	.sect	'.debug_info'
.L245:
	.word	319
	.half	3
	.word	.L246
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L248,.L247
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lHwDisableAscLinTxIntr',0,1,164,22,13,1,1
	.word	.L145,.L587,.L144
	.byte	4
	.byte	'HwUnit',0,1,164,22,56
	.word	.L588,.L589
	.byte	4
	.byte	'ApiAccessId',0,1,164,22,69
	.word	.L364,.L590
	.byte	5
	.word	.L591
	.byte	6
	.byte	'Offset',0,1,167,22,10
	.word	.L393,.L592
	.byte	6
	.byte	'IntrSrc',0,1,167,22,17
	.word	.L393,.L593
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lHwDisableAscLinTxIntr')
	.sect	'.debug_abbrev'
.L246:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58,15,59
	.byte	15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lHwDisableAscLinTxIntr')
	.sect	'.debug_line'
.L247:
	.word	.L1051-.L1050
.L1050:
	.half	3
	.word	.L1053-.L1052
.L1052:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1053:
	.byte	5,27,7,0,5,2
	.word	.L145
	.byte	3,168,22,1,5,69,9
	.half	.L1054-.L145
	.byte	3,28,1,5,1,9
	.half	.L759-.L1054
	.byte	3,52,1,7,9
	.half	.L249-.L759
	.byte	0,1,1
.L1051:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lHwDisableAscLinTxIntr')
	.sect	'.debug_ranges'
.L248:
	.word	-1,.L145,0,.L249-.L145,0,0
.L591:
	.word	-1,.L145,0,.L587-.L145,-1,.L147,0,.L344-.L147,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lHwDisableAscLinRxIntr')
	.sect	'.debug_info'
.L250:
	.word	319
	.half	3
	.word	.L251
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L253,.L252
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lHwDisableAscLinRxIntr',0,1,186,21,13,1,1
	.word	.L141,.L594,.L140
	.byte	4
	.byte	'HwUnit',0,1,186,21,56
	.word	.L595,.L596
	.byte	4
	.byte	'ApiAccessId',0,1,187,21,69
	.word	.L364,.L597
	.byte	5
	.word	.L598
	.byte	6
	.byte	'Offset',0,1,190,21,10
	.word	.L393,.L599
	.byte	6
	.byte	'IntrSrc',0,1,190,21,17
	.word	.L393,.L600
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lHwDisableAscLinRxIntr')
	.sect	'.debug_abbrev'
.L251:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58,15,59
	.byte	15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lHwDisableAscLinRxIntr')
	.sect	'.debug_line'
.L252:
	.word	.L1056-.L1055
.L1055:
	.half	3
	.word	.L1058-.L1057
.L1057:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1058:
	.byte	5,27,7,0,5,2
	.word	.L141
	.byte	3,191,21,1,5,69,9
	.half	.L1059-.L141
	.byte	3,28,1,5,1,9
	.half	.L757-.L1059
	.byte	3,51,1,7,9
	.half	.L254-.L757
	.byte	0,1,1
.L1056:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lHwDisableAscLinRxIntr')
	.sect	'.debug_ranges'
.L253:
	.word	-1,.L141,0,.L254-.L141,0,0
.L598:
	.word	-1,.L141,0,.L594-.L141,-1,.L143,0,.L339-.L143,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_lHwDisableAscLinErrIntr')
	.sect	'.debug_info'
.L255:
	.word	320
	.half	3
	.word	.L256
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L258,.L257
	.byte	2
	.word	.L156
	.byte	3
	.byte	'Uart_lHwDisableAscLinErrIntr',0,1,207,20,13,1,1
	.word	.L133,.L601,.L132
	.byte	4
	.byte	'HwUnit',0,1,207,20,57
	.word	.L602,.L603
	.byte	4
	.byte	'ApiAccessId',0,1,208,20,69
	.word	.L364,.L604
	.byte	5
	.word	.L605
	.byte	6
	.byte	'Offset',0,1,211,20,10
	.word	.L393,.L606
	.byte	6
	.byte	'IntrSrc',0,1,211,20,17
	.word	.L393,.L607
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_lHwDisableAscLinErrIntr')
	.sect	'.debug_abbrev'
.L256:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58,15,59
	.byte	15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Uart_lHwDisableAscLinErrIntr')
	.sect	'.debug_line'
.L257:
	.word	.L1061-.L1060
.L1060:
	.half	3
	.word	.L1063-.L1062
.L1062:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1063:
	.byte	5,27,7,0,5,2
	.word	.L133
	.byte	3,212,20,1,5,68,9
	.half	.L1064-.L133
	.byte	3,29,1,5,1,9
	.half	.L755-.L1064
	.byte	3,50,1,7,9
	.half	.L259-.L755
	.byte	0,1,1
.L1061:
	.sdecl	'.debug_ranges',debug,cluster('Uart_lHwDisableAscLinErrIntr')
	.sect	'.debug_ranges'
.L258:
	.word	-1,.L133,0,.L259-.L133,0,0
.L605:
	.word	-1,.L133,0,.L601-.L133,-1,.L135,0,.L334-.L135,-1,.L137,0,.L349-.L137,-1,.L139,0,.L269-.L139,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L260:
	.word	208
	.half	3
	.word	.L261
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L263,.L262
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_1',0,1,142,23,13,1
	.word	.L151,.L264,.L150
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L261:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L262:
	.word	.L1066-.L1065
.L1065:
	.half	3
	.word	.L1068-.L1067
.L1067:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1068:
	.byte	5,3,7,0,5,2
	.word	.L151
	.byte	3,152,23,1,9
	.half	.L1069-.L151
	.byte	3,9,1,9
	.half	.L264-.L1069
	.byte	0,1,1,5,3,0,5,2
	.word	.L151
	.byte	3,198,23,1,9
	.half	.L1069-.L151
	.byte	3,10,1,9
	.half	.L760-.L1069
	.byte	3,81,1,7,9
	.half	.L264-.L760
	.byte	0,1,1,5,3,0,5,2
	.word	.L151
	.byte	3,243,23,1,9
	.half	.L1069-.L151
	.byte	3,9,1,9
	.half	.L760-.L1069
	.byte	3,165,127,1,7,9
	.half	.L264-.L760
	.byte	0,1,1
.L1066:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L263:
	.word	-1,.L151,0,.L264-.L151,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L265:
	.word	208
	.half	3
	.word	.L266
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L268,.L267
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_2',0,1,207,20,13,1
	.word	.L139,.L269,.L138
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L266:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L267:
	.word	.L1071-.L1070
.L1070:
	.half	3
	.word	.L1073-.L1072
.L1072:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1073:
	.byte	5,68,7,0,5,2
	.word	.L139
	.byte	3,241,20,1,5,16,9
	.half	.L1074-.L139
	.byte	3,127,1,5,3,9
	.half	.L753-.L1074
	.byte	3,4,1,5,21,7,9
	.half	.L1075-.L753
	.byte	3,42,1,5,10,3,127,1,5,21,9
	.half	.L754-.L1075
	.byte	3,1,1,5,75,9
	.half	.L1076-.L754
	.byte	3,127,1,5,51,9
	.half	.L1077-.L1076
	.byte	3,1,1,5,72,3,126,1,5,3,9
	.half	.L77-.L1077
	.byte	3,88,1,7,9
	.half	.L269-.L77
	.byte	0,1,1,5,69,0,5,2
	.word	.L139
	.byte	3,219,21,1,5,15,9
	.half	.L1074-.L139
	.byte	1,5,3,9
	.half	.L753-.L1074
	.byte	3,3,1,5,22,7,9
	.half	.L1075-.L753
	.byte	3,43,1,5,54,3,126,1,5,22,9
	.half	.L754-.L1075
	.byte	3,2,1,5,51,9
	.half	.L1076-.L754
	.byte	3,127,1,5,52,9
	.half	.L1077-.L1076
	.byte	3,1,1,5,51,3,126,1,5,3,9
	.half	.L77-.L1077
	.byte	3,237,126,1,7,9
	.half	.L269-.L77
	.byte	0,1,1,5,69,0,5,2
	.word	.L139
	.byte	3,196,22,1,5,16,9
	.half	.L1074-.L139
	.byte	1,5,3,9
	.half	.L753-.L1074
	.byte	3,4,1,5,22,7,9
	.half	.L1075-.L753
	.byte	3,43,1,5,54,3,126,1,5,22,9
	.half	.L754-.L1075
	.byte	3,2,1,5,51,9
	.half	.L1076-.L754
	.byte	3,127,1,5,52,9
	.half	.L1077-.L1076
	.byte	3,1,1,5,51,3,126,1,5,3,9
	.half	.L77-.L1077
	.byte	3,131,126,1,7,9
	.half	.L269-.L77
	.byte	0,1,1
.L1071:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L268:
	.word	-1,.L139,0,.L269-.L139,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L270:
	.word	208
	.half	3
	.word	.L271
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L273,.L272
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_3',0,1,223,17,6,1
	.word	.L127,.L274,.L126
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L271:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L272:
	.word	.L1079-.L1078
.L1078:
	.half	3
	.word	.L1081-.L1080
.L1080:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1081:
	.byte	5,6,7,0,5,2
	.word	.L127
	.byte	3,222,17,1,5,30,3,5,1,5,45,9
	.half	.L717-.L127
	.byte	1,5,44,9
	.half	.L1082-.L717
	.byte	1,5,13,9
	.half	.L716-.L1082
	.byte	3,4,1,5,31,9
	.half	.L1083-.L716
	.byte	1,9
	.half	.L274-.L1083
	.byte	0,1,1,5,6,0,5,2
	.word	.L127
	.byte	3,165,18,1,5,31,3,4,1,5,46,9
	.half	.L717-.L127
	.byte	1,5,45,9
	.half	.L1082-.L717
	.byte	1,5,13,9
	.half	.L716-.L1082
	.byte	3,190,127,1,5,31,9
	.half	.L1083-.L716
	.byte	3,199,0,1,9
	.half	.L718-.L1083
	.byte	3,185,127,1,7,9
	.half	.L274-.L718
	.byte	0,1,1
.L1079:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L273:
	.word	-1,.L127,0,.L274-.L127,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_4')
	.sect	'.debug_info'
.L275:
	.word	208
	.half	3
	.word	.L276
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L278,.L277
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_4',0,1,178,10,13,1
	.word	.L95,.L279,.L94
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_4')
	.sect	'.debug_abbrev'
.L276:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_4')
	.sect	'.debug_line'
.L277:
	.word	.L1085-.L1084
.L1084:
	.half	3
	.word	.L1087-.L1086
.L1086:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1087:
	.byte	5,30,7,0,5,2
	.word	.L95
	.byte	3,184,10,1,5,14,1,5,30,9
	.half	.L1088-.L95
	.byte	1,9
	.half	.L279-.L1088
	.byte	0,1,1,5,29,0,5,2
	.word	.L95
	.byte	3,225,13,1,5,14,3,215,124,1,5,29,9
	.half	.L1088-.L95
	.byte	3,169,3,1,5,30,9
	.half	.L1089-.L1088
	.byte	3,215,124,1,7,9
	.half	.L279-.L1089
	.byte	0,1,1,5,25,0,5,2
	.word	.L95
	.byte	3,184,17,1,5,14,3,128,121,1,5,25,9
	.half	.L1088-.L95
	.byte	3,128,7,1,5,30,9
	.half	.L1089-.L1088
	.byte	3,128,121,1,7,9
	.half	.L279-.L1089
	.byte	0,1,1
.L1085:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_4')
	.sect	'.debug_ranges'
.L278:
	.word	-1,.L95,0,.L279-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_5')
	.sect	'.debug_info'
.L280:
	.word	208
	.half	3
	.word	.L281
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L283,.L282
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_5',0,1,162,11,13,1
	.word	.L99,.L284,.L98
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_5')
	.sect	'.debug_abbrev'
.L281:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_5')
	.sect	'.debug_line'
.L282:
	.word	.L1091-.L1090
.L1090:
	.half	3
	.word	.L1093-.L1092
.L1092:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1093:
	.byte	5,3,7,0,5,2
	.word	.L99
	.byte	3,164,11,1,9
	.half	.L1094-.L99
	.byte	3,4,1,9
	.half	.L284-.L1094
	.byte	0,1,1,5,3,0,5,2
	.word	.L99
	.byte	3,168,11,1,9
	.half	.L1094-.L99
	.byte	3,4,1,9
	.half	.L1095-.L1094
	.byte	3,124,1,7,9
	.half	.L284-.L1095
	.byte	0,1,1,5,3,0,5,2
	.word	.L99
	.byte	3,172,11,1,9
	.half	.L1094-.L99
	.byte	3,4,1,9
	.half	.L1095-.L1094
	.byte	3,120,1,7,9
	.half	.L284-.L1095
	.byte	0,1,1,5,32,0,5,2
	.word	.L99
	.byte	3,210,14,1,5,74,9
	.half	.L1094-.L99
	.byte	3,4,1,5,3,9
	.half	.L1095-.L1094
	.byte	3,210,124,1,7,9
	.half	.L284-.L1095
	.byte	0,1,1
.L1091:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_5')
	.sect	'.debug_ranges'
.L283:
	.word	-1,.L99,0,.L284-.L99,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_6')
	.sect	'.debug_info'
.L285:
	.word	208
	.half	3
	.word	.L286
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L288,.L287
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_6',0,1,179,12,17,1
	.word	.L113,.L289,.L112
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_6')
	.sect	'.debug_abbrev'
.L286:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_6')
	.sect	'.debug_line'
.L287:
	.word	.L1097-.L1096
.L1096:
	.half	3
	.word	.L1099-.L1098
.L1098:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1099:
	.byte	5,6,7,0,5,2
	.word	.L113
	.byte	3,172,13,1,9
	.half	.L289-.L113
	.byte	0,1,1,5,73,0,5,2
	.word	.L113
	.byte	3,184,18,1,5,39,9
	.half	.L701-.L113
	.byte	3,1,1,5,32,3,127,1,5,6,9
	.half	.L700-.L701
	.byte	3,244,122,1,7,9
	.half	.L289-.L700
	.byte	0,1,1,5,73,0,5,2
	.word	.L113
	.byte	3,207,18,1,5,39,9
	.half	.L701-.L113
	.byte	3,1,1,5,32,3,127,1,5,6,9
	.half	.L700-.L701
	.byte	3,221,122,1,7,9
	.half	.L289-.L700
	.byte	0,1,1,5,72,0,5,2
	.word	.L113
	.byte	3,199,19,1,5,39,9
	.half	.L701-.L113
	.byte	3,1,1,5,31,3,127,1,5,6,9
	.half	.L700-.L701
	.byte	3,229,121,1,7,9
	.half	.L289-.L700
	.byte	0,1,1
.L1097:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_6')
	.sect	'.debug_ranges'
.L288:
	.word	-1,.L113,0,.L289-.L113,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_7')
	.sect	'.debug_info'
.L290:
	.word	208
	.half	3
	.word	.L291
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L293,.L292
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_7',0,1,197,5,13,1
	.word	.L89,.L294,.L88
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_7')
	.sect	'.debug_abbrev'
.L291:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_7')
	.sect	'.debug_line'
.L292:
	.word	.L1101-.L1100
.L1100:
	.half	3
	.word	.L1103-.L1102
.L1102:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1103:
	.byte	5,3,7,0,5,2
	.word	.L89
	.byte	3,221,5,1,9
	.half	.L294-.L89
	.byte	0,1,1,5,3,0,5,2
	.word	.L89
	.byte	3,187,6,1,9
	.half	.L627-.L89
	.byte	3,162,127,1,7,9
	.half	.L294-.L627
	.byte	0,1,1
.L1101:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_7')
	.sect	'.debug_ranges'
.L293:
	.word	-1,.L89,0,.L294-.L89,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_8')
	.sect	'.debug_info'
.L295:
	.word	208
	.half	3
	.word	.L296
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L298,.L297
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_8',0,1,179,12,17,1
	.word	.L111,.L299,.L110
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_8')
	.sect	'.debug_abbrev'
.L296:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_8')
	.sect	'.debug_line'
.L297:
	.word	.L1105-.L1104
.L1104:
	.half	3
	.word	.L1107-.L1106
.L1106:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1107:
	.byte	5,34,7,0,5,2
	.word	.L111
	.byte	3,253,12,1,9
	.half	.L299-.L111
	.byte	0,1,1,5,26,0,5,2
	.word	.L111
	.byte	3,128,18,1,5,34,9
	.half	.L1108-.L111
	.byte	3,253,122,1,7,9
	.half	.L299-.L1108
	.byte	0,1,1,5,26,0,5,2
	.word	.L111
	.byte	3,226,19,1,5,34,9
	.half	.L1108-.L111
	.byte	3,155,121,1,7,9
	.half	.L299-.L1108
	.byte	0,1,1
.L1105:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_8')
	.sect	'.debug_ranges'
.L298:
	.word	-1,.L111,0,.L299-.L111,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_9')
	.sect	'.debug_info'
.L300:
	.word	208
	.half	3
	.word	.L301
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L303,.L302
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_9',0,1,179,12,17,1
	.word	.L109,.L304,.L108
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_9')
	.sect	'.debug_abbrev'
.L301:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_9')
	.sect	'.debug_line'
.L302:
	.word	.L1110-.L1109
.L1109:
	.half	3
	.word	.L1112-.L1111
.L1111:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1112:
	.byte	5,25,7,0,5,2
	.word	.L109
	.byte	3,137,13,1,9
	.half	.L304-.L109
	.byte	0,1,1,5,24,0,5,2
	.word	.L109
	.byte	3,234,17,1,5,25,9
	.half	.L1113-.L109
	.byte	3,159,123,1,7,9
	.half	.L304-.L1113
	.byte	0,1,1,5,24,0,5,2
	.word	.L109
	.byte	3,143,19,1,5,25,9
	.half	.L1113-.L109
	.byte	3,250,121,1,7,9
	.half	.L304-.L1113
	.byte	0,1,1
.L1110:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_9')
	.sect	'.debug_ranges'
.L303:
	.word	-1,.L109,0,.L304-.L109,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_10')
	.sect	'.debug_info'
.L305:
	.word	209
	.half	3
	.word	.L306
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L308,.L307
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_10',0,1,179,12,17,1
	.word	.L107,.L309,.L106
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_10')
	.sect	'.debug_abbrev'
.L306:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_10')
	.sect	'.debug_line'
.L307:
	.word	.L1115-.L1114
.L1114:
	.half	3
	.word	.L1117-.L1116
.L1116:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1117:
	.byte	5,20,7,0,5,2
	.word	.L107
	.byte	3,139,13,1,5,4,1,9
	.half	.L309-.L107
	.byte	0,1,1,5,21,0,5,2
	.word	.L107
	.byte	3,246,19,1,5,4,3,149,121,1,9
	.half	.L309-.L107
	.byte	0,1,1
.L1115:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_10')
	.sect	'.debug_ranges'
.L308:
	.word	-1,.L107,0,.L309-.L107,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_11')
	.sect	'.debug_info'
.L310:
	.word	209
	.half	3
	.word	.L311
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L313,.L312
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_11',0,1,179,12,17,1
	.word	.L105,.L314,.L104
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_11')
	.sect	'.debug_abbrev'
.L311:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_11')
	.sect	'.debug_line'
.L312:
	.word	.L1119-.L1118
.L1118:
	.half	3
	.word	.L1121-.L1120
.L1120:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1121:
	.byte	5,6,7,0,5,2
	.word	.L105
	.byte	3,176,13,1,9
	.half	.L314-.L105
	.byte	0,1,1,5,77,0,5,2
	.word	.L105
	.byte	3,215,18,1,5,39,9
	.half	.L703-.L105
	.byte	3,1,1,5,35,3,127,1,5,6,9
	.half	.L702-.L703
	.byte	3,217,122,1,7,9
	.half	.L314-.L702
	.byte	0,1,1
.L1119:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_11')
	.sect	'.debug_ranges'
.L313:
	.word	-1,.L105,0,.L314-.L105,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_12')
	.sect	'.debug_info'
.L315:
	.word	209
	.half	3
	.word	.L316
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L318,.L317
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_12',0,1,223,17,6,1
	.word	.L125,.L319,.L124
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_12')
	.sect	'.debug_abbrev'
.L316:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_12')
	.sect	'.debug_line'
.L317:
	.word	.L1123-.L1122
.L1122:
	.half	3
	.word	.L1125-.L1124
.L1124:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1125:
	.byte	5,23,7,0,5,2
	.word	.L125
	.byte	3,239,17,1,5,7,1,9
	.half	.L319-.L125
	.byte	0,1,1,5,38,0,5,2
	.word	.L125
	.byte	3,175,18,1,5,22,1,5,7,9
	.half	.L1126-.L125
	.byte	3,64,1,7,9
	.half	.L319-.L1126
	.byte	0,1,1
.L1123:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_12')
	.sect	'.debug_ranges'
.L318:
	.word	-1,.L125,0,.L319-.L125,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_13')
	.sect	'.debug_info'
.L320:
	.word	209
	.half	3
	.word	.L321
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L323,.L322
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_13',0,1,225,2,6,1
	.word	.L85,.L324,.L84
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_13')
	.sect	'.debug_abbrev'
.L321:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_13')
	.sect	'.debug_line'
.L322:
	.word	.L1128-.L1127
.L1127:
	.half	3
	.word	.L1130-.L1129
.L1129:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1130:
	.byte	5,9,7,0,5,2
	.word	.L85
	.byte	3,183,3,1,9
	.half	.L324-.L85
	.byte	0,1,1,5,5,0,5,2
	.word	.L85
	.byte	3,232,9,1,5,9,9
	.half	.L1131-.L85
	.byte	3,207,121,1,7,9
	.half	.L324-.L1131
	.byte	0,1,1,5,14,0,5,2
	.word	.L85
	.byte	3,184,10,1,5,9,9
	.half	.L1131-.L85
	.byte	3,255,120,1,7,9
	.half	.L324-.L1131
	.byte	0,1,1,5,13,0,5,2
	.word	.L85
	.byte	3,225,13,1,5,9,9
	.half	.L1131-.L85
	.byte	3,214,117,1,7,9
	.half	.L324-.L1131
	.byte	0,1,1,5,9,0,5,2
	.word	.L85
	.byte	3,184,17,1,9
	.half	.L1131-.L85
	.byte	3,255,113,1,7,9
	.half	.L324-.L1131
	.byte	0,1,1,5,4,0,5,2
	.word	.L85
	.byte	3,139,13,1,5,9,9
	.half	.L1131-.L85
	.byte	3,172,118,1,7,9
	.half	.L324-.L1131
	.byte	0,1,1,5,5,0,5,2
	.word	.L85
	.byte	3,246,19,1,5,9,9
	.half	.L1131-.L85
	.byte	3,193,111,1,7,9
	.half	.L324-.L1131
	.byte	0,1,1
.L1128:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_13')
	.sect	'.debug_ranges'
.L323:
	.word	-1,.L85,0,.L324-.L85,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_14')
	.sect	'.debug_info'
.L325:
	.word	209
	.half	3
	.word	.L326
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L328,.L327
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_14',0,1,225,2,6,1
	.word	.L83,.L329,.L82
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_14')
	.sect	'.debug_abbrev'
.L326:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_14')
	.sect	'.debug_line'
.L327:
	.word	.L1133-.L1132
.L1132:
	.half	3
	.word	.L1135-.L1134
.L1134:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1135:
	.byte	5,9,7,0,5,2
	.word	.L83
	.byte	3,186,3,1,9
	.half	.L329-.L83
	.byte	0,1,1,5,13,0,5,2
	.word	.L83
	.byte	3,231,17,1,5,9,9
	.half	.L1136-.L83
	.byte	3,211,113,1,7,9
	.half	.L329-.L1136
	.byte	0,1,1,5,13,0,5,2
	.word	.L83
	.byte	3,174,18,1,5,9,9
	.half	.L1136-.L83
	.byte	3,140,113,1,7,9
	.half	.L329-.L1136
	.byte	0,1,1,5,19,0,5,2
	.word	.L83
	.byte	3,139,19,1,5,9,9
	.half	.L1136-.L83
	.byte	3,175,112,1,7,9
	.half	.L329-.L1136
	.byte	0,1,1
.L1133:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_14')
	.sect	'.debug_ranges'
.L328:
	.word	-1,.L83,0,.L329-.L83,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_15')
	.sect	'.debug_info'
.L330:
	.word	209
	.half	3
	.word	.L331
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L333,.L332
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_15',0,1,207,20,13,1
	.word	.L135,.L334,.L134
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_15')
	.sect	'.debug_abbrev'
.L331:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_15')
	.sect	'.debug_line'
.L332:
	.word	.L1138-.L1137
.L1137:
	.half	3
	.word	.L1140-.L1139
.L1139:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1140:
	.byte	5,27,7,0,5,2
	.word	.L135
	.byte	3,212,20,1,5,43,9
	.half	.L752-.L135
	.byte	3,29,1,9
	.half	.L334-.L752
	.byte	0,1,1,5,27,0,5,2
	.word	.L135
	.byte	3,212,20,1,5,3,9
	.half	.L752-.L135
	.byte	3,196,2,1,5,43,9
	.half	.L1141-.L752
	.byte	3,217,125,1,7,9
	.half	.L334-.L1141
	.byte	0,1,1
.L1138:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_15')
	.sect	'.debug_ranges'
.L333:
	.word	-1,.L135,0,.L334-.L135,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_16')
	.sect	'.debug_info'
.L335:
	.word	209
	.half	3
	.word	.L336
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L338,.L337
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_16',0,1,186,21,13,1
	.word	.L143,.L339,.L142
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_16')
	.sect	'.debug_abbrev'
.L336:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_16')
	.sect	'.debug_line'
.L337:
	.word	.L1143-.L1142
.L1142:
	.half	3
	.word	.L1145-.L1144
.L1144:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1145:
	.byte	5,27,7,0,5,2
	.word	.L143
	.byte	3,191,21,1,5,45,9
	.half	.L756-.L143
	.byte	3,28,1,9
	.half	.L339-.L756
	.byte	0,1,1,5,27,0,5,2
	.word	.L143
	.byte	3,191,21,1,5,3,9
	.half	.L756-.L143
	.byte	3,135,2,1,5,45,9
	.half	.L1146-.L756
	.byte	3,149,126,1,7,9
	.half	.L339-.L1146
	.byte	0,1,1
.L1143:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_16')
	.sect	'.debug_ranges'
.L338:
	.word	-1,.L143,0,.L339-.L143,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_17')
	.sect	'.debug_info'
.L340:
	.word	209
	.half	3
	.word	.L341
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L343,.L342
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_17',0,1,164,22,13,1
	.word	.L147,.L344,.L146
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_17')
	.sect	'.debug_abbrev'
.L341:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_17')
	.sect	'.debug_line'
.L342:
	.word	.L1148-.L1147
.L1147:
	.half	3
	.word	.L1150-.L1149
.L1149:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1150:
	.byte	5,27,7,0,5,2
	.word	.L147
	.byte	3,168,22,1,5,46,9
	.half	.L758-.L147
	.byte	3,28,1,9
	.half	.L344-.L758
	.byte	0,1,1,5,27,0,5,2
	.word	.L147
	.byte	3,168,22,1,5,3,9
	.half	.L758-.L147
	.byte	3,203,1,1,5,46,9
	.half	.L1151-.L758
	.byte	3,209,126,1,7,9
	.half	.L344-.L1151
	.byte	0,1,1
.L1148:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_17')
	.sect	'.debug_ranges'
.L343:
	.word	-1,.L147,0,.L344-.L147,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_18')
	.sect	'.debug_info'
.L345:
	.word	209
	.half	3
	.word	.L346
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L348,.L347
	.byte	2
	.word	.L156
	.byte	3
	.byte	'.cocofun_18',0,1,207,20,13,1
	.word	.L137,.L349,.L136
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_18')
	.sect	'.debug_abbrev'
.L346:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_18')
	.sect	'.debug_line'
.L347:
	.word	.L1153-.L1152
.L1152:
	.half	3
	.word	.L1155-.L1154
.L1154:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Uart.c',0,0,0,0,0
.L1155:
	.byte	5,27,7,0,5,2
	.word	.L137
	.byte	3,212,20,1,5,43,3,29,1,9
	.half	.L349-.L137
	.byte	0,1,1,5,27,0,5,2
	.word	.L137
	.byte	3,145,23,1,5,3,3,7,1,5,43,9
	.half	.L751-.L137
	.byte	3,217,125,1,7,9
	.half	.L349-.L751
	.byte	0,1,1,5,27,0,5,2
	.word	.L137
	.byte	3,191,21,1,5,45,3,28,1,5,43,9
	.half	.L751-.L137
	.byte	3,150,127,1,7,9
	.half	.L349-.L751
	.byte	0,1,1,5,27,0,5,2
	.word	.L137
	.byte	3,191,23,1,5,3,3,7,1,5,43,9
	.half	.L751-.L137
	.byte	3,171,125,1,7,9
	.half	.L349-.L751
	.byte	0,1,1,5,27,0,5,2
	.word	.L137
	.byte	3,168,22,1,5,46,3,28,1,5,43,9
	.half	.L751-.L137
	.byte	3,173,126,1,7,9
	.half	.L349-.L751
	.byte	0,1,1,5,27,0,5,2
	.word	.L137
	.byte	3,236,23,1,5,3,3,7,1,5,43,9
	.half	.L751-.L137
	.byte	3,254,124,1,7,9
	.half	.L349-.L751
	.byte	0,1,1
.L1153:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_18')
	.sect	'.debug_ranges'
.L348:
	.word	-1,.L137,0,.L349-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_kConfigPtr')
	.sect	'.debug_info'
.L350:
	.word	202
	.half	3
	.word	.L351
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L156
	.byte	3
	.byte	'Uart_kConfigPtr',0,1,239,1,31
	.word	.L359
	.byte	5,3
	.word	Uart_kConfigPtr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_kConfigPtr')
	.sect	'.debug_abbrev'
.L351:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_BusChannelMap')
	.sect	'.debug_info'
.L352:
	.word	205
	.half	3
	.word	.L353
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L156
	.byte	3
	.byte	'Uart_BusChannelMap',0,1,132,2,27
	.word	.L608
	.byte	5,3
	.word	Uart_BusChannelMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_BusChannelMap')
	.sect	'.debug_abbrev'
.L353:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_ChannelInfo')
	.sect	'.debug_info'
.L354:
	.word	203
	.half	3
	.word	.L355
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L156
	.byte	3
	.byte	'Uart_ChannelInfo',0,1,157,2,29
	.word	.L609
	.byte	5,3
	.word	Uart_ChannelInfo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_ChannelInfo')
	.sect	'.debug_abbrev'
.L355:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_ChLock')
	.sect	'.debug_info'
.L356:
	.word	198
	.half	3
	.word	.L357
	.byte	4,1
	.byte	'..\\mcal_src\\Uart.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L156
	.byte	3
	.byte	'Uart_ChLock',0,1,171,2,15
	.word	.L610
	.byte	5,3
	.word	Uart_ChLock
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_ChLock')
	.sect	'.debug_abbrev'
.L357:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L150:
	.word	-1,.L151,0,.L264-.L151
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_10')
	.sect	'.debug_loc'
.L106:
	.word	-1,.L107,0,.L309-.L107
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_11')
	.sect	'.debug_loc'
.L104:
	.word	-1,.L105,0,.L314-.L105
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_12')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L125,0,.L319-.L125
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_13')
	.sect	'.debug_loc'
.L84:
	.word	-1,.L85,0,.L324-.L85
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_14')
	.sect	'.debug_loc'
.L82:
	.word	-1,.L83,0,.L329-.L83
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_15')
	.sect	'.debug_loc'
.L134:
	.word	-1,.L135,0,.L334-.L135
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_16')
	.sect	'.debug_loc'
.L142:
	.word	-1,.L143,0,.L339-.L143
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_17')
	.sect	'.debug_loc'
.L146:
	.word	-1,.L147,0,.L344-.L147
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_18')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L137,0,.L349-.L137
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L138:
	.word	-1,.L139,0,.L269-.L139
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L126:
	.word	-1,.L127,0,.L274-.L127
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_4')
	.sect	'.debug_loc'
.L94:
	.word	-1,.L95,0,.L279-.L95
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_5')
	.sect	'.debug_loc'
.L98:
	.word	-1,.L99,0,.L284-.L99
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_6')
	.sect	'.debug_loc'
.L112:
	.word	-1,.L113,0,.L289-.L113
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_7')
	.sect	'.debug_loc'
.L88:
	.word	-1,.L89,0,.L294-.L89
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_8')
	.sect	'.debug_loc'
.L110:
	.word	-1,.L111,0,.L299-.L111
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_9')
	.sect	'.debug_loc'
.L108:
	.word	-1,.L109,0,.L304-.L109
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_GetStatus')
	.sect	'.debug_loc'
.L436:
	.word	-1,.L121,.L85-.L121,.L324-.L121
	.half	5
	.byte	144,34,157,32,0
	.word	.L95-.L121,.L279-.L121
	.half	5
	.byte	144,34,157,32,0
	.word	.L715-.L121,.L435-.L121
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L437:
	.word	0,0
.L120:
	.word	-1,.L121,0,.L435-.L121
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_Init')
	.sect	'.debug_loc'
.L366:
	.word	-1,.L81,.L85-.L81,.L324-.L81
	.half	5
	.byte	144,37,157,32,0
	.word	.L614-.L81,.L329-.L81
	.half	5
	.byte	144,37,157,32,0
	.word	.L615-.L81,.L616-.L81
	.half	5
	.byte	144,37,157,32,0
	.word	.L2-.L81,.L358-.L81
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L363:
	.word	-1,.L81,.L614-.L81,.L329-.L81
	.half	1
	.byte	109
	.word	.L617-.L81,.L2-.L81
	.half	1
	.byte	109
	.word	.L622-.L81,.L623-.L81
	.half	1
	.byte	100
	.word	0,0
.L368:
	.word	-1,.L81,.L85-.L81,.L324-.L81
	.half	5
	.byte	144,36,157,32,32
	.word	.L614-.L81,.L329-.L81
	.half	5
	.byte	144,36,157,32,32
	.word	.L611-.L81,.L618-.L81
	.half	5
	.byte	144,36,157,32,32
	.word	.L5-.L81,.L358-.L81
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L360:
	.word	-1,.L81,.L85-.L81,.L324-.L81
	.half	1
	.byte	100
	.word	.L85-.L81,.L324-.L81
	.half	2
	.byte	145,120
	.word	0,.L3-.L81
	.half	1
	.byte	100
	.word	.L612-.L81,.L613-.L81
	.half	1
	.byte	98
	.word	.L614-.L81,.L329-.L81
	.half	2
	.byte	145,120
	.word	.L615-.L81,.L358-.L81
	.half	2
	.byte	145,120
	.word	.L625-.L81,.L6-.L81
	.half	1
	.byte	98
	.word	0,0
.L373:
	.word	0,0
.L367:
	.word	-1,.L81,.L85-.L81,.L324-.L81
	.half	5
	.byte	144,36,157,32,0
	.word	.L614-.L81,.L329-.L81
	.half	5
	.byte	144,36,157,32,0
	.word	.L619-.L81,.L358-.L81
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L380:
	.word	-1,.L81,.L614-.L81,.L329-.L81
	.half	5
	.byte	144,39,157,32,32
	.word	.L620-.L81,.L621-.L81
	.half	5
	.byte	144,39,157,32,32
	.word	.L4-.L81,.L624-.L81
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L365:
	.word	-1,.L81,.L614-.L81,.L329-.L81
	.half	5
	.byte	144,37,157,32,32
	.word	.L370-.L81,.L2-.L81
	.half	5
	.byte	144,37,157,32,32
	.word	.L622-.L81,.L623-.L81
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L80:
	.word	-1,.L81,0,.L611-.L81
	.half	2
	.byte	138,0
	.word	.L611-.L81,.L358-.L81
	.half	2
	.byte	138,8
	.word	.L358-.L81,.L358-.L81
	.half	2
	.byte	138,0
	.word	0,0
.L375:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_IsrError')
	.sect	'.debug_loc'
.L455:
	.word	-1,.L131,.L109-.L131,.L304-.L131
	.half	5
	.byte	144,36,157,32,0
	.word	.L743-.L131,.L744-.L131
	.half	5
	.byte	144,36,157,32,0
	.word	.L113-.L131,.L289-.L131
	.half	5
	.byte	144,36,157,32,0
	.word	.L111-.L131,.L299-.L131
	.half	5
	.byte	144,36,157,32,0
	.word	.L107-.L131,.L689-.L131
	.half	5
	.byte	144,36,157,32,0
	.word	.L85-.L131,.L324-.L131
	.half	5
	.byte	144,36,157,32,0
	.word	.L747-.L131,.L449-.L131
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L452:
	.word	-1,.L131,.L746-.L131,.L744-.L131
	.half	1
	.byte	108
	.word	.L113-.L131,.L289-.L131
	.half	1
	.byte	108
	.word	.L111-.L131,.L299-.L131
	.half	1
	.byte	108
	.word	.L107-.L131,.L689-.L131
	.half	1
	.byte	108
	.word	.L85-.L131,.L324-.L131
	.half	1
	.byte	108
	.word	.L747-.L131,.L449-.L131
	.half	1
	.byte	108
	.word	0,0
.L454:
	.word	-1,.L131,.L614-.L131,.L329-.L131
	.half	5
	.byte	144,36,157,32,32
	.word	.L109-.L131,.L304-.L131
	.half	5
	.byte	144,36,157,32,32
	.word	.L745-.L131,.L744-.L131
	.half	5
	.byte	144,36,157,32,32
	.word	.L113-.L131,.L289-.L131
	.half	5
	.byte	144,36,157,32,32
	.word	.L748-.L131,.L59-.L131
	.half	5
	.byte	144,34,157,32,32
	.word	.L111-.L131,.L299-.L131
	.half	5
	.byte	144,36,157,32,32
	.word	.L107-.L131,.L689-.L131
	.half	5
	.byte	144,36,157,32,32
	.word	.L85-.L131,.L324-.L131
	.half	5
	.byte	144,36,157,32,32
	.word	.L747-.L131,.L449-.L131
	.half	5
	.byte	144,36,157,32,32
	.word	.L750-.L131,.L75-.L131
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L451:
	.word	-1,.L131,.L614-.L131,.L329-.L131
	.half	1
	.byte	111
	.word	.L109-.L131,.L304-.L131
	.half	1
	.byte	111
	.word	.L745-.L131,.L744-.L131
	.half	1
	.byte	111
	.word	.L113-.L131,.L289-.L131
	.half	1
	.byte	111
	.word	.L111-.L131,.L299-.L131
	.half	1
	.byte	111
	.word	.L747-.L131,.L749-.L131
	.half	1
	.byte	111
	.word	0,0
.L450:
	.word	-1,.L131,.L614-.L131,.L329-.L131
	.half	5
	.byte	144,34,157,32,0
	.word	.L109-.L131,.L304-.L131
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L744-.L131
	.half	5
	.byte	144,34,157,32,0
	.word	.L113-.L131,.L289-.L131
	.half	5
	.byte	144,34,157,32,0
	.word	.L747-.L131,.L59-.L131
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L130:
	.word	-1,.L131,0,.L449-.L131
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_IsrReceive')
	.sect	'.debug_loc'
.L443:
	.word	-1,.L123,.L718-.L123,.L274-.L123
	.half	5
	.byte	144,36,157,32,32
	.word	.L109-.L123,.L304-.L123
	.half	5
	.byte	144,36,157,32,32
	.word	.L720-.L123,.L722-.L123
	.half	5
	.byte	144,34,157,32,0
	.word	.L125-.L123,.L319-.L123
	.half	5
	.byte	144,36,157,32,32
	.word	.L111-.L123,.L299-.L123
	.half	5
	.byte	144,36,157,32,32
	.word	.L719-.L123,.L438-.L123
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L441:
	.word	-1,.L123,.L125-.L123,.L319-.L123
	.half	1
	.byte	108
	.word	.L111-.L123,.L299-.L123
	.half	1
	.byte	108
	.word	.L727-.L123,.L438-.L123
	.half	1
	.byte	108
	.word	0,0
.L442:
	.word	-1,.L123,.L83-.L123,.L329-.L123
	.half	1
	.byte	111
	.word	.L716-.L123,.L274-.L123
	.half	1
	.byte	111
	.word	.L109-.L123,.L304-.L123
	.half	1
	.byte	111
	.word	.L721-.L123,.L722-.L123
	.half	1
	.byte	100
	.word	.L125-.L123,.L319-.L123
	.half	1
	.byte	111
	.word	.L723-.L123,.L724-.L123
	.half	1
	.byte	100
	.word	.L111-.L123,.L299-.L123
	.half	1
	.byte	111
	.word	.L719-.L123,.L728-.L123
	.half	1
	.byte	111
	.word	0,0
.L439:
	.word	-1,.L123,.L83-.L123,.L329-.L123
	.half	5
	.byte	144,36,157,32,0
	.word	.L83-.L123,.L329-.L123
	.half	5
	.byte	144,34,157,32,0
	.word	.L717-.L123,.L274-.L123
	.half	5
	.byte	144,36,157,32,0
	.word	.L127-.L123,.L274-.L123
	.half	5
	.byte	144,34,157,32,0
	.word	.L109-.L123,.L304-.L123
	.half	5
	.byte	144,36,157,32,0
	.word	.L109-.L123,.L304-.L123
	.half	5
	.byte	144,34,157,32,0
	.word	.L719-.L123,.L720-.L123
	.half	5
	.byte	144,34,157,32,0
	.word	.L125-.L123,.L319-.L123
	.half	5
	.byte	144,36,157,32,0
	.word	.L725-.L123,.L726-.L123
	.half	5
	.byte	144,34,157,32,0
	.word	.L111-.L123,.L299-.L123
	.half	5
	.byte	144,36,157,32,0
	.word	.L719-.L123,.L438-.L123
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L122:
	.word	-1,.L123,0,.L438-.L123
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_IsrTransmit')
	.sect	'.debug_loc'
.L448:
	.word	-1,.L129,.L718-.L129,.L274-.L129
	.half	5
	.byte	144,36,157,32,32
	.word	.L125-.L129,.L319-.L129
	.half	5
	.byte	144,36,157,32,32
	.word	.L113-.L129,.L289-.L129
	.half	5
	.byte	144,36,157,32,32
	.word	.L105-.L129,.L314-.L129
	.half	5
	.byte	144,36,157,32,32
	.word	.L731-.L129,.L444-.L129
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L446:
	.word	-1,.L129,.L83-.L129,.L329-.L129
	.half	1
	.byte	111
	.word	.L716-.L129,.L274-.L129
	.half	1
	.byte	111
	.word	.L125-.L129,.L319-.L129
	.half	1
	.byte	111
	.word	.L113-.L129,.L289-.L129
	.half	1
	.byte	111
	.word	.L731-.L129,.L732-.L129
	.half	1
	.byte	111
	.word	.L733-.L129,.L734-.L129
	.half	1
	.byte	100
	.word	.L105-.L129,.L314-.L129
	.half	1
	.byte	111
	.word	.L55-.L129,.L444-.L129
	.half	1
	.byte	111
	.word	.L737-.L129,.L738-.L129
	.half	1
	.byte	100
	.word	.L739-.L129,.L740-.L129
	.half	1
	.byte	100
	.word	.L741-.L129,.L742-.L129
	.half	1
	.byte	100
	.word	0,0
.L445:
	.word	-1,.L129,.L83-.L129,.L329-.L129
	.half	5
	.byte	144,36,157,32,0
	.word	.L83-.L129,.L329-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	.L127-.L129,.L274-.L129
	.half	5
	.byte	144,36,157,32,0
	.word	.L127-.L129,.L274-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	.L125-.L129,.L319-.L129
	.half	5
	.byte	144,36,157,32,0
	.word	.L125-.L129,.L319-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	.L113-.L129,.L289-.L129
	.half	5
	.byte	144,36,157,32,0
	.word	.L113-.L129,.L289-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	.L731-.L129,.L733-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	.L735-.L129,.L736-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	.L105-.L129,.L314-.L129
	.half	5
	.byte	144,36,157,32,0
	.word	.L105-.L129,.L314-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	.L731-.L129,.L444-.L129
	.half	5
	.byte	144,36,157,32,0
	.word	.L55-.L129,.L737-.L129
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L447:
	.word	-1,.L129,.L729-.L129,.L730-.L129
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L128:
	.word	-1,.L129,0,.L444-.L129
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_Read')
	.sect	'.debug_loc'
.L394:
	.word	-1,.L91,.L663-.L91,.L324-.L91
	.half	5
	.byte	144,33,157,32,0
	.word	.L659-.L91,.L664-.L91
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L383:
	.word	-1,.L91,0,.L659-.L91
	.half	5
	.byte	144,34,157,32,0
	.word	.L661-.L91,.L662-.L91
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L389:
	.word	-1,.L91,.L395-.L91,.L20-.L91
	.half	1
	.byte	111
	.word	.L402-.L91,.L664-.L91
	.half	1
	.byte	100
	.word	0,0
.L391:
	.word	-1,.L91,.L667-.L91,.L20-.L91
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L385:
	.word	-1,.L91,0,.L660-.L91
	.half	1
	.byte	100
	.word	.L663-.L91,.L324-.L91
	.half	1
	.byte	108
	.word	.L665-.L91,.L382-.L91
	.half	1
	.byte	108
	.word	0,0
.L390:
	.word	-1,.L91,.L663-.L91,.L324-.L91
	.half	5
	.byte	144,36,157,32,32
	.word	.L665-.L91,.L382-.L91
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L387:
	.word	-1,.L91,0,.L659-.L91
	.half	5
	.byte	144,34,157,32,32
	.word	.L663-.L91,.L324-.L91
	.half	5
	.byte	144,36,157,32,0
	.word	.L666-.L91,.L667-.L91
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L392:
	.word	-1,.L91,.L398-.L91,.L21-.L91
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L90:
	.word	-1,.L91,0,.L382-.L91
	.half	2
	.byte	138,0
	.word	0,0
.L401:
	.word	-1,.L91,.L22-.L91,.L669-.L91
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L406:
	.word	-1,.L91,.L670-.L91,.L20-.L91
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L404:
	.word	-1,.L91,.L669-.L91,.L670-.L91
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L397:
	.word	-1,.L91,.L396-.L91,.L668-.L91
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L399:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_Write')
	.sect	'.debug_loc'
.L416:
	.word	-1,.L103,.L686-.L103,.L32-.L103
	.half	5
	.byte	144,33,157,32,0
	.word	.L109-.L103,.L304-.L103
	.half	5
	.byte	144,33,157,32,0
	.word	.L107-.L103,.L689-.L103
	.half	5
	.byte	144,33,157,32,0
	.word	.L663-.L103,.L324-.L103
	.half	5
	.byte	144,33,157,32,0
	.word	.L690-.L103,.L691-.L103
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L408:
	.word	-1,.L103,.L684-.L103,.L299-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	.L684-.L103,.L299-.L103
	.half	5
	.byte	144,34,157,32,0
	.word	.L685-.L103,.L32-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	0,.L686-.L103
	.half	5
	.byte	144,34,157,32,0
	.word	.L109-.L103,.L304-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	.L107-.L103,.L689-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	.L663-.L103,.L324-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	.L690-.L103,.L427-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	.L694-.L103,.L691-.L103
	.half	5
	.byte	144,34,157,32,0
	.word	.L113-.L103,.L700-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	.L105-.L103,.L702-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	.L430-.L103,.L407-.L103
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L412:
	.word	-1,.L103,.L107-.L103,.L689-.L103
	.half	1
	.byte	98
	.word	.L663-.L103,.L324-.L103
	.half	1
	.byte	98
	.word	.L692-.L103,.L691-.L103
	.half	1
	.byte	98
	.word	0,0
.L413:
	.word	-1,.L103,.L417-.L103,.L427-.L103
	.half	1
	.byte	111
	.word	.L422-.L103,.L691-.L103
	.half	1
	.byte	100
	.word	.L113-.L103,.L700-.L103
	.half	1
	.byte	111
	.word	.L105-.L103,.L702-.L103
	.half	1
	.byte	111
	.word	.L430-.L103,.L33-.L103
	.half	1
	.byte	111
	.word	.L705-.L103,.L706-.L103
	.half	1
	.byte	100
	.word	0,0
.L415:
	.word	-1,.L103,.L695-.L103,.L427-.L103
	.half	5
	.byte	144,37,157,32,32
	.word	.L113-.L103,.L700-.L103
	.half	5
	.byte	144,37,157,32,32
	.word	.L105-.L103,.L702-.L103
	.half	5
	.byte	144,37,157,32,32
	.word	.L430-.L103,.L33-.L103
	.half	5
	.byte	144,37,157,32,32
	.word	0,0
.L409:
	.word	-1,.L103,.L684-.L103,.L299-.L103
	.half	1
	.byte	108
	.word	.L684-.L103,.L299-.L103
	.half	1
	.byte	100
	.word	.L685-.L103,.L32-.L103
	.half	1
	.byte	108
	.word	0,.L687-.L103
	.half	1
	.byte	100
	.word	.L109-.L103,.L304-.L103
	.half	1
	.byte	108
	.word	.L107-.L103,.L689-.L103
	.half	1
	.byte	108
	.word	.L663-.L103,.L324-.L103
	.half	1
	.byte	108
	.word	.L690-.L103,.L693-.L103
	.half	1
	.byte	108
	.word	0,0
.L414:
	.word	-1,.L103,.L684-.L103,.L299-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	.L688-.L103,.L32-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	.L109-.L103,.L304-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	.L107-.L103,.L689-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	.L663-.L103,.L324-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	.L690-.L103,.L427-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	.L113-.L103,.L700-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	.L105-.L103,.L702-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	.L430-.L103,.L407-.L103
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L410:
	.word	-1,.L103,.L684-.L103,.L299-.L103
	.half	5
	.byte	144,34,157,32,32
	.word	.L684-.L103,.L299-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	0,.L686-.L103
	.half	5
	.byte	144,34,157,32,32
	.word	.L688-.L103,.L32-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	.L109-.L103,.L304-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	.L107-.L103,.L689-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	.L663-.L103,.L324-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	.L690-.L103,.L427-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	.L113-.L103,.L700-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	.L105-.L103,.L702-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	.L430-.L103,.L407-.L103
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L102:
	.word	-1,.L103,0,.L407-.L103
	.half	2
	.byte	138,0
	.word	0,0
.L421:
	.word	-1,.L103,.L697-.L103,.L698-.L103
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L424:
	.word	-1,.L103,.L699-.L103,.L427-.L103
	.half	5
	.byte	144,39,157,32,32
	.word	.L113-.L103,.L701-.L103
	.half	5
	.byte	144,39,157,32,32
	.word	.L34-.L103,.L35-.L103
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L433:
	.word	-1,.L103,.L707-.L103,.L33-.L103
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L419:
	.word	-1,.L103,.L418-.L103,.L696-.L103
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L431:
	.word	-1,.L103,.L430-.L103,.L704-.L103
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L428:
	.word	-1,.L103,.L105-.L103,.L703-.L103
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lClearReadInterrupts')
	.sect	'.debug_loc'
.L564:
	.word	-1,.L101,0,.L562-.L101
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L563:
	.word	-1,.L101,0,.L562-.L101
	.half	1
	.byte	100
	.word	0,0
.L100:
	.word	-1,.L101,0,.L562-.L101
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lClearWriteInterrupts')
	.sect	'.debug_loc'
.L561:
	.word	-1,.L119,0,.L559-.L119
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L560:
	.word	-1,.L119,0,.L559-.L119
	.half	1
	.byte	100
	.word	0,0
.L118:
	.word	-1,.L119,0,.L559-.L119
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lEnableReadInterrupts')
	.sect	'.debug_loc'
.L546:
	.word	-1,.L97,.L99-.L97,.L284-.L97
	.half	1
	.byte	100
	.word	0,.L545-.L97
	.half	1
	.byte	100
	.word	0,0
.L96:
	.word	-1,.L97,0,.L545-.L97
	.half	2
	.byte	138,0
	.word	0,0
.L549:
	.word	-1,.L97,.L681-.L97,.L548-.L97
	.half	5
	.byte	144,39,157,32,32
	.word	.L99-.L97,.L284-.L97
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L551:
	.word	-1,.L97,.L682-.L97,.L550-.L97
	.half	5
	.byte	144,39,157,32,32
	.word	.L99-.L97,.L284-.L97
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L553:
	.word	-1,.L97,.L683-.L97,.L552-.L97
	.half	5
	.byte	144,39,157,32,32
	.word	.L99-.L97,.L284-.L97
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L555:
	.word	-1,.L97,.L554-.L97,.L545-.L97
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lEnableWriteInterrupts')
	.sect	'.debug_loc'
.L558:
	.word	-1,.L117,.L99-.L117,.L284-.L117
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L556-.L117
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L557:
	.word	-1,.L117,.L99-.L117,.L284-.L117
	.half	1
	.byte	100
	.word	0,.L556-.L117
	.half	1
	.byte	100
	.word	0,0
.L116:
	.word	-1,.L117,0,.L556-.L117
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lHwDisableAscLinErrIntr')
	.sect	'.debug_loc'
.L604:
	.word	-1,.L133,.L137-.L133,.L751-.L133
	.half	5
	.byte	144,34,157,32,32
	.word	.L752-.L133,.L334-.L133
	.half	5
	.byte	144,34,157,32,32
	.word	.L139-.L133,.L77-.L133
	.half	5
	.byte	144,34,157,32,32
	.word	.L755-.L133,.L601-.L133
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L603:
	.word	-1,.L133,.L137-.L133,.L751-.L133
	.half	5
	.byte	144,34,157,32,0
	.word	.L752-.L133,.L334-.L133
	.half	5
	.byte	144,34,157,32,0
	.word	.L139-.L133,.L77-.L133
	.half	5
	.byte	144,34,157,32,0
	.word	.L755-.L133,.L601-.L133
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L607:
	.word	-1,.L133,.L753-.L133,.L754-.L133
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L606:
	.word	-1,.L133,.L752-.L133,.L334-.L133
	.half	5
	.byte	144,39,157,32,32
	.word	.L139-.L133,.L753-.L133
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L132:
	.word	-1,.L133,0,.L601-.L133
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lHwDisableAscLinRxIntr')
	.sect	'.debug_loc'
.L597:
	.word	-1,.L141,.L137-.L141,.L349-.L141
	.half	5
	.byte	144,34,157,32,32
	.word	.L756-.L141,.L339-.L141
	.half	5
	.byte	144,34,157,32,32
	.word	.L139-.L141,.L269-.L141
	.half	5
	.byte	144,34,157,32,32
	.word	.L757-.L141,.L594-.L141
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L596:
	.word	-1,.L141,.L137-.L141,.L349-.L141
	.half	5
	.byte	144,34,157,32,0
	.word	.L756-.L141,.L339-.L141
	.half	5
	.byte	144,34,157,32,0
	.word	.L139-.L141,.L269-.L141
	.half	5
	.byte	144,34,157,32,0
	.word	.L757-.L141,.L594-.L141
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L600:
	.word	-1,.L141,.L753-.L141,.L754-.L141
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L599:
	.word	-1,.L141,.L137-.L141,.L349-.L141
	.half	5
	.byte	144,39,157,32,32
	.word	.L756-.L141,.L339-.L141
	.half	5
	.byte	144,39,157,32,32
	.word	.L139-.L141,.L753-.L141
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L140:
	.word	-1,.L141,0,.L594-.L141
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lHwDisableAscLinTxIntr')
	.sect	'.debug_loc'
.L590:
	.word	-1,.L145,.L137-.L145,.L349-.L145
	.half	5
	.byte	144,34,157,32,32
	.word	.L758-.L145,.L344-.L145
	.half	5
	.byte	144,34,157,32,32
	.word	.L139-.L145,.L269-.L145
	.half	5
	.byte	144,34,157,32,32
	.word	.L759-.L145,.L587-.L145
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L589:
	.word	-1,.L145,.L137-.L145,.L349-.L145
	.half	5
	.byte	144,34,157,32,0
	.word	.L758-.L145,.L344-.L145
	.half	5
	.byte	144,34,157,32,0
	.word	.L139-.L145,.L269-.L145
	.half	5
	.byte	144,34,157,32,0
	.word	.L759-.L145,.L587-.L145
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L593:
	.word	-1,.L145,.L753-.L145,.L754-.L145
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L592:
	.word	-1,.L145,.L137-.L145,.L349-.L145
	.half	5
	.byte	144,39,157,32,32
	.word	.L758-.L145,.L344-.L145
	.half	5
	.byte	144,39,157,32,32
	.word	.L139-.L145,.L753-.L145
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L144:
	.word	-1,.L145,0,.L587-.L145
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lHwEnableAscLinErrIntr')
	.sect	'.debug_loc'
.L581:
	.word	-1,.L149,.L137-.L149,.L349-.L149
	.half	5
	.byte	144,34,157,32,0
	.word	.L752-.L149,.L334-.L149
	.half	5
	.byte	144,34,157,32,0
	.word	.L151-.L149,.L760-.L149
	.half	5
	.byte	144,34,157,32,0
	.word	.L585-.L149,.L579-.L149
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L582:
	.word	-1,.L149,.L137-.L149,.L349-.L149
	.half	5
	.byte	144,39,157,32,32
	.word	.L752-.L149,.L334-.L149
	.half	5
	.byte	144,39,157,32,32
	.word	.L151-.L149,.L761-.L149
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L148:
	.word	-1,.L149,0,.L579-.L149
	.half	2
	.byte	138,0
	.word	0,0
.L586:
	.word	-1,.L149,.L762-.L149,.L763-.L149
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L764:
	.word	-1,.L149,.L585-.L149,.L579-.L149
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lHwEnableAscLinRxIntr')
	.sect	'.debug_loc'
.L574:
	.word	-1,.L153,.L137-.L153,.L349-.L153
	.half	5
	.byte	144,34,157,32,0
	.word	.L756-.L153,.L339-.L153
	.half	5
	.byte	144,34,157,32,0
	.word	.L151-.L153,.L264-.L153
	.half	5
	.byte	144,34,157,32,0
	.word	.L577-.L153,.L572-.L153
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L575:
	.word	-1,.L153,.L137-.L153,.L349-.L153
	.half	5
	.byte	144,39,157,32,32
	.word	.L756-.L153,.L339-.L153
	.half	5
	.byte	144,39,157,32,32
	.word	.L151-.L153,.L761-.L153
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L152:
	.word	-1,.L153,0,.L572-.L153
	.half	2
	.byte	138,0
	.word	0,0
.L765:
	.word	-1,.L153,.L766-.L153,.L264-.L153
	.half	5
	.byte	144,39,157,32,32
	.word	.L577-.L153,.L572-.L153
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L578:
	.word	-1,.L153,.L762-.L153,.L763-.L153
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lHwEnableAscLinTxIntr')
	.sect	'.debug_loc'
.L567:
	.word	-1,.L155,.L137-.L155,.L349-.L155
	.half	5
	.byte	144,34,157,32,0
	.word	.L758-.L155,.L344-.L155
	.half	5
	.byte	144,34,157,32,0
	.word	.L151-.L155,.L264-.L155
	.half	5
	.byte	144,34,157,32,0
	.word	.L570-.L155,.L565-.L155
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L568:
	.word	-1,.L155,.L137-.L155,.L349-.L155
	.half	5
	.byte	144,39,157,32,32
	.word	.L758-.L155,.L344-.L155
	.half	5
	.byte	144,39,157,32,32
	.word	.L151-.L155,.L761-.L155
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L154:
	.word	-1,.L155,0,.L565-.L155
	.half	2
	.byte	138,0
	.word	0,0
.L571:
	.word	-1,.L155,.L762-.L155,.L763-.L155
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L767:
	.word	-1,.L155,.L766-.L155,.L264-.L155
	.half	5
	.byte	144,39,157,32,32
	.word	.L570-.L155,.L565-.L155
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lHwInit')
	.sect	'.debug_loc'
.L458:
	.word	-1,.L87,0,.L467-.L87
	.half	1
	.byte	100
	.word	.L89-.L87,.L627-.L87
	.half	1
	.byte	100
	.word	.L470-.L87,.L515-.L87
	.half	1
	.byte	100
	.word	.L89-.L87,.L294-.L87
	.half	1
	.byte	100
	.word	.L517-.L87,.L456-.L87
	.half	1
	.byte	100
	.word	0,0
.L459:
	.word	-1,.L87,.L626-.L87,.L467-.L87
	.half	1
	.byte	111
	.word	.L89-.L87,.L627-.L87
	.half	1
	.byte	111
	.word	.L470-.L87,.L515-.L87
	.half	1
	.byte	111
	.word	.L89-.L87,.L294-.L87
	.half	1
	.byte	111
	.word	.L517-.L87,.L456-.L87
	.half	1
	.byte	111
	.word	0,0
.L457:
	.word	-1,.L87,0,.L467-.L87
	.half	5
	.byte	144,34,157,32,0
	.word	.L89-.L87,.L627-.L87
	.half	5
	.byte	144,34,157,32,0
	.word	.L470-.L87,.L515-.L87
	.half	5
	.byte	144,34,157,32,0
	.word	.L89-.L87,.L294-.L87
	.half	5
	.byte	144,34,157,32,0
	.word	.L517-.L87,.L456-.L87
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L460:
	.word	-1,.L87,.L461-.L87,.L467-.L87
	.half	5
	.byte	144,32,157,32,0
	.word	.L89-.L87,.L627-.L87
	.half	5
	.byte	144,32,157,32,0
	.word	.L470-.L87,.L472-.L87
	.half	5
	.byte	144,32,157,32,0
	.word	.L472-.L87,.L476-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	.L17-.L87,.L456-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L86:
	.word	-1,.L87,0,.L456-.L87
	.half	2
	.byte	138,0
	.word	0,0
.L493:
	.word	-1,.L87,.L492-.L87,.L638-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L468:
	.word	-1,.L87,.L89-.L87,.L628-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L512:
	.word	-1,.L87,.L655-.L87,.L656-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L487:
	.word	-1,.L87,.L632-.L87,.L633-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L508:
	.word	-1,.L87,.L507-.L87,.L653-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L514:
	.word	-1,.L87,.L513-.L87,.L657-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L463:
	.word	-1,.L87,.L462-.L87,.L8-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L479:
	.word	-1,.L87,.L478-.L87,.L629-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L516:
	.word	-1,.L87,.L658-.L87,.L515-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	.L89-.L87,.L628-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L500:
	.word	-1,.L87,.L646-.L87,.L647-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L471:
	.word	-1,.L87,.L470-.L87,.L11-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L506:
	.word	-1,.L87,.L651-.L87,.L652-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L518:
	.word	-1,.L87,.L628-.L87,.L294-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	.L517-.L87,.L17-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L504:
	.word	-1,.L87,.L503-.L87,.L650-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L485:
	.word	-1,.L87,.L630-.L87,.L631-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L489:
	.word	-1,.L87,.L634-.L87,.L635-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L502:
	.word	-1,.L87,.L648-.L87,.L649-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L510:
	.word	-1,.L87,.L509-.L87,.L654-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L497:
	.word	-1,.L87,.L641-.L87,.L642-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L474:
	.word	-1,.L87,.L473-.L87,.L14-.L87
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L483:
	.word	-1,.L87,.L644-.L87,.L645-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L495:
	.word	-1,.L87,.L639-.L87,.L640-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L498:
	.word	-1,.L87,.L481-.L87,.L643-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L491:
	.word	-1,.L87,.L636-.L87,.L637-.L87
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lRead')
	.sect	'.debug_loc'
.L523:
	.word	-1,.L93,.L672-.L93,.L519-.L93
	.half	1
	.byte	98
	.word	0,0
.L521:
	.word	-1,.L93,.L663-.L93,.L324-.L93
	.half	5
	.byte	144,34,157,32,0
	.word	.L95-.L93,.L279-.L93
	.half	5
	.byte	144,34,157,32,0
	.word	.L671-.L93,.L519-.L93
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L520:
	.word	-1,.L93,.L663-.L93,.L324-.L93
	.half	1
	.byte	100
	.word	.L95-.L93,.L279-.L93
	.half	1
	.byte	100
	.word	.L671-.L93,.L519-.L93
	.half	1
	.byte	100
	.word	0,0
.L524:
	.word	-1,.L93,.L25-.L93,.L673-.L93
	.half	5
	.byte	144,32,157,32,0
	.word	.L530-.L93,.L677-.L93
	.half	5
	.byte	144,32,157,32,0
	.word	.L678-.L93,.L679-.L93
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L525:
	.word	-1,.L93,.L533-.L93,.L680-.L93
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L526:
	.word	-1,.L93,.L674-.L93,.L675-.L93
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L92:
	.word	-1,.L93,0,.L519-.L93
	.half	2
	.byte	138,0
	.word	0,0
.L532:
	.word	-1,.L93,.L531-.L93,.L26-.L93
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L534:
	.word	0,0
.L535:
	.word	-1,.L93,.L31-.L93,.L30-.L93
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L529:
	.word	-1,.L93,.L528-.L93,.L676-.L93
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Uart_lWrite')
	.sect	'.debug_loc'
.L539:
	.word	-1,.L115,.L709-.L115,.L536-.L115
	.half	1
	.byte	101
	.word	0,0
.L538:
	.word	-1,.L115,.L85-.L115,.L324-.L115
	.half	5
	.byte	144,34,157,32,0
	.word	.L95-.L115,.L279-.L115
	.half	5
	.byte	144,34,157,32,0
	.word	.L708-.L115,.L536-.L115
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L537:
	.word	-1,.L115,.L85-.L115,.L324-.L115
	.half	1
	.byte	100
	.word	.L95-.L115,.L279-.L115
	.half	1
	.byte	100
	.word	.L708-.L115,.L536-.L115
	.half	1
	.byte	100
	.word	0,0
.L541:
	.word	-1,.L115,.L711-.L115,.L712-.L115
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L114:
	.word	-1,.L115,0,.L536-.L115
	.half	2
	.byte	138,0
	.word	0,0
.L540:
	.word	-1,.L115,.L38-.L115,.L710-.L115
	.half	5
	.byte	144,32,157,32,0
	.word	.L43-.L115,.L713-.L115
	.half	5
	.byte	144,32,157,32,0
	.word	.L714-.L115,.L536-.L115
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L544:
	.word	-1,.L115,.L543-.L115,.L43-.L115
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1156:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Uart_Init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1156,.L81,.L358-.L81
	.byte	4
	.word	(.L611-.L81)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L358-.L611)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Uart_lHwInit')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L87,.L456-.L87
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Uart_Read')
	.sect	'.debug_frame'
	.word	12
	.word	.L1156,.L91,.L382-.L91
	.sdecl	'.debug_frame',debug,cluster('Uart_lRead')
	.sect	'.debug_frame'
	.word	20
	.word	.L1156,.L93,.L519-.L93
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Uart_lEnableReadInterrupts')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L97,.L545-.L97
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Uart_lClearReadInterrupts')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L101,.L562-.L101
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Uart_Write')
	.sect	'.debug_frame'
	.word	12
	.word	.L1156,.L103,.L407-.L103
	.sdecl	'.debug_frame',debug,cluster('Uart_lWrite')
	.sect	'.debug_frame'
	.word	20
	.word	.L1156,.L115,.L536-.L115
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Uart_lEnableWriteInterrupts')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L117,.L556-.L117
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Uart_lClearWriteInterrupts')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L119,.L559-.L119
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Uart_GetStatus')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L121,.L435-.L121
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Uart_IsrReceive')
	.sect	'.debug_frame'
	.word	12
	.word	.L1156,.L123,.L438-.L123
	.sdecl	'.debug_frame',debug,cluster('Uart_IsrTransmit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1156,.L129,.L444-.L129
	.sdecl	'.debug_frame',debug,cluster('Uart_IsrError')
	.sect	'.debug_frame'
	.word	12
	.word	.L1156,.L131,.L449-.L131
	.sdecl	'.debug_frame',debug,cluster('Uart_lHwDisableAscLinErrIntr')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L133,.L601-.L133
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Uart_lHwDisableAscLinRxIntr')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L141,.L594-.L141
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Uart_lHwDisableAscLinTxIntr')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L145,.L587-.L145
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Uart_lHwEnableAscLinErrIntr')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L149,.L579-.L149
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Uart_lHwEnableAscLinRxIntr')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L153,.L572-.L153
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Uart_lHwEnableAscLinTxIntr')
	.sect	'.debug_frame'
	.word	24
	.word	.L1156,.L155,.L565-.L155
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1157:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_14')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L83,.L329-.L83
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_13')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L85,.L324-.L85
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_7')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L89,.L294-.L89
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_4')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L95,.L279-.L95
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_5')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L99,.L284-.L99
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_11')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L105,.L314-.L105
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_10')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L107,.L309-.L107
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_9')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L109,.L304-.L109
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_8')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L111,.L299-.L111
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_6')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L113,.L289-.L113
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_12')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L125,.L319-.L125
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L127,.L274-.L127
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_15')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L135,.L334-.L135
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_18')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L137,.L349-.L137
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L139,.L269-.L139
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_16')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L143,.L339-.L143
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_17')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L147,.L344-.L147
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L1157,.L151,.L264-.L151
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Uart.c	  3073  /*----------------------------------------------------------------------------*/
; ..\mcal_src\Uart.c	  3074  /* Memory Map of the UART Code */
; ..\mcal_src\Uart.c	  3075  #define UART_STOP_SEC_CODE
; ..\mcal_src\Uart.c	  3076  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Uart.c	  3077   allowed only for MemMap.h*/
; ..\mcal_src\Uart.c	  3078  #include "MemMap.h"
; ..\mcal_src\Uart.c	  3079  /*******************************************************************************
; ..\mcal_src\Uart.c	  3080  **                               End of File                                  **
; ..\mcal_src\Uart.c	  3081  *******************************************************************************/

	; Module end
