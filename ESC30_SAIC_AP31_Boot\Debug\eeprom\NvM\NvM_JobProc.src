	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc14420a -c99 --dep-file=eeprom\\NvM\\.NvM_JobProc.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\NvM\\NvM_JobProc.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\NvM\\NvM_JobProc.src ..\\eeprom\\NvM\\NvM_JobProc.c"
	.compiler_name		"ctc"
	.name	"NvM_JobProc"

	
$TC16X
	
	.sdecl	'.text.NvM_JobProc.NvM_JobProcInit',code,cluster('NvM_JobProcInit')
	.sect	'.text.NvM_JobProc.NvM_JobProcInit'
	.align	2
	
	.global	NvM_JobProcInit

; ..\eeprom\NvM\NvM_JobProc.c	     1  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	     2   *  COPYRIGHT
; ..\eeprom\NvM\NvM_JobProc.c	     3   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_JobProc.c	     4   *  \verbatim
; ..\eeprom\NvM\NvM_JobProc.c	     5   *  Copyright (c) 2019 by Vector Informatik GmbH.                                                  All rights reserved.
; ..\eeprom\NvM\NvM_JobProc.c	     6   *
; ..\eeprom\NvM\NvM_JobProc.c	     7   *                This software is copyright protected and proprietary to Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_JobProc.c	     8   *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
; ..\eeprom\NvM\NvM_JobProc.c	     9   *                All other rights remain with Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_JobProc.c	    10   *  \endverbatim
; ..\eeprom\NvM\NvM_JobProc.c	    11   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_JobProc.c	    12   *  FILE DESCRIPTION
; ..\eeprom\NvM\NvM_JobProc.c	    13   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_JobProc.c	    14   *         File:  NvM_JobProc.c
; ..\eeprom\NvM\NvM_JobProc.c	    15   *      Project:  MemService_AsrNvM
; ..\eeprom\NvM\NvM_JobProc.c	    16   *       Module:  NvM - Submodule JobProc (Job Processing)
; ..\eeprom\NvM\NvM_JobProc.c	    17   *    Generator:  -
; ..\eeprom\NvM\NvM_JobProc.c	    18   *
; ..\eeprom\NvM\NvM_JobProc.c	    19   *  Description:  This sub-module implements the FSM for the NVM, including the state
; ..\eeprom\NvM\NvM_JobProc.c	    20   *                description table.
; ..\eeprom\NvM\NvM_JobProc.c	    21   *
; ..\eeprom\NvM\NvM_JobProc.c	    22   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    23  
; ..\eeprom\NvM\NvM_JobProc.c	    24  /* Do not modify this file! */
; ..\eeprom\NvM\NvM_JobProc.c	    25  
; ..\eeprom\NvM\NvM_JobProc.c	    26  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    27   *  MODULE SWITCH
; ..\eeprom\NvM\NvM_JobProc.c	    28   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    29  #define NVM_JOBPROC_SOURCE
; ..\eeprom\NvM\NvM_JobProc.c	    30  
; ..\eeprom\NvM\NvM_JobProc.c	    31  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    32   *  INCLUDES
; ..\eeprom\NvM\NvM_JobProc.c	    33   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    34  #include "Std_Types.h"
; ..\eeprom\NvM\NvM_JobProc.c	    35  
; ..\eeprom\NvM\NvM_JobProc.c	    36  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    37   *  INCLUDE OF CONFIGURATION (ALL SECTIONS)
; ..\eeprom\NvM\NvM_JobProc.c	    38   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    39  #include "NvM_Cfg.h"
; ..\eeprom\NvM\NvM_JobProc.c	    40  #include "NvM_PrivateCfg.h"
; ..\eeprom\NvM\NvM_JobProc.c	    41  
; ..\eeprom\NvM\NvM_JobProc.c	    42  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    43   *  MODULE HEADER INCLUDES
; ..\eeprom\NvM\NvM_JobProc.c	    44   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    45  #include "NvM_JobProc.h"
; ..\eeprom\NvM\NvM_JobProc.c	    46  
; ..\eeprom\NvM\NvM_JobProc.c	    47  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    48   *  INCLUDE OF THE ACTIONS AND QUERIES
; ..\eeprom\NvM\NvM_JobProc.c	    49   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    50  #include "NvM_Act.h"
; ..\eeprom\NvM\NvM_JobProc.c	    51  #include "NvM_Qry.h"
; ..\eeprom\NvM\NvM_JobProc.c	    52  
; ..\eeprom\NvM\NvM_JobProc.c	    53  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    54   *  INTERNAL CHECKS
; ..\eeprom\NvM\NvM_JobProc.c	    55   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    56  #if (3u != NVM_MAX_NUM_OF_STATE_CONDITIONS)
; ..\eeprom\NvM\NvM_JobProc.c	    57  # error "Only case '3 conditions (4 state exits)' is implemented"
; ..\eeprom\NvM\NvM_JobProc.c	    58  #endif
; ..\eeprom\NvM\NvM_JobProc.c	    59  
; ..\eeprom\NvM\NvM_JobProc.c	    60  #if (2u != NVM_MAX_NUM_OF_QRY_AND)
; ..\eeprom\NvM\NvM_JobProc.c	    61  # error "Currently two AND-associated queries per state exit are supported"
; ..\eeprom\NvM\NvM_JobProc.c	    62  #endif
; ..\eeprom\NvM\NvM_JobProc.c	    63  
; ..\eeprom\NvM\NvM_JobProc.c	    64  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    65   *  INTERNAL MACROS
; ..\eeprom\NvM\NvM_JobProc.c	    66   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    67  #ifndef NVM_LOCAL /* COV_NVM_COMPATIBILITY */
; ..\eeprom\NvM\NvM_JobProc.c	    68  # define NVM_LOCAL static
; ..\eeprom\NvM\NvM_JobProc.c	    69  #endif
; ..\eeprom\NvM\NvM_JobProc.c	    70  
; ..\eeprom\NvM\NvM_JobProc.c	    71  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    72   *  INTERNAL FORWARD DECLARATIONS
; ..\eeprom\NvM\NvM_JobProc.c	    73   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    74  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_JobProc.c	    75    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	    76  
; ..\eeprom\NvM\NvM_JobProc.c	    77  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    78   * NvM_FsmQuery
; ..\eeprom\NvM\NvM_JobProc.c	    79   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    80  /*! \brief Process a State machine query.
; ..\eeprom\NvM\NvM_JobProc.c	    81   *  \details Executes first query and then the second, in case the first was successful.
; ..\eeprom\NvM\NvM_JobProc.c	    82   *  \param[in] NvM_Queries_at as a pointer to the query list (array). Called must ensure validity. Must contain 2 queries.
; ..\eeprom\NvM\NvM_JobProc.c	    83   *  \return TRUE both queries executed successfully
; ..\eeprom\NvM\NvM_JobProc.c	    84   *  \return FALSE otherwise
; ..\eeprom\NvM\NvM_JobProc.c	    85   *  \context TASK
; ..\eeprom\NvM\NvM_JobProc.c	    86   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_JobProc.c	    87   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_JobProc.c	    88   *  \pre -
; ..\eeprom\NvM\NvM_JobProc.c	    89   */
; ..\eeprom\NvM\NvM_JobProc.c	    90  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_FsmQuery(NvM_StateQueryPtrType NvM_Queries_at);
; ..\eeprom\NvM\NvM_JobProc.c	    91  
; ..\eeprom\NvM\NvM_JobProc.c	    92  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	    93   * NvM_FsmAction
; ..\eeprom\NvM\NvM_JobProc.c	    94   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	    95  /*! \brief Process a State change - perform the defined actions.
; ..\eeprom\NvM\NvM_JobProc.c	    96   *  \details Process a State change - perform the defined actions.
; ..\eeprom\NvM\NvM_JobProc.c	    97   *  \param[in] NvM_Actions_pt as a pointer to two action indexes (action function pointer array) used to call
; ..\eeprom\NvM\NvM_JobProc.c	    98   *             the actions. Caller must ensure validity.
; ..\eeprom\NvM\NvM_JobProc.c	    99   *  \context TASK
; ..\eeprom\NvM\NvM_JobProc.c	   100   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_JobProc.c	   101   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_JobProc.c	   102   *  \pre -
; ..\eeprom\NvM\NvM_JobProc.c	   103   */
; ..\eeprom\NvM\NvM_JobProc.c	   104  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_FsmAction(NvM_StateChangeActionsPtrType NvM_Actions_pt);
; ..\eeprom\NvM\NvM_JobProc.c	   105  
; ..\eeprom\NvM\NvM_JobProc.c	   106  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_JobProc.c	   107    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	   108  
; ..\eeprom\NvM\NvM_JobProc.c	   109  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   110   *  INTERNAL MODULE GLOBAL VARIABLES
; ..\eeprom\NvM\NvM_JobProc.c	   111   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   112  
; ..\eeprom\NvM\NvM_JobProc.c	   113  #define NVM_START_SEC_VAR_FAST_8
; ..\eeprom\NvM\NvM_JobProc.c	   114    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	   115  
; ..\eeprom\NvM\NvM_JobProc.c	   116  /* at least for development mode (where the following two state types are enums) the
; ..\eeprom\NvM\NvM_JobProc.c	   117  * compiler is expected to ensure correct alignment, regardless of any given section alignment.
; ..\eeprom\NvM\NvM_JobProc.c	   118  * The state variables are used quite frequently (but never via pointers)
; ..\eeprom\NvM\NvM_JobProc.c	   119  */
; ..\eeprom\NvM\NvM_JobProc.c	   120  VAR(NvM_StateIdType, NVM_FAST_DATA) NvM_JobMainState_t;
; ..\eeprom\NvM\NvM_JobProc.c	   121  VAR(NvM_StateIdType, NVM_FAST_DATA) NvM_JobSubState_t;
; ..\eeprom\NvM\NvM_JobProc.c	   122  
; ..\eeprom\NvM\NvM_JobProc.c	   123  #define NVM_STOP_SEC_VAR_FAST_8
; ..\eeprom\NvM\NvM_JobProc.c	   124    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	   125  
; ..\eeprom\NvM\NvM_JobProc.c	   126  #define NVM_START_SEC_VAR_UNSPECIFIED
; ..\eeprom\NvM\NvM_JobProc.c	   127    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	   128  
; ..\eeprom\NvM\NvM_JobProc.c	   129  /* state of the task state machine */
; ..\eeprom\NvM\NvM_JobProc.c	   130  VAR(NvM_StateIdType, NVM_PRIVATE_DATA) NvM_TaskState_t = NVM_STATE_UNINIT;
; ..\eeprom\NvM\NvM_JobProc.c	   131  
; ..\eeprom\NvM\NvM_JobProc.c	   132  #define NVM_STOP_SEC_VAR_UNSPECIFIED
; ..\eeprom\NvM\NvM_JobProc.c	   133    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	   134  
; ..\eeprom\NvM\NvM_JobProc.c	   135  #define NVM_START_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM_JobProc.c	   136    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	   137  
; ..\eeprom\NvM\NvM_JobProc.c	   138  VAR(NvM_BlockInfoType, NVM_PRIVATE_DATA) NvM_CurrentBlockInfo_t;
; ..\eeprom\NvM\NvM_JobProc.c	   139  
; ..\eeprom\NvM\NvM_JobProc.c	   140  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	   141  VAR(NvM_RepairBlockStateType, NVM_PRIVATE_DATA) NvM_RepairRedBlockState;
; ..\eeprom\NvM\NvM_JobProc.c	   142  #endif
; ..\eeprom\NvM\NvM_JobProc.c	   143  
; ..\eeprom\NvM\NvM_JobProc.c	   144  #define NVM_STOP_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM_JobProc.c	   145    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	   146  
; ..\eeprom\NvM\NvM_JobProc.c	   147  #define NVM_START_SEC_CONST_UNSPECIFIED
; ..\eeprom\NvM\NvM_JobProc.c	   148    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	   149  
; ..\eeprom\NvM\NvM_JobProc.c	   150  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   151   *  JOB DESCRIPTOR TABLE
; ..\eeprom\NvM\NvM_JobProc.c	   152   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   153  CONST(NvM_IntServiceDescrType, NVM_PRIVATE_CONST) NvM_IntServiceDescrTable_at[(uint32)NVM_INT_FID_NO_JOB_PENDING + 1u] =
; ..\eeprom\NvM\NvM_JobProc.c	   154  {
; ..\eeprom\NvM\NvM_JobProc.c	   155      /* NVM_INT_FID_WRITE_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	   156      {
; ..\eeprom\NvM\NvM_JobProc.c	   157          NVM_ACT_ID_InitWriteBlock, NVM_STATE_WRITE_INITIAL, NVM_WRITE_BLOCK
; ..\eeprom\NvM\NvM_JobProc.c	   158      },
; ..\eeprom\NvM\NvM_JobProc.c	   159  
; ..\eeprom\NvM\NvM_JobProc.c	   160      /* NVM_INT_FID_READ_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	   161      {
; ..\eeprom\NvM\NvM_JobProc.c	   162          NVM_ACT_ID_ReadNvBlock, NVM_STATE_READ_READ_DATA, NVM_READ_BLOCK
; ..\eeprom\NvM\NvM_JobProc.c	   163      },
; ..\eeprom\NvM\NvM_JobProc.c	   164  
; ..\eeprom\NvM\NvM_JobProc.c	   165      /* NVM_INT_FID_RESTORE_DEFAULTS */
; ..\eeprom\NvM\NvM_JobProc.c	   166      {
; ..\eeprom\NvM\NvM_JobProc.c	   167          NVM_ACT_ID_InitRestoreBlockDefaults, NVM_STATE_RESTORE_LOAD_ROM, NVM_RESTORE_BLOCK_DEFAULTS
; ..\eeprom\NvM\NvM_JobProc.c	   168      },
; ..\eeprom\NvM\NvM_JobProc.c	   169  
; ..\eeprom\NvM\NvM_JobProc.c	   170  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM_JobProc.c	   171      /* NVM_INT_FID_INVALIDATE_NV_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	   172      {
; ..\eeprom\NvM\NvM_JobProc.c	   173          NVM_ACT_ID_InvalidateNvBlock, NVM_STATE_INVALIDATING_BLOCK, NVM_INVALIDATE_NV_BLOCK
; ..\eeprom\NvM\NvM_JobProc.c	   174      },
; ..\eeprom\NvM\NvM_JobProc.c	   175  
; ..\eeprom\NvM\NvM_JobProc.c	   176      /* NVM_INT_FID_ERASE_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	   177      {
; ..\eeprom\NvM\NvM_JobProc.c	   178          NVM_ACT_ID_EraseNvBlock, NVM_STATE_ERASE_ERASE_BLOCK, NVM_ERASE_BLOCK
; ..\eeprom\NvM\NvM_JobProc.c	   179      },
; ..\eeprom\NvM\NvM_JobProc.c	   180  #else
; ..\eeprom\NvM\NvM_JobProc.c	   181      /* NVM_INT_FID_INVALIDATE_NV_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	   182      {
; ..\eeprom\NvM\NvM_JobProc.c	   183          NVM_ACT_ID_Nop, NVM_STATE_FSM_FINISHED, NVM_INVALIDATE_NV_BLOCK
; ..\eeprom\NvM\NvM_JobProc.c	   184      },
; ..\eeprom\NvM\NvM_JobProc.c	   185  
; ..\eeprom\NvM\NvM_JobProc.c	   186      /* NVM_INT_FID_ERASE_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	   187      {
; ..\eeprom\NvM\NvM_JobProc.c	   188          NVM_ACT_ID_Nop, NVM_STATE_FSM_FINISHED, NVM_ERASE_BLOCK
; ..\eeprom\NvM\NvM_JobProc.c	   189      },
; ..\eeprom\NvM\NvM_JobProc.c	   190  #endif
; ..\eeprom\NvM\NvM_JobProc.c	   191      /* NVM_INT_FID_WRITE_ALL */
; ..\eeprom\NvM\NvM_JobProc.c	   192      {
; ..\eeprom\NvM\NvM_JobProc.c	   193          NVM_ACT_ID_InitWriteAll, NVM_STATE_WRITEALL_PROC_BLOCK, NVM_WRITE_ALL
; ..\eeprom\NvM\NvM_JobProc.c	   194      },
; ..\eeprom\NvM\NvM_JobProc.c	   195  
; ..\eeprom\NvM\NvM_JobProc.c	   196      /* NVM_INT_FID_READ_ALL */
; ..\eeprom\NvM\NvM_JobProc.c	   197      {
; ..\eeprom\NvM\NvM_JobProc.c	   198          NVM_ACT_ID_InitReadAll, NVM_STATE_READALL_PROC_CONFIG_ID, NVM_READ_ALL
; ..\eeprom\NvM\NvM_JobProc.c	   199      },
; ..\eeprom\NvM\NvM_JobProc.c	   200  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	   201      /* NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS */
; ..\eeprom\NvM\NvM_JobProc.c	   202      {
; ..\eeprom\NvM\NvM_JobProc.c	   203          NVM_ACT_ID_RepairRedBlocksInit, NVM_STATE_REPAIRREDUNDANT_MAIN, NVM_REPAIR_REDUNDANT_BLOCKS
; ..\eeprom\NvM\NvM_JobProc.c	   204      },
; ..\eeprom\NvM\NvM_JobProc.c	   205  #else
; ..\eeprom\NvM\NvM_JobProc.c	   206      /* NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS */
; ..\eeprom\NvM\NvM_JobProc.c	   207      {
; ..\eeprom\NvM\NvM_JobProc.c	   208          NVM_ACT_ID_Nop, NVM_STATE_FSM_FINISHED, NVM_REPAIR_REDUNDANT_BLOCKS
; ..\eeprom\NvM\NvM_JobProc.c	   209      },
; ..\eeprom\NvM\NvM_JobProc.c	   210  #endif
; ..\eeprom\NvM\NvM_JobProc.c	   211      /* NVM_INT_FID_NO_JOB_PENDING */
; ..\eeprom\NvM\NvM_JobProc.c	   212      {
; ..\eeprom\NvM\NvM_JobProc.c	   213          NVM_ACT_ID_Nop, NVM_STATE_FSM_FINISHED, NVM_MAINFUNCTION
; ..\eeprom\NvM\NvM_JobProc.c	   214      }
; ..\eeprom\NvM\NvM_JobProc.c	   215  };
; ..\eeprom\NvM\NvM_JobProc.c	   216  
; ..\eeprom\NvM\NvM_JobProc.c	   217  /* these macros shall increase the readability of the State Descriptor Table */
; ..\eeprom\NvM\NvM_JobProc.c	   218  #define NVM_STATE_IF
; ..\eeprom\NvM\NvM_JobProc.c	   219  #define NVM_STATE_ELSEIF
; ..\eeprom\NvM\NvM_JobProc.c	   220  #define NVM_STATE_THEN
; ..\eeprom\NvM\NvM_JobProc.c	   221  #define NVM_STATE_ELSE
; ..\eeprom\NvM\NvM_JobProc.c	   222  
; ..\eeprom\NvM\NvM_JobProc.c	   223  #define NVM_NEXT_STATE(state) (state) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
; ..\eeprom\NvM\NvM_JobProc.c	   224  
; ..\eeprom\NvM\NvM_JobProc.c	   225  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   226   *  STATE DESCRIPTOR TABLE
; ..\eeprom\NvM\NvM_JobProc.c	   227   *  - stores all NvM state machines within one single array with states as element type.
; ..\eeprom\NvM\NvM_JobProc.c	   228   *  - states are accessed via indexes - each state machine knows where to begin, following states are indexed from
; ..\eeprom\NvM\NvM_JobProc.c	   229   *  current processing state
; ..\eeprom\NvM\NvM_JobProc.c	   230   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   231  NVM_LOCAL CONST(NvM_StateDescrType, NVM_PRIVATE_CONST) NvM_StateDescrTable_at[] = /* PRQA S 3218 */ /* MD_NvM_8.9_StateTable */
; ..\eeprom\NvM\NvM_JobProc.c	   232  {
; ..\eeprom\NvM\NvM_JobProc.c	   233  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   234   *  NvM uninitialized
; ..\eeprom\NvM\NvM_JobProc.c	   235   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   236      /* NVM_STATE_UNINIT
; ..\eeprom\NvM\NvM_JobProc.c	   237       * if the TASK runs when the NVM is not initialized yet, use Wait Action to remain in UNINIT state.
; ..\eeprom\NvM\NvM_JobProc.c	   238       * This state can only be left through a call to NvM_Init by ECU state manager */
; ..\eeprom\NvM\NvM_JobProc.c	   239      {
; ..\eeprom\NvM\NvM_JobProc.c	   240          {
; ..\eeprom\NvM\NvM_JobProc.c	   241              /* Exit 1 */
; ..\eeprom\NvM\NvM_JobProc.c	   242              {
; ..\eeprom\NvM\NvM_JobProc.c	   243                  NVM_STATE_IF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   244                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   245                      NVM_NEXT_STATE(NVM_STATE_UNINIT)
; ..\eeprom\NvM\NvM_JobProc.c	   246              },
; ..\eeprom\NvM\NvM_JobProc.c	   247              /* Exit 2 */
; ..\eeprom\NvM\NvM_JobProc.c	   248              {
; ..\eeprom\NvM\NvM_JobProc.c	   249                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   250                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   251                      NVM_NEXT_STATE(NVM_STATE_UNINIT)
; ..\eeprom\NvM\NvM_JobProc.c	   252              },
; ..\eeprom\NvM\NvM_JobProc.c	   253              /* Exit 3 */
; ..\eeprom\NvM\NvM_JobProc.c	   254              {
; ..\eeprom\NvM\NvM_JobProc.c	   255                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   256                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   257                      NVM_NEXT_STATE(NVM_STATE_UNINIT)
; ..\eeprom\NvM\NvM_JobProc.c	   258              }
; ..\eeprom\NvM\NvM_JobProc.c	   259          },
; ..\eeprom\NvM\NvM_JobProc.c	   260          /* final case */
; ..\eeprom\NvM\NvM_JobProc.c	   261          {
; ..\eeprom\NvM\NvM_JobProc.c	   262              NVM_STATE_ELSE{NVM_ACT_ID_Wait, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   263              NVM_NEXT_STATE(NVM_STATE_UNINIT)
; ..\eeprom\NvM\NvM_JobProc.c	   264          }
; ..\eeprom\NvM\NvM_JobProc.c	   265      },
; ..\eeprom\NvM\NvM_JobProc.c	   266  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   267   *  NvM idle
; ..\eeprom\NvM\NvM_JobProc.c	   268   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   269  #if (NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_1)
; ..\eeprom\NvM\NvM_JobProc.c	   270      /* NVM_STATE_IDLE */
; ..\eeprom\NvM\NvM_JobProc.c	   271      /* in API config class 1, we only have to wait for a multi block job ReadAll or WriteAll */
; ..\eeprom\NvM\NvM_JobProc.c	   272      {
; ..\eeprom\NvM\NvM_JobProc.c	   273          {
; ..\eeprom\NvM\NvM_JobProc.c	   274              /* Multi block job found */
; ..\eeprom\NvM\NvM_JobProc.c	   275              {
; ..\eeprom\NvM\NvM_JobProc.c	   276                  NVM_STATE_IF{NVM_QRY_ID_MULTI_BLK_JOB, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   277                      NVM_STATE_THEN{NVM_ACT_ID_GetMultiBlockJob, NVM_ACT_ID_InitMainFsm},
; ..\eeprom\NvM\NvM_JobProc.c	   278  # if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	   279                      NVM_NEXT_STATE(NVM_STATE_MULTI_BLOCK_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   280  # else
; ..\eeprom\NvM\NvM_JobProc.c	   281                      NVM_NEXT_STATE(NVM_STATE_NORMAL_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   282  # endif
; ..\eeprom\NvM\NvM_JobProc.c	   283              },
; ..\eeprom\NvM\NvM_JobProc.c	   284              /* no job found */
; ..\eeprom\NvM\NvM_JobProc.c	   285              {
; ..\eeprom\NvM\NvM_JobProc.c	   286                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   287                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   288                      NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   289              },
; ..\eeprom\NvM\NvM_JobProc.c	   290              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   291              {
; ..\eeprom\NvM\NvM_JobProc.c	   292                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   293                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   294                      NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   295              }
; ..\eeprom\NvM\NvM_JobProc.c	   296          },
; ..\eeprom\NvM\NvM_JobProc.c	   297          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   298          {
; ..\eeprom\NvM\NvM_JobProc.c	   299              NVM_STATE_ELSE{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   300              NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   301          }
; ..\eeprom\NvM\NvM_JobProc.c	   302      },
; ..\eeprom\NvM\NvM_JobProc.c	   303  #elif (NVM_JOB_PRIORISATION == STD_ON) /* && (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1) */
; ..\eeprom\NvM\NvM_JobProc.c	   304      /* NVM_STATE_IDLE */
; ..\eeprom\NvM\NvM_JobProc.c	   305      /* Scan job lists, if no job was found: wait */
; ..\eeprom\NvM\NvM_JobProc.c	   306      {
; ..\eeprom\NvM\NvM_JobProc.c	   307          {
; ..\eeprom\NvM\NvM_JobProc.c	   308              /* High priority job found */
; ..\eeprom\NvM\NvM_JobProc.c	   309              {
; ..\eeprom\NvM\NvM_JobProc.c	   310                  NVM_STATE_IF{NVM_QRY_ID_HI_PRIO_JOB, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   311                      NVM_STATE_THEN{NVM_ACT_ID_GetHighPrioJob, NVM_ACT_ID_InitMainFsm},
; ..\eeprom\NvM\NvM_JobProc.c	   312                      NVM_NEXT_STATE(NVM_STATE_HIGH_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   313              },
; ..\eeprom\NvM\NvM_JobProc.c	   314              /* Normal priority job found */
; ..\eeprom\NvM\NvM_JobProc.c	   315              {
; ..\eeprom\NvM\NvM_JobProc.c	   316                  NVM_STATE_ELSEIF{NVM_QRY_ID_NORMAL_PRIO_JOB, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   317                      NVM_STATE_THEN{NVM_ACT_ID_GetNormalPrioJob, NVM_ACT_ID_InitMainFsm},
; ..\eeprom\NvM\NvM_JobProc.c	   318                      NVM_NEXT_STATE(NVM_STATE_NORMAL_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   319              },
; ..\eeprom\NvM\NvM_JobProc.c	   320              /* Multi block job found */
; ..\eeprom\NvM\NvM_JobProc.c	   321              {
; ..\eeprom\NvM\NvM_JobProc.c	   322                  NVM_STATE_ELSEIF{NVM_QRY_ID_MULTI_BLK_JOB, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   323                      NVM_STATE_THEN{NVM_ACT_ID_GetMultiBlockJob, NVM_ACT_ID_InitMainFsm},
; ..\eeprom\NvM\NvM_JobProc.c	   324  # if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	   325                      NVM_NEXT_STATE(NVM_STATE_MULTI_BLOCK_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   326  # else
; ..\eeprom\NvM\NvM_JobProc.c	   327                      NVM_NEXT_STATE(NVM_STATE_NORMAL_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   328  # endif
; ..\eeprom\NvM\NvM_JobProc.c	   329              }
; ..\eeprom\NvM\NvM_JobProc.c	   330          },
; ..\eeprom\NvM\NvM_JobProc.c	   331          /* final case, no job found */
; ..\eeprom\NvM\NvM_JobProc.c	   332          {
; ..\eeprom\NvM\NvM_JobProc.c	   333              NVM_STATE_ELSE{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   334              NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   335          }
; ..\eeprom\NvM\NvM_JobProc.c	   336      },
; ..\eeprom\NvM\NvM_JobProc.c	   337  #else /* (NVM_JOB_PRIORISATION == STD_OFF) && (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1) */
; ..\eeprom\NvM\NvM_JobProc.c	   338      /* NVM_STATE_IDLE */
; ..\eeprom\NvM\NvM_JobProc.c	   339      /* Scan job lists, if no job was found: wait */
; ..\eeprom\NvM\NvM_JobProc.c	   340      {
; ..\eeprom\NvM\NvM_JobProc.c	   341          {
; ..\eeprom\NvM\NvM_JobProc.c	   342              /* Single block job found */
; ..\eeprom\NvM\NvM_JobProc.c	   343              {
; ..\eeprom\NvM\NvM_JobProc.c	   344                  NVM_STATE_ELSEIF{NVM_QRY_ID_NORMAL_PRIO_JOB, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   345                      NVM_STATE_THEN{NVM_ACT_ID_GetNormalPrioJob, NVM_ACT_ID_InitMainFsm},
; ..\eeprom\NvM\NvM_JobProc.c	   346                      NVM_NEXT_STATE(NVM_STATE_NORMAL_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   347              },
; ..\eeprom\NvM\NvM_JobProc.c	   348              /* Multi block job found */
; ..\eeprom\NvM\NvM_JobProc.c	   349              {
; ..\eeprom\NvM\NvM_JobProc.c	   350                  NVM_STATE_ELSEIF{NVM_QRY_ID_MULTI_BLK_JOB, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   351                      NVM_STATE_THEN{NVM_ACT_ID_GetMultiBlockJob, NVM_ACT_ID_InitMainFsm},
; ..\eeprom\NvM\NvM_JobProc.c	   352  # if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	   353                      NVM_NEXT_STATE(NVM_STATE_MULTI_BLOCK_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   354  # else
; ..\eeprom\NvM\NvM_JobProc.c	   355                      NVM_NEXT_STATE(NVM_STATE_NORMAL_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   356  # endif
; ..\eeprom\NvM\NvM_JobProc.c	   357              },
; ..\eeprom\NvM\NvM_JobProc.c	   358              /* no job found */
; ..\eeprom\NvM\NvM_JobProc.c	   359              {
; ..\eeprom\NvM\NvM_JobProc.c	   360                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   361                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   362                      NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   363              }
; ..\eeprom\NvM\NvM_JobProc.c	   364          },
; ..\eeprom\NvM\NvM_JobProc.c	   365          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   366          {
; ..\eeprom\NvM\NvM_JobProc.c	   367              NVM_STATE_ELSE{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   368              NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   369          }
; ..\eeprom\NvM\NvM_JobProc.c	   370      },
; ..\eeprom\NvM\NvM_JobProc.c	   371  #endif /* (NVM_JOB_PRIORISATION == STD_OFF) && (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1) */
; ..\eeprom\NvM\NvM_JobProc.c	   372  #if ((NVM_API_CONFIG_CLASS != NVM_API_CONFIG_CLASS_1) && (NVM_JOB_PRIORISATION == STD_ON))
; ..\eeprom\NvM\NvM_JobProc.c	   373      /* NVM_STATE_HIGH_PRIO_JOB = High prio WriteJob, not cancelable */
; ..\eeprom\NvM\NvM_JobProc.c	   374      {
; ..\eeprom\NvM\NvM_JobProc.c	   375          {
; ..\eeprom\NvM\NvM_JobProc.c	   376              /* main job fsm still running */
; ..\eeprom\NvM\NvM_JobProc.c	   377              {
; ..\eeprom\NvM\NvM_JobProc.c	   378                  NVM_STATE_IF{NVM_QRY_ID_MAIN_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   379                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   380                      NVM_NEXT_STATE(NVM_STATE_HIGH_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   381              },
; ..\eeprom\NvM\NvM_JobProc.c	   382              /* main job fsm finished */
; ..\eeprom\NvM\NvM_JobProc.c	   383              {
; ..\eeprom\NvM\NvM_JobProc.c	   384                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   385                      /* Finish the job and exit MainFunction. Reason: when re-starting an canceled WriteAll
; ..\eeprom\NvM\NvM_JobProc.c	   386                       * (via immediate priority job) directly in same MainFunction as finishing the immediate
; ..\eeprom\NvM\NvM_JobProc.c	   387                       * priority job, the WriteAll will overwrite the immediate priority job result! Therefore
; ..\eeprom\NvM\NvM_JobProc.c	   388                       * exit the MainFunction and "allow" processing of other components, which may wait for the
; ..\eeprom\NvM\NvM_JobProc.c	   389                       * immediate priority job. */
; ..\eeprom\NvM\NvM_JobProc.c	   390                      NVM_STATE_THEN{NVM_ACT_ID_FinishMainJob, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   391                      NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   392              },
; ..\eeprom\NvM\NvM_JobProc.c	   393              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   394              {
; ..\eeprom\NvM\NvM_JobProc.c	   395                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   396                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   397                      NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   398              }
; ..\eeprom\NvM\NvM_JobProc.c	   399          },
; ..\eeprom\NvM\NvM_JobProc.c	   400          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   401          {
; ..\eeprom\NvM\NvM_JobProc.c	   402              NVM_STATE_ELSE{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   403              NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   404          }
; ..\eeprom\NvM\NvM_JobProc.c	   405      },
; ..\eeprom\NvM\NvM_JobProc.c	   406  #endif /* ((NVM_API_CONFIG_CLASS != NVM_API_CONFIG_CLASS_1) && (NVM_JOB_PRIORISATION == STD_ON)) */
; ..\eeprom\NvM\NvM_JobProc.c	   407  #if ((NVM_JOB_PRIORISATION == STD_ON) && (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1))
; ..\eeprom\NvM\NvM_JobProc.c	   408      /* NVM_STATE_NORMAL_PRIO_JOB = cancelable, all "NOT HighPrioWrite" jobs */
; ..\eeprom\NvM\NvM_JobProc.c	   409      {
; ..\eeprom\NvM\NvM_JobProc.c	   410          {
; ..\eeprom\NvM\NvM_JobProc.c	   411              {   /* low prio job running or finished, immediate job occurred */
; ..\eeprom\NvM\NvM_JobProc.c	   412                  NVM_STATE_IF{NVM_QRY_ID_HI_PRIO_JOB, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   413                      NVM_STATE_THEN{NVM_ACT_ID_GetHighPrioJob, NVM_ACT_ID_InitMainFsm},
; ..\eeprom\NvM\NvM_JobProc.c	   414                      NVM_NEXT_STATE(NVM_STATE_HIGH_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   415              },
; ..\eeprom\NvM\NvM_JobProc.c	   416              {   /* continue with the running job */
; ..\eeprom\NvM\NvM_JobProc.c	   417                  NVM_STATE_ELSEIF{NVM_QRY_ID_MAIN_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   418                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   419                      NVM_NEXT_STATE(NVM_STATE_NORMAL_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   420              },
; ..\eeprom\NvM\NvM_JobProc.c	   421              {   /* job fsm finished */
; ..\eeprom\NvM\NvM_JobProc.c	   422                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   423                      NVM_STATE_THEN{NVM_ACT_ID_FinishMainJob, NVM_ACT_ID_QueueFreeLastJob},
; ..\eeprom\NvM\NvM_JobProc.c	   424                      NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   425              }
; ..\eeprom\NvM\NvM_JobProc.c	   426          },
; ..\eeprom\NvM\NvM_JobProc.c	   427          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   428          {
; ..\eeprom\NvM\NvM_JobProc.c	   429              NVM_STATE_ELSE{NVM_ACT_ID_FinishMainJob, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   430              NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   431          }
; ..\eeprom\NvM\NvM_JobProc.c	   432      },
; ..\eeprom\NvM\NvM_JobProc.c	   433  #else
; ..\eeprom\NvM\NvM_JobProc.c	   434      /* NVM_STATE_NORMAL_PRIO_JOB */
; ..\eeprom\NvM\NvM_JobProc.c	   435      /* not cancelable as there are no high prio jobs, jobs do not need to be freed after processing */
; ..\eeprom\NvM\NvM_JobProc.c	   436      {
; ..\eeprom\NvM\NvM_JobProc.c	   437          {
; ..\eeprom\NvM\NvM_JobProc.c	   438              {   /* continue the running job */
; ..\eeprom\NvM\NvM_JobProc.c	   439                  NVM_STATE_IF{NVM_QRY_ID_MAIN_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   440                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   441                      NVM_NEXT_STATE(NVM_STATE_NORMAL_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   442              },
; ..\eeprom\NvM\NvM_JobProc.c	   443              {   /* job finished */
; ..\eeprom\NvM\NvM_JobProc.c	   444                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   445                      NVM_STATE_THEN{NVM_ACT_ID_FinishMainJob, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   446                      NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   447              },
; ..\eeprom\NvM\NvM_JobProc.c	   448              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   449              {
; ..\eeprom\NvM\NvM_JobProc.c	   450                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   451                      NVM_STATE_THEN{NVM_ACT_ID_FinishMainJob, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   452                      NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   453              }
; ..\eeprom\NvM\NvM_JobProc.c	   454          },
; ..\eeprom\NvM\NvM_JobProc.c	   455          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   456          {
; ..\eeprom\NvM\NvM_JobProc.c	   457              NVM_STATE_ELSE{NVM_ACT_ID_FinishReadBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   458              NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   459          }
; ..\eeprom\NvM\NvM_JobProc.c	   460      },
; ..\eeprom\NvM\NvM_JobProc.c	   461  
; ..\eeprom\NvM\NvM_JobProc.c	   462  #endif
; ..\eeprom\NvM\NvM_JobProc.c	   463  
; ..\eeprom\NvM\NvM_JobProc.c	   464  #if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	   465      /* NVM_STATE_MULTI_BLOCK_JOB */
; ..\eeprom\NvM\NvM_JobProc.c	   466  # if ((NVM_JOB_PRIORISATION == STD_ON) && (NVM_API_CONFIG_CLASS > NVM_API_CONFIG_CLASS_1))
; ..\eeprom\NvM\NvM_JobProc.c	   467          /* NVM_STATE_NORMAL_PRIO_JOB = cancelable, all "NOT HighPrioWrite" jobs */
; ..\eeprom\NvM\NvM_JobProc.c	   468          {
; ..\eeprom\NvM\NvM_JobProc.c	   469              {
; ..\eeprom\NvM\NvM_JobProc.c	   470                  {   /* WriteAll, but it was killed => Finish FSM, even if a high priority job might be waiting
; ..\eeprom\NvM\NvM_JobProc.c	   471                         It would be next job in "normal" processing - from IDLE */
; ..\eeprom\NvM\NvM_JobProc.c	   472                      NVM_STATE_IF{NVM_QR_ID_WRITEALL_KILLED, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   473                          NVM_STATE_THEN{NVM_ACT_ID_KillWritAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   474                          NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   475                  },
; ..\eeprom\NvM\NvM_JobProc.c	   476                  {   /* low prio job running or finished, immediate job occurred */
; ..\eeprom\NvM\NvM_JobProc.c	   477                      NVM_STATE_ELSEIF{NVM_QRY_ID_HI_PRIO_JOB, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   478                          NVM_STATE_THEN{NVM_ACT_ID_GetHighPrioJob, NVM_ACT_ID_InitMainFsm},
; ..\eeprom\NvM\NvM_JobProc.c	   479                          NVM_NEXT_STATE(NVM_STATE_HIGH_PRIO_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   480                  },
; ..\eeprom\NvM\NvM_JobProc.c	   481                  {   /* continue with the running job */
; ..\eeprom\NvM\NvM_JobProc.c	   482                      NVM_STATE_ELSEIF{NVM_QRY_ID_MAIN_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   483                          NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   484                          NVM_NEXT_STATE(NVM_STATE_MULTI_BLOCK_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   485                  }
; ..\eeprom\NvM\NvM_JobProc.c	   486              },
; ..\eeprom\NvM\NvM_JobProc.c	   487              /* final case: job fsm finished */
; ..\eeprom\NvM\NvM_JobProc.c	   488              {
; ..\eeprom\NvM\NvM_JobProc.c	   489                  NVM_STATE_ELSE{NVM_ACT_ID_FinishMainJob, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   490                  NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   491              }
; ..\eeprom\NvM\NvM_JobProc.c	   492          },
; ..\eeprom\NvM\NvM_JobProc.c	   493  # else
; ..\eeprom\NvM\NvM_JobProc.c	   494          /* not cancelable as there are no high priority jobs,
; ..\eeprom\NvM\NvM_JobProc.c	   495           * jobs do not need to be freed after processing */
; ..\eeprom\NvM\NvM_JobProc.c	   496          {
; ..\eeprom\NvM\NvM_JobProc.c	   497              {
; ..\eeprom\NvM\NvM_JobProc.c	   498                  {   /* WriteAll, but it was killed => Finish FSM, even if a high prio job might be waiting
; ..\eeprom\NvM\NvM_JobProc.c	   499                         It would be next job in "normal" processing - from IDLE */
; ..\eeprom\NvM\NvM_JobProc.c	   500                      NVM_STATE_IF{NVM_QR_ID_WRITEALL_KILLED, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   501                          NVM_STATE_THEN{NVM_ACT_ID_KillWritAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   502                          NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   503                  },
; ..\eeprom\NvM\NvM_JobProc.c	   504                  {   /* continue the running job */
; ..\eeprom\NvM\NvM_JobProc.c	   505                      NVM_STATE_ELSEIF{NVM_QRY_ID_MAIN_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   506                          NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   507                          NVM_NEXT_STATE(NVM_STATE_MULTI_BLOCK_JOB)
; ..\eeprom\NvM\NvM_JobProc.c	   508                  },
; ..\eeprom\NvM\NvM_JobProc.c	   509                  {   /* job finished */
; ..\eeprom\NvM\NvM_JobProc.c	   510                      NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   511                          NVM_STATE_THEN{NVM_ACT_ID_FinishMainJob, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   512                          NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   513                  },
; ..\eeprom\NvM\NvM_JobProc.c	   514              },
; ..\eeprom\NvM\NvM_JobProc.c	   515              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   516              {
; ..\eeprom\NvM\NvM_JobProc.c	   517                  NVM_STATE_ELSE{NVM_ACT_ID_FinishMainJob, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   518                  NVM_NEXT_STATE(NVM_STATE_IDLE)
; ..\eeprom\NvM\NvM_JobProc.c	   519              }
; ..\eeprom\NvM\NvM_JobProc.c	   520          },
; ..\eeprom\NvM\NvM_JobProc.c	   521  # endif /* Job prioritization */
; ..\eeprom\NvM\NvM_JobProc.c	   522  #endif /* NVM_KILL_WRITEALL_API */
; ..\eeprom\NvM\NvM_JobProc.c	   523  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   524   *  ReadBlock
; ..\eeprom\NvM\NvM_JobProc.c	   525   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   526      /* NVM_STATE_READ_READ_DATA */
; ..\eeprom\NvM\NvM_JobProc.c	   527      {
; ..\eeprom\NvM\NvM_JobProc.c	   528          {
; ..\eeprom\NvM\NvM_JobProc.c	   529              {
; ..\eeprom\NvM\NvM_JobProc.c	   530                  /* NV busy -> wait */
; ..\eeprom\NvM\NvM_JobProc.c	   531                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   532                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   533                      NVM_NEXT_STATE(NVM_STATE_READ_READ_DATA)
; ..\eeprom\NvM\NvM_JobProc.c	   534              },
; ..\eeprom\NvM\NvM_JobProc.c	   535              {
; ..\eeprom\NvM\NvM_JobProc.c	   536                  /* NV idle && OK && CRC calculation ongoing -> calculate the CRC */
; ..\eeprom\NvM\NvM_JobProc.c	   537                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_CRC_BUSY},
; ..\eeprom\NvM\NvM_JobProc.c	   538                      NVM_STATE_THEN{NVM_ACT_ID_ProcessCrcRead, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   539                      NVM_NEXT_STATE(NVM_STATE_READ_CMP_CRC)
; ..\eeprom\NvM\NvM_JobProc.c	   540              },
; ..\eeprom\NvM\NvM_JobProc.c	   541              {
; ..\eeprom\NvM\NvM_JobProc.c	   542                  /* NV idle && OK && no CRC -> take care about data validation */
; ..\eeprom\NvM\NvM_JobProc.c	   543                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   544                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   545                      NVM_NEXT_STATE(NVM_STATE_READ_DATA_VALIDATION)
; ..\eeprom\NvM\NvM_JobProc.c	   546              }
; ..\eeprom\NvM\NvM_JobProc.c	   547          },
; ..\eeprom\NvM\NvM_JobProc.c	   548          {
; ..\eeprom\NvM\NvM_JobProc.c	   549              /* NV idle && != OK -> try recovery */
; ..\eeprom\NvM\NvM_JobProc.c	   550              NVM_STATE_ELSE{NVM_ACT_ID_Nop,  NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   551              NVM_NEXT_STATE(NVM_STATE_READ_IMPL_RECOV)
; ..\eeprom\NvM\NvM_JobProc.c	   552          }
; ..\eeprom\NvM\NvM_JobProc.c	   553      },
; ..\eeprom\NvM\NvM_JobProc.c	   554      /* NVM_STATE_READ_DATA_VALIDATION */
; ..\eeprom\NvM\NvM_JobProc.c	   555      {
; ..\eeprom\NvM\NvM_JobProc.c	   556          {
; ..\eeprom\NvM\NvM_JobProc.c	   557              {
; ..\eeprom\NvM\NvM_JobProc.c	   558                  /* Decryption and data transformation -> if successful, data is valid, finalize job. */
; ..\eeprom\NvM\NvM_JobProc.c	   559                  NVM_STATE_IF{NVM_QRY_SYNCDECRYPT, NVM_QRY_POST_READ_TRANSFORM},
; ..\eeprom\NvM\NvM_JobProc.c	   560                      NVM_STATE_THEN{NVM_ACT_ID_ValidateRam, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   561                      NVM_NEXT_STATE(NVM_STATE_READ_FINALIZE)
; ..\eeprom\NvM\NvM_JobProc.c	   562              },
; ..\eeprom\NvM\NvM_JobProc.c	   563              {
; ..\eeprom\NvM\NvM_JobProc.c	   564                  /* Retry decryption job if configured and last job was not successful */
; ..\eeprom\NvM\NvM_JobProc.c	   565                  NVM_STATE_ELSEIF{NVM_QRY_CSM_RETRIES_NECESSARY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   566                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   567                      NVM_NEXT_STATE(NVM_STATE_READ_DATA_VALIDATION)
; ..\eeprom\NvM\NvM_JobProc.c	   568              },
; ..\eeprom\NvM\NvM_JobProc.c	   569              {
; ..\eeprom\NvM\NvM_JobProc.c	   570                  /* Decryption or transform job failed -> set result and try recovery */
; ..\eeprom\NvM\NvM_JobProc.c	   571                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   572                      NVM_STATE_THEN{NVM_ACT_ID_SetReqNotOk, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   573                      NVM_NEXT_STATE(NVM_STATE_READ_IMPL_RECOV)
; ..\eeprom\NvM\NvM_JobProc.c	   574              }
; ..\eeprom\NvM\NvM_JobProc.c	   575          },
; ..\eeprom\NvM\NvM_JobProc.c	   576          {
; ..\eeprom\NvM\NvM_JobProc.c	   577              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   578              NVM_STATE_ELSE{NVM_ACT_ID_Nop,  NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   579              NVM_NEXT_STATE(NVM_STATE_READ_IMPL_RECOV)
; ..\eeprom\NvM\NvM_JobProc.c	   580          }
; ..\eeprom\NvM\NvM_JobProc.c	   581      },
; ..\eeprom\NvM\NvM_JobProc.c	   582      /* NVM_STATE_READ_CMP_CRC */
; ..\eeprom\NvM\NvM_JobProc.c	   583      {
; ..\eeprom\NvM\NvM_JobProc.c	   584          {
; ..\eeprom\NvM\NvM_JobProc.c	   585              {
; ..\eeprom\NvM\NvM_JobProc.c	   586                  /* CRC calculation ongoing -> process next calculation step */
; ..\eeprom\NvM\NvM_JobProc.c	   587                  NVM_STATE_IF{NVM_QRY_ID_CRC_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   588                      NVM_STATE_THEN{NVM_ACT_ID_ProcessCrcRead, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   589                      NVM_NEXT_STATE(NVM_STATE_READ_CMP_CRC)
; ..\eeprom\NvM\NvM_JobProc.c	   590              },
; ..\eeprom\NvM\NvM_JobProc.c	   591              {
; ..\eeprom\NvM\NvM_JobProc.c	   592                  /* CRC calculated && matches -> go the data validation */
; ..\eeprom\NvM\NvM_JobProc.c	   593                  NVM_STATE_ELSEIF{NVM_QRY_ID_CRC_MATCH, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   594                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   595                      NVM_NEXT_STATE(NVM_STATE_READ_DATA_VALIDATION)
; ..\eeprom\NvM\NvM_JobProc.c	   596              },
; ..\eeprom\NvM\NvM_JobProc.c	   597              {
; ..\eeprom\NvM\NvM_JobProc.c	   598                  /* CRC calculated && mismatch -> try recovery */
; ..\eeprom\NvM\NvM_JobProc.c	   599                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   600                      NVM_STATE_THEN{NVM_ACT_ID_SetReqIntegrityFailed, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   601                      NVM_NEXT_STATE(NVM_STATE_READ_IMPL_RECOV)
; ..\eeprom\NvM\NvM_JobProc.c	   602              }
; ..\eeprom\NvM\NvM_JobProc.c	   603          },
; ..\eeprom\NvM\NvM_JobProc.c	   604          {
; ..\eeprom\NvM\NvM_JobProc.c	   605              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   606              NVM_STATE_ELSE{NVM_ACT_ID_SetReqIntegrityFailed, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   607              NVM_NEXT_STATE(NVM_STATE_READ_IMPL_RECOV)
; ..\eeprom\NvM\NvM_JobProc.c	   608          }
; ..\eeprom\NvM\NvM_JobProc.c	   609      },
; ..\eeprom\NvM\NvM_JobProc.c	   610      /* NVM_STATE_READ_IMPL_RECOV */
; ..\eeprom\NvM\NvM_JobProc.c	   611      {
; ..\eeprom\NvM\NvM_JobProc.c	   612          {
; ..\eeprom\NvM\NvM_JobProc.c	   613              {
; ..\eeprom\NvM\NvM_JobProc.c	   614                  /* Redundant block && the first sub block -> try the other one */
; ..\eeprom\NvM\NvM_JobProc.c	   615                  NVM_STATE_IF{NVM_QRY_ID_REDUNDANT_BLOCK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   616                      NVM_STATE_THEN{NVM_ACT_ID_SetupRedundant, NVM_ACT_ID_ReadNvBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   617                      NVM_NEXT_STATE(NVM_STATE_READ_READ_DATA)
; ..\eeprom\NvM\NvM_JobProc.c	   618              },
; ..\eeprom\NvM\NvM_JobProc.c	   619              {
; ..\eeprom\NvM\NvM_JobProc.c	   620                  /* Not redundant || the second sub block -> try to restore ROM */
; ..\eeprom\NvM\NvM_JobProc.c	   621                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   622                      NVM_STATE_THEN{NVM_ACT_ID_InitRestoreBlockDefaults, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   623                      NVM_NEXT_STATE(NVM_STATE_READ_LOAD_ROM)
; ..\eeprom\NvM\NvM_JobProc.c	   624              },
; ..\eeprom\NvM\NvM_JobProc.c	   625              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   626              {
; ..\eeprom\NvM\NvM_JobProc.c	   627                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   628                      NVM_STATE_THEN{NVM_ACT_ID_InitRestoreBlockDefaults, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   629                      NVM_NEXT_STATE(NVM_STATE_READ_LOAD_ROM)
; ..\eeprom\NvM\NvM_JobProc.c	   630              }
; ..\eeprom\NvM\NvM_JobProc.c	   631          },
; ..\eeprom\NvM\NvM_JobProc.c	   632          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   633          {
; ..\eeprom\NvM\NvM_JobProc.c	   634              NVM_STATE_THEN{NVM_ACT_ID_InitRestoreBlockDefaults, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   635              NVM_NEXT_STATE(NVM_STATE_READ_LOAD_ROM)
; ..\eeprom\NvM\NvM_JobProc.c	   636          }
; ..\eeprom\NvM\NvM_JobProc.c	   637      },
; ..\eeprom\NvM\NvM_JobProc.c	   638      /* NVM_STATE_READ_LOAD_ROM */
; ..\eeprom\NvM\NvM_JobProc.c	   639      {
; ..\eeprom\NvM\NvM_JobProc.c	   640          {
; ..\eeprom\NvM\NvM_JobProc.c	   641              {
; ..\eeprom\NvM\NvM_JobProc.c	   642                  /* Restoring pending -> next restore step
; ..\eeprom\NvM\NvM_JobProc.c	   643                     Don't use "ActWait"! It would cause a wait cycle, if we didn't anything (no ROM defaults). */
; ..\eeprom\NvM\NvM_JobProc.c	   644                  NVM_STATE_IF{NVM_QRY_ID_DATA_COPY_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   645                      NVM_STATE_THEN{NVM_ACT_ID_RestoreRomDefaults, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   646                      NVM_NEXT_STATE(NVM_STATE_READ_LOAD_ROM)
; ..\eeprom\NvM\NvM_JobProc.c	   647              },
; ..\eeprom\NvM\NvM_JobProc.c	   648              {
; ..\eeprom\NvM\NvM_JobProc.c	   649                  /* No ROM || default loaded -> finish restoring and the read */
; ..\eeprom\NvM\NvM_JobProc.c	   650                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   651                      NVM_STATE_THEN{NVM_ACT_ID_FinishRestoreRomDefaults, NVM_ACT_ID_FinishReadBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   652                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   653              },
; ..\eeprom\NvM\NvM_JobProc.c	   654              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   655              {
; ..\eeprom\NvM\NvM_JobProc.c	   656                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   657                      NVM_STATE_THEN{NVM_ACT_ID_FinishRestoreRomDefaults, NVM_ACT_ID_FinishReadBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   658                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   659              }
; ..\eeprom\NvM\NvM_JobProc.c	   660          },
; ..\eeprom\NvM\NvM_JobProc.c	   661          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   662          {
; ..\eeprom\NvM\NvM_JobProc.c	   663              NVM_STATE_THEN{NVM_ACT_ID_FinishRestoreRomDefaults, NVM_ACT_ID_FinishReadBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   664              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   665          }
; ..\eeprom\NvM\NvM_JobProc.c	   666      },
; ..\eeprom\NvM\NvM_JobProc.c	   667      /* NVM_STATE_READ_FINALIZE */
; ..\eeprom\NvM\NvM_JobProc.c	   668      {
; ..\eeprom\NvM\NvM_JobProc.c	   669          {
; ..\eeprom\NvM\NvM_JobProc.c	   670              {
; ..\eeprom\NvM\NvM_JobProc.c	   671                  /* data copy busy (e.g. explicit sync) -> process step and wait */
; ..\eeprom\NvM\NvM_JobProc.c	   672                  NVM_STATE_IF{NVM_QRY_ID_DATA_COPY_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   673                      NVM_STATE_THEN{NVM_ACT_ID_ReadCopyData, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   674                      NVM_NEXT_STATE(NVM_STATE_READ_FINALIZE)
; ..\eeprom\NvM\NvM_JobProc.c	   675              },
; ..\eeprom\NvM\NvM_JobProc.c	   676              {
; ..\eeprom\NvM\NvM_JobProc.c	   677                  /* data copy done -> finalize the read job */
; ..\eeprom\NvM\NvM_JobProc.c	   678                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   679                      NVM_STATE_THEN{NVM_ACT_ID_FinishReadBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   680                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   681              },
; ..\eeprom\NvM\NvM_JobProc.c	   682              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   683              {
; ..\eeprom\NvM\NvM_JobProc.c	   684                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   685                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   686                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   687              }
; ..\eeprom\NvM\NvM_JobProc.c	   688          },
; ..\eeprom\NvM\NvM_JobProc.c	   689          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   690          {
; ..\eeprom\NvM\NvM_JobProc.c	   691              NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   692              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   693          }
; ..\eeprom\NvM\NvM_JobProc.c	   694      },
; ..\eeprom\NvM\NvM_JobProc.c	   695  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   696   *  WriteBlock
; ..\eeprom\NvM\NvM_JobProc.c	   697   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   698      /* NVM_STATE_WRITE_INITIAL = initial state of Write Block */
; ..\eeprom\NvM\NvM_JobProc.c	   699      {
; ..\eeprom\NvM\NvM_JobProc.c	   700          {
; ..\eeprom\NvM\NvM_JobProc.c	   701              {   /* Create data. */
; ..\eeprom\NvM\NvM_JobProc.c	   702                  NVM_STATE_IF{NVM_QRY_ID_DATA_COPY_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   703                      NVM_STATE_THEN{NVM_ACT_ID_CopyNvDataToBuf, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   704                      NVM_NEXT_STATE(NVM_STATE_WRITE_INITIAL)
; ..\eeprom\NvM\NvM_JobProc.c	   705              },
; ..\eeprom\NvM\NvM_JobProc.c	   706              {   /* Encrypt data */
; ..\eeprom\NvM\NvM_JobProc.c	   707                  NVM_STATE_ELSEIF{NVM_QRY_SYNCENCRYPT, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   708                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   709                      NVM_NEXT_STATE(NVM_STATE_WRITE_CRCCALC)
; ..\eeprom\NvM\NvM_JobProc.c	   710              },
; ..\eeprom\NvM\NvM_JobProc.c	   711              {   /* Retry encryption in special cases. */
; ..\eeprom\NvM\NvM_JobProc.c	   712                  NVM_STATE_ELSEIF{NVM_QRY_CSM_RETRIES_NECESSARY, NVM_QRY_ID_TRUE },
; ..\eeprom\NvM\NvM_JobProc.c	   713                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   714                      NVM_NEXT_STATE(NVM_STATE_WRITE_INITIAL)
; ..\eeprom\NvM\NvM_JobProc.c	   715              }
; ..\eeprom\NvM\NvM_JobProc.c	   716          },
; ..\eeprom\NvM\NvM_JobProc.c	   717          {   /* Encryption job fails */
; ..\eeprom\NvM\NvM_JobProc.c	   718              NVM_STATE_ELSE{NVM_ACT_ID_SetReqNotOk, NVM_ACT_ID_FinishWriteBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   719              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   720          }
; ..\eeprom\NvM\NvM_JobProc.c	   721      },
; ..\eeprom\NvM\NvM_JobProc.c	   722      /* NVM_STATE_WRITE_CRCCALC */
; ..\eeprom\NvM\NvM_JobProc.c	   723      {
; ..\eeprom\NvM\NvM_JobProc.c	   724          {
; ..\eeprom\NvM\NvM_JobProc.c	   725              {   /* Calculate CRC */
; ..\eeprom\NvM\NvM_JobProc.c	   726                  NVM_STATE_ELSEIF{NVM_QRY_ID_CRC_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   727                      NVM_STATE_THEN{NVM_ACT_ID_ProcessCrc, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   728                      NVM_NEXT_STATE(NVM_STATE_WRITE_CRCCALC)
; ..\eeprom\NvM\NvM_JobProc.c	   729              },
; ..\eeprom\NvM\NvM_JobProc.c	   730              {   /* Redundant block -> go to next redundant block write state */
; ..\eeprom\NvM\NvM_JobProc.c	   731                  NVM_STATE_ELSEIF{NVM_QRY_ID_REDUNDANT_BLOCK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   732                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   733                      NVM_NEXT_STATE(NVM_STATE_WRITE_TEST_PRI_READ)
; ..\eeprom\NvM\NvM_JobProc.c	   734              },
; ..\eeprom\NvM\NvM_JobProc.c	   735              {   /* Native or dataset block -> go to the CRC compare check state */
; ..\eeprom\NvM\NvM_JobProc.c	   736                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   737                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   738                      NVM_NEXT_STATE(NVM_STATE_WRITE_CRCCOMPMECHANISM)
; ..\eeprom\NvM\NvM_JobProc.c	   739              }
; ..\eeprom\NvM\NvM_JobProc.c	   740          },
; ..\eeprom\NvM\NvM_JobProc.c	   741          {   /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   742              NVM_STATE_ELSE{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   743              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   744          }
; ..\eeprom\NvM\NvM_JobProc.c	   745      },
; ..\eeprom\NvM\NvM_JobProc.c	   746      /* NVM_STATE_WRITE_CRCCOMPMECHANISM */
; ..\eeprom\NvM\NvM_JobProc.c	   747      {
; ..\eeprom\NvM\NvM_JobProc.c	   748          {
; ..\eeprom\NvM\NvM_JobProc.c	   749              {   /* CRC calculated, check CRC compare mechanism */
; ..\eeprom\NvM\NvM_JobProc.c	   750                  NVM_STATE_IF{NvM_QRY_CRC_COMP_MECHANISM_SKIPWRITE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   751                      NVM_STATE_THEN{NVM_ACT_ID_FinishWriteBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   752                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   753              },
; ..\eeprom\NvM\NvM_JobProc.c	   754              {   /* CRC does not match or the CRC compare mechanism is disabled for current block -> write */
; ..\eeprom\NvM\NvM_JobProc.c	   755                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   756                      NVM_STATE_THEN{NVM_ACT_ID_WriteNvBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   757                      NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_2)
; ..\eeprom\NvM\NvM_JobProc.c	   758              },
; ..\eeprom\NvM\NvM_JobProc.c	   759              {   /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   760                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   761                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   762                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   763              }
; ..\eeprom\NvM\NvM_JobProc.c	   764          },
; ..\eeprom\NvM\NvM_JobProc.c	   765          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   766          {
; ..\eeprom\NvM\NvM_JobProc.c	   767              NVM_STATE_ELSE{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   768              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   769          }
; ..\eeprom\NvM\NvM_JobProc.c	   770      },
; ..\eeprom\NvM\NvM_JobProc.c	   771      /* NVM_STATE_WRITE_TEST_PRI_READ */
; ..\eeprom\NvM\NvM_JobProc.c	   772      {
; ..\eeprom\NvM\NvM_JobProc.c	   773          {
; ..\eeprom\NvM\NvM_JobProc.c	   774              {   /* read of primary NvBlock not finished -> wait */
; ..\eeprom\NvM\NvM_JobProc.c	   775                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   776                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   777                      NVM_NEXT_STATE(NVM_STATE_WRITE_TEST_PRI_READ)
; ..\eeprom\NvM\NvM_JobProc.c	   778              },
; ..\eeprom\NvM\NvM_JobProc.c	   779              {   /* result of primary NvBlock OK -> test secondary NvBlock */
; ..\eeprom\NvM\NvM_JobProc.c	   780                  NVM_STATE_IF{NVM_QRY_ID_REDUNDANT_BLOCK, NVM_QRY_ID_LAST_RESULT_OK},
; ..\eeprom\NvM\NvM_JobProc.c	   781                      NVM_STATE_THEN{NVM_ACT_ID_SetupOther, NVM_ACT_ID_TestBlockBlank},
; ..\eeprom\NvM\NvM_JobProc.c	   782                      NVM_NEXT_STATE(NVM_STATE_WRITE_TEST_SEC_READ)
; ..\eeprom\NvM\NvM_JobProc.c	   783              },
; ..\eeprom\NvM\NvM_JobProc.c	   784              {   /* result of primary NvBlock not OK -> write primary NvBlock first */
; ..\eeprom\NvM\NvM_JobProc.c	   785                  NVM_STATE_ELSEIF{NVM_QRY_ID_REDUNDANT_BLOCK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   786                      NVM_STATE_THEN{NVM_ACT_ID_WriteNvBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   787                      NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_1)
; ..\eeprom\NvM\NvM_JobProc.c	   788              }
; ..\eeprom\NvM\NvM_JobProc.c	   789          },
; ..\eeprom\NvM\NvM_JobProc.c	   790          {   /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   791              NVM_STATE_ELSE{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   792              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   793          }
; ..\eeprom\NvM\NvM_JobProc.c	   794      },
; ..\eeprom\NvM\NvM_JobProc.c	   795      /* NVM_STATE_WRITE_TEST_SEC_READ */
; ..\eeprom\NvM\NvM_JobProc.c	   796      {
; ..\eeprom\NvM\NvM_JobProc.c	   797          {
; ..\eeprom\NvM\NvM_JobProc.c	   798              {   /* read of secondary NvBlock not finished -> wait */
; ..\eeprom\NvM\NvM_JobProc.c	   799                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   800                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   801                      NVM_NEXT_STATE(NVM_STATE_WRITE_TEST_SEC_READ)
; ..\eeprom\NvM\NvM_JobProc.c	   802              },
; ..\eeprom\NvM\NvM_JobProc.c	   803              {   /* Both blocks readable, CRC matches -> no write, finalize job */
; ..\eeprom\NvM\NvM_JobProc.c	   804                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NvM_QRY_CRC_COMP_MECHANISM_SKIPWRITE},
; ..\eeprom\NvM\NvM_JobProc.c	   805                     NVM_STATE_THEN{NVM_ACT_ID_FinishWriteBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   806                     NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   807              },
; ..\eeprom\NvM\NvM_JobProc.c	   808              {   /* result of secondary NvBlock OK -> write primary NvBlock first */
; ..\eeprom\NvM\NvM_JobProc.c	   809                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   810                      NVM_STATE_THEN{NVM_ACT_ID_SetupOther, NVM_ACT_ID_WriteNvBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   811                      NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_1)
; ..\eeprom\NvM\NvM_JobProc.c	   812              }
; ..\eeprom\NvM\NvM_JobProc.c	   813          },
; ..\eeprom\NvM\NvM_JobProc.c	   814          /* result of secondary NvBlock not OK -> write secondary NvBlock first */
; ..\eeprom\NvM\NvM_JobProc.c	   815          {
; ..\eeprom\NvM\NvM_JobProc.c	   816              NVM_STATE_ELSE{NVM_ACT_ID_WriteNvBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   817              NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_1)
; ..\eeprom\NvM\NvM_JobProc.c	   818          }
; ..\eeprom\NvM\NvM_JobProc.c	   819      },
; ..\eeprom\NvM\NvM_JobProc.c	   820  
; ..\eeprom\NvM\NvM_JobProc.c	   821      /* NVM_STATE_WRITE_WR_DATA_CRC_1 */
; ..\eeprom\NvM\NvM_JobProc.c	   822      {
; ..\eeprom\NvM\NvM_JobProc.c	   823          {
; ..\eeprom\NvM\NvM_JobProc.c	   824              {   /* nv write not finished -> wait */
; ..\eeprom\NvM\NvM_JobProc.c	   825                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   826                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   827                      NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_1)
; ..\eeprom\NvM\NvM_JobProc.c	   828              },
; ..\eeprom\NvM\NvM_JobProc.c	   829              {   /* result ok -> write other NvBlock */
; ..\eeprom\NvM\NvM_JobProc.c	   830                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   831                      NVM_STATE_THEN{NVM_ACT_ID_SetupOther, NVM_ACT_ID_WriteNvBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   832                      NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_2)
; ..\eeprom\NvM\NvM_JobProc.c	   833              },
; ..\eeprom\NvM\NvM_JobProc.c	   834              {   /* result not ok, write retries exceeded -> write other NvBlock */
; ..\eeprom\NvM\NvM_JobProc.c	   835                  NVM_STATE_ELSEIF{NVM_QRY_ID_WRITE_RETRIES_EXCEEDED, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   836                      NVM_STATE_THEN{NVM_ACT_ID_SetupOther, NVM_ACT_ID_WriteNvBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   837                      NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_2)
; ..\eeprom\NvM\NvM_JobProc.c	   838              }
; ..\eeprom\NvM\NvM_JobProc.c	   839          },
; ..\eeprom\NvM\NvM_JobProc.c	   840          {   /* result not ok -> retry */
; ..\eeprom\NvM\NvM_JobProc.c	   841              NVM_STATE_ELSE{NVM_ACT_ID_WriteNvBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   842              NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_1)
; ..\eeprom\NvM\NvM_JobProc.c	   843          }
; ..\eeprom\NvM\NvM_JobProc.c	   844      },
; ..\eeprom\NvM\NvM_JobProc.c	   845      /* NVM_STATE_WRITE_WR_DATA_CRC_2 */
; ..\eeprom\NvM\NvM_JobProc.c	   846      {
; ..\eeprom\NvM\NvM_JobProc.c	   847          {
; ..\eeprom\NvM\NvM_JobProc.c	   848              {   /* nv write not finished -> wait */
; ..\eeprom\NvM\NvM_JobProc.c	   849                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   850                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   851                      NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_2)
; ..\eeprom\NvM\NvM_JobProc.c	   852              },
; ..\eeprom\NvM\NvM_JobProc.c	   853              {   /* result ok -> update NvState and finish block */
; ..\eeprom\NvM\NvM_JobProc.c	   854                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   855                      NVM_STATE_THEN{NVM_ACT_ID_FinishWriteBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   856                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   857              },
; ..\eeprom\NvM\NvM_JobProc.c	   858              {   /* result not ok, retries exceeded -> update NvState and finish block */
; ..\eeprom\NvM\NvM_JobProc.c	   859                  NVM_STATE_ELSEIF{NVM_QRY_ID_WRITE_RETRIES_EXCEEDED, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   860                      NVM_STATE_THEN{NVM_ACT_ID_FinishWriteBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   861                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   862              }
; ..\eeprom\NvM\NvM_JobProc.c	   863          },
; ..\eeprom\NvM\NvM_JobProc.c	   864          {   /* result not ok -> retry */
; ..\eeprom\NvM\NvM_JobProc.c	   865              NVM_STATE_ELSE{NVM_ACT_ID_WriteNvBlock, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   866              NVM_NEXT_STATE(NVM_STATE_WRITE_WR_DATA_CRC_2)
; ..\eeprom\NvM\NvM_JobProc.c	   867          }
; ..\eeprom\NvM\NvM_JobProc.c	   868      },    
; ..\eeprom\NvM\NvM_JobProc.c	   869  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   870   *  RestoreBlockDefaults
; ..\eeprom\NvM\NvM_JobProc.c	   871   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   872      /* NVM_STATE_RESTORE_LOAD_ROM */
; ..\eeprom\NvM\NvM_JobProc.c	   873      {
; ..\eeprom\NvM\NvM_JobProc.c	   874          {
; ..\eeprom\NvM\NvM_JobProc.c	   875              {   /* copy(!) busy -> continue processing */
; ..\eeprom\NvM\NvM_JobProc.c	   876                  NVM_STATE_IF{NVM_QRY_ID_DATA_COPY_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   877                      NVM_STATE_THEN{NVM_ACT_ID_RestoreRomDefaults, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   878                      NVM_NEXT_STATE(NVM_STATE_RESTORE_LOAD_ROM)
; ..\eeprom\NvM\NvM_JobProc.c	   879              },
; ..\eeprom\NvM\NvM_JobProc.c	   880              {   /* copy completed */
; ..\eeprom\NvM\NvM_JobProc.c	   881                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   882                      NVM_STATE_THEN{NVM_ACT_ID_FinishRestoreRomDefaults, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   883                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   884              },
; ..\eeprom\NvM\NvM_JobProc.c	   885              {   /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   886                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   887                      NVM_STATE_THEN{NVM_ACT_ID_FinishRestoreRomDefaults, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   888                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   889              }
; ..\eeprom\NvM\NvM_JobProc.c	   890          },
; ..\eeprom\NvM\NvM_JobProc.c	   891          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   892          {
; ..\eeprom\NvM\NvM_JobProc.c	   893              NVM_STATE_ELSE{NVM_ACT_ID_FinishRestoreRomDefaults, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   894              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   895          }
; ..\eeprom\NvM\NvM_JobProc.c	   896      },
; ..\eeprom\NvM\NvM_JobProc.c	   897  #if(NVM_API_CONFIG_CLASS == NVM_API_CONFIG_CLASS_3)
; ..\eeprom\NvM\NvM_JobProc.c	   898  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   899   *  InvalidateNvBlock
; ..\eeprom\NvM\NvM_JobProc.c	   900   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   901      /* NVM_STATE_INVALIDATING_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	   902      {
; ..\eeprom\NvM\NvM_JobProc.c	   903          {
; ..\eeprom\NvM\NvM_JobProc.c	   904              {   /* nv busy -> wait; if not busy LastResult is set */
; ..\eeprom\NvM\NvM_JobProc.c	   905                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   906                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   907                      NVM_NEXT_STATE(NVM_STATE_INVALIDATING_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	   908              },
; ..\eeprom\NvM\NvM_JobProc.c	   909              {   /* nv ready, result okay, redundant -> invalidate redundant */
; ..\eeprom\NvM\NvM_JobProc.c	   910                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_REDUNDANT_BLOCK},
; ..\eeprom\NvM\NvM_JobProc.c	   911                      NVM_STATE_THEN{NVM_ACT_ID_SetupRedundant, NVM_ACT_ID_InvalidateNvBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   912                      NVM_NEXT_STATE(NVM_STATE_INVALIDATING_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	   913              },
; ..\eeprom\NvM\NvM_JobProc.c	   914              {   /* nv ready and (not redundant or result not ok) -> finish; result is already set */
; ..\eeprom\NvM\NvM_JobProc.c	   915                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   916                      NVM_STATE_THEN{NVM_ACT_ID_UpdateNvState, NVM_ACT_ID_FinishEraseBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   917                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   918              }
; ..\eeprom\NvM\NvM_JobProc.c	   919          },
; ..\eeprom\NvM\NvM_JobProc.c	   920          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   921          {
; ..\eeprom\NvM\NvM_JobProc.c	   922              NVM_STATE_ELSE{NVM_ACT_ID_UpdateNvState, NVM_ACT_ID_FinishEraseBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   923              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   924          }
; ..\eeprom\NvM\NvM_JobProc.c	   925      },
; ..\eeprom\NvM\NvM_JobProc.c	   926  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   927   *  EraseNvBlock
; ..\eeprom\NvM\NvM_JobProc.c	   928   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   929      /* NVM_STATE_ERASE_ERASE_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	   930      {
; ..\eeprom\NvM\NvM_JobProc.c	   931          {
; ..\eeprom\NvM\NvM_JobProc.c	   932              {   /* nv busy -> wait */
; ..\eeprom\NvM\NvM_JobProc.c	   933                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   934                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   935                      NVM_NEXT_STATE(NVM_STATE_ERASE_ERASE_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	   936              },
; ..\eeprom\NvM\NvM_JobProc.c	   937              {   /* nv ready, result ok, redundant -> erase redundant */
; ..\eeprom\NvM\NvM_JobProc.c	   938                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_REDUNDANT_BLOCK},
; ..\eeprom\NvM\NvM_JobProc.c	   939                      NVM_STATE_THEN{NVM_ACT_ID_SetupRedundant, NVM_ACT_ID_EraseNvBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   940                      NVM_NEXT_STATE(NVM_STATE_ERASE_ERASE_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	   941              },
; ..\eeprom\NvM\NvM_JobProc.c	   942              {   /* nv ready, not redundant or result not ok -> finish (LastResult was previously set) */
; ..\eeprom\NvM\NvM_JobProc.c	   943                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   944                      NVM_STATE_THEN{NVM_ACT_ID_UpdateNvState, NVM_ACT_ID_FinishEraseBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   945                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   946              }
; ..\eeprom\NvM\NvM_JobProc.c	   947          },
; ..\eeprom\NvM\NvM_JobProc.c	   948          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   949          {
; ..\eeprom\NvM\NvM_JobProc.c	   950              NVM_STATE_ELSE{NVM_ACT_ID_UpdateNvState, NVM_ACT_ID_FinishEraseBlock},
; ..\eeprom\NvM\NvM_JobProc.c	   951              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   952          }
; ..\eeprom\NvM\NvM_JobProc.c	   953      },
; ..\eeprom\NvM\NvM_JobProc.c	   954  #endif
; ..\eeprom\NvM\NvM_JobProc.c	   955  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	   956   *  ReadAll
; ..\eeprom\NvM\NvM_JobProc.c	   957   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	   958      /* NVM_STATE_READALL_PROC_CONFIG_ID = analyze Config NV Block */
; ..\eeprom\NvM\NvM_JobProc.c	   959      {
; ..\eeprom\NvM\NvM_JobProc.c	   960          {
; ..\eeprom\NvM\NvM_JobProc.c	   961              {   /* sub fsm still running? */
; ..\eeprom\NvM\NvM_JobProc.c	   962                  NVM_STATE_IF{NVM_QRY_ID_SUB_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   963                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   964                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_CONFIG_ID)
; ..\eeprom\NvM\NvM_JobProc.c	   965              },
; ..\eeprom\NvM\NvM_JobProc.c	   966              {   /* sub fsm read finished */
; ..\eeprom\NvM\NvM_JobProc.c	   967                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   968                      NVM_STATE_THEN{NVM_ACT_ID_FinishCfgIdCheck, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	   969                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	   970              },
; ..\eeprom\NvM\NvM_JobProc.c	   971              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   972              {
; ..\eeprom\NvM\NvM_JobProc.c	   973                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   974                      NVM_STATE_THEN{NVM_ACT_ID_FinishCfgIdCheck, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	   975                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	   976              }
; ..\eeprom\NvM\NvM_JobProc.c	   977          },
; ..\eeprom\NvM\NvM_JobProc.c	   978          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	   979          {
; ..\eeprom\NvM\NvM_JobProc.c	   980              NVM_STATE_ELSE{NVM_ACT_ID_FinishCfgIdCheck, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	   981              NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	   982          }
; ..\eeprom\NvM\NvM_JobProc.c	   983      },
; ..\eeprom\NvM\NvM_JobProc.c	   984  
; ..\eeprom\NvM\NvM_JobProc.c	   985  #if(NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	   986      /* NVM_STATE_READALL_PROC_RAM_BLOCK = analyze Ram Block */
; ..\eeprom\NvM\NvM_JobProc.c	   987      {
; ..\eeprom\NvM\NvM_JobProc.c	   988          {
; ..\eeprom\NvM\NvM_JobProc.c	   989              {   /* last block finished -> end read all */
; ..\eeprom\NvM\NvM_JobProc.c	   990                  NVM_STATE_IF{NVM_QRY_ID_LAST_BLOCK_DONE_READALL, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	   991                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	   992                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	   993              },
; ..\eeprom\NvM\NvM_JobProc.c	   994              {   /* last block not finished, check valid/crc
; ..\eeprom\NvM\NvM_JobProc.c	   995                     => if the block does not have CRC, this transition will never be taken, because CRC won't be busy */
; ..\eeprom\NvM\NvM_JobProc.c	   996                  NVM_STATE_ELSEIF{NVM_QRY_ID_RAM_VALID, NVM_QRY_ID_CRC_BUSY},
; ..\eeprom\NvM\NvM_JobProc.c	   997                      NVM_STATE_THEN{NVM_ACT_ID_ProcessCrc, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	   998                      NVM_NEXT_STATE(NVM_STATE_READALL_CHK_RAM_VALIDITY)
; ..\eeprom\NvM\NvM_JobProc.c	   999              },
; ..\eeprom\NvM\NvM_JobProc.c	  1000              {   /* not valid or no crc && KillReadAll */
; ..\eeprom\NvM\NvM_JobProc.c	  1001                  NVM_STATE_ELSEIF{NVM_QRY_READALL_KILLED, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1002                      NVM_STATE_THEN{NVM_ACT_ID_SetInitialAttr, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1003                      NVM_NEXT_STATE(NVM_STATE_READALL_KILLED)
; ..\eeprom\NvM\NvM_JobProc.c	  1004              }
; ..\eeprom\NvM\NvM_JobProc.c	  1005          },
; ..\eeprom\NvM\NvM_JobProc.c	  1006          /* not valid or no crc */
; ..\eeprom\NvM\NvM_JobProc.c	  1007          {
; ..\eeprom\NvM\NvM_JobProc.c	  1008              NVM_STATE_ELSE{NVM_ACT_ID_SetInitialAttr, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1009              NVM_NEXT_STATE(NVM_STATE_READALL_CHK_SKIP)
; ..\eeprom\NvM\NvM_JobProc.c	  1010          }
; ..\eeprom\NvM\NvM_JobProc.c	  1011      },
; ..\eeprom\NvM\NvM_JobProc.c	  1012  #else
; ..\eeprom\NvM\NvM_JobProc.c	  1013      /* NVM_STATE_READALL_PROC_RAM_BLOCK = analyze Ram Block */
; ..\eeprom\NvM\NvM_JobProc.c	  1014      {
; ..\eeprom\NvM\NvM_JobProc.c	  1015          {
; ..\eeprom\NvM\NvM_JobProc.c	  1016              {   /* last block finished -> end read all */
; ..\eeprom\NvM\NvM_JobProc.c	  1017                  NVM_STATE_IF{NVM_QRY_ID_LAST_BLOCK_DONE_READALL, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1018                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1019                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1020              },
; ..\eeprom\NvM\NvM_JobProc.c	  1021              {   /* last block not finished && KillReadAll */
; ..\eeprom\NvM\NvM_JobProc.c	  1022                  NVM_STATE_ELSEIF{NVM_QRY_READALL_KILLED, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1023                      NVM_STATE_THEN{NVM_ACT_ID_SetInitialAttr, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1024                      NVM_NEXT_STATE(NVM_STATE_READALL_KILLED)
; ..\eeprom\NvM\NvM_JobProc.c	  1025              },
; ..\eeprom\NvM\NvM_JobProc.c	  1026              /* last block not finished, we don't check the block's validity */
; ..\eeprom\NvM\NvM_JobProc.c	  1027              {
; ..\eeprom\NvM\NvM_JobProc.c	  1028                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1029                      NVM_STATE_THEN{NVM_ACT_ID_SetInitialAttr, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1030                      NVM_NEXT_STATE(NVM_STATE_READALL_CHK_SKIP)
; ..\eeprom\NvM\NvM_JobProc.c	  1031              }
; ..\eeprom\NvM\NvM_JobProc.c	  1032          },
; ..\eeprom\NvM\NvM_JobProc.c	  1033          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1034          {
; ..\eeprom\NvM\NvM_JobProc.c	  1035              NVM_STATE_ELSE{NVM_ACT_ID_SetInitialAttr, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1036              NVM_NEXT_STATE(NVM_STATE_READALL_CHK_SKIP)
; ..\eeprom\NvM\NvM_JobProc.c	  1037          }
; ..\eeprom\NvM\NvM_JobProc.c	  1038      },
; ..\eeprom\NvM\NvM_JobProc.c	  1039  #endif
; ..\eeprom\NvM\NvM_JobProc.c	  1040      /* NVM_STATE_READALL_CHK_SKIP */
; ..\eeprom\NvM\NvM_JobProc.c	  1041      {
; ..\eeprom\NvM\NvM_JobProc.c	  1042          {
; ..\eeprom\NvM\NvM_JobProc.c	  1043              {   /* WriteOnce -> check readability to set the write protection */
; ..\eeprom\NvM\NvM_JobProc.c	  1044                  NVM_STATE_IF{NVM_QRY_ID_SKIP_BLOCK, NVM_QRY_ID_WRITE_BLOCK_ONCE},
; ..\eeprom\NvM\NvM_JobProc.c	  1045                      NVM_STATE_THEN{NVM_ACT_ID_TestBlockBlank, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1046                      NVM_NEXT_STATE(NVM_STATE_READALL_READABILITY_CHECK)
; ..\eeprom\NvM\NvM_JobProc.c	  1047              },
; ..\eeprom\NvM\NvM_JobProc.c	  1048              {   /* not WriteOnce, no read, skip and continue with next block */
; ..\eeprom\NvM\NvM_JobProc.c	  1049                  NVM_STATE_ELSEIF{NVM_QRY_ID_SKIP_BLOCK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1050                      NVM_STATE_THEN{NVM_ACT_ID_SetReqSkipped, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	  1051                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1052              },
; ..\eeprom\NvM\NvM_JobProc.c	  1053              {   /* no skip && extended runtime preparation -> restore ROM defaults, no read */
; ..\eeprom\NvM\NvM_JobProc.c	  1054                  NVM_STATE_ELSEIF{NVM_QRY_ID_EXT_RUNTIME, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1055                      NVM_STATE_THEN{NVM_ACT_ID_InitRestoreBlockDefaultsSubFsm, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1056                      NVM_NEXT_STATE(NVM_STATE_READALL_LOAD_DEFAULTS)
; ..\eeprom\NvM\NvM_JobProc.c	  1057              }
; ..\eeprom\NvM\NvM_JobProc.c	  1058          },
; ..\eeprom\NvM\NvM_JobProc.c	  1059          /* no skip && normal runtime preparation -> read from NV RAM */
; ..\eeprom\NvM\NvM_JobProc.c	  1060          {
; ..\eeprom\NvM\NvM_JobProc.c	  1061              NVM_STATE_ELSE{NVM_ACT_ID_InitReadBlockSubFsm, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1062              NVM_NEXT_STATE(NVM_STATE_READALL_READ_NV)
; ..\eeprom\NvM\NvM_JobProc.c	  1063          }
; ..\eeprom\NvM\NvM_JobProc.c	  1064      },
; ..\eeprom\NvM\NvM_JobProc.c	  1065      /* NVM_STATE_READALL_KILLED */
; ..\eeprom\NvM\NvM_JobProc.c	  1066      {
; ..\eeprom\NvM\NvM_JobProc.c	  1067          {
; ..\eeprom\NvM\NvM_JobProc.c	  1068              { /* Block shall be skipped, do not load default data! */
; ..\eeprom\NvM\NvM_JobProc.c	  1069                  NVM_STATE_ELSEIF{NVM_QRY_ID_SKIP_BLOCK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1070                      NVM_STATE_THEN{NVM_ACT_ID_SetReqSkipped, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	  1071                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1072              },
; ..\eeprom\NvM\NvM_JobProc.c	  1073              { /* block shall be processed -> load default data. */
; ..\eeprom\NvM\NvM_JobProc.c	  1074                  NVM_STATE_IF{NVM_QRY_ID_HAS_ROM, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1075                      NVM_STATE_THEN{NVM_ACT_ID_SetReqOk, NVM_ACT_ID_InitRestoreBlockDefaultsSubFsm},
; ..\eeprom\NvM\NvM_JobProc.c	  1076                      NVM_NEXT_STATE(NVM_STATE_READALL_LOAD_DEFAULTS)
; ..\eeprom\NvM\NvM_JobProc.c	  1077              },
; ..\eeprom\NvM\NvM_JobProc.c	  1078              { /* block shall be processed but does not have ROM -> skip block */
; ..\eeprom\NvM\NvM_JobProc.c	  1079                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1080                      NVM_STATE_THEN{NVM_ACT_ID_SetReqSkipped, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	  1081                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1082              }
; ..\eeprom\NvM\NvM_JobProc.c	  1083          },
; ..\eeprom\NvM\NvM_JobProc.c	  1084          { /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1085              NVM_STATE_ELSE{NVM_ACT_ID_SetReqSkipped, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	  1086              NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1087          }
; ..\eeprom\NvM\NvM_JobProc.c	  1088      },
; ..\eeprom\NvM\NvM_JobProc.c	  1089      /* NVM_STATE_READALL_WR_ONCE_PROT = a write once block shall be skipped */
; ..\eeprom\NvM\NvM_JobProc.c	  1090      {
; ..\eeprom\NvM\NvM_JobProc.c	  1091          {
; ..\eeprom\NvM\NvM_JobProc.c	  1092              {   /* byte was read - result was ok --> next block, please */
; ..\eeprom\NvM\NvM_JobProc.c	  1093                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1094                      NVM_STATE_THEN{NVM_ACT_ID_FinishReadBlockAndSetSkipped, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	  1095                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1096              },
; ..\eeprom\NvM\NvM_JobProc.c	  1097              {   /* result not ok, redundant -> try redundant */
; ..\eeprom\NvM\NvM_JobProc.c	  1098                  NVM_STATE_ELSEIF{NVM_QRY_ID_REDUNDANT_BLOCK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1099                      NVM_STATE_THEN{NVM_ACT_ID_SetupRedundant, NVM_ACT_ID_TestBlockBlank},
; ..\eeprom\NvM\NvM_JobProc.c	  1100                      NVM_NEXT_STATE(NVM_STATE_READALL_READABILITY_CHECK)
; ..\eeprom\NvM\NvM_JobProc.c	  1101              },
; ..\eeprom\NvM\NvM_JobProc.c	  1102              {   /* not redundant or done, result not ok. */
; ..\eeprom\NvM\NvM_JobProc.c	  1103                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1104                      NVM_STATE_THEN{NVM_ACT_ID_FinishReadBlockAndSetSkipped, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	  1105                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1106              }
; ..\eeprom\NvM\NvM_JobProc.c	  1107          },
; ..\eeprom\NvM\NvM_JobProc.c	  1108          {   /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1109              NVM_STATE_ELSE{NVM_ACT_ID_FinishReadBlockAndSetSkipped, NVM_ACT_ID_InitNextBlockReadAll},
; ..\eeprom\NvM\NvM_JobProc.c	  1110              NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1111          }
; ..\eeprom\NvM\NvM_JobProc.c	  1112      },
; ..\eeprom\NvM\NvM_JobProc.c	  1113  #if(NVM_SET_RAM_BLOCK_STATUS_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	  1114      /* NVM_STATE_READALL_CHK_RAM_VALIDITY = analyze Ram Block */
; ..\eeprom\NvM\NvM_JobProc.c	  1115      {
; ..\eeprom\NvM\NvM_JobProc.c	  1116          {
; ..\eeprom\NvM\NvM_JobProc.c	  1117              {   /* Crc not ready -> Wait */
; ..\eeprom\NvM\NvM_JobProc.c	  1118                  NVM_STATE_IF{NVM_QRY_ID_CRC_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1119                      NVM_STATE_THEN{NVM_ACT_ID_ProcessCrc, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	  1120                      NVM_NEXT_STATE(NVM_STATE_READALL_CHK_RAM_VALIDITY)
; ..\eeprom\NvM\NvM_JobProc.c	  1121              },
; ..\eeprom\NvM\NvM_JobProc.c	  1122              {   /* crc ready, crc match -> finish block and use current attributes */
; ..\eeprom\NvM\NvM_JobProc.c	  1123                  NVM_STATE_ELSEIF{NVM_QRY_ID_CRC_MATCH, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1124                      NVM_STATE_THEN{NVM_ACT_ID_InitNextBlockReadAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1125                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1126              },
; ..\eeprom\NvM\NvM_JobProc.c	  1127              {   /* crc mismatch && KillReadAll */
; ..\eeprom\NvM\NvM_JobProc.c	  1128                  NVM_STATE_ELSEIF{NVM_QRY_READALL_KILLED, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1129                      NVM_STATE_THEN{NVM_ACT_ID_SetInitialAttr, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1130                      NVM_NEXT_STATE(NVM_STATE_READALL_KILLED)
; ..\eeprom\NvM\NvM_JobProc.c	  1131              }
; ..\eeprom\NvM\NvM_JobProc.c	  1132          },
; ..\eeprom\NvM\NvM_JobProc.c	  1133          /* crc mismatch */
; ..\eeprom\NvM\NvM_JobProc.c	  1134          {
; ..\eeprom\NvM\NvM_JobProc.c	  1135              NVM_STATE_ELSE{NVM_ACT_ID_SetInitialAttr, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1136              NVM_NEXT_STATE(NVM_STATE_READALL_CHK_SKIP)
; ..\eeprom\NvM\NvM_JobProc.c	  1137          }
; ..\eeprom\NvM\NvM_JobProc.c	  1138      },
; ..\eeprom\NvM\NvM_JobProc.c	  1139  #endif
; ..\eeprom\NvM\NvM_JobProc.c	  1140      /* NVM_STATE_READALL_READ_NV */
; ..\eeprom\NvM\NvM_JobProc.c	  1141      {   /* At this point the underlying module is processing or processed the job. The job handling
; ..\eeprom\NvM\NvM_JobProc.c	  1142             will be done by the sub FSM -> cancel the busy NV */
; ..\eeprom\NvM\NvM_JobProc.c	  1143          {
; ..\eeprom\NvM\NvM_JobProc.c	  1144              {   /* NV busy -> cancel and abort sub FSM */
; ..\eeprom\NvM\NvM_JobProc.c	  1145                  NVM_STATE_ELSEIF{NVM_QRY_ID_NV_BUSY, NVM_QRY_READALL_KILLED},
; ..\eeprom\NvM\NvM_JobProc.c	  1146                      NVM_STATE_THEN{NVM_ACT_ID_CancelNV, NVM_ACT_ID_KillSubFsm},
; ..\eeprom\NvM\NvM_JobProc.c	  1147                      NVM_NEXT_STATE(NVM_STATE_READALL_KILLED)
; ..\eeprom\NvM\NvM_JobProc.c	  1148              },
; ..\eeprom\NvM\NvM_JobProc.c	  1149              {   /* NV idle, only internal processing -> do nothing */
; ..\eeprom\NvM\NvM_JobProc.c	  1150                  NVM_STATE_IF{NVM_QRY_ID_SUB_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1151                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1152                      NVM_NEXT_STATE(NVM_STATE_READALL_READ_NV)
; ..\eeprom\NvM\NvM_JobProc.c	  1153              },
; ..\eeprom\NvM\NvM_JobProc.c	  1154              {   /* sub fsm finished */
; ..\eeprom\NvM\NvM_JobProc.c	  1155                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1156                      NVM_STATE_THEN{NVM_ACT_ID_InitNextBlockReadAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1157                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1158              }
; ..\eeprom\NvM\NvM_JobProc.c	  1159          },
; ..\eeprom\NvM\NvM_JobProc.c	  1160          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1161          {
; ..\eeprom\NvM\NvM_JobProc.c	  1162              NVM_STATE_ELSE{NVM_ACT_ID_InitNextBlockReadAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1163              NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1164          }
; ..\eeprom\NvM\NvM_JobProc.c	  1165      },
; ..\eeprom\NvM\NvM_JobProc.c	  1166      /* NVM_STATE_READALL_LOAD_DEFAULTS */
; ..\eeprom\NvM\NvM_JobProc.c	  1167      {   /* only internal, no KillReadAll handling necessary! */
; ..\eeprom\NvM\NvM_JobProc.c	  1168          {
; ..\eeprom\NvM\NvM_JobProc.c	  1169              {   /* read in process -> do nothing */
; ..\eeprom\NvM\NvM_JobProc.c	  1170                  NVM_STATE_IF{NVM_QRY_ID_SUB_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1171                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1172                      NVM_NEXT_STATE(NVM_STATE_READALL_LOAD_DEFAULTS)
; ..\eeprom\NvM\NvM_JobProc.c	  1173              },
; ..\eeprom\NvM\NvM_JobProc.c	  1174              {   /* sub fsm finished */
; ..\eeprom\NvM\NvM_JobProc.c	  1175                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1176                      NVM_STATE_THEN{NVM_ACT_ID_InitNextBlockReadAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1177                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1178              },
; ..\eeprom\NvM\NvM_JobProc.c	  1179              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1180              {
; ..\eeprom\NvM\NvM_JobProc.c	  1181                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1182                      NVM_STATE_THEN{NVM_ACT_ID_InitNextBlockReadAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1183                      NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1184              }
; ..\eeprom\NvM\NvM_JobProc.c	  1185          },
; ..\eeprom\NvM\NvM_JobProc.c	  1186          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1187          {
; ..\eeprom\NvM\NvM_JobProc.c	  1188              NVM_STATE_ELSE{NVM_ACT_ID_InitNextBlockReadAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1189              NVM_NEXT_STATE(NVM_STATE_READALL_PROC_RAM_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1190          }
; ..\eeprom\NvM\NvM_JobProc.c	  1191      },
; ..\eeprom\NvM\NvM_JobProc.c	  1192      /* NVM_STATE_READALL_READABILITY_CHECK */
; ..\eeprom\NvM\NvM_JobProc.c	  1193      {   /* at this point the underlying module is processing or processed the job */
; ..\eeprom\NvM\NvM_JobProc.c	  1194          {
; ..\eeprom\NvM\NvM_JobProc.c	  1195              {   /* NV busy && KillReadAll -> cancel underlying device */
; ..\eeprom\NvM\NvM_JobProc.c	  1196                  NVM_STATE_ELSEIF{NVM_QRY_ID_NV_BUSY, NVM_QRY_READALL_KILLED},
; ..\eeprom\NvM\NvM_JobProc.c	  1197                        NVM_STATE_THEN{NVM_ACT_ID_CancelNV, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1198                        NVM_NEXT_STATE(NVM_STATE_READALL_KILLED)
; ..\eeprom\NvM\NvM_JobProc.c	  1199              },
; ..\eeprom\NvM\NvM_JobProc.c	  1200              {   /* one byte was read - wait for MemHwA to finish this read job */
; ..\eeprom\NvM\NvM_JobProc.c	  1201                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1202                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1203                      NVM_NEXT_STATE(NVM_STATE_READALL_READABILITY_CHECK)
; ..\eeprom\NvM\NvM_JobProc.c	  1204              },
; ..\eeprom\NvM\NvM_JobProc.c	  1205              {   /* reading done, check the block state */
; ..\eeprom\NvM\NvM_JobProc.c	  1206                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1207                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1208                      NVM_NEXT_STATE(NVM_STATE_READALL_WR_ONCE_PROT)
; ..\eeprom\NvM\NvM_JobProc.c	  1209              }
; ..\eeprom\NvM\NvM_JobProc.c	  1210          },
; ..\eeprom\NvM\NvM_JobProc.c	  1211          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1212          {
; ..\eeprom\NvM\NvM_JobProc.c	  1213              NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1214                  NVM_NEXT_STATE(NVM_STATE_READALL_WR_ONCE_PROT)
; ..\eeprom\NvM\NvM_JobProc.c	  1215          }
; ..\eeprom\NvM\NvM_JobProc.c	  1216      },
; ..\eeprom\NvM\NvM_JobProc.c	  1217  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1218   *  WriteAll
; ..\eeprom\NvM\NvM_JobProc.c	  1219   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	  1220      /* NVM_STATE_WRITEALL_PROC_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	  1221      {
; ..\eeprom\NvM\NvM_JobProc.c	  1222          {
; ..\eeprom\NvM\NvM_JobProc.c	  1223              {   /* write all canceled ->finish */
; ..\eeprom\NvM\NvM_JobProc.c	  1224                  NVM_STATE_IF{NVM_QRY_ID_CANCEL_WRITE_ALL, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1225                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1226                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1227              },
; ..\eeprom\NvM\NvM_JobProc.c	  1228  
; ..\eeprom\NvM\NvM_JobProc.c	  1229              {   /* last block done  */
; ..\eeprom\NvM\NvM_JobProc.c	  1230                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_BLOCK_DONE_WRITEALL, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1231                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1232                      NVM_NEXT_STATE(NVM_STATE_WRITEALL_WAIT_MEMHWA)
; ..\eeprom\NvM\NvM_JobProc.c	  1233              },
; ..\eeprom\NvM\NvM_JobProc.c	  1234              {    /* not canceled, not last block done, shall be written? */
; ..\eeprom\NvM\NvM_JobProc.c	  1235                  NVM_STATE_ELSEIF{NVM_QRY_ID_BLK_WRITE_ALL, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1236                      NVM_STATE_THEN{NVM_ACT_ID_SetBlockPendingWriteAll, NVM_ACT_ID_InitWriteBlockFsm},
; ..\eeprom\NvM\NvM_JobProc.c	  1237                      NVM_NEXT_STATE(NVM_STATE_WRITEALL_WRITE_FSM)
; ..\eeprom\NvM\NvM_JobProc.c	  1238              }
; ..\eeprom\NvM\NvM_JobProc.c	  1239          },
; ..\eeprom\NvM\NvM_JobProc.c	  1240          {   /* skip block */
; ..\eeprom\NvM\NvM_JobProc.c	  1241              NVM_STATE_ELSE{NVM_ACT_ID_SetReqSkipped, NVM_ACT_ID_InitNextBlockWriteAll},
; ..\eeprom\NvM\NvM_JobProc.c	  1242              NVM_NEXT_STATE(NVM_STATE_WRITEALL_PROC_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1243          }
; ..\eeprom\NvM\NvM_JobProc.c	  1244      },
; ..\eeprom\NvM\NvM_JobProc.c	  1245      /* NVM_STATE_WRITEALL_WRITE_FSM */
; ..\eeprom\NvM\NvM_JobProc.c	  1246      {
; ..\eeprom\NvM\NvM_JobProc.c	  1247          {
; ..\eeprom\NvM\NvM_JobProc.c	  1248              {   /* write fsm still running - do nothing */
; ..\eeprom\NvM\NvM_JobProc.c	  1249                  NVM_STATE_IF{NVM_QRY_ID_SUB_FSM_RUNNING, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1250                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1251                      NVM_NEXT_STATE(NVM_STATE_WRITEALL_WRITE_FSM)
; ..\eeprom\NvM\NvM_JobProc.c	  1252              },
; ..\eeprom\NvM\NvM_JobProc.c	  1253              {   /* fsm finished - handle next block */
; ..\eeprom\NvM\NvM_JobProc.c	  1254                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1255                      NVM_STATE_THEN{NVM_ACT_ID_InitNextBlockWriteAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1256                      NVM_NEXT_STATE(NVM_STATE_WRITEALL_PROC_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1257              },
; ..\eeprom\NvM\NvM_JobProc.c	  1258              /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1259              {
; ..\eeprom\NvM\NvM_JobProc.c	  1260                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1261                      NVM_STATE_THEN{NVM_ACT_ID_InitNextBlockWriteAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1262                      NVM_NEXT_STATE(NVM_STATE_WRITEALL_PROC_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1263              }
; ..\eeprom\NvM\NvM_JobProc.c	  1264          },
; ..\eeprom\NvM\NvM_JobProc.c	  1265          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1266          {
; ..\eeprom\NvM\NvM_JobProc.c	  1267              NVM_STATE_THEN{NVM_ACT_ID_InitNextBlockWriteAll, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1268              NVM_NEXT_STATE(NVM_STATE_WRITEALL_PROC_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1269          }
; ..\eeprom\NvM\NvM_JobProc.c	  1270      },
; ..\eeprom\NvM\NvM_JobProc.c	  1271      /* NVM_STATE_WRITEALL_WAIT_MEMHWA */
; ..\eeprom\NvM\NvM_JobProc.c	  1272      {
; ..\eeprom\NvM\NvM_JobProc.c	  1273          {
; ..\eeprom\NvM\NvM_JobProc.c	  1274              {   /* write all cancelled */
; ..\eeprom\NvM\NvM_JobProc.c	  1275                  NVM_STATE_IF{NVM_QRY_ID_CANCEL_WRITE_ALL, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1276                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1277                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1278              },
; ..\eeprom\NvM\NvM_JobProc.c	  1279              {   /* MemHwA is still busy  */
; ..\eeprom\NvM\NvM_JobProc.c	  1280                  NVM_STATE_ELSEIF{NVM_QRY_ID_MEMHWA_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1281                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1282                      NVM_NEXT_STATE(NVM_STATE_WRITEALL_WAIT_MEMHWA)
; ..\eeprom\NvM\NvM_JobProc.c	  1283              },
; ..\eeprom\NvM\NvM_JobProc.c	  1284              {   /* MemHwA is completely IDLE */
; ..\eeprom\NvM\NvM_JobProc.c	  1285                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1286                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1287                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1288              }
; ..\eeprom\NvM\NvM_JobProc.c	  1289          },
; ..\eeprom\NvM\NvM_JobProc.c	  1290          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1291          {
; ..\eeprom\NvM\NvM_JobProc.c	  1292              NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1293              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1294          }
; ..\eeprom\NvM\NvM_JobProc.c	  1295      },
; ..\eeprom\NvM\NvM_JobProc.c	  1296  #if (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON)
; ..\eeprom\NvM\NvM_JobProc.c	  1297  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1298   *  Repair Redundant Blocks
; ..\eeprom\NvM\NvM_JobProc.c	  1299   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	  1300      /* NVM_STATE_REPAIRREDUNDANT_MAIN */
; ..\eeprom\NvM\NvM_JobProc.c	  1301      {
; ..\eeprom\NvM\NvM_JobProc.c	  1302          {
; ..\eeprom\NvM\NvM_JobProc.c	  1303              {   /* check end before checking whether to suspend! - does not make sense to suspend though we are done... */
; ..\eeprom\NvM\NvM_JobProc.c	  1304                  /* same block order as read all - reached last block, job end */
; ..\eeprom\NvM\NvM_JobProc.c	  1305                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_BLOCK_DONE_READALL, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1306                      NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksFinish, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1307                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1308              },
; ..\eeprom\NvM\NvM_JobProc.c	  1309              {   /* suspend job if there is a single or multi block job */
; ..\eeprom\NvM\NvM_JobProc.c	  1310                  NVM_STATE_IF{NVM_QRY_ID_SUSPEND_REPAIR_REDUNDANT_BLOCKS, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1311                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1312                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1313              },
; ..\eeprom\NvM\NvM_JobProc.c	  1314              {   /* we have a redundant block to check */
; ..\eeprom\NvM\NvM_JobProc.c	  1315                  NVM_STATE_ELSEIF{NVM_QRY_ID_REDUNDANT_BLOCK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1316                      NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlockReadCheck, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1317                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_CHECK_FIRST)
; ..\eeprom\NvM\NvM_JobProc.c	  1318              }
; ..\eeprom\NvM\NvM_JobProc.c	  1319          },
; ..\eeprom\NvM\NvM_JobProc.c	  1320          { /* next block */
; ..\eeprom\NvM\NvM_JobProc.c	  1321              NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksInitNext, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1322              NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_MAIN)
; ..\eeprom\NvM\NvM_JobProc.c	  1323          }
; ..\eeprom\NvM\NvM_JobProc.c	  1324      },
; ..\eeprom\NvM\NvM_JobProc.c	  1325      /* NVM_STATE_REPAIRREDUNDANT_CHECK_FIRST */
; ..\eeprom\NvM\NvM_JobProc.c	  1326      {
; ..\eeprom\NvM\NvM_JobProc.c	  1327          {
; ..\eeprom\NvM\NvM_JobProc.c	  1328              {   /* wait for underlying modules */
; ..\eeprom\NvM\NvM_JobProc.c	  1329                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1330                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1331                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_CHECK_FIRST)
; ..\eeprom\NvM\NvM_JobProc.c	  1332              },
; ..\eeprom\NvM\NvM_JobProc.c	  1333              {   /* suspend job if there is a single or multi block job */
; ..\eeprom\NvM\NvM_JobProc.c	  1334                  NVM_STATE_ELSEIF{NVM_QRY_ID_SUSPEND_REPAIR_REDUNDANT_BLOCKS, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1335                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1336                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1337              },
; ..\eeprom\NvM\NvM_JobProc.c	  1338              {   /* process Crc calculation in case read was successful and Crc is still busy */
; ..\eeprom\NvM\NvM_JobProc.c	  1339                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_CRC_BUSY},
; ..\eeprom\NvM\NvM_JobProc.c	  1340                      NVM_STATE_THEN{NVM_ACT_ID_ProcessCrc, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	  1341                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_CHECK_FIRST)
; ..\eeprom\NvM\NvM_JobProc.c	  1342              }
; ..\eeprom\NvM\NvM_JobProc.c	  1343          },
; ..\eeprom\NvM\NvM_JobProc.c	  1344          { /* first block is done, go to next block */
; ..\eeprom\NvM\NvM_JobProc.c	  1345              NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlockFinishReadCheck, NVM_ACT_ID_RepairRedBlockReadCheck},
; ..\eeprom\NvM\NvM_JobProc.c	  1346              NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_CHECK_SECOND)
; ..\eeprom\NvM\NvM_JobProc.c	  1347          }
; ..\eeprom\NvM\NvM_JobProc.c	  1348      },
; ..\eeprom\NvM\NvM_JobProc.c	  1349      /* NVM_STATE_REPAIRREDUNDANT_CHECK_SECOND */
; ..\eeprom\NvM\NvM_JobProc.c	  1350      {
; ..\eeprom\NvM\NvM_JobProc.c	  1351          {
; ..\eeprom\NvM\NvM_JobProc.c	  1352              {   /* wait for underlying modules */
; ..\eeprom\NvM\NvM_JobProc.c	  1353                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1354                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1355                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_CHECK_SECOND)
; ..\eeprom\NvM\NvM_JobProc.c	  1356              },
; ..\eeprom\NvM\NvM_JobProc.c	  1357              {   /* suspend job if there is a single or multi block job */
; ..\eeprom\NvM\NvM_JobProc.c	  1358                  NVM_STATE_ELSEIF{NVM_QRY_ID_SUSPEND_REPAIR_REDUNDANT_BLOCKS, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1359                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1360                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1361              },
; ..\eeprom\NvM\NvM_JobProc.c	  1362              {   /* process Crc calculation in case read was successful and Crc is still busy */
; ..\eeprom\NvM\NvM_JobProc.c	  1363                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_CRC_BUSY},
; ..\eeprom\NvM\NvM_JobProc.c	  1364                      NVM_STATE_THEN{NVM_ACT_ID_ProcessCrc, NVM_ACT_ID_Wait},
; ..\eeprom\NvM\NvM_JobProc.c	  1365                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_CHECK_SECOND)
; ..\eeprom\NvM\NvM_JobProc.c	  1366              }
; ..\eeprom\NvM\NvM_JobProc.c	  1367          },
; ..\eeprom\NvM\NvM_JobProc.c	  1368          {   /* second block is done, go to validate state */
; ..\eeprom\NvM\NvM_JobProc.c	  1369              NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlockFinishReadCheck, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1370              NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_VALIDATE_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1371          }
; ..\eeprom\NvM\NvM_JobProc.c	  1372      },
; ..\eeprom\NvM\NvM_JobProc.c	  1373      /* NVM_STATE_REPAIRREDUNDANT_VALIDATE_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	  1374      {
; ..\eeprom\NvM\NvM_JobProc.c	  1375          {
; ..\eeprom\NvM\NvM_JobProc.c	  1376              {   /* suspend job if there is a single or multi block job */
; ..\eeprom\NvM\NvM_JobProc.c	  1377                  NVM_STATE_ELSEIF{NVM_QRY_ID_SUSPEND_REPAIR_REDUNDANT_BLOCKS, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1378                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1379                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1380              },
; ..\eeprom\NvM\NvM_JobProc.c	  1381              {   /* repair block in case  */
; ..\eeprom\NvM\NvM_JobProc.c	  1382                  NVM_STATE_ELSEIF{NVM_QRY_ID_REPAIR_RED_BLOCK_DEFECT, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1383                      NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksReadValid, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1384                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_READ_VALID_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1385              },
; ..\eeprom\NvM\NvM_JobProc.c	  1386              {   /* nothing to repair, next block */
; ..\eeprom\NvM\NvM_JobProc.c	  1387                  NVM_STATE_IF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1388                      NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksInitNext, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1389                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_MAIN)
; ..\eeprom\NvM\NvM_JobProc.c	  1390              }
; ..\eeprom\NvM\NvM_JobProc.c	  1391          },
; ..\eeprom\NvM\NvM_JobProc.c	  1392          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1393          {
; ..\eeprom\NvM\NvM_JobProc.c	  1394              NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksInitNext, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1395              NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_MAIN)
; ..\eeprom\NvM\NvM_JobProc.c	  1396          }
; ..\eeprom\NvM\NvM_JobProc.c	  1397      },
; ..\eeprom\NvM\NvM_JobProc.c	  1398      /* NVM_STATE_REPAIRREDUNDANT_READ_VALID_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	  1399      {
; ..\eeprom\NvM\NvM_JobProc.c	  1400          {
; ..\eeprom\NvM\NvM_JobProc.c	  1401              {   /* wait for underlying modules */
; ..\eeprom\NvM\NvM_JobProc.c	  1402                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1403                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1404                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_READ_VALID_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1405              },
; ..\eeprom\NvM\NvM_JobProc.c	  1406              {   /* suspend job if there is a single or multi block job */
; ..\eeprom\NvM\NvM_JobProc.c	  1407                  NVM_STATE_ELSEIF{NVM_QRY_ID_SUSPEND_REPAIR_REDUNDANT_BLOCKS, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1408                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1409                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1410              },
; ..\eeprom\NvM\NvM_JobProc.c	  1411              {   /* read OK, we do not need to check Crc, setup write job */
; ..\eeprom\NvM\NvM_JobProc.c	  1412                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1413                      NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlockWriteInvalid, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1414                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_WRITE_INVALID_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1415              }
; ..\eeprom\NvM\NvM_JobProc.c	  1416          },
; ..\eeprom\NvM\NvM_JobProc.c	  1417          {   /* read not ok */
; ..\eeprom\NvM\NvM_JobProc.c	  1418              NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksFinishBlock, NVM_ACT_ID_RepairRedBlocksInitNext},
; ..\eeprom\NvM\NvM_JobProc.c	  1419              NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_MAIN)
; ..\eeprom\NvM\NvM_JobProc.c	  1420          }
; ..\eeprom\NvM\NvM_JobProc.c	  1421      },
; ..\eeprom\NvM\NvM_JobProc.c	  1422      /* NVM_STATE_REPAIRREDUNDANT_WRITE_INVALID_BLOCK */
; ..\eeprom\NvM\NvM_JobProc.c	  1423      {
; ..\eeprom\NvM\NvM_JobProc.c	  1424          {
; ..\eeprom\NvM\NvM_JobProc.c	  1425              {   /* wait for underlying modules */
; ..\eeprom\NvM\NvM_JobProc.c	  1426                  NVM_STATE_IF{NVM_QRY_ID_NV_BUSY, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1427                      NVM_STATE_THEN{NVM_ACT_ID_Wait, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1428                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_WRITE_INVALID_BLOCK)
; ..\eeprom\NvM\NvM_JobProc.c	  1429              },
; ..\eeprom\NvM\NvM_JobProc.c	  1430              {   /* validate write job, next block */
; ..\eeprom\NvM\NvM_JobProc.c	  1431                  NVM_STATE_ELSEIF{NVM_QRY_ID_LAST_RESULT_OK, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1432                      NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksFinishBlock, NVM_ACT_ID_RepairRedBlocksInitNext},
; ..\eeprom\NvM\NvM_JobProc.c	  1433                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_MAIN)
; ..\eeprom\NvM\NvM_JobProc.c	  1434              },
; ..\eeprom\NvM\NvM_JobProc.c	  1435              {   /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1436                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1437                      NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksFinishBlock, NVM_ACT_ID_RepairRedBlocksInitNext},
; ..\eeprom\NvM\NvM_JobProc.c	  1438                      NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_MAIN)
; ..\eeprom\NvM\NvM_JobProc.c	  1439              }
; ..\eeprom\NvM\NvM_JobProc.c	  1440          },
; ..\eeprom\NvM\NvM_JobProc.c	  1441          /* not used */
; ..\eeprom\NvM\NvM_JobProc.c	  1442          {
; ..\eeprom\NvM\NvM_JobProc.c	  1443              NVM_STATE_THEN{NVM_ACT_ID_RepairRedBlocksFinishBlock, NVM_ACT_ID_RepairRedBlocksInitNext},
; ..\eeprom\NvM\NvM_JobProc.c	  1444              NVM_NEXT_STATE(NVM_STATE_REPAIRREDUNDANT_MAIN)
; ..\eeprom\NvM\NvM_JobProc.c	  1445          }
; ..\eeprom\NvM\NvM_JobProc.c	  1446      },
; ..\eeprom\NvM\NvM_JobProc.c	  1447  #endif
; ..\eeprom\NvM\NvM_JobProc.c	  1448  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1449   *  NvM job finished
; ..\eeprom\NvM\NvM_JobProc.c	  1450   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	  1451      /* NVM_STATE_FSM_FINISHED */
; ..\eeprom\NvM\NvM_JobProc.c	  1452      /* always stay in this state. Only by re-initializing a FSM exits this state */
; ..\eeprom\NvM\NvM_JobProc.c	  1453      {
; ..\eeprom\NvM\NvM_JobProc.c	  1454          {
; ..\eeprom\NvM\NvM_JobProc.c	  1455              {
; ..\eeprom\NvM\NvM_JobProc.c	  1456                  NVM_STATE_IF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1457                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1458                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1459              },
; ..\eeprom\NvM\NvM_JobProc.c	  1460              {
; ..\eeprom\NvM\NvM_JobProc.c	  1461                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1462                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1463                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1464              },
; ..\eeprom\NvM\NvM_JobProc.c	  1465              {
; ..\eeprom\NvM\NvM_JobProc.c	  1466                  NVM_STATE_ELSEIF{NVM_QRY_ID_TRUE, NVM_QRY_ID_TRUE},
; ..\eeprom\NvM\NvM_JobProc.c	  1467                      NVM_STATE_THEN{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1468                      NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1469              }
; ..\eeprom\NvM\NvM_JobProc.c	  1470          },
; ..\eeprom\NvM\NvM_JobProc.c	  1471          {
; ..\eeprom\NvM\NvM_JobProc.c	  1472              NVM_STATE_ELSE{NVM_ACT_ID_Nop, NVM_ACT_ID_Nop},
; ..\eeprom\NvM\NvM_JobProc.c	  1473              NVM_NEXT_STATE(NVM_STATE_FSM_FINISHED)
; ..\eeprom\NvM\NvM_JobProc.c	  1474          }
; ..\eeprom\NvM\NvM_JobProc.c	  1475      }
; ..\eeprom\NvM\NvM_JobProc.c	  1476  };
; ..\eeprom\NvM\NvM_JobProc.c	  1477  
; ..\eeprom\NvM\NvM_JobProc.c	  1478  #define NVM_STOP_SEC_CONST_UNSPECIFIED
; ..\eeprom\NvM\NvM_JobProc.c	  1479    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	  1480  
; ..\eeprom\NvM\NvM_JobProc.c	  1481  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1482   *  IMPLEMENTATION
; ..\eeprom\NvM\NvM_JobProc.c	  1483   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	  1484  
; ..\eeprom\NvM\NvM_JobProc.c	  1485  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_JobProc.c	  1486    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	  1487  
; ..\eeprom\NvM\NvM_JobProc.c	  1488  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1489  *  NvM_JobProcInit
; ..\eeprom\NvM\NvM_JobProc.c	  1490  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	  1491  /*!
; ..\eeprom\NvM\NvM_JobProc.c	  1492   * Internal comment removed.
; ..\eeprom\NvM\NvM_JobProc.c	  1493   *
; ..\eeprom\NvM\NvM_JobProc.c	  1494   *
; ..\eeprom\NvM\NvM_JobProc.c	  1495   */
; ..\eeprom\NvM\NvM_JobProc.c	  1496  FUNC(void, NVM_PRIVATE_CODE) NvM_JobProcInit(void)
; Function NvM_JobProcInit
.L12:
NvM_JobProcInit:	.type	func

; ..\eeprom\NvM\NvM_JobProc.c	  1497  {
; ..\eeprom\NvM\NvM_JobProc.c	  1498      NvM_CurrentJob_t.JobServiceId_t = NVM_INT_FID_NO_JOB_PENDING;
	movh.a	a15,#@his(NvM_CurrentJob_t+2)
.L97:
	mov	d15,#8
	st.b	[a15]@los(NvM_CurrentJob_t+2),d15
.L98:

; ..\eeprom\NvM\NvM_JobProc.c	  1499  
; ..\eeprom\NvM\NvM_JobProc.c	  1500      NvM_CurrentBlockInfo_t.InternalFlags_u8 = 0u;
	movh.a	a15,#@his(NvM_CurrentBlockInfo_t+42)
.L99:
	mov	d15,#0
	st.b	[a15]@los(NvM_CurrentBlockInfo_t+42),d15
.L100:

; ..\eeprom\NvM\NvM_JobProc.c	  1501  
; ..\eeprom\NvM\NvM_JobProc.c	  1502      NvM_JobMainState_t = NVM_STATE_FSM_FINISHED;
	movh.a	a15,#@his(NvM_JobMainState_t)
.L101:
	mov	d15,#32
	st.b	[a15]@los(NvM_JobMainState_t),d15
.L102:

; ..\eeprom\NvM\NvM_JobProc.c	  1503      NvM_JobSubState_t  = NVM_STATE_FSM_FINISHED;
	movh.a	a15,#@his(NvM_JobSubState_t)
.L103:
	st.b	[a15]@los(NvM_JobSubState_t),d15
.L104:

; ..\eeprom\NvM\NvM_JobProc.c	  1504      NvM_TaskState_t    = NVM_STATE_IDLE;
	movh.a	a15,#@his(NvM_TaskState_t)
.L105:
	mov	d15,#1
	st.b	[a15]@los(NvM_TaskState_t),d15
.L106:

; ..\eeprom\NvM\NvM_JobProc.c	  1505  }
	ret
.L55:
	
__NvM_JobProcInit_function_end:
	.size	NvM_JobProcInit,__NvM_JobProcInit_function_end-NvM_JobProcInit
.L27:
	; End of function
	
	.sdecl	'.text.NvM_JobProc.NvM_Fsm',code,cluster('NvM_Fsm')
	.sect	'.text.NvM_JobProc.NvM_Fsm'
	.align	2
	
	.global	NvM_Fsm

; ..\eeprom\NvM\NvM_JobProc.c	  1506  
; ..\eeprom\NvM\NvM_JobProc.c	  1507  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1508  *  NvM_Fsm
; ..\eeprom\NvM\NvM_JobProc.c	  1509  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	  1510  /*!
; ..\eeprom\NvM\NvM_JobProc.c	  1511   * Internal comment removed.
; ..\eeprom\NvM\NvM_JobProc.c	  1512   *
; ..\eeprom\NvM\NvM_JobProc.c	  1513   *
; ..\eeprom\NvM\NvM_JobProc.c	  1514   *
; ..\eeprom\NvM\NvM_JobProc.c	  1515   */
; ..\eeprom\NvM\NvM_JobProc.c	  1516  FUNC(NvM_StateIdType, NVM_PRIVATE_CODE) NvM_Fsm(NvM_StateIdType NvM_CurrentState_t)
; Function NvM_Fsm
.L14:
NvM_Fsm:	.type	func

; ..\eeprom\NvM\NvM_JobProc.c	  1517  {
; ..\eeprom\NvM\NvM_JobProc.c	  1518      /* local variables */
; ..\eeprom\NvM\NvM_JobProc.c	  1519      NvM_StateIdType NvM_RetState_tloc;
; ..\eeprom\NvM\NvM_JobProc.c	  1520      NvM_StateChangeActionsPtrType ChangeActions_ptloc;
; ..\eeprom\NvM\NvM_JobProc.c	  1521      CONSTP2CONST(NvM_StateDescrType, AUTOMATIC, NVM_PRIVATE_CONST) CurrentState_ptloc =
; ..\eeprom\NvM\NvM_JobProc.c	  1522          &NvM_StateDescrTable_at[NvM_CurrentState_t];
	mul	d15,d4,#24
	movh.a	a15,#@his(NvM_StateDescrTable_at)
	lea	a15,[a15]@los(NvM_StateDescrTable_at)
.L111:
	addsc.a	a15,a15,d15,#0
.L75:

; ..\eeprom\NvM\NvM_JobProc.c	  1523  
; ..\eeprom\NvM\NvM_JobProc.c	  1524      /* Execute the first query, if it is fulfilled then do the corresponding
; ..\eeprom\NvM\NvM_JobProc.c	  1525       * action and return with the next state to NvM_MainFunction(). If the first
; ..\eeprom\NvM\NvM_JobProc.c	  1526       * query is not true then execute next query as long as the right exit is found.
; ..\eeprom\NvM\NvM_JobProc.c	  1527       */
; ..\eeprom\NvM\NvM_JobProc.c	  1528      if(NvM_FsmQuery(CurrentState_ptloc->ChangeConditions_at[0].Queries_at)) /* SBSW_NvM_FuncCall_PtrParam_FsmQuery */
	mov.aa	a4,a15
.L76:
	call	NvM_FsmQuery
.L74:
	jeq	d2,#0,.L2
.L112:

; ..\eeprom\NvM\NvM_JobProc.c	  1529      {
; ..\eeprom\NvM\NvM_JobProc.c	  1530          NvM_RetState_tloc = CurrentState_ptloc->ChangeConditions_at[0].NextState_t;
	ld.bu	d15,[a15]4
.L77:

; ..\eeprom\NvM\NvM_JobProc.c	  1531          ChangeActions_ptloc = &CurrentState_ptloc->ChangeConditions_at[0].Actions_t;
	add.a	a15,#2
.L113:
	j	.L3
.L2:

; ..\eeprom\NvM\NvM_JobProc.c	  1532      }
; ..\eeprom\NvM\NvM_JobProc.c	  1533      else if (NvM_FsmQuery(CurrentState_ptloc->ChangeConditions_at[1].Queries_at)) /* SBSW_NvM_FuncCall_PtrParam_FsmQuery */
	lea	a4,[a15]6
.L114:
	call	NvM_FsmQuery
.L115:
	jeq	d2,#0,.L4
.L116:

; ..\eeprom\NvM\NvM_JobProc.c	  1534      {
; ..\eeprom\NvM\NvM_JobProc.c	  1535          NvM_RetState_tloc = CurrentState_ptloc->ChangeConditions_at[1].NextState_t;
	ld.bu	d15,[a15]10
.L78:

; ..\eeprom\NvM\NvM_JobProc.c	  1536          ChangeActions_ptloc = &CurrentState_ptloc->ChangeConditions_at[1].Actions_t;
	lea	a15,[a15]8
.L117:
	j	.L5
.L4:

; ..\eeprom\NvM\NvM_JobProc.c	  1537      }
; ..\eeprom\NvM\NvM_JobProc.c	  1538      else if (NvM_FsmQuery(CurrentState_ptloc->ChangeConditions_at[2].Queries_at)) /* SBSW_NvM_FuncCall_PtrParam_FsmQuery */
	lea	a4,[a15]12
.L118:
	call	NvM_FsmQuery
.L119:
	jeq	d2,#0,.L6
.L120:

; ..\eeprom\NvM\NvM_JobProc.c	  1539      {
; ..\eeprom\NvM\NvM_JobProc.c	  1540          NvM_RetState_tloc = CurrentState_ptloc->ChangeConditions_at[2].NextState_t;
	ld.bu	d15,[a15]16
.L79:

; ..\eeprom\NvM\NvM_JobProc.c	  1541          ChangeActions_ptloc = &CurrentState_ptloc->ChangeConditions_at[2].Actions_t;
	lea	a15,[a15]14
.L121:
	j	.L7
.L6:

; ..\eeprom\NvM\NvM_JobProc.c	  1542      }
; ..\eeprom\NvM\NvM_JobProc.c	  1543      else
; ..\eeprom\NvM\NvM_JobProc.c	  1544      {
; ..\eeprom\NvM\NvM_JobProc.c	  1545          NvM_RetState_tloc = CurrentState_ptloc->FinalCondition_t.NextState_t;
	ld.bu	d15,[a15]20
.L80:

; ..\eeprom\NvM\NvM_JobProc.c	  1546          ChangeActions_ptloc = &CurrentState_ptloc->FinalCondition_t.Actions_t;
	lea	a15,[a15]18
.L7:
.L5:
.L3:

; ..\eeprom\NvM\NvM_JobProc.c	  1547      }
; ..\eeprom\NvM\NvM_JobProc.c	  1548  
; ..\eeprom\NvM\NvM_JobProc.c	  1549      NvM_FsmAction(ChangeActions_ptloc); /* SBSW_NvM_FuncCall_PtrParam_FsmAction */
	mov.aa	a4,a15
.L81:
	call	NvM_FsmAction
.L82:

; ..\eeprom\NvM\NvM_JobProc.c	  1550  
; ..\eeprom\NvM\NvM_JobProc.c	  1551      return NvM_RetState_tloc;
; ..\eeprom\NvM\NvM_JobProc.c	  1552  }
	mov	d2,d15
	ret
.L57:
	
__NvM_Fsm_function_end:
	.size	NvM_Fsm,__NvM_Fsm_function_end-NvM_Fsm
.L32:
	; End of function
	
	.sdecl	'.text.NvM_JobProc.NvM_FsmQuery',code,cluster('NvM_FsmQuery')
	.sect	'.text.NvM_JobProc.NvM_FsmQuery'
	.align	2
	

; ..\eeprom\NvM\NvM_JobProc.c	  1553  
; ..\eeprom\NvM\NvM_JobProc.c	  1554  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1555  *  NvM_FsmQuery
; ..\eeprom\NvM\NvM_JobProc.c	  1556  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	  1557  /*!
; ..\eeprom\NvM\NvM_JobProc.c	  1558   * Internal comment removed.
; ..\eeprom\NvM\NvM_JobProc.c	  1559   *
; ..\eeprom\NvM\NvM_JobProc.c	  1560   *
; ..\eeprom\NvM\NvM_JobProc.c	  1561   *
; ..\eeprom\NvM\NvM_JobProc.c	  1562   *
; ..\eeprom\NvM\NvM_JobProc.c	  1563   */
; ..\eeprom\NvM\NvM_JobProc.c	  1564  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_FsmQuery(NvM_StateQueryPtrType NvM_Queries_at)
; Function NvM_FsmQuery
.L16:
NvM_FsmQuery:	.type	func
	mov.aa	a15,a4
.L84:

; ..\eeprom\NvM\NvM_JobProc.c	  1565  {
; ..\eeprom\NvM\NvM_JobProc.c	  1566      /* Currently there are Queries with side-effects, forcing
; ..\eeprom\NvM\NvM_JobProc.c	  1567       * execution in the defined order
; ..\eeprom\NvM\NvM_JobProc.c	  1568       */
; ..\eeprom\NvM\NvM_JobProc.c	  1569  
; ..\eeprom\NvM\NvM_JobProc.c	  1570      boolean retVal = NvM_QueryTable_ap[NvM_Queries_at[0u]](); /* SBSW_NvM_FuncPtrCall_QueryAction */
	ld.bu	d15,[a15]
.L126:
	movh.a	a12,#@his(NvM_QueryTable_ap)
	lea	a12,[a12]@los(NvM_QueryTable_ap)
.L127:
	addsc.a	a2,a12,d15,#2
	ld.a	a2,[a2]
.L128:
	calli	a2
.L83:

; ..\eeprom\NvM\NvM_JobProc.c	  1571  
; ..\eeprom\NvM\NvM_JobProc.c	  1572      if (retVal)
	jeq	d2,#0,.L9
.L129:

; ..\eeprom\NvM\NvM_JobProc.c	  1573      {
; ..\eeprom\NvM\NvM_JobProc.c	  1574          retVal = NvM_QueryTable_ap[NvM_Queries_at[1u]](); /* SBSW_NvM_FuncPtrCall_QueryAction */
	ld.bu	d15,[a15]1
.L130:
	addsc.a	a15,a12,d15,#2
.L85:
	ld.a	a15,[a15]
.L131:
	ji	a15
.L9:

; ..\eeprom\NvM\NvM_JobProc.c	  1575      }
; ..\eeprom\NvM\NvM_JobProc.c	  1576  
; ..\eeprom\NvM\NvM_JobProc.c	  1577      return retVal;
; ..\eeprom\NvM\NvM_JobProc.c	  1578  }
	ret
.L65:
	
__NvM_FsmQuery_function_end:
	.size	NvM_FsmQuery,__NvM_FsmQuery_function_end-NvM_FsmQuery
.L37:
	; End of function
	
	.sdecl	'.text.NvM_JobProc.NvM_FsmAction',code,cluster('NvM_FsmAction')
	.sect	'.text.NvM_JobProc.NvM_FsmAction'
	.align	2
	

; ..\eeprom\NvM\NvM_JobProc.c	  1579  
; ..\eeprom\NvM\NvM_JobProc.c	  1580  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1581  *  NvM_FsmAction
; ..\eeprom\NvM\NvM_JobProc.c	  1582  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_JobProc.c	  1583  /*!
; ..\eeprom\NvM\NvM_JobProc.c	  1584   * Internal comment removed.
; ..\eeprom\NvM\NvM_JobProc.c	  1585   *
; ..\eeprom\NvM\NvM_JobProc.c	  1586   *
; ..\eeprom\NvM\NvM_JobProc.c	  1587   */
; ..\eeprom\NvM\NvM_JobProc.c	  1588  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_FsmAction(NvM_StateChangeActionsPtrType NvM_Actions_pt)
; Function NvM_FsmAction
.L18:
NvM_FsmAction:	.type	func
	mov.aa	a15,a4
.L87:

; ..\eeprom\NvM\NvM_JobProc.c	  1589  {
; ..\eeprom\NvM\NvM_JobProc.c	  1590      NvM_ActionTable_ap[NvM_Actions_pt->ExitHandler_t](); /* SBSW_NvM_FuncPtrCall_QueryAction */
	ld.bu	d15,[a15]
.L136:
	movh.a	a12,#@his(NvM_ActionTable_ap)
	lea	a12,[a12]@los(NvM_ActionTable_ap)
.L137:
	addsc.a	a2,a12,d15,#2
	ld.a	a2,[a2]
.L138:
	calli	a2
.L86:

; ..\eeprom\NvM\NvM_JobProc.c	  1591      NvM_ActionTable_ap[NvM_Actions_pt->EntryHandler_t](); /* SBSW_NvM_FuncPtrCall_QueryAction */
	ld.bu	d15,[a15]1
.L139:
	addsc.a	a15,a12,d15,#2
.L88:
	ld.a	a15,[a15]
.L140:
	ji	a15
.L69:
	
__NvM_FsmAction_function_end:
	.size	NvM_FsmAction,__NvM_FsmAction_function_end-NvM_FsmAction
.L42:
	; End of function
	
	.sdecl	'.bss.NvM_JobProc.NvM_JobMainState_t',data,cluster('NvM_JobMainState_t')
	.sect	'.bss.NvM_JobProc.NvM_JobMainState_t'
	.global	NvM_JobMainState_t
NvM_JobMainState_t:	.type	object
	.size	NvM_JobMainState_t,1
	.space	1
	.sdecl	'.bss.NvM_JobProc.NvM_JobSubState_t',data,cluster('NvM_JobSubState_t')
	.sect	'.bss.NvM_JobProc.NvM_JobSubState_t'
	.global	NvM_JobSubState_t
NvM_JobSubState_t:	.type	object
	.size	NvM_JobSubState_t,1
	.space	1
	.sdecl	'.data.NvM_JobProc.NvM_TaskState_t',data,cluster('NvM_TaskState_t')
	.sect	'.data.NvM_JobProc.NvM_TaskState_t'
	.global	NvM_TaskState_t
NvM_TaskState_t:	.type	object
	.size	NvM_TaskState_t,1
	.space	1
	.sdecl	'.bss.NvM_JobProc.NvM_CurrentBlockInfo_t',data,cluster('NvM_CurrentBlockInfo_t')
	.sect	'.bss.NvM_JobProc.NvM_CurrentBlockInfo_t'
	.global	NvM_CurrentBlockInfo_t
	.align	4
NvM_CurrentBlockInfo_t:	.type	object
	.size	NvM_CurrentBlockInfo_t,44
	.space	44
	.sdecl	'.rodata.NvM_JobProc.NvM_IntServiceDescrTable_at',data,rom,cluster('NvM_IntServiceDescrTable_at')
	.sect	'.rodata.NvM_JobProc.NvM_IntServiceDescrTable_at'
	.global	NvM_IntServiceDescrTable_at
	.align	4
NvM_IntServiceDescrTable_at:	.type	object
	.size	NvM_IntServiceDescrTable_at,36
	.byte	7,10,7
	.space	1
	.byte	23,4,6
	.space	1
	.byte	9,17,8
	.space	1
	.byte	20,18,11
	.space	1
	.byte	19,19,9
	.space	1
	.byte	6,29,13
	.space	1
	.byte	3,20,12
	.space	1
	.byte	45,32,21
	.space	1
	.byte	45,32,14
	.space	1
	.sdecl	'.rodata.NvM_JobProc.NvM_StateDescrTable_at',data,rom,cluster('NvM_StateDescrTable_at')
	.sect	'.rodata.NvM_JobProc.NvM_StateDescrTable_at'
	.align	4
NvM_StateDescrTable_at:	.type	object
	.size	NvM_StateDescrTable_at,792
	.byte	28,28,44,44
	.space	2
	.byte	28,28,44,44
	.space	2
	.byte	28,28,44,44
	.space	2
	.byte	44,44
	.space	4
	.byte	11,28,43,1
	.byte	2
	.space	1
	.byte	10,28,39,1
	.byte	3
	.space	1
	.byte	28,28,44,45
	.byte	1
	.space	1
	.byte	44,45,1
	.space	3
	.byte	9,28,45,45
	.byte	2
	.space	1
	.byte	28,28,10,45
	.byte	1
	.space	1
	.byte	28,28,10,45
	.byte	1
	.space	1
	.byte	16,45,1
	.space	3
	.byte	2,28,11,45
	.byte	1
	.space	1
	.byte	9,28,45,45
	.byte	3
	.space	1
	.byte	28,28,10,45
	.byte	1
	.space	1
	.byte	10,45,1
	.space	3
	.byte	12,28,44,45
	.byte	4
	.space	1
	.byte	8,3,24,44
	.byte	6
	.space	1
	.byte	8,28,45,45
	.byte	5
	.space	1
	.byte	45,45,7
	.space	3
	.byte	25,23,29,45
	.byte	9
	.space	1
	.byte	27,28,44,45
	.byte	5
	.space	1
	.byte	28,28,35,45
	.byte	7
	.space	1
	.byte	45,45,7
	.space	3
	.byte	3,28,24,44
	.byte	6
	.space	1
	.byte	5,28,45,45
	.byte	5
	.space	1
	.byte	28,28,33,45
	.byte	7
	.space	1
	.byte	33,45,7
	.space	3
	.byte	15,28,30,23
	.byte	4
	.space	1
	.byte	28,28,9,45
	.byte	8
	.space	1
	.byte	28,28,9,45
	.byte	8
	.space	1
	.byte	9,45,8
	.space	3
	.byte	4,28,26,45
	.byte	8
	.space	1
	.byte	28,28,27,16
	.byte	32
	.space	1
	.byte	28,28,27,16
	.byte	32
	.space	1
	.byte	27,16,32
	.space	3
	.byte	4,28,25,44
	.byte	9
	.space	1
	.byte	28,28,16,45
	.byte	32
	.space	1
	.byte	28,28,45,45
	.byte	32
	.space	1
	.byte	45,45,32
	.space	3
	.byte	4,28,38,44
	.byte	10
	.space	1
	.byte	26,28,44,45
	.byte	11
	.space	1
	.byte	27,28,44,45
	.byte	10
	.space	1
	.byte	35,17,32
	.space	3
	.byte	3,28,21,44
	.byte	11
	.space	1
	.byte	15,28,45,45
	.byte	13
	.space	1
	.byte	28,28,45,45
	.byte	12
	.space	1
	.byte	45,45,32
	.space	3
	.byte	22,28,17,45
	.byte	32
	.space	1
	.byte	28,28,22,45
	.byte	16
	.space	1
	.byte	28,28,45,45
	.byte	32
	.space	1
	.byte	45,45,32
	.space	3
	.byte	12,28,44,45
	.byte	13
	.space	1
	.byte	15,8,31,28
	.byte	14
	.space	1
	.byte	15,28,22,45
	.byte	15
	.space	1
	.byte	45,45,32
	.space	3
	.byte	12,28,44,45
	.byte	14
	.space	1
	.byte	8,22,17,45
	.byte	32
	.space	1
	.byte	8,28,31,22
	.byte	15
	.space	1
	.byte	22,45,15
	.space	3
	.byte	12,28,44,45
	.byte	15
	.space	1
	.byte	8,28,31,22
	.byte	16
	.space	1
	.byte	19,28,31,22
	.byte	16
	.space	1
	.byte	22,45,15
	.space	3
	.byte	12,28,44,45
	.byte	16
	.space	1
	.byte	8,28,17,45
	.byte	32
	.space	1
	.byte	19,28,17,45
	.byte	32
	.space	1
	.byte	22,45,16
	.space	3
	.byte	4,28,26,45
	.byte	17
	.space	1
	.byte	28,28,27,45
	.byte	32
	.space	1
	.byte	28,28,27,45
	.byte	32
	.space	1
	.byte	27,45,32
	.space	3
	.byte	12,28,44,45
	.byte	18
	.space	1
	.byte	8,15,30,20
	.byte	18
	.space	1
	.byte	28,28,32,18
	.byte	32
	.space	1
	.byte	32,18,32
	.space	3
	.byte	12,28,44,45
	.byte	19
	.space	1
	.byte	8,15,30,19
	.byte	19
	.space	1
	.byte	28,28,32,18
	.byte	32
	.space	1
	.byte	32,18,32
	.space	3
	.byte	17,28,45,45
	.byte	20
	.space	1
	.byte	28,28,15,13
	.byte	21
	.space	1
	.byte	28,28,15,13
	.byte	21
	.space	1
	.byte	15,13,21
	.space	3
	.byte	6,28,45,45
	.byte	32
	.space	1
	.byte	14,3,21,44
	.byte	25
	.space	1
	.byte	24,28
	.space	1
	.byte	45,23
	.space	2
	.byte	45,22
	.space	3
	.byte	16,18,28,45
	.byte	28
	.space	1
	.byte	16,28,34,13
	.byte	21
	.space	1
	.byte	21,28,5,45
	.byte	27
	.space	1
	.byte	4,45,26
	.space	3
	.byte	16,28,34,13
	.byte	21
	.space	1
	.byte	20,28,36,5
	.byte	27
	.space	1
	.byte	28,28,34,13
	.byte	21
	.space	1
	.byte	34,13,21
	.space	3
	.byte	8,28,42,13
	.byte	21
	.space	1
	.byte	15,28,30,28
	.byte	28
	.space	1
	.byte	28,28,42,13
	.byte	21
	.space	1
	.byte	42,13,21
	.space	3
	.byte	3,28,21,44
	.byte	25
	.space	1
	.byte	5,28,13,45
	.byte	21
	.space	1
	.byte	24,28
	.space	1
	.byte	45,23
	.space	2
	.byte	45,22
	.space	3
	.byte	12,24,40,41
	.byte	23
	.space	1
	.byte	17,28,45,45
	.byte	26
	.space	1
	.byte	28,28,13,45
	.byte	21
	.space	1
	.byte	13,45,21
	.space	3
	.byte	17,28,45,45
	.byte	27
	.space	1
	.byte	28,28,13,45
	.byte	21
	.space	1
	.byte	28,28,13,45
	.byte	21
	.space	1
	.byte	13,45,21
	.space	3
	.byte	12,24,40,45
	.byte	23
	.space	1
	.byte	12,28,44,45
	.byte	28
	.space	1
	.byte	28,28,45,45
	.byte	24
	.space	1
	.byte	45,45,24
	.space	3
	.byte	1,28,45,45
	.byte	32
	.space	1
	.byte	7,28,45,45
	.byte	31
	.space	2
	.byte	28,37,8,30
	.space	1
	.byte	34,14,29
	.space	3
	.byte	17,28,45,45
	.byte	30
	.space	1
	.byte	28,28,14,45
	.byte	29
	.space	1
	.byte	28,28,14,45
	.byte	29
	.space	1
	.byte	14,45,29
	.space	3
	.byte	1,28,45,45
	.byte	32
	.space	1
	.byte	13,28,44,45
	.byte	31
	.space	1
	.byte	28,28,45,45
	.byte	32
	.space	1
	.byte	45,45,32
	.space	3
	.byte	28,28,45,45
	.byte	32
	.space	1
	.byte	28,28,45,45
	.byte	32
	.space	1
	.byte	28,28,45,45
	.byte	32
	.space	1
	.byte	45,45,32
	.space	3
	.calls	'NvM_Fsm','NvM_FsmQuery'
	.calls	'NvM_Fsm','NvM_FsmAction'
	.calls	'NvM_FsmQuery','__INDIRECT__'
	.calls	'NvM_FsmAction','__INDIRECT__'
	.calls	'NvM_JobProcInit','',0
	.calls	'NvM_Fsm','',0
	.calls	'NvM_FsmQuery','',0
	.extern	NvM_ActionTable_ap
	.extern	NvM_QueryTable_ap
	.extern	NvM_CurrentJob_t
	.extern	__INDIRECT__
	.calls	'NvM_FsmAction','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L20:
	.word	9705
	.half	3
	.word	.L21
	.byte	4
.L19:
	.byte	1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L22
.L56:
	.byte	2,1,96,9,1,3
	.byte	'NVM_STATE_UNINIT',0,0,3
	.byte	'NVM_STATE_IDLE',0,1,3
	.byte	'NVM_STATE_NORMAL_PRIO_JOB',0,2,3
	.byte	'NVM_STATE_MULTI_BLOCK_JOB',0,3,3
	.byte	'NVM_STATE_READ_READ_DATA',0,4,3
	.byte	'NVM_STATE_READ_DATA_VALIDATION',0,5,3
	.byte	'NVM_STATE_READ_CMP_CRC',0,6,3
	.byte	'NVM_STATE_READ_IMPL_RECOV',0,7,3
	.byte	'NVM_STATE_READ_LOAD_ROM',0,8,3
	.byte	'NVM_STATE_READ_FINALIZE',0,9,3
	.byte	'NVM_STATE_WRITE_INITIAL',0,10,3
	.byte	'NVM_STATE_WRITE_CRCCALC',0,11,3
	.byte	'NVM_STATE_WRITE_CRCCOMPMECHANISM',0,12,3
	.byte	'NVM_STATE_WRITE_TEST_PRI_READ',0,13,3
	.byte	'NVM_STATE_WRITE_TEST_SEC_READ',0,14,3
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_1',0,15,3
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_2',0,16,3
	.byte	'NVM_STATE_RESTORE_LOAD_ROM',0,17,3
	.byte	'NVM_STATE_INVALIDATING_BLOCK',0,18,3
	.byte	'NVM_STATE_ERASE_ERASE_BLOCK',0,19,3
	.byte	'NVM_STATE_READALL_PROC_CONFIG_ID',0,20,3
	.byte	'NVM_STATE_READALL_PROC_RAM_BLOCK',0,21,3
	.byte	'NVM_STATE_READALL_CHK_SKIP',0,22,3
	.byte	'NVM_STATE_READALL_KILLED',0,23,3
	.byte	'NVM_STATE_READALL_WR_ONCE_PROT',0,24,3
	.byte	'NVM_STATE_READALL_CHK_RAM_VALIDITY',0,25,3
	.byte	'NVM_STATE_READALL_READ_NV',0,26,3
	.byte	'NVM_STATE_READALL_LOAD_DEFAULTS',0,27,3
	.byte	'NVM_STATE_READALL_READABILITY_CHECK',0,28,3
	.byte	'NVM_STATE_WRITEALL_PROC_BLOCK',0,29,3
	.byte	'NVM_STATE_WRITEALL_WRITE_FSM',0,30,3
	.byte	'NVM_STATE_WRITEALL_WAIT_MEMHWA',0,31,3
	.byte	'NVM_STATE_FSM_FINISHED',0,32,0,4,1,228,1,9,2,2,2,42,9,1,3
	.byte	'NVM_ACT_ID_SetInitialAttr',0,0,3
	.byte	'NVM_ACT_ID_InitMainFsm',0,1,3
	.byte	'NVM_ACT_ID_InitBlock',0,2,3
	.byte	'NVM_ACT_ID_InitReadAll',0,3,3
	.byte	'NVM_ACT_ID_InitReadBlockSubFsm',0,4,3
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaultsSubFsm',0,5,3
	.byte	'NVM_ACT_ID_InitWriteAll',0,6,3
	.byte	'NVM_ACT_ID_InitWriteBlock',0,7,3
	.byte	'NVM_ACT_ID_InitWriteBlockFsm',0,8,3
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaults',0,9,3
	.byte	'NVM_ACT_ID_FinishMainJob',0,10,3
	.byte	'NVM_ACT_ID_KillWritAll',0,11,3
	.byte	'NVM_ACT_ID_FinishBlock',0,12,3
	.byte	'NVM_ACT_ID_InitNextBlockReadAll',0,13,3
	.byte	'NVM_ACT_ID_InitNextBlockWriteAll',0,14,3
	.byte	'NVM_ACT_ID_FinishCfgIdCheck',0,15,3
	.byte	'NVM_ACT_ID_FinishReadBlock',0,16,3
	.byte	'NVM_ACT_ID_FinishWriteBlock',0,17,3
	.byte	'NVM_ACT_ID_FinishEraseBlock',0,18,3
	.byte	'NVM_ACT_ID_EraseNvBlock',0,19,3
	.byte	'NVM_ACT_ID_InvalidateNvBlock',0,20,3
	.byte	'NVM_ACT_ID_ProcessCrc',0,21,3
	.byte	'NVM_ACT_ID_WriteNvBlock',0,22,3
	.byte	'NVM_ACT_ID_ReadNvBlock',0,23,3
	.byte	'NVM_ACT_ID_ProcessCrcRead',0,24,3
	.byte	'NVM_ACT_ID_ReadCopyData',0,25,3
	.byte	'NVM_ACT_ID_RestoreRomDefaults',0,26,3
	.byte	'NVM_ACT_ID_FinishRestoreRomDefaults',0,27,3
	.byte	'NVM_ACT_ID_TestBlockBlank',0,28,3
	.byte	'NVM_ACT_ID_ValidateRam',0,29,3
	.byte	'NVM_ACT_ID_SetupRedundant',0,30,3
	.byte	'NVM_ACT_ID_SetupOther',0,31,3
	.byte	'NVM_ACT_ID_UpdateNvState',0,32,3
	.byte	'NVM_ACT_ID_SetReqIntegrityFailed',0,33,3
	.byte	'NVM_ACT_ID_SetReqSkipped',0,34,3
	.byte	'NVM_ACT_ID_SetReqNotOk',0,35,3
	.byte	'NVM_ACT_ID_SetReqOk',0,36,3
	.byte	'NVM_ACT_ID_SetBlockPendingWriteAll',0,37,3
	.byte	'NVM_ACT_ID_CopyNvDataToBuf',0,38,3
	.byte	'NVM_ACT_ID_GetMultiBlockJob',0,39,3
	.byte	'NVM_ACT_ID_CancelNV',0,40,3
	.byte	'NVM_ACT_ID_KillSubFsm',0,41,3
	.byte	'NVM_ACT_ID_FinishReadBlockAndSetSkipped',0,42,3
	.byte	'NVM_ACT_ID_GetNormalPrioJob',0,43,3
	.byte	'NVM_ACT_ID_Wait',0,44,3
	.byte	'NVM_ACT_ID_Nop',0,45,0,5
	.byte	'ExitHandler_t',0,1
	.word	1173
	.byte	2,35,0,5
	.byte	'EntryHandler_t',0,1
	.word	1173
	.byte	2,35,1,0,6
	.word	1167
	.byte	7
	.word	2539
	.byte	6
	.word	1167
	.byte	7
	.word	2549
.L60:
	.byte	8
	.byte	'NvM_StateChangeActionsPtrType',0,1,234,1,75
	.word	2554
	.byte	4,1,249,1,9,24,4,1,236,1,9,6,2,3,48,9,1,3
	.byte	'NVM_QRY_ID_BLK_WRITE_ALL',0,0,3
	.byte	'NVM_QRY_ID_CANCEL_WRITE_ALL',0,1,3
	.byte	'NVM_QR_ID_WRITEALL_KILLED',0,2,3
	.byte	'NVM_QRY_ID_CRC_BUSY',0,3,3
	.byte	'NVM_QRY_ID_DATA_COPY_BUSY',0,4,3
	.byte	'NVM_QRY_ID_CRC_MATCH',0,5,3
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_READALL',0,6,3
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_WRITEALL',0,7,3
	.byte	'NVM_QRY_ID_LAST_RESULT_OK',0,8,3
	.byte	'NVM_QRY_ID_MAIN_FSM_RUNNING',0,9,3
	.byte	'NVM_QRY_ID_MULTI_BLK_JOB',0,10,3
	.byte	'NVM_QRY_ID_NORMAL_PRIO_JOB',0,11,3
	.byte	'NVM_QRY_ID_NV_BUSY',0,12,3
	.byte	'NVM_QRY_ID_MEMHWA_BUSY',0,13,3
	.byte	'NVM_QRY_ID_RAM_VALID',0,14,3
	.byte	'NVM_QRY_ID_REDUNDANT_BLOCK',0,15,3
	.byte	'NVM_QRY_ID_SKIP_BLOCK',0,16,3
	.byte	'NVM_QRY_ID_SUB_FSM_RUNNING',0,17,3
	.byte	'NVM_QRY_ID_WRITE_BLOCK_ONCE',0,18,3
	.byte	'NVM_QRY_ID_WRITE_RETRIES_EXCEEDED',0,19,3
	.byte	'NVM_QRY_ID_HAS_ROM',0,20,3
	.byte	'NVM_QRY_ID_EXT_RUNTIME',0,21,3
	.byte	'NvM_QRY_CRC_COMP_MECHANISM_SKIPWRITE',0,22,3
	.byte	'NVM_QRY_POST_READ_TRANSFORM',0,23,3
	.byte	'NVM_QRY_READALL_KILLED',0,24,3
	.byte	'NVM_QRY_SYNCDECRYPT',0,25,3
	.byte	'NVM_QRY_SYNCENCRYPT',0,26,3
	.byte	'NVM_QRY_CSM_RETRIES_NECESSARY',0,27,3
	.byte	'NVM_QRY_ID_TRUE',0,28,0,9,2
	.word	2610
	.byte	10,1,0,5
	.byte	'Queries_at',0,2
	.word	3414
	.byte	2,35,0,5
	.byte	'Actions_t',0,2
	.word	1167
	.byte	2,35,2,5
	.byte	'NextState_t',0,1
	.word	182
	.byte	2,35,4,0,9,18
	.word	2604
	.byte	10,2,0,5
	.byte	'ChangeConditions_at',0,18
	.word	3484
	.byte	2,35,0,4,1,243,1,9,4,5
	.byte	'Actions_t',0,2
	.word	1167
	.byte	2,35,0,5
	.byte	'NextState_t',0,1
	.word	182
	.byte	2,35,2,0,5
	.byte	'FinalCondition_t',0,4
	.word	3522
	.byte	2,35,18,0,6
	.word	2598
	.byte	7
	.word	3596
.L62:
	.byte	6
	.word	3601
.L64:
	.byte	11
	.byte	'unsigned char',0,1,8,6
	.word	2610
	.byte	7
	.word	3628
	.byte	6
	.word	2610
	.byte	7
	.word	3638
.L66:
	.byte	8
	.byte	'NvM_StateQueryPtrType',0,3,96,69
	.word	3643
	.byte	12
	.byte	'__INDIRECT__',0,4,1,1,1,1,1,13
	.byte	'void',0,7
	.word	3698
	.byte	8
	.byte	'__prof_adm',0,4,1,1
	.word	3704
	.byte	14,1,7
	.word	3728
	.byte	8
	.byte	'__codeptr',0,4,1,1
	.word	3730
	.byte	8
	.byte	'uint8',0,5,90,29
	.word	3611
	.byte	11
	.byte	'short int',0,2,5,8
	.byte	'sint16',0,5,91,29
	.word	3767
	.byte	11
	.byte	'unsigned short int',0,2,7,8
	.byte	'uint16',0,5,92,29
	.word	3795
	.byte	11
	.byte	'unsigned long int',0,4,7,8
	.byte	'uint32',0,5,94,29
	.word	3832
	.byte	8
	.byte	'boolean',0,5,105,29
	.word	3611
	.byte	11
	.byte	'unsigned long long int',0,8,7,8
	.byte	'uint64',0,5,130,1,30
	.word	3884
	.byte	8
	.byte	'Std_ReturnType',0,6,113,15
	.word	3611
	.byte	8
	.byte	'PduLengthType',0,7,76,22
	.word	3795
	.byte	8
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,8,112,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,8,115,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,8,118,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,8,121,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,8,124,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,8,136,1,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,8,139,1,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,8,148,1,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,8,151,1,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,8,141,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,8,144,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,8,147,3,16
	.word	3795
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,8,150,3,16
	.word	3795
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,8,153,3,16
	.word	3795
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,8,156,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,8,159,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,8,162,3,16
	.word	3795
	.byte	8
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,8,165,3,16
	.word	3795
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,8,180,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,8,183,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,8,186,3,16
	.word	3832
	.byte	8
	.byte	'IdtAppCom_MainPwrFltRsn',0,8,192,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGMDiags',0,8,195,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGMFltRsn',0,8,198,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGMSts',0,8,201,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGMSwSts',0,8,207,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,8,210,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,8,213,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,8,216,3,16
	.word	3795
	.byte	8
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,8,219,3,16
	.word	3795
	.byte	8
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,8,225,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,8,228,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_RednPwrFltRsn',0,8,231,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,8,234,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_SHWAIndSts',0,8,237,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_SHWASysFltSts',0,8,240,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_SHWASysMsg',0,8,243,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,8,246,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_SHWASysSts',0,8,249,3,15
	.word	3611
	.byte	8
	.byte	'IdtAppCom_SHWASysTakeOver',0,8,252,3,15
	.word	3611
	.byte	8
	.byte	'NvM_BlockIdType',0,8,227,5,16
	.word	3795
	.byte	8
	.byte	'NvM_RequestResultType',0,8,207,6,15
	.word	3611
	.byte	8
	.byte	'NvM_ServiceIdType',0,8,231,6,15
	.word	3611
	.byte	11
	.byte	'unsigned int',0,4,7,8
	.byte	'Rte_BitType',0,8,230,7,22
	.word	5611
	.byte	2,9,59,9,1,3
	.byte	'MEMIF_UNINIT',0,0,3
	.byte	'MEMIF_IDLE',0,1,3
	.byte	'MEMIF_BUSY',0,2,3
	.byte	'MEMIF_BUSY_INTERNAL',0,3,0,8
	.byte	'MemIf_StatusType',0,9,65,3
	.word	5648
	.byte	2,9,72,9,1,3
	.byte	'MEMIF_JOB_OK',0,0,3
	.byte	'MEMIF_JOB_FAILED',0,1,3
	.byte	'MEMIF_JOB_PENDING',0,2,3
	.byte	'MEMIF_JOB_CANCELED',0,3,3
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,3
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,8
	.byte	'MemIf_JobResultType',0,9,80,3
	.word	5742
	.byte	15
	.word	3611
	.byte	1,1,16
	.word	3795
	.byte	16
	.word	3795
	.byte	7
	.word	3611
	.byte	16
	.word	5917
	.byte	16
	.word	3795
	.byte	0,7
	.word	5900
	.byte	8
	.byte	'MemIf_ApiReadType',0,10,120,9
	.word	5933
	.byte	15
	.word	3611
	.byte	1,1,16
	.word	3795
	.byte	16
	.word	5917
	.byte	0,7
	.word	5964
	.byte	8
	.byte	'MemIf_ApiWriteType',0,10,121,9
	.word	5982
	.byte	15
	.word	3611
	.byte	1,1,16
	.word	3795
	.byte	0,7
	.word	6014
	.byte	8
	.byte	'MemIf_ApiEraseImmediateBlockType',0,10,122,9
	.word	6027
	.byte	8
	.byte	'MemIf_ApiInvalidateBlockType',0,10,123,9
	.word	6027
	.byte	17,1,1,7
	.word	6110
	.byte	8
	.byte	'MemIf_ApiCancelType',0,10,124,9
	.word	6113
	.byte	18
	.word	5648
	.byte	1,1,7
	.word	6146
	.byte	8
	.byte	'MemIf_ApiGetStatusType',0,10,125,9
	.word	6153
	.byte	18
	.word	5742
	.byte	1,1,7
	.word	6189
	.byte	8
	.byte	'MemIf_ApiGetJobResultType',0,10,126,9
	.word	6196
	.byte	19,1,1,2,9,88,9,1,3
	.byte	'MEMIF_MODE_SLOW',0,0,3
	.byte	'MEMIF_MODE_FAST',0,1,0,16
	.word	6238
	.byte	0,7
	.word	6235
	.byte	8
	.byte	'MemIf_ApiSetModeType',0,10,127,9
	.word	6286
	.byte	8
	.byte	'NvM_BitFieldType',0,11,113,22
	.word	5611
	.byte	8
	.byte	'NvM_CrcType',0,11,116,26
	.word	5611
	.byte	4,11,124,9,4,5
	.byte	'NvDataIndex_t',0,1
	.word	3611
	.byte	2,35,0,5
	.byte	'NvRamErrorStatus_u8',0,1
	.word	3611
	.byte	2,35,1,5
	.byte	'NvRamAttributes_u8',0,1
	.word	3611
	.byte	2,35,2,0,8
	.byte	'NvM_RamMngmtAreaType',0,11,129,1,3
	.word	6365
	.byte	7
	.word	6365
	.byte	8
	.byte	'NvM_RamMngmtPtrType',0,11,131,1,65
	.word	6481
	.byte	15
	.word	3611
	.byte	1,1,16
	.word	3611
	.byte	16
	.word	3611
	.byte	0,7
	.word	6515
	.byte	8
	.byte	'NvM_JobEndCbkPtrType',0,11,134,1,9
	.word	6533
	.byte	15
	.word	3611
	.byte	1,1,16
	.word	3795
	.byte	16
	.word	3611
	.byte	16
	.word	3611
	.byte	0,7
	.word	6568
	.byte	8
	.byte	'NvM_JobEndCbkExtPtrType',0,11,137,1,9
	.word	6591
	.byte	18
	.word	3611
	.byte	1,1,7
	.word	6629
	.byte	8
	.byte	'NvM_InitCbkPtrType',0,11,142,1,9
	.word	6636
	.byte	15
	.word	3611
	.byte	1,1,16
	.word	3795
	.byte	16
	.word	3704
	.byte	16
	.word	3795
	.byte	0,7
	.word	6669
	.byte	8
	.byte	'NvM_InitCbkExtPtrType',0,11,145,1,9
	.word	6692
	.byte	15
	.word	3611
	.byte	1,1,16
	.word	3704
	.byte	0,7
	.word	6728
	.byte	8
	.byte	'NvM_WriteRamToNvMCbkPtrType',0,11,148,1,9
	.word	6741
	.byte	15
	.word	3611
	.byte	1,1,6
	.word	3698
	.byte	7
	.word	6790
	.byte	16
	.word	6795
	.byte	0,7
	.word	6783
	.byte	8
	.byte	'NvM_ReadRamFromNvMCbkPtrType',0,11,149,1,9
	.word	6806
	.byte	19,1,1,16
	.word	3795
	.byte	16
	.word	3704
	.byte	16
	.word	3795
	.byte	0,7
	.word	6849
	.byte	8
	.byte	'NvM_PreWriteTransformCbkPtrType',0,11,152,1,9
	.word	6868
	.byte	8
	.byte	'NvM_PostReadTransformCbkPtrType',0,11,153,1,9
	.word	6692
	.byte	7
	.word	3611
	.byte	8
	.byte	'NvM_RamAddressType',0,11,161,1,48
	.word	6955
	.byte	6
	.word	3611
	.byte	7
	.word	6988
	.byte	8
	.byte	'NvM_ConstRamAddressType',0,11,162,1,50
	.word	6993
	.byte	8
	.byte	'NvM_RomAddressType',0,11,164,1,50
	.word	6993
	.byte	8
	.byte	'NvM_RamCrcAddressType',0,11,170,1,51
	.word	6955
	.byte	4,11,196,1,9,64,5
	.byte	'RamBlockDataAddr_t',0,4
	.word	6960
	.byte	2,35,0,5
	.byte	'RomBlockDataAddr_pt',0,4
	.word	7031
	.byte	2,35,4,5
	.byte	'InitCbkFunc_pt',0,4
	.word	6641
	.byte	2,35,8,5
	.byte	'InitCbkExtFunc_pt',0,4
	.word	6697
	.byte	2,35,12,5
	.byte	'JobEndCbkFunc_pt',0,4
	.word	6538
	.byte	2,35,16,5
	.byte	'JobEndCbkExtFunc_pt',0,4
	.word	6596
	.byte	2,35,20,5
	.byte	'CbkGetMirrorFunc_pt',0,4
	.word	6811
	.byte	2,35,24,5
	.byte	'CbkSetMirrorFunc_pt',0,4
	.word	6746
	.byte	2,35,28,5
	.byte	'CbkPreWriteTransform',0,4
	.word	6873
	.byte	2,35,32,5
	.byte	'CbkPostReadTransform',0,4
	.word	6914
	.byte	2,35,36,5
	.byte	'RamBlockCrcAddr_t',0,4
	.word	7059
	.byte	2,35,40,5
	.byte	'CRCCompMechanismCrcAddr_t',0,4
	.word	7059
	.byte	2,35,44,5
	.byte	'NvIdentifier_u16',0,2
	.word	3795
	.byte	2,35,48,5
	.byte	'NvBlockLength_u16',0,2
	.word	3795
	.byte	2,35,50,5
	.byte	'NvCryptoReference',0,1
	.word	3611
	.byte	2,35,52,5
	.byte	'NvBlockNVRAMDataLength',0,2
	.word	3795
	.byte	2,35,54,20
	.byte	'NvBlockCount_u8',0,1
	.word	3611
	.byte	8,0,2,35,56,20
	.byte	'BlockPrio_u8',0,1
	.word	3611
	.byte	8,0,2,35,57,20
	.byte	'DeviceId_u8',0,1
	.word	3611
	.byte	4,4,2,35,58,20
	.byte	'MngmtType_t',0,1
	.word	3611
	.byte	2,2,2,35,58,20
	.byte	'CrcSettings',0,1
	.word	3611
	.byte	2,0,2,35,58,20
	.byte	'Flags_u8',0,1
	.word	3611
	.byte	8,0,2,35,59,20
	.byte	'NotifyBswM',0,1
	.word	3611
	.byte	1,7,2,35,60,0,8
	.byte	'NvM_BlockDescriptorType',0,11,223,1,3
	.word	7090
	.byte	7
	.word	3611
	.byte	6
	.word	3611
	.byte	7
	.word	7752
	.byte	7
	.word	6629
	.byte	7
	.word	6669
	.byte	7
	.word	6515
	.byte	7
	.word	6568
	.byte	7
	.word	6783
	.byte	7
	.word	6728
	.byte	7
	.word	6849
	.byte	7
	.word	6669
	.byte	7
	.word	3611
	.byte	8
	.byte	'NvM_CsmJobIdType',0,11,231,1,16
	.word	3832
	.byte	6
	.word	7090
	.byte	7
	.word	7833
	.byte	8
	.byte	'NvM_BlockDescrPtrType',0,11,240,1,71
	.word	7838
	.byte	2,11,243,1,9,1,3
	.byte	'NVM_INT_FID_WRITE_BLOCK',0,0,3
	.byte	'NVM_INT_FID_READ_BLOCK',0,1,3
	.byte	'NVM_INT_FID_RESTORE_DEFAULTS',0,2,3
	.byte	'NVM_INT_FID_INVALIDATE_NV_BLOCK',0,3,3
	.byte	'NVM_INT_FID_ERASE_BLOCK',0,4,3
	.byte	'NVM_INT_FID_WRITE_ALL',0,5,3
	.byte	'NVM_INT_FID_READ_ALL',0,6,3
	.byte	'NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS',0,7,3
	.byte	'NVM_INT_FID_NO_JOB_PENDING',0,8,0,8
	.byte	'NvM_InternalServiceIdType',0,11,254,1,3
	.word	7874
	.byte	8
	.byte	'NvM_QueueEntryRefType',0,11,129,2,15
	.word	3611
	.byte	8
	.byte	'NvM_StateActionIdType',0,2,110,3
	.word	1173
	.byte	8
	.byte	'NvM_ActFctPtrType',0,2,113,9
	.word	6113
	.byte	9,184,1
	.word	8233
	.byte	10,45,0,6
	.word	8259
	.byte	21
	.byte	'NvM_ActionTable_ap',0,2,127,52
	.word	8269
	.byte	1,1,7
	.word	6110
	.byte	8
	.byte	'NvM_StateQueryIdType',0,3,93,3
	.word	2610
	.byte	7
	.word	6629
	.byte	8
	.byte	'NvM_QryFctPtrType',0,3,102,9
	.word	8337
	.byte	9,116
	.word	8342
	.byte	10,28,0,6
	.word	8368
	.byte	21
	.byte	'NvM_QueryTable_ap',0,3,109,52
	.word	8377
	.byte	1,1,7
	.word	6629
	.byte	8
	.byte	'NvM_CrcBufferPtrType',0,12,68,59
	.word	6955
	.byte	19,1,1,6
	.word	3611
	.byte	7
	.word	8447
	.byte	16
	.word	8452
	.byte	16
	.word	3795
	.byte	7
	.word	3832
	.byte	16
	.word	8467
	.byte	0,7
	.word	8444
	.byte	8
	.byte	'NvM_CrcCalculateFPtr',0,12,74,9
	.word	8478
	.byte	15
	.word	3611
	.byte	1,1,16
	.word	8452
	.byte	16
	.word	8452
	.byte	0,7
	.word	8512
	.byte	8
	.byte	'NvM_CrcCompareFPtr',0,12,75,9
	.word	8530
	.byte	19,1,1,16
	.word	5917
	.byte	16
	.word	8452
	.byte	0,7
	.word	8562
	.byte	8
	.byte	'NvM_CrcCopyToBufferFPtr',0,12,76,9
	.word	8576
	.byte	22
	.byte	'NvM_CrcHandlerClass',0,12,79,8,20,5
	.byte	'calc',0,4
	.word	8483
	.byte	2,35,0,5
	.byte	'compare',0,4
	.word	8535
	.byte	2,35,4,5
	.byte	'copyToBuffer',0,4
	.word	8581
	.byte	2,35,8,5
	.byte	'initialCrcValue',0,4
	.word	3832
	.byte	2,35,12,5
	.byte	'crcLength',0,1
	.word	3611
	.byte	2,35,16,0,6
	.word	8613
	.byte	7
	.word	8736
	.byte	8
	.byte	'NvM_CrcHandlerClassConstPtr',0,12,88,75
	.word	8741
	.byte	7
	.word	8444
	.byte	7
	.word	8512
	.byte	7
	.word	8562
	.byte	22
	.byte	'NvM_CrcJobStruct',0,12,91,16,20,5
	.byte	'CurrentCrcValue',0,4
	.word	3832
	.byte	2,35,0,5
	.byte	'RamData_pt',0,4
	.word	6998
	.byte	2,35,4,5
	.byte	'CrcBuffer',0,4
	.word	8415
	.byte	2,35,8,5
	.byte	'HandlerInstance_pt',0,4
	.word	8746
	.byte	2,35,12,5
	.byte	'RemainingLength_u16',0,2
	.word	3795
	.byte	2,35,16,0,8
	.byte	'NvM_CrcJobType',0,12,98,3
	.word	8797
	.byte	6
	.word	3611
	.byte	7
	.word	8964
	.byte	7
	.word	3611
	.byte	6
	.word	8613
	.byte	7
	.word	8979
	.byte	4,1,57,9,8,5
	.byte	'JobBlockId_t',0,2
	.word	3795
	.byte	2,35,0,5
	.byte	'JobServiceId_t',0,1
	.word	7874
	.byte	2,35,2,5
	.byte	'RamAddr_t',0,4
	.word	6960
	.byte	2,35,4,0,8
	.byte	'NvM_JobType',0,1,62,3
	.word	8989
.L71:
	.byte	4,1,66,9,44,5
	.byte	'Descriptor_pt',0,4
	.word	7843
	.byte	2,35,0,5
	.byte	'Mngmt_pt',0,4
	.word	6486
	.byte	2,35,4,5
	.byte	'RamAddr_t',0,4
	.word	6960
	.byte	2,35,8,5
	.byte	'NvRamAddr_t',0,4
	.word	6960
	.byte	2,35,12,5
	.byte	'BlockCrcJob_t',0,20
	.word	8797
	.byte	2,35,16,5
	.byte	'NvIdentifier_u16',0,2
	.word	3795
	.byte	2,35,36,5
	.byte	'ByteCount_u16',0,2
	.word	3795
	.byte	2,35,38,5
	.byte	'LastResult_t',0,1
	.word	3611
	.byte	2,35,40,5
	.byte	'WriteRetryCounter_u8',0,1
	.word	3611
	.byte	2,35,41,5
	.byte	'InternalFlags_u8',0,1
	.word	3611
	.byte	2,35,42,5
	.byte	'NvState_u8',0,1
	.word	3611
	.byte	2,35,43,0,8
	.byte	'NvM_BlockInfoType',0,1,83,3
	.word	9080
	.byte	6
	.word	7090
	.byte	7
	.word	9363
	.byte	7
	.word	6365
	.byte	8
	.byte	'NvM_StateIdType',0,1,215,1,3
	.word	182
	.byte	4,1,217,1,9,4,5
	.byte	'InitialActionId',0,1
	.word	1173
	.byte	2,35,0,5
	.byte	'InitialState_t',0,1
	.word	182
	.byte	2,35,1,5
	.byte	'PublicFid_t',0,1
	.word	3611
	.byte	2,35,2,0,8
	.byte	'NvM_IntServiceDescrType',0,1,222,1,3
	.word	9403
	.byte	8
	.byte	'NvM_StateChangeActionsType',0,1,232,1,3
	.word	1167
	.byte	8
	.byte	'NvM_StateChangeIfDescrType',0,1,241,1,3
	.word	2604
	.byte	8
	.byte	'NvM_StateChangeElseDescrType',0,1,247,1,3
	.word	3522
	.byte	8
	.byte	'NvM_StateDescrType',0,1,253,1,3
	.word	2598
	.byte	21
	.byte	'NvM_CurrentJob_t',0,1,160,2,43
	.word	8989
	.byte	1,1,9,36
	.word	9403
	.byte	10,8,0
.L72:
	.byte	6
	.word	9679
	.byte	9,152,6
	.word	2598
	.byte	10,32,0
.L73:
	.byte	6
	.word	9693
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,4,1,58,15,59,15,57,15,11,15,0,0,3,40,0,3,8,28,13,0,0,4
	.byte	19,1,58,15,59,15,57,15,11,15,0,0,5,13,0,3,8,11,15,73,19,56,9,0,0,6,38,0,73,19,0,0,7,15,0,73,19,0,0,8,22
	.byte	0,3,8,58,15,59,15,57,15,73,19,0,0,9,1,1,11,15,73,19,0,0,10,33,0,47,15,0,0,11,36,0,3,8,11,15,62,15,0,0
	.byte	12,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,13,59,0,3,8,0,0,14,21,0,54,15,0,0,15,21,1,73,19,54
	.byte	15,39,12,0,0,16,5,0,73,19,0,0,17,21,0,54,15,39,12,0,0,18,21,0,73,19,54,15,39,12,0,0,19,21,1,54,15,39,12
	.byte	0,0,20,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,21,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,22
	.byte	19,1,3,8,58,15,59,15,57,15,11,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L22:
	.word	.L90-.L89
.L89:
	.half	3
	.word	.L92-.L91
.L91:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM',0
	.byte	0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Std_Types.h',0,1,0,0
	.byte	'ComStack_Types.h',0,1,0,0
	.byte	'Rte_Type.h',0,2,0,0
	.byte	'MemIf_Types.h',0,3,0,0
	.byte	'_Cfg.h',0,3,0,0
	.byte	'_PrivateCfg.h',0,4,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.h',0,0,0,0,0
.L92:
.L90:
	.sdecl	'.debug_info',debug,cluster('NvM_JobProcInit')
	.sect	'.debug_info'
.L23:
	.word	234
	.half	3
	.word	.L24
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L26,.L25
	.byte	2
	.word	.L19
	.byte	3
	.byte	'NvM_JobProcInit',0,1,216,11,30,1,1,1
	.word	.L12,.L55,.L11
	.byte	4
	.word	.L12,.L55
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_JobProcInit')
	.sect	'.debug_abbrev'
.L24:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_JobProcInit')
	.sect	'.debug_line'
.L25:
	.word	.L94-.L93
.L93:
	.half	3
	.word	.L96-.L95
.L95:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0,0,0,0,0
.L96:
	.byte	5,5,7,0,5,2
	.word	.L12
	.byte	3,217,11,1,5,39,9
	.half	.L97-.L12
	.byte	1,5,37,1,5,5,9
	.half	.L98-.L97
	.byte	3,2,1,5,47,9
	.half	.L99-.L98
	.byte	1,5,45,1,5,5,9
	.half	.L100-.L99
	.byte	3,2,1,5,26,9
	.half	.L101-.L100
	.byte	1,5,24,1,5,5,9
	.half	.L102-.L101
	.byte	3,1,1,5,24,9
	.half	.L103-.L102
	.byte	1,5,5,9
	.half	.L104-.L103
	.byte	3,1,1,5,26,9
	.half	.L105-.L104
	.byte	1,5,24,1,5,1,9
	.half	.L106-.L105
	.byte	3,1,1,7,9
	.half	.L27-.L106
	.byte	0,1,1
.L94:
	.sdecl	'.debug_ranges',debug,cluster('NvM_JobProcInit')
	.sect	'.debug_ranges'
.L26:
	.word	-1,.L12,0,.L27-.L12,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Fsm')
	.sect	'.debug_info'
.L28:
	.word	359
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L31,.L30
	.byte	2
	.word	.L19
	.byte	3
	.byte	'NvM_Fsm',0,1,236,11,41
	.word	.L56
	.byte	1,1,1
	.word	.L14,.L57,.L13
	.byte	4
	.byte	'NvM_CurrentState_t',0,1,236,11,65
	.word	.L56,.L58
	.byte	5
	.word	.L14,.L57
	.byte	6
	.byte	'NvM_RetState_tloc',0,1,239,11,21
	.word	.L56,.L59
	.byte	6
	.byte	'ChangeActions_ptloc',0,1,240,11,35
	.word	.L60,.L61
	.byte	6
	.byte	'CurrentState_ptloc',0,1,241,11,68
	.word	.L62,.L63
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Fsm')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_Fsm')
	.sect	'.debug_line'
.L30:
	.word	.L108-.L107
.L107:
	.half	3
	.word	.L110-.L109
.L109:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0,0,0,0,0
.L110:
	.byte	5,32,7,0,5,2
	.word	.L14
	.byte	3,241,11,1,5,10,1,5,32,9
	.half	.L111-.L14
	.byte	1,5,63,9
	.half	.L75-.L111
	.byte	3,6,1,5,5,9
	.half	.L74-.L75
	.byte	1,5,71,7,9
	.half	.L112-.L74
	.byte	3,2,1,5,74,9
	.half	.L77-.L112
	.byte	3,1,1,5,83,9
	.half	.L113-.L77
	.byte	3,127,1,5,66,9
	.half	.L2-.L113
	.byte	3,3,1,5,69,9
	.half	.L114-.L2
	.byte	1,5,10,9
	.half	.L115-.L114
	.byte	1,5,71,7,9
	.half	.L116-.L115
	.byte	3,2,1,5,74,9
	.half	.L78-.L116
	.byte	3,1,1,5,83,9
	.half	.L117-.L78
	.byte	3,127,1,5,66,9
	.half	.L4-.L117
	.byte	3,3,1,5,69,9
	.half	.L118-.L4
	.byte	1,5,10,9
	.half	.L119-.L118
	.byte	1,5,71,7,9
	.half	.L120-.L119
	.byte	3,2,1,5,74,9
	.half	.L79-.L120
	.byte	3,1,1,5,83,9
	.half	.L121-.L79
	.byte	3,127,1,5,65,9
	.half	.L6-.L121
	.byte	3,5,1,5,50,9
	.half	.L80-.L6
	.byte	3,1,1,5,19,9
	.half	.L3-.L80
	.byte	3,3,1,5,5,9
	.half	.L82-.L3
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L32-.L82
	.byte	0,1,1
.L108:
	.sdecl	'.debug_ranges',debug,cluster('NvM_Fsm')
	.sect	'.debug_ranges'
.L31:
	.word	-1,.L14,0,.L32-.L14,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_FsmQuery')
	.sect	'.debug_info'
.L33:
	.word	283
	.half	3
	.word	.L34
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L36,.L35
	.byte	2
	.word	.L19
	.byte	3
	.byte	'NvM_FsmQuery',0,1,156,12,43
	.word	.L64
	.byte	1,1
	.word	.L16,.L65,.L15
	.byte	4
	.byte	'NvM_Queries_at',0,1,156,12,78
	.word	.L66,.L67
	.byte	5
	.word	.L16,.L65
	.byte	6
	.byte	'retVal',0,1,162,12,13
	.word	.L64,.L68
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_FsmQuery')
	.sect	'.debug_abbrev'
.L34:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_FsmQuery')
	.sect	'.debug_line'
.L35:
	.word	.L123-.L122
.L122:
	.half	3
	.word	.L125-.L124
.L124:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0,0,0,0,0
.L125:
	.byte	5,43,7,0,5,2
	.word	.L16
	.byte	3,155,12,1,5,54,9
	.half	.L84-.L16
	.byte	3,6,1,5,22,9
	.half	.L126-.L84
	.byte	1,5,39,9
	.half	.L127-.L126
	.byte	1,5,59,9
	.half	.L128-.L127
	.byte	1,5,5,9
	.half	.L83-.L128
	.byte	3,2,1,5,50,7,9
	.half	.L129-.L83
	.byte	3,2,1,5,35,9
	.half	.L130-.L129
	.byte	1,5,55,9
	.half	.L131-.L130
	.byte	1,5,1,7,9
	.half	.L9-.L131
	.byte	3,4,1,7,9
	.half	.L37-.L9
	.byte	0,1,1
.L123:
	.sdecl	'.debug_ranges',debug,cluster('NvM_FsmQuery')
	.sect	'.debug_ranges'
.L36:
	.word	-1,.L16,0,.L37-.L16,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_FsmAction')
	.sect	'.debug_info'
.L38:
	.word	259
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L41,.L40
	.byte	2
	.word	.L19
	.byte	3
	.byte	'NvM_FsmAction',0,1,180,12,40,1,1
	.word	.L18,.L69,.L17
	.byte	4
	.byte	'NvM_Actions_pt',0,1,180,12,84
	.word	.L60,.L70
	.byte	5
	.word	.L18,.L69
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_FsmAction')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_FsmAction')
	.sect	'.debug_line'
.L40:
	.word	.L133-.L132
.L132:
	.half	3
	.word	.L135-.L134
.L134:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0,0,0,0,0
.L135:
	.byte	5,40,7,0,5,2
	.word	.L18
	.byte	3,179,12,1,5,38,9
	.half	.L87-.L18
	.byte	3,2,1,5,5,9
	.half	.L136-.L87
	.byte	1,5,23,9
	.half	.L137-.L136
	.byte	1,5,54,9
	.half	.L138-.L137
	.byte	1,5,38,9
	.half	.L86-.L138
	.byte	3,1,1,5,23,9
	.half	.L139-.L86
	.byte	1,5,55,9
	.half	.L140-.L139
	.byte	1,5,1,7,9
	.half	.L42-.L140
	.byte	3,1,0,1,1
.L133:
	.sdecl	'.debug_ranges',debug,cluster('NvM_FsmAction')
	.sect	'.debug_ranges'
.L41:
	.word	-1,.L18,0,.L42-.L18,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_JobMainState_t')
	.sect	'.debug_info'
.L43:
	.word	214
	.half	3
	.word	.L44
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L19
	.byte	3
	.byte	'NvM_JobMainState_t',0,4,120,37
	.word	.L56
	.byte	1,5,3
	.word	NvM_JobMainState_t
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_JobMainState_t')
	.sect	'.debug_abbrev'
.L44:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_JobSubState_t')
	.sect	'.debug_info'
.L45:
	.word	213
	.half	3
	.word	.L46
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L19
	.byte	3
	.byte	'NvM_JobSubState_t',0,4,121,37
	.word	.L56
	.byte	1,5,3
	.word	NvM_JobSubState_t
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_JobSubState_t')
	.sect	'.debug_abbrev'
.L46:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_TaskState_t')
	.sect	'.debug_info'
.L47:
	.word	212
	.half	3
	.word	.L48
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L19
	.byte	3
	.byte	'NvM_TaskState_t',0,4,130,1,40
	.word	.L56
	.byte	1,5,3
	.word	NvM_TaskState_t
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_TaskState_t')
	.sect	'.debug_abbrev'
.L48:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CurrentBlockInfo_t')
	.sect	'.debug_info'
.L49:
	.word	219
	.half	3
	.word	.L50
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L19
	.byte	3
	.byte	'NvM_CurrentBlockInfo_t',0,4,138,1,42
	.word	.L71
	.byte	1,5,3
	.word	NvM_CurrentBlockInfo_t
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CurrentBlockInfo_t')
	.sect	'.debug_abbrev'
.L50:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_IntServiceDescrTable_at')
	.sect	'.debug_info'
.L51:
	.word	224
	.half	3
	.word	.L52
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L19
	.byte	3
	.byte	'NvM_IntServiceDescrTable_at',0,4,153,1,51
	.word	.L72
	.byte	1,5,3
	.word	NvM_IntServiceDescrTable_at
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_IntServiceDescrTable_at')
	.sect	'.debug_abbrev'
.L52:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_StateDescrTable_at')
	.sect	'.debug_info'
.L53:
	.word	218
	.half	3
	.word	.L54
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L19
	.byte	3
	.byte	'NvM_StateDescrTable_at',0,4,231,1,56
	.word	.L73
	.byte	5,3
	.word	NvM_StateDescrTable_at
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_StateDescrTable_at')
	.sect	'.debug_abbrev'
.L54:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_Fsm')
	.sect	'.debug_loc'
.L61:
	.word	0,0
.L63:
	.word	-1,.L14,.L75-.L14,.L57-.L14
	.half	1
	.byte	111
	.word	.L76-.L14,.L74-.L14
	.half	1
	.byte	100
	.word	.L81-.L14,.L82-.L14
	.half	1
	.byte	100
	.word	0,0
.L58:
	.word	-1,.L14,0,.L74-.L14
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L13:
	.word	-1,.L14,0,.L57-.L14
	.half	2
	.byte	138,0
	.word	0,0
.L59:
	.word	-1,.L14,.L77-.L14,.L2-.L14
	.half	5
	.byte	144,39,157,32,32
	.word	.L78-.L14,.L4-.L14
	.half	5
	.byte	144,39,157,32,32
	.word	.L79-.L14,.L6-.L14
	.half	5
	.byte	144,39,157,32,32
	.word	.L80-.L14,.L57-.L14
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_FsmAction')
	.sect	'.debug_loc'
.L70:
	.word	-1,.L18,0,.L86-.L18
	.half	1
	.byte	100
	.word	.L87-.L18,.L88-.L18
	.half	1
	.byte	111
	.word	0,0
.L17:
	.word	-1,.L18,0,.L69-.L18
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_FsmQuery')
	.sect	'.debug_loc'
.L15:
	.word	-1,.L16,0,.L65-.L16
	.half	2
	.byte	138,0
	.word	0,0
.L67:
	.word	-1,.L16,0,.L83-.L16
	.half	1
	.byte	100
	.word	.L84-.L16,.L85-.L16
	.half	1
	.byte	111
	.word	.L9-.L16,.L65-.L16
	.half	1
	.byte	111
	.word	0,0
.L68:
	.word	-1,.L16,.L83-.L16,.L65-.L16
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_JobProcInit')
	.sect	'.debug_loc'
.L11:
	.word	-1,.L12,0,.L55-.L12
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L141:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('NvM_JobProcInit')
	.sect	'.debug_frame'
	.word	24
	.word	.L141,.L12,.L55-.L12
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_Fsm')
	.sect	'.debug_frame'
	.word	12
	.word	.L141,.L14,.L57-.L14
	.sdecl	'.debug_frame',debug,cluster('NvM_FsmQuery')
	.sect	'.debug_frame'
	.word	12
	.word	.L141,.L16,.L65-.L16
	.sdecl	'.debug_frame',debug,cluster('NvM_FsmAction')
	.sect	'.debug_frame'
	.word	12
	.word	.L141,.L18,.L69-.L18

; ..\eeprom\NvM\NvM_JobProc.c	  1592  }
; ..\eeprom\NvM\NvM_JobProc.c	  1593  
; ..\eeprom\NvM\NvM_JobProc.c	  1594  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_JobProc.c	  1595    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_JobProc.c	  1596  
; ..\eeprom\NvM\NvM_JobProc.c	  1597  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_JobProc.c	  1598   *  END OF FILE: NvM_JobProc.c
; ..\eeprom\NvM\NvM_JobProc.c	  1599   *********************************************************************************************************************/

	; Module end
