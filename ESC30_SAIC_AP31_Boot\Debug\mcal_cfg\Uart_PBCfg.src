	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc25000a -c99 --dep-file=mcal_cfg\\.Uart_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\Uart_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\Uart_PBCfg.src ..\\mcal_cfg\\Uart_PBCfg.c"
	.compiler_name		"ctc"
	.name	"Uart_PBCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Uart_kChannelConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Uart_kChannelConfig0:	.type	object
	.size	Uart_kChannelConfig0,32
	.word	CDD_UartTransmit,CDD_UartRecive
	.space	8
	.half	24,1000,24
	.byte	9,1,1,8
	.space	3
	.byte	1
	.space	2
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Uart_ConfigRoot')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.global	Uart_ConfigRoot
	.align	4
Uart_ConfigRoot:	.type	object
	.size	Uart_ConfigRoot,8
	.word	Uart_kChannelConfig0
	.byte	1
	.space	3
	.calls	'__INDIRECT__','CDD_UartTransmit'
	.extern	CDD_UartTransmit
	.extern	CDD_UartRecive
	.extern	__INDIRECT__
	.calls	'__INDIRECT__','CDD_UartRecive'
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1428
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\Uart_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'CDD_UartTransmit',0,1,62,15,1,1,1,1,3
	.byte	'Uart_ErrorIdType',0,2,253,3,14,1,4
	.byte	'UART_NO_ERR',0,0,4
	.byte	'UART_PARITY_ERR',0,1,4
	.byte	'UART_FRAME_ERR',0,2,4
	.byte	'UART_TXOVERFLOW_ERR',0,3,4
	.byte	'UART_RXOVERFLOW_ERR',0,4,4
	.byte	'UART_RXUNDERFLOW_ERR',0,5,0,5
	.byte	'ErrorId',0,1,62,49
	.word	204
	.byte	0,2
	.byte	'CDD_UartRecive',0,1,65,15,1,1,1,1,5
	.byte	'ErrorId',0,1,65,47
	.word	204
	.byte	0,6
	.byte	'__INDIRECT__',0,1,1,1,1,1,1,7
	.byte	'void',0,8
	.word	421
	.byte	9
	.byte	'__prof_adm',0,1,1,1
	.word	427
	.byte	10,1,8
	.word	451
	.byte	9
	.byte	'__codeptr',0,1,1,1
	.word	453
	.byte	11
	.byte	'unsigned char',0,1,8,9
	.byte	'uint8',0,3,90,29
	.word	476
	.byte	11
	.byte	'unsigned short int',0,2,7,9
	.byte	'uint16',0,3,92,29
	.word	507
	.byte	8
	.word	476
	.byte	9
	.byte	'Uart_MemPtrType',0,2,231,3,16
	.word	544
	.byte	9
	.byte	'Uart_SizeType',0,2,240,3,16
	.word	507
	.byte	9
	.byte	'Uart_ErrorIdType',0,2,133,4,2
	.word	204
	.byte	12,1,1,13
	.word	204
	.byte	0,8
	.word	623
	.byte	9
	.byte	'Uart_NotificationPtrType',0,2,135,4,15
	.word	632
	.byte	14
	.byte	'UartNotifType',0,2,142,4,16,16,15
	.byte	'UartTransmitNotifPtr',0,4
	.word	637
	.byte	2,35,0,15
	.byte	'UartReceiveNotifPtr',0,4
	.word	637
	.byte	2,35,4,15
	.byte	'UartAbortTransmitNotifPtr',0,4
	.word	637
	.byte	2,35,8,15
	.byte	'UartAbortReceiveNotifPtr',0,4
	.word	637
	.byte	2,35,12,0,9
	.byte	'UartNotifType',0,2,148,4,2
	.word	671
	.byte	8
	.word	623
	.byte	14
	.byte	'Uart_ChannelType',0,2,155,4,16,32,15
	.byte	'UartNotif',0,16
	.word	671
	.byte	2,35,0,15
	.byte	'HwBrgNumerator',0,2
	.word	507
	.byte	2,35,16,15
	.byte	'HwBrgDenominator',0,2
	.word	507
	.byte	2,35,18,15
	.byte	'HwBitconPrescalar',0,2
	.word	507
	.byte	2,35,20,15
	.byte	'HwBitconOversampling',0,1
	.word	476
	.byte	2,35,22,15
	.byte	'HwModule',0,1
	.word	476
	.byte	2,35,23,15
	.byte	'StopBits',0,1
	.word	476
	.byte	2,35,24,15
	.byte	'DataLength',0,1
	.word	476
	.byte	2,35,25,15
	.byte	'RxPinSelection',0,1
	.word	476
	.byte	2,35,26,15
	.byte	'ParityEnable',0,1
	.word	476
	.byte	2,35,27,15
	.byte	'Parity',0,1
	.word	476
	.byte	2,35,28,15
	.byte	'CtsEnable',0,1
	.word	476
	.byte	2,35,29,15
	.byte	'CtsPolarity',0,1
	.word	476
	.byte	2,35,30,0,9
	.byte	'Uart_ChannelType',0,2,183,4,2
	.word	848
	.byte	14
	.byte	'Uart_ConfigType',0,2,189,4,16,8,16
	.word	848
	.byte	8
	.word	1204
	.byte	15
	.byte	'ChannelConfigPtr',0,4
	.word	1209
	.byte	2,35,0,15
	.byte	'NoOfChannels',0,1
	.word	476
	.byte	2,35,4,0,9
	.byte	'Uart_ConfigType',0,2,195,4,2
	.word	1182
	.byte	3
	.byte	'Uart_StateType',0,2,228,4,14,1,4
	.byte	'UART_UNINITIALISED',0,0,4
	.byte	'UART_INITIALISED',0,1,4
	.byte	'UART_OPERATION_IN_PROGRESS',0,2,0,9
	.byte	'Uart_StateType',0,2,233,4,2
	.word	1288
	.byte	17,8
	.word	1182
	.byte	18,0,0
.L10:
	.byte	16
	.word	1403
	.byte	17,32
	.word	848
	.byte	18,0,0
.L11:
	.byte	16
	.word	1417
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,4,1,3,8,58,15,59,15,57,15,11,15,0,0,4,40,0,3,8,28,13,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,46
	.byte	0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,7,59,0,3,8,0,0,8,15,0,73,19,0,0,9,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,10,21,0,54,15,0,0,11,36,0,3,8,11,15,62,15,0,0,12,21,1,54,15,39,12,0,0,13,5,0,73,19,0,0,14
	.byte	19,1,3,8,58,15,59,15,57,15,11,15,0,0,15,13,0,3,8,11,15,73,19,56,9,0,0,16,38,0,73,19,0,0,17,1,1,11,15,73
	.byte	19,0,0,18,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L13-.L12
.L12:
	.half	3
	.word	.L15-.L14
.L14:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'..\\mcal_cfg\\Uart_PBCfg.c',0,0,0,0
	.byte	'Uart.h',0,1,0,0
	.byte	'Platform_Types.h',0,1,0,0,0
.L15:
.L13:
	.sdecl	'.debug_info',debug,cluster('Uart_ConfigRoot')
	.sect	'.debug_info'
.L6:
	.word	208
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\Uart_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Uart_ConfigRoot',0,1,101,24
	.word	.L10
	.byte	1,5,3
	.word	Uart_ConfigRoot
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_ConfigRoot')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Uart_kChannelConfig0')
	.sect	'.debug_info'
.L8:
	.word	212
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_cfg\\Uart_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Uart_kChannelConfig0',0,1,70,32
	.word	.L11
	.byte	5,3
	.word	Uart_kChannelConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Uart_kChannelConfig0')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0

; ..\mcal_cfg\Uart_PBCfg.c	     1  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	     2  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2014)                                 **
; ..\mcal_cfg\Uart_PBCfg.c	     4  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	     5  ** All rights reserved.                                                       **
; ..\mcal_cfg\Uart_PBCfg.c	     6  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_cfg\Uart_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_cfg\Uart_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_cfg\Uart_PBCfg.c	    10  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    11  ********************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	    12  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    13  **  $FILENAME   : Uart_PBCfg.c $                                             **
; ..\mcal_cfg\Uart_PBCfg.c	    14  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    15  **  $CC VERSION : \main\24 $                                                 **
; ..\mcal_cfg\Uart_PBCfg.c	    16  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    17  **  DATE, TIME: 2020-07-10, 14:56:11                                          **
; ..\mcal_cfg\Uart_PBCfg.c	    18  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    19  **  GENERATOR : Build b141014-0350                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    20  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    21  **  AUTHOR    : DL-AUTOSAR-Engineering                                        **
; ..\mcal_cfg\Uart_PBCfg.c	    22  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    23  **  VENDOR    : Infineon Technologies                                         **
; ..\mcal_cfg\Uart_PBCfg.c	    24  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    25  **  DESCRIPTION  : Uart configuration generated out of ECU configuration      **
; ..\mcal_cfg\Uart_PBCfg.c	    26  **                 file(Uart.bmd/.xdm)                                        **
; ..\mcal_cfg\Uart_PBCfg.c	    27  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    28  **  MAY BE CHANGED BY USER [yes/no]: No                                       **
; ..\mcal_cfg\Uart_PBCfg.c	    29  **                                                                            **
; ..\mcal_cfg\Uart_PBCfg.c	    30  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	    31  
; ..\mcal_cfg\Uart_PBCfg.c	    32  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	    33  **                      Includes                                              **
; ..\mcal_cfg\Uart_PBCfg.c	    34  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	    35  /* Include UART Module File */
; ..\mcal_cfg\Uart_PBCfg.c	    36  #include "Uart.h"
; ..\mcal_cfg\Uart_PBCfg.c	    37  
; ..\mcal_cfg\Uart_PBCfg.c	    38  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	    39  **                      Private Macro Definitions                             **
; ..\mcal_cfg\Uart_PBCfg.c	    40  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	    41  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	    42  **                      Imported Compiler Switch Check                        **
; ..\mcal_cfg\Uart_PBCfg.c	    43  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	    44  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	    45  **                      Private Type Definitions                              **
; ..\mcal_cfg\Uart_PBCfg.c	    46  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	    47  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	    48  **                      Private Function Declarations                         **
; ..\mcal_cfg\Uart_PBCfg.c	    49  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	    50  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	    51  **                      Global Constant Definitions                           **
; ..\mcal_cfg\Uart_PBCfg.c	    52  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	    53  /*
; ..\mcal_cfg\Uart_PBCfg.c	    54                 Container: UartConfigSet
; ..\mcal_cfg\Uart_PBCfg.c	    55  */
; ..\mcal_cfg\Uart_PBCfg.c	    56  #define UART_START_SEC_POSTBUILDCFG
; ..\mcal_cfg\Uart_PBCfg.c	    57  /*
; ..\mcal_cfg\Uart_PBCfg.c	    58   * To be used for global or static constants (unspecified size)
; ..\mcal_cfg\Uart_PBCfg.c	    59  */
; ..\mcal_cfg\Uart_PBCfg.c	    60  #include "MemMap.h"
; ..\mcal_cfg\Uart_PBCfg.c	    61   /* Notification Function of UartTransmitNotifPtr */
; ..\mcal_cfg\Uart_PBCfg.c	    62    extern void CDD_UartTransmit(Uart_ErrorIdType ErrorId);
; ..\mcal_cfg\Uart_PBCfg.c	    63  
; ..\mcal_cfg\Uart_PBCfg.c	    64   /* Notification Function of UartRecieveNotifPtr */
; ..\mcal_cfg\Uart_PBCfg.c	    65    extern void CDD_UartRecive(Uart_ErrorIdType ErrorId);
; ..\mcal_cfg\Uart_PBCfg.c	    66  
; ..\mcal_cfg\Uart_PBCfg.c	    67          
; ..\mcal_cfg\Uart_PBCfg.c	    68  /* Uart Channel Initialization Data */
; ..\mcal_cfg\Uart_PBCfg.c	    69  
; ..\mcal_cfg\Uart_PBCfg.c	    70  static const Uart_ChannelType  Uart_kChannelConfig0[1] = 
; ..\mcal_cfg\Uart_PBCfg.c	    71  {
; ..\mcal_cfg\Uart_PBCfg.c	    72    {  /* Notification function pointers for UART Module */
; ..\mcal_cfg\Uart_PBCfg.c	    73      {
; ..\mcal_cfg\Uart_PBCfg.c	    74        /* Call-back Notification Function for Write operation */
; ..\mcal_cfg\Uart_PBCfg.c	    75        &CDD_UartTransmit,
; ..\mcal_cfg\Uart_PBCfg.c	    76            /* Call-back Notification Function for Read operation */
; ..\mcal_cfg\Uart_PBCfg.c	    77        &CDD_UartRecive,
; ..\mcal_cfg\Uart_PBCfg.c	    78            /* Call-back Notification Function for AbortWrite operation */
; ..\mcal_cfg\Uart_PBCfg.c	    79        NULL_PTR,
; ..\mcal_cfg\Uart_PBCfg.c	    80        /* Call-back Notification Function for AbortRead operation */
; ..\mcal_cfg\Uart_PBCfg.c	    81        NULL_PTR,
; ..\mcal_cfg\Uart_PBCfg.c	    82      },
; ..\mcal_cfg\Uart_PBCfg.c	    83    	/* BaudRate : 9600 Hz  */
; ..\mcal_cfg\Uart_PBCfg.c	    84      24U,       /* BRG.NUMERATOR value */
; ..\mcal_cfg\Uart_PBCfg.c	    85      1000U,     /* BRG.DENOMINATOR value */
; ..\mcal_cfg\Uart_PBCfg.c	    86      24U,       /* BITCON.PRESCALAR value */
; ..\mcal_cfg\Uart_PBCfg.c	    87      9U,       /* BITCON.Oversampling value */
; ..\mcal_cfg\Uart_PBCfg.c	    88      UART_ASCLIN1,    /* HW Unit used */
; ..\mcal_cfg\Uart_PBCfg.c	    89      1U,      /* Number of Stop Bits*/
; ..\mcal_cfg\Uart_PBCfg.c	    90      8U,      /* DataLength*/
; ..\mcal_cfg\Uart_PBCfg.c	    91      0U,      /* UartRxPinSelection */
; ..\mcal_cfg\Uart_PBCfg.c	    92      0U,      /*Parity Enable*/ 
; ..\mcal_cfg\Uart_PBCfg.c	    93      0U,      /* Odd or Even Parity */ 
; ..\mcal_cfg\Uart_PBCfg.c	    94      1U,      /*CTS Enable*/
; ..\mcal_cfg\Uart_PBCfg.c	    95      0U,      /*RTS/CTS Polarity*/
; ..\mcal_cfg\Uart_PBCfg.c	    96    },
; ..\mcal_cfg\Uart_PBCfg.c	    97  };
; ..\mcal_cfg\Uart_PBCfg.c	    98  
; ..\mcal_cfg\Uart_PBCfg.c	    99  /* Uart Module Initialization Data */
; ..\mcal_cfg\Uart_PBCfg.c	   100  
; ..\mcal_cfg\Uart_PBCfg.c	   101  const Uart_ConfigType  Uart_ConfigRoot[1] = 
; ..\mcal_cfg\Uart_PBCfg.c	   102   {
; ..\mcal_cfg\Uart_PBCfg.c	   103     {
; ..\mcal_cfg\Uart_PBCfg.c	   104        Uart_kChannelConfig0,
; ..\mcal_cfg\Uart_PBCfg.c	   105        1U,
; ..\mcal_cfg\Uart_PBCfg.c	   106     }  
; ..\mcal_cfg\Uart_PBCfg.c	   107   };
; ..\mcal_cfg\Uart_PBCfg.c	   108  
; ..\mcal_cfg\Uart_PBCfg.c	   109  #define UART_STOP_SEC_POSTBUILDCFG
; ..\mcal_cfg\Uart_PBCfg.c	   110  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_cfg\Uart_PBCfg.c	   111   allowed only for MemMap.h*/
; ..\mcal_cfg\Uart_PBCfg.c	   112  #include "MemMap.h"
; ..\mcal_cfg\Uart_PBCfg.c	   113  
; ..\mcal_cfg\Uart_PBCfg.c	   114  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	   115  **                      Global Variable Definitions                           **
; ..\mcal_cfg\Uart_PBCfg.c	   116  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	   117  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	   118  **                      Private Constant Definitions                          **
; ..\mcal_cfg\Uart_PBCfg.c	   119  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	   120  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	   121  **                      Private Variable Definitions                          **
; ..\mcal_cfg\Uart_PBCfg.c	   122  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	   123  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	   124  **                      Global Function Definitions                           **
; ..\mcal_cfg\Uart_PBCfg.c	   125  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	   126  /*******************************************************************************
; ..\mcal_cfg\Uart_PBCfg.c	   127  **                      Private Function Definitions                          **
; ..\mcal_cfg\Uart_PBCfg.c	   128  *******************************************************************************/
; ..\mcal_cfg\Uart_PBCfg.c	   129  

	; Module end
