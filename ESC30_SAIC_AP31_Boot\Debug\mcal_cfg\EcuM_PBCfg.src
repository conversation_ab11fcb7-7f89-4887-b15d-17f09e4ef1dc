	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc27060a -c99 --dep-file=mcal_cfg\\.EcuM_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\EcuM_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\EcuM_PBCfg.src ..\\mcal_cfg\\EcuM_PBCfg.c"
	.compiler_name		"ctc"
	.name	"EcuM_PBCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_ECUM_PB',data,rom,cluster('EcuM_ConfigAlternative')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_ECUM_PB'
	.global	EcuM_ConfigAlternative
	.align	4
EcuM_ConfigAlternative:	.type	object
	.size	EcuM_ConfigAlternative,40
	.space	2
	.word	-1
	.space	2
	.word	Mcu_ConfigRoot,Dio_ConfigRoot,Port_ConfigRoot,Adc_ConfigRoot
	.word	Uart_ConfigRoot,Spi_ConfigRoot,Dma_ConfigRoot
	.byte	255
	.space	3
	.extern	Mcu_ConfigRoot
	.extern	Port_ConfigRoot
	.extern	Dio_ConfigRoot
	.extern	Dma_ConfigRoot
	.extern	Spi_ConfigRoot
	.extern	Adc_ConfigRoot
	.extern	Uart_ConfigRoot
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	111461
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\EcuM_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	179
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	185
	.byte	5,1,3
	.word	209
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	211
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	234
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	265
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	302
	.byte	4
	.byte	'boolean',0,2,105,29
	.word	234
	.byte	7
	.byte	'EcuM_ConfigType_Tag',0,3,140,1,16,40,8
	.word	265
	.byte	9
	.byte	'ConfigurationIdentifier',0,2
	.word	380
	.byte	2,35,0,8
	.word	302
	.byte	9
	.byte	'PreCompileIdentifier',0,4
	.word	418
	.byte	2,35,2,7
	.byte	'Mcu_ConfigType',0,4,189,4,16,28,7
	.byte	'Mcu_ClockCfgType',0,4,236,3,16,80,10,8
	.word	234
	.byte	11,7,0,9
	.byte	'K2div',0,8
	.word	497
	.byte	2,35,0,10,32
	.word	302
	.byte	11,7,0,9
	.byte	'K2RampToPllDelayTicks',0,32
	.word	521
	.byte	2,35,8,12,4,247,3,3,4,13
	.byte	'K1div',0,1
	.word	234
	.byte	7,1,2,35,0,13
	.byte	'K3div',0,2
	.word	265
	.byte	7,2,2,35,0,6
	.byte	'unsigned int',0,4,7,13
	.byte	'Ndiv',0,4
	.word	601
	.byte	7,11,2,35,2,13
	.byte	'Pdiv',0,2
	.word	265
	.byte	4,7,2,35,2,13
	.byte	'K2steps',0,1
	.word	234
	.byte	4,3,2,35,3,13
	.byte	'PllMode',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'Reserved',0,1
	.word	234
	.byte	2,0,2,35,3,0,9
	.byte	'Mcu_ClockDivValues',0,4
	.word	561
	.byte	2,35,40,12,4,132,4,3,4,13
	.byte	'McuErayNDivider',0,1
	.word	234
	.byte	5,3,2,35,0,13
	.byte	'McuErayK2Divider',0,2
	.word	265
	.byte	7,4,2,35,0,13
	.byte	'McuErayK3Divider',0,4
	.word	601
	.byte	7,13,2,35,2,13
	.byte	'McuErayPDivider',0,1
	.word	234
	.byte	4,1,2,35,2,13
	.byte	'Reserved',0,2
	.word	265
	.byte	9,0,2,35,2,0,9
	.byte	'MCU_ErayPllDivValues',0,4
	.word	736
	.byte	2,35,44,9
	.byte	'Ccucon0',0,4
	.word	302
	.byte	2,35,48,9
	.byte	'Ccucon1',0,4
	.word	302
	.byte	2,35,52,9
	.byte	'Ccucon2',0,4
	.word	302
	.byte	2,35,56,9
	.byte	'Ccucon5',0,4
	.word	302
	.byte	2,35,60,9
	.byte	'Ccucon6',0,4
	.word	302
	.byte	2,35,64,9
	.byte	'Ccucon7',0,4
	.word	302
	.byte	2,35,68,9
	.byte	'Ccucon8',0,4
	.word	302
	.byte	2,35,72,9
	.byte	'K2RampToPllDelayConf',0,1
	.word	234
	.byte	2,35,76,0,8
	.word	474
	.byte	3
	.word	1053
	.byte	9
	.byte	'ClockCfgPtr',0,4
	.word	1058
	.byte	2,35,0,7
	.byte	'Gtm_ConfigType',0,5,192,7,16,12,12,5,230,6,9,64,9
	.byte	'GtmClockEnable',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'GtmCmuClkCnt',0,32
	.word	521
	.byte	2,35,4,9
	.byte	'GtmFxdClkControl',0,4
	.word	302
	.byte	2,35,36,12,5,223,6,9,8,9
	.byte	'CmuEclkNum',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'CmuEclkDen',0,4
	.word	302
	.byte	2,35,4,0,10,24
	.word	1183
	.byte	11,2,0,9
	.byte	'GtmEclk',0,24
	.word	1230
	.byte	2,35,40,0,8
	.word	1105
	.byte	3
	.word	1257
	.byte	9
	.byte	'GtmClockSettingPtr',0,4
	.word	1262
	.byte	2,35,0,12,5,249,5,9,36,10,4
	.word	302
	.byte	11,0,0,9
	.byte	'TimInSel',0,4
	.word	1301
	.byte	2,35,0,9
	.byte	'ToutSel',0,32
	.word	521
	.byte	2,35,4,0,8
	.word	1295
	.byte	3
	.word	1346
	.byte	9
	.byte	'GtmPortConfigPtr',0,4
	.word	1351
	.byte	2,35,4,12,5,129,7,9,72,9
	.byte	'GtmModuleSleepEnable',0,1
	.word	234
	.byte	2,35,0,9
	.byte	'GtmGclkNum',0,4
	.word	302
	.byte	2,35,2,9
	.byte	'GtmGclkDen',0,4
	.word	302
	.byte	2,35,6,9
	.byte	'GtmAccessEnable0',0,4
	.word	302
	.byte	2,35,10,9
	.byte	'GtmAccessEnable1',0,4
	.word	302
	.byte	2,35,14,10,2
	.word	265
	.byte	11,0,0,9
	.byte	'GtmTimModuleUsage',0,2
	.word	1510
	.byte	2,35,18,10,1
	.word	234
	.byte	11,0,0,9
	.byte	'GtmTimUsage',0,1
	.word	1546
	.byte	2,35,20,12,5,138,6,11,24,9
	.byte	'TimUsage',0,1
	.word	234
	.byte	2,35,0,9
	.byte	'TimIrqEn',0,1
	.word	234
	.byte	2,35,1,9
	.byte	'TimErrIrqEn',0,1
	.word	234
	.byte	2,35,2,9
	.byte	'TimExtCapSrc',0,1
	.word	234
	.byte	2,35,3,9
	.byte	'TimCtrlValue',0,4
	.word	302
	.byte	2,35,4,12,5,129,6,9,8,9
	.byte	'TimRisingEdgeFilter',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'TimFallingEdgeFilter',0,4
	.word	302
	.byte	2,35,4,0,8
	.word	1683
	.byte	3
	.word	1749
	.byte	9
	.byte	'GtmTimFltPtr',0,4
	.word	1754
	.byte	2,35,8,9
	.byte	'TimCntsValue',0,4
	.word	302
	.byte	2,35,12,9
	.byte	'TimTduValue',0,4
	.word	302
	.byte	2,35,16,9
	.byte	'TimInSrcSel',0,4
	.word	302
	.byte	2,35,20,0,8
	.word	1576
	.byte	3
	.word	1846
	.byte	9
	.byte	'GtmTimConfigPtr',0,4
	.word	1851
	.byte	2,35,24,9
	.byte	'GtmTomTgcUsage',0,1
	.word	1546
	.byte	2,35,28,12,5,189,6,9,12,9
	.byte	'GtmTomIntTrig',0,2
	.word	265
	.byte	2,35,0,9
	.byte	'GtmTomActTb',0,4
	.word	302
	.byte	2,35,2,12,5,177,6,9,16,9
	.byte	'GtmTomUpdateEn',0,2
	.word	265
	.byte	2,35,0,9
	.byte	'GtmTomEndisCtrl',0,2
	.word	265
	.byte	2,35,2,9
	.byte	'GtmTomEndisStat',0,2
	.word	265
	.byte	2,35,4,9
	.byte	'GtmTomOutenCtrl',0,2
	.word	265
	.byte	2,35,6,9
	.byte	'GtmTomOutenStat',0,2
	.word	265
	.byte	2,35,8,9
	.byte	'GtmTomFupd',0,4
	.word	302
	.byte	2,35,10,0,8
	.word	1955
	.byte	3
	.word	2106
	.byte	9
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	2111
	.byte	2,35,8,0,8
	.word	1905
	.byte	3
	.word	2148
	.byte	9
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	2153
	.byte	2,35,32,10,8
	.word	302
	.byte	11,1,0,9
	.byte	'GtmTomModuleUsage',0,8
	.word	2186
	.byte	2,35,36,9
	.byte	'GtmTomUsage',0,4
	.word	1301
	.byte	2,35,44,12,5,211,6,9,12,9
	.byte	'TomUsage',0,1
	.word	234
	.byte	2,35,0,9
	.byte	'GtmTomIrqMode',0,1
	.word	234
	.byte	2,35,1,9
	.byte	'GtmTomControlWord',0,4
	.word	302
	.byte	2,35,2,12,5,199,6,9,12,9
	.byte	'GtmTomIrqEn',0,1
	.word	234
	.byte	2,35,0,9
	.byte	'GtmTomCn0Value',0,2
	.word	265
	.byte	2,35,2,9
	.byte	'GtmTomCm0Value',0,2
	.word	265
	.byte	2,35,4,9
	.byte	'GtmTomCm1Value',0,2
	.word	265
	.byte	2,35,6,9
	.byte	'GtmTomSr0Value',0,2
	.word	265
	.byte	2,35,8,9
	.byte	'GtmTomSr1Value',0,2
	.word	265
	.byte	2,35,10,0,8
	.word	2317
	.byte	3
	.word	2465
	.byte	9
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	2470
	.byte	2,35,8,0,8
	.word	2243
	.byte	3
	.word	2505
	.byte	9
	.byte	'GtmTomConfigPtr',0,4
	.word	2510
	.byte	2,35,48,12,5,154,6,11,40,9
	.byte	'Gtm_TimUsage',0,8
	.word	497
	.byte	2,35,0,10,16
	.word	234
	.byte	11,15,0,10,32
	.word	2568
	.byte	11,1,0,9
	.byte	'Gtm_TomUsage',0,32
	.word	2577
	.byte	2,35,8,0,8
	.word	2540
	.byte	3
	.word	2609
	.byte	9
	.byte	'GtmModUsageConfigPtr',0,4
	.word	2614
	.byte	2,35,52,12,5,240,6,9,4,9
	.byte	'GtmCtrlValue',0,2
	.word	265
	.byte	2,35,0,9
	.byte	'GtmIrqEnable',0,2
	.word	265
	.byte	2,35,2,0,8
	.word	2649
	.byte	3
	.word	2700
	.byte	9
	.byte	'GtmGeneralConfigPtr',0,4
	.word	2705
	.byte	2,35,56,12,5,249,6,9,6,9
	.byte	'TbuChannelCtrl',0,1
	.word	234
	.byte	2,35,0,9
	.byte	'TbuBaseValue',0,4
	.word	302
	.byte	2,35,2,0,8
	.word	2739
	.byte	3
	.word	2792
	.byte	9
	.byte	'GtmTbuConfigPtr',0,4
	.word	2797
	.byte	2,35,60,8
	.word	234
	.byte	3
	.word	2827
	.byte	9
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	2832
	.byte	2,35,64,9
	.byte	'GtmTtcanTriggers',0,2
	.word	1510
	.byte	2,35,68,0,8
	.word	1382
	.byte	3
	.word	2894
	.byte	9
	.byte	'GtmModuleConfigPtr',0,4
	.word	2899
	.byte	2,35,8,0,8
	.word	1084
	.byte	3
	.word	2933
	.byte	9
	.byte	'GtmConfigRootPtr',0,4
	.word	2938
	.byte	2,35,4,9
	.byte	'ResetCfg',0,4
	.word	302
	.byte	2,35,8,9
	.byte	'NoOfClockCfg',0,4
	.word	302
	.byte	2,35,12,9
	.byte	'NoOfRamCfg',0,4
	.word	302
	.byte	2,35,16,9
	.byte	'MaxMode',0,4
	.word	302
	.byte	2,35,20,7
	.byte	'Mcu_StandbyModeType',0,4,171,4,16,6,9
	.byte	'PMSWCR0',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'CrcCheckEnable',0,1
	.word	234
	.byte	2,35,4,0,8
	.word	3046
	.byte	3
	.word	3114
	.byte	9
	.byte	'StandbyCfgPtr',0,4
	.word	3119
	.byte	2,35,24,0,8
	.word	453
	.byte	3
	.word	3148
	.byte	9
	.byte	'Mcu_ConfigData',0,4
	.word	3153
	.byte	2,35,8,7
	.byte	'Dio_ConfigType',0,6,147,2,16,12,7
	.byte	'Dio_PortChannelIdType',0,6,139,2,16,8,9
	.byte	'Dio_PortIdConfig',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'Dio_ChannelConfig',0,4
	.word	302
	.byte	2,35,4,0,8
	.word	3203
	.byte	3
	.word	3285
	.byte	9
	.byte	'Dio_PortChannelConfigPtr',0,4
	.word	3290
	.byte	2,35,0,7
	.byte	'Dio_ChannelGroupType',0,6,129,2,16,4,9
	.byte	'mask',0,2
	.word	265
	.byte	2,35,0,9
	.byte	'offset',0,1
	.word	234
	.byte	2,35,2,9
	.byte	'port',0,1
	.word	234
	.byte	2,35,3,0,8
	.word	3329
	.byte	3
	.word	3401
	.byte	9
	.byte	'Dio_ChannelGroupConfigPtr',0,4
	.word	3406
	.byte	2,35,4,9
	.byte	'Dio_ChannelGroupConfigSize',0,4
	.word	302
	.byte	2,35,8,0,8
	.word	3182
	.byte	3
	.word	3483
	.byte	9
	.byte	'Dio_ConfigData',0,4
	.word	3488
	.byte	2,35,12,7
	.byte	'Port_ConfigType',0,7,176,3,16,12,7
	.byte	'Port_n_ConfigType',0,7,140,3,16,28,14
	.byte	'Port_n_ControlType',0,7,206,2,15,16,12,7,208,2,3,16,9
	.byte	'PC0',0,1
	.word	234
	.byte	2,35,0,9
	.byte	'PC1',0,1
	.word	234
	.byte	2,35,1,9
	.byte	'PC2',0,1
	.word	234
	.byte	2,35,2,9
	.byte	'PC3',0,1
	.word	234
	.byte	2,35,3,9
	.byte	'PC4',0,1
	.word	234
	.byte	2,35,4,9
	.byte	'PC5',0,1
	.word	234
	.byte	2,35,5,9
	.byte	'PC6',0,1
	.word	234
	.byte	2,35,6,9
	.byte	'PC7',0,1
	.word	234
	.byte	2,35,7,9
	.byte	'PC8',0,1
	.word	234
	.byte	2,35,8,9
	.byte	'PC9',0,1
	.word	234
	.byte	2,35,9,9
	.byte	'PC10',0,1
	.word	234
	.byte	2,35,10,9
	.byte	'PC11',0,1
	.word	234
	.byte	2,35,11,9
	.byte	'PC12',0,1
	.word	234
	.byte	2,35,12,9
	.byte	'PC13',0,1
	.word	234
	.byte	2,35,13,9
	.byte	'PC14',0,1
	.word	234
	.byte	2,35,14,9
	.byte	'PC15',0,1
	.word	234
	.byte	2,35,15,0,9
	.byte	'B',0,16
	.word	3588
	.byte	2,35,0,10,16
	.word	302
	.byte	11,3,0,9
	.byte	'U',0,16
	.word	3820
	.byte	2,35,0,0,9
	.byte	'PinControl',0,16
	.word	3563
	.byte	2,35,0,14
	.byte	'Port_n_PinType',0,7,176,2,15,4,12,7,178,2,3,2,13
	.byte	'P0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'P2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'P3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'P4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'P5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'P6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'P7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'P8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'P9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'P10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'P11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'P12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'P13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'P14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'P15',0,1
	.word	234
	.byte	1,0,2,35,1,0,9
	.byte	'B',0,2
	.word	3882
	.byte	2,35,0,9
	.byte	'U',0,4
	.word	302
	.byte	2,35,0,0,9
	.byte	'PinLevel',0,4
	.word	3861
	.byte	2,35,16,9
	.byte	'DriverStrength0',0,4
	.word	302
	.byte	2,35,20,9
	.byte	'DriverStrength1',0,4
	.word	302
	.byte	2,35,24,0,8
	.word	3539
	.byte	3
	.word	4211
	.byte	9
	.byte	'PortConfigSetPtr',0,4
	.word	4216
	.byte	2,35,0,8
	.word	265
	.byte	3
	.word	4247
	.byte	9
	.byte	'PDiscSet',0,4
	.word	4252
	.byte	2,35,4,8
	.word	302
	.byte	3
	.word	4275
	.byte	9
	.byte	'Port_PCSRConfigTypePtr',0,4
	.word	4280
	.byte	2,35,8,0,8
	.word	3517
	.byte	3
	.word	4318
	.byte	9
	.byte	'Port_ConfigData',0,4
	.word	4323
	.byte	2,35,16,7
	.byte	'Adc_ConfigType',0,8,247,7,17,16,7
	.byte	'Adc_KernelConfigType',0,8,163,7,17,16,7
	.byte	'Adc_HwUnitCfgType',0,8,138,6,16,20,9
	.byte	'ArbitrationLength',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'TriggSrcArbLevel',0,4
	.word	302
	.byte	2,35,4,9
	.byte	'KernelInputClass',0,8
	.word	2186
	.byte	2,35,8,9
	.byte	'SyncConvMode',0,1
	.word	234
	.byte	2,35,16,9
	.byte	'SlaveReady',0,1
	.word	234
	.byte	2,35,17,9
	.byte	'DmaChannel',0,1
	.word	234
	.byte	2,35,18,0,8
	.word	4401
	.byte	3
	.word	4567
	.byte	9
	.byte	'HwCfgPtr',0,4
	.word	4572
	.byte	2,35,0,7
	.byte	'Adc_ChannelCfgType',0,8,136,7,17,28,9
	.byte	'AdcChConfigValue',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'AdcChResAccumulation',0,4
	.word	302
	.byte	2,35,4,9
	.byte	'AdcIsChLimitChkEnabled',0,4
	.word	302
	.byte	2,35,8,9
	.byte	'AdcLimitChkRange',0,4
	.word	302
	.byte	2,35,12,9
	.byte	'AdcLimitChkBnd',0,4
	.word	302
	.byte	2,35,16,9
	.byte	'AdcChBndSelxValue',0,4
	.word	302
	.byte	2,35,20,9
	.byte	'AdcSyncChannel',0,1
	.word	234
	.byte	2,35,24,0,8
	.word	4595
	.byte	3
	.word	4810
	.byte	9
	.byte	'ChCfgPtr',0,4
	.word	4815
	.byte	2,35,4,7
	.byte	'Adc_GroupCfgType',0,8,182,6,16,12,8
	.word	234
	.byte	3
	.word	4861
	.byte	9
	.byte	'GroupDefinition',0,4
	.word	4866
	.byte	2,35,0,9
	.byte	'IntChMask',0,2
	.word	265
	.byte	2,35,4,9
	.byte	'TriggSrc',0,1
	.word	234
	.byte	2,35,6,9
	.byte	'GrpRequestSrc',0,1
	.word	234
	.byte	2,35,7,9
	.byte	'ConvMode',0,1
	.word	234
	.byte	2,35,8,9
	.byte	'NumSamples',0,1
	.word	234
	.byte	2,35,9,9
	.byte	'StreamBufferMode',0,1
	.word	234
	.byte	2,35,10,0,8
	.word	4838
	.byte	3
	.word	5021
	.byte	9
	.byte	'GrpCfgPtr',0,4
	.word	5026
	.byte	2,35,8,9
	.byte	'TotCfgGrps',0,1
	.word	234
	.byte	2,35,12,0,8
	.word	4374
	.byte	3
	.word	5071
	.byte	10,8
	.word	5076
	.byte	11,1,0,9
	.byte	'CfgPtr',0,8
	.word	5081
	.byte	2,35,0,7
	.byte	'Adc_GlobalCfgType',0,8,186,7,17,28,9
	.byte	'ClkPrescale',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'GlobInputClass',0,8
	.word	2186
	.byte	2,35,4,9
	.byte	'PostCalEnable',0,4
	.word	302
	.byte	2,35,12,9
	.byte	'LowPowerSupply',0,4
	.word	302
	.byte	2,35,16,9
	.byte	'RefPrechargeCtrl',0,4
	.word	302
	.byte	2,35,20,9
	.byte	'OperationMode',0,1
	.word	234
	.byte	2,35,24,0,8
	.word	5106
	.byte	3
	.word	5272
	.byte	9
	.byte	'GlobCfgPtr',0,4
	.word	5277
	.byte	2,35,8,9
	.byte	'SleepMode',0,1
	.word	234
	.byte	2,35,12,0,8
	.word	4353
	.byte	3
	.word	5322
	.byte	9
	.byte	'Adc_ConfigData',0,4
	.word	5327
	.byte	2,35,20,7
	.byte	'Uart_ConfigType',0,9,189,4,16,8,7
	.byte	'Uart_ChannelType',0,9,155,4,16,32,7
	.byte	'UartNotifType',0,9,142,4,16,16,15,1,1,16
	.byte	'Uart_ErrorIdType',0,9,253,3,14,1,17
	.byte	'UART_NO_ERR',0,0,17
	.byte	'UART_PARITY_ERR',0,1,17
	.byte	'UART_FRAME_ERR',0,2,17
	.byte	'UART_TXOVERFLOW_ERR',0,3,17
	.byte	'UART_RXOVERFLOW_ERR',0,4,17
	.byte	'UART_RXUNDERFLOW_ERR',0,5,0,18
	.word	5424
	.byte	0,3
	.word	5421
	.byte	4
	.byte	'Uart_NotificationPtrType',0,9,135,4,15
	.word	5570
	.byte	9
	.byte	'UartTransmitNotifPtr',0,4
	.word	5575
	.byte	2,35,0,9
	.byte	'UartReceiveNotifPtr',0,4
	.word	5575
	.byte	2,35,4,9
	.byte	'UartAbortTransmitNotifPtr',0,4
	.word	5575
	.byte	2,35,8,9
	.byte	'UartAbortReceiveNotifPtr',0,4
	.word	5575
	.byte	2,35,12,0,9
	.byte	'UartNotif',0,16
	.word	5401
	.byte	2,35,0,9
	.byte	'HwBrgNumerator',0,2
	.word	265
	.byte	2,35,16,9
	.byte	'HwBrgDenominator',0,2
	.word	265
	.byte	2,35,18,9
	.byte	'HwBitconPrescalar',0,2
	.word	265
	.byte	2,35,20,9
	.byte	'HwBitconOversampling',0,1
	.word	234
	.byte	2,35,22,9
	.byte	'HwModule',0,1
	.word	234
	.byte	2,35,23,9
	.byte	'StopBits',0,1
	.word	234
	.byte	2,35,24,9
	.byte	'DataLength',0,1
	.word	234
	.byte	2,35,25,9
	.byte	'RxPinSelection',0,1
	.word	234
	.byte	2,35,26,9
	.byte	'ParityEnable',0,1
	.word	234
	.byte	2,35,27,9
	.byte	'Parity',0,1
	.word	234
	.byte	2,35,28,9
	.byte	'CtsEnable',0,1
	.word	234
	.byte	2,35,29,9
	.byte	'CtsPolarity',0,1
	.word	234
	.byte	2,35,30,0,8
	.word	5378
	.byte	3
	.word	6023
	.byte	9
	.byte	'ChannelConfigPtr',0,4
	.word	6028
	.byte	2,35,0,9
	.byte	'NoOfChannels',0,1
	.word	234
	.byte	2,35,4,0,8
	.word	5356
	.byte	3
	.word	6082
	.byte	9
	.byte	'Uart_ConfigData',0,4
	.word	6087
	.byte	2,35,24,7
	.byte	'Spi_ConfigType',0,10,237,9,16,40,7
	.byte	'Spi_ChannelConfig',0,10,247,7,16,12,9
	.byte	'DefaultData',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'DataConfig',0,2
	.word	265
	.byte	2,35,4,9
	.byte	'NoOfBuffers',0,2
	.word	265
	.byte	2,35,6,9
	.byte	'ChannelBufferType',0,1
	.word	234
	.byte	2,35,8,0,8
	.word	6138
	.byte	3
	.word	6252
	.byte	9
	.byte	'SpiChannelConfigPtr',0,4
	.word	6257
	.byte	2,35,0,7
	.byte	'Spi_JobConfig',0,10,183,8,16,24,19,1,1,3
	.word	6311
	.byte	4
	.byte	'Spi_NotifFunctionPtrType',0,10,238,7,15
	.word	6314
	.byte	9
	.byte	'JobEndNotification',0,4
	.word	6319
	.byte	2,35,0,8
	.word	234
	.byte	3
	.word	6381
	.byte	9
	.byte	'ChannelLinkPtr',0,4
	.word	6386
	.byte	2,35,4,9
	.byte	'BaudRateConfig',0,4
	.word	302
	.byte	2,35,8,9
	.byte	'TimeDelayConfig',0,4
	.word	302
	.byte	2,35,12,9
	.byte	'CSPin',0,2
	.word	265
	.byte	2,35,16,9
	.byte	'CSPolarity',0,1
	.word	234
	.byte	2,35,18,9
	.byte	'ShiftClkConfig',0,1
	.word	234
	.byte	2,35,19,9
	.byte	'JobPriority',0,1
	.word	234
	.byte	2,35,20,9
	.byte	'HwUnit',0,1
	.word	234
	.byte	2,35,21,9
	.byte	'ChannelBasedChipSelect',0,1
	.word	234
	.byte	2,35,22,9
	.byte	'ParitySelection',0,1
	.word	234
	.byte	2,35,23,0,8
	.word	6291
	.byte	3
	.word	6618
	.byte	9
	.byte	'SpiJobConfigPtr',0,4
	.word	6623
	.byte	2,35,4,7
	.byte	'Spi_SequenceConfig',0,10,252,8,16,16,9
	.byte	'SeqEndNotification',0,4
	.word	6319
	.byte	2,35,0,9
	.byte	'JobLinkPtr',0,4
	.word	4252
	.byte	2,35,4,9
	.byte	'SeqSharingJobs',0,4
	.word	2832
	.byte	2,35,8,9
	.byte	'JobsInParamSeq',0,2
	.word	265
	.byte	2,35,12,9
	.byte	'InterruptibleSequence',0,1
	.word	234
	.byte	2,35,14,0,8
	.word	6653
	.byte	3
	.word	6806
	.byte	9
	.byte	'SpiSequenceConfigPtr',0,4
	.word	6811
	.byte	2,35,8,7
	.byte	'Spi_HWModuleConfig',0,10,174,9,16,16,8
	.word	302
	.byte	9
	.byte	'HWClkSetting',0,4
	.word	6871
	.byte	2,35,0,8
	.word	302
	.byte	9
	.byte	'HWCSPolaritySetting',0,4
	.word	6898
	.byte	2,35,4,8
	.word	302
	.byte	9
	.byte	'HWPinSetting',0,4
	.word	6932
	.byte	2,35,8,7
	.byte	'Spi_DmaConfigType',0,10,164,9,16,2,20,11,147,1,9,1,17
	.byte	'DMA_CHANNEL0',0,0,17
	.byte	'DMA_CHANNEL1',0,1,17
	.byte	'DMA_CHANNEL2',0,2,17
	.byte	'DMA_CHANNEL3',0,3,17
	.byte	'DMA_CHANNEL4',0,4,17
	.byte	'DMA_CHANNEL5',0,5,17
	.byte	'DMA_CHANNEL6',0,6,17
	.byte	'DMA_CHANNEL7',0,7,17
	.byte	'DMA_CHANNEL8',0,8,17
	.byte	'DMA_CHANNEL9',0,9,17
	.byte	'DMA_CHANNEL10',0,10,17
	.byte	'DMA_CHANNEL11',0,11,17
	.byte	'DMA_CHANNEL12',0,12,17
	.byte	'DMA_CHANNEL13',0,13,17
	.byte	'DMA_CHANNEL14',0,14,17
	.byte	'DMA_CHANNEL15',0,15,17
	.byte	'DMA_CHANNEL16',0,16,17
	.byte	'DMA_CHANNEL17',0,17,17
	.byte	'DMA_CHANNEL18',0,18,17
	.byte	'DMA_CHANNEL19',0,19,17
	.byte	'DMA_CHANNEL20',0,20,17
	.byte	'DMA_CHANNEL21',0,21,17
	.byte	'DMA_CHANNEL22',0,22,17
	.byte	'DMA_CHANNEL23',0,23,17
	.byte	'DMA_CHANNEL24',0,24,17
	.byte	'DMA_CHANNEL25',0,25,17
	.byte	'DMA_CHANNEL26',0,26,17
	.byte	'DMA_CHANNEL27',0,27,17
	.byte	'DMA_CHANNEL28',0,28,17
	.byte	'DMA_CHANNEL29',0,29,17
	.byte	'DMA_CHANNEL30',0,30,17
	.byte	'DMA_CHANNEL31',0,31,17
	.byte	'DMA_CHANNEL32',0,32,17
	.byte	'DMA_CHANNEL33',0,33,17
	.byte	'DMA_CHANNEL34',0,34,17
	.byte	'DMA_CHANNEL35',0,35,17
	.byte	'DMA_CHANNEL36',0,36,17
	.byte	'DMA_CHANNEL37',0,37,17
	.byte	'DMA_CHANNEL38',0,38,17
	.byte	'DMA_CHANNEL39',0,39,17
	.byte	'DMA_CHANNEL40',0,40,17
	.byte	'DMA_CHANNEL41',0,41,17
	.byte	'DMA_CHANNEL42',0,42,17
	.byte	'DMA_CHANNEL43',0,43,17
	.byte	'DMA_CHANNEL44',0,44,17
	.byte	'DMA_CHANNEL45',0,45,17
	.byte	'DMA_CHANNEL46',0,46,17
	.byte	'DMA_CHANNEL47',0,47,17
	.byte	'DMA_CHANNEL48',0,48,17
	.byte	'DMA_CHANNEL49',0,49,17
	.byte	'DMA_CHANNEL50',0,50,17
	.byte	'DMA_CHANNEL51',0,51,17
	.byte	'DMA_CHANNEL52',0,52,17
	.byte	'DMA_CHANNEL53',0,53,17
	.byte	'DMA_CHANNEL54',0,54,17
	.byte	'DMA_CHANNEL55',0,55,17
	.byte	'DMA_CHANNEL56',0,56,17
	.byte	'DMA_CHANNEL57',0,57,17
	.byte	'DMA_CHANNEL58',0,58,17
	.byte	'DMA_CHANNEL59',0,59,17
	.byte	'DMA_CHANNEL60',0,60,17
	.byte	'DMA_CHANNEL61',0,61,17
	.byte	'DMA_CHANNEL62',0,62,17
	.byte	'DMA_CHANNEL63',0,63,17
	.byte	'DMA_CHANNEL64',0,192,0,17
	.byte	'DMA_CHANNEL65',0,193,0,17
	.byte	'DMA_CHANNEL66',0,194,0,17
	.byte	'DMA_CHANNEL67',0,195,0,17
	.byte	'DMA_CHANNEL68',0,196,0,17
	.byte	'DMA_CHANNEL69',0,197,0,17
	.byte	'DMA_CHANNEL70',0,198,0,17
	.byte	'DMA_CHANNEL71',0,199,0,17
	.byte	'DMA_CHANNEL72',0,200,0,17
	.byte	'DMA_CHANNEL73',0,201,0,17
	.byte	'DMA_CHANNEL74',0,202,0,17
	.byte	'DMA_CHANNEL75',0,203,0,17
	.byte	'DMA_CHANNEL76',0,204,0,17
	.byte	'DMA_CHANNEL77',0,205,0,17
	.byte	'DMA_CHANNEL78',0,206,0,17
	.byte	'DMA_CHANNEL79',0,207,0,17
	.byte	'DMA_CHANNEL80',0,208,0,17
	.byte	'DMA_CHANNEL81',0,209,0,17
	.byte	'DMA_CHANNEL82',0,210,0,17
	.byte	'DMA_CHANNEL83',0,211,0,17
	.byte	'DMA_CHANNEL84',0,212,0,17
	.byte	'DMA_CHANNEL85',0,213,0,17
	.byte	'DMA_CHANNEL86',0,214,0,17
	.byte	'DMA_CHANNEL87',0,215,0,17
	.byte	'DMA_CHANNEL88',0,216,0,17
	.byte	'DMA_CHANNEL89',0,217,0,17
	.byte	'DMA_CHANNEL90',0,218,0,17
	.byte	'DMA_CHANNEL91',0,219,0,17
	.byte	'DMA_CHANNEL92',0,220,0,17
	.byte	'DMA_CHANNEL93',0,221,0,17
	.byte	'DMA_CHANNEL94',0,222,0,17
	.byte	'DMA_CHANNEL95',0,223,0,17
	.byte	'DMA_CHANNEL96',0,224,0,17
	.byte	'DMA_CHANNEL97',0,225,0,17
	.byte	'DMA_CHANNEL98',0,226,0,17
	.byte	'DMA_CHANNEL99',0,227,0,17
	.byte	'DMA_CHANNEL100',0,228,0,17
	.byte	'DMA_CHANNEL101',0,229,0,17
	.byte	'DMA_CHANNEL102',0,230,0,17
	.byte	'DMA_CHANNEL103',0,231,0,17
	.byte	'DMA_CHANNEL104',0,232,0,17
	.byte	'DMA_CHANNEL105',0,233,0,17
	.byte	'DMA_CHANNEL106',0,234,0,17
	.byte	'DMA_CHANNEL107',0,235,0,17
	.byte	'DMA_CHANNEL108',0,236,0,17
	.byte	'DMA_CHANNEL109',0,237,0,17
	.byte	'DMA_CHANNEL110',0,238,0,17
	.byte	'DMA_CHANNEL111',0,239,0,17
	.byte	'DMA_CHANNEL112',0,240,0,17
	.byte	'DMA_CHANNEL113',0,241,0,17
	.byte	'DMA_CHANNEL114',0,242,0,17
	.byte	'DMA_CHANNEL115',0,243,0,17
	.byte	'DMA_CHANNEL116',0,244,0,17
	.byte	'DMA_CHANNEL117',0,245,0,17
	.byte	'DMA_CHANNEL118',0,246,0,17
	.byte	'DMA_CHANNEL119',0,247,0,17
	.byte	'DMA_CHANNEL120',0,248,0,17
	.byte	'DMA_CHANNEL121',0,249,0,17
	.byte	'DMA_CHANNEL122',0,250,0,17
	.byte	'DMA_CHANNEL123',0,251,0,17
	.byte	'DMA_CHANNEL124',0,252,0,17
	.byte	'DMA_CHANNEL125',0,253,0,17
	.byte	'DMA_CHANNEL126',0,254,0,17
	.byte	'DMA_CHANNEL127',0,255,0,17
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0,9
	.byte	'TxDmaChannel',0,1
	.word	6983
	.byte	2,35,0,9
	.byte	'RxDmaChannel',0,1
	.word	6983
	.byte	2,35,1,0,8
	.word	6959
	.byte	3
	.word	9188
	.byte	9
	.byte	'SpiDmaConfigPtr',0,4
	.word	9193
	.byte	2,35,12,0,8
	.word	6846
	.byte	3
	.word	9224
	.byte	10,16
	.word	9229
	.byte	11,3,0,9
	.byte	'HWModuleConfigPtr',0,16
	.word	9234
	.byte	2,35,12,7
	.byte	'Spi_BaudrateEconType',0,10,214,9,16,6,9
	.byte	'EconVal',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'QSPIHwUnit',0,1
	.word	234
	.byte	2,35,4,0,8
	.word	9270
	.byte	3
	.word	9335
	.byte	9
	.byte	'SpiBaudrateEconPtr',0,4
	.word	9340
	.byte	2,35,28,9
	.byte	'NoOfJobs',0,2
	.word	265
	.byte	2,35,32,9
	.byte	'NoOfChannels',0,1
	.word	234
	.byte	2,35,34,9
	.byte	'NoOfSequences',0,1
	.word	234
	.byte	2,35,35,9
	.byte	'NoOfEconReg',0,1
	.word	234
	.byte	2,35,36,0,8
	.word	6117
	.byte	3
	.word	9458
	.byte	9
	.byte	'Spi_ConfigData',0,4
	.word	9463
	.byte	2,35,28,7
	.byte	'Dma_ConfigType',0,12,187,4,16,24,7
	.byte	'Dma_ChannelConfigType',0,12,174,4,16,12,9
	.byte	'DmaChannelConfig',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'DmaAddrIntControl',0,4
	.word	302
	.byte	2,35,4,9
	.byte	'DmaHwResourceMode',0,1
	.word	234
	.byte	2,35,8,9
	.byte	'DmaChannelHwPartitionConfig',0,1
	.word	234
	.byte	2,35,9,9
	.byte	'DmaChannelNumber',0,1
	.word	234
	.byte	2,35,10,0,8
	.word	9513
	.byte	3
	.word	9685
	.byte	9
	.byte	'ChannelCfgPtr',0,4
	.word	9690
	.byte	2,35,0,9
	.byte	'DmaPat0',0,4
	.word	302
	.byte	2,35,4,9
	.byte	'DmaPat1',0,4
	.word	302
	.byte	2,35,8,9
	.byte	'DmaMovEng0Err',0,4
	.word	302
	.byte	2,35,12,9
	.byte	'DmaMovEng1Err',0,4
	.word	302
	.byte	2,35,16,9
	.byte	'ChannelsConfigured',0,1
	.word	234
	.byte	2,35,20,0,8
	.word	9492
	.byte	3
	.word	9827
	.byte	9
	.byte	'Dma_ConfigData',0,4
	.word	9832
	.byte	2,35,32,8
	.word	234
	.byte	9
	.byte	'LocalConfigData',0,1
	.word	9861
	.byte	2,35,36,0,10,40
	.word	354
	.byte	11,0,0
.L8:
	.byte	8
	.word	9892
	.byte	3
	.word	5421
	.byte	3
	.word	6311
	.byte	4
	.byte	'unsigned_int',0,13,121,22
	.word	601
	.byte	7
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,14,45,16,4,13
	.byte	'EN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_ACCEN0_Bits',0,14,79,3
	.word	9937
	.byte	7
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,14,82,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_SCU_ACCEN1_Bits',0,14,85,3
	.word	10494
	.byte	7
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,14,88,16,4,13
	.byte	'STM0DIS',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'STM1DIS',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'STM2DIS',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,4
	.word	601
	.byte	29,0,2,35,2,0,4
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,14,94,3
	.word	10571
	.byte	7
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,14,97,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'BAUD2DIV',0,1
	.word	234
	.byte	4,0,2,35,0,13
	.byte	'SRIDIV',0,1
	.word	234
	.byte	4,4,2,35,1,13
	.byte	'LPDIV',0,1
	.word	234
	.byte	4,0,2,35,1,13
	.byte	'SPBDIV',0,1
	.word	234
	.byte	4,4,2,35,2,13
	.byte	'FSI2DIV',0,1
	.word	234
	.byte	2,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	234
	.byte	2,0,2,35,2,13
	.byte	'FSIDIV',0,1
	.word	234
	.byte	2,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	234
	.byte	2,4,2,35,3,13
	.byte	'CLKSEL',0,1
	.word	234
	.byte	2,2,2,35,3,13
	.byte	'UP',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON0_Bits',0,14,111,3
	.word	10707
	.byte	7
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,14,114,16,4,13
	.byte	'CANDIV',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'ERAYDIV',0,1
	.word	234
	.byte	4,0,2,35,0,13
	.byte	'STMDIV',0,1
	.word	234
	.byte	4,4,2,35,1,13
	.byte	'GTMDIV',0,1
	.word	234
	.byte	4,0,2,35,1,13
	.byte	'ETHDIV',0,1
	.word	234
	.byte	4,4,2,35,2,13
	.byte	'ASCLINFDIV',0,1
	.word	234
	.byte	4,0,2,35,2,13
	.byte	'ASCLINSDIV',0,1
	.word	234
	.byte	4,4,2,35,3,13
	.byte	'INSEL',0,1
	.word	234
	.byte	2,2,2,35,3,13
	.byte	'UP',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON1_Bits',0,14,126,3
	.word	10989
	.byte	7
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,14,129,1,16,4,13
	.byte	'BBBDIV',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	601
	.byte	26,2,2,35,2,13
	.byte	'UP',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON2_Bits',0,14,135,1,3
	.word	11227
	.byte	7
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,14,138,1,16,4,13
	.byte	'PLLDIV',0,1
	.word	234
	.byte	6,2,2,35,0,13
	.byte	'PLLSEL',0,1
	.word	234
	.byte	2,0,2,35,0,13
	.byte	'PLLERAYDIV',0,1
	.word	234
	.byte	6,2,2,35,1,13
	.byte	'PLLERAYSEL',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'SRIDIV',0,1
	.word	234
	.byte	6,2,2,35,2,13
	.byte	'SRISEL',0,1
	.word	234
	.byte	2,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	6,2,2,35,3,13
	.byte	'UP',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON3_Bits',0,14,149,1,3
	.word	11355
	.byte	7
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,14,152,1,16,4,13
	.byte	'SPBDIV',0,1
	.word	234
	.byte	6,2,2,35,0,13
	.byte	'SPBSEL',0,1
	.word	234
	.byte	2,0,2,35,0,13
	.byte	'GTMDIV',0,1
	.word	234
	.byte	6,2,2,35,1,13
	.byte	'GTMSEL',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'STMDIV',0,1
	.word	234
	.byte	6,2,2,35,2,13
	.byte	'STMSEL',0,1
	.word	234
	.byte	2,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	6,2,2,35,3,13
	.byte	'UP',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON4_Bits',0,14,163,1,3
	.word	11582
	.byte	7
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,14,166,1,16,4,13
	.byte	'MAXDIV',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	601
	.byte	26,2,2,35,2,13
	.byte	'UP',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON5_Bits',0,14,172,1,3
	.word	11801
	.byte	7
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,14,175,1,16,4,13
	.byte	'CPU0DIV',0,1
	.word	234
	.byte	6,2,2,35,0,13
	.byte	'reserved_6',0,4
	.word	601
	.byte	26,0,2,35,2,0,4
	.byte	'Ifx_SCU_CCUCON6_Bits',0,14,179,1,3
	.word	11929
	.byte	7
	.byte	'_Ifx_SCU_CHIPID_Bits',0,14,182,1,16,4,13
	.byte	'CHREV',0,1
	.word	234
	.byte	6,2,2,35,0,13
	.byte	'CHTEC',0,1
	.word	234
	.byte	2,0,2,35,0,13
	.byte	'CHID',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'EEA',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'UCODE',0,1
	.word	234
	.byte	7,0,2,35,2,13
	.byte	'FSIZE',0,1
	.word	234
	.byte	4,4,2,35,3,13
	.byte	'SP',0,1
	.word	234
	.byte	2,2,2,35,3,13
	.byte	'SEC',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CHIPID_Bits',0,14,193,1,3
	.word	12029
	.byte	7
	.byte	'_Ifx_SCU_DTSCON_Bits',0,14,196,1,16,4,13
	.byte	'PWD',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'START',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	2,4,2,35,0,13
	.byte	'CAL',0,4
	.word	601
	.byte	22,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	234
	.byte	5,1,2,35,3,13
	.byte	'SLCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_DTSCON_Bits',0,14,204,1,3
	.word	12237
	.byte	7
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,14,207,1,16,4,13
	.byte	'LOWER',0,2
	.word	265
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	234
	.byte	5,1,2,35,1,13
	.byte	'LLU',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'UPPER',0,2
	.word	265
	.byte	10,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	234
	.byte	4,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'UOF',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_DTSLIM_Bits',0,14,216,1,3
	.word	12402
	.byte	7
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,14,219,1,16,4,13
	.byte	'RESULT',0,2
	.word	265
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	234
	.byte	4,2,2,35,1,13
	.byte	'RDY',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'BUSY',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,14,226,1,3
	.word	12585
	.byte	7
	.byte	'_Ifx_SCU_EICR_Bits',0,14,229,1,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'EXIS0',0,1
	.word	234
	.byte	3,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'FEN0',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'REN0',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'LDEN0',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'EIEN0',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'INP0',0,1
	.word	234
	.byte	3,1,2,35,1,13
	.byte	'reserved_15',0,4
	.word	601
	.byte	5,12,2,35,2,13
	.byte	'EXIS1',0,1
	.word	234
	.byte	3,1,2,35,2,13
	.byte	'reserved_23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'FEN1',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'REN1',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'LDEN1',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'EIEN1',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'INP1',0,1
	.word	234
	.byte	3,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EICR_Bits',0,14,248,1,3
	.word	12739
	.byte	7
	.byte	'_Ifx_SCU_EIFR_Bits',0,14,251,1,16,4,13
	.byte	'INTF0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'INTF1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'INTF2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'INTF3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'INTF4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'INTF5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'INTF6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'INTF7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	601
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_EIFR_Bits',0,14,134,2,3
	.word	13103
	.byte	7
	.byte	'_Ifx_SCU_EMSR_Bits',0,14,137,2,16,4,13
	.byte	'POL',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'MODE',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'ENON',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'PSEL',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,2
	.word	265
	.byte	12,0,2,35,0,13
	.byte	'EMSF',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'SEMSF',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	234
	.byte	6,0,2,35,2,13
	.byte	'EMSFM',0,1
	.word	234
	.byte	2,6,2,35,3,13
	.byte	'SEMSFM',0,1
	.word	234
	.byte	2,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	234
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_EMSR_Bits',0,14,150,2,3
	.word	13314
	.byte	7
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,14,153,2,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	7,1,2,35,0,13
	.byte	'EDCON',0,2
	.word	265
	.byte	2,7,2,35,0,13
	.byte	'reserved_9',0,4
	.word	601
	.byte	23,0,2,35,2,0,4
	.byte	'Ifx_SCU_ESRCFG_Bits',0,14,158,2,3
	.word	13566
	.byte	7
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,14,161,2,16,4,13
	.byte	'ARI',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'ARC',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	601
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_ESROCFG_Bits',0,14,166,2,3
	.word	13684
	.byte	7
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,14,169,2,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	28,4,2,35,2,13
	.byte	'EVR13OFF',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'BPEVR13OFF',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVR13CON_Bits',0,14,176,2,3
	.word	13795
	.byte	7
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,14,179,2,16,4,13
	.byte	'ADC13V',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'ADCSWDV',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	7,1,2,35,3,13
	.byte	'VAL',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,14,186,2,3
	.word	13958
	.byte	7
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,14,189,2,16,4,13
	.byte	'EVR13OVMOD',0,1
	.word	234
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	2,4,2,35,0,13
	.byte	'EVR13UVMOD',0,1
	.word	234
	.byte	2,2,2,35,0,13
	.byte	'reserved_6',0,2
	.word	265
	.byte	10,0,2,35,0,13
	.byte	'SWDOVMOD',0,1
	.word	234
	.byte	2,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	234
	.byte	2,4,2,35,2,13
	.byte	'SWDUVMOD',0,1
	.word	234
	.byte	2,2,2,35,2,13
	.byte	'reserved_22',0,2
	.word	265
	.byte	8,2,2,35,2,13
	.byte	'SLCK',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,14,201,2,3
	.word	14120
	.byte	7
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,14,204,2,16,4,13
	.byte	'EVR13OVVAL',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'SWDOVVAL',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	6,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVROVMON_Bits',0,14,212,2,3
	.word	14398
	.byte	7
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,14,215,2,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	28,4,2,35,2,13
	.byte	'RSTSWDOFF',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'BPRSTSWDOFF',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,14,222,2,3
	.word	14577
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,14,225,2,16,4,13
	.byte	'SD33P',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	234
	.byte	4,0,2,35,0,13
	.byte	'SD33I',0,1
	.word	234
	.byte	4,4,2,35,1,13
	.byte	'reserved_12',0,4
	.word	601
	.byte	19,1,2,35,2,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,14,232,2,3
	.word	14737
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,14,235,2,16,4,13
	.byte	'SDFREQSPRD',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	234
	.byte	4,0,2,35,0,13
	.byte	'TON',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'TOFF',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'SDSTEP',0,1
	.word	234
	.byte	4,4,2,35,3,13
	.byte	'SYNCDIV',0,1
	.word	234
	.byte	3,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,14,244,2,3
	.word	14898
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,14,247,2,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'STBS',0,1
	.word	234
	.byte	2,6,2,35,1,13
	.byte	'STSP',0,1
	.word	234
	.byte	2,4,2,35,1,13
	.byte	'NS',0,1
	.word	234
	.byte	2,2,2,35,1,13
	.byte	'OL',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'PIAD',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'ADCMODE',0,1
	.word	234
	.byte	4,4,2,35,2,13
	.byte	'ADCLPF',0,1
	.word	234
	.byte	2,2,2,35,2,13
	.byte	'ADCLSB',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'reserved_23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'SDLUT',0,1
	.word	234
	.byte	6,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,14,134,3,3
	.word	15090
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,14,137,3,16,4,13
	.byte	'SDOLCON',0,1
	.word	234
	.byte	7,1,2,35,0,13
	.byte	'MODSEL',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'MODLOW',0,1
	.word	234
	.byte	7,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'SDVOKLVL',0,1
	.word	234
	.byte	6,2,2,35,2,13
	.byte	'MODMAN',0,1
	.word	234
	.byte	2,0,2,35,2,13
	.byte	'MODHIGH',0,1
	.word	234
	.byte	7,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,14,147,3,3
	.word	15386
	.byte	7
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,14,150,3,16,4,13
	.byte	'EVR13',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'OV13',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	2,4,2,35,0,13
	.byte	'OVSWD',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'UV13',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'UVSWD',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	2,6,2,35,1,13
	.byte	'BGPROK',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'reserved_11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'SCMOD',0,1
	.word	234
	.byte	2,2,2,35,1,13
	.byte	'reserved_14',0,4
	.word	601
	.byte	18,0,2,35,2,0,4
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,14,164,3,3
	.word	15601
	.byte	7
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,14,167,3,16,4,13
	.byte	'EVR13UVVAL',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'SWDUVVAL',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	6,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,14,175,3,3
	.word	15890
	.byte	7
	.byte	'_Ifx_SCU_EXTCON_Bits',0,14,178,3,16,4,13
	.byte	'EN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'SEL0',0,1
	.word	234
	.byte	4,2,2,35,0,13
	.byte	'reserved_6',0,2
	.word	265
	.byte	10,0,2,35,0,13
	.byte	'EN1',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'NSEL',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'SEL1',0,1
	.word	234
	.byte	4,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	234
	.byte	2,0,2,35,2,13
	.byte	'DIV1',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_EXTCON_Bits',0,14,189,3,3
	.word	16069
	.byte	7
	.byte	'_Ifx_SCU_FDR_Bits',0,14,192,3,16,4,13
	.byte	'STEP',0,2
	.word	265
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	234
	.byte	4,2,2,35,1,13
	.byte	'DM',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'RESULT',0,2
	.word	265
	.byte	10,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	234
	.byte	5,1,2,35,3,13
	.byte	'DISCLK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_FDR_Bits',0,14,200,3,3
	.word	16287
	.byte	7
	.byte	'_Ifx_SCU_FMR_Bits',0,14,203,3,16,4,13
	.byte	'FS0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'FS1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'FS2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'FS3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'FS4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'FS5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'FS6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'FS7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'FC0',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'FC1',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'FC2',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'FC3',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'FC4',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'FC5',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'FC6',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'FC7',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_FMR_Bits',0,14,223,3,3
	.word	16450
	.byte	7
	.byte	'_Ifx_SCU_ID_Bits',0,14,226,3,16,4,13
	.byte	'MODREV',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'MODTYPE',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'MODNUMBER',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_ID_Bits',0,14,231,3,3
	.word	16786
	.byte	7
	.byte	'_Ifx_SCU_IGCR_Bits',0,14,234,3,16,4,13
	.byte	'IPEN00',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'IPEN01',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'IPEN02',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'IPEN03',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'IPEN04',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'IPEN05',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'IPEN06',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'IPEN07',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	5,3,2,35,1,13
	.byte	'GEEN0',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'IGP0',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'IPEN10',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'IPEN11',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'IPEN12',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'IPEN13',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'IPEN14',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'IPEN15',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'IPEN16',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'IPEN17',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	5,3,2,35,3,13
	.byte	'GEEN1',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'IGP1',0,1
	.word	234
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_SCU_IGCR_Bits',0,14,130,4,3
	.word	16893
	.byte	7
	.byte	'_Ifx_SCU_IN_Bits',0,14,133,4,16,4,13
	.byte	'P0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	601
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_IN_Bits',0,14,138,4,3
	.word	17345
	.byte	7
	.byte	'_Ifx_SCU_IOCR_Bits',0,14,141,4,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'PC0',0,1
	.word	234
	.byte	4,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	4,4,2,35,1,13
	.byte	'PC1',0,1
	.word	234
	.byte	4,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_IOCR_Bits',0,14,148,4,3
	.word	17444
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,14,151,4,16,4,13
	.byte	'LBISTREQ',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'LBISTREQP',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'PATTERNS',0,2
	.word	265
	.byte	14,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,14,157,4,3
	.word	17594
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,14,160,4,16,4,13
	.byte	'SEED',0,4
	.word	601
	.byte	23,9,2,35,2,13
	.byte	'reserved_23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'SPLITSH',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'BODY',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'LBISTFREQU',0,1
	.word	234
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,14,167,4,3
	.word	17743
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,14,170,4,16,4,13
	.byte	'SIGNATURE',0,4
	.word	601
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	7,1,2,35,3,13
	.byte	'LBISTDONE',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,14,175,4,3
	.word	17904
	.byte	7
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,14,178,4,16,4,13
	.byte	'reserved_0',0,2
	.word	265
	.byte	16,0,2,35,0,13
	.byte	'LS',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,2
	.word	265
	.byte	14,1,2,35,2,13
	.byte	'LSEN',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_LCLCON0_Bits',0,14,184,4,3
	.word	18034
	.byte	7
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,14,187,4,16,4,13
	.byte	'LCLT0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'LCLT1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	601
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_LCLTEST_Bits',0,14,192,4,3
	.word	18168
	.byte	7
	.byte	'_Ifx_SCU_MANID_Bits',0,14,195,4,16,4,13
	.byte	'DEPT',0,1
	.word	234
	.byte	5,3,2,35,0,13
	.byte	'MANUF',0,2
	.word	265
	.byte	11,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_MANID_Bits',0,14,200,4,3
	.word	18283
	.byte	7
	.byte	'_Ifx_SCU_OMR_Bits',0,14,203,4,16,4,13
	.byte	'PS0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PS1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,2
	.word	265
	.byte	14,0,2,35,0,13
	.byte	'PCL0',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'PCL1',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,2
	.word	265
	.byte	14,0,2,35,2,0,4
	.byte	'Ifx_SCU_OMR_Bits',0,14,211,4,3
	.word	18394
	.byte	7
	.byte	'_Ifx_SCU_OSCCON_Bits',0,14,214,4,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PLLLV',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'OSCRES',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'GAINSEL',0,1
	.word	234
	.byte	2,3,2,35,0,13
	.byte	'MODE',0,1
	.word	234
	.byte	2,1,2,35,0,13
	.byte	'SHBY',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'PLLHV',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'X1D',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'X1DEN',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	234
	.byte	4,0,2,35,1,13
	.byte	'OSCVAL',0,1
	.word	234
	.byte	5,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	234
	.byte	2,1,2,35,2,13
	.byte	'APREN',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_OSCCON_Bits',0,14,231,4,3
	.word	18552
	.byte	7
	.byte	'_Ifx_SCU_OUT_Bits',0,14,234,4,16,4,13
	.byte	'P0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	601
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_OUT_Bits',0,14,239,4,3
	.word	18892
	.byte	7
	.byte	'_Ifx_SCU_OVCCON_Bits',0,14,242,4,16,4,13
	.byte	'CSEL0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'CSEL1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'CSEL2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,2
	.word	265
	.byte	13,0,2,35,0,13
	.byte	'OVSTRT',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'OVSTP',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'DCINVAL',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	234
	.byte	5,0,2,35,2,13
	.byte	'OVCONF',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'POVCONF',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	234
	.byte	6,0,2,35,3,0,4
	.byte	'Ifx_SCU_OVCCON_Bits',0,14,255,4,3
	.word	18993
	.byte	7
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,14,130,5,16,4,13
	.byte	'OVEN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'OVEN1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'OVEN2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,4
	.word	601
	.byte	29,0,2,35,2,0,4
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,14,136,5,3
	.word	19260
	.byte	7
	.byte	'_Ifx_SCU_PDISC_Bits',0,14,139,5,16,4,13
	.byte	'PDIS0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PDIS1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	601
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDISC_Bits',0,14,144,5,3
	.word	19396
	.byte	7
	.byte	'_Ifx_SCU_PDR_Bits',0,14,147,5,16,4,13
	.byte	'PD0',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'PL0',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PD1',0,1
	.word	234
	.byte	3,1,2,35,0,13
	.byte	'PL1',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	601
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDR_Bits',0,14,154,5,3
	.word	19507
	.byte	7
	.byte	'_Ifx_SCU_PDRR_Bits',0,14,157,5,16,4,13
	.byte	'PDR0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PDR1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'PDR2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'PDR3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PDR4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'PDR5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'PDR6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PDR7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	601
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDRR_Bits',0,14,168,5,3
	.word	19640
	.byte	7
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,14,171,5,16,4,13
	.byte	'VCOBYP',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'VCOPWD',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'MODEN',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'SETFINDIS',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'CLRFINDIS',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'OSCDISCDIS',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'reserved_7',0,2
	.word	265
	.byte	2,7,2,35,0,13
	.byte	'NDIV',0,1
	.word	234
	.byte	7,0,2,35,1,13
	.byte	'PLLPWD',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'RESLD',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	234
	.byte	5,0,2,35,2,13
	.byte	'PDIV',0,1
	.word	234
	.byte	4,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	234
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_PLLCON0_Bits',0,14,188,5,3
	.word	19843
	.byte	7
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,14,191,5,16,4,13
	.byte	'K2DIV',0,1
	.word	234
	.byte	7,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'K3DIV',0,1
	.word	234
	.byte	7,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'K1DIV',0,1
	.word	234
	.byte	7,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	265
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLCON1_Bits',0,14,199,5,3
	.word	20199
	.byte	7
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,14,202,5,16,4,13
	.byte	'MODCFG',0,2
	.word	265
	.byte	16,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLCON2_Bits',0,14,206,5,3
	.word	20377
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,14,209,5,16,4,13
	.byte	'VCOBYP',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'VCOPWD',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	2,4,2,35,0,13
	.byte	'SETFINDIS',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'CLRFINDIS',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'OSCDISCDIS',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'reserved_7',0,2
	.word	265
	.byte	2,7,2,35,0,13
	.byte	'NDIV',0,1
	.word	234
	.byte	5,2,2,35,1,13
	.byte	'reserved_14',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'PLLPWD',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'RESLD',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	234
	.byte	5,0,2,35,2,13
	.byte	'PDIV',0,1
	.word	234
	.byte	4,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	234
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,14,226,5,3
	.word	20477
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,14,229,5,16,4,13
	.byte	'K2DIV',0,1
	.word	234
	.byte	7,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'K3DIV',0,1
	.word	234
	.byte	4,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	234
	.byte	4,0,2,35,1,13
	.byte	'K1DIV',0,1
	.word	234
	.byte	7,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	265
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,14,237,5,3
	.word	20847
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,14,240,5,16,4,13
	.byte	'VCOBYST',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PWDSTAT',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'VCOLOCK',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'FINDIS',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'K1RDY',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'K2RDY',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,4
	.word	601
	.byte	26,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,14,249,5,3
	.word	21033
	.byte	7
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,14,252,5,16,4,13
	.byte	'VCOBYST',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'VCOLOCK',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'FINDIS',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'K1RDY',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'K2RDY',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'MODRUN',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	601
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,14,135,6,3
	.word	21231
	.byte	7
	.byte	'_Ifx_SCU_PMCSR_Bits',0,14,138,6,16,4,13
	.byte	'REQSLP',0,1
	.word	234
	.byte	2,6,2,35,0,13
	.byte	'SMUSLP',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	234
	.byte	5,0,2,35,0,13
	.byte	'PMST',0,1
	.word	234
	.byte	3,5,2,35,1,13
	.byte	'reserved_11',0,4
	.word	601
	.byte	21,0,2,35,2,0,4
	.byte	'Ifx_SCU_PMCSR_Bits',0,14,145,6,3
	.word	21464
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,14,148,6,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'ESR1WKEN',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'PINAWKEN',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'PINBWKEN',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'ESR0DFEN',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'ESR0EDCON',0,1
	.word	234
	.byte	2,1,2,35,0,13
	.byte	'ESR1DFEN',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'ESR1EDCON',0,1
	.word	234
	.byte	2,6,2,35,1,13
	.byte	'PINADFEN',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'PINAEDCON',0,1
	.word	234
	.byte	2,3,2,35,1,13
	.byte	'PINBDFEN',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'PINBEDCON',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'reserved_16',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'STBYRAMSEL',0,1
	.word	234
	.byte	2,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'WUTWKEN',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	234
	.byte	2,1,2,35,2,13
	.byte	'PORSTDF',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'DCDCSYNC',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	234
	.byte	3,3,2,35,3,13
	.byte	'ESR0TRIST',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,14,174,6,3
	.word	21616
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,14,177,6,16,4,13
	.byte	'reserved_0',0,2
	.word	265
	.byte	12,4,2,35,0,13
	.byte	'IRADIS',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'reserved_13',0,4
	.word	601
	.byte	14,5,2,35,2,13
	.byte	'STBYEVEN',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'STBYEV',0,1
	.word	234
	.byte	3,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,14,185,6,3
	.word	22175
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,14,188,6,16,4,13
	.byte	'WUTREL',0,4
	.word	601
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	4,4,2,35,3,13
	.byte	'WUTDIV',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'WUTEN',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'WUTMODE',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,14,196,6,3
	.word	22358
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,14,199,6,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	2,6,2,35,0,13
	.byte	'ESR1WKP',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'ESR1OVRUN',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PINAWKP',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'PINAOVRUN',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'PINBWKP',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PINBOVRUN',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'PORSTDF',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'HWCFGEVR',0,1
	.word	234
	.byte	3,3,2,35,1,13
	.byte	'STBYRAM',0,1
	.word	234
	.byte	2,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'WUTWKP',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'WUTOVRUN',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'WUTWKEN',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'ESR1WKEN',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'PINAWKEN',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'PINBWKEN',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	265
	.byte	4,5,2,35,2,13
	.byte	'ESR0TRIST',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'WUTEN',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'WUTMODE',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'WUTRUN',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,14,226,6,3
	.word	22527
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,14,229,6,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	2,6,2,35,0,13
	.byte	'ESR1WKPCLR',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'ESR1OVRUNCLR',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PINAWKPCLR',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'PINAOVRUNCLR',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'PINBWKPCLR',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PINBOVRUNCLR',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'WUTWKPCLR',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'WUTOVRUNCLR',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,2
	.word	265
	.byte	14,0,2,35,2,0,4
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,14,242,6,3
	.word	23094
	.byte	7
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,14,245,6,16,4,13
	.byte	'WUTCNT',0,4
	.word	601
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	7,1,2,35,3,13
	.byte	'VAL',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,14,250,6,3
	.word	23410
	.byte	7
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,14,253,6,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'CLRC',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,2
	.word	265
	.byte	10,4,2,35,0,13
	.byte	'CSS0',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'CSS1',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'CSS2',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'USRINFO',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_RSTCON2_Bits',0,14,135,7,3
	.word	23529
	.byte	7
	.byte	'_Ifx_SCU_RSTCON_Bits',0,14,138,7,16,4,13
	.byte	'ESR0',0,1
	.word	234
	.byte	2,6,2,35,0,13
	.byte	'ESR1',0,1
	.word	234
	.byte	2,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	234
	.byte	2,2,2,35,0,13
	.byte	'SMU',0,1
	.word	234
	.byte	2,0,2,35,0,13
	.byte	'SW',0,1
	.word	234
	.byte	2,6,2,35,1,13
	.byte	'STM0',0,1
	.word	234
	.byte	2,4,2,35,1,13
	.byte	'STM1',0,1
	.word	234
	.byte	2,2,2,35,1,13
	.byte	'STM2',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_RSTCON_Bits',0,14,149,7,3
	.word	23738
	.byte	7
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,14,152,7,16,4,13
	.byte	'ESR0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'ESR1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'SMU',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'SW',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'STM0',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'STM1',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'STM2',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'PORST',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'CB0',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'CB1',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'CB3',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	234
	.byte	2,1,2,35,2,13
	.byte	'EVR13',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'EVR33',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'SWD',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	234
	.byte	2,4,2,35,3,13
	.byte	'STBYR',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	234
	.byte	3,0,2,35,3,0,4
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,14,175,7,3
	.word	23949
	.byte	7
	.byte	'_Ifx_SCU_SAFECON_Bits',0,14,178,7,16,4,13
	.byte	'HBT',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	601
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_SCU_SAFECON_Bits',0,14,182,7,3
	.word	24381
	.byte	7
	.byte	'_Ifx_SCU_STSTAT_Bits',0,14,185,7,16,4,13
	.byte	'HWCFG',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'FTM',0,1
	.word	234
	.byte	7,1,2,35,1,13
	.byte	'MODE',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'FCBAE',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'LUDIS',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'TRSTL',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'SPDEN',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	234
	.byte	3,0,2,35,2,13
	.byte	'RAMINT',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	234
	.byte	7,0,2,35,3,0,4
	.byte	'Ifx_SCU_STSTAT_Bits',0,14,198,7,3
	.word	24477
	.byte	7
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,14,201,7,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'SWRSTREQ',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	601
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,14,206,7,3
	.word	24737
	.byte	7
	.byte	'_Ifx_SCU_SYSCON_Bits',0,14,209,7,16,4,13
	.byte	'CCTRIG0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'RAMINTM',0,1
	.word	234
	.byte	2,4,2,35,0,13
	.byte	'SETLUDIS',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'reserved_5',0,1
	.word	234
	.byte	3,0,2,35,0,13
	.byte	'DATM',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,4
	.word	601
	.byte	23,0,2,35,2,0,4
	.byte	'Ifx_SCU_SYSCON_Bits',0,14,218,7,3
	.word	24862
	.byte	7
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,14,221,7,16,4,13
	.byte	'ESR0T',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	601
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,14,228,7,3
	.word	25059
	.byte	7
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,14,231,7,16,4,13
	.byte	'ESR0T',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	601
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,14,238,7,3
	.word	25212
	.byte	7
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,14,241,7,16,4,13
	.byte	'ESR0T',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	601
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPSET_Bits',0,14,248,7,3
	.word	25365
	.byte	7
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,14,251,7,16,4,13
	.byte	'ESR0T',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	601
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,14,130,8,3
	.word	25518
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,14,133,8,16,4,6
	.byte	'unsigned int',0,4,7,13
	.byte	'ENDINIT',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'LCK',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'PW',0,4
	.word	25705
	.byte	14,16,2,35,0,13
	.byte	'REL',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,14,139,8,3
	.word	25673
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,14,142,8,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	2,6,2,35,0,13
	.byte	'IR0',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'DR',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'IR1',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'UR',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PAR',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'TCR',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'TCTR',0,1
	.word	234
	.byte	7,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,14,154,8,3
	.word	25819
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,14,157,8,16,4,13
	.byte	'AE',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'OE',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'IS0',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'DS',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'TO',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'IS1',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'US',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PAS',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'TCS',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'TCT',0,1
	.word	234
	.byte	7,0,2,35,1,13
	.byte	'TIM',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,14,170,8,3
	.word	26057
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,14,173,8,16,4,13
	.byte	'ENDINIT',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'LCK',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'PW',0,4
	.word	25705
	.byte	14,16,2,35,0,13
	.byte	'REL',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,14,179,8,3
	.word	26280
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,14,182,8,16,4,13
	.byte	'CLRIRF',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'IR0',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'DR',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'IR1',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'UR',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PAR',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'TCR',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'TCTR',0,1
	.word	234
	.byte	7,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,14,195,8,3
	.word	26406
	.byte	7
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,14,198,8,16,4,13
	.byte	'AE',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'OE',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'IS0',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'DS',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'TO',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'IS1',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'US',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PAS',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'TCS',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'TCT',0,1
	.word	234
	.byte	7,0,2,35,1,13
	.byte	'TIM',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,14,211,8,3
	.word	26658
	.byte	21,14,219,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,6
	.byte	'int',0,4,5,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	9937
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ACCEN0',0,14,224,8,3
	.word	26877
	.byte	21,14,227,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	10494
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ACCEN1',0,14,232,8,3
	.word	26948
	.byte	21,14,235,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	10571
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ARSTDIS',0,14,240,8,3
	.word	27012
	.byte	21,14,243,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	10707
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON0',0,14,248,8,3
	.word	27077
	.byte	21,14,251,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	10989
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON1',0,14,128,9,3
	.word	27142
	.byte	21,14,131,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	11227
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON2',0,14,136,9,3
	.word	27207
	.byte	21,14,139,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	11355
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON3',0,14,144,9,3
	.word	27272
	.byte	21,14,147,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	11582
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON4',0,14,152,9,3
	.word	27337
	.byte	21,14,155,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	11801
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON5',0,14,160,9,3
	.word	27402
	.byte	21,14,163,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	11929
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON6',0,14,168,9,3
	.word	27467
	.byte	21,14,171,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	12029
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CHIPID',0,14,176,9,3
	.word	27532
	.byte	21,14,179,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	12237
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSCON',0,14,184,9,3
	.word	27596
	.byte	21,14,187,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	12402
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSLIM',0,14,192,9,3
	.word	27660
	.byte	21,14,195,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	12585
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSSTAT',0,14,200,9,3
	.word	27724
	.byte	21,14,203,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	12739
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EICR',0,14,208,9,3
	.word	27789
	.byte	21,14,211,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	13103
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EIFR',0,14,216,9,3
	.word	27851
	.byte	21,14,219,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	13314
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EMSR',0,14,224,9,3
	.word	27913
	.byte	21,14,227,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	13566
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ESRCFG',0,14,232,9,3
	.word	27975
	.byte	21,14,235,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	13684
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ESROCFG',0,14,240,9,3
	.word	28039
	.byte	21,14,243,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	13795
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVR13CON',0,14,248,9,3
	.word	28104
	.byte	21,14,251,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	13958
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRADCSTAT',0,14,128,10,3
	.word	28170
	.byte	21,14,131,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	14120
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRMONCTRL',0,14,136,10,3
	.word	28238
	.byte	21,14,139,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	14398
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVROVMON',0,14,144,10,3
	.word	28306
	.byte	21,14,147,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	14577
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRRSTCON',0,14,152,10,3
	.word	28372
	.byte	21,14,155,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	14737
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,14,160,10,3
	.word	28439
	.byte	21,14,163,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	14898
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL1',0,14,168,10,3
	.word	28508
	.byte	21,14,171,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	15090
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL2',0,14,176,10,3
	.word	28576
	.byte	21,14,179,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	15386
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL3',0,14,184,10,3
	.word	28644
	.byte	21,14,187,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	15601
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSTAT',0,14,192,10,3
	.word	28712
	.byte	21,14,195,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	15890
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRUVMON',0,14,200,10,3
	.word	28777
	.byte	21,14,203,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	16069
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EXTCON',0,14,208,10,3
	.word	28843
	.byte	21,14,211,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	16287
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_FDR',0,14,216,10,3
	.word	28907
	.byte	21,14,219,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	16450
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_FMR',0,14,224,10,3
	.word	28968
	.byte	21,14,227,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	16786
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ID',0,14,232,10,3
	.word	29029
	.byte	21,14,235,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	16893
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IGCR',0,14,240,10,3
	.word	29089
	.byte	21,14,243,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	17345
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IN',0,14,248,10,3
	.word	29151
	.byte	21,14,251,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	17444
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IOCR',0,14,128,11,3
	.word	29211
	.byte	21,14,131,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	17594
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL0',0,14,136,11,3
	.word	29273
	.byte	21,14,139,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	17743
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL1',0,14,144,11,3
	.word	29341
	.byte	21,14,147,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	17904
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL2',0,14,152,11,3
	.word	29409
	.byte	21,14,155,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	18034
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LCLCON0',0,14,160,11,3
	.word	29477
	.byte	21,14,163,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	18168
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LCLTEST',0,14,168,11,3
	.word	29542
	.byte	21,14,171,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	18283
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_MANID',0,14,176,11,3
	.word	29607
	.byte	21,14,179,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	18394
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OMR',0,14,184,11,3
	.word	29670
	.byte	21,14,187,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	18552
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OSCCON',0,14,192,11,3
	.word	29731
	.byte	21,14,195,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	18892
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OUT',0,14,200,11,3
	.word	29795
	.byte	21,14,203,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	18993
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OVCCON',0,14,208,11,3
	.word	29856
	.byte	21,14,211,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	19260
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OVCENABLE',0,14,216,11,3
	.word	29920
	.byte	21,14,219,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	19396
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDISC',0,14,224,11,3
	.word	29987
	.byte	21,14,227,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	19507
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDR',0,14,232,11,3
	.word	30050
	.byte	21,14,235,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	19640
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDRR',0,14,240,11,3
	.word	30111
	.byte	21,14,243,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	19843
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON0',0,14,248,11,3
	.word	30173
	.byte	21,14,251,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	20199
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON1',0,14,128,12,3
	.word	30238
	.byte	21,14,131,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	20377
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON2',0,14,136,12,3
	.word	30303
	.byte	21,14,139,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	20477
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYCON0',0,14,144,12,3
	.word	30368
	.byte	21,14,147,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	20847
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYCON1',0,14,152,12,3
	.word	30437
	.byte	21,14,155,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	21033
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYSTAT',0,14,160,12,3
	.word	30506
	.byte	21,14,163,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	21231
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLSTAT',0,14,168,12,3
	.word	30575
	.byte	21,14,171,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	21464
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMCSR',0,14,176,12,3
	.word	30640
	.byte	21,14,179,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	21616
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR0',0,14,184,12,3
	.word	30703
	.byte	21,14,187,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	22175
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR1',0,14,192,12,3
	.word	30768
	.byte	21,14,195,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	22358
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR3',0,14,200,12,3
	.word	30833
	.byte	21,14,203,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	22527
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWSTAT',0,14,208,12,3
	.word	30898
	.byte	21,14,211,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	23094
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWSTATCLR',0,14,216,12,3
	.word	30964
	.byte	21,14,219,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	23410
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWUTCNT',0,14,224,12,3
	.word	31033
	.byte	21,14,227,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	23738
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTCON',0,14,232,12,3
	.word	31100
	.byte	21,14,235,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	23529
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTCON2',0,14,240,12,3
	.word	31164
	.byte	21,14,243,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	23949
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTSTAT',0,14,248,12,3
	.word	31229
	.byte	21,14,251,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	24381
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SAFECON',0,14,128,13,3
	.word	31294
	.byte	21,14,131,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	24477
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_STSTAT',0,14,136,13,3
	.word	31359
	.byte	21,14,139,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	24737
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SWRSTCON',0,14,144,13,3
	.word	31423
	.byte	21,14,147,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	24862
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SYSCON',0,14,152,13,3
	.word	31489
	.byte	21,14,155,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	25059
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPCLR',0,14,160,13,3
	.word	31553
	.byte	21,14,163,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	25212
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPDIS',0,14,168,13,3
	.word	31618
	.byte	21,14,171,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	25365
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPSET',0,14,176,13,3
	.word	31683
	.byte	21,14,179,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	25518
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPSTAT',0,14,184,13,3
	.word	31748
	.byte	21,14,187,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	25673
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON0',0,14,192,13,3
	.word	31814
	.byte	21,14,195,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	25819
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON1',0,14,200,13,3
	.word	31883
	.byte	21,14,203,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	26057
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_SR',0,14,208,13,3
	.word	31952
	.byte	21,14,211,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	26280
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON0',0,14,216,13,3
	.word	32019
	.byte	21,14,219,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	26406
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON1',0,14,224,13,3
	.word	32086
	.byte	21,14,227,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	26658
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_SR',0,14,232,13,3
	.word	32153
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU',0,14,243,13,25,12,9
	.byte	'CON0',0,4
	.word	31814
	.byte	2,35,0,9
	.byte	'CON1',0,4
	.word	31883
	.byte	2,35,4,9
	.byte	'SR',0,4
	.word	31952
	.byte	2,35,8,0,22
	.word	32218
	.byte	4
	.byte	'Ifx_SCU_WDTCPU',0,14,248,13,3
	.word	32281
	.byte	7
	.byte	'_Ifx_SCU_WDTS',0,14,251,13,25,12,9
	.byte	'CON0',0,4
	.word	32019
	.byte	2,35,0,9
	.byte	'CON1',0,4
	.word	32086
	.byte	2,35,4,9
	.byte	'SR',0,4
	.word	32153
	.byte	2,35,8,0,22
	.word	32310
	.byte	4
	.byte	'Ifx_SCU_WDTS',0,14,128,14,3
	.word	32371
	.byte	7
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,15,45,16,4,13
	.byte	'EN0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'EN1',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'EN2',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'EN3',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'EN4',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'EN5',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'EN6',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'EN7',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'EN8',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'EN9',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'EN10',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'EN11',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'EN12',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'EN13',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'EN14',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'EN15',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'EN16',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'EN17',0,4
	.word	25705
	.byte	1,14,2,35,0,13
	.byte	'EN18',0,4
	.word	25705
	.byte	1,13,2,35,0,13
	.byte	'EN19',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'EN20',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'EN21',0,4
	.word	25705
	.byte	1,10,2,35,0,13
	.byte	'EN22',0,4
	.word	25705
	.byte	1,9,2,35,0,13
	.byte	'EN23',0,4
	.word	25705
	.byte	1,8,2,35,0,13
	.byte	'EN24',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'EN25',0,4
	.word	25705
	.byte	1,6,2,35,0,13
	.byte	'EN26',0,4
	.word	25705
	.byte	1,5,2,35,0,13
	.byte	'EN27',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'EN28',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'EN29',0,4
	.word	25705
	.byte	1,2,2,35,0,13
	.byte	'EN30',0,4
	.word	25705
	.byte	1,1,2,35,0,13
	.byte	'EN31',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN0_Bits',0,15,79,3
	.word	32398
	.byte	7
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,15,82,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN1_Bits',0,15,85,3
	.word	32955
	.byte	7
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,15,88,16,4,13
	.byte	'SEL0',0,4
	.word	25705
	.byte	4,28,2,35,0,13
	.byte	'SEL1',0,4
	.word	25705
	.byte	4,24,2,35,0,13
	.byte	'SEL2',0,4
	.word	25705
	.byte	4,20,2,35,0,13
	.byte	'SEL3',0,4
	.word	25705
	.byte	4,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,15,95,3
	.word	33032
	.byte	7
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,15,98,16,4,13
	.byte	'SEL0',0,4
	.word	25705
	.byte	4,28,2,35,0,13
	.byte	'SEL1',0,4
	.word	25705
	.byte	4,24,2,35,0,13
	.byte	'SEL2',0,4
	.word	25705
	.byte	4,20,2,35,0,13
	.byte	'SEL3',0,4
	.word	25705
	.byte	4,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,15,105,3
	.word	33186
	.byte	7
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,15,108,16,4,13
	.byte	'TO_ADDR',0,4
	.word	25705
	.byte	20,12,2,35,0,13
	.byte	'TO_W1R0',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'reserved_21',0,4
	.word	25705
	.byte	11,0,2,35,0,0,4
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,15,113,3
	.word	33340
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,15,116,16,4,13
	.byte	'BRG_MODE',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'MSK_WR_RSP',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	6,24,2,35,0,13
	.byte	'MODE_UP_PGR',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'BUFF_OVL',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'SYNC_INPUT_REG',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'reserved_13',0,4
	.word	25705
	.byte	3,16,2,35,0,13
	.byte	'BRG_RST',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'reserved_17',0,4
	.word	25705
	.byte	7,8,2,35,0,13
	.byte	'BUFF_DPT',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,15,129,1,3
	.word	33468
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,15,132,1,16,4,13
	.byte	'NEW_TRAN_PTR',0,4
	.word	25705
	.byte	5,27,2,35,0,13
	.byte	'FIRST_RSP_PTR',0,4
	.word	25705
	.byte	5,22,2,35,0,13
	.byte	'TRAN_IN_PGR',0,4
	.word	25705
	.byte	5,17,2,35,0,13
	.byte	'ABT_TRAN_PGR',0,4
	.word	25705
	.byte	5,12,2,35,0,13
	.byte	'FBC',0,4
	.word	25705
	.byte	6,6,2,35,0,13
	.byte	'RSP_TRAN_RDY',0,4
	.word	25705
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,15,140,1,3
	.word	33775
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,15,143,1,16,4,13
	.byte	'TRAN_IN_PGR2',0,4
	.word	25705
	.byte	5,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,15,147,1,3
	.word	33977
	.byte	7
	.byte	'_Ifx_GTM_CLC_Bits',0,15,150,1,16,4,13
	.byte	'DISR',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'DISS',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'EDIS',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_CLC_Bits',0,15,157,1,3
	.word	34090
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,15,160,1,16,4,13
	.byte	'CLK_CNT',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,15,164,1,3
	.word	34233
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,15,167,1,16,4,13
	.byte	'CLK_CNT',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'CLK6_SEL',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'reserved_25',0,4
	.word	25705
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,15,172,1,3
	.word	34350
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,15,175,1,16,4,13
	.byte	'CLK_CNT',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'CLK7_SEL',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'reserved_25',0,4
	.word	25705
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,15,180,1,3
	.word	34485
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,15,183,1,16,4,13
	.byte	'EN_CLK0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'EN_CLK1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'EN_CLK2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'EN_CLK3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'EN_CLK4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'EN_CLK5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'EN_CLK6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'EN_CLK7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'EN_ECLK0',0,4
	.word	25705
	.byte	2,14,2,35,0,13
	.byte	'EN_ECLK1',0,4
	.word	25705
	.byte	2,12,2,35,0,13
	.byte	'EN_ECLK2',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'EN_FXCLK',0,4
	.word	25705
	.byte	2,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,15,198,1,3
	.word	34620
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,15,201,1,16,4,13
	.byte	'ECLK_DEN',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,15,205,1,3
	.word	34940
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,15,208,1,16,4,13
	.byte	'ECLK_NUM',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,15,212,1,3
	.word	35052
	.byte	7
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,15,215,1,16,4,13
	.byte	'FXCLK_SEL',0,4
	.word	25705
	.byte	4,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,15,219,1,3
	.word	35164
	.byte	7
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,15,222,1,16,4,13
	.byte	'GCLK_DEN',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,15,226,1,3
	.word	35280
	.byte	7
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,15,229,1,16,4,13
	.byte	'GCLK_NUM',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,15,233,1,3
	.word	35392
	.byte	7
	.byte	'_Ifx_GTM_CTRL_Bits',0,15,236,1,16,4,13
	.byte	'RF_PROT',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TO_MODE',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'TO_VAL',0,4
	.word	25705
	.byte	5,23,2,35,0,13
	.byte	'reserved_9',0,4
	.word	25705
	.byte	23,0,2,35,0,0,4
	.byte	'Ifx_GTM_CTRL_Bits',0,15,243,1,3
	.word	35504
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,15,246,1,16,4,13
	.byte	'O1SEL_0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	2,29,2,35,0,13
	.byte	'SWAP_0',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'O1F_0',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'reserved_6',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'O1SEL_1',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'I1SEL_1',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'SH_EN_1',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'SWAP_1',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'O1F_1',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'reserved_14',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'O1SEL_2',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'I1SEL_2',0,4
	.word	25705
	.byte	1,14,2,35,0,13
	.byte	'SH_EN_2',0,4
	.word	25705
	.byte	1,13,2,35,0,13
	.byte	'SWAP_2',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'O1F_2',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'reserved_22',0,4
	.word	25705
	.byte	2,8,2,35,0,13
	.byte	'O1SEL_3',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'I1SEL_3',0,4
	.word	25705
	.byte	1,6,2,35,0,13
	.byte	'SH_EN_3',0,4
	.word	25705
	.byte	1,5,2,35,0,13
	.byte	'SWAP_3',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'O1F_3',0,4
	.word	25705
	.byte	2,2,2,35,0,13
	.byte	'reserved_30',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,15,143,2,3
	.word	35657
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,15,146,2,16,4,13
	.byte	'POL0_0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'OC0_0',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'SL0_0',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'DT0_0',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'POL1_0',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'OC1_0',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'SL1_0',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'DT1_0',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'POL0_1',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'OC0_1',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'SL0_1',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'DT0_1',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'POL1_1',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'OC1_1',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'SL1_1',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'DT1_1',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'POL0_2',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'OC0_2',0,4
	.word	25705
	.byte	1,14,2,35,0,13
	.byte	'SL0_2',0,4
	.word	25705
	.byte	1,13,2,35,0,13
	.byte	'DT0_2',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'POL1_2',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'OC1_2',0,4
	.word	25705
	.byte	1,10,2,35,0,13
	.byte	'SL1_2',0,4
	.word	25705
	.byte	1,9,2,35,0,13
	.byte	'DT1_2',0,4
	.word	25705
	.byte	1,8,2,35,0,13
	.byte	'POL0_3',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'OC0_3',0,4
	.word	25705
	.byte	1,6,2,35,0,13
	.byte	'SL0_3',0,4
	.word	25705
	.byte	1,5,2,35,0,13
	.byte	'DT0_3',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'POL1_3',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'OC1_3',0,4
	.word	25705
	.byte	1,2,2,35,0,13
	.byte	'SL1_3',0,4
	.word	25705
	.byte	1,1,2,35,0,13
	.byte	'DT1_3',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,15,180,2,3
	.word	36169
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,15,183,2,16,4,13
	.byte	'POL0_0_SR',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'OC0_0_SR',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'SL0_0_SR',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'DT0_0_SR',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'POL1_0_SR',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'OC1_0_SR',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'SL1_0_SR',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'DT1_0_SR',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'POL0_1_SR',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'OC0_1_SR',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'SL0_1_SR',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'DT0_1_SR',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'POL1_1_SR',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'OC1_1_SR',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'SL1_1_SR',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'DT1_1_SR',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'POL0_2_SR',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'OC0_2_SR',0,4
	.word	25705
	.byte	1,14,2,35,0,13
	.byte	'SL0_2_SR',0,4
	.word	25705
	.byte	1,13,2,35,0,13
	.byte	'DT0_2_SR',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'POL1_2_SR',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'OC1_2_SR',0,4
	.word	25705
	.byte	1,10,2,35,0,13
	.byte	'SL1_2_SR',0,4
	.word	25705
	.byte	1,9,2,35,0,13
	.byte	'DT1_2_SR',0,4
	.word	25705
	.byte	1,8,2,35,0,13
	.byte	'POL0_3_SR',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'OC0_3_SR',0,4
	.word	25705
	.byte	1,6,2,35,0,13
	.byte	'SL0_3_SR',0,4
	.word	25705
	.byte	1,5,2,35,0,13
	.byte	'DT0_3_SR',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'POL1_3_SR',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'OC1_3_SR',0,4
	.word	25705
	.byte	1,2,2,35,0,13
	.byte	'SL1_3_SR',0,4
	.word	25705
	.byte	1,1,2,35,0,13
	.byte	'DT1_3_SR',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,15,217,2,3
	.word	36790
	.byte	7
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,15,220,2,16,4,13
	.byte	'CLK_SEL',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'UPD_MODE',0,4
	.word	25705
	.byte	3,25,2,35,0,13
	.byte	'reserved_7',0,4
	.word	25705
	.byte	25,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,15,226,2,3
	.word	37513
	.byte	7
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,15,229,2,16,4,13
	.byte	'RELRISE',0,4
	.word	25705
	.byte	10,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	25705
	.byte	6,16,2,35,0,13
	.byte	'RELFALL',0,4
	.word	25705
	.byte	10,6,2,35,0,13
	.byte	'reserved_26',0,4
	.word	25705
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,15,235,2,3
	.word	37657
	.byte	7
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,15,238,2,16,4,13
	.byte	'RELBLK',0,4
	.word	25705
	.byte	10,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	25705
	.byte	6,16,2,35,0,13
	.byte	'PSU_IN_SEL',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'IN_POL',0,4
	.word	25705
	.byte	1,14,2,35,0,13
	.byte	'reserved_18',0,4
	.word	25705
	.byte	2,12,2,35,0,13
	.byte	'SHIFT_SEL',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'reserved_22',0,4
	.word	25705
	.byte	10,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,15,247,2,3
	.word	37806
	.byte	7
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,15,250,2,16,4,13
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,15,129,3,3
	.word	38021
	.byte	7
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,15,132,3,16,4,13
	.byte	'GRSTEN',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'BRIDGE_MODE_RST',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'AEI_IN',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	5,24,2,35,0,13
	.byte	'TOM_OUT_RST',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	25705
	.byte	3,20,2,35,0,13
	.byte	'reserved_12',0,4
	.word	25705
	.byte	4,16,2,35,0,13
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'IRQ_MODE_PULSE',0,4
	.word	25705
	.byte	1,14,2,35,0,13
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	25705
	.byte	1,13,2,35,0,13
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'reserved_20',0,4
	.word	25705
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_GTM_HW_CONF_Bits',0,15,146,3,3
	.word	38225
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,15,149,3,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	4,28,2,35,0,13
	.byte	'AEI_IRQ',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,15,154,3,3
	.word	38582
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,15,157,3,16,4,13
	.byte	'TIM0_CH0_IRQ',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TIM0_CH1_IRQ',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'TIM0_CH2_IRQ',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'TIM0_CH3_IRQ',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'TIM0_CH4_IRQ',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'TIM0_CH5_IRQ',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'TIM0_CH6_IRQ',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'TIM0_CH7_IRQ',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,15,168,3,3
	.word	38710
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,15,171,3,16,4,13
	.byte	'TOM0_CH0_IRQ',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TOM0_CH1_IRQ',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'TOM0_CH2_IRQ',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'TOM0_CH3_IRQ',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'TOM0_CH4_IRQ',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'TOM0_CH5_IRQ',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'TOM0_CH6_IRQ',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'TOM0_CH7_IRQ',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'TOM0_CH8_IRQ',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'TOM0_CH9_IRQ',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'TOM0_CH10_IRQ',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'TOM0_CH11_IRQ',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'TOM0_CH12_IRQ',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'TOM0_CH13_IRQ',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'TOM0_CH14_IRQ',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'TOM0_CH15_IRQ',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'TOM1_CH0_IRQ',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'TOM1_CH1_IRQ',0,4
	.word	25705
	.byte	1,14,2,35,0,13
	.byte	'TOM1_CH2_IRQ',0,4
	.word	25705
	.byte	1,13,2,35,0,13
	.byte	'TOM1_CH3_IRQ',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'TOM1_CH4_IRQ',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'TOM1_CH5_IRQ',0,4
	.word	25705
	.byte	1,10,2,35,0,13
	.byte	'TOM1_CH6_IRQ',0,4
	.word	25705
	.byte	1,9,2,35,0,13
	.byte	'TOM1_CH7_IRQ',0,4
	.word	25705
	.byte	1,8,2,35,0,13
	.byte	'TOM1_CH8_IRQ',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'TOM1_CH9_IRQ',0,4
	.word	25705
	.byte	1,6,2,35,0,13
	.byte	'TOM1_CH10_IRQ',0,4
	.word	25705
	.byte	1,5,2,35,0,13
	.byte	'TOM1_CH11_IRQ',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'TOM1_CH12_IRQ',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'TOM1_CH13_IRQ',0,4
	.word	25705
	.byte	1,2,2,35,0,13
	.byte	'TOM1_CH14_IRQ',0,4
	.word	25705
	.byte	1,1,2,35,0,13
	.byte	'TOM1_CH15_IRQ',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,15,205,3,3
	.word	38989
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,15,208,3,16,4,13
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,15,219,3,3
	.word	39834
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,15,222,3,16,4,13
	.byte	'GTM_EIRQ',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	3,28,2,35,0,13
	.byte	'TIM0_EIRQ',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,15,228,3,3
	.word	40127
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,15,231,3,16,4,13
	.byte	'SEL0',0,4
	.word	25705
	.byte	4,28,2,35,0,13
	.byte	'SEL1',0,4
	.word	25705
	.byte	4,24,2,35,0,13
	.byte	'SEL2',0,4
	.word	25705
	.byte	4,20,2,35,0,13
	.byte	'SEL3',0,4
	.word	25705
	.byte	4,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,15,238,3,3
	.word	40281
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,15,241,3,16,4,13
	.byte	'SEL0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'SEL1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'SEL2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'SEL3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'SEL4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'SEL5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'SEL6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'SEL7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'SEL8',0,4
	.word	25705
	.byte	2,14,2,35,0,13
	.byte	'SEL9',0,4
	.word	25705
	.byte	2,12,2,35,0,13
	.byte	'SEL10',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'SEL11',0,4
	.word	25705
	.byte	2,8,2,35,0,13
	.byte	'SEL12',0,4
	.word	25705
	.byte	2,6,2,35,0,13
	.byte	'SEL13',0,4
	.word	25705
	.byte	2,4,2,35,0,13
	.byte	'SEL14',0,4
	.word	25705
	.byte	2,2,2,35,0,13
	.byte	'SEL15',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,15,131,4,3
	.word	40451
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,15,134,4,16,4,13
	.byte	'CH0SEL',0,4
	.word	25705
	.byte	4,28,2,35,0,13
	.byte	'CH1SEL',0,4
	.word	25705
	.byte	4,24,2,35,0,13
	.byte	'CH2SEL',0,4
	.word	25705
	.byte	4,20,2,35,0,13
	.byte	'CH3SEL',0,4
	.word	25705
	.byte	4,16,2,35,0,13
	.byte	'CH4SEL',0,4
	.word	25705
	.byte	4,12,2,35,0,13
	.byte	'CH5SEL',0,4
	.word	25705
	.byte	4,8,2,35,0,13
	.byte	'CH6SEL',0,4
	.word	25705
	.byte	4,4,2,35,0,13
	.byte	'CH7SEL',0,4
	.word	25705
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,15,144,4,3
	.word	40792
	.byte	7
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,15,147,4,16,4,13
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,15,154,4,3
	.word	41017
	.byte	7
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,15,157,4,16,4,13
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'TRG_AEI_USP_BE',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,15,164,4,3
	.word	41215
	.byte	7
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,15,167,4,16,4,13
	.byte	'IRQ_MODE',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,15,171,4,3
	.word	41411
	.byte	7
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,15,174,4,16,4,13
	.byte	'AEI_TO_XPT',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'AEI_USP_ADDR',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'AEI_IM_ADDR',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'AEI_USP_BE',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,15,181,4,3
	.word	41514
	.byte	7
	.byte	'_Ifx_GTM_KRST0_Bits',0,15,184,4,16,4,13
	.byte	'RST',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'RSTSTAT',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRST0_Bits',0,15,189,4,3
	.word	41692
	.byte	7
	.byte	'_Ifx_GTM_KRST1_Bits',0,15,192,4,16,4,13
	.byte	'RST',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRST1_Bits',0,15,196,4,3
	.word	41803
	.byte	7
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,15,199,4,16,4,13
	.byte	'CLR',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,15,203,4,3
	.word	41895
	.byte	7
	.byte	'_Ifx_GTM_OCS_Bits',0,15,206,4,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'SUS',0,4
	.word	25705
	.byte	4,4,2,35,0,13
	.byte	'SUS_P',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'SUSSTA',0,4
	.word	25705
	.byte	1,2,2,35,0,13
	.byte	'reserved_30',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_OCS_Bits',0,15,213,4,3
	.word	41991
	.byte	7
	.byte	'_Ifx_GTM_ODA_Bits',0,15,216,4,16,4,13
	.byte	'DDREN',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'DREN',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_ODA_Bits',0,15,221,4,3
	.word	42137
	.byte	7
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,15,224,4,16,4,13
	.byte	'CV',0,4
	.word	25705
	.byte	27,5,2,35,0,13
	.byte	'reserved_27',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'CM',0,4
	.word	25705
	.byte	2,2,2,35,0,13
	.byte	'reserved_30',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU0T_Bits',0,15,230,4,3
	.word	42243
	.byte	7
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,15,233,4,16,4,13
	.byte	'CV',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	4,4,2,35,0,13
	.byte	'EN',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'reserved_29',0,4
	.word	25705
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU1T_Bits',0,15,239,4,3
	.word	42374
	.byte	7
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,15,242,4,16,4,13
	.byte	'CV',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	4,4,2,35,0,13
	.byte	'EN',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'reserved_29',0,4
	.word	25705
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU2T_Bits',0,15,248,4,3
	.word	42505
	.byte	7
	.byte	'_Ifx_GTM_OTSC0_Bits',0,15,251,4,16,4,13
	.byte	'B0LMT',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'B0LMI',0,4
	.word	25705
	.byte	4,24,2,35,0,13
	.byte	'B0HMT',0,4
	.word	25705
	.byte	3,21,2,35,0,13
	.byte	'reserved_11',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'B0HMI',0,4
	.word	25705
	.byte	4,16,2,35,0,13
	.byte	'B1LMT',0,4
	.word	25705
	.byte	3,13,2,35,0,13
	.byte	'reserved_19',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'B1LMI',0,4
	.word	25705
	.byte	4,8,2,35,0,13
	.byte	'B1HMT',0,4
	.word	25705
	.byte	3,5,2,35,0,13
	.byte	'reserved_27',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'B1HMI',0,4
	.word	25705
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTSC0_Bits',0,15,137,5,3
	.word	42636
	.byte	7
	.byte	'_Ifx_GTM_OTSS_Bits',0,15,140,5,16,4,13
	.byte	'OTGB0',0,4
	.word	25705
	.byte	4,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	4,24,2,35,0,13
	.byte	'OTGB1',0,4
	.word	25705
	.byte	4,20,2,35,0,13
	.byte	'reserved_12',0,4
	.word	25705
	.byte	4,16,2,35,0,13
	.byte	'OTGB2',0,4
	.word	25705
	.byte	4,12,2,35,0,13
	.byte	'reserved_20',0,4
	.word	25705
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTSS_Bits',0,15,148,5,3
	.word	42918
	.byte	7
	.byte	'_Ifx_GTM_REV_Bits',0,15,151,5,16,4,13
	.byte	'STEP',0,4
	.word	25705
	.byte	8,24,2,35,0,13
	.byte	'NO',0,4
	.word	25705
	.byte	4,20,2,35,0,13
	.byte	'MINOR',0,4
	.word	25705
	.byte	4,16,2,35,0,13
	.byte	'MAJOR',0,4
	.word	25705
	.byte	4,12,2,35,0,13
	.byte	'DEV_CODE0',0,4
	.word	25705
	.byte	4,8,2,35,0,13
	.byte	'DEV_CODE1',0,4
	.word	25705
	.byte	4,4,2,35,0,13
	.byte	'DEV_CODE2',0,4
	.word	25705
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_REV_Bits',0,15,160,5,3
	.word	43090
	.byte	7
	.byte	'_Ifx_GTM_RST_Bits',0,15,163,5,16,4,13
	.byte	'RST',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_RST_Bits',0,15,167,5,3
	.word	43268
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,15,170,5,16,4,13
	.byte	'BASE',0,4
	.word	25705
	.byte	27,5,2,35,0,13
	.byte	'reserved_27',0,4
	.word	25705
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,15,174,5,3
	.word	43356
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,15,177,5,16,4,13
	.byte	'LOW_RES',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'CH_CLK_SRC',0,4
	.word	25705
	.byte	3,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,15,182,5,3
	.word	43464
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,15,185,5,16,4,13
	.byte	'BASE',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,15,189,5,3
	.word	43596
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,15,192,5,16,4,13
	.byte	'CH_MODE',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'CH_CLK_SRC',0,4
	.word	25705
	.byte	3,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,15,197,5,3
	.word	43704
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,15,200,5,16,4,13
	.byte	'BASE',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,15,204,5,3
	.word	43836
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,15,207,5,16,4,13
	.byte	'CH_MODE',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'CH_CLK_SRC',0,4
	.word	25705
	.byte	3,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,15,212,5,3
	.word	43944
	.byte	7
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,15,215,5,16,4,13
	.byte	'ENDIS_CH0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'ENDIS_CH1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'ENDIS_CH2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'reserved_6',0,4
	.word	25705
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,15,221,5,3
	.word	44076
	.byte	7
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,15,224,5,16,4,13
	.byte	'SRC_CH0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'SRC_CH1',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'SRC_CH2',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'SRC_CH3',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'SRC_CH4',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'SRC_CH5',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'SRC_CH6',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'SRC_CH7',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,15,235,5,3
	.word	44222
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,15,238,5,16,4,13
	.byte	'CNT',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,15,242,5,3
	.word	44469
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,15,245,5,16,4,13
	.byte	'CNTS',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'ECNT',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,15,249,5,3
	.word	44572
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,15,252,5,16,4,13
	.byte	'TIM_EN',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TIM_MODE',0,4
	.word	25705
	.byte	3,28,2,35,0,13
	.byte	'OSM',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'CICTRL',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'TBU0x_SEL',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'GPR0_SEL',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'GPR1_SEL',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'CNTS_SEL',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'DSL',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'ISL',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'ECNT_RESET',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'FLT_EN',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'FLT_CNT_FRQ',0,4
	.word	25705
	.byte	2,13,2,35,0,13
	.byte	'EXT_CAP_EN',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'FLT_MODE_RE',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'FLT_CTR_RE',0,4
	.word	25705
	.byte	1,10,2,35,0,13
	.byte	'FLT_MODE_FE',0,4
	.word	25705
	.byte	1,9,2,35,0,13
	.byte	'FLT_CTR_FE',0,4
	.word	25705
	.byte	1,8,2,35,0,13
	.byte	'CLK_SEL',0,4
	.word	25705
	.byte	3,5,2,35,0,13
	.byte	'FR_ECNT_OFL',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'EGPR0_SEL',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'EGPR1_SEL',0,4
	.word	25705
	.byte	1,2,2,35,0,13
	.byte	'TOCTRL',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,15,150,6,3
	.word	44671
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,15,153,6,16,4,13
	.byte	'ECNT',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,15,157,6,3
	.word	45219
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,15,160,6,16,4,13
	.byte	'EXT_CAP_SRC',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,15,164,6,3
	.word	45325
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,15,167,6,16,4,13
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'TODET_EIRQ_EN',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'reserved_6',0,4
	.word	25705
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,15,176,6,3
	.word	45439
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,15,179,6,16,4,13
	.byte	'FLT_FE',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,15,183,6,3
	.word	45694
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,15,186,6,16,4,13
	.byte	'FLT_RE',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,15,190,6,3
	.word	45806
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,15,193,6,16,4,13
	.byte	'GPR0',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'ECNT',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,15,197,6,3
	.word	45918
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,15,200,6,16,4,13
	.byte	'GPR1',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'ECNT',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,15,204,6,3
	.word	46017
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,15,207,6,16,4,13
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'TODET_IRQ_EN',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'reserved_6',0,4
	.word	25705
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,15,216,6,3
	.word	46116
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,15,219,6,16,4,13
	.byte	'TRG_NEWVAL',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TRG_ECNTOFL',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'TRG_CNTOFL',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'TRG_GPRzOFL',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'TRG_TODET',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'TRG_GLITCHDET',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'reserved_6',0,4
	.word	25705
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,15,228,6,3
	.word	46363
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,15,231,6,16,4,13
	.byte	'IRQ_MODE',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,15,235,6,3
	.word	46602
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,15,238,6,16,4,13
	.byte	'NEWVAL',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'ECNTOFL',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'CNTOFL',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'GPRzOFL',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'TODET',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'GLITCHDET',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'reserved_6',0,4
	.word	25705
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,15,247,6,3
	.word	46719
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,15,250,6,16,4,13
	.byte	'TO_CNT',0,4
	.word	25705
	.byte	8,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,15,254,6,3
	.word	46932
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,15,129,7,16,4,13
	.byte	'TOV',0,4
	.word	25705
	.byte	8,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	20,4,2,35,0,13
	.byte	'TCS',0,4
	.word	25705
	.byte	3,1,2,35,0,13
	.byte	'reserved_31',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,15,135,7,3
	.word	47039
	.byte	7
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,15,138,7,16,4,13
	.byte	'VAL_0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'MODE_0',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'VAL_1',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'MODE_1',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'VAL_2',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'MODE_2',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'VAL_3',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'MODE_3',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'VAL_4',0,4
	.word	25705
	.byte	2,14,2,35,0,13
	.byte	'MODE_4',0,4
	.word	25705
	.byte	2,12,2,35,0,13
	.byte	'VAL_5',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'MODE_5',0,4
	.word	25705
	.byte	2,8,2,35,0,13
	.byte	'VAL_6',0,4
	.word	25705
	.byte	2,6,2,35,0,13
	.byte	'MODE_6',0,4
	.word	25705
	.byte	2,4,2,35,0,13
	.byte	'VAL_7',0,4
	.word	25705
	.byte	2,2,2,35,0,13
	.byte	'MODE_7',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,15,156,7,3
	.word	47181
	.byte	7
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,15,159,7,16,4,13
	.byte	'F_OUT',0,4
	.word	25705
	.byte	8,24,2,35,0,13
	.byte	'F_IN',0,4
	.word	25705
	.byte	8,16,2,35,0,13
	.byte	'TIM_IN',0,4
	.word	25705
	.byte	8,8,2,35,0,13
	.byte	'reserved_24',0,4
	.word	25705
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,15,165,7,3
	.word	47526
	.byte	7
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,15,168,7,16,4,13
	.byte	'RST_CH0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'RST_CH1',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'RST_CH2',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'RST_CH3',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'RST_CH4',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'RST_CH5',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'RST_CH6',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'RST_CH7',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_RST_Bits',0,15,179,7,3
	.word	47667
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,15,182,7,16,4,13
	.byte	'CM0',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,15,186,7,3
	.word	47900
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,15,189,7,16,4,13
	.byte	'CM1',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,15,193,7,3
	.word	48003
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,15,196,7,16,4,13
	.byte	'CN0',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,15,200,7,3
	.word	48106
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,15,203,7,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	11,21,2,35,0,13
	.byte	'SL',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'CLK_SRC_SR',0,4
	.word	25705
	.byte	3,17,2,35,0,13
	.byte	'reserved_15',0,4
	.word	25705
	.byte	5,12,2,35,0,13
	.byte	'RST_CCU0',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'OSM_TRIG',0,4
	.word	25705
	.byte	1,10,2,35,0,13
	.byte	'EXT_TRIG',0,4
	.word	25705
	.byte	1,9,2,35,0,13
	.byte	'EXTTRIGOUT',0,4
	.word	25705
	.byte	1,8,2,35,0,13
	.byte	'TRIGOUT',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'reserved_25',0,4
	.word	25705
	.byte	1,6,2,35,0,13
	.byte	'OSM',0,4
	.word	25705
	.byte	1,5,2,35,0,13
	.byte	'BITREV',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'reserved_28',0,4
	.word	25705
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,15,218,7,3
	.word	48209
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,15,221,7,16,4,13
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,15,226,7,3
	.word	48537
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,15,229,7,16,4,13
	.byte	'TRG_CCU0TC0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TRG_CCU1TC0',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,15,234,7,3
	.word	48680
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,15,237,7,16,4,13
	.byte	'IRQ_MODE',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,15,241,7,3
	.word	48829
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,15,244,7,16,4,13
	.byte	'CCU0TC',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'CCU1TC',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,15,249,7,3
	.word	48946
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,15,252,7,16,4,13
	.byte	'SR0',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,15,128,8,3
	.word	49083
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,15,131,8,16,4,13
	.byte	'SR1',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,15,135,8,3
	.word	49186
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,15,138,8,16,4,13
	.byte	'OL',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,15,142,8,3
	.word	49289
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,15,145,8,16,4,13
	.byte	'ACT_TB',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'TB_TRIG',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'TBU_SEL',0,4
	.word	25705
	.byte	2,5,2,35,0,13
	.byte	'reserved_27',0,4
	.word	25705
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,15,151,8,3
	.word	49392
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,15,154,8,16,4,13
	.byte	'ENDIS_CTRL0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'ENDIS_CTRL1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'ENDIS_CTRL2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'ENDIS_CTRL3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'ENDIS_CTRL4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'ENDIS_CTRL5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'ENDIS_CTRL6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'ENDIS_CTRL7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,15,165,8,3
	.word	49546
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,15,168,8,16,4,13
	.byte	'ENDIS_STAT0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'ENDIS_STAT1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'ENDIS_STAT2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'ENDIS_STAT3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'ENDIS_STAT4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'ENDIS_STAT5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'ENDIS_STAT6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'ENDIS_STAT7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,15,179,8,3
	.word	49836
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,15,182,8,16,4,13
	.byte	'FUPD_CTRL0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'FUPD_CTRL1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'FUPD_CTRL2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'FUPD_CTRL3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'FUPD_CTRL4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'FUPD_CTRL5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'FUPD_CTRL6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'FUPD_CTRL7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'RSTCN0_CH0',0,4
	.word	25705
	.byte	2,14,2,35,0,13
	.byte	'RSTCN0_CH1',0,4
	.word	25705
	.byte	2,12,2,35,0,13
	.byte	'RSTCN0_CH2',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'RSTCN0_CH3',0,4
	.word	25705
	.byte	2,8,2,35,0,13
	.byte	'RSTCN0_CH4',0,4
	.word	25705
	.byte	2,6,2,35,0,13
	.byte	'RSTCN0_CH5',0,4
	.word	25705
	.byte	2,4,2,35,0,13
	.byte	'RSTCN0_CH6',0,4
	.word	25705
	.byte	2,2,2,35,0,13
	.byte	'RSTCN0_CH7',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,15,200,8,3
	.word	50126
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,15,203,8,16,4,13
	.byte	'HOST_TRIG',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	7,24,2,35,0,13
	.byte	'RST_CH0',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'RST_CH1',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'RST_CH2',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'RST_CH3',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'RST_CH4',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'RST_CH5',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'RST_CH6',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'RST_CH7',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'UPEN_CTRL0',0,4
	.word	25705
	.byte	2,14,2,35,0,13
	.byte	'UPEN_CTRL1',0,4
	.word	25705
	.byte	2,12,2,35,0,13
	.byte	'UPEN_CTRL2',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'UPEN_CTRL3',0,4
	.word	25705
	.byte	2,8,2,35,0,13
	.byte	'UPEN_CTRL4',0,4
	.word	25705
	.byte	2,6,2,35,0,13
	.byte	'UPEN_CTRL5',0,4
	.word	25705
	.byte	2,4,2,35,0,13
	.byte	'UPEN_CTRL6',0,4
	.word	25705
	.byte	2,2,2,35,0,13
	.byte	'UPEN_CTRL7',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,15,223,8,3
	.word	50559
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,15,226,8,16,4,13
	.byte	'INT_TRIG0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'INT_TRIG1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'INT_TRIG2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'INT_TRIG3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'INT_TRIG4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'INT_TRIG5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'INT_TRIG6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'INT_TRIG7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,15,237,8,3
	.word	51009
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,15,240,8,16,4,13
	.byte	'OUTEN_CTRL0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'OUTEN_CTRL1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'OUTEN_CTRL2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'OUTEN_CTRL3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'OUTEN_CTRL4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'OUTEN_CTRL5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'OUTEN_CTRL6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'OUTEN_CTRL7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,15,251,8,3
	.word	51279
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,15,254,8,16,4,13
	.byte	'OUTEN_STAT0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'OUTEN_STAT1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'OUTEN_STAT2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'OUTEN_STAT3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'OUTEN_STAT4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'OUTEN_STAT5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'OUTEN_STAT6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'OUTEN_STAT7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,15,137,9,3
	.word	51569
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,15,140,9,16,4,13
	.byte	'ACT_TB',0,4
	.word	25705
	.byte	24,8,2,35,0,13
	.byte	'TB_TRIG',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'TBU_SEL',0,4
	.word	25705
	.byte	2,5,2,35,0,13
	.byte	'reserved_27',0,4
	.word	25705
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,15,146,9,3
	.word	51859
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,15,149,9,16,4,13
	.byte	'ENDIS_CTRL0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'ENDIS_CTRL1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'ENDIS_CTRL2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'ENDIS_CTRL3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'ENDIS_CTRL4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'ENDIS_CTRL5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'ENDIS_CTRL6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'ENDIS_CTRL7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,15,160,9,3
	.word	52013
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,15,163,9,16,4,13
	.byte	'ENDIS_STAT0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'ENDIS_STAT1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'ENDIS_STAT2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'ENDIS_STAT3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'ENDIS_STAT4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'ENDIS_STAT5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'ENDIS_STAT6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'ENDIS_STAT7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,15,174,9,3
	.word	52303
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,15,177,9,16,4,13
	.byte	'FUPD_CTRL0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'FUPD_CTRL1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'FUPD_CTRL2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'FUPD_CTRL3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'FUPD_CTRL4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'FUPD_CTRL5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'FUPD_CTRL6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'FUPD_CTRL7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'RSTCN0_CH0',0,4
	.word	25705
	.byte	2,14,2,35,0,13
	.byte	'RSTCN0_CH1',0,4
	.word	25705
	.byte	2,12,2,35,0,13
	.byte	'RSTCN0_CH2',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'RSTCN0_CH3',0,4
	.word	25705
	.byte	2,8,2,35,0,13
	.byte	'RSTCN0_CH4',0,4
	.word	25705
	.byte	2,6,2,35,0,13
	.byte	'RSTCN0_CH5',0,4
	.word	25705
	.byte	2,4,2,35,0,13
	.byte	'RSTCN0_CH6',0,4
	.word	25705
	.byte	2,2,2,35,0,13
	.byte	'RSTCN0_CH7',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,15,195,9,3
	.word	52593
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,15,198,9,16,4,13
	.byte	'HOST_TRIG',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	7,24,2,35,0,13
	.byte	'RST_CH0',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'RST_CH1',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'RST_CH2',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'RST_CH3',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'RST_CH4',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'RST_CH5',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'RST_CH6',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'RST_CH7',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'UPEN_CTRL0',0,4
	.word	25705
	.byte	2,14,2,35,0,13
	.byte	'UPEN_CTRL1',0,4
	.word	25705
	.byte	2,12,2,35,0,13
	.byte	'UPEN_CTRL2',0,4
	.word	25705
	.byte	2,10,2,35,0,13
	.byte	'UPEN_CTRL3',0,4
	.word	25705
	.byte	2,8,2,35,0,13
	.byte	'UPEN_CTRL4',0,4
	.word	25705
	.byte	2,6,2,35,0,13
	.byte	'UPEN_CTRL5',0,4
	.word	25705
	.byte	2,4,2,35,0,13
	.byte	'UPEN_CTRL6',0,4
	.word	25705
	.byte	2,2,2,35,0,13
	.byte	'UPEN_CTRL7',0,4
	.word	25705
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,15,218,9,3
	.word	53026
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,15,221,9,16,4,13
	.byte	'INT_TRIG0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'INT_TRIG1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'INT_TRIG2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'INT_TRIG3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'INT_TRIG4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'INT_TRIG5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'INT_TRIG6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'INT_TRIG7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,15,232,9,3
	.word	53476
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,15,235,9,16,4,13
	.byte	'OUTEN_CTRL0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'OUTEN_CTRL1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'OUTEN_CTRL2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'OUTEN_CTRL3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'OUTEN_CTRL4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'OUTEN_CTRL5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'OUTEN_CTRL6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'OUTEN_CTRL7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,15,246,9,3
	.word	53746
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,15,249,9,16,4,13
	.byte	'OUTEN_STAT0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'OUTEN_STAT1',0,4
	.word	25705
	.byte	2,28,2,35,0,13
	.byte	'OUTEN_STAT2',0,4
	.word	25705
	.byte	2,26,2,35,0,13
	.byte	'OUTEN_STAT3',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'OUTEN_STAT4',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'OUTEN_STAT5',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'OUTEN_STAT6',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'OUTEN_STAT7',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,15,132,10,3
	.word	54036
	.byte	21,15,140,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	32398
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN0',0,15,145,10,3
	.word	54326
	.byte	21,15,148,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	32955
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN1',0,15,153,10,3
	.word	54390
	.byte	21,15,156,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	33032
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,15,161,10,3
	.word	54454
	.byte	21,15,164,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	33186
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,15,169,10,3
	.word	54524
	.byte	21,15,172,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	33340
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,15,177,10,3
	.word	54594
	.byte	21,15,180,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	33468
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_MODE',0,15,185,10,3
	.word	54664
	.byte	21,15,188,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	33775
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,15,193,10,3
	.word	54733
	.byte	21,15,196,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	33977
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,15,201,10,3
	.word	54802
	.byte	21,15,204,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	34090
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CLC',0,15,209,10,3
	.word	54871
	.byte	21,15,212,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	34233
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,15,217,10,3
	.word	54932
	.byte	21,15,220,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	34350
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,15,225,10,3
	.word	55005
	.byte	21,15,228,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	34485
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,15,233,10,3
	.word	55077
	.byte	21,15,236,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	34620
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_EN',0,15,241,10,3
	.word	55149
	.byte	21,15,244,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	34940
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,15,249,10,3
	.word	55217
	.byte	21,15,252,10,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	35052
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,15,129,11,3
	.word	55287
	.byte	21,15,132,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	35164
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,15,137,11,3
	.word	55357
	.byte	21,15,140,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	35280
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,15,145,11,3
	.word	55429
	.byte	21,15,148,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	35392
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,15,153,11,3
	.word	55499
	.byte	21,15,156,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	35504
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CTRL',0,15,161,11,3
	.word	55569
	.byte	21,15,164,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	35657
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,15,169,11,3
	.word	55631
	.byte	21,15,172,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	36169
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,15,177,11,3
	.word	55701
	.byte	21,15,180,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	36790
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,15,185,11,3
	.word	55771
	.byte	21,15,188,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	37513
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CTRL',0,15,193,11,3
	.word	55844
	.byte	21,15,196,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	37657
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_DTV_CH',0,15,201,11,3
	.word	55910
	.byte	21,15,204,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	37806
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,15,209,11,3
	.word	55978
	.byte	21,15,212,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	38021
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_EIRQ_EN',0,15,217,11,3
	.word	56047
	.byte	21,15,220,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	38225
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_HW_CONF',0,15,225,11,3
	.word	56112
	.byte	21,15,228,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	38582
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_0',0,15,233,11,3
	.word	56177
	.byte	21,15,236,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	38710
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_2',0,15,241,11,3
	.word	56245
	.byte	21,15,244,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	38989
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_6',0,15,249,11,3
	.word	56313
	.byte	21,15,252,11,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	39834
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,15,129,12,3
	.word	56381
	.byte	21,15,132,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	40127
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,15,137,12,3
	.word	56452
	.byte	21,15,140,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	40281
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,15,145,12,3
	.word	56522
	.byte	21,15,148,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	40451
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,15,153,12,3
	.word	56599
	.byte	21,15,156,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	40792
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,15,161,12,3
	.word	56674
	.byte	21,15,164,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	41017
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_EN',0,15,169,12,3
	.word	56750
	.byte	21,15,172,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	41215
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_FORCINT',0,15,177,12,3
	.word	56814
	.byte	21,15,180,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	41411
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_MODE',0,15,185,12,3
	.word	56883
	.byte	21,15,188,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	41514
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,15,193,12,3
	.word	56949
	.byte	21,15,196,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	41692
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRST0',0,15,201,12,3
	.word	57017
	.byte	21,15,204,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	41803
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRST1',0,15,209,12,3
	.word	57080
	.byte	21,15,212,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	41895
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRSTCLR',0,15,217,12,3
	.word	57143
	.byte	21,15,220,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	41991
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OCS',0,15,225,12,3
	.word	57208
	.byte	21,15,228,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	42137
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ODA',0,15,233,12,3
	.word	57269
	.byte	21,15,236,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	42243
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU0T',0,15,241,12,3
	.word	57330
	.byte	21,15,244,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	42374
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU1T',0,15,249,12,3
	.word	57394
	.byte	21,15,252,12,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	42505
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU2T',0,15,129,13,3
	.word	57458
	.byte	21,15,132,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	42636
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTSC0',0,15,137,13,3
	.word	57522
	.byte	21,15,140,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	42918
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTSS',0,15,145,13,3
	.word	57585
	.byte	21,15,148,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	43090
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_REV',0,15,153,13,3
	.word	57647
	.byte	21,15,156,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	43268
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_RST',0,15,161,13,3
	.word	57708
	.byte	21,15,164,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	43356
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,15,169,13,3
	.word	57769
	.byte	21,15,172,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	43464
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,15,177,13,3
	.word	57839
	.byte	21,15,180,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	43596
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,15,185,13,3
	.word	57909
	.byte	21,15,188,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	43704
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,15,193,13,3
	.word	57979
	.byte	21,15,196,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	43836
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,15,201,13,3
	.word	58049
	.byte	21,15,204,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	43944
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,15,209,13,3
	.word	58119
	.byte	21,15,212,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	44076
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CHEN',0,15,217,13,3
	.word	58189
	.byte	21,15,220,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	44222
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,15,225,13,3
	.word	58255
	.byte	21,15,228,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	44469
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNT',0,15,233,13,3
	.word	58327
	.byte	21,15,236,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	44572
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,15,241,13,3
	.word	58395
	.byte	21,15,244,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	44671
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,15,249,13,3
	.word	58464
	.byte	21,15,252,13,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	45219
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,15,129,14,3
	.word	58533
	.byte	21,15,132,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	45325
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,15,137,14,3
	.word	58602
	.byte	21,15,140,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	45439
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,15,145,14,3
	.word	58672
	.byte	21,15,148,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	45694
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,15,153,14,3
	.word	58744
	.byte	21,15,156,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	45806
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,15,161,14,3
	.word	58815
	.byte	21,15,164,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	45918
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,15,169,14,3
	.word	58886
	.byte	21,15,172,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	46017
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,15,177,14,3
	.word	58955
	.byte	21,15,180,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	46116
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,15,185,14,3
	.word	59024
	.byte	21,15,188,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	46363
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,15,193,14,3
	.word	59095
	.byte	21,15,196,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	46602
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,15,201,14,3
	.word	59171
	.byte	21,15,204,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	46719
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,15,209,14,3
	.word	59244
	.byte	21,15,212,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	46932
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,15,217,14,3
	.word	59319
	.byte	21,15,220,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	47039
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,15,225,14,3
	.word	59388
	.byte	21,15,228,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	47181
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_IN_SRC',0,15,233,14,3
	.word	59457
	.byte	21,15,236,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	47526
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_INP_VAL',0,15,241,14,3
	.word	59525
	.byte	21,15,244,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	47667
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_RST',0,15,249,14,3
	.word	59594
	.byte	21,15,252,14,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	47900
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM0',0,15,129,15,3
	.word	59659
	.byte	21,15,132,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	48003
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM1',0,15,137,15,3
	.word	59727
	.byte	21,15,140,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	48106
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CN0',0,15,145,15,3
	.word	59795
	.byte	21,15,148,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	48209
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,15,153,15,3
	.word	59863
	.byte	21,15,156,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	48537
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,15,161,15,3
	.word	59932
	.byte	21,15,164,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	48680
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,15,169,15,3
	.word	60003
	.byte	21,15,172,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	48829
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,15,177,15,3
	.word	60079
	.byte	21,15,180,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	48946
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,15,185,15,3
	.word	60152
	.byte	21,15,188,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	49083
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR0',0,15,193,15,3
	.word	60227
	.byte	21,15,196,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	49186
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR1',0,15,201,15,3
	.word	60295
	.byte	21,15,204,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	49289
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_STAT',0,15,209,15,3
	.word	60363
	.byte	21,15,212,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	49392
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,15,217,15,3
	.word	60432
	.byte	21,15,220,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	49546
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,15,225,15,3
	.word	60505
	.byte	21,15,228,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	49836
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,15,233,15,3
	.word	60582
	.byte	21,15,236,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	50126
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,15,241,15,3
	.word	60659
	.byte	21,15,244,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	50559
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,15,249,15,3
	.word	60735
	.byte	21,15,252,15,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	51009
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,15,129,16,3
	.word	60810
	.byte	21,15,132,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	51279
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,15,137,16,3
	.word	60885
	.byte	21,15,140,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	51569
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,15,145,16,3
	.word	60962
	.byte	21,15,148,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	51859
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,15,153,16,3
	.word	61039
	.byte	21,15,156,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	52013
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,15,161,16,3
	.word	61112
	.byte	21,15,164,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	52303
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,15,169,16,3
	.word	61189
	.byte	21,15,172,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	52593
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,15,177,16,3
	.word	61266
	.byte	21,15,180,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	53026
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,15,185,16,3
	.word	61342
	.byte	21,15,188,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	53476
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,15,193,16,3
	.word	61417
	.byte	21,15,196,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	53746
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,15,201,16,3
	.word	61492
	.byte	21,15,204,16,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	54036
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,15,209,16,3
	.word	61569
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,15,220,16,25,4,9
	.byte	'CTRL',0,4
	.word	54932
	.byte	2,35,0,0,22
	.word	61646
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK0_5',0,15,223,16,3
	.word	61687
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_6',0,15,226,16,25,4,9
	.byte	'CTRL',0,4
	.word	55005
	.byte	2,35,0,0,22
	.word	61720
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK_6',0,15,229,16,3
	.word	61760
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_7',0,15,232,16,25,4,9
	.byte	'CTRL',0,4
	.word	55077
	.byte	2,35,0,0,22
	.word	61792
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK_7',0,15,235,16,3
	.word	61832
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK',0,15,238,16,25,8,9
	.byte	'NUM',0,4
	.word	55287
	.byte	2,35,0,9
	.byte	'DEN',0,4
	.word	55217
	.byte	2,35,4,0,22
	.word	61864
	.byte	4
	.byte	'Ifx_GTM_CMU_ECLK',0,15,242,16,3
	.word	61915
	.byte	7
	.byte	'_Ifx_GTM_CMU_FXCLK',0,15,245,16,25,4,9
	.byte	'CTRL',0,4
	.word	55357
	.byte	2,35,0,0,22
	.word	61946
	.byte	4
	.byte	'Ifx_GTM_CMU_FXCLK',0,15,248,16,3
	.word	61986
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,15,251,16,25,4,9
	.byte	'OUTSEL',0,4
	.word	56522
	.byte	2,35,0,0,22
	.word	62018
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,15,254,16,3
	.word	62063
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_T',0,15,129,17,25,32,10,32
	.word	56599
	.byte	11,7,0,9
	.byte	'OUTSEL',0,32
	.word	62124
	.byte	2,35,0,0,22
	.word	62098
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_T',0,15,132,17,3
	.word	62150
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,15,135,17,25,32,9
	.byte	'INSEL',0,4
	.word	56674
	.byte	2,35,0,10,28
	.word	234
	.byte	11,27,0,9
	.byte	'reserved_4',0,28
	.word	62226
	.byte	2,35,4,0,22
	.word	62183
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,15,139,17,3
	.word	62256
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH',0,15,142,17,25,116,9
	.byte	'GPR0',0,4
	.word	58886
	.byte	2,35,0,9
	.byte	'GPR1',0,4
	.word	58955
	.byte	2,35,4,9
	.byte	'CNT',0,4
	.word	58327
	.byte	2,35,8,9
	.byte	'ECNT',0,4
	.word	58533
	.byte	2,35,12,9
	.byte	'CNTS',0,4
	.word	58395
	.byte	2,35,16,9
	.byte	'TDUC',0,4
	.word	59319
	.byte	2,35,20,9
	.byte	'TDUV',0,4
	.word	59388
	.byte	2,35,24,9
	.byte	'FLT_RE',0,4
	.word	58815
	.byte	2,35,28,9
	.byte	'FLT_FE',0,4
	.word	58744
	.byte	2,35,32,9
	.byte	'CTRL',0,4
	.word	58464
	.byte	2,35,36,9
	.byte	'ECTRL',0,4
	.word	58602
	.byte	2,35,40,9
	.byte	'IRQ_NOTIFY',0,4
	.word	59244
	.byte	2,35,44,9
	.byte	'IRQ_EN',0,4
	.word	59024
	.byte	2,35,48,9
	.byte	'IRQ_FORCINT',0,4
	.word	59095
	.byte	2,35,52,9
	.byte	'IRQ_MODE',0,4
	.word	59171
	.byte	2,35,56,9
	.byte	'EIRQ_EN',0,4
	.word	58672
	.byte	2,35,60,10,52
	.word	234
	.byte	11,51,0,9
	.byte	'reserved_40',0,52
	.word	62563
	.byte	2,35,64,0,22
	.word	62291
	.byte	4
	.byte	'Ifx_GTM_TIM_CH',0,15,161,17,3
	.word	62594
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH',0,15,164,17,25,48,9
	.byte	'CTRL',0,4
	.word	59863
	.byte	2,35,0,9
	.byte	'SR0',0,4
	.word	60227
	.byte	2,35,4,9
	.byte	'SR1',0,4
	.word	60295
	.byte	2,35,8,9
	.byte	'CM0',0,4
	.word	59659
	.byte	2,35,12,9
	.byte	'CM1',0,4
	.word	59727
	.byte	2,35,16,9
	.byte	'CN0',0,4
	.word	59795
	.byte	2,35,20,9
	.byte	'STAT',0,4
	.word	60363
	.byte	2,35,24,9
	.byte	'IRQ_NOTIFY',0,4
	.word	60152
	.byte	2,35,28,9
	.byte	'IRQ_EN',0,4
	.word	59932
	.byte	2,35,32,9
	.byte	'IRQ_FORCINT',0,4
	.word	60003
	.byte	2,35,36,9
	.byte	'IRQ_MODE',0,4
	.word	60079
	.byte	2,35,40,10,4
	.word	234
	.byte	11,3,0,9
	.byte	'reserved_2C',0,4
	.word	62813
	.byte	2,35,44,0,22
	.word	62623
	.byte	4
	.byte	'Ifx_GTM_TOM_CH',0,15,178,17,3
	.word	62844
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE',0,15,191,17,25,12,9
	.byte	'MODE',0,4
	.word	54664
	.byte	2,35,0,9
	.byte	'PTR1',0,4
	.word	54733
	.byte	2,35,4,9
	.byte	'PTR2',0,4
	.word	54802
	.byte	2,35,8,0,22
	.word	62873
	.byte	4
	.byte	'Ifx_GTM_BRIDGE',0,15,196,17,3
	.word	62938
	.byte	7
	.byte	'_Ifx_GTM_CMU',0,15,199,17,25,72,9
	.byte	'CLK_EN',0,4
	.word	55149
	.byte	2,35,0,9
	.byte	'GCLK_NUM',0,4
	.word	55499
	.byte	2,35,4,9
	.byte	'GCLK_DEN',0,4
	.word	55429
	.byte	2,35,8,10,24
	.word	61646
	.byte	11,5,0,22
	.word	63038
	.byte	9
	.byte	'CLK0_5',0,24
	.word	63047
	.byte	2,35,12,22
	.word	61720
	.byte	9
	.byte	'CLK_6',0,4
	.word	63068
	.byte	2,35,36,22
	.word	61792
	.byte	9
	.byte	'CLK_7',0,4
	.word	63088
	.byte	2,35,40,10,24
	.word	61864
	.byte	11,2,0,22
	.word	63108
	.byte	9
	.byte	'ECLK',0,24
	.word	63117
	.byte	2,35,44,22
	.word	61946
	.byte	9
	.byte	'FXCLK',0,4
	.word	63136
	.byte	2,35,68,0,22
	.word	62967
	.byte	4
	.byte	'Ifx_GTM_CMU',0,15,209,17,3
	.word	63157
	.byte	7
	.byte	'_Ifx_GTM_DTM',0,15,212,17,25,36,9
	.byte	'CTRL',0,4
	.word	55844
	.byte	2,35,0,9
	.byte	'CH_CTRL1',0,4
	.word	55631
	.byte	2,35,4,9
	.byte	'CH_CTRL2',0,4
	.word	55701
	.byte	2,35,8,9
	.byte	'CH_CTRL2_SR',0,4
	.word	55771
	.byte	2,35,12,9
	.byte	'PS_CTRL',0,4
	.word	55978
	.byte	2,35,16,10,16
	.word	55910
	.byte	11,3,0,9
	.byte	'DTV_CH',0,16
	.word	63290
	.byte	2,35,20,0,22
	.word	63183
	.byte	4
	.byte	'Ifx_GTM_DTM',0,15,220,17,3
	.word	63316
	.byte	7
	.byte	'_Ifx_GTM_ICM',0,15,223,17,25,60,9
	.byte	'IRQG_0',0,4
	.word	56177
	.byte	2,35,0,9
	.byte	'reserved_4',0,4
	.word	62813
	.byte	2,35,4,9
	.byte	'IRQG_2',0,4
	.word	56245
	.byte	2,35,8,10,12
	.word	234
	.byte	11,11,0,9
	.byte	'reserved_C',0,12
	.word	63413
	.byte	2,35,12,9
	.byte	'IRQG_6',0,4
	.word	56313
	.byte	2,35,24,10,20
	.word	234
	.byte	11,19,0,9
	.byte	'reserved_1C',0,20
	.word	63458
	.byte	2,35,28,9
	.byte	'IRQG_MEI',0,4
	.word	56452
	.byte	2,35,48,9
	.byte	'reserved_34',0,4
	.word	62813
	.byte	2,35,52,9
	.byte	'IRQG_CEI1',0,4
	.word	56381
	.byte	2,35,56,0,22
	.word	63342
	.byte	4
	.byte	'Ifx_GTM_ICM',0,15,234,17,3
	.word	63547
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL',0,15,237,17,25,148,1,10,32
	.word	62183
	.byte	11,0,0,22
	.word	63598
	.byte	9
	.byte	'TIM',0,32
	.word	63607
	.byte	2,35,0,22
	.word	62098
	.byte	9
	.byte	'T',0,32
	.word	63625
	.byte	2,35,32,10,80
	.word	234
	.byte	11,79,0,9
	.byte	'reserved_40',0,80
	.word	63641
	.byte	2,35,64,22
	.word	62018
	.byte	9
	.byte	'CAN',0,4
	.word	63671
	.byte	3,35,144,1,0,22
	.word	63573
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL',0,15,243,17,3
	.word	63691
	.byte	7
	.byte	'_Ifx_GTM_TBU',0,15,246,17,25,28,9
	.byte	'CHEN',0,4
	.word	58189
	.byte	2,35,0,9
	.byte	'CH0_CTRL',0,4
	.word	57839
	.byte	2,35,4,9
	.byte	'CH0_BASE',0,4
	.word	57769
	.byte	2,35,8,9
	.byte	'CH1_CTRL',0,4
	.word	57979
	.byte	2,35,12,9
	.byte	'CH1_BASE',0,4
	.word	57909
	.byte	2,35,16,9
	.byte	'CH2_CTRL',0,4
	.word	58119
	.byte	2,35,20,9
	.byte	'CH2_BASE',0,4
	.word	58049
	.byte	2,35,24,0,22
	.word	63722
	.byte	4
	.byte	'Ifx_GTM_TBU',0,15,255,17,3
	.word	63864
	.byte	7
	.byte	'_Ifx_GTM_TIM',0,15,130,18,25,128,8,22
	.word	62291
	.byte	9
	.byte	'CH0',0,116
	.word	63910
	.byte	2,35,0,9
	.byte	'INP_VAL',0,4
	.word	59525
	.byte	2,35,116,9
	.byte	'IN_SRC',0,4
	.word	59457
	.byte	2,35,120,9
	.byte	'RST',0,4
	.word	59594
	.byte	2,35,124,22
	.word	62291
	.byte	9
	.byte	'CH1',0,116
	.word	63974
	.byte	3,35,128,1,9
	.byte	'reserved_F4',0,12
	.word	63413
	.byte	3,35,244,1,22
	.word	62291
	.byte	9
	.byte	'CH2',0,116
	.word	64015
	.byte	3,35,128,2,9
	.byte	'reserved_174',0,12
	.word	63413
	.byte	3,35,244,2,22
	.word	62291
	.byte	9
	.byte	'CH3',0,116
	.word	64057
	.byte	3,35,128,3,9
	.byte	'reserved_1F4',0,12
	.word	63413
	.byte	3,35,244,3,22
	.word	62291
	.byte	9
	.byte	'CH4',0,116
	.word	64099
	.byte	3,35,128,4,9
	.byte	'reserved_274',0,12
	.word	63413
	.byte	3,35,244,4,22
	.word	62291
	.byte	9
	.byte	'CH5',0,116
	.word	64141
	.byte	3,35,128,5,9
	.byte	'reserved_2F4',0,12
	.word	63413
	.byte	3,35,244,5,22
	.word	62291
	.byte	9
	.byte	'CH6',0,116
	.word	64183
	.byte	3,35,128,6,9
	.byte	'reserved_374',0,12
	.word	63413
	.byte	3,35,244,6,22
	.word	62291
	.byte	9
	.byte	'CH7',0,116
	.word	64225
	.byte	3,35,128,7,9
	.byte	'reserved_3F4',0,12
	.word	63413
	.byte	3,35,244,7,0,22
	.word	63890
	.byte	4
	.byte	'Ifx_GTM_TIM',0,15,150,18,3
	.word	64268
	.byte	7
	.byte	'_Ifx_GTM_TOM',0,15,153,18,25,128,16,22
	.word	62623
	.byte	9
	.byte	'CH0',0,48
	.word	64314
	.byte	2,35,0,9
	.byte	'TGC0_GLB_CTRL',0,4
	.word	60735
	.byte	2,35,48,9
	.byte	'TGC0_ACT_TB',0,4
	.word	60432
	.byte	2,35,52,9
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	60659
	.byte	2,35,56,9
	.byte	'TGC0_INT_TRIG',0,4
	.word	60810
	.byte	2,35,60,22
	.word	62623
	.byte	9
	.byte	'CH1',0,48
	.word	64423
	.byte	2,35,64,9
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	60505
	.byte	2,35,112,9
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	60582
	.byte	2,35,116,9
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	60885
	.byte	2,35,120,9
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	60962
	.byte	2,35,124,22
	.word	62623
	.byte	9
	.byte	'CH2',0,48
	.word	64541
	.byte	3,35,128,1,10,16
	.word	234
	.byte	11,15,0,9
	.byte	'reserved_B0',0,16
	.word	64560
	.byte	3,35,176,1,22
	.word	62623
	.byte	9
	.byte	'CH3',0,48
	.word	64591
	.byte	3,35,192,1,9
	.byte	'reserved_F0',0,16
	.word	64560
	.byte	3,35,240,1,22
	.word	62623
	.byte	9
	.byte	'CH4',0,48
	.word	64632
	.byte	3,35,128,2,9
	.byte	'reserved_130',0,16
	.word	64560
	.byte	3,35,176,2,22
	.word	62623
	.byte	9
	.byte	'CH5',0,48
	.word	64674
	.byte	3,35,192,2,9
	.byte	'reserved_170',0,16
	.word	64560
	.byte	3,35,240,2,22
	.word	62623
	.byte	9
	.byte	'CH6',0,48
	.word	64716
	.byte	3,35,128,3,9
	.byte	'reserved_1B0',0,16
	.word	64560
	.byte	3,35,176,3,22
	.word	62623
	.byte	9
	.byte	'CH7',0,48
	.word	64758
	.byte	3,35,192,3,9
	.byte	'reserved_1F0',0,16
	.word	64560
	.byte	3,35,240,3,22
	.word	62623
	.byte	9
	.byte	'CH8',0,48
	.word	64800
	.byte	3,35,128,4,9
	.byte	'TGC1_GLB_CTRL',0,4
	.word	61342
	.byte	3,35,176,4,9
	.byte	'TGC1_ACT_TB',0,4
	.word	61039
	.byte	3,35,180,4,9
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	61266
	.byte	3,35,184,4,9
	.byte	'TGC1_INT_TRIG',0,4
	.word	61417
	.byte	3,35,188,4,22
	.word	62623
	.byte	9
	.byte	'CH9',0,48
	.word	64914
	.byte	3,35,192,4,9
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	61112
	.byte	3,35,240,4,9
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	61189
	.byte	3,35,244,4,9
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	61492
	.byte	3,35,248,4,9
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	61569
	.byte	3,35,252,4,22
	.word	62623
	.byte	9
	.byte	'CH10',0,48
	.word	65037
	.byte	3,35,128,5,9
	.byte	'reserved_2B0',0,16
	.word	64560
	.byte	3,35,176,5,22
	.word	62623
	.byte	9
	.byte	'CH11',0,48
	.word	65080
	.byte	3,35,192,5,9
	.byte	'reserved_2F0',0,16
	.word	64560
	.byte	3,35,240,5,22
	.word	62623
	.byte	9
	.byte	'CH12',0,48
	.word	65123
	.byte	3,35,128,6,9
	.byte	'reserved_330',0,16
	.word	64560
	.byte	3,35,176,6,22
	.word	62623
	.byte	9
	.byte	'CH13',0,48
	.word	65166
	.byte	3,35,192,6,9
	.byte	'reserved_370',0,16
	.word	64560
	.byte	3,35,240,6,22
	.word	62623
	.byte	9
	.byte	'CH14',0,48
	.word	65209
	.byte	3,35,128,7,9
	.byte	'reserved_3B0',0,16
	.word	64560
	.byte	3,35,176,7,22
	.word	62623
	.byte	9
	.byte	'CH15',0,48
	.word	65252
	.byte	3,35,192,7,10,144,8
	.word	234
	.byte	11,143,8,0,9
	.byte	'reserved_3F0',0,144,8
	.word	65272
	.byte	3,35,240,7,0,22
	.word	64294
	.byte	4
	.byte	'Ifx_GTM_TOM',0,15,199,18,3
	.word	65308
	.byte	12,5,130,4,20,64,9
	.byte	'CTRL',0,4
	.word	59863
	.byte	2,35,0,9
	.byte	'SR0',0,4
	.word	60227
	.byte	2,35,4,9
	.byte	'SR1',0,4
	.word	60295
	.byte	2,35,8,9
	.byte	'CM0',0,4
	.word	59659
	.byte	2,35,12,9
	.byte	'CM1',0,4
	.word	59727
	.byte	2,35,16,9
	.byte	'CN0',0,4
	.word	59795
	.byte	2,35,20,9
	.byte	'STAT',0,4
	.word	60363
	.byte	2,35,24,9
	.byte	'IRQ_NOTIFY',0,4
	.word	60152
	.byte	2,35,28,9
	.byte	'IRQ_EN',0,4
	.word	59932
	.byte	2,35,32,9
	.byte	'IRQ_FORCINT',0,4
	.word	60003
	.byte	2,35,36,9
	.byte	'IRQ_MODE',0,4
	.word	60079
	.byte	2,35,40,10,20
	.word	234
	.byte	11,19,0,9
	.byte	'reserved_2C',0,20
	.word	65508
	.byte	2,35,44,0,22
	.word	65334
	.byte	4
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,5,155,4,4
	.word	65539
	.byte	12,5,157,4,20,128,4,9
	.byte	'GLB_CTRL',0,4
	.word	60735
	.byte	2,35,0,9
	.byte	'ACT_TB',0,4
	.word	60432
	.byte	2,35,4,9
	.byte	'FUPD_CTRL',0,4
	.word	60659
	.byte	2,35,8,9
	.byte	'INT_TRIG',0,4
	.word	60810
	.byte	2,35,12,10,48
	.word	234
	.byte	11,47,0,9
	.byte	'reserved_tgc0',0,48
	.word	65651
	.byte	2,35,16,9
	.byte	'ENDIS_CTRL',0,4
	.word	60505
	.byte	2,35,64,9
	.byte	'ENDIS_STAT',0,4
	.word	60582
	.byte	2,35,68,9
	.byte	'OUTEN_CTRL',0,4
	.word	60885
	.byte	2,35,72,9
	.byte	'OUTEN_STAT',0,4
	.word	60962
	.byte	2,35,76,10,176,3
	.word	234
	.byte	11,175,3,0,9
	.byte	'reserved_tgc1',0,176,3
	.word	65763
	.byte	2,35,80,0,22
	.word	65573
	.byte	4
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,5,177,4,5
	.word	65799
	.byte	12,5,179,4,20,128,16,9
	.byte	'reserved_tom0',0,48
	.word	65651
	.byte	2,35,0,10,128,8
	.word	65573
	.byte	11,1,0,22
	.word	65864
	.byte	9
	.byte	'TGC',0,128,8
	.word	65874
	.byte	2,35,48,10,208,7
	.word	234
	.byte	11,207,7,0,9
	.byte	'reserved_tgc2',0,208,7
	.word	65893
	.byte	3,35,176,8,0,22
	.word	65834
	.byte	4
	.byte	'Ifx_GTM_TOM_TGCx',0,5,184,4,5
	.word	65930
	.byte	12,5,187,4,20,128,16,10,128,8
	.word	65334
	.byte	11,15,0,22
	.word	65968
	.byte	9
	.byte	'CH',0,128,8
	.word	65978
	.byte	2,35,0,10,128,8
	.word	234
	.byte	11,255,7,0,9
	.byte	'reserved_tom1',0,128,8
	.word	65996
	.byte	3,35,128,8,0,22
	.word	65961
	.byte	4
	.byte	'Ifx_GTM_TOM_CHx',0,5,191,4,5
	.word	66033
	.byte	12,5,212,4,20,128,1,9
	.byte	'CH_GPR0',0,4
	.word	58886
	.byte	2,35,0,9
	.byte	'CH_GPR1',0,4
	.word	58955
	.byte	2,35,4,9
	.byte	'CH_CNT',0,4
	.word	58327
	.byte	2,35,8,9
	.byte	'CH_ECNT',0,4
	.word	58533
	.byte	2,35,12,9
	.byte	'CH_CNTS',0,4
	.word	58395
	.byte	2,35,16,9
	.byte	'CH_TDUC',0,4
	.word	59319
	.byte	2,35,20,9
	.byte	'CH_TDUV',0,4
	.word	59388
	.byte	2,35,24,9
	.byte	'CH_FLT_RE',0,4
	.word	58815
	.byte	2,35,28,9
	.byte	'CH_FLT_FE',0,4
	.word	58744
	.byte	2,35,32,9
	.byte	'CH_CTRL',0,4
	.word	58464
	.byte	2,35,36,9
	.byte	'CH_ECTRL',0,4
	.word	58602
	.byte	2,35,40,9
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	59244
	.byte	2,35,44,9
	.byte	'CH_IRQ_EN',0,4
	.word	59024
	.byte	2,35,48,9
	.byte	'CH_IRQ_FORCINT',0,4
	.word	59095
	.byte	2,35,52,9
	.byte	'CH_IRQ_MODE',0,4
	.word	59171
	.byte	2,35,56,9
	.byte	'CH_EIRQ_EN',0,4
	.word	58672
	.byte	2,35,60,10,64
	.word	234
	.byte	11,63,0,9
	.byte	'reserved_40',0,64
	.word	66368
	.byte	2,35,64,0,22
	.word	66063
	.byte	4
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,5,248,4,4
	.word	66399
	.byte	12,5,250,4,20,8,9
	.byte	'IN_SRC',0,4
	.word	59457
	.byte	2,35,0,9
	.byte	'RST',0,4
	.word	59594
	.byte	2,35,4,0,22
	.word	66433
	.byte	4
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,5,255,4,4
	.word	66469
	.byte	12,5,129,5,21,128,16,10,128,8
	.word	66063
	.byte	11,7,0,22
	.word	66520
	.byte	9
	.byte	'CH',0,128,8
	.word	66530
	.byte	2,35,0,9
	.byte	'reserved_tim1',0,128,8
	.word	65996
	.byte	3,35,128,8,0,22
	.word	66513
	.byte	4
	.byte	'Ifx_GTM_TIM_CHx',0,5,133,5,4
	.word	66574
	.byte	12,5,135,5,20,128,16,10,120
	.word	234
	.byte	11,119,0,9
	.byte	'reserved_tim2',0,120
	.word	66611
	.byte	2,35,0,22
	.word	66433
	.byte	9
	.byte	'IN_SRC_RESET',0,8
	.word	66643
	.byte	2,35,120,10,128,15
	.word	234
	.byte	11,255,14,0,9
	.byte	'reserved_tim3',0,128,15
	.word	66670
	.byte	3,35,128,1,0,22
	.word	66604
	.byte	4
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,5,140,5,4
	.word	66707
	.byte	20,5,174,5,11,1,17
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,17
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,17
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,17
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,17
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,17
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,17
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,17
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,4
	.byte	'Gtm_ConfigurableClockType',0,5,184,5,4
	.word	66745
	.byte	20,5,188,5,11,1,17
	.byte	'GTM_LOW',0,0,17
	.byte	'GTM_HIGH',0,1,0,4
	.byte	'Gtm_OutputLevelType',0,5,192,5,4
	.word	66979
	.byte	20,5,195,5,11,1,17
	.byte	'TOM_GLB_CTRL',0,0,17
	.byte	'TOM_ACT_TB',0,1,17
	.byte	'TOM_FUPD_CTRL',0,2,17
	.byte	'TOM_INT_TRIG',0,3,17
	.byte	'TOM_RESERVED_0',0,4,17
	.byte	'TOM_RESERVED_1',0,5,17
	.byte	'TOM_RESERVED_2',0,6,17
	.byte	'TOM_RESERVED_3',0,7,17
	.byte	'TOM_RESERVED_4',0,8,17
	.byte	'TOM_RESERVED_5',0,9,17
	.byte	'TOM_RESERVED_6',0,10,17
	.byte	'TOM_RESERVED_7',0,11,17
	.byte	'TOM_RESERVED_8',0,12,17
	.byte	'TOM_RESERVED_9',0,13,17
	.byte	'TOM_RESERVED_10',0,14,17
	.byte	'TOM_RESERVED_11',0,15,17
	.byte	'TOM_ENDIS_CTRL',0,16,17
	.byte	'TOM_ENDIS_STAT',0,17,17
	.byte	'TOM_OUTEN_CTRL',0,18,17
	.byte	'TOM_OUTEN_STAT',0,19,0,4
	.byte	'Gtm_TomTimerRegistersType',0,5,217,5,4
	.word	67036
	.byte	12,5,221,5,11,8,9
	.byte	'FltRisingEdge',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'FltFallingEdge',0,4
	.word	302
	.byte	2,35,4,0,4
	.byte	'Gtm_TimFilterType',0,5,225,5,4
	.word	67411
	.byte	4
	.byte	'Gtm_TbuChCtrlType',0,5,230,5,32
	.word	57839
	.byte	4
	.byte	'Gtm_TbuChBaseType',0,5,231,5,32
	.word	57769
	.byte	12,5,233,5,11,8,9
	.byte	'CH_CTRL',0,4
	.word	57839
	.byte	2,35,0,9
	.byte	'CH_BASE',0,4
	.word	57769
	.byte	2,35,4,0,4
	.byte	'Gtm_TbuChType',0,5,237,5,4
	.word	67546
	.byte	4
	.byte	'Gtm_PortConfigType',0,5,253,5,2
	.word	1295
	.byte	4
	.byte	'Gtm_TimFltType',0,5,134,6,2
	.word	1683
	.byte	4
	.byte	'Gtm_TimConfigType',0,5,151,6,4
	.word	1576
	.byte	4
	.byte	'Gtm_ModUsageConfigType',0,5,163,6,4
	.word	2540
	.byte	4
	.byte	'Gtm_TomTgcConfigGroupType',0,5,185,6,2
	.word	1955
	.byte	4
	.byte	'Gtm_TomTgcConfigType',0,5,196,6,2
	.word	1905
	.byte	4
	.byte	'Gtm_TomChannelConfigType',0,5,207,6,2
	.word	2317
	.byte	4
	.byte	'Gtm_TomConfigType',0,5,219,6,2
	.word	2243
	.byte	4
	.byte	'Gtm_ExtClkType',0,5,227,6,2
	.word	1183
	.byte	4
	.byte	'Gtm_ClockSettingType',0,5,236,6,2
	.word	1105
	.byte	4
	.byte	'Gtm_GeneralConfigType',0,5,245,6,2
	.word	2649
	.byte	4
	.byte	'Gtm_TbuConfigType',0,5,253,6,2
	.word	2739
	.byte	4
	.byte	'Gtm_ModuleConfigType',0,5,163,7,2
	.word	1382
	.byte	15,1,1,18
	.word	234
	.byte	18
	.word	234
	.byte	18
	.word	234
	.byte	18
	.word	265
	.byte	0,3
	.word	67989
	.byte	4
	.byte	'Gtm_NotificationPtrType',0,5,172,7,16
	.word	68013
	.byte	4
	.byte	'Gtm_ConfigType',0,5,197,7,2
	.word	1084
	.byte	4
	.byte	'Mcu_ClockType',0,4,156,3,18
	.word	302
	.byte	4
	.byte	'Mcu_ModeType',0,4,162,3,18
	.word	302
	.byte	4
	.byte	'Mcu_RamSectionType',0,4,168,3,18
	.word	302
	.byte	4
	.byte	'Mcu_RamBaseAdrType',0,4,178,3,18
	.word	185
	.byte	4
	.byte	'Mcu_RamSizeType',0,4,181,3,17
	.word	302
	.byte	4
	.byte	'Mcu_RamPrstDatType',0,4,184,3,16
	.word	234
	.byte	4
	.byte	'Mcu_ClockCfgType',0,4,162,4,2
	.word	474
	.byte	4
	.byte	'Mcu_StandbyModeType',0,4,175,4,2
	.word	3046
	.byte	4
	.byte	'Mcu_ConfigType',0,4,212,4,2
	.word	453
	.byte	10,28
	.word	453
	.byte	11,0,0,8
	.word	68308
	.byte	23
	.byte	'Mcu_ConfigRoot',0,4,235,4,29
	.word	68317
	.byte	1,1,4
	.byte	'Port_n_PinType',0,7,203,2,3
	.word	3861
	.byte	4
	.byte	'Port_n_ControlType',0,7,233,2,2
	.word	3563
	.byte	4
	.byte	'Port_n_ConfigType',0,7,164,3,2
	.word	3539
	.byte	4
	.byte	'Port_n_PCSRConfigType',0,7,167,3,16
	.word	302
	.byte	10,12
	.word	3517
	.byte	11,0,0,8
	.word	68458
	.byte	23
	.byte	'Port_ConfigRoot',0,7,205,3,37
	.word	68467
	.byte	1,1,7
	.byte	'_Ifx_P_ACCEN0_Bits',0,16,45,16,4,13
	.byte	'EN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_ACCEN0_Bits',0,16,79,3
	.word	68499
	.byte	7
	.byte	'_Ifx_P_ACCEN1_Bits',0,16,82,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_P_ACCEN1_Bits',0,16,85,3
	.word	69052
	.byte	7
	.byte	'_Ifx_P_ESR_Bits',0,16,88,16,4,13
	.byte	'EN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_ESR_Bits',0,16,107,3
	.word	69125
	.byte	7
	.byte	'_Ifx_P_ID_Bits',0,16,110,16,4,13
	.byte	'MODREV',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'MODTYPE',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'MODNUMBER',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_ID_Bits',0,16,115,3
	.word	69439
	.byte	7
	.byte	'_Ifx_P_IN_Bits',0,16,118,16,4,13
	.byte	'P0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'P2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'P3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'P4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'P5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'P6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'P7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'P8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'P9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'P10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'P11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'P12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'P13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'P14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'P15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_IN_Bits',0,16,137,1,3
	.word	69540
	.byte	7
	.byte	'_Ifx_P_IOCR0_Bits',0,16,140,1,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'PC0',0,1
	.word	234
	.byte	5,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	3,5,2,35,1,13
	.byte	'PC1',0,1
	.word	234
	.byte	5,0,2,35,1,13
	.byte	'reserved_16',0,1
	.word	234
	.byte	3,5,2,35,2,13
	.byte	'PC2',0,1
	.word	234
	.byte	5,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'PC3',0,1
	.word	234
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_P_IOCR0_Bits',0,16,150,1,3
	.word	69837
	.byte	7
	.byte	'_Ifx_P_IOCR12_Bits',0,16,153,1,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'PC12',0,1
	.word	234
	.byte	5,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	3,5,2,35,1,13
	.byte	'PC13',0,1
	.word	234
	.byte	5,0,2,35,1,13
	.byte	'reserved_16',0,1
	.word	234
	.byte	3,5,2,35,2,13
	.byte	'PC14',0,1
	.word	234
	.byte	5,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'PC15',0,1
	.word	234
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_P_IOCR12_Bits',0,16,163,1,3
	.word	70038
	.byte	7
	.byte	'_Ifx_P_IOCR4_Bits',0,16,166,1,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'PC4',0,1
	.word	234
	.byte	5,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	3,5,2,35,1,13
	.byte	'PC5',0,1
	.word	234
	.byte	5,0,2,35,1,13
	.byte	'reserved_16',0,1
	.word	234
	.byte	3,5,2,35,2,13
	.byte	'PC6',0,1
	.word	234
	.byte	5,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'PC7',0,1
	.word	234
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_P_IOCR4_Bits',0,16,176,1,3
	.word	70245
	.byte	7
	.byte	'_Ifx_P_IOCR8_Bits',0,16,179,1,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'PC8',0,1
	.word	234
	.byte	5,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	3,5,2,35,1,13
	.byte	'PC9',0,1
	.word	234
	.byte	5,0,2,35,1,13
	.byte	'reserved_16',0,1
	.word	234
	.byte	3,5,2,35,2,13
	.byte	'PC10',0,1
	.word	234
	.byte	5,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'PC11',0,1
	.word	234
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_P_IOCR8_Bits',0,16,189,1,3
	.word	70446
	.byte	7
	.byte	'_Ifx_P_OMCR0_Bits',0,16,192,1,16,4,13
	.byte	'reserved_0',0,2
	.word	265
	.byte	16,0,2,35,0,13
	.byte	'PCL0',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'PCL1',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'PCL2',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'PCL3',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,2
	.word	265
	.byte	12,0,2,35,2,0,4
	.byte	'Ifx_P_OMCR0_Bits',0,16,200,1,3
	.word	70649
	.byte	7
	.byte	'_Ifx_P_OMCR12_Bits',0,16,203,1,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	28,4,2,35,2,13
	.byte	'PCL12',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'PCL13',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'PCL14',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'PCL15',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_OMCR12_Bits',0,16,210,1,3
	.word	70809
	.byte	7
	.byte	'_Ifx_P_OMCR4_Bits',0,16,213,1,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	20,12,2,35,2,13
	.byte	'PCL4',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'PCL5',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'PCL6',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'PCL7',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_P_OMCR4_Bits',0,16,221,1,3
	.word	70952
	.byte	7
	.byte	'_Ifx_P_OMCR8_Bits',0,16,224,1,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	24,8,2,35,2,13
	.byte	'PCL8',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'PCL9',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'PCL10',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'PCL11',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	234
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_P_OMCR8_Bits',0,16,232,1,3
	.word	71112
	.byte	7
	.byte	'_Ifx_P_OMCR_Bits',0,16,235,1,16,4,13
	.byte	'reserved_0',0,2
	.word	265
	.byte	16,0,2,35,0,13
	.byte	'PCL0',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'PCL1',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'PCL2',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'PCL3',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'PCL4',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'PCL5',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'PCL6',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'PCL7',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'PCL8',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'PCL9',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'PCL10',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'PCL11',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'PCL12',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'PCL13',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'PCL14',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'PCL15',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_OMCR_Bits',0,16,254,1,3
	.word	71274
	.byte	7
	.byte	'_Ifx_P_OMR_Bits',0,16,129,2,16,4,13
	.byte	'PS0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PS1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'PS2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'PS3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PS4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'PS5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'PS6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PS7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'PS8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'PS9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'PS10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'PS11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'PS12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'PS13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'PS14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'PS15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'PCL0',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'PCL1',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'PCL2',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'PCL3',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'PCL4',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'PCL5',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'PCL6',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'PCL7',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'PCL8',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'PCL9',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'PCL10',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'PCL11',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'PCL12',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'PCL13',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'PCL14',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'PCL15',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_OMR_Bits',0,16,163,2,3
	.word	71607
	.byte	7
	.byte	'_Ifx_P_OMSR0_Bits',0,16,166,2,16,4,13
	.byte	'PS0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PS1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'PS2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'PS3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	601
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR0_Bits',0,16,173,2,3
	.word	72162
	.byte	7
	.byte	'_Ifx_P_OMSR12_Bits',0,16,176,2,16,4,13
	.byte	'reserved_0',0,2
	.word	265
	.byte	12,4,2,35,0,13
	.byte	'PS12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'PS13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'PS14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'PS15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR12_Bits',0,16,184,2,3
	.word	72295
	.byte	7
	.byte	'_Ifx_P_OMSR4_Bits',0,16,187,2,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'PS4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'PS5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'PS6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PS7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	601
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR4_Bits',0,16,195,2,3
	.word	72457
	.byte	7
	.byte	'_Ifx_P_OMSR8_Bits',0,16,198,2,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'PS8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'PS9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'PS10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'PS11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,4
	.word	601
	.byte	20,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR8_Bits',0,16,206,2,3
	.word	72612
	.byte	7
	.byte	'_Ifx_P_OMSR_Bits',0,16,209,2,16,4,13
	.byte	'PS0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PS1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'PS2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'PS3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PS4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'PS5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'PS6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PS7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'PS8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'PS9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'PS10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'PS11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'PS12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'PS13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'PS14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'PS15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_OMSR_Bits',0,16,228,2,3
	.word	72770
	.byte	7
	.byte	'_Ifx_P_OUT_Bits',0,16,231,2,16,4,13
	.byte	'P0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'P2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'P3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'P4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'P5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'P6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'P7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'P8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'P9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'P10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'P11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'P12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'P13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'P14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'P15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_OUT_Bits',0,16,250,2,3
	.word	73088
	.byte	7
	.byte	'_Ifx_P_PCSR_Bits',0,16,253,2,16,4,13
	.byte	'reserved_0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'SEL1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'SEL2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,2
	.word	265
	.byte	6,7,2,35,0,13
	.byte	'SEL9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'SEL10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'reserved_11',0,4
	.word	601
	.byte	20,1,2,35,2,13
	.byte	'LCK',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_PCSR_Bits',0,16,135,3,3
	.word	73388
	.byte	7
	.byte	'_Ifx_P_PDISC_Bits',0,16,138,3,16,4,13
	.byte	'PDIS0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'PDIS1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'PDIS2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'PDIS3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PDIS4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'PDIS5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'PDIS6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'PDIS7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'PDIS8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'PDIS9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'PDIS10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'PDIS11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'PDIS12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'PDIS13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'PDIS14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'PDIS15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_P_PDISC_Bits',0,16,157,3,3
	.word	73584
	.byte	7
	.byte	'_Ifx_P_PDR0_Bits',0,16,160,3,16,4,13
	.byte	'PD0',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'PL0',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PD1',0,1
	.word	234
	.byte	3,1,2,35,0,13
	.byte	'PL1',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'PD2',0,1
	.word	234
	.byte	3,5,2,35,1,13
	.byte	'PL2',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'PD3',0,1
	.word	234
	.byte	3,1,2,35,1,13
	.byte	'PL3',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'PD4',0,1
	.word	234
	.byte	3,5,2,35,2,13
	.byte	'PL4',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'PD5',0,1
	.word	234
	.byte	3,1,2,35,2,13
	.byte	'PL5',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'PD6',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'PL6',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'PD7',0,1
	.word	234
	.byte	3,1,2,35,3,13
	.byte	'PL7',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_PDR0_Bits',0,16,178,3,3
	.word	73936
	.byte	7
	.byte	'_Ifx_P_PDR1_Bits',0,16,181,3,16,4,13
	.byte	'PD8',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'PL8',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'PD9',0,1
	.word	234
	.byte	3,1,2,35,0,13
	.byte	'PL9',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'PD10',0,1
	.word	234
	.byte	3,5,2,35,1,13
	.byte	'PL10',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'PD11',0,1
	.word	234
	.byte	3,1,2,35,1,13
	.byte	'PL11',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'PD12',0,1
	.word	234
	.byte	3,5,2,35,2,13
	.byte	'PL12',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'PD13',0,1
	.word	234
	.byte	3,1,2,35,2,13
	.byte	'PL13',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'PD14',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'PL14',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'PD15',0,1
	.word	234
	.byte	3,1,2,35,3,13
	.byte	'PL15',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_P_PDR1_Bits',0,16,199,3,3
	.word	74225
	.byte	21,16,207,3,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	68499
	.byte	2,35,0,0,4
	.byte	'Ifx_P_ACCEN0',0,16,212,3,3
	.word	74526
	.byte	21,16,215,3,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	69052
	.byte	2,35,0,0,4
	.byte	'Ifx_P_ACCEN1',0,16,220,3,3
	.word	74588
	.byte	21,16,223,3,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	69125
	.byte	2,35,0,0,4
	.byte	'Ifx_P_ESR',0,16,228,3,3
	.word	74650
	.byte	21,16,231,3,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	69439
	.byte	2,35,0,0,4
	.byte	'Ifx_P_ID',0,16,236,3,3
	.word	74709
	.byte	21,16,239,3,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	69540
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IN',0,16,244,3,3
	.word	74767
	.byte	21,16,247,3,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	69837
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IOCR0',0,16,252,3,3
	.word	74825
	.byte	21,16,255,3,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	70038
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IOCR12',0,16,132,4,3
	.word	74886
	.byte	21,16,135,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	70245
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IOCR4',0,16,140,4,3
	.word	74948
	.byte	21,16,143,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	70446
	.byte	2,35,0,0,4
	.byte	'Ifx_P_IOCR8',0,16,148,4,3
	.word	75009
	.byte	21,16,151,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	71274
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR',0,16,156,4,3
	.word	75070
	.byte	21,16,159,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	70649
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR0',0,16,164,4,3
	.word	75130
	.byte	21,16,167,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	70809
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR12',0,16,172,4,3
	.word	75191
	.byte	21,16,175,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	70952
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR4',0,16,180,4,3
	.word	75253
	.byte	21,16,183,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	71112
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMCR8',0,16,188,4,3
	.word	75314
	.byte	21,16,191,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	71607
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMR',0,16,196,4,3
	.word	75375
	.byte	21,16,199,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	72770
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR',0,16,204,4,3
	.word	75434
	.byte	21,16,207,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	72162
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR0',0,16,212,4,3
	.word	75494
	.byte	21,16,215,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	72295
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR12',0,16,220,4,3
	.word	75555
	.byte	21,16,223,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	72457
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR4',0,16,228,4,3
	.word	75617
	.byte	21,16,231,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	72612
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OMSR8',0,16,236,4,3
	.word	75678
	.byte	21,16,239,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	73088
	.byte	2,35,0,0,4
	.byte	'Ifx_P_OUT',0,16,244,4,3
	.word	75739
	.byte	21,16,247,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	73388
	.byte	2,35,0,0,4
	.byte	'Ifx_P_PCSR',0,16,252,4,3
	.word	75798
	.byte	21,16,255,4,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	73584
	.byte	2,35,0,0,4
	.byte	'Ifx_P_PDISC',0,16,132,5,3
	.word	75858
	.byte	21,16,135,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	73936
	.byte	2,35,0,0,4
	.byte	'Ifx_P_PDR0',0,16,140,5,3
	.word	75919
	.byte	21,16,143,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	74225
	.byte	2,35,0,0,4
	.byte	'Ifx_P_PDR1',0,16,148,5,3
	.word	75979
	.byte	4
	.byte	'Dio_PortType',0,6,249,1,17
	.word	234
	.byte	4
	.byte	'Dio_PortLevelType',0,6,252,1,17
	.word	265
	.byte	4
	.byte	'Dio_ChannelGroupType',0,6,137,2,3
	.word	3329
	.byte	4
	.byte	'Dio_PortChannelIdType',0,6,145,2,2
	.word	3203
	.byte	10,12
	.word	3182
	.byte	11,0,0,8
	.word	76149
	.byte	23
	.byte	'Dio_ConfigRoot',0,6,190,2,36
	.word	76158
	.byte	1,1,20,12,215,3,9,1,17
	.byte	'DMA_CH_INCREMENT_DIR_NEG',0,0,17
	.byte	'DMA_CH_INCREMENT_DIR_POS',0,1,0,4
	.byte	'Dma_ChIncrementDirectionType',0,12,219,3,3
	.word	76189
	.byte	20,12,226,3,9,1,17
	.byte	'DMA_CH_ADDR_MOD_FACTOR_1',0,0,17
	.byte	'DMA_CH_ADDR_MOD_FACTOR_2',0,1,17
	.byte	'DMA_CH_ADDR_MOD_FACTOR_4',0,2,17
	.byte	'DMA_CH_ADDR_MOD_FACTOR_8',0,3,17
	.byte	'DMA_CH_ADDR_MOD_FACTOR_16',0,4,17
	.byte	'DMA_CH_ADDR_MOD_FACTOR_32',0,5,17
	.byte	'DMA_CH_ADDR_MOD_FACTOR_64',0,6,17
	.byte	'DMA_CH_ADDR_MOD_FACTOR_128',0,7,0,4
	.byte	'Dma_ChAddressModOffsetType',0,12,236,3,3
	.word	76288
	.byte	20,12,243,3,9,1,17
	.byte	'DMA_CH_CIRC_BUFF_DISABLE',0,0,17
	.byte	'DMA_CH_CIRC_BUFF_ENABLE',0,1,0,4
	.byte	'Dma_ChCircularBuffEnType',0,12,247,3,3
	.word	76552
	.byte	20,12,254,3,9,1,17
	.byte	'DMA_CH_WRAP_DISABLE',0,0,17
	.byte	'DMA_CH_WRAP_ENABLE',0,1,0,4
	.byte	'Dma_ChWrapEnType',0,12,130,4,3
	.word	76646
	.byte	20,12,133,4,9,1,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_1',0,0,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_2',0,1,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_4',0,2,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_8',0,3,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_16',0,4,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_32',0,5,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_64',0,6,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_128',0,7,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_256',0,8,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_512',0,9,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_1KB',0,10,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_2KB',0,11,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_4KB',0,12,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_8KB',0,13,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_16KB',0,14,17
	.byte	'DMA_CH_CIRC_BUFF_LEN_32KB',0,15,0,4
	.byte	'Dma_ChCircBuffSizeType',0,12,151,4,3
	.word	76722
	.byte	4
	.byte	'Dma_ChannelConfigType',0,12,181,4,2
	.word	9513
	.byte	4
	.byte	'Dma_ConfigType',0,12,195,4,2
	.word	9492
	.byte	10,24
	.word	9492
	.byte	11,0,0,8
	.word	77239
	.byte	23
	.byte	'Dma_ConfigRoot',0,12,204,4,29
	.word	77248
	.byte	1,1,7
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,17,45,16,4,13
	.byte	'EN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN00_Bits',0,17,79,3
	.word	77279
	.byte	7
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,17,82,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN01_Bits',0,17,85,3
	.word	77838
	.byte	7
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,17,88,16,4,13
	.byte	'EN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN10_Bits',0,17,122,3
	.word	77917
	.byte	7
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,17,125,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN11_Bits',0,17,128,1,3
	.word	78476
	.byte	7
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,17,131,1,16,4,13
	.byte	'EN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN20_Bits',0,17,165,1,3
	.word	78556
	.byte	7
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,17,168,1,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN21_Bits',0,17,171,1,3
	.word	79117
	.byte	7
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,17,174,1,16,4,13
	.byte	'EN0',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	234
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	234
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	234
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	234
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN30_Bits',0,17,208,1,3
	.word	79198
	.byte	7
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,17,211,1,16,4,13
	.byte	'reserved_0',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN31_Bits',0,17,214,1,3
	.word	79759
	.byte	7
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,17,217,1,16,4,13
	.byte	'reserved_0',0,2
	.word	265
	.byte	16,0,2,35,0,13
	.byte	'CSER',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'CDER',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	234
	.byte	2,4,2,35,2,13
	.byte	'CSPBER',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'CSRIER',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	234
	.byte	2,0,2,35,2,13
	.byte	'CRAMER',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'CSLLER',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'CDLLER',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	234
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,17,230,1,3
	.word	79840
	.byte	7
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,17,233,1,16,4,13
	.byte	'reserved_0',0,2
	.word	265
	.byte	16,0,2,35,0,13
	.byte	'ESER',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'EDER',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	234
	.byte	6,0,2,35,2,13
	.byte	'ERER',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'ELER',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	234
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_EER_Bits',0,17,243,1,3
	.word	80114
	.byte	7
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,17,246,1,16,4,13
	.byte	'LEC',0,1
	.word	234
	.byte	7,1,2,35,0,13
	.byte	'reserved_7',0,2
	.word	265
	.byte	9,0,2,35,0,13
	.byte	'SER',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'DER',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	234
	.byte	2,4,2,35,2,13
	.byte	'SPBER',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'SRIER',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	234
	.byte	2,0,2,35,2,13
	.byte	'RAMER',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'SLLER',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'DLLER',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	234
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,17,132,2,3
	.word	80328
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,17,135,2,16,4,13
	.byte	'SMF',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'INCS',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'DMF',0,1
	.word	234
	.byte	3,1,2,35,0,13
	.byte	'INCD',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'CBLS',0,1
	.word	234
	.byte	4,4,2,35,1,13
	.byte	'CBLD',0,1
	.word	234
	.byte	4,0,2,35,1,13
	.byte	'SHCT',0,1
	.word	234
	.byte	4,4,2,35,2,13
	.byte	'SCBE',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'DCBE',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'STAMP',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'ETRL',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'WRPSE',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'WRPDE',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'INTCT',0,1
	.word	234
	.byte	2,4,2,35,3,13
	.byte	'IRDV',0,1
	.word	234
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,17,152,2,3
	.word	80612
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,17,155,2,16,4,13
	.byte	'TREL',0,2
	.word	265
	.byte	14,2,2,35,0,13
	.byte	'reserved_14',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'BLKM',0,1
	.word	234
	.byte	3,5,2,35,2,13
	.byte	'RROAT',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'CHMODE',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'CHDW',0,1
	.word	234
	.byte	3,0,2,35,2,13
	.byte	'PATSEL',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'PRSEL',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'DMAPRIO',0,1
	.word	234
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,17,168,2,3
	.word	80923
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,17,171,2,16,4,13
	.byte	'TCOUNT',0,2
	.word	265
	.byte	14,2,2,35,0,13
	.byte	'reserved_14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'LXO',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'WRPS',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'WRPD',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'ICH',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'IPM',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,1
	.word	234
	.byte	2,2,2,35,2,13
	.byte	'BUFFER',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'FROZEN',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,17,184,2,3
	.word	81196
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,17,187,2,16,4,13
	.byte	'DADR',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,17,190,2,3
	.word	81463
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,17,193,2,16,4,13
	.byte	'RD00',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'RD01',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'RD02',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'RD03',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,17,199,2,3
	.word	81546
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,17,202,2,16,4,13
	.byte	'RD10',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'RD11',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'RD12',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'RD13',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,17,208,2,3
	.word	81673
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,17,211,2,16,4,13
	.byte	'RD20',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'RD21',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'RD22',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'RD23',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,17,217,2,3
	.word	81800
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,17,220,2,16,4,13
	.byte	'RD30',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'RD31',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'RD32',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'RD33',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,17,226,2,3
	.word	81927
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,17,229,2,16,4,13
	.byte	'RD40',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'RD41',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'RD42',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'RD43',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,17,235,2,3
	.word	82054
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,17,238,2,16,4,13
	.byte	'RD50',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'RD51',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'RD52',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'RD53',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,17,244,2,3
	.word	82181
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,17,247,2,16,4,13
	.byte	'RD60',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'RD61',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'RD62',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'RD63',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,17,253,2,3
	.word	82308
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,17,128,3,16,4,13
	.byte	'RD70',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'RD71',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'RD72',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'RD73',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,17,134,3,3
	.word	82435
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,17,137,3,16,4,13
	.byte	'RDCRC',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,17,140,3,3
	.word	82562
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,17,143,3,16,4,13
	.byte	'SADR',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,17,146,3,3
	.word	82648
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,17,149,3,16,4,13
	.byte	'SDCRC',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,17,152,3,3
	.word	82731
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,17,155,3,16,4,13
	.byte	'SHADR',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,17,158,3,3
	.word	82817
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,17,161,3,16,4,13
	.byte	'RS',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	234
	.byte	3,4,2,35,0,13
	.byte	'WS',0,1
	.word	234
	.byte	1,3,2,35,0,13
	.byte	'reserved_5',0,2
	.word	265
	.byte	11,0,2,35,0,13
	.byte	'CH',0,1
	.word	234
	.byte	7,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	265
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,17,169,3,3
	.word	82903
	.byte	7
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,17,172,3,16,4,13
	.byte	'SMF',0,1
	.word	234
	.byte	3,5,2,35,0,13
	.byte	'INCS',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'DMF',0,1
	.word	234
	.byte	3,1,2,35,0,13
	.byte	'INCD',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'CBLS',0,1
	.word	234
	.byte	4,4,2,35,1,13
	.byte	'CBLD',0,1
	.word	234
	.byte	4,0,2,35,1,13
	.byte	'SHCT',0,1
	.word	234
	.byte	4,4,2,35,2,13
	.byte	'SCBE',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'DCBE',0,1
	.word	234
	.byte	1,2,2,35,2,13
	.byte	'STAMP',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'ETRL',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'WRPSE',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'WRPDE',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'INTCT',0,1
	.word	234
	.byte	2,4,2,35,3,13
	.byte	'IRDV',0,1
	.word	234
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,17,189,3,3
	.word	83075
	.byte	7
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,17,192,3,16,4,13
	.byte	'TREL',0,2
	.word	265
	.byte	14,2,2,35,0,13
	.byte	'reserved_14',0,1
	.word	234
	.byte	2,0,2,35,1,13
	.byte	'BLKM',0,1
	.word	234
	.byte	3,5,2,35,2,13
	.byte	'RROAT',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'CHMODE',0,1
	.word	234
	.byte	1,3,2,35,2,13
	.byte	'CHDW',0,1
	.word	234
	.byte	3,0,2,35,2,13
	.byte	'PATSEL',0,1
	.word	234
	.byte	3,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'PRSEL',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'DMAPRIO',0,1
	.word	234
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,17,205,3,3
	.word	83378
	.byte	7
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,17,208,3,16,4,13
	.byte	'TCOUNT',0,2
	.word	265
	.byte	14,2,2,35,0,13
	.byte	'reserved_14',0,1
	.word	234
	.byte	1,1,2,35,1,13
	.byte	'LXO',0,1
	.word	234
	.byte	1,0,2,35,1,13
	.byte	'WRPS',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'WRPD',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'ICH',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'IPM',0,1
	.word	234
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,1
	.word	234
	.byte	2,2,2,35,2,13
	.byte	'BUFFER',0,1
	.word	234
	.byte	1,1,2,35,2,13
	.byte	'FROZEN',0,1
	.word	234
	.byte	1,0,2,35,2,13
	.byte	'SWB',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'CWRP',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'CICH',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'SIT',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	234
	.byte	3,1,2,35,3,13
	.byte	'SCH',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,17,226,3,3
	.word	83647
	.byte	7
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,17,229,3,16,4,13
	.byte	'DADR',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_DADR_Bits',0,17,232,3,3
	.word	83985
	.byte	7
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,17,235,3,16,4,13
	.byte	'RDCRC',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,17,238,3,3
	.word	84060
	.byte	7
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,17,241,3,16,4,13
	.byte	'SADR',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SADR_Bits',0,17,244,3,3
	.word	84140
	.byte	7
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,17,247,3,16,4,13
	.byte	'SDCRC',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,17,250,3,3
	.word	84215
	.byte	7
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,17,253,3,16,4,13
	.byte	'SHADR',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,17,128,4,3
	.word	84295
	.byte	7
	.byte	'_Ifx_DMA_CLC_Bits',0,17,131,4,16,4,13
	.byte	'DISR',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'DISS',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'EDIS',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	601
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_DMA_CLC_Bits',0,17,138,4,3
	.word	84373
	.byte	7
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,17,141,4,16,4,13
	.byte	'SIT',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	601
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_ERRINTR_Bits',0,17,145,4,3
	.word	84516
	.byte	7
	.byte	'_Ifx_DMA_HRR_Bits',0,17,148,4,16,4,13
	.byte	'HRP',0,1
	.word	234
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	601
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_DMA_HRR_Bits',0,17,152,4,3
	.word	84612
	.byte	7
	.byte	'_Ifx_DMA_ID_Bits',0,17,155,4,16,4,13
	.byte	'MODREV',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'MODTYPE',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'MODNUMBER',0,2
	.word	265
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_DMA_ID_Bits',0,17,160,4,3
	.word	84700
	.byte	7
	.byte	'_Ifx_DMA_MEMCON_Bits',0,17,163,4,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	2,30,2,35,0,13
	.byte	'INTERR',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'RMWERR',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'DATAERR',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'reserved_7',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'PMIC',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'ERRDIS',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	25705
	.byte	22,0,2,35,0,0,4
	.byte	'Ifx_DMA_MEMCON_Bits',0,17,175,4,3
	.word	84807
	.byte	7
	.byte	'_Ifx_DMA_MODE_Bits',0,17,178,4,16,4,13
	.byte	'MODE',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	601
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_MODE_Bits',0,17,182,4,3
	.word	85064
	.byte	7
	.byte	'_Ifx_DMA_OTSS_Bits',0,17,185,4,16,4,13
	.byte	'TGS',0,1
	.word	234
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	234
	.byte	3,1,2,35,0,13
	.byte	'BS',0,1
	.word	234
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	601
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_DMA_OTSS_Bits',0,17,191,4,3
	.word	85155
	.byte	7
	.byte	'_Ifx_DMA_PRR0_Bits',0,17,194,4,16,4,13
	.byte	'PAT00',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'PAT01',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'PAT02',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'PAT03',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_PRR0_Bits',0,17,200,4,3
	.word	85281
	.byte	7
	.byte	'_Ifx_DMA_PRR1_Bits',0,17,203,4,16,4,13
	.byte	'PAT10',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'PAT11',0,1
	.word	234
	.byte	8,0,2,35,1,13
	.byte	'PAT12',0,1
	.word	234
	.byte	8,0,2,35,2,13
	.byte	'PAT13',0,1
	.word	234
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_PRR1_Bits',0,17,209,4,3
	.word	85402
	.byte	7
	.byte	'_Ifx_DMA_SUSACR_Bits',0,17,212,4,16,4,13
	.byte	'SUSAC',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	601
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_SUSACR_Bits',0,17,216,4,3
	.word	85523
	.byte	7
	.byte	'_Ifx_DMA_SUSENR_Bits',0,17,219,4,16,4,13
	.byte	'SUSEN',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	601
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_SUSENR_Bits',0,17,223,4,3
	.word	85619
	.byte	7
	.byte	'_Ifx_DMA_TIME_Bits',0,17,226,4,16,4,13
	.byte	'COUNT',0,4
	.word	601
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_TIME_Bits',0,17,229,4,3
	.word	85715
	.byte	7
	.byte	'_Ifx_DMA_TSR_Bits',0,17,232,4,16,4,13
	.byte	'RST',0,1
	.word	234
	.byte	1,7,2,35,0,13
	.byte	'HTRE',0,1
	.word	234
	.byte	1,6,2,35,0,13
	.byte	'TRL',0,1
	.word	234
	.byte	1,5,2,35,0,13
	.byte	'CH',0,1
	.word	234
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	234
	.byte	4,0,2,35,0,13
	.byte	'HLTREQ',0,1
	.word	234
	.byte	1,7,2,35,1,13
	.byte	'HLTACK',0,1
	.word	234
	.byte	1,6,2,35,1,13
	.byte	'reserved_10',0,1
	.word	234
	.byte	6,0,2,35,1,13
	.byte	'ECH',0,1
	.word	234
	.byte	1,7,2,35,2,13
	.byte	'DCH',0,1
	.word	234
	.byte	1,6,2,35,2,13
	.byte	'CTL',0,1
	.word	234
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	234
	.byte	5,0,2,35,2,13
	.byte	'HLTCLR',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	234
	.byte	7,0,2,35,3,0,4
	.byte	'Ifx_DMA_TSR_Bits',0,17,248,4,3
	.word	85785
	.byte	21,17,128,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	77279
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN00',0,17,133,5,3
	.word	86086
	.byte	21,17,136,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	77838
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN01',0,17,141,5,3
	.word	86151
	.byte	21,17,144,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	77917
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN10',0,17,149,5,3
	.word	86216
	.byte	21,17,152,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	78476
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN11',0,17,157,5,3
	.word	86281
	.byte	21,17,160,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	78556
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN20',0,17,165,5,3
	.word	86346
	.byte	21,17,168,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	79117
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN21',0,17,173,5,3
	.word	86411
	.byte	21,17,176,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	79198
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN30',0,17,181,5,3
	.word	86476
	.byte	21,17,184,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	79759
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN31',0,17,189,5,3
	.word	86541
	.byte	21,17,192,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	79840
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_CLRE',0,17,197,5,3
	.word	86606
	.byte	21,17,200,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	80114
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_EER',0,17,205,5,3
	.word	86672
	.byte	21,17,208,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	80328
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ERRSR',0,17,213,5,3
	.word	86737
	.byte	21,17,216,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	80612
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,17,221,5,3
	.word	86804
	.byte	21,17,224,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	80923
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,17,229,5,3
	.word	86874
	.byte	21,17,232,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	81196
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,17,237,5,3
	.word	86943
	.byte	21,17,240,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	81463
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_DADR',0,17,245,5,3
	.word	87012
	.byte	21,17,248,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	81546
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R0',0,17,253,5,3
	.word	87081
	.byte	21,17,128,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	81673
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R1',0,17,133,6,3
	.word	87148
	.byte	21,17,136,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	81800
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R2',0,17,141,6,3
	.word	87215
	.byte	21,17,144,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	81927
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R3',0,17,149,6,3
	.word	87282
	.byte	21,17,152,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82054
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R4',0,17,157,6,3
	.word	87349
	.byte	21,17,160,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82181
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R5',0,17,165,6,3
	.word	87416
	.byte	21,17,168,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82308
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R6',0,17,173,6,3
	.word	87483
	.byte	21,17,176,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82435
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R7',0,17,181,6,3
	.word	87550
	.byte	21,17,184,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82562
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,17,189,6,3
	.word	87617
	.byte	21,17,192,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82648
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SADR',0,17,197,6,3
	.word	87687
	.byte	21,17,200,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82731
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,17,205,6,3
	.word	87756
	.byte	21,17,208,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82817
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,17,213,6,3
	.word	87826
	.byte	21,17,216,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	82903
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SR',0,17,221,6,3
	.word	87896
	.byte	21,17,224,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	83075
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_ADICR',0,17,229,6,3
	.word	87963
	.byte	21,17,232,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	83378
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_CHCFGR',0,17,237,6,3
	.word	88029
	.byte	21,17,240,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	83647
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_CHCSR',0,17,245,6,3
	.word	88096
	.byte	21,17,248,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	83985
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_DADR',0,17,253,6,3
	.word	88162
	.byte	21,17,128,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84060
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_RDCRCR',0,17,133,7,3
	.word	88227
	.byte	21,17,136,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84140
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SADR',0,17,141,7,3
	.word	88294
	.byte	21,17,144,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84215
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SDCRCR',0,17,149,7,3
	.word	88359
	.byte	21,17,152,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84295
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SHADR',0,17,157,7,3
	.word	88426
	.byte	21,17,160,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84373
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CLC',0,17,165,7,3
	.word	88492
	.byte	21,17,168,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84516
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ERRINTR',0,17,173,7,3
	.word	88553
	.byte	21,17,176,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84612
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_HRR',0,17,181,7,3
	.word	88618
	.byte	21,17,184,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84700
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ID',0,17,189,7,3
	.word	88679
	.byte	21,17,192,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	84807
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_MEMCON',0,17,197,7,3
	.word	88739
	.byte	21,17,200,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	85064
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_MODE',0,17,205,7,3
	.word	88803
	.byte	21,17,208,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	85155
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_OTSS',0,17,213,7,3
	.word	88865
	.byte	21,17,216,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	85281
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_PRR0',0,17,221,7,3
	.word	88927
	.byte	21,17,224,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	85402
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_PRR1',0,17,229,7,3
	.word	88989
	.byte	21,17,232,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	85523
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_SUSACR',0,17,237,7,3
	.word	89051
	.byte	21,17,240,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	85619
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_SUSENR',0,17,245,7,3
	.word	89115
	.byte	21,17,248,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	85715
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_TIME',0,17,253,7,3
	.word	89179
	.byte	21,17,128,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	85785
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_TSR',0,17,133,8,3
	.word	89241
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME',0,17,144,8,25,112,9
	.byte	'SR',0,4
	.word	87896
	.byte	2,35,0,9
	.byte	'reserved_4',0,12
	.word	63413
	.byte	2,35,4,9
	.byte	'R0',0,4
	.word	87081
	.byte	2,35,16,9
	.byte	'R1',0,4
	.word	87148
	.byte	2,35,20,9
	.byte	'R2',0,4
	.word	87215
	.byte	2,35,24,9
	.byte	'R3',0,4
	.word	87282
	.byte	2,35,28,9
	.byte	'R4',0,4
	.word	87349
	.byte	2,35,32,9
	.byte	'R5',0,4
	.word	87416
	.byte	2,35,36,9
	.byte	'R6',0,4
	.word	87483
	.byte	2,35,40,9
	.byte	'R7',0,4
	.word	87550
	.byte	2,35,44,10,32
	.word	234
	.byte	11,31,0,9
	.byte	'reserved_30',0,32
	.word	89452
	.byte	2,35,48,9
	.byte	'RDCRC',0,4
	.word	87617
	.byte	2,35,80,9
	.byte	'SDCRC',0,4
	.word	87756
	.byte	2,35,84,9
	.byte	'SADR',0,4
	.word	87687
	.byte	2,35,88,9
	.byte	'DADR',0,4
	.word	87012
	.byte	2,35,92,9
	.byte	'ADICR',0,4
	.word	86804
	.byte	2,35,96,9
	.byte	'CHCR',0,4
	.word	86874
	.byte	2,35,100,9
	.byte	'SHADR',0,4
	.word	87826
	.byte	2,35,104,9
	.byte	'CHSR',0,4
	.word	86943
	.byte	2,35,108,0,22
	.word	89302
	.byte	4
	.byte	'Ifx_DMA_BLK_ME',0,17,165,8,3
	.word	89599
	.byte	7
	.byte	'_Ifx_DMA_BLK',0,17,178,8,25,128,1,9
	.byte	'EER',0,4
	.word	86672
	.byte	2,35,0,9
	.byte	'ERRSR',0,4
	.word	86737
	.byte	2,35,4,9
	.byte	'CLRE',0,4
	.word	86606
	.byte	2,35,8,9
	.byte	'reserved_C',0,4
	.word	62813
	.byte	2,35,12,22
	.word	89302
	.byte	9
	.byte	'ME',0,112
	.word	89710
	.byte	2,35,16,0,22
	.word	89628
	.byte	4
	.byte	'Ifx_DMA_BLK',0,17,185,8,3
	.word	89728
	.byte	7
	.byte	'_Ifx_DMA_CH',0,17,188,8,25,32,9
	.byte	'RDCRCR',0,4
	.word	88227
	.byte	2,35,0,9
	.byte	'SDCRCR',0,4
	.word	88359
	.byte	2,35,4,9
	.byte	'SADR',0,4
	.word	88294
	.byte	2,35,8,9
	.byte	'DADR',0,4
	.word	88162
	.byte	2,35,12,9
	.byte	'ADICR',0,4
	.word	87963
	.byte	2,35,16,9
	.byte	'CHCFGR',0,4
	.word	88029
	.byte	2,35,20,9
	.byte	'SHADR',0,4
	.word	88426
	.byte	2,35,24,9
	.byte	'CHCSR',0,4
	.word	88096
	.byte	2,35,28,0,22
	.word	89754
	.byte	4
	.byte	'Ifx_DMA_CH',0,17,198,8,3
	.word	89894
	.byte	7
	.byte	'_Ifx_CPU_A_Bits',0,18,45,16,4,13
	.byte	'ADDR',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_A_Bits',0,18,48,3
	.word	89919
	.byte	7
	.byte	'_Ifx_CPU_BIV_Bits',0,18,51,16,4,13
	.byte	'VSS',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'BIV',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_BIV_Bits',0,18,55,3
	.word	89980
	.byte	7
	.byte	'_Ifx_CPU_BTV_Bits',0,18,58,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'BTV',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_BTV_Bits',0,18,62,3
	.word	90059
	.byte	7
	.byte	'_Ifx_CPU_CCNT_Bits',0,18,65,16,4,13
	.byte	'CountValue',0,4
	.word	25705
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_CCNT_Bits',0,18,69,3
	.word	90145
	.byte	7
	.byte	'_Ifx_CPU_CCTRL_Bits',0,18,72,16,4,13
	.byte	'CM',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'CE',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'M1',0,4
	.word	25705
	.byte	3,27,2,35,0,13
	.byte	'M2',0,4
	.word	25705
	.byte	3,24,2,35,0,13
	.byte	'M3',0,4
	.word	25705
	.byte	3,21,2,35,0,13
	.byte	'reserved_11',0,4
	.word	25705
	.byte	21,0,2,35,0,0,4
	.byte	'Ifx_CPU_CCTRL_Bits',0,18,80,3
	.word	90234
	.byte	7
	.byte	'_Ifx_CPU_COMPAT_Bits',0,18,83,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'RM',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'SP',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_COMPAT_Bits',0,18,89,3
	.word	90380
	.byte	7
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,18,92,16,4,13
	.byte	'CORE_ID',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CORE_ID_Bits',0,18,96,3
	.word	90507
	.byte	7
	.byte	'_Ifx_CPU_CPR_L_Bits',0,18,99,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'LOWBND',0,4
	.word	25705
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPR_L_Bits',0,18,103,3
	.word	90605
	.byte	7
	.byte	'_Ifx_CPU_CPR_U_Bits',0,18,106,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'UPPBND',0,4
	.word	25705
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPR_U_Bits',0,18,110,3
	.word	90698
	.byte	7
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,18,113,16,4,13
	.byte	'MODREV',0,4
	.word	25705
	.byte	8,24,2,35,0,13
	.byte	'MOD_32B',0,4
	.word	25705
	.byte	8,16,2,35,0,13
	.byte	'MOD',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPU_ID_Bits',0,18,118,3
	.word	90791
	.byte	7
	.byte	'_Ifx_CPU_CPXE_Bits',0,18,121,16,4,13
	.byte	'XE',0,4
	.word	25705
	.byte	8,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPXE_Bits',0,18,125,3
	.word	90898
	.byte	7
	.byte	'_Ifx_CPU_CREVT_Bits',0,18,128,1,16,4,13
	.byte	'EVTA',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'BBM',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'BOD',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'SUSP',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'CNT',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_CREVT_Bits',0,18,136,1,3
	.word	90985
	.byte	7
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,18,139,1,16,4,13
	.byte	'CID',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CUS_ID_Bits',0,18,143,1,3
	.word	91139
	.byte	7
	.byte	'_Ifx_CPU_D_Bits',0,18,146,1,16,4,13
	.byte	'DATA',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_D_Bits',0,18,149,1,3
	.word	91233
	.byte	7
	.byte	'_Ifx_CPU_DATR_Bits',0,18,152,1,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'SBE',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	25705
	.byte	5,23,2,35,0,13
	.byte	'CWE',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'CFE',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'reserved_11',0,4
	.word	25705
	.byte	3,18,2,35,0,13
	.byte	'SOE',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'SME',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DATR_Bits',0,18,163,1,3
	.word	91296
	.byte	7
	.byte	'_Ifx_CPU_DBGSR_Bits',0,18,166,1,16,4,13
	.byte	'DE',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'HALT',0,4
	.word	25705
	.byte	2,29,2,35,0,13
	.byte	'SIH',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'SUSP',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'PREVSUSP',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'PEVT',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'EVTSRC',0,4
	.word	25705
	.byte	5,19,2,35,0,13
	.byte	'reserved_13',0,4
	.word	25705
	.byte	19,0,2,35,0,0,4
	.byte	'Ifx_CPU_DBGSR_Bits',0,18,177,1,3
	.word	91514
	.byte	7
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,18,180,1,16,4,13
	.byte	'DTA',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_DBGTCR_Bits',0,18,184,1,3
	.word	91729
	.byte	7
	.byte	'_Ifx_CPU_DCON0_Bits',0,18,187,1,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'DCBYP',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCON0_Bits',0,18,192,1,3
	.word	91823
	.byte	7
	.byte	'_Ifx_CPU_DCON2_Bits',0,18,195,1,16,4,13
	.byte	'DCACHE_SZE',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'DSCRATCH_SZE',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCON2_Bits',0,18,199,1,3
	.word	91939
	.byte	7
	.byte	'_Ifx_CPU_DCX_Bits',0,18,202,1,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	6,26,2,35,0,13
	.byte	'DCXValue',0,4
	.word	25705
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCX_Bits',0,18,206,1,3
	.word	92040
	.byte	7
	.byte	'_Ifx_CPU_DEADD_Bits',0,18,209,1,16,4,13
	.byte	'ERROR_ADDRESS',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_DEADD_Bits',0,18,212,1,3
	.word	92133
	.byte	7
	.byte	'_Ifx_CPU_DIEAR_Bits',0,18,215,1,16,4,13
	.byte	'TA',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_DIEAR_Bits',0,18,218,1,3
	.word	92213
	.byte	7
	.byte	'_Ifx_CPU_DIETR_Bits',0,18,221,1,16,4,13
	.byte	'IED',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'IE_T',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'IE_C',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'IE_S',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'IE_BI',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'E_INFO',0,4
	.word	25705
	.byte	6,21,2,35,0,13
	.byte	'IE_DUAL',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'IE_SP',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'IE_BS',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'reserved_14',0,4
	.word	25705
	.byte	18,0,2,35,0,0,4
	.byte	'Ifx_CPU_DIETR_Bits',0,18,233,1,3
	.word	92282
	.byte	7
	.byte	'_Ifx_CPU_DMS_Bits',0,18,236,1,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'DMSValue',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_DMS_Bits',0,18,240,1,3
	.word	92511
	.byte	7
	.byte	'_Ifx_CPU_DPR_L_Bits',0,18,243,1,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'LOWBND',0,4
	.word	25705
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPR_L_Bits',0,18,247,1,3
	.word	92604
	.byte	7
	.byte	'_Ifx_CPU_DPR_U_Bits',0,18,250,1,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'UPPBND',0,4
	.word	25705
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPR_U_Bits',0,18,254,1,3
	.word	92699
	.byte	7
	.byte	'_Ifx_CPU_DPRE_Bits',0,18,129,2,16,4,13
	.byte	'RE',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPRE_Bits',0,18,133,2,3
	.word	92794
	.byte	7
	.byte	'_Ifx_CPU_DPWE_Bits',0,18,136,2,16,4,13
	.byte	'WE',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPWE_Bits',0,18,140,2,3
	.word	92884
	.byte	7
	.byte	'_Ifx_CPU_DSTR_Bits',0,18,143,2,16,4,13
	.byte	'SRE',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'GAE',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'LBE',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	3,26,2,35,0,13
	.byte	'CRE',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'reserved_7',0,4
	.word	25705
	.byte	7,18,2,35,0,13
	.byte	'DTME',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'LOE',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'SDE',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'SCE',0,4
	.word	25705
	.byte	1,14,2,35,0,13
	.byte	'CAC',0,4
	.word	25705
	.byte	1,13,2,35,0,13
	.byte	'MPE',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'CLE',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'reserved_21',0,4
	.word	25705
	.byte	3,8,2,35,0,13
	.byte	'ALN',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'reserved_25',0,4
	.word	25705
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_CPU_DSTR_Bits',0,18,161,2,3
	.word	92974
	.byte	7
	.byte	'_Ifx_CPU_EXEVT_Bits',0,18,164,2,16,4,13
	.byte	'EVTA',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'BBM',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'BOD',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'SUSP',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'CNT',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_EXEVT_Bits',0,18,172,2,3
	.word	93298
	.byte	7
	.byte	'_Ifx_CPU_FCX_Bits',0,18,175,2,16,4,13
	.byte	'FCXO',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'FCXS',0,4
	.word	25705
	.byte	4,12,2,35,0,13
	.byte	'reserved_20',0,4
	.word	25705
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_FCX_Bits',0,18,180,2,3
	.word	93452
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,18,183,2,16,4,13
	.byte	'TST',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TCL',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	6,24,2,35,0,13
	.byte	'RM',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	25705
	.byte	8,14,2,35,0,13
	.byte	'FXE',0,4
	.word	25705
	.byte	1,13,2,35,0,13
	.byte	'FUE',0,4
	.word	25705
	.byte	1,12,2,35,0,13
	.byte	'FZE',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'FVE',0,4
	.word	25705
	.byte	1,10,2,35,0,13
	.byte	'FIE',0,4
	.word	25705
	.byte	1,9,2,35,0,13
	.byte	'reserved_23',0,4
	.word	25705
	.byte	3,6,2,35,0,13
	.byte	'FX',0,4
	.word	25705
	.byte	1,5,2,35,0,13
	.byte	'FU',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'FZ',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'FV',0,4
	.word	25705
	.byte	1,2,2,35,0,13
	.byte	'FI',0,4
	.word	25705
	.byte	1,1,2,35,0,13
	.byte	'reserved_31',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,18,202,2,3
	.word	93558
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,18,205,2,16,4,13
	.byte	'OPC',0,4
	.word	25705
	.byte	8,24,2,35,0,13
	.byte	'FMT',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'reserved_9',0,4
	.word	25705
	.byte	7,16,2,35,0,13
	.byte	'DREG',0,4
	.word	25705
	.byte	4,12,2,35,0,13
	.byte	'reserved_20',0,4
	.word	25705
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,18,212,2,3
	.word	93907
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,18,215,2,16,4,13
	.byte	'PC',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,18,218,2,3
	.word	94067
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,18,221,2,16,4,13
	.byte	'SRC1',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,18,224,2,3
	.word	94148
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,18,227,2,16,4,13
	.byte	'SRC2',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,18,230,2,3
	.word	94235
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,18,233,2,16,4,13
	.byte	'SRC3',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,18,236,2,3
	.word	94322
	.byte	7
	.byte	'_Ifx_CPU_ICNT_Bits',0,18,239,2,16,4,13
	.byte	'CountValue',0,4
	.word	25705
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_ICNT_Bits',0,18,243,2,3
	.word	94409
	.byte	7
	.byte	'_Ifx_CPU_ICR_Bits',0,18,246,2,16,4,13
	.byte	'CCPN',0,4
	.word	25705
	.byte	10,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	25705
	.byte	5,17,2,35,0,13
	.byte	'IE',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'PIPN',0,4
	.word	25705
	.byte	10,6,2,35,0,13
	.byte	'reserved_26',0,4
	.word	25705
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_CPU_ICR_Bits',0,18,253,2,3
	.word	94500
	.byte	7
	.byte	'_Ifx_CPU_ISP_Bits',0,18,128,3,16,4,13
	.byte	'ISP',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_ISP_Bits',0,18,131,3,3
	.word	94643
	.byte	7
	.byte	'_Ifx_CPU_LCX_Bits',0,18,134,3,16,4,13
	.byte	'LCXO',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'LCXS',0,4
	.word	25705
	.byte	4,12,2,35,0,13
	.byte	'reserved_20',0,4
	.word	25705
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_LCX_Bits',0,18,139,3,3
	.word	94709
	.byte	7
	.byte	'_Ifx_CPU_M1CNT_Bits',0,18,142,3,16,4,13
	.byte	'CountValue',0,4
	.word	25705
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M1CNT_Bits',0,18,146,3,3
	.word	94815
	.byte	7
	.byte	'_Ifx_CPU_M2CNT_Bits',0,18,149,3,16,4,13
	.byte	'CountValue',0,4
	.word	25705
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M2CNT_Bits',0,18,153,3,3
	.word	94908
	.byte	7
	.byte	'_Ifx_CPU_M3CNT_Bits',0,18,156,3,16,4,13
	.byte	'CountValue',0,4
	.word	25705
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M3CNT_Bits',0,18,160,3,3
	.word	95001
	.byte	7
	.byte	'_Ifx_CPU_PC_Bits',0,18,163,3,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'PC',0,4
	.word	25705
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_PC_Bits',0,18,167,3,3
	.word	95094
	.byte	7
	.byte	'_Ifx_CPU_PCON0_Bits',0,18,170,3,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'PCBYP',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON0_Bits',0,18,175,3,3
	.word	95179
	.byte	7
	.byte	'_Ifx_CPU_PCON1_Bits',0,18,178,3,16,4,13
	.byte	'PCINV',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'PBINV',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	25705
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON1_Bits',0,18,183,3,3
	.word	95295
	.byte	7
	.byte	'_Ifx_CPU_PCON2_Bits',0,18,186,3,16,4,13
	.byte	'PCACHE_SZE',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'PSCRATCH_SZE',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON2_Bits',0,18,190,3,3
	.word	95406
	.byte	7
	.byte	'_Ifx_CPU_PCXI_Bits',0,18,193,3,16,4,13
	.byte	'PCXO',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'PCXS',0,4
	.word	25705
	.byte	4,12,2,35,0,13
	.byte	'UL',0,4
	.word	25705
	.byte	1,11,2,35,0,13
	.byte	'PIE',0,4
	.word	25705
	.byte	1,10,2,35,0,13
	.byte	'PCPN',0,4
	.word	25705
	.byte	10,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCXI_Bits',0,18,200,3,3
	.word	95507
	.byte	7
	.byte	'_Ifx_CPU_PIEAR_Bits',0,18,203,3,16,4,13
	.byte	'TA',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_PIEAR_Bits',0,18,206,3,3
	.word	95637
	.byte	7
	.byte	'_Ifx_CPU_PIETR_Bits',0,18,209,3,16,4,13
	.byte	'IED',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'IE_T',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'IE_C',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'IE_S',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'IE_BI',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'E_INFO',0,4
	.word	25705
	.byte	6,21,2,35,0,13
	.byte	'IE_DUAL',0,4
	.word	25705
	.byte	1,20,2,35,0,13
	.byte	'IE_SP',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'IE_BS',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'reserved_14',0,4
	.word	25705
	.byte	18,0,2,35,0,0,4
	.byte	'Ifx_CPU_PIETR_Bits',0,18,221,3,3
	.word	95706
	.byte	7
	.byte	'_Ifx_CPU_PMA0_Bits',0,18,224,3,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	13,19,2,35,0,13
	.byte	'DAC',0,4
	.word	25705
	.byte	3,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA0_Bits',0,18,229,3,3
	.word	95935
	.byte	7
	.byte	'_Ifx_CPU_PMA1_Bits',0,18,232,3,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	14,18,2,35,0,13
	.byte	'CAC',0,4
	.word	25705
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA1_Bits',0,18,237,3,3
	.word	96048
	.byte	7
	.byte	'_Ifx_CPU_PMA2_Bits',0,18,240,3,16,4,13
	.byte	'PSI',0,4
	.word	25705
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	25705
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA2_Bits',0,18,244,3,3
	.word	96161
	.byte	7
	.byte	'_Ifx_CPU_PSTR_Bits',0,18,247,3,16,4,13
	.byte	'FRE',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'FBE',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	9,20,2,35,0,13
	.byte	'FPE',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'reserved_13',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'FME',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'reserved_15',0,4
	.word	25705
	.byte	17,0,2,35,0,0,4
	.byte	'Ifx_CPU_PSTR_Bits',0,18,129,4,3
	.word	96252
	.byte	7
	.byte	'_Ifx_CPU_PSW_Bits',0,18,132,4,16,4,13
	.byte	'CDC',0,4
	.word	25705
	.byte	7,25,2,35,0,13
	.byte	'CDE',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'GW',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'IS',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'IO',0,4
	.word	25705
	.byte	2,20,2,35,0,13
	.byte	'PRS',0,4
	.word	25705
	.byte	2,18,2,35,0,13
	.byte	'S',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'reserved_15',0,4
	.word	25705
	.byte	12,5,2,35,0,13
	.byte	'SAV',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'AV',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'SV',0,4
	.word	25705
	.byte	1,2,2,35,0,13
	.byte	'V',0,4
	.word	25705
	.byte	1,1,2,35,0,13
	.byte	'C',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_PSW_Bits',0,18,147,4,3
	.word	96455
	.byte	7
	.byte	'_Ifx_CPU_SEGEN_Bits',0,18,150,4,16,4,13
	.byte	'ADFLIP',0,4
	.word	25705
	.byte	8,24,2,35,0,13
	.byte	'ADTYPE',0,4
	.word	25705
	.byte	2,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	25705
	.byte	21,1,2,35,0,13
	.byte	'AE',0,4
	.word	25705
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_SEGEN_Bits',0,18,156,4,3
	.word	96698
	.byte	7
	.byte	'_Ifx_CPU_SMACON_Bits',0,18,159,4,16,4,13
	.byte	'PC',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'PT',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	5,24,2,35,0,13
	.byte	'DC',0,4
	.word	25705
	.byte	1,23,2,35,0,13
	.byte	'reserved_9',0,4
	.word	25705
	.byte	1,22,2,35,0,13
	.byte	'DT',0,4
	.word	25705
	.byte	1,21,2,35,0,13
	.byte	'reserved_11',0,4
	.word	25705
	.byte	13,8,2,35,0,13
	.byte	'IODT',0,4
	.word	25705
	.byte	1,7,2,35,0,13
	.byte	'reserved_25',0,4
	.word	25705
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_CPU_SMACON_Bits',0,18,171,4,3
	.word	96826
	.byte	7
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,18,174,4,16,4,13
	.byte	'EN',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,18,177,4,3
	.word	97067
	.byte	7
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,18,180,4,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,18,183,4,3
	.word	97150
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,18,186,4,16,4,13
	.byte	'EN',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,18,189,4,3
	.word	97241
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,18,192,4,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,18,195,4,3
	.word	97332
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,18,198,4,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	5,27,2,35,0,13
	.byte	'ADDR',0,4
	.word	25705
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,18,202,4,3
	.word	97431
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,18,205,4,16,4,13
	.byte	'reserved_0',0,4
	.word	25705
	.byte	5,27,2,35,0,13
	.byte	'ADDR',0,4
	.word	25705
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,18,209,4,3
	.word	97538
	.byte	7
	.byte	'_Ifx_CPU_SWEVT_Bits',0,18,212,4,16,4,13
	.byte	'EVTA',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'BBM',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'BOD',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'SUSP',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'CNT',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_SWEVT_Bits',0,18,220,4,3
	.word	97645
	.byte	7
	.byte	'_Ifx_CPU_SYSCON_Bits',0,18,223,4,16,4,13
	.byte	'FCDSF',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'PROTEN',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'TPROTEN',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'IS',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'IT',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SYSCON_Bits',0,18,231,4,3
	.word	97799
	.byte	7
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,18,234,4,16,4,13
	.byte	'ASI',0,4
	.word	25705
	.byte	5,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	25705
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,18,238,4,3
	.word	97960
	.byte	7
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,18,241,4,16,4,13
	.byte	'TEXP0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'TEXP1',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'TEXP2',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	25705
	.byte	13,16,2,35,0,13
	.byte	'TTRAP',0,4
	.word	25705
	.byte	1,15,2,35,0,13
	.byte	'reserved_17',0,4
	.word	25705
	.byte	15,0,2,35,0,0,4
	.byte	'Ifx_CPU_TPS_CON_Bits',0,18,249,4,3
	.word	98058
	.byte	7
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,18,252,4,16,4,13
	.byte	'Timer',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,18,255,4,3
	.word	98230
	.byte	7
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,18,130,5,16,4,13
	.byte	'ADDR',0,4
	.word	25705
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_TR_ADR_Bits',0,18,133,5,3
	.word	98310
	.byte	7
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,18,136,5,16,4,13
	.byte	'EVTA',0,4
	.word	25705
	.byte	3,29,2,35,0,13
	.byte	'BBM',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'BOD',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'SUSP',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'CNT',0,4
	.word	25705
	.byte	2,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	4,20,2,35,0,13
	.byte	'TYP',0,4
	.word	25705
	.byte	1,19,2,35,0,13
	.byte	'RNG',0,4
	.word	25705
	.byte	1,18,2,35,0,13
	.byte	'reserved_14',0,4
	.word	25705
	.byte	1,17,2,35,0,13
	.byte	'ASI_EN',0,4
	.word	25705
	.byte	1,16,2,35,0,13
	.byte	'ASI',0,4
	.word	25705
	.byte	5,11,2,35,0,13
	.byte	'reserved_21',0,4
	.word	25705
	.byte	6,5,2,35,0,13
	.byte	'AST',0,4
	.word	25705
	.byte	1,4,2,35,0,13
	.byte	'ALD',0,4
	.word	25705
	.byte	1,3,2,35,0,13
	.byte	'reserved_29',0,4
	.word	25705
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_CPU_TR_EVT_Bits',0,18,153,5,3
	.word	98383
	.byte	7
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,18,156,5,16,4,13
	.byte	'T0',0,4
	.word	25705
	.byte	1,31,2,35,0,13
	.byte	'T1',0,4
	.word	25705
	.byte	1,30,2,35,0,13
	.byte	'T2',0,4
	.word	25705
	.byte	1,29,2,35,0,13
	.byte	'T3',0,4
	.word	25705
	.byte	1,28,2,35,0,13
	.byte	'T4',0,4
	.word	25705
	.byte	1,27,2,35,0,13
	.byte	'T5',0,4
	.word	25705
	.byte	1,26,2,35,0,13
	.byte	'T6',0,4
	.word	25705
	.byte	1,25,2,35,0,13
	.byte	'T7',0,4
	.word	25705
	.byte	1,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	25705
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,18,167,5,3
	.word	98701
	.byte	21,18,175,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	89919
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_A',0,18,180,5,3
	.word	98896
	.byte	21,18,183,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	89980
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_BIV',0,18,188,5,3
	.word	98955
	.byte	21,18,191,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90059
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_BTV',0,18,196,5,3
	.word	99016
	.byte	21,18,199,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90145
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CCNT',0,18,204,5,3
	.word	99077
	.byte	21,18,207,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90234
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CCTRL',0,18,212,5,3
	.word	99139
	.byte	21,18,215,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90380
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_COMPAT',0,18,220,5,3
	.word	99202
	.byte	21,18,223,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90507
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CORE_ID',0,18,228,5,3
	.word	99266
	.byte	21,18,231,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90605
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPR_L',0,18,236,5,3
	.word	99331
	.byte	21,18,239,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90698
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPR_U',0,18,244,5,3
	.word	99394
	.byte	21,18,247,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90791
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPU_ID',0,18,252,5,3
	.word	99457
	.byte	21,18,255,5,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90898
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPXE',0,18,132,6,3
	.word	99521
	.byte	21,18,135,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	90985
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CREVT',0,18,140,6,3
	.word	99583
	.byte	21,18,143,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	91139
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CUS_ID',0,18,148,6,3
	.word	99646
	.byte	21,18,151,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	91233
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_D',0,18,156,6,3
	.word	99710
	.byte	21,18,159,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	91296
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DATR',0,18,164,6,3
	.word	99769
	.byte	21,18,167,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	91514
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DBGSR',0,18,172,6,3
	.word	99831
	.byte	21,18,175,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	91729
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DBGTCR',0,18,180,6,3
	.word	99894
	.byte	21,18,183,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	91823
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCON0',0,18,188,6,3
	.word	99958
	.byte	21,18,191,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	91939
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCON2',0,18,196,6,3
	.word	100021
	.byte	21,18,199,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92040
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCX',0,18,204,6,3
	.word	100084
	.byte	21,18,207,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92133
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DEADD',0,18,212,6,3
	.word	100145
	.byte	21,18,215,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92213
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DIEAR',0,18,220,6,3
	.word	100208
	.byte	21,18,223,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92282
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DIETR',0,18,228,6,3
	.word	100271
	.byte	21,18,231,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92511
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DMS',0,18,236,6,3
	.word	100334
	.byte	21,18,239,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92604
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPR_L',0,18,244,6,3
	.word	100395
	.byte	21,18,247,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92699
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPR_U',0,18,252,6,3
	.word	100458
	.byte	21,18,255,6,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92794
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPRE',0,18,132,7,3
	.word	100521
	.byte	21,18,135,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92884
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPWE',0,18,140,7,3
	.word	100583
	.byte	21,18,143,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	92974
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DSTR',0,18,148,7,3
	.word	100645
	.byte	21,18,151,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	93298
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_EXEVT',0,18,156,7,3
	.word	100707
	.byte	21,18,159,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	93452
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FCX',0,18,164,7,3
	.word	100770
	.byte	21,18,167,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	93558
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,18,172,7,3
	.word	100831
	.byte	21,18,175,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	93907
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,18,180,7,3
	.word	100901
	.byte	21,18,183,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94067
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,18,188,7,3
	.word	100971
	.byte	21,18,191,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94148
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,18,196,7,3
	.word	101040
	.byte	21,18,199,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94235
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,18,204,7,3
	.word	101111
	.byte	21,18,207,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94322
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,18,212,7,3
	.word	101182
	.byte	21,18,215,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94409
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ICNT',0,18,220,7,3
	.word	101253
	.byte	21,18,223,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94500
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ICR',0,18,228,7,3
	.word	101315
	.byte	21,18,231,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94643
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ISP',0,18,236,7,3
	.word	101376
	.byte	21,18,239,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94709
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_LCX',0,18,244,7,3
	.word	101437
	.byte	21,18,247,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94815
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M1CNT',0,18,252,7,3
	.word	101498
	.byte	21,18,255,7,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	94908
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M2CNT',0,18,132,8,3
	.word	101561
	.byte	21,18,135,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95001
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M3CNT',0,18,140,8,3
	.word	101624
	.byte	21,18,143,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95094
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PC',0,18,148,8,3
	.word	101687
	.byte	21,18,151,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95179
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON0',0,18,156,8,3
	.word	101747
	.byte	21,18,159,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95295
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON1',0,18,164,8,3
	.word	101810
	.byte	21,18,167,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95406
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON2',0,18,172,8,3
	.word	101873
	.byte	21,18,175,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95507
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCXI',0,18,180,8,3
	.word	101936
	.byte	21,18,183,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95637
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PIEAR',0,18,188,8,3
	.word	101998
	.byte	21,18,191,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95706
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PIETR',0,18,196,8,3
	.word	102061
	.byte	21,18,199,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	95935
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA0',0,18,204,8,3
	.word	102124
	.byte	21,18,207,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	96048
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA1',0,18,212,8,3
	.word	102186
	.byte	21,18,215,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	96161
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA2',0,18,220,8,3
	.word	102248
	.byte	21,18,223,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	96252
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PSTR',0,18,228,8,3
	.word	102310
	.byte	21,18,231,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	96455
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PSW',0,18,236,8,3
	.word	102372
	.byte	21,18,239,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	96698
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SEGEN',0,18,244,8,3
	.word	102433
	.byte	21,18,247,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	96826
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SMACON',0,18,252,8,3
	.word	102496
	.byte	21,18,255,8,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97067
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENA',0,18,132,9,3
	.word	102560
	.byte	21,18,135,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97150
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENB',0,18,140,9,3
	.word	102630
	.byte	21,18,143,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97241
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,18,148,9,3
	.word	102700
	.byte	21,18,151,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97332
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,18,156,9,3
	.word	102774
	.byte	21,18,159,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97431
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,18,164,9,3
	.word	102848
	.byte	21,18,167,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97538
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,18,172,9,3
	.word	102918
	.byte	21,18,175,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97645
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SWEVT',0,18,180,9,3
	.word	102988
	.byte	21,18,183,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97799
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SYSCON',0,18,188,9,3
	.word	103051
	.byte	21,18,191,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	97960
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TASK_ASI',0,18,196,9,3
	.word	103115
	.byte	21,18,199,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	98058
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TPS_CON',0,18,204,9,3
	.word	103181
	.byte	21,18,207,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	98230
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TPS_TIMER',0,18,212,9,3
	.word	103246
	.byte	21,18,215,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	98310
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TR_ADR',0,18,220,9,3
	.word	103313
	.byte	21,18,223,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	98383
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TR_EVT',0,18,228,9,3
	.word	103377
	.byte	21,18,231,9,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	98701
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TRIG_ACC',0,18,236,9,3
	.word	103441
	.byte	7
	.byte	'_Ifx_CPU_CPR',0,18,247,9,25,8,9
	.byte	'L',0,4
	.word	99331
	.byte	2,35,0,9
	.byte	'U',0,4
	.word	99394
	.byte	2,35,4,0,22
	.word	103507
	.byte	4
	.byte	'Ifx_CPU_CPR',0,18,251,9,3
	.word	103549
	.byte	7
	.byte	'_Ifx_CPU_DPR',0,18,254,9,25,8,9
	.byte	'L',0,4
	.word	100395
	.byte	2,35,0,9
	.byte	'U',0,4
	.word	100458
	.byte	2,35,4,0,22
	.word	103575
	.byte	4
	.byte	'Ifx_CPU_DPR',0,18,130,10,3
	.word	103617
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN',0,18,133,10,25,16,9
	.byte	'LA',0,4
	.word	102848
	.byte	2,35,0,9
	.byte	'UA',0,4
	.word	102918
	.byte	2,35,4,9
	.byte	'ACCENA',0,4
	.word	102700
	.byte	2,35,8,9
	.byte	'ACCENB',0,4
	.word	102774
	.byte	2,35,12,0,22
	.word	103643
	.byte	4
	.byte	'Ifx_CPU_SPROT_RGN',0,18,139,10,3
	.word	103725
	.byte	7
	.byte	'_Ifx_CPU_TPS',0,18,142,10,25,16,9
	.byte	'CON',0,4
	.word	103181
	.byte	2,35,0,10,12
	.word	103246
	.byte	11,2,0,9
	.byte	'TIMER',0,12
	.word	103789
	.byte	2,35,4,0,22
	.word	103757
	.byte	4
	.byte	'Ifx_CPU_TPS',0,18,146,10,3
	.word	103814
	.byte	7
	.byte	'_Ifx_CPU_TR',0,18,149,10,25,8,9
	.byte	'EVT',0,4
	.word	103377
	.byte	2,35,0,9
	.byte	'ADR',0,4
	.word	103313
	.byte	2,35,4,0,22
	.word	103840
	.byte	4
	.byte	'Ifx_CPU_TR',0,18,153,10,3
	.word	103885
	.byte	7
	.byte	'_Ifx_SRC_SRCR_Bits',0,19,45,16,4,13
	.byte	'SRPN',0,1
	.word	234
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	234
	.byte	2,6,2,35,1,13
	.byte	'SRE',0,1
	.word	234
	.byte	1,5,2,35,1,13
	.byte	'TOS',0,1
	.word	234
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	234
	.byte	4,0,2,35,1,13
	.byte	'ECC',0,1
	.word	234
	.byte	5,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	234
	.byte	3,0,2,35,2,13
	.byte	'SRR',0,1
	.word	234
	.byte	1,7,2,35,3,13
	.byte	'CLRR',0,1
	.word	234
	.byte	1,6,2,35,3,13
	.byte	'SETR',0,1
	.word	234
	.byte	1,5,2,35,3,13
	.byte	'IOV',0,1
	.word	234
	.byte	1,4,2,35,3,13
	.byte	'IOVCLR',0,1
	.word	234
	.byte	1,3,2,35,3,13
	.byte	'SWS',0,1
	.word	234
	.byte	1,2,2,35,3,13
	.byte	'SWSCLR',0,1
	.word	234
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	234
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SRC_SRCR_Bits',0,19,62,3
	.word	103910
	.byte	21,19,70,9,4,9
	.byte	'U',0,4
	.word	601
	.byte	2,35,0,9
	.byte	'I',0,4
	.word	26894
	.byte	2,35,0,9
	.byte	'B',0,4
	.word	103910
	.byte	2,35,0,0,4
	.byte	'Ifx_SRC_SRCR',0,19,75,3
	.word	104226
	.byte	7
	.byte	'_Ifx_SRC_ASCLIN',0,19,86,25,12,9
	.byte	'TX',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'RX',0,4
	.word	104226
	.byte	2,35,4,9
	.byte	'ERR',0,4
	.word	104226
	.byte	2,35,8,0,22
	.word	104286
	.byte	4
	.byte	'Ifx_SRC_ASCLIN',0,19,91,3
	.word	104345
	.byte	7
	.byte	'_Ifx_SRC_BCUSPB',0,19,94,25,4,9
	.byte	'SBSRC',0,4
	.word	104226
	.byte	2,35,0,0,22
	.word	104373
	.byte	4
	.byte	'Ifx_SRC_BCUSPB',0,19,97,3
	.word	104410
	.byte	7
	.byte	'_Ifx_SRC_CAN',0,19,100,25,64,10,64
	.word	104226
	.byte	11,15,0,9
	.byte	'INT',0,64
	.word	104456
	.byte	2,35,0,0,22
	.word	104438
	.byte	4
	.byte	'Ifx_SRC_CAN',0,19,103,3
	.word	104479
	.byte	7
	.byte	'_Ifx_SRC_CAN1',0,19,106,25,32,10,32
	.word	104226
	.byte	11,7,0,9
	.byte	'INT',0,32
	.word	104523
	.byte	2,35,0,0,22
	.word	104504
	.byte	4
	.byte	'Ifx_SRC_CAN1',0,19,109,3
	.word	104546
	.byte	7
	.byte	'_Ifx_SRC_CCU6',0,19,112,25,16,9
	.byte	'SR0',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'SR1',0,4
	.word	104226
	.byte	2,35,4,9
	.byte	'SR2',0,4
	.word	104226
	.byte	2,35,8,9
	.byte	'SR3',0,4
	.word	104226
	.byte	2,35,12,0,22
	.word	104572
	.byte	4
	.byte	'Ifx_SRC_CCU6',0,19,118,3
	.word	104644
	.byte	7
	.byte	'_Ifx_SRC_CERBERUS',0,19,121,25,8,10,8
	.word	104226
	.byte	11,1,0,9
	.byte	'SR',0,8
	.word	104693
	.byte	2,35,0,0,22
	.word	104670
	.byte	4
	.byte	'Ifx_SRC_CERBERUS',0,19,124,3
	.word	104715
	.byte	7
	.byte	'_Ifx_SRC_CPU',0,19,127,25,32,9
	.byte	'SBSRC',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'reserved_4',0,28
	.word	62226
	.byte	2,35,4,0,22
	.word	104745
	.byte	4
	.byte	'Ifx_SRC_CPU',0,19,131,1,3
	.word	104799
	.byte	7
	.byte	'_Ifx_SRC_DMA',0,19,134,1,25,80,9
	.byte	'ERR',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'reserved_4',0,12
	.word	63413
	.byte	2,35,4,9
	.byte	'CH',0,64
	.word	104456
	.byte	2,35,16,0,22
	.word	104825
	.byte	4
	.byte	'Ifx_SRC_DMA',0,19,139,1,3
	.word	104890
	.byte	7
	.byte	'_Ifx_SRC_EMEM',0,19,142,1,25,4,9
	.byte	'SR',0,4
	.word	104226
	.byte	2,35,0,0,22
	.word	104916
	.byte	4
	.byte	'Ifx_SRC_EMEM',0,19,145,1,3
	.word	104949
	.byte	7
	.byte	'_Ifx_SRC_ERAY',0,19,148,1,25,80,9
	.byte	'INT',0,8
	.word	104693
	.byte	2,35,0,9
	.byte	'TINT',0,8
	.word	104693
	.byte	2,35,8,9
	.byte	'NDAT',0,8
	.word	104693
	.byte	2,35,16,9
	.byte	'MBSC',0,8
	.word	104693
	.byte	2,35,24,9
	.byte	'OBUSY',0,4
	.word	104226
	.byte	2,35,32,9
	.byte	'IBUSY',0,4
	.word	104226
	.byte	2,35,36,10,40
	.word	234
	.byte	11,39,0,9
	.byte	'reserved_28',0,40
	.word	105081
	.byte	2,35,40,0,22
	.word	104976
	.byte	4
	.byte	'Ifx_SRC_ERAY',0,19,157,1,3
	.word	105112
	.byte	7
	.byte	'_Ifx_SRC_ETH',0,19,160,1,25,4,9
	.byte	'SR',0,4
	.word	104226
	.byte	2,35,0,0,22
	.word	105139
	.byte	4
	.byte	'Ifx_SRC_ETH',0,19,163,1,3
	.word	105171
	.byte	7
	.byte	'_Ifx_SRC_EVR',0,19,166,1,25,8,9
	.byte	'WUT',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'SCDC',0,4
	.word	104226
	.byte	2,35,4,0,22
	.word	105197
	.byte	4
	.byte	'Ifx_SRC_EVR',0,19,170,1,3
	.word	105244
	.byte	7
	.byte	'_Ifx_SRC_FFT',0,19,173,1,25,12,9
	.byte	'DONE',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'ERR',0,4
	.word	104226
	.byte	2,35,4,9
	.byte	'RFS',0,4
	.word	104226
	.byte	2,35,8,0,22
	.word	105270
	.byte	4
	.byte	'Ifx_SRC_FFT',0,19,178,1,3
	.word	105330
	.byte	7
	.byte	'_Ifx_SRC_GPSR',0,19,181,1,25,128,12,9
	.byte	'SR0',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'SR1',0,4
	.word	104226
	.byte	2,35,4,9
	.byte	'SR2',0,4
	.word	104226
	.byte	2,35,8,9
	.byte	'SR3',0,4
	.word	104226
	.byte	2,35,12,10,240,11
	.word	234
	.byte	11,239,11,0,9
	.byte	'reserved_10',0,240,11
	.word	105429
	.byte	2,35,16,0,22
	.word	105356
	.byte	4
	.byte	'Ifx_SRC_GPSR',0,19,188,1,3
	.word	105463
	.byte	7
	.byte	'_Ifx_SRC_GPT12',0,19,191,1,25,48,9
	.byte	'CIRQ',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'T2',0,4
	.word	104226
	.byte	2,35,4,9
	.byte	'T3',0,4
	.word	104226
	.byte	2,35,8,9
	.byte	'T4',0,4
	.word	104226
	.byte	2,35,12,9
	.byte	'T5',0,4
	.word	104226
	.byte	2,35,16,9
	.byte	'T6',0,4
	.word	104226
	.byte	2,35,20,10,24
	.word	234
	.byte	11,23,0,9
	.byte	'reserved_18',0,24
	.word	105585
	.byte	2,35,24,0,22
	.word	105490
	.byte	4
	.byte	'Ifx_SRC_GPT12',0,19,200,1,3
	.word	105616
	.byte	7
	.byte	'_Ifx_SRC_GTM',0,19,203,1,25,192,11,9
	.byte	'AEIIRQ',0,4
	.word	104226
	.byte	2,35,0,10,236,2
	.word	234
	.byte	11,235,2,0,9
	.byte	'reserved_4',0,236,2
	.word	105680
	.byte	2,35,4,9
	.byte	'ERR',0,4
	.word	104226
	.byte	3,35,240,2,9
	.byte	'reserved_174',0,12
	.word	63413
	.byte	3,35,244,2,10,32
	.word	104523
	.byte	11,0,0,9
	.byte	'TIM',0,32
	.word	105749
	.byte	3,35,128,3,10,224,7
	.word	234
	.byte	11,223,7,0,9
	.byte	'reserved_1A0',0,224,7
	.word	105772
	.byte	3,35,160,3,10,64
	.word	104523
	.byte	11,1,0,9
	.byte	'TOM',0,64
	.word	105807
	.byte	3,35,128,11,0,22
	.word	105644
	.byte	4
	.byte	'Ifx_SRC_GTM',0,19,212,1,3
	.word	105831
	.byte	7
	.byte	'_Ifx_SRC_HSM',0,19,215,1,25,8,9
	.byte	'HSM',0,8
	.word	104693
	.byte	2,35,0,0,22
	.word	105857
	.byte	4
	.byte	'Ifx_SRC_HSM',0,19,218,1,3
	.word	105890
	.byte	7
	.byte	'_Ifx_SRC_LMU',0,19,221,1,25,4,9
	.byte	'SR',0,4
	.word	104226
	.byte	2,35,0,0,22
	.word	105916
	.byte	4
	.byte	'Ifx_SRC_LMU',0,19,224,1,3
	.word	105948
	.byte	7
	.byte	'_Ifx_SRC_PMU',0,19,227,1,25,4,9
	.byte	'SR',0,4
	.word	104226
	.byte	2,35,0,0,22
	.word	105974
	.byte	4
	.byte	'Ifx_SRC_PMU',0,19,230,1,3
	.word	106006
	.byte	7
	.byte	'_Ifx_SRC_QSPI',0,19,233,1,25,24,9
	.byte	'TX',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'RX',0,4
	.word	104226
	.byte	2,35,4,9
	.byte	'ERR',0,4
	.word	104226
	.byte	2,35,8,9
	.byte	'PT',0,4
	.word	104226
	.byte	2,35,12,9
	.byte	'HC',0,4
	.word	104226
	.byte	2,35,16,9
	.byte	'U',0,4
	.word	104226
	.byte	2,35,20,0,22
	.word	106032
	.byte	4
	.byte	'Ifx_SRC_QSPI',0,19,241,1,3
	.word	106125
	.byte	7
	.byte	'_Ifx_SRC_SCU',0,19,244,1,25,20,9
	.byte	'DTS',0,4
	.word	104226
	.byte	2,35,0,10,16
	.word	104226
	.byte	11,3,0,9
	.byte	'ERU',0,16
	.word	106184
	.byte	2,35,4,0,22
	.word	106152
	.byte	4
	.byte	'Ifx_SRC_SCU',0,19,248,1,3
	.word	106207
	.byte	7
	.byte	'_Ifx_SRC_SENT',0,19,251,1,25,16,9
	.byte	'SR',0,16
	.word	106184
	.byte	2,35,0,0,22
	.word	106233
	.byte	4
	.byte	'Ifx_SRC_SENT',0,19,254,1,3
	.word	106266
	.byte	7
	.byte	'_Ifx_SRC_SMU',0,19,129,2,25,12,10,12
	.word	104226
	.byte	11,2,0,9
	.byte	'SR',0,12
	.word	106312
	.byte	2,35,0,0,22
	.word	106293
	.byte	4
	.byte	'Ifx_SRC_SMU',0,19,132,2,3
	.word	106334
	.byte	7
	.byte	'_Ifx_SRC_STM',0,19,135,2,25,96,9
	.byte	'SR0',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'SR1',0,4
	.word	104226
	.byte	2,35,4,10,88
	.word	234
	.byte	11,87,0,9
	.byte	'reserved_8',0,88
	.word	106405
	.byte	2,35,8,0,22
	.word	106360
	.byte	4
	.byte	'Ifx_SRC_STM',0,19,140,2,3
	.word	106435
	.byte	7
	.byte	'_Ifx_SRC_VADCCG',0,19,143,2,25,192,2,9
	.byte	'SR0',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'SR1',0,4
	.word	104226
	.byte	2,35,4,9
	.byte	'SR2',0,4
	.word	104226
	.byte	2,35,8,9
	.byte	'SR3',0,4
	.word	104226
	.byte	2,35,12,10,176,2
	.word	234
	.byte	11,175,2,0,9
	.byte	'reserved_10',0,176,2
	.word	106536
	.byte	2,35,16,0,22
	.word	106461
	.byte	4
	.byte	'Ifx_SRC_VADCCG',0,19,150,2,3
	.word	106570
	.byte	7
	.byte	'_Ifx_SRC_VADCG',0,19,153,2,25,16,9
	.byte	'SR0',0,4
	.word	104226
	.byte	2,35,0,9
	.byte	'SR1',0,4
	.word	104226
	.byte	2,35,4,9
	.byte	'SR2',0,4
	.word	104226
	.byte	2,35,8,9
	.byte	'SR3',0,4
	.word	104226
	.byte	2,35,12,0,22
	.word	106599
	.byte	4
	.byte	'Ifx_SRC_VADCG',0,19,159,2,3
	.word	106673
	.byte	7
	.byte	'_Ifx_SRC_XBAR',0,19,162,2,25,4,9
	.byte	'SRC',0,4
	.word	104226
	.byte	2,35,0,0,22
	.word	106701
	.byte	4
	.byte	'Ifx_SRC_XBAR',0,19,165,2,3
	.word	106735
	.byte	7
	.byte	'_Ifx_SRC_GASCLIN',0,19,178,2,25,24,10,24
	.word	104286
	.byte	11,1,0,22
	.word	106785
	.byte	9
	.byte	'ASCLIN',0,24
	.word	106794
	.byte	2,35,0,0,22
	.word	106762
	.byte	4
	.byte	'Ifx_SRC_GASCLIN',0,19,181,2,3
	.word	106816
	.byte	7
	.byte	'_Ifx_SRC_GBCU',0,19,184,2,25,4,22
	.word	104373
	.byte	9
	.byte	'SPB',0,4
	.word	106866
	.byte	2,35,0,0,22
	.word	106846
	.byte	4
	.byte	'Ifx_SRC_GBCU',0,19,187,2,3
	.word	106885
	.byte	7
	.byte	'_Ifx_SRC_GCAN',0,19,190,2,25,96,10,64
	.word	104438
	.byte	11,0,0,22
	.word	106932
	.byte	9
	.byte	'CAN',0,64
	.word	106941
	.byte	2,35,0,10,32
	.word	104504
	.byte	11,0,0,22
	.word	106959
	.byte	9
	.byte	'CAN1',0,32
	.word	106968
	.byte	2,35,64,0,22
	.word	106912
	.byte	4
	.byte	'Ifx_SRC_GCAN',0,19,194,2,3
	.word	106988
	.byte	7
	.byte	'_Ifx_SRC_GCCU6',0,19,197,2,25,32,10,32
	.word	104572
	.byte	11,1,0,22
	.word	107036
	.byte	9
	.byte	'CCU6',0,32
	.word	107045
	.byte	2,35,0,0,22
	.word	107015
	.byte	4
	.byte	'Ifx_SRC_GCCU6',0,19,200,2,3
	.word	107065
	.byte	7
	.byte	'_Ifx_SRC_GCERBERUS',0,19,203,2,25,8,22
	.word	104670
	.byte	9
	.byte	'CERBERUS',0,8
	.word	107118
	.byte	2,35,0,0,22
	.word	107093
	.byte	4
	.byte	'Ifx_SRC_GCERBERUS',0,19,206,2,3
	.word	107142
	.byte	7
	.byte	'_Ifx_SRC_GCPU',0,19,209,2,25,32,10,32
	.word	104745
	.byte	11,0,0,22
	.word	107194
	.byte	9
	.byte	'CPU',0,32
	.word	107203
	.byte	2,35,0,0,22
	.word	107174
	.byte	4
	.byte	'Ifx_SRC_GCPU',0,19,212,2,3
	.word	107222
	.byte	7
	.byte	'_Ifx_SRC_GDMA',0,19,215,2,25,80,10,80
	.word	104825
	.byte	11,0,0,22
	.word	107269
	.byte	9
	.byte	'DMA',0,80
	.word	107278
	.byte	2,35,0,0,22
	.word	107249
	.byte	4
	.byte	'Ifx_SRC_GDMA',0,19,218,2,3
	.word	107297
	.byte	7
	.byte	'_Ifx_SRC_GEMEM',0,19,221,2,25,4,10,4
	.word	104916
	.byte	11,0,0,22
	.word	107345
	.byte	9
	.byte	'EMEM',0,4
	.word	107354
	.byte	2,35,0,0,22
	.word	107324
	.byte	4
	.byte	'Ifx_SRC_GEMEM',0,19,224,2,3
	.word	107374
	.byte	7
	.byte	'_Ifx_SRC_GERAY',0,19,227,2,25,80,10,80
	.word	104976
	.byte	11,0,0,22
	.word	107423
	.byte	9
	.byte	'ERAY',0,80
	.word	107432
	.byte	2,35,0,0,22
	.word	107402
	.byte	4
	.byte	'Ifx_SRC_GERAY',0,19,230,2,3
	.word	107452
	.byte	7
	.byte	'_Ifx_SRC_GETH',0,19,233,2,25,4,10,4
	.word	105139
	.byte	11,0,0,22
	.word	107500
	.byte	9
	.byte	'ETH',0,4
	.word	107509
	.byte	2,35,0,0,22
	.word	107480
	.byte	4
	.byte	'Ifx_SRC_GETH',0,19,236,2,3
	.word	107528
	.byte	7
	.byte	'_Ifx_SRC_GEVR',0,19,239,2,25,8,10,8
	.word	105197
	.byte	11,0,0,22
	.word	107575
	.byte	9
	.byte	'EVR',0,8
	.word	107584
	.byte	2,35,0,0,22
	.word	107555
	.byte	4
	.byte	'Ifx_SRC_GEVR',0,19,242,2,3
	.word	107603
	.byte	7
	.byte	'_Ifx_SRC_GFFT',0,19,245,2,25,12,10,12
	.word	105270
	.byte	11,0,0,22
	.word	107650
	.byte	9
	.byte	'FFT',0,12
	.word	107659
	.byte	2,35,0,0,22
	.word	107630
	.byte	4
	.byte	'Ifx_SRC_GFFT',0,19,248,2,3
	.word	107678
	.byte	7
	.byte	'_Ifx_SRC_GGPSR',0,19,251,2,25,128,12,10,128,12
	.word	105356
	.byte	11,0,0,22
	.word	107727
	.byte	9
	.byte	'GPSR',0,128,12
	.word	107737
	.byte	2,35,0,0,22
	.word	107705
	.byte	4
	.byte	'Ifx_SRC_GGPSR',0,19,254,2,3
	.word	107758
	.byte	7
	.byte	'_Ifx_SRC_GGPT12',0,19,129,3,25,48,10,48
	.word	105490
	.byte	11,0,0,22
	.word	107808
	.byte	9
	.byte	'GPT12',0,48
	.word	107817
	.byte	2,35,0,0,22
	.word	107786
	.byte	4
	.byte	'Ifx_SRC_GGPT12',0,19,132,3,3
	.word	107838
	.byte	7
	.byte	'_Ifx_SRC_GGTM',0,19,135,3,25,192,11,10,192,11
	.word	105644
	.byte	11,0,0,22
	.word	107888
	.byte	9
	.byte	'GTM',0,192,11
	.word	107898
	.byte	2,35,0,0,22
	.word	107867
	.byte	4
	.byte	'Ifx_SRC_GGTM',0,19,138,3,3
	.word	107918
	.byte	7
	.byte	'_Ifx_SRC_GHSM',0,19,141,3,25,8,10,8
	.word	105857
	.byte	11,0,0,22
	.word	107965
	.byte	9
	.byte	'HSM',0,8
	.word	107974
	.byte	2,35,0,0,22
	.word	107945
	.byte	4
	.byte	'Ifx_SRC_GHSM',0,19,144,3,3
	.word	107993
	.byte	7
	.byte	'_Ifx_SRC_GLMU',0,19,147,3,25,4,10,4
	.word	105916
	.byte	11,0,0,22
	.word	108040
	.byte	9
	.byte	'LMU',0,4
	.word	108049
	.byte	2,35,0,0,22
	.word	108020
	.byte	4
	.byte	'Ifx_SRC_GLMU',0,19,150,3,3
	.word	108068
	.byte	7
	.byte	'_Ifx_SRC_GPMU',0,19,153,3,25,8,10,8
	.word	105974
	.byte	11,1,0,22
	.word	108115
	.byte	9
	.byte	'PMU',0,8
	.word	108124
	.byte	2,35,0,0,22
	.word	108095
	.byte	4
	.byte	'Ifx_SRC_GPMU',0,19,156,3,3
	.word	108143
	.byte	7
	.byte	'_Ifx_SRC_GQSPI',0,19,159,3,25,96,10,96
	.word	106032
	.byte	11,3,0,22
	.word	108191
	.byte	9
	.byte	'QSPI',0,96
	.word	108200
	.byte	2,35,0,0,22
	.word	108170
	.byte	4
	.byte	'Ifx_SRC_GQSPI',0,19,162,3,3
	.word	108220
	.byte	7
	.byte	'_Ifx_SRC_GSCU',0,19,165,3,25,20,22
	.word	106152
	.byte	9
	.byte	'SCU',0,20
	.word	108268
	.byte	2,35,0,0,22
	.word	108248
	.byte	4
	.byte	'Ifx_SRC_GSCU',0,19,168,3,3
	.word	108287
	.byte	7
	.byte	'_Ifx_SRC_GSENT',0,19,171,3,25,16,10,16
	.word	106233
	.byte	11,0,0,22
	.word	108335
	.byte	9
	.byte	'SENT',0,16
	.word	108344
	.byte	2,35,0,0,22
	.word	108314
	.byte	4
	.byte	'Ifx_SRC_GSENT',0,19,174,3,3
	.word	108364
	.byte	7
	.byte	'_Ifx_SRC_GSMU',0,19,177,3,25,12,10,12
	.word	106293
	.byte	11,0,0,22
	.word	108412
	.byte	9
	.byte	'SMU',0,12
	.word	108421
	.byte	2,35,0,0,22
	.word	108392
	.byte	4
	.byte	'Ifx_SRC_GSMU',0,19,180,3,3
	.word	108440
	.byte	7
	.byte	'_Ifx_SRC_GSTM',0,19,183,3,25,96,10,96
	.word	106360
	.byte	11,0,0,22
	.word	108487
	.byte	9
	.byte	'STM',0,96
	.word	108496
	.byte	2,35,0,0,22
	.word	108467
	.byte	4
	.byte	'Ifx_SRC_GSTM',0,19,186,3,3
	.word	108515
	.byte	7
	.byte	'_Ifx_SRC_GVADC',0,19,189,3,25,224,4,10,64
	.word	106599
	.byte	11,3,0,22
	.word	108564
	.byte	9
	.byte	'G',0,64
	.word	108573
	.byte	2,35,0,10,224,1
	.word	234
	.byte	11,223,1,0,9
	.byte	'reserved_40',0,224,1
	.word	108589
	.byte	2,35,64,10,192,2
	.word	106461
	.byte	11,0,0,22
	.word	108622
	.byte	9
	.byte	'CG',0,192,2
	.word	108632
	.byte	3,35,160,2,0,22
	.word	108542
	.byte	4
	.byte	'Ifx_SRC_GVADC',0,19,194,3,3
	.word	108652
	.byte	7
	.byte	'_Ifx_SRC_GXBAR',0,19,197,3,25,4,22
	.word	106701
	.byte	9
	.byte	'XBAR',0,4
	.word	108701
	.byte	2,35,0,0,22
	.word	108680
	.byte	4
	.byte	'Ifx_SRC_GXBAR',0,19,200,3,3
	.word	108721
	.byte	4
	.byte	'Dma_StatusType',0,11,121,22
	.word	601
	.byte	4
	.byte	'Dma_ErrorStatusType',0,11,141,1,22
	.word	601
	.byte	4
	.byte	'Dma_ChannelType',0,11,149,2,2
	.word	6983
	.byte	4
	.byte	'Spi_NumberOfDataType',0,10,169,7,16
	.word	265
	.byte	4
	.byte	'Spi_ChannelType',0,10,177,7,15
	.word	234
	.byte	4
	.byte	'Spi_JobType',0,10,185,7,16
	.word	265
	.byte	4
	.byte	'Spi_SequenceType',0,10,193,7,15
	.word	234
	.byte	4
	.byte	'Spi_ChannelConfigType',0,10,144,8,3
	.word	6138
	.byte	4
	.byte	'Spi_DelayConfigType',0,10,162,8,16
	.word	302
	.byte	4
	.byte	'Spi_HWUnitType',0,10,170,8,15
	.word	234
	.byte	4
	.byte	'Spi_JobConfigType',0,10,241,8,2
	.word	6291
	.byte	4
	.byte	'Spi_SequenceConfigType',0,10,157,9,2
	.word	6653
	.byte	4
	.byte	'Spi_DmaConfigType',0,10,168,9,2
	.word	6959
	.byte	4
	.byte	'Spi_HWModuleConfigType',0,10,187,9,2
	.word	6846
	.byte	4
	.byte	'Spi_BaudrateEconType',0,10,222,9,2
	.word	9270
	.byte	4
	.byte	'Spi_ConfigType',0,10,156,10,3
	.word	6117
	.byte	7
	.byte	'Spi_LastChannelDataType',0,10,161,10,16,8,3
	.word	302
	.byte	9
	.byte	'LastDataPtr',0,4
	.word	109214
	.byte	2,35,0,9
	.byte	'DataWidth',0,2
	.word	265
	.byte	2,35,4,0,4
	.byte	'Spi_LastChannelDataType',0,10,165,10,2
	.word	109184
	.byte	10,40
	.word	6117
	.byte	11,0,0,8
	.word	109293
	.byte	23
	.byte	'Spi_ConfigRoot',0,10,181,11,29
	.word	109302
	.byte	1,1,4
	.byte	'Adc_ChannelType',0,8,227,2,15
	.word	234
	.byte	4
	.byte	'Adc_GroupType',0,8,233,2,16
	.word	265
	.byte	4
	.byte	'Adc_ValueGroupType',0,8,247,2,16
	.word	265
	.byte	4
	.byte	'Adc_PrescaleType',0,8,144,3,16
	.word	302
	.byte	4
	.byte	'Adc_TriggerSourceType',0,8,200,3,15
	.word	234
	.byte	4
	.byte	'Adc_GroupConvModeType',0,8,208,3,15
	.word	234
	.byte	4
	.byte	'Adc_GroupPriorityType',0,8,215,3,15
	.word	234
	.byte	4
	.byte	'Adc_GroupDefType',0,8,130,4,25
	.word	234
	.byte	4
	.byte	'Adc_StreamNumSampleType',0,8,135,4,15
	.word	234
	.byte	4
	.byte	'Adc_StreamBufferModeType',0,8,148,4,15
	.word	234
	.byte	4
	.byte	'Adc_TriggSrcArbLevelType',0,8,179,4,16
	.word	302
	.byte	7
	.byte	'Adc_TriggSrcData',0,8,205,4,16,4,9
	.byte	'GrpId',0,2
	.word	265
	.byte	2,35,0,9
	.byte	'GrpPriority',0,1
	.word	234
	.byte	2,35,2,9
	.byte	'IsrDoNothing',0,1
	.word	234
	.byte	2,35,3,0,4
	.byte	'Adc_TriggSrcDataType',0,8,223,4,2
	.word	109655
	.byte	7
	.byte	'Adc_GroupData',0,8,232,4,16,32,10,16
	.word	234
	.byte	11,15,0,9
	.byte	'GrpChannels',0,16
	.word	109787
	.byte	2,35,0,9
	.byte	'GrpChannelRes',0,16
	.word	2568
	.byte	2,35,16,0,4
	.byte	'Adc_GroupDataType',0,8,250,4,2
	.word	109767
	.byte	4
	.byte	'Adc_HwUnitCfgType',0,8,179,6,2
	.word	4401
	.byte	4
	.byte	'Adc_GroupCfgType',0,8,129,7,3
	.word	4838
	.byte	4
	.byte	'Adc_ChannelCfgType',0,8,160,7,3
	.word	4595
	.byte	4
	.byte	'Adc_KernelConfigType',0,8,183,7,3
	.word	4374
	.byte	4
	.byte	'Adc_GlobalCfgType',0,8,244,7,3
	.word	5106
	.byte	4
	.byte	'Adc_ConfigType',0,8,134,8,3
	.word	4353
	.byte	10,16
	.word	4353
	.byte	11,0,0,8
	.word	110030
	.byte	23
	.byte	'Adc_ConfigRoot',0,8,142,8,29
	.word	110039
	.byte	1,1,4
	.byte	'PduIdType',0,20,72,22
	.word	234
	.byte	4
	.byte	'PduLengthType',0,20,76,22
	.word	265
	.byte	4
	.byte	'Can_IdType',0,21,46,16
	.word	302
	.byte	7
	.byte	'Can_TxHwObjectConfigType',0,22,218,3,16,2,9
	.byte	'MsgObjId',0,1
	.word	234
	.byte	2,35,0,9
	.byte	'HwControllerId',0,1
	.word	234
	.byte	2,35,1,0,4
	.byte	'Can_TxHwObjectConfigType',0,22,236,3,3
	.word	110129
	.byte	7
	.byte	'Can_RxHwObjectConfigType',0,22,241,3,16,12,9
	.byte	'MaskRef',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'MsgId',0,4
	.word	302
	.byte	2,35,4,9
	.byte	'MsgObjId',0,1
	.word	234
	.byte	2,35,8,9
	.byte	'HwControllerId',0,1
	.word	234
	.byte	2,35,9,0,4
	.byte	'Can_RxHwObjectConfigType',0,22,131,4,3
	.word	110237
	.byte	7
	.byte	'Can_ControllerMOMapConfigType',0,22,165,4,16,4,10,4
	.word	234
	.byte	11,3,0,9
	.byte	'ControllerMOMap',0,4
	.word	110413
	.byte	2,35,0,0,4
	.byte	'Can_ControllerMOMapConfigType',0,22,168,4,3
	.word	110377
	.byte	7
	.byte	'Can_NPCRValueType',0,22,172,4,16,2,9
	.byte	'Can_NPCRValue',0,2
	.word	265
	.byte	2,35,0,0,4
	.byte	'Can_NPCRValueType',0,22,175,4,3
	.word	110487
	.byte	7
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,22,178,4,16,6,9
	.byte	'CanControllerBaudrate',0,4
	.word	302
	.byte	2,35,0,9
	.byte	'CanControllerBaudrateCfg',0,2
	.word	265
	.byte	2,35,4,0,4
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,22,182,4,3
	.word	110562
	.byte	7
	.byte	'Can_BaudrateConfigPtrType',0,22,185,4,16,4,8
	.word	110562
	.byte	3
	.word	110759
	.byte	9
	.byte	'Can_kBaudrateConfigPtr',0,4
	.word	110764
	.byte	2,35,0,0,4
	.byte	'Can_BaudrateConfigPtrType',0,22,188,4,3
	.word	110727
	.byte	7
	.byte	'Can_FDConfigParamType',0,22,193,4,16,6,9
	.byte	'CanControllerFDBaudrate',0,2
	.word	265
	.byte	2,35,0,9
	.byte	'CanControllerTxDelayComp',0,2
	.word	265
	.byte	2,35,2,9
	.byte	'CanControllerTxBRS',0,1
	.word	234
	.byte	2,35,4,0,4
	.byte	'Can_FDConfigParamType',0,22,198,4,3
	.word	110837
	.byte	7
	.byte	'Can_FDConfigParamPtrType',0,22,200,4,16,4,8
	.word	110837
	.byte	3
	.word	111023
	.byte	9
	.byte	'Can_kFDConfigParamPtr',0,4
	.word	111028
	.byte	2,35,0,0,4
	.byte	'Can_FDConfigParamPtrType',0,22,203,4,3
	.word	110992
	.byte	7
	.byte	'Can_EventHandlingType',0,22,210,4,16,4,9
	.byte	'CanEventType',0,4
	.word	110413
	.byte	2,35,0,0,4
	.byte	'Can_EventHandlingType',0,22,213,4,3
	.word	111099
	.byte	3
	.word	234
	.byte	4
	.byte	'Uart_MemPtrType',0,9,231,3,16
	.word	111181
	.byte	4
	.byte	'Uart_SizeType',0,9,240,3,16
	.word	265
	.byte	4
	.byte	'UartNotifType',0,9,148,4,2
	.word	5401
	.byte	4
	.byte	'Uart_ChannelType',0,9,183,4,2
	.word	5378
	.byte	4
	.byte	'Uart_ConfigType',0,9,195,4,2
	.word	5356
	.byte	16
	.byte	'Uart_StateType',0,9,228,4,14,1,17
	.byte	'UART_UNINITIALISED',0,0,17
	.byte	'UART_INITIALISED',0,1,17
	.byte	'UART_OPERATION_IN_PROGRESS',0,2,0,4
	.byte	'Uart_StateType',0,9,233,4,2
	.word	111308
	.byte	10,8
	.word	5356
	.byte	11,0,0,8
	.word	111423
	.byte	23
	.byte	'Uart_ConfigRoot',0,9,133,5,30
	.word	111432
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,38,0
	.byte	73,19,0,0,9,13,0,3,8,11,15,73,19,56,9,0,0,10,1,1,11,15,73,19,0,0,11,33,0,47,15,0,0,12,19,1,58,15,59,15
	.byte	57,15,11,15,0,0,13,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,14,23,1,3,8,58,15,59,15,57,15,11,15,0,0,15
	.byte	21,1,54,15,39,12,0,0,16,4,1,3,8,58,15,59,15,57,15,11,15,0,0,17,40,0,3,8,28,13,0,0,18,5,0,73,19,0,0,19
	.byte	21,0,54,15,39,12,0,0,20,4,1,58,15,59,15,57,15,11,15,0,0,21,23,1,58,15,59,15,57,15,11,15,0,0,22,53,0,73
	.byte	19,0,0,23,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc',0
	.byte	0
	.byte	'..\\mcal_cfg\\EcuM_PBCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'EcuM_Cfg.h',0,2,0,0
	.byte	'Mcu.h',0,1,0,0
	.byte	'Gtm.h',0,1,0,0
	.byte	'Dio.h',0,1,0,0
	.byte	'Port.h',0,1,0,0
	.byte	'Adc.h',0,1,0,0
	.byte	'Uart.h',0,1,0,0
	.byte	'Spi.h',0,3,0,0
	.byte	'Mcal_DmaLib.h',0,1,0,0
	.byte	'Dma.h',0,4,0,0
	.byte	'Mcal_TcLib.h',0,1,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IfxGtm_regdef.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'ComStack_Types.h',0,1,0,0
	.byte	'Can_GeneralTypes.h',0,1,0,0
	.byte	'Can_17_MCanP.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('EcuM_ConfigAlternative')
	.sect	'.debug_info'
.L6:
	.word	216
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\EcuM_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'EcuM_ConfigAlternative',0,1,154,1,23
	.word	.L8
	.byte	1,5,3
	.word	EcuM_ConfigAlternative
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_ConfigAlternative')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0

; ..\mcal_cfg\EcuM_PBCfg.c	     1  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	     2  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_cfg\EcuM_PBCfg.c	     4  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	     5  ** All rights reserved.                                                       **
; ..\mcal_cfg\EcuM_PBCfg.c	     6  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_cfg\EcuM_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_cfg\EcuM_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_cfg\EcuM_PBCfg.c	    10  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    11  ********************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	    12  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    13  **   $FILENAME   : EcuM_PBCfg.c $                                             **
; ..\mcal_cfg\EcuM_PBCfg.c	    14  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    15  **   $CC VERSION : \main\39 $                                                 **
; ..\mcal_cfg\EcuM_PBCfg.c	    16  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    17  **   DATE, TIME: 2020-07-10, 14:56:09                                         **
; ..\mcal_cfg\EcuM_PBCfg.c	    18  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    19  **   GENERATOR : Build b141014-0350                                           **
; ..\mcal_cfg\EcuM_PBCfg.c	    20  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    21  **   AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\mcal_cfg\EcuM_PBCfg.c	    22  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    23  **   VENDOR    : Infineon Technologies                                        **
; ..\mcal_cfg\EcuM_PBCfg.c	    24  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    25  **   DESCRIPTION  : EcuM configuration generated from ECU configuration file  **
; ..\mcal_cfg\EcuM_PBCfg.c	    26  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    27  **   SPECIFICATION(S) : AUTOSAR_SWS_ECU_StateManager.pdf, AS4.0               **
; ..\mcal_cfg\EcuM_PBCfg.c	    28  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    29  **   MAY BE CHANGED BY USER [yes/no]: NO                                      **
; ..\mcal_cfg\EcuM_PBCfg.c	    30  **                                                                            **
; ..\mcal_cfg\EcuM_PBCfg.c	    31  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	    32  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	    33  **                      Includes                                              **
; ..\mcal_cfg\EcuM_PBCfg.c	    34  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	    35  #include "EcuM.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    36  
; ..\mcal_cfg\EcuM_PBCfg.c	    37  #include "Mcu.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    38  
; ..\mcal_cfg\EcuM_PBCfg.c	    39  #ifdef ECUM_USES_GPT
; ..\mcal_cfg\EcuM_PBCfg.c	    40  #include "Gpt.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    41  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    42  #ifdef ECUM_USES_PORT
; ..\mcal_cfg\EcuM_PBCfg.c	    43  #include "Port.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    44  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    45  #ifdef ECUM_USES_DIO
; ..\mcal_cfg\EcuM_PBCfg.c	    46  #include "Dio.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    47  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    48  #ifdef ECUM_USES_DMA
; ..\mcal_cfg\EcuM_PBCfg.c	    49  #include "Dma.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    50  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    51  #ifdef ECUM_USES_SPI
; ..\mcal_cfg\EcuM_PBCfg.c	    52  #include "Spi.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    53  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    54  #ifdef ECUM_USES_ADC
; ..\mcal_cfg\EcuM_PBCfg.c	    55  #include "Adc.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    56  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    57  #ifdef ECUM_USES_FLS
; ..\mcal_cfg\EcuM_PBCfg.c	    58  #include "Fls_17_Pmu.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    59  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    60  #ifdef ECUM_USES_FEE
; ..\mcal_cfg\EcuM_PBCfg.c	    61  #include "Fee.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    62  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    63  #ifdef ECUM_USES_CAN
; ..\mcal_cfg\EcuM_PBCfg.c	    64  #include "Can_17_MCanP.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    65  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    66  #ifdef ECUM_USES_PWM
; ..\mcal_cfg\EcuM_PBCfg.c	    67  #include "Pwm_17_Gtm.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    68  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    69  #ifdef ECUM_USES_FADC
; ..\mcal_cfg\EcuM_PBCfg.c	    70  #include "Fadc.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    71  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    72  #ifdef ECUM_USES_ICU
; ..\mcal_cfg\EcuM_PBCfg.c	    73  #include "Icu_17_GtmCcu6.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    74  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    75  #ifdef ECUM_USES_WDG
; ..\mcal_cfg\EcuM_PBCfg.c	    76  #include "Wdg_17_Scu.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    77  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    78  #ifdef ECUM_USES_MLI
; ..\mcal_cfg\EcuM_PBCfg.c	    79  #include "Mli.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    80  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    81  #ifdef ECUM_USES_SCI
; ..\mcal_cfg\EcuM_PBCfg.c	    82  #include "Sci.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    83  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    84  #ifdef ECUM_USES_MCHK
; ..\mcal_cfg\EcuM_PBCfg.c	    85  #include "Mchk.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    86  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    87  #ifdef ECUM_USES_MSC
; ..\mcal_cfg\EcuM_PBCfg.c	    88  #include "Msc.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    89  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    90  #ifdef ECUM_USES_FR_17_ERAY
; ..\mcal_cfg\EcuM_PBCfg.c	    91  #include "Fr_17_Eray.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    92  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    93  #ifdef ECUM_USES_LIN
; ..\mcal_cfg\EcuM_PBCfg.c	    94  #include "Lin_17_AscLin.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    95  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    96  #ifdef ECUM_USES_UART
; ..\mcal_cfg\EcuM_PBCfg.c	    97  #include "Uart.h"
; ..\mcal_cfg\EcuM_PBCfg.c	    98  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	    99  #ifdef ECUM_USES_ETH
; ..\mcal_cfg\EcuM_PBCfg.c	   100  #include "Eth_17_EthMac.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   101  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   102  #ifdef ECUM_USES_CRC
; ..\mcal_cfg\EcuM_PBCfg.c	   103  #include "Crc.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   104  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   105  #ifdef ECUM_USES_RAMTST
; ..\mcal_cfg\EcuM_PBCfg.c	   106  #include "RamTst_Api.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   107  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   108  #ifdef ECUM_USES_CANTRCV_17_6250GV33
; ..\mcal_cfg\EcuM_PBCfg.c	   109  #include "CanTrcv_17_6250GV33.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   110  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   111  #ifdef ECUM_USES_CANTRCV_17_6251G
; ..\mcal_cfg\EcuM_PBCfg.c	   112  #include "CanTrcv_17_6251G.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   113  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   114  #ifdef ECUM_USES_FLSLOADER
; ..\mcal_cfg\EcuM_PBCfg.c	   115  #include "FlsLoader.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   116  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   117  #ifdef ECUM_USES_SENT
; ..\mcal_cfg\EcuM_PBCfg.c	   118  #include "Sent.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   119  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   120  #ifdef ECUM_USES_IOM
; ..\mcal_cfg\EcuM_PBCfg.c	   121  #include "Iom.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   122  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   123  #ifdef ECUM_USES_HSSL
; ..\mcal_cfg\EcuM_PBCfg.c	   124  #include "Hssl.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   125  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   126  #ifdef ECUM_USES_DSADC
; ..\mcal_cfg\EcuM_PBCfg.c	   127  #include "Dsadc.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   128  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   129  #ifdef ECUM_USES_SMU
; ..\mcal_cfg\EcuM_PBCfg.c	   130  #include "Smu.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   131  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   132  #ifdef ECUM_USES_I2C
; ..\mcal_cfg\EcuM_PBCfg.c	   133  #include "I2c.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   134  #endif
; ..\mcal_cfg\EcuM_PBCfg.c	   135  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	   136  **                      Private Macro Definitions                             **
; ..\mcal_cfg\EcuM_PBCfg.c	   137  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	   138  
; ..\mcal_cfg\EcuM_PBCfg.c	   139  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	   140  **                      Private Type Definitions                              **
; ..\mcal_cfg\EcuM_PBCfg.c	   141  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	   142  
; ..\mcal_cfg\EcuM_PBCfg.c	   143  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	   144  **                      Private Function Declarations                         **
; ..\mcal_cfg\EcuM_PBCfg.c	   145  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	   146  
; ..\mcal_cfg\EcuM_PBCfg.c	   147  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	   148  **                      Global Constant Definitions                           **
; ..\mcal_cfg\EcuM_PBCfg.c	   149  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	   150  #define ECUM_START_SEC_POSTBUILDCFG
; ..\mcal_cfg\EcuM_PBCfg.c	   151  #include "MemMap.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   152  
; ..\mcal_cfg\EcuM_PBCfg.c	   153  
; ..\mcal_cfg\EcuM_PBCfg.c	   154  const EcuM_ConfigType EcuM_ConfigAlternative[] = 
; ..\mcal_cfg\EcuM_PBCfg.c	   155  {
; ..\mcal_cfg\EcuM_PBCfg.c	   156    {
; ..\mcal_cfg\EcuM_PBCfg.c	   157      /* Identifier of post-build time configuration */
; ..\mcal_cfg\EcuM_PBCfg.c	   158      0,
; ..\mcal_cfg\EcuM_PBCfg.c	   159      /* Hash Identifier of pre compile time configuration */
; ..\mcal_cfg\EcuM_PBCfg.c	   160      0xffffffff,
; ..\mcal_cfg\EcuM_PBCfg.c	   161  
; ..\mcal_cfg\EcuM_PBCfg.c	   162      &Mcu_ConfigRoot[0],
; ..\mcal_cfg\EcuM_PBCfg.c	   163      &Dio_ConfigRoot[0],
; ..\mcal_cfg\EcuM_PBCfg.c	   164      &Port_ConfigRoot[0],
; ..\mcal_cfg\EcuM_PBCfg.c	   165      &Adc_ConfigRoot[0],
; ..\mcal_cfg\EcuM_PBCfg.c	   166  
; ..\mcal_cfg\EcuM_PBCfg.c	   167      &Uart_ConfigRoot[0],
; ..\mcal_cfg\EcuM_PBCfg.c	   168      &Spi_ConfigRoot[0],
; ..\mcal_cfg\EcuM_PBCfg.c	   169      &Dma_ConfigRoot[0],
; ..\mcal_cfg\EcuM_PBCfg.c	   170      0xFF                  /* EcuM Internal Data */
; ..\mcal_cfg\EcuM_PBCfg.c	   171    }
; ..\mcal_cfg\EcuM_PBCfg.c	   172  };
; ..\mcal_cfg\EcuM_PBCfg.c	   173  
; ..\mcal_cfg\EcuM_PBCfg.c	   174  
; ..\mcal_cfg\EcuM_PBCfg.c	   175  
; ..\mcal_cfg\EcuM_PBCfg.c	   176  
; ..\mcal_cfg\EcuM_PBCfg.c	   177  #define ECUM_STOP_SEC_POSTBUILDCFG
; ..\mcal_cfg\EcuM_PBCfg.c	   178  #include "MemMap.h"
; ..\mcal_cfg\EcuM_PBCfg.c	   179  
; ..\mcal_cfg\EcuM_PBCfg.c	   180  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	   181  **                      Global Variable Definitions                           **
; ..\mcal_cfg\EcuM_PBCfg.c	   182  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	   183  
; ..\mcal_cfg\EcuM_PBCfg.c	   184  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	   185  **                      Private Constant Definitions                          **
; ..\mcal_cfg\EcuM_PBCfg.c	   186  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	   187  
; ..\mcal_cfg\EcuM_PBCfg.c	   188  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	   189  **                      Private Variable Definitions                          **
; ..\mcal_cfg\EcuM_PBCfg.c	   190  *******************************************************************************/
; ..\mcal_cfg\EcuM_PBCfg.c	   191  
; ..\mcal_cfg\EcuM_PBCfg.c	   192  /*******************************************************************************
; ..\mcal_cfg\EcuM_PBCfg.c	   193  **                      Global Function Definitions                           **
; ..\mcal_cfg\EcuM_PBCfg.c	   194  *******************************************************************************/

	; Module end
