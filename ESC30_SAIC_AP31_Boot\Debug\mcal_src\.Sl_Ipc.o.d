mcal_src\Sl_Ipc.o :	..\mcal_src\Sl_Ipc.c
..\mcal_src\Sl_Ipc.c :
mcal_src\Sl_Ipc.o :	..\mcal_src\Platform_Types.h
..\mcal_src\Platform_Types.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Compiler_Cfg.h
..\mcal_src\Compiler_Cfg.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Mcal_TcLib.h
..\mcal_src\Mcal_TcLib.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Std_Types.h
..\mcal_src\Std_Types.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Compiler.h
..\mcal_src\Compiler.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Mcal_Compiler.h
..\mcal_src\Mcal_Compiler.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Mcal_Options.h
..\mcal_src\Mcal_Options.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Sl_Ipc.h
..\mcal_src\Sl_Ipc.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Mcal.h
..\mcal_src\Mcal.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Mcal_TcLib.h
..\mcal_src\Mcal_TcLib.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\Mcal_WdgLib.h
..\mcal_src\Mcal_WdgLib.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
mcal_src\Sl_Ipc.o :	..\mcal_src\MemMap.h
..\mcal_src\MemMap.h :
