	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc20020a -c99 --dep-file=mcal_src\\.Can_17_MCanP_Platform.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Can_17_MCanP_Platform.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Can_17_MCanP_Platform.src ..\\mcal_src\\Can_17_MCanP_Platform.c"
	.compiler_name		"ctc"
	.name	"Can_17_MCanP_Platform"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Can_lFrameInitialise')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Can_lFrameInitialise

; ..\mcal_src\Can_17_MCanP_Platform.c	     1  /******************************************************************************
; ..\mcal_src\Can_17_MCanP_Platform.c	     2  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	     3  ** Copyright (C) Infineon Technologies (2016)                                **
; ..\mcal_src\Can_17_MCanP_Platform.c	     4  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Can_17_MCanP_Platform.c	     6  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Can_17_MCanP_Platform.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Can_17_MCanP_Platform.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Can_17_MCanP_Platform.c	    10  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    11  *******************************************************************************
; ..\mcal_src\Can_17_MCanP_Platform.c	    12  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    13  **  $FILENAME   : Can_17_MCanP_Platform.c $                                  **
; ..\mcal_src\Can_17_MCanP_Platform.c	    14  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    15  **  $CC VERSION : \main\5 $                                                  **
; ..\mcal_src\Can_17_MCanP_Platform.c	    16  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    17  **  $DATE       : 2016-07-22 $                                               **
; ..\mcal_src\Can_17_MCanP_Platform.c	    18  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    19  **  AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\mcal_src\Can_17_MCanP_Platform.c	    20  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    21  **  VENDOR    : Infineon Technologies                                        **
; ..\mcal_src\Can_17_MCanP_Platform.c	    22  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    23  **  DESCRIPTION  : This file contains                                        **
; ..\mcal_src\Can_17_MCanP_Platform.c	    24  **                - CAN driver API implementation                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	    25  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    26  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Can_17_MCanP_Platform.c	    27  **                                                                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    28  ******************************************************************************/
; ..\mcal_src\Can_17_MCanP_Platform.c	    29  /*******************************************************************************
; ..\mcal_src\Can_17_MCanP_Platform.c	    30  **                      Includes                                              **
; ..\mcal_src\Can_17_MCanP_Platform.c	    31  *******************************************************************************/
; ..\mcal_src\Can_17_MCanP_Platform.c	    32  
; ..\mcal_src\Can_17_MCanP_Platform.c	    33  /* Register definition file for Aurix target */
; ..\mcal_src\Can_17_MCanP_Platform.c	    34  #include "IfxCan_reg.h"
; ..\mcal_src\Can_17_MCanP_Platform.c	    35  #include "IfxSrc_reg.h"
; ..\mcal_src\Can_17_MCanP_Platform.c	    36  /*for proctected mode support macros */
; ..\mcal_src\Can_17_MCanP_Platform.c	    37  #include "Can_17_MCanP.h"
; ..\mcal_src\Can_17_MCanP_Platform.c	    38  /*including header file for Can_17_MCanP_Platform.c*/
; ..\mcal_src\Can_17_MCanP_Platform.c	    39  #include "Can_17_MCanP_Platform.h"
; ..\mcal_src\Can_17_MCanP_Platform.c	    40  
; ..\mcal_src\Can_17_MCanP_Platform.c	    41  
; ..\mcal_src\Can_17_MCanP_Platform.c	    42  /*******************************************************************************
; ..\mcal_src\Can_17_MCanP_Platform.c	    43  **                      Private Macro Definitions                             **
; ..\mcal_src\Can_17_MCanP_Platform.c	    44  *******************************************************************************/
; ..\mcal_src\Can_17_MCanP_Platform.c	    45  #define CAN_ONE                         (1U)
; ..\mcal_src\Can_17_MCanP_Platform.c	    46  #define CAN_17_MCANP_START_SEC_CONST_32BIT
; ..\mcal_src\Can_17_MCanP_Platform.c	    47  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directive is
; ..\mcal_src\Can_17_MCanP_Platform.c	    48   allowed only for MemMap.h*/
; ..\mcal_src\Can_17_MCanP_Platform.c	    49  #include "MemMap.h"
; ..\mcal_src\Can_17_MCanP_Platform.c	    50  
; ..\mcal_src\Can_17_MCanP_Platform.c	    51  #ifdef CAN_MOD_R_AVAILABLE
; ..\mcal_src\Can_17_MCanP_Platform.c	    52  #if (CAN_NUM_CONTROLLERS_IN_KERNEL1 == 3U )
; ..\mcal_src\Can_17_MCanP_Platform.c	    53  /* CAN base address Kernel wise */
; ..\mcal_src\Can_17_MCanP_Platform.c	    54  Ifx_CAN* const CAN_KER[] = { &MODULE_CAN, &MODULE_CAN1 };
; ..\mcal_src\Can_17_MCanP_Platform.c	    55  #else
; ..\mcal_src\Can_17_MCanP_Platform.c	    56  /* CAN base address Kernel wise */
; ..\mcal_src\Can_17_MCanP_Platform.c	    57  Ifx_CAN* const CAN_KER[] = { &MODULE_CAN, &MODULE_CANR };
; ..\mcal_src\Can_17_MCanP_Platform.c	    58  #endif
; ..\mcal_src\Can_17_MCanP_Platform.c	    59  #else
; ..\mcal_src\Can_17_MCanP_Platform.c	    60  /* CAN base address Kernel wise */
; ..\mcal_src\Can_17_MCanP_Platform.c	    61  Ifx_CAN* const CAN_KER[] = { &MODULE_CAN };
; ..\mcal_src\Can_17_MCanP_Platform.c	    62  #endif
; ..\mcal_src\Can_17_MCanP_Platform.c	    63  
; ..\mcal_src\Can_17_MCanP_Platform.c	    64  #define CAN_17_MCANP_STOP_SEC_CONST_32BIT
; ..\mcal_src\Can_17_MCanP_Platform.c	    65  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directive is
; ..\mcal_src\Can_17_MCanP_Platform.c	    66   allowed only for MemMap.h*/
; ..\mcal_src\Can_17_MCanP_Platform.c	    67  #include "MemMap.h"
; ..\mcal_src\Can_17_MCanP_Platform.c	    68  
; ..\mcal_src\Can_17_MCanP_Platform.c	    69  /*******************************************************************************
; ..\mcal_src\Can_17_MCanP_Platform.c	    70  **                      Global Function Definitions                           **
; ..\mcal_src\Can_17_MCanP_Platform.c	    71  *******************************************************************************/
; ..\mcal_src\Can_17_MCanP_Platform.c	    72  
; ..\mcal_src\Can_17_MCanP_Platform.c	    73  /* Memory map of the CAN driver code */
; ..\mcal_src\Can_17_MCanP_Platform.c	    74  #define CAN_17_MCANP_START_SEC_CODE
; ..\mcal_src\Can_17_MCanP_Platform.c	    75  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directive is
; ..\mcal_src\Can_17_MCanP_Platform.c	    76   allowed only for MemMap.h*/
; ..\mcal_src\Can_17_MCanP_Platform.c	    77  #include "MemMap.h"
; ..\mcal_src\Can_17_MCanP_Platform.c	    78  /*******************************************************************************
; ..\mcal_src\Can_17_MCanP_Platform.c	    79  ** Syntax           :                  void Can_lFrameInitialise              **
; ..\mcal_src\Can_17_MCanP_Platform.c	    80  **                                           (                                **
; ..\mcal_src\Can_17_MCanP_Platform.c	    81  **                                             uint8 KerIdx,                  **
; ..\mcal_src\Can_17_MCanP_Platform.c	    82  **                                             uint8 HwCtrlIDKer              **
; ..\mcal_src\Can_17_MCanP_Platform.c	    83  **                                             uint16 CanControllerFDBaudrate **
; ..\mcal_src\Can_17_MCanP_Platform.c	    84  **                                             uint16 CanControllerTxDelayComp**
; ..\mcal_src\Can_17_MCanP_Platform.c	    85  **                                           )                                **
; ..\mcal_src\Can_17_MCanP_Platform.c	    86  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	    87  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	    88  ** Service ID       : None                                                    **
; ..\mcal_src\Can_17_MCanP_Platform.c	    89  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	    90  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Can_17_MCanP_Platform.c	    91  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	    92  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Can_17_MCanP_Platform.c	    93  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	    94  ** Parameters (in)  : KerIdx - Associated CAN Kernal Id                       **
; ..\mcal_src\Can_17_MCanP_Platform.c	    95  **                    HwCtrlIDKer - Associated CAN Hardwarecontroller ID      **
; ..\mcal_src\Can_17_MCanP_Platform.c	    96  **                    CanControllerFDBaudrate                                 **
; ..\mcal_src\Can_17_MCanP_Platform.c	    97  **                      CanControllerTxDelayComp                              **
; ..\mcal_src\Can_17_MCanP_Platform.c	    98  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	    99  ** Parameters (out) : None                                                    **
; ..\mcal_src\Can_17_MCanP_Platform.c	   100  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	   101  ** Return value     : None                                                    **
; ..\mcal_src\Can_17_MCanP_Platform.c	   102  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	   103  ** Description      :  this api is for FD frame ISO support                   **
; ..\mcal_src\Can_17_MCanP_Platform.c	   104  **                                                                            **
; ..\mcal_src\Can_17_MCanP_Platform.c	   105  *******************************************************************************/
; ..\mcal_src\Can_17_MCanP_Platform.c	   106  void Can_lFrameInitialise(uint8 KerIdx,uint8 HwCtrlIDKer,\ 
; Function Can_lFrameInitialise
.L3:
Can_lFrameInitialise:	.type	func

; ..\mcal_src\Can_17_MCanP_Platform.c	   107                   uint16 CanControllerFDBaudrate,uint16 CanControllerTxDelayComp)
; ..\mcal_src\Can_17_MCanP_Platform.c	   108  {
; ..\mcal_src\Can_17_MCanP_Platform.c	   109      CAN_SFR_INIT_USER_MODE_MODIFY32(CAN_KER[KerIdx]->N[HwCtrlIDKer].CR.U,\ 
	movh.a	a15,#@his(CAN_KER)
	lea	a15,[a15]@los(CAN_KER)
	addsc.a	a15,a15,d4,#2
	sha	d15,d5,#8
	ld.a	a2,[a15]
	addsc.a	a2,a2,d15,#0
	ld.w	d0,[a2]512
	insert	d0,d0,#1,#9,#1
	st.w	[a2]512,d0
.L22:

; ..\mcal_src\Can_17_MCanP_Platform.c	   110                    CAN_NCR_FDEN_CLEARMASK,(uint32)CAN_ONE << CAN_NCR_FDEN_BITPOS)
; ..\mcal_src\Can_17_MCanP_Platform.c	   111  
; ..\mcal_src\Can_17_MCanP_Platform.c	   112      /* Set Fast Node Bit Timing value */
; ..\mcal_src\Can_17_MCanP_Platform.c	   113      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used due to
; ..\mcal_src\Can_17_MCanP_Platform.c	   114          PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Can_17_MCanP_Platform.c	   115      CAN_SFR_INIT_USER_MODE_WRITE32(CAN_KER[KerIdx]->N[HwCtrlIDKer].FBTR.U,\ 
	ld.a	a2,[a15]
	addsc.a	a2,a2,d15,#0
	st.w	[a2]568,d6
.L34:

; ..\mcal_src\Can_17_MCanP_Platform.c	   116                                          (unsigned_int)CanControllerFDBaudrate);
; ..\mcal_src\Can_17_MCanP_Platform.c	   117  
; ..\mcal_src\Can_17_MCanP_Platform.c	   118      /* Set Transceiver Delay Compensation Offset values */
; ..\mcal_src\Can_17_MCanP_Platform.c	   119      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used due to
; ..\mcal_src\Can_17_MCanP_Platform.c	   120          PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Can_17_MCanP_Platform.c	   121      CAN_SFR_INIT_USER_MODE_WRITE32(CAN_KER[KerIdx]->N[HwCtrlIDKer].TDCR.U,\ 
	ld.a	a15,[a15]
	addsc.a	a15,a15,d15,#0
	st.w	[a15]572,d7
.L35:

; ..\mcal_src\Can_17_MCanP_Platform.c	   122                                          (unsigned_int)CanControllerTxDelayComp);
; ..\mcal_src\Can_17_MCanP_Platform.c	   123  }
	ret
.L15:
	
__Can_lFrameInitialise_function_end:
	.size	Can_lFrameInitialise,__Can_lFrameInitialise_function_end-Can_lFrameInitialise
.L12:
	; End of function
	
	.sdecl	'.rodata.CPU0.Private.DEFAULT_CONST_32BIT',data,rom,cluster('CAN_KER')
	.sect	'.rodata.CPU0.Private.DEFAULT_CONST_32BIT'
	.global	CAN_KER
	.align	4
CAN_KER:	.type	object
	.size	CAN_KER,8
	.word	-268337152,-268271616
	.calls	'Can_lFrameInitialise','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L5:
	.word	18498
	.half	3
	.word	.L6
	.byte	4
.L4:
	.byte	1
	.byte	'..\\mcal_src\\Can_17_MCanP_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L7
.L16:
	.byte	2
	.byte	'unsigned char',0,1,8
.L19:
	.byte	2
	.byte	'unsigned short int',0,2,7
.L23:
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'void',0,4
	.word	250
	.byte	5
	.byte	'__prof_adm',0,1,1,1
	.word	256
	.byte	6,1,4
	.word	280
	.byte	5
	.byte	'__codeptr',0,1,1,1
	.word	282
	.byte	7
	.byte	'_Ifx_CAN_ACCEN0_Bits',0,2,49,16,4,8
	.byte	'EN0',0,1
	.word	190
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	190
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	190
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	190
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	190
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	190
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	190
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	190
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	190
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	190
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	190
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	190
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	190
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	190
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	190
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	190
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	190
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	190
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	190
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	190
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	190
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	190
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	190
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	190
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	190
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	190
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	190
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	190
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	190
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	190
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	190
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_CAN_ACCEN0_Bits',0,2,83,3
	.word	305
	.byte	7
	.byte	'_Ifx_CAN_ACCEN1_Bits',0,2,86,16,4,2
	.byte	'unsigned int',0,4,7,8
	.byte	'reserved_0',0,4
	.word	888
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_CAN_ACCEN1_Bits',0,2,89,3
	.word	862
	.byte	7
	.byte	'_Ifx_CAN_CLC_Bits',0,2,92,16,4,8
	.byte	'DISR',0,1
	.word	190
	.byte	1,7,2,35,0,8
	.byte	'DISS',0,1
	.word	190
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	190
	.byte	1,5,2,35,0,8
	.byte	'EDIS',0,1
	.word	190
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	888
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_CAN_CLC_Bits',0,2,99,3
	.word	955
	.byte	7
	.byte	'_Ifx_CAN_FDR_Bits',0,2,102,16,4,8
	.byte	'STEP',0,2
	.word	207
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	190
	.byte	4,2,2,35,1,8
	.byte	'DM',0,1
	.word	190
	.byte	2,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	207
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_CAN_FDR_Bits',0,2,108,3
	.word	1096
	.byte	7
	.byte	'_Ifx_CAN_ID_Bits',0,2,111,16,4,8
	.byte	'MODREV',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'MODTYPE',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'MODNUMBER',0,2
	.word	207
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_CAN_ID_Bits',0,2,116,3
	.word	1221
	.byte	7
	.byte	'_Ifx_CAN_KRST0_Bits',0,2,119,16,4,8
	.byte	'RST',0,1
	.word	190
	.byte	1,7,2,35,0,8
	.byte	'RSTSTAT',0,1
	.word	190
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	888
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_CAN_KRST0_Bits',0,2,124,3
	.word	1326
	.byte	7
	.byte	'_Ifx_CAN_KRST1_Bits',0,2,127,16,4,8
	.byte	'RST',0,1
	.word	190
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	888
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_CAN_KRST1_Bits',0,2,131,1,3
	.word	1435
	.byte	7
	.byte	'_Ifx_CAN_KRSTCLR_Bits',0,2,134,1,16,4,8
	.byte	'CLR',0,1
	.word	190
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	888
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_CAN_KRSTCLR_Bits',0,2,138,1,3
	.word	1526
	.byte	7
	.byte	'_Ifx_CAN_LIST_Bits',0,2,141,1,16,4,8
	.byte	'BEGIN',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'END',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'SIZE',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'EMPTY',0,1
	.word	190
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	190
	.byte	7,0,2,35,3,0,5
	.byte	'Ifx_CAN_LIST_Bits',0,2,148,1,3
	.word	1622
	.byte	7
	.byte	'_Ifx_CAN_MCR_Bits',0,2,151,1,16,4,8
	.byte	'CLKSEL',0,1
	.word	190
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	190
	.byte	4,0,2,35,0,8
	.byte	'DXCM',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'reserved_9',0,1
	.word	190
	.byte	3,4,2,35,1,8
	.byte	'MPSEL',0,1
	.word	190
	.byte	4,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	207
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_CAN_MCR_Bits',0,2,159,1,3
	.word	1763
	.byte	7
	.byte	'_Ifx_CAN_MECR_Bits',0,2,162,1,16,4,8
	.byte	'TH',0,2
	.word	207
	.byte	16,0,2,35,0,8
	.byte	'INP',0,1
	.word	190
	.byte	4,4,2,35,2,8
	.byte	'NODE',0,1
	.word	190
	.byte	3,1,2,35,2,8
	.byte	'reserved_23',0,1
	.word	190
	.byte	1,0,2,35,2,8
	.byte	'ANYED',0,1
	.word	190
	.byte	1,7,2,35,3,8
	.byte	'CAPEIE',0,1
	.word	190
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	190
	.byte	1,5,2,35,3,8
	.byte	'DEPTH',0,1
	.word	190
	.byte	3,2,2,35,3,8
	.byte	'SOF',0,1
	.word	190
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	190
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_CAN_MECR_Bits',0,2,174,1,3
	.word	1932
	.byte	7
	.byte	'_Ifx_CAN_MESTAT_Bits',0,2,177,1,16,4,8
	.byte	'CAPT',0,2
	.word	207
	.byte	16,0,2,35,0,8
	.byte	'CAPRED',0,1
	.word	190
	.byte	1,7,2,35,2,8
	.byte	'CAPE',0,1
	.word	190
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,2
	.word	207
	.byte	14,0,2,35,2,0,5
	.byte	'Ifx_CAN_MESTAT_Bits',0,2,183,1,3
	.word	2166
	.byte	7
	.byte	'_Ifx_CAN_MITR_Bits',0,2,186,1,16,4,8
	.byte	'IT',0,2
	.word	207
	.byte	16,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	207
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_CAN_MITR_Bits',0,2,190,1,3
	.word	2296
	.byte	7
	.byte	'_Ifx_CAN_MO_AMR_Bits',0,2,193,1,16,4,8
	.byte	'AM',0,4
	.word	888
	.byte	29,3,2,35,2,8
	.byte	'MIDE',0,1
	.word	190
	.byte	1,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	190
	.byte	2,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_AMR_Bits',0,2,198,1,3
	.word	2386
	.byte	7
	.byte	'_Ifx_CAN_MO_AR_Bits',0,2,201,1,16,4,8
	.byte	'ID',0,4
	.word	888
	.byte	29,3,2,35,2,8
	.byte	'IDE',0,1
	.word	190
	.byte	1,2,2,35,3,8
	.byte	'PRI',0,1
	.word	190
	.byte	2,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_AR_Bits',0,2,206,1,3
	.word	2496
	.byte	7
	.byte	'_Ifx_CAN_MO_CTR_Bits',0,2,209,1,16,4,8
	.byte	'RESRXPND',0,1
	.word	190
	.byte	1,7,2,35,0,8
	.byte	'RESTXPND',0,1
	.word	190
	.byte	1,6,2,35,0,8
	.byte	'RESRXUPD',0,1
	.word	190
	.byte	1,5,2,35,0,8
	.byte	'RESNEWDAT',0,1
	.word	190
	.byte	1,4,2,35,0,8
	.byte	'RESMSGLST',0,1
	.word	190
	.byte	1,3,2,35,0,8
	.byte	'RESMSGVAL',0,1
	.word	190
	.byte	1,2,2,35,0,8
	.byte	'RESRTSEL',0,1
	.word	190
	.byte	1,1,2,35,0,8
	.byte	'RESRXEN',0,1
	.word	190
	.byte	1,0,2,35,0,8
	.byte	'RESTXRQ',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'RESTXEN0',0,1
	.word	190
	.byte	1,6,2,35,1,8
	.byte	'RESTXEN1',0,1
	.word	190
	.byte	1,5,2,35,1,8
	.byte	'RESDIR',0,1
	.word	190
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	190
	.byte	4,0,2,35,1,8
	.byte	'SETRXPND',0,1
	.word	190
	.byte	1,7,2,35,2,8
	.byte	'SETTXPND',0,1
	.word	190
	.byte	1,6,2,35,2,8
	.byte	'SETRXUPD',0,1
	.word	190
	.byte	1,5,2,35,2,8
	.byte	'SETNEWDAT',0,1
	.word	190
	.byte	1,4,2,35,2,8
	.byte	'SETMSGLST',0,1
	.word	190
	.byte	1,3,2,35,2,8
	.byte	'SETMSGVAL',0,1
	.word	190
	.byte	1,2,2,35,2,8
	.byte	'SETRTSEL',0,1
	.word	190
	.byte	1,1,2,35,2,8
	.byte	'SETRXEN',0,1
	.word	190
	.byte	1,0,2,35,2,8
	.byte	'SETTXRQ',0,1
	.word	190
	.byte	1,7,2,35,3,8
	.byte	'SETTXEN0',0,1
	.word	190
	.byte	1,6,2,35,3,8
	.byte	'SETTXEN1',0,1
	.word	190
	.byte	1,5,2,35,3,8
	.byte	'SETDIR',0,1
	.word	190
	.byte	1,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	190
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_CTR_Bits',0,2,237,1,3
	.word	2595
	.byte	7
	.byte	'_Ifx_CAN_MO_DATAH_Bits',0,2,240,1,16,4,8
	.byte	'DB4',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB5',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB6',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB7',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_DATAH_Bits',0,2,246,1,3
	.word	3176
	.byte	7
	.byte	'_Ifx_CAN_MO_DATAL_Bits',0,2,249,1,16,4,8
	.byte	'DB0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB1',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB2',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB3',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_DATAL_Bits',0,2,255,1,3
	.word	3297
	.byte	7
	.byte	'_Ifx_CAN_MO_EDATA0_Bits',0,2,130,2,16,4,8
	.byte	'DB0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB1',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB2',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB3',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_EDATA0_Bits',0,2,136,2,3
	.word	3418
	.byte	7
	.byte	'_Ifx_CAN_MO_EDATA1_Bits',0,2,139,2,16,4,8
	.byte	'DB0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB1',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB2',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB3',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_EDATA1_Bits',0,2,145,2,3
	.word	3541
	.byte	7
	.byte	'_Ifx_CAN_MO_EDATA2_Bits',0,2,148,2,16,4,8
	.byte	'DB0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB1',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB2',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB3',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_EDATA2_Bits',0,2,154,2,3
	.word	3664
	.byte	7
	.byte	'_Ifx_CAN_MO_EDATA3_Bits',0,2,157,2,16,4,8
	.byte	'DB0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB1',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB2',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB3',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_EDATA3_Bits',0,2,163,2,3
	.word	3787
	.byte	7
	.byte	'_Ifx_CAN_MO_EDATA4_Bits',0,2,166,2,16,4,8
	.byte	'DB0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB1',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB2',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB3',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_EDATA4_Bits',0,2,172,2,3
	.word	3910
	.byte	7
	.byte	'_Ifx_CAN_MO_EDATA5_Bits',0,2,175,2,16,4,8
	.byte	'DB0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB1',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB2',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB3',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_EDATA5_Bits',0,2,181,2,3
	.word	4033
	.byte	7
	.byte	'_Ifx_CAN_MO_EDATA6_Bits',0,2,184,2,16,4,8
	.byte	'DB0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'DB1',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'DB2',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'DB3',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_EDATA6_Bits',0,2,190,2,3
	.word	4156
	.byte	7
	.byte	'_Ifx_CAN_MO_FCR_Bits',0,2,193,2,16,4,8
	.byte	'MMC',0,1
	.word	190
	.byte	4,4,2,35,0,8
	.byte	'RXTOE',0,1
	.word	190
	.byte	1,3,2,35,0,8
	.byte	'BRS',0,1
	.word	190
	.byte	1,2,2,35,0,8
	.byte	'FDF',0,1
	.word	190
	.byte	1,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	190
	.byte	1,0,2,35,0,8
	.byte	'GDFS',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'IDC',0,1
	.word	190
	.byte	1,6,2,35,1,8
	.byte	'DLCC',0,1
	.word	190
	.byte	1,5,2,35,1,8
	.byte	'DATC',0,1
	.word	190
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	190
	.byte	4,0,2,35,1,8
	.byte	'RXIE',0,1
	.word	190
	.byte	1,7,2,35,2,8
	.byte	'TXIE',0,1
	.word	190
	.byte	1,6,2,35,2,8
	.byte	'OVIE',0,1
	.word	190
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	190
	.byte	1,4,2,35,2,8
	.byte	'FRREN',0,1
	.word	190
	.byte	1,3,2,35,2,8
	.byte	'RMM',0,1
	.word	190
	.byte	1,2,2,35,2,8
	.byte	'SDT',0,1
	.word	190
	.byte	1,1,2,35,2,8
	.byte	'STT',0,1
	.word	190
	.byte	1,0,2,35,2,8
	.byte	'DLC',0,1
	.word	190
	.byte	4,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	190
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_FCR_Bits',0,2,215,2,3
	.word	4279
	.byte	7
	.byte	'_Ifx_CAN_MO_FGPR_Bits',0,2,218,2,16,4,8
	.byte	'BOT',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'TOP',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'CUR',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'SEL',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_FGPR_Bits',0,2,224,2,3
	.word	4677
	.byte	7
	.byte	'_Ifx_CAN_MO_IPR_Bits',0,2,227,2,16,4,8
	.byte	'RXINP',0,1
	.word	190
	.byte	4,4,2,35,0,8
	.byte	'TXINP',0,1
	.word	190
	.byte	4,0,2,35,0,8
	.byte	'MPN',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'CFCVAL',0,2
	.word	207
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_CAN_MO_IPR_Bits',0,2,233,2,3
	.word	4796
	.byte	7
	.byte	'_Ifx_CAN_MO_STAT_Bits',0,2,236,2,16,4,8
	.byte	'RXPND',0,1
	.word	190
	.byte	1,7,2,35,0,8
	.byte	'TXPND',0,1
	.word	190
	.byte	1,6,2,35,0,8
	.byte	'RXUPD',0,1
	.word	190
	.byte	1,5,2,35,0,8
	.byte	'NEWDAT',0,1
	.word	190
	.byte	1,4,2,35,0,8
	.byte	'MSGLST',0,1
	.word	190
	.byte	1,3,2,35,0,8
	.byte	'MSGVAL',0,1
	.word	190
	.byte	1,2,2,35,0,8
	.byte	'RTSEL',0,1
	.word	190
	.byte	1,1,2,35,0,8
	.byte	'RXEN',0,1
	.word	190
	.byte	1,0,2,35,0,8
	.byte	'TXRQ',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'TXEN0',0,1
	.word	190
	.byte	1,6,2,35,1,8
	.byte	'TXEN1',0,1
	.word	190
	.byte	1,5,2,35,1,8
	.byte	'DIR',0,1
	.word	190
	.byte	1,4,2,35,1,8
	.byte	'LIST',0,1
	.word	190
	.byte	4,0,2,35,1,8
	.byte	'PPREV',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'PNEXT',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_MO_STAT_Bits',0,2,253,2,3
	.word	4920
	.byte	7
	.byte	'_Ifx_CAN_MSID_Bits',0,2,128,3,16,4,8
	.byte	'INDEX',0,1
	.word	190
	.byte	6,2,2,35,0,8
	.byte	'reserved_6',0,4
	.word	888
	.byte	26,0,2,35,2,0,5
	.byte	'Ifx_CAN_MSID_Bits',0,2,132,3,3
	.word	5232
	.byte	7
	.byte	'_Ifx_CAN_MSIMASK_Bits',0,2,135,3,16,4,8
	.byte	'IM',0,4
	.word	888
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_CAN_MSIMASK_Bits',0,2,138,3,3
	.word	5324
	.byte	7
	.byte	'_Ifx_CAN_MSPND_Bits',0,2,141,3,16,4,8
	.byte	'PND',0,4
	.word	888
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_CAN_MSPND_Bits',0,2,144,3,3
	.word	5397
	.byte	7
	.byte	'_Ifx_CAN_N_BTEVR_Bits',0,2,147,3,16,4,8
	.byte	'BRP',0,1
	.word	190
	.byte	6,2,2,35,0,8
	.byte	'reserved_6',0,1
	.word	190
	.byte	2,0,2,35,0,8
	.byte	'SJW',0,1
	.word	190
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	190
	.byte	3,1,2,35,1,8
	.byte	'DIV8',0,1
	.word	190
	.byte	1,0,2,35,1,8
	.byte	'TSEG2',0,1
	.word	190
	.byte	5,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	190
	.byte	1,2,2,35,2,8
	.byte	'TSEG1',0,2
	.word	207
	.byte	6,4,2,35,2,8
	.byte	'reserved_28',0,1
	.word	190
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_CAN_N_BTEVR_Bits',0,2,158,3,3
	.word	5467
	.byte	7
	.byte	'_Ifx_CAN_N_BTR_Bits',0,2,161,3,16,4,8
	.byte	'BRP',0,1
	.word	190
	.byte	6,2,2,35,0,8
	.byte	'SJW',0,1
	.word	190
	.byte	2,0,2,35,0,8
	.byte	'TSEG1',0,1
	.word	190
	.byte	4,4,2,35,1,8
	.byte	'TSEG2',0,1
	.word	190
	.byte	3,1,2,35,1,8
	.byte	'DIV8',0,1
	.word	190
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	207
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_CAN_N_BTR_Bits',0,2,169,3,3
	.word	5697
	.byte	7
	.byte	'_Ifx_CAN_N_CR_Bits',0,2,172,3,16,4,8
	.byte	'INIT',0,1
	.word	190
	.byte	1,7,2,35,0,8
	.byte	'TRIE',0,1
	.word	190
	.byte	1,6,2,35,0,8
	.byte	'LECIE',0,1
	.word	190
	.byte	1,5,2,35,0,8
	.byte	'ALIE',0,1
	.word	190
	.byte	1,4,2,35,0,8
	.byte	'CANDIS',0,1
	.word	190
	.byte	1,3,2,35,0,8
	.byte	'TXDIS',0,1
	.word	190
	.byte	1,2,2,35,0,8
	.byte	'CCE',0,1
	.word	190
	.byte	1,1,2,35,0,8
	.byte	'CALM',0,1
	.word	190
	.byte	1,0,2,35,0,8
	.byte	'SUSEN',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'FDEN',0,1
	.word	190
	.byte	1,6,2,35,1,8
	.byte	'reserved_10',0,4
	.word	888
	.byte	22,0,2,35,2,0,5
	.byte	'Ifx_CAN_N_CR_Bits',0,2,185,3,3
	.word	5855
	.byte	7
	.byte	'_Ifx_CAN_N_ECNT_Bits',0,2,188,3,16,4,8
	.byte	'REC',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'TEC',0,1
	.word	190
	.byte	8,0,2,35,1,8
	.byte	'EWRNLVL',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'LETD',0,1
	.word	190
	.byte	1,7,2,35,3,8
	.byte	'LEINC',0,1
	.word	190
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	190
	.byte	6,0,2,35,3,0,5
	.byte	'Ifx_CAN_N_ECNT_Bits',0,2,196,3,3
	.word	6095
	.byte	7
	.byte	'_Ifx_CAN_N_FBTR_Bits',0,2,199,3,16,4,8
	.byte	'FBRP',0,1
	.word	190
	.byte	6,2,2,35,0,8
	.byte	'FSJW',0,1
	.word	190
	.byte	2,0,2,35,0,8
	.byte	'FTSEG1',0,1
	.word	190
	.byte	4,4,2,35,1,8
	.byte	'FTSEG2',0,1
	.word	190
	.byte	3,1,2,35,1,8
	.byte	'reserved_15',0,4
	.word	888
	.byte	17,0,2,35,2,0,5
	.byte	'Ifx_CAN_N_FBTR_Bits',0,2,206,3,3
	.word	6257
	.byte	7
	.byte	'_Ifx_CAN_N_FCR_Bits',0,2,209,3,16,4,8
	.byte	'CFC',0,2
	.word	207
	.byte	16,0,2,35,0,8
	.byte	'CFSEL',0,1
	.word	190
	.byte	3,5,2,35,2,8
	.byte	'CFMOD',0,1
	.word	190
	.byte	2,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	190
	.byte	1,2,2,35,2,8
	.byte	'CFCIE',0,1
	.word	190
	.byte	1,1,2,35,2,8
	.byte	'CFCOV',0,1
	.word	190
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_N_FCR_Bits',0,2,218,3,3
	.word	6405
	.byte	7
	.byte	'_Ifx_CAN_N_IPR_Bits',0,2,221,3,16,4,8
	.byte	'ALINP',0,1
	.word	190
	.byte	4,4,2,35,0,8
	.byte	'LECINP',0,1
	.word	190
	.byte	4,0,2,35,0,8
	.byte	'TRINP',0,1
	.word	190
	.byte	4,4,2,35,1,8
	.byte	'CFCINP',0,1
	.word	190
	.byte	4,0,2,35,1,8
	.byte	'TEINP',0,1
	.word	190
	.byte	4,4,2,35,2,8
	.byte	'reserved_20',0,2
	.word	207
	.byte	12,0,2,35,2,0,5
	.byte	'Ifx_CAN_N_IPR_Bits',0,2,229,3,3
	.word	6589
	.byte	7
	.byte	'_Ifx_CAN_N_PCR_Bits',0,2,232,3,16,4,8
	.byte	'RXSEL',0,1
	.word	190
	.byte	3,5,2,35,0,8
	.byte	'reserved_3',0,1
	.word	190
	.byte	5,0,2,35,0,8
	.byte	'LBM',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'reserved_9',0,4
	.word	888
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_CAN_N_PCR_Bits',0,2,238,3,3
	.word	6754
	.byte	7
	.byte	'_Ifx_CAN_N_SR_Bits',0,2,241,3,16,4,8
	.byte	'LEC',0,1
	.word	190
	.byte	3,5,2,35,0,8
	.byte	'TXOK',0,1
	.word	190
	.byte	1,4,2,35,0,8
	.byte	'RXOK',0,1
	.word	190
	.byte	1,3,2,35,0,8
	.byte	'ALERT',0,1
	.word	190
	.byte	1,2,2,35,0,8
	.byte	'EWRN',0,1
	.word	190
	.byte	1,1,2,35,0,8
	.byte	'BOFF',0,1
	.word	190
	.byte	1,0,2,35,0,8
	.byte	'LLE',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'LOE',0,1
	.word	190
	.byte	1,6,2,35,1,8
	.byte	'SUSACK',0,1
	.word	190
	.byte	1,5,2,35,1,8
	.byte	'RESI',0,1
	.word	190
	.byte	1,4,2,35,1,8
	.byte	'FLEC',0,1
	.word	190
	.byte	3,1,2,35,1,8
	.byte	'reserved_15',0,4
	.word	888
	.byte	17,0,2,35,2,0,5
	.byte	'Ifx_CAN_N_SR_Bits',0,2,255,3,3
	.word	6885
	.byte	7
	.byte	'_Ifx_CAN_N_TCCR_Bits',0,2,130,4,16,4,8
	.byte	'reserved_0',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'TPSC',0,1
	.word	190
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,4
	.word	888
	.byte	6,14,2,35,2,8
	.byte	'TRIGSRC',0,1
	.word	190
	.byte	3,3,2,35,2,8
	.byte	'reserved_21',0,2
	.word	207
	.byte	11,0,2,35,2,0,5
	.byte	'Ifx_CAN_N_TCCR_Bits',0,2,137,4,3
	.word	7137
	.byte	7
	.byte	'_Ifx_CAN_N_TDCR_Bits',0,2,140,4,16,4,8
	.byte	'TDCV',0,1
	.word	190
	.byte	5,3,2,35,0,8
	.byte	'reserved_5',0,1
	.word	190
	.byte	3,0,2,35,0,8
	.byte	'TDCO',0,1
	.word	190
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	190
	.byte	3,1,2,35,1,8
	.byte	'TDC',0,1
	.word	190
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	207
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_CAN_N_TDCR_Bits',0,2,148,4,3
	.word	7297
	.byte	7
	.byte	'_Ifx_CAN_N_TRTR_Bits',0,2,151,4,16,4,8
	.byte	'RELOAD',0,2
	.word	207
	.byte	16,0,2,35,0,8
	.byte	'reserved_16',0,1
	.word	190
	.byte	6,2,2,35,2,8
	.byte	'TEIE',0,1
	.word	190
	.byte	1,1,2,35,2,8
	.byte	'TE',0,1
	.word	190
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_N_TRTR_Bits',0,2,158,4,3
	.word	7469
	.byte	7
	.byte	'_Ifx_CAN_N_TTTR_Bits',0,2,161,4,16,4,8
	.byte	'RELOAD',0,2
	.word	207
	.byte	16,0,2,35,0,8
	.byte	'TXMO',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'STRT',0,1
	.word	190
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	190
	.byte	7,0,2,35,3,0,5
	.byte	'Ifx_CAN_N_TTTR_Bits',0,2,167,4,3
	.word	7620
	.byte	7
	.byte	'_Ifx_CAN_OCS_Bits',0,2,170,4,16,4,8
	.byte	'TGS',0,1
	.word	190
	.byte	2,6,2,35,0,8
	.byte	'TGB',0,1
	.word	190
	.byte	1,5,2,35,0,8
	.byte	'TG_P',0,1
	.word	190
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	888
	.byte	20,8,2,35,2,8
	.byte	'SUS',0,1
	.word	190
	.byte	4,4,2,35,3,8
	.byte	'SUS_P',0,1
	.word	190
	.byte	1,3,2,35,3,8
	.byte	'SUSSTA',0,1
	.word	190
	.byte	1,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	190
	.byte	2,0,2,35,3,0,5
	.byte	'Ifx_CAN_OCS_Bits',0,2,180,4,3
	.word	7750
	.byte	7
	.byte	'_Ifx_CAN_PANCTR_Bits',0,2,183,4,16,4,8
	.byte	'PANCMD',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'BUSY',0,1
	.word	190
	.byte	1,7,2,35,1,8
	.byte	'RBUSY',0,1
	.word	190
	.byte	1,6,2,35,1,8
	.byte	'reserved_10',0,1
	.word	190
	.byte	6,0,2,35,1,8
	.byte	'PANAR1',0,1
	.word	190
	.byte	8,0,2,35,2,8
	.byte	'PANAR2',0,1
	.word	190
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_CAN_PANCTR_Bits',0,2,191,4,3
	.word	7942
	.byte	9,2,199,4,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,2
	.byte	'int',0,4,5,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	305
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_ACCEN0',0,2,204,4,3
	.word	8109
	.byte	9,2,207,4,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	862
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_ACCEN1',0,2,212,4,3
	.word	8180
	.byte	9,2,215,4,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	955
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_CLC',0,2,220,4,3
	.word	8244
	.byte	9,2,223,4,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1096
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_FDR',0,2,228,4,3
	.word	8305
	.byte	9,2,231,4,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1221
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_ID',0,2,236,4,3
	.word	8366
	.byte	9,2,239,4,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1326
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_KRST0',0,2,244,4,3
	.word	8426
	.byte	9,2,247,4,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1435
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_KRST1',0,2,252,4,3
	.word	8489
	.byte	9,2,255,4,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1526
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_KRSTCLR',0,2,132,5,3
	.word	8552
	.byte	9,2,135,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1622
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_LIST',0,2,140,5,3
	.word	8617
	.byte	9,2,143,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1763
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MCR',0,2,148,5,3
	.word	8679
	.byte	9,2,151,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1932
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MECR',0,2,156,5,3
	.word	8740
	.byte	9,2,159,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2166
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MESTAT',0,2,164,5,3
	.word	8802
	.byte	9,2,167,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2296
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MITR',0,2,172,5,3
	.word	8866
	.byte	9,2,175,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2386
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_AMR',0,2,180,5,3
	.word	8928
	.byte	9,2,183,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2496
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_AR',0,2,188,5,3
	.word	8992
	.byte	9,2,191,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2595
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_CTR',0,2,196,5,3
	.word	9055
	.byte	9,2,199,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3176
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_DATAH',0,2,204,5,3
	.word	9119
	.byte	9,2,207,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3297
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_DATAL',0,2,212,5,3
	.word	9185
	.byte	9,2,215,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3418
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_EDATA0',0,2,220,5,3
	.word	9251
	.byte	9,2,223,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3541
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_EDATA1',0,2,228,5,3
	.word	9318
	.byte	9,2,231,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3664
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_EDATA2',0,2,236,5,3
	.word	9385
	.byte	9,2,239,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3787
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_EDATA3',0,2,244,5,3
	.word	9452
	.byte	9,2,247,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3910
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_EDATA4',0,2,252,5,3
	.word	9519
	.byte	9,2,255,5,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4033
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_EDATA5',0,2,132,6,3
	.word	9586
	.byte	9,2,135,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4156
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_EDATA6',0,2,140,6,3
	.word	9653
	.byte	9,2,143,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4279
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_FCR',0,2,148,6,3
	.word	9720
	.byte	9,2,151,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4677
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_FGPR',0,2,156,6,3
	.word	9784
	.byte	9,2,159,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4796
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_IPR',0,2,164,6,3
	.word	9849
	.byte	9,2,167,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4920
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MO_STAT',0,2,172,6,3
	.word	9913
	.byte	9,2,175,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5232
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MSID',0,2,180,6,3
	.word	9978
	.byte	9,2,183,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5324
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MSIMASK',0,2,188,6,3
	.word	10040
	.byte	9,2,191,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5397
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_MSPND',0,2,196,6,3
	.word	10105
	.byte	9,2,199,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5467
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_BTEVR',0,2,204,6,3
	.word	10168
	.byte	9,2,207,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5697
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_BTR',0,2,212,6,3
	.word	10233
	.byte	9,2,215,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5855
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_CR',0,2,220,6,3
	.word	10296
	.byte	9,2,223,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6095
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_ECNT',0,2,228,6,3
	.word	10358
	.byte	9,2,231,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6257
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_FBTR',0,2,236,6,3
	.word	10422
	.byte	9,2,239,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6405
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_FCR',0,2,244,6,3
	.word	10486
	.byte	9,2,247,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6589
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_IPR',0,2,252,6,3
	.word	10549
	.byte	9,2,255,6,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6754
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_PCR',0,2,132,7,3
	.word	10612
	.byte	9,2,135,7,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6885
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_SR',0,2,140,7,3
	.word	10675
	.byte	9,2,143,7,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7137
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_TCCR',0,2,148,7,3
	.word	10737
	.byte	9,2,151,7,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7297
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_TDCR',0,2,156,7,3
	.word	10801
	.byte	9,2,159,7,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7469
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_TRTR',0,2,164,7,3
	.word	10865
	.byte	9,2,167,7,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7620
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_N_TTTR',0,2,172,7,3
	.word	10929
	.byte	9,2,175,7,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7750
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_OCS',0,2,180,7,3
	.word	10993
	.byte	9,2,183,7,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7942
	.byte	2,35,0,0,5
	.byte	'Ifx_CAN_PANCTR',0,2,188,7,3
	.word	11054
	.byte	7
	.byte	'_Ifx_CAN_MO',0,2,199,7,25,32,9,2,201,7,5,4,10
	.byte	'EDATA0',0,4
	.word	9251
	.byte	2,35,0,10
	.byte	'FCR',0,4
	.word	9720
	.byte	2,35,0,0,11,4
	.word	11136
	.byte	2,35,0,9,2,207,7,5,4,10
	.byte	'EDATA1',0,4
	.word	9318
	.byte	2,35,0,10
	.byte	'FGPR',0,4
	.word	9784
	.byte	2,35,0,0,11,4
	.word	11181
	.byte	2,35,4,9,2,213,7,5,4,10
	.byte	'EDATA2',0,4
	.word	9385
	.byte	2,35,0,10
	.byte	'IPR',0,4
	.word	9849
	.byte	2,35,0,0,11,4
	.word	11227
	.byte	2,35,8,9,2,219,7,5,4,10
	.byte	'AMR',0,4
	.word	8928
	.byte	2,35,0,10
	.byte	'EDATA3',0,4
	.word	9452
	.byte	2,35,0,0,11,4
	.word	11272
	.byte	2,35,12,9,2,225,7,5,4,10
	.byte	'DATAL',0,4
	.word	9185
	.byte	2,35,0,10
	.byte	'EDATA4',0,4
	.word	9519
	.byte	2,35,0,0,11,4
	.word	11317
	.byte	2,35,16,9,2,231,7,5,4,10
	.byte	'DATAH',0,4
	.word	9119
	.byte	2,35,0,10
	.byte	'EDATA5',0,4
	.word	9586
	.byte	2,35,0,0,11,4
	.word	11364
	.byte	2,35,20,9,2,237,7,5,4,10
	.byte	'AR',0,4
	.word	8992
	.byte	2,35,0,10
	.byte	'EDATA6',0,4
	.word	9653
	.byte	2,35,0,0,11,4
	.word	11411
	.byte	2,35,24,9,2,243,7,5,4,10
	.byte	'CTR',0,4
	.word	9055
	.byte	2,35,0,10
	.byte	'STAT',0,4
	.word	9913
	.byte	2,35,0,0,11,4
	.word	11455
	.byte	2,35,28,0,12
	.word	11118
	.byte	5
	.byte	'Ifx_CAN_MO',0,2,249,7,3
	.word	11499
	.byte	7
	.byte	'_Ifx_CAN_N',0,2,252,7,25,128,2,10
	.byte	'CR',0,4
	.word	10296
	.byte	2,35,0,10
	.byte	'SR',0,4
	.word	10675
	.byte	2,35,4,10
	.byte	'IPR',0,4
	.word	10549
	.byte	2,35,8,10
	.byte	'PCR',0,4
	.word	10612
	.byte	2,35,12,9,2,130,8,5,4,10
	.byte	'BTEVR',0,4
	.word	10168
	.byte	2,35,0,10
	.byte	'BTR',0,4
	.word	10233
	.byte	2,35,0,0,11,4
	.word	11592
	.byte	2,35,16,10
	.byte	'ECNT',0,4
	.word	10358
	.byte	2,35,20,10
	.byte	'FCR',0,4
	.word	10486
	.byte	2,35,24,10
	.byte	'TCCR',0,4
	.word	10737
	.byte	2,35,28,10
	.byte	'TRTR',0,4
	.word	10865
	.byte	2,35,32,10
	.byte	'TATTR',0,4
	.word	10929
	.byte	2,35,36,10
	.byte	'TBTTR',0,4
	.word	10929
	.byte	2,35,40,10
	.byte	'TCTTR',0,4
	.word	10929
	.byte	2,35,44,13,8
	.word	190
	.byte	14,7,0,10
	.byte	'reserved_30',0,8
	.word	11736
	.byte	2,35,48,10
	.byte	'FBTR',0,4
	.word	10422
	.byte	2,35,56,10
	.byte	'TDCR',0,4
	.word	10801
	.byte	2,35,60,13,192,1
	.word	190
	.byte	14,191,1,0,10
	.byte	'reserved_40',0,192,1
	.word	11794
	.byte	2,35,64,0,12
	.word	11524
	.byte	5
	.byte	'Ifx_CAN_N',0,2,147,8,3
	.word	11828
	.byte	7
	.byte	'_Ifx_CAN',0,2,160,8,25,128,128,1,10
	.byte	'CLC',0,4
	.word	8244
	.byte	2,35,0,13,4
	.word	190
	.byte	14,3,0,10
	.byte	'reserved_4',0,4
	.word	11882
	.byte	2,35,4,10
	.byte	'ID',0,4
	.word	8366
	.byte	2,35,8,10
	.byte	'FDR',0,4
	.word	8305
	.byte	2,35,12,13,216,1
	.word	190
	.byte	14,215,1,0,10
	.byte	'reserved_10',0,216,1
	.word	11936
	.byte	2,35,16,10
	.byte	'OCS',0,4
	.word	10993
	.byte	3,35,232,1,10
	.byte	'KRSTCLR',0,4
	.word	8552
	.byte	3,35,236,1,10
	.byte	'KRST1',0,4
	.word	8489
	.byte	3,35,240,1,10
	.byte	'KRST0',0,4
	.word	8426
	.byte	3,35,244,1,10
	.byte	'ACCEN1',0,4
	.word	8180
	.byte	3,35,248,1,10
	.byte	'ACCEN0',0,4
	.word	8109
	.byte	3,35,252,1,13,64
	.word	8617
	.byte	14,15,0,10
	.byte	'LIST',0,64
	.word	12067
	.byte	3,35,128,2,13,32
	.word	10105
	.byte	14,7,0,10
	.byte	'MSPND',0,32
	.word	12091
	.byte	3,35,192,2,13,32
	.word	190
	.byte	14,31,0,10
	.byte	'reserved_160',0,32
	.word	12116
	.byte	3,35,224,2,13,32
	.word	9978
	.byte	14,7,0,10
	.byte	'MSID',0,32
	.word	12148
	.byte	3,35,128,3,10
	.byte	'reserved_1A0',0,32
	.word	12116
	.byte	3,35,160,3,10
	.byte	'MSIMASK',0,4
	.word	10040
	.byte	3,35,192,3,10
	.byte	'PANCTR',0,4
	.word	11054
	.byte	3,35,196,3,10
	.byte	'MCR',0,4
	.word	8679
	.byte	3,35,200,3,10
	.byte	'MITR',0,4
	.word	8866
	.byte	3,35,204,3,10
	.byte	'MECR',0,4
	.word	8740
	.byte	3,35,208,3,10
	.byte	'MESTAT',0,4
	.word	8802
	.byte	3,35,212,3,13,40
	.word	190
	.byte	14,39,0,10
	.byte	'reserved_1D8',0,40
	.word	12291
	.byte	3,35,216,3,13,128,6
	.word	11524
	.byte	14,2,0,12
	.word	12323
	.byte	10
	.byte	'N',0,128,6
	.word	12333
	.byte	3,35,128,4,13,128,22
	.word	190
	.byte	14,255,21,0,10
	.byte	'reserved_500',0,128,22
	.word	12351
	.byte	3,35,128,10,13,128,32
	.word	11118
	.byte	14,127,0,12
	.word	12386
	.byte	10
	.byte	'MO',0,128,32
	.word	12396
	.byte	3,35,128,32,13,128,64
	.word	190
	.byte	14,255,63,0,10
	.byte	'reserved_2000',0,128,64
	.word	12415
	.byte	3,35,128,64,0,12
	.word	11852
	.byte	5
	.byte	'Ifx_CAN',0,2,189,8,3
	.word	12452
	.byte	7
	.byte	'_Ifx_SRC_SRCR_Bits',0,3,45,16,4,8
	.byte	'SRPN',0,1
	.word	190
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	190
	.byte	2,6,2,35,1,8
	.byte	'SRE',0,1
	.word	190
	.byte	1,5,2,35,1,8
	.byte	'TOS',0,1
	.word	190
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	190
	.byte	4,0,2,35,1,8
	.byte	'ECC',0,1
	.word	190
	.byte	5,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	190
	.byte	3,0,2,35,2,8
	.byte	'SRR',0,1
	.word	190
	.byte	1,7,2,35,3,8
	.byte	'CLRR',0,1
	.word	190
	.byte	1,6,2,35,3,8
	.byte	'SETR',0,1
	.word	190
	.byte	1,5,2,35,3,8
	.byte	'IOV',0,1
	.word	190
	.byte	1,4,2,35,3,8
	.byte	'IOVCLR',0,1
	.word	190
	.byte	1,3,2,35,3,8
	.byte	'SWS',0,1
	.word	190
	.byte	1,2,2,35,3,8
	.byte	'SWSCLR',0,1
	.word	190
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	190
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SRC_SRCR_Bits',0,3,62,3
	.word	12474
	.byte	9,3,70,9,4,10
	.byte	'U',0,4
	.word	888
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	8126
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12474
	.byte	2,35,0,0,5
	.byte	'Ifx_SRC_SRCR',0,3,75,3
	.word	12790
	.byte	7
	.byte	'_Ifx_SRC_ASCLIN',0,3,86,25,12,10
	.byte	'TX',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'RX',0,4
	.word	12790
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	12790
	.byte	2,35,8,0,12
	.word	12850
	.byte	5
	.byte	'Ifx_SRC_ASCLIN',0,3,91,3
	.word	12909
	.byte	7
	.byte	'_Ifx_SRC_BCUSPB',0,3,94,25,4,10
	.byte	'SBSRC',0,4
	.word	12790
	.byte	2,35,0,0,12
	.word	12937
	.byte	5
	.byte	'Ifx_SRC_BCUSPB',0,3,97,3
	.word	12974
	.byte	7
	.byte	'_Ifx_SRC_CAN',0,3,100,25,64,13,64
	.word	12790
	.byte	14,15,0,10
	.byte	'INT',0,64
	.word	13020
	.byte	2,35,0,0,12
	.word	13002
	.byte	5
	.byte	'Ifx_SRC_CAN',0,3,103,3
	.word	13043
	.byte	7
	.byte	'_Ifx_SRC_CAN1',0,3,106,25,32,13,32
	.word	12790
	.byte	14,7,0,10
	.byte	'INT',0,32
	.word	13087
	.byte	2,35,0,0,12
	.word	13068
	.byte	5
	.byte	'Ifx_SRC_CAN1',0,3,109,3
	.word	13110
	.byte	7
	.byte	'_Ifx_SRC_CCU6',0,3,112,25,16,10
	.byte	'SR0',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	12790
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	12790
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	12790
	.byte	2,35,12,0,12
	.word	13136
	.byte	5
	.byte	'Ifx_SRC_CCU6',0,3,118,3
	.word	13208
	.byte	7
	.byte	'_Ifx_SRC_CERBERUS',0,3,121,25,8,13,8
	.word	12790
	.byte	14,1,0,10
	.byte	'SR',0,8
	.word	13257
	.byte	2,35,0,0,12
	.word	13234
	.byte	5
	.byte	'Ifx_SRC_CERBERUS',0,3,124,3
	.word	13279
	.byte	7
	.byte	'_Ifx_SRC_CPU',0,3,127,25,32,10
	.byte	'SBSRC',0,4
	.word	12790
	.byte	2,35,0,13,28
	.word	190
	.byte	14,27,0,10
	.byte	'reserved_4',0,28
	.word	13342
	.byte	2,35,4,0,12
	.word	13309
	.byte	5
	.byte	'Ifx_SRC_CPU',0,3,131,1,3
	.word	13372
	.byte	7
	.byte	'_Ifx_SRC_DMA',0,3,134,1,25,80,10
	.byte	'ERR',0,4
	.word	12790
	.byte	2,35,0,13,12
	.word	190
	.byte	14,11,0,10
	.byte	'reserved_4',0,12
	.word	13430
	.byte	2,35,4,10
	.byte	'CH',0,64
	.word	13020
	.byte	2,35,16,0,12
	.word	13398
	.byte	5
	.byte	'Ifx_SRC_DMA',0,3,139,1,3
	.word	13472
	.byte	7
	.byte	'_Ifx_SRC_EMEM',0,3,142,1,25,4,10
	.byte	'SR',0,4
	.word	12790
	.byte	2,35,0,0,12
	.word	13498
	.byte	5
	.byte	'Ifx_SRC_EMEM',0,3,145,1,3
	.word	13531
	.byte	7
	.byte	'_Ifx_SRC_ERAY',0,3,148,1,25,80,10
	.byte	'INT',0,8
	.word	13257
	.byte	2,35,0,10
	.byte	'TINT',0,8
	.word	13257
	.byte	2,35,8,10
	.byte	'NDAT',0,8
	.word	13257
	.byte	2,35,16,10
	.byte	'MBSC',0,8
	.word	13257
	.byte	2,35,24,10
	.byte	'OBUSY',0,4
	.word	12790
	.byte	2,35,32,10
	.byte	'IBUSY',0,4
	.word	12790
	.byte	2,35,36,10
	.byte	'reserved_28',0,40
	.word	12291
	.byte	2,35,40,0,12
	.word	13558
	.byte	5
	.byte	'Ifx_SRC_ERAY',0,3,157,1,3
	.word	13685
	.byte	7
	.byte	'_Ifx_SRC_ETH',0,3,160,1,25,4,10
	.byte	'SR',0,4
	.word	12790
	.byte	2,35,0,0,12
	.word	13712
	.byte	5
	.byte	'Ifx_SRC_ETH',0,3,163,1,3
	.word	13744
	.byte	7
	.byte	'_Ifx_SRC_EVR',0,3,166,1,25,8,10
	.byte	'WUT',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'SCDC',0,4
	.word	12790
	.byte	2,35,4,0,12
	.word	13770
	.byte	5
	.byte	'Ifx_SRC_EVR',0,3,170,1,3
	.word	13817
	.byte	7
	.byte	'_Ifx_SRC_FFT',0,3,173,1,25,12,10
	.byte	'DONE',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'ERR',0,4
	.word	12790
	.byte	2,35,4,10
	.byte	'RFS',0,4
	.word	12790
	.byte	2,35,8,0,12
	.word	13843
	.byte	5
	.byte	'Ifx_SRC_FFT',0,3,178,1,3
	.word	13903
	.byte	7
	.byte	'_Ifx_SRC_GPSR',0,3,181,1,25,128,12,10
	.byte	'SR0',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	12790
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	12790
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	12790
	.byte	2,35,12,13,240,11
	.word	190
	.byte	14,239,11,0,10
	.byte	'reserved_10',0,240,11
	.word	14002
	.byte	2,35,16,0,12
	.word	13929
	.byte	5
	.byte	'Ifx_SRC_GPSR',0,3,188,1,3
	.word	14036
	.byte	7
	.byte	'_Ifx_SRC_GPT12',0,3,191,1,25,48,10
	.byte	'CIRQ',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'T2',0,4
	.word	12790
	.byte	2,35,4,10
	.byte	'T3',0,4
	.word	12790
	.byte	2,35,8,10
	.byte	'T4',0,4
	.word	12790
	.byte	2,35,12,10
	.byte	'T5',0,4
	.word	12790
	.byte	2,35,16,10
	.byte	'T6',0,4
	.word	12790
	.byte	2,35,20,13,24
	.word	190
	.byte	14,23,0,10
	.byte	'reserved_18',0,24
	.word	14158
	.byte	2,35,24,0,12
	.word	14063
	.byte	5
	.byte	'Ifx_SRC_GPT12',0,3,200,1,3
	.word	14189
	.byte	7
	.byte	'_Ifx_SRC_GTM',0,3,203,1,25,192,11,10
	.byte	'AEIIRQ',0,4
	.word	12790
	.byte	2,35,0,13,236,2
	.word	190
	.byte	14,235,2,0,10
	.byte	'reserved_4',0,236,2
	.word	14253
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	12790
	.byte	3,35,240,2,10
	.byte	'reserved_174',0,12
	.word	13430
	.byte	3,35,244,2,13,32
	.word	13087
	.byte	14,0,0,10
	.byte	'TIM',0,32
	.word	14322
	.byte	3,35,128,3,13,224,7
	.word	190
	.byte	14,223,7,0,10
	.byte	'reserved_1A0',0,224,7
	.word	14345
	.byte	3,35,160,3,13,64
	.word	13087
	.byte	14,1,0,10
	.byte	'TOM',0,64
	.word	14380
	.byte	3,35,128,11,0,12
	.word	14217
	.byte	5
	.byte	'Ifx_SRC_GTM',0,3,212,1,3
	.word	14404
	.byte	7
	.byte	'_Ifx_SRC_HSM',0,3,215,1,25,8,10
	.byte	'HSM',0,8
	.word	13257
	.byte	2,35,0,0,12
	.word	14430
	.byte	5
	.byte	'Ifx_SRC_HSM',0,3,218,1,3
	.word	14463
	.byte	7
	.byte	'_Ifx_SRC_LMU',0,3,221,1,25,4,10
	.byte	'SR',0,4
	.word	12790
	.byte	2,35,0,0,12
	.word	14489
	.byte	5
	.byte	'Ifx_SRC_LMU',0,3,224,1,3
	.word	14521
	.byte	7
	.byte	'_Ifx_SRC_PMU',0,3,227,1,25,4,10
	.byte	'SR',0,4
	.word	12790
	.byte	2,35,0,0,12
	.word	14547
	.byte	5
	.byte	'Ifx_SRC_PMU',0,3,230,1,3
	.word	14579
	.byte	7
	.byte	'_Ifx_SRC_QSPI',0,3,233,1,25,24,10
	.byte	'TX',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'RX',0,4
	.word	12790
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	12790
	.byte	2,35,8,10
	.byte	'PT',0,4
	.word	12790
	.byte	2,35,12,10
	.byte	'HC',0,4
	.word	12790
	.byte	2,35,16,10
	.byte	'U',0,4
	.word	12790
	.byte	2,35,20,0,12
	.word	14605
	.byte	5
	.byte	'Ifx_SRC_QSPI',0,3,241,1,3
	.word	14698
	.byte	7
	.byte	'_Ifx_SRC_SCU',0,3,244,1,25,20,10
	.byte	'DTS',0,4
	.word	12790
	.byte	2,35,0,13,16
	.word	12790
	.byte	14,3,0,10
	.byte	'ERU',0,16
	.word	14757
	.byte	2,35,4,0,12
	.word	14725
	.byte	5
	.byte	'Ifx_SRC_SCU',0,3,248,1,3
	.word	14780
	.byte	7
	.byte	'_Ifx_SRC_SENT',0,3,251,1,25,16,10
	.byte	'SR',0,16
	.word	14757
	.byte	2,35,0,0,12
	.word	14806
	.byte	5
	.byte	'Ifx_SRC_SENT',0,3,254,1,3
	.word	14839
	.byte	7
	.byte	'_Ifx_SRC_SMU',0,3,129,2,25,12,13,12
	.word	12790
	.byte	14,2,0,10
	.byte	'SR',0,12
	.word	14885
	.byte	2,35,0,0,12
	.word	14866
	.byte	5
	.byte	'Ifx_SRC_SMU',0,3,132,2,3
	.word	14907
	.byte	7
	.byte	'_Ifx_SRC_STM',0,3,135,2,25,96,10
	.byte	'SR0',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	12790
	.byte	2,35,4,13,88
	.word	190
	.byte	14,87,0,10
	.byte	'reserved_8',0,88
	.word	14978
	.byte	2,35,8,0,12
	.word	14933
	.byte	5
	.byte	'Ifx_SRC_STM',0,3,140,2,3
	.word	15008
	.byte	7
	.byte	'_Ifx_SRC_VADCCG',0,3,143,2,25,192,2,10
	.byte	'SR0',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	12790
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	12790
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	12790
	.byte	2,35,12,13,176,2
	.word	190
	.byte	14,175,2,0,10
	.byte	'reserved_10',0,176,2
	.word	15109
	.byte	2,35,16,0,12
	.word	15034
	.byte	5
	.byte	'Ifx_SRC_VADCCG',0,3,150,2,3
	.word	15143
	.byte	7
	.byte	'_Ifx_SRC_VADCG',0,3,153,2,25,16,10
	.byte	'SR0',0,4
	.word	12790
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	12790
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	12790
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	12790
	.byte	2,35,12,0,12
	.word	15172
	.byte	5
	.byte	'Ifx_SRC_VADCG',0,3,159,2,3
	.word	15246
	.byte	7
	.byte	'_Ifx_SRC_XBAR',0,3,162,2,25,4,10
	.byte	'SRC',0,4
	.word	12790
	.byte	2,35,0,0,12
	.word	15274
	.byte	5
	.byte	'Ifx_SRC_XBAR',0,3,165,2,3
	.word	15308
	.byte	7
	.byte	'_Ifx_SRC_GASCLIN',0,3,178,2,25,24,13,24
	.word	12850
	.byte	14,1,0,12
	.word	15358
	.byte	10
	.byte	'ASCLIN',0,24
	.word	15367
	.byte	2,35,0,0,12
	.word	15335
	.byte	5
	.byte	'Ifx_SRC_GASCLIN',0,3,181,2,3
	.word	15389
	.byte	7
	.byte	'_Ifx_SRC_GBCU',0,3,184,2,25,4,12
	.word	12937
	.byte	10
	.byte	'SPB',0,4
	.word	15439
	.byte	2,35,0,0,12
	.word	15419
	.byte	5
	.byte	'Ifx_SRC_GBCU',0,3,187,2,3
	.word	15458
	.byte	7
	.byte	'_Ifx_SRC_GCAN',0,3,190,2,25,96,13,64
	.word	13002
	.byte	14,0,0,12
	.word	15505
	.byte	10
	.byte	'CAN',0,64
	.word	15514
	.byte	2,35,0,13,32
	.word	13068
	.byte	14,0,0,12
	.word	15532
	.byte	10
	.byte	'CAN1',0,32
	.word	15541
	.byte	2,35,64,0,12
	.word	15485
	.byte	5
	.byte	'Ifx_SRC_GCAN',0,3,194,2,3
	.word	15561
	.byte	7
	.byte	'_Ifx_SRC_GCCU6',0,3,197,2,25,32,13,32
	.word	13136
	.byte	14,1,0,12
	.word	15609
	.byte	10
	.byte	'CCU6',0,32
	.word	15618
	.byte	2,35,0,0,12
	.word	15588
	.byte	5
	.byte	'Ifx_SRC_GCCU6',0,3,200,2,3
	.word	15638
	.byte	7
	.byte	'_Ifx_SRC_GCERBERUS',0,3,203,2,25,8,12
	.word	13234
	.byte	10
	.byte	'CERBERUS',0,8
	.word	15691
	.byte	2,35,0,0,12
	.word	15666
	.byte	5
	.byte	'Ifx_SRC_GCERBERUS',0,3,206,2,3
	.word	15715
	.byte	7
	.byte	'_Ifx_SRC_GCPU',0,3,209,2,25,32,13,32
	.word	13309
	.byte	14,0,0,12
	.word	15767
	.byte	10
	.byte	'CPU',0,32
	.word	15776
	.byte	2,35,0,0,12
	.word	15747
	.byte	5
	.byte	'Ifx_SRC_GCPU',0,3,212,2,3
	.word	15795
	.byte	7
	.byte	'_Ifx_SRC_GDMA',0,3,215,2,25,80,13,80
	.word	13398
	.byte	14,0,0,12
	.word	15842
	.byte	10
	.byte	'DMA',0,80
	.word	15851
	.byte	2,35,0,0,12
	.word	15822
	.byte	5
	.byte	'Ifx_SRC_GDMA',0,3,218,2,3
	.word	15870
	.byte	7
	.byte	'_Ifx_SRC_GEMEM',0,3,221,2,25,4,13,4
	.word	13498
	.byte	14,0,0,12
	.word	15918
	.byte	10
	.byte	'EMEM',0,4
	.word	15927
	.byte	2,35,0,0,12
	.word	15897
	.byte	5
	.byte	'Ifx_SRC_GEMEM',0,3,224,2,3
	.word	15947
	.byte	7
	.byte	'_Ifx_SRC_GERAY',0,3,227,2,25,80,13,80
	.word	13558
	.byte	14,0,0,12
	.word	15996
	.byte	10
	.byte	'ERAY',0,80
	.word	16005
	.byte	2,35,0,0,12
	.word	15975
	.byte	5
	.byte	'Ifx_SRC_GERAY',0,3,230,2,3
	.word	16025
	.byte	7
	.byte	'_Ifx_SRC_GETH',0,3,233,2,25,4,13,4
	.word	13712
	.byte	14,0,0,12
	.word	16073
	.byte	10
	.byte	'ETH',0,4
	.word	16082
	.byte	2,35,0,0,12
	.word	16053
	.byte	5
	.byte	'Ifx_SRC_GETH',0,3,236,2,3
	.word	16101
	.byte	7
	.byte	'_Ifx_SRC_GEVR',0,3,239,2,25,8,13,8
	.word	13770
	.byte	14,0,0,12
	.word	16148
	.byte	10
	.byte	'EVR',0,8
	.word	16157
	.byte	2,35,0,0,12
	.word	16128
	.byte	5
	.byte	'Ifx_SRC_GEVR',0,3,242,2,3
	.word	16176
	.byte	7
	.byte	'_Ifx_SRC_GFFT',0,3,245,2,25,12,13,12
	.word	13843
	.byte	14,0,0,12
	.word	16223
	.byte	10
	.byte	'FFT',0,12
	.word	16232
	.byte	2,35,0,0,12
	.word	16203
	.byte	5
	.byte	'Ifx_SRC_GFFT',0,3,248,2,3
	.word	16251
	.byte	7
	.byte	'_Ifx_SRC_GGPSR',0,3,251,2,25,128,12,13,128,12
	.word	13929
	.byte	14,0,0,12
	.word	16300
	.byte	10
	.byte	'GPSR',0,128,12
	.word	16310
	.byte	2,35,0,0,12
	.word	16278
	.byte	5
	.byte	'Ifx_SRC_GGPSR',0,3,254,2,3
	.word	16331
	.byte	7
	.byte	'_Ifx_SRC_GGPT12',0,3,129,3,25,48,13,48
	.word	14063
	.byte	14,0,0,12
	.word	16381
	.byte	10
	.byte	'GPT12',0,48
	.word	16390
	.byte	2,35,0,0,12
	.word	16359
	.byte	5
	.byte	'Ifx_SRC_GGPT12',0,3,132,3,3
	.word	16411
	.byte	7
	.byte	'_Ifx_SRC_GGTM',0,3,135,3,25,192,11,13,192,11
	.word	14217
	.byte	14,0,0,12
	.word	16461
	.byte	10
	.byte	'GTM',0,192,11
	.word	16471
	.byte	2,35,0,0,12
	.word	16440
	.byte	5
	.byte	'Ifx_SRC_GGTM',0,3,138,3,3
	.word	16491
	.byte	7
	.byte	'_Ifx_SRC_GHSM',0,3,141,3,25,8,13,8
	.word	14430
	.byte	14,0,0,12
	.word	16538
	.byte	10
	.byte	'HSM',0,8
	.word	16547
	.byte	2,35,0,0,12
	.word	16518
	.byte	5
	.byte	'Ifx_SRC_GHSM',0,3,144,3,3
	.word	16566
	.byte	7
	.byte	'_Ifx_SRC_GLMU',0,3,147,3,25,4,13,4
	.word	14489
	.byte	14,0,0,12
	.word	16613
	.byte	10
	.byte	'LMU',0,4
	.word	16622
	.byte	2,35,0,0,12
	.word	16593
	.byte	5
	.byte	'Ifx_SRC_GLMU',0,3,150,3,3
	.word	16641
	.byte	7
	.byte	'_Ifx_SRC_GPMU',0,3,153,3,25,8,13,8
	.word	14547
	.byte	14,1,0,12
	.word	16688
	.byte	10
	.byte	'PMU',0,8
	.word	16697
	.byte	2,35,0,0,12
	.word	16668
	.byte	5
	.byte	'Ifx_SRC_GPMU',0,3,156,3,3
	.word	16716
	.byte	7
	.byte	'_Ifx_SRC_GQSPI',0,3,159,3,25,96,13,96
	.word	14605
	.byte	14,3,0,12
	.word	16764
	.byte	10
	.byte	'QSPI',0,96
	.word	16773
	.byte	2,35,0,0,12
	.word	16743
	.byte	5
	.byte	'Ifx_SRC_GQSPI',0,3,162,3,3
	.word	16793
	.byte	7
	.byte	'_Ifx_SRC_GSCU',0,3,165,3,25,20,12
	.word	14725
	.byte	10
	.byte	'SCU',0,20
	.word	16841
	.byte	2,35,0,0,12
	.word	16821
	.byte	5
	.byte	'Ifx_SRC_GSCU',0,3,168,3,3
	.word	16860
	.byte	7
	.byte	'_Ifx_SRC_GSENT',0,3,171,3,25,16,13,16
	.word	14806
	.byte	14,0,0,12
	.word	16908
	.byte	10
	.byte	'SENT',0,16
	.word	16917
	.byte	2,35,0,0,12
	.word	16887
	.byte	5
	.byte	'Ifx_SRC_GSENT',0,3,174,3,3
	.word	16937
	.byte	7
	.byte	'_Ifx_SRC_GSMU',0,3,177,3,25,12,13,12
	.word	14866
	.byte	14,0,0,12
	.word	16985
	.byte	10
	.byte	'SMU',0,12
	.word	16994
	.byte	2,35,0,0,12
	.word	16965
	.byte	5
	.byte	'Ifx_SRC_GSMU',0,3,180,3,3
	.word	17013
	.byte	7
	.byte	'_Ifx_SRC_GSTM',0,3,183,3,25,96,13,96
	.word	14933
	.byte	14,0,0,12
	.word	17060
	.byte	10
	.byte	'STM',0,96
	.word	17069
	.byte	2,35,0,0,12
	.word	17040
	.byte	5
	.byte	'Ifx_SRC_GSTM',0,3,186,3,3
	.word	17088
	.byte	7
	.byte	'_Ifx_SRC_GVADC',0,3,189,3,25,224,4,13,64
	.word	15172
	.byte	14,3,0,12
	.word	17137
	.byte	10
	.byte	'G',0,64
	.word	17146
	.byte	2,35,0,13,224,1
	.word	190
	.byte	14,223,1,0,10
	.byte	'reserved_40',0,224,1
	.word	17162
	.byte	2,35,64,13,192,2
	.word	15034
	.byte	14,0,0,12
	.word	17195
	.byte	10
	.byte	'CG',0,192,2
	.word	17205
	.byte	3,35,160,2,0,12
	.word	17115
	.byte	5
	.byte	'Ifx_SRC_GVADC',0,3,194,3,3
	.word	17225
	.byte	7
	.byte	'_Ifx_SRC_GXBAR',0,3,197,3,25,4,12
	.word	15274
	.byte	10
	.byte	'XBAR',0,4
	.word	17274
	.byte	2,35,0,0,12
	.word	17253
	.byte	5
	.byte	'Ifx_SRC_GXBAR',0,3,200,3,3
	.word	17294
	.byte	5
	.byte	'uint8',0,4,90,29
	.word	190
	.byte	5
	.byte	'uint16',0,4,92,29
	.word	207
	.byte	5
	.byte	'uint32',0,4,94,29
	.word	229
	.byte	5
	.byte	'PduIdType',0,5,72,22
	.word	190
	.byte	5
	.byte	'PduLengthType',0,5,76,22
	.word	207
	.byte	5
	.byte	'Can_IdType',0,6,46,16
	.word	229
	.byte	7
	.byte	'Can_TxHwObjectConfigType',0,7,218,3,16,2,10
	.byte	'MsgObjId',0,1
	.word	190
	.byte	2,35,0,10
	.byte	'HwControllerId',0,1
	.word	190
	.byte	2,35,1,0,5
	.byte	'Can_TxHwObjectConfigType',0,7,236,3,3
	.word	17425
	.byte	7
	.byte	'Can_RxHwObjectConfigType',0,7,241,3,16,12,10
	.byte	'MaskRef',0,4
	.word	229
	.byte	2,35,0,10
	.byte	'MsgId',0,4
	.word	229
	.byte	2,35,4,10
	.byte	'MsgObjId',0,1
	.word	190
	.byte	2,35,8,10
	.byte	'HwControllerId',0,1
	.word	190
	.byte	2,35,9,0,5
	.byte	'Can_RxHwObjectConfigType',0,7,131,4,3
	.word	17533
	.byte	7
	.byte	'Can_ControllerMOMapConfigType',0,7,165,4,16,4,13,4
	.word	190
	.byte	14,3,0,10
	.byte	'ControllerMOMap',0,4
	.word	17709
	.byte	2,35,0,0,5
	.byte	'Can_ControllerMOMapConfigType',0,7,168,4,3
	.word	17673
	.byte	7
	.byte	'Can_NPCRValueType',0,7,172,4,16,2,10
	.byte	'Can_NPCRValue',0,2
	.word	207
	.byte	2,35,0,0,5
	.byte	'Can_NPCRValueType',0,7,175,4,3
	.word	17783
	.byte	7
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,7,178,4,16,6,10
	.byte	'CanControllerBaudrate',0,4
	.word	229
	.byte	2,35,0,10
	.byte	'CanControllerBaudrateCfg',0,2
	.word	207
	.byte	2,35,4,0,5
	.byte	'Can_17_MCanP_ControllerBaudrateConfigType',0,7,182,4,3
	.word	17858
	.byte	7
	.byte	'Can_BaudrateConfigPtrType',0,7,185,4,16,4,15
	.word	17858
	.byte	4
	.word	18055
	.byte	10
	.byte	'Can_kBaudrateConfigPtr',0,4
	.word	18060
	.byte	2,35,0,0,5
	.byte	'Can_BaudrateConfigPtrType',0,7,188,4,3
	.word	18023
	.byte	7
	.byte	'Can_FDConfigParamType',0,7,193,4,16,6,10
	.byte	'CanControllerFDBaudrate',0,2
	.word	207
	.byte	2,35,0,10
	.byte	'CanControllerTxDelayComp',0,2
	.word	207
	.byte	2,35,2,10
	.byte	'CanControllerTxBRS',0,1
	.word	190
	.byte	2,35,4,0,5
	.byte	'Can_FDConfigParamType',0,7,198,4,3
	.word	18133
	.byte	7
	.byte	'Can_FDConfigParamPtrType',0,7,200,4,16,4,15
	.word	18133
	.byte	4
	.word	18319
	.byte	10
	.byte	'Can_kFDConfigParamPtr',0,4
	.word	18324
	.byte	2,35,0,0,5
	.byte	'Can_FDConfigParamPtrType',0,7,203,4,3
	.word	18288
	.byte	7
	.byte	'Can_EventHandlingType',0,7,210,4,16,4,10
	.byte	'CanEventType',0,4
	.word	17709
	.byte	2,35,0,0,5
	.byte	'Can_EventHandlingType',0,7,213,4,3
	.word	18395
	.byte	12
	.word	11852
	.byte	4
	.word	18477
	.byte	13,8
	.word	18482
	.byte	14,1,0
.L25:
	.byte	15
	.word	18487
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L6:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,59,0,3,8,0,0,4,15,0,73,19,0
	.byte	0,5,22,0,3,8,58,15,59,15,57,15,73,19,0,0,6,21,0,54,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13
	.byte	0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,23,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9
	.byte	0,0,11,13,0,11,15,73,19,56,9,0,0,12,53,0,73,19,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,38,0,73
	.byte	19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L7:
	.word	.L27-.L26
.L26:
	.half	3
	.word	.L29-.L28
.L28:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Can_17_MCanP_Platform.c',0,0,0,0
	.byte	'..\\mcal_src\\IfxCan_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\ComStack_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Can_GeneralTypes.h',0,0,0,0
	.byte	'..\\mcal_src\\Can_17_MCanP.h',0,0,0,0,0
.L29:
.L27:
	.sdecl	'.debug_info',debug,cluster('Can_lFrameInitialise')
	.sect	'.debug_info'
.L8:
	.word	389
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_src\\Can_17_MCanP_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L11,.L10
	.byte	2
	.word	.L4
	.byte	3
	.byte	'Can_lFrameInitialise',0,1,106,6,1,1,1
	.word	.L3,.L15,.L2
	.byte	4
	.byte	'KerIdx',0,1,106,33
	.word	.L16,.L17
	.byte	4
	.byte	'HwCtrlIDKer',0,1,106,46
	.word	.L16,.L18
	.byte	4
	.byte	'CanControllerFDBaudrate',0,1,107,25
	.word	.L19,.L20
	.byte	4
	.byte	'CanControllerTxDelayComp',0,1,107,56
	.word	.L19,.L21
	.byte	5
	.word	.L3,.L15
	.byte	5
	.word	.L3,.L22
	.byte	6
	.byte	'val',0,1,109,5
	.word	.L23,.L24
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Can_lFrameInitialise')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Can_lFrameInitialise')
	.sect	'.debug_line'
.L10:
	.word	.L31-.L30
.L30:
	.half	3
	.word	.L33-.L32
.L32:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Can_17_MCanP_Platform.c',0,0,0,0,0
.L33:
	.byte	5,5,7,0,5,2
	.word	.L3
	.byte	3,236,0,1,9
	.half	.L22-.L3
	.byte	3,6,1,9
	.half	.L34-.L22
	.byte	3,6,1,5,1,9
	.half	.L35-.L34
	.byte	3,2,1,7,9
	.half	.L12-.L35
	.byte	0,1,1
.L31:
	.sdecl	'.debug_ranges',debug,cluster('Can_lFrameInitialise')
	.sect	'.debug_ranges'
.L11:
	.word	-1,.L3,0,.L12-.L3,0,0
	.sdecl	'.debug_info',debug,cluster('CAN_KER')
	.sect	'.debug_info'
.L13:
	.word	211
	.half	3
	.word	.L14
	.byte	4,1
	.byte	'..\\mcal_src\\Can_17_MCanP_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L4
	.byte	3
	.byte	'CAN_KER',0,1,54,16
	.word	.L25
	.byte	1,5,3
	.word	CAN_KER
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('CAN_KER')
	.sect	'.debug_abbrev'
.L14:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('Can_lFrameInitialise')
	.sect	'.debug_loc'
.L20:
	.word	-1,.L3,0,.L15-.L3
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L21:
	.word	-1,.L3,0,.L15-.L3
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
.L2:
	.word	-1,.L3,0,.L15-.L3
	.half	2
	.byte	138,0
	.word	0,0
.L18:
	.word	-1,.L3,0,.L15-.L3
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L17:
	.word	-1,.L3,0,.L15-.L3
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L24:
	.word	-1,.L3,.L22-.L3,.L15-.L3
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L36:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Can_lFrameInitialise')
	.sect	'.debug_frame'
	.word	24
	.word	.L36,.L3,.L15-.L3
	.byte	8,19,8,20,8,21,8,22,8,23,0,0

; ..\mcal_src\Can_17_MCanP_Platform.c	   124  #define CAN_17_MCANP_STOP_SEC_CODE
; ..\mcal_src\Can_17_MCanP_Platform.c	   125  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directive is
; ..\mcal_src\Can_17_MCanP_Platform.c	   126   allowed only for MemMap.h*/
; ..\mcal_src\Can_17_MCanP_Platform.c	   127  #include "MemMap.h"

	; Module end
