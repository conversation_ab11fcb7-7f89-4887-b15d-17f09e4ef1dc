	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc792a -c99 --dep-file=flash\\.FL_Cfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=flash\\FL_Cfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o flash\\FL_Cfg.src ..\\flash\\FL_Cfg.c"
	.compiler_name		"ctc"
	.name	"FL_Cfg"

	
$TC16X
	
	.sdecl	'.data.FL_Cfg.CurrentProgrammingBlock',data,cluster('CurrentProgrammingBlock')
	.sect	'.data.FL_Cfg.CurrentProgrammingBlock'
	.global	CurrentProgrammingBlock
CurrentProgrammingBlock:	.type	object
	.size	CurrentProgrammingBlock,1
	.byte	255
	.sdecl	'.data.FL_Cfg.CurrentErasingAddress',data,cluster('CurrentErasingAddress')
	.sect	'.data.FL_Cfg.CurrentErasingAddress'
	.global	CurrentErasingAddress
	.align	4
CurrentErasingAddress:	.type	object
	.size	CurrentErasingAddress,4
	.space	4
	.sdecl	'.data.FL_Cfg.CurrentErasingLength',data,cluster('CurrentErasingLength')
	.sect	'.data.FL_Cfg.CurrentErasingLength'
	.global	CurrentErasingLength
	.align	4
CurrentErasingLength:	.type	object
	.size	CurrentErasingLength,4
	.space	4
	.sdecl	'.rodata.FL_Cfg.FL_BlkInfo',data,rom,cluster('FL_BlkInfo')
	.sect	'.rodata.FL_Cfg.FL_BlkInfo'
	.global	FL_BlkInfo
	.align	4
FL_BlkInfo:	.type	object
	.size	FL_BlkInfo,48
	.space	4
	.word	534
	.byte	1,2
	.word	-1610088448
	.byte	1
	.space	1
	.word	65535
	.byte	1
	.space	3
	.word	-1610088448,1572864
	.space	1
	.byte	2
	.word	-1610088448
	.byte	1
	.space	1
	.word	65535
	.byte	1
	.space	3
	.sdecl	'.bss.FL_Cfg.FL_Header_Buffer',data,cluster('FL_Header_Buffer')
	.sect	'.bss.FL_Cfg.FL_Header_Buffer'
	.global	FL_Header_Buffer
	.align	4
FL_Header_Buffer:	.type	object
	.size	FL_Header_Buffer,640
	.space	640
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	1290
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\flash\\FL_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	172
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	178
	.byte	5,1,3
	.word	202
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	204
.L18:
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	227
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	258
.L19:
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	295
	.byte	4
	.byte	'boolean',0,2,105,29
	.word	227
	.byte	7,3,52,9,164,1,8,164,1
	.word	227
	.byte	9,163,1,0,10
	.byte	'datas',0,164,1
	.word	353
	.byte	2,35,0,11,3,55,5,164,1,10
	.byte	'certFmt',0,1
	.word	227
	.byte	2,35,0,8,8
	.word	227
	.byte	9,7,0,10
	.byte	'pModNum',0,8
	.word	403
	.byte	2,35,1,8,16
	.word	227
	.byte	9,15,0,10
	.byte	'customPars',0,16
	.word	429
	.byte	2,35,9,8,3
	.word	227
	.byte	9,2,0,10
	.byte	'certFailDate',0,3
	.word	458
	.byte	2,35,25,8,4
	.word	227
	.byte	9,3,0,10
	.byte	'certSequenceNum',0,4
	.word	489
	.byte	2,35,28,10
	.byte	'signAlgoFlg',0,1
	.word	227
	.byte	2,35,32,10
	.byte	'pubKeyCurPar',0,1
	.word	227
	.byte	2,35,33,10
	.byte	'hashAlgoFlg',0,1
	.word	227
	.byte	2,35,34,10
	.byte	'pubKeyIdx',0,1
	.word	227
	.byte	2,35,35,8,64
	.word	227
	.byte	9,63,0,10
	.byte	'certPubKey',0,64
	.word	606
	.byte	2,35,36,10
	.byte	'certSigner',0,64
	.word	606
	.byte	2,35,100,0,10
	.byte	'parameters',0,164,1
	.word	380
	.byte	2,35,0,0,4
	.byte	'Secure_SignerInfoType',0,3,68,3
	.word	347
	.byte	4
	.byte	'_iob_flag_t',0,4,75,25
	.word	258
	.byte	12,5,72,9,1,13
	.byte	'INTERNAL_FLS',0,0,13
	.byte	'EXTERNAL_FLS',0,1,0,4
	.byte	'FL_FlashType',0,5,76,2
	.word	728
	.byte	12,5,78,9,1,13
	.byte	'NO_CRC',0,0,13
	.byte	'LAST_ADDR',0,1,13
	.byte	'HEAD_ADDR',0,2,0,4
	.byte	'FL_CrcAddrType',0,5,83,2
	.word	785
	.byte	11,5,89,9,24,14
	.word	295
	.byte	10
	.byte	'address',0,4
	.word	852
	.byte	2,35,0,14
	.word	295
	.byte	10
	.byte	'length',0,4
	.word	874
	.byte	2,35,4,14
	.word	728
	.byte	10
	.byte	'flashtype',0,1
	.word	895
	.byte	2,35,8,14
	.word	785
	.byte	10
	.byte	'crcaddrtype',0,1
	.word	919
	.byte	2,35,9,14
	.word	295
	.byte	10
	.byte	'crcaddress',0,4
	.word	945
	.byte	2,35,10,14
	.word	227
	.byte	10
	.byte	'isvital',0,1
	.word	970
	.byte	2,35,14,14
	.word	295
	.byte	10
	.byte	'maxProgAttempt',0,4
	.word	992
	.byte	2,35,16,14
	.word	227
	.byte	10
	.byte	'moduleid',0,1
	.word	1021
	.byte	2,35,20,0,4
	.byte	'FL_BlockDescriptorType',0,5,105,3
	.word	847
	.byte	11,5,108,9,8,10
	.byte	'address',0,4
	.word	295
	.byte	2,35,0,10
	.byte	'length',0,4
	.word	295
	.byte	2,35,4,0,4
	.byte	'FL_SegmentInfoType',0,5,116,3
	.word	1076
	.byte	11,5,129,1,9,20,10
	.byte	'blkValid',0,1
	.word	227
	.byte	2,35,0,10
	.byte	'blkProgAttempt',0,2
	.word	258
	.byte	2,35,2,10
	.byte	'blkChecksum',0,4
	.word	295
	.byte	2,35,4,8,9
	.word	227
	.byte	9,8,0,10
	.byte	'fingerPrint',0,9
	.word	1211
	.byte	2,35,8,0,4
	.byte	'FL_blockInfoType',0,5,139,1,3
	.word	1142
.L16:
	.byte	8,128,5
	.word	227
	.byte	9,255,4,0,8,48
	.word	847
	.byte	9,1,0
.L17:
	.byte	14
	.word	1279
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,23,1,58,15,59,15,57,15,11,15,0,0,8,1,1,11,15
	.byte	73,19,0,0,9,33,0,47,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0,0,11,19,1,58,15,59,15,57,15,11,15,0,0,12,4,1
	.byte	58,15,59,15,57,15,11,15,0,0,13,40,0,3,8,28,13,0,0,14,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L21-.L20
.L20:
	.half	3
	.word	.L23-.L22
.L22:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0,0
	.byte	'..\\flash\\FL_Cfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Secure_Types.h',0,2,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'..\\flash\\FL.h',0,0,0,0,0
.L23:
.L21:
	.sdecl	'.debug_info',debug,cluster('FL_Header_Buffer')
	.sect	'.debug_info'
.L6:
	.word	202
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\flash\\FL_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'FL_Header_Buffer',0,1,43,7
	.word	.L16
	.byte	1,5,3
	.word	FL_Header_Buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FL_Header_Buffer')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('FL_BlkInfo')
	.sect	'.debug_info'
.L8:
	.word	196
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\flash\\FL_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'FL_BlkInfo',0,1,37,30
	.word	.L17
	.byte	1,5,3
	.word	FL_BlkInfo
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FL_BlkInfo')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('CurrentProgrammingBlock')
	.sect	'.debug_info'
.L10:
	.word	209
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\flash\\FL_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'CurrentProgrammingBlock',0,1,34,7
	.word	.L18
	.byte	1,5,3
	.word	CurrentProgrammingBlock
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('CurrentProgrammingBlock')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('CurrentErasingAddress')
	.sect	'.debug_info'
.L12:
	.word	207
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\flash\\FL_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'CurrentErasingAddress',0,1,35,8
	.word	.L19
	.byte	1,5,3
	.word	CurrentErasingAddress
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('CurrentErasingAddress')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('CurrentErasingLength')
	.sect	'.debug_info'
.L14:
	.word	206
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\flash\\FL_Cfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'CurrentErasingLength',0,1,36,8
	.word	.L19
	.byte	1,5,3
	.word	CurrentErasingLength
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('CurrentErasingLength')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0

; ..\flash\FL_Cfg.c	     1  /*============================================================================*/
; ..\flash\FL_Cfg.c	     2  /** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
; ..\flash\FL_Cfg.c	     3   *  
; ..\flash\FL_Cfg.c	     4   *  All rights reserved. This software is iSOFT property. Duplication 
; ..\flash\FL_Cfg.c	     5   *  or disclosure without iSOFT written authorization is prohibited.
; ..\flash\FL_Cfg.c	     6   *  
; ..\flash\FL_Cfg.c	     7   *  @file       <FL_Cfg.c>
; ..\flash\FL_Cfg.c	     8   *  @brief      <Flash Loader Configuration >
; ..\flash\FL_Cfg.c	     9   *               describe the block infomation.
; ..\flash\FL_Cfg.c	    10   *  
; ..\flash\FL_Cfg.c	    11   *  <Compiler: CodeWarrior    MCU:9S12G64>
; ..\flash\FL_Cfg.c	    12   *
; ..\flash\FL_Cfg.c	    13   *  <AUTHOR> Chen>
; ..\flash\FL_Cfg.c	    14   *  @date       <2012-12-27>
; ..\flash\FL_Cfg.c	    15   */
; ..\flash\FL_Cfg.c	    16  /*============================================================================*/
; ..\flash\FL_Cfg.c	    17  
; ..\flash\FL_Cfg.c	    18  /*=======[R E V I S I O N   H I S T O R Y]====================================*/
; ..\flash\FL_Cfg.c	    19  /** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
; ..\flash\FL_Cfg.c	    20   *  V1.0    20121227    Gary       Initial version
; ..\flash\FL_Cfg.c	    21   *
; ..\flash\FL_Cfg.c	    22   *  V1.1    20130913    ccl        update
; ..\flash\FL_Cfg.c	    23   */
; ..\flash\FL_Cfg.c	    24  /*============================================================================*/
; ..\flash\FL_Cfg.c	    25  
; ..\flash\FL_Cfg.c	    26  /*=======[I N C L U D E S]====================================================*/
; ..\flash\FL_Cfg.c	    27  #include "FL.h"
; ..\flash\FL_Cfg.c	    28  
; ..\flash\FL_Cfg.c	    29  
; ..\flash\FL_Cfg.c	    30  #define APP_INTEGRITY_ADDR 0xA0080000uL //0xA0028000
; ..\flash\FL_Cfg.c	    31  #define CAL_INTEGRITY_ADDR 0xA0028000
; ..\flash\FL_Cfg.c	    32  #define EXT_INTEGRITY_ADDR 0x05000000
; ..\flash\FL_Cfg.c	    33  /*=======[E X T E R N A L   D A T A]==========================================*/
; ..\flash\FL_Cfg.c	    34  uint8 CurrentProgrammingBlock=0xFF;
; ..\flash\FL_Cfg.c	    35  uint32 CurrentErasingAddress=0x00;
; ..\flash\FL_Cfg.c	    36  uint32 CurrentErasingLength=0x00;
; ..\flash\FL_Cfg.c	    37  const FL_BlockDescriptorType FL_BlkInfo[FL_NUM_LOGICAL_BLOCKS ] =
; ..\flash\FL_Cfg.c	    38      {
; ..\flash\FL_Cfg.c	    39          { 0x00000000uL, 0x216uL,EXTERNAL_FLS,HEAD_ADDR,APP_INTEGRITY_ADDR,TRUE, 0xFFFFu,0x01 },    //code
; ..\flash\FL_Cfg.c	    40  //        { 0xA0028000uL, 0x1D8000uL,INTERNAL_FLS,HEAD_ADDR,APP_INTEGRITY_ADDR,TRUE, 0xFFFFu,0x01 },    //code
; ..\flash\FL_Cfg.c	    41          { 0xA0080000uL, 0x180000uL,INTERNAL_FLS,HEAD_ADDR,APP_INTEGRITY_ADDR,TRUE, 0xFFFFu,0x01 },
; ..\flash\FL_Cfg.c	    42      };
; ..\flash\FL_Cfg.c	    43  uint8 FL_Header_Buffer[640u];
; ..\flash\FL_Cfg.c	    44  
; ..\flash\FL_Cfg.c	    45  /*=======[E N D   O F   F I L E]==============================================*/
; ..\flash\FL_Cfg.c	    46  

	; Module end
