	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc11248a -c99 --dep-file=vss_code\\.vsscommon.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=vss_code\\vsscommon.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o vss_code\\vsscommon.src ..\\vss_code\\vsscommon.c"
	.compiler_name		"ctc"
	.name	"vsscommon"

	
$TC16X
	
	.sdecl	'.text.vss_api_code',code,cluster('VssA2I')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	VssA2I

; ..\vss_code\vsscommon.c	     1  #include "vsscommon.h"
; ..\vss_code\vsscommon.c	     2  #include "vssconf.h"
; ..\vss_code\vsscommon.c	     3  
; ..\vss_code\vsscommon.c	     4  #pragma section code "vss_api_code" 
; ..\vss_code\vsscommon.c	     5  
; ..\vss_code\vsscommon.c	     6  vss_sint32 VssA2I(const char*  content)
; Function VssA2I
.L55:
VssA2I:	.type	func

; ..\vss_code\vsscommon.c	     7  {
; ..\vss_code\vsscommon.c	     8  	vss_sint32 rt = 0;
	mov	d2,#0
.L227:

; ..\vss_code\vsscommon.c	     9  	vss_sint32 sig = 1;
	mov	d0,#1
.L228:

; ..\vss_code\vsscommon.c	    10  	vss_uint32 off = 0;
; ..\vss_code\vsscommon.c	    11  
; ..\vss_code\vsscommon.c	    12  	if(content == VSS_NULL)
	mov	d1,d2
	jz.a	a4,.L3
.L229:

; ..\vss_code\vsscommon.c	    13  		return 0;
; ..\vss_code\vsscommon.c	    14  
; ..\vss_code\vsscommon.c	    15  	if(content[off] == '-')
	ld.b	d3,[a4]0
.L247:
	mov	d15,#45
.L248:
	eq	d4,d3,#45
.L249:

; ..\vss_code\vsscommon.c	    16  		sig = -1;
	seln	d0,d4,d0,#-1
.L250:

; ..\vss_code\vsscommon.c	    17  
; ..\vss_code\vsscommon.c	    18  	if(content[off] == '-' ||content[off] == '+' )
	jeq	d15,d3,.L5
.L251:
	mov	d15,#43
.L252:
	jne	d15,d3,.L6
.L5:

; ..\vss_code\vsscommon.c	    19  		off++;
	mov	d1,#1
.L6:

; ..\vss_code\vsscommon.c	    20  
; ..\vss_code\vsscommon.c	    21  	while (content[off] >= '0' && content[off] <= '9')
; ..\vss_code\vsscommon.c	    22  	{
; ..\vss_code\vsscommon.c	    23  		rt = rt * 10 + (content[off] - '0');
	addsc.a	a15,a4,d1,#0
.L253:
	j	.L7
.L8:
	ld.b	d1,[a15+]1
.L254:
	add	d1,d1,#-48
.L255:
	madd	d2,d1,d2,#10
.L7:
	ld.b	d15,[a15]0
	add	d15,d15,#-48
	extr.u	d15,d15,#0,#8
.L256:
	jlt.u	d15,#10,.L8
.L257:

; ..\vss_code\vsscommon.c	    24  		off++;
; ..\vss_code\vsscommon.c	    25  	}
; ..\vss_code\vsscommon.c	    26  
; ..\vss_code\vsscommon.c	    27  	return rt * sig;
	mul	d2,d0

; ..\vss_code\vsscommon.c	    28  }
.L3:
	ret
.L150:
	
__VssA2I_function_end:
	.size	VssA2I,__VssA2I_function_end-VssA2I
.L88:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_set')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_set

; ..\vss_code\vsscommon.c	    29  
; ..\vss_code\vsscommon.c	    30  
; ..\vss_code\vsscommon.c	    31  //len:输入数据字数
; ..\vss_code\vsscommon.c	    32  void mem_set(vss_uint32 *result, const vss_uint32 content, vss_uint32 len)
; Function mem_set
.L57:
mem_set:	.type	func

; ..\vss_code\vsscommon.c	    33  {
; ..\vss_code\vsscommon.c	    34  	vss_uint32 i = 0;
	mov	d15,#0
.L230:

; ..\vss_code\vsscommon.c	    35  	if(len < 1)	return;
	jeq	d5,#0,.L10

; ..\vss_code\vsscommon.c	    36  
; ..\vss_code\vsscommon.c	    37  	for (; i < len; i++)
.L11:

; ..\vss_code\vsscommon.c	    38  		result[i]=content;
	add	d15,#1
	st.w	[a4+],d4
.L262:
	jlt.u	d15,d5,.L11

; ..\vss_code\vsscommon.c	    39  }
.L10:
	ret
.L157:
	
__mem_set_function_end:
	.size	mem_set,__mem_set_function_end-mem_set
.L93:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_set8')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_set8

; ..\vss_code\vsscommon.c	    40  
; ..\vss_code\vsscommon.c	    41  //len:输入数据字节数
; ..\vss_code\vsscommon.c	    42  void mem_set8(vss_uint8 *result, const vss_uint8 content, vss_uint32 len)
; Function mem_set8
.L59:
mem_set8:	.type	func

; ..\vss_code\vsscommon.c	    43  {
; ..\vss_code\vsscommon.c	    44  #if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
; ..\vss_code\vsscommon.c	    45  	memset(result, content, len);
; ..\vss_code\vsscommon.c	    46  #else
; ..\vss_code\vsscommon.c	    47  	vss_uint32 i = 0;
	mov	d15,#0
.L231:

; ..\vss_code\vsscommon.c	    48  	if(len < 1)	return;
	jeq	d5,#0,.L12

; ..\vss_code\vsscommon.c	    49  
; ..\vss_code\vsscommon.c	    50  	for (; i < len; i++)
.L13:

; ..\vss_code\vsscommon.c	    51  		result[i] = content;
	add	d15,#1
	st.b	[a4+],d4
.L267:
	jlt.u	d15,d5,.L13

; ..\vss_code\vsscommon.c	    52  #endif
; ..\vss_code\vsscommon.c	    53  }
.L12:
	ret
.L164:
	
__mem_set8_function_end:
	.size	mem_set8,__mem_set8_function_end-mem_set8
.L98:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_cpy')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_cpy

; ..\vss_code\vsscommon.c	    54  
; ..\vss_code\vsscommon.c	    55  //len:输入数据字节数
; ..\vss_code\vsscommon.c	    56  void mem_cpy(vss_uint32 *result, const vss_uint32 *content, vss_uint32 len)
; Function mem_cpy
.L61:
mem_cpy:	.type	func

; ..\vss_code\vsscommon.c	    57  {
; ..\vss_code\vsscommon.c	    58  #if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
; ..\vss_code\vsscommon.c	    59  	memcpy(result, content, len * sizeof(vss_uint32));
; ..\vss_code\vsscommon.c	    60  #else
; ..\vss_code\vsscommon.c	    61  	vss_uint32 i = 0;
	mov	d15,#0
.L232:

; ..\vss_code\vsscommon.c	    62  	if(len < 1)	return;
	jeq	d4,#0,.L14

; ..\vss_code\vsscommon.c	    63  
; ..\vss_code\vsscommon.c	    64  	for (; i < len; i++)
.L15:

; ..\vss_code\vsscommon.c	    65  		result[i]=content[i];
	add	d15,#1
	ld.w	d0,[a5+]
.L272:
	st.w	[a4+],d0
.L273:
	jlt.u	d15,d4,.L15

; ..\vss_code\vsscommon.c	    66  #endif
; ..\vss_code\vsscommon.c	    67  }
.L14:
	ret
.L171:
	
__mem_cpy_function_end:
	.size	mem_cpy,__mem_cpy_function_end-mem_cpy
.L103:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_cpy8')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_cpy8

; ..\vss_code\vsscommon.c	    68  
; ..\vss_code\vsscommon.c	    69  //len:锟斤拷锟斤拷锟斤拷锟斤拷纸锟斤拷锟?
; ..\vss_code\vsscommon.c	    70  void mem_cpy8(vss_uint8 *result, const vss_uint8 *content, vss_uint32 len)
; Function mem_cpy8
.L63:
mem_cpy8:	.type	func

; ..\vss_code\vsscommon.c	    71  {
; ..\vss_code\vsscommon.c	    72  #if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
; ..\vss_code\vsscommon.c	    73  	memcpy(result, content, len);
; ..\vss_code\vsscommon.c	    74  #else
; ..\vss_code\vsscommon.c	    75  	vss_uint32 i = 0;
	mov	d15,#0
.L233:

; ..\vss_code\vsscommon.c	    76  
; ..\vss_code\vsscommon.c	    77  	if(len < 1)	return;
	jeq	d4,#0,.L16

; ..\vss_code\vsscommon.c	    78  
; ..\vss_code\vsscommon.c	    79  	for (; i < len; i++)
.L17:

; ..\vss_code\vsscommon.c	    80  		result[i] = content[i];
	add	d15,#1
	ld.bu	d0,[a5+]
.L278:
	st.b	[a4+],d0
.L279:
	jlt.u	d15,d4,.L17

; ..\vss_code\vsscommon.c	    81  #endif
; ..\vss_code\vsscommon.c	    82  }
.L16:
	ret
.L177:
	
__mem_cpy8_function_end:
	.size	mem_cpy8,__mem_cpy8_function_end-mem_cpy8
.L108:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_cmp')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_cmp

; ..\vss_code\vsscommon.c	    83  
; ..\vss_code\vsscommon.c	    84  //len:输入数据字节数
; ..\vss_code\vsscommon.c	    85  vss_uint8 mem_cmp(const   vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len)
; Function mem_cmp
.L65:
mem_cmp:	.type	func

; ..\vss_code\vsscommon.c	    86  {
; ..\vss_code\vsscommon.c	    87  	vss_sint32 i;
; ..\vss_code\vsscommon.c	    88  	
; ..\vss_code\vsscommon.c	    89  	if(len < 1)	return EQUAL;
	jeq	d4,#0,.L18
.L284:

; ..\vss_code\vsscommon.c	    90  
; ..\vss_code\vsscommon.c	    91  	for (i=len-1;i>=0;i--)
	add	d15,d4,#-1
.L234:

; ..\vss_code\vsscommon.c	    92  		if (data1[i]>data2[i])
	addsc.a	a15,a5,d15,#2
.L285:
	addsc.a	a2,a4,d15,#2
.L286:
	j	.L19
.L20:
	ld.w	d0,[a15]
.L287:
	ld.w	d1,[a2]
.L288:
	jge.u	d0,d1,.L21
.L289:

; ..\vss_code\vsscommon.c	    93  			return BIGGER;
; ..\vss_code\vsscommon.c	    94  		else if (data1[i]<data2[i])
; ..\vss_code\vsscommon.c	    95  			return SMALLER;
; ..\vss_code\vsscommon.c	    96  
; ..\vss_code\vsscommon.c	    97  	return EQUAL;
; ..\vss_code\vsscommon.c	    98  }
	mov	d2,#1
	ret
.L21:
	jge.u	d1,d0,.L23
.L290:
	mov	d2,#2
	ret
.L23:
	add	d15,#-1
	add.a	a15,#-4
	add.a	a2,#-4
.L19:
	jge	d15,#0,.L20
.L18:
	mov	d2,#0
	ret
.L184:
	
__mem_cmp_function_end:
	.size	mem_cmp,__mem_cmp_function_end-mem_cmp
.L113:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_cmp8')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_cmp8

; ..\vss_code\vsscommon.c	    99  
; ..\vss_code\vsscommon.c	   100  //len:输入数据字节数
; ..\vss_code\vsscommon.c	   101  vss_uint8 mem_cmp8(const vss_uint8 *data1, const vss_uint8 *data2, vss_uint32 len)
; Function mem_cmp8
.L67:
mem_cmp8:	.type	func

; ..\vss_code\vsscommon.c	   102  {
; ..\vss_code\vsscommon.c	   103  #if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
; ..\vss_code\vsscommon.c	   104  	memcmp(data1, data2, len);
; ..\vss_code\vsscommon.c	   105  #else
; ..\vss_code\vsscommon.c	   106  	vss_sint32 i = 0;
; ..\vss_code\vsscommon.c	   107  	
; ..\vss_code\vsscommon.c	   108  	if(len < 1)	return EQUAL;
	jeq	d4,#0,.L26
.L295:

; ..\vss_code\vsscommon.c	   109  
; ..\vss_code\vsscommon.c	   110  	for (i = len - 1; i >= 0; i--)
	add	d15,d4,#-1
.L235:

; ..\vss_code\vsscommon.c	   111  	{
; ..\vss_code\vsscommon.c	   112  		if (data1[i] > data2[i])
	addsc.a	a15,a5,d15,#0
.L296:
	addsc.a	a2,a4,d15,#0
.L297:
	j	.L27
.L28:
	ld.bu	d0,[a15]
.L298:
	ld.bu	d1,[a2]
.L299:
	jge.u	d0,d1,.L29
.L300:

; ..\vss_code\vsscommon.c	   113  			return BIGGER;
; ..\vss_code\vsscommon.c	   114  		else if (data1[i] < data2[i])
; ..\vss_code\vsscommon.c	   115  			return SMALLER;
; ..\vss_code\vsscommon.c	   116  	}
; ..\vss_code\vsscommon.c	   117  
; ..\vss_code\vsscommon.c	   118  	return EQUAL;
; ..\vss_code\vsscommon.c	   119  #endif
; ..\vss_code\vsscommon.c	   120  }
	mov	d2,#1
	ret
.L29:
	jge.u	d1,d0,.L31
.L301:
	mov	d2,#2
	ret
.L31:
	add	d15,#-1
	add.a	a15,#-1
	add.a	a2,#-1
.L27:
	jge	d15,#0,.L28
.L26:
	mov	d2,#0
	ret
.L189:
	
__mem_cmp8_function_end:
	.size	mem_cmp8,__mem_cmp8_function_end-mem_cmp8
.L118:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_cmp2')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_cmp2

; ..\vss_code\vsscommon.c	   121  
; ..\vss_code\vsscommon.c	   122  //len:输入数据字数
; ..\vss_code\vsscommon.c	   123  vss_uint8 mem_cmp2(const vss_uint32 *data1, const vss_uint32 data2, vss_uint32 len)
; Function mem_cmp2
.L69:
mem_cmp2:	.type	func

; ..\vss_code\vsscommon.c	   124  {
; ..\vss_code\vsscommon.c	   125  	vss_uint32 i;
; ..\vss_code\vsscommon.c	   126  
; ..\vss_code\vsscommon.c	   127  	for (i=len-1;i>=1;i--)
	add	d15,d5,#-1
.L236:

; ..\vss_code\vsscommon.c	   128  		if (data1[i]!=0)
	addsc.a	a15,a4,d15,#2
.L306:
	j	.L34
.L35:
	ld.w	d0,[a15]
.L307:
	jne	d0,#0,.L36
.L308:
	add.a	a15,#-4
	add	d15,#-1
.L34:
	jne	d15,#0,.L35
.L309:

; ..\vss_code\vsscommon.c	   129  			return BIGGER;
; ..\vss_code\vsscommon.c	   130  	if (data1[0]==data2)
	ld.w	d15,[a4]
.L237:
	jne	d15,d4,.L37
.L310:

; ..\vss_code\vsscommon.c	   131  		return EQUAL;
; ..\vss_code\vsscommon.c	   132  	else if (data1[0]>data2)
; ..\vss_code\vsscommon.c	   133  		return BIGGER;
; ..\vss_code\vsscommon.c	   134  	else
; ..\vss_code\vsscommon.c	   135  		return SMALLER;
; ..\vss_code\vsscommon.c	   136  }
	mov	d2,#0
	ret
.L37:
	jge.u	d4,d15,.L39
.L36:
	mov	d2,#1
	ret
.L39:
	mov	d2,#2
	ret
.L194:
	
__mem_cmp2_function_end:
	.size	mem_cmp2,__mem_cmp2_function_end-mem_cmp2
.L123:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_swap')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_swap

; ..\vss_code\vsscommon.c	   137  
; ..\vss_code\vsscommon.c	   138  //len:输入数据字数
; ..\vss_code\vsscommon.c	   139  void mem_swap(vss_uint32 *data1, vss_uint32 *data2, vss_uint32 len)
; Function mem_swap
.L71:
mem_swap:	.type	func

; ..\vss_code\vsscommon.c	   140  {
; ..\vss_code\vsscommon.c	   141  	vss_uint32 i;
; ..\vss_code\vsscommon.c	   142  	vss_uint32 temp;
; ..\vss_code\vsscommon.c	   143  
; ..\vss_code\vsscommon.c	   144  	for (i=0;i<len;i++)
	mov	d15,#0
	j	.L42
.L43:

; ..\vss_code\vsscommon.c	   145  	{
; ..\vss_code\vsscommon.c	   146  		temp=data1[i];
; ..\vss_code\vsscommon.c	   147  		data1[i]=data2[i];
	ld.w	d1,[a5]
.L315:
	add	d15,#1
	ld.w	d0,[a4]
.L238:
	st.w	[a4+],d1
.L316:

; ..\vss_code\vsscommon.c	   148  		data2[i]=temp;
	st.w	[a5+],d0
.L42:
	jlt.u	d15,d4,.L43
.L317:

; ..\vss_code\vsscommon.c	   149  	}
; ..\vss_code\vsscommon.c	   150  }
	ret
.L200:
	
__mem_swap_function_end:
	.size	mem_swap,__mem_swap_function_end-mem_swap
.L128:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_and')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_and

; ..\vss_code\vsscommon.c	   151  
; ..\vss_code\vsscommon.c	   152  //len:输入数据字数
; ..\vss_code\vsscommon.c	   153  void mem_and(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len)
; Function mem_and
.L73:
mem_and:	.type	func

; ..\vss_code\vsscommon.c	   154  {
; ..\vss_code\vsscommon.c	   155  	vss_uint32 i;
; ..\vss_code\vsscommon.c	   156  
; ..\vss_code\vsscommon.c	   157  	for (i=0;i<len;i++)
	mov	d0,#0
	j	.L44
.L45:

; ..\vss_code\vsscommon.c	   158  		result[i]=data1[i] & data2[i];
	ld.w	d15,[a5+]
.L322:
	ld.w	d1,[a6+]
.L323:
	add	d0,#1
.L324:
	and	d15,d1
	st.w	[a4+],d15
.L44:
	jlt.u	d0,d4,.L45
.L325:

; ..\vss_code\vsscommon.c	   159  }
	ret
.L206:
	
__mem_and_function_end:
	.size	mem_and,__mem_and_function_end-mem_and
.L133:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_or')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_or

; ..\vss_code\vsscommon.c	   160  
; ..\vss_code\vsscommon.c	   161  //len:输入数据字数
; ..\vss_code\vsscommon.c	   162  void mem_or(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len)
; Function mem_or
.L75:
mem_or:	.type	func

; ..\vss_code\vsscommon.c	   163  {
; ..\vss_code\vsscommon.c	   164  	vss_uint32 i;
; ..\vss_code\vsscommon.c	   165  
; ..\vss_code\vsscommon.c	   166  	for (i=0;i<len;i++)
	mov	d0,#0
	j	.L46
.L47:

; ..\vss_code\vsscommon.c	   167  		result[i]=data1[i] | data2[i];
	ld.w	d15,[a5+]
.L330:
	ld.w	d1,[a6+]
.L331:
	add	d0,#1
.L332:
	or	d15,d1
	st.w	[a4+],d15
.L46:
	jlt.u	d0,d4,.L47
.L333:

; ..\vss_code\vsscommon.c	   168  }
	ret
.L212:
	
__mem_or_function_end:
	.size	mem_or,__mem_or_function_end-mem_or
.L138:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('mem_xor')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	mem_xor

; ..\vss_code\vsscommon.c	   169  
; ..\vss_code\vsscommon.c	   170  //len:输入数据字数
; ..\vss_code\vsscommon.c	   171  void mem_xor(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len)
; Function mem_xor
.L77:
mem_xor:	.type	func

; ..\vss_code\vsscommon.c	   172  {
; ..\vss_code\vsscommon.c	   173  	vss_uint32 i;
; ..\vss_code\vsscommon.c	   174  
; ..\vss_code\vsscommon.c	   175  	for (i=0;i<len;i++)
	mov	d0,#0
	j	.L48
.L49:

; ..\vss_code\vsscommon.c	   176  		result[i]=data1[i] ^ data2[i];
	ld.w	d15,[a5+]
.L338:
	ld.w	d1,[a6+]
.L339:
	add	d0,#1
.L340:
	xor	d15,d1
	st.w	[a4+],d15
.L48:
	jlt.u	d0,d4,.L49
.L341:

; ..\vss_code\vsscommon.c	   177  }
	ret
.L218:
	
__mem_xor_function_end:
	.size	mem_xor,__mem_xor_function_end-mem_xor
.L143:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('VssStrLen')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	VssStrLen

; ..\vss_code\vsscommon.c	   178  
; ..\vss_code\vsscommon.c	   179  vss_sint32 VssStrLen(const char*  content)
; Function VssStrLen
.L79:
VssStrLen:	.type	func

; ..\vss_code\vsscommon.c	   180  {
; ..\vss_code\vsscommon.c	   181   vss_sint32 rt = 0;
; ..\vss_code\vsscommon.c	   182  
; ..\vss_code\vsscommon.c	   183   if(content == VSS_NULL)
	mov	d2,#0
	jz.a	a4,.L51

; ..\vss_code\vsscommon.c	   184    return 0;
; ..\vss_code\vsscommon.c	   185  
; ..\vss_code\vsscommon.c	   186  
; ..\vss_code\vsscommon.c	   187   while (content[rt++] != '\0')
.L52:
	ld.bu	d15,[a4+]1
.L346:
	add	d2,#1
.L347:
	jne	d15,#0,.L52

; ..\vss_code\vsscommon.c	   188   {
; ..\vss_code\vsscommon.c	   189   }
; ..\vss_code\vsscommon.c	   190  
; ..\vss_code\vsscommon.c	   191   return rt;
; ..\vss_code\vsscommon.c	   192  }
.L51:
	ret
.L224:
	
__VssStrLen_function_end:
	.size	VssStrLen,__VssStrLen_function_end-VssStrLen
.L148:
	; End of function
	
	.calls	'VssA2I','',0
	.calls	'mem_set','',0
	.calls	'mem_set8','',0
	.calls	'mem_cpy','',0
	.calls	'mem_cpy8','',0
	.calls	'mem_cmp','',0
	.calls	'mem_cmp8','',0
	.calls	'mem_cmp2','',0
	.calls	'mem_swap','',0
	.calls	'mem_and','',0
	.calls	'mem_or','',0
	.calls	'mem_xor','',0
	.calls	'VssStrLen','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L81:
	.word	718
	.half	3
	.word	.L82
	.byte	4
.L80:
	.byte	1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L83
.L149:
	.byte	2
	.byte	'long int',0,4,5,2
	.byte	'char',0,1,6,3
	.word	190
.L151:
	.byte	4
	.word	198
.L155:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L158:
	.byte	4
	.word	208
.L160:
	.byte	3
	.word	208
.L183:
	.byte	2
	.byte	'unsigned char',0,1,8
.L165:
	.byte	4
	.word	239
.L167:
	.byte	3
	.word	239
	.byte	3
	.word	208
.L173:
	.byte	4
	.word	266
	.byte	3
	.word	239
.L179:
	.byte	4
	.word	276
.L196:
	.byte	3
	.word	208
	.byte	5
	.byte	'void',0,4
	.word	291
	.byte	6
	.byte	'__prof_adm',0,1,1,1
	.word	297
	.byte	7,1,4
	.word	321
	.byte	6
	.byte	'__codeptr',0,1,1,1
	.word	323
	.byte	6
	.byte	'vss_uint8',0,2,8,24
	.word	239
	.byte	6
	.byte	'vss_uint32',0,2,13,24
	.word	208
	.byte	6
	.byte	'vss_sint32',0,2,14,23
	.word	178
	.byte	2
	.byte	'unsigned long long int',0,8,7,6
	.byte	'vss_uint64',0,2,17,28
	.word	402
	.byte	6
	.byte	'vss_ulong',0,2,18,24
	.word	208
	.byte	6
	.byte	'BYTE',0,2,21,22
	.word	239
	.byte	6
	.byte	'WORD',0,2,22,22
	.word	208
	.byte	6
	.byte	'SM3_WORD_T',0,2,25,22
	.word	208
	.byte	8,2,27,9,168,1,9
	.byte	'm_size',0,4
	.word	208
	.byte	2,35,0,10,128,1
	.word	239
	.byte	11,127,0,9
	.byte	'remain',0,128,1
	.word	532
	.byte	2,35,4,9
	.byte	'r_len',0,4
	.word	208
	.byte	3,35,132,1,10,32
	.word	208
	.byte	11,7,0,9
	.byte	'iv',0,32
	.word	575
	.byte	3,35,136,1,0,6
	.byte	'SM3_CTX_T',0,2,32,3
	.word	510
	.byte	8,2,34,9,108,10,64
	.word	239
	.byte	11,63,0,9
	.byte	'data',0,64
	.word	621
	.byte	2,35,0,9
	.byte	'datalen',0,4
	.word	208
	.byte	2,35,64,9
	.byte	'bitlen',0,8
	.word	402
	.byte	2,35,68,10,32
	.word	208
	.byte	11,7,0,9
	.byte	'state',0,32
	.word	677
	.byte	2,35,76,0,6
	.byte	'SHA256_CTX',0,2,39,3
	.word	616
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L82:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,38,0,73,19,0,0,4,15,0,73,19
	.byte	0,0,5,59,0,3,8,0,0,6,22,0,3,8,58,15,59,15,57,15,73,19,0,0,7,21,0,54,15,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,11,15,73,19,56,9,0,0,10,1,1,11,15,73,19,0,0,11,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L83:
	.word	.L240-.L239
.L239:
	.half	3
	.word	.L242-.L241
.L241:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0
	.byte	'..\\vss_code\\vsstype.h',0,0,0,0,0
.L242:
.L240:
	.sdecl	'.debug_info',debug,cluster('VssA2I')
	.sect	'.debug_info'
.L84:
	.word	292
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L87,.L86
	.byte	2
	.word	.L80
	.byte	3
	.byte	'VssA2I',0,1,6,12
	.word	.L149
	.byte	1,1,1
	.word	.L55,.L150,.L54
	.byte	4
	.byte	'content',0,1,6,32
	.word	.L151,.L152
	.byte	5
	.word	.L55,.L150
	.byte	6
	.byte	'rt',0,1,8,13
	.word	.L149,.L153
	.byte	6
	.byte	'sig',0,1,9,13
	.word	.L149,.L154
	.byte	6
	.byte	'off',0,1,10,13
	.word	.L155,.L156
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VssA2I')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VssA2I')
	.sect	'.debug_line'
.L86:
	.word	.L244-.L243
.L243:
	.half	3
	.word	.L246-.L245
.L245:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L246:
	.byte	5,16,7,0,5,2
	.word	.L55
	.byte	3,7,1,5,17,9
	.half	.L227-.L55
	.byte	3,1,1,9
	.half	.L228-.L227
	.byte	3,1,1,5,2,3,2,1,5,12,7,9
	.half	.L229-.L228
	.byte	3,3,1,5,21,9
	.half	.L247-.L229
	.byte	1,5,2,9
	.half	.L248-.L247
	.byte	1,5,7,9
	.half	.L249-.L248
	.byte	3,1,1,5,5,9
	.half	.L250-.L249
	.byte	3,2,1,5,43,7,9
	.half	.L251-.L250
	.byte	1,5,40,9
	.half	.L252-.L251
	.byte	1,5,6,7,9
	.half	.L5-.L252
	.byte	3,1,1,5,26,9
	.half	.L6-.L5
	.byte	3,4,1,5,51,9
	.half	.L253-.L6
	.byte	3,126,1,5,26,9
	.half	.L8-.L253
	.byte	3,2,1,5,32,9
	.half	.L254-.L8
	.byte	1,5,16,9
	.half	.L255-.L254
	.byte	1,5,39,9
	.half	.L7-.L255
	.byte	3,126,1,5,45,9
	.half	.L256-.L7
	.byte	1,5,12,7,9
	.half	.L257-.L256
	.byte	3,6,1,5,1,9
	.half	.L3-.L257
	.byte	3,1,1,7,9
	.half	.L88-.L3
	.byte	0,1,1
.L244:
	.sdecl	'.debug_ranges',debug,cluster('VssA2I')
	.sect	'.debug_ranges'
.L87:
	.word	-1,.L55,0,.L88-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('mem_set')
	.sect	'.debug_info'
.L89:
	.word	291
	.half	3
	.word	.L90
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L92,.L91
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_set',0,1,32,6,1,1,1
	.word	.L57,.L157,.L56
	.byte	4
	.byte	'result',0,1,32,26
	.word	.L158,.L159
	.byte	4
	.byte	'content',0,1,32,51
	.word	.L160,.L161
	.byte	4
	.byte	'len',0,1,32,71
	.word	.L155,.L162
	.byte	5
	.word	.L57,.L157
	.byte	6
	.byte	'i',0,1,34,13
	.word	.L155,.L163
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_set')
	.sect	'.debug_abbrev'
.L90:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_set')
	.sect	'.debug_line'
.L91:
	.word	.L259-.L258
.L258:
	.half	3
	.word	.L261-.L260
.L260:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L261:
	.byte	5,15,7,0,5,2
	.word	.L57
	.byte	3,33,1,5,2,9
	.half	.L230-.L57
	.byte	3,1,1,5,19,7,9
	.half	.L11-.L230
	.byte	3,2,1,5,12,3,1,1,5,16,9
	.half	.L262-.L11
	.byte	3,127,1,5,1,7,9
	.half	.L10-.L262
	.byte	3,2,1,7,9
	.half	.L93-.L10
	.byte	0,1,1
.L259:
	.sdecl	'.debug_ranges',debug,cluster('mem_set')
	.sect	'.debug_ranges'
.L92:
	.word	-1,.L57,0,.L93-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('mem_set8')
	.sect	'.debug_info'
.L94:
	.word	292
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L97,.L96
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_set8',0,1,42,6,1,1,1
	.word	.L59,.L164,.L58
	.byte	4
	.byte	'result',0,1,42,26
	.word	.L165,.L166
	.byte	4
	.byte	'content',0,1,42,50
	.word	.L167,.L168
	.byte	4
	.byte	'len',0,1,42,70
	.word	.L155,.L169
	.byte	5
	.word	.L59,.L164
	.byte	6
	.byte	'i',0,1,47,13
	.word	.L155,.L170
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_set8')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_set8')
	.sect	'.debug_line'
.L96:
	.word	.L264-.L263
.L263:
	.half	3
	.word	.L266-.L265
.L265:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L266:
	.byte	5,15,7,0,5,2
	.word	.L59
	.byte	3,46,1,5,2,9
	.half	.L231-.L59
	.byte	3,1,1,5,19,7,9
	.half	.L13-.L231
	.byte	3,2,1,5,13,3,1,1,5,16,9
	.half	.L267-.L13
	.byte	3,127,1,5,1,7,9
	.half	.L12-.L267
	.byte	3,3,1,7,9
	.half	.L98-.L12
	.byte	0,1,1
.L264:
	.sdecl	'.debug_ranges',debug,cluster('mem_set8')
	.sect	'.debug_ranges'
.L97:
	.word	-1,.L59,0,.L98-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('mem_cpy')
	.sect	'.debug_info'
.L99:
	.word	291
	.half	3
	.word	.L100
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L102,.L101
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_cpy',0,1,56,6,1,1,1
	.word	.L61,.L171,.L60
	.byte	4
	.byte	'result',0,1,56,26
	.word	.L158,.L172
	.byte	4
	.byte	'content',0,1,56,52
	.word	.L173,.L174
	.byte	4
	.byte	'len',0,1,56,72
	.word	.L155,.L175
	.byte	5
	.word	.L61,.L171
	.byte	6
	.byte	'i',0,1,61,13
	.word	.L155,.L176
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_cpy')
	.sect	'.debug_abbrev'
.L100:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_cpy')
	.sect	'.debug_line'
.L101:
	.word	.L269-.L268
.L268:
	.half	3
	.word	.L271-.L270
.L270:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L271:
	.byte	5,15,7,0,5,2
	.word	.L61
	.byte	3,60,1,5,2,9
	.half	.L232-.L61
	.byte	3,1,1,5,19,7,9
	.half	.L15-.L232
	.byte	3,2,1,5,20,3,1,1,5,12,9
	.half	.L272-.L15
	.byte	1,5,16,9
	.half	.L273-.L272
	.byte	3,127,1,5,1,7,9
	.half	.L14-.L273
	.byte	3,3,1,7,9
	.half	.L103-.L14
	.byte	0,1,1
.L269:
	.sdecl	'.debug_ranges',debug,cluster('mem_cpy')
	.sect	'.debug_ranges'
.L102:
	.word	-1,.L61,0,.L103-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('mem_cpy8')
	.sect	'.debug_info'
.L104:
	.word	292
	.half	3
	.word	.L105
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L107,.L106
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_cpy8',0,1,70,6,1,1,1
	.word	.L63,.L177,.L62
	.byte	4
	.byte	'result',0,1,70,26
	.word	.L165,.L178
	.byte	4
	.byte	'content',0,1,70,51
	.word	.L179,.L180
	.byte	4
	.byte	'len',0,1,70,71
	.word	.L155,.L181
	.byte	5
	.word	.L63,.L177
	.byte	6
	.byte	'i',0,1,75,13
	.word	.L155,.L182
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_cpy8')
	.sect	'.debug_abbrev'
.L105:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_cpy8')
	.sect	'.debug_line'
.L106:
	.word	.L275-.L274
.L274:
	.half	3
	.word	.L277-.L276
.L276:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L277:
	.byte	5,15,7,0,5,2
	.word	.L63
	.byte	3,202,0,1,5,2,9
	.half	.L233-.L63
	.byte	3,2,1,5,19,7,9
	.half	.L17-.L233
	.byte	3,2,1,5,22,3,1,1,5,13,9
	.half	.L278-.L17
	.byte	1,5,16,9
	.half	.L279-.L278
	.byte	3,127,1,5,1,7,9
	.half	.L16-.L279
	.byte	3,3,1,7,9
	.half	.L108-.L16
	.byte	0,1,1
.L275:
	.sdecl	'.debug_ranges',debug,cluster('mem_cpy8')
	.sect	'.debug_ranges'
.L107:
	.word	-1,.L63,0,.L108-.L63,0,0
	.sdecl	'.debug_info',debug,cluster('mem_cmp')
	.sect	'.debug_info'
.L109:
	.word	292
	.half	3
	.word	.L110
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L112,.L111
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_cmp',0,1,85,11
	.word	.L183
	.byte	1,1,1
	.word	.L65,.L184,.L64
	.byte	4
	.byte	'data1',0,1,85,39
	.word	.L173,.L185
	.byte	4
	.byte	'data2',0,1,85,64
	.word	.L173,.L186
	.byte	4
	.byte	'len',0,1,85,82
	.word	.L155,.L187
	.byte	5
	.word	.L65,.L184
	.byte	6
	.byte	'i',0,1,87,13
	.word	.L149,.L188
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_cmp')
	.sect	'.debug_abbrev'
.L110:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_cmp')
	.sect	'.debug_line'
.L111:
	.word	.L281-.L280
.L280:
	.half	3
	.word	.L283-.L282
.L282:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L283:
	.byte	5,2,7,0,5,2
	.word	.L65
	.byte	3,216,0,1,5,12,7,9
	.half	.L284-.L65
	.byte	3,2,1,5,21,9
	.half	.L234-.L284
	.byte	3,1,1,5,12,9
	.half	.L285-.L234
	.byte	1,5,19,9
	.half	.L286-.L285
	.byte	3,127,1,5,21,9
	.half	.L20-.L286
	.byte	3,1,1,5,12,9
	.half	.L287-.L20
	.byte	1,5,3,9
	.half	.L288-.L287
	.byte	1,5,11,7,9
	.half	.L289-.L288
	.byte	3,1,1,5,1,3,5,1,5,8,7,9
	.half	.L21-.L289
	.byte	3,124,1,5,11,7,9
	.half	.L290-.L21
	.byte	3,1,1,5,1,3,3,1,5,21,7,9
	.half	.L23-.L290
	.byte	3,121,1,5,19,9
	.half	.L19-.L23
	.byte	1,5,9,7,9
	.half	.L18-.L19
	.byte	3,6,1,5,1,3,1,1,7,9
	.half	.L113-.L18
	.byte	0,1,1
.L281:
	.sdecl	'.debug_ranges',debug,cluster('mem_cmp')
	.sect	'.debug_ranges'
.L112:
	.word	-1,.L65,0,.L113-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('mem_cmp8')
	.sect	'.debug_info'
.L114:
	.word	293
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L117,.L116
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_cmp8',0,1,101,11
	.word	.L183
	.byte	1,1,1
	.word	.L67,.L189,.L66
	.byte	4
	.byte	'data1',0,1,101,37
	.word	.L179,.L190
	.byte	4
	.byte	'data2',0,1,101,61
	.word	.L179,.L191
	.byte	4
	.byte	'len',0,1,101,79
	.word	.L155,.L192
	.byte	5
	.word	.L67,.L189
	.byte	6
	.byte	'i',0,1,106,13
	.word	.L149,.L193
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_cmp8')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_cmp8')
	.sect	'.debug_line'
.L116:
	.word	.L292-.L291
.L291:
	.half	3
	.word	.L294-.L293
.L293:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L294:
	.byte	5,2,7,0,5,2
	.word	.L67
	.byte	3,235,0,1,5,15,7,9
	.half	.L295-.L67
	.byte	3,2,1,5,23,9
	.half	.L235-.L295
	.byte	3,2,1,5,12,9
	.half	.L296-.L235
	.byte	1,5,26,9
	.half	.L297-.L296
	.byte	3,126,1,5,23,9
	.half	.L28-.L297
	.byte	3,2,1,5,12,9
	.half	.L298-.L28
	.byte	1,5,3,9
	.half	.L299-.L298
	.byte	1,5,11,7,9
	.half	.L300-.L299
	.byte	3,1,1,5,1,3,7,1,5,8,7,9
	.half	.L29-.L300
	.byte	3,122,1,5,11,7,9
	.half	.L301-.L29
	.byte	3,1,1,5,1,3,5,1,5,29,7,9
	.half	.L31-.L301
	.byte	3,118,1,5,26,9
	.half	.L27-.L31
	.byte	1,5,9,7,9
	.half	.L26-.L27
	.byte	3,8,1,5,1,3,2,1,7,9
	.half	.L118-.L26
	.byte	0,1,1
.L292:
	.sdecl	'.debug_ranges',debug,cluster('mem_cmp8')
	.sect	'.debug_ranges'
.L117:
	.word	-1,.L67,0,.L118-.L67,0,0
	.sdecl	'.debug_info',debug,cluster('mem_cmp2')
	.sect	'.debug_info'
.L119:
	.word	293
	.half	3
	.word	.L120
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L122,.L121
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_cmp2',0,1,123,11
	.word	.L183
	.byte	1,1,1
	.word	.L69,.L194,.L68
	.byte	4
	.byte	'data1',0,1,123,38
	.word	.L173,.L195
	.byte	4
	.byte	'data2',0,1,123,62
	.word	.L196,.L197
	.byte	4
	.byte	'len',0,1,123,80
	.word	.L155,.L198
	.byte	5
	.word	.L69,.L194
	.byte	6
	.byte	'i',0,1,125,13
	.word	.L155,.L199
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_cmp2')
	.sect	'.debug_abbrev'
.L120:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_cmp2')
	.sect	'.debug_line'
.L121:
	.word	.L303-.L302
.L302:
	.half	3
	.word	.L305-.L304
.L304:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L305:
	.byte	5,12,7,0,5,2
	.word	.L69
	.byte	3,254,0,1,9
	.half	.L236-.L69
	.byte	3,1,1,5,19,9
	.half	.L306-.L236
	.byte	3,127,1,5,12,9
	.half	.L35-.L306
	.byte	3,1,1,5,3,9
	.half	.L307-.L35
	.byte	1,5,21,7,9
	.half	.L308-.L307
	.byte	3,127,1,5,19,9
	.half	.L34-.L308
	.byte	1,5,11,7,9
	.half	.L309-.L34
	.byte	3,3,1,5,2,9
	.half	.L237-.L309
	.byte	1,5,10,7,9
	.half	.L310-.L237
	.byte	3,1,1,5,1,3,5,1,5,7,7,9
	.half	.L37-.L310
	.byte	3,124,1,5,10,7,9
	.half	.L36-.L37
	.byte	3,1,1,5,1,3,3,1,5,10,7,9
	.half	.L39-.L36
	.byte	3,127,1,5,1,3,1,1,7,9
	.half	.L123-.L39
	.byte	0,1,1
.L303:
	.sdecl	'.debug_ranges',debug,cluster('mem_cmp2')
	.sect	'.debug_ranges'
.L122:
	.word	-1,.L69,0,.L123-.L69,0,0
	.sdecl	'.debug_info',debug,cluster('mem_swap')
	.sect	'.debug_info'
.L124:
	.word	312
	.half	3
	.word	.L125
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L127,.L126
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_swap',0,1,139,1,6,1,1,1
	.word	.L71,.L200,.L70
	.byte	4
	.byte	'data1',0,1,139,1,27
	.word	.L158,.L201
	.byte	4
	.byte	'data2',0,1,139,1,46
	.word	.L158,.L202
	.byte	4
	.byte	'len',0,1,139,1,64
	.word	.L155,.L203
	.byte	5
	.word	.L71,.L200
	.byte	6
	.byte	'i',0,1,141,1,13
	.word	.L155,.L204
	.byte	6
	.byte	'temp',0,1,142,1,13
	.word	.L155,.L205
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_swap')
	.sect	'.debug_abbrev'
.L125:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_swap')
	.sect	'.debug_line'
.L126:
	.word	.L312-.L311
.L311:
	.half	3
	.word	.L314-.L313
.L313:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L314:
	.byte	5,8,7,0,5,2
	.word	.L71
	.byte	3,143,1,1,5,16,1,5,17,9
	.half	.L43-.L71
	.byte	3,3,1,5,18,9
	.half	.L315-.L43
	.byte	3,125,1,5,13,3,2,1,5,11,9
	.half	.L238-.L315
	.byte	3,1,1,9
	.half	.L316-.L238
	.byte	3,1,1,5,16,9
	.half	.L42-.L316
	.byte	3,124,1,5,1,7,9
	.half	.L317-.L42
	.byte	3,6,1,7,9
	.half	.L128-.L317
	.byte	0,1,1
.L312:
	.sdecl	'.debug_ranges',debug,cluster('mem_swap')
	.sect	'.debug_ranges'
.L127:
	.word	-1,.L71,0,.L128-.L71,0,0
	.sdecl	'.debug_info',debug,cluster('mem_and')
	.sect	'.debug_info'
.L129:
	.word	313
	.half	3
	.word	.L130
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L132,.L131
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_and',0,1,153,1,6,1,1,1
	.word	.L73,.L206,.L72
	.byte	4
	.byte	'result',0,1,153,1,26
	.word	.L158,.L207
	.byte	4
	.byte	'data1',0,1,153,1,52
	.word	.L173,.L208
	.byte	4
	.byte	'data2',0,1,153,1,77
	.word	.L173,.L209
	.byte	4
	.byte	'len',0,1,153,1,95
	.word	.L155,.L210
	.byte	5
	.word	.L73,.L206
	.byte	6
	.byte	'i',0,1,155,1,13
	.word	.L155,.L211
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_and')
	.sect	'.debug_abbrev'
.L130:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_and')
	.sect	'.debug_line'
.L131:
	.word	.L319-.L318
.L318:
	.half	3
	.word	.L321-.L320
.L320:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L321:
	.byte	5,8,7,0,5,2
	.word	.L73
	.byte	3,156,1,1,5,16,1,5,18,9
	.half	.L45-.L73
	.byte	3,1,1,5,29,9
	.half	.L322-.L45
	.byte	1,5,18,9
	.half	.L323-.L322
	.byte	3,127,1,5,22,9
	.half	.L324-.L323
	.byte	3,1,1,5,12,1,5,16,9
	.half	.L44-.L324
	.byte	3,127,1,5,1,7,9
	.half	.L325-.L44
	.byte	3,2,1,7,9
	.half	.L133-.L325
	.byte	0,1,1
.L319:
	.sdecl	'.debug_ranges',debug,cluster('mem_and')
	.sect	'.debug_ranges'
.L132:
	.word	-1,.L73,0,.L133-.L73,0,0
	.sdecl	'.debug_info',debug,cluster('mem_or')
	.sect	'.debug_info'
.L134:
	.word	312
	.half	3
	.word	.L135
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L137,.L136
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_or',0,1,162,1,6,1,1,1
	.word	.L75,.L212,.L74
	.byte	4
	.byte	'result',0,1,162,1,25
	.word	.L158,.L213
	.byte	4
	.byte	'data1',0,1,162,1,51
	.word	.L173,.L214
	.byte	4
	.byte	'data2',0,1,162,1,76
	.word	.L173,.L215
	.byte	4
	.byte	'len',0,1,162,1,94
	.word	.L155,.L216
	.byte	5
	.word	.L75,.L212
	.byte	6
	.byte	'i',0,1,164,1,13
	.word	.L155,.L217
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_or')
	.sect	'.debug_abbrev'
.L135:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_or')
	.sect	'.debug_line'
.L136:
	.word	.L327-.L326
.L326:
	.half	3
	.word	.L329-.L328
.L328:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L329:
	.byte	5,8,7,0,5,2
	.word	.L75
	.byte	3,165,1,1,5,16,1,5,18,9
	.half	.L47-.L75
	.byte	3,1,1,5,29,9
	.half	.L330-.L47
	.byte	1,5,18,9
	.half	.L331-.L330
	.byte	3,127,1,5,22,9
	.half	.L332-.L331
	.byte	3,1,1,5,12,1,5,16,9
	.half	.L46-.L332
	.byte	3,127,1,5,1,7,9
	.half	.L333-.L46
	.byte	3,2,1,7,9
	.half	.L138-.L333
	.byte	0,1,1
.L327:
	.sdecl	'.debug_ranges',debug,cluster('mem_or')
	.sect	'.debug_ranges'
.L137:
	.word	-1,.L75,0,.L138-.L75,0,0
	.sdecl	'.debug_info',debug,cluster('mem_xor')
	.sect	'.debug_info'
.L139:
	.word	313
	.half	3
	.word	.L140
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L142,.L141
	.byte	2
	.word	.L80
	.byte	3
	.byte	'mem_xor',0,1,171,1,6,1,1,1
	.word	.L77,.L218,.L76
	.byte	4
	.byte	'result',0,1,171,1,26
	.word	.L158,.L219
	.byte	4
	.byte	'data1',0,1,171,1,52
	.word	.L173,.L220
	.byte	4
	.byte	'data2',0,1,171,1,77
	.word	.L173,.L221
	.byte	4
	.byte	'len',0,1,171,1,95
	.word	.L155,.L222
	.byte	5
	.word	.L77,.L218
	.byte	6
	.byte	'i',0,1,173,1,13
	.word	.L155,.L223
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('mem_xor')
	.sect	'.debug_abbrev'
.L140:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('mem_xor')
	.sect	'.debug_line'
.L141:
	.word	.L335-.L334
.L334:
	.half	3
	.word	.L337-.L336
.L336:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L337:
	.byte	5,8,7,0,5,2
	.word	.L77
	.byte	3,174,1,1,5,16,1,5,18,9
	.half	.L49-.L77
	.byte	3,1,1,5,29,9
	.half	.L338-.L49
	.byte	1,5,18,9
	.half	.L339-.L338
	.byte	3,127,1,5,22,9
	.half	.L340-.L339
	.byte	3,1,1,5,12,1,5,16,9
	.half	.L48-.L340
	.byte	3,127,1,5,1,7,9
	.half	.L341-.L48
	.byte	3,2,1,7,9
	.half	.L143-.L341
	.byte	0,1,1
.L335:
	.sdecl	'.debug_ranges',debug,cluster('mem_xor')
	.sect	'.debug_ranges'
.L142:
	.word	-1,.L77,0,.L143-.L77,0,0
	.sdecl	'.debug_info',debug,cluster('VssStrLen')
	.sect	'.debug_info'
.L144:
	.word	266
	.half	3
	.word	.L145
	.byte	4,1
	.byte	'..\\vss_code\\vsscommon.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L147,.L146
	.byte	2
	.word	.L80
	.byte	3
	.byte	'VssStrLen',0,1,179,1,12
	.word	.L149
	.byte	1,1,1
	.word	.L79,.L224,.L78
	.byte	4
	.byte	'content',0,1,179,1,35
	.word	.L151,.L225
	.byte	5
	.word	.L79,.L224
	.byte	6
	.byte	'rt',0,1,181,1,13
	.word	.L149,.L226
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('VssStrLen')
	.sect	'.debug_abbrev'
.L145:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('VssStrLen')
	.sect	'.debug_line'
.L146:
	.word	.L343-.L342
.L342:
	.half	3
	.word	.L345-.L344
.L344:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsscommon.c',0,0,0,0,0
.L345:
	.byte	5,16,7,0,5,2
	.word	.L79
	.byte	3,180,1,1,5,2,3,2,1,5,16,7,9
	.half	.L52-.L79
	.byte	3,4,1,5,19,9
	.half	.L346-.L52
	.byte	1,5,30,9
	.half	.L347-.L346
	.byte	1,5,1,7,9
	.half	.L51-.L347
	.byte	3,5,1,7,9
	.half	.L148-.L51
	.byte	0,1,1
.L343:
	.sdecl	'.debug_ranges',debug,cluster('VssStrLen')
	.sect	'.debug_ranges'
.L147:
	.word	-1,.L79,0,.L148-.L79,0,0
	.sdecl	'.debug_loc',debug,cluster('VssA2I')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L150-.L55
	.half	2
	.byte	138,0
	.word	0,0
.L152:
	.word	-1,.L55,0,.L150-.L55
	.half	1
	.byte	100
	.word	0,0
.L156:
	.word	-1,.L55,.L229-.L55,.L8-.L55
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L153:
	.word	-1,.L55,.L227-.L55,.L3-.L55
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L154:
	.word	-1,.L55,.L228-.L55,.L150-.L55
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('VssStrLen')
	.sect	'.debug_loc'
.L78:
	.word	-1,.L79,0,.L224-.L79
	.half	2
	.byte	138,0
	.word	0,0
.L225:
	.word	-1,.L79,0,.L224-.L79
	.half	1
	.byte	100
	.word	0,0
.L226:
	.word	-1,.L79,.L52-.L79,.L224-.L79
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_and')
	.sect	'.debug_loc'
.L208:
	.word	-1,.L73,0,.L206-.L73
	.half	1
	.byte	101
	.word	0,0
.L209:
	.word	-1,.L73,0,.L206-.L73
	.half	1
	.byte	102
	.word	0,0
.L211:
	.word	-1,.L73,.L45-.L73,.L206-.L73
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L210:
	.word	-1,.L73,0,.L206-.L73
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L72:
	.word	-1,.L73,0,.L206-.L73
	.half	2
	.byte	138,0
	.word	0,0
.L207:
	.word	-1,.L73,0,.L206-.L73
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_cmp')
	.sect	'.debug_loc'
.L185:
	.word	-1,.L65,0,.L184-.L65
	.half	1
	.byte	100
	.word	0,0
.L186:
	.word	-1,.L65,0,.L184-.L65
	.half	1
	.byte	101
	.word	0,0
.L188:
	.word	-1,.L65,.L234-.L65,.L18-.L65
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L187:
	.word	-1,.L65,0,.L184-.L65
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L64:
	.word	-1,.L65,0,.L184-.L65
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_cmp2')
	.sect	'.debug_loc'
.L195:
	.word	-1,.L69,0,.L194-.L69
	.half	1
	.byte	100
	.word	0,0
.L197:
	.word	-1,.L69,0,.L194-.L69
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L199:
	.word	-1,.L69,.L236-.L69,.L237-.L69
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L198:
	.word	-1,.L69,0,.L194-.L69
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L68:
	.word	-1,.L69,0,.L194-.L69
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_cmp8')
	.sect	'.debug_loc'
.L190:
	.word	-1,.L67,0,.L189-.L67
	.half	1
	.byte	100
	.word	0,0
.L191:
	.word	-1,.L67,0,.L189-.L67
	.half	1
	.byte	101
	.word	0,0
.L193:
	.word	-1,.L67,.L235-.L67,.L26-.L67
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L192:
	.word	-1,.L67,0,.L189-.L67
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L66:
	.word	-1,.L67,0,.L189-.L67
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_cpy')
	.sect	'.debug_loc'
.L174:
	.word	-1,.L61,0,.L171-.L61
	.half	1
	.byte	101
	.word	0,0
.L176:
	.word	-1,.L61,.L232-.L61,.L171-.L61
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L175:
	.word	-1,.L61,0,.L171-.L61
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L60:
	.word	-1,.L61,0,.L171-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L172:
	.word	-1,.L61,0,.L171-.L61
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_cpy8')
	.sect	'.debug_loc'
.L180:
	.word	-1,.L63,0,.L177-.L63
	.half	1
	.byte	101
	.word	0,0
.L182:
	.word	-1,.L63,.L233-.L63,.L177-.L63
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L181:
	.word	-1,.L63,0,.L177-.L63
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L62:
	.word	-1,.L63,0,.L177-.L63
	.half	2
	.byte	138,0
	.word	0,0
.L178:
	.word	-1,.L63,0,.L177-.L63
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_or')
	.sect	'.debug_loc'
.L214:
	.word	-1,.L75,0,.L212-.L75
	.half	1
	.byte	101
	.word	0,0
.L215:
	.word	-1,.L75,0,.L212-.L75
	.half	1
	.byte	102
	.word	0,0
.L217:
	.word	-1,.L75,.L47-.L75,.L212-.L75
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L216:
	.word	-1,.L75,0,.L212-.L75
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L74:
	.word	-1,.L75,0,.L212-.L75
	.half	2
	.byte	138,0
	.word	0,0
.L213:
	.word	-1,.L75,0,.L212-.L75
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_set')
	.sect	'.debug_loc'
.L161:
	.word	-1,.L57,0,.L157-.L57
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L163:
	.word	-1,.L57,.L230-.L57,.L157-.L57
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L162:
	.word	-1,.L57,0,.L157-.L57
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L56:
	.word	-1,.L57,0,.L157-.L57
	.half	2
	.byte	138,0
	.word	0,0
.L159:
	.word	-1,.L57,0,.L157-.L57
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_set8')
	.sect	'.debug_loc'
.L168:
	.word	-1,.L59,0,.L164-.L59
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L170:
	.word	-1,.L59,.L231-.L59,.L164-.L59
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L169:
	.word	-1,.L59,0,.L164-.L59
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L58:
	.word	-1,.L59,0,.L164-.L59
	.half	2
	.byte	138,0
	.word	0,0
.L166:
	.word	-1,.L59,0,.L164-.L59
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_swap')
	.sect	'.debug_loc'
.L201:
	.word	-1,.L71,0,.L200-.L71
	.half	1
	.byte	100
	.word	0,0
.L202:
	.word	-1,.L71,0,.L200-.L71
	.half	1
	.byte	101
	.word	0,0
.L204:
	.word	-1,.L71,.L43-.L71,.L200-.L71
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L203:
	.word	-1,.L71,0,.L200-.L71
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L70:
	.word	-1,.L71,0,.L200-.L71
	.half	2
	.byte	138,0
	.word	0,0
.L205:
	.word	-1,.L71,.L238-.L71,.L42-.L71
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('mem_xor')
	.sect	'.debug_loc'
.L220:
	.word	-1,.L77,0,.L218-.L77
	.half	1
	.byte	101
	.word	0,0
.L221:
	.word	-1,.L77,0,.L218-.L77
	.half	1
	.byte	102
	.word	0,0
.L223:
	.word	-1,.L77,.L49-.L77,.L218-.L77
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L222:
	.word	-1,.L77,0,.L218-.L77
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L76:
	.word	-1,.L77,0,.L218-.L77
	.half	2
	.byte	138,0
	.word	0,0
.L219:
	.word	-1,.L77,0,.L218-.L77
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L348:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('VssA2I')
	.sect	'.debug_frame'
	.word	24
	.word	.L348,.L55,.L150-.L55
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('mem_set')
	.sect	'.debug_frame'
	.word	24
	.word	.L348,.L57,.L157-.L57
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('mem_set8')
	.sect	'.debug_frame'
	.word	24
	.word	.L348,.L59,.L164-.L59
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('mem_cpy')
	.sect	'.debug_frame'
	.word	20
	.word	.L348,.L61,.L171-.L61
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('mem_cpy8')
	.sect	'.debug_frame'
	.word	20
	.word	.L348,.L63,.L177-.L63
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('mem_cmp')
	.sect	'.debug_frame'
	.word	20
	.word	.L348,.L65,.L184-.L65
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('mem_cmp8')
	.sect	'.debug_frame'
	.word	20
	.word	.L348,.L67,.L189-.L67
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('mem_cmp2')
	.sect	'.debug_frame'
	.word	24
	.word	.L348,.L69,.L194-.L69
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('mem_swap')
	.sect	'.debug_frame'
	.word	20
	.word	.L348,.L71,.L200-.L71
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('mem_and')
	.sect	'.debug_frame'
	.word	20
	.word	.L348,.L73,.L206-.L73
	.byte	8,18,8,19,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('mem_or')
	.sect	'.debug_frame'
	.word	20
	.word	.L348,.L75,.L212-.L75
	.byte	8,18,8,19,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('mem_xor')
	.sect	'.debug_frame'
	.word	20
	.word	.L348,.L77,.L218-.L77
	.byte	8,18,8,19,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('VssStrLen')
	.sect	'.debug_frame'
	.word	24
	.word	.L348,.L79,.L224-.L79
	.byte	8,18,8,19,8,21,8,22,8,23,0,0

; ..\vss_code\vsscommon.c	   193  
; ..\vss_code\vsscommon.c	   194  
; ..\vss_code\vsscommon.c	   195  #pragma section code restore
; ..\vss_code\vsscommon.c	   196  
; ..\vss_code\vsscommon.c	   197  

	; Module end
