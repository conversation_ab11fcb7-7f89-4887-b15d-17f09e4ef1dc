	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc23812a -c99 --dep-file=mcal_src\\.Os.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Os.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Os.src ..\\mcal_src\\Os.c"
	.compiler_name		"ctc"
	.name	"Os"

	
$TC16X
	
	.sdecl	'.text.Os.OSEKMP_UserEnableAllInterrupts',code,cluster('OSEKMP_UserEnableAllInterrupts')
	.sect	'.text.Os.OSEKMP_UserEnableAllInterrupts'
	.align	2
	
	.global	OSEKMP_UserEnableAllInterrupts

; ..\mcal_src\Os.c	     1  /******************************************************************************
; ..\mcal_src\Os.c	     2  **                                                                           **
; ..\mcal_src\Os.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_src\Os.c	     4  **                                                                           **
; ..\mcal_src\Os.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Os.c	     6  **                                                                           **
; ..\mcal_src\Os.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Os.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Os.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Os.c	    10  **                                                                           **
; ..\mcal_src\Os.c	    11  *******************************************************************************
; ..\mcal_src\Os.c	    12  **                                                                           **
; ..\mcal_src\Os.c	    13  **  $FILENAME   : Os.c $                                                     **
; ..\mcal_src\Os.c	    14  **                                                                           **
; ..\mcal_src\Os.c	    15  **  $CC VERSION : \main\14 $                                                 **
; ..\mcal_src\Os.c	    16  **                                                                           **
; ..\mcal_src\Os.c	    17  **  $DATE       : 2016-03-24 $                                               **
; ..\mcal_src\Os.c	    18  **                                                                           **
; ..\mcal_src\Os.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Os.c	    20  **                                                                           **
; ..\mcal_src\Os.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Os.c	    22  **                                                                           **
; ..\mcal_src\Os.c	    23  **  DESCRIPTION : This file contains                                         **
; ..\mcal_src\Os.c	    24  **                - stub for OS functionality.                               **
; ..\mcal_src\Os.c	    25  **                                                                           **
; ..\mcal_src\Os.c	    26  **  MAY BE CHANGED BY USER [yes/no]: Yes                                     **
; ..\mcal_src\Os.c	    27  **                                                                           **
; ..\mcal_src\Os.c	    28  ******************************************************************************/
; ..\mcal_src\Os.c	    29  /*******************************************************************************
; ..\mcal_src\Os.c	    30  **                      Includes                                              **
; ..\mcal_src\Os.c	    31  *******************************************************************************/
; ..\mcal_src\Os.c	    32  
; ..\mcal_src\Os.c	    33  /* Inclusion of Platform_Types.h and Compiler.h */
; ..\mcal_src\Os.c	    34  #include "Std_Types.h"
; ..\mcal_src\Os.c	    35  
; ..\mcal_src\Os.c	    36  /* Inclusion of Tasking sfr file */
; ..\mcal_src\Os.c	    37  #include "IfxCpu_reg.h"
; ..\mcal_src\Os.c	    38  
; ..\mcal_src\Os.c	    39  /* Include Mcal for Library */
; ..\mcal_src\Os.c	    40  #include "Mcal.h"
; ..\mcal_src\Os.c	    41  
; ..\mcal_src\Os.c	    42  /* Include Os.h*/
; ..\mcal_src\Os.c	    43  #include "Os.h"
; ..\mcal_src\Os.c	    44  /* Include Mcal_DmaLib */
; ..\mcal_src\Os.c	    45  #include "Mcal_DmaLib.h"
; ..\mcal_src\Os.c	    46  /*******************************************************************************
; ..\mcal_src\Os.c	    47  **                      Imported Compiler Switch Checks                       **
; ..\mcal_src\Os.c	    48  *******************************************************************************/
; ..\mcal_src\Os.c	    49  
; ..\mcal_src\Os.c	    50  /*******************************************************************************
; ..\mcal_src\Os.c	    51  **                      Private Macro Definitions                             **
; ..\mcal_src\Os.c	    52  *******************************************************************************/
; ..\mcal_src\Os.c	    53  
; ..\mcal_src\Os.c	    54  /*******************************************************************************
; ..\mcal_src\Os.c	    55  **                      Private Type Definitions                              **
; ..\mcal_src\Os.c	    56  *******************************************************************************/
; ..\mcal_src\Os.c	    57  
; ..\mcal_src\Os.c	    58  
; ..\mcal_src\Os.c	    59  /*******************************************************************************
; ..\mcal_src\Os.c	    60  **                      Private Function Declarations                         **
; ..\mcal_src\Os.c	    61  *******************************************************************************/
; ..\mcal_src\Os.c	    62  
; ..\mcal_src\Os.c	    63  
; ..\mcal_src\Os.c	    64  /*******************************************************************************
; ..\mcal_src\Os.c	    65  **                      Global Constant Definitions                           **
; ..\mcal_src\Os.c	    66  *******************************************************************************/
; ..\mcal_src\Os.c	    67  
; ..\mcal_src\Os.c	    68  
; ..\mcal_src\Os.c	    69  /*******************************************************************************
; ..\mcal_src\Os.c	    70  **                      Global Variable Definitions                           **
; ..\mcal_src\Os.c	    71  *******************************************************************************/
; ..\mcal_src\Os.c	    72  
; ..\mcal_src\Os.c	    73  
; ..\mcal_src\Os.c	    74  
; ..\mcal_src\Os.c	    75  /*******************************************************************************
; ..\mcal_src\Os.c	    76  **                      Private Constant Definitions                          **
; ..\mcal_src\Os.c	    77  *******************************************************************************/
; ..\mcal_src\Os.c	    78  
; ..\mcal_src\Os.c	    79  
; ..\mcal_src\Os.c	    80  /*******************************************************************************
; ..\mcal_src\Os.c	    81  **                      Private Variable Definitions                          **
; ..\mcal_src\Os.c	    82  *******************************************************************************/
; ..\mcal_src\Os.c	    83  
; ..\mcal_src\Os.c	    84  static volatile uint8 Os_IntSaveDisableCounter[3];
; ..\mcal_src\Os.c	    85  static volatile sint32 Os_SavedIntLevelNested[3];
; ..\mcal_src\Os.c	    86  
; ..\mcal_src\Os.c	    87  /*******************************************************************************
; ..\mcal_src\Os.c	    88  **                      Private Function Definitions                          **
; ..\mcal_src\Os.c	    89  *******************************************************************************/
; ..\mcal_src\Os.c	    90  
; ..\mcal_src\Os.c	    91  
; ..\mcal_src\Os.c	    92  /*******************************************************************************
; ..\mcal_src\Os.c	    93  **                      Global Function Definitions                           **
; ..\mcal_src\Os.c	    94  *******************************************************************************/
; ..\mcal_src\Os.c	    95  
; ..\mcal_src\Os.c	    96  
; ..\mcal_src\Os.c	    97  /*******************************************************************************
; ..\mcal_src\Os.c	    98  ** Syntax           : void OSEKMP_UserEnableAllInterrupts(void)               **
; ..\mcal_src\Os.c	    99  **                                                                            **
; ..\mcal_src\Os.c	   100  ** Service ID       :  none                                                   **
; ..\mcal_src\Os.c	   101  **                                                                            **
; ..\mcal_src\Os.c	   102  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Os.c	   103  **                                                                            **
; ..\mcal_src\Os.c	   104  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Os.c	   105  **                                                                            **
; ..\mcal_src\Os.c	   106  ** Parameters(in)   : none                                                    **
; ..\mcal_src\Os.c	   107  **                                                                            **
; ..\mcal_src\Os.c	   108  ** Parameters (out) : none                                                    **
; ..\mcal_src\Os.c	   109  **                                                                            **
; ..\mcal_src\Os.c	   110  ** Return value     : none                                                    **
; ..\mcal_src\Os.c	   111  **                                                                            **
; ..\mcal_src\Os.c	   112  ** Description      : This function Enables all interrupts                    **
; ..\mcal_src\Os.c	   113  **                                                                            **
; ..\mcal_src\Os.c	   114  *******************************************************************************/
; ..\mcal_src\Os.c	   115  void OSEKMP_UserEnableAllInterrupts(void)
; Function OSEKMP_UserEnableAllInterrupts
.L7:
OSEKMP_UserEnableAllInterrupts:	.type	func

; ..\mcal_src\Os.c	   116  {
; ..\mcal_src\Os.c	   117    ENABLE();
	enable
.L100:

; ..\mcal_src\Os.c	   118  }
	ret
.L63:
	
__OSEKMP_UserEnableAllInterrupts_function_end:
	.size	OSEKMP_UserEnableAllInterrupts,__OSEKMP_UserEnableAllInterrupts_function_end-OSEKMP_UserEnableAllInterrupts
.L28:
	; End of function
	
	.sdecl	'.text.Os.OSEKMP_UserDisableAllInterrupts',code,cluster('OSEKMP_UserDisableAllInterrupts')
	.sect	'.text.Os.OSEKMP_UserDisableAllInterrupts'
	.align	2
	
	.global	OSEKMP_UserDisableAllInterrupts

; ..\mcal_src\Os.c	   119  
; ..\mcal_src\Os.c	   120  /*******************************************************************************
; ..\mcal_src\Os.c	   121  ** Syntax           : void OSEKMP_UserDisableAllInterrupts(void)              **
; ..\mcal_src\Os.c	   122  **                                                                            **
; ..\mcal_src\Os.c	   123  ** Service ID       : none                                                    **
; ..\mcal_src\Os.c	   124  **                                                                            **
; ..\mcal_src\Os.c	   125  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Os.c	   126  **                                                                            **
; ..\mcal_src\Os.c	   127  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Os.c	   128  **                                                                            **
; ..\mcal_src\Os.c	   129  ** Parameters(in)   : none                                                    **
; ..\mcal_src\Os.c	   130  **                                                                            **
; ..\mcal_src\Os.c	   131  ** Parameters (out) : none                                                    **
; ..\mcal_src\Os.c	   132  **                                                                            **
; ..\mcal_src\Os.c	   133  ** Return value     : none                                                    **
; ..\mcal_src\Os.c	   134  **                                                                            **
; ..\mcal_src\Os.c	   135  ** Description      : This function Disables all interrupts                   **
; ..\mcal_src\Os.c	   136  **                                                                            **
; ..\mcal_src\Os.c	   137  *******************************************************************************/
; ..\mcal_src\Os.c	   138  void OSEKMP_UserDisableAllInterrupts(void)
; Function OSEKMP_UserDisableAllInterrupts
.L9:
OSEKMP_UserDisableAllInterrupts:	.type	func

; ..\mcal_src\Os.c	   139  {
; ..\mcal_src\Os.c	   140  
; ..\mcal_src\Os.c	   141    DISABLE();
	disable
.L105:

; ..\mcal_src\Os.c	   142  
; ..\mcal_src\Os.c	   143  }
	ret
.L64:
	
__OSEKMP_UserDisableAllInterrupts_function_end:
	.size	OSEKMP_UserDisableAllInterrupts,__OSEKMP_UserDisableAllInterrupts_function_end-OSEKMP_UserDisableAllInterrupts
.L33:
	; End of function
	
	.sdecl	'.text.Os.OSEKMP_UserSuspendAllInterrupts',code,cluster('OSEKMP_UserSuspendAllInterrupts')
	.sect	'.text.Os.OSEKMP_UserSuspendAllInterrupts'
	.align	2
	
	.global	OSEKMP_UserSuspendAllInterrupts

; ..\mcal_src\Os.c	   144  
; ..\mcal_src\Os.c	   145  /******************************************************************************/
; ..\mcal_src\Os.c	   146  
; ..\mcal_src\Os.c	   147  
; ..\mcal_src\Os.c	   148  /*******************************************************************************
; ..\mcal_src\Os.c	   149  ** Syntax           : void OSEKMP_UserSuspendAllInterrupts(void)              **
; ..\mcal_src\Os.c	   150  **                                                                            **
; ..\mcal_src\Os.c	   151  ** Service ID       :  none                                                   **
; ..\mcal_src\Os.c	   152  **                                                                            **
; ..\mcal_src\Os.c	   153  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Os.c	   154  **                                                                            **
; ..\mcal_src\Os.c	   155  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Os.c	   156  **                                                                            **
; ..\mcal_src\Os.c	   157  ** Parameters(in)   : none                                                    **
; ..\mcal_src\Os.c	   158  **                                                                            **
; ..\mcal_src\Os.c	   159  ** Parameters (out) : none                                                    **
; ..\mcal_src\Os.c	   160  **                                                                            **
; ..\mcal_src\Os.c	   161  ** Return value     : none                                                    **
; ..\mcal_src\Os.c	   162  **                                                                            **
; ..\mcal_src\Os.c	   163  ** Description      : This function Suspends all interrupts, nested           **
; ..\mcal_src\Os.c	   164  **                                                                            **
; ..\mcal_src\Os.c	   165  *******************************************************************************/
; ..\mcal_src\Os.c	   166  void OSEKMP_UserSuspendAllInterrupts(void)
; Function OSEKMP_UserSuspendAllInterrupts
.L11:
OSEKMP_UserSuspendAllInterrupts:	.type	func

; ..\mcal_src\Os.c	   167  {
; ..\mcal_src\Os.c	   168    uint8 CoreId;
; ..\mcal_src\Os.c	   169    
; ..\mcal_src\Os.c	   170    /* Get the current core id */
; ..\mcal_src\Os.c	   171    CoreId = Mcal_GetCoreId();
	call	Mcal_GetCoreId
.L88:

; ..\mcal_src\Os.c	   172  
; ..\mcal_src\Os.c	   173    if(Os_IntSaveDisableCounter[CoreId] == 0)
	fcall	.cocofun_1
.L110:
	jne	d15,#0,.L2
.L111:

; ..\mcal_src\Os.c	   174    {
; ..\mcal_src\Os.c	   175      
; ..\mcal_src\Os.c	   176      ISYNC();
	isync
.L112:

; ..\mcal_src\Os.c	   177      Os_SavedIntLevelNested[CoreId] = MFCR(CPU_ICR);/* disable interrupts */
	movh.a	a2,#@his(Os_SavedIntLevelNested)
	lea	a2,[a2]@los(Os_SavedIntLevelNested)
.L113:
	addsc.a	a2,a2,d2,#2
.L114:
	mfcr	d15,#65068
.L115:
	st.w	[a2],d15
.L116:

; ..\mcal_src\Os.c	   178      DISABLE();
	disable
.L2:

; ..\mcal_src\Os.c	   179    }
; ..\mcal_src\Os.c	   180    Os_IntSaveDisableCounter[CoreId]++;
	ld.bu	d15,[a15]
.L117:
	add	d15,#1
	st.b	[a15],d15
.L118:

; ..\mcal_src\Os.c	   181  }
	ret
.L65:
	
__OSEKMP_UserSuspendAllInterrupts_function_end:
	.size	OSEKMP_UserSuspendAllInterrupts,__OSEKMP_UserSuspendAllInterrupts_function_end-OSEKMP_UserSuspendAllInterrupts
.L38:
	; End of function
	
	.sdecl	'.text.Os..cocofun_1',code,cluster('.cocofun_1')
	.sect	'.text.Os..cocofun_1'
	.align	2
; Function .cocofun_1
.L13:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh.a	a15,#@his(Os_IntSaveDisableCounter)
	lea	a15,[a15]@los(Os_IntSaveDisableCounter)
.L143:
	addsc.a	a15,a15,d2,#0
	ld.bu	d15,[a15]
.L144:
	fret
.L58:
	; End of function
	.sdecl	'.text.Os.OSEKMP_UserResumeAllInterrupts',code,cluster('OSEKMP_UserResumeAllInterrupts')
	.sect	'.text.Os.OSEKMP_UserResumeAllInterrupts'
	.align	2
	
	.global	OSEKMP_UserResumeAllInterrupts

; ..\mcal_src\Os.c	   182  /******************************************************************************/
; ..\mcal_src\Os.c	   183  
; ..\mcal_src\Os.c	   184  
; ..\mcal_src\Os.c	   185  /*******************************************************************************
; ..\mcal_src\Os.c	   186  ** Syntax           : void OSEKMP_UserResumeAllInterrupts(void)               **
; ..\mcal_src\Os.c	   187  **                                                                            **
; ..\mcal_src\Os.c	   188  ** Service ID       :  none                                                   **
; ..\mcal_src\Os.c	   189  **                                                                            **
; ..\mcal_src\Os.c	   190  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Os.c	   191  **                                                                            **
; ..\mcal_src\Os.c	   192  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Os.c	   193  **                                                                            **
; ..\mcal_src\Os.c	   194  ** Parameters(in)   : none                                                    **
; ..\mcal_src\Os.c	   195  **                                                                            **
; ..\mcal_src\Os.c	   196  ** Parameters (out) : none                                                    **
; ..\mcal_src\Os.c	   197  **                                                                            **
; ..\mcal_src\Os.c	   198  ** Return value     : none                                                    **
; ..\mcal_src\Os.c	   199  **                                                                            **
; ..\mcal_src\Os.c	   200  ** Description      : This function Resumes all interrupts, nested            **
; ..\mcal_src\Os.c	   201  **                                                                **
; ..\mcal_src\Os.c	   202  *******************************************************************************/
; ..\mcal_src\Os.c	   203  void OSEKMP_UserResumeAllInterrupts(void)
; Function OSEKMP_UserResumeAllInterrupts
.L15:
OSEKMP_UserResumeAllInterrupts:	.type	func

; ..\mcal_src\Os.c	   204  {
; ..\mcal_src\Os.c	   205    uint8 CoreId;
; ..\mcal_src\Os.c	   206    
; ..\mcal_src\Os.c	   207    /* Get the current core id */
; ..\mcal_src\Os.c	   208    CoreId = Mcal_GetCoreId();
	call	Mcal_GetCoreId
.L89:

; ..\mcal_src\Os.c	   209    
; ..\mcal_src\Os.c	   210    if (Os_IntSaveDisableCounter[CoreId] > 0)
	fcall	.cocofun_1
.L123:
	jeq	d15,#0,.L3
.L124:

; ..\mcal_src\Os.c	   211    {
; ..\mcal_src\Os.c	   212      Os_IntSaveDisableCounter[CoreId]--;
	ld.bu	d15,[a15]
.L125:
	add	d15,#-1
	st.b	[a15],d15
.L3:

; ..\mcal_src\Os.c	   213    }
; ..\mcal_src\Os.c	   214    
; ..\mcal_src\Os.c	   215    if (Os_IntSaveDisableCounter[CoreId] == 0)
	ld.bu	d15,[a15]
.L126:
	jne	d15,#0,.L4
.L127:

; ..\mcal_src\Os.c	   216    {
; ..\mcal_src\Os.c	   217      if (Os_SavedIntLevelNested[CoreId] & (0x00000001U << 15))
	movh.a	a15,#@his(Os_SavedIntLevelNested)
	lea	a15,[a15]@los(Os_SavedIntLevelNested)
.L128:
	addsc.a	a15,a15,d2,#2
	ld.w	d15,[a15]
.L129:
	jz.t	d15:15,.L5
.L130:

; ..\mcal_src\Os.c	   218      {
; ..\mcal_src\Os.c	   219        /* interrupts were enabled, enable again */
; ..\mcal_src\Os.c	   220        ENABLE();
	enable
.L5:
.L4:

; ..\mcal_src\Os.c	   221      }
; ..\mcal_src\Os.c	   222    }
; ..\mcal_src\Os.c	   223  }
	ret
.L69:
	
__OSEKMP_UserResumeAllInterrupts_function_end:
	.size	OSEKMP_UserResumeAllInterrupts,__OSEKMP_UserResumeAllInterrupts_function_end-OSEKMP_UserResumeAllInterrupts
.L43:
	; End of function
	
	.sdecl	'.text.Os.Os_GetCurrentStackArea',code,cluster('Os_GetCurrentStackArea')
	.sect	'.text.Os.Os_GetCurrentStackArea'
	.align	2
	
	.global	Os_GetCurrentStackArea

; ..\mcal_src\Os.c	   224  /******************************************************************************/
; ..\mcal_src\Os.c	   225  
; ..\mcal_src\Os.c	   226  /*******************************************************************************
; ..\mcal_src\Os.c	   227  ** Syntax           : void Os_GetCurrentStackArea(void  **start, void **end)  **
; ..\mcal_src\Os.c	   228  **                                                                            **
; ..\mcal_src\Os.c	   229  ** Service ID       : none                                                    **
; ..\mcal_src\Os.c	   230  **                                                                            **
; ..\mcal_src\Os.c	   231  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Os.c	   232  **                                                                            **
; ..\mcal_src\Os.c	   233  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Os.c	   234  **                                                                            **
; ..\mcal_src\Os.c	   235  ** Parameters(in)   : none                                                    **
; ..\mcal_src\Os.c	   236  **                                                                            **
; ..\mcal_src\Os.c	   237  ** Parameters (out) : start: Start address of Stack, end: Stack End address   **
; ..\mcal_src\Os.c	   238  **                                                                            **
; ..\mcal_src\Os.c	   239  ** Return value     : none                                                    **
; ..\mcal_src\Os.c	   240  **                                                                            **
; ..\mcal_src\Os.c	   241  ** Description      : This function provides the stack's range                **
; ..\mcal_src\Os.c	   242  **                                                                **
; ..\mcal_src\Os.c	   243  *******************************************************************************/
; ..\mcal_src\Os.c	   244  void Os_GetCurrentStackArea(void  **start, void **end)
; Function Os_GetCurrentStackArea
.L17:
Os_GetCurrentStackArea:	.type	func

; ..\mcal_src\Os.c	   245  {
; ..\mcal_src\Os.c	   246    /* Temporarily dummy */
; ..\mcal_src\Os.c	   247    /* this line is provided to remove unused param warning in GNU */
; ..\mcal_src\Os.c	   248    UNUSED_PARAMETER(start)
; ..\mcal_src\Os.c	   249    /* this line is provided to remove unused param warning in GNU */
; ..\mcal_src\Os.c	   250    UNUSED_PARAMETER(end)
; ..\mcal_src\Os.c	   251  
; ..\mcal_src\Os.c	   252  }
	ret
.L71:
	
__Os_GetCurrentStackArea_function_end:
	.size	Os_GetCurrentStackArea,__Os_GetCurrentStackArea_function_end-Os_GetCurrentStackArea
.L48:
	; End of function
	
	.sdecl	'.text.Os.Os_UpdateRegSV',code,cluster('Os_UpdateRegSV')
	.sect	'.text.Os.Os_UpdateRegSV'
	.align	2
	
	.global	Os_UpdateRegSV

; ..\mcal_src\Os.c	   253  /*******************************************************************************
; ..\mcal_src\Os.c	   254  ** Syntax           : void Os_UpdateRegSV(volatileuint32 * Address, uint32    **
; ..\mcal_src\Os.c	   255  **               SetMask, uint32 ClearMask, uint8 InitProtectionType,         **
; ..\mcal_src\Os.c	   256  **                        uint16 Area)                                        **
; ..\mcal_src\Os.c	   257  **                                                                            **
; ..\mcal_src\Os.c	   258  ** Service ID       : none                                                    **
; ..\mcal_src\Os.c	   259  **                                                                            **
; ..\mcal_src\Os.c	   260  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Os.c	   261  **                                                                            **
; ..\mcal_src\Os.c	   262  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Os.c	   263  **                                                                            **
; ..\mcal_src\Os.c	   264  ** Parameters (in) :  Address - Address of the SFR to be modified             **
; ..\mcal_src\Os.c	   265  ** SetMask - Set mask to be ored with the value                               **
; ..\mcal_src\Os.c	   266  ** ClearMask - Clear mask to be anded with the value                          **
; ..\mcal_src\Os.c	   267  ** InitProtectionType - Type of the protection required for SFR update.       **
; ..\mcal_src\Os.c	   268  **                      MCAL_NO_ENDINIT_PROTECTION (1U)                       **
; ..\mcal_src\Os.c	   269  **                      MCAL_CPU_ENDINIT_PROTECTION  (2U)                     **
; ..\mcal_src\Os.c	   270  **                      MCAL_SAFETY_ENDINIT_PROTECTION  (3U)                  **
; ..\mcal_src\Os.c	   271  ** Area - To be used by OS                                                    **
; ..\mcal_src\Os.c	   272  ** Parameters(out)   : Value referenced by Address will be modified           **
; ..\mcal_src\Os.c	   273  **                                                                            **
; ..\mcal_src\Os.c	   274  ** Return value     : none                                                    **
; ..\mcal_src\Os.c	   275  **                                                                            **
; ..\mcal_src\Os.c	   276  ** Description      : This function provides the OS interface                 **
; ..\mcal_src\Os.c	   277  **                                                                            **
; ..\mcal_src\Os.c	   278  *******************************************************************************/
; ..\mcal_src\Os.c	   279  void Os_UpdateRegSV (volatile uint32 *Address,uint32 SetMask,uint32 ClearMask,\ 
; Function Os_UpdateRegSV
.L19:
Os_UpdateRegSV:	.type	func

; ..\mcal_src\Os.c	   280                              uint8 InitProtectionType,uint16 Area)
; ..\mcal_src\Os.c	   281  {
; ..\mcal_src\Os.c	   282    MCAL_REG_MODIFY32(*Address,~ClearMask,SetMask )
	mov	d0,#-1
	ld.w	d15,[a4]
	xor	d5,d0
.L90:
	and	d15,d5
.L91:
	or	d15,d4
	st.w	[a4],d15
.L84:

; ..\mcal_src\Os.c	   283    UNUSED_PARAMETER(Area)
; ..\mcal_src\Os.c	   284    UNUSED_PARAMETER(InitProtectionType)
; ..\mcal_src\Os.c	   285  }
	ret
.L75:
	
__Os_UpdateRegSV_function_end:
	.size	Os_UpdateRegSV,__Os_UpdateRegSV_function_end-Os_UpdateRegSV
.L53:
	; End of function
	
	.sdecl	'.bss.Os.Os_IntSaveDisableCounter',data,cluster('Os_IntSaveDisableCounter')
	.sect	'.bss.Os.Os_IntSaveDisableCounter'
Os_IntSaveDisableCounter:	.type	object
	.size	Os_IntSaveDisableCounter,3
	.space	3
	.sdecl	'.bss.Os.Os_SavedIntLevelNested',data,cluster('Os_SavedIntLevelNested')
	.sect	'.bss.Os.Os_SavedIntLevelNested'
	.align	4
Os_SavedIntLevelNested:	.type	object
	.size	Os_SavedIntLevelNested,12
	.space	12
	.calls	'OSEKMP_UserSuspendAllInterrupts','Mcal_GetCoreId'
	.calls	'OSEKMP_UserResumeAllInterrupts','Mcal_GetCoreId'
	.calls	'OSEKMP_UserSuspendAllInterrupts','.cocofun_1'
	.calls	'OSEKMP_UserResumeAllInterrupts','.cocofun_1'
	.calls	'OSEKMP_UserEnableAllInterrupts','',0
	.calls	'OSEKMP_UserDisableAllInterrupts','',0
	.calls	'OSEKMP_UserSuspendAllInterrupts','',0
	.calls	'.cocofun_1','',0
	.calls	'OSEKMP_UserResumeAllInterrupts','',0
	.calls	'Os_GetCurrentStackArea','',0
	.extern	Mcal_GetCoreId
	.calls	'Os_UpdateRegSV','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L21:
	.word	56705
	.half	3
	.word	.L22
	.byte	4
.L20:
	.byte	1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L23
	.byte	2
	.byte	'int',0,4,5,3
	.word	171
	.byte	4
	.byte	'__mfcr',0
	.word	178
	.byte	1,1,1,1,5
	.word	171
	.byte	0,6
	.byte	'__isync',0,1,1,1,1,6
	.byte	'__disable',0,1,1,1,1,6
	.byte	'__enable',0,1,1,1,1
.L67:
	.byte	2
	.byte	'unsigned char',0,1,8,7
	.byte	'Mcal_GetCoreId',0,1,145,2,14
	.word	247
	.byte	1,1,1,1,8
	.byte	'void',0,9
	.word	292
.L72:
	.byte	9
	.word	298
.L78:
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.word	308
.L76:
	.byte	9
	.word	329
.L82:
	.byte	2
	.byte	'unsigned short int',0,2,7,10
	.byte	'__prof_adm',0,2,1,1
	.word	298
	.byte	11,1,9
	.word	380
	.byte	10
	.byte	'__codeptr',0,2,1,1
	.word	382
	.byte	10
	.byte	'uint8',0,3,90,29
	.word	247
	.byte	10
	.byte	'uint16',0,3,92,29
	.word	339
	.byte	2
	.byte	'long int',0,4,5,10
	.byte	'sint32',0,3,93,29
	.word	434
	.byte	10
	.byte	'uint32',0,3,94,29
	.word	308
	.byte	12
	.byte	'_Ifx_CPU_A_Bits',0,4,45,16,4,2
	.byte	'unsigned int',0,4,7,13
	.byte	'ADDR',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_A_Bits',0,4,48,3
	.word	476
	.byte	12
	.byte	'_Ifx_CPU_BIV_Bits',0,4,51,16,4,13
	.byte	'VSS',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'BIV',0,4
	.word	497
	.byte	31,0,2,35,0,0,10
	.byte	'Ifx_CPU_BIV_Bits',0,4,55,3
	.word	553
	.byte	12
	.byte	'_Ifx_CPU_BTV_Bits',0,4,58,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'BTV',0,4
	.word	497
	.byte	31,0,2,35,0,0,10
	.byte	'Ifx_CPU_BTV_Bits',0,4,62,3
	.word	632
	.byte	12
	.byte	'_Ifx_CPU_CCNT_Bits',0,4,65,16,4,13
	.byte	'CountValue',0,4
	.word	497
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	497
	.byte	1,0,2,35,0,0,10
	.byte	'Ifx_CPU_CCNT_Bits',0,4,69,3
	.word	718
	.byte	12
	.byte	'_Ifx_CPU_CCTRL_Bits',0,4,72,16,4,13
	.byte	'CM',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'CE',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'M1',0,4
	.word	497
	.byte	3,27,2,35,0,13
	.byte	'M2',0,4
	.word	497
	.byte	3,24,2,35,0,13
	.byte	'M3',0,4
	.word	497
	.byte	3,21,2,35,0,13
	.byte	'reserved_11',0,4
	.word	497
	.byte	21,0,2,35,0,0,10
	.byte	'Ifx_CPU_CCTRL_Bits',0,4,80,3
	.word	807
	.byte	12
	.byte	'_Ifx_CPU_COMPAT_Bits',0,4,83,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'RM',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'SP',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	497
	.byte	27,0,2,35,0,0,10
	.byte	'Ifx_CPU_COMPAT_Bits',0,4,89,3
	.word	953
	.byte	12
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,4,92,16,4,13
	.byte	'CORE_ID',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	497
	.byte	29,0,2,35,0,0,10
	.byte	'Ifx_CPU_CORE_ID_Bits',0,4,96,3
	.word	1080
	.byte	12
	.byte	'_Ifx_CPU_CPR_L_Bits',0,4,99,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'LOWBND',0,4
	.word	497
	.byte	29,0,2,35,0,0,10
	.byte	'Ifx_CPU_CPR_L_Bits',0,4,103,3
	.word	1178
	.byte	12
	.byte	'_Ifx_CPU_CPR_U_Bits',0,4,106,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'UPPBND',0,4
	.word	497
	.byte	29,0,2,35,0,0,10
	.byte	'Ifx_CPU_CPR_U_Bits',0,4,110,3
	.word	1271
	.byte	12
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,4,113,16,4,13
	.byte	'MODREV',0,4
	.word	497
	.byte	8,24,2,35,0,13
	.byte	'MOD_32B',0,4
	.word	497
	.byte	8,16,2,35,0,13
	.byte	'MOD',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_CPU_ID_Bits',0,4,118,3
	.word	1364
	.byte	12
	.byte	'_Ifx_CPU_CPXE_Bits',0,4,121,16,4,13
	.byte	'XE',0,4
	.word	497
	.byte	8,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,10
	.byte	'Ifx_CPU_CPXE_Bits',0,4,125,3
	.word	1471
	.byte	12
	.byte	'_Ifx_CPU_CREVT_Bits',0,4,128,1,16,4,13
	.byte	'EVTA',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'BBM',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'BOD',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'SUSP',0,4
	.word	497
	.byte	1,26,2,35,0,13
	.byte	'CNT',0,4
	.word	497
	.byte	2,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,10
	.byte	'Ifx_CPU_CREVT_Bits',0,4,136,1,3
	.word	1558
	.byte	12
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,4,139,1,16,4,13
	.byte	'CID',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	497
	.byte	29,0,2,35,0,0,10
	.byte	'Ifx_CPU_CUS_ID_Bits',0,4,143,1,3
	.word	1712
	.byte	12
	.byte	'_Ifx_CPU_D_Bits',0,4,146,1,16,4,13
	.byte	'DATA',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_D_Bits',0,4,149,1,3
	.word	1806
	.byte	12
	.byte	'_Ifx_CPU_DATR_Bits',0,4,152,1,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'SBE',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'reserved_4',0,4
	.word	497
	.byte	5,23,2,35,0,13
	.byte	'CWE',0,4
	.word	497
	.byte	1,22,2,35,0,13
	.byte	'CFE',0,4
	.word	497
	.byte	1,21,2,35,0,13
	.byte	'reserved_11',0,4
	.word	497
	.byte	3,18,2,35,0,13
	.byte	'SOE',0,4
	.word	497
	.byte	1,17,2,35,0,13
	.byte	'SME',0,4
	.word	497
	.byte	1,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_DATR_Bits',0,4,163,1,3
	.word	1869
	.byte	12
	.byte	'_Ifx_CPU_DBGSR_Bits',0,4,166,1,16,4,13
	.byte	'DE',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'HALT',0,4
	.word	497
	.byte	2,29,2,35,0,13
	.byte	'SIH',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'SUSP',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	497
	.byte	1,26,2,35,0,13
	.byte	'PREVSUSP',0,4
	.word	497
	.byte	1,25,2,35,0,13
	.byte	'PEVT',0,4
	.word	497
	.byte	1,24,2,35,0,13
	.byte	'EVTSRC',0,4
	.word	497
	.byte	5,19,2,35,0,13
	.byte	'reserved_13',0,4
	.word	497
	.byte	19,0,2,35,0,0,10
	.byte	'Ifx_CPU_DBGSR_Bits',0,4,177,1,3
	.word	2087
	.byte	12
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,4,180,1,16,4,13
	.byte	'DTA',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,10
	.byte	'Ifx_CPU_DBGTCR_Bits',0,4,184,1,3
	.word	2302
	.byte	12
	.byte	'_Ifx_CPU_DCON0_Bits',0,4,187,1,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'DCBYP',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,10
	.byte	'Ifx_CPU_DCON0_Bits',0,4,192,1,3
	.word	2396
	.byte	12
	.byte	'_Ifx_CPU_DCON2_Bits',0,4,195,1,16,4,13
	.byte	'DCACHE_SZE',0,4
	.word	497
	.byte	16,16,2,35,0,13
	.byte	'DSCRATCH_SZE',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_DCON2_Bits',0,4,199,1,3
	.word	2512
	.byte	12
	.byte	'_Ifx_CPU_DCX_Bits',0,4,202,1,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	6,26,2,35,0,13
	.byte	'DCXValue',0,4
	.word	497
	.byte	26,0,2,35,0,0,10
	.byte	'Ifx_CPU_DCX_Bits',0,4,206,1,3
	.word	2613
	.byte	12
	.byte	'_Ifx_CPU_DEADD_Bits',0,4,209,1,16,4,13
	.byte	'ERROR_ADDRESS',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_DEADD_Bits',0,4,212,1,3
	.word	2706
	.byte	12
	.byte	'_Ifx_CPU_DIEAR_Bits',0,4,215,1,16,4,13
	.byte	'TA',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_DIEAR_Bits',0,4,218,1,3
	.word	2786
	.byte	12
	.byte	'_Ifx_CPU_DIETR_Bits',0,4,221,1,16,4,13
	.byte	'IED',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'IE_T',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'IE_C',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'IE_S',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'IE_BI',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'E_INFO',0,4
	.word	497
	.byte	6,21,2,35,0,13
	.byte	'IE_DUAL',0,4
	.word	497
	.byte	1,20,2,35,0,13
	.byte	'IE_SP',0,4
	.word	497
	.byte	1,19,2,35,0,13
	.byte	'IE_BS',0,4
	.word	497
	.byte	1,18,2,35,0,13
	.byte	'reserved_14',0,4
	.word	497
	.byte	18,0,2,35,0,0,10
	.byte	'Ifx_CPU_DIETR_Bits',0,4,233,1,3
	.word	2855
	.byte	12
	.byte	'_Ifx_CPU_DMS_Bits',0,4,236,1,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'DMSValue',0,4
	.word	497
	.byte	31,0,2,35,0,0,10
	.byte	'Ifx_CPU_DMS_Bits',0,4,240,1,3
	.word	3084
	.byte	12
	.byte	'_Ifx_CPU_DPR_L_Bits',0,4,243,1,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'LOWBND',0,4
	.word	497
	.byte	29,0,2,35,0,0,10
	.byte	'Ifx_CPU_DPR_L_Bits',0,4,247,1,3
	.word	3177
	.byte	12
	.byte	'_Ifx_CPU_DPR_U_Bits',0,4,250,1,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'UPPBND',0,4
	.word	497
	.byte	29,0,2,35,0,0,10
	.byte	'Ifx_CPU_DPR_U_Bits',0,4,254,1,3
	.word	3272
	.byte	12
	.byte	'_Ifx_CPU_DPRE_Bits',0,4,129,2,16,4,13
	.byte	'RE',0,4
	.word	497
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_DPRE_Bits',0,4,133,2,3
	.word	3367
	.byte	12
	.byte	'_Ifx_CPU_DPWE_Bits',0,4,136,2,16,4,13
	.byte	'WE',0,4
	.word	497
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_DPWE_Bits',0,4,140,2,3
	.word	3457
	.byte	12
	.byte	'_Ifx_CPU_DSTR_Bits',0,4,143,2,16,4,13
	.byte	'SRE',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'GAE',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'LBE',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	497
	.byte	3,26,2,35,0,13
	.byte	'CRE',0,4
	.word	497
	.byte	1,25,2,35,0,13
	.byte	'reserved_7',0,4
	.word	497
	.byte	7,18,2,35,0,13
	.byte	'DTME',0,4
	.word	497
	.byte	1,17,2,35,0,13
	.byte	'LOE',0,4
	.word	497
	.byte	1,16,2,35,0,13
	.byte	'SDE',0,4
	.word	497
	.byte	1,15,2,35,0,13
	.byte	'SCE',0,4
	.word	497
	.byte	1,14,2,35,0,13
	.byte	'CAC',0,4
	.word	497
	.byte	1,13,2,35,0,13
	.byte	'MPE',0,4
	.word	497
	.byte	1,12,2,35,0,13
	.byte	'CLE',0,4
	.word	497
	.byte	1,11,2,35,0,13
	.byte	'reserved_21',0,4
	.word	497
	.byte	3,8,2,35,0,13
	.byte	'ALN',0,4
	.word	497
	.byte	1,7,2,35,0,13
	.byte	'reserved_25',0,4
	.word	497
	.byte	7,0,2,35,0,0,10
	.byte	'Ifx_CPU_DSTR_Bits',0,4,161,2,3
	.word	3547
	.byte	12
	.byte	'_Ifx_CPU_EXEVT_Bits',0,4,164,2,16,4,13
	.byte	'EVTA',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'BBM',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'BOD',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'SUSP',0,4
	.word	497
	.byte	1,26,2,35,0,13
	.byte	'CNT',0,4
	.word	497
	.byte	2,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,10
	.byte	'Ifx_CPU_EXEVT_Bits',0,4,172,2,3
	.word	3871
	.byte	12
	.byte	'_Ifx_CPU_FCX_Bits',0,4,175,2,16,4,13
	.byte	'FCXO',0,4
	.word	497
	.byte	16,16,2,35,0,13
	.byte	'FCXS',0,4
	.word	497
	.byte	4,12,2,35,0,13
	.byte	'reserved_20',0,4
	.word	497
	.byte	12,0,2,35,0,0,10
	.byte	'Ifx_CPU_FCX_Bits',0,4,180,2,3
	.word	4025
	.byte	12
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,4,183,2,16,4,13
	.byte	'TST',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'TCL',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	497
	.byte	6,24,2,35,0,13
	.byte	'RM',0,4
	.word	497
	.byte	2,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	497
	.byte	8,14,2,35,0,13
	.byte	'FXE',0,4
	.word	497
	.byte	1,13,2,35,0,13
	.byte	'FUE',0,4
	.word	497
	.byte	1,12,2,35,0,13
	.byte	'FZE',0,4
	.word	497
	.byte	1,11,2,35,0,13
	.byte	'FVE',0,4
	.word	497
	.byte	1,10,2,35,0,13
	.byte	'FIE',0,4
	.word	497
	.byte	1,9,2,35,0,13
	.byte	'reserved_23',0,4
	.word	497
	.byte	3,6,2,35,0,13
	.byte	'FX',0,4
	.word	497
	.byte	1,5,2,35,0,13
	.byte	'FU',0,4
	.word	497
	.byte	1,4,2,35,0,13
	.byte	'FZ',0,4
	.word	497
	.byte	1,3,2,35,0,13
	.byte	'FV',0,4
	.word	497
	.byte	1,2,2,35,0,13
	.byte	'FI',0,4
	.word	497
	.byte	1,1,2,35,0,13
	.byte	'reserved_31',0,4
	.word	497
	.byte	1,0,2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,4,202,2,3
	.word	4131
	.byte	12
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,4,205,2,16,4,13
	.byte	'OPC',0,4
	.word	497
	.byte	8,24,2,35,0,13
	.byte	'FMT',0,4
	.word	497
	.byte	1,23,2,35,0,13
	.byte	'reserved_9',0,4
	.word	497
	.byte	7,16,2,35,0,13
	.byte	'DREG',0,4
	.word	497
	.byte	4,12,2,35,0,13
	.byte	'reserved_20',0,4
	.word	497
	.byte	12,0,2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,4,212,2,3
	.word	4480
	.byte	12
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,4,215,2,16,4,13
	.byte	'PC',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,4,218,2,3
	.word	4640
	.byte	12
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,4,221,2,16,4,13
	.byte	'SRC1',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,4,224,2,3
	.word	4721
	.byte	12
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,4,227,2,16,4,13
	.byte	'SRC2',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,4,230,2,3
	.word	4808
	.byte	12
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,4,233,2,16,4,13
	.byte	'SRC3',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,4,236,2,3
	.word	4895
	.byte	12
	.byte	'_Ifx_CPU_ICNT_Bits',0,4,239,2,16,4,13
	.byte	'CountValue',0,4
	.word	497
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	497
	.byte	1,0,2,35,0,0,10
	.byte	'Ifx_CPU_ICNT_Bits',0,4,243,2,3
	.word	4982
	.byte	12
	.byte	'_Ifx_CPU_ICR_Bits',0,4,246,2,16,4,13
	.byte	'CCPN',0,4
	.word	497
	.byte	10,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	497
	.byte	5,17,2,35,0,13
	.byte	'IE',0,4
	.word	497
	.byte	1,16,2,35,0,13
	.byte	'PIPN',0,4
	.word	497
	.byte	10,6,2,35,0,13
	.byte	'reserved_26',0,4
	.word	497
	.byte	6,0,2,35,0,0,10
	.byte	'Ifx_CPU_ICR_Bits',0,4,253,2,3
	.word	5073
	.byte	12
	.byte	'_Ifx_CPU_ISP_Bits',0,4,128,3,16,4,13
	.byte	'ISP',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_ISP_Bits',0,4,131,3,3
	.word	5216
	.byte	12
	.byte	'_Ifx_CPU_LCX_Bits',0,4,134,3,16,4,13
	.byte	'LCXO',0,4
	.word	497
	.byte	16,16,2,35,0,13
	.byte	'LCXS',0,4
	.word	497
	.byte	4,12,2,35,0,13
	.byte	'reserved_20',0,4
	.word	497
	.byte	12,0,2,35,0,0,10
	.byte	'Ifx_CPU_LCX_Bits',0,4,139,3,3
	.word	5282
	.byte	12
	.byte	'_Ifx_CPU_M1CNT_Bits',0,4,142,3,16,4,13
	.byte	'CountValue',0,4
	.word	497
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	497
	.byte	1,0,2,35,0,0,10
	.byte	'Ifx_CPU_M1CNT_Bits',0,4,146,3,3
	.word	5388
	.byte	12
	.byte	'_Ifx_CPU_M2CNT_Bits',0,4,149,3,16,4,13
	.byte	'CountValue',0,4
	.word	497
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	497
	.byte	1,0,2,35,0,0,10
	.byte	'Ifx_CPU_M2CNT_Bits',0,4,153,3,3
	.word	5481
	.byte	12
	.byte	'_Ifx_CPU_M3CNT_Bits',0,4,156,3,16,4,13
	.byte	'CountValue',0,4
	.word	497
	.byte	31,1,2,35,0,13
	.byte	'SOvf',0,4
	.word	497
	.byte	1,0,2,35,0,0,10
	.byte	'Ifx_CPU_M3CNT_Bits',0,4,160,3,3
	.word	5574
	.byte	12
	.byte	'_Ifx_CPU_PC_Bits',0,4,163,3,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'PC',0,4
	.word	497
	.byte	31,0,2,35,0,0,10
	.byte	'Ifx_CPU_PC_Bits',0,4,167,3,3
	.word	5667
	.byte	12
	.byte	'_Ifx_CPU_PCON0_Bits',0,4,170,3,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'PCBYP',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,10
	.byte	'Ifx_CPU_PCON0_Bits',0,4,175,3,3
	.word	5752
	.byte	12
	.byte	'_Ifx_CPU_PCON1_Bits',0,4,178,3,16,4,13
	.byte	'PCINV',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'PBINV',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,10
	.byte	'Ifx_CPU_PCON1_Bits',0,4,183,3,3
	.word	5868
	.byte	12
	.byte	'_Ifx_CPU_PCON2_Bits',0,4,186,3,16,4,13
	.byte	'PCACHE_SZE',0,4
	.word	497
	.byte	16,16,2,35,0,13
	.byte	'PSCRATCH_SZE',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_PCON2_Bits',0,4,190,3,3
	.word	5979
	.byte	12
	.byte	'_Ifx_CPU_PCXI_Bits',0,4,193,3,16,4,13
	.byte	'PCXO',0,4
	.word	497
	.byte	16,16,2,35,0,13
	.byte	'PCXS',0,4
	.word	497
	.byte	4,12,2,35,0,13
	.byte	'UL',0,4
	.word	497
	.byte	1,11,2,35,0,13
	.byte	'PIE',0,4
	.word	497
	.byte	1,10,2,35,0,13
	.byte	'PCPN',0,4
	.word	497
	.byte	10,0,2,35,0,0,10
	.byte	'Ifx_CPU_PCXI_Bits',0,4,200,3,3
	.word	6080
	.byte	12
	.byte	'_Ifx_CPU_PIEAR_Bits',0,4,203,3,16,4,13
	.byte	'TA',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_PIEAR_Bits',0,4,206,3,3
	.word	6210
	.byte	12
	.byte	'_Ifx_CPU_PIETR_Bits',0,4,209,3,16,4,13
	.byte	'IED',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'IE_T',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'IE_C',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'IE_S',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'IE_BI',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'E_INFO',0,4
	.word	497
	.byte	6,21,2,35,0,13
	.byte	'IE_DUAL',0,4
	.word	497
	.byte	1,20,2,35,0,13
	.byte	'IE_SP',0,4
	.word	497
	.byte	1,19,2,35,0,13
	.byte	'IE_BS',0,4
	.word	497
	.byte	1,18,2,35,0,13
	.byte	'reserved_14',0,4
	.word	497
	.byte	18,0,2,35,0,0,10
	.byte	'Ifx_CPU_PIETR_Bits',0,4,221,3,3
	.word	6279
	.byte	12
	.byte	'_Ifx_CPU_PMA0_Bits',0,4,224,3,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	13,19,2,35,0,13
	.byte	'DAC',0,4
	.word	497
	.byte	3,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_PMA0_Bits',0,4,229,3,3
	.word	6508
	.byte	12
	.byte	'_Ifx_CPU_PMA1_Bits',0,4,232,3,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	14,18,2,35,0,13
	.byte	'CAC',0,4
	.word	497
	.byte	2,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_PMA1_Bits',0,4,237,3,3
	.word	6621
	.byte	12
	.byte	'_Ifx_CPU_PMA2_Bits',0,4,240,3,16,4,13
	.byte	'PSI',0,4
	.word	497
	.byte	16,16,2,35,0,13
	.byte	'reserved_16',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_CPU_PMA2_Bits',0,4,244,3,3
	.word	6734
	.byte	12
	.byte	'_Ifx_CPU_PSTR_Bits',0,4,247,3,16,4,13
	.byte	'FRE',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'FBE',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	497
	.byte	9,20,2,35,0,13
	.byte	'FPE',0,4
	.word	497
	.byte	1,19,2,35,0,13
	.byte	'reserved_13',0,4
	.word	497
	.byte	1,18,2,35,0,13
	.byte	'FME',0,4
	.word	497
	.byte	1,17,2,35,0,13
	.byte	'reserved_15',0,4
	.word	497
	.byte	17,0,2,35,0,0,10
	.byte	'Ifx_CPU_PSTR_Bits',0,4,129,4,3
	.word	6825
	.byte	12
	.byte	'_Ifx_CPU_PSW_Bits',0,4,132,4,16,4,13
	.byte	'CDC',0,4
	.word	497
	.byte	7,25,2,35,0,13
	.byte	'CDE',0,4
	.word	497
	.byte	1,24,2,35,0,13
	.byte	'GW',0,4
	.word	497
	.byte	1,23,2,35,0,13
	.byte	'IS',0,4
	.word	497
	.byte	1,22,2,35,0,13
	.byte	'IO',0,4
	.word	497
	.byte	2,20,2,35,0,13
	.byte	'PRS',0,4
	.word	497
	.byte	2,18,2,35,0,13
	.byte	'S',0,4
	.word	497
	.byte	1,17,2,35,0,13
	.byte	'reserved_15',0,4
	.word	497
	.byte	12,5,2,35,0,13
	.byte	'SAV',0,4
	.word	497
	.byte	1,4,2,35,0,13
	.byte	'AV',0,4
	.word	497
	.byte	1,3,2,35,0,13
	.byte	'SV',0,4
	.word	497
	.byte	1,2,2,35,0,13
	.byte	'V',0,4
	.word	497
	.byte	1,1,2,35,0,13
	.byte	'C',0,4
	.word	497
	.byte	1,0,2,35,0,0,10
	.byte	'Ifx_CPU_PSW_Bits',0,4,147,4,3
	.word	7028
	.byte	12
	.byte	'_Ifx_CPU_SEGEN_Bits',0,4,150,4,16,4,13
	.byte	'ADFLIP',0,4
	.word	497
	.byte	8,24,2,35,0,13
	.byte	'ADTYPE',0,4
	.word	497
	.byte	2,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	497
	.byte	21,1,2,35,0,13
	.byte	'AE',0,4
	.word	497
	.byte	1,0,2,35,0,0,10
	.byte	'Ifx_CPU_SEGEN_Bits',0,4,156,4,3
	.word	7271
	.byte	12
	.byte	'_Ifx_CPU_SMACON_Bits',0,4,159,4,16,4,13
	.byte	'PC',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'reserved_1',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'PT',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	497
	.byte	5,24,2,35,0,13
	.byte	'DC',0,4
	.word	497
	.byte	1,23,2,35,0,13
	.byte	'reserved_9',0,4
	.word	497
	.byte	1,22,2,35,0,13
	.byte	'DT',0,4
	.word	497
	.byte	1,21,2,35,0,13
	.byte	'reserved_11',0,4
	.word	497
	.byte	13,8,2,35,0,13
	.byte	'IODT',0,4
	.word	497
	.byte	1,7,2,35,0,13
	.byte	'reserved_25',0,4
	.word	497
	.byte	7,0,2,35,0,0,10
	.byte	'Ifx_CPU_SMACON_Bits',0,4,171,4,3
	.word	7399
	.byte	12
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,4,174,4,16,4,13
	.byte	'EN',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,4,177,4,3
	.word	7640
	.byte	12
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,4,180,4,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,4,183,4,3
	.word	7723
	.byte	12
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,4,186,4,16,4,13
	.byte	'EN',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,4,189,4,3
	.word	7814
	.byte	12
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,4,192,4,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,4,195,4,3
	.word	7905
	.byte	12
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,4,198,4,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	5,27,2,35,0,13
	.byte	'ADDR',0,4
	.word	497
	.byte	27,0,2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,4,202,4,3
	.word	8004
	.byte	12
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,4,205,4,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	5,27,2,35,0,13
	.byte	'ADDR',0,4
	.word	497
	.byte	27,0,2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,4,209,4,3
	.word	8111
	.byte	12
	.byte	'_Ifx_CPU_SWEVT_Bits',0,4,212,4,16,4,13
	.byte	'EVTA',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'BBM',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'BOD',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'SUSP',0,4
	.word	497
	.byte	1,26,2,35,0,13
	.byte	'CNT',0,4
	.word	497
	.byte	2,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,10
	.byte	'Ifx_CPU_SWEVT_Bits',0,4,220,4,3
	.word	8218
	.byte	12
	.byte	'_Ifx_CPU_SYSCON_Bits',0,4,223,4,16,4,13
	.byte	'FCDSF',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'PROTEN',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'TPROTEN',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'IS',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'IT',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	497
	.byte	27,0,2,35,0,0,10
	.byte	'Ifx_CPU_SYSCON_Bits',0,4,231,4,3
	.word	8372
	.byte	12
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,4,234,4,16,4,13
	.byte	'ASI',0,4
	.word	497
	.byte	5,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	497
	.byte	27,0,2,35,0,0,10
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,4,238,4,3
	.word	8533
	.byte	12
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,4,241,4,16,4,13
	.byte	'TEXP0',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'TEXP1',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'TEXP2',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	497
	.byte	13,16,2,35,0,13
	.byte	'TTRAP',0,4
	.word	497
	.byte	1,15,2,35,0,13
	.byte	'reserved_17',0,4
	.word	497
	.byte	15,0,2,35,0,0,10
	.byte	'Ifx_CPU_TPS_CON_Bits',0,4,249,4,3
	.word	8631
	.byte	12
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,4,252,4,16,4,13
	.byte	'Timer',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,4,255,4,3
	.word	8803
	.byte	12
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,4,130,5,16,4,13
	.byte	'ADDR',0,4
	.word	497
	.byte	32,0,2,35,0,0,10
	.byte	'Ifx_CPU_TR_ADR_Bits',0,4,133,5,3
	.word	8883
	.byte	12
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,4,136,5,16,4,13
	.byte	'EVTA',0,4
	.word	497
	.byte	3,29,2,35,0,13
	.byte	'BBM',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'BOD',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'SUSP',0,4
	.word	497
	.byte	1,26,2,35,0,13
	.byte	'CNT',0,4
	.word	497
	.byte	2,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	497
	.byte	4,20,2,35,0,13
	.byte	'TYP',0,4
	.word	497
	.byte	1,19,2,35,0,13
	.byte	'RNG',0,4
	.word	497
	.byte	1,18,2,35,0,13
	.byte	'reserved_14',0,4
	.word	497
	.byte	1,17,2,35,0,13
	.byte	'ASI_EN',0,4
	.word	497
	.byte	1,16,2,35,0,13
	.byte	'ASI',0,4
	.word	497
	.byte	5,11,2,35,0,13
	.byte	'reserved_21',0,4
	.word	497
	.byte	6,5,2,35,0,13
	.byte	'AST',0,4
	.word	497
	.byte	1,4,2,35,0,13
	.byte	'ALD',0,4
	.word	497
	.byte	1,3,2,35,0,13
	.byte	'reserved_29',0,4
	.word	497
	.byte	3,0,2,35,0,0,10
	.byte	'Ifx_CPU_TR_EVT_Bits',0,4,153,5,3
	.word	8956
	.byte	12
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,4,156,5,16,4,13
	.byte	'T0',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'T1',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'T2',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'T3',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'T4',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'T5',0,4
	.word	497
	.byte	1,26,2,35,0,13
	.byte	'T6',0,4
	.word	497
	.byte	1,25,2,35,0,13
	.byte	'T7',0,4
	.word	497
	.byte	1,24,2,35,0,13
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,10
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,4,167,5,3
	.word	9274
	.byte	14,4,175,5,9,4,2
	.byte	'unsigned int',0,4,7,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	476
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_A',0,4,180,5,3
	.word	9469
	.byte	14,4,183,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	553
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_BIV',0,4,188,5,3
	.word	9544
	.byte	14,4,191,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	632
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_BTV',0,4,196,5,3
	.word	9605
	.byte	14,4,199,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	718
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CCNT',0,4,204,5,3
	.word	9666
	.byte	14,4,207,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	807
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CCTRL',0,4,212,5,3
	.word	9728
	.byte	14,4,215,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	953
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_COMPAT',0,4,220,5,3
	.word	9791
	.byte	14,4,223,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1080
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CORE_ID',0,4,228,5,3
	.word	9855
	.byte	14,4,231,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1178
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CPR_L',0,4,236,5,3
	.word	9920
	.byte	14,4,239,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1271
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CPR_U',0,4,244,5,3
	.word	9983
	.byte	14,4,247,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1364
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CPU_ID',0,4,252,5,3
	.word	10046
	.byte	14,4,255,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1471
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CPXE',0,4,132,6,3
	.word	10110
	.byte	14,4,135,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1558
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CREVT',0,4,140,6,3
	.word	10172
	.byte	14,4,143,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1712
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_CUS_ID',0,4,148,6,3
	.word	10235
	.byte	14,4,151,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1806
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_D',0,4,156,6,3
	.word	10299
	.byte	14,4,159,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	1869
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DATR',0,4,164,6,3
	.word	10358
	.byte	14,4,167,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2087
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DBGSR',0,4,172,6,3
	.word	10420
	.byte	14,4,175,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2302
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DBGTCR',0,4,180,6,3
	.word	10483
	.byte	14,4,183,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2396
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DCON0',0,4,188,6,3
	.word	10547
	.byte	14,4,191,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2512
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DCON2',0,4,196,6,3
	.word	10610
	.byte	14,4,199,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2613
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DCX',0,4,204,6,3
	.word	10673
	.byte	14,4,207,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2706
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DEADD',0,4,212,6,3
	.word	10734
	.byte	14,4,215,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2786
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DIEAR',0,4,220,6,3
	.word	10797
	.byte	14,4,223,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	2855
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DIETR',0,4,228,6,3
	.word	10860
	.byte	14,4,231,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3084
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DMS',0,4,236,6,3
	.word	10923
	.byte	14,4,239,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3177
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DPR_L',0,4,244,6,3
	.word	10984
	.byte	14,4,247,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3272
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DPR_U',0,4,252,6,3
	.word	11047
	.byte	14,4,255,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3367
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DPRE',0,4,132,7,3
	.word	11110
	.byte	14,4,135,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3457
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DPWE',0,4,140,7,3
	.word	11172
	.byte	14,4,143,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3547
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_DSTR',0,4,148,7,3
	.word	11234
	.byte	14,4,151,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	3871
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_EXEVT',0,4,156,7,3
	.word	11296
	.byte	14,4,159,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4025
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_FCX',0,4,164,7,3
	.word	11359
	.byte	14,4,167,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4131
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,4,172,7,3
	.word	11420
	.byte	14,4,175,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4480
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,4,180,7,3
	.word	11490
	.byte	14,4,183,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4640
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,4,188,7,3
	.word	11560
	.byte	14,4,191,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4721
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,4,196,7,3
	.word	11629
	.byte	14,4,199,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4808
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,4,204,7,3
	.word	11700
	.byte	14,4,207,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4895
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,4,212,7,3
	.word	11771
	.byte	14,4,215,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	4982
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_ICNT',0,4,220,7,3
	.word	11842
	.byte	14,4,223,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5073
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_ICR',0,4,228,7,3
	.word	11904
	.byte	14,4,231,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5216
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_ISP',0,4,236,7,3
	.word	11965
	.byte	14,4,239,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5282
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_LCX',0,4,244,7,3
	.word	12026
	.byte	14,4,247,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5388
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_M1CNT',0,4,252,7,3
	.word	12087
	.byte	14,4,255,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5481
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_M2CNT',0,4,132,8,3
	.word	12150
	.byte	14,4,135,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5574
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_M3CNT',0,4,140,8,3
	.word	12213
	.byte	14,4,143,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5667
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PC',0,4,148,8,3
	.word	12276
	.byte	14,4,151,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5752
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PCON0',0,4,156,8,3
	.word	12336
	.byte	14,4,159,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5868
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PCON1',0,4,164,8,3
	.word	12399
	.byte	14,4,167,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	5979
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PCON2',0,4,172,8,3
	.word	12462
	.byte	14,4,175,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6080
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PCXI',0,4,180,8,3
	.word	12525
	.byte	14,4,183,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6210
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PIEAR',0,4,188,8,3
	.word	12587
	.byte	14,4,191,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6279
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PIETR',0,4,196,8,3
	.word	12650
	.byte	14,4,199,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6508
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PMA0',0,4,204,8,3
	.word	12713
	.byte	14,4,207,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6621
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PMA1',0,4,212,8,3
	.word	12775
	.byte	14,4,215,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6734
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PMA2',0,4,220,8,3
	.word	12837
	.byte	14,4,223,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	6825
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PSTR',0,4,228,8,3
	.word	12899
	.byte	14,4,231,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7028
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_PSW',0,4,236,8,3
	.word	12961
	.byte	14,4,239,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7271
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SEGEN',0,4,244,8,3
	.word	13022
	.byte	14,4,247,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7399
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SMACON',0,4,252,8,3
	.word	13085
	.byte	14,4,255,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7640
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_ACCENA',0,4,132,9,3
	.word	13149
	.byte	14,4,135,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7723
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_ACCENB',0,4,140,9,3
	.word	13219
	.byte	14,4,143,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7814
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,4,148,9,3
	.word	13289
	.byte	14,4,151,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	7905
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,4,156,9,3
	.word	13363
	.byte	14,4,159,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8004
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,4,164,9,3
	.word	13437
	.byte	14,4,167,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8111
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,4,172,9,3
	.word	13507
	.byte	14,4,175,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8218
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SWEVT',0,4,180,9,3
	.word	13577
	.byte	14,4,183,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8372
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_SYSCON',0,4,188,9,3
	.word	13640
	.byte	14,4,191,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8533
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_TASK_ASI',0,4,196,9,3
	.word	13704
	.byte	14,4,199,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8631
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_TPS_CON',0,4,204,9,3
	.word	13770
	.byte	14,4,207,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8803
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_TPS_TIMER',0,4,212,9,3
	.word	13835
	.byte	14,4,215,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8883
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_TR_ADR',0,4,220,9,3
	.word	13902
	.byte	14,4,223,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	8956
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_TR_EVT',0,4,228,9,3
	.word	13966
	.byte	14,4,231,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	9274
	.byte	2,35,0,0,10
	.byte	'Ifx_CPU_TRIG_ACC',0,4,236,9,3
	.word	14030
	.byte	12
	.byte	'_Ifx_CPU_CPR',0,4,247,9,25,8,15
	.byte	'L',0,4
	.word	9920
	.byte	2,35,0,15
	.byte	'U',0,4
	.word	9983
	.byte	2,35,4,0,3
	.word	14096
	.byte	10
	.byte	'Ifx_CPU_CPR',0,4,251,9,3
	.word	14138
	.byte	12
	.byte	'_Ifx_CPU_DPR',0,4,254,9,25,8,15
	.byte	'L',0,4
	.word	10984
	.byte	2,35,0,15
	.byte	'U',0,4
	.word	11047
	.byte	2,35,4,0,3
	.word	14164
	.byte	10
	.byte	'Ifx_CPU_DPR',0,4,130,10,3
	.word	14206
	.byte	12
	.byte	'_Ifx_CPU_SPROT_RGN',0,4,133,10,25,16,15
	.byte	'LA',0,4
	.word	13437
	.byte	2,35,0,15
	.byte	'UA',0,4
	.word	13507
	.byte	2,35,4,15
	.byte	'ACCENA',0,4
	.word	13289
	.byte	2,35,8,15
	.byte	'ACCENB',0,4
	.word	13363
	.byte	2,35,12,0,3
	.word	14232
	.byte	10
	.byte	'Ifx_CPU_SPROT_RGN',0,4,139,10,3
	.word	14314
	.byte	12
	.byte	'_Ifx_CPU_TPS',0,4,142,10,25,16,15
	.byte	'CON',0,4
	.word	13770
	.byte	2,35,0,16,12
	.word	13835
	.byte	17,2,0,15
	.byte	'TIMER',0,12
	.word	14378
	.byte	2,35,4,0,3
	.word	14346
	.byte	10
	.byte	'Ifx_CPU_TPS',0,4,146,10,3
	.word	14403
	.byte	12
	.byte	'_Ifx_CPU_TR',0,4,149,10,25,8,15
	.byte	'EVT',0,4
	.word	13966
	.byte	2,35,0,15
	.byte	'ADR',0,4
	.word	13902
	.byte	2,35,4,0,3
	.word	14429
	.byte	10
	.byte	'Ifx_CPU_TR',0,4,153,10,3
	.word	14474
	.byte	12
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,5,45,16,4,13
	.byte	'EN0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	247
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	247
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_DMA_ACCEN00_Bits',0,5,79,3
	.word	14499
	.byte	12
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,5,82,16,4,13
	.byte	'reserved_0',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_ACCEN01_Bits',0,5,85,3
	.word	15058
	.byte	12
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,5,88,16,4,13
	.byte	'EN0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	247
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	247
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_DMA_ACCEN10_Bits',0,5,122,3
	.word	15137
	.byte	12
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,5,125,16,4,13
	.byte	'reserved_0',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_ACCEN11_Bits',0,5,128,1,3
	.word	15696
	.byte	12
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,5,131,1,16,4,13
	.byte	'EN0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	247
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	247
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_DMA_ACCEN20_Bits',0,5,165,1,3
	.word	15776
	.byte	12
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,5,168,1,16,4,13
	.byte	'reserved_0',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_ACCEN21_Bits',0,5,171,1,3
	.word	16337
	.byte	12
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,5,174,1,16,4,13
	.byte	'EN0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	247
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	247
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_DMA_ACCEN30_Bits',0,5,208,1,3
	.word	16418
	.byte	12
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,5,211,1,16,4,13
	.byte	'reserved_0',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_ACCEN31_Bits',0,5,214,1,3
	.word	16979
	.byte	12
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,5,217,1,16,4,13
	.byte	'reserved_0',0,2
	.word	339
	.byte	16,0,2,35,0,13
	.byte	'CSER',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'CDER',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	247
	.byte	2,4,2,35,2,13
	.byte	'CSPBER',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'CSRIER',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	247
	.byte	2,0,2,35,2,13
	.byte	'CRAMER',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'CSLLER',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'CDLLER',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	247
	.byte	5,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,5,230,1,3
	.word	17060
	.byte	12
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,5,233,1,16,4,13
	.byte	'reserved_0',0,2
	.word	339
	.byte	16,0,2,35,0,13
	.byte	'ESER',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'EDER',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	247
	.byte	6,0,2,35,2,13
	.byte	'ERER',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'ELER',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	247
	.byte	5,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_EER_Bits',0,5,243,1,3
	.word	17334
	.byte	12
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,5,246,1,16,4,13
	.byte	'LEC',0,1
	.word	247
	.byte	7,1,2,35,0,13
	.byte	'reserved_7',0,2
	.word	339
	.byte	9,0,2,35,0,13
	.byte	'SER',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'DER',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	247
	.byte	2,4,2,35,2,13
	.byte	'SPBER',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'SRIER',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	247
	.byte	2,0,2,35,2,13
	.byte	'RAMER',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'SLLER',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'DLLER',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	247
	.byte	5,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,5,132,2,3
	.word	17548
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,5,135,2,16,4,13
	.byte	'SMF',0,1
	.word	247
	.byte	3,5,2,35,0,13
	.byte	'INCS',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'DMF',0,1
	.word	247
	.byte	3,1,2,35,0,13
	.byte	'INCD',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'CBLS',0,1
	.word	247
	.byte	4,4,2,35,1,13
	.byte	'CBLD',0,1
	.word	247
	.byte	4,0,2,35,1,13
	.byte	'SHCT',0,1
	.word	247
	.byte	4,4,2,35,2,13
	.byte	'SCBE',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'DCBE',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'STAMP',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'ETRL',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'WRPSE',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'WRPDE',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'INTCT',0,1
	.word	247
	.byte	2,4,2,35,3,13
	.byte	'IRDV',0,1
	.word	247
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,5,152,2,3
	.word	17832
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,5,155,2,16,4,13
	.byte	'TREL',0,2
	.word	339
	.byte	14,2,2,35,0,13
	.byte	'reserved_14',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'BLKM',0,1
	.word	247
	.byte	3,5,2,35,2,13
	.byte	'RROAT',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'CHMODE',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'CHDW',0,1
	.word	247
	.byte	3,0,2,35,2,13
	.byte	'PATSEL',0,1
	.word	247
	.byte	3,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'PRSEL',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'DMAPRIO',0,1
	.word	247
	.byte	2,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,5,168,2,3
	.word	18143
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,5,171,2,16,4,13
	.byte	'TCOUNT',0,2
	.word	339
	.byte	14,2,2,35,0,13
	.byte	'reserved_14',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'LXO',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'WRPS',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'WRPD',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'ICH',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'IPM',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,1
	.word	247
	.byte	2,2,2,35,2,13
	.byte	'BUFFER',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'FROZEN',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,5,184,2,3
	.word	18416
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,5,187,2,16,4,13
	.byte	'DADR',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,5,190,2,3
	.word	18683
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,5,193,2,16,4,13
	.byte	'RD00',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'RD01',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'RD02',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'RD03',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,5,199,2,3
	.word	18766
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,5,202,2,16,4,13
	.byte	'RD10',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'RD11',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'RD12',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'RD13',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,5,208,2,3
	.word	18893
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,5,211,2,16,4,13
	.byte	'RD20',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'RD21',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'RD22',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'RD23',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,5,217,2,3
	.word	19020
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,5,220,2,16,4,13
	.byte	'RD30',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'RD31',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'RD32',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'RD33',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,5,226,2,3
	.word	19147
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,5,229,2,16,4,13
	.byte	'RD40',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'RD41',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'RD42',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'RD43',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,5,235,2,3
	.word	19274
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,5,238,2,16,4,13
	.byte	'RD50',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'RD51',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'RD52',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'RD53',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,5,244,2,3
	.word	19401
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,5,247,2,16,4,13
	.byte	'RD60',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'RD61',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'RD62',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'RD63',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,5,253,2,3
	.word	19528
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,5,128,3,16,4,13
	.byte	'RD70',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'RD71',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'RD72',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'RD73',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,5,134,3,3
	.word	19655
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,5,137,3,16,4,13
	.byte	'RDCRC',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,5,140,3,3
	.word	19782
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,5,143,3,16,4,13
	.byte	'SADR',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,5,146,3,3
	.word	19868
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,5,149,3,16,4,13
	.byte	'SDCRC',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,5,152,3,3
	.word	19951
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,5,155,3,16,4,13
	.byte	'SHADR',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,5,158,3,3
	.word	20037
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,5,161,3,16,4,13
	.byte	'RS',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	247
	.byte	3,4,2,35,0,13
	.byte	'WS',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'reserved_5',0,2
	.word	339
	.byte	11,0,2,35,0,13
	.byte	'CH',0,1
	.word	247
	.byte	7,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	339
	.byte	9,0,2,35,2,0,10
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,5,169,3,3
	.word	20123
	.byte	12
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,5,172,3,16,4,13
	.byte	'SMF',0,1
	.word	247
	.byte	3,5,2,35,0,13
	.byte	'INCS',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'DMF',0,1
	.word	247
	.byte	3,1,2,35,0,13
	.byte	'INCD',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'CBLS',0,1
	.word	247
	.byte	4,4,2,35,1,13
	.byte	'CBLD',0,1
	.word	247
	.byte	4,0,2,35,1,13
	.byte	'SHCT',0,1
	.word	247
	.byte	4,4,2,35,2,13
	.byte	'SCBE',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'DCBE',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'STAMP',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'ETRL',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'WRPSE',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'WRPDE',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'INTCT',0,1
	.word	247
	.byte	2,4,2,35,3,13
	.byte	'IRDV',0,1
	.word	247
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,5,189,3,3
	.word	20295
	.byte	12
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,5,192,3,16,4,13
	.byte	'TREL',0,2
	.word	339
	.byte	14,2,2,35,0,13
	.byte	'reserved_14',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'BLKM',0,1
	.word	247
	.byte	3,5,2,35,2,13
	.byte	'RROAT',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'CHMODE',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'CHDW',0,1
	.word	247
	.byte	3,0,2,35,2,13
	.byte	'PATSEL',0,1
	.word	247
	.byte	3,5,2,35,3,13
	.byte	'reserved_27',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'PRSEL',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'DMAPRIO',0,1
	.word	247
	.byte	2,0,2,35,3,0,10
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,5,205,3,3
	.word	20598
	.byte	12
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,5,208,3,16,4,13
	.byte	'TCOUNT',0,2
	.word	339
	.byte	14,2,2,35,0,13
	.byte	'reserved_14',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'LXO',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'WRPS',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'WRPD',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'ICH',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'IPM',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'reserved_20',0,1
	.word	247
	.byte	2,2,2,35,2,13
	.byte	'BUFFER',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'FROZEN',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'SWB',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'CWRP',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'CICH',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'SIT',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	247
	.byte	3,1,2,35,3,13
	.byte	'SCH',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,5,226,3,3
	.word	20867
	.byte	12
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,5,229,3,16,4,13
	.byte	'DADR',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_CH_DADR_Bits',0,5,232,3,3
	.word	21205
	.byte	12
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,5,235,3,16,4,13
	.byte	'RDCRC',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,5,238,3,3
	.word	21280
	.byte	12
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,5,241,3,16,4,13
	.byte	'SADR',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_CH_SADR_Bits',0,5,244,3,3
	.word	21360
	.byte	12
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,5,247,3,16,4,13
	.byte	'SDCRC',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,5,250,3,3
	.word	21435
	.byte	12
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,5,253,3,16,4,13
	.byte	'SHADR',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,5,128,4,3
	.word	21515
	.byte	12
	.byte	'_Ifx_DMA_CLC_Bits',0,5,131,4,16,4,13
	.byte	'DISR',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'DISS',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'EDIS',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	9475
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_DMA_CLC_Bits',0,5,138,4,3
	.word	21593
	.byte	12
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,5,141,4,16,4,13
	.byte	'SIT',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	9475
	.byte	31,0,2,35,2,0,10
	.byte	'Ifx_DMA_ERRINTR_Bits',0,5,145,4,3
	.word	21736
	.byte	12
	.byte	'_Ifx_DMA_HRR_Bits',0,5,148,4,16,4,13
	.byte	'HRP',0,1
	.word	247
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	9475
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_DMA_HRR_Bits',0,5,152,4,3
	.word	21832
	.byte	12
	.byte	'_Ifx_DMA_ID_Bits',0,5,155,4,16,4,13
	.byte	'MODREV',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'MODTYPE',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'MODNUMBER',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_DMA_ID_Bits',0,5,160,4,3
	.word	21920
	.byte	12
	.byte	'_Ifx_DMA_MEMCON_Bits',0,5,163,4,16,4,13
	.byte	'reserved_0',0,4
	.word	497
	.byte	2,30,2,35,0,13
	.byte	'INTERR',0,4
	.word	497
	.byte	1,29,2,35,0,13
	.byte	'reserved_3',0,4
	.word	497
	.byte	1,28,2,35,0,13
	.byte	'RMWERR',0,4
	.word	497
	.byte	1,27,2,35,0,13
	.byte	'reserved_5',0,4
	.word	497
	.byte	1,26,2,35,0,13
	.byte	'DATAERR',0,4
	.word	497
	.byte	1,25,2,35,0,13
	.byte	'reserved_7',0,4
	.word	497
	.byte	1,24,2,35,0,13
	.byte	'PMIC',0,4
	.word	497
	.byte	1,23,2,35,0,13
	.byte	'ERRDIS',0,4
	.word	497
	.byte	1,22,2,35,0,13
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,10
	.byte	'Ifx_DMA_MEMCON_Bits',0,5,175,4,3
	.word	22027
	.byte	12
	.byte	'_Ifx_DMA_MODE_Bits',0,5,178,4,16,4,13
	.byte	'MODE',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	9475
	.byte	31,0,2,35,2,0,10
	.byte	'Ifx_DMA_MODE_Bits',0,5,182,4,3
	.word	22284
	.byte	12
	.byte	'_Ifx_DMA_OTSS_Bits',0,5,185,4,16,4,13
	.byte	'TGS',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	247
	.byte	3,1,2,35,0,13
	.byte	'BS',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	9475
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_DMA_OTSS_Bits',0,5,191,4,3
	.word	22375
	.byte	12
	.byte	'_Ifx_DMA_PRR0_Bits',0,5,194,4,16,4,13
	.byte	'PAT00',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'PAT01',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'PAT02',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'PAT03',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_PRR0_Bits',0,5,200,4,3
	.word	22501
	.byte	12
	.byte	'_Ifx_DMA_PRR1_Bits',0,5,203,4,16,4,13
	.byte	'PAT10',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'PAT11',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'PAT12',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'PAT13',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_DMA_PRR1_Bits',0,5,209,4,3
	.word	22622
	.byte	12
	.byte	'_Ifx_DMA_SUSACR_Bits',0,5,212,4,16,4,13
	.byte	'SUSAC',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	9475
	.byte	31,0,2,35,2,0,10
	.byte	'Ifx_DMA_SUSACR_Bits',0,5,216,4,3
	.word	22743
	.byte	12
	.byte	'_Ifx_DMA_SUSENR_Bits',0,5,219,4,16,4,13
	.byte	'SUSEN',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	9475
	.byte	31,0,2,35,2,0,10
	.byte	'Ifx_DMA_SUSENR_Bits',0,5,223,4,3
	.word	22839
	.byte	12
	.byte	'_Ifx_DMA_TIME_Bits',0,5,226,4,16,4,13
	.byte	'COUNT',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_DMA_TIME_Bits',0,5,229,4,3
	.word	22935
	.byte	12
	.byte	'_Ifx_DMA_TSR_Bits',0,5,232,4,16,4,13
	.byte	'RST',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'HTRE',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'TRL',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'CH',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	247
	.byte	4,0,2,35,0,13
	.byte	'HLTREQ',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'HLTACK',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'reserved_10',0,1
	.word	247
	.byte	6,0,2,35,1,13
	.byte	'ECH',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'DCH',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'CTL',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	247
	.byte	5,0,2,35,2,13
	.byte	'HLTCLR',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	247
	.byte	7,0,2,35,3,0,10
	.byte	'Ifx_DMA_TSR_Bits',0,5,248,4,3
	.word	23005
	.byte	14,5,128,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	14499
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ACCEN00',0,5,133,5,3
	.word	23306
	.byte	14,5,136,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15058
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ACCEN01',0,5,141,5,3
	.word	23371
	.byte	14,5,144,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15137
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ACCEN10',0,5,149,5,3
	.word	23436
	.byte	14,5,152,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15696
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ACCEN11',0,5,157,5,3
	.word	23501
	.byte	14,5,160,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	15776
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ACCEN20',0,5,165,5,3
	.word	23566
	.byte	14,5,168,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	16337
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ACCEN21',0,5,173,5,3
	.word	23631
	.byte	14,5,176,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	16418
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ACCEN30',0,5,181,5,3
	.word	23696
	.byte	14,5,184,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	16979
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ACCEN31',0,5,189,5,3
	.word	23761
	.byte	14,5,192,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	17060
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_CLRE',0,5,197,5,3
	.word	23826
	.byte	14,5,200,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	17334
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_EER',0,5,205,5,3
	.word	23892
	.byte	14,5,208,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	17548
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ERRSR',0,5,213,5,3
	.word	23957
	.byte	14,5,216,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	17832
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,5,221,5,3
	.word	24024
	.byte	14,5,224,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	18143
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,5,229,5,3
	.word	24094
	.byte	14,5,232,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	18416
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,5,237,5,3
	.word	24163
	.byte	14,5,240,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	18683
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_DADR',0,5,245,5,3
	.word	24232
	.byte	14,5,248,5,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	18766
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_R0',0,5,253,5,3
	.word	24301
	.byte	14,5,128,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	18893
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_R1',0,5,133,6,3
	.word	24368
	.byte	14,5,136,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19020
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_R2',0,5,141,6,3
	.word	24435
	.byte	14,5,144,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19147
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_R3',0,5,149,6,3
	.word	24502
	.byte	14,5,152,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19274
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_R4',0,5,157,6,3
	.word	24569
	.byte	14,5,160,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19401
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_R5',0,5,165,6,3
	.word	24636
	.byte	14,5,168,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19528
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_R6',0,5,173,6,3
	.word	24703
	.byte	14,5,176,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19655
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_R7',0,5,181,6,3
	.word	24770
	.byte	14,5,184,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19782
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,5,189,6,3
	.word	24837
	.byte	14,5,192,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19868
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_SADR',0,5,197,6,3
	.word	24907
	.byte	14,5,200,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	19951
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,5,205,6,3
	.word	24976
	.byte	14,5,208,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	20037
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,5,213,6,3
	.word	25046
	.byte	14,5,216,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	20123
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_BLK_ME_SR',0,5,221,6,3
	.word	25116
	.byte	14,5,224,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	20295
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CH_ADICR',0,5,229,6,3
	.word	25183
	.byte	14,5,232,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	20598
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CH_CHCFGR',0,5,237,6,3
	.word	25249
	.byte	14,5,240,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	20867
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CH_CHCSR',0,5,245,6,3
	.word	25316
	.byte	14,5,248,6,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21205
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CH_DADR',0,5,253,6,3
	.word	25382
	.byte	14,5,128,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21280
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CH_RDCRCR',0,5,133,7,3
	.word	25447
	.byte	14,5,136,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21360
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CH_SADR',0,5,141,7,3
	.word	25514
	.byte	14,5,144,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21435
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CH_SDCRCR',0,5,149,7,3
	.word	25579
	.byte	14,5,152,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21515
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CH_SHADR',0,5,157,7,3
	.word	25646
	.byte	14,5,160,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21593
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_CLC',0,5,165,7,3
	.word	25712
	.byte	14,5,168,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21736
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ERRINTR',0,5,173,7,3
	.word	25773
	.byte	14,5,176,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21832
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_HRR',0,5,181,7,3
	.word	25838
	.byte	14,5,184,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	21920
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_ID',0,5,189,7,3
	.word	25899
	.byte	14,5,192,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22027
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_MEMCON',0,5,197,7,3
	.word	25959
	.byte	14,5,200,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22284
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_MODE',0,5,205,7,3
	.word	26023
	.byte	14,5,208,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22375
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_OTSS',0,5,213,7,3
	.word	26085
	.byte	14,5,216,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22501
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_PRR0',0,5,221,7,3
	.word	26147
	.byte	14,5,224,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22622
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_PRR1',0,5,229,7,3
	.word	26209
	.byte	14,5,232,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22743
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_SUSACR',0,5,237,7,3
	.word	26271
	.byte	14,5,240,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22839
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_SUSENR',0,5,245,7,3
	.word	26335
	.byte	14,5,248,7,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	22935
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_TIME',0,5,253,7,3
	.word	26399
	.byte	14,5,128,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	23005
	.byte	2,35,0,0,10
	.byte	'Ifx_DMA_TSR',0,5,133,8,3
	.word	26461
	.byte	12
	.byte	'_Ifx_DMA_BLK_ME',0,5,144,8,25,112,15
	.byte	'SR',0,4
	.word	25116
	.byte	2,35,0,16,12
	.word	247
	.byte	17,11,0,15
	.byte	'reserved_4',0,12
	.word	26556
	.byte	2,35,4,15
	.byte	'R0',0,4
	.word	24301
	.byte	2,35,16,15
	.byte	'R1',0,4
	.word	24368
	.byte	2,35,20,15
	.byte	'R2',0,4
	.word	24435
	.byte	2,35,24,15
	.byte	'R3',0,4
	.word	24502
	.byte	2,35,28,15
	.byte	'R4',0,4
	.word	24569
	.byte	2,35,32,15
	.byte	'R5',0,4
	.word	24636
	.byte	2,35,36,15
	.byte	'R6',0,4
	.word	24703
	.byte	2,35,40,15
	.byte	'R7',0,4
	.word	24770
	.byte	2,35,44,16,32
	.word	247
	.byte	17,31,0,15
	.byte	'reserved_30',0,32
	.word	26681
	.byte	2,35,48,15
	.byte	'RDCRC',0,4
	.word	24837
	.byte	2,35,80,15
	.byte	'SDCRC',0,4
	.word	24976
	.byte	2,35,84,15
	.byte	'SADR',0,4
	.word	24907
	.byte	2,35,88,15
	.byte	'DADR',0,4
	.word	24232
	.byte	2,35,92,15
	.byte	'ADICR',0,4
	.word	24024
	.byte	2,35,96,15
	.byte	'CHCR',0,4
	.word	24094
	.byte	2,35,100,15
	.byte	'SHADR',0,4
	.word	25046
	.byte	2,35,104,15
	.byte	'CHSR',0,4
	.word	24163
	.byte	2,35,108,0,3
	.word	26522
	.byte	10
	.byte	'Ifx_DMA_BLK_ME',0,5,165,8,3
	.word	26828
	.byte	12
	.byte	'_Ifx_DMA_BLK',0,5,178,8,25,128,1,15
	.byte	'EER',0,4
	.word	23892
	.byte	2,35,0,15
	.byte	'ERRSR',0,4
	.word	23957
	.byte	2,35,4,15
	.byte	'CLRE',0,4
	.word	23826
	.byte	2,35,8,16,4
	.word	247
	.byte	17,3,0,15
	.byte	'reserved_C',0,4
	.word	26919
	.byte	2,35,12,3
	.word	26522
	.byte	15
	.byte	'ME',0,112
	.word	26948
	.byte	2,35,16,0,3
	.word	26857
	.byte	10
	.byte	'Ifx_DMA_BLK',0,5,185,8,3
	.word	26966
	.byte	12
	.byte	'_Ifx_DMA_CH',0,5,188,8,25,32,15
	.byte	'RDCRCR',0,4
	.word	25447
	.byte	2,35,0,15
	.byte	'SDCRCR',0,4
	.word	25579
	.byte	2,35,4,15
	.byte	'SADR',0,4
	.word	25514
	.byte	2,35,8,15
	.byte	'DADR',0,4
	.word	25382
	.byte	2,35,12,15
	.byte	'ADICR',0,4
	.word	25183
	.byte	2,35,16,15
	.byte	'CHCFGR',0,4
	.word	25249
	.byte	2,35,20,15
	.byte	'SHADR',0,4
	.word	25646
	.byte	2,35,24,15
	.byte	'CHCSR',0,4
	.word	25316
	.byte	2,35,28,0,3
	.word	26992
	.byte	10
	.byte	'Ifx_DMA_CH',0,5,198,8,3
	.word	27132
	.byte	12
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,13
	.byte	'EN0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'EN1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'EN2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'EN3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'EN4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'EN5',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'EN6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'EN7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'EN8',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'EN9',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'EN10',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'EN11',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'EN12',0,1
	.word	247
	.byte	1,3,2,35,1,13
	.byte	'EN13',0,1
	.word	247
	.byte	1,2,2,35,1,13
	.byte	'EN14',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'EN15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'EN16',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'EN17',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'EN18',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'EN19',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'EN20',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'EN21',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'EN22',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'EN23',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'EN24',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'EN25',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'EN26',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'EN27',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'EN28',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'EN29',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'EN30',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'EN31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	27157
	.byte	12
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,13
	.byte	'reserved_0',0,4
	.word	9475
	.byte	32,0,2,35,2,0,10
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	27714
	.byte	12
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,13
	.byte	'STM0DIS',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'STM1DIS',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'STM2DIS',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,4
	.word	9475
	.byte	29,0,2,35,2,0,10
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	27791
	.byte	12
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'BAUD2DIV',0,1
	.word	247
	.byte	4,0,2,35,0,13
	.byte	'SRIDIV',0,1
	.word	247
	.byte	4,4,2,35,1,13
	.byte	'LPDIV',0,1
	.word	247
	.byte	4,0,2,35,1,13
	.byte	'SPBDIV',0,1
	.word	247
	.byte	4,4,2,35,2,13
	.byte	'FSI2DIV',0,1
	.word	247
	.byte	2,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	247
	.byte	2,0,2,35,2,13
	.byte	'FSIDIV',0,1
	.word	247
	.byte	2,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	247
	.byte	2,4,2,35,3,13
	.byte	'CLKSEL',0,1
	.word	247
	.byte	2,2,2,35,3,13
	.byte	'UP',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	27927
	.byte	12
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,13
	.byte	'CANDIV',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'ERAYDIV',0,1
	.word	247
	.byte	4,0,2,35,0,13
	.byte	'STMDIV',0,1
	.word	247
	.byte	4,4,2,35,1,13
	.byte	'GTMDIV',0,1
	.word	247
	.byte	4,0,2,35,1,13
	.byte	'ETHDIV',0,1
	.word	247
	.byte	4,4,2,35,2,13
	.byte	'ASCLINFDIV',0,1
	.word	247
	.byte	4,0,2,35,2,13
	.byte	'ASCLINSDIV',0,1
	.word	247
	.byte	4,4,2,35,3,13
	.byte	'INSEL',0,1
	.word	247
	.byte	2,2,2,35,3,13
	.byte	'UP',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	28209
	.byte	12
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,13
	.byte	'BBBDIV',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	9475
	.byte	26,2,2,35,2,13
	.byte	'UP',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	28447
	.byte	12
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,13
	.byte	'PLLDIV',0,1
	.word	247
	.byte	6,2,2,35,0,13
	.byte	'PLLSEL',0,1
	.word	247
	.byte	2,0,2,35,0,13
	.byte	'PLLERAYDIV',0,1
	.word	247
	.byte	6,2,2,35,1,13
	.byte	'PLLERAYSEL',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'SRIDIV',0,1
	.word	247
	.byte	6,2,2,35,2,13
	.byte	'SRISEL',0,1
	.word	247
	.byte	2,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	6,2,2,35,3,13
	.byte	'UP',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,149,1,3
	.word	28575
	.byte	12
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,152,1,16,4,13
	.byte	'SPBDIV',0,1
	.word	247
	.byte	6,2,2,35,0,13
	.byte	'SPBSEL',0,1
	.word	247
	.byte	2,0,2,35,0,13
	.byte	'GTMDIV',0,1
	.word	247
	.byte	6,2,2,35,1,13
	.byte	'GTMSEL',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'STMDIV',0,1
	.word	247
	.byte	6,2,2,35,2,13
	.byte	'STMSEL',0,1
	.word	247
	.byte	2,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	6,2,2,35,3,13
	.byte	'UP',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,163,1,3
	.word	28802
	.byte	12
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,166,1,16,4,13
	.byte	'MAXDIV',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	9475
	.byte	26,2,2,35,2,13
	.byte	'UP',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,172,1,3
	.word	29021
	.byte	12
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,175,1,16,4,13
	.byte	'CPU0DIV',0,1
	.word	247
	.byte	6,2,2,35,0,13
	.byte	'reserved_6',0,4
	.word	9475
	.byte	26,0,2,35,2,0,10
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,179,1,3
	.word	29149
	.byte	12
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,182,1,16,4,13
	.byte	'CHREV',0,1
	.word	247
	.byte	6,2,2,35,0,13
	.byte	'CHTEC',0,1
	.word	247
	.byte	2,0,2,35,0,13
	.byte	'CHID',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'EEA',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'UCODE',0,1
	.word	247
	.byte	7,0,2,35,2,13
	.byte	'FSIZE',0,1
	.word	247
	.byte	4,4,2,35,3,13
	.byte	'SP',0,1
	.word	247
	.byte	2,2,2,35,3,13
	.byte	'SEC',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,193,1,3
	.word	29249
	.byte	12
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,196,1,16,4,13
	.byte	'PWD',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'START',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	2,4,2,35,0,13
	.byte	'CAL',0,4
	.word	9475
	.byte	22,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	247
	.byte	5,1,2,35,3,13
	.byte	'SLCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,204,1,3
	.word	29457
	.byte	12
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,207,1,16,4,13
	.byte	'LOWER',0,2
	.word	339
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	247
	.byte	5,1,2,35,1,13
	.byte	'LLU',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'UPPER',0,2
	.word	339
	.byte	10,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	247
	.byte	4,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'UOF',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,216,1,3
	.word	29622
	.byte	12
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,219,1,16,4,13
	.byte	'RESULT',0,2
	.word	339
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	247
	.byte	4,2,2,35,1,13
	.byte	'RDY',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'BUSY',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,226,1,3
	.word	29805
	.byte	12
	.byte	'_Ifx_SCU_EICR_Bits',0,6,229,1,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'EXIS0',0,1
	.word	247
	.byte	3,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'FEN0',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'REN0',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'LDEN0',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'EIEN0',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'INP0',0,1
	.word	247
	.byte	3,1,2,35,1,13
	.byte	'reserved_15',0,4
	.word	9475
	.byte	5,12,2,35,2,13
	.byte	'EXIS1',0,1
	.word	247
	.byte	3,1,2,35,2,13
	.byte	'reserved_23',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'FEN1',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'REN1',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'LDEN1',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'EIEN1',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'INP1',0,1
	.word	247
	.byte	3,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EICR_Bits',0,6,248,1,3
	.word	29959
	.byte	12
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,251,1,16,4,13
	.byte	'INTF0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'INTF1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'INTF2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'INTF3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'INTF4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'INTF5',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'INTF6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'INTF7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	9475
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_SCU_EIFR_Bits',0,6,134,2,3
	.word	30323
	.byte	12
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,137,2,16,4,13
	.byte	'POL',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'MODE',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'ENON',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'PSEL',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,2
	.word	339
	.byte	12,0,2,35,0,13
	.byte	'EMSF',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'SEMSF',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	247
	.byte	6,0,2,35,2,13
	.byte	'EMSFM',0,1
	.word	247
	.byte	2,6,2,35,3,13
	.byte	'SEMSFM',0,1
	.word	247
	.byte	2,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	247
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_SCU_EMSR_Bits',0,6,150,2,3
	.word	30534
	.byte	12
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,153,2,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	7,1,2,35,0,13
	.byte	'EDCON',0,2
	.word	339
	.byte	2,7,2,35,0,13
	.byte	'reserved_9',0,4
	.word	9475
	.byte	23,0,2,35,2,0,10
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,158,2,3
	.word	30786
	.byte	12
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,161,2,16,4,13
	.byte	'ARI',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'ARC',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	9475
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,166,2,3
	.word	30904
	.byte	12
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,169,2,16,4,13
	.byte	'reserved_0',0,4
	.word	9475
	.byte	28,4,2,35,2,13
	.byte	'EVR13OFF',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'BPEVR13OFF',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,176,2,3
	.word	31015
	.byte	12
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,179,2,16,4,13
	.byte	'ADC13V',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'ADCSWDV',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	7,1,2,35,3,13
	.byte	'VAL',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,186,2,3
	.word	31178
	.byte	12
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,189,2,16,4,13
	.byte	'EVR13OVMOD',0,1
	.word	247
	.byte	2,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	2,4,2,35,0,13
	.byte	'EVR13UVMOD',0,1
	.word	247
	.byte	2,2,2,35,0,13
	.byte	'reserved_6',0,2
	.word	339
	.byte	10,0,2,35,0,13
	.byte	'SWDOVMOD',0,1
	.word	247
	.byte	2,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	247
	.byte	2,4,2,35,2,13
	.byte	'SWDUVMOD',0,1
	.word	247
	.byte	2,2,2,35,2,13
	.byte	'reserved_22',0,2
	.word	339
	.byte	8,2,2,35,2,13
	.byte	'SLCK',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,201,2,3
	.word	31340
	.byte	12
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,204,2,16,4,13
	.byte	'EVR13OVVAL',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'SWDOVVAL',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	6,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,212,2,3
	.word	31618
	.byte	12
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,215,2,16,4,13
	.byte	'reserved_0',0,4
	.word	9475
	.byte	28,4,2,35,2,13
	.byte	'RSTSWDOFF',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'BPRSTSWDOFF',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,222,2,3
	.word	31797
	.byte	12
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,225,2,16,4,13
	.byte	'SD33P',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	247
	.byte	4,0,2,35,0,13
	.byte	'SD33I',0,1
	.word	247
	.byte	4,4,2,35,1,13
	.byte	'reserved_12',0,4
	.word	9475
	.byte	19,1,2,35,2,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,232,2,3
	.word	31957
	.byte	12
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,235,2,16,4,13
	.byte	'SDFREQSPRD',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	247
	.byte	4,0,2,35,0,13
	.byte	'TON',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'TOFF',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'SDSTEP',0,1
	.word	247
	.byte	4,4,2,35,3,13
	.byte	'SYNCDIV',0,1
	.word	247
	.byte	3,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,244,2,3
	.word	32118
	.byte	12
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,247,2,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'STBS',0,1
	.word	247
	.byte	2,6,2,35,1,13
	.byte	'STSP',0,1
	.word	247
	.byte	2,4,2,35,1,13
	.byte	'NS',0,1
	.word	247
	.byte	2,2,2,35,1,13
	.byte	'OL',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'PIAD',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'ADCMODE',0,1
	.word	247
	.byte	4,4,2,35,2,13
	.byte	'ADCLPF',0,1
	.word	247
	.byte	2,2,2,35,2,13
	.byte	'ADCLSB',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'reserved_23',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'SDLUT',0,1
	.word	247
	.byte	6,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,134,3,3
	.word	32310
	.byte	12
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,137,3,16,4,13
	.byte	'SDOLCON',0,1
	.word	247
	.byte	7,1,2,35,0,13
	.byte	'MODSEL',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'MODLOW',0,1
	.word	247
	.byte	7,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'SDVOKLVL',0,1
	.word	247
	.byte	6,2,2,35,2,13
	.byte	'MODMAN',0,1
	.word	247
	.byte	2,0,2,35,2,13
	.byte	'MODHIGH',0,1
	.word	247
	.byte	7,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,147,3,3
	.word	32606
	.byte	12
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,150,3,16,4,13
	.byte	'EVR13',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'OV13',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	2,4,2,35,0,13
	.byte	'OVSWD',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'UV13',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'UVSWD',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	2,6,2,35,1,13
	.byte	'BGPROK',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'reserved_11',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'SCMOD',0,1
	.word	247
	.byte	2,2,2,35,1,13
	.byte	'reserved_14',0,4
	.word	9475
	.byte	18,0,2,35,2,0,10
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,164,3,3
	.word	32821
	.byte	12
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,167,3,16,4,13
	.byte	'EVR13UVVAL',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'SWDUVVAL',0,1
	.word	247
	.byte	8,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	6,2,2,35,3,13
	.byte	'SLCK',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,175,3,3
	.word	33110
	.byte	12
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,178,3,16,4,13
	.byte	'EN0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'SEL0',0,1
	.word	247
	.byte	4,2,2,35,0,13
	.byte	'reserved_6',0,2
	.word	339
	.byte	10,0,2,35,0,13
	.byte	'EN1',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'NSEL',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'SEL1',0,1
	.word	247
	.byte	4,2,2,35,2,13
	.byte	'reserved_22',0,1
	.word	247
	.byte	2,0,2,35,2,13
	.byte	'DIV1',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,189,3,3
	.word	33289
	.byte	12
	.byte	'_Ifx_SCU_FDR_Bits',0,6,192,3,16,4,13
	.byte	'STEP',0,2
	.word	339
	.byte	10,6,2,35,0,13
	.byte	'reserved_10',0,1
	.word	247
	.byte	4,2,2,35,1,13
	.byte	'DM',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'RESULT',0,2
	.word	339
	.byte	10,6,2,35,2,13
	.byte	'reserved_26',0,1
	.word	247
	.byte	5,1,2,35,3,13
	.byte	'DISCLK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_FDR_Bits',0,6,200,3,3
	.word	33507
	.byte	12
	.byte	'_Ifx_SCU_FMR_Bits',0,6,203,3,16,4,13
	.byte	'FS0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'FS1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'FS2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'FS3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'FS4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'FS5',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'FS6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'FS7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'FC0',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'FC1',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'FC2',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'FC3',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'FC4',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'FC5',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'FC6',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'FC7',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_SCU_FMR_Bits',0,6,223,3,3
	.word	33670
	.byte	12
	.byte	'_Ifx_SCU_ID_Bits',0,6,226,3,16,4,13
	.byte	'MODREV',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'MODTYPE',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'MODNUMBER',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_ID_Bits',0,6,231,3,3
	.word	34006
	.byte	12
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,234,3,16,4,13
	.byte	'IPEN00',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'IPEN01',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'IPEN02',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'IPEN03',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'IPEN04',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'IPEN05',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'IPEN06',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'IPEN07',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	5,3,2,35,1,13
	.byte	'GEEN0',0,1
	.word	247
	.byte	1,2,2,35,1,13
	.byte	'IGP0',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'IPEN10',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'IPEN11',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'IPEN12',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'IPEN13',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'IPEN14',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'IPEN15',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'IPEN16',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'IPEN17',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	5,3,2,35,3,13
	.byte	'GEEN1',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'IGP1',0,1
	.word	247
	.byte	2,0,2,35,3,0,10
	.byte	'Ifx_SCU_IGCR_Bits',0,6,130,4,3
	.word	34113
	.byte	12
	.byte	'_Ifx_SCU_IN_Bits',0,6,133,4,16,4,13
	.byte	'P0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	9475
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_IN_Bits',0,6,138,4,3
	.word	34565
	.byte	12
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,141,4,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	4,4,2,35,0,13
	.byte	'PC0',0,1
	.word	247
	.byte	4,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	4,4,2,35,1,13
	.byte	'PC1',0,1
	.word	247
	.byte	4,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_IOCR_Bits',0,6,148,4,3
	.word	34664
	.byte	12
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,151,4,16,4,13
	.byte	'LBISTREQ',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'LBISTREQP',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'PATTERNS',0,2
	.word	339
	.byte	14,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,157,4,3
	.word	34814
	.byte	12
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,160,4,16,4,13
	.byte	'SEED',0,4
	.word	9475
	.byte	23,9,2,35,2,13
	.byte	'reserved_23',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'SPLITSH',0,1
	.word	247
	.byte	3,5,2,35,3,13
	.byte	'BODY',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'LBISTFREQU',0,1
	.word	247
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,167,4,3
	.word	34963
	.byte	12
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,170,4,16,4,13
	.byte	'SIGNATURE',0,4
	.word	9475
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	7,1,2,35,3,13
	.byte	'LBISTDONE',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,175,4,3
	.word	35124
	.byte	12
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,6,178,4,16,4,13
	.byte	'reserved_0',0,2
	.word	339
	.byte	16,0,2,35,0,13
	.byte	'LS',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,2
	.word	339
	.byte	14,1,2,35,2,13
	.byte	'LSEN',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_LCLCON0_Bits',0,6,184,4,3
	.word	35254
	.byte	12
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,187,4,16,4,13
	.byte	'LCLT0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'LCLT1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	9475
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,192,4,3
	.word	35388
	.byte	12
	.byte	'_Ifx_SCU_MANID_Bits',0,6,195,4,16,4,13
	.byte	'DEPT',0,1
	.word	247
	.byte	5,3,2,35,0,13
	.byte	'MANUF',0,2
	.word	339
	.byte	11,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_MANID_Bits',0,6,200,4,3
	.word	35503
	.byte	12
	.byte	'_Ifx_SCU_OMR_Bits',0,6,203,4,16,4,13
	.byte	'PS0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'PS1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,2
	.word	339
	.byte	14,0,2,35,0,13
	.byte	'PCL0',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'PCL1',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,2
	.word	339
	.byte	14,0,2,35,2,0,10
	.byte	'Ifx_SCU_OMR_Bits',0,6,211,4,3
	.word	35614
	.byte	12
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,214,4,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'PLLLV',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'OSCRES',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'GAINSEL',0,1
	.word	247
	.byte	2,3,2,35,0,13
	.byte	'MODE',0,1
	.word	247
	.byte	2,1,2,35,0,13
	.byte	'SHBY',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'PLLHV',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'X1D',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'X1DEN',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	247
	.byte	4,0,2,35,1,13
	.byte	'OSCVAL',0,1
	.word	247
	.byte	5,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	247
	.byte	2,1,2,35,2,13
	.byte	'APREN',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	8,0,2,35,3,0,10
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,231,4,3
	.word	35772
	.byte	12
	.byte	'_Ifx_SCU_OUT_Bits',0,6,234,4,16,4,13
	.byte	'P0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'P1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	9475
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_OUT_Bits',0,6,239,4,3
	.word	36112
	.byte	12
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,242,4,16,4,13
	.byte	'CSEL0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'CSEL1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'CSEL2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,2
	.word	339
	.byte	13,0,2,35,0,13
	.byte	'OVSTRT',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'OVSTP',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'DCINVAL',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	247
	.byte	5,0,2,35,2,13
	.byte	'OVCONF',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'POVCONF',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	247
	.byte	6,0,2,35,3,0,10
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,255,4,3
	.word	36213
	.byte	12
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,130,5,16,4,13
	.byte	'OVEN0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'OVEN1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'OVEN2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,4
	.word	9475
	.byte	29,0,2,35,2,0,10
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,136,5,3
	.word	36480
	.byte	12
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,139,5,16,4,13
	.byte	'PDIS0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'PDIS1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	9475
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_PDISC_Bits',0,6,144,5,3
	.word	36616
	.byte	12
	.byte	'_Ifx_SCU_PDR_Bits',0,6,147,5,16,4,13
	.byte	'PD0',0,1
	.word	247
	.byte	3,5,2,35,0,13
	.byte	'PL0',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'PD1',0,1
	.word	247
	.byte	3,1,2,35,0,13
	.byte	'PL1',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	9475
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_SCU_PDR_Bits',0,6,154,5,3
	.word	36727
	.byte	12
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,157,5,16,4,13
	.byte	'PDR0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'PDR1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'PDR2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'PDR3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'PDR4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'PDR5',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'PDR6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'PDR7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	9475
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_SCU_PDRR_Bits',0,6,168,5,3
	.word	36860
	.byte	12
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,171,5,16,4,13
	.byte	'VCOBYP',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'VCOPWD',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'MODEN',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'SETFINDIS',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'CLRFINDIS',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'OSCDISCDIS',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'reserved_7',0,2
	.word	339
	.byte	2,7,2,35,0,13
	.byte	'NDIV',0,1
	.word	247
	.byte	7,0,2,35,1,13
	.byte	'PLLPWD',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'RESLD',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	247
	.byte	5,0,2,35,2,13
	.byte	'PDIV',0,1
	.word	247
	.byte	4,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	247
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,188,5,3
	.word	37063
	.byte	12
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,191,5,16,4,13
	.byte	'K2DIV',0,1
	.word	247
	.byte	7,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'K3DIV',0,1
	.word	247
	.byte	7,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'K1DIV',0,1
	.word	247
	.byte	7,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	339
	.byte	9,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,199,5,3
	.word	37419
	.byte	12
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,202,5,16,4,13
	.byte	'MODCFG',0,2
	.word	339
	.byte	16,0,2,35,0,13
	.byte	'reserved_16',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,206,5,3
	.word	37597
	.byte	12
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,209,5,16,4,13
	.byte	'VCOBYP',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'VCOPWD',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	2,4,2,35,0,13
	.byte	'SETFINDIS',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'CLRFINDIS',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'OSCDISCDIS',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'reserved_7',0,2
	.word	339
	.byte	2,7,2,35,0,13
	.byte	'NDIV',0,1
	.word	247
	.byte	5,2,2,35,1,13
	.byte	'reserved_14',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'PLLPWD',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'RESLD',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	247
	.byte	5,0,2,35,2,13
	.byte	'PDIV',0,1
	.word	247
	.byte	4,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	247
	.byte	4,0,2,35,3,0,10
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,226,5,3
	.word	37697
	.byte	12
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,229,5,16,4,13
	.byte	'K2DIV',0,1
	.word	247
	.byte	7,1,2,35,0,13
	.byte	'reserved_7',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'K3DIV',0,1
	.word	247
	.byte	4,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	247
	.byte	4,0,2,35,1,13
	.byte	'K1DIV',0,1
	.word	247
	.byte	7,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	339
	.byte	9,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,237,5,3
	.word	38067
	.byte	12
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,240,5,16,4,13
	.byte	'VCOBYST',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'PWDSTAT',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'VCOLOCK',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'FINDIS',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'K1RDY',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'K2RDY',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,4
	.word	9475
	.byte	26,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,249,5,3
	.word	38253
	.byte	12
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,252,5,16,4,13
	.byte	'VCOBYST',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'VCOLOCK',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'FINDIS',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'K1RDY',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'K2RDY',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'reserved_6',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'MODRUN',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,4
	.word	9475
	.byte	24,0,2,35,2,0,10
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,135,6,3
	.word	38451
	.byte	12
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,138,6,16,4,13
	.byte	'REQSLP',0,1
	.word	247
	.byte	2,6,2,35,0,13
	.byte	'SMUSLP',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'reserved_3',0,1
	.word	247
	.byte	5,0,2,35,0,13
	.byte	'PMST',0,1
	.word	247
	.byte	3,5,2,35,1,13
	.byte	'reserved_11',0,4
	.word	9475
	.byte	21,0,2,35,2,0,10
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,145,6,3
	.word	38684
	.byte	12
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,148,6,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'ESR1WKEN',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'PINAWKEN',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'PINBWKEN',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'ESR0DFEN',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'ESR0EDCON',0,1
	.word	247
	.byte	2,1,2,35,0,13
	.byte	'ESR1DFEN',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'ESR1EDCON',0,1
	.word	247
	.byte	2,6,2,35,1,13
	.byte	'PINADFEN',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'PINAEDCON',0,1
	.word	247
	.byte	2,3,2,35,1,13
	.byte	'PINBDFEN',0,1
	.word	247
	.byte	1,2,2,35,1,13
	.byte	'PINBEDCON',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'reserved_16',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'STBYRAMSEL',0,1
	.word	247
	.byte	2,5,2,35,2,13
	.byte	'reserved_19',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'WUTWKEN',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	247
	.byte	2,1,2,35,2,13
	.byte	'PORSTDF',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'DCDCSYNC',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	247
	.byte	3,3,2,35,3,13
	.byte	'ESR0TRIST',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'reserved_30',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,174,6,3
	.word	38836
	.byte	12
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,177,6,16,4,13
	.byte	'reserved_0',0,2
	.word	339
	.byte	12,4,2,35,0,13
	.byte	'IRADIS',0,1
	.word	247
	.byte	1,3,2,35,1,13
	.byte	'reserved_13',0,4
	.word	9475
	.byte	14,5,2,35,2,13
	.byte	'STBYEVEN',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'STBYEV',0,1
	.word	247
	.byte	3,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,185,6,3
	.word	39395
	.byte	12
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,6,188,6,16,4,13
	.byte	'WUTREL',0,4
	.word	9475
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	4,4,2,35,3,13
	.byte	'WUTDIV',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'WUTEN',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'WUTMODE',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'LCK',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,6,196,6,3
	.word	39578
	.byte	12
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,199,6,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	2,6,2,35,0,13
	.byte	'ESR1WKP',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'ESR1OVRUN',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'PINAWKP',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'PINAOVRUN',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'PINBWKP',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'PINBOVRUN',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'PORSTDF',0,1
	.word	247
	.byte	1,6,2,35,1,13
	.byte	'HWCFGEVR',0,1
	.word	247
	.byte	3,3,2,35,1,13
	.byte	'STBYRAM',0,1
	.word	247
	.byte	2,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'WUTWKP',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'WUTOVRUN',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'WUTWKEN',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'ESR1WKEN',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'PINAWKEN',0,1
	.word	247
	.byte	1,2,2,35,2,13
	.byte	'PINBWKEN',0,1
	.word	247
	.byte	1,1,2,35,2,13
	.byte	'reserved_23',0,2
	.word	339
	.byte	4,5,2,35,2,13
	.byte	'ESR0TRIST',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'reserved_28',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'WUTEN',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'WUTMODE',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'WUTRUN',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,226,6,3
	.word	39747
	.byte	12
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,229,6,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	2,6,2,35,0,13
	.byte	'ESR1WKPCLR',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'ESR1OVRUNCLR',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'PINAWKPCLR',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'PINAOVRUNCLR',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'PINBWKPCLR',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'PINBOVRUNCLR',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'WUTWKPCLR',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'WUTOVRUNCLR',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,2
	.word	339
	.byte	14,0,2,35,2,0,10
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,242,6,3
	.word	40314
	.byte	12
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,6,245,6,16,4,13
	.byte	'WUTCNT',0,4
	.word	9475
	.byte	24,8,2,35,2,13
	.byte	'reserved_24',0,1
	.word	247
	.byte	7,1,2,35,3,13
	.byte	'VAL',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,6,250,6,3
	.word	40630
	.byte	12
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,253,6,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'CLRC',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,2
	.word	339
	.byte	10,4,2,35,0,13
	.byte	'CSS0',0,1
	.word	247
	.byte	1,3,2,35,1,13
	.byte	'CSS1',0,1
	.word	247
	.byte	1,2,2,35,1,13
	.byte	'CSS2',0,1
	.word	247
	.byte	1,1,2,35,1,13
	.byte	'reserved_15',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'USRINFO',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,135,7,3
	.word	40749
	.byte	12
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,138,7,16,4,13
	.byte	'ESR0',0,1
	.word	247
	.byte	2,6,2,35,0,13
	.byte	'ESR1',0,1
	.word	247
	.byte	2,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	247
	.byte	2,2,2,35,0,13
	.byte	'SMU',0,1
	.word	247
	.byte	2,0,2,35,0,13
	.byte	'SW',0,1
	.word	247
	.byte	2,6,2,35,1,13
	.byte	'STM0',0,1
	.word	247
	.byte	2,4,2,35,1,13
	.byte	'STM1',0,1
	.word	247
	.byte	2,2,2,35,1,13
	.byte	'STM2',0,1
	.word	247
	.byte	2,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,149,7,3
	.word	40958
	.byte	12
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,152,7,16,4,13
	.byte	'ESR0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'ESR1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'SMU',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'SW',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'STM0',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'STM1',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'STM2',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	8,0,2,35,1,13
	.byte	'PORST',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'reserved_17',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'CB0',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'CB1',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'CB3',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	247
	.byte	2,1,2,35,2,13
	.byte	'EVR13',0,1
	.word	247
	.byte	1,0,2,35,2,13
	.byte	'EVR33',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'SWD',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'reserved_26',0,1
	.word	247
	.byte	2,4,2,35,3,13
	.byte	'STBYR',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'reserved_29',0,1
	.word	247
	.byte	3,0,2,35,3,0,10
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,175,7,3
	.word	41169
	.byte	12
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,178,7,16,4,13
	.byte	'HBT',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,4
	.word	9475
	.byte	31,0,2,35,2,0,10
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,182,7,3
	.word	41601
	.byte	12
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,185,7,16,4,13
	.byte	'HWCFG',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'FTM',0,1
	.word	247
	.byte	7,1,2,35,1,13
	.byte	'MODE',0,1
	.word	247
	.byte	1,0,2,35,1,13
	.byte	'FCBAE',0,1
	.word	247
	.byte	1,7,2,35,2,13
	.byte	'LUDIS',0,1
	.word	247
	.byte	1,6,2,35,2,13
	.byte	'reserved_18',0,1
	.word	247
	.byte	1,5,2,35,2,13
	.byte	'TRSTL',0,1
	.word	247
	.byte	1,4,2,35,2,13
	.byte	'SPDEN',0,1
	.word	247
	.byte	1,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	247
	.byte	3,0,2,35,2,13
	.byte	'RAMINT',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'reserved_25',0,1
	.word	247
	.byte	7,0,2,35,3,0,10
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,198,7,3
	.word	41697
	.byte	12
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,201,7,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'SWRSTREQ',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,4
	.word	9475
	.byte	30,0,2,35,2,0,10
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,206,7,3
	.word	41957
	.byte	12
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,209,7,16,4,13
	.byte	'CCTRIG0',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'RAMINTM',0,1
	.word	247
	.byte	2,4,2,35,0,13
	.byte	'SETLUDIS',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'reserved_5',0,1
	.word	247
	.byte	3,0,2,35,0,13
	.byte	'DATM',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'reserved_9',0,4
	.word	9475
	.byte	23,0,2,35,2,0,10
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,218,7,3
	.word	42082
	.byte	12
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,221,7,16,4,13
	.byte	'ESR0T',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	9475
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,228,7,3
	.word	42279
	.byte	12
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,231,7,16,4,13
	.byte	'ESR0T',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	9475
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,238,7,3
	.word	42432
	.byte	12
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,241,7,16,4,13
	.byte	'ESR0T',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	9475
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,248,7,3
	.word	42585
	.byte	12
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,251,7,16,4,13
	.byte	'ESR0T',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'ESR1T',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'reserved_2',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'SMUT',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,4
	.word	9475
	.byte	28,0,2,35,2,0,10
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,130,8,3
	.word	42738
	.byte	12
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,133,8,16,4,13
	.byte	'ENDINIT',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'LCK',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'PW',0,4
	.word	497
	.byte	14,16,2,35,0,13
	.byte	'REL',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,139,8,3
	.word	42893
	.byte	12
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,142,8,16,4,13
	.byte	'reserved_0',0,1
	.word	247
	.byte	2,6,2,35,0,13
	.byte	'IR0',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'DR',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'IR1',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'UR',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'PAR',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'TCR',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'TCTR',0,1
	.word	247
	.byte	7,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,154,8,3
	.word	43023
	.byte	12
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,157,8,16,4,13
	.byte	'AE',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'OE',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'IS0',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'DS',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'TO',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'IS1',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'US',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'PAS',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'TCS',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'TCT',0,1
	.word	247
	.byte	7,0,2,35,1,13
	.byte	'TIM',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,170,8,3
	.word	43261
	.byte	12
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,173,8,16,4,13
	.byte	'ENDINIT',0,4
	.word	497
	.byte	1,31,2,35,0,13
	.byte	'LCK',0,4
	.word	497
	.byte	1,30,2,35,0,13
	.byte	'PW',0,4
	.word	497
	.byte	14,16,2,35,0,13
	.byte	'REL',0,4
	.word	497
	.byte	16,0,2,35,0,0,10
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,179,8,3
	.word	43484
	.byte	12
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,182,8,16,4,13
	.byte	'CLRIRF',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'reserved_1',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'IR0',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'DR',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'reserved_4',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'IR1',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'UR',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'PAR',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'TCR',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'TCTR',0,1
	.word	247
	.byte	7,0,2,35,1,13
	.byte	'reserved_16',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,195,8,3
	.word	43610
	.byte	12
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,198,8,16,4,13
	.byte	'AE',0,1
	.word	247
	.byte	1,7,2,35,0,13
	.byte	'OE',0,1
	.word	247
	.byte	1,6,2,35,0,13
	.byte	'IS0',0,1
	.word	247
	.byte	1,5,2,35,0,13
	.byte	'DS',0,1
	.word	247
	.byte	1,4,2,35,0,13
	.byte	'TO',0,1
	.word	247
	.byte	1,3,2,35,0,13
	.byte	'IS1',0,1
	.word	247
	.byte	1,2,2,35,0,13
	.byte	'US',0,1
	.word	247
	.byte	1,1,2,35,0,13
	.byte	'PAS',0,1
	.word	247
	.byte	1,0,2,35,0,13
	.byte	'TCS',0,1
	.word	247
	.byte	1,7,2,35,1,13
	.byte	'TCT',0,1
	.word	247
	.byte	7,0,2,35,1,13
	.byte	'TIM',0,2
	.word	339
	.byte	16,0,2,35,2,0,10
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,211,8,3
	.word	43862
	.byte	14,6,219,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	27157
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ACCEN0',0,6,224,8,3
	.word	44081
	.byte	14,6,227,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	27714
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ACCEN1',0,6,232,8,3
	.word	44145
	.byte	14,6,235,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	27791
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ARSTDIS',0,6,240,8,3
	.word	44209
	.byte	14,6,243,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	27927
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON0',0,6,248,8,3
	.word	44274
	.byte	14,6,251,8,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28209
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON1',0,6,128,9,3
	.word	44339
	.byte	14,6,131,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28447
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON2',0,6,136,9,3
	.word	44404
	.byte	14,6,139,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28575
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON3',0,6,144,9,3
	.word	44469
	.byte	14,6,147,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	28802
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON4',0,6,152,9,3
	.word	44534
	.byte	14,6,155,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29021
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON5',0,6,160,9,3
	.word	44599
	.byte	14,6,163,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29149
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CCUCON6',0,6,168,9,3
	.word	44664
	.byte	14,6,171,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29249
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_CHIPID',0,6,176,9,3
	.word	44729
	.byte	14,6,179,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29457
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_DTSCON',0,6,184,9,3
	.word	44793
	.byte	14,6,187,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29622
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_DTSLIM',0,6,192,9,3
	.word	44857
	.byte	14,6,195,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29805
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_DTSSTAT',0,6,200,9,3
	.word	44921
	.byte	14,6,203,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	29959
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EICR',0,6,208,9,3
	.word	44986
	.byte	14,6,211,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30323
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EIFR',0,6,216,9,3
	.word	45048
	.byte	14,6,219,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30534
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EMSR',0,6,224,9,3
	.word	45110
	.byte	14,6,227,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30786
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ESRCFG',0,6,232,9,3
	.word	45172
	.byte	14,6,235,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	30904
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ESROCFG',0,6,240,9,3
	.word	45236
	.byte	14,6,243,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31015
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVR13CON',0,6,248,9,3
	.word	45301
	.byte	14,6,251,9,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31178
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,128,10,3
	.word	45367
	.byte	14,6,131,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31340
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,136,10,3
	.word	45435
	.byte	14,6,139,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31618
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVROVMON',0,6,144,10,3
	.word	45503
	.byte	14,6,147,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31797
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRRSTCON',0,6,152,10,3
	.word	45569
	.byte	14,6,155,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	31957
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,160,10,3
	.word	45636
	.byte	14,6,163,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	32118
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,168,10,3
	.word	45705
	.byte	14,6,171,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	32310
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,176,10,3
	.word	45773
	.byte	14,6,179,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	32606
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,184,10,3
	.word	45841
	.byte	14,6,187,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	32821
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRSTAT',0,6,192,10,3
	.word	45909
	.byte	14,6,195,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33110
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EVRUVMON',0,6,200,10,3
	.word	45974
	.byte	14,6,203,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33289
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_EXTCON',0,6,208,10,3
	.word	46040
	.byte	14,6,211,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33507
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_FDR',0,6,216,10,3
	.word	46104
	.byte	14,6,219,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	33670
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_FMR',0,6,224,10,3
	.word	46165
	.byte	14,6,227,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34006
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_ID',0,6,232,10,3
	.word	46226
	.byte	14,6,235,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34113
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_IGCR',0,6,240,10,3
	.word	46286
	.byte	14,6,243,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34565
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_IN',0,6,248,10,3
	.word	46348
	.byte	14,6,251,10,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34664
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_IOCR',0,6,128,11,3
	.word	46408
	.byte	14,6,131,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34814
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,136,11,3
	.word	46470
	.byte	14,6,139,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	34963
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,144,11,3
	.word	46538
	.byte	14,6,147,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35124
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,152,11,3
	.word	46606
	.byte	14,6,155,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35254
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LCLCON0',0,6,160,11,3
	.word	46674
	.byte	14,6,163,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35388
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_LCLTEST',0,6,168,11,3
	.word	46739
	.byte	14,6,171,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35503
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_MANID',0,6,176,11,3
	.word	46804
	.byte	14,6,179,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35614
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OMR',0,6,184,11,3
	.word	46867
	.byte	14,6,187,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	35772
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OSCCON',0,6,192,11,3
	.word	46928
	.byte	14,6,195,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36112
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OUT',0,6,200,11,3
	.word	46992
	.byte	14,6,203,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36213
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OVCCON',0,6,208,11,3
	.word	47053
	.byte	14,6,211,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36480
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_OVCENABLE',0,6,216,11,3
	.word	47117
	.byte	14,6,219,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36616
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PDISC',0,6,224,11,3
	.word	47184
	.byte	14,6,227,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36727
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PDR',0,6,232,11,3
	.word	47247
	.byte	14,6,235,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	36860
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PDRR',0,6,240,11,3
	.word	47308
	.byte	14,6,243,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	37063
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLCON0',0,6,248,11,3
	.word	47370
	.byte	14,6,251,11,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	37419
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLCON1',0,6,128,12,3
	.word	47435
	.byte	14,6,131,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	37597
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLCON2',0,6,136,12,3
	.word	47500
	.byte	14,6,139,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	37697
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,144,12,3
	.word	47565
	.byte	14,6,147,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38067
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,152,12,3
	.word	47634
	.byte	14,6,155,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38253
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,160,12,3
	.word	47703
	.byte	14,6,163,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38451
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PLLSTAT',0,6,168,12,3
	.word	47772
	.byte	14,6,171,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38684
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMCSR',0,6,176,12,3
	.word	47837
	.byte	14,6,179,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	38836
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWCR0',0,6,184,12,3
	.word	47900
	.byte	14,6,187,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	39395
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWCR1',0,6,192,12,3
	.word	47965
	.byte	14,6,195,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	39578
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWCR3',0,6,200,12,3
	.word	48030
	.byte	14,6,203,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	39747
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWSTAT',0,6,208,12,3
	.word	48095
	.byte	14,6,211,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	40314
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,216,12,3
	.word	48161
	.byte	14,6,219,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	40630
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_PMSWUTCNT',0,6,224,12,3
	.word	48230
	.byte	14,6,227,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	40958
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_RSTCON',0,6,232,12,3
	.word	48297
	.byte	14,6,235,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	40749
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_RSTCON2',0,6,240,12,3
	.word	48361
	.byte	14,6,243,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	41169
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_RSTSTAT',0,6,248,12,3
	.word	48426
	.byte	14,6,251,12,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	41601
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_SAFECON',0,6,128,13,3
	.word	48491
	.byte	14,6,131,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	41697
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_STSTAT',0,6,136,13,3
	.word	48556
	.byte	14,6,139,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	41957
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_SWRSTCON',0,6,144,13,3
	.word	48620
	.byte	14,6,147,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42082
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_SYSCON',0,6,152,13,3
	.word	48686
	.byte	14,6,155,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42279
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_TRAPCLR',0,6,160,13,3
	.word	48750
	.byte	14,6,163,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42432
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_TRAPDIS',0,6,168,13,3
	.word	48815
	.byte	14,6,171,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42585
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_TRAPSET',0,6,176,13,3
	.word	48880
	.byte	14,6,179,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42738
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_TRAPSTAT',0,6,184,13,3
	.word	48945
	.byte	14,6,187,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	42893
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,192,13,3
	.word	49011
	.byte	14,6,195,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43023
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,200,13,3
	.word	49080
	.byte	14,6,203,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43261
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,208,13,3
	.word	49149
	.byte	14,6,211,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43484
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTS_CON0',0,6,216,13,3
	.word	49216
	.byte	14,6,219,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43610
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTS_CON1',0,6,224,13,3
	.word	49283
	.byte	14,6,227,13,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	43862
	.byte	2,35,0,0,10
	.byte	'Ifx_SCU_WDTS_SR',0,6,232,13,3
	.word	49350
	.byte	12
	.byte	'_Ifx_SCU_WDTCPU',0,6,243,13,25,12,15
	.byte	'CON0',0,4
	.word	49011
	.byte	2,35,0,15
	.byte	'CON1',0,4
	.word	49080
	.byte	2,35,4,15
	.byte	'SR',0,4
	.word	49149
	.byte	2,35,8,0,3
	.word	49415
	.byte	10
	.byte	'Ifx_SCU_WDTCPU',0,6,248,13,3
	.word	49478
	.byte	12
	.byte	'_Ifx_SCU_WDTS',0,6,251,13,25,12,15
	.byte	'CON0',0,4
	.word	49216
	.byte	2,35,0,15
	.byte	'CON1',0,4
	.word	49283
	.byte	2,35,4,15
	.byte	'SR',0,4
	.word	49350
	.byte	2,35,8,0,3
	.word	49507
	.byte	10
	.byte	'Ifx_SCU_WDTS',0,6,128,14,3
	.word	49568
	.byte	12
	.byte	'_Ifx_SRC_SRCR_Bits',0,7,45,16,4,13
	.byte	'SRPN',0,1
	.word	247
	.byte	8,0,2,35,0,13
	.byte	'reserved_8',0,1
	.word	247
	.byte	2,6,2,35,1,13
	.byte	'SRE',0,1
	.word	247
	.byte	1,5,2,35,1,13
	.byte	'TOS',0,1
	.word	247
	.byte	1,4,2,35,1,13
	.byte	'reserved_12',0,1
	.word	247
	.byte	4,0,2,35,1,13
	.byte	'ECC',0,1
	.word	247
	.byte	5,3,2,35,2,13
	.byte	'reserved_21',0,1
	.word	247
	.byte	3,0,2,35,2,13
	.byte	'SRR',0,1
	.word	247
	.byte	1,7,2,35,3,13
	.byte	'CLRR',0,1
	.word	247
	.byte	1,6,2,35,3,13
	.byte	'SETR',0,1
	.word	247
	.byte	1,5,2,35,3,13
	.byte	'IOV',0,1
	.word	247
	.byte	1,4,2,35,3,13
	.byte	'IOVCLR',0,1
	.word	247
	.byte	1,3,2,35,3,13
	.byte	'SWS',0,1
	.word	247
	.byte	1,2,2,35,3,13
	.byte	'SWSCLR',0,1
	.word	247
	.byte	1,1,2,35,3,13
	.byte	'reserved_31',0,1
	.word	247
	.byte	1,0,2,35,3,0,10
	.byte	'Ifx_SRC_SRCR_Bits',0,7,62,3
	.word	49595
	.byte	14,7,70,9,4,15
	.byte	'U',0,4
	.word	9475
	.byte	2,35,0,15
	.byte	'I',0,4
	.word	171
	.byte	2,35,0,15
	.byte	'B',0,4
	.word	49595
	.byte	2,35,0,0,10
	.byte	'Ifx_SRC_SRCR',0,7,75,3
	.word	49911
	.byte	12
	.byte	'_Ifx_SRC_ASCLIN',0,7,86,25,12,15
	.byte	'TX',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'RX',0,4
	.word	49911
	.byte	2,35,4,15
	.byte	'ERR',0,4
	.word	49911
	.byte	2,35,8,0,3
	.word	49971
	.byte	10
	.byte	'Ifx_SRC_ASCLIN',0,7,91,3
	.word	50030
	.byte	12
	.byte	'_Ifx_SRC_BCUSPB',0,7,94,25,4,15
	.byte	'SBSRC',0,4
	.word	49911
	.byte	2,35,0,0,3
	.word	50058
	.byte	10
	.byte	'Ifx_SRC_BCUSPB',0,7,97,3
	.word	50095
	.byte	12
	.byte	'_Ifx_SRC_CAN',0,7,100,25,64,16,64
	.word	49911
	.byte	17,15,0,15
	.byte	'INT',0,64
	.word	50141
	.byte	2,35,0,0,3
	.word	50123
	.byte	10
	.byte	'Ifx_SRC_CAN',0,7,103,3
	.word	50164
	.byte	12
	.byte	'_Ifx_SRC_CAN1',0,7,106,25,32,16,32
	.word	49911
	.byte	17,7,0,15
	.byte	'INT',0,32
	.word	50208
	.byte	2,35,0,0,3
	.word	50189
	.byte	10
	.byte	'Ifx_SRC_CAN1',0,7,109,3
	.word	50231
	.byte	12
	.byte	'_Ifx_SRC_CCU6',0,7,112,25,16,15
	.byte	'SR0',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	49911
	.byte	2,35,4,15
	.byte	'SR2',0,4
	.word	49911
	.byte	2,35,8,15
	.byte	'SR3',0,4
	.word	49911
	.byte	2,35,12,0,3
	.word	50257
	.byte	10
	.byte	'Ifx_SRC_CCU6',0,7,118,3
	.word	50329
	.byte	12
	.byte	'_Ifx_SRC_CERBERUS',0,7,121,25,8,16,8
	.word	49911
	.byte	17,1,0,15
	.byte	'SR',0,8
	.word	50378
	.byte	2,35,0,0,3
	.word	50355
	.byte	10
	.byte	'Ifx_SRC_CERBERUS',0,7,124,3
	.word	50400
	.byte	12
	.byte	'_Ifx_SRC_CPU',0,7,127,25,32,15
	.byte	'SBSRC',0,4
	.word	49911
	.byte	2,35,0,16,28
	.word	247
	.byte	17,27,0,15
	.byte	'reserved_4',0,28
	.word	50463
	.byte	2,35,4,0,3
	.word	50430
	.byte	10
	.byte	'Ifx_SRC_CPU',0,7,131,1,3
	.word	50493
	.byte	12
	.byte	'_Ifx_SRC_DMA',0,7,134,1,25,80,15
	.byte	'ERR',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'reserved_4',0,12
	.word	26556
	.byte	2,35,4,15
	.byte	'CH',0,64
	.word	50141
	.byte	2,35,16,0,3
	.word	50519
	.byte	10
	.byte	'Ifx_SRC_DMA',0,7,139,1,3
	.word	50584
	.byte	12
	.byte	'_Ifx_SRC_EMEM',0,7,142,1,25,4,15
	.byte	'SR',0,4
	.word	49911
	.byte	2,35,0,0,3
	.word	50610
	.byte	10
	.byte	'Ifx_SRC_EMEM',0,7,145,1,3
	.word	50643
	.byte	12
	.byte	'_Ifx_SRC_ERAY',0,7,148,1,25,80,15
	.byte	'INT',0,8
	.word	50378
	.byte	2,35,0,15
	.byte	'TINT',0,8
	.word	50378
	.byte	2,35,8,15
	.byte	'NDAT',0,8
	.word	50378
	.byte	2,35,16,15
	.byte	'MBSC',0,8
	.word	50378
	.byte	2,35,24,15
	.byte	'OBUSY',0,4
	.word	49911
	.byte	2,35,32,15
	.byte	'IBUSY',0,4
	.word	49911
	.byte	2,35,36,16,40
	.word	247
	.byte	17,39,0,15
	.byte	'reserved_28',0,40
	.word	50775
	.byte	2,35,40,0,3
	.word	50670
	.byte	10
	.byte	'Ifx_SRC_ERAY',0,7,157,1,3
	.word	50806
	.byte	12
	.byte	'_Ifx_SRC_ETH',0,7,160,1,25,4,15
	.byte	'SR',0,4
	.word	49911
	.byte	2,35,0,0,3
	.word	50833
	.byte	10
	.byte	'Ifx_SRC_ETH',0,7,163,1,3
	.word	50865
	.byte	12
	.byte	'_Ifx_SRC_EVR',0,7,166,1,25,8,15
	.byte	'WUT',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'SCDC',0,4
	.word	49911
	.byte	2,35,4,0,3
	.word	50891
	.byte	10
	.byte	'Ifx_SRC_EVR',0,7,170,1,3
	.word	50938
	.byte	12
	.byte	'_Ifx_SRC_FFT',0,7,173,1,25,12,15
	.byte	'DONE',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'ERR',0,4
	.word	49911
	.byte	2,35,4,15
	.byte	'RFS',0,4
	.word	49911
	.byte	2,35,8,0,3
	.word	50964
	.byte	10
	.byte	'Ifx_SRC_FFT',0,7,178,1,3
	.word	51024
	.byte	12
	.byte	'_Ifx_SRC_GPSR',0,7,181,1,25,128,12,15
	.byte	'SR0',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	49911
	.byte	2,35,4,15
	.byte	'SR2',0,4
	.word	49911
	.byte	2,35,8,15
	.byte	'SR3',0,4
	.word	49911
	.byte	2,35,12,16,240,11
	.word	247
	.byte	17,239,11,0,15
	.byte	'reserved_10',0,240,11
	.word	51123
	.byte	2,35,16,0,3
	.word	51050
	.byte	10
	.byte	'Ifx_SRC_GPSR',0,7,188,1,3
	.word	51157
	.byte	12
	.byte	'_Ifx_SRC_GPT12',0,7,191,1,25,48,15
	.byte	'CIRQ',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'T2',0,4
	.word	49911
	.byte	2,35,4,15
	.byte	'T3',0,4
	.word	49911
	.byte	2,35,8,15
	.byte	'T4',0,4
	.word	49911
	.byte	2,35,12,15
	.byte	'T5',0,4
	.word	49911
	.byte	2,35,16,15
	.byte	'T6',0,4
	.word	49911
	.byte	2,35,20,16,24
	.word	247
	.byte	17,23,0,15
	.byte	'reserved_18',0,24
	.word	51279
	.byte	2,35,24,0,3
	.word	51184
	.byte	10
	.byte	'Ifx_SRC_GPT12',0,7,200,1,3
	.word	51310
	.byte	12
	.byte	'_Ifx_SRC_GTM',0,7,203,1,25,192,11,15
	.byte	'AEIIRQ',0,4
	.word	49911
	.byte	2,35,0,16,236,2
	.word	247
	.byte	17,235,2,0,15
	.byte	'reserved_4',0,236,2
	.word	51374
	.byte	2,35,4,15
	.byte	'ERR',0,4
	.word	49911
	.byte	3,35,240,2,15
	.byte	'reserved_174',0,12
	.word	26556
	.byte	3,35,244,2,16,32
	.word	50208
	.byte	17,0,0,15
	.byte	'TIM',0,32
	.word	51443
	.byte	3,35,128,3,16,224,7
	.word	247
	.byte	17,223,7,0,15
	.byte	'reserved_1A0',0,224,7
	.word	51466
	.byte	3,35,160,3,16,64
	.word	50208
	.byte	17,1,0,15
	.byte	'TOM',0,64
	.word	51501
	.byte	3,35,128,11,0,3
	.word	51338
	.byte	10
	.byte	'Ifx_SRC_GTM',0,7,212,1,3
	.word	51525
	.byte	12
	.byte	'_Ifx_SRC_HSM',0,7,215,1,25,8,15
	.byte	'HSM',0,8
	.word	50378
	.byte	2,35,0,0,3
	.word	51551
	.byte	10
	.byte	'Ifx_SRC_HSM',0,7,218,1,3
	.word	51584
	.byte	12
	.byte	'_Ifx_SRC_LMU',0,7,221,1,25,4,15
	.byte	'SR',0,4
	.word	49911
	.byte	2,35,0,0,3
	.word	51610
	.byte	10
	.byte	'Ifx_SRC_LMU',0,7,224,1,3
	.word	51642
	.byte	12
	.byte	'_Ifx_SRC_PMU',0,7,227,1,25,4,15
	.byte	'SR',0,4
	.word	49911
	.byte	2,35,0,0,3
	.word	51668
	.byte	10
	.byte	'Ifx_SRC_PMU',0,7,230,1,3
	.word	51700
	.byte	12
	.byte	'_Ifx_SRC_QSPI',0,7,233,1,25,24,15
	.byte	'TX',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'RX',0,4
	.word	49911
	.byte	2,35,4,15
	.byte	'ERR',0,4
	.word	49911
	.byte	2,35,8,15
	.byte	'PT',0,4
	.word	49911
	.byte	2,35,12,15
	.byte	'HC',0,4
	.word	49911
	.byte	2,35,16,15
	.byte	'U',0,4
	.word	49911
	.byte	2,35,20,0,3
	.word	51726
	.byte	10
	.byte	'Ifx_SRC_QSPI',0,7,241,1,3
	.word	51819
	.byte	12
	.byte	'_Ifx_SRC_SCU',0,7,244,1,25,20,15
	.byte	'DTS',0,4
	.word	49911
	.byte	2,35,0,16,16
	.word	49911
	.byte	17,3,0,15
	.byte	'ERU',0,16
	.word	51878
	.byte	2,35,4,0,3
	.word	51846
	.byte	10
	.byte	'Ifx_SRC_SCU',0,7,248,1,3
	.word	51901
	.byte	12
	.byte	'_Ifx_SRC_SENT',0,7,251,1,25,16,15
	.byte	'SR',0,16
	.word	51878
	.byte	2,35,0,0,3
	.word	51927
	.byte	10
	.byte	'Ifx_SRC_SENT',0,7,254,1,3
	.word	51960
	.byte	12
	.byte	'_Ifx_SRC_SMU',0,7,129,2,25,12,16,12
	.word	49911
	.byte	17,2,0,15
	.byte	'SR',0,12
	.word	52006
	.byte	2,35,0,0,3
	.word	51987
	.byte	10
	.byte	'Ifx_SRC_SMU',0,7,132,2,3
	.word	52028
	.byte	12
	.byte	'_Ifx_SRC_STM',0,7,135,2,25,96,15
	.byte	'SR0',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	49911
	.byte	2,35,4,16,88
	.word	247
	.byte	17,87,0,15
	.byte	'reserved_8',0,88
	.word	52099
	.byte	2,35,8,0,3
	.word	52054
	.byte	10
	.byte	'Ifx_SRC_STM',0,7,140,2,3
	.word	52129
	.byte	12
	.byte	'_Ifx_SRC_VADCCG',0,7,143,2,25,192,2,15
	.byte	'SR0',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	49911
	.byte	2,35,4,15
	.byte	'SR2',0,4
	.word	49911
	.byte	2,35,8,15
	.byte	'SR3',0,4
	.word	49911
	.byte	2,35,12,16,176,2
	.word	247
	.byte	17,175,2,0,15
	.byte	'reserved_10',0,176,2
	.word	52230
	.byte	2,35,16,0,3
	.word	52155
	.byte	10
	.byte	'Ifx_SRC_VADCCG',0,7,150,2,3
	.word	52264
	.byte	12
	.byte	'_Ifx_SRC_VADCG',0,7,153,2,25,16,15
	.byte	'SR0',0,4
	.word	49911
	.byte	2,35,0,15
	.byte	'SR1',0,4
	.word	49911
	.byte	2,35,4,15
	.byte	'SR2',0,4
	.word	49911
	.byte	2,35,8,15
	.byte	'SR3',0,4
	.word	49911
	.byte	2,35,12,0,3
	.word	52293
	.byte	10
	.byte	'Ifx_SRC_VADCG',0,7,159,2,3
	.word	52367
	.byte	12
	.byte	'_Ifx_SRC_XBAR',0,7,162,2,25,4,15
	.byte	'SRC',0,4
	.word	49911
	.byte	2,35,0,0,3
	.word	52395
	.byte	10
	.byte	'Ifx_SRC_XBAR',0,7,165,2,3
	.word	52429
	.byte	12
	.byte	'_Ifx_SRC_GASCLIN',0,7,178,2,25,24,16,24
	.word	49971
	.byte	17,1,0,3
	.word	52479
	.byte	15
	.byte	'ASCLIN',0,24
	.word	52488
	.byte	2,35,0,0,3
	.word	52456
	.byte	10
	.byte	'Ifx_SRC_GASCLIN',0,7,181,2,3
	.word	52510
	.byte	12
	.byte	'_Ifx_SRC_GBCU',0,7,184,2,25,4,3
	.word	50058
	.byte	15
	.byte	'SPB',0,4
	.word	52560
	.byte	2,35,0,0,3
	.word	52540
	.byte	10
	.byte	'Ifx_SRC_GBCU',0,7,187,2,3
	.word	52579
	.byte	12
	.byte	'_Ifx_SRC_GCAN',0,7,190,2,25,96,16,64
	.word	50123
	.byte	17,0,0,3
	.word	52626
	.byte	15
	.byte	'CAN',0,64
	.word	52635
	.byte	2,35,0,16,32
	.word	50189
	.byte	17,0,0,3
	.word	52653
	.byte	15
	.byte	'CAN1',0,32
	.word	52662
	.byte	2,35,64,0,3
	.word	52606
	.byte	10
	.byte	'Ifx_SRC_GCAN',0,7,194,2,3
	.word	52682
	.byte	12
	.byte	'_Ifx_SRC_GCCU6',0,7,197,2,25,32,16,32
	.word	50257
	.byte	17,1,0,3
	.word	52730
	.byte	15
	.byte	'CCU6',0,32
	.word	52739
	.byte	2,35,0,0,3
	.word	52709
	.byte	10
	.byte	'Ifx_SRC_GCCU6',0,7,200,2,3
	.word	52759
	.byte	12
	.byte	'_Ifx_SRC_GCERBERUS',0,7,203,2,25,8,3
	.word	50355
	.byte	15
	.byte	'CERBERUS',0,8
	.word	52812
	.byte	2,35,0,0,3
	.word	52787
	.byte	10
	.byte	'Ifx_SRC_GCERBERUS',0,7,206,2,3
	.word	52836
	.byte	12
	.byte	'_Ifx_SRC_GCPU',0,7,209,2,25,32,16,32
	.word	50430
	.byte	17,0,0,3
	.word	52888
	.byte	15
	.byte	'CPU',0,32
	.word	52897
	.byte	2,35,0,0,3
	.word	52868
	.byte	10
	.byte	'Ifx_SRC_GCPU',0,7,212,2,3
	.word	52916
	.byte	12
	.byte	'_Ifx_SRC_GDMA',0,7,215,2,25,80,16,80
	.word	50519
	.byte	17,0,0,3
	.word	52963
	.byte	15
	.byte	'DMA',0,80
	.word	52972
	.byte	2,35,0,0,3
	.word	52943
	.byte	10
	.byte	'Ifx_SRC_GDMA',0,7,218,2,3
	.word	52991
	.byte	12
	.byte	'_Ifx_SRC_GEMEM',0,7,221,2,25,4,16,4
	.word	50610
	.byte	17,0,0,3
	.word	53039
	.byte	15
	.byte	'EMEM',0,4
	.word	53048
	.byte	2,35,0,0,3
	.word	53018
	.byte	10
	.byte	'Ifx_SRC_GEMEM',0,7,224,2,3
	.word	53068
	.byte	12
	.byte	'_Ifx_SRC_GERAY',0,7,227,2,25,80,16,80
	.word	50670
	.byte	17,0,0,3
	.word	53117
	.byte	15
	.byte	'ERAY',0,80
	.word	53126
	.byte	2,35,0,0,3
	.word	53096
	.byte	10
	.byte	'Ifx_SRC_GERAY',0,7,230,2,3
	.word	53146
	.byte	12
	.byte	'_Ifx_SRC_GETH',0,7,233,2,25,4,16,4
	.word	50833
	.byte	17,0,0,3
	.word	53194
	.byte	15
	.byte	'ETH',0,4
	.word	53203
	.byte	2,35,0,0,3
	.word	53174
	.byte	10
	.byte	'Ifx_SRC_GETH',0,7,236,2,3
	.word	53222
	.byte	12
	.byte	'_Ifx_SRC_GEVR',0,7,239,2,25,8,16,8
	.word	50891
	.byte	17,0,0,3
	.word	53269
	.byte	15
	.byte	'EVR',0,8
	.word	53278
	.byte	2,35,0,0,3
	.word	53249
	.byte	10
	.byte	'Ifx_SRC_GEVR',0,7,242,2,3
	.word	53297
	.byte	12
	.byte	'_Ifx_SRC_GFFT',0,7,245,2,25,12,16,12
	.word	50964
	.byte	17,0,0,3
	.word	53344
	.byte	15
	.byte	'FFT',0,12
	.word	53353
	.byte	2,35,0,0,3
	.word	53324
	.byte	10
	.byte	'Ifx_SRC_GFFT',0,7,248,2,3
	.word	53372
	.byte	12
	.byte	'_Ifx_SRC_GGPSR',0,7,251,2,25,128,12,16,128,12
	.word	51050
	.byte	17,0,0,3
	.word	53421
	.byte	15
	.byte	'GPSR',0,128,12
	.word	53431
	.byte	2,35,0,0,3
	.word	53399
	.byte	10
	.byte	'Ifx_SRC_GGPSR',0,7,254,2,3
	.word	53452
	.byte	12
	.byte	'_Ifx_SRC_GGPT12',0,7,129,3,25,48,16,48
	.word	51184
	.byte	17,0,0,3
	.word	53502
	.byte	15
	.byte	'GPT12',0,48
	.word	53511
	.byte	2,35,0,0,3
	.word	53480
	.byte	10
	.byte	'Ifx_SRC_GGPT12',0,7,132,3,3
	.word	53532
	.byte	12
	.byte	'_Ifx_SRC_GGTM',0,7,135,3,25,192,11,16,192,11
	.word	51338
	.byte	17,0,0,3
	.word	53582
	.byte	15
	.byte	'GTM',0,192,11
	.word	53592
	.byte	2,35,0,0,3
	.word	53561
	.byte	10
	.byte	'Ifx_SRC_GGTM',0,7,138,3,3
	.word	53612
	.byte	12
	.byte	'_Ifx_SRC_GHSM',0,7,141,3,25,8,16,8
	.word	51551
	.byte	17,0,0,3
	.word	53659
	.byte	15
	.byte	'HSM',0,8
	.word	53668
	.byte	2,35,0,0,3
	.word	53639
	.byte	10
	.byte	'Ifx_SRC_GHSM',0,7,144,3,3
	.word	53687
	.byte	12
	.byte	'_Ifx_SRC_GLMU',0,7,147,3,25,4,16,4
	.word	51610
	.byte	17,0,0,3
	.word	53734
	.byte	15
	.byte	'LMU',0,4
	.word	53743
	.byte	2,35,0,0,3
	.word	53714
	.byte	10
	.byte	'Ifx_SRC_GLMU',0,7,150,3,3
	.word	53762
	.byte	12
	.byte	'_Ifx_SRC_GPMU',0,7,153,3,25,8,16,8
	.word	51668
	.byte	17,1,0,3
	.word	53809
	.byte	15
	.byte	'PMU',0,8
	.word	53818
	.byte	2,35,0,0,3
	.word	53789
	.byte	10
	.byte	'Ifx_SRC_GPMU',0,7,156,3,3
	.word	53837
	.byte	12
	.byte	'_Ifx_SRC_GQSPI',0,7,159,3,25,96,16,96
	.word	51726
	.byte	17,3,0,3
	.word	53885
	.byte	15
	.byte	'QSPI',0,96
	.word	53894
	.byte	2,35,0,0,3
	.word	53864
	.byte	10
	.byte	'Ifx_SRC_GQSPI',0,7,162,3,3
	.word	53914
	.byte	12
	.byte	'_Ifx_SRC_GSCU',0,7,165,3,25,20,3
	.word	51846
	.byte	15
	.byte	'SCU',0,20
	.word	53962
	.byte	2,35,0,0,3
	.word	53942
	.byte	10
	.byte	'Ifx_SRC_GSCU',0,7,168,3,3
	.word	53981
	.byte	12
	.byte	'_Ifx_SRC_GSENT',0,7,171,3,25,16,16,16
	.word	51927
	.byte	17,0,0,3
	.word	54029
	.byte	15
	.byte	'SENT',0,16
	.word	54038
	.byte	2,35,0,0,3
	.word	54008
	.byte	10
	.byte	'Ifx_SRC_GSENT',0,7,174,3,3
	.word	54058
	.byte	12
	.byte	'_Ifx_SRC_GSMU',0,7,177,3,25,12,16,12
	.word	51987
	.byte	17,0,0,3
	.word	54106
	.byte	15
	.byte	'SMU',0,12
	.word	54115
	.byte	2,35,0,0,3
	.word	54086
	.byte	10
	.byte	'Ifx_SRC_GSMU',0,7,180,3,3
	.word	54134
	.byte	12
	.byte	'_Ifx_SRC_GSTM',0,7,183,3,25,96,16,96
	.word	52054
	.byte	17,0,0,3
	.word	54181
	.byte	15
	.byte	'STM',0,96
	.word	54190
	.byte	2,35,0,0,3
	.word	54161
	.byte	10
	.byte	'Ifx_SRC_GSTM',0,7,186,3,3
	.word	54209
	.byte	12
	.byte	'_Ifx_SRC_GVADC',0,7,189,3,25,224,4,16,64
	.word	52293
	.byte	17,3,0,3
	.word	54258
	.byte	15
	.byte	'G',0,64
	.word	54267
	.byte	2,35,0,16,224,1
	.word	247
	.byte	17,223,1,0,15
	.byte	'reserved_40',0,224,1
	.word	54283
	.byte	2,35,64,16,192,2
	.word	52155
	.byte	17,0,0,3
	.word	54316
	.byte	15
	.byte	'CG',0,192,2
	.word	54326
	.byte	3,35,160,2,0,3
	.word	54236
	.byte	10
	.byte	'Ifx_SRC_GVADC',0,7,194,3,3
	.word	54346
	.byte	12
	.byte	'_Ifx_SRC_GXBAR',0,7,197,3,25,4,3
	.word	52395
	.byte	15
	.byte	'XBAR',0,4
	.word	54395
	.byte	2,35,0,0,3
	.word	54374
	.byte	10
	.byte	'Ifx_SRC_GXBAR',0,7,200,3,3
	.word	54415
	.byte	10
	.byte	'Dma_StatusType',0,8,121,22
	.word	9475
	.byte	10
	.byte	'Dma_ErrorStatusType',0,8,141,1,22
	.word	9475
	.byte	18,8,147,1,9,1,19
	.byte	'DMA_CHANNEL0',0,0,19
	.byte	'DMA_CHANNEL1',0,1,19
	.byte	'DMA_CHANNEL2',0,2,19
	.byte	'DMA_CHANNEL3',0,3,19
	.byte	'DMA_CHANNEL4',0,4,19
	.byte	'DMA_CHANNEL5',0,5,19
	.byte	'DMA_CHANNEL6',0,6,19
	.byte	'DMA_CHANNEL7',0,7,19
	.byte	'DMA_CHANNEL8',0,8,19
	.byte	'DMA_CHANNEL9',0,9,19
	.byte	'DMA_CHANNEL10',0,10,19
	.byte	'DMA_CHANNEL11',0,11,19
	.byte	'DMA_CHANNEL12',0,12,19
	.byte	'DMA_CHANNEL13',0,13,19
	.byte	'DMA_CHANNEL14',0,14,19
	.byte	'DMA_CHANNEL15',0,15,19
	.byte	'DMA_CHANNEL16',0,16,19
	.byte	'DMA_CHANNEL17',0,17,19
	.byte	'DMA_CHANNEL18',0,18,19
	.byte	'DMA_CHANNEL19',0,19,19
	.byte	'DMA_CHANNEL20',0,20,19
	.byte	'DMA_CHANNEL21',0,21,19
	.byte	'DMA_CHANNEL22',0,22,19
	.byte	'DMA_CHANNEL23',0,23,19
	.byte	'DMA_CHANNEL24',0,24,19
	.byte	'DMA_CHANNEL25',0,25,19
	.byte	'DMA_CHANNEL26',0,26,19
	.byte	'DMA_CHANNEL27',0,27,19
	.byte	'DMA_CHANNEL28',0,28,19
	.byte	'DMA_CHANNEL29',0,29,19
	.byte	'DMA_CHANNEL30',0,30,19
	.byte	'DMA_CHANNEL31',0,31,19
	.byte	'DMA_CHANNEL32',0,32,19
	.byte	'DMA_CHANNEL33',0,33,19
	.byte	'DMA_CHANNEL34',0,34,19
	.byte	'DMA_CHANNEL35',0,35,19
	.byte	'DMA_CHANNEL36',0,36,19
	.byte	'DMA_CHANNEL37',0,37,19
	.byte	'DMA_CHANNEL38',0,38,19
	.byte	'DMA_CHANNEL39',0,39,19
	.byte	'DMA_CHANNEL40',0,40,19
	.byte	'DMA_CHANNEL41',0,41,19
	.byte	'DMA_CHANNEL42',0,42,19
	.byte	'DMA_CHANNEL43',0,43,19
	.byte	'DMA_CHANNEL44',0,44,19
	.byte	'DMA_CHANNEL45',0,45,19
	.byte	'DMA_CHANNEL46',0,46,19
	.byte	'DMA_CHANNEL47',0,47,19
	.byte	'DMA_CHANNEL48',0,48,19
	.byte	'DMA_CHANNEL49',0,49,19
	.byte	'DMA_CHANNEL50',0,50,19
	.byte	'DMA_CHANNEL51',0,51,19
	.byte	'DMA_CHANNEL52',0,52,19
	.byte	'DMA_CHANNEL53',0,53,19
	.byte	'DMA_CHANNEL54',0,54,19
	.byte	'DMA_CHANNEL55',0,55,19
	.byte	'DMA_CHANNEL56',0,56,19
	.byte	'DMA_CHANNEL57',0,57,19
	.byte	'DMA_CHANNEL58',0,58,19
	.byte	'DMA_CHANNEL59',0,59,19
	.byte	'DMA_CHANNEL60',0,60,19
	.byte	'DMA_CHANNEL61',0,61,19
	.byte	'DMA_CHANNEL62',0,62,19
	.byte	'DMA_CHANNEL63',0,63,19
	.byte	'DMA_CHANNEL64',0,192,0,19
	.byte	'DMA_CHANNEL65',0,193,0,19
	.byte	'DMA_CHANNEL66',0,194,0,19
	.byte	'DMA_CHANNEL67',0,195,0,19
	.byte	'DMA_CHANNEL68',0,196,0,19
	.byte	'DMA_CHANNEL69',0,197,0,19
	.byte	'DMA_CHANNEL70',0,198,0,19
	.byte	'DMA_CHANNEL71',0,199,0,19
	.byte	'DMA_CHANNEL72',0,200,0,19
	.byte	'DMA_CHANNEL73',0,201,0,19
	.byte	'DMA_CHANNEL74',0,202,0,19
	.byte	'DMA_CHANNEL75',0,203,0,19
	.byte	'DMA_CHANNEL76',0,204,0,19
	.byte	'DMA_CHANNEL77',0,205,0,19
	.byte	'DMA_CHANNEL78',0,206,0,19
	.byte	'DMA_CHANNEL79',0,207,0,19
	.byte	'DMA_CHANNEL80',0,208,0,19
	.byte	'DMA_CHANNEL81',0,209,0,19
	.byte	'DMA_CHANNEL82',0,210,0,19
	.byte	'DMA_CHANNEL83',0,211,0,19
	.byte	'DMA_CHANNEL84',0,212,0,19
	.byte	'DMA_CHANNEL85',0,213,0,19
	.byte	'DMA_CHANNEL86',0,214,0,19
	.byte	'DMA_CHANNEL87',0,215,0,19
	.byte	'DMA_CHANNEL88',0,216,0,19
	.byte	'DMA_CHANNEL89',0,217,0,19
	.byte	'DMA_CHANNEL90',0,218,0,19
	.byte	'DMA_CHANNEL91',0,219,0,19
	.byte	'DMA_CHANNEL92',0,220,0,19
	.byte	'DMA_CHANNEL93',0,221,0,19
	.byte	'DMA_CHANNEL94',0,222,0,19
	.byte	'DMA_CHANNEL95',0,223,0,19
	.byte	'DMA_CHANNEL96',0,224,0,19
	.byte	'DMA_CHANNEL97',0,225,0,19
	.byte	'DMA_CHANNEL98',0,226,0,19
	.byte	'DMA_CHANNEL99',0,227,0,19
	.byte	'DMA_CHANNEL100',0,228,0,19
	.byte	'DMA_CHANNEL101',0,229,0,19
	.byte	'DMA_CHANNEL102',0,230,0,19
	.byte	'DMA_CHANNEL103',0,231,0,19
	.byte	'DMA_CHANNEL104',0,232,0,19
	.byte	'DMA_CHANNEL105',0,233,0,19
	.byte	'DMA_CHANNEL106',0,234,0,19
	.byte	'DMA_CHANNEL107',0,235,0,19
	.byte	'DMA_CHANNEL108',0,236,0,19
	.byte	'DMA_CHANNEL109',0,237,0,19
	.byte	'DMA_CHANNEL110',0,238,0,19
	.byte	'DMA_CHANNEL111',0,239,0,19
	.byte	'DMA_CHANNEL112',0,240,0,19
	.byte	'DMA_CHANNEL113',0,241,0,19
	.byte	'DMA_CHANNEL114',0,242,0,19
	.byte	'DMA_CHANNEL115',0,243,0,19
	.byte	'DMA_CHANNEL116',0,244,0,19
	.byte	'DMA_CHANNEL117',0,245,0,19
	.byte	'DMA_CHANNEL118',0,246,0,19
	.byte	'DMA_CHANNEL119',0,247,0,19
	.byte	'DMA_CHANNEL120',0,248,0,19
	.byte	'DMA_CHANNEL121',0,249,0,19
	.byte	'DMA_CHANNEL122',0,250,0,19
	.byte	'DMA_CHANNEL123',0,251,0,19
	.byte	'DMA_CHANNEL124',0,252,0,19
	.byte	'DMA_CHANNEL125',0,253,0,19
	.byte	'DMA_CHANNEL126',0,254,0,19
	.byte	'DMA_CHANNEL127',0,255,0,19
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0,10
	.byte	'Dma_ChannelType',0,8,149,2,2
	.word	54495
	.byte	16,3
	.word	247
	.byte	17,2,0
.L86:
	.byte	3
	.word	56680
	.byte	16,12
	.word	434
	.byte	17,2,0
.L87:
	.byte	3
	.word	56694
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L22:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,53,0,73,19,0,0,4,46,1,3,8,73
	.byte	19,54,15,39,12,63,12,60,12,0,0,5,5,0,73,19,0,0,6,46,0,3,8,54,15,39,12,63,12,60,12,0,0,7,46,0,3,8,58,15
	.byte	59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,8,59,0,3,8,0,0,9,15,0,73,19,0,0,10,22,0,3,8,58,15,59,15
	.byte	57,15,73,19,0,0,11,21,0,54,15,0,0,12,19,1,3,8,58,15,59,15,57,15,11,15,0,0,13,13,0,3,8,11,15,73,19,13,15
	.byte	12,15,56,9,0,0,14,23,1,58,15,59,15,57,15,11,15,0,0,15,13,0,3,8,11,15,73,19,56,9,0,0,16,1,1,11,15,73,19
	.byte	0,0,17,33,0,47,15,0,0,18,4,1,58,15,59,15,57,15,11,15,0,0,19,40,0,3,8,28,13,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L23:
	.word	.L93-.L92
.L92:
	.half	3
	.word	.L95-.L94
.L94:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Mcal_TcLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Os.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxCpu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxDma_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.h',0,0,0,0,0
.L95:
.L93:
	.sdecl	'.debug_info',debug,cluster('OSEKMP_UserEnableAllInterrupts')
	.sect	'.debug_info'
.L24:
	.word	237
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L27,.L26
	.byte	2
	.word	.L20
	.byte	3
	.byte	'OSEKMP_UserEnableAllInterrupts',0,1,115,6,1,1,1
	.word	.L7,.L63,.L6
	.byte	4
	.word	.L7,.L63
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('OSEKMP_UserEnableAllInterrupts')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('OSEKMP_UserEnableAllInterrupts')
	.sect	'.debug_line'
.L26:
	.word	.L97-.L96
.L96:
	.half	3
	.word	.L99-.L98
.L98:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Os.c',0,0,0,0,0
.L99:
	.byte	5,3,7,0,5,2
	.word	.L7
	.byte	3,244,0,1,5,1,9
	.half	.L100-.L7
	.byte	3,1,1,7,9
	.half	.L28-.L100
	.byte	0,1,1
.L97:
	.sdecl	'.debug_ranges',debug,cluster('OSEKMP_UserEnableAllInterrupts')
	.sect	'.debug_ranges'
.L27:
	.word	-1,.L7,0,.L28-.L7,0,0
	.sdecl	'.debug_info',debug,cluster('OSEKMP_UserDisableAllInterrupts')
	.sect	'.debug_info'
.L29:
	.word	239
	.half	3
	.word	.L30
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L32,.L31
	.byte	2
	.word	.L20
	.byte	3
	.byte	'OSEKMP_UserDisableAllInterrupts',0,1,138,1,6,1,1,1
	.word	.L9,.L64,.L8
	.byte	4
	.word	.L9,.L64
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('OSEKMP_UserDisableAllInterrupts')
	.sect	'.debug_abbrev'
.L30:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('OSEKMP_UserDisableAllInterrupts')
	.sect	'.debug_line'
.L31:
	.word	.L102-.L101
.L101:
	.half	3
	.word	.L104-.L103
.L103:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Os.c',0,0,0,0,0
.L104:
	.byte	5,3,7,0,5,2
	.word	.L9
	.byte	3,140,1,1,5,1,9
	.half	.L105-.L9
	.byte	3,2,1,7,9
	.half	.L33-.L105
	.byte	0,1,1
.L102:
	.sdecl	'.debug_ranges',debug,cluster('OSEKMP_UserDisableAllInterrupts')
	.sect	'.debug_ranges'
.L32:
	.word	-1,.L9,0,.L33-.L9,0,0
	.sdecl	'.debug_info',debug,cluster('OSEKMP_UserSuspendAllInterrupts')
	.sect	'.debug_info'
.L34:
	.word	256
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L37,.L36
	.byte	2
	.word	.L20
	.byte	3
	.byte	'OSEKMP_UserSuspendAllInterrupts',0,1,166,1,6,1,1,1
	.word	.L11,.L65,.L10
	.byte	4
	.word	.L66
	.byte	5
	.byte	'CoreId',0,1,168,1,9
	.word	.L67,.L68
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('OSEKMP_UserSuspendAllInterrupts')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('OSEKMP_UserSuspendAllInterrupts')
	.sect	'.debug_line'
.L36:
	.word	.L107-.L106
.L106:
	.half	3
	.word	.L109-.L108
.L108:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Os.c',0,0,0,0,0
.L109:
	.byte	5,26,7,0,5,2
	.word	.L11
	.byte	3,170,1,1,5,6,9
	.half	.L88-.L11
	.byte	3,2,1,5,3,9
	.half	.L110-.L88
	.byte	1,5,5,7,9
	.half	.L111-.L110
	.byte	3,3,1,9
	.half	.L112-.L111
	.byte	3,1,1,5,27,9
	.half	.L113-.L112
	.byte	1,5,38,9
	.half	.L114-.L113
	.byte	1,5,36,9
	.half	.L115-.L114
	.byte	1,5,5,9
	.half	.L116-.L115
	.byte	3,1,1,5,27,9
	.half	.L2-.L116
	.byte	3,2,1,5,35,9
	.half	.L117-.L2
	.byte	1,5,1,9
	.half	.L118-.L117
	.byte	3,1,1,7,9
	.half	.L38-.L118
	.byte	0,1,1
.L107:
	.sdecl	'.debug_ranges',debug,cluster('OSEKMP_UserSuspendAllInterrupts')
	.sect	'.debug_ranges'
.L37:
	.word	-1,.L11,0,.L38-.L11,0,0
.L66:
	.word	-1,.L11,0,.L65-.L11,-1,.L13,0,.L58-.L13,0,0
	.sdecl	'.debug_info',debug,cluster('OSEKMP_UserResumeAllInterrupts')
	.sect	'.debug_info'
.L39:
	.word	259
	.half	3
	.word	.L40
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L42,.L41
	.byte	2
	.word	.L20
	.byte	3
	.byte	'OSEKMP_UserResumeAllInterrupts',0,1,203,1,6,1,1,1
	.word	.L15,.L69,.L14
	.byte	4
	.word	.L15,.L69
	.byte	5
	.byte	'CoreId',0,1,205,1,9
	.word	.L67,.L70
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('OSEKMP_UserResumeAllInterrupts')
	.sect	'.debug_abbrev'
.L40:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('OSEKMP_UserResumeAllInterrupts')
	.sect	'.debug_line'
.L41:
	.word	.L120-.L119
.L119:
	.half	3
	.word	.L122-.L121
.L121:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Os.c',0,0,0,0,0
.L122:
	.byte	5,26,7,0,5,2
	.word	.L15
	.byte	3,207,1,1,5,7,9
	.half	.L89-.L15
	.byte	3,2,1,5,3,9
	.half	.L123-.L89
	.byte	1,5,29,7,9
	.half	.L124-.L123
	.byte	3,2,1,5,37,9
	.half	.L125-.L124
	.byte	1,5,31,9
	.half	.L3-.L125
	.byte	3,3,1,5,3,9
	.half	.L126-.L3
	.byte	1,5,9,7,9
	.half	.L127-.L126
	.byte	3,2,1,5,31,9
	.half	.L128-.L127
	.byte	1,5,5,9
	.half	.L129-.L128
	.byte	1,5,7,7,9
	.half	.L130-.L129
	.byte	3,3,1,5,1,9
	.half	.L4-.L130
	.byte	3,3,1,7,9
	.half	.L43-.L4
	.byte	0,1,1
.L120:
	.sdecl	'.debug_ranges',debug,cluster('OSEKMP_UserResumeAllInterrupts')
	.sect	'.debug_ranges'
.L42:
	.word	-1,.L15,0,.L43-.L15,0,0
	.sdecl	'.debug_info',debug,cluster('Os_GetCurrentStackArea')
	.sect	'.debug_info'
.L44:
	.word	266
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L47,.L46
	.byte	2
	.word	.L20
	.byte	3
	.byte	'Os_GetCurrentStackArea',0,1,244,1,6,1,1,1
	.word	.L17,.L71,.L16
	.byte	4
	.byte	'start',0,1,244,1,37
	.word	.L72,.L73
	.byte	4
	.byte	'end',0,1,244,1,51
	.word	.L72,.L74
	.byte	5
	.word	.L17,.L71
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Os_GetCurrentStackArea')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Os_GetCurrentStackArea')
	.sect	'.debug_line'
.L46:
	.word	.L132-.L131
.L131:
	.half	3
	.word	.L134-.L133
.L133:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Os.c',0,0,0,0,0
.L134:
	.byte	5,1,7,0,5,2
	.word	.L17
	.byte	3,251,1,1,7,9
	.half	.L48-.L17
	.byte	0,1,1
.L132:
	.sdecl	'.debug_ranges',debug,cluster('Os_GetCurrentStackArea')
	.sect	'.debug_ranges'
.L47:
	.word	-1,.L17,0,.L48-.L17,0,0
	.sdecl	'.debug_info',debug,cluster('Os_UpdateRegSV')
	.sect	'.debug_info'
.L49:
	.word	365
	.half	3
	.word	.L50
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L52,.L51
	.byte	2
	.word	.L20
	.byte	3
	.byte	'Os_UpdateRegSV',0,1,151,2,6,1,1,1
	.word	.L19,.L75,.L18
	.byte	4
	.byte	'Address',0,1,151,2,39
	.word	.L76,.L77
	.byte	4
	.byte	'SetMask',0,1,151,2,54
	.word	.L78,.L79
	.byte	4
	.byte	'ClearMask',0,1,151,2,69
	.word	.L78,.L80
	.byte	4
	.byte	'InitProtectionType',0,1,152,2,35
	.word	.L67,.L81
	.byte	4
	.byte	'Area',0,1,152,2,61
	.word	.L82,.L83
	.byte	5
	.word	.L19,.L75
	.byte	5
	.word	.L19,.L84
	.byte	6
	.byte	'val',0,1,154,2,3
	.word	.L78,.L85
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Os_UpdateRegSV')
	.sect	'.debug_abbrev'
.L50:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Os_UpdateRegSV')
	.sect	'.debug_line'
.L51:
	.word	.L136-.L135
.L135:
	.half	3
	.word	.L138-.L137
.L137:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Os.c',0,0,0,0,0
.L138:
	.byte	5,3,7,0,5,2
	.word	.L19
	.byte	3,153,2,1,5,1,9
	.half	.L84-.L19
	.byte	3,3,1,7,9
	.half	.L53-.L84
	.byte	0,1,1
.L136:
	.sdecl	'.debug_ranges',debug,cluster('Os_UpdateRegSV')
	.sect	'.debug_ranges'
.L52:
	.word	-1,.L19,0,.L53-.L19,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L54:
	.word	206
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L57,.L56
	.byte	2
	.word	.L20
	.byte	3
	.byte	'.cocofun_1',0,1,166,1,6,1
	.word	.L13,.L58,.L12
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L56:
	.word	.L140-.L139
.L139:
	.half	3
	.word	.L142-.L141
.L141:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Os.c',0,0,0,0,0
.L142:
	.byte	5,6,7,0,5,2
	.word	.L13
	.byte	3,172,1,1,5,30,9
	.half	.L143-.L13
	.byte	1,9
	.half	.L58-.L143
	.byte	0,1,1,5,7,0,5,2
	.word	.L13
	.byte	3,209,1,1,5,31,9
	.half	.L143-.L13
	.byte	1,5,30,9
	.half	.L144-.L143
	.byte	3,91,1,7,9
	.half	.L58-.L144
	.byte	0,1,1
.L140:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L57:
	.word	-1,.L13,0,.L58-.L13,0,0
	.sdecl	'.debug_info',debug,cluster('Os_IntSaveDisableCounter')
	.sect	'.debug_info'
.L59:
	.word	208
	.half	3
	.word	.L60
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L20
	.byte	3
	.byte	'Os_IntSaveDisableCounter',0,2,84,23
	.word	.L86
	.byte	5,3
	.word	Os_IntSaveDisableCounter
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Os_IntSaveDisableCounter')
	.sect	'.debug_abbrev'
.L60:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Os_SavedIntLevelNested')
	.sect	'.debug_info'
.L61:
	.word	206
	.half	3
	.word	.L62
	.byte	4,1
	.byte	'..\\mcal_src\\Os.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L20
	.byte	3
	.byte	'Os_SavedIntLevelNested',0,2,85,24
	.word	.L87
	.byte	5,3
	.word	Os_SavedIntLevelNested
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Os_SavedIntLevelNested')
	.sect	'.debug_abbrev'
.L62:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L12:
	.word	-1,.L13,0,.L58-.L13
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('OSEKMP_UserDisableAllInterrupts')
	.sect	'.debug_loc'
.L8:
	.word	-1,.L9,0,.L64-.L9
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('OSEKMP_UserEnableAllInterrupts')
	.sect	'.debug_loc'
.L6:
	.word	-1,.L7,0,.L63-.L7
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('OSEKMP_UserResumeAllInterrupts')
	.sect	'.debug_loc'
.L70:
	.word	-1,.L15,.L13-.L15,.L58-.L15
	.half	5
	.byte	144,33,157,32,0
	.word	.L89-.L15,.L69-.L15
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L14:
	.word	-1,.L15,0,.L69-.L15
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('OSEKMP_UserSuspendAllInterrupts')
	.sect	'.debug_loc'
.L68:
	.word	-1,.L11,.L13-.L11,.L58-.L11
	.half	5
	.byte	144,33,157,32,0
	.word	.L88-.L11,.L65-.L11
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L10:
	.word	-1,.L11,0,.L65-.L11
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Os_GetCurrentStackArea')
	.sect	'.debug_loc'
.L16:
	.word	-1,.L17,0,.L71-.L17
	.half	2
	.byte	138,0
	.word	0,0
.L74:
	.word	-1,.L17,0,.L71-.L17
	.half	1
	.byte	101
	.word	0,0
.L73:
	.word	-1,.L17,0,.L71-.L17
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Os_UpdateRegSV')
	.sect	'.debug_loc'
.L77:
	.word	-1,.L19,0,.L75-.L19
	.half	1
	.byte	100
	.word	0,0
.L83:
	.word	-1,.L19,0,.L75-.L19
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
.L80:
	.word	-1,.L19,0,.L90-.L19
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L81:
	.word	-1,.L19,0,.L75-.L19
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L18:
	.word	-1,.L19,0,.L75-.L19
	.half	2
	.byte	138,0
	.word	0,0
.L79:
	.word	-1,.L19,0,.L75-.L19
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L85:
	.word	-1,.L19,.L91-.L19,.L75-.L19
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L145:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('OSEKMP_UserEnableAllInterrupts')
	.sect	'.debug_frame'
	.word	24
	.word	.L145,.L7,.L63-.L7
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('OSEKMP_UserDisableAllInterrupts')
	.sect	'.debug_frame'
	.word	24
	.word	.L145,.L9,.L64-.L9
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('OSEKMP_UserSuspendAllInterrupts')
	.sect	'.debug_frame'
	.word	12
	.word	.L145,.L11,.L65-.L11
	.sdecl	'.debug_frame',debug,cluster('OSEKMP_UserResumeAllInterrupts')
	.sect	'.debug_frame'
	.word	12
	.word	.L145,.L15,.L69-.L15
	.sdecl	'.debug_frame',debug,cluster('Os_GetCurrentStackArea')
	.sect	'.debug_frame'
	.word	24
	.word	.L145,.L17,.L71-.L17
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Os_UpdateRegSV')
	.sect	'.debug_frame'
	.word	24
	.word	.L145,.L19,.L75-.L19
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L146:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L146,.L13,.L58-.L13
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Os.c	   286  
; ..\mcal_src\Os.c	   287  

	; Module end
