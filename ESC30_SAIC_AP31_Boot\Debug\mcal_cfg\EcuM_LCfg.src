	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc17708a -c99 --dep-file=mcal_cfg\\.EcuM_LCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\EcuM_LCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\EcuM_LCfg.src ..\\mcal_cfg\\EcuM_LCfg.c"
	.compiler_name		"ctc"
	.name	"EcuM_LCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.DEFAULT_CONST_32BIT',data,rom,cluster('EcuM_ConfigConsistencyHash')
	.sect	'.rodata.CPU0.Private.DEFAULT_CONST_32BIT'
	.global	EcuM_ConfigConsistencyHash
	.align	4
EcuM_ConfigConsistencyHash:	.type	object
	.size	EcuM_ConfigConsistencyHash,4
	.word	-1
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	339
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\EcuM_LCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	178
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	184
	.byte	5,1,3
	.word	208
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	210
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	233
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	264
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	301
.L8:
	.byte	7
	.word	301
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'..\\mcal_cfg\\EcuM_LCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('EcuM_ConfigConsistencyHash')
	.sect	'.debug_info'
.L6:
	.word	218
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\EcuM_LCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'EcuM_ConfigConsistencyHash',0,1,59,14
	.word	.L8
	.byte	1,5,3
	.word	EcuM_ConfigConsistencyHash
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('EcuM_ConfigConsistencyHash')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0

; ..\mcal_cfg\EcuM_LCfg.c	     1  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	     2  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_cfg\EcuM_LCfg.c	     4  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	     5  ** All rights reserved.                                                       **
; ..\mcal_cfg\EcuM_LCfg.c	     6  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_cfg\EcuM_LCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_cfg\EcuM_LCfg.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_cfg\EcuM_LCfg.c	    10  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    11  ********************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    12  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    13  **   $FILENAME   : EcuM_LCfg.c $                                              **
; ..\mcal_cfg\EcuM_LCfg.c	    14  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    15  **   $CC VERSION : \main\6 $                                                  **
; ..\mcal_cfg\EcuM_LCfg.c	    16  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    17  **   DATE, TIME: 2020-07-10, 14:56:10                                         **
; ..\mcal_cfg\EcuM_LCfg.c	    18  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    19  **   GENERATOR : Build b141014-0350                                           **
; ..\mcal_cfg\EcuM_LCfg.c	    20  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    21  **   AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\mcal_cfg\EcuM_LCfg.c	    22  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    23  **   VENDOR    : Infineon Technologies                                        **
; ..\mcal_cfg\EcuM_LCfg.c	    24  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    25  **   DESCRIPTION  : EcuM configuration generated from ECU configuration file  **
; ..\mcal_cfg\EcuM_LCfg.c	    26  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    27  **   MAY BE CHANGED BY USER [yes/no]: NO                                      **
; ..\mcal_cfg\EcuM_LCfg.c	    28  **                                                                            **
; ..\mcal_cfg\EcuM_LCfg.c	    29  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    30   
; ..\mcal_cfg\EcuM_LCfg.c	    31  
; ..\mcal_cfg\EcuM_LCfg.c	    32  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    33  **                      Includes                                              **
; ..\mcal_cfg\EcuM_LCfg.c	    34  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    35  #include "EcuM.h"
; ..\mcal_cfg\EcuM_LCfg.c	    36  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    37  **                      Private Macro Definitions                             **
; ..\mcal_cfg\EcuM_LCfg.c	    38  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    39  
; ..\mcal_cfg\EcuM_LCfg.c	    40  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    41  **                      Private Type Definitions                              **
; ..\mcal_cfg\EcuM_LCfg.c	    42  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    43  
; ..\mcal_cfg\EcuM_LCfg.c	    44  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    45  **                      Private Function Declarations                         **
; ..\mcal_cfg\EcuM_LCfg.c	    46  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    47  
; ..\mcal_cfg\EcuM_LCfg.c	    48  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    49  **                      Global Constant Definitions                           **
; ..\mcal_cfg\EcuM_LCfg.c	    50  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    51  
; ..\mcal_cfg\EcuM_LCfg.c	    52  #define ECUM_START_SEC_CONST_32BIT
; ..\mcal_cfg\EcuM_LCfg.c	    53  #include "MemMap.h"
; ..\mcal_cfg\EcuM_LCfg.c	    54  /*
; ..\mcal_cfg\EcuM_LCfg.c	    55  Configuration: EcuM_ConfigConsistencyHash 
; ..\mcal_cfg\EcuM_LCfg.c	    56  */
; ..\mcal_cfg\EcuM_LCfg.c	    57  /* Hash over all pre-compile and link-time parameters. Currently this is not 
; ..\mcal_cfg\EcuM_LCfg.c	    58    calculated and not used */
; ..\mcal_cfg\EcuM_LCfg.c	    59  const uint32 EcuM_ConfigConsistencyHash = 0xFFFFFFFFU;  
; ..\mcal_cfg\EcuM_LCfg.c	    60  
; ..\mcal_cfg\EcuM_LCfg.c	    61  #define ECUM_STOP_SEC_CONST_32BIT
; ..\mcal_cfg\EcuM_LCfg.c	    62  #include "MemMap.h"
; ..\mcal_cfg\EcuM_LCfg.c	    63  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    64  **                      Global Variable Definitions                           **
; ..\mcal_cfg\EcuM_LCfg.c	    65  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    66  
; ..\mcal_cfg\EcuM_LCfg.c	    67  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    68  **                      Private Constant Definitions                          **
; ..\mcal_cfg\EcuM_LCfg.c	    69  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    70  
; ..\mcal_cfg\EcuM_LCfg.c	    71  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    72  **                      Private Variable Definitions                          **
; ..\mcal_cfg\EcuM_LCfg.c	    73  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    74  
; ..\mcal_cfg\EcuM_LCfg.c	    75  /*******************************************************************************
; ..\mcal_cfg\EcuM_LCfg.c	    76  **                      Global Function Definitions                           **
; ..\mcal_cfg\EcuM_LCfg.c	    77  *******************************************************************************/
; ..\mcal_cfg\EcuM_LCfg.c	    78  

	; Module end
