	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc6136a -c99 --dep-file=eeprom\\NvM\\.NvM_Crc.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\NvM\\NvM_Crc.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\NvM\\NvM_Crc.src ..\\eeprom\\NvM\\NvM_Crc.c"
	.compiler_name		"ctc"
	.name	"NvM_Crc"

	
$TC16X
	
	.sdecl	'.text.NvM_Crc.NvM_CrcJob_Create',code,cluster('NvM_CrcJob_Create')
	.sect	'.text.NvM_Crc.NvM_CrcJob_Create'
	.align	2
	
	.global	NvM_CrcJob_Create

; ..\eeprom\NvM\NvM_Crc.c	     1  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	     2   *  COPYRIGHT
; ..\eeprom\NvM\NvM_Crc.c	     3   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Crc.c	     4   *  \verbatim
; ..\eeprom\NvM\NvM_Crc.c	     5   *  Copyright (c) 2019 by Vector Informatik GmbH.                                                  All rights reserved.
; ..\eeprom\NvM\NvM_Crc.c	     6   *
; ..\eeprom\NvM\NvM_Crc.c	     7   *                This software is copyright protected and proprietary to Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_Crc.c	     8   *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
; ..\eeprom\NvM\NvM_Crc.c	     9   *                All other rights remain with Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_Crc.c	    10   *  \endverbatim
; ..\eeprom\NvM\NvM_Crc.c	    11   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Crc.c	    12   *  FILE DESCRIPTION
; ..\eeprom\NvM\NvM_Crc.c	    13   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Crc.c	    14   *         File:  NvM_Crc.c
; ..\eeprom\NvM\NvM_Crc.c	    15   *      Project:  MemService_AsrNvM
; ..\eeprom\NvM\NvM_Crc.c	    16   *       Module:  NvM - Submodule Crc (Crc processing)
; ..\eeprom\NvM\NvM_Crc.c	    17   *    Generator:  -
; ..\eeprom\NvM\NvM_Crc.c	    18   *
; ..\eeprom\NvM\NvM_Crc.c	    19   *  Description:  This sub-module implements the CRC recalculation FSM that
; ..\eeprom\NvM\NvM_Crc.c	    20   *                recalculates the CRC piece-wise
; ..\eeprom\NvM\NvM_Crc.c	    21   *
; ..\eeprom\NvM\NvM_Crc.c	    22   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    23  
; ..\eeprom\NvM\NvM_Crc.c	    24  /* Do not modify this file! */
; ..\eeprom\NvM\NvM_Crc.c	    25  
; ..\eeprom\NvM\NvM_Crc.c	    26  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    27   *  MODULE SWITCH
; ..\eeprom\NvM\NvM_Crc.c	    28   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    29  #define NVM_CRC_SOURCE
; ..\eeprom\NvM\NvM_Crc.c	    30  
; ..\eeprom\NvM\NvM_Crc.c	    31  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    32   *  INCLUDES
; ..\eeprom\NvM\NvM_Crc.c	    33   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    34  #include "Std_Types.h"
; ..\eeprom\NvM\NvM_Crc.c	    35  
; ..\eeprom\NvM\NvM_Crc.c	    36  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    37   *  INCLUDE OF CONFIGURATION (ALL SECTIONS)
; ..\eeprom\NvM\NvM_Crc.c	    38   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    39  #include "NvM_Cfg.h"
; ..\eeprom\NvM\NvM_Crc.c	    40  #include "NvM_PrivateCfg.h"
; ..\eeprom\NvM\NvM_Crc.c	    41  
; ..\eeprom\NvM\NvM_Crc.c	    42  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    43   *  INCLUDE OF THE CENTRAL INTERNAL INCLUDE
; ..\eeprom\NvM\NvM_Crc.c	    44   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    45  #include "NvM_JobProc.h"
; ..\eeprom\NvM\NvM_Crc.c	    46  
; ..\eeprom\NvM\NvM_Crc.c	    47  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    48   *  MODULE HEADER INCLUDES
; ..\eeprom\NvM\NvM_Crc.c	    49   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    50  #include "NvM_Crc.h"
; ..\eeprom\NvM\NvM_Crc.c	    51  
; ..\eeprom\NvM\NvM_Crc.c	    52  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    53   *  INTERNAL MACROS
; ..\eeprom\NvM\NvM_Crc.c	    54   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    55  #ifndef NVM_LOCAL /* COV_NVM_COMPATIBILITY */
; ..\eeprom\NvM\NvM_Crc.c	    56  # define NVM_LOCAL static
; ..\eeprom\NvM\NvM_Crc.c	    57  #endif
; ..\eeprom\NvM\NvM_Crc.c	    58  
; ..\eeprom\NvM\NvM_Crc.c	    59  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    60   *  INTERNAL FORWARD DECLARATIONS
; ..\eeprom\NvM\NvM_Crc.c	    61   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    62  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_Crc.c	    63    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	    64  
; ..\eeprom\NvM\NvM_Crc.c	    65  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    66   * NvM_Crc_NoCrc_Calculate
; ..\eeprom\NvM\NvM_Crc.c	    67   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    68  /*! \brief Do nothing, function is a dummy (blocks without Crc).
; ..\eeprom\NvM\NvM_Crc.c	    69   *  \details Do nothing, function is a dummy (blocks without Crc).
; ..\eeprom\NvM\NvM_Crc.c	    70   *  \param[in] DataPtr, value not relevant, because dummy function.
; ..\eeprom\NvM\NvM_Crc.c	    71   *  \param[in] Length, value not relevant, because dummy function.
; ..\eeprom\NvM\NvM_Crc.c	    72   *  \param[in] CurrentValue, value not relevant, because dummy function.
; ..\eeprom\NvM\NvM_Crc.c	    73   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	    74   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	    75   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	    76   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	    77   */
; ..\eeprom\NvM\NvM_Crc.c	    78  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_NoCrc_Calculate (NvM_ConstRamAddressType DataPtr, uint16 Length, NvM_CrcValuePtrType CurrentValue);
; ..\eeprom\NvM\NvM_Crc.c	    79  
; ..\eeprom\NvM\NvM_Crc.c	    80  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    81   * NvM_Crc_NoCrc_Compare
; ..\eeprom\NvM\NvM_Crc.c	    82   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    83  /*! \brief Do nothing, function is a dummy (blocks without Crc).
; ..\eeprom\NvM\NvM_Crc.c	    84   *  \details Do nothing, function is a dummy (blocks without Crc).
; ..\eeprom\NvM\NvM_Crc.c	    85   *  \param[in] CrcBuff, value not relevant, because dummy function.
; ..\eeprom\NvM\NvM_Crc.c	    86   *  \param[in] CurrentValue, value not relevant, because dummy function.
; ..\eeprom\NvM\NvM_Crc.c	    87   *  \return TRUE always
; ..\eeprom\NvM\NvM_Crc.c	    88   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	    89   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	    90   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	    91   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	    92   */
; ..\eeprom\NvM\NvM_Crc.c	    93  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_Crc_NoCrc_Compare (NvM_CrcBufferConstPtrType CrcBuff, NvM_CrcValueRefType CurrentValue);
; ..\eeprom\NvM\NvM_Crc.c	    94  
; ..\eeprom\NvM\NvM_Crc.c	    95  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	    96   * NvM_Crc_NoCrc_CopyToBuffer
; ..\eeprom\NvM\NvM_Crc.c	    97   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	    98  /*! \brief Do nothing, function is a dummy (blocks without Crc).
; ..\eeprom\NvM\NvM_Crc.c	    99   *  \details Do nothing, function is a dummy (blocks without Crc).
; ..\eeprom\NvM\NvM_Crc.c	   100   *  \param[in] Dest, value not relevant, because dummy function.
; ..\eeprom\NvM\NvM_Crc.c	   101   *  \param[in] Src, value not relevant, because dummy function.
; ..\eeprom\NvM\NvM_Crc.c	   102   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	   103   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	   104   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	   105   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	   106   */
; ..\eeprom\NvM\NvM_Crc.c	   107  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_NoCrc_CopyToBuffer (NvM_CrcBufferPtrType Dest, NvM_CrcValueRefType Src);
; ..\eeprom\NvM\NvM_Crc.c	   108  
; ..\eeprom\NvM\NvM_Crc.c	   109  #if (NVM_USE_CRC16 == STD_ON)
; ..\eeprom\NvM\NvM_Crc.c	   110  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   111   * NvM_Crc_Crc16_Calculate
; ..\eeprom\NvM\NvM_Crc.c	   112   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   113  /*! \brief Calculates Crc 16.
; ..\eeprom\NvM\NvM_Crc.c	   114   *  \details Invokes the Crc16 calculation function exactly once.
; ..\eeprom\NvM\NvM_Crc.c	   115   *  \param[in] DataPtr to calculate the CRC over. Must not be NULL_PTR. Caller has to ensure correctness - has to
; ..\eeprom\NvM\NvM_Crc.c	   116   *             match the Length.
; ..\eeprom\NvM\NvM_Crc.c	   117   *  \param[in] Length as number of bytes to calculate the CRC. Has to match the DataPtr target length.
; ..\eeprom\NvM\NvM_Crc.c	   118   *  \param[in,out] CurrCrc the current CRC value, will be used as CRC start value and overwritten by the new CRC.
; ..\eeprom\NvM\NvM_Crc.c	   119   *                 Caller has to ensure correctness, that means length. Must not be NULL_PTR.
; ..\eeprom\NvM\NvM_Crc.c	   120   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	   121   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	   122   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	   123   *  \config at least one block with Crc 16
; ..\eeprom\NvM\NvM_Crc.c	   124   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	   125   */
; ..\eeprom\NvM\NvM_Crc.c	   126  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_Crc16_Calculate (NvM_ConstRamAddressType DataPtr, uint16 Length, NvM_CrcValuePtrType CurrCrc);
; ..\eeprom\NvM\NvM_Crc.c	   127  
; ..\eeprom\NvM\NvM_Crc.c	   128  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   129   * NvM_Crc_Crc16_Compare
; ..\eeprom\NvM\NvM_Crc.c	   130   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   131  /*! \brief Compares given (two) Crc16 values.
; ..\eeprom\NvM\NvM_Crc.c	   132   *  \details The given Crc values have to be Crc16 - first two bytes will be compared.
; ..\eeprom\NvM\NvM_Crc.c	   133   *  \param[in] Crc1 as the first value to be compared. Length must fit at least the CRC length. Must not be NULL_PTR.
; ..\eeprom\NvM\NvM_Crc.c	   134   *  \param[in] Crc2 as the second value to be compared. Length must fit at least the CRC length. Must not be NULL_PTR.
; ..\eeprom\NvM\NvM_Crc.c	   135   *  \return given Crc values fits: TRUE, otherwise: FALSE
; ..\eeprom\NvM\NvM_Crc.c	   136   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	   137   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	   138   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	   139   *  \config at least one block with Crc 16
; ..\eeprom\NvM\NvM_Crc.c	   140   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	   141   */
; ..\eeprom\NvM\NvM_Crc.c	   142  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_Crc_Crc16_Compare (NvM_CrcBufferConstPtrType Crc1, NvM_CrcValueRefType Crc2);
; ..\eeprom\NvM\NvM_Crc.c	   143  
; ..\eeprom\NvM\NvM_Crc.c	   144  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   145   * NvM_Crc_Crc16_CopyToBuffer
; ..\eeprom\NvM\NvM_Crc.c	   146   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   147  /*! \brief Copies source Crc16 buffer to the destination Crc16 buffer.
; ..\eeprom\NvM\NvM_Crc.c	   148   *  \details The given Crc buffer have to be for Crc16 - that means at least two byte long. The API will copy only
; ..\eeprom\NvM\NvM_Crc.c	   149   *           the first two bytes from source to destination
; ..\eeprom\NvM\NvM_Crc.c	   150   *  \param[in] Dest to copy to source to. Must not be NULL_PTR. Caller has to ensure correctness.
; ..\eeprom\NvM\NvM_Crc.c	   151   *  \param[in] Src to copy to the destination. Must not be NULL_PTR. Caller has to ensure correctness.
; ..\eeprom\NvM\NvM_Crc.c	   152   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	   153   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	   154   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	   155   *  \config at least one block with Crc 16
; ..\eeprom\NvM\NvM_Crc.c	   156   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	   157   */
; ..\eeprom\NvM\NvM_Crc.c	   158  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_Crc16_CopyToBuffer (NvM_CrcBufferPtrType Dest, NvM_CrcValueRefType Src);
; ..\eeprom\NvM\NvM_Crc.c	   159  #endif /* (NVM_USE_CRC16 == STD_ON) */
; ..\eeprom\NvM\NvM_Crc.c	   160  
; ..\eeprom\NvM\NvM_Crc.c	   161  #if (NVM_USE_CRC32 == STD_ON)
; ..\eeprom\NvM\NvM_Crc.c	   162  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   163   * NvM_Crc_Crc32_Calculate
; ..\eeprom\NvM\NvM_Crc.c	   164   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   165  /*! \brief Calculates Crc 32.
; ..\eeprom\NvM\NvM_Crc.c	   166   *  \details Invokes the Crc32 calculation function exactly once.
; ..\eeprom\NvM\NvM_Crc.c	   167   *  \param[in] DataPtr to calculate the CRC over. Must not be NULL_PTR. Caller has to ensure correctness - has to
; ..\eeprom\NvM\NvM_Crc.c	   168   *             match the Length.
; ..\eeprom\NvM\NvM_Crc.c	   169   *  \param[in] Length as number of bytes to calculate the CRC. Has to match the DataPtr target length.
; ..\eeprom\NvM\NvM_Crc.c	   170   *  \param[in,out] CurrCrc the current CRC value, will be used as CRC start value and overwritten by the new CRC.
; ..\eeprom\NvM\NvM_Crc.c	   171   *                 Caller has to ensure correctness, that means length. Must not be NULL_PTR.
; ..\eeprom\NvM\NvM_Crc.c	   172   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	   173   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	   174   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	   175   *  \config at least one block with Crc 32
; ..\eeprom\NvM\NvM_Crc.c	   176   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	   177   */
; ..\eeprom\NvM\NvM_Crc.c	   178  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_Crc32_Calculate(NvM_ConstRamAddressType DataPtr, uint16 Length, NvM_CrcValuePtrType CurrCrc);
; ..\eeprom\NvM\NvM_Crc.c	   179  
; ..\eeprom\NvM\NvM_Crc.c	   180  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   181   * NvM_Crc_Crc32_Compare
; ..\eeprom\NvM\NvM_Crc.c	   182   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   183  /*! \brief Compares given (two) Crc16 values.
; ..\eeprom\NvM\NvM_Crc.c	   184   *  \details The given Crc values have to be Crc32 - first four bytes will be compared.
; ..\eeprom\NvM\NvM_Crc.c	   185   *  \param[in] Crc1 as the first value to be compared. Length must fit at least the CRC length. Must not be NULL_PTR.
; ..\eeprom\NvM\NvM_Crc.c	   186   *  \param[in] Crc2 as the second value to be compared. Length must fit at least the CRC length. Must not be NULL_PTR.
; ..\eeprom\NvM\NvM_Crc.c	   187   *  \return given Crc values fits: TRUE, otherwise: FALSE
; ..\eeprom\NvM\NvM_Crc.c	   188   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	   189   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	   190   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	   191   *  \config at least one block with Crc 32
; ..\eeprom\NvM\NvM_Crc.c	   192   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	   193   */
; ..\eeprom\NvM\NvM_Crc.c	   194  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_Crc_Crc32_Compare (NvM_CrcBufferConstPtrType Crc1, NvM_CrcValueRefType Crc2);
; ..\eeprom\NvM\NvM_Crc.c	   195  
; ..\eeprom\NvM\NvM_Crc.c	   196  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   197   * NvM_Crc_Crc32_CopyToBuffer
; ..\eeprom\NvM\NvM_Crc.c	   198   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   199  /*! \brief Copies source Crc32 buffer to the destination Crc32 buffer.
; ..\eeprom\NvM\NvM_Crc.c	   200   *  \details The given Crc buffer have to be for Crc32 - that means at least four byte long. The API will copy only
; ..\eeprom\NvM\NvM_Crc.c	   201   *           the first four bytes from source to destination
; ..\eeprom\NvM\NvM_Crc.c	   202   *  \param[in] Dest to copy to source to. Must not be NULL_PTR. Caller has to ensure correctness.
; ..\eeprom\NvM\NvM_Crc.c	   203   *  \param[in] Src to copy to the destination. Must not be NULL_PTR. Caller has to ensure correctness.
; ..\eeprom\NvM\NvM_Crc.c	   204   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	   205   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	   206   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	   207   *  \config at least one block with Crc 32
; ..\eeprom\NvM\NvM_Crc.c	   208   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	   209   */
; ..\eeprom\NvM\NvM_Crc.c	   210  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_Crc32_CopyToBuffer (NvM_CrcBufferPtrType Dest, NvM_CrcValueRefType Src);
; ..\eeprom\NvM\NvM_Crc.c	   211  #endif /* (NVM_USE_CRC32 == STD_ON) */
; ..\eeprom\NvM\NvM_Crc.c	   212  
; ..\eeprom\NvM\NvM_Crc.c	   213  #if ((NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) && (NVM_CALC_RAM_CRC_USED == STD_ON))
; ..\eeprom\NvM\NvM_Crc.c	   214  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   215   * NvM_CrcQueueCountTrailingZeros
; ..\eeprom\NvM\NvM_Crc.c	   216   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   217  /*! \brief Removes all lower bits with value 0 from given parameter.
; ..\eeprom\NvM\NvM_Crc.c	   218   *  \details Removes all lower bits with value 0 from given parameter.
; ..\eeprom\NvM\NvM_Crc.c	   219   *  \param[in] Word the value to count the trailing zero for.
; ..\eeprom\NvM\NvM_Crc.c	   220   *  \return given parameter without any lower bits with 0
; ..\eeprom\NvM\NvM_Crc.c	   221   *  \context TASK
; ..\eeprom\NvM\NvM_Crc.c	   222   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Crc.c	   223   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Crc.c	   224   *  \pre -
; ..\eeprom\NvM\NvM_Crc.c	   225   */
; ..\eeprom\NvM\NvM_Crc.c	   226  NVM_LOCAL uint8 NvM_CrcQueueCountTrailingZeros(NvM_CrcQueueEntryType Word);
; ..\eeprom\NvM\NvM_Crc.c	   227  #endif /* ((NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) && (NVM_CALC_RAM_CRC_USED == STD_ON)) */
; ..\eeprom\NvM\NvM_Crc.c	   228  
; ..\eeprom\NvM\NvM_Crc.c	   229  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_Crc.c	   230    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   231  
; ..\eeprom\NvM\NvM_Crc.c	   232  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   233   *  INTERNAL MODULE VARIABLES
; ..\eeprom\NvM\NvM_Crc.c	   234   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   235  #define NVM_START_SEC_CONST_UNSPECIFIED
; ..\eeprom\NvM\NvM_Crc.c	   236    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   237  
; ..\eeprom\NvM\NvM_Crc.c	   238  /* "Dummy" Handler for Disabled CRC -> exists always */
; ..\eeprom\NvM\NvM_Crc.c	   239  NVM_LOCAL CONST(struct NvM_CrcHandlerClass, NVM_PRIVATE_CONST) NvM_Crc_NoCrcHandler_t =
; ..\eeprom\NvM\NvM_Crc.c	   240  {
; ..\eeprom\NvM\NvM_Crc.c	   241      NvM_Crc_NoCrc_Calculate,
; ..\eeprom\NvM\NvM_Crc.c	   242      NvM_Crc_NoCrc_Compare,
; ..\eeprom\NvM\NvM_Crc.c	   243      NvM_Crc_NoCrc_CopyToBuffer,
; ..\eeprom\NvM\NvM_Crc.c	   244      0, /* initial value */
; ..\eeprom\NvM\NvM_Crc.c	   245      0  /* crc length */
; ..\eeprom\NvM\NvM_Crc.c	   246  };
; ..\eeprom\NvM\NvM_Crc.c	   247  
; ..\eeprom\NvM\NvM_Crc.c	   248  #if (NVM_USE_CRC16 == STD_ON)
; ..\eeprom\NvM\NvM_Crc.c	   249  /* CRC16 handler, only available if there is at least one NvM block configured with CRC16 */
; ..\eeprom\NvM\NvM_Crc.c	   250  NVM_LOCAL CONST(struct NvM_CrcHandlerClass, NVM_PRIVATE_CONST) NvM_Crc_Crc16Handler_t =
; ..\eeprom\NvM\NvM_Crc.c	   251  {
; ..\eeprom\NvM\NvM_Crc.c	   252      NvM_Crc_Crc16_Calculate,
; ..\eeprom\NvM\NvM_Crc.c	   253      NvM_Crc_Crc16_Compare,
; ..\eeprom\NvM\NvM_Crc.c	   254      NvM_Crc_Crc16_CopyToBuffer,
; ..\eeprom\NvM\NvM_Crc.c	   255      NVM_INITIAL_CRC_16_VALUE, /* initial value */
; ..\eeprom\NvM\NvM_Crc.c	   256      2  /* crc length */
; ..\eeprom\NvM\NvM_Crc.c	   257  };
; ..\eeprom\NvM\NvM_Crc.c	   258  #else
; ..\eeprom\NvM\NvM_Crc.c	   259  # define NvM_Crc_Crc16Handler_t NvM_Crc_NoCrcHandler_t
; ..\eeprom\NvM\NvM_Crc.c	   260  #endif /* (NVM_USE_CRC16 == STD_ON) */
; ..\eeprom\NvM\NvM_Crc.c	   261  
; ..\eeprom\NvM\NvM_Crc.c	   262  #if (NVM_USE_CRC32 == STD_ON)
; ..\eeprom\NvM\NvM_Crc.c	   263  /* CRC32 handler, only available if there is at least one NvM block configured with CRC32 */
; ..\eeprom\NvM\NvM_Crc.c	   264  NVM_LOCAL CONST(struct NvM_CrcHandlerClass, NVM_PRIVATE_CONST) NvM_Crc_Crc32Handler_t =
; ..\eeprom\NvM\NvM_Crc.c	   265  {
; ..\eeprom\NvM\NvM_Crc.c	   266      NvM_Crc_Crc32_Calculate,
; ..\eeprom\NvM\NvM_Crc.c	   267      NvM_Crc_Crc32_Compare,
; ..\eeprom\NvM\NvM_Crc.c	   268      NvM_Crc_Crc32_CopyToBuffer,
; ..\eeprom\NvM\NvM_Crc.c	   269      NVM_INITIAL_CRC_32_VALUE, /* initial value */
; ..\eeprom\NvM\NvM_Crc.c	   270      4  /* crc length */
; ..\eeprom\NvM\NvM_Crc.c	   271  };
; ..\eeprom\NvM\NvM_Crc.c	   272  #else
; ..\eeprom\NvM\NvM_Crc.c	   273  # define NvM_Crc_Crc32Handler_t NvM_Crc_NoCrcHandler_t
; ..\eeprom\NvM\NvM_Crc.c	   274  #endif /* (NVM_USE_CRC32 == STD_ON) */
; ..\eeprom\NvM\NvM_Crc.c	   275  
; ..\eeprom\NvM\NvM_Crc.c	   276  /* CRC handler table including all currently supported and enabled/used CRC type handlers */
; ..\eeprom\NvM\NvM_Crc.c	   277  /* PRQA S 3218 1 */ /* MD_NvM_8.9_CrcHandlerTable */
; ..\eeprom\NvM\NvM_Crc.c	   278  NVM_LOCAL CONST(NvM_CrcHandlerClassConstPtr, NVM_PRIVATE_CONST) NvM_CrcHandlerTable_at[4] =
; ..\eeprom\NvM\NvM_Crc.c	   279  {
; ..\eeprom\NvM\NvM_Crc.c	   280      &NvM_Crc_NoCrcHandler_t,
; ..\eeprom\NvM\NvM_Crc.c	   281      &NvM_Crc_NoCrcHandler_t, /* reserved for CRC8 */
; ..\eeprom\NvM\NvM_Crc.c	   282      &NvM_Crc_Crc16Handler_t,
; ..\eeprom\NvM\NvM_Crc.c	   283      &NvM_Crc_Crc32Handler_t
; ..\eeprom\NvM\NvM_Crc.c	   284  };
; ..\eeprom\NvM\NvM_Crc.c	   285  
; ..\eeprom\NvM\NvM_Crc.c	   286  #define NVM_STOP_SEC_CONST_UNSPECIFIED
; ..\eeprom\NvM\NvM_Crc.c	   287    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   288  
; ..\eeprom\NvM\NvM_Crc.c	   289  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   290   *  CRC queue will only be available if the SetRamBlockStatus API and NvMCalcRamCrc are enabled
; ..\eeprom\NvM\NvM_Crc.c	   291   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   292  #if((NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) && (NVM_CALC_RAM_CRC_USED == STD_ON))
; ..\eeprom\NvM\NvM_Crc.c	   293  
; ..\eeprom\NvM\NvM_Crc.c	   294  /* the size of the CRC job queue in 32bitWords. It is a bit-string, one bit for each block */
; ..\eeprom\NvM\NvM_Crc.c	   295  # define NVM_SIZE_CRC_JOB_QUEUE ((NVM_TOTAL_NUM_OF_NVRAM_BLOCKS + NVM_CRC_QUEUE_BITINDEX_MASK) >> NVM_CRC_QUEUE_ENTRY_SHIFT)
; ..\eeprom\NvM\NvM_Crc.c	   296  
; ..\eeprom\NvM\NvM_Crc.c	   297  # define NVM_START_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM_Crc.c	   298    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   299  
; ..\eeprom\NvM\NvM_Crc.c	   300  /* Crc job queue (It's just a bit string) */
; ..\eeprom\NvM\NvM_Crc.c	   301  NVM_LOCAL VAR(NvM_CrcQueueEntryType, NVM_PRIVATE_DATA) NvM_CrcQueue_at[NVM_SIZE_CRC_JOB_QUEUE];
; ..\eeprom\NvM\NvM_Crc.c	   302  /* current CRC queue position to start scanning at */
; ..\eeprom\NvM\NvM_Crc.c	   303  NVM_LOCAL VAR(NvM_BlockIdType, NVM_PRIVATE_DATA) NvM_CrcQueueScanStart_u16;
; ..\eeprom\NvM\NvM_Crc.c	   304  
; ..\eeprom\NvM\NvM_Crc.c	   305  # define NVM_STOP_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM_Crc.c	   306    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   307  
; ..\eeprom\NvM\NvM_Crc.c	   308  # define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_Crc.c	   309    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   310  
; ..\eeprom\NvM\NvM_Crc.c	   311  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   312  *  NvM_CrcQueueInit
; ..\eeprom\NvM\NvM_Crc.c	   313  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   314  /*!
; ..\eeprom\NvM\NvM_Crc.c	   315   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   316   *
; ..\eeprom\NvM\NvM_Crc.c	   317   *
; ..\eeprom\NvM\NvM_Crc.c	   318   *
; ..\eeprom\NvM\NvM_Crc.c	   319   */
; ..\eeprom\NvM\NvM_Crc.c	   320  FUNC(void, NVM_PRIVATE_CODE) NvM_CrcQueueInit(void)
; ..\eeprom\NvM\NvM_Crc.c	   321  {
; ..\eeprom\NvM\NvM_Crc.c	   322      uint16 counter = (NvM_CrcQueueSize_u16 + NVM_CRC_QUEUE_BITINDEX_MASK) >> NVM_CRC_QUEUE_ENTRY_SHIFT;
; ..\eeprom\NvM\NvM_Crc.c	   323  
; ..\eeprom\NvM\NvM_Crc.c	   324      do
; ..\eeprom\NvM\NvM_Crc.c	   325      {
; ..\eeprom\NvM\NvM_Crc.c	   326          --counter;
; ..\eeprom\NvM\NvM_Crc.c	   327          NvM_CrcQueue_at[counter] = 0u; /* SBSW_NvM_AccessCrcQueue */
; ..\eeprom\NvM\NvM_Crc.c	   328      } while (counter > 0u);
; ..\eeprom\NvM\NvM_Crc.c	   329  
; ..\eeprom\NvM\NvM_Crc.c	   330      /* start queue scan with block 1, since block ID 0 is a reserved block, never requesting CRC re-calculation */
; ..\eeprom\NvM\NvM_Crc.c	   331      NvM_CrcQueueScanStart_u16 = 1u;
; ..\eeprom\NvM\NvM_Crc.c	   332  }
; ..\eeprom\NvM\NvM_Crc.c	   333  
; ..\eeprom\NvM\NvM_Crc.c	   334  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   335  *  NvM_CrcQueueJob
; ..\eeprom\NvM\NvM_Crc.c	   336  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   337  /*!
; ..\eeprom\NvM\NvM_Crc.c	   338   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   339   *
; ..\eeprom\NvM\NvM_Crc.c	   340   *
; ..\eeprom\NvM\NvM_Crc.c	   341   *
; ..\eeprom\NvM\NvM_Crc.c	   342   *
; ..\eeprom\NvM\NvM_Crc.c	   343   */
; ..\eeprom\NvM\NvM_Crc.c	   344  FUNC(void, NVM_PRIVATE_CODE) NvM_CrcQueueJob(NvM_BlockIdType BlockId)
; ..\eeprom\NvM\NvM_Crc.c	   345  {
; ..\eeprom\NvM\NvM_Crc.c	   346      /* perform calculations outside of Critical section, in order to make it as short as possible */
; ..\eeprom\NvM\NvM_Crc.c	   347      const NvM_CrcQueueEntryType bitMask = (((NvM_CrcQueueEntryType)1u) << (BlockId & NVM_CRC_QUEUE_BITINDEX_MASK));
; ..\eeprom\NvM\NvM_Crc.c	   348      const NvM_CrcQueueEntryPtr queueEntry = &NvM_CrcQueue_at[BlockId >> NVM_CRC_QUEUE_ENTRY_SHIFT];
; ..\eeprom\NvM\NvM_Crc.c	   349  
; ..\eeprom\NvM\NvM_Crc.c	   350      NvM_EnterCriticalSection();
; ..\eeprom\NvM\NvM_Crc.c	   351  
; ..\eeprom\NvM\NvM_Crc.c	   352      /* #21 process the actual queuing (set corresponding bit) */
; ..\eeprom\NvM\NvM_Crc.c	   353      *queueEntry |= bitMask; /* SBSW_NvM_AccessCrcQueue */
; ..\eeprom\NvM\NvM_Crc.c	   354  
; ..\eeprom\NvM\NvM_Crc.c	   355      NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM_Crc.c	   356  }
; ..\eeprom\NvM\NvM_Crc.c	   357  
; ..\eeprom\NvM\NvM_Crc.c	   358  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   359  *  NvM_CrcGetQueuedBlockId
; ..\eeprom\NvM\NvM_Crc.c	   360  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   361  /*!
; ..\eeprom\NvM\NvM_Crc.c	   362   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   363   *
; ..\eeprom\NvM\NvM_Crc.c	   364   *
; ..\eeprom\NvM\NvM_Crc.c	   365   *
; ..\eeprom\NvM\NvM_Crc.c	   366   *
; ..\eeprom\NvM\NvM_Crc.c	   367   *
; ..\eeprom\NvM\NvM_Crc.c	   368   *
; ..\eeprom\NvM\NvM_Crc.c	   369   *
; ..\eeprom\NvM\NvM_Crc.c	   370   *
; ..\eeprom\NvM\NvM_Crc.c	   371   *
; ..\eeprom\NvM\NvM_Crc.c	   372   *
; ..\eeprom\NvM\NvM_Crc.c	   373   */
; ..\eeprom\NvM\NvM_Crc.c	   374  FUNC(NvM_BlockIdType, NVM_PRIVATE_CODE) NvM_CrcGetQueuedBlockId(void)
; ..\eeprom\NvM\NvM_Crc.c	   375  {
; ..\eeprom\NvM\NvM_Crc.c	   376      /* #10 Setup required values */
; ..\eeprom\NvM\NvM_Crc.c	   377  
; ..\eeprom\NvM\NvM_Crc.c	   378      /* set the block Id whether to begin searching - set in previous search correctly or begin from block Id 1 */
; ..\eeprom\NvM\NvM_Crc.c	   379      uint16 newBlockId = (NvM_CrcQueueScanStart_u16 < NvM_CrcQueueSize_u16) ? NvM_CrcQueueScanStart_u16 : 1u;
; ..\eeprom\NvM\NvM_Crc.c	   380      /* calculate number of queue elements (each with 32 bits) */
; ..\eeprom\NvM\NvM_Crc.c	   381      const uint16 queueSizeWords = (NvM_CrcQueueSize_u16 + NVM_CRC_QUEUE_BITINDEX_MASK) >> NVM_CRC_QUEUE_ENTRY_SHIFT;
; ..\eeprom\NvM\NvM_Crc.c	   382  
; ..\eeprom\NvM\NvM_Crc.c	   383      uint8 bitPos = (uint8)(newBlockId & NVM_CRC_QUEUE_BITINDEX_MASK);
; ..\eeprom\NvM\NvM_Crc.c	   384      const NvM_CrcQueueEntryType entryMask = ~((((NvM_CrcQueueEntryType)1u) << bitPos) - 1u);
; ..\eeprom\NvM\NvM_Crc.c	   385  
; ..\eeprom\NvM\NvM_Crc.c	   386      /* shift out lowest 5 bits to get the queue element with current newBlockId */
; ..\eeprom\NvM\NvM_Crc.c	   387      newBlockId >>= NVM_CRC_QUEUE_ENTRY_SHIFT;
; ..\eeprom\NvM\NvM_Crc.c	   388  
; ..\eeprom\NvM\NvM_Crc.c	   389      bitPos = NvM_CrcQueueCountTrailingZeros(entryMask & NvM_CrcQueue_at[newBlockId]);
; ..\eeprom\NvM\NvM_Crc.c	   390  
; ..\eeprom\NvM\NvM_Crc.c	   391      /* #20 search for queue element which includes at least one set bit (queued block) */
; ..\eeprom\NvM\NvM_Crc.c	   392  
; ..\eeprom\NvM\NvM_Crc.c	   393      /* search for queue element with at least one bit != 0 */
; ..\eeprom\NvM\NvM_Crc.c	   394      while (bitPos >= NVM_CRC_QUEUE_ENTRY_BITS)
; ..\eeprom\NvM\NvM_Crc.c	   395      {
; ..\eeprom\NvM\NvM_Crc.c	   396          /* we already checked the first queue element, now check the next one */
; ..\eeprom\NvM\NvM_Crc.c	   397          newBlockId++;
; ..\eeprom\NvM\NvM_Crc.c	   398  
; ..\eeprom\NvM\NvM_Crc.c	   399          /* Crc queue end reached, abort */
; ..\eeprom\NvM\NvM_Crc.c	   400          if (newBlockId >= queueSizeWords)
; ..\eeprom\NvM\NvM_Crc.c	   401          {
; ..\eeprom\NvM\NvM_Crc.c	   402              NvM_CrcQueueScanStart_u16 = NvM_CrcQueueSize_u16;
; ..\eeprom\NvM\NvM_Crc.c	   403              break;
; ..\eeprom\NvM\NvM_Crc.c	   404          }
; ..\eeprom\NvM\NvM_Crc.c	   405  
; ..\eeprom\NvM\NvM_Crc.c	   406          /* check current queue element for set bits */
; ..\eeprom\NvM\NvM_Crc.c	   407          bitPos = NvM_CrcQueueCountTrailingZeros(NvM_CrcQueue_at[newBlockId]);
; ..\eeprom\NvM\NvM_Crc.c	   408      }
; ..\eeprom\NvM\NvM_Crc.c	   409  
; ..\eeprom\NvM\NvM_Crc.c	   410      /* #30 if an queue element with queued block Id was found */
; ..\eeprom\NvM\NvM_Crc.c	   411      if(newBlockId < queueSizeWords)
; ..\eeprom\NvM\NvM_Crc.c	   412      {
; ..\eeprom\NvM\NvM_Crc.c	   413          NvM_EnterCriticalSection();
; ..\eeprom\NvM\NvM_Crc.c	   414  
; ..\eeprom\NvM\NvM_Crc.c	   415          /* clear the Block's corresponding bit, since it was set (and cannot be cleared elsewhere, we can use XOR) */
; ..\eeprom\NvM\NvM_Crc.c	   416          NvM_CrcQueue_at[newBlockId] ^= (((NvM_CrcQueueEntryType)1u) << bitPos); /* SBSW_NvM_AccessCrcQueue */
; ..\eeprom\NvM\NvM_Crc.c	   417  
; ..\eeprom\NvM\NvM_Crc.c	   418          NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM_Crc.c	   419  
; ..\eeprom\NvM\NvM_Crc.c	   420          /* set newBlockId to first block within queue element */
; ..\eeprom\NvM\NvM_Crc.c	   421          newBlockId <<= NVM_CRC_QUEUE_ENTRY_SHIFT;
; ..\eeprom\NvM\NvM_Crc.c	   422          /* calculate the real set block Id from newBlockId and the set bit within queue element */
; ..\eeprom\NvM\NvM_Crc.c	   423          newBlockId |= bitPos;
; ..\eeprom\NvM\NvM_Crc.c	   424  
; ..\eeprom\NvM\NvM_Crc.c	   425          /* update Block Id where to start the next queue scan, wrap around will be handled when starting the next scan */
; ..\eeprom\NvM\NvM_Crc.c	   426          NvM_CrcQueueScanStart_u16 = (NvM_BlockIdType)(newBlockId + 1u);
; ..\eeprom\NvM\NvM_Crc.c	   427      }
; ..\eeprom\NvM\NvM_Crc.c	   428      /* #40 if no queue element with queued block Id was found */
; ..\eeprom\NvM\NvM_Crc.c	   429      else
; ..\eeprom\NvM\NvM_Crc.c	   430      {
; ..\eeprom\NvM\NvM_Crc.c	   431          newBlockId = 0u;
; ..\eeprom\NvM\NvM_Crc.c	   432      }
; ..\eeprom\NvM\NvM_Crc.c	   433  
; ..\eeprom\NvM\NvM_Crc.c	   434      /* #50 return next block Id to calculate Crc for */
; ..\eeprom\NvM\NvM_Crc.c	   435      return (NvM_BlockIdType)newBlockId;
; ..\eeprom\NvM\NvM_Crc.c	   436  }
; ..\eeprom\NvM\NvM_Crc.c	   437  
; ..\eeprom\NvM\NvM_Crc.c	   438  /* Count the number of cleared bits after the least significant bit that is set *
; ..\eeprom\NvM\NvM_Crc.c	   439   * It works either 32bit or 16bit words (depending on platform)                 */
; ..\eeprom\NvM\NvM_Crc.c	   440  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   441  *  NvM_CrcQueueCountTrailingZeros
; ..\eeprom\NvM\NvM_Crc.c	   442  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   443  /*!
; ..\eeprom\NvM\NvM_Crc.c	   444   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   445   *
; ..\eeprom\NvM\NvM_Crc.c	   446   *
; ..\eeprom\NvM\NvM_Crc.c	   447   *
; ..\eeprom\NvM\NvM_Crc.c	   448   */
; ..\eeprom\NvM\NvM_Crc.c	   449  NVM_LOCAL uint8 NvM_CrcQueueCountTrailingZeros(NvM_CrcQueueEntryType Word)
; ..\eeprom\NvM\NvM_Crc.c	   450  {
; ..\eeprom\NvM\NvM_Crc.c	   451      uint8 trailingZeroes = 0u;
; ..\eeprom\NvM\NvM_Crc.c	   452  
; ..\eeprom\NvM\NvM_Crc.c	   453      if(Word == 0u)
; ..\eeprom\NvM\NvM_Crc.c	   454      {   /* special condition -> avoid going through the divide and conquer algorithm below.  *
; ..\eeprom\NvM\NvM_Crc.c	   455           * => it would require additional handling at the remaining two bits.               */
; ..\eeprom\NvM\NvM_Crc.c	   456          trailingZeroes = NVM_CRC_QUEUE_ENTRY_BITS;
; ..\eeprom\NvM\NvM_Crc.c	   457      }
; ..\eeprom\NvM\NvM_Crc.c	   458      else
; ..\eeprom\NvM\NvM_Crc.c	   459      {
; ..\eeprom\NvM\NvM_Crc.c	   460          NvM_CrcQueueEntryType currWord = Word;
; ..\eeprom\NvM\NvM_Crc.c	   461          /* is none of the lower 16 bits set? */
; ..\eeprom\NvM\NvM_Crc.c	   462          if((currWord & 0xFFFFu) == 0u)
; ..\eeprom\NvM\NvM_Crc.c	   463          {
; ..\eeprom\NvM\NvM_Crc.c	   464              trailingZeroes |= 0x10u;
; ..\eeprom\NvM\NvM_Crc.c	   465              currWord >>= 16u;
; ..\eeprom\NvM\NvM_Crc.c	   466          }
; ..\eeprom\NvM\NvM_Crc.c	   467  
; ..\eeprom\NvM\NvM_Crc.c	   468          /* is none of the lower 8bits set? */
; ..\eeprom\NvM\NvM_Crc.c	   469          if((currWord & 0xFFu) == 0u)
; ..\eeprom\NvM\NvM_Crc.c	   470          {
; ..\eeprom\NvM\NvM_Crc.c	   471              trailingZeroes |= 0x08u;
; ..\eeprom\NvM\NvM_Crc.c	   472              currWord >>= 8u;
; ..\eeprom\NvM\NvM_Crc.c	   473          }
; ..\eeprom\NvM\NvM_Crc.c	   474  
; ..\eeprom\NvM\NvM_Crc.c	   475          /* is none of the lower 4 bits set?*/
; ..\eeprom\NvM\NvM_Crc.c	   476          if((currWord & 0x0Fu) == 0u)
; ..\eeprom\NvM\NvM_Crc.c	   477          {
; ..\eeprom\NvM\NvM_Crc.c	   478              trailingZeroes |= 0x04u;
; ..\eeprom\NvM\NvM_Crc.c	   479              currWord >>= 4u;
; ..\eeprom\NvM\NvM_Crc.c	   480          }
; ..\eeprom\NvM\NvM_Crc.c	   481  
; ..\eeprom\NvM\NvM_Crc.c	   482          /* is none of the lower 2 bits set? */
; ..\eeprom\NvM\NvM_Crc.c	   483          if((currWord & 0x03u) == 0u)
; ..\eeprom\NvM\NvM_Crc.c	   484          {
; ..\eeprom\NvM\NvM_Crc.c	   485              trailingZeroes |= 2u;
; ..\eeprom\NvM\NvM_Crc.c	   486              currWord >>= 2u;
; ..\eeprom\NvM\NvM_Crc.c	   487          }
; ..\eeprom\NvM\NvM_Crc.c	   488  
; ..\eeprom\NvM\NvM_Crc.c	   489          /* Process least significant bit.
; ..\eeprom\NvM\NvM_Crc.c	   490          * If the least significant bit is zero, add 1, because the second one cannot be cleared
; ..\eeprom\NvM\NvM_Crc.c	   491          * Initially, we checked for word == 0, therefore one of both bits must be set here
; ..\eeprom\NvM\NvM_Crc.c	   492          * If LSB is set, add nothing.
; ..\eeprom\NvM\NvM_Crc.c	   493          * The cast appears to be unnecessary. However, some Compilers might issue a warning,
; ..\eeprom\NvM\NvM_Crc.c	   494          * if uint16_least is 16bit, while QueueBitMask is 32bit width */
; ..\eeprom\NvM\NvM_Crc.c	   495          trailingZeroes |= (uint8)((currWord & 1u) ^ 1u);
; ..\eeprom\NvM\NvM_Crc.c	   496      }
; ..\eeprom\NvM\NvM_Crc.c	   497  
; ..\eeprom\NvM\NvM_Crc.c	   498      return trailingZeroes;
; ..\eeprom\NvM\NvM_Crc.c	   499  }
; ..\eeprom\NvM\NvM_Crc.c	   500  
; ..\eeprom\NvM\NvM_Crc.c	   501  # define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_Crc.c	   502    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   503  
; ..\eeprom\NvM\NvM_Crc.c	   504  #endif /* ((NVM_SET_RAM_BLOCK_STATUS_API == STD_ON) && (NVM_CALC_RAM_CRC_USED == STD_ON)) */
; ..\eeprom\NvM\NvM_Crc.c	   505  
; ..\eeprom\NvM\NvM_Crc.c	   506  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_Crc.c	   507    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   508  
; ..\eeprom\NvM\NvM_Crc.c	   509  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   510  *  NvM_CrcJob_Create
; ..\eeprom\NvM\NvM_Crc.c	   511  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   512  /*!
; ..\eeprom\NvM\NvM_Crc.c	   513   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   514   *
; ..\eeprom\NvM\NvM_Crc.c	   515   *
; ..\eeprom\NvM\NvM_Crc.c	   516   *
; ..\eeprom\NvM\NvM_Crc.c	   517   *
; ..\eeprom\NvM\NvM_Crc.c	   518   *
; ..\eeprom\NvM\NvM_Crc.c	   519   */
; ..\eeprom\NvM\NvM_Crc.c	   520  FUNC(void, NVM_PRIVATE_CODE) NvM_CrcJob_Create(
; Function NvM_CrcJob_Create
.L20:
NvM_CrcJob_Create:	.type	func

; ..\eeprom\NvM\NvM_Crc.c	   521      NvM_CrcJobPtrType Self, NvM_BlockIdType BlockId, NvM_RamAddressType RamDataPtr, uint16 DataLength)
; ..\eeprom\NvM\NvM_Crc.c	   522  {
; ..\eeprom\NvM\NvM_Crc.c	   523      const NvM_BlockDescrPtrType descr_pt = &(NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)]);
	insert	d15,d4,#0,#15,#17
	movh.a	a15,#@his(NvM_BlockDescriptorTable_at)
	lea	a15,[a15]@los(NvM_BlockDescriptorTable_at)
.L190:
	sha	d15,#6
.L191:
	addsc.a	a15,a15,d15,#0
.L192:

; ..\eeprom\NvM\NvM_Crc.c	   524  
; ..\eeprom\NvM\NvM_Crc.c	   525      Self->HandlerInstance_pt = NvM_CrcHandlerTable_at[descr_pt->CrcSettings]; /* SBSW_NvM_AccessCrcJobPtr */
	ld.bu	d15,[a15]58
.L193:
	movh.a	a15,#@his(NvM_CrcHandlerTable_at)
.L194:
	extr.u	d15,d15,#6,#2
	lea	a15,[a15]@los(NvM_CrcHandlerTable_at)
.L195:
	addsc.a	a15,a15,d15,#2
	ld.a	a15,[a15]
.L196:
	st.a	[a4]12,a15
.L197:

; ..\eeprom\NvM\NvM_Crc.c	   526      Self->CurrentCrcValue = Self->HandlerInstance_pt->initialCrcValue; /* SBSW_NvM_AccessCrcJobPtr */
	ld.w	d15,[a15]12
.L198:
	st.w	[a4],d15
.L199:

; ..\eeprom\NvM\NvM_Crc.c	   527      Self->RamData_pt = RamDataPtr; /* SBSW_NvM_AccessCrcJobPtr */
	st.a	[a4]4,a5
.L200:

; ..\eeprom\NvM\NvM_Crc.c	   528      Self->CrcBuffer = (RamDataPtr != NULL_PTR) ? (&RamDataPtr[DataLength]) : NULL_PTR; /* SBSW_NvM_AccessCrcJobPtr */
	jz.a	a5,.L2
.L201:
	addsc.a	a2,a5,d5,#0
.L202:
	j	.L3
.L2:
	mov.a	a2,#0
.L3:
	st.a	[a4]8,a2
.L203:

; ..\eeprom\NvM\NvM_Crc.c	   529      /* Let an CRC job complete immediately, if there's actually no CRC, or if there are no RAM data. */
; ..\eeprom\NvM\NvM_Crc.c	   530      Self->RemainingLength_u16 = /* SBSW_NvM_AccessCrcJobPtr */
; ..\eeprom\NvM\NvM_Crc.c	   531          ((Self->HandlerInstance_pt->crcLength > 0u) && (RamDataPtr != NULL_PTR)) ? DataLength : 0u;
	ld.bu	d15,[a15]16
.L204:
	jeq	d15,#0,.L4
.L205:
	jnz.a	a5,.L6
.L4:
	mov	d5,#0
.L6:
	st.h	[a4]16,d5
.L206:

; ..\eeprom\NvM\NvM_Crc.c	   532  
; ..\eeprom\NvM\NvM_Crc.c	   533  #if(NVM_USE_BLOCK_ID_CHECK == STD_ON)
; ..\eeprom\NvM\NvM_Crc.c	   534      if(descr_pt->CrcSettings != NVM_BLOCK_USE_CRC_OFF)
; ..\eeprom\NvM\NvM_Crc.c	   535      {
; ..\eeprom\NvM\NvM_Crc.c	   536          /* Always use the origin block Id - otherwise the next origin block read will deliver data for the DCM block Id and the
; ..\eeprom\NvM\NvM_Crc.c	   537           * Crc comparison fails. */
; ..\eeprom\NvM\NvM_Crc.c	   538          NvM_BlockIdType originBlockId = NVM_BLOCK_FROM_DCM_ID(BlockId);
; ..\eeprom\NvM\NvM_Crc.c	   539          const NvM_RamMngmtPtrType ramMngmt_pt =
; ..\eeprom\NvM\NvM_Crc.c	   540              (originBlockId != BlockId) ? (&NvM_DcmBlockMngmt_t) : (&NvM_BlockMngmtArea_at[BlockId]); /* COV_NVM_APICFGCLASS */
; ..\eeprom\NvM\NvM_Crc.c	   541  
; ..\eeprom\NvM\NvM_Crc.c	   542          uint8 ramData[3];
; ..\eeprom\NvM\NvM_Crc.c	   543          ramData[0] = (uint8)(originBlockId >> 8); /* SBSW_NvM_AccessArray_BlockIdInCrc */
; ..\eeprom\NvM\NvM_Crc.c	   544          ramData[1] = (uint8)originBlockId;  /* SBSW_NvM_AccessArray_BlockIdInCrc */
; ..\eeprom\NvM\NvM_Crc.c	   545          ramData[2] = (uint8)ramMngmt_pt->NvDataIndex_t; /* SBSW_NvM_AccessArray_BlockIdInCrc */
; ..\eeprom\NvM\NvM_Crc.c	   546  
; ..\eeprom\NvM\NvM_Crc.c	   547          Self->HandlerInstance_pt->calc(ramData, 3, &Self->CurrentCrcValue); /* SBSW_NvM_FuncPtrCall_CrcHandler */
; ..\eeprom\NvM\NvM_Crc.c	   548      }
; ..\eeprom\NvM\NvM_Crc.c	   549  #endif
; ..\eeprom\NvM\NvM_Crc.c	   550  }
	ret
.L113:
	
__NvM_CrcJob_Create_function_end:
	.size	NvM_CrcJob_Create,__NvM_CrcJob_Create_function_end-NvM_CrcJob_Create
.L51:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_CrcJob_Process',code,cluster('NvM_CrcJob_Process')
	.sect	'.text.NvM_Crc.NvM_CrcJob_Process'
	.align	2
	
	.global	NvM_CrcJob_Process

; ..\eeprom\NvM\NvM_Crc.c	   551  
; ..\eeprom\NvM\NvM_Crc.c	   552  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   553  *  NvM_CrcJob_Process
; ..\eeprom\NvM\NvM_Crc.c	   554  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   555  /*!
; ..\eeprom\NvM\NvM_Crc.c	   556   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   557   *
; ..\eeprom\NvM\NvM_Crc.c	   558   *
; ..\eeprom\NvM\NvM_Crc.c	   559   *
; ..\eeprom\NvM\NvM_Crc.c	   560   */
; ..\eeprom\NvM\NvM_Crc.c	   561  FUNC(void, NVM_PRIVATE_CODE) NvM_CrcJob_Process(NvM_CrcJobPtrType Self, uint16 ProcessLength)
; Function NvM_CrcJob_Process
.L22:
NvM_CrcJob_Process:	.type	func
	mov.aa	a5,a4
.L171:

; ..\eeprom\NvM\NvM_Crc.c	   562  {
; ..\eeprom\NvM\NvM_Crc.c	   563      if(Self->RemainingLength_u16 > 0u) /* COV_NVM_COVEREDINOTHERCFG */
	ld.hu	d15,[a5]16
.L211:
	jeq	d15,#0,.L7
.L124:

; ..\eeprom\NvM\NvM_Crc.c	   564      {
; ..\eeprom\NvM\NvM_Crc.c	   565          const NvM_ConstRamAddressType currRamPtr = Self->RamData_pt;
	ld.a	a4,[a5]4
.L170:

; ..\eeprom\NvM\NvM_Crc.c	   566          uint16 currLength = Self->RemainingLength_u16;
; ..\eeprom\NvM\NvM_Crc.c	   567  
; ..\eeprom\NvM\NvM_Crc.c	   568          /* if both value are equal, we would not need to adapt currentLength, but finishJob needs to be set. */
; ..\eeprom\NvM\NvM_Crc.c	   569          if(currLength > ProcessLength)
	min.u	d4,d15,d4
.L169:

; ..\eeprom\NvM\NvM_Crc.c	   570          {
; ..\eeprom\NvM\NvM_Crc.c	   571              currLength = ProcessLength;
; ..\eeprom\NvM\NvM_Crc.c	   572          }
; ..\eeprom\NvM\NvM_Crc.c	   573  
; ..\eeprom\NvM\NvM_Crc.c	   574          Self->RemainingLength_u16 -= currLength; /* SBSW_NvM_AccessCrcJobPtr */
	sub	d15,d4
	st.h	[a5]16,d15
.L212:

; ..\eeprom\NvM\NvM_Crc.c	   575  
; ..\eeprom\NvM\NvM_Crc.c	   576          /* set RamAddress for next calculation cycle */
; ..\eeprom\NvM\NvM_Crc.c	   577          Self->RamData_pt = &currRamPtr[currLength];  /* SBSW_NvM_AccessCrcJobPtr */
	addsc.a	a15,a4,d4,#0
.L213:
	st.a	[a5]4,a15
.L214:

; ..\eeprom\NvM\NvM_Crc.c	   578  
; ..\eeprom\NvM\NvM_Crc.c	   579          Self->HandlerInstance_pt->calc(currRamPtr, currLength, &Self->CurrentCrcValue); /* SBSW_NvM_FuncPtrCall_CrcHandler */
	ld.a	a15,[a5]12
.L215:
	ld.a	a15,[a15]
.L216:
	ji	a15
.L7:

; ..\eeprom\NvM\NvM_Crc.c	   580      }
; ..\eeprom\NvM\NvM_Crc.c	   581  }
	ret
.L121:
	
__NvM_CrcJob_Process_function_end:
	.size	NvM_CrcJob_Process,__NvM_CrcJob_Process_function_end-NvM_CrcJob_Process
.L56:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_CrcJob_Compare',code,cluster('NvM_CrcJob_Compare')
	.sect	'.text.NvM_Crc.NvM_CrcJob_Compare'
	.align	2
	
	.global	NvM_CrcJob_Compare

; ..\eeprom\NvM\NvM_Crc.c	   582  
; ..\eeprom\NvM\NvM_Crc.c	   583  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   584  *  NvM_CrcJob_Compare
; ..\eeprom\NvM\NvM_Crc.c	   585  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   586  /*!
; ..\eeprom\NvM\NvM_Crc.c	   587   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   588   *
; ..\eeprom\NvM\NvM_Crc.c	   589   *
; ..\eeprom\NvM\NvM_Crc.c	   590   *
; ..\eeprom\NvM\NvM_Crc.c	   591   *
; ..\eeprom\NvM\NvM_Crc.c	   592   */
; ..\eeprom\NvM\NvM_Crc.c	   593  FUNC(boolean, NVM_PRIVATE_CODE) NvM_CrcJob_Compare(NvM_CrcJobConstPtrType Self)
; Function NvM_CrcJob_Compare
.L24:
NvM_CrcJob_Compare:	.type	func
	mov.aa	a5,a4
.L173:

; ..\eeprom\NvM\NvM_Crc.c	   594  {
; ..\eeprom\NvM\NvM_Crc.c	   595      boolean result = FALSE;
; ..\eeprom\NvM\NvM_Crc.c	   596  
; ..\eeprom\NvM\NvM_Crc.c	   597      if(Self->CrcBuffer != NULL_PTR)
	ld.a	a4,[a5]8
.L172:
	mov	d2,#0
.L174:
	jz.a	a4,.L8
.L228:

; ..\eeprom\NvM\NvM_Crc.c	   598      {
; ..\eeprom\NvM\NvM_Crc.c	   599          result = (Self->HandlerInstance_pt->compare(Self->CrcBuffer, (NvM_CrcValueRefType)&Self->CurrentCrcValue)); /* SBSW_NvM_FuncPtrCall_CrcHandler */
	ld.a	a15,[a5]12
.L229:
	ld.a	a15,[a15]4
.L230:
	ji	a15
.L8:

; ..\eeprom\NvM\NvM_Crc.c	   600      }
; ..\eeprom\NvM\NvM_Crc.c	   601  
; ..\eeprom\NvM\NvM_Crc.c	   602      return result;
; ..\eeprom\NvM\NvM_Crc.c	   603  }
	ret
.L132:
	
__NvM_CrcJob_Compare_function_end:
	.size	NvM_CrcJob_Compare,__NvM_CrcJob_Compare_function_end-NvM_CrcJob_Compare
.L66:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_CrcJob_CopyToBuffer',code,cluster('NvM_CrcJob_CopyToBuffer')
	.sect	'.text.NvM_Crc.NvM_CrcJob_CopyToBuffer'
	.align	2
	
	.global	NvM_CrcJob_CopyToBuffer

; ..\eeprom\NvM\NvM_Crc.c	   604  
; ..\eeprom\NvM\NvM_Crc.c	   605  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   606  *  NvM_CrcJob_CopyToBuffer
; ..\eeprom\NvM\NvM_Crc.c	   607  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   608  /*!
; ..\eeprom\NvM\NvM_Crc.c	   609   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   610   *
; ..\eeprom\NvM\NvM_Crc.c	   611   *
; ..\eeprom\NvM\NvM_Crc.c	   612   *
; ..\eeprom\NvM\NvM_Crc.c	   613   */
; ..\eeprom\NvM\NvM_Crc.c	   614  FUNC(void, NVM_PRIVATE_CODE) NvM_CrcJob_CopyToBuffer(NvM_CrcJobConstPtrType Self)
; Function NvM_CrcJob_CopyToBuffer
.L26:
NvM_CrcJob_CopyToBuffer:	.type	func
	mov.aa	a5,a4
.L176:

; ..\eeprom\NvM\NvM_Crc.c	   615  {
; ..\eeprom\NvM\NvM_Crc.c	   616      if(Self->CrcBuffer != NULL_PTR)
	ld.a	a4,[a5]8
.L175:
	jz.a	a4,.L10
.L221:

; ..\eeprom\NvM\NvM_Crc.c	   617      {
; ..\eeprom\NvM\NvM_Crc.c	   618          Self->HandlerInstance_pt->copyToBuffer(Self->CrcBuffer, (NvM_CrcValueRefType)&Self->CurrentCrcValue); /* SBSW_NvM_FuncPtrCall_CrcHandler */
	ld.a	a15,[a5]12
.L222:
	ld.a	a15,[a15]8
.L223:
	ji	a15
.L10:

; ..\eeprom\NvM\NvM_Crc.c	   619      }
; ..\eeprom\NvM\NvM_Crc.c	   620  }
	ret
.L128:
	
__NvM_CrcJob_CopyToBuffer_function_end:
	.size	NvM_CrcJob_CopyToBuffer,__NvM_CrcJob_CopyToBuffer_function_end-NvM_CrcJob_CopyToBuffer
.L61:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_CrcJob_ExportBufferedValue',code,cluster('NvM_CrcJob_ExportBufferedValue')
	.sect	'.text.NvM_Crc.NvM_CrcJob_ExportBufferedValue'
	.align	2
	
	.global	NvM_CrcJob_ExportBufferedValue

; ..\eeprom\NvM\NvM_Crc.c	   621  
; ..\eeprom\NvM\NvM_Crc.c	   622  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   623  *  NvM_CrcJob_ExportBufferedValue
; ..\eeprom\NvM\NvM_Crc.c	   624  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   625  /*!
; ..\eeprom\NvM\NvM_Crc.c	   626   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   627   *
; ..\eeprom\NvM\NvM_Crc.c	   628   *
; ..\eeprom\NvM\NvM_Crc.c	   629   *
; ..\eeprom\NvM\NvM_Crc.c	   630   *
; ..\eeprom\NvM\NvM_Crc.c	   631   */
; ..\eeprom\NvM\NvM_Crc.c	   632  FUNC(void, NVM_PRIVATE_CODE) NvM_CrcJob_ExportBufferedValue(NvM_CrcJobConstPtrType Self, NvM_CrcBufferPtrType DestPtr)
; Function NvM_CrcJob_ExportBufferedValue
.L28:
NvM_CrcJob_ExportBufferedValue:	.type	func

; ..\eeprom\NvM\NvM_Crc.c	   633  {
; ..\eeprom\NvM\NvM_Crc.c	   634      if((DestPtr != NULL_PTR) && (Self->CrcBuffer != NULL_PTR))
	jz.a	a5,.L11
.L235:
	ld.a	a2,[a4]8
.L236:
	jz.a	a2,.L12
.L237:

; ..\eeprom\NvM\NvM_Crc.c	   635      {
; ..\eeprom\NvM\NvM_Crc.c	   636          Self->HandlerInstance_pt->copyToBuffer(DestPtr, Self->CrcBuffer); /* SBSW_NvM_FuncPtrCall_CrcHandler */
	ld.a	a15,[a4]12
.L238:
	ld.a	a15,[a15]8
.L239:
	mov.aa	a4,a5
.L178:
	mov.aa	a5,a2
.L177:
	ji	a15
.L12:
.L11:

; ..\eeprom\NvM\NvM_Crc.c	   637      }
; ..\eeprom\NvM\NvM_Crc.c	   638  }
	ret
.L135:
	
__NvM_CrcJob_ExportBufferedValue_function_end:
	.size	NvM_CrcJob_ExportBufferedValue,__NvM_CrcJob_ExportBufferedValue_function_end-NvM_CrcJob_ExportBufferedValue
.L71:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_CrcJob_ImportBufferedValue',code,cluster('NvM_CrcJob_ImportBufferedValue')
	.sect	'.text.NvM_Crc.NvM_CrcJob_ImportBufferedValue'
	.align	2
	
	.global	NvM_CrcJob_ImportBufferedValue

; ..\eeprom\NvM\NvM_Crc.c	   639  
; ..\eeprom\NvM\NvM_Crc.c	   640  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   641  *  NvM_CrcJob_ImportBufferedValue
; ..\eeprom\NvM\NvM_Crc.c	   642  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   643  /*!
; ..\eeprom\NvM\NvM_Crc.c	   644   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   645   *
; ..\eeprom\NvM\NvM_Crc.c	   646   *
; ..\eeprom\NvM\NvM_Crc.c	   647   */
; ..\eeprom\NvM\NvM_Crc.c	   648  FUNC(void, NVM_PRIVATE_CODE) NvM_CrcJob_ImportBufferedValue(NvM_CrcJobConstPtrType Self, NvM_CrcBufferConstPtrType SrcPtr)
; Function NvM_CrcJob_ImportBufferedValue
.L30:
NvM_CrcJob_ImportBufferedValue:	.type	func

; ..\eeprom\NvM\NvM_Crc.c	   649  {
; ..\eeprom\NvM\NvM_Crc.c	   650      if((Self->CrcBuffer != NULL_PTR) && (SrcPtr != NULL_PTR))
	ld.a	a2,[a4]8
.L244:
	jz.a	a2,.L13
.L245:
	jz.a	a5,.L14
.L246:

; ..\eeprom\NvM\NvM_Crc.c	   651      {
; ..\eeprom\NvM\NvM_Crc.c	   652          Self->HandlerInstance_pt->copyToBuffer(Self->CrcBuffer, SrcPtr); /* SBSW_NvM_FuncPtrCall_CrcHandler */
	ld.a	a15,[a4]12
.L247:
	ld.a	a15,[a15]8
.L248:
	mov.aa	a4,a2
.L179:
	ji	a15
.L14:
.L13:

; ..\eeprom\NvM\NvM_Crc.c	   653      }
; ..\eeprom\NvM\NvM_Crc.c	   654  }
	ret
.L139:
	
__NvM_CrcJob_ImportBufferedValue_function_end:
	.size	NvM_CrcJob_ImportBufferedValue,__NvM_CrcJob_ImportBufferedValue_function_end-NvM_CrcJob_ImportBufferedValue
.L76:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_Crc_NoCrc_Calculate',code,cluster('NvM_Crc_NoCrc_Calculate')
	.sect	'.text.NvM_Crc.NvM_Crc_NoCrc_Calculate'
	.align	2
	

; ..\eeprom\NvM\NvM_Crc.c	   655  
; ..\eeprom\NvM\NvM_Crc.c	   656  /********************* No-CRC Handler Functions *************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   657  
; ..\eeprom\NvM\NvM_Crc.c	   658  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   659  *  NvM_Crc_NoCrc_Calculate
; ..\eeprom\NvM\NvM_Crc.c	   660  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   661  /*!
; ..\eeprom\NvM\NvM_Crc.c	   662   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   663   *
; ..\eeprom\NvM\NvM_Crc.c	   664   *
; ..\eeprom\NvM\NvM_Crc.c	   665   */
; ..\eeprom\NvM\NvM_Crc.c	   666  /* PRQA S 3673 1 */ /* MD_NvM_8.13_NoCrcHandler */
; ..\eeprom\NvM\NvM_Crc.c	   667  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE)  NvM_Crc_NoCrc_Calculate(NvM_ConstRamAddressType DataPtr, uint16 Length, NvM_CrcValuePtrType CurrentValue) /* COV_NVM_NOCRCDUMMYHANDLER */
; Function NvM_Crc_NoCrc_Calculate
.L32:
NvM_Crc_NoCrc_Calculate:	.type	func

; ..\eeprom\NvM\NvM_Crc.c	   668  {
; ..\eeprom\NvM\NvM_Crc.c	   669      NVM_DUMMY_STATEMENT_CONST(DataPtr);
; ..\eeprom\NvM\NvM_Crc.c	   670      NVM_DUMMY_STATEMENT(Length);
; ..\eeprom\NvM\NvM_Crc.c	   671      NVM_DUMMY_STATEMENT(CurrentValue);
; ..\eeprom\NvM\NvM_Crc.c	   672  }
	ret
.L143:
	
__NvM_Crc_NoCrc_Calculate_function_end:
	.size	NvM_Crc_NoCrc_Calculate,__NvM_Crc_NoCrc_Calculate_function_end-NvM_Crc_NoCrc_Calculate
.L81:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_Crc_NoCrc_Compare',code,cluster('NvM_Crc_NoCrc_Compare')
	.sect	'.text.NvM_Crc.NvM_Crc_NoCrc_Compare'
	.align	2
	

; ..\eeprom\NvM\NvM_Crc.c	   673  
; ..\eeprom\NvM\NvM_Crc.c	   674  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   675  *  NvM_Crc_NoCrc_Compare
; ..\eeprom\NvM\NvM_Crc.c	   676  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   677  /*!
; ..\eeprom\NvM\NvM_Crc.c	   678   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   679   *
; ..\eeprom\NvM\NvM_Crc.c	   680   *
; ..\eeprom\NvM\NvM_Crc.c	   681   */
; ..\eeprom\NvM\NvM_Crc.c	   682  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_Crc_NoCrc_Compare(NvM_CrcBufferConstPtrType CrcBuff, NvM_CrcValueRefType CurrentValue) /* COV_NVM_NOCRCDUMMYHANDLER */
; Function NvM_Crc_NoCrc_Compare
.L34:
NvM_Crc_NoCrc_Compare:	.type	func

; ..\eeprom\NvM\NvM_Crc.c	   683  {
; ..\eeprom\NvM\NvM_Crc.c	   684      NVM_DUMMY_STATEMENT_CONST(CrcBuff);
; ..\eeprom\NvM\NvM_Crc.c	   685      NVM_DUMMY_STATEMENT_CONST(CurrentValue);
; ..\eeprom\NvM\NvM_Crc.c	   686  
; ..\eeprom\NvM\NvM_Crc.c	   687      return TRUE;
; ..\eeprom\NvM\NvM_Crc.c	   688  }
	mov	d2,#1
	ret
.L149:
	
__NvM_Crc_NoCrc_Compare_function_end:
	.size	NvM_Crc_NoCrc_Compare,__NvM_Crc_NoCrc_Compare_function_end-NvM_Crc_NoCrc_Compare
.L86:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_Crc_NoCrc_CopyToBuffer',code,cluster('NvM_Crc_NoCrc_CopyToBuffer')
	.sect	'.text.NvM_Crc.NvM_Crc_NoCrc_CopyToBuffer'
	.align	2
	

; ..\eeprom\NvM\NvM_Crc.c	   689  
; ..\eeprom\NvM\NvM_Crc.c	   690  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   691  *  NvM_Crc_NoCrc_CopyToBuffer
; ..\eeprom\NvM\NvM_Crc.c	   692  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   693  /*!
; ..\eeprom\NvM\NvM_Crc.c	   694   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   695   *
; ..\eeprom\NvM\NvM_Crc.c	   696   *
; ..\eeprom\NvM\NvM_Crc.c	   697   */
; ..\eeprom\NvM\NvM_Crc.c	   698  /* PRQA S 3673 1 */ /* MD_NvM_8.13_NoCrcHandler */
; ..\eeprom\NvM\NvM_Crc.c	   699  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_NoCrc_CopyToBuffer(NvM_CrcBufferPtrType Dest, NvM_CrcValueRefType Src)
; Function NvM_Crc_NoCrc_CopyToBuffer
.L36:
NvM_Crc_NoCrc_CopyToBuffer:	.type	func

; ..\eeprom\NvM\NvM_Crc.c	   700  {
; ..\eeprom\NvM\NvM_Crc.c	   701      NVM_DUMMY_STATEMENT(Dest);
; ..\eeprom\NvM\NvM_Crc.c	   702      NVM_DUMMY_STATEMENT_CONST(Src);
; ..\eeprom\NvM\NvM_Crc.c	   703  }
	ret
.L153:
	
__NvM_Crc_NoCrc_CopyToBuffer_function_end:
	.size	NvM_Crc_NoCrc_CopyToBuffer,__NvM_Crc_NoCrc_CopyToBuffer_function_end-NvM_Crc_NoCrc_CopyToBuffer
.L91:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_Crc_Crc16_Calculate',code,cluster('NvM_Crc_Crc16_Calculate')
	.sect	'.text.NvM_Crc.NvM_Crc_Crc16_Calculate'
	.align	2
	

; ..\eeprom\NvM\NvM_Crc.c	   704  
; ..\eeprom\NvM\NvM_Crc.c	   705  #if (NVM_USE_CRC16 == STD_ON) /* CRC16 handler, only available if there is at least one NvM block configured with CRC16 */
; ..\eeprom\NvM\NvM_Crc.c	   706  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   707  *  NvM_Crc_Crc16_Calculate
; ..\eeprom\NvM\NvM_Crc.c	   708  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   709  /*!
; ..\eeprom\NvM\NvM_Crc.c	   710   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   711   *
; ..\eeprom\NvM\NvM_Crc.c	   712   *
; ..\eeprom\NvM\NvM_Crc.c	   713   */
; ..\eeprom\NvM\NvM_Crc.c	   714  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE)  NvM_Crc_Crc16_Calculate(NvM_ConstRamAddressType DataPtr, uint16 Length, NvM_CrcValuePtrType CurrCrc)
; Function NvM_Crc_Crc16_Calculate
.L38:
NvM_Crc_Crc16_Calculate:	.type	func
	mov.aa	a15,a5
.L181:

; ..\eeprom\NvM\NvM_Crc.c	   715  {
; ..\eeprom\NvM\NvM_Crc.c	   716      *CurrCrc = Crc_CalculateCRC16(DataPtr, Length, (uint16)(*CurrCrc), FALSE); /* SBSW_NvM_FuncCall_CrcModule */ /* SBSW_NvM_AccessPtr_CrcValue */
	ld.w	d15,[a15]
.L265:
	mov	d6,#0
.L266:
	extr.u	d5,d15,#0,#16
	call	Crc_CalculateCRC16
.L180:
	st.w	[a15],d2
.L267:

; ..\eeprom\NvM\NvM_Crc.c	   717  }
	ret
.L156:
	
__NvM_Crc_Crc16_Calculate_function_end:
	.size	NvM_Crc_Crc16_Calculate,__NvM_Crc_Crc16_Calculate_function_end-NvM_Crc_Crc16_Calculate
.L96:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_Crc_Crc16_Compare',code,cluster('NvM_Crc_Crc16_Compare')
	.sect	'.text.NvM_Crc.NvM_Crc_Crc16_Compare'
	.align	2
	

; ..\eeprom\NvM\NvM_Crc.c	   718  
; ..\eeprom\NvM\NvM_Crc.c	   719  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   720  *  NvM_Crc_Crc16_Compare
; ..\eeprom\NvM\NvM_Crc.c	   721  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   722  /*!
; ..\eeprom\NvM\NvM_Crc.c	   723   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   724   *
; ..\eeprom\NvM\NvM_Crc.c	   725   *
; ..\eeprom\NvM\NvM_Crc.c	   726   */
; ..\eeprom\NvM\NvM_Crc.c	   727  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_Crc_Crc16_Compare(NvM_CrcBufferConstPtrType Crc1, NvM_CrcValueRefType Crc2)
; Function NvM_Crc_Crc16_Compare
.L40:
NvM_Crc_Crc16_Compare:	.type	func

; ..\eeprom\NvM\NvM_Crc.c	   728  {
; ..\eeprom\NvM\NvM_Crc.c	   729      return (boolean)((Crc1[0] == Crc2[0]) && (Crc1[1] == Crc2[1]));
	ld.bu	d15,[a4]
.L272:
	ld.bu	d0,[a5]
.L273:
	mov	d2,#0
.L274:
	jne	d15,d0,.L16
.L275:
	ld.bu	d15,[a4]1
.L276:
	ld.bu	d0,[a5]1
.L277:
	eq	d15,d15,d0
.L278:
	cmov	d2,d15,#1
.L16:

; ..\eeprom\NvM\NvM_Crc.c	   730  }
	ret
.L160:
	
__NvM_Crc_Crc16_Compare_function_end:
	.size	NvM_Crc_Crc16_Compare,__NvM_Crc_Crc16_Compare_function_end-NvM_Crc_Crc16_Compare
.L101:
	; End of function
	
	.sdecl	'.text.NvM_Crc.NvM_Crc_Crc16_CopyToBuffer',code,cluster('NvM_Crc_Crc16_CopyToBuffer')
	.sect	'.text.NvM_Crc.NvM_Crc_Crc16_CopyToBuffer'
	.align	2
	

; ..\eeprom\NvM\NvM_Crc.c	   731  
; ..\eeprom\NvM\NvM_Crc.c	   732  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   733  *  NvM_Crc_Crc16_CopyToBuffer
; ..\eeprom\NvM\NvM_Crc.c	   734  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   735  /*!
; ..\eeprom\NvM\NvM_Crc.c	   736   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   737   *
; ..\eeprom\NvM\NvM_Crc.c	   738   *
; ..\eeprom\NvM\NvM_Crc.c	   739   */
; ..\eeprom\NvM\NvM_Crc.c	   740  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_Crc16_CopyToBuffer (NvM_CrcBufferPtrType Dest, NvM_CrcValueRefType Src)
; Function NvM_Crc_Crc16_CopyToBuffer
.L42:
NvM_Crc_Crc16_CopyToBuffer:	.type	func

; ..\eeprom\NvM\NvM_Crc.c	   741  {
; ..\eeprom\NvM\NvM_Crc.c	   742      Dest[0] = Src[0]; /* SBSW_NvM_AccessArray_CrcBuffers */
	ld.bu	d15,[a5]
.L283:
	st.b	[a4],d15
.L284:

; ..\eeprom\NvM\NvM_Crc.c	   743      Dest[1] = Src[1]; /* SBSW_NvM_AccessArray_CrcBuffers */
	ld.bu	d15,[a5]1
.L285:
	st.b	[a4]1,d15
.L286:

; ..\eeprom\NvM\NvM_Crc.c	   744  }
	ret
.L163:
	
__NvM_Crc_Crc16_CopyToBuffer_function_end:
	.size	NvM_Crc_Crc16_CopyToBuffer,__NvM_Crc_Crc16_CopyToBuffer_function_end-NvM_Crc_Crc16_CopyToBuffer
.L106:
	; End of function
	
	.sdecl	'.rodata.NvM_Crc.NvM_Crc_NoCrcHandler_t',data,rom,cluster('NvM_Crc_NoCrcHandler_t')
	.sect	'.rodata.NvM_Crc.NvM_Crc_NoCrcHandler_t'
	.align	4
NvM_Crc_NoCrcHandler_t:	.type	object
	.size	NvM_Crc_NoCrcHandler_t,20
	.word	NvM_Crc_NoCrc_Calculate,NvM_Crc_NoCrc_Compare,NvM_Crc_NoCrc_CopyToBuffer
	.space	8
	.sdecl	'.rodata.NvM_Crc.NvM_Crc_Crc16Handler_t',data,rom,cluster('NvM_Crc_Crc16Handler_t')
	.sect	'.rodata.NvM_Crc.NvM_Crc_Crc16Handler_t'
	.align	4
NvM_Crc_Crc16Handler_t:	.type	object
	.size	NvM_Crc_Crc16Handler_t,20
	.word	NvM_Crc_Crc16_Calculate,NvM_Crc_Crc16_Compare,NvM_Crc_Crc16_CopyToBuffer,-1
	.byte	2
	.space	3
	.sdecl	'.rodata.NvM_Crc.NvM_CrcHandlerTable_at',data,rom,cluster('NvM_CrcHandlerTable_at')
	.sect	'.rodata.NvM_Crc.NvM_CrcHandlerTable_at'
	.align	4
NvM_CrcHandlerTable_at:	.type	object
	.size	NvM_CrcHandlerTable_at,16
	.word	NvM_Crc_NoCrcHandler_t,NvM_Crc_NoCrcHandler_t,NvM_Crc_Crc16Handler_t,NvM_Crc_NoCrcHandler_t
	.calls	'__INDIRECT__','NvM_Crc_NoCrc_Calculate'
	.calls	'__INDIRECT__','NvM_Crc_NoCrc_Compare'
	.calls	'__INDIRECT__','NvM_Crc_NoCrc_CopyToBuffer'
	.calls	'__INDIRECT__','NvM_Crc_Crc16_Calculate'
	.calls	'__INDIRECT__','NvM_Crc_Crc16_Compare'
	.calls	'__INDIRECT__','NvM_Crc_Crc16_CopyToBuffer'
	.calls	'NvM_CrcJob_Process','__INDIRECT__'
	.calls	'NvM_CrcJob_Compare','__INDIRECT__'
	.calls	'NvM_CrcJob_CopyToBuffer','__INDIRECT__'
	.calls	'NvM_CrcJob_ExportBufferedValue','__INDIRECT__'
	.calls	'NvM_CrcJob_ImportBufferedValue','__INDIRECT__'
	.calls	'NvM_Crc_Crc16_Calculate','Crc_CalculateCRC16'
	.calls	'NvM_CrcJob_Create','',0
	.calls	'NvM_CrcJob_Process','',0
	.calls	'NvM_CrcJob_Compare','',0
	.calls	'NvM_CrcJob_CopyToBuffer','',0
	.calls	'NvM_CrcJob_ExportBufferedValue','',0
	.calls	'NvM_CrcJob_ImportBufferedValue','',0
	.calls	'NvM_Crc_NoCrc_Calculate','',0
	.calls	'NvM_Crc_NoCrc_Compare','',0
	.calls	'NvM_Crc_NoCrc_CopyToBuffer','',0
	.calls	'NvM_Crc_Crc16_Calculate','',0
	.calls	'NvM_Crc_Crc16_Compare','',0
	.extern	Crc_CalculateCRC16
	.extern	NvM_BlockDescriptorTable_at
	.extern	__INDIRECT__
	.calls	'NvM_Crc_Crc16_CopyToBuffer','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L44:
	.word	9604
	.half	3
	.word	.L45
	.byte	4
.L43:
	.byte	1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L46
.L116:
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'Crc_CalculateCRC16',0,1,170,1,24
	.word	178
	.byte	1,1,1,1
.L131:
	.byte	2
	.byte	'unsigned char',0,1,8,4
	.word	232
	.byte	5
	.word	249
	.byte	6
	.byte	'Crc_DataRefType',0,1,92,50
	.word	254
	.byte	7
	.byte	'Crc_DataPtr',0,1,170,1,60
	.word	259
	.byte	2
	.byte	'unsigned long int',0,4,7,7
	.byte	'Crc_Length',0,1,170,1,80
	.word	304
	.byte	7
	.byte	'Crc_StartValue16',0,1,170,1,99
	.word	178
	.byte	7
	.byte	'Crc_IsFirstCall',0,1,170,1,125
	.word	232
	.byte	0,4
	.word	232
	.byte	5
	.word	397
	.byte	8
	.byte	'NvM_CrcJobStruct',0,2,91,16,20,9
	.byte	'CurrentCrcValue',0,4
	.word	304
	.byte	2,35,0
.L144:
	.byte	6
	.byte	'NvM_ConstRamAddressType',0,3,162,1,50
	.word	254
	.byte	9
	.byte	'RamData_pt',0,4
	.word	454
	.byte	2,35,4,5
	.word	232
.L137:
	.byte	6
	.byte	'NvM_CrcBufferPtrType',0,2,68,59
	.word	507
	.byte	9
	.byte	'CrcBuffer',0,4
	.word	512
	.byte	2,35,8,8
	.byte	'NvM_CrcHandlerClass',0,2,79,8,20,10,1,1,4
	.word	232
	.byte	5
	.word	588
	.byte	11
	.word	593
	.byte	11
	.word	178
	.byte	5
	.word	304
	.byte	11
	.word	608
	.byte	0,5
	.word	585
	.byte	6
	.byte	'NvM_CrcCalculateFPtr',0,2,74,9
	.word	619
	.byte	9
	.byte	'calc',0,4
	.word	624
	.byte	2,35,0,12
	.word	232
	.byte	1,1,11
	.word	593
	.byte	11
	.word	593
	.byte	0,5
	.word	667
	.byte	6
	.byte	'NvM_CrcCompareFPtr',0,2,75,9
	.word	685
	.byte	9
	.byte	'compare',0,4
	.word	690
	.byte	2,35,4,10,1,1,5
	.word	232
	.byte	11
	.word	737
	.byte	11
	.word	593
	.byte	0,5
	.word	734
	.byte	6
	.byte	'NvM_CrcCopyToBufferFPtr',0,2,76,9
	.word	753
	.byte	9
	.byte	'copyToBuffer',0,4
	.word	758
	.byte	2,35,8,9
	.byte	'initialCrcValue',0,4
	.word	304
	.byte	2,35,12,9
	.byte	'crcLength',0,1
	.word	232
	.byte	2,35,16,0,4
	.word	560
	.byte	5
	.word	857
	.byte	6
	.byte	'NvM_CrcHandlerClassConstPtr',0,2,88,75
	.word	862
	.byte	9
	.byte	'HandlerInstance_pt',0,4
	.word	867
	.byte	2,35,12,9
	.byte	'RemainingLength_u16',0,2
	.word	178
	.byte	2,35,16,0,5
	.word	407
	.byte	4
	.word	232
	.byte	5
	.word	966
	.byte	5
	.word	232
	.byte	4
	.word	560
	.byte	5
	.word	981
	.byte	5
	.word	585
	.byte	5
	.word	667
	.byte	5
	.word	734
	.byte	5
	.word	407
.L114:
	.byte	6
	.byte	'NvM_CrcJobPtrType',0,2,100,60
	.word	1006
	.byte	5
	.word	232
.L118:
	.byte	6
	.byte	'NvM_RamAddressType',0,3,161,1,48
	.word	507
.L125:
	.byte	4
	.word	454
	.byte	4
	.word	407
	.byte	5
	.word	1075
	.byte	4
	.word	407
	.byte	5
	.word	1085
.L129:
	.byte	6
	.byte	'NvM_CrcJobConstPtrType',0,2,101,62
	.word	1090
	.byte	4
	.word	232
	.byte	5
	.word	1126
.L141:
	.byte	6
	.byte	'NvM_CrcBufferConstPtrType',0,2,69,61
	.word	254
	.byte	5
	.word	304
	.byte	5
	.word	304
.L147:
	.byte	6
	.byte	'NvM_CrcValuePtrType',0,2,71,52
	.word	1175
	.byte	4
	.word	232
	.byte	5
	.word	1208
.L151:
	.byte	6
	.byte	'NvM_CrcValueRefType',0,2,72,53
	.word	254
	.byte	13
	.byte	'__INDIRECT__',0,4,1,1,1,1,1,14
	.byte	'void',0,5
	.word	1266
	.byte	6
	.byte	'__prof_adm',0,4,1,1
	.word	1272
	.byte	15,1,5
	.word	1296
	.byte	6
	.byte	'__codeptr',0,4,1,1
	.word	1298
	.byte	6
	.byte	'uint8',0,5,90,29
	.word	232
	.byte	2
	.byte	'short int',0,2,5,6
	.byte	'sint16',0,5,91,29
	.word	1335
	.byte	6
	.byte	'uint16',0,5,92,29
	.word	178
	.byte	6
	.byte	'uint32',0,5,94,29
	.word	304
	.byte	6
	.byte	'boolean',0,5,105,29
	.word	232
	.byte	2
	.byte	'unsigned long long int',0,8,7,6
	.byte	'uint64',0,5,130,1,30
	.word	1409
	.byte	6
	.byte	'Std_ReturnType',0,6,113,15
	.word	232
	.byte	6
	.byte	'PduLengthType',0,7,76,22
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,8,112,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,8,115,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,8,118,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,8,121,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,8,124,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,8,136,1,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,8,139,1,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,8,148,1,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,8,151,1,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,8,141,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,8,144,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,8,147,3,16
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,8,150,3,16
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,8,153,3,16
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,8,156,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,8,159,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,8,162,3,16
	.word	178
	.byte	6
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,8,165,3,16
	.word	178
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,8,180,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,8,183,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,8,186,3,16
	.word	304
	.byte	6
	.byte	'IdtAppCom_MainPwrFltRsn',0,8,192,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGMDiags',0,8,195,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGMFltRsn',0,8,198,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGMSts',0,8,201,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGMSwSts',0,8,207,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,8,210,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,8,213,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,8,216,3,16
	.word	178
	.byte	6
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,8,219,3,16
	.word	178
	.byte	6
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,8,225,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,8,228,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_RednPwrFltRsn',0,8,231,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,8,234,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_SHWAIndSts',0,8,237,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_SHWASysFltSts',0,8,240,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_SHWASysMsg',0,8,243,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,8,246,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_SHWASysSts',0,8,249,3,15
	.word	232
	.byte	6
	.byte	'IdtAppCom_SHWASysTakeOver',0,8,252,3,15
	.word	232
	.byte	6
	.byte	'NvM_BlockIdType',0,8,227,5,16
	.word	178
	.byte	6
	.byte	'NvM_RequestResultType',0,8,207,6,15
	.word	232
	.byte	6
	.byte	'NvM_ServiceIdType',0,8,231,6,15
	.word	232
	.byte	2
	.byte	'unsigned int',0,4,7,6
	.byte	'Rte_BitType',0,8,230,7,22
	.word	3136
	.byte	16,9,59,9,1,17
	.byte	'MEMIF_UNINIT',0,0,17
	.byte	'MEMIF_IDLE',0,1,17
	.byte	'MEMIF_BUSY',0,2,17
	.byte	'MEMIF_BUSY_INTERNAL',0,3,0,6
	.byte	'MemIf_StatusType',0,9,65,3
	.word	3173
	.byte	16,9,72,9,1,17
	.byte	'MEMIF_JOB_OK',0,0,17
	.byte	'MEMIF_JOB_FAILED',0,1,17
	.byte	'MEMIF_JOB_PENDING',0,2,17
	.byte	'MEMIF_JOB_CANCELED',0,3,17
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,17
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,6
	.byte	'MemIf_JobResultType',0,9,80,3
	.word	3267
	.byte	12
	.word	232
	.byte	1,1,11
	.word	178
	.byte	11
	.word	178
	.byte	11
	.word	737
	.byte	11
	.word	178
	.byte	0,5
	.word	3425
	.byte	6
	.byte	'MemIf_ApiReadType',0,10,120,9
	.word	3453
	.byte	12
	.word	232
	.byte	1,1,11
	.word	178
	.byte	11
	.word	737
	.byte	0,5
	.word	3484
	.byte	6
	.byte	'MemIf_ApiWriteType',0,10,121,9
	.word	3502
	.byte	12
	.word	232
	.byte	1,1,11
	.word	178
	.byte	0,5
	.word	3534
	.byte	6
	.byte	'MemIf_ApiEraseImmediateBlockType',0,10,122,9
	.word	3547
	.byte	6
	.byte	'MemIf_ApiInvalidateBlockType',0,10,123,9
	.word	3547
	.byte	18,1,1,5
	.word	3630
	.byte	6
	.byte	'MemIf_ApiCancelType',0,10,124,9
	.word	3633
	.byte	19
	.word	3173
	.byte	1,1,5
	.word	3666
	.byte	6
	.byte	'MemIf_ApiGetStatusType',0,10,125,9
	.word	3673
	.byte	19
	.word	3267
	.byte	1,1,5
	.word	3709
	.byte	6
	.byte	'MemIf_ApiGetJobResultType',0,10,126,9
	.word	3716
	.byte	10,1,1,16,9,88,9,1,17
	.byte	'MEMIF_MODE_SLOW',0,0,17
	.byte	'MEMIF_MODE_FAST',0,1,0,11
	.word	3758
	.byte	0,5
	.word	3755
	.byte	6
	.byte	'MemIf_ApiSetModeType',0,10,127,9
	.word	3806
	.byte	6
	.byte	'NvM_BitFieldType',0,3,113,22
	.word	3136
	.byte	6
	.byte	'NvM_CrcType',0,3,116,26
	.word	3136
	.byte	20,3,124,9,4,9
	.byte	'NvDataIndex_t',0,1
	.word	232
	.byte	2,35,0,9
	.byte	'NvRamErrorStatus_u8',0,1
	.word	232
	.byte	2,35,1,9
	.byte	'NvRamAttributes_u8',0,1
	.word	232
	.byte	2,35,2,0,6
	.byte	'NvM_RamMngmtAreaType',0,3,129,1,3
	.word	3885
	.byte	5
	.word	3885
	.byte	6
	.byte	'NvM_RamMngmtPtrType',0,3,131,1,65
	.word	4001
	.byte	12
	.word	232
	.byte	1,1,11
	.word	232
	.byte	11
	.word	232
	.byte	0,5
	.word	4035
	.byte	6
	.byte	'NvM_JobEndCbkPtrType',0,3,134,1,9
	.word	4053
	.byte	12
	.word	232
	.byte	1,1,11
	.word	178
	.byte	11
	.word	232
	.byte	11
	.word	232
	.byte	0,5
	.word	4088
	.byte	6
	.byte	'NvM_JobEndCbkExtPtrType',0,3,137,1,9
	.word	4111
	.byte	19
	.word	232
	.byte	1,1,5
	.word	4149
	.byte	6
	.byte	'NvM_InitCbkPtrType',0,3,142,1,9
	.word	4156
	.byte	12
	.word	232
	.byte	1,1,11
	.word	178
	.byte	11
	.word	1272
	.byte	11
	.word	178
	.byte	0,5
	.word	4189
	.byte	6
	.byte	'NvM_InitCbkExtPtrType',0,3,145,1,9
	.word	4212
	.byte	12
	.word	232
	.byte	1,1,11
	.word	1272
	.byte	0,5
	.word	4248
	.byte	6
	.byte	'NvM_WriteRamToNvMCbkPtrType',0,3,148,1,9
	.word	4261
	.byte	12
	.word	232
	.byte	1,1,4
	.word	1266
	.byte	5
	.word	4310
	.byte	11
	.word	4315
	.byte	0,5
	.word	4303
	.byte	6
	.byte	'NvM_ReadRamFromNvMCbkPtrType',0,3,149,1,9
	.word	4326
	.byte	10,1,1,11
	.word	178
	.byte	11
	.word	1272
	.byte	11
	.word	178
	.byte	0,5
	.word	4369
	.byte	6
	.byte	'NvM_PreWriteTransformCbkPtrType',0,3,152,1,9
	.word	4388
	.byte	6
	.byte	'NvM_PostReadTransformCbkPtrType',0,3,153,1,9
	.word	4212
	.byte	6
	.byte	'NvM_RomAddressType',0,3,164,1,50
	.word	254
	.byte	6
	.byte	'NvM_RamCrcAddressType',0,3,170,1,51
	.word	507
	.byte	20,3,196,1,9,64,9
	.byte	'RamBlockDataAddr_t',0,4
	.word	1042
	.byte	2,35,0,9
	.byte	'RomBlockDataAddr_pt',0,4
	.word	4475
	.byte	2,35,4,9
	.byte	'InitCbkFunc_pt',0,4
	.word	4161
	.byte	2,35,8,9
	.byte	'InitCbkExtFunc_pt',0,4
	.word	4217
	.byte	2,35,12,9
	.byte	'JobEndCbkFunc_pt',0,4
	.word	4058
	.byte	2,35,16,9
	.byte	'JobEndCbkExtFunc_pt',0,4
	.word	4116
	.byte	2,35,20,9
	.byte	'CbkGetMirrorFunc_pt',0,4
	.word	4331
	.byte	2,35,24,9
	.byte	'CbkSetMirrorFunc_pt',0,4
	.word	4266
	.byte	2,35,28,9
	.byte	'CbkPreWriteTransform',0,4
	.word	4393
	.byte	2,35,32,9
	.byte	'CbkPostReadTransform',0,4
	.word	4434
	.byte	2,35,36,9
	.byte	'RamBlockCrcAddr_t',0,4
	.word	4503
	.byte	2,35,40,9
	.byte	'CRCCompMechanismCrcAddr_t',0,4
	.word	4503
	.byte	2,35,44,9
	.byte	'NvIdentifier_u16',0,2
	.word	178
	.byte	2,35,48,9
	.byte	'NvBlockLength_u16',0,2
	.word	178
	.byte	2,35,50,9
	.byte	'NvCryptoReference',0,1
	.word	232
	.byte	2,35,52,9
	.byte	'NvBlockNVRAMDataLength',0,2
	.word	178
	.byte	2,35,54,21
	.byte	'NvBlockCount_u8',0,1
	.word	232
	.byte	8,0,2,35,56,21
	.byte	'BlockPrio_u8',0,1
	.word	232
	.byte	8,0,2,35,57,21
	.byte	'DeviceId_u8',0,1
	.word	232
	.byte	4,4,2,35,58,21
	.byte	'MngmtType_t',0,1
	.word	232
	.byte	2,2,2,35,58,21
	.byte	'CrcSettings',0,1
	.word	232
	.byte	2,0,2,35,58,21
	.byte	'Flags_u8',0,1
	.word	232
	.byte	8,0,2,35,59,21
	.byte	'NotifyBswM',0,1
	.word	232
	.byte	1,7,2,35,60,0,6
	.byte	'NvM_BlockDescriptorType',0,3,223,1,3
	.word	4534
	.byte	4
	.word	232
	.byte	5
	.word	5191
	.byte	5
	.word	4149
	.byte	5
	.word	4189
	.byte	5
	.word	4035
	.byte	5
	.word	4088
	.byte	5
	.word	4303
	.byte	5
	.word	4248
	.byte	5
	.word	4369
	.byte	5
	.word	4189
	.byte	5
	.word	232
	.byte	6
	.byte	'NvM_CsmJobIdType',0,3,231,1,16
	.word	304
	.byte	4
	.word	4534
	.byte	5
	.word	5272
	.byte	6
	.byte	'NvM_BlockDescrPtrType',0,3,240,1,71
	.word	5277
	.byte	16,3,243,1,9,1,17
	.byte	'NVM_INT_FID_WRITE_BLOCK',0,0,17
	.byte	'NVM_INT_FID_READ_BLOCK',0,1,17
	.byte	'NVM_INT_FID_RESTORE_DEFAULTS',0,2,17
	.byte	'NVM_INT_FID_INVALIDATE_NV_BLOCK',0,3,17
	.byte	'NVM_INT_FID_ERASE_BLOCK',0,4,17
	.byte	'NVM_INT_FID_WRITE_ALL',0,5,17
	.byte	'NVM_INT_FID_READ_ALL',0,6,17
	.byte	'NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS',0,7,17
	.byte	'NVM_INT_FID_NO_JOB_PENDING',0,8,0,6
	.byte	'NvM_InternalServiceIdType',0,3,254,1,3
	.word	5313
	.byte	6
	.byte	'NvM_QueueEntryRefType',0,3,129,2,15
	.word	232
	.byte	22
	.word	4534
	.byte	23,0,4
	.word	5642
	.byte	24
	.byte	'NvM_BlockDescriptorTable_at',0,3,151,3,57
	.word	5649
	.byte	1,1,16,11,42,9,1,17
	.byte	'NVM_ACT_ID_SetInitialAttr',0,0,17
	.byte	'NVM_ACT_ID_InitMainFsm',0,1,17
	.byte	'NVM_ACT_ID_InitBlock',0,2,17
	.byte	'NVM_ACT_ID_InitReadAll',0,3,17
	.byte	'NVM_ACT_ID_InitReadBlockSubFsm',0,4,17
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaultsSubFsm',0,5,17
	.byte	'NVM_ACT_ID_InitWriteAll',0,6,17
	.byte	'NVM_ACT_ID_InitWriteBlock',0,7,17
	.byte	'NVM_ACT_ID_InitWriteBlockFsm',0,8,17
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaults',0,9,17
	.byte	'NVM_ACT_ID_FinishMainJob',0,10,17
	.byte	'NVM_ACT_ID_KillWritAll',0,11,17
	.byte	'NVM_ACT_ID_FinishBlock',0,12,17
	.byte	'NVM_ACT_ID_InitNextBlockReadAll',0,13,17
	.byte	'NVM_ACT_ID_InitNextBlockWriteAll',0,14,17
	.byte	'NVM_ACT_ID_FinishCfgIdCheck',0,15,17
	.byte	'NVM_ACT_ID_FinishReadBlock',0,16,17
	.byte	'NVM_ACT_ID_FinishWriteBlock',0,17,17
	.byte	'NVM_ACT_ID_FinishEraseBlock',0,18,17
	.byte	'NVM_ACT_ID_EraseNvBlock',0,19,17
	.byte	'NVM_ACT_ID_InvalidateNvBlock',0,20,17
	.byte	'NVM_ACT_ID_ProcessCrc',0,21,17
	.byte	'NVM_ACT_ID_WriteNvBlock',0,22,17
	.byte	'NVM_ACT_ID_ReadNvBlock',0,23,17
	.byte	'NVM_ACT_ID_ProcessCrcRead',0,24,17
	.byte	'NVM_ACT_ID_ReadCopyData',0,25,17
	.byte	'NVM_ACT_ID_RestoreRomDefaults',0,26,17
	.byte	'NVM_ACT_ID_FinishRestoreRomDefaults',0,27,17
	.byte	'NVM_ACT_ID_TestBlockBlank',0,28,17
	.byte	'NVM_ACT_ID_ValidateRam',0,29,17
	.byte	'NVM_ACT_ID_SetupRedundant',0,30,17
	.byte	'NVM_ACT_ID_SetupOther',0,31,17
	.byte	'NVM_ACT_ID_UpdateNvState',0,32,17
	.byte	'NVM_ACT_ID_SetReqIntegrityFailed',0,33,17
	.byte	'NVM_ACT_ID_SetReqSkipped',0,34,17
	.byte	'NVM_ACT_ID_SetReqNotOk',0,35,17
	.byte	'NVM_ACT_ID_SetReqOk',0,36,17
	.byte	'NVM_ACT_ID_SetBlockPendingWriteAll',0,37,17
	.byte	'NVM_ACT_ID_CopyNvDataToBuf',0,38,17
	.byte	'NVM_ACT_ID_GetMultiBlockJob',0,39,17
	.byte	'NVM_ACT_ID_CancelNV',0,40,17
	.byte	'NVM_ACT_ID_KillSubFsm',0,41,17
	.byte	'NVM_ACT_ID_FinishReadBlockAndSetSkipped',0,42,17
	.byte	'NVM_ACT_ID_GetNormalPrioJob',0,43,17
	.byte	'NVM_ACT_ID_Wait',0,44,17
	.byte	'NVM_ACT_ID_Nop',0,45,0,6
	.byte	'NvM_StateActionIdType',0,11,110,3
	.word	5693
	.byte	16,12,48,9,1,17
	.byte	'NVM_QRY_ID_BLK_WRITE_ALL',0,0,17
	.byte	'NVM_QRY_ID_CANCEL_WRITE_ALL',0,1,17
	.byte	'NVM_QR_ID_WRITEALL_KILLED',0,2,17
	.byte	'NVM_QRY_ID_CRC_BUSY',0,3,17
	.byte	'NVM_QRY_ID_DATA_COPY_BUSY',0,4,17
	.byte	'NVM_QRY_ID_CRC_MATCH',0,5,17
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_READALL',0,6,17
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_WRITEALL',0,7,17
	.byte	'NVM_QRY_ID_LAST_RESULT_OK',0,8,17
	.byte	'NVM_QRY_ID_MAIN_FSM_RUNNING',0,9,17
	.byte	'NVM_QRY_ID_MULTI_BLK_JOB',0,10,17
	.byte	'NVM_QRY_ID_NORMAL_PRIO_JOB',0,11,17
	.byte	'NVM_QRY_ID_NV_BUSY',0,12,17
	.byte	'NVM_QRY_ID_MEMHWA_BUSY',0,13,17
	.byte	'NVM_QRY_ID_RAM_VALID',0,14,17
	.byte	'NVM_QRY_ID_REDUNDANT_BLOCK',0,15,17
	.byte	'NVM_QRY_ID_SKIP_BLOCK',0,16,17
	.byte	'NVM_QRY_ID_SUB_FSM_RUNNING',0,17,17
	.byte	'NVM_QRY_ID_WRITE_BLOCK_ONCE',0,18,17
	.byte	'NVM_QRY_ID_WRITE_RETRIES_EXCEEDED',0,19,17
	.byte	'NVM_QRY_ID_HAS_ROM',0,20,17
	.byte	'NVM_QRY_ID_EXT_RUNTIME',0,21,17
	.byte	'NvM_QRY_CRC_COMP_MECHANISM_SKIPWRITE',0,22,17
	.byte	'NVM_QRY_POST_READ_TRANSFORM',0,23,17
	.byte	'NVM_QRY_READALL_KILLED',0,24,17
	.byte	'NVM_QRY_SYNCDECRYPT',0,25,17
	.byte	'NVM_QRY_SYNCENCRYPT',0,26,17
	.byte	'NVM_QRY_CSM_RETRIES_NECESSARY',0,27,17
	.byte	'NVM_QRY_ID_TRUE',0,28,0,6
	.byte	'NvM_StateQueryIdType',0,12,93,3
	.word	7041
	.byte	6
	.byte	'NvM_CrcJobType',0,2,98,3
	.word	407
	.byte	20,13,57,9,8,9
	.byte	'JobBlockId_t',0,2
	.word	178
	.byte	2,35,0,9
	.byte	'JobServiceId_t',0,1
	.word	5313
	.byte	2,35,2,9
	.byte	'RamAddr_t',0,4
	.word	1042
	.byte	2,35,4,0,6
	.byte	'NvM_JobType',0,13,62,3
	.word	7897
	.byte	20,13,66,9,44,9
	.byte	'Descriptor_pt',0,4
	.word	5282
	.byte	2,35,0,9
	.byte	'Mngmt_pt',0,4
	.word	4006
	.byte	2,35,4,9
	.byte	'RamAddr_t',0,4
	.word	1042
	.byte	2,35,8,9
	.byte	'NvRamAddr_t',0,4
	.word	1042
	.byte	2,35,12,9
	.byte	'BlockCrcJob_t',0,20
	.word	407
	.byte	2,35,16,9
	.byte	'NvIdentifier_u16',0,2
	.word	178
	.byte	2,35,36,9
	.byte	'ByteCount_u16',0,2
	.word	178
	.byte	2,35,38,9
	.byte	'LastResult_t',0,1
	.word	232
	.byte	2,35,40,9
	.byte	'WriteRetryCounter_u8',0,1
	.word	232
	.byte	2,35,41,9
	.byte	'InternalFlags_u8',0,1
	.word	232
	.byte	2,35,42,9
	.byte	'NvState_u8',0,1
	.word	232
	.byte	2,35,43,0,6
	.byte	'NvM_BlockInfoType',0,13,83,3
	.word	7988
	.byte	4
	.word	4534
	.byte	5
	.word	8271
	.byte	5
	.word	3885
	.byte	16,13,96,9,1,17
	.byte	'NVM_STATE_UNINIT',0,0,17
	.byte	'NVM_STATE_IDLE',0,1,17
	.byte	'NVM_STATE_NORMAL_PRIO_JOB',0,2,17
	.byte	'NVM_STATE_MULTI_BLOCK_JOB',0,3,17
	.byte	'NVM_STATE_READ_READ_DATA',0,4,17
	.byte	'NVM_STATE_READ_DATA_VALIDATION',0,5,17
	.byte	'NVM_STATE_READ_CMP_CRC',0,6,17
	.byte	'NVM_STATE_READ_IMPL_RECOV',0,7,17
	.byte	'NVM_STATE_READ_LOAD_ROM',0,8,17
	.byte	'NVM_STATE_READ_FINALIZE',0,9,17
	.byte	'NVM_STATE_WRITE_INITIAL',0,10,17
	.byte	'NVM_STATE_WRITE_CRCCALC',0,11,17
	.byte	'NVM_STATE_WRITE_CRCCOMPMECHANISM',0,12,17
	.byte	'NVM_STATE_WRITE_TEST_PRI_READ',0,13,17
	.byte	'NVM_STATE_WRITE_TEST_SEC_READ',0,14,17
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_1',0,15,17
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_2',0,16,17
	.byte	'NVM_STATE_RESTORE_LOAD_ROM',0,17,17
	.byte	'NVM_STATE_INVALIDATING_BLOCK',0,18,17
	.byte	'NVM_STATE_ERASE_ERASE_BLOCK',0,19,17
	.byte	'NVM_STATE_READALL_PROC_CONFIG_ID',0,20,17
	.byte	'NVM_STATE_READALL_PROC_RAM_BLOCK',0,21,17
	.byte	'NVM_STATE_READALL_CHK_SKIP',0,22,17
	.byte	'NVM_STATE_READALL_KILLED',0,23,17
	.byte	'NVM_STATE_READALL_WR_ONCE_PROT',0,24,17
	.byte	'NVM_STATE_READALL_CHK_RAM_VALIDITY',0,25,17
	.byte	'NVM_STATE_READALL_READ_NV',0,26,17
	.byte	'NVM_STATE_READALL_LOAD_DEFAULTS',0,27,17
	.byte	'NVM_STATE_READALL_READABILITY_CHECK',0,28,17
	.byte	'NVM_STATE_WRITEALL_PROC_BLOCK',0,29,17
	.byte	'NVM_STATE_WRITEALL_WRITE_FSM',0,30,17
	.byte	'NVM_STATE_WRITEALL_WAIT_MEMHWA',0,31,17
	.byte	'NVM_STATE_FSM_FINISHED',0,32,0,6
	.byte	'NvM_StateIdType',0,13,215,1,3
	.word	8286
	.byte	20,13,228,1,9,2,9
	.byte	'ExitHandler_t',0,1
	.word	5693
	.byte	2,35,0,9
	.byte	'EntryHandler_t',0,1
	.word	5693
	.byte	2,35,1,0,6
	.byte	'NvM_StateChangeActionsType',0,13,232,1,3
	.word	9296
	.byte	20,13,236,1,9,6,25,2
	.word	7041
	.byte	26,1,0,9
	.byte	'Queries_at',0,2
	.word	9392
	.byte	2,35,0,9
	.byte	'Actions_t',0,2
	.word	9296
	.byte	2,35,2,9
	.byte	'NextState_t',0,1
	.word	8286
	.byte	2,35,4,0,6
	.byte	'NvM_StateChangeIfDescrType',0,13,241,1,3
	.word	9386
	.byte	20,13,243,1,9,4,9
	.byte	'Actions_t',0,2
	.word	9296
	.byte	2,35,0,9
	.byte	'NextState_t',0,1
	.word	8286
	.byte	2,35,2,0,6
	.byte	'NvM_StateChangeElseDescrType',0,13,247,1,3
	.word	9498
.L166:
	.byte	4
	.word	560
.L167:
	.byte	4
	.word	560
	.byte	25,16
	.word	867
	.byte	26,3,0
.L168:
	.byte	4
	.word	9593
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,58,15,59,15,57,15
	.byte	73,19,54,15,39,12,63,12,60,12,0,0,4,38,0,73,19,0,0,5,15,0,73,19,0,0,6,22,0,3,8,58,15,59,15,57,15,73,19
	.byte	0,0,7,5,0,3,8,58,15,59,15,57,15,73,19,0,0,8,19,1,3,8,58,15,59,15,57,15,11,15,0,0,9,13,0,3,8,11,15,73,19
	.byte	56,9,0,0,10,21,1,54,15,39,12,0,0,11,5,0,73,19,0,0,12,21,1,73,19,54,15,39,12,0,0,13,46,0,3,8,58,15,59,15
	.byte	57,15,54,15,63,12,60,12,0,0,14,59,0,3,8,0,0,15,21,0,54,15,0,0,16,4,1,58,15,59,15,57,15,11,15,0,0,17,40
	.byte	0,3,8,28,13,0,0,18,21,0,54,15,39,12,0,0,19,21,0,73,19,54,15,39,12,0,0,20,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,21,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,22,1,1,73,19,0,0,23,33,0,0,0,24,52,0,3,8,58,15,59,15
	.byte	57,15,73,19,63,12,60,12,0,0,25,1,1,11,15,73,19,0,0,26,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L46:
	.word	.L183-.L182
.L182:
	.half	3
	.word	.L185-.L184
.L184:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	0
	.byte	'Crc.h',0,1,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.h',0,0,0,0
	.byte	'_PrivateCfg.h',0,2,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0
	.byte	'Platform_Types.h',0,3,0,0
	.byte	'Std_Types.h',0,3,0,0
	.byte	'ComStack_Types.h',0,3,0,0
	.byte	'Rte_Type.h',0,4,0,0
	.byte	'MemIf_Types.h',0,5,0,0
	.byte	'_Cfg.h',0,5,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.h',0,0,0,0,0
.L185:
.L183:
	.sdecl	'.debug_info',debug,cluster('NvM_CrcJob_Create')
	.sect	'.debug_info'
.L47:
	.word	319
	.half	3
	.word	.L48
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L50,.L49
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_CrcJob_Create',0,1,136,4,30,1,1,1
	.word	.L20,.L113,.L19
	.byte	4
	.byte	'Self',0,1,137,4,23
	.word	.L114,.L115
	.byte	4
	.byte	'BlockId',0,1,137,4,45
	.word	.L116,.L117
	.byte	4
	.byte	'RamDataPtr',0,1,137,4,73
	.word	.L118,.L119
	.byte	4
	.byte	'DataLength',0,1,137,4,92
	.word	.L116,.L120
	.byte	5
	.word	.L20,.L113
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CrcJob_Create')
	.sect	'.debug_abbrev'
.L48:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CrcJob_Create')
	.sect	'.debug_line'
.L49:
	.word	.L187-.L186
.L186:
	.half	3
	.word	.L189-.L188
.L188:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L189:
	.byte	5,74,7,0,5,2
	.word	.L20
	.byte	3,138,4,1,5,46,1,5,74,9
	.half	.L190-.L20
	.byte	1,5,73,9
	.half	.L191-.L190
	.byte	1,5,63,9
	.half	.L192-.L191
	.byte	3,2,1,5,32,9
	.half	.L193-.L192
	.byte	1,5,63,9
	.half	.L194-.L193
	.byte	1,5,32,1,5,54,9
	.half	.L195-.L194
	.byte	1,5,30,9
	.half	.L196-.L195
	.byte	1,5,53,9
	.half	.L197-.L196
	.byte	3,1,1,5,27,9
	.half	.L198-.L197
	.byte	1,5,22,9
	.half	.L199-.L198
	.byte	3,1,1,5,23,9
	.half	.L200-.L199
	.byte	3,1,1,5,62,7,9
	.half	.L201-.L200
	.byte	1,5,76,9
	.half	.L202-.L201
	.byte	1,5,48,9
	.half	.L2-.L202
	.byte	1,5,21,9
	.half	.L3-.L2
	.byte	1,5,35,9
	.half	.L203-.L3
	.byte	3,3,1,5,10,9
	.half	.L204-.L203
	.byte	1,5,68,7,9
	.half	.L205-.L204
	.byte	1,5,82,7,9
	.half	.L4-.L205
	.byte	1,5,31,9
	.half	.L6-.L4
	.byte	3,127,1,5,1,9
	.half	.L206-.L6
	.byte	3,20,1,7,9
	.half	.L51-.L206
	.byte	0,1,1
.L187:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CrcJob_Create')
	.sect	'.debug_ranges'
.L50:
	.word	-1,.L20,0,.L51-.L20,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CrcJob_Process')
	.sect	'.debug_info'
.L52:
	.word	337
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L55,.L54
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_CrcJob_Process',0,1,177,4,30,1,1,1
	.word	.L22,.L121,.L21
	.byte	4
	.byte	'Self',0,1,177,4,67
	.word	.L114,.L122
	.byte	4
	.byte	'ProcessLength',0,1,177,4,80
	.word	.L116,.L123
	.byte	5
	.word	.L22,.L121
	.byte	5
	.word	.L124,.L7
	.byte	6
	.byte	'currRamPtr',0,1,181,4,39
	.word	.L125,.L126
	.byte	6
	.byte	'currLength',0,1,182,4,16
	.word	.L116,.L127
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CrcJob_Process')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CrcJob_Process')
	.sect	'.debug_line'
.L54:
	.word	.L208-.L207
.L207:
	.half	3
	.word	.L210-.L209
.L209:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L210:
	.byte	5,30,7,0,5,2
	.word	.L22
	.byte	3,176,4,1,5,12,9
	.half	.L171-.L22
	.byte	3,2,1,5,5,9
	.half	.L211-.L171
	.byte	1,5,56,7,9
	.half	.L124-.L211
	.byte	3,2,1,5,23,9
	.half	.L170-.L124
	.byte	3,4,1,5,35,9
	.half	.L169-.L170
	.byte	3,5,1,5,39,9
	.half	.L212-.L169
	.byte	3,3,1,5,26,9
	.half	.L213-.L212
	.byte	1,5,13,9
	.half	.L214-.L213
	.byte	3,2,1,5,33,9
	.half	.L215-.L214
	.byte	1,5,69,9
	.half	.L216-.L215
	.byte	1,5,1,7,9
	.half	.L7-.L216
	.byte	3,2,1,7,9
	.half	.L56-.L7
	.byte	0,1,1
.L208:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CrcJob_Process')
	.sect	'.debug_ranges'
.L55:
	.word	-1,.L22,0,.L56-.L22,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CrcJob_CopyToBuffer')
	.sect	'.debug_info'
.L57:
	.word	256
	.half	3
	.word	.L58
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L60,.L59
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_CrcJob_CopyToBuffer',0,1,230,4,30,1,1,1
	.word	.L26,.L128,.L25
	.byte	4
	.byte	'Self',0,1,230,4,77
	.word	.L129,.L130
	.byte	5
	.word	.L26,.L128
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CrcJob_CopyToBuffer')
	.sect	'.debug_abbrev'
.L58:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CrcJob_CopyToBuffer')
	.sect	'.debug_line'
.L59:
	.word	.L218-.L217
.L217:
	.half	3
	.word	.L220-.L219
.L219:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L220:
	.byte	5,30,7,0,5,2
	.word	.L26
	.byte	3,229,4,1,5,12,9
	.half	.L176-.L26
	.byte	3,2,1,5,5,9
	.half	.L175-.L176
	.byte	1,5,13,7,9
	.half	.L221-.L175
	.byte	3,2,1,5,33,9
	.half	.L222-.L221
	.byte	1,5,65,9
	.half	.L223-.L222
	.byte	1,5,1,7,9
	.half	.L10-.L223
	.byte	3,2,1,7,9
	.half	.L61-.L10
	.byte	0,1,1
.L218:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CrcJob_CopyToBuffer')
	.sect	'.debug_ranges'
.L60:
	.word	-1,.L26,0,.L61-.L26,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CrcJob_Compare')
	.sect	'.debug_info'
.L62:
	.word	276
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L65,.L64
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_CrcJob_Compare',0,1,209,4,33
	.word	.L131
	.byte	1,1,1
	.word	.L24,.L132,.L23
	.byte	4
	.byte	'Self',0,1,209,4,75
	.word	.L129,.L133
	.byte	5
	.word	.L24,.L132
	.byte	6
	.byte	'result',0,1,211,4,13
	.word	.L131,.L134
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CrcJob_Compare')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CrcJob_Compare')
	.sect	'.debug_line'
.L64:
	.word	.L225-.L224
.L224:
	.half	3
	.word	.L227-.L226
.L226:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L227:
	.byte	5,33,7,0,5,2
	.word	.L24
	.byte	3,208,4,1,5,12,9
	.half	.L173-.L24
	.byte	3,4,1,5,20,9
	.half	.L172-.L173
	.byte	3,126,1,5,5,9
	.half	.L174-.L172
	.byte	3,2,1,5,23,7,9
	.half	.L228-.L174
	.byte	3,2,1,5,43,9
	.half	.L229-.L228
	.byte	1,5,70,9
	.half	.L230-.L229
	.byte	1,5,1,7,9
	.half	.L8-.L230
	.byte	3,4,1,7,9
	.half	.L66-.L8
	.byte	0,1,1
.L225:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CrcJob_Compare')
	.sect	'.debug_ranges'
.L65:
	.word	-1,.L24,0,.L66-.L24,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CrcJob_ExportBufferedValue')
	.sect	'.debug_info'
.L67:
	.word	284
	.half	3
	.word	.L68
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L70,.L69
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_CrcJob_ExportBufferedValue',0,1,248,4,30,1,1,1
	.word	.L28,.L135,.L27
	.byte	4
	.byte	'Self',0,1,248,4,84
	.word	.L129,.L136
	.byte	4
	.byte	'DestPtr',0,1,248,4,111
	.word	.L137,.L138
	.byte	5
	.word	.L28,.L135
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CrcJob_ExportBufferedValue')
	.sect	'.debug_abbrev'
.L68:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CrcJob_ExportBufferedValue')
	.sect	'.debug_line'
.L69:
	.word	.L232-.L231
.L231:
	.half	3
	.word	.L234-.L233
.L233:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L234:
	.byte	5,8,7,0,5,2
	.word	.L28
	.byte	3,249,4,1,5,38,7,9
	.half	.L235-.L28
	.byte	1,5,50,9
	.half	.L236-.L235
	.byte	1,5,13,7,9
	.half	.L237-.L236
	.byte	3,2,1,5,33,9
	.half	.L238-.L237
	.byte	1,5,61,9
	.half	.L239-.L238
	.byte	1,5,1,9
	.half	.L11-.L239
	.byte	3,2,1,7,9
	.half	.L71-.L11
	.byte	0,1,1
.L232:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CrcJob_ExportBufferedValue')
	.sect	'.debug_ranges'
.L70:
	.word	-1,.L28,0,.L71-.L28,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CrcJob_ImportBufferedValue')
	.sect	'.debug_info'
.L72:
	.word	283
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L75,.L74
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_CrcJob_ImportBufferedValue',0,1,136,5,30,1,1,1
	.word	.L30,.L139,.L29
	.byte	4
	.byte	'Self',0,1,136,5,84
	.word	.L129,.L140
	.byte	4
	.byte	'SrcPtr',0,1,136,5,116
	.word	.L141,.L142
	.byte	5
	.word	.L30,.L139
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CrcJob_ImportBufferedValue')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_CrcJob_ImportBufferedValue')
	.sect	'.debug_line'
.L74:
	.word	.L241-.L240
.L240:
	.half	3
	.word	.L243-.L242
.L242:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L243:
	.byte	5,13,7,0,5,2
	.word	.L30
	.byte	3,137,5,1,5,8,9
	.half	.L244-.L30
	.byte	1,5,49,7,9
	.half	.L245-.L244
	.byte	1,5,13,7,9
	.half	.L246-.L245
	.byte	3,2,1,5,33,9
	.half	.L247-.L246
	.byte	1,5,65,9
	.half	.L248-.L247
	.byte	1,5,1,9
	.half	.L13-.L248
	.byte	3,2,1,7,9
	.half	.L76-.L13
	.byte	0,1,1
.L241:
	.sdecl	'.debug_ranges',debug,cluster('NvM_CrcJob_ImportBufferedValue')
	.sect	'.debug_ranges'
.L75:
	.word	-1,.L30,0,.L76-.L30,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Crc_NoCrc_Calculate')
	.sect	'.debug_info'
.L77:
	.word	305
	.half	3
	.word	.L78
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L80,.L79
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_Crc_NoCrc_Calculate',0,1,155,5,41,1,1
	.word	.L32,.L143,.L31
	.byte	4
	.byte	'DataPtr',0,1,155,5,89
	.word	.L144,.L145
	.byte	4
	.byte	'Length',0,1,155,5,105
	.word	.L116,.L146
	.byte	4
	.byte	'CurrentValue',0,1,155,5,133,1
	.word	.L147,.L148
	.byte	5
	.word	.L32,.L143
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Crc_NoCrc_Calculate')
	.sect	'.debug_abbrev'
.L78:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_Crc_NoCrc_Calculate')
	.sect	'.debug_line'
.L79:
	.word	.L250-.L249
.L249:
	.half	3
	.word	.L252-.L251
.L251:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L252:
	.byte	5,1,7,0,5,2
	.word	.L32
	.byte	3,159,5,1,7,9
	.half	.L81-.L32
	.byte	0,1,1
.L250:
	.sdecl	'.debug_ranges',debug,cluster('NvM_Crc_NoCrc_Calculate')
	.sect	'.debug_ranges'
.L80:
	.word	-1,.L32,0,.L81-.L32,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Crc_NoCrc_Compare')
	.sect	'.debug_info'
.L82:
	.word	286
	.half	3
	.word	.L83
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L85,.L84
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_Crc_NoCrc_Compare',0,1,170,5,43
	.word	.L131
	.byte	1,1
	.word	.L34,.L149,.L33
	.byte	4
	.byte	'CrcBuff',0,1,170,5,91
	.word	.L141,.L150
	.byte	4
	.byte	'CurrentValue',0,1,170,5,120
	.word	.L151,.L152
	.byte	5
	.word	.L34,.L149
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Crc_NoCrc_Compare')
	.sect	'.debug_abbrev'
.L83:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_Crc_NoCrc_Compare')
	.sect	'.debug_line'
.L84:
	.word	.L254-.L253
.L253:
	.half	3
	.word	.L256-.L255
.L255:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L256:
	.byte	5,12,7,0,5,2
	.word	.L34
	.byte	3,174,5,1,5,1,3,1,1,7,9
	.half	.L86-.L34
	.byte	0,1,1
.L254:
	.sdecl	'.debug_ranges',debug,cluster('NvM_Crc_NoCrc_Compare')
	.sect	'.debug_ranges'
.L85:
	.word	-1,.L34,0,.L86-.L34,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Crc_NoCrc_CopyToBuffer')
	.sect	'.debug_info'
.L87:
	.word	275
	.half	3
	.word	.L88
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L90,.L89
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_Crc_NoCrc_CopyToBuffer',0,1,187,5,40,1,1
	.word	.L36,.L153,.L35
	.byte	4
	.byte	'Dest',0,1,187,5,88
	.word	.L137,.L154
	.byte	4
	.byte	'Src',0,1,187,5,114
	.word	.L151,.L155
	.byte	5
	.word	.L36,.L153
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Crc_NoCrc_CopyToBuffer')
	.sect	'.debug_abbrev'
.L88:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_Crc_NoCrc_CopyToBuffer')
	.sect	'.debug_line'
.L89:
	.word	.L258-.L257
.L257:
	.half	3
	.word	.L260-.L259
.L259:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L260:
	.byte	5,1,7,0,5,2
	.word	.L36
	.byte	3,190,5,1,7,9
	.half	.L91-.L36
	.byte	0,1,1
.L258:
	.sdecl	'.debug_ranges',debug,cluster('NvM_Crc_NoCrc_CopyToBuffer')
	.sect	'.debug_ranges'
.L90:
	.word	-1,.L36,0,.L91-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Crc_Crc16_Calculate')
	.sect	'.debug_info'
.L92:
	.word	300
	.half	3
	.word	.L93
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L95,.L94
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_Crc_Crc16_Calculate',0,1,202,5,41,1,1
	.word	.L38,.L156,.L37
	.byte	4
	.byte	'DataPtr',0,1,202,5,89
	.word	.L144,.L157
	.byte	4
	.byte	'Length',0,1,202,5,105
	.word	.L116,.L158
	.byte	4
	.byte	'CurrCrc',0,1,202,5,133,1
	.word	.L147,.L159
	.byte	5
	.word	.L38,.L156
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Crc_Crc16_Calculate')
	.sect	'.debug_abbrev'
.L93:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_Crc_Crc16_Calculate')
	.sect	'.debug_line'
.L94:
	.word	.L262-.L261
.L261:
	.half	3
	.word	.L264-.L263
.L263:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L264:
	.byte	5,41,7,0,5,2
	.word	.L38
	.byte	3,201,5,1,5,61,9
	.half	.L181-.L38
	.byte	3,2,1,5,72,9
	.half	.L265-.L181
	.byte	1,5,52,9
	.half	.L266-.L265
	.byte	1,5,72,1,5,14,9
	.half	.L180-.L266
	.byte	1,5,1,9
	.half	.L267-.L180
	.byte	3,1,1,7,9
	.half	.L96-.L267
	.byte	0,1,1
.L262:
	.sdecl	'.debug_ranges',debug,cluster('NvM_Crc_Crc16_Calculate')
	.sect	'.debug_ranges'
.L95:
	.word	-1,.L38,0,.L96-.L38,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Crc_Crc16_Compare')
	.sect	'.debug_info'
.L97:
	.word	275
	.half	3
	.word	.L98
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L100,.L99
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_Crc_Crc16_Compare',0,1,215,5,43
	.word	.L131
	.byte	1,1
	.word	.L40,.L160,.L39
	.byte	4
	.byte	'Crc1',0,1,215,5,91
	.word	.L141,.L161
	.byte	4
	.byte	'Crc2',0,1,215,5,117
	.word	.L151,.L162
	.byte	5
	.word	.L40,.L160
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Crc_Crc16_Compare')
	.sect	'.debug_abbrev'
.L98:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_Crc_Crc16_Compare')
	.sect	'.debug_line'
.L99:
	.word	.L269-.L268
.L268:
	.half	3
	.word	.L271-.L270
.L270:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L271:
	.byte	5,27,7,0,5,2
	.word	.L40
	.byte	3,216,5,1,5,38,9
	.half	.L272-.L40
	.byte	1,5,43,9
	.half	.L273-.L272
	.byte	1,5,22,9
	.half	.L274-.L273
	.byte	1,5,51,7,9
	.half	.L275-.L274
	.byte	1,5,62,9
	.half	.L276-.L275
	.byte	1,5,55,9
	.half	.L277-.L276
	.byte	1,5,43,9
	.half	.L278-.L277
	.byte	1,5,1,9
	.half	.L16-.L278
	.byte	3,1,1,7,9
	.half	.L101-.L16
	.byte	0,1,1
.L269:
	.sdecl	'.debug_ranges',debug,cluster('NvM_Crc_Crc16_Compare')
	.sect	'.debug_ranges'
.L100:
	.word	-1,.L40,0,.L101-.L40,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Crc_Crc16_CopyToBuffer')
	.sect	'.debug_info'
.L102:
	.word	275
	.half	3
	.word	.L103
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L105,.L104
	.byte	2
	.word	.L43
	.byte	3
	.byte	'NvM_Crc_Crc16_CopyToBuffer',0,1,228,5,40,1,1
	.word	.L42,.L163,.L41
	.byte	4
	.byte	'Dest',0,1,228,5,89
	.word	.L137,.L164
	.byte	4
	.byte	'Src',0,1,228,5,115
	.word	.L151,.L165
	.byte	5
	.word	.L42,.L163
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Crc_Crc16_CopyToBuffer')
	.sect	'.debug_abbrev'
.L103:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_Crc_Crc16_CopyToBuffer')
	.sect	'.debug_line'
.L104:
	.word	.L280-.L279
.L279:
	.half	3
	.word	.L282-.L281
.L281:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0,0,0,0,0
.L282:
	.byte	5,18,7,0,5,2
	.word	.L42
	.byte	3,229,5,1,5,13,9
	.half	.L283-.L42
	.byte	1,5,18,9
	.half	.L284-.L283
	.byte	3,1,1,5,13,9
	.half	.L285-.L284
	.byte	1,5,1,9
	.half	.L286-.L285
	.byte	3,1,1,7,9
	.half	.L106-.L286
	.byte	0,1,1
.L280:
	.sdecl	'.debug_ranges',debug,cluster('NvM_Crc_Crc16_CopyToBuffer')
	.sect	'.debug_ranges'
.L105:
	.word	-1,.L42,0,.L106-.L42,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Crc_NoCrcHandler_t')
	.sect	'.debug_info'
.L107:
	.word	214
	.half	3
	.word	.L108
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L43
	.byte	3
	.byte	'NvM_Crc_NoCrcHandler_t',0,4,239,1,64
	.word	.L166
	.byte	5,3
	.word	NvM_Crc_NoCrcHandler_t
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Crc_NoCrcHandler_t')
	.sect	'.debug_abbrev'
.L108:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_Crc_Crc16Handler_t')
	.sect	'.debug_info'
.L109:
	.word	214
	.half	3
	.word	.L110
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L43
	.byte	3
	.byte	'NvM_Crc_Crc16Handler_t',0,4,250,1,64
	.word	.L167
	.byte	5,3
	.word	NvM_Crc_Crc16Handler_t
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_Crc_Crc16Handler_t')
	.sect	'.debug_abbrev'
.L110:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CrcHandlerTable_at')
	.sect	'.debug_info'
.L111:
	.word	214
	.half	3
	.word	.L112
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Crc.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L43
	.byte	3
	.byte	'NvM_CrcHandlerTable_at',0,4,150,2,65
	.word	.L168
	.byte	5,3
	.word	NvM_CrcHandlerTable_at
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CrcHandlerTable_at')
	.sect	'.debug_abbrev'
.L112:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CrcJob_Compare')
	.sect	'.debug_loc'
.L23:
	.word	-1,.L24,0,.L132-.L24
	.half	2
	.byte	138,0
	.word	0,0
.L133:
	.word	-1,.L24,0,.L172-.L24
	.half	1
	.byte	100
	.word	.L173-.L24,.L132-.L24
	.half	1
	.byte	101
	.word	0,0
.L134:
	.word	-1,.L24,.L174-.L24,.L132-.L24
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CrcJob_CopyToBuffer')
	.sect	'.debug_loc'
.L25:
	.word	-1,.L26,0,.L128-.L26
	.half	2
	.byte	138,0
	.word	0,0
.L130:
	.word	-1,.L26,0,.L175-.L26
	.half	1
	.byte	100
	.word	.L176-.L26,.L128-.L26
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CrcJob_Create')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L20,0,.L113-.L20
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L120:
	.word	-1,.L20,0,.L113-.L20
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L19:
	.word	-1,.L20,0,.L113-.L20
	.half	2
	.byte	138,0
	.word	0,0
.L119:
	.word	-1,.L20,0,.L113-.L20
	.half	1
	.byte	101
	.word	0,0
.L115:
	.word	-1,.L20,0,.L113-.L20
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CrcJob_ExportBufferedValue')
	.sect	'.debug_loc'
.L138:
	.word	-1,.L28,0,.L177-.L28
	.half	1
	.byte	101
	.word	.L178-.L28,.L11-.L28
	.half	1
	.byte	100
	.word	.L11-.L28,.L135-.L28
	.half	1
	.byte	101
	.word	0,0
.L27:
	.word	-1,.L28,0,.L135-.L28
	.half	2
	.byte	138,0
	.word	0,0
.L136:
	.word	-1,.L28,0,.L178-.L28
	.half	1
	.byte	100
	.word	.L11-.L28,.L135-.L28
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CrcJob_ImportBufferedValue')
	.sect	'.debug_loc'
.L29:
	.word	-1,.L30,0,.L139-.L30
	.half	2
	.byte	138,0
	.word	0,0
.L140:
	.word	-1,.L30,0,.L179-.L30
	.half	1
	.byte	100
	.word	.L13-.L30,.L139-.L30
	.half	1
	.byte	100
	.word	0,0
.L142:
	.word	-1,.L30,0,.L139-.L30
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_CrcJob_Process')
	.sect	'.debug_loc'
.L21:
	.word	-1,.L22,0,.L121-.L22
	.half	2
	.byte	138,0
	.word	0,0
.L123:
	.word	-1,.L22,0,.L169-.L22
	.half	5
	.byte	144,34,157,32,0
	.word	.L7-.L22,.L121-.L22
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L122:
	.word	-1,.L22,0,.L170-.L22
	.half	1
	.byte	100
	.word	.L171-.L22,.L121-.L22
	.half	1
	.byte	101
	.word	.L7-.L22,.L121-.L22
	.half	1
	.byte	100
	.word	0,0
.L127:
	.word	-1,.L22,.L169-.L22,.L7-.L22
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L126:
	.word	-1,.L22,.L170-.L22,.L7-.L22
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_Crc_Crc16_Calculate')
	.sect	'.debug_loc'
.L159:
	.word	-1,.L38,0,.L180-.L38
	.half	1
	.byte	101
	.word	.L181-.L38,.L156-.L38
	.half	1
	.byte	111
	.word	0,0
.L157:
	.word	-1,.L38,0,.L180-.L38
	.half	1
	.byte	100
	.word	0,0
.L158:
	.word	-1,.L38,0,.L180-.L38
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L37:
	.word	-1,.L38,0,.L156-.L38
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_Crc_Crc16_Compare')
	.sect	'.debug_loc'
.L161:
	.word	-1,.L40,0,.L160-.L40
	.half	1
	.byte	100
	.word	0,0
.L162:
	.word	-1,.L40,0,.L160-.L40
	.half	1
	.byte	101
	.word	0,0
.L39:
	.word	-1,.L40,0,.L160-.L40
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_Crc_Crc16_CopyToBuffer')
	.sect	'.debug_loc'
.L164:
	.word	-1,.L42,0,.L163-.L42
	.half	1
	.byte	100
	.word	0,0
.L41:
	.word	-1,.L42,0,.L163-.L42
	.half	2
	.byte	138,0
	.word	0,0
.L165:
	.word	-1,.L42,0,.L163-.L42
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_Crc_NoCrc_Calculate')
	.sect	'.debug_loc'
.L148:
	.word	-1,.L32,0,.L143-.L32
	.half	1
	.byte	101
	.word	0,0
.L145:
	.word	-1,.L32,0,.L143-.L32
	.half	1
	.byte	100
	.word	0,0
.L146:
	.word	-1,.L32,0,.L143-.L32
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L31:
	.word	-1,.L32,0,.L143-.L32
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_Crc_NoCrc_Compare')
	.sect	'.debug_loc'
.L150:
	.word	-1,.L34,0,.L149-.L34
	.half	1
	.byte	100
	.word	0,0
.L152:
	.word	-1,.L34,0,.L149-.L34
	.half	1
	.byte	101
	.word	0,0
.L33:
	.word	-1,.L34,0,.L149-.L34
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_Crc_NoCrc_CopyToBuffer')
	.sect	'.debug_loc'
.L154:
	.word	-1,.L36,0,.L153-.L36
	.half	1
	.byte	100
	.word	0,0
.L35:
	.word	-1,.L36,0,.L153-.L36
	.half	2
	.byte	138,0
	.word	0,0
.L155:
	.word	-1,.L36,0,.L153-.L36
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L287:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('NvM_CrcJob_Create')
	.sect	'.debug_frame'
	.word	20
	.word	.L287,.L20,.L113-.L20
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_CrcJob_Process')
	.sect	'.debug_frame'
	.word	12
	.word	.L287,.L22,.L121-.L22
	.sdecl	'.debug_frame',debug,cluster('NvM_CrcJob_Compare')
	.sect	'.debug_frame'
	.word	12
	.word	.L287,.L24,.L132-.L24
	.sdecl	'.debug_frame',debug,cluster('NvM_CrcJob_CopyToBuffer')
	.sect	'.debug_frame'
	.word	12
	.word	.L287,.L26,.L128-.L26
	.sdecl	'.debug_frame',debug,cluster('NvM_CrcJob_ExportBufferedValue')
	.sect	'.debug_frame'
	.word	12
	.word	.L287,.L28,.L135-.L28
	.sdecl	'.debug_frame',debug,cluster('NvM_CrcJob_ImportBufferedValue')
	.sect	'.debug_frame'
	.word	12
	.word	.L287,.L30,.L139-.L30
	.sdecl	'.debug_frame',debug,cluster('NvM_Crc_NoCrc_Calculate')
	.sect	'.debug_frame'
	.word	24
	.word	.L287,.L32,.L143-.L32
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_Crc_NoCrc_Compare')
	.sect	'.debug_frame'
	.word	24
	.word	.L287,.L34,.L149-.L34
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_Crc_NoCrc_CopyToBuffer')
	.sect	'.debug_frame'
	.word	24
	.word	.L287,.L36,.L153-.L36
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_Crc_Crc16_Calculate')
	.sect	'.debug_frame'
	.word	12
	.word	.L287,.L38,.L156-.L38
	.sdecl	'.debug_frame',debug,cluster('NvM_Crc_Crc16_Compare')
	.sect	'.debug_frame'
	.word	20
	.word	.L287,.L40,.L160-.L40
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_Crc_Crc16_CopyToBuffer')
	.sect	'.debug_frame'
	.word	20
	.word	.L287,.L42,.L163-.L42
	.byte	8,18,8,19,8,22,8,23

; ..\eeprom\NvM\NvM_Crc.c	   745  #endif /* (NVM_USE_CRC16 == STD_ON) */
; ..\eeprom\NvM\NvM_Crc.c	   746  
; ..\eeprom\NvM\NvM_Crc.c	   747  #if (NVM_USE_CRC32 == STD_ON) /* CRC32 handler, only available if there is at least one NvM block configured with CRC32 */
; ..\eeprom\NvM\NvM_Crc.c	   748  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   749  *  NvM_Crc_Crc32_Calculate
; ..\eeprom\NvM\NvM_Crc.c	   750  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   751  /*!
; ..\eeprom\NvM\NvM_Crc.c	   752   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   753   *
; ..\eeprom\NvM\NvM_Crc.c	   754   *
; ..\eeprom\NvM\NvM_Crc.c	   755   */
; ..\eeprom\NvM\NvM_Crc.c	   756  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_Crc32_Calculate(NvM_ConstRamAddressType DataPtr, uint16 Length, NvM_CrcValuePtrType CurrCrc)
; ..\eeprom\NvM\NvM_Crc.c	   757  {
; ..\eeprom\NvM\NvM_Crc.c	   758      *CurrCrc = Crc_CalculateCRC32(DataPtr, Length, (*CurrCrc) ^ NVM_CRC32_XOR_VALUE, FALSE); /* SBSW_NvM_FuncCall_CrcModule */ /* SBSW_NvM_AccessPtr_CrcValue */
; ..\eeprom\NvM\NvM_Crc.c	   759  }
; ..\eeprom\NvM\NvM_Crc.c	   760  
; ..\eeprom\NvM\NvM_Crc.c	   761  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   762  *  NvM_Crc_Crc32_Compare
; ..\eeprom\NvM\NvM_Crc.c	   763  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   764  /*!
; ..\eeprom\NvM\NvM_Crc.c	   765   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   766   *
; ..\eeprom\NvM\NvM_Crc.c	   767   *
; ..\eeprom\NvM\NvM_Crc.c	   768   */
; ..\eeprom\NvM\NvM_Crc.c	   769  NVM_LOCAL FUNC(boolean, NVM_PRIVATE_CODE) NvM_Crc_Crc32_Compare(NvM_CrcBufferConstPtrType Crc1, NvM_CrcValueRefType Crc2)
; ..\eeprom\NvM\NvM_Crc.c	   770  {
; ..\eeprom\NvM\NvM_Crc.c	   771      return (boolean)((Crc1[0] == Crc2[0]) && (Crc1[1] == Crc2[1]) && (Crc1[2] == Crc2[2]) && (Crc1[3] == Crc2[3]));
; ..\eeprom\NvM\NvM_Crc.c	   772  }
; ..\eeprom\NvM\NvM_Crc.c	   773  
; ..\eeprom\NvM\NvM_Crc.c	   774  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   775  *  NvM_Crc_Crc32_CopyToBuffer
; ..\eeprom\NvM\NvM_Crc.c	   776  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Crc.c	   777  /*!
; ..\eeprom\NvM\NvM_Crc.c	   778   * Internal comment removed.
; ..\eeprom\NvM\NvM_Crc.c	   779   *
; ..\eeprom\NvM\NvM_Crc.c	   780   *
; ..\eeprom\NvM\NvM_Crc.c	   781   */
; ..\eeprom\NvM\NvM_Crc.c	   782  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_Crc_Crc32_CopyToBuffer (NvM_CrcBufferPtrType Dest, NvM_CrcValueRefType Src)
; ..\eeprom\NvM\NvM_Crc.c	   783  {
; ..\eeprom\NvM\NvM_Crc.c	   784      Dest[0] = Src[0]; /* SBSW_NvM_AccessArray_CrcBuffers */
; ..\eeprom\NvM\NvM_Crc.c	   785      Dest[1] = Src[1]; /* SBSW_NvM_AccessArray_CrcBuffers */
; ..\eeprom\NvM\NvM_Crc.c	   786      Dest[2] = Src[2]; /* SBSW_NvM_AccessArray_CrcBuffers */
; ..\eeprom\NvM\NvM_Crc.c	   787      Dest[3] = Src[3]; /* SBSW_NvM_AccessArray_CrcBuffers */
; ..\eeprom\NvM\NvM_Crc.c	   788  }
; ..\eeprom\NvM\NvM_Crc.c	   789  #endif /* (NVM_USE_CRC32 == STD_ON) */
; ..\eeprom\NvM\NvM_Crc.c	   790  
; ..\eeprom\NvM\NvM_Crc.c	   791  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_Crc.c	   792    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Crc.c	   793  
; ..\eeprom\NvM\NvM_Crc.c	   794  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Crc.c	   795   *  END OF FILE: NvM_Crc.c
; ..\eeprom\NvM\NvM_Crc.c	   796   *********************************************************************************************************************/

	; Module end
