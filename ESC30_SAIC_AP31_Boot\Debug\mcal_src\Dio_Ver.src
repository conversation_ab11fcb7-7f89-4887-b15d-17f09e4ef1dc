	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc8036a -c99 --dep-file=mcal_src\\.Dio_Ver.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Dio_Ver.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Dio_Ver.src ..\\mcal_src\\Dio_Ver.c"
	.compiler_name		"ctc"
	.name	"Dio_Ver"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_Init

; ..\mcal_src\Dio_Ver.c	     1  /******************************************************************************
; ..\mcal_src\Dio_Ver.c	     2  **                                                                           **
; ..\mcal_src\Dio_Ver.c	     3  ** Copyright (C) Infineon Technologies (2018)                                **
; ..\mcal_src\Dio_Ver.c	     4  **                                                                           **
; ..\mcal_src\Dio_Ver.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Dio_Ver.c	     6  **                                                                           **
; ..\mcal_src\Dio_Ver.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Dio_Ver.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Dio_Ver.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Dio_Ver.c	    10  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    11  *******************************************************************************
; ..\mcal_src\Dio_Ver.c	    12  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    13  **  $FILENAME   : Dio_Ver.c $                                                **
; ..\mcal_src\Dio_Ver.c	    14  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    15  **  $CC VERSION : \main\24 $                                                 **
; ..\mcal_src\Dio_Ver.c	    16  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    17  **  $DATE       : 2018-07-20 $                                               **
; ..\mcal_src\Dio_Ver.c	    18  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Dio_Ver.c	    20  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Dio_Ver.c	    22  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    23  **  DESCRIPTION : This contains the functionality for DIO driver             **
; ..\mcal_src\Dio_Ver.c	    24  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    25  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Dio_Ver.c	    26  **                                                                           **
; ..\mcal_src\Dio_Ver.c	    27  *******************************************************************************
; ..\mcal_src\Dio_Ver.c	    28  ** TRACEABILITY : [cover parentID= DS_NAS_DIO_PR699,DS_NAS_DIO_PR730,
; ..\mcal_src\Dio_Ver.c	    29                                     DS_MCAL_DIO_0517,
; ..\mcal_src\Dio_Ver.c	    30                                     DS_MCAL_DIO_0503_0504_0508,
; ..\mcal_src\Dio_Ver.c	    31                                     DS_MCAL_AS4XX_DIO_0509_1_0513_0_0500,
; ..\mcal_src\Dio_Ver.c	    32                                     DS_MCAL_AS4XX_DIO_0509_4_0513_3]          **
; ..\mcal_src\Dio_Ver.c	    33  **                [/cover]                                                   **
; ..\mcal_src\Dio_Ver.c	    34  ******************************************************************************/
; ..\mcal_src\Dio_Ver.c	    35  
; ..\mcal_src\Dio_Ver.c	    36  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	    37  **                      Includes                                              **
; ..\mcal_src\Dio_Ver.c	    38  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	    39  
; ..\mcal_src\Dio_Ver.c	    40  /* Own header file, this includes own configuration file also */
; ..\mcal_src\Dio_Ver.c	    41  /* DIO117: Inclusion structure */
; ..\mcal_src\Dio_Ver.c	    42  #include "Dio.h"
; ..\mcal_src\Dio_Ver.c	    43  
; ..\mcal_src\Dio_Ver.c	    44  /* AS Version Specific header file */
; ..\mcal_src\Dio_Ver.c	    45  #include "Dio_Ver.h"
; ..\mcal_src\Dio_Ver.c	    46  
; ..\mcal_src\Dio_Ver.c	    47  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	    48  **                      Imported Compiler Switch Check                        **
; ..\mcal_src\Dio_Ver.c	    49  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	    50  
; ..\mcal_src\Dio_Ver.c	    51  /* Version checks */
; ..\mcal_src\Dio_Ver.c	    52  
; ..\mcal_src\Dio_Ver.c	    53  #ifndef DIO_AR_RELEASE_MAJOR_VERSION
; ..\mcal_src\Dio_Ver.c	    54    #error "DIO_AR_RELEASE_MAJOR_VERSION is not defined. "
; ..\mcal_src\Dio_Ver.c	    55  #endif
; ..\mcal_src\Dio_Ver.c	    56  
; ..\mcal_src\Dio_Ver.c	    57  #ifndef DIO_AR_RELEASE_MINOR_VERSION
; ..\mcal_src\Dio_Ver.c	    58    #error "DIO_AR_RELEASE_MINOR_VERSION is not defined. "
; ..\mcal_src\Dio_Ver.c	    59  #endif
; ..\mcal_src\Dio_Ver.c	    60  
; ..\mcal_src\Dio_Ver.c	    61  #ifndef DIO_AR_RELEASE_REVISION_VERSION
; ..\mcal_src\Dio_Ver.c	    62    #error "DIO_AR_RELEASE_REVISION_VERSION is not defined. "
; ..\mcal_src\Dio_Ver.c	    63  #endif
; ..\mcal_src\Dio_Ver.c	    64  
; ..\mcal_src\Dio_Ver.c	    65  #if ( DIO_AR_RELEASE_MAJOR_VERSION != 4U)
; ..\mcal_src\Dio_Ver.c	    66    #error "DIO_AR_RELEASE_MAJOR_VERSION does not match. "
; ..\mcal_src\Dio_Ver.c	    67  #endif
; ..\mcal_src\Dio_Ver.c	    68  
; ..\mcal_src\Dio_Ver.c	    69  #if ( DIO_AR_RELEASE_MINOR_VERSION != 0U )
; ..\mcal_src\Dio_Ver.c	    70    #error "DIO_AR_RELEASE_MINOR_VERSION does not match. "
; ..\mcal_src\Dio_Ver.c	    71  #endif
; ..\mcal_src\Dio_Ver.c	    72  
; ..\mcal_src\Dio_Ver.c	    73  
; ..\mcal_src\Dio_Ver.c	    74  #if (DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	    75  
; ..\mcal_src\Dio_Ver.c	    76  #ifndef DET_AR_RELEASE_MAJOR_VERSION
; ..\mcal_src\Dio_Ver.c	    77    #error "DET_AR_RELEASE_MAJOR_VERSION is not defined. "
; ..\mcal_src\Dio_Ver.c	    78  #endif
; ..\mcal_src\Dio_Ver.c	    79  
; ..\mcal_src\Dio_Ver.c	    80  #ifndef DET_AR_RELEASE_MINOR_VERSION
; ..\mcal_src\Dio_Ver.c	    81    #error "DET_AR_RELEASE_MINOR_VERSION is not defined. "
; ..\mcal_src\Dio_Ver.c	    82  #endif
; ..\mcal_src\Dio_Ver.c	    83  
; ..\mcal_src\Dio_Ver.c	    84  /* UTP AI00251674 fix */
; ..\mcal_src\Dio_Ver.c	    85  #if (IFX_DET_VERSION_CHECK == STD_ON)
; ..\mcal_src\Dio_Ver.c	    86  
; ..\mcal_src\Dio_Ver.c	    87  #if ( DET_AR_RELEASE_MAJOR_VERSION != 4U )
; ..\mcal_src\Dio_Ver.c	    88    #error "DET_AR_RELEASE_MAJOR_VERSION does not match. "
; ..\mcal_src\Dio_Ver.c	    89  #endif
; ..\mcal_src\Dio_Ver.c	    90  
; ..\mcal_src\Dio_Ver.c	    91  #if ( DET_AR_RELEASE_MINOR_VERSION != 0U )
; ..\mcal_src\Dio_Ver.c	    92    #error "DET_AR_RELEASE_MINOR_VERSION does not match. "
; ..\mcal_src\Dio_Ver.c	    93  #endif
; ..\mcal_src\Dio_Ver.c	    94  
; ..\mcal_src\Dio_Ver.c	    95  #endif /* (IFX_DET_VERSION_CHECK == STD_ON) */
; ..\mcal_src\Dio_Ver.c	    96  
; ..\mcal_src\Dio_Ver.c	    97  #endif /*#if (DIO_DEV_ERROR_DETECT == STD_ON)*/
; ..\mcal_src\Dio_Ver.c	    98  
; ..\mcal_src\Dio_Ver.c	    99  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   100  **                      Private Variable macro                                **
; ..\mcal_src\Dio_Ver.c	   101  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   102  
; ..\mcal_src\Dio_Ver.c	   103  /* IOCR0 register offset in Port_RegType */
; ..\mcal_src\Dio_Ver.c	   104  #define DIO_PORT_IOCR0_REG_OFFSET  (4U)
; ..\mcal_src\Dio_Ver.c	   105  
; ..\mcal_src\Dio_Ver.c	   106  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   107  **                      Private Variable Definitions                          **
; ..\mcal_src\Dio_Ver.c	   108  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   109  
; ..\mcal_src\Dio_Ver.c	   110  #if((DIO_LOADABLE_USED == STD_ON) || (DIO_CONFIG_COUNT > 1U))
; ..\mcal_src\Dio_Ver.c	   111    #define DIO_START_SEC_VAR_32BIT
; ..\mcal_src\Dio_Ver.c	   112    #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   113  
; ..\mcal_src\Dio_Ver.c	   114    const Dio_ConfigType  *Dio_kConfigPtr;
; ..\mcal_src\Dio_Ver.c	   115  
; ..\mcal_src\Dio_Ver.c	   116    #define DIO_STOP_SEC_VAR_32BIT
; ..\mcal_src\Dio_Ver.c	   117    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio_Ver.c	   118    is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   119    #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   120  #else
; ..\mcal_src\Dio_Ver.c	   121    #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio_Ver.c	   122      #if(DIO_PB_FIXED_ADDRESS == STD_ON)
; ..\mcal_src\Dio_Ver.c	   123        #define DIO_START_SEC_CONST_32BIT
; ..\mcal_src\Dio_Ver.c	   124        #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   125  
; ..\mcal_src\Dio_Ver.c	   126        const Dio_ConfigType * const Dio_kConfigPtr = &Dio_ConfigRoot[0];
; ..\mcal_src\Dio_Ver.c	   127  
; ..\mcal_src\Dio_Ver.c	   128        #define DIO_STOP_SEC_CONST_32BIT
; ..\mcal_src\Dio_Ver.c	   129        /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after
; ..\mcal_src\Dio_Ver.c	   130          pre-processor directives is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   131        #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   132      #else
; ..\mcal_src\Dio_Ver.c	   133        #define DIO_START_SEC_VAR_32BIT
; ..\mcal_src\Dio_Ver.c	   134        #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   135  
; ..\mcal_src\Dio_Ver.c	   136        const Dio_ConfigType  *Dio_kConfigPtr;
; ..\mcal_src\Dio_Ver.c	   137  
; ..\mcal_src\Dio_Ver.c	   138        #define DIO_STOP_SEC_VAR_32BIT
; ..\mcal_src\Dio_Ver.c	   139        /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after
; ..\mcal_src\Dio_Ver.c	   140          pre-processor directives is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   141        #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   142      #endif /* DIO_PB_FIXED_ADDRESS == STD_ON */
; ..\mcal_src\Dio_Ver.c	   143    #endif /* DIO_DEV_ERROR_DETECT == STD_ON || DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio_Ver.c	   144  #endif /* DIO_LOADABLE_USED == STD_ON */
; ..\mcal_src\Dio_Ver.c	   145  
; ..\mcal_src\Dio_Ver.c	   146  #if (DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   147    #define DIO_START_SEC_VAR_8BIT
; ..\mcal_src\Dio_Ver.c	   148    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio_Ver.c	   149      is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   150    #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   151    /* Init Status Variable.
; ..\mcal_src\Dio_Ver.c	   152       It has to be initialized to "0U" after every reset,
; ..\mcal_src\Dio_Ver.c	   153       as "0" represents the deinitialized state */
; ..\mcal_src\Dio_Ver.c	   154    static  uint8 Dio_InitStatus;
; ..\mcal_src\Dio_Ver.c	   155    #define DIO_STOP_SEC_VAR_8BIT
; ..\mcal_src\Dio_Ver.c	   156    /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio_Ver.c	   157      is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   158    #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   159  #endif /* DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio_Ver.c	   160  
; ..\mcal_src\Dio_Ver.c	   161  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   162  **                      Private Function Declarations                         **
; ..\mcal_src\Dio_Ver.c	   163  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   164  
; ..\mcal_src\Dio_Ver.c	   165  #if(DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   166  
; ..\mcal_src\Dio_Ver.c	   167  /*Memory Map of the DIO Code*/
; ..\mcal_src\Dio_Ver.c	   168  #define DIO_START_SEC_CODE
; ..\mcal_src\Dio_Ver.c	   169  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Dio_Ver.c	   170  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio_Ver.c	   171    is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   172  #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   173  
; ..\mcal_src\Dio_Ver.c	   174  /* This function returns the init status of DIO module */
; ..\mcal_src\Dio_Ver.c	   175  static uint8 Dio_lcheckDetStatus(uint8 ApiId);
; ..\mcal_src\Dio_Ver.c	   176  
; ..\mcal_src\Dio_Ver.c	   177  /*Memory Map of the DIO Code*/
; ..\mcal_src\Dio_Ver.c	   178  #define DIO_STOP_SEC_CODE
; ..\mcal_src\Dio_Ver.c	   179  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Dio_Ver.c	   180  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio_Ver.c	   181    is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   182  #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   183  
; ..\mcal_src\Dio_Ver.c	   184  #endif /* DIO_DEV_ERROR_DETECT */
; ..\mcal_src\Dio_Ver.c	   185  
; ..\mcal_src\Dio_Ver.c	   186  
; ..\mcal_src\Dio_Ver.c	   187  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   188  **                      Private Function Definition                           **
; ..\mcal_src\Dio_Ver.c	   189  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   190  
; ..\mcal_src\Dio_Ver.c	   191  /*Memory Map of the DIO Code*/
; ..\mcal_src\Dio_Ver.c	   192  #define DIO_START_SEC_CODE
; ..\mcal_src\Dio_Ver.c	   193  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Dio_Ver.c	   194  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio_Ver.c	   195    is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   196  #include "MemMap.h"
; ..\mcal_src\Dio_Ver.c	   197  
; ..\mcal_src\Dio_Ver.c	   198  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   199  ** Traceability     : [cover parentID=DS_AS40X_DIO165,DS_AS40X_DIO166,
; ..\mcal_src\Dio_Ver.c	   200                         DS_AS40X_DIO167_DIO176,DS_AS40X_DIO129,
; ..\mcal_src\Dio_Ver.c	   201                         SAS_MCAL_AS4XX_DIO_0500]                               **
; ..\mcal_src\Dio_Ver.c	   202  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   203  ** Syntax           : void Dio_Init                                           **
; ..\mcal_src\Dio_Ver.c	   204  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   205  **                      const Dio_ConfigType * ConfigPtr                      **
; ..\mcal_src\Dio_Ver.c	   206  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   207  ** [/cover]                                                                   **
; ..\mcal_src\Dio_Ver.c	   208  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   209  ** Service ID       : 0x10                                                    **
; ..\mcal_src\Dio_Ver.c	   210  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   211  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   212  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   213  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Dio_Ver.c	   214  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   215  ** Parameters(in)   : ConfigPtr - Pointer to DIO configuration                **
; ..\mcal_src\Dio_Ver.c	   216  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   217  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio_Ver.c	   218  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   219  ** Return value     : none                                                    **
; ..\mcal_src\Dio_Ver.c	   220  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   221  ** Description      : This function:                                          **
; ..\mcal_src\Dio_Ver.c	   222  **   - DIO166: Initializes all global variables of the Dio module             **
; ..\mcal_src\Dio_Ver.c	   223  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   224  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   225  void Dio_Init(const Dio_ConfigType *ConfigPtr)
; Function Dio_Init
.L8:
Dio_Init:	.type	func

; ..\mcal_src\Dio_Ver.c	   226  {
; ..\mcal_src\Dio_Ver.c	   227     #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio_Ver.c	   228     uint8 DetError;
; ..\mcal_src\Dio_Ver.c	   229     #if(DIO_PB_FIXED_ADDRESS == STD_OFF)
; ..\mcal_src\Dio_Ver.c	   230       if (ConfigPtr == NULL_PTR)
; ..\mcal_src\Dio_Ver.c	   231       {
; ..\mcal_src\Dio_Ver.c	   232          DetError = 1U;
; ..\mcal_src\Dio_Ver.c	   233       }
; ..\mcal_src\Dio_Ver.c	   234     #else
; ..\mcal_src\Dio_Ver.c	   235       if(ConfigPtr != Dio_kConfigPtr)
; ..\mcal_src\Dio_Ver.c	   236       {
; ..\mcal_src\Dio_Ver.c	   237          DetError = 1U;
; ..\mcal_src\Dio_Ver.c	   238       }
; ..\mcal_src\Dio_Ver.c	   239     #endif /*DIO_PB_FIXED_ADDRESS*/
; ..\mcal_src\Dio_Ver.c	   240  
; ..\mcal_src\Dio_Ver.c	   241     #if(DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio_Ver.c	   242       else if((ConfigPtr->Dio_MarkerCheckValue) !=
; ..\mcal_src\Dio_Ver.c	   243        (((uint32)DIO_MODULE_ID << DIO_MODULEID_SHIFT_VAL) | DIO_INSTANCE_ID))
; ..\mcal_src\Dio_Ver.c	   244       {
; ..\mcal_src\Dio_Ver.c	   245          DetError = 1U;
; ..\mcal_src\Dio_Ver.c	   246       }
; ..\mcal_src\Dio_Ver.c	   247     #endif /* DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio_Ver.c	   248     else
; ..\mcal_src\Dio_Ver.c	   249     {
; ..\mcal_src\Dio_Ver.c	   250        DetError = 0U;
; ..\mcal_src\Dio_Ver.c	   251     }
; ..\mcal_src\Dio_Ver.c	   252     if(DetError != 1U)
; ..\mcal_src\Dio_Ver.c	   253     #endif /* (DIO_DEV_ERROR_DETECT) || (DIO_SAFETY_ENABLE == STD_ON)*/
; ..\mcal_src\Dio_Ver.c	   254     {
; ..\mcal_src\Dio_Ver.c	   255     /* Store ConfigPtr to use in other APIs */
; ..\mcal_src\Dio_Ver.c	   256     #if((DIO_LOADABLE_USED == STD_ON) || (DIO_CONFIG_COUNT > 1U))
; ..\mcal_src\Dio_Ver.c	   257      Dio_kConfigPtr = ConfigPtr;
; ..\mcal_src\Dio_Ver.c	   258     #else
; ..\mcal_src\Dio_Ver.c	   259      #if((DIO_PB_FIXED_ADDRESS == STD_OFF) &&           \ 
; ..\mcal_src\Dio_Ver.c	   260          ((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON)))
; ..\mcal_src\Dio_Ver.c	   261        Dio_kConfigPtr = ConfigPtr;
; ..\mcal_src\Dio_Ver.c	   262      #else
; ..\mcal_src\Dio_Ver.c	   263        UNUSED_PARAMETER(ConfigPtr)
; ..\mcal_src\Dio_Ver.c	   264      #endif /* PBfixed && (DET || Safety) */
; ..\mcal_src\Dio_Ver.c	   265     #endif /* DIO_LOADABLE_USED */
; ..\mcal_src\Dio_Ver.c	   266  
; ..\mcal_src\Dio_Ver.c	   267     #if(DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   268     Dio_InitStatus = DIO_INITIALIZED;
; ..\mcal_src\Dio_Ver.c	   269     #endif /* DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio_Ver.c	   270     }
; ..\mcal_src\Dio_Ver.c	   271     #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio_Ver.c	   272     else
; ..\mcal_src\Dio_Ver.c	   273     {
; ..\mcal_src\Dio_Ver.c	   274      #if(DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   275         /* Report DIO_E_PARAM_CONFIG DET */
; ..\mcal_src\Dio_Ver.c	   276         Det_ReportError(
; ..\mcal_src\Dio_Ver.c	   277           (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio_Ver.c	   278           DIO_INSTANCE_ID,
; ..\mcal_src\Dio_Ver.c	   279           DIO_SID_INIT,
; ..\mcal_src\Dio_Ver.c	   280           DIO_E_PARAM_CONFIG);
; ..\mcal_src\Dio_Ver.c	   281      #endif /* DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio_Ver.c	   282  
; ..\mcal_src\Dio_Ver.c	   283      #if(DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio_Ver.c	   284         /* Report DIO_E_PARAM_CONFIG DET */
; ..\mcal_src\Dio_Ver.c	   285         SafeMcal_ReportError(
; ..\mcal_src\Dio_Ver.c	   286           (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio_Ver.c	   287           DIO_INSTANCE_ID,
; ..\mcal_src\Dio_Ver.c	   288           DIO_SID_INIT,
; ..\mcal_src\Dio_Ver.c	   289           DIO_E_PARAM_CONFIG);
; ..\mcal_src\Dio_Ver.c	   290      #endif /* DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio_Ver.c	   291     }
; ..\mcal_src\Dio_Ver.c	   292     #endif
; ..\mcal_src\Dio_Ver.c	   293     /* DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio_Ver.c	   294  }/* Dio_Init */
	ret
.L25:
	
__Dio_Init_function_end:
	.size	Dio_Init,__Dio_Init_function_end-Dio_Init
.L19:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_FlipChannel')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_FlipChannel

; ..\mcal_src\Dio_Ver.c	   295  
; ..\mcal_src\Dio_Ver.c	   296  #if (DIO_INITCHECK_API == STD_ON)
; ..\mcal_src\Dio_Ver.c	   297  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   298  ** Traceability     : [cover parentID= DS_MCAL_AS4XX_DIO_0528,
; ..\mcal_src\Dio_Ver.c	   299                                         DS_AS4XX_DIO_PR912_4,
; ..\mcal_src\Dio_Ver.c	   300  **                                     DS_MCAL_AS4XX_DIO_0520,
; ..\mcal_src\Dio_Ver.c	   301  **                                     DS_MCAL_AS4XX_DIO_0523,
; ..\mcal_src\Dio_Ver.c	   302  **                                     DS_MCAL_AS4XX_DIO_0521,
; ..\mcal_src\Dio_Ver.c	   303  **                                     DS_MCAL_AS4XX_DIO_0522]           **
; ..\mcal_src\Dio_Ver.c	   304  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   305  ** Syntax           : Std_ReturnType Dio_InitCheck                            **
; ..\mcal_src\Dio_Ver.c	   306  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   307  **                      const Dio_ConfigType * ConfigPtr                      **
; ..\mcal_src\Dio_Ver.c	   308  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   309  ** [/cover]                                                                   **
; ..\mcal_src\Dio_Ver.c	   310  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   311  ** Service ID       : 0x10                                                    **
; ..\mcal_src\Dio_Ver.c	   312  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   313  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   314  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   315  ** Reentrancy       : non - reentrant                                         **
; ..\mcal_src\Dio_Ver.c	   316  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   317  ** Parameters(in)   : ConfigPtr - Pointer to DIO configuration                **
; ..\mcal_src\Dio_Ver.c	   318  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   319  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio_Ver.c	   320  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   321  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio_Ver.c	   322  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   323  ** Description      : This function verifies the initialization               **
; ..\mcal_src\Dio_Ver.c	   324  **                    done by Dio_Init                                        **
; ..\mcal_src\Dio_Ver.c	   325  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   326  Std_ReturnType Dio_InitCheck(const Dio_ConfigType *ConfigPtr)
; ..\mcal_src\Dio_Ver.c	   327  {
; ..\mcal_src\Dio_Ver.c	   328    Std_ReturnType RetVal;
; ..\mcal_src\Dio_Ver.c	   329  
; ..\mcal_src\Dio_Ver.c	   330    RetVal = E_NOT_OK;
; ..\mcal_src\Dio_Ver.c	   331  
; ..\mcal_src\Dio_Ver.c	   332    if(Dio_kConfigPtr == ConfigPtr)
; ..\mcal_src\Dio_Ver.c	   333    {
; ..\mcal_src\Dio_Ver.c	   334      RetVal = E_OK;
; ..\mcal_src\Dio_Ver.c	   335    }
; ..\mcal_src\Dio_Ver.c	   336    return(RetVal);
; ..\mcal_src\Dio_Ver.c	   337  }/* Dio_InitCheck */
; ..\mcal_src\Dio_Ver.c	   338  #endif /* (DIO_INITCHECK_API == STD_ON) */
; ..\mcal_src\Dio_Ver.c	   339  
; ..\mcal_src\Dio_Ver.c	   340  /* Enable / Disable the use of the function */
; ..\mcal_src\Dio_Ver.c	   341  #if (DIO_FLIP_CHANNEL_API == STD_ON)
; ..\mcal_src\Dio_Ver.c	   342  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   343  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   344  ** Traceability     : [cover parentID=DS_AS40X_DIO190,DS_AS40X_DIO191,
; ..\mcal_src\Dio_Ver.c	   345                         DS_AS40X_DIO192,DS_AS40X_DIO193,DS_AS40X_DIO060_3,
; ..\mcal_src\Dio_Ver.c	   346                         DS_AS_DIO089,DS_AS40X_DIO118_4,DS_AS4XX_DIO_PR912_1]   **
; ..\mcal_src\Dio_Ver.c	   347  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   348  ** Syntax           : Dio_LevelType Dio_FlipChannel                           **
; ..\mcal_src\Dio_Ver.c	   349  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   350  **                      Dio_ChannelType ChannelId                             **
; ..\mcal_src\Dio_Ver.c	   351  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   352  ** [/cover]                                                                   **
; ..\mcal_src\Dio_Ver.c	   353  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   354  ** Service ID       : 0x11                                                    **
; ..\mcal_src\Dio_Ver.c	   355  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   356  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   357  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   358  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Dio_Ver.c	   359  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   360  ** Parameters (in)  : ChannelId - ChannelId whose level to be inverted        **
; ..\mcal_src\Dio_Ver.c	   361  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   362  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio_Ver.c	   363  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   364  ** Return value     : Dio_LevelType - The function returns value or the level **
; ..\mcal_src\Dio_Ver.c	   365  **                  of the specified channel which is of type Dio_LevelType   **
; ..\mcal_src\Dio_Ver.c	   366  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   367  ** Description      : This function: Flip the channel level                   **
; ..\mcal_src\Dio_Ver.c	   368  **      - DIO191: returns the inverted level of specified output channel      **
; ..\mcal_src\Dio_Ver.c	   369  **      - DIO192: The specified channel is input, API shall have no influence **
; ..\mcal_src\Dio_Ver.c	   370  **                      on the physical output of the channel                 **
; ..\mcal_src\Dio_Ver.c	   371  **      - DIO193: The specified channel is input, API shall have no influence **
; ..\mcal_src\Dio_Ver.c	   372  **                      on the result of next Read service                    **
; ..\mcal_src\Dio_Ver.c	   373  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   374  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   375  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   376  Dio_LevelType Dio_FlipChannel(Dio_ChannelType ChannelId)
; Function Dio_FlipChannel
.L10:
Dio_FlipChannel:	.type	func
	mov	d9,d4
.L42:

; ..\mcal_src\Dio_Ver.c	   377  {
; ..\mcal_src\Dio_Ver.c	   378    Ifx_P           *GetPortAddressPtr;
; ..\mcal_src\Dio_Ver.c	   379    uint32           OmrVal;
; ..\mcal_src\Dio_Ver.c	   380    uint32           PinNumber;
; ..\mcal_src\Dio_Ver.c	   381    uint32           PinPosition;
; ..\mcal_src\Dio_Ver.c	   382    Dio_LevelType    RetVal;
; ..\mcal_src\Dio_Ver.c	   383    volatile uint32  *IocrRegPtr;
; ..\mcal_src\Dio_Ver.c	   384  
; ..\mcal_src\Dio_Ver.c	   385    RetVal = (Dio_LevelType)STD_LOW;
	mov	d10,#0
.L43:

; ..\mcal_src\Dio_Ver.c	   386    OmrVal = DIO_OMR_RESET_BIT;
; ..\mcal_src\Dio_Ver.c	   387  
; ..\mcal_src\Dio_Ver.c	   388    #if(DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   389    if(Dio_lcheckDetStatus(DIO_SID_FLIPCHANNEL) == DIO_NO_ERROR)
; ..\mcal_src\Dio_Ver.c	   390    #endif /* DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio_Ver.c	   391    {
; ..\mcal_src\Dio_Ver.c	   392      /* Check for the validity of symbolic channel ID
; ..\mcal_src\Dio_Ver.c	   393         Reported DET if error detected */
; ..\mcal_src\Dio_Ver.c	   394    #if((DIO_SAFETY_ENABLE == STD_ON) || (DIO_DEV_ERROR_DETECT == STD_ON))
; ..\mcal_src\Dio_Ver.c	   395      if(Dio_lCheckChannelId(DIO_SID_FLIPCHANNEL,ChannelId) ==
; ..\mcal_src\Dio_Ver.c	   396                (uint8)DIO_NO_ERROR)
; ..\mcal_src\Dio_Ver.c	   397    #endif /*DIO_DEV_ERROR_DETECT == STD_ON || DIO_SAFETY_ENABLE == STD_ON*/
; ..\mcal_src\Dio_Ver.c	   398      {
; ..\mcal_src\Dio_Ver.c	   399        /* GetPortAddressPtr will hold the port address */
; ..\mcal_src\Dio_Ver.c	   400        GetPortAddressPtr = Dio_lGetPortAdr(Dio_lGetPortNumber(ChannelId));
	movh	d11,#1
	call	Dio_lGetPortNumber
.L41:
	mov	d4,d2
	call	Dio_lGetPortAdr
.L44:
	mov.aa	a15,a2
.L46:

; ..\mcal_src\Dio_Ver.c	   401  
; ..\mcal_src\Dio_Ver.c	   402        /* Get the IOCR0 register address of particular port */
; ..\mcal_src\Dio_Ver.c	   403        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Dio_Ver.c	   404          due to GetPortAddressPtr pointing to a structure and
; ..\mcal_src\Dio_Ver.c	   405          is within allowed range.*/
; ..\mcal_src\Dio_Ver.c	   406        IocrRegPtr = ((volatile uint32*)(volatile void*)GetPortAddressPtr +
	lea	a12,[a15]16
.L47:

; ..\mcal_src\Dio_Ver.c	   407                                                   DIO_PORT_IOCR0_REG_OFFSET);
; ..\mcal_src\Dio_Ver.c	   408        PinNumber = Dio_lGetPinNumber(ChannelId);
	mov	d4,d9
	call	Dio_lGetPinNumber
.L45:
	mov	d12,d2
.L49:

; ..\mcal_src\Dio_Ver.c	   409  
; ..\mcal_src\Dio_Ver.c	   410        /* Get the Pin position */
; ..\mcal_src\Dio_Ver.c	   411        PinPosition = ((uint32)0x01U << Dio_lGetPinNumber(ChannelId));
	mov	d8,#1
.L64:
	mov	d4,d9
	call	Dio_lGetPinNumber
.L48:

; ..\mcal_src\Dio_Ver.c	   412  
; ..\mcal_src\Dio_Ver.c	   413        /* Read the Channel level and decide the return value */
; ..\mcal_src\Dio_Ver.c	   414        if((PinPosition & (DIO_SFR_RUNTIME_USER_MODE_READ32\ 
	sh	d8,d8,d2
	ld.w	d15,[a15]36
.L50:
	and	d8,d15
.L51:

; ..\mcal_src\Dio_Ver.c	   415                             (GetPortAddressPtr->IN.U)))!= (Dio_LevelType)STD_LOW)
; ..\mcal_src\Dio_Ver.c	   416        {
; ..\mcal_src\Dio_Ver.c	   417          /* current level of the channel*/
; ..\mcal_src\Dio_Ver.c	   418           RetVal = (Dio_LevelType)STD_HIGH;
; ..\mcal_src\Dio_Ver.c	   419        }
; ..\mcal_src\Dio_Ver.c	   420        /*Check the channel is configured as output channel*/
; ..\mcal_src\Dio_Ver.c	   421        /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Dio_Ver.c	   422          due to IocrRegPtr pointing to a structure and
; ..\mcal_src\Dio_Ver.c	   423          is within allowed range.*/
; ..\mcal_src\Dio_Ver.c	   424        if(((DIO_SFR_RUNTIME_USER_MODE_READ32(*(IocrRegPtr + \ 
	insert	d15,d12,#0,#0,#2
.L65:
	seln	d10,d8,d10,#1
.L66:
	addsc.a	a2,a12,d15,#0
.L67:

; ..\mcal_src\Dio_Ver.c	   425             (PinNumber/DIO_NUM_FOUR))) >> DIO_IOCR0_BIT_SHIFT_COUNT(PinNumber))\ 
	and	d15,d12,#3
.L68:
	ld.w	d0,[a2]
.L69:
	sh	d15,#3
.L70:
	rsub	d15,#0
	sh	d0,d0,d15
.L71:
	jz.t	d0:7,.L3
.L72:

; ..\mcal_src\Dio_Ver.c	   426                     & (uint32)DIO_PORT_DIR_MSK) != (uint32)DIO_PORT_PIN_IN)
; ..\mcal_src\Dio_Ver.c	   427        {
; ..\mcal_src\Dio_Ver.c	   428           /* Invert the Level of dio channel */
; ..\mcal_src\Dio_Ver.c	   429           if(RetVal == STD_LOW)
	jne	d10,#0,.L4
.L73:

; ..\mcal_src\Dio_Ver.c	   430           {
; ..\mcal_src\Dio_Ver.c	   431              RetVal = (Dio_LevelType)STD_HIGH;
	mov	d10,#1
.L74:

; ..\mcal_src\Dio_Ver.c	   432              OmrVal = 0x01U;
	mov	d11,d10
	j	.L5
.L4:

; ..\mcal_src\Dio_Ver.c	   433           }
; ..\mcal_src\Dio_Ver.c	   434           else
; ..\mcal_src\Dio_Ver.c	   435           {
; ..\mcal_src\Dio_Ver.c	   436              RetVal = (Dio_LevelType)STD_LOW;
	mov	d10,#0
.L5:

; ..\mcal_src\Dio_Ver.c	   437           }
; ..\mcal_src\Dio_Ver.c	   438           /* Write to the PORT OMR register to reflect at the channel*/
; ..\mcal_src\Dio_Ver.c	   439           DIO_SFR_RUNTIME_USER_MODE_WRITE32(GetPortAddressPtr->OMR.U, \ 
	sh	d11,d11,d12
	st.w	[a15]4,d11
.L3:

; ..\mcal_src\Dio_Ver.c	   440                                          (unsigned_int)(OmrVal << PinNumber));
; ..\mcal_src\Dio_Ver.c	   441        }
; ..\mcal_src\Dio_Ver.c	   442      }
; ..\mcal_src\Dio_Ver.c	   443    }
; ..\mcal_src\Dio_Ver.c	   444    return RetVal;
; ..\mcal_src\Dio_Ver.c	   445  }/* Dio_FlipChannel */
	mov	d2,d10
	ret
.L29:
	
__Dio_FlipChannel_function_end:
	.size	Dio_FlipChannel,__Dio_FlipChannel_function_end-Dio_FlipChannel
.L24:
	; End of function
	
	.calls	'Dio_FlipChannel','Dio_lGetPortNumber'
	.calls	'Dio_FlipChannel','Dio_lGetPortAdr'
	.calls	'Dio_FlipChannel','Dio_lGetPinNumber'
	.calls	'Dio_Init','',0
	.extern	Dio_lGetPortNumber
	.extern	Dio_lGetPortAdr
	.extern	Dio_lGetPinNumber
	.calls	'Dio_FlipChannel','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L12:
	.word	9159
	.half	3
	.word	.L13
	.byte	4
.L11:
	.byte	1
	.byte	'..\\mcal_src\\Dio_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L14
	.byte	2
	.byte	'Dio_ConfigType',0,1,147,2,16,12,2
	.byte	'Dio_PortChannelIdType',0,1,139,2,16,8
.L34:
	.byte	3
	.byte	'unsigned long int',0,4,7,4
	.byte	'Dio_PortIdConfig',0,4
	.word	225
	.byte	2,35,0,4
	.byte	'Dio_ChannelConfig',0,4
	.word	225
	.byte	2,35,4,0,5
	.word	197
	.byte	6
	.word	300
	.byte	4
	.byte	'Dio_PortChannelConfigPtr',0,4
	.word	305
	.byte	2,35,0,2
	.byte	'Dio_ChannelGroupType',0,1,129,2,16,4
.L30:
	.byte	3
	.byte	'unsigned short int',0,2,7,4
	.byte	'mask',0,2
	.word	371
	.byte	2,35,0
.L28:
	.byte	3
	.byte	'unsigned char',0,1,8,4
	.byte	'offset',0,1
	.word	407
	.byte	2,35,2,4
	.byte	'port',0,1
	.word	407
	.byte	2,35,3,0,5
	.word	344
	.byte	6
	.word	455
	.byte	4
	.byte	'Dio_ChannelGroupConfigPtr',0,4
	.word	460
	.byte	2,35,4,4
	.byte	'Dio_ChannelGroupConfigSize',0,4
	.word	225
	.byte	2,35,8,0,5
	.word	176
.L26:
	.byte	6
	.word	537
	.byte	2
	.byte	'_Ifx_P',0,2,159,5,25,128,2,7,2,239,4,9,4,3
	.byte	'unsigned int',0,4,7,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,3
	.byte	'int',0,4,5,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OUT_Bits',0,2,231,2,16,4,8
	.byte	'P0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'P2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'P3',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'P4',0,1
	.word	407
	.byte	1,3,2,35,0,8
	.byte	'P5',0,1
	.word	407
	.byte	1,2,2,35,0,8
	.byte	'P6',0,1
	.word	407
	.byte	1,1,2,35,0,8
	.byte	'P7',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'P8',0,1
	.word	407
	.byte	1,7,2,35,1,8
	.byte	'P9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'P10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'P11',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'P12',0,1
	.word	407
	.byte	1,3,2,35,1,8
	.byte	'P13',0,1
	.word	407
	.byte	1,2,2,35,1,8
	.byte	'P14',0,1
	.word	407
	.byte	1,1,2,35,1,8
	.byte	'P15',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	371
	.byte	16,0,2,35,2,0,4
	.byte	'B',0,4
	.word	612
	.byte	2,35,0,0,4
	.byte	'OUT',0,4
	.word	561
	.byte	2,35,0,7,2,191,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMR_Bits',0,2,129,2,16,4,8
	.byte	'PS0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'PS2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'PS3',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'PS4',0,1
	.word	407
	.byte	1,3,2,35,0,8
	.byte	'PS5',0,1
	.word	407
	.byte	1,2,2,35,0,8
	.byte	'PS6',0,1
	.word	407
	.byte	1,1,2,35,0,8
	.byte	'PS7',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'PS8',0,1
	.word	407
	.byte	1,7,2,35,1,8
	.byte	'PS9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'PS10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'PS11',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'PS12',0,1
	.word	407
	.byte	1,3,2,35,1,8
	.byte	'PS13',0,1
	.word	407
	.byte	1,2,2,35,1,8
	.byte	'PS14',0,1
	.word	407
	.byte	1,1,2,35,1,8
	.byte	'PS15',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'PCL0',0,1
	.word	407
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	407
	.byte	1,6,2,35,2,8
	.byte	'PCL2',0,1
	.word	407
	.byte	1,5,2,35,2,8
	.byte	'PCL3',0,1
	.word	407
	.byte	1,4,2,35,2,8
	.byte	'PCL4',0,1
	.word	407
	.byte	1,3,2,35,2,8
	.byte	'PCL5',0,1
	.word	407
	.byte	1,2,2,35,2,8
	.byte	'PCL6',0,1
	.word	407
	.byte	1,1,2,35,2,8
	.byte	'PCL7',0,1
	.word	407
	.byte	1,0,2,35,2,8
	.byte	'PCL8',0,1
	.word	407
	.byte	1,7,2,35,3,8
	.byte	'PCL9',0,1
	.word	407
	.byte	1,6,2,35,3,8
	.byte	'PCL10',0,1
	.word	407
	.byte	1,5,2,35,3,8
	.byte	'PCL11',0,1
	.word	407
	.byte	1,4,2,35,3,8
	.byte	'PCL12',0,1
	.word	407
	.byte	1,3,2,35,3,8
	.byte	'PCL13',0,1
	.word	407
	.byte	1,2,2,35,3,8
	.byte	'PCL14',0,1
	.word	407
	.byte	1,1,2,35,3,8
	.byte	'PCL15',0,1
	.word	407
	.byte	1,0,2,35,3,0,4
	.byte	'B',0,4
	.word	941
	.byte	2,35,0,0,4
	.byte	'OMR',0,4
	.word	913
	.byte	2,35,4,7,2,231,3,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_ID_Bits',0,2,110,16,4,8
	.byte	'MODREV',0,1
	.word	407
	.byte	8,0,2,35,0,8
	.byte	'MODTYPE',0,1
	.word	407
	.byte	8,0,2,35,1,8
	.byte	'MODNUMBER',0,2
	.word	371
	.byte	16,0,2,35,2,0,4
	.byte	'B',0,4
	.word	1525
	.byte	2,35,0,0,4
	.byte	'ID',0,4
	.word	1497
	.byte	2,35,8,9,4
	.word	407
	.byte	10,3,0,4
	.byte	'reserved_C',0,4
	.word	1628
	.byte	2,35,12,7,2,247,3,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_IOCR0_Bits',0,2,140,1,16,4,8
	.byte	'reserved_0',0,1
	.word	407
	.byte	3,5,2,35,0,8
	.byte	'PC0',0,1
	.word	407
	.byte	5,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	407
	.byte	3,5,2,35,1,8
	.byte	'PC1',0,1
	.word	407
	.byte	5,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	407
	.byte	3,5,2,35,2,8
	.byte	'PC2',0,1
	.word	407
	.byte	5,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	407
	.byte	3,5,2,35,3,8
	.byte	'PC3',0,1
	.word	407
	.byte	5,0,2,35,3,0,4
	.byte	'B',0,4
	.word	1685
	.byte	2,35,0,0,4
	.byte	'IOCR0',0,4
	.word	1657
	.byte	2,35,16,7,2,135,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_IOCR4_Bits',0,2,166,1,16,4,8
	.byte	'reserved_0',0,1
	.word	407
	.byte	3,5,2,35,0,8
	.byte	'PC4',0,1
	.word	407
	.byte	5,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	407
	.byte	3,5,2,35,1,8
	.byte	'PC5',0,1
	.word	407
	.byte	5,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	407
	.byte	3,5,2,35,2,8
	.byte	'PC6',0,1
	.word	407
	.byte	5,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	407
	.byte	3,5,2,35,3,8
	.byte	'PC7',0,1
	.word	407
	.byte	5,0,2,35,3,0,4
	.byte	'B',0,4
	.word	1915
	.byte	2,35,0,0,4
	.byte	'IOCR4',0,4
	.word	1887
	.byte	2,35,20,7,2,143,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_IOCR8_Bits',0,2,179,1,16,4,8
	.byte	'reserved_0',0,1
	.word	407
	.byte	3,5,2,35,0,8
	.byte	'PC8',0,1
	.word	407
	.byte	5,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	407
	.byte	3,5,2,35,1,8
	.byte	'PC9',0,1
	.word	407
	.byte	5,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	407
	.byte	3,5,2,35,2,8
	.byte	'PC10',0,1
	.word	407
	.byte	5,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	407
	.byte	3,5,2,35,3,8
	.byte	'PC11',0,1
	.word	407
	.byte	5,0,2,35,3,0,4
	.byte	'B',0,4
	.word	2145
	.byte	2,35,0,0,4
	.byte	'IOCR8',0,4
	.word	2117
	.byte	2,35,24,7,2,255,3,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_IOCR12_Bits',0,2,153,1,16,4,8
	.byte	'reserved_0',0,1
	.word	407
	.byte	3,5,2,35,0,8
	.byte	'PC12',0,1
	.word	407
	.byte	5,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	407
	.byte	3,5,2,35,1,8
	.byte	'PC13',0,1
	.word	407
	.byte	5,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	407
	.byte	3,5,2,35,2,8
	.byte	'PC14',0,1
	.word	407
	.byte	5,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	407
	.byte	3,5,2,35,3,8
	.byte	'PC15',0,1
	.word	407
	.byte	5,0,2,35,3,0,4
	.byte	'B',0,4
	.word	2377
	.byte	2,35,0,0,4
	.byte	'IOCR12',0,4
	.word	2349
	.byte	2,35,28,4
	.byte	'reserved_20',0,4
	.word	1628
	.byte	2,35,32,7,2,239,3,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_IN_Bits',0,2,118,16,4,8
	.byte	'P0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'P2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'P3',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'P4',0,1
	.word	407
	.byte	1,3,2,35,0,8
	.byte	'P5',0,1
	.word	407
	.byte	1,2,2,35,0,8
	.byte	'P6',0,1
	.word	407
	.byte	1,1,2,35,0,8
	.byte	'P7',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'P8',0,1
	.word	407
	.byte	1,7,2,35,1,8
	.byte	'P9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'P10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'P11',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'P12',0,1
	.word	407
	.byte	1,3,2,35,1,8
	.byte	'P13',0,1
	.word	407
	.byte	1,2,2,35,1,8
	.byte	'P14',0,1
	.word	407
	.byte	1,1,2,35,1,8
	.byte	'P15',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	371
	.byte	16,0,2,35,2,0,4
	.byte	'B',0,4
	.word	2634
	.byte	2,35,0,0,4
	.byte	'IN',0,4
	.word	2606
	.byte	2,35,36,9,24
	.word	407
	.byte	10,23,0,4
	.byte	'reserved_28',0,24
	.word	2932
	.byte	2,35,40,7,2,135,5,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_PDR0_Bits',0,2,160,3,16,4,8
	.byte	'PD0',0,1
	.word	407
	.byte	3,5,2,35,0,8
	.byte	'PL0',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'PD1',0,1
	.word	407
	.byte	3,1,2,35,0,8
	.byte	'PL1',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'PD2',0,1
	.word	407
	.byte	3,5,2,35,1,8
	.byte	'PL2',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'PD3',0,1
	.word	407
	.byte	3,1,2,35,1,8
	.byte	'PL3',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'PD4',0,1
	.word	407
	.byte	3,5,2,35,2,8
	.byte	'PL4',0,1
	.word	407
	.byte	1,4,2,35,2,8
	.byte	'PD5',0,1
	.word	407
	.byte	3,1,2,35,2,8
	.byte	'PL5',0,1
	.word	407
	.byte	1,0,2,35,2,8
	.byte	'PD6',0,1
	.word	407
	.byte	3,5,2,35,3,8
	.byte	'PL6',0,1
	.word	407
	.byte	1,4,2,35,3,8
	.byte	'PD7',0,1
	.word	407
	.byte	3,1,2,35,3,8
	.byte	'PL7',0,1
	.word	407
	.byte	1,0,2,35,3,0,4
	.byte	'B',0,4
	.word	2990
	.byte	2,35,0,0,4
	.byte	'PDR0',0,4
	.word	2962
	.byte	2,35,64,7,2,143,5,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_PDR1_Bits',0,2,181,3,16,4,8
	.byte	'PD8',0,1
	.word	407
	.byte	3,5,2,35,0,8
	.byte	'PL8',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'PD9',0,1
	.word	407
	.byte	3,1,2,35,0,8
	.byte	'PL9',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'PD10',0,1
	.word	407
	.byte	3,5,2,35,1,8
	.byte	'PL10',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'PD11',0,1
	.word	407
	.byte	3,1,2,35,1,8
	.byte	'PL11',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'PD12',0,1
	.word	407
	.byte	3,5,2,35,2,8
	.byte	'PL12',0,1
	.word	407
	.byte	1,4,2,35,2,8
	.byte	'PD13',0,1
	.word	407
	.byte	3,1,2,35,2,8
	.byte	'PL13',0,1
	.word	407
	.byte	1,0,2,35,2,8
	.byte	'PD14',0,1
	.word	407
	.byte	3,5,2,35,3,8
	.byte	'PL14',0,1
	.word	407
	.byte	1,4,2,35,3,8
	.byte	'PD15',0,1
	.word	407
	.byte	3,1,2,35,3,8
	.byte	'PL15',0,1
	.word	407
	.byte	1,0,2,35,3,0,4
	.byte	'B',0,4
	.word	3308
	.byte	2,35,0,0,4
	.byte	'PDR1',0,4
	.word	3280
	.byte	2,35,68,9,8
	.word	407
	.byte	10,7,0,4
	.byte	'reserved_48',0,8
	.word	3610
	.byte	2,35,72,7,2,223,3,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_ESR_Bits',0,2,88,16,4,8
	.byte	'EN0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	407
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	407
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	407
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	407
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	407
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	407
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	407
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	371
	.byte	16,0,2,35,2,0,4
	.byte	'B',0,4
	.word	3668
	.byte	2,35,0,0,4
	.byte	'ESR',0,4
	.word	3640
	.byte	2,35,80,9,12
	.word	407
	.byte	10,11,0,4
	.byte	'reserved_54',0,12
	.word	3984
	.byte	2,35,84,7,2,255,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_PDISC_Bits',0,2,138,3,16,4,8
	.byte	'PDIS0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'PDIS1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'PDIS2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'PDIS3',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'PDIS4',0,1
	.word	407
	.byte	1,3,2,35,0,8
	.byte	'PDIS5',0,1
	.word	407
	.byte	1,2,2,35,0,8
	.byte	'PDIS6',0,1
	.word	407
	.byte	1,1,2,35,0,8
	.byte	'PDIS7',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'PDIS8',0,1
	.word	407
	.byte	1,7,2,35,1,8
	.byte	'PDIS9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'PDIS10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'PDIS11',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'PDIS12',0,1
	.word	407
	.byte	1,3,2,35,1,8
	.byte	'PDIS13',0,1
	.word	407
	.byte	1,2,2,35,1,8
	.byte	'PDIS14',0,1
	.word	407
	.byte	1,1,2,35,1,8
	.byte	'PDIS15',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	371
	.byte	16,0,2,35,2,0,4
	.byte	'B',0,4
	.word	4042
	.byte	2,35,0,0,4
	.byte	'PDISC',0,4
	.word	4014
	.byte	2,35,96,7,2,247,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_PCSR_Bits',0,2,253,2,16,4,8
	.byte	'reserved_0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'SEL1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'SEL2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,2
	.word	371
	.byte	6,7,2,35,0,8
	.byte	'SEL9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'SEL10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'reserved_11',0,4
	.word	567
	.byte	20,1,2,35,2,8
	.byte	'LCK',0,1
	.word	407
	.byte	1,0,2,35,3,0,4
	.byte	'B',0,4
	.word	4423
	.byte	2,35,0,0,4
	.byte	'PCSR',0,4
	.word	4395
	.byte	2,35,100,4
	.byte	'reserved_64',0,8
	.word	3610
	.byte	2,35,104,7,2,207,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMSR0_Bits',0,2,166,2,16,4,8
	.byte	'PS0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'PS2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'PS3',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	567
	.byte	28,0,2,35,2,0,4
	.byte	'B',0,4
	.word	4669
	.byte	2,35,0,0,4
	.byte	'OMSR0',0,4
	.word	4641
	.byte	2,35,112,7,2,223,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMSR4_Bits',0,2,187,2,16,4,8
	.byte	'reserved_0',0,1
	.word	407
	.byte	4,4,2,35,0,8
	.byte	'PS4',0,1
	.word	407
	.byte	1,3,2,35,0,8
	.byte	'PS5',0,1
	.word	407
	.byte	1,2,2,35,0,8
	.byte	'PS6',0,1
	.word	407
	.byte	1,1,2,35,0,8
	.byte	'PS7',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	567
	.byte	24,0,2,35,2,0,4
	.byte	'B',0,4
	.word	4831
	.byte	2,35,0,0,4
	.byte	'OMSR4',0,4
	.word	4803
	.byte	2,35,116,7,2,231,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMSR8_Bits',0,2,198,2,16,4,8
	.byte	'reserved_0',0,1
	.word	407
	.byte	8,0,2,35,0,8
	.byte	'PS8',0,1
	.word	407
	.byte	1,7,2,35,1,8
	.byte	'PS9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'PS10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'PS11',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,4
	.word	567
	.byte	20,0,2,35,2,0,4
	.byte	'B',0,4
	.word	5015
	.byte	2,35,0,0,4
	.byte	'OMSR8',0,4
	.word	4987
	.byte	2,35,120,7,2,215,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMSR12_Bits',0,2,176,2,16,4,8
	.byte	'reserved_0',0,2
	.word	371
	.byte	12,4,2,35,0,8
	.byte	'PS12',0,1
	.word	407
	.byte	1,3,2,35,1,8
	.byte	'PS13',0,1
	.word	407
	.byte	1,2,2,35,1,8
	.byte	'PS14',0,1
	.word	407
	.byte	1,1,2,35,1,8
	.byte	'PS15',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	371
	.byte	16,0,2,35,2,0,4
	.byte	'B',0,4
	.word	5202
	.byte	2,35,0,0,4
	.byte	'OMSR12',0,4
	.word	5174
	.byte	2,35,124,7,2,159,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMCR0_Bits',0,2,192,1,16,4,8
	.byte	'reserved_0',0,2
	.word	371
	.byte	16,0,2,35,0,8
	.byte	'PCL0',0,1
	.word	407
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	407
	.byte	1,6,2,35,2,8
	.byte	'PCL2',0,1
	.word	407
	.byte	1,5,2,35,2,8
	.byte	'PCL3',0,1
	.word	407
	.byte	1,4,2,35,2,8
	.byte	'reserved_20',0,2
	.word	371
	.byte	12,0,2,35,2,0,4
	.byte	'B',0,4
	.word	5393
	.byte	2,35,0,0,4
	.byte	'OMCR0',0,4
	.word	5365
	.byte	3,35,128,1,7,2,175,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMCR4_Bits',0,2,213,1,16,4,8
	.byte	'reserved_0',0,4
	.word	567
	.byte	20,12,2,35,2,8
	.byte	'PCL4',0,1
	.word	407
	.byte	1,3,2,35,2,8
	.byte	'PCL5',0,1
	.word	407
	.byte	1,2,2,35,2,8
	.byte	'PCL6',0,1
	.word	407
	.byte	1,1,2,35,2,8
	.byte	'PCL7',0,1
	.word	407
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	407
	.byte	8,0,2,35,3,0,4
	.byte	'B',0,4
	.word	5583
	.byte	2,35,0,0,4
	.byte	'OMCR4',0,4
	.word	5555
	.byte	3,35,132,1,7,2,183,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMCR8_Bits',0,2,224,1,16,4,8
	.byte	'reserved_0',0,4
	.word	567
	.byte	24,8,2,35,2,8
	.byte	'PCL8',0,1
	.word	407
	.byte	1,7,2,35,3,8
	.byte	'PCL9',0,1
	.word	407
	.byte	1,6,2,35,3,8
	.byte	'PCL10',0,1
	.word	407
	.byte	1,5,2,35,3,8
	.byte	'PCL11',0,1
	.word	407
	.byte	1,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	407
	.byte	4,0,2,35,3,0,4
	.byte	'B',0,4
	.word	5773
	.byte	2,35,0,0,4
	.byte	'OMCR8',0,4
	.word	5745
	.byte	3,35,136,1,7,2,167,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMCR12_Bits',0,2,203,1,16,4,8
	.byte	'reserved_0',0,4
	.word	567
	.byte	28,4,2,35,2,8
	.byte	'PCL12',0,1
	.word	407
	.byte	1,3,2,35,3,8
	.byte	'PCL13',0,1
	.word	407
	.byte	1,2,2,35,3,8
	.byte	'PCL14',0,1
	.word	407
	.byte	1,1,2,35,3,8
	.byte	'PCL15',0,1
	.word	407
	.byte	1,0,2,35,3,0,4
	.byte	'B',0,4
	.word	5965
	.byte	2,35,0,0,4
	.byte	'OMCR12',0,4
	.word	5937
	.byte	3,35,140,1,7,2,199,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMSR_Bits',0,2,209,2,16,4,8
	.byte	'PS0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'PS2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'PS3',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'PS4',0,1
	.word	407
	.byte	1,3,2,35,0,8
	.byte	'PS5',0,1
	.word	407
	.byte	1,2,2,35,0,8
	.byte	'PS6',0,1
	.word	407
	.byte	1,1,2,35,0,8
	.byte	'PS7',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'PS8',0,1
	.word	407
	.byte	1,7,2,35,1,8
	.byte	'PS9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'PS10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'PS11',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'PS12',0,1
	.word	407
	.byte	1,3,2,35,1,8
	.byte	'PS13',0,1
	.word	407
	.byte	1,2,2,35,1,8
	.byte	'PS14',0,1
	.word	407
	.byte	1,1,2,35,1,8
	.byte	'PS15',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	371
	.byte	16,0,2,35,2,0,4
	.byte	'B',0,4
	.word	6138
	.byte	2,35,0,0,4
	.byte	'OMSR',0,4
	.word	6110
	.byte	3,35,144,1,7,2,151,4,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_OMCR_Bits',0,2,235,1,16,4,8
	.byte	'reserved_0',0,2
	.word	371
	.byte	16,0,2,35,0,8
	.byte	'PCL0',0,1
	.word	407
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	407
	.byte	1,6,2,35,2,8
	.byte	'PCL2',0,1
	.word	407
	.byte	1,5,2,35,2,8
	.byte	'PCL3',0,1
	.word	407
	.byte	1,4,2,35,2,8
	.byte	'PCL4',0,1
	.word	407
	.byte	1,3,2,35,2,8
	.byte	'PCL5',0,1
	.word	407
	.byte	1,2,2,35,2,8
	.byte	'PCL6',0,1
	.word	407
	.byte	1,1,2,35,2,8
	.byte	'PCL7',0,1
	.word	407
	.byte	1,0,2,35,2,8
	.byte	'PCL8',0,1
	.word	407
	.byte	1,7,2,35,3,8
	.byte	'PCL9',0,1
	.word	407
	.byte	1,6,2,35,3,8
	.byte	'PCL10',0,1
	.word	407
	.byte	1,5,2,35,3,8
	.byte	'PCL11',0,1
	.word	407
	.byte	1,4,2,35,3,8
	.byte	'PCL12',0,1
	.word	407
	.byte	1,3,2,35,3,8
	.byte	'PCL13',0,1
	.word	407
	.byte	1,2,2,35,3,8
	.byte	'PCL14',0,1
	.word	407
	.byte	1,1,2,35,3,8
	.byte	'PCL15',0,1
	.word	407
	.byte	1,0,2,35,3,0,4
	.byte	'B',0,4
	.word	6486
	.byte	2,35,0,0,4
	.byte	'OMCR',0,4
	.word	6458
	.byte	3,35,148,1,9,96
	.word	407
	.byte	10,95,0,4
	.byte	'reserved_98',0,96
	.word	6821
	.byte	3,35,152,1,7,2,215,3,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_ACCEN1_Bits',0,2,82,16,4,8
	.byte	'reserved_0',0,4
	.word	567
	.byte	32,0,2,35,2,0,4
	.byte	'B',0,4
	.word	6880
	.byte	2,35,0,0,4
	.byte	'ACCEN1',0,4
	.word	6852
	.byte	3,35,248,1,7,2,207,3,9,4,4
	.byte	'U',0,4
	.word	567
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	594
	.byte	2,35,0,2
	.byte	'_Ifx_P_ACCEN0_Bits',0,2,45,16,4,8
	.byte	'EN0',0,1
	.word	407
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	407
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	407
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	407
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	407
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	407
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	407
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	407
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	407
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	407
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	407
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	407
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	407
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	407
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	407
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	407
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	407
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	407
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	407
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	407
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	407
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	407
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	407
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	407
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	407
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	407
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	407
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	407
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	407
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	407
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	407
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	407
	.byte	1,0,2,35,3,0,4
	.byte	'B',0,4
	.word	6984
	.byte	2,35,0,0,4
	.byte	'ACCEN0',0,4
	.word	6956
	.byte	3,35,252,1,0,11
	.word	547
.L32:
	.byte	6
	.word	7541
	.byte	11
	.word	225
.L39:
	.byte	6
	.word	7551
	.byte	12
	.byte	'Dio_lGetPortNumber',0,3,144,1,21
	.word	407
	.byte	1,1,1,1,13
	.byte	'ChannelId',0,3,144,1,56
	.word	371
	.byte	0,12
	.byte	'Dio_lGetPortAdr',0,3,147,1,15
	.word	7546
	.byte	1,1,1,1,13
	.byte	'PortNumber',0,3,147,1,44
	.word	407
	.byte	0,12
	.byte	'Dio_lGetPinNumber',0,3,151,1,14
	.word	407
	.byte	1,1,1,1,13
	.byte	'ChannelId',0,3,151,1,48
	.word	371
	.byte	0,14
	.byte	'void',0,6
	.word	7714
	.byte	15
	.byte	'__prof_adm',0,4,1,1
	.word	7720
	.byte	16,1,6
	.word	7744
	.byte	15
	.byte	'__codeptr',0,4,1,1
	.word	7746
	.byte	15
	.byte	'uint8',0,5,90,29
	.word	407
	.byte	15
	.byte	'uint16',0,5,92,29
	.word	371
	.byte	15
	.byte	'uint32',0,5,94,29
	.word	225
	.byte	15
	.byte	'Ifx_P_ACCEN0_Bits',0,2,79,3
	.word	6984
	.byte	15
	.byte	'Ifx_P_ACCEN1_Bits',0,2,85,3
	.word	6880
	.byte	15
	.byte	'Ifx_P_ESR_Bits',0,2,107,3
	.word	3668
	.byte	15
	.byte	'Ifx_P_ID_Bits',0,2,115,3
	.word	1525
	.byte	15
	.byte	'Ifx_P_IN_Bits',0,2,137,1,3
	.word	2634
	.byte	15
	.byte	'Ifx_P_IOCR0_Bits',0,2,150,1,3
	.word	1685
	.byte	15
	.byte	'Ifx_P_IOCR12_Bits',0,2,163,1,3
	.word	2377
	.byte	15
	.byte	'Ifx_P_IOCR4_Bits',0,2,176,1,3
	.word	1915
	.byte	15
	.byte	'Ifx_P_IOCR8_Bits',0,2,189,1,3
	.word	2145
	.byte	15
	.byte	'Ifx_P_OMCR0_Bits',0,2,200,1,3
	.word	5393
	.byte	15
	.byte	'Ifx_P_OMCR12_Bits',0,2,210,1,3
	.word	5965
	.byte	15
	.byte	'Ifx_P_OMCR4_Bits',0,2,221,1,3
	.word	5583
	.byte	15
	.byte	'Ifx_P_OMCR8_Bits',0,2,232,1,3
	.word	5773
	.byte	15
	.byte	'Ifx_P_OMCR_Bits',0,2,254,1,3
	.word	6486
	.byte	15
	.byte	'Ifx_P_OMR_Bits',0,2,163,2,3
	.word	941
	.byte	15
	.byte	'Ifx_P_OMSR0_Bits',0,2,173,2,3
	.word	4669
	.byte	15
	.byte	'Ifx_P_OMSR12_Bits',0,2,184,2,3
	.word	5202
	.byte	15
	.byte	'Ifx_P_OMSR4_Bits',0,2,195,2,3
	.word	4831
	.byte	15
	.byte	'Ifx_P_OMSR8_Bits',0,2,206,2,3
	.word	5015
	.byte	15
	.byte	'Ifx_P_OMSR_Bits',0,2,228,2,3
	.word	6138
	.byte	15
	.byte	'Ifx_P_OUT_Bits',0,2,250,2,3
	.word	612
	.byte	15
	.byte	'Ifx_P_PCSR_Bits',0,2,135,3,3
	.word	4423
	.byte	15
	.byte	'Ifx_P_PDISC_Bits',0,2,157,3,3
	.word	4042
	.byte	15
	.byte	'Ifx_P_PDR0_Bits',0,2,178,3,3
	.word	2990
	.byte	15
	.byte	'Ifx_P_PDR1_Bits',0,2,199,3,3
	.word	3308
	.byte	15
	.byte	'Ifx_P_ACCEN0',0,2,212,3,3
	.word	6956
	.byte	15
	.byte	'Ifx_P_ACCEN1',0,2,220,3,3
	.word	6852
	.byte	15
	.byte	'Ifx_P_ESR',0,2,228,3,3
	.word	3640
	.byte	15
	.byte	'Ifx_P_ID',0,2,236,3,3
	.word	1497
	.byte	15
	.byte	'Ifx_P_IN',0,2,244,3,3
	.word	2606
	.byte	15
	.byte	'Ifx_P_IOCR0',0,2,252,3,3
	.word	1657
	.byte	15
	.byte	'Ifx_P_IOCR12',0,2,132,4,3
	.word	2349
	.byte	15
	.byte	'Ifx_P_IOCR4',0,2,140,4,3
	.word	1887
	.byte	15
	.byte	'Ifx_P_IOCR8',0,2,148,4,3
	.word	2117
	.byte	15
	.byte	'Ifx_P_OMCR',0,2,156,4,3
	.word	6458
	.byte	15
	.byte	'Ifx_P_OMCR0',0,2,164,4,3
	.word	5365
	.byte	15
	.byte	'Ifx_P_OMCR12',0,2,172,4,3
	.word	5937
	.byte	15
	.byte	'Ifx_P_OMCR4',0,2,180,4,3
	.word	5555
	.byte	15
	.byte	'Ifx_P_OMCR8',0,2,188,4,3
	.word	5745
	.byte	15
	.byte	'Ifx_P_OMR',0,2,196,4,3
	.word	913
	.byte	15
	.byte	'Ifx_P_OMSR',0,2,204,4,3
	.word	6110
	.byte	15
	.byte	'Ifx_P_OMSR0',0,2,212,4,3
	.word	4641
	.byte	15
	.byte	'Ifx_P_OMSR12',0,2,220,4,3
	.word	5174
	.byte	15
	.byte	'Ifx_P_OMSR4',0,2,228,4,3
	.word	4803
	.byte	15
	.byte	'Ifx_P_OMSR8',0,2,236,4,3
	.word	4987
	.byte	15
	.byte	'Ifx_P_OUT',0,2,244,4,3
	.word	561
	.byte	15
	.byte	'Ifx_P_PCSR',0,2,252,4,3
	.word	4395
	.byte	15
	.byte	'Ifx_P_PDISC',0,2,132,5,3
	.word	4014
	.byte	15
	.byte	'Ifx_P_PDR0',0,2,140,5,3
	.word	2962
	.byte	15
	.byte	'Ifx_P_PDR1',0,2,148,5,3
	.word	3280
	.byte	11
	.word	547
	.byte	15
	.byte	'Ifx_P',0,2,193,5,3
	.word	8960
	.byte	15
	.byte	'Dio_LevelType',0,1,237,1,17
	.word	407
	.byte	15
	.byte	'Dio_ChannelType',0,1,243,1,17
	.word	371
	.byte	15
	.byte	'Dio_PortType',0,1,249,1,17
	.word	407
	.byte	15
	.byte	'Dio_PortLevelType',0,1,252,1,17
	.word	371
	.byte	15
	.byte	'Dio_ChannelGroupType',0,1,137,2,3
	.word	344
	.byte	15
	.byte	'Dio_PortChannelIdType',0,1,145,2,2
	.word	197
	.byte	15
	.byte	'Dio_ConfigType',0,1,179,2,2
	.word	176
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,19,1,3,8,58,15,59,15,57,15,11,15,0,0,3,36,0,3,8,11,15
	.byte	62,15,0,0,4,13,0,3,8,11,15,73,19,56,9,0,0,5,38,0,73,19,0,0,6,15,0,73,19,0,0,7,23,1,58,15,59,15,57,15,11
	.byte	15,0,0,8,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,1,1,11,15,73,19,0,0,10,33,0,47,15,0,0,11,53,0,73
	.byte	19,0,0,12,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,13,5,0,3,8,58,15,59,15,57,15,73
	.byte	19,0,0,14,59,0,3,8,0,0,15,22,0,3,8,58,15,59,15,57,15,73,19,0,0,16,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L14:
	.word	.L53-.L52
.L52:
	.half	3
	.word	.L55-.L54
.L54:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxPort_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Dio_Ver.h',0,0,0,0
	.byte	'..\\mcal_src\\Dio_Ver.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0,0
.L55:
.L53:
	.sdecl	'.debug_info',debug,cluster('Dio_Init')
	.sect	'.debug_info'
.L15:
	.word	244
	.half	3
	.word	.L16
	.byte	4,1
	.byte	'..\\mcal_src\\Dio_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L18,.L17
	.byte	2
	.word	.L11
	.byte	3
	.byte	'Dio_Init',0,1,225,1,6,1,1,1
	.word	.L8,.L25,.L7
	.byte	4
	.byte	'ConfigPtr',0,1,225,1,37
	.word	.L26,.L27
	.byte	5
	.word	.L8,.L25
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_Init')
	.sect	'.debug_abbrev'
.L16:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_Init')
	.sect	'.debug_line'
.L17:
	.word	.L57-.L56
.L56:
	.half	3
	.word	.L59-.L58
.L58:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio_Ver.c',0,0,0,0,0
.L59:
	.byte	5,1,7,0,5,2
	.word	.L8
	.byte	3,165,2,1,7,9
	.half	.L19-.L8
	.byte	0,1,1
.L57:
	.sdecl	'.debug_ranges',debug,cluster('Dio_Init')
	.sect	'.debug_ranges'
.L18:
	.word	-1,.L8,0,.L19-.L8,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_FlipChannel')
	.sect	'.debug_info'
.L20:
	.word	399
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\mcal_src\\Dio_Ver.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L23,.L22
	.byte	2
	.word	.L11
	.byte	3
	.byte	'Dio_FlipChannel',0,1,248,2,15
	.word	.L28
	.byte	1,1,1
	.word	.L10,.L29,.L9
	.byte	4
	.byte	'ChannelId',0,1,248,2,47
	.word	.L30,.L31
	.byte	5
	.word	.L10,.L29
	.byte	6
	.byte	'GetPortAddressPtr',0,1,250,2,20
	.word	.L32,.L33
	.byte	6
	.byte	'OmrVal',0,1,251,2,20
	.word	.L34,.L35
	.byte	6
	.byte	'PinNumber',0,1,252,2,20
	.word	.L34,.L36
	.byte	6
	.byte	'PinPosition',0,1,253,2,20
	.word	.L34,.L37
	.byte	6
	.byte	'RetVal',0,1,254,2,20
	.word	.L28,.L38
	.byte	6
	.byte	'IocrRegPtr',0,1,255,2,21
	.word	.L39,.L40
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_FlipChannel')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_FlipChannel')
	.sect	'.debug_line'
.L22:
	.word	.L61-.L60
.L60:
	.half	3
	.word	.L63-.L62
.L62:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio_Ver.c',0,0,0,0,0
.L63:
	.byte	5,15,7,0,5,2
	.word	.L10
	.byte	3,247,2,1,5,10,9
	.half	.L42-.L10
	.byte	3,9,1,5,12,9
	.half	.L43-.L42
	.byte	3,1,1,5,62,3,14,1,5,61,9
	.half	.L41-.L43
	.byte	1,5,25,9
	.half	.L44-.L41
	.byte	1,5,73,9
	.half	.L46-.L44
	.byte	3,6,1,5,37,9
	.half	.L47-.L46
	.byte	3,2,1,5,17,9
	.half	.L45-.L47
	.byte	1,5,22,9
	.half	.L49-.L45
	.byte	3,3,1,5,57,9
	.half	.L64-.L49
	.byte	1,5,36,9
	.half	.L48-.L64
	.byte	1,5,26,3,3,1,5,23,9
	.half	.L50-.L48
	.byte	1,5,12,9
	.half	.L51-.L50
	.byte	3,10,1,5,17,9
	.half	.L65-.L51
	.byte	3,122,1,5,12,9
	.half	.L66-.L65
	.byte	3,6,1,5,42,9
	.half	.L67-.L66
	.byte	3,1,1,5,12,9
	.half	.L68-.L67
	.byte	3,127,1,5,42,9
	.half	.L69-.L68
	.byte	3,1,1,5,39,9
	.half	.L70-.L69
	.byte	1,5,7,9
	.half	.L71-.L70
	.byte	3,127,1,5,10,7,9
	.half	.L72-.L71
	.byte	3,5,1,5,20,7,9
	.half	.L73-.L72
	.byte	3,2,1,9
	.half	.L74-.L73
	.byte	3,1,1,5,45,3,127,1,5,20,9
	.half	.L4-.L74
	.byte	3,5,1,5,10,9
	.half	.L5-.L4
	.byte	3,3,1,5,3,9
	.half	.L3-.L5
	.byte	3,5,1,5,1,3,1,1,7,9
	.half	.L24-.L3
	.byte	0,1,1
.L61:
	.sdecl	'.debug_ranges',debug,cluster('Dio_FlipChannel')
	.sect	'.debug_ranges'
.L23:
	.word	-1,.L10,0,.L24-.L10,0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_FlipChannel')
	.sect	'.debug_loc'
.L31:
	.word	-1,.L10,0,.L41-.L10
	.half	5
	.byte	144,34,157,32,0
	.word	.L42-.L10,.L29-.L10
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L9:
	.word	-1,.L10,0,.L29-.L10
	.half	2
	.byte	138,0
	.word	0,0
.L33:
	.word	-1,.L10,.L44-.L10,.L45-.L10
	.half	1
	.byte	98
	.word	.L46-.L10,.L29-.L10
	.half	1
	.byte	111
	.word	0,0
.L40:
	.word	-1,.L10,.L47-.L10,.L29-.L10
	.half	1
	.byte	108
	.word	0,0
.L35:
	.word	-1,.L10,.L41-.L10,.L3-.L10
	.half	5
	.byte	144,37,157,32,32
	.word	0,0
.L36:
	.word	-1,.L10,.L45-.L10,.L48-.L10
	.half	5
	.byte	144,33,157,32,0
	.word	.L49-.L10,.L29-.L10
	.half	5
	.byte	144,38,157,32,0
	.word	0,0
.L37:
	.word	-1,.L10,.L50-.L10,.L51-.L10
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L38:
	.word	-1,.L10,.L43-.L10,.L29-.L10
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_Init')
	.sect	'.debug_loc'
.L27:
	.word	-1,.L8,0,.L25-.L8
	.half	1
	.byte	100
	.word	0,0
.L7:
	.word	-1,.L8,0,.L25-.L8
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L75:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Dio_Init')
	.sect	'.debug_frame'
	.word	24
	.word	.L75,.L8,.L25-.L8
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Dio_FlipChannel')
	.sect	'.debug_frame'
	.word	12
	.word	.L75,.L10,.L29-.L10

; ..\mcal_src\Dio_Ver.c	   446  #endif /*(DIO_FLIP_CHANNEL_API == STD_ON) */
; ..\mcal_src\Dio_Ver.c	   447  
; ..\mcal_src\Dio_Ver.c	   448  #if(DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   449  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   450  ** Syntax           : static uint8 Dio_lcheckDetStatus                        **
; ..\mcal_src\Dio_Ver.c	   451  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   452  **                      uint8 ApiId                                           **
; ..\mcal_src\Dio_Ver.c	   453  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   454  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   455  ** Service ID       : None                                                    **
; ..\mcal_src\Dio_Ver.c	   456  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   457  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   458  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   459  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio_Ver.c	   460  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   461  ** Parameters (in)  : ApiId - Service ID                                      **
; ..\mcal_src\Dio_Ver.c	   462  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   463  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio_Ver.c	   464  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   465  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio_Ver.c	   466  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   467  ** Description      :                                                         **
; ..\mcal_src\Dio_Ver.c	   468  ** - This function returns the init status of DIO module                      **
; ..\mcal_src\Dio_Ver.c	   469  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   470  static uint8 Dio_lcheckDetStatus(uint8 ApiId)
; ..\mcal_src\Dio_Ver.c	   471  {
; ..\mcal_src\Dio_Ver.c	   472    if(Dio_InitStatus != DIO_INITIALIZED)
; ..\mcal_src\Dio_Ver.c	   473    {
; ..\mcal_src\Dio_Ver.c	   474      Det_ReportError(
; ..\mcal_src\Dio_Ver.c	   475        (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio_Ver.c	   476        DIO_INSTANCE_ID,
; ..\mcal_src\Dio_Ver.c	   477        ApiId,
; ..\mcal_src\Dio_Ver.c	   478        DIO_E_UNINIT);
; ..\mcal_src\Dio_Ver.c	   479    }
; ..\mcal_src\Dio_Ver.c	   480    return Dio_InitStatus;
; ..\mcal_src\Dio_Ver.c	   481  }
; ..\mcal_src\Dio_Ver.c	   482  #endif /* DIO_DEV_ERROR_DETECT */
; ..\mcal_src\Dio_Ver.c	   483  
; ..\mcal_src\Dio_Ver.c	   484  
; ..\mcal_src\Dio_Ver.c	   485  #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio_Ver.c	   486  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   487  ** Syntax           : uint8 Dio_lErrorCheckChannelGroupDet                    **
; ..\mcal_src\Dio_Ver.c	   488  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   489  **                      const Dio_ChannelGroupType *ChannelGroupIdPtr,        **
; ..\mcal_src\Dio_Ver.c	   490  **                      uint8 ApiId                                           **
; ..\mcal_src\Dio_Ver.c	   491  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   492  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   493  ** Service ID       : None                                                    **
; ..\mcal_src\Dio_Ver.c	   494  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   495  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   496  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   497  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio_Ver.c	   498  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   499  ** Parameters (in)  : ApiId - Service ID                                      **
; ..\mcal_src\Dio_Ver.c	   500  **                    *ChannelGroupIdPtr - Pointer to the Group ofchannels    **
; ..\mcal_src\Dio_Ver.c	   501  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   502  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio_Ver.c	   503  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   504  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio_Ver.c	   505  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   506  ** Description      :                                                         **
; ..\mcal_src\Dio_Ver.c	   507  ** - This function returns the validity of the ChannelGroup                   **
; ..\mcal_src\Dio_Ver.c	   508  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   509  uint8 Dio_lErrorCheckChannelGroupDet(
; ..\mcal_src\Dio_Ver.c	   510               const Dio_ChannelGroupType *ChannelGroupIdPtr,
; ..\mcal_src\Dio_Ver.c	   511               uint8 ApiId)
; ..\mcal_src\Dio_Ver.c	   512  {
; ..\mcal_src\Dio_Ver.c	   513    uint8 RetVal;
; ..\mcal_src\Dio_Ver.c	   514    RetVal = DIO_ERROR;
; ..\mcal_src\Dio_Ver.c	   515  
; ..\mcal_src\Dio_Ver.c	   516    #if(DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   517    if(Dio_lcheckDetStatus(ApiId) == (uint8)DIO_NO_ERROR)
; ..\mcal_src\Dio_Ver.c	   518    #endif /* DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio_Ver.c	   519    {
; ..\mcal_src\Dio_Ver.c	   520       /* Check for the validity of group configuration
; ..\mcal_src\Dio_Ver.c	   521          Reports DET error if detected */
; ..\mcal_src\Dio_Ver.c	   522       if(Dio_lCheckGroupId(ApiId,ChannelGroupIdPtr) == (uint8)DIO_NO_ERROR)
; ..\mcal_src\Dio_Ver.c	   523       {
; ..\mcal_src\Dio_Ver.c	   524          RetVal = DIO_NO_ERROR;
; ..\mcal_src\Dio_Ver.c	   525       }
; ..\mcal_src\Dio_Ver.c	   526    }
; ..\mcal_src\Dio_Ver.c	   527    return RetVal;
; ..\mcal_src\Dio_Ver.c	   528  }
; ..\mcal_src\Dio_Ver.c	   529  
; ..\mcal_src\Dio_Ver.c	   530  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   531  ** Syntax           : uint8 Dio_lErrorCheckChannelDet                         **
; ..\mcal_src\Dio_Ver.c	   532  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   533  **                      Dio_ChannelType ChannelId,                            **
; ..\mcal_src\Dio_Ver.c	   534  **                      uint8 ApiId                                           **
; ..\mcal_src\Dio_Ver.c	   535  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   536  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   537  ** Service ID       : None                                                    **
; ..\mcal_src\Dio_Ver.c	   538  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   539  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   540  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   541  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio_Ver.c	   542  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   543  ** Parameters (in)  : ApiId - Service ID                                      **
; ..\mcal_src\Dio_Ver.c	   544  **                    ChannelId - Identifier for the PortPin                  **
; ..\mcal_src\Dio_Ver.c	   545  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   546  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio_Ver.c	   547  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   548  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio_Ver.c	   549  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   550  ** Description      :                                                         **
; ..\mcal_src\Dio_Ver.c	   551  ** - This function returns the validity of the ChannelId                      **
; ..\mcal_src\Dio_Ver.c	   552  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   553  uint8 Dio_lErrorCheckChannelDet(
; ..\mcal_src\Dio_Ver.c	   554               Dio_ChannelType ChannelId,
; ..\mcal_src\Dio_Ver.c	   555               uint8 ApiId)
; ..\mcal_src\Dio_Ver.c	   556  {
; ..\mcal_src\Dio_Ver.c	   557    uint8 RetVal;
; ..\mcal_src\Dio_Ver.c	   558  
; ..\mcal_src\Dio_Ver.c	   559    RetVal = DIO_ERROR;
; ..\mcal_src\Dio_Ver.c	   560  
; ..\mcal_src\Dio_Ver.c	   561    #if(DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   562    if(Dio_lcheckDetStatus(ApiId) == (uint8)DIO_NO_ERROR)
; ..\mcal_src\Dio_Ver.c	   563    #endif /* DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio_Ver.c	   564    {
; ..\mcal_src\Dio_Ver.c	   565       /* Check for the validity of group configuration
; ..\mcal_src\Dio_Ver.c	   566          Reports DET error if detected */
; ..\mcal_src\Dio_Ver.c	   567       if(Dio_lCheckChannelId(ApiId,ChannelId) == (uint8)DIO_NO_ERROR)
; ..\mcal_src\Dio_Ver.c	   568       {
; ..\mcal_src\Dio_Ver.c	   569          RetVal = DIO_NO_ERROR;
; ..\mcal_src\Dio_Ver.c	   570       }
; ..\mcal_src\Dio_Ver.c	   571    }
; ..\mcal_src\Dio_Ver.c	   572    return RetVal;
; ..\mcal_src\Dio_Ver.c	   573  }
; ..\mcal_src\Dio_Ver.c	   574  
; ..\mcal_src\Dio_Ver.c	   575  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   576  ** Syntax           : uint8 Dio_lErrorCheckPortDet                            **
; ..\mcal_src\Dio_Ver.c	   577  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   578  **                      Dio_ChannelType PortId,                               **
; ..\mcal_src\Dio_Ver.c	   579  **                      uint8 ApiId                                           **
; ..\mcal_src\Dio_Ver.c	   580  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   581  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   582  ** Service ID       : None                                                    **
; ..\mcal_src\Dio_Ver.c	   583  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   584  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   585  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   586  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio_Ver.c	   587  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   588  ** Parameters (in)  : ApiId - Service ID                                      **
; ..\mcal_src\Dio_Ver.c	   589  **                    PortId - Identifier for the Port                        **
; ..\mcal_src\Dio_Ver.c	   590  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   591  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio_Ver.c	   592  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   593  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio_Ver.c	   594  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   595  ** Description      :                                                         **
; ..\mcal_src\Dio_Ver.c	   596  ** - This function returns the validity of the PortId                         **
; ..\mcal_src\Dio_Ver.c	   597  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   598  uint8 Dio_lErrorCheckPortDet(Dio_PortType PortId, uint8 ApiId)
; ..\mcal_src\Dio_Ver.c	   599  {
; ..\mcal_src\Dio_Ver.c	   600    uint8 RetVal;
; ..\mcal_src\Dio_Ver.c	   601    RetVal = DIO_ERROR;
; ..\mcal_src\Dio_Ver.c	   602  
; ..\mcal_src\Dio_Ver.c	   603    #if(DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio_Ver.c	   604    if(Dio_lcheckDetStatus(ApiId) == (uint8)DIO_NO_ERROR)
; ..\mcal_src\Dio_Ver.c	   605    #endif /* DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio_Ver.c	   606    {
; ..\mcal_src\Dio_Ver.c	   607       /* Check for the validity of group configuration
; ..\mcal_src\Dio_Ver.c	   608          Reports DET error if detected */
; ..\mcal_src\Dio_Ver.c	   609       if(Dio_lCheckPortId(ApiId,PortId) == (uint8)DIO_NO_ERROR)
; ..\mcal_src\Dio_Ver.c	   610       {
; ..\mcal_src\Dio_Ver.c	   611          RetVal = DIO_NO_ERROR;
; ..\mcal_src\Dio_Ver.c	   612       }
; ..\mcal_src\Dio_Ver.c	   613    }
; ..\mcal_src\Dio_Ver.c	   614    return RetVal;
; ..\mcal_src\Dio_Ver.c	   615  }
; ..\mcal_src\Dio_Ver.c	   616  
; ..\mcal_src\Dio_Ver.c	   617  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   618  ** Syntax           : uint8 Dio_lCheckChGrpValue                              **
; ..\mcal_src\Dio_Ver.c	   619  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   620  **                      const Dio_ChannelGroupType *GroupIdPtr                **
; ..\mcal_src\Dio_Ver.c	   621  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   622  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   623  ** Service ID       : None                                                    **
; ..\mcal_src\Dio_Ver.c	   624  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   625  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   626  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   627  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio_Ver.c	   628  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   629  ** Parameters (in)  : ApiId - Service ID                                      **
; ..\mcal_src\Dio_Ver.c	   630  **                    ChGroupId - Identifier for the Channel Group            **
; ..\mcal_src\Dio_Ver.c	   631  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   632  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio_Ver.c	   633  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   634  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio_Ver.c	   635  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   636  ** Description      :                                                         **
; ..\mcal_src\Dio_Ver.c	   637  ** - This function returns the validity of the ChGrpId                        **
; ..\mcal_src\Dio_Ver.c	   638  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   639  uint8 Dio_lCheckChGrpValue(const Dio_ChannelGroupType *GroupIdPtr)
; ..\mcal_src\Dio_Ver.c	   640  {
; ..\mcal_src\Dio_Ver.c	   641  #if((DIO_CONFIG_COUNT == 1U) || (DIO_CH_GRP_SET_U > 1U))
; ..\mcal_src\Dio_Ver.c	   642     uint32 Index;
; ..\mcal_src\Dio_Ver.c	   643  #endif
; ..\mcal_src\Dio_Ver.c	   644     uint8  ErrStatus = DIO_ERROR;
; ..\mcal_src\Dio_Ver.c	   645  
; ..\mcal_src\Dio_Ver.c	   646  #if(DIO_CONFIG_COUNT == 1U)
; ..\mcal_src\Dio_Ver.c	   647     for(Index = 0U;Index < (Dio_kConfigPtr->Dio_ChannelGroupConfigSize);Index++)
; ..\mcal_src\Dio_Ver.c	   648     {
; ..\mcal_src\Dio_Ver.c	   649       /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Dio_Ver.c	   650         due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Dio_Ver.c	   651       if (GroupIdPtr == &(Dio_kConfigPtr->Dio_ChannelGroupConfigPtr[Index]))
; ..\mcal_src\Dio_Ver.c	   652       {
; ..\mcal_src\Dio_Ver.c	   653       /*DIO114: channel group is valid within the current configuration*/
; ..\mcal_src\Dio_Ver.c	   654         ErrStatus = (uint8)DIO_NO_ERROR;
; ..\mcal_src\Dio_Ver.c	   655         break;
; ..\mcal_src\Dio_Ver.c	   656       }
; ..\mcal_src\Dio_Ver.c	   657     }
; ..\mcal_src\Dio_Ver.c	   658  #else
; ..\mcal_src\Dio_Ver.c	   659    #if(DIO_CH_GRP_SET_U == 1U)
; ..\mcal_src\Dio_Ver.c	   660     if (GroupIdPtr == &(Dio_kConfigPtr->Dio_ChGrpIdMap[0U]))
; ..\mcal_src\Dio_Ver.c	   661     {
; ..\mcal_src\Dio_Ver.c	   662     /*DIO114: channel group is valid within the current configuration*/
; ..\mcal_src\Dio_Ver.c	   663       ErrStatus = (uint8)DIO_NO_ERROR;
; ..\mcal_src\Dio_Ver.c	   664     }
; ..\mcal_src\Dio_Ver.c	   665    #elif(DIO_CH_GRP_SET_U > 1U)
; ..\mcal_src\Dio_Ver.c	   666     for(Index = 0U; Index < (DIO_CH_GRP_SET_U); Index++)
; ..\mcal_src\Dio_Ver.c	   667     {
; ..\mcal_src\Dio_Ver.c	   668       if (GroupIdPtr == &(Dio_kConfigPtr->Dio_ChGrpIdMap[Index]))
; ..\mcal_src\Dio_Ver.c	   669       {
; ..\mcal_src\Dio_Ver.c	   670       /*DIO114: channel group is valid within the current configuration*/
; ..\mcal_src\Dio_Ver.c	   671         ErrStatus = (uint8)DIO_NO_ERROR;
; ..\mcal_src\Dio_Ver.c	   672         break;
; ..\mcal_src\Dio_Ver.c	   673       }
; ..\mcal_src\Dio_Ver.c	   674     }
; ..\mcal_src\Dio_Ver.c	   675    #else
; ..\mcal_src\Dio_Ver.c	   676     UNUSED_PARAMETER(GroupIdPtr)
; ..\mcal_src\Dio_Ver.c	   677    #endif /* DIO_CH_GRP_SET_U > 1U */
; ..\mcal_src\Dio_Ver.c	   678  #endif
; ..\mcal_src\Dio_Ver.c	   679  
; ..\mcal_src\Dio_Ver.c	   680     return ErrStatus;
; ..\mcal_src\Dio_Ver.c	   681  }
; ..\mcal_src\Dio_Ver.c	   682  
; ..\mcal_src\Dio_Ver.c	   683  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   684  ** Syntax           : uint8 Dio_lCheckAnalogChannel                           **
; ..\mcal_src\Dio_Ver.c	   685  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   686  **                      uint8 ApiId, uint32 PortReadOnly                      **
; ..\mcal_src\Dio_Ver.c	   687  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   688  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   689  ** Service ID       : None                                                    **
; ..\mcal_src\Dio_Ver.c	   690  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   691  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   692  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   693  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio_Ver.c	   694  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   695  ** Parameters (in)  : ApiId - Service ID                                      **
; ..\mcal_src\Dio_Ver.c	   696  **                    PortReadOnly - Flag to represent Analog PortPins        **
; ..\mcal_src\Dio_Ver.c	   697  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   698  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio_Ver.c	   699  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   700  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio_Ver.c	   701  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   702  ** Description      :                                                         **
; ..\mcal_src\Dio_Ver.c	   703  ** - This function returns status for Read/Write Called on Analog Ports       **
; ..\mcal_src\Dio_Ver.c	   704  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   705  uint8 Dio_lCheckAnalogChannel(uint8 ApiId, uint32 PortReadOnly)
; ..\mcal_src\Dio_Ver.c	   706  {
; ..\mcal_src\Dio_Ver.c	   707     uint8 ErrStatus;
; ..\mcal_src\Dio_Ver.c	   708     ErrStatus = (uint8)DIO_NO_ERROR;
; ..\mcal_src\Dio_Ver.c	   709  
; ..\mcal_src\Dio_Ver.c	   710     #if( DIO_FLIP_CHANNEL_API == STD_ON )
; ..\mcal_src\Dio_Ver.c	   711     if( ( (ApiId == DIO_SID_WRITECHANNEL) ||
; ..\mcal_src\Dio_Ver.c	   712           (ApiId == DIO_SID_FLIPCHANNEL)
; ..\mcal_src\Dio_Ver.c	   713         ) &&  (PortReadOnly != 0U)
; ..\mcal_src\Dio_Ver.c	   714       )
; ..\mcal_src\Dio_Ver.c	   715       {
; ..\mcal_src\Dio_Ver.c	   716         /* Error status = Error has occurred */
; ..\mcal_src\Dio_Ver.c	   717         ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio_Ver.c	   718       }
; ..\mcal_src\Dio_Ver.c	   719     #else
; ..\mcal_src\Dio_Ver.c	   720     if( (ApiId == DIO_SID_WRITECHANNEL) &&  (PortReadOnly != 0U) )
; ..\mcal_src\Dio_Ver.c	   721     {
; ..\mcal_src\Dio_Ver.c	   722       /* Error status = Error has occurred */
; ..\mcal_src\Dio_Ver.c	   723       ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio_Ver.c	   724     }
; ..\mcal_src\Dio_Ver.c	   725     #endif
; ..\mcal_src\Dio_Ver.c	   726     return ErrStatus;
; ..\mcal_src\Dio_Ver.c	   727  }
; ..\mcal_src\Dio_Ver.c	   728  
; ..\mcal_src\Dio_Ver.c	   729  /*******************************************************************************
; ..\mcal_src\Dio_Ver.c	   730  ** Syntax           : uint8 Dio_lCheckAnalogPort                              **
; ..\mcal_src\Dio_Ver.c	   731  **                    (                                                       **
; ..\mcal_src\Dio_Ver.c	   732  **                      uint8 ApiId, uint32 PortReadOnly                      **
; ..\mcal_src\Dio_Ver.c	   733  **                    )                                                       **
; ..\mcal_src\Dio_Ver.c	   734  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   735  ** Service ID       : None                                                    **
; ..\mcal_src\Dio_Ver.c	   736  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   737  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio_Ver.c	   738  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   739  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio_Ver.c	   740  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   741  ** Parameters (in)  : ApiId - Service ID                                      **
; ..\mcal_src\Dio_Ver.c	   742  **                    PortReadOnly - Flag to represent Analog PortPins        **
; ..\mcal_src\Dio_Ver.c	   743  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   744  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio_Ver.c	   745  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   746  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio_Ver.c	   747  **                                                                            **
; ..\mcal_src\Dio_Ver.c	   748  ** Description      :                                                         **
; ..\mcal_src\Dio_Ver.c	   749  ** - This function returns status for Read/Write Called on Analog Ports       **
; ..\mcal_src\Dio_Ver.c	   750  *******************************************************************************/
; ..\mcal_src\Dio_Ver.c	   751  uint8 Dio_lCheckAnalogPort(uint8 ApiId, uint32 PortReadOnly)
; ..\mcal_src\Dio_Ver.c	   752  {
; ..\mcal_src\Dio_Ver.c	   753     uint8 ErrStatus;
; ..\mcal_src\Dio_Ver.c	   754     ErrStatus = (uint8)DIO_NO_ERROR;
; ..\mcal_src\Dio_Ver.c	   755  
; ..\mcal_src\Dio_Ver.c	   756     if( (ApiId == DIO_SID_WRITEPORT) &&  (PortReadOnly != 0U) )
; ..\mcal_src\Dio_Ver.c	   757     {
; ..\mcal_src\Dio_Ver.c	   758       /* Error status = Error has occurred */
; ..\mcal_src\Dio_Ver.c	   759       ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio_Ver.c	   760     }
; ..\mcal_src\Dio_Ver.c	   761     return ErrStatus;
; ..\mcal_src\Dio_Ver.c	   762  }
; ..\mcal_src\Dio_Ver.c	   763  
; ..\mcal_src\Dio_Ver.c	   764  #endif /* DIO_DEV_ERROR_DETECT == STD_ON || DIO_SAFETY_ENABLE == STD_ON*/
; ..\mcal_src\Dio_Ver.c	   765  
; ..\mcal_src\Dio_Ver.c	   766  /*Memory Map of the DIO Code*/
; ..\mcal_src\Dio_Ver.c	   767  #define DIO_STOP_SEC_CODE
; ..\mcal_src\Dio_Ver.c	   768  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Dio_Ver.c	   769  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio_Ver.c	   770    is allowed only for MemMap.h*/
; ..\mcal_src\Dio_Ver.c	   771  #include "MemMap.h"

	; Module end
