	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc2676a -c99 --dep-file=flash\\.Cal.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=flash\\Cal.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o flash\\Cal.src ..\\flash\\Cal.c"
	.compiler_name		"ctc"
	.name	"Cal"

	
$TC16X
	
	.sdecl	'.text.Cal.Cal_CrcInit',code,cluster('Cal_CrcInit')
	.sect	'.text.Cal.Cal_CrcInit'
	.align	2
	
	.global	Cal_CrcInit

; ..\flash\Cal.c	     1  /*============================================================================*/
; ..\flash\Cal.c	     2  /** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
; ..\flash\Cal.c	     3   *  
; ..\flash\Cal.c	     4   *  All rights reserved. This software is iSOFT property. Duplication 
; ..\flash\Cal.c	     5   *  or disclosure without iSOFT written authorization is prohibited.
; ..\flash\Cal.c	     6   *  
; ..\flash\Cal.c	     7   *  @file       <Cal.c>
; ..\flash\Cal.c	     8   *  @brief      <Calculate the Crc >
; ..\flash\Cal.c	     9   *    
; ..\flash\Cal.c	    10   *  <Compiler: CodeWarrior    MCU:9S12G64>
; ..\flash\Cal.c	    11   *
; ..\flash\Cal.c	    12   *  <AUTHOR> Chen>
; ..\flash\Cal.c	    13   *  @date       <2012-12-27>
; ..\flash\Cal.c	    14   */
; ..\flash\Cal.c	    15  /*============================================================================*/
; ..\flash\Cal.c	    16  
; ..\flash\Cal.c	    17  /*=======[R E V I S I O N   H I S T O R Y]====================================*/
; ..\flash\Cal.c	    18  /** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
; ..\flash\Cal.c	    19   *  V1.0    20121227    Gary       Initial version
; ..\flash\Cal.c	    20   */
; ..\flash\Cal.c	    21  /*============================================================================*/
; ..\flash\Cal.c	    22  
; ..\flash\Cal.c	    23  /*=======[I N C L U D E S]====================================================*/
; ..\flash\Cal.c	    24  
; ..\flash\Cal.c	    25  #include "Cal.h"
; ..\flash\Cal.c	    26  
; ..\flash\Cal.c	    27  /*=======[E X T E R N A L   D A T A]==========================================*/
; ..\flash\Cal.c	    28  #if (CAL_CRC32 == CAL_METHOD)
; ..\flash\Cal.c	    29  const uint32 Cal_Crc32Tab[256] =
; ..\flash\Cal.c	    30  {
; ..\flash\Cal.c	    31      0x00000000ul, 0x06233697ul, 0x05C45641ul, 0x03E760D6ul, 0x020A97EDul, 0x0429A17Aul, 0x07CEC1ACul, 0x01EDF73Bul, 
; ..\flash\Cal.c	    32      0x04152FDAul, 0x0236194Dul, 0x01D1799Bul, 0x07F24F0Cul, 0x061FB837ul, 0x003C8EA0ul, 0x03DBEE76ul, 0x05F8D8E1ul, 
; ..\flash\Cal.c	    33      0x01A864DBul, 0x078B524Cul, 0x046C329Aul, 0x024F040Dul, 0x03A2F336ul, 0x0581C5A1ul, 0x0666A577ul, 0x004593E0ul, 
; ..\flash\Cal.c	    34      0x05BD4B01ul, 0x039E7D96ul, 0x00791D40ul, 0x065A2BD7ul, 0x07B7DCECul, 0x0194EA7Bul, 0x02738AADul, 0x0450BC3Aul, 
; ..\flash\Cal.c	    35      0x0350C9B6ul, 0x0573FF21ul, 0x06949FF7ul, 0x00B7A960ul, 0x015A5E5Bul, 0x077968CCul, 0x049E081Aul, 0x02BD3E8Dul, 
; ..\flash\Cal.c	    36      0x0745E66Cul, 0x0166D0FBul, 0x0281B02Dul, 0x04A286BAul, 0x054F7181ul, 0x036C4716ul, 0x008B27C0ul, 0x06A81157ul, 
; ..\flash\Cal.c	    37      0x02F8AD6Dul, 0x04DB9BFAul, 0x073CFB2Cul, 0x011FCDBBul, 0x00F23A80ul, 0x06D10C17ul, 0x05366CC1ul, 0x03155A56ul, 
; ..\flash\Cal.c	    38      0x06ED82B7ul, 0x00CEB420ul, 0x0329D4F6ul, 0x050AE261ul, 0x04E7155Aul, 0x02C423CDul, 0x0123431Bul, 0x0700758Cul, 
; ..\flash\Cal.c	    39      0x06A1936Cul, 0x0082A5FBul, 0x0365C52Dul, 0x0546F3BAul, 0x04AB0481ul, 0x02883216ul, 0x016F52C0ul, 0x074C6457ul, 
; ..\flash\Cal.c	    40      0x02B4BCB6ul, 0x04978A21ul, 0x0770EAF7ul, 0x0153DC60ul, 0x00BE2B5Bul, 0x069D1DCCul, 0x057A7D1Aul, 0x03594B8Dul, 
; ..\flash\Cal.c	    41      0x0709F7B7ul, 0x012AC120ul, 0x02CDA1F6ul, 0x04EE9761ul, 0x0503605Aul, 0x032056CDul, 0x00C7361Bul, 0x06E4008Cul, 
; ..\flash\Cal.c	    42      0x031CD86Dul, 0x053FEEFAul, 0x06D88E2Cul, 0x00FBB8BBul, 0x01164F80ul, 0x07357917ul, 0x04D219C1ul, 0x02F12F56ul, 
; ..\flash\Cal.c	    43      0x05F15ADAul, 0x03D26C4Dul, 0x00350C9Bul, 0x06163A0Cul, 0x07FBCD37ul, 0x01D8FBA0ul, 0x023F9B76ul, 0x041CADE1ul, 
; ..\flash\Cal.c	    44      0x01E47500ul, 0x07C74397ul, 0x04202341ul, 0x020315D6ul, 0x03EEE2EDul, 0x05CDD47Aul, 0x062AB4ACul, 0x0009823Bul, 
; ..\flash\Cal.c	    45      0x04593E01ul, 0x027A0896ul, 0x019D6840ul, 0x07BE5ED7ul, 0x0653A9ECul, 0x00709F7Bul, 0x0397FFADul, 0x05B4C93Aul, 
; ..\flash\Cal.c	    46      0x004C11DBul, 0x066F274Cul, 0x0588479Aul, 0x03AB710Dul, 0x02468636ul, 0x0465B0A1ul, 0x0782D077ul, 0x01A1E6E0ul, 
; ..\flash\Cal.c	    47      0x04C11DB7ul, 0x02E22B20ul, 0x01054BF6ul, 0x07267D61ul, 0x06CB8A5Aul, 0x00E8BCCDul, 0x030FDC1Bul, 0x052CEA8Cul, 
; ..\flash\Cal.c	    48      0x00D4326Dul, 0x06F704FAul, 0x0510642Cul, 0x033352BBul, 0x02DEA580ul, 0x04FD9317ul, 0x071AF3C1ul, 0x0139C556ul, 
; ..\flash\Cal.c	    49      0x0569796Cul, 0x034A4FFBul, 0x00AD2F2Dul, 0x068E19BAul, 0x0763EE81ul, 0x0140D816ul, 0x02A7B8C0ul, 0x04848E57ul, 
; ..\flash\Cal.c	    50      0x017C56B6ul, 0x075F6021ul, 0x04B800F7ul, 0x029B3660ul, 0x0376C15Bul, 0x0555F7CCul, 0x06B2971Aul, 0x0091A18Dul, 
; ..\flash\Cal.c	    51      0x0791D401ul, 0x01B2E296ul, 0x02558240ul, 0x0476B4D7ul, 0x059B43ECul, 0x03B8757Bul, 0x005F15ADul, 0x067C233Aul, 
; ..\flash\Cal.c	    52      0x0384FBDBul, 0x05A7CD4Cul, 0x0640AD9Aul, 0x00639B0Dul, 0x018E6C36ul, 0x07AD5AA1ul, 0x044A3A77ul, 0x02690CE0ul, 
; ..\flash\Cal.c	    53      0x0639B0DAul, 0x001A864Dul, 0x03FDE69Bul, 0x05DED00Cul, 0x04332737ul, 0x021011A0ul, 0x01F77176ul, 0x07D447E1ul, 
; ..\flash\Cal.c	    54      0x022C9F00ul, 0x040FA997ul, 0x07E8C941ul, 0x01CBFFD6ul, 0x002608EDul, 0x06053E7Aul, 0x05E25EACul, 0x03C1683Bul, 
; ..\flash\Cal.c	    55      0x02608EDBul, 0x0443B84Cul, 0x07A4D89Aul, 0x0187EE0Dul, 0x006A1936ul, 0x06492FA1ul, 0x05AE4F77ul, 0x038D79E0ul, 
; ..\flash\Cal.c	    56      0x0675A101ul, 0x00569796ul, 0x03B1F740ul, 0x0592C1D7ul, 0x047F36ECul, 0x025C007Bul, 0x01BB60ADul, 0x0798563Aul, 
; ..\flash\Cal.c	    57      0x03C8EA00ul, 0x05EBDC97ul, 0x060CBC41ul, 0x002F8AD6ul, 0x01C27DEDul, 0x07E14B7Aul, 0x04062BACul, 0x02251D3Bul, 
; ..\flash\Cal.c	    58      0x07DDC5DAul, 0x01FEF34Dul, 0x0219939Bul, 0x043AA50Cul, 0x05D75237ul, 0x03F464A0ul, 0x00130476ul, 0x063032E1ul, 
; ..\flash\Cal.c	    59      0x0130476Dul, 0x071371FAul, 0x04F4112Cul, 0x02D727BBul, 0x033AD080ul, 0x0519E617ul, 0x06FE86C1ul, 0x00DDB056ul, 
; ..\flash\Cal.c	    60      0x052568B7ul, 0x03065E20ul, 0x00E13EF6ul, 0x06C20861ul, 0x072FFF5Aul, 0x010CC9CDul, 0x02EBA91Bul, 0x04C89F8Cul, 
; ..\flash\Cal.c	    61      0x009823B6ul, 0x06BB1521ul, 0x055C75F7ul, 0x037F4360ul, 0x0292B45Bul, 0x04B182CCul, 0x0756E21Aul, 0x0175D48Dul, 
; ..\flash\Cal.c	    62      0x048D0C6Cul, 0x02AE3AFBul, 0x01495A2Dul, 0x076A6CBAul, 0x06879B81ul, 0x00A4AD16ul, 0x0343CDC0ul, 0x0560FB57ul
; ..\flash\Cal.c	    63  
; ..\flash\Cal.c	    64  };
; ..\flash\Cal.c	    65  
; ..\flash\Cal.c	    66  #else
; ..\flash\Cal.c	    67  const uint16 Cal_Crc16Tab[256] = 
; ..\flash\Cal.c	    68  {
; ..\flash\Cal.c	    69      0x0000u, 0x1021u, 0x2042u, 0x3063u, 0x4084u, 0x50a5u, 0x60c6u, 0x70e7u,
; ..\flash\Cal.c	    70      0x8108u, 0x9129u, 0xa14au, 0xb16bu, 0xc18cu, 0xd1adu, 0xe1ceu, 0xf1efu,
; ..\flash\Cal.c	    71      0x1231u, 0x0210u, 0x3273u, 0x2252u, 0x52b5u, 0x4294u, 0x72f7u, 0x62d6u,
; ..\flash\Cal.c	    72      0x9339u, 0x8318u, 0xb37bu, 0xa35au, 0xd3bdu, 0xc39cu, 0xf3ffu, 0xe3deu,
; ..\flash\Cal.c	    73      0x2462u, 0x3443u, 0x0420u, 0x1401u, 0x64e6u, 0x74c7u, 0x44a4u, 0x5485u,
; ..\flash\Cal.c	    74      0xa56au, 0xb54bu, 0x8528u, 0x9509u, 0xe5eeu, 0xf5cfu, 0xc5acu, 0xd58du,
; ..\flash\Cal.c	    75      0x3653u, 0x2672u, 0x1611u, 0x0630u, 0x76d7u, 0x66f6u, 0x5695u, 0x46b4u,
; ..\flash\Cal.c	    76      0xb75bu, 0xa77au, 0x9719u, 0x8738u, 0xf7dfu, 0xe7feu, 0xd79du, 0xc7bcu,
; ..\flash\Cal.c	    77      0x48c4u, 0x58e5u, 0x6886u, 0x78a7u, 0x0840u, 0x1861u, 0x2802u, 0x3823u,
; ..\flash\Cal.c	    78      0xc9ccu, 0xd9edu, 0xe98eu, 0xf9afu, 0x8948u, 0x9969u, 0xa90au, 0xb92bu,
; ..\flash\Cal.c	    79      0x5af5u, 0x4ad4u, 0x7ab7u, 0x6a96u, 0x1a71u, 0x0a50u, 0x3a33u, 0x2a12u,
; ..\flash\Cal.c	    80      0xdbfdu, 0xcbdcu, 0xfbbfu, 0xeb9eu, 0x9b79u, 0x8b58u, 0xbb3bu, 0xab1au,
; ..\flash\Cal.c	    81      0x6ca6u, 0x7c87u, 0x4ce4u, 0x5cc5u, 0x2c22u, 0x3c03u, 0x0c60u, 0x1c41u,
; ..\flash\Cal.c	    82      0xedaeu, 0xfd8fu, 0xcdecu, 0xddcdu, 0xad2au, 0xbd0bu, 0x8d68u, 0x9d49u,
; ..\flash\Cal.c	    83      0x7e97u, 0x6eb6u, 0x5ed5u, 0x4ef4u, 0x3e13u, 0x2e32u, 0x1e51u, 0x0e70u,
; ..\flash\Cal.c	    84      0xff9fu, 0xefbeu, 0xdfddu, 0xcffcu, 0xbf1bu, 0xaf3au, 0x9f59u, 0x8f78u,
; ..\flash\Cal.c	    85      0x9188u, 0x81a9u, 0xb1cau, 0xa1ebu, 0xd10cu, 0xc12du, 0xf14eu, 0xe16fu,
; ..\flash\Cal.c	    86      0x1080u, 0x00a1u, 0x30c2u, 0x20e3u, 0x5004u, 0x4025u, 0x7046u, 0x6067u,
; ..\flash\Cal.c	    87      0x83b9u, 0x9398u, 0xa3fbu, 0xb3dau, 0xc33du, 0xd31cu, 0xe37fu, 0xf35eu,
; ..\flash\Cal.c	    88      0x02b1u, 0x1290u, 0x22f3u, 0x32d2u, 0x4235u, 0x5214u, 0x6277u, 0x7256u,
; ..\flash\Cal.c	    89      0xb5eau, 0xa5cbu, 0x95a8u, 0x8589u, 0xf56eu, 0xe54fu, 0xd52cu, 0xc50du,
; ..\flash\Cal.c	    90      0x34e2u, 0x24c3u, 0x14a0u, 0x0481u, 0x7466u, 0x6447u, 0x5424u, 0x4405u,
; ..\flash\Cal.c	    91      0xa7dbu, 0xb7fau, 0x8799u, 0x97b8u, 0xe75fu, 0xf77eu, 0xc71du, 0xd73cu,
; ..\flash\Cal.c	    92      0x26d3u, 0x36f2u, 0x0691u, 0x16b0u, 0x6657u, 0x7676u, 0x4615u, 0x5634u,
; ..\flash\Cal.c	    93      0xd94cu, 0xc96du, 0xf90eu, 0xe92fu, 0x99c8u, 0x89e9u, 0xb98au, 0xa9abu,
; ..\flash\Cal.c	    94      0x5844u, 0x4865u, 0x7806u, 0x6827u, 0x18c0u, 0x08e1u, 0x3882u, 0x28a3u,
; ..\flash\Cal.c	    95      0xcb7du, 0xdb5cu, 0xeb3fu, 0xfb1eu, 0x8bf9u, 0x9bd8u, 0xabbbu, 0xbb9au,
; ..\flash\Cal.c	    96      0x4a75u, 0x5a54u, 0x6a37u, 0x7a16u, 0x0af1u, 0x1ad0u, 0x2ab3u, 0x3a92u,
; ..\flash\Cal.c	    97      0xfd2eu, 0xed0fu, 0xdd6cu, 0xcd4du, 0xbdaau, 0xad8bu, 0x9de8u, 0x8dc9u,
; ..\flash\Cal.c	    98      0x7c26u, 0x6c07u, 0x5c64u, 0x4c45u, 0x3ca2u, 0x2c83u, 0x1ce0u, 0x0cc1u,
; ..\flash\Cal.c	    99      0xef1fu, 0xff3eu, 0xcf5du, 0xdf7cu, 0xaf9bu, 0xbfbau, 0x8fd9u, 0x9ff8u,
; ..\flash\Cal.c	   100      0x6e17u, 0x7e36u, 0x4e55u, 0x5e74u, 0x2e93u, 0x3eb2u, 0x0ed1u, 0x1ef0u
; ..\flash\Cal.c	   101  };
; ..\flash\Cal.c	   102  #endif
; ..\flash\Cal.c	   103  
; ..\flash\Cal.c	   104  /*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
; ..\flash\Cal.c	   105  
; ..\flash\Cal.c	   106  /******************************************************************************/
; ..\flash\Cal.c	   107  /**
; ..\flash\Cal.c	   108   * @brief               <CRC32 initialize>
; ..\flash\Cal.c	   109   * 
; ..\flash\Cal.c	   110   * <This Funtion Initializes the CRC algorithm> .
; ..\flash\Cal.c	   111   * Service ID   :       <NONE>
; ..\flash\Cal.c	   112   * Sync/Async   :       <Synchronous>
; ..\flash\Cal.c	   113   * Reentrancy           <Reentrant>
; ..\flash\Cal.c	   114   * @param[in]           <NONE>
; ..\flash\Cal.c	   115   * @param[out]          <NONE>
; ..\flash\Cal.c	   116   * @param[in/out]       <curCrc (IN/OUT)>
; ..\flash\Cal.c	   117   * @return              <NONE>    
; ..\flash\Cal.c	   118   */
; ..\flash\Cal.c	   119  /******************************************************************************/
; ..\flash\Cal.c	   120  void Cal_CrcInit(SecM_CRCType *curCrc)
; Function Cal_CrcInit
.L5:
Cal_CrcInit:	.type	func

; ..\flash\Cal.c	   121  {
; ..\flash\Cal.c	   122  #if (CAL_CRC32 == CAL_METHOD)
; ..\flash\Cal.c	   123      *curCrc = 0xFFFFFFFFuL;
; ..\flash\Cal.c	   124  #else
; ..\flash\Cal.c	   125      /* CRC16 */
; ..\flash\Cal.c	   126      *curCrc = 0xFFFFu;
	mov.u	d15,#65535
	st.h	[a4],d15
.L53:

; ..\flash\Cal.c	   127  #endif 
; ..\flash\Cal.c	   128      return;
; ..\flash\Cal.c	   129  }
	ret
.L31:
	
__Cal_CrcInit_function_end:
	.size	Cal_CrcInit,__Cal_CrcInit_function_end-Cal_CrcInit
.L18:
	; End of function
	
	.sdecl	'.text.Cal.Cal_CrcCal',code,cluster('Cal_CrcCal')
	.sect	'.text.Cal.Cal_CrcCal'
	.align	2
	
	.global	Cal_CrcCal

; ..\flash\Cal.c	   130  
; ..\flash\Cal.c	   131  /******************************************************************************/
; ..\flash\Cal.c	   132  /**
; ..\flash\Cal.c	   133   * @brief               <CRC32 compute>
; ..\flash\Cal.c	   134   * 
; ..\flash\Cal.c	   135   * <This Funtion computes the CRC value> .
; ..\flash\Cal.c	   136   * Service ID   :       <NONE>
; ..\flash\Cal.c	   137   * Sync/Async   :       <Synchronous>
; ..\flash\Cal.c	   138   * Reentrancy           <Reentrant>
; ..\flash\Cal.c	   139   * @param[in]           <buf (IN), size (IN)>
; ..\flash\Cal.c	   140   * @param[out]          <NONE>
; ..\flash\Cal.c	   141   * @param[in/out]       <curCrc (IN/OUT)>
; ..\flash\Cal.c	   142   * @return              <NONE>    
; ..\flash\Cal.c	   143   */
; ..\flash\Cal.c	   144  /******************************************************************************/
; ..\flash\Cal.c	   145  void Cal_CrcCal(SecM_CRCType *curCrc,
; Function Cal_CrcCal
.L7:
Cal_CrcCal:	.type	func

; ..\flash\Cal.c	   146      const uint8 *buf,
; ..\flash\Cal.c	   147      const uint32 size)
; ..\flash\Cal.c	   148  {
; ..\flash\Cal.c	   149      uint32 i;
; ..\flash\Cal.c	   150      
; ..\flash\Cal.c	   151  #if (CAL_CRC32 == CAL_METHOD)
; ..\flash\Cal.c	   152      for (i = 0; i < size; i++)
; ..\flash\Cal.c	   153      {
; ..\flash\Cal.c	   154  
; ..\flash\Cal.c	   155          *curCrc = Cal_Crc32Tab[(*curCrc ^ (uint32)buf[i]) & 0xffu]
; ..\flash\Cal.c	   156              ^ (*curCrc >> 8);
; ..\flash\Cal.c	   157      }
; ..\flash\Cal.c	   158  
; ..\flash\Cal.c	   159  #else
; ..\flash\Cal.c	   160      /* CRC16 */
; ..\flash\Cal.c	   161      for (i = 0; i < size; i++)
	mov	d15,#0
	j	.L2
.L3:

; ..\flash\Cal.c	   162      {
; ..\flash\Cal.c	   163          *curCrc = Cal_Crc16Tab[(*curCrc >> 8 ^ (uint16)buf[i]) & 0xffu] 
; ..\flash\Cal.c	   164              ^ (*curCrc << 8);
	ld.hu	d1,[a4]0
.L58:
	ld.bu	d2,[a5+]
.L59:
	sha	d0,d1,#-8
	movh.a	a15,#@his(Cal_Crc16Tab)
.L60:
	xor	d0,d2
	lea	a15,[a15]@los(Cal_Crc16Tab)
.L61:
	addsc.a	a15,a15,d0,#1
.L62:
	sha	d1,d1,#8
.L63:
	ld.hu	d0,[a15]0
.L64:
	add	d15,#1
.L65:
	xor	d0,d1
	st.h	[a4],d0
.L2:
	jlt.u	d15,d4,.L3
.L66:

; ..\flash\Cal.c	   165      }    
; ..\flash\Cal.c	   166  #endif
; ..\flash\Cal.c	   167   
; ..\flash\Cal.c	   168      return;
; ..\flash\Cal.c	   169  }
	ret
.L34:
	
__Cal_CrcCal_function_end:
	.size	Cal_CrcCal,__Cal_CrcCal_function_end-Cal_CrcCal
.L23:
	; End of function
	
	.sdecl	'.text.Cal.Cal_CrcFinalize',code,cluster('Cal_CrcFinalize')
	.sect	'.text.Cal.Cal_CrcFinalize'
	.align	2
	
	.global	Cal_CrcFinalize

; ..\flash\Cal.c	   170  
; ..\flash\Cal.c	   171  
; ..\flash\Cal.c	   172  /******************************************************************************/
; ..\flash\Cal.c	   173  /**
; ..\flash\Cal.c	   174   * @brief               <CRC32 finish>
; ..\flash\Cal.c	   175   * 
; ..\flash\Cal.c	   176   * <This Funtion finish the CRC compute.> .
; ..\flash\Cal.c	   177   * Service ID   :       <NONE>
; ..\flash\Cal.c	   178   * Sync/Async   :       <Synchronous>
; ..\flash\Cal.c	   179   * Reentrancy           <Reentrant>
; ..\flash\Cal.c	   180   * @param[in]           <NONE>
; ..\flash\Cal.c	   181   * @param[out]          <NONE>
; ..\flash\Cal.c	   182   * @param[in/out]       <curCrc (IN/OUT)>
; ..\flash\Cal.c	   183   * @return              <NONE>    
; ..\flash\Cal.c	   184   */
; ..\flash\Cal.c	   185  /******************************************************************************/
; ..\flash\Cal.c	   186  void Cal_CrcFinalize(SecM_CRCType *curCrc)
; Function Cal_CrcFinalize
.L9:
Cal_CrcFinalize:	.type	func

; ..\flash\Cal.c	   187  {
; ..\flash\Cal.c	   188  #if (CAL_CRC32 == CAL_METHOD)
; ..\flash\Cal.c	   189      *curCrc ^= 0xFFFFFFFFuL;
; ..\flash\Cal.c	   190  #endif 
; ..\flash\Cal.c	   191      
; ..\flash\Cal.c	   192      return;
; ..\flash\Cal.c	   193  }
	ret
.L42:
	
__Cal_CrcFinalize_function_end:
	.size	Cal_CrcFinalize,__Cal_CrcFinalize_function_end-Cal_CrcFinalize
.L28:
	; End of function
	
	.sdecl	'.rodata.Cal.Cal_Crc16Tab',data,rom,cluster('Cal_Crc16Tab')
	.sect	'.rodata.Cal.Cal_Crc16Tab'
	.global	Cal_Crc16Tab
	.align	4
Cal_Crc16Tab:	.type	object
	.size	Cal_Crc16Tab,512
	.space	2
	.half	4129,8258,12387,16516,20645,24774,28903,33032
	.half	37161,41290,45419,49548,53677,57806,61935,4657
	.half	528,12915,8786,21173,17044,29431,25302,37689
	.half	33560,45947,41818,54205,50076,62463,58334,9314
	.half	13379,1056,5121,25830,29895,17572,21637,42346
	.half	46411,34088,38153,58862,62927,50604,54669,13907
	.half	9842,5649,1584,30423,26358,22165,18100,46939
	.half	42874,38681,34616,63455,59390,55197,51132,18628
	.half	22757,26758,30887,2112,6241,10242,14371,51660
	.half	55789,59790,63919,35144,39273,43274,47403,23285
	.half	19156,31415,27286,6769,2640,14899,10770,56317
	.half	52188,64447,60318,39801,35672,47931,43802,27814
	.half	31879,19684,23749,11298,15363,3168,7233,60846
	.half	64911,52716,56781,44330,48395,36200,40265,32407
	.half	28342,24277,20212,15891,11826,7761,3696,65439
	.half	61374,57309,53244,48923,44858,40793,36728,37256
	.half	33193,45514,41451,53516,49453,61774,57711,4224
	.half	161,12482,8419,20484,16421,28742,24679,33721
	.half	37784,41979,46042,49981,54044,58239,62302,689
	.half	4752,8947,13010,16949,21012,25207,29270,46570
	.half	42443,38312,34185,62830,58703,54572,50445,13538
	.half	9411,5280,1153,29798,25671,21540,17413,42971
	.half	47098,34713,38840,59231,63358,50973,55100,9939
	.half	14066,1681,5808,26199,30326,17941,22068,55628
	.half	51565,63758,59695,39368,35305,47498,43435,22596
	.half	18533,30726,26663,6336,2273,14466,10403,52093
	.half	56156,60223,64286,35833,39896,43963,48026,19061
	.half	23124,27191,31254,2801,6864,10931,14994,64814
	.half	60687,56684,52557,48554,44427,40424,36297,31782
	.half	27655,23652,19525,15522,11395,7392,3265,61215
	.half	65342,53085,57212,44955,49082,36825,40952,28183
	.half	32310,20053,24180,11923
	.half	16050,3793,7920
	.calls	'Cal_CrcInit','',0
	.calls	'Cal_CrcCal','',0
	.calls	'Cal_CrcFinalize','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L11:
	.word	1222
	.half	3
	.word	.L12
	.byte	4
.L10:
	.byte	1
	.byte	'..\\flash\\Cal.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L13
	.byte	2
	.byte	'unsigned short int',0,2,7
.L32:
	.byte	3
	.word	169
	.byte	2
	.byte	'unsigned char',0,1,8,4
	.word	196
.L36:
	.byte	3
	.word	213
.L40:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L38:
	.byte	4
	.word	223
	.byte	5
	.byte	'void',0,3
	.word	249
	.byte	6
	.byte	'__prof_adm',0,1,1,1
	.word	255
	.byte	7,1,3
	.word	279
	.byte	6
	.byte	'__codeptr',0,1,1,1
	.word	281
	.byte	6
	.byte	'uint8',0,2,90,29
	.word	196
	.byte	6
	.byte	'uint16',0,2,92,29
	.word	169
	.byte	6
	.byte	'uint32',0,2,94,29
	.word	223
	.byte	6
	.byte	'boolean',0,2,105,29
	.word	196
	.byte	8,3,52,9,164,1,9,164,1
	.word	196
	.byte	10,163,1,0,11
	.byte	'datas',0,164,1
	.word	370
	.byte	2,35,0,12,3,55,5,164,1,11
	.byte	'certFmt',0,1
	.word	196
	.byte	2,35,0,9,8
	.word	196
	.byte	10,7,0,11
	.byte	'pModNum',0,8
	.word	420
	.byte	2,35,1,9,16
	.word	196
	.byte	10,15,0,11
	.byte	'customPars',0,16
	.word	446
	.byte	2,35,9,9,3
	.word	196
	.byte	10,2,0,11
	.byte	'certFailDate',0,3
	.word	475
	.byte	2,35,25,9,4
	.word	196
	.byte	10,3,0,11
	.byte	'certSequenceNum',0,4
	.word	506
	.byte	2,35,28,11
	.byte	'signAlgoFlg',0,1
	.word	196
	.byte	2,35,32,11
	.byte	'pubKeyCurPar',0,1
	.word	196
	.byte	2,35,33,11
	.byte	'hashAlgoFlg',0,1
	.word	196
	.byte	2,35,34,11
	.byte	'pubKeyIdx',0,1
	.word	196
	.byte	2,35,35,9,64
	.word	196
	.byte	10,63,0,11
	.byte	'certPubKey',0,64
	.word	623
	.byte	2,35,36,11
	.byte	'certSigner',0,64
	.word	623
	.byte	2,35,100,0,11
	.byte	'parameters',0,164,1
	.word	397
	.byte	2,35,0,0,6
	.byte	'Secure_SignerInfoType',0,3,68,3
	.word	364
	.byte	6
	.byte	'_iob_flag_t',0,4,75,25
	.word	169
	.byte	13,5,72,9,1,14
	.byte	'INTERNAL_FLS',0,0,14
	.byte	'EXTERNAL_FLS',0,1,0,6
	.byte	'FL_FlashType',0,5,76,2
	.word	745
	.byte	13,5,78,9,1,14
	.byte	'NO_CRC',0,0,14
	.byte	'LAST_ADDR',0,1,14
	.byte	'HEAD_ADDR',0,2,0,6
	.byte	'FL_CrcAddrType',0,5,83,2
	.word	802
	.byte	12,5,108,9,8,11
	.byte	'address',0,4
	.word	223
	.byte	2,35,0,11
	.byte	'length',0,4
	.word	223
	.byte	2,35,4,0,6
	.byte	'FL_SegmentInfoType',0,5,116,3
	.word	864
	.byte	12,5,119,9,164,6,11
	.byte	'nrOfSegments',0,2
	.word	169
	.byte	2,35,0,11
	.byte	'blockChecked',0,1
	.word	196
	.byte	2,35,2,9,160,6
	.word	864
	.byte	10,99,0,11
	.byte	'segmentInfo',0,160,6
	.word	980
	.byte	2,35,4,0,6
	.byte	'FL_SegmentListType',0,5,126,3
	.word	930
	.byte	12,5,129,1,9,20,11
	.byte	'blkValid',0,1
	.word	196
	.byte	2,35,0,11
	.byte	'blkProgAttempt',0,2
	.word	169
	.byte	2,35,2,11
	.byte	'blkChecksum',0,4
	.word	223
	.byte	2,35,4,9,9
	.word	196
	.byte	10,8,0,11
	.byte	'fingerPrint',0,9
	.word	1109
	.byte	2,35,8,0,6
	.byte	'FL_blockInfoType',0,5,139,1,3
	.word	1040
	.byte	6
	.byte	'SecM_WordType',0,6,68,16
	.word	223
	.byte	6
	.byte	'SecM_CRCType',0,6,80,16
	.word	169
	.byte	9,128,4
	.word	169
	.byte	10,255,1,0
.L44:
	.byte	4
	.word	1209
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L12:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,15,0,73,19,0,0,4,38,0,73,19
	.byte	0,0,5,59,0,3,8,0,0,6,22,0,3,8,58,15,59,15,57,15,73,19,0,0,7,21,0,54,15,0,0,8,23,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,1,1,11,15,73,19,0,0,10,33,0,47,15,0,0,11,13,0,3,8,11,15,73,19,56,9,0,0,12,19,1,58,15,59,15,57
	.byte	15,11,15,0,0,13,4,1,58,15,59,15,57,15,11,15,0,0,14,40,0,3,8,28,13,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L13:
	.word	.L46-.L45
.L45:
	.half	3
	.word	.L48-.L47
.L47:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0,0
	.byte	'..\\flash\\Cal.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Secure_Types.h',0,2,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'..\\flash\\FL.h',0,0,0,0
	.byte	'..\\flash\\SecM.h',0,0,0,0,0
.L48:
.L46:
	.sdecl	'.debug_info',debug,cluster('Cal_CrcInit')
	.sect	'.debug_info'
.L14:
	.word	235
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\flash\\Cal.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L17,.L16
	.byte	2
	.word	.L10
	.byte	3
	.byte	'Cal_CrcInit',0,1,120,6,1,1,1
	.word	.L5,.L31,.L4
	.byte	4
	.byte	'curCrc',0,1,120,32
	.word	.L32,.L33
	.byte	5
	.word	.L5,.L31
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Cal_CrcInit')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Cal_CrcInit')
	.sect	'.debug_line'
.L16:
	.word	.L50-.L49
.L49:
	.half	3
	.word	.L52-.L51
.L51:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\Cal.c',0,0,0,0,0
.L52:
	.byte	5,15,7,0,5,2
	.word	.L5
	.byte	3,253,0,1,5,13,1,5,1,9
	.half	.L53-.L5
	.byte	3,3,1,7,9
	.half	.L18-.L53
	.byte	0,1,1
.L50:
	.sdecl	'.debug_ranges',debug,cluster('Cal_CrcInit')
	.sect	'.debug_ranges'
.L17:
	.word	-1,.L5,0,.L18-.L5,0,0
	.sdecl	'.debug_info',debug,cluster('Cal_CrcCal')
	.sect	'.debug_info'
.L19:
	.word	287
	.half	3
	.word	.L20
	.byte	4,1
	.byte	'..\\flash\\Cal.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L22,.L21
	.byte	2
	.word	.L10
	.byte	3
	.byte	'Cal_CrcCal',0,1,145,1,6,1,1,1
	.word	.L7,.L34,.L6
	.byte	4
	.byte	'curCrc',0,1,145,1,31
	.word	.L32,.L35
	.byte	4
	.byte	'buf',0,1,146,1,18
	.word	.L36,.L37
	.byte	4
	.byte	'size',0,1,147,1,18
	.word	.L38,.L39
	.byte	5
	.word	.L7,.L34
	.byte	6
	.byte	'i',0,1,149,1,12
	.word	.L40,.L41
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Cal_CrcCal')
	.sect	'.debug_abbrev'
.L20:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Cal_CrcCal')
	.sect	'.debug_line'
.L21:
	.word	.L55-.L54
.L54:
	.half	3
	.word	.L57-.L56
.L56:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\Cal.c',0,0,0,0,0
.L57:
	.byte	5,12,7,0,5,2
	.word	.L7
	.byte	3,160,1,1,5,25,1,5,16,9
	.half	.L3-.L7
	.byte	3,3,1,5,59,9
	.half	.L58-.L3
	.byte	3,127,1,5,41,9
	.half	.L59-.L58
	.byte	1,5,19,1,5,46,9
	.half	.L60-.L59
	.byte	1,5,19,1,5,31,9
	.half	.L61-.L60
	.byte	1,5,24,9
	.half	.L62-.L61
	.byte	3,1,1,5,31,9
	.half	.L63-.L62
	.byte	3,127,1,5,28,9
	.half	.L64-.L63
	.byte	3,126,1,5,17,9
	.half	.L65-.L64
	.byte	3,2,1,5,25,9
	.half	.L2-.L65
	.byte	3,126,1,5,1,7,9
	.half	.L66-.L2
	.byte	3,8,1,7,9
	.half	.L23-.L66
	.byte	0,1,1
.L55:
	.sdecl	'.debug_ranges',debug,cluster('Cal_CrcCal')
	.sect	'.debug_ranges'
.L22:
	.word	-1,.L7,0,.L23-.L7,0,0
	.sdecl	'.debug_info',debug,cluster('Cal_CrcFinalize')
	.sect	'.debug_info'
.L24:
	.word	241
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'..\\flash\\Cal.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L27,.L26
	.byte	2
	.word	.L10
	.byte	3
	.byte	'Cal_CrcFinalize',0,1,186,1,6,1,1,1
	.word	.L9,.L42,.L8
	.byte	4
	.byte	'curCrc',0,1,186,1,36
	.word	.L32,.L43
	.byte	5
	.word	.L9,.L42
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Cal_CrcFinalize')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Cal_CrcFinalize')
	.sect	'.debug_line'
.L26:
	.word	.L68-.L67
.L67:
	.half	3
	.word	.L70-.L69
.L69:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\flash\\Cal.c',0,0,0,0,0
.L70:
	.byte	5,1,7,0,5,2
	.word	.L9
	.byte	3,192,1,1,7,9
	.half	.L28-.L9
	.byte	0,1,1
.L68:
	.sdecl	'.debug_ranges',debug,cluster('Cal_CrcFinalize')
	.sect	'.debug_ranges'
.L27:
	.word	-1,.L9,0,.L28-.L9,0,0
	.sdecl	'.debug_info',debug,cluster('Cal_Crc16Tab')
	.sect	'.debug_info'
.L29:
	.word	195
	.half	3
	.word	.L30
	.byte	4,1
	.byte	'..\\flash\\Cal.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L10
	.byte	3
	.byte	'Cal_Crc16Tab',0,1,67,14
	.word	.L44
	.byte	1,5,3
	.word	Cal_Crc16Tab
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Cal_Crc16Tab')
	.sect	'.debug_abbrev'
.L30:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('Cal_CrcCal')
	.sect	'.debug_loc'
.L6:
	.word	-1,.L7,0,.L34-.L7
	.half	2
	.byte	138,0
	.word	0,0
.L37:
	.word	-1,.L7,0,.L34-.L7
	.half	1
	.byte	101
	.word	0,0
.L35:
	.word	-1,.L7,0,.L34-.L7
	.half	1
	.byte	100
	.word	0,0
.L41:
	.word	-1,.L7,.L3-.L7,.L34-.L7
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L39:
	.word	-1,.L7,0,.L34-.L7
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Cal_CrcFinalize')
	.sect	'.debug_loc'
.L8:
	.word	-1,.L9,0,.L42-.L9
	.half	2
	.byte	138,0
	.word	0,0
.L43:
	.word	-1,.L9,0,.L42-.L9
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Cal_CrcInit')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L31-.L5
	.half	2
	.byte	138,0
	.word	0,0
.L33:
	.word	-1,.L5,0,.L31-.L5
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L71:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Cal_CrcInit')
	.sect	'.debug_frame'
	.word	24
	.word	.L71,.L5,.L31-.L5
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Cal_CrcCal')
	.sect	'.debug_frame'
	.word	20
	.word	.L71,.L7,.L34-.L7
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Cal_CrcFinalize')
	.sect	'.debug_frame'
	.word	24
	.word	.L71,.L9,.L42-.L9
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\flash\Cal.c	   194  
; ..\flash\Cal.c	   195  /*=======[E N D   O F   F I L E]==============================================*/
; ..\flash\Cal.c	   196  

	; Module end
