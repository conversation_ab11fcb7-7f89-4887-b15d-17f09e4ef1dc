	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc13852a -c99 --dep-file=vss_code\\.vsskeym.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=vss_code\\vsskeym.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o vss_code\\vsskeym.src ..\\vss_code\\vsskeym.c"
	.compiler_name		"ctc"
	.name	"vsskeym"

	
$TC16X
	
	.sdecl	'.text.vss_api_code',code,cluster('getEnvIndex')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	getEnvIndex

; ..\vss_code\vsskeym.c	     1  #include "errid.h"
; ..\vss_code\vsskeym.c	     2  #include "vsskeym.h"
; ..\vss_code\vsskeym.c	     3  #include "vsstype.h"
; ..\vss_code\vsskeym.c	     4  #include "vssconf.h"
; ..\vss_code\vsskeym.c	     5  #include "vssapi.h"
; ..\vss_code\vsskeym.c	     6  #include "vsscommon.h"
; ..\vss_code\vsskeym.c	     7  #include "cert.h"
; ..\vss_code\vsskeym.c	     8  
; ..\vss_code\vsskeym.c	     9  extern flash_io_cb *g_vssIocb;
; ..\vss_code\vsskeym.c	    10  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	    11  extern vss_uint8 g_vssAsymmKey[ASYMM_KEY_LEN];
; ..\vss_code\vsskeym.c	    12  extern vss_uint8 g_vssRootCert[ROOT_CERT_SIZE];
; ..\vss_code\vsskeym.c	    13  extern vss_uint8 g_vssUserCert[USER_CERT_SIZE];
; ..\vss_code\vsskeym.c	    14  extern vss_uint8 g_vssSymmKey[SYMM_KEY_NUM][SYMM_CIPHER_LEN];
; ..\vss_code\vsskeym.c	    15  extern vss_uint8 g_vssSessKey[SESS_KEY_NUM][SYMM_CIPHER_LEN];
; ..\vss_code\vsskeym.c	    16  #endif
; ..\vss_code\vsskeym.c	    17  extern vss_uint8 g_cVssAlg;
; ..\vss_code\vsskeym.c	    18  extern vss_uint8 g_cVssEnv;
; ..\vss_code\vsskeym.c	    19  extern const vss_uint8 vsskeym_KEK[16];
; ..\vss_code\vsskeym.c	    20  extern const vss_uint8 vsskeym_SECOC[16];
; ..\vss_code\vsskeym.c	    21  extern const vss_uint8 vss_ZERO[32];
; ..\vss_code\vsskeym.c	    22  
; ..\vss_code\vsskeym.c	    23  
; ..\vss_code\vsskeym.c	    24  #pragma section code "vss_api_code" 
; ..\vss_code\vsskeym.c	    25  
; ..\vss_code\vsskeym.c	    26  enum{
; ..\vss_code\vsskeym.c	    27  	KEY_FLAG_USED = 1,
; ..\vss_code\vsskeym.c	    28  	KEY_FLAG_EXPORT = 2,
; ..\vss_code\vsskeym.c	    29  	KEY_FLAG_VALID = 4,
; ..\vss_code\vsskeym.c	    30  	KEY_FLAG_CHIP = 8,
; ..\vss_code\vsskeym.c	    31  };
; ..\vss_code\vsskeym.c	    32  
; ..\vss_code\vsskeym.c	    33  #define FLASH_IO_RETRY_COUNT	3
; ..\vss_code\vsskeym.c	    34  
; ..\vss_code\vsskeym.c	    35  typedef struct
; ..\vss_code\vsskeym.c	    36  {
; ..\vss_code\vsskeym.c	    37  	/* 8 bytes key attribute */
; ..\vss_code\vsskeym.c	    38  	vss_uint8 cFlag;
; ..\vss_code\vsskeym.c	    39  	vss_uint8 cKeyLen;
; ..\vss_code\vsskeym.c	    40  	vss_uint8 cIndex;
; ..\vss_code\vsskeym.c	    41  	vss_uint8 cAlg;
; ..\vss_code\vsskeym.c	    42  	vss_uint8 cv[3];
; ..\vss_code\vsskeym.c	    43  	vss_uint8 cRFU;   
; ..\vss_code\vsskeym.c	    44  	/* 24 bytes key value */
; ..\vss_code\vsskeym.c	    45  	vss_uint8 value[SYMM_KEY_LEN];
; ..\vss_code\vsskeym.c	    46  } TSymmKeyItem;
; ..\vss_code\vsskeym.c	    47  
; ..\vss_code\vsskeym.c	    48  typedef struct
; ..\vss_code\vsskeym.c	    49  {
; ..\vss_code\vsskeym.c	    50  	/* 8 bytes key attribute */
; ..\vss_code\vsskeym.c	    51  	vss_uint8 cUseFlag;
; ..\vss_code\vsskeym.c	    52  	vss_uint8 cIndex;
; ..\vss_code\vsskeym.c	    53  	vss_uint8 cAlg; /*1-ECC; 2-SM2*/
; ..\vss_code\vsskeym.c	    54  	vss_uint8 cXLen;
; ..\vss_code\vsskeym.c	    55  	vss_uint8 cYLen;
; ..\vss_code\vsskeym.c	    56  	vss_uint8 cSKLen;
; ..\vss_code\vsskeym.c	    57  	vss_uint8 cRFU7;
; ..\vss_code\vsskeym.c	    58  	vss_uint8 cRFU8;
; ..\vss_code\vsskeym.c	    59  	/* 96 bytes key value */
; ..\vss_code\vsskeym.c	    60  	vss_uint8 value[ASYMM_KEY_LEN];
; ..\vss_code\vsskeym.c	    61  	/* 8 bytes key mac */
; ..\vss_code\vsskeym.c	    62  	vss_uint8 mac[ASYMM_KEY_MAC];
; ..\vss_code\vsskeym.c	    63  } TAsymmKeyItem;
; ..\vss_code\vsskeym.c	    64  
; ..\vss_code\vsskeym.c	    65  vss_uint32 getEnvIndex(vss_uint32 certType)
; Function getEnvIndex
.L317:
getEnvIndex:	.type	func

; ..\vss_code\vsskeym.c	    66  {
; ..\vss_code\vsskeym.c	    67  	if(certType == CERT_TYPE_USR)
	jne	d4,#1,.L2
.L1567:

; ..\vss_code\vsskeym.c	    68  		return SYS_CERT_NUM;
; ..\vss_code\vsskeym.c	    69  	if(g_cVssAlg == ALG_GJ && g_cVssEnv == ENV_QA)
; ..\vss_code\vsskeym.c	    70  		return 0;
; ..\vss_code\vsskeym.c	    71  
; ..\vss_code\vsskeym.c	    72  	if(g_cVssAlg == ALG_GM && g_cVssEnv == ENV_QA)
; ..\vss_code\vsskeym.c	    73  		return 1;
; ..\vss_code\vsskeym.c	    74  
; ..\vss_code\vsskeym.c	    75  	if(g_cVssAlg == ALG_GJ && g_cVssEnv == ENV_PP)
; ..\vss_code\vsskeym.c	    76  		return 0;
; ..\vss_code\vsskeym.c	    77  
; ..\vss_code\vsskeym.c	    78  	if(g_cVssAlg == ALG_GM && g_cVssEnv == ENV_PP)
; ..\vss_code\vsskeym.c	    79  		return 1;
; ..\vss_code\vsskeym.c	    80  
; ..\vss_code\vsskeym.c	    81  	if(g_cVssAlg == ALG_GJ && g_cVssEnv == ENV_P)
; ..\vss_code\vsskeym.c	    82  		return 2;
; ..\vss_code\vsskeym.c	    83  
; ..\vss_code\vsskeym.c	    84  	if(g_cVssAlg == ALG_GM && g_cVssEnv == ENV_P)
; ..\vss_code\vsskeym.c	    85  		return 3;
; ..\vss_code\vsskeym.c	    86  	return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	    87  }
	mov	d2,#6
	ret
.L2:
	fcall	.cocofun_12
.L836:
	jne	d15,#1,.L4
.L1568:
	fcall	.cocofun_2
.L1569:
	jeq	d0,#1,.L5
.L4:
	jne	d15,#2,.L6
.L1570:
	fcall	.cocofun_2
.L1571:
	jeq	d0,#1,.L7
.L6:
	jne	d15,#1,.L8
.L1572:
	fcall	.cocofun_2
.L1573:
	jne	d0,#2,.L9
.L5:
	mov	d2,#0
	ret
.L9:
.L8:
	jne	d15,#2,.L11
.L1574:
	fcall	.cocofun_2
.L1575:
	jne	d0,#2,.L12
.L7:
	mov	d2,#1
	ret
.L12:
.L11:
	jne	d15,#1,.L14
.L1576:
	fcall	.cocofun_2
.L1577:
	jne	d0,#3,.L15
.L1578:
	mov	d2,#2
	ret
.L15:
.L14:
	jne	d15,#2,.L17
.L1579:
	movh.a	a15,#@his(g_cVssEnv)
	ld.bu	d15,[a15]@los(g_cVssEnv)
.L1580:
	jne	d15,#3,.L18
.L1581:
	mov	d2,#3
	ret
.L18:
.L17:
	mov	d2,#27
	ret
.L815:
	
__getEnvIndex_function_end:
	.size	getEnvIndex,__getEnvIndex_function_end-getEnvIndex
.L541:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_12')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_12
.L319:
.cocofun_12:	.type	func
; Function body .cocofun_12, coco_iter:0
	movh.a	a15,#@his(g_cVssAlg)
.L1041:
	ld.bu	d15,[a15]@los(g_cVssAlg)
.L1709:
	fret
.L611:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_2')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_2
.L321:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	movh.a	a15,#@his(g_cVssEnv)
	ld.bu	d0,[a15]@los(g_cVssEnv)
.L1656:
	fret
.L561:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('LoadCert')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	LoadCert

; ..\vss_code\vsskeym.c	    88  
; ..\vss_code\vsskeym.c	    89  vss_uint32 LoadCert(vss_uint32 certType, vss_uint32* certlen, vss_uint8* certData)
; Function LoadCert
.L323:
LoadCert:	.type	func
	mov	d15,d4
	mov.aa	a12,a4
.L839:
	mov.aa	a13,a5
.L844:

; ..\vss_code\vsskeym.c	    90  {
; ..\vss_code\vsskeym.c	    91  	vss_uint32 certId = 0;
; ..\vss_code\vsskeym.c	    92  	
; ..\vss_code\vsskeym.c	    93  	if (certData == VSS_NULL || certlen == VSS_NULL)
	jz.a	a13,.L21
.L1111:
	jnz.a	a12,.L22
.L21:

; ..\vss_code\vsskeym.c	    94  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	    95  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	    96  	if(certType == CERT_TYPE_ROOT && mem_cmp8(g_vssRootCert, vss_ZERO, 16))
; ..\vss_code\vsskeym.c	    97  	{
; ..\vss_code\vsskeym.c	    98  		mem_cpy8(certData, g_vssRootCert, ROOT_CERT_SIZE);
; ..\vss_code\vsskeym.c	    99  		*certlen = ROOT_CERT_SIZE;
; ..\vss_code\vsskeym.c	   100  		return 0;
; ..\vss_code\vsskeym.c	   101  	}
; ..\vss_code\vsskeym.c	   102  
; ..\vss_code\vsskeym.c	   103  	if(certType == CERT_TYPE_USR && mem_cmp8(g_vssUserCert, vss_ZERO, 16))
; ..\vss_code\vsskeym.c	   104  	{
; ..\vss_code\vsskeym.c	   105  		mem_cpy8(certData, g_vssUserCert, USER_CERT_SIZE);
; ..\vss_code\vsskeym.c	   106  		*certlen = USER_CERT_SIZE;
; ..\vss_code\vsskeym.c	   107  		return 0;
; ..\vss_code\vsskeym.c	   108  	}
; ..\vss_code\vsskeym.c	   109  #endif
; ..\vss_code\vsskeym.c	   110  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   111  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   112  
; ..\vss_code\vsskeym.c	   113  	certId = getEnvIndex(certType);
; ..\vss_code\vsskeym.c	   114  	if(certId == ERR_SYSTEM_INIT)
; ..\vss_code\vsskeym.c	   115  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   116  	return LoadCertById(certId, certlen, certData);
; ..\vss_code\vsskeym.c	   117  }
	mov	d2,#4
	ret
.L22:
	jne	d15,#0,.L24
.L1112:
	movh.a	a15,#@his(g_vssRootCert)
	lea	a15,[a15]@los(g_vssRootCert)
.L1113:
	fcall	.cocofun_9
.L837:
	call	mem_cmp8
.L1114:
	jeq	d2,#0,.L25
.L1115:
	mov	d4,#144
	mov.aa	a4,a13
.L841:
	mov.aa	a5,a15
	call	mem_cpy8
.L842:
	mov	d15,#144
	st.w	[a12],d15
.L840:
	mov	d2,#0
	ret
.L25:
.L24:
	jne	d15,#1,.L27
.L1116:
	movh.a	a15,#@his(g_vssUserCert)
	lea	a15,[a15]@los(g_vssUserCert)
.L1117:
	fcall	.cocofun_9
.L1118:
	call	mem_cmp8
.L1119:
	jeq	d2,#0,.L28
.L1120:
	mov	d4,#176
	mov.aa	a4,a13
.L846:
	mov.aa	a5,a15
	call	mem_cpy8
.L847:
	mov	d15,#176
	st.w	[a12],d15
.L845:
	mov	d2,#0
	ret
.L28:
.L27:
	movh.a	a15,#@his(g_vssIocb)
	ld.w	d0,[a15]@los(g_vssIocb)
.L1121:
	jeq	d0,#0,.L30
.L1122:
	mov	d4,d15
	call	getEnvIndex
.L849:
	mov	d15,#27
.L848:
	jne	d15,d2,.L31
.L30:
	mov	d2,#27
	ret
.L31:
	mov	d4,d2
	mov.aa	a4,a12
.L850:
	mov.aa	a5,a13
.L851:
	j	LoadCertById
.L643:
	
__LoadCert_function_end:
	.size	LoadCert,__LoadCert_function_end-LoadCert
.L416:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_9')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_9
.L325:
.cocofun_9:	.type	func
; Function body .cocofun_9, coco_iter:0
	fcall	.cocofun_14
.L843:
	mov.aa	a4,a15
.L1694:
	fret
.L596:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_14')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_14
.L327:
.cocofun_14:	.type	func
; Function body .cocofun_14, coco_iter:1
	movh.a	a5,#@his(vss_ZERO)
.L838:
	lea	a5,[a5]@los(vss_ZERO)
.L1719:
	mov	d4,#16
	fret
.L621:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('LoadCertById')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	LoadCertById

; ..\vss_code\vsskeym.c	   118  
; ..\vss_code\vsskeym.c	   119  vss_uint32 LoadCertById(vss_uint32 certId, vss_uint32* certlen, vss_uint8* certData)
; Function LoadCertById
.L329:
LoadCertById:	.type	func
	lea	a10,[a10]-536
.L852:
	mov	d8,d4
	mov.aa	a12,a4
.L858:
	mov.aa	a13,a5
.L857:

; ..\vss_code\vsskeym.c	   120  {
; ..\vss_code\vsskeym.c	   121  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   122  	vss_uint8 buf[256];
; ..\vss_code\vsskeym.c	   123  	vss_uint8 cert[256];
; ..\vss_code\vsskeym.c	   124  	vss_uint8 mac[16];
; ..\vss_code\vsskeym.c	   125  	vss_uint32 offset = 0;
; ..\vss_code\vsskeym.c	   126  	vss_uint32 noutlen = 0;
	mov	d15,#0
	st.w	[a10]528,d15
.L1127:

; ..\vss_code\vsskeym.c	   127  	vss_uint32 readlen = 0;
; ..\vss_code\vsskeym.c	   128  
; ..\vss_code\vsskeym.c	   129  	if(certId < SYS_CERT_NUM)
	jge.u	d8,#6,.L34
.L1128:

; ..\vss_code\vsskeym.c	   130  	{
; ..\vss_code\vsskeym.c	   131  		offset = FLASH_ROOTCERT_OFFSET + certId * ROOT_CIPHER_LEN;
	mul	d15,d8,#160
.L1129:
	add	d5,d15,#128
.L853:

; ..\vss_code\vsskeym.c	   132  		readlen = ROOT_CIPHER_LEN;
	mov	d15,#160
	j	.L35
.L34:

; ..\vss_code\vsskeym.c	   133  	}else{
; ..\vss_code\vsskeym.c	   134  		offset = FLASH_USERCERT_OFFSET;
	mov	d5,#1776
.L856:

; ..\vss_code\vsskeym.c	   135  		readlen = USER_CIPHER_LEN;
	mov	d15,#192
.L35:

; ..\vss_code\vsskeym.c	   136  	}
; ..\vss_code\vsskeym.c	   137  
; ..\vss_code\vsskeym.c	   138  	ret = g_vssIocb(FLASH_IO_READ, offset, buf, readlen);
	fcall	.cocofun_1
.L1130:
	mov	d4,#0
	mov.aa	a4,a10
.L855:
	mov	d6,d15
	calli	a15
.L854:

; ..\vss_code\vsskeym.c	   139  	if (ret)
	jeq	d2,#0,.L36
.L1131:

; ..\vss_code\vsskeym.c	   140  	{
; ..\vss_code\vsskeym.c	   141  		return ERR_READ_FLASH;
; ..\vss_code\vsskeym.c	   142  	}
; ..\vss_code\vsskeym.c	   143  
; ..\vss_code\vsskeym.c	   144  	if(mem_cmp8(buf, vss_ZERO, 16) == 0)
; ..\vss_code\vsskeym.c	   145  		return ERR_CERT_NOTFOUND;
; ..\vss_code\vsskeym.c	   146  	
; ..\vss_code\vsskeym.c	   147  	ret = VssSM4Mac(buf, readlen - 16, (vss_uint8*)vsskeym_KEK, 16, mac);
; ..\vss_code\vsskeym.c	   148  	if(ret || mem_cmp8(buf + readlen - 16, mac, 16))
; ..\vss_code\vsskeym.c	   149  		return ERR_DATA_VERIFY;
; ..\vss_code\vsskeym.c	   150  
; ..\vss_code\vsskeym.c	   151  	ret = VssSM4Calc(buf, readlen - 16, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_NO_FORCE,  cert, &noutlen);
; ..\vss_code\vsskeym.c	   152  	if(ret)
; ..\vss_code\vsskeym.c	   153  		return ERR_CERT_INVALID;
; ..\vss_code\vsskeym.c	   154  	
; ..\vss_code\vsskeym.c	   155  	if(certId < SYS_CERT_NUM)
; ..\vss_code\vsskeym.c	   156  	{
; ..\vss_code\vsskeym.c	   157  		if(cert[0] != CERTTYPE_ROOT)
; ..\vss_code\vsskeym.c	   158  			return ERR_CERT_INVALID;
; ..\vss_code\vsskeym.c	   159  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	   160  		mem_cpy8(g_vssRootCert, cert, ROOT_CERT_SIZE);
; ..\vss_code\vsskeym.c	   161  #endif
; ..\vss_code\vsskeym.c	   162  		mem_cpy8(certData, cert, ROOT_CERT_SIZE);
; ..\vss_code\vsskeym.c	   163  		*certlen = ROOT_CERT_SIZE;
; ..\vss_code\vsskeym.c	   164  	}else if(certId == SYS_CERT_NUM)
; ..\vss_code\vsskeym.c	   165  	{
; ..\vss_code\vsskeym.c	   166  		if(cert[0] != CERTTYPE_USR)
; ..\vss_code\vsskeym.c	   167  			return ERR_CERT_INVALID;
; ..\vss_code\vsskeym.c	   168  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	   169  		mem_cpy8(g_vssUserCert, cert, USER_CERT_SIZE);
; ..\vss_code\vsskeym.c	   170  #endif
; ..\vss_code\vsskeym.c	   171  		mem_cpy8(certData, cert, USER_CERT_SIZE);
; ..\vss_code\vsskeym.c	   172  		*certlen = USER_CERT_SIZE;
; ..\vss_code\vsskeym.c	   173  	}
; ..\vss_code\vsskeym.c	   174  	
; ..\vss_code\vsskeym.c	   175  	return ret;
; ..\vss_code\vsskeym.c	   176  }
	mov	d2,#28
	ret
.L36:
	mov.aa	a4,a10
.L1132:
	fcall	.cocofun_14
.L1133:
	call	mem_cmp8
.L859:
	jne	d2,#0,.L38
.L1134:
	mov	d2,#14
	ret
.L38:
	fcall	.cocofun_6
.L860:
	add	d9,d15,#-16
	mov.aa	a4,a10
.L1135:
	mov	d5,#16
	lea	a6,[a10]512
	mov	d4,d9
	mov.aa	a5,a15
	call	VssSM4Mac
.L862:
	jne	d2,#0,.L40
.L1136:
	addsc.a	a2,a10,d15,#0
.L1137:
	mov	d4,#16
	lea	a5,[a10]512
.L1138:
	lea	a4,[a2]-16
.L1139:
	call	mem_cmp8
.L863:
	jeq	d2,#0,.L41
.L40:
	mov	d2,#34
	ret
.L41:
	mov	d4,d9
	mov.aa	a5,a15
.L1140:
	mov	d5,#16
	mov.aa	a4,a10
.L1141:
	mov	d6,#1
	lea	a6,[a10]256
.L1142:
	mov	d7,#0
	lea	a7,[a10]528
	call	VssSM4Calc
.L864:
	jne	d2,#0,.L43
.L1143:
	jge.u	d8,#6,.L44
.L1144:
	ld.bu	d0,[a10]256
.L1145:
	mov	d15,#16
.L861:
	jne	d15,d0,.L45
.L1146:
	fcall	.cocofun_15
.L1147:
	lea	a5,[a10]256
.L1148:
	mov	d4,#144
	call	mem_cpy8
.L865:
	lea	a5,[a10]256
.L1149:
	mov	d4,#144
	mov.aa	a4,a13
.L866:
	call	mem_cpy8
.L867:
	mov	d15,#144
	j	.L46
.L44:
	jne	d8,#6,.L47
.L1150:
	ld.bu	d0,[a10]256
.L1151:
	mov	d15,#48
.L868:
	jeq	d15,d0,.L48
.L45:
.L43:
	mov	d2,#26
	ret
.L48:
	fcall	.cocofun_16
.L869:
	lea	a5,[a10]256
.L1152:
	mov	d4,#176
	call	mem_cpy8
.L870:
	lea	a5,[a10]256
.L1153:
	mov	d4,#176
	mov.aa	a4,a13
.L871:
	call	mem_cpy8
.L872:
	mov	d15,#176
.L46:
	st.w	[a12],d15
.L47:
	mov	d2,#0
	ret
.L651:
	
__LoadCertById_function_end:
	.size	LoadCertById,__LoadCertById_function_end-LoadCertById
.L421:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_16')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_16
.L331:
.cocofun_16:	.type	func
; Function body .cocofun_16, coco_iter:1
	movh.a	a4,#@his(g_vssUserCert)
	lea	a4,[a4]@los(g_vssUserCert)
.L1729:
	fret
.L631:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_15')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_15
.L333:
.cocofun_15:	.type	func
; Function body .cocofun_15, coco_iter:1
	movh.a	a4,#@his(g_vssRootCert)
	lea	a4,[a4]@los(g_vssRootCert)
.L1724:
	fret
.L626:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_6')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_6
.L335:
.cocofun_6:	.type	func
; Function body .cocofun_6, coco_iter:0
	movh.a	a15,#@his(vsskeym_KEK)
	lea	a15,[a15]@los(vsskeym_KEK)
.L1677:
	fret
.L581:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_1')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_1
.L337:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh.a	a15,#@his(g_vssIocb)
.L1018:
	ld.a	a15,[a15]@los(g_vssIocb)
.L1651:
	fret
.L556:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('SaveCert')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SaveCert

; ..\vss_code\vsskeym.c	   177  
; ..\vss_code\vsskeym.c	   178  vss_uint32 SaveCert(vss_uint32 certType, vss_uint32 certlen, vss_uint8* certData)
; Function SaveCert
.L339:
SaveCert:	.type	func
	lea	a10,[a10]-264
.L873:
	mov	e10,d5,d4
	mov.aa	a13,a4
.L875:

; ..\vss_code\vsskeym.c	   179  {
; ..\vss_code\vsskeym.c	   180  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   181  	vss_uint8 buf[256];
; ..\vss_code\vsskeym.c	   182  	vss_uint32 certId = 0;
; ..\vss_code\vsskeym.c	   183  	vss_uint32 offset = 0;
; ..\vss_code\vsskeym.c	   184  	vss_uint32 noutlen =0;
	mov	d15,#0
	st.w	[a10]256,d15
.L1158:

; ..\vss_code\vsskeym.c	   185  	vss_uint32 readlen = 0;
; ..\vss_code\vsskeym.c	   186  	
; ..\vss_code\vsskeym.c	   187  	if (certData == VSS_NULL)
	jz.a	a13,.L51
.L1159:

; ..\vss_code\vsskeym.c	   188  		return ERR_PARAMETER;	
; ..\vss_code\vsskeym.c	   189  
; ..\vss_code\vsskeym.c	   190  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_7
.L1160:
	jeq	d15,#0,.L52
.L877:

; ..\vss_code\vsskeym.c	   191  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   192  
; ..\vss_code\vsskeym.c	   193  	certId = getEnvIndex(certType);
	mov	d4,d10
	call	getEnvIndex
.L876:

; ..\vss_code\vsskeym.c	   194  	if(certId == ERR_SYSTEM_INIT)
	mov	d15,#27
.L1161:
	jne	d15,d2,.L53

; ..\vss_code\vsskeym.c	   195  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   196  
; ..\vss_code\vsskeym.c	   197  	switch(certType)
; ..\vss_code\vsskeym.c	   198  	{
; ..\vss_code\vsskeym.c	   199  		case CERT_TYPE_ROOT:
; ..\vss_code\vsskeym.c	   200  			offset = FLASH_ROOTCERT_OFFSET + certId * ROOT_CIPHER_LEN;
; ..\vss_code\vsskeym.c	   201  			readlen = ROOT_CIPHER_LEN;
; ..\vss_code\vsskeym.c	   202  			break;
; ..\vss_code\vsskeym.c	   203  		case CERT_TYPE_USR:
; ..\vss_code\vsskeym.c	   204  			offset = FLASH_USERCERT_OFFSET;
; ..\vss_code\vsskeym.c	   205  			readlen = USER_CIPHER_LEN;
; ..\vss_code\vsskeym.c	   206  			break;
; ..\vss_code\vsskeym.c	   207  		default:
; ..\vss_code\vsskeym.c	   208  			return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   209  	}
; ..\vss_code\vsskeym.c	   210  
; ..\vss_code\vsskeym.c	   211  	if(certlen != (readlen - 16))
; ..\vss_code\vsskeym.c	   212  	{
; ..\vss_code\vsskeym.c	   213  		return ERR_LEN_INVALID;
; ..\vss_code\vsskeym.c	   214  	}
; ..\vss_code\vsskeym.c	   215  	
; ..\vss_code\vsskeym.c	   216  	ret = VssSM4Calc((vss_uint8*)certData, certlen, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
; ..\vss_code\vsskeym.c	   217  	if (ret)
; ..\vss_code\vsskeym.c	   218  	{
; ..\vss_code\vsskeym.c	   219  		return ret;
; ..\vss_code\vsskeym.c	   220  	}
; ..\vss_code\vsskeym.c	   221  	
; ..\vss_code\vsskeym.c	   222  	ret = VssSM4Mac(buf, certlen, (vss_uint8*)vsskeym_KEK, 16, buf + certlen);
; ..\vss_code\vsskeym.c	   223  	if (ret)
; ..\vss_code\vsskeym.c	   224  	{
; ..\vss_code\vsskeym.c	   225  		return ret;
; ..\vss_code\vsskeym.c	   226  	}
; ..\vss_code\vsskeym.c	   227  	
; ..\vss_code\vsskeym.c	   228  	ret = g_vssIocb(FLASH_IO_WRITE, offset, buf, readlen);
; ..\vss_code\vsskeym.c	   229  	if (ret)
; ..\vss_code\vsskeym.c	   230  	{
; ..\vss_code\vsskeym.c	   231  		return ERR_WRITE_FLASH;
; ..\vss_code\vsskeym.c	   232  	}
; ..\vss_code\vsskeym.c	   233  
; ..\vss_code\vsskeym.c	   234  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))	
; ..\vss_code\vsskeym.c	   235  	if(certType == CERT_TYPE_ROOT)
; ..\vss_code\vsskeym.c	   236  	{
; ..\vss_code\vsskeym.c	   237  		mem_cpy8(g_vssRootCert, certData, ROOT_CERT_SIZE);
; ..\vss_code\vsskeym.c	   238  	}else if(certType == CERT_TYPE_USR)
; ..\vss_code\vsskeym.c	   239  	{
; ..\vss_code\vsskeym.c	   240  		mem_cpy8(g_vssUserCert, certData, USER_CERT_SIZE);
; ..\vss_code\vsskeym.c	   241  	}
; ..\vss_code\vsskeym.c	   242  #endif	
; ..\vss_code\vsskeym.c	   243  	return 0;	
; ..\vss_code\vsskeym.c	   244  }
.L52:
	mov	d2,#27
	ret
.L53:
	jeq	d10,#0,.L55
.L1162:
	jeq	d10,#1,.L56
.L878:
	j	.L57
.L55:
	mul	d0,d2,#160
.L1163:
	mov	d9,#160
.L879:
	add	d8,d0,#128
	j	.L58
.L56:
	mov	d8,#1776
.L880:
	mov	d9,#192
	j	.L59
.L57:
.L51:
	mov	d2,#4
	ret
.L59:
.L58:
	add	d15,d9,#-16
.L881:
	jeq	d15,d11,.L61
.L882:
	mov	d2,#18
	ret
.L61:
	movh.a	a12,#@his(vsskeym_KEK)
	lea	a12,[a12]@los(vsskeym_KEK)
.L1164:
	fcall	.cocofun_3
.L874:
	mov.aa	a6,a10
.L883:
	lea	a7,[a10]256
	mov.aa	a4,a13
.L885:
	mov	d4,d11
	mov.aa	a5,a12
.L886:
	call	VssSM4Calc
.L884:
	jne	d2,#0,.L65
.L1165:
	mov.aa	a4,a10
.L887:
	mov	d5,#16
	addsc.a	a6,a10,d11,#0
	mov	d4,d11
	mov.aa	a5,a12
.L888:
	call	VssSM4Mac
.L889:
	jne	d2,#0,.L65
.L1166:
	ld.a	a15,[a15]
.L1167:
	mov	d4,#1
	mov.aa	a4,a10
.L1168:
	mov	d5,d8
.L890:
	mov	d6,d9
	calli	a15
.L891:
	jeq	d2,#0,.L66
.L1169:
	mov	d2,#29
.L65:
	ret
.L66:
	jne	d10,#0,.L68
.L1170:
	fcall	.cocofun_15
.L892:
	mov	d4,#144
	j	.L69
.L68:
	fcall	.cocofun_16
.L893:
	mov	d4,#176
.L69:
	mov.aa	a5,a13
.L895:
	call	mem_cpy8
.L894:
	mov	d2,#0
	ret
.L665:
	
__SaveCert_function_end:
	.size	SaveCert,__SaveCert_function_end-SaveCert
.L426:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_7')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_7
.L341:
.cocofun_7:	.type	func
; Function body .cocofun_7, coco_iter:0
	movh.a	a15,#@his(g_vssIocb)
	lea	a15,[a15]@los(g_vssIocb)
	ld.w	d15,[a15]
.L1682:
	fret
.L586:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_3')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_3
.L343:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:0
	mov	d5,#16
.L1000:
	mov	d6,#0
.L1661:
	mov	d7,d6
	fret
.L566:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('GenSymmKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GenSymmKey

; ..\vss_code\vsskeym.c	   245  
; ..\vss_code\vsskeym.c	   246  vss_uint32 GenSymmKey(vss_uint8* code, vss_char8* keyList)
; Function GenSymmKey
.L345:
GenSymmKey:	.type	func
	lea	a10,[a10]-616
.L896:
	mov.aa	a14,a4
.L899:
	mov.aa	a12,a5
.L900:

; ..\vss_code\vsskeym.c	   247  {
; ..\vss_code\vsskeym.c	   248  	vss_uint8 vss_div[16];
; ..\vss_code\vsskeym.c	   249  	vss_uint8 sn[8];
; ..\vss_code\vsskeym.c	   250  	vss_uint8 key[16];
; ..\vss_code\vsskeym.c	   251  	vss_uint8 i = 0; 
; ..\vss_code\vsskeym.c	   252  	vss_uint8 k = 0; 
; ..\vss_code\vsskeym.c	   253  	vss_uint8 flag = 0; 
; ..\vss_code\vsskeym.c	   254  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   255  	vss_uint8 mk[16];
; ..\vss_code\vsskeym.c	   256  	vss_uint8 keyatb[4];
; ..\vss_code\vsskeym.c	   257  	vss_uint8 cfgData[FLASH_CFG_DATA];
; ..\vss_code\vsskeym.c	   258  	vss_uint8 backup[SYMM_KEY_NUM * SYMM_CIPHER_LEN];
; ..\vss_code\vsskeym.c	   259  	vss_uint32 noutlen=0;
	mov	d8,#0
	st.w	[a10]604,d8
.L901:

; ..\vss_code\vsskeym.c	   260  	vss_uint32 key_off=0;
; ..\vss_code\vsskeym.c	   261  
; ..\vss_code\vsskeym.c	   262  	if(code == VSS_NULL || keyList == VSS_NULL)
	jz.a	a14,.L71
.L1175:
	jnz.a	a12,.L72
.L71:

; ..\vss_code\vsskeym.c	   263  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   264  
; ..\vss_code\vsskeym.c	   265  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   266  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   267  	
; ..\vss_code\vsskeym.c	   268  	ret = LoadCFGData(cfgData);
; ..\vss_code\vsskeym.c	   269  	if (ret)
; ..\vss_code\vsskeym.c	   270  	{
; ..\vss_code\vsskeym.c	   271  		return ret;
; ..\vss_code\vsskeym.c	   272  	}
; ..\vss_code\vsskeym.c	   273  
; ..\vss_code\vsskeym.c	   274  	ret = g_vssIocb(FLASH_IO_READ, FLASH_KEYDATA_OFFSET, backup, SYMM_KEY_NUM * SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   275  	if (ret)
; ..\vss_code\vsskeym.c	   276  	{
; ..\vss_code\vsskeym.c	   277  		return ERR_READ_FLASH;
; ..\vss_code\vsskeym.c	   278  	}
; ..\vss_code\vsskeym.c	   279  
; ..\vss_code\vsskeym.c	   280  	mem_set8(sn, 0, 8);
; ..\vss_code\vsskeym.c	   281  	for (i = 0; i < 16; i++)
; ..\vss_code\vsskeym.c	   282  	{
; ..\vss_code\vsskeym.c	   283  		mk[i] = code[i] ^ code[i + 16];
; ..\vss_code\vsskeym.c	   284  	}
; ..\vss_code\vsskeym.c	   285  
; ..\vss_code\vsskeym.c	   286  	keyList[0] = 0;
; ..\vss_code\vsskeym.c	   287  	for (i = 0; i < SYMM_KEY_NUM; i++)
; ..\vss_code\vsskeym.c	   288  	{		
; ..\vss_code\vsskeym.c	   289  		mem_cpy8(keyatb, cfgData + FLASH_CFG_KEY + i * 4, 4);
; ..\vss_code\vsskeym.c	   290  		if(i >= COMM_KEY && i < RESV_KEY)
; ..\vss_code\vsskeym.c	   291  		{
; ..\vss_code\vsskeym.c	   292  			flag = keyatb[1] & ~KEY_FLAG_VALID;
; ..\vss_code\vsskeym.c	   293  		}else{
; ..\vss_code\vsskeym.c	   294  			flag = keyatb[1] | KEY_FLAG_VALID;
; ..\vss_code\vsskeym.c	   295  		}
; ..\vss_code\vsskeym.c	   296  
; ..\vss_code\vsskeym.c	   297  		{
; ..\vss_code\vsskeym.c	   298  			sn[7] = i;
; ..\vss_code\vsskeym.c	   299  			for (k = 0; k < 8; k++)
; ..\vss_code\vsskeym.c	   300  			{
; ..\vss_code\vsskeym.c	   301  				vss_div[0 + k] = sn[k];
; ..\vss_code\vsskeym.c	   302  				vss_div[8 + k] = ~sn[k];
; ..\vss_code\vsskeym.c	   303  			}			
; ..\vss_code\vsskeym.c	   304  			
; ..\vss_code\vsskeym.c	   305  			VssSM4Calc(vss_div, 16, mk, 16, 0, 0, key, &noutlen);
; ..\vss_code\vsskeym.c	   306  		}
; ..\vss_code\vsskeym.c	   307  
; ..\vss_code\vsskeym.c	   308  		ret = SetSymmKey(i, flag, keyatb[3], key);
; ..\vss_code\vsskeym.c	   309  		if (ret)
; ..\vss_code\vsskeym.c	   310  		{
; ..\vss_code\vsskeym.c	   311  			break;
; ..\vss_code\vsskeym.c	   312  		}
; ..\vss_code\vsskeym.c	   313  
; ..\vss_code\vsskeym.c	   314  		if((keyatb[1] & KEY_FLAG_USED) > 0)
; ..\vss_code\vsskeym.c	   315  		{
; ..\vss_code\vsskeym.c	   316  			if(i > 9)
; ..\vss_code\vsskeym.c	   317  			{
; ..\vss_code\vsskeym.c	   318  				keyList[key_off++] = '0' + (i / 10);
; ..\vss_code\vsskeym.c	   319  				keyList[key_off++] = '0' + (i % 10);
; ..\vss_code\vsskeym.c	   320  			}else{
; ..\vss_code\vsskeym.c	   321  				keyList[key_off++] = '0' + i;
; ..\vss_code\vsskeym.c	   322  			}
; ..\vss_code\vsskeym.c	   323  			keyList[key_off++] = ',';
; ..\vss_code\vsskeym.c	   324  		}
; ..\vss_code\vsskeym.c	   325  	}
; ..\vss_code\vsskeym.c	   326  
; ..\vss_code\vsskeym.c	   327  	if(ret)
; ..\vss_code\vsskeym.c	   328  	{
; ..\vss_code\vsskeym.c	   329  		g_vssIocb(FLASH_IO_WRITE, FLASH_KEYDATA_OFFSET, backup, SYMM_KEY_NUM * SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   330  	}else{
; ..\vss_code\vsskeym.c	   331  		if(code[31] & 1)
; ..\vss_code\vsskeym.c	   332  			ret = SetAlg(ALG_GJ);
; ..\vss_code\vsskeym.c	   333  		else
; ..\vss_code\vsskeym.c	   334  			ret = SetAlg(ALG_GM);
; ..\vss_code\vsskeym.c	   335  
; ..\vss_code\vsskeym.c	   336  		SaveKeyCode(32, code);
; ..\vss_code\vsskeym.c	   337  	}
; ..\vss_code\vsskeym.c	   338  
; ..\vss_code\vsskeym.c	   339  	return ret;
; ..\vss_code\vsskeym.c	   340  }
	mov	d2,#4
	ret
.L72:
	movh.a	a13,#@his(g_vssIocb)
	lea	a13,[a13]@los(g_vssIocb)
	ld.w	d15,[a13]
.L1176:
	jne	d15,#0,.L74
.L1177:
	mov	d2,#27
	ret
.L74:
	lea	a4,[a10]60
.L897:
	call	LoadCFGData
.L898:
	mov	d9,d2
.L904:
	jne	d9,#0,.L76
.L1178:
	ld.a	a15,[a13]
.L1179:
	mov	d4,#0
	lea	a4,[a10]156
.L1180:
	mov	d5,#1152
.L1181:
	mov	d6,#448
	calli	a15
.L905:
	jeq	d2,#0,.L77
.L1182:
	mov	d2,#28
	ret
.L77:
	mov	d4,#0
	lea	a4,[a10]16
.L1183:
	mov	d5,#8
	call	mem_set8
.L903:
	mov	d15,#0
	mov.aa	a15,a14
.L906:
	mov.a	a2,#15
.L79:
	addsc.a	a4,a10,d15,#0
.L1184:
	ld.bu	d0,[a15]
.L1185:
	ld.bu	d1,[a15]16
.L1186:
	add	d15,#1
	add.a	a15,#1
.L1187:
	xor	d0,d1
	st.b	[a4]40,d0
	loop	a2,.L79
.L1188:
	mov	d10,#0
	st.b	[a12],d10
.L907:
	mov.a	a15,#13
	st.a	[a10]608,a15
.L80:
	addsc.a	a15,a10,d10,#2
.L1189:
	mov	d4,#4
	lea	a4,[a10]56
.L1190:
	lea	a5,[a15]88
.L1191:
	call	mem_cpy8
.L1192:
	add	d0,d10,#-5
	ld.bu	d15,[a10]57
.L1193:
	extr.u	d0,d0,#0,#8
.L1194:
	jge.u	d0,#4,.L81
.L1195:
	and	d9,d15,#251
	j	.L82
.L81:
	or	d9,d15,#4
.L82:
	st.b	[a10]23,d10
.L1196:
	mov	d0,#0
	lea	a2,[a10]16
.L909:
	mov.a	a4,#7
.L83:
	addsc.a	a15,a10,d0,#0
.L1197:
	ld.bu	d1,[a2+]
.L1198:
	add	d0,#1
	st.b	[a15],d1
.L1199:
	xor	d15,d1,#255
	st.b	[a15]8,d15
	loop	a4,.L83
.L1200:
	mov	d4,#16
	mov.aa	a4,a10
.L1201:
	mov	d6,#0
	lea	a5,[a10]40
.L1202:
	mov	d5,d4
	lea	a6,[a10]24
.L1203:
	mov	d7,d6
	lea	a7,[a10]604
	call	VssSM4Calc
.L910:
	ld.bu	d6,[a10]59
.L1204:
	lea	a4,[a10]24
	mov	e4,d9,d10
	call	SetSymmKey
.L911:
	mov	d9,d2
.L908:
	jne	d9,#0,.L84
.L1205:
	ld.bu	d0,[a10]57
.L1206:
	jz.t	d0:0,.L85
.L1207:
	addsc.a	a15,a12,d8,#0
.L1208:
	jlt.u	d10,#10,.L86
.L1209:
	mov	d15,#49
	st.b	[a15],d15
.L1210:
	mov	d15,#10
.L1211:
	div.u	e0,d10,d15
.L1212:
	add	d8,#1
.L1213:
	addsc.a	a15,a12,d8,#0
.L1214:
	add	d15,d1,#48
	j	.L87
.L86:
	add	d15,d10,#48
.L87:
	st.b	[a15],d15
.L1215:
	add	d15,d8,#1
.L902:
	addsc.a	a15,a12,d15,#0
.L1216:
	add	d8,d15,#1
.L914:
	mov	d0,#44
	st.b	[a15],d0
.L85:
	ld.a	a15,[a10]608
.L1217:
	add	d10,#1
.L1218:
	add.a	a15,#-1
	st.a	[a10]608,a15
	add.a	a15,#1
	loop	a15,.L80
.L84:
	jeq	d9,#0,.L88
.L1219:
	ld.a	a15,[a13]
.L1220:
	mov	d4,#1
	lea	a4,[a10]156
.L1221:
	mov	d5,#1152
.L1222:
	mov	d6,#448
	calli	a15
.L912:
	j	.L89
.L88:
	ld.bu	d15,[a14]31
.L1223:
	jz.t	d15:0,.L90
.L1224:
	mov	d4,#1
	j	.L91
.L90:
	mov	d4,#2
.L91:
	call	SetAlg
.L913:
	mov	d9,d2
	mov.aa	a4,a14
.L915:
	mov	d4,#32
	call	SaveKeyCode
.L89:
.L76:
	mov	d2,d9
	ret
.L676:
	
__GenSymmKey_function_end:
	.size	GenSymmKey,__GenSymmKey_function_end-GenSymmKey
.L431:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('LoadSymmKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	LoadSymmKey

; ..\vss_code\vsskeym.c	   341  
; ..\vss_code\vsskeym.c	   342  vss_uint32 LoadSymmKey(vss_uint32 keyId,  TSymmKeyItem* key)
; Function LoadSymmKey
.L347:
LoadSymmKey:	.type	func
	mov	d8,d4
	sub.a	a10,#40
.L916:

; ..\vss_code\vsskeym.c	   343  {
; ..\vss_code\vsskeym.c	   344  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   345  	vss_uint8 buf[SYMM_CIPHER_LEN];
; ..\vss_code\vsskeym.c	   346  	vss_uint32 offset = 0;
; ..\vss_code\vsskeym.c	   347  	vss_uint32 noutlen =0;
	mov	d15,#0
	st.w	[a10]32,d15
.L1586:

; ..\vss_code\vsskeym.c	   348  	vss_uint8 i = 0;
; ..\vss_code\vsskeym.c	   349  
; ..\vss_code\vsskeym.c	   350  	if (keyId >= TOTAL_KEY_NUM)
	mov	d15,#16
	mov.aa	a15,a4
.L920:
	jlt.u	d8,d15,.L93
.L1587:

; ..\vss_code\vsskeym.c	   351  		return ERR_INDEX_INVALID;
; ..\vss_code\vsskeym.c	   352  	
; ..\vss_code\vsskeym.c	   353  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   354  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   355  
; ..\vss_code\vsskeym.c	   356  	mem_set8((vss_uint8*)key, 0, SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   357  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))	
; ..\vss_code\vsskeym.c	   358  	if(keyId < SYMM_KEY_NUM)
; ..\vss_code\vsskeym.c	   359  	{
; ..\vss_code\vsskeym.c	   360  		if(mem_cmp8(g_vssSymmKey[keyId], vss_ZERO, 16))
; ..\vss_code\vsskeym.c	   361  			mem_cpy8((vss_uint8*)key, g_vssSymmKey[keyId], SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   362  	}else{
; ..\vss_code\vsskeym.c	   363  		if(mem_cmp8(g_vssSessKey[keyId - SYMM_KEY_NUM], vss_ZERO, 16))
; ..\vss_code\vsskeym.c	   364  			mem_cpy8((vss_uint8*)key, g_vssSessKey[keyId - SYMM_KEY_NUM], SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   365  	}
; ..\vss_code\vsskeym.c	   366  
; ..\vss_code\vsskeym.c	   367  	if(key->cFlag & KEY_FLAG_USED)
; ..\vss_code\vsskeym.c	   368  		return 0;
; ..\vss_code\vsskeym.c	   369  #endif
; ..\vss_code\vsskeym.c	   370  	offset = FLASH_KEYDATA_OFFSET + SYMM_CIPHER_LEN * keyId;
; ..\vss_code\vsskeym.c	   371  
; ..\vss_code\vsskeym.c	   372  	i = 0;
; ..\vss_code\vsskeym.c	   373  	while(i++ < FLASH_IO_RETRY_COUNT)
; ..\vss_code\vsskeym.c	   374  	{
; ..\vss_code\vsskeym.c	   375  		ret = g_vssIocb(FLASH_IO_READ, offset, buf, SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   376  		if (ret == 0)
; ..\vss_code\vsskeym.c	   377  		{
; ..\vss_code\vsskeym.c	   378  			break;
; ..\vss_code\vsskeym.c	   379  		}
; ..\vss_code\vsskeym.c	   380  	}
; ..\vss_code\vsskeym.c	   381  	
; ..\vss_code\vsskeym.c	   382  	if (ret)
; ..\vss_code\vsskeym.c	   383  	{
; ..\vss_code\vsskeym.c	   384  		return ERR_READ_FLASH;
; ..\vss_code\vsskeym.c	   385  	}
; ..\vss_code\vsskeym.c	   386  	
; ..\vss_code\vsskeym.c	   387  	if (mem_cmp8(buf, vss_ZERO, 16) == 0)
; ..\vss_code\vsskeym.c	   388  		return ERR_KEY_NOTFOUND;
; ..\vss_code\vsskeym.c	   389  	
; ..\vss_code\vsskeym.c	   390  	ret = VssSM4Calc(buf, SYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_NO_FORCE, (vss_uint8*)key, &noutlen);
; ..\vss_code\vsskeym.c	   391  	if(ret)
; ..\vss_code\vsskeym.c	   392  		return ERR_KEY_INVALID;
; ..\vss_code\vsskeym.c	   393  
; ..\vss_code\vsskeym.c	   394   	if(key->cKeyLen != 16 && key->cKeyLen != 12 && key->cKeyLen != 8
; ..\vss_code\vsskeym.c	   395  		&& key->cKeyLen != 4 && key->cKeyLen != 2)
; ..\vss_code\vsskeym.c	   396  	{
; ..\vss_code\vsskeym.c	   397  		return ERR_KEYLEN_INVALID;
; ..\vss_code\vsskeym.c	   398  	}
; ..\vss_code\vsskeym.c	   399  
; ..\vss_code\vsskeym.c	   400  	//if (key->cFlag & (KEY_FLAG_USED|KEY_FLAG_EXPORT))
; ..\vss_code\vsskeym.c	   401  	if (key->cFlag & KEY_FLAG_USED)
; ..\vss_code\vsskeym.c	   402  	{
; ..\vss_code\vsskeym.c	   403  		ret = VssSM4Calc((vss_uint8*)vss_ZERO, 16, key->value, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
; ..\vss_code\vsskeym.c	   404  		if(ret)
; ..\vss_code\vsskeym.c	   405  			return ERR_KEY_INVALID;
; ..\vss_code\vsskeym.c	   406  
; ..\vss_code\vsskeym.c	   407  		if(mem_cmp8(buf, key->cv, 3) != 0)
; ..\vss_code\vsskeym.c	   408  			return ERR_DATA_VERIFY;
; ..\vss_code\vsskeym.c	   409  		
; ..\vss_code\vsskeym.c	   410  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	   411  		if(keyId < SYMM_KEY_NUM)
; ..\vss_code\vsskeym.c	   412  		{
; ..\vss_code\vsskeym.c	   413  			mem_cpy8(g_vssSymmKey[keyId], (vss_uint8*)key, SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   414  		}else{
; ..\vss_code\vsskeym.c	   415  			mem_cpy8(g_vssSessKey[keyId - SYMM_KEY_NUM], (vss_uint8*)key, SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   416  		}
; ..\vss_code\vsskeym.c	   417  #endif
; ..\vss_code\vsskeym.c	   418  		
; ..\vss_code\vsskeym.c	   419  		ret = 0;
; ..\vss_code\vsskeym.c	   420  	}else{
; ..\vss_code\vsskeym.c	   421  		ret = ERR_KEY_NOTFOUND;
; ..\vss_code\vsskeym.c	   422  	}
; ..\vss_code\vsskeym.c	   423  	
; ..\vss_code\vsskeym.c	   424  	return ret;
; ..\vss_code\vsskeym.c	   425  }
	mov	d2,#15
	ret
.L93:
	movh.a	a12,#@his(g_vssIocb)
	lea	a12,[a12]@los(g_vssIocb)
	ld.w	d15,[a12]
.L1588:
	jne	d15,#0,.L95
.L1589:
	mov	d2,#27
	ret
.L95:
	mov	d4,#0
	mov.aa	a4,a15
.L919:
	mov	d5,#32
	call	mem_set8
.L918:
	movh.a	a13,#@his(vss_ZERO)
	lea	a13,[a13]@los(vss_ZERO)
.L1590:
	jge.u	d8,#14,.L97
.L1591:
	sh	d15,d8,#5
	movh.a	a2,#@his(g_vssSymmKey)
	lea	a2,[a2]@los(g_vssSymmKey)
.L1592:
	addsc.a	a14,a2,d15,#0
.L1593:
	mov	d4,#16
	mov.aa	a5,a13
	mov.aa	a4,a14
	call	mem_cmp8
.L1594:
	jeq	d2,#0,.L98
.L1595:
	j	.L99
.L97:
	sh	d15,d8,#5
	movh.a	a2,#@his(g_vssSessKey)
	lea	a2,[a2]@los(g_vssSessKey)
.L1596:
	addsc.a	a2,a2,d15,#0
.L1597:
	mov	d4,#16
	mov.aa	a5,a13
.L1598:
	lea	a14,[a2]-448
.L1599:
	mov.aa	a4,a14
	call	mem_cmp8
.L1600:
	jeq	d2,#0,.L100
.L99:
	mov	d4,#32
	mov.aa	a4,a15
.L921:
	mov.aa	a5,a14
	call	mem_cpy8
.L100:
.L98:
	ld.bu	d15,[a15]
.L1601:
	jz.t	d15:0,.L101
.L1602:
	mov	d2,#0
	ret
.L101:
	sh	d11,d8,#5
.L1603:
	addi	d9,d11,#1152
.L922:
	mov	d10,#0
.L103:
	add	d10,#1
	ld.a	a2,[a12]
.L1604:
	mov	d4,#0
	mov.aa	a4,a10
.L1605:
	mov	d6,#32
	mov	d5,d9
	calli	a2
.L923:
	jeq	d2,#0,.L104
.L1606:
	jlt.u	d10,#3,.L103
.L104:
	jeq	d2,#0,.L105
.L1607:
	mov	d2,#28
	ret
.L105:
	mov.aa	a4,a10
.L1608:
	mov	d4,#16
	mov.aa	a5,a13
	call	mem_cmp8
.L924:
	jeq	d2,#0,.L108
.L1609:
	mov.aa	a4,a10
.L1610:
	fcall	.cocofun_4
.L917:
	lea	a7,[a10]32
.L926:
	mov.aa	a6,a15
.L928:
	call	VssSM4Calc
.L929:
	jne	d2,#0,.L109
.L1611:
	ld.bu	d15,[a15]1
.L1612:
	mov	d0,#16
.L1613:
	jeq	d15,d0,.L110
.L1614:
	mov	d0,#12
.L1615:
	jeq	d15,d0,.L111
.L1616:
	mov	d0,#8
.L1617:
	jeq	d15,d0,.L112
.L1618:
	jeq	d15,#4,.L113
.L1619:
	jeq	d15,#2,.L114
.L1620:
	mov	d2,#25
	ret
.L114:
.L113:
.L112:
.L111:
.L110:
	ld.bu	d15,[a15]
.L1621:
	jz.t	d15:0,.L116
.L1622:
	mov	d4,#16
	lea	a5,[a15]8
.L1623:
	mov	d6,#0
	mov.aa	a4,a13
.L1624:
	mov	d5,d4
	mov.aa	a6,a10
.L1625:
	mov	d7,d6
	lea	a7,[a10]32
	call	VssSM4Calc
.L1626:
	jeq	d2,#0,.L117
.L109:
	mov	d2,#6
	ret
.L117:
	mov.aa	a4,a10
.L1627:
	lea	a5,[a15]4
.L1628:
	mov	d4,#3
	call	mem_cmp8
.L930:
	jeq	d2,#0,.L119
.L1629:
	mov	d2,#34
	ret
.L119:
	jge.u	d8,#14,.L121
.L1630:
	sh	d8,#5
	movh.a	a2,#@his(g_vssSymmKey)
.L927:
	lea	a2,[a2]@los(g_vssSymmKey)
.L1631:
	addsc.a	a4,a2,d8,#0
.L1632:
	j	.L122
.L121:
	movh.a	a2,#@his(g_vssSessKey)
	lea	a2,[a2]@los(g_vssSessKey)
.L1633:
	addsc.a	a2,a2,d11,#0
	lea	a4,[a2]-448
.L122:
	mov	d4,#32
	mov.aa	a5,a15
.L931:
	call	mem_cpy8
.L932:
	mov	d2,#0
	ret
.L116:
.L108:
	mov	d2,#5
	ret
.L818:
	
__LoadSymmKey_function_end:
	.size	LoadSymmKey,__LoadSymmKey_function_end-LoadSymmKey
.L546:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_4')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_4
.L349:
.cocofun_4:	.type	func
; Function body .cocofun_4, coco_iter:0
	fcall	.cocofun_17
.L925:
	mov	d5,#16
.L1666:
	mov	d6,#1
.L1667:
	mov	d7,#0
	fret
.L571:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_17')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_17
.L351:
.cocofun_17:	.type	func
; Function body .cocofun_17, coco_iter:1
	mov	d4,#32
	movh.a	a5,#@his(vsskeym_KEK)
	lea	a5,[a5]@los(vsskeym_KEK)
.L1734:
	fret
.L636:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('SaveSymmKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SaveSymmKey

; ..\vss_code\vsskeym.c	   426  
; ..\vss_code\vsskeym.c	   427  vss_uint32 SaveSymmKey(vss_uint32 keyId, TSymmKeyItem* key)
; Function SaveSymmKey
.L353:
SaveSymmKey:	.type	func
	mov	d8,d4
	sub.a	a10,#40
.L933:

; ..\vss_code\vsskeym.c	   428  {
; ..\vss_code\vsskeym.c	   429  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   430  	vss_uint32 offset = 0;
; ..\vss_code\vsskeym.c	   431  	vss_uint32 noutlen =0;
	mov	d15,#0
	st.w	[a10],d15
.L934:

; ..\vss_code\vsskeym.c	   432  	vss_uint8 buf[SYMM_CIPHER_LEN];
; ..\vss_code\vsskeym.c	   433  	vss_uint8 i = 0;
; ..\vss_code\vsskeym.c	   434  
; ..\vss_code\vsskeym.c	   435  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_7
.L935:
	jne	d15,#0,.L125
.L1638:

; ..\vss_code\vsskeym.c	   436  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   437  		
; ..\vss_code\vsskeym.c	   438  	if (keyId >= TOTAL_KEY_NUM)
; ..\vss_code\vsskeym.c	   439  		return ERR_INDEX_INVALID;
; ..\vss_code\vsskeym.c	   440  
; ..\vss_code\vsskeym.c	   441  	ret = VssSM4Calc((vss_uint8*)key, SYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
; ..\vss_code\vsskeym.c	   442  	if(ret)
; ..\vss_code\vsskeym.c	   443  		return ERR_KEY_INVALID;
; ..\vss_code\vsskeym.c	   444  	
; ..\vss_code\vsskeym.c	   445  	offset = FLASH_KEYDATA_OFFSET + SYMM_CIPHER_LEN * keyId;
; ..\vss_code\vsskeym.c	   446  
; ..\vss_code\vsskeym.c	   447  	i = 0;
; ..\vss_code\vsskeym.c	   448  	while(i++ < FLASH_IO_RETRY_COUNT)
; ..\vss_code\vsskeym.c	   449  	{
; ..\vss_code\vsskeym.c	   450  		ret = g_vssIocb(FLASH_IO_WRITE, offset, buf, SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   451  		if (ret == 0)
; ..\vss_code\vsskeym.c	   452  		{
; ..\vss_code\vsskeym.c	   453  			break;
; ..\vss_code\vsskeym.c	   454  		}
; ..\vss_code\vsskeym.c	   455  	}
; ..\vss_code\vsskeym.c	   456  	
; ..\vss_code\vsskeym.c	   457  	if (ret)
; ..\vss_code\vsskeym.c	   458  	{
; ..\vss_code\vsskeym.c	   459  		return ERR_WRITE_FLASH;
; ..\vss_code\vsskeym.c	   460  	}
; ..\vss_code\vsskeym.c	   461  
; ..\vss_code\vsskeym.c	   462  	return 0;
; ..\vss_code\vsskeym.c	   463  }
	mov	d2,#27
	ret
.L125:
	mov	d15,#16
.L1639:
	jlt.u	d8,d15,.L127
.L1640:
	mov	d2,#15
	ret
.L127:
	fcall	.cocofun_17
.L1641:
	fcall	.cocofun_3
.L936:
	lea	a6,[a10]4
.L1642:
	mov.aa	a7,a10
	call	VssSM4Calc
.L937:
	jeq	d2,#0,.L129
.L1643:
	mov	d2,#6
	ret
.L129:
	sh	d8,#5
.L938:
	addi	d15,d8,#1152
.L939:
	mov	d8,#0
.L131:
	add	d8,#1
	ld.a	a2,[a15]
.L1644:
	mov	d4,#1
	lea	a4,[a10]4
.L1645:
	mov	d6,#32
	mov	d5,d15
	calli	a2
.L941:
	jeq	d2,#0,.L132
.L1646:
	jlt.u	d8,#3,.L131
.L132:
	mov	d15,#29
.L940:
	sel	d2,d2,d15,#0
	ret
.L828:
	
__SaveSymmKey_function_end:
	.size	SaveSymmKey,__SaveSymmKey_function_end-SaveSymmKey
.L551:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('GetSessKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GetSessKey

; ..\vss_code\vsskeym.c	   464  
; ..\vss_code\vsskeym.c	   465  vss_uint32 GetSessKey(vss_uint32 keyId,  vss_uint32* keyLen, vss_uint8* keyData)
; Function GetSessKey
.L355:
GetSessKey:	.type	func

; ..\vss_code\vsskeym.c	   466  {
; ..\vss_code\vsskeym.c	   467  	if(keyId >= SESS_KEY_NUM)
	jlt.u	d4,#2,.L136
.L1229:

; ..\vss_code\vsskeym.c	   468  		return ERR_INDEX_INVALID;
; ..\vss_code\vsskeym.c	   469  	
; ..\vss_code\vsskeym.c	   470  	return GetSymmKey(keyId + SYMM_KEY_NUM, keyLen, keyData);
; ..\vss_code\vsskeym.c	   471  }
	mov	d2,#15
	ret
.L136:
	add	d4,d4,#14
.L942:
	j	GetSymmKey
.L698:
	
__GetSessKey_function_end:
	.size	GetSessKey,__GetSessKey_function_end-GetSessKey
.L436:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('GetSymmKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GetSymmKey

; ..\vss_code\vsskeym.c	   472  
; ..\vss_code\vsskeym.c	   473  vss_uint32 GetSymmKey(vss_uint32 keyId,  vss_uint32* keyLen, vss_uint8* keyData)
; Function GetSymmKey
.L357:
GetSymmKey:	.type	func
	sub.a	a10,#32
.L943:
	mov.aa	a15,a4
.L946:
	mov.aa	a12,a5
.L947:

; ..\vss_code\vsskeym.c	   474  {
; ..\vss_code\vsskeym.c	   475  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   476  	TSymmKeyItem key;
; ..\vss_code\vsskeym.c	   477  
; ..\vss_code\vsskeym.c	   478  	if (keyData == VSS_NULL || keyLen == VSS_NULL)
	jz.a	a12,.L139
.L1234:
	jnz.a	a15,.L140
.L139:

; ..\vss_code\vsskeym.c	   479  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   480  
; ..\vss_code\vsskeym.c	   481  	if(keyId >= TOTAL_KEY_NUM)
; ..\vss_code\vsskeym.c	   482  		return ERR_INDEX_INVALID;
; ..\vss_code\vsskeym.c	   483  	
; ..\vss_code\vsskeym.c	   484  	ret = LoadSymmKey(keyId, &key);
; ..\vss_code\vsskeym.c	   485  	if(ret)
; ..\vss_code\vsskeym.c	   486  		return ret;
; ..\vss_code\vsskeym.c	   487  	
; ..\vss_code\vsskeym.c	   488  	if(key.cFlag & KEY_FLAG_USED)
; ..\vss_code\vsskeym.c	   489  	{
; ..\vss_code\vsskeym.c	   490  		if(key.cFlag & KEY_FLAG_VALID)
; ..\vss_code\vsskeym.c	   491  		{
; ..\vss_code\vsskeym.c	   492  			*keyLen = key.cKeyLen;
; ..\vss_code\vsskeym.c	   493  			mem_cpy8(keyData, key.value, 16);
; ..\vss_code\vsskeym.c	   494  		}else{
; ..\vss_code\vsskeym.c	   495  			*keyLen  = 16;
; ..\vss_code\vsskeym.c	   496  			mem_cpy8(keyData, vsskeym_SECOC, 16);
; ..\vss_code\vsskeym.c	   497  		}
; ..\vss_code\vsskeym.c	   498  	}else{
; ..\vss_code\vsskeym.c	   499  		ret = ERR_KEY_NOTFOUND;
; ..\vss_code\vsskeym.c	   500  	}
; ..\vss_code\vsskeym.c	   501  
; ..\vss_code\vsskeym.c	   502  	return ret;
; ..\vss_code\vsskeym.c	   503  }
	mov	d2,#4
	ret
.L140:
	mov	d15,#16
.L1235:
	jlt.u	d4,d15,.L142
.L1236:
	mov	d2,#15
	ret
.L142:
	mov.aa	a4,a10
.L945:
	call	LoadSymmKey
.L944:
	mov	d8,d2
.L949:
	jne	d8,#0,.L144
.L1237:
	ld.bu	d15,[a10]
.L1238:
	jz.t	d15:0,.L145
.L1239:
	jz.t	d15:2,.L146
.L1240:
	ld.bu	d15,[a10]1
.L1241:
	st.w	[a15],d15
.L1242:
	lea	a5,[a10]8
.L1243:
	mov	d4,#16
	mov.aa	a4,a12
.L950:
	call	mem_cpy8
.L948:
	j	.L147
.L146:
	mov	d4,#16
	st.w	[a15],d4
.L1244:
	movh.a	a5,#@his(vsskeym_SECOC)
	lea	a5,[a5]@los(vsskeym_SECOC)
.L1245:
	mov.aa	a4,a12
.L952:
	call	mem_cpy8
.L951:
	j	.L148
.L145:
	mov	d8,#5
.L148:
.L147:
.L144:
	mov	d2,d8
	ret
.L702:
	
__GetSymmKey_function_end:
	.size	GetSymmKey,__GetSymmKey_function_end-GetSymmKey
.L441:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SetKeyActive')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SetKeyActive

; ..\vss_code\vsskeym.c	   504  
; ..\vss_code\vsskeym.c	   505  vss_uint32 SetKeyActive(vss_uint32 keyId, vss_uint32 valid)
; Function SetKeyActive
.L359:
SetKeyActive:	.type	func
	mov	e8,d5,d4
	sub.a	a10,#32
.L953:

; ..\vss_code\vsskeym.c	   506  {
; ..\vss_code\vsskeym.c	   507  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   508  	TSymmKeyItem key;
; ..\vss_code\vsskeym.c	   509  	
; ..\vss_code\vsskeym.c	   510  	if (keyId >= TOTAL_KEY_NUM)
	mov	d15,#16
.L955:
	jlt.u	d8,d15,.L150
.L956:

; ..\vss_code\vsskeym.c	   511  		return ERR_INDEX_INVALID;
; ..\vss_code\vsskeym.c	   512  
; ..\vss_code\vsskeym.c	   513  	ret = LoadSymmKey(keyId, &key);
; ..\vss_code\vsskeym.c	   514  	if(ret)
; ..\vss_code\vsskeym.c	   515  		return ret;
; ..\vss_code\vsskeym.c	   516  
; ..\vss_code\vsskeym.c	   517  	if(key.cFlag & KEY_FLAG_USED)
; ..\vss_code\vsskeym.c	   518  	{
; ..\vss_code\vsskeym.c	   519  		if(valid)
; ..\vss_code\vsskeym.c	   520  		{
; ..\vss_code\vsskeym.c	   521  			if(key.cFlag & KEY_FLAG_VALID)
; ..\vss_code\vsskeym.c	   522  			{
; ..\vss_code\vsskeym.c	   523  			}else{
; ..\vss_code\vsskeym.c	   524  				key.cFlag |= KEY_FLAG_VALID;
; ..\vss_code\vsskeym.c	   525  				ret = SetSymmKey(keyId, key.cFlag, key.cKeyLen, key.value);
; ..\vss_code\vsskeym.c	   526  			}
; ..\vss_code\vsskeym.c	   527  		}else{
; ..\vss_code\vsskeym.c	   528  			if(key.cFlag & KEY_FLAG_VALID)
; ..\vss_code\vsskeym.c	   529  			{
; ..\vss_code\vsskeym.c	   530  				key.cFlag &= ~KEY_FLAG_VALID;
; ..\vss_code\vsskeym.c	   531  				ret = SetSymmKey(keyId, key.cFlag, key.cKeyLen, key.value);
; ..\vss_code\vsskeym.c	   532  			}else{
; ..\vss_code\vsskeym.c	   533  			}
; ..\vss_code\vsskeym.c	   534  		}
; ..\vss_code\vsskeym.c	   535  	}else{
; ..\vss_code\vsskeym.c	   536  		return ERR_KEY_INVALID;
; ..\vss_code\vsskeym.c	   537  	}
; ..\vss_code\vsskeym.c	   538  	
; ..\vss_code\vsskeym.c	   539  	return ret;
; ..\vss_code\vsskeym.c	   540  }
	mov	d2,#15
	ret
.L150:
	mov.aa	a4,a10
.L957:
	mov	d4,d8
	call	LoadSymmKey
.L954:
	jne	d2,#0,.L152
.L1250:
	ld.bu	d15,[a10]
.L1251:
	jz.t	d15:0,.L153
.L958:
	jeq	d9,#0,.L154
.L959:
	jnz.t	d15:2,.L155
.L1252:
	or	d5,d15,#4
	st.b	[a10],d5
.L960:
	mov	d4,d8
	lea	a4,[a10]8
.L961:
	ld.bu	d6,[a10]1
.L1253:
	j	SetSymmKey
.L154:
	jz.t	d15:2,.L157
.L1254:
	and	d5,d15,#251
	st.b	[a10],d5
.L962:
	mov	d4,d8
	lea	a4,[a10]8
.L963:
	ld.bu	d6,[a10]1
.L1255:
	j	SetSymmKey
.L153:
	mov	d2,#6
.L157:
.L155:
.L152:
	ret
.L709:
	
__SetKeyActive_function_end:
	.size	SetKeyActive,__SetKeyActive_function_end-SetKeyActive
.L446:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('GetKeyActive')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GetKeyActive

; ..\vss_code\vsskeym.c	   541  
; ..\vss_code\vsskeym.c	   542  vss_uint32 GetKeyActive(vss_uint32 keyId, vss_uint8* valid)
; Function GetKeyActive
.L361:
GetKeyActive:	.type	func
	sub.a	a10,#32
.L964:
	mov.aa	a15,a4
.L967:

; ..\vss_code\vsskeym.c	   543  {
; ..\vss_code\vsskeym.c	   544  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   545  	TSymmKeyItem key;
; ..\vss_code\vsskeym.c	   546  	
; ..\vss_code\vsskeym.c	   547  	if(valid == VSS_NULL)
	jnz.a	a15,.L161
.L1260:

; ..\vss_code\vsskeym.c	   548  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   549  	
; ..\vss_code\vsskeym.c	   550  	if (keyId >= TOTAL_KEY_NUM)
; ..\vss_code\vsskeym.c	   551  		return ERR_INDEX_INVALID;
; ..\vss_code\vsskeym.c	   552  
; ..\vss_code\vsskeym.c	   553  	ret = LoadSymmKey(keyId, &key);
; ..\vss_code\vsskeym.c	   554  	if(ret)
; ..\vss_code\vsskeym.c	   555  		return ret;
; ..\vss_code\vsskeym.c	   556  
; ..\vss_code\vsskeym.c	   557  	if(key.cFlag & KEY_FLAG_USED)
; ..\vss_code\vsskeym.c	   558  	{
; ..\vss_code\vsskeym.c	   559  		if(key.cFlag & KEY_FLAG_VALID)
; ..\vss_code\vsskeym.c	   560  		{
; ..\vss_code\vsskeym.c	   561  			*valid = 1; 
; ..\vss_code\vsskeym.c	   562  		}else{
; ..\vss_code\vsskeym.c	   563  			*valid = 0; 
; ..\vss_code\vsskeym.c	   564  		}
; ..\vss_code\vsskeym.c	   565  	}else{
; ..\vss_code\vsskeym.c	   566  		return ERR_KEY_INVALID;
; ..\vss_code\vsskeym.c	   567  	}
; ..\vss_code\vsskeym.c	   568  	
; ..\vss_code\vsskeym.c	   569  	return ret;
; ..\vss_code\vsskeym.c	   570  }
	mov	d2,#4
	ret
.L161:
	mov	d15,#16
.L1261:
	jlt.u	d4,d15,.L163
.L1262:
	mov	d2,#15
	ret
.L163:
	mov.aa	a4,a10
.L966:
	call	LoadSymmKey
.L965:
	jne	d2,#0,.L165
.L1263:
	ld.bu	d15,[a10]
.L1264:
	jz.t	d15:0,.L166
.L1265:
	jz.t	d15:2,.L167
.L1266:
	mov	d15,#1
	st.b	[a15],d15
.L165:
	ret
.L167:
	mov	d15,#0
	st.b	[a15],d15
.L1267:
	ret
.L166:
	mov	d2,#6
	ret
.L714:
	
__GetKeyActive_function_end:
	.size	GetKeyActive,__GetKeyActive_function_end-GetKeyActive
.L451:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SetSessKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SetSessKey

; ..\vss_code\vsskeym.c	   571  
; ..\vss_code\vsskeym.c	   572  
; ..\vss_code\vsskeym.c	   573  vss_uint32 SetSessKey(vss_uint32 keyId, vss_uint32 keyLen, vss_uint8* keyData)
; Function SetSessKey
.L363:
SetSessKey:	.type	func
	mov	d6,d5
.L970:

; ..\vss_code\vsskeym.c	   574  {
; ..\vss_code\vsskeym.c	   575  	vss_uint32 flag = 0;
; ..\vss_code\vsskeym.c	   576  	
; ..\vss_code\vsskeym.c	   577  	if(keyId >= SESS_KEY_NUM)
	jlt.u	d4,#2,.L172
.L1272:

; ..\vss_code\vsskeym.c	   578  		return ERR_INDEX_INVALID;
; ..\vss_code\vsskeym.c	   579  	
; ..\vss_code\vsskeym.c	   580  	flag = KEY_FLAG_USED | KEY_FLAG_EXPORT | KEY_FLAG_VALID;
; ..\vss_code\vsskeym.c	   581  	return SetSymmKey(keyId + SYMM_KEY_NUM, flag, keyLen, keyData);
; ..\vss_code\vsskeym.c	   582  }
	mov	d2,#15
	ret
.L172:
	add	d4,d4,#14
.L968:
	mov	d5,#7
.L969:
	j	SetSymmKey
.L719:
	
__SetSessKey_function_end:
	.size	SetSessKey,__SetSessKey_function_end-SetSessKey
.L456:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SetSymmKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SetSymmKey

; ..\vss_code\vsskeym.c	   583  
; ..\vss_code\vsskeym.c	   584  vss_uint32 SetSymmKey(vss_uint32 keyId, vss_uint32 flag, vss_uint32 keyLen, vss_uint8* keyData)
; Function SetSymmKey
.L365:
SetSymmKey:	.type	func
	mov	e8,d5,d4
	mov.aa	a15,a4
.L974:
	mov	d10,d6
	sub.a	a10,#56
.L971:

; ..\vss_code\vsskeym.c	   585  {
; ..\vss_code\vsskeym.c	   586  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   587  	vss_uint32 noutlen =0;
	mov	d15,#0
	st.w	[a10],d15
.L1277:

; ..\vss_code\vsskeym.c	   588  	vss_uint8 cv[16];
; ..\vss_code\vsskeym.c	   589  	TSymmKeyItem key;
; ..\vss_code\vsskeym.c	   590  
; ..\vss_code\vsskeym.c	   591  	if(keyData == VSS_NULL)
	jnz.a	a15,.L175
.L1278:

; ..\vss_code\vsskeym.c	   592  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   593  	
; ..\vss_code\vsskeym.c	   594  	if(keyId >= TOTAL_KEY_NUM)
; ..\vss_code\vsskeym.c	   595  		return ERR_INDEX_INVALID;
; ..\vss_code\vsskeym.c	   596  	
; ..\vss_code\vsskeym.c	   597  	if (keyLen != 16 && keyLen != 12 && keyLen != 8 && keyLen != 4 && keyLen != 2)
; ..\vss_code\vsskeym.c	   598  		return ERR_DATA_INVALID;
; ..\vss_code\vsskeym.c	   599  	
; ..\vss_code\vsskeym.c	   600  	mem_set8((vss_uint8*)&key, 0, SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   601  	key.cFlag = flag;
; ..\vss_code\vsskeym.c	   602  	key.cIndex = keyId;
; ..\vss_code\vsskeym.c	   603  	key.cKeyLen = keyLen;
; ..\vss_code\vsskeym.c	   604  	mem_set8(key.value, 0xFF, SYMM_KEY_LEN);
; ..\vss_code\vsskeym.c	   605  	mem_cpy8(key.value, keyData, keyLen);
; ..\vss_code\vsskeym.c	   606  	ret = VssSM4Calc((vss_uint8*)vss_ZERO, 16, key.value, 16, CALC_ENC, PAD_NO_FORCE, cv, &noutlen);
; ..\vss_code\vsskeym.c	   607  	if(ret)
; ..\vss_code\vsskeym.c	   608  		return ERR_KEY_INVALID;
; ..\vss_code\vsskeym.c	   609  	
; ..\vss_code\vsskeym.c	   610  	mem_cpy8(key.cv, cv, 3);
; ..\vss_code\vsskeym.c	   611  	ret = SaveSymmKey(keyId, &key);
; ..\vss_code\vsskeym.c	   612  	if(ret)
; ..\vss_code\vsskeym.c	   613  		return ERR_WRITE_FLASH;
; ..\vss_code\vsskeym.c	   614  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	   615  	if(keyId < SYMM_KEY_NUM)
; ..\vss_code\vsskeym.c	   616  	{
; ..\vss_code\vsskeym.c	   617  		mem_cpy8(g_vssSymmKey[keyId], (vss_uint8*)&key, SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   618  	}else{
; ..\vss_code\vsskeym.c	   619  		mem_cpy8(g_vssSessKey[keyId - SYMM_KEY_NUM], (vss_uint8*)&key, SYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   620  	}
; ..\vss_code\vsskeym.c	   621  #endif
; ..\vss_code\vsskeym.c	   622  	return 0;
; ..\vss_code\vsskeym.c	   623  }
	mov	d2,#4
	ret
.L175:
	mov	d15,#16
.L976:
	jlt.u	d8,d15,.L177
.L977:
	mov	d2,#15
	ret
.L177:
	mov	d15,#16
.L1279:
	jeq	d15,d10,.L179
.L1280:
	mov	d15,#12
.L1281:
	jeq	d15,d10,.L180
.L1282:
	mov	d15,#8
.L1283:
	jeq	d15,d10,.L181
.L1284:
	jeq	d10,#4,.L182
.L1285:
	jeq	d10,#2,.L183
.L1286:
	mov	d2,#11
	ret
.L183:
.L182:
.L181:
.L180:
.L179:
	mov	d4,#0
	lea	a4,[a10]20
.L973:
	mov	d5,#32
	call	mem_set8
.L972:
	st.b	[a10]20,d9
.L978:
	mov	d4,#255
	st.b	[a10]22,d8
.L979:
	mov	d5,#24
	st.b	[a10]21,d10
.L1287:
	lea	a4,[a10]28
.L1288:
	call	mem_set8
.L1289:
	lea	a4,[a10]28
.L1290:
	mov.aa	a5,a15
.L980:
	mov	d4,d10
	call	mem_cpy8
.L981:
	mov	d4,#16
	movh.a	a4,#@his(vss_ZERO)
.L1291:
	mov	d6,#0
	lea	a4,[a4]@los(vss_ZERO)
.L1292:
	mov	d5,d4
	lea	a5,[a10]28
.L1293:
	mov	d7,d6
	lea	a6,[a10]4
.L1294:
	mov.aa	a7,a10
	call	VssSM4Calc
.L982:
	jeq	d2,#0,.L185
.L1295:
	mov	d2,#6
	ret
.L185:
	lea	a4,[a10]24
.L1296:
	lea	a5,[a10]4
.L1297:
	mov	d4,#3
	call	mem_cpy8
.L983:
	lea	a4,[a10]20
.L984:
	mov	d4,d8
	call	SaveSymmKey
.L985:
	jeq	d2,#0,.L187
.L1298:
	mov	d2,#29
	ret
.L187:
	jge.u	d8,#14,.L189
.L987:
	sh	d8,#5
	movh.a	a15,#@his(g_vssSymmKey)
.L975:
	lea	a15,[a15]@los(g_vssSymmKey)
.L1299:
	addsc.a	a4,a15,d8,#0
.L1300:
	j	.L190
.L189:
	sh	d8,#5
	movh.a	a15,#@his(g_vssSessKey)
.L988:
	lea	a15,[a15]@los(g_vssSessKey)
.L1301:
	addsc.a	a15,a15,d8,#0
	lea	a4,[a15]-448
.L190:
	lea	a5,[a10]20
.L1302:
	mov	d4,#32
	call	mem_cpy8
.L986:
	mov	d2,#0
	ret
.L723:
	
__SetSymmKey_function_end:
	.size	SetSymmKey,__SetSymmKey_function_end-SetSymmKey
.L461:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('GenAsymmKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GenAsymmKey

; ..\vss_code\vsskeym.c	   624  
; ..\vss_code\vsskeym.c	   625  vss_uint32 GenAsymmKey(vss_uint8* x, vss_uint8* y)
; Function GenAsymmKey
.L367:
GenAsymmKey:	.type	func
	sub.a	a10,#248
.L989:
	mov.aa	a12,a4
.L992:
	mov.aa	a13,a5
.L993:

; ..\vss_code\vsskeym.c	   626  {
; ..\vss_code\vsskeym.c	   627  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   628  	vss_uint8 buf[ASYMM_CIPHER_LEN];
; ..\vss_code\vsskeym.c	   629  	vss_uint8 mac[16];
; ..\vss_code\vsskeym.c	   630  	vss_uint32 offset = 0;
; ..\vss_code\vsskeym.c	   631  	vss_uint32 noutlen =0;
	mov	d15,#0
	st.w	[a10]128,d15
.L1307:

; ..\vss_code\vsskeym.c	   632  	TAsymmKeyItem keyData;
; ..\vss_code\vsskeym.c	   633  
; ..\vss_code\vsskeym.c	   634  	if(x == VSS_NULL || y == VSS_NULL)
	jz.a	a12,.L192
.L1308:
	jnz.a	a13,.L193
.L192:

; ..\vss_code\vsskeym.c	   635  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   636  
; ..\vss_code\vsskeym.c	   637  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   638  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   639  	
; ..\vss_code\vsskeym.c	   640  	mem_set8((vss_uint8*)&keyData, 0, ASYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   641  	keyData.cAlg = g_cVssAlg;
; ..\vss_code\vsskeym.c	   642  	keyData.cUseFlag = 1;
; ..\vss_code\vsskeym.c	   643  	keyData.cXLen = 32;
; ..\vss_code\vsskeym.c	   644  	keyData.cYLen = 32;
; ..\vss_code\vsskeym.c	   645  	keyData.cSKLen = 32;
; ..\vss_code\vsskeym.c	   646  
; ..\vss_code\vsskeym.c	   647  	if(g_cVssAlg == ALG_GJ)
; ..\vss_code\vsskeym.c	   648  	{
; ..\vss_code\vsskeym.c	   649  		ret = VssECCGenKey(keyData.value, keyData.value + 32, keyData.value + 64);
; ..\vss_code\vsskeym.c	   650  	}else if(g_cVssAlg == ALG_GM)
; ..\vss_code\vsskeym.c	   651  	{
; ..\vss_code\vsskeym.c	   652  		ret = VssSM2GenKey(keyData.value, keyData.value + 32, keyData.value + 64);
; ..\vss_code\vsskeym.c	   653  	}else{
; ..\vss_code\vsskeym.c	   654  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   655  	}
; ..\vss_code\vsskeym.c	   656  	if(ret)
; ..\vss_code\vsskeym.c	   657  		return ret;
; ..\vss_code\vsskeym.c	   658  
; ..\vss_code\vsskeym.c	   659  	ret = VssSM4Mac(keyData.value, ASYMM_KEY_LEN, (vss_uint8*)vsskeym_KEK, 16, mac);
; ..\vss_code\vsskeym.c	   660  	if(ret)
; ..\vss_code\vsskeym.c	   661  		return ret;
; ..\vss_code\vsskeym.c	   662  
; ..\vss_code\vsskeym.c	   663  	mem_cpy8(keyData.mac, mac, ASYMM_KEY_MAC);
; ..\vss_code\vsskeym.c	   664  
; ..\vss_code\vsskeym.c	   665  	ret = VssSM4Calc((vss_uint8*)&keyData,ASYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
; ..\vss_code\vsskeym.c	   666  	if(ret || noutlen != ASYMM_CIPHER_LEN)
; ..\vss_code\vsskeym.c	   667  		return ERR_CALC_FAIL;
; ..\vss_code\vsskeym.c	   668  
; ..\vss_code\vsskeym.c	   669  	offset = FLASH_ASYMMKEY_OFFSET;
; ..\vss_code\vsskeym.c	   670  	ret = g_vssIocb(FLASH_IO_WRITE, offset, buf, ASYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   671  	if (ret)
; ..\vss_code\vsskeym.c	   672  	{
; ..\vss_code\vsskeym.c	   673  		return ERR_WRITE_FLASH;
; ..\vss_code\vsskeym.c	   674  	}
; ..\vss_code\vsskeym.c	   675  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	   676  	mem_cpy8(g_vssAsymmKey, keyData.value, ASYMM_KEY_LEN);
; ..\vss_code\vsskeym.c	   677  #endif
; ..\vss_code\vsskeym.c	   678  	mem_cpy8(x, keyData.value, 32);
; ..\vss_code\vsskeym.c	   679  	mem_cpy8(y, keyData.value + 32, 32);
; ..\vss_code\vsskeym.c	   680  
; ..\vss_code\vsskeym.c	   681  	return 0;
; ..\vss_code\vsskeym.c	   682  }
	mov	d2,#4
	ret
.L193:
	movh.a	a14,#@his(g_vssIocb)
	lea	a14,[a14]@los(g_vssIocb)
	ld.w	d15,[a14]
.L1309:
	jeq	d15,#0,.L195
.L1310:
	mov	d4,#0
	lea	a4,[a10]132
.L991:
	mov	d5,#112
	call	mem_set8
.L990:
	fcall	.cocofun_12
.L996:
	st.b	[a10]134,d15
.L1311:
	mov	d0,#1
	st.b	[a10]132,d0
.L1312:
	mov	d0,#32
	st.b	[a10]135,d0
.L1313:
	st.b	[a10]136,d0
.L1314:
	st.b	[a10]137,d0
.L1315:
	jne	d15,#1,.L196
.L1316:
	lea	a4,[a10]140
.L1317:
	lea	a5,[a10]172
.L1318:
	lea	a6,[a10]204
	call	VssECCGenKey
.L994:
	j	.L197
.L196:
	jne	d15,#2,.L198
.L1319:
	lea	a4,[a10]140
.L1320:
	lea	a5,[a10]172
.L1321:
	lea	a6,[a10]204
	call	VssSM2GenKey
.L995:
	j	.L199
.L198:
.L195:
	mov	d2,#27
	ret
.L199:
.L197:
	jne	d2,#0,.L203
.L1322:
	fcall	.cocofun_6
.L1323:
	lea	a4,[a10]140
.L1324:
	fcall	.cocofun_5
.L997:
	lea	a6,[a10]112
.L998:
	mov.aa	a5,a15
	call	VssSM4Mac
.L1325:
	jne	d2,#0,.L203
.L1326:
	lea	a4,[a10]236
.L1327:
	lea	a5,[a10]112
.L1328:
	mov	d4,#8
	call	mem_cpy8
.L999:
	lea	a4,[a10]132
.L1329:
	mov	d4,#112
	fcall	.cocofun_3
.L1330:
	mov.aa	a6,a10
.L1331:
	lea	a7,[a10]128
	mov.aa	a5,a15
	call	VssSM4Calc
.L1001:
	jne	d2,#0,.L204
.L1332:
	ld.w	d15,[a10]128
.L1333:
	mov	d0,#112
.L1334:
	jeq	d15,d0,.L205
.L204:
	mov	d2,#8
.L203:
	ret
.L205:
	ld.a	a15,[a14]
.L1335:
	mov	d4,#1
.L1336:
	mov	d5,#1664
	mov.aa	a4,a10
.L1337:
	mov	d6,#112
	calli	a15
.L1338:
	jeq	d2,#0,.L207
.L1339:
	mov	d2,#29
	ret
.L207:
	fcall	.cocofun_18
.L1002:
	lea	a5,[a10]140
.L1340:
	mov	d4,#96
	call	mem_cpy8
.L1003:
	lea	a5,[a10]140
.L1341:
	mov	d4,#32
	mov.aa	a4,a12
.L1004:
	call	mem_cpy8
.L1005:
	lea	a5,[a10]172
.L1342:
	mov	d4,#32
	mov.aa	a4,a13
.L1006:
	call	mem_cpy8
.L1007:
	mov	d2,#0
	ret
.L732:
	
__GenAsymmKey_function_end:
	.size	GenAsymmKey,__GenAsymmKey_function_end-GenAsymmKey
.L466:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_18')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_18
.L369:
.cocofun_18:	.type	func
; Function body .cocofun_18, coco_iter:1
	movh.a	a4,#@his(g_vssAsymmKey)
	lea	a4,[a4]@los(g_vssAsymmKey)
.L1739:
	fret
.L641:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_5')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_5
.L371:
.cocofun_5:	.type	func
; Function body .cocofun_5, coco_iter:0
	mov	d4,#96
.L1672:
	mov	d5,#16
	fret
.L576:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('LoadAsymmKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	LoadAsymmKey

; ..\vss_code\vsskeym.c	   683  
; ..\vss_code\vsskeym.c	   684  vss_uint32 LoadAsymmKey(vss_uint8* x, vss_uint8* y, vss_uint8* sk)
; Function LoadAsymmKey
.L373:
LoadAsymmKey:	.type	func
	sub.a	a10,#248
.L1008:
	mov.aa	a12,a4
.L1020:
	mov.aa	a13,a5
.L1021:
	st.a	[a10]244,a6
.L1019:

; ..\vss_code\vsskeym.c	   685  {
; ..\vss_code\vsskeym.c	   686  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   687  	vss_uint8 buf[ASYMM_CIPHER_LEN];
; ..\vss_code\vsskeym.c	   688  	vss_uint8 mac[16];
; ..\vss_code\vsskeym.c	   689  	vss_uint32 offset = 0;
; ..\vss_code\vsskeym.c	   690  	vss_uint32 noutlen =0;
	mov	d15,#0
	st.w	[a10]128,d15
.L1347:

; ..\vss_code\vsskeym.c	   691  	TAsymmKeyItem keyData;
; ..\vss_code\vsskeym.c	   692  
; ..\vss_code\vsskeym.c	   693  	if(x == VSS_NULL || y == VSS_NULL || sk == VSS_NULL)
	jz.a	a12,.L210
.L1348:
	jz.a	a13,.L211
.L1349:
	mov.aa	a15,a6
.L1012:
	jnz.a	a15,.L212
.L211:
.L210:

; ..\vss_code\vsskeym.c	   694  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   695  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	   696  	if(mem_cmp8(g_vssAsymmKey, mac, 16))
; ..\vss_code\vsskeym.c	   697  	{
; ..\vss_code\vsskeym.c	   698  		mem_cpy8(x, g_vssAsymmKey, 32);
; ..\vss_code\vsskeym.c	   699  		mem_cpy8(y, g_vssAsymmKey + 32, 32);
; ..\vss_code\vsskeym.c	   700  		mem_cpy8(sk, g_vssAsymmKey + 64, 32);
; ..\vss_code\vsskeym.c	   701  
; ..\vss_code\vsskeym.c	   702  		return 0;
; ..\vss_code\vsskeym.c	   703  	}
; ..\vss_code\vsskeym.c	   704  #endif
; ..\vss_code\vsskeym.c	   705  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   706  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   707  
; ..\vss_code\vsskeym.c	   708  	offset = FLASH_ASYMMKEY_OFFSET;
; ..\vss_code\vsskeym.c	   709  	ret = g_vssIocb(FLASH_IO_READ, offset, buf, ASYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   710  	if (ret)
; ..\vss_code\vsskeym.c	   711  	{
; ..\vss_code\vsskeym.c	   712  		return ERR_READ_FLASH;
; ..\vss_code\vsskeym.c	   713  	}
; ..\vss_code\vsskeym.c	   714  	
; ..\vss_code\vsskeym.c	   715  	ret = VssSM4Calc(buf, ASYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_FORCE, (vss_uint8*)&keyData, &noutlen);
; ..\vss_code\vsskeym.c	   716  	if(ret || noutlen != ASYMM_CIPHER_LEN)
; ..\vss_code\vsskeym.c	   717  		return ERR_CALC_FAIL;
; ..\vss_code\vsskeym.c	   718  
; ..\vss_code\vsskeym.c	   719  	if(keyData.cUseFlag == 0)
; ..\vss_code\vsskeym.c	   720  		return ERR_KEY_NOTFOUND;
; ..\vss_code\vsskeym.c	   721  		
; ..\vss_code\vsskeym.c	   722  	ret = VssSM4Mac(keyData.value, ASYMM_KEY_LEN, (vss_uint8*)vsskeym_KEK, 16, mac);
; ..\vss_code\vsskeym.c	   723  	if(ret || mem_cmp8(keyData.mac, mac, ASYMM_KEY_MAC))
; ..\vss_code\vsskeym.c	   724  		return ERR_DATA_VERIFY;
; ..\vss_code\vsskeym.c	   725  	
; ..\vss_code\vsskeym.c	   726  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	   727  	mem_cpy8(g_vssAsymmKey, keyData.value, ASYMM_KEY_LEN);
; ..\vss_code\vsskeym.c	   728  #endif
; ..\vss_code\vsskeym.c	   729  	mem_cpy8(x, keyData.value, 32);
; ..\vss_code\vsskeym.c	   730  	mem_cpy8(y, keyData.value + 32, 32);
; ..\vss_code\vsskeym.c	   731  	mem_cpy8(sk, keyData.value + 64, 32);
; ..\vss_code\vsskeym.c	   732  
; ..\vss_code\vsskeym.c	   733  	return 0;
; ..\vss_code\vsskeym.c	   734  }
	mov	d2,#4
	ret
.L212:
	movh.a	a14,#@his(g_vssAsymmKey)
	lea	a14,[a14]@los(g_vssAsymmKey)
.L1350:
	lea	a5,[a10]112
.L1011:
	mov	d4,#16
	mov.aa	a4,a14
.L1010:
	call	mem_cmp8
.L1009:
	jeq	d2,#0,.L214
.L1351:
	mov	d4,#32
	mov.aa	a4,a12
.L1014:
	mov.aa	a5,a14
	call	mem_cpy8
.L1015:
	lea	a5,[a14]32
.L1352:
	mov	d4,#32
	mov.aa	a4,a13
.L1016:
	call	mem_cpy8
.L1017:
	lea	a5,[a14]64
.L1353:
	j	.L215
.L214:
	fcall	.cocofun_1
.L1013:
	jnz.a	a15,.L216
.L1354:
	mov	d2,#27
	ret
.L216:
	mov	d4,#0
.L1355:
	mov	d5,#1664
	mov.aa	a4,a10
.L1356:
	mov	d6,#112
	calli	a15
.L1022:
	jeq	d2,#0,.L218
.L1357:
	mov	d2,#28
	ret
.L218:
	fcall	.cocofun_6
.L1023:
	mov	d6,#1
	mov.aa	a5,a15
.L1358:
	mov	d4,#112
	mov.aa	a4,a10
.L1359:
	mov	d5,#16
	lea	a6,[a10]132
.L1360:
	mov	d7,d6
	lea	a7,[a10]128
	call	VssSM4Calc
.L1361:
	jne	d2,#0,.L220
.L1362:
	ld.w	d15,[a10]128
.L1363:
	mov	d0,#112
.L1364:
	jeq	d15,d0,.L221
.L220:
	mov	d2,#8
	ret
.L221:
	ld.bu	d15,[a10]132
.L1365:
	jne	d15,#0,.L223
.L1366:
	mov	d2,#5
	ret
.L223:
	lea	a4,[a10]140
.L1367:
	fcall	.cocofun_5
.L1368:
	lea	a6,[a10]112
	mov.aa	a5,a15
	call	VssSM4Mac
.L1369:
	jne	d2,#0,.L225
.L1370:
	lea	a4,[a10]236
.L1371:
	lea	a5,[a10]112
.L1372:
	mov	d4,#8
	call	mem_cmp8
.L1024:
	jeq	d2,#0,.L226
.L225:
	mov	d2,#34
	ret
.L226:
	lea	a5,[a10]140
.L1373:
	mov	d4,#96
	mov.aa	a4,a14
	call	mem_cpy8
.L1374:
	lea	a5,[a10]140
.L1375:
	mov	d4,#32
	mov.aa	a4,a12
.L1025:
	call	mem_cpy8
.L1026:
	lea	a5,[a10]172
.L1376:
	mov	d4,#32
	mov.aa	a4,a13
.L1027:
	call	mem_cpy8
.L1028:
	lea	a5,[a10]204
.L215:
	mov	d4,#32
	ld.a	a4,[a10]244
.L1029:
	call	mem_cpy8
.L1030:
	mov	d2,#0
	ret
.L743:
	
__LoadAsymmKey_function_end:
	.size	LoadAsymmKey,__LoadAsymmKey_function_end-LoadAsymmKey
.L471:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SaveAsymmKey')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SaveAsymmKey

; ..\vss_code\vsskeym.c	   735  
; ..\vss_code\vsskeym.c	   736  vss_uint32 SaveAsymmKey(vss_uint8* x, vss_uint8* y, vss_uint8* sk)
; Function SaveAsymmKey
.L375:
SaveAsymmKey:	.type	func
	sub.a	a10,#248
.L1031:
	mov.aa	a15,a4
.L1034:
	mov.aa	a12,a5
.L1045:
	mov.aa	a13,a6
.L1044:

; ..\vss_code\vsskeym.c	   737  {
; ..\vss_code\vsskeym.c	   738  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   739  	vss_uint8 buf[ASYMM_CIPHER_LEN];
; ..\vss_code\vsskeym.c	   740  	vss_uint8 mac[16];
; ..\vss_code\vsskeym.c	   741  	vss_uint32 offset = 0;
; ..\vss_code\vsskeym.c	   742  	vss_uint32 noutlen =0;
	mov	d15,#0
	st.w	[a10]128,d15
.L1381:

; ..\vss_code\vsskeym.c	   743  	TAsymmKeyItem keyData;
; ..\vss_code\vsskeym.c	   744  
; ..\vss_code\vsskeym.c	   745  	if(x == VSS_NULL || y == VSS_NULL || sk == VSS_NULL)
	jz.a	a15,.L229
.L1382:
	jz.a	a12,.L230
.L1383:
	jnz.a	a13,.L231
.L230:
.L229:

; ..\vss_code\vsskeym.c	   746  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   747  
; ..\vss_code\vsskeym.c	   748  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   749  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   750  
; ..\vss_code\vsskeym.c	   751  	mem_set8((vss_uint8*)&keyData, 0, ASYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   752  	mem_cpy8(keyData.value, x, 32);
; ..\vss_code\vsskeym.c	   753  	mem_cpy8(keyData.value + 32, y, 32);
; ..\vss_code\vsskeym.c	   754  	mem_cpy8(keyData.value + 64, sk, 32);
; ..\vss_code\vsskeym.c	   755  
; ..\vss_code\vsskeym.c	   756  	keyData.cAlg = g_cVssAlg;
; ..\vss_code\vsskeym.c	   757  	keyData.cUseFlag = 1;
; ..\vss_code\vsskeym.c	   758  	keyData.cXLen = 32;
; ..\vss_code\vsskeym.c	   759  	keyData.cYLen = 32;
; ..\vss_code\vsskeym.c	   760  	keyData.cSKLen = 32;
; ..\vss_code\vsskeym.c	   761  		
; ..\vss_code\vsskeym.c	   762  	ret = VssSM4Mac(keyData.value, ASYMM_KEY_LEN, (vss_uint8*)vsskeym_KEK, 16, mac);
; ..\vss_code\vsskeym.c	   763  	if(ret)
; ..\vss_code\vsskeym.c	   764  		return ERR_DATA_INVALID;
; ..\vss_code\vsskeym.c	   765  	mem_cpy8(keyData.mac, mac, ASYMM_KEY_MAC);
; ..\vss_code\vsskeym.c	   766  	
; ..\vss_code\vsskeym.c	   767  	ret = VssSM4Calc((vss_uint8*)&keyData, ASYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
; ..\vss_code\vsskeym.c	   768  	if(ret || noutlen != ASYMM_CIPHER_LEN)
; ..\vss_code\vsskeym.c	   769  		return ERR_CALC_FAIL;
; ..\vss_code\vsskeym.c	   770  
; ..\vss_code\vsskeym.c	   771  	offset = FLASH_ASYMMKEY_OFFSET;
; ..\vss_code\vsskeym.c	   772  	ret = g_vssIocb(FLASH_IO_WRITE, offset, buf, ASYMM_CIPHER_LEN);
; ..\vss_code\vsskeym.c	   773  	if (ret)
; ..\vss_code\vsskeym.c	   774  	{
; ..\vss_code\vsskeym.c	   775  		return ERR_READ_FLASH;
; ..\vss_code\vsskeym.c	   776  	}
; ..\vss_code\vsskeym.c	   777  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))	
; ..\vss_code\vsskeym.c	   778  	mem_cpy8(g_vssAsymmKey, keyData.value, ASYMM_KEY_LEN);
; ..\vss_code\vsskeym.c	   779  #endif
; ..\vss_code\vsskeym.c	   780  	return 0;
; ..\vss_code\vsskeym.c	   781  }
	mov	d2,#4
	ret
.L231:
	movh.a	a14,#@his(g_vssIocb)
	lea	a14,[a14]@los(g_vssIocb)
	ld.w	d15,[a14]
.L1384:
	jne	d15,#0,.L233
.L1385:
	mov	d2,#27
	ret
.L233:
	mov	d4,#0
	lea	a4,[a10]132
.L1033:
	mov	d5,#112
	call	mem_set8
.L1032:
	lea	a4,[a10]140
.L1386:
	mov	d4,#32
	mov.aa	a5,a15
.L1036:
	call	mem_cpy8
.L1037:
	lea	a4,[a10]172
.L1387:
	mov	d4,#32
	mov.aa	a5,a12
.L1038:
	call	mem_cpy8
.L1039:
	lea	a4,[a10]204
.L1388:
	mov	d4,#32
	mov.aa	a5,a13
.L1040:
	call	mem_cpy8
.L1389:
	fcall	.cocofun_12
.L1035:
	st.b	[a10]134,d15
.L1390:
	mov	d15,#1
	st.b	[a10]132,d15
.L1391:
	mov	d15,#32
	st.b	[a10]135,d15
.L1392:
	st.b	[a10]136,d15
.L1393:
	st.b	[a10]137,d15
.L1394:
	fcall	.cocofun_6
.L1395:
	lea	a4,[a10]140
.L1396:
	fcall	.cocofun_5
.L1397:
	lea	a6,[a10]112
	mov.aa	a5,a15
	call	VssSM4Mac
.L1042:
	jeq	d2,#0,.L235
.L1398:
	mov	d2,#11
	ret
.L235:
	lea	a4,[a10]236
.L1399:
	lea	a5,[a10]112
.L1400:
	mov	d4,#8
	call	mem_cpy8
.L1043:
	lea	a4,[a10]132
.L1401:
	mov	d4,#112
	fcall	.cocofun_3
.L1402:
	mov.aa	a6,a10
.L1403:
	lea	a7,[a10]128
	mov.aa	a5,a15
	call	VssSM4Calc
.L1046:
	jne	d2,#0,.L237
.L1404:
	ld.w	d15,[a10]128
.L1405:
	mov	d0,#112
.L1406:
	jeq	d15,d0,.L238
.L237:
	mov	d2,#8
	ret
.L238:
	ld.a	a15,[a14]
.L1407:
	mov	d4,#1
.L1408:
	mov	d5,#1664
	mov.aa	a4,a10
.L1409:
	mov	d6,#112
	calli	a15
.L1410:
	jeq	d2,#0,.L240
.L1411:
	mov	d2,#28
	ret
.L240:
	fcall	.cocofun_18
.L1047:
	lea	a5,[a10]140
.L1412:
	mov	d4,#96
	call	mem_cpy8
.L1048:
	mov	d2,#0
	ret
.L752:
	
__SaveAsymmKey_function_end:
	.size	SaveAsymmKey,__SaveAsymmKey_function_end-SaveAsymmKey
.L476:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('LoadCFGData')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	LoadCFGData

; ..\vss_code\vsskeym.c	   782  
; ..\vss_code\vsskeym.c	   783  vss_uint32 LoadCFGData(vss_uint8* cfgData)
; Function LoadCFGData
.L377:
LoadCFGData:	.type	func
	sub.a	a10,#136
.L1049:
	mov.aa	a15,a4
.L1051:

; ..\vss_code\vsskeym.c	   784  {
; ..\vss_code\vsskeym.c	   785  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   786  	vss_uint8 tmp[FLASH_CFG_SIZE];
; ..\vss_code\vsskeym.c	   787  	vss_uint8 mac[16];
; ..\vss_code\vsskeym.c	   788  	vss_uint32 outlen = 0;
	mov	d15,#0
	st.w	[a10]128,d15
.L1050:

; ..\vss_code\vsskeym.c	   789  
; ..\vss_code\vsskeym.c	   790  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_13
.L1052:
	jnz.a	a2,.L243
.L1417:

; ..\vss_code\vsskeym.c	   791  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   792  	
; ..\vss_code\vsskeym.c	   793  	ret = g_vssIocb(FLASH_IO_READ, FLASH_CFG_OFFSET, tmp, FLASH_CFG_SIZE);
; ..\vss_code\vsskeym.c	   794  	if (ret)
; ..\vss_code\vsskeym.c	   795  	{
; ..\vss_code\vsskeym.c	   796  		return ERR_READ_FLASH;
; ..\vss_code\vsskeym.c	   797  	}
; ..\vss_code\vsskeym.c	   798  	
; ..\vss_code\vsskeym.c	   799  	ret = VssSM4Mac(tmp, FLASH_CFG_DATA, (vss_uint8*)vsskeym_KEK, 16, mac);
; ..\vss_code\vsskeym.c	   800  	if(ret || mem_cmp8(mac, tmp + FLASH_CFG_DATA, 16))
; ..\vss_code\vsskeym.c	   801  	{
; ..\vss_code\vsskeym.c	   802  		return ERR_DATA_VERIFY;
; ..\vss_code\vsskeym.c	   803  	}
; ..\vss_code\vsskeym.c	   804  	
; ..\vss_code\vsskeym.c	   805  	ret = VssSM4Calc(tmp, FLASH_CFG_DATA, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_NO_FORCE, cfgData, &outlen);
; ..\vss_code\vsskeym.c	   806  	if (ret)
; ..\vss_code\vsskeym.c	   807  	{
; ..\vss_code\vsskeym.c	   808  		return ERR_READ_CFG;
; ..\vss_code\vsskeym.c	   809  	}
; ..\vss_code\vsskeym.c	   810  
; ..\vss_code\vsskeym.c	   811  	return 0;
; ..\vss_code\vsskeym.c	   812  }
	mov	d2,#27
	ret
.L243:
	mov	d4,#0
.L1418:
	mov	d5,d4
	mov.aa	a4,a10
.L1053:
	mov	d6,#112
	calli	a2
.L1054:
	jeq	d2,#0,.L245
.L1419:
	mov	d2,#28
	ret
.L245:
	movh.a	a12,#@his(vsskeym_KEK)
	lea	a12,[a12]@los(vsskeym_KEK)
.L1420:
	mov.aa	a4,a10
.L1421:
	fcall	.cocofun_5
.L1422:
	lea	a6,[a10]112
	mov.aa	a5,a12
	call	VssSM4Mac
.L1423:
	jne	d2,#0,.L247
.L1424:
	lea	a4,[a10]112
.L1425:
	lea	a5,[a10]96
.L1426:
	mov	d4,#16
	call	mem_cmp8
.L1055:
	jeq	d2,#0,.L248
.L247:
	mov	d2,#34
	ret
.L248:
	mov.aa	a4,a10
.L1427:
	fcall	.cocofun_5
.L1428:
	mov	d6,#1
	lea	a7,[a10]128
.L1429:
	mov	d7,#0
	mov.aa	a5,a12
	mov.aa	a6,a15
.L1056:
	call	VssSM4Calc
.L1057:
	mov	d15,#31
.L1430:
	sel	d2,d2,d15,#0
	ret
.L761:
	
__LoadCFGData_function_end:
	.size	LoadCFGData,__LoadCFGData_function_end-LoadCFGData
.L481:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_13')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_13
.L379:
.cocofun_13:	.type	func
; Function body .cocofun_13, coco_iter:0
	movh.a	a2,#@his(g_vssIocb)
	ld.a	a2,[a2]@los(g_vssIocb)
.L1714:
	fret
.L616:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('SaveCFGData')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SaveCFGData

; ..\vss_code\vsskeym.c	   813  
; ..\vss_code\vsskeym.c	   814  vss_uint32 SaveCFGData(vss_uint8* cfgData)
; Function SaveCFGData
.L381:
SaveCFGData:	.type	func

; ..\vss_code\vsskeym.c	   815  {
; ..\vss_code\vsskeym.c	   816  #if 0
; ..\vss_code\vsskeym.c	   817  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   818  	vss_uint8 tmp[FLASH_CFG_SIZE] = {0};
; ..\vss_code\vsskeym.c	   819  	vss_uint32 outlen = 0;
; ..\vss_code\vsskeym.c	   820  	vss_uint8 mac[16]={0};
; ..\vss_code\vsskeym.c	   821  
; ..\vss_code\vsskeym.c	   822  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   823  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   824  	
; ..\vss_code\vsskeym.c	   825  	ret = VssSM4Calc(cfgData, FLASH_CFG_DATA, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, tmp, &outlen);
; ..\vss_code\vsskeym.c	   826  	if (ret)
; ..\vss_code\vsskeym.c	   827  	{
; ..\vss_code\vsskeym.c	   828  		return ERR_CALC_FAIL;
; ..\vss_code\vsskeym.c	   829  	}
; ..\vss_code\vsskeym.c	   830  	
; ..\vss_code\vsskeym.c	   831  	ret = VssSM4Mac(tmp, FLASH_CFG_DATA, (vss_uint8*)vsskeym_KEK, 16, tmp + FLASH_CFG_DATA);
; ..\vss_code\vsskeym.c	   832  	if (ret)
; ..\vss_code\vsskeym.c	   833  	{
; ..\vss_code\vsskeym.c	   834  		return ERR_CALC_FAIL;
; ..\vss_code\vsskeym.c	   835  	}
; ..\vss_code\vsskeym.c	   836  	
; ..\vss_code\vsskeym.c	   837  	ret = g_vssIocb(FLASH_IO_WRITE, FLASH_CFG_OFFSET, tmp, FLASH_CFG_SIZE);
; ..\vss_code\vsskeym.c	   838  	if (ret)
; ..\vss_code\vsskeym.c	   839  	{
; ..\vss_code\vsskeym.c	   840  		return ERR_WRITE_FLASH;
; ..\vss_code\vsskeym.c	   841  	}
; ..\vss_code\vsskeym.c	   842  	
; ..\vss_code\vsskeym.c	   843  	return 0;
; ..\vss_code\vsskeym.c	   844  #else
; ..\vss_code\vsskeym.c	   845  	return ERR_NOT_SUPPORT;
; ..\vss_code\vsskeym.c	   846  #endif
; ..\vss_code\vsskeym.c	   847  }
	mov	d2,#24
	ret
.L768:
	
__SaveCFGData_function_end:
	.size	SaveCFGData,__SaveCFGData_function_end-SaveCFGData
.L486:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SaveKeyCode')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SaveKeyCode

; ..\vss_code\vsskeym.c	   848  
; ..\vss_code\vsskeym.c	   849  vss_uint32 SaveKeyCode(vss_uint32 len ,vss_uint8* code)
; Function SaveKeyCode
.L383:
SaveKeyCode:	.type	func
	sub.a	a10,#40
.L1058:

; ..\vss_code\vsskeym.c	   850  {
; ..\vss_code\vsskeym.c	   851  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   852  	vss_uint8 tmp[32];
; ..\vss_code\vsskeym.c	   853  	vss_uint32 outlen = 0;
	mov	d15,#0
	st.w	[a10]32,d15
.L1059:

; ..\vss_code\vsskeym.c	   854  
; ..\vss_code\vsskeym.c	   855  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_7
.L1060:
	jne	d15,#0,.L254
.L1463:

; ..\vss_code\vsskeym.c	   856  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   857  	
; ..\vss_code\vsskeym.c	   858  	ret = VssSM4Calc(code, len, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, tmp, &outlen);
; ..\vss_code\vsskeym.c	   859  	if (ret)
; ..\vss_code\vsskeym.c	   860  	{
; ..\vss_code\vsskeym.c	   861  		return ERR_CALC_FAIL;
; ..\vss_code\vsskeym.c	   862  	}
; ..\vss_code\vsskeym.c	   863  
; ..\vss_code\vsskeym.c	   864  	if(outlen != FLASH_KEYCODE_SIZE)
; ..\vss_code\vsskeym.c	   865  	{
; ..\vss_code\vsskeym.c	   866  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   867  	}
; ..\vss_code\vsskeym.c	   868  
; ..\vss_code\vsskeym.c	   869  	ret = g_vssIocb(FLASH_IO_WRITE, FLASH_KEYCODE_OFFSET, tmp, FLASH_KEYCODE_SIZE);
; ..\vss_code\vsskeym.c	   870  	if (ret)
; ..\vss_code\vsskeym.c	   871  	{
; ..\vss_code\vsskeym.c	   872  		return ERR_WRITE_FLASH;
; ..\vss_code\vsskeym.c	   873  	}	
; ..\vss_code\vsskeym.c	   874  
; ..\vss_code\vsskeym.c	   875  	return 0;
; ..\vss_code\vsskeym.c	   876  }
	mov	d2,#27
	ret
.L254:
	movh.a	a5,#@his(vsskeym_KEK)
	lea	a5,[a5]@los(vsskeym_KEK)
.L1464:
	fcall	.cocofun_3
.L1465:
	mov.aa	a6,a10
.L1466:
	lea	a7,[a10]32
	call	VssSM4Calc
.L1061:
	jeq	d2,#0,.L256
.L1467:
	mov	d2,#8
	ret
.L256:
	ld.w	d15,[a10]32
.L1468:
	mov	d0,#32
.L1469:
	jeq	d15,d0,.L258
.L1470:
	mov	d2,#4
	ret
.L258:
	ld.a	a15,[a15]
.L1471:
	mov	d4,#1
.L1472:
	mov	d5,#1088
	mov.aa	a4,a10
.L1473:
	mov	d6,#32
	calli	a15
.L1474:
	mov	d15,#29
.L1475:
	sel	d2,d2,d15,#0
	ret
.L783:
	
__SaveKeyCode_function_end:
	.size	SaveKeyCode,__SaveKeyCode_function_end-SaveKeyCode
.L501:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('LoadKeyCode')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	LoadKeyCode

; ..\vss_code\vsskeym.c	   877  
; ..\vss_code\vsskeym.c	   878  vss_uint32 LoadKeyCode(vss_uint32* len ,vss_uint8* code)
; Function LoadKeyCode
.L385:
LoadKeyCode:	.type	func
	sub.a	a10,#32
.L1062:
	mov.aa	a15,a4
.L1066:
	mov.aa	a12,a5
.L1065:

; ..\vss_code\vsskeym.c	   879  {
; ..\vss_code\vsskeym.c	   880  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   881  	vss_uint8 tmp[32];
; ..\vss_code\vsskeym.c	   882  	vss_uint32 outlen = 0;
; ..\vss_code\vsskeym.c	   883  
; ..\vss_code\vsskeym.c	   884  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_13
.L1480:
	jnz.a	a2,.L263
.L1481:

; ..\vss_code\vsskeym.c	   885  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   886  	
; ..\vss_code\vsskeym.c	   887  	ret = g_vssIocb(FLASH_IO_READ, FLASH_KEYCODE_OFFSET, tmp, FLASH_KEYCODE_SIZE);
; ..\vss_code\vsskeym.c	   888  	if (ret)
; ..\vss_code\vsskeym.c	   889  	{
; ..\vss_code\vsskeym.c	   890  		return ERR_READ_FLASH;
; ..\vss_code\vsskeym.c	   891  	}
; ..\vss_code\vsskeym.c	   892  
; ..\vss_code\vsskeym.c	   893  	ret = VssSM4Calc(tmp, FLASH_KEYCODE_SIZE, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_NO_FORCE, code, len);
; ..\vss_code\vsskeym.c	   894  	if (ret)
; ..\vss_code\vsskeym.c	   895  	{
; ..\vss_code\vsskeym.c	   896  		return ERR_READ_FLASH;
; ..\vss_code\vsskeym.c	   897  	}
; ..\vss_code\vsskeym.c	   898  	
; ..\vss_code\vsskeym.c	   899  	return ret;
; ..\vss_code\vsskeym.c	   900  }
	mov	d2,#27
	ret
.L263:
	mov	d4,#0
.L1482:
	mov	d5,#1088
	mov.aa	a4,a10
.L1064:
	mov	d6,#32
	calli	a2
.L1063:
	jne	d2,#0,.L265
.L1483:
	mov.aa	a4,a10
.L1484:
	fcall	.cocofun_4
.L1485:
	mov.aa	a6,a12
.L1067:
	mov.aa	a7,a15
.L1069:
	call	VssSM4Calc
.L1068:
	jeq	d2,#0,.L266
.L265:
	mov	d2,#28
	ret
.L266:
	mov	d2,#0
	ret
.L790:
	
__LoadKeyCode_function_end:
	.size	LoadKeyCode,__LoadKeyCode_function_end-LoadKeyCode
.L506:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('CryptoPaddingTo16')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	CryptoPaddingTo16

; ..\vss_code\vsskeym.c	   901  
; ..\vss_code\vsskeym.c	   902  vss_uint32 CryptoPaddingTo16(vss_uint8 *in, vss_uint32 inLen, vss_uint32 force) {
; Function CryptoPaddingTo16
.L387:
CryptoPaddingTo16:	.type	func

; ..\vss_code\vsskeym.c	   903  	vss_uint32 outlen = 0;
; ..\vss_code\vsskeym.c	   904  	vss_uint32 i = 0;
; ..\vss_code\vsskeym.c	   905  	vss_uint32 nPadLen = 16 - inLen % 16;
	and	d15,d4,#15
.L1439:
	rsub	d2,d15,#16
.L1070:

; ..\vss_code\vsskeym.c	   906  	vss_uint8 *pTemp;
; ..\vss_code\vsskeym.c	   907  
; ..\vss_code\vsskeym.c	   908  	if (force == 0 && nPadLen == 16)
	jne	d5,#0,.L269
.L1440:
	mov	d15,#16
.L1441:
	jne	d15,d2,.L270
.L1442:

; ..\vss_code\vsskeym.c	   909  		return inLen;
; ..\vss_code\vsskeym.c	   910  
; ..\vss_code\vsskeym.c	   911  	pTemp = in + inLen;
; ..\vss_code\vsskeym.c	   912  	*pTemp = 0x80;
; ..\vss_code\vsskeym.c	   913  	pTemp++;
; ..\vss_code\vsskeym.c	   914  
; ..\vss_code\vsskeym.c	   915  	for (i = 1; i < nPadLen; i++) {
; ..\vss_code\vsskeym.c	   916  		*pTemp = 0;
; ..\vss_code\vsskeym.c	   917  		pTemp++;
; ..\vss_code\vsskeym.c	   918  	}
; ..\vss_code\vsskeym.c	   919  
; ..\vss_code\vsskeym.c	   920  	outlen = inLen + nPadLen;
; ..\vss_code\vsskeym.c	   921  	return outlen;
; ..\vss_code\vsskeym.c	   922  }
	mov	d2,d4
	ret
.L270:
.L269:
	addsc.a	a15,a4,d4,#0
.L1071:
	mov	d15,#128
.L1443:
	st.b	[a15+],d15
.L1444:
	mov	d15,#1
.L1072:
	mov	d0,#0
	j	.L272
.L273:
	st.b	[a15+],d0
.L1445:
	add	d15,#1
.L272:
	jlt.u	d15,d2,.L273
.L1446:
	add	d2,d4
	ret
.L770:
	
__CryptoPaddingTo16_function_end:
	.size	CryptoPaddingTo16,__CryptoPaddingTo16_function_end-CryptoPaddingTo16
.L491:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('CryptoUnPadding16')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	CryptoUnPadding16

; ..\vss_code\vsskeym.c	   923  
; ..\vss_code\vsskeym.c	   924  vss_uint32 CryptoUnPadding16(vss_uint8 *in, vss_uint32 inLen) {
; Function CryptoUnPadding16
.L389:
CryptoUnPadding16:	.type	func

; ..\vss_code\vsskeym.c	   925  	vss_uint32 i = 0;
; ..\vss_code\vsskeym.c	   926  	vss_uint32 oriLen = inLen;
; ..\vss_code\vsskeym.c	   927  	vss_uint8 *pTemp = in + inLen - 1;
	addsc.a	a15,a4,d4,#0
.L1073:

; ..\vss_code\vsskeym.c	   928  
; ..\vss_code\vsskeym.c	   929  	for (i = 0; i < 16; i++) {
	mov	d0,#0
.L1074:
	add.a	a15,#-1
.L1451:
	mov	d15,#16
	j	.L275
.L276:

; ..\vss_code\vsskeym.c	   930  		if (*pTemp == 0) {
	ld.bu	d1,[a15]
.L1452:
	jne	d1,#0,.L277
.L1453:

; ..\vss_code\vsskeym.c	   931  			pTemp--;
; ..\vss_code\vsskeym.c	   932  			oriLen--;
	add	d4,#-1
	add.a	a15,#-1
.L1454:
	add	d0,#1
	j	.L278
.L277:

; ..\vss_code\vsskeym.c	   933  		} else if (*pTemp == 0x80) {
	mov	d15,#128
.L1455:
	jne	d15,d1,.L279
.L1456:

; ..\vss_code\vsskeym.c	   934  			*pTemp = 0;
	mov	d15,#0
	st.b	[a15],d15
.L1457:

; ..\vss_code\vsskeym.c	   935  			oriLen--;
	add	d4,#-1
.L279:

; ..\vss_code\vsskeym.c	   936  			return oriLen;
; ..\vss_code\vsskeym.c	   937  		} else {
; ..\vss_code\vsskeym.c	   938  			break;
; ..\vss_code\vsskeym.c	   939  		}
; ..\vss_code\vsskeym.c	   940  	}
; ..\vss_code\vsskeym.c	   941  
; ..\vss_code\vsskeym.c	   942  	return oriLen;
; ..\vss_code\vsskeym.c	   943  }
.L280:
	mov	d2,d4
	ret
.L278:
.L275:
	jlt.u	d0,d15,.L276
.L1458:
	j	.L280
.L777:
	
__CryptoUnPadding16_function_end:
	.size	CryptoUnPadding16,__CryptoUnPadding16_function_end-CryptoUnPadding16
.L496:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SetAlg')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SetAlg

; ..\vss_code\vsskeym.c	   944  
; ..\vss_code\vsskeym.c	   945  vss_uint32 SetAlg(vss_uint8 alg)
; Function SetAlg
.L391:
SetAlg:	.type	func
	sub.a	a10,#8
.L1075:
	st.b	[a10],d4
.L1490:

; ..\vss_code\vsskeym.c	   946  {
; ..\vss_code\vsskeym.c	   947  	vss_uint32 ret = 0;
	mov	d8,#0
.L1076:

; ..\vss_code\vsskeym.c	   948  	
; ..\vss_code\vsskeym.c	   949  	if(alg != ALG_GJ && alg != ALG_GM)
	ld.bu	d15,[a10]
	jeq	d15,#1,.L282
.L1491:
	jeq	d15,#2,.L283
.L1492:

; ..\vss_code\vsskeym.c	   950  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   951  
; ..\vss_code\vsskeym.c	   952  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   953  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   954  
; ..\vss_code\vsskeym.c	   955  	if(g_cVssAlg != alg)
; ..\vss_code\vsskeym.c	   956  	{
; ..\vss_code\vsskeym.c	   957  		ret = g_vssIocb(FLASH_IO_WRITE, FLASH_CFG_ALG, &alg, 1);
; ..\vss_code\vsskeym.c	   958  		if(ret == 0)
; ..\vss_code\vsskeym.c	   959  		{	
; ..\vss_code\vsskeym.c	   960  			g_cVssAlg = alg;
; ..\vss_code\vsskeym.c	   961  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	   962  			mem_set8(g_vssAsymmKey, 0, ASYMM_KEY_LEN);
; ..\vss_code\vsskeym.c	   963  			mem_set8(g_vssRootCert, 0, ROOT_CERT_SIZE);
; ..\vss_code\vsskeym.c	   964  			mem_set8(g_vssUserCert, 0, USER_CERT_SIZE);
; ..\vss_code\vsskeym.c	   965  #endif
; ..\vss_code\vsskeym.c	   966  		}
; ..\vss_code\vsskeym.c	   967  	}
; ..\vss_code\vsskeym.c	   968  
; ..\vss_code\vsskeym.c	   969  	return ret;
; ..\vss_code\vsskeym.c	   970  }
	mov	d2,#4
	ret
.L283:
.L282:
	fcall	.cocofun_1
.L1077:
	jnz.a	a15,.L285
.L1493:
	mov	d2,#27
	ret
.L285:
	movh.a	a12,#@his(g_cVssAlg)
	lea	a12,[a12]@los(g_cVssAlg)
	ld.bu	d15,[a12]
.L1494:
	ld.bu	d0,[a10]
.L1495:
	jeq	d15,d0,.L287
.L1496:
	mov	d4,#1
.L1079:
	mov	d5,#1121
	mov.aa	a4,a10
.L1497:
	mov	d6,d4
	calli	a15
.L1080:
	mov	d8,d2
.L1081:
	jne	d8,#0,.L288
.L1498:
	ld.bu	d15,[a10]
.L1499:
	fcall	.cocofun_8
.L1078:
	call	mem_set8
.L1500:
	fcall	.cocofun_10
.L1501:
	call	mem_set8
.L1502:
	fcall	.cocofun_11
.L1503:
	call	mem_set8
.L288:
.L287:
	mov	d2,d8
	ret
.L795:
	
__SetAlg_function_end:
	.size	SetAlg,__SetAlg_function_end-SetAlg
.L511:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_11')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_11
.L393:
.cocofun_11:	.type	func
; Function body .cocofun_11, coco_iter:0
	fcall	.cocofun_16
.L1083:
	mov	d4,#0
.L1704:
	mov	d5,#176
	fret
.L606:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_10')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_10
.L395:
.cocofun_10:	.type	func
; Function body .cocofun_10, coco_iter:0
	fcall	.cocofun_15
.L1082:
	mov	d4,#0
.L1699:
	mov	d5,#144
	fret
.L601:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('.cocofun_8')
	.sect	'.text.vss_api_code'
	.align	2
; Function .cocofun_8
.L397:
.cocofun_8:	.type	func
; Function body .cocofun_8, coco_iter:0
	st.b	[a12],d15
.L1687:
	fcall	.cocofun_18
.L1688:
	mov	d4,#0
.L1689:
	mov	d5,#96
	fret
.L591:
	; End of function
	.sdecl	'.text.vss_api_code',code,cluster('GetAlg')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GetAlg

; ..\vss_code\vsskeym.c	   971  
; ..\vss_code\vsskeym.c	   972  vss_uint32 GetAlg(void)
; Function GetAlg
.L399:
GetAlg:	.type	func
	sub.a	a10,#8
.L1084:

; ..\vss_code\vsskeym.c	   973  {
; ..\vss_code\vsskeym.c	   974  
; ..\vss_code\vsskeym.c	   975  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	   976  	vss_uint8 load;
; ..\vss_code\vsskeym.c	   977  	
; ..\vss_code\vsskeym.c	   978  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_1
.L1508:
	jnz.a	a15,.L290
.L1509:

; ..\vss_code\vsskeym.c	   979  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	   980  	
; ..\vss_code\vsskeym.c	   981  	ret = g_vssIocb(FLASH_IO_READ, FLASH_CFG_ALG, &load, 1);
; ..\vss_code\vsskeym.c	   982  	if(ret == 0 && load != 0)
; ..\vss_code\vsskeym.c	   983  	{	
; ..\vss_code\vsskeym.c	   984  		g_cVssAlg = load;
; ..\vss_code\vsskeym.c	   985  	}
; ..\vss_code\vsskeym.c	   986  
; ..\vss_code\vsskeym.c	   987  	return ret;
; ..\vss_code\vsskeym.c	   988  }
	mov	d2,#27
	ret
.L290:
	mov	d4,#0
.L1510:
	mov	d5,#1121
	mov.aa	a4,a10
.L1511:
	mov	d6,#1
	calli	a15
.L1085:
	jne	d2,#0,.L292
.L1512:
	ld.bu	d15,[a10]
.L1513:
	jeq	d15,#0,.L293
.L1514:
	movh.a	a15,#@his(g_cVssAlg)
.L1515:
	st.b	[a15]@los(g_cVssAlg),d15
.L293:
.L292:
	ret
.L799:
	
__GetAlg_function_end:
	.size	GetAlg,__GetAlg_function_end-GetAlg
.L516:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SetEnvironment')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SetEnvironment

; ..\vss_code\vsskeym.c	   989  
; ..\vss_code\vsskeym.c	   990  vss_uint32 SetEnvironment(vss_uint8 env)
; Function SetEnvironment
.L401:
SetEnvironment:	.type	func
	sub.a	a10,#8
.L1086:
	st.b	[a10],d4
.L1520:

; ..\vss_code\vsskeym.c	   991  {
; ..\vss_code\vsskeym.c	   992  	vss_uint32 ret = 0;
	mov	d8,#0
.L1087:

; ..\vss_code\vsskeym.c	   993  	
; ..\vss_code\vsskeym.c	   994  	if(env != ENV_QA && env != ENV_PP && env != ENV_P)
	ld.bu	d15,[a10]
	jeq	d15,#1,.L295
.L1521:
	jeq	d15,#2,.L296
.L1522:
	jeq	d15,#3,.L297
.L1523:

; ..\vss_code\vsskeym.c	   995  		return ERR_PARAMETER;
; ..\vss_code\vsskeym.c	   996  
; ..\vss_code\vsskeym.c	   997  
; ..\vss_code\vsskeym.c	   998  	if (g_vssIocb == VSS_NULL)
; ..\vss_code\vsskeym.c	   999  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	  1000  	
; ..\vss_code\vsskeym.c	  1001  	if(g_cVssEnv != env)
; ..\vss_code\vsskeym.c	  1002  	{
; ..\vss_code\vsskeym.c	  1003  		ret = g_vssIocb(FLASH_IO_WRITE, FLASH_CFG_ENV, &env, 1);
; ..\vss_code\vsskeym.c	  1004  		if(ret == 0)
; ..\vss_code\vsskeym.c	  1005  		{	
; ..\vss_code\vsskeym.c	  1006  			g_cVssEnv = env;
; ..\vss_code\vsskeym.c	  1007  #if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
; ..\vss_code\vsskeym.c	  1008  			mem_set8(g_vssAsymmKey, 0, ASYMM_KEY_LEN);
; ..\vss_code\vsskeym.c	  1009  			mem_set8(g_vssRootCert, 0, ROOT_CERT_SIZE);
; ..\vss_code\vsskeym.c	  1010  			mem_set8(g_vssUserCert, 0, USER_CERT_SIZE);
; ..\vss_code\vsskeym.c	  1011  #endif
; ..\vss_code\vsskeym.c	  1012  		}
; ..\vss_code\vsskeym.c	  1013  	}
; ..\vss_code\vsskeym.c	  1014  
; ..\vss_code\vsskeym.c	  1015  	return ret;
; ..\vss_code\vsskeym.c	  1016  }
	mov	d2,#4
	ret
.L297:
.L296:
.L295:
	fcall	.cocofun_1
.L1088:
	jnz.a	a15,.L299
.L1524:
	mov	d2,#27
	ret
.L299:
	movh.a	a12,#@his(g_cVssEnv)
	lea	a12,[a12]@los(g_cVssEnv)
	ld.bu	d15,[a12]
.L1525:
	ld.bu	d0,[a10]
.L1526:
	jeq	d15,d0,.L301
.L1527:
	mov	d4,#1
.L1089:
	mov	d5,#1120
	mov.aa	a4,a10
.L1528:
	mov	d6,d4
	calli	a15
.L1090:
	mov	d8,d2
.L1092:
	jne	d8,#0,.L302
.L1529:
	ld.bu	d15,[a10]
.L1530:
	fcall	.cocofun_8
.L1531:
	call	mem_set8
.L1532:
	fcall	.cocofun_10
.L1091:
	call	mem_set8
.L1533:
	fcall	.cocofun_11
.L1534:
	call	mem_set8
.L302:
.L301:
	mov	d2,d8
	ret
.L802:
	
__SetEnvironment_function_end:
	.size	SetEnvironment,__SetEnvironment_function_end-SetEnvironment
.L521:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('GetEnvironment')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GetEnvironment

; ..\vss_code\vsskeym.c	  1017  
; ..\vss_code\vsskeym.c	  1018  vss_uint32 GetEnvironment(void)
; Function GetEnvironment
.L403:
GetEnvironment:	.type	func
	sub.a	a10,#8
.L1093:

; ..\vss_code\vsskeym.c	  1019  {
; ..\vss_code\vsskeym.c	  1020  
; ..\vss_code\vsskeym.c	  1021  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	  1022  	vss_uint8 load = 0;
	mov	d15,#0
	st.b	[a10],d15
.L1094:

; ..\vss_code\vsskeym.c	  1023  	
; ..\vss_code\vsskeym.c	  1024  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_1
.L1095:
	jnz.a	a15,.L304
.L1539:

; ..\vss_code\vsskeym.c	  1025  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	  1026  	
; ..\vss_code\vsskeym.c	  1027  	ret = g_vssIocb(FLASH_IO_READ, FLASH_CFG_ENV, &load, 1);
; ..\vss_code\vsskeym.c	  1028  	if(ret == 0 && load != 0)
; ..\vss_code\vsskeym.c	  1029  	{	
; ..\vss_code\vsskeym.c	  1030  		g_cVssEnv = load;
; ..\vss_code\vsskeym.c	  1031  	}
; ..\vss_code\vsskeym.c	  1032  
; ..\vss_code\vsskeym.c	  1033  	return ret;
; ..\vss_code\vsskeym.c	  1034  }
	mov	d2,#27
	ret
.L304:
	mov	d4,#0
.L1540:
	mov	d5,#1120
	mov.aa	a4,a10
.L1541:
	mov	d6,#1
	calli	a15
.L1096:
	jne	d2,#0,.L306
.L1542:
	ld.bu	d15,[a10]
.L1543:
	jeq	d15,#0,.L307
.L1544:
	movh.a	a15,#@his(g_cVssEnv)
.L1545:
	st.b	[a15]@los(g_cVssEnv),d15
.L307:
.L306:
	ret
.L805:
	
__GetEnvironment_function_end:
	.size	GetEnvironment,__GetEnvironment_function_end-GetEnvironment
.L526:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('SetWroteFlag')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	SetWroteFlag

; ..\vss_code\vsskeym.c	  1035  
; ..\vss_code\vsskeym.c	  1036  vss_uint32 SetWroteFlag(vss_uint8 flag)
; Function SetWroteFlag
.L405:
SetWroteFlag:	.type	func
	sub.a	a10,#8
.L1097:
	st.b	[a10],d4
.L1550:

; ..\vss_code\vsskeym.c	  1037  {
; ..\vss_code\vsskeym.c	  1038  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	  1039  	
; ..\vss_code\vsskeym.c	  1040  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_1
.L1551:
	jnz.a	a15,.L309
.L1552:

; ..\vss_code\vsskeym.c	  1041  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	  1042  	
; ..\vss_code\vsskeym.c	  1043  	ret = g_vssIocb(FLASH_IO_WRITE, FLASH_CFG_WROTE, &flag, 1);
; ..\vss_code\vsskeym.c	  1044  	return ret;
; ..\vss_code\vsskeym.c	  1045  }
	mov	d2,#27
	ret
.L309:
	mov	d4,#1
.L1098:
	mov	d5,#1122
	mov.aa	a4,a10
.L1553:
	mov	d6,d4
	ji	a15
.L808:
	
__SetWroteFlag_function_end:
	.size	SetWroteFlag,__SetWroteFlag_function_end-SetWroteFlag
.L531:
	; End of function
	
	.sdecl	'.text.vss_api_code',code,cluster('GetWroteFLag')
	.sect	'.text.vss_api_code'
	.align	2
	
	.global	GetWroteFLag

; ..\vss_code\vsskeym.c	  1046  
; ..\vss_code\vsskeym.c	  1047  vss_uint32 GetWroteFLag(vss_uint8* flag)
; Function GetWroteFLag
.L407:
GetWroteFLag:	.type	func
	sub.a	a10,#8
.L1099:
	mov.aa	a15,a4
.L1100:

; ..\vss_code\vsskeym.c	  1048  {
; ..\vss_code\vsskeym.c	  1049  
; ..\vss_code\vsskeym.c	  1050  	vss_uint32 ret = 0;
; ..\vss_code\vsskeym.c	  1051  	vss_uint8 load;
; ..\vss_code\vsskeym.c	  1052  	
; ..\vss_code\vsskeym.c	  1053  	if (g_vssIocb == VSS_NULL)
	fcall	.cocofun_13
.L1558:
	jnz.a	a2,.L312
.L1559:

; ..\vss_code\vsskeym.c	  1054  		return ERR_SYSTEM_INIT;
; ..\vss_code\vsskeym.c	  1055  	
; ..\vss_code\vsskeym.c	  1056  	ret = g_vssIocb(FLASH_IO_READ, FLASH_CFG_WROTE, &load, 1);
; ..\vss_code\vsskeym.c	  1057  	if(ret == 0)
; ..\vss_code\vsskeym.c	  1058  	{
; ..\vss_code\vsskeym.c	  1059  		if(load == 1)
; ..\vss_code\vsskeym.c	  1060  			*flag = 1;
; ..\vss_code\vsskeym.c	  1061  		else
; ..\vss_code\vsskeym.c	  1062  			*flag = 0;
; ..\vss_code\vsskeym.c	  1063  	}
; ..\vss_code\vsskeym.c	  1064  
; ..\vss_code\vsskeym.c	  1065  	return ret;
; ..\vss_code\vsskeym.c	  1066  }
	mov	d2,#27
	ret
.L312:
	mov	d4,#0
.L1560:
	mov	d5,#1122
	mov.aa	a4,a10
.L1101:
	mov	d6,#1
	calli	a2
.L1102:
	jne	d2,#0,.L314
.L1561:
	ld.bu	d15,[a10]
.L1562:
	eq	d15,d15,#1
	st.b	[a15],d15
.L314:
	ret
.L811:
	
__GetWroteFLag_function_end:
	.size	GetWroteFLag,__GetWroteFLag_function_end-GetWroteFLag
.L536:
	; End of function
	
	.calls	'LoadCert','mem_cmp8'
	.calls	'LoadCert','mem_cpy8'
	.calls	'LoadCert','getEnvIndex'
	.calls	'LoadCert','LoadCertById'
	.calls	'LoadCertById','__INDIRECT__'
	.calls	'LoadCertById','mem_cmp8'
	.calls	'LoadCertById','VssSM4Mac'
	.calls	'LoadCertById','VssSM4Calc'
	.calls	'LoadCertById','mem_cpy8'
	.calls	'SaveCert','getEnvIndex'
	.calls	'SaveCert','VssSM4Calc'
	.calls	'SaveCert','VssSM4Mac'
	.calls	'SaveCert','__INDIRECT__'
	.calls	'SaveCert','mem_cpy8'
	.calls	'GenSymmKey','LoadCFGData'
	.calls	'GenSymmKey','__INDIRECT__'
	.calls	'GenSymmKey','mem_set8'
	.calls	'GenSymmKey','mem_cpy8'
	.calls	'GenSymmKey','VssSM4Calc'
	.calls	'GenSymmKey','SetSymmKey'
	.calls	'GenSymmKey','SetAlg'
	.calls	'GenSymmKey','SaveKeyCode'
	.calls	'LoadSymmKey','mem_set8'
	.calls	'LoadSymmKey','mem_cmp8'
	.calls	'LoadSymmKey','mem_cpy8'
	.calls	'LoadSymmKey','__INDIRECT__'
	.calls	'LoadSymmKey','VssSM4Calc'
	.calls	'SaveSymmKey','VssSM4Calc'
	.calls	'SaveSymmKey','__INDIRECT__'
	.calls	'GetSessKey','GetSymmKey'
	.calls	'GetSymmKey','LoadSymmKey'
	.calls	'GetSymmKey','mem_cpy8'
	.calls	'SetKeyActive','LoadSymmKey'
	.calls	'SetKeyActive','SetSymmKey'
	.calls	'GetKeyActive','LoadSymmKey'
	.calls	'SetSessKey','SetSymmKey'
	.calls	'SetSymmKey','mem_set8'
	.calls	'SetSymmKey','mem_cpy8'
	.calls	'SetSymmKey','VssSM4Calc'
	.calls	'SetSymmKey','SaveSymmKey'
	.calls	'GenAsymmKey','mem_set8'
	.calls	'GenAsymmKey','VssECCGenKey'
	.calls	'GenAsymmKey','VssSM2GenKey'
	.calls	'GenAsymmKey','VssSM4Mac'
	.calls	'GenAsymmKey','mem_cpy8'
	.calls	'GenAsymmKey','VssSM4Calc'
	.calls	'GenAsymmKey','__INDIRECT__'
	.calls	'LoadAsymmKey','mem_cmp8'
	.calls	'LoadAsymmKey','mem_cpy8'
	.calls	'LoadAsymmKey','__INDIRECT__'
	.calls	'LoadAsymmKey','VssSM4Calc'
	.calls	'LoadAsymmKey','VssSM4Mac'
	.calls	'SaveAsymmKey','mem_set8'
	.calls	'SaveAsymmKey','mem_cpy8'
	.calls	'SaveAsymmKey','VssSM4Mac'
	.calls	'SaveAsymmKey','VssSM4Calc'
	.calls	'SaveAsymmKey','__INDIRECT__'
	.calls	'LoadCFGData','__INDIRECT__'
	.calls	'LoadCFGData','VssSM4Mac'
	.calls	'LoadCFGData','mem_cmp8'
	.calls	'LoadCFGData','VssSM4Calc'
	.calls	'SaveKeyCode','VssSM4Calc'
	.calls	'SaveKeyCode','__INDIRECT__'
	.calls	'LoadKeyCode','__INDIRECT__'
	.calls	'LoadKeyCode','VssSM4Calc'
	.calls	'SetAlg','__INDIRECT__'
	.calls	'SetAlg','mem_set8'
	.calls	'GetAlg','__INDIRECT__'
	.calls	'SetEnvironment','__INDIRECT__'
	.calls	'SetEnvironment','mem_set8'
	.calls	'GetEnvironment','__INDIRECT__'
	.calls	'SetWroteFlag','__INDIRECT__'
	.calls	'GetWroteFLag','__INDIRECT__'
	.calls	'getEnvIndex','.cocofun_12'
	.calls	'getEnvIndex','.cocofun_2'
	.calls	'LoadCert','.cocofun_9'
	.calls	'.cocofun_9','.cocofun_14'
	.calls	'LoadCertById','.cocofun_1'
	.calls	'LoadCertById','.cocofun_14'
	.calls	'LoadCertById','.cocofun_6'
	.calls	'LoadCertById','.cocofun_15'
	.calls	'LoadCertById','.cocofun_16'
	.calls	'SaveCert','.cocofun_7'
	.calls	'SaveCert','.cocofun_3'
	.calls	'SaveCert','.cocofun_15'
	.calls	'SaveCert','.cocofun_16'
	.calls	'LoadSymmKey','.cocofun_4'
	.calls	'.cocofun_4','.cocofun_17'
	.calls	'SaveSymmKey','.cocofun_7'
	.calls	'SaveSymmKey','.cocofun_17'
	.calls	'SaveSymmKey','.cocofun_3'
	.calls	'GenAsymmKey','.cocofun_12'
	.calls	'GenAsymmKey','.cocofun_6'
	.calls	'GenAsymmKey','.cocofun_5'
	.calls	'GenAsymmKey','.cocofun_3'
	.calls	'GenAsymmKey','.cocofun_18'
	.calls	'LoadAsymmKey','.cocofun_1'
	.calls	'LoadAsymmKey','.cocofun_6'
	.calls	'LoadAsymmKey','.cocofun_5'
	.calls	'SaveAsymmKey','.cocofun_12'
	.calls	'SaveAsymmKey','.cocofun_6'
	.calls	'SaveAsymmKey','.cocofun_5'
	.calls	'SaveAsymmKey','.cocofun_3'
	.calls	'SaveAsymmKey','.cocofun_18'
	.calls	'LoadCFGData','.cocofun_13'
	.calls	'LoadCFGData','.cocofun_5'
	.calls	'SaveKeyCode','.cocofun_7'
	.calls	'SaveKeyCode','.cocofun_3'
	.calls	'LoadKeyCode','.cocofun_13'
	.calls	'LoadKeyCode','.cocofun_4'
	.calls	'SetAlg','.cocofun_1'
	.calls	'SetAlg','.cocofun_8'
	.calls	'SetAlg','.cocofun_10'
	.calls	'SetAlg','.cocofun_11'
	.calls	'.cocofun_11','.cocofun_16'
	.calls	'.cocofun_10','.cocofun_15'
	.calls	'.cocofun_8','.cocofun_18'
	.calls	'GetAlg','.cocofun_1'
	.calls	'SetEnvironment','.cocofun_1'
	.calls	'SetEnvironment','.cocofun_8'
	.calls	'SetEnvironment','.cocofun_10'
	.calls	'SetEnvironment','.cocofun_11'
	.calls	'GetEnvironment','.cocofun_1'
	.calls	'SetWroteFlag','.cocofun_1'
	.calls	'GetWroteFLag','.cocofun_13'
	.calls	'getEnvIndex','',0
	.calls	'.cocofun_12','',0
	.calls	'.cocofun_2','',0
	.calls	'LoadCert','',0
	.calls	'.cocofun_9','',0
	.calls	'.cocofun_14','',0
	.calls	'LoadCertById','',536
	.calls	'.cocofun_16','',0
	.calls	'.cocofun_15','',0
	.calls	'.cocofun_6','',0
	.calls	'.cocofun_1','',0
	.calls	'SaveCert','',264
	.calls	'.cocofun_7','',0
	.calls	'.cocofun_3','',0
	.calls	'GenSymmKey','',616
	.calls	'LoadSymmKey','',40
	.calls	'.cocofun_4','',0
	.calls	'.cocofun_17','',0
	.calls	'SaveSymmKey','',40
	.calls	'GetSessKey','',0
	.calls	'GetSymmKey','',32
	.calls	'SetKeyActive','',32
	.calls	'GetKeyActive','',32
	.calls	'SetSessKey','',0
	.calls	'SetSymmKey','',56
	.calls	'GenAsymmKey','',248
	.calls	'.cocofun_18','',0
	.calls	'.cocofun_5','',0
	.calls	'LoadAsymmKey','',248
	.calls	'SaveAsymmKey','',248
	.calls	'LoadCFGData','',136
	.calls	'.cocofun_13','',0
	.calls	'SaveCFGData','',0
	.calls	'SaveKeyCode','',40
	.calls	'LoadKeyCode','',32
	.calls	'CryptoPaddingTo16','',0
	.calls	'CryptoUnPadding16','',0
	.calls	'SetAlg','',8
	.calls	'.cocofun_11','',0
	.calls	'.cocofun_10','',0
	.calls	'.cocofun_8','',0
	.calls	'GetAlg','',8
	.calls	'SetEnvironment','',8
	.calls	'GetEnvironment','',8
	.calls	'SetWroteFlag','',8
	.extern	VssSM2GenKey
	.extern	VssECCGenKey
	.extern	VssSM4Calc
	.extern	VssSM4Mac
	.extern	mem_set8
	.extern	mem_cpy8
	.extern	mem_cmp8
	.extern	g_vssIocb
	.extern	g_vssAsymmKey
	.extern	g_vssRootCert
	.extern	g_vssUserCert
	.extern	g_vssSymmKey
	.extern	g_vssSessKey
	.extern	g_cVssAlg
	.extern	g_cVssEnv
	.extern	vsskeym_KEK
	.extern	vsskeym_SECOC
	.extern	vss_ZERO
	.extern	__INDIRECT__
	.calls	'GetWroteFLag','',8
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L409:
	.word	2020
	.half	3
	.word	.L410
	.byte	4
.L408:
	.byte	1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L411
.L642:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L645:
	.byte	3
	.word	176
.L684:
	.byte	2
	.byte	'unsigned char',0,1,8
.L647:
	.byte	3
	.word	202
.L657:
	.byte	4,128,2
	.word	202
	.byte	5,255,1,0
.L660:
	.byte	4,16
	.word	202
	.byte	5,15,0,2
	.byte	'char',0,1,6
.L678:
	.byte	3
	.word	244
.L681:
	.byte	4,8
	.word	202
	.byte	5,7,0
.L690:
	.byte	4,4
	.word	202
	.byte	5,3,0
.L692:
	.byte	4,96
	.word	202
	.byte	5,95,0
.L694:
	.byte	4,192,3
	.word	202
	.byte	5,191,3,0
.L707:
	.byte	6,1,35,9,32,7
	.byte	'cFlag',0,1
	.word	202
	.byte	2,35,0,7
	.byte	'cKeyLen',0,1
	.word	202
	.byte	2,35,1,7
	.byte	'cIndex',0,1
	.word	202
	.byte	2,35,2,7
	.byte	'cAlg',0,1
	.word	202
	.byte	2,35,3,4,3
	.word	202
	.byte	5,2,0,7
	.byte	'cv',0,3
	.word	362
	.byte	2,35,4,7
	.byte	'cRFU',0,1
	.word	202
	.byte	2,35,7,4,24
	.word	202
	.byte	5,23,0,7
	.byte	'value',0,24
	.word	397
	.byte	2,35,8,0
.L737:
	.byte	4,112
	.word	202
	.byte	5,111,0
.L741:
	.byte	6,1,48,9,112,7
	.byte	'cUseFlag',0,1
	.word	202
	.byte	2,35,0,7
	.byte	'cIndex',0,1
	.word	202
	.byte	2,35,1,7
	.byte	'cAlg',0,1
	.word	202
	.byte	2,35,2,7
	.byte	'cXLen',0,1
	.word	202
	.byte	2,35,3,7
	.byte	'cYLen',0,1
	.word	202
	.byte	2,35,4,7
	.byte	'cSKLen',0,1
	.word	202
	.byte	2,35,5,7
	.byte	'cRFU7',0,1
	.word	202
	.byte	2,35,6,7
	.byte	'cRFU8',0,1
	.word	202
	.byte	2,35,7,7
	.byte	'value',0,96
	.word	275
	.byte	2,35,8,7
	.byte	'mac',0,8
	.word	257
	.byte	2,35,104,0
.L787:
	.byte	4,32
	.word	202
	.byte	5,31,0,8
	.byte	'VssSM2GenKey',0,2,79,12
	.word	176
	.byte	1,1,1,1,9
	.byte	'szX',0,2,79,36
	.word	219
	.byte	9
	.byte	'szY',0,2,79,51
	.word	219
	.byte	9
	.byte	'szSK',0,2,79,67
	.word	219
	.byte	0,8
	.byte	'VssECCGenKey',0,2,124,12
	.word	176
	.byte	1,1,1,1,9
	.byte	'szX',0,2,124,36
	.word	219
	.byte	9
	.byte	'szY',0,2,124,52
	.word	219
	.byte	9
	.byte	'szSK',0,2,124,68
	.word	219
	.byte	0,8
	.byte	'VssSM4Calc',0,2,142,4,12
	.word	176
	.byte	1,1,1,1,9
	.byte	'in',0,2,142,4,34
	.word	219
	.byte	9
	.byte	'inLen',0,2,142,4,49
	.word	176
	.byte	9
	.byte	'key',0,2,142,4,67
	.word	219
	.byte	9
	.byte	'keyLen',0,2,142,4,83
	.word	176
	.byte	9
	.byte	'calcFlag',0,2,142,4,102
	.word	176
	.byte	9
	.byte	'padding',0,2,142,4,123
	.word	176
	.byte	9
	.byte	'out',0,2,142,4,143,1
	.word	219
	.byte	9
	.byte	'pOutLen',0,2,142,4,160,1
	.word	197
	.byte	0,8
	.byte	'VssSM4Mac',0,2,194,4,12
	.word	176
	.byte	1,1,1,1,9
	.byte	'in',0,2,194,4,33
	.word	219
	.byte	9
	.byte	'inLen',0,2,194,4,48
	.word	176
	.byte	9
	.byte	'key',0,2,194,4,66
	.word	219
	.byte	9
	.byte	'keyLen',0,2,194,4,82
	.word	176
	.byte	9
	.byte	'out',0,2,194,4,101
	.word	219
	.byte	0,10
	.byte	'mem_set8',0,3,17,6,1,1,1,1,9
	.byte	'result',0,3,17,26
	.word	219
	.byte	11
	.word	202
	.byte	9
	.byte	'content',0,3,17,50
	.word	997
	.byte	9
	.byte	'len',0,3,17,70
	.word	176
	.byte	0,10
	.byte	'mem_cpy8',0,3,19,6,1,1,1,1,9
	.byte	'result',0,3,19,26
	.word	219
	.byte	11
	.word	202
	.byte	3
	.word	1063
	.byte	9
	.byte	'content',0,3,19,51
	.word	1068
	.byte	9
	.byte	'len',0,3,19,71
	.word	176
	.byte	0,8
	.byte	'mem_cmp8',0,3,21,11
	.word	202
	.byte	1,1,1,1,9
	.byte	'data1',0,3,21,37
	.word	1068
	.byte	9
	.byte	'data2',0,3,21,61
	.word	1068
	.byte	9
	.byte	'len',0,3,21,79
	.word	176
	.byte	0
.L820:
	.byte	3
	.word	295
	.byte	12
	.byte	'__INDIRECT__',0,1,1,1,1,1,1,13
	.byte	'void',0,3
	.word	1189
	.byte	14
	.byte	'__prof_adm',0,1,1,1
	.word	1195
	.byte	15,1,3
	.word	1219
	.byte	14
	.byte	'__codeptr',0,1,1,1
	.word	1221
	.byte	14
	.byte	'vss_uint8',0,4,8,24
	.word	202
	.byte	14
	.byte	'vss_char8',0,4,10,17
	.word	244
	.byte	14
	.byte	'vss_uint32',0,4,13,24
	.word	176
	.byte	2
	.byte	'unsigned long long int',0,8,7,14
	.byte	'vss_uint64',0,4,17,28
	.word	1299
	.byte	14
	.byte	'vss_ulong',0,4,18,24
	.word	176
	.byte	14
	.byte	'BYTE',0,4,21,22
	.word	202
	.byte	14
	.byte	'WORD',0,4,22,22
	.word	176
	.byte	14
	.byte	'SM3_WORD_T',0,4,25,22
	.word	176
	.byte	6,4,27,9,168,1,7
	.byte	'm_size',0,4
	.word	176
	.byte	2,35,0,4,128,1
	.word	202
	.byte	5,127,0,7
	.byte	'remain',0,128,1
	.word	1429
	.byte	2,35,4,7
	.byte	'r_len',0,4
	.word	176
	.byte	3,35,132,1,4,32
	.word	176
	.byte	5,7,0,7
	.byte	'iv',0,32
	.word	1472
	.byte	3,35,136,1,0,14
	.byte	'SM3_CTX_T',0,4,32,3
	.word	1407
	.byte	6,4,34,9,108,4,64
	.word	202
	.byte	5,63,0,7
	.byte	'data',0,64
	.word	1518
	.byte	2,35,0,7
	.byte	'datalen',0,4
	.word	176
	.byte	2,35,64,7
	.byte	'bitlen',0,8
	.word	1299
	.byte	2,35,68,4,32
	.word	176
	.byte	5,7,0,7
	.byte	'state',0,32
	.word	1574
	.byte	2,35,76,0,14
	.byte	'SHA256_CTX',0,4,39,3
	.word	1513
	.byte	16
	.word	176
	.byte	1,17
	.word	176
	.byte	17
	.word	176
	.byte	3
	.word	202
	.byte	17
	.word	1634
	.byte	17
	.word	176
	.byte	0,14
	.byte	'flash_io_cb',0,4,56,20
	.word	1618
	.byte	18
	.word	176
	.byte	1,1,17
	.word	176
	.byte	17
	.word	176
	.byte	17
	.word	1634
	.byte	17
	.word	176
	.byte	0,3
	.word	1670
	.byte	19
	.byte	'g_vssIocb',0,1,9,21
	.word	1698
	.byte	1,1,19
	.byte	'g_vssAsymmKey',0,1,11,18
	.word	275
	.byte	1,1,4,144,1
	.word	202
	.byte	5,143,1,0,19
	.byte	'g_vssRootCert',0,1,12,18
	.word	1747
	.byte	1,1,4,176,1
	.word	202
	.byte	5,175,1,0,19
	.byte	'g_vssUserCert',0,1,13,18
	.word	1782
	.byte	1,1,4,192,3
	.word	589
	.byte	5,13,0,19
	.byte	'g_vssSymmKey',0,1,14,18
	.word	1817
	.byte	1,1,4,64
	.word	589
	.byte	5,1,0,19
	.byte	'g_vssSessKey',0,1,15,18
	.word	1850
	.byte	1,1,19
	.byte	'g_cVssAlg',0,1,17,18
	.word	202
	.byte	1,1,19
	.byte	'g_cVssEnv',0,1,18,18
	.word	202
	.byte	1,1,11
	.word	235
	.byte	19
	.byte	'vsskeym_KEK',0,1,19,24
	.word	1922
	.byte	1,1,11
	.word	235
	.byte	19
	.byte	'vsskeym_SECOC',0,1,20,24
	.word	1949
	.byte	1,1,11
	.word	589
	.byte	19
	.byte	'vss_ZERO',0,1,21,24
	.word	1978
	.byte	1,1,14
	.byte	'TSymmKeyItem',0,1,46,3
	.word	295
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L410:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,15,0,73,19,0,0,4,1,1,11,15
	.byte	73,19,0,0,5,33,0,47,15,0,0,6,19,1,58,15,59,15,57,15,11,15,0,0,7,13,0,3,8,11,15,73,19,56,9,0,0,8,46,1,3
	.byte	8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,9,5,0,3,8,58,15,59,15,57,15,73,19,0,0,10,46,1,3
	.byte	8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,11,38,0,73,19,0,0,12,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	63,12,60,12,0,0,13,59,0,3,8,0,0,14,22,0,3,8,58,15,59,15,57,15,73,19,0,0,15,21,0,54,15,0,0,16,21,1,73,19
	.byte	39,12,0,0,17,5,0,73,19,0,0,18,21,1,73,19,54,15,39,12,0,0,19,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60
	.byte	12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L411:
	.word	.L1104-.L1103
.L1103:
	.half	3
	.word	.L1106-.L1105
.L1105:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0
	.byte	'..\\vss_code\\vssapi.h',0,0,0,0
	.byte	'..\\vss_code\\vsscommon.h',0,0,0,0
	.byte	'..\\vss_code\\vsstype.h',0,0,0,0,0
.L1106:
.L1104:
	.sdecl	'.debug_info',debug,cluster('LoadCert')
	.sect	'.debug_info'
.L412:
	.word	302
	.half	3
	.word	.L413
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L415,.L414
	.byte	2
	.word	.L408
	.byte	3
	.byte	'LoadCert',0,1,89,12
	.word	.L642
	.byte	1,1,1
	.word	.L323,.L643,.L322
	.byte	4
	.byte	'certType',0,1,89,32
	.word	.L642,.L644
	.byte	4
	.byte	'certlen',0,1,89,54
	.word	.L645,.L646
	.byte	4
	.byte	'certData',0,1,89,74
	.word	.L647,.L648
	.byte	5
	.word	.L649
	.byte	6
	.byte	'certId',0,1,91,13
	.word	.L642,.L650
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('LoadCert')
	.sect	'.debug_abbrev'
.L413:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('LoadCert')
	.sect	'.debug_line'
.L414:
	.word	.L1108-.L1107
.L1107:
	.half	3
	.word	.L1110-.L1109
.L1109:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1110:
	.byte	5,12,7,0,5,2
	.word	.L323
	.byte	3,216,0,1,5,6,9
	.half	.L844-.L323
	.byte	3,4,1,5,38,7,9
	.half	.L1111-.L844
	.byte	1,5,10,7,9
	.half	.L21-.L1111
	.byte	3,1,1,5,1,3,23,1,5,5,7,9
	.half	.L22-.L21
	.byte	3,107,1,5,44,7,9
	.half	.L1112-.L22
	.byte	1,5,59,9
	.half	.L1113-.L1112
	.byte	1,5,69,9
	.half	.L837-.L1113
	.byte	1,5,43,9
	.half	.L1114-.L837
	.byte	1,5,37,7,9
	.half	.L1115-.L1114
	.byte	3,2,1,5,14,9
	.half	.L842-.L1115
	.byte	3,1,1,5,12,1,5,10,9
	.half	.L840-.L842
	.byte	3,1,1,5,1,3,17,1,5,5,7,9
	.half	.L24-.L840
	.byte	3,114,1,5,43,7,9
	.half	.L1116-.L24
	.byte	1,5,58,9
	.half	.L1117-.L1116
	.byte	1,5,68,9
	.half	.L1118-.L1117
	.byte	1,5,42,9
	.half	.L1119-.L1118
	.byte	1,5,37,7,9
	.half	.L1120-.L1119
	.byte	3,2,1,5,14,9
	.half	.L847-.L1120
	.byte	3,1,1,5,12,1,5,10,9
	.half	.L845-.L847
	.byte	3,1,1,5,1,3,10,1,5,6,7,9
	.half	.L27-.L845
	.byte	3,121,1,5,2,9
	.half	.L1121-.L27
	.byte	1,5,23,7,9
	.half	.L1122-.L1121
	.byte	3,3,1,5,15,9
	.half	.L849-.L1122
	.byte	3,1,1,5,2,9
	.half	.L848-.L849
	.byte	1,5,10,7,9
	.half	.L30-.L848
	.byte	3,1,1,5,1,3,2,1,5,39,7,9
	.half	.L31-.L30
	.byte	3,127,1,5,1,9
	.half	.L416-.L31
	.byte	3,1,0,1,1
.L1108:
	.sdecl	'.debug_ranges',debug,cluster('LoadCert')
	.sect	'.debug_ranges'
.L415:
	.word	-1,.L323,0,.L416-.L323,0,0
.L649:
	.word	-1,.L323,0,.L643-.L323,-1,.L325,0,.L596-.L325,-1,.L327,0,.L621-.L327,0,0
	.sdecl	'.debug_info',debug,cluster('LoadCertById')
	.sect	'.debug_info'
.L417:
	.word	409
	.half	3
	.word	.L418
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L420,.L419
	.byte	2
	.word	.L408
	.byte	3
	.byte	'LoadCertById',0,1,119,12
	.word	.L642
	.byte	1,1,1
	.word	.L329,.L651,.L328
	.byte	4
	.byte	'certId',0,1,119,36
	.word	.L642,.L652
	.byte	4
	.byte	'certlen',0,1,119,56
	.word	.L645,.L653
	.byte	4
	.byte	'certData',0,1,119,76
	.word	.L647,.L654
	.byte	5
	.word	.L655
	.byte	6
	.byte	'ret',0,1,121,13
	.word	.L642,.L656
	.byte	6
	.byte	'buf',0,1,122,12
	.word	.L657,.L658
	.byte	6
	.byte	'cert',0,1,123,12
	.word	.L657,.L659
	.byte	6
	.byte	'mac',0,1,124,12
	.word	.L660,.L661
	.byte	6
	.byte	'offset',0,1,125,13
	.word	.L642,.L662
	.byte	6
	.byte	'noutlen',0,1,126,13
	.word	.L642,.L663
	.byte	6
	.byte	'readlen',0,1,127,13
	.word	.L642,.L664
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('LoadCertById')
	.sect	'.debug_abbrev'
.L418:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('LoadCertById')
	.sect	'.debug_line'
.L419:
	.word	.L1124-.L1123
.L1123:
	.half	3
	.word	.L1126-.L1125
.L1125:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1126:
	.byte	5,12,7,0,5,2
	.word	.L329
	.byte	3,246,0,1,5,23,9
	.half	.L857-.L329
	.byte	3,7,1,5,21,1,5,2,9
	.half	.L1127-.L857
	.byte	3,3,1,5,43,7,9
	.half	.L1128-.L1127
	.byte	3,2,1,5,34,9
	.half	.L1129-.L1128
	.byte	1,5,11,9
	.half	.L853-.L1129
	.byte	3,1,1,5,60,3,127,1,5,10,9
	.half	.L34-.L853
	.byte	3,3,1,5,11,9
	.half	.L856-.L34
	.byte	3,1,1,5,8,9
	.half	.L35-.L856
	.byte	3,3,1,5,18,9
	.half	.L1130-.L35
	.byte	1,5,41,1,5,46,9
	.half	.L855-.L1130
	.byte	1,5,2,9
	.half	.L854-.L855
	.byte	3,1,1,5,10,7,9
	.half	.L1131-.L854
	.byte	3,2,1,5,1,3,35,1,5,14,7,9
	.half	.L36-.L1131
	.byte	3,96,1,5,19,9
	.half	.L1132-.L36
	.byte	1,5,29,9
	.half	.L1133-.L1132
	.byte	1,5,2,9
	.half	.L859-.L1133
	.byte	1,5,10,7,9
	.half	.L1134-.L859
	.byte	3,1,1,5,1,3,31,1,5,49,7,9
	.half	.L38-.L1134
	.byte	3,99,1,5,31,9
	.half	.L860-.L38
	.byte	1,5,18,1,5,62,9
	.half	.L1135-.L860
	.byte	1,5,66,1,5,5,9
	.half	.L862-.L1135
	.byte	3,1,1,5,25,7,9
	.half	.L1136-.L862
	.byte	1,5,46,9
	.half	.L1137-.L1136
	.byte	1,5,41,1,5,35,9
	.half	.L1138-.L1137
	.byte	1,5,46,9
	.half	.L1139-.L1138
	.byte	1,5,20,9
	.half	.L863-.L1139
	.byte	1,5,10,7,9
	.half	.L40-.L863
	.byte	3,1,1,5,1,3,27,1,5,99,7,9
	.half	.L41-.L40
	.byte	3,103,1,5,63,9
	.half	.L1140-.L41
	.byte	1,5,19,1,5,67,9
	.half	.L1141-.L1140
	.byte	1,5,92,1,5,77,9
	.half	.L1142-.L1141
	.byte	1,5,99,1,5,2,9
	.half	.L864-.L1142
	.byte	3,1,1,7,9
	.half	.L1143-.L864
	.byte	3,3,1,5,10,7,9
	.half	.L1144-.L1143
	.byte	3,2,1,5,17,9
	.half	.L1145-.L1144
	.byte	1,5,3,9
	.half	.L861-.L1145
	.byte	1,5,12,7,9
	.half	.L1146-.L861
	.byte	3,3,1,5,27,9
	.half	.L1147-.L1146
	.byte	1,5,33,9
	.half	.L1148-.L1147
	.byte	1,5,22,9
	.half	.L865-.L1148
	.byte	3,2,1,5,28,9
	.half	.L1149-.L865
	.byte	1,5,14,9
	.half	.L867-.L1149
	.byte	3,1,1,5,48,3,125,1,5,8,9
	.half	.L44-.L867
	.byte	3,4,1,5,10,7,9
	.half	.L1150-.L44
	.byte	3,2,1,5,17,9
	.half	.L1151-.L1150
	.byte	1,5,3,9
	.half	.L868-.L1151
	.byte	1,5,11,7,9
	.half	.L43-.L868
	.byte	3,1,1,5,1,3,9,1,5,12,7,9
	.half	.L48-.L43
	.byte	3,121,1,5,27,9
	.half	.L869-.L48
	.byte	1,5,33,9
	.half	.L1152-.L869
	.byte	1,5,22,9
	.half	.L870-.L1152
	.byte	3,2,1,5,28,9
	.half	.L1153-.L870
	.byte	1,5,14,9
	.half	.L872-.L1153
	.byte	3,1,1,5,12,9
	.half	.L46-.L872
	.byte	1,5,9,9
	.half	.L47-.L46
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L421-.L47
	.byte	0,1,1
.L1124:
	.sdecl	'.debug_ranges',debug,cluster('LoadCertById')
	.sect	'.debug_ranges'
.L420:
	.word	-1,.L329,0,.L421-.L329,0,0
.L655:
	.word	-1,.L329,0,.L651-.L329,-1,.L331,0,.L631-.L331,-1,.L333,0,.L626-.L333,-1,.L335,0,.L581-.L335,-1,.L337,0
	.word	.L556-.L337,0,0
	.sdecl	'.debug_info',debug,cluster('SaveCert')
	.sect	'.debug_info'
.L422:
	.word	403
	.half	3
	.word	.L423
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L425,.L424
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SaveCert',0,1,178,1,12
	.word	.L642
	.byte	1,1,1
	.word	.L339,.L665,.L338
	.byte	4
	.byte	'certType',0,1,178,1,32
	.word	.L642,.L666
	.byte	4
	.byte	'certlen',0,1,178,1,53
	.word	.L642,.L667
	.byte	4
	.byte	'certData',0,1,178,1,73
	.word	.L647,.L668
	.byte	5
	.word	.L669
	.byte	6
	.byte	'ret',0,1,180,1,13
	.word	.L642,.L670
	.byte	6
	.byte	'buf',0,1,181,1,12
	.word	.L657,.L671
	.byte	6
	.byte	'certId',0,1,182,1,13
	.word	.L642,.L672
	.byte	6
	.byte	'offset',0,1,183,1,13
	.word	.L642,.L673
	.byte	6
	.byte	'noutlen',0,1,184,1,13
	.word	.L642,.L674
	.byte	6
	.byte	'readlen',0,1,185,1,13
	.word	.L642,.L675
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SaveCert')
	.sect	'.debug_abbrev'
.L423:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SaveCert')
	.sect	'.debug_line'
.L424:
	.word	.L1155-.L1154
.L1154:
	.half	3
	.word	.L1157-.L1156
.L1156:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1157:
	.byte	5,12,7,0,5,2
	.word	.L339
	.byte	3,177,1,1,5,22,9
	.half	.L875-.L339
	.byte	3,6,1,5,21,1,5,2,9
	.half	.L1158-.L875
	.byte	3,3,1,5,6,7,9
	.half	.L1159-.L1158
	.byte	3,3,1,5,2,9
	.half	.L1160-.L1159
	.byte	1,5,23,7,9
	.half	.L877-.L1160
	.byte	3,3,1,5,15,9
	.half	.L876-.L877
	.byte	3,1,1,5,2,9
	.half	.L1161-.L876
	.byte	1,5,10,7,9
	.half	.L52-.L1161
	.byte	3,1,1,5,1,3,49,1,5,8,7,9
	.half	.L53-.L52
	.byte	3,83,1,7,9
	.half	.L1162-.L53
	.byte	3,4,1,7,9
	.half	.L878-.L1162
	.byte	1,5,44,9
	.half	.L55-.L878
	.byte	3,125,1,5,12,9
	.half	.L1163-.L55
	.byte	3,1,1,5,35,9
	.half	.L879-.L1163
	.byte	3,127,1,5,4,3,2,1,5,11,9
	.half	.L56-.L879
	.byte	3,2,1,5,12,9
	.half	.L880-.L56
	.byte	3,1,1,5,4,3,1,1,5,11,9
	.half	.L51-.L880
	.byte	3,2,1,5,1,3,36,1,5,25,7,9
	.half	.L58-.L51
	.byte	3,95,1,5,2,9
	.half	.L881-.L58
	.byte	1,5,10,7,9
	.half	.L882-.L881
	.byte	3,2,1,5,1,3,31,1,5,62,7,9
	.half	.L61-.L882
	.byte	3,100,1,5,75,9
	.half	.L1164-.L61
	.byte	1,5,103,9
	.half	.L874-.L1164
	.byte	1,5,109,9
	.half	.L883-.L874
	.byte	1,5,2,9
	.half	.L884-.L883
	.byte	3,1,1,5,18,7,9
	.half	.L1165-.L884
	.byte	3,5,1,5,57,9
	.half	.L887-.L1165
	.byte	1,5,65,1,5,2,9
	.half	.L889-.L887
	.byte	3,1,1,5,8,7,9
	.half	.L1166-.L889
	.byte	3,5,1,5,18,9
	.half	.L1167-.L1166
	.byte	1,5,42,1,5,47,9
	.half	.L1168-.L1167
	.byte	1,5,2,9
	.half	.L891-.L1168
	.byte	3,1,1,5,10,7,9
	.half	.L1169-.L891
	.byte	3,2,1,5,1,9
	.half	.L65-.L1169
	.byte	3,13,1,5,2,7,9
	.half	.L66-.L65
	.byte	3,119,1,5,12,7,9
	.half	.L1170-.L66
	.byte	3,2,1,5,37,9
	.half	.L892-.L1170
	.byte	1,5,52,1,5,12,9
	.half	.L68-.L892
	.byte	3,3,1,5,37,9
	.half	.L893-.L68
	.byte	1,5,9,9
	.half	.L894-.L893
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L426-.L894
	.byte	0,1,1
.L1155:
	.sdecl	'.debug_ranges',debug,cluster('SaveCert')
	.sect	'.debug_ranges'
.L425:
	.word	-1,.L339,0,.L426-.L339,0,0
.L669:
	.word	-1,.L339,0,.L665-.L339,-1,.L341,0,.L586-.L341,-1,.L343,0,.L566-.L343,0,0
	.sdecl	'.debug_info',debug,cluster('GenSymmKey')
	.sect	'.debug_info'
.L427:
	.word	505
	.half	3
	.word	.L428
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L430,.L429
	.byte	2
	.word	.L408
	.byte	3
	.byte	'GenSymmKey',0,1,246,1,12
	.word	.L642
	.byte	1,1,1
	.word	.L345,.L676,.L344
	.byte	4
	.byte	'code',0,1,246,1,34
	.word	.L647,.L677
	.byte	4
	.byte	'keyList',0,1,246,1,51
	.word	.L678,.L679
	.byte	5
	.word	.L345,.L676
	.byte	6
	.byte	'vss_div',0,1,248,1,12
	.word	.L660,.L680
	.byte	6
	.byte	'sn',0,1,249,1,12
	.word	.L681,.L682
	.byte	6
	.byte	'key',0,1,250,1,12
	.word	.L660,.L683
	.byte	6
	.byte	'i',0,1,251,1,12
	.word	.L684,.L685
	.byte	6
	.byte	'k',0,1,252,1,12
	.word	.L684,.L686
	.byte	6
	.byte	'flag',0,1,253,1,12
	.word	.L684,.L687
	.byte	6
	.byte	'ret',0,1,254,1,13
	.word	.L642,.L688
	.byte	6
	.byte	'mk',0,1,255,1,12
	.word	.L660,.L689
	.byte	6
	.byte	'keyatb',0,1,128,2,12
	.word	.L690,.L691
	.byte	6
	.byte	'cfgData',0,1,129,2,12
	.word	.L692,.L693
	.byte	6
	.byte	'backup',0,1,130,2,12
	.word	.L694,.L695
	.byte	6
	.byte	'noutlen',0,1,131,2,13
	.word	.L642,.L696
	.byte	6
	.byte	'key_off',0,1,132,2,13
	.word	.L642,.L697
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('GenSymmKey')
	.sect	'.debug_abbrev'
.L428:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('GenSymmKey')
	.sect	'.debug_line'
.L429:
	.word	.L1172-.L1171
.L1171:
	.half	3
	.word	.L1174-.L1173
.L1173:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1174:
	.byte	5,12,7,0,5,2
	.word	.L345
	.byte	3,245,1,1,5,21,9
	.half	.L900-.L345
	.byte	3,13,1,5,20,1,5,5,9
	.half	.L901-.L900
	.byte	3,3,1,5,33,7,9
	.half	.L1175-.L901
	.byte	1,5,10,7,9
	.half	.L71-.L1175
	.byte	3,1,1,5,1,3,205,0,1,5,6,7,9
	.half	.L72-.L71
	.byte	3,181,127,1,5,2,9
	.half	.L1176-.L72
	.byte	1,5,10,7,9
	.half	.L1177-.L1176
	.byte	3,1,1,5,1,3,202,0,1,5,20,7,9
	.half	.L74-.L1177
	.byte	3,184,127,1,5,6,9
	.half	.L898-.L74
	.byte	1,5,2,9
	.half	.L904-.L898
	.byte	3,1,1,5,8,7,9
	.half	.L1178-.L904
	.byte	3,5,1,5,18,9
	.half	.L1179-.L1178
	.byte	1,5,55,1,5,33,9
	.half	.L1180-.L1179
	.byte	1,5,76,9
	.half	.L1181-.L1180
	.byte	1,5,2,9
	.half	.L905-.L1181
	.byte	3,1,1,5,10,7,9
	.half	.L1182-.L905
	.byte	3,2,1,5,1,3,63,1,5,15,7,9
	.half	.L77-.L1182
	.byte	3,68,1,5,11,1,5,18,9
	.half	.L1183-.L77
	.byte	1,5,9,9
	.half	.L903-.L1183
	.byte	3,1,1,5,25,1,5,20,9
	.half	.L906-.L903
	.byte	1,5,5,9
	.half	.L79-.L906
	.byte	3,2,1,5,15,9
	.half	.L1184-.L79
	.byte	1,5,25,9
	.half	.L1185-.L1184
	.byte	1,5,23,9
	.half	.L1186-.L1185
	.byte	3,126,1,5,19,9
	.half	.L1187-.L1186
	.byte	3,2,1,5,9,1,5,20,3,126,1,5,15,7,9
	.half	.L1188-.L1187
	.byte	3,5,1,5,13,1,5,30,9
	.half	.L907-.L1188
	.byte	3,1,1,5,44,9
	.half	.L80-.L907
	.byte	3,2,1,5,53,9
	.half	.L1189-.L80
	.byte	1,5,12,1,5,44,9
	.half	.L1190-.L1189
	.byte	1,5,53,9
	.half	.L1191-.L1190
	.byte	1,5,23,9
	.half	.L1192-.L1191
	.byte	3,1,1,5,17,3,2,1,5,23,9
	.half	.L1193-.L1192
	.byte	3,126,1,5,25,9
	.half	.L1194-.L1193
	.byte	1,5,21,7,9
	.half	.L1195-.L1194
	.byte	3,2,1,5,38,1,5,21,9
	.half	.L81-.L1195
	.byte	3,2,1,5,10,9
	.half	.L82-.L81
	.byte	3,4,1,5,11,9
	.half	.L1196-.L82
	.byte	3,1,1,5,22,3,2,1,5,21,9
	.half	.L909-.L1196
	.byte	3,126,1,5,12,9
	.half	.L83-.L909
	.byte	3,2,1,5,24,9
	.half	.L1197-.L83
	.byte	1,9
	.half	.L1198-.L1197
	.byte	3,126,1,5,20,3,2,1,5,22,9
	.half	.L1199-.L1198
	.byte	3,1,1,5,20,1,5,21,3,125,1,5,24,7,9
	.half	.L1200-.L1199
	.byte	3,6,1,5,15,1,5,36,9
	.half	.L1201-.L1200
	.byte	1,5,28,1,5,32,9
	.half	.L1202-.L1201
	.byte	1,5,42,1,5,39,9
	.half	.L1203-.L1202
	.byte	1,5,48,1,5,35,9
	.half	.L910-.L1203
	.byte	3,3,1,5,40,9
	.half	.L1204-.L910
	.byte	1,5,7,9
	.half	.L911-.L1204
	.byte	1,5,3,9
	.half	.L908-.L911
	.byte	3,1,1,5,13,7,9
	.half	.L1205-.L908
	.byte	3,5,1,5,3,9
	.half	.L1206-.L1205
	.byte	1,5,12,7,9
	.half	.L1207-.L1206
	.byte	3,4,1,5,4,9
	.half	.L1208-.L1207
	.byte	3,126,1,5,30,7,9
	.half	.L1209-.L1208
	.byte	3,2,1,5,24,1,5,37,9
	.half	.L1210-.L1209
	.byte	3,1,1,5,35,9
	.half	.L1211-.L1210
	.byte	1,5,20,9
	.half	.L1212-.L1211
	.byte	3,127,1,5,12,9
	.half	.L1213-.L1212
	.byte	3,1,1,5,30,9
	.half	.L1214-.L1213
	.byte	1,5,40,3,127,1,5,30,9
	.half	.L86-.L1214
	.byte	3,3,1,5,24,9
	.half	.L87-.L86
	.byte	1,5,20,9
	.half	.L1215-.L87
	.byte	1,5,11,9
	.half	.L902-.L1215
	.byte	3,2,1,5,19,9
	.half	.L1216-.L902
	.byte	1,5,25,9
	.half	.L914-.L1216
	.byte	1,5,23,1,5,30,9
	.half	.L85-.L914
	.byte	3,92,1,5,33,9
	.half	.L1217-.L85
	.byte	1,5,30,9
	.half	.L1218-.L1217
	.byte	1,5,2,9
	.half	.L84-.L1218
	.byte	3,40,1,5,3,7,9
	.half	.L1219-.L84
	.byte	3,2,1,5,13,9
	.half	.L1220-.L1219
	.byte	1,5,51,1,5,33,9
	.half	.L1221-.L1220
	.byte	3,73,1,5,76,9
	.half	.L1222-.L1221
	.byte	1,5,72,3,55,1,5,90,9
	.half	.L912-.L1222
	.byte	1,5,10,9
	.half	.L88-.L912
	.byte	3,2,1,5,3,9
	.half	.L1223-.L88
	.byte	1,5,17,7,9
	.half	.L1224-.L1223
	.byte	3,1,1,5,24,1,5,17,9
	.half	.L90-.L1224
	.byte	3,2,1,5,8,9
	.half	.L913-.L90
	.byte	1,5,19,3,2,1,5,15,9
	.half	.L915-.L913
	.byte	1,5,19,1,5,2,9
	.half	.L76-.L915
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L431-.L76
	.byte	0,1,1
.L1172:
	.sdecl	'.debug_ranges',debug,cluster('GenSymmKey')
	.sect	'.debug_ranges'
.L430:
	.word	-1,.L345,0,.L431-.L345,0,0
	.sdecl	'.debug_info',debug,cluster('GetSessKey')
	.sect	'.debug_info'
.L432:
	.word	287
	.half	3
	.word	.L433
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L435,.L434
	.byte	2
	.word	.L408
	.byte	3
	.byte	'GetSessKey',0,1,209,3,12
	.word	.L642
	.byte	1,1,1
	.word	.L355,.L698,.L354
	.byte	4
	.byte	'keyId',0,1,209,3,34
	.word	.L642,.L699
	.byte	4
	.byte	'keyLen',0,1,209,3,54
	.word	.L645,.L700
	.byte	4
	.byte	'keyData',0,1,209,3,73
	.word	.L647,.L701
	.byte	5
	.word	.L355,.L698
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('GetSessKey')
	.sect	'.debug_abbrev'
.L433:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('GetSessKey')
	.sect	'.debug_line'
.L434:
	.word	.L1226-.L1225
.L1225:
	.half	3
	.word	.L1228-.L1227
.L1227:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1228:
	.byte	5,2,7,0,5,2
	.word	.L355
	.byte	3,210,3,1,5,10,7,9
	.half	.L1229-.L355
	.byte	3,1,1,5,1,3,3,1,5,26,7,9
	.half	.L136-.L1229
	.byte	3,127,1,5,50,9
	.half	.L942-.L136
	.byte	1,5,1,7,9
	.half	.L436-.L942
	.byte	3,1,0,1,1
.L1226:
	.sdecl	'.debug_ranges',debug,cluster('GetSessKey')
	.sect	'.debug_ranges'
.L435:
	.word	-1,.L355,0,.L436-.L355,0,0
	.sdecl	'.debug_info',debug,cluster('GetSymmKey')
	.sect	'.debug_info'
.L437:
	.word	322
	.half	3
	.word	.L438
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L440,.L439
	.byte	2
	.word	.L408
	.byte	3
	.byte	'GetSymmKey',0,1,217,3,12
	.word	.L642
	.byte	1,1,1
	.word	.L357,.L702,.L356
	.byte	4
	.byte	'keyId',0,1,217,3,34
	.word	.L642,.L703
	.byte	4
	.byte	'keyLen',0,1,217,3,54
	.word	.L645,.L704
	.byte	4
	.byte	'keyData',0,1,217,3,73
	.word	.L647,.L705
	.byte	5
	.word	.L357,.L702
	.byte	6
	.byte	'ret',0,1,219,3,13
	.word	.L642,.L706
	.byte	6
	.byte	'key',0,1,220,3,15
	.word	.L707,.L708
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('GetSymmKey')
	.sect	'.debug_abbrev'
.L438:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('GetSymmKey')
	.sect	'.debug_line'
.L439:
	.word	.L1231-.L1230
.L1230:
	.half	3
	.word	.L1233-.L1232
.L1232:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1233:
	.byte	5,12,7,0,5,2
	.word	.L357
	.byte	3,216,3,1,5,6,9
	.half	.L947-.L357
	.byte	3,5,1,5,36,7,9
	.half	.L1234-.L947
	.byte	1,5,10,7,9
	.half	.L139-.L1234
	.byte	3,1,1,5,1,3,24,1,5,14,7,9
	.half	.L140-.L139
	.byte	3,106,1,5,2,9
	.half	.L1235-.L140
	.byte	1,5,10,7,9
	.half	.L1236-.L1235
	.byte	3,1,1,5,1,3,21,1,5,28,7,9
	.half	.L142-.L1236
	.byte	3,109,1,5,6,9
	.half	.L944-.L142
	.byte	1,5,2,9
	.half	.L949-.L944
	.byte	3,1,1,5,8,7,9
	.half	.L1237-.L949
	.byte	3,3,1,5,2,9
	.half	.L1238-.L1237
	.byte	1,5,3,7,9
	.half	.L1239-.L1238
	.byte	3,2,1,5,17,7,9
	.half	.L1240-.L1239
	.byte	3,2,1,5,12,9
	.half	.L1241-.L1240
	.byte	1,5,25,9
	.half	.L1242-.L1241
	.byte	3,1,1,5,33,9
	.half	.L1243-.L1242
	.byte	1,5,25,9
	.half	.L948-.L1243
	.byte	3,127,1,5,15,9
	.half	.L146-.L948
	.byte	3,3,1,5,13,1,5,22,9
	.half	.L1244-.L146
	.byte	3,1,1,5,37,9
	.half	.L1245-.L1244
	.byte	1,5,4,9
	.half	.L951-.L1245
	.byte	3,126,1,5,7,9
	.half	.L145-.L951
	.byte	3,5,1,5,2,9
	.half	.L144-.L145
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L441-.L144
	.byte	0,1,1
.L1231:
	.sdecl	'.debug_ranges',debug,cluster('GetSymmKey')
	.sect	'.debug_ranges'
.L440:
	.word	-1,.L357,0,.L441-.L357,0,0
	.sdecl	'.debug_info',debug,cluster('SetKeyActive')
	.sect	'.debug_info'
.L442:
	.word	302
	.half	3
	.word	.L443
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L445,.L444
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SetKeyActive',0,1,249,3,12
	.word	.L642
	.byte	1,1,1
	.word	.L359,.L709,.L358
	.byte	4
	.byte	'keyId',0,1,249,3,36
	.word	.L642,.L710
	.byte	4
	.byte	'valid',0,1,249,3,54
	.word	.L642,.L711
	.byte	5
	.word	.L359,.L709
	.byte	6
	.byte	'ret',0,1,251,3,13
	.word	.L642,.L712
	.byte	6
	.byte	'key',0,1,252,3,15
	.word	.L707,.L713
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SetKeyActive')
	.sect	'.debug_abbrev'
.L443:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SetKeyActive')
	.sect	'.debug_line'
.L444:
	.word	.L1247-.L1246
.L1246:
	.half	3
	.word	.L1249-.L1248
.L1248:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1249:
	.byte	5,12,7,0,5,2
	.word	.L359
	.byte	3,248,3,1,5,15,9
	.half	.L953-.L359
	.byte	3,5,1,5,2,9
	.half	.L955-.L953
	.byte	1,5,10,7,9
	.half	.L956-.L955
	.byte	3,1,1,5,1,3,29,1,5,28,7,9
	.half	.L150-.L956
	.byte	3,101,1,5,2,9
	.half	.L954-.L150
	.byte	3,1,1,5,8,7,9
	.half	.L1250-.L954
	.byte	3,3,1,5,2,9
	.half	.L1251-.L1250
	.byte	1,5,3,7,9
	.half	.L958-.L1251
	.byte	3,2,1,5,4,7,9
	.half	.L959-.L958
	.byte	3,2,1,5,15,7,9
	.half	.L1252-.L959
	.byte	3,3,1,5,56,9
	.half	.L960-.L1252
	.byte	3,1,1,5,43,9
	.half	.L961-.L960
	.byte	1,5,56,9
	.half	.L1253-.L961
	.byte	1,5,4,7,9
	.half	.L154-.L1253
	.byte	3,3,1,5,15,7,9
	.half	.L1254-.L154
	.byte	3,2,1,5,56,9
	.half	.L962-.L1254
	.byte	3,1,1,5,43,9
	.half	.L963-.L962
	.byte	1,5,56,9
	.half	.L1255-.L963
	.byte	1,5,10,7,9
	.half	.L153-.L1255
	.byte	3,5,1,5,1,9
	.half	.L152-.L153
	.byte	3,4,1,7,9
	.half	.L446-.L152
	.byte	0,1,1
.L1247:
	.sdecl	'.debug_ranges',debug,cluster('SetKeyActive')
	.sect	'.debug_ranges'
.L445:
	.word	-1,.L359,0,.L446-.L359,0,0
	.sdecl	'.debug_info',debug,cluster('GetKeyActive')
	.sect	'.debug_info'
.L447:
	.word	302
	.half	3
	.word	.L448
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L450,.L449
	.byte	2
	.word	.L408
	.byte	3
	.byte	'GetKeyActive',0,1,158,4,12
	.word	.L642
	.byte	1,1,1
	.word	.L361,.L714,.L360
	.byte	4
	.byte	'keyId',0,1,158,4,36
	.word	.L642,.L715
	.byte	4
	.byte	'valid',0,1,158,4,54
	.word	.L647,.L716
	.byte	5
	.word	.L361,.L714
	.byte	6
	.byte	'ret',0,1,160,4,13
	.word	.L642,.L717
	.byte	6
	.byte	'key',0,1,161,4,15
	.word	.L707,.L718
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('GetKeyActive')
	.sect	'.debug_abbrev'
.L448:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('GetKeyActive')
	.sect	'.debug_line'
.L449:
	.word	.L1257-.L1256
.L1256:
	.half	3
	.word	.L1259-.L1258
.L1258:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1259:
	.byte	5,12,7,0,5,2
	.word	.L361
	.byte	3,157,4,1,5,2,9
	.half	.L967-.L361
	.byte	3,5,1,5,10,7,9
	.half	.L1260-.L967
	.byte	3,1,1,5,1,3,22,1,5,15,7,9
	.half	.L161-.L1260
	.byte	3,108,1,5,2,9
	.half	.L1261-.L161
	.byte	1,5,10,7,9
	.half	.L1262-.L1261
	.byte	3,1,1,5,1,3,19,1,5,28,7,9
	.half	.L163-.L1262
	.byte	3,111,1,5,2,9
	.half	.L965-.L163
	.byte	3,1,1,5,8,7,9
	.half	.L1263-.L965
	.byte	3,3,1,5,2,9
	.half	.L1264-.L1263
	.byte	1,5,3,7,9
	.half	.L1265-.L1264
	.byte	3,2,1,5,13,7,9
	.half	.L1266-.L1265
	.byte	3,2,1,5,11,1,5,1,9
	.half	.L165-.L1266
	.byte	3,9,1,5,13,7,9
	.half	.L167-.L165
	.byte	3,121,1,5,11,1,5,1,9
	.half	.L1267-.L167
	.byte	3,7,1,5,10,7,9
	.half	.L166-.L1267
	.byte	3,124,1,5,1,3,4,1,7,9
	.half	.L451-.L166
	.byte	0,1,1
.L1257:
	.sdecl	'.debug_ranges',debug,cluster('GetKeyActive')
	.sect	'.debug_ranges'
.L450:
	.word	-1,.L361,0,.L451-.L361,0,0
	.sdecl	'.debug_info',debug,cluster('SetSessKey')
	.sect	'.debug_info'
.L452:
	.word	287
	.half	3
	.word	.L453
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L455,.L454
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SetSessKey',0,1,189,4,12
	.word	.L642
	.byte	1,1,1
	.word	.L363,.L719,.L362
	.byte	4
	.byte	'keyId',0,1,189,4,34
	.word	.L642,.L720
	.byte	4
	.byte	'keyLen',0,1,189,4,52
	.word	.L642,.L721
	.byte	4
	.byte	'keyData',0,1,189,4,71
	.word	.L647,.L722
	.byte	5
	.word	.L363,.L719
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SetSessKey')
	.sect	'.debug_abbrev'
.L453:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SetSessKey')
	.sect	'.debug_line'
.L454:
	.word	.L1269-.L1268
.L1268:
	.half	3
	.word	.L1271-.L1270
.L1270:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1271:
	.byte	5,12,7,0,5,2
	.word	.L363
	.byte	3,188,4,1,5,2,9
	.half	.L970-.L363
	.byte	3,4,1,5,10,7,9
	.half	.L1272-.L970
	.byte	3,1,1,5,1,3,4,1,5,26,7,9
	.half	.L172-.L1272
	.byte	3,127,1,5,42,9
	.half	.L968-.L172
	.byte	1,5,56,9
	.half	.L969-.L968
	.byte	1,5,1,7,9
	.half	.L456-.L969
	.byte	3,1,0,1,1
.L1269:
	.sdecl	'.debug_ranges',debug,cluster('SetSessKey')
	.sect	'.debug_ranges'
.L455:
	.word	-1,.L363,0,.L456-.L363,0,0
	.sdecl	'.debug_info',debug,cluster('SetSymmKey')
	.sect	'.debug_info'
.L457:
	.word	377
	.half	3
	.word	.L458
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L460,.L459
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SetSymmKey',0,1,200,4,12
	.word	.L642
	.byte	1,1,1
	.word	.L365,.L723,.L364
	.byte	4
	.byte	'keyId',0,1,200,4,34
	.word	.L642,.L724
	.byte	4
	.byte	'flag',0,1,200,4,52
	.word	.L642,.L725
	.byte	4
	.byte	'keyLen',0,1,200,4,69
	.word	.L642,.L726
	.byte	4
	.byte	'keyData',0,1,200,4,88
	.word	.L647,.L727
	.byte	5
	.word	.L365,.L723
	.byte	6
	.byte	'ret',0,1,202,4,13
	.word	.L642,.L728
	.byte	6
	.byte	'noutlen',0,1,203,4,13
	.word	.L642,.L729
	.byte	6
	.byte	'cv',0,1,204,4,12
	.word	.L660,.L730
	.byte	6
	.byte	'key',0,1,205,4,15
	.word	.L707,.L731
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SetSymmKey')
	.sect	'.debug_abbrev'
.L458:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SetSymmKey')
	.sect	'.debug_line'
.L459:
	.word	.L1274-.L1273
.L1273:
	.half	3
	.word	.L1276-.L1275
.L1275:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1276:
	.byte	5,12,7,0,5,2
	.word	.L365
	.byte	3,199,4,1,5,22,9
	.half	.L971-.L365
	.byte	3,3,1,5,21,1,5,2,9
	.half	.L1277-.L971
	.byte	3,4,1,5,10,7,9
	.half	.L1278-.L1277
	.byte	3,1,1,5,1,3,31,1,5,14,7,9
	.half	.L175-.L1278
	.byte	3,99,1,5,2,9
	.half	.L976-.L175
	.byte	1,5,10,7,9
	.half	.L977-.L976
	.byte	3,1,1,5,1,3,28,1,5,16,7,9
	.half	.L177-.L977
	.byte	3,102,1,5,6,9
	.half	.L1279-.L177
	.byte	1,5,32,7,9
	.half	.L1280-.L1279
	.byte	1,5,29,9
	.half	.L1281-.L1280
	.byte	1,5,48,7,9
	.half	.L1282-.L1281
	.byte	1,5,45,9
	.half	.L1283-.L1282
	.byte	1,5,60,7,9
	.half	.L1284-.L1283
	.byte	1,5,75,7,9
	.half	.L1285-.L1284
	.byte	1,5,10,7,9
	.half	.L1286-.L1285
	.byte	3,1,1,5,1,3,25,1,5,29,7,9
	.half	.L179-.L1286
	.byte	3,105,1,5,24,1,5,32,9
	.half	.L973-.L179
	.byte	1,5,12,9
	.half	.L972-.L973
	.byte	3,1,1,5,22,9
	.half	.L978-.L972
	.byte	3,3,1,5,13,3,126,1,5,28,9
	.half	.L979-.L978
	.byte	3,2,1,5,14,3,127,1,9
	.half	.L1287-.L979
	.byte	3,1,1,5,28,9
	.half	.L1288-.L1287
	.byte	1,5,14,9
	.half	.L1289-.L1288
	.byte	3,1,1,5,31,9
	.half	.L1290-.L1289
	.byte	1,5,41,9
	.half	.L981-.L1290
	.byte	3,1,1,5,31,1,5,60,9
	.half	.L1291-.L981
	.byte	1,5,31,1,5,56,9
	.half	.L1292-.L1291
	.byte	1,5,48,1,5,70,9
	.half	.L1293-.L1292
	.byte	1,5,84,1,5,89,9
	.half	.L1294-.L1293
	.byte	1,5,2,9
	.half	.L982-.L1294
	.byte	3,1,1,5,10,7,9
	.half	.L1295-.L982
	.byte	3,1,1,5,1,3,15,1,5,14,7,9
	.half	.L185-.L1295
	.byte	3,115,1,5,19,9
	.half	.L1296-.L185
	.byte	1,5,23,9
	.half	.L1297-.L1296
	.byte	1,5,28,9
	.half	.L983-.L1297
	.byte	3,1,1,5,2,9
	.half	.L985-.L983
	.byte	3,1,1,5,10,7,9
	.half	.L1298-.L985
	.byte	3,1,1,5,1,3,10,1,5,2,7,9
	.half	.L187-.L1298
	.byte	3,120,1,5,25,7,9
	.half	.L987-.L187
	.byte	3,2,1,5,12,1,5,24,9
	.half	.L1299-.L987
	.byte	1,5,67,9
	.half	.L1300-.L1299
	.byte	1,5,31,9
	.half	.L189-.L1300
	.byte	3,2,1,5,12,1,5,24,9
	.half	.L1301-.L189
	.byte	1,5,61,9
	.half	.L190-.L1301
	.byte	1,5,66,9
	.half	.L1302-.L190
	.byte	1,5,9,9
	.half	.L986-.L1302
	.byte	3,3,1,5,1,3,1,1,7,9
	.half	.L461-.L986
	.byte	0,1,1
.L1274:
	.sdecl	'.debug_ranges',debug,cluster('SetSymmKey')
	.sect	'.debug_ranges'
.L460:
	.word	-1,.L365,0,.L461-.L365,0,0
	.sdecl	'.debug_info',debug,cluster('GenAsymmKey')
	.sect	'.debug_info'
.L462:
	.word	348
	.half	3
	.word	.L463
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L465,.L464
	.byte	2
	.word	.L408
	.byte	3
	.byte	'GenAsymmKey',0,1,241,4,12
	.word	.L642
	.byte	1,1,1
	.word	.L367,.L732,.L366
	.byte	4
	.byte	'x',0,1,241,4,35
	.word	.L647,.L733
	.byte	4
	.byte	'y',0,1,241,4,49
	.word	.L647,.L734
	.byte	5
	.word	.L735
	.byte	6
	.byte	'ret',0,1,243,4,13
	.word	.L642,.L736
	.byte	6
	.byte	'buf',0,1,244,4,12
	.word	.L737,.L738
	.byte	6
	.byte	'mac',0,1,245,4,12
	.word	.L660,.L739
	.byte	6
	.byte	'noutlen',0,1,247,4,13
	.word	.L642,.L740
	.byte	6
	.byte	'keyData',0,1,248,4,16
	.word	.L741,.L742
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('GenAsymmKey')
	.sect	'.debug_abbrev'
.L463:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('GenAsymmKey')
	.sect	'.debug_line'
.L464:
	.word	.L1304-.L1303
.L1303:
	.half	3
	.word	.L1306-.L1305
.L1305:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1306:
	.byte	5,12,7,0,5,2
	.word	.L367
	.byte	3,240,4,1,5,22,9
	.half	.L993-.L367
	.byte	3,6,1,5,21,1,5,5,9
	.half	.L1307-.L993
	.byte	3,3,1,5,24,7,9
	.half	.L1308-.L1307
	.byte	1,5,10,7,9
	.half	.L192-.L1308
	.byte	3,1,1,5,1,3,47,1,5,6,7,9
	.half	.L193-.L192
	.byte	3,83,1,5,2,9
	.half	.L1309-.L193
	.byte	1,5,33,7,9
	.half	.L1310-.L1309
	.byte	3,3,1,5,24,1,5,36,9
	.half	.L991-.L1310
	.byte	1,5,17,9
	.half	.L990-.L991
	.byte	3,1,1,5,15,9
	.half	.L996-.L990
	.byte	1,5,21,9
	.half	.L1311-.L996
	.byte	3,1,1,5,19,1,5,18,9
	.half	.L1312-.L1311
	.byte	3,1,1,5,16,1,9
	.half	.L1313-.L1312
	.byte	3,1,1,5,17,9
	.half	.L1314-.L1313
	.byte	3,1,1,5,2,9
	.half	.L1315-.L1314
	.byte	3,2,1,5,29,7,9
	.half	.L1316-.L1315
	.byte	3,2,1,5,44,9
	.half	.L1317-.L1316
	.byte	1,5,64,9
	.half	.L1318-.L1317
	.byte	1,5,76,9
	.half	.L994-.L1318
	.byte	1,5,8,9
	.half	.L196-.L994
	.byte	3,1,1,5,29,7,9
	.half	.L1319-.L196
	.byte	3,2,1,5,44,9
	.half	.L1320-.L1319
	.byte	1,5,64,9
	.half	.L1321-.L1320
	.byte	1,5,76,9
	.half	.L995-.L1321
	.byte	1,5,10,9
	.half	.L195-.L995
	.byte	3,2,1,5,1,3,28,1,5,2,7,9
	.half	.L197-.L195
	.byte	3,102,1,5,60,7,9
	.half	.L1322-.L197
	.byte	3,3,1,5,25,9
	.half	.L1323-.L1322
	.byte	1,5,33,9
	.half	.L1324-.L1323
	.byte	1,5,77,9
	.half	.L997-.L1324
	.byte	1,5,2,9
	.half	.L1325-.L997
	.byte	3,1,1,5,18,7,9
	.half	.L1326-.L1325
	.byte	3,3,1,5,24,9
	.half	.L1327-.L1326
	.byte	1,5,29,9
	.half	.L1328-.L1327
	.byte	1,5,32,9
	.half	.L999-.L1328
	.byte	3,2,1,5,40,9
	.half	.L1329-.L999
	.byte	1,5,83,1,5,111,9
	.half	.L1330-.L1329
	.byte	1,5,117,9
	.half	.L1331-.L1330
	.byte	1,5,5,9
	.half	.L1001-.L1331
	.byte	3,1,1,5,12,7,9
	.half	.L1332-.L1001
	.byte	1,5,23,9
	.half	.L1333-.L1332
	.byte	1,5,20,9
	.half	.L1334-.L1333
	.byte	1,5,10,7,9
	.half	.L204-.L1334
	.byte	3,1,1,5,1,9
	.half	.L203-.L204
	.byte	3,15,1,5,8,7,9
	.half	.L205-.L203
	.byte	3,116,1,5,18,9
	.half	.L1335-.L205
	.byte	1,5,34,9
	.half	.L1336-.L1335
	.byte	1,5,42,1,5,47,9
	.half	.L1337-.L1336
	.byte	1,5,2,9
	.half	.L1338-.L1337
	.byte	3,1,1,5,10,7,9
	.half	.L1339-.L1338
	.byte	3,2,1,5,1,3,9,1,5,11,7,9
	.half	.L207-.L1339
	.byte	3,122,1,5,33,9
	.half	.L1002-.L207
	.byte	1,5,41,9
	.half	.L1340-.L1002
	.byte	1,5,21,9
	.half	.L1003-.L1340
	.byte	3,2,1,5,29,9
	.half	.L1341-.L1003
	.byte	1,5,21,9
	.half	.L1005-.L1341
	.byte	3,1,1,5,34,9
	.half	.L1342-.L1005
	.byte	1,5,9,9
	.half	.L1007-.L1342
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L466-.L1007
	.byte	0,1,1
.L1304:
	.sdecl	'.debug_ranges',debug,cluster('GenAsymmKey')
	.sect	'.debug_ranges'
.L465:
	.word	-1,.L367,0,.L466-.L367,0,0
.L735:
	.word	-1,.L367,0,.L732-.L367,-1,.L369,0,.L641-.L369,-1,.L371,0,.L576-.L371,0,0
	.sdecl	'.debug_info',debug,cluster('LoadAsymmKey')
	.sect	'.debug_info'
.L467:
	.word	369
	.half	3
	.word	.L468
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L470,.L469
	.byte	2
	.word	.L408
	.byte	3
	.byte	'LoadAsymmKey',0,1,172,5,12
	.word	.L642
	.byte	1,1,1
	.word	.L373,.L743,.L372
	.byte	4
	.byte	'x',0,1,172,5,36
	.word	.L647,.L744
	.byte	4
	.byte	'y',0,1,172,5,50
	.word	.L647,.L745
	.byte	4
	.byte	'sk',0,1,172,5,64
	.word	.L647,.L746
	.byte	5
	.word	.L373,.L743
	.byte	6
	.byte	'ret',0,1,174,5,13
	.word	.L642,.L747
	.byte	6
	.byte	'buf',0,1,175,5,12
	.word	.L737,.L748
	.byte	6
	.byte	'mac',0,1,176,5,12
	.word	.L660,.L749
	.byte	6
	.byte	'noutlen',0,1,178,5,13
	.word	.L642,.L750
	.byte	6
	.byte	'keyData',0,1,179,5,16
	.word	.L741,.L751
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('LoadAsymmKey')
	.sect	'.debug_abbrev'
.L468:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('LoadAsymmKey')
	.sect	'.debug_line'
.L469:
	.word	.L1344-.L1343
.L1343:
	.half	3
	.word	.L1346-.L1345
.L1345:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1346:
	.byte	5,12,7,0,5,2
	.word	.L373
	.byte	3,171,5,1,5,22,9
	.half	.L1019-.L373
	.byte	3,6,1,5,21,1,5,5,9
	.half	.L1347-.L1019
	.byte	3,3,1,5,24,7,9
	.half	.L1348-.L1347
	.byte	1,5,42,7,9
	.half	.L1349-.L1348
	.byte	1,5,10,9
	.half	.L210-.L1349
	.byte	3,1,1,5,1,3,40,1,5,14,7,9
	.half	.L212-.L210
	.byte	3,90,1,5,29,9
	.half	.L1350-.L212
	.byte	1,5,34,9
	.half	.L1011-.L1350
	.byte	1,5,2,9
	.half	.L1009-.L1011
	.byte	1,5,30,7,9
	.half	.L1351-.L1009
	.byte	3,2,1,5,29,9
	.half	.L1015-.L1351
	.byte	3,1,1,5,35,9
	.half	.L1352-.L1015
	.byte	1,5,30,9
	.half	.L1017-.L1352
	.byte	3,1,1,5,3,9
	.half	.L1353-.L1017
	.byte	3,2,1,5,6,9
	.half	.L214-.L1353
	.byte	3,3,1,5,2,9
	.half	.L1013-.L214
	.byte	1,5,10,7,9
	.half	.L1354-.L1013
	.byte	3,1,1,5,1,3,28,1,5,18,7,9
	.half	.L216-.L1354
	.byte	3,103,1,5,33,9
	.half	.L1355-.L216
	.byte	1,5,41,1,5,46,9
	.half	.L1356-.L1355
	.byte	1,5,2,9
	.half	.L1022-.L1356
	.byte	3,1,1,5,10,7,9
	.half	.L1357-.L1022
	.byte	3,2,1,5,1,3,22,1,5,54,7,9
	.half	.L218-.L1357
	.byte	3,109,1,5,71,9
	.half	.L1023-.L218
	.byte	1,5,115,1,5,24,9
	.half	.L1358-.L1023
	.byte	1,5,19,1,5,67,9
	.half	.L1359-.L1358
	.byte	1,5,105,1,5,81,9
	.half	.L1360-.L1359
	.byte	1,5,115,1,5,5,9
	.half	.L1361-.L1360
	.byte	3,1,1,5,12,7,9
	.half	.L1362-.L1361
	.byte	1,5,23,9
	.half	.L1363-.L1362
	.byte	1,5,20,9
	.half	.L1364-.L1363
	.byte	1,5,10,7,9
	.half	.L220-.L1364
	.byte	3,1,1,5,1,3,17,1,5,12,7,9
	.half	.L221-.L220
	.byte	3,113,1,5,2,9
	.half	.L1365-.L221
	.byte	1,5,10,7,9
	.half	.L1366-.L1365
	.byte	3,1,1,5,1,3,14,1,5,25,7,9
	.half	.L223-.L1366
	.byte	3,116,1,5,33,9
	.half	.L1367-.L223
	.byte	1,5,77,9
	.half	.L1368-.L1367
	.byte	1,5,5,9
	.half	.L1369-.L1368
	.byte	3,1,1,5,28,7,9
	.half	.L1370-.L1369
	.byte	1,5,34,9
	.half	.L1371-.L1370
	.byte	1,5,39,9
	.half	.L1372-.L1371
	.byte	1,5,20,9
	.half	.L1024-.L1372
	.byte	1,5,10,7,9
	.half	.L225-.L1024
	.byte	3,1,1,5,1,3,10,1,5,33,7,9
	.half	.L226-.L225
	.byte	3,121,1,5,41,9
	.half	.L1373-.L226
	.byte	1,5,21,9
	.half	.L1374-.L1373
	.byte	3,2,1,5,29,9
	.half	.L1375-.L1374
	.byte	1,5,21,9
	.half	.L1026-.L1375
	.byte	3,1,1,5,34,9
	.half	.L1376-.L1026
	.byte	1,5,22,9
	.half	.L1028-.L1376
	.byte	3,1,1,5,35,9
	.half	.L215-.L1028
	.byte	1,5,9,9
	.half	.L1030-.L215
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L471-.L1030
	.byte	0,1,1
.L1344:
	.sdecl	'.debug_ranges',debug,cluster('LoadAsymmKey')
	.sect	'.debug_ranges'
.L470:
	.word	-1,.L373,0,.L471-.L373,0,0
	.sdecl	'.debug_info',debug,cluster('SaveAsymmKey')
	.sect	'.debug_info'
.L472:
	.word	369
	.half	3
	.word	.L473
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L475,.L474
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SaveAsymmKey',0,1,224,5,12
	.word	.L642
	.byte	1,1,1
	.word	.L375,.L752,.L374
	.byte	4
	.byte	'x',0,1,224,5,36
	.word	.L647,.L753
	.byte	4
	.byte	'y',0,1,224,5,50
	.word	.L647,.L754
	.byte	4
	.byte	'sk',0,1,224,5,64
	.word	.L647,.L755
	.byte	5
	.word	.L375,.L752
	.byte	6
	.byte	'ret',0,1,226,5,13
	.word	.L642,.L756
	.byte	6
	.byte	'buf',0,1,227,5,12
	.word	.L737,.L757
	.byte	6
	.byte	'mac',0,1,228,5,12
	.word	.L660,.L758
	.byte	6
	.byte	'noutlen',0,1,230,5,13
	.word	.L642,.L759
	.byte	6
	.byte	'keyData',0,1,231,5,16
	.word	.L741,.L760
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SaveAsymmKey')
	.sect	'.debug_abbrev'
.L473:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SaveAsymmKey')
	.sect	'.debug_line'
.L474:
	.word	.L1378-.L1377
.L1377:
	.half	3
	.word	.L1380-.L1379
.L1379:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1380:
	.byte	5,12,7,0,5,2
	.word	.L375
	.byte	3,223,5,1,5,22,9
	.half	.L1044-.L375
	.byte	3,6,1,5,21,1,5,5,9
	.half	.L1381-.L1044
	.byte	3,3,1,5,24,7,9
	.half	.L1382-.L1381
	.byte	1,5,42,7,9
	.half	.L1383-.L1382
	.byte	1,5,10,7,9
	.half	.L229-.L1383
	.byte	3,1,1,5,1,3,35,1,5,6,7,9
	.half	.L231-.L229
	.byte	3,95,1,5,2,9
	.half	.L1384-.L231
	.byte	1,5,10,7,9
	.half	.L1385-.L1384
	.byte	3,1,1,5,1,3,32,1,5,33,7,9
	.half	.L233-.L1385
	.byte	3,98,1,5,24,1,5,36,9
	.half	.L1033-.L233
	.byte	1,5,18,9
	.half	.L1032-.L1033
	.byte	3,1,1,5,29,9
	.half	.L1386-.L1032
	.byte	1,5,18,9
	.half	.L1037-.L1386
	.byte	3,1,1,5,34,9
	.half	.L1387-.L1037
	.byte	1,5,18,9
	.half	.L1039-.L1387
	.byte	3,1,1,5,35,9
	.half	.L1388-.L1039
	.byte	1,5,17,9
	.half	.L1389-.L1388
	.byte	3,2,1,5,15,9
	.half	.L1035-.L1389
	.byte	1,5,21,9
	.half	.L1390-.L1035
	.byte	3,1,1,5,19,1,5,18,9
	.half	.L1391-.L1390
	.byte	3,1,1,5,16,1,9
	.half	.L1392-.L1391
	.byte	3,1,1,5,17,9
	.half	.L1393-.L1392
	.byte	3,1,1,5,60,9
	.half	.L1394-.L1393
	.byte	3,2,1,5,25,9
	.half	.L1395-.L1394
	.byte	1,5,33,9
	.half	.L1396-.L1395
	.byte	1,5,77,9
	.half	.L1397-.L1396
	.byte	1,5,2,9
	.half	.L1042-.L1397
	.byte	3,1,1,5,10,7,9
	.half	.L1398-.L1042
	.byte	3,1,1,5,1,3,17,1,5,18,7,9
	.half	.L235-.L1398
	.byte	3,112,1,5,24,9
	.half	.L1399-.L235
	.byte	1,5,29,9
	.half	.L1400-.L1399
	.byte	1,5,32,9
	.half	.L1043-.L1400
	.byte	3,2,1,5,41,9
	.half	.L1401-.L1043
	.byte	1,5,84,1,5,112,9
	.half	.L1402-.L1401
	.byte	1,5,118,9
	.half	.L1403-.L1402
	.byte	1,5,5,9
	.half	.L1046-.L1403
	.byte	3,1,1,5,12,7,9
	.half	.L1404-.L1046
	.byte	1,5,23,9
	.half	.L1405-.L1404
	.byte	1,5,20,9
	.half	.L1406-.L1405
	.byte	1,5,10,7,9
	.half	.L237-.L1406
	.byte	3,1,1,5,1,3,12,1,5,8,7,9
	.half	.L238-.L237
	.byte	3,119,1,5,18,9
	.half	.L1407-.L238
	.byte	1,5,34,9
	.half	.L1408-.L1407
	.byte	1,5,42,1,5,47,9
	.half	.L1409-.L1408
	.byte	1,5,2,9
	.half	.L1410-.L1409
	.byte	3,1,1,5,10,7,9
	.half	.L1411-.L1410
	.byte	3,2,1,5,1,3,6,1,5,11,7,9
	.half	.L240-.L1411
	.byte	3,125,1,5,33,9
	.half	.L1047-.L240
	.byte	1,5,41,9
	.half	.L1412-.L1047
	.byte	1,5,9,9
	.half	.L1048-.L1412
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L476-.L1048
	.byte	0,1,1
.L1378:
	.sdecl	'.debug_ranges',debug,cluster('SaveAsymmKey')
	.sect	'.debug_ranges'
.L475:
	.word	-1,.L375,0,.L476-.L375,0,0
	.sdecl	'.debug_info',debug,cluster('LoadCFGData')
	.sect	'.debug_info'
.L477:
	.word	317
	.half	3
	.word	.L478
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L480,.L479
	.byte	2
	.word	.L408
	.byte	3
	.byte	'LoadCFGData',0,1,143,6,12
	.word	.L642
	.byte	1,1,1
	.word	.L377,.L761,.L376
	.byte	4
	.byte	'cfgData',0,1,143,6,35
	.word	.L647,.L762
	.byte	5
	.word	.L763
	.byte	6
	.byte	'ret',0,1,145,6,13
	.word	.L642,.L764
	.byte	6
	.byte	'tmp',0,1,146,6,12
	.word	.L737,.L765
	.byte	6
	.byte	'mac',0,1,147,6,12
	.word	.L660,.L766
	.byte	6
	.byte	'outlen',0,1,148,6,13
	.word	.L642,.L767
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('LoadCFGData')
	.sect	'.debug_abbrev'
.L478:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('LoadCFGData')
	.sect	'.debug_line'
.L479:
	.word	.L1414-.L1413
.L1413:
	.half	3
	.word	.L1416-.L1415
.L1415:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1416:
	.byte	5,12,7,0,5,2
	.word	.L377
	.byte	3,142,6,1,5,22,9
	.half	.L1051-.L377
	.byte	3,5,1,5,20,1,5,6,9
	.half	.L1050-.L1051
	.byte	3,2,1,5,2,9
	.half	.L1052-.L1050
	.byte	1,5,10,7,9
	.half	.L1417-.L1052
	.byte	3,1,1,5,1,3,21,1,5,18,7,9
	.half	.L243-.L1417
	.byte	3,109,1,5,33,9
	.half	.L1418-.L243
	.byte	1,5,51,1,5,56,9
	.half	.L1053-.L1418
	.byte	1,5,2,9
	.half	.L1054-.L1053
	.byte	3,1,1,5,10,7,9
	.half	.L1419-.L1054
	.byte	3,2,1,5,1,3,16,1,5,51,7,9
	.half	.L245-.L1419
	.byte	3,115,1,5,18,9
	.half	.L1420-.L245
	.byte	1,5,23,9
	.half	.L1421-.L1420
	.byte	1,5,68,9
	.half	.L1422-.L1421
	.byte	1,5,5,9
	.half	.L1423-.L1422
	.byte	3,1,1,5,21,7,9
	.half	.L1424-.L1423
	.byte	1,5,30,9
	.half	.L1425-.L1424
	.byte	1,5,48,9
	.half	.L1426-.L1425
	.byte	1,5,20,9
	.half	.L1055-.L1426
	.byte	1,5,10,7,9
	.half	.L247-.L1055
	.byte	3,2,1,5,1,3,10,1,5,19,7,9
	.half	.L248-.L247
	.byte	3,121,1,5,24,9
	.half	.L1427-.L248
	.byte	1,5,69,9
	.half	.L1428-.L1427
	.byte	1,5,103,1,5,79,9
	.half	.L1429-.L1428
	.byte	1,5,103,1,5,10,9
	.half	.L1057-.L1429
	.byte	3,3,1,5,1,9
	.half	.L1430-.L1057
	.byte	3,4,1,7,9
	.half	.L481-.L1430
	.byte	0,1,1
.L1414:
	.sdecl	'.debug_ranges',debug,cluster('LoadCFGData')
	.sect	'.debug_ranges'
.L480:
	.word	-1,.L377,0,.L481-.L377,0,0
.L763:
	.word	-1,.L377,0,.L761-.L377,-1,.L379,0,.L616-.L379,0,0
	.sdecl	'.debug_info',debug,cluster('SaveCFGData')
	.sect	'.debug_info'
.L482:
	.word	249
	.half	3
	.word	.L483
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L485,.L484
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SaveCFGData',0,1,174,6,12
	.word	.L642
	.byte	1,1,1
	.word	.L381,.L768,.L380
	.byte	4
	.byte	'cfgData',0,1,174,6,35
	.word	.L647,.L769
	.byte	5
	.word	.L381,.L768
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SaveCFGData')
	.sect	'.debug_abbrev'
.L483:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SaveCFGData')
	.sect	'.debug_line'
.L484:
	.word	.L1432-.L1431
.L1431:
	.half	3
	.word	.L1434-.L1433
.L1433:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1434:
	.byte	5,9,7,0,5,2
	.word	.L381
	.byte	3,204,6,1,5,1,3,2,1,7,9
	.half	.L486-.L381
	.byte	0,1,1
.L1432:
	.sdecl	'.debug_ranges',debug,cluster('SaveCFGData')
	.sect	'.debug_ranges'
.L485:
	.word	-1,.L381,0,.L486-.L381,0,0
	.sdecl	'.debug_info',debug,cluster('CryptoPaddingTo16')
	.sect	'.debug_info'
.L487:
	.word	344
	.half	3
	.word	.L488
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L490,.L489
	.byte	2
	.word	.L408
	.byte	3
	.byte	'CryptoPaddingTo16',0,1,134,7,12
	.word	.L642
	.byte	1,1,1
	.word	.L387,.L770,.L386
	.byte	4
	.byte	'in',0,1,134,7,41
	.word	.L647,.L771
	.byte	4
	.byte	'inLen',0,1,134,7,56
	.word	.L642,.L772
	.byte	4
	.byte	'force',0,1,134,7,74
	.word	.L642,.L773
	.byte	5
	.word	.L387,.L770
	.byte	6
	.byte	'i',0,1,136,7,13
	.word	.L642,.L774
	.byte	6
	.byte	'nPadLen',0,1,137,7,13
	.word	.L642,.L775
	.byte	6
	.byte	'pTemp',0,1,138,7,13
	.word	.L647,.L776
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('CryptoPaddingTo16')
	.sect	'.debug_abbrev'
.L488:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('CryptoPaddingTo16')
	.sect	'.debug_line'
.L489:
	.word	.L1436-.L1435
.L1435:
	.half	3
	.word	.L1438-.L1437
.L1437:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1438:
	.byte	5,28,7,0,5,2
	.word	.L387
	.byte	3,136,7,1,5,26,9
	.half	.L1439-.L387
	.byte	1,5,6,9
	.half	.L1070-.L1439
	.byte	3,3,1,5,31,7,9
	.half	.L1440-.L1070
	.byte	1,5,28,9
	.half	.L1441-.L1440
	.byte	1,5,3,7,9
	.half	.L1442-.L1441
	.byte	3,1,1,5,1,3,13,1,5,13,7,9
	.half	.L269-.L1442
	.byte	3,117,1,5,11,9
	.half	.L1071-.L269
	.byte	3,1,1,5,9,9
	.half	.L1443-.L1071
	.byte	1,9
	.half	.L1444-.L1443
	.byte	3,3,1,5,12,9
	.half	.L1072-.L1444
	.byte	3,1,1,5,25,3,127,1,5,10,9
	.half	.L273-.L1072
	.byte	3,1,1,5,28,9
	.half	.L1445-.L273
	.byte	3,127,1,5,25,9
	.half	.L272-.L1445
	.byte	1,5,17,7,9
	.half	.L1446-.L272
	.byte	3,5,1,5,1,3,2,1,7,9
	.half	.L491-.L1446
	.byte	0,1,1
.L1436:
	.sdecl	'.debug_ranges',debug,cluster('CryptoPaddingTo16')
	.sect	'.debug_ranges'
.L490:
	.word	-1,.L387,0,.L491-.L387,0,0
	.sdecl	'.debug_info',debug,cluster('CryptoUnPadding16')
	.sect	'.debug_info'
.L492:
	.word	324
	.half	3
	.word	.L493
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L495,.L494
	.byte	2
	.word	.L408
	.byte	3
	.byte	'CryptoUnPadding16',0,1,156,7,12
	.word	.L642
	.byte	1,1,1
	.word	.L389,.L777,.L388
	.byte	4
	.byte	'in',0,1,156,7,41
	.word	.L647,.L778
	.byte	4
	.byte	'inLen',0,1,156,7,56
	.word	.L642,.L779
	.byte	5
	.word	.L389,.L777
	.byte	6
	.byte	'i',0,1,157,7,13
	.word	.L642,.L780
	.byte	6
	.byte	'oriLen',0,1,158,7,13
	.word	.L642,.L781
	.byte	6
	.byte	'pTemp',0,1,159,7,13
	.word	.L647,.L782
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('CryptoUnPadding16')
	.sect	'.debug_abbrev'
.L493:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('CryptoUnPadding16')
	.sect	'.debug_line'
.L494:
	.word	.L1448-.L1447
.L1447:
	.half	3
	.word	.L1450-.L1449
.L1449:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1450:
	.byte	5,24,7,0,5,2
	.word	.L389
	.byte	3,158,7,1,5,9,9
	.half	.L1073-.L389
	.byte	3,2,1,5,32,9
	.half	.L1074-.L1073
	.byte	3,126,1,5,18,9
	.half	.L1451-.L1074
	.byte	3,2,1,5,20,1,5,7,9
	.half	.L276-.L1451
	.byte	3,1,1,5,3,9
	.half	.L1452-.L276
	.byte	1,5,10,7,9
	.half	.L1453-.L1452
	.byte	3,2,1,5,9,3,127,1,5,23,9
	.half	.L1454-.L1453
	.byte	3,126,1,5,11,3,2,1,5,24,9
	.half	.L277-.L1454
	.byte	3,2,1,5,10,9
	.half	.L1455-.L277
	.byte	1,5,13,7,9
	.half	.L1456-.L1455
	.byte	3,1,1,5,11,1,5,10,9
	.half	.L1457-.L1456
	.byte	3,1,1,5,4,9
	.half	.L280-.L1457
	.byte	3,1,1,5,1,3,7,1,5,20,7,9
	.half	.L275-.L280
	.byte	3,114,1,5,2,7,9
	.half	.L1458-.L275
	.byte	3,13,1,5,1,9
	.half	.L496-.L1458
	.byte	3,1,0,1,1
.L1448:
	.sdecl	'.debug_ranges',debug,cluster('CryptoUnPadding16')
	.sect	'.debug_ranges'
.L495:
	.word	-1,.L389,0,.L496-.L389,0,0
	.sdecl	'.debug_info',debug,cluster('SaveKeyCode')
	.sect	'.debug_info'
.L497:
	.word	318
	.half	3
	.word	.L498
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L500,.L499
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SaveKeyCode',0,1,209,6,12
	.word	.L642
	.byte	1,1,1
	.word	.L383,.L783,.L382
	.byte	4
	.byte	'len',0,1,209,6,35
	.word	.L642,.L784
	.byte	4
	.byte	'code',0,1,209,6,51
	.word	.L647,.L785
	.byte	5
	.word	.L383,.L783
	.byte	6
	.byte	'ret',0,1,211,6,13
	.word	.L642,.L786
	.byte	6
	.byte	'tmp',0,1,212,6,12
	.word	.L787,.L788
	.byte	6
	.byte	'outlen',0,1,213,6,13
	.word	.L642,.L789
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SaveKeyCode')
	.sect	'.debug_abbrev'
.L498:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SaveKeyCode')
	.sect	'.debug_line'
.L499:
	.word	.L1460-.L1459
.L1459:
	.half	3
	.word	.L1462-.L1461
.L1461:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1462:
	.byte	5,12,7,0,5,2
	.word	.L383
	.byte	3,208,6,1,5,22,9
	.half	.L1058-.L383
	.byte	3,4,1,5,20,1,5,6,9
	.half	.L1059-.L1058
	.byte	3,2,1,5,2,9
	.half	.L1060-.L1059
	.byte	1,5,10,7,9
	.half	.L1463-.L1060
	.byte	3,1,1,5,1,3,20,1,5,42,7,9
	.half	.L254-.L1463
	.byte	3,110,1,5,55,9
	.half	.L1464-.L254
	.byte	1,5,83,9
	.half	.L1465-.L1464
	.byte	1,5,89,9
	.half	.L1466-.L1465
	.byte	1,5,2,9
	.half	.L1061-.L1466
	.byte	3,1,1,5,10,7,9
	.half	.L1467-.L1061
	.byte	3,2,1,5,1,3,15,1,5,5,7,9
	.half	.L256-.L1467
	.byte	3,116,1,5,15,9
	.half	.L1468-.L256
	.byte	1,5,2,9
	.half	.L1469-.L1468
	.byte	1,5,10,7,9
	.half	.L1470-.L1469
	.byte	3,2,1,5,1,3,10,1,5,8,7,9
	.half	.L258-.L1470
	.byte	3,121,1,5,18,9
	.half	.L1471-.L258
	.byte	1,5,34,9
	.half	.L1472-.L1471
	.byte	1,5,56,1,5,61,9
	.half	.L1473-.L1472
	.byte	1,5,10,9
	.half	.L1474-.L1473
	.byte	3,3,1,5,1,9
	.half	.L1475-.L1474
	.byte	3,4,1,7,9
	.half	.L501-.L1475
	.byte	0,1,1
.L1460:
	.sdecl	'.debug_ranges',debug,cluster('SaveKeyCode')
	.sect	'.debug_ranges'
.L500:
	.word	-1,.L383,0,.L501-.L383,0,0
	.sdecl	'.debug_info',debug,cluster('LoadKeyCode')
	.sect	'.debug_info'
.L502:
	.word	298
	.half	3
	.word	.L503
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L505,.L504
	.byte	2
	.word	.L408
	.byte	3
	.byte	'LoadKeyCode',0,1,238,6,12
	.word	.L642
	.byte	1,1,1
	.word	.L385,.L790,.L384
	.byte	4
	.byte	'len',0,1,238,6,36
	.word	.L645,.L791
	.byte	4
	.byte	'code',0,1,238,6,52
	.word	.L647,.L792
	.byte	5
	.word	.L385,.L790
	.byte	6
	.byte	'ret',0,1,240,6,13
	.word	.L642,.L793
	.byte	6
	.byte	'tmp',0,1,241,6,12
	.word	.L787,.L794
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('LoadKeyCode')
	.sect	'.debug_abbrev'
.L503:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('LoadKeyCode')
	.sect	'.debug_line'
.L504:
	.word	.L1477-.L1476
.L1476:
	.half	3
	.word	.L1479-.L1478
.L1478:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1479:
	.byte	5,12,7,0,5,2
	.word	.L385
	.byte	3,237,6,1,5,6,9
	.half	.L1065-.L385
	.byte	3,6,1,5,2,9
	.half	.L1480-.L1065
	.byte	1,5,10,7,9
	.half	.L1481-.L1480
	.byte	3,1,1,5,1,3,15,1,5,18,7,9
	.half	.L263-.L1481
	.byte	3,115,1,5,33,9
	.half	.L1482-.L263
	.byte	1,5,55,1,5,60,9
	.half	.L1064-.L1482
	.byte	1,5,2,9
	.half	.L1063-.L1064
	.byte	3,1,1,5,19,7,9
	.half	.L1483-.L1063
	.byte	3,5,1,5,24,9
	.half	.L1484-.L1483
	.byte	1,5,103,9
	.half	.L1485-.L1484
	.byte	1,5,2,9
	.half	.L1068-.L1485
	.byte	3,1,1,5,10,7,9
	.half	.L265-.L1068
	.byte	3,2,1,5,1,3,4,1,5,9,7,9
	.half	.L266-.L265
	.byte	3,127,1,5,1,3,1,1,7,9
	.half	.L506-.L266
	.byte	0,1,1
.L1477:
	.sdecl	'.debug_ranges',debug,cluster('LoadKeyCode')
	.sect	'.debug_ranges'
.L505:
	.word	-1,.L385,0,.L506-.L385,0,0
	.sdecl	'.debug_info',debug,cluster('SetAlg')
	.sect	'.debug_info'
.L507:
	.word	254
	.half	3
	.word	.L508
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L510,.L509
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SetAlg',0,1,177,7,12
	.word	.L642
	.byte	1,1,1
	.word	.L391,.L795,.L390
	.byte	4
	.byte	'alg',0,1,177,7,29
	.word	.L684,.L796
	.byte	5
	.word	.L797
	.byte	6
	.byte	'ret',0,1,179,7,13
	.word	.L642,.L798
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SetAlg')
	.sect	'.debug_abbrev'
.L508:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SetAlg')
	.sect	'.debug_line'
.L509:
	.word	.L1487-.L1486
.L1486:
	.half	3
	.word	.L1489-.L1488
.L1488:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1489:
	.byte	5,12,7,0,5,2
	.word	.L391
	.byte	3,176,7,1,5,17,9
	.half	.L1490-.L391
	.byte	3,2,1,5,5,9
	.half	.L1076-.L1490
	.byte	3,2,1,5,26,9
	.half	.L1491-.L1076
	.byte	1,5,10,7,9
	.half	.L1492-.L1491
	.byte	3,1,1,5,1,3,20,1,5,6,7,9
	.half	.L282-.L1492
	.byte	3,110,1,5,2,9
	.half	.L1077-.L282
	.byte	1,5,10,7,9
	.half	.L1493-.L1077
	.byte	3,1,1,5,1,3,17,1,5,5,7,9
	.half	.L285-.L1493
	.byte	3,113,1,5,18,9
	.half	.L1494-.L285
	.byte	1,5,2,9
	.half	.L1495-.L1494
	.byte	1,5,19,7,9
	.half	.L1496-.L1495
	.byte	3,2,1,5,35,9
	.half	.L1079-.L1496
	.byte	1,5,51,1,5,56,9
	.half	.L1497-.L1079
	.byte	1,5,7,9
	.half	.L1080-.L1497
	.byte	1,5,3,9
	.half	.L1081-.L1080
	.byte	3,1,1,5,16,7,9
	.half	.L1498-.L1081
	.byte	3,2,1,5,14,9
	.half	.L1499-.L1498
	.byte	1,5,31,9
	.half	.L1078-.L1499
	.byte	3,2,1,5,13,9
	.half	.L1500-.L1078
	.byte	3,1,1,5,31,9
	.half	.L1501-.L1500
	.byte	1,5,13,9
	.half	.L1502-.L1501
	.byte	3,1,1,5,31,9
	.half	.L1503-.L1502
	.byte	1,5,2,9
	.half	.L287-.L1503
	.byte	3,5,1,5,1,3,1,1,7,9
	.half	.L511-.L287
	.byte	0,1,1
.L1487:
	.sdecl	'.debug_ranges',debug,cluster('SetAlg')
	.sect	'.debug_ranges'
.L510:
	.word	-1,.L391,0,.L511-.L391,0,0
.L797:
	.word	-1,.L391,0,.L795-.L391,-1,.L393,0,.L606-.L393,-1,.L395,0,.L601-.L395,-1,.L397,0,.L591-.L397,0,0
	.sdecl	'.debug_info',debug,cluster('GetAlg')
	.sect	'.debug_info'
.L512:
	.word	259
	.half	3
	.word	.L513
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L515,.L514
	.byte	2
	.word	.L408
	.byte	3
	.byte	'GetAlg',0,1,204,7,12
	.word	.L642
	.byte	1,1,1
	.word	.L399,.L799,.L398
	.byte	4
	.word	.L399,.L799
	.byte	5
	.byte	'ret',0,1,207,7,13
	.word	.L642,.L800
	.byte	5
	.byte	'load',0,1,208,7,12
	.word	.L684,.L801
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('GetAlg')
	.sect	'.debug_abbrev'
.L513:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('GetAlg')
	.sect	'.debug_line'
.L514:
	.word	.L1505-.L1504
.L1504:
	.half	3
	.word	.L1507-.L1506
.L1506:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1507:
	.byte	5,12,7,0,5,2
	.word	.L399
	.byte	3,203,7,1,5,6,9
	.half	.L1084-.L399
	.byte	3,6,1,5,2,9
	.half	.L1508-.L1084
	.byte	1,5,10,7,9
	.half	.L1509-.L1508
	.byte	3,1,1,5,1,3,9,1,5,18,7,9
	.half	.L290-.L1509
	.byte	3,121,1,5,33,9
	.half	.L1510-.L290
	.byte	1,5,49,1,5,55,9
	.half	.L1511-.L1510
	.byte	1,5,5,9
	.half	.L1085-.L1511
	.byte	3,1,1,5,17,7,9
	.half	.L1512-.L1085
	.byte	1,5,22,9
	.half	.L1513-.L1512
	.byte	1,5,3,7,9
	.half	.L1514-.L1513
	.byte	3,2,1,5,13,9
	.half	.L1515-.L1514
	.byte	1,5,1,9
	.half	.L292-.L1515
	.byte	3,4,1,7,9
	.half	.L516-.L292
	.byte	0,1,1
.L1505:
	.sdecl	'.debug_ranges',debug,cluster('GetAlg')
	.sect	'.debug_ranges'
.L515:
	.word	-1,.L399,0,.L516-.L399,0,0
	.sdecl	'.debug_info',debug,cluster('SetEnvironment')
	.sect	'.debug_info'
.L517:
	.word	266
	.half	3
	.word	.L518
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L520,.L519
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SetEnvironment',0,1,222,7,12
	.word	.L642
	.byte	1,1,1
	.word	.L401,.L802,.L400
	.byte	4
	.byte	'env',0,1,222,7,37
	.word	.L684,.L803
	.byte	5
	.word	.L401,.L802
	.byte	6
	.byte	'ret',0,1,224,7,13
	.word	.L642,.L804
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SetEnvironment')
	.sect	'.debug_abbrev'
.L518:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SetEnvironment')
	.sect	'.debug_line'
.L519:
	.word	.L1517-.L1516
.L1516:
	.half	3
	.word	.L1519-.L1518
.L1518:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1519:
	.byte	5,12,7,0,5,2
	.word	.L401
	.byte	3,221,7,1,5,17,9
	.half	.L1520-.L401
	.byte	3,2,1,5,5,9
	.half	.L1087-.L1520
	.byte	3,2,1,5,26,9
	.half	.L1521-.L1087
	.byte	1,5,43,7,9
	.half	.L1522-.L1521
	.byte	1,5,10,7,9
	.half	.L1523-.L1522
	.byte	3,1,1,5,1,3,21,1,5,6,7,9
	.half	.L295-.L1523
	.byte	3,110,1,5,2,9
	.half	.L1088-.L295
	.byte	1,5,10,7,9
	.half	.L1524-.L1088
	.byte	3,1,1,5,1,3,17,1,5,5,7,9
	.half	.L299-.L1524
	.byte	3,113,1,5,18,9
	.half	.L1525-.L299
	.byte	1,5,2,9
	.half	.L1526-.L1525
	.byte	1,5,19,7,9
	.half	.L1527-.L1526
	.byte	3,2,1,5,35,9
	.half	.L1089-.L1527
	.byte	1,5,51,1,5,56,9
	.half	.L1528-.L1089
	.byte	1,5,7,9
	.half	.L1090-.L1528
	.byte	1,5,3,9
	.half	.L1092-.L1090
	.byte	3,1,1,5,16,7,9
	.half	.L1529-.L1092
	.byte	3,2,1,5,14,9
	.half	.L1530-.L1529
	.byte	1,5,31,9
	.half	.L1531-.L1530
	.byte	3,2,1,5,13,9
	.half	.L1532-.L1531
	.byte	3,1,1,5,31,9
	.half	.L1091-.L1532
	.byte	1,5,13,9
	.half	.L1533-.L1091
	.byte	3,1,1,5,31,9
	.half	.L1534-.L1533
	.byte	1,5,2,9
	.half	.L301-.L1534
	.byte	3,5,1,5,1,3,1,1,7,9
	.half	.L521-.L301
	.byte	0,1,1
.L1517:
	.sdecl	'.debug_ranges',debug,cluster('SetEnvironment')
	.sect	'.debug_ranges'
.L520:
	.word	-1,.L401,0,.L521-.L401,0,0
	.sdecl	'.debug_info',debug,cluster('GetEnvironment')
	.sect	'.debug_info'
.L522:
	.word	267
	.half	3
	.word	.L523
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L525,.L524
	.byte	2
	.word	.L408
	.byte	3
	.byte	'GetEnvironment',0,1,250,7,12
	.word	.L642
	.byte	1,1,1
	.word	.L403,.L805,.L402
	.byte	4
	.word	.L403,.L805
	.byte	5
	.byte	'ret',0,1,253,7,13
	.word	.L642,.L806
	.byte	5
	.byte	'load',0,1,254,7,12
	.word	.L684,.L807
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('GetEnvironment')
	.sect	'.debug_abbrev'
.L523:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('GetEnvironment')
	.sect	'.debug_line'
.L524:
	.word	.L1536-.L1535
.L1535:
	.half	3
	.word	.L1538-.L1537
.L1537:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1538:
	.byte	5,12,7,0,5,2
	.word	.L403
	.byte	3,249,7,1,5,19,9
	.half	.L1093-.L403
	.byte	3,4,1,5,17,1,5,6,9
	.half	.L1094-.L1093
	.byte	3,2,1,5,2,9
	.half	.L1095-.L1094
	.byte	1,5,10,7,9
	.half	.L1539-.L1095
	.byte	3,1,1,5,1,3,9,1,5,18,7,9
	.half	.L304-.L1539
	.byte	3,121,1,5,33,9
	.half	.L1540-.L304
	.byte	1,5,49,1,5,55,9
	.half	.L1541-.L1540
	.byte	1,5,5,9
	.half	.L1096-.L1541
	.byte	3,1,1,5,17,7,9
	.half	.L1542-.L1096
	.byte	1,5,22,9
	.half	.L1543-.L1542
	.byte	1,5,3,7,9
	.half	.L1544-.L1543
	.byte	3,2,1,5,13,9
	.half	.L1545-.L1544
	.byte	1,5,1,9
	.half	.L306-.L1545
	.byte	3,4,1,7,9
	.half	.L526-.L306
	.byte	0,1,1
.L1536:
	.sdecl	'.debug_ranges',debug,cluster('GetEnvironment')
	.sect	'.debug_ranges'
.L525:
	.word	-1,.L403,0,.L526-.L403,0,0
	.sdecl	'.debug_info',debug,cluster('SetWroteFlag')
	.sect	'.debug_info'
.L527:
	.word	265
	.half	3
	.word	.L528
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L530,.L529
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SetWroteFlag',0,1,140,8,12
	.word	.L642
	.byte	1,1,1
	.word	.L405,.L808,.L404
	.byte	4
	.byte	'flag',0,1,140,8,35
	.word	.L684,.L809
	.byte	5
	.word	.L405,.L808
	.byte	6
	.byte	'ret',0,1,142,8,13
	.word	.L642,.L810
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SetWroteFlag')
	.sect	'.debug_abbrev'
.L528:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SetWroteFlag')
	.sect	'.debug_line'
.L529:
	.word	.L1547-.L1546
.L1546:
	.half	3
	.word	.L1549-.L1548
.L1548:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1549:
	.byte	5,12,7,0,5,2
	.word	.L405
	.byte	3,139,8,1,5,6,9
	.half	.L1550-.L405
	.byte	3,4,1,5,2,9
	.half	.L1551-.L1550
	.byte	1,5,10,7,9
	.half	.L1552-.L1551
	.byte	3,1,1,5,1,3,4,1,5,18,7,9
	.half	.L309-.L1552
	.byte	3,126,1,5,34,9
	.half	.L1098-.L309
	.byte	1,5,52,1,5,58,9
	.half	.L1553-.L1098
	.byte	1,5,1,9
	.half	.L531-.L1553
	.byte	3,2,0,1,1
.L1547:
	.sdecl	'.debug_ranges',debug,cluster('SetWroteFlag')
	.sect	'.debug_ranges'
.L530:
	.word	-1,.L405,0,.L531-.L405,0,0
	.sdecl	'.debug_info',debug,cluster('GetWroteFLag')
	.sect	'.debug_info'
.L532:
	.word	283
	.half	3
	.word	.L533
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L535,.L534
	.byte	2
	.word	.L408
	.byte	3
	.byte	'GetWroteFLag',0,1,151,8,12
	.word	.L642
	.byte	1,1,1
	.word	.L407,.L811,.L406
	.byte	4
	.byte	'flag',0,1,151,8,36
	.word	.L647,.L812
	.byte	5
	.word	.L407,.L811
	.byte	6
	.byte	'ret',0,1,154,8,13
	.word	.L642,.L813
	.byte	6
	.byte	'load',0,1,155,8,12
	.word	.L684,.L814
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('GetWroteFLag')
	.sect	'.debug_abbrev'
.L533:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('GetWroteFLag')
	.sect	'.debug_line'
.L534:
	.word	.L1555-.L1554
.L1554:
	.half	3
	.word	.L1557-.L1556
.L1556:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1557:
	.byte	5,12,7,0,5,2
	.word	.L407
	.byte	3,150,8,1,5,6,9
	.half	.L1100-.L407
	.byte	3,6,1,5,2,9
	.half	.L1558-.L1100
	.byte	1,5,10,7,9
	.half	.L1559-.L1558
	.byte	3,1,1,5,1,3,12,1,5,18,7,9
	.half	.L312-.L1559
	.byte	3,118,1,5,33,9
	.half	.L1560-.L312
	.byte	1,5,51,1,5,57,9
	.half	.L1101-.L1560
	.byte	1,5,2,9
	.half	.L1102-.L1101
	.byte	3,1,1,5,6,7,9
	.half	.L1561-.L1102
	.byte	3,2,1,5,11,9
	.half	.L1562-.L1561
	.byte	1,5,10,3,1,1,5,1,9
	.half	.L314-.L1562
	.byte	3,6,1,7,9
	.half	.L536-.L314
	.byte	0,1,1
.L1555:
	.sdecl	'.debug_ranges',debug,cluster('GetWroteFLag')
	.sect	'.debug_ranges'
.L535:
	.word	-1,.L407,0,.L536-.L407,0,0
	.sdecl	'.debug_info',debug,cluster('getEnvIndex')
	.sect	'.debug_info'
.L537:
	.word	244
	.half	3
	.word	.L538
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L540,.L539
	.byte	2
	.word	.L408
	.byte	3
	.byte	'getEnvIndex',0,1,65,12
	.word	.L642
	.byte	1,1,1
	.word	.L317,.L815,.L316
	.byte	4
	.byte	'certType',0,1,65,35
	.word	.L642,.L816
	.byte	5
	.word	.L817
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('getEnvIndex')
	.sect	'.debug_abbrev'
.L538:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('getEnvIndex')
	.sect	'.debug_line'
.L539:
	.word	.L1564-.L1563
.L1563:
	.half	3
	.word	.L1566-.L1565
.L1565:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1566:
	.byte	5,2,7,0,5,2
	.word	.L317
	.byte	3,194,0,1,5,10,7,9
	.half	.L1567-.L317
	.byte	3,1,1,5,1,3,19,1,5,5,7,9
	.half	.L2-.L1567
	.byte	3,110,1,5,28,9
	.half	.L1568-.L2
	.byte	1,5,38,9
	.half	.L1569-.L1568
	.byte	1,5,5,7,9
	.half	.L4-.L1569
	.byte	3,3,1,5,28,7,9
	.half	.L1570-.L4
	.byte	1,5,38,9
	.half	.L1571-.L1570
	.byte	1,5,5,7,9
	.half	.L6-.L1571
	.byte	3,3,1,5,28,7,9
	.half	.L1572-.L6
	.byte	1,5,38,9
	.half	.L1573-.L1572
	.byte	1,5,10,7,9
	.half	.L5-.L1573
	.byte	3,1,1,5,1,3,11,1,5,5,7,9
	.half	.L8-.L5
	.byte	3,119,1,5,28,7,9
	.half	.L1574-.L8
	.byte	1,5,38,9
	.half	.L1575-.L1574
	.byte	1,5,10,7,9
	.half	.L7-.L1575
	.byte	3,1,1,5,1,3,8,1,5,5,7,9
	.half	.L11-.L7
	.byte	3,122,1,5,28,7,9
	.half	.L1576-.L11
	.byte	1,5,38,9
	.half	.L1577-.L1576
	.byte	1,5,10,7,9
	.half	.L1578-.L1577
	.byte	3,1,1,5,1,3,5,1,5,5,7,9
	.half	.L14-.L1578
	.byte	3,125,1,5,28,7,9
	.half	.L1579-.L14
	.byte	1,5,38,9
	.half	.L1580-.L1579
	.byte	1,5,10,7,9
	.half	.L1581-.L1580
	.byte	3,1,1,5,1,3,2,1,5,9,7,9
	.half	.L17-.L1581
	.byte	3,127,1,5,1,3,1,1,7,9
	.half	.L541-.L17
	.byte	0,1,1
.L1564:
	.sdecl	'.debug_ranges',debug,cluster('getEnvIndex')
	.sect	'.debug_ranges'
.L540:
	.word	-1,.L317,0,.L541-.L317,0,0
.L817:
	.word	-1,.L317,0,.L815-.L317,-1,.L319,0,.L611-.L319,-1,.L321,0,.L561-.L321,0,0
	.sdecl	'.debug_info',debug,cluster('LoadSymmKey')
	.sect	'.debug_info'
.L542:
	.word	351
	.half	3
	.word	.L543
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L545,.L544
	.byte	2
	.word	.L408
	.byte	3
	.byte	'LoadSymmKey',0,1,214,2,12
	.word	.L642
	.byte	1,1,1
	.word	.L347,.L818,.L346
	.byte	4
	.byte	'keyId',0,1,214,2,35
	.word	.L642,.L819
	.byte	4
	.byte	'key',0,1,214,2,57
	.word	.L820,.L821
	.byte	5
	.word	.L822
	.byte	6
	.byte	'ret',0,1,216,2,13
	.word	.L642,.L823
	.byte	6
	.byte	'buf',0,1,217,2,12
	.word	.L787,.L824
	.byte	6
	.byte	'offset',0,1,218,2,13
	.word	.L642,.L825
	.byte	6
	.byte	'noutlen',0,1,219,2,13
	.word	.L642,.L826
	.byte	6
	.byte	'i',0,1,220,2,12
	.word	.L684,.L827
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('LoadSymmKey')
	.sect	'.debug_abbrev'
.L543:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('LoadSymmKey')
	.sect	'.debug_line'
.L544:
	.word	.L1583-.L1582
.L1582:
	.half	3
	.word	.L1585-.L1584
.L1584:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1585:
	.byte	5,12,7,0,5,2
	.word	.L347
	.byte	3,213,2,1,5,22,9
	.half	.L916-.L347
	.byte	3,5,1,5,21,1,5,15,9
	.half	.L1586-.L916
	.byte	3,3,1,5,12,3,120,1,5,2,9
	.half	.L920-.L1586
	.byte	3,8,1,5,10,7,9
	.half	.L1587-.L920
	.byte	3,1,1,5,1,3,202,0,1,5,6,7,9
	.half	.L93-.L1587
	.byte	3,184,127,1,5,2,9
	.half	.L1588-.L93
	.byte	1,5,10,7,9
	.half	.L1589-.L1588
	.byte	3,1,1,5,1,3,199,0,1,5,28,7,9
	.half	.L95-.L1589
	.byte	3,187,127,1,5,31,1,5,36,9
	.half	.L918-.L95
	.byte	3,4,1,5,2,9
	.half	.L1590-.L918
	.byte	3,126,1,5,28,7,9
	.half	.L1591-.L1590
	.byte	3,2,1,5,15,1,5,27,9
	.half	.L1592-.L1591
	.byte	1,5,46,9
	.half	.L1593-.L1592
	.byte	1,5,3,9
	.half	.L1594-.L1593
	.byte	1,7,9
	.half	.L1595-.L1594
	.byte	1,5,34,9
	.half	.L97-.L1595
	.byte	3,3,1,5,15,1,5,27,9
	.half	.L1596-.L97
	.byte	1,5,61,9
	.half	.L1597-.L1596
	.byte	1,5,27,9
	.half	.L1598-.L1597
	.byte	1,5,61,9
	.half	.L1599-.L1598
	.byte	1,5,3,9
	.half	.L1600-.L1599
	.byte	1,5,66,7,9
	.half	.L99-.L1600
	.byte	3,1,1,5,8,9
	.half	.L98-.L99
	.byte	3,3,1,5,2,9
	.half	.L1601-.L98
	.byte	1,5,10,7,9
	.half	.L1602-.L1601
	.byte	3,1,1,5,1,3,57,1,5,52,7,9
	.half	.L101-.L1602
	.byte	3,73,1,5,32,9
	.half	.L1603-.L101
	.byte	1,5,4,9
	.half	.L922-.L1603
	.byte	3,2,1,5,9,9
	.half	.L103-.L922
	.byte	3,1,1,3,2,1,5,19,9
	.half	.L1604-.L103
	.byte	1,5,42,1,5,47,9
	.half	.L1605-.L1604
	.byte	1,5,3,9
	.half	.L923-.L1605
	.byte	3,1,1,5,34,7,9
	.half	.L1606-.L923
	.byte	3,125,1,5,2,7,9
	.half	.L104-.L1606
	.byte	3,9,1,5,10,7,9
	.half	.L1607-.L104
	.byte	3,2,1,5,1,3,41,1,5,15,7,9
	.half	.L105-.L1607
	.byte	3,90,1,5,30,9
	.half	.L1608-.L105
	.byte	1,5,2,9
	.half	.L924-.L1608
	.byte	1,5,19,7,9
	.half	.L1609-.L924
	.byte	3,3,1,5,24,9
	.half	.L1610-.L1609
	.byte	1,5,112,9
	.half	.L917-.L1610
	.byte	1,5,2,9
	.half	.L929-.L917
	.byte	3,1,1,5,9,7,9
	.half	.L1611-.L929
	.byte	3,3,1,5,22,9
	.half	.L1612-.L1611
	.byte	1,5,6,9
	.half	.L1613-.L1612
	.byte	1,5,44,7,9
	.half	.L1614-.L1613
	.byte	1,5,41,9
	.half	.L1615-.L1614
	.byte	1,5,66,7,9
	.half	.L1616-.L1615
	.byte	1,5,63,9
	.half	.L1617-.L1616
	.byte	1,5,19,7,9
	.half	.L1618-.L1617
	.byte	3,1,1,5,40,7,9
	.half	.L1619-.L1618
	.byte	1,5,10,7,9
	.half	.L1620-.L1619
	.byte	3,2,1,5,1,3,28,1,5,9,7,9
	.half	.L110-.L1620
	.byte	3,104,1,5,2,9
	.half	.L1621-.L110
	.byte	1,5,42,7,9
	.half	.L1622-.L1621
	.byte	3,2,1,5,49,1,5,62,9
	.half	.L1623-.L1622
	.byte	1,5,92,1,5,58,9
	.half	.L1624-.L1623
	.byte	1,5,86,1,5,72,9
	.half	.L1625-.L1624
	.byte	1,5,92,1,5,3,9
	.half	.L1626-.L1625
	.byte	3,1,1,5,11,7,9
	.half	.L109-.L1626
	.byte	3,1,1,5,1,3,20,1,5,15,7,9
	.half	.L117-.L109
	.byte	3,110,1,5,23,9
	.half	.L1627-.L117
	.byte	1,5,29,9
	.half	.L1628-.L1627
	.byte	1,5,3,9
	.half	.L930-.L1628
	.byte	1,5,11,7,9
	.half	.L1629-.L930
	.byte	3,1,1,5,1,3,17,1,5,3,7,9
	.half	.L119-.L1629
	.byte	3,114,1,5,26,7,9
	.half	.L1630-.L119
	.byte	3,2,1,5,13,1,5,25,9
	.half	.L1631-.L1630
	.byte	1,5,67,9
	.half	.L1632-.L1631
	.byte	1,5,13,9
	.half	.L121-.L1632
	.byte	3,2,1,5,25,9
	.half	.L1633-.L121
	.byte	1,5,66,9
	.half	.L122-.L1633
	.byte	1,5,7,9
	.half	.L932-.L122
	.byte	3,4,1,5,1,3,6,1,5,7,7,9
	.half	.L108-.L932
	.byte	3,124,1,5,1,3,4,1,7,9
	.half	.L546-.L108
	.byte	0,1,1
.L1583:
	.sdecl	'.debug_ranges',debug,cluster('LoadSymmKey')
	.sect	'.debug_ranges'
.L545:
	.word	-1,.L347,0,.L546-.L347,0,0
.L822:
	.word	-1,.L347,0,.L818-.L347,-1,.L349,0,.L571-.L349,-1,.L351,0,.L636-.L351,0,0
	.sdecl	'.debug_info',debug,cluster('SaveSymmKey')
	.sect	'.debug_info'
.L547:
	.word	355
	.half	3
	.word	.L548
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L550,.L549
	.byte	2
	.word	.L408
	.byte	3
	.byte	'SaveSymmKey',0,1,171,3,12
	.word	.L642
	.byte	1,1,1
	.word	.L353,.L828,.L352
	.byte	4
	.byte	'keyId',0,1,171,3,35
	.word	.L642,.L829
	.byte	4
	.byte	'key',0,1,171,3,56
	.word	.L820,.L830
	.byte	5
	.word	.L353,.L828
	.byte	6
	.byte	'ret',0,1,173,3,13
	.word	.L642,.L831
	.byte	6
	.byte	'offset',0,1,174,3,13
	.word	.L642,.L832
	.byte	6
	.byte	'noutlen',0,1,175,3,13
	.word	.L642,.L833
	.byte	6
	.byte	'buf',0,1,176,3,12
	.word	.L787,.L834
	.byte	6
	.byte	'i',0,1,177,3,12
	.word	.L684,.L835
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SaveSymmKey')
	.sect	'.debug_abbrev'
.L548:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SaveSymmKey')
	.sect	'.debug_line'
.L549:
	.word	.L1635-.L1634
.L1634:
	.half	3
	.word	.L1637-.L1636
.L1636:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1637:
	.byte	5,12,7,0,5,2
	.word	.L353
	.byte	3,170,3,1,5,22,9
	.half	.L933-.L353
	.byte	3,4,1,5,21,1,5,6,9
	.half	.L934-.L933
	.byte	3,4,1,5,2,9
	.half	.L935-.L934
	.byte	1,5,10,7,9
	.half	.L1638-.L935
	.byte	3,1,1,5,1,3,27,1,5,15,7,9
	.half	.L125-.L1638
	.byte	3,103,1,5,2,9
	.half	.L1639-.L125
	.byte	1,5,10,7,9
	.half	.L1640-.L1639
	.byte	3,1,1,5,1,3,24,1,5,36,7,9
	.half	.L127-.L1640
	.byte	3,106,1,5,78,9
	.half	.L1641-.L127
	.byte	1,5,106,9
	.half	.L936-.L1641
	.byte	1,5,112,9
	.half	.L1642-.L936
	.byte	1,5,2,9
	.half	.L937-.L1642
	.byte	3,1,1,5,10,7,9
	.half	.L1643-.L937
	.byte	3,1,1,5,1,3,20,1,5,52,7,9
	.half	.L129-.L1643
	.byte	3,110,1,5,32,9
	.half	.L938-.L129
	.byte	1,5,4,9
	.half	.L939-.L938
	.byte	3,2,1,5,9,9
	.half	.L131-.L939
	.byte	3,1,1,3,2,1,5,19,9
	.half	.L1644-.L131
	.byte	1,5,43,1,5,48,9
	.half	.L1645-.L1644
	.byte	1,5,3,9
	.half	.L941-.L1645
	.byte	3,1,1,5,34,7,9
	.half	.L1646-.L941
	.byte	3,125,1,5,10,7,9
	.half	.L132-.L1646
	.byte	3,11,1,5,1,9
	.half	.L940-.L132
	.byte	3,4,1,7,9
	.half	.L551-.L940
	.byte	0,1,1
.L1635:
	.sdecl	'.debug_ranges',debug,cluster('SaveSymmKey')
	.sect	'.debug_ranges'
.L550:
	.word	-1,.L353,0,.L551-.L353,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L552:
	.word	210
	.half	3
	.word	.L553
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L555,.L554
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_1',0,1,119,12,1
	.word	.L337,.L556,.L336
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L553:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L554:
	.word	.L1648-.L1647
.L1647:
	.half	3
	.word	.L1650-.L1649
.L1649:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1650:
	.byte	5,8,7,0,5,2
	.word	.L337
	.byte	3,137,1,1,9
	.half	.L556-.L337
	.byte	0,1,1,5,6,0,5,2
	.word	.L337
	.byte	3,192,5,1,5,8,9
	.half	.L1651-.L337
	.byte	3,201,123,1,7,9
	.half	.L556-.L1651
	.byte	0,1,1,5,6,0,5,2
	.word	.L337
	.byte	3,183,7,1,5,8,9
	.half	.L1651-.L337
	.byte	3,210,121,1,7,9
	.half	.L556-.L1651
	.byte	0,1,1,5,6,0,5,2
	.word	.L337
	.byte	3,209,7,1,5,8,9
	.half	.L1651-.L337
	.byte	3,184,121,1,7,9
	.half	.L556-.L1651
	.byte	0,1,1,5,6,0,5,2
	.word	.L337
	.byte	3,229,7,1,5,8,9
	.half	.L1651-.L337
	.byte	3,164,121,1,7,9
	.half	.L556-.L1651
	.byte	0,1,1,5,6,0,5,2
	.word	.L337
	.byte	3,255,7,1,5,8,9
	.half	.L1651-.L337
	.byte	3,138,121,1,7,9
	.half	.L556-.L1651
	.byte	0,1,1,5,6,0,5,2
	.word	.L337
	.byte	3,143,8,1,5,8,9
	.half	.L1651-.L337
	.byte	3,250,120,1,7,9
	.half	.L556-.L1651
	.byte	0,1,1
.L1648:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L555:
	.word	-1,.L337,0,.L556-.L337,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L557:
	.word	210
	.half	3
	.word	.L558
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L560,.L559
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_2',0,1,65,12,1
	.word	.L321,.L561,.L320
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L558:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L559:
	.word	.L1653-.L1652
.L1652:
	.half	3
	.word	.L1655-.L1654
.L1654:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1655:
	.byte	5,28,7,0,5,2
	.word	.L321
	.byte	3,196,0,1,9
	.half	.L561-.L321
	.byte	0,1,1,5,28,0,5,2
	.word	.L321
	.byte	3,199,0,1,9
	.half	.L1656-.L321
	.byte	3,125,1,7,9
	.half	.L561-.L1656
	.byte	0,1,1,5,28,0,5,2
	.word	.L321
	.byte	3,202,0,1,9
	.half	.L1656-.L321
	.byte	3,122,1,7,9
	.half	.L561-.L1656
	.byte	0,1,1,5,28,0,5,2
	.word	.L321
	.byte	3,205,0,1,9
	.half	.L1656-.L321
	.byte	3,119,1,7,9
	.half	.L561-.L1656
	.byte	0,1,1,5,28,0,5,2
	.word	.L321
	.byte	3,208,0,1,9
	.half	.L1656-.L321
	.byte	3,116,1,7,9
	.half	.L561-.L1656
	.byte	0,1,1
.L1653:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L560:
	.word	-1,.L321,0,.L561-.L321,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L562:
	.word	211
	.half	3
	.word	.L563
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L565,.L564
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_3',0,1,178,1,12,1
	.word	.L343,.L566,.L342
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L563:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L564:
	.word	.L1658-.L1657
.L1657:
	.half	3
	.word	.L1660-.L1659
.L1659:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1660:
	.byte	5,75,7,0,5,2
	.word	.L343
	.byte	3,215,1,1,5,79,9
	.half	.L1000-.L343
	.byte	1,5,89,9
	.half	.L1661-.L1000
	.byte	1,9
	.half	.L566-.L1661
	.byte	0,1,1,5,78,0,5,2
	.word	.L343
	.byte	3,184,3,1,5,82,9
	.half	.L1000-.L343
	.byte	1,5,92,9
	.half	.L1661-.L1000
	.byte	1,5,89,3,159,126,1,7,9
	.half	.L566-.L1661
	.byte	0,1,1,5,83,0,5,2
	.word	.L343
	.byte	3,152,5,1,5,87,9
	.half	.L1000-.L343
	.byte	1,5,97,9
	.half	.L1661-.L1000
	.byte	1,5,89,3,191,124,1,7,9
	.half	.L566-.L1661
	.byte	0,1,1,5,84,0,5,2
	.word	.L343
	.byte	3,254,5,1,5,88,9
	.half	.L1000-.L343
	.byte	1,5,98,9
	.half	.L1661-.L1000
	.byte	1,5,89,3,217,123,1,7,9
	.half	.L566-.L1661
	.byte	0,1,1,5,55,0,5,2
	.word	.L343
	.byte	3,217,6,1,5,59,9
	.half	.L1000-.L343
	.byte	1,5,69,9
	.half	.L1661-.L1000
	.byte	1,5,89,3,254,122,1,7,9
	.half	.L566-.L1661
	.byte	0,1,1
.L1658:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L565:
	.word	-1,.L343,0,.L566-.L343,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_4')
	.sect	'.debug_info'
.L567:
	.word	211
	.half	3
	.word	.L568
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L570,.L569
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_4',0,1,214,2,12,1
	.word	.L349,.L571,.L348
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_4')
	.sect	'.debug_abbrev'
.L568:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_4')
	.sect	'.debug_line'
.L569:
	.word	.L1663-.L1662
.L1662:
	.half	3
	.word	.L1665-.L1664
.L1664:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1665:
	.byte	5,24,7,0,5,2
	.word	.L349
	.byte	3,133,3,1,5,66,9
	.half	.L925-.L349
	.byte	1,5,70,9
	.half	.L1666-.L925
	.byte	1,5,80,9
	.half	.L1667-.L1666
	.byte	1,9
	.half	.L571-.L1667
	.byte	0,1,1,5,24,0,5,2
	.word	.L349
	.byte	3,133,3,1,5,69,9
	.half	.L925-.L349
	.byte	3,247,3,1,5,73,9
	.half	.L1666-.L925
	.byte	1,5,83,9
	.half	.L1667-.L1666
	.byte	1,5,80,3,137,124,1,7,9
	.half	.L571-.L1667
	.byte	0,1,1
.L1663:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_4')
	.sect	'.debug_ranges'
.L570:
	.word	-1,.L349,0,.L571-.L349,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_5')
	.sect	'.debug_info'
.L572:
	.word	211
	.half	3
	.word	.L573
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L575,.L574
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_5',0,1,241,4,12,1
	.word	.L371,.L576,.L370
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_5')
	.sect	'.debug_abbrev'
.L573:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_5')
	.sect	'.debug_line'
.L574:
	.word	.L1669-.L1668
.L1668:
	.half	3
	.word	.L1671-.L1670
.L1670:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1671:
	.byte	5,33,7,0,5,2
	.word	.L371
	.byte	3,146,5,1,5,73,9
	.half	.L1672-.L371
	.byte	1,9
	.half	.L576-.L1672
	.byte	0,1,1,5,33,0,5,2
	.word	.L371
	.byte	3,209,5,1,5,73,9
	.half	.L1672-.L371
	.byte	1,3,65,1,7,9
	.half	.L576-.L1672
	.byte	0,1,1,5,33,0,5,2
	.word	.L371
	.byte	3,249,5,1,5,73,9
	.half	.L1672-.L371
	.byte	1,3,153,127,1,7,9
	.half	.L576-.L1672
	.byte	0,1,1,5,23,0,5,2
	.word	.L371
	.byte	3,158,6,1,5,64,9
	.half	.L1672-.L371
	.byte	1,5,73,3,244,126,1,7,9
	.half	.L576-.L1672
	.byte	0,1,1,5,24,0,5,2
	.word	.L371
	.byte	3,164,6,1,5,65,9
	.half	.L1672-.L371
	.byte	1,5,73,3,238,126,1,7,9
	.half	.L576-.L1672
	.byte	0,1,1
.L1669:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_5')
	.sect	'.debug_ranges'
.L575:
	.word	-1,.L371,0,.L576-.L371,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_6')
	.sect	'.debug_info'
.L577:
	.word	210
	.half	3
	.word	.L578
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L580,.L579
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_6',0,1,119,12,1
	.word	.L335,.L581,.L334
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_6')
	.sect	'.debug_abbrev'
.L578:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_6')
	.sect	'.debug_line'
.L579:
	.word	.L1674-.L1673
.L1673:
	.half	3
	.word	.L1676-.L1675
.L1675:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1676:
	.byte	5,49,7,0,5,2
	.word	.L335
	.byte	3,146,1,1,9
	.half	.L581-.L335
	.byte	0,1,1,5,60,0,5,2
	.word	.L335
	.byte	3,146,5,1,5,49,9
	.half	.L1677-.L335
	.byte	3,128,124,1,7,9
	.half	.L581-.L1677
	.byte	0,1,1,5,54,0,5,2
	.word	.L335
	.byte	3,202,5,1,5,49,9
	.half	.L1677-.L335
	.byte	3,200,123,1,7,9
	.half	.L581-.L1677
	.byte	0,1,1,5,60,0,5,2
	.word	.L335
	.byte	3,249,5,1,5,49,9
	.half	.L1677-.L335
	.byte	3,153,123,1,7,9
	.half	.L581-.L1677
	.byte	0,1,1
.L1674:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_6')
	.sect	'.debug_ranges'
.L580:
	.word	-1,.L335,0,.L581-.L335,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_7')
	.sect	'.debug_info'
.L582:
	.word	211
	.half	3
	.word	.L583
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L585,.L584
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_7',0,1,178,1,12,1
	.word	.L341,.L586,.L340
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_7')
	.sect	'.debug_abbrev'
.L583:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_7')
	.sect	'.debug_line'
.L584:
	.word	.L1679-.L1678
.L1678:
	.half	3
	.word	.L1681-.L1680
.L1680:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1681:
	.byte	5,6,7,0,5,2
	.word	.L341
	.byte	3,189,1,1,9
	.half	.L586-.L341
	.byte	0,1,1,5,6,0,5,2
	.word	.L341
	.byte	3,178,3,1,9
	.half	.L1682-.L341
	.byte	3,139,126,1,7,9
	.half	.L586-.L1682
	.byte	0,1,1,5,6,0,5,2
	.word	.L341
	.byte	3,214,6,1,9
	.half	.L1682-.L341
	.byte	3,231,122,1,7,9
	.half	.L586-.L1682
	.byte	0,1,1
.L1679:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_7')
	.sect	'.debug_ranges'
.L585:
	.word	-1,.L341,0,.L586-.L341,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_8')
	.sect	'.debug_info'
.L587:
	.word	211
	.half	3
	.word	.L588
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L590,.L589
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_8',0,1,177,7,12,1
	.word	.L397,.L591,.L396
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_8')
	.sect	'.debug_abbrev'
.L588:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_8')
	.sect	'.debug_line'
.L589:
	.word	.L1684-.L1683
.L1683:
	.half	3
	.word	.L1686-.L1685
.L1685:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1686:
	.byte	5,14,7,0,5,2
	.word	.L397
	.byte	3,191,7,1,5,13,9
	.half	.L1687-.L397
	.byte	3,2,1,5,28,9
	.half	.L1688-.L1687
	.byte	1,5,31,9
	.half	.L1689-.L1688
	.byte	1,9
	.half	.L591-.L1689
	.byte	0,1,1,5,14,0,5,2
	.word	.L397
	.byte	3,237,7,1,5,13,9
	.half	.L1687-.L397
	.byte	3,84,1,5,28,9
	.half	.L1688-.L1687
	.byte	3,46,1,5,31,9
	.half	.L1689-.L1688
	.byte	1,3,82,1,7,9
	.half	.L591-.L1689
	.byte	0,1,1
.L1684:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_8')
	.sect	'.debug_ranges'
.L590:
	.word	-1,.L397,0,.L591-.L397,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_9')
	.sect	'.debug_info'
.L592:
	.word	210
	.half	3
	.word	.L593
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L595,.L594
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_9',0,1,89,12,1
	.word	.L325,.L596,.L324
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_9')
	.sect	'.debug_abbrev'
.L593:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_9')
	.sect	'.debug_line'
.L594:
	.word	.L1691-.L1690
.L1690:
	.half	3
	.word	.L1693-.L1692
.L1692:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1693:
	.byte	5,59,7,0,5,2
	.word	.L325
	.byte	3,223,0,1,5,69,9
	.half	.L843-.L325
	.byte	1,9
	.half	.L596-.L843
	.byte	0,1,1,5,59,0,5,2
	.word	.L325
	.byte	3,223,0,1,5,68,9
	.half	.L843-.L325
	.byte	3,7,1,5,69,9
	.half	.L1694-.L843
	.byte	3,121,1,7,9
	.half	.L596-.L1694
	.byte	0,1,1
.L1691:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_9')
	.sect	'.debug_ranges'
.L595:
	.word	-1,.L325,0,.L596-.L325,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_10')
	.sect	'.debug_info'
.L597:
	.word	212
	.half	3
	.word	.L598
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L600,.L599
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_10',0,1,177,7,12,1
	.word	.L395,.L601,.L394
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_10')
	.sect	'.debug_abbrev'
.L598:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_10')
	.sect	'.debug_line'
.L599:
	.word	.L1696-.L1695
.L1695:
	.half	3
	.word	.L1698-.L1697
.L1697:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1698:
	.byte	5,13,7,0,5,2
	.word	.L395
	.byte	3,194,7,1,5,28,9
	.half	.L1082-.L395
	.byte	1,5,31,9
	.half	.L1699-.L1082
	.byte	1,9
	.half	.L601-.L1699
	.byte	0,1,1,5,13,0,5,2
	.word	.L395
	.byte	3,194,7,1,5,28,9
	.half	.L1082-.L395
	.byte	3,46,1,5,31,9
	.half	.L1699-.L1082
	.byte	1,3,82,1,7,9
	.half	.L601-.L1699
	.byte	0,1,1
.L1696:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_10')
	.sect	'.debug_ranges'
.L600:
	.word	-1,.L395,0,.L601-.L395,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_11')
	.sect	'.debug_info'
.L602:
	.word	212
	.half	3
	.word	.L603
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L605,.L604
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_11',0,1,177,7,12,1
	.word	.L393,.L606,.L392
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_11')
	.sect	'.debug_abbrev'
.L603:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_11')
	.sect	'.debug_line'
.L604:
	.word	.L1701-.L1700
.L1700:
	.half	3
	.word	.L1703-.L1702
.L1702:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1703:
	.byte	5,13,7,0,5,2
	.word	.L393
	.byte	3,195,7,1,5,28,9
	.half	.L1083-.L393
	.byte	1,5,31,9
	.half	.L1704-.L1083
	.byte	1,9
	.half	.L606-.L1704
	.byte	0,1,1,5,13,0,5,2
	.word	.L393
	.byte	3,195,7,1,5,28,9
	.half	.L1083-.L393
	.byte	3,46,1,5,31,9
	.half	.L1704-.L1083
	.byte	1,3,82,1,7,9
	.half	.L606-.L1704
	.byte	0,1,1
.L1701:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_11')
	.sect	'.debug_ranges'
.L605:
	.word	-1,.L393,0,.L606-.L393,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_12')
	.sect	'.debug_info'
.L607:
	.word	211
	.half	3
	.word	.L608
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L610,.L609
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_12',0,1,65,12,1
	.word	.L319,.L611,.L318
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_12')
	.sect	'.debug_abbrev'
.L608:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_12')
	.sect	'.debug_line'
.L609:
	.word	.L1706-.L1705
.L1705:
	.half	3
	.word	.L1708-.L1707
.L1707:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1708:
	.byte	5,5,7,0,5,2
	.word	.L319
	.byte	3,196,0,1,9
	.half	.L611-.L319
	.byte	0,1,1,5,17,0,5,2
	.word	.L319
	.byte	3,128,5,1,5,5,9
	.half	.L1709-.L319
	.byte	3,196,123,1,7,9
	.half	.L611-.L1709
	.byte	0,1,1,5,17,0,5,2
	.word	.L319
	.byte	3,243,5,1,5,5,9
	.half	.L1709-.L319
	.byte	3,209,122,1,7,9
	.half	.L611-.L1709
	.byte	0,1,1
.L1706:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_12')
	.sect	'.debug_ranges'
.L610:
	.word	-1,.L319,0,.L611-.L319,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_13')
	.sect	'.debug_info'
.L612:
	.word	212
	.half	3
	.word	.L613
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L615,.L614
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_13',0,1,143,6,12,1
	.word	.L379,.L616,.L378
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_13')
	.sect	'.debug_abbrev'
.L613:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_13')
	.sect	'.debug_line'
.L614:
	.word	.L1711-.L1710
.L1710:
	.half	3
	.word	.L1713-.L1712
.L1712:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1713:
	.byte	5,6,7,0,5,2
	.word	.L379
	.byte	3,149,6,1,9
	.half	.L616-.L379
	.byte	0,1,1,5,6,0,5,2
	.word	.L379
	.byte	3,243,6,1,9
	.half	.L1714-.L379
	.byte	3,162,127,1,7,9
	.half	.L616-.L1714
	.byte	0,1,1,5,6,0,5,2
	.word	.L379
	.byte	3,156,8,1,9
	.half	.L1714-.L379
	.byte	3,249,125,1,7,9
	.half	.L616-.L1714
	.byte	0,1,1
.L1711:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_13')
	.sect	'.debug_ranges'
.L615:
	.word	-1,.L379,0,.L616-.L379,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_14')
	.sect	'.debug_info'
.L617:
	.word	211
	.half	3
	.word	.L618
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L620,.L619
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_14',0,1,89,12,1
	.word	.L327,.L621,.L326
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_14')
	.sect	'.debug_abbrev'
.L618:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_14')
	.sect	'.debug_line'
.L619:
	.word	.L1716-.L1715
.L1715:
	.half	3
	.word	.L1718-.L1717
.L1717:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1718:
	.byte	5,59,7,0,5,2
	.word	.L327
	.byte	3,223,0,1,5,69,9
	.half	.L1719-.L327
	.byte	1,9
	.half	.L621-.L1719
	.byte	0,1,1,5,58,0,5,2
	.word	.L327
	.byte	3,230,0,1,5,68,9
	.half	.L1719-.L327
	.byte	1,5,69,3,121,1,7,9
	.half	.L621-.L1719
	.byte	0,1,1,5,19,0,5,2
	.word	.L327
	.byte	3,143,1,1,5,29,9
	.half	.L1719-.L327
	.byte	1,5,69,3,80,1,7,9
	.half	.L621-.L1719
	.byte	0,1,1
.L1716:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_14')
	.sect	'.debug_ranges'
.L620:
	.word	-1,.L327,0,.L621-.L327,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_15')
	.sect	'.debug_info'
.L622:
	.word	211
	.half	3
	.word	.L623
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L625,.L624
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_15',0,1,119,12,1
	.word	.L333,.L626,.L332
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_15')
	.sect	'.debug_abbrev'
.L623:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_15')
	.sect	'.debug_line'
.L624:
	.word	.L1721-.L1720
.L1720:
	.half	3
	.word	.L1723-.L1722
.L1722:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1723:
	.byte	5,12,7,0,5,2
	.word	.L333
	.byte	3,159,1,1,9
	.half	.L626-.L333
	.byte	0,1,1,5,12,0,5,2
	.word	.L333
	.byte	3,236,1,1,9
	.half	.L1724-.L333
	.byte	3,179,127,1,7,9
	.half	.L626-.L1724
	.byte	0,1,1,5,13,0,5,2
	.word	.L333
	.byte	3,194,7,1,5,12,9
	.half	.L1724-.L333
	.byte	3,221,121,1,7,9
	.half	.L626-.L1724
	.byte	0,1,1,5,13,0,5,2
	.word	.L333
	.byte	3,240,7,1,5,12,9
	.half	.L1724-.L333
	.byte	3,175,121,1,7,9
	.half	.L626-.L1724
	.byte	0,1,1
.L1721:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_15')
	.sect	'.debug_ranges'
.L625:
	.word	-1,.L333,0,.L626-.L333,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_16')
	.sect	'.debug_info'
.L627:
	.word	211
	.half	3
	.word	.L628
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L630,.L629
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_16',0,1,119,12,1
	.word	.L331,.L631,.L330
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_16')
	.sect	'.debug_abbrev'
.L628:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_16')
	.sect	'.debug_line'
.L629:
	.word	.L1726-.L1725
.L1725:
	.half	3
	.word	.L1728-.L1727
.L1727:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1728:
	.byte	5,12,7,0,5,2
	.word	.L331
	.byte	3,168,1,1,9
	.half	.L631-.L331
	.byte	0,1,1,5,12,0,5,2
	.word	.L331
	.byte	3,239,1,1,9
	.half	.L1729-.L331
	.byte	3,185,127,1,7,9
	.half	.L631-.L1729
	.byte	0,1,1,5,13,0,5,2
	.word	.L331
	.byte	3,195,7,1,5,12,9
	.half	.L1729-.L331
	.byte	3,229,121,1,7,9
	.half	.L631-.L1729
	.byte	0,1,1,5,13,0,5,2
	.word	.L331
	.byte	3,241,7,1,5,12,9
	.half	.L1729-.L331
	.byte	3,183,121,1,7,9
	.half	.L631-.L1729
	.byte	0,1,1
.L1726:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_16')
	.sect	'.debug_ranges'
.L630:
	.word	-1,.L331,0,.L631-.L331,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_17')
	.sect	'.debug_info'
.L632:
	.word	212
	.half	3
	.word	.L633
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L635,.L634
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_17',0,1,214,2,12,1
	.word	.L351,.L636,.L350
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_17')
	.sect	'.debug_abbrev'
.L633:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_17')
	.sect	'.debug_line'
.L634:
	.word	.L1731-.L1730
.L1730:
	.half	3
	.word	.L1733-.L1732
.L1732:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1733:
	.byte	5,24,7,0,5,2
	.word	.L351
	.byte	3,133,3,1,5,53,1,9
	.half	.L636-.L351
	.byte	0,1,1,5,24,0,5,2
	.word	.L351
	.byte	3,252,6,1,5,56,1,5,53,9
	.half	.L1734-.L351
	.byte	3,137,124,1,7,9
	.half	.L636-.L1734
	.byte	0,1,1,5,36,0,5,2
	.word	.L351
	.byte	3,184,3,1,5,65,1,5,53,9
	.half	.L1734-.L351
	.byte	3,77,1,7,9
	.half	.L636-.L1734
	.byte	0,1,1
.L1731:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_17')
	.sect	'.debug_ranges'
.L635:
	.word	-1,.L351,0,.L636-.L351,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_18')
	.sect	'.debug_info'
.L637:
	.word	212
	.half	3
	.word	.L638
	.byte	4,1
	.byte	'..\\vss_code\\vsskeym.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L640,.L639
	.byte	2
	.word	.L408
	.byte	3
	.byte	'.cocofun_18',0,1,241,4,12,1
	.word	.L369,.L641,.L368
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_18')
	.sect	'.debug_abbrev'
.L638:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_18')
	.sect	'.debug_line'
.L639:
	.word	.L1736-.L1735
.L1735:
	.half	3
	.word	.L1738-.L1737
.L1737:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\vss_code\\vsskeym.c',0,0,0,0,0
.L1738:
	.byte	5,11,7,0,5,2
	.word	.L369
	.byte	3,163,5,1,9
	.half	.L641-.L369
	.byte	0,1,1,5,11,0,5,2
	.word	.L369
	.byte	3,137,6,1,9
	.half	.L1739-.L369
	.byte	3,154,127,1,7,9
	.half	.L641-.L1739
	.byte	0,1,1,5,13,0,5,2
	.word	.L369
	.byte	3,193,7,1,5,11,9
	.half	.L1739-.L369
	.byte	3,226,125,1,7,9
	.half	.L641-.L1739
	.byte	0,1,1,5,13,0,5,2
	.word	.L369
	.byte	3,239,7,1,5,11,9
	.half	.L1739-.L369
	.byte	3,180,125,1,7,9
	.half	.L641-.L1739
	.byte	0,1,1
.L1736:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_18')
	.sect	'.debug_ranges'
.L640:
	.word	-1,.L369,0,.L641-.L369,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L336:
	.word	-1,.L337,0,.L556-.L337
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_10')
	.sect	'.debug_loc'
.L394:
	.word	-1,.L395,0,.L601-.L395
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_11')
	.sect	'.debug_loc'
.L392:
	.word	-1,.L393,0,.L606-.L393
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_12')
	.sect	'.debug_loc'
.L318:
	.word	-1,.L319,0,.L611-.L319
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_13')
	.sect	'.debug_loc'
.L378:
	.word	-1,.L379,0,.L616-.L379
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_14')
	.sect	'.debug_loc'
.L326:
	.word	-1,.L327,0,.L621-.L327
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_15')
	.sect	'.debug_loc'
.L332:
	.word	-1,.L333,0,.L626-.L333
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_16')
	.sect	'.debug_loc'
.L330:
	.word	-1,.L331,0,.L631-.L331
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_17')
	.sect	'.debug_loc'
.L350:
	.word	-1,.L351,0,.L636-.L351
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_18')
	.sect	'.debug_loc'
.L368:
	.word	-1,.L369,0,.L641-.L369
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L320:
	.word	-1,.L321,0,.L561-.L321
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L342:
	.word	-1,.L343,0,.L566-.L343
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_4')
	.sect	'.debug_loc'
.L348:
	.word	-1,.L349,0,.L571-.L349
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_5')
	.sect	'.debug_loc'
.L370:
	.word	-1,.L371,0,.L576-.L371
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_6')
	.sect	'.debug_loc'
.L334:
	.word	-1,.L335,0,.L581-.L335
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_7')
	.sect	'.debug_loc'
.L340:
	.word	-1,.L341,0,.L586-.L341
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_8')
	.sect	'.debug_loc'
.L396:
	.word	-1,.L397,0,.L591-.L397
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_9')
	.sect	'.debug_loc'
.L324:
	.word	-1,.L325,0,.L596-.L325
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CryptoPaddingTo16')
	.sect	'.debug_loc'
.L386:
	.word	-1,.L387,0,.L770-.L387
	.half	2
	.byte	138,0
	.word	0,0
.L773:
	.word	-1,.L387,0,.L770-.L387
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L774:
	.word	-1,.L387,.L1072-.L387,.L770-.L387
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L771:
	.word	-1,.L387,0,.L770-.L387
	.half	1
	.byte	100
	.word	0,0
.L772:
	.word	-1,.L387,0,.L770-.L387
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L775:
	.word	-1,.L387,.L1070-.L387,.L770-.L387
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L776:
	.word	-1,.L387,.L1071-.L387,.L770-.L387
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CryptoUnPadding16')
	.sect	'.debug_loc'
.L388:
	.word	-1,.L389,0,.L777-.L389
	.half	2
	.byte	138,0
	.word	0,0
.L780:
	.word	-1,.L389,.L1074-.L389,.L777-.L389
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L778:
	.word	-1,.L389,0,.L777-.L389
	.half	1
	.byte	100
	.word	0,0
.L779:
	.word	-1,.L389,0,.L777-.L389
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L781:
	.word	-1,.L389,0,.L777-.L389
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L782:
	.word	-1,.L389,.L1073-.L389,.L777-.L389
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GenAsymmKey')
	.sect	'.debug_loc'
.L366:
	.word	-1,.L367,0,.L989-.L367
	.half	2
	.byte	138,0
	.word	.L989-.L367,.L193-.L367
	.half	3
	.byte	138,248,1
	.word	.L193-.L367,.L193-.L367
	.half	2
	.byte	138,0
	.word	.L193-.L367,.L199-.L367
	.half	3
	.byte	138,248,1
	.word	.L199-.L367,.L199-.L367
	.half	2
	.byte	138,0
	.word	.L199-.L367,.L205-.L367
	.half	3
	.byte	138,248,1
	.word	.L205-.L367,.L205-.L367
	.half	2
	.byte	138,0
	.word	.L205-.L367,.L207-.L367
	.half	3
	.byte	138,248,1
	.word	.L207-.L367,.L207-.L367
	.half	2
	.byte	138,0
	.word	.L207-.L367,.L732-.L367
	.half	3
	.byte	138,248,1
	.word	.L732-.L367,.L732-.L367
	.half	2
	.byte	138,0
	.word	0,0
.L738:
	.word	-1,.L367,0,.L990-.L367
	.half	3
	.byte	145,136,126
	.word	.L319-.L367,.L611-.L367
	.half	3
	.byte	145,136,126
	.word	.L335-.L367,.L581-.L367
	.half	3
	.byte	145,136,126
	.word	.L996-.L367,.L997-.L367
	.half	3
	.byte	145,136,126
	.word	.L371-.L367,.L576-.L367
	.half	3
	.byte	145,136,126
	.word	.L1000-.L367,.L566-.L367
	.half	3
	.byte	145,136,126
	.word	.L998-.L367,.L207-.L367
	.half	3
	.byte	145,136,126
	.word	.L369-.L367,.L641-.L367
	.half	3
	.byte	145,136,126
	.word	.L1002-.L367,.L732-.L367
	.half	3
	.byte	145,136,126
	.word	0,0
.L742:
	.word	-1,.L367,0,.L990-.L367
	.half	3
	.byte	145,140,127
	.word	.L319-.L367,.L611-.L367
	.half	3
	.byte	145,140,127
	.word	.L335-.L367,.L581-.L367
	.half	3
	.byte	145,140,127
	.word	.L996-.L367,.L997-.L367
	.half	3
	.byte	145,140,127
	.word	.L371-.L367,.L576-.L367
	.half	3
	.byte	145,140,127
	.word	.L1000-.L367,.L566-.L367
	.half	3
	.byte	145,140,127
	.word	.L998-.L367,.L207-.L367
	.half	3
	.byte	145,140,127
	.word	.L369-.L367,.L641-.L367
	.half	3
	.byte	145,140,127
	.word	.L1002-.L367,.L732-.L367
	.half	3
	.byte	145,140,127
	.word	0,0
.L739:
	.word	-1,.L367,0,.L990-.L367
	.half	3
	.byte	145,248,126
	.word	.L319-.L367,.L611-.L367
	.half	3
	.byte	145,248,126
	.word	.L335-.L367,.L581-.L367
	.half	3
	.byte	145,248,126
	.word	.L996-.L367,.L997-.L367
	.half	3
	.byte	145,248,126
	.word	.L371-.L367,.L576-.L367
	.half	3
	.byte	145,248,126
	.word	.L1000-.L367,.L566-.L367
	.half	3
	.byte	145,248,126
	.word	.L998-.L367,.L207-.L367
	.half	3
	.byte	145,248,126
	.word	.L369-.L367,.L641-.L367
	.half	3
	.byte	145,248,126
	.word	.L1002-.L367,.L732-.L367
	.half	3
	.byte	145,248,126
	.word	0,0
.L740:
	.word	-1,.L367,0,.L990-.L367
	.half	3
	.byte	145,136,127
	.word	.L319-.L367,.L611-.L367
	.half	3
	.byte	145,136,127
	.word	.L335-.L367,.L581-.L367
	.half	3
	.byte	145,136,127
	.word	.L996-.L367,.L997-.L367
	.half	3
	.byte	145,136,127
	.word	.L371-.L367,.L576-.L367
	.half	3
	.byte	145,136,127
	.word	.L1000-.L367,.L566-.L367
	.half	3
	.byte	145,136,127
	.word	.L998-.L367,.L207-.L367
	.half	3
	.byte	145,136,127
	.word	.L369-.L367,.L641-.L367
	.half	3
	.byte	145,136,127
	.word	.L1002-.L367,.L732-.L367
	.half	3
	.byte	145,136,127
	.word	0,0
.L736:
	.word	-1,.L367,.L994-.L367,.L196-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L995-.L367,.L195-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L335-.L367,.L581-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L197-.L367,.L997-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L371-.L367,.L576-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L998-.L367,.L999-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L1001-.L367,.L203-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L205-.L367,.L207-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L369-.L367,.L641-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	.L1002-.L367,.L1003-.L367
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L733:
	.word	-1,.L367,0,.L991-.L367
	.half	1
	.byte	100
	.word	.L992-.L367,.L990-.L367
	.half	1
	.byte	108
	.word	.L319-.L367,.L611-.L367
	.half	1
	.byte	108
	.word	.L335-.L367,.L581-.L367
	.half	1
	.byte	108
	.word	.L996-.L367,.L997-.L367
	.half	1
	.byte	108
	.word	.L371-.L367,.L576-.L367
	.half	1
	.byte	108
	.word	.L1000-.L367,.L566-.L367
	.half	1
	.byte	108
	.word	.L998-.L367,.L207-.L367
	.half	1
	.byte	108
	.word	.L369-.L367,.L641-.L367
	.half	1
	.byte	108
	.word	.L1002-.L367,.L732-.L367
	.half	1
	.byte	108
	.word	.L1004-.L367,.L1005-.L367
	.half	1
	.byte	100
	.word	0,0
.L734:
	.word	-1,.L367,0,.L990-.L367
	.half	1
	.byte	101
	.word	.L993-.L367,.L990-.L367
	.half	1
	.byte	109
	.word	.L319-.L367,.L611-.L367
	.half	1
	.byte	109
	.word	.L335-.L367,.L581-.L367
	.half	1
	.byte	109
	.word	.L996-.L367,.L997-.L367
	.half	1
	.byte	109
	.word	.L371-.L367,.L576-.L367
	.half	1
	.byte	109
	.word	.L1000-.L367,.L566-.L367
	.half	1
	.byte	109
	.word	.L998-.L367,.L207-.L367
	.half	1
	.byte	109
	.word	.L369-.L367,.L641-.L367
	.half	1
	.byte	109
	.word	.L1002-.L367,.L732-.L367
	.half	1
	.byte	109
	.word	.L1006-.L367,.L1007-.L367
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GenSymmKey')
	.sect	'.debug_loc'
.L344:
	.word	-1,.L345,0,.L896-.L345
	.half	2
	.byte	138,0
	.word	.L896-.L345,.L72-.L345
	.half	3
	.byte	138,232,4
	.word	.L72-.L345,.L72-.L345
	.half	2
	.byte	138,0
	.word	.L72-.L345,.L74-.L345
	.half	3
	.byte	138,232,4
	.word	.L74-.L345,.L74-.L345
	.half	2
	.byte	138,0
	.word	.L74-.L345,.L77-.L345
	.half	3
	.byte	138,232,4
	.word	.L77-.L345,.L77-.L345
	.half	2
	.byte	138,0
	.word	.L77-.L345,.L676-.L345
	.half	3
	.byte	138,232,4
	.word	.L676-.L345,.L676-.L345
	.half	2
	.byte	138,0
	.word	0,0
.L695:
	.word	-1,.L345,0,.L676-.L345
	.half	3
	.byte	145,180,124
	.word	0,0
.L693:
	.word	-1,.L345,0,.L676-.L345
	.half	3
	.byte	145,212,123
	.word	0,0
.L677:
	.word	-1,.L345,0,.L897-.L345
	.half	1
	.byte	100
	.word	.L899-.L345,.L676-.L345
	.half	1
	.byte	110
	.word	.L915-.L345,.L76-.L345
	.half	1
	.byte	100
	.word	0,0
.L687:
	.word	-1,.L345,.L82-.L345,.L908-.L345
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L685:
	.word	-1,.L345,.L906-.L345,.L907-.L345
	.half	5
	.byte	144,39,157,32,32
	.word	.L907-.L345,.L76-.L345
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L686:
	.word	-1,.L345,.L909-.L345,.L910-.L345
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L683:
	.word	-1,.L345,0,.L676-.L345
	.half	3
	.byte	145,176,123
	.word	0,0
.L679:
	.word	-1,.L345,0,.L898-.L345
	.half	1
	.byte	101
	.word	.L900-.L345,.L676-.L345
	.half	1
	.byte	108
	.word	0,0
.L697:
	.word	-1,.L345,.L901-.L345,.L902-.L345
	.half	5
	.byte	144,36,157,32,0
	.word	.L902-.L345,.L914-.L345
	.half	5
	.byte	144,39,157,32,32
	.word	.L914-.L345,.L676-.L345
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L691:
	.word	-1,.L345,0,.L676-.L345
	.half	3
	.byte	145,208,123
	.word	0,0
.L689:
	.word	-1,.L345,0,.L676-.L345
	.half	3
	.byte	145,192,123
	.word	0,0
.L696:
	.word	-1,.L345,0,.L676-.L345
	.half	2
	.byte	145,116
	.word	0,0
.L688:
	.word	-1,.L345,.L898-.L345,.L903-.L345
	.half	5
	.byte	144,33,157,32,0
	.word	.L904-.L345,.L905-.L345
	.half	5
	.byte	144,36,157,32,32
	.word	.L911-.L345,.L912-.L345
	.half	5
	.byte	144,33,157,32,0
	.word	.L908-.L345,.L913-.L345
	.half	5
	.byte	144,36,157,32,32
	.word	.L88-.L345,.L76-.L345
	.half	5
	.byte	144,33,157,32,0
	.word	.L915-.L345,.L676-.L345
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L682:
	.word	-1,.L345,0,.L676-.L345
	.half	3
	.byte	145,168,123
	.word	0,0
.L680:
	.word	-1,.L345,0,.L676-.L345
	.half	3
	.byte	145,152,123
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GetAlg')
	.sect	'.debug_loc'
.L398:
	.word	-1,.L399,0,.L1084-.L399
	.half	2
	.byte	138,0
	.word	.L1084-.L399,.L290-.L399
	.half	2
	.byte	138,8
	.word	.L290-.L399,.L290-.L399
	.half	2
	.byte	138,0
	.word	.L290-.L399,.L799-.L399
	.half	2
	.byte	138,8
	.word	.L799-.L399,.L799-.L399
	.half	2
	.byte	138,0
	.word	0,0
.L801:
	.word	-1,.L399,.L337-.L399,.L556-.L399
	.half	2
	.byte	145,120
	.word	0,.L799-.L399
	.half	2
	.byte	145,120
	.word	0,0
.L800:
	.word	-1,.L399,.L1085-.L399,.L799-.L399
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GetEnvironment')
	.sect	'.debug_loc'
.L402:
	.word	-1,.L403,0,.L1093-.L403
	.half	2
	.byte	138,0
	.word	.L1093-.L403,.L304-.L403
	.half	2
	.byte	138,8
	.word	.L304-.L403,.L304-.L403
	.half	2
	.byte	138,0
	.word	.L304-.L403,.L805-.L403
	.half	2
	.byte	138,8
	.word	.L805-.L403,.L805-.L403
	.half	2
	.byte	138,0
	.word	0,0
.L807:
	.word	-1,.L403,0,.L1094-.L403
	.half	2
	.byte	145,120
	.word	.L337-.L403,.L556-.L403
	.half	2
	.byte	145,120
	.word	.L1095-.L403,.L805-.L403
	.half	2
	.byte	145,120
	.word	0,0
.L806:
	.word	-1,.L403,.L1096-.L403,.L805-.L403
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GetKeyActive')
	.sect	'.debug_loc'
.L360:
	.word	-1,.L361,0,.L964-.L361
	.half	2
	.byte	138,0
	.word	.L964-.L361,.L161-.L361
	.half	2
	.byte	138,32
	.word	.L161-.L361,.L161-.L361
	.half	2
	.byte	138,0
	.word	.L161-.L361,.L163-.L361
	.half	2
	.byte	138,32
	.word	.L163-.L361,.L163-.L361
	.half	2
	.byte	138,0
	.word	.L163-.L361,.L167-.L361
	.half	2
	.byte	138,32
	.word	.L167-.L361,.L167-.L361
	.half	2
	.byte	138,0
	.word	.L167-.L361,.L166-.L361
	.half	2
	.byte	138,32
	.word	.L166-.L361,.L166-.L361
	.half	2
	.byte	138,0
	.word	.L166-.L361,.L714-.L361
	.half	2
	.byte	138,32
	.word	.L714-.L361,.L714-.L361
	.half	2
	.byte	138,0
	.word	0,0
.L718:
	.word	-1,.L361,0,.L714-.L361
	.half	2
	.byte	145,96
	.word	0,0
.L715:
	.word	-1,.L361,0,.L965-.L361
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L717:
	.word	-1,.L361,.L965-.L361,.L714-.L361
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L716:
	.word	-1,.L361,0,.L966-.L361
	.half	1
	.byte	100
	.word	.L967-.L361,.L714-.L361
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GetSessKey')
	.sect	'.debug_loc'
.L354:
	.word	-1,.L355,0,.L698-.L355
	.half	2
	.byte	138,0
	.word	0,0
.L701:
	.word	-1,.L355,0,.L698-.L355
	.half	1
	.byte	101
	.word	0,0
.L699:
	.word	-1,.L355,0,.L942-.L355
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L700:
	.word	-1,.L355,0,.L698-.L355
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GetSymmKey')
	.sect	'.debug_loc'
.L356:
	.word	-1,.L357,0,.L943-.L357
	.half	2
	.byte	138,0
	.word	.L943-.L357,.L140-.L357
	.half	2
	.byte	138,32
	.word	.L140-.L357,.L140-.L357
	.half	2
	.byte	138,0
	.word	.L140-.L357,.L142-.L357
	.half	2
	.byte	138,32
	.word	.L142-.L357,.L142-.L357
	.half	2
	.byte	138,0
	.word	.L142-.L357,.L702-.L357
	.half	2
	.byte	138,32
	.word	.L702-.L357,.L702-.L357
	.half	2
	.byte	138,0
	.word	0,0
.L708:
	.word	-1,.L357,0,.L702-.L357
	.half	2
	.byte	145,96
	.word	0,0
.L705:
	.word	-1,.L357,0,.L944-.L357
	.half	1
	.byte	101
	.word	.L947-.L357,.L702-.L357
	.half	1
	.byte	108
	.word	.L950-.L357,.L948-.L357
	.half	1
	.byte	100
	.word	.L952-.L357,.L951-.L357
	.half	1
	.byte	100
	.word	0,0
.L703:
	.word	-1,.L357,0,.L944-.L357
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L704:
	.word	-1,.L357,0,.L945-.L357
	.half	1
	.byte	100
	.word	.L946-.L357,.L702-.L357
	.half	1
	.byte	111
	.word	0,0
.L706:
	.word	-1,.L357,.L944-.L357,.L948-.L357
	.half	5
	.byte	144,33,157,32,0
	.word	.L949-.L357,.L702-.L357
	.half	5
	.byte	144,36,157,32,0
	.word	.L146-.L357,.L951-.L357
	.half	5
	.byte	144,33,157,32,0
	.word	.L145-.L357,.L144-.L357
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('GetWroteFLag')
	.sect	'.debug_loc'
.L406:
	.word	-1,.L407,0,.L1099-.L407
	.half	2
	.byte	138,0
	.word	.L1099-.L407,.L312-.L407
	.half	2
	.byte	138,8
	.word	.L312-.L407,.L312-.L407
	.half	2
	.byte	138,0
	.word	.L312-.L407,.L811-.L407
	.half	2
	.byte	138,8
	.word	.L811-.L407,.L811-.L407
	.half	2
	.byte	138,0
	.word	0,0
.L812:
	.word	-1,.L407,.L379-.L407,.L616-.L407
	.half	1
	.byte	111
	.word	.L379-.L407,.L616-.L407
	.half	1
	.byte	100
	.word	.L1100-.L407,.L811-.L407
	.half	1
	.byte	111
	.word	0,.L1101-.L407
	.half	1
	.byte	100
	.word	0,0
.L814:
	.word	-1,.L407,.L379-.L407,.L616-.L407
	.half	2
	.byte	145,120
	.word	0,.L811-.L407
	.half	2
	.byte	145,120
	.word	0,0
.L813:
	.word	-1,.L407,.L1102-.L407,.L811-.L407
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('LoadAsymmKey')
	.sect	'.debug_loc'
.L372:
	.word	-1,.L373,0,.L1008-.L373
	.half	2
	.byte	138,0
	.word	.L1008-.L373,.L212-.L373
	.half	3
	.byte	138,248,1
	.word	.L212-.L373,.L212-.L373
	.half	2
	.byte	138,0
	.word	.L212-.L373,.L216-.L373
	.half	3
	.byte	138,248,1
	.word	.L216-.L373,.L216-.L373
	.half	2
	.byte	138,0
	.word	.L216-.L373,.L218-.L373
	.half	3
	.byte	138,248,1
	.word	.L218-.L373,.L218-.L373
	.half	2
	.byte	138,0
	.word	.L218-.L373,.L221-.L373
	.half	3
	.byte	138,248,1
	.word	.L221-.L373,.L221-.L373
	.half	2
	.byte	138,0
	.word	.L221-.L373,.L223-.L373
	.half	3
	.byte	138,248,1
	.word	.L223-.L373,.L223-.L373
	.half	2
	.byte	138,0
	.word	.L223-.L373,.L226-.L373
	.half	3
	.byte	138,248,1
	.word	.L226-.L373,.L226-.L373
	.half	2
	.byte	138,0
	.word	.L226-.L373,.L743-.L373
	.half	3
	.byte	138,248,1
	.word	.L743-.L373,.L743-.L373
	.half	2
	.byte	138,0
	.word	0,0
.L748:
	.word	-1,.L373,.L337-.L373,.L556-.L373
	.half	3
	.byte	145,136,126
	.word	0,.L218-.L373
	.half	3
	.byte	145,136,126
	.word	.L335-.L373,.L581-.L373
	.half	3
	.byte	145,136,126
	.word	.L371-.L373,.L576-.L373
	.half	3
	.byte	145,136,126
	.word	.L1023-.L373,.L743-.L373
	.half	3
	.byte	145,136,126
	.word	0,0
.L751:
	.word	-1,.L373,.L337-.L373,.L556-.L373
	.half	3
	.byte	145,140,127
	.word	0,.L218-.L373
	.half	3
	.byte	145,140,127
	.word	.L335-.L373,.L581-.L373
	.half	3
	.byte	145,140,127
	.word	.L371-.L373,.L576-.L373
	.half	3
	.byte	145,140,127
	.word	.L1023-.L373,.L743-.L373
	.half	3
	.byte	145,140,127
	.word	0,0
.L749:
	.word	-1,.L373,.L337-.L373,.L556-.L373
	.half	3
	.byte	145,248,126
	.word	0,.L218-.L373
	.half	3
	.byte	145,248,126
	.word	.L335-.L373,.L581-.L373
	.half	3
	.byte	145,248,126
	.word	.L371-.L373,.L576-.L373
	.half	3
	.byte	145,248,126
	.word	.L1023-.L373,.L743-.L373
	.half	3
	.byte	145,248,126
	.word	0,0
.L750:
	.word	-1,.L373,.L337-.L373,.L556-.L373
	.half	3
	.byte	145,136,127
	.word	0,.L218-.L373
	.half	3
	.byte	145,136,127
	.word	.L335-.L373,.L581-.L373
	.half	3
	.byte	145,136,127
	.word	.L371-.L373,.L576-.L373
	.half	3
	.byte	145,136,127
	.word	.L1023-.L373,.L743-.L373
	.half	3
	.byte	145,136,127
	.word	0,0
.L747:
	.word	-1,.L373,.L1022-.L373,.L218-.L373
	.half	5
	.byte	144,33,157,32,0
	.word	.L335-.L373,.L581-.L373
	.half	5
	.byte	144,33,157,32,0
	.word	.L371-.L373,.L576-.L373
	.half	5
	.byte	144,33,157,32,0
	.word	.L1023-.L373,.L1024-.L373
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L746:
	.word	-1,.L373,0,.L1009-.L373
	.half	1
	.byte	102
	.word	.L1012-.L373,.L210-.L373
	.half	1
	.byte	111
	.word	.L212-.L373,.L1013-.L373
	.half	1
	.byte	111
	.word	.L337-.L373,.L556-.L373
	.half	2
	.byte	145,124
	.word	.L337-.L373,.L1018-.L373
	.half	1
	.byte	111
	.word	.L1019-.L373,.L218-.L373
	.half	2
	.byte	145,124
	.word	.L335-.L373,.L581-.L373
	.half	2
	.byte	145,124
	.word	.L371-.L373,.L576-.L373
	.half	2
	.byte	145,124
	.word	.L1023-.L373,.L743-.L373
	.half	2
	.byte	145,124
	.word	.L1029-.L373,.L1030-.L373
	.half	1
	.byte	100
	.word	0,0
.L744:
	.word	-1,.L373,0,.L1010-.L373
	.half	1
	.byte	100
	.word	.L1014-.L373,.L1015-.L373
	.half	1
	.byte	100
	.word	.L337-.L373,.L556-.L373
	.half	1
	.byte	108
	.word	.L1020-.L373,.L218-.L373
	.half	1
	.byte	108
	.word	.L335-.L373,.L581-.L373
	.half	1
	.byte	108
	.word	.L371-.L373,.L576-.L373
	.half	1
	.byte	108
	.word	.L1023-.L373,.L743-.L373
	.half	1
	.byte	108
	.word	.L1025-.L373,.L1026-.L373
	.half	1
	.byte	100
	.word	0,0
.L745:
	.word	-1,.L373,0,.L1011-.L373
	.half	1
	.byte	101
	.word	.L1016-.L373,.L1017-.L373
	.half	1
	.byte	100
	.word	.L337-.L373,.L556-.L373
	.half	1
	.byte	109
	.word	.L1021-.L373,.L218-.L373
	.half	1
	.byte	109
	.word	.L335-.L373,.L581-.L373
	.half	1
	.byte	109
	.word	.L371-.L373,.L576-.L373
	.half	1
	.byte	109
	.word	.L1023-.L373,.L743-.L373
	.half	1
	.byte	109
	.word	.L1027-.L373,.L1028-.L373
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('LoadCFGData')
	.sect	'.debug_loc'
.L376:
	.word	-1,.L377,0,.L1049-.L377
	.half	2
	.byte	138,0
	.word	.L1049-.L377,.L243-.L377
	.half	3
	.byte	138,136,1
	.word	.L243-.L377,.L243-.L377
	.half	2
	.byte	138,0
	.word	.L243-.L377,.L245-.L377
	.half	3
	.byte	138,136,1
	.word	.L245-.L377,.L245-.L377
	.half	2
	.byte	138,0
	.word	.L245-.L377,.L248-.L377
	.half	3
	.byte	138,136,1
	.word	.L248-.L377,.L248-.L377
	.half	2
	.byte	138,0
	.word	.L248-.L377,.L761-.L377
	.half	3
	.byte	138,136,1
	.word	.L761-.L377,.L761-.L377
	.half	2
	.byte	138,0
	.word	0,0
.L762:
	.word	-1,.L377,0,.L1050-.L377
	.half	1
	.byte	100
	.word	.L1051-.L377,.L1050-.L377
	.half	1
	.byte	111
	.word	.L379-.L377,.L616-.L377
	.half	1
	.byte	111
	.word	.L379-.L377,.L616-.L377
	.half	1
	.byte	100
	.word	.L1052-.L377,.L1053-.L377
	.half	1
	.byte	100
	.word	.L371-.L377,.L576-.L377
	.half	1
	.byte	111
	.word	.L1052-.L377,.L761-.L377
	.half	1
	.byte	111
	.word	.L1056-.L377,.L1057-.L377
	.half	1
	.byte	102
	.word	0,0
.L766:
	.word	-1,.L377,0,.L1050-.L377
	.half	2
	.byte	145,104
	.word	.L379-.L377,.L616-.L377
	.half	2
	.byte	145,104
	.word	.L371-.L377,.L576-.L377
	.half	2
	.byte	145,104
	.word	.L1052-.L377,.L761-.L377
	.half	2
	.byte	145,104
	.word	0,0
.L767:
	.word	-1,.L377,0,.L1050-.L377
	.half	2
	.byte	145,120
	.word	.L379-.L377,.L616-.L377
	.half	2
	.byte	145,120
	.word	.L371-.L377,.L576-.L377
	.half	2
	.byte	145,120
	.word	.L1052-.L377,.L761-.L377
	.half	2
	.byte	145,120
	.word	0,0
.L764:
	.word	-1,.L377,.L371-.L377,.L576-.L377
	.half	5
	.byte	144,33,157,32,0
	.word	.L1054-.L377,.L1055-.L377
	.half	5
	.byte	144,33,157,32,0
	.word	.L1057-.L377,.L761-.L377
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L765:
	.word	-1,.L377,0,.L1050-.L377
	.half	3
	.byte	145,248,126
	.word	.L379-.L377,.L616-.L377
	.half	3
	.byte	145,248,126
	.word	.L371-.L377,.L576-.L377
	.half	3
	.byte	145,248,126
	.word	.L1052-.L377,.L761-.L377
	.half	3
	.byte	145,248,126
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('LoadCert')
	.sect	'.debug_loc'
.L322:
	.word	-1,.L323,0,.L643-.L323
	.half	2
	.byte	138,0
	.word	0,0
.L648:
	.word	-1,.L323,0,.L837-.L323
	.half	1
	.byte	101
	.word	.L327-.L323,.L621-.L323
	.half	1
	.byte	109
	.word	.L327-.L323,.L838-.L323
	.half	1
	.byte	101
	.word	.L841-.L323,.L842-.L323
	.half	1
	.byte	100
	.word	.L843-.L323,.L596-.L323
	.half	1
	.byte	109
	.word	.L844-.L323,.L643-.L323
	.half	1
	.byte	109
	.word	.L846-.L323,.L847-.L323
	.half	1
	.byte	100
	.word	.L851-.L323,.L643-.L323
	.half	1
	.byte	101
	.word	0,0
.L650:
	.word	-1,.L323,.L849-.L323,.L30-.L323
	.half	5
	.byte	144,33,157,32,0
	.word	.L31-.L323,.L643-.L323
	.half	5
	.byte	144,33,157,32,0
	.word	.L850-.L323,.L643-.L323
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L644:
	.word	-1,.L323,0,.L837-.L323
	.half	5
	.byte	144,34,157,32,0
	.word	.L327-.L323,.L621-.L323
	.half	5
	.byte	144,39,157,32,32
	.word	.L327-.L323,.L621-.L323
	.half	5
	.byte	144,34,157,32,0
	.word	.L839-.L323,.L840-.L323
	.half	5
	.byte	144,39,157,32,32
	.word	.L843-.L323,.L596-.L323
	.half	5
	.byte	144,39,157,32,32
	.word	.L24-.L323,.L845-.L323
	.half	5
	.byte	144,39,157,32,32
	.word	.L27-.L323,.L848-.L323
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L646:
	.word	-1,.L323,0,.L837-.L323
	.half	1
	.byte	100
	.word	.L327-.L323,.L621-.L323
	.half	1
	.byte	100
	.word	.L327-.L323,.L621-.L323
	.half	1
	.byte	108
	.word	.L843-.L323,.L596-.L323
	.half	1
	.byte	108
	.word	.L839-.L323,.L643-.L323
	.half	1
	.byte	108
	.word	.L850-.L323,.L643-.L323
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('LoadCertById')
	.sect	'.debug_loc'
.L328:
	.word	-1,.L329,0,.L852-.L329
	.half	2
	.byte	138,0
	.word	.L852-.L329,.L36-.L329
	.half	3
	.byte	138,152,4
	.word	.L36-.L329,.L36-.L329
	.half	2
	.byte	138,0
	.word	.L36-.L329,.L38-.L329
	.half	3
	.byte	138,152,4
	.word	.L38-.L329,.L38-.L329
	.half	2
	.byte	138,0
	.word	.L38-.L329,.L41-.L329
	.half	3
	.byte	138,152,4
	.word	.L41-.L329,.L41-.L329
	.half	2
	.byte	138,0
	.word	.L41-.L329,.L48-.L329
	.half	3
	.byte	138,152,4
	.word	.L48-.L329,.L48-.L329
	.half	2
	.byte	138,0
	.word	.L48-.L329,.L651-.L329
	.half	3
	.byte	138,152,4
	.word	.L651-.L329,.L651-.L329
	.half	2
	.byte	138,0
	.word	0,0
.L658:
	.word	-1,.L329,.L337-.L329,.L556-.L329
	.half	3
	.byte	145,232,123
	.word	.L327-.L329,.L621-.L329
	.half	3
	.byte	145,232,123
	.word	0,.L38-.L329
	.half	3
	.byte	145,232,123
	.word	.L335-.L329,.L581-.L329
	.half	3
	.byte	145,232,123
	.word	.L333-.L329,.L626-.L329
	.half	3
	.byte	145,232,123
	.word	.L860-.L329,.L48-.L329
	.half	3
	.byte	145,232,123
	.word	.L331-.L329,.L631-.L329
	.half	3
	.byte	145,232,123
	.word	.L869-.L329,.L651-.L329
	.half	3
	.byte	145,232,123
	.word	0,0
.L659:
	.word	-1,.L329,.L337-.L329,.L556-.L329
	.half	3
	.byte	145,232,125
	.word	.L327-.L329,.L621-.L329
	.half	3
	.byte	145,232,125
	.word	0,.L38-.L329
	.half	3
	.byte	145,232,125
	.word	.L335-.L329,.L581-.L329
	.half	3
	.byte	145,232,125
	.word	.L333-.L329,.L626-.L329
	.half	3
	.byte	145,232,125
	.word	.L860-.L329,.L48-.L329
	.half	3
	.byte	145,232,125
	.word	.L331-.L329,.L631-.L329
	.half	3
	.byte	145,232,125
	.word	.L869-.L329,.L651-.L329
	.half	3
	.byte	145,232,125
	.word	0,0
.L654:
	.word	-1,.L329,.L337-.L329,.L556-.L329
	.half	1
	.byte	109
	.word	.L337-.L329,.L556-.L329
	.half	1
	.byte	101
	.word	0,.L854-.L329
	.half	1
	.byte	101
	.word	.L327-.L329,.L621-.L329
	.half	1
	.byte	109
	.word	.L857-.L329,.L38-.L329
	.half	1
	.byte	109
	.word	.L335-.L329,.L581-.L329
	.half	1
	.byte	109
	.word	.L333-.L329,.L626-.L329
	.half	1
	.byte	109
	.word	.L860-.L329,.L48-.L329
	.half	1
	.byte	109
	.word	.L866-.L329,.L867-.L329
	.half	1
	.byte	100
	.word	.L331-.L329,.L631-.L329
	.half	1
	.byte	109
	.word	.L869-.L329,.L651-.L329
	.half	1
	.byte	109
	.word	.L871-.L329,.L872-.L329
	.half	1
	.byte	100
	.word	0,0
.L652:
	.word	-1,.L329,.L337-.L329,.L556-.L329
	.half	5
	.byte	144,34,157,32,0
	.word	.L337-.L329,.L556-.L329
	.half	5
	.byte	144,36,157,32,0
	.word	0,.L855-.L329
	.half	5
	.byte	144,34,157,32,0
	.word	.L327-.L329,.L621-.L329
	.half	5
	.byte	144,36,157,32,0
	.word	.L858-.L329,.L38-.L329
	.half	5
	.byte	144,36,157,32,0
	.word	.L335-.L329,.L581-.L329
	.half	5
	.byte	144,36,157,32,0
	.word	.L333-.L329,.L626-.L329
	.half	5
	.byte	144,36,157,32,0
	.word	.L860-.L329,.L48-.L329
	.half	5
	.byte	144,36,157,32,0
	.word	.L331-.L329,.L631-.L329
	.half	5
	.byte	144,36,157,32,0
	.word	.L869-.L329,.L651-.L329
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L653:
	.word	-1,.L329,.L337-.L329,.L556-.L329
	.half	1
	.byte	108
	.word	.L337-.L329,.L556-.L329
	.half	1
	.byte	100
	.word	0,.L855-.L329
	.half	1
	.byte	100
	.word	.L327-.L329,.L621-.L329
	.half	1
	.byte	108
	.word	.L858-.L329,.L38-.L329
	.half	1
	.byte	108
	.word	.L335-.L329,.L581-.L329
	.half	1
	.byte	108
	.word	.L333-.L329,.L626-.L329
	.half	1
	.byte	108
	.word	.L860-.L329,.L48-.L329
	.half	1
	.byte	108
	.word	.L331-.L329,.L631-.L329
	.half	1
	.byte	108
	.word	.L869-.L329,.L651-.L329
	.half	1
	.byte	108
	.word	0,0
.L661:
	.word	-1,.L329,.L337-.L329,.L556-.L329
	.half	2
	.byte	145,104
	.word	.L327-.L329,.L621-.L329
	.half	2
	.byte	145,104
	.word	0,.L38-.L329
	.half	2
	.byte	145,104
	.word	.L335-.L329,.L581-.L329
	.half	2
	.byte	145,104
	.word	.L333-.L329,.L626-.L329
	.half	2
	.byte	145,104
	.word	.L860-.L329,.L48-.L329
	.half	2
	.byte	145,104
	.word	.L331-.L329,.L631-.L329
	.half	2
	.byte	145,104
	.word	.L869-.L329,.L651-.L329
	.half	2
	.byte	145,104
	.word	0,0
.L663:
	.word	-1,.L329,.L337-.L329,.L556-.L329
	.half	2
	.byte	145,120
	.word	.L327-.L329,.L621-.L329
	.half	2
	.byte	145,120
	.word	0,.L38-.L329
	.half	2
	.byte	145,120
	.word	.L335-.L329,.L581-.L329
	.half	2
	.byte	145,120
	.word	.L333-.L329,.L626-.L329
	.half	2
	.byte	145,120
	.word	.L860-.L329,.L48-.L329
	.half	2
	.byte	145,120
	.word	.L331-.L329,.L631-.L329
	.half	2
	.byte	145,120
	.word	.L869-.L329,.L651-.L329
	.half	2
	.byte	145,120
	.word	0,0
.L662:
	.word	-1,.L329,.L853-.L329,.L34-.L329
	.half	5
	.byte	144,34,157,32,32
	.word	.L337-.L329,.L556-.L329
	.half	5
	.byte	144,34,157,32,32
	.word	.L856-.L329,.L854-.L329
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L664:
	.word	-1,.L329,.L337-.L329,.L556-.L329
	.half	5
	.byte	144,39,157,32,32
	.word	.L327-.L329,.L621-.L329
	.half	5
	.byte	144,39,157,32,32
	.word	.L35-.L329,.L38-.L329
	.half	5
	.byte	144,39,157,32,32
	.word	.L335-.L329,.L581-.L329
	.half	5
	.byte	144,39,157,32,32
	.word	.L860-.L329,.L861-.L329
	.half	5
	.byte	144,39,157,32,32
	.word	.L44-.L329,.L868-.L329
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L656:
	.word	-1,.L329,.L327-.L329,.L621-.L329
	.half	5
	.byte	144,33,157,32,0
	.word	.L854-.L329,.L859-.L329
	.half	5
	.byte	144,33,157,32,0
	.word	.L862-.L329,.L863-.L329
	.half	5
	.byte	144,33,157,32,0
	.word	.L333-.L329,.L626-.L329
	.half	5
	.byte	144,33,157,32,0
	.word	.L864-.L329,.L865-.L329
	.half	5
	.byte	144,33,157,32,0
	.word	.L44-.L329,.L48-.L329
	.half	5
	.byte	144,33,157,32,0
	.word	.L331-.L329,.L631-.L329
	.half	5
	.byte	144,33,157,32,0
	.word	.L869-.L329,.L870-.L329
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('LoadKeyCode')
	.sect	'.debug_loc'
.L384:
	.word	-1,.L385,0,.L1062-.L385
	.half	2
	.byte	138,0
	.word	.L1062-.L385,.L263-.L385
	.half	2
	.byte	138,32
	.word	.L263-.L385,.L263-.L385
	.half	2
	.byte	138,0
	.word	.L263-.L385,.L266-.L385
	.half	2
	.byte	138,32
	.word	.L266-.L385,.L266-.L385
	.half	2
	.byte	138,0
	.word	.L266-.L385,.L790-.L385
	.half	2
	.byte	138,32
	.word	.L790-.L385,.L790-.L385
	.half	2
	.byte	138,0
	.word	0,0
.L792:
	.word	-1,.L385,.L379-.L385,.L616-.L385
	.half	1
	.byte	101
	.word	.L379-.L385,.L616-.L385
	.half	1
	.byte	108
	.word	0,.L1063-.L385
	.half	1
	.byte	101
	.word	.L351-.L385,.L636-.L385
	.half	1
	.byte	108
	.word	.L925-.L385,.L571-.L385
	.half	1
	.byte	108
	.word	.L1065-.L385,.L790-.L385
	.half	1
	.byte	108
	.word	.L1067-.L385,.L1068-.L385
	.half	1
	.byte	102
	.word	0,0
.L791:
	.word	-1,.L385,.L379-.L385,.L616-.L385
	.half	1
	.byte	111
	.word	.L379-.L385,.L616-.L385
	.half	1
	.byte	100
	.word	0,.L1064-.L385
	.half	1
	.byte	100
	.word	.L351-.L385,.L636-.L385
	.half	1
	.byte	111
	.word	.L925-.L385,.L571-.L385
	.half	1
	.byte	111
	.word	.L1066-.L385,.L790-.L385
	.half	1
	.byte	111
	.word	.L1069-.L385,.L1068-.L385
	.half	1
	.byte	103
	.word	0,0
.L793:
	.word	-1,.L385,.L351-.L385,.L636-.L385
	.half	5
	.byte	144,33,157,32,0
	.word	.L925-.L385,.L571-.L385
	.half	5
	.byte	144,33,157,32,0
	.word	.L1063-.L385,.L265-.L385
	.half	5
	.byte	144,33,157,32,0
	.word	.L266-.L385,.L790-.L385
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L794:
	.word	-1,.L385,.L379-.L385,.L616-.L385
	.half	2
	.byte	145,96
	.word	.L351-.L385,.L636-.L385
	.half	2
	.byte	145,96
	.word	.L925-.L385,.L571-.L385
	.half	2
	.byte	145,96
	.word	0,.L790-.L385
	.half	2
	.byte	145,96
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('LoadSymmKey')
	.sect	'.debug_loc'
.L346:
	.word	-1,.L347,0,.L916-.L347
	.half	2
	.byte	138,0
	.word	.L916-.L347,.L93-.L347
	.half	2
	.byte	138,40
	.word	.L93-.L347,.L93-.L347
	.half	2
	.byte	138,0
	.word	.L93-.L347,.L95-.L347
	.half	2
	.byte	138,40
	.word	.L95-.L347,.L95-.L347
	.half	2
	.byte	138,0
	.word	.L95-.L347,.L101-.L347
	.half	2
	.byte	138,40
	.word	.L101-.L347,.L101-.L347
	.half	2
	.byte	138,0
	.word	.L101-.L347,.L105-.L347
	.half	2
	.byte	138,40
	.word	.L105-.L347,.L105-.L347
	.half	2
	.byte	138,0
	.word	.L105-.L347,.L114-.L347
	.half	2
	.byte	138,40
	.word	.L114-.L347,.L114-.L347
	.half	2
	.byte	138,0
	.word	.L114-.L347,.L117-.L347
	.half	2
	.byte	138,40
	.word	.L117-.L347,.L117-.L347
	.half	2
	.byte	138,0
	.word	.L117-.L347,.L119-.L347
	.half	2
	.byte	138,40
	.word	.L119-.L347,.L119-.L347
	.half	2
	.byte	138,0
	.word	.L119-.L347,.L116-.L347
	.half	2
	.byte	138,40
	.word	.L116-.L347,.L116-.L347
	.half	2
	.byte	138,0
	.word	.L116-.L347,.L818-.L347
	.half	2
	.byte	138,40
	.word	.L818-.L347,.L818-.L347
	.half	2
	.byte	138,0
	.word	0,0
.L824:
	.word	-1,.L347,0,.L917-.L347
	.half	2
	.byte	145,88
	.word	.L351-.L347,.L636-.L347
	.half	2
	.byte	145,88
	.word	.L925-.L347,.L571-.L347
	.half	2
	.byte	145,88
	.word	.L926-.L347,.L818-.L347
	.half	2
	.byte	145,88
	.word	0,0
.L827:
	.word	-1,.L347,.L103-.L347,.L917-.L347
	.half	5
	.byte	144,37,157,32,0
	.word	.L351-.L347,.L636-.L347
	.half	5
	.byte	144,37,157,32,0
	.word	.L925-.L347,.L571-.L347
	.half	5
	.byte	144,37,157,32,0
	.word	.L926-.L347,.L818-.L347
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L821:
	.word	-1,.L347,0,.L918-.L347
	.half	1
	.byte	100
	.word	.L920-.L347,.L917-.L347
	.half	1
	.byte	111
	.word	.L921-.L347,.L98-.L347
	.half	1
	.byte	100
	.word	.L351-.L347,.L636-.L347
	.half	1
	.byte	111
	.word	.L925-.L347,.L571-.L347
	.half	1
	.byte	111
	.word	.L926-.L347,.L818-.L347
	.half	1
	.byte	111
	.word	.L928-.L347,.L929-.L347
	.half	1
	.byte	102
	.word	.L931-.L347,.L932-.L347
	.half	1
	.byte	101
	.word	0,0
.L819:
	.word	-1,.L347,0,.L919-.L347
	.half	5
	.byte	144,34,157,32,0
	.word	.L916-.L347,.L917-.L347
	.half	5
	.byte	144,36,157,32,0
	.word	.L351-.L347,.L636-.L347
	.half	5
	.byte	144,36,157,32,0
	.word	.L925-.L347,.L571-.L347
	.half	5
	.byte	144,36,157,32,0
	.word	.L926-.L347,.L927-.L347
	.half	5
	.byte	144,36,157,32,0
	.word	.L121-.L347,.L122-.L347
	.half	5
	.byte	144,36,157,32,0
	.word	.L108-.L347,.L818-.L347
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L826:
	.word	-1,.L347,0,.L917-.L347
	.half	2
	.byte	145,120
	.word	.L351-.L347,.L636-.L347
	.half	2
	.byte	145,120
	.word	.L925-.L347,.L571-.L347
	.half	2
	.byte	145,120
	.word	.L926-.L347,.L818-.L347
	.half	2
	.byte	145,120
	.word	0,0
.L825:
	.word	-1,.L347,.L922-.L347,.L917-.L347
	.half	5
	.byte	144,36,157,32,32
	.word	.L351-.L347,.L636-.L347
	.half	5
	.byte	144,36,157,32,32
	.word	.L925-.L347,.L571-.L347
	.half	5
	.byte	144,36,157,32,32
	.word	.L926-.L347,.L818-.L347
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L823:
	.word	-1,.L347,.L923-.L347,.L924-.L347
	.half	5
	.byte	144,33,157,32,0
	.word	.L929-.L347,.L109-.L347
	.half	5
	.byte	144,33,157,32,0
	.word	.L117-.L347,.L930-.L347
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SaveAsymmKey')
	.sect	'.debug_loc'
.L374:
	.word	-1,.L375,0,.L1031-.L375
	.half	2
	.byte	138,0
	.word	.L1031-.L375,.L231-.L375
	.half	3
	.byte	138,248,1
	.word	.L231-.L375,.L231-.L375
	.half	2
	.byte	138,0
	.word	.L231-.L375,.L233-.L375
	.half	3
	.byte	138,248,1
	.word	.L233-.L375,.L233-.L375
	.half	2
	.byte	138,0
	.word	.L233-.L375,.L235-.L375
	.half	3
	.byte	138,248,1
	.word	.L235-.L375,.L235-.L375
	.half	2
	.byte	138,0
	.word	.L235-.L375,.L238-.L375
	.half	3
	.byte	138,248,1
	.word	.L238-.L375,.L238-.L375
	.half	2
	.byte	138,0
	.word	.L238-.L375,.L240-.L375
	.half	3
	.byte	138,248,1
	.word	.L240-.L375,.L240-.L375
	.half	2
	.byte	138,0
	.word	.L240-.L375,.L752-.L375
	.half	3
	.byte	138,248,1
	.word	.L752-.L375,.L752-.L375
	.half	2
	.byte	138,0
	.word	0,0
.L757:
	.word	-1,.L375,.L319-.L375,.L611-.L375
	.half	3
	.byte	145,136,126
	.word	.L335-.L375,.L581-.L375
	.half	3
	.byte	145,136,126
	.word	.L371-.L375,.L576-.L375
	.half	3
	.byte	145,136,126
	.word	.L1000-.L375,.L566-.L375
	.half	3
	.byte	145,136,126
	.word	0,.L240-.L375
	.half	3
	.byte	145,136,126
	.word	.L369-.L375,.L641-.L375
	.half	3
	.byte	145,136,126
	.word	.L1047-.L375,.L752-.L375
	.half	3
	.byte	145,136,126
	.word	0,0
.L760:
	.word	-1,.L375,.L319-.L375,.L611-.L375
	.half	3
	.byte	145,140,127
	.word	.L335-.L375,.L581-.L375
	.half	3
	.byte	145,140,127
	.word	.L371-.L375,.L576-.L375
	.half	3
	.byte	145,140,127
	.word	.L1000-.L375,.L566-.L375
	.half	3
	.byte	145,140,127
	.word	0,.L240-.L375
	.half	3
	.byte	145,140,127
	.word	.L369-.L375,.L641-.L375
	.half	3
	.byte	145,140,127
	.word	.L1047-.L375,.L752-.L375
	.half	3
	.byte	145,140,127
	.word	0,0
.L758:
	.word	-1,.L375,.L319-.L375,.L611-.L375
	.half	3
	.byte	145,248,126
	.word	.L335-.L375,.L581-.L375
	.half	3
	.byte	145,248,126
	.word	.L371-.L375,.L576-.L375
	.half	3
	.byte	145,248,126
	.word	.L1000-.L375,.L566-.L375
	.half	3
	.byte	145,248,126
	.word	0,.L240-.L375
	.half	3
	.byte	145,248,126
	.word	.L369-.L375,.L641-.L375
	.half	3
	.byte	145,248,126
	.word	.L1047-.L375,.L752-.L375
	.half	3
	.byte	145,248,126
	.word	0,0
.L759:
	.word	-1,.L375,.L319-.L375,.L611-.L375
	.half	3
	.byte	145,136,127
	.word	.L335-.L375,.L581-.L375
	.half	3
	.byte	145,136,127
	.word	.L371-.L375,.L576-.L375
	.half	3
	.byte	145,136,127
	.word	.L1000-.L375,.L566-.L375
	.half	3
	.byte	145,136,127
	.word	0,.L240-.L375
	.half	3
	.byte	145,136,127
	.word	.L369-.L375,.L641-.L375
	.half	3
	.byte	145,136,127
	.word	.L1047-.L375,.L752-.L375
	.half	3
	.byte	145,136,127
	.word	0,0
.L756:
	.word	-1,.L375,.L1042-.L375,.L1043-.L375
	.half	5
	.byte	144,33,157,32,0
	.word	.L1046-.L375,.L240-.L375
	.half	5
	.byte	144,33,157,32,0
	.word	.L369-.L375,.L641-.L375
	.half	5
	.byte	144,33,157,32,0
	.word	.L1047-.L375,.L1048-.L375
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L755:
	.word	-1,.L375,0,.L1032-.L375
	.half	1
	.byte	102
	.word	.L1040-.L375,.L1035-.L375
	.half	1
	.byte	101
	.word	.L319-.L375,.L611-.L375
	.half	1
	.byte	109
	.word	.L335-.L375,.L581-.L375
	.half	1
	.byte	109
	.word	.L371-.L375,.L576-.L375
	.half	1
	.byte	109
	.word	.L1000-.L375,.L566-.L375
	.half	1
	.byte	109
	.word	.L1044-.L375,.L240-.L375
	.half	1
	.byte	109
	.word	.L369-.L375,.L641-.L375
	.half	1
	.byte	109
	.word	.L1047-.L375,.L752-.L375
	.half	1
	.byte	109
	.word	0,0
.L753:
	.word	-1,.L375,0,.L1033-.L375
	.half	1
	.byte	100
	.word	.L1034-.L375,.L1035-.L375
	.half	1
	.byte	111
	.word	.L1036-.L375,.L1037-.L375
	.half	1
	.byte	101
	.word	.L319-.L375,.L1041-.L375
	.half	1
	.byte	111
	.word	0,0
.L754:
	.word	-1,.L375,0,.L1032-.L375
	.half	1
	.byte	101
	.word	.L1038-.L375,.L1039-.L375
	.half	1
	.byte	101
	.word	.L319-.L375,.L611-.L375
	.half	1
	.byte	108
	.word	.L335-.L375,.L581-.L375
	.half	1
	.byte	108
	.word	.L371-.L375,.L576-.L375
	.half	1
	.byte	108
	.word	.L1000-.L375,.L566-.L375
	.half	1
	.byte	108
	.word	.L1045-.L375,.L240-.L375
	.half	1
	.byte	108
	.word	.L369-.L375,.L641-.L375
	.half	1
	.byte	108
	.word	.L1047-.L375,.L752-.L375
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SaveCFGData')
	.sect	'.debug_loc'
.L380:
	.word	-1,.L381,0,.L768-.L381
	.half	2
	.byte	138,0
	.word	0,0
.L769:
	.word	-1,.L381,0,.L768-.L381
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SaveCert')
	.sect	'.debug_loc'
.L338:
	.word	-1,.L339,0,.L873-.L339
	.half	2
	.byte	138,0
	.word	.L873-.L339,.L53-.L339
	.half	3
	.byte	138,136,2
	.word	.L53-.L339,.L53-.L339
	.half	2
	.byte	138,0
	.word	.L53-.L339,.L59-.L339
	.half	3
	.byte	138,136,2
	.word	.L59-.L339,.L59-.L339
	.half	2
	.byte	138,0
	.word	.L59-.L339,.L61-.L339
	.half	3
	.byte	138,136,2
	.word	.L61-.L339,.L61-.L339
	.half	2
	.byte	138,0
	.word	.L61-.L339,.L66-.L339
	.half	3
	.byte	138,136,2
	.word	.L66-.L339,.L66-.L339
	.half	2
	.byte	138,0
	.word	.L66-.L339,.L665-.L339
	.half	3
	.byte	138,136,2
	.word	.L665-.L339,.L665-.L339
	.half	2
	.byte	138,0
	.word	0,0
.L671:
	.word	-1,.L339,.L341-.L339,.L586-.L339
	.half	3
	.byte	145,248,125
	.word	0,.L874-.L339
	.half	3
	.byte	145,248,125
	.word	.L343-.L339,.L566-.L339
	.half	3
	.byte	145,248,125
	.word	.L333-.L339,.L626-.L339
	.half	3
	.byte	145,248,125
	.word	.L883-.L339,.L68-.L339
	.half	3
	.byte	145,248,125
	.word	.L331-.L339,.L631-.L339
	.half	3
	.byte	145,248,125
	.word	.L893-.L339,.L665-.L339
	.half	3
	.byte	145,248,125
	.word	0,0
.L668:
	.word	-1,.L339,.L341-.L339,.L586-.L339
	.half	1
	.byte	109
	.word	.L341-.L339,.L586-.L339
	.half	1
	.byte	100
	.word	.L875-.L339,.L874-.L339
	.half	1
	.byte	109
	.word	0,.L876-.L339
	.half	1
	.byte	100
	.word	.L343-.L339,.L566-.L339
	.half	1
	.byte	109
	.word	.L885-.L339,.L884-.L339
	.half	1
	.byte	100
	.word	.L333-.L339,.L626-.L339
	.half	1
	.byte	109
	.word	.L883-.L339,.L68-.L339
	.half	1
	.byte	109
	.word	.L331-.L339,.L631-.L339
	.half	1
	.byte	109
	.word	.L893-.L339,.L665-.L339
	.half	1
	.byte	109
	.word	.L895-.L339,.L894-.L339
	.half	1
	.byte	101
	.word	0,0
.L672:
	.word	-1,.L339,.L876-.L339,.L52-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	.L53-.L339,.L51-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	.L58-.L339,.L874-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	.L343-.L339,.L566-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	.L883-.L339,.L884-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L666:
	.word	-1,.L339,.L341-.L339,.L586-.L339
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L876-.L339
	.half	5
	.byte	144,34,157,32,0
	.word	.L877-.L339,.L876-.L339
	.half	5
	.byte	144,37,157,32,0
	.word	.L53-.L339,.L878-.L339
	.half	5
	.byte	144,37,157,32,0
	.word	.L66-.L339,.L892-.L339
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L667:
	.word	-1,.L339,.L341-.L339,.L586-.L339
	.half	5
	.byte	144,34,157,32,32
	.word	0,.L876-.L339
	.half	5
	.byte	144,34,157,32,32
	.word	.L881-.L339,.L882-.L339
	.half	5
	.byte	144,37,157,32,32
	.word	.L885-.L339,.L886-.L339
	.half	5
	.byte	144,37,157,32,32
	.word	.L886-.L339,.L884-.L339
	.half	5
	.byte	144,34,157,32,0
	.word	.L887-.L339,.L888-.L339
	.half	5
	.byte	144,37,157,32,32
	.word	.L888-.L339,.L889-.L339
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L674:
	.word	-1,.L339,.L341-.L339,.L586-.L339
	.half	2
	.byte	145,120
	.word	0,.L874-.L339
	.half	2
	.byte	145,120
	.word	.L343-.L339,.L566-.L339
	.half	2
	.byte	145,120
	.word	.L333-.L339,.L626-.L339
	.half	2
	.byte	145,120
	.word	.L883-.L339,.L68-.L339
	.half	2
	.byte	145,120
	.word	.L331-.L339,.L631-.L339
	.half	2
	.byte	145,120
	.word	.L893-.L339,.L665-.L339
	.half	2
	.byte	145,120
	.word	0,0
.L673:
	.word	-1,.L339,.L880-.L339,.L51-.L339
	.half	5
	.byte	144,36,157,32,0
	.word	.L58-.L339,.L874-.L339
	.half	5
	.byte	144,36,157,32,0
	.word	.L343-.L339,.L566-.L339
	.half	5
	.byte	144,36,157,32,0
	.word	.L890-.L339,.L891-.L339
	.half	5
	.byte	144,34,157,32,32
	.word	.L333-.L339,.L626-.L339
	.half	5
	.byte	144,36,157,32,0
	.word	.L883-.L339,.L68-.L339
	.half	5
	.byte	144,36,157,32,0
	.word	.L331-.L339,.L631-.L339
	.half	5
	.byte	144,36,157,32,0
	.word	.L893-.L339,.L665-.L339
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L675:
	.word	-1,.L339,.L879-.L339,.L56-.L339
	.half	5
	.byte	144,36,157,32,32
	.word	.L58-.L339,.L874-.L339
	.half	5
	.byte	144,36,157,32,32
	.word	.L343-.L339,.L566-.L339
	.half	5
	.byte	144,36,157,32,32
	.word	.L333-.L339,.L626-.L339
	.half	5
	.byte	144,36,157,32,32
	.word	.L883-.L339,.L68-.L339
	.half	5
	.byte	144,36,157,32,32
	.word	.L331-.L339,.L631-.L339
	.half	5
	.byte	144,36,157,32,32
	.word	.L893-.L339,.L665-.L339
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L670:
	.word	-1,.L339,.L884-.L339,.L65-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	.L333-.L339,.L626-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	.L66-.L339,.L68-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	.L331-.L339,.L631-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	.L893-.L339,.L894-.L339
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SaveKeyCode')
	.sect	'.debug_loc'
.L382:
	.word	-1,.L383,0,.L1058-.L383
	.half	2
	.byte	138,0
	.word	.L1058-.L383,.L254-.L383
	.half	2
	.byte	138,40
	.word	.L254-.L383,.L254-.L383
	.half	2
	.byte	138,0
	.word	.L254-.L383,.L256-.L383
	.half	2
	.byte	138,40
	.word	.L256-.L383,.L256-.L383
	.half	2
	.byte	138,0
	.word	.L256-.L383,.L258-.L383
	.half	2
	.byte	138,40
	.word	.L258-.L383,.L258-.L383
	.half	2
	.byte	138,0
	.word	.L258-.L383,.L783-.L383
	.half	2
	.byte	138,40
	.word	.L783-.L383,.L783-.L383
	.half	2
	.byte	138,0
	.word	0,0
.L785:
	.word	-1,.L383,0,.L1059-.L383
	.half	1
	.byte	100
	.word	.L341-.L383,.L586-.L383
	.half	1
	.byte	100
	.word	.L343-.L383,.L566-.L383
	.half	1
	.byte	100
	.word	.L1060-.L383,.L1061-.L383
	.half	1
	.byte	100
	.word	0,0
.L784:
	.word	-1,.L383,0,.L1059-.L383
	.half	5
	.byte	144,34,157,32,0
	.word	.L341-.L383,.L586-.L383
	.half	5
	.byte	144,34,157,32,0
	.word	.L343-.L383,.L566-.L383
	.half	5
	.byte	144,34,157,32,0
	.word	.L1060-.L383,.L1061-.L383
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L789:
	.word	-1,.L383,0,.L1059-.L383
	.half	2
	.byte	145,120
	.word	.L341-.L383,.L586-.L383
	.half	2
	.byte	145,120
	.word	.L343-.L383,.L566-.L383
	.half	2
	.byte	145,120
	.word	.L1060-.L383,.L783-.L383
	.half	2
	.byte	145,120
	.word	0,0
.L786:
	.word	-1,.L383,.L1061-.L383,.L783-.L383
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L788:
	.word	-1,.L383,0,.L1059-.L383
	.half	2
	.byte	145,88
	.word	.L341-.L383,.L586-.L383
	.half	2
	.byte	145,88
	.word	.L343-.L383,.L566-.L383
	.half	2
	.byte	145,88
	.word	.L1060-.L383,.L783-.L383
	.half	2
	.byte	145,88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SaveSymmKey')
	.sect	'.debug_loc'
.L352:
	.word	-1,.L353,0,.L933-.L353
	.half	2
	.byte	138,0
	.word	.L933-.L353,.L125-.L353
	.half	2
	.byte	138,40
	.word	.L125-.L353,.L125-.L353
	.half	2
	.byte	138,0
	.word	.L125-.L353,.L127-.L353
	.half	2
	.byte	138,40
	.word	.L127-.L353,.L127-.L353
	.half	2
	.byte	138,0
	.word	.L127-.L353,.L129-.L353
	.half	2
	.byte	138,40
	.word	.L129-.L353,.L129-.L353
	.half	2
	.byte	138,0
	.word	.L129-.L353,.L828-.L353
	.half	2
	.byte	138,40
	.word	.L828-.L353,.L828-.L353
	.half	2
	.byte	138,0
	.word	0,0
.L834:
	.word	-1,.L353,0,.L934-.L353
	.half	2
	.byte	145,92
	.word	.L341-.L353,.L586-.L353
	.half	2
	.byte	145,92
	.word	.L935-.L353,.L127-.L353
	.half	2
	.byte	145,92
	.word	.L351-.L353,.L636-.L353
	.half	2
	.byte	145,92
	.word	.L343-.L353,.L566-.L353
	.half	2
	.byte	145,92
	.word	.L936-.L353,.L828-.L353
	.half	2
	.byte	145,92
	.word	0,0
.L835:
	.word	-1,.L353,.L131-.L353,.L828-.L353
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L830:
	.word	-1,.L353,0,.L934-.L353
	.half	1
	.byte	100
	.word	.L341-.L353,.L586-.L353
	.half	1
	.byte	100
	.word	.L935-.L353,.L127-.L353
	.half	1
	.byte	100
	.word	.L351-.L353,.L636-.L353
	.half	1
	.byte	100
	.word	.L343-.L353,.L566-.L353
	.half	1
	.byte	100
	.word	.L936-.L353,.L937-.L353
	.half	1
	.byte	100
	.word	0,0
.L829:
	.word	-1,.L353,0,.L934-.L353
	.half	5
	.byte	144,34,157,32,0
	.word	.L933-.L353,.L934-.L353
	.half	5
	.byte	144,36,157,32,0
	.word	.L341-.L353,.L586-.L353
	.half	5
	.byte	144,36,157,32,0
	.word	.L341-.L353,.L586-.L353
	.half	5
	.byte	144,34,157,32,0
	.word	.L935-.L353,.L127-.L353
	.half	5
	.byte	144,36,157,32,0
	.word	.L935-.L353,.L127-.L353
	.half	5
	.byte	144,34,157,32,0
	.word	.L351-.L353,.L636-.L353
	.half	5
	.byte	144,36,157,32,0
	.word	.L351-.L353,.L351-.L353
	.half	5
	.byte	144,34,157,32,0
	.word	.L343-.L353,.L566-.L353
	.half	5
	.byte	144,36,157,32,0
	.word	.L936-.L353,.L938-.L353
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L833:
	.word	-1,.L353,0,.L934-.L353
	.half	2
	.byte	145,88
	.word	.L341-.L353,.L586-.L353
	.half	2
	.byte	145,88
	.word	.L935-.L353,.L127-.L353
	.half	2
	.byte	145,88
	.word	.L351-.L353,.L636-.L353
	.half	2
	.byte	145,88
	.word	.L343-.L353,.L566-.L353
	.half	2
	.byte	145,88
	.word	.L936-.L353,.L828-.L353
	.half	2
	.byte	145,88
	.word	0,0
.L832:
	.word	-1,.L353,.L939-.L353,.L940-.L353
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L831:
	.word	-1,.L353,.L937-.L353,.L131-.L353
	.half	5
	.byte	144,33,157,32,0
	.word	.L941-.L353,.L828-.L353
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SetAlg')
	.sect	'.debug_loc'
.L390:
	.word	-1,.L391,0,.L1075-.L391
	.half	2
	.byte	138,0
	.word	.L1075-.L391,.L283-.L391
	.half	2
	.byte	138,8
	.word	.L283-.L391,.L283-.L391
	.half	2
	.byte	138,0
	.word	.L283-.L391,.L285-.L391
	.half	2
	.byte	138,8
	.word	.L285-.L391,.L285-.L391
	.half	2
	.byte	138,0
	.word	.L285-.L391,.L795-.L391
	.half	2
	.byte	138,8
	.word	.L795-.L391,.L795-.L391
	.half	2
	.byte	138,0
	.word	0,0
.L796:
	.word	-1,.L391,0,.L282-.L391
	.half	2
	.byte	145,120
	.word	0,.L282-.L391
	.half	5
	.byte	144,34,157,32,0
	.word	.L337-.L391,.L556-.L391
	.half	2
	.byte	145,120
	.word	.L337-.L391,.L556-.L391
	.half	5
	.byte	144,34,157,32,0
	.word	.L1077-.L391,.L1078-.L391
	.half	2
	.byte	145,120
	.word	.L1077-.L391,.L1079-.L391
	.half	5
	.byte	144,34,157,32,0
	.word	.L369-.L391,.L641-.L391
	.half	2
	.byte	145,120
	.word	.L397-.L391,.L591-.L391
	.half	2
	.byte	145,120
	.word	.L333-.L391,.L626-.L391
	.half	2
	.byte	145,120
	.word	.L1082-.L391,.L601-.L391
	.half	2
	.byte	145,120
	.word	.L331-.L391,.L631-.L391
	.half	2
	.byte	145,120
	.word	.L1083-.L391,.L606-.L391
	.half	2
	.byte	145,120
	.word	.L287-.L391,.L795-.L391
	.half	2
	.byte	145,120
	.word	0,0
.L798:
	.word	-1,.L391,.L1076-.L391,.L282-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L337-.L391,.L556-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L1077-.L391,.L1080-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L1080-.L391,.L1078-.L391
	.half	5
	.byte	144,33,157,32,0
	.word	.L1081-.L391,.L1078-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L369-.L391,.L641-.L391
	.half	5
	.byte	144,33,157,32,0
	.word	.L369-.L391,.L641-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L397-.L391,.L591-.L391
	.half	5
	.byte	144,33,157,32,0
	.word	.L397-.L391,.L591-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L333-.L391,.L626-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L1082-.L391,.L601-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L331-.L391,.L631-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L1083-.L391,.L606-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	.L287-.L391,.L795-.L391
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SetEnvironment')
	.sect	'.debug_loc'
.L400:
	.word	-1,.L401,0,.L1086-.L401
	.half	2
	.byte	138,0
	.word	.L1086-.L401,.L297-.L401
	.half	2
	.byte	138,8
	.word	.L297-.L401,.L297-.L401
	.half	2
	.byte	138,0
	.word	.L297-.L401,.L299-.L401
	.half	2
	.byte	138,8
	.word	.L299-.L401,.L299-.L401
	.half	2
	.byte	138,0
	.word	.L299-.L401,.L802-.L401
	.half	2
	.byte	138,8
	.word	.L802-.L401,.L802-.L401
	.half	2
	.byte	138,0
	.word	0,0
.L803:
	.word	-1,.L401,0,.L295-.L401
	.half	2
	.byte	145,120
	.word	0,.L295-.L401
	.half	5
	.byte	144,34,157,32,0
	.word	.L337-.L401,.L556-.L401
	.half	2
	.byte	145,120
	.word	.L337-.L401,.L556-.L401
	.half	5
	.byte	144,34,157,32,0
	.word	.L1088-.L401,.L1089-.L401
	.half	5
	.byte	144,34,157,32,0
	.word	.L369-.L401,.L641-.L401
	.half	2
	.byte	145,120
	.word	.L397-.L401,.L591-.L401
	.half	2
	.byte	145,120
	.word	.L333-.L401,.L626-.L401
	.half	2
	.byte	145,120
	.word	.L1082-.L401,.L601-.L401
	.half	2
	.byte	145,120
	.word	.L331-.L401,.L631-.L401
	.half	2
	.byte	145,120
	.word	.L1083-.L401,.L606-.L401
	.half	2
	.byte	145,120
	.word	.L1088-.L401,.L802-.L401
	.half	2
	.byte	145,120
	.word	0,0
.L804:
	.word	-1,.L401,.L1087-.L401,.L295-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L337-.L401,.L556-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L1088-.L401,.L1090-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L369-.L401,.L641-.L401
	.half	5
	.byte	144,33,157,32,0
	.word	.L369-.L401,.L641-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L397-.L401,.L591-.L401
	.half	5
	.byte	144,33,157,32,0
	.word	.L397-.L401,.L591-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L1090-.L401,.L1091-.L401
	.half	5
	.byte	144,33,157,32,0
	.word	.L333-.L401,.L626-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L1082-.L401,.L601-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L331-.L401,.L631-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L1083-.L401,.L606-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	.L1092-.L401,.L802-.L401
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SetKeyActive')
	.sect	'.debug_loc'
.L358:
	.word	-1,.L359,0,.L953-.L359
	.half	2
	.byte	138,0
	.word	.L953-.L359,.L150-.L359
	.half	2
	.byte	138,32
	.word	.L150-.L359,.L150-.L359
	.half	2
	.byte	138,0
	.word	.L150-.L359,.L154-.L359
	.half	2
	.byte	138,32
	.word	.L154-.L359,.L154-.L359
	.half	2
	.byte	138,0
	.word	.L154-.L359,.L153-.L359
	.half	2
	.byte	138,32
	.word	.L153-.L359,.L153-.L359
	.half	2
	.byte	138,0
	.word	.L153-.L359,.L709-.L359
	.half	2
	.byte	138,32
	.word	.L709-.L359,.L709-.L359
	.half	2
	.byte	138,0
	.word	0,0
.L713:
	.word	-1,.L359,0,.L709-.L359
	.half	2
	.byte	145,96
	.word	0,0
.L710:
	.word	-1,.L359,0,.L954-.L359
	.half	5
	.byte	144,34,157,32,0
	.word	.L955-.L359,.L956-.L359
	.half	5
	.byte	144,36,157,32,0
	.word	.L957-.L359,.L954-.L359
	.half	5
	.byte	144,36,157,32,0
	.word	.L960-.L359,.L961-.L359
	.half	5
	.byte	144,36,157,32,0
	.word	.L961-.L359,.L154-.L359
	.half	5
	.byte	144,34,157,32,0
	.word	.L962-.L359,.L963-.L359
	.half	5
	.byte	144,36,157,32,0
	.word	.L963-.L359,.L153-.L359
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L712:
	.word	-1,.L359,.L954-.L359,.L152-.L359
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L711:
	.word	-1,.L359,0,.L954-.L359
	.half	5
	.byte	144,34,157,32,32
	.word	.L958-.L359,.L959-.L359
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SetSessKey')
	.sect	'.debug_loc'
.L362:
	.word	-1,.L363,0,.L719-.L363
	.half	2
	.byte	138,0
	.word	0,0
.L722:
	.word	-1,.L363,0,.L719-.L363
	.half	1
	.byte	100
	.word	0,0
.L720:
	.word	-1,.L363,0,.L968-.L363
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L721:
	.word	-1,.L363,0,.L969-.L363
	.half	5
	.byte	144,34,157,32,32
	.word	.L970-.L363,.L719-.L363
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SetSymmKey')
	.sect	'.debug_loc'
.L364:
	.word	-1,.L365,0,.L971-.L365
	.half	2
	.byte	138,0
	.word	.L971-.L365,.L175-.L365
	.half	2
	.byte	138,56
	.word	.L175-.L365,.L175-.L365
	.half	2
	.byte	138,0
	.word	.L175-.L365,.L177-.L365
	.half	2
	.byte	138,56
	.word	.L177-.L365,.L177-.L365
	.half	2
	.byte	138,0
	.word	.L177-.L365,.L183-.L365
	.half	2
	.byte	138,56
	.word	.L183-.L365,.L183-.L365
	.half	2
	.byte	138,0
	.word	.L183-.L365,.L185-.L365
	.half	2
	.byte	138,56
	.word	.L185-.L365,.L185-.L365
	.half	2
	.byte	138,0
	.word	.L185-.L365,.L187-.L365
	.half	2
	.byte	138,56
	.word	.L187-.L365,.L187-.L365
	.half	2
	.byte	138,0
	.word	.L187-.L365,.L723-.L365
	.half	2
	.byte	138,56
	.word	.L723-.L365,.L723-.L365
	.half	2
	.byte	138,0
	.word	0,0
.L730:
	.word	-1,.L365,0,.L723-.L365
	.half	2
	.byte	145,76
	.word	0,0
.L725:
	.word	-1,.L365,0,.L972-.L365
	.half	5
	.byte	144,34,157,32,32
	.word	.L972-.L365,.L978-.L365
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L731:
	.word	-1,.L365,0,.L723-.L365
	.half	2
	.byte	145,92
	.word	0,0
.L727:
	.word	-1,.L365,0,.L973-.L365
	.half	1
	.byte	100
	.word	.L974-.L365,.L975-.L365
	.half	1
	.byte	111
	.word	.L980-.L365,.L981-.L365
	.half	1
	.byte	101
	.word	.L189-.L365,.L988-.L365
	.half	1
	.byte	111
	.word	0,0
.L724:
	.word	-1,.L365,0,.L973-.L365
	.half	5
	.byte	144,34,157,32,0
	.word	.L976-.L365,.L977-.L365
	.half	5
	.byte	144,36,157,32,0
	.word	.L978-.L365,.L979-.L365
	.half	5
	.byte	144,36,157,32,0
	.word	.L984-.L365,.L985-.L365
	.half	5
	.byte	144,36,157,32,0
	.word	.L187-.L365,.L987-.L365
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L726:
	.word	-1,.L365,0,.L972-.L365
	.half	5
	.byte	144,35,157,32,0
	.word	.L971-.L365,.L723-.L365
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L729:
	.word	-1,.L365,0,.L723-.L365
	.half	2
	.byte	145,72
	.word	0,0
.L728:
	.word	-1,.L365,.L982-.L365,.L983-.L365
	.half	5
	.byte	144,33,157,32,0
	.word	.L985-.L365,.L986-.L365
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SetWroteFlag')
	.sect	'.debug_loc'
.L404:
	.word	-1,.L405,0,.L1097-.L405
	.half	2
	.byte	138,0
	.word	.L1097-.L405,.L309-.L405
	.half	2
	.byte	138,8
	.word	.L309-.L405,.L309-.L405
	.half	2
	.byte	138,0
	.word	.L309-.L405,.L808-.L405
	.half	2
	.byte	138,8
	.word	.L808-.L405,.L808-.L405
	.half	2
	.byte	138,0
	.word	0,0
.L809:
	.word	-1,.L405,.L337-.L405,.L556-.L405
	.half	2
	.byte	145,120
	.word	.L337-.L405,.L556-.L405
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L808-.L405
	.half	2
	.byte	145,120
	.word	0,.L1098-.L405
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L810:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('getEnvIndex')
	.sect	'.debug_loc'
.L816:
	.word	-1,.L317,0,.L2-.L317
	.half	5
	.byte	144,34,157,32,0
	.word	.L319-.L317,.L611-.L317
	.half	5
	.byte	144,34,157,32,0
	.word	.L321-.L317,.L561-.L317
	.half	5
	.byte	144,34,157,32,0
	.word	.L836-.L317,.L815-.L317
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L316:
	.word	-1,.L317,0,.L815-.L317
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1740:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('getEnvIndex')
	.sect	'.debug_frame'
	.word	24
	.word	.L1740,.L317,.L815-.L317
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('LoadCert')
	.sect	'.debug_frame'
	.word	12
	.word	.L1740,.L323,.L643-.L323
	.sdecl	'.debug_frame',debug,cluster('LoadCertById')
	.sect	'.debug_frame'
	.word	112
	.word	.L1740,.L329,.L651-.L329
	.byte	4
	.word	(.L852-.L329)/2
	.byte	19,152,4,22,26,4,19,138,152,4,4
	.word	(.L36-.L852)/2
	.byte	19,0,8,26,19,152,4,22,26,4,19,138,152,4,4
	.word	(.L38-.L36)/2
	.byte	19,0,8,26,19,152,4,22,26,4,19,138,152,4,4
	.word	(.L41-.L38)/2
	.byte	19,0,8,26,19,152,4,22,26,4,19,138,152,4,4
	.word	(.L48-.L41)/2
	.byte	19,0,8,26,19,152,4,22,26,4,19,138,152,4,4
	.word	(.L651-.L48)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('SaveCert')
	.sect	'.debug_frame'
	.word	112
	.word	.L1740,.L339,.L665-.L339
	.byte	4
	.word	(.L873-.L339)/2
	.byte	19,136,2,22,26,4,19,138,136,2,4
	.word	(.L53-.L873)/2
	.byte	19,0,8,26,19,136,2,22,26,4,19,138,136,2,4
	.word	(.L59-.L53)/2
	.byte	19,0,8,26,19,136,2,22,26,4,19,138,136,2,4
	.word	(.L61-.L59)/2
	.byte	19,0,8,26,19,136,2,22,26,4,19,138,136,2,4
	.word	(.L66-.L61)/2
	.byte	19,0,8,26,19,136,2,22,26,4,19,138,136,2,4
	.word	(.L665-.L66)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('GenSymmKey')
	.sect	'.debug_frame'
	.word	96
	.word	.L1740,.L345,.L676-.L345
	.byte	4
	.word	(.L896-.L345)/2
	.byte	19,232,4,22,26,4,19,138,232,4,4
	.word	(.L72-.L896)/2
	.byte	19,0,8,26,19,232,4,22,26,4,19,138,232,4,4
	.word	(.L74-.L72)/2
	.byte	19,0,8,26,19,232,4,22,26,4,19,138,232,4,4
	.word	(.L77-.L74)/2
	.byte	19,0,8,26,19,232,4,22,26,4,19,138,232,4,4
	.word	(.L676-.L77)/2
	.byte	19,0,8,26,0,0,0
	.sdecl	'.debug_frame',debug,cluster('LoadSymmKey')
	.sect	'.debug_frame'
	.word	172
	.word	.L1740,.L347,.L818-.L347
	.byte	4
	.word	(.L916-.L347)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L93-.L916)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L95-.L93)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L101-.L95)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L105-.L101)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L114-.L105)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L117-.L114)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L119-.L117)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L116-.L119)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L818-.L116)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('SaveSymmKey')
	.sect	'.debug_frame'
	.word	88
	.word	.L1740,.L353,.L828-.L353
	.byte	4
	.word	(.L933-.L353)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L125-.L933)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L127-.L125)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L129-.L127)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L828-.L129)/2
	.byte	19,0,8,26,0,0,0
	.sdecl	'.debug_frame',debug,cluster('GetSessKey')
	.sect	'.debug_frame'
	.word	12
	.word	.L1740,.L355,.L698-.L355
	.sdecl	'.debug_frame',debug,cluster('GetSymmKey')
	.sect	'.debug_frame'
	.word	68
	.word	.L1740,.L357,.L702-.L357
	.byte	4
	.word	(.L943-.L357)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L140-.L943)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L142-.L140)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L702-.L142)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('SetKeyActive')
	.sect	'.debug_frame'
	.word	88
	.word	.L1740,.L359,.L709-.L359
	.byte	4
	.word	(.L953-.L359)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L150-.L953)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L154-.L150)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L153-.L154)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L709-.L153)/2
	.byte	19,0,8,26,0,0,0
	.sdecl	'.debug_frame',debug,cluster('GetKeyActive')
	.sect	'.debug_frame'
	.word	104
	.word	.L1740,.L361,.L714-.L361
	.byte	4
	.word	(.L964-.L361)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L161-.L964)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L163-.L161)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L167-.L163)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L166-.L167)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L714-.L166)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('SetSessKey')
	.sect	'.debug_frame'
	.word	12
	.word	.L1740,.L363,.L719-.L363
	.sdecl	'.debug_frame',debug,cluster('SetSymmKey')
	.sect	'.debug_frame'
	.word	120
	.word	.L1740,.L365,.L723-.L365
	.byte	4
	.word	(.L971-.L365)/2
	.byte	19,56,22,26,3,19,138,56,4
	.word	(.L175-.L971)/2
	.byte	19,0,8,26,19,56,22,26,3,19,138,56,4
	.word	(.L177-.L175)/2
	.byte	19,0,8,26,19,56,22,26,3,19,138,56,4
	.word	(.L183-.L177)/2
	.byte	19,0,8,26,19,56,22,26,3,19,138,56,4
	.word	(.L185-.L183)/2
	.byte	19,0,8,26,19,56,22,26,3,19,138,56,4
	.word	(.L187-.L185)/2
	.byte	19,0,8,26,19,56,22,26,3,19,138,56,4
	.word	(.L723-.L187)/2
	.byte	19,0,8,26,0
	.sdecl	'.debug_frame',debug,cluster('GenAsymmKey')
	.sect	'.debug_frame'
	.word	112
	.word	.L1740,.L367,.L732-.L367
	.byte	4
	.word	(.L989-.L367)/2
	.byte	19,248,1,22,26,4,19,138,248,1,4
	.word	(.L193-.L989)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L199-.L193)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L205-.L199)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L207-.L205)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L732-.L207)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('LoadAsymmKey')
	.sect	'.debug_frame'
	.word	152
	.word	.L1740,.L373,.L743-.L373
	.byte	4
	.word	(.L1008-.L373)/2
	.byte	19,248,1,22,26,4,19,138,248,1,4
	.word	(.L212-.L1008)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L216-.L212)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L218-.L216)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L221-.L218)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L223-.L221)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L226-.L223)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L743-.L226)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('SaveAsymmKey')
	.sect	'.debug_frame'
	.word	132
	.word	.L1740,.L375,.L752-.L375
	.byte	4
	.word	(.L1031-.L375)/2
	.byte	19,248,1,22,26,4,19,138,248,1,4
	.word	(.L231-.L1031)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L233-.L231)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L235-.L233)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L238-.L235)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L240-.L238)/2
	.byte	19,0,8,26,19,248,1,22,26,4,19,138,248,1,4
	.word	(.L752-.L240)/2
	.byte	19,0,8,26,0
	.sdecl	'.debug_frame',debug,cluster('LoadCFGData')
	.sect	'.debug_frame'
	.word	96
	.word	.L1740,.L377,.L761-.L377
	.byte	4
	.word	(.L1049-.L377)/2
	.byte	19,136,1,22,26,4,19,138,136,1,4
	.word	(.L243-.L1049)/2
	.byte	19,0,8,26,19,136,1,22,26,4,19,138,136,1,4
	.word	(.L245-.L243)/2
	.byte	19,0,8,26,19,136,1,22,26,4,19,138,136,1,4
	.word	(.L248-.L245)/2
	.byte	19,0,8,26,19,136,1,22,26,4,19,138,136,1,4
	.word	(.L761-.L248)/2
	.byte	19,0,8,26,0,0,0
	.sdecl	'.debug_frame',debug,cluster('SaveCFGData')
	.sect	'.debug_frame'
	.word	24
	.word	.L1740,.L381,.L768-.L381
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('SaveKeyCode')
	.sect	'.debug_frame'
	.word	88
	.word	.L1740,.L383,.L783-.L383
	.byte	4
	.word	(.L1058-.L383)/2
	.byte	19,40,22,26,3,19,138,40,4
	.word	(.L254-.L1058)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L256-.L254)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L258-.L256)/2
	.byte	19,0,8,26,19,40,22,26,3,19,138,40,4
	.word	(.L783-.L258)/2
	.byte	19,0,8,26,0,0,0
	.sdecl	'.debug_frame',debug,cluster('LoadKeyCode')
	.sect	'.debug_frame'
	.word	68
	.word	.L1740,.L385,.L790-.L385
	.byte	4
	.word	(.L1062-.L385)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L263-.L1062)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L266-.L263)/2
	.byte	19,0,8,26,19,32,22,26,3,19,138,32,4
	.word	(.L790-.L266)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('CryptoPaddingTo16')
	.sect	'.debug_frame'
	.word	24
	.word	.L1740,.L387,.L770-.L387
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('CryptoUnPadding16')
	.sect	'.debug_frame'
	.word	24
	.word	.L1740,.L389,.L777-.L389
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('SetAlg')
	.sect	'.debug_frame'
	.word	68
	.word	.L1740,.L391,.L795-.L391
	.byte	4
	.word	(.L1075-.L391)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L283-.L1075)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L285-.L283)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L795-.L285)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('GetAlg')
	.sect	'.debug_frame'
	.word	52
	.word	.L1740,.L399,.L799-.L399
	.byte	4
	.word	(.L1084-.L399)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L290-.L1084)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L799-.L290)/2
	.byte	19,0,8,26,0
	.sdecl	'.debug_frame',debug,cluster('SetEnvironment')
	.sect	'.debug_frame'
	.word	68
	.word	.L1740,.L401,.L802-.L401
	.byte	4
	.word	(.L1086-.L401)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L297-.L1086)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L299-.L297)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L802-.L299)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('GetEnvironment')
	.sect	'.debug_frame'
	.word	52
	.word	.L1740,.L403,.L805-.L403
	.byte	4
	.word	(.L1093-.L403)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L304-.L1093)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L805-.L304)/2
	.byte	19,0,8,26,0
	.sdecl	'.debug_frame',debug,cluster('SetWroteFlag')
	.sect	'.debug_frame'
	.word	52
	.word	.L1740,.L405,.L808-.L405
	.byte	4
	.word	(.L1097-.L405)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L309-.L1097)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L808-.L309)/2
	.byte	19,0,8,26,0
	.sdecl	'.debug_frame',debug,cluster('GetWroteFLag')
	.sect	'.debug_frame'
	.word	52
	.word	.L1740,.L407,.L811-.L407
	.byte	4
	.word	(.L1099-.L407)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L312-.L1099)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L811-.L312)/2
	.byte	19,0,8,26,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1741:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_12')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L319,.L611-.L319
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L321,.L561-.L321
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_9')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L325,.L596-.L325
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_14')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L327,.L621-.L327
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_16')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L331,.L631-.L331
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_15')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L333,.L626-.L333
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_6')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L335,.L581-.L335
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L337,.L556-.L337
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_7')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L341,.L586-.L341
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L343,.L566-.L343
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_4')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L349,.L571-.L349
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_17')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L351,.L636-.L351
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_18')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L369,.L641-.L369
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_5')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L371,.L576-.L371
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_13')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L379,.L616-.L379
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_11')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L393,.L606-.L393
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_10')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L395,.L601-.L395
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_8')
	.sect	'.debug_frame'
	.word	24
	.word	.L1741,.L397,.L591-.L397
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\vss_code\vsskeym.c	  1067  
; ..\vss_code\vsskeym.c	  1068  #pragma section code restore
; ..\vss_code\vsskeym.c	  1069  
; ..\vss_code\vsskeym.c	  1070  

	; Module end
