	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc23928a -c99 --dep-file=Src_file\\.CDD_EyeQ_PowerCtrl.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=Src_file\\CDD_EyeQ_PowerCtrl.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o Src_file\\CDD_EyeQ_PowerCtrl.src ..\\Src_file\\CDD_EyeQ_PowerCtrl.c"
	.compiler_name		"ctc"
	.name	"CDD_EyeQ_PowerCtrl"

	
$TC16X
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.CDD_IIC_COM_Delay',code,cluster('CDD_IIC_COM_Delay')
	.sect	'.text.CDD_EyeQ_PowerCtrl.CDD_IIC_COM_Delay'
	.align	2
	
	.global	CDD_IIC_COM_Delay

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     1  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     2  |  File Name:  SM_PwrMng.c
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     3  |  Description:  Implementation of the System Power Manage
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     4  |-------------------------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     5  | (c) This software is the proprietary of DIAS.
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     6  |     All rights are reserved by DIAS.
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     7  |-------------------------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     8  | Initials      Name                   Company
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	     9  | --------      --------------------   -----------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    10  | XG           XiaoGang           		DIAS
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    11  |-------------------------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    12  |               R E V I S I O N   H I S T O R Y
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    13  |-------------------------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    14  | Date          Version      Author    Description
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    15  | ------------  --------     -------   ------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    16  | 2020-04-10    01.00.00     XiaoGang       Creation
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    17  | 2020-04-11    01.00.01     Bruce          modify all
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    18  |
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    19  |
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    20  |******************************************************************************/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    21  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    22  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    23  |    Other Header File Inclusion
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    24  |******************************************************************************/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    25  #include "Std_Types.h"
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    26  #include "Dio_Cfg.h"
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    27  #include "Port.h"
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    28  #include "CDD_EyeQ_PowerCtrl.h"
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    29  //#include "EyeQ_ValSig.h"
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    30  #include "Std_Types.h"
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    31  #include "Platform_Types.h"
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    32  #include "Appl.h"
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    33  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    34  |    Macro Definition
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    35  |******************************************************************************/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    36  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    37  #define IIC_A_MaxIndex            15
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    38  #define IIC_B_MaxIndex            33
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    39  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    40  #define PortInput_Mode            0x00
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    41  #define PortOutput_Mode           0x80
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    42  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    43  #define ProtPinDirIn_ChipA        0x212
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    44  #define ProtPinDirIn_ChipB        0x210
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    45  #define EyeQ_POR                  DIO_CHANNEL_10_1
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    46  #define EyeQ_PRB                  DIO_CHANNEL_10_2
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    47  #define Res_ChipA                 DIO_CHANNEL_33_6
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    48  #define Res_ChipB                 DIO_CHANNEL_33_7
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    49  #define En_ChipA                  DIO_CHANNEL_33_3
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    50  #define En_ChipB                  DIO_CHANNEL_15_0
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    51  #define EnMicron_Spi              DIO_CHANNEL_11_2
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    52  #define EnCamPower                DIO_CHANNEL_15_3
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    53  #define EthPinDirIn               0x0A3
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    54  #define Eth_Res                   DIO_CHANNEL_10_3
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    55  #define Adc_0_StartAdd            0xF0020700
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    56  #define Adc_1_StartAdd            0xF0020B00
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    57  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    58  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    59  #define MicronClock_Id  0x0B6
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    60  #define MicronCs_Id     0x0B8
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    61  #define MicronMosi_Id   0x0B9
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    62  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    63  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    64  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    65  |    Enum Definition
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    66  |******************************************************************************/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    67  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    68  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    69  |    Typedef Definition
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    70  |******************************************************************************/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    71  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    72  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    73  |    Global variables Declaration
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    74  |******************************************************************************/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    75  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    76  uint8 SM_EthH_Flag=0;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    77  uint8 EyeQPower_Flag=0;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    78  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    79  static uint8  IIC_A_Register[IIC_A_MaxIndex]={0};
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    80  static uint8  IIC_B_Register[IIC_B_MaxIndex]={0};
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    81  uint8  IIC_A_RegVal[IIC_A_MaxIndex]={
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    82  		                    			0xC7, 0x85, 0x00, 0x00, /*0xDE*/0x5E, 0x91, 0x01, 0x55, 0x55, 0x05,
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    83  		                    			0x00, 0x02, 0x01, 0xD6, 0x71
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    84  									};
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    85  uint8  IIC_B_RegVal[IIC_B_MaxIndex]={
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    86  		                    			0xC7, 0x3C, 0xC8, 0x3C, 0xC6, 0x3C, 0xC6, 0x3C, 0x61, 0x61,
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    87  		                    			0x61, 0x61, 0xB1, 0xB1, 0xFC, 0xFC, 0x1B, 0x1B/*0x1F*/, /*0x5A*/0x58, /*0xB0*/0xB1,
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    88  		                    			0x00, 0x00, /*0xDE*/0x5E, 0x95, 0x00, 0x55, 0x55, 0x05, 0x00, 0x06,
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    89  		                    			0x01, 0xD6, 0x71
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    90                           	 	 	 };
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    91  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    92  uint8  IIC_A_RegAdd[IIC_A_MaxIndex]={
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    93  		                   	   	   	   0x02, 0x12, 0x16, 0x17, 0x19, 0x21, 0x22, 0x23, 0x24, 0x25,
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    94  		                   	   	   	   0x28, 0x29, 0x2B, 0x2C, 0x2D
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    95  									};
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    96  uint8  IIC_B_RegAdd[IIC_B_MaxIndex]={
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    97  		                   	   	   	   0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    98  		                   	   	   	   0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	    99  		                   	   	   	   0x16, 0x17, 0x19, 0x21, 0x22, 0x23, 0x24, 0x25, 0x28, 0x29,
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   100  		                   	   	   	   0x2B, 0x2C, 0x2D
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   101                           	 	 	 };
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   102  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   103  uint8 IIC_A_diag[3]={0};
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   104  uint8 IIC_B_diag[3]={0};
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   105  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   106  |    Static local variables Declaration
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   107  |******************************************************************************/	
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   108  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   109  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   110  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   111  |    Function Source Code
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   112  |******************************************************************************/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   113  void CDD_IIC_COM_Delay(uint32 Delaytime)
; Function CDD_IIC_COM_Delay
.L104:
CDD_IIC_COM_Delay:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   114  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   115  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   116  	uint32 u32t_Counter=0;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   117  	uint32 u32t_CounterOver=0xffffffff-Delaytime;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   118  	u32t_Counter = Stm0_GetSysTickTime();
	lea	a15,0xf0000010
	ld.w	d0,[a15]
.L334:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   119  	if(u32t_Counter>=(0xffffffff-Delaytime))
	rsub	d15,d4,#-1
.L433:
	jlt.u	d0,d15,.L2

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   120  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   121  		while(((0xffffffff-u32t_Counter+Stm0_GetSysTickTime())<=Delaytime)|(Stm0_GetSysTickTime()>u32t_Counter))
.L3:
	ld.w	d15,[a15]
.L434:
	ld.w	d2,[a15]
.L435:
	lt.u	d1,d0,d15
.L436:
	rsub	d15,d0,#-1
.L437:
	add	d15,d2
.L438:
	ge.u	d15,d4,d15
.L439:
	or.t	d15,d15:0,d1:0
.L440:
	jne	d15,#0,.L3
.L441:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   122  		{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   123  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   124  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   125  	else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   126  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   127  		while((Stm0_GetSysTickTime()-u32t_Counter)<=Delaytime)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   128  		{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   129  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   130  }
	ret
.L2:
.L5:
	ld.w	d15,[a15]
.L442:
	sub	d15,d0
.L443:
	jge.u	d4,d15,.L5
.L444:
	ret
.L274:
	
__CDD_IIC_COM_Delay_function_end:
	.size	CDD_IIC_COM_Delay,__CDD_IIC_COM_Delay_function_end-CDD_IIC_COM_Delay
.L153:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.CDD_IIC_Start',code,cluster('CDD_IIC_Start')
	.sect	'.text.CDD_EyeQ_PowerCtrl.CDD_IIC_Start'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   131  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   132  /*----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   133  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   134  /* delay 100us*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   135  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   136  /*----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   137  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   138  static void CDD_IIC_Start(uint8 channel)
; Function CDD_IIC_Start
.L106:
CDD_IIC_Start:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   139  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   140  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   141  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   142  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   143  		case IIC_A :
	jeq	d4,#0,.L6
.L517:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   144  			Dio_WriteChannel(SDA_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   145  			CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   146  			Dio_WriteChannel(SCL_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   147  			CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   148  			Dio_WriteChannel(SDA_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   149  			CDD_IIC_COM_Delay(200);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   150  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   151  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   152  		case IIC_B:
	jeq	d4,#1,.L7
.L518:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   153  			Dio_WriteChannel(SDA_B,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   154  			CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   155  			Dio_WriteChannel(SCL_B,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   156  			CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   157  			Dio_WriteChannel(SDA_B,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   158  			CDD_IIC_COM_Delay(200);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   159  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   160  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   161  		default:
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   162  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   163  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   164  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   165  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   166  }
	ret
.L6:
	mov	d15,#530
.L519:
	mov	d5,#1
	mov	d4,d15
	call	Dio_WriteChannel
.L335:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L520:
	mov	d4,#529
	j	.L9
.L7:
	mov	d15,#528
.L521:
	mov	d5,#1
	mov	d4,d15
	call	Dio_WriteChannel
.L336:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L522:
	mov	d4,#547
.L9:
	mov	d5,#1
	call	Dio_WriteChannel
.L523:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L524:
	mov	d5,#0
	mov	d4,d15
	call	Dio_WriteChannel
.L525:
	mov	d4,#200
	j	CDD_IIC_COM_Delay
.L288:
	
__CDD_IIC_Start_function_end:
	.size	CDD_IIC_Start,__CDD_IIC_Start_function_end-CDD_IIC_Start
.L183:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.CDD_IIC_Stop',code,cluster('CDD_IIC_Stop')
	.sect	'.text.CDD_EyeQ_PowerCtrl.CDD_IIC_Stop'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   167  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   168  /*----------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   169  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   170  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   171  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   172  ----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   173  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   174  static void CDD_IIC_Stop(uint8 channel)
; Function CDD_IIC_Stop
.L108:
CDD_IIC_Stop:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   175  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   176  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   177  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   178  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   179  		case IIC_A :
	jeq	d4,#0,.L10
.L530:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   180  			Dio_WriteChannel(SDA_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   181  			CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   182  			Dio_WriteChannel(SCL_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   183  			CDD_IIC_COM_Delay(500);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   184  			Dio_WriteChannel(SDA_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   185  			CDD_IIC_COM_Delay(200);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   186  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   187  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   188  		case IIC_B :
	jeq	d4,#1,.L11
.L531:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   189  			Dio_WriteChannel(SDA_B,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   190  			CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   191  			Dio_WriteChannel(SCL_B,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   192  			CDD_IIC_COM_Delay(500);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   193  			Dio_WriteChannel(SDA_B,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   194  			CDD_IIC_COM_Delay(200);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   195  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   196  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   197  		default:
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   198  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   199  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   200  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   201  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   202  }
	ret
.L10:
	mov	d15,#530
.L532:
	mov	d5,#0
	mov	d4,d15
	call	Dio_WriteChannel
.L337:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L533:
	mov	d4,#529
	j	.L13
.L11:
	mov	d15,#528
.L534:
	mov	d5,#0
	mov	d4,d15
	call	Dio_WriteChannel
.L338:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L535:
	mov	d4,#547
.L13:
	mov	d5,#1
	call	Dio_WriteChannel
.L536:
	mov	d4,#500
	call	CDD_IIC_COM_Delay
.L537:
	mov	d5,#1
	mov	d4,d15
	call	Dio_WriteChannel
.L538:
	mov	d4,#200
	j	CDD_IIC_COM_Delay
.L290:
	
__CDD_IIC_Stop_function_end:
	.size	CDD_IIC_Stop,__CDD_IIC_Stop_function_end-CDD_IIC_Stop
.L188:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.sendAck',code,cluster('sendAck')
	.sect	'.text.CDD_EyeQ_PowerCtrl.sendAck'
	.align	2
	
	.global	sendAck

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   203  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   204  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   205  /*----------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   206  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   207  ----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   208  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   209  void sendAck(uint8 channel)
; Function sendAck
.L110:
sendAck:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   210  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   211  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   212  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   213  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   214  	    case IIC_A :
	jeq	d4,#0,.L14
.L543:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   215  	    	Dio_WriteChannel(SCL_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   216  	    	CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   217  	    	Dio_WriteChannel(SDA_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   218  	    	CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   219  	    	Dio_WriteChannel(SCL_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   220  	    	CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   221  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   222  	    case IIC_B :
	jeq	d4,#1,.L15
.L544:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   223  	    	Dio_WriteChannel(SCL_B,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   224  	    	CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   225  	    	Dio_WriteChannel(SDA_B,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   226  	    	CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   227  	    	Dio_WriteChannel(SCL_B,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   228  	    	CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   229  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   230  	    default:
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   231  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   232  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   233  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   234  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   235  }
	ret
.L14:
	mov	d15,#529
.L545:
	mov	d5,#0
	mov	d4,d15
	call	Dio_WriteChannel
.L339:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L546:
	mov	d4,#530
	j	.L17
.L15:
	mov	d15,#547
.L547:
	mov	d5,#0
	mov	d4,d15
	call	Dio_WriteChannel
.L340:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L548:
	mov	d4,#528
.L17:
	mov	d5,#0
	call	Dio_WriteChannel
.L549:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L550:
	mov	d5,#1
	mov	d4,d15
	call	Dio_WriteChannel
.L551:
	mov	d4,#100
	j	CDD_IIC_COM_Delay
.L292:
	
__sendAck_function_end:
	.size	sendAck,__sendAck_function_end-sendAck
.L193:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.sendNoAck',code,cluster('sendNoAck')
	.sect	'.text.CDD_EyeQ_PowerCtrl.sendNoAck'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   236  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   237  /*----------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   238  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   239  ----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   240  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   241  static void sendNoAck(uint8 channel)
; Function sendNoAck
.L112:
sendNoAck:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   242  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   243  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   244  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   245  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   246  	    case IIC_A :
	jeq	d4,#0,.L18
.L556:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   247  	    	Dio_WriteChannel(SCL_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   248  	    	CDD_IIC_COM_Delay(200);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   249  	    	Dio_WriteChannel(SDA_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   250  	    	CDD_IIC_COM_Delay(600);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   251  	    	Dio_WriteChannel(SCL_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   252  	    	CDD_IIC_COM_Delay(600);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   253  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   254  	    case IIC_B :
	jeq	d4,#1,.L19
.L557:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   255  	    	Dio_WriteChannel(SCL_B,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   256  	    	CDD_IIC_COM_Delay(200);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   257  	    	Dio_WriteChannel(SDA_B,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   258  	    	CDD_IIC_COM_Delay(600);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   259  	    	Dio_WriteChannel(SCL_B,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   260  	    	CDD_IIC_COM_Delay(600);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   261  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   262  	    default:
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   263  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   264  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   265  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   266  }
	ret
.L18:
	mov	d15,#529
.L558:
	mov	d5,#0
	mov	d4,d15
	call	Dio_WriteChannel
.L341:
	mov	d4,#200
	call	CDD_IIC_COM_Delay
.L559:
	mov	d4,#530
	j	.L21
.L19:
	mov	d15,#547
.L560:
	mov	d5,#0
	mov	d4,d15
	call	Dio_WriteChannel
.L342:
	mov	d4,#200
	call	CDD_IIC_COM_Delay
.L561:
	mov	d4,#528
.L21:
	mov	d5,#1
	call	Dio_WriteChannel
.L562:
	mov	d8,#600
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L563:
	mov	d5,#1
	mov	d4,d15
	call	Dio_WriteChannel
.L564:
	mov	d4,d8
	j	CDD_IIC_COM_Delay
.L294:
	
__sendNoAck_function_end:
	.size	sendNoAck,__sendNoAck_function_end-sendNoAck
.L198:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.checkAck',code,cluster('checkAck')
	.sect	'.text.CDD_EyeQ_PowerCtrl.checkAck'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   267  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   268  /*----------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   269  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   270   0 = noACK; 1 = ACK ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   271  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   272  ----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   273  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   274  static uint8 checkAck(uint8 channel)
; Function checkAck
.L114:
checkAck:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   275  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   276  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   277  	uint8 tempbit=0;
	mov	d15,#0
.L344:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   278  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   279  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   280  	    case IIC_A :
	jeq	d4,#0,.L22
.L569:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   281  	    	CDD_IIC_COM_Delay(200);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   282  //	    	Port_SetPinDirection(ProtPinDirIn_ChipA,PortInput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   283  	    	CDD_IIC_COM_Delay(200);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   284  	    	Dio_WriteChannel(SCL_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   285  	    	CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   286  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   287  	    	tempbit = Dio_ReadChannel(SDA_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   288  	    	CDD_IIC_COM_Delay(500);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   289  	    	Dio_WriteChannel(SCL_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   290  	    	CDD_IIC_COM_Delay(160);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   291  //	    	Port_SetPinDirection(ProtPinDirIn_ChipA,PortOutput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   292  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   293  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   294  	    case IIC_B :
	jeq	d4,#1,.L23
.L570:
	j	.L24
.L22:
	mov	d4,#200
	call	CDD_IIC_COM_Delay
.L343:
	mov	d4,#200
	call	CDD_IIC_COM_Delay
.L571:
	mov	d8,#529
.L572:
	mov	d5,#1
	mov	d4,d8
	call	Dio_WriteChannel
.L573:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L574:
	mov	d4,#530
	j	.L25
.L23:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   295  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   296  	    	CDD_IIC_COM_Delay(200);
	mov	d4,#200
	call	CDD_IIC_COM_Delay
.L346:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   297  //	    	Port_SetPinDirection(ProtPinDirIn_ChipB,PortInput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   298  	    	CDD_IIC_COM_Delay(200);
	mov	d4,#200
	call	CDD_IIC_COM_Delay
.L575:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   299  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   300  	    	Dio_WriteChannel(SCL_B,STD_HIGH);
	mov	d8,#547
.L576:
	mov	d5,#1
	mov	d4,d8
	call	Dio_WriteChannel
.L577:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   301  	        CDD_IIC_COM_Delay(100);
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L578:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   302  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   303  	        tempbit = Dio_ReadChannel(SDA_B);
	mov	d4,#528

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   304  	        CDD_IIC_COM_Delay(500);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   305  	    	Dio_WriteChannel(SCL_B,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   306  	    	CDD_IIC_COM_Delay(160);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   307  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   308  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   309  //	        Port_SetPinDirection(ProtPinDirIn_ChipB,PortOutput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   310  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   311  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   312  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   313  	    default:
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   314  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   315  	    	break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   316  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   317  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   318  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   319  	if(tempbit==1)
.L25:
	call	Dio_ReadChannel
.L345:
	mov	d15,d2
.L348:
	mov	d4,#500
	call	CDD_IIC_COM_Delay
.L347:
	mov	d5,#0
	mov	d4,d8
	call	Dio_WriteChannel
.L579:
	mov	d4,#160
	call	CDD_IIC_COM_Delay
.L24:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   320  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   321  		return E_OK; /*noACK*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   322  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   323  	else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   324  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   325  		return E_NOT_OK; /*ACK*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   326  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   327  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   328  }
	ne	d2,d15,#1
	ret
.L296:
	
__checkAck_function_end:
	.size	checkAck,__checkAck_function_end-checkAck
.L203:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.writeByte',code,cluster('writeByte')
	.sect	'.text.CDD_EyeQ_PowerCtrl.writeByte'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   329  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   330  /*----------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   331  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   332  OK
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   333  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   334   a positive clock edge clock a bit into the ROM
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   335  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   336  ----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   337  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   338  static uint8 writeByte(uint8 channel,uint8 datum)
; Function writeByte
.L116:
writeByte:	.type	func
	mov	d15,d5
.L349:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   339  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   340  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   341  	uint8 ret=E_OK;
	mov	d8,#0
.L351:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   342  	uint8 bitCnt = 0 ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   343  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   344  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   345  		case IIC_A :
	jeq	d4,#0,.L27
.L584:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   346  			for(bitCnt=0; bitCnt<8; bitCnt++)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   347  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   348  				Dio_WriteChannel(SCL_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   349  				CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   350  				if ((datum&0x80) == 0x80)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   351  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   352  					Dio_WriteChannel(SDA_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   353  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   354  				else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   355  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   356  					Dio_WriteChannel(SDA_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   357  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   358  				CDD_IIC_COM_Delay(360);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   359  				Dio_WriteChannel(SCL_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   360  				CDD_IIC_COM_Delay(600);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   361  				datum<<=1 ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   362  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   363  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   364  			Dio_WriteChannel(SCL_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   365  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   366  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   367  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   368  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   369  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   370  		case IIC_B :
	jeq	d4,#1,.L28
.L585:
	j	.L29
.L27:
	mov	d9,#529
	mov.a	a15,#7
.L30:
	mov	d5,#0
	mov	d4,d9
	call	Dio_WriteChannel
.L586:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L350:
	fcall	.cocofun_2
.L352:
	mov	d4,#530
	call	Dio_WriteChannel
.L587:
	mov	d4,#360
	call	CDD_IIC_COM_Delay
.L588:
	mov	d5,#1
	mov	d4,d9
	call	Dio_WriteChannel
.L589:
	mov	d4,#600
	call	CDD_IIC_COM_Delay
.L590:
	sha	d15,#1
.L353:
	extr.u	d15,d15,#0,#8
	loop	a15,.L30
.L355:
	mov	d5,#0
	mov	d4,d9
	call	Dio_WriteChannel
.L591:
	j	.L33
.L28:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   371  			for(bitCnt=0; bitCnt<8; bitCnt++)
	mov	d9,#547
	mov.a	a15,#7
.L34:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   372  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   373  				Dio_WriteChannel(SCL_B,STD_LOW);
	mov	d5,#0
	mov	d4,d9
	call	Dio_WriteChannel
.L592:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   374  				CDD_IIC_COM_Delay(100);
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L354:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   375  				if ((datum&0x80) == 0x80)
	fcall	.cocofun_2
.L356:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   376  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   377  					Dio_WriteChannel(SDA_B,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   378  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   379  				else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   380  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   381  					Dio_WriteChannel(SDA_B,STD_LOW);
	mov	d4,#528
	call	Dio_WriteChannel
.L593:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   382  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   383  				CDD_IIC_COM_Delay(360);
	mov	d4,#360
	call	CDD_IIC_COM_Delay
.L594:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   384  				Dio_WriteChannel(SCL_B,STD_HIGH);
	mov	d5,#1
	mov	d4,d9
	call	Dio_WriteChannel
.L595:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   385  				CDD_IIC_COM_Delay(600);
	mov	d4,#600
	call	CDD_IIC_COM_Delay
.L596:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   386  				datum<<=1 ;
	sha	d15,#1
.L357:
	extr.u	d15,d15,#0,#8
	loop	a15,.L34
.L358:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   387  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   388  			Dio_WriteChannel(SCL_B,STD_LOW);
	mov	d5,#0
	mov	d4,d9
	call	Dio_WriteChannel
.L597:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   389  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   390  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   391  			break;
	j	.L37

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   392  		default:
.L29:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   393  			ret=E_NOT_OK;
	mov	d8,#1

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   394  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   395  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   396  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   397  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   398  	return ret;
.L37:
.L33:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   399  }
	mov	d2,d8
	ret
.L299:
	
__writeByte_function_end:
	.size	writeByte,__writeByte_function_end-writeByte
.L208:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl..cocofun_2',code,cluster('.cocofun_2')
	.sect	'.text.CDD_EyeQ_PowerCtrl..cocofun_2'
	.align	2
; Function .cocofun_2
.L118:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	jz.t	d15:7,.L31
.L714:
	mov	d5,#1
	j	.L32
.L31:
	mov	d5,#0
.L32:
	fret
.L243:
	; End of function
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.readByte',code,cluster('readByte')
	.sect	'.text.CDD_EyeQ_PowerCtrl.readByte'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   400  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   401  /*----------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   402  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   403  OK
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   404  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   405  ----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   406  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   407  static uint8 readByte(uint8 channel)
; Function readByte
.L120:
readByte:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   408  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   409  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   410  	uint8 tempbit = 1 ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   411  	uint8 temp = 0 ;
	mov	d15,#0
.L360:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   412  	uint8 bitCnt ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   413  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   414  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   415  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   416  		case IIC_A :
	jeq	d4,#0,.L39
.L602:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   417  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   418  			Dio_WriteChannel(SDA_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   419  			CDD_IIC_COM_Delay(450);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   420  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   421  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   422  //			Port_SetPinDirection(ProtPinDirIn_ChipA,PortInput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   423  			CDD_IIC_COM_Delay(450);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   424  			for(bitCnt=0; bitCnt<8; bitCnt++)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   425  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   426  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   427  				Dio_WriteChannel(SCL_A,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   428  				CDD_IIC_COM_Delay(450);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   429  				tempbit = Dio_ReadChannel(SDA_A) ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   430  				if (tempbit)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   431  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   432  					temp |= 0x01 ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   433  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   434  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   435  				else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   436  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   437  					temp &= 0xfe ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   438  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   439  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   440  				Dio_WriteChannel(SCL_A,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   441  				CDD_IIC_COM_Delay(450);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   442  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   443  				if(bitCnt<7)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   444  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   445  					temp <<= 1 ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   446  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   447  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   448  //			Port_SetPinDirection(ProtPinDirIn_ChipA,PortOutput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   449  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   450  		case IIC_B :
	jeq	d4,#1,.L40
.L603:
	j	.L41
.L39:
	mov	d5,#1
.L604:
	mov	d4,#530
	call	Dio_WriteChannel
.L359:
	mov	d8,#450
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L605:
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L606:
	mov	d9,#0
	mov.a	a15,#7
.L42:
	mov	d5,#1
.L607:
	mov	d4,#529
	call	Dio_WriteChannel
.L608:
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L362:
	mov	d4,#530
	call	Dio_ReadChannel
.L361:
	fcall	.cocofun_1
.L609:
	mov	d4,#529
	call	Dio_WriteChannel
.L363:
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L364:
	fcall	.cocofun_3
.L610:
	loop	a15,.L42
.L366:
	j	.L46
.L40:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   451  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   452  			Dio_WriteChannel(SDA_B,STD_HIGH);
	mov	d5,#1
.L611:
	mov	d4,#528
	call	Dio_WriteChannel
.L368:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   453  			CDD_IIC_COM_Delay(450);
	mov	d8,#450
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L612:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   454  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   455  //			Port_SetPinDirection(ProtPinDirIn_ChipB,PortInput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   456  			CDD_IIC_COM_Delay(450);
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L613:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   457  			for(bitCnt=0; bitCnt<8; bitCnt++)
	mov	d9,#0
	mov.a	a15,#7
.L47:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   458  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   459  				Dio_WriteChannel(SCL_B,STD_HIGH);
	mov	d5,#1
.L614:
	mov	d4,#547
	call	Dio_WriteChannel
.L615:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   460  				CDD_IIC_COM_Delay(450);
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L369:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   461  				tempbit = Dio_ReadChannel(SDA_B) ;
	mov	d4,#528
	call	Dio_ReadChannel
.L367:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   462  				if (tempbit)
	fcall	.cocofun_1
.L370:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   463  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   464  					temp |= 0x01 ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   465  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   466  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   467  				else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   468  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   469  					temp &= 0xfe ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   470  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   471  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   472  				Dio_WriteChannel(SCL_B,STD_LOW);
	mov	d4,#547
	call	Dio_WriteChannel
.L372:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   473  				CDD_IIC_COM_Delay(450);
	mov	d4,d8
	call	CDD_IIC_COM_Delay
.L371:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   474  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   475  				if(bitCnt<7)
	fcall	.cocofun_3
.L373:
	loop	a15,.L47

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   476  				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   477  					temp <<= 1 ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   478  				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   479  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   480  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   481  //			Port_SetPinDirection(ProtPinDirIn_ChipB,PortOutput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   482  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   483  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   484  		default:
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   485  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   486  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   487  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   488  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   489  	return(temp) ;
.L46:
.L41:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   490  }
	mov	d2,d15
	ret
.L305:
	
__readByte_function_end:
	.size	readByte,__readByte_function_end-readByte
.L213:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl..cocofun_3',code,cluster('.cocofun_3')
	.sect	'.text.CDD_EyeQ_PowerCtrl..cocofun_3'
	.align	2
; Function .cocofun_3
.L122:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:0
	jge.u	d9,#7,.L45
.L719:
	sha	d15,#1
.L365:
	extr.u	d15,d15,#0,#8
.L45:
	add	d9,#1
	fret
.L248:
	; End of function
	.sdecl	'.text.CDD_EyeQ_PowerCtrl..cocofun_1',code,cluster('.cocofun_1')
	.sect	'.text.CDD_EyeQ_PowerCtrl..cocofun_1'
	.align	2
; Function .cocofun_1
.L124:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	jeq	d2,#0,.L43
.L709:
	or	d15,#1
	j	.L44
.L43:
	and	d15,#254
.L44:
	mov	d5,#0
	fret
.L238:
	; End of function
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.writeOneByte',code,cluster('writeOneByte')
	.sect	'.text.CDD_EyeQ_PowerCtrl.writeOneByte'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   491  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   492  /*~~~~~~~~~~~~~~~~~~~~~~~ API ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   493  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   494  /*-----------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   495  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   496  wirte one byte to ROM --random write
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   497  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   498  -----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   499  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   500  static uint8 writeOneByte(uint8 channel,uint8 addr, uint8 datum)
; Function writeOneByte
.L126:
writeOneByte:	.type	func
	mov	d15,d5
.L375:
	mov	d8,d6
.L376:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   501  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   502  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   503  	uint8 ret=E_OK;
	mov	d9,#0
.L377:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   504  	uint8 tempbit ;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   505  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   506  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   507  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   508  		case IIC_A :
	jeq	d4,#0,.L52
.L620:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   509  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   510  			CDD_IIC_Start(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   511  			writeByte(IIC_A,0xC0);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   512  			tempbit = checkAck(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   513  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   514  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   515  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   516  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   517  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   518  			writeByte(IIC_A,addr);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   519  			tempbit = checkAck(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   520  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   521  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   522  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   523  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   524  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   525  			writeByte(IIC_A,datum);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   526  			tempbit = checkAck(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   527  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   528  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   529  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   530  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   531  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   532  			CDD_IIC_Stop(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   533  			CDD_IIC_COM_Delay(4000);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   534  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   535  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   536  		case IIC_B :
	jeq	d4,#1,.L53
.L621:
	j	.L54
.L52:
	mov	d4,#0
	call	CDD_IIC_Start
.L374:
	mov	d4,#0
.L622:
	mov	d5,#192
	call	writeByte
.L623:
	mov	d4,#0
	call	checkAck
.L378:
	jne	d2,#0,.L55

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   537  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   538  			CDD_IIC_Start(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   539  			writeByte(IIC_B,0xC8);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   540  			tempbit = checkAck(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   541  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   542  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   543  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   544  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   545  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   546  			writeByte(IIC_B,addr);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   547  			tempbit = checkAck(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   548  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   549  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   550  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   551  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   552  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   553  			writeByte(IIC_B,datum);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   554  			tempbit = checkAck(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   555  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   556  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   557  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   558  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   559  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   560  			CDD_IIC_Stop(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   561  			CDD_IIC_COM_Delay(4000);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   562  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   563  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   564  		default:
.L54:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   565  			ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   566  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   567  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   568  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   569  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   570  	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   571  }
.L56:
.L57:
.L58:
.L59:
.L60:
	mov	d9,#1
.L61:
.L62:
	mov	d2,d9
	ret
.L55:
	mov	d4,#0
.L624:
	mov	d5,d15
	call	writeByte
.L379:
	mov	d4,#0
	call	checkAck
.L380:
	jeq	d2,#0,.L60
.L625:
	mov	d4,#0
.L626:
	mov	d5,d8
	call	writeByte
.L381:
	mov	d4,#0
	call	checkAck
.L382:
	jeq	d2,#0,.L59
.L627:
	mov	d4,#0
	call	CDD_IIC_Stop
.L383:
	mov	d4,#4000
	call	CDD_IIC_COM_Delay
.L628:
	j	.L62
.L53:
	mov	d4,#1
	call	CDD_IIC_Start
.L384:
	mov	d4,#1
.L629:
	mov	d5,#200
	call	writeByte
.L630:
	mov	d4,#1
	call	checkAck
.L385:
	jeq	d2,#0,.L58
.L631:
	mov	d4,#1
.L632:
	mov	d5,d15
	call	writeByte
.L386:
	mov	d4,#1
	call	checkAck
.L387:
	jeq	d2,#0,.L57
.L633:
	mov	d4,#1
.L634:
	mov	d5,d8
	call	writeByte
.L388:
	mov	d4,#1
	call	checkAck
.L389:
	jeq	d2,#0,.L56
.L635:
	mov	d4,#1
	call	CDD_IIC_Stop
.L390:
	mov	d4,#4000
	call	CDD_IIC_COM_Delay
.L636:
	j	.L61
.L311:
	
__writeOneByte_function_end:
	.size	writeOneByte,__writeOneByte_function_end-writeOneByte
.L218:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.readOneByte',code,cluster('readOneByte')
	.sect	'.text.CDD_EyeQ_PowerCtrl.readOneByte'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   572  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   573  /*-----------------------------------------------------------------
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   574  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   575  read one byte from rom --random read
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   576  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   577  -----------------------------------------------------------------*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   578  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   579  static uint8 readOneByte(uint8 channel,uint8 addr,uint8*datum)
; Function readOneByte
.L128:
readOneByte:	.type	func
	mov	d15,d5
	mov.aa	a15,a4
.L392:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   580  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   581  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   582  	uint8  ret=E_OK;
	mov	d8,#0
.L393:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   583  	uint8 tempbit = 1;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   584  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   585  	switch(channel)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   586  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   587  		case IIC_A:
	jeq	d4,#0,.L64
.L641:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   588  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   589  			CDD_IIC_Start(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   590  			writeByte(IIC_A,0xC0);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   591  			tempbit = checkAck(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   592  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   593  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   594  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   595  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   596  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   597  			writeByte(IIC_A,addr);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   598  			tempbit = checkAck(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   599  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   600  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   601  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   602  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   603  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   604  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   605  			CDD_IIC_Start(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   606  			writeByte(IIC_A,0xC1);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   607  			tempbit = checkAck(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   608  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   609  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   610  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   611  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   612  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   613  			*datum = readByte(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   614  			sendNoAck(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   615  			CDD_IIC_Stop(IIC_A);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   616  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   617  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   618  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   619  		case IIC_B:
	jeq	d4,#1,.L65
.L642:
	j	.L66
.L64:
	mov	d4,#0
	call	CDD_IIC_Start
.L391:
	mov	d4,#0
.L643:
	mov	d5,#192
	call	writeByte
.L644:
	mov	d4,#0
	call	checkAck
.L394:
	jne	d2,#0,.L67

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   620  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   621  			CDD_IIC_Start(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   622  			writeByte(IIC_B,0xC8);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   623  			tempbit = checkAck(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   624  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   625  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   626  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   627  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   628  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   629  			writeByte(IIC_B,addr);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   630  			tempbit = checkAck(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   631  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   632  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   633  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   634  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   635  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   636  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   637  			CDD_IIC_Start(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   638  			writeByte(IIC_B,0xC9);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   639  			tempbit = checkAck(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   640  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   641  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   642  			if(tempbit==0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   643  			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   644  				ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   645  				return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   646  			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   647  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   648  			*datum = readByte(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   649  			sendNoAck(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   650  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   651  			CDD_IIC_Stop(IIC_B);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   652  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   653  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   654  		default:
.L66:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   655  			ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   656  			break;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   657  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   658  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   659  	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   660  }
.L68:
.L69:
.L70:
.L71:
.L72:
	mov	d8,#1
.L73:
.L74:
	mov	d2,d8
	ret
.L67:
	mov	d4,#0
.L645:
	mov	d5,d15
	call	writeByte
.L395:
	mov	d4,#0
	call	checkAck
.L396:
	jeq	d2,#0,.L72
.L646:
	mov	d4,#0
	call	CDD_IIC_Start
.L397:
	mov	d4,#0
.L647:
	mov	d5,#193
	call	writeByte
.L648:
	mov	d4,#0
	call	checkAck
.L398:
	jeq	d2,#0,.L71
.L649:
	mov	d4,#0
	call	readByte
.L399:
	st.b	[a15],d2
.L650:
	mov	d4,#0
	call	sendNoAck
.L651:
	mov	d4,#0
	call	CDD_IIC_Stop
.L652:
	j	.L74
.L65:
	mov	d4,#1
	call	CDD_IIC_Start
.L400:
	mov	d4,#1
.L653:
	mov	d5,#200
	call	writeByte
.L654:
	mov	d4,#1
	call	checkAck
.L401:
	jeq	d2,#0,.L70
.L655:
	mov	d4,#1
.L656:
	mov	d5,d15
	call	writeByte
.L402:
	mov	d4,#1
	call	checkAck
.L403:
	jeq	d2,#0,.L69
.L657:
	mov	d4,#1
	call	CDD_IIC_Start
.L404:
	mov	d4,#1
.L658:
	mov	d5,#201
	call	writeByte
.L659:
	mov	d4,#1
	call	checkAck
.L405:
	jeq	d2,#0,.L68
.L660:
	mov	d4,#1
	call	readByte
.L406:
	st.b	[a15],d2
.L661:
	mov	d4,#1
	call	sendNoAck
.L662:
	mov	d4,#1
	call	CDD_IIC_Stop
.L663:
	j	.L73
.L317:
	
__readOneByte_function_end:
	.size	readOneByte,__readOneByte_function_end-readOneByte
.L223:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrRegSet',code,cluster('SM_EyeQPwrRegSet')
	.sect	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrRegSet'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   661  static uint8 SM_EyeQPwrRegSet(void)
; Function SM_EyeQPwrRegSet
.L130:
SM_EyeQPwrRegSet:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   662  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   663  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   664  	uint8 IIC_Index=0;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   665  	uint8 ret=E_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   666  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   667  	/*   LP875701    */
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   668  	for(IIC_Index=0;IIC_Index < IIC_A_MaxIndex;IIC_Index++)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   669  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   670  		ret=writeOneByte(IIC_A,IIC_A_RegAdd[IIC_Index],IIC_A_RegVal[IIC_Index]);
	movh.a	a15,#@his(IIC_A_RegVal)
	lea	a15,[a15]@los(IIC_A_RegVal)
.L668:
	movh.a	a12,#@his(IIC_A_RegAdd)
	lea	a12,[a12]@los(IIC_A_RegAdd)
.L669:
	mov.a	a13,#14
.L76:
	mov	d4,#0
	ld.bu	d5,[a12]
.L670:
	ld.bu	d6,[a15]
	call	writeOneByte
.L407:
	mov	d15,d2
.L409:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   671  		if(1==ret)
	jne	d15,#0,.L77
.L671:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   672  		{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   673  			return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   674  		}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   675  		else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   676  		{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   677  		CDD_IIC_COM_Delay(100);
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L408:
	add.a	a15,#1
.L672:
	add.a	a12,#1
	loop	a13,.L76
.L673:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   678  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   679  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   680  	/*    LP87563    */
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   681  	for(IIC_Index=0;IIC_Index < IIC_B_MaxIndex;IIC_Index++)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   682  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   683  		ret=writeOneByte(IIC_B,IIC_B_RegAdd[IIC_Index],IIC_B_RegVal[IIC_Index]);
	movh.a	a15,#@his(IIC_B_RegVal)
	lea	a15,[a15]@los(IIC_B_RegVal)
.L674:
	fcall	.cocofun_4
.L675:
	lea	a13,32
.L78:
	mov	d4,#1
	ld.bu	d5,[a12]
.L676:
	ld.bu	d6,[a15]
	call	writeOneByte
.L410:
	mov	d15,d2
.L412:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   684  		if(1==ret)
	jne	d15,#0,.L79
.L677:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   685  		{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   686  			return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   687  		}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   688  		else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   689  		{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   690  		CDD_IIC_COM_Delay(100);
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L411:
	add.a	a15,#1
.L678:
	add.a	a12,#1
	loop	a13,.L78

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   691  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   692  	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   693  }
.L79:
.L77:
	mov	d2,d15
	ret
.L324:
	
__SM_EyeQPwrRegSet_function_end:
	.size	SM_EyeQPwrRegSet,__SM_EyeQPwrRegSet_function_end-SM_EyeQPwrRegSet
.L228:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl..cocofun_4',code,cluster('.cocofun_4')
	.sect	'.text.CDD_EyeQ_PowerCtrl..cocofun_4'
	.align	2
; Function .cocofun_4
.L132:
.cocofun_4:	.type	func
; Function body .cocofun_4, coco_iter:0
	movh.a	a12,#@his(IIC_B_RegAdd)
	lea	a12,[a12]@los(IIC_B_RegAdd)
.L724:
	fret
.L253:
	; End of function
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrRegRead',code,cluster('SM_EyeQPwrRegRead')
	.sect	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrRegRead'
	.align	2
	

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   694  static uint8 SM_EyeQPwrRegRead(void)
; Function SM_EyeQPwrRegRead
.L134:
SM_EyeQPwrRegRead:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   695  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   696  	uint8 IIC_Index=0;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   697  	uint8 ret=E_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   698  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   699  	/*   LP875701    */
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   700  	for(IIC_Index=0;IIC_Index<IIC_A_MaxIndex;IIC_Index++)
	mov	d8,#0
	j	.L81
.L82:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   701  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   702  		ret=readOneByte(IIC_A,IIC_A_RegAdd[IIC_Index],&IIC_A_Register[IIC_Index]);
	movh.a	a15,#@his(IIC_A_Register)
.L683:
	movh.a	a2,#@his(IIC_A_RegAdd)
.L684:
	lea	a15,[a15]@los(IIC_A_Register)
.L685:
	addsc.a	a15,a15,d8,#0
.L686:
	lea	a2,[a2]@los(IIC_A_RegAdd)
.L687:
	addsc.a	a2,a2,d8,#0
.L688:
	mov	d4,#0
	mov.aa	a4,a15
.L689:
	ld.bu	d5,[a2]
.L690:
	call	readOneByte
.L413:
	mov	d15,d2
.L414:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   703  		if(1==ret)
	jne	d15,#0,.L83
.L691:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   704  		{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   705  			return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   706  		}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   707  		else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   708  		{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   709  		if(IIC_A_Register[IIC_Index]==IIC_A_RegVal[IIC_Index])
	ld.bu	d0,[a15]
.L692:
	movh.a	a15,#@his(IIC_A_RegVal)
	lea	a15,[a15]@los(IIC_A_RegVal)
.L693:
	addsc.a	a15,a15,d8,#0
	ld.bu	d15,[a15]
.L415:
	jeq	d15,d0,.L84

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   710  		{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   711  		else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   712  		{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   713  			ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   714  			return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   715  		}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   716  		CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   717  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   718  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   719  	/*    LP87563    */
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   720  	for(IIC_Index=0;IIC_Index<IIC_B_MaxIndex;IIC_Index++)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   721  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   722  		ret=readOneByte(IIC_B,IIC_B_RegAdd[IIC_Index],&IIC_B_Register[IIC_Index]);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   723  		if(1==ret)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   724  		{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   725  			return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   726  		}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   727  		else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   728  		{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   729  		if(IIC_B_Register[IIC_Index]==IIC_B_RegVal[IIC_Index])
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   730  		{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   731  		else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   732  		{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   733  			ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   734  			return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   735  		}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   736  		CDD_IIC_COM_Delay(100);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   737  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   738  	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   739  }
.L85:
	mov	d15,#1
.L83:
.L86:
.L87:
	mov	d2,d15
	ret
.L84:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L416:
	add	d8,#1
.L81:
	jlt.u	d8,#15,.L82
.L694:
	movh.a	a15,#@his(IIC_B_Register)
	lea	a15,[a15]@los(IIC_B_Register)
.L695:
	fcall	.cocofun_4
.L696:
	movh.a	a13,#@his(IIC_B_RegVal)
	lea	a13,[a13]@los(IIC_B_RegVal)
.L697:
	lea	a14,32
.L89:
	mov	d4,#1
	ld.bu	d5,[a12]
.L698:
	mov.aa	a4,a15
	call	readOneByte
.L417:
	mov	d15,d2
.L419:
	jne	d15,#0,.L87
.L699:
	ld.bu	d0,[a15]
.L700:
	ld.bu	d1,[a13]
.L701:
	jne	d0,d1,.L85
.L702:
	mov	d4,#100
	call	CDD_IIC_COM_Delay
.L418:
	add.a	a15,#1
	add.a	a12,#1
.L703:
	add.a	a13,#1
	loop	a14,.L89
.L704:
	j	.L86
.L328:
	
__SM_EyeQPwrRegRead_function_end:
	.size	SM_EyeQPwrRegRead,__SM_EyeQPwrRegRead_function_end-SM_EyeQPwrRegRead
.L233:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.SM_PwrInit',code,cluster('SM_PwrInit')
	.sect	'.text.CDD_EyeQ_PowerCtrl.SM_PwrInit'
	.align	2
	
	.global	SM_PwrInit

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   740  uint8 SM_PwrInit(void)
; Function SM_PwrInit
.L136:
SM_PwrInit:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   741  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   742  	uint8 u8t_ret=E_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   743  	return u8t_ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   744  }
	mov	d2,#0
	ret
.L279:
	
__SM_PwrInit_function_end:
	.size	SM_PwrInit,__SM_PwrInit_function_end-SM_PwrInit
.L158:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrMainFunc',code,cluster('SM_EyeQPwrMainFunc')
	.sect	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrMainFunc'
	.align	2
	
	.global	SM_EyeQPwrMainFunc

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   745  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   746  uint8 SM_EyeQPwrMainFunc(void)
; Function SM_EyeQPwrMainFunc
.L138:
SM_EyeQPwrMainFunc:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   747  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   748  	uint8 ret=E_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   749  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   750  	ret= SM_EyeQPwrRegSet();
	call	SM_EyeQPwrRegSet
.L420:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   751  	if(1==ret)
	jne	d2,#0,.L91
.L453:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   752  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   753  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   754  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   755  	else{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   756  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   757  	ret=SM_EyeQPwrRegRead();
	j	SM_EyeQPwrRegRead

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   758  	if(1==ret)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   759  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   760  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   761  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   762  	else{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   763  	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   764  }
.L91:
	ret
.L280:
	
__SM_EyeQPwrMainFunc_function_end:
	.size	SM_EyeQPwrMainFunc,__SM_EyeQPwrMainFunc_function_end-SM_EyeQPwrMainFunc
.L163:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrDiagMainFunc',code,cluster('SM_EyeQPwrDiagMainFunc')
	.sect	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrDiagMainFunc'
	.align	2
	
	.global	SM_EyeQPwrDiagMainFunc

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   765  uint8 SM_EyeQPwrDiagMainFunc(void)
; Function SM_EyeQPwrDiagMainFunc
.L140:
SM_EyeQPwrDiagMainFunc:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   766  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   767  	uint8 ret=E_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   768  	ret=readOneByte(IIC_A,IIC_A_RegAdd[0],&IIC_A_diag[0]);
	movh.a	a12,#@his(IIC_A_diag)
	lea	a12,[a12]@los(IIC_A_diag)
.L458:
	movh.a	a15,#@his(IIC_A_RegAdd)
	lea	a15,[a15]@los(IIC_A_RegAdd)
.L459:
	mov	d4,#0
	ld.bu	d5,[a15]
.L460:
	mov.aa	a4,a12
	call	readOneByte
.L421:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   769  	if(1==ret)
	jne	d2,#0,.L93
.L461:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   770  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   771  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   772  	}else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   773  	{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   774  	ret=readOneByte(IIC_A,IIC_A_RegAdd[1],&IIC_A_diag[1]);
	mov	d4,#0
	ld.bu	d5,[a15]1
.L462:
	lea	a4,[a12]1
.L463:
	call	readOneByte
.L464:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   775  	if(1==ret)
	jne	d2,#0,.L94
.L465:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   776  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   777  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   778  	}else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   779  	{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   780  	ret=readOneByte(IIC_A,IIC_A_RegAdd[2],&IIC_A_diag[2]);
	mov	d4,#0
	ld.bu	d5,[a15]2
.L466:
	add.a	a12,#2
	mov.aa	a4,a12
	call	readOneByte
.L467:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   781  	if(1==ret)
	jne	d2,#0,.L95
.L468:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   782  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   783  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   784  	}else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   785  	{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   786  	ret=readOneByte(IIC_B,IIC_B_RegAdd[0],&IIC_B_diag[0]);
	movh.a	a15,#@his(IIC_B_diag)
	lea	a15,[a15]@los(IIC_B_diag)
.L469:
	fcall	.cocofun_4
.L470:
	mov	d4,#1
	ld.bu	d5,[a12]
.L471:
	mov.aa	a4,a15
	call	readOneByte
.L472:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   787  	if(1==ret)
	jne	d2,#0,.L96
.L473:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   788  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   789  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   790  	}else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   791  	{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   792  	ret=readOneByte(IIC_B,IIC_B_RegAdd[0],&IIC_B_diag[1]);
	mov	d4,#1
	ld.bu	d5,[a12]
.L474:
	lea	a4,[a15]1
.L475:
	call	readOneByte
.L476:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   793  	if(1==ret)
	jne	d2,#0,.L97
.L477:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   794  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   795  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   796  	}else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   797  	{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   798  	ret=readOneByte(IIC_B,IIC_B_RegAdd[0],&IIC_B_diag[2]);
	mov	d4,#1
	ld.bu	d5,[a12]
.L478:
	add.a	a15,#2
	mov.aa	a4,a15
	j	readOneByte

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   799  	if(1==ret)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   800  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   801  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   802  	}else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   803  	{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   804  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   805  	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   806  }
.L97:
.L96:
.L95:
.L94:
.L93:
	ret
.L282:
	
__SM_EyeQPwrDiagMainFunc_function_end:
	.size	SM_EyeQPwrDiagMainFunc,__SM_EyeQPwrDiagMainFunc_function_end-SM_EyeQPwrDiagMainFunc
.L168:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrUpMainFunc',code,cluster('SM_EyeQPwrUpMainFunc')
	.sect	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrUpMainFunc'
	.align	2
	
	.global	SM_EyeQPwrUpMainFunc

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   807  uint8 SM_EyeQPwrUpMainFunc(void)
; Function SM_EyeQPwrUpMainFunc
.L142:
SM_EyeQPwrUpMainFunc:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   808  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   809  	uint8 ret=E_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   810  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   811  	Dio_WriteChannel(EyeQ_POR,STD_LOW);                      /*POR*/
	mov	d4,#161
.L483:
	mov	d5,#0
	call	Dio_WriteChannel
.L484:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   812  	Dio_WriteChannel(Res_ChipA,STD_HIGH);                    
	mov	d4,#534
.L485:
	mov	d5,#1
	call	Dio_WriteChannel
.L486:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   813  	Dio_WriteChannel(Res_ChipB,STD_HIGH);                    
	mov	d4,#535
.L487:
	mov	d5,#1
	call	Dio_WriteChannel
.L488:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   814  	CDD_IIC_COM_Delay(120000);                               
	mov	d4,#1875
	sh	d4,#6
	call	CDD_IIC_COM_Delay
.L489:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   815  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   816  	ret=SM_EyeQPwrMainFunc();
	call	SM_EyeQPwrMainFunc
.L422:
	mov	d15,d2
.L423:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   817  	if(1==ret)
	jne	d15,#0,.L99
.L490:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   818  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   819  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   820  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   821  	else{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   822  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   823  	CDD_IIC_COM_Delay(50000);
	mov.u	d4,#50000
	call	CDD_IIC_COM_Delay
.L491:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   824  	Dio_WriteChannel(En_ChipB,STD_HIGH);	           
	mov	d4,#240
.L492:
	mov	d5,#1
	call	Dio_WriteChannel
.L493:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   825  	Dio_WriteChannel(En_ChipA,STD_HIGH);                
	mov	d4,#531
.L494:
	mov	d5,#1
	call	Dio_WriteChannel
.L495:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   826  	Dio_WriteChannel(EnCamPower,STD_HIGH);                  
	mov	d4,#243
.L496:
	mov	d5,#1
	call	Dio_WriteChannel
.L497:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   827  	CDD_IIC_COM_Delay(700000);                            
	mov	d4,#21875
	sh	d4,#5
	call	CDD_IIC_COM_Delay
.L498:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   828  	Dio_WriteChannel(EyeQ_POR,STD_LOW);                       /*POR*/
	mov	d4,#161
.L499:
	mov	d5,#0
	call	Dio_WriteChannel
.L500:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   829  	Dio_WriteChannel(EyeQ_PRB,STD_HIGH);                      /*PRB*/
	mov	d4,#162
.L501:
	mov	d5,#1
	call	Dio_WriteChannel
.L502:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   830  	Dio_WriteChannel(EnMicron_Spi,STD_HIGH);        
	mov	d4,#178
.L503:
	mov	d5,#1
	call	Dio_WriteChannel

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   831  	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   832  }
.L99:
	mov	d2,d15
	ret
.L284:
	
__SM_EyeQPwrUpMainFunc_function_end:
	.size	SM_EyeQPwrUpMainFunc,__SM_EyeQPwrUpMainFunc_function_end-SM_EyeQPwrUpMainFunc
.L173:
	; End of function
	
	.sdecl	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrDownMainFunc',code,cluster('SM_EyeQPwrDownMainFunc')
	.sect	'.text.CDD_EyeQ_PowerCtrl.SM_EyeQPwrDownMainFunc'
	.align	2
	
	.global	SM_EyeQPwrDownMainFunc

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   833  //uint8 SM_EyeQPwrOnEnMainFunc(void)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   834  //{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   835  //	uint8 ret=E_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   836  //	return ret;///////////////////////////////////////////////
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   837  //	if( ValSigValid == EyeQ_GetValSigStatus() )
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   838  //	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   839  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   840  //		Port_SetPinDirection(MicronClock_Id,PortInput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   841  //		Port_SetPinDirection(MicronCs_Id,PortInput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   842  //		Port_SetPinDirection(MicronMosi_Id,PortInput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   843  //		Dio_WriteChannel(EnMicron_Spi,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   844  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   845  //		CDD_IIC_COM_Delay(1000000);                       //delay 10ms
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   846  //		Dio_WriteChannel(EyeQ_POR,STD_HIGH);              /*POR*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   847  //		Dio_WriteChannel(EyeQ_PRB,STD_LOW);               /*PRB*/
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   848  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   849  //		EyeQPower_Flag=1;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   850  //	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   851  //	else
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   852  //	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   853  //		ret=E_NOT_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   854  //	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   855  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   856  //	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   857  //}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   858  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   859  //void SM_EthEnable(void)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   860  //{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   861  //	if(1==EyeQPower_Flag)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   862  //	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   863  //		Port_SetPinDirection(EthPinDirIn,PortOutput_Mode);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   864  //		if(SM_EthH_Flag>0)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   865  //		{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   866  //			SM_EthH_Flag++;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   867  //			if(SM_EthH_Flag>28)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   868  //			{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   869  //				Dio_WriteChannel(Eth_Res,STD_LOW);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   870  //				if(SM_EthH_Flag>38)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   871  //				{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   872  //					Dio_WriteChannel(Eth_Res,STD_HIGH);
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   873  //					SM_EthH_Flag=0;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   874  //				}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   875  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   876  //			}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   877  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   878  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   879  //
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   880  //		}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   881  //	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   882  //}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   883  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   884  uint8 SM_EyeQPwrDownMainFunc(void)
; Function SM_EyeQPwrDownMainFunc
.L144:
SM_EyeQPwrDownMainFunc:	.type	func

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   885  {
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   886  	uint8 ret=E_OK;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   887  	Dio_WriteChannel(En_ChipB,STD_LOW);	           /*Disable power B*/
	mov	d4,#240
.L508:
	mov	d5,#0
	call	Dio_WriteChannel
.L509:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   888  	Dio_WriteChannel(En_ChipA,STD_LOW);            /*Disable power A*/
	mov	d4,#531
.L510:
	mov	d5,#0
	call	Dio_WriteChannel
.L511:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   889  	ret = Dio_ReadChannel(En_ChipB);
	mov	d4,#240
	call	Dio_ReadChannel
.L424:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   890  	if(1==ret)
	jeq	d2,#1,.L101
.L512:

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   891  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   892  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   893  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   894  	else{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   895  	ret = Dio_ReadChannel(En_ChipB);
	mov	d4,#240
	j	Dio_ReadChannel

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   896  	if(1==ret)
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   897  	{
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   898  		return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   899  	}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   900  	else{;}
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   901  	return ret;
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   902  }
.L101:
	ret
.L286:
	
__SM_EyeQPwrDownMainFunc_function_end:
	.size	SM_EyeQPwrDownMainFunc,__SM_EyeQPwrDownMainFunc_function_end-SM_EyeQPwrDownMainFunc
.L178:
	; End of function
	
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.SM_EthH_Flag',data,cluster('SM_EthH_Flag')
	.sect	'.data.CDD_EyeQ_PowerCtrl.SM_EthH_Flag'
	.global	SM_EthH_Flag
SM_EthH_Flag:	.type	object
	.size	SM_EthH_Flag,1
	.space	1
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.EyeQPower_Flag',data,cluster('EyeQPower_Flag')
	.sect	'.data.CDD_EyeQ_PowerCtrl.EyeQPower_Flag'
	.global	EyeQPower_Flag
EyeQPower_Flag:	.type	object
	.size	EyeQPower_Flag,1
	.space	1
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.IIC_A_Register',data,cluster('IIC_A_Register')
	.sect	'.data.CDD_EyeQ_PowerCtrl.IIC_A_Register'
	.align	4
IIC_A_Register:	.type	object
	.size	IIC_A_Register,15
	.space	15
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.IIC_B_Register',data,cluster('IIC_B_Register')
	.sect	'.data.CDD_EyeQ_PowerCtrl.IIC_B_Register'
	.align	4
IIC_B_Register:	.type	object
	.size	IIC_B_Register,33
	.space	33
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.IIC_A_RegVal',data,cluster('IIC_A_RegVal')
	.sect	'.data.CDD_EyeQ_PowerCtrl.IIC_A_RegVal'
	.global	IIC_A_RegVal
	.align	4
IIC_A_RegVal:	.type	object
	.size	IIC_A_RegVal,15
	.byte	199,133
	.space	2
	.byte	94,145,1,85
	.byte	85,5
	.space	1
	.byte	2,1,214,113
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.IIC_B_RegVal',data,cluster('IIC_B_RegVal')
	.sect	'.data.CDD_EyeQ_PowerCtrl.IIC_B_RegVal'
	.global	IIC_B_RegVal
	.align	4
IIC_B_RegVal:	.type	object
	.size	IIC_B_RegVal,33
	.byte	199,60,200,60,198,60,198,60
	.byte	97,97,97,97,177,177,252,252
	.byte	27,27,88,177
	.space	2
	.byte	94,149
	.space	1
	.byte	85,85,5
	.space	1
	.byte	6,1,214,113
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.IIC_A_RegAdd',data,cluster('IIC_A_RegAdd')
	.sect	'.data.CDD_EyeQ_PowerCtrl.IIC_A_RegAdd'
	.global	IIC_A_RegAdd
	.align	4
IIC_A_RegAdd:	.type	object
	.size	IIC_A_RegAdd,15
	.byte	2,18,22,23,25,33,34,35
	.byte	36,37,40,41
	.byte	43,44,45
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.IIC_B_RegAdd',data,cluster('IIC_B_RegAdd')
	.sect	'.data.CDD_EyeQ_PowerCtrl.IIC_B_RegAdd'
	.global	IIC_B_RegAdd
	.align	4
IIC_B_RegAdd:	.type	object
	.size	IIC_B_RegAdd,33
	.byte	2,3,4,5,6,7,8,9
	.byte	10,11,12,13,14,15,16,17
	.byte	18,19,20,21,22,23,25,33
	.byte	34,35,36,37,40,41,43,44
	.byte	45
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.IIC_A_diag',data,cluster('IIC_A_diag')
	.sect	'.data.CDD_EyeQ_PowerCtrl.IIC_A_diag'
	.global	IIC_A_diag
IIC_A_diag:	.type	object
	.size	IIC_A_diag,3
	.space	3
	.sdecl	'.data.CDD_EyeQ_PowerCtrl.IIC_B_diag',data,cluster('IIC_B_diag')
	.sect	'.data.CDD_EyeQ_PowerCtrl.IIC_B_diag'
	.global	IIC_B_diag
IIC_B_diag:	.type	object
	.size	IIC_B_diag,3
	.space	3
	.calls	'CDD_IIC_Start','Dio_WriteChannel'
	.calls	'CDD_IIC_Start','CDD_IIC_COM_Delay'
	.calls	'CDD_IIC_Stop','Dio_WriteChannel'
	.calls	'CDD_IIC_Stop','CDD_IIC_COM_Delay'
	.calls	'sendAck','Dio_WriteChannel'
	.calls	'sendAck','CDD_IIC_COM_Delay'
	.calls	'sendNoAck','Dio_WriteChannel'
	.calls	'sendNoAck','CDD_IIC_COM_Delay'
	.calls	'checkAck','CDD_IIC_COM_Delay'
	.calls	'checkAck','Dio_WriteChannel'
	.calls	'checkAck','Dio_ReadChannel'
	.calls	'writeByte','Dio_WriteChannel'
	.calls	'writeByte','CDD_IIC_COM_Delay'
	.calls	'readByte','Dio_WriteChannel'
	.calls	'readByte','CDD_IIC_COM_Delay'
	.calls	'readByte','Dio_ReadChannel'
	.calls	'writeOneByte','CDD_IIC_Start'
	.calls	'writeOneByte','writeByte'
	.calls	'writeOneByte','checkAck'
	.calls	'writeOneByte','CDD_IIC_Stop'
	.calls	'writeOneByte','CDD_IIC_COM_Delay'
	.calls	'readOneByte','CDD_IIC_Start'
	.calls	'readOneByte','writeByte'
	.calls	'readOneByte','checkAck'
	.calls	'readOneByte','readByte'
	.calls	'readOneByte','sendNoAck'
	.calls	'readOneByte','CDD_IIC_Stop'
	.calls	'SM_EyeQPwrRegSet','writeOneByte'
	.calls	'SM_EyeQPwrRegSet','CDD_IIC_COM_Delay'
	.calls	'SM_EyeQPwrRegRead','readOneByte'
	.calls	'SM_EyeQPwrRegRead','CDD_IIC_COM_Delay'
	.calls	'SM_EyeQPwrMainFunc','SM_EyeQPwrRegSet'
	.calls	'SM_EyeQPwrMainFunc','SM_EyeQPwrRegRead'
	.calls	'SM_EyeQPwrDiagMainFunc','readOneByte'
	.calls	'SM_EyeQPwrUpMainFunc','Dio_WriteChannel'
	.calls	'SM_EyeQPwrUpMainFunc','CDD_IIC_COM_Delay'
	.calls	'SM_EyeQPwrUpMainFunc','SM_EyeQPwrMainFunc'
	.calls	'SM_EyeQPwrDownMainFunc','Dio_WriteChannel'
	.calls	'SM_EyeQPwrDownMainFunc','Dio_ReadChannel'
	.calls	'writeByte','.cocofun_2'
	.calls	'readByte','.cocofun_1'
	.calls	'readByte','.cocofun_3'
	.calls	'SM_EyeQPwrRegSet','.cocofun_4'
	.calls	'SM_EyeQPwrRegRead','.cocofun_4'
	.calls	'SM_EyeQPwrDiagMainFunc','.cocofun_4'
	.calls	'CDD_IIC_COM_Delay','',0
	.calls	'CDD_IIC_Start','',0
	.calls	'CDD_IIC_Stop','',0
	.calls	'sendAck','',0
	.calls	'sendNoAck','',0
	.calls	'checkAck','',0
	.calls	'writeByte','',0
	.calls	'.cocofun_2','',0
	.calls	'readByte','',0
	.calls	'.cocofun_3','',0
	.calls	'.cocofun_1','',0
	.calls	'writeOneByte','',0
	.calls	'readOneByte','',0
	.calls	'SM_EyeQPwrRegSet','',0
	.calls	'.cocofun_4','',0
	.calls	'SM_EyeQPwrRegRead','',0
	.calls	'SM_PwrInit','',0
	.calls	'SM_EyeQPwrMainFunc','',0
	.calls	'SM_EyeQPwrDiagMainFunc','',0
	.calls	'SM_EyeQPwrUpMainFunc','',0
	.extern	Dio_ReadChannel
	.extern	Dio_WriteChannel
	.calls	'SM_EyeQPwrDownMainFunc','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L146:
	.word	51305
	.half	3
	.word	.L147
	.byte	4
.L145:
	.byte	1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L148
.L278:
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'Dio_ReadChannel',0,1,188,3,22
	.word	187
	.byte	1,1,1,1,2
	.byte	'unsigned short int',0,2,7,4
	.byte	'ChannelId',0,1,190,3,19
	.word	233
	.byte	0,5
	.byte	'Dio_WriteChannel',0,1,232,3,13,1,1,1,1,4
	.byte	'ChannelId',0,1,234,3,19
	.word	233
	.byte	4
	.byte	'Level',0,1,235,3,17
	.word	187
	.byte	0
.L275:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L320:
	.byte	6
	.word	187
	.byte	7
	.byte	'void',0,6
	.word	362
	.byte	8
	.byte	'__prof_adm',0,2,1,1
	.word	368
	.byte	9,1,6
	.word	392
	.byte	8
	.byte	'__codeptr',0,2,1,1
	.word	394
	.byte	8
	.byte	'uint8',0,3,90,29
	.word	187
	.byte	8
	.byte	'uint16',0,3,92,29
	.word	233
	.byte	8
	.byte	'uint32',0,3,94,29
	.word	336
	.byte	8
	.byte	'boolean',0,3,105,29
	.word	187
	.byte	2
	.byte	'unsigned int',0,4,7,8
	.byte	'unsigned_int',0,4,121,22
	.word	477
	.byte	10
	.byte	'Port_n_PinType',0,5,176,2,15,4,11,5,178,2,3,2,12
	.byte	'P0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'P2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'P3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'P4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'P5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'P6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'P7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'P8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'P9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'P10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'P11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'P12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'P13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'P14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'P15',0,1
	.word	187
	.byte	1,0,2,35,1,0,13
	.byte	'B',0,2
	.word	535
	.byte	2,35,0,13
	.byte	'U',0,4
	.word	336
	.byte	2,35,0,0,8
	.byte	'Port_n_PinType',0,5,203,2,3
	.word	514
	.byte	10
	.byte	'Port_n_ControlType',0,5,206,2,15,16,11,5,208,2,3,16,13
	.byte	'PC0',0,1
	.word	187
	.byte	2,35,0,13
	.byte	'PC1',0,1
	.word	187
	.byte	2,35,1,13
	.byte	'PC2',0,1
	.word	187
	.byte	2,35,2,13
	.byte	'PC3',0,1
	.word	187
	.byte	2,35,3,13
	.byte	'PC4',0,1
	.word	187
	.byte	2,35,4,13
	.byte	'PC5',0,1
	.word	187
	.byte	2,35,5,13
	.byte	'PC6',0,1
	.word	187
	.byte	2,35,6,13
	.byte	'PC7',0,1
	.word	187
	.byte	2,35,7,13
	.byte	'PC8',0,1
	.word	187
	.byte	2,35,8,13
	.byte	'PC9',0,1
	.word	187
	.byte	2,35,9,13
	.byte	'PC10',0,1
	.word	187
	.byte	2,35,10,13
	.byte	'PC11',0,1
	.word	187
	.byte	2,35,11,13
	.byte	'PC12',0,1
	.word	187
	.byte	2,35,12,13
	.byte	'PC13',0,1
	.word	187
	.byte	2,35,13,13
	.byte	'PC14',0,1
	.word	187
	.byte	2,35,14,13
	.byte	'PC15',0,1
	.word	187
	.byte	2,35,15,0,13
	.byte	'B',0,16
	.word	844
	.byte	2,35,0,14,16
	.word	336
	.byte	15,3,0,13
	.byte	'U',0,16
	.word	1076
	.byte	2,35,0,0,8
	.byte	'Port_n_ControlType',0,5,233,2,2
	.word	819
	.byte	16
	.byte	'Port_n_ConfigType',0,5,140,3,16,28,13
	.byte	'PinControl',0,16
	.word	819
	.byte	2,35,0,13
	.byte	'PinLevel',0,4
	.word	514
	.byte	2,35,16,13
	.byte	'DriverStrength0',0,4
	.word	336
	.byte	2,35,20,13
	.byte	'DriverStrength1',0,4
	.word	336
	.byte	2,35,24,0,8
	.byte	'Port_n_ConfigType',0,5,164,3,2
	.word	1125
	.byte	8
	.byte	'Port_n_PCSRConfigType',0,5,167,3,16
	.word	336
	.byte	16
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,12
	.byte	'EN0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	187
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	187
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	187
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	187
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	187
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	187
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	187
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	187
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	187
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	187
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	187
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	187
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	187
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	187
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	187
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	187
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	1296
	.byte	16
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,12
	.byte	'reserved_0',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	1849
	.byte	16
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,12
	.byte	'EN0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	233
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	1922
	.byte	16
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,12
	.byte	'MODREV',0,1
	.word	187
	.byte	8,0,2,35,0,12
	.byte	'MODTYPE',0,1
	.word	187
	.byte	8,0,2,35,1,12
	.byte	'MODNUMBER',0,2
	.word	233
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2236
	.byte	16
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,12
	.byte	'P0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'P2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'P3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'P4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'P5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'P6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'P7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'P8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'P9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'P10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'P11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'P12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'P13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'P14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'P15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	233
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	2337
	.byte	16
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,12
	.byte	'reserved_0',0,1
	.word	187
	.byte	3,5,2,35,0,12
	.byte	'PC0',0,1
	.word	187
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	187
	.byte	3,5,2,35,1,12
	.byte	'PC1',0,1
	.word	187
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	187
	.byte	3,5,2,35,2,12
	.byte	'PC2',0,1
	.word	187
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	187
	.byte	3,5,2,35,3,12
	.byte	'PC3',0,1
	.word	187
	.byte	5,0,2,35,3,0,8
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2634
	.byte	16
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,12
	.byte	'reserved_0',0,1
	.word	187
	.byte	3,5,2,35,0,12
	.byte	'PC12',0,1
	.word	187
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	187
	.byte	3,5,2,35,1,12
	.byte	'PC13',0,1
	.word	187
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	187
	.byte	3,5,2,35,2,12
	.byte	'PC14',0,1
	.word	187
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	187
	.byte	3,5,2,35,3,12
	.byte	'PC15',0,1
	.word	187
	.byte	5,0,2,35,3,0,8
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	2835
	.byte	16
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,12
	.byte	'reserved_0',0,1
	.word	187
	.byte	3,5,2,35,0,12
	.byte	'PC4',0,1
	.word	187
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	187
	.byte	3,5,2,35,1,12
	.byte	'PC5',0,1
	.word	187
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	187
	.byte	3,5,2,35,2,12
	.byte	'PC6',0,1
	.word	187
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	187
	.byte	3,5,2,35,3,12
	.byte	'PC7',0,1
	.word	187
	.byte	5,0,2,35,3,0,8
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	3042
	.byte	16
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,12
	.byte	'reserved_0',0,1
	.word	187
	.byte	3,5,2,35,0,12
	.byte	'PC8',0,1
	.word	187
	.byte	5,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	187
	.byte	3,5,2,35,1,12
	.byte	'PC9',0,1
	.word	187
	.byte	5,0,2,35,1,12
	.byte	'reserved_16',0,1
	.word	187
	.byte	3,5,2,35,2,12
	.byte	'PC10',0,1
	.word	187
	.byte	5,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	187
	.byte	3,5,2,35,3,12
	.byte	'PC11',0,1
	.word	187
	.byte	5,0,2,35,3,0,8
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	3243
	.byte	16
	.byte	'_Ifx_P_OMCR0_Bits',0,6,192,1,16,4,12
	.byte	'reserved_0',0,2
	.word	233
	.byte	16,0,2,35,0,12
	.byte	'PCL0',0,1
	.word	187
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	187
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	187
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	187
	.byte	1,4,2,35,2,12
	.byte	'reserved_20',0,2
	.word	233
	.byte	12,0,2,35,2,0,8
	.byte	'Ifx_P_OMCR0_Bits',0,6,200,1,3
	.word	3446
	.byte	16
	.byte	'_Ifx_P_OMCR12_Bits',0,6,203,1,16,4,12
	.byte	'reserved_0',0,4
	.word	477
	.byte	28,4,2,35,2,12
	.byte	'PCL12',0,1
	.word	187
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	187
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	187
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	187
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_P_OMCR12_Bits',0,6,210,1,3
	.word	3606
	.byte	16
	.byte	'_Ifx_P_OMCR4_Bits',0,6,213,1,16,4,12
	.byte	'reserved_0',0,4
	.word	477
	.byte	20,12,2,35,2,12
	.byte	'PCL4',0,1
	.word	187
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	187
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	187
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	187
	.byte	1,0,2,35,2,12
	.byte	'reserved_24',0,1
	.word	187
	.byte	8,0,2,35,3,0,8
	.byte	'Ifx_P_OMCR4_Bits',0,6,221,1,3
	.word	3749
	.byte	16
	.byte	'_Ifx_P_OMCR8_Bits',0,6,224,1,16,4,12
	.byte	'reserved_0',0,4
	.word	477
	.byte	24,8,2,35,2,12
	.byte	'PCL8',0,1
	.word	187
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	187
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	187
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	187
	.byte	1,4,2,35,3,12
	.byte	'reserved_28',0,1
	.word	187
	.byte	4,0,2,35,3,0,8
	.byte	'Ifx_P_OMCR8_Bits',0,6,232,1,3
	.word	3909
	.byte	16
	.byte	'_Ifx_P_OMCR_Bits',0,6,235,1,16,4,12
	.byte	'reserved_0',0,2
	.word	233
	.byte	16,0,2,35,0,12
	.byte	'PCL0',0,1
	.word	187
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	187
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	187
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	187
	.byte	1,4,2,35,2,12
	.byte	'PCL4',0,1
	.word	187
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	187
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	187
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	187
	.byte	1,0,2,35,2,12
	.byte	'PCL8',0,1
	.word	187
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	187
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	187
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	187
	.byte	1,4,2,35,3,12
	.byte	'PCL12',0,1
	.word	187
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	187
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	187
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	187
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_P_OMCR_Bits',0,6,254,1,3
	.word	4071
	.byte	16
	.byte	'_Ifx_P_OMR_Bits',0,6,129,2,16,4,12
	.byte	'PS0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'PS4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'PS8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'PS12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'PCL0',0,1
	.word	187
	.byte	1,7,2,35,2,12
	.byte	'PCL1',0,1
	.word	187
	.byte	1,6,2,35,2,12
	.byte	'PCL2',0,1
	.word	187
	.byte	1,5,2,35,2,12
	.byte	'PCL3',0,1
	.word	187
	.byte	1,4,2,35,2,12
	.byte	'PCL4',0,1
	.word	187
	.byte	1,3,2,35,2,12
	.byte	'PCL5',0,1
	.word	187
	.byte	1,2,2,35,2,12
	.byte	'PCL6',0,1
	.word	187
	.byte	1,1,2,35,2,12
	.byte	'PCL7',0,1
	.word	187
	.byte	1,0,2,35,2,12
	.byte	'PCL8',0,1
	.word	187
	.byte	1,7,2,35,3,12
	.byte	'PCL9',0,1
	.word	187
	.byte	1,6,2,35,3,12
	.byte	'PCL10',0,1
	.word	187
	.byte	1,5,2,35,3,12
	.byte	'PCL11',0,1
	.word	187
	.byte	1,4,2,35,3,12
	.byte	'PCL12',0,1
	.word	187
	.byte	1,3,2,35,3,12
	.byte	'PCL13',0,1
	.word	187
	.byte	1,2,2,35,3,12
	.byte	'PCL14',0,1
	.word	187
	.byte	1,1,2,35,3,12
	.byte	'PCL15',0,1
	.word	187
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_P_OMR_Bits',0,6,163,2,3
	.word	4404
	.byte	16
	.byte	'_Ifx_P_OMSR0_Bits',0,6,166,2,16,4,12
	.byte	'PS0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	477
	.byte	28,0,2,35,2,0,8
	.byte	'Ifx_P_OMSR0_Bits',0,6,173,2,3
	.word	4959
	.byte	16
	.byte	'_Ifx_P_OMSR12_Bits',0,6,176,2,16,4,12
	.byte	'reserved_0',0,2
	.word	233
	.byte	12,4,2,35,0,12
	.byte	'PS12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	233
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_P_OMSR12_Bits',0,6,184,2,3
	.word	5092
	.byte	16
	.byte	'_Ifx_P_OMSR4_Bits',0,6,187,2,16,4,12
	.byte	'reserved_0',0,1
	.word	187
	.byte	4,4,2,35,0,12
	.byte	'PS4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'reserved_8',0,4
	.word	477
	.byte	24,0,2,35,2,0,8
	.byte	'Ifx_P_OMSR4_Bits',0,6,195,2,3
	.word	5254
	.byte	16
	.byte	'_Ifx_P_OMSR8_Bits',0,6,198,2,16,4,12
	.byte	'reserved_0',0,1
	.word	187
	.byte	8,0,2,35,0,12
	.byte	'PS8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,4
	.word	477
	.byte	20,0,2,35,2,0,8
	.byte	'Ifx_P_OMSR8_Bits',0,6,206,2,3
	.word	5409
	.byte	16
	.byte	'_Ifx_P_OMSR_Bits',0,6,209,2,16,4,12
	.byte	'PS0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'PS1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'PS2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'PS3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'PS4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'PS5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'PS6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'PS7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'PS8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'PS9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'PS10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'PS11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'PS12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'PS13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'PS14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'PS15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	233
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_P_OMSR_Bits',0,6,228,2,3
	.word	5567
	.byte	16
	.byte	'_Ifx_P_OUT_Bits',0,6,231,2,16,4,12
	.byte	'P0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'P1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'P2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'P3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'P4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'P5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'P6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'P7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'P8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'P9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'P10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'P11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'P12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'P13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'P14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'P15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	233
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_P_OUT_Bits',0,6,250,2,3
	.word	5885
	.byte	16
	.byte	'_Ifx_P_PCSR_Bits',0,6,253,2,16,4,12
	.byte	'reserved_0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'SEL1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'SEL2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,2
	.word	233
	.byte	6,7,2,35,0,12
	.byte	'SEL9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'SEL10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'reserved_11',0,4
	.word	477
	.byte	20,1,2,35,2,12
	.byte	'LCK',0,1
	.word	187
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_P_PCSR_Bits',0,6,135,3,3
	.word	6185
	.byte	16
	.byte	'_Ifx_P_PDISC_Bits',0,6,138,3,16,4,12
	.byte	'PDIS0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'PDIS1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'PDIS2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'PDIS3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'PDIS4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'PDIS5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'PDIS6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'PDIS7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'PDIS8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'PDIS9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'PDIS10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'PDIS11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'PDIS12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'PDIS13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'PDIS14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'PDIS15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'reserved_16',0,2
	.word	233
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_P_PDISC_Bits',0,6,157,3,3
	.word	6381
	.byte	16
	.byte	'_Ifx_P_PDR0_Bits',0,6,160,3,16,4,12
	.byte	'PD0',0,1
	.word	187
	.byte	3,5,2,35,0,12
	.byte	'PL0',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'PD1',0,1
	.word	187
	.byte	3,1,2,35,0,12
	.byte	'PL1',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'PD2',0,1
	.word	187
	.byte	3,5,2,35,1,12
	.byte	'PL2',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'PD3',0,1
	.word	187
	.byte	3,1,2,35,1,12
	.byte	'PL3',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'PD4',0,1
	.word	187
	.byte	3,5,2,35,2,12
	.byte	'PL4',0,1
	.word	187
	.byte	1,4,2,35,2,12
	.byte	'PD5',0,1
	.word	187
	.byte	3,1,2,35,2,12
	.byte	'PL5',0,1
	.word	187
	.byte	1,0,2,35,2,12
	.byte	'PD6',0,1
	.word	187
	.byte	3,5,2,35,3,12
	.byte	'PL6',0,1
	.word	187
	.byte	1,4,2,35,3,12
	.byte	'PD7',0,1
	.word	187
	.byte	3,1,2,35,3,12
	.byte	'PL7',0,1
	.word	187
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_P_PDR0_Bits',0,6,178,3,3
	.word	6733
	.byte	16
	.byte	'_Ifx_P_PDR1_Bits',0,6,181,3,16,4,12
	.byte	'PD8',0,1
	.word	187
	.byte	3,5,2,35,0,12
	.byte	'PL8',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'PD9',0,1
	.word	187
	.byte	3,1,2,35,0,12
	.byte	'PL9',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'PD10',0,1
	.word	187
	.byte	3,5,2,35,1,12
	.byte	'PL10',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'PD11',0,1
	.word	187
	.byte	3,1,2,35,1,12
	.byte	'PL11',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'PD12',0,1
	.word	187
	.byte	3,5,2,35,2,12
	.byte	'PL12',0,1
	.word	187
	.byte	1,4,2,35,2,12
	.byte	'PD13',0,1
	.word	187
	.byte	3,1,2,35,2,12
	.byte	'PL13',0,1
	.word	187
	.byte	1,0,2,35,2,12
	.byte	'PD14',0,1
	.word	187
	.byte	3,5,2,35,3,12
	.byte	'PL14',0,1
	.word	187
	.byte	1,4,2,35,3,12
	.byte	'PD15',0,1
	.word	187
	.byte	3,1,2,35,3,12
	.byte	'PL15',0,1
	.word	187
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_P_PDR1_Bits',0,6,199,3,3
	.word	7022
	.byte	17,6,207,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,2
	.byte	'int',0,4,5,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	1296
	.byte	2,35,0,0,8
	.byte	'Ifx_P_ACCEN0',0,6,212,3,3
	.word	7323
	.byte	17,6,215,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	1849
	.byte	2,35,0,0,8
	.byte	'Ifx_P_ACCEN1',0,6,220,3,3
	.word	7392
	.byte	17,6,223,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	1922
	.byte	2,35,0,0,8
	.byte	'Ifx_P_ESR',0,6,228,3,3
	.word	7454
	.byte	17,6,231,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2236
	.byte	2,35,0,0,8
	.byte	'Ifx_P_ID',0,6,236,3,3
	.word	7513
	.byte	17,6,239,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2337
	.byte	2,35,0,0,8
	.byte	'Ifx_P_IN',0,6,244,3,3
	.word	7571
	.byte	17,6,247,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2634
	.byte	2,35,0,0,8
	.byte	'Ifx_P_IOCR0',0,6,252,3,3
	.word	7629
	.byte	17,6,255,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	2835
	.byte	2,35,0,0,8
	.byte	'Ifx_P_IOCR12',0,6,132,4,3
	.word	7690
	.byte	17,6,135,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3042
	.byte	2,35,0,0,8
	.byte	'Ifx_P_IOCR4',0,6,140,4,3
	.word	7752
	.byte	17,6,143,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3243
	.byte	2,35,0,0,8
	.byte	'Ifx_P_IOCR8',0,6,148,4,3
	.word	7813
	.byte	17,6,151,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4071
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMCR',0,6,156,4,3
	.word	7874
	.byte	17,6,159,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3446
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMCR0',0,6,164,4,3
	.word	7934
	.byte	17,6,167,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3606
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMCR12',0,6,172,4,3
	.word	7995
	.byte	17,6,175,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3749
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMCR4',0,6,180,4,3
	.word	8057
	.byte	17,6,183,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	3909
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMCR8',0,6,188,4,3
	.word	8118
	.byte	17,6,191,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4404
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMR',0,6,196,4,3
	.word	8179
	.byte	17,6,199,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5567
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMSR',0,6,204,4,3
	.word	8238
	.byte	17,6,207,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	4959
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMSR0',0,6,212,4,3
	.word	8298
	.byte	17,6,215,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5092
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMSR12',0,6,220,4,3
	.word	8359
	.byte	17,6,223,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5254
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMSR4',0,6,228,4,3
	.word	8421
	.byte	17,6,231,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5409
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OMSR8',0,6,236,4,3
	.word	8482
	.byte	17,6,239,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	5885
	.byte	2,35,0,0,8
	.byte	'Ifx_P_OUT',0,6,244,4,3
	.word	8543
	.byte	17,6,247,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6185
	.byte	2,35,0,0,8
	.byte	'Ifx_P_PCSR',0,6,252,4,3
	.word	8602
	.byte	17,6,255,4,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6381
	.byte	2,35,0,0,8
	.byte	'Ifx_P_PDISC',0,6,132,5,3
	.word	8662
	.byte	17,6,135,5,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	6733
	.byte	2,35,0,0,8
	.byte	'Ifx_P_PDR0',0,6,140,5,3
	.word	8723
	.byte	17,6,143,5,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	7022
	.byte	2,35,0,0,8
	.byte	'Ifx_P_PDR1',0,6,148,5,3
	.word	8783
	.byte	8
	.byte	'Dio_LevelType',0,1,237,1,17
	.word	187
	.byte	8
	.byte	'Dio_ChannelType',0,1,243,1,17
	.word	233
	.byte	8
	.byte	'Dio_PortType',0,1,249,1,17
	.word	187
	.byte	8
	.byte	'Dio_PortLevelType',0,1,252,1,17
	.word	233
	.byte	16
	.byte	'Dio_ChannelGroupType',0,1,129,2,16,4,13
	.byte	'mask',0,2
	.word	233
	.byte	2,35,0,13
	.byte	'offset',0,1
	.word	187
	.byte	2,35,2,13
	.byte	'port',0,1
	.word	187
	.byte	2,35,3,0,8
	.byte	'Dio_ChannelGroupType',0,1,137,2,3
	.word	8940
	.byte	16
	.byte	'Dio_PortChannelIdType',0,1,139,2,16,8,13
	.byte	'Dio_PortIdConfig',0,4
	.word	336
	.byte	2,35,0,13
	.byte	'Dio_ChannelConfig',0,4
	.word	336
	.byte	2,35,4,0,8
	.byte	'Dio_PortChannelIdType',0,1,145,2,2
	.word	9042
	.byte	16
	.byte	'_Ifx_STM_ACCEN0_Bits',0,7,45,16,4,12
	.byte	'EN0',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'EN1',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'EN2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'EN3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'EN4',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'EN5',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'EN6',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'EN7',0,1
	.word	187
	.byte	1,0,2,35,0,12
	.byte	'EN8',0,1
	.word	187
	.byte	1,7,2,35,1,12
	.byte	'EN9',0,1
	.word	187
	.byte	1,6,2,35,1,12
	.byte	'EN10',0,1
	.word	187
	.byte	1,5,2,35,1,12
	.byte	'EN11',0,1
	.word	187
	.byte	1,4,2,35,1,12
	.byte	'EN12',0,1
	.word	187
	.byte	1,3,2,35,1,12
	.byte	'EN13',0,1
	.word	187
	.byte	1,2,2,35,1,12
	.byte	'EN14',0,1
	.word	187
	.byte	1,1,2,35,1,12
	.byte	'EN15',0,1
	.word	187
	.byte	1,0,2,35,1,12
	.byte	'EN16',0,1
	.word	187
	.byte	1,7,2,35,2,12
	.byte	'EN17',0,1
	.word	187
	.byte	1,6,2,35,2,12
	.byte	'EN18',0,1
	.word	187
	.byte	1,5,2,35,2,12
	.byte	'EN19',0,1
	.word	187
	.byte	1,4,2,35,2,12
	.byte	'EN20',0,1
	.word	187
	.byte	1,3,2,35,2,12
	.byte	'EN21',0,1
	.word	187
	.byte	1,2,2,35,2,12
	.byte	'EN22',0,1
	.word	187
	.byte	1,1,2,35,2,12
	.byte	'EN23',0,1
	.word	187
	.byte	1,0,2,35,2,12
	.byte	'EN24',0,1
	.word	187
	.byte	1,7,2,35,3,12
	.byte	'EN25',0,1
	.word	187
	.byte	1,6,2,35,3,12
	.byte	'EN26',0,1
	.word	187
	.byte	1,5,2,35,3,12
	.byte	'EN27',0,1
	.word	187
	.byte	1,4,2,35,3,12
	.byte	'EN28',0,1
	.word	187
	.byte	1,3,2,35,3,12
	.byte	'EN29',0,1
	.word	187
	.byte	1,2,2,35,3,12
	.byte	'EN30',0,1
	.word	187
	.byte	1,1,2,35,3,12
	.byte	'EN31',0,1
	.word	187
	.byte	1,0,2,35,3,0,8
	.byte	'Ifx_STM_ACCEN0_Bits',0,7,79,3
	.word	9155
	.byte	16
	.byte	'_Ifx_STM_ACCEN1_Bits',0,7,82,16,4,12
	.byte	'reserved_0',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_ACCEN1_Bits',0,7,85,3
	.word	9712
	.byte	16
	.byte	'_Ifx_STM_CAP_Bits',0,7,88,16,4,12
	.byte	'STMCAP63_32',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_CAP_Bits',0,7,91,3
	.word	9789
	.byte	16
	.byte	'_Ifx_STM_CAPSV_Bits',0,7,94,16,4,12
	.byte	'STMCAP63_32',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_CAPSV_Bits',0,7,97,3
	.word	9861
	.byte	16
	.byte	'_Ifx_STM_CLC_Bits',0,7,100,16,4,12
	.byte	'DISR',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'DISS',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'EDIS',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	477
	.byte	28,0,2,35,2,0,8
	.byte	'Ifx_STM_CLC_Bits',0,7,107,3
	.word	9937
	.byte	16
	.byte	'_Ifx_STM_CMCON_Bits',0,7,110,16,4,12
	.byte	'MSIZE0',0,1
	.word	187
	.byte	5,3,2,35,0,12
	.byte	'reserved_5',0,1
	.word	187
	.byte	3,0,2,35,0,12
	.byte	'MSTART0',0,1
	.word	187
	.byte	5,3,2,35,1,12
	.byte	'reserved_13',0,1
	.word	187
	.byte	3,0,2,35,1,12
	.byte	'MSIZE1',0,1
	.word	187
	.byte	5,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	187
	.byte	3,0,2,35,2,12
	.byte	'MSTART1',0,1
	.word	187
	.byte	5,3,2,35,3,12
	.byte	'reserved_29',0,1
	.word	187
	.byte	3,0,2,35,3,0,8
	.byte	'Ifx_STM_CMCON_Bits',0,7,120,3
	.word	10078
	.byte	16
	.byte	'_Ifx_STM_CMP_Bits',0,7,123,16,4,12
	.byte	'CMPVAL',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_CMP_Bits',0,7,126,3
	.word	10296
	.byte	16
	.byte	'_Ifx_STM_ICR_Bits',0,7,129,1,16,4,12
	.byte	'CMP0EN',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'CMP0IR',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'CMP0OS',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'reserved_3',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'CMP1EN',0,1
	.word	187
	.byte	1,3,2,35,0,12
	.byte	'CMP1IR',0,1
	.word	187
	.byte	1,2,2,35,0,12
	.byte	'CMP1OS',0,1
	.word	187
	.byte	1,1,2,35,0,12
	.byte	'reserved_7',0,4
	.word	477
	.byte	25,0,2,35,2,0,8
	.byte	'Ifx_STM_ICR_Bits',0,7,139,1,3
	.word	10363
	.byte	16
	.byte	'_Ifx_STM_ID_Bits',0,7,142,1,16,4,12
	.byte	'MODREV',0,1
	.word	187
	.byte	8,0,2,35,0,12
	.byte	'MODTYPE',0,1
	.word	187
	.byte	8,0,2,35,1,12
	.byte	'MODNUMBER',0,2
	.word	233
	.byte	16,0,2,35,2,0,8
	.byte	'Ifx_STM_ID_Bits',0,7,147,1,3
	.word	10566
	.byte	16
	.byte	'_Ifx_STM_ISCR_Bits',0,7,150,1,16,4,12
	.byte	'CMP0IRR',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'CMP0IRS',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'CMP1IRR',0,1
	.word	187
	.byte	1,5,2,35,0,12
	.byte	'CMP1IRS',0,1
	.word	187
	.byte	1,4,2,35,0,12
	.byte	'reserved_4',0,4
	.word	477
	.byte	28,0,2,35,2,0,8
	.byte	'Ifx_STM_ISCR_Bits',0,7,157,1,3
	.word	10673
	.byte	16
	.byte	'_Ifx_STM_KRST0_Bits',0,7,160,1,16,4,12
	.byte	'RST',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'RSTSTAT',0,1
	.word	187
	.byte	1,6,2,35,0,12
	.byte	'reserved_2',0,4
	.word	477
	.byte	30,0,2,35,2,0,8
	.byte	'Ifx_STM_KRST0_Bits',0,7,165,1,3
	.word	10824
	.byte	16
	.byte	'_Ifx_STM_KRST1_Bits',0,7,168,1,16,4,12
	.byte	'RST',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	477
	.byte	31,0,2,35,2,0,8
	.byte	'Ifx_STM_KRST1_Bits',0,7,172,1,3
	.word	10935
	.byte	16
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,7,175,1,16,4,12
	.byte	'CLR',0,1
	.word	187
	.byte	1,7,2,35,0,12
	.byte	'reserved_1',0,4
	.word	477
	.byte	31,0,2,35,2,0,8
	.byte	'Ifx_STM_KRSTCLR_Bits',0,7,179,1,3
	.word	11027
	.byte	16
	.byte	'_Ifx_STM_OCS_Bits',0,7,182,1,16,4,12
	.byte	'reserved_0',0,4
	.word	477
	.byte	24,8,2,35,2,12
	.byte	'SUS',0,1
	.word	187
	.byte	4,4,2,35,3,12
	.byte	'SUS_P',0,1
	.word	187
	.byte	1,3,2,35,3,12
	.byte	'SUSSTA',0,1
	.word	187
	.byte	1,2,2,35,3,12
	.byte	'reserved_30',0,1
	.word	187
	.byte	2,0,2,35,3,0,8
	.byte	'Ifx_STM_OCS_Bits',0,7,189,1,3
	.word	11123
	.byte	16
	.byte	'_Ifx_STM_TIM0_Bits',0,7,192,1,16,4,12
	.byte	'STM31_0',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_TIM0_Bits',0,7,195,1,3
	.word	11269
	.byte	16
	.byte	'_Ifx_STM_TIM0SV_Bits',0,7,198,1,16,4,12
	.byte	'STM31_0',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_TIM0SV_Bits',0,7,201,1,3
	.word	11341
	.byte	16
	.byte	'_Ifx_STM_TIM1_Bits',0,7,204,1,16,4,12
	.byte	'STM35_4',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_TIM1_Bits',0,7,207,1,3
	.word	11417
	.byte	16
	.byte	'_Ifx_STM_TIM2_Bits',0,7,210,1,16,4,12
	.byte	'STM39_8',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_TIM2_Bits',0,7,213,1,3
	.word	11489
	.byte	16
	.byte	'_Ifx_STM_TIM3_Bits',0,7,216,1,16,4,12
	.byte	'STM43_12',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_TIM3_Bits',0,7,219,1,3
	.word	11561
	.byte	16
	.byte	'_Ifx_STM_TIM4_Bits',0,7,222,1,16,4,12
	.byte	'STM47_16',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_TIM4_Bits',0,7,225,1,3
	.word	11634
	.byte	16
	.byte	'_Ifx_STM_TIM5_Bits',0,7,228,1,16,4,12
	.byte	'STM51_20',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_TIM5_Bits',0,7,231,1,3
	.word	11707
	.byte	16
	.byte	'_Ifx_STM_TIM6_Bits',0,7,234,1,16,4,12
	.byte	'STM63_32',0,4
	.word	477
	.byte	32,0,2,35,2,0,8
	.byte	'Ifx_STM_TIM6_Bits',0,7,237,1,3
	.word	11780
	.byte	17,7,245,1,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9155
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_ACCEN0',0,7,250,1,3
	.word	11853
	.byte	17,7,253,1,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9712
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_ACCEN1',0,7,130,2,3
	.word	11917
	.byte	17,7,133,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9789
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_CAP',0,7,138,2,3
	.word	11981
	.byte	17,7,141,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9861
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_CAPSV',0,7,146,2,3
	.word	12042
	.byte	17,7,149,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	9937
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_CLC',0,7,154,2,3
	.word	12105
	.byte	17,7,157,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10078
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_CMCON',0,7,162,2,3
	.word	12166
	.byte	17,7,165,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10296
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_CMP',0,7,170,2,3
	.word	12229
	.byte	17,7,173,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10363
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_ICR',0,7,178,2,3
	.word	12290
	.byte	17,7,181,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10566
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_ID',0,7,186,2,3
	.word	12351
	.byte	17,7,189,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10673
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_ISCR',0,7,194,2,3
	.word	12411
	.byte	17,7,197,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10824
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_KRST0',0,7,202,2,3
	.word	12473
	.byte	17,7,205,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	10935
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_KRST1',0,7,210,2,3
	.word	12536
	.byte	17,7,213,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11027
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_KRSTCLR',0,7,218,2,3
	.word	12599
	.byte	17,7,221,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11123
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_OCS',0,7,226,2,3
	.word	12664
	.byte	17,7,229,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11269
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_TIM0',0,7,234,2,3
	.word	12725
	.byte	17,7,237,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11341
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_TIM0SV',0,7,242,2,3
	.word	12787
	.byte	17,7,245,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11417
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_TIM1',0,7,250,2,3
	.word	12851
	.byte	17,7,253,2,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11489
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_TIM2',0,7,130,3,3
	.word	12913
	.byte	17,7,133,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11561
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_TIM3',0,7,138,3,3
	.word	12975
	.byte	17,7,141,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11634
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_TIM4',0,7,146,3,3
	.word	13037
	.byte	17,7,149,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11707
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_TIM5',0,7,154,3,3
	.word	13099
	.byte	17,7,157,3,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	11780
	.byte	2,35,0,0,8
	.byte	'Ifx_STM_TIM6',0,7,162,3,3
	.word	13161
	.byte	16
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,8,45,16,4,2
	.byte	'unsigned int',0,4,7,12
	.byte	'EN0',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'EN1',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'EN2',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'EN3',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'EN4',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'EN5',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'EN6',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'EN7',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'EN8',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'EN9',0,4
	.word	13249
	.byte	1,22,2,35,0,12
	.byte	'EN10',0,4
	.word	13249
	.byte	1,21,2,35,0,12
	.byte	'EN11',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'EN12',0,4
	.word	13249
	.byte	1,19,2,35,0,12
	.byte	'EN13',0,4
	.word	13249
	.byte	1,18,2,35,0,12
	.byte	'EN14',0,4
	.word	13249
	.byte	1,17,2,35,0,12
	.byte	'EN15',0,4
	.word	13249
	.byte	1,16,2,35,0,12
	.byte	'EN16',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'EN17',0,4
	.word	13249
	.byte	1,14,2,35,0,12
	.byte	'EN18',0,4
	.word	13249
	.byte	1,13,2,35,0,12
	.byte	'EN19',0,4
	.word	13249
	.byte	1,12,2,35,0,12
	.byte	'EN20',0,4
	.word	13249
	.byte	1,11,2,35,0,12
	.byte	'EN21',0,4
	.word	13249
	.byte	1,10,2,35,0,12
	.byte	'EN22',0,4
	.word	13249
	.byte	1,9,2,35,0,12
	.byte	'EN23',0,4
	.word	13249
	.byte	1,8,2,35,0,12
	.byte	'EN24',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'EN25',0,4
	.word	13249
	.byte	1,6,2,35,0,12
	.byte	'EN26',0,4
	.word	13249
	.byte	1,5,2,35,0,12
	.byte	'EN27',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'EN28',0,4
	.word	13249
	.byte	1,3,2,35,0,12
	.byte	'EN29',0,4
	.word	13249
	.byte	1,2,2,35,0,12
	.byte	'EN30',0,4
	.word	13249
	.byte	1,1,2,35,0,12
	.byte	'EN31',0,4
	.word	13249
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_ACCEN0_Bits',0,8,79,3
	.word	13223
	.byte	16
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,8,82,16,4,12
	.byte	'reserved_0',0,4
	.word	13249
	.byte	32,0,2,35,0,0,8
	.byte	'Ifx_GTM_ACCEN1_Bits',0,8,85,3
	.word	13796
	.byte	16
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,8,88,16,4,12
	.byte	'SEL0',0,4
	.word	13249
	.byte	4,28,2,35,0,12
	.byte	'SEL1',0,4
	.word	13249
	.byte	4,24,2,35,0,12
	.byte	'SEL2',0,4
	.word	13249
	.byte	4,20,2,35,0,12
	.byte	'SEL3',0,4
	.word	13249
	.byte	4,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,8,95,3
	.word	13873
	.byte	16
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,8,98,16,4,12
	.byte	'SEL0',0,4
	.word	13249
	.byte	4,28,2,35,0,12
	.byte	'SEL1',0,4
	.word	13249
	.byte	4,24,2,35,0,12
	.byte	'SEL2',0,4
	.word	13249
	.byte	4,20,2,35,0,12
	.byte	'SEL3',0,4
	.word	13249
	.byte	4,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,8,105,3
	.word	14027
	.byte	16
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,8,108,16,4,12
	.byte	'TO_ADDR',0,4
	.word	13249
	.byte	20,12,2,35,0,12
	.byte	'TO_W1R0',0,4
	.word	13249
	.byte	1,11,2,35,0,12
	.byte	'reserved_21',0,4
	.word	13249
	.byte	11,0,2,35,0,0,8
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,8,113,3
	.word	14181
	.byte	16
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,8,116,16,4,12
	.byte	'BRG_MODE',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'MSK_WR_RSP',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	6,24,2,35,0,12
	.byte	'MODE_UP_PGR',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'BUFF_OVL',0,4
	.word	13249
	.byte	1,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'SYNC_INPUT_REG',0,4
	.word	13249
	.byte	1,19,2,35,0,12
	.byte	'reserved_13',0,4
	.word	13249
	.byte	3,16,2,35,0,12
	.byte	'BRG_RST',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'reserved_17',0,4
	.word	13249
	.byte	7,8,2,35,0,12
	.byte	'BUFF_DPT',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,8,129,1,3
	.word	14309
	.byte	16
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,8,132,1,16,4,12
	.byte	'NEW_TRAN_PTR',0,4
	.word	13249
	.byte	5,27,2,35,0,12
	.byte	'FIRST_RSP_PTR',0,4
	.word	13249
	.byte	5,22,2,35,0,12
	.byte	'TRAN_IN_PGR',0,4
	.word	13249
	.byte	5,17,2,35,0,12
	.byte	'ABT_TRAN_PGR',0,4
	.word	13249
	.byte	5,12,2,35,0,12
	.byte	'FBC',0,4
	.word	13249
	.byte	6,6,2,35,0,12
	.byte	'RSP_TRAN_RDY',0,4
	.word	13249
	.byte	6,0,2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,8,140,1,3
	.word	14616
	.byte	16
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,8,143,1,16,4,12
	.byte	'TRAN_IN_PGR2',0,4
	.word	13249
	.byte	5,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	13249
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,8,147,1,3
	.word	14818
	.byte	16
	.byte	'_Ifx_GTM_CLC_Bits',0,8,150,1,16,4,12
	.byte	'DISR',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'DISS',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'EDIS',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_CLC_Bits',0,8,157,1,3
	.word	14931
	.byte	16
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,8,160,1,16,4,12
	.byte	'CLK_CNT',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,8,164,1,3
	.word	15074
	.byte	16
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,8,167,1,16,4,12
	.byte	'CLK_CNT',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'CLK6_SEL',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'reserved_25',0,4
	.word	13249
	.byte	7,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,8,172,1,3
	.word	15191
	.byte	16
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,8,175,1,16,4,12
	.byte	'CLK_CNT',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'CLK7_SEL',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'reserved_25',0,4
	.word	13249
	.byte	7,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,8,180,1,3
	.word	15326
	.byte	16
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,8,183,1,16,4,12
	.byte	'EN_CLK0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'EN_CLK1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'EN_CLK2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'EN_CLK3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'EN_CLK4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'EN_CLK5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'EN_CLK6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'EN_CLK7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'EN_ECLK0',0,4
	.word	13249
	.byte	2,14,2,35,0,12
	.byte	'EN_ECLK1',0,4
	.word	13249
	.byte	2,12,2,35,0,12
	.byte	'EN_ECLK2',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'EN_FXCLK',0,4
	.word	13249
	.byte	2,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,8,198,1,3
	.word	15461
	.byte	16
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,8,201,1,16,4,12
	.byte	'ECLK_DEN',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,8,205,1,3
	.word	15781
	.byte	16
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,8,208,1,16,4,12
	.byte	'ECLK_NUM',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,8,212,1,3
	.word	15893
	.byte	16
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,8,215,1,16,4,12
	.byte	'FXCLK_SEL',0,4
	.word	13249
	.byte	4,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,8,219,1,3
	.word	16005
	.byte	16
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,8,222,1,16,4,12
	.byte	'GCLK_DEN',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,8,226,1,3
	.word	16121
	.byte	16
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,8,229,1,16,4,12
	.byte	'GCLK_NUM',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,8,233,1,3
	.word	16233
	.byte	16
	.byte	'_Ifx_GTM_CTRL_Bits',0,8,236,1,16,4,12
	.byte	'RF_PROT',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'TO_MODE',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'TO_VAL',0,4
	.word	13249
	.byte	5,23,2,35,0,12
	.byte	'reserved_9',0,4
	.word	13249
	.byte	23,0,2,35,0,0,8
	.byte	'Ifx_GTM_CTRL_Bits',0,8,243,1,3
	.word	16345
	.byte	16
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,8,246,1,16,4,12
	.byte	'O1SEL_0',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	13249
	.byte	2,29,2,35,0,12
	.byte	'SWAP_0',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'O1F_0',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'O1SEL_1',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'I1SEL_1',0,4
	.word	13249
	.byte	1,22,2,35,0,12
	.byte	'SH_EN_1',0,4
	.word	13249
	.byte	1,21,2,35,0,12
	.byte	'SWAP_1',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'O1F_1',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'reserved_14',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'O1SEL_2',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'I1SEL_2',0,4
	.word	13249
	.byte	1,14,2,35,0,12
	.byte	'SH_EN_2',0,4
	.word	13249
	.byte	1,13,2,35,0,12
	.byte	'SWAP_2',0,4
	.word	13249
	.byte	1,12,2,35,0,12
	.byte	'O1F_2',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'reserved_22',0,4
	.word	13249
	.byte	2,8,2,35,0,12
	.byte	'O1SEL_3',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'I1SEL_3',0,4
	.word	13249
	.byte	1,6,2,35,0,12
	.byte	'SH_EN_3',0,4
	.word	13249
	.byte	1,5,2,35,0,12
	.byte	'SWAP_3',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'O1F_3',0,4
	.word	13249
	.byte	2,2,2,35,0,12
	.byte	'reserved_30',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,8,143,2,3
	.word	16498
	.byte	16
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,8,146,2,16,4,12
	.byte	'POL0_0',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'OC0_0',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'SL0_0',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'DT0_0',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'POL1_0',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'OC1_0',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'SL1_0',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'DT1_0',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'POL0_1',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'OC0_1',0,4
	.word	13249
	.byte	1,22,2,35,0,12
	.byte	'SL0_1',0,4
	.word	13249
	.byte	1,21,2,35,0,12
	.byte	'DT0_1',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'POL1_1',0,4
	.word	13249
	.byte	1,19,2,35,0,12
	.byte	'OC1_1',0,4
	.word	13249
	.byte	1,18,2,35,0,12
	.byte	'SL1_1',0,4
	.word	13249
	.byte	1,17,2,35,0,12
	.byte	'DT1_1',0,4
	.word	13249
	.byte	1,16,2,35,0,12
	.byte	'POL0_2',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'OC0_2',0,4
	.word	13249
	.byte	1,14,2,35,0,12
	.byte	'SL0_2',0,4
	.word	13249
	.byte	1,13,2,35,0,12
	.byte	'DT0_2',0,4
	.word	13249
	.byte	1,12,2,35,0,12
	.byte	'POL1_2',0,4
	.word	13249
	.byte	1,11,2,35,0,12
	.byte	'OC1_2',0,4
	.word	13249
	.byte	1,10,2,35,0,12
	.byte	'SL1_2',0,4
	.word	13249
	.byte	1,9,2,35,0,12
	.byte	'DT1_2',0,4
	.word	13249
	.byte	1,8,2,35,0,12
	.byte	'POL0_3',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'OC0_3',0,4
	.word	13249
	.byte	1,6,2,35,0,12
	.byte	'SL0_3',0,4
	.word	13249
	.byte	1,5,2,35,0,12
	.byte	'DT0_3',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'POL1_3',0,4
	.word	13249
	.byte	1,3,2,35,0,12
	.byte	'OC1_3',0,4
	.word	13249
	.byte	1,2,2,35,0,12
	.byte	'SL1_3',0,4
	.word	13249
	.byte	1,1,2,35,0,12
	.byte	'DT1_3',0,4
	.word	13249
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,8,180,2,3
	.word	17010
	.byte	16
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,8,183,2,16,4,12
	.byte	'POL0_0_SR',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'OC0_0_SR',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'SL0_0_SR',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'DT0_0_SR',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'POL1_0_SR',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'OC1_0_SR',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'SL1_0_SR',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'DT1_0_SR',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'POL0_1_SR',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'OC0_1_SR',0,4
	.word	13249
	.byte	1,22,2,35,0,12
	.byte	'SL0_1_SR',0,4
	.word	13249
	.byte	1,21,2,35,0,12
	.byte	'DT0_1_SR',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'POL1_1_SR',0,4
	.word	13249
	.byte	1,19,2,35,0,12
	.byte	'OC1_1_SR',0,4
	.word	13249
	.byte	1,18,2,35,0,12
	.byte	'SL1_1_SR',0,4
	.word	13249
	.byte	1,17,2,35,0,12
	.byte	'DT1_1_SR',0,4
	.word	13249
	.byte	1,16,2,35,0,12
	.byte	'POL0_2_SR',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'OC0_2_SR',0,4
	.word	13249
	.byte	1,14,2,35,0,12
	.byte	'SL0_2_SR',0,4
	.word	13249
	.byte	1,13,2,35,0,12
	.byte	'DT0_2_SR',0,4
	.word	13249
	.byte	1,12,2,35,0,12
	.byte	'POL1_2_SR',0,4
	.word	13249
	.byte	1,11,2,35,0,12
	.byte	'OC1_2_SR',0,4
	.word	13249
	.byte	1,10,2,35,0,12
	.byte	'SL1_2_SR',0,4
	.word	13249
	.byte	1,9,2,35,0,12
	.byte	'DT1_2_SR',0,4
	.word	13249
	.byte	1,8,2,35,0,12
	.byte	'POL0_3_SR',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'OC0_3_SR',0,4
	.word	13249
	.byte	1,6,2,35,0,12
	.byte	'SL0_3_SR',0,4
	.word	13249
	.byte	1,5,2,35,0,12
	.byte	'DT0_3_SR',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'POL1_3_SR',0,4
	.word	13249
	.byte	1,3,2,35,0,12
	.byte	'OC1_3_SR',0,4
	.word	13249
	.byte	1,2,2,35,0,12
	.byte	'SL1_3_SR',0,4
	.word	13249
	.byte	1,1,2,35,0,12
	.byte	'DT1_3_SR',0,4
	.word	13249
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,8,217,2,3
	.word	17631
	.byte	16
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,8,220,2,16,4,12
	.byte	'CLK_SEL',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'UPD_MODE',0,4
	.word	13249
	.byte	3,25,2,35,0,12
	.byte	'reserved_7',0,4
	.word	13249
	.byte	25,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,8,226,2,3
	.word	18354
	.byte	16
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,8,229,2,16,4,12
	.byte	'RELRISE',0,4
	.word	13249
	.byte	10,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	13249
	.byte	6,16,2,35,0,12
	.byte	'RELFALL',0,4
	.word	13249
	.byte	10,6,2,35,0,12
	.byte	'reserved_26',0,4
	.word	13249
	.byte	6,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,8,235,2,3
	.word	18498
	.byte	16
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,8,238,2,16,4,12
	.byte	'RELBLK',0,4
	.word	13249
	.byte	10,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	13249
	.byte	6,16,2,35,0,12
	.byte	'PSU_IN_SEL',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'IN_POL',0,4
	.word	13249
	.byte	1,14,2,35,0,12
	.byte	'reserved_18',0,4
	.word	13249
	.byte	2,12,2,35,0,12
	.byte	'SHIFT_SEL',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'reserved_22',0,4
	.word	13249
	.byte	10,0,2,35,0,0,8
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,8,247,2,3
	.word	18647
	.byte	16
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,8,250,2,16,4,12
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,8,129,3,3
	.word	18862
	.byte	16
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,8,132,3,16,4,12
	.byte	'GRSTEN',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'BRIDGE_MODE_RST',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'AEI_IN',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	13249
	.byte	5,24,2,35,0,12
	.byte	'TOM_OUT_RST',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	13249
	.byte	3,20,2,35,0,12
	.byte	'reserved_12',0,4
	.word	13249
	.byte	4,16,2,35,0,12
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'IRQ_MODE_PULSE',0,4
	.word	13249
	.byte	1,14,2,35,0,12
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	13249
	.byte	1,13,2,35,0,12
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	13249
	.byte	1,12,2,35,0,12
	.byte	'reserved_20',0,4
	.word	13249
	.byte	12,0,2,35,0,0,8
	.byte	'Ifx_GTM_HW_CONF_Bits',0,8,146,3,3
	.word	19066
	.byte	16
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,8,149,3,16,4,12
	.byte	'reserved_0',0,4
	.word	13249
	.byte	4,28,2,35,0,12
	.byte	'AEI_IRQ',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	13249
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,8,154,3,3
	.word	19423
	.byte	16
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,8,157,3,16,4,12
	.byte	'TIM0_CH0_IRQ',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'TIM0_CH1_IRQ',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'TIM0_CH2_IRQ',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'TIM0_CH3_IRQ',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'TIM0_CH4_IRQ',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'TIM0_CH5_IRQ',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'TIM0_CH6_IRQ',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'TIM0_CH7_IRQ',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	13249
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,8,168,3,3
	.word	19551
	.byte	16
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,8,171,3,16,4,12
	.byte	'TOM0_CH0_IRQ',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'TOM0_CH1_IRQ',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'TOM0_CH2_IRQ',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'TOM0_CH3_IRQ',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'TOM0_CH4_IRQ',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'TOM0_CH5_IRQ',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'TOM0_CH6_IRQ',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'TOM0_CH7_IRQ',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'TOM0_CH8_IRQ',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'TOM0_CH9_IRQ',0,4
	.word	13249
	.byte	1,22,2,35,0,12
	.byte	'TOM0_CH10_IRQ',0,4
	.word	13249
	.byte	1,21,2,35,0,12
	.byte	'TOM0_CH11_IRQ',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'TOM0_CH12_IRQ',0,4
	.word	13249
	.byte	1,19,2,35,0,12
	.byte	'TOM0_CH13_IRQ',0,4
	.word	13249
	.byte	1,18,2,35,0,12
	.byte	'TOM0_CH14_IRQ',0,4
	.word	13249
	.byte	1,17,2,35,0,12
	.byte	'TOM0_CH15_IRQ',0,4
	.word	13249
	.byte	1,16,2,35,0,12
	.byte	'TOM1_CH0_IRQ',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'TOM1_CH1_IRQ',0,4
	.word	13249
	.byte	1,14,2,35,0,12
	.byte	'TOM1_CH2_IRQ',0,4
	.word	13249
	.byte	1,13,2,35,0,12
	.byte	'TOM1_CH3_IRQ',0,4
	.word	13249
	.byte	1,12,2,35,0,12
	.byte	'TOM1_CH4_IRQ',0,4
	.word	13249
	.byte	1,11,2,35,0,12
	.byte	'TOM1_CH5_IRQ',0,4
	.word	13249
	.byte	1,10,2,35,0,12
	.byte	'TOM1_CH6_IRQ',0,4
	.word	13249
	.byte	1,9,2,35,0,12
	.byte	'TOM1_CH7_IRQ',0,4
	.word	13249
	.byte	1,8,2,35,0,12
	.byte	'TOM1_CH8_IRQ',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'TOM1_CH9_IRQ',0,4
	.word	13249
	.byte	1,6,2,35,0,12
	.byte	'TOM1_CH10_IRQ',0,4
	.word	13249
	.byte	1,5,2,35,0,12
	.byte	'TOM1_CH11_IRQ',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'TOM1_CH12_IRQ',0,4
	.word	13249
	.byte	1,3,2,35,0,12
	.byte	'TOM1_CH13_IRQ',0,4
	.word	13249
	.byte	1,2,2,35,0,12
	.byte	'TOM1_CH14_IRQ',0,4
	.word	13249
	.byte	1,1,2,35,0,12
	.byte	'TOM1_CH15_IRQ',0,4
	.word	13249
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,8,205,3,3
	.word	19830
	.byte	16
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,8,208,3,16,4,12
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	13249
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,8,219,3,3
	.word	20675
	.byte	16
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,8,222,3,16,4,12
	.byte	'GTM_EIRQ',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	13249
	.byte	3,28,2,35,0,12
	.byte	'TIM0_EIRQ',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	13249
	.byte	27,0,2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,8,228,3,3
	.word	20968
	.byte	16
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,8,231,3,16,4,12
	.byte	'SEL0',0,4
	.word	13249
	.byte	4,28,2,35,0,12
	.byte	'SEL1',0,4
	.word	13249
	.byte	4,24,2,35,0,12
	.byte	'SEL2',0,4
	.word	13249
	.byte	4,20,2,35,0,12
	.byte	'SEL3',0,4
	.word	13249
	.byte	4,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,8,238,3,3
	.word	21122
	.byte	16
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,8,241,3,16,4,12
	.byte	'SEL0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'SEL1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'SEL2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'SEL3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'SEL4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'SEL5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'SEL6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'SEL7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'SEL8',0,4
	.word	13249
	.byte	2,14,2,35,0,12
	.byte	'SEL9',0,4
	.word	13249
	.byte	2,12,2,35,0,12
	.byte	'SEL10',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'SEL11',0,4
	.word	13249
	.byte	2,8,2,35,0,12
	.byte	'SEL12',0,4
	.word	13249
	.byte	2,6,2,35,0,12
	.byte	'SEL13',0,4
	.word	13249
	.byte	2,4,2,35,0,12
	.byte	'SEL14',0,4
	.word	13249
	.byte	2,2,2,35,0,12
	.byte	'SEL15',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,8,131,4,3
	.word	21292
	.byte	16
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,8,134,4,16,4,12
	.byte	'CH0SEL',0,4
	.word	13249
	.byte	4,28,2,35,0,12
	.byte	'CH1SEL',0,4
	.word	13249
	.byte	4,24,2,35,0,12
	.byte	'CH2SEL',0,4
	.word	13249
	.byte	4,20,2,35,0,12
	.byte	'CH3SEL',0,4
	.word	13249
	.byte	4,16,2,35,0,12
	.byte	'CH4SEL',0,4
	.word	13249
	.byte	4,12,2,35,0,12
	.byte	'CH5SEL',0,4
	.word	13249
	.byte	4,8,2,35,0,12
	.byte	'CH6SEL',0,4
	.word	13249
	.byte	4,4,2,35,0,12
	.byte	'CH7SEL',0,4
	.word	13249
	.byte	4,0,2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,8,144,4,3
	.word	21633
	.byte	16
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,8,147,4,16,4,12
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,8,154,4,3
	.word	21858
	.byte	16
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,8,157,4,16,4,12
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'TRG_AEI_USP_BE',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,8,164,4,3
	.word	22056
	.byte	16
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,8,167,4,16,4,12
	.byte	'IRQ_MODE',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,8,171,4,3
	.word	22252
	.byte	16
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,8,174,4,16,4,12
	.byte	'AEI_TO_XPT',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'AEI_USP_ADDR',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'AEI_IM_ADDR',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'AEI_USP_BE',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,8,181,4,3
	.word	22355
	.byte	16
	.byte	'_Ifx_GTM_KRST0_Bits',0,8,184,4,16,4,12
	.byte	'RST',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'RSTSTAT',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_KRST0_Bits',0,8,189,4,3
	.word	22533
	.byte	16
	.byte	'_Ifx_GTM_KRST1_Bits',0,8,192,4,16,4,12
	.byte	'RST',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	13249
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_GTM_KRST1_Bits',0,8,196,4,3
	.word	22644
	.byte	16
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,8,199,4,16,4,12
	.byte	'CLR',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	13249
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,8,203,4,3
	.word	22736
	.byte	16
	.byte	'_Ifx_GTM_OCS_Bits',0,8,206,4,16,4,12
	.byte	'reserved_0',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'SUS',0,4
	.word	13249
	.byte	4,4,2,35,0,12
	.byte	'SUS_P',0,4
	.word	13249
	.byte	1,3,2,35,0,12
	.byte	'SUSSTA',0,4
	.word	13249
	.byte	1,2,2,35,0,12
	.byte	'reserved_30',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_OCS_Bits',0,8,213,4,3
	.word	22832
	.byte	16
	.byte	'_Ifx_GTM_ODA_Bits',0,8,216,4,16,4,12
	.byte	'DDREN',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'DREN',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_ODA_Bits',0,8,221,4,3
	.word	22978
	.byte	16
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,8,224,4,16,4,12
	.byte	'CV',0,4
	.word	13249
	.byte	27,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'CM',0,4
	.word	13249
	.byte	2,2,2,35,0,12
	.byte	'reserved_30',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTBU0T_Bits',0,8,230,4,3
	.word	23084
	.byte	16
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,8,233,4,16,4,12
	.byte	'CV',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	4,4,2,35,0,12
	.byte	'EN',0,4
	.word	13249
	.byte	1,3,2,35,0,12
	.byte	'reserved_29',0,4
	.word	13249
	.byte	3,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTBU1T_Bits',0,8,239,4,3
	.word	23215
	.byte	16
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,8,242,4,16,4,12
	.byte	'CV',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	4,4,2,35,0,12
	.byte	'EN',0,4
	.word	13249
	.byte	1,3,2,35,0,12
	.byte	'reserved_29',0,4
	.word	13249
	.byte	3,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTBU2T_Bits',0,8,248,4,3
	.word	23346
	.byte	16
	.byte	'_Ifx_GTM_OTSC0_Bits',0,8,251,4,16,4,12
	.byte	'B0LMT',0,4
	.word	13249
	.byte	3,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'B0LMI',0,4
	.word	13249
	.byte	4,24,2,35,0,12
	.byte	'B0HMT',0,4
	.word	13249
	.byte	3,21,2,35,0,12
	.byte	'reserved_11',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'B0HMI',0,4
	.word	13249
	.byte	4,16,2,35,0,12
	.byte	'B1LMT',0,4
	.word	13249
	.byte	3,13,2,35,0,12
	.byte	'reserved_19',0,4
	.word	13249
	.byte	1,12,2,35,0,12
	.byte	'B1LMI',0,4
	.word	13249
	.byte	4,8,2,35,0,12
	.byte	'B1HMT',0,4
	.word	13249
	.byte	3,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'B1HMI',0,4
	.word	13249
	.byte	4,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTSC0_Bits',0,8,137,5,3
	.word	23477
	.byte	16
	.byte	'_Ifx_GTM_OTSS_Bits',0,8,140,5,16,4,12
	.byte	'OTGB0',0,4
	.word	13249
	.byte	4,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	4,24,2,35,0,12
	.byte	'OTGB1',0,4
	.word	13249
	.byte	4,20,2,35,0,12
	.byte	'reserved_12',0,4
	.word	13249
	.byte	4,16,2,35,0,12
	.byte	'OTGB2',0,4
	.word	13249
	.byte	4,12,2,35,0,12
	.byte	'reserved_20',0,4
	.word	13249
	.byte	12,0,2,35,0,0,8
	.byte	'Ifx_GTM_OTSS_Bits',0,8,148,5,3
	.word	23759
	.byte	16
	.byte	'_Ifx_GTM_REV_Bits',0,8,151,5,16,4,12
	.byte	'STEP',0,4
	.word	13249
	.byte	8,24,2,35,0,12
	.byte	'NO',0,4
	.word	13249
	.byte	4,20,2,35,0,12
	.byte	'MINOR',0,4
	.word	13249
	.byte	4,16,2,35,0,12
	.byte	'MAJOR',0,4
	.word	13249
	.byte	4,12,2,35,0,12
	.byte	'DEV_CODE0',0,4
	.word	13249
	.byte	4,8,2,35,0,12
	.byte	'DEV_CODE1',0,4
	.word	13249
	.byte	4,4,2,35,0,12
	.byte	'DEV_CODE2',0,4
	.word	13249
	.byte	4,0,2,35,0,0,8
	.byte	'Ifx_GTM_REV_Bits',0,8,160,5,3
	.word	23931
	.byte	16
	.byte	'_Ifx_GTM_RST_Bits',0,8,163,5,16,4,12
	.byte	'RST',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	13249
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_GTM_RST_Bits',0,8,167,5,3
	.word	24109
	.byte	16
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,8,170,5,16,4,12
	.byte	'BASE',0,4
	.word	13249
	.byte	27,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	13249
	.byte	5,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,8,174,5,3
	.word	24197
	.byte	16
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,8,177,5,16,4,12
	.byte	'LOW_RES',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'CH_CLK_SRC',0,4
	.word	13249
	.byte	3,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,8,182,5,3
	.word	24305
	.byte	16
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,8,185,5,16,4,12
	.byte	'BASE',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,8,189,5,3
	.word	24437
	.byte	16
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,8,192,5,16,4,12
	.byte	'CH_MODE',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'CH_CLK_SRC',0,4
	.word	13249
	.byte	3,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,8,197,5,3
	.word	24545
	.byte	16
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,8,200,5,16,4,12
	.byte	'BASE',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,8,204,5,3
	.word	24677
	.byte	16
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,8,207,5,16,4,12
	.byte	'CH_MODE',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'CH_CLK_SRC',0,4
	.word	13249
	.byte	3,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	13249
	.byte	28,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,8,212,5,3
	.word	24785
	.byte	16
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,8,215,5,16,4,12
	.byte	'ENDIS_CH0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_CH1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_CH2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	13249
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,8,221,5,3
	.word	24917
	.byte	16
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,8,224,5,16,4,12
	.byte	'SRC_CH0',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'SRC_CH1',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'SRC_CH2',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'SRC_CH3',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'SRC_CH4',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'SRC_CH5',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'SRC_CH6',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'SRC_CH7',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	13249
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,8,235,5,3
	.word	25063
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,8,238,5,16,4,12
	.byte	'CNT',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,8,242,5,3
	.word	25310
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,8,245,5,16,4,12
	.byte	'CNTS',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'ECNT',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,8,249,5,3
	.word	25413
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,8,252,5,16,4,12
	.byte	'TIM_EN',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'TIM_MODE',0,4
	.word	13249
	.byte	3,28,2,35,0,12
	.byte	'OSM',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'CICTRL',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'TBU0x_SEL',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'GPR0_SEL',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'GPR1_SEL',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'CNTS_SEL',0,4
	.word	13249
	.byte	1,19,2,35,0,12
	.byte	'DSL',0,4
	.word	13249
	.byte	1,18,2,35,0,12
	.byte	'ISL',0,4
	.word	13249
	.byte	1,17,2,35,0,12
	.byte	'ECNT_RESET',0,4
	.word	13249
	.byte	1,16,2,35,0,12
	.byte	'FLT_EN',0,4
	.word	13249
	.byte	1,15,2,35,0,12
	.byte	'FLT_CNT_FRQ',0,4
	.word	13249
	.byte	2,13,2,35,0,12
	.byte	'EXT_CAP_EN',0,4
	.word	13249
	.byte	1,12,2,35,0,12
	.byte	'FLT_MODE_RE',0,4
	.word	13249
	.byte	1,11,2,35,0,12
	.byte	'FLT_CTR_RE',0,4
	.word	13249
	.byte	1,10,2,35,0,12
	.byte	'FLT_MODE_FE',0,4
	.word	13249
	.byte	1,9,2,35,0,12
	.byte	'FLT_CTR_FE',0,4
	.word	13249
	.byte	1,8,2,35,0,12
	.byte	'CLK_SEL',0,4
	.word	13249
	.byte	3,5,2,35,0,12
	.byte	'FR_ECNT_OFL',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'EGPR0_SEL',0,4
	.word	13249
	.byte	1,3,2,35,0,12
	.byte	'EGPR1_SEL',0,4
	.word	13249
	.byte	1,2,2,35,0,12
	.byte	'TOCTRL',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,8,150,6,3
	.word	25512
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,8,153,6,16,4,12
	.byte	'ECNT',0,4
	.word	13249
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,8,157,6,3
	.word	26060
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,8,160,6,16,4,12
	.byte	'EXT_CAP_SRC',0,4
	.word	13249
	.byte	3,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	13249
	.byte	29,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,8,164,6,3
	.word	26166
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,8,167,6,16,4,12
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'TODET_EIRQ_EN',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	13249
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,8,176,6,3
	.word	26280
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,8,179,6,16,4,12
	.byte	'FLT_FE',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,8,183,6,3
	.word	26535
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,8,186,6,16,4,12
	.byte	'FLT_RE',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,8,190,6,3
	.word	26647
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,8,193,6,16,4,12
	.byte	'GPR0',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'ECNT',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,8,197,6,3
	.word	26759
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,8,200,6,16,4,12
	.byte	'GPR1',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'ECNT',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,8,204,6,3
	.word	26858
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,8,207,6,16,4,12
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'TODET_IRQ_EN',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	13249
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,8,216,6,3
	.word	26957
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,8,219,6,16,4,12
	.byte	'TRG_NEWVAL',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'TRG_ECNTOFL',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'TRG_CNTOFL',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'TRG_GPRzOFL',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'TRG_TODET',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'TRG_GLITCHDET',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	13249
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,8,228,6,3
	.word	27204
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,8,231,6,16,4,12
	.byte	'IRQ_MODE',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,8,235,6,3
	.word	27443
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,8,238,6,16,4,12
	.byte	'NEWVAL',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'ECNTOFL',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'CNTOFL',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'GPRzOFL',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'TODET',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'GLITCHDET',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	13249
	.byte	26,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,8,247,6,3
	.word	27560
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,8,250,6,16,4,12
	.byte	'TO_CNT',0,4
	.word	13249
	.byte	8,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	13249
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,8,254,6,3
	.word	27773
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,8,129,7,16,4,12
	.byte	'TOV',0,4
	.word	13249
	.byte	8,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	13249
	.byte	20,4,2,35,0,12
	.byte	'TCS',0,4
	.word	13249
	.byte	3,1,2,35,0,12
	.byte	'reserved_31',0,4
	.word	13249
	.byte	1,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,8,135,7,3
	.word	27880
	.byte	16
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,8,138,7,16,4,12
	.byte	'VAL_0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'MODE_0',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'VAL_1',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'MODE_1',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'VAL_2',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'MODE_2',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'VAL_3',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'MODE_3',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'VAL_4',0,4
	.word	13249
	.byte	2,14,2,35,0,12
	.byte	'MODE_4',0,4
	.word	13249
	.byte	2,12,2,35,0,12
	.byte	'VAL_5',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'MODE_5',0,4
	.word	13249
	.byte	2,8,2,35,0,12
	.byte	'VAL_6',0,4
	.word	13249
	.byte	2,6,2,35,0,12
	.byte	'MODE_6',0,4
	.word	13249
	.byte	2,4,2,35,0,12
	.byte	'VAL_7',0,4
	.word	13249
	.byte	2,2,2,35,0,12
	.byte	'MODE_7',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,8,156,7,3
	.word	28022
	.byte	16
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,8,159,7,16,4,12
	.byte	'F_OUT',0,4
	.word	13249
	.byte	8,24,2,35,0,12
	.byte	'F_IN',0,4
	.word	13249
	.byte	8,16,2,35,0,12
	.byte	'TIM_IN',0,4
	.word	13249
	.byte	8,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	13249
	.byte	8,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,8,165,7,3
	.word	28367
	.byte	16
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,8,168,7,16,4,12
	.byte	'RST_CH0',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'RST_CH1',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'RST_CH2',0,4
	.word	13249
	.byte	1,29,2,35,0,12
	.byte	'RST_CH3',0,4
	.word	13249
	.byte	1,28,2,35,0,12
	.byte	'RST_CH4',0,4
	.word	13249
	.byte	1,27,2,35,0,12
	.byte	'RST_CH5',0,4
	.word	13249
	.byte	1,26,2,35,0,12
	.byte	'RST_CH6',0,4
	.word	13249
	.byte	1,25,2,35,0,12
	.byte	'RST_CH7',0,4
	.word	13249
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	13249
	.byte	24,0,2,35,0,0,8
	.byte	'Ifx_GTM_TIM_RST_Bits',0,8,179,7,3
	.word	28508
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,8,182,7,16,4,12
	.byte	'CM0',0,4
	.word	13249
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,8,186,7,3
	.word	28741
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,8,189,7,16,4,12
	.byte	'CM1',0,4
	.word	13249
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,8,193,7,3
	.word	28844
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,8,196,7,16,4,12
	.byte	'CN0',0,4
	.word	13249
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,8,200,7,3
	.word	28947
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,8,203,7,16,4,12
	.byte	'reserved_0',0,4
	.word	13249
	.byte	11,21,2,35,0,12
	.byte	'SL',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'CLK_SRC_SR',0,4
	.word	13249
	.byte	3,17,2,35,0,12
	.byte	'reserved_15',0,4
	.word	13249
	.byte	5,12,2,35,0,12
	.byte	'RST_CCU0',0,4
	.word	13249
	.byte	1,11,2,35,0,12
	.byte	'OSM_TRIG',0,4
	.word	13249
	.byte	1,10,2,35,0,12
	.byte	'EXT_TRIG',0,4
	.word	13249
	.byte	1,9,2,35,0,12
	.byte	'EXTTRIGOUT',0,4
	.word	13249
	.byte	1,8,2,35,0,12
	.byte	'TRIGOUT',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'reserved_25',0,4
	.word	13249
	.byte	1,6,2,35,0,12
	.byte	'OSM',0,4
	.word	13249
	.byte	1,5,2,35,0,12
	.byte	'BITREV',0,4
	.word	13249
	.byte	1,4,2,35,0,12
	.byte	'reserved_28',0,4
	.word	13249
	.byte	4,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,8,218,7,3
	.word	29050
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,8,221,7,16,4,12
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,8,226,7,3
	.word	29378
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,8,229,7,16,4,12
	.byte	'TRG_CCU0TC0',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'TRG_CCU1TC0',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,8,234,7,3
	.word	29521
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,8,237,7,16,4,12
	.byte	'IRQ_MODE',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,8,241,7,3
	.word	29670
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,8,244,7,16,4,12
	.byte	'CCU0TC',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'CCU1TC',0,4
	.word	13249
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	13249
	.byte	30,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,8,249,7,3
	.word	29787
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,8,252,7,16,4,12
	.byte	'SR0',0,4
	.word	13249
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,8,128,8,3
	.word	29924
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,8,131,8,16,4,12
	.byte	'SR1',0,4
	.word	13249
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,8,135,8,3
	.word	30027
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,8,138,8,16,4,12
	.byte	'OL',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	13249
	.byte	31,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,8,142,8,3
	.word	30130
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,8,145,8,16,4,12
	.byte	'ACT_TB',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'TB_TRIG',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'TBU_SEL',0,4
	.word	13249
	.byte	2,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	13249
	.byte	5,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,8,151,8,3
	.word	30233
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,8,154,8,16,4,12
	.byte	'ENDIS_CTRL0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_CTRL1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_CTRL2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'ENDIS_CTRL3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'ENDIS_CTRL4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'ENDIS_CTRL5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'ENDIS_CTRL6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'ENDIS_CTRL7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,8,165,8,3
	.word	30387
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,8,168,8,16,4,12
	.byte	'ENDIS_STAT0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_STAT1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_STAT2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'ENDIS_STAT3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'ENDIS_STAT4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'ENDIS_STAT5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'ENDIS_STAT6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'ENDIS_STAT7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,8,179,8,3
	.word	30677
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,8,182,8,16,4,12
	.byte	'FUPD_CTRL0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'FUPD_CTRL1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'FUPD_CTRL2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'FUPD_CTRL3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'FUPD_CTRL4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'FUPD_CTRL5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'FUPD_CTRL6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'FUPD_CTRL7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'RSTCN0_CH0',0,4
	.word	13249
	.byte	2,14,2,35,0,12
	.byte	'RSTCN0_CH1',0,4
	.word	13249
	.byte	2,12,2,35,0,12
	.byte	'RSTCN0_CH2',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'RSTCN0_CH3',0,4
	.word	13249
	.byte	2,8,2,35,0,12
	.byte	'RSTCN0_CH4',0,4
	.word	13249
	.byte	2,6,2,35,0,12
	.byte	'RSTCN0_CH5',0,4
	.word	13249
	.byte	2,4,2,35,0,12
	.byte	'RSTCN0_CH6',0,4
	.word	13249
	.byte	2,2,2,35,0,12
	.byte	'RSTCN0_CH7',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,8,200,8,3
	.word	30967
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,8,203,8,16,4,12
	.byte	'HOST_TRIG',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	13249
	.byte	7,24,2,35,0,12
	.byte	'RST_CH0',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'RST_CH1',0,4
	.word	13249
	.byte	1,22,2,35,0,12
	.byte	'RST_CH2',0,4
	.word	13249
	.byte	1,21,2,35,0,12
	.byte	'RST_CH3',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'RST_CH4',0,4
	.word	13249
	.byte	1,19,2,35,0,12
	.byte	'RST_CH5',0,4
	.word	13249
	.byte	1,18,2,35,0,12
	.byte	'RST_CH6',0,4
	.word	13249
	.byte	1,17,2,35,0,12
	.byte	'RST_CH7',0,4
	.word	13249
	.byte	1,16,2,35,0,12
	.byte	'UPEN_CTRL0',0,4
	.word	13249
	.byte	2,14,2,35,0,12
	.byte	'UPEN_CTRL1',0,4
	.word	13249
	.byte	2,12,2,35,0,12
	.byte	'UPEN_CTRL2',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'UPEN_CTRL3',0,4
	.word	13249
	.byte	2,8,2,35,0,12
	.byte	'UPEN_CTRL4',0,4
	.word	13249
	.byte	2,6,2,35,0,12
	.byte	'UPEN_CTRL5',0,4
	.word	13249
	.byte	2,4,2,35,0,12
	.byte	'UPEN_CTRL6',0,4
	.word	13249
	.byte	2,2,2,35,0,12
	.byte	'UPEN_CTRL7',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,8,223,8,3
	.word	31400
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,8,226,8,16,4,12
	.byte	'INT_TRIG0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'INT_TRIG1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'INT_TRIG2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'INT_TRIG3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'INT_TRIG4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'INT_TRIG5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'INT_TRIG6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'INT_TRIG7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,8,237,8,3
	.word	31850
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,8,240,8,16,4,12
	.byte	'OUTEN_CTRL0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'OUTEN_CTRL1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'OUTEN_CTRL2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'OUTEN_CTRL3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'OUTEN_CTRL4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'OUTEN_CTRL5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'OUTEN_CTRL6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'OUTEN_CTRL7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,8,251,8,3
	.word	32120
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,8,254,8,16,4,12
	.byte	'OUTEN_STAT0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'OUTEN_STAT1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'OUTEN_STAT2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'OUTEN_STAT3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'OUTEN_STAT4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'OUTEN_STAT5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'OUTEN_STAT6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'OUTEN_STAT7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,8,137,9,3
	.word	32410
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,8,140,9,16,4,12
	.byte	'ACT_TB',0,4
	.word	13249
	.byte	24,8,2,35,0,12
	.byte	'TB_TRIG',0,4
	.word	13249
	.byte	1,7,2,35,0,12
	.byte	'TBU_SEL',0,4
	.word	13249
	.byte	2,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	13249
	.byte	5,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,8,146,9,3
	.word	32700
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,8,149,9,16,4,12
	.byte	'ENDIS_CTRL0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_CTRL1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_CTRL2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'ENDIS_CTRL3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'ENDIS_CTRL4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'ENDIS_CTRL5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'ENDIS_CTRL6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'ENDIS_CTRL7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,8,160,9,3
	.word	32854
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,8,163,9,16,4,12
	.byte	'ENDIS_STAT0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_STAT1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_STAT2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'ENDIS_STAT3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'ENDIS_STAT4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'ENDIS_STAT5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'ENDIS_STAT6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'ENDIS_STAT7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,8,174,9,3
	.word	33144
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,8,177,9,16,4,12
	.byte	'FUPD_CTRL0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'FUPD_CTRL1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'FUPD_CTRL2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'FUPD_CTRL3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'FUPD_CTRL4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'FUPD_CTRL5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'FUPD_CTRL6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'FUPD_CTRL7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'RSTCN0_CH0',0,4
	.word	13249
	.byte	2,14,2,35,0,12
	.byte	'RSTCN0_CH1',0,4
	.word	13249
	.byte	2,12,2,35,0,12
	.byte	'RSTCN0_CH2',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'RSTCN0_CH3',0,4
	.word	13249
	.byte	2,8,2,35,0,12
	.byte	'RSTCN0_CH4',0,4
	.word	13249
	.byte	2,6,2,35,0,12
	.byte	'RSTCN0_CH5',0,4
	.word	13249
	.byte	2,4,2,35,0,12
	.byte	'RSTCN0_CH6',0,4
	.word	13249
	.byte	2,2,2,35,0,12
	.byte	'RSTCN0_CH7',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,8,195,9,3
	.word	33434
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,8,198,9,16,4,12
	.byte	'HOST_TRIG',0,4
	.word	13249
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	13249
	.byte	7,24,2,35,0,12
	.byte	'RST_CH0',0,4
	.word	13249
	.byte	1,23,2,35,0,12
	.byte	'RST_CH1',0,4
	.word	13249
	.byte	1,22,2,35,0,12
	.byte	'RST_CH2',0,4
	.word	13249
	.byte	1,21,2,35,0,12
	.byte	'RST_CH3',0,4
	.word	13249
	.byte	1,20,2,35,0,12
	.byte	'RST_CH4',0,4
	.word	13249
	.byte	1,19,2,35,0,12
	.byte	'RST_CH5',0,4
	.word	13249
	.byte	1,18,2,35,0,12
	.byte	'RST_CH6',0,4
	.word	13249
	.byte	1,17,2,35,0,12
	.byte	'RST_CH7',0,4
	.word	13249
	.byte	1,16,2,35,0,12
	.byte	'UPEN_CTRL0',0,4
	.word	13249
	.byte	2,14,2,35,0,12
	.byte	'UPEN_CTRL1',0,4
	.word	13249
	.byte	2,12,2,35,0,12
	.byte	'UPEN_CTRL2',0,4
	.word	13249
	.byte	2,10,2,35,0,12
	.byte	'UPEN_CTRL3',0,4
	.word	13249
	.byte	2,8,2,35,0,12
	.byte	'UPEN_CTRL4',0,4
	.word	13249
	.byte	2,6,2,35,0,12
	.byte	'UPEN_CTRL5',0,4
	.word	13249
	.byte	2,4,2,35,0,12
	.byte	'UPEN_CTRL6',0,4
	.word	13249
	.byte	2,2,2,35,0,12
	.byte	'UPEN_CTRL7',0,4
	.word	13249
	.byte	2,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,8,218,9,3
	.word	33867
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,8,221,9,16,4,12
	.byte	'INT_TRIG0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'INT_TRIG1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'INT_TRIG2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'INT_TRIG3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'INT_TRIG4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'INT_TRIG5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'INT_TRIG6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'INT_TRIG7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,8,232,9,3
	.word	34317
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,8,235,9,16,4,12
	.byte	'OUTEN_CTRL0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'OUTEN_CTRL1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'OUTEN_CTRL2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'OUTEN_CTRL3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'OUTEN_CTRL4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'OUTEN_CTRL5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'OUTEN_CTRL6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'OUTEN_CTRL7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,8,246,9,3
	.word	34587
	.byte	16
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,8,249,9,16,4,12
	.byte	'OUTEN_STAT0',0,4
	.word	13249
	.byte	2,30,2,35,0,12
	.byte	'OUTEN_STAT1',0,4
	.word	13249
	.byte	2,28,2,35,0,12
	.byte	'OUTEN_STAT2',0,4
	.word	13249
	.byte	2,26,2,35,0,12
	.byte	'OUTEN_STAT3',0,4
	.word	13249
	.byte	2,24,2,35,0,12
	.byte	'OUTEN_STAT4',0,4
	.word	13249
	.byte	2,22,2,35,0,12
	.byte	'OUTEN_STAT5',0,4
	.word	13249
	.byte	2,20,2,35,0,12
	.byte	'OUTEN_STAT6',0,4
	.word	13249
	.byte	2,18,2,35,0,12
	.byte	'OUTEN_STAT7',0,4
	.word	13249
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	13249
	.byte	16,0,2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,8,132,10,3
	.word	34877
	.byte	17,8,140,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	13223
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ACCEN0',0,8,145,10,3
	.word	35167
	.byte	17,8,148,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	13796
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ACCEN1',0,8,153,10,3
	.word	35231
	.byte	17,8,156,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	13873
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,8,161,10,3
	.word	35295
	.byte	17,8,164,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14027
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,8,169,10,3
	.word	35365
	.byte	17,8,172,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14181
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,8,177,10,3
	.word	35435
	.byte	17,8,180,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14309
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_MODE',0,8,185,10,3
	.word	35505
	.byte	17,8,188,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14616
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,8,193,10,3
	.word	35574
	.byte	17,8,196,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14818
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,8,201,10,3
	.word	35643
	.byte	17,8,204,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	14931
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CLC',0,8,209,10,3
	.word	35712
	.byte	17,8,212,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15074
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,8,217,10,3
	.word	35773
	.byte	17,8,220,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15191
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,8,225,10,3
	.word	35846
	.byte	17,8,228,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15326
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,8,233,10,3
	.word	35918
	.byte	17,8,236,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15461
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_CLK_EN',0,8,241,10,3
	.word	35990
	.byte	17,8,244,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15781
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,8,249,10,3
	.word	36058
	.byte	17,8,252,10,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	15893
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,8,129,11,3
	.word	36128
	.byte	17,8,132,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16005
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,8,137,11,3
	.word	36198
	.byte	17,8,140,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16121
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,8,145,11,3
	.word	36270
	.byte	17,8,148,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16233
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,8,153,11,3
	.word	36340
	.byte	17,8,156,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16345
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_CTRL',0,8,161,11,3
	.word	36410
	.byte	17,8,164,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	16498
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,8,169,11,3
	.word	36472
	.byte	17,8,172,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17010
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,8,177,11,3
	.word	36542
	.byte	17,8,180,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	17631
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,8,185,11,3
	.word	36612
	.byte	17,8,188,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18354
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_CTRL',0,8,193,11,3
	.word	36685
	.byte	17,8,196,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18498
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_DTV_CH',0,8,201,11,3
	.word	36751
	.byte	17,8,204,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18647
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,8,209,11,3
	.word	36819
	.byte	17,8,212,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	18862
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_EIRQ_EN',0,8,217,11,3
	.word	36888
	.byte	17,8,220,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19066
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_HW_CONF',0,8,225,11,3
	.word	36953
	.byte	17,8,228,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19423
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_0',0,8,233,11,3
	.word	37018
	.byte	17,8,236,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19551
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_2',0,8,241,11,3
	.word	37086
	.byte	17,8,244,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	19830
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_6',0,8,249,11,3
	.word	37154
	.byte	17,8,252,11,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20675
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,8,129,12,3
	.word	37222
	.byte	17,8,132,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	20968
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,8,137,12,3
	.word	37293
	.byte	17,8,140,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21122
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,8,145,12,3
	.word	37363
	.byte	17,8,148,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21292
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,8,153,12,3
	.word	37440
	.byte	17,8,156,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21633
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,8,161,12,3
	.word	37515
	.byte	17,8,164,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	21858
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_EN',0,8,169,12,3
	.word	37591
	.byte	17,8,172,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22056
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_FORCINT',0,8,177,12,3
	.word	37655
	.byte	17,8,180,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22252
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_MODE',0,8,185,12,3
	.word	37724
	.byte	17,8,188,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22355
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,8,193,12,3
	.word	37790
	.byte	17,8,196,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22533
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_KRST0',0,8,201,12,3
	.word	37858
	.byte	17,8,204,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22644
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_KRST1',0,8,209,12,3
	.word	37921
	.byte	17,8,212,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22736
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_KRSTCLR',0,8,217,12,3
	.word	37984
	.byte	17,8,220,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22832
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OCS',0,8,225,12,3
	.word	38049
	.byte	17,8,228,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	22978
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_ODA',0,8,233,12,3
	.word	38110
	.byte	17,8,236,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23084
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTBU0T',0,8,241,12,3
	.word	38171
	.byte	17,8,244,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23215
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTBU1T',0,8,249,12,3
	.word	38235
	.byte	17,8,252,12,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23346
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTBU2T',0,8,129,13,3
	.word	38299
	.byte	17,8,132,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23477
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTSC0',0,8,137,13,3
	.word	38363
	.byte	17,8,140,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23759
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_OTSS',0,8,145,13,3
	.word	38426
	.byte	17,8,148,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	23931
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_REV',0,8,153,13,3
	.word	38488
	.byte	17,8,156,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24109
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_RST',0,8,161,13,3
	.word	38549
	.byte	17,8,164,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24197
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,8,169,13,3
	.word	38610
	.byte	17,8,172,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24305
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,8,177,13,3
	.word	38680
	.byte	17,8,180,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24437
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,8,185,13,3
	.word	38750
	.byte	17,8,188,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24545
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,8,193,13,3
	.word	38820
	.byte	17,8,196,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24677
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,8,201,13,3
	.word	38890
	.byte	17,8,204,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24785
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,8,209,13,3
	.word	38960
	.byte	17,8,212,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	24917
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TBU_CHEN',0,8,217,13,3
	.word	39030
	.byte	17,8,220,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	25063
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,8,225,13,3
	.word	39096
	.byte	17,8,228,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	25310
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CNT',0,8,233,13,3
	.word	39168
	.byte	17,8,236,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	25413
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,8,241,13,3
	.word	39236
	.byte	17,8,244,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	25512
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,8,249,13,3
	.word	39305
	.byte	17,8,252,13,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26060
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,8,129,14,3
	.word	39374
	.byte	17,8,132,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26166
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,8,137,14,3
	.word	39443
	.byte	17,8,140,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26280
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,8,145,14,3
	.word	39513
	.byte	17,8,148,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26535
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,8,153,14,3
	.word	39585
	.byte	17,8,156,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26647
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,8,161,14,3
	.word	39656
	.byte	17,8,164,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26759
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,8,169,14,3
	.word	39727
	.byte	17,8,172,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26858
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,8,177,14,3
	.word	39796
	.byte	17,8,180,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	26957
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,8,185,14,3
	.word	39865
	.byte	17,8,188,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	27204
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,8,193,14,3
	.word	39936
	.byte	17,8,196,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	27443
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,8,201,14,3
	.word	40012
	.byte	17,8,204,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	27560
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,8,209,14,3
	.word	40085
	.byte	17,8,212,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	27773
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,8,217,14,3
	.word	40160
	.byte	17,8,220,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	27880
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,8,225,14,3
	.word	40229
	.byte	17,8,228,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28022
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_IN_SRC',0,8,233,14,3
	.word	40298
	.byte	17,8,236,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28367
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_INP_VAL',0,8,241,14,3
	.word	40366
	.byte	17,8,244,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28508
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TIM_RST',0,8,249,14,3
	.word	40435
	.byte	17,8,252,14,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28741
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CM0',0,8,129,15,3
	.word	40500
	.byte	17,8,132,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28844
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CM1',0,8,137,15,3
	.word	40568
	.byte	17,8,140,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	28947
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CN0',0,8,145,15,3
	.word	40636
	.byte	17,8,148,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29050
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,8,153,15,3
	.word	40704
	.byte	17,8,156,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29378
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,8,161,15,3
	.word	40773
	.byte	17,8,164,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29521
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,8,169,15,3
	.word	40844
	.byte	17,8,172,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29670
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,8,177,15,3
	.word	40920
	.byte	17,8,180,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29787
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,8,185,15,3
	.word	40993
	.byte	17,8,188,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	29924
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_SR0',0,8,193,15,3
	.word	41068
	.byte	17,8,196,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30027
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_SR1',0,8,201,15,3
	.word	41136
	.byte	17,8,204,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30130
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_CH_STAT',0,8,209,15,3
	.word	41204
	.byte	17,8,212,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30233
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,8,217,15,3
	.word	41273
	.byte	17,8,220,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30387
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,8,225,15,3
	.word	41346
	.byte	17,8,228,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30677
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,8,233,15,3
	.word	41423
	.byte	17,8,236,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	30967
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,8,241,15,3
	.word	41500
	.byte	17,8,244,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	31400
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,8,249,15,3
	.word	41576
	.byte	17,8,252,15,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	31850
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,8,129,16,3
	.word	41651
	.byte	17,8,132,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	32120
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,8,137,16,3
	.word	41726
	.byte	17,8,140,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	32410
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,8,145,16,3
	.word	41803
	.byte	17,8,148,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	32700
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,8,153,16,3
	.word	41880
	.byte	17,8,156,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	32854
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,8,161,16,3
	.word	41953
	.byte	17,8,164,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	33144
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,8,169,16,3
	.word	42030
	.byte	17,8,172,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	33434
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,8,177,16,3
	.word	42107
	.byte	17,8,180,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	33867
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,8,185,16,3
	.word	42183
	.byte	17,8,188,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	34317
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,8,193,16,3
	.word	42258
	.byte	17,8,196,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	34587
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,8,201,16,3
	.word	42333
	.byte	17,8,204,16,9,4,13
	.byte	'U',0,4
	.word	477
	.byte	2,35,0,13
	.byte	'I',0,4
	.word	7340
	.byte	2,35,0,13
	.byte	'B',0,4
	.word	34877
	.byte	2,35,0,0,8
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,8,209,16,3
	.word	42410
	.byte	16
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,8,220,16,25,4,13
	.byte	'CTRL',0,4
	.word	35773
	.byte	2,35,0,0,18
	.word	42487
	.byte	8
	.byte	'Ifx_GTM_CMU_CLK0_5',0,8,223,16,3
	.word	42528
	.byte	16
	.byte	'_Ifx_GTM_CMU_CLK_6',0,8,226,16,25,4,13
	.byte	'CTRL',0,4
	.word	35846
	.byte	2,35,0,0,18
	.word	42561
	.byte	8
	.byte	'Ifx_GTM_CMU_CLK_6',0,8,229,16,3
	.word	42601
	.byte	16
	.byte	'_Ifx_GTM_CMU_CLK_7',0,8,232,16,25,4,13
	.byte	'CTRL',0,4
	.word	35918
	.byte	2,35,0,0,18
	.word	42633
	.byte	8
	.byte	'Ifx_GTM_CMU_CLK_7',0,8,235,16,3
	.word	42673
	.byte	16
	.byte	'_Ifx_GTM_CMU_ECLK',0,8,238,16,25,8,13
	.byte	'NUM',0,4
	.word	36128
	.byte	2,35,0,13
	.byte	'DEN',0,4
	.word	36058
	.byte	2,35,4,0,18
	.word	42705
	.byte	8
	.byte	'Ifx_GTM_CMU_ECLK',0,8,242,16,3
	.word	42756
	.byte	16
	.byte	'_Ifx_GTM_CMU_FXCLK',0,8,245,16,25,4,13
	.byte	'CTRL',0,4
	.word	36198
	.byte	2,35,0,0,18
	.word	42787
	.byte	8
	.byte	'Ifx_GTM_CMU_FXCLK',0,8,248,16,3
	.word	42827
	.byte	16
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,8,251,16,25,4,13
	.byte	'OUTSEL',0,4
	.word	37363
	.byte	2,35,0,0,18
	.word	42859
	.byte	8
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,8,254,16,3
	.word	42904
	.byte	16
	.byte	'_Ifx_GTM_INOUTSEL_T',0,8,129,17,25,32,14,32
	.word	37440
	.byte	15,7,0,13
	.byte	'OUTSEL',0,32
	.word	42965
	.byte	2,35,0,0,18
	.word	42939
	.byte	8
	.byte	'Ifx_GTM_INOUTSEL_T',0,8,132,17,3
	.word	42991
	.byte	16
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,8,135,17,25,32,13
	.byte	'INSEL',0,4
	.word	37515
	.byte	2,35,0,14,28
	.word	187
	.byte	15,27,0,13
	.byte	'reserved_4',0,28
	.word	43067
	.byte	2,35,4,0,18
	.word	43024
	.byte	8
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,8,139,17,3
	.word	43097
	.byte	16
	.byte	'_Ifx_GTM_TIM_CH',0,8,142,17,25,116,13
	.byte	'GPR0',0,4
	.word	39727
	.byte	2,35,0,13
	.byte	'GPR1',0,4
	.word	39796
	.byte	2,35,4,13
	.byte	'CNT',0,4
	.word	39168
	.byte	2,35,8,13
	.byte	'ECNT',0,4
	.word	39374
	.byte	2,35,12,13
	.byte	'CNTS',0,4
	.word	39236
	.byte	2,35,16,13
	.byte	'TDUC',0,4
	.word	40160
	.byte	2,35,20,13
	.byte	'TDUV',0,4
	.word	40229
	.byte	2,35,24,13
	.byte	'FLT_RE',0,4
	.word	39656
	.byte	2,35,28,13
	.byte	'FLT_FE',0,4
	.word	39585
	.byte	2,35,32,13
	.byte	'CTRL',0,4
	.word	39305
	.byte	2,35,36,13
	.byte	'ECTRL',0,4
	.word	39443
	.byte	2,35,40,13
	.byte	'IRQ_NOTIFY',0,4
	.word	40085
	.byte	2,35,44,13
	.byte	'IRQ_EN',0,4
	.word	39865
	.byte	2,35,48,13
	.byte	'IRQ_FORCINT',0,4
	.word	39936
	.byte	2,35,52,13
	.byte	'IRQ_MODE',0,4
	.word	40012
	.byte	2,35,56,13
	.byte	'EIRQ_EN',0,4
	.word	39513
	.byte	2,35,60,14,52
	.word	187
	.byte	15,51,0,13
	.byte	'reserved_40',0,52
	.word	43404
	.byte	2,35,64,0,18
	.word	43132
	.byte	8
	.byte	'Ifx_GTM_TIM_CH',0,8,161,17,3
	.word	43435
	.byte	16
	.byte	'_Ifx_GTM_TOM_CH',0,8,164,17,25,48,13
	.byte	'CTRL',0,4
	.word	40704
	.byte	2,35,0,13
	.byte	'SR0',0,4
	.word	41068
	.byte	2,35,4,13
	.byte	'SR1',0,4
	.word	41136
	.byte	2,35,8,13
	.byte	'CM0',0,4
	.word	40500
	.byte	2,35,12,13
	.byte	'CM1',0,4
	.word	40568
	.byte	2,35,16,13
	.byte	'CN0',0,4
	.word	40636
	.byte	2,35,20,13
	.byte	'STAT',0,4
	.word	41204
	.byte	2,35,24,13
	.byte	'IRQ_NOTIFY',0,4
	.word	40993
	.byte	2,35,28,13
	.byte	'IRQ_EN',0,4
	.word	40773
	.byte	2,35,32,13
	.byte	'IRQ_FORCINT',0,4
	.word	40844
	.byte	2,35,36,13
	.byte	'IRQ_MODE',0,4
	.word	40920
	.byte	2,35,40,14,4
	.word	187
	.byte	15,3,0,13
	.byte	'reserved_2C',0,4
	.word	43654
	.byte	2,35,44,0,18
	.word	43464
	.byte	8
	.byte	'Ifx_GTM_TOM_CH',0,8,178,17,3
	.word	43685
	.byte	16
	.byte	'_Ifx_GTM_BRIDGE',0,8,191,17,25,12,13
	.byte	'MODE',0,4
	.word	35505
	.byte	2,35,0,13
	.byte	'PTR1',0,4
	.word	35574
	.byte	2,35,4,13
	.byte	'PTR2',0,4
	.word	35643
	.byte	2,35,8,0,18
	.word	43714
	.byte	8
	.byte	'Ifx_GTM_BRIDGE',0,8,196,17,3
	.word	43779
	.byte	16
	.byte	'_Ifx_GTM_CMU',0,8,199,17,25,72,13
	.byte	'CLK_EN',0,4
	.word	35990
	.byte	2,35,0,13
	.byte	'GCLK_NUM',0,4
	.word	36340
	.byte	2,35,4,13
	.byte	'GCLK_DEN',0,4
	.word	36270
	.byte	2,35,8,14,24
	.word	42487
	.byte	15,5,0,18
	.word	43879
	.byte	13
	.byte	'CLK0_5',0,24
	.word	43888
	.byte	2,35,12,18
	.word	42561
	.byte	13
	.byte	'CLK_6',0,4
	.word	43909
	.byte	2,35,36,18
	.word	42633
	.byte	13
	.byte	'CLK_7',0,4
	.word	43929
	.byte	2,35,40,14,24
	.word	42705
	.byte	15,2,0,18
	.word	43949
	.byte	13
	.byte	'ECLK',0,24
	.word	43958
	.byte	2,35,44,18
	.word	42787
	.byte	13
	.byte	'FXCLK',0,4
	.word	43977
	.byte	2,35,68,0,18
	.word	43808
	.byte	8
	.byte	'Ifx_GTM_CMU',0,8,209,17,3
	.word	43998
	.byte	16
	.byte	'_Ifx_GTM_DTM',0,8,212,17,25,36,13
	.byte	'CTRL',0,4
	.word	36685
	.byte	2,35,0,13
	.byte	'CH_CTRL1',0,4
	.word	36472
	.byte	2,35,4,13
	.byte	'CH_CTRL2',0,4
	.word	36542
	.byte	2,35,8,13
	.byte	'CH_CTRL2_SR',0,4
	.word	36612
	.byte	2,35,12,13
	.byte	'PS_CTRL',0,4
	.word	36819
	.byte	2,35,16,14,16
	.word	36751
	.byte	15,3,0,13
	.byte	'DTV_CH',0,16
	.word	44131
	.byte	2,35,20,0,18
	.word	44024
	.byte	8
	.byte	'Ifx_GTM_DTM',0,8,220,17,3
	.word	44157
	.byte	16
	.byte	'_Ifx_GTM_ICM',0,8,223,17,25,60,13
	.byte	'IRQG_0',0,4
	.word	37018
	.byte	2,35,0,13
	.byte	'reserved_4',0,4
	.word	43654
	.byte	2,35,4,13
	.byte	'IRQG_2',0,4
	.word	37086
	.byte	2,35,8,14,12
	.word	187
	.byte	15,11,0,13
	.byte	'reserved_C',0,12
	.word	44254
	.byte	2,35,12,13
	.byte	'IRQG_6',0,4
	.word	37154
	.byte	2,35,24,14,20
	.word	187
	.byte	15,19,0,13
	.byte	'reserved_1C',0,20
	.word	44299
	.byte	2,35,28,13
	.byte	'IRQG_MEI',0,4
	.word	37293
	.byte	2,35,48,13
	.byte	'reserved_34',0,4
	.word	43654
	.byte	2,35,52,13
	.byte	'IRQG_CEI1',0,4
	.word	37222
	.byte	2,35,56,0,18
	.word	44183
	.byte	8
	.byte	'Ifx_GTM_ICM',0,8,234,17,3
	.word	44388
	.byte	16
	.byte	'_Ifx_GTM_INOUTSEL',0,8,237,17,25,148,1,14,32
	.word	43024
	.byte	15,0,0,18
	.word	44439
	.byte	13
	.byte	'TIM',0,32
	.word	44448
	.byte	2,35,0,18
	.word	42939
	.byte	13
	.byte	'T',0,32
	.word	44466
	.byte	2,35,32,14,80
	.word	187
	.byte	15,79,0,13
	.byte	'reserved_40',0,80
	.word	44482
	.byte	2,35,64,18
	.word	42859
	.byte	13
	.byte	'CAN',0,4
	.word	44512
	.byte	3,35,144,1,0,18
	.word	44414
	.byte	8
	.byte	'Ifx_GTM_INOUTSEL',0,8,243,17,3
	.word	44532
	.byte	16
	.byte	'_Ifx_GTM_TBU',0,8,246,17,25,28,13
	.byte	'CHEN',0,4
	.word	39030
	.byte	2,35,0,13
	.byte	'CH0_CTRL',0,4
	.word	38680
	.byte	2,35,4,13
	.byte	'CH0_BASE',0,4
	.word	38610
	.byte	2,35,8,13
	.byte	'CH1_CTRL',0,4
	.word	38820
	.byte	2,35,12,13
	.byte	'CH1_BASE',0,4
	.word	38750
	.byte	2,35,16,13
	.byte	'CH2_CTRL',0,4
	.word	38960
	.byte	2,35,20,13
	.byte	'CH2_BASE',0,4
	.word	38890
	.byte	2,35,24,0,18
	.word	44563
	.byte	8
	.byte	'Ifx_GTM_TBU',0,8,255,17,3
	.word	44705
	.byte	16
	.byte	'_Ifx_GTM_TIM',0,8,130,18,25,128,8,18
	.word	43132
	.byte	13
	.byte	'CH0',0,116
	.word	44751
	.byte	2,35,0,13
	.byte	'INP_VAL',0,4
	.word	40366
	.byte	2,35,116,13
	.byte	'IN_SRC',0,4
	.word	40298
	.byte	2,35,120,13
	.byte	'RST',0,4
	.word	40435
	.byte	2,35,124,18
	.word	43132
	.byte	13
	.byte	'CH1',0,116
	.word	44815
	.byte	3,35,128,1,13
	.byte	'reserved_F4',0,12
	.word	44254
	.byte	3,35,244,1,18
	.word	43132
	.byte	13
	.byte	'CH2',0,116
	.word	44856
	.byte	3,35,128,2,13
	.byte	'reserved_174',0,12
	.word	44254
	.byte	3,35,244,2,18
	.word	43132
	.byte	13
	.byte	'CH3',0,116
	.word	44898
	.byte	3,35,128,3,13
	.byte	'reserved_1F4',0,12
	.word	44254
	.byte	3,35,244,3,18
	.word	43132
	.byte	13
	.byte	'CH4',0,116
	.word	44940
	.byte	3,35,128,4,13
	.byte	'reserved_274',0,12
	.word	44254
	.byte	3,35,244,4,18
	.word	43132
	.byte	13
	.byte	'CH5',0,116
	.word	44982
	.byte	3,35,128,5,13
	.byte	'reserved_2F4',0,12
	.word	44254
	.byte	3,35,244,5,18
	.word	43132
	.byte	13
	.byte	'CH6',0,116
	.word	45024
	.byte	3,35,128,6,13
	.byte	'reserved_374',0,12
	.word	44254
	.byte	3,35,244,6,18
	.word	43132
	.byte	13
	.byte	'CH7',0,116
	.word	45066
	.byte	3,35,128,7,13
	.byte	'reserved_3F4',0,12
	.word	44254
	.byte	3,35,244,7,0,18
	.word	44731
	.byte	8
	.byte	'Ifx_GTM_TIM',0,8,150,18,3
	.word	45109
	.byte	16
	.byte	'_Ifx_GTM_TOM',0,8,153,18,25,128,16,18
	.word	43464
	.byte	13
	.byte	'CH0',0,48
	.word	45155
	.byte	2,35,0,13
	.byte	'TGC0_GLB_CTRL',0,4
	.word	41576
	.byte	2,35,48,13
	.byte	'TGC0_ACT_TB',0,4
	.word	41273
	.byte	2,35,52,13
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	41500
	.byte	2,35,56,13
	.byte	'TGC0_INT_TRIG',0,4
	.word	41651
	.byte	2,35,60,18
	.word	43464
	.byte	13
	.byte	'CH1',0,48
	.word	45264
	.byte	2,35,64,13
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	41346
	.byte	2,35,112,13
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	41423
	.byte	2,35,116,13
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	41726
	.byte	2,35,120,13
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	41803
	.byte	2,35,124,18
	.word	43464
	.byte	13
	.byte	'CH2',0,48
	.word	45382
	.byte	3,35,128,1,14,16
	.word	187
	.byte	15,15,0,13
	.byte	'reserved_B0',0,16
	.word	45401
	.byte	3,35,176,1,18
	.word	43464
	.byte	13
	.byte	'CH3',0,48
	.word	45432
	.byte	3,35,192,1,13
	.byte	'reserved_F0',0,16
	.word	45401
	.byte	3,35,240,1,18
	.word	43464
	.byte	13
	.byte	'CH4',0,48
	.word	45473
	.byte	3,35,128,2,13
	.byte	'reserved_130',0,16
	.word	45401
	.byte	3,35,176,2,18
	.word	43464
	.byte	13
	.byte	'CH5',0,48
	.word	45515
	.byte	3,35,192,2,13
	.byte	'reserved_170',0,16
	.word	45401
	.byte	3,35,240,2,18
	.word	43464
	.byte	13
	.byte	'CH6',0,48
	.word	45557
	.byte	3,35,128,3,13
	.byte	'reserved_1B0',0,16
	.word	45401
	.byte	3,35,176,3,18
	.word	43464
	.byte	13
	.byte	'CH7',0,48
	.word	45599
	.byte	3,35,192,3,13
	.byte	'reserved_1F0',0,16
	.word	45401
	.byte	3,35,240,3,18
	.word	43464
	.byte	13
	.byte	'CH8',0,48
	.word	45641
	.byte	3,35,128,4,13
	.byte	'TGC1_GLB_CTRL',0,4
	.word	42183
	.byte	3,35,176,4,13
	.byte	'TGC1_ACT_TB',0,4
	.word	41880
	.byte	3,35,180,4,13
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	42107
	.byte	3,35,184,4,13
	.byte	'TGC1_INT_TRIG',0,4
	.word	42258
	.byte	3,35,188,4,18
	.word	43464
	.byte	13
	.byte	'CH9',0,48
	.word	45755
	.byte	3,35,192,4,13
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	41953
	.byte	3,35,240,4,13
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	42030
	.byte	3,35,244,4,13
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	42333
	.byte	3,35,248,4,13
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	42410
	.byte	3,35,252,4,18
	.word	43464
	.byte	13
	.byte	'CH10',0,48
	.word	45878
	.byte	3,35,128,5,13
	.byte	'reserved_2B0',0,16
	.word	45401
	.byte	3,35,176,5,18
	.word	43464
	.byte	13
	.byte	'CH11',0,48
	.word	45921
	.byte	3,35,192,5,13
	.byte	'reserved_2F0',0,16
	.word	45401
	.byte	3,35,240,5,18
	.word	43464
	.byte	13
	.byte	'CH12',0,48
	.word	45964
	.byte	3,35,128,6,13
	.byte	'reserved_330',0,16
	.word	45401
	.byte	3,35,176,6,18
	.word	43464
	.byte	13
	.byte	'CH13',0,48
	.word	46007
	.byte	3,35,192,6,13
	.byte	'reserved_370',0,16
	.word	45401
	.byte	3,35,240,6,18
	.word	43464
	.byte	13
	.byte	'CH14',0,48
	.word	46050
	.byte	3,35,128,7,13
	.byte	'reserved_3B0',0,16
	.word	45401
	.byte	3,35,176,7,18
	.word	43464
	.byte	13
	.byte	'CH15',0,48
	.word	46093
	.byte	3,35,192,7,14,144,8
	.word	187
	.byte	15,143,8,0,13
	.byte	'reserved_3F0',0,144,8
	.word	46113
	.byte	3,35,240,7,0,18
	.word	45135
	.byte	8
	.byte	'Ifx_GTM_TOM',0,8,199,18,3
	.word	46149
	.byte	11,9,130,4,20,64,13
	.byte	'CTRL',0,4
	.word	40704
	.byte	2,35,0,13
	.byte	'SR0',0,4
	.word	41068
	.byte	2,35,4,13
	.byte	'SR1',0,4
	.word	41136
	.byte	2,35,8,13
	.byte	'CM0',0,4
	.word	40500
	.byte	2,35,12,13
	.byte	'CM1',0,4
	.word	40568
	.byte	2,35,16,13
	.byte	'CN0',0,4
	.word	40636
	.byte	2,35,20,13
	.byte	'STAT',0,4
	.word	41204
	.byte	2,35,24,13
	.byte	'IRQ_NOTIFY',0,4
	.word	40993
	.byte	2,35,28,13
	.byte	'IRQ_EN',0,4
	.word	40773
	.byte	2,35,32,13
	.byte	'IRQ_FORCINT',0,4
	.word	40844
	.byte	2,35,36,13
	.byte	'IRQ_MODE',0,4
	.word	40920
	.byte	2,35,40,14,20
	.word	187
	.byte	15,19,0,13
	.byte	'reserved_2C',0,20
	.word	46349
	.byte	2,35,44,0,18
	.word	46175
	.byte	8
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,9,155,4,4
	.word	46380
	.byte	11,9,157,4,20,128,4,13
	.byte	'GLB_CTRL',0,4
	.word	41576
	.byte	2,35,0,13
	.byte	'ACT_TB',0,4
	.word	41273
	.byte	2,35,4,13
	.byte	'FUPD_CTRL',0,4
	.word	41500
	.byte	2,35,8,13
	.byte	'INT_TRIG',0,4
	.word	41651
	.byte	2,35,12,14,48
	.word	187
	.byte	15,47,0,13
	.byte	'reserved_tgc0',0,48
	.word	46492
	.byte	2,35,16,13
	.byte	'ENDIS_CTRL',0,4
	.word	41346
	.byte	2,35,64,13
	.byte	'ENDIS_STAT',0,4
	.word	41423
	.byte	2,35,68,13
	.byte	'OUTEN_CTRL',0,4
	.word	41726
	.byte	2,35,72,13
	.byte	'OUTEN_STAT',0,4
	.word	41803
	.byte	2,35,76,14,176,3
	.word	187
	.byte	15,175,3,0,13
	.byte	'reserved_tgc1',0,176,3
	.word	46604
	.byte	2,35,80,0,18
	.word	46414
	.byte	8
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,9,177,4,5
	.word	46640
	.byte	11,9,179,4,20,128,16,13
	.byte	'reserved_tom0',0,48
	.word	46492
	.byte	2,35,0,14,128,8
	.word	46414
	.byte	15,1,0,18
	.word	46705
	.byte	13
	.byte	'TGC',0,128,8
	.word	46715
	.byte	2,35,48,14,208,7
	.word	187
	.byte	15,207,7,0,13
	.byte	'reserved_tgc2',0,208,7
	.word	46734
	.byte	3,35,176,8,0,18
	.word	46675
	.byte	8
	.byte	'Ifx_GTM_TOM_TGCx',0,9,184,4,5
	.word	46771
	.byte	11,9,187,4,20,128,16,14,128,8
	.word	46175
	.byte	15,15,0,18
	.word	46809
	.byte	13
	.byte	'CH',0,128,8
	.word	46819
	.byte	2,35,0,14,128,8
	.word	187
	.byte	15,255,7,0,13
	.byte	'reserved_tom1',0,128,8
	.word	46837
	.byte	3,35,128,8,0,18
	.word	46802
	.byte	8
	.byte	'Ifx_GTM_TOM_CHx',0,9,191,4,5
	.word	46874
	.byte	11,9,212,4,20,128,1,13
	.byte	'CH_GPR0',0,4
	.word	39727
	.byte	2,35,0,13
	.byte	'CH_GPR1',0,4
	.word	39796
	.byte	2,35,4,13
	.byte	'CH_CNT',0,4
	.word	39168
	.byte	2,35,8,13
	.byte	'CH_ECNT',0,4
	.word	39374
	.byte	2,35,12,13
	.byte	'CH_CNTS',0,4
	.word	39236
	.byte	2,35,16,13
	.byte	'CH_TDUC',0,4
	.word	40160
	.byte	2,35,20,13
	.byte	'CH_TDUV',0,4
	.word	40229
	.byte	2,35,24,13
	.byte	'CH_FLT_RE',0,4
	.word	39656
	.byte	2,35,28,13
	.byte	'CH_FLT_FE',0,4
	.word	39585
	.byte	2,35,32,13
	.byte	'CH_CTRL',0,4
	.word	39305
	.byte	2,35,36,13
	.byte	'CH_ECTRL',0,4
	.word	39443
	.byte	2,35,40,13
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	40085
	.byte	2,35,44,13
	.byte	'CH_IRQ_EN',0,4
	.word	39865
	.byte	2,35,48,13
	.byte	'CH_IRQ_FORCINT',0,4
	.word	39936
	.byte	2,35,52,13
	.byte	'CH_IRQ_MODE',0,4
	.word	40012
	.byte	2,35,56,13
	.byte	'CH_EIRQ_EN',0,4
	.word	39513
	.byte	2,35,60,14,64
	.word	187
	.byte	15,63,0,13
	.byte	'reserved_40',0,64
	.word	47209
	.byte	2,35,64,0,18
	.word	46904
	.byte	8
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,9,248,4,4
	.word	47240
	.byte	11,9,250,4,20,8,13
	.byte	'IN_SRC',0,4
	.word	40298
	.byte	2,35,0,13
	.byte	'RST',0,4
	.word	40435
	.byte	2,35,4,0,18
	.word	47274
	.byte	8
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,9,255,4,4
	.word	47310
	.byte	11,9,129,5,21,128,16,14,128,8
	.word	46904
	.byte	15,7,0,18
	.word	47361
	.byte	13
	.byte	'CH',0,128,8
	.word	47371
	.byte	2,35,0,13
	.byte	'reserved_tim1',0,128,8
	.word	46837
	.byte	3,35,128,8,0,18
	.word	47354
	.byte	8
	.byte	'Ifx_GTM_TIM_CHx',0,9,133,5,4
	.word	47415
	.byte	11,9,135,5,20,128,16,14,120
	.word	187
	.byte	15,119,0,13
	.byte	'reserved_tim2',0,120
	.word	47452
	.byte	2,35,0,18
	.word	47274
	.byte	13
	.byte	'IN_SRC_RESET',0,8
	.word	47484
	.byte	2,35,120,14,128,15
	.word	187
	.byte	15,255,14,0,13
	.byte	'reserved_tim3',0,128,15
	.word	47511
	.byte	3,35,128,1,0,18
	.word	47445
	.byte	8
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,9,140,5,4
	.word	47548
	.byte	19,9,174,5,11,1,20
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,20
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,20
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,20
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,20
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,20
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,20
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,20
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,8
	.byte	'Gtm_ConfigurableClockType',0,9,184,5,4
	.word	47586
	.byte	19,9,188,5,11,1,20
	.byte	'GTM_LOW',0,0,20
	.byte	'GTM_HIGH',0,1,0,8
	.byte	'Gtm_OutputLevelType',0,9,192,5,4
	.word	47820
	.byte	19,9,195,5,11,1,20
	.byte	'TOM_GLB_CTRL',0,0,20
	.byte	'TOM_ACT_TB',0,1,20
	.byte	'TOM_FUPD_CTRL',0,2,20
	.byte	'TOM_INT_TRIG',0,3,20
	.byte	'TOM_RESERVED_0',0,4,20
	.byte	'TOM_RESERVED_1',0,5,20
	.byte	'TOM_RESERVED_2',0,6,20
	.byte	'TOM_RESERVED_3',0,7,20
	.byte	'TOM_RESERVED_4',0,8,20
	.byte	'TOM_RESERVED_5',0,9,20
	.byte	'TOM_RESERVED_6',0,10,20
	.byte	'TOM_RESERVED_7',0,11,20
	.byte	'TOM_RESERVED_8',0,12,20
	.byte	'TOM_RESERVED_9',0,13,20
	.byte	'TOM_RESERVED_10',0,14,20
	.byte	'TOM_RESERVED_11',0,15,20
	.byte	'TOM_ENDIS_CTRL',0,16,20
	.byte	'TOM_ENDIS_STAT',0,17,20
	.byte	'TOM_OUTEN_CTRL',0,18,20
	.byte	'TOM_OUTEN_STAT',0,19,0,8
	.byte	'Gtm_TomTimerRegistersType',0,9,217,5,4
	.word	47877
	.byte	11,9,221,5,11,8,13
	.byte	'FltRisingEdge',0,4
	.word	336
	.byte	2,35,0,13
	.byte	'FltFallingEdge',0,4
	.word	336
	.byte	2,35,4,0,8
	.byte	'Gtm_TimFilterType',0,9,225,5,4
	.word	48252
	.byte	8
	.byte	'Gtm_TbuChCtrlType',0,9,230,5,32
	.word	38680
	.byte	8
	.byte	'Gtm_TbuChBaseType',0,9,231,5,32
	.word	38610
	.byte	11,9,233,5,11,8,13
	.byte	'CH_CTRL',0,4
	.word	38680
	.byte	2,35,0,13
	.byte	'CH_BASE',0,4
	.word	38610
	.byte	2,35,4,0,8
	.byte	'Gtm_TbuChType',0,9,237,5,4
	.word	48387
	.byte	11,9,249,5,9,36,14,4
	.word	336
	.byte	15,0,0,13
	.byte	'TimInSel',0,4
	.word	48457
	.byte	2,35,0,14,32
	.word	336
	.byte	15,7,0,13
	.byte	'ToutSel',0,32
	.word	48484
	.byte	2,35,4,0,8
	.byte	'Gtm_PortConfigType',0,9,253,5,2
	.word	48451
	.byte	11,9,129,6,9,8,13
	.byte	'TimRisingEdgeFilter',0,4
	.word	336
	.byte	2,35,0,13
	.byte	'TimFallingEdgeFilter',0,4
	.word	336
	.byte	2,35,4,0,8
	.byte	'Gtm_TimFltType',0,9,134,6,2
	.word	48539
	.byte	11,9,138,6,11,24,13
	.byte	'TimUsage',0,1
	.word	187
	.byte	2,35,0,13
	.byte	'TimIrqEn',0,1
	.word	187
	.byte	2,35,1,13
	.byte	'TimErrIrqEn',0,1
	.word	187
	.byte	2,35,2,13
	.byte	'TimExtCapSrc',0,1
	.word	187
	.byte	2,35,3,13
	.byte	'TimCtrlValue',0,4
	.word	336
	.byte	2,35,4,21
	.word	48539
	.byte	6
	.word	48736
	.byte	13
	.byte	'GtmTimFltPtr',0,4
	.word	48741
	.byte	2,35,8,13
	.byte	'TimCntsValue',0,4
	.word	336
	.byte	2,35,12,13
	.byte	'TimTduValue',0,4
	.word	336
	.byte	2,35,16,13
	.byte	'TimInSrcSel',0,4
	.word	336
	.byte	2,35,20,0,8
	.byte	'Gtm_TimConfigType',0,9,151,6,4
	.word	48629
	.byte	11,9,154,6,11,40,14,8
	.word	187
	.byte	15,7,0,13
	.byte	'Gtm_TimUsage',0,8
	.word	48866
	.byte	2,35,0,14,16
	.word	187
	.byte	15,15,0,14,32
	.word	48897
	.byte	15,1,0,13
	.byte	'Gtm_TomUsage',0,32
	.word	48906
	.byte	2,35,8,0,8
	.byte	'Gtm_ModUsageConfigType',0,9,163,6,4
	.word	48860
	.byte	11,9,177,6,9,16,13
	.byte	'GtmTomUpdateEn',0,2
	.word	233
	.byte	2,35,0,13
	.byte	'GtmTomEndisCtrl',0,2
	.word	233
	.byte	2,35,2,13
	.byte	'GtmTomEndisStat',0,2
	.word	233
	.byte	2,35,4,13
	.byte	'GtmTomOutenCtrl',0,2
	.word	233
	.byte	2,35,6,13
	.byte	'GtmTomOutenStat',0,2
	.word	233
	.byte	2,35,8,13
	.byte	'GtmTomFupd',0,4
	.word	336
	.byte	2,35,10,0,8
	.byte	'Gtm_TomTgcConfigGroupType',0,9,185,6,2
	.word	48970
	.byte	11,9,189,6,9,12,13
	.byte	'GtmTomIntTrig',0,2
	.word	233
	.byte	2,35,0,13
	.byte	'GtmTomActTb',0,4
	.word	336
	.byte	2,35,2,21
	.word	48970
	.byte	6
	.word	49206
	.byte	13
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	49211
	.byte	2,35,8,0,8
	.byte	'Gtm_TomTgcConfigType',0,9,196,6,2
	.word	49156
	.byte	11,9,199,6,9,12,13
	.byte	'GtmTomIrqEn',0,1
	.word	187
	.byte	2,35,0,13
	.byte	'GtmTomCn0Value',0,2
	.word	233
	.byte	2,35,2,13
	.byte	'GtmTomCm0Value',0,2
	.word	233
	.byte	2,35,4,13
	.byte	'GtmTomCm1Value',0,2
	.word	233
	.byte	2,35,6,13
	.byte	'GtmTomSr0Value',0,2
	.word	233
	.byte	2,35,8,13
	.byte	'GtmTomSr1Value',0,2
	.word	233
	.byte	2,35,10,0,8
	.byte	'Gtm_TomChannelConfigType',0,9,207,6,2
	.word	49278
	.byte	11,9,211,6,9,12,13
	.byte	'TomUsage',0,1
	.word	187
	.byte	2,35,0,13
	.byte	'GtmTomIrqMode',0,1
	.word	187
	.byte	2,35,1,13
	.byte	'GtmTomControlWord',0,4
	.word	336
	.byte	2,35,2,21
	.word	49278
	.byte	6
	.word	49534
	.byte	13
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	49539
	.byte	2,35,8,0,8
	.byte	'Gtm_TomConfigType',0,9,219,6,2
	.word	49460
	.byte	11,9,223,6,9,8,13
	.byte	'CmuEclkNum',0,4
	.word	336
	.byte	2,35,0,13
	.byte	'CmuEclkDen',0,4
	.word	336
	.byte	2,35,4,0,8
	.byte	'Gtm_ExtClkType',0,9,227,6,2
	.word	49601
	.byte	11,9,230,6,9,64,13
	.byte	'GtmClockEnable',0,4
	.word	336
	.byte	2,35,0,13
	.byte	'GtmCmuClkCnt',0,32
	.word	48484
	.byte	2,35,4,13
	.byte	'GtmFxdClkControl',0,4
	.word	336
	.byte	2,35,36,14,24
	.word	49601
	.byte	15,2,0,13
	.byte	'GtmEclk',0,24
	.word	49750
	.byte	2,35,40,0,8
	.byte	'Gtm_ClockSettingType',0,9,236,6,2
	.word	49672
	.byte	11,9,240,6,9,4,13
	.byte	'GtmCtrlValue',0,2
	.word	233
	.byte	2,35,0,13
	.byte	'GtmIrqEnable',0,2
	.word	233
	.byte	2,35,2,0,8
	.byte	'Gtm_GeneralConfigType',0,9,245,6,2
	.word	49807
	.byte	11,9,249,6,9,6,13
	.byte	'TbuChannelCtrl',0,1
	.word	187
	.byte	2,35,0,13
	.byte	'TbuBaseValue',0,4
	.word	336
	.byte	2,35,2,0,8
	.byte	'Gtm_TbuConfigType',0,9,253,6,2
	.word	49889
	.byte	11,9,129,7,9,72,13
	.byte	'GtmModuleSleepEnable',0,1
	.word	187
	.byte	2,35,0,13
	.byte	'GtmGclkNum',0,4
	.word	336
	.byte	2,35,2,13
	.byte	'GtmGclkDen',0,4
	.word	336
	.byte	2,35,6,13
	.byte	'GtmAccessEnable0',0,4
	.word	336
	.byte	2,35,10,13
	.byte	'GtmAccessEnable1',0,4
	.word	336
	.byte	2,35,14,14,2
	.word	233
	.byte	15,0,0,13
	.byte	'GtmTimModuleUsage',0,2
	.word	50097
	.byte	2,35,18,14,1
	.word	187
	.byte	15,0,0,13
	.byte	'GtmTimUsage',0,1
	.word	50133
	.byte	2,35,20,21
	.word	48629
	.byte	6
	.word	50163
	.byte	13
	.byte	'GtmTimConfigPtr',0,4
	.word	50168
	.byte	2,35,24,13
	.byte	'GtmTomTgcUsage',0,1
	.word	50133
	.byte	2,35,28,21
	.word	49156
	.byte	6
	.word	50222
	.byte	13
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	50227
	.byte	2,35,32,14,8
	.word	336
	.byte	15,1,0,13
	.byte	'GtmTomModuleUsage',0,8
	.word	50260
	.byte	2,35,36,13
	.byte	'GtmTomUsage',0,4
	.word	48457
	.byte	2,35,44,21
	.word	49460
	.byte	6
	.word	50317
	.byte	13
	.byte	'GtmTomConfigPtr',0,4
	.word	50322
	.byte	2,35,48,21
	.word	48860
	.byte	6
	.word	50352
	.byte	13
	.byte	'GtmModUsageConfigPtr',0,4
	.word	50357
	.byte	2,35,52,21
	.word	49807
	.byte	6
	.word	50392
	.byte	13
	.byte	'GtmGeneralConfigPtr',0,4
	.word	50397
	.byte	2,35,56,21
	.word	49889
	.byte	6
	.word	50431
	.byte	13
	.byte	'GtmTbuConfigPtr',0,4
	.word	50436
	.byte	2,35,60,21
	.word	187
	.byte	6
	.word	50466
	.byte	13
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	50471
	.byte	2,35,64,13
	.byte	'GtmTtcanTriggers',0,2
	.word	50097
	.byte	2,35,68,0,8
	.byte	'Gtm_ModuleConfigType',0,9,163,7,2
	.word	49969
	.byte	22,1,1,23
	.word	187
	.byte	23
	.word	187
	.byte	23
	.word	187
	.byte	23
	.word	233
	.byte	0,6
	.word	50563
	.byte	8
	.byte	'Gtm_NotificationPtrType',0,9,172,7,16
	.word	50587
	.byte	17,10,52,9,164,1,14,164,1
	.word	187
	.byte	15,163,1,0,13
	.byte	'datas',0,164,1
	.word	50631
	.byte	2,35,0,11,10,55,5,164,1,13
	.byte	'certFmt',0,1
	.word	187
	.byte	2,35,0,13
	.byte	'pModNum',0,8
	.word	48866
	.byte	2,35,1,13
	.byte	'customPars',0,16
	.word	48897
	.byte	2,35,9
.L333:
	.byte	14,3
	.word	187
	.byte	15,2,0,13
	.byte	'certFailDate',0,3
	.word	50718
	.byte	2,35,25,14,4
	.word	187
	.byte	15,3,0,13
	.byte	'certSequenceNum',0,4
	.word	50749
	.byte	2,35,28,13
	.byte	'signAlgoFlg',0,1
	.word	187
	.byte	2,35,32,13
	.byte	'pubKeyCurPar',0,1
	.word	187
	.byte	2,35,33,13
	.byte	'hashAlgoFlg',0,1
	.word	187
	.byte	2,35,34,13
	.byte	'pubKeyIdx',0,1
	.word	187
	.byte	2,35,35,13
	.byte	'certPubKey',0,64
	.word	47209
	.byte	2,35,36,13
	.byte	'certSigner',0,64
	.word	47209
	.byte	2,35,100,0,13
	.byte	'parameters',0,164,1
	.word	50658
	.byte	2,35,0,0,8
	.byte	'Secure_SignerInfoType',0,10,68,3
	.word	50625
	.byte	8
	.byte	'_iob_flag_t',0,11,75,25
	.word	233
	.byte	19,12,72,9,1,20
	.byte	'INTERNAL_FLS',0,0,20
	.byte	'EXTERNAL_FLS',0,1,0,8
	.byte	'FL_FlashType',0,12,76,2
	.word	50979
	.byte	19,12,78,9,1,20
	.byte	'NO_CRC',0,0,20
	.byte	'LAST_ADDR',0,1,20
	.byte	'HEAD_ADDR',0,2,0,8
	.byte	'FL_CrcAddrType',0,12,83,2
	.word	51036
	.byte	11,12,108,9,8,13
	.byte	'address',0,4
	.word	336
	.byte	2,35,0,13
	.byte	'length',0,4
	.word	336
	.byte	2,35,4,0,8
	.byte	'FL_SegmentInfoType',0,12,116,3
	.word	51098
	.byte	11,12,129,1,9,20,13
	.byte	'blkValid',0,1
	.word	187
	.byte	2,35,0,13
	.byte	'blkProgAttempt',0,2
	.word	233
	.byte	2,35,2,13
	.byte	'blkChecksum',0,4
	.word	336
	.byte	2,35,4,14,9
	.word	187
	.byte	15,8,0,13
	.byte	'fingerPrint',0,9
	.word	51233
	.byte	2,35,8,0,8
	.byte	'FL_blockInfoType',0,12,139,1,3
	.word	51164
.L331:
	.byte	14,15
	.word	187
	.byte	15,14,0
.L332:
	.byte	14,33
	.word	187
	.byte	15,32,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,58,15,59,15,57,15
	.byte	73,19,54,15,39,12,63,12,60,12,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,46,1,3,8,58,15,59,15,57,15,54
	.byte	15,39,12,63,12,60,12,0,0,6,15,0,73,19,0,0,7,59,0,3,8,0,0,8,22,0,3,8,58,15,59,15,57,15,73,19,0,0,9,21,0
	.byte	54,15,0,0,10,23,1,3,8,58,15,59,15,57,15,11,15,0,0,11,19,1,58,15,59,15,57,15,11,15,0,0,12,13,0,3,8,11,15
	.byte	73,19,13,15,12,15,56,9,0,0,13,13,0,3,8,11,15,73,19,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16
	.byte	19,1,3,8,58,15,59,15,57,15,11,15,0,0,17,23,1,58,15,59,15,57,15,11,15,0,0,18,53,0,73,19,0,0,19,4,1,58,15
	.byte	59,15,57,15,11,15,0,0,20,40,0,3,8,28,13,0,0,21,38,0,73,19,0,0,22,21,1,54,15,39,12,0,0,23,5,0,73,19,0,0
	.byte	0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L148:
	.word	.L426-.L425
.L425:
	.half	3
	.word	.L428-.L427
.L427:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure',0
	.byte	'E:\\soft\\Tasking\\ctc\\include\\',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash',0
	.byte	0
	.byte	'Dio.h',0,1,0,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'Mcal_TcLib.h',0,1,0,0
	.byte	'Port.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxGtm_regdef.h',0,1,0,0
	.byte	'Gtm.h',0,1,0,0
	.byte	'Secure_Types.h',0,2,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'FL.h',0,4,0,0,0
.L428:
.L426:
	.sdecl	'.debug_info',debug,cluster('CDD_IIC_COM_Delay')
	.sect	'.debug_info'
.L149:
	.word	288
	.half	3
	.word	.L150
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L152,.L151
	.byte	2
	.word	.L145
	.byte	3
	.byte	'CDD_IIC_COM_Delay',0,1,113,6,1,1,1
	.word	.L104,.L274,.L103
	.byte	4
	.byte	'Delaytime',0,1,113,31
	.word	.L275,.L276
	.byte	5
	.word	.L104,.L274
	.byte	6
	.byte	'u32t_Counter',0,1,116,9
	.word	.L275,.L277
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('CDD_IIC_COM_Delay')
	.sect	'.debug_abbrev'
.L150:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('CDD_IIC_COM_Delay')
	.sect	'.debug_line'
.L151:
	.word	.L430-.L429
.L429:
	.half	3
	.word	.L432-.L431
.L431:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L432:
	.byte	5,17,7,0,5,2
	.word	.L104
	.byte	3,245,0,1,5,30,9
	.half	.L334-.L104
	.byte	3,1,1,5,2,9
	.half	.L433-.L334
	.byte	1,5,71,7,9
	.half	.L3-.L433
	.byte	3,2,1,5,35,9
	.half	.L434-.L3
	.byte	1,5,92,9
	.half	.L435-.L434
	.byte	1,5,21,9
	.half	.L436-.L435
	.byte	1,5,34,9
	.half	.L437-.L436
	.byte	1,5,57,9
	.half	.L438-.L437
	.byte	1,5,69,9
	.half	.L439-.L438
	.byte	1,5,106,9
	.half	.L440-.L439
	.byte	1,5,1,7,9
	.half	.L441-.L440
	.byte	3,9,1,5,10,7,9
	.half	.L5-.L441
	.byte	3,125,1,5,31,9
	.half	.L442-.L5
	.byte	1,5,56,9
	.half	.L443-.L442
	.byte	1,5,1,7,9
	.half	.L444-.L443
	.byte	3,3,1,7,9
	.half	.L153-.L444
	.byte	0,1,1
.L430:
	.sdecl	'.debug_ranges',debug,cluster('CDD_IIC_COM_Delay')
	.sect	'.debug_ranges'
.L152:
	.word	-1,.L104,0,.L153-.L104,0,0
	.sdecl	'.debug_info',debug,cluster('SM_PwrInit')
	.sect	'.debug_info'
.L154:
	.word	238
	.half	3
	.word	.L155
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L157,.L156
	.byte	2
	.word	.L145
	.byte	3
	.byte	'SM_PwrInit',0,1,228,5,7
	.word	.L278
	.byte	1,1,1
	.word	.L136,.L279,.L135
	.byte	4
	.word	.L136,.L279
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SM_PwrInit')
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SM_PwrInit')
	.sect	'.debug_line'
.L156:
	.word	.L446-.L445
.L445:
	.half	3
	.word	.L448-.L447
.L447:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L448:
	.byte	5,9,7,0,5,2
	.word	.L136
	.byte	3,230,5,1,5,1,3,1,1,7,9
	.half	.L158-.L136
	.byte	0,1,1
.L446:
	.sdecl	'.debug_ranges',debug,cluster('SM_PwrInit')
	.sect	'.debug_ranges'
.L157:
	.word	-1,.L136,0,.L158-.L136,0,0
	.sdecl	'.debug_info',debug,cluster('SM_EyeQPwrMainFunc')
	.sect	'.debug_info'
.L159:
	.word	264
	.half	3
	.word	.L160
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L162,.L161
	.byte	2
	.word	.L145
	.byte	3
	.byte	'SM_EyeQPwrMainFunc',0,1,234,5,7
	.word	.L278
	.byte	1,1,1
	.word	.L138,.L280,.L137
	.byte	4
	.word	.L138,.L280
	.byte	5
	.byte	'ret',0,1,236,5,8
	.word	.L278,.L281
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SM_EyeQPwrMainFunc')
	.sect	'.debug_abbrev'
.L160:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SM_EyeQPwrMainFunc')
	.sect	'.debug_line'
.L161:
	.word	.L450-.L449
.L449:
	.half	3
	.word	.L452-.L451
.L451:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L452:
	.byte	5,23,7,0,5,2
	.word	.L138
	.byte	3,237,5,1,5,2,9
	.half	.L420-.L138
	.byte	3,1,1,5,23,7,9
	.half	.L453-.L420
	.byte	3,6,1,5,1,7,9
	.half	.L91-.L453
	.byte	3,7,1,7,9
	.half	.L163-.L91
	.byte	0,1,1
.L450:
	.sdecl	'.debug_ranges',debug,cluster('SM_EyeQPwrMainFunc')
	.sect	'.debug_ranges'
.L162:
	.word	-1,.L138,0,.L163-.L138,0,0
	.sdecl	'.debug_info',debug,cluster('SM_EyeQPwrDiagMainFunc')
	.sect	'.debug_info'
.L164:
	.word	268
	.half	3
	.word	.L165
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L167,.L166
	.byte	2
	.word	.L145
	.byte	3
	.byte	'SM_EyeQPwrDiagMainFunc',0,1,253,5,7
	.word	.L278
	.byte	1,1,1
	.word	.L140,.L282,.L139
	.byte	4
	.word	.L140,.L282
	.byte	5
	.byte	'ret',0,1,255,5,8
	.word	.L278,.L283
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SM_EyeQPwrDiagMainFunc')
	.sect	'.debug_abbrev'
.L165:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SM_EyeQPwrDiagMainFunc')
	.sect	'.debug_line'
.L166:
	.word	.L455-.L454
.L454:
	.half	3
	.word	.L457-.L456
.L456:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L457:
	.byte	5,41,7,0,5,2
	.word	.L140
	.byte	3,255,5,1,5,24,9
	.half	.L458-.L140
	.byte	1,5,18,9
	.half	.L459-.L458
	.byte	1,5,36,1,5,41,9
	.half	.L460-.L459
	.byte	1,5,2,9
	.half	.L421-.L460
	.byte	3,1,1,5,18,7,9
	.half	.L461-.L421
	.byte	3,5,1,5,36,1,5,52,9
	.half	.L462-.L461
	.byte	1,5,51,9
	.half	.L463-.L462
	.byte	1,5,2,9
	.half	.L464-.L463
	.byte	3,1,1,5,18,7,9
	.half	.L465-.L464
	.byte	3,5,1,5,36,1,5,51,9
	.half	.L466-.L465
	.byte	1,5,2,9
	.half	.L467-.L466
	.byte	3,1,1,5,41,7,9
	.half	.L468-.L467
	.byte	3,5,1,5,24,9
	.half	.L469-.L468
	.byte	1,5,18,9
	.half	.L470-.L469
	.byte	1,5,36,1,5,41,9
	.half	.L471-.L470
	.byte	1,5,2,9
	.half	.L472-.L471
	.byte	3,1,1,5,18,7,9
	.half	.L473-.L472
	.byte	3,5,1,5,36,1,5,52,9
	.half	.L474-.L473
	.byte	1,5,51,9
	.half	.L475-.L474
	.byte	1,5,2,9
	.half	.L476-.L475
	.byte	3,1,1,5,18,7,9
	.half	.L477-.L476
	.byte	3,5,1,5,36,1,5,51,9
	.half	.L478-.L477
	.byte	1,5,1,9
	.half	.L93-.L478
	.byte	3,8,1,7,9
	.half	.L168-.L93
	.byte	0,1,1
.L455:
	.sdecl	'.debug_ranges',debug,cluster('SM_EyeQPwrDiagMainFunc')
	.sect	'.debug_ranges'
.L167:
	.word	-1,.L140,0,.L168-.L140,0,0
	.sdecl	'.debug_info',debug,cluster('SM_EyeQPwrUpMainFunc')
	.sect	'.debug_info'
.L169:
	.word	266
	.half	3
	.word	.L170
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L172,.L171
	.byte	2
	.word	.L145
	.byte	3
	.byte	'SM_EyeQPwrUpMainFunc',0,1,167,6,7
	.word	.L278
	.byte	1,1,1
	.word	.L142,.L284,.L141
	.byte	4
	.word	.L142,.L284
	.byte	5
	.byte	'ret',0,1,169,6,8
	.word	.L278,.L285
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SM_EyeQPwrUpMainFunc')
	.sect	'.debug_abbrev'
.L170:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SM_EyeQPwrUpMainFunc')
	.sect	'.debug_line'
.L171:
	.word	.L480-.L479
.L479:
	.half	3
	.word	.L482-.L481
.L481:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L482:
	.byte	5,19,7,0,5,2
	.word	.L142
	.byte	3,170,6,1,5,28,9
	.half	.L483-.L142
	.byte	1,5,19,9
	.half	.L484-.L483
	.byte	3,1,1,5,29,9
	.half	.L485-.L484
	.byte	1,5,19,9
	.half	.L486-.L485
	.byte	3,1,1,5,29,9
	.half	.L487-.L486
	.byte	1,5,20,9
	.half	.L488-.L487
	.byte	3,1,1,5,24,9
	.half	.L489-.L488
	.byte	3,2,1,5,5,9
	.half	.L422-.L489
	.byte	1,5,2,9
	.half	.L423-.L422
	.byte	3,1,1,5,20,7,9
	.half	.L490-.L423
	.byte	3,6,1,5,19,9
	.half	.L491-.L490
	.byte	3,1,1,5,28,9
	.half	.L492-.L491
	.byte	1,5,19,9
	.half	.L493-.L492
	.byte	3,1,1,5,28,9
	.half	.L494-.L493
	.byte	1,5,19,9
	.half	.L495-.L494
	.byte	3,1,1,5,30,9
	.half	.L496-.L495
	.byte	1,5,20,9
	.half	.L497-.L496
	.byte	3,1,1,5,19,9
	.half	.L498-.L497
	.byte	3,1,1,5,28,9
	.half	.L499-.L498
	.byte	1,5,19,9
	.half	.L500-.L499
	.byte	3,1,1,5,28,9
	.half	.L501-.L500
	.byte	1,5,19,9
	.half	.L502-.L501
	.byte	3,1,1,5,32,9
	.half	.L503-.L502
	.byte	1,5,2,9
	.half	.L99-.L503
	.byte	3,1,1,5,1,3,1,1,7,9
	.half	.L173-.L99
	.byte	0,1,1
.L480:
	.sdecl	'.debug_ranges',debug,cluster('SM_EyeQPwrUpMainFunc')
	.sect	'.debug_ranges'
.L172:
	.word	-1,.L142,0,.L173-.L142,0,0
	.sdecl	'.debug_info',debug,cluster('SM_EyeQPwrDownMainFunc')
	.sect	'.debug_info'
.L174:
	.word	268
	.half	3
	.word	.L175
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L177,.L176
	.byte	2
	.word	.L145
	.byte	3
	.byte	'SM_EyeQPwrDownMainFunc',0,1,244,6,7
	.word	.L278
	.byte	1,1,1
	.word	.L144,.L286,.L143
	.byte	4
	.word	.L144,.L286
	.byte	5
	.byte	'ret',0,1,246,6,8
	.word	.L278,.L287
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SM_EyeQPwrDownMainFunc')
	.sect	'.debug_abbrev'
.L175:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SM_EyeQPwrDownMainFunc')
	.sect	'.debug_line'
.L176:
	.word	.L505-.L504
.L504:
	.half	3
	.word	.L507-.L506
.L506:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L507:
	.byte	5,19,7,0,5,2
	.word	.L144
	.byte	3,246,6,1,5,28,9
	.half	.L508-.L144
	.byte	1,5,19,9
	.half	.L509-.L508
	.byte	3,1,1,5,28,9
	.half	.L510-.L509
	.byte	1,5,24,9
	.half	.L511-.L510
	.byte	3,1,1,5,2,9
	.half	.L424-.L511
	.byte	3,1,1,5,24,7,9
	.half	.L512-.L424
	.byte	3,5,1,5,1,9
	.half	.L101-.L512
	.byte	3,7,1,7,9
	.half	.L178-.L101
	.byte	0,1,1
.L505:
	.sdecl	'.debug_ranges',debug,cluster('SM_EyeQPwrDownMainFunc')
	.sect	'.debug_ranges'
.L177:
	.word	-1,.L144,0,.L178-.L144,0,0
	.sdecl	'.debug_info',debug,cluster('CDD_IIC_Start')
	.sect	'.debug_info'
.L179:
	.word	257
	.half	3
	.word	.L180
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L182,.L181
	.byte	2
	.word	.L145
	.byte	3
	.byte	'CDD_IIC_Start',0,1,138,1,13,1,1
	.word	.L106,.L288,.L105
	.byte	4
	.byte	'channel',0,1,138,1,33
	.word	.L278,.L289
	.byte	5
	.word	.L106,.L288
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CDD_IIC_Start')
	.sect	'.debug_abbrev'
.L180:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CDD_IIC_Start')
	.sect	'.debug_line'
.L181:
	.word	.L514-.L513
.L513:
	.half	3
	.word	.L516-.L515
.L515:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L516:
	.byte	5,8,7,0,5,2
	.word	.L106
	.byte	3,142,1,1,7,9
	.half	.L517-.L106
	.byte	3,9,1,5,1,7,9
	.half	.L518-.L517
	.byte	3,14,1,5,21,7,9
	.half	.L6-.L518
	.byte	3,106,1,5,27,9
	.half	.L519-.L6
	.byte	1,5,22,9
	.half	.L335-.L519
	.byte	3,1,1,5,21,9
	.half	.L520-.L335
	.byte	3,1,1,5,4,3,5,1,5,21,9
	.half	.L7-.L520
	.byte	3,2,1,5,27,9
	.half	.L521-.L7
	.byte	1,5,22,9
	.half	.L336-.L521
	.byte	3,1,1,5,21,9
	.half	.L522-.L336
	.byte	3,1,1,5,27,9
	.half	.L9-.L522
	.byte	1,5,22,9
	.half	.L523-.L9
	.byte	3,1,1,5,27,9
	.half	.L524-.L523
	.byte	3,1,1,5,22,9
	.half	.L525-.L524
	.byte	3,1,1,5,1,9
	.half	.L183-.L525
	.byte	3,8,0,1,1
.L514:
	.sdecl	'.debug_ranges',debug,cluster('CDD_IIC_Start')
	.sect	'.debug_ranges'
.L182:
	.word	-1,.L106,0,.L183-.L106,0,0
	.sdecl	'.debug_info',debug,cluster('CDD_IIC_Stop')
	.sect	'.debug_info'
.L184:
	.word	256
	.half	3
	.word	.L185
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L187,.L186
	.byte	2
	.word	.L145
	.byte	3
	.byte	'CDD_IIC_Stop',0,1,174,1,13,1,1
	.word	.L108,.L290,.L107
	.byte	4
	.byte	'channel',0,1,174,1,32
	.word	.L278,.L291
	.byte	5
	.word	.L108,.L290
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('CDD_IIC_Stop')
	.sect	'.debug_abbrev'
.L185:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('CDD_IIC_Stop')
	.sect	'.debug_line'
.L186:
	.word	.L527-.L526
.L526:
	.half	3
	.word	.L529-.L528
.L528:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L529:
	.byte	5,8,7,0,5,2
	.word	.L108
	.byte	3,178,1,1,7,9
	.half	.L530-.L108
	.byte	3,9,1,5,1,7,9
	.half	.L531-.L530
	.byte	3,14,1,5,21,7,9
	.half	.L10-.L531
	.byte	3,106,1,5,27,9
	.half	.L532-.L10
	.byte	1,5,22,9
	.half	.L337-.L532
	.byte	3,1,1,5,21,9
	.half	.L533-.L337
	.byte	3,1,1,5,4,3,5,1,5,21,9
	.half	.L11-.L533
	.byte	3,2,1,5,27,9
	.half	.L534-.L11
	.byte	1,5,22,9
	.half	.L338-.L534
	.byte	3,1,1,5,21,9
	.half	.L535-.L338
	.byte	3,1,1,5,27,9
	.half	.L13-.L535
	.byte	1,5,22,9
	.half	.L536-.L13
	.byte	3,1,1,5,27,9
	.half	.L537-.L536
	.byte	3,1,1,5,22,9
	.half	.L538-.L537
	.byte	3,1,1,5,1,9
	.half	.L188-.L538
	.byte	3,8,0,1,1
.L527:
	.sdecl	'.debug_ranges',debug,cluster('CDD_IIC_Stop')
	.sect	'.debug_ranges'
.L187:
	.word	-1,.L108,0,.L188-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('sendAck')
	.sect	'.debug_info'
.L189:
	.word	252
	.half	3
	.word	.L190
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L192,.L191
	.byte	2
	.word	.L145
	.byte	3
	.byte	'sendAck',0,1,209,1,6,1,1,1
	.word	.L110,.L292,.L109
	.byte	4
	.byte	'channel',0,1,209,1,20
	.word	.L278,.L293
	.byte	5
	.word	.L110,.L292
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('sendAck')
	.sect	'.debug_abbrev'
.L190:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('sendAck')
	.sect	'.debug_line'
.L191:
	.word	.L540-.L539
.L539:
	.half	3
	.word	.L542-.L541
.L541:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L542:
	.byte	5,11,7,0,5,2
	.word	.L110
	.byte	3,213,1,1,7,9
	.half	.L543-.L110
	.byte	3,8,1,5,1,7,9
	.half	.L544-.L543
	.byte	3,13,1,5,24,7,9
	.half	.L14-.L544
	.byte	3,108,1,5,30,9
	.half	.L545-.L14
	.byte	1,5,25,9
	.half	.L339-.L545
	.byte	3,1,1,5,24,9
	.half	.L546-.L339
	.byte	3,1,1,5,7,3,4,1,5,24,9
	.half	.L15-.L546
	.byte	3,2,1,5,30,9
	.half	.L547-.L15
	.byte	1,5,25,9
	.half	.L340-.L547
	.byte	3,1,1,5,24,9
	.half	.L548-.L340
	.byte	3,1,1,5,30,9
	.half	.L17-.L548
	.byte	1,5,25,9
	.half	.L549-.L17
	.byte	3,1,1,5,30,9
	.half	.L550-.L549
	.byte	3,1,1,5,25,9
	.half	.L551-.L550
	.byte	3,1,1,5,1,9
	.half	.L193-.L551
	.byte	3,7,0,1,1
.L540:
	.sdecl	'.debug_ranges',debug,cluster('sendAck')
	.sect	'.debug_ranges'
.L192:
	.word	-1,.L110,0,.L193-.L110,0,0
	.sdecl	'.debug_info',debug,cluster('sendNoAck')
	.sect	'.debug_info'
.L194:
	.word	253
	.half	3
	.word	.L195
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L197,.L196
	.byte	2
	.word	.L145
	.byte	3
	.byte	'sendNoAck',0,1,241,1,13,1,1
	.word	.L112,.L294,.L111
	.byte	4
	.byte	'channel',0,1,241,1,29
	.word	.L278,.L295
	.byte	5
	.word	.L112,.L294
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('sendNoAck')
	.sect	'.debug_abbrev'
.L195:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('sendNoAck')
	.sect	'.debug_line'
.L196:
	.word	.L553-.L552
.L552:
	.half	3
	.word	.L555-.L554
.L554:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L555:
	.byte	5,11,7,0,5,2
	.word	.L112
	.byte	3,245,1,1,7,9
	.half	.L556-.L112
	.byte	3,8,1,5,1,7,9
	.half	.L557-.L556
	.byte	3,12,1,5,24,7,9
	.half	.L18-.L557
	.byte	3,109,1,5,30,9
	.half	.L558-.L18
	.byte	1,5,25,9
	.half	.L341-.L558
	.byte	3,1,1,5,24,9
	.half	.L559-.L341
	.byte	3,1,1,5,7,3,4,1,5,24,9
	.half	.L19-.L559
	.byte	3,2,1,5,30,9
	.half	.L560-.L19
	.byte	1,5,25,9
	.half	.L342-.L560
	.byte	3,1,1,5,24,9
	.half	.L561-.L342
	.byte	3,1,1,5,30,9
	.half	.L21-.L561
	.byte	1,5,25,9
	.half	.L562-.L21
	.byte	3,1,1,5,30,9
	.half	.L563-.L562
	.byte	3,1,1,5,25,9
	.half	.L564-.L563
	.byte	3,1,1,5,1,9
	.half	.L198-.L564
	.byte	3,6,0,1,1
.L553:
	.sdecl	'.debug_ranges',debug,cluster('sendNoAck')
	.sect	'.debug_ranges'
.L197:
	.word	-1,.L112,0,.L198-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('checkAck')
	.sect	'.debug_info'
.L199:
	.word	278
	.half	3
	.word	.L200
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L202,.L201
	.byte	2
	.word	.L145
	.byte	3
	.byte	'checkAck',0,1,146,2,14
	.word	.L278
	.byte	1,1
	.word	.L114,.L296,.L113
	.byte	4
	.byte	'channel',0,1,146,2,29
	.word	.L278,.L297
	.byte	5
	.word	.L114,.L296
	.byte	6
	.byte	'tempbit',0,1,149,2,8
	.word	.L278,.L298
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('checkAck')
	.sect	'.debug_abbrev'
.L200:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('checkAck')
	.sect	'.debug_line'
.L201:
	.word	.L566-.L565
.L565:
	.half	3
	.word	.L568-.L567
.L567:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L568:
	.byte	5,15,7,0,5,2
	.word	.L114
	.byte	3,148,2,1,5,11,9
	.half	.L344-.L114
	.byte	3,3,1,7,9
	.half	.L569-.L344
	.byte	3,14,1,7,9
	.half	.L570-.L569
	.byte	1,5,25,9
	.half	.L22-.L570
	.byte	3,115,1,9
	.half	.L343-.L22
	.byte	3,2,1,5,24,9
	.half	.L571-.L343
	.byte	3,1,1,5,30,9
	.half	.L572-.L571
	.byte	1,5,25,9
	.half	.L573-.L572
	.byte	3,1,1,5,33,9
	.half	.L574-.L573
	.byte	3,2,1,5,7,3,6,1,5,25,9
	.half	.L23-.L574
	.byte	3,3,1,9
	.half	.L346-.L23
	.byte	3,2,1,5,24,9
	.half	.L575-.L346
	.byte	3,2,1,5,30,9
	.half	.L576-.L575
	.byte	1,5,28,9
	.half	.L577-.L576
	.byte	3,1,1,5,36,9
	.half	.L578-.L577
	.byte	3,2,1,5,18,9
	.half	.L345-.L578
	.byte	1,5,28,9
	.half	.L348-.L345
	.byte	3,1,1,5,30,9
	.half	.L347-.L348
	.byte	3,1,1,5,25,9
	.half	.L579-.L347
	.byte	3,1,1,5,5,9
	.half	.L24-.L579
	.byte	3,13,1,5,1,3,9,1,7,9
	.half	.L203-.L24
	.byte	0,1,1
.L566:
	.sdecl	'.debug_ranges',debug,cluster('checkAck')
	.sect	'.debug_ranges'
.L202:
	.word	-1,.L114,0,.L203-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('writeByte')
	.sect	'.debug_info'
.L204:
	.word	310
	.half	3
	.word	.L205
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L207,.L206
	.byte	2
	.word	.L145
	.byte	3
	.byte	'writeByte',0,1,210,2,14
	.word	.L278
	.byte	1,1
	.word	.L116,.L299,.L115
	.byte	4
	.byte	'channel',0,1,210,2,30
	.word	.L278,.L300
	.byte	4
	.byte	'datum',0,1,210,2,44
	.word	.L278,.L301
	.byte	5
	.word	.L302
	.byte	6
	.byte	'ret',0,1,213,2,8
	.word	.L278,.L303
	.byte	6
	.byte	'bitCnt',0,1,214,2,8
	.word	.L278,.L304
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('writeByte')
	.sect	'.debug_abbrev'
.L205:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('writeByte')
	.sect	'.debug_line'
.L206:
	.word	.L581-.L580
.L580:
	.half	3
	.word	.L583-.L582
.L582:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L583:
	.byte	5,14,7,0,5,2
	.word	.L116
	.byte	3,209,2,1,5,11,9
	.half	.L349-.L116
	.byte	3,3,1,5,8,9
	.half	.L351-.L349
	.byte	3,4,1,7,9
	.half	.L584-.L351
	.byte	3,25,1,7,9
	.half	.L585-.L584
	.byte	1,5,16,9
	.half	.L27-.L585
	.byte	3,104,1,5,26,1,5,28,9
	.half	.L30-.L27
	.byte	3,2,1,5,23,9
	.half	.L586-.L30
	.byte	3,1,1,5,5,9
	.half	.L350-.L586
	.byte	3,1,1,5,16,9
	.half	.L352-.L350
	.byte	3,124,1,5,29,3,10,1,5,23,9
	.half	.L587-.L352
	.byte	3,2,1,5,28,9
	.half	.L588-.L587
	.byte	3,1,1,5,23,9
	.half	.L589-.L588
	.byte	3,1,1,5,10,9
	.half	.L590-.L589
	.byte	3,1,1,5,26,9
	.half	.L353-.L590
	.byte	3,113,1,5,27,7,9
	.half	.L355-.L353
	.byte	3,18,1,5,4,9
	.half	.L591-.L355
	.byte	3,4,1,5,16,9
	.half	.L28-.L591
	.byte	3,3,1,5,26,1,5,28,9
	.half	.L34-.L28
	.byte	3,2,1,5,23,9
	.half	.L592-.L34
	.byte	3,1,1,5,5,9
	.half	.L354-.L592
	.byte	3,1,1,5,16,9
	.half	.L356-.L354
	.byte	3,124,1,5,29,3,10,1,5,23,9
	.half	.L593-.L356
	.byte	3,2,1,5,28,9
	.half	.L594-.L593
	.byte	3,1,1,5,23,9
	.half	.L595-.L594
	.byte	3,1,1,5,10,9
	.half	.L596-.L595
	.byte	3,1,1,5,26,9
	.half	.L357-.L596
	.byte	3,113,1,5,27,7,9
	.half	.L358-.L357
	.byte	3,17,1,5,4,9
	.half	.L597-.L358
	.byte	3,3,1,5,7,9
	.half	.L29-.L597
	.byte	3,2,1,5,2,9
	.half	.L33-.L29
	.byte	3,5,1,5,1,3,1,1,7,9
	.half	.L208-.L33
	.byte	0,1,1
.L581:
	.sdecl	'.debug_ranges',debug,cluster('writeByte')
	.sect	'.debug_ranges'
.L207:
	.word	-1,.L116,0,.L208-.L116,0,0
.L302:
	.word	-1,.L116,0,.L299-.L116,-1,.L118,0,.L243-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('readByte')
	.sect	'.debug_info'
.L209:
	.word	312
	.half	3
	.word	.L210
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L212,.L211
	.byte	2
	.word	.L145
	.byte	3
	.byte	'readByte',0,1,151,3,14
	.word	.L278
	.byte	1,1
	.word	.L120,.L305,.L119
	.byte	4
	.byte	'channel',0,1,151,3,29
	.word	.L278,.L306
	.byte	5
	.word	.L307
	.byte	6
	.byte	'tempbit',0,1,154,3,8
	.word	.L278,.L308
	.byte	6
	.byte	'temp',0,1,155,3,8
	.word	.L278,.L309
	.byte	6
	.byte	'bitCnt',0,1,156,3,8
	.word	.L278,.L310
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('readByte')
	.sect	'.debug_abbrev'
.L210:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('readByte')
	.sect	'.debug_line'
.L211:
	.word	.L599-.L598
.L598:
	.half	3
	.word	.L601-.L600
.L600:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L601:
	.byte	5,13,7,0,5,2
	.word	.L120
	.byte	3,154,3,1,5,8,9
	.half	.L360-.L120
	.byte	3,5,1,7,9
	.half	.L602-.L360
	.byte	3,34,1,7,9
	.half	.L603-.L602
	.byte	1,5,27,9
	.half	.L39-.L603
	.byte	3,96,1,5,21,9
	.half	.L604-.L39
	.byte	1,5,27,1,5,22,9
	.half	.L359-.L604
	.byte	3,1,1,9
	.half	.L605-.L359
	.byte	3,4,1,5,14,9
	.half	.L606-.L605
	.byte	3,1,1,5,26,1,5,28,9
	.half	.L42-.L606
	.byte	3,3,1,5,36,9
	.half	.L607-.L42
	.byte	3,119,1,5,28,3,9,1,5,23,9
	.half	.L608-.L607
	.byte	3,1,1,5,21,9
	.half	.L362-.L608
	.byte	3,118,1,5,31,3,11,1,5,5,9
	.half	.L361-.L362
	.byte	3,1,1,5,36,9
	.half	.L609-.L361
	.byte	3,116,1,5,28,3,22,1,5,23,9
	.half	.L363-.L609
	.byte	3,1,1,5,5,9
	.half	.L364-.L363
	.byte	3,2,1,5,26,9
	.half	.L610-.L364
	.byte	3,109,1,5,4,7,9
	.half	.L366-.L610
	.byte	3,25,1,5,27,9
	.half	.L40-.L366
	.byte	3,3,1,5,21,9
	.half	.L611-.L40
	.byte	1,5,27,1,5,22,9
	.half	.L368-.L611
	.byte	3,1,1,9
	.half	.L612-.L368
	.byte	3,3,1,5,14,9
	.half	.L613-.L612
	.byte	3,1,1,5,26,1,5,28,9
	.half	.L47-.L613
	.byte	3,2,1,5,36,9
	.half	.L614-.L47
	.byte	3,121,1,5,28,3,7,1,5,23,9
	.half	.L615-.L614
	.byte	3,1,1,5,21,9
	.half	.L369-.L615
	.byte	3,120,1,5,31,3,9,1,5,5,9
	.half	.L367-.L369
	.byte	3,1,1,5,36,9
	.half	.L370-.L367
	.byte	3,118,1,5,28,3,20,1,5,23,9
	.half	.L372-.L370
	.byte	3,1,1,5,5,9
	.half	.L371-.L372
	.byte	3,2,1,5,26,9
	.half	.L373-.L371
	.byte	3,110,1,5,2,7,9
	.half	.L41-.L373
	.byte	3,32,1,5,1,3,1,1,7,9
	.half	.L213-.L41
	.byte	0,1,1
.L599:
	.sdecl	'.debug_ranges',debug,cluster('readByte')
	.sect	'.debug_ranges'
.L212:
	.word	-1,.L120,0,.L213-.L120,0,0
.L307:
	.word	-1,.L120,0,.L305-.L120,-1,.L122,0,.L248-.L122,-1,.L124,0,.L238-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('writeOneByte')
	.sect	'.debug_info'
.L214:
	.word	336
	.half	3
	.word	.L215
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L217,.L216
	.byte	2
	.word	.L145
	.byte	3
	.byte	'writeOneByte',0,1,244,3,14
	.word	.L278
	.byte	1,1
	.word	.L126,.L311,.L125
	.byte	4
	.byte	'channel',0,1,244,3,33
	.word	.L278,.L312
	.byte	4
	.byte	'addr',0,1,244,3,47
	.word	.L278,.L313
	.byte	4
	.byte	'datum',0,1,244,3,59
	.word	.L278,.L314
	.byte	5
	.word	.L126,.L311
	.byte	6
	.byte	'ret',0,1,247,3,8
	.word	.L278,.L315
	.byte	6
	.byte	'tempbit',0,1,248,3,8
	.word	.L278,.L316
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('writeOneByte')
	.sect	'.debug_abbrev'
.L215:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('writeOneByte')
	.sect	'.debug_line'
.L216:
	.word	.L617-.L616
.L616:
	.half	3
	.word	.L619-.L618
.L618:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L619:
	.byte	5,14,7,0,5,2
	.word	.L126
	.byte	3,243,3,1,5,11,9
	.half	.L376-.L126
	.byte	3,3,1,5,8,9
	.half	.L377-.L376
	.byte	3,5,1,7,9
	.half	.L620-.L377
	.byte	3,28,1,7,9
	.half	.L621-.L620
	.byte	1,5,18,9
	.half	.L52-.L621
	.byte	3,102,1,5,14,9
	.half	.L374-.L52
	.byte	3,1,1,5,20,9
	.half	.L622-.L374
	.byte	1,5,23,9
	.half	.L623-.L622
	.byte	3,1,1,5,4,9
	.half	.L378-.L623
	.byte	3,1,1,5,8,7,9
	.half	.L60-.L378
	.byte	3,2,1,5,5,9
	.half	.L62-.L60
	.byte	3,1,1,5,1,3,55,1,5,14,7,9
	.half	.L55-.L62
	.byte	3,75,1,5,20,9
	.half	.L624-.L55
	.byte	1,5,23,9
	.half	.L379-.L624
	.byte	3,1,1,5,4,9
	.half	.L380-.L379
	.byte	3,1,1,5,14,7,9
	.half	.L625-.L380
	.byte	3,5,1,5,20,9
	.half	.L626-.L625
	.byte	1,5,23,9
	.half	.L381-.L626
	.byte	3,1,1,5,4,9
	.half	.L382-.L381
	.byte	3,1,1,5,17,7,9
	.half	.L627-.L382
	.byte	3,5,1,5,22,9
	.half	.L383-.L627
	.byte	3,1,1,5,4,9
	.half	.L628-.L383
	.byte	3,2,1,5,18,9
	.half	.L53-.L628
	.byte	3,3,1,5,14,9
	.half	.L384-.L53
	.byte	3,1,1,5,20,9
	.half	.L629-.L384
	.byte	1,5,23,9
	.half	.L630-.L629
	.byte	3,1,1,5,4,9
	.half	.L385-.L630
	.byte	3,1,1,5,14,7,9
	.half	.L631-.L385
	.byte	3,5,1,5,20,9
	.half	.L632-.L631
	.byte	1,5,23,9
	.half	.L386-.L632
	.byte	3,1,1,5,4,9
	.half	.L387-.L386
	.byte	3,1,1,5,14,7,9
	.half	.L633-.L387
	.byte	3,5,1,5,20,9
	.half	.L634-.L633
	.byte	1,5,23,9
	.half	.L388-.L634
	.byte	3,1,1,5,4,9
	.half	.L389-.L388
	.byte	3,1,1,5,17,7,9
	.half	.L635-.L389
	.byte	3,5,1,5,22,9
	.half	.L390-.L635
	.byte	3,1,1,5,4,9
	.half	.L636-.L390
	.byte	3,2,1,5,1,9
	.half	.L218-.L636
	.byte	3,8,0,1,1
.L617:
	.sdecl	'.debug_ranges',debug,cluster('writeOneByte')
	.sect	'.debug_ranges'
.L217:
	.word	-1,.L126,0,.L218-.L126,0,0
	.sdecl	'.debug_info',debug,cluster('readOneByte')
	.sect	'.debug_info'
.L219:
	.word	335
	.half	3
	.word	.L220
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L222,.L221
	.byte	2
	.word	.L145
	.byte	3
	.byte	'readOneByte',0,1,195,4,14
	.word	.L278
	.byte	1,1
	.word	.L128,.L317,.L127
	.byte	4
	.byte	'channel',0,1,195,4,32
	.word	.L278,.L318
	.byte	4
	.byte	'addr',0,1,195,4,46
	.word	.L278,.L319
	.byte	4
	.byte	'datum',0,1,195,4,57
	.word	.L320,.L321
	.byte	5
	.word	.L128,.L317
	.byte	6
	.byte	'ret',0,1,198,4,9
	.word	.L278,.L322
	.byte	6
	.byte	'tempbit',0,1,199,4,8
	.word	.L278,.L323
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('readOneByte')
	.sect	'.debug_abbrev'
.L220:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('readOneByte')
	.sect	'.debug_line'
.L221:
	.word	.L638-.L637
.L637:
	.half	3
	.word	.L640-.L639
.L639:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L640:
	.byte	5,14,7,0,5,2
	.word	.L128
	.byte	3,194,4,1,5,12,9
	.half	.L392-.L128
	.byte	3,3,1,5,8,9
	.half	.L393-.L392
	.byte	3,5,1,7,9
	.half	.L641-.L393
	.byte	3,32,1,7,9
	.half	.L642-.L641
	.byte	1,5,18,9
	.half	.L64-.L642
	.byte	3,98,1,5,14,9
	.half	.L391-.L64
	.byte	3,1,1,5,20,9
	.half	.L643-.L391
	.byte	1,5,23,9
	.half	.L644-.L643
	.byte	3,1,1,5,4,9
	.half	.L394-.L644
	.byte	3,1,1,5,8,7,9
	.half	.L72-.L394
	.byte	3,2,1,5,5,9
	.half	.L74-.L72
	.byte	3,1,1,5,1,3,193,0,1,5,14,7,9
	.half	.L67-.L74
	.byte	3,65,1,5,20,9
	.half	.L645-.L67
	.byte	1,5,23,9
	.half	.L395-.L645
	.byte	3,1,1,5,4,9
	.half	.L396-.L395
	.byte	3,1,1,5,18,7,9
	.half	.L646-.L396
	.byte	3,6,1,5,14,9
	.half	.L397-.L646
	.byte	3,1,1,5,20,9
	.half	.L647-.L397
	.byte	1,5,23,9
	.half	.L648-.L647
	.byte	3,1,1,5,4,9
	.half	.L398-.L648
	.byte	3,1,1,5,22,7,9
	.half	.L649-.L398
	.byte	3,5,1,5,11,9
	.half	.L399-.L649
	.byte	1,5,14,9
	.half	.L650-.L399
	.byte	3,1,1,5,17,9
	.half	.L651-.L650
	.byte	3,1,1,5,4,9
	.half	.L652-.L651
	.byte	3,2,1,5,18,9
	.half	.L65-.L652
	.byte	3,4,1,5,14,9
	.half	.L400-.L65
	.byte	3,1,1,5,20,9
	.half	.L653-.L400
	.byte	1,5,23,9
	.half	.L654-.L653
	.byte	3,1,1,5,4,9
	.half	.L401-.L654
	.byte	3,1,1,5,14,7,9
	.half	.L655-.L401
	.byte	3,5,1,5,20,9
	.half	.L656-.L655
	.byte	1,5,23,9
	.half	.L402-.L656
	.byte	3,1,1,5,4,9
	.half	.L403-.L402
	.byte	3,1,1,5,18,7,9
	.half	.L657-.L403
	.byte	3,6,1,5,14,9
	.half	.L404-.L657
	.byte	3,1,1,5,20,9
	.half	.L658-.L404
	.byte	1,5,23,9
	.half	.L659-.L658
	.byte	3,1,1,5,4,9
	.half	.L405-.L659
	.byte	3,3,1,5,22,7,9
	.half	.L660-.L405
	.byte	3,6,1,5,11,9
	.half	.L406-.L660
	.byte	1,5,14,9
	.half	.L661-.L406
	.byte	3,1,1,5,17,9
	.half	.L662-.L661
	.byte	3,2,1,5,4,9
	.half	.L663-.L662
	.byte	3,2,1,5,1,9
	.half	.L223-.L663
	.byte	3,7,0,1,1
.L638:
	.sdecl	'.debug_ranges',debug,cluster('readOneByte')
	.sect	'.debug_ranges'
.L222:
	.word	-1,.L128,0,.L223-.L128,0,0
	.sdecl	'.debug_info',debug,cluster('SM_EyeQPwrRegSet')
	.sect	'.debug_info'
.L224:
	.word	280
	.half	3
	.word	.L225
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L227,.L226
	.byte	2
	.word	.L145
	.byte	3
	.byte	'SM_EyeQPwrRegSet',0,1,149,5,14
	.word	.L278
	.byte	1,1
	.word	.L130,.L324,.L129
	.byte	4
	.word	.L325
	.byte	5
	.byte	'IIC_Index',0,1,152,5,8
	.word	.L278,.L326
	.byte	5
	.byte	'ret',0,1,153,5,8
	.word	.L278,.L327
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SM_EyeQPwrRegSet')
	.sect	'.debug_abbrev'
.L225:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SM_EyeQPwrRegSet')
	.sect	'.debug_line'
.L226:
	.word	.L665-.L664
.L664:
	.half	3
	.word	.L667-.L666
.L666:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L667:
	.byte	5,50,7,0,5,2
	.word	.L130
	.byte	3,157,5,1,5,26,9
	.half	.L668-.L130
	.byte	1,5,44,9
	.half	.L669-.L668
	.byte	3,126,1,5,20,9
	.half	.L76-.L669
	.byte	3,2,1,5,38,1,5,62,9
	.half	.L670-.L76
	.byte	1,5,6,9
	.half	.L407-.L670
	.byte	1,5,3,9
	.half	.L409-.L407
	.byte	3,1,1,5,21,7,9
	.half	.L671-.L409
	.byte	3,6,1,5,54,9
	.half	.L408-.L671
	.byte	3,119,1,5,44,9
	.half	.L672-.L408
	.byte	1,5,50,7,9
	.half	.L673-.L672
	.byte	3,15,1,5,26,9
	.half	.L674-.L673
	.byte	1,5,44,9
	.half	.L675-.L674
	.byte	3,126,1,5,20,9
	.half	.L78-.L675
	.byte	3,2,1,5,38,1,5,62,9
	.half	.L676-.L78
	.byte	1,5,6,9
	.half	.L410-.L676
	.byte	1,5,3,9
	.half	.L412-.L410
	.byte	3,1,1,5,21,7,9
	.half	.L677-.L412
	.byte	3,6,1,5,54,9
	.half	.L411-.L677
	.byte	3,119,1,5,44,9
	.half	.L678-.L411
	.byte	1,5,2,7,9
	.half	.L77-.L678
	.byte	3,11,1,5,1,3,1,1,7,9
	.half	.L228-.L77
	.byte	0,1,1
.L665:
	.sdecl	'.debug_ranges',debug,cluster('SM_EyeQPwrRegSet')
	.sect	'.debug_ranges'
.L227:
	.word	-1,.L130,0,.L228-.L130,0,0
.L325:
	.word	-1,.L130,0,.L324-.L130,-1,.L132,0,.L253-.L132,0,0
	.sdecl	'.debug_info',debug,cluster('SM_EyeQPwrRegRead')
	.sect	'.debug_info'
.L229:
	.word	285
	.half	3
	.word	.L230
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L232,.L231
	.byte	2
	.word	.L145
	.byte	3
	.byte	'SM_EyeQPwrRegRead',0,1,182,5,14
	.word	.L278
	.byte	1,1
	.word	.L134,.L328,.L133
	.byte	4
	.word	.L134,.L328
	.byte	5
	.byte	'IIC_Index',0,1,184,5,8
	.word	.L278,.L329
	.byte	5
	.byte	'ret',0,1,185,5,8
	.word	.L278,.L330
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('SM_EyeQPwrRegRead')
	.sect	'.debug_abbrev'
.L230:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('SM_EyeQPwrRegRead')
	.sect	'.debug_line'
.L231:
	.word	.L680-.L679
.L679:
	.half	3
	.word	.L682-.L681
.L681:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L682:
	.byte	5,15,7,0,5,2
	.word	.L134
	.byte	3,187,5,1,5,42,1,5,50,9
	.half	.L82-.L134
	.byte	3,2,1,5,25,9
	.half	.L683-.L82
	.byte	1,5,50,9
	.half	.L684-.L683
	.byte	1,5,64,9
	.half	.L685-.L684
	.byte	1,5,25,9
	.half	.L686-.L685
	.byte	1,5,37,9
	.half	.L687-.L686
	.byte	1,5,19,9
	.half	.L688-.L687
	.byte	1,5,64,1,5,37,9
	.half	.L689-.L688
	.byte	1,5,64,9
	.half	.L690-.L689
	.byte	1,5,6,9
	.half	.L413-.L690
	.byte	1,5,3,9
	.half	.L414-.L413
	.byte	3,1,1,5,20,7,9
	.half	.L691-.L414
	.byte	3,6,1,5,33,9
	.half	.L692-.L691
	.byte	1,5,45,9
	.half	.L693-.L692
	.byte	1,5,3,9
	.half	.L415-.L693
	.byte	1,5,7,7,9
	.half	.L85-.L415
	.byte	3,4,1,5,4,9
	.half	.L87-.L85
	.byte	3,1,1,5,1,3,25,1,5,21,7,9
	.half	.L84-.L87
	.byte	3,105,1,5,52,9
	.half	.L416-.L84
	.byte	3,112,1,5,42,9
	.half	.L81-.L416
	.byte	1,5,50,7,9
	.half	.L694-.L81
	.byte	3,22,1,5,25,9
	.half	.L695-.L694
	.byte	1,5,33,9
	.half	.L696-.L695
	.byte	3,7,1,5,42,9
	.half	.L697-.L696
	.byte	3,119,1,5,19,9
	.half	.L89-.L697
	.byte	3,2,1,5,37,1,5,64,9
	.half	.L698-.L89
	.byte	1,5,6,9
	.half	.L417-.L698
	.byte	1,5,3,9
	.half	.L419-.L417
	.byte	3,1,1,5,20,7,9
	.half	.L699-.L419
	.byte	3,6,1,5,45,9
	.half	.L700-.L699
	.byte	1,5,3,9
	.half	.L701-.L700
	.byte	1,5,21,7,9
	.half	.L702-.L701
	.byte	3,7,1,5,52,9
	.half	.L418-.L702
	.byte	3,112,1,5,42,9
	.half	.L703-.L418
	.byte	1,5,2,7,9
	.half	.L704-.L703
	.byte	3,18,1,5,1,9
	.half	.L233-.L704
	.byte	3,1,0,1,1
.L680:
	.sdecl	'.debug_ranges',debug,cluster('SM_EyeQPwrRegRead')
	.sect	'.debug_ranges'
.L232:
	.word	-1,.L134,0,.L233-.L134,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L234:
	.word	222
	.half	3
	.word	.L235
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L237,.L236
	.byte	2
	.word	.L145
	.byte	3
	.byte	'.cocofun_1',0,1,151,3,14,1
	.word	.L124,.L238,.L123
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L235:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L236:
	.word	.L706-.L705
.L705:
	.half	3
	.word	.L708-.L707
.L707:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L708:
	.byte	5,5,7,0,5,2
	.word	.L124
	.byte	3,173,3,1,5,11,7,9
	.half	.L709-.L124
	.byte	3,2,1,5,19,1,5,11,9
	.half	.L43-.L709
	.byte	3,5,1,5,28,9
	.half	.L44-.L43
	.byte	3,3,1,9
	.half	.L238-.L44
	.byte	0,1,1,5,5,0,5,2
	.word	.L124
	.byte	3,205,3,1,5,11,7,9
	.half	.L709-.L124
	.byte	3,2,1,5,19,1,5,11,9
	.half	.L43-.L709
	.byte	3,5,1,5,28,9
	.half	.L44-.L43
	.byte	3,3,1,3,96,1,7,9
	.half	.L238-.L44
	.byte	0,1,1
.L706:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L237:
	.word	-1,.L124,0,.L238-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L239:
	.word	222
	.half	3
	.word	.L240
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L242,.L241
	.byte	2
	.word	.L145
	.byte	3
	.byte	'.cocofun_2',0,1,210,2,14,1
	.word	.L118,.L243,.L117
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L240:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L241:
	.word	.L711-.L710
.L710:
	.half	3
	.word	.L713-.L712
.L712:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L713:
	.byte	5,5,7,0,5,2
	.word	.L118
	.byte	3,221,2,1,5,29,7,9
	.half	.L714-.L118
	.byte	3,2,1,5,38,1,5,29,9
	.half	.L31-.L714
	.byte	3,4,1,5,5,9
	.half	.L32-.L31
	.byte	3,126,1,7,9
	.half	.L243-.L32
	.byte	0,1,1,5,5,0,5,2
	.word	.L118
	.byte	3,246,2,1,5,29,7,9
	.half	.L714-.L118
	.byte	3,2,1,5,38,1,5,29,9
	.half	.L31-.L714
	.byte	3,4,1,5,5,9
	.half	.L32-.L31
	.byte	3,101,1,7,9
	.half	.L243-.L32
	.byte	0,1,1
.L711:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L242:
	.word	-1,.L118,0,.L243-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L244:
	.word	222
	.half	3
	.word	.L245
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L247,.L246
	.byte	2
	.word	.L145
	.byte	3
	.byte	'.cocofun_3',0,1,151,3,14,1
	.word	.L122,.L248,.L121
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L245:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L246:
	.word	.L716-.L715
.L715:
	.half	3
	.word	.L718-.L717
.L717:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L718:
	.byte	5,5,7,0,5,2
	.word	.L122
	.byte	3,186,3,1,5,11,7,9
	.half	.L719-.L122
	.byte	3,2,1,5,34,9
	.half	.L45-.L719
	.byte	3,107,1,9
	.half	.L248-.L45
	.byte	0,1,1,5,5,0,5,2
	.word	.L122
	.byte	3,218,3,1,5,11,7,9
	.half	.L719-.L122
	.byte	3,2,1,5,34,9
	.half	.L45-.L719
	.byte	3,108,1,3,95,1,7,9
	.half	.L248-.L45
	.byte	0,1,1
.L716:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L247:
	.word	-1,.L122,0,.L248-.L122,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_4')
	.sect	'.debug_info'
.L249:
	.word	222
	.half	3
	.word	.L250
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L252,.L251
	.byte	2
	.word	.L145
	.byte	3
	.byte	'.cocofun_4',0,1,149,5,14,1
	.word	.L132,.L253,.L131
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_4')
	.sect	'.debug_abbrev'
.L250:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_4')
	.sect	'.debug_line'
.L251:
	.word	.L721-.L720
.L720:
	.half	3
	.word	.L723-.L722
.L722:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0,0,0,0,0
.L723:
	.byte	5,26,7,0,5,2
	.word	.L132
	.byte	3,170,5,1,9
	.half	.L253-.L132
	.byte	0,1,1,5,25,0,5,2
	.word	.L132
	.byte	3,209,5,1,5,26,9
	.half	.L724-.L132
	.byte	3,89,1,7,9
	.half	.L253-.L724
	.byte	0,1,1,5,24,0,5,2
	.word	.L132
	.byte	3,145,6,1,5,26,9
	.half	.L724-.L132
	.byte	3,153,127,1,7,9
	.half	.L253-.L724
	.byte	0,1,1
.L721:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_4')
	.sect	'.debug_ranges'
.L252:
	.word	-1,.L132,0,.L253-.L132,0,0
	.sdecl	'.debug_info',debug,cluster('SM_EthH_Flag')
	.sect	'.debug_info'
.L254:
	.word	213
	.half	3
	.word	.L255
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'SM_EthH_Flag',0,2,76,7
	.word	.L278
	.byte	1,5,3
	.word	SM_EthH_Flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('SM_EthH_Flag')
	.sect	'.debug_abbrev'
.L255:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('EyeQPower_Flag')
	.sect	'.debug_info'
.L256:
	.word	215
	.half	3
	.word	.L257
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'EyeQPower_Flag',0,2,77,7
	.word	.L278
	.byte	1,5,3
	.word	EyeQPower_Flag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('EyeQPower_Flag')
	.sect	'.debug_abbrev'
.L257:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IIC_A_Register')
	.sect	'.debug_info'
.L258:
	.word	214
	.half	3
	.word	.L259
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'IIC_A_Register',0,2,79,15
	.word	.L331
	.byte	5,3
	.word	IIC_A_Register
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IIC_A_Register')
	.sect	'.debug_abbrev'
.L259:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('IIC_B_Register')
	.sect	'.debug_info'
.L260:
	.word	214
	.half	3
	.word	.L261
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'IIC_B_Register',0,2,80,15
	.word	.L332
	.byte	5,3
	.word	IIC_B_Register
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IIC_B_Register')
	.sect	'.debug_abbrev'
.L261:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('IIC_A_RegVal')
	.sect	'.debug_info'
.L262:
	.word	213
	.half	3
	.word	.L263
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'IIC_A_RegVal',0,2,81,8
	.word	.L331
	.byte	1,5,3
	.word	IIC_A_RegVal
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IIC_A_RegVal')
	.sect	'.debug_abbrev'
.L263:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IIC_B_RegVal')
	.sect	'.debug_info'
.L264:
	.word	213
	.half	3
	.word	.L265
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'IIC_B_RegVal',0,2,85,8
	.word	.L332
	.byte	1,5,3
	.word	IIC_B_RegVal
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IIC_B_RegVal')
	.sect	'.debug_abbrev'
.L265:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IIC_A_RegAdd')
	.sect	'.debug_info'
.L266:
	.word	213
	.half	3
	.word	.L267
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'IIC_A_RegAdd',0,2,92,8
	.word	.L331
	.byte	1,5,3
	.word	IIC_A_RegAdd
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IIC_A_RegAdd')
	.sect	'.debug_abbrev'
.L267:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IIC_B_RegAdd')
	.sect	'.debug_info'
.L268:
	.word	213
	.half	3
	.word	.L269
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'IIC_B_RegAdd',0,2,96,8
	.word	.L332
	.byte	1,5,3
	.word	IIC_B_RegAdd
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IIC_B_RegAdd')
	.sect	'.debug_abbrev'
.L269:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IIC_A_diag')
	.sect	'.debug_info'
.L270:
	.word	211
	.half	3
	.word	.L271
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'IIC_A_diag',0,2,103,7
	.word	.L333
	.byte	1,5,3
	.word	IIC_A_diag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IIC_A_diag')
	.sect	'.debug_abbrev'
.L271:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IIC_B_diag')
	.sect	'.debug_info'
.L272:
	.word	211
	.half	3
	.word	.L273
	.byte	4,1
	.byte	'..\\Src_file\\CDD_EyeQ_PowerCtrl.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L145
	.byte	3
	.byte	'IIC_B_diag',0,2,104,7
	.word	.L333
	.byte	1,5,3
	.word	IIC_B_diag
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IIC_B_diag')
	.sect	'.debug_abbrev'
.L273:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L123:
	.word	-1,.L124,0,.L238-.L124
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L118,0,.L243-.L118
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L121:
	.word	-1,.L122,0,.L248-.L122
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_4')
	.sect	'.debug_loc'
.L131:
	.word	-1,.L132,0,.L253-.L132
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CDD_IIC_COM_Delay')
	.sect	'.debug_loc'
.L103:
	.word	-1,.L104,0,.L274-.L104
	.half	2
	.byte	138,0
	.word	0,0
.L276:
	.word	-1,.L104,0,.L274-.L104
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L277:
	.word	-1,.L104,.L334-.L104,.L274-.L104
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CDD_IIC_Start')
	.sect	'.debug_loc'
.L105:
	.word	-1,.L106,0,.L288-.L106
	.half	2
	.byte	138,0
	.word	0,0
.L289:
	.word	-1,.L106,0,.L335-.L106
	.half	5
	.byte	144,34,157,32,0
	.word	.L7-.L106,.L336-.L106
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('CDD_IIC_Stop')
	.sect	'.debug_loc'
.L107:
	.word	-1,.L108,0,.L290-.L108
	.half	2
	.byte	138,0
	.word	0,0
.L291:
	.word	-1,.L108,0,.L337-.L108
	.half	5
	.byte	144,34,157,32,0
	.word	.L11-.L108,.L338-.L108
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SM_EyeQPwrDiagMainFunc')
	.sect	'.debug_loc'
.L139:
	.word	-1,.L140,0,.L282-.L140
	.half	2
	.byte	138,0
	.word	0,0
.L283:
	.word	-1,.L140,.L132-.L140,.L253-.L140
	.half	5
	.byte	144,33,157,32,0
	.word	.L421-.L140,.L282-.L140
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SM_EyeQPwrDownMainFunc')
	.sect	'.debug_loc'
.L143:
	.word	-1,.L144,0,.L286-.L144
	.half	2
	.byte	138,0
	.word	0,0
.L287:
	.word	-1,.L144,.L424-.L144,.L286-.L144
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SM_EyeQPwrMainFunc')
	.sect	'.debug_loc'
.L137:
	.word	-1,.L138,0,.L280-.L138
	.half	2
	.byte	138,0
	.word	0,0
.L281:
	.word	-1,.L138,.L420-.L138,.L280-.L138
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SM_EyeQPwrRegRead')
	.sect	'.debug_loc'
.L329:
	.word	-1,.L134,.L132-.L134,.L253-.L134
	.half	5
	.byte	144,36,157,32,0
	.word	.L82-.L134,.L328-.L134
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L133:
	.word	-1,.L134,0,.L328-.L134
	.half	2
	.byte	138,0
	.word	0,0
.L330:
	.word	-1,.L134,.L413-.L134,.L85-.L134
	.half	5
	.byte	144,33,157,32,0
	.word	.L414-.L134,.L415-.L134
	.half	5
	.byte	144,39,157,32,32
	.word	.L87-.L134,.L84-.L134
	.half	5
	.byte	144,39,157,32,32
	.word	.L84-.L134,.L416-.L134
	.half	5
	.byte	144,33,157,32,0
	.word	.L417-.L134,.L418-.L134
	.half	5
	.byte	144,33,157,32,0
	.word	.L419-.L134,.L328-.L134
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SM_EyeQPwrRegSet')
	.sect	'.debug_loc'
.L326:
	.word	0,0
.L129:
	.word	-1,.L130,0,.L324-.L130
	.half	2
	.byte	138,0
	.word	0,0
.L327:
	.word	-1,.L130,.L407-.L130,.L408-.L130
	.half	5
	.byte	144,33,157,32,0
	.word	.L132-.L130,.L253-.L130
	.half	5
	.byte	144,39,157,32,32
	.word	.L409-.L130,.L410-.L130
	.half	5
	.byte	144,39,157,32,32
	.word	.L410-.L130,.L411-.L130
	.half	5
	.byte	144,33,157,32,0
	.word	.L412-.L130,.L324-.L130
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SM_EyeQPwrUpMainFunc')
	.sect	'.debug_loc'
.L141:
	.word	-1,.L142,0,.L284-.L142
	.half	2
	.byte	138,0
	.word	0,0
.L285:
	.word	-1,.L142,.L422-.L142,.L423-.L142
	.half	5
	.byte	144,33,157,32,0
	.word	.L423-.L142,.L284-.L142
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SM_PwrInit')
	.sect	'.debug_loc'
.L135:
	.word	-1,.L136,0,.L279-.L136
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('checkAck')
	.sect	'.debug_loc'
.L297:
	.word	-1,.L114,0,.L343-.L114
	.half	5
	.byte	144,34,157,32,0
	.word	.L23-.L114,.L346-.L114
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L113:
	.word	-1,.L114,0,.L296-.L114
	.half	2
	.byte	138,0
	.word	0,0
.L298:
	.word	-1,.L114,.L344-.L114,.L345-.L114
	.half	5
	.byte	144,39,157,32,32
	.word	.L345-.L114,.L347-.L114
	.half	5
	.byte	144,33,157,32,0
	.word	.L348-.L114,.L296-.L114
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('readByte')
	.sect	'.debug_loc'
.L310:
	.word	-1,.L120,.L42-.L120,.L361-.L120
	.half	5
	.byte	144,36,157,32,32
	.word	.L124-.L120,.L238-.L120
	.half	5
	.byte	144,36,157,32,32
	.word	.L363-.L120,.L364-.L120
	.half	5
	.byte	144,36,157,32,32
	.word	.L122-.L120,.L248-.L120
	.half	5
	.byte	144,36,157,32,32
	.word	.L366-.L120,.L40-.L120
	.half	5
	.byte	144,36,157,32,32
	.word	.L47-.L120,.L367-.L120
	.half	5
	.byte	144,36,157,32,32
	.word	.L370-.L120,.L371-.L120
	.half	5
	.byte	144,36,157,32,32
	.word	.L373-.L120,.L41-.L120
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L306:
	.word	-1,.L120,0,.L359-.L120
	.half	5
	.byte	144,34,157,32,0
	.word	.L40-.L120,.L368-.L120
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L119:
	.word	-1,.L120,0,.L305-.L120
	.half	2
	.byte	138,0
	.word	0,0
.L309:
	.word	-1,.L120,.L360-.L120,.L361-.L120
	.half	5
	.byte	144,39,157,32,32
	.word	.L124-.L120,.L238-.L120
	.half	5
	.byte	144,39,157,32,32
	.word	.L363-.L120,.L364-.L120
	.half	5
	.byte	144,39,157,32,32
	.word	.L122-.L120,.L365-.L120
	.half	5
	.byte	144,39,157,32,32
	.word	.L45-.L120,.L248-.L120
	.half	5
	.byte	144,39,157,32,32
	.word	.L366-.L120,.L367-.L120
	.half	5
	.byte	144,39,157,32,32
	.word	.L370-.L120,.L371-.L120
	.half	5
	.byte	144,39,157,32,32
	.word	.L373-.L120,.L305-.L120
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L308:
	.word	-1,.L120,.L362-.L120,.L361-.L120
	.half	5
	.byte	144,33,157,32,0
	.word	.L124-.L120,.L238-.L120
	.half	5
	.byte	144,33,157,32,0
	.word	.L369-.L120,.L367-.L120
	.half	5
	.byte	144,33,157,32,0
	.word	.L370-.L120,.L372-.L120
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('readOneByte')
	.sect	'.debug_loc'
.L319:
	.word	-1,.L128,0,.L391-.L128
	.half	5
	.byte	144,34,157,32,32
	.word	.L392-.L128,.L317-.L128
	.half	5
	.byte	144,39,157,32,32
	.word	.L65-.L128,.L400-.L128
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L318:
	.word	-1,.L128,0,.L391-.L128
	.half	5
	.byte	144,34,157,32,0
	.word	.L65-.L128,.L400-.L128
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L321:
	.word	-1,.L128,0,.L391-.L128
	.half	1
	.byte	100
	.word	.L392-.L128,.L317-.L128
	.half	1
	.byte	111
	.word	.L65-.L128,.L400-.L128
	.half	1
	.byte	100
	.word	0,0
.L127:
	.word	-1,.L128,0,.L317-.L128
	.half	2
	.byte	138,0
	.word	0,0
.L322:
	.word	-1,.L128,.L393-.L128,.L317-.L128
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L323:
	.word	-1,.L128,.L394-.L128,.L72-.L128
	.half	5
	.byte	144,33,157,32,0
	.word	.L67-.L128,.L395-.L128
	.half	5
	.byte	144,33,157,32,0
	.word	.L396-.L128,.L397-.L128
	.half	5
	.byte	144,33,157,32,0
	.word	.L398-.L128,.L399-.L128
	.half	5
	.byte	144,33,157,32,0
	.word	.L401-.L128,.L402-.L128
	.half	5
	.byte	144,33,157,32,0
	.word	.L403-.L128,.L404-.L128
	.half	5
	.byte	144,33,157,32,0
	.word	.L405-.L128,.L406-.L128
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sendAck')
	.sect	'.debug_loc'
.L293:
	.word	-1,.L110,0,.L339-.L110
	.half	5
	.byte	144,34,157,32,0
	.word	.L15-.L110,.L340-.L110
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L109:
	.word	-1,.L110,0,.L292-.L110
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('sendNoAck')
	.sect	'.debug_loc'
.L295:
	.word	-1,.L112,0,.L341-.L112
	.half	5
	.byte	144,34,157,32,0
	.word	.L19-.L112,.L342-.L112
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L111:
	.word	-1,.L112,0,.L294-.L112
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('writeByte')
	.sect	'.debug_loc'
.L304:
	.word	0,0
.L300:
	.word	-1,.L116,0,.L30-.L116
	.half	5
	.byte	144,34,157,32,0
	.word	.L28-.L116,.L34-.L116
	.half	5
	.byte	144,34,157,32,0
	.word	.L29-.L116,.L33-.L116
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L301:
	.word	-1,.L116,0,.L30-.L116
	.half	5
	.byte	144,34,157,32,32
	.word	.L349-.L116,.L350-.L116
	.half	5
	.byte	144,39,157,32,32
	.word	.L118-.L116,.L243-.L116
	.half	5
	.byte	144,39,157,32,32
	.word	.L352-.L116,.L353-.L116
	.half	5
	.byte	144,39,157,32,32
	.word	.L355-.L116,.L354-.L116
	.half	5
	.byte	144,39,157,32,32
	.word	.L28-.L116,.L34-.L116
	.half	5
	.byte	144,34,157,32,32
	.word	.L356-.L116,.L357-.L116
	.half	5
	.byte	144,39,157,32,32
	.word	.L358-.L116,.L299-.L116
	.half	5
	.byte	144,39,157,32,32
	.word	.L29-.L116,.L33-.L116
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L303:
	.word	-1,.L116,.L351-.L116,.L350-.L116
	.half	5
	.byte	144,36,157,32,0
	.word	.L118-.L116,.L243-.L116
	.half	5
	.byte	144,36,157,32,0
	.word	.L352-.L116,.L354-.L116
	.half	5
	.byte	144,36,157,32,0
	.word	.L356-.L116,.L299-.L116
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L115:
	.word	-1,.L116,0,.L299-.L116
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('writeOneByte')
	.sect	'.debug_loc'
.L313:
	.word	-1,.L126,0,.L374-.L126
	.half	5
	.byte	144,34,157,32,32
	.word	.L375-.L126,.L311-.L126
	.half	5
	.byte	144,39,157,32,32
	.word	.L53-.L126,.L384-.L126
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L312:
	.word	-1,.L126,0,.L374-.L126
	.half	5
	.byte	144,34,157,32,0
	.word	.L53-.L126,.L384-.L126
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L314:
	.word	-1,.L126,0,.L374-.L126
	.half	5
	.byte	144,35,157,32,0
	.word	.L376-.L126,.L311-.L126
	.half	5
	.byte	144,36,157,32,0
	.word	.L53-.L126,.L384-.L126
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L315:
	.word	-1,.L126,.L377-.L126,.L311-.L126
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L316:
	.word	-1,.L126,.L378-.L126,.L60-.L126
	.half	5
	.byte	144,33,157,32,0
	.word	.L55-.L126,.L379-.L126
	.half	5
	.byte	144,33,157,32,0
	.word	.L380-.L126,.L381-.L126
	.half	5
	.byte	144,33,157,32,0
	.word	.L382-.L126,.L383-.L126
	.half	5
	.byte	144,33,157,32,0
	.word	.L385-.L126,.L386-.L126
	.half	5
	.byte	144,33,157,32,0
	.word	.L387-.L126,.L388-.L126
	.half	5
	.byte	144,33,157,32,0
	.word	.L389-.L126,.L390-.L126
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L125:
	.word	-1,.L126,0,.L311-.L126
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L725:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('CDD_IIC_COM_Delay')
	.sect	'.debug_frame'
	.word	24
	.word	.L725,.L104,.L274-.L104
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('CDD_IIC_Start')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L106,.L288-.L106
	.sdecl	'.debug_frame',debug,cluster('CDD_IIC_Stop')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L108,.L290-.L108
	.sdecl	'.debug_frame',debug,cluster('sendAck')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L110,.L292-.L110
	.sdecl	'.debug_frame',debug,cluster('sendNoAck')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L112,.L294-.L112
	.sdecl	'.debug_frame',debug,cluster('checkAck')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L114,.L296-.L114
	.sdecl	'.debug_frame',debug,cluster('writeByte')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L116,.L299-.L116
	.sdecl	'.debug_frame',debug,cluster('readByte')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L120,.L305-.L120
	.sdecl	'.debug_frame',debug,cluster('writeOneByte')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L126,.L311-.L126
	.sdecl	'.debug_frame',debug,cluster('readOneByte')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L128,.L317-.L128
	.sdecl	'.debug_frame',debug,cluster('SM_EyeQPwrRegSet')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L130,.L324-.L130
	.sdecl	'.debug_frame',debug,cluster('SM_EyeQPwrRegRead')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L134,.L328-.L134
	.sdecl	'.debug_frame',debug,cluster('SM_PwrInit')
	.sect	'.debug_frame'
	.word	24
	.word	.L725,.L136,.L279-.L136
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('SM_EyeQPwrMainFunc')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L138,.L280-.L138
	.sdecl	'.debug_frame',debug,cluster('SM_EyeQPwrDiagMainFunc')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L140,.L282-.L140
	.sdecl	'.debug_frame',debug,cluster('SM_EyeQPwrUpMainFunc')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L142,.L284-.L142
	.sdecl	'.debug_frame',debug,cluster('SM_EyeQPwrDownMainFunc')
	.sect	'.debug_frame'
	.word	12
	.word	.L725,.L144,.L286-.L144
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L726:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L726,.L118,.L243-.L118
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L726,.L122,.L248-.L122
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L726,.L124,.L238-.L124
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_4')
	.sect	'.debug_frame'
	.word	24
	.word	.L726,.L132,.L253-.L132
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   903  
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   904  /*******************************************************************************
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   905  |    Function Source Code End
; ..\Src_file\CDD_EyeQ_PowerCtrl.c	   906  |******************************************************************************/

	; Module end
