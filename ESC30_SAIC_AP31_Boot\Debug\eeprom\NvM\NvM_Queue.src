	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc22848a -c99 --dep-file=eeprom\\NvM\\.NvM_Queue.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\NvM\\NvM_Queue.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\NvM\\NvM_Queue.src ..\\eeprom\\NvM\\NvM_Queue.c"
	.compiler_name		"ctc"
	.name	"NvM_Queue"

	
$TC16X
	
	.sdecl	'.text.NvM_Queue.NvM_QueueInit',code,cluster('NvM_QueueInit')
	.sect	'.text.NvM_Queue.NvM_QueueInit'
	.align	2
	
	.global	NvM_QueueInit

; ..\eeprom\NvM\NvM_Queue.c	     1  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	     2   *  COPYRIGHT
; ..\eeprom\NvM\NvM_Queue.c	     3   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Queue.c	     4   *  \verbatim
; ..\eeprom\NvM\NvM_Queue.c	     5   *  Copyright (c) 2019 by Vector Informatik GmbH.                                                  All rights reserved.
; ..\eeprom\NvM\NvM_Queue.c	     6   *
; ..\eeprom\NvM\NvM_Queue.c	     7   *                This software is copyright protected and proprietary to Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_Queue.c	     8   *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
; ..\eeprom\NvM\NvM_Queue.c	     9   *                All other rights remain with Vector Informatik GmbH.
; ..\eeprom\NvM\NvM_Queue.c	    10   *  \endverbatim
; ..\eeprom\NvM\NvM_Queue.c	    11   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Queue.c	    12   *  FILE DESCRIPTION
; ..\eeprom\NvM\NvM_Queue.c	    13   *  -------------------------------------------------------------------------------------------------------------------
; ..\eeprom\NvM\NvM_Queue.c	    14   *         File:  NvM_Queue.c
; ..\eeprom\NvM\NvM_Queue.c	    15   *      Project:  MemService_AsrNvM
; ..\eeprom\NvM\NvM_Queue.c	    16   *       Module:  NvM - Submodule Queue
; ..\eeprom\NvM\NvM_Queue.c	    17   *    Generator:  -
; ..\eeprom\NvM\NvM_Queue.c	    18   *
; ..\eeprom\NvM\NvM_Queue.c	    19   *  Description:  This sub-module contains the queue-handling of the (optionally)
; ..\eeprom\NvM\NvM_Queue.c	    20   *                prioritized job queue.
; ..\eeprom\NvM\NvM_Queue.c	    21   *
; ..\eeprom\NvM\NvM_Queue.c	    22   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    23  
; ..\eeprom\NvM\NvM_Queue.c	    24  /* Do not modify this file! */
; ..\eeprom\NvM\NvM_Queue.c	    25  
; ..\eeprom\NvM\NvM_Queue.c	    26  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    27   *  MODULE SWITCH
; ..\eeprom\NvM\NvM_Queue.c	    28   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    29  #define NVM_QUEUE_SOURCE
; ..\eeprom\NvM\NvM_Queue.c	    30  
; ..\eeprom\NvM\NvM_Queue.c	    31  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    32   *  INCLUDES
; ..\eeprom\NvM\NvM_Queue.c	    33   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    34  #include "Std_Types.h"
; ..\eeprom\NvM\NvM_Queue.c	    35  
; ..\eeprom\NvM\NvM_Queue.c	    36  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    37   *  INCLUDE MAIN HEADER (BECAUSE OF ERROR CODES)
; ..\eeprom\NvM\NvM_Queue.c	    38   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    39  #include "NvM.h"
; ..\eeprom\NvM\NvM_Queue.c	    40  #include "NvM_PrivateCfg.h"
; ..\eeprom\NvM\NvM_Queue.c	    41  
; ..\eeprom\NvM\NvM_Queue.c	    42  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    43   *  INCLUDE OF THE CENTRAL INTERNAL INCLUDE
; ..\eeprom\NvM\NvM_Queue.c	    44   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    45  #include "NvM_JobProc.h"
; ..\eeprom\NvM\NvM_Queue.c	    46  
; ..\eeprom\NvM\NvM_Queue.c	    47  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    48   *  MODULE HEADER INCLUDES
; ..\eeprom\NvM\NvM_Queue.c	    49   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    50  #include "NvM_Queue.h"
; ..\eeprom\NvM\NvM_Queue.c	    51  
; ..\eeprom\NvM\NvM_Queue.c	    52  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    53   *  INTERNAL MACROS
; ..\eeprom\NvM\NvM_Queue.c	    54   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    55  #ifndef NVM_LOCAL /* COV_NVM_COMPATIBILITY */
; ..\eeprom\NvM\NvM_Queue.c	    56  # define NVM_LOCAL static
; ..\eeprom\NvM\NvM_Queue.c	    57  #endif
; ..\eeprom\NvM\NvM_Queue.c	    58  
; ..\eeprom\NvM\NvM_Queue.c	    59  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    60   *  INTERNAL DATA (this is the only object we always need, regardless of NVM_API_CONFIG_CLASS)
; ..\eeprom\NvM\NvM_Queue.c	    61   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    62  #define NVM_START_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM_Queue.c	    63    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Queue.c	    64  
; ..\eeprom\NvM\NvM_Queue.c	    65  VAR(NvM_JobType, NVM_PRIVATE_DATA) NvM_CurrentJob_t;
; ..\eeprom\NvM\NvM_Queue.c	    66  
; ..\eeprom\NvM\NvM_Queue.c	    67  #define NVM_STOP_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM_Queue.c	    68    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Queue.c	    69  
; ..\eeprom\NvM\NvM_Queue.c	    70  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    71   *  MODULE SWITCH - NVM Configuration class
; ..\eeprom\NvM\NvM_Queue.c	    72   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    73  /* queueing is only needed in config classes 2 and 3 */
; ..\eeprom\NvM\NvM_Queue.c	    74  #if (NVM_API_CONFIG_CLASS != NVM_API_CONFIG_CLASS_1)
; ..\eeprom\NvM\NvM_Queue.c	    75  
; ..\eeprom\NvM\NvM_Queue.c	    76  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    77   *  INTERNAL DEFINES
; ..\eeprom\NvM\NvM_Queue.c	    78   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    79  #define NVM_IMMEDIATE_JOB_PRIO  (0x00u)
; ..\eeprom\NvM\NvM_Queue.c	    80  #define NVM_LOWEST_JOB_PRIO     (0xFFu)
; ..\eeprom\NvM\NvM_Queue.c	    81  #define NVM_LIST_END            (0xFFu)
; ..\eeprom\NvM\NvM_Queue.c	    82  
; ..\eeprom\NvM\NvM_Queue.c	    83  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    84   *  INTERNAL TYPE DEFINITIONS
; ..\eeprom\NvM\NvM_Queue.c	    85   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    86  
; ..\eeprom\NvM\NvM_Queue.c	    87  /* type to reference to a List */
; ..\eeprom\NvM\NvM_Queue.c	    88  typedef P2VAR(NvM_QueueEntryRefType, AUTOMATIC, NVM_PRIVATE_DATA) NvM_QueueListHeadRefType;
; ..\eeprom\NvM\NvM_Queue.c	    89  
; ..\eeprom\NvM\NvM_Queue.c	    90  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	    91   *  INTERNAL DATA
; ..\eeprom\NvM\NvM_Queue.c	    92   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	    93  #define NVM_START_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM_Queue.c	    94    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Queue.c	    95  
; ..\eeprom\NvM\NvM_Queue.c	    96  /*---- Internal Module Global Variables ------------------------------- */
; ..\eeprom\NvM\NvM_Queue.c	    97  #if(NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	    98  NVM_LOCAL VAR(NvM_JobQueueType, NVM_PRIVATE_DATA) NvM_HighPrioQueue;
; ..\eeprom\NvM\NvM_Queue.c	    99  
; ..\eeprom\NvM\NvM_Queue.c	   100  NVM_LOCAL VAR(NvM_QueueEntryRefType, NVM_PRIVATE_DATA) NvM_LastJobEntry;
; ..\eeprom\NvM\NvM_Queue.c	   101  #endif
; ..\eeprom\NvM\NvM_Queue.c	   102  
; ..\eeprom\NvM\NvM_Queue.c	   103  NVM_LOCAL VAR(NvM_JobQueueType, NVM_PRIVATE_DATA) NvM_NormalPrioQueue;
; ..\eeprom\NvM\NvM_Queue.c	   104  
; ..\eeprom\NvM\NvM_Queue.c	   105  #define NVM_STOP_SEC_VAR_NOINIT_UNSPECIFIED
; ..\eeprom\NvM\NvM_Queue.c	   106    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Queue.c	   107  
; ..\eeprom\NvM\NvM_Queue.c	   108  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   109   *  INTERNAL FORWARD DECLARATIONS
; ..\eeprom\NvM\NvM_Queue.c	   110   **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   111  
; ..\eeprom\NvM\NvM_Queue.c	   112  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   113   * NvM_QueuePush
; ..\eeprom\NvM\NvM_Queue.c	   114   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   115  /*! \brief Add job to queue.
; ..\eeprom\NvM\NvM_Queue.c	   116   *  \details Pushes the given element onto the given list, i.e. the element is inserted at list head.
; ..\eeprom\NvM\NvM_Queue.c	   117   *  \param[in] Queue as an index to the next queue element. Caller has to ensure validity.
; ..\eeprom\NvM\NvM_Queue.c	   118   *  \param[in] Elem as an index to the queue, shall be enqueued at the end of the linked list.
; ..\eeprom\NvM\NvM_Queue.c	   119   *             Caller has to ensure validity.
; ..\eeprom\NvM\NvM_Queue.c	   120   *  \context TASK
; ..\eeprom\NvM\NvM_Queue.c	   121   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Queue.c	   122   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Queue.c	   123   *  \config Configuration class is > 1
; ..\eeprom\NvM\NvM_Queue.c	   124   *  \pre -
; ..\eeprom\NvM\NvM_Queue.c	   125   */
; ..\eeprom\NvM\NvM_Queue.c	   126  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_QueuePush(NvM_QueueListHeadRefType Queue, NvM_QueueEntryRefType Elem);
; ..\eeprom\NvM\NvM_Queue.c	   127  
; ..\eeprom\NvM\NvM_Queue.c	   128  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   129   * NvM_QueuePop
; ..\eeprom\NvM\NvM_Queue.c	   130   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   131  /*! \brief Removes first element from queue.
; ..\eeprom\NvM\NvM_Queue.c	   132   *  \details Pops the first element from the given list, i.e. the element is removed from the list and will be returned.
; ..\eeprom\NvM\NvM_Queue.c	   133   *           The given list shall not be empty!
; ..\eeprom\NvM\NvM_Queue.c	   134   *  \param[in,out] Queue as an index to the queue to pop out of the queue. Caller has to ensure validity.
; ..\eeprom\NvM\NvM_Queue.c	   135   *  \return given element's index
; ..\eeprom\NvM\NvM_Queue.c	   136   *  \context TASK
; ..\eeprom\NvM\NvM_Queue.c	   137   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Queue.c	   138   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Queue.c	   139   *  \config Configuration class is > 1
; ..\eeprom\NvM\NvM_Queue.c	   140   *  \pre -
; ..\eeprom\NvM\NvM_Queue.c	   141   */
; ..\eeprom\NvM\NvM_Queue.c	   142  NVM_LOCAL FUNC(NvM_QueueEntryRefType, NVM_PRIVATE_CODE) NvM_QueuePop(NvM_QueueListHeadRefType Queue);
; ..\eeprom\NvM\NvM_Queue.c	   143  
; ..\eeprom\NvM\NvM_Queue.c	   144  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   145   * NvM_QueueFindBlock
; ..\eeprom\NvM\NvM_Queue.c	   146   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   147  /*! \brief Searches for requested block within the queue.
; ..\eeprom\NvM\NvM_Queue.c	   148   *  \details Searches a specific block in the queue and returns a reference to the queue element containing the blockId
; ..\eeprom\NvM\NvM_Queue.c	   149               The queue is not modified here!
; ..\eeprom\NvM\NvM_Queue.c	   150               Must be called within a critical section (because preemption might result in queue modification, corrupting
; ..\eeprom\NvM\NvM_Queue.c	   151               the search.
; ..\eeprom\NvM\NvM_Queue.c	   152   *  \param[in] QueueHead as an index to the queue to start and end the search with (ring list).
; ..\eeprom\NvM\NvM_Queue.c	   153   *             Caller has to ensure validity.
; ..\eeprom\NvM\NvM_Queue.c	   154   *  \param[in] BlockId in range [1, (number of blocks - 1)].
; ..\eeprom\NvM\NvM_Queue.c	   155   *  \return queue element which stores the blockId if blockId was found within queue
; ..\eeprom\NvM\NvM_Queue.c	   156   *  \return queue end if blockId wasn't found within queue
; ..\eeprom\NvM\NvM_Queue.c	   157   *  \context TASK
; ..\eeprom\NvM\NvM_Queue.c	   158   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Queue.c	   159   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Queue.c	   160   *  \config Configuration class is > 1
; ..\eeprom\NvM\NvM_Queue.c	   161   *  \pre -
; ..\eeprom\NvM\NvM_Queue.c	   162   */
; ..\eeprom\NvM\NvM_Queue.c	   163  NVM_LOCAL FUNC(NvM_QueueEntryRefType, NVM_PRIVATE_CODE) NvM_QueueFindBlock(NvM_QueueEntryRefType QueueHead, NvM_BlockIdType BlockId);
; ..\eeprom\NvM\NvM_Queue.c	   164  
; ..\eeprom\NvM\NvM_Queue.c	   165  #if (NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   166  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   167   * NvM_QueueRequeueLastJob
; ..\eeprom\NvM\NvM_Queue.c	   168   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   169  /*! \brief Requeus the last job.
; ..\eeprom\NvM\NvM_Queue.c	   170   *  \details Puts the last found job back into the Normal Prio Queue. It will be inserted at the list end.
; ..\eeprom\NvM\NvM_Queue.c	   171   *  \context TASK
; ..\eeprom\NvM\NvM_Queue.c	   172   *  \reentrant FALSE
; ..\eeprom\NvM\NvM_Queue.c	   173   *  \synchronous TRUE
; ..\eeprom\NvM\NvM_Queue.c	   174   *  \config Configuration class is > 1 and priority handling is enabled
; ..\eeprom\NvM\NvM_Queue.c	   175   *  \pre -
; ..\eeprom\NvM\NvM_Queue.c	   176   */
; ..\eeprom\NvM\NvM_Queue.c	   177  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_QueueRequeueLastJob(void);
; ..\eeprom\NvM\NvM_Queue.c	   178  #endif
; ..\eeprom\NvM\NvM_Queue.c	   179  
; ..\eeprom\NvM\NvM_Queue.c	   180  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   181   *  IMPLEMENTATION
; ..\eeprom\NvM\NvM_Queue.c	   182   *********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   183  #define NVM_START_SEC_CODE
; ..\eeprom\NvM\NvM_Queue.c	   184    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Queue.c	   185  
; ..\eeprom\NvM\NvM_Queue.c	   186  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   187  *  NvM_QueueInit
; ..\eeprom\NvM\NvM_Queue.c	   188  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   189  /*!
; ..\eeprom\NvM\NvM_Queue.c	   190   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   191   *
; ..\eeprom\NvM\NvM_Queue.c	   192   *
; ..\eeprom\NvM\NvM_Queue.c	   193   *
; ..\eeprom\NvM\NvM_Queue.c	   194   *
; ..\eeprom\NvM\NvM_Queue.c	   195   */
; ..\eeprom\NvM\NvM_Queue.c	   196  FUNC(void, NVM_PRIVATE_CODE) NvM_QueueInit(void)
; Function NvM_QueueInit
.L27:
NvM_QueueInit:	.type	func

; ..\eeprom\NvM\NvM_Queue.c	   197  {
; ..\eeprom\NvM\NvM_Queue.c	   198      uint8 maxQueueIndex = (NVM_SIZE_STANDARD_JOB_QUEUE + NVM_SIZE_IMMEDIATE_JOB_QUEUE - 1u);
; ..\eeprom\NvM\NvM_Queue.c	   199      uint8 index = maxQueueIndex;
; ..\eeprom\NvM\NvM_Queue.c	   200  
; ..\eeprom\NvM\NvM_Queue.c	   201      /* link all list elements */
; ..\eeprom\NvM\NvM_Queue.c	   202      while(index > 0u)
; ..\eeprom\NvM\NvM_Queue.c	   203      {
; ..\eeprom\NvM\NvM_Queue.c	   204          /* element at <index> is linked to its predecessor */
; ..\eeprom\NvM\NvM_Queue.c	   205          NvM_JobQueue_at[index].PrevEntry = index - 1u; /* SBSW_NvM_AccessJobQueue */
	mov	d0,#95
	movh.a	a15,#@his(NvM_JobQueue_at)
.L152:
	lea	a15,[a15]@los(NvM_JobQueue_at)
.L193:
	lea	a2,[a15]1140
.L194:
	lea	a4,94
.L2:
	add	d15,d0,#-1
	st.b	[a2]8,d15
.L195:

; ..\eeprom\NvM\NvM_Queue.c	   206  
; ..\eeprom\NvM\NvM_Queue.c	   207          /* now we are at the predecessor, link it with it successor */
; ..\eeprom\NvM\NvM_Queue.c	   208          NvM_JobQueue_at[index - 1u].NextEntry = index; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   209  
; ..\eeprom\NvM\NvM_Queue.c	   210          /* Initializing the SeviceId member of each list element is not necessary, because initially
; ..\eeprom\NvM\NvM_Queue.c	   211             they are in the "empty" list --> the member will not be used.
; ..\eeprom\NvM\NvM_Queue.c	   212             Once an element gets into the job queue, the Service Id will be initialized */
; ..\eeprom\NvM\NvM_Queue.c	   213  
; ..\eeprom\NvM\NvM_Queue.c	   214          --index;
	mul	d15,d0,#12
	lea	a2,[a2]-12
.L196:
	addsc.a	a5,a15,d15,#0
.L197:
	st.b	[a5]-5,d0
.L198:
	add	d0,#-1
	loop	a4,.L2
.L199:

; ..\eeprom\NvM\NvM_Queue.c	   215      }
; ..\eeprom\NvM\NvM_Queue.c	   216  
; ..\eeprom\NvM\NvM_Queue.c	   217      /* split the lists into two separate ring lists */
; ..\eeprom\NvM\NvM_Queue.c	   218      index = NVM_SIZE_STANDARD_JOB_QUEUE;
; ..\eeprom\NvM\NvM_Queue.c	   219  
; ..\eeprom\NvM\NvM_Queue.c	   220  #if (NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   221          NvM_LastJobEntry = NVM_LIST_END;
; ..\eeprom\NvM\NvM_Queue.c	   222  
; ..\eeprom\NvM\NvM_Queue.c	   223          NvM_HighPrioQueue.SrvList = NVM_LIST_END;
; ..\eeprom\NvM\NvM_Queue.c	   224          NvM_HighPrioQueue.EmptyList = index;
; ..\eeprom\NvM\NvM_Queue.c	   225  
; ..\eeprom\NvM\NvM_Queue.c	   226          NvM_JobQueue_at[maxQueueIndex].NextEntry = index; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   227          NvM_JobQueue_at[index].PrevEntry = maxQueueIndex; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   228  #endif
; ..\eeprom\NvM\NvM_Queue.c	   229  
; ..\eeprom\NvM\NvM_Queue.c	   230      /* normal prio queue */
; ..\eeprom\NvM\NvM_Queue.c	   231      NvM_JobQueue_at[--index].NextEntry = 0u; /* SBSW_NvM_AccessJobQueue */
	mov	d0,#0
	st.b	[a15]1147,d0
.L153:

; ..\eeprom\NvM\NvM_Queue.c	   232      NvM_JobQueue_at[0].PrevEntry = index; /* SBSW_NvM_AccessJobQueue */
	mov	d15,#95
	st.b	[a15]8,d15
.L200:

; ..\eeprom\NvM\NvM_Queue.c	   233  
; ..\eeprom\NvM\NvM_Queue.c	   234      NvM_NormalPrioQueue.EmptyList = 0u;
	movh.a	a15,#@his(NvM_NormalPrioQueue)
	lea	a15,[a15]@los(NvM_NormalPrioQueue)
.L201:
	st.b	[a15]1,d0
.L202:

; ..\eeprom\NvM\NvM_Queue.c	   235      NvM_NormalPrioQueue.SrvList = NVM_LIST_END;
	mov	d15,#255
	st.b	[a15],d15
.L203:

; ..\eeprom\NvM\NvM_Queue.c	   236  }
	ret
.L97:
	
__NvM_QueueInit_function_end:
	.size	NvM_QueueInit,__NvM_QueueInit_function_end-NvM_QueueInit
.L52:
	; End of function
	
	.sdecl	'.text.NvM_Queue.NvM_QueueJob',code,cluster('NvM_QueueJob')
	.sect	'.text.NvM_Queue.NvM_QueueJob'
	.align	2
	
	.global	NvM_QueueJob

; ..\eeprom\NvM\NvM_Queue.c	   237  
; ..\eeprom\NvM\NvM_Queue.c	   238  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   239  *  NvM_QueueJob
; ..\eeprom\NvM\NvM_Queue.c	   240  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   241  /*!
; ..\eeprom\NvM\NvM_Queue.c	   242   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   243   *
; ..\eeprom\NvM\NvM_Queue.c	   244   *
; ..\eeprom\NvM\NvM_Queue.c	   245   *
; ..\eeprom\NvM\NvM_Queue.c	   246   *
; ..\eeprom\NvM\NvM_Queue.c	   247   *
; ..\eeprom\NvM\NvM_Queue.c	   248   *
; ..\eeprom\NvM\NvM_Queue.c	   249   *
; ..\eeprom\NvM\NvM_Queue.c	   250   *
; ..\eeprom\NvM\NvM_Queue.c	   251   *
; ..\eeprom\NvM\NvM_Queue.c	   252   *
; ..\eeprom\NvM\NvM_Queue.c	   253   *
; ..\eeprom\NvM\NvM_Queue.c	   254   */
; ..\eeprom\NvM\NvM_Queue.c	   255  FUNC(boolean, NVM_PRIVATE_CODE) NvM_QueueJob(NvM_BlockIdType BlockId,
; Function NvM_QueueJob
.L29:
NvM_QueueJob:	.type	func
	mov	d11,d4
.L156:
	mov	d12,d5
	mov.aa	a13,a4
.L157:

; ..\eeprom\NvM\NvM_Queue.c	   256                                     NvM_InternalServiceIdType ServiceId,
; ..\eeprom\NvM\NvM_Queue.c	   257                                            NvM_RamAddressType RamAddress
; ..\eeprom\NvM\NvM_Queue.c	   258      )
; ..\eeprom\NvM\NvM_Queue.c	   259  {
; ..\eeprom\NvM\NvM_Queue.c	   260      boolean retVal = FALSE;
	mov	d8,#0
.L162:

; ..\eeprom\NvM\NvM_Queue.c	   261      boolean queueFull;
; ..\eeprom\NvM\NvM_Queue.c	   262      boolean blockAlreadyPending;
; ..\eeprom\NvM\NvM_Queue.c	   263      /* get block management area */
; ..\eeprom\NvM\NvM_Queue.c	   264      const NvM_RamMngmtPtrType ramMngmtPtr =
; ..\eeprom\NvM\NvM_Queue.c	   265          ((BlockId & NVM_DCM_BLOCK_OFFSET) != 0u) ? (&NvM_DcmBlockMngmt_t) : (&NvM_BlockMngmtArea_at[BlockId]);
	jz.t	d11:15,.L3
.L208:
	movh.a	a12,#@his(NvM_DcmBlockMngmt_t)
	lea	a12,[a12]@los(NvM_DcmBlockMngmt_t)
.L209:
	j	.L4
.L3:
	movh.a	a15,#@his(NvM_BlockMngmtArea_at)
	lea	a15,[a15]@los(NvM_BlockMngmtArea_at)
.L210:
	addsc.a	a12,a15,d11,#2
.L4:

; ..\eeprom\NvM\NvM_Queue.c	   266  
; ..\eeprom\NvM\NvM_Queue.c	   267  #if(NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   268      const uint8 priority = (uint8)NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].BlockPrio_u8;
; ..\eeprom\NvM\NvM_Queue.c	   269      /* NvM_HighPrioQueue is only the right queue if block has an immediate priority, current job is a
; ..\eeprom\NvM\NvM_Queue.c	   270        write-job and requested block is not a DCM-Block. Otherwise NvM_NormalPrioQueue is right queue. */
; ..\eeprom\NvM\NvM_Queue.c	   271      P2VAR(NvM_JobQueueType, AUTOMATIC, NVM_PRIVATE_DATA) usedQueue =
; ..\eeprom\NvM\NvM_Queue.c	   272          ((priority == 0u) && (ServiceId == NVM_INT_FID_WRITE_BLOCK) && ((BlockId & NVM_DCM_BLOCK_OFFSET) == 0u)) ?
; ..\eeprom\NvM\NvM_Queue.c	   273          (&NvM_HighPrioQueue) : (&NvM_NormalPrioQueue);
; ..\eeprom\NvM\NvM_Queue.c	   274  #else
; ..\eeprom\NvM\NvM_Queue.c	   275      P2VAR(NvM_JobQueueType, AUTOMATIC, NVM_PRIVATE_DATA) usedQueue = &NvM_NormalPrioQueue;
; ..\eeprom\NvM\NvM_Queue.c	   276  #endif
; ..\eeprom\NvM\NvM_Queue.c	   277      /* #200 critical section (Reason: During accessing the job queue, it shall not be possible to access it from another task) */
; ..\eeprom\NvM\NvM_Queue.c	   278      NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L154:
	movh.a	a14,#@his(NvM_NormalPrioQueue)
	lea	a14,[a14]@los(NvM_NormalPrioQueue)
.L211:

; ..\eeprom\NvM\NvM_Queue.c	   279      /* check queue fill status before queuing the block! */
; ..\eeprom\NvM\NvM_Queue.c	   280      queueFull = (usedQueue->EmptyList == NVM_LIST_END);
	mov.aa	a4,a14
	ld.bu	d0,[+a4]1
.L212:

; ..\eeprom\NvM\NvM_Queue.c	   281      blockAlreadyPending = (ramMngmtPtr->NvRamErrorStatus_u8 == NVM_REQ_PENDING);
	ld.bu	d15,[a12]1
.L213:
	eq	d9,d0,#255
.L161:
	eq	d10,d15,#2
.L158:

; ..\eeprom\NvM\NvM_Queue.c	   282      /* #210 queue is not full and the requested block isn't already pending */
; ..\eeprom\NvM\NvM_Queue.c	   283      if((queueFull == FALSE) && (blockAlreadyPending == FALSE))
	jne	d9,#0,.L5
.L214:
	jne	d10,#0,.L6
.L111:

; ..\eeprom\NvM\NvM_Queue.c	   284      {
; ..\eeprom\NvM\NvM_Queue.c	   285          /* #211 find next free element in queue */
; ..\eeprom\NvM\NvM_Queue.c	   286          const NvM_QueueEntryRefType elem = NvM_QueuePop(&usedQueue->EmptyList); /* SBSW_NvM_FuncCall_PtrParam_Queue */
	call	NvM_QueuePop
.L159:

; ..\eeprom\NvM\NvM_Queue.c	   287          CONSTP2VAR(NvM_QueueEntryType, AUTOMATIC, NVM_PRIVATE_DATA) elemPtr = &NvM_JobQueue_at[elem];
	mul	d15,d2,#12
	fcall	.cocofun_1
.L215:
	addsc.a	a15,a2,d15,#0
.L163:

; ..\eeprom\NvM\NvM_Queue.c	   288          /* #212 setup and queue NvM job */
; ..\eeprom\NvM\NvM_Queue.c	   289          elemPtr->BlockId   = BlockId; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   290          elemPtr->RamAddr_t = RamAddress; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   291          elemPtr->ServiceId = ServiceId; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   292  #if(NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   293          elemPtr->JobPrio = priority; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   294  #endif
; ..\eeprom\NvM\NvM_Queue.c	   295          NvM_QueuePush(&usedQueue->SrvList, elem); /* SBSW_NvM_FuncCall_PtrParam_Queue */
	mov	d4,d2
	mov.aa	a4,a14
.L164:
	st.h	[a15]4,d11
.L216:
	st.a	[a15],a13
.L217:
	st.b	[a15]6,d12
.L218:
	call	NvM_QueuePush
.L160:

; ..\eeprom\NvM\NvM_Queue.c	   296          /* #213 set the block status to NVM_REQ_PENDING */
; ..\eeprom\NvM\NvM_Queue.c	   297          ramMngmtPtr->NvRamErrorStatus_u8 = NVM_REQ_PENDING; /* SBSW_NvM_AccessBlockManagementArea */
	mov	d15,#2
	st.b	[a12]1,d15
.L219:

; ..\eeprom\NvM\NvM_Queue.c	   298          /* block queued and pending, return successfully */
; ..\eeprom\NvM\NvM_Queue.c	   299          retVal = TRUE;
	mov	d8,#1
.L6:
.L5:

; ..\eeprom\NvM\NvM_Queue.c	   300      }
; ..\eeprom\NvM\NvM_Queue.c	   301      /* exit critical section */
; ..\eeprom\NvM\NvM_Queue.c	   302      NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L220:

; ..\eeprom\NvM\NvM_Queue.c	   303      /* #300 queue was full, request to queue the next block leads to queue overflow */
; ..\eeprom\NvM\NvM_Queue.c	   304      if(queueFull == TRUE)
	jne	d9,#0,.L7
.L221:

; ..\eeprom\NvM\NvM_Queue.c	   305      {
; ..\eeprom\NvM\NvM_Queue.c	   306          /* #310 report queue overflow to DEM */
; ..\eeprom\NvM\NvM_Queue.c	   307          NvM_DemReportErrorQueueOverflow();
; ..\eeprom\NvM\NvM_Queue.c	   308      }
; ..\eeprom\NvM\NvM_Queue.c	   309      /* #400 queue wasn't full, block wasn't pending, queuing was successful */
; ..\eeprom\NvM\NvM_Queue.c	   310      else if (blockAlreadyPending == FALSE)
	jne	d10,#0,.L8
.L222:

; ..\eeprom\NvM\NvM_Queue.c	   311      {
; ..\eeprom\NvM\NvM_Queue.c	   312          /* #410 invoke notifications */
; ..\eeprom\NvM\NvM_Queue.c	   313          /* invoke the notification out of the critical section! */
; ..\eeprom\NvM\NvM_Queue.c	   314          NvM_BlockNotification(BlockId, NvM_IntServiceDescrTable_at[ServiceId].PublicFid_t, NVM_REQ_PENDING);
	movh.a	a15,#@his(NvM_IntServiceDescrTable_at)
	lea	a15,[a15]@los(NvM_IntServiceDescrTable_at)
.L223:
	addsc.a	a15,a15,d12,#2
.L224:
	mov	d4,d11
.L165:
	ld.bu	d5,[a15]2
.L225:
	mov	d6,#2
	call	NvM_BlockNotification
.L8:
.L7:

; ..\eeprom\NvM\NvM_Queue.c	   315      }
; ..\eeprom\NvM\NvM_Queue.c	   316      else
; ..\eeprom\NvM\NvM_Queue.c	   317      {
; ..\eeprom\NvM\NvM_Queue.c	   318          /* nothing to do here */
; ..\eeprom\NvM\NvM_Queue.c	   319      }
; ..\eeprom\NvM\NvM_Queue.c	   320  
; ..\eeprom\NvM\NvM_Queue.c	   321      return retVal;
; ..\eeprom\NvM\NvM_Queue.c	   322  }
	mov	d2,d8
	ret
.L100:
	
__NvM_QueueJob_function_end:
	.size	NvM_QueueJob,__NvM_QueueJob_function_end-NvM_QueueJob
.L57:
	; End of function
	
	.sdecl	'.text.NvM_Queue..cocofun_1',code,cluster('.cocofun_1')
	.sect	'.text.NvM_Queue..cocofun_1'
	.align	2
; Function .cocofun_1
.L31:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	movh.a	a2,#@his(NvM_JobQueue_at)
.L155:
	lea	a2,[a2]@los(NvM_JobQueue_at)
.L319:
	fret
.L92:
	; End of function
	.sdecl	'.text.NvM_Queue.NvM_UnQueueJob',code,cluster('NvM_UnQueueJob')
	.sect	'.text.NvM_Queue.NvM_UnQueueJob'
	.align	2
	
	.global	NvM_UnQueueJob

; ..\eeprom\NvM\NvM_Queue.c	   323  
; ..\eeprom\NvM\NvM_Queue.c	   324  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   325  *  NvM_UnQueueJob
; ..\eeprom\NvM\NvM_Queue.c	   326  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   327  /*!
; ..\eeprom\NvM\NvM_Queue.c	   328   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   329   *
; ..\eeprom\NvM\NvM_Queue.c	   330   *
; ..\eeprom\NvM\NvM_Queue.c	   331   *
; ..\eeprom\NvM\NvM_Queue.c	   332   *
; ..\eeprom\NvM\NvM_Queue.c	   333   *
; ..\eeprom\NvM\NvM_Queue.c	   334   *
; ..\eeprom\NvM\NvM_Queue.c	   335   *
; ..\eeprom\NvM\NvM_Queue.c	   336   *
; ..\eeprom\NvM\NvM_Queue.c	   337   */
; ..\eeprom\NvM\NvM_Queue.c	   338  FUNC(boolean, NVM_PRIVATE_CODE) NvM_UnQueueJob(NvM_BlockIdType BlockId)
; Function NvM_UnQueueJob
.L33:
NvM_UnQueueJob:	.type	func
	mov	d8,d4
	sub.a	a10,#8
.L166:

; ..\eeprom\NvM\NvM_Queue.c	   339  { 
; ..\eeprom\NvM\NvM_Queue.c	   340     const NvM_RamMngmtPtrType NvM_RamMngmt_ptloc =
; ..\eeprom\NvM\NvM_Queue.c	   341      ((BlockId & NVM_DCM_BLOCK_OFFSET) != 0u) ? (&NvM_DcmBlockMngmt_t) : (&NvM_BlockMngmtArea_at[BlockId]);
	jz.t	d8:15,.L10
.L230:
	movh.a	a12,#@his(NvM_DcmBlockMngmt_t)
	lea	a12,[a12]@los(NvM_DcmBlockMngmt_t)
.L231:
	j	.L11
.L10:
	movh.a	a15,#@his(NvM_BlockMngmtArea_at)
	lea	a15,[a15]@los(NvM_BlockMngmtArea_at)
.L232:
	addsc.a	a12,a15,d8,#2
.L11:

; ..\eeprom\NvM\NvM_Queue.c	   342      boolean retVal = FALSE;
; ..\eeprom\NvM\NvM_Queue.c	   343      NvM_QueueEntryRefType elem; 
; ..\eeprom\NvM\NvM_Queue.c	   344      P2VAR(NvM_JobQueueType, AUTOMATIC, NVM_PRIVATE_DATA) UsedQueue = &NvM_NormalPrioQueue;
; ..\eeprom\NvM\NvM_Queue.c	   345  
; ..\eeprom\NvM\NvM_Queue.c	   346      /* Entire Search is a critical section, since pre-emption might change the queue (esp. list head),
; ..\eeprom\NvM\NvM_Queue.c	   347         which might cause the loop to never finish (especially when NvM_MainFunction empties the queue */
; ..\eeprom\NvM\NvM_Queue.c	   348      NvM_EnterCriticalSection();
	mov	d9,#0
	call	NvM_EnterCriticalSection
.L167:
	movh.a	a13,#@his(NvM_NormalPrioQueue)
	lea	a13,[a13]@los(NvM_NormalPrioQueue)
.L233:

; ..\eeprom\NvM\NvM_Queue.c	   349  
; ..\eeprom\NvM\NvM_Queue.c	   350      /* If NVM_JOB_PRIORISATION is ON we might have two queues to scan:                     *
; ..\eeprom\NvM\NvM_Queue.c	   351       *   - For a HighPrio job (prio == 0) both queues (Reads are not high prio!)           *
; ..\eeprom\NvM\NvM_Queue.c	   352       *   - For a Low Prio job (prio != 0) only the normal prio queue                       *
; ..\eeprom\NvM\NvM_Queue.c	   353       * ==> Always start searching in NormalPrioQueue                                       *
; ..\eeprom\NvM\NvM_Queue.c	   354       * ==> conditionally search HighPrioQueue.                                             *
; ..\eeprom\NvM\NvM_Queue.c	   355       * After that UsedQueue points to the Queue where the Block was found                  */
; ..\eeprom\NvM\NvM_Queue.c	   356  
; ..\eeprom\NvM\NvM_Queue.c	   357      elem = NvM_QueueFindBlock(UsedQueue->SrvList, BlockId);
	ld.bu	d4,[a13]
.L234:
	mov	d5,d8
	call	NvM_QueueFindBlock
.L168:

; ..\eeprom\NvM\NvM_Queue.c	   358  
; ..\eeprom\NvM\NvM_Queue.c	   359  #if(NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   360      /* If nothing was found in normal prio queue, look into high prio queue, if it is a high prio block *
; ..\eeprom\NvM\NvM_Queue.c	   361       * (and not its DCM-alias)                                                                          */
; ..\eeprom\NvM\NvM_Queue.c	   362      if((elem == NVM_LIST_END) &&
; ..\eeprom\NvM\NvM_Queue.c	   363          (NvM_BlockDescriptorTable_at[NVM_BLOCK_FROM_DCM_ID(BlockId)].BlockPrio_u8 == 0u) &&
; ..\eeprom\NvM\NvM_Queue.c	   364          ((BlockId & NVM_DCM_BLOCK_OFFSET) == 0u))
; ..\eeprom\NvM\NvM_Queue.c	   365      {
; ..\eeprom\NvM\NvM_Queue.c	   366          UsedQueue = &NvM_HighPrioQueue;
; ..\eeprom\NvM\NvM_Queue.c	   367          elem = NvM_QueueFindBlock(UsedQueue->SrvList, BlockId);
; ..\eeprom\NvM\NvM_Queue.c	   368      }
; ..\eeprom\NvM\NvM_Queue.c	   369  #endif
; ..\eeprom\NvM\NvM_Queue.c	   370  
; ..\eeprom\NvM\NvM_Queue.c	   371      /* If block was found, remove it from the queue <UsedQueue>,    *
; ..\eeprom\NvM\NvM_Queue.c	   372       *  free the queue Item and set block's error/status to CANCELED */
; ..\eeprom\NvM\NvM_Queue.c	   373      if(elem != NVM_LIST_END)
	mov	d15,#255
.L235:
	jeq	d15,d2,.L12
.L120:

; ..\eeprom\NvM\NvM_Queue.c	   374      {
; ..\eeprom\NvM\NvM_Queue.c	   375          const NvM_InternalServiceIdType srvId = NvM_JobQueue_at[elem].ServiceId;
	mul	d15,d2,#12
	fcall	.cocofun_1
.L236:
	addsc.a	a15,a2,d15,#0
.L237:

; ..\eeprom\NvM\NvM_Queue.c	   376          const NvM_ServiceIdType   PublicServiceId =  NvM_IntServiceDescrTable_at[srvId].PublicFid_t;
; ..\eeprom\NvM\NvM_Queue.c	   377          NvM_QueueEntryRefType tmpElem;
; ..\eeprom\NvM\NvM_Queue.c	   378  
; ..\eeprom\NvM\NvM_Queue.c	   379          retVal = TRUE;
; ..\eeprom\NvM\NvM_Queue.c	   380  
; ..\eeprom\NvM\NvM_Queue.c	   381  
; ..\eeprom\NvM\NvM_Queue.c	   382          /* pop "elem" of queue -> pretend "elem" being the head of the job queue. 
; ..\eeprom\NvM\NvM_Queue.c	   383           * After that "elem" point to its successor (if any); tmpElem points to original entry */
; ..\eeprom\NvM\NvM_Queue.c	   384          tmpElem = NvM_QueuePop(&elem); /* SBSW_NvM_FuncCall_PtrParam_Queue */
	mov	d9,#1
	mov.aa	a4,a10
.L238:
	ld.bu	d15,[a15]6
.L239:
	movh.a	a15,#@his(NvM_IntServiceDescrTable_at)
	lea	a15,[a15]@los(NvM_IntServiceDescrTable_at)
.L240:
	addsc.a	a15,a15,d15,#2
.L241:
	ld.bu	d10,[a15]2
.L170:
	st.b	[a10],d2
	call	NvM_QueuePop
.L169:

; ..\eeprom\NvM\NvM_Queue.c	   385  
; ..\eeprom\NvM\NvM_Queue.c	   386          /* if we removed the first element from the queue, we need to adjust the real queue head.  *
; ..\eeprom\NvM\NvM_Queue.c	   387           * Otherwise it was not changed by removing the "elem"                                     */    
; ..\eeprom\NvM\NvM_Queue.c	   388          if(UsedQueue->SrvList == tmpElem)
	ld.bu	d15,[a13]
.L242:
	ld.bu	d0,[a10]
.L172:
	jne	d15,d2,.L13
.L243:

; ..\eeprom\NvM\NvM_Queue.c	   389          {
; ..\eeprom\NvM\NvM_Queue.c	   390              UsedQueue->SrvList = elem; /* SBSW_NvM_AccessPtr_UsedQueue */
	st.b	[a13],d0
.L13:

; ..\eeprom\NvM\NvM_Queue.c	   391          }
; ..\eeprom\NvM\NvM_Queue.c	   392        
; ..\eeprom\NvM\NvM_Queue.c	   393          /* free the element --> add it to free-list; normally "elem" does not point to the correct entry anymore */
; ..\eeprom\NvM\NvM_Queue.c	   394          NvM_QueuePush(&UsedQueue->EmptyList, tmpElem); /* SBSW_NvM_FuncCall_PtrParam_Queue */
	add.a	a13,#1
.L244:
	mov.aa	a4,a13
	mov	d4,d2
	call	NvM_QueuePush
.L171:

; ..\eeprom\NvM\NvM_Queue.c	   395  
; ..\eeprom\NvM\NvM_Queue.c	   396          NvM_RamMngmt_ptloc->NvRamErrorStatus_u8 = NVM_REQ_CANCELED; /* SBSW_NvM_AccessBlockManagementArea */
	mov	d6,#6
	st.b	[a12]1,d6
.L245:

; ..\eeprom\NvM\NvM_Queue.c	   397          NvM_BlockNotification(BlockId, PublicServiceId, NVM_REQ_CANCELED);
	mov	e4,d10,d8
	call	NvM_BlockNotification
.L12:

; ..\eeprom\NvM\NvM_Queue.c	   398      }
; ..\eeprom\NvM\NvM_Queue.c	   399  
; ..\eeprom\NvM\NvM_Queue.c	   400      NvM_ExitCriticalSection();
	call	NvM_ExitCriticalSection
.L246:

; ..\eeprom\NvM\NvM_Queue.c	   401  
; ..\eeprom\NvM\NvM_Queue.c	   402      return retVal;
; ..\eeprom\NvM\NvM_Queue.c	   403  } 
	mov	d2,d9
	ret
.L116:
	
__NvM_UnQueueJob_function_end:
	.size	NvM_UnQueueJob,__NvM_UnQueueJob_function_end-NvM_UnQueueJob
.L62:
	; End of function
	
	.sdecl	'.text.NvM_Queue.NvM_QryNormalPrioJob',code,cluster('NvM_QryNormalPrioJob')
	.sect	'.text.NvM_Queue.NvM_QryNormalPrioJob'
	.align	2
	
	.global	NvM_QryNormalPrioJob

; ..\eeprom\NvM\NvM_Queue.c	   404  
; ..\eeprom\NvM\NvM_Queue.c	   405  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   406  *  NvM_QryNormalPrioJob
; ..\eeprom\NvM\NvM_Queue.c	   407  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   408  /*!
; ..\eeprom\NvM\NvM_Queue.c	   409   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   410   *
; ..\eeprom\NvM\NvM_Queue.c	   411   *
; ..\eeprom\NvM\NvM_Queue.c	   412   */
; ..\eeprom\NvM\NvM_Queue.c	   413  FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryNormalPrioJob(void)
; Function NvM_QryNormalPrioJob
.L35:
NvM_QryNormalPrioJob:	.type	func

; ..\eeprom\NvM\NvM_Queue.c	   414  {
; ..\eeprom\NvM\NvM_Queue.c	   415      return (boolean)(NvM_NormalPrioQueue.SrvList != NVM_LIST_END);
	movh.a	a15,#@his(NvM_NormalPrioQueue)
.L251:
	ld.bu	d15,[a15]@los(NvM_NormalPrioQueue)
.L252:

; ..\eeprom\NvM\NvM_Queue.c	   416  }
	ne	d2,d15,#255
	ret
.L124:
	
__NvM_QryNormalPrioJob_function_end:
	.size	NvM_QryNormalPrioJob,__NvM_QryNormalPrioJob_function_end-NvM_QryNormalPrioJob
.L67:
	; End of function
	
	.sdecl	'.text.NvM_Queue.NvM_ActGetNormalPrioJob',code,cluster('NvM_ActGetNormalPrioJob')
	.sect	'.text.NvM_Queue.NvM_ActGetNormalPrioJob'
	.align	2
	
	.global	NvM_ActGetNormalPrioJob

; ..\eeprom\NvM\NvM_Queue.c	   417  
; ..\eeprom\NvM\NvM_Queue.c	   418  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   419  *  NvM_ActGetNormalPrioJOb
; ..\eeprom\NvM\NvM_Queue.c	   420  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   421  /*!
; ..\eeprom\NvM\NvM_Queue.c	   422   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   423   *
; ..\eeprom\NvM\NvM_Queue.c	   424   *
; ..\eeprom\NvM\NvM_Queue.c	   425   *
; ..\eeprom\NvM\NvM_Queue.c	   426   *
; ..\eeprom\NvM\NvM_Queue.c	   427   *
; ..\eeprom\NvM\NvM_Queue.c	   428   *
; ..\eeprom\NvM\NvM_Queue.c	   429   *
; ..\eeprom\NvM\NvM_Queue.c	   430   *
; ..\eeprom\NvM\NvM_Queue.c	   431   *
; ..\eeprom\NvM\NvM_Queue.c	   432   *
; ..\eeprom\NvM\NvM_Queue.c	   433   *
; ..\eeprom\NvM\NvM_Queue.c	   434   *
; ..\eeprom\NvM\NvM_Queue.c	   435   */
; ..\eeprom\NvM\NvM_Queue.c	   436  FUNC(void, NVM_PRIVATE_CODE) NvM_ActGetNormalPrioJob(void)
; Function NvM_ActGetNormalPrioJob
.L37:
NvM_ActGetNormalPrioJob:	.type	func

; ..\eeprom\NvM\NvM_Queue.c	   437  {
; ..\eeprom\NvM\NvM_Queue.c	   438  #if (NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   439      /* search for the highest prioritized entry */
; ..\eeprom\NvM\NvM_Queue.c	   440      NvM_QueueEntryRefType maxPrioElem;
; ..\eeprom\NvM\NvM_Queue.c	   441      NvM_QueueEntryRefType currentElem;
; ..\eeprom\NvM\NvM_Queue.c	   442      uint8 maxPrio;
; ..\eeprom\NvM\NvM_Queue.c	   443  
; ..\eeprom\NvM\NvM_Queue.c	   444      NvM_EnterCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   445      /* We have to guarantee, that we really get the last element.
; ..\eeprom\NvM\NvM_Queue.c	   446       * We only get it, as long as our head element remains the head element.
; ..\eeprom\NvM\NvM_Queue.c	   447       * Otherwise it would just point to its predecessor. */
; ..\eeprom\NvM\NvM_Queue.c	   448      currentElem = NvM_JobQueue_at[NvM_NormalPrioQueue.SrvList].PrevEntry;
; ..\eeprom\NvM\NvM_Queue.c	   449      maxPrioElem = NvM_JobQueue_at[NvM_NormalPrioQueue.SrvList].PrevEntry;
; ..\eeprom\NvM\NvM_Queue.c	   450  
; ..\eeprom\NvM\NvM_Queue.c	   451      maxPrio = NvM_JobQueue_at[maxPrioElem].JobPrio;
; ..\eeprom\NvM\NvM_Queue.c	   452  
; ..\eeprom\NvM\NvM_Queue.c	   453      NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   454  
; ..\eeprom\NvM\NvM_Queue.c	   455      /* the actual iteration over the queue is not a critical section,
; ..\eeprom\NvM\NvM_Queue.c	   456         because the consistency of the list (prev and next pointers) is guaranteed by
; ..\eeprom\NvM\NvM_Queue.c	   457         NvM_QueueJob()
; ..\eeprom\NvM\NvM_Queue.c	   458         If the queue contains only one element, it will be checked twice
; ..\eeprom\NvM\NvM_Queue.c	   459         (one iteration - it will be compared with itself -> that's no problem */
; ..\eeprom\NvM\NvM_Queue.c	   460      do
; ..\eeprom\NvM\NvM_Queue.c	   461      {
; ..\eeprom\NvM\NvM_Queue.c	   462          currentElem = NvM_JobQueue_at[currentElem].PrevEntry;
; ..\eeprom\NvM\NvM_Queue.c	   463  
; ..\eeprom\NvM\NvM_Queue.c	   464          if(NvM_JobQueue_at[currentElem].JobPrio < maxPrio)
; ..\eeprom\NvM\NvM_Queue.c	   465          {
; ..\eeprom\NvM\NvM_Queue.c	   466              maxPrioElem = currentElem;
; ..\eeprom\NvM\NvM_Queue.c	   467              maxPrio = NvM_JobQueue_at[currentElem].JobPrio;
; ..\eeprom\NvM\NvM_Queue.c	   468          }
; ..\eeprom\NvM\NvM_Queue.c	   469      }
; ..\eeprom\NvM\NvM_Queue.c	   470      while(currentElem != NvM_NormalPrioQueue.SrvList);
; ..\eeprom\NvM\NvM_Queue.c	   471  
; ..\eeprom\NvM\NvM_Queue.c	   472  
; ..\eeprom\NvM\NvM_Queue.c	   473      /* now, we have the entry of interest. */
; ..\eeprom\NvM\NvM_Queue.c	   474      NvM_LastJobEntry = maxPrioElem;
; ..\eeprom\NvM\NvM_Queue.c	   475  
; ..\eeprom\NvM\NvM_Queue.c	   476      NvM_CurrentJob_t.JobBlockId_t = NvM_JobQueue_at[maxPrioElem].BlockId;
; ..\eeprom\NvM\NvM_Queue.c	   477      NvM_CurrentJob_t.JobServiceId_t = NvM_JobQueue_at[maxPrioElem].ServiceId;
; ..\eeprom\NvM\NvM_Queue.c	   478      NvM_CurrentJob_t.RamAddr_t = NvM_JobQueue_at[maxPrioElem].RamAddr_t;
; ..\eeprom\NvM\NvM_Queue.c	   479  
; ..\eeprom\NvM\NvM_Queue.c	   480  
; ..\eeprom\NvM\NvM_Queue.c	   481      /* remove entry from the queue, discard the return value */
; ..\eeprom\NvM\NvM_Queue.c	   482      NvM_EnterCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   483  
; ..\eeprom\NvM\NvM_Queue.c	   484      (void)NvM_QueuePop(&maxPrioElem); /* SBSW_NvM_FuncCall_PtrParam_Queue */
; ..\eeprom\NvM\NvM_Queue.c	   485  
; ..\eeprom\NvM\NvM_Queue.c	   486      /* if it was the first element of the queue, we need to adjust the real queue head.
; ..\eeprom\NvM\NvM_Queue.c	   487       * Otherwise it would not have been changed be removing the entry. */
; ..\eeprom\NvM\NvM_Queue.c	   488      if(NvM_NormalPrioQueue.SrvList == NvM_LastJobEntry)
; ..\eeprom\NvM\NvM_Queue.c	   489      {
; ..\eeprom\NvM\NvM_Queue.c	   490          NvM_NormalPrioQueue.SrvList = maxPrioElem;
; ..\eeprom\NvM\NvM_Queue.c	   491      }
; ..\eeprom\NvM\NvM_Queue.c	   492  
; ..\eeprom\NvM\NvM_Queue.c	   493      NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   494  #else
; ..\eeprom\NvM\NvM_Queue.c	   495  
; ..\eeprom\NvM\NvM_Queue.c	   496      NvM_QueueEntryRefType elem;
; ..\eeprom\NvM\NvM_Queue.c	   497  
; ..\eeprom\NvM\NvM_Queue.c	   498      NvM_EnterCriticalSection();
	call	NvM_EnterCriticalSection
.L257:

; ..\eeprom\NvM\NvM_Queue.c	   499      /* just take the last queue element, don't store it in NvM_LastJobEntry (it does not exist),
; ..\eeprom\NvM\NvM_Queue.c	   500       * but remove it from the queue.
; ..\eeprom\NvM\NvM_Queue.c	   501       * Just update the queue head to point to its prev element (which is the tail), then pop.
; ..\eeprom\NvM\NvM_Queue.c	   502         After that, the head points to the head again.
; ..\eeprom\NvM\NvM_Queue.c	   503       */
; ..\eeprom\NvM\NvM_Queue.c	   504      NvM_NormalPrioQueue.SrvList = NvM_JobQueue_at[NvM_NormalPrioQueue.SrvList].PrevEntry;
	movh.a	a12,#@his(NvM_NormalPrioQueue)
	lea	a12,[a12]@los(NvM_NormalPrioQueue)
.L258:
	ld.bu	d15,[a12]
.L259:
	movh.a	a13,#@his(NvM_JobQueue_at)
.L260:
	mul	d15,d15,#12
	lea	a13,[a13]@los(NvM_JobQueue_at)
.L261:

; ..\eeprom\NvM\NvM_Queue.c	   505      elem = NvM_QueuePop(&NvM_NormalPrioQueue.SrvList); /* SBSW_NvM_FuncCall_PtrParam_Queue */
	mov.aa	a4,a12
.L262:
	addsc.a	a15,a13,d15,#0
.L263:
	ld.bu	d15,[a15]8
.L264:
	st.b	[a12],d15
.L265:
	call	NvM_QueuePop
.L173:

; ..\eeprom\NvM\NvM_Queue.c	   506  
; ..\eeprom\NvM\NvM_Queue.c	   507      /* free the element --> add it to free-list */
; ..\eeprom\NvM\NvM_Queue.c	   508      NvM_QueuePush(&NvM_NormalPrioQueue.EmptyList, elem); /* SBSW_NvM_FuncCall_PtrParam_Queue */
	mov	d15,d2
	add.a	a12,#1
.L175:
	mov.aa	a4,a12
	mov	d4,d15
	call	NvM_QueuePush
.L174:

; ..\eeprom\NvM\NvM_Queue.c	   509  
; ..\eeprom\NvM\NvM_Queue.c	   510      NvM_CurrentJob_t.JobBlockId_t = NvM_JobQueue_at[elem].BlockId;
	mul	d15,d15,#12
	movh.a	a15,#@his(NvM_CurrentJob_t)
.L176:
	lea	a15,[a15]@los(NvM_CurrentJob_t)
.L266:
	addsc.a	a2,a13,d15,#0
.L267:
	ld.hu	d15,[a2]4
.L268:
	st.h	[a15],d15
.L269:

; ..\eeprom\NvM\NvM_Queue.c	   511      NvM_CurrentJob_t.JobServiceId_t = NvM_JobQueue_at[elem].ServiceId;
	ld.bu	d15,[a2]6
.L270:
	st.b	[a15]2,d15
.L271:

; ..\eeprom\NvM\NvM_Queue.c	   512      NvM_CurrentJob_t.RamAddr_t = NvM_JobQueue_at[elem].RamAddr_t;
	ld.a	a2,[a2]
.L272:
	st.a	[a15]4,a2
.L273:

; ..\eeprom\NvM\NvM_Queue.c	   513  
; ..\eeprom\NvM\NvM_Queue.c	   514  
; ..\eeprom\NvM\NvM_Queue.c	   515      NvM_ExitCriticalSection();
	j	NvM_ExitCriticalSection
.L125:
	
__NvM_ActGetNormalPrioJob_function_end:
	.size	NvM_ActGetNormalPrioJob,__NvM_ActGetNormalPrioJob_function_end-NvM_ActGetNormalPrioJob
.L72:
	; End of function
	
	.sdecl	'.text.NvM_Queue.NvM_QueuePush',code,cluster('NvM_QueuePush')
	.sect	'.text.NvM_Queue.NvM_QueuePush'
	.align	2
	

; ..\eeprom\NvM\NvM_Queue.c	   516  #endif /* (NVM_JOB_PRIORISATION == STD_ON) */
; ..\eeprom\NvM\NvM_Queue.c	   517  }
; ..\eeprom\NvM\NvM_Queue.c	   518  
; ..\eeprom\NvM\NvM_Queue.c	   519  #if (NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   520  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   521  *  NvM_QryHighPrioJob
; ..\eeprom\NvM\NvM_Queue.c	   522  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   523  /*!
; ..\eeprom\NvM\NvM_Queue.c	   524   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   525   *
; ..\eeprom\NvM\NvM_Queue.c	   526   *
; ..\eeprom\NvM\NvM_Queue.c	   527   */
; ..\eeprom\NvM\NvM_Queue.c	   528  FUNC(boolean, NVM_PRIVATE_CODE) NvM_QryHighPrioJob(void)
; ..\eeprom\NvM\NvM_Queue.c	   529  {
; ..\eeprom\NvM\NvM_Queue.c	   530      return (boolean)(NvM_HighPrioQueue.SrvList != NVM_LIST_END);
; ..\eeprom\NvM\NvM_Queue.c	   531  }
; ..\eeprom\NvM\NvM_Queue.c	   532  
; ..\eeprom\NvM\NvM_Queue.c	   533  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   534  *  NvM_ActGetHighPrioJob
; ..\eeprom\NvM\NvM_Queue.c	   535  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   536  /*!
; ..\eeprom\NvM\NvM_Queue.c	   537   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   538   *
; ..\eeprom\NvM\NvM_Queue.c	   539   *
; ..\eeprom\NvM\NvM_Queue.c	   540   *
; ..\eeprom\NvM\NvM_Queue.c	   541   *
; ..\eeprom\NvM\NvM_Queue.c	   542   *
; ..\eeprom\NvM\NvM_Queue.c	   543   */
; ..\eeprom\NvM\NvM_Queue.c	   544  FUNC(void, NVM_PRIVATE_CODE) NvM_ActGetHighPrioJob(void)
; ..\eeprom\NvM\NvM_Queue.c	   545  {
; ..\eeprom\NvM\NvM_Queue.c	   546      NvM_QueueEntryRefType elem;
; ..\eeprom\NvM\NvM_Queue.c	   547  
; ..\eeprom\NvM\NvM_Queue.c	   548      /* #10 Cancel the underlying module and re-queue the canceled job. This shall only be done
; ..\eeprom\NvM\NvM_Queue.c	   549       * in case currently there is a NORMAL PRIO or MULTI BLOCK (only if KilWriteAll enabled) job */
; ..\eeprom\NvM\NvM_Queue.c	   550  # if (NVM_KILL_WRITEALL_API == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   551      if((NvM_TaskState_t == NVM_STATE_NORMAL_PRIO_JOB) || (NvM_TaskState_t == NVM_STATE_MULTI_BLOCK_JOB))
; ..\eeprom\NvM\NvM_Queue.c	   552  # else
; ..\eeprom\NvM\NvM_Queue.c	   553      if(NVM_STATE_NORMAL_PRIO_JOB == NvM_TaskState_t)
; ..\eeprom\NvM\NvM_Queue.c	   554  #endif
; ..\eeprom\NvM\NvM_Queue.c	   555      {
; ..\eeprom\NvM\NvM_Queue.c	   556          NvM_ActCancelNV();
; ..\eeprom\NvM\NvM_Queue.c	   557  
; ..\eeprom\NvM\NvM_Queue.c	   558          NvM_QueueRequeueLastJob();
; ..\eeprom\NvM\NvM_Queue.c	   559      }
; ..\eeprom\NvM\NvM_Queue.c	   560  
; ..\eeprom\NvM\NvM_Queue.c	   561      NvM_EnterCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   562  
; ..\eeprom\NvM\NvM_Queue.c	   563      /* make the last entry (predecessor of list head) the current entry */
; ..\eeprom\NvM\NvM_Queue.c	   564      NvM_HighPrioQueue.SrvList = NvM_JobQueue_at[NvM_HighPrioQueue.SrvList].PrevEntry;
; ..\eeprom\NvM\NvM_Queue.c	   565  
; ..\eeprom\NvM\NvM_Queue.c	   566      /* get the head element (it's actually the last one) from list, the new head is its successor,
; ..\eeprom\NvM\NvM_Queue.c	   567       * which is the original head */
; ..\eeprom\NvM\NvM_Queue.c	   568      elem = NvM_QueuePop(&NvM_HighPrioQueue.SrvList); /* SBSW_NvM_FuncCall_PtrParam_Queue */
; ..\eeprom\NvM\NvM_Queue.c	   569  
; ..\eeprom\NvM\NvM_Queue.c	   570      /* an immediate write job cannot be interrupted, therefore we can free the list element */
; ..\eeprom\NvM\NvM_Queue.c	   571      NvM_QueuePush(&NvM_HighPrioQueue.EmptyList, elem); /* SBSW_NvM_FuncCall_PtrParam_Queue */
; ..\eeprom\NvM\NvM_Queue.c	   572  
; ..\eeprom\NvM\NvM_Queue.c	   573      NvM_CurrentJob_t.JobBlockId_t   = NvM_JobQueue_at[elem].BlockId;
; ..\eeprom\NvM\NvM_Queue.c	   574      NvM_CurrentJob_t.JobServiceId_t = NvM_JobQueue_at[elem].ServiceId;
; ..\eeprom\NvM\NvM_Queue.c	   575      NvM_CurrentJob_t.RamAddr_t      = NvM_JobQueue_at[elem].RamAddr_t;
; ..\eeprom\NvM\NvM_Queue.c	   576  
; ..\eeprom\NvM\NvM_Queue.c	   577      /* make sure that the last job entry is empty --> no re-queuing will be performed */
; ..\eeprom\NvM\NvM_Queue.c	   578      NvM_LastJobEntry = NVM_LIST_END;
; ..\eeprom\NvM\NvM_Queue.c	   579  
; ..\eeprom\NvM\NvM_Queue.c	   580      NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   581  }
; ..\eeprom\NvM\NvM_Queue.c	   582  
; ..\eeprom\NvM\NvM_Queue.c	   583  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   584  *  NvM_ActQueueFreeLastJob
; ..\eeprom\NvM\NvM_Queue.c	   585  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   586  /*!
; ..\eeprom\NvM\NvM_Queue.c	   587   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   588   *
; ..\eeprom\NvM\NvM_Queue.c	   589   *
; ..\eeprom\NvM\NvM_Queue.c	   590   *
; ..\eeprom\NvM\NvM_Queue.c	   591   *
; ..\eeprom\NvM\NvM_Queue.c	   592   */
; ..\eeprom\NvM\NvM_Queue.c	   593  FUNC(void, NVM_PRIVATE_CODE) NvM_ActQueueFreeLastJob(void)
; ..\eeprom\NvM\NvM_Queue.c	   594  {
; ..\eeprom\NvM\NvM_Queue.c	   595      if(NvM_LastJobEntry != NVM_LIST_END)
; ..\eeprom\NvM\NvM_Queue.c	   596      {
; ..\eeprom\NvM\NvM_Queue.c	   597          NvM_EnterCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   598          /* add element at list start */
; ..\eeprom\NvM\NvM_Queue.c	   599          NvM_QueuePush(&NvM_NormalPrioQueue.EmptyList, NvM_LastJobEntry); /* SBSW_NvM_FuncCall_PtrParam_Queue */
; ..\eeprom\NvM\NvM_Queue.c	   600  
; ..\eeprom\NvM\NvM_Queue.c	   601          NvM_LastJobEntry = NVM_LIST_END;
; ..\eeprom\NvM\NvM_Queue.c	   602          NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   603      }
; ..\eeprom\NvM\NvM_Queue.c	   604  }
; ..\eeprom\NvM\NvM_Queue.c	   605  #endif /* (NVM_JOB_PRIORISATION == STD_ON) */
; ..\eeprom\NvM\NvM_Queue.c	   606  
; ..\eeprom\NvM\NvM_Queue.c	   607  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   608  *  NvM_QueuePush
; ..\eeprom\NvM\NvM_Queue.c	   609  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   610  /*!
; ..\eeprom\NvM\NvM_Queue.c	   611   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   612   *
; ..\eeprom\NvM\NvM_Queue.c	   613   *
; ..\eeprom\NvM\NvM_Queue.c	   614   *
; ..\eeprom\NvM\NvM_Queue.c	   615   *
; ..\eeprom\NvM\NvM_Queue.c	   616   *
; ..\eeprom\NvM\NvM_Queue.c	   617   *
; ..\eeprom\NvM\NvM_Queue.c	   618   */
; ..\eeprom\NvM\NvM_Queue.c	   619  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_QueuePush(NvM_QueueListHeadRefType Queue, NvM_QueueEntryRefType Elem)
; Function NvM_QueuePush
.L39:
NvM_QueuePush:	.type	func

; ..\eeprom\NvM\NvM_Queue.c	   620  {
; ..\eeprom\NvM\NvM_Queue.c	   621      CONSTP2VAR(NvM_QueueEntryType, AUTOMATIC, NVM_PRIVATE_DATA) elemPtr = &NvM_JobQueue_at[Elem];
	fcall	.cocofun_1
.L177:

; ..\eeprom\NvM\NvM_Queue.c	   622  
; ..\eeprom\NvM\NvM_Queue.c	   623      if(*Queue == NVM_LIST_END)
	mul	d15,d4,#12
	ld.bu	d0,[a4]
.L278:
	addsc.a	a15,a2,d15,#0
.L178:
	mov	d15,#255
.L279:
	jne	d15,d0,.L16
.L280:

; ..\eeprom\NvM\NvM_Queue.c	   624      {
; ..\eeprom\NvM\NvM_Queue.c	   625          /* queue is currently empty. put new element in, link it with itself */
; ..\eeprom\NvM\NvM_Queue.c	   626          *Queue = Elem; /* SBSW_NvM_AccessPtr_QueueIndex */
	st.b	[a4],d4
.L281:

; ..\eeprom\NvM\NvM_Queue.c	   627          elemPtr->NextEntry = Elem; /* SBSW_NvM_AccessJobQueue */
	st.b	[a15]7,d4
.L282:

; ..\eeprom\NvM\NvM_Queue.c	   628          elemPtr->PrevEntry = Elem; /* SBSW_NvM_AccessJobQueue */
	st.b	[a15]8,d4
.L283:

; ..\eeprom\NvM\NvM_Queue.c	   629      }
; ..\eeprom\NvM\NvM_Queue.c	   630      else
; ..\eeprom\NvM\NvM_Queue.c	   631      {
; ..\eeprom\NvM\NvM_Queue.c	   632          CONSTP2VAR(NvM_QueueEntryType, AUTOMATIC, NVM_PRIVATE_DATA) NextPtr = &NvM_JobQueue_at[*Queue];
; ..\eeprom\NvM\NvM_Queue.c	   633  
; ..\eeprom\NvM\NvM_Queue.c	   634          elemPtr->NextEntry = *Queue; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   635          elemPtr->PrevEntry = NextPtr->PrevEntry;  /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   636  
; ..\eeprom\NvM\NvM_Queue.c	   637          NvM_JobQueue_at[NextPtr->PrevEntry].NextEntry = Elem; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   638          NextPtr->PrevEntry = Elem; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   639          *Queue = Elem; /* SBSW_NvM_AccessPtr_QueueIndex */
; ..\eeprom\NvM\NvM_Queue.c	   640      }
; ..\eeprom\NvM\NvM_Queue.c	   641  }
	ret
.L16:
	mul	d15,d0,#12
	st.b	[a15]7,d0
.L284:
	addsc.a	a5,a2,d15,#0
.L180:
	ld.bu	d15,[a5]8
.L285:
	st.b	[a15]8,d15
.L286:
	ld.bu	d15,[a5]8
.L287:
	mul	d15,d15,#12
	addsc.a	a15,a2,d15,#0
.L179:
	st.b	[a15]7,d4
.L288:
	st.b	[a5]8,d4
.L289:
	st.b	[a4],d4
.L133:
	ret
.L127:
	
__NvM_QueuePush_function_end:
	.size	NvM_QueuePush,__NvM_QueuePush_function_end-NvM_QueuePush
.L77:
	; End of function
	
	.sdecl	'.text.NvM_Queue.NvM_QueuePop',code,cluster('NvM_QueuePop')
	.sect	'.text.NvM_Queue.NvM_QueuePop'
	.align	2
	

; ..\eeprom\NvM\NvM_Queue.c	   642  
; ..\eeprom\NvM\NvM_Queue.c	   643  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   644  *  NvM_QueuePop
; ..\eeprom\NvM\NvM_Queue.c	   645  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   646  /*!
; ..\eeprom\NvM\NvM_Queue.c	   647   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   648   *
; ..\eeprom\NvM\NvM_Queue.c	   649   *
; ..\eeprom\NvM\NvM_Queue.c	   650   *
; ..\eeprom\NvM\NvM_Queue.c	   651   *
; ..\eeprom\NvM\NvM_Queue.c	   652   *
; ..\eeprom\NvM\NvM_Queue.c	   653   *
; ..\eeprom\NvM\NvM_Queue.c	   654   *
; ..\eeprom\NvM\NvM_Queue.c	   655   */
; ..\eeprom\NvM\NvM_Queue.c	   656  NVM_LOCAL FUNC(NvM_QueueEntryRefType, NVM_PRIVATE_CODE) NvM_QueuePop(NvM_QueueListHeadRefType Queue)
; Function NvM_QueuePop
.L41:
NvM_QueuePop:	.type	func

; ..\eeprom\NvM\NvM_Queue.c	   657  {
; ..\eeprom\NvM\NvM_Queue.c	   658      const uint8 retVal = *Queue;
	ld.bu	d2,[a4]
.L182:

; ..\eeprom\NvM\NvM_Queue.c	   659      CONSTP2VAR(NvM_QueueEntryType, AUTOMATIC, NVM_PRIVATE_DATA) elemPtr = &NvM_JobQueue_at[retVal];
	fcall	.cocofun_1
.L294:
	mul	d15,d2,#12
	addsc.a	a15,a2,d15,#0
.L183:

; ..\eeprom\NvM\NvM_Queue.c	   660  
; ..\eeprom\NvM\NvM_Queue.c	   661      if(elemPtr->NextEntry == retVal)
	ld.bu	d15,[a15]7
.L295:
	jne	d15,d2,.L18
.L296:

; ..\eeprom\NvM\NvM_Queue.c	   662      {
; ..\eeprom\NvM\NvM_Queue.c	   663          /* element is linked with itself, therefore the list is empty, now */
; ..\eeprom\NvM\NvM_Queue.c	   664          *Queue = NVM_LIST_END; /* SBSW_NvM_AccessPtr_QueueIndex */
	mov	d15,#255
	st.b	[a4],d15
.L297:

; ..\eeprom\NvM\NvM_Queue.c	   665      }
; ..\eeprom\NvM\NvM_Queue.c	   666      else
; ..\eeprom\NvM\NvM_Queue.c	   667      {
; ..\eeprom\NvM\NvM_Queue.c	   668          *Queue = elemPtr->NextEntry; /* SBSW_NvM_AccessPtr_QueueIndex */
; ..\eeprom\NvM\NvM_Queue.c	   669  
; ..\eeprom\NvM\NvM_Queue.c	   670          NvM_JobQueue_at[elemPtr->NextEntry].PrevEntry = elemPtr->PrevEntry; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   671          NvM_JobQueue_at[elemPtr->PrevEntry].NextEntry = elemPtr->NextEntry; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   672      }
; ..\eeprom\NvM\NvM_Queue.c	   673  
; ..\eeprom\NvM\NvM_Queue.c	   674      return retVal;
; ..\eeprom\NvM\NvM_Queue.c	   675  }
	ret
.L18:
	st.b	[a4],d15
.L298:
	ld.bu	d15,[a15]7
.L299:
	mul	d15,d15,#12
	addsc.a	a4,a2,d15,#0
.L181:
	ld.bu	d15,[a15]8
.L300:
	st.b	[a4]8,d15
.L301:
	ld.bu	d15,[a15]8
.L302:
	mul	d15,d15,#12
	addsc.a	a2,a2,d15,#0
.L303:
	ld.bu	d15,[a15]7
.L304:
	st.b	[a2]7,d15
.L305:
	ret
.L136:
	
__NvM_QueuePop_function_end:
	.size	NvM_QueuePop,__NvM_QueuePop_function_end-NvM_QueuePop
.L82:
	; End of function
	
	.sdecl	'.text.NvM_Queue.NvM_QueueFindBlock',code,cluster('NvM_QueueFindBlock')
	.sect	'.text.NvM_Queue.NvM_QueueFindBlock'
	.align	2
	

; ..\eeprom\NvM\NvM_Queue.c	   676  
; ..\eeprom\NvM\NvM_Queue.c	   677  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   678  *  NvM_QueueFindBlock
; ..\eeprom\NvM\NvM_Queue.c	   679  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   680  /*!
; ..\eeprom\NvM\NvM_Queue.c	   681   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   682   *
; ..\eeprom\NvM\NvM_Queue.c	   683   *
; ..\eeprom\NvM\NvM_Queue.c	   684   *
; ..\eeprom\NvM\NvM_Queue.c	   685   *
; ..\eeprom\NvM\NvM_Queue.c	   686   */
; ..\eeprom\NvM\NvM_Queue.c	   687  NVM_LOCAL FUNC(NvM_QueueEntryRefType, NVM_PRIVATE_CODE) NvM_QueueFindBlock(NvM_QueueEntryRefType QueueHead, NvM_BlockIdType BlockId)
; Function NvM_QueueFindBlock
.L43:
NvM_QueueFindBlock:	.type	func

; ..\eeprom\NvM\NvM_Queue.c	   688  {
; ..\eeprom\NvM\NvM_Queue.c	   689      NvM_QueueEntryRefType entryRef = NVM_LIST_END;
	mov	d2,#255
.L184:

; ..\eeprom\NvM\NvM_Queue.c	   690  
; ..\eeprom\NvM\NvM_Queue.c	   691      if(QueueHead != NVM_LIST_END)
	jeq	d4,d2,.L21
.L147:

; ..\eeprom\NvM\NvM_Queue.c	   692      {
; ..\eeprom\NvM\NvM_Queue.c	   693          NvM_QueueEntryRefType elem = QueueHead;
; ..\eeprom\NvM\NvM_Queue.c	   694          /* search for the block in the queue and prevent from scanning an empty queue */
; ..\eeprom\NvM\NvM_Queue.c	   695          do
; ..\eeprom\NvM\NvM_Queue.c	   696          {
; ..\eeprom\NvM\NvM_Queue.c	   697              elem = NvM_JobQueue_at[elem].NextEntry;
	mov	d0,d4
	fcall	.cocofun_1

; ..\eeprom\NvM\NvM_Queue.c	   698  
; ..\eeprom\NvM\NvM_Queue.c	   699              if(NvM_JobQueue_at[elem].BlockId == BlockId)
; ..\eeprom\NvM\NvM_Queue.c	   700              {
; ..\eeprom\NvM\NvM_Queue.c	   701                  /* found the entry */
; ..\eeprom\NvM\NvM_Queue.c	   702                  entryRef = elem;
; ..\eeprom\NvM\NvM_Queue.c	   703                  break;
; ..\eeprom\NvM\NvM_Queue.c	   704              }
; ..\eeprom\NvM\NvM_Queue.c	   705          }
; ..\eeprom\NvM\NvM_Queue.c	   706          while(elem != QueueHead);
.L22:
	mul	d15,d0,#12
	addsc.a	a15,a2,d15,#0
.L310:
	ld.bu	d0,[a15]7
.L311:
	mul	d15,d0,#12
	addsc.a	a15,a2,d15,#0
.L312:
	ld.hu	d15,[a15]4
.L313:
	jne	d15,d5,.L23
.L314:
	mov	d2,d0
.L21:

; ..\eeprom\NvM\NvM_Queue.c	   707      }
; ..\eeprom\NvM\NvM_Queue.c	   708  
; ..\eeprom\NvM\NvM_Queue.c	   709      return entryRef;
; ..\eeprom\NvM\NvM_Queue.c	   710  }
	ret
.L23:
	jne	d0,d4,.L22
.L148:
	ret
.L142:
	
__NvM_QueueFindBlock_function_end:
	.size	NvM_QueueFindBlock,__NvM_QueueFindBlock_function_end-NvM_QueueFindBlock
.L87:
	; End of function
	
	.sdecl	'.bss.NvM_Queue.NvM_CurrentJob_t',data,cluster('NvM_CurrentJob_t')
	.sect	'.bss.NvM_Queue.NvM_CurrentJob_t'
	.global	NvM_CurrentJob_t
	.align	4
NvM_CurrentJob_t:	.type	object
	.size	NvM_CurrentJob_t,8
	.space	8
	.sdecl	'.bss.NvM_Queue.NvM_NormalPrioQueue',data,cluster('NvM_NormalPrioQueue')
	.sect	'.bss.NvM_Queue.NvM_NormalPrioQueue'
	.align	2
NvM_NormalPrioQueue:	.type	object
	.size	NvM_NormalPrioQueue,2
	.space	2
	.calls	'NvM_QueueJob','NvM_EnterCriticalSection'
	.calls	'NvM_QueueJob','NvM_QueuePop'
	.calls	'NvM_QueueJob','NvM_QueuePush'
	.calls	'NvM_QueueJob','NvM_ExitCriticalSection'
	.calls	'NvM_QueueJob','NvM_BlockNotification'
	.calls	'NvM_UnQueueJob','NvM_EnterCriticalSection'
	.calls	'NvM_UnQueueJob','NvM_QueueFindBlock'
	.calls	'NvM_UnQueueJob','NvM_QueuePop'
	.calls	'NvM_UnQueueJob','NvM_QueuePush'
	.calls	'NvM_UnQueueJob','NvM_BlockNotification'
	.calls	'NvM_UnQueueJob','NvM_ExitCriticalSection'
	.calls	'NvM_ActGetNormalPrioJob','NvM_EnterCriticalSection'
	.calls	'NvM_ActGetNormalPrioJob','NvM_QueuePop'
	.calls	'NvM_ActGetNormalPrioJob','NvM_QueuePush'
	.calls	'NvM_ActGetNormalPrioJob','NvM_ExitCriticalSection'
	.calls	'NvM_QueueJob','.cocofun_1'
	.calls	'NvM_UnQueueJob','.cocofun_1'
	.calls	'NvM_QueuePush','.cocofun_1'
	.calls	'NvM_QueuePop','.cocofun_1'
	.calls	'NvM_QueueFindBlock','.cocofun_1'
	.calls	'NvM_QueueInit','',0
	.calls	'NvM_QueueJob','',0
	.calls	'.cocofun_1','',0
	.calls	'NvM_UnQueueJob','',8
	.calls	'NvM_QryNormalPrioJob','',0
	.calls	'NvM_ActGetNormalPrioJob','',0
	.calls	'NvM_QueuePush','',0
	.calls	'NvM_QueuePop','',0
	.extern	NvM_EnterCriticalSection
	.extern	NvM_ExitCriticalSection
	.extern	NvM_JobQueue_at
	.extern	NvM_BlockMngmtArea_at
	.extern	NvM_DcmBlockMngmt_t
	.extern	NvM_BlockNotification
	.extern	NvM_IntServiceDescrTable_at
	.calls	'NvM_QueueFindBlock','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L45:
	.word	9841
	.half	3
	.word	.L46
	.byte	4
.L44:
	.byte	1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L47
	.byte	2
	.byte	'NvM_EnterCriticalSection',0,1,192,2,37,1,1,1,1,2
	.byte	'NvM_ExitCriticalSection',0,1,204,2,37,1,1,1,1,3
	.byte	'NvM_BlockNotification',0,2,192,1,37,1,1,1,1
.L101:
	.byte	4
	.byte	'unsigned short int',0,2,7,5
	.byte	'BlockId',0,2,192,1,75
	.word	278
.L98:
	.byte	4
	.byte	'unsigned char',0,1,8,5
	.byte	'ServiceId',0,2,192,1,102
	.word	317
	.byte	5
	.byte	'JobResult',0,2,192,1,135,1
	.word	317
	.byte	0
.L103:
	.byte	6,1,243,1,9,1,7
	.byte	'NVM_INT_FID_WRITE_BLOCK',0,0,7
	.byte	'NVM_INT_FID_READ_BLOCK',0,1,7
	.byte	'NVM_INT_FID_RESTORE_DEFAULTS',0,2,7
	.byte	'NVM_INT_FID_INVALIDATE_NV_BLOCK',0,3,7
	.byte	'NVM_INT_FID_ERASE_BLOCK',0,4,7
	.byte	'NVM_INT_FID_WRITE_ALL',0,5,7
	.byte	'NVM_INT_FID_READ_ALL',0,6,7
	.byte	'NVM_INT_FID_REPAIR_REDUNDANT_BLOCKS',0,7,7
	.byte	'NVM_INT_FID_NO_JOB_PENDING',0,8,0,8
	.word	317
	.byte	8
	.word	317
.L105:
	.byte	9
	.byte	'NvM_RamAddressType',0,1,161,1,48
	.word	642
.L112:
	.byte	10
	.word	317
	.byte	11,1,132,2,9,12,12
	.byte	'RamAddr_t',0,4
	.word	647
	.byte	2,35,0,12
	.byte	'BlockId',0,2
	.word	278
	.byte	2,35,4,12
	.byte	'ServiceId',0,1
	.word	374
	.byte	2,35,6,12
	.byte	'NextEntry',0,1
	.word	317
	.byte	2,35,7,12
	.byte	'PrevEntry',0,1
	.word	317
	.byte	2,35,8,0,8
	.word	680
.L114:
	.byte	10
	.word	780
.L121:
	.byte	10
	.word	317
	.byte	8
	.word	317
	.byte	8
	.word	317
.L128:
	.byte	9
	.byte	'NvM_QueueListHeadRefType',0,3,88,67
	.word	800
.L131:
	.byte	10
	.word	780
.L134:
	.byte	10
	.word	780
.L138:
	.byte	10
	.word	317
.L140:
	.byte	10
	.word	780
	.byte	13
	.byte	'void',0,8
	.word	858
	.byte	9
	.byte	'__prof_adm',0,3,1,1
	.word	864
	.byte	14,1,8
	.word	888
	.byte	9
	.byte	'__codeptr',0,3,1,1
	.word	890
	.byte	9
	.byte	'uint8',0,4,90,29
	.word	317
	.byte	4
	.byte	'short int',0,2,5,9
	.byte	'sint16',0,4,91,29
	.word	927
	.byte	9
	.byte	'uint16',0,4,92,29
	.word	278
	.byte	4
	.byte	'unsigned long int',0,4,7,9
	.byte	'uint32',0,4,94,29
	.word	970
	.byte	9
	.byte	'boolean',0,4,105,29
	.word	317
	.byte	4
	.byte	'unsigned long long int',0,8,7,9
	.byte	'uint64',0,4,130,1,30
	.word	1022
	.byte	9
	.byte	'Std_ReturnType',0,5,113,15
	.word	317
	.byte	9
	.byte	'PduLengthType',0,6,76,22
	.word	278
	.byte	9
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,7,112,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,7,115,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,7,118,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,7,121,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,7,124,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,7,136,1,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,7,139,1,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,7,148,1,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,7,151,1,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,7,141,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,7,144,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,7,147,3,16
	.word	278
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,7,150,3,16
	.word	278
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,7,153,3,16
	.word	278
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,7,156,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,7,159,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,7,162,3,16
	.word	278
	.byte	9
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,7,165,3,16
	.word	278
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,7,180,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,7,183,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,7,186,3,16
	.word	970
	.byte	9
	.byte	'IdtAppCom_MainPwrFltRsn',0,7,192,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGMDiags',0,7,195,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGMFltRsn',0,7,198,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGMSts',0,7,201,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGMSwSts',0,7,207,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,7,210,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,7,213,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,7,216,3,16
	.word	278
	.byte	9
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,7,219,3,16
	.word	278
	.byte	9
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,7,225,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,7,228,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_RednPwrFltRsn',0,7,231,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,7,234,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_SHWAIndSts',0,7,237,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_SHWASysFltSts',0,7,240,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_SHWASysMsg',0,7,243,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,7,246,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_SHWASysSts',0,7,249,3,15
	.word	317
	.byte	9
	.byte	'IdtAppCom_SHWASysTakeOver',0,7,252,3,15
	.word	317
	.byte	9
	.byte	'NvM_BlockIdType',0,7,227,5,16
	.word	278
	.byte	9
	.byte	'NvM_RequestResultType',0,7,207,6,15
	.word	317
	.byte	9
	.byte	'NvM_ServiceIdType',0,7,231,6,15
	.word	317
	.byte	4
	.byte	'unsigned int',0,4,7,9
	.byte	'Rte_BitType',0,7,230,7,22
	.word	2749
	.byte	6,8,59,9,1,7
	.byte	'MEMIF_UNINIT',0,0,7
	.byte	'MEMIF_IDLE',0,1,7
	.byte	'MEMIF_BUSY',0,2,7
	.byte	'MEMIF_BUSY_INTERNAL',0,3,0,9
	.byte	'MemIf_StatusType',0,8,65,3
	.word	2786
	.byte	6,8,72,9,1,7
	.byte	'MEMIF_JOB_OK',0,0,7
	.byte	'MEMIF_JOB_FAILED',0,1,7
	.byte	'MEMIF_JOB_PENDING',0,2,7
	.byte	'MEMIF_JOB_CANCELED',0,3,7
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,7
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,9
	.byte	'MemIf_JobResultType',0,8,80,3
	.word	2880
	.byte	15
	.word	317
	.byte	1,1,16
	.word	278
	.byte	16
	.word	278
	.byte	8
	.word	317
	.byte	16
	.word	3055
	.byte	16
	.word	278
	.byte	0,8
	.word	3038
	.byte	9
	.byte	'MemIf_ApiReadType',0,9,120,9
	.word	3071
	.byte	15
	.word	317
	.byte	1,1,16
	.word	278
	.byte	16
	.word	3055
	.byte	0,8
	.word	3102
	.byte	9
	.byte	'MemIf_ApiWriteType',0,9,121,9
	.word	3120
	.byte	15
	.word	317
	.byte	1,1,16
	.word	278
	.byte	0,8
	.word	3152
	.byte	9
	.byte	'MemIf_ApiEraseImmediateBlockType',0,9,122,9
	.word	3165
	.byte	9
	.byte	'MemIf_ApiInvalidateBlockType',0,9,123,9
	.word	3165
	.byte	17,1,1,8
	.word	3248
	.byte	9
	.byte	'MemIf_ApiCancelType',0,9,124,9
	.word	3251
	.byte	18
	.word	2786
	.byte	1,1,8
	.word	3284
	.byte	9
	.byte	'MemIf_ApiGetStatusType',0,9,125,9
	.word	3291
	.byte	18
	.word	2880
	.byte	1,1,8
	.word	3327
	.byte	9
	.byte	'MemIf_ApiGetJobResultType',0,9,126,9
	.word	3334
	.byte	19,1,1,6,8,88,9,1,7
	.byte	'MEMIF_MODE_SLOW',0,0,7
	.byte	'MEMIF_MODE_FAST',0,1,0,16
	.word	3376
	.byte	0,8
	.word	3373
	.byte	9
	.byte	'MemIf_ApiSetModeType',0,9,127,9
	.word	3424
	.byte	9
	.byte	'NvM_BitFieldType',0,1,113,22
	.word	2749
	.byte	9
	.byte	'NvM_CrcType',0,1,116,26
	.word	2749
	.byte	11,1,124,9,4,12
	.byte	'NvDataIndex_t',0,1
	.word	317
	.byte	2,35,0,12
	.byte	'NvRamErrorStatus_u8',0,1
	.word	317
	.byte	2,35,1,12
	.byte	'NvRamAttributes_u8',0,1
	.word	317
	.byte	2,35,2,0,9
	.byte	'NvM_RamMngmtAreaType',0,1,129,1,3
	.word	3503
	.byte	8
	.word	3503
	.byte	9
	.byte	'NvM_RamMngmtPtrType',0,1,131,1,65
	.word	3619
	.byte	15
	.word	317
	.byte	1,1,16
	.word	317
	.byte	16
	.word	317
	.byte	0,8
	.word	3653
	.byte	9
	.byte	'NvM_JobEndCbkPtrType',0,1,134,1,9
	.word	3671
	.byte	15
	.word	317
	.byte	1,1,16
	.word	278
	.byte	16
	.word	317
	.byte	16
	.word	317
	.byte	0,8
	.word	3706
	.byte	9
	.byte	'NvM_JobEndCbkExtPtrType',0,1,137,1,9
	.word	3729
	.byte	18
	.word	317
	.byte	1,1,8
	.word	3767
	.byte	9
	.byte	'NvM_InitCbkPtrType',0,1,142,1,9
	.word	3774
	.byte	15
	.word	317
	.byte	1,1,16
	.word	278
	.byte	16
	.word	864
	.byte	16
	.word	278
	.byte	0,8
	.word	3807
	.byte	9
	.byte	'NvM_InitCbkExtPtrType',0,1,145,1,9
	.word	3830
	.byte	15
	.word	317
	.byte	1,1,16
	.word	864
	.byte	0,8
	.word	3866
	.byte	9
	.byte	'NvM_WriteRamToNvMCbkPtrType',0,1,148,1,9
	.word	3879
	.byte	15
	.word	317
	.byte	1,1,10
	.word	858
	.byte	8
	.word	3928
	.byte	16
	.word	3933
	.byte	0,8
	.word	3921
	.byte	9
	.byte	'NvM_ReadRamFromNvMCbkPtrType',0,1,149,1,9
	.word	3944
	.byte	19,1,1,16
	.word	278
	.byte	16
	.word	864
	.byte	16
	.word	278
	.byte	0,8
	.word	3987
	.byte	9
	.byte	'NvM_PreWriteTransformCbkPtrType',0,1,152,1,9
	.word	4006
	.byte	9
	.byte	'NvM_PostReadTransformCbkPtrType',0,1,153,1,9
	.word	3830
	.byte	10
	.word	317
	.byte	8
	.word	4093
	.byte	9
	.byte	'NvM_ConstRamAddressType',0,1,162,1,50
	.word	4098
	.byte	9
	.byte	'NvM_RomAddressType',0,1,164,1,50
	.word	4098
	.byte	9
	.byte	'NvM_RamCrcAddressType',0,1,170,1,51
	.word	642
	.byte	11,1,196,1,9,64,12
	.byte	'RamBlockDataAddr_t',0,4
	.word	647
	.byte	2,35,0,12
	.byte	'RomBlockDataAddr_pt',0,4
	.word	4136
	.byte	2,35,4,12
	.byte	'InitCbkFunc_pt',0,4
	.word	3779
	.byte	2,35,8,12
	.byte	'InitCbkExtFunc_pt',0,4
	.word	3835
	.byte	2,35,12,12
	.byte	'JobEndCbkFunc_pt',0,4
	.word	3676
	.byte	2,35,16,12
	.byte	'JobEndCbkExtFunc_pt',0,4
	.word	3734
	.byte	2,35,20,12
	.byte	'CbkGetMirrorFunc_pt',0,4
	.word	3949
	.byte	2,35,24,12
	.byte	'CbkSetMirrorFunc_pt',0,4
	.word	3884
	.byte	2,35,28,12
	.byte	'CbkPreWriteTransform',0,4
	.word	4011
	.byte	2,35,32,12
	.byte	'CbkPostReadTransform',0,4
	.word	4052
	.byte	2,35,36,12
	.byte	'RamBlockCrcAddr_t',0,4
	.word	4164
	.byte	2,35,40,12
	.byte	'CRCCompMechanismCrcAddr_t',0,4
	.word	4164
	.byte	2,35,44,12
	.byte	'NvIdentifier_u16',0,2
	.word	278
	.byte	2,35,48,12
	.byte	'NvBlockLength_u16',0,2
	.word	278
	.byte	2,35,50,12
	.byte	'NvCryptoReference',0,1
	.word	317
	.byte	2,35,52,12
	.byte	'NvBlockNVRAMDataLength',0,2
	.word	278
	.byte	2,35,54,20
	.byte	'NvBlockCount_u8',0,1
	.word	317
	.byte	8,0,2,35,56,20
	.byte	'BlockPrio_u8',0,1
	.word	317
	.byte	8,0,2,35,57,20
	.byte	'DeviceId_u8',0,1
	.word	317
	.byte	4,4,2,35,58,20
	.byte	'MngmtType_t',0,1
	.word	317
	.byte	2,2,2,35,58,20
	.byte	'CrcSettings',0,1
	.word	317
	.byte	2,0,2,35,58,20
	.byte	'Flags_u8',0,1
	.word	317
	.byte	8,0,2,35,59,20
	.byte	'NotifyBswM',0,1
	.word	317
	.byte	1,7,2,35,60,0,9
	.byte	'NvM_BlockDescriptorType',0,1,223,1,3
	.word	4195
	.byte	10
	.word	317
	.byte	8
	.word	4852
	.byte	8
	.word	3767
	.byte	8
	.word	3807
	.byte	8
	.word	3653
	.byte	8
	.word	3706
	.byte	8
	.word	3921
	.byte	8
	.word	3866
	.byte	8
	.word	3987
	.byte	8
	.word	3807
	.byte	8
	.word	317
	.byte	9
	.byte	'NvM_CsmJobIdType',0,1,231,1,16
	.word	970
	.byte	10
	.word	4195
	.byte	8
	.word	4933
	.byte	9
	.byte	'NvM_BlockDescrPtrType',0,1,240,1,71
	.word	4938
	.byte	9
	.byte	'NvM_InternalServiceIdType',0,1,254,1,3
	.word	374
	.byte	9
	.byte	'NvM_QueueEntryRefType',0,1,129,2,15
	.word	317
	.byte	9
	.byte	'NvM_QueueEntryType',0,1,142,2,3
	.word	680
	.byte	21
	.word	680
	.byte	22,0,23
	.byte	'NvM_JobQueue_at',0,1,240,2,50
	.word	5068
	.byte	1,1,21
	.word	3503
	.byte	22,0,23
	.byte	'NvM_BlockMngmtArea_at',0,1,160,3,51
	.word	5102
	.byte	1,1,23
	.byte	'NvM_DcmBlockMngmt_t',0,1,163,3,51
	.word	3503
	.byte	1,1,6,2,42,9,1,7
	.byte	'NVM_ACT_ID_SetInitialAttr',0,0,7
	.byte	'NVM_ACT_ID_InitMainFsm',0,1,7
	.byte	'NVM_ACT_ID_InitBlock',0,2,7
	.byte	'NVM_ACT_ID_InitReadAll',0,3,7
	.byte	'NVM_ACT_ID_InitReadBlockSubFsm',0,4,7
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaultsSubFsm',0,5,7
	.byte	'NVM_ACT_ID_InitWriteAll',0,6,7
	.byte	'NVM_ACT_ID_InitWriteBlock',0,7,7
	.byte	'NVM_ACT_ID_InitWriteBlockFsm',0,8,7
	.byte	'NVM_ACT_ID_InitRestoreBlockDefaults',0,9,7
	.byte	'NVM_ACT_ID_FinishMainJob',0,10,7
	.byte	'NVM_ACT_ID_KillWritAll',0,11,7
	.byte	'NVM_ACT_ID_FinishBlock',0,12,7
	.byte	'NVM_ACT_ID_InitNextBlockReadAll',0,13,7
	.byte	'NVM_ACT_ID_InitNextBlockWriteAll',0,14,7
	.byte	'NVM_ACT_ID_FinishCfgIdCheck',0,15,7
	.byte	'NVM_ACT_ID_FinishReadBlock',0,16,7
	.byte	'NVM_ACT_ID_FinishWriteBlock',0,17,7
	.byte	'NVM_ACT_ID_FinishEraseBlock',0,18,7
	.byte	'NVM_ACT_ID_EraseNvBlock',0,19,7
	.byte	'NVM_ACT_ID_InvalidateNvBlock',0,20,7
	.byte	'NVM_ACT_ID_ProcessCrc',0,21,7
	.byte	'NVM_ACT_ID_WriteNvBlock',0,22,7
	.byte	'NVM_ACT_ID_ReadNvBlock',0,23,7
	.byte	'NVM_ACT_ID_ProcessCrcRead',0,24,7
	.byte	'NVM_ACT_ID_ReadCopyData',0,25,7
	.byte	'NVM_ACT_ID_RestoreRomDefaults',0,26,7
	.byte	'NVM_ACT_ID_FinishRestoreRomDefaults',0,27,7
	.byte	'NVM_ACT_ID_TestBlockBlank',0,28,7
	.byte	'NVM_ACT_ID_ValidateRam',0,29,7
	.byte	'NVM_ACT_ID_SetupRedundant',0,30,7
	.byte	'NVM_ACT_ID_SetupOther',0,31,7
	.byte	'NVM_ACT_ID_UpdateNvState',0,32,7
	.byte	'NVM_ACT_ID_SetReqIntegrityFailed',0,33,7
	.byte	'NVM_ACT_ID_SetReqSkipped',0,34,7
	.byte	'NVM_ACT_ID_SetReqNotOk',0,35,7
	.byte	'NVM_ACT_ID_SetReqOk',0,36,7
	.byte	'NVM_ACT_ID_SetBlockPendingWriteAll',0,37,7
	.byte	'NVM_ACT_ID_CopyNvDataToBuf',0,38,7
	.byte	'NVM_ACT_ID_GetMultiBlockJob',0,39,7
	.byte	'NVM_ACT_ID_CancelNV',0,40,7
	.byte	'NVM_ACT_ID_KillSubFsm',0,41,7
	.byte	'NVM_ACT_ID_FinishReadBlockAndSetSkipped',0,42,7
	.byte	'NVM_ACT_ID_GetNormalPrioJob',0,43,7
	.byte	'NVM_ACT_ID_Wait',0,44,7
	.byte	'NVM_ACT_ID_Nop',0,45,0,9
	.byte	'NvM_StateActionIdType',0,2,110,3
	.word	5173
	.byte	6,10,48,9,1,7
	.byte	'NVM_QRY_ID_BLK_WRITE_ALL',0,0,7
	.byte	'NVM_QRY_ID_CANCEL_WRITE_ALL',0,1,7
	.byte	'NVM_QR_ID_WRITEALL_KILLED',0,2,7
	.byte	'NVM_QRY_ID_CRC_BUSY',0,3,7
	.byte	'NVM_QRY_ID_DATA_COPY_BUSY',0,4,7
	.byte	'NVM_QRY_ID_CRC_MATCH',0,5,7
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_READALL',0,6,7
	.byte	'NVM_QRY_ID_LAST_BLOCK_DONE_WRITEALL',0,7,7
	.byte	'NVM_QRY_ID_LAST_RESULT_OK',0,8,7
	.byte	'NVM_QRY_ID_MAIN_FSM_RUNNING',0,9,7
	.byte	'NVM_QRY_ID_MULTI_BLK_JOB',0,10,7
	.byte	'NVM_QRY_ID_NORMAL_PRIO_JOB',0,11,7
	.byte	'NVM_QRY_ID_NV_BUSY',0,12,7
	.byte	'NVM_QRY_ID_MEMHWA_BUSY',0,13,7
	.byte	'NVM_QRY_ID_RAM_VALID',0,14,7
	.byte	'NVM_QRY_ID_REDUNDANT_BLOCK',0,15,7
	.byte	'NVM_QRY_ID_SKIP_BLOCK',0,16,7
	.byte	'NVM_QRY_ID_SUB_FSM_RUNNING',0,17,7
	.byte	'NVM_QRY_ID_WRITE_BLOCK_ONCE',0,18,7
	.byte	'NVM_QRY_ID_WRITE_RETRIES_EXCEEDED',0,19,7
	.byte	'NVM_QRY_ID_HAS_ROM',0,20,7
	.byte	'NVM_QRY_ID_EXT_RUNTIME',0,21,7
	.byte	'NvM_QRY_CRC_COMP_MECHANISM_SKIPWRITE',0,22,7
	.byte	'NVM_QRY_POST_READ_TRANSFORM',0,23,7
	.byte	'NVM_QRY_READALL_KILLED',0,24,7
	.byte	'NVM_QRY_SYNCDECRYPT',0,25,7
	.byte	'NVM_QRY_SYNCENCRYPT',0,26,7
	.byte	'NVM_QRY_CSM_RETRIES_NECESSARY',0,27,7
	.byte	'NVM_QRY_ID_TRUE',0,28,0,9
	.byte	'NvM_StateQueryIdType',0,10,93,3
	.word	6521
	.byte	9
	.byte	'NvM_CrcBufferPtrType',0,11,68,59
	.word	642
	.byte	19,1,1,10
	.word	317
	.byte	8
	.word	7386
	.byte	16
	.word	7391
	.byte	16
	.word	278
	.byte	8
	.word	970
	.byte	16
	.word	7406
	.byte	0,8
	.word	7383
	.byte	9
	.byte	'NvM_CrcCalculateFPtr',0,11,74,9
	.word	7417
	.byte	15
	.word	317
	.byte	1,1,16
	.word	7391
	.byte	16
	.word	7391
	.byte	0,8
	.word	7451
	.byte	9
	.byte	'NvM_CrcCompareFPtr',0,11,75,9
	.word	7469
	.byte	19,1,1,16
	.word	3055
	.byte	16
	.word	7391
	.byte	0,8
	.word	7501
	.byte	9
	.byte	'NvM_CrcCopyToBufferFPtr',0,11,76,9
	.word	7515
	.byte	24
	.byte	'NvM_CrcHandlerClass',0,11,79,8,20,12
	.byte	'calc',0,4
	.word	7422
	.byte	2,35,0,12
	.byte	'compare',0,4
	.word	7474
	.byte	2,35,4,12
	.byte	'copyToBuffer',0,4
	.word	7520
	.byte	2,35,8,12
	.byte	'initialCrcValue',0,4
	.word	970
	.byte	2,35,12,12
	.byte	'crcLength',0,1
	.word	317
	.byte	2,35,16,0,10
	.word	7552
	.byte	8
	.word	7675
	.byte	9
	.byte	'NvM_CrcHandlerClassConstPtr',0,11,88,75
	.word	7680
	.byte	8
	.word	7383
	.byte	8
	.word	7451
	.byte	8
	.word	7501
	.byte	24
	.byte	'NvM_CrcJobStruct',0,11,91,16,20,12
	.byte	'CurrentCrcValue',0,4
	.word	970
	.byte	2,35,0,12
	.byte	'RamData_pt',0,4
	.word	4103
	.byte	2,35,4,12
	.byte	'CrcBuffer',0,4
	.word	7354
	.byte	2,35,8,12
	.byte	'HandlerInstance_pt',0,4
	.word	7685
	.byte	2,35,12,12
	.byte	'RemainingLength_u16',0,2
	.word	278
	.byte	2,35,16,0,9
	.byte	'NvM_CrcJobType',0,11,98,3
	.word	7736
	.byte	10
	.word	317
	.byte	8
	.word	7903
	.byte	8
	.word	317
	.byte	10
	.word	7552
	.byte	8
	.word	7918
.L150:
	.byte	11,12,57,9,8,12
	.byte	'JobBlockId_t',0,2
	.word	278
	.byte	2,35,0,12
	.byte	'JobServiceId_t',0,1
	.word	374
	.byte	2,35,2,12
	.byte	'RamAddr_t',0,4
	.word	647
	.byte	2,35,4,0,9
	.byte	'NvM_JobType',0,12,62,3
	.word	7928
	.byte	11,12,66,9,44,12
	.byte	'Descriptor_pt',0,4
	.word	4943
	.byte	2,35,0,12
	.byte	'Mngmt_pt',0,4
	.word	3624
	.byte	2,35,4,12
	.byte	'RamAddr_t',0,4
	.word	647
	.byte	2,35,8,12
	.byte	'NvRamAddr_t',0,4
	.word	647
	.byte	2,35,12,12
	.byte	'BlockCrcJob_t',0,20
	.word	7736
	.byte	2,35,16,12
	.byte	'NvIdentifier_u16',0,2
	.word	278
	.byte	2,35,36,12
	.byte	'ByteCount_u16',0,2
	.word	278
	.byte	2,35,38,12
	.byte	'LastResult_t',0,1
	.word	317
	.byte	2,35,40,12
	.byte	'WriteRetryCounter_u8',0,1
	.word	317
	.byte	2,35,41,12
	.byte	'InternalFlags_u8',0,1
	.word	317
	.byte	2,35,42,12
	.byte	'NvState_u8',0,1
	.word	317
	.byte	2,35,43,0,9
	.byte	'NvM_BlockInfoType',0,12,83,3
	.word	8019
	.byte	10
	.word	4195
	.byte	8
	.word	8302
	.byte	8
	.word	3503
	.byte	6,12,96,9,1,7
	.byte	'NVM_STATE_UNINIT',0,0,7
	.byte	'NVM_STATE_IDLE',0,1,7
	.byte	'NVM_STATE_NORMAL_PRIO_JOB',0,2,7
	.byte	'NVM_STATE_MULTI_BLOCK_JOB',0,3,7
	.byte	'NVM_STATE_READ_READ_DATA',0,4,7
	.byte	'NVM_STATE_READ_DATA_VALIDATION',0,5,7
	.byte	'NVM_STATE_READ_CMP_CRC',0,6,7
	.byte	'NVM_STATE_READ_IMPL_RECOV',0,7,7
	.byte	'NVM_STATE_READ_LOAD_ROM',0,8,7
	.byte	'NVM_STATE_READ_FINALIZE',0,9,7
	.byte	'NVM_STATE_WRITE_INITIAL',0,10,7
	.byte	'NVM_STATE_WRITE_CRCCALC',0,11,7
	.byte	'NVM_STATE_WRITE_CRCCOMPMECHANISM',0,12,7
	.byte	'NVM_STATE_WRITE_TEST_PRI_READ',0,13,7
	.byte	'NVM_STATE_WRITE_TEST_SEC_READ',0,14,7
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_1',0,15,7
	.byte	'NVM_STATE_WRITE_WR_DATA_CRC_2',0,16,7
	.byte	'NVM_STATE_RESTORE_LOAD_ROM',0,17,7
	.byte	'NVM_STATE_INVALIDATING_BLOCK',0,18,7
	.byte	'NVM_STATE_ERASE_ERASE_BLOCK',0,19,7
	.byte	'NVM_STATE_READALL_PROC_CONFIG_ID',0,20,7
	.byte	'NVM_STATE_READALL_PROC_RAM_BLOCK',0,21,7
	.byte	'NVM_STATE_READALL_CHK_SKIP',0,22,7
	.byte	'NVM_STATE_READALL_KILLED',0,23,7
	.byte	'NVM_STATE_READALL_WR_ONCE_PROT',0,24,7
	.byte	'NVM_STATE_READALL_CHK_RAM_VALIDITY',0,25,7
	.byte	'NVM_STATE_READALL_READ_NV',0,26,7
	.byte	'NVM_STATE_READALL_LOAD_DEFAULTS',0,27,7
	.byte	'NVM_STATE_READALL_READABILITY_CHECK',0,28,7
	.byte	'NVM_STATE_WRITEALL_PROC_BLOCK',0,29,7
	.byte	'NVM_STATE_WRITEALL_WRITE_FSM',0,30,7
	.byte	'NVM_STATE_WRITEALL_WAIT_MEMHWA',0,31,7
	.byte	'NVM_STATE_FSM_FINISHED',0,32,0,9
	.byte	'NvM_StateIdType',0,12,215,1,3
	.word	8317
	.byte	11,12,217,1,9,4,12
	.byte	'InitialActionId',0,1
	.word	5173
	.byte	2,35,0,12
	.byte	'InitialState_t',0,1
	.word	8317
	.byte	2,35,1,12
	.byte	'PublicFid_t',0,1
	.word	317
	.byte	2,35,2,0,9
	.byte	'NvM_IntServiceDescrType',0,12,222,1,3
	.word	9327
	.byte	11,12,228,1,9,2,12
	.byte	'ExitHandler_t',0,1
	.word	5173
	.byte	2,35,0,12
	.byte	'EntryHandler_t',0,1
	.word	5173
	.byte	2,35,1,0,9
	.byte	'NvM_StateChangeActionsType',0,12,232,1,3
	.word	9437
	.byte	11,12,236,1,9,6,25,2
	.word	6521
	.byte	26,1,0,12
	.byte	'Queries_at',0,2
	.word	9533
	.byte	2,35,0,12
	.byte	'Actions_t',0,2
	.word	9437
	.byte	2,35,2,12
	.byte	'NextState_t',0,1
	.word	8317
	.byte	2,35,4,0,9
	.byte	'NvM_StateChangeIfDescrType',0,12,241,1,3
	.word	9527
	.byte	11,12,243,1,9,4,12
	.byte	'Actions_t',0,2
	.word	9437
	.byte	2,35,0,12
	.byte	'NextState_t',0,1
	.word	8317
	.byte	2,35,2,0,9
	.byte	'NvM_StateChangeElseDescrType',0,12,247,1,3
	.word	9639
	.byte	25,36
	.word	9327
	.byte	26,8,0,10
	.word	9724
	.byte	23
	.byte	'NvM_IntServiceDescrTable_at',0,12,174,2,58
	.word	9733
	.byte	1,1
.L151:
	.byte	11,13,45,9,2,12
	.byte	'SrvList',0,1
	.word	317
	.byte	2,35,0,12
	.byte	'EmptyList',0,1
	.word	317
	.byte	2,35,1,0,9
	.byte	'NvM_JobQueueType',0,13,49,3
	.word	9777
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L46:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,4,36,0,3,8,11,15,62,15,0,0,5,5,0,3,8,58,15,59
	.byte	15,57,15,73,19,0,0,6,4,1,58,15,59,15,57,15,11,15,0,0,7,40,0,3,8,28,13,0,0,8,15,0,73,19,0,0,9,22,0,3,8
	.byte	58,15,59,15,57,15,73,19,0,0,10,38,0,73,19,0,0,11,19,1,58,15,59,15,57,15,11,15,0,0,12,13,0,3,8,11,15,73
	.byte	19,56,9,0,0,13,59,0,3,8,0,0,14,21,0,54,15,0,0,15,21,1,73,19,54,15,39,12,0,0,16,5,0,73,19,0,0,17,21,0,54
	.byte	15,39,12,0,0,18,21,0,73,19,54,15,39,12,0,0,19,21,1,54,15,39,12,0,0,20,13,0,3,8,11,15,73,19,13,15,12,15
	.byte	56,9,0,0,21,1,1,73,19,0,0,22,33,0,0,0,23,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,24,19,1,3,8
	.byte	58,15,59,15,57,15,11,15,0,0,25,1,1,11,15,73,19,0,0,26,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L47:
	.word	.L186-.L185
.L185:
	.half	3
	.word	.L188-.L187
.L187:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	0
	.byte	'_PrivateCfg.h',0,1,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Act.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0
	.byte	'Platform_Types.h',0,2,0,0
	.byte	'Std_Types.h',0,2,0,0
	.byte	'ComStack_Types.h',0,2,0,0
	.byte	'Rte_Type.h',0,3,0,0
	.byte	'MemIf_Types.h',0,4,0,0
	.byte	'_Cfg.h',0,4,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Qry.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Crc.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_JobProc.h',0,0,0,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.h',0,0,0,0,0
.L188:
.L186:
	.sdecl	'.debug_info',debug,cluster('NvM_QueueInit')
	.sect	'.debug_info'
.L48:
	.word	250
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L51,.L50
	.byte	2
	.word	.L44
	.byte	3
	.byte	'NvM_QueueInit',0,1,196,1,30,1,1,1
	.word	.L27,.L97,.L26
	.byte	4
	.word	.L27,.L97
	.byte	5
	.byte	'index',0,1,199,1,11
	.word	.L98,.L99
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QueueInit')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QueueInit')
	.sect	'.debug_line'
.L50:
	.word	.L190-.L189
.L189:
	.half	3
	.word	.L192-.L191
.L191:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L192:
	.byte	5,17,7,0,5,2
	.word	.L27
	.byte	3,198,1,1,5,9,3,6,1,5,24,9
	.half	.L193-.L27
	.byte	1,5,21,9
	.half	.L194-.L193
	.byte	3,125,1,5,50,9
	.half	.L2-.L194
	.byte	3,3,1,5,42,1,5,24,9
	.half	.L195-.L2
	.byte	3,3,1,5,9,3,6,1,9
	.half	.L196-.L195
	.byte	3,122,1,5,47,9
	.half	.L197-.L196
	.byte	1,5,9,9
	.half	.L198-.L197
	.byte	3,6,1,5,21,3,116,1,5,42,7,9
	.half	.L199-.L198
	.byte	3,29,1,5,40,1,5,36,9
	.half	.L153-.L199
	.byte	3,1,1,5,34,1,5,5,9
	.half	.L200-.L153
	.byte	3,2,1,5,35,9
	.half	.L201-.L200
	.byte	1,9
	.half	.L202-.L201
	.byte	3,1,1,5,33,1,5,1,9
	.half	.L203-.L202
	.byte	3,1,1,7,9
	.half	.L52-.L203
	.byte	0,1,1
.L190:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QueueInit')
	.sect	'.debug_ranges'
.L51:
	.word	-1,.L27,0,.L52-.L27,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QueueJob')
	.sect	'.debug_info'
.L53:
	.word	423
	.half	3
	.word	.L54
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L56,.L55
	.byte	2
	.word	.L44
	.byte	3
	.byte	'NvM_QueueJob',0,1,255,1,33
	.word	.L98
	.byte	1,1,1
	.word	.L29,.L100,.L28
	.byte	4
	.byte	'BlockId',0,1,255,1,62
	.word	.L101,.L102
	.byte	4
	.byte	'ServiceId',0,1,128,2,62
	.word	.L103,.L104
	.byte	4
	.byte	'RamAddress',0,1,129,2,62
	.word	.L105,.L106
	.byte	5
	.word	.L29,.L100
	.byte	6
	.byte	'retVal',0,1,132,2,13
	.word	.L98,.L107
	.byte	6
	.byte	'queueFull',0,1,133,2,13
	.word	.L98,.L108
	.byte	6
	.byte	'blockAlreadyPending',0,1,134,2,13
	.word	.L98,.L109
	.byte	7
	.word	.L110
	.byte	6
	.byte	'elem',0,1,158,2,37
	.word	.L112,.L113
	.byte	6
	.byte	'elemPtr',0,1,159,2,69
	.word	.L114,.L115
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QueueJob')
	.sect	'.debug_abbrev'
.L54:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,11,1,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QueueJob')
	.sect	'.debug_line'
.L55:
	.word	.L205-.L204
.L204:
	.half	3
	.word	.L207-.L206
.L206:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L207:
	.byte	5,33,7,0,5,2
	.word	.L29
	.byte	3,254,1,1,5,20,9
	.half	.L157-.L29
	.byte	3,5,1,5,9,9
	.half	.L162-.L157
	.byte	3,5,1,5,54,7,9
	.half	.L208-.L162
	.byte	1,5,75,9
	.half	.L209-.L208
	.byte	1,5,79,9
	.half	.L3-.L209
	.byte	1,5,100,9
	.half	.L210-.L3
	.byte	1,5,29,9
	.half	.L4-.L210
	.byte	3,13,1,5,71,9
	.half	.L154-.L4
	.byte	3,125,1,5,27,9
	.half	.L211-.L154
	.byte	3,5,1,5,39,9
	.half	.L212-.L211
	.byte	3,1,1,9
	.half	.L213-.L212
	.byte	3,127,1,5,61,9
	.half	.L161-.L213
	.byte	3,1,1,5,8,9
	.half	.L158-.L161
	.byte	3,2,1,5,53,7,9
	.half	.L214-.L158
	.byte	1,5,67,7,9
	.half	.L111-.L214
	.byte	3,3,1,5,95,9
	.half	.L159-.L111
	.byte	3,1,1,5,80,1,5,95,9
	.half	.L215-.L159
	.byte	1,5,44,9
	.half	.L163-.L215
	.byte	3,8,1,5,28,9
	.half	.L164-.L163
	.byte	3,122,1,9
	.half	.L216-.L164
	.byte	3,1,1,9
	.half	.L217-.L216
	.byte	3,1,1,5,44,9
	.half	.L218-.L217
	.byte	3,4,1,9
	.half	.L160-.L218
	.byte	3,2,1,5,42,1,5,16,9
	.half	.L219-.L160
	.byte	3,2,1,5,28,9
	.half	.L5-.L219
	.byte	3,3,1,5,5,9
	.half	.L220-.L5
	.byte	3,2,1,5,10,7,9
	.half	.L221-.L220
	.byte	3,6,1,5,40,7,9
	.half	.L222-.L221
	.byte	3,4,1,5,67,9
	.half	.L223-.L222
	.byte	1,5,92,9
	.half	.L224-.L223
	.byte	1,5,78,9
	.half	.L165-.L224
	.byte	1,5,92,9
	.half	.L225-.L165
	.byte	1,5,5,9
	.half	.L7-.L225
	.byte	3,7,1,5,1,3,1,1,7,9
	.half	.L57-.L7
	.byte	0,1,1
.L205:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QueueJob')
	.sect	'.debug_ranges'
.L56:
	.word	-1,.L29,0,.L57-.L29,0,0
.L110:
	.word	-1,.L29,.L111-.L29,.L5-.L29,-1,.L31,0,.L92-.L31,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_UnQueueJob')
	.sect	'.debug_info'
.L58:
	.word	355
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L61,.L60
	.byte	2
	.word	.L44
	.byte	3
	.byte	'NvM_UnQueueJob',0,1,210,2,33
	.word	.L98
	.byte	1,1,1
	.word	.L33,.L116,.L32
	.byte	4
	.byte	'BlockId',0,1,210,2,64
	.word	.L101,.L117
	.byte	5
	.word	.L33,.L116
	.byte	6
	.byte	'retVal',0,1,214,2,13
	.word	.L98,.L118
	.byte	6
	.byte	'elem',0,1,215,2,27
	.word	.L98,.L119
	.byte	5
	.word	.L120,.L12
	.byte	6
	.byte	'PublicServiceId',0,1,248,2,35
	.word	.L121,.L122
	.byte	6
	.byte	'tmpElem',0,1,249,2,31
	.word	.L98,.L123
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_UnQueueJob')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_UnQueueJob')
	.sect	'.debug_line'
.L60:
	.word	.L227-.L226
.L226:
	.half	3
	.word	.L229-.L228
.L228:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L229:
	.byte	5,33,7,0,5,2
	.word	.L33
	.byte	3,209,2,1,5,5,9
	.half	.L166-.L33
	.byte	3,3,1,5,50,7,9
	.half	.L230-.L166
	.byte	1,5,71,9
	.half	.L231-.L230
	.byte	1,5,75,9
	.half	.L10-.L231
	.byte	1,5,96,9
	.half	.L232-.L10
	.byte	1,5,20,9
	.half	.L11-.L232
	.byte	3,1,1,5,29,3,6,1,5,71,9
	.half	.L167-.L11
	.byte	3,124,1,5,40,9
	.half	.L233-.L167
	.byte	3,13,1,5,51,9
	.half	.L234-.L233
	.byte	1,5,16,9
	.half	.L168-.L234
	.byte	3,16,1,5,5,9
	.half	.L235-.L168
	.byte	1,5,64,7,9
	.half	.L120-.L235
	.byte	3,2,1,5,49,1,5,64,9
	.half	.L236-.L120
	.byte	1,5,16,9
	.half	.L237-.L236
	.byte	3,4,1,5,33,3,5,1,5,70,9
	.half	.L238-.L237
	.byte	3,119,1,5,54,9
	.half	.L239-.L238
	.byte	3,1,1,5,81,9
	.half	.L240-.L239
	.byte	1,5,88,9
	.half	.L241-.L240
	.byte	1,5,33,9
	.half	.L170-.L241
	.byte	3,8,1,5,21,9
	.half	.L169-.L170
	.byte	3,4,1,5,33,9
	.half	.L242-.L169
	.byte	3,124,1,5,9,9
	.half	.L172-.L242
	.byte	3,4,1,5,32,7,9
	.half	.L243-.L172
	.byte	3,2,1,5,33,9
	.half	.L13-.L243
	.byte	3,4,1,5,46,9
	.half	.L244-.L13
	.byte	1,5,51,9
	.half	.L171-.L244
	.byte	3,2,1,5,49,1,5,57,9
	.half	.L245-.L171
	.byte	3,1,1,5,28,9
	.half	.L12-.L245
	.byte	3,3,1,5,5,9
	.half	.L246-.L12
	.byte	3,2,1,5,1,3,1,1,7,9
	.half	.L62-.L246
	.byte	0,1,1
.L227:
	.sdecl	'.debug_ranges',debug,cluster('NvM_UnQueueJob')
	.sect	'.debug_ranges'
.L61:
	.word	-1,.L33,0,.L62-.L33,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QryNormalPrioJob')
	.sect	'.debug_info'
.L63:
	.word	241
	.half	3
	.word	.L64
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L66,.L65
	.byte	2
	.word	.L44
	.byte	3
	.byte	'NvM_QryNormalPrioJob',0,1,157,3,33
	.word	.L98
	.byte	1,1,1
	.word	.L35,.L124,.L34
	.byte	4
	.word	.L35,.L124
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QryNormalPrioJob')
	.sect	'.debug_abbrev'
.L64:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QryNormalPrioJob')
	.sect	'.debug_line'
.L65:
	.word	.L248-.L247
.L247:
	.half	3
	.word	.L250-.L249
.L249:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L250:
	.byte	5,22,7,0,5,2
	.word	.L35
	.byte	3,158,3,1,5,41,9
	.half	.L251-.L35
	.byte	1,5,50,9
	.half	.L252-.L251
	.byte	1,5,1,3,1,1,7,9
	.half	.L67-.L252
	.byte	0,1,1
.L248:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QryNormalPrioJob')
	.sect	'.debug_ranges'
.L66:
	.word	-1,.L35,0,.L67-.L35,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_ActGetNormalPrioJob')
	.sect	'.debug_info'
.L68:
	.word	259
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L71,.L70
	.byte	2
	.word	.L44
	.byte	3
	.byte	'NvM_ActGetNormalPrioJob',0,1,180,3,30,1,1,1
	.word	.L37,.L125,.L36
	.byte	4
	.word	.L37,.L125
	.byte	5
	.byte	'elem',0,1,240,3,27
	.word	.L98,.L126
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_ActGetNormalPrioJob')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_ActGetNormalPrioJob')
	.sect	'.debug_line'
.L70:
	.word	.L254-.L253
.L253:
	.half	3
	.word	.L256-.L255
.L255:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L256:
	.byte	5,29,7,0,5,2
	.word	.L37
	.byte	3,241,3,1,5,51,9
	.half	.L257-.L37
	.byte	3,6,1,5,70,9
	.half	.L258-.L257
	.byte	1,5,35,9
	.half	.L259-.L258
	.byte	1,5,50,9
	.half	.L260-.L259
	.byte	1,5,35,1,5,45,9
	.half	.L261-.L260
	.byte	3,1,1,5,50,9
	.half	.L262-.L261
	.byte	3,127,1,5,79,9
	.half	.L263-.L262
	.byte	1,5,33,9
	.half	.L264-.L263
	.byte	1,5,45,9
	.half	.L265-.L264
	.byte	3,1,1,5,10,9
	.half	.L173-.L265
	.byte	1,5,39,3,3,1,5,51,9
	.half	.L175-.L173
	.byte	1,5,52,9
	.half	.L174-.L175
	.byte	3,2,1,5,5,1,5,52,9
	.half	.L266-.L174
	.byte	1,5,58,9
	.half	.L267-.L266
	.byte	1,5,35,9
	.half	.L268-.L267
	.byte	1,5,60,9
	.half	.L269-.L268
	.byte	3,1,1,5,37,9
	.half	.L270-.L269
	.byte	1,5,55,9
	.half	.L271-.L270
	.byte	3,1,1,5,32,9
	.half	.L272-.L271
	.byte	1,5,28,9
	.half	.L273-.L272
	.byte	3,3,1,5,1,7,9
	.half	.L72-.L273
	.byte	3,2,0,1,1
.L254:
	.sdecl	'.debug_ranges',debug,cluster('NvM_ActGetNormalPrioJob')
	.sect	'.debug_ranges'
.L71:
	.word	-1,.L37,0,.L72-.L37,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QueuePush')
	.sect	'.debug_info'
.L73:
	.word	319
	.half	3
	.word	.L74
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L76,.L75
	.byte	2
	.word	.L44
	.byte	3
	.byte	'NvM_QueuePush',0,1,235,4,40,1,1
	.word	.L39,.L127,.L38
	.byte	4
	.byte	'Queue',0,1,235,4,79
	.word	.L128,.L129
	.byte	4
	.byte	'Elem',0,1,235,4,108
	.word	.L98,.L130
	.byte	5
	.word	.L39,.L127
	.byte	6
	.byte	'elemPtr',0,1,237,4,65
	.word	.L131,.L132
	.byte	5
	.word	.L16,.L133
	.byte	6
	.byte	'NextPtr',0,1,248,4,69
	.word	.L134,.L135
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QueuePush')
	.sect	'.debug_abbrev'
.L74:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QueuePush')
	.sect	'.debug_line'
.L75:
	.word	.L275-.L274
.L274:
	.half	3
	.word	.L277-.L276
.L276:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L277:
	.byte	5,76,7,0,5,2
	.word	.L39
	.byte	3,236,4,1,5,91,9
	.half	.L177-.L39
	.byte	1,5,8,3,2,1,5,91,9
	.half	.L278-.L177
	.byte	3,126,1,5,18,9
	.half	.L178-.L278
	.byte	3,2,1,5,5,9
	.half	.L279-.L178
	.byte	1,5,16,7,9
	.half	.L280-.L279
	.byte	3,3,1,5,28,9
	.half	.L281-.L280
	.byte	3,1,1,9
	.half	.L282-.L281
	.byte	3,1,1,5,1,9
	.half	.L283-.L282
	.byte	3,13,1,5,95,7,9
	.half	.L16-.L283
	.byte	3,119,1,5,28,3,2,1,5,95,9
	.half	.L284-.L16
	.byte	3,126,1,5,37,9
	.half	.L180-.L284
	.byte	3,3,1,5,28,9
	.half	.L285-.L180
	.byte	1,5,32,9
	.half	.L286-.L285
	.byte	3,2,1,5,24,9
	.half	.L287-.L286
	.byte	1,5,55,9
	.half	.L179-.L287
	.byte	1,5,28,9
	.half	.L288-.L179
	.byte	3,1,1,5,16,9
	.half	.L289-.L288
	.byte	3,1,1,5,1,9
	.half	.L133-.L289
	.byte	3,2,1,7,9
	.half	.L77-.L133
	.byte	0,1,1
.L275:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QueuePush')
	.sect	'.debug_ranges'
.L76:
	.word	-1,.L39,0,.L77-.L39,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QueuePop')
	.sect	'.debug_info'
.L78:
	.word	293
	.half	3
	.word	.L79
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L81,.L80
	.byte	2
	.word	.L44
	.byte	3
	.byte	'NvM_QueuePop',0,1,144,5,57
	.word	.L98
	.byte	1,1
	.word	.L41,.L136,.L40
	.byte	4
	.byte	'Queue',0,1,144,5,95
	.word	.L128,.L137
	.byte	5
	.word	.L41,.L136
	.byte	6
	.byte	'retVal',0,1,146,5,17
	.word	.L138,.L139
	.byte	6
	.byte	'elemPtr',0,1,147,5,65
	.word	.L140,.L141
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QueuePop')
	.sect	'.debug_abbrev'
.L79:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QueuePop')
	.sect	'.debug_line'
.L80:
	.word	.L291-.L290
.L290:
	.half	3
	.word	.L293-.L292
.L292:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L293:
	.byte	5,26,7,0,5,2
	.word	.L41
	.byte	3,145,5,1,5,76,9
	.half	.L182-.L41
	.byte	3,1,1,5,91,9
	.half	.L294-.L182
	.byte	1,5,15,9
	.half	.L183-.L294
	.byte	3,2,1,5,5,9
	.half	.L295-.L183
	.byte	1,5,18,7,9
	.half	.L296-.L295
	.byte	3,3,1,5,16,1,5,1,9
	.half	.L297-.L296
	.byte	3,11,1,5,16,7,9
	.half	.L18-.L297
	.byte	3,121,1,5,32,9
	.half	.L298-.L18
	.byte	3,2,1,5,24,9
	.half	.L299-.L298
	.byte	1,5,64,9
	.half	.L181-.L299
	.byte	1,5,55,9
	.half	.L300-.L181
	.byte	1,5,32,9
	.half	.L301-.L300
	.byte	3,1,1,5,24,9
	.half	.L302-.L301
	.byte	1,5,64,9
	.half	.L303-.L302
	.byte	1,5,55,9
	.half	.L304-.L303
	.byte	1,5,1,9
	.half	.L305-.L304
	.byte	3,4,1,7,9
	.half	.L82-.L305
	.byte	0,1,1
.L291:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QueuePop')
	.sect	'.debug_ranges'
.L81:
	.word	-1,.L41,0,.L82-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_QueueFindBlock')
	.sect	'.debug_info'
.L83:
	.word	329
	.half	3
	.word	.L84
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L86,.L85
	.byte	2
	.word	.L44
	.byte	3
	.byte	'NvM_QueueFindBlock',0,1,175,5,57
	.word	.L98
	.byte	1,1
	.word	.L43,.L142,.L42
	.byte	4
	.byte	'QueueHead',0,1,175,5,98
	.word	.L98,.L143
	.byte	4
	.byte	'BlockId',0,1,175,5,125
	.word	.L101,.L144
	.byte	5
	.word	.L43,.L142
	.byte	6
	.byte	'entryRef',0,1,177,5,27
	.word	.L98,.L145
	.byte	7
	.word	.L146
	.byte	6
	.byte	'elem',0,1,181,5,31
	.word	.L98,.L149
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_QueueFindBlock')
	.sect	'.debug_abbrev'
.L84:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,11,1,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('NvM_QueueFindBlock')
	.sect	'.debug_line'
.L85:
	.word	.L307-.L306
.L306:
	.half	3
	.word	.L309-.L308
.L308:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L309:
	.byte	5,36,7,0,5,2
	.word	.L43
	.byte	3,176,5,1,5,5,9
	.half	.L184-.L43
	.byte	3,2,1,5,36,7,9
	.half	.L147-.L184
	.byte	3,2,1,5,20,3,4,1,5,35,9
	.half	.L22-.L147
	.byte	1,5,41,9
	.half	.L310-.L22
	.byte	1,5,31,9
	.half	.L311-.L310
	.byte	3,2,1,5,37,9
	.half	.L312-.L311
	.byte	1,5,13,9
	.half	.L313-.L312
	.byte	1,5,26,7,9
	.half	.L314-.L313
	.byte	3,3,1,5,1,9
	.half	.L21-.L314
	.byte	3,8,1,5,33,7,9
	.half	.L23-.L21
	.byte	3,124,1,5,1,7,9
	.half	.L148-.L23
	.byte	3,4,1,7,9
	.half	.L87-.L148
	.byte	0,1,1
.L307:
	.sdecl	'.debug_ranges',debug,cluster('NvM_QueueFindBlock')
	.sect	'.debug_ranges'
.L86:
	.word	-1,.L43,0,.L87-.L43,0,0
.L146:
	.word	-1,.L43,.L147-.L43,.L21-.L43,.L23-.L43,.L148-.L43,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L88:
	.word	215
	.half	3
	.word	.L89
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L91,.L90
	.byte	2
	.word	.L44
	.byte	3
	.byte	'.cocofun_1',0,1,255,1,33,1
	.word	.L31,.L92,.L30
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L89:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L90:
	.word	.L316-.L315
.L315:
	.half	3
	.word	.L318-.L317
.L317:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0,0,0,0,0
.L318:
	.byte	5,80,7,0,5,2
	.word	.L31
	.byte	3,158,2,1,9
	.half	.L92-.L31
	.byte	0,1,1,5,49,0,5,2
	.word	.L31
	.byte	3,246,2,1,5,80,9
	.half	.L319-.L31
	.byte	3,168,127,1,7,9
	.half	.L92-.L319
	.byte	0,1,1,5,76,0,5,2
	.word	.L31
	.byte	3,236,4,1,5,80,9
	.half	.L319-.L31
	.byte	3,178,125,1,7,9
	.half	.L92-.L319
	.byte	0,1,1,5,76,0,5,2
	.word	.L31
	.byte	3,146,5,1,5,80,9
	.half	.L319-.L31
	.byte	3,140,125,1,7,9
	.half	.L92-.L319
	.byte	0,1,1,5,20,0,5,2
	.word	.L31
	.byte	3,184,5,1,5,80,9
	.half	.L319-.L31
	.byte	3,230,124,1,7,9
	.half	.L92-.L319
	.byte	0,1,1
.L316:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L91:
	.word	-1,.L31,0,.L92-.L31,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_CurrentJob_t')
	.sect	'.debug_info'
.L93:
	.word	210
	.half	3
	.word	.L94
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L44
	.byte	3
	.byte	'NvM_CurrentJob_t',0,3,65,36
	.word	.L150
	.byte	1,5,3
	.word	NvM_CurrentJob_t
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_CurrentJob_t')
	.sect	'.debug_abbrev'
.L94:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('NvM_NormalPrioQueue')
	.sect	'.debug_info'
.L95:
	.word	212
	.half	3
	.word	.L96
	.byte	4,1
	.byte	'..\\eeprom\\NvM\\NvM_Queue.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L44
	.byte	3
	.byte	'NvM_NormalPrioQueue',0,3,103,51
	.word	.L151
	.byte	5,3
	.word	NvM_NormalPrioQueue
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('NvM_NormalPrioQueue')
	.sect	'.debug_abbrev'
.L96:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L30:
	.word	-1,.L31,0,.L92-.L31
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_ActGetNormalPrioJob')
	.sect	'.debug_loc'
.L36:
	.word	-1,.L37,0,.L125-.L37
	.half	2
	.byte	138,0
	.word	0,0
.L126:
	.word	-1,.L37,.L173-.L37,.L174-.L37
	.half	5
	.byte	144,33,157,32,0
	.word	.L175-.L37,.L176-.L37
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QryNormalPrioJob')
	.sect	'.debug_loc'
.L34:
	.word	-1,.L35,0,.L124-.L35
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QueueFindBlock')
	.sect	'.debug_loc'
.L144:
	.word	-1,.L43,.L155-.L43,.L92-.L43
	.half	5
	.byte	144,34,157,32,32
	.word	0,.L142-.L43
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L42:
	.word	-1,.L43,0,.L142-.L43
	.half	2
	.byte	138,0
	.word	0,0
.L143:
	.word	-1,.L43,.L155-.L43,.L92-.L43
	.half	5
	.byte	144,34,157,32,0
	.word	0,.L142-.L43
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L149:
	.word	-1,.L43,.L155-.L43,.L92-.L43
	.half	5
	.byte	144,32,157,32,0
	.word	.L147-.L43,.L21-.L43
	.half	5
	.byte	144,32,157,32,0
	.word	.L23-.L43,.L142-.L43
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L145:
	.word	-1,.L43,.L155-.L43,.L92-.L43
	.half	5
	.byte	144,33,157,32,0
	.word	.L184-.L43,.L142-.L43
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QueueInit')
	.sect	'.debug_loc'
.L26:
	.word	-1,.L27,0,.L97-.L27
	.half	2
	.byte	138,0
	.word	0,0
.L99:
	.word	-1,.L27,.L152-.L27,.L153-.L27
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QueueJob')
	.sect	'.debug_loc'
.L102:
	.word	-1,.L29,0,.L154-.L29
	.half	5
	.byte	144,34,157,32,0
	.word	.L155-.L29,.L92-.L29
	.half	5
	.byte	144,37,157,32,32
	.word	.L156-.L29,.L100-.L29
	.half	5
	.byte	144,37,157,32,32
	.word	.L165-.L29,.L7-.L29
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L28:
	.word	-1,.L29,0,.L100-.L29
	.half	2
	.byte	138,0
	.word	0,0
.L106:
	.word	-1,.L29,0,.L154-.L29
	.half	1
	.byte	100
	.word	.L155-.L29,.L92-.L29
	.half	1
	.byte	109
	.word	.L157-.L29,.L100-.L29
	.half	1
	.byte	109
	.word	0,0
.L104:
	.word	-1,.L29,0,.L154-.L29
	.half	5
	.byte	144,34,157,32,32
	.word	.L155-.L29,.L92-.L29
	.half	5
	.byte	144,38,157,32,0
	.word	.L157-.L29,.L100-.L29
	.half	5
	.byte	144,38,157,32,0
	.word	0,0
.L109:
	.word	-1,.L29,.L155-.L29,.L92-.L29
	.half	5
	.byte	144,37,157,32,0
	.word	.L158-.L29,.L100-.L29
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L113:
	.word	-1,.L29,.L155-.L29,.L92-.L29
	.half	5
	.byte	144,33,157,32,0
	.word	.L159-.L29,.L160-.L29
	.half	5
	.byte	144,33,157,32,0
	.word	.L164-.L29,.L160-.L29
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L115:
	.word	-1,.L29,.L163-.L29,.L5-.L29
	.half	1
	.byte	111
	.word	0,0
.L108:
	.word	-1,.L29,.L155-.L29,.L92-.L29
	.half	5
	.byte	144,36,157,32,32
	.word	.L161-.L29,.L100-.L29
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L107:
	.word	-1,.L29,.L155-.L29,.L92-.L29
	.half	5
	.byte	144,36,157,32,0
	.word	.L162-.L29,.L100-.L29
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QueuePop')
	.sect	'.debug_loc'
.L40:
	.word	-1,.L41,0,.L136-.L41
	.half	2
	.byte	138,0
	.word	0,0
.L137:
	.word	-1,.L41,.L31-.L41,.L92-.L41
	.half	1
	.byte	100
	.word	0,.L181-.L41
	.half	1
	.byte	100
	.word	0,0
.L141:
	.word	-1,.L41,.L183-.L41,.L136-.L41
	.half	1
	.byte	111
	.word	0,0
.L139:
	.word	-1,.L41,.L31-.L41,.L92-.L41
	.half	5
	.byte	144,33,157,32,0
	.word	.L182-.L41,.L136-.L41
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_QueuePush')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L39,.L31-.L39,.L92-.L39
	.half	5
	.byte	144,34,157,32,0
	.word	.L177-.L39,.L127-.L39
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L135:
	.word	-1,.L39,.L180-.L39,.L127-.L39
	.half	1
	.byte	101
	.word	0,0
.L38:
	.word	-1,.L39,0,.L127-.L39
	.half	2
	.byte	138,0
	.word	0,0
.L129:
	.word	-1,.L39,.L31-.L39,.L92-.L39
	.half	1
	.byte	100
	.word	.L177-.L39,.L127-.L39
	.half	1
	.byte	100
	.word	0,0
.L132:
	.word	-1,.L39,.L178-.L39,.L179-.L39
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('NvM_UnQueueJob')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L33,0,.L167-.L33
	.half	5
	.byte	144,34,157,32,0
	.word	.L155-.L33,.L92-.L33
	.half	5
	.byte	144,36,157,32,0
	.word	.L166-.L33,.L116-.L33
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L32:
	.word	-1,.L33,0,.L166-.L33
	.half	2
	.byte	138,0
	.word	.L166-.L33,.L116-.L33
	.half	2
	.byte	138,8
	.word	.L116-.L33,.L116-.L33
	.half	2
	.byte	138,0
	.word	0,0
.L122:
	.word	-1,.L33,.L170-.L33,.L12-.L33
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
.L119:
	.word	-1,.L33,.L155-.L33,.L92-.L33
	.half	5
	.byte	144,33,157,32,0
	.word	.L168-.L33,.L169-.L33
	.half	5
	.byte	144,33,157,32,0
	.word	.L172-.L33,.L171-.L33
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L118:
	.word	-1,.L33,.L155-.L33,.L92-.L33
	.half	5
	.byte	144,36,157,32,32
	.word	.L167-.L33,.L116-.L33
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L123:
	.word	-1,.L33,.L169-.L33,.L171-.L33
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L320:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('NvM_QueueInit')
	.sect	'.debug_frame'
	.word	20
	.word	.L320,.L27,.L97-.L27
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_QueueJob')
	.sect	'.debug_frame'
	.word	12
	.word	.L320,.L29,.L100-.L29
	.sdecl	'.debug_frame',debug,cluster('NvM_UnQueueJob')
	.sect	'.debug_frame'
	.word	36
	.word	.L320,.L33,.L116-.L33
	.byte	4
	.word	(.L166-.L33)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L116-.L166)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_QryNormalPrioJob')
	.sect	'.debug_frame'
	.word	24
	.word	.L320,.L35,.L124-.L35
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_ActGetNormalPrioJob')
	.sect	'.debug_frame'
	.word	12
	.word	.L320,.L37,.L125-.L37
	.sdecl	'.debug_frame',debug,cluster('NvM_QueuePush')
	.sect	'.debug_frame'
	.word	20
	.word	.L320,.L39,.L127-.L39
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('NvM_QueuePop')
	.sect	'.debug_frame'
	.word	20
	.word	.L320,.L41,.L136-.L41
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('NvM_QueueFindBlock')
	.sect	'.debug_frame'
	.word	24
	.word	.L320,.L43,.L142-.L43
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L321:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L321,.L31,.L92-.L31
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\eeprom\NvM\NvM_Queue.c	   711  
; ..\eeprom\NvM\NvM_Queue.c	   712  #if (NVM_JOB_PRIORISATION == STD_ON)
; ..\eeprom\NvM\NvM_Queue.c	   713  /**********************************************************************************************************************
; ..\eeprom\NvM\NvM_Queue.c	   714  *  NvM_QueueRequeueLastJob
; ..\eeprom\NvM\NvM_Queue.c	   715  **********************************************************************************************************************/
; ..\eeprom\NvM\NvM_Queue.c	   716  /*!
; ..\eeprom\NvM\NvM_Queue.c	   717   * Internal comment removed.
; ..\eeprom\NvM\NvM_Queue.c	   718   *
; ..\eeprom\NvM\NvM_Queue.c	   719   *
; ..\eeprom\NvM\NvM_Queue.c	   720   *
; ..\eeprom\NvM\NvM_Queue.c	   721   *
; ..\eeprom\NvM\NvM_Queue.c	   722   *
; ..\eeprom\NvM\NvM_Queue.c	   723   */
; ..\eeprom\NvM\NvM_Queue.c	   724  NVM_LOCAL FUNC(void, NVM_PRIVATE_CODE) NvM_QueueRequeueLastJob(void)
; ..\eeprom\NvM\NvM_Queue.c	   725  {
; ..\eeprom\NvM\NvM_Queue.c	   726      if(NvM_LastJobEntry != NVM_LIST_END)
; ..\eeprom\NvM\NvM_Queue.c	   727      {
; ..\eeprom\NvM\NvM_Queue.c	   728          NvM_EnterCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   729          /* add element at list start */
; ..\eeprom\NvM\NvM_Queue.c	   730          NvM_QueuePush(&NvM_NormalPrioQueue.SrvList, NvM_LastJobEntry); /* SBSW_NvM_FuncCall_PtrParam_Queue */
; ..\eeprom\NvM\NvM_Queue.c	   731  
; ..\eeprom\NvM\NvM_Queue.c	   732          /*  set the highest possible priority */
; ..\eeprom\NvM\NvM_Queue.c	   733          NvM_JobQueue_at[NvM_LastJobEntry].JobPrio = NVM_IMMEDIATE_JOB_PRIO; /* SBSW_NvM_AccessJobQueue */
; ..\eeprom\NvM\NvM_Queue.c	   734  
; ..\eeprom\NvM\NvM_Queue.c	   735          /* shift Queue Start to next element, remember: it is a ring list ... */
; ..\eeprom\NvM\NvM_Queue.c	   736          NvM_NormalPrioQueue.SrvList = NvM_JobQueue_at[NvM_LastJobEntry].NextEntry;
; ..\eeprom\NvM\NvM_Queue.c	   737  
; ..\eeprom\NvM\NvM_Queue.c	   738          NvM_LastJobEntry = NVM_LIST_END;
; ..\eeprom\NvM\NvM_Queue.c	   739          NvM_ExitCriticalSection();
; ..\eeprom\NvM\NvM_Queue.c	   740      }
; ..\eeprom\NvM\NvM_Queue.c	   741  }
; ..\eeprom\NvM\NvM_Queue.c	   742  #endif /* (NVM_JOB_PRIORISATION == STD_ON) */
; ..\eeprom\NvM\NvM_Queue.c	   743  
; ..\eeprom\NvM\NvM_Queue.c	   744  #define NVM_STOP_SEC_CODE
; ..\eeprom\NvM\NvM_Queue.c	   745    /* PRQA S 5087 */ /* MD_MSR_MemMap */
; ..\eeprom\NvM\NvM_Queue.c	   746  
; ..\eeprom\NvM\NvM_Queue.c	   747  #endif /* (NVM_API_CONFIG_CLASS != NVM_API_CONFIG_CLASS_1) */
; ..\eeprom\NvM\NvM_Queue.c	   748  
; ..\eeprom\NvM\NvM_Queue.c	   749  /*---- End of File ---------------------------------------------------------*/

	; Module end
