	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc29092a -c99 --dep-file=mcal_cfg\\.Gtm_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\Gtm_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\Gtm_PBCfg.src ..\\mcal_cfg\\Gtm_PBCfg.c"
	.compiler_name		"ctc"
	.name	"Gtm_PBCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kClockSetting0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kClockSetting0:	.type	object
	.size	Gtm_kClockSetting0,64
	.word	9786714,99,9
	.space	24
	.word	1,1,1,1
	.word	1,1,1
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kGeneralConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kGeneralConfig0:	.type	object
	.size	Gtm_kGeneralConfig0,4
	.half	1,1
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kTomTgcConfigGroup0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kTomTgcConfigGroup0:	.type	object
	.size	Gtm_kTomTgcConfigGroup0,16
	.half	2730
	.space	2
	.half	2730
	.space	10
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kTomChannelConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kTomChannelConfig0:	.type	object
	.size	Gtm_kTomChannelConfig0,72
	.byte	1
	.space	3
	.half	1000,500,1000,500
	.byte	1
	.space	3
	.half	2000,1000,2000,1000
	.byte	1
	.space	3
	.half	5000,2500,5000,2500
	.byte	1
	.space	3
	.half	10000,5000,10000,5000
	.byte	1
	.space	3
	.half	20000,10000,20000,10000
	.byte	1
	.space	3
	.half	50000,25000,50000,25000
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kTomTgcConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kTomTgcConfig0:	.type	object
	.size	Gtm_kTomTgcConfig0,12
	.space	8
	.word	Gtm_kTomTgcConfigGroup0
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kTomConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kTomConfig0:	.type	object
	.size	Gtm_kTomConfig0,72
	.space	1
	.byte	2
	.space	6
	.word	Gtm_kTomChannelConfig0
	.space	1
	.byte	2
	.space	6
	.word	Gtm_kTomChannelConfig0+12
	.space	1
	.byte	2
	.space	6
	.word	Gtm_kTomChannelConfig0+24
	.space	1
	.byte	2
	.space	6
	.word	Gtm_kTomChannelConfig0+36
	.space	1
	.byte	2
	.space	6
	.word	Gtm_kTomChannelConfig0+48
	.space	1
	.byte	2
	.space	6
	.word	Gtm_kTomChannelConfig0+60
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kTimFlt0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.global	Gtm_kTimFlt0
	.align	4
Gtm_kTimFlt0:	.type	object
	.size	Gtm_kTimFlt0,32
	.word	100,100,100,100,100,100,100,100
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kTimConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kTimConfig0:	.type	object
	.size	Gtm_kTimConfig0,96
	.space	1
	.byte	129
	.space	2
	.word	65541,Gtm_kTimFlt0
	.space	8
	.word	5
	.space	1
	.byte	129
	.space	2
	.word	65541,Gtm_kTimFlt0+8
	.space	8
	.word	80
	.space	1
	.byte	129
	.space	2
	.word	65541,Gtm_kTimFlt0+16
	.space	8
	.word	327680
	.space	1
	.byte	129
	.space	2
	.word	65541,Gtm_kTimFlt0+24
	.space	8
	.word	5242880
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kModUsage0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kModUsage0:	.type	object
	.size	Gtm_kModUsage0,40
	.space	1
	.byte	1,255,255,2
	.byte	3,255,255
	.space	1
	.byte	1,2,3,4,5,255,255,255
	.byte	255,255,255,255,255,255,255,255
	.byte	255,255,255,255,255,255,255,255
	.byte	255,255,255,255
	.byte	255,255,255
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kAdcConnections0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kAdcConnections0:	.type	object
	.size	Gtm_kAdcConnections0,4
	.space	4
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kPortConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kPortConfig0:	.type	object
	.size	Gtm_kPortConfig0,36
	.word	1114146
	.space	32
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_kModuleConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Gtm_kModuleConfig0:	.type	object
	.size	Gtm_kModuleConfig0,72
	.space	2
	.word	1,1,-1
	.space	4
	.half	2570
	.byte	51
	.space	3
	.word	Gtm_kTimConfig0
	.byte	1
	.space	3
	.word	Gtm_kTomTgcConfig0,4095
	.space	4
	.word	63,Gtm_kTomConfig0,Gtm_kModUsage0,Gtm_kGeneralConfig0
	.space	4
	.word	Gtm_kAdcConnections0
	.space	4
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Gtm_ConfigRoot')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.global	Gtm_ConfigRoot
	.align	4
Gtm_ConfigRoot:	.type	object
	.size	Gtm_ConfigRoot,12
	.word	Gtm_kClockSetting0,Gtm_kPortConfig0,Gtm_kModuleConfig0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	38070
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	178
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	184
	.byte	5,1,3
	.word	208
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	210
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	233
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	264
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	301
	.byte	4
	.byte	'boolean',0,2,105,29
	.word	233
	.byte	7
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,3,45,16,4,6
	.byte	'unsigned int',0,4,7,8
	.byte	'EN0',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'EN1',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'EN2',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'EN3',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'EN4',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'EN5',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'EN6',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'EN7',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'EN8',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'EN9',0,4
	.word	379
	.byte	1,22,2,35,0,8
	.byte	'EN10',0,4
	.word	379
	.byte	1,21,2,35,0,8
	.byte	'EN11',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'EN12',0,4
	.word	379
	.byte	1,19,2,35,0,8
	.byte	'EN13',0,4
	.word	379
	.byte	1,18,2,35,0,8
	.byte	'EN14',0,4
	.word	379
	.byte	1,17,2,35,0,8
	.byte	'EN15',0,4
	.word	379
	.byte	1,16,2,35,0,8
	.byte	'EN16',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'EN17',0,4
	.word	379
	.byte	1,14,2,35,0,8
	.byte	'EN18',0,4
	.word	379
	.byte	1,13,2,35,0,8
	.byte	'EN19',0,4
	.word	379
	.byte	1,12,2,35,0,8
	.byte	'EN20',0,4
	.word	379
	.byte	1,11,2,35,0,8
	.byte	'EN21',0,4
	.word	379
	.byte	1,10,2,35,0,8
	.byte	'EN22',0,4
	.word	379
	.byte	1,9,2,35,0,8
	.byte	'EN23',0,4
	.word	379
	.byte	1,8,2,35,0,8
	.byte	'EN24',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'EN25',0,4
	.word	379
	.byte	1,6,2,35,0,8
	.byte	'EN26',0,4
	.word	379
	.byte	1,5,2,35,0,8
	.byte	'EN27',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'EN28',0,4
	.word	379
	.byte	1,3,2,35,0,8
	.byte	'EN29',0,4
	.word	379
	.byte	1,2,2,35,0,8
	.byte	'EN30',0,4
	.word	379
	.byte	1,1,2,35,0,8
	.byte	'EN31',0,4
	.word	379
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN0_Bits',0,3,79,3
	.word	353
	.byte	7
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,3,82,16,4,8
	.byte	'reserved_0',0,4
	.word	379
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN1_Bits',0,3,85,3
	.word	926
	.byte	7
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,3,88,16,4,8
	.byte	'SEL0',0,4
	.word	379
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	379
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	379
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	379
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,3,95,3
	.word	1003
	.byte	7
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,3,98,16,4,8
	.byte	'SEL0',0,4
	.word	379
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	379
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	379
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	379
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,3,105,3
	.word	1157
	.byte	7
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,3,108,16,4,8
	.byte	'TO_ADDR',0,4
	.word	379
	.byte	20,12,2,35,0,8
	.byte	'TO_W1R0',0,4
	.word	379
	.byte	1,11,2,35,0,8
	.byte	'reserved_21',0,4
	.word	379
	.byte	11,0,2,35,0,0,4
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,3,113,3
	.word	1311
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,3,116,16,4,8
	.byte	'BRG_MODE',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'MSK_WR_RSP',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	6,24,2,35,0,8
	.byte	'MODE_UP_PGR',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'BUFF_OVL',0,4
	.word	379
	.byte	1,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'SYNC_INPUT_REG',0,4
	.word	379
	.byte	1,19,2,35,0,8
	.byte	'reserved_13',0,4
	.word	379
	.byte	3,16,2,35,0,8
	.byte	'BRG_RST',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'reserved_17',0,4
	.word	379
	.byte	7,8,2,35,0,8
	.byte	'BUFF_DPT',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,3,129,1,3
	.word	1439
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,3,132,1,16,4,8
	.byte	'NEW_TRAN_PTR',0,4
	.word	379
	.byte	5,27,2,35,0,8
	.byte	'FIRST_RSP_PTR',0,4
	.word	379
	.byte	5,22,2,35,0,8
	.byte	'TRAN_IN_PGR',0,4
	.word	379
	.byte	5,17,2,35,0,8
	.byte	'ABT_TRAN_PGR',0,4
	.word	379
	.byte	5,12,2,35,0,8
	.byte	'FBC',0,4
	.word	379
	.byte	6,6,2,35,0,8
	.byte	'RSP_TRAN_RDY',0,4
	.word	379
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,3,140,1,3
	.word	1746
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,3,143,1,16,4,8
	.byte	'TRAN_IN_PGR2',0,4
	.word	379
	.byte	5,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	379
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,3,147,1,3
	.word	1948
	.byte	7
	.byte	'_Ifx_GTM_CLC_Bits',0,3,150,1,16,4,8
	.byte	'DISR',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'DISS',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'EDIS',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_CLC_Bits',0,3,157,1,3
	.word	2061
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,3,160,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,3,164,1,3
	.word	2204
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,3,167,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'CLK6_SEL',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	379
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,3,172,1,3
	.word	2321
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,3,175,1,16,4,8
	.byte	'CLK_CNT',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'CLK7_SEL',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	379
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,3,180,1,3
	.word	2456
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,3,183,1,16,4,8
	.byte	'EN_CLK0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'EN_CLK1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'EN_CLK2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'EN_CLK3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'EN_CLK4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'EN_CLK5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'EN_CLK6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'EN_CLK7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'EN_ECLK0',0,4
	.word	379
	.byte	2,14,2,35,0,8
	.byte	'EN_ECLK1',0,4
	.word	379
	.byte	2,12,2,35,0,8
	.byte	'EN_ECLK2',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'EN_FXCLK',0,4
	.word	379
	.byte	2,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,3,198,1,3
	.word	2591
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,3,201,1,16,4,8
	.byte	'ECLK_DEN',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,3,205,1,3
	.word	2911
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,3,208,1,16,4,8
	.byte	'ECLK_NUM',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,3,212,1,3
	.word	3023
	.byte	7
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,3,215,1,16,4,8
	.byte	'FXCLK_SEL',0,4
	.word	379
	.byte	4,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,3,219,1,3
	.word	3135
	.byte	7
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,3,222,1,16,4,8
	.byte	'GCLK_DEN',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,3,226,1,3
	.word	3251
	.byte	7
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,3,229,1,16,4,8
	.byte	'GCLK_NUM',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,3,233,1,3
	.word	3363
	.byte	7
	.byte	'_Ifx_GTM_CTRL_Bits',0,3,236,1,16,4,8
	.byte	'RF_PROT',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'TO_MODE',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'TO_VAL',0,4
	.word	379
	.byte	5,23,2,35,0,8
	.byte	'reserved_9',0,4
	.word	379
	.byte	23,0,2,35,0,0,4
	.byte	'Ifx_GTM_CTRL_Bits',0,3,243,1,3
	.word	3475
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,3,246,1,16,4,8
	.byte	'O1SEL_0',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	379
	.byte	2,29,2,35,0,8
	.byte	'SWAP_0',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'O1F_0',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'O1SEL_1',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'I1SEL_1',0,4
	.word	379
	.byte	1,22,2,35,0,8
	.byte	'SH_EN_1',0,4
	.word	379
	.byte	1,21,2,35,0,8
	.byte	'SWAP_1',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'O1F_1',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'O1SEL_2',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'I1SEL_2',0,4
	.word	379
	.byte	1,14,2,35,0,8
	.byte	'SH_EN_2',0,4
	.word	379
	.byte	1,13,2,35,0,8
	.byte	'SWAP_2',0,4
	.word	379
	.byte	1,12,2,35,0,8
	.byte	'O1F_2',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'reserved_22',0,4
	.word	379
	.byte	2,8,2,35,0,8
	.byte	'O1SEL_3',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'I1SEL_3',0,4
	.word	379
	.byte	1,6,2,35,0,8
	.byte	'SH_EN_3',0,4
	.word	379
	.byte	1,5,2,35,0,8
	.byte	'SWAP_3',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'O1F_3',0,4
	.word	379
	.byte	2,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,3,143,2,3
	.word	3628
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,3,146,2,16,4,8
	.byte	'POL0_0',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'OC0_0',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'SL0_0',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'DT0_0',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'POL1_0',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'OC1_0',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'SL1_0',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'DT1_0',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'POL0_1',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'OC0_1',0,4
	.word	379
	.byte	1,22,2,35,0,8
	.byte	'SL0_1',0,4
	.word	379
	.byte	1,21,2,35,0,8
	.byte	'DT0_1',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'POL1_1',0,4
	.word	379
	.byte	1,19,2,35,0,8
	.byte	'OC1_1',0,4
	.word	379
	.byte	1,18,2,35,0,8
	.byte	'SL1_1',0,4
	.word	379
	.byte	1,17,2,35,0,8
	.byte	'DT1_1',0,4
	.word	379
	.byte	1,16,2,35,0,8
	.byte	'POL0_2',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'OC0_2',0,4
	.word	379
	.byte	1,14,2,35,0,8
	.byte	'SL0_2',0,4
	.word	379
	.byte	1,13,2,35,0,8
	.byte	'DT0_2',0,4
	.word	379
	.byte	1,12,2,35,0,8
	.byte	'POL1_2',0,4
	.word	379
	.byte	1,11,2,35,0,8
	.byte	'OC1_2',0,4
	.word	379
	.byte	1,10,2,35,0,8
	.byte	'SL1_2',0,4
	.word	379
	.byte	1,9,2,35,0,8
	.byte	'DT1_2',0,4
	.word	379
	.byte	1,8,2,35,0,8
	.byte	'POL0_3',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'OC0_3',0,4
	.word	379
	.byte	1,6,2,35,0,8
	.byte	'SL0_3',0,4
	.word	379
	.byte	1,5,2,35,0,8
	.byte	'DT0_3',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'POL1_3',0,4
	.word	379
	.byte	1,3,2,35,0,8
	.byte	'OC1_3',0,4
	.word	379
	.byte	1,2,2,35,0,8
	.byte	'SL1_3',0,4
	.word	379
	.byte	1,1,2,35,0,8
	.byte	'DT1_3',0,4
	.word	379
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,3,180,2,3
	.word	4140
	.byte	7
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,3,183,2,16,4,8
	.byte	'POL0_0_SR',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'OC0_0_SR',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'SL0_0_SR',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'DT0_0_SR',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'POL1_0_SR',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'OC1_0_SR',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'SL1_0_SR',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'DT1_0_SR',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'POL0_1_SR',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'OC0_1_SR',0,4
	.word	379
	.byte	1,22,2,35,0,8
	.byte	'SL0_1_SR',0,4
	.word	379
	.byte	1,21,2,35,0,8
	.byte	'DT0_1_SR',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'POL1_1_SR',0,4
	.word	379
	.byte	1,19,2,35,0,8
	.byte	'OC1_1_SR',0,4
	.word	379
	.byte	1,18,2,35,0,8
	.byte	'SL1_1_SR',0,4
	.word	379
	.byte	1,17,2,35,0,8
	.byte	'DT1_1_SR',0,4
	.word	379
	.byte	1,16,2,35,0,8
	.byte	'POL0_2_SR',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'OC0_2_SR',0,4
	.word	379
	.byte	1,14,2,35,0,8
	.byte	'SL0_2_SR',0,4
	.word	379
	.byte	1,13,2,35,0,8
	.byte	'DT0_2_SR',0,4
	.word	379
	.byte	1,12,2,35,0,8
	.byte	'POL1_2_SR',0,4
	.word	379
	.byte	1,11,2,35,0,8
	.byte	'OC1_2_SR',0,4
	.word	379
	.byte	1,10,2,35,0,8
	.byte	'SL1_2_SR',0,4
	.word	379
	.byte	1,9,2,35,0,8
	.byte	'DT1_2_SR',0,4
	.word	379
	.byte	1,8,2,35,0,8
	.byte	'POL0_3_SR',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'OC0_3_SR',0,4
	.word	379
	.byte	1,6,2,35,0,8
	.byte	'SL0_3_SR',0,4
	.word	379
	.byte	1,5,2,35,0,8
	.byte	'DT0_3_SR',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'POL1_3_SR',0,4
	.word	379
	.byte	1,3,2,35,0,8
	.byte	'OC1_3_SR',0,4
	.word	379
	.byte	1,2,2,35,0,8
	.byte	'SL1_3_SR',0,4
	.word	379
	.byte	1,1,2,35,0,8
	.byte	'DT1_3_SR',0,4
	.word	379
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,3,217,2,3
	.word	4761
	.byte	7
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,3,220,2,16,4,8
	.byte	'CLK_SEL',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'UPD_MODE',0,4
	.word	379
	.byte	3,25,2,35,0,8
	.byte	'reserved_7',0,4
	.word	379
	.byte	25,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,3,226,2,3
	.word	5484
	.byte	7
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,3,229,2,16,4,8
	.byte	'RELRISE',0,4
	.word	379
	.byte	10,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	379
	.byte	6,16,2,35,0,8
	.byte	'RELFALL',0,4
	.word	379
	.byte	10,6,2,35,0,8
	.byte	'reserved_26',0,4
	.word	379
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,3,235,2,3
	.word	5628
	.byte	7
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,3,238,2,16,4,8
	.byte	'RELBLK',0,4
	.word	379
	.byte	10,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	379
	.byte	6,16,2,35,0,8
	.byte	'PSU_IN_SEL',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'IN_POL',0,4
	.word	379
	.byte	1,14,2,35,0,8
	.byte	'reserved_18',0,4
	.word	379
	.byte	2,12,2,35,0,8
	.byte	'SHIFT_SEL',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'reserved_22',0,4
	.word	379
	.byte	10,0,2,35,0,0,4
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,3,247,2,3
	.word	5777
	.byte	7
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,3,250,2,16,4,8
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,3,129,3,3
	.word	5992
	.byte	7
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,3,132,3,16,4,8
	.byte	'GRSTEN',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'BRIDGE_MODE_RST',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'AEI_IN',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	379
	.byte	5,24,2,35,0,8
	.byte	'TOM_OUT_RST',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	379
	.byte	3,20,2,35,0,8
	.byte	'reserved_12',0,4
	.word	379
	.byte	4,16,2,35,0,8
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'IRQ_MODE_PULSE',0,4
	.word	379
	.byte	1,14,2,35,0,8
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	379
	.byte	1,13,2,35,0,8
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	379
	.byte	1,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	379
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_GTM_HW_CONF_Bits',0,3,146,3,3
	.word	6196
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,3,149,3,16,4,8
	.byte	'reserved_0',0,4
	.word	379
	.byte	4,28,2,35,0,8
	.byte	'AEI_IRQ',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	379
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,3,154,3,3
	.word	6553
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,3,157,3,16,4,8
	.byte	'TIM0_CH0_IRQ',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'TIM0_CH1_IRQ',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'TIM0_CH2_IRQ',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'TIM0_CH3_IRQ',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'TIM0_CH4_IRQ',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'TIM0_CH5_IRQ',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'TIM0_CH6_IRQ',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'TIM0_CH7_IRQ',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	379
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,3,168,3,3
	.word	6681
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,3,171,3,16,4,8
	.byte	'TOM0_CH0_IRQ',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'TOM0_CH1_IRQ',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'TOM0_CH2_IRQ',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'TOM0_CH3_IRQ',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'TOM0_CH4_IRQ',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'TOM0_CH5_IRQ',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'TOM0_CH6_IRQ',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'TOM0_CH7_IRQ',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'TOM0_CH8_IRQ',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'TOM0_CH9_IRQ',0,4
	.word	379
	.byte	1,22,2,35,0,8
	.byte	'TOM0_CH10_IRQ',0,4
	.word	379
	.byte	1,21,2,35,0,8
	.byte	'TOM0_CH11_IRQ',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'TOM0_CH12_IRQ',0,4
	.word	379
	.byte	1,19,2,35,0,8
	.byte	'TOM0_CH13_IRQ',0,4
	.word	379
	.byte	1,18,2,35,0,8
	.byte	'TOM0_CH14_IRQ',0,4
	.word	379
	.byte	1,17,2,35,0,8
	.byte	'TOM0_CH15_IRQ',0,4
	.word	379
	.byte	1,16,2,35,0,8
	.byte	'TOM1_CH0_IRQ',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'TOM1_CH1_IRQ',0,4
	.word	379
	.byte	1,14,2,35,0,8
	.byte	'TOM1_CH2_IRQ',0,4
	.word	379
	.byte	1,13,2,35,0,8
	.byte	'TOM1_CH3_IRQ',0,4
	.word	379
	.byte	1,12,2,35,0,8
	.byte	'TOM1_CH4_IRQ',0,4
	.word	379
	.byte	1,11,2,35,0,8
	.byte	'TOM1_CH5_IRQ',0,4
	.word	379
	.byte	1,10,2,35,0,8
	.byte	'TOM1_CH6_IRQ',0,4
	.word	379
	.byte	1,9,2,35,0,8
	.byte	'TOM1_CH7_IRQ',0,4
	.word	379
	.byte	1,8,2,35,0,8
	.byte	'TOM1_CH8_IRQ',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'TOM1_CH9_IRQ',0,4
	.word	379
	.byte	1,6,2,35,0,8
	.byte	'TOM1_CH10_IRQ',0,4
	.word	379
	.byte	1,5,2,35,0,8
	.byte	'TOM1_CH11_IRQ',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'TOM1_CH12_IRQ',0,4
	.word	379
	.byte	1,3,2,35,0,8
	.byte	'TOM1_CH13_IRQ',0,4
	.word	379
	.byte	1,2,2,35,0,8
	.byte	'TOM1_CH14_IRQ',0,4
	.word	379
	.byte	1,1,2,35,0,8
	.byte	'TOM1_CH15_IRQ',0,4
	.word	379
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,3,205,3,3
	.word	6960
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,3,208,3,16,4,8
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	379
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,3,219,3,3
	.word	7805
	.byte	7
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,3,222,3,16,4,8
	.byte	'GTM_EIRQ',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	379
	.byte	3,28,2,35,0,8
	.byte	'TIM0_EIRQ',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	379
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,3,228,3,3
	.word	8098
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,3,231,3,16,4,8
	.byte	'SEL0',0,4
	.word	379
	.byte	4,28,2,35,0,8
	.byte	'SEL1',0,4
	.word	379
	.byte	4,24,2,35,0,8
	.byte	'SEL2',0,4
	.word	379
	.byte	4,20,2,35,0,8
	.byte	'SEL3',0,4
	.word	379
	.byte	4,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,3,238,3,3
	.word	8252
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,3,241,3,16,4,8
	.byte	'SEL0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'SEL1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'SEL2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'SEL3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'SEL4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'SEL5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'SEL6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'SEL7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'SEL8',0,4
	.word	379
	.byte	2,14,2,35,0,8
	.byte	'SEL9',0,4
	.word	379
	.byte	2,12,2,35,0,8
	.byte	'SEL10',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'SEL11',0,4
	.word	379
	.byte	2,8,2,35,0,8
	.byte	'SEL12',0,4
	.word	379
	.byte	2,6,2,35,0,8
	.byte	'SEL13',0,4
	.word	379
	.byte	2,4,2,35,0,8
	.byte	'SEL14',0,4
	.word	379
	.byte	2,2,2,35,0,8
	.byte	'SEL15',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,3,131,4,3
	.word	8422
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,3,134,4,16,4,8
	.byte	'CH0SEL',0,4
	.word	379
	.byte	4,28,2,35,0,8
	.byte	'CH1SEL',0,4
	.word	379
	.byte	4,24,2,35,0,8
	.byte	'CH2SEL',0,4
	.word	379
	.byte	4,20,2,35,0,8
	.byte	'CH3SEL',0,4
	.word	379
	.byte	4,16,2,35,0,8
	.byte	'CH4SEL',0,4
	.word	379
	.byte	4,12,2,35,0,8
	.byte	'CH5SEL',0,4
	.word	379
	.byte	4,8,2,35,0,8
	.byte	'CH6SEL',0,4
	.word	379
	.byte	4,4,2,35,0,8
	.byte	'CH7SEL',0,4
	.word	379
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,3,144,4,3
	.word	8763
	.byte	7
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,3,147,4,16,4,8
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,3,154,4,3
	.word	8988
	.byte	7
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,3,157,4,16,4,8
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'TRG_AEI_USP_BE',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,3,164,4,3
	.word	9186
	.byte	7
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,3,167,4,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,3,171,4,3
	.word	9382
	.byte	7
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,3,174,4,16,4,8
	.byte	'AEI_TO_XPT',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'AEI_USP_ADDR',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'AEI_IM_ADDR',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'AEI_USP_BE',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,3,181,4,3
	.word	9485
	.byte	7
	.byte	'_Ifx_GTM_KRST0_Bits',0,3,184,4,16,4,8
	.byte	'RST',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'RSTSTAT',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRST0_Bits',0,3,189,4,3
	.word	9663
	.byte	7
	.byte	'_Ifx_GTM_KRST1_Bits',0,3,192,4,16,4,8
	.byte	'RST',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	379
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRST1_Bits',0,3,196,4,3
	.word	9774
	.byte	7
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,3,199,4,16,4,8
	.byte	'CLR',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	379
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,3,203,4,3
	.word	9866
	.byte	7
	.byte	'_Ifx_GTM_OCS_Bits',0,3,206,4,16,4,8
	.byte	'reserved_0',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'SUS',0,4
	.word	379
	.byte	4,4,2,35,0,8
	.byte	'SUS_P',0,4
	.word	379
	.byte	1,3,2,35,0,8
	.byte	'SUSSTA',0,4
	.word	379
	.byte	1,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_OCS_Bits',0,3,213,4,3
	.word	9962
	.byte	7
	.byte	'_Ifx_GTM_ODA_Bits',0,3,216,4,16,4,8
	.byte	'DDREN',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'DREN',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_ODA_Bits',0,3,221,4,3
	.word	10108
	.byte	7
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,3,224,4,16,4,8
	.byte	'CV',0,4
	.word	379
	.byte	27,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'CM',0,4
	.word	379
	.byte	2,2,2,35,0,8
	.byte	'reserved_30',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU0T_Bits',0,3,230,4,3
	.word	10214
	.byte	7
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,3,233,4,16,4,8
	.byte	'CV',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	4,4,2,35,0,8
	.byte	'EN',0,4
	.word	379
	.byte	1,3,2,35,0,8
	.byte	'reserved_29',0,4
	.word	379
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU1T_Bits',0,3,239,4,3
	.word	10345
	.byte	7
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,3,242,4,16,4,8
	.byte	'CV',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	4,4,2,35,0,8
	.byte	'EN',0,4
	.word	379
	.byte	1,3,2,35,0,8
	.byte	'reserved_29',0,4
	.word	379
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTBU2T_Bits',0,3,248,4,3
	.word	10476
	.byte	7
	.byte	'_Ifx_GTM_OTSC0_Bits',0,3,251,4,16,4,8
	.byte	'B0LMT',0,4
	.word	379
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'B0LMI',0,4
	.word	379
	.byte	4,24,2,35,0,8
	.byte	'B0HMT',0,4
	.word	379
	.byte	3,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'B0HMI',0,4
	.word	379
	.byte	4,16,2,35,0,8
	.byte	'B1LMT',0,4
	.word	379
	.byte	3,13,2,35,0,8
	.byte	'reserved_19',0,4
	.word	379
	.byte	1,12,2,35,0,8
	.byte	'B1LMI',0,4
	.word	379
	.byte	4,8,2,35,0,8
	.byte	'B1HMT',0,4
	.word	379
	.byte	3,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'B1HMI',0,4
	.word	379
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTSC0_Bits',0,3,137,5,3
	.word	10607
	.byte	7
	.byte	'_Ifx_GTM_OTSS_Bits',0,3,140,5,16,4,8
	.byte	'OTGB0',0,4
	.word	379
	.byte	4,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	4,24,2,35,0,8
	.byte	'OTGB1',0,4
	.word	379
	.byte	4,20,2,35,0,8
	.byte	'reserved_12',0,4
	.word	379
	.byte	4,16,2,35,0,8
	.byte	'OTGB2',0,4
	.word	379
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	379
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_GTM_OTSS_Bits',0,3,148,5,3
	.word	10889
	.byte	7
	.byte	'_Ifx_GTM_REV_Bits',0,3,151,5,16,4,8
	.byte	'STEP',0,4
	.word	379
	.byte	8,24,2,35,0,8
	.byte	'NO',0,4
	.word	379
	.byte	4,20,2,35,0,8
	.byte	'MINOR',0,4
	.word	379
	.byte	4,16,2,35,0,8
	.byte	'MAJOR',0,4
	.word	379
	.byte	4,12,2,35,0,8
	.byte	'DEV_CODE0',0,4
	.word	379
	.byte	4,8,2,35,0,8
	.byte	'DEV_CODE1',0,4
	.word	379
	.byte	4,4,2,35,0,8
	.byte	'DEV_CODE2',0,4
	.word	379
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_REV_Bits',0,3,160,5,3
	.word	11061
	.byte	7
	.byte	'_Ifx_GTM_RST_Bits',0,3,163,5,16,4,8
	.byte	'RST',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	379
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_RST_Bits',0,3,167,5,3
	.word	11239
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,3,170,5,16,4,8
	.byte	'BASE',0,4
	.word	379
	.byte	27,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	379
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,3,174,5,3
	.word	11327
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,3,177,5,16,4,8
	.byte	'LOW_RES',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	379
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,3,182,5,3
	.word	11435
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,3,185,5,16,4,8
	.byte	'BASE',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,3,189,5,3
	.word	11567
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,3,192,5,16,4,8
	.byte	'CH_MODE',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	379
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,3,197,5,3
	.word	11675
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,3,200,5,16,4,8
	.byte	'BASE',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,3,204,5,3
	.word	11807
	.byte	7
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,3,207,5,16,4,8
	.byte	'CH_MODE',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'CH_CLK_SRC',0,4
	.word	379
	.byte	3,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	379
	.byte	28,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,3,212,5,3
	.word	11915
	.byte	7
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,3,215,5,16,4,8
	.byte	'ENDIS_CH0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CH1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CH2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	379
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,3,221,5,3
	.word	12047
	.byte	7
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,3,224,5,16,4,8
	.byte	'SRC_CH0',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'SRC_CH1',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'SRC_CH2',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'SRC_CH3',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'SRC_CH4',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'SRC_CH5',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'SRC_CH6',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'SRC_CH7',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	379
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,3,235,5,3
	.word	12193
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,3,238,5,16,4,8
	.byte	'CNT',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,3,242,5,3
	.word	12440
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,3,245,5,16,4,8
	.byte	'CNTS',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,3,249,5,3
	.word	12543
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,3,252,5,16,4,8
	.byte	'TIM_EN',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'TIM_MODE',0,4
	.word	379
	.byte	3,28,2,35,0,8
	.byte	'OSM',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'CICTRL',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'TBU0x_SEL',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'GPR0_SEL',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'GPR1_SEL',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'CNTS_SEL',0,4
	.word	379
	.byte	1,19,2,35,0,8
	.byte	'DSL',0,4
	.word	379
	.byte	1,18,2,35,0,8
	.byte	'ISL',0,4
	.word	379
	.byte	1,17,2,35,0,8
	.byte	'ECNT_RESET',0,4
	.word	379
	.byte	1,16,2,35,0,8
	.byte	'FLT_EN',0,4
	.word	379
	.byte	1,15,2,35,0,8
	.byte	'FLT_CNT_FRQ',0,4
	.word	379
	.byte	2,13,2,35,0,8
	.byte	'EXT_CAP_EN',0,4
	.word	379
	.byte	1,12,2,35,0,8
	.byte	'FLT_MODE_RE',0,4
	.word	379
	.byte	1,11,2,35,0,8
	.byte	'FLT_CTR_RE',0,4
	.word	379
	.byte	1,10,2,35,0,8
	.byte	'FLT_MODE_FE',0,4
	.word	379
	.byte	1,9,2,35,0,8
	.byte	'FLT_CTR_FE',0,4
	.word	379
	.byte	1,8,2,35,0,8
	.byte	'CLK_SEL',0,4
	.word	379
	.byte	3,5,2,35,0,8
	.byte	'FR_ECNT_OFL',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'EGPR0_SEL',0,4
	.word	379
	.byte	1,3,2,35,0,8
	.byte	'EGPR1_SEL',0,4
	.word	379
	.byte	1,2,2,35,0,8
	.byte	'TOCTRL',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,3,150,6,3
	.word	12642
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,3,153,6,16,4,8
	.byte	'ECNT',0,4
	.word	379
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,3,157,6,3
	.word	13190
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,3,160,6,16,4,8
	.byte	'EXT_CAP_SRC',0,4
	.word	379
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	379
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,3,164,6,3
	.word	13296
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,3,167,6,16,4,8
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'TODET_EIRQ_EN',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	379
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,3,176,6,3
	.word	13410
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,3,179,6,16,4,8
	.byte	'FLT_FE',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,3,183,6,3
	.word	13665
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,3,186,6,16,4,8
	.byte	'FLT_RE',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,3,190,6,3
	.word	13777
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,3,193,6,16,4,8
	.byte	'GPR0',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,3,197,6,3
	.word	13889
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,3,200,6,16,4,8
	.byte	'GPR1',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'ECNT',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,3,204,6,3
	.word	13988
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,3,207,6,16,4,8
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'TODET_IRQ_EN',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	379
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,3,216,6,3
	.word	14087
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,3,219,6,16,4,8
	.byte	'TRG_NEWVAL',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'TRG_ECNTOFL',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'TRG_CNTOFL',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'TRG_GPRzOFL',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'TRG_TODET',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'TRG_GLITCHDET',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	379
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,3,228,6,3
	.word	14334
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,3,231,6,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,3,235,6,3
	.word	14573
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,3,238,6,16,4,8
	.byte	'NEWVAL',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'ECNTOFL',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'CNTOFL',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'GPRzOFL',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'TODET',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'GLITCHDET',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'reserved_6',0,4
	.word	379
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,3,247,6,3
	.word	14690
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,3,250,6,16,4,8
	.byte	'TO_CNT',0,4
	.word	379
	.byte	8,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	379
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,3,254,6,3
	.word	14903
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,3,129,7,16,4,8
	.byte	'TOV',0,4
	.word	379
	.byte	8,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	379
	.byte	20,4,2,35,0,8
	.byte	'TCS',0,4
	.word	379
	.byte	3,1,2,35,0,8
	.byte	'reserved_31',0,4
	.word	379
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,3,135,7,3
	.word	15010
	.byte	7
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,3,138,7,16,4,8
	.byte	'VAL_0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'MODE_0',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'VAL_1',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'MODE_1',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'VAL_2',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'MODE_2',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'VAL_3',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'MODE_3',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'VAL_4',0,4
	.word	379
	.byte	2,14,2,35,0,8
	.byte	'MODE_4',0,4
	.word	379
	.byte	2,12,2,35,0,8
	.byte	'VAL_5',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'MODE_5',0,4
	.word	379
	.byte	2,8,2,35,0,8
	.byte	'VAL_6',0,4
	.word	379
	.byte	2,6,2,35,0,8
	.byte	'MODE_6',0,4
	.word	379
	.byte	2,4,2,35,0,8
	.byte	'VAL_7',0,4
	.word	379
	.byte	2,2,2,35,0,8
	.byte	'MODE_7',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,3,156,7,3
	.word	15152
	.byte	7
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,3,159,7,16,4,8
	.byte	'F_OUT',0,4
	.word	379
	.byte	8,24,2,35,0,8
	.byte	'F_IN',0,4
	.word	379
	.byte	8,16,2,35,0,8
	.byte	'TIM_IN',0,4
	.word	379
	.byte	8,8,2,35,0,8
	.byte	'reserved_24',0,4
	.word	379
	.byte	8,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,3,165,7,3
	.word	15497
	.byte	7
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,3,168,7,16,4,8
	.byte	'RST_CH0',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	379
	.byte	1,29,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	379
	.byte	1,28,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	379
	.byte	1,27,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	379
	.byte	1,26,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	379
	.byte	1,25,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	379
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	379
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_GTM_TIM_RST_Bits',0,3,179,7,3
	.word	15638
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,3,182,7,16,4,8
	.byte	'CM0',0,4
	.word	379
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,3,186,7,3
	.word	15871
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,3,189,7,16,4,8
	.byte	'CM1',0,4
	.word	379
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,3,193,7,3
	.word	15974
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,3,196,7,16,4,8
	.byte	'CN0',0,4
	.word	379
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,3,200,7,3
	.word	16077
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,3,203,7,16,4,8
	.byte	'reserved_0',0,4
	.word	379
	.byte	11,21,2,35,0,8
	.byte	'SL',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'CLK_SRC_SR',0,4
	.word	379
	.byte	3,17,2,35,0,8
	.byte	'reserved_15',0,4
	.word	379
	.byte	5,12,2,35,0,8
	.byte	'RST_CCU0',0,4
	.word	379
	.byte	1,11,2,35,0,8
	.byte	'OSM_TRIG',0,4
	.word	379
	.byte	1,10,2,35,0,8
	.byte	'EXT_TRIG',0,4
	.word	379
	.byte	1,9,2,35,0,8
	.byte	'EXTTRIGOUT',0,4
	.word	379
	.byte	1,8,2,35,0,8
	.byte	'TRIGOUT',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	379
	.byte	1,6,2,35,0,8
	.byte	'OSM',0,4
	.word	379
	.byte	1,5,2,35,0,8
	.byte	'BITREV',0,4
	.word	379
	.byte	1,4,2,35,0,8
	.byte	'reserved_28',0,4
	.word	379
	.byte	4,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,3,218,7,3
	.word	16180
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,3,221,7,16,4,8
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,3,226,7,3
	.word	16508
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,3,229,7,16,4,8
	.byte	'TRG_CCU0TC0',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'TRG_CCU1TC0',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,3,234,7,3
	.word	16651
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,3,237,7,16,4,8
	.byte	'IRQ_MODE',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,3,241,7,3
	.word	16800
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,3,244,7,16,4,8
	.byte	'CCU0TC',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'CCU1TC',0,4
	.word	379
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	379
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,3,249,7,3
	.word	16917
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,3,252,7,16,4,8
	.byte	'SR0',0,4
	.word	379
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,3,128,8,3
	.word	17054
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,3,131,8,16,4,8
	.byte	'SR1',0,4
	.word	379
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,3,135,8,3
	.word	17157
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,3,138,8,16,4,8
	.byte	'OL',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	379
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,3,142,8,3
	.word	17260
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,3,145,8,16,4,8
	.byte	'ACT_TB',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'TB_TRIG',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'TBU_SEL',0,4
	.word	379
	.byte	2,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	379
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,3,151,8,3
	.word	17363
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,3,154,8,16,4,8
	.byte	'ENDIS_CTRL0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CTRL1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CTRL2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_CTRL3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_CTRL4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_CTRL5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_CTRL6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_CTRL7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,3,165,8,3
	.word	17517
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,3,168,8,16,4,8
	.byte	'ENDIS_STAT0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_STAT1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_STAT2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_STAT3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_STAT4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_STAT5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_STAT6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_STAT7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,3,179,8,3
	.word	17807
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,3,182,8,16,4,8
	.byte	'FUPD_CTRL0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'FUPD_CTRL1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'FUPD_CTRL2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'FUPD_CTRL3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'FUPD_CTRL4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'FUPD_CTRL5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'FUPD_CTRL6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'FUPD_CTRL7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'RSTCN0_CH0',0,4
	.word	379
	.byte	2,14,2,35,0,8
	.byte	'RSTCN0_CH1',0,4
	.word	379
	.byte	2,12,2,35,0,8
	.byte	'RSTCN0_CH2',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'RSTCN0_CH3',0,4
	.word	379
	.byte	2,8,2,35,0,8
	.byte	'RSTCN0_CH4',0,4
	.word	379
	.byte	2,6,2,35,0,8
	.byte	'RSTCN0_CH5',0,4
	.word	379
	.byte	2,4,2,35,0,8
	.byte	'RSTCN0_CH6',0,4
	.word	379
	.byte	2,2,2,35,0,8
	.byte	'RSTCN0_CH7',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,3,200,8,3
	.word	18097
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,3,203,8,16,4,8
	.byte	'HOST_TRIG',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	379
	.byte	7,24,2,35,0,8
	.byte	'RST_CH0',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	379
	.byte	1,22,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	379
	.byte	1,21,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	379
	.byte	1,19,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	379
	.byte	1,18,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	379
	.byte	1,17,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	379
	.byte	1,16,2,35,0,8
	.byte	'UPEN_CTRL0',0,4
	.word	379
	.byte	2,14,2,35,0,8
	.byte	'UPEN_CTRL1',0,4
	.word	379
	.byte	2,12,2,35,0,8
	.byte	'UPEN_CTRL2',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'UPEN_CTRL3',0,4
	.word	379
	.byte	2,8,2,35,0,8
	.byte	'UPEN_CTRL4',0,4
	.word	379
	.byte	2,6,2,35,0,8
	.byte	'UPEN_CTRL5',0,4
	.word	379
	.byte	2,4,2,35,0,8
	.byte	'UPEN_CTRL6',0,4
	.word	379
	.byte	2,2,2,35,0,8
	.byte	'UPEN_CTRL7',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,3,223,8,3
	.word	18530
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,3,226,8,16,4,8
	.byte	'INT_TRIG0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'INT_TRIG1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'INT_TRIG2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'INT_TRIG3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'INT_TRIG4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'INT_TRIG5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'INT_TRIG6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'INT_TRIG7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,3,237,8,3
	.word	18980
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,3,240,8,16,4,8
	.byte	'OUTEN_CTRL0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_CTRL1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_CTRL2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_CTRL3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_CTRL4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_CTRL5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_CTRL6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_CTRL7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,3,251,8,3
	.word	19250
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,3,254,8,16,4,8
	.byte	'OUTEN_STAT0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_STAT1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_STAT2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_STAT3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_STAT4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_STAT5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_STAT6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_STAT7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,3,137,9,3
	.word	19540
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,3,140,9,16,4,8
	.byte	'ACT_TB',0,4
	.word	379
	.byte	24,8,2,35,0,8
	.byte	'TB_TRIG',0,4
	.word	379
	.byte	1,7,2,35,0,8
	.byte	'TBU_SEL',0,4
	.word	379
	.byte	2,5,2,35,0,8
	.byte	'reserved_27',0,4
	.word	379
	.byte	5,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,3,146,9,3
	.word	19830
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,3,149,9,16,4,8
	.byte	'ENDIS_CTRL0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_CTRL1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_CTRL2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_CTRL3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_CTRL4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_CTRL5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_CTRL6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_CTRL7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,3,160,9,3
	.word	19984
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,3,163,9,16,4,8
	.byte	'ENDIS_STAT0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'ENDIS_STAT1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'ENDIS_STAT2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'ENDIS_STAT3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'ENDIS_STAT4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'ENDIS_STAT5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'ENDIS_STAT6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'ENDIS_STAT7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,3,174,9,3
	.word	20274
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,3,177,9,16,4,8
	.byte	'FUPD_CTRL0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'FUPD_CTRL1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'FUPD_CTRL2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'FUPD_CTRL3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'FUPD_CTRL4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'FUPD_CTRL5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'FUPD_CTRL6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'FUPD_CTRL7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'RSTCN0_CH0',0,4
	.word	379
	.byte	2,14,2,35,0,8
	.byte	'RSTCN0_CH1',0,4
	.word	379
	.byte	2,12,2,35,0,8
	.byte	'RSTCN0_CH2',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'RSTCN0_CH3',0,4
	.word	379
	.byte	2,8,2,35,0,8
	.byte	'RSTCN0_CH4',0,4
	.word	379
	.byte	2,6,2,35,0,8
	.byte	'RSTCN0_CH5',0,4
	.word	379
	.byte	2,4,2,35,0,8
	.byte	'RSTCN0_CH6',0,4
	.word	379
	.byte	2,2,2,35,0,8
	.byte	'RSTCN0_CH7',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,3,195,9,3
	.word	20564
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,3,198,9,16,4,8
	.byte	'HOST_TRIG',0,4
	.word	379
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	379
	.byte	7,24,2,35,0,8
	.byte	'RST_CH0',0,4
	.word	379
	.byte	1,23,2,35,0,8
	.byte	'RST_CH1',0,4
	.word	379
	.byte	1,22,2,35,0,8
	.byte	'RST_CH2',0,4
	.word	379
	.byte	1,21,2,35,0,8
	.byte	'RST_CH3',0,4
	.word	379
	.byte	1,20,2,35,0,8
	.byte	'RST_CH4',0,4
	.word	379
	.byte	1,19,2,35,0,8
	.byte	'RST_CH5',0,4
	.word	379
	.byte	1,18,2,35,0,8
	.byte	'RST_CH6',0,4
	.word	379
	.byte	1,17,2,35,0,8
	.byte	'RST_CH7',0,4
	.word	379
	.byte	1,16,2,35,0,8
	.byte	'UPEN_CTRL0',0,4
	.word	379
	.byte	2,14,2,35,0,8
	.byte	'UPEN_CTRL1',0,4
	.word	379
	.byte	2,12,2,35,0,8
	.byte	'UPEN_CTRL2',0,4
	.word	379
	.byte	2,10,2,35,0,8
	.byte	'UPEN_CTRL3',0,4
	.word	379
	.byte	2,8,2,35,0,8
	.byte	'UPEN_CTRL4',0,4
	.word	379
	.byte	2,6,2,35,0,8
	.byte	'UPEN_CTRL5',0,4
	.word	379
	.byte	2,4,2,35,0,8
	.byte	'UPEN_CTRL6',0,4
	.word	379
	.byte	2,2,2,35,0,8
	.byte	'UPEN_CTRL7',0,4
	.word	379
	.byte	2,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,3,218,9,3
	.word	20997
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,3,221,9,16,4,8
	.byte	'INT_TRIG0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'INT_TRIG1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'INT_TRIG2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'INT_TRIG3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'INT_TRIG4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'INT_TRIG5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'INT_TRIG6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'INT_TRIG7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,3,232,9,3
	.word	21447
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,3,235,9,16,4,8
	.byte	'OUTEN_CTRL0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_CTRL1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_CTRL2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_CTRL3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_CTRL4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_CTRL5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_CTRL6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_CTRL7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,3,246,9,3
	.word	21717
	.byte	7
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,3,249,9,16,4,8
	.byte	'OUTEN_STAT0',0,4
	.word	379
	.byte	2,30,2,35,0,8
	.byte	'OUTEN_STAT1',0,4
	.word	379
	.byte	2,28,2,35,0,8
	.byte	'OUTEN_STAT2',0,4
	.word	379
	.byte	2,26,2,35,0,8
	.byte	'OUTEN_STAT3',0,4
	.word	379
	.byte	2,24,2,35,0,8
	.byte	'OUTEN_STAT4',0,4
	.word	379
	.byte	2,22,2,35,0,8
	.byte	'OUTEN_STAT5',0,4
	.word	379
	.byte	2,20,2,35,0,8
	.byte	'OUTEN_STAT6',0,4
	.word	379
	.byte	2,18,2,35,0,8
	.byte	'OUTEN_STAT7',0,4
	.word	379
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	379
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,3,132,10,3
	.word	22007
	.byte	9,3,140,10,9,4,6
	.byte	'unsigned int',0,4,7,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,6
	.byte	'int',0,4,5,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	353
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN0',0,3,145,10,3
	.word	22297
	.byte	9,3,148,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	926
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ACCEN1',0,3,153,10,3
	.word	22384
	.byte	9,3,156,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1003
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,3,161,10,3
	.word	22448
	.byte	9,3,164,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1157
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,3,169,10,3
	.word	22518
	.byte	9,3,172,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1311
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,3,177,10,3
	.word	22588
	.byte	9,3,180,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1439
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_MODE',0,3,185,10,3
	.word	22658
	.byte	9,3,188,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1746
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,3,193,10,3
	.word	22727
	.byte	9,3,196,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1948
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,3,201,10,3
	.word	22796
	.byte	9,3,204,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2061
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CLC',0,3,209,10,3
	.word	22865
	.byte	9,3,212,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2204
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,3,217,10,3
	.word	22926
	.byte	9,3,220,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2321
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,3,225,10,3
	.word	22999
	.byte	9,3,228,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2456
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,3,233,10,3
	.word	23071
	.byte	9,3,236,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2591
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_CLK_EN',0,3,241,10,3
	.word	23143
	.byte	9,3,244,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2911
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,3,249,10,3
	.word	23211
	.byte	9,3,252,10,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3023
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,3,129,11,3
	.word	23281
	.byte	9,3,132,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3135
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,3,137,11,3
	.word	23351
	.byte	9,3,140,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3251
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,3,145,11,3
	.word	23423
	.byte	9,3,148,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3363
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,3,153,11,3
	.word	23493
	.byte	9,3,156,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3475
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_CTRL',0,3,161,11,3
	.word	23563
	.byte	9,3,164,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3628
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,3,169,11,3
	.word	23625
	.byte	9,3,172,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4140
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,3,177,11,3
	.word	23695
	.byte	9,3,180,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4761
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,3,185,11,3
	.word	23765
	.byte	9,3,188,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5484
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_CTRL',0,3,193,11,3
	.word	23838
	.byte	9,3,196,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5628
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_DTV_CH',0,3,201,11,3
	.word	23904
	.byte	9,3,204,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5777
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,3,209,11,3
	.word	23972
	.byte	9,3,212,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5992
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_EIRQ_EN',0,3,217,11,3
	.word	24041
	.byte	9,3,220,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6196
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_HW_CONF',0,3,225,11,3
	.word	24106
	.byte	9,3,228,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6553
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_0',0,3,233,11,3
	.word	24171
	.byte	9,3,236,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6681
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_2',0,3,241,11,3
	.word	24239
	.byte	9,3,244,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6960
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_6',0,3,249,11,3
	.word	24307
	.byte	9,3,252,11,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7805
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,3,129,12,3
	.word	24375
	.byte	9,3,132,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8098
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,3,137,12,3
	.word	24446
	.byte	9,3,140,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8252
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,3,145,12,3
	.word	24516
	.byte	9,3,148,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8422
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,3,153,12,3
	.word	24593
	.byte	9,3,156,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8763
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,3,161,12,3
	.word	24668
	.byte	9,3,164,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8988
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_EN',0,3,169,12,3
	.word	24744
	.byte	9,3,172,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9186
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_FORCINT',0,3,177,12,3
	.word	24808
	.byte	9,3,180,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9382
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_MODE',0,3,185,12,3
	.word	24877
	.byte	9,3,188,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9485
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,3,193,12,3
	.word	24943
	.byte	9,3,196,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9663
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRST0',0,3,201,12,3
	.word	25011
	.byte	9,3,204,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9774
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRST1',0,3,209,12,3
	.word	25074
	.byte	9,3,212,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9866
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_KRSTCLR',0,3,217,12,3
	.word	25137
	.byte	9,3,220,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	9962
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OCS',0,3,225,12,3
	.word	25202
	.byte	9,3,228,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10108
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_ODA',0,3,233,12,3
	.word	25263
	.byte	9,3,236,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10214
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU0T',0,3,241,12,3
	.word	25324
	.byte	9,3,244,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10345
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU1T',0,3,249,12,3
	.word	25388
	.byte	9,3,252,12,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10476
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTBU2T',0,3,129,13,3
	.word	25452
	.byte	9,3,132,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10607
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTSC0',0,3,137,13,3
	.word	25516
	.byte	9,3,140,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	10889
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_OTSS',0,3,145,13,3
	.word	25579
	.byte	9,3,148,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11061
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_REV',0,3,153,13,3
	.word	25641
	.byte	9,3,156,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11239
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_RST',0,3,161,13,3
	.word	25702
	.byte	9,3,164,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11327
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,3,169,13,3
	.word	25763
	.byte	9,3,172,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11435
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,3,177,13,3
	.word	25833
	.byte	9,3,180,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11567
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,3,185,13,3
	.word	25903
	.byte	9,3,188,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11675
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,3,193,13,3
	.word	25973
	.byte	9,3,196,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11807
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,3,201,13,3
	.word	26043
	.byte	9,3,204,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11915
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,3,209,13,3
	.word	26113
	.byte	9,3,212,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12047
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TBU_CHEN',0,3,217,13,3
	.word	26183
	.byte	9,3,220,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12193
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,3,225,13,3
	.word	26249
	.byte	9,3,228,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12440
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNT',0,3,233,13,3
	.word	26321
	.byte	9,3,236,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12543
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,3,241,13,3
	.word	26389
	.byte	9,3,244,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12642
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,3,249,13,3
	.word	26458
	.byte	9,3,252,13,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13190
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,3,129,14,3
	.word	26527
	.byte	9,3,132,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13296
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,3,137,14,3
	.word	26596
	.byte	9,3,140,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13410
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,3,145,14,3
	.word	26666
	.byte	9,3,148,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13665
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,3,153,14,3
	.word	26738
	.byte	9,3,156,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13777
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,3,161,14,3
	.word	26809
	.byte	9,3,164,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13889
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,3,169,14,3
	.word	26880
	.byte	9,3,172,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13988
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,3,177,14,3
	.word	26949
	.byte	9,3,180,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14087
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,3,185,14,3
	.word	27018
	.byte	9,3,188,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14334
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,3,193,14,3
	.word	27089
	.byte	9,3,196,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14573
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,3,201,14,3
	.word	27165
	.byte	9,3,204,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14690
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,3,209,14,3
	.word	27238
	.byte	9,3,212,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14903
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,3,217,14,3
	.word	27313
	.byte	9,3,220,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15010
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,3,225,14,3
	.word	27382
	.byte	9,3,228,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15152
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_IN_SRC',0,3,233,14,3
	.word	27451
	.byte	9,3,236,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15497
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_INP_VAL',0,3,241,14,3
	.word	27519
	.byte	9,3,244,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15638
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TIM_RST',0,3,249,14,3
	.word	27588
	.byte	9,3,252,14,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15871
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM0',0,3,129,15,3
	.word	27653
	.byte	9,3,132,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15974
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CM1',0,3,137,15,3
	.word	27721
	.byte	9,3,140,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16077
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CN0',0,3,145,15,3
	.word	27789
	.byte	9,3,148,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16180
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,3,153,15,3
	.word	27857
	.byte	9,3,156,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16508
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,3,161,15,3
	.word	27926
	.byte	9,3,164,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16651
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,3,169,15,3
	.word	27997
	.byte	9,3,172,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16800
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,3,177,15,3
	.word	28073
	.byte	9,3,180,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16917
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,3,185,15,3
	.word	28146
	.byte	9,3,188,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17054
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR0',0,3,193,15,3
	.word	28221
	.byte	9,3,196,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17157
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_SR1',0,3,201,15,3
	.word	28289
	.byte	9,3,204,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17260
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_CH_STAT',0,3,209,15,3
	.word	28357
	.byte	9,3,212,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17363
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,3,217,15,3
	.word	28426
	.byte	9,3,220,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17517
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,3,225,15,3
	.word	28499
	.byte	9,3,228,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17807
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,3,233,15,3
	.word	28576
	.byte	9,3,236,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18097
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,3,241,15,3
	.word	28653
	.byte	9,3,244,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18530
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,3,249,15,3
	.word	28729
	.byte	9,3,252,15,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18980
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,3,129,16,3
	.word	28804
	.byte	9,3,132,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19250
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,3,137,16,3
	.word	28879
	.byte	9,3,140,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19540
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,3,145,16,3
	.word	28956
	.byte	9,3,148,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19830
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,3,153,16,3
	.word	29033
	.byte	9,3,156,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19984
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,3,161,16,3
	.word	29106
	.byte	9,3,164,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20274
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,3,169,16,3
	.word	29183
	.byte	9,3,172,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20564
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,3,177,16,3
	.word	29260
	.byte	9,3,180,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20997
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,3,185,16,3
	.word	29336
	.byte	9,3,188,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21447
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,3,193,16,3
	.word	29411
	.byte	9,3,196,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21717
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,3,201,16,3
	.word	29486
	.byte	9,3,204,16,9,4,10
	.byte	'U',0,4
	.word	22303
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	22330
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22007
	.byte	2,35,0,0,4
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,3,209,16,3
	.word	29563
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,3,220,16,25,4,10
	.byte	'CTRL',0,4
	.word	22926
	.byte	2,35,0,0,11
	.word	29640
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK0_5',0,3,223,16,3
	.word	29681
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_6',0,3,226,16,25,4,10
	.byte	'CTRL',0,4
	.word	22999
	.byte	2,35,0,0,11
	.word	29714
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK_6',0,3,229,16,3
	.word	29754
	.byte	7
	.byte	'_Ifx_GTM_CMU_CLK_7',0,3,232,16,25,4,10
	.byte	'CTRL',0,4
	.word	23071
	.byte	2,35,0,0,11
	.word	29786
	.byte	4
	.byte	'Ifx_GTM_CMU_CLK_7',0,3,235,16,3
	.word	29826
	.byte	7
	.byte	'_Ifx_GTM_CMU_ECLK',0,3,238,16,25,8,10
	.byte	'NUM',0,4
	.word	23281
	.byte	2,35,0,10
	.byte	'DEN',0,4
	.word	23211
	.byte	2,35,4,0,11
	.word	29858
	.byte	4
	.byte	'Ifx_GTM_CMU_ECLK',0,3,242,16,3
	.word	29909
	.byte	7
	.byte	'_Ifx_GTM_CMU_FXCLK',0,3,245,16,25,4,10
	.byte	'CTRL',0,4
	.word	23351
	.byte	2,35,0,0,11
	.word	29940
	.byte	4
	.byte	'Ifx_GTM_CMU_FXCLK',0,3,248,16,3
	.word	29980
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,3,251,16,25,4,10
	.byte	'OUTSEL',0,4
	.word	24516
	.byte	2,35,0,0,11
	.word	30012
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,3,254,16,3
	.word	30057
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_T',0,3,129,17,25,32,12,32
	.word	24593
	.byte	13,7,0,10
	.byte	'OUTSEL',0,32
	.word	30118
	.byte	2,35,0,0,11
	.word	30092
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_T',0,3,132,17,3
	.word	30144
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,3,135,17,25,32,10
	.byte	'INSEL',0,4
	.word	24668
	.byte	2,35,0,12,28
	.word	233
	.byte	13,27,0,10
	.byte	'reserved_4',0,28
	.word	30220
	.byte	2,35,4,0,11
	.word	30177
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,3,139,17,3
	.word	30250
	.byte	7
	.byte	'_Ifx_GTM_TIM_CH',0,3,142,17,25,116,10
	.byte	'GPR0',0,4
	.word	26880
	.byte	2,35,0,10
	.byte	'GPR1',0,4
	.word	26949
	.byte	2,35,4,10
	.byte	'CNT',0,4
	.word	26321
	.byte	2,35,8,10
	.byte	'ECNT',0,4
	.word	26527
	.byte	2,35,12,10
	.byte	'CNTS',0,4
	.word	26389
	.byte	2,35,16,10
	.byte	'TDUC',0,4
	.word	27313
	.byte	2,35,20,10
	.byte	'TDUV',0,4
	.word	27382
	.byte	2,35,24,10
	.byte	'FLT_RE',0,4
	.word	26809
	.byte	2,35,28,10
	.byte	'FLT_FE',0,4
	.word	26738
	.byte	2,35,32,10
	.byte	'CTRL',0,4
	.word	26458
	.byte	2,35,36,10
	.byte	'ECTRL',0,4
	.word	26596
	.byte	2,35,40,10
	.byte	'IRQ_NOTIFY',0,4
	.word	27238
	.byte	2,35,44,10
	.byte	'IRQ_EN',0,4
	.word	27018
	.byte	2,35,48,10
	.byte	'IRQ_FORCINT',0,4
	.word	27089
	.byte	2,35,52,10
	.byte	'IRQ_MODE',0,4
	.word	27165
	.byte	2,35,56,10
	.byte	'EIRQ_EN',0,4
	.word	26666
	.byte	2,35,60,12,52
	.word	233
	.byte	13,51,0,10
	.byte	'reserved_40',0,52
	.word	30557
	.byte	2,35,64,0,11
	.word	30285
	.byte	4
	.byte	'Ifx_GTM_TIM_CH',0,3,161,17,3
	.word	30588
	.byte	7
	.byte	'_Ifx_GTM_TOM_CH',0,3,164,17,25,48,10
	.byte	'CTRL',0,4
	.word	27857
	.byte	2,35,0,10
	.byte	'SR0',0,4
	.word	28221
	.byte	2,35,4,10
	.byte	'SR1',0,4
	.word	28289
	.byte	2,35,8,10
	.byte	'CM0',0,4
	.word	27653
	.byte	2,35,12,10
	.byte	'CM1',0,4
	.word	27721
	.byte	2,35,16,10
	.byte	'CN0',0,4
	.word	27789
	.byte	2,35,20,10
	.byte	'STAT',0,4
	.word	28357
	.byte	2,35,24,10
	.byte	'IRQ_NOTIFY',0,4
	.word	28146
	.byte	2,35,28,10
	.byte	'IRQ_EN',0,4
	.word	27926
	.byte	2,35,32,10
	.byte	'IRQ_FORCINT',0,4
	.word	27997
	.byte	2,35,36,10
	.byte	'IRQ_MODE',0,4
	.word	28073
	.byte	2,35,40,12,4
	.word	233
	.byte	13,3,0,10
	.byte	'reserved_2C',0,4
	.word	30807
	.byte	2,35,44,0,11
	.word	30617
	.byte	4
	.byte	'Ifx_GTM_TOM_CH',0,3,178,17,3
	.word	30838
	.byte	7
	.byte	'_Ifx_GTM_BRIDGE',0,3,191,17,25,12,10
	.byte	'MODE',0,4
	.word	22658
	.byte	2,35,0,10
	.byte	'PTR1',0,4
	.word	22727
	.byte	2,35,4,10
	.byte	'PTR2',0,4
	.word	22796
	.byte	2,35,8,0,11
	.word	30867
	.byte	4
	.byte	'Ifx_GTM_BRIDGE',0,3,196,17,3
	.word	30932
	.byte	7
	.byte	'_Ifx_GTM_CMU',0,3,199,17,25,72,10
	.byte	'CLK_EN',0,4
	.word	23143
	.byte	2,35,0,10
	.byte	'GCLK_NUM',0,4
	.word	23493
	.byte	2,35,4,10
	.byte	'GCLK_DEN',0,4
	.word	23423
	.byte	2,35,8,12,24
	.word	29640
	.byte	13,5,0,11
	.word	31032
	.byte	10
	.byte	'CLK0_5',0,24
	.word	31041
	.byte	2,35,12,11
	.word	29714
	.byte	10
	.byte	'CLK_6',0,4
	.word	31062
	.byte	2,35,36,11
	.word	29786
	.byte	10
	.byte	'CLK_7',0,4
	.word	31082
	.byte	2,35,40,12,24
	.word	29858
	.byte	13,2,0,11
	.word	31102
	.byte	10
	.byte	'ECLK',0,24
	.word	31111
	.byte	2,35,44,11
	.word	29940
	.byte	10
	.byte	'FXCLK',0,4
	.word	31130
	.byte	2,35,68,0,11
	.word	30961
	.byte	4
	.byte	'Ifx_GTM_CMU',0,3,209,17,3
	.word	31151
	.byte	7
	.byte	'_Ifx_GTM_DTM',0,3,212,17,25,36,10
	.byte	'CTRL',0,4
	.word	23838
	.byte	2,35,0,10
	.byte	'CH_CTRL1',0,4
	.word	23625
	.byte	2,35,4,10
	.byte	'CH_CTRL2',0,4
	.word	23695
	.byte	2,35,8,10
	.byte	'CH_CTRL2_SR',0,4
	.word	23765
	.byte	2,35,12,10
	.byte	'PS_CTRL',0,4
	.word	23972
	.byte	2,35,16,12,16
	.word	23904
	.byte	13,3,0,10
	.byte	'DTV_CH',0,16
	.word	31284
	.byte	2,35,20,0,11
	.word	31177
	.byte	4
	.byte	'Ifx_GTM_DTM',0,3,220,17,3
	.word	31310
	.byte	7
	.byte	'_Ifx_GTM_ICM',0,3,223,17,25,60,10
	.byte	'IRQG_0',0,4
	.word	24171
	.byte	2,35,0,10
	.byte	'reserved_4',0,4
	.word	30807
	.byte	2,35,4,10
	.byte	'IRQG_2',0,4
	.word	24239
	.byte	2,35,8,12,12
	.word	233
	.byte	13,11,0,10
	.byte	'reserved_C',0,12
	.word	31407
	.byte	2,35,12,10
	.byte	'IRQG_6',0,4
	.word	24307
	.byte	2,35,24,12,20
	.word	233
	.byte	13,19,0,10
	.byte	'reserved_1C',0,20
	.word	31452
	.byte	2,35,28,10
	.byte	'IRQG_MEI',0,4
	.word	24446
	.byte	2,35,48,10
	.byte	'reserved_34',0,4
	.word	30807
	.byte	2,35,52,10
	.byte	'IRQG_CEI1',0,4
	.word	24375
	.byte	2,35,56,0,11
	.word	31336
	.byte	4
	.byte	'Ifx_GTM_ICM',0,3,234,17,3
	.word	31541
	.byte	7
	.byte	'_Ifx_GTM_INOUTSEL',0,3,237,17,25,148,1,12,32
	.word	30177
	.byte	13,0,0,11
	.word	31592
	.byte	10
	.byte	'TIM',0,32
	.word	31601
	.byte	2,35,0,11
	.word	30092
	.byte	10
	.byte	'T',0,32
	.word	31619
	.byte	2,35,32,12,80
	.word	233
	.byte	13,79,0,10
	.byte	'reserved_40',0,80
	.word	31635
	.byte	2,35,64,11
	.word	30012
	.byte	10
	.byte	'CAN',0,4
	.word	31665
	.byte	3,35,144,1,0,11
	.word	31567
	.byte	4
	.byte	'Ifx_GTM_INOUTSEL',0,3,243,17,3
	.word	31685
	.byte	7
	.byte	'_Ifx_GTM_TBU',0,3,246,17,25,28,10
	.byte	'CHEN',0,4
	.word	26183
	.byte	2,35,0,10
	.byte	'CH0_CTRL',0,4
	.word	25833
	.byte	2,35,4,10
	.byte	'CH0_BASE',0,4
	.word	25763
	.byte	2,35,8,10
	.byte	'CH1_CTRL',0,4
	.word	25973
	.byte	2,35,12,10
	.byte	'CH1_BASE',0,4
	.word	25903
	.byte	2,35,16,10
	.byte	'CH2_CTRL',0,4
	.word	26113
	.byte	2,35,20,10
	.byte	'CH2_BASE',0,4
	.word	26043
	.byte	2,35,24,0,11
	.word	31716
	.byte	4
	.byte	'Ifx_GTM_TBU',0,3,255,17,3
	.word	31858
	.byte	7
	.byte	'_Ifx_GTM_TIM',0,3,130,18,25,128,8,11
	.word	30285
	.byte	10
	.byte	'CH0',0,116
	.word	31904
	.byte	2,35,0,10
	.byte	'INP_VAL',0,4
	.word	27519
	.byte	2,35,116,10
	.byte	'IN_SRC',0,4
	.word	27451
	.byte	2,35,120,10
	.byte	'RST',0,4
	.word	27588
	.byte	2,35,124,11
	.word	30285
	.byte	10
	.byte	'CH1',0,116
	.word	31968
	.byte	3,35,128,1,10
	.byte	'reserved_F4',0,12
	.word	31407
	.byte	3,35,244,1,11
	.word	30285
	.byte	10
	.byte	'CH2',0,116
	.word	32009
	.byte	3,35,128,2,10
	.byte	'reserved_174',0,12
	.word	31407
	.byte	3,35,244,2,11
	.word	30285
	.byte	10
	.byte	'CH3',0,116
	.word	32051
	.byte	3,35,128,3,10
	.byte	'reserved_1F4',0,12
	.word	31407
	.byte	3,35,244,3,11
	.word	30285
	.byte	10
	.byte	'CH4',0,116
	.word	32093
	.byte	3,35,128,4,10
	.byte	'reserved_274',0,12
	.word	31407
	.byte	3,35,244,4,11
	.word	30285
	.byte	10
	.byte	'CH5',0,116
	.word	32135
	.byte	3,35,128,5,10
	.byte	'reserved_2F4',0,12
	.word	31407
	.byte	3,35,244,5,11
	.word	30285
	.byte	10
	.byte	'CH6',0,116
	.word	32177
	.byte	3,35,128,6,10
	.byte	'reserved_374',0,12
	.word	31407
	.byte	3,35,244,6,11
	.word	30285
	.byte	10
	.byte	'CH7',0,116
	.word	32219
	.byte	3,35,128,7,10
	.byte	'reserved_3F4',0,12
	.word	31407
	.byte	3,35,244,7,0,11
	.word	31884
	.byte	4
	.byte	'Ifx_GTM_TIM',0,3,150,18,3
	.word	32262
	.byte	7
	.byte	'_Ifx_GTM_TOM',0,3,153,18,25,128,16,11
	.word	30617
	.byte	10
	.byte	'CH0',0,48
	.word	32308
	.byte	2,35,0,10
	.byte	'TGC0_GLB_CTRL',0,4
	.word	28729
	.byte	2,35,48,10
	.byte	'TGC0_ACT_TB',0,4
	.word	28426
	.byte	2,35,52,10
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	28653
	.byte	2,35,56,10
	.byte	'TGC0_INT_TRIG',0,4
	.word	28804
	.byte	2,35,60,11
	.word	30617
	.byte	10
	.byte	'CH1',0,48
	.word	32417
	.byte	2,35,64,10
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	28499
	.byte	2,35,112,10
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	28576
	.byte	2,35,116,10
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	28879
	.byte	2,35,120,10
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	28956
	.byte	2,35,124,11
	.word	30617
	.byte	10
	.byte	'CH2',0,48
	.word	32535
	.byte	3,35,128,1,12,16
	.word	233
	.byte	13,15,0,10
	.byte	'reserved_B0',0,16
	.word	32554
	.byte	3,35,176,1,11
	.word	30617
	.byte	10
	.byte	'CH3',0,48
	.word	32585
	.byte	3,35,192,1,10
	.byte	'reserved_F0',0,16
	.word	32554
	.byte	3,35,240,1,11
	.word	30617
	.byte	10
	.byte	'CH4',0,48
	.word	32626
	.byte	3,35,128,2,10
	.byte	'reserved_130',0,16
	.word	32554
	.byte	3,35,176,2,11
	.word	30617
	.byte	10
	.byte	'CH5',0,48
	.word	32668
	.byte	3,35,192,2,10
	.byte	'reserved_170',0,16
	.word	32554
	.byte	3,35,240,2,11
	.word	30617
	.byte	10
	.byte	'CH6',0,48
	.word	32710
	.byte	3,35,128,3,10
	.byte	'reserved_1B0',0,16
	.word	32554
	.byte	3,35,176,3,11
	.word	30617
	.byte	10
	.byte	'CH7',0,48
	.word	32752
	.byte	3,35,192,3,10
	.byte	'reserved_1F0',0,16
	.word	32554
	.byte	3,35,240,3,11
	.word	30617
	.byte	10
	.byte	'CH8',0,48
	.word	32794
	.byte	3,35,128,4,10
	.byte	'TGC1_GLB_CTRL',0,4
	.word	29336
	.byte	3,35,176,4,10
	.byte	'TGC1_ACT_TB',0,4
	.word	29033
	.byte	3,35,180,4,10
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	29260
	.byte	3,35,184,4,10
	.byte	'TGC1_INT_TRIG',0,4
	.word	29411
	.byte	3,35,188,4,11
	.word	30617
	.byte	10
	.byte	'CH9',0,48
	.word	32908
	.byte	3,35,192,4,10
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	29106
	.byte	3,35,240,4,10
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	29183
	.byte	3,35,244,4,10
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	29486
	.byte	3,35,248,4,10
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	29563
	.byte	3,35,252,4,11
	.word	30617
	.byte	10
	.byte	'CH10',0,48
	.word	33031
	.byte	3,35,128,5,10
	.byte	'reserved_2B0',0,16
	.word	32554
	.byte	3,35,176,5,11
	.word	30617
	.byte	10
	.byte	'CH11',0,48
	.word	33074
	.byte	3,35,192,5,10
	.byte	'reserved_2F0',0,16
	.word	32554
	.byte	3,35,240,5,11
	.word	30617
	.byte	10
	.byte	'CH12',0,48
	.word	33117
	.byte	3,35,128,6,10
	.byte	'reserved_330',0,16
	.word	32554
	.byte	3,35,176,6,11
	.word	30617
	.byte	10
	.byte	'CH13',0,48
	.word	33160
	.byte	3,35,192,6,10
	.byte	'reserved_370',0,16
	.word	32554
	.byte	3,35,240,6,11
	.word	30617
	.byte	10
	.byte	'CH14',0,48
	.word	33203
	.byte	3,35,128,7,10
	.byte	'reserved_3B0',0,16
	.word	32554
	.byte	3,35,176,7,11
	.word	30617
	.byte	10
	.byte	'CH15',0,48
	.word	33246
	.byte	3,35,192,7,12,144,8
	.word	233
	.byte	13,143,8,0,10
	.byte	'reserved_3F0',0,144,8
	.word	33266
	.byte	3,35,240,7,0,11
	.word	32288
	.byte	4
	.byte	'Ifx_GTM_TOM',0,3,199,18,3
	.word	33302
	.byte	14,4,130,4,20,64,10
	.byte	'CTRL',0,4
	.word	27857
	.byte	2,35,0,10
	.byte	'SR0',0,4
	.word	28221
	.byte	2,35,4,10
	.byte	'SR1',0,4
	.word	28289
	.byte	2,35,8,10
	.byte	'CM0',0,4
	.word	27653
	.byte	2,35,12,10
	.byte	'CM1',0,4
	.word	27721
	.byte	2,35,16,10
	.byte	'CN0',0,4
	.word	27789
	.byte	2,35,20,10
	.byte	'STAT',0,4
	.word	28357
	.byte	2,35,24,10
	.byte	'IRQ_NOTIFY',0,4
	.word	28146
	.byte	2,35,28,10
	.byte	'IRQ_EN',0,4
	.word	27926
	.byte	2,35,32,10
	.byte	'IRQ_FORCINT',0,4
	.word	27997
	.byte	2,35,36,10
	.byte	'IRQ_MODE',0,4
	.word	28073
	.byte	2,35,40,12,20
	.word	233
	.byte	13,19,0,10
	.byte	'reserved_2C',0,20
	.word	33502
	.byte	2,35,44,0,11
	.word	33328
	.byte	4
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,4,155,4,4
	.word	33533
	.byte	14,4,157,4,20,128,4,10
	.byte	'GLB_CTRL',0,4
	.word	28729
	.byte	2,35,0,10
	.byte	'ACT_TB',0,4
	.word	28426
	.byte	2,35,4,10
	.byte	'FUPD_CTRL',0,4
	.word	28653
	.byte	2,35,8,10
	.byte	'INT_TRIG',0,4
	.word	28804
	.byte	2,35,12,12,48
	.word	233
	.byte	13,47,0,10
	.byte	'reserved_tgc0',0,48
	.word	33645
	.byte	2,35,16,10
	.byte	'ENDIS_CTRL',0,4
	.word	28499
	.byte	2,35,64,10
	.byte	'ENDIS_STAT',0,4
	.word	28576
	.byte	2,35,68,10
	.byte	'OUTEN_CTRL',0,4
	.word	28879
	.byte	2,35,72,10
	.byte	'OUTEN_STAT',0,4
	.word	28956
	.byte	2,35,76,12,176,3
	.word	233
	.byte	13,175,3,0,10
	.byte	'reserved_tgc1',0,176,3
	.word	33757
	.byte	2,35,80,0,11
	.word	33567
	.byte	4
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,4,177,4,5
	.word	33793
	.byte	14,4,179,4,20,128,16,10
	.byte	'reserved_tom0',0,48
	.word	33645
	.byte	2,35,0,12,128,8
	.word	33567
	.byte	13,1,0,11
	.word	33858
	.byte	10
	.byte	'TGC',0,128,8
	.word	33868
	.byte	2,35,48,12,208,7
	.word	233
	.byte	13,207,7,0,10
	.byte	'reserved_tgc2',0,208,7
	.word	33887
	.byte	3,35,176,8,0,11
	.word	33828
	.byte	4
	.byte	'Ifx_GTM_TOM_TGCx',0,4,184,4,5
	.word	33924
	.byte	14,4,187,4,20,128,16,12,128,8
	.word	33328
	.byte	13,15,0,11
	.word	33962
	.byte	10
	.byte	'CH',0,128,8
	.word	33972
	.byte	2,35,0,12,128,8
	.word	233
	.byte	13,255,7,0,10
	.byte	'reserved_tom1',0,128,8
	.word	33990
	.byte	3,35,128,8,0,11
	.word	33955
	.byte	4
	.byte	'Ifx_GTM_TOM_CHx',0,4,191,4,5
	.word	34027
	.byte	14,4,212,4,20,128,1,10
	.byte	'CH_GPR0',0,4
	.word	26880
	.byte	2,35,0,10
	.byte	'CH_GPR1',0,4
	.word	26949
	.byte	2,35,4,10
	.byte	'CH_CNT',0,4
	.word	26321
	.byte	2,35,8,10
	.byte	'CH_ECNT',0,4
	.word	26527
	.byte	2,35,12,10
	.byte	'CH_CNTS',0,4
	.word	26389
	.byte	2,35,16,10
	.byte	'CH_TDUC',0,4
	.word	27313
	.byte	2,35,20,10
	.byte	'CH_TDUV',0,4
	.word	27382
	.byte	2,35,24,10
	.byte	'CH_FLT_RE',0,4
	.word	26809
	.byte	2,35,28,10
	.byte	'CH_FLT_FE',0,4
	.word	26738
	.byte	2,35,32,10
	.byte	'CH_CTRL',0,4
	.word	26458
	.byte	2,35,36,10
	.byte	'CH_ECTRL',0,4
	.word	26596
	.byte	2,35,40,10
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	27238
	.byte	2,35,44,10
	.byte	'CH_IRQ_EN',0,4
	.word	27018
	.byte	2,35,48,10
	.byte	'CH_IRQ_FORCINT',0,4
	.word	27089
	.byte	2,35,52,10
	.byte	'CH_IRQ_MODE',0,4
	.word	27165
	.byte	2,35,56,10
	.byte	'CH_EIRQ_EN',0,4
	.word	26666
	.byte	2,35,60,12,64
	.word	233
	.byte	13,63,0,10
	.byte	'reserved_40',0,64
	.word	34362
	.byte	2,35,64,0,11
	.word	34057
	.byte	4
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,4,248,4,4
	.word	34393
	.byte	14,4,250,4,20,8,10
	.byte	'IN_SRC',0,4
	.word	27451
	.byte	2,35,0,10
	.byte	'RST',0,4
	.word	27588
	.byte	2,35,4,0,11
	.word	34427
	.byte	4
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,4,255,4,4
	.word	34463
	.byte	14,4,129,5,21,128,16,12,128,8
	.word	34057
	.byte	13,7,0,11
	.word	34514
	.byte	10
	.byte	'CH',0,128,8
	.word	34524
	.byte	2,35,0,10
	.byte	'reserved_tim1',0,128,8
	.word	33990
	.byte	3,35,128,8,0,11
	.word	34507
	.byte	4
	.byte	'Ifx_GTM_TIM_CHx',0,4,133,5,4
	.word	34568
	.byte	14,4,135,5,20,128,16,12,120
	.word	233
	.byte	13,119,0,10
	.byte	'reserved_tim2',0,120
	.word	34605
	.byte	2,35,0,11
	.word	34427
	.byte	10
	.byte	'IN_SRC_RESET',0,8
	.word	34637
	.byte	2,35,120,12,128,15
	.word	233
	.byte	13,255,14,0,10
	.byte	'reserved_tim3',0,128,15
	.word	34664
	.byte	3,35,128,1,0,11
	.word	34598
	.byte	4
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,4,140,5,4
	.word	34701
	.byte	15,4,174,5,11,1,16
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,16
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,16
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,16
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,16
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,16
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,16
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,16
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,4
	.byte	'Gtm_ConfigurableClockType',0,4,184,5,4
	.word	34739
	.byte	15,4,188,5,11,1,16
	.byte	'GTM_LOW',0,0,16
	.byte	'GTM_HIGH',0,1,0,4
	.byte	'Gtm_OutputLevelType',0,4,192,5,4
	.word	34973
	.byte	15,4,195,5,11,1,16
	.byte	'TOM_GLB_CTRL',0,0,16
	.byte	'TOM_ACT_TB',0,1,16
	.byte	'TOM_FUPD_CTRL',0,2,16
	.byte	'TOM_INT_TRIG',0,3,16
	.byte	'TOM_RESERVED_0',0,4,16
	.byte	'TOM_RESERVED_1',0,5,16
	.byte	'TOM_RESERVED_2',0,6,16
	.byte	'TOM_RESERVED_3',0,7,16
	.byte	'TOM_RESERVED_4',0,8,16
	.byte	'TOM_RESERVED_5',0,9,16
	.byte	'TOM_RESERVED_6',0,10,16
	.byte	'TOM_RESERVED_7',0,11,16
	.byte	'TOM_RESERVED_8',0,12,16
	.byte	'TOM_RESERVED_9',0,13,16
	.byte	'TOM_RESERVED_10',0,14,16
	.byte	'TOM_RESERVED_11',0,15,16
	.byte	'TOM_ENDIS_CTRL',0,16,16
	.byte	'TOM_ENDIS_STAT',0,17,16
	.byte	'TOM_OUTEN_CTRL',0,18,16
	.byte	'TOM_OUTEN_STAT',0,19,0,4
	.byte	'Gtm_TomTimerRegistersType',0,4,217,5,4
	.word	35030
	.byte	14,4,221,5,11,8,10
	.byte	'FltRisingEdge',0,4
	.word	301
	.byte	2,35,0,10
	.byte	'FltFallingEdge',0,4
	.word	301
	.byte	2,35,4,0,4
	.byte	'Gtm_TimFilterType',0,4,225,5,4
	.word	35405
	.byte	4
	.byte	'Gtm_TbuChCtrlType',0,4,230,5,32
	.word	25833
	.byte	4
	.byte	'Gtm_TbuChBaseType',0,4,231,5,32
	.word	25763
	.byte	14,4,233,5,11,8,10
	.byte	'CH_CTRL',0,4
	.word	25833
	.byte	2,35,0,10
	.byte	'CH_BASE',0,4
	.word	25763
	.byte	2,35,4,0,4
	.byte	'Gtm_TbuChType',0,4,237,5,4
	.word	35540
	.byte	14,4,249,5,9,36,12,4
	.word	301
	.byte	13,0,0,10
	.byte	'TimInSel',0,4
	.word	35610
	.byte	2,35,0,12,32
	.word	301
	.byte	13,7,0,10
	.byte	'ToutSel',0,32
	.word	35637
	.byte	2,35,4,0,4
	.byte	'Gtm_PortConfigType',0,4,253,5,2
	.word	35604
	.byte	14,4,129,6,9,8,10
	.byte	'TimRisingEdgeFilter',0,4
	.word	301
	.byte	2,35,0,10
	.byte	'TimFallingEdgeFilter',0,4
	.word	301
	.byte	2,35,4,0,4
	.byte	'Gtm_TimFltType',0,4,134,6,2
	.word	35692
	.byte	14,4,138,6,11,24,10
	.byte	'TimUsage',0,1
	.word	233
	.byte	2,35,0,10
	.byte	'TimIrqEn',0,1
	.word	233
	.byte	2,35,1,10
	.byte	'TimErrIrqEn',0,1
	.word	233
	.byte	2,35,2,10
	.byte	'TimExtCapSrc',0,1
	.word	233
	.byte	2,35,3,10
	.byte	'TimCtrlValue',0,4
	.word	301
	.byte	2,35,4,17
	.word	35692
	.byte	3
	.word	35889
	.byte	10
	.byte	'GtmTimFltPtr',0,4
	.word	35894
	.byte	2,35,8,10
	.byte	'TimCntsValue',0,4
	.word	301
	.byte	2,35,12,10
	.byte	'TimTduValue',0,4
	.word	301
	.byte	2,35,16,10
	.byte	'TimInSrcSel',0,4
	.word	301
	.byte	2,35,20,0,4
	.byte	'Gtm_TimConfigType',0,4,151,6,4
	.word	35782
	.byte	14,4,154,6,11,40,12,8
	.word	233
	.byte	13,7,0,10
	.byte	'Gtm_TimUsage',0,8
	.word	36019
	.byte	2,35,0,12,16
	.word	233
	.byte	13,15,0,12,32
	.word	36050
	.byte	13,1,0,10
	.byte	'Gtm_TomUsage',0,32
	.word	36059
	.byte	2,35,8,0,4
	.byte	'Gtm_ModUsageConfigType',0,4,163,6,4
	.word	36013
	.byte	14,4,177,6,9,16,10
	.byte	'GtmTomUpdateEn',0,2
	.word	264
	.byte	2,35,0,10
	.byte	'GtmTomEndisCtrl',0,2
	.word	264
	.byte	2,35,2,10
	.byte	'GtmTomEndisStat',0,2
	.word	264
	.byte	2,35,4,10
	.byte	'GtmTomOutenCtrl',0,2
	.word	264
	.byte	2,35,6,10
	.byte	'GtmTomOutenStat',0,2
	.word	264
	.byte	2,35,8,10
	.byte	'GtmTomFupd',0,4
	.word	301
	.byte	2,35,10,0,4
	.byte	'Gtm_TomTgcConfigGroupType',0,4,185,6,2
	.word	36123
	.byte	14,4,189,6,9,12,10
	.byte	'GtmTomIntTrig',0,2
	.word	264
	.byte	2,35,0,10
	.byte	'GtmTomActTb',0,4
	.word	301
	.byte	2,35,2,17
	.word	36123
	.byte	3
	.word	36359
	.byte	10
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	36364
	.byte	2,35,8,0,4
	.byte	'Gtm_TomTgcConfigType',0,4,196,6,2
	.word	36309
	.byte	14,4,199,6,9,12,10
	.byte	'GtmTomIrqEn',0,1
	.word	233
	.byte	2,35,0,10
	.byte	'GtmTomCn0Value',0,2
	.word	264
	.byte	2,35,2,10
	.byte	'GtmTomCm0Value',0,2
	.word	264
	.byte	2,35,4,10
	.byte	'GtmTomCm1Value',0,2
	.word	264
	.byte	2,35,6,10
	.byte	'GtmTomSr0Value',0,2
	.word	264
	.byte	2,35,8,10
	.byte	'GtmTomSr1Value',0,2
	.word	264
	.byte	2,35,10,0,4
	.byte	'Gtm_TomChannelConfigType',0,4,207,6,2
	.word	36431
	.byte	14,4,211,6,9,12,10
	.byte	'TomUsage',0,1
	.word	233
	.byte	2,35,0,10
	.byte	'GtmTomIrqMode',0,1
	.word	233
	.byte	2,35,1,10
	.byte	'GtmTomControlWord',0,4
	.word	301
	.byte	2,35,2,17
	.word	36431
	.byte	3
	.word	36687
	.byte	10
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	36692
	.byte	2,35,8,0,4
	.byte	'Gtm_TomConfigType',0,4,219,6,2
	.word	36613
	.byte	14,4,223,6,9,8,10
	.byte	'CmuEclkNum',0,4
	.word	301
	.byte	2,35,0,10
	.byte	'CmuEclkDen',0,4
	.word	301
	.byte	2,35,4,0,4
	.byte	'Gtm_ExtClkType',0,4,227,6,2
	.word	36754
	.byte	14,4,230,6,9,64,10
	.byte	'GtmClockEnable',0,4
	.word	301
	.byte	2,35,0,10
	.byte	'GtmCmuClkCnt',0,32
	.word	35637
	.byte	2,35,4,10
	.byte	'GtmFxdClkControl',0,4
	.word	301
	.byte	2,35,36,12,24
	.word	36754
	.byte	13,2,0,10
	.byte	'GtmEclk',0,24
	.word	36903
	.byte	2,35,40,0,4
	.byte	'Gtm_ClockSettingType',0,4,236,6,2
	.word	36825
	.byte	14,4,240,6,9,4,10
	.byte	'GtmCtrlValue',0,2
	.word	264
	.byte	2,35,0,10
	.byte	'GtmIrqEnable',0,2
	.word	264
	.byte	2,35,2,0,4
	.byte	'Gtm_GeneralConfigType',0,4,245,6,2
	.word	36960
	.byte	14,4,249,6,9,6,10
	.byte	'TbuChannelCtrl',0,1
	.word	233
	.byte	2,35,0,10
	.byte	'TbuBaseValue',0,4
	.word	301
	.byte	2,35,2,0,4
	.byte	'Gtm_TbuConfigType',0,4,253,6,2
	.word	37042
	.byte	14,4,129,7,9,72,10
	.byte	'GtmModuleSleepEnable',0,1
	.word	233
	.byte	2,35,0,10
	.byte	'GtmGclkNum',0,4
	.word	301
	.byte	2,35,2,10
	.byte	'GtmGclkDen',0,4
	.word	301
	.byte	2,35,6,10
	.byte	'GtmAccessEnable0',0,4
	.word	301
	.byte	2,35,10,10
	.byte	'GtmAccessEnable1',0,4
	.word	301
	.byte	2,35,14,12,2
	.word	264
	.byte	13,0,0,10
	.byte	'GtmTimModuleUsage',0,2
	.word	37250
	.byte	2,35,18,12,1
	.word	233
	.byte	13,0,0,10
	.byte	'GtmTimUsage',0,1
	.word	37286
	.byte	2,35,20,17
	.word	35782
	.byte	3
	.word	37316
	.byte	10
	.byte	'GtmTimConfigPtr',0,4
	.word	37321
	.byte	2,35,24,10
	.byte	'GtmTomTgcUsage',0,1
	.word	37286
	.byte	2,35,28,17
	.word	36309
	.byte	3
	.word	37375
	.byte	10
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	37380
	.byte	2,35,32,12,8
	.word	301
	.byte	13,1,0,10
	.byte	'GtmTomModuleUsage',0,8
	.word	37413
	.byte	2,35,36,10
	.byte	'GtmTomUsage',0,4
	.word	35610
	.byte	2,35,44,17
	.word	36613
	.byte	3
	.word	37470
	.byte	10
	.byte	'GtmTomConfigPtr',0,4
	.word	37475
	.byte	2,35,48,17
	.word	36013
	.byte	3
	.word	37505
	.byte	10
	.byte	'GtmModUsageConfigPtr',0,4
	.word	37510
	.byte	2,35,52,17
	.word	36960
	.byte	3
	.word	37545
	.byte	10
	.byte	'GtmGeneralConfigPtr',0,4
	.word	37550
	.byte	2,35,56,17
	.word	37042
	.byte	3
	.word	37584
	.byte	10
	.byte	'GtmTbuConfigPtr',0,4
	.word	37589
	.byte	2,35,60,17
	.word	233
	.byte	3
	.word	37619
	.byte	10
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	37624
	.byte	2,35,64,10
	.byte	'GtmTtcanTriggers',0,2
	.word	37250
	.byte	2,35,68,0,4
	.byte	'Gtm_ModuleConfigType',0,4,163,7,2
	.word	37122
	.byte	18,1,1,19
	.word	233
	.byte	19
	.word	233
	.byte	19
	.word	233
	.byte	19
	.word	264
	.byte	0,3
	.word	37716
	.byte	4
	.byte	'Gtm_NotificationPtrType',0,4,172,7,16
	.word	37740
	.byte	7
	.byte	'Gtm_ConfigType',0,4,192,7,16,12,17
	.word	36825
	.byte	3
	.word	37799
	.byte	10
	.byte	'GtmClockSettingPtr',0,4
	.word	37804
	.byte	2,35,0,17
	.word	35604
	.byte	3
	.word	37837
	.byte	10
	.byte	'GtmPortConfigPtr',0,4
	.word	37842
	.byte	2,35,4,17
	.word	37122
	.byte	3
	.word	37873
	.byte	10
	.byte	'GtmModuleConfigPtr',0,4
	.word	37878
	.byte	2,35,8,0,4
	.byte	'Gtm_ConfigType',0,4,197,7,2
	.word	37778
	.byte	12,12
	.word	37778
	.byte	13,0,0
.L32:
	.byte	17
	.word	37936
.L33:
	.byte	17
	.word	36825
.L34:
	.byte	17
	.word	36960
	.byte	12,16
	.word	36123
	.byte	13,0,0
.L35:
	.byte	17
	.word	37960
	.byte	12,72
	.word	36431
	.byte	13,5,0
.L36:
	.byte	17
	.word	37974
	.byte	12,12
	.word	36309
	.byte	13,0,0
.L37:
	.byte	17
	.word	37988
	.byte	12,72
	.word	36613
	.byte	13,5,0
.L38:
	.byte	17
	.word	38002
	.byte	12,32
	.word	35692
	.byte	13,3,0
.L39:
	.byte	17
	.word	38016
	.byte	12,96
	.word	35782
	.byte	13,3,0
.L40:
	.byte	17
	.word	38030
.L41:
	.byte	17
	.word	36013
	.byte	12,4
	.word	233
	.byte	13,3,0
.L42:
	.byte	17
	.word	38049
.L43:
	.byte	17
	.word	35604
.L44:
	.byte	17
	.word	37122
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,23,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0
	.byte	0,11,53,0,73,19,0,0,12,1,1,11,15,73,19,0,0,13,33,0,47,15,0,0,14,19,1,58,15,59,15,57,15,11,15,0,0,15,4
	.byte	1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0,17,38,0,73,19,0,0,18,21,1,54,15,39,12,0,0,19,5,0,73
	.byte	19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L46-.L45
.L45:
	.half	3
	.word	.L48-.L47
.L47:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	0
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'IfxGtm_regdef.h',0,1,0,0
	.byte	'Gtm.h',0,1,0,0,0
.L48:
.L46:
	.sdecl	'.debug_info',debug,cluster('Gtm_ConfigRoot')
	.sect	'.debug_info'
.L6:
	.word	207
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_ConfigRoot',0,1,244,3,22
	.word	.L32
	.byte	1,5,3
	.word	Gtm_ConfigRoot
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_ConfigRoot')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kClockSetting0')
	.sect	'.debug_info'
.L8:
	.word	210
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kClockSetting0',0,1,150,1,35
	.word	.L33
	.byte	5,3
	.word	Gtm_kClockSetting0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kClockSetting0')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kGeneralConfig0')
	.sect	'.debug_info'
.L10:
	.word	211
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kGeneralConfig0',0,1,173,1,36
	.word	.L34
	.byte	5,3
	.word	Gtm_kGeneralConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kGeneralConfig0')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kTomTgcConfigGroup0')
	.sect	'.debug_info'
.L12:
	.word	215
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kTomTgcConfigGroup0',0,1,180,1,40
	.word	.L35
	.byte	5,3
	.word	Gtm_kTomTgcConfigGroup0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kTomTgcConfigGroup0')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kTomChannelConfig0')
	.sect	'.debug_info'
.L14:
	.word	214
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kTomChannelConfig0',0,1,194,1,39
	.word	.L36
	.byte	5,3
	.word	Gtm_kTomChannelConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kTomChannelConfig0')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kTomTgcConfig0')
	.sect	'.debug_info'
.L16:
	.word	210
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kTomTgcConfig0',0,1,252,1,35
	.word	.L37
	.byte	5,3
	.word	Gtm_kTomTgcConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kTomTgcConfig0')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kTomConfig0')
	.sect	'.debug_info'
.L18:
	.word	207
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kTomConfig0',0,1,134,2,32
	.word	.L38
	.byte	5,3
	.word	Gtm_kTomConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kTomConfig0')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kTimFlt0')
	.sect	'.debug_info'
.L20:
	.word	205
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kTimFlt0',0,1,187,2,22
	.word	.L39
	.byte	1,5,3
	.word	Gtm_kTimFlt0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kTimFlt0')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kTimConfig0')
	.sect	'.debug_info'
.L22:
	.word	207
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kTimConfig0',0,1,208,2,32
	.word	.L40
	.byte	5,3
	.word	Gtm_kTimConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kTimConfig0')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kModUsage0')
	.sect	'.debug_info'
.L24:
	.word	206
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kModUsage0',0,1,129,3,37
	.word	.L41
	.byte	5,3
	.word	Gtm_kModUsage0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kModUsage0')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kAdcConnections0')
	.sect	'.debug_info'
.L26:
	.word	212
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kAdcConnections0',0,1,187,3,20
	.word	.L42
	.byte	5,3
	.word	Gtm_kAdcConnections0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kAdcConnections0')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kPortConfig0')
	.sect	'.debug_info'
.L28:
	.word	208
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kPortConfig0',0,1,193,3,33
	.word	.L43
	.byte	5,3
	.word	Gtm_kPortConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kPortConfig0')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kModuleConfig0')
	.sect	'.debug_info'
.L30:
	.word	210
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'..\\mcal_cfg\\Gtm_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Gtm_kModuleConfig0',0,1,210,3,35
	.word	.L44
	.byte	5,3
	.word	Gtm_kModuleConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kModuleConfig0')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0

; ..\mcal_cfg\Gtm_PBCfg.c	     1  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	     2  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2014)                                 **
; ..\mcal_cfg\Gtm_PBCfg.c	     4  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	     5  ** All rights reserved.                                                       **
; ..\mcal_cfg\Gtm_PBCfg.c	     6  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_cfg\Gtm_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_cfg\Gtm_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_cfg\Gtm_PBCfg.c	    10  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    11  ********************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	    12  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    13  **  FILENAME  : Gtm_PBCfg.c                                                   **
; ..\mcal_cfg\Gtm_PBCfg.c	    14  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    15  **  $CC VERSION : \main\dev_tc23x\10 $                                       **
; ..\mcal_cfg\Gtm_PBCfg.c	    16  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    17  **  DATE, TIME: 2020-07-10, 14:56:10                                          **
; ..\mcal_cfg\Gtm_PBCfg.c	    18  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    19  **  GENERATOR : Build b141014-0350                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    20  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    21  **  BSW MODULE DECRIPTION : Mcu.bmd                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    22  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    23  **  VARIANT   : VariantPB                                                     **
; ..\mcal_cfg\Gtm_PBCfg.c	    24  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    25  **  PLATFORM  : Infineon Aurix                                                **
; ..\mcal_cfg\Gtm_PBCfg.c	    26  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    27  **  COMPILER  : Tasking/GNU/Diab                                              **
; ..\mcal_cfg\Gtm_PBCfg.c	    28  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    29  **  AUTHOR    : DL-AUTOSAR-Engineering                                        **
; ..\mcal_cfg\Gtm_PBCfg.c	    30  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    31  **  VENDOR    : Infineon Technologies                                         **
; ..\mcal_cfg\Gtm_PBCfg.c	    32  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    33  **  DESCRIPTION  : GTM configuration generated out of ECU configuration      **
; ..\mcal_cfg\Gtm_PBCfg.c	    34  **                 file                                                       **
; ..\mcal_cfg\Gtm_PBCfg.c	    35  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    36  **  SPECIFICATION(S) : complex driver implementation                          **
; ..\mcal_cfg\Gtm_PBCfg.c	    37  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    38  **  MAY BE CHANGED BY USER [yes/no]: no                                       **
; ..\mcal_cfg\Gtm_PBCfg.c	    39  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    40  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	    41  /******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	    42  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    43  ** Copyright (C) Infineon Technologies (2018)                                **
; ..\mcal_cfg\Gtm_PBCfg.c	    44  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    45  ** All rights reserved.                                                      **
; ..\mcal_cfg\Gtm_PBCfg.c	    46  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    47  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_cfg\Gtm_PBCfg.c	    48  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_cfg\Gtm_PBCfg.c	    49  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_cfg\Gtm_PBCfg.c	    50  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    51  *******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	    52  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    53  **  FILENAME  : Gtm.m                                                        **
; ..\mcal_cfg\Gtm_PBCfg.c	    54  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    55  **  $CC VERSION : \main\dev_tc23x\10 $                                       **
; ..\mcal_cfg\Gtm_PBCfg.c	    56  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    57  **  DATE, TIME: 2020-07-10, 14:56:10                                         **
; ..\mcal_cfg\Gtm_PBCfg.c	    58  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    59  **  GENERATOR : Build b141014-0350                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    60  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    61  **  AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\mcal_cfg\Gtm_PBCfg.c	    62  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    63  **  VENDOR    : Infineon Technologies                                        **
; ..\mcal_cfg\Gtm_PBCfg.c	    64  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    65  **  DESCRIPTION  : GTM configuration generated out of ECU configuration      **
; ..\mcal_cfg\Gtm_PBCfg.c	    66  **                 file (Mcu.bmd/.xdm) for Gtm.m file for TC23x              **
; ..\mcal_cfg\Gtm_PBCfg.c	    67  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    68  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_cfg\Gtm_PBCfg.c	    69  **                                                                           **
; ..\mcal_cfg\Gtm_PBCfg.c	    70  ******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	    71  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	    72  **                                                                            **
; ..\mcal_cfg\Gtm_PBCfg.c	    73  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	    74  
; ..\mcal_cfg\Gtm_PBCfg.c	    75  
; ..\mcal_cfg\Gtm_PBCfg.c	    76  
; ..\mcal_cfg\Gtm_PBCfg.c	    77  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	    78  **                      Includes                                              **
; ..\mcal_cfg\Gtm_PBCfg.c	    79  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	    80  
; ..\mcal_cfg\Gtm_PBCfg.c	    81  /* Own header file, this includes own configuration file also */
; ..\mcal_cfg\Gtm_PBCfg.c	    82  #include "Gtm.h"
; ..\mcal_cfg\Gtm_PBCfg.c	    83  
; ..\mcal_cfg\Gtm_PBCfg.c	    84  
; ..\mcal_cfg\Gtm_PBCfg.c	    85  
; ..\mcal_cfg\Gtm_PBCfg.c	    86  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	    87  **                      Global Macro Definitions                              **
; ..\mcal_cfg\Gtm_PBCfg.c	    88  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	    89  /* Note:
; ..\mcal_cfg\Gtm_PBCfg.c	    90  The user can configure the parameters with the tag Configuration:
; ..\mcal_cfg\Gtm_PBCfg.c	    91  The user should not change anything under the tag Configuration Options:
; ..\mcal_cfg\Gtm_PBCfg.c	    92  */
; ..\mcal_cfg\Gtm_PBCfg.c	    93  
; ..\mcal_cfg\Gtm_PBCfg.c	    94  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	    95  **                      Private Macro Definitions                             **
; ..\mcal_cfg\Gtm_PBCfg.c	    96  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	    97  #define GTM_INTERRUPT_LEVEL_MODE          (0U)
; ..\mcal_cfg\Gtm_PBCfg.c	    98  #define GTM_INTERRUPT_PULSE_MODE          (1U)
; ..\mcal_cfg\Gtm_PBCfg.c	    99  #define GTM_INTERRUPT_PULSE_NOTIFY_MODE   (2U) 
; ..\mcal_cfg\Gtm_PBCfg.c	   100  #define GTM_INTERRUPT_SINGLE_PULSE_MODE   (3U)
; ..\mcal_cfg\Gtm_PBCfg.c	   101  
; ..\mcal_cfg\Gtm_PBCfg.c	   102  
; ..\mcal_cfg\Gtm_PBCfg.c	   103  #define TSPP1_SUBUNIT_OUTPUT 5
; ..\mcal_cfg\Gtm_PBCfg.c	   104  
; ..\mcal_cfg\Gtm_PBCfg.c	   105  /* For Tbu */
; ..\mcal_cfg\Gtm_PBCfg.c	   106  #define BITS_0_TO_23   (0U)
; ..\mcal_cfg\Gtm_PBCfg.c	   107  #define BITS_3_TO_26   (1U)
; ..\mcal_cfg\Gtm_PBCfg.c	   108  #define FREE_RUNNING_COUNTER_MODE      (0)
; ..\mcal_cfg\Gtm_PBCfg.c	   109  #define FORWARD_BACKWARD_COUNTER_MODE  (1)
; ..\mcal_cfg\Gtm_PBCfg.c	   110  
; ..\mcal_cfg\Gtm_PBCfg.c	   111  #define Gtm_lTbuBuildControl(Bit0, Bit123, Bit5)                              \ 
; ..\mcal_cfg\Gtm_PBCfg.c	   112   (0x00U | (uint8)(Bit0) | (uint8)((uint8)(Bit123) << 1) |                     \ 
; ..\mcal_cfg\Gtm_PBCfg.c	   113                                                   (uint8)((uint8)(Bit5) << 5))
; ..\mcal_cfg\Gtm_PBCfg.c	   114  #define Gtm_lTimerCtrlValue(Word, ClockValue)                                 \ 
; ..\mcal_cfg\Gtm_PBCfg.c	   115                          ((uint32)(Word) | (uint32)((uint32)(ClockValue) << 12))
; ..\mcal_cfg\Gtm_PBCfg.c	   116  #define Gtm_TimbuildReg(Word, ClockValue)                                     \ 
; ..\mcal_cfg\Gtm_PBCfg.c	   117                           ((uint32)(Word)| (uint32)((uint32)(ClockValue) << 24))
; ..\mcal_cfg\Gtm_PBCfg.c	   118  #define Gtm_TimTduBuildReg(Word, ClockValue)                                  \ 
; ..\mcal_cfg\Gtm_PBCfg.c	   119                          ((uint32)(Word) | (uint32)((uint32)(ClockValue) << 28))
; ..\mcal_cfg\Gtm_PBCfg.c	   120  /* For Interrupt Mode Appending */
; ..\mcal_cfg\Gtm_PBCfg.c	   121  #define Gtm_lIncludeIntMode8Bit(Bytevalue, IrqMode)                           \ 
; ..\mcal_cfg\Gtm_PBCfg.c	   122                            ((uint8)(Bytevalue) | (uint8)((uint8)(IrqMode) << 6))
; ..\mcal_cfg\Gtm_PBCfg.c	   123  #define Gtm_lIncludeIntMode16Bit(Intvalue, IrqMode)                           \ 
; ..\mcal_cfg\Gtm_PBCfg.c	   124                         ((uint16)(Intvalue) | (uint16)((uint16)(IrqMode) << 14))
; ..\mcal_cfg\Gtm_PBCfg.c	   125  #define Gtm_lIncludeIntMode32Bit(Wordvalue, IrqMode)                          \ 
; ..\mcal_cfg\Gtm_PBCfg.c	   126                        ((uint32)(Wordvalue) | (uint32)((uint32)(IrqMode) << 30))
; ..\mcal_cfg\Gtm_PBCfg.c	   127  
; ..\mcal_cfg\Gtm_PBCfg.c	   128  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   129  **                      Private Type Definitions                              **
; ..\mcal_cfg\Gtm_PBCfg.c	   130  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   131  
; ..\mcal_cfg\Gtm_PBCfg.c	   132  
; ..\mcal_cfg\Gtm_PBCfg.c	   133  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   134  **                      Private Function Declarations                         **
; ..\mcal_cfg\Gtm_PBCfg.c	   135  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   136  
; ..\mcal_cfg\Gtm_PBCfg.c	   137  
; ..\mcal_cfg\Gtm_PBCfg.c	   138  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   139  **                      Global Funtion Declarations                           **
; ..\mcal_cfg\Gtm_PBCfg.c	   140  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   141  
; ..\mcal_cfg\Gtm_PBCfg.c	   142  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   143  **                      Global Constant Definitions                           **
; ..\mcal_cfg\Gtm_PBCfg.c	   144  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   145  #define MCU_START_SEC_POSTBUILDCFG
; ..\mcal_cfg\Gtm_PBCfg.c	   146  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_cfg\Gtm_PBCfg.c	   147   allowed only for MemMap.h*/
; ..\mcal_cfg\Gtm_PBCfg.c	   148  #include "MemMap.h"
; ..\mcal_cfg\Gtm_PBCfg.c	   149  
; ..\mcal_cfg\Gtm_PBCfg.c	   150  static const Gtm_ClockSettingType Gtm_kClockSetting0 = 
; ..\mcal_cfg\Gtm_PBCfg.c	   151  {
; ..\mcal_cfg\Gtm_PBCfg.c	   152    0x0095555aU,
; ..\mcal_cfg\Gtm_PBCfg.c	   153    {
; ..\mcal_cfg\Gtm_PBCfg.c	   154      0x00000063U,
; ..\mcal_cfg\Gtm_PBCfg.c	   155      0x00000009U,
; ..\mcal_cfg\Gtm_PBCfg.c	   156      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   157      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   158      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   159      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   160      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   161      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   162    },
; ..\mcal_cfg\Gtm_PBCfg.c	   163  
; ..\mcal_cfg\Gtm_PBCfg.c	   164      0x1U,
; ..\mcal_cfg\Gtm_PBCfg.c	   165  
; ..\mcal_cfg\Gtm_PBCfg.c	   166    {
; ..\mcal_cfg\Gtm_PBCfg.c	   167      {  0x00000001U,  0x00000001U  },
; ..\mcal_cfg\Gtm_PBCfg.c	   168      {  0x00000001U,  0x00000001U  },
; ..\mcal_cfg\Gtm_PBCfg.c	   169      {  0x00000001U,  0x00000001U  }
; ..\mcal_cfg\Gtm_PBCfg.c	   170    }
; ..\mcal_cfg\Gtm_PBCfg.c	   171  };
; ..\mcal_cfg\Gtm_PBCfg.c	   172  
; ..\mcal_cfg\Gtm_PBCfg.c	   173  static const Gtm_GeneralConfigType Gtm_kGeneralConfig0 =
; ..\mcal_cfg\Gtm_PBCfg.c	   174  {
; ..\mcal_cfg\Gtm_PBCfg.c	   175    0x0001U,
; ..\mcal_cfg\Gtm_PBCfg.c	   176    Gtm_lIncludeIntMode8Bit(1U,GTM_INTERRUPT_LEVEL_MODE)
; ..\mcal_cfg\Gtm_PBCfg.c	   177    };
; ..\mcal_cfg\Gtm_PBCfg.c	   178  
; ..\mcal_cfg\Gtm_PBCfg.c	   179  
; ..\mcal_cfg\Gtm_PBCfg.c	   180  static const Gtm_TomTgcConfigGroupType Gtm_kTomTgcConfigGroup0[] =
; ..\mcal_cfg\Gtm_PBCfg.c	   181  {
; ..\mcal_cfg\Gtm_PBCfg.c	   182    {
; ..\mcal_cfg\Gtm_PBCfg.c	   183  
; ..\mcal_cfg\Gtm_PBCfg.c	   184        0x0aaaU,
; ..\mcal_cfg\Gtm_PBCfg.c	   185        0x0000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   186        0x0aaaU,
; ..\mcal_cfg\Gtm_PBCfg.c	   187        0x0000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   188        0x0000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   189        0x00000000U,      
; ..\mcal_cfg\Gtm_PBCfg.c	   190    },
; ..\mcal_cfg\Gtm_PBCfg.c	   191  
; ..\mcal_cfg\Gtm_PBCfg.c	   192  };
; ..\mcal_cfg\Gtm_PBCfg.c	   193  
; ..\mcal_cfg\Gtm_PBCfg.c	   194  static const Gtm_TomChannelConfigType Gtm_kTomChannelConfig0[]=
; ..\mcal_cfg\Gtm_PBCfg.c	   195  {
; ..\mcal_cfg\Gtm_PBCfg.c	   196    {
; ..\mcal_cfg\Gtm_PBCfg.c	   197      0x1U,
; ..\mcal_cfg\Gtm_PBCfg.c	   198      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   199      0x3e8U,
; ..\mcal_cfg\Gtm_PBCfg.c	   200      0x1f4U,
; ..\mcal_cfg\Gtm_PBCfg.c	   201      0x3e8U,
; ..\mcal_cfg\Gtm_PBCfg.c	   202      0x1f4U,
; ..\mcal_cfg\Gtm_PBCfg.c	   203    },     
; ..\mcal_cfg\Gtm_PBCfg.c	   204  
; ..\mcal_cfg\Gtm_PBCfg.c	   205    {
; ..\mcal_cfg\Gtm_PBCfg.c	   206      0x1U,
; ..\mcal_cfg\Gtm_PBCfg.c	   207      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   208      0x7d0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   209      0x3e8U,
; ..\mcal_cfg\Gtm_PBCfg.c	   210      0x7d0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   211      0x3e8U,
; ..\mcal_cfg\Gtm_PBCfg.c	   212    },     
; ..\mcal_cfg\Gtm_PBCfg.c	   213  
; ..\mcal_cfg\Gtm_PBCfg.c	   214    {
; ..\mcal_cfg\Gtm_PBCfg.c	   215      0x1U,
; ..\mcal_cfg\Gtm_PBCfg.c	   216      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   217      0x1388U,
; ..\mcal_cfg\Gtm_PBCfg.c	   218      0x9c4U,
; ..\mcal_cfg\Gtm_PBCfg.c	   219      0x1388U,
; ..\mcal_cfg\Gtm_PBCfg.c	   220      0x9c4U,
; ..\mcal_cfg\Gtm_PBCfg.c	   221    },     
; ..\mcal_cfg\Gtm_PBCfg.c	   222  
; ..\mcal_cfg\Gtm_PBCfg.c	   223    {
; ..\mcal_cfg\Gtm_PBCfg.c	   224      0x1U,
; ..\mcal_cfg\Gtm_PBCfg.c	   225      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   226      0x2710U,
; ..\mcal_cfg\Gtm_PBCfg.c	   227      0x1388U,
; ..\mcal_cfg\Gtm_PBCfg.c	   228      0x2710U,
; ..\mcal_cfg\Gtm_PBCfg.c	   229      0x1388U,
; ..\mcal_cfg\Gtm_PBCfg.c	   230    },     
; ..\mcal_cfg\Gtm_PBCfg.c	   231  
; ..\mcal_cfg\Gtm_PBCfg.c	   232    {
; ..\mcal_cfg\Gtm_PBCfg.c	   233      0x1U,
; ..\mcal_cfg\Gtm_PBCfg.c	   234      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   235      0x4e20U,
; ..\mcal_cfg\Gtm_PBCfg.c	   236      0x2710U,
; ..\mcal_cfg\Gtm_PBCfg.c	   237      0x4e20U,
; ..\mcal_cfg\Gtm_PBCfg.c	   238      0x2710U,
; ..\mcal_cfg\Gtm_PBCfg.c	   239    },     
; ..\mcal_cfg\Gtm_PBCfg.c	   240  
; ..\mcal_cfg\Gtm_PBCfg.c	   241    {
; ..\mcal_cfg\Gtm_PBCfg.c	   242      0x1U,
; ..\mcal_cfg\Gtm_PBCfg.c	   243      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   244      0xc350U,
; ..\mcal_cfg\Gtm_PBCfg.c	   245      0x61a8U,
; ..\mcal_cfg\Gtm_PBCfg.c	   246      0xc350U,
; ..\mcal_cfg\Gtm_PBCfg.c	   247      0x61a8U,
; ..\mcal_cfg\Gtm_PBCfg.c	   248    },     
; ..\mcal_cfg\Gtm_PBCfg.c	   249  
; ..\mcal_cfg\Gtm_PBCfg.c	   250  
; ..\mcal_cfg\Gtm_PBCfg.c	   251  };
; ..\mcal_cfg\Gtm_PBCfg.c	   252  static const Gtm_TomTgcConfigType Gtm_kTomTgcConfig0[] =
; ..\mcal_cfg\Gtm_PBCfg.c	   253  {
; ..\mcal_cfg\Gtm_PBCfg.c	   254    {
; ..\mcal_cfg\Gtm_PBCfg.c	   255      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   256      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   257      &Gtm_kTomTgcConfigGroup0[0]
; ..\mcal_cfg\Gtm_PBCfg.c	   258    },
; ..\mcal_cfg\Gtm_PBCfg.c	   259  
; ..\mcal_cfg\Gtm_PBCfg.c	   260  };
; ..\mcal_cfg\Gtm_PBCfg.c	   261  
; ..\mcal_cfg\Gtm_PBCfg.c	   262  static const Gtm_TomConfigType Gtm_kTomConfig0[] = 
; ..\mcal_cfg\Gtm_PBCfg.c	   263  {
; ..\mcal_cfg\Gtm_PBCfg.c	   264    {
; ..\mcal_cfg\Gtm_PBCfg.c	   265      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   266      GTM_INTERRUPT_PULSE_NOTIFY_MODE,
; ..\mcal_cfg\Gtm_PBCfg.c	   267      Gtm_lTimerCtrlValue(0x0U,GTM_FIXED_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   268      &Gtm_kTomChannelConfig0[0]
; ..\mcal_cfg\Gtm_PBCfg.c	   269    },
; ..\mcal_cfg\Gtm_PBCfg.c	   270  
; ..\mcal_cfg\Gtm_PBCfg.c	   271  
; ..\mcal_cfg\Gtm_PBCfg.c	   272    {
; ..\mcal_cfg\Gtm_PBCfg.c	   273      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   274      GTM_INTERRUPT_PULSE_NOTIFY_MODE,
; ..\mcal_cfg\Gtm_PBCfg.c	   275      Gtm_lTimerCtrlValue(0x0U,GTM_FIXED_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   276      &Gtm_kTomChannelConfig0[1]
; ..\mcal_cfg\Gtm_PBCfg.c	   277    },
; ..\mcal_cfg\Gtm_PBCfg.c	   278  
; ..\mcal_cfg\Gtm_PBCfg.c	   279  
; ..\mcal_cfg\Gtm_PBCfg.c	   280    {
; ..\mcal_cfg\Gtm_PBCfg.c	   281      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   282      GTM_INTERRUPT_PULSE_NOTIFY_MODE,
; ..\mcal_cfg\Gtm_PBCfg.c	   283      Gtm_lTimerCtrlValue(0x0U,GTM_FIXED_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   284      &Gtm_kTomChannelConfig0[2]
; ..\mcal_cfg\Gtm_PBCfg.c	   285    },
; ..\mcal_cfg\Gtm_PBCfg.c	   286  
; ..\mcal_cfg\Gtm_PBCfg.c	   287  
; ..\mcal_cfg\Gtm_PBCfg.c	   288    {
; ..\mcal_cfg\Gtm_PBCfg.c	   289      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   290      GTM_INTERRUPT_PULSE_NOTIFY_MODE,
; ..\mcal_cfg\Gtm_PBCfg.c	   291      Gtm_lTimerCtrlValue(0x0U,GTM_FIXED_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   292      &Gtm_kTomChannelConfig0[3]
; ..\mcal_cfg\Gtm_PBCfg.c	   293    },
; ..\mcal_cfg\Gtm_PBCfg.c	   294  
; ..\mcal_cfg\Gtm_PBCfg.c	   295  
; ..\mcal_cfg\Gtm_PBCfg.c	   296    {
; ..\mcal_cfg\Gtm_PBCfg.c	   297      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   298      GTM_INTERRUPT_PULSE_NOTIFY_MODE,
; ..\mcal_cfg\Gtm_PBCfg.c	   299      Gtm_lTimerCtrlValue(0x0U,GTM_FIXED_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   300      &Gtm_kTomChannelConfig0[4]
; ..\mcal_cfg\Gtm_PBCfg.c	   301    },
; ..\mcal_cfg\Gtm_PBCfg.c	   302  
; ..\mcal_cfg\Gtm_PBCfg.c	   303  
; ..\mcal_cfg\Gtm_PBCfg.c	   304    {
; ..\mcal_cfg\Gtm_PBCfg.c	   305      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   306      GTM_INTERRUPT_PULSE_NOTIFY_MODE,
; ..\mcal_cfg\Gtm_PBCfg.c	   307      Gtm_lTimerCtrlValue(0x0U,GTM_FIXED_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   308      &Gtm_kTomChannelConfig0[5]
; ..\mcal_cfg\Gtm_PBCfg.c	   309    },
; ..\mcal_cfg\Gtm_PBCfg.c	   310  
; ..\mcal_cfg\Gtm_PBCfg.c	   311  
; ..\mcal_cfg\Gtm_PBCfg.c	   312  
; ..\mcal_cfg\Gtm_PBCfg.c	   313  };
; ..\mcal_cfg\Gtm_PBCfg.c	   314  
; ..\mcal_cfg\Gtm_PBCfg.c	   315  const Gtm_TimFltType Gtm_kTimFlt0[] = 
; ..\mcal_cfg\Gtm_PBCfg.c	   316  {
; ..\mcal_cfg\Gtm_PBCfg.c	   317    {
; ..\mcal_cfg\Gtm_PBCfg.c	   318      0x00000064U,
; ..\mcal_cfg\Gtm_PBCfg.c	   319      0x00000064U
; ..\mcal_cfg\Gtm_PBCfg.c	   320    },
; ..\mcal_cfg\Gtm_PBCfg.c	   321    {
; ..\mcal_cfg\Gtm_PBCfg.c	   322      0x00000064U,
; ..\mcal_cfg\Gtm_PBCfg.c	   323      0x00000064U
; ..\mcal_cfg\Gtm_PBCfg.c	   324    },
; ..\mcal_cfg\Gtm_PBCfg.c	   325    {
; ..\mcal_cfg\Gtm_PBCfg.c	   326      0x00000064U,
; ..\mcal_cfg\Gtm_PBCfg.c	   327      0x00000064U
; ..\mcal_cfg\Gtm_PBCfg.c	   328    },
; ..\mcal_cfg\Gtm_PBCfg.c	   329    {
; ..\mcal_cfg\Gtm_PBCfg.c	   330      0x00000064U,
; ..\mcal_cfg\Gtm_PBCfg.c	   331      0x00000064U
; ..\mcal_cfg\Gtm_PBCfg.c	   332    },
; ..\mcal_cfg\Gtm_PBCfg.c	   333  };
; ..\mcal_cfg\Gtm_PBCfg.c	   334  
; ..\mcal_cfg\Gtm_PBCfg.c	   335  
; ..\mcal_cfg\Gtm_PBCfg.c	   336  static const Gtm_TimConfigType Gtm_kTimConfig0[] =
; ..\mcal_cfg\Gtm_PBCfg.c	   337  {
; ..\mcal_cfg\Gtm_PBCfg.c	   338    {
; ..\mcal_cfg\Gtm_PBCfg.c	   339      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   340      Gtm_lIncludeIntMode8Bit(0x1U,GTM_INTERRUPT_PULSE_NOTIFY_MODE),
; ..\mcal_cfg\Gtm_PBCfg.c	   341      0x00U,
; ..\mcal_cfg\Gtm_PBCfg.c	   342      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   343      Gtm_TimbuildReg(0x10005U, GTM_CONFIGURABLE_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   344      &Gtm_kTimFlt0[0],
; ..\mcal_cfg\Gtm_PBCfg.c	   345      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   346      Gtm_TimTduBuildReg(0x0U,GTM_CONFIGURABLE_CLOCK_0),  
; ..\mcal_cfg\Gtm_PBCfg.c	   347      0x00000005U
; ..\mcal_cfg\Gtm_PBCfg.c	   348    },
; ..\mcal_cfg\Gtm_PBCfg.c	   349    {
; ..\mcal_cfg\Gtm_PBCfg.c	   350      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   351      Gtm_lIncludeIntMode8Bit(0x1U,GTM_INTERRUPT_PULSE_NOTIFY_MODE),
; ..\mcal_cfg\Gtm_PBCfg.c	   352      0x00U,
; ..\mcal_cfg\Gtm_PBCfg.c	   353      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   354      Gtm_TimbuildReg(0x10005U, GTM_CONFIGURABLE_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   355      &Gtm_kTimFlt0[1],
; ..\mcal_cfg\Gtm_PBCfg.c	   356      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   357      Gtm_TimTduBuildReg(0x0U,GTM_CONFIGURABLE_CLOCK_0),  
; ..\mcal_cfg\Gtm_PBCfg.c	   358      0x00000050U
; ..\mcal_cfg\Gtm_PBCfg.c	   359    },
; ..\mcal_cfg\Gtm_PBCfg.c	   360    {
; ..\mcal_cfg\Gtm_PBCfg.c	   361      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   362      Gtm_lIncludeIntMode8Bit(0x1U,GTM_INTERRUPT_PULSE_NOTIFY_MODE),
; ..\mcal_cfg\Gtm_PBCfg.c	   363      0x00U,
; ..\mcal_cfg\Gtm_PBCfg.c	   364      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   365      Gtm_TimbuildReg(0x10005U, GTM_CONFIGURABLE_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   366      &Gtm_kTimFlt0[2],
; ..\mcal_cfg\Gtm_PBCfg.c	   367      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   368      Gtm_TimTduBuildReg(0x0U,GTM_CONFIGURABLE_CLOCK_0),  
; ..\mcal_cfg\Gtm_PBCfg.c	   369      0x00050000U
; ..\mcal_cfg\Gtm_PBCfg.c	   370    },
; ..\mcal_cfg\Gtm_PBCfg.c	   371    {
; ..\mcal_cfg\Gtm_PBCfg.c	   372      GTM_DRIVER_COMPLEX,
; ..\mcal_cfg\Gtm_PBCfg.c	   373      Gtm_lIncludeIntMode8Bit(0x1U,GTM_INTERRUPT_PULSE_NOTIFY_MODE),
; ..\mcal_cfg\Gtm_PBCfg.c	   374      0x00U,
; ..\mcal_cfg\Gtm_PBCfg.c	   375      0x0U,
; ..\mcal_cfg\Gtm_PBCfg.c	   376      Gtm_TimbuildReg(0x10005U, GTM_CONFIGURABLE_CLOCK_0),
; ..\mcal_cfg\Gtm_PBCfg.c	   377      &Gtm_kTimFlt0[3],
; ..\mcal_cfg\Gtm_PBCfg.c	   378      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   379      Gtm_TimTduBuildReg(0x0U,GTM_CONFIGURABLE_CLOCK_0),  
; ..\mcal_cfg\Gtm_PBCfg.c	   380      0x00500000U
; ..\mcal_cfg\Gtm_PBCfg.c	   381    },
; ..\mcal_cfg\Gtm_PBCfg.c	   382  
; ..\mcal_cfg\Gtm_PBCfg.c	   383  };
; ..\mcal_cfg\Gtm_PBCfg.c	   384  
; ..\mcal_cfg\Gtm_PBCfg.c	   385  static const Gtm_ModUsageConfigType Gtm_kModUsage0 =
; ..\mcal_cfg\Gtm_PBCfg.c	   386  {  
; ..\mcal_cfg\Gtm_PBCfg.c	   387     /*TIM Module Usage */
; ..\mcal_cfg\Gtm_PBCfg.c	   388         {
; ..\mcal_cfg\Gtm_PBCfg.c	   389        0x00U, /*GTM Configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   390        0x01U, /*GTM Configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   391        GTM_TIM_CH_NOT_USEDBY_ICU,
; ..\mcal_cfg\Gtm_PBCfg.c	   392        GTM_TIM_CH_NOT_USEDBY_ICU,
; ..\mcal_cfg\Gtm_PBCfg.c	   393        0x02U, /*GTM Configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   394        0x03U, /*GTM Configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   395        GTM_TIM_CH_NOT_USEDBY_ICU,
; ..\mcal_cfg\Gtm_PBCfg.c	   396        GTM_TIM_CH_NOT_USEDBY_ICU,
; ..\mcal_cfg\Gtm_PBCfg.c	   397     },
; ..\mcal_cfg\Gtm_PBCfg.c	   398      {   /*TOM module Usage */
; ..\mcal_cfg\Gtm_PBCfg.c	   399       /*TOM Module 0 Usage*/    {
; ..\mcal_cfg\Gtm_PBCfg.c	   400        0x00U, /* GTM configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   401        0x01U, /* GTM configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   402        0x02U, /* GTM configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   403        0x03U, /* GTM configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   404        0x04U, /* GTM configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   405        0x05U, /* GTM configured channel*/
; ..\mcal_cfg\Gtm_PBCfg.c	   406        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   407        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   408        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   409        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   410        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   411        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   412        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   413        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   414        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   415        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   416      },
; ..\mcal_cfg\Gtm_PBCfg.c	   417       /*TOM Module 1 Usage*/
; ..\mcal_cfg\Gtm_PBCfg.c	   418      {
; ..\mcal_cfg\Gtm_PBCfg.c	   419        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   420        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   421        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   422        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   423        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   424        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   425        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   426        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   427        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   428        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   429        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   430        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   431        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   432        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   433        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   434        GTM_TOM_CH_NOT_USEDBY_GPT_PWM,
; ..\mcal_cfg\Gtm_PBCfg.c	   435      },
; ..\mcal_cfg\Gtm_PBCfg.c	   436    }
; ..\mcal_cfg\Gtm_PBCfg.c	   437  
; ..\mcal_cfg\Gtm_PBCfg.c	   438  };
; ..\mcal_cfg\Gtm_PBCfg.c	   439  
; ..\mcal_cfg\Gtm_PBCfg.c	   440  
; ..\mcal_cfg\Gtm_PBCfg.c	   441  
; ..\mcal_cfg\Gtm_PBCfg.c	   442  
; ..\mcal_cfg\Gtm_PBCfg.c	   443  static const uint8 Gtm_kAdcConnections0[GTM_NO_OF_ADC_MODULES] = 
; ..\mcal_cfg\Gtm_PBCfg.c	   444  {
; ..\mcal_cfg\Gtm_PBCfg.c	   445    0x00U,  0x00U,  0x00U,  0x00U,
; ..\mcal_cfg\Gtm_PBCfg.c	   446  };
; ..\mcal_cfg\Gtm_PBCfg.c	   447  
; ..\mcal_cfg\Gtm_PBCfg.c	   448  
; ..\mcal_cfg\Gtm_PBCfg.c	   449  static const Gtm_PortConfigType Gtm_kPortConfig0 =
; ..\mcal_cfg\Gtm_PBCfg.c	   450  {
; ..\mcal_cfg\Gtm_PBCfg.c	   451    {
; ..\mcal_cfg\Gtm_PBCfg.c	   452      0x00110022U,
; ..\mcal_cfg\Gtm_PBCfg.c	   453    },
; ..\mcal_cfg\Gtm_PBCfg.c	   454    {
; ..\mcal_cfg\Gtm_PBCfg.c	   455      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   456      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   457      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   458      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   459      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   460      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   461      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   462      0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   463    }
; ..\mcal_cfg\Gtm_PBCfg.c	   464  };
; ..\mcal_cfg\Gtm_PBCfg.c	   465  
; ..\mcal_cfg\Gtm_PBCfg.c	   466  static const Gtm_ModuleConfigType Gtm_kModuleConfig0 =
; ..\mcal_cfg\Gtm_PBCfg.c	   467  {
; ..\mcal_cfg\Gtm_PBCfg.c	   468  
; ..\mcal_cfg\Gtm_PBCfg.c	   469    GTM_SLEEP_DISABLE,  /* Module Sleep Mode */
; ..\mcal_cfg\Gtm_PBCfg.c	   470    1U,  /* Global Clock Configuration - Numerator */
; ..\mcal_cfg\Gtm_PBCfg.c	   471    1U,/* Global Clock Configuration - Denominator */
; ..\mcal_cfg\Gtm_PBCfg.c	   472    
; ..\mcal_cfg\Gtm_PBCfg.c	   473    0xFFFFFFFFU,  /* Access Enable 0 */
; ..\mcal_cfg\Gtm_PBCfg.c	   474    0UL,  /* Access Enable 1 */
; ..\mcal_cfg\Gtm_PBCfg.c	   475  
; ..\mcal_cfg\Gtm_PBCfg.c	   476    {  0x0a0aU
; ..\mcal_cfg\Gtm_PBCfg.c	   477    },    /* TIM Module Usage by GTM and ICU driver*/
; ..\mcal_cfg\Gtm_PBCfg.c	   478    {  0x33U
; ..\mcal_cfg\Gtm_PBCfg.c	   479    },  /* TIM Usage */
; ..\mcal_cfg\Gtm_PBCfg.c	   480    &Gtm_kTimConfig0[0],  /* TIM Configuration Pointer */
; ..\mcal_cfg\Gtm_PBCfg.c	   481    {0x01U},  /* TOM TGC Usage */
; ..\mcal_cfg\Gtm_PBCfg.c	   482    &Gtm_kTomTgcConfig0[0],  /* TOM TGC Configuration Pointer */
; ..\mcal_cfg\Gtm_PBCfg.c	   483    {0x00000fffU,
; ..\mcal_cfg\Gtm_PBCfg.c	   484     0x00000000U,
; ..\mcal_cfg\Gtm_PBCfg.c	   485     },
; ..\mcal_cfg\Gtm_PBCfg.c	   486    {0x0000003fU
; ..\mcal_cfg\Gtm_PBCfg.c	   487    },  /* TOM Usage */
; ..\mcal_cfg\Gtm_PBCfg.c	   488    &Gtm_kTomConfig0[0],  /* TOM Configuration Pointer */
; ..\mcal_cfg\Gtm_PBCfg.c	   489  
; ..\mcal_cfg\Gtm_PBCfg.c	   490    &Gtm_kModUsage0, /* Configuration for GTM Usage by other modules */
; ..\mcal_cfg\Gtm_PBCfg.c	   491    &Gtm_kGeneralConfig0,  /* GTM General Configuration */
; ..\mcal_cfg\Gtm_PBCfg.c	   492    NULL_PTR,  /* TBU Configuration Pointer */
; ..\mcal_cfg\Gtm_PBCfg.c	   493  
; ..\mcal_cfg\Gtm_PBCfg.c	   494    &Gtm_kAdcConnections0[0],  /* Adc Connections Configuration Pointer*/
; ..\mcal_cfg\Gtm_PBCfg.c	   495    {
; ..\mcal_cfg\Gtm_PBCfg.c	   496      0x0000U,  /* Ttcan Connections Configuration*/
; ..\mcal_cfg\Gtm_PBCfg.c	   497    },
; ..\mcal_cfg\Gtm_PBCfg.c	   498  };
; ..\mcal_cfg\Gtm_PBCfg.c	   499  
; ..\mcal_cfg\Gtm_PBCfg.c	   500  const Gtm_ConfigType Gtm_ConfigRoot[GTM_CONFIG_COUNT]  =
; ..\mcal_cfg\Gtm_PBCfg.c	   501  {
; ..\mcal_cfg\Gtm_PBCfg.c	   502    {
; ..\mcal_cfg\Gtm_PBCfg.c	   503      /*  GTM Module Clock Settings  */
; ..\mcal_cfg\Gtm_PBCfg.c	   504      &Gtm_kClockSetting0,
; ..\mcal_cfg\Gtm_PBCfg.c	   505      /*  Pointer to Gtm Configuration structure  */
; ..\mcal_cfg\Gtm_PBCfg.c	   506      &Gtm_kPortConfig0,
; ..\mcal_cfg\Gtm_PBCfg.c	   507      &Gtm_kModuleConfig0,
; ..\mcal_cfg\Gtm_PBCfg.c	   508    },
; ..\mcal_cfg\Gtm_PBCfg.c	   509  };
; ..\mcal_cfg\Gtm_PBCfg.c	   510  #define MCU_STOP_SEC_POSTBUILDCFG
; ..\mcal_cfg\Gtm_PBCfg.c	   511  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_cfg\Gtm_PBCfg.c	   512   allowed only for MemMap.h*/
; ..\mcal_cfg\Gtm_PBCfg.c	   513  #include "MemMap.h"
; ..\mcal_cfg\Gtm_PBCfg.c	   514  
; ..\mcal_cfg\Gtm_PBCfg.c	   515  
; ..\mcal_cfg\Gtm_PBCfg.c	   516  
; ..\mcal_cfg\Gtm_PBCfg.c	   517  
; ..\mcal_cfg\Gtm_PBCfg.c	   518  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   519  **                      Global Variable Definitions                           **
; ..\mcal_cfg\Gtm_PBCfg.c	   520  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   521  
; ..\mcal_cfg\Gtm_PBCfg.c	   522  
; ..\mcal_cfg\Gtm_PBCfg.c	   523  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   524  **                      Private Constant Definitions                          **
; ..\mcal_cfg\Gtm_PBCfg.c	   525  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   526  
; ..\mcal_cfg\Gtm_PBCfg.c	   527  
; ..\mcal_cfg\Gtm_PBCfg.c	   528  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   529  **                      Private Variable Definitions                          **
; ..\mcal_cfg\Gtm_PBCfg.c	   530  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   531  
; ..\mcal_cfg\Gtm_PBCfg.c	   532  
; ..\mcal_cfg\Gtm_PBCfg.c	   533  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   534  **                      Global Function Definitions                           **
; ..\mcal_cfg\Gtm_PBCfg.c	   535  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   536  
; ..\mcal_cfg\Gtm_PBCfg.c	   537  
; ..\mcal_cfg\Gtm_PBCfg.c	   538  /*******************************************************************************
; ..\mcal_cfg\Gtm_PBCfg.c	   539  **                      Private Function Definitions                          **
; ..\mcal_cfg\Gtm_PBCfg.c	   540  *******************************************************************************/
; ..\mcal_cfg\Gtm_PBCfg.c	   541  

	; Module end
