	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc28604a -c99 --dep-file=mcal_src\\.Gtm.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Gtm.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Gtm.src ..\\mcal_src\\Gtm.c"
	.compiler_name		"ctc"
	.name	"Gtm"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_Init')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_Init

; ..\mcal_src\Gtm.c	     1  
; ..\mcal_src\Gtm.c	     2  /******************************************************************************
; ..\mcal_src\Gtm.c	     3  **                                                                           **
; ..\mcal_src\Gtm.c	     4  ** Copyright (C) Infineon Technologies (2014)                                **
; ..\mcal_src\Gtm.c	     5  **                                                                           **
; ..\mcal_src\Gtm.c	     6  ** All rights reserved.                                                      **
; ..\mcal_src\Gtm.c	     7  **                                                                           **
; ..\mcal_src\Gtm.c	     8  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Gtm.c	     9  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Gtm.c	    10  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Gtm.c	    11  **                                                                           **
; ..\mcal_src\Gtm.c	    12  *******************************************************************************
; ..\mcal_src\Gtm.c	    13  **                                                                           **
; ..\mcal_src\Gtm.c	    14  **  $FILENAME   : Gtm.c $                                                    **
; ..\mcal_src\Gtm.c	    15  **                                                                           **
; ..\mcal_src\Gtm.c	    16  **  $CC VERSION : \main\dev_tc23x\40 $                                       **
; ..\mcal_src\Gtm.c	    17  **                                                                           **
; ..\mcal_src\Gtm.c	    18  **  $DATE       : 2018-06-08 $                                               **
; ..\mcal_src\Gtm.c	    19  **                                                                           **
; ..\mcal_src\Gtm.c	    20  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Gtm.c	    21  **                                                                           **
; ..\mcal_src\Gtm.c	    22  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Gtm.c	    23  **                                                                           **
; ..\mcal_src\Gtm.c	    24  **  DESCRIPTION : This file contains                                         **
; ..\mcal_src\Gtm.c	    25  **                functionality of GTM driver.                               **
; ..\mcal_src\Gtm.c	    26  **                                                                           **
; ..\mcal_src\Gtm.c	    27  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Gtm.c	    28  **                                                                           **
; ..\mcal_src\Gtm.c	    29  ******************************************************************************/
; ..\mcal_src\Gtm.c	    30  
; ..\mcal_src\Gtm.c	    31  /*  TRACEABILITY: [cover parentID=DS_NAS_GTM_PR1086,
; ..\mcal_src\Gtm.c	    32  DS_NAS_GTM_PR73,DS_NAS_GTM_PR228,SAS_NAS_ALL_PR70,DS_NAS_GTM_PR128,
; ..\mcal_src\Gtm.c	    33  SAS_NAS_ALL_PR470,SAS_NAS_ALL_PR630_PR631,SAS_NAS_ALL_PR749,
; ..\mcal_src\Gtm.c	    34  SAS_NAS_ALL_PR1652,DS_NAS_GTM_PR115]
; ..\mcal_src\Gtm.c	    35  [/cover]
; ..\mcal_src\Gtm.c	    36  */
; ..\mcal_src\Gtm.c	    37  
; ..\mcal_src\Gtm.c	    38  /*******************************************************************************
; ..\mcal_src\Gtm.c	    39  **                      Includes                                              **
; ..\mcal_src\Gtm.c	    40  *******************************************************************************/
; ..\mcal_src\Gtm.c	    41  /* Own header file, this includes own configuration file also */
; ..\mcal_src\Gtm.c	    42  #include "Gtm.h"
; ..\mcal_src\Gtm.c	    43  #include "Gtm_Local.h"
; ..\mcal_src\Gtm.c	    44  /* Include Irq definitions for IRQ */
; ..\mcal_src\Gtm.c	    45  #include "IfxSrc_reg.h"
; ..\mcal_src\Gtm.c	    46  
; ..\mcal_src\Gtm.c	    47  
; ..\mcal_src\Gtm.c	    48  #if (GTM_MCUSAFETY_ENABLE == STD_ON)
; ..\mcal_src\Gtm.c	    49  #include "SafetyReport.h"
; ..\mcal_src\Gtm.c	    50  #endif
; ..\mcal_src\Gtm.c	    51  
; ..\mcal_src\Gtm.c	    52  /*******************************************************************************
; ..\mcal_src\Gtm.c	    53  **                      Private Macro Definitions                             **
; ..\mcal_src\Gtm.c	    54  *******************************************************************************/
; ..\mcal_src\Gtm.c	    55  
; ..\mcal_src\Gtm.c	    56  #define GTM_VALUE_ZERO (0U)
; ..\mcal_src\Gtm.c	    57  #define GTM_ONE (1)
; ..\mcal_src\Gtm.c	    58  #define GTM_ZERO (0)
; ..\mcal_src\Gtm.c	    59  
; ..\mcal_src\Gtm.c	    60  #define GTM_ZERO_U (0U)
; ..\mcal_src\Gtm.c	    61  #define GTM_ONE_U (1U)
; ..\mcal_src\Gtm.c	    62  #define GTM_TWO_U (2U)
; ..\mcal_src\Gtm.c	    63  #define GTM_SIXTEEN_U (16U)
; ..\mcal_src\Gtm.c	    64  #define GTM_EIGHT_U (8U)
; ..\mcal_src\Gtm.c	    65  #define GTM_THREE_UL (3UL)
; ..\mcal_src\Gtm.c	    66  #define GTM_GET_IRQ_VAL_8 (0x3FUL)
; ..\mcal_src\Gtm.c	    67  
; ..\mcal_src\Gtm.c	    68  
; ..\mcal_src\Gtm.c	    69  /* Get the IRQ Mode Information from a 8bit variable */
; ..\mcal_src\Gtm.c	    70  #define GTM_GET_MODE_VAL_8 (0xC0UL)
; ..\mcal_src\Gtm.c	    71  #define GTM_GET_EIRQ_VAL_8 (0x1E00UL)
; ..\mcal_src\Gtm.c	    72  
; ..\mcal_src\Gtm.c	    73  /* Bits to shift to get the mode information to LSB for a 8 bit variable */
; ..\mcal_src\Gtm.c	    74  #define GTM_SHIFT_TO_LSB_8 (6)
; ..\mcal_src\Gtm.c	    75  #define GTM_SHIFT_EIRQ_TO_LSB_8 (9U)
; ..\mcal_src\Gtm.c	    76  /* Get the IRQ Enable Information from a 32 bit variable */
; ..\mcal_src\Gtm.c	    77  #define GTM_GET_IRQ_VAL_32 (0x3FFFFFFFUL)
; ..\mcal_src\Gtm.c	    78  /* Get the IRQ Mode Information from a 32 bit variable */
; ..\mcal_src\Gtm.c	    79  #define GTM_GET_MODE_VAL_32 (0xC0000000UL)
; ..\mcal_src\Gtm.c	    80  /* Bits to shift to get the mode information to LSB for a 32 bit variable */
; ..\mcal_src\Gtm.c	    81  #define GTM_SHIFT_TO_LSB_32 (30U)
; ..\mcal_src\Gtm.c	    82  
; ..\mcal_src\Gtm.c	    83  
; ..\mcal_src\Gtm.c	    84  #define GTM_TOM_CHAN_PER_SRN (2U)
; ..\mcal_src\Gtm.c	    85  #define GTM_GET_INT_STATUS (3UL)
; ..\mcal_src\Gtm.c	    86  #define GTM_GET_TIM_INT_STATUS (1UL)
; ..\mcal_src\Gtm.c	    87  
; ..\mcal_src\Gtm.c	    88  
; ..\mcal_src\Gtm.c	    89  #define GTM_CLEAR_INTERRUPT (3UL)
; ..\mcal_src\Gtm.c	    90  #define GTM_CLEAR_TIM_INTERRUPT (0x3FUL)
; ..\mcal_src\Gtm.c	    91  
; ..\mcal_src\Gtm.c	    92  #define GTM_TOM_TGC_GET_FUPD (0xFFFFUL)
; ..\mcal_src\Gtm.c	    93  #define GTM_TOM_TGC_CLEAR_FUPD (0x5555UL)
; ..\mcal_src\Gtm.c	    94  #define GTM_TOM_CHAN_ENABLE (3UL)
; ..\mcal_src\Gtm.c	    95  
; ..\mcal_src\Gtm.c	    96  #define GTM_CCU1 (1U)
; ..\mcal_src\Gtm.c	    97  
; ..\mcal_src\Gtm.c	    98  #define GTM_TIM_GLITCH_POS (5U)
; ..\mcal_src\Gtm.c	    99  
; ..\mcal_src\Gtm.c	   100  #define GTM_CMU_CLK_ENABLE (2UL)
; ..\mcal_src\Gtm.c	   101  #define GTM_CMU_REG_CLK_ENABLE (3UL)
; ..\mcal_src\Gtm.c	   102  #define GTM_CMU_START_FROM_EXTCLK (16U)
; ..\mcal_src\Gtm.c	   103  
; ..\mcal_src\Gtm.c	   104  #define GTM_DISABLE_ALL_CLOCKS (0x00555555U)
; ..\mcal_src\Gtm.c	   105  
; ..\mcal_src\Gtm.c	   106  #define GTM_TBU_CONFIGURED (5U)
; ..\mcal_src\Gtm.c	   107  
; ..\mcal_src\Gtm.c	   108  #define GTM_BITS_IN_U32 (32U)
; ..\mcal_src\Gtm.c	   109  #define GTM_TBU_GET_LOWER_NIBBLE  (0x0FUL)
; ..\mcal_src\Gtm.c	   110  
; ..\mcal_src\Gtm.c	   111  /* Get the CMU EIRQ Enable Information from a 32 bit variable */
; ..\mcal_src\Gtm.c	   112  #define GTM_CMU_GET_EIRQ_VAL_32 (0x00FFFFFFUL)
; ..\mcal_src\Gtm.c	   113  /* Get the CMU Fixed clock control line Info from a 32 bit variable */
; ..\mcal_src\Gtm.c	   114  #define GTM_GET_FIXED_CLK_VAL (0x0FUL)
; ..\mcal_src\Gtm.c	   115  
; ..\mcal_src\Gtm.c	   116  #define GTM_CMU_NO_OF_CLOCKS (8U)
; ..\mcal_src\Gtm.c	   117  #define GTM_CMU_NO_OF_EXT_CLOCKS (3U)
; ..\mcal_src\Gtm.c	   118  #define GTM_CMU_TOTAL_CLOCKS (12U)
; ..\mcal_src\Gtm.c	   119  
; ..\mcal_src\Gtm.c	   120  
; ..\mcal_src\Gtm.c	   121  /* Address of first register of ICM from where the corresponding Sub-Modules
; ..\mcal_src\Gtm.c	   122     status are reported */
; ..\mcal_src\Gtm.c	   123  #define GTM_TIM_ICM_BASE_ADDRESS (&GTM_ICM_IRQG_2)
; ..\mcal_src\Gtm.c	   124  #define GTM_TOM_ICM_BASE_ADDRESS (&GTM_ICM_IRQG_6)
; ..\mcal_src\Gtm.c	   125  
; ..\mcal_src\Gtm.c	   126  /* Number of Modules status that are reported per ICM register */
; ..\mcal_src\Gtm.c	   127  #define GTM_TOM_MODULES_IN_ICM_REG  (2U)
; ..\mcal_src\Gtm.c	   128  #define GTM_TIM_MODULES_IN_ICM_REG  (4U)
; ..\mcal_src\Gtm.c	   129  
; ..\mcal_src\Gtm.c	   130  /* This signifies that number of TOM/TIM module definitions present in a
; ..\mcal_src\Gtm.c	   131     single element of GtmTomUsage/GtmTimUsage parameter */
; ..\mcal_src\Gtm.c	   132  #define GTM_TOM_MODS_IN_UINT32 (GTM_BITS_IN_U32/GTM_CHANNELS_PER_TOM_MODULE)
; ..\mcal_src\Gtm.c	   133  #define GTM_TIM_MODS_IN_UINT32 (GTM_BITS_IN_U32/GTM_CHANNELS_PER_TIM_MODULE)
; ..\mcal_src\Gtm.c	   134  
; ..\mcal_src\Gtm.c	   135  #define GTM_MCAL_TIMEOUT_VAL (150000U)
; ..\mcal_src\Gtm.c	   136  #define GTM_INVALID_NUMBER (0xFFU)
; ..\mcal_src\Gtm.c	   137  
; ..\mcal_src\Gtm.c	   138  /*Xor compare value for initcheck*/
; ..\mcal_src\Gtm.c	   139  #define GTM_32_XOR_COMPARE_VAL (0xFFFFFFFFUL)
; ..\mcal_src\Gtm.c	   140  #define GTM_GET_EN_REG_BIT (0x00555555UL)
; ..\mcal_src\Gtm.c	   141  #define GTM_ENDIS_CLR_MASK          (0x0000FFFFUL)
; ..\mcal_src\Gtm.c	   142  #define GTM_OUTEN_CLR_MASK          (0x0000FFFFUL)
; ..\mcal_src\Gtm.c	   143  #define GTM_FUPD_CLR_MASK          (0x0000FFFFUL)
; ..\mcal_src\Gtm.c	   144  #define GTM_KERNEL_RESET_DELAY (0x100U)
; ..\mcal_src\Gtm.c	   145  
; ..\mcal_src\Gtm.c	   146  /*******************************************************************************
; ..\mcal_src\Gtm.c	   147  **                      Private Type Definitions                              **
; ..\mcal_src\Gtm.c	   148  *******************************************************************************/
; ..\mcal_src\Gtm.c	   149  /** CMU object */
; ..\mcal_src\Gtm.c	   150  typedef Ifx_GTM_CMU_CLK0_5 Gtm_CmuClkCtrl;
; ..\mcal_src\Gtm.c	   151  typedef Ifx_GTM_CMU_ECLK_NUM Gtm_CmuEclkNumType;
; ..\mcal_src\Gtm.c	   152  typedef Ifx_GTM_CMU_ECLK_DEN Gtm_CmuEclkDenType;
; ..\mcal_src\Gtm.c	   153  
; ..\mcal_src\Gtm.c	   154  typedef struct
; ..\mcal_src\Gtm.c	   155  {
; ..\mcal_src\Gtm.c	   156    Gtm_CmuEclkNumType CmuEclkNum;
; ..\mcal_src\Gtm.c	   157    Gtm_CmuEclkDenType CmuEclkDen;
; ..\mcal_src\Gtm.c	   158  }Gtm_CmuEclkType;
; ..\mcal_src\Gtm.c	   159  
; ..\mcal_src\Gtm.c	   160  
; ..\mcal_src\Gtm.c	   161  typedef volatile struct
; ..\mcal_src\Gtm.c	   162  {
; ..\mcal_src\Gtm.c	   163    /* CMU Clock Enable Register */
; ..\mcal_src\Gtm.c	   164    Ifx_GTM_CMU_CLK_EN CLK_EN;
; ..\mcal_src\Gtm.c	   165    /* CMU Global Clock Control Numerator Register */
; ..\mcal_src\Gtm.c	   166    Ifx_GTM_CMU_GCLK_NUM GCLK_NUM;
; ..\mcal_src\Gtm.c	   167    /* CMU Global Clock Control Denominator Register */
; ..\mcal_src\Gtm.c	   168    Ifx_GTM_CMU_GCLK_DEN GCLK_DEN;
; ..\mcal_src\Gtm.c	   169    /* CMU Control For Clock Register */
; ..\mcal_src\Gtm.c	   170    Gtm_CmuClkCtrl ClkCtrl[GTM_CMU_NO_OF_CLOCKS];
; ..\mcal_src\Gtm.c	   171    /* CMU External Clock Control Registers */
; ..\mcal_src\Gtm.c	   172    Gtm_CmuEclkType CmuEclk[GTM_CMU_NO_OF_EXT_CLOCKS];
; ..\mcal_src\Gtm.c	   173    /* Fixed clock divider value  */
; ..\mcal_src\Gtm.c	   174    uint32 GtmFxdClkControl;
; ..\mcal_src\Gtm.c	   175  } Gtm_CmuType;
; ..\mcal_src\Gtm.c	   176  
; ..\mcal_src\Gtm.c	   177  
; ..\mcal_src\Gtm.c	   178  #define GTM_START_SEC_CODE
; ..\mcal_src\Gtm.c	   179  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   180   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   181  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   182  
; ..\mcal_src\Gtm.c	   183  /*******************************************************************************
; ..\mcal_src\Gtm.c	   184  **                      Private Function Declarations                         **
; ..\mcal_src\Gtm.c	   185  *******************************************************************************/
; ..\mcal_src\Gtm.c	   186  #if (defined(GTM_TOM_MODULE_USED)) 
; ..\mcal_src\Gtm.c	   187  /* To get the Source that has triggered Interrupt */
; ..\mcal_src\Gtm.c	   188  IFX_LOCAL_INLINE uint32 Gtm_lGetIntSource(uint32 Value, uint8 Index)
; ..\mcal_src\Gtm.c	   189  {
; ..\mcal_src\Gtm.c	   190    return((uint32)(Value & (GTM_BIT_SET << Index )));
; ..\mcal_src\Gtm.c	   191  }
; ..\mcal_src\Gtm.c	   192  #endif
; ..\mcal_src\Gtm.c	   193  /* for #if (defined(GTM_TOM_MODULE_USED))  */
; ..\mcal_src\Gtm.c	   194  
; ..\mcal_src\Gtm.c	   195  /* To identify if a sub-module is configured or not */
; ..\mcal_src\Gtm.c	   196  IFX_LOCAL_INLINE boolean Gtm_lGetUnused8(uint8 CtrlVal)
; ..\mcal_src\Gtm.c	   197  {
; ..\mcal_src\Gtm.c	   198    return(((CtrlVal & GTM_SETUNUSED8) > GTM_VALUE_ZERO)?\ 
; ..\mcal_src\Gtm.c	   199                                                    (boolean)TRUE:(boolean)FALSE);
; ..\mcal_src\Gtm.c	   200  }
; ..\mcal_src\Gtm.c	   201  
; ..\mcal_src\Gtm.c	   202  
; ..\mcal_src\Gtm.c	   203  /* To get the value at bitposition of the input "Value" */
; ..\mcal_src\Gtm.c	   204  IFX_LOCAL_INLINE uint8 Gtm_lGetBit8(uint8 Value, uint8 BitPosition)
; ..\mcal_src\Gtm.c	   205  {
; ..\mcal_src\Gtm.c	   206    return((uint8)(((Value & (uint8)(GTM_ONE_U << BitPosition))> GTM_VALUE_ZERO)
; ..\mcal_src\Gtm.c	   207                                                              ?GTM_ONE:GTM_ZERO));
; ..\mcal_src\Gtm.c	   208  }
; ..\mcal_src\Gtm.c	   209  
; ..\mcal_src\Gtm.c	   210  /* Get the Driver that uses the particular channel */
; ..\mcal_src\Gtm.c	   211  IFX_LOCAL_INLINE uint8 Gtm_lGetModuleIndex(uint32 ModUsage, uint8 Channel)
; ..\mcal_src\Gtm.c	   212  {
; ..\mcal_src\Gtm.c	   213    return((uint8)((ModUsage & (GTM_THREE_UL << (GTM_TWO_U * Channel))) >>
; ..\mcal_src\Gtm.c	   214                                                          (GTM_TWO_U * Channel)));
; ..\mcal_src\Gtm.c	   215  }
; ..\mcal_src\Gtm.c	   216  
; ..\mcal_src\Gtm.c	   217  IFX_LOCAL_INLINE void Gtm_lCmuClockConfigure(void);
; ..\mcal_src\Gtm.c	   218  
; ..\mcal_src\Gtm.c	   219  #ifdef GTM_TIM_MODULE_USED
; ..\mcal_src\Gtm.c	   220  
; ..\mcal_src\Gtm.c	   221  IFX_LOCAL_INLINE uint32 Gtm_lGetTimIrqStatus(uint8 ModuleNo,\ 
; ..\mcal_src\Gtm.c	   222                                                            uint8 ChannelNumber);
; ..\mcal_src\Gtm.c	   223  #endif
; ..\mcal_src\Gtm.c	   224  IFX_LOCAL_INLINE void Gtm_lCmuExtClkConfigure(void);
; ..\mcal_src\Gtm.c	   225  
; ..\mcal_src\Gtm.c	   226  IFX_LOCAL_INLINE void  Gtm_lCmuConfClkConfigure(void);
; ..\mcal_src\Gtm.c	   227  
; ..\mcal_src\Gtm.c	   228  IFX_LOCAL_INLINE void Gtm_lSaveTgcStatus(Ifx_GTM_TOM_TGC_TYPE *TomTgcRegPtr);
; ..\mcal_src\Gtm.c	   229  
; ..\mcal_src\Gtm.c	   230  IFX_LOCAL_INLINE void Gtm_lTomHostTrigger(void);
; ..\mcal_src\Gtm.c	   231  
; ..\mcal_src\Gtm.c	   232  IFX_LOCAL_INLINE void Gtm_lTomTgcConfigure(void);
; ..\mcal_src\Gtm.c	   233  
; ..\mcal_src\Gtm.c	   234  IFX_LOCAL_INLINE void Gtm_lPortConfig(void);
; ..\mcal_src\Gtm.c	   235  
; ..\mcal_src\Gtm.c	   236  IFX_LOCAL_INLINE void Gtm_lTbuConfig(void);
; ..\mcal_src\Gtm.c	   237  
; ..\mcal_src\Gtm.c	   238  IFX_LOCAL_INLINE void Gtm_lTtcanConnectionsConfig(void);
; ..\mcal_src\Gtm.c	   239  
; ..\mcal_src\Gtm.c	   240  IFX_LOCAL_INLINE Std_ReturnType Gtm_lEnableGtm(void);
; ..\mcal_src\Gtm.c	   241  
; ..\mcal_src\Gtm.c	   242  
; ..\mcal_src\Gtm.c	   243  #if (GTM_SFR_RESET_ENABLE == STD_ON)
; ..\mcal_src\Gtm.c	   244  IFX_LOCAL_INLINE void Gtm_lResetKernelInit(void);
; ..\mcal_src\Gtm.c	   245  
; ..\mcal_src\Gtm.c	   246  #endif
; ..\mcal_src\Gtm.c	   247  
; ..\mcal_src\Gtm.c	   248  #if (GTM_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Gtm.c	   249  
; ..\mcal_src\Gtm.c	   250  IFX_LOCAL_INLINE void Gtm_lResetKernelDeInit(void);
; ..\mcal_src\Gtm.c	   251  
; ..\mcal_src\Gtm.c	   252  #endif
; ..\mcal_src\Gtm.c	   253  
; ..\mcal_src\Gtm.c	   254  IFX_LOCAL_INLINE void Gtm_lTomConfigure(void);
; ..\mcal_src\Gtm.c	   255  
; ..\mcal_src\Gtm.c	   256  
; ..\mcal_src\Gtm.c	   257  #if (defined(GTM_TOM_MODULE_USED))
; ..\mcal_src\Gtm.c	   258  IFX_LOCAL_INLINE uint32 Gtm_lGetTomIrqStatus(uint8 ModuleNo,
; ..\mcal_src\Gtm.c	   259                                              uint8 ChannelNumber);
; ..\mcal_src\Gtm.c	   260  #endif
; ..\mcal_src\Gtm.c	   261  
; ..\mcal_src\Gtm.c	   262  
; ..\mcal_src\Gtm.c	   263  
; ..\mcal_src\Gtm.c	   264  #define GTM_STOP_SEC_CODE
; ..\mcal_src\Gtm.c	   265  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   266   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   267  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   268  
; ..\mcal_src\Gtm.c	   269  /*******************************************************************************
; ..\mcal_src\Gtm.c	   270  **                      Global Constant Definitions                           **
; ..\mcal_src\Gtm.c	   271  *******************************************************************************/
; ..\mcal_src\Gtm.c	   272  
; ..\mcal_src\Gtm.c	   273  /*
; ..\mcal_src\Gtm.c	   274   * To be used for global or static variables (32 bits) that are initialized
; ..\mcal_src\Gtm.c	   275   * after every reset (the normal case)
; ..\mcal_src\Gtm.c	   276     Inclusion of MemMap.h
; ..\mcal_src\Gtm.c	   277  */
; ..\mcal_src\Gtm.c	   278  
; ..\mcal_src\Gtm.c	   279  #define GTM_START_SEC_VAR_32BIT
; ..\mcal_src\Gtm.c	   280  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   281   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   282  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   283  
; ..\mcal_src\Gtm.c	   284  const Gtm_ConfigType *Gtm_kConfigPtr;
; ..\mcal_src\Gtm.c	   285  
; ..\mcal_src\Gtm.c	   286  #define GTM_STOP_SEC_VAR_32BIT
; ..\mcal_src\Gtm.c	   287  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   288   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   289  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   290  
; ..\mcal_src\Gtm.c	   291  #if ((GTM_NO_OF_TIM_CH_CONF_NOTIF > 0U)  ||(GTM_NO_OF_TOM_CH_CONF_NOTIF > 0U))
; ..\mcal_src\Gtm.c	   292  
; ..\mcal_src\Gtm.c	   293  #define GTM_START_SEC_CONST_UNSPECIFIED
; ..\mcal_src\Gtm.c	   294  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   295   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   296  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   297  
; ..\mcal_src\Gtm.c	   298  
; ..\mcal_src\Gtm.c	   299  /* Declaration of GTM Notification Link time configuration */
; ..\mcal_src\Gtm.c	   300  /*IFX_MISRA_RULE_08_07_STATUS= Reports Misra violation 8.7 under specific 
; ..\mcal_src\Gtm.c	   301  configuration.
; ..\mcal_src\Gtm.c	   302  If only TOM  or only TIM notification is configured then 
; ..\mcal_src\Gtm.c	   303  Gtm_kNotifConfig0 will be used only in one function body (ISR).*/
; ..\mcal_src\Gtm.c	   304  extern const Gtm_NotificationConfigType Gtm_kNotifConfig0;
; ..\mcal_src\Gtm.c	   305  
; ..\mcal_src\Gtm.c	   306  #define GTM_STOP_SEC_CONST_UNSPECIFIED
; ..\mcal_src\Gtm.c	   307  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   308   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   309  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   310  
; ..\mcal_src\Gtm.c	   311  #endif 
; ..\mcal_src\Gtm.c	   312  /* End of ((GTM_NO_OF_TIM_CH_CONF_NOTIF > 0U) 
; ..\mcal_src\Gtm.c	   313  ||(GTM_NO_OF_TOM_CH_CONF_NOTIF > 0U)) */
; ..\mcal_src\Gtm.c	   314  
; ..\mcal_src\Gtm.c	   315  
; ..\mcal_src\Gtm.c	   316  #ifdef IFX_GTM_DEBUG
; ..\mcal_src\Gtm.c	   317  
; ..\mcal_src\Gtm.c	   318  /* included the extern declaration of the debug variables in the 
; ..\mcal_src\Gtm.c	   319   memory section GTM_SEC_VAR_32BIT based on Jira #0000051018-1288 */
; ..\mcal_src\Gtm.c	   320  #define GTM_START_SEC_VAR_32BIT
; ..\mcal_src\Gtm.c	   321  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Gtm.c	   322   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   323  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   324  
; ..\mcal_src\Gtm.c	   325  extern volatile uint32 TestGtm_DebugMask01;
; ..\mcal_src\Gtm.c	   326  extern volatile uint32 TestGtm_DebugMask02;
; ..\mcal_src\Gtm.c	   327  
; ..\mcal_src\Gtm.c	   328  #define GTM_STOP_SEC_VAR_32BIT
; ..\mcal_src\Gtm.c	   329  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_src\Gtm.c	   330   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   331  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   332  
; ..\mcal_src\Gtm.c	   333  #endif /* End of ifdef IFX_GTM_DEBUG */
; ..\mcal_src\Gtm.c	   334  
; ..\mcal_src\Gtm.c	   335  
; ..\mcal_src\Gtm.c	   336  /*******************************************************************************
; ..\mcal_src\Gtm.c	   337  **                      Global Variable Definitions                           **
; ..\mcal_src\Gtm.c	   338  *******************************************************************************/
; ..\mcal_src\Gtm.c	   339  
; ..\mcal_src\Gtm.c	   340  /*******************************************************************************
; ..\mcal_src\Gtm.c	   341  **                      Private Constant Definitions                          **
; ..\mcal_src\Gtm.c	   342  *******************************************************************************/
; ..\mcal_src\Gtm.c	   343  
; ..\mcal_src\Gtm.c	   344  /*******************************************************************************
; ..\mcal_src\Gtm.c	   345  **                      Private Variable Definitions                          **
; ..\mcal_src\Gtm.c	   346  *******************************************************************************/
; ..\mcal_src\Gtm.c	   347  
; ..\mcal_src\Gtm.c	   348  /*******************************************************************************
; ..\mcal_src\Gtm.c	   349  **                      Global Function Declarations                          **
; ..\mcal_src\Gtm.c	   350  *******************************************************************************/
; ..\mcal_src\Gtm.c	   351  #if (GTM_GPT_MODULE_USED == STD_ON)
; ..\mcal_src\Gtm.c	   352  
; ..\mcal_src\Gtm.c	   353  #define GPT_START_SEC_CALLOUT_CODE
; ..\mcal_src\Gtm.c	   354  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   355   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   356  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   357  
; ..\mcal_src\Gtm.c	   358  /*******************************************************************************
; ..\mcal_src\Gtm.c	   359  ** Syntax : void Gpt_Isr (uint8 ModChannelNo)                                 **
; ..\mcal_src\Gtm.c	   360  **                                                                            **
; ..\mcal_src\Gtm.c	   361  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   362  **                                                                            **
; ..\mcal_src\Gtm.c	   363  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   364  **                                                                            **
; ..\mcal_src\Gtm.c	   365  ** Reentrancy:       reentrant                                                **
; ..\mcal_src\Gtm.c	   366  **                                                                            **
; ..\mcal_src\Gtm.c	   367  ** Parameters (in):  ModChannelNo- Gpt channel number which called the        **
; ..\mcal_src\Gtm.c	   368  **            TOM channel interrupt                                           **
; ..\mcal_src\Gtm.c	   369  **                                                                            **
; ..\mcal_src\Gtm.c	   370  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   371  **                                                                            **
; ..\mcal_src\Gtm.c	   372  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	   373  **                                                                            **
; ..\mcal_src\Gtm.c	   374  ** Description :     This ISR services the interrupt generated from GTM to GPT**
; ..\mcal_src\Gtm.c	   375  *******************************************************************************/
; ..\mcal_src\Gtm.c	   376  extern void Gpt_Isr(uint8 ModChannelNo);
; ..\mcal_src\Gtm.c	   377  
; ..\mcal_src\Gtm.c	   378  #define GPT_STOP_SEC_CALLOUT_CODE
; ..\mcal_src\Gtm.c	   379  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   380   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   381  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   382  
; ..\mcal_src\Gtm.c	   383  #endif
; ..\mcal_src\Gtm.c	   384  
; ..\mcal_src\Gtm.c	   385  #if (GTM_PWM_MODULE_USED == STD_ON)
; ..\mcal_src\Gtm.c	   386  
; ..\mcal_src\Gtm.c	   387  #define PWM_17_GTM_START_SEC_CALLOUT_CODE
; ..\mcal_src\Gtm.c	   388  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   389   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   390  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   391  /*******************************************************************************
; ..\mcal_src\Gtm.c	   392  ** Syntax : void Pwm_17_Gtm_Isr (uint8 ModChannelNo)                          **
; ..\mcal_src\Gtm.c	   393  **                                                                            **
; ..\mcal_src\Gtm.c	   394  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   395  **                                                                            **
; ..\mcal_src\Gtm.c	   396  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   397  **                                                                            **
; ..\mcal_src\Gtm.c	   398  ** Reentrancy:       reentrant                                                **
; ..\mcal_src\Gtm.c	   399  **                                                                            **
; ..\mcal_src\Gtm.c	   400  ** Parameters (in):  ModChannelNo- PWM channel number which called the        **
; ..\mcal_src\Gtm.c	   401  **            TOM channel interrupt                                           **
; ..\mcal_src\Gtm.c	   402  **                                                                            **
; ..\mcal_src\Gtm.c	   403  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   404  **                                                                            **
; ..\mcal_src\Gtm.c	   405  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	   406  **                                                                            **
; ..\mcal_src\Gtm.c	   407  ** Description :     This ISR services the interrupt generated from GTM to PWM**
; ..\mcal_src\Gtm.c	   408  *******************************************************************************/
; ..\mcal_src\Gtm.c	   409  extern void Pwm_17_Gtm_Isr(uint8 ModChannelNo);
; ..\mcal_src\Gtm.c	   410  
; ..\mcal_src\Gtm.c	   411  #define PWM_17_GTM_STOP_SEC_CALLOUT_CODE
; ..\mcal_src\Gtm.c	   412  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   413   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   414  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   415  
; ..\mcal_src\Gtm.c	   416  #endif
; ..\mcal_src\Gtm.c	   417  
; ..\mcal_src\Gtm.c	   418  
; ..\mcal_src\Gtm.c	   419  #if (GTM_ICU_MODULE_USED == STD_ON)
; ..\mcal_src\Gtm.c	   420  
; ..\mcal_src\Gtm.c	   421  #define ICU_17_GTMCCU6_START_SEC_CALLOUT_CODE
; ..\mcal_src\Gtm.c	   422  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   423   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   424  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   425  /*******************************************************************************
; ..\mcal_src\Gtm.c	   426  ** Syntax : void Icu_17_GtmCcu6_Eru_Isr (uint8 ModChannelNo)                  **
; ..\mcal_src\Gtm.c	   427  **                                                                            **
; ..\mcal_src\Gtm.c	   428  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   429  **                                                                            **
; ..\mcal_src\Gtm.c	   430  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   431  **                                                                            **
; ..\mcal_src\Gtm.c	   432  ** Reentrancy:       reentrant                                                **
; ..\mcal_src\Gtm.c	   433  **                                                                            **
; ..\mcal_src\Gtm.c	   434  ** Parameters (in):  ModChannelNo- Icu channel number which called the        **
; ..\mcal_src\Gtm.c	   435  **            TOM channel interrupt                                           **
; ..\mcal_src\Gtm.c	   436  **                                                                            **
; ..\mcal_src\Gtm.c	   437  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   438  **                                                                            **
; ..\mcal_src\Gtm.c	   439  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	   440  **                                                                            **
; ..\mcal_src\Gtm.c	   441  ** Description :     This ISR services the interrupt generated from GTM to ICU**
; ..\mcal_src\Gtm.c	   442  *******************************************************************************/
; ..\mcal_src\Gtm.c	   443  extern void Icu_17_GtmCcu6_Isr(uint8 ModChannelNo);
; ..\mcal_src\Gtm.c	   444  
; ..\mcal_src\Gtm.c	   445  #define ICU_17_GTMCCU6_STOP_SEC_CALLOUT_CODE
; ..\mcal_src\Gtm.c	   446  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   447   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   448  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   449  
; ..\mcal_src\Gtm.c	   450  #endif
; ..\mcal_src\Gtm.c	   451  
; ..\mcal_src\Gtm.c	   452  /*******************************************************************************
; ..\mcal_src\Gtm.c	   453  **                      Global Function Definitions                           **
; ..\mcal_src\Gtm.c	   454  *******************************************************************************/
; ..\mcal_src\Gtm.c	   455  /*Memory Map of the GTM Code*/
; ..\mcal_src\Gtm.c	   456  #define GTM_START_SEC_CODE
; ..\mcal_src\Gtm.c	   457  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	   458   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	   459  #include "MemMap.h"
; ..\mcal_src\Gtm.c	   460  
; ..\mcal_src\Gtm.c	   461  /*******************************************************************************
; ..\mcal_src\Gtm.c	   462  ** Traceability : [cover parentID=DS_NAS_GTM_PR1088,DS_NAS_GTM_PR1089,
; ..\mcal_src\Gtm.c	   463    DS_NAS_GTM_PR1090,DS_NAS_GTM_PR1091,DS_NAS_EP_GTM_PR3018]
; ..\mcal_src\Gtm.c	   464  ** Syntax : Std_ReturnType  Gtm_Init(const Gtm_ConfigType *ConfigPtr)         **
; ..\mcal_src\Gtm.c	   465  ** [/cover]                                                                   **
; ..\mcal_src\Gtm.c	   466  **                                                                            **
; ..\mcal_src\Gtm.c	   467  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   468  **                                                                            **
; ..\mcal_src\Gtm.c	   469  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   470  **                                                                            **
; ..\mcal_src\Gtm.c	   471  ** Reentrancy:       non reentrant                                            **
; ..\mcal_src\Gtm.c	   472  **                                                                            **
; ..\mcal_src\Gtm.c	   473  ** Parameters (in):  ConfigPtr - Pointer to configuration set                 **
; ..\mcal_src\Gtm.c	   474  **                                                                            **
; ..\mcal_src\Gtm.c	   475  ** Parameters (out): none                                                     **
; ..\mcal_src\Gtm.c	   476  **                                                                            **
; ..\mcal_src\Gtm.c	   477  ** Return value:     E_OK if GTM is initialized successfully                  **
; ..\mcal_src\Gtm.c	   478  **                   E_NOT_OK if GTM is not initialized successfully          **
; ..\mcal_src\Gtm.c	   479  **                                                                            **
; ..\mcal_src\Gtm.c	   480  ** Description :     This routine initializes the GTM module.                 **
; ..\mcal_src\Gtm.c	   481  **                   This routine must be called before executing ICU / PWM / **
; ..\mcal_src\Gtm.c	   482  **                   GPT driver.                                              **
; ..\mcal_src\Gtm.c	   483  **                   The intention of this function is to make the            **
; ..\mcal_src\Gtm.c	   484  **                   configuration settings for GTM driver                    **
; ..\mcal_src\Gtm.c	   485  **                   also enable Interrupt Request for all resources that is  **
; ..\mcal_src\Gtm.c	   486  **                   used across ICU/PWM/GPT and GTM modules.                 **
; ..\mcal_src\Gtm.c	   487  **                   Clock Setting for GTM module clock and clock bus         **
; ..\mcal_src\Gtm.c	   488  **                   configuration will be done in this                       **
; ..\mcal_src\Gtm.c	   489  **                   function.                                                **
; ..\mcal_src\Gtm.c	   490  **                                                                            **
; ..\mcal_src\Gtm.c	   491  *******************************************************************************/
; ..\mcal_src\Gtm.c	   492  Std_ReturnType Gtm_Init(const Gtm_ConfigType *ConfigPtr)
; Function Gtm_Init
.L51:
Gtm_Init:	.type	func
	sub.a	a10,#64
.L298:
	st.a	[a10]56,a4
.L316:

; ..\mcal_src\Gtm.c	   493  {
; ..\mcal_src\Gtm.c	   494    Std_ReturnType GtmEnableStatus= E_NOT_OK;
; ..\mcal_src\Gtm.c	   495    const Gtm_GeneralConfigType *GeneralConfigPtr; /* Pointer to General Config */
; ..\mcal_src\Gtm.c	   496    uint32 TimeOut = GTM_MCAL_TIMEOUT_VAL;
; ..\mcal_src\Gtm.c	   497  
; ..\mcal_src\Gtm.c	   498    Gtm_kConfigPtr = ConfigPtr;
	movh.a	a15,#@his(Gtm_kConfigPtr)
	lea	a15,[a15]@los(Gtm_kConfigPtr)
	st.a	[a10]52,a15
.L395:
	st.a	[a15],a4
.L105:

; ..\mcal_src\Gtm.c	   499  
; ..\mcal_src\Gtm.c	   500    /*************************** Enable GTM *************************************/
; ..\mcal_src\Gtm.c	   501    GtmEnableStatus = Gtm_lEnableGtm();
; ..\mcal_src\Gtm.c	   502    /* Check if Initialization was successful*/
; ..\mcal_src\Gtm.c	   503    if(GtmEnableStatus == E_OK)
; ..\mcal_src\Gtm.c	   504    {
; ..\mcal_src\Gtm.c	   505      /********************* Enable GTM Global Clock ****************************/
; ..\mcal_src\Gtm.c	   506  
; ..\mcal_src\Gtm.c	   507      Gtm_lCmuClockConfigure();
; ..\mcal_src\Gtm.c	   508  
; ..\mcal_src\Gtm.c	   509      /********************* General Initialization***************** ************/
; ..\mcal_src\Gtm.c	   510      /* Get the general configuration */
; ..\mcal_src\Gtm.c	   511      GeneralConfigPtr = ConfigPtr->GtmModuleConfigPtr->GtmGeneralConfigPtr;
; ..\mcal_src\Gtm.c	   512  
; ..\mcal_src\Gtm.c	   513      GTM_SFR_INIT_USER_MODE_WRITE32((GTM_CTRL.U),\ 
; ..\mcal_src\Gtm.c	   514         ((uint32)(GeneralConfigPtr->GtmCtrlValue)));
; ..\mcal_src\Gtm.c	   515  
; ..\mcal_src\Gtm.c	   516      GTM_SFR_INIT_USER_MODE_WRITE32((GTM_IRQ_EN.U), \ 
; ..\mcal_src\Gtm.c	   517         ((uint32)((uint32)GeneralConfigPtr->GtmIrqEnable & \ 
; ..\mcal_src\Gtm.c	   518           GTM_GET_IRQ_VAL_8)));
; ..\mcal_src\Gtm.c	   519  
; ..\mcal_src\Gtm.c	   520      GTM_SFR_INIT_USER_MODE_WRITE32((GTM_IRQ_MODE.U),\ 
; ..\mcal_src\Gtm.c	   521         ((uint32)(((uint32) (GeneralConfigPtr->GtmIrqEnable)& \ 
; ..\mcal_src\Gtm.c	   522           GTM_GET_MODE_VAL_8) >> GTM_SHIFT_TO_LSB_8)));
; ..\mcal_src\Gtm.c	   523  
; ..\mcal_src\Gtm.c	   524      GTM_SFR_INIT_USER_MODE_WRITE32((GTM_EIRQ_EN.U),\ 
; ..\mcal_src\Gtm.c	   525         ((uint32)(((uint32)(GeneralConfigPtr->GtmIrqEnable) & \ 
; ..\mcal_src\Gtm.c	   526            GTM_GET_EIRQ_VAL_8) >> GTM_SHIFT_EIRQ_TO_LSB_8)));
; ..\mcal_src\Gtm.c	   527  
; ..\mcal_src\Gtm.c	   528  
; ..\mcal_src\Gtm.c	   529      /* SRN Enable */
; ..\mcal_src\Gtm.c	   530      GTM_SFR_INIT_MODIFY32((SRC_GTMAEIIRQ.U),GTM_SRC_CLEAR_MASK,\ 
; ..\mcal_src\Gtm.c	   531                              GTM_BIT_SRE_MASK)
; ..\mcal_src\Gtm.c	   532  
; ..\mcal_src\Gtm.c	   533      /****************** TIM Initialization ************************************/
; ..\mcal_src\Gtm.c	   534      Gtm_lTimConfigure();
; ..\mcal_src\Gtm.c	   535  
; ..\mcal_src\Gtm.c	   536      /****************** TOM Initialization ************************************/
; ..\mcal_src\Gtm.c	   537  
; ..\mcal_src\Gtm.c	   538      Gtm_lTomConfigure();
; ..\mcal_src\Gtm.c	   539  
; ..\mcal_src\Gtm.c	   540      /****************** PORT Initialization ***********************************/
; ..\mcal_src\Gtm.c	   541      Gtm_lPortConfig();
; ..\mcal_src\Gtm.c	   542  
; ..\mcal_src\Gtm.c	   543      /****************** TBU Initialization ************************************/
; ..\mcal_src\Gtm.c	   544      Gtm_lTbuConfig();
; ..\mcal_src\Gtm.c	   545  
; ..\mcal_src\Gtm.c	   546      /******************** ADC Connections Initialization **********************/
; ..\mcal_src\Gtm.c	   547      Gtm_lAdcConnectionsConfig();
; ..\mcal_src\Gtm.c	   548  
; ..\mcal_src\Gtm.c	   549      /******************** TTCAN Connections Initialization ********************/
; ..\mcal_src\Gtm.c	   550      Gtm_lTtcanConnectionsConfig();
; ..\mcal_src\Gtm.c	   551  
; ..\mcal_src\Gtm.c	   552      /******************** Access Enable Initialization ************************/
; ..\mcal_src\Gtm.c	   553  
; ..\mcal_src\Gtm.c	   554      GTM_SFR_INIT_RESETSAFETYENDINIT_TIMED(TimeOut);
; ..\mcal_src\Gtm.c	   555      GTM_SFR_INIT_WRITE32((GTM_ACCEN0.U),\ 
; ..\mcal_src\Gtm.c	   556                            (ConfigPtr->GtmModuleConfigPtr->GtmAccessEnable0));
; ..\mcal_src\Gtm.c	   557      GTM_SFR_INIT_SETSAFETYENDINIT_TIMED();
; ..\mcal_src\Gtm.c	   558   }
; ..\mcal_src\Gtm.c	   559  
; ..\mcal_src\Gtm.c	   560   return (GtmEnableStatus);
; ..\mcal_src\Gtm.c	   561  
; ..\mcal_src\Gtm.c	   562  }/* End of Gtm_Init */
; ..\mcal_src\Gtm.c	   563  
; ..\mcal_src\Gtm.c	   564  #if (GTM_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Gtm.c	   565  /*******************************************************************************
; ..\mcal_src\Gtm.c	   566  ** Traceability : [cover parentID = DS_NAS_HE2_GTM_PR3021,
; ..\mcal_src\Gtm.c	   567                                                          DS_NAS_EP_GTM_PR3021] **
; ..\mcal_src\Gtm.c	   568  ** Syntax : void Gtm_DeInit (void)                                            **
; ..\mcal_src\Gtm.c	   569  **                                                                            **
; ..\mcal_src\Gtm.c	   570  ** [/cover]                                                                   **
; ..\mcal_src\Gtm.c	   571  **                                                                            **
; ..\mcal_src\Gtm.c	   572  ** Service ID:       None                                                     **
; ..\mcal_src\Gtm.c	   573  **                                                                            **
; ..\mcal_src\Gtm.c	   574  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   575  **                                                                            **
; ..\mcal_src\Gtm.c	   576  ** Reentrancy:       Non Reentrant                                            **
; ..\mcal_src\Gtm.c	   577  **                                                                            **
; ..\mcal_src\Gtm.c	   578  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	   579  **                                                                            **
; ..\mcal_src\Gtm.c	   580  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   581  **                                                                            **
; ..\mcal_src\Gtm.c	   582  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	   583  **                                                                            **
; ..\mcal_src\Gtm.c	   584  ** Description :  This service shall de-initialize GTM hardware peripheral    **
; ..\mcal_src\Gtm.c	   585  ** and global variables                                                       **
; ..\mcal_src\Gtm.c	   586  **                                                                            **
; ..\mcal_src\Gtm.c	   587  *******************************************************************************/
; ..\mcal_src\Gtm.c	   588  void Gtm_DeInit(void)
; ..\mcal_src\Gtm.c	   589  {
; ..\mcal_src\Gtm.c	   590    volatile uint32  ReadBack;
; ..\mcal_src\Gtm.c	   591    /* De-Init all SRC functions*/
; ..\mcal_src\Gtm.c	   592    Gtm_lResetGtmSRCReg(); 
; ..\mcal_src\Gtm.c	   593  
; ..\mcal_src\Gtm.c	   594    /* Reset GTM kernel */
; ..\mcal_src\Gtm.c	   595    Gtm_lResetKernelDeInit();
; ..\mcal_src\Gtm.c	   596  
; ..\mcal_src\Gtm.c	   597    /* Disable GTM Module */
; ..\mcal_src\Gtm.c	   598    GTM_SFR_DEINIT_RESETENDINIT();
; ..\mcal_src\Gtm.c	   599  
; ..\mcal_src\Gtm.c	   600    GTM_SFR_DEINIT_MODIFY32((MODULE_GTM.CLC.U),GTM_CLC_CLR_MASK,GTM_ONE_U)
; ..\mcal_src\Gtm.c	   601  
; ..\mcal_src\Gtm.c	   602    /* Read back the CLC register */
; ..\mcal_src\Gtm.c	   603    ReadBack = GTM_SFR_DEINIT_USER_MODE_READ32((MODULE_GTM.CLC.U));
; ..\mcal_src\Gtm.c	   604  
; ..\mcal_src\Gtm.c	   605    GTM_SFR_DEINIT_SETENDINIT();
; ..\mcal_src\Gtm.c	   606  
; ..\mcal_src\Gtm.c	   607    /* Reset the global config pointer to NULL */
; ..\mcal_src\Gtm.c	   608    Gtm_kConfigPtr = NULL_PTR;
; ..\mcal_src\Gtm.c	   609  
; ..\mcal_src\Gtm.c	   610    UNUSED_PARAMETER(ReadBack)
; ..\mcal_src\Gtm.c	   611  
; ..\mcal_src\Gtm.c	   612  }/* End of Gtm_DeInit() */
; ..\mcal_src\Gtm.c	   613  
; ..\mcal_src\Gtm.c	   614  #endif
; ..\mcal_src\Gtm.c	   615  /* #if (GTM_DEINIT_API_ENABLE == STD_ON) */
; ..\mcal_src\Gtm.c	   616  
; ..\mcal_src\Gtm.c	   617  /*******************************************************************************
; ..\mcal_src\Gtm.c	   618  ** Traceability : [cover parentID = DS_MCAL_GTM_0403_01, DS_MCAL_GTM_0402_01] **
; ..\mcal_src\Gtm.c	   619  ** Syntax : void Gtm_IsrTomModule(uint8 ModuleNo, uint8 ChannelNumber)        **
; ..\mcal_src\Gtm.c	   620  **                                                                            **
; ..\mcal_src\Gtm.c	   621  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   622  **                                                                            **
; ..\mcal_src\Gtm.c	   623  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   624  **                                                                            **
; ..\mcal_src\Gtm.c	   625  ** Reentrancy:       reentrant                                                **
; ..\mcal_src\Gtm.c	   626  **                                                                            **
; ..\mcal_src\Gtm.c	   627  ** Parameters (in):  ModuleNo - TOM Module Number                             **
; ..\mcal_src\Gtm.c	   628  **                   ChannelNumber - Channel number (0,2,4,6,8,10,12,14)      **
; ..\mcal_src\Gtm.c	   629  **                                                                            **
; ..\mcal_src\Gtm.c	   630  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   631  **                                                                            **
; ..\mcal_src\Gtm.c	   632  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	   633  **                                                                            **
; ..\mcal_src\Gtm.c	   634  ** Description :     All TOM Interrupt sources are mapped to this function.   **
; ..\mcal_src\Gtm.c	   635  **                   On an interrupt request from TOM isr, channel number and **
; ..\mcal_src\Gtm.c	   636  **                   module number are passed as parameter. Since 2 interrupt **
; ..\mcal_src\Gtm.c	   637  **                   sources are mapped  to one Interrupt node, the parameter **
; ..\mcal_src\Gtm.c	   638  **                   to this function will be an even number only.            **
; ..\mcal_src\Gtm.c	   639  *******************************************************************************/
; ..\mcal_src\Gtm.c	   640  void Gtm_IsrTomModule(uint8 ModuleNo, uint8 ChannelNumber)
; ..\mcal_src\Gtm.c	   641  {
; ..\mcal_src\Gtm.c	   642  #ifdef GTM_TOM_MODULE_USED
; ..\mcal_src\Gtm.c	   643  
; ..\mcal_src\Gtm.c	   644    uint8 ChanIndex;          /* Maintains an index to the channel to check     */
; ..\mcal_src\Gtm.c	   645    uint8 ModChNum; /*Holds the GPT/PWM channel for ModuleNo and ChannelNumber */
; ..\mcal_src\Gtm.c	   646    uint16 NotifRegVal; /* Holds the value of IRQ NOTIF reg before clearing reg */
; ..\mcal_src\Gtm.c	   647    uint32 IrqStatus;       /* Conveys which channel has triggered the Interrupt*/
; ..\mcal_src\Gtm.c	   648    uint32 TomModuleUsage;  /*Holds the value of TOM module usage for ModuleNo*/
; ..\mcal_src\Gtm.c	   649    #if (GTM_NO_OF_TOM_CH_CONF_NOTIF > 0U)
; ..\mcal_src\Gtm.c	   650    Gtm_NotificationPtrType NotifFn;  /* Notification function */
; ..\mcal_src\Gtm.c	   651    #endif
; ..\mcal_src\Gtm.c	   652  
; ..\mcal_src\Gtm.c	   653    Ifx_GTM_TOM_CH_TYPE *ChannelPtr; /* Pointer to the TOM channel Register  */
; ..\mcal_src\Gtm.c	   654    /* Module usage configuration pointer*/
; ..\mcal_src\Gtm.c	   655    const Gtm_ModUsageConfigType *ModUsageConfigPtr;
; ..\mcal_src\Gtm.c	   656  
; ..\mcal_src\Gtm.c	   657    /* Get the GtmModUsageConfigPtr config pointer*/
; ..\mcal_src\Gtm.c	   658    ModUsageConfigPtr =(Gtm_kConfigPtr->
; ..\mcal_src\Gtm.c	   659                                    GtmModuleConfigPtr->GtmModUsageConfigPtr);
; ..\mcal_src\Gtm.c	   660    /* Get the module channel no corresponding to ModuleNo and ChannelNumber
; ..\mcal_src\Gtm.c	   661    for GTM compex driver - Notification index in array Gtm_kNotifConfig0[]
; ..\mcal_src\Gtm.c	   662    for GPT - Gpt channel no
; ..\mcal_src\Gtm.c	   663    for PWM - PWM channel no
; ..\mcal_src\Gtm.c	   664    */
; ..\mcal_src\Gtm.c	   665  
; ..\mcal_src\Gtm.c	   666    /* Get the usage information for TOM module*/
; ..\mcal_src\Gtm.c	   667    TomModuleUsage = (uint32)Gtm_kConfigPtr->GtmModuleConfigPtr->                \ 
; ..\mcal_src\Gtm.c	   668                                                    GtmTomModuleUsage[ModuleNo];
; ..\mcal_src\Gtm.c	   669  
; ..\mcal_src\Gtm.c	   670      /* Initialize ChanIndex to the number of channels mapped to one
; ..\mcal_src\Gtm.c	   671         Interrupt Node */
; ..\mcal_src\Gtm.c	   672      ChanIndex = GTM_TOM_CHAN_PER_SRN;
; ..\mcal_src\Gtm.c	   673      /* Get the Irq Group status to detect the channel that has
; ..\mcal_src\Gtm.c	   674      triggered the ISR*/
; ..\mcal_src\Gtm.c	   675      IrqStatus = Gtm_lGetTomIrqStatus(ModuleNo, ChannelNumber);
; ..\mcal_src\Gtm.c	   676      /* Loop through both the channels */
; ..\mcal_src\Gtm.c	   677      do
; ..\mcal_src\Gtm.c	   678      {
; ..\mcal_src\Gtm.c	   679        ChanIndex--;
; ..\mcal_src\Gtm.c	   680        /* Check which the channel has triggered interrupt
; ..\mcal_src\Gtm.c	   681                                           ( 2 TOM channel shares the same SRN)*/
; ..\mcal_src\Gtm.c	   682        if(Gtm_lGetIntSource(IrqStatus, ChanIndex))
; ..\mcal_src\Gtm.c	   683        {
; ..\mcal_src\Gtm.c	   684          /* Get the Channel Register Pointer */
; ..\mcal_src\Gtm.c	   685          ChannelPtr = &((*(Ifx_GTM_TOMx*)(volatile void *)(MODULE_GTM.TOM)).
; ..\mcal_src\Gtm.c	   686                                  TOM_CH[ModuleNo].CH[ChannelNumber + ChanIndex]);
; ..\mcal_src\Gtm.c	   687          /* Clear the status for receiving other interrupts */
; ..\mcal_src\Gtm.c	   688          NotifRegVal = (uint16) ChannelPtr->IRQ_NOTIFY.U;
; ..\mcal_src\Gtm.c	   689          ChannelPtr->IRQ_NOTIFY.U = GTM_CLEAR_INTERRUPT;
; ..\mcal_src\Gtm.c	   690  
; ..\mcal_src\Gtm.c	   691          ModChNum = ModUsageConfigPtr->                                         \ 
; ..\mcal_src\Gtm.c	   692                                Gtm_TomUsage[ModuleNo][ChannelNumber + ChanIndex];
; ..\mcal_src\Gtm.c	   693  
; ..\mcal_src\Gtm.c	   694          #if (GTM_NO_OF_TOM_CH_CONF_NOTIF > 0U)
; ..\mcal_src\Gtm.c	   695  
; ..\mcal_src\Gtm.c	   696          if (Gtm_lGetModuleIndex(TomModuleUsage,ChannelNumber + ChanIndex) ==   \ 
; ..\mcal_src\Gtm.c	   697                                                          GTM_TOM_DRIVER_COMPLEX)
; ..\mcal_src\Gtm.c	   698          {
; ..\mcal_src\Gtm.c	   699            /*Get notification number from Lcfg */
; ..\mcal_src\Gtm.c	   700            NotifFn = Gtm_kNotifConfig0.TomNotifUsage[ModChNum];
; ..\mcal_src\Gtm.c	   701            /* Call the notification function if configured */
; ..\mcal_src\Gtm.c	   702            NotifFn(GTM_TOM_MODULE, ModuleNo,                                \ 
; ..\mcal_src\Gtm.c	   703                                          ChannelNumber + ChanIndex,NotifRegVal);
; ..\mcal_src\Gtm.c	   704          }
; ..\mcal_src\Gtm.c	   705          #else
; ..\mcal_src\Gtm.c	   706            UNUSED_PARAMETER(NotifRegVal)
; ..\mcal_src\Gtm.c	   707          #endif
; ..\mcal_src\Gtm.c	   708  
; ..\mcal_src\Gtm.c	   709          #if (GTM_GPT_MODULE_USED == STD_ON)
; ..\mcal_src\Gtm.c	   710           if(Gtm_lGetModuleIndex(TomModuleUsage,ChannelNumber + ChanIndex) == \ 
; ..\mcal_src\Gtm.c	   711                                                                   GTM_DRIVER_GPT)
; ..\mcal_src\Gtm.c	   712           {
; ..\mcal_src\Gtm.c	   713             Gpt_Isr(ModChNum);
; ..\mcal_src\Gtm.c	   714           }
; ..\mcal_src\Gtm.c	   715          #endif
; ..\mcal_src\Gtm.c	   716          #if (GTM_PWM_MODULE_USED == STD_ON)
; ..\mcal_src\Gtm.c	   717           if(Gtm_lGetModuleIndex(TomModuleUsage,ChannelNumber + ChanIndex) ==  \ 
; ..\mcal_src\Gtm.c	   718                                                               GTM_DRIVER_PWM_MOD)
; ..\mcal_src\Gtm.c	   719           {
; ..\mcal_src\Gtm.c	   720             Pwm_17_Gtm_Isr(ModChNum);
; ..\mcal_src\Gtm.c	   721           }
; ..\mcal_src\Gtm.c	   722          #endif
; ..\mcal_src\Gtm.c	   723        }
; ..\mcal_src\Gtm.c	   724      /* Span through both the channels */
; ..\mcal_src\Gtm.c	   725      }while(ChanIndex > GTM_ZERO_U);
; ..\mcal_src\Gtm.c	   726  #else /* included to remove GNU compilation warning */
; ..\mcal_src\Gtm.c	   727    UNUSED_PARAMETER(ModuleNo)
; ..\mcal_src\Gtm.c	   728    UNUSED_PARAMETER(ChannelNumber)
; ..\mcal_src\Gtm.c	   729  #endif
; ..\mcal_src\Gtm.c	   730  /*#ifdef GTM_TOM_MODULE_USED*/
; ..\mcal_src\Gtm.c	   731  }
; ..\mcal_src\Gtm.c	   732  
; ..\mcal_src\Gtm.c	   733  /*******************************************************************************
; ..\mcal_src\Gtm.c	   734  ** Traceability : [cover parentID=DS_NAS_HE2_GTM_PR2935_1, DS_MCAL_GTM_0403_02,
; ..\mcal_src\Gtm.c	   735                                                           DS_MCAL_GTM_0402_02] **
; ..\mcal_src\Gtm.c	   736  ** Traceability : [cover parentID = DS_MCAL_GTM_0403_03, DS_MCAL_GTM_0402_03] **
; ..\mcal_src\Gtm.c	   737  ** Syntax : void Gtm_IsrTimModule(uint8 ModuleNo, uint8 ChannelNumber)        **
; ..\mcal_src\Gtm.c	   738  **                                                                            **
; ..\mcal_src\Gtm.c	   739  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   740  **                                                                            **
; ..\mcal_src\Gtm.c	   741  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   742  **                                                                            **
; ..\mcal_src\Gtm.c	   743  ** Reentrancy:       reentrant                                                **
; ..\mcal_src\Gtm.c	   744  **                                                                            **
; ..\mcal_src\Gtm.c	   745  ** Parameters (in):  ModuleNo: TIM Module Number                              **
; ..\mcal_src\Gtm.c	   746  **                   ChannelNumber - Channel number (0 to 7)                  **
; ..\mcal_src\Gtm.c	   747  **                                                                            **
; ..\mcal_src\Gtm.c	   748  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   749  **                                                                            **
; ..\mcal_src\Gtm.c	   750  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	   751  **                                                                            **
; ..\mcal_src\Gtm.c	   752  ** Description :     All TIM Interrupt sources are mapped to this function.   **
; ..\mcal_src\Gtm.c	   753  **                   On an interrupt request from TIM isr, channel number and **
; ..\mcal_src\Gtm.c	   754  **                   module number are passed as parameter. Notification      **
; ..\mcal_src\Gtm.c	   755  **                   function if configured, will be called                   **
; ..\mcal_src\Gtm.c	   756  *******************************************************************************/
; ..\mcal_src\Gtm.c	   757  void Gtm_IsrTimModule(uint8 ModuleNo, uint8 ChannelNumber)
; ..\mcal_src\Gtm.c	   758  {
; ..\mcal_src\Gtm.c	   759  #ifdef GTM_TIM_MODULE_USED
; ..\mcal_src\Gtm.c	   760  
; ..\mcal_src\Gtm.c	   761    /* Following Holds the GPT/PWM channel for ModuleNo and ChannelNumber */
; ..\mcal_src\Gtm.c	   762    uint8 ModUsageChno;
; ..\mcal_src\Gtm.c	   763    uint8 ModChNum;  /*Holds the GPT/PWM channel for ModuleNo and ChannelNumber */
; ..\mcal_src\Gtm.c	   764    uint16 NotifRegVal; /* Holds the value of IRQ NOTIF reg before clearing reg */
; ..\mcal_src\Gtm.c	   765    uint32 TimModuleUsage;  /*Holds the value of TIM module usage for ModuleNo*/
; ..\mcal_src\Gtm.c	   766    Ifx_GTM_TIM_CH_TYPE *TimChannelRegPtr; /* Pointer to TIM channel Register */
; ..\mcal_src\Gtm.c	   767    /*Module usage configuration pointer*/
; ..\mcal_src\Gtm.c	   768    const Gtm_ModUsageConfigType *ModUsageConfigPtr;
; ..\mcal_src\Gtm.c	   769    #if (GTM_NO_OF_TIM_CH_CONF_NOTIF > 0U)
; ..\mcal_src\Gtm.c	   770    Gtm_NotificationPtrType NotifFn;  /* Notification function */
; ..\mcal_src\Gtm.c	   771    #endif
; ..\mcal_src\Gtm.c	   772  
; ..\mcal_src\Gtm.c	   773    /* Check if current interrupt is valid interrupt or spurious interrupt */
; ..\mcal_src\Gtm.c	   774    if(Gtm_lGetTimIrqStatus( ModuleNo, ChannelNumber) == GTM_ONE_U)
; ..\mcal_src\Gtm.c	   775  
; ..\mcal_src\Gtm.c	   776    {
; ..\mcal_src\Gtm.c	   777      /* Get the location of TIM channel registers */
; ..\mcal_src\Gtm.c	   778      /*IFX_MISRA_RULE_11_05_STATUS=volatile in terms of pointer access.
; ..\mcal_src\Gtm.c	   779      Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	   780      TimChannelRegPtr = &((*(Ifx_GTM_TIMx*)(volatile void *)(MODULE_GTM.TIM)).\ 
; ..\mcal_src\Gtm.c	   781                                            CH_TIM[ModuleNo].CH[ChannelNumber]);
; ..\mcal_src\Gtm.c	   782  
; ..\mcal_src\Gtm.c	   783      /* Clear the Notify for receiving new interrupt requests */
; ..\mcal_src\Gtm.c	   784      NotifRegVal = (uint16) TimChannelRegPtr->CH_IRQ_NOTIFY.U;
; ..\mcal_src\Gtm.c	   785      TimChannelRegPtr->CH_IRQ_NOTIFY.U = GTM_CLEAR_TIM_INTERRUPT;
; ..\mcal_src\Gtm.c	   786    /* Get the TIM module usage*/
; ..\mcal_src\Gtm.c	   787    TimModuleUsage = (uint32)Gtm_kConfigPtr->GtmModuleConfigPtr->                \ 
; ..\mcal_src\Gtm.c	   788                                            GtmTimModuleUsage[ModuleNo];
; ..\mcal_src\Gtm.c	   789    /* Get the module usage config pointer */
; ..\mcal_src\Gtm.c	   790    ModUsageConfigPtr = (Gtm_kConfigPtr->                                        \ 
; ..\mcal_src\Gtm.c	   791                                       GtmModuleConfigPtr->GtmModUsageConfigPtr);
; ..\mcal_src\Gtm.c	   792    /* Retrieve the correspoding module ch no. i.e. for ICU - ICU channel no
; ..\mcal_src\Gtm.c	   793     and for GTM complex driver Notificaiton no*/
; ..\mcal_src\Gtm.c	   794    ModChNum = ModUsageConfigPtr->Gtm_TimUsage[ChannelNumber];
; ..\mcal_src\Gtm.c	   795    ModUsageChno = ChannelNumber;
; ..\mcal_src\Gtm.c	   796  
; ..\mcal_src\Gtm.c	   797    #if (GTM_NO_OF_TIM_CH_CONF_NOTIF > 0U)
; ..\mcal_src\Gtm.c	   798  
; ..\mcal_src\Gtm.c	   799    if(Gtm_lGetModuleIndex(TimModuleUsage ,ModUsageChno) ==                      \ 
; ..\mcal_src\Gtm.c	   800                                                          GTM_TIM_DRIVER_COMPLEX)
; ..\mcal_src\Gtm.c	   801    {
; ..\mcal_src\Gtm.c	   802      /* If interrupt has been raised for an unconfigured channel then 0xFF
; ..\mcal_src\Gtm.c	   803         will be returned */
; ..\mcal_src\Gtm.c	   804      NotifFn = Gtm_kNotifConfig0.TimNotifUsage[ModChNum];
; ..\mcal_src\Gtm.c	   805      /* Call the notification function if configured */
; ..\mcal_src\Gtm.c	   806      NotifFn(GTM_TIM_MODULE, ModuleNo, ChannelNumber, NotifRegVal);
; ..\mcal_src\Gtm.c	   807    }
; ..\mcal_src\Gtm.c	   808    #else
; ..\mcal_src\Gtm.c	   809      UNUSED_PARAMETER(NotifRegVal)
; ..\mcal_src\Gtm.c	   810    #endif
; ..\mcal_src\Gtm.c	   811  
; ..\mcal_src\Gtm.c	   812      #if (GTM_ICU_MODULE_USED == STD_ON)
; ..\mcal_src\Gtm.c	   813      /* Check of the interrupt is configured by ICU driver */
; ..\mcal_src\Gtm.c	   814      if(Gtm_lGetModuleIndex(TimModuleUsage,ModUsageChno) == GTM_DRIVER_ICU)
; ..\mcal_src\Gtm.c	   815      {
; ..\mcal_src\Gtm.c	   816        Icu_17_GtmCcu6_Isr(ModChNum);
; ..\mcal_src\Gtm.c	   817      }
; ..\mcal_src\Gtm.c	   818      #endif
; ..\mcal_src\Gtm.c	   819    }
; ..\mcal_src\Gtm.c	   820    #if (GTM_MCUSAFETY_ENABLE == STD_ON)
; ..\mcal_src\Gtm.c	   821    else
; ..\mcal_src\Gtm.c	   822    {
; ..\mcal_src\Gtm.c	   823      /* Current interrupt is spurious intterrupt */
; ..\mcal_src\Gtm.c	   824      SafeMcal_ReportError ((uint16)GTM_MCU_MODULE_ID,GTM_MCU_MODULE_INSTANCE,
; ..\mcal_src\Gtm.c	   825                       GTM_SID_TIM_ISR,GTM_E_SPURIOUS_INTRPT);
; ..\mcal_src\Gtm.c	   826    }
; ..\mcal_src\Gtm.c	   827    #endif
; ..\mcal_src\Gtm.c	   828    /*  #if (GTM_MCUSAFETY_ENABLE == STD_ON)*/
; ..\mcal_src\Gtm.c	   829    #else /* included to remove GNU compilation warning */
; ..\mcal_src\Gtm.c	   830      UNUSED_PARAMETER(ModuleNo)
; ..\mcal_src\Gtm.c	   831      UNUSED_PARAMETER(ChannelNumber)
; ..\mcal_src\Gtm.c	   832    #endif
; ..\mcal_src\Gtm.c	   833  /*#ifdef GTM_TIM_MODULE_USED*/
; ..\mcal_src\Gtm.c	   834  
; ..\mcal_src\Gtm.c	   835  }
; ..\mcal_src\Gtm.c	   836  
; ..\mcal_src\Gtm.c	   837  #if (defined(GTM_TOM_MODULE_USED))
; ..\mcal_src\Gtm.c	   838  
; ..\mcal_src\Gtm.c	   839  /*******************************************************************************
; ..\mcal_src\Gtm.c	   840  ** Syntax : IFX_LOCAL_INLINE uint32 Gtm_lGetTomIrqStatus(uint8 ModuleNo,      **
; ..\mcal_src\Gtm.c	   841  **                                                       uint8 ChannelNumber) **
; ..\mcal_src\Gtm.c	   842  **                                                                            **
; ..\mcal_src\Gtm.c	   843  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   844  **                                                                            **
; ..\mcal_src\Gtm.c	   845  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   846  **                                                                            **
; ..\mcal_src\Gtm.c	   847  ** Reentrancy:       reentrant                                                **
; ..\mcal_src\Gtm.c	   848  **                                                                            **
; ..\mcal_src\Gtm.c	   849  ** Parameters (in):  ModuleNo - TOM Module Number                             **
; ..\mcal_src\Gtm.c	   850  **                   ChannelNumber - Channel number (0,2,4,6,8,10,12,14)      **
; ..\mcal_src\Gtm.c	   851  **                                                                            **
; ..\mcal_src\Gtm.c	   852  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   853  **                                                                            **
; ..\mcal_src\Gtm.c	   854  ** Return value:     IrqStatus - Interrupt status of the channels             **
; ..\mcal_src\Gtm.c	   855  **                                                                            **
; ..\mcal_src\Gtm.c	   856  ** Description :     This function fetches the interrupt status of the channel**
; ..\mcal_src\Gtm.c	   857  **                   number and the next channel number                       **
; ..\mcal_src\Gtm.c	   858  *******************************************************************************/
; ..\mcal_src\Gtm.c	   859  IFX_LOCAL_INLINE uint32 Gtm_lGetTomIrqStatus(uint8 ModuleNo,uint8 ChannelNumber)
; ..\mcal_src\Gtm.c	   860  {
; ..\mcal_src\Gtm.c	   861    volatile uint32 RegVal;
; ..\mcal_src\Gtm.c	   862    uint32 IrqStatus;
; ..\mcal_src\Gtm.c	   863    uint8 RegPos;
; ..\mcal_src\Gtm.c	   864  
; ..\mcal_src\Gtm.c	   865    /* Ascertain the position of Interrupt status for the corresponding module
; ..\mcal_src\Gtm.c	   866       and channel*/
; ..\mcal_src\Gtm.c	   867    RegPos = ((ModuleNo % GTM_TOM_MODULES_IN_ICM_REG) * \ 
; ..\mcal_src\Gtm.c	   868                                     GTM_CHANNELS_PER_TOM_MODULE) + ChannelNumber;
; ..\mcal_src\Gtm.c	   869  
; ..\mcal_src\Gtm.c	   870    /* Identify the ICM register corresponding to the Module number */
; ..\mcal_src\Gtm.c	   871    /* Identify the ICM register corresponding to the Module number */
; ..\mcal_src\Gtm.c	   872    /* MISRA Rule Violation 11.4 and 17.4 Pointer to Pointer conversion*/
; ..\mcal_src\Gtm.c	   873    /* Pointer to pointer conversion is performed to simplify the code. Since
; ..\mcal_src\Gtm.c	   874       each of the ICM register has a different name, to make this available in a
; ..\mcal_src\Gtm.c	   875       single functions, this conversion is performed. This pointer arithmetic
; ..\mcal_src\Gtm.c	   876       is desired */
; ..\mcal_src\Gtm.c	   877  
; ..\mcal_src\Gtm.c	   878  
; ..\mcal_src\Gtm.c	   879      /*IFX_MISRA_RULE_11_05_STATUS=volatile in terms of pointer access.
; ..\mcal_src\Gtm.c	   880      Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	   881    /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic is used to efficiently 
; ..\mcal_src\Gtm.c	   882     access the SFRs of multiple GTM ICM registers.*/
; ..\mcal_src\Gtm.c	   883    RegVal = *(((uint32*)(volatile void *)GTM_TOM_ICM_BASE_ADDRESS) + \ 
; ..\mcal_src\Gtm.c	   884                                           (ModuleNo/GTM_TOM_MODULES_IN_ICM_REG));
; ..\mcal_src\Gtm.c	   885  
; ..\mcal_src\Gtm.c	   886  
; ..\mcal_src\Gtm.c	   887    /* Retrieve the IRQ status of the channel number and the next channel */
; ..\mcal_src\Gtm.c	   888    IrqStatus = (RegVal & (GTM_GET_INT_STATUS << RegPos)) >> (RegPos);
; ..\mcal_src\Gtm.c	   889    return(IrqStatus);
; ..\mcal_src\Gtm.c	   890  }
; ..\mcal_src\Gtm.c	   891  
; ..\mcal_src\Gtm.c	   892  #endif
; ..\mcal_src\Gtm.c	   893  /*  for #if (defined(GTM_TOM_MODULE_USED))   */
; ..\mcal_src\Gtm.c	   894  
; ..\mcal_src\Gtm.c	   895  /*******************************************************************************
; ..\mcal_src\Gtm.c	   896  ** Syntax : void Gtm_lCmuConfClkConfigure(void)                               **
; ..\mcal_src\Gtm.c	   897  **                                                                            **
; ..\mcal_src\Gtm.c	   898  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   899  **                                                                            **
; ..\mcal_src\Gtm.c	   900  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   901  **                                                                            **
; ..\mcal_src\Gtm.c	   902  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	   903  **                                                                            **
; ..\mcal_src\Gtm.c	   904  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	   905  **                                                                            **
; ..\mcal_src\Gtm.c	   906  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   907  **                                                                            **
; ..\mcal_src\Gtm.c	   908  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	   909  **                                                                            **
; ..\mcal_src\Gtm.c	   910  ** Description :     Function to initialize CMU module                        **
; ..\mcal_src\Gtm.c	   911  *******************************************************************************/
; ..\mcal_src\Gtm.c	   912  IFX_LOCAL_INLINE void  Gtm_lCmuConfClkConfigure(void)
; ..\mcal_src\Gtm.c	   913  {
; ..\mcal_src\Gtm.c	   914    const Gtm_ClockSettingType *ClockSettingPtr;   /* Pointer to Clock Settings */
; ..\mcal_src\Gtm.c	   915    uint8  Count;             /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	   916  
; ..\mcal_src\Gtm.c	   917  
; ..\mcal_src\Gtm.c	   918  
; ..\mcal_src\Gtm.c	   919    /* Get Clock Configuration */
; ..\mcal_src\Gtm.c	   920    ClockSettingPtr = Gtm_kConfigPtr->GtmClockSettingPtr;
; ..\mcal_src\Gtm.c	   921  
; ..\mcal_src\Gtm.c	   922  
; ..\mcal_src\Gtm.c	   923    /* Configure all clocks */
; ..\mcal_src\Gtm.c	   924    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_CLOCKS; Count++)
; ..\mcal_src\Gtm.c	   925    {
; ..\mcal_src\Gtm.c	   926      if(ClockSettingPtr->GtmClockEnable & \ 
; ..\mcal_src\Gtm.c	   927                   (uint32)(GTM_CMU_CLK_ENABLE << (Count * GTM_CMU_BITS_PER_CLK)))
; ..\mcal_src\Gtm.c	   928      {
; ..\mcal_src\Gtm.c	   929        /* The clock structure provided by Sfr will cause increase in code size.
; ..\mcal_src\Gtm.c	   930           Hence a type Gtm_CmuType has been created and used to provide an
; ..\mcal_src\Gtm.c	   931           array approach for CMU clocks */
; ..\mcal_src\Gtm.c	   932  
; ..\mcal_src\Gtm.c	   933  
; ..\mcal_src\Gtm.c	   934        /* The CMU structure as per SFR does not support for arrayed approach. To
; ..\mcal_src\Gtm.c	   935           reduce the code size, a new type Gtm_CmuType is introduced and is
; ..\mcal_src\Gtm.c	   936           typecasted in this case */
; ..\mcal_src\Gtm.c	   937        /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	   938           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	   939        GTM_SFR_INIT_USER_MODE_WRITE32((((Gtm_CmuType*)(void*)\ 
; ..\mcal_src\Gtm.c	   940             &(MODULE_GTM.CMU))->ClkCtrl[Count].CTRL.U),\ 
; ..\mcal_src\Gtm.c	   941             (ClockSettingPtr->GtmCmuClkCnt[Count]));
; ..\mcal_src\Gtm.c	   942  
; ..\mcal_src\Gtm.c	   943      }
; ..\mcal_src\Gtm.c	   944  
; ..\mcal_src\Gtm.c	   945    }
; ..\mcal_src\Gtm.c	   946  }
; ..\mcal_src\Gtm.c	   947  
; ..\mcal_src\Gtm.c	   948  /*******************************************************************************
; ..\mcal_src\Gtm.c	   949  ** Syntax : void Gtm_lCmuExtClkConfigure(void)                                **
; ..\mcal_src\Gtm.c	   950  **                                                                            **
; ..\mcal_src\Gtm.c	   951  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	   952  **                                                                            **
; ..\mcal_src\Gtm.c	   953  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	   954  **                                                                            **
; ..\mcal_src\Gtm.c	   955  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	   956  **                                                                            **
; ..\mcal_src\Gtm.c	   957  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	   958  **                                                                            **
; ..\mcal_src\Gtm.c	   959  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	   960  **                                                                            **
; ..\mcal_src\Gtm.c	   961  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	   962  **                                                                            **
; ..\mcal_src\Gtm.c	   963  ** Description :     Function to initialize CMU module                        **
; ..\mcal_src\Gtm.c	   964  *******************************************************************************/
; ..\mcal_src\Gtm.c	   965  IFX_LOCAL_INLINE void Gtm_lCmuExtClkConfigure(void)
; ..\mcal_src\Gtm.c	   966  {
; ..\mcal_src\Gtm.c	   967    const Gtm_ClockSettingType *ClockSettingPtr;   /* Pointer to Clock Settings */
; ..\mcal_src\Gtm.c	   968    uint8  Count;             /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	   969    uint32 Regval;
; ..\mcal_src\Gtm.c	   970  
; ..\mcal_src\Gtm.c	   971    /* Get Clock Configuration */
; ..\mcal_src\Gtm.c	   972    ClockSettingPtr = Gtm_kConfigPtr->GtmClockSettingPtr;
; ..\mcal_src\Gtm.c	   973  
; ..\mcal_src\Gtm.c	   974    /* Configure External Clocks         */
; ..\mcal_src\Gtm.c	   975    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_EXT_CLOCKS; Count++)
; ..\mcal_src\Gtm.c	   976    {
; ..\mcal_src\Gtm.c	   977      if(ClockSettingPtr->GtmClockEnable & \ 
; ..\mcal_src\Gtm.c	   978        (uint32)(GTM_CMU_CLK_ENABLE <<((Count * GTM_CMU_BITS_PER_CLK) + \ 
; ..\mcal_src\Gtm.c	   979        GTM_CMU_START_FROM_EXTCLK)))
; ..\mcal_src\Gtm.c	   980      {
; ..\mcal_src\Gtm.c	   981        /* The clock structure provided by Sfr will cause increase in code size.
; ..\mcal_src\Gtm.c	   982           Hence a type Gtm_CmuType has been created and used to provide an
; ..\mcal_src\Gtm.c	   983           array approach for CMU clocks */
; ..\mcal_src\Gtm.c	   984        /* MISRA rule violation 11.4 and 1.2 */
; ..\mcal_src\Gtm.c	   985  
; ..\mcal_src\Gtm.c	   986        /* The CMU structure as per SFR does not support for arrayed approach. To
; ..\mcal_src\Gtm.c	   987           reduce the code size, a new type Gtm_CmuType is introduced and is
; ..\mcal_src\Gtm.c	   988           typecasted in this case */
; ..\mcal_src\Gtm.c	   989  
; ..\mcal_src\Gtm.c	   990        /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	   991           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	   992        GTM_SFR_INIT_USER_MODE_WRITE32((\ 
; ..\mcal_src\Gtm.c	   993        ((Gtm_CmuType*)(void*)&(MODULE_GTM.CMU))->CmuEclk[Count].CmuEclkNum.U ),\ 
; ..\mcal_src\Gtm.c	   994                                ClockSettingPtr->GtmEclk[Count].CmuEclkNum);
; ..\mcal_src\Gtm.c	   995        /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	   996           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	   997        GTM_SFR_INIT_USER_MODE_WRITE32((\ 
; ..\mcal_src\Gtm.c	   998        ((Gtm_CmuType*)(void*)&(MODULE_GTM.CMU))->CmuEclk[Count].CmuEclkNum.U),\ 
; ..\mcal_src\Gtm.c	   999                                ClockSettingPtr->GtmEclk[Count].CmuEclkNum);
; ..\mcal_src\Gtm.c	  1000        /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	  1001           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	  1002        GTM_SFR_INIT_USER_MODE_WRITE32((\ 
; ..\mcal_src\Gtm.c	  1003        ((Gtm_CmuType*)(void*)&(MODULE_GTM.CMU))->CmuEclk[Count].CmuEclkDen.U),\ 
; ..\mcal_src\Gtm.c	  1004                                ClockSettingPtr->GtmEclk[Count].CmuEclkDen);
; ..\mcal_src\Gtm.c	  1005  
; ..\mcal_src\Gtm.c	  1006  
; ..\mcal_src\Gtm.c	  1007      }
; ..\mcal_src\Gtm.c	  1008    }
; ..\mcal_src\Gtm.c	  1009    
; ..\mcal_src\Gtm.c	  1010    Regval = ((uint32)(ClockSettingPtr->GtmFxdClkControl) & \ 
; ..\mcal_src\Gtm.c	  1011            GTM_GET_FIXED_CLK_VAL);
; ..\mcal_src\Gtm.c	  1012    
; ..\mcal_src\Gtm.c	  1013    /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	  1014           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	  1015    GTM_SFR_INIT_USER_MODE_WRITE32((\ 
; ..\mcal_src\Gtm.c	  1016    ((Gtm_CmuType*)(void*)&(MODULE_GTM.CMU))->GtmFxdClkControl),Regval);
; ..\mcal_src\Gtm.c	  1017  }
; ..\mcal_src\Gtm.c	  1018  
; ..\mcal_src\Gtm.c	  1019  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1020  ** Syntax : void Gtm_lCmuClockConfigure(void)                                 **
; ..\mcal_src\Gtm.c	  1021  **                                                                            **
; ..\mcal_src\Gtm.c	  1022  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1023  **                                                                            **
; ..\mcal_src\Gtm.c	  1024  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1025  **                                                                            **
; ..\mcal_src\Gtm.c	  1026  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1027  **                                                                            **
; ..\mcal_src\Gtm.c	  1028  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1029  **                                                                            **
; ..\mcal_src\Gtm.c	  1030  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1031  **                                                                            **
; ..\mcal_src\Gtm.c	  1032  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1033  **                                                                            **
; ..\mcal_src\Gtm.c	  1034  ** Description :     Function to initialize CMU module                        **
; ..\mcal_src\Gtm.c	  1035  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1036  IFX_LOCAL_INLINE void Gtm_lCmuClockConfigure(void)
; ..\mcal_src\Gtm.c	  1037  {
; ..\mcal_src\Gtm.c	  1038    const Gtm_ClockSettingType *ClockSettingPtr;   /* Pointer to Clock Settings */
; ..\mcal_src\Gtm.c	  1039    uint32 RegTemp;           /* Temporary variable to store the register value */
; ..\mcal_src\Gtm.c	  1040    uint32 RegTemp1;          /* Temporary variable to store the register value */
; ..\mcal_src\Gtm.c	  1041    uint32 RegTemp3;          /* Temporary variable to store the register value */
; ..\mcal_src\Gtm.c	  1042    uint32 RegTemp4;          /* Temporary variable to store the register value */
; ..\mcal_src\Gtm.c	  1043    uint8  Count;             /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	  1044    uint8  EnableClock;       /* To Indicate if clock need to be enabled        */
; ..\mcal_src\Gtm.c	  1045  
; ..\mcal_src\Gtm.c	  1046    /* Get Clock Configuration */
; ..\mcal_src\Gtm.c	  1047    ClockSettingPtr = Gtm_kConfigPtr->GtmClockSettingPtr;
; ..\mcal_src\Gtm.c	  1048  
; ..\mcal_src\Gtm.c	  1049    /* Check if any new value configured for Numerator or Denominator of the
; ..\mcal_src\Gtm.c	  1050       Global Clock */
; ..\mcal_src\Gtm.c	  1051  
; ..\mcal_src\Gtm.c	  1052    /* The content of Numerator and Denominator are temporarily taken in local
; ..\mcal_src\Gtm.c	  1053       variables and used in the if statement below for Misra reasons.          */
; ..\mcal_src\Gtm.c	  1054    RegTemp3 = GTM_SFR_INIT_USER_MODE_READ32(GTM_CMU_GCLK_NUM.U);
; ..\mcal_src\Gtm.c	  1055    RegTemp4 = GTM_SFR_INIT_USER_MODE_READ32(GTM_CMU_GCLK_DEN.U);
; ..\mcal_src\Gtm.c	  1056    RegTemp1 = GTM_ZERO_UL;
; ..\mcal_src\Gtm.c	  1057    /* Indicate that Clock has been enabled prior */
; ..\mcal_src\Gtm.c	  1058    EnableClock = GTM_ZERO_U;
; ..\mcal_src\Gtm.c	  1059  
; ..\mcal_src\Gtm.c	  1060    if( (RegTemp3 != Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkNum) ||          \ 
; ..\mcal_src\Gtm.c	  1061        (RegTemp4 != Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkDen))
; ..\mcal_src\Gtm.c	  1062    {
; ..\mcal_src\Gtm.c	  1063      RegTemp = GTM_SFR_INIT_USER_MODE_READ32(GTM_CMU_CLK_EN.U);
; ..\mcal_src\Gtm.c	  1064  
; ..\mcal_src\Gtm.c	  1065      /* Clock dividers can be initialized only when the corresponding clocks are
; ..\mcal_src\Gtm.c	  1066         disabled */
; ..\mcal_src\Gtm.c	  1067      GTM_SFR_INIT_USER_MODE_WRITE32(GTM_CMU_CLK_EN.U,GTM_DISABLE_ALL_CLOCKS);
; ..\mcal_src\Gtm.c	  1068      /* Indicate that Clock has been disabled and need to be enabled */
; ..\mcal_src\Gtm.c	  1069      EnableClock = GTM_ONE_U;
; ..\mcal_src\Gtm.c	  1070      /* For reliable clock setting, it is recommended to have 2 initializations
; ..\mcal_src\Gtm.c	  1071         of Numerator followed by a single initialization of Denominator        */
; ..\mcal_src\Gtm.c	  1072      GTM_SFR_INIT_USER_MODE_WRITE32(GTM_CMU_GCLK_NUM.U ,\ 
; ..\mcal_src\Gtm.c	  1073                          Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkNum);
; ..\mcal_src\Gtm.c	  1074      GTM_SFR_INIT_USER_MODE_WRITE32(GTM_CMU_GCLK_NUM.U ,\ 
; ..\mcal_src\Gtm.c	  1075                          Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkNum);
; ..\mcal_src\Gtm.c	  1076      GTM_SFR_INIT_USER_MODE_WRITE32(GTM_CMU_GCLK_DEN.U ,\ 
; ..\mcal_src\Gtm.c	  1077                          Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkDen);
; ..\mcal_src\Gtm.c	  1078      /* Reenable the Clocks which were enabled earlier. A valid clock enable will
; ..\mcal_src\Gtm.c	  1079         be read as 11b. For enabling, 10b has to be written, hence the
; ..\mcal_src\Gtm.c	  1080         conversion is performed as mentioned below */
; ..\mcal_src\Gtm.c	  1081      for(Count = GTM_ZERO_U; Count < GTM_CMU_TOTAL_CLOCKS; Count++)
; ..\mcal_src\Gtm.c	  1082      {
; ..\mcal_src\Gtm.c	  1083        if(( RegTemp & \ 
; ..\mcal_src\Gtm.c	  1084                    (GTM_CMU_REG_CLK_ENABLE << (Count * GTM_CMU_BITS_PER_CLK)) ) )
; ..\mcal_src\Gtm.c	  1085        {
; ..\mcal_src\Gtm.c	  1086          RegTemp1 |= (GTM_ENABLE_CLK << (Count * GTM_CMU_BITS_PER_CLK));
; ..\mcal_src\Gtm.c	  1087        }
; ..\mcal_src\Gtm.c	  1088      }
; ..\mcal_src\Gtm.c	  1089    }
; ..\mcal_src\Gtm.c	  1090  
; ..\mcal_src\Gtm.c	  1091    if(ClockSettingPtr != NULL_PTR)      /* Configure CMU clocks if configured  */
; ..\mcal_src\Gtm.c	  1092    {
; ..\mcal_src\Gtm.c	  1093      /* If clock is enabled, then it needs to be disabled for this activity */
; ..\mcal_src\Gtm.c	  1094      if(EnableClock == GTM_ZERO_U)
; ..\mcal_src\Gtm.c	  1095      {
; ..\mcal_src\Gtm.c	  1096        /* Disable all clock enables  */
; ..\mcal_src\Gtm.c	  1097        GTM_SFR_INIT_USER_MODE_WRITE32(GTM_CMU_CLK_EN.U,GTM_DISABLE_ALL_CLOCKS);
; ..\mcal_src\Gtm.c	  1098      }
; ..\mcal_src\Gtm.c	  1099  
; ..\mcal_src\Gtm.c	  1100      /* Invoke the functions to configure Configurable and External Clocks */
; ..\mcal_src\Gtm.c	  1101      Gtm_lCmuConfClkConfigure();
; ..\mcal_src\Gtm.c	  1102      Gtm_lCmuExtClkConfigure();
; ..\mcal_src\Gtm.c	  1103  
; ..\mcal_src\Gtm.c	  1104  
; ..\mcal_src\Gtm.c	  1105      /* Enable clocks */
; ..\mcal_src\Gtm.c	  1106      GTM_SFR_INIT_USER_MODE_WRITE32(GTM_CMU_CLK_EN.U ,(unsigned_int)\ 
; ..\mcal_src\Gtm.c	  1107                                  (RegTemp1 |(ClockSettingPtr->GtmClockEnable)));
; ..\mcal_src\Gtm.c	  1108      /* Indicate that Clock has been enabled and need not be enabled again */
; ..\mcal_src\Gtm.c	  1109      EnableClock = GTM_ZERO_U;
; ..\mcal_src\Gtm.c	  1110    }
; ..\mcal_src\Gtm.c	  1111    if(EnableClock == GTM_ONE_U)
; ..\mcal_src\Gtm.c	  1112    {
; ..\mcal_src\Gtm.c	  1113      GTM_SFR_INIT_USER_MODE_WRITE32(GTM_CMU_CLK_EN.U ,(uint32)RegTemp1);
; ..\mcal_src\Gtm.c	  1114    }
; ..\mcal_src\Gtm.c	  1115  }
; ..\mcal_src\Gtm.c	  1116  
; ..\mcal_src\Gtm.c	  1117  
; ..\mcal_src\Gtm.c	  1118  
; ..\mcal_src\Gtm.c	  1119  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1120  ** Syntax : void Gtm_lSaveTgcStatus(Ifx_GTM_TOM_TGC_TYPE *TomTgcRegPtr)       **
; ..\mcal_src\Gtm.c	  1121  **                                                                            **
; ..\mcal_src\Gtm.c	  1122  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1123  **                                                                            **
; ..\mcal_src\Gtm.c	  1124  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1125  **                                                                            **
; ..\mcal_src\Gtm.c	  1126  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1127  **                                                                            **
; ..\mcal_src\Gtm.c	  1128  ** Parameters (in):  TomTgcRegPtr - Pointer to TGC Configuration              **
; ..\mcal_src\Gtm.c	  1129  **                                                                            **
; ..\mcal_src\Gtm.c	  1130  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1131  **                                                                            **
; ..\mcal_src\Gtm.c	  1132  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1133  **                                                                            **
; ..\mcal_src\Gtm.c	  1134  ** Description :     Function to Save TGC information                         **
; ..\mcal_src\Gtm.c	  1135  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1136  IFX_LOCAL_INLINE void Gtm_lSaveTgcStatus(Ifx_GTM_TOM_TGC_TYPE *TomTgcRegPtr)
; ..\mcal_src\Gtm.c	  1137  {
; ..\mcal_src\Gtm.c	  1138    uint8 MinorCnt;
; ..\mcal_src\Gtm.c	  1139  
; ..\mcal_src\Gtm.c	  1140    for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_SIXTEEN_U; MinorCnt++)
; ..\mcal_src\Gtm.c	  1141    {
; ..\mcal_src\Gtm.c	  1142      if((GTM_SFR_INIT_USER_MODE_READ32(TomTgcRegPtr->ENDIS_STAT.U) &  \ 
; ..\mcal_src\Gtm.c	  1143      (GTM_TOM_CHAN_ENABLE << (MinorCnt * GTM_TOM_BITS_PER_CHAN))) > GTM_ZERO_UL)
; ..\mcal_src\Gtm.c	  1144      {
; ..\mcal_src\Gtm.c	  1145        GTM_SFR_INIT_USER_MODE_MODIFY32((TomTgcRegPtr->ENDIS_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1146           GTM_GET_LAST_16BITS,((uint32)\ 
; ..\mcal_src\Gtm.c	  1147           (uint32)GTM_TWO_U << (MinorCnt * GTM_TOM_BITS_PER_CHAN)))
; ..\mcal_src\Gtm.c	  1148  
; ..\mcal_src\Gtm.c	  1149      }
; ..\mcal_src\Gtm.c	  1150      if((GTM_SFR_INIT_USER_MODE_READ32(TomTgcRegPtr->OUTEN_STAT.U) &
; ..\mcal_src\Gtm.c	  1151         (GTM_TOM_CHAN_ENABLE<<(MinorCnt * GTM_TOM_BITS_PER_CHAN))) > GTM_ZERO_UL)
; ..\mcal_src\Gtm.c	  1152      {
; ..\mcal_src\Gtm.c	  1153        GTM_SFR_INIT_USER_MODE_MODIFY32(TomTgcRegPtr->OUTEN_CTRL.U ,
; ..\mcal_src\Gtm.c	  1154             GTM_GET_LAST_16BITS,((uint32)\ 
; ..\mcal_src\Gtm.c	  1155             ((uint32)GTM_TWO_U << (MinorCnt * GTM_TOM_BITS_PER_CHAN))))
; ..\mcal_src\Gtm.c	  1156      }
; ..\mcal_src\Gtm.c	  1157    }
; ..\mcal_src\Gtm.c	  1158  }
; ..\mcal_src\Gtm.c	  1159  
; ..\mcal_src\Gtm.c	  1160  
; ..\mcal_src\Gtm.c	  1161  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1162  ** Syntax : void Gtm_lTomHostTrigger(void)                                    **
; ..\mcal_src\Gtm.c	  1163  **                                                                            **
; ..\mcal_src\Gtm.c	  1164  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1165  **                                                                            **
; ..\mcal_src\Gtm.c	  1166  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1167  **                                                                            **
; ..\mcal_src\Gtm.c	  1168  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1169  **                                                                            **
; ..\mcal_src\Gtm.c	  1170  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1171  **                                                                            **
; ..\mcal_src\Gtm.c	  1172  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1173  **                                                                            **
; ..\mcal_src\Gtm.c	  1174  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1175  **                                                                            **
; ..\mcal_src\Gtm.c	  1176  ** Description :     Function to provide TOM Host Trigger                     **
; ..\mcal_src\Gtm.c	  1177  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1178  IFX_LOCAL_INLINE void Gtm_lTomHostTrigger(void)
; ..\mcal_src\Gtm.c	  1179  {
; ..\mcal_src\Gtm.c	  1180    Ifx_GTM_TOM_TGC_TYPE *TomTgcRegPtr;            /* Pointer to TOM TGC Reg    */
; ..\mcal_src\Gtm.c	  1181    uint8 MajorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	  1182    uint8 MinorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	  1183  
; ..\mcal_src\Gtm.c	  1184  
; ..\mcal_src\Gtm.c	  1185    /* Check the Fupd status and invoke a Host Trigger to set the clocks        */
; ..\mcal_src\Gtm.c	  1186    /* MajorCnt - Count to maintain TOM Module Count            */
; ..\mcal_src\Gtm.c	  1187    /* MinorCnt - Count to maintain TGC Count                   */
; ..\mcal_src\Gtm.c	  1188    for(MajorCnt = GTM_ZERO_U; MajorCnt < GTM_NO_OF_TOM_MODULES; MajorCnt++)
; ..\mcal_src\Gtm.c	  1189    {
; ..\mcal_src\Gtm.c	  1190      for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_NO_OF_TGC_PER_MODULE;MinorCnt++)
; ..\mcal_src\Gtm.c	  1191      {
; ..\mcal_src\Gtm.c	  1192        TomTgcRegPtr = &(((*(Ifx_GTM_TOMx*)(volatile void *)(MODULE_GTM.TOM)).
; ..\mcal_src\Gtm.c	  1193                                  TOM_TGC[MajorCnt].TGC[MinorCnt]));
; ..\mcal_src\Gtm.c	  1194  
; ..\mcal_src\Gtm.c	  1195        if((GTM_SFR_INIT_USER_MODE_READ32(TomTgcRegPtr->FUPD_CTRL.U) & \ 
; ..\mcal_src\Gtm.c	  1196                 GTM_TOM_TGC_GET_FUPD) > GTM_ZERO_UL)
; ..\mcal_src\Gtm.c	  1197        {
; ..\mcal_src\Gtm.c	  1198          /* Invoke an Host Trigger */
; ..\mcal_src\Gtm.c	  1199          GTM_SFR_INIT_USER_MODE_MODIFY32(TomTgcRegPtr->GLB_CTRL.U,\ 
; ..\mcal_src\Gtm.c	  1200              GTM_GLB_CTRL_CLR_MASK,GTM_BIT_SET)
; ..\mcal_src\Gtm.c	  1201        }
; ..\mcal_src\Gtm.c	  1202      }
; ..\mcal_src\Gtm.c	  1203    }
; ..\mcal_src\Gtm.c	  1204  }
; ..\mcal_src\Gtm.c	  1205  
; ..\mcal_src\Gtm.c	  1206  
; ..\mcal_src\Gtm.c	  1207  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1208  ** Syntax : void Gtm_lTomTgcConfigure(void)                                   **
; ..\mcal_src\Gtm.c	  1209  **                                                                            **
; ..\mcal_src\Gtm.c	  1210  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1211  **                                                                            **
; ..\mcal_src\Gtm.c	  1212  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1213  **                                                                            **
; ..\mcal_src\Gtm.c	  1214  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1215  **                                                                            **
; ..\mcal_src\Gtm.c	  1216  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1217  **                                                                            **
; ..\mcal_src\Gtm.c	  1218  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1219  **                                                                            **
; ..\mcal_src\Gtm.c	  1220  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1221  **                                                                            **
; ..\mcal_src\Gtm.c	  1222  ** Description :     Function to initialize TGC's of TOM                      **
; ..\mcal_src\Gtm.c	  1223  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1224  IFX_LOCAL_INLINE void Gtm_lTomTgcConfigure(void)
; ..\mcal_src\Gtm.c	  1225  {
; ..\mcal_src\Gtm.c	  1226    const Gtm_TomTgcConfigType *TomConfigPtr;      /* Pointer to TOM Config     */
; ..\mcal_src\Gtm.c	  1227    Ifx_GTM_TOM_TGC_TYPE* TomTgcRegPtr;            /* Pointer to TOM TGC Reg    */
; ..\mcal_src\Gtm.c	  1228    uint8 TomCnt;             /* Variable to TOM Channel Initialization Count   */
; ..\mcal_src\Gtm.c	  1229    uint8 MajorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	  1230    uint8 MinorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	  1231    uint8 ModuleNo;           /* Variable to hold Module Number                 */
; ..\mcal_src\Gtm.c	  1232    uint8 TgcNumber;          /* Variable to hold TGC Number                    */
; ..\mcal_src\Gtm.c	  1233    volatile uint32 MajorCountLoopLimit = (((GTM_NO_OF_TOM_MODULES * \ 
; ..\mcal_src\Gtm.c	  1234                         GTM_NO_OF_TGC_PER_MODULE)/ GTM_BITS_IN_U32) + GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1235    /* Count to maintain track of the index in TOM TGC Config      */
; ..\mcal_src\Gtm.c	  1236    TomCnt = GTM_ZERO_U;
; ..\mcal_src\Gtm.c	  1237  
; ..\mcal_src\Gtm.c	  1238    /* MajorCnt -Count to maintain an index to the GtmTomTgcUsage Array         */
; ..\mcal_src\Gtm.c	  1239    /* MinorCnt -Count to maintain an index to the channels in
; ..\mcal_src\Gtm.c	  1240       GtmTomTgcUsage Array*/
; ..\mcal_src\Gtm.c	  1241    for(MajorCnt = GTM_ZERO_U; MajorCnt < MajorCountLoopLimit; MajorCnt++)
; ..\mcal_src\Gtm.c	  1242    {
; ..\mcal_src\Gtm.c	  1243      for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_BITS_IN_U8 ; MinorCnt++)
; ..\mcal_src\Gtm.c	  1244      {
; ..\mcal_src\Gtm.c	  1245        /* Check if the TGC is configured */
; ..\mcal_src\Gtm.c	  1246        if((Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomTgcUsage[MajorCnt] &
; ..\mcal_src\Gtm.c	  1247           (GTM_BIT_SET << MinorCnt)) > GTM_ZERO_UL)
; ..\mcal_src\Gtm.c	  1248        {
; ..\mcal_src\Gtm.c	  1249          /* Extract Module Number and Channel Number from the Loop Count       */
; ..\mcal_src\Gtm.c	  1250          ModuleNo = ((MajorCnt * GTM_BITS_IN_U32) + MinorCnt)/
; ..\mcal_src\Gtm.c	  1251                       GTM_NO_OF_TGC_PER_MODULE;
; ..\mcal_src\Gtm.c	  1252          TgcNumber = (MinorCnt) % GTM_NO_OF_TGC_PER_MODULE;
; ..\mcal_src\Gtm.c	  1253  
; ..\mcal_src\Gtm.c	  1254          /* MISRA Rule Violation 17.4
; ..\mcal_src\Gtm.c	  1255             Pointer arithmetic other than array indexing used
; ..\mcal_src\Gtm.c	  1256             This cannot be avoided because of Post Build structure */
; ..\mcal_src\Gtm.c	  1257  
; ..\mcal_src\Gtm.c	  1258          /* Get the pointer to the TGC configuration */
; ..\mcal_src\Gtm.c	  1259          /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic used due to 
; ..\mcal_src\Gtm.c	  1260       PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Gtm.c	  1261          TomConfigPtr =                                                         \ 
; ..\mcal_src\Gtm.c	  1262                &(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomTgcConfigPtr[TomCnt]);
; ..\mcal_src\Gtm.c	  1263  
; ..\mcal_src\Gtm.c	  1264          TomCnt++;
; ..\mcal_src\Gtm.c	  1265  
; ..\mcal_src\Gtm.c	  1266          /* Get the Pointer to the TGC registers */
; ..\mcal_src\Gtm.c	  1267          TomTgcRegPtr = &((*(Ifx_GTM_TOMx*)(volatile void *)
; ..\mcal_src\Gtm.c	  1268                             (MODULE_GTM.TOM)).TOM_TGC[ModuleNo].TGC[TgcNumber]);
; ..\mcal_src\Gtm.c	  1269  
; ..\mcal_src\Gtm.c	  1270  
; ..\mcal_src\Gtm.c	  1271          GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->ACT_TB.U),\ 
; ..\mcal_src\Gtm.c	  1272                             TomConfigPtr->GtmTomActTb);
; ..\mcal_src\Gtm.c	  1273          GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->INT_TRIG.U),\ 
; ..\mcal_src\Gtm.c	  1274                             (uint32)(TomConfigPtr->GtmTomIntTrig));
; ..\mcal_src\Gtm.c	  1275  
; ..\mcal_src\Gtm.c	  1276          /* Check if the TGC is configured ie. if the channels for TGC is
; ..\mcal_src\Gtm.c	  1277             configured for Complex usage */
; ..\mcal_src\Gtm.c	  1278          if(TomConfigPtr->GtmTomTgcConfigGrpPtr != NULL_PTR)
; ..\mcal_src\Gtm.c	  1279          {
; ..\mcal_src\Gtm.c	  1280            GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->FUPD_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1281                             TomConfigPtr->GtmTomTgcConfigGrpPtr->GtmTomFupd);
; ..\mcal_src\Gtm.c	  1282            GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->OUTEN_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1283              (uint32)(TomConfigPtr->GtmTomTgcConfigGrpPtr->GtmTomOutenCtrl));
; ..\mcal_src\Gtm.c	  1284            GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->OUTEN_STAT.U),\ 
; ..\mcal_src\Gtm.c	  1285                          (TomConfigPtr->GtmTomTgcConfigGrpPtr->GtmTomOutenStat));
; ..\mcal_src\Gtm.c	  1286            GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->GLB_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1287            (((uint32)TomConfigPtr->GtmTomTgcConfigGrpPtr->GtmTomUpdateEn)\ 
; ..\mcal_src\Gtm.c	  1288                                                      << GTM_SIXTEEN_U));
; ..\mcal_src\Gtm.c	  1289            GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->ENDIS_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1290              (uint32)(TomConfigPtr->GtmTomTgcConfigGrpPtr->GtmTomEndisCtrl));
; ..\mcal_src\Gtm.c	  1291            GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->ENDIS_STAT.U),\ 
; ..\mcal_src\Gtm.c	  1292              (uint32)(TomConfigPtr->GtmTomTgcConfigGrpPtr->GtmTomEndisStat));
; ..\mcal_src\Gtm.c	  1293  
; ..\mcal_src\Gtm.c	  1294          }
; ..\mcal_src\Gtm.c	  1295        }
; ..\mcal_src\Gtm.c	  1296      }
; ..\mcal_src\Gtm.c	  1297    }
; ..\mcal_src\Gtm.c	  1298  }
; ..\mcal_src\Gtm.c	  1299  
; ..\mcal_src\Gtm.c	  1300  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1301  ** Syntax : void Gtm_lPortConfig(void)                                        **
; ..\mcal_src\Gtm.c	  1302  **                                                                            **
; ..\mcal_src\Gtm.c	  1303  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1304  **                                                                            **
; ..\mcal_src\Gtm.c	  1305  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1306  **                                                                            **
; ..\mcal_src\Gtm.c	  1307  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1308  **                                                                            **
; ..\mcal_src\Gtm.c	  1309  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1310  **                                                                            **
; ..\mcal_src\Gtm.c	  1311  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1312  **                                                                            **
; ..\mcal_src\Gtm.c	  1313  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1314  **                                                                            **
; ..\mcal_src\Gtm.c	  1315  ** Description :     Function to initialize Port Interfaces to GTM            **
; ..\mcal_src\Gtm.c	  1316  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1317  IFX_LOCAL_INLINE void Gtm_lPortConfig(void)
; ..\mcal_src\Gtm.c	  1318  {
; ..\mcal_src\Gtm.c	  1319    const Gtm_PortConfigType *PortConfigPtr;       /* Pointer to Port Config    */
; ..\mcal_src\Gtm.c	  1320    uint8 Count;
; ..\mcal_src\Gtm.c	  1321    volatile uint32 GtmToPortToutSelRegCount = GTM_NO_OF_TIM_MODULES;
; ..\mcal_src\Gtm.c	  1322    /* Port configuration for TIM Channels */
; ..\mcal_src\Gtm.c	  1323    PortConfigPtr = Gtm_kConfigPtr->GtmPortConfigPtr;
; ..\mcal_src\Gtm.c	  1324    if(Gtm_kConfigPtr->GtmPortConfigPtr != NULL_PTR)
; ..\mcal_src\Gtm.c	  1325    {
; ..\mcal_src\Gtm.c	  1326      for(Count = GTM_ZERO_U ; Count < GtmToPortToutSelRegCount ; Count++)
; ..\mcal_src\Gtm.c	  1327      {
; ..\mcal_src\Gtm.c	  1328        GTM_SFR_INIT_WRITE32((MODULE_GTM.INOUTSEL.TIM[Count].INSEL.U),\ 
; ..\mcal_src\Gtm.c	  1329                                     PortConfigPtr->TimInSel[Count]);
; ..\mcal_src\Gtm.c	  1330      }
; ..\mcal_src\Gtm.c	  1331      /* Port configuration for TOM Channels */
; ..\mcal_src\Gtm.c	  1332      for(Count = GTM_ZERO_U ; Count < GTM_NO_OF_TOUTSEL_REGISTERS ; Count++)
; ..\mcal_src\Gtm.c	  1333      {
; ..\mcal_src\Gtm.c	  1334        GTM_SFR_INIT_WRITE32((MODULE_GTM.INOUTSEL.T.OUTSEL[Count].U),
; ..\mcal_src\Gtm.c	  1335                                      PortConfigPtr->ToutSel[Count]);
; ..\mcal_src\Gtm.c	  1336      }
; ..\mcal_src\Gtm.c	  1337    }
; ..\mcal_src\Gtm.c	  1338  }
; ..\mcal_src\Gtm.c	  1339  
; ..\mcal_src\Gtm.c	  1340  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1341  ** Syntax : void Gtm_lTbuConfig(void)                                         **
; ..\mcal_src\Gtm.c	  1342  **                                                                            **
; ..\mcal_src\Gtm.c	  1343  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1344  **                                                                            **
; ..\mcal_src\Gtm.c	  1345  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1346  **                                                                            **
; ..\mcal_src\Gtm.c	  1347  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1348  **                                                                            **
; ..\mcal_src\Gtm.c	  1349  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1350  **                                                                            **
; ..\mcal_src\Gtm.c	  1351  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1352  **                                                                            **
; ..\mcal_src\Gtm.c	  1353  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1354  **                                                                            **
; ..\mcal_src\Gtm.c	  1355  ** Description :     Function to initialize TBU                               **
; ..\mcal_src\Gtm.c	  1356  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1357  IFX_LOCAL_INLINE void Gtm_lTbuConfig(void)
; ..\mcal_src\Gtm.c	  1358  {
; ..\mcal_src\Gtm.c	  1359    const Gtm_TbuConfigType *TbuConfigPtr;         /* Pointer to Tbu Config     */
; ..\mcal_src\Gtm.c	  1360    volatile Gtm_TbuType *GtmTbu;                  /* TBU Register type         */
; ..\mcal_src\Gtm.c	  1361    uint8 Count;
; ..\mcal_src\Gtm.c	  1362    uint32 RegVal;
; ..\mcal_src\Gtm.c	  1363    /* Check if Tbu need to be Initialized */
; ..\mcal_src\Gtm.c	  1364    if(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTbuConfigPtr != NULL_PTR)
; ..\mcal_src\Gtm.c	  1365    {
; ..\mcal_src\Gtm.c	  1366      /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	  1367           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	  1368      GtmTbu = (volatile Gtm_TbuType*)(void*)&(MODULE_GTM.TBU);
; ..\mcal_src\Gtm.c	  1369  
; ..\mcal_src\Gtm.c	  1370      for(Count =GTM_ZERO_U; Count < GTM_NO_OF_TBU_CHANNELS; Count++)
; ..\mcal_src\Gtm.c	  1371      {
; ..\mcal_src\Gtm.c	  1372        /* MISRA Rule Violation 17.4
; ..\mcal_src\Gtm.c	  1373           Pointer arithmetic other than array indexing used
; ..\mcal_src\Gtm.c	  1374           This cannot be avoided because of Post Build structure */
; ..\mcal_src\Gtm.c	  1375  
; ..\mcal_src\Gtm.c	  1376       /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic used due to 
; ..\mcal_src\Gtm.c	  1377       PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Gtm.c	  1378       TbuConfigPtr=&(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTbuConfigPtr[Count]);
; ..\mcal_src\Gtm.c	  1379        /* If the Tbu channel need to initialized*/
; ..\mcal_src\Gtm.c	  1380        if(Gtm_lGetUnused8(TbuConfigPtr->TbuChannelCtrl) == (boolean)FALSE)
; ..\mcal_src\Gtm.c	  1381        {
; ..\mcal_src\Gtm.c	  1382          /* Disable the channel if in case enabled prior */
; ..\mcal_src\Gtm.c	  1383          GTM_SFR_INIT_USER_MODE_WRITE32(GTM_TBU_CHEN.U , \ 
; ..\mcal_src\Gtm.c	  1384                                  (GTM_BIT_SET << (GTM_BITS_PER_TBU * Count)));
; ..\mcal_src\Gtm.c	  1385          GTM_SFR_INIT_USER_MODE_WRITE32((GtmTbu->GtmTbuCh[Count].CH_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1386                       (uint32)((uint32)TbuConfigPtr->TbuChannelCtrl &     \ 
; ..\mcal_src\Gtm.c	  1387                       GTM_TBU_GET_LOWER_NIBBLE));
; ..\mcal_src\Gtm.c	  1388          GTM_SFR_INIT_USER_MODE_WRITE32((GtmTbu->GtmTbuCh[Count].CH_BASE.U),\ 
; ..\mcal_src\Gtm.c	  1389                        TbuConfigPtr->TbuBaseValue);
; ..\mcal_src\Gtm.c	  1390          RegVal = (uint32)(Gtm_lGetBit8(TbuConfigPtr->TbuChannelCtrl,\ 
; ..\mcal_src\Gtm.c	  1391                            GTM_TBU_CONFIGURED));
; ..\mcal_src\Gtm.c	  1392          RegVal = (uint32)(RegVal << ((GTM_BITS_PER_TBU * Count) + GTM_ONE_U));
; ..\mcal_src\Gtm.c	  1393          GTM_SFR_INIT_USER_MODE_WRITE32((GTM_TBU_CHEN.U),RegVal);
; ..\mcal_src\Gtm.c	  1394        }
; ..\mcal_src\Gtm.c	  1395      }
; ..\mcal_src\Gtm.c	  1396    }
; ..\mcal_src\Gtm.c	  1397  }
; ..\mcal_src\Gtm.c	  1398  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1399  ** Syntax : void Gtm_lTtcanConnectionsConfig(void)                            **
; ..\mcal_src\Gtm.c	  1400  **                                                                            **
; ..\mcal_src\Gtm.c	  1401  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1402  **                                                                            **
; ..\mcal_src\Gtm.c	  1403  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1404  **                                                                            **
; ..\mcal_src\Gtm.c	  1405  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1406  **                                                                            **
; ..\mcal_src\Gtm.c	  1407  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1408  **                                                                            **
; ..\mcal_src\Gtm.c	  1409  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1410  **                                                                            **
; ..\mcal_src\Gtm.c	  1411  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1412  **                                                                            **
; ..\mcal_src\Gtm.c	  1413  ** Description :     Function to initialize TTCAN Connections                 **
; ..\mcal_src\Gtm.c	  1414  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1415  IFX_LOCAL_INLINE void Gtm_lTtcanConnectionsConfig(void)
; ..\mcal_src\Gtm.c	  1416  {
; ..\mcal_src\Gtm.c	  1417    GTM_SFR_INIT_USER_MODE_WRITE32((MODULE_GTM.INOUTSEL.CAN.OUTSEL.U),  \ 
; ..\mcal_src\Gtm.c	  1418               (uint32)(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTtcanTriggers[0]));
; ..\mcal_src\Gtm.c	  1419  }
; ..\mcal_src\Gtm.c	  1420  
; ..\mcal_src\Gtm.c	  1421  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1422  ** Syntax : Std_ReturnType Gtm_lEnableGtm(void)                               **
; ..\mcal_src\Gtm.c	  1423  **                                                                            **
; ..\mcal_src\Gtm.c	  1424  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1425  **                                                                            **
; ..\mcal_src\Gtm.c	  1426  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1427  **                                                                            **
; ..\mcal_src\Gtm.c	  1428  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1429  **                                                                            **
; ..\mcal_src\Gtm.c	  1430  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1431  **                                                                            **
; ..\mcal_src\Gtm.c	  1432  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1433  **                                                                            **
; ..\mcal_src\Gtm.c	  1434  ** Return value:     E_OK if GTM clock is enabled successfully                **
; ..\mcal_src\Gtm.c	  1435  **                   E_NOT_OK if GTM clock is not enabled successfully        **
; ..\mcal_src\Gtm.c	  1436  **                                                                            **
; ..\mcal_src\Gtm.c	  1437  ** Description :     Function to Enable GTM                                   **
; ..\mcal_src\Gtm.c	  1438  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1439  IFX_LOCAL_INLINE Std_ReturnType Gtm_lEnableGtm(void)
; ..\mcal_src\Gtm.c	  1440  {
; ..\mcal_src\Gtm.c	  1441    Std_ReturnType GtmEnableStatus= E_NOT_OK;
; ..\mcal_src\Gtm.c	  1442    uint32 GtmClockEnableStatus;
; ..\mcal_src\Gtm.c	  1443  
; ..\mcal_src\Gtm.c	  1444    GTM_SFR_INIT_RESETENDINIT();
	mov	d8,#1
	call	Mcal_ResetENDINIT
.L113:

; ..\mcal_src\Gtm.c	  1445  
; ..\mcal_src\Gtm.c	  1446    /* Enable the GTM Module */
; ..\mcal_src\Gtm.c	  1447    GTM_SFR_INIT_MODIFY32(MODULE_GTM.CLC.U,GTM_CLC_DISR_CLR_MASK, GTM_ZERO_U)
	movh.a	a12,#61466
	lea	a12,[a12]@los(0xf019fd00)
	ld.w	d15,[a12]
.L299:
	and	d15,#10
	st.w	[a12],d15
.L114:

; ..\mcal_src\Gtm.c	  1448  
; ..\mcal_src\Gtm.c	  1449    GTM_SFR_INIT_SETENDINIT();
	call	Mcal_SetENDINIT
.L396:

; ..\mcal_src\Gtm.c	  1450  
; ..\mcal_src\Gtm.c	  1451    #ifdef IFX_GTM_DEBUG
; ..\mcal_src\Gtm.c	  1452      GtmClockEnableStatus = (((GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1453                                 MODULE_GTM.CLC.U)>>GTM_ONE_U)&GTM_ONE_U)\ 
; ..\mcal_src\Gtm.c	  1454                                 | TestGtm_DebugMask02);
; ..\mcal_src\Gtm.c	  1455    #else
; ..\mcal_src\Gtm.c	  1456      GtmClockEnableStatus = (((uint32)(GTM_SFR_INIT_USER_MODE_READ32(\ 
	ld.w	d15,[a12]
.L300:

; ..\mcal_src\Gtm.c	  1461    if(GtmClockEnableStatus == GTM_ZERO_U)      (inlined)
	jnz.t	d15:1,.L2
.L117:

; ..\mcal_src\Gtm.c	  1457                                  MODULE_GTM.CLC.U))>>GTM_ONE_U)&GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1458    #endif
; ..\mcal_src\Gtm.c	  1459  
; ..\mcal_src\Gtm.c	  1460    /* Check if GTM clock is enabled successfully*/
; ..\mcal_src\Gtm.c	  1461    if(GtmClockEnableStatus == GTM_ZERO_U)
; ..\mcal_src\Gtm.c	  1462    {
; ..\mcal_src\Gtm.c	  1463      volatile uint32 ReadBack;
; ..\mcal_src\Gtm.c	  1464  
; ..\mcal_src\Gtm.c	  1465      #if (GTM_SFR_RESET_ENABLE == STD_ON)
; ..\mcal_src\Gtm.c	  1466  
; ..\mcal_src\Gtm.c	  1467      /* Reset the GTM kernel */
; ..\mcal_src\Gtm.c	  1468      Gtm_lResetKernelInit ();
; ..\mcal_src\Gtm.c	  1469      #endif
; ..\mcal_src\Gtm.c	  1470  
; ..\mcal_src\Gtm.c	  1471     GTM_SFR_INIT_RESETENDINIT();
	call	Mcal_ResetENDINIT
.L397:

; ..\mcal_src\Gtm.c	  1472      /* Enable GTM Sleep */
; ..\mcal_src\Gtm.c	  1473      if(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmModuleSleepEnable==              \ 
	ld.a	a15,[a15]
.L398:
	ld.a	a15,[a15]8
.L399:
	ld.bu	d15,[a15]
.L400:

; ..\mcal_src\Gtm.c	  1473      if(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmModuleSleepEnable==              \       (inlined)
	jne	d15,#1,.L3
.L121:

; ..\mcal_src\Gtm.c	  1474                                                                 GTM_SLEEP_ENABLE)
; ..\mcal_src\Gtm.c	  1475      {
; ..\mcal_src\Gtm.c	  1476        /* Enable Sleep Mode if configured */
; ..\mcal_src\Gtm.c	  1477        GTM_SFR_INIT_MODIFY32(MODULE_GTM.CLC.U,GTM_CLC_EDIS_CLR_MASK, GTM_ZERO_U)
	ld.w	d15,[a12]
.L301:
	and	d15,#3

; ..\mcal_src\Gtm.c	  1477        GTM_SFR_INIT_MODIFY32(MODULE_GTM.CLC.U,GTM_CLC_EDIS_CLR_MASK, GTM_ZERO_U)      (inlined)
	j	.L4

; ..\mcal_src\Gtm.c	  1473      if(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmModuleSleepEnable==              \       (inlined)
.L3:

; ..\mcal_src\Gtm.c	  1478      }
; ..\mcal_src\Gtm.c	  1479      else
; ..\mcal_src\Gtm.c	  1480      {
; ..\mcal_src\Gtm.c	  1481        GTM_SFR_INIT_MODIFY32(MODULE_GTM.CLC.U,GTM_CLC_CLR_MASK, \ 
	ld.w	d15,[a12]
	and	d15,#11
.L302:
	or	d15,#8

; ..\mcal_src\Gtm.c	  1479      else      (inlined)
.L4:
	st.w	[a12],d15
.L124:

; ..\mcal_src\Gtm.c	  1482                                                      GTM_CLC_EDIS_SET_MASK)
; ..\mcal_src\Gtm.c	  1483        /* Disable Sleep Mode if configured */
; ..\mcal_src\Gtm.c	  1484      }
; ..\mcal_src\Gtm.c	  1485  
; ..\mcal_src\Gtm.c	  1486      GTM_SFR_INIT_SETENDINIT();
	call	Mcal_SetENDINIT
.L401:

; ..\mcal_src\Gtm.c	  1487  
; ..\mcal_src\Gtm.c	  1488      /* Read back the CLC register */
; ..\mcal_src\Gtm.c	  1489      ReadBack = GTM_SFR_INIT_USER_MODE_READ32(MODULE_GTM.CLC.U);
	ld.w	d15,[a12]
.L402:
	st.w	[a10],d15
.L403:

; ..\mcal_src\Gtm.c	  1490  
; ..\mcal_src\Gtm.c	  1491      /* ReadBack is required to ensure the CLC register modifications */
; ..\mcal_src\Gtm.c	  1492      UNUSED_PARAMETER(ReadBack)
; ..\mcal_src\Gtm.c	  1493  
; ..\mcal_src\Gtm.c	  1494      /*Set the GTM Status to Enabled */
; ..\mcal_src\Gtm.c	  1495      GtmEnableStatus = E_OK;
	mov	d8,#0
.L404:
	ld.w	d15,[a10]

; ..\mcal_src\Gtm.c	  1461    if(GtmClockEnableStatus == GTM_ZERO_U)      (inlined)
.L2:
	jne	d8,#0,.L5
.L106:
	ld.a	a15,[a10]52
.L405:
	mov	d4,#0
	movh.a	a2,#61456
.L303:
	mov	d5,d4
	lea	a2,[a2]@los(0xf0100308)
.L304:
	ld.a	a6,[a15]
.L406:
	movh.a	a15,#61456
	lea	a15,[a15]@los(0xf0100304)
.L407:
	lea	a4,[a6]8
	ld.a	a5,[a4]
.L408:
	ld.w	d0,[a15]
.L305:
	ld.a	a7,[a6]
.L307:
	ld.w	d15,[a5]2
.L409:
	ld.w	d1,[a2]
.L308:

; ..\mcal_src\Gtm.c	  1060    if( (RegTemp3 != Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkNum) ||          \       (inlined)
	jne	d15,d0,.L6
.L410:
	ld.w	d0,[a5]6
.L306:

; ..\mcal_src\Gtm.c	  1061        (RegTemp4 != Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkDen))      (inlined)
	jeq	d1,d0,.L7

; ..\mcal_src\Gtm.c	  1060    if( (RegTemp3 != Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkNum) ||          \       (inlined)
.L6:
	mov	d15,#21845
	movh.a	a5,#61456
.L411:
	addih	d15,d15,#85
	ld.w	d0,[a5]@los(0xf0100300)
.L310:
	st.w	[a5]@los(0xf0100300),d15
.L412:
	mov	d5,#1
.L413:
	ld.a	a5,[a4]
	ld.w	d1,[a5]2
.L309:
	st.w	[a15],d1
.L414:
	ld.a	a5,[a4]
	ld.w	d1,[a5]2
	st.w	[a15],d1
.L415:
	ld.a	a15,[a4]
	ld.w	d1,[a15]6
	st.w	[a2],d1
.L416:
	mov	d1,#0

; ..\mcal_src\Gtm.c	  1081      for(Count = GTM_ZERO_U; Count < GTM_CMU_TOTAL_CLOCKS; Count++)      (inlined)
	mov.a	a15,#11
.L8:
	sh	d3,d1,#1
.L417:
	mov	d2,#3
.L418:
	sh	d2,d2,d3
.L419:
	and	d2,d0
.L420:

; ..\mcal_src\Gtm.c	  1083        if(( RegTemp & \       (inlined)
	jeq	d2,#0,.L9
.L421:
	mov	d15,#2
.L422:
	sh	d15,d15,d3
.L423:
	or	d4,d15

; ..\mcal_src\Gtm.c	  1083        if(( RegTemp & \       (inlined)
.L9:
	add	d1,#1

; ..\mcal_src\Gtm.c	  1081      for(Count = GTM_ZERO_U; Count < GTM_CMU_TOTAL_CLOCKS; Count++)      (inlined)
	loop	a15,.L8

; ..\mcal_src\Gtm.c	  1060    if( (RegTemp3 != Gtm_kConfigPtr->GtmModuleConfigPtr->GtmGclkNum) ||          \       (inlined)
.L7:

; ..\mcal_src\Gtm.c	  1091    if(ClockSettingPtr != NULL_PTR)      /* Configure CMU clocks if configured  */      (inlined)
	jz.a	a7,.L10
.L424:

; ..\mcal_src\Gtm.c	  1094      if(EnableClock == GTM_ZERO_U)      (inlined)
	jne	d5,#0,.L11
.L425:
	mov	d15,#21845
	movh.a	a15,#61456
	addih	d15,d15,#85
	st.w	[a15]@los(0xf0100300),d15

; ..\mcal_src\Gtm.c	  1094      if(EnableClock == GTM_ZERO_U)      (inlined)
.L11:
	ld.a	a15,[a6]
.L311:
	mov	d15,#0
	movh.a	a12,#61456
.L137:
	lea	a12,[a12]@los(0xf0100300)
.L140:
	lea	a2,[a15]4
.L426:

; ..\mcal_src\Gtm.c	   924    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_CLOCKS; Count++)      (inlined)
	mov.a	a4,#7
.L12:
	sh	d2,d15,#1
	ld.w	d0,[a15]
.L427:
	mov	d1,#2
.L428:
	sh	d1,d1,d2
.L429:
	and	d0,d1
.L430:

; ..\mcal_src\Gtm.c	   926      if(ClockSettingPtr->GtmClockEnable & \       (inlined)
	jeq	d0,#0,.L13
.L431:
	movh.a	a5,#61456
	lea	a5,[a5]@los(0xf010030c)
	addsc.a	a5,a5,d15,#2
	ld.w	d0,[a2]
	st.w	[a5],d0

; ..\mcal_src\Gtm.c	   926      if(ClockSettingPtr->GtmClockEnable & \       (inlined)
.L13:
	add	d15,#1
	add.a	a2,#4

; ..\mcal_src\Gtm.c	   924    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_CLOCKS; Count++)      (inlined)
	loop	a4,.L12
.L141:
	ld.a	a2,[a6]
.L312:
	mov	d0,#0

; ..\mcal_src\Gtm.c	   975    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_EXT_CLOCKS; Count++)      (inlined)
	mov.a	a4,#2
.L313:
	mov.aa	a15,a2

; ..\mcal_src\Gtm.c	   975    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_EXT_CLOCKS; Count++)      (inlined)
.L14:
	sh	d2,d0,#1
	ld.w	d15,[a2]
.L432:
	mov	d1,#2
.L433:
	add	d2,d2,#16
.L434:
	sh	d1,d1,d2
.L435:
	and	d15,d1
.L436:

; ..\mcal_src\Gtm.c	   977      if(ClockSettingPtr->GtmClockEnable & \       (inlined)
	jeq	d15,#0,.L15
.L437:
	movh.a	a5,#61456
	lea	a5,[a5]@los(0xf010032c)
	addsc.a	a5,a5,d0,#3
	ld.w	d15,[a15]40
	st.w	[a5],d15
.L438:
	ld.w	d15,[a15]40
	st.w	[a5],d15
.L439:
	ld.w	d15,[a15]44
	st.w	[a5]4,d15

; ..\mcal_src\Gtm.c	   977      if(ClockSettingPtr->GtmClockEnable & \       (inlined)
.L15:
	add	d0,#1
	lea	a15,[a15]8

; ..\mcal_src\Gtm.c	   975    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_EXT_CLOCKS; Count++)      (inlined)
	loop	a4,.L14
.L440:
	ld.w	d15,[a2]36
.L441:
	movh.a	a15,#61456
.L442:
	and	d15,#15
	st.w	[a15]@los(0xf0100344),d15
.L145:
	mov	d5,#0
.L443:
	ld.w	d15,[a7]
	or	d15,d4
	st.w	[a12],d15

; ..\mcal_src\Gtm.c	  1091    if(ClockSettingPtr != NULL_PTR)      /* Configure CMU clocks if configured  */      (inlined)
.L10:

; ..\mcal_src\Gtm.c	  1111    if(EnableClock == GTM_ONE_U)      (inlined)
	jeq	d5,#0,.L16
.L444:
	movh.a	a15,#61456
	st.w	[a15]@los(0xf0100300),d4

; ..\mcal_src\Gtm.c	  1111    if(EnableClock == GTM_ONE_U)      (inlined)
.L16:
	ld.a	a15,[a10]56
.L314:
	movh.a	a2,#61456
.L445:
	mov	d0,#7680
.L446:
	ld.a	a15,[a15]8
.L447:
	ld.a	a15,[a15]56
.L315:
	ld.hu	d15,[a15]0
	st.w	[a2]8,d15
.L448:
	ld.hu	d15,[a15]2
	and	d15,#63
	st.w	[a2]20,d15
.L449:
	ld.hu	d15,[a15]2
	and	d15,#192
	sh	d15,#-6
	st.w	[a2]28,d15
.L450:
	ld.hu	d15,[a15]2
.L150:
	movh.a	a15,#61444
.L151:
	and	d15,d0
	sh	d15,d15,#-9
	st.w	[a2]32,d15
.L152:
	mov	d0,#3327
	ld.w	d15,[a15]@los(0xf0039600)
	addih	d0,d0,#32543
	and	d15,d0
	insert	d15,d15,#1,#10,#1
	st.w	[a15]@los(0xf0039600),d15
.L153:
	call	Gtm_lTimConfigure
.L156:

; ..\mcal_src\Gtm.c	  1496  
; ..\mcal_src\Gtm.c	  1497    }
; ..\mcal_src\Gtm.c	  1498  
; ..\mcal_src\Gtm.c	  1499    return (GtmEnableStatus);
; ..\mcal_src\Gtm.c	  1500  
; ..\mcal_src\Gtm.c	  1501  }
; ..\mcal_src\Gtm.c	  1502  
; ..\mcal_src\Gtm.c	  1503  #if (GTM_SFR_RESET_ENABLE == STD_ON)
; ..\mcal_src\Gtm.c	  1504  
; ..\mcal_src\Gtm.c	  1505  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1506  ** Syntax : IFX_LOCAL_INLINE void Gtm_lResetKernelInit(void)                  **
; ..\mcal_src\Gtm.c	  1507  **                                                                            **
; ..\mcal_src\Gtm.c	  1508  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1509  **                                                                            **
; ..\mcal_src\Gtm.c	  1510  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1511  **                                                                            **
; ..\mcal_src\Gtm.c	  1512  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1513  **                                                                            **
; ..\mcal_src\Gtm.c	  1514  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1515  **                                                                            **
; ..\mcal_src\Gtm.c	  1516  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1517  **                                                                            **
; ..\mcal_src\Gtm.c	  1518  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1519  **                                                                            **
; ..\mcal_src\Gtm.c	  1520  ** Description :     Function to Reset GTM  kernel                            **
; ..\mcal_src\Gtm.c	  1521  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1522  IFX_LOCAL_INLINE void Gtm_lResetKernelInit(void)
; ..\mcal_src\Gtm.c	  1523  {
; ..\mcal_src\Gtm.c	  1524    uint32 KernelResetTimeOutCount = GTM_KERNEL_RESET_DELAY;
; ..\mcal_src\Gtm.c	  1525    volatile uint32 ResetReadBack;
; ..\mcal_src\Gtm.c	  1526    uint32 ResetStatus;
; ..\mcal_src\Gtm.c	  1527  
; ..\mcal_src\Gtm.c	  1528    /* Reset the kernel */
; ..\mcal_src\Gtm.c	  1529    GTM_SFR_INIT_RESETENDINIT();
; ..\mcal_src\Gtm.c	  1530  
; ..\mcal_src\Gtm.c	  1531    GTM_SFR_INIT_MODIFY32(GTM_KRST0.U,GTM_KRST0_CLR_MASK,GTM_ONE_U)
; ..\mcal_src\Gtm.c	  1532    ResetReadBack = GTM_SFR_INIT_USER_MODE_READ32(GTM_KRST0.U);
; ..\mcal_src\Gtm.c	  1533    GTM_SFR_INIT_WRITE32(GTM_KRST1.U,GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1534    ResetReadBack = GTM_SFR_INIT_USER_MODE_READ32(GTM_KRST1.U);
; ..\mcal_src\Gtm.c	  1535  
; ..\mcal_src\Gtm.c	  1536    GTM_SFR_INIT_SETENDINIT();
; ..\mcal_src\Gtm.c	  1537  
; ..\mcal_src\Gtm.c	  1538  
; ..\mcal_src\Gtm.c	  1539    /* Wait for Reset acknowledgement */
; ..\mcal_src\Gtm.c	  1540    do
; ..\mcal_src\Gtm.c	  1541    {
; ..\mcal_src\Gtm.c	  1542      #ifdef IFX_GTM_DEBUG
; ..\mcal_src\Gtm.c	  1543      ResetStatus = (((((uint32)GTM_SFR_INIT_USER_MODE_READ32(GTM_KRST0.U))>>\ 
; ..\mcal_src\Gtm.c	  1544                     GTM_ONE_U)&GTM_ONE_U)& TestGtm_DebugMask01);
; ..\mcal_src\Gtm.c	  1545      #else
; ..\mcal_src\Gtm.c	  1546      ResetStatus = ((((uint32)GTM_SFR_INIT_USER_MODE_READ32(GTM_KRST0.U))>>\ 
; ..\mcal_src\Gtm.c	  1547                     GTM_ONE_U)&GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1548      #endif
; ..\mcal_src\Gtm.c	  1549      KernelResetTimeOutCount--;
; ..\mcal_src\Gtm.c	  1550    }while((ResetStatus == GTM_ZERO_U) && \ 
; ..\mcal_src\Gtm.c	  1551                                         (KernelResetTimeOutCount > GTM_ZERO_U));
; ..\mcal_src\Gtm.c	  1552  
; ..\mcal_src\Gtm.c	  1553    GTM_SFR_INIT_RESETENDINIT();
; ..\mcal_src\Gtm.c	  1554    /* Clear the status bit */
; ..\mcal_src\Gtm.c	  1555    GTM_SFR_INIT_WRITE32(GTM_KRSTCLR.U ,GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1556    GTM_SFR_INIT_SETENDINIT();
; ..\mcal_src\Gtm.c	  1557  
; ..\mcal_src\Gtm.c	  1558    /* Read back kernel reset clear register */
; ..\mcal_src\Gtm.c	  1559    ResetReadBack = GTM_SFR_INIT_USER_MODE_READ32(GTM_KRSTCLR.U);
; ..\mcal_src\Gtm.c	  1560    UNUSED_PARAMETER(ResetReadBack)
; ..\mcal_src\Gtm.c	  1561  }
; ..\mcal_src\Gtm.c	  1562  
; ..\mcal_src\Gtm.c	  1563  #endif
; ..\mcal_src\Gtm.c	  1564  
; ..\mcal_src\Gtm.c	  1565  
; ..\mcal_src\Gtm.c	  1566  #if (GTM_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Gtm.c	  1567  
; ..\mcal_src\Gtm.c	  1568  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1569  ** Syntax : IFX_LOCAL_INLINE void Gtm_lResetKernelDeInit(void)                **
; ..\mcal_src\Gtm.c	  1570  **                                                                            **
; ..\mcal_src\Gtm.c	  1571  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1572  **                                                                            **
; ..\mcal_src\Gtm.c	  1573  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1574  **                                                                            **
; ..\mcal_src\Gtm.c	  1575  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1576  **                                                                            **
; ..\mcal_src\Gtm.c	  1577  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1578  **                                                                            **
; ..\mcal_src\Gtm.c	  1579  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1580  **                                                                            **
; ..\mcal_src\Gtm.c	  1581  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1582  **                                                                            **
; ..\mcal_src\Gtm.c	  1583  ** Description :     Function to Reset GTM  kernel                            **
; ..\mcal_src\Gtm.c	  1584  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1585  IFX_LOCAL_INLINE void Gtm_lResetKernelDeInit(void)
; ..\mcal_src\Gtm.c	  1586  {
; ..\mcal_src\Gtm.c	  1587    uint32 KernelResetTimeOutCount = GTM_KERNEL_RESET_DELAY;
; ..\mcal_src\Gtm.c	  1588    volatile uint32 ResetReadBack;
; ..\mcal_src\Gtm.c	  1589    uint32 ResetStatus;
; ..\mcal_src\Gtm.c	  1590  
; ..\mcal_src\Gtm.c	  1591    /* Reset the kernel */
; ..\mcal_src\Gtm.c	  1592    GTM_SFR_DEINIT_RESETENDINIT();
; ..\mcal_src\Gtm.c	  1593  
; ..\mcal_src\Gtm.c	  1594    GTM_SFR_DEINIT_MODIFY32(GTM_KRST0.U,GTM_KRST0_CLR_MASK,GTM_ONE_U)
; ..\mcal_src\Gtm.c	  1595    ResetReadBack = GTM_SFR_DEINIT_USER_MODE_READ32(GTM_KRST0.U);
; ..\mcal_src\Gtm.c	  1596    GTM_SFR_DEINIT_WRITE32(GTM_KRST1.U,GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1597    ResetReadBack = GTM_SFR_DEINIT_USER_MODE_READ32(GTM_KRST1.U);
; ..\mcal_src\Gtm.c	  1598  
; ..\mcal_src\Gtm.c	  1599    GTM_SFR_DEINIT_SETENDINIT();
; ..\mcal_src\Gtm.c	  1600  
; ..\mcal_src\Gtm.c	  1601  
; ..\mcal_src\Gtm.c	  1602    /* Wait for Reset acknowledgement */
; ..\mcal_src\Gtm.c	  1603    do
; ..\mcal_src\Gtm.c	  1604    {
; ..\mcal_src\Gtm.c	  1605      #ifdef IFX_GTM_DEBUG
; ..\mcal_src\Gtm.c	  1606      ResetStatus = (((((uint32)GTM_SFR_DEINIT_USER_MODE_READ32(GTM_KRST0.U))>>\ 
; ..\mcal_src\Gtm.c	  1607                     GTM_ONE_U)&GTM_ONE_U)& TestGtm_DebugMask01);
; ..\mcal_src\Gtm.c	  1608      #else
; ..\mcal_src\Gtm.c	  1609      ResetStatus = ((((uint32)GTM_SFR_DEINIT_USER_MODE_READ32(GTM_KRST0.U))>>\ 
; ..\mcal_src\Gtm.c	  1610                      GTM_ONE_U)&GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1611      #endif
; ..\mcal_src\Gtm.c	  1612      KernelResetTimeOutCount--;
; ..\mcal_src\Gtm.c	  1613    }while((ResetStatus == GTM_ZERO_U) && \ 
; ..\mcal_src\Gtm.c	  1614                                         (KernelResetTimeOutCount > GTM_ZERO_U));
; ..\mcal_src\Gtm.c	  1615  
; ..\mcal_src\Gtm.c	  1616    GTM_SFR_DEINIT_RESETENDINIT();
; ..\mcal_src\Gtm.c	  1617    /* Clear the status bit */
; ..\mcal_src\Gtm.c	  1618  
; ..\mcal_src\Gtm.c	  1619    GTM_SFR_DEINIT_WRITE32(GTM_KRSTCLR.U,GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1620    GTM_SFR_DEINIT_SETENDINIT();
; ..\mcal_src\Gtm.c	  1621  
; ..\mcal_src\Gtm.c	  1622    /* Read back kernel reset clear register */
; ..\mcal_src\Gtm.c	  1623  
; ..\mcal_src\Gtm.c	  1624    ResetReadBack = GTM_SFR_DEINIT_USER_MODE_READ32(GTM_KRSTCLR.U);
; ..\mcal_src\Gtm.c	  1625    UNUSED_PARAMETER(ResetReadBack)
; ..\mcal_src\Gtm.c	  1626  }
; ..\mcal_src\Gtm.c	  1627  
; ..\mcal_src\Gtm.c	  1628  #endif
; ..\mcal_src\Gtm.c	  1629  
; ..\mcal_src\Gtm.c	  1630  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1631  ** Syntax : void Gtm_lTomConfigure(void)                                      **
; ..\mcal_src\Gtm.c	  1632  **                                                                            **
; ..\mcal_src\Gtm.c	  1633  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1634  **                                                                            **
; ..\mcal_src\Gtm.c	  1635  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1636  **                                                                            **
; ..\mcal_src\Gtm.c	  1637  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm.c	  1638  **                                                                            **
; ..\mcal_src\Gtm.c	  1639  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm.c	  1640  **                                                                            **
; ..\mcal_src\Gtm.c	  1641  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1642  **                                                                            **
; ..\mcal_src\Gtm.c	  1643  ** Return value:     None                                                     **
; ..\mcal_src\Gtm.c	  1644  **                                                                            **
; ..\mcal_src\Gtm.c	  1645  ** Description :     Function to Initialize TOM Module                        **
; ..\mcal_src\Gtm.c	  1646  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1647  IFX_LOCAL_INLINE void Gtm_lTomConfigure(void)
; ..\mcal_src\Gtm.c	  1648  {
; ..\mcal_src\Gtm.c	  1649    Ifx_GTM_TOM_TGC_TYPE *TomTgcRegPtr;               /* Pointer to TOM TGC Reg */
; ..\mcal_src\Gtm.c	  1650    /* The following 3 variables backs up the FUPD, OUTEN and ENDIS before
; ..\mcal_src\Gtm.c	  1651       initialization of TOM channels start. The array size should be maximum
; ..\mcal_src\Gtm.c	  1652       of TOM. */
; ..\mcal_src\Gtm.c	  1653    uint32 FupdBackupValue[GTM_NO_OF_TOM_MODULES][GTM_NO_OF_TGC_PER_MODULE];
; ..\mcal_src\Gtm.c	  1654    uint32 OutenCtrlBackupValue[GTM_NO_OF_TOM_MODULES][GTM_NO_OF_TGC_PER_MODULE];
; ..\mcal_src\Gtm.c	  1655    uint32 EndisCtrlBackupValue[GTM_NO_OF_TOM_MODULES][GTM_NO_OF_TGC_PER_MODULE];
; ..\mcal_src\Gtm.c	  1656    uint8 Count;              /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	  1657    uint8 LoopCount;          /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	  1658    uint8 MinorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm.c	  1659    uint8 ModuleIndex;        /* Variable to hold the Module that uses the Timer*/
; ..\mcal_src\Gtm.c	  1660  
; ..\mcal_src\Gtm.c	  1661  
; ..\mcal_src\Gtm.c	  1662  
; ..\mcal_src\Gtm.c	  1663    /* This is required to retain the old values of EndisCtrl, OutenCtrl and Fupd,
; ..\mcal_src\Gtm.c	  1664       hence a Fupd given for setting the new clock value will not affect the
; ..\mcal_src\Gtm.c	  1665       previously existing channels */
; ..\mcal_src\Gtm.c	  1666    for(Count = GTM_ZERO_U; Count < GTM_NO_OF_TOM_MODULES; Count++)
; ..\mcal_src\Gtm.c	  1667    {
; ..\mcal_src\Gtm.c	  1668      for(LoopCount = GTM_ZERO_U;
; ..\mcal_src\Gtm.c	  1669          LoopCount < GTM_NO_OF_TGC_PER_MODULE; LoopCount++)
; ..\mcal_src\Gtm.c	  1670      {
; ..\mcal_src\Gtm.c	  1671        /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	  1672           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	  1673        TomTgcRegPtr = &((*(Ifx_GTM_TOMx*)(void*)
; ..\mcal_src\Gtm.c	  1674                       (MODULE_GTM.TOM)).TOM_TGC[Count].TGC[LoopCount]);
; ..\mcal_src\Gtm.c	  1675  
; ..\mcal_src\Gtm.c	  1676        FupdBackupValue[Count][LoopCount] =                        \ 
; ..\mcal_src\Gtm.c	  1677                 GTM_SFR_INIT_USER_MODE_READ32((TomTgcRegPtr->FUPD_CTRL.U));
; ..\mcal_src\Gtm.c	  1678        OutenCtrlBackupValue[Count][LoopCount] =                   \ 
; ..\mcal_src\Gtm.c	  1679                 GTM_SFR_INIT_USER_MODE_READ32((TomTgcRegPtr->OUTEN_CTRL.U));
; ..\mcal_src\Gtm.c	  1680        EndisCtrlBackupValue[Count][LoopCount] =                   \ 
; ..\mcal_src\Gtm.c	  1681                 GTM_SFR_INIT_USER_MODE_READ32((TomTgcRegPtr->ENDIS_CTRL.U));
; ..\mcal_src\Gtm.c	  1682        /* Make EndisCtrl and OutenCtrl to its Stat values */
; ..\mcal_src\Gtm.c	  1683        /* Clear the FUPD contents so on a Host Trigger, none of the old
; ..\mcal_src\Gtm.c	  1684           parameters are updated */
; ..\mcal_src\Gtm.c	  1685        GTM_SFR_INIT_USER_MODE_WRITE32((TomTgcRegPtr->FUPD_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1686                                                   GTM_TOM_TGC_CLEAR_FUPD);
; ..\mcal_src\Gtm.c	  1687  
; ..\mcal_src\Gtm.c	  1688        Gtm_lSaveTgcStatus(TomTgcRegPtr);
; ..\mcal_src\Gtm.c	  1689  
; ..\mcal_src\Gtm.c	  1690        for(MinorCnt = GTM_ZERO_U;
; ..\mcal_src\Gtm.c	  1691            MinorCnt < GTM_TOM_CHANNELS_PER_TGC; MinorCnt++)
; ..\mcal_src\Gtm.c	  1692        {
; ..\mcal_src\Gtm.c	  1693  
; ..\mcal_src\Gtm.c	  1694          ModuleIndex = Gtm_lGetModuleIndex(                                     \ 
; ..\mcal_src\Gtm.c	  1695          (uint32)(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomModuleUsage[Count]),
	ld.a	a15,[a10]52
.L451:
	mov	d3,#0
	movh.a	a12,#61457
.L317:
	lea	a12,[a12]@los(0xf0108030)
.L452:
	ld.a	a5,[a15]

; ..\mcal_src\Gtm.c	  1666    for(Count = GTM_ZERO_U; Count < GTM_NO_OF_TOM_MODULES; Count++)      (inlined)
.L17:
	addsc.a	a2,a10,d3,#3
.L453:
	mov	d4,#0

; ..\mcal_src\Gtm.c	  1669          LoopCount < GTM_NO_OF_TGC_PER_MODULE; LoopCount++)      (inlined)
	mov.a	a13,#1
.L318:
	mov.aa	a6,a2
.L454:
	lea	a7,[a2]16
.L455:
	lea	a4,[a2]32

; ..\mcal_src\Gtm.c	  1669          LoopCount < GTM_NO_OF_TGC_PER_MODULE; LoopCount++)      (inlined)
.L18:
	fcall	.cocofun_1
.L320:
	addsc.a	a2,a6,d4,#2
.L456:
	ld.w	d15,[a15]8
.L457:
	st.w	[a2],d15
.L458:
	addsc.a	a2,a7,d4,#2
.L459:
	ld.w	d0,[a15]72
.L460:
	st.w	[a2],d0
.L461:
	addsc.a	a2,a4,d4,#2
.L462:
	ld.w	d0,[a15]64
.L463:
	st.w	[a2],d0
.L464:
	mov	d15,#21845
	st.w	[a15]8,d15
.L170:
	mov	d5,#0

; ..\mcal_src\Gtm.c	  1140    for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_SIXTEEN_U; MinorCnt++)      (inlined)
	mov.a	a2,#15
.L19:
	sh	d6,d5,#1
	ld.w	d1,[a15]68
.L465:
	mov	d0,#3
.L466:
	sh	d0,d0,d6
.L467:
	and	d1,d0
.L468:

; ..\mcal_src\Gtm.c	  1142      if((GTM_SFR_INIT_USER_MODE_READ32(TomTgcRegPtr->ENDIS_STAT.U) &  \       (inlined)
	jeq	d1,#0,.L20
.L177:
	ld.w	d1,[a15]64
	insert	d1,d1,#0,#16,#16
	mov	d2,#2
	sh	d2,d2,d6
.L322:
	or	d1,d2
	st.w	[a15]64,d1

; ..\mcal_src\Gtm.c	  1142      if((GTM_SFR_INIT_USER_MODE_READ32(TomTgcRegPtr->ENDIS_STAT.U) &  \       (inlined)
.L20:
	ld.w	d15,[a15]76
.L469:
	and	d15,d0
.L470:

; ..\mcal_src\Gtm.c	  1150      if((GTM_SFR_INIT_USER_MODE_READ32(TomTgcRegPtr->OUTEN_STAT.U) &      (inlined)
	jeq	d15,#0,.L21
.L180:
	ld.w	d15,[a15]72
	insert	d15,d15,#0,#16,#16
	mov	d0,#2
	sh	d0,d0,d6
.L323:
	or	d15,d0
	st.w	[a15]72,d15

; ..\mcal_src\Gtm.c	  1150      if((GTM_SFR_INIT_USER_MODE_READ32(TomTgcRegPtr->OUTEN_STAT.U) &      (inlined)
.L21:
	add	d5,#1

; ..\mcal_src\Gtm.c	  1140    for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_SIXTEEN_U; MinorCnt++)      (inlined)
	loop	a2,.L19
.L171:

; ..\mcal_src\Gtm.c	  1696                      (uint8)(MinorCnt + (LoopCount * GTM_TOM_CHANNELS_PER_TGC)));
; ..\mcal_src\Gtm.c	  1697  
; ..\mcal_src\Gtm.c	  1698          if(ModuleIndex > GTM_ZERO_U)
; ..\mcal_src\Gtm.c	  1699          {
; ..\mcal_src\Gtm.c	  1700  
; ..\mcal_src\Gtm.c	  1701            /* Clear the information in FUPD/OutEn/EndisCtrl if the corresponding
; ..\mcal_src\Gtm.c	  1702               channel is configured for GPT or PWM */
; ..\mcal_src\Gtm.c	  1703            FupdBackupValue[Count][LoopCount] &=
	mov	d5,#0
	addsc.a	a2,a6,d4,#2
.L321:

; ..\mcal_src\Gtm.c	  1691            MinorCnt < GTM_TOM_CHANNELS_PER_TGC; MinorCnt++)      (inlined)
	mov.a	a14,#7
.L22:
	ld.a	a15,[a5]8
.L471:
	sh	d0,d4,#3
.L325:
	add	d0,d5
.L472:
	addsc.a	a15,a15,d3,#2
.L473:
	sh	d0,#1
.L326:
	ld.w	d15,[a15]36
.L474:
	mov	d1,#3
.L475:
	sh	d2,d1,d0
.L476:
	and	d2,d15
.L477:
	rsub	d0,#0
	sh	d2,d2,d0
.L478:
	extr.u	d15,d2,#0,#8
.L479:

; ..\mcal_src\Gtm.c	  1698          if(ModuleIndex > GTM_ZERO_U)      (inlined)
	jeq	d15,#0,.L23
.L480:

; ..\mcal_src\Gtm.c	  1704                     ~(GTM_TOM_CHAN_ENABLE << (GTM_TOM_BITS_PER_CHAN * MinorCnt));
	sh	d0,d5,#1
	ld.w	d15,[a2]
.L481:

; ..\mcal_src\Gtm.c	  1705            OutenCtrlBackupValue[Count][LoopCount]&=
	addsc.a	a15,a7,d4,#2
.L482:
	sh	d1,d1,d0
.L483:
	mov	d0,#-1
	xor	d1,d0
.L484:
	and	d15,d1
	st.w	[a2],d15
.L485:
	ld.w	d15,[a15]
.L486:
	and	d15,d1
	st.w	[a15],d15
.L487:

; ..\mcal_src\Gtm.c	  1706                     ~(GTM_TOM_CHAN_ENABLE << (GTM_TOM_BITS_PER_CHAN * MinorCnt));
; ..\mcal_src\Gtm.c	  1707            EndisCtrlBackupValue[Count][LoopCount]&=
	addsc.a	a15,a4,d4,#2
	ld.w	d15,[a15]
.L488:
	and	d15,d1
	st.w	[a15],d15

; ..\mcal_src\Gtm.c	  1698          if(ModuleIndex > GTM_ZERO_U)      (inlined)
.L23:
	add	d5,#1

; ..\mcal_src\Gtm.c	  1691            MinorCnt < GTM_TOM_CHANNELS_PER_TGC; MinorCnt++)      (inlined)
	loop	a14,.L22
.L489:
	add	d4,#1

; ..\mcal_src\Gtm.c	  1669          LoopCount < GTM_NO_OF_TGC_PER_MODULE; LoopCount++)      (inlined)
	loop	a13,.L18
.L490:
	add	d3,#1
.L491:

; ..\mcal_src\Gtm.c	  1666    for(Count = GTM_ZERO_U; Count < GTM_NO_OF_TOM_MODULES; Count++)      (inlined)
	jlt.u	d3,#2,.L17
.L492:

; ..\mcal_src\Gtm.c	  1708                     ~(GTM_TOM_CHAN_ENABLE << (GTM_TOM_BITS_PER_CHAN * MinorCnt));
; ..\mcal_src\Gtm.c	  1709          }
; ..\mcal_src\Gtm.c	  1710        }
; ..\mcal_src\Gtm.c	  1711      }
; ..\mcal_src\Gtm.c	  1712    }
; ..\mcal_src\Gtm.c	  1713  
; ..\mcal_src\Gtm.c	  1714  #if ((GTM_PWM_MODULE_USED == STD_ON) || (GTM_GPT_MODULE_USED == STD_ON))
; ..\mcal_src\Gtm.c	  1715    /* Clock Setting for GPT and PWM Modules */
; ..\mcal_src\Gtm.c	  1716    Gtm_lTomClockSetting();
; ..\mcal_src\Gtm.c	  1717    Gtm_lTomHostTrigger();
; ..\mcal_src\Gtm.c	  1718    /* Clock Settings End */
; ..\mcal_src\Gtm.c	  1719  #endif
; ..\mcal_src\Gtm.c	  1720    /*  #if ((GTM_PWM_MODULE_USED == STD_ON) || (GTM_GPT_MODULE_USED == STD_ON))*/
; ..\mcal_src\Gtm.c	  1721  
; ..\mcal_src\Gtm.c	  1722  
; ..\mcal_src\Gtm.c	  1723    Gtm_lTomComplexConfig();
	call	Gtm_lTomComplexConfig
.L183:
	mov	d15,#1
	st.w	[a10]48,d15
.L493:
	mov	d2,#0
.L327:
	ld.w	d15,[a10]48
.L494:
	mov	d3,d2
.L329:

; ..\mcal_src\Gtm.c	  1241    for(MajorCnt = GTM_ZERO_U; MajorCnt < MajorCountLoopLimit; MajorCnt++)      (inlined)
	jeq	d15,#0,.L24
.L495:
	ld.a	a15,[a10]52
	ld.a	a15,[a15]
.L496:
	lea	a4,[a15]8

; ..\mcal_src\Gtm.c	  1241    for(MajorCnt = GTM_ZERO_U; MajorCnt < MajorCountLoopLimit; MajorCnt++)      (inlined)
.L25:
	mov	d4,#0

; ..\mcal_src\Gtm.c	  1243      for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_BITS_IN_U8 ; MinorCnt++)      (inlined)
	mov.a	a5,#7
.L26:
	ld.a	a15,[a4]
.L497:
	mov	d1,#1
.L498:
	sh	d1,d1,d4
.L499:
	addsc.a	a2,a15,d3,#0
	ld.bu	d0,[a2]28
.L500:
	and	d0,d1
.L501:

; ..\mcal_src\Gtm.c	  1246        if((Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomTgcUsage[MajorCnt] &      (inlined)
	jeq	d0,#0,.L27
.L502:
	sh	d0,d3,#5
	ld.a	a15,[a15]32
.L503:
	add	d0,d4
.L504:
	extr.u	d0,d0,#1,#8
.L331:
	mul	d15,d2,#12
.L505:
	and	d1,d4,#1
.L332:
	addsc.a	a15,a15,d15,#0
.L333:
	sha	d15,d0,#11
.L506:
	addsc.a	a2,a12,d15,#0
.L507:
	sha	d15,d1,#9
.L508:
	addsc.a	a6,a2,d15,#0
.L334:
	ld.w	d15,[a15]2
	st.w	[a6]4,d15
.L509:
	add	d2,#1
.L328:
	ld.hu	d15,[a15]0
	st.w	[a6]12,d15
.L510:
	extr.u	d2,d2,#0,#8
.L335:
	ld.a	a2,[a15]8
.L511:

; ..\mcal_src\Gtm.c	  1278          if(TomConfigPtr->GtmTomTgcConfigGrpPtr != NULL_PTR)      (inlined)
	jz.a	a2,.L28
.L512:
	ld.w	d15,[a2]10
	st.w	[a6]8,d15
.L513:
	ld.a	a2,[a15]8
	ld.hu	d15,[a2]6
	st.w	[a6]72,d15
.L514:
	ld.a	a2,[a15]8
	ld.hu	d15,[a2]8
	st.w	[a6]76,d15
.L515:
	ld.a	a2,[a15]8
	ld.hu	d15,[a2]0
	sh	d15,d15,#16
	st.w	[a6],d15
.L516:
	ld.a	a2,[a15]8
	ld.hu	d15,[a2]2
	st.w	[a6]64,d15
.L517:
	ld.a	a15,[a15]8
	ld.hu	d15,[a15]4
	st.w	[a6]68,d15

; ..\mcal_src\Gtm.c	  1278          if(TomConfigPtr->GtmTomTgcConfigGrpPtr != NULL_PTR)      (inlined)
.L28:

; ..\mcal_src\Gtm.c	  1246        if((Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomTgcUsage[MajorCnt] &      (inlined)
.L27:
	add	d4,#1

; ..\mcal_src\Gtm.c	  1243      for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_BITS_IN_U8 ; MinorCnt++)      (inlined)
	loop	a5,.L26
.L518:
	add	d3,#1
.L330:
	extr.u	d3,d3,#0,#8
	ld.w	d15,[a10]48
.L336:

; ..\mcal_src\Gtm.c	  1241    for(MajorCnt = GTM_ZERO_U; MajorCnt < MajorCountLoopLimit; MajorCnt++)      (inlined)
	jlt.u	d3,d15,.L25
.L24:
	ld.a	a15,[a10]52
.L184:

; ..\mcal_src\Gtm.c	  1724  
; ..\mcal_src\Gtm.c	  1725    Gtm_lTomTgcConfigure();
; ..\mcal_src\Gtm.c	  1726  
; ..\mcal_src\Gtm.c	  1727    /* Get the old values of Fupd/OutenCtrl/OutenStat values back again */
; ..\mcal_src\Gtm.c	  1728    for(Count = GTM_ZERO_U; Count < GTM_NO_OF_TOM_MODULES; Count++)
	mov	d3,#0
.L187:
	ld.a	a5,[a15]
.L519:
	lea	a6,[a5]8

; ..\mcal_src\Gtm.c	  1728    for(Count = GTM_ZERO_U; Count < GTM_NO_OF_TOM_MODULES; Count++)      (inlined)
.L29:

; ..\mcal_src\Gtm.c	  1729    {
; ..\mcal_src\Gtm.c	  1730      for(LoopCount = GTM_ZERO_U;
; ..\mcal_src\Gtm.c	  1731          LoopCount < GTM_NO_OF_TGC_PER_MODULE; LoopCount++)
	mov	d4,#0

; ..\mcal_src\Gtm.c	  1731          LoopCount < GTM_NO_OF_TGC_PER_MODULE; LoopCount++)      (inlined)
	mov.a	a7,#1
.L30:

; ..\mcal_src\Gtm.c	  1732      {
; ..\mcal_src\Gtm.c	  1733        /* Get the pointer to the TOM Registers */
; ..\mcal_src\Gtm.c	  1734        TomTgcRegPtr = &((*(Ifx_GTM_TOMx*)                                       \ 
; ..\mcal_src\Gtm.c	  1735             (volatile void *)(MODULE_GTM.TOM)).TOM_TGC[Count].TGC[LoopCount]);
	fcall	.cocofun_1
.L337:

; ..\mcal_src\Gtm.c	  1736        for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_SIXTEEN_U; MinorCnt++)
; ..\mcal_src\Gtm.c	  1737        {
; ..\mcal_src\Gtm.c	  1738          if((EndisCtrlBackupValue[Count][LoopCount]&                            \ 
	addsc.a	a2,a10,d3,#3
.L520:
	mov	d5,#0

; ..\mcal_src\Gtm.c	  1736        for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_SIXTEEN_U; MinorCnt++)      (inlined)
	mov.a	a3,#15
.L339:
	lea	a4,[a2]32
.L521:
	addsc.a	a4,a4,d4,#2
.L522:

; ..\mcal_src\Gtm.c	  1739                     (GTM_TOM_CHAN_ENABLE<<(MinorCnt * GTM_TOM_BITS_PER_CHAN)))> \ 
; ..\mcal_src\Gtm.c	  1740                     GTM_ZERO_UL)
; ..\mcal_src\Gtm.c	  1741          {
; ..\mcal_src\Gtm.c	  1742            GTM_SFR_INIT_USER_MODE_MODIFY32((TomTgcRegPtr->ENDIS_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1743                  (GTM_ENDIS_CLR_MASK),\ 
; ..\mcal_src\Gtm.c	  1744                  ((uint32)((uint32)GTM_TOM_CHAN_DISABLE <<\ 
; ..\mcal_src\Gtm.c	  1745                  ((MinorCnt * GTM_TOM_BITS_PER_CHAN)+GTM_ONE_U))))
; ..\mcal_src\Gtm.c	  1746  
; ..\mcal_src\Gtm.c	  1747          }
; ..\mcal_src\Gtm.c	  1748          if((OutenCtrlBackupValue[Count][LoopCount]&
	lea	a13,[a2]16
.L523:

; ..\mcal_src\Gtm.c	  1749                 (uint32)((uint32)GTM_TOM_CHAN_ENABLE<<(MinorCnt * \ 
; ..\mcal_src\Gtm.c	  1750                 GTM_TOM_BITS_PER_CHAN)))> GTM_ZERO_UL)
; ..\mcal_src\Gtm.c	  1751          {
; ..\mcal_src\Gtm.c	  1752            GTM_SFR_INIT_USER_MODE_MODIFY32((TomTgcRegPtr->OUTEN_CTRL.U),\ 
; ..\mcal_src\Gtm.c	  1753                  GTM_OUTEN_CLR_MASK,((uint32)((uint32)GTM_TOM_CHAN_DISABLE <<\ 
; ..\mcal_src\Gtm.c	  1754                  ((MinorCnt * GTM_TOM_BITS_PER_CHAN)+GTM_ONE_U))))
; ..\mcal_src\Gtm.c	  1755          }
; ..\mcal_src\Gtm.c	  1756          if((FupdBackupValue[Count][LoopCount] &
	mov.aa	a14,a2

; ..\mcal_src\Gtm.c	  1736        for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_SIXTEEN_U; MinorCnt++)      (inlined)
.L31:
	sh	d6,d5,#1
	ld.w	d1,[a4]
.L524:
	mov	d0,#3
.L525:
	sh	d0,d0,d6
.L526:
	and	d1,d0
.L527:

; ..\mcal_src\Gtm.c	  1738          if((EndisCtrlBackupValue[Count][LoopCount]&                            \       (inlined)
	jeq	d1,#0,.L32
.L199:
	ld.w	d1,[a15]64
	mov	d2,#1
	insert	d1,d1,#0,#16,#16
	add	d15,d6,#1
	sh	d2,d2,d15
.L340:
	or	d1,d2
	st.w	[a15]64,d1

; ..\mcal_src\Gtm.c	  1738          if((EndisCtrlBackupValue[Count][LoopCount]&                            \       (inlined)
.L32:
	addsc.a	a2,a13,d4,#2
	ld.w	d1,[a2]
.L528:
	and	d1,d0
.L529:

; ..\mcal_src\Gtm.c	  1748          if((OutenCtrlBackupValue[Count][LoopCount]&      (inlined)
	jeq	d1,#0,.L33
.L202:
	ld.w	d15,[a15]72
	mov	d2,#1
	insert	d1,d15,#0,#16,#16
	add	d15,d6,#1
	sh	d2,d2,d15
.L341:
	or	d1,d2
	st.w	[a15]72,d1

; ..\mcal_src\Gtm.c	  1748          if((OutenCtrlBackupValue[Count][LoopCount]&      (inlined)
.L33:
	addsc.a	a2,a14,d4,#2
	ld.w	d15,[a2]
.L530:
	and	d15,d0
.L531:

; ..\mcal_src\Gtm.c	  1756          if((FupdBackupValue[Count][LoopCount] &      (inlined)
	jeq	d15,#0,.L34
.L205:

; ..\mcal_src\Gtm.c	  1757                  (GTM_TOM_CHAN_ENABLE<<(MinorCnt * GTM_TOM_BITS_PER_CHAN))) >   \ 
; ..\mcal_src\Gtm.c	  1758                  GTM_ZERO_UL)
; ..\mcal_src\Gtm.c	  1759          {
; ..\mcal_src\Gtm.c	  1760            GTM_SFR_INIT_USER_MODE_MODIFY32((TomTgcRegPtr->FUPD_CTRL.U),\ 
	ld.w	d15,[a15]8
	mov	d0,#1
	insert	d15,d15,#0,#16,#16
	add	d6,#1
	sh	d0,d0,d6
.L342:
	or	d15,d0
	st.w	[a15]8,d15

; ..\mcal_src\Gtm.c	  1756          if((FupdBackupValue[Count][LoopCount] &      (inlined)
.L34:
	add	d5,#1

; ..\mcal_src\Gtm.c	  1736        for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_SIXTEEN_U; MinorCnt++)      (inlined)
	loop	a3,.L31
.L532:
	add	d4,#1

; ..\mcal_src\Gtm.c	  1731          LoopCount < GTM_NO_OF_TGC_PER_MODULE; LoopCount++)      (inlined)
	loop	a7,.L30
.L533:
	add	d3,#1
.L534:

; ..\mcal_src\Gtm.c	  1728    for(Count = GTM_ZERO_U; Count < GTM_NO_OF_TOM_MODULES; Count++)      (inlined)
	jlt.u	d3,#2,.L29
.L157:
	mov	d15,#1
	st.w	[a10],d15
.L535:
	ld.a	a15,[a5]4
.L338:

; ..\mcal_src\Gtm.c	  1324    if(Gtm_kConfigPtr->GtmPortConfigPtr != NULL_PTR)      (inlined)
	jz.a	a15,.L35
.L536:
	ld.w	d0,[a10]
.L537:
	mov	d15,#0
.L343:

; ..\mcal_src\Gtm.c	  1326      for(Count = GTM_ZERO_U ; Count < GtmToPortToutSelRegCount ; Count++)      (inlined)
	jeq	d0,#0,.L36
.L37:
	addsc.a	a4,a15,d15,#2
	sha	d0,d15,#5
	movh.a	a2,#61466
	lea	a2,[a2]@los(0xf019fd10)
	addsc.a	a2,a2,d0,#0
	ld.w	d0,[a4]
.L538:
	add	d15,#1
	st.w	[a2],d0
.L344:
	extr.u	d15,d15,#0,#8
.L345:
	ld.w	d0,[a10]
.L539:

; ..\mcal_src\Gtm.c	  1326      for(Count = GTM_ZERO_U ; Count < GtmToPortToutSelRegCount ; Count++)      (inlined)
	jlt.u	d15,d0,.L37
.L36:
	mov	d15,#0
	add.a	a15,#4
.L540:

; ..\mcal_src\Gtm.c	  1332      for(Count = GTM_ZERO_U ; Count < GTM_NO_OF_TOUTSEL_REGISTERS ; Count++)      (inlined)
	mov.a	a2,#7
.L38:
	movh.a	a4,#61466
	lea	a4,[a4]@los(0xf019fd30)
	addsc.a	a4,a4,d15,#2
.L541:
	add	d15,#1
	ld.w	d0,[a15+]
.L542:
	st.w	[a4],d0

; ..\mcal_src\Gtm.c	  1332      for(Count = GTM_ZERO_U ; Count < GTM_NO_OF_TOUTSEL_REGISTERS ; Count++)      (inlined)
	loop	a2,.L38

; ..\mcal_src\Gtm.c	  1324    if(Gtm_kConfigPtr->GtmPortConfigPtr != NULL_PTR)      (inlined)
.L35:
	ld.a	a15,[a6]
.L543:
	ld.w	d15,[a15]60
.L544:

; ..\mcal_src\Gtm.c	  1364    if(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTbuConfigPtr != NULL_PTR)      (inlined)
	jeq	d15,#0,.L39
.L545:
	mov	d0,#0

; ..\mcal_src\Gtm.c	  1370      for(Count =GTM_ZERO_U; Count < GTM_NO_OF_TBU_CHANNELS; Count++)      (inlined)
	mov.a	a2,#2
.L346:
	movh.a	a5,#61456

; ..\mcal_src\Gtm.c	  1370      for(Count =GTM_ZERO_U; Count < GTM_NO_OF_TBU_CHANNELS; Count++)      (inlined)
.L40:
	ld.a	a15,[a6]
.L546:
	mul	d15,d0,#6
.L547:
	ld.a	a15,[a15]60
.L548:
	addsc.a	a4,a15,d15,#0
.L347:
	ld.bu	d15,[a4]
.L549:

; ..\mcal_src\Gtm.c	  1380        if(Gtm_lGetUnused8(TbuConfigPtr->TbuChannelCtrl) == (boolean)FALSE)      (inlined)
	jnz.t	d15:7,.L41
.L550:
	sh	d1,d0,#1
	mov.aa	a15,a5
.L551:
	mov	d15,#1
	lea	a15,[a15]@los(0xf0100104)
	addsc.a	a15,a15,d0,#3
.L552:
	sh	d15,d15,d1
	st.w	[a5]@los(0xf0100100),d15
.L553:
	add	d1,#1
.L554:
	ld.bu	d15,[a4]
	and	d15,#15
	st.w	[a15],d15
.L555:
	ld.w	d15,[a4]2
	st.w	[a15]4,d15
.L556:
	ld.bu	d15,[a4]
.L557:
	and	d15,#32
.L558:
	ne	d15,d15,#0
.L559:
	sh	d15,d15,d1
	st.w	[a5]@los(0xf0100100),d15

; ..\mcal_src\Gtm.c	  1380        if(Gtm_lGetUnused8(TbuConfigPtr->TbuChannelCtrl) == (boolean)FALSE)      (inlined)
.L41:
	add	d0,#1

; ..\mcal_src\Gtm.c	  1370      for(Count =GTM_ZERO_U; Count < GTM_NO_OF_TBU_CHANNELS; Count++)      (inlined)
	loop	a2,.L40

; ..\mcal_src\Gtm.c	  1364    if(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTbuConfigPtr != NULL_PTR)      (inlined)
.L39:
	call	Gtm_lAdcConnectionsConfig
.L218:
	ld.a	a15,[a10]52
.L219:
	mov	d4,#9375
	sh	d4,#4
.L222:
	ld.a	a15,[a15]
	ld.a	a15,[a15]8
	ld.hu	d15,[a15]68
	movh.a	a15,#61466
	st.w	[a15]@los(0xf019fda0),d15
.L223:
	call	Mcal_ResetSafetyENDINIT_Timed
.L560:
	ld.a	a15,[a10]56
.L348:
	ld.a	a15,[a15]8
	ld.w	d15,[a15]10
	movh.a	a15,#61466
.L349:
	st.w	[a15]@los(0xf019fdfc),d15
.L561:
	call	Mcal_SetSafetyENDINIT_Timed
.L5:
	mov	d2,d8
	ret
.L99:
	
__Gtm_Init_function_end:
	.size	Gtm_Init,__Gtm_Init_function_end-Gtm_Init
.L70:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_1')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_1
.L53:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	sha	d15,d3,#11
.L637:
	addsc.a	a2,a12,d15,#0
.L638:
	sha	d15,d4,#9
.L639:
	addsc.a	a15,a2,d15,#0
.L319:
	fret
.L90:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_DeInit')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_DeInit
; Function Gtm_DeInit
.L55:
Gtm_DeInit:	.type	func
	sub.a	a10,#8
.L350:
	call	Gtm_lResetGtmSRCReg
.L228:
	mov	d8,#256
	call	Mcal_ResetENDINIT
.L237:
	movh.a	a15,#61466
	lea	a15,[a15]@los(0xf019fdf4)
	ld.w	d15,[a15]
.L238:
	movh.a	a2,#61466
.L239:
	and	d15,#3
.L351:
	or	d15,#1
	st.w	[a15],d15
.L240:
	ld.w	d15,[a15]
.L352:
	st.w	[a10]4,d15
.L566:
	mov	d15,#1
	st.w	[a2]@los(0xf019fdf0),d15
.L567:
	ld.w	d15,[a2]@los(0xf019fdf0)
.L568:
	st.w	[a10]4,d15
.L569:
	call	Mcal_SetENDINIT

; ..\mcal_src\Gtm.c	  1614                                         (KernelResetTimeOutCount > GTM_ZERO_U));      (inlined)
.L43:
	ld.w	d15,[a15]
.L570:
	add	d8,#-1
.L571:

; ..\mcal_src\Gtm.c	  1613    }while((ResetStatus == GTM_ZERO_U) && \       (inlined)
	jnz.t	d15:1,.L44
.L572:

; ..\mcal_src\Gtm.c	  1614                                         (KernelResetTimeOutCount > GTM_ZERO_U));      (inlined)
	jne	d8,#0,.L43

; ..\mcal_src\Gtm.c	  1613    }while((ResetStatus == GTM_ZERO_U) && \       (inlined)
.L44:
	call	Mcal_ResetENDINIT
.L573:
	movh.a	a15,#61466
	mov	d15,#1
	st.w	[a15]@los(0xf019fdec),d15
.L574:
	call	Mcal_SetENDINIT
.L575:
	ld.w	d15,[a15]@los(0xf019fdec)
.L576:
	st.w	[a10]4,d15
.L577:
	ld.w	d15,[a10]4
.L229:
	call	Mcal_ResetENDINIT
.L242:
	movh.a	a15,#61466
	lea	a15,[a15]@los(0xf019fd00)
	ld.w	d15,[a15]
	and	d15,#11
.L353:
	or	d15,#1
	st.w	[a15],d15
.L243:
	ld.w	d15,[a15]
.L354:
	st.w	[a10],d15
.L578:
	call	Mcal_SetENDINIT
.L579:
	movh.a	a15,#@his(Gtm_kConfigPtr)
.L580:
	mov.a	a2,#0
.L581:
	st.a	[a15]@los(Gtm_kConfigPtr),a2
.L582:
	ld.w	d15,[a10]
.L583:
	ret
.L224:
	
__Gtm_DeInit_function_end:
	.size	Gtm_DeInit,__Gtm_DeInit_function_end-Gtm_DeInit
.L75:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_IsrTomModule')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_IsrTomModule
; Function Gtm_IsrTomModule
.L57:
Gtm_IsrTomModule:	.type	func
	sub.a	a10,#8
.L355:
	mov	e8,d5,d4
	fcall	.cocofun_2
.L358:
	addsc.a	a15,a15,d8,#2
.L259:
	and	d15,d8,#1
.L260:
	ld.w	d10,[a15]36
.L267:
	sh	d0,d15,#4
	movh.a	a15,#61456
.L359:
	sh	d15,d8,#-1
	lea	a15,[a15]@los(0xf0100618)
.L360:
	addsc.a	a15,a15,d15,#2
.L361:
	add	d0,d9
.L362:
	ld.w	d15,[a15]
.L588:
	extr.u	d0,d0,#0,#8
	st.w	[a10],d15
.L363:
	mov	d1,#3
.L589:
	ld.w	d15,[a10]
.L590:
	sh	d1,d1,d0
.L591:
	and	d15,d1
.L592:
	rsub	d0,#0
	sh	d12,d15,d0
.L268:
	sha	d15,d8,#4
.L364:
	addsc.a	a15,a2,d15,#0
.L593:
	mov	d11,#2
.L365:
	lea	a15,[a15]8
.L366:
	addsc.a	a12,a15,d9,#0
.L45:
	add	d11,#-1
.L594:
	mov	d4,#1
.L595:
	sh	d0,d4,d11
.L269:
	and	d0,d12
.L270:
	jeq	d0,#0,.L46
.L367:
	sha	d15,d8,#11
	movh.a	a15,#61457
.L368:
	add	d0,d9,d11
	lea	a15,[a15]@los(0xf0108000)
.L369:
	addsc.a	a15,a15,d15,#0
.L596:
	sha	d15,d0,#6
.L597:
	addsc.a	a15,a15,d15,#0
.L370:
	extr.u	d6,d0,#0,#8
.L598:
	ld.w	d15,[a15]28
.L599:
	sh	d0,d6,#1
.L600:
	extr.u	d7,d15,#0,#16
.L372:
	mov	d15,#3
	st.w	[a15]28,d15
.L601:
	sh	d15,d15,d0
	addsc.a	a15,a12,d11,#0
.L371:
	and	d15,d10
.L602:
	rsub	d0,#0
	ld.bu	d1,[a15]
.L373:
	sh	d15,d15,d0
.L603:
	extr.u	d15,d15,#0,#8
.L604:
	jne	d15,#3,.L47
.L605:
	movh.a	a15,#@his(Gtm_kNotifConfig0)
	lea	a15,[a15]@los(Gtm_kNotifConfig0)
.L606:
	addsc.a	a15,a15,d1,#2
	ld.a	a15,[a15]16
.L374:
	mov	d5,d8
.L375:
	calli	a15
.L47:
.L46:
	jne	d11,#0,.L45
.L607:
	ret
.L245:
	
__Gtm_IsrTomModule_function_end:
	.size	Gtm_IsrTomModule,__Gtm_IsrTomModule_function_end-Gtm_IsrTomModule
.L80:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_2')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_2
.L59:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	movh.a	a15,#@his(Gtm_kConfigPtr)
.L356:
	ld.a	a15,[a15]@los(Gtm_kConfigPtr)
.L644:
	ld.a	a15,[a15]8
.L645:
	ld.a	a2,[a15]52
.L357:
	fret
.L95:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_IsrTimModule')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_IsrTimModule
; Function Gtm_IsrTimModule
.L61:
Gtm_IsrTimModule:	.type	func

; ..\mcal_src\Gtm.c	  1761                  (GTM_FUPD_CLR_MASK),((uint32)((uint32)GTM_TOM_CHAN_DISABLE <<\ 
; ..\mcal_src\Gtm.c	  1762                  ((MinorCnt * GTM_TOM_BITS_PER_CHAN)+GTM_ONE_U))))
; ..\mcal_src\Gtm.c	  1763          }
; ..\mcal_src\Gtm.c	  1764        }
; ..\mcal_src\Gtm.c	  1765      }
; ..\mcal_src\Gtm.c	  1766    }
; ..\mcal_src\Gtm.c	  1767  }
; ..\mcal_src\Gtm.c	  1768  
; ..\mcal_src\Gtm.c	  1769  /*check if MCU InitCheck is enabled*/
; ..\mcal_src\Gtm.c	  1770  #if (GTM_MCUINITCHECK_API == STD_ON)
; ..\mcal_src\Gtm.c	  1771  
; ..\mcal_src\Gtm.c	  1772  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1773  ** Traceability : [cover parentID=DS_MCAL_GTM_0006,
; ..\mcal_src\Gtm.c	  1774  DS_MCAL_GTM_0011,DS_MCAL_GTM_0010,DS_MCAL_GTM_9000]                           **
; ..\mcal_src\Gtm.c	  1775  **                                                                            **
; ..\mcal_src\Gtm.c	  1776  ** Syntax          : void Gtm_InitCheck (const Gtm_ConfigType* ConfigPtr)     **
; ..\mcal_src\Gtm.c	  1777  ** [/cover]                                                                   **
; ..\mcal_src\Gtm.c	  1778  **                                                                            **
; ..\mcal_src\Gtm.c	  1779  ** Service ID      :    None                                                  **
; ..\mcal_src\Gtm.c	  1780  **                                                                            **
; ..\mcal_src\Gtm.c	  1781  ** Sync/Async      :    Synchronous                                           **
; ..\mcal_src\Gtm.c	  1782  **                                                                            **
; ..\mcal_src\Gtm.c	  1783  ** Reentrancy      :    Non Reentrant                                         **
; ..\mcal_src\Gtm.c	  1784  **                                                                            **
; ..\mcal_src\Gtm.c	  1785  ** Parameters (in) :    ConfigPtr - Pointer to GTM Driver configuration set   **
; ..\mcal_src\Gtm.c	  1786  **                                                                            **
; ..\mcal_src\Gtm.c	  1787  ** Parameters (out):    None                                                  **
; ..\mcal_src\Gtm.c	  1788  **                                                                            **
; ..\mcal_src\Gtm.c	  1789  ** Return value    :    E_OK - if initialization comparison is success        **
; ..\mcal_src\Gtm.c	  1790  **                      E_NOT_OK - if initialization comparison fails         **
; ..\mcal_src\Gtm.c	  1791  **                                                                            **
; ..\mcal_src\Gtm.c	  1792  ** Description     : This routine verifies the initialization the GTM driver. **
; ..\mcal_src\Gtm.c	  1793  **                                                                            **
; ..\mcal_src\Gtm.c	  1794  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1795  Std_ReturnType Gtm_InitCheck (const Gtm_ConfigType* ConfigPtr)
; ..\mcal_src\Gtm.c	  1796  {
; ..\mcal_src\Gtm.c	  1797    uint32 TempCompVar = 0x0U;
; ..\mcal_src\Gtm.c	  1798    uint32 CompareVal = GTM_32_XOR_COMPARE_VAL;
; ..\mcal_src\Gtm.c	  1799    uint32 TempClockEnable;
; ..\mcal_src\Gtm.c	  1800    uint32 Count;
; ..\mcal_src\Gtm.c	  1801    const Gtm_GeneralConfigType *GeneralConfigPtr; /* Pointer to General Config */
; ..\mcal_src\Gtm.c	  1802    const Gtm_ClockSettingType *ClockSettingPtr;
; ..\mcal_src\Gtm.c	  1803    Std_ReturnType ErrorFlag;
; ..\mcal_src\Gtm.c	  1804  
; ..\mcal_src\Gtm.c	  1805    ErrorFlag = E_OK;
; ..\mcal_src\Gtm.c	  1806    TempClockEnable = (uint32)((uint32)(((uint32)GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1807                 MODULE_GTM.CLC.U))>> GTM_ONE_U)&GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1808    /* Check if config pointer is NULL or GTM clock is not enabled properly
; ..\mcal_src\Gtm.c	  1809       by Gtm_Init*/
; ..\mcal_src\Gtm.c	  1810    if ((NULL_PTR == ConfigPtr) || (TempClockEnable == GTM_ONE_U))
; ..\mcal_src\Gtm.c	  1811    {
; ..\mcal_src\Gtm.c	  1812      ErrorFlag = E_NOT_OK;
; ..\mcal_src\Gtm.c	  1813    }
; ..\mcal_src\Gtm.c	  1814    else
; ..\mcal_src\Gtm.c	  1815    {
; ..\mcal_src\Gtm.c	  1816    ClockSettingPtr = ConfigPtr->GtmClockSettingPtr;
; ..\mcal_src\Gtm.c	  1817    GeneralConfigPtr = ConfigPtr->GtmModuleConfigPtr->GtmGeneralConfigPtr;
; ..\mcal_src\Gtm.c	  1818  
; ..\mcal_src\Gtm.c	  1819    TempClockEnable = ClockSettingPtr->GtmClockEnable;
; ..\mcal_src\Gtm.c	  1820  
; ..\mcal_src\Gtm.c	  1821    /* check the consistency of CLC register*/
; ..\mcal_src\Gtm.c	  1822    TempCompVar = (((uint32)GTM_SFR_INIT_USER_MODE_READ32(MODULE_GTM.CLC.U))>>\ 
; ..\mcal_src\Gtm.c	  1823                       GTM_CLC_EDIS_OFF_POS);
; ..\mcal_src\Gtm.c	  1824    TempCompVar = TempCompVar & GTM_ONE_U;
; ..\mcal_src\Gtm.c	  1825    if(ConfigPtr->GtmModuleConfigPtr->GtmModuleSleepEnable == GTM_SLEEP_ENABLE)
; ..\mcal_src\Gtm.c	  1826    {
; ..\mcal_src\Gtm.c	  1827      TempCompVar = TempCompVar ^ ~(uint32)(GTM_ZERO_U);
; ..\mcal_src\Gtm.c	  1828    }
; ..\mcal_src\Gtm.c	  1829    else
; ..\mcal_src\Gtm.c	  1830    {
; ..\mcal_src\Gtm.c	  1831      TempCompVar = TempCompVar ^ ~(uint32)(GTM_ONE_U);
; ..\mcal_src\Gtm.c	  1832    }
; ..\mcal_src\Gtm.c	  1833    CompareVal = ( TempCompVar == GTM_32_XOR_COMPARE_VAL) ?
; ..\mcal_src\Gtm.c	  1834                                            CompareVal : (CompareVal & (0U));
; ..\mcal_src\Gtm.c	  1835  
; ..\mcal_src\Gtm.c	  1836    /* check the consistency of GTM CMU registers*/
; ..\mcal_src\Gtm.c	  1837    /* CMU CLK EN register*/
; ..\mcal_src\Gtm.c	  1838    TempCompVar = TempClockEnable ^ GTM_GET_EN_REG_BIT;
; ..\mcal_src\Gtm.c	  1839    TempCompVar = TempCompVar ^ ~(uint32)(GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1840                                     GTM_CMU_CLK_EN.U));
; ..\mcal_src\Gtm.c	  1841    CompareVal = CompareVal ^ (~((uint32)TempCompVar));
; ..\mcal_src\Gtm.c	  1842  
; ..\mcal_src\Gtm.c	  1843    /* CMU CLK NUM and DEN register*/
; ..\mcal_src\Gtm.c	  1844    TempCompVar = ConfigPtr->GtmModuleConfigPtr->GtmGclkNum;
; ..\mcal_src\Gtm.c	  1845    TempCompVar = TempCompVar ^ ~(((uint32)(GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1846                                 GTM_CMU_GCLK_NUM.U))));
; ..\mcal_src\Gtm.c	  1847    CompareVal = CompareVal ^ (~((uint32)TempCompVar));
; ..\mcal_src\Gtm.c	  1848    TempCompVar = ConfigPtr->GtmModuleConfigPtr->GtmGclkDen;
; ..\mcal_src\Gtm.c	  1849    TempCompVar = TempCompVar ^ ~(((uint32)(GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1850                                 GTM_CMU_GCLK_DEN.U))));
; ..\mcal_src\Gtm.c	  1851    CompareVal = CompareVal ^ ~(((uint32)TempCompVar));
; ..\mcal_src\Gtm.c	  1852  
; ..\mcal_src\Gtm.c	  1853  
; ..\mcal_src\Gtm.c	  1854    /* CMU CLK CTRL register configuration*/
; ..\mcal_src\Gtm.c	  1855    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_CLOCKS; Count++)
; ..\mcal_src\Gtm.c	  1856    {
; ..\mcal_src\Gtm.c	  1857      if((TempClockEnable ) &                              \ 
; ..\mcal_src\Gtm.c	  1858              (uint32)(GTM_CMU_CLK_ENABLE << (Count * GTM_CMU_BITS_PER_CLK)))
; ..\mcal_src\Gtm.c	  1859      {
; ..\mcal_src\Gtm.c	  1860        TempCompVar = ClockSettingPtr->GtmCmuClkCnt[Count];
; ..\mcal_src\Gtm.c	  1861        /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	  1862           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	  1863        TempCompVar = TempCompVar ^ ~((uint32)(GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1864           (((Gtm_CmuType*)(void*)&(MODULE_GTM.CMU))->ClkCtrl[Count].CTRL.U))));
; ..\mcal_src\Gtm.c	  1865        CompareVal = CompareVal ^ ~((uint32)(TempCompVar));
; ..\mcal_src\Gtm.c	  1866      }
; ..\mcal_src\Gtm.c	  1867    }
; ..\mcal_src\Gtm.c	  1868  
; ..\mcal_src\Gtm.c	  1869    /* CMU EXT : NUM and DEN  register configuration*/
; ..\mcal_src\Gtm.c	  1870    for(Count = GTM_ZERO_U; Count < GTM_CMU_NO_OF_EXT_CLOCKS; Count++)
; ..\mcal_src\Gtm.c	  1871    {
; ..\mcal_src\Gtm.c	  1872     if(ClockSettingPtr->GtmClockEnable & \ 
; ..\mcal_src\Gtm.c	  1873        (uint32)(GTM_CMU_CLK_ENABLE <<((Count * GTM_CMU_BITS_PER_CLK) + \ 
; ..\mcal_src\Gtm.c	  1874        GTM_CMU_START_FROM_EXTCLK)))
; ..\mcal_src\Gtm.c	  1875     {
; ..\mcal_src\Gtm.c	  1876       TempCompVar = ClockSettingPtr->GtmEclk[Count].CmuEclkNum;
; ..\mcal_src\Gtm.c	  1877       /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	  1878           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	  1879       TempCompVar = TempCompVar ^ ~((uint32)(GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1880             (((Gtm_CmuType*)(void*)&(MODULE_GTM.CMU))->CmuEclk[Count].\ 
; ..\mcal_src\Gtm.c	  1881                CmuEclkNum.U))));
; ..\mcal_src\Gtm.c	  1882       CompareVal = CompareVal ^ ~((uint32)(TempCompVar));
; ..\mcal_src\Gtm.c	  1883  
; ..\mcal_src\Gtm.c	  1884       TempCompVar = ClockSettingPtr->GtmEclk[Count].CmuEclkDen;
; ..\mcal_src\Gtm.c	  1885       /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	  1886           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	  1887       TempCompVar = TempCompVar ^ ~((uint32)(GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1888         (((Gtm_CmuType*)(void *)&(MODULE_GTM.CMU))->CmuEclk[Count].\ 
; ..\mcal_src\Gtm.c	  1889            CmuEclkDen.U))));
; ..\mcal_src\Gtm.c	  1890       CompareVal = CompareVal ^ ~((uint32)(TempCompVar));
; ..\mcal_src\Gtm.c	  1891     }
; ..\mcal_src\Gtm.c	  1892    }
; ..\mcal_src\Gtm.c	  1893  
; ..\mcal_src\Gtm.c	  1894    /* CMU FXCLK register configuration*/
; ..\mcal_src\Gtm.c	  1895    TempCompVar = (ClockSettingPtr->GtmFxdClkControl) & GTM_GET_FIXED_CLK_VAL;
; ..\mcal_src\Gtm.c	  1896    /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm.c	  1897           Permitted for special function registers.*/
; ..\mcal_src\Gtm.c	  1898    TempCompVar = TempCompVar ^ ~((uint32)GTM_SFR_INIT_USER_MODE_READ32(\ 
; ..\mcal_src\Gtm.c	  1899                 (((Gtm_CmuType*)(void*)&(MODULE_GTM.CMU))->GtmFxdClkControl)));
; ..\mcal_src\Gtm.c	  1900    CompareVal = CompareVal ^ ~(TempCompVar);
; ..\mcal_src\Gtm.c	  1901  
; ..\mcal_src\Gtm.c	  1902    /* check the consistency of GTM CTRL register*/
; ..\mcal_src\Gtm.c	  1903    TempCompVar = GTM_SFR_INIT_USER_MODE_READ32(GTM_CTRL.U) ;
; ..\mcal_src\Gtm.c	  1904    TempCompVar = TempCompVar ^ ~((uint32)(GeneralConfigPtr->GtmCtrlValue));
; ..\mcal_src\Gtm.c	  1905    CompareVal = CompareVal ^ ~((uint32)(TempCompVar));
; ..\mcal_src\Gtm.c	  1906    /* check the consistency of GTM IRQ EN register */
; ..\mcal_src\Gtm.c	  1907    TempCompVar = GTM_SFR_INIT_USER_MODE_READ32(GTM_IRQ_EN.U) ;
; ..\mcal_src\Gtm.c	  1908    TempCompVar = TempCompVar ^ ~((uint32)
; ..\mcal_src\Gtm.c	  1909                           (GeneralConfigPtr->GtmIrqEnable & GTM_GET_IRQ_VAL_8));
; ..\mcal_src\Gtm.c	  1910    CompareVal = CompareVal ^ ~((uint32)(TempCompVar));
; ..\mcal_src\Gtm.c	  1911      /* check the consistency of GTM IRQ MODE register */
; ..\mcal_src\Gtm.c	  1912    TempCompVar = GTM_SFR_INIT_USER_MODE_READ32(GTM_IRQ_MODE.U) ;
; ..\mcal_src\Gtm.c	  1913    TempCompVar = TempCompVar ^ ~((uint32)(((GeneralConfigPtr->GtmIrqEnable) & \ 
; ..\mcal_src\Gtm.c	  1914                                     GTM_GET_MODE_VAL_8) >> GTM_SHIFT_TO_LSB_8));
; ..\mcal_src\Gtm.c	  1915    CompareVal = CompareVal ^ ~((uint32)(TempCompVar));
; ..\mcal_src\Gtm.c	  1916    /* check GTM SRN Enable register*/
; ..\mcal_src\Gtm.c	  1917    TempCompVar = (uint32)(GTM_SFR_INIT_USER_MODE_READ32(SRC_GTMAEIIRQ.U) & \ 
; ..\mcal_src\Gtm.c	  1918                             GTM_BIT_SRE_MASK) ;
; ..\mcal_src\Gtm.c	  1919    TempCompVar = TempCompVar ^ ~((uint32)(GTM_BIT_SET << GTM_BIT_SRE));
; ..\mcal_src\Gtm.c	  1920    CompareVal = CompareVal ^ ~((uint32)(TempCompVar));
; ..\mcal_src\Gtm.c	  1921    }
; ..\mcal_src\Gtm.c	  1922    if (!(CompareVal == GTM_32_XOR_COMPARE_VAL))
; ..\mcal_src\Gtm.c	  1923    {
; ..\mcal_src\Gtm.c	  1924      ErrorFlag = E_NOT_OK;
; ..\mcal_src\Gtm.c	  1925    }
; ..\mcal_src\Gtm.c	  1926    return ErrorFlag;
; ..\mcal_src\Gtm.c	  1927  }
; ..\mcal_src\Gtm.c	  1928  
; ..\mcal_src\Gtm.c	  1929  #endif
; ..\mcal_src\Gtm.c	  1930  /* #if (GTM_MCUINITCHECK_API == STD_ON)
; ..\mcal_src\Gtm.c	  1931  */
; ..\mcal_src\Gtm.c	  1932  
; ..\mcal_src\Gtm.c	  1933  #ifdef GTM_TIM_MODULE_USED
; ..\mcal_src\Gtm.c	  1934  
; ..\mcal_src\Gtm.c	  1935  /*******************************************************************************
; ..\mcal_src\Gtm.c	  1936  ** Syntax : IFX_LOCAL_INLINE uint32 Gtm_lGetTimIrqStatus(uint8 ModuleNo,      **
; ..\mcal_src\Gtm.c	  1937  **                                                       uint8 ChannelNumber) **
; ..\mcal_src\Gtm.c	  1938  **                                                                            **
; ..\mcal_src\Gtm.c	  1939  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm.c	  1940  **                                                                            **
; ..\mcal_src\Gtm.c	  1941  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm.c	  1942  **                                                                            **
; ..\mcal_src\Gtm.c	  1943  ** Reentrancy:       reentrant                                                **
; ..\mcal_src\Gtm.c	  1944  **                                                                            **
; ..\mcal_src\Gtm.c	  1945  ** Parameters (in):  ModuleNo - TIM Module Number                             **
; ..\mcal_src\Gtm.c	  1946  **                   ChannelNumber - Channel number (0..7)                    **
; ..\mcal_src\Gtm.c	  1947  **                                                                            **
; ..\mcal_src\Gtm.c	  1948  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm.c	  1949  **                                                                            **
; ..\mcal_src\Gtm.c	  1950  ** Return value:     IrqStatus - Interrupt status of the channels             **
; ..\mcal_src\Gtm.c	  1951  **                                                                            **
; ..\mcal_src\Gtm.c	  1952  ** Description :     This function fetches the interrupt status of the channel**
; ..\mcal_src\Gtm.c	  1953  **                   number                                                   **
; ..\mcal_src\Gtm.c	  1954  *******************************************************************************/
; ..\mcal_src\Gtm.c	  1955  IFX_LOCAL_INLINE uint32 Gtm_lGetTimIrqStatus(uint8 ModuleNo,uint8 ChannelNumber)
; ..\mcal_src\Gtm.c	  1956  {
; ..\mcal_src\Gtm.c	  1957    volatile uint32 RegVal;
; ..\mcal_src\Gtm.c	  1958    uint32 IrqStatus;
; ..\mcal_src\Gtm.c	  1959    uint8 RegPos;
; ..\mcal_src\Gtm.c	  1960  
; ..\mcal_src\Gtm.c	  1961    /* Ascertain the position of Interrupt status for the corresponding module
; ..\mcal_src\Gtm.c	  1962       and channel*/
; ..\mcal_src\Gtm.c	  1963    RegPos = ((ModuleNo % GTM_TIM_MODULES_IN_ICM_REG) * \ 
; ..\mcal_src\Gtm.c	  1964                                     GTM_CHANNELS_PER_TIM_MODULE) + ChannelNumber;
; ..\mcal_src\Gtm.c	  1965  
; ..\mcal_src\Gtm.c	  1966    /* Identify the ICM register corresponding to the Module number */
; ..\mcal_src\Gtm.c	  1967    /* Identify the ICM register corresponding to the Module number */
; ..\mcal_src\Gtm.c	  1968    /* MISRA Rule Violation 11.4 and 17.4 Pointer to Pointer conversion*/
; ..\mcal_src\Gtm.c	  1969    /* Pointer to pointer conversion is performed to simplify the code. Since
; ..\mcal_src\Gtm.c	  1970       each of the ICM register has a different name, to make this available in a
; ..\mcal_src\Gtm.c	  1971       single functions, this conversion is performed. This pointer arithmetic
; ..\mcal_src\Gtm.c	  1972       is desired */
; ..\mcal_src\Gtm.c	  1973  
; ..\mcal_src\Gtm.c	  1974  
; ..\mcal_src\Gtm.c	  1975    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used to efficiently
; ..\mcal_src\Gtm.c	  1976     access the SFRs of multiple GTM ICM registers.*/
; ..\mcal_src\Gtm.c	  1977    RegVal = *(((volatile uint32*)(volatile void *)GTM_TIM_ICM_BASE_ADDRESS) + \ 
	mov	d3,d4
	movh.a	a15,#61456
.L284:
	and	d15,d3,#3
	lea	a15,[a15]@los(0xf0100608)
.L612:
	sh	d0,d15,#3
	sub.a	a10,#8
.L376:

; ..\mcal_src\Gtm.c	  1978                                           (ModuleNo/GTM_TIM_MODULES_IN_ICM_REG));
	insert	d15,d3,#0,#0,#2
.L285:
	mov	d6,d5
.L292:
	add	d0,d6
.L613:
	addsc.a	a15,a15,d15,#0
.L614:
	extr.u	d0,d0,#0,#8
.L378:
	ld.w	d1,[a15]
.L615:
	st.w	[a10],d1
.L616:

; ..\mcal_src\Gtm.c	  1979  
; ..\mcal_src\Gtm.c	  1980  
; ..\mcal_src\Gtm.c	  1981    /* Retrieve the IRQ status of the channel number and the next channel */
; ..\mcal_src\Gtm.c	  1982    IrqStatus = (RegVal & (GTM_GET_TIM_INT_STATUS << RegPos)) >> (RegPos);
	mov	d2,#1
.L617:
	ld.w	d1,[a10]
.L618:
	sh	d2,d2,d0
.L619:
	and	d1,d2
.L620:
	rsub	d0,#0
	sh	d1,d1,d0
.L379:
	jne	d1,#1,.L48
.L293:
	sha	d15,d3,#11
	movh.a	a15,#61456
	lea	a15,[a15]@los(0xf0101000)
.L621:
	addsc.a	a15,a15,d15,#0
.L622:
	sha	d15,d6,#7
.L623:
	addsc.a	a15,a15,d15,#0
.L380:
	ld.w	d15,[a15]44
.L624:
	extr.u	d7,d15,#0,#16
.L381:
	mov	d15,#63
	st.w	[a15]44,d15
.L377:
	fcall	.cocofun_2
.L382:
	addsc.a	a15,a15,d3,#1
.L625:
	sh	d1,d6,#1
	addsc.a	a2,a2,d6,#0
.L384:
	mov	d2,#3
	ld.hu	d15,[a15]18
.L626:
	sh	d2,d2,d1
	ld.bu	d0,[a2]
.L386:
	and	d15,d2
.L627:
	rsub	d1,#0
	sh	d15,d15,d1
.L628:
	extr.u	d15,d15,#0,#8
.L629:
	jne	d15,#2,.L49
.L630:
	movh.a	a15,#@his(Gtm_kNotifConfig0)
	lea	a15,[a15]@los(Gtm_kNotifConfig0)
.L631:
	addsc.a	a15,a15,d0,#2
	ld.a	a15,[a15]
.L632:
	mov	d4,#0
.L385:
	mov	d5,d3
.L383:
	ji	a15
.L49:
.L48:
	ret
.L275:
	
__Gtm_IsrTimModule_function_end:
	.size	Gtm_IsrTimModule,__Gtm_IsrTimModule_function_end-Gtm_IsrTimModule
.L85:
	; End of function
	
	.sdecl	'.bss.CPU0.Private.DEFAULT_RAM_32BIT',data,cluster('Gtm_kConfigPtr')
	.sect	'.bss.CPU0.Private.DEFAULT_RAM_32BIT'
	.global	Gtm_kConfigPtr
	.align	4
Gtm_kConfigPtr:	.type	object
	.size	Gtm_kConfigPtr,4
	.space	4
	.calls	'Gtm_Init','Mcal_ResetENDINIT'
	.calls	'Gtm_Init','Mcal_SetENDINIT'
	.calls	'Gtm_Init','Gtm_lTimConfigure'
	.calls	'Gtm_Init','Gtm_lTomComplexConfig'
	.calls	'Gtm_Init','Gtm_lAdcConnectionsConfig'
	.calls	'Gtm_Init','Mcal_ResetSafetyENDINIT_Timed'
	.calls	'Gtm_Init','Mcal_SetSafetyENDINIT_Timed'
	.calls	'Gtm_DeInit','Gtm_lResetGtmSRCReg'
	.calls	'Gtm_DeInit','Mcal_ResetENDINIT'
	.calls	'Gtm_DeInit','Mcal_SetENDINIT'
	.calls	'Gtm_IsrTomModule','__INDIRECT__'
	.calls	'Gtm_IsrTimModule','__INDIRECT__'
	.calls	'Gtm_Init','.cocofun_1'
	.calls	'Gtm_IsrTomModule','.cocofun_2'
	.calls	'Gtm_IsrTimModule','.cocofun_2'
	.calls	'Gtm_Init','',64
	.calls	'.cocofun_1','',0
	.calls	'Gtm_DeInit','',8
	.calls	'Gtm_IsrTomModule','',8
	.calls	'.cocofun_2','',0
	.extern	Mcal_ResetENDINIT
	.extern	Mcal_SetENDINIT
	.extern	Mcal_ResetSafetyENDINIT_Timed
	.extern	Mcal_SetSafetyENDINIT_Timed
	.extern	Gtm_lAdcConnectionsConfig
	.extern	Gtm_lTomComplexConfig
	.extern	Gtm_lTimConfigure
	.extern	Gtm_lResetGtmSRCReg
	.extern	Gtm_kNotifConfig0
	.extern	__INDIRECT__
	.calls	'Gtm_IsrTimModule','',8
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L63:
	.word	44560
	.half	3
	.word	.L64
	.byte	4
.L62:
	.byte	1
	.byte	'..\\mcal_src\\Gtm.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L65
.L110:
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'Gtm_lGetIntSource',0,3,1,188,1,25
	.word	172
	.byte	1,1,4
	.byte	'Value',0,1,188,1,50
	.word	172
.L98:
	.byte	2
	.byte	'unsigned char',0,1,8,4
	.byte	'Index',0,1,188,1,63
	.word	238
	.byte	5,0,3
	.byte	'Gtm_lGetUnused8',0,3,1,196,1,26
	.word	238
	.byte	1,1,4
	.byte	'CtrlVal',0,1,196,1,48
	.word	238
	.byte	5,0,3
	.byte	'Gtm_lGetBit8',0,3,1,204,1,24
	.word	238
	.byte	1,1,4
	.byte	'Value',0,1,204,1,43
	.word	238
	.byte	4
	.byte	'BitPosition',0,1,204,1,56
	.word	238
	.byte	5,0,3
	.byte	'Gtm_lGetModuleIndex',0,3,1,211,1,24
	.word	238
	.byte	1,1,4
	.byte	'ModUsage',0,1,211,1,51
	.word	172
	.byte	4
	.byte	'Channel',0,1,211,1,67
	.word	238
	.byte	5,0
.L126:
	.byte	6
	.byte	'Gtm_lCmuClockConfigure',0,3,1,140,8,23,1,1
.L127:
	.byte	7,5,5,0,0
.L283:
	.byte	3
	.byte	'Gtm_lGetTimIrqStatus',0,3,1,163,15,25
	.word	172
	.byte	1,1
.L286:
	.byte	4
	.byte	'ModuleNo',0,1,163,15,52
	.word	238
.L288:
	.byte	4
	.byte	'ChannelNumber',0,1,163,15,67
	.word	238
.L290:
	.byte	5,0
.L144:
	.byte	6
	.byte	'Gtm_lCmuExtClkConfigure',0,3,1,197,7,23,1,1
.L146:
	.byte	5,0
.L136:
	.byte	6
	.byte	'Gtm_lCmuConfClkConfigure',0,3,1,144,7,24,1,1
.L138:
	.byte	5,0
.L169:
	.byte	6
	.byte	'Gtm_lSaveTgcStatus',0,3,1,240,8,23,1,1,8,2,157,4,20,128,4,9,3,244,15,9,4,2
	.byte	'unsigned int',0,4,7,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,2
	.byte	'int',0,4,5,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,3,203,8,16,4,2
	.byte	'unsigned int',0,4,7,12
	.byte	'HOST_TRIG',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	755
	.byte	7,24,2,35,0,12
	.byte	'RST_CH0',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'RST_CH1',0,4
	.word	755
	.byte	1,22,2,35,0,12
	.byte	'RST_CH2',0,4
	.word	755
	.byte	1,21,2,35,0,12
	.byte	'RST_CH3',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'RST_CH4',0,4
	.word	755
	.byte	1,19,2,35,0,12
	.byte	'RST_CH5',0,4
	.word	755
	.byte	1,18,2,35,0,12
	.byte	'RST_CH6',0,4
	.word	755
	.byte	1,17,2,35,0,12
	.byte	'RST_CH7',0,4
	.word	755
	.byte	1,16,2,35,0,12
	.byte	'UPEN_CTRL0',0,4
	.word	755
	.byte	2,14,2,35,0,12
	.byte	'UPEN_CTRL1',0,4
	.word	755
	.byte	2,12,2,35,0,12
	.byte	'UPEN_CTRL2',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'UPEN_CTRL3',0,4
	.word	755
	.byte	2,8,2,35,0,12
	.byte	'UPEN_CTRL4',0,4
	.word	755
	.byte	2,6,2,35,0,12
	.byte	'UPEN_CTRL5',0,4
	.word	755
	.byte	2,4,2,35,0,12
	.byte	'UPEN_CTRL6',0,4
	.word	755
	.byte	2,2,2,35,0,12
	.byte	'UPEN_CTRL7',0,4
	.word	755
	.byte	2,0,2,35,0,0,10
	.byte	'B',0,4
	.word	717
	.byte	2,35,0,0,10
	.byte	'GLB_CTRL',0,4
	.word	666
	.byte	2,35,0,9,3,212,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,3,145,8,16,4,12
	.byte	'ACT_TB',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'TB_TRIG',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'TBU_SEL',0,4
	.word	755
	.byte	2,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	755
	.byte	5,0,2,35,0,0,10
	.byte	'B',0,4
	.word	1201
	.byte	2,35,0,0,10
	.byte	'ACT_TB',0,4
	.word	1173
	.byte	2,35,4,9,3,236,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,3,182,8,16,4,12
	.byte	'FUPD_CTRL0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'FUPD_CTRL1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'FUPD_CTRL2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'FUPD_CTRL3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'FUPD_CTRL4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'FUPD_CTRL5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'FUPD_CTRL6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'FUPD_CTRL7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'RSTCN0_CH0',0,4
	.word	755
	.byte	2,14,2,35,0,12
	.byte	'RSTCN0_CH1',0,4
	.word	755
	.byte	2,12,2,35,0,12
	.byte	'RSTCN0_CH2',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'RSTCN0_CH3',0,4
	.word	755
	.byte	2,8,2,35,0,12
	.byte	'RSTCN0_CH4',0,4
	.word	755
	.byte	2,6,2,35,0,12
	.byte	'RSTCN0_CH5',0,4
	.word	755
	.byte	2,4,2,35,0,12
	.byte	'RSTCN0_CH6',0,4
	.word	755
	.byte	2,2,2,35,0,12
	.byte	'RSTCN0_CH7',0,4
	.word	755
	.byte	2,0,2,35,0,0,10
	.byte	'B',0,4
	.word	1373
	.byte	2,35,0,0,10
	.byte	'FUPD_CTRL',0,4
	.word	1345
	.byte	2,35,8,9,3,252,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,3,226,8,16,4,12
	.byte	'INT_TRIG0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'INT_TRIG1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'INT_TRIG2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'INT_TRIG3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'INT_TRIG4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'INT_TRIG5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'INT_TRIG6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'INT_TRIG7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	1824
	.byte	2,35,0,0,10
	.byte	'INT_TRIG',0,4
	.word	1796
	.byte	2,35,12,13,48
	.word	238
	.byte	14,47,0,10
	.byte	'reserved_tgc0',0,48
	.word	2084
	.byte	2,35,16,9,3,220,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,3,154,8,16,4,12
	.byte	'ENDIS_CTRL0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_CTRL1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_CTRL2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'ENDIS_CTRL3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'ENDIS_CTRL4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'ENDIS_CTRL5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'ENDIS_CTRL6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'ENDIS_CTRL7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	2144
	.byte	2,35,0,0,10
	.byte	'ENDIS_CTRL',0,4
	.word	2116
	.byte	2,35,64,9,3,228,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,3,168,8,16,4,12
	.byte	'ENDIS_STAT0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_STAT1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_STAT2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'ENDIS_STAT3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'ENDIS_STAT4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'ENDIS_STAT5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'ENDIS_STAT6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'ENDIS_STAT7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	2452
	.byte	2,35,0,0,10
	.byte	'ENDIS_STAT',0,4
	.word	2424
	.byte	2,35,68,9,3,132,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,3,240,8,16,4,12
	.byte	'OUTEN_CTRL0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'OUTEN_CTRL1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'OUTEN_CTRL2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'OUTEN_CTRL3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'OUTEN_CTRL4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'OUTEN_CTRL5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'OUTEN_CTRL6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'OUTEN_CTRL7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	2760
	.byte	2,35,0,0,10
	.byte	'OUTEN_CTRL',0,4
	.word	2732
	.byte	2,35,72,9,3,140,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,3,254,8,16,4,12
	.byte	'OUTEN_STAT0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'OUTEN_STAT1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'OUTEN_STAT2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'OUTEN_STAT3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'OUTEN_STAT4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'OUTEN_STAT5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'OUTEN_STAT6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'OUTEN_STAT7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	3068
	.byte	2,35,0,0,10
	.byte	'OUTEN_STAT',0,4
	.word	3040
	.byte	2,35,76,13,176,3
	.word	238
	.byte	14,175,3,0,10
	.byte	'reserved_tgc1',0,176,3
	.word	3348
	.byte	2,35,80,0,15
	.word	659
.L160:
	.byte	16
	.word	3384
.L172:
	.byte	4
	.byte	'TomTgcRegPtr',0,1,240,8,64
	.word	3389
.L174:
	.byte	7
.L176:
	.byte	5
.L179:
	.byte	5,0,0
.L182:
	.byte	6
	.byte	'Gtm_lTomTgcConfigure',0,3,1,200,9,23,1,1
.L185:
	.byte	5,0
.L207:
	.byte	6
	.byte	'Gtm_lPortConfig',0,3,1,165,10,23,1,1
.L208:
	.byte	5,0
.L212:
	.byte	6
	.byte	'Gtm_lTbuConfig',0,3,1,205,10,23,1,1
.L213:
	.byte	7,5,5,0,0
.L217:
	.byte	6
	.byte	'Gtm_lTtcanConnectionsConfig',0,3,1,135,11,23,1,1
.L220:
	.byte	5,0
.L104:
	.byte	3
	.byte	'Gtm_lEnableGtm',0,3,1,159,11,33
	.word	238
	.byte	1,1
.L107:
	.byte	7
.L112:
	.byte	5
.L116:
	.byte	7
.L120:
	.byte	5
.L123:
	.byte	5,0,0,0
.L227:
	.byte	6
	.byte	'Gtm_lResetKernelDeInit',0,3,1,177,12,23,1,1
.L230:
	.byte	7
.L235:
	.byte	5,0,0
.L155:
	.byte	6
	.byte	'Gtm_lTomConfigure',0,3,1,239,12,23,1,1
.L158:
	.byte	7
.L198:
	.byte	5
.L201:
	.byte	5
.L204:
	.byte	5,5,5,5,0,0
.L258:
	.byte	3
	.byte	'Gtm_lGetTomIrqStatus',0,3,1,219,6,25
	.word	172
	.byte	1,1
.L261:
	.byte	4
	.byte	'ModuleNo',0,1,219,6,52
	.word	238
.L263:
	.byte	4
	.byte	'ChannelNumber',0,1,219,6,67
	.word	238
.L265:
	.byte	5,0,17
	.byte	'Mcal_ResetENDINIT',0,4,119,13,1,1,1,1,17
	.byte	'Mcal_SetENDINIT',0,4,146,1,13,1,1,1,1,18
	.byte	'Mcal_ResetSafetyENDINIT_Timed',0,4,190,2,13,1,1,1,1,4
	.byte	'TimeOut',0,4,190,2,50
	.word	172
	.byte	0,17
	.byte	'Mcal_SetSafetyENDINIT_Timed',0,4,214,2,13,1,1,1,1,11
	.byte	'Gtm_ConfigType',0,2,192,7,16,12,8,2,230,6,9,64,10
	.byte	'GtmClockEnable',0,4
	.word	172
	.byte	2,35,0,13,32
	.word	172
	.byte	14,7,0,10
	.byte	'GtmCmuClkCnt',0,32
	.word	3921
	.byte	2,35,4,10
	.byte	'GtmFxdClkControl',0,4
	.word	172
	.byte	2,35,36,8,2,223,6,9,8,10
	.byte	'CmuEclkNum',0,4
	.word	172
	.byte	2,35,0,10
	.byte	'CmuEclkDen',0,4
	.word	172
	.byte	2,35,4,0,13,24
	.word	3978
	.byte	14,2,0,10
	.byte	'GtmEclk',0,24
	.word	4025
	.byte	2,35,40,0,19
	.word	3891
.L128:
	.byte	16
	.word	4052
	.byte	10
	.byte	'GtmClockSettingPtr',0,4
	.word	4057
	.byte	2,35,0,8,2,249,5,9,36,13,4
	.word	172
	.byte	14,0,0,10
	.byte	'TimInSel',0,4
	.word	4096
	.byte	2,35,0,10
	.byte	'ToutSel',0,32
	.word	3921
	.byte	2,35,4,0,19
	.word	4090
	.byte	16
	.word	4141
	.byte	10
	.byte	'GtmPortConfigPtr',0,4
	.word	4146
	.byte	2,35,4,8,2,129,7,9,72,10
	.byte	'GtmModuleSleepEnable',0,1
	.word	238
	.byte	2,35,0,10
	.byte	'GtmGclkNum',0,4
	.word	172
	.byte	2,35,2,10
	.byte	'GtmGclkDen',0,4
	.word	172
	.byte	2,35,6,10
	.byte	'GtmAccessEnable0',0,4
	.word	172
	.byte	2,35,10,10
	.byte	'GtmAccessEnable1',0,4
	.word	172
	.byte	2,35,14
.L251:
	.byte	2
	.byte	'unsigned short int',0,2,7,13,2
	.word	4305
	.byte	14,0,0,10
	.byte	'GtmTimModuleUsage',0,2
	.word	4327
	.byte	2,35,18,13,1
	.word	238
	.byte	14,0,0,10
	.byte	'GtmTimUsage',0,1
	.word	4363
	.byte	2,35,20,8,2,138,6,11,24,10
	.byte	'TimUsage',0,1
	.word	238
	.byte	2,35,0,10
	.byte	'TimIrqEn',0,1
	.word	238
	.byte	2,35,1,10
	.byte	'TimErrIrqEn',0,1
	.word	238
	.byte	2,35,2,10
	.byte	'TimExtCapSrc',0,1
	.word	238
	.byte	2,35,3,10
	.byte	'TimCtrlValue',0,4
	.word	172
	.byte	2,35,4,8,2,129,6,9,8,10
	.byte	'TimRisingEdgeFilter',0,4
	.word	172
	.byte	2,35,0,10
	.byte	'TimFallingEdgeFilter',0,4
	.word	172
	.byte	2,35,4,0,19
	.word	4500
	.byte	16
	.word	4566
	.byte	10
	.byte	'GtmTimFltPtr',0,4
	.word	4571
	.byte	2,35,8,10
	.byte	'TimCntsValue',0,4
	.word	172
	.byte	2,35,12,10
	.byte	'TimTduValue',0,4
	.word	172
	.byte	2,35,16,10
	.byte	'TimInSrcSel',0,4
	.word	172
	.byte	2,35,20,0,19
	.word	4393
	.byte	16
	.word	4663
	.byte	10
	.byte	'GtmTimConfigPtr',0,4
	.word	4668
	.byte	2,35,24,10
	.byte	'GtmTomTgcUsage',0,1
	.word	4363
	.byte	2,35,28,8,2,189,6,9,12,10
	.byte	'GtmTomIntTrig',0,2
	.word	4305
	.byte	2,35,0,10
	.byte	'GtmTomActTb',0,4
	.word	172
	.byte	2,35,2,8,2,177,6,9,16,10
	.byte	'GtmTomUpdateEn',0,2
	.word	4305
	.byte	2,35,0,10
	.byte	'GtmTomEndisCtrl',0,2
	.word	4305
	.byte	2,35,2,10
	.byte	'GtmTomEndisStat',0,2
	.word	4305
	.byte	2,35,4,10
	.byte	'GtmTomOutenCtrl',0,2
	.word	4305
	.byte	2,35,6,10
	.byte	'GtmTomOutenStat',0,2
	.word	4305
	.byte	2,35,8,10
	.byte	'GtmTomFupd',0,4
	.word	172
	.byte	2,35,10,0,19
	.word	4772
	.byte	16
	.word	4923
	.byte	10
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	4928
	.byte	2,35,8,0,19
	.word	4722
.L188:
	.byte	16
	.word	4965
	.byte	10
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	4970
	.byte	2,35,32,13,8
	.word	172
	.byte	14,1,0,10
	.byte	'GtmTomModuleUsage',0,8
	.word	5003
	.byte	2,35,36,10
	.byte	'GtmTomUsage',0,4
	.word	4096
	.byte	2,35,44,8,2,211,6,9,12,10
	.byte	'TomUsage',0,1
	.word	238
	.byte	2,35,0,10
	.byte	'GtmTomIrqMode',0,1
	.word	238
	.byte	2,35,1,10
	.byte	'GtmTomControlWord',0,4
	.word	172
	.byte	2,35,2,8,2,199,6,9,12,10
	.byte	'GtmTomIrqEn',0,1
	.word	238
	.byte	2,35,0,10
	.byte	'GtmTomCn0Value',0,2
	.word	4305
	.byte	2,35,2,10
	.byte	'GtmTomCm0Value',0,2
	.word	4305
	.byte	2,35,4,10
	.byte	'GtmTomCm1Value',0,2
	.word	4305
	.byte	2,35,6,10
	.byte	'GtmTomSr0Value',0,2
	.word	4305
	.byte	2,35,8,10
	.byte	'GtmTomSr1Value',0,2
	.word	4305
	.byte	2,35,10,0,19
	.word	5134
	.byte	16
	.word	5282
	.byte	10
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	5287
	.byte	2,35,8,0,19
	.word	5060
	.byte	16
	.word	5322
	.byte	10
	.byte	'GtmTomConfigPtr',0,4
	.word	5327
	.byte	2,35,48,8,2,154,6,11,40,13,8
	.word	238
	.byte	14,7,0,10
	.byte	'Gtm_TimUsage',0,8
	.word	5363
	.byte	2,35,0,13,16
	.word	238
	.byte	14,15,0,13,32
	.word	5394
	.byte	14,1,0,10
	.byte	'Gtm_TomUsage',0,32
	.word	5403
	.byte	2,35,8,0,19
	.word	5357
.L256:
	.byte	16
	.word	5435
	.byte	10
	.byte	'GtmModUsageConfigPtr',0,4
	.word	5440
	.byte	2,35,52,8,2,240,6,9,4,10
	.byte	'GtmCtrlValue',0,2
	.word	4305
	.byte	2,35,0,10
	.byte	'GtmIrqEnable',0,2
	.word	4305
	.byte	2,35,2,0,19
	.word	5475
.L102:
	.byte	16
	.word	5526
	.byte	10
	.byte	'GtmGeneralConfigPtr',0,4
	.word	5531
	.byte	2,35,56,8,2,249,6,9,6,10
	.byte	'TbuChannelCtrl',0,1
	.word	238
	.byte	2,35,0,10
	.byte	'TbuBaseValue',0,4
	.word	172
	.byte	2,35,2,0,19
	.word	5565
.L214:
	.byte	16
	.word	5618
	.byte	10
	.byte	'GtmTbuConfigPtr',0,4
	.word	5623
	.byte	2,35,60,19
	.word	238
	.byte	16
	.word	5653
	.byte	10
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	5658
	.byte	2,35,64,10
	.byte	'GtmTtcanTriggers',0,2
	.word	4327
	.byte	2,35,68,0,19
	.word	4177
	.byte	16
	.word	5720
	.byte	10
	.byte	'GtmModuleConfigPtr',0,4
	.word	5725
	.byte	2,35,8,0,19
	.word	3870
.L100:
	.byte	16
	.word	5759
.L118:
	.byte	15
	.word	172
.L162:
	.byte	13,16
	.word	5003
	.byte	14,1,0
.L196:
	.byte	15
	.word	172
.L210:
	.byte	15
	.word	172
.L225:
	.byte	15
	.word	172
.L232:
	.byte	15
	.word	172
	.byte	8,2,130,4,20,64,9,3,148,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,3,203,7,16,4,12
	.byte	'reserved_0',0,4
	.word	755
	.byte	11,21,2,35,0,12
	.byte	'SL',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'CLK_SRC_SR',0,4
	.word	755
	.byte	3,17,2,35,0,12
	.byte	'reserved_15',0,4
	.word	755
	.byte	5,12,2,35,0,12
	.byte	'RST_CCU0',0,4
	.word	755
	.byte	1,11,2,35,0,12
	.byte	'OSM_TRIG',0,4
	.word	755
	.byte	1,10,2,35,0,12
	.byte	'EXT_TRIG',0,4
	.word	755
	.byte	1,9,2,35,0,12
	.byte	'EXTTRIGOUT',0,4
	.word	755
	.byte	1,8,2,35,0,12
	.byte	'TRIGOUT',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'reserved_25',0,4
	.word	755
	.byte	1,6,2,35,0,12
	.byte	'OSM',0,4
	.word	755
	.byte	1,5,2,35,0,12
	.byte	'BITREV',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'reserved_28',0,4
	.word	755
	.byte	4,0,2,35,0,0,10
	.byte	'B',0,4
	.word	5837
	.byte	2,35,0,0,10
	.byte	'CTRL',0,4
	.word	5809
	.byte	2,35,0,9,3,188,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,3,252,7,16,4,12
	.byte	'SR0',0,4
	.word	755
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	6185
	.byte	2,35,0,0,10
	.byte	'SR0',0,4
	.word	6157
	.byte	2,35,4,9,3,196,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,3,131,8,16,4,12
	.byte	'SR1',0,4
	.word	755
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	6308
	.byte	2,35,0,0,10
	.byte	'SR1',0,4
	.word	6280
	.byte	2,35,8,9,3,252,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,3,182,7,16,4,12
	.byte	'CM0',0,4
	.word	755
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	6431
	.byte	2,35,0,0,10
	.byte	'CM0',0,4
	.word	6403
	.byte	2,35,12,9,3,132,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,3,189,7,16,4,12
	.byte	'CM1',0,4
	.word	755
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	6554
	.byte	2,35,0,0,10
	.byte	'CM1',0,4
	.word	6526
	.byte	2,35,16,9,3,140,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,3,196,7,16,4,12
	.byte	'CN0',0,4
	.word	755
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	6677
	.byte	2,35,0,0,10
	.byte	'CN0',0,4
	.word	6649
	.byte	2,35,20,9,3,204,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,3,138,8,16,4,12
	.byte	'OL',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	755
	.byte	31,0,2,35,0,0,10
	.byte	'B',0,4
	.word	6800
	.byte	2,35,0,0,10
	.byte	'STAT',0,4
	.word	6772
	.byte	2,35,24,9,3,180,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,3,244,7,16,4,12
	.byte	'CCU0TC',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'CCU1TC',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	30,0,2,35,0,0,10
	.byte	'B',0,4
	.word	6923
	.byte	2,35,0,0,10
	.byte	'IRQ_NOTIFY',0,4
	.word	6895
	.byte	2,35,28,9,3,156,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,3,221,7,16,4,12
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	30,0,2,35,0,0,10
	.byte	'B',0,4
	.word	7080
	.byte	2,35,0,0,10
	.byte	'IRQ_EN',0,4
	.word	7052
	.byte	2,35,32,9,3,164,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,3,229,7,16,4,12
	.byte	'TRG_CCU0TC0',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'TRG_CCU1TC0',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	30,0,2,35,0,0,10
	.byte	'B',0,4
	.word	7243
	.byte	2,35,0,0,10
	.byte	'IRQ_FORCINT',0,4
	.word	7215
	.byte	2,35,36,9,3,172,15,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,3,237,7,16,4,12
	.byte	'IRQ_MODE',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	30,0,2,35,0,0,10
	.byte	'B',0,4
	.word	7412
	.byte	2,35,0,0,10
	.byte	'IRQ_MODE',0,4
	.word	7384
	.byte	2,35,40,13,20
	.word	238
	.byte	14,19,0,10
	.byte	'reserved_2C',0,20
	.word	7521
	.byte	2,35,44,0,15
	.word	5803
.L254:
	.byte	16
	.word	7552
.L271:
	.byte	15
	.word	172
	.byte	8,2,212,4,20,128,1,9,3,164,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,3,193,6,16,4,12
	.byte	'GPR0',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'ECNT',0,4
	.word	755
	.byte	8,0,2,35,0,0,10
	.byte	'B',0,4
	.word	7602
	.byte	2,35,0,0,10
	.byte	'CH_GPR0',0,4
	.word	7574
	.byte	2,35,0,9,3,172,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,3,200,6,16,4,12
	.byte	'GPR1',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'ECNT',0,4
	.word	755
	.byte	8,0,2,35,0,0,10
	.byte	'B',0,4
	.word	7724
	.byte	2,35,0,0,10
	.byte	'CH_GPR1',0,4
	.word	7696
	.byte	2,35,4,9,3,228,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,3,238,5,16,4,12
	.byte	'CNT',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,10
	.byte	'B',0,4
	.word	7846
	.byte	2,35,0,0,10
	.byte	'CH_CNT',0,4
	.word	7818
	.byte	2,35,8,9,3,252,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,3,153,6,16,4,12
	.byte	'ECNT',0,4
	.word	755
	.byte	16,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,10
	.byte	'B',0,4
	.word	7972
	.byte	2,35,0,0,10
	.byte	'CH_ECNT',0,4
	.word	7944
	.byte	2,35,12,9,3,236,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,3,245,5,16,4,12
	.byte	'CNTS',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'ECNT',0,4
	.word	755
	.byte	8,0,2,35,0,0,10
	.byte	'B',0,4
	.word	8101
	.byte	2,35,0,0,10
	.byte	'CH_CNTS',0,4
	.word	8073
	.byte	2,35,16,9,3,212,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,3,250,6,16,4,12
	.byte	'TO_CNT',0,4
	.word	755
	.byte	8,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	755
	.byte	24,0,2,35,0,0,10
	.byte	'B',0,4
	.word	8223
	.byte	2,35,0,0,10
	.byte	'CH_TDUC',0,4
	.word	8195
	.byte	2,35,20,9,3,220,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,3,129,7,16,4,12
	.byte	'TOV',0,4
	.word	755
	.byte	8,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	755
	.byte	20,4,2,35,0,12
	.byte	'TCS',0,4
	.word	755
	.byte	3,1,2,35,0,12
	.byte	'reserved_31',0,4
	.word	755
	.byte	1,0,2,35,0,0,10
	.byte	'B',0,4
	.word	8353
	.byte	2,35,0,0,10
	.byte	'CH_TDUV',0,4
	.word	8325
	.byte	2,35,24,9,3,156,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,3,186,6,16,4,12
	.byte	'FLT_RE',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,10
	.byte	'B',0,4
	.word	8518
	.byte	2,35,0,0,10
	.byte	'CH_FLT_RE',0,4
	.word	8490
	.byte	2,35,28,9,3,148,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,3,179,6,16,4,12
	.byte	'FLT_FE',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,10
	.byte	'B',0,4
	.word	8653
	.byte	2,35,0,0,10
	.byte	'CH_FLT_FE',0,4
	.word	8625
	.byte	2,35,32,9,3,244,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,3,252,5,16,4,12
	.byte	'TIM_EN',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'TIM_MODE',0,4
	.word	755
	.byte	3,28,2,35,0,12
	.byte	'OSM',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'CICTRL',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'TBU0x_SEL',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'GPR0_SEL',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'GPR1_SEL',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'CNTS_SEL',0,4
	.word	755
	.byte	1,19,2,35,0,12
	.byte	'DSL',0,4
	.word	755
	.byte	1,18,2,35,0,12
	.byte	'ISL',0,4
	.word	755
	.byte	1,17,2,35,0,12
	.byte	'ECNT_RESET',0,4
	.word	755
	.byte	1,16,2,35,0,12
	.byte	'FLT_EN',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'FLT_CNT_FRQ',0,4
	.word	755
	.byte	2,13,2,35,0,12
	.byte	'EXT_CAP_EN',0,4
	.word	755
	.byte	1,12,2,35,0,12
	.byte	'FLT_MODE_RE',0,4
	.word	755
	.byte	1,11,2,35,0,12
	.byte	'FLT_CTR_RE',0,4
	.word	755
	.byte	1,10,2,35,0,12
	.byte	'FLT_MODE_FE',0,4
	.word	755
	.byte	1,9,2,35,0,12
	.byte	'FLT_CTR_FE',0,4
	.word	755
	.byte	1,8,2,35,0,12
	.byte	'CLK_SEL',0,4
	.word	755
	.byte	3,5,2,35,0,12
	.byte	'FR_ECNT_OFL',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'EGPR0_SEL',0,4
	.word	755
	.byte	1,3,2,35,0,12
	.byte	'EGPR1_SEL',0,4
	.word	755
	.byte	1,2,2,35,0,12
	.byte	'TOCTRL',0,4
	.word	755
	.byte	2,0,2,35,0,0,10
	.byte	'B',0,4
	.word	8788
	.byte	2,35,0,0,10
	.byte	'CH_CTRL',0,4
	.word	8760
	.byte	2,35,36,9,3,132,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,3,160,6,16,4,12
	.byte	'EXT_CAP_SRC',0,4
	.word	755
	.byte	3,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	755
	.byte	29,0,2,35,0,0,10
	.byte	'B',0,4
	.word	9359
	.byte	2,35,0,0,10
	.byte	'CH_ECTRL',0,4
	.word	9331
	.byte	2,35,40,9,3,204,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,3,238,6,16,4,12
	.byte	'NEWVAL',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'ECNTOFL',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'CNTOFL',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'GPRzOFL',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'TODET',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'GLITCHDET',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	755
	.byte	26,0,2,35,0,0,10
	.byte	'B',0,4
	.word	9496
	.byte	2,35,0,0,10
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	9468
	.byte	2,35,44,9,3,180,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,3,207,6,16,4,12
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'TODET_IRQ_EN',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	755
	.byte	26,0,2,35,0,0,10
	.byte	'B',0,4
	.word	9732
	.byte	2,35,0,0,10
	.byte	'CH_IRQ_EN',0,4
	.word	9704
	.byte	2,35,48,9,3,188,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,3,219,6,16,4,12
	.byte	'TRG_NEWVAL',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'TRG_ECNTOFL',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'TRG_CNTOFL',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'TRG_GPRzOFL',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'TRG_TODET',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'TRG_GLITCHDET',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	755
	.byte	26,0,2,35,0,0,10
	.byte	'B',0,4
	.word	10002
	.byte	2,35,0,0,10
	.byte	'CH_IRQ_FORCINT',0,4
	.word	9974
	.byte	2,35,52,9,3,196,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,3,231,6,16,4,12
	.byte	'IRQ_MODE',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	30,0,2,35,0,0,10
	.byte	'B',0,4
	.word	10264
	.byte	2,35,0,0,10
	.byte	'CH_IRQ_MODE',0,4
	.word	10236
	.byte	2,35,56,9,3,140,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,11
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,3,167,6,16,4,12
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'TODET_EIRQ_EN',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	755
	.byte	26,0,2,35,0,0,10
	.byte	'B',0,4
	.word	10404
	.byte	2,35,0,0,10
	.byte	'CH_EIRQ_EN',0,4
	.word	10376
	.byte	2,35,60,13,64
	.word	238
	.byte	14,63,0,10
	.byte	'reserved_40',0,64
	.word	10654
	.byte	2,35,64,0,15
	.word	7567
.L280:
	.byte	16
	.word	10685
.L294:
	.byte	15
	.word	172
	.byte	17
	.byte	'Gtm_lAdcConnectionsConfig',0,5,81,13,1,1,1,1,17
	.byte	'Gtm_lTomComplexConfig',0,5,82,13,1,1,1,1,17
	.byte	'Gtm_lTimConfigure',0,5,84,13,1,1,1,1,17
	.byte	'Gtm_lResetGtmSRCReg',0,5,88,13,1,1,1,1,20
	.word	193
	.byte	21
	.word	223
	.byte	21
	.word	255
	.byte	5,0,20
	.word	272
	.byte	21
	.word	300
	.byte	5,0,20
	.word	319
	.byte	21
	.word	344
	.byte	21
	.word	359
	.byte	5,0,20
	.word	382
	.byte	21
	.word	414
	.byte	21
	.word	432
	.byte	5,0,20
	.word	451
	.byte	7,22
	.word	597
	.byte	23
	.word	630
	.byte	0,5,22
	.word	563
	.byte	23
	.word	595
	.byte	0,5,0,0,20
	.word	487
	.byte	21
	.word	520
	.byte	21
	.word	538
	.byte	5,0,20
	.word	563
	.byte	5,0,20
	.word	597
	.byte	5,0,20
	.word	632
	.byte	21
	.word	3394
	.byte	7,5,5,0,0,20
	.word	3421
	.byte	5,0,20
	.word	3452
	.byte	5,0,20
	.word	3478
	.byte	7,22
	.word	272
	.byte	21
	.word	300
	.byte	23
	.word	317
	.byte	0,5,22
	.word	319
	.byte	21
	.word	344
	.byte	21
	.word	359
	.byte	23
	.word	380
	.byte	0,5,0,0,20
	.word	3506
	.byte	5,0,20
	.word	3544
	.byte	7,5,7,5,5,0,0,0,20
	.word	3579
	.byte	7,5,0,0,20
	.word	3614
	.byte	7,5,5,5,22
	.word	382
	.byte	21
	.word	414
	.byte	21
	.word	432
	.byte	23
	.word	449
	.byte	0,5,22
	.word	632
	.byte	21
	.word	3394
	.byte	24
	.word	3416
	.byte	23
	.word	3417
	.byte	23
	.word	3418
	.byte	0,0,5,22
	.word	3421
	.byte	23
	.word	3450
	.byte	0,5,0,0,20
	.word	3649
	.byte	21
	.word	3682
	.byte	21
	.word	3700
	.byte	5,0,25
	.byte	'__INDIRECT__',0,1,1,1,1,1,1,26
	.byte	'void',0,16
	.word	11159
	.byte	27
	.byte	'__prof_adm',0,1,1,1
	.word	11165
	.byte	28,1,16
	.word	11189
	.byte	27
	.byte	'__codeptr',0,1,1,1
	.word	11191
	.byte	27
	.byte	'uint8',0,6,90,29
	.word	238
	.byte	27
	.byte	'uint16',0,6,92,29
	.word	4305
	.byte	27
	.byte	'uint32',0,6,94,29
	.word	172
	.byte	27
	.byte	'boolean',0,6,105,29
	.word	238
	.byte	27
	.byte	'Std_ReturnType',0,7,113,15
	.word	238
	.byte	11
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,3,45,16,4,12
	.byte	'EN0',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'EN1',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'EN2',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'EN3',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'EN4',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'EN5',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'EN6',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'EN7',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'EN8',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'EN9',0,4
	.word	755
	.byte	1,22,2,35,0,12
	.byte	'EN10',0,4
	.word	755
	.byte	1,21,2,35,0,12
	.byte	'EN11',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'EN12',0,4
	.word	755
	.byte	1,19,2,35,0,12
	.byte	'EN13',0,4
	.word	755
	.byte	1,18,2,35,0,12
	.byte	'EN14',0,4
	.word	755
	.byte	1,17,2,35,0,12
	.byte	'EN15',0,4
	.word	755
	.byte	1,16,2,35,0,12
	.byte	'EN16',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'EN17',0,4
	.word	755
	.byte	1,14,2,35,0,12
	.byte	'EN18',0,4
	.word	755
	.byte	1,13,2,35,0,12
	.byte	'EN19',0,4
	.word	755
	.byte	1,12,2,35,0,12
	.byte	'EN20',0,4
	.word	755
	.byte	1,11,2,35,0,12
	.byte	'EN21',0,4
	.word	755
	.byte	1,10,2,35,0,12
	.byte	'EN22',0,4
	.word	755
	.byte	1,9,2,35,0,12
	.byte	'EN23',0,4
	.word	755
	.byte	1,8,2,35,0,12
	.byte	'EN24',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'EN25',0,4
	.word	755
	.byte	1,6,2,35,0,12
	.byte	'EN26',0,4
	.word	755
	.byte	1,5,2,35,0,12
	.byte	'EN27',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'EN28',0,4
	.word	755
	.byte	1,3,2,35,0,12
	.byte	'EN29',0,4
	.word	755
	.byte	1,2,2,35,0,12
	.byte	'EN30',0,4
	.word	755
	.byte	1,1,2,35,0,12
	.byte	'EN31',0,4
	.word	755
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_GTM_ACCEN0_Bits',0,3,79,3
	.word	11297
	.byte	11
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,3,82,16,4,12
	.byte	'reserved_0',0,4
	.word	755
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_GTM_ACCEN1_Bits',0,3,85,3
	.word	11854
	.byte	11
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,3,88,16,4,12
	.byte	'SEL0',0,4
	.word	755
	.byte	4,28,2,35,0,12
	.byte	'SEL1',0,4
	.word	755
	.byte	4,24,2,35,0,12
	.byte	'SEL2',0,4
	.word	755
	.byte	4,20,2,35,0,12
	.byte	'SEL3',0,4
	.word	755
	.byte	4,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,3,95,3
	.word	11931
	.byte	11
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,3,98,16,4,12
	.byte	'SEL0',0,4
	.word	755
	.byte	4,28,2,35,0,12
	.byte	'SEL1',0,4
	.word	755
	.byte	4,24,2,35,0,12
	.byte	'SEL2',0,4
	.word	755
	.byte	4,20,2,35,0,12
	.byte	'SEL3',0,4
	.word	755
	.byte	4,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,3,105,3
	.word	12085
	.byte	11
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,3,108,16,4,12
	.byte	'TO_ADDR',0,4
	.word	755
	.byte	20,12,2,35,0,12
	.byte	'TO_W1R0',0,4
	.word	755
	.byte	1,11,2,35,0,12
	.byte	'reserved_21',0,4
	.word	755
	.byte	11,0,2,35,0,0,27
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,3,113,3
	.word	12239
	.byte	11
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,3,116,16,4,12
	.byte	'BRG_MODE',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'MSK_WR_RSP',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	6,24,2,35,0,12
	.byte	'MODE_UP_PGR',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'BUFF_OVL',0,4
	.word	755
	.byte	1,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'SYNC_INPUT_REG',0,4
	.word	755
	.byte	1,19,2,35,0,12
	.byte	'reserved_13',0,4
	.word	755
	.byte	3,16,2,35,0,12
	.byte	'BRG_RST',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'reserved_17',0,4
	.word	755
	.byte	7,8,2,35,0,12
	.byte	'BUFF_DPT',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,3,129,1,3
	.word	12367
	.byte	11
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,3,132,1,16,4,12
	.byte	'NEW_TRAN_PTR',0,4
	.word	755
	.byte	5,27,2,35,0,12
	.byte	'FIRST_RSP_PTR',0,4
	.word	755
	.byte	5,22,2,35,0,12
	.byte	'TRAN_IN_PGR',0,4
	.word	755
	.byte	5,17,2,35,0,12
	.byte	'ABT_TRAN_PGR',0,4
	.word	755
	.byte	5,12,2,35,0,12
	.byte	'FBC',0,4
	.word	755
	.byte	6,6,2,35,0,12
	.byte	'RSP_TRAN_RDY',0,4
	.word	755
	.byte	6,0,2,35,0,0,27
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,3,140,1,3
	.word	12674
	.byte	11
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,3,143,1,16,4,12
	.byte	'TRAN_IN_PGR2',0,4
	.word	755
	.byte	5,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	755
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,3,147,1,3
	.word	12876
	.byte	11
	.byte	'_Ifx_GTM_CLC_Bits',0,3,150,1,16,4,12
	.byte	'DISR',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'DISS',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'EDIS',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_CLC_Bits',0,3,157,1,3
	.word	12989
	.byte	11
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,3,160,1,16,4,12
	.byte	'CLK_CNT',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,3,164,1,3
	.word	13132
	.byte	11
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,3,167,1,16,4,12
	.byte	'CLK_CNT',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'CLK6_SEL',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'reserved_25',0,4
	.word	755
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,3,172,1,3
	.word	13249
	.byte	11
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,3,175,1,16,4,12
	.byte	'CLK_CNT',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'CLK7_SEL',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'reserved_25',0,4
	.word	755
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,3,180,1,3
	.word	13384
	.byte	11
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,3,183,1,16,4,12
	.byte	'EN_CLK0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'EN_CLK1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'EN_CLK2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'EN_CLK3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'EN_CLK4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'EN_CLK5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'EN_CLK6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'EN_CLK7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'EN_ECLK0',0,4
	.word	755
	.byte	2,14,2,35,0,12
	.byte	'EN_ECLK1',0,4
	.word	755
	.byte	2,12,2,35,0,12
	.byte	'EN_ECLK2',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'EN_FXCLK',0,4
	.word	755
	.byte	2,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,3,198,1,3
	.word	13519
	.byte	11
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,3,201,1,16,4,12
	.byte	'ECLK_DEN',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,3,205,1,3
	.word	13839
	.byte	11
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,3,208,1,16,4,12
	.byte	'ECLK_NUM',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,3,212,1,3
	.word	13951
	.byte	11
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,3,215,1,16,4,12
	.byte	'FXCLK_SEL',0,4
	.word	755
	.byte	4,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,3,219,1,3
	.word	14063
	.byte	11
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,3,222,1,16,4,12
	.byte	'GCLK_DEN',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,3,226,1,3
	.word	14179
	.byte	11
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,3,229,1,16,4,12
	.byte	'GCLK_NUM',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,3,233,1,3
	.word	14291
	.byte	11
	.byte	'_Ifx_GTM_CTRL_Bits',0,3,236,1,16,4,12
	.byte	'RF_PROT',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'TO_MODE',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'TO_VAL',0,4
	.word	755
	.byte	5,23,2,35,0,12
	.byte	'reserved_9',0,4
	.word	755
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_GTM_CTRL_Bits',0,3,243,1,3
	.word	14403
	.byte	11
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,3,246,1,16,4,12
	.byte	'O1SEL_0',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	755
	.byte	2,29,2,35,0,12
	.byte	'SWAP_0',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'O1F_0',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'O1SEL_1',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'I1SEL_1',0,4
	.word	755
	.byte	1,22,2,35,0,12
	.byte	'SH_EN_1',0,4
	.word	755
	.byte	1,21,2,35,0,12
	.byte	'SWAP_1',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'O1F_1',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'reserved_14',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'O1SEL_2',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'I1SEL_2',0,4
	.word	755
	.byte	1,14,2,35,0,12
	.byte	'SH_EN_2',0,4
	.word	755
	.byte	1,13,2,35,0,12
	.byte	'SWAP_2',0,4
	.word	755
	.byte	1,12,2,35,0,12
	.byte	'O1F_2',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'reserved_22',0,4
	.word	755
	.byte	2,8,2,35,0,12
	.byte	'O1SEL_3',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'I1SEL_3',0,4
	.word	755
	.byte	1,6,2,35,0,12
	.byte	'SH_EN_3',0,4
	.word	755
	.byte	1,5,2,35,0,12
	.byte	'SWAP_3',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'O1F_3',0,4
	.word	755
	.byte	2,2,2,35,0,12
	.byte	'reserved_30',0,4
	.word	755
	.byte	2,0,2,35,0,0,27
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,3,143,2,3
	.word	14556
	.byte	11
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,3,146,2,16,4,12
	.byte	'POL0_0',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'OC0_0',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'SL0_0',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'DT0_0',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'POL1_0',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'OC1_0',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'SL1_0',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'DT1_0',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'POL0_1',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'OC0_1',0,4
	.word	755
	.byte	1,22,2,35,0,12
	.byte	'SL0_1',0,4
	.word	755
	.byte	1,21,2,35,0,12
	.byte	'DT0_1',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'POL1_1',0,4
	.word	755
	.byte	1,19,2,35,0,12
	.byte	'OC1_1',0,4
	.word	755
	.byte	1,18,2,35,0,12
	.byte	'SL1_1',0,4
	.word	755
	.byte	1,17,2,35,0,12
	.byte	'DT1_1',0,4
	.word	755
	.byte	1,16,2,35,0,12
	.byte	'POL0_2',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'OC0_2',0,4
	.word	755
	.byte	1,14,2,35,0,12
	.byte	'SL0_2',0,4
	.word	755
	.byte	1,13,2,35,0,12
	.byte	'DT0_2',0,4
	.word	755
	.byte	1,12,2,35,0,12
	.byte	'POL1_2',0,4
	.word	755
	.byte	1,11,2,35,0,12
	.byte	'OC1_2',0,4
	.word	755
	.byte	1,10,2,35,0,12
	.byte	'SL1_2',0,4
	.word	755
	.byte	1,9,2,35,0,12
	.byte	'DT1_2',0,4
	.word	755
	.byte	1,8,2,35,0,12
	.byte	'POL0_3',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'OC0_3',0,4
	.word	755
	.byte	1,6,2,35,0,12
	.byte	'SL0_3',0,4
	.word	755
	.byte	1,5,2,35,0,12
	.byte	'DT0_3',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'POL1_3',0,4
	.word	755
	.byte	1,3,2,35,0,12
	.byte	'OC1_3',0,4
	.word	755
	.byte	1,2,2,35,0,12
	.byte	'SL1_3',0,4
	.word	755
	.byte	1,1,2,35,0,12
	.byte	'DT1_3',0,4
	.word	755
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,3,180,2,3
	.word	15068
	.byte	11
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,3,183,2,16,4,12
	.byte	'POL0_0_SR',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'OC0_0_SR',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'SL0_0_SR',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'DT0_0_SR',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'POL1_0_SR',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'OC1_0_SR',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'SL1_0_SR',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'DT1_0_SR',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'POL0_1_SR',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'OC0_1_SR',0,4
	.word	755
	.byte	1,22,2,35,0,12
	.byte	'SL0_1_SR',0,4
	.word	755
	.byte	1,21,2,35,0,12
	.byte	'DT0_1_SR',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'POL1_1_SR',0,4
	.word	755
	.byte	1,19,2,35,0,12
	.byte	'OC1_1_SR',0,4
	.word	755
	.byte	1,18,2,35,0,12
	.byte	'SL1_1_SR',0,4
	.word	755
	.byte	1,17,2,35,0,12
	.byte	'DT1_1_SR',0,4
	.word	755
	.byte	1,16,2,35,0,12
	.byte	'POL0_2_SR',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'OC0_2_SR',0,4
	.word	755
	.byte	1,14,2,35,0,12
	.byte	'SL0_2_SR',0,4
	.word	755
	.byte	1,13,2,35,0,12
	.byte	'DT0_2_SR',0,4
	.word	755
	.byte	1,12,2,35,0,12
	.byte	'POL1_2_SR',0,4
	.word	755
	.byte	1,11,2,35,0,12
	.byte	'OC1_2_SR',0,4
	.word	755
	.byte	1,10,2,35,0,12
	.byte	'SL1_2_SR',0,4
	.word	755
	.byte	1,9,2,35,0,12
	.byte	'DT1_2_SR',0,4
	.word	755
	.byte	1,8,2,35,0,12
	.byte	'POL0_3_SR',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'OC0_3_SR',0,4
	.word	755
	.byte	1,6,2,35,0,12
	.byte	'SL0_3_SR',0,4
	.word	755
	.byte	1,5,2,35,0,12
	.byte	'DT0_3_SR',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'POL1_3_SR',0,4
	.word	755
	.byte	1,3,2,35,0,12
	.byte	'OC1_3_SR',0,4
	.word	755
	.byte	1,2,2,35,0,12
	.byte	'SL1_3_SR',0,4
	.word	755
	.byte	1,1,2,35,0,12
	.byte	'DT1_3_SR',0,4
	.word	755
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,3,217,2,3
	.word	15689
	.byte	11
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,3,220,2,16,4,12
	.byte	'CLK_SEL',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'UPD_MODE',0,4
	.word	755
	.byte	3,25,2,35,0,12
	.byte	'reserved_7',0,4
	.word	755
	.byte	25,0,2,35,0,0,27
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,3,226,2,3
	.word	16412
	.byte	11
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,3,229,2,16,4,12
	.byte	'RELRISE',0,4
	.word	755
	.byte	10,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	755
	.byte	6,16,2,35,0,12
	.byte	'RELFALL',0,4
	.word	755
	.byte	10,6,2,35,0,12
	.byte	'reserved_26',0,4
	.word	755
	.byte	6,0,2,35,0,0,27
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,3,235,2,3
	.word	16556
	.byte	11
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,3,238,2,16,4,12
	.byte	'RELBLK',0,4
	.word	755
	.byte	10,22,2,35,0,12
	.byte	'reserved_10',0,4
	.word	755
	.byte	6,16,2,35,0,12
	.byte	'PSU_IN_SEL',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'IN_POL',0,4
	.word	755
	.byte	1,14,2,35,0,12
	.byte	'reserved_18',0,4
	.word	755
	.byte	2,12,2,35,0,12
	.byte	'SHIFT_SEL',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'reserved_22',0,4
	.word	755
	.byte	10,0,2,35,0,0,27
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,3,247,2,3
	.word	16705
	.byte	11
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,3,250,2,16,4,12
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,3,129,3,3
	.word	16920
	.byte	11
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,3,132,3,16,4,12
	.byte	'GRSTEN',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'BRIDGE_MODE_RST',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'AEI_IN',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	755
	.byte	5,24,2,35,0,12
	.byte	'TOM_OUT_RST',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	755
	.byte	3,20,2,35,0,12
	.byte	'reserved_12',0,4
	.word	755
	.byte	4,16,2,35,0,12
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'IRQ_MODE_PULSE',0,4
	.word	755
	.byte	1,14,2,35,0,12
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	755
	.byte	1,13,2,35,0,12
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	755
	.byte	1,12,2,35,0,12
	.byte	'reserved_20',0,4
	.word	755
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_GTM_HW_CONF_Bits',0,3,146,3,3
	.word	17124
	.byte	11
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,3,149,3,16,4,12
	.byte	'reserved_0',0,4
	.word	755
	.byte	4,28,2,35,0,12
	.byte	'AEI_IRQ',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	755
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,3,154,3,3
	.word	17481
	.byte	11
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,3,157,3,16,4,12
	.byte	'TIM0_CH0_IRQ',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'TIM0_CH1_IRQ',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'TIM0_CH2_IRQ',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'TIM0_CH3_IRQ',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'TIM0_CH4_IRQ',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'TIM0_CH5_IRQ',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'TIM0_CH6_IRQ',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'TIM0_CH7_IRQ',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	755
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,3,168,3,3
	.word	17609
	.byte	11
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,3,171,3,16,4,12
	.byte	'TOM0_CH0_IRQ',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'TOM0_CH1_IRQ',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'TOM0_CH2_IRQ',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'TOM0_CH3_IRQ',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'TOM0_CH4_IRQ',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'TOM0_CH5_IRQ',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'TOM0_CH6_IRQ',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'TOM0_CH7_IRQ',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'TOM0_CH8_IRQ',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'TOM0_CH9_IRQ',0,4
	.word	755
	.byte	1,22,2,35,0,12
	.byte	'TOM0_CH10_IRQ',0,4
	.word	755
	.byte	1,21,2,35,0,12
	.byte	'TOM0_CH11_IRQ',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'TOM0_CH12_IRQ',0,4
	.word	755
	.byte	1,19,2,35,0,12
	.byte	'TOM0_CH13_IRQ',0,4
	.word	755
	.byte	1,18,2,35,0,12
	.byte	'TOM0_CH14_IRQ',0,4
	.word	755
	.byte	1,17,2,35,0,12
	.byte	'TOM0_CH15_IRQ',0,4
	.word	755
	.byte	1,16,2,35,0,12
	.byte	'TOM1_CH0_IRQ',0,4
	.word	755
	.byte	1,15,2,35,0,12
	.byte	'TOM1_CH1_IRQ',0,4
	.word	755
	.byte	1,14,2,35,0,12
	.byte	'TOM1_CH2_IRQ',0,4
	.word	755
	.byte	1,13,2,35,0,12
	.byte	'TOM1_CH3_IRQ',0,4
	.word	755
	.byte	1,12,2,35,0,12
	.byte	'TOM1_CH4_IRQ',0,4
	.word	755
	.byte	1,11,2,35,0,12
	.byte	'TOM1_CH5_IRQ',0,4
	.word	755
	.byte	1,10,2,35,0,12
	.byte	'TOM1_CH6_IRQ',0,4
	.word	755
	.byte	1,9,2,35,0,12
	.byte	'TOM1_CH7_IRQ',0,4
	.word	755
	.byte	1,8,2,35,0,12
	.byte	'TOM1_CH8_IRQ',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'TOM1_CH9_IRQ',0,4
	.word	755
	.byte	1,6,2,35,0,12
	.byte	'TOM1_CH10_IRQ',0,4
	.word	755
	.byte	1,5,2,35,0,12
	.byte	'TOM1_CH11_IRQ',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'TOM1_CH12_IRQ',0,4
	.word	755
	.byte	1,3,2,35,0,12
	.byte	'TOM1_CH13_IRQ',0,4
	.word	755
	.byte	1,2,2,35,0,12
	.byte	'TOM1_CH14_IRQ',0,4
	.word	755
	.byte	1,1,2,35,0,12
	.byte	'TOM1_CH15_IRQ',0,4
	.word	755
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,3,205,3,3
	.word	17888
	.byte	11
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,3,208,3,16,4,12
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	755
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,3,219,3,3
	.word	18733
	.byte	11
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,3,222,3,16,4,12
	.byte	'GTM_EIRQ',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	755
	.byte	3,28,2,35,0,12
	.byte	'TIM0_EIRQ',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'reserved_5',0,4
	.word	755
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,3,228,3,3
	.word	19026
	.byte	11
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,3,231,3,16,4,12
	.byte	'SEL0',0,4
	.word	755
	.byte	4,28,2,35,0,12
	.byte	'SEL1',0,4
	.word	755
	.byte	4,24,2,35,0,12
	.byte	'SEL2',0,4
	.word	755
	.byte	4,20,2,35,0,12
	.byte	'SEL3',0,4
	.word	755
	.byte	4,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,3,238,3,3
	.word	19180
	.byte	11
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,3,241,3,16,4,12
	.byte	'SEL0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'SEL1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'SEL2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'SEL3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'SEL4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'SEL5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'SEL6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'SEL7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'SEL8',0,4
	.word	755
	.byte	2,14,2,35,0,12
	.byte	'SEL9',0,4
	.word	755
	.byte	2,12,2,35,0,12
	.byte	'SEL10',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'SEL11',0,4
	.word	755
	.byte	2,8,2,35,0,12
	.byte	'SEL12',0,4
	.word	755
	.byte	2,6,2,35,0,12
	.byte	'SEL13',0,4
	.word	755
	.byte	2,4,2,35,0,12
	.byte	'SEL14',0,4
	.word	755
	.byte	2,2,2,35,0,12
	.byte	'SEL15',0,4
	.word	755
	.byte	2,0,2,35,0,0,27
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,3,131,4,3
	.word	19350
	.byte	11
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,3,134,4,16,4,12
	.byte	'CH0SEL',0,4
	.word	755
	.byte	4,28,2,35,0,12
	.byte	'CH1SEL',0,4
	.word	755
	.byte	4,24,2,35,0,12
	.byte	'CH2SEL',0,4
	.word	755
	.byte	4,20,2,35,0,12
	.byte	'CH3SEL',0,4
	.word	755
	.byte	4,16,2,35,0,12
	.byte	'CH4SEL',0,4
	.word	755
	.byte	4,12,2,35,0,12
	.byte	'CH5SEL',0,4
	.word	755
	.byte	4,8,2,35,0,12
	.byte	'CH6SEL',0,4
	.word	755
	.byte	4,4,2,35,0,12
	.byte	'CH7SEL',0,4
	.word	755
	.byte	4,0,2,35,0,0,27
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,3,144,4,3
	.word	19691
	.byte	11
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,3,147,4,16,4,12
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,3,154,4,3
	.word	19916
	.byte	11
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,3,157,4,16,4,12
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'TRG_AEI_USP_BE',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,3,164,4,3
	.word	20114
	.byte	11
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,3,167,4,16,4,12
	.byte	'IRQ_MODE',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,3,171,4,3
	.word	20310
	.byte	11
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,3,174,4,16,4,12
	.byte	'AEI_TO_XPT',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'AEI_USP_ADDR',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'AEI_IM_ADDR',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'AEI_USP_BE',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,3,181,4,3
	.word	20413
	.byte	11
	.byte	'_Ifx_GTM_KRST0_Bits',0,3,184,4,16,4,12
	.byte	'RST',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'RSTSTAT',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_GTM_KRST0_Bits',0,3,189,4,3
	.word	20591
	.byte	11
	.byte	'_Ifx_GTM_KRST1_Bits',0,3,192,4,16,4,12
	.byte	'RST',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	755
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_GTM_KRST1_Bits',0,3,196,4,3
	.word	20702
	.byte	11
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,3,199,4,16,4,12
	.byte	'CLR',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	755
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,3,203,4,3
	.word	20794
	.byte	11
	.byte	'_Ifx_GTM_OCS_Bits',0,3,206,4,16,4,12
	.byte	'reserved_0',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'SUS',0,4
	.word	755
	.byte	4,4,2,35,0,12
	.byte	'SUS_P',0,4
	.word	755
	.byte	1,3,2,35,0,12
	.byte	'SUSSTA',0,4
	.word	755
	.byte	1,2,2,35,0,12
	.byte	'reserved_30',0,4
	.word	755
	.byte	2,0,2,35,0,0,27
	.byte	'Ifx_GTM_OCS_Bits',0,3,213,4,3
	.word	20890
	.byte	11
	.byte	'_Ifx_GTM_ODA_Bits',0,3,216,4,16,4,12
	.byte	'DDREN',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'DREN',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'reserved_2',0,4
	.word	755
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_GTM_ODA_Bits',0,3,221,4,3
	.word	21036
	.byte	11
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,3,224,4,16,4,12
	.byte	'CV',0,4
	.word	755
	.byte	27,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'CM',0,4
	.word	755
	.byte	2,2,2,35,0,12
	.byte	'reserved_30',0,4
	.word	755
	.byte	2,0,2,35,0,0,27
	.byte	'Ifx_GTM_OTBU0T_Bits',0,3,230,4,3
	.word	21142
	.byte	11
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,3,233,4,16,4,12
	.byte	'CV',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	4,4,2,35,0,12
	.byte	'EN',0,4
	.word	755
	.byte	1,3,2,35,0,12
	.byte	'reserved_29',0,4
	.word	755
	.byte	3,0,2,35,0,0,27
	.byte	'Ifx_GTM_OTBU1T_Bits',0,3,239,4,3
	.word	21273
	.byte	11
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,3,242,4,16,4,12
	.byte	'CV',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	4,4,2,35,0,12
	.byte	'EN',0,4
	.word	755
	.byte	1,3,2,35,0,12
	.byte	'reserved_29',0,4
	.word	755
	.byte	3,0,2,35,0,0,27
	.byte	'Ifx_GTM_OTBU2T_Bits',0,3,248,4,3
	.word	21404
	.byte	11
	.byte	'_Ifx_GTM_OTSC0_Bits',0,3,251,4,16,4,12
	.byte	'B0LMT',0,4
	.word	755
	.byte	3,29,2,35,0,12
	.byte	'reserved_3',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'B0LMI',0,4
	.word	755
	.byte	4,24,2,35,0,12
	.byte	'B0HMT',0,4
	.word	755
	.byte	3,21,2,35,0,12
	.byte	'reserved_11',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'B0HMI',0,4
	.word	755
	.byte	4,16,2,35,0,12
	.byte	'B1LMT',0,4
	.word	755
	.byte	3,13,2,35,0,12
	.byte	'reserved_19',0,4
	.word	755
	.byte	1,12,2,35,0,12
	.byte	'B1LMI',0,4
	.word	755
	.byte	4,8,2,35,0,12
	.byte	'B1HMT',0,4
	.word	755
	.byte	3,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	755
	.byte	1,4,2,35,0,12
	.byte	'B1HMI',0,4
	.word	755
	.byte	4,0,2,35,0,0,27
	.byte	'Ifx_GTM_OTSC0_Bits',0,3,137,5,3
	.word	21535
	.byte	11
	.byte	'_Ifx_GTM_OTSS_Bits',0,3,140,5,16,4,12
	.byte	'OTGB0',0,4
	.word	755
	.byte	4,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	4,24,2,35,0,12
	.byte	'OTGB1',0,4
	.word	755
	.byte	4,20,2,35,0,12
	.byte	'reserved_12',0,4
	.word	755
	.byte	4,16,2,35,0,12
	.byte	'OTGB2',0,4
	.word	755
	.byte	4,12,2,35,0,12
	.byte	'reserved_20',0,4
	.word	755
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_GTM_OTSS_Bits',0,3,148,5,3
	.word	21817
	.byte	11
	.byte	'_Ifx_GTM_REV_Bits',0,3,151,5,16,4,12
	.byte	'STEP',0,4
	.word	755
	.byte	8,24,2,35,0,12
	.byte	'NO',0,4
	.word	755
	.byte	4,20,2,35,0,12
	.byte	'MINOR',0,4
	.word	755
	.byte	4,16,2,35,0,12
	.byte	'MAJOR',0,4
	.word	755
	.byte	4,12,2,35,0,12
	.byte	'DEV_CODE0',0,4
	.word	755
	.byte	4,8,2,35,0,12
	.byte	'DEV_CODE1',0,4
	.word	755
	.byte	4,4,2,35,0,12
	.byte	'DEV_CODE2',0,4
	.word	755
	.byte	4,0,2,35,0,0,27
	.byte	'Ifx_GTM_REV_Bits',0,3,160,5,3
	.word	21989
	.byte	11
	.byte	'_Ifx_GTM_RST_Bits',0,3,163,5,16,4,12
	.byte	'RST',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	755
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_GTM_RST_Bits',0,3,167,5,3
	.word	22167
	.byte	11
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,3,170,5,16,4,12
	.byte	'BASE',0,4
	.word	755
	.byte	27,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	755
	.byte	5,0,2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,3,174,5,3
	.word	22255
	.byte	11
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,3,177,5,16,4,12
	.byte	'LOW_RES',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'CH_CLK_SRC',0,4
	.word	755
	.byte	3,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,3,182,5,3
	.word	22363
	.byte	11
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,3,185,5,16,4,12
	.byte	'BASE',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,3,189,5,3
	.word	22495
	.byte	11
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,3,192,5,16,4,12
	.byte	'CH_MODE',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'CH_CLK_SRC',0,4
	.word	755
	.byte	3,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,3,197,5,3
	.word	22603
	.byte	11
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,3,200,5,16,4,12
	.byte	'BASE',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,3,204,5,3
	.word	22735
	.byte	11
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,3,207,5,16,4,12
	.byte	'CH_MODE',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'CH_CLK_SRC',0,4
	.word	755
	.byte	3,28,2,35,0,12
	.byte	'reserved_4',0,4
	.word	755
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,3,212,5,3
	.word	22843
	.byte	11
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,3,215,5,16,4,12
	.byte	'ENDIS_CH0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_CH1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_CH2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'reserved_6',0,4
	.word	755
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,3,221,5,3
	.word	22975
	.byte	11
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,3,224,5,16,4,12
	.byte	'SRC_CH0',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'SRC_CH1',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'SRC_CH2',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'SRC_CH3',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'SRC_CH4',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'SRC_CH5',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'SRC_CH6',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'SRC_CH7',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	755
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,3,235,5,3
	.word	23121
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,3,242,5,3
	.word	7846
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,3,249,5,3
	.word	8101
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,3,150,6,3
	.word	8788
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,3,157,6,3
	.word	7972
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,3,164,6,3
	.word	9359
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,3,176,6,3
	.word	10404
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,3,183,6,3
	.word	8653
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,3,190,6,3
	.word	8518
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,3,197,6,3
	.word	7602
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,3,204,6,3
	.word	7724
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,3,216,6,3
	.word	9732
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,3,228,6,3
	.word	10002
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,3,235,6,3
	.word	10264
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,3,247,6,3
	.word	9496
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,3,254,6,3
	.word	8223
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,3,135,7,3
	.word	8353
	.byte	11
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,3,138,7,16,4,12
	.byte	'VAL_0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'MODE_0',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'VAL_1',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'MODE_1',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'VAL_2',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'MODE_2',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'VAL_3',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'MODE_3',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'VAL_4',0,4
	.word	755
	.byte	2,14,2,35,0,12
	.byte	'MODE_4',0,4
	.word	755
	.byte	2,12,2,35,0,12
	.byte	'VAL_5',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'MODE_5',0,4
	.word	755
	.byte	2,8,2,35,0,12
	.byte	'VAL_6',0,4
	.word	755
	.byte	2,6,2,35,0,12
	.byte	'MODE_6',0,4
	.word	755
	.byte	2,4,2,35,0,12
	.byte	'VAL_7',0,4
	.word	755
	.byte	2,2,2,35,0,12
	.byte	'MODE_7',0,4
	.word	755
	.byte	2,0,2,35,0,0,27
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,3,156,7,3
	.word	23938
	.byte	11
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,3,159,7,16,4,12
	.byte	'F_OUT',0,4
	.word	755
	.byte	8,24,2,35,0,12
	.byte	'F_IN',0,4
	.word	755
	.byte	8,16,2,35,0,12
	.byte	'TIM_IN',0,4
	.word	755
	.byte	8,8,2,35,0,12
	.byte	'reserved_24',0,4
	.word	755
	.byte	8,0,2,35,0,0,27
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,3,165,7,3
	.word	24283
	.byte	11
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,3,168,7,16,4,12
	.byte	'RST_CH0',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'RST_CH1',0,4
	.word	755
	.byte	1,30,2,35,0,12
	.byte	'RST_CH2',0,4
	.word	755
	.byte	1,29,2,35,0,12
	.byte	'RST_CH3',0,4
	.word	755
	.byte	1,28,2,35,0,12
	.byte	'RST_CH4',0,4
	.word	755
	.byte	1,27,2,35,0,12
	.byte	'RST_CH5',0,4
	.word	755
	.byte	1,26,2,35,0,12
	.byte	'RST_CH6',0,4
	.word	755
	.byte	1,25,2,35,0,12
	.byte	'RST_CH7',0,4
	.word	755
	.byte	1,24,2,35,0,12
	.byte	'reserved_8',0,4
	.word	755
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_GTM_TIM_RST_Bits',0,3,179,7,3
	.word	24424
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,3,186,7,3
	.word	6431
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,3,193,7,3
	.word	6554
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,3,200,7,3
	.word	6677
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,3,218,7,3
	.word	5837
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,3,226,7,3
	.word	7080
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,3,234,7,3
	.word	7243
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,3,241,7,3
	.word	7412
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,3,249,7,3
	.word	6923
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,3,128,8,3
	.word	6185
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,3,135,8,3
	.word	6308
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,3,142,8,3
	.word	6800
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,3,151,8,3
	.word	1201
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,3,165,8,3
	.word	2144
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,3,179,8,3
	.word	2452
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,3,200,8,3
	.word	1373
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,3,223,8,3
	.word	717
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,3,237,8,3
	.word	1824
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,3,251,8,3
	.word	2760
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,3,137,9,3
	.word	3068
	.byte	11
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,3,140,9,16,4,12
	.byte	'ACT_TB',0,4
	.word	755
	.byte	24,8,2,35,0,12
	.byte	'TB_TRIG',0,4
	.word	755
	.byte	1,7,2,35,0,12
	.byte	'TBU_SEL',0,4
	.word	755
	.byte	2,5,2,35,0,12
	.byte	'reserved_27',0,4
	.word	755
	.byte	5,0,2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,3,146,9,3
	.word	25372
	.byte	11
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,3,149,9,16,4,12
	.byte	'ENDIS_CTRL0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_CTRL1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_CTRL2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'ENDIS_CTRL3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'ENDIS_CTRL4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'ENDIS_CTRL5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'ENDIS_CTRL6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'ENDIS_CTRL7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,3,160,9,3
	.word	25526
	.byte	11
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,3,163,9,16,4,12
	.byte	'ENDIS_STAT0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'ENDIS_STAT1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'ENDIS_STAT2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'ENDIS_STAT3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'ENDIS_STAT4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'ENDIS_STAT5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'ENDIS_STAT6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'ENDIS_STAT7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,3,174,9,3
	.word	25816
	.byte	11
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,3,177,9,16,4,12
	.byte	'FUPD_CTRL0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'FUPD_CTRL1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'FUPD_CTRL2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'FUPD_CTRL3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'FUPD_CTRL4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'FUPD_CTRL5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'FUPD_CTRL6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'FUPD_CTRL7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'RSTCN0_CH0',0,4
	.word	755
	.byte	2,14,2,35,0,12
	.byte	'RSTCN0_CH1',0,4
	.word	755
	.byte	2,12,2,35,0,12
	.byte	'RSTCN0_CH2',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'RSTCN0_CH3',0,4
	.word	755
	.byte	2,8,2,35,0,12
	.byte	'RSTCN0_CH4',0,4
	.word	755
	.byte	2,6,2,35,0,12
	.byte	'RSTCN0_CH5',0,4
	.word	755
	.byte	2,4,2,35,0,12
	.byte	'RSTCN0_CH6',0,4
	.word	755
	.byte	2,2,2,35,0,12
	.byte	'RSTCN0_CH7',0,4
	.word	755
	.byte	2,0,2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,3,195,9,3
	.word	26106
	.byte	11
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,3,198,9,16,4,12
	.byte	'HOST_TRIG',0,4
	.word	755
	.byte	1,31,2,35,0,12
	.byte	'reserved_1',0,4
	.word	755
	.byte	7,24,2,35,0,12
	.byte	'RST_CH0',0,4
	.word	755
	.byte	1,23,2,35,0,12
	.byte	'RST_CH1',0,4
	.word	755
	.byte	1,22,2,35,0,12
	.byte	'RST_CH2',0,4
	.word	755
	.byte	1,21,2,35,0,12
	.byte	'RST_CH3',0,4
	.word	755
	.byte	1,20,2,35,0,12
	.byte	'RST_CH4',0,4
	.word	755
	.byte	1,19,2,35,0,12
	.byte	'RST_CH5',0,4
	.word	755
	.byte	1,18,2,35,0,12
	.byte	'RST_CH6',0,4
	.word	755
	.byte	1,17,2,35,0,12
	.byte	'RST_CH7',0,4
	.word	755
	.byte	1,16,2,35,0,12
	.byte	'UPEN_CTRL0',0,4
	.word	755
	.byte	2,14,2,35,0,12
	.byte	'UPEN_CTRL1',0,4
	.word	755
	.byte	2,12,2,35,0,12
	.byte	'UPEN_CTRL2',0,4
	.word	755
	.byte	2,10,2,35,0,12
	.byte	'UPEN_CTRL3',0,4
	.word	755
	.byte	2,8,2,35,0,12
	.byte	'UPEN_CTRL4',0,4
	.word	755
	.byte	2,6,2,35,0,12
	.byte	'UPEN_CTRL5',0,4
	.word	755
	.byte	2,4,2,35,0,12
	.byte	'UPEN_CTRL6',0,4
	.word	755
	.byte	2,2,2,35,0,12
	.byte	'UPEN_CTRL7',0,4
	.word	755
	.byte	2,0,2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,3,218,9,3
	.word	26539
	.byte	11
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,3,221,9,16,4,12
	.byte	'INT_TRIG0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'INT_TRIG1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'INT_TRIG2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'INT_TRIG3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'INT_TRIG4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'INT_TRIG5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'INT_TRIG6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'INT_TRIG7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,3,232,9,3
	.word	26989
	.byte	11
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,3,235,9,16,4,12
	.byte	'OUTEN_CTRL0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'OUTEN_CTRL1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'OUTEN_CTRL2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'OUTEN_CTRL3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'OUTEN_CTRL4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'OUTEN_CTRL5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'OUTEN_CTRL6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'OUTEN_CTRL7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,3,246,9,3
	.word	27259
	.byte	11
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,3,249,9,16,4,12
	.byte	'OUTEN_STAT0',0,4
	.word	755
	.byte	2,30,2,35,0,12
	.byte	'OUTEN_STAT1',0,4
	.word	755
	.byte	2,28,2,35,0,12
	.byte	'OUTEN_STAT2',0,4
	.word	755
	.byte	2,26,2,35,0,12
	.byte	'OUTEN_STAT3',0,4
	.word	755
	.byte	2,24,2,35,0,12
	.byte	'OUTEN_STAT4',0,4
	.word	755
	.byte	2,22,2,35,0,12
	.byte	'OUTEN_STAT5',0,4
	.word	755
	.byte	2,20,2,35,0,12
	.byte	'OUTEN_STAT6',0,4
	.word	755
	.byte	2,18,2,35,0,12
	.byte	'OUTEN_STAT7',0,4
	.word	755
	.byte	2,16,2,35,0,12
	.byte	'reserved_16',0,4
	.word	755
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,3,132,10,3
	.word	27549
	.byte	9,3,140,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11297
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ACCEN0',0,3,145,10,3
	.word	27839
	.byte	9,3,148,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11854
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ACCEN1',0,3,153,10,3
	.word	27903
	.byte	9,3,156,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	11931
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,3,161,10,3
	.word	27967
	.byte	9,3,164,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12085
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,3,169,10,3
	.word	28037
	.byte	9,3,172,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12239
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,3,177,10,3
	.word	28107
	.byte	9,3,180,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12367
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_BRIDGE_MODE',0,3,185,10,3
	.word	28177
	.byte	9,3,188,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12674
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,3,193,10,3
	.word	28246
	.byte	9,3,196,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12876
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,3,201,10,3
	.word	28315
	.byte	9,3,204,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	12989
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CLC',0,3,209,10,3
	.word	28384
	.byte	9,3,212,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13132
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,3,217,10,3
	.word	28445
	.byte	9,3,220,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13249
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,3,225,10,3
	.word	28518
	.byte	9,3,228,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13384
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,3,233,10,3
	.word	28590
	.byte	9,3,236,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13519
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_CLK_EN',0,3,241,10,3
	.word	28662
	.byte	9,3,244,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13839
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,3,249,10,3
	.word	28730
	.byte	9,3,252,10,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13951
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,3,129,11,3
	.word	28800
	.byte	9,3,132,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14063
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,3,137,11,3
	.word	28870
	.byte	9,3,140,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14179
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,3,145,11,3
	.word	28942
	.byte	9,3,148,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14291
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,3,153,11,3
	.word	29012
	.byte	9,3,156,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14403
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_CTRL',0,3,161,11,3
	.word	29082
	.byte	9,3,164,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14556
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,3,169,11,3
	.word	29144
	.byte	9,3,172,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15068
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,3,177,11,3
	.word	29214
	.byte	9,3,180,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15689
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,3,185,11,3
	.word	29284
	.byte	9,3,188,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16412
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_DTM_CTRL',0,3,193,11,3
	.word	29357
	.byte	9,3,196,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16556
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_DTM_DTV_CH',0,3,201,11,3
	.word	29423
	.byte	9,3,204,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16705
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,3,209,11,3
	.word	29491
	.byte	9,3,212,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16920
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_EIRQ_EN',0,3,217,11,3
	.word	29560
	.byte	9,3,220,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17124
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_HW_CONF',0,3,225,11,3
	.word	29625
	.byte	9,3,228,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17481
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_0',0,3,233,11,3
	.word	29690
	.byte	9,3,236,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17609
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_2',0,3,241,11,3
	.word	29758
	.byte	9,3,244,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17888
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_6',0,3,249,11,3
	.word	29826
	.byte	9,3,252,11,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18733
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,3,129,12,3
	.word	29894
	.byte	9,3,132,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19026
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,3,137,12,3
	.word	29965
	.byte	9,3,140,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19180
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,3,145,12,3
	.word	30035
	.byte	9,3,148,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19350
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,3,153,12,3
	.word	30112
	.byte	9,3,156,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19691
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,3,161,12,3
	.word	30187
	.byte	9,3,164,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19916
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_IRQ_EN',0,3,169,12,3
	.word	30263
	.byte	9,3,172,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20114
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_IRQ_FORCINT',0,3,177,12,3
	.word	30327
	.byte	9,3,180,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20310
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_IRQ_MODE',0,3,185,12,3
	.word	30396
	.byte	9,3,188,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20413
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,3,193,12,3
	.word	30462
	.byte	9,3,196,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20591
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_KRST0',0,3,201,12,3
	.word	30530
	.byte	9,3,204,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20702
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_KRST1',0,3,209,12,3
	.word	30593
	.byte	9,3,212,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20794
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_KRSTCLR',0,3,217,12,3
	.word	30656
	.byte	9,3,220,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20890
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_OCS',0,3,225,12,3
	.word	30721
	.byte	9,3,228,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21036
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_ODA',0,3,233,12,3
	.word	30782
	.byte	9,3,236,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21142
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_OTBU0T',0,3,241,12,3
	.word	30843
	.byte	9,3,244,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21273
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_OTBU1T',0,3,249,12,3
	.word	30907
	.byte	9,3,252,12,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21404
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_OTBU2T',0,3,129,13,3
	.word	30971
	.byte	9,3,132,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21535
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_OTSC0',0,3,137,13,3
	.word	31035
	.byte	9,3,140,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21817
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_OTSS',0,3,145,13,3
	.word	31098
	.byte	9,3,148,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21989
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_REV',0,3,153,13,3
	.word	31160
	.byte	9,3,156,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22167
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_RST',0,3,161,13,3
	.word	31221
	.byte	9,3,164,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22255
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,3,169,13,3
	.word	31282
	.byte	9,3,172,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22363
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,3,177,13,3
	.word	31352
	.byte	9,3,180,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22495
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,3,185,13,3
	.word	31422
	.byte	9,3,188,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22603
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,3,193,13,3
	.word	31492
	.byte	9,3,196,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22735
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,3,201,13,3
	.word	31562
	.byte	9,3,204,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22843
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,3,209,13,3
	.word	31632
	.byte	9,3,212,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22975
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TBU_CHEN',0,3,217,13,3
	.word	31702
	.byte	9,3,220,13,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23121
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,3,225,13,3
	.word	31768
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_CNT',0,3,233,13,3
	.word	7818
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,3,241,13,3
	.word	8073
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,3,249,13,3
	.word	8760
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,3,129,14,3
	.word	7944
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,3,137,14,3
	.word	9331
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,3,145,14,3
	.word	10376
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,3,153,14,3
	.word	8625
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,3,161,14,3
	.word	8490
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,3,169,14,3
	.word	7574
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,3,177,14,3
	.word	7696
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,3,185,14,3
	.word	9704
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,3,193,14,3
	.word	9974
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,3,201,14,3
	.word	10236
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,3,209,14,3
	.word	9468
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,3,217,14,3
	.word	8195
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,3,225,14,3
	.word	8325
	.byte	9,3,228,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23938
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TIM_IN_SRC',0,3,233,14,3
	.word	32330
	.byte	9,3,236,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24283
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TIM_INP_VAL',0,3,241,14,3
	.word	32398
	.byte	9,3,244,14,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24424
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TIM_RST',0,3,249,14,3
	.word	32467
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_CM0',0,3,129,15,3
	.word	6403
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_CM1',0,3,137,15,3
	.word	6526
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_CN0',0,3,145,15,3
	.word	6649
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,3,153,15,3
	.word	5809
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,3,161,15,3
	.word	7052
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,3,169,15,3
	.word	7215
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,3,177,15,3
	.word	7384
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,3,185,15,3
	.word	6895
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_SR0',0,3,193,15,3
	.word	6157
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_SR1',0,3,201,15,3
	.word	6280
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_STAT',0,3,209,15,3
	.word	6772
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,3,217,15,3
	.word	1173
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,3,225,15,3
	.word	2116
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,3,233,15,3
	.word	2424
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,3,241,15,3
	.word	1345
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,3,249,15,3
	.word	666
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,3,129,16,3
	.word	1796
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,3,137,16,3
	.word	2732
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,3,145,16,3
	.word	3040
	.byte	9,3,148,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25372
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,3,153,16,3
	.word	33152
	.byte	9,3,156,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25526
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,3,161,16,3
	.word	33225
	.byte	9,3,164,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25816
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,3,169,16,3
	.word	33302
	.byte	9,3,172,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26106
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,3,177,16,3
	.word	33379
	.byte	9,3,180,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26539
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,3,185,16,3
	.word	33455
	.byte	9,3,188,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26989
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,3,193,16,3
	.word	33530
	.byte	9,3,196,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27259
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,3,201,16,3
	.word	33605
	.byte	9,3,204,16,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27549
	.byte	2,35,0,0,27
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,3,209,16,3
	.word	33682
	.byte	11
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,3,220,16,25,4,10
	.byte	'CTRL',0,4
	.word	28445
	.byte	2,35,0,0,15
	.word	33759
	.byte	27
	.byte	'Ifx_GTM_CMU_CLK0_5',0,3,223,16,3
	.word	33800
	.byte	11
	.byte	'_Ifx_GTM_CMU_CLK_6',0,3,226,16,25,4,10
	.byte	'CTRL',0,4
	.word	28518
	.byte	2,35,0,0,15
	.word	33833
	.byte	27
	.byte	'Ifx_GTM_CMU_CLK_6',0,3,229,16,3
	.word	33873
	.byte	11
	.byte	'_Ifx_GTM_CMU_CLK_7',0,3,232,16,25,4,10
	.byte	'CTRL',0,4
	.word	28590
	.byte	2,35,0,0,15
	.word	33905
	.byte	27
	.byte	'Ifx_GTM_CMU_CLK_7',0,3,235,16,3
	.word	33945
	.byte	11
	.byte	'_Ifx_GTM_CMU_ECLK',0,3,238,16,25,8,10
	.byte	'NUM',0,4
	.word	28800
	.byte	2,35,0,10
	.byte	'DEN',0,4
	.word	28730
	.byte	2,35,4,0,15
	.word	33977
	.byte	27
	.byte	'Ifx_GTM_CMU_ECLK',0,3,242,16,3
	.word	34028
	.byte	11
	.byte	'_Ifx_GTM_CMU_FXCLK',0,3,245,16,25,4,10
	.byte	'CTRL',0,4
	.word	28870
	.byte	2,35,0,0,15
	.word	34059
	.byte	27
	.byte	'Ifx_GTM_CMU_FXCLK',0,3,248,16,3
	.word	34099
	.byte	11
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,3,251,16,25,4,10
	.byte	'OUTSEL',0,4
	.word	30035
	.byte	2,35,0,0,15
	.word	34131
	.byte	27
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,3,254,16,3
	.word	34176
	.byte	11
	.byte	'_Ifx_GTM_INOUTSEL_T',0,3,129,17,25,32,13,32
	.word	30112
	.byte	14,7,0,10
	.byte	'OUTSEL',0,32
	.word	34237
	.byte	2,35,0,0,15
	.word	34211
	.byte	27
	.byte	'Ifx_GTM_INOUTSEL_T',0,3,132,17,3
	.word	34263
	.byte	11
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,3,135,17,25,32,10
	.byte	'INSEL',0,4
	.word	30187
	.byte	2,35,0,13,28
	.word	238
	.byte	14,27,0,10
	.byte	'reserved_4',0,28
	.word	34339
	.byte	2,35,4,0,15
	.word	34296
	.byte	27
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,3,139,17,3
	.word	34369
	.byte	11
	.byte	'_Ifx_GTM_TIM_CH',0,3,142,17,25,116,10
	.byte	'GPR0',0,4
	.word	7574
	.byte	2,35,0,10
	.byte	'GPR1',0,4
	.word	7696
	.byte	2,35,4,10
	.byte	'CNT',0,4
	.word	7818
	.byte	2,35,8,10
	.byte	'ECNT',0,4
	.word	7944
	.byte	2,35,12,10
	.byte	'CNTS',0,4
	.word	8073
	.byte	2,35,16,10
	.byte	'TDUC',0,4
	.word	8195
	.byte	2,35,20,10
	.byte	'TDUV',0,4
	.word	8325
	.byte	2,35,24,10
	.byte	'FLT_RE',0,4
	.word	8490
	.byte	2,35,28,10
	.byte	'FLT_FE',0,4
	.word	8625
	.byte	2,35,32,10
	.byte	'CTRL',0,4
	.word	8760
	.byte	2,35,36,10
	.byte	'ECTRL',0,4
	.word	9331
	.byte	2,35,40,10
	.byte	'IRQ_NOTIFY',0,4
	.word	9468
	.byte	2,35,44,10
	.byte	'IRQ_EN',0,4
	.word	9704
	.byte	2,35,48,10
	.byte	'IRQ_FORCINT',0,4
	.word	9974
	.byte	2,35,52,10
	.byte	'IRQ_MODE',0,4
	.word	10236
	.byte	2,35,56,10
	.byte	'EIRQ_EN',0,4
	.word	10376
	.byte	2,35,60,13,52
	.word	238
	.byte	14,51,0,10
	.byte	'reserved_40',0,52
	.word	34676
	.byte	2,35,64,0,15
	.word	34404
	.byte	27
	.byte	'Ifx_GTM_TIM_CH',0,3,161,17,3
	.word	34707
	.byte	11
	.byte	'_Ifx_GTM_TOM_CH',0,3,164,17,25,48,10
	.byte	'CTRL',0,4
	.word	5809
	.byte	2,35,0,10
	.byte	'SR0',0,4
	.word	6157
	.byte	2,35,4,10
	.byte	'SR1',0,4
	.word	6280
	.byte	2,35,8,10
	.byte	'CM0',0,4
	.word	6403
	.byte	2,35,12,10
	.byte	'CM1',0,4
	.word	6526
	.byte	2,35,16,10
	.byte	'CN0',0,4
	.word	6649
	.byte	2,35,20,10
	.byte	'STAT',0,4
	.word	6772
	.byte	2,35,24,10
	.byte	'IRQ_NOTIFY',0,4
	.word	6895
	.byte	2,35,28,10
	.byte	'IRQ_EN',0,4
	.word	7052
	.byte	2,35,32,10
	.byte	'IRQ_FORCINT',0,4
	.word	7215
	.byte	2,35,36,10
	.byte	'IRQ_MODE',0,4
	.word	7384
	.byte	2,35,40,13,4
	.word	238
	.byte	14,3,0,10
	.byte	'reserved_2C',0,4
	.word	34926
	.byte	2,35,44,0,15
	.word	34736
	.byte	27
	.byte	'Ifx_GTM_TOM_CH',0,3,178,17,3
	.word	34957
	.byte	11
	.byte	'_Ifx_GTM_BRIDGE',0,3,191,17,25,12,10
	.byte	'MODE',0,4
	.word	28177
	.byte	2,35,0,10
	.byte	'PTR1',0,4
	.word	28246
	.byte	2,35,4,10
	.byte	'PTR2',0,4
	.word	28315
	.byte	2,35,8,0,15
	.word	34986
	.byte	27
	.byte	'Ifx_GTM_BRIDGE',0,3,196,17,3
	.word	35051
	.byte	11
	.byte	'_Ifx_GTM_CMU',0,3,199,17,25,72,10
	.byte	'CLK_EN',0,4
	.word	28662
	.byte	2,35,0,10
	.byte	'GCLK_NUM',0,4
	.word	29012
	.byte	2,35,4,10
	.byte	'GCLK_DEN',0,4
	.word	28942
	.byte	2,35,8,13,24
	.word	33759
	.byte	14,5,0,15
	.word	35151
	.byte	10
	.byte	'CLK0_5',0,24
	.word	35160
	.byte	2,35,12,15
	.word	33833
	.byte	10
	.byte	'CLK_6',0,4
	.word	35181
	.byte	2,35,36,15
	.word	33905
	.byte	10
	.byte	'CLK_7',0,4
	.word	35201
	.byte	2,35,40,13,24
	.word	33977
	.byte	14,2,0,15
	.word	35221
	.byte	10
	.byte	'ECLK',0,24
	.word	35230
	.byte	2,35,44,15
	.word	34059
	.byte	10
	.byte	'FXCLK',0,4
	.word	35249
	.byte	2,35,68,0,15
	.word	35080
	.byte	27
	.byte	'Ifx_GTM_CMU',0,3,209,17,3
	.word	35270
	.byte	11
	.byte	'_Ifx_GTM_DTM',0,3,212,17,25,36,10
	.byte	'CTRL',0,4
	.word	29357
	.byte	2,35,0,10
	.byte	'CH_CTRL1',0,4
	.word	29144
	.byte	2,35,4,10
	.byte	'CH_CTRL2',0,4
	.word	29214
	.byte	2,35,8,10
	.byte	'CH_CTRL2_SR',0,4
	.word	29284
	.byte	2,35,12,10
	.byte	'PS_CTRL',0,4
	.word	29491
	.byte	2,35,16,13,16
	.word	29423
	.byte	14,3,0,10
	.byte	'DTV_CH',0,16
	.word	35403
	.byte	2,35,20,0,15
	.word	35296
	.byte	27
	.byte	'Ifx_GTM_DTM',0,3,220,17,3
	.word	35429
	.byte	11
	.byte	'_Ifx_GTM_ICM',0,3,223,17,25,60,10
	.byte	'IRQG_0',0,4
	.word	29690
	.byte	2,35,0,10
	.byte	'reserved_4',0,4
	.word	34926
	.byte	2,35,4,10
	.byte	'IRQG_2',0,4
	.word	29758
	.byte	2,35,8,13,12
	.word	238
	.byte	14,11,0,10
	.byte	'reserved_C',0,12
	.word	35526
	.byte	2,35,12,10
	.byte	'IRQG_6',0,4
	.word	29826
	.byte	2,35,24,13,20
	.word	238
	.byte	14,19,0,10
	.byte	'reserved_1C',0,20
	.word	35571
	.byte	2,35,28,10
	.byte	'IRQG_MEI',0,4
	.word	29965
	.byte	2,35,48,10
	.byte	'reserved_34',0,4
	.word	34926
	.byte	2,35,52,10
	.byte	'IRQG_CEI1',0,4
	.word	29894
	.byte	2,35,56,0,15
	.word	35455
	.byte	27
	.byte	'Ifx_GTM_ICM',0,3,234,17,3
	.word	35660
	.byte	11
	.byte	'_Ifx_GTM_INOUTSEL',0,3,237,17,25,148,1,13,32
	.word	34296
	.byte	14,0,0,15
	.word	35711
	.byte	10
	.byte	'TIM',0,32
	.word	35720
	.byte	2,35,0,15
	.word	34211
	.byte	10
	.byte	'T',0,32
	.word	35738
	.byte	2,35,32,13,80
	.word	238
	.byte	14,79,0,10
	.byte	'reserved_40',0,80
	.word	35754
	.byte	2,35,64,15
	.word	34131
	.byte	10
	.byte	'CAN',0,4
	.word	35784
	.byte	3,35,144,1,0,15
	.word	35686
	.byte	27
	.byte	'Ifx_GTM_INOUTSEL',0,3,243,17,3
	.word	35804
	.byte	11
	.byte	'_Ifx_GTM_TBU',0,3,246,17,25,28,10
	.byte	'CHEN',0,4
	.word	31702
	.byte	2,35,0,10
	.byte	'CH0_CTRL',0,4
	.word	31352
	.byte	2,35,4,10
	.byte	'CH0_BASE',0,4
	.word	31282
	.byte	2,35,8,10
	.byte	'CH1_CTRL',0,4
	.word	31492
	.byte	2,35,12,10
	.byte	'CH1_BASE',0,4
	.word	31422
	.byte	2,35,16,10
	.byte	'CH2_CTRL',0,4
	.word	31632
	.byte	2,35,20,10
	.byte	'CH2_BASE',0,4
	.word	31562
	.byte	2,35,24,0,15
	.word	35835
	.byte	27
	.byte	'Ifx_GTM_TBU',0,3,255,17,3
	.word	35977
	.byte	11
	.byte	'_Ifx_GTM_TIM',0,3,130,18,25,128,8,15
	.word	34404
	.byte	10
	.byte	'CH0',0,116
	.word	36023
	.byte	2,35,0,10
	.byte	'INP_VAL',0,4
	.word	32398
	.byte	2,35,116,10
	.byte	'IN_SRC',0,4
	.word	32330
	.byte	2,35,120,10
	.byte	'RST',0,4
	.word	32467
	.byte	2,35,124,15
	.word	34404
	.byte	10
	.byte	'CH1',0,116
	.word	36087
	.byte	3,35,128,1,10
	.byte	'reserved_F4',0,12
	.word	35526
	.byte	3,35,244,1,15
	.word	34404
	.byte	10
	.byte	'CH2',0,116
	.word	36128
	.byte	3,35,128,2,10
	.byte	'reserved_174',0,12
	.word	35526
	.byte	3,35,244,2,15
	.word	34404
	.byte	10
	.byte	'CH3',0,116
	.word	36170
	.byte	3,35,128,3,10
	.byte	'reserved_1F4',0,12
	.word	35526
	.byte	3,35,244,3,15
	.word	34404
	.byte	10
	.byte	'CH4',0,116
	.word	36212
	.byte	3,35,128,4,10
	.byte	'reserved_274',0,12
	.word	35526
	.byte	3,35,244,4,15
	.word	34404
	.byte	10
	.byte	'CH5',0,116
	.word	36254
	.byte	3,35,128,5,10
	.byte	'reserved_2F4',0,12
	.word	35526
	.byte	3,35,244,5,15
	.word	34404
	.byte	10
	.byte	'CH6',0,116
	.word	36296
	.byte	3,35,128,6,10
	.byte	'reserved_374',0,12
	.word	35526
	.byte	3,35,244,6,15
	.word	34404
	.byte	10
	.byte	'CH7',0,116
	.word	36338
	.byte	3,35,128,7,10
	.byte	'reserved_3F4',0,12
	.word	35526
	.byte	3,35,244,7,0,15
	.word	36003
	.byte	27
	.byte	'Ifx_GTM_TIM',0,3,150,18,3
	.word	36381
	.byte	11
	.byte	'_Ifx_GTM_TOM',0,3,153,18,25,128,16,15
	.word	34736
	.byte	10
	.byte	'CH0',0,48
	.word	36427
	.byte	2,35,0,10
	.byte	'TGC0_GLB_CTRL',0,4
	.word	666
	.byte	2,35,48,10
	.byte	'TGC0_ACT_TB',0,4
	.word	1173
	.byte	2,35,52,10
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	1345
	.byte	2,35,56,10
	.byte	'TGC0_INT_TRIG',0,4
	.word	1796
	.byte	2,35,60,15
	.word	34736
	.byte	10
	.byte	'CH1',0,48
	.word	36536
	.byte	2,35,64,10
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	2116
	.byte	2,35,112,10
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	2424
	.byte	2,35,116,10
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	2732
	.byte	2,35,120,10
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	3040
	.byte	2,35,124,15
	.word	34736
	.byte	10
	.byte	'CH2',0,48
	.word	36654
	.byte	3,35,128,1,13,16
	.word	238
	.byte	14,15,0,10
	.byte	'reserved_B0',0,16
	.word	36673
	.byte	3,35,176,1,15
	.word	34736
	.byte	10
	.byte	'CH3',0,48
	.word	36704
	.byte	3,35,192,1,10
	.byte	'reserved_F0',0,16
	.word	36673
	.byte	3,35,240,1,15
	.word	34736
	.byte	10
	.byte	'CH4',0,48
	.word	36745
	.byte	3,35,128,2,10
	.byte	'reserved_130',0,16
	.word	36673
	.byte	3,35,176,2,15
	.word	34736
	.byte	10
	.byte	'CH5',0,48
	.word	36787
	.byte	3,35,192,2,10
	.byte	'reserved_170',0,16
	.word	36673
	.byte	3,35,240,2,15
	.word	34736
	.byte	10
	.byte	'CH6',0,48
	.word	36829
	.byte	3,35,128,3,10
	.byte	'reserved_1B0',0,16
	.word	36673
	.byte	3,35,176,3,15
	.word	34736
	.byte	10
	.byte	'CH7',0,48
	.word	36871
	.byte	3,35,192,3,10
	.byte	'reserved_1F0',0,16
	.word	36673
	.byte	3,35,240,3,15
	.word	34736
	.byte	10
	.byte	'CH8',0,48
	.word	36913
	.byte	3,35,128,4,10
	.byte	'TGC1_GLB_CTRL',0,4
	.word	33455
	.byte	3,35,176,4,10
	.byte	'TGC1_ACT_TB',0,4
	.word	33152
	.byte	3,35,180,4,10
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	33379
	.byte	3,35,184,4,10
	.byte	'TGC1_INT_TRIG',0,4
	.word	33530
	.byte	3,35,188,4,15
	.word	34736
	.byte	10
	.byte	'CH9',0,48
	.word	37027
	.byte	3,35,192,4,10
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	33225
	.byte	3,35,240,4,10
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	33302
	.byte	3,35,244,4,10
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	33605
	.byte	3,35,248,4,10
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	33682
	.byte	3,35,252,4,15
	.word	34736
	.byte	10
	.byte	'CH10',0,48
	.word	37150
	.byte	3,35,128,5,10
	.byte	'reserved_2B0',0,16
	.word	36673
	.byte	3,35,176,5,15
	.word	34736
	.byte	10
	.byte	'CH11',0,48
	.word	37193
	.byte	3,35,192,5,10
	.byte	'reserved_2F0',0,16
	.word	36673
	.byte	3,35,240,5,15
	.word	34736
	.byte	10
	.byte	'CH12',0,48
	.word	37236
	.byte	3,35,128,6,10
	.byte	'reserved_330',0,16
	.word	36673
	.byte	3,35,176,6,15
	.word	34736
	.byte	10
	.byte	'CH13',0,48
	.word	37279
	.byte	3,35,192,6,10
	.byte	'reserved_370',0,16
	.word	36673
	.byte	3,35,240,6,15
	.word	34736
	.byte	10
	.byte	'CH14',0,48
	.word	37322
	.byte	3,35,128,7,10
	.byte	'reserved_3B0',0,16
	.word	36673
	.byte	3,35,176,7,15
	.word	34736
	.byte	10
	.byte	'CH15',0,48
	.word	37365
	.byte	3,35,192,7,13,144,8
	.word	238
	.byte	14,143,8,0,10
	.byte	'reserved_3F0',0,144,8
	.word	37385
	.byte	3,35,240,7,0,15
	.word	36407
	.byte	27
	.byte	'Ifx_GTM_TOM',0,3,199,18,3
	.word	37421
	.byte	15
	.word	5803
	.byte	27
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,2,155,4,4
	.word	37447
	.byte	15
	.word	659
	.byte	27
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,2,177,4,5
	.word	37481
	.byte	8,2,179,4,20,128,16,10
	.byte	'reserved_tom0',0,48
	.word	2084
	.byte	2,35,0,13,128,8
	.word	659
	.byte	14,1,0,15
	.word	37546
	.byte	10
	.byte	'TGC',0,128,8
	.word	37556
	.byte	2,35,48,13,208,7
	.word	238
	.byte	14,207,7,0,10
	.byte	'reserved_tgc2',0,208,7
	.word	37575
	.byte	3,35,176,8,0,15
	.word	37516
	.byte	27
	.byte	'Ifx_GTM_TOM_TGCx',0,2,184,4,5
	.word	37612
	.byte	8,2,187,4,20,128,16,13,128,8
	.word	5803
	.byte	14,15,0,15
	.word	37650
	.byte	10
	.byte	'CH',0,128,8
	.word	37660
	.byte	2,35,0,13,128,8
	.word	238
	.byte	14,255,7,0,10
	.byte	'reserved_tom1',0,128,8
	.word	37678
	.byte	3,35,128,8,0,15
	.word	37643
	.byte	27
	.byte	'Ifx_GTM_TOM_CHx',0,2,191,4,5
	.word	37715
	.byte	15
	.word	7567
	.byte	27
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,2,248,4,4
	.word	37745
	.byte	8,2,250,4,20,8,10
	.byte	'IN_SRC',0,4
	.word	32330
	.byte	2,35,0,10
	.byte	'RST',0,4
	.word	32467
	.byte	2,35,4,0,15
	.word	37779
	.byte	27
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,2,255,4,4
	.word	37815
	.byte	8,2,129,5,21,128,16,13,128,8
	.word	7567
	.byte	14,7,0,15
	.word	37866
	.byte	10
	.byte	'CH',0,128,8
	.word	37876
	.byte	2,35,0,10
	.byte	'reserved_tim1',0,128,8
	.word	37678
	.byte	3,35,128,8,0,15
	.word	37859
	.byte	27
	.byte	'Ifx_GTM_TIM_CHx',0,2,133,5,4
	.word	37920
	.byte	8,2,135,5,20,128,16,13,120
	.word	238
	.byte	14,119,0,10
	.byte	'reserved_tim2',0,120
	.word	37957
	.byte	2,35,0,15
	.word	37779
	.byte	10
	.byte	'IN_SRC_RESET',0,8
	.word	37989
	.byte	2,35,120,13,128,15
	.word	238
	.byte	14,255,14,0,10
	.byte	'reserved_tim3',0,128,15
	.word	38016
	.byte	3,35,128,1,0,15
	.word	37950
	.byte	27
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,2,140,5,4
	.word	38053
	.byte	29,2,174,5,11,1,30
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,30
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,30
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,30
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,30
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,30
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,30
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,30
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,27
	.byte	'Gtm_ConfigurableClockType',0,2,184,5,4
	.word	38091
	.byte	29,2,188,5,11,1,30
	.byte	'GTM_LOW',0,0,30
	.byte	'GTM_HIGH',0,1,0,27
	.byte	'Gtm_OutputLevelType',0,2,192,5,4
	.word	38325
	.byte	29,2,195,5,11,1,30
	.byte	'TOM_GLB_CTRL',0,0,30
	.byte	'TOM_ACT_TB',0,1,30
	.byte	'TOM_FUPD_CTRL',0,2,30
	.byte	'TOM_INT_TRIG',0,3,30
	.byte	'TOM_RESERVED_0',0,4,30
	.byte	'TOM_RESERVED_1',0,5,30
	.byte	'TOM_RESERVED_2',0,6,30
	.byte	'TOM_RESERVED_3',0,7,30
	.byte	'TOM_RESERVED_4',0,8,30
	.byte	'TOM_RESERVED_5',0,9,30
	.byte	'TOM_RESERVED_6',0,10,30
	.byte	'TOM_RESERVED_7',0,11,30
	.byte	'TOM_RESERVED_8',0,12,30
	.byte	'TOM_RESERVED_9',0,13,30
	.byte	'TOM_RESERVED_10',0,14,30
	.byte	'TOM_RESERVED_11',0,15,30
	.byte	'TOM_ENDIS_CTRL',0,16,30
	.byte	'TOM_ENDIS_STAT',0,17,30
	.byte	'TOM_OUTEN_CTRL',0,18,30
	.byte	'TOM_OUTEN_STAT',0,19,0,27
	.byte	'Gtm_TomTimerRegistersType',0,2,217,5,4
	.word	38382
	.byte	8,2,221,5,11,8,10
	.byte	'FltRisingEdge',0,4
	.word	172
	.byte	2,35,0,10
	.byte	'FltFallingEdge',0,4
	.word	172
	.byte	2,35,4,0,27
	.byte	'Gtm_TimFilterType',0,2,225,5,4
	.word	38757
	.byte	27
	.byte	'Gtm_TbuChCtrlType',0,2,230,5,32
	.word	31352
	.byte	27
	.byte	'Gtm_TbuChBaseType',0,2,231,5,32
	.word	31282
	.byte	8,2,233,5,11,8,10
	.byte	'CH_CTRL',0,4
	.word	31352
	.byte	2,35,0,10
	.byte	'CH_BASE',0,4
	.word	31282
	.byte	2,35,4,0,27
	.byte	'Gtm_TbuChType',0,2,237,5,4
	.word	38892
	.byte	27
	.byte	'Gtm_PortConfigType',0,2,253,5,2
	.word	4090
	.byte	27
	.byte	'Gtm_TimFltType',0,2,134,6,2
	.word	4500
	.byte	27
	.byte	'Gtm_TimConfigType',0,2,151,6,4
	.word	4393
	.byte	27
	.byte	'Gtm_ModUsageConfigType',0,2,163,6,4
	.word	5357
	.byte	27
	.byte	'Gtm_TomTgcConfigGroupType',0,2,185,6,2
	.word	4772
	.byte	27
	.byte	'Gtm_TomTgcConfigType',0,2,196,6,2
	.word	4722
	.byte	27
	.byte	'Gtm_TomChannelConfigType',0,2,207,6,2
	.word	5134
	.byte	27
	.byte	'Gtm_TomConfigType',0,2,219,6,2
	.word	5060
	.byte	27
	.byte	'Gtm_ExtClkType',0,2,227,6,2
	.word	3978
	.byte	27
	.byte	'Gtm_ClockSettingType',0,2,236,6,2
	.word	3891
	.byte	27
	.byte	'Gtm_GeneralConfigType',0,2,245,6,2
	.word	5475
	.byte	27
	.byte	'Gtm_TbuConfigType',0,2,253,6,2
	.word	5565
	.byte	27
	.byte	'Gtm_ModuleConfigType',0,2,163,7,2
	.word	4177
	.byte	31,1,1,32
	.word	238
	.byte	32
	.word	238
	.byte	32
	.word	238
	.byte	32
	.word	4305
	.byte	0,16
	.word	39335
	.byte	27
	.byte	'Gtm_NotificationPtrType',0,2,172,7,16
	.word	39359
	.byte	8,2,176,7,9,40,13,16
	.word	39364
	.byte	14,3,0,10
	.byte	'TimNotifUsage',0,16
	.word	39403
	.byte	2,35,0,13,24
	.word	39364
	.byte	14,5,0,10
	.byte	'TomNotifUsage',0,24
	.word	39435
	.byte	2,35,16,0,27
	.byte	'Gtm_NotificationConfigType',0,2,184,7,2
	.word	39397
	.byte	16
	.word	39335
	.byte	27
	.byte	'Gtm_ConfigType',0,2,197,7,2
	.word	3870
	.byte	11
	.byte	'_Ifx_SRC_SRCR_Bits',0,8,45,16,4,12
	.byte	'SRPN',0,1
	.word	238
	.byte	8,0,2,35,0,12
	.byte	'reserved_8',0,1
	.word	238
	.byte	2,6,2,35,1,12
	.byte	'SRE',0,1
	.word	238
	.byte	1,5,2,35,1,12
	.byte	'TOS',0,1
	.word	238
	.byte	1,4,2,35,1,12
	.byte	'reserved_12',0,1
	.word	238
	.byte	4,0,2,35,1,12
	.byte	'ECC',0,1
	.word	238
	.byte	5,3,2,35,2,12
	.byte	'reserved_21',0,1
	.word	238
	.byte	3,0,2,35,2,12
	.byte	'SRR',0,1
	.word	238
	.byte	1,7,2,35,3,12
	.byte	'CLRR',0,1
	.word	238
	.byte	1,6,2,35,3,12
	.byte	'SETR',0,1
	.word	238
	.byte	1,5,2,35,3,12
	.byte	'IOV',0,1
	.word	238
	.byte	1,4,2,35,3,12
	.byte	'IOVCLR',0,1
	.word	238
	.byte	1,3,2,35,3,12
	.byte	'SWS',0,1
	.word	238
	.byte	1,2,2,35,3,12
	.byte	'SWSCLR',0,1
	.word	238
	.byte	1,1,2,35,3,12
	.byte	'reserved_31',0,1
	.word	238
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SRC_SRCR_Bits',0,8,62,3
	.word	39533
	.byte	9,8,70,9,4,10
	.byte	'U',0,4
	.word	672
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	699
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39533
	.byte	2,35,0,0,27
	.byte	'Ifx_SRC_SRCR',0,8,75,3
	.word	39849
	.byte	11
	.byte	'_Ifx_SRC_ASCLIN',0,8,86,25,12,10
	.byte	'TX',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'RX',0,4
	.word	39849
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	39849
	.byte	2,35,8,0,15
	.word	39909
	.byte	27
	.byte	'Ifx_SRC_ASCLIN',0,8,91,3
	.word	39968
	.byte	11
	.byte	'_Ifx_SRC_BCUSPB',0,8,94,25,4,10
	.byte	'SBSRC',0,4
	.word	39849
	.byte	2,35,0,0,15
	.word	39996
	.byte	27
	.byte	'Ifx_SRC_BCUSPB',0,8,97,3
	.word	40033
	.byte	11
	.byte	'_Ifx_SRC_CAN',0,8,100,25,64,13,64
	.word	39849
	.byte	14,15,0,10
	.byte	'INT',0,64
	.word	40079
	.byte	2,35,0,0,15
	.word	40061
	.byte	27
	.byte	'Ifx_SRC_CAN',0,8,103,3
	.word	40102
	.byte	11
	.byte	'_Ifx_SRC_CAN1',0,8,106,25,32,13,32
	.word	39849
	.byte	14,7,0,10
	.byte	'INT',0,32
	.word	40146
	.byte	2,35,0,0,15
	.word	40127
	.byte	27
	.byte	'Ifx_SRC_CAN1',0,8,109,3
	.word	40169
	.byte	11
	.byte	'_Ifx_SRC_CCU6',0,8,112,25,16,10
	.byte	'SR0',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	39849
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	39849
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	39849
	.byte	2,35,12,0,15
	.word	40195
	.byte	27
	.byte	'Ifx_SRC_CCU6',0,8,118,3
	.word	40267
	.byte	11
	.byte	'_Ifx_SRC_CERBERUS',0,8,121,25,8,13,8
	.word	39849
	.byte	14,1,0,10
	.byte	'SR',0,8
	.word	40316
	.byte	2,35,0,0,15
	.word	40293
	.byte	27
	.byte	'Ifx_SRC_CERBERUS',0,8,124,3
	.word	40338
	.byte	11
	.byte	'_Ifx_SRC_CPU',0,8,127,25,32,10
	.byte	'SBSRC',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'reserved_4',0,28
	.word	34339
	.byte	2,35,4,0,15
	.word	40368
	.byte	27
	.byte	'Ifx_SRC_CPU',0,8,131,1,3
	.word	40422
	.byte	11
	.byte	'_Ifx_SRC_DMA',0,8,134,1,25,80,10
	.byte	'ERR',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'reserved_4',0,12
	.word	35526
	.byte	2,35,4,10
	.byte	'CH',0,64
	.word	40079
	.byte	2,35,16,0,15
	.word	40448
	.byte	27
	.byte	'Ifx_SRC_DMA',0,8,139,1,3
	.word	40513
	.byte	11
	.byte	'_Ifx_SRC_EMEM',0,8,142,1,25,4,10
	.byte	'SR',0,4
	.word	39849
	.byte	2,35,0,0,15
	.word	40539
	.byte	27
	.byte	'Ifx_SRC_EMEM',0,8,145,1,3
	.word	40572
	.byte	11
	.byte	'_Ifx_SRC_ERAY',0,8,148,1,25,80,10
	.byte	'INT',0,8
	.word	40316
	.byte	2,35,0,10
	.byte	'TINT',0,8
	.word	40316
	.byte	2,35,8,10
	.byte	'NDAT',0,8
	.word	40316
	.byte	2,35,16,10
	.byte	'MBSC',0,8
	.word	40316
	.byte	2,35,24,10
	.byte	'OBUSY',0,4
	.word	39849
	.byte	2,35,32,10
	.byte	'IBUSY',0,4
	.word	39849
	.byte	2,35,36,13,40
	.word	238
	.byte	14,39,0,10
	.byte	'reserved_28',0,40
	.word	40704
	.byte	2,35,40,0,15
	.word	40599
	.byte	27
	.byte	'Ifx_SRC_ERAY',0,8,157,1,3
	.word	40735
	.byte	11
	.byte	'_Ifx_SRC_ETH',0,8,160,1,25,4,10
	.byte	'SR',0,4
	.word	39849
	.byte	2,35,0,0,15
	.word	40762
	.byte	27
	.byte	'Ifx_SRC_ETH',0,8,163,1,3
	.word	40794
	.byte	11
	.byte	'_Ifx_SRC_EVR',0,8,166,1,25,8,10
	.byte	'WUT',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'SCDC',0,4
	.word	39849
	.byte	2,35,4,0,15
	.word	40820
	.byte	27
	.byte	'Ifx_SRC_EVR',0,8,170,1,3
	.word	40867
	.byte	11
	.byte	'_Ifx_SRC_FFT',0,8,173,1,25,12,10
	.byte	'DONE',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'ERR',0,4
	.word	39849
	.byte	2,35,4,10
	.byte	'RFS',0,4
	.word	39849
	.byte	2,35,8,0,15
	.word	40893
	.byte	27
	.byte	'Ifx_SRC_FFT',0,8,178,1,3
	.word	40953
	.byte	11
	.byte	'_Ifx_SRC_GPSR',0,8,181,1,25,128,12,10
	.byte	'SR0',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	39849
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	39849
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	39849
	.byte	2,35,12,13,240,11
	.word	238
	.byte	14,239,11,0,10
	.byte	'reserved_10',0,240,11
	.word	41052
	.byte	2,35,16,0,15
	.word	40979
	.byte	27
	.byte	'Ifx_SRC_GPSR',0,8,188,1,3
	.word	41086
	.byte	11
	.byte	'_Ifx_SRC_GPT12',0,8,191,1,25,48,10
	.byte	'CIRQ',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'T2',0,4
	.word	39849
	.byte	2,35,4,10
	.byte	'T3',0,4
	.word	39849
	.byte	2,35,8,10
	.byte	'T4',0,4
	.word	39849
	.byte	2,35,12,10
	.byte	'T5',0,4
	.word	39849
	.byte	2,35,16,10
	.byte	'T6',0,4
	.word	39849
	.byte	2,35,20,13,24
	.word	238
	.byte	14,23,0,10
	.byte	'reserved_18',0,24
	.word	41208
	.byte	2,35,24,0,15
	.word	41113
	.byte	27
	.byte	'Ifx_SRC_GPT12',0,8,200,1,3
	.word	41239
	.byte	11
	.byte	'_Ifx_SRC_GTM',0,8,203,1,25,192,11,10
	.byte	'AEIIRQ',0,4
	.word	39849
	.byte	2,35,0,13,236,2
	.word	238
	.byte	14,235,2,0,10
	.byte	'reserved_4',0,236,2
	.word	41303
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	39849
	.byte	3,35,240,2,10
	.byte	'reserved_174',0,12
	.word	35526
	.byte	3,35,244,2,13,32
	.word	40146
	.byte	14,0,0,10
	.byte	'TIM',0,32
	.word	41372
	.byte	3,35,128,3,13,224,7
	.word	238
	.byte	14,223,7,0,10
	.byte	'reserved_1A0',0,224,7
	.word	41395
	.byte	3,35,160,3,13,64
	.word	40146
	.byte	14,1,0,10
	.byte	'TOM',0,64
	.word	41430
	.byte	3,35,128,11,0,15
	.word	41267
	.byte	27
	.byte	'Ifx_SRC_GTM',0,8,212,1,3
	.word	41454
	.byte	11
	.byte	'_Ifx_SRC_HSM',0,8,215,1,25,8,10
	.byte	'HSM',0,8
	.word	40316
	.byte	2,35,0,0,15
	.word	41480
	.byte	27
	.byte	'Ifx_SRC_HSM',0,8,218,1,3
	.word	41513
	.byte	11
	.byte	'_Ifx_SRC_LMU',0,8,221,1,25,4,10
	.byte	'SR',0,4
	.word	39849
	.byte	2,35,0,0,15
	.word	41539
	.byte	27
	.byte	'Ifx_SRC_LMU',0,8,224,1,3
	.word	41571
	.byte	11
	.byte	'_Ifx_SRC_PMU',0,8,227,1,25,4,10
	.byte	'SR',0,4
	.word	39849
	.byte	2,35,0,0,15
	.word	41597
	.byte	27
	.byte	'Ifx_SRC_PMU',0,8,230,1,3
	.word	41629
	.byte	11
	.byte	'_Ifx_SRC_QSPI',0,8,233,1,25,24,10
	.byte	'TX',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'RX',0,4
	.word	39849
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	39849
	.byte	2,35,8,10
	.byte	'PT',0,4
	.word	39849
	.byte	2,35,12,10
	.byte	'HC',0,4
	.word	39849
	.byte	2,35,16,10
	.byte	'U',0,4
	.word	39849
	.byte	2,35,20,0,15
	.word	41655
	.byte	27
	.byte	'Ifx_SRC_QSPI',0,8,241,1,3
	.word	41748
	.byte	11
	.byte	'_Ifx_SRC_SCU',0,8,244,1,25,20,10
	.byte	'DTS',0,4
	.word	39849
	.byte	2,35,0,13,16
	.word	39849
	.byte	14,3,0,10
	.byte	'ERU',0,16
	.word	41807
	.byte	2,35,4,0,15
	.word	41775
	.byte	27
	.byte	'Ifx_SRC_SCU',0,8,248,1,3
	.word	41830
	.byte	11
	.byte	'_Ifx_SRC_SENT',0,8,251,1,25,16,10
	.byte	'SR',0,16
	.word	41807
	.byte	2,35,0,0,15
	.word	41856
	.byte	27
	.byte	'Ifx_SRC_SENT',0,8,254,1,3
	.word	41889
	.byte	11
	.byte	'_Ifx_SRC_SMU',0,8,129,2,25,12,13,12
	.word	39849
	.byte	14,2,0,10
	.byte	'SR',0,12
	.word	41935
	.byte	2,35,0,0,15
	.word	41916
	.byte	27
	.byte	'Ifx_SRC_SMU',0,8,132,2,3
	.word	41957
	.byte	11
	.byte	'_Ifx_SRC_STM',0,8,135,2,25,96,10
	.byte	'SR0',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	39849
	.byte	2,35,4,13,88
	.word	238
	.byte	14,87,0,10
	.byte	'reserved_8',0,88
	.word	42028
	.byte	2,35,8,0,15
	.word	41983
	.byte	27
	.byte	'Ifx_SRC_STM',0,8,140,2,3
	.word	42058
	.byte	11
	.byte	'_Ifx_SRC_VADCCG',0,8,143,2,25,192,2,10
	.byte	'SR0',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	39849
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	39849
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	39849
	.byte	2,35,12,13,176,2
	.word	238
	.byte	14,175,2,0,10
	.byte	'reserved_10',0,176,2
	.word	42159
	.byte	2,35,16,0,15
	.word	42084
	.byte	27
	.byte	'Ifx_SRC_VADCCG',0,8,150,2,3
	.word	42193
	.byte	11
	.byte	'_Ifx_SRC_VADCG',0,8,153,2,25,16,10
	.byte	'SR0',0,4
	.word	39849
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	39849
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	39849
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	39849
	.byte	2,35,12,0,15
	.word	42222
	.byte	27
	.byte	'Ifx_SRC_VADCG',0,8,159,2,3
	.word	42296
	.byte	11
	.byte	'_Ifx_SRC_XBAR',0,8,162,2,25,4,10
	.byte	'SRC',0,4
	.word	39849
	.byte	2,35,0,0,15
	.word	42324
	.byte	27
	.byte	'Ifx_SRC_XBAR',0,8,165,2,3
	.word	42358
	.byte	11
	.byte	'_Ifx_SRC_GASCLIN',0,8,178,2,25,24,13,24
	.word	39909
	.byte	14,1,0,15
	.word	42408
	.byte	10
	.byte	'ASCLIN',0,24
	.word	42417
	.byte	2,35,0,0,15
	.word	42385
	.byte	27
	.byte	'Ifx_SRC_GASCLIN',0,8,181,2,3
	.word	42439
	.byte	11
	.byte	'_Ifx_SRC_GBCU',0,8,184,2,25,4,15
	.word	39996
	.byte	10
	.byte	'SPB',0,4
	.word	42489
	.byte	2,35,0,0,15
	.word	42469
	.byte	27
	.byte	'Ifx_SRC_GBCU',0,8,187,2,3
	.word	42508
	.byte	11
	.byte	'_Ifx_SRC_GCAN',0,8,190,2,25,96,13,64
	.word	40061
	.byte	14,0,0,15
	.word	42555
	.byte	10
	.byte	'CAN',0,64
	.word	42564
	.byte	2,35,0,13,32
	.word	40127
	.byte	14,0,0,15
	.word	42582
	.byte	10
	.byte	'CAN1',0,32
	.word	42591
	.byte	2,35,64,0,15
	.word	42535
	.byte	27
	.byte	'Ifx_SRC_GCAN',0,8,194,2,3
	.word	42611
	.byte	11
	.byte	'_Ifx_SRC_GCCU6',0,8,197,2,25,32,13,32
	.word	40195
	.byte	14,1,0,15
	.word	42659
	.byte	10
	.byte	'CCU6',0,32
	.word	42668
	.byte	2,35,0,0,15
	.word	42638
	.byte	27
	.byte	'Ifx_SRC_GCCU6',0,8,200,2,3
	.word	42688
	.byte	11
	.byte	'_Ifx_SRC_GCERBERUS',0,8,203,2,25,8,15
	.word	40293
	.byte	10
	.byte	'CERBERUS',0,8
	.word	42741
	.byte	2,35,0,0,15
	.word	42716
	.byte	27
	.byte	'Ifx_SRC_GCERBERUS',0,8,206,2,3
	.word	42765
	.byte	11
	.byte	'_Ifx_SRC_GCPU',0,8,209,2,25,32,13,32
	.word	40368
	.byte	14,0,0,15
	.word	42817
	.byte	10
	.byte	'CPU',0,32
	.word	42826
	.byte	2,35,0,0,15
	.word	42797
	.byte	27
	.byte	'Ifx_SRC_GCPU',0,8,212,2,3
	.word	42845
	.byte	11
	.byte	'_Ifx_SRC_GDMA',0,8,215,2,25,80,13,80
	.word	40448
	.byte	14,0,0,15
	.word	42892
	.byte	10
	.byte	'DMA',0,80
	.word	42901
	.byte	2,35,0,0,15
	.word	42872
	.byte	27
	.byte	'Ifx_SRC_GDMA',0,8,218,2,3
	.word	42920
	.byte	11
	.byte	'_Ifx_SRC_GEMEM',0,8,221,2,25,4,13,4
	.word	40539
	.byte	14,0,0,15
	.word	42968
	.byte	10
	.byte	'EMEM',0,4
	.word	42977
	.byte	2,35,0,0,15
	.word	42947
	.byte	27
	.byte	'Ifx_SRC_GEMEM',0,8,224,2,3
	.word	42997
	.byte	11
	.byte	'_Ifx_SRC_GERAY',0,8,227,2,25,80,13,80
	.word	40599
	.byte	14,0,0,15
	.word	43046
	.byte	10
	.byte	'ERAY',0,80
	.word	43055
	.byte	2,35,0,0,15
	.word	43025
	.byte	27
	.byte	'Ifx_SRC_GERAY',0,8,230,2,3
	.word	43075
	.byte	11
	.byte	'_Ifx_SRC_GETH',0,8,233,2,25,4,13,4
	.word	40762
	.byte	14,0,0,15
	.word	43123
	.byte	10
	.byte	'ETH',0,4
	.word	43132
	.byte	2,35,0,0,15
	.word	43103
	.byte	27
	.byte	'Ifx_SRC_GETH',0,8,236,2,3
	.word	43151
	.byte	11
	.byte	'_Ifx_SRC_GEVR',0,8,239,2,25,8,13,8
	.word	40820
	.byte	14,0,0,15
	.word	43198
	.byte	10
	.byte	'EVR',0,8
	.word	43207
	.byte	2,35,0,0,15
	.word	43178
	.byte	27
	.byte	'Ifx_SRC_GEVR',0,8,242,2,3
	.word	43226
	.byte	11
	.byte	'_Ifx_SRC_GFFT',0,8,245,2,25,12,13,12
	.word	40893
	.byte	14,0,0,15
	.word	43273
	.byte	10
	.byte	'FFT',0,12
	.word	43282
	.byte	2,35,0,0,15
	.word	43253
	.byte	27
	.byte	'Ifx_SRC_GFFT',0,8,248,2,3
	.word	43301
	.byte	11
	.byte	'_Ifx_SRC_GGPSR',0,8,251,2,25,128,12,13,128,12
	.word	40979
	.byte	14,0,0,15
	.word	43350
	.byte	10
	.byte	'GPSR',0,128,12
	.word	43360
	.byte	2,35,0,0,15
	.word	43328
	.byte	27
	.byte	'Ifx_SRC_GGPSR',0,8,254,2,3
	.word	43381
	.byte	11
	.byte	'_Ifx_SRC_GGPT12',0,8,129,3,25,48,13,48
	.word	41113
	.byte	14,0,0,15
	.word	43431
	.byte	10
	.byte	'GPT12',0,48
	.word	43440
	.byte	2,35,0,0,15
	.word	43409
	.byte	27
	.byte	'Ifx_SRC_GGPT12',0,8,132,3,3
	.word	43461
	.byte	11
	.byte	'_Ifx_SRC_GGTM',0,8,135,3,25,192,11,13,192,11
	.word	41267
	.byte	14,0,0,15
	.word	43511
	.byte	10
	.byte	'GTM',0,192,11
	.word	43521
	.byte	2,35,0,0,15
	.word	43490
	.byte	27
	.byte	'Ifx_SRC_GGTM',0,8,138,3,3
	.word	43541
	.byte	11
	.byte	'_Ifx_SRC_GHSM',0,8,141,3,25,8,13,8
	.word	41480
	.byte	14,0,0,15
	.word	43588
	.byte	10
	.byte	'HSM',0,8
	.word	43597
	.byte	2,35,0,0,15
	.word	43568
	.byte	27
	.byte	'Ifx_SRC_GHSM',0,8,144,3,3
	.word	43616
	.byte	11
	.byte	'_Ifx_SRC_GLMU',0,8,147,3,25,4,13,4
	.word	41539
	.byte	14,0,0,15
	.word	43663
	.byte	10
	.byte	'LMU',0,4
	.word	43672
	.byte	2,35,0,0,15
	.word	43643
	.byte	27
	.byte	'Ifx_SRC_GLMU',0,8,150,3,3
	.word	43691
	.byte	11
	.byte	'_Ifx_SRC_GPMU',0,8,153,3,25,8,13,8
	.word	41597
	.byte	14,1,0,15
	.word	43738
	.byte	10
	.byte	'PMU',0,8
	.word	43747
	.byte	2,35,0,0,15
	.word	43718
	.byte	27
	.byte	'Ifx_SRC_GPMU',0,8,156,3,3
	.word	43766
	.byte	11
	.byte	'_Ifx_SRC_GQSPI',0,8,159,3,25,96,13,96
	.word	41655
	.byte	14,3,0,15
	.word	43814
	.byte	10
	.byte	'QSPI',0,96
	.word	43823
	.byte	2,35,0,0,15
	.word	43793
	.byte	27
	.byte	'Ifx_SRC_GQSPI',0,8,162,3,3
	.word	43843
	.byte	11
	.byte	'_Ifx_SRC_GSCU',0,8,165,3,25,20,15
	.word	41775
	.byte	10
	.byte	'SCU',0,20
	.word	43891
	.byte	2,35,0,0,15
	.word	43871
	.byte	27
	.byte	'Ifx_SRC_GSCU',0,8,168,3,3
	.word	43910
	.byte	11
	.byte	'_Ifx_SRC_GSENT',0,8,171,3,25,16,13,16
	.word	41856
	.byte	14,0,0,15
	.word	43958
	.byte	10
	.byte	'SENT',0,16
	.word	43967
	.byte	2,35,0,0,15
	.word	43937
	.byte	27
	.byte	'Ifx_SRC_GSENT',0,8,174,3,3
	.word	43987
	.byte	11
	.byte	'_Ifx_SRC_GSMU',0,8,177,3,25,12,13,12
	.word	41916
	.byte	14,0,0,15
	.word	44035
	.byte	10
	.byte	'SMU',0,12
	.word	44044
	.byte	2,35,0,0,15
	.word	44015
	.byte	27
	.byte	'Ifx_SRC_GSMU',0,8,180,3,3
	.word	44063
	.byte	11
	.byte	'_Ifx_SRC_GSTM',0,8,183,3,25,96,13,96
	.word	41983
	.byte	14,0,0,15
	.word	44110
	.byte	10
	.byte	'STM',0,96
	.word	44119
	.byte	2,35,0,0,15
	.word	44090
	.byte	27
	.byte	'Ifx_SRC_GSTM',0,8,186,3,3
	.word	44138
	.byte	11
	.byte	'_Ifx_SRC_GVADC',0,8,189,3,25,224,4,13,64
	.word	42222
	.byte	14,3,0,15
	.word	44187
	.byte	10
	.byte	'G',0,64
	.word	44196
	.byte	2,35,0,13,224,1
	.word	238
	.byte	14,223,1,0,10
	.byte	'reserved_40',0,224,1
	.word	44212
	.byte	2,35,64,13,192,2
	.word	42084
	.byte	14,0,0,15
	.word	44245
	.byte	10
	.byte	'CG',0,192,2
	.word	44255
	.byte	3,35,160,2,0,15
	.word	44165
	.byte	27
	.byte	'Ifx_SRC_GVADC',0,8,194,3,3
	.word	44275
	.byte	11
	.byte	'_Ifx_SRC_GXBAR',0,8,197,3,25,4,15
	.word	42324
	.byte	10
	.byte	'XBAR',0,4
	.word	44324
	.byte	2,35,0,0,15
	.word	44303
	.byte	27
	.byte	'Ifx_SRC_GXBAR',0,8,200,3,3
	.word	44344
	.byte	15
	.word	33759
	.byte	27
	.byte	'Gtm_CmuClkCtrl',0,1,150,1,28
	.word	44372
	.byte	27
	.byte	'Gtm_CmuEclkNumType',0,1,151,1,30
	.word	28800
	.byte	27
	.byte	'Gtm_CmuEclkDenType',0,1,152,1,30
	.word	28730
	.byte	8,1,154,1,9,8,10
	.byte	'CmuEclkNum',0,4
	.word	28800
	.byte	2,35,0,10
	.byte	'CmuEclkDen',0,4
	.word	28730
	.byte	2,35,4,0,27
	.byte	'Gtm_CmuEclkType',0,1,158,1,2
	.word	44457
	.byte	19
	.word	39397
	.byte	33
	.byte	'Gtm_kNotifConfig0',0,1,176,2,41
	.word	44529
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L64:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,46,1,3,8,32,13,58,15,59,15
	.byte	57,15,73,19,54,15,39,12,0,0,4,5,0,3,8,58,15,59,15,57,15,73,19,0,0,5,11,0,0,0,6,46,1,3,8,32,13,58,15,59
	.byte	15,57,15,54,15,39,12,0,0,7,11,1,0,0,8,19,1,58,15,59,15,57,15,11,15,0,0,9,23,1,58,15,59,15,57,15,11,15
	.byte	0,0,10,13,0,3,8,11,15,73,19,56,9,0,0,11,19,1,3,8,58,15,59,15,57,15,11,15,0,0,12,13,0,3,8,11,15,73,19,13
	.byte	15,12,15,56,9,0,0,13,1,1,11,15,73,19,0,0,14,33,0,47,15,0,0,15,53,0,73,19,0,0,16,15,0,73,19,0,0,17,46,0
	.byte	3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,18,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,19,38,0,73,19,0,0,20,46,1,49,19,0,0,21,5,0,49,19,0,0,22,29,1,49,19,0,0,23,11,0,49,19,0,0,24,11,1,49
	.byte	19,0,0,25,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,26,59,0,3,8,0,0,27,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,28,21,0,54,15,0,0,29,4,1,58,15,59,15,57,15,11,15,0,0,30,40,0,3,8,28,13,0,0,31,21,1,54,15
	.byte	39,12,0,0,32,5,0,73,19,0,0,33,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L65:
	.word	.L388-.L387
.L387:
	.half	3
	.word	.L390-.L389
.L389:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm.c',0,0,0,0
	.byte	'..\\mcal_src\\Gtm.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxGtm_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_WdgLib.h',0,0,0,0
	.byte	'..\\mcal_src\\Gtm_Local.h',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\Std_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0,0
.L390:
.L388:
	.sdecl	'.debug_info',debug,cluster('Gtm_Init')
	.sect	'.debug_info'
.L66:
	.word	1726
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L69,.L68
	.byte	2
	.word	.L62
	.byte	3
	.byte	'Gtm_Init',0,1,236,3,16
	.word	.L98
	.byte	1,1,1
	.word	.L51,.L99,.L50
	.byte	4
	.byte	'ConfigPtr',0,1,236,3,47
	.word	.L100,.L101
	.byte	5
	.word	.L51,.L99
	.byte	6
	.byte	'GeneralConfigPtr',0,1,239,3,32
	.word	.L102,.L103
	.byte	7
	.word	.L104,.L105,.L106
	.byte	8
	.word	.L107,.L108
	.byte	6
	.byte	'GtmEnableStatus',0,1,161,11,18
	.word	.L98,.L109
	.byte	6
	.byte	'GtmClockEnableStatus',0,1,162,11,10
	.word	.L110,.L111
	.byte	9
	.word	.L112,.L113,.L114
	.byte	6
	.byte	'val',0,1,167,11,3
	.word	.L110,.L115
	.byte	0,9
	.word	.L116,.L117,.L2
	.byte	6
	.byte	'ReadBack',0,1,183,11,21
	.word	.L118,.L119
	.byte	9
	.word	.L120,.L121,.L3
	.byte	6
	.byte	'val',0,1,197,11,7
	.word	.L110,.L122
	.byte	0,9
	.word	.L123,.L3,.L124
	.byte	6
	.byte	'val',0,1,201,11,7
	.word	.L110,.L125
	.byte	0,0,0,0,7
	.word	.L126,.L106,.L16
	.byte	9
	.word	.L127,.L106,.L16
	.byte	6
	.byte	'ClockSettingPtr',0,1,142,8,31
	.word	.L128,.L129
	.byte	6
	.byte	'RegTemp',0,1,143,8,10
	.word	.L110,.L130
	.byte	6
	.byte	'RegTemp1',0,1,144,8,10
	.word	.L110,.L131
	.byte	6
	.byte	'RegTemp3',0,1,145,8,10
	.word	.L110,.L132
	.byte	6
	.byte	'RegTemp4',0,1,146,8,10
	.word	.L110,.L133
	.byte	6
	.byte	'Count',0,1,147,8,10
	.word	.L98,.L134
	.byte	6
	.byte	'EnableClock',0,1,148,8,10
	.word	.L98,.L135
	.byte	7
	.word	.L136,.L11,.L137
	.byte	8
	.word	.L138,.L139
	.byte	6
	.byte	'ClockSettingPtr',0,1,146,7,31
	.word	.L128,.L142
	.byte	6
	.byte	'Count',0,1,147,7,10
	.word	.L98,.L143
	.byte	0,0,10
	.word	.L136,.L140,.L141
	.byte	7
	.word	.L144,.L141,.L145
	.byte	9
	.word	.L146,.L141,.L145
	.byte	6
	.byte	'ClockSettingPtr',0,1,199,7,31
	.word	.L128,.L147
	.byte	6
	.byte	'Count',0,1,200,7,10
	.word	.L98,.L148
	.byte	0,0,0,0,11
	.word	.L149
	.byte	6
	.byte	'val',0,1,146,4,5
	.word	.L110,.L154
	.byte	0,7
	.word	.L155,.L156,.L157
	.byte	8
	.word	.L158,.L159
	.byte	6
	.byte	'TomTgcRegPtr',0,1,241,12,25
	.word	.L160,.L161
	.byte	6
	.byte	'FupdBackupValue',0,1,245,12,10
	.word	.L162,.L163
	.byte	6
	.byte	'OutenCtrlBackupValue',0,1,246,12,10
	.word	.L162,.L164
	.byte	6
	.byte	'EndisCtrlBackupValue',0,1,247,12,10
	.word	.L162,.L165
	.byte	6
	.byte	'Count',0,1,248,12,9
	.word	.L98,.L166
	.byte	6
	.byte	'LoopCount',0,1,249,12,9
	.word	.L98,.L167
	.byte	6
	.byte	'MinorCnt',0,1,250,12,9
	.word	.L98,.L168
	.byte	7
	.word	.L169,.L170,.L171
	.byte	12
	.word	.L172,.L173
	.byte	9
	.word	.L174,.L170,.L171
	.byte	6
	.byte	'MinorCnt',0,1,242,8,9
	.word	.L98,.L175
	.byte	9
	.word	.L176,.L177,.L20
	.byte	6
	.byte	'val',0,1,249,8,7
	.word	.L110,.L178
	.byte	0,9
	.word	.L179,.L180,.L21
	.byte	6
	.byte	'val',0,1,129,9,7
	.word	.L110,.L181
	.byte	0,0,0,7
	.word	.L182,.L183,.L184
	.byte	8
	.word	.L185,.L186
	.byte	6
	.byte	'TomConfigPtr',0,1,202,9,31
	.word	.L188,.L189
	.byte	6
	.byte	'TomTgcRegPtr',0,1,203,9,25
	.word	.L160,.L190
	.byte	6
	.byte	'TomCnt',0,1,204,9,9
	.word	.L98,.L191
	.byte	6
	.byte	'MajorCnt',0,1,205,9,9
	.word	.L98,.L192
	.byte	6
	.byte	'MinorCnt',0,1,206,9,9
	.word	.L98,.L193
	.byte	6
	.byte	'ModuleNo',0,1,207,9,9
	.word	.L98,.L194
	.byte	6
	.byte	'TgcNumber',0,1,208,9,9
	.word	.L98,.L195
	.byte	6
	.byte	'MajorCountLoopLimit',0,1,209,9,19
	.word	.L196,.L197
	.byte	0,0,10
	.word	.L182,.L187,.L29
	.byte	9
	.word	.L198,.L199,.L32
	.byte	6
	.byte	'val',0,1,206,13,11
	.word	.L110,.L200
	.byte	0,9
	.word	.L201,.L202,.L33
	.byte	6
	.byte	'val',0,1,216,13,11
	.word	.L110,.L203
	.byte	0,9
	.word	.L204,.L205,.L34
	.byte	6
	.byte	'val',0,1,224,13,11
	.word	.L110,.L206
	.byte	0,0,0,7
	.word	.L207,.L157,.L35
	.byte	9
	.word	.L208,.L157,.L35
	.byte	6
	.byte	'Count',0,1,168,10,9
	.word	.L98,.L209
	.byte	6
	.byte	'GtmToPortToutSelRegCount',0,1,169,10,19
	.word	.L210,.L211
	.byte	0,0,7
	.word	.L212,.L35,.L39
	.byte	9
	.word	.L213,.L35,.L39
	.byte	6
	.byte	'TbuConfigPtr',0,1,207,10,28
	.word	.L214,.L215
	.byte	6
	.byte	'Count',0,1,209,10,9
	.word	.L98,.L216
	.byte	0,0,7
	.word	.L217,.L218,.L219
	.byte	13
	.word	.L220,.L221
	.byte	0,10
	.word	.L217,.L222,.L223
	.byte	10
	.word	.L104,.L5,.L99
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_Init')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,85,6,0,0,9,11,1,49,16
	.byte	17,1,18,1,0,0,10,29,0,49,16,17,1,18,1,0,0,11,11,1,85,6,0,0,12,5,0,49,16,2,6,0,0,13,11,0,49,16,85,6,0,0
	.byte	0
	.sdecl	'.debug_line',debug,cluster('Gtm_Init')
	.sect	'.debug_line'
.L68:
	.word	.L392-.L391
.L391:
	.half	3
	.word	.L394-.L393
.L393:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm.c',0,0,0,0,0
.L394:
	.byte	5,16,7,0,5,2
	.word	.L51
	.byte	3,235,3,1,5,3,9
	.half	.L316-.L51
	.byte	3,6,1,5,18,9
	.half	.L395-.L316
	.byte	1,5,33,9
	.half	.L105-.L395
	.byte	3,175,7,1,5,3,3,3,1,9
	.half	.L113-.L105
	.byte	3,3,1,9
	.half	.L114-.L113
	.byte	3,2,1,5,39,9
	.half	.L396-.L114
	.byte	3,7,1,5,3,9
	.half	.L300-.L396
	.byte	3,5,1,5,4,7,9
	.half	.L117-.L300
	.byte	3,10,1,5,8,9
	.half	.L397-.L117
	.byte	3,2,1,5,22,9
	.half	.L398-.L397
	.byte	1,5,42,9
	.half	.L399-.L398
	.byte	1,5,5,9
	.half	.L400-.L399
	.byte	1,5,7,7,9
	.half	.L121-.L400
	.byte	3,4,1,9
	.half	.L3-.L121
	.byte	3,4,1,5,5,9
	.half	.L124-.L3
	.byte	3,5,1,5,16,9
	.half	.L401-.L124
	.byte	3,3,1,5,14,9
	.half	.L402-.L401
	.byte	1,5,21,9
	.half	.L403-.L402
	.byte	3,6,1,5,5,9
	.half	.L404-.L403
	.byte	3,125,1,5,3,9
	.half	.L2-.L404
	.byte	3,163,120,1,5,21,7,9
	.half	.L106-.L2
	.byte	3,160,4,1,5,12,9
	.half	.L405-.L106
	.byte	3,9,1,5,14,3,127,1,5,15,9
	.half	.L303-.L405
	.byte	3,3,1,5,14,3,125,1,5,21,9
	.half	.L304-.L303
	.byte	3,120,1,5,14,9
	.half	.L406-.L304
	.byte	3,7,1,5,34,9
	.half	.L407-.L406
	.byte	3,6,1,5,14,9
	.half	.L408-.L407
	.byte	3,122,1,5,35,9
	.half	.L305-.L408
	.byte	3,121,1,5,54,9
	.half	.L307-.L305
	.byte	3,13,1,5,14,9
	.half	.L409-.L307
	.byte	3,123,1,5,7,9
	.half	.L308-.L409
	.byte	3,5,1,5,54,7,9
	.half	.L410-.L308
	.byte	3,1,1,5,17,9
	.half	.L306-.L410
	.byte	1,5,5,7,9
	.half	.L6-.L306
	.byte	3,6,1,5,15,3,124,1,5,5,9
	.half	.L411-.L6
	.byte	3,4,1,5,15,3,124,1,5,5,9
	.half	.L310-.L411
	.byte	3,4,1,5,17,9
	.half	.L412-.L310
	.byte	3,2,1,5,5,9
	.half	.L413-.L412
	.byte	3,3,1,9
	.half	.L414-.L413
	.byte	3,2,1,9
	.half	.L415-.L414
	.byte	3,2,1,5,15,9
	.half	.L416-.L415
	.byte	3,5,1,5,57,1,5,47,9
	.half	.L8-.L416
	.byte	3,3,1,5,20,9
	.half	.L417-.L8
	.byte	1,5,43,9
	.half	.L418-.L417
	.byte	1,5,20,9
	.half	.L419-.L418
	.byte	3,127,1,5,7,9
	.half	.L420-.L419
	.byte	1,5,22,7,9
	.half	.L421-.L420
	.byte	3,3,1,5,37,9
	.half	.L422-.L421
	.byte	1,5,18,9
	.half	.L423-.L422
	.byte	1,5,64,9
	.half	.L9-.L423
	.byte	3,123,1,5,57,1,5,3,7,9
	.half	.L7-.L9
	.byte	3,10,1,5,5,7,9
	.half	.L424-.L7
	.byte	3,3,1,5,7,7,9
	.half	.L425-.L424
	.byte	3,3,1,5,35,9
	.half	.L11-.L425
	.byte	3,207,126,1,5,13,9
	.half	.L311-.L11
	.byte	3,4,1,5,15,3,139,1,1,5,7,9
	.half	.L140-.L311
	.byte	3,132,127,1,5,55,9
	.half	.L426-.L140
	.byte	3,113,1,5,50,9
	.half	.L12-.L426
	.byte	3,3,1,5,23,3,127,1,5,27,9
	.half	.L427-.L12
	.byte	3,1,1,5,46,9
	.half	.L428-.L427
	.byte	1,5,40,9
	.half	.L429-.L428
	.byte	3,127,1,5,5,9
	.half	.L430-.L429
	.byte	1,5,7,7,9
	.half	.L431-.L430
	.byte	3,13,1,5,62,9
	.half	.L13-.L431
	.byte	3,113,1,5,55,1,5,35,7,9
	.half	.L141-.L13
	.byte	3,48,1,5,13,9
	.half	.L312-.L141
	.byte	3,3,1,5,59,1,5,68,9
	.half	.L313-.L312
	.byte	1,5,39,9
	.half	.L14-.L313
	.byte	3,3,1,5,23,3,127,1,5,16,9
	.half	.L432-.L14
	.byte	3,1,1,5,69,9
	.half	.L433-.L432
	.byte	1,5,35,9
	.half	.L434-.L433
	.byte	1,5,40,9
	.half	.L435-.L434
	.byte	3,127,1,5,5,9
	.half	.L436-.L435
	.byte	1,5,7,7,9
	.half	.L437-.L436
	.byte	3,15,1,9
	.half	.L438-.L437
	.byte	3,5,1,9
	.half	.L439-.L438
	.byte	3,5,1,5,66,9
	.half	.L15-.L439
	.byte	3,101,1,5,59,1,5,37,7,9
	.half	.L440-.L15
	.byte	3,35,1,5,3,9
	.half	.L441-.L440
	.byte	3,5,1,5,57,9
	.half	.L442-.L441
	.byte	3,123,1,5,3,3,5,1,5,17,9
	.half	.L145-.L442
	.byte	3,222,0,1,5,5,9
	.half	.L443-.L145
	.byte	3,125,1,5,3,9
	.half	.L10-.L443
	.byte	3,5,1,5,5,7,9
	.half	.L444-.L10
	.byte	3,2,1,5,33,9
	.half	.L16-.L444
	.byte	3,166,123,1,5,5,9
	.half	.L314-.L16
	.byte	3,2,1,9
	.half	.L445-.L314
	.byte	3,11,1,5,33,9
	.half	.L446-.L445
	.byte	3,115,1,5,53,9
	.half	.L447-.L446
	.byte	1,5,5,9
	.half	.L315-.L447
	.byte	3,2,1,9
	.half	.L448-.L315
	.byte	3,3,1,9
	.half	.L449-.L448
	.byte	3,4,1,9
	.half	.L450-.L449
	.byte	3,4,1,9
	.half	.L150-.L450
	.byte	3,6,1,9
	.half	.L151-.L150
	.byte	3,122,1,9
	.half	.L152-.L151
	.byte	3,6,1,5,22,9
	.half	.L153-.L152
	.byte	3,4,1,5,18,9
	.half	.L156-.L153
	.byte	3,137,9,1,5,13,9
	.half	.L451-.L156
	.byte	3,99,1,5,54,3,8,1,5,18,9
	.half	.L452-.L451
	.byte	3,21,1,5,22,9
	.half	.L17-.L452
	.byte	3,109,1,5,19,9
	.half	.L453-.L17
	.byte	3,120,1,5,45,3,1,1,5,22,9
	.half	.L318-.L453
	.byte	3,7,1,5,27,9
	.half	.L454-.L318
	.byte	3,2,1,9
	.half	.L455-.L454
	.byte	3,2,1,5,48,9
	.half	.L18-.L455
	.byte	3,122,1,5,29,9
	.half	.L320-.L18
	.byte	3,2,1,5,16,9
	.half	.L456-.L320
	.byte	3,1,1,5,41,9
	.half	.L457-.L456
	.byte	3,127,1,5,34,9
	.half	.L458-.L457
	.byte	3,2,1,5,16,9
	.half	.L459-.L458
	.byte	3,1,1,5,46,9
	.half	.L460-.L459
	.byte	3,127,1,5,34,9
	.half	.L461-.L460
	.byte	3,2,1,5,16,9
	.half	.L462-.L461
	.byte	3,1,1,5,46,9
	.half	.L463-.L462
	.byte	3,127,1,5,7,9
	.half	.L464-.L463
	.byte	3,5,1,5,16,9
	.half	.L170-.L464
	.byte	3,223,123,1,5,54,1,5,30,9
	.half	.L19-.L170
	.byte	3,3,1,5,9,3,127,1,5,6,9
	.half	.L465-.L19
	.byte	3,1,1,5,26,9
	.half	.L466-.L465
	.byte	1,5,67,9
	.half	.L467-.L466
	.byte	3,127,1,5,5,9
	.half	.L468-.L467
	.byte	1,5,7,7,9
	.half	.L177-.L468
	.byte	3,3,1,5,9,9
	.half	.L20-.L177
	.byte	3,5,1,5,67,9
	.half	.L469-.L20
	.byte	1,5,5,9
	.half	.L470-.L469
	.byte	1,5,7,7,9
	.half	.L180-.L470
	.byte	3,3,1,5,64,9
	.half	.L21-.L180
	.byte	3,115,1,5,54,1,5,20,7,9
	.half	.L171-.L21
	.byte	3,166,4,1,5,33,3,13,1,5,46,9
	.half	.L321-.L171
	.byte	3,116,1,5,32,9
	.half	.L22-.L321
	.byte	3,4,1,5,41,9
	.half	.L471-.L22
	.byte	3,1,1,5,38,9
	.half	.L325-.L471
	.byte	1,5,71,9
	.half	.L472-.L325
	.byte	3,127,1,5,70,9
	.half	.L473-.L472
	.byte	3,183,116,1,5,71,9
	.half	.L326-.L473
	.byte	3,201,11,1,5,31,9
	.half	.L474-.L326
	.byte	3,182,116,1,5,44,9
	.half	.L475-.L474
	.byte	1,5,28,9
	.half	.L476-.L475
	.byte	1,5,71,9
	.half	.L477-.L476
	.byte	1,5,10,9
	.half	.L478-.L477
	.byte	1,5,9,9
	.half	.L479-.L478
	.byte	3,205,11,1,5,70,7,9
	.half	.L480-.L479
	.byte	3,6,1,5,33,3,127,1,5,38,9
	.half	.L481-.L480
	.byte	3,2,1,5,42,9
	.half	.L482-.L481
	.byte	3,127,1,5,20,9
	.half	.L483-.L482
	.byte	1,5,45,9
	.half	.L484-.L483
	.byte	3,127,1,5,38,9
	.half	.L485-.L484
	.byte	3,2,1,5,49,9
	.half	.L486-.L485
	.byte	1,5,38,9
	.half	.L487-.L486
	.byte	3,2,1,5,49,9
	.half	.L488-.L487
	.byte	1,5,56,9
	.half	.L23-.L488
	.byte	3,112,1,5,46,1,5,56,7,9
	.half	.L489-.L23
	.byte	3,106,1,5,45,1,5,63,7,9
	.half	.L490-.L489
	.byte	3,125,1,5,56,9
	.half	.L491-.L490
	.byte	1,5,24,7,9
	.half	.L492-.L491
	.byte	3,57,1,5,68,9
	.half	.L183-.L492
	.byte	3,151,124,1,5,39,3,127,1,5,10,9
	.half	.L493-.L183
	.byte	3,3,1,5,41,9
	.half	.L327-.L493
	.byte	3,5,1,5,16,9
	.half	.L494-.L327
	.byte	1,5,60,9
	.half	.L329-.L494
	.byte	1,5,11,7,9
	.half	.L495-.L329
	.byte	3,5,1,5,25,9
	.half	.L496-.L495
	.byte	1,5,18,9
	.half	.L25-.L496
	.byte	3,125,1,5,58,1,5,25,9
	.half	.L26-.L25
	.byte	3,3,1,5,11,9
	.half	.L497-.L26
	.byte	3,1,1,5,23,9
	.half	.L498-.L497
	.byte	1,5,61,9
	.half	.L499-.L498
	.byte	3,127,1,5,72,9
	.half	.L500-.L499
	.byte	1,5,7,9
	.half	.L501-.L500
	.byte	1,5,22,7,9
	.half	.L502-.L501
	.byte	3,4,1,5,51,3,12,1,5,50,9
	.half	.L503-.L502
	.byte	3,116,1,5,61,9
	.half	.L504-.L503
	.byte	1,5,71,9
	.half	.L331-.L504
	.byte	3,12,1,5,22,9
	.half	.L505-.L331
	.byte	3,118,1,5,71,9
	.half	.L332-.L505
	.byte	3,10,1,5,54,9
	.half	.L333-.L332
	.byte	3,6,1,5,63,9
	.half	.L506-.L333
	.byte	1,5,68,9
	.half	.L507-.L506
	.byte	1,5,63,9
	.half	.L508-.L507
	.byte	1,5,9,9
	.half	.L334-.L508
	.byte	3,3,1,5,15,9
	.half	.L509-.L334
	.byte	3,121,1,5,9,9
	.half	.L328-.L509
	.byte	3,9,1,5,15,9
	.half	.L510-.L328
	.byte	3,119,1,5,24,9
	.half	.L335-.L510
	.byte	3,14,1,5,9,9
	.half	.L511-.L335
	.byte	1,5,11,7,9
	.half	.L512-.L511
	.byte	3,2,1,9
	.half	.L513-.L512
	.byte	3,2,1,9
	.half	.L514-.L513
	.byte	3,2,1,9
	.half	.L515-.L514
	.byte	3,2,1,9
	.half	.L516-.L515
	.byte	3,3,1,9
	.half	.L517-.L516
	.byte	3,2,1,5,68,9
	.half	.L27-.L517
	.byte	3,80,1,5,58,1,5,70,7,9
	.half	.L518-.L27
	.byte	3,126,1,5,41,9
	.half	.L330-.L518
	.byte	1,5,60,9
	.half	.L336-.L330
	.byte	1,5,11,7,9
	.half	.L24-.L336
	.byte	3,5,1,5,13,9
	.half	.L184-.L24
	.byte	3,226,3,1,5,11,9
	.half	.L187-.L184
	.byte	3,158,124,1,5,25,9
	.half	.L519-.L187
	.byte	1,5,19,9
	.half	.L29-.L519
	.byte	3,228,3,1,5,45,3,1,1,5,55,9
	.half	.L30-.L29
	.byte	3,4,1,5,33,9
	.half	.L337-.L30
	.byte	3,3,1,5,20,9
	.half	.L520-.L337
	.byte	3,126,1,5,58,1,5,33,9
	.half	.L339-.L520
	.byte	3,2,1,5,40,9
	.half	.L521-.L339
	.byte	1,5,33,9
	.half	.L522-.L521
	.byte	3,10,1,5,28,9
	.half	.L523-.L522
	.byte	3,8,1,5,43,9
	.half	.L31-.L523
	.byte	3,111,1,5,40,3,127,1,5,21,9
	.half	.L524-.L31
	.byte	3,1,1,5,40,9
	.half	.L525-.L524
	.byte	1,5,51,9
	.half	.L526-.L525
	.byte	3,127,1,5,9,9
	.half	.L527-.L526
	.byte	1,5,11,7,9
	.half	.L199-.L527
	.byte	3,4,1,5,40,9
	.half	.L32-.L199
	.byte	3,6,1,5,51,9
	.half	.L528-.L32
	.byte	1,5,9,9
	.half	.L529-.L528
	.byte	1,5,11,7,9
	.half	.L202-.L529
	.byte	3,4,1,5,35,9
	.half	.L33-.L202
	.byte	3,4,1,5,47,9
	.half	.L530-.L33
	.byte	1,5,9,9
	.half	.L531-.L530
	.byte	1,5,11,7,9
	.half	.L205-.L531
	.byte	3,4,1,5,68,9
	.half	.L34-.L205
	.byte	3,104,1,5,58,1,5,56,7,9
	.half	.L532-.L34
	.byte	3,123,1,5,45,1,5,63,7,9
	.half	.L533-.L532
	.byte	3,125,1,5,56,9
	.half	.L534-.L533
	.byte	1,5,46,7,9
	.half	.L157-.L534
	.byte	3,233,124,1,5,44,1,5,33,9
	.half	.L535-.L157
	.byte	3,2,1,5,3,9
	.half	.L338-.L535
	.byte	3,1,1,5,38,7,9
	.half	.L536-.L338
	.byte	3,2,1,5,15,9
	.half	.L537-.L536
	.byte	1,5,63,9
	.half	.L343-.L537
	.byte	1,5,7,7,9
	.half	.L37-.L343
	.byte	3,2,1,5,70,9
	.half	.L538-.L37
	.byte	3,126,1,5,7,3,2,1,5,70,9
	.half	.L344-.L538
	.byte	3,126,1,5,38,9
	.half	.L345-.L344
	.byte	1,5,63,9
	.half	.L539-.L345
	.byte	1,5,15,7,9
	.half	.L36-.L539
	.byte	3,6,1,5,7,3,2,1,5,66,9
	.half	.L540-.L36
	.byte	3,126,1,5,7,9
	.half	.L38-.L540
	.byte	3,2,1,5,73,9
	.half	.L541-.L38
	.byte	3,126,1,5,7,3,2,1,5,66,9
	.half	.L542-.L541
	.byte	3,126,1,5,20,7,9
	.half	.L35-.L542
	.byte	3,32,1,5,40,9
	.half	.L543-.L35
	.byte	1,5,3,9
	.half	.L544-.L543
	.byte	1,5,15,7,9
	.half	.L545-.L544
	.byte	3,6,1,5,58,1,5,9,9
	.half	.L346-.L545
	.byte	3,13,1,5,35,9
	.half	.L40-.L346
	.byte	3,123,1,5,72,9
	.half	.L546-.L40
	.byte	1,5,55,9
	.half	.L547-.L546
	.byte	1,5,72,9
	.half	.L548-.L547
	.byte	1,5,38,9
	.half	.L347-.L548
	.byte	3,2,1,5,7,9
	.half	.L549-.L347
	.byte	1,5,9,7,9
	.half	.L550-.L549
	.byte	3,3,1,3,2,1,9
	.half	.L551-.L550
	.byte	3,126,1,3,2,1,9
	.half	.L552-.L551
	.byte	3,126,1,5,65,9
	.half	.L553-.L552
	.byte	3,9,1,5,9,9
	.half	.L554-.L553
	.byte	3,121,1,9
	.half	.L555-.L554
	.byte	3,3,1,5,52,9
	.half	.L556-.L555
	.byte	3,2,1,5,26,9
	.half	.L557-.L556
	.byte	3,224,118,1,5,62,9
	.half	.L558-.L557
	.byte	1,5,34,9
	.half	.L559-.L558
	.byte	3,162,9,1,5,9,3,1,1,5,65,9
	.half	.L41-.L559
	.byte	3,105,1,5,58,1,5,30,7,9
	.half	.L39-.L41
	.byte	3,201,121,1,5,3,9
	.half	.L218-.L39
	.byte	3,230,6,1,5,5,9
	.half	.L219-.L218
	.byte	3,161,121,1,5,3,9
	.half	.L222-.L219
	.byte	3,223,6,1,5,5,9
	.half	.L223-.L222
	.byte	3,161,121,1,9
	.half	.L560-.L223
	.byte	3,1,1,9
	.half	.L561-.L560
	.byte	3,2,1,5,2,9
	.half	.L5-.L561
	.byte	3,3,1,5,1,3,2,1,7,9
	.half	.L70-.L5
	.byte	0,1,1
.L392:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_Init')
	.sect	'.debug_ranges'
.L69:
	.word	-1,.L51,0,.L70-.L51,0,0
.L108:
	.word	-1,.L51,.L105-.L51,.L106-.L51,.L5-.L51,.L99-.L51,0,0
.L139:
	.word	-1,.L51,.L11-.L51,.L137-.L51,.L140-.L51,.L141-.L51,0,0
.L149:
	.word	-1,.L51,.L150-.L51,.L151-.L51,.L152-.L51,.L153-.L51,0,0
.L159:
	.word	-1,.L51,.L156-.L51,.L157-.L51,-1,.L53,0,.L90-.L53,0,0
.L186:
	.word	-1,.L51,.L183-.L51,.L184-.L51,.L187-.L51,.L29-.L51,0,0
.L221:
	.word	-1,.L51,.L218-.L51,.L219-.L51,.L222-.L51,.L223-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_DeInit')
	.sect	'.debug_info'
.L71:
	.word	413
	.half	3
	.word	.L72
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L74,.L73
	.byte	2
	.word	.L62
	.byte	3
	.byte	'Gtm_DeInit',0,1,204,4,6,1,1,1
	.word	.L55,.L224,.L54
	.byte	4
	.word	.L55,.L224
	.byte	5
	.byte	'ReadBack',0,1,206,4,20
	.word	.L225,.L226
	.byte	6
	.word	.L227,.L228,.L229
	.byte	7
	.word	.L230,.L228,.L229
	.byte	5
	.byte	'KernelResetTimeOutCount',0,1,179,12,10
	.word	.L110,.L231
	.byte	5
	.byte	'ResetReadBack',0,1,180,12,19
	.word	.L232,.L233
	.byte	5
	.byte	'ResetStatus',0,1,181,12,10
	.word	.L110,.L234
	.byte	8
	.word	.L235,.L236
	.byte	5
	.byte	'val',0,1,186,12,3
	.word	.L110,.L241
	.byte	0,0,0,4
	.word	.L242,.L243
	.byte	5
	.byte	'val',0,1,216,4,3
	.word	.L110,.L244
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_DeInit')
	.sect	'.debug_abbrev'
.L72:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,29,1
	.byte	49,16,17,1,18,1,0,0,7,11,1,49,16,17,1,18,1,0,0,8,11,1,49,16,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Gtm_DeInit')
	.sect	'.debug_line'
.L73:
	.word	.L563-.L562
.L562:
	.half	3
	.word	.L565-.L564
.L564:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm.c',0,0,0,0,0
.L565:
	.byte	5,6,7,0,5,2
	.word	.L55
	.byte	3,203,4,1,5,22,9
	.half	.L350-.L55
	.byte	3,4,1,5,34,9
	.half	.L228-.L350
	.byte	3,227,7,1,5,3,3,5,1,9
	.half	.L237-.L228
	.byte	3,2,1,9
	.half	.L238-.L237
	.byte	3,2,1,9
	.half	.L239-.L238
	.byte	3,126,1,5,19,9
	.half	.L240-.L239
	.byte	3,1,1,5,17,9
	.half	.L352-.L240
	.byte	1,5,3,9
	.half	.L566-.L352
	.byte	3,1,1,5,19,9
	.half	.L567-.L566
	.byte	3,1,1,5,17,9
	.half	.L568-.L567
	.byte	1,5,3,9
	.half	.L569-.L568
	.byte	3,2,1,5,30,9
	.half	.L43-.L569
	.byte	3,10,1,5,28,9
	.half	.L570-.L43
	.byte	3,3,1,5,10,9
	.half	.L571-.L570
	.byte	3,1,1,5,65,7,9
	.half	.L572-.L571
	.byte	3,1,1,5,3,7,9
	.half	.L44-.L572
	.byte	3,2,1,9
	.half	.L573-.L44
	.byte	3,3,1,9
	.half	.L574-.L573
	.byte	3,1,1,5,19,9
	.half	.L575-.L574
	.byte	3,4,1,5,17,9
	.half	.L576-.L575
	.byte	1,5,3,9
	.half	.L577-.L576
	.byte	3,1,1,9
	.half	.L229-.L577
	.byte	3,253,119,1,9
	.half	.L242-.L229
	.byte	3,2,1,5,14,9
	.half	.L243-.L242
	.byte	3,3,1,5,12,9
	.half	.L354-.L243
	.byte	1,5,3,9
	.half	.L578-.L354
	.byte	3,2,1,9
	.half	.L579-.L578
	.byte	3,3,1,5,20,9
	.half	.L580-.L579
	.byte	1,5,18,9
	.half	.L581-.L580
	.byte	1,5,3,9
	.half	.L582-.L581
	.byte	3,2,1,5,1,9
	.half	.L583-.L582
	.byte	3,2,1,7,9
	.half	.L75-.L583
	.byte	0,1,1
.L563:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_DeInit')
	.sect	'.debug_ranges'
.L74:
	.word	-1,.L55,0,.L75-.L55,0,0
.L236:
	.word	-1,.L55,.L237-.L55,.L238-.L55,.L239-.L55,.L240-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_IsrTomModule')
	.sect	'.debug_info'
.L76:
	.word	593
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L79,.L78
	.byte	2
	.word	.L62
	.byte	3
	.byte	'Gtm_IsrTomModule',0,1,128,5,6,1,1,1
	.word	.L57,.L245,.L56
	.byte	4
	.byte	'ModuleNo',0,1,128,5,29
	.word	.L98,.L246
	.byte	4
	.byte	'ChannelNumber',0,1,128,5,45
	.word	.L98,.L247
	.byte	5
	.word	.L248
	.byte	6
	.byte	'ChanIndex',0,1,132,5,9
	.word	.L98,.L249
	.byte	6
	.byte	'ModChNum',0,1,133,5,9
	.word	.L98,.L250
	.byte	6
	.byte	'NotifRegVal',0,1,134,5,10
	.word	.L251,.L252
	.byte	6
	.byte	'TomModuleUsage',0,1,136,5,10
	.word	.L110,.L253
	.byte	6
	.byte	'ChannelPtr',0,1,141,5,24
	.word	.L254,.L255
	.byte	6
	.byte	'ModUsageConfigPtr',0,1,143,5,33
	.word	.L256,.L257
	.byte	7
	.word	.L258,.L259,.L260
	.byte	8
	.word	.L261,.L262
	.byte	8
	.word	.L263,.L264
	.byte	9
	.word	.L265,.L266
	.byte	6
	.byte	'RegVal',0,1,221,6,19
	.word	.L271,.L272
	.byte	6
	.byte	'IrqStatus',0,1,222,6,10
	.word	.L110,.L273
	.byte	6
	.byte	'RegPos',0,1,223,6,9
	.word	.L98,.L274
	.byte	0,0,7
	.word	.L258,.L267,.L268
	.byte	8
	.word	.L261,.L262
	.byte	8
	.word	.L263,.L264
	.byte	0,7
	.word	.L258,.L269,.L270
	.byte	8
	.word	.L261,.L262
	.byte	8
	.word	.L263,.L264
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_IsrTomModule')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,85,6,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Gtm_IsrTomModule')
	.sect	'.debug_line'
.L78:
	.word	.L585-.L584
.L584:
	.half	3
	.word	.L587-.L586
.L586:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm.c',0,0,0,0,0
.L587:
	.byte	5,6,7,0,5,2
	.word	.L57
	.byte	3,255,4,1,5,23,9
	.half	.L355-.L57
	.byte	3,18,1,5,68,9
	.half	.L358-.L355
	.byte	3,10,1,5,14,9
	.half	.L259-.L358
	.byte	3,199,1,1,5,68,9
	.half	.L260-.L259
	.byte	3,185,126,1,5,14,9
	.half	.L267-.L260
	.byte	3,199,1,1,5,15,3,16,1,5,43,9
	.half	.L359-.L267
	.byte	3,1,1,5,15,3,127,1,5,67,9
	.half	.L360-.L359
	.byte	1,5,65,9
	.half	.L361-.L360
	.byte	3,113,1,5,12,9
	.half	.L362-.L361
	.byte	3,15,1,5,65,9
	.half	.L588-.L362
	.byte	3,113,1,5,10,3,15,1,5,26,9
	.half	.L363-.L588
	.byte	3,5,1,5,16,9
	.half	.L589-.L363
	.byte	1,5,45,9
	.half	.L590-.L589
	.byte	1,5,23,9
	.half	.L591-.L590
	.byte	1,5,57,9
	.half	.L592-.L591
	.byte	1,5,44,9
	.half	.L268-.L592
	.byte	3,188,126,1,5,43,9
	.half	.L364-.L268
	.byte	1,5,15,9
	.half	.L593-.L364
	.byte	3,108,1,5,37,9
	.half	.L365-.L593
	.byte	3,19,1,5,43,9
	.half	.L366-.L365
	.byte	3,1,1,5,16,9
	.half	.L45-.L366
	.byte	3,115,1,5,28,9
	.half	.L594-.L45
	.byte	3,151,124,1,5,40,9
	.half	.L595-.L594
	.byte	1,5,25,9
	.half	.L269-.L595
	.byte	1,5,7,9
	.half	.L270-.L269
	.byte	3,236,3,1,5,40,7,9
	.half	.L367-.L270
	.byte	3,4,1,5,75,3,127,1,5,67,9
	.half	.L368-.L367
	.byte	3,1,1,5,75,3,127,1,5,39,9
	.half	.L369-.L368
	.byte	3,1,1,5,67,9
	.half	.L596-.L369
	.byte	1,5,49,9
	.half	.L597-.L596
	.byte	1,5,62,9
	.half	.L370-.L597
	.byte	3,10,1,5,54,9
	.half	.L598-.L370
	.byte	3,120,1,5,70,9
	.half	.L599-.L598
	.byte	3,166,124,1,5,23,9
	.half	.L600-.L599
	.byte	3,218,3,1,5,36,9
	.half	.L372-.L600
	.byte	3,1,1,5,34,1,5,44,9
	.half	.L601-.L372
	.byte	3,164,124,1,5,43,3,223,3,1,5,28,9
	.half	.L371-.L601
	.byte	3,161,124,1,5,71,9
	.half	.L602-.L371
	.byte	1,5,53,3,223,3,1,5,71,9
	.half	.L373-.L602
	.byte	3,161,124,1,5,10,9
	.half	.L603-.L373
	.byte	1,5,9,9
	.half	.L604-.L603
	.byte	3,227,3,1,5,21,7,9
	.half	.L605-.L604
	.byte	3,4,1,5,52,9
	.half	.L606-.L605
	.byte	1,5,67,9
	.half	.L374-.L606
	.byte	3,3,1,5,35,9
	.half	.L46-.L374
	.byte	3,22,1,5,1,7,9
	.half	.L607-.L46
	.byte	3,6,1,7,9
	.half	.L80-.L607
	.byte	0,1,1
.L585:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_IsrTomModule')
	.sect	'.debug_ranges'
.L79:
	.word	-1,.L57,0,.L80-.L57,0,0
.L248:
	.word	-1,.L57,0,.L245-.L57,-1,.L59,0,.L95-.L59,0,0
.L266:
	.word	-1,.L57,.L259-.L57,.L260-.L57,.L267-.L57,.L268-.L57,.L269-.L57,.L270-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_IsrTimModule')
	.sect	'.debug_info'
.L81:
	.word	520
	.half	3
	.word	.L82
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L84,.L83
	.byte	2
	.word	.L62
	.byte	3
	.byte	'Gtm_IsrTimModule',0,1,245,5,6,1,1,1
	.word	.L61,.L275,.L60
	.byte	4
	.byte	'ModuleNo',0,1,245,5,29
	.word	.L98,.L276
	.byte	4
	.byte	'ChannelNumber',0,1,245,5,45
	.word	.L98,.L277
	.byte	5
	.word	.L61,.L275
	.byte	6
	.byte	'ModChNum',0,1,251,5,9
	.word	.L98,.L278
	.byte	6
	.byte	'NotifRegVal',0,1,252,5,10
	.word	.L251,.L279
	.byte	6
	.byte	'TimChannelRegPtr',0,1,254,5,24
	.word	.L280,.L281
	.byte	6
	.byte	'ModUsageConfigPtr',0,1,128,6,33
	.word	.L256,.L282
	.byte	7
	.word	.L283,.L284,.L285
	.byte	8
	.word	.L286,.L287
	.byte	8
	.word	.L288,.L289
	.byte	9
	.word	.L290,.L291
	.byte	6
	.byte	'RegVal',0,1,165,15,19
	.word	.L294,.L295
	.byte	6
	.byte	'IrqStatus',0,1,166,15,10
	.word	.L110,.L296
	.byte	6
	.byte	'RegPos',0,1,167,15,9
	.word	.L98,.L297
	.byte	0,0,7
	.word	.L283,.L292,.L293
	.byte	8
	.word	.L286,.L287
	.byte	8
	.word	.L288,.L289
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_IsrTimModule')
	.sect	'.debug_abbrev'
.L82:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,85,6,0,0
	.byte	0
	.sdecl	'.debug_line',debug,cluster('Gtm_IsrTimModule')
	.sect	'.debug_line'
.L83:
	.word	.L609-.L608
.L608:
	.half	3
	.word	.L611-.L610
.L610:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm.c',0,0,0,0,0
.L611:
	.byte	5,6,7,0,5,2
	.word	.L61
	.byte	3,244,5,1,5,15,3,196,9,1,5,14,9
	.half	.L284-.L61
	.byte	3,114,1,5,15,3,14,1,5,14,9
	.half	.L612-.L284
	.byte	3,114,1,5,6,3,202,118,1,5,43,9
	.half	.L376-.L612
	.byte	3,197,9,1,5,6,9
	.half	.L285-.L376
	.byte	3,187,118,1,5,65,9
	.half	.L292-.L285
	.byte	3,183,9,1,5,76,9
	.half	.L613-.L292
	.byte	3,13,1,5,65,9
	.half	.L614-.L613
	.byte	3,115,1,5,12,9
	.half	.L378-.L614
	.byte	3,13,1,5,10,9
	.half	.L615-.L378
	.byte	1,5,26,9
	.half	.L616-.L615
	.byte	3,5,1,5,16,9
	.half	.L617-.L616
	.byte	1,5,49,9
	.half	.L618-.L617
	.byte	1,5,23,9
	.half	.L619-.L618
	.byte	1,5,61,9
	.half	.L620-.L619
	.byte	1,5,3,9
	.half	.L379-.L620
	.byte	3,200,118,1,5,50,7,9
	.half	.L293-.L379
	.byte	3,7,1,5,77,3,127,1,5,49,9
	.half	.L621-.L293
	.byte	3,1,1,5,63,9
	.half	.L622-.L621
	.byte	1,5,62,9
	.half	.L623-.L622
	.byte	1,5,59,9
	.half	.L380-.L623
	.byte	3,3,1,5,19,9
	.half	.L624-.L380
	.byte	1,5,41,9
	.half	.L381-.L624
	.byte	3,1,1,5,39,1,5,24,9
	.half	.L377-.L381
	.byte	3,5,1,5,60,9
	.half	.L382-.L377
	.byte	3,126,1,5,70,9
	.half	.L625-.L382
	.byte	3,194,123,1,5,45,3,196,4,1,5,31,9
	.half	.L384-.L625
	.byte	3,187,123,1,5,60,3,191,4,1,5,44,9
	.half	.L626-.L384
	.byte	3,193,123,1,5,45,3,197,4,1,5,28,9
	.half	.L386-.L626
	.byte	3,187,123,1,5,71,9
	.half	.L627-.L386
	.byte	1,5,10,9
	.half	.L628-.L627
	.byte	1,5,3,9
	.half	.L629-.L628
	.byte	3,202,4,1,5,15,7,9
	.half	.L630-.L629
	.byte	3,5,1,5,46,9
	.half	.L631-.L630
	.byte	1,5,13,9
	.half	.L632-.L631
	.byte	3,2,1,5,54,9
	.half	.L385-.L632
	.byte	1,5,1,9
	.half	.L48-.L385
	.byte	3,29,1,7,9
	.half	.L85-.L48
	.byte	0,1,1
.L609:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_IsrTimModule')
	.sect	'.debug_ranges'
.L84:
	.word	-1,.L61,0,.L85-.L61,0,0
.L291:
	.word	-1,.L61,.L284-.L61,.L285-.L61,.L292-.L61,.L293-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L86:
	.word	207
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L89,.L88
	.byte	2
	.word	.L62
	.byte	3
	.byte	'.cocofun_1',0,1,236,3,16,1
	.word	.L53,.L90,.L52
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L88:
	.word	.L634-.L633
.L633:
	.half	3
	.word	.L636-.L635
.L635:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm.c',0,0,0,0,0
.L636:
	.byte	5,48,7,0,5,2
	.word	.L53
	.byte	3,137,13,1,5,54,9
	.half	.L637-.L53
	.byte	1,5,59,9
	.half	.L638-.L637
	.byte	1,5,54,9
	.half	.L639-.L638
	.byte	1,9
	.half	.L90-.L639
	.byte	0,1,1,5,55,0,5,2
	.word	.L53
	.byte	3,198,13,1,5,61,9
	.half	.L637-.L53
	.byte	1,5,66,9
	.half	.L638-.L637
	.byte	1,5,61,9
	.half	.L639-.L638
	.byte	1,5,54,9
	.half	.L319-.L639
	.byte	3,67,1,7,9
	.half	.L90-.L319
	.byte	0,1,1
.L634:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L89:
	.word	-1,.L53,0,.L90-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L91:
	.word	207
	.half	3
	.word	.L92
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L94,.L93
	.byte	2
	.word	.L62
	.byte	3
	.byte	'.cocofun_2',0,1,128,5,6,1
	.word	.L59,.L95,.L58
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L92:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L93:
	.word	.L641-.L640
.L640:
	.half	3
	.word	.L643-.L642
.L642:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm.c',0,0,0,0,0
.L643:
	.byte	5,23,7,0,5,2
	.word	.L59
	.byte	3,145,5,1,5,37,9
	.half	.L644-.L59
	.byte	1,5,53,9
	.half	.L645-.L644
	.byte	3,1,1,9
	.half	.L95-.L645
	.byte	0,1,1,5,24,0,5,2
	.word	.L59
	.byte	3,149,6,1,5,38,9
	.half	.L644-.L59
	.byte	1,5,56,9
	.half	.L645-.L644
	.byte	3,1,1,5,53,9
	.half	.L357-.L645
	.byte	3,252,126,1,7,9
	.half	.L95-.L357
	.byte	0,1,1
.L641:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L94:
	.word	-1,.L59,0,.L95-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_kConfigPtr')
	.sect	'.debug_info'
.L96:
	.word	201
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L62
	.byte	3
	.byte	'Gtm_kConfigPtr',0,1,156,2,23
	.word	.L100
	.byte	1,5,3
	.word	Gtm_kConfigPtr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_kConfigPtr')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L90-.L53
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L58:
	.word	-1,.L59,0,.L95-.L59
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_DeInit')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L350-.L55
	.half	2
	.byte	138,0
	.word	.L350-.L55,.L224-.L55
	.half	2
	.byte	138,8
	.word	.L224-.L55,.L224-.L55
	.half	2
	.byte	138,0
	.word	0,0
.L231:
	.word	-1,.L55,.L237-.L55,.L224-.L55
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L226:
	.word	-1,.L55,0,.L224-.L55
	.half	2
	.byte	145,120
	.word	0,0
.L233:
	.word	-1,.L55,0,.L224-.L55
	.half	2
	.byte	145,124
	.word	0,0
.L234:
	.word	0,0
.L241:
	.word	-1,.L55,.L351-.L55,.L352-.L55
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L244:
	.word	-1,.L55,.L353-.L55,.L354-.L55
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_Init')
	.sect	'.debug_loc'
.L324:
	.word	-1,.L51,.L325-.L51,.L326-.L51
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L142:
	.word	-1,.L51,.L311-.L51,.L14-.L51
	.half	1
	.byte	111
	.word	0,0
.L147:
	.word	-1,.L51,.L312-.L51,.L10-.L51
	.half	1
	.byte	98
	.word	0,0
.L129:
	.word	-1,.L51,.L307-.L51,.L156-.L51
	.half	1
	.byte	103
	.word	0,0
.L101:
	.word	-1,.L51,0,.L113-.L51
	.half	1
	.byte	100
	.word	.L314-.L51,.L151-.L51
	.half	1
	.byte	111
	.word	.L53-.L51,.L90-.L51
	.half	2
	.byte	145,120
	.word	.L316-.L51,.L30-.L51
	.half	2
	.byte	145,120
	.word	.L337-.L51,.L99-.L51
	.half	2
	.byte	145,120
	.word	.L348-.L51,.L349-.L51
	.half	1
	.byte	111
	.word	0,0
.L209:
	.word	-1,.L51,.L343-.L51,.L344-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	.L345-.L51,.L35-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L134:
	.word	-1,.L51,.L8-.L51,.L7-.L51
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L148:
	.word	-1,.L51,.L313-.L51,.L10-.L51
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L143:
	.word	-1,.L51,.L137-.L51,.L14-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L166:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	5
	.byte	144,33,157,32,32
	.word	.L317-.L51,.L183-.L51
	.half	5
	.byte	144,33,157,32,32
	.word	.L187-.L51,.L30-.L51
	.half	5
	.byte	144,33,157,32,32
	.word	.L337-.L51,.L218-.L51
	.half	5
	.byte	144,33,157,32,32
	.word	0,0
.L216:
	.word	-1,.L51,.L346-.L51,.L39-.L51
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L135:
	.word	-1,.L51,.L304-.L51,.L156-.L51
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L165:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	2
	.byte	145,96
	.word	0,.L30-.L51
	.half	2
	.byte	145,96
	.word	.L337-.L51,.L99-.L51
	.half	2
	.byte	145,96
	.word	0,0
.L163:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	2
	.byte	145,64
	.word	0,.L30-.L51
	.half	2
	.byte	145,64
	.word	.L337-.L51,.L99-.L51
	.half	2
	.byte	145,64
	.word	0,0
.L103:
	.word	-1,.L51,.L315-.L51,.L151-.L51
	.half	1
	.byte	111
	.word	0,0
.L111:
	.word	0,0
.L109:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	5
	.byte	144,36,157,32,0
	.word	.L113-.L51,.L30-.L51
	.half	5
	.byte	144,36,157,32,0
	.word	.L337-.L51,.L99-.L51
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L211:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	2
	.byte	145,64
	.word	0,.L30-.L51
	.half	2
	.byte	145,64
	.word	.L337-.L51,.L99-.L51
	.half	2
	.byte	145,64
	.word	0,0
.L50:
	.word	-1,.L51,0,.L298-.L51
	.half	2
	.byte	138,0
	.word	.L298-.L51,.L99-.L51
	.half	3
	.byte	138,192,0
	.word	.L99-.L51,.L99-.L51
	.half	2
	.byte	138,0
	.word	0,0
.L167:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	5
	.byte	144,34,157,32,0
	.word	.L318-.L51,.L183-.L51
	.half	5
	.byte	144,34,157,32,0
	.word	.L337-.L51,.L218-.L51
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L192:
	.word	-1,.L51,.L329-.L51,.L330-.L51
	.half	5
	.byte	144,33,157,32,32
	.word	.L336-.L51,.L187-.L51
	.half	5
	.byte	144,33,157,32,32
	.word	0,0
.L197:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	2
	.byte	145,112
	.word	0,.L30-.L51
	.half	2
	.byte	145,112
	.word	.L337-.L51,.L99-.L51
	.half	2
	.byte	145,112
	.word	0,0
.L193:
	.word	-1,.L51,.L26-.L51,.L24-.L51
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L168:
	.word	-1,.L51,.L321-.L51,.L183-.L51
	.half	5
	.byte	144,34,157,32,32
	.word	.L339-.L51,.L218-.L51
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L175:
	.word	-1,.L51,.L19-.L51,.L321-.L51
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L194:
	.word	-1,.L51,.L331-.L51,.L27-.L51
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L164:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	2
	.byte	145,80
	.word	0,.L30-.L51
	.half	2
	.byte	145,80
	.word	.L337-.L51,.L99-.L51
	.half	2
	.byte	145,80
	.word	0,0
.L119:
	.word	-1,.L51,.L53-.L51,.L90-.L51
	.half	2
	.byte	145,64
	.word	0,.L30-.L51
	.half	2
	.byte	145,64
	.word	.L337-.L51,.L99-.L51
	.half	2
	.byte	145,64
	.word	0,0
.L130:
	.word	-1,.L51,.L310-.L51,.L7-.L51
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L131:
	.word	-1,.L51,.L303-.L51,.L156-.L51
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L132:
	.word	-1,.L51,.L305-.L51,.L306-.L51
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L133:
	.word	-1,.L51,.L308-.L51,.L309-.L51
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L215:
	.word	-1,.L51,.L347-.L51,.L39-.L51
	.half	1
	.byte	100
	.word	0,0
.L195:
	.word	-1,.L51,.L332-.L51,.L27-.L51
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L191:
	.word	-1,.L51,.L327-.L51,.L328-.L51
	.half	5
	.byte	144,33,157,32,0
	.word	.L335-.L51,.L29-.L51
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L189:
	.word	-1,.L51,.L333-.L51,.L27-.L51
	.half	1
	.byte	111
	.word	0,0
.L161:
	.word	-1,.L51,.L319-.L51,.L90-.L51
	.half	1
	.byte	111
	.word	.L320-.L51,.L22-.L51
	.half	1
	.byte	111
	.word	.L337-.L51,.L338-.L51
	.half	1
	.byte	111
	.word	0,0
.L190:
	.word	-1,.L51,.L334-.L51,.L27-.L51
	.half	1
	.byte	102
	.word	0,0
.L173:
	.word	0,0
.L181:
	.word	-1,.L51,.L323-.L51,.L21-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L115:
	.word	-1,.L51,.L299-.L51,.L300-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L125:
	.word	-1,.L51,.L302-.L51,.L124-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L178:
	.word	-1,.L51,.L322-.L51,.L20-.L51
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L122:
	.word	-1,.L51,.L301-.L51,.L3-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L200:
	.word	-1,.L51,.L340-.L51,.L32-.L51
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L206:
	.word	-1,.L51,.L342-.L51,.L34-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L203:
	.word	-1,.L51,.L341-.L51,.L33-.L51
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L154:
	.word	-1,.L51,.L153-.L51,.L17-.L51
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_IsrTimModule')
	.sect	'.debug_loc'
.L289:
	.word	0,0
.L277:
	.word	-1,.L61,0,.L377-.L61
	.half	5
	.byte	144,34,157,32,32
	.word	.L292-.L61,.L377-.L61
	.half	5
	.byte	144,35,157,32,0
	.word	.L59-.L61,.L95-.L61
	.half	5
	.byte	144,34,157,32,32
	.word	.L59-.L61,.L95-.L61
	.half	5
	.byte	144,35,157,32,0
	.word	.L382-.L61,.L383-.L61
	.half	5
	.byte	144,34,157,32,32
	.word	.L382-.L61,.L275-.L61
	.half	5
	.byte	144,35,157,32,0
	.word	.L48-.L61,.L275-.L61
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L60:
	.word	-1,.L61,0,.L376-.L61
	.half	2
	.byte	138,0
	.word	.L376-.L61,.L49-.L61
	.half	2
	.byte	138,8
	.word	.L49-.L61,.L49-.L61
	.half	2
	.byte	138,0
	.word	.L49-.L61,.L275-.L61
	.half	2
	.byte	138,8
	.word	.L275-.L61,.L275-.L61
	.half	2
	.byte	138,0
	.word	0,0
.L296:
	.word	-1,.L61,.L379-.L61,.L377-.L61
	.half	5
	.byte	144,32,157,32,32
	.word	.L59-.L61,.L95-.L61
	.half	5
	.byte	144,32,157,32,32
	.word	.L382-.L61,.L384-.L61
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L278:
	.word	-1,.L61,.L386-.L61,.L48-.L61
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L282:
	.word	-1,.L61,.L357-.L61,.L95-.L61
	.half	1
	.byte	98
	.word	.L382-.L61,.L384-.L61
	.half	1
	.byte	98
	.word	0,0
.L276:
	.word	-1,.L61,0,.L377-.L61
	.half	5
	.byte	144,34,157,32,0
	.word	.L284-.L61,.L377-.L61
	.half	5
	.byte	144,33,157,32,32
	.word	.L59-.L61,.L95-.L61
	.half	5
	.byte	144,34,157,32,0
	.word	.L59-.L61,.L95-.L61
	.half	5
	.byte	144,33,157,32,32
	.word	.L382-.L61,.L385-.L61
	.half	5
	.byte	144,34,157,32,0
	.word	.L382-.L61,.L275-.L61
	.half	5
	.byte	144,33,157,32,32
	.word	.L383-.L61,.L48-.L61
	.half	5
	.byte	144,34,157,32,32
	.word	.L48-.L61,.L275-.L61
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L287:
	.word	0,0
.L279:
	.word	-1,.L61,.L381-.L61,.L377-.L61
	.half	5
	.byte	144,35,157,32,32
	.word	.L59-.L61,.L95-.L61
	.half	5
	.byte	144,35,157,32,32
	.word	.L382-.L61,.L48-.L61
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
.L297:
	.word	-1,.L61,.L378-.L61,.L377-.L61
	.half	5
	.byte	144,32,157,32,0
	.word	.L59-.L61,.L95-.L61
	.half	5
	.byte	144,32,157,32,0
	.word	.L382-.L61,.L386-.L61
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L295:
	.word	-1,.L61,0,.L377-.L61
	.half	2
	.byte	145,120
	.word	.L59-.L61,.L95-.L61
	.half	2
	.byte	145,120
	.word	.L382-.L61,.L275-.L61
	.half	2
	.byte	145,120
	.word	0,0
.L281:
	.word	-1,.L61,.L380-.L61,.L377-.L61
	.half	1
	.byte	111
	.word	.L59-.L61,.L356-.L61
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_IsrTomModule')
	.sect	'.debug_loc'
.L249:
	.word	-1,.L57,.L365-.L57,.L245-.L57
	.half	5
	.byte	144,37,157,32,32
	.word	0,0
.L264:
	.word	0,0
.L247:
	.word	-1,.L57,.L356-.L57,.L95-.L57
	.half	5
	.byte	144,34,157,32,32
	.word	0,.L45-.L57
	.half	5
	.byte	144,34,157,32,32
	.word	.L361-.L57,.L362-.L57
	.half	5
	.byte	144,36,157,32,32
	.word	.L366-.L57,.L45-.L57
	.half	5
	.byte	144,36,157,32,32
	.word	.L368-.L57,.L369-.L57
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L255:
	.word	-1,.L57,.L370-.L57,.L371-.L57
	.half	1
	.byte	111
	.word	0,0
.L56:
	.word	-1,.L57,0,.L355-.L57
	.half	2
	.byte	138,0
	.word	.L355-.L57,.L245-.L57
	.half	2
	.byte	138,8
	.word	.L245-.L57,.L245-.L57
	.half	2
	.byte	138,0
	.word	0,0
.L273:
	.word	-1,.L57,.L268-.L57,.L245-.L57
	.half	5
	.byte	144,38,157,32,0
	.word	0,0
.L250:
	.word	-1,.L57,.L373-.L57,.L46-.L57
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L257:
	.word	-1,.L57,.L357-.L57,.L95-.L57
	.half	1
	.byte	98
	.word	.L358-.L57,.L45-.L57
	.half	1
	.byte	98
	.word	0,0
.L262:
	.word	0,0
.L246:
	.word	-1,.L57,.L356-.L57,.L95-.L57
	.half	5
	.byte	144,34,157,32,0
	.word	.L357-.L57,.L95-.L57
	.half	5
	.byte	144,36,157,32,0
	.word	.L358-.L57,.L260-.L57
	.half	5
	.byte	144,36,157,32,0
	.word	0,.L45-.L57
	.half	5
	.byte	144,34,157,32,0
	.word	.L359-.L57,.L360-.L57
	.half	5
	.byte	144,36,157,32,0
	.word	.L268-.L57,.L364-.L57
	.half	5
	.byte	144,36,157,32,0
	.word	.L367-.L57,.L368-.L57
	.half	5
	.byte	144,36,157,32,0
	.word	.L374-.L57,.L375-.L57
	.half	5
	.byte	144,36,157,32,0
	.word	.L375-.L57,.L46-.L57
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L252:
	.word	-1,.L57,.L372-.L57,.L46-.L57
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
.L274:
	.word	-1,.L57,.L363-.L57,.L45-.L57
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L272:
	.word	-1,.L57,.L356-.L57,.L95-.L57
	.half	2
	.byte	145,120
	.word	0,.L245-.L57
	.half	2
	.byte	145,120
	.word	0,0
.L253:
	.word	-1,.L57,.L267-.L57,.L245-.L57
	.half	5
	.byte	144,37,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L646:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Gtm_Init')
	.sect	'.debug_frame'
	.word	36
	.word	.L646,.L51,.L99-.L51
	.byte	4
	.word	(.L298-.L51)/2
	.byte	19,192,0,22,26,4,19,138,192,0,4
	.word	(.L99-.L298)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('Gtm_DeInit')
	.sect	'.debug_frame'
	.word	36
	.word	.L646,.L55,.L224-.L55
	.byte	4
	.word	(.L350-.L55)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L224-.L350)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Gtm_IsrTomModule')
	.sect	'.debug_frame'
	.word	36
	.word	.L646,.L57,.L245-.L57
	.byte	4
	.word	(.L355-.L57)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L245-.L355)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Gtm_IsrTimModule')
	.sect	'.debug_frame'
	.word	52
	.word	.L646,.L61,.L275-.L61
	.byte	4
	.word	(.L376-.L61)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L49-.L376)/2
	.byte	19,0,8,26,19,8,22,26,3,19,138,8,4
	.word	(.L275-.L49)/2
	.byte	19,0,8,26,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L647:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L647,.L53,.L90-.L53
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L647,.L59,.L95-.L59
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Gtm.c	  1983    return(IrqStatus);
; ..\mcal_src\Gtm.c	  1984  }
; ..\mcal_src\Gtm.c	  1985  
; ..\mcal_src\Gtm.c	  1986  #endif
; ..\mcal_src\Gtm.c	  1987  /*  for #if (defined(GTM_TIM_MODULE_USED) && (GTM_MCUSAFETY_ENABLE == STD_ON))
; ..\mcal_src\Gtm.c	  1988  */
; ..\mcal_src\Gtm.c	  1989  
; ..\mcal_src\Gtm.c	  1990  #define GTM_STOP_SEC_CODE
; ..\mcal_src\Gtm.c	  1991  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm.c	  1992   allowed only for MemMap.h*/
; ..\mcal_src\Gtm.c	  1993  #include "MemMap.h"
; ..\mcal_src\Gtm.c	  1994  

	; Module end
