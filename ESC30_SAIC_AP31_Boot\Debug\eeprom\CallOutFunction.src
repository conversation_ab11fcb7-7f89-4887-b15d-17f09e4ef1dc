	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc29728a -c99 --dep-file=eeprom\\.CallOutFunction.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\CallOutFunction.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\CallOutFunction.src ..\\eeprom\\CallOutFunction.c"
	.compiler_name		"ctc"
	.name	"CallOutFunction"

	
$TC16X
	
	.sdecl	'.text.CallOutFunction.Mcal_SafeErrorHandler',code,cluster('Mcal_SafeErrorHandler')
	.sect	'.text.CallOutFunction.Mcal_SafeErrorHandler'
	.align	2
	
	.global	Mcal_SafeErrorHandler

; ..\eeprom\CallOutFunction.c	     1  /*
; ..\eeprom\CallOutFunction.c	     2   * CallOutFunction.c
; ..\eeprom\CallOutFunction.c	     3   *
; ..\eeprom\CallOutFunction.c	     4   *  Created on: 2021-1-11
; ..\eeprom\CallOutFunction.c	     5   *      Author: fanghongqing
; ..\eeprom\CallOutFunction.c	     6   */
; ..\eeprom\CallOutFunction.c	     7  
; ..\eeprom\CallOutFunction.c	     8  #include "Mcal_TcLib.h"
; ..\eeprom\CallOutFunction.c	     9  #include "NvM.h"
; ..\eeprom\CallOutFunction.c	    10  
; ..\eeprom\CallOutFunction.c	    11  /*This Function comes from Mcal.c*/
; ..\eeprom\CallOutFunction.c	    12  void Mcal_SafeErrorHandler(uint32 ErrorType)
; Function Mcal_SafeErrorHandler
.L5:
Mcal_SafeErrorHandler:	.type	func

; ..\eeprom\CallOutFunction.c	    13  {
; ..\eeprom\CallOutFunction.c	    14    volatile uint32 TimeOut;
; ..\eeprom\CallOutFunction.c	    15  
; ..\eeprom\CallOutFunction.c	    16    TimeOut = 0U;
	mov	d15,#0
	sub.a	a10,#8
.L39:

; ..\eeprom\CallOutFunction.c	    17    /* User can add the code here */
; ..\eeprom\CallOutFunction.c	    18    UNUSED_PARAMETER(ErrorType)
; ..\eeprom\CallOutFunction.c	    19    /* While loop added for UTP ********** */
; ..\eeprom\CallOutFunction.c	    20    while(TimeOut < 0xFFFFFFFFU)
	mov	d0,#-1
	j	.L2
.L3:

; ..\eeprom\CallOutFunction.c	    21    {
; ..\eeprom\CallOutFunction.c	    22      TimeOut++;
	ld.w	d15,[a10]
.L48:
	add	d15,#1
.L2:
	st.w	[a10],d15
.L49:
	ld.w	d15,[a10]
.L50:
	jlt.u	d15,d0,.L3
.L51:

; ..\eeprom\CallOutFunction.c	    23    }
; ..\eeprom\CallOutFunction.c	    24    /* Control should not reach here. WDG timeout happens before this. */
; ..\eeprom\CallOutFunction.c	    25    __debug();
	debug
.L52:

; ..\eeprom\CallOutFunction.c	    26  
; ..\eeprom\CallOutFunction.c	    27  }
	ret
.L29:
	
__Mcal_SafeErrorHandler_function_end:
	.size	Mcal_SafeErrorHandler,__Mcal_SafeErrorHandler_function_end-Mcal_SafeErrorHandler
.L18:
	; End of function
	
	.sdecl	'.text.CallOutFunction.BswM_NvM_CurrentJobMode',code,cluster('BswM_NvM_CurrentJobMode')
	.sect	'.text.CallOutFunction.BswM_NvM_CurrentJobMode'
	.align	2
	
	.global	BswM_NvM_CurrentJobMode

; ..\eeprom\CallOutFunction.c	    28  
; ..\eeprom\CallOutFunction.c	    29  void BswM_NvM_CurrentJobMode(uint8 ServiceId, NvM_RequestResultType CurrentJobMode)
; Function BswM_NvM_CurrentJobMode
.L7:
BswM_NvM_CurrentJobMode:	.type	func

; ..\eeprom\CallOutFunction.c	    30  {
; ..\eeprom\CallOutFunction.c	    31  
; ..\eeprom\CallOutFunction.c	    32  }
	ret
.L34:
	
__BswM_NvM_CurrentJobMode_function_end:
	.size	BswM_NvM_CurrentJobMode,__BswM_NvM_CurrentJobMode_function_end-BswM_NvM_CurrentJobMode
.L23:
	; End of function
	
	.sdecl	'.text.CallOutFunction.FeeIllegalStateNotification',code,cluster('FeeIllegalStateNotification')
	.sect	'.text.CallOutFunction.FeeIllegalStateNotification'
	.align	2
	
	.global	FeeIllegalStateNotification

; ..\eeprom\CallOutFunction.c	    33  
; ..\eeprom\CallOutFunction.c	    34  void FeeIllegalStateNotification(void)
; Function FeeIllegalStateNotification
.L9:
FeeIllegalStateNotification:	.type	func

; ..\eeprom\CallOutFunction.c	    35  {
; ..\eeprom\CallOutFunction.c	    36  
; ..\eeprom\CallOutFunction.c	    37  }
	ret
.L38:
	
__FeeIllegalStateNotification_function_end:
	.size	FeeIllegalStateNotification,__FeeIllegalStateNotification_function_end-FeeIllegalStateNotification
.L28:
	; End of function
	
	.calls	'Mcal_SafeErrorHandler','',8
	.calls	'BswM_NvM_CurrentJobMode','',0
	.calls	'FeeIllegalStateNotification','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L11:
	.word	2089
	.half	3
	.word	.L12
	.byte	4
.L10:
	.byte	1
	.byte	'..\\eeprom\\CallOutFunction.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L13
	.byte	2
	.byte	'__debug',0,1,1,1,1
.L30:
	.byte	3
	.byte	'unsigned long int',0,4,7
.L32:
	.byte	4
	.word	195
.L35:
	.byte	3
	.byte	'unsigned char',0,1,8,5
	.byte	'void',0,6
	.word	238
	.byte	7
	.byte	'__prof_adm',0,1,1,1
	.word	244
	.byte	8,1,6
	.word	268
	.byte	7
	.byte	'__codeptr',0,1,1,1
	.word	270
	.byte	7
	.byte	'uint8',0,2,90,29
	.word	221
	.byte	3
	.byte	'short int',0,2,5,7
	.byte	'sint16',0,2,91,29
	.word	307
	.byte	3
	.byte	'unsigned short int',0,2,7,7
	.byte	'uint16',0,2,92,29
	.word	335
	.byte	7
	.byte	'uint32',0,2,94,29
	.word	195
	.byte	7
	.byte	'boolean',0,2,105,29
	.word	221
	.byte	3
	.byte	'unsigned long long int',0,8,7,7
	.byte	'uint64',0,2,130,1,30
	.word	403
	.byte	7
	.byte	'PduLengthType',0,3,76,22
	.word	335
	.byte	7
	.byte	'IdtAppCom_EBSBatSOFVol_ASIL',0,4,112,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_PDU03_CRC',0,4,115,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_PDU03_RC',0,4,118,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_PDU04_CRC',0,4,121,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_PDU04_RC',0,4,124,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_HADS_020ms_PDU00_CRC',0,4,136,1,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_HADS_020ms_PDU00_RC',0,4,139,1,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGM_050ms_PDU00_CRC',0,4,148,1,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGM_050ms_PDU00_RC',0,4,151,1,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBSBatDisconSts_ASIL',0,4,141,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve01',0,4,144,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve02',0,4,147,3,16
	.word	335
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve03',0,4,150,3,16
	.word	335
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_FrP03_Reserve04',0,4,153,3,16
	.word	335
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve01',0,4,156,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve02',0,4,159,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve03',0,4,162,3,16
	.word	335
	.byte	7
	.byte	'IdtAppCom_EBS_100ms_FrP04_Reserve04',0,4,165,3,16
	.word	335
	.byte	7
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve01',0,4,180,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve02',0,4,183,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_HADS_020ms_PDU00_Reserve03',0,4,186,3,16
	.word	195
	.byte	7
	.byte	'IdtAppCom_MainPwrFltRsn',0,4,192,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGMDiags',0,4,195,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGMFltRsn',0,4,198,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGMSts',0,4,201,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGMSwSts',0,4,207,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve01',0,4,210,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve02',0,4,213,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve03',0,4,216,3,16
	.word	335
	.byte	7
	.byte	'IdtAppCom_PGM_050ms_PDU00_Reserve04',0,4,219,3,16
	.word	335
	.byte	7
	.byte	'IdtAppCom_PwrSysStsInfoToAutoDrvng',0,4,225,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_PwrSysStsToAutoDrvng',0,4,228,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_RednPwrFltRsn',0,4,231,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_SHWAEPBAppdReq',0,4,234,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_SHWAIndSts',0,4,237,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_SHWASysFltSts',0,4,240,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_SHWASysMsg',0,4,243,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_SHWASysReqHzrdLghtReqSts',0,4,246,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_SHWASysSts',0,4,249,3,15
	.word	221
	.byte	7
	.byte	'IdtAppCom_SHWASysTakeOver',0,4,252,3,15
	.word	221
	.byte	7
	.byte	'NvM_RequestResultType',0,4,207,6,15
	.word	221
	.byte	3
	.byte	'unsigned int',0,4,7,7
	.byte	'Rte_BitType',0,4,230,7,22
	.word	2055
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L12:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,0,3,8,54,15,39,12,63,12,60,12,0,0,3,36,0,3,8,11,15
	.byte	62,15,0,0,4,53,0,73,19,0,0,5,59,0,3,8,0,0,6,15,0,73,19,0,0,7,22,0,3,8,58,15,59,15,57,15,73,19,0,0,8,21
	.byte	0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L13:
	.word	.L41-.L40
.L40:
	.half	3
	.word	.L43-.L42
.L42:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom',0
	.byte	0
	.byte	'..\\eeprom\\CallOutFunction.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'ComStack_Types.h',0,1,0,0
	.byte	'Rte_Type.h',0,2,0,0,0
.L43:
.L41:
	.sdecl	'.debug_info',debug,cluster('Mcal_SafeErrorHandler')
	.sect	'.debug_info'
.L14:
	.word	282
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\eeprom\\CallOutFunction.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L17,.L16
	.byte	2
	.word	.L10
	.byte	3
	.byte	'Mcal_SafeErrorHandler',0,1,12,6,1,1,1
	.word	.L5,.L29,.L4
	.byte	4
	.byte	'ErrorType',0,1,12,35
	.word	.L30,.L31
	.byte	5
	.word	.L5,.L29
	.byte	6
	.byte	'TimeOut',0,1,14,19
	.word	.L32,.L33
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Mcal_SafeErrorHandler')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Mcal_SafeErrorHandler')
	.sect	'.debug_line'
.L16:
	.word	.L45-.L44
.L44:
	.half	3
	.word	.L47-.L46
.L46:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\CallOutFunction.c',0,0,0,0,0
.L47:
	.byte	5,13,7,0,5,2
	.word	.L5
	.byte	3,15,1,5,6,3,124,1,5,19,9
	.half	.L39-.L5
	.byte	3,8,1,5,30,1,5,5,9
	.half	.L3-.L39
	.byte	3,2,1,5,12,9
	.half	.L48-.L3
	.byte	1,5,9,9
	.half	.L49-.L48
	.byte	3,126,1,5,30,9
	.half	.L50-.L49
	.byte	1,5,10,7,9
	.half	.L51-.L50
	.byte	3,5,1,5,1,9
	.half	.L52-.L51
	.byte	3,2,1,7,9
	.half	.L18-.L52
	.byte	0,1,1
.L45:
	.sdecl	'.debug_ranges',debug,cluster('Mcal_SafeErrorHandler')
	.sect	'.debug_ranges'
.L17:
	.word	-1,.L5,0,.L18-.L5,0,0
	.sdecl	'.debug_info',debug,cluster('BswM_NvM_CurrentJobMode')
	.sect	'.debug_info'
.L19:
	.word	290
	.half	3
	.word	.L20
	.byte	4,1
	.byte	'..\\eeprom\\CallOutFunction.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L22,.L21
	.byte	2
	.word	.L10
	.byte	3
	.byte	'BswM_NvM_CurrentJobMode',0,1,29,6,1,1,1
	.word	.L7,.L34,.L6
	.byte	4
	.byte	'ServiceId',0,1,29,36
	.word	.L35,.L36
	.byte	4
	.byte	'CurrentJobMode',0,1,29,69
	.word	.L35,.L37
	.byte	5
	.word	.L7,.L34
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('BswM_NvM_CurrentJobMode')
	.sect	'.debug_abbrev'
.L20:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('BswM_NvM_CurrentJobMode')
	.sect	'.debug_line'
.L21:
	.word	.L54-.L53
.L53:
	.half	3
	.word	.L56-.L55
.L55:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\CallOutFunction.c',0,0,0,0,0
.L56:
	.byte	5,1,7,0,5,2
	.word	.L7
	.byte	3,31,1,7,9
	.half	.L23-.L7
	.byte	0,1,1
.L54:
	.sdecl	'.debug_ranges',debug,cluster('BswM_NvM_CurrentJobMode')
	.sect	'.debug_ranges'
.L22:
	.word	-1,.L7,0,.L23-.L7,0,0
	.sdecl	'.debug_info',debug,cluster('FeeIllegalStateNotification')
	.sect	'.debug_info'
.L24:
	.word	245
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'..\\eeprom\\CallOutFunction.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L27,.L26
	.byte	2
	.word	.L10
	.byte	3
	.byte	'FeeIllegalStateNotification',0,1,34,6,1,1,1
	.word	.L9,.L38,.L8
	.byte	4
	.word	.L9,.L38
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('FeeIllegalStateNotification')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('FeeIllegalStateNotification')
	.sect	'.debug_line'
.L26:
	.word	.L58-.L57
.L57:
	.half	3
	.word	.L60-.L59
.L59:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\eeprom\\CallOutFunction.c',0,0,0,0,0
.L60:
	.byte	5,1,7,0,5,2
	.word	.L9
	.byte	3,36,1,7,9
	.half	.L28-.L9
	.byte	0,1,1
.L58:
	.sdecl	'.debug_ranges',debug,cluster('FeeIllegalStateNotification')
	.sect	'.debug_ranges'
.L27:
	.word	-1,.L9,0,.L28-.L9,0,0
	.sdecl	'.debug_loc',debug,cluster('BswM_NvM_CurrentJobMode')
	.sect	'.debug_loc'
.L6:
	.word	-1,.L7,0,.L34-.L7
	.half	2
	.byte	138,0
	.word	0,0
.L37:
	.word	-1,.L7,0,.L34-.L7
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L36:
	.word	-1,.L7,0,.L34-.L7
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('FeeIllegalStateNotification')
	.sect	'.debug_loc'
.L8:
	.word	-1,.L9,0,.L38-.L9
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Mcal_SafeErrorHandler')
	.sect	'.debug_loc'
.L31:
	.word	-1,.L5,0,.L29-.L5
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L4:
	.word	-1,.L5,0,.L39-.L5
	.half	2
	.byte	138,0
	.word	.L39-.L5,.L29-.L5
	.half	2
	.byte	138,8
	.word	.L29-.L5,.L29-.L5
	.half	2
	.byte	138,0
	.word	0,0
.L33:
	.word	-1,.L5,0,.L29-.L5
	.half	2
	.byte	145,120
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L61:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Mcal_SafeErrorHandler')
	.sect	'.debug_frame'
	.word	48
	.word	.L61,.L5,.L29-.L5
	.byte	8,18,8,19,8,20,8,21,8,22,8,23,4
	.word	(.L39-.L5)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L29-.L39)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('BswM_NvM_CurrentJobMode')
	.sect	'.debug_frame'
	.word	24
	.word	.L61,.L7,.L34-.L7
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('FeeIllegalStateNotification')
	.sect	'.debug_frame'
	.word	24
	.word	.L61,.L9,.L38-.L9
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\eeprom\CallOutFunction.c	    38  

	; Module end
