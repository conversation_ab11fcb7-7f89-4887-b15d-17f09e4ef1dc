	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc4472a -c99 --dep-file=mcal_src\\.Dio.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Dio.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Dio.src ..\\mcal_src\\Dio.c"
	.compiler_name		"ctc"
	.name	"Dio"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_ReadChannelGroup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_ReadChannelGroup

; ..\mcal_src\Dio.c	     1  /******************************************************************************
; ..\mcal_src\Dio.c	     2  **                                                                           **
; ..\mcal_src\Dio.c	     3  ** Copyright (C) Infineon Technologies (2018)                                **
; ..\mcal_src\Dio.c	     4  **                                                                           **
; ..\mcal_src\Dio.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Dio.c	     6  **                                                                           **
; ..\mcal_src\Dio.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Dio.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Dio.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Dio.c	    10  **                                                                           **
; ..\mcal_src\Dio.c	    11  *******************************************************************************
; ..\mcal_src\Dio.c	    12  **                                                                           **
; ..\mcal_src\Dio.c	    13  **  $FILENAME   : Dio.c $                                                    **
; ..\mcal_src\Dio.c	    14  **                                                                           **
; ..\mcal_src\Dio.c	    15  **  $CC VERSION : \main\78 $                                                 **
; ..\mcal_src\Dio.c	    16  **                                                                           **
; ..\mcal_src\Dio.c	    17  **  $DATE       : 2018-07-20 $                                               **
; ..\mcal_src\Dio.c	    18  **                                                                           **
; ..\mcal_src\Dio.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Dio.c	    20  **                                                                           **
; ..\mcal_src\Dio.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Dio.c	    22  **                                                                           **
; ..\mcal_src\Dio.c	    23  **  DESCRIPTION : This contains the functionality for DIO driver             **
; ..\mcal_src\Dio.c	    24  **                                                                           **
; ..\mcal_src\Dio.c	    25  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Dio.c	    26  **                                                                           **
; ..\mcal_src\Dio.c	    27  *******************************************************************************
; ..\mcal_src\Dio.c	    28  **  TRACEABILITY : [cover parentID=DS_AS40X_DIO106,DS_AS_DIO001,
; ..\mcal_src\Dio.c	    29                      DS_AS_DIO005,DS_AS_DIO051_DIO055,DS_NAS_DIO_PR228,
; ..\mcal_src\Dio.c	    30                      DS_NAS_DIO_PR127,SAS_AS4XX_DIO_PR913,DS_MCAL_DIO_0517
; ..\mcal_src\Dio.c	    31                      SAS_AS40X_DIO117_DIO168_DIO169_DIO170,DS_NAS_DIO_PR716,
; ..\mcal_src\Dio.c	    32                      SAS_AS4XX_DIO171,DS_MCAL_DIO_0517,
; ..\mcal_src\Dio.c	    33                      DS_MCAL_DIO_0503_0504_0508,SAS_AS321_DIO117,
; ..\mcal_src\Dio.c	    34                      DS_MCAL_DIO_0509_2_0513_1,SAS_NAS_ALL_PR70,
; ..\mcal_src\Dio.c	    35                      DS_MCAL_DIO_0509_3_0513_2,SAS_NAS_ALL_PR630_PR631,
; ..\mcal_src\Dio.c	    36                      SAS_NAS_ALL_PR1652,DS_MCAL_DIO_0509_5_0513_4,
; ..\mcal_src\Dio.c	    37                      SAS_NAS_ALL_PR748,DS_MCAL_DIO_0509_6_0513_5,
; ..\mcal_src\Dio.c	    38                      SAS_NAS_ALL_PR749,DS_MCAL_DIO_0509_7_0513_7,
; ..\mcal_src\Dio.c	    39                      DS_AS_DIO_PR123,DS_MCAL_DIO_0513_8_0509_8,
; ..\mcal_src\Dio.c	    40                      SAS_AS_DIO194,DS_MCAL_DIO_0509_9_0513_9]                 **
; ..\mcal_src\Dio.c	    41  **                 [/cover]                                                  **
; ..\mcal_src\Dio.c	    42  *******************************************************************************/
; ..\mcal_src\Dio.c	    43  
; ..\mcal_src\Dio.c	    44  /*******************************************************************************
; ..\mcal_src\Dio.c	    45  **                      Includes                                              **
; ..\mcal_src\Dio.c	    46  *******************************************************************************/
; ..\mcal_src\Dio.c	    47  
; ..\mcal_src\Dio.c	    48  /* Own header file, this includes own configuration file also */
; ..\mcal_src\Dio.c	    49  /* DIO117: Inclusion structure */
; ..\mcal_src\Dio.c	    50  #include "Dio.h"
; ..\mcal_src\Dio.c	    51  
; ..\mcal_src\Dio.c	    52  /* Version sepcific header file */
; ..\mcal_src\Dio.c	    53  #include "Dio_Ver.h"
; ..\mcal_src\Dio.c	    54  
; ..\mcal_src\Dio.c	    55  /*******************************************************************************
; ..\mcal_src\Dio.c	    56  **                      Imported Compiler Switch Check                        **
; ..\mcal_src\Dio.c	    57  *******************************************************************************/
; ..\mcal_src\Dio.c	    58  
; ..\mcal_src\Dio.c	    59  /* Version checks */
; ..\mcal_src\Dio.c	    60  
; ..\mcal_src\Dio.c	    61  #ifndef DIO_SW_MAJOR_VERSION
; ..\mcal_src\Dio.c	    62    #error "DIO_SW_MAJOR_VERSION is not defined. "
; ..\mcal_src\Dio.c	    63  #endif
; ..\mcal_src\Dio.c	    64  
; ..\mcal_src\Dio.c	    65  #ifndef DIO_SW_MINOR_VERSION
; ..\mcal_src\Dio.c	    66    #error "DIO_SW_MINOR_VERSION is not defined. "
; ..\mcal_src\Dio.c	    67  #endif
; ..\mcal_src\Dio.c	    68  
; ..\mcal_src\Dio.c	    69  #ifndef DIO_SW_PATCH_VERSION
; ..\mcal_src\Dio.c	    70    #error "DIO_SW_PATCH_VERSION is not defined. "
; ..\mcal_src\Dio.c	    71  #endif
; ..\mcal_src\Dio.c	    72  
; ..\mcal_src\Dio.c	    73  /* Check for Correct inclusion of headers */
; ..\mcal_src\Dio.c	    74  #if ( DIO_SW_MAJOR_VERSION != 3U )
; ..\mcal_src\Dio.c	    75    #error "DIO_SW_MAJOR_VERSION does not match. "
; ..\mcal_src\Dio.c	    76  #endif
; ..\mcal_src\Dio.c	    77  #if ( DIO_SW_MINOR_VERSION != 3U )
; ..\mcal_src\Dio.c	    78    #error "DIO_SW_MINOR_VERSION does not match. "
; ..\mcal_src\Dio.c	    79  #endif
; ..\mcal_src\Dio.c	    80  
; ..\mcal_src\Dio.c	    81  /*******************************************************************************
; ..\mcal_src\Dio.c	    82  **                      Private Macro Definitions                             **
; ..\mcal_src\Dio.c	    83  *******************************************************************************/
; ..\mcal_src\Dio.c	    84  
; ..\mcal_src\Dio.c	    85  #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio.c	    86  
; ..\mcal_src\Dio.c	    87  /* 0th Bit Mask value */
; ..\mcal_src\Dio.c	    88  #define DIO_CHANNEL_BIT_MASK         (0x01U)
; ..\mcal_src\Dio.c	    89  
; ..\mcal_src\Dio.c	    90  /* Port numbers */
; ..\mcal_src\Dio.c	    91  #define DIO_NUMBER_31                (31U)
; ..\mcal_src\Dio.c	    92  #define DIO_NUMBER_32                (32U)
; ..\mcal_src\Dio.c	    93  
; ..\mcal_src\Dio.c	    94  #endif /* DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio.c	    95  
; ..\mcal_src\Dio.c	    96  /* To optimize Dio_lGetPortAdr */
; ..\mcal_src\Dio.c	    97  #define DIO_QUOTIENT_EXTRACT_NUM     (0xCDU)
; ..\mcal_src\Dio.c	    98  #define DIO_QUOTIENT_SHIFT           (11U)
; ..\mcal_src\Dio.c	    99  #define DIO_SHIFT_FOUR               (4U)
; ..\mcal_src\Dio.c	   100  #define DIO_SHIFT_THREE              (3U)
; ..\mcal_src\Dio.c	   101  
; ..\mcal_src\Dio.c	   102  /* This macro is used to reset the Port pins */
; ..\mcal_src\Dio.c	   103  #define DIO_CHANNEL_GROUP_RESET     ((uint32)16)
; ..\mcal_src\Dio.c	   104  
; ..\mcal_src\Dio.c	   105  /*******************************************************************************
; ..\mcal_src\Dio.c	   106  **                   Function like macro definitions                          **
; ..\mcal_src\Dio.c	   107  *******************************************************************************/
; ..\mcal_src\Dio.c	   108  
; ..\mcal_src\Dio.c	   109  /*******************************************************************************
; ..\mcal_src\Dio.c	   110  **                      Private Type Definitions                              **
; ..\mcal_src\Dio.c	   111  *******************************************************************************/
; ..\mcal_src\Dio.c	   112  
; ..\mcal_src\Dio.c	   113  
; ..\mcal_src\Dio.c	   114  
; ..\mcal_src\Dio.c	   115  /*******************************************************************************
; ..\mcal_src\Dio.c	   116  **                      Private Function Declarations                         **
; ..\mcal_src\Dio.c	   117  *******************************************************************************/
; ..\mcal_src\Dio.c	   118  
; ..\mcal_src\Dio.c	   119  #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio.c	   120  
; ..\mcal_src\Dio.c	   121  #define DIO_START_SEC_CODE
; ..\mcal_src\Dio.c	   122  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Dio.c	   123  #include "MemMap.h"
; ..\mcal_src\Dio.c	   124  
; ..\mcal_src\Dio.c	   125  /* INLINE Function to check if the port is
; ..\mcal_src\Dio.c	   126        available or not for the microcontroller*/
; ..\mcal_src\Dio.c	   127  IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable31(uint32 Port);
; ..\mcal_src\Dio.c	   128  
; ..\mcal_src\Dio.c	   129  /* INLINE Function to check if the port is
; ..\mcal_src\Dio.c	   130         available or not for the microcontroller*/
; ..\mcal_src\Dio.c	   131  IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable40(uint32 Port);
; ..\mcal_src\Dio.c	   132  
; ..\mcal_src\Dio.c	   133  /* INLINE Function to check if the port
; ..\mcal_src\Dio.c	   134              is read only or it is writable*/
; ..\mcal_src\Dio.c	   135  IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable(uint32 Port);
; ..\mcal_src\Dio.c	   136  
; ..\mcal_src\Dio.c	   137  /* INLINE Function to check if the
; ..\mcal_src\Dio.c	   138       port is read only or it is writable */
; ..\mcal_src\Dio.c	   139  IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly31(uint32 Port);
; ..\mcal_src\Dio.c	   140  
; ..\mcal_src\Dio.c	   141  /* INLINE Function to check if the
; ..\mcal_src\Dio.c	   142       port is read only or it is writable */
; ..\mcal_src\Dio.c	   143  IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly40(uint32 Port);
; ..\mcal_src\Dio.c	   144  
; ..\mcal_src\Dio.c	   145  /* INLINE Function to check if the port
; ..\mcal_src\Dio.c	   146            is read only or it is writable */
; ..\mcal_src\Dio.c	   147  IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly(uint32 Port);
; ..\mcal_src\Dio.c	   148  
; ..\mcal_src\Dio.c	   149  /* INLINE Function to check if the Pin
; ..\mcal_src\Dio.c	   150            available or not */
; ..\mcal_src\Dio.c	   151  IFX_LOCAL_INLINE uint16 Dio_lIsPinAvailable(uint32 Port, uint8 Pin);
; ..\mcal_src\Dio.c	   152  
; ..\mcal_src\Dio.c	   153  #define DIO_STOP_SEC_CODE
; ..\mcal_src\Dio.c	   154  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Dio.c	   155  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio.c	   156    is allowed only for MemMap.h*/
; ..\mcal_src\Dio.c	   157  #include "MemMap.h"
; ..\mcal_src\Dio.c	   158  
; ..\mcal_src\Dio.c	   159  #endif /* (DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON)*/
; ..\mcal_src\Dio.c	   160  
; ..\mcal_src\Dio.c	   161  /*******************************************************************************
; ..\mcal_src\Dio.c	   162  **                      Global Constant Definitions                           **
; ..\mcal_src\Dio.c	   163  *******************************************************************************/
; ..\mcal_src\Dio.c	   164  
; ..\mcal_src\Dio.c	   165  /*******************************************************************************
; ..\mcal_src\Dio.c	   166  **                      Global Variable Definitions                           **
; ..\mcal_src\Dio.c	   167  *******************************************************************************/
; ..\mcal_src\Dio.c	   168  
; ..\mcal_src\Dio.c	   169  /*******************************************************************************
; ..\mcal_src\Dio.c	   170  **                      Private Constant Definitions                          **
; ..\mcal_src\Dio.c	   171  *******************************************************************************/
; ..\mcal_src\Dio.c	   172  /* The following private definitions are available only if development error
; ..\mcal_src\Dio.c	   173     detection is enabled */
; ..\mcal_src\Dio.c	   174  #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio.c	   175  
; ..\mcal_src\Dio.c	   176    /* Start of memory mapping of 8 bit constant */
; ..\mcal_src\Dio.c	   177    #define DIO_START_SEC_CONST_8BIT
; ..\mcal_src\Dio.c	   178  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio.c	   179    is allowed only for MemMap.h*/
; ..\mcal_src\Dio.c	   180    #include "MemMap.h"
; ..\mcal_src\Dio.c	   181  
; ..\mcal_src\Dio.c	   182    /* Information on the most significant pin for each port */
; ..\mcal_src\Dio.c	   183  /*IFX_MISRA_RULE_08_07_STATUS=Fixing the issue,
; ..\mcal_src\Dio.c	   184    doesn't hv impact on the memory allotment of the variable
; ..\mcal_src\Dio.c	   185    and also makes the code not readable*/
; ..\mcal_src\Dio.c	   186    static const uint8 Dio_kMSPortPin[] =
; ..\mcal_src\Dio.c	   187    {
; ..\mcal_src\Dio.c	   188      DIO_PORT_0_MSPIN,
; ..\mcal_src\Dio.c	   189      DIO_PORT_1_MSPIN,
; ..\mcal_src\Dio.c	   190      DIO_PORT_2_MSPIN,
; ..\mcal_src\Dio.c	   191      DIO_PORT_3_MSPIN,
; ..\mcal_src\Dio.c	   192      DIO_PORT_4_MSPIN,
; ..\mcal_src\Dio.c	   193      DIO_PORT_5_MSPIN,
; ..\mcal_src\Dio.c	   194      DIO_PORT_6_MSPIN,
; ..\mcal_src\Dio.c	   195      DIO_PORT_7_MSPIN,
; ..\mcal_src\Dio.c	   196      DIO_PORT_8_MSPIN,
; ..\mcal_src\Dio.c	   197      DIO_PORT_9_MSPIN,
; ..\mcal_src\Dio.c	   198      DIO_PORT_10_MSPIN,
; ..\mcal_src\Dio.c	   199      DIO_PORT_11_MSPIN,
; ..\mcal_src\Dio.c	   200      DIO_PORT_12_MSPIN,
; ..\mcal_src\Dio.c	   201      DIO_PORT_13_MSPIN,
; ..\mcal_src\Dio.c	   202      DIO_PORT_14_MSPIN,
; ..\mcal_src\Dio.c	   203      DIO_PORT_15_MSPIN,
; ..\mcal_src\Dio.c	   204      DIO_PORT_16_MSPIN,
; ..\mcal_src\Dio.c	   205      DIO_PORT_17_MSPIN,
; ..\mcal_src\Dio.c	   206      DIO_PORT_18_MSPIN,
; ..\mcal_src\Dio.c	   207      DIO_PORT_19_MSPIN,
; ..\mcal_src\Dio.c	   208      DIO_PORT_20_MSPIN,
; ..\mcal_src\Dio.c	   209      DIO_PORT_21_MSPIN,
; ..\mcal_src\Dio.c	   210      DIO_PORT_22_MSPIN,
; ..\mcal_src\Dio.c	   211      DIO_PORT_23_MSPIN,
; ..\mcal_src\Dio.c	   212      DIO_PORT_24_MSPIN,
; ..\mcal_src\Dio.c	   213      DIO_PORT_25_MSPIN,
; ..\mcal_src\Dio.c	   214      DIO_PORT_26_MSPIN,
; ..\mcal_src\Dio.c	   215      DIO_PORT_27_MSPIN,
; ..\mcal_src\Dio.c	   216      DIO_PORT_28_MSPIN,
; ..\mcal_src\Dio.c	   217      DIO_PORT_29_MSPIN,
; ..\mcal_src\Dio.c	   218      DIO_PORT_30_MSPIN,
; ..\mcal_src\Dio.c	   219      DIO_PORT_31_MSPIN,
; ..\mcal_src\Dio.c	   220      DIO_PORT_32_MSPIN,
; ..\mcal_src\Dio.c	   221      DIO_PORT_33_MSPIN,
; ..\mcal_src\Dio.c	   222      DIO_PORT_34_MSPIN,
; ..\mcal_src\Dio.c	   223      DIO_PORT_35_MSPIN,
; ..\mcal_src\Dio.c	   224      DIO_PORT_36_MSPIN,
; ..\mcal_src\Dio.c	   225      DIO_PORT_37_MSPIN,
; ..\mcal_src\Dio.c	   226      DIO_PORT_38_MSPIN,
; ..\mcal_src\Dio.c	   227      DIO_PORT_39_MSPIN,
; ..\mcal_src\Dio.c	   228      DIO_PORT_40_MSPIN,
; ..\mcal_src\Dio.c	   229      DIO_PORT_41_MSPIN
; ..\mcal_src\Dio.c	   230    };
; ..\mcal_src\Dio.c	   231  
; ..\mcal_src\Dio.c	   232    /* End of memory mapping of 8 bit constant */
; ..\mcal_src\Dio.c	   233    #define DIO_STOP_SEC_CONST_8BIT
; ..\mcal_src\Dio.c	   234  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio.c	   235    is allowed only for MemMap.h*/
; ..\mcal_src\Dio.c	   236    #include "MemMap.h"
; ..\mcal_src\Dio.c	   237  
; ..\mcal_src\Dio.c	   238  #endif /* DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio.c	   239  
; ..\mcal_src\Dio.c	   240  /* Start of memory mapping of 16 bit constant */
; ..\mcal_src\Dio.c	   241  #define DIO_START_SEC_CONST_16BIT
; ..\mcal_src\Dio.c	   242  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio.c	   243    is allowed only for MemMap.h*/
; ..\mcal_src\Dio.c	   244  #include "MemMap.h"
; ..\mcal_src\Dio.c	   245  
; ..\mcal_src\Dio.c	   246  /* Mask values for all the ports.
; ..\mcal_src\Dio.c	   247     This constant is used to mask the undefined port pins within the port */
; ..\mcal_src\Dio.c	   248  /*IFX_MISRA_RULE_08_07_STATUS= (occurs with DET OFF)
; ..\mcal_src\Dio.c	   249    Fixing the issue, doesn't have impact on the memory allotment
; ..\mcal_src\Dio.c	   250    of the variable and also makes the code not readable*/
; ..\mcal_src\Dio.c	   251  static const Dio_PortLevelType Dio_kMaskUndefinedPortPins[] =
; ..\mcal_src\Dio.c	   252  {
; ..\mcal_src\Dio.c	   253    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT0,
; ..\mcal_src\Dio.c	   254    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT1,
; ..\mcal_src\Dio.c	   255    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT2,
; ..\mcal_src\Dio.c	   256    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT3,
; ..\mcal_src\Dio.c	   257    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT4,
; ..\mcal_src\Dio.c	   258    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT5,
; ..\mcal_src\Dio.c	   259    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT6,
; ..\mcal_src\Dio.c	   260    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT7,
; ..\mcal_src\Dio.c	   261    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT8,
; ..\mcal_src\Dio.c	   262    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT9,
; ..\mcal_src\Dio.c	   263    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT10,
; ..\mcal_src\Dio.c	   264    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT11,
; ..\mcal_src\Dio.c	   265    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT12,
; ..\mcal_src\Dio.c	   266    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT13,
; ..\mcal_src\Dio.c	   267    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT14,
; ..\mcal_src\Dio.c	   268    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT15,
; ..\mcal_src\Dio.c	   269    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT16,
; ..\mcal_src\Dio.c	   270    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT17,
; ..\mcal_src\Dio.c	   271    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT18,
; ..\mcal_src\Dio.c	   272    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT19,
; ..\mcal_src\Dio.c	   273    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT20,
; ..\mcal_src\Dio.c	   274    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT21,
; ..\mcal_src\Dio.c	   275    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT22,
; ..\mcal_src\Dio.c	   276    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT23,
; ..\mcal_src\Dio.c	   277    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT24,
; ..\mcal_src\Dio.c	   278    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT25,
; ..\mcal_src\Dio.c	   279    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT26,
; ..\mcal_src\Dio.c	   280    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT27,
; ..\mcal_src\Dio.c	   281    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT28,
; ..\mcal_src\Dio.c	   282    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT29,
; ..\mcal_src\Dio.c	   283    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT30,
; ..\mcal_src\Dio.c	   284    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT31,
; ..\mcal_src\Dio.c	   285    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT32,
; ..\mcal_src\Dio.c	   286    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT33,
; ..\mcal_src\Dio.c	   287    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT34,
; ..\mcal_src\Dio.c	   288    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT35,
; ..\mcal_src\Dio.c	   289    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT36,
; ..\mcal_src\Dio.c	   290    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT37,
; ..\mcal_src\Dio.c	   291    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT38,
; ..\mcal_src\Dio.c	   292    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT39,
; ..\mcal_src\Dio.c	   293    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT40,
; ..\mcal_src\Dio.c	   294    (Dio_PortLevelType)DIO_MASK_UNDEF_PINS_PORT41
; ..\mcal_src\Dio.c	   295  };
; ..\mcal_src\Dio.c	   296  
; ..\mcal_src\Dio.c	   297  /* Stop of memory mapping of 16 bit constant */
; ..\mcal_src\Dio.c	   298  #define DIO_STOP_SEC_CONST_16BIT
; ..\mcal_src\Dio.c	   299  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio.c	   300    is allowed only for MemMap.h*/
; ..\mcal_src\Dio.c	   301  #include "MemMap.h"
; ..\mcal_src\Dio.c	   302  
; ..\mcal_src\Dio.c	   303  /*******************************************************************************
; ..\mcal_src\Dio.c	   304  **                      Private Variable Definitions                          **
; ..\mcal_src\Dio.c	   305  *******************************************************************************/
; ..\mcal_src\Dio.c	   306  
; ..\mcal_src\Dio.c	   307  
; ..\mcal_src\Dio.c	   308  /*******************************************************************************
; ..\mcal_src\Dio.c	   309  **                      Private Function Definitions                          **
; ..\mcal_src\Dio.c	   310  *******************************************************************************/
; ..\mcal_src\Dio.c	   311  /* Memory Map of the DIO Code */
; ..\mcal_src\Dio.c	   312  #define DIO_START_SEC_CODE
; ..\mcal_src\Dio.c	   313  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Dio.c	   314  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio.c	   315    is allowed only for MemMap.h*/
; ..\mcal_src\Dio.c	   316  #include "MemMap.h"
; ..\mcal_src\Dio.c	   317  
; ..\mcal_src\Dio.c	   318  /*******************************************************************************
; ..\mcal_src\Dio.c	   319  **                      Global Function Definitions                           **
; ..\mcal_src\Dio.c	   320  *******************************************************************************/
; ..\mcal_src\Dio.c	   321  
; ..\mcal_src\Dio.c	   322  /* DIO001: DIO driver does not provide the initialization interface */
; ..\mcal_src\Dio.c	   323  /* DIO102: It is the users responsibility to initialize the PORT driver before
; ..\mcal_src\Dio.c	   324     using any functionality of the DIO driver */
; ..\mcal_src\Dio.c	   325  /* DIO002: Direction change of pin is supported by the PORT driver */
; ..\mcal_src\Dio.c	   326  /* DIO049: DIO061: The run time configuration for DIO ports and port pins is
; ..\mcal_src\Dio.c	   327     handled by PORT driver */
; ..\mcal_src\Dio.c	   328  /* DIO051: All DIO services writes or read directly the hardware registers
; ..\mcal_src\Dio.c	   329     and no buffering is done */
; ..\mcal_src\Dio.c	   330  /* DIO055: All the services of this driver are synchronous */
; ..\mcal_src\Dio.c	   331  /* DIO101: This driver does not provide any callback notifications */
; ..\mcal_src\Dio.c	   332  
; ..\mcal_src\Dio.c	   333  /* Enable / Disable DET check functions with DIO_DEV_ERROR_DETECT switch */
; ..\mcal_src\Dio.c	   334  #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio.c	   335  /*******************************************************************************
; ..\mcal_src\Dio.c	   336  ** Traceability     : [cover parentID=DS_AS_DIO074,DS_AS40X_DIO175,DS_AS_DIO179]
; ..\mcal_src\Dio.c	   337  **                                                                            **
; ..\mcal_src\Dio.c	   338  ** Syntax           : uint8 Dio_lCheckChannelId                               **
; ..\mcal_src\Dio.c	   339  **                    (                                                       **
; ..\mcal_src\Dio.c	   340  **                      uint8 ApiId,                                          **
; ..\mcal_src\Dio.c	   341  **                      Dio_ChannelType ChannelId                             **
; ..\mcal_src\Dio.c	   342  **                    )                                                       **
; ..\mcal_src\Dio.c	   343  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	   344  **                                                                            **
; ..\mcal_src\Dio.c	   345  ** Service ID       : none                                                    **
; ..\mcal_src\Dio.c	   346  **                                                                            **
; ..\mcal_src\Dio.c	   347  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	   348  **                                                                            **
; ..\mcal_src\Dio.c	   349  ** Reentrancy       : reentrant                                               **
; ..\mcal_src\Dio.c	   350  **                                                                            **
; ..\mcal_src\Dio.c	   351  ** Parameters (in)  : ApiId - Service ID of API that calls this function      **
; ..\mcal_src\Dio.c	   352  **                    ChannelId - that needs to be checked for validity       **
; ..\mcal_src\Dio.c	   353  **                                                                            **
; ..\mcal_src\Dio.c	   354  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio.c	   355  **                                                                            **
; ..\mcal_src\Dio.c	   356  ** Return value     : uint32                                                  **
; ..\mcal_src\Dio.c	   357  **                    DIO_NO_ERROR: if no error is detected                   **
; ..\mcal_src\Dio.c	   358  **                    DIO_ERROR: if DET error is detected                     **
; ..\mcal_src\Dio.c	   359  **                                                                            **
; ..\mcal_src\Dio.c	   360  ** Description      : This function:                                          **
; ..\mcal_src\Dio.c	   361  **  - DIO048: This function checks ChannelId for DET error                    **
; ..\mcal_src\Dio.c	   362  **    DIO_E_PARAM_INVALID_CHANNEL_ID                                          **
; ..\mcal_src\Dio.c	   363  **  - is not for the user of DIO driver                                       **
; ..\mcal_src\Dio.c	   364  **                                                                            **
; ..\mcal_src\Dio.c	   365  *******************************************************************************/
; ..\mcal_src\Dio.c	   366  uint8 Dio_lCheckChannelId(uint8 ApiId, Dio_ChannelType ChannelId)
; ..\mcal_src\Dio.c	   367  {
; ..\mcal_src\Dio.c	   368    uint32       PortNumber;
; ..\mcal_src\Dio.c	   369    uint32       Index;
; ..\mcal_src\Dio.c	   370    uint32       ConfigIndex;
; ..\mcal_src\Dio.c	   371    uint32       PortReadOnly;
; ..\mcal_src\Dio.c	   372    uint32       PortAvailable;
; ..\mcal_src\Dio.c	   373    uint32       PinAvailable;
; ..\mcal_src\Dio.c	   374    uint8        PinNumber;
; ..\mcal_src\Dio.c	   375    uint8        ErrStatus;
; ..\mcal_src\Dio.c	   376  
; ..\mcal_src\Dio.c	   377    ErrStatus = (uint8)DIO_NO_ERROR;
; ..\mcal_src\Dio.c	   378    ConfigIndex = 0U;
; ..\mcal_src\Dio.c	   379  
; ..\mcal_src\Dio.c	   380    /* If the DIO channel ID is greater than the max value of Port pin ID */
; ..\mcal_src\Dio.c	   381    if(ChannelId > DIO_MAX_VALID_PORT_PIN_ID)
; ..\mcal_src\Dio.c	   382    {
; ..\mcal_src\Dio.c	   383      ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   384    }
; ..\mcal_src\Dio.c	   385    else
; ..\mcal_src\Dio.c	   386    {
; ..\mcal_src\Dio.c	   387      /* Derive the port and pin number from symbolic channel ID */
; ..\mcal_src\Dio.c	   388      PortNumber = (uint32)Dio_lGetPortNumber(ChannelId);
; ..\mcal_src\Dio.c	   389      PinNumber  = (uint8)Dio_lGetPinNumber(ChannelId);
; ..\mcal_src\Dio.c	   390  
; ..\mcal_src\Dio.c	   391      PortAvailable = Dio_lIsPortAvailable(PortNumber);
; ..\mcal_src\Dio.c	   392      PinAvailable  = Dio_lIsPinAvailable(PortNumber,PinNumber);
; ..\mcal_src\Dio.c	   393  
; ..\mcal_src\Dio.c	   394      if( (PortAvailable == 0U) || (PinAvailable == 0U) )
; ..\mcal_src\Dio.c	   395      {
; ..\mcal_src\Dio.c	   396         ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   397      }
; ..\mcal_src\Dio.c	   398      else
; ..\mcal_src\Dio.c	   399      {
; ..\mcal_src\Dio.c	   400         for(Index = 0U;Index <= PortNumber;Index++)
; ..\mcal_src\Dio.c	   401         {
; ..\mcal_src\Dio.c	   402           if(Dio_lIsPortAvailable(Index) != 0U)
; ..\mcal_src\Dio.c	   403           {
; ..\mcal_src\Dio.c	   404             ConfigIndex++; /* to identify the Index of configuration*/
; ..\mcal_src\Dio.c	   405           }
; ..\mcal_src\Dio.c	   406         }
; ..\mcal_src\Dio.c	   407           /*decremented since the ConfigIndex value is 1 for Index 0*/
; ..\mcal_src\Dio.c	   408         ConfigIndex--;
; ..\mcal_src\Dio.c	   409         PortReadOnly = Dio_lIsPortReadOnly(PortNumber);
; ..\mcal_src\Dio.c	   410  
; ..\mcal_src\Dio.c	   411         /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Dio.c	   412           due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Dio.c	   413         if(
; ..\mcal_src\Dio.c	   414          Dio_kConfigPtr->Dio_PortChannelConfigPtr[ConfigIndex].Dio_PortIdConfig
; ..\mcal_src\Dio.c	   415                                                     == DIO_PORT_NOT_CONFIGURED)
; ..\mcal_src\Dio.c	   416         {
; ..\mcal_src\Dio.c	   417           ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   418         }
; ..\mcal_src\Dio.c	   419  
; ..\mcal_src\Dio.c	   420         /* Check for the validity of symbolic Channel ID */
; ..\mcal_src\Dio.c	   421         else if(Dio_lCheckAnalogChannel(ApiId,PortReadOnly) == DIO_ERROR)
; ..\mcal_src\Dio.c	   422         {
; ..\mcal_src\Dio.c	   423           ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   424         }
; ..\mcal_src\Dio.c	   425         /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Dio.c	   426           due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Dio.c	   427         else if(((Dio_kConfigPtr->
; ..\mcal_src\Dio.c	   428                   Dio_PortChannelConfigPtr[ConfigIndex].Dio_ChannelConfig) &
; ..\mcal_src\Dio.c	   429                  ((uint32)0x1U << PinNumber)) == 0U
; ..\mcal_src\Dio.c	   430                 )
; ..\mcal_src\Dio.c	   431         {
; ..\mcal_src\Dio.c	   432           ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   433         }
; ..\mcal_src\Dio.c	   434         else
; ..\mcal_src\Dio.c	   435         {
; ..\mcal_src\Dio.c	   436           /*Misra check*/
; ..\mcal_src\Dio.c	   437         }
; ..\mcal_src\Dio.c	   438       }
; ..\mcal_src\Dio.c	   439    }
; ..\mcal_src\Dio.c	   440  
; ..\mcal_src\Dio.c	   441    if(ErrStatus == (uint8)DIO_ERROR)
; ..\mcal_src\Dio.c	   442    {
; ..\mcal_src\Dio.c	   443      #if (DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio.c	   444        /* The channel id passed to the function is invalid. Report error id.
; ..\mcal_src\Dio.c	   445           DIO_E_PARAM_INVALID_CHANNEL_ID to the error hook function */
; ..\mcal_src\Dio.c	   446        Det_ReportError( (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio.c	   447                          DIO_INSTANCE_ID,
; ..\mcal_src\Dio.c	   448                          ApiId,
; ..\mcal_src\Dio.c	   449                          DIO_E_PARAM_INVALID_CHANNEL_ID
; ..\mcal_src\Dio.c	   450                        );
; ..\mcal_src\Dio.c	   451     #endif /*DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio.c	   452  
; ..\mcal_src\Dio.c	   453     #if (DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio.c	   454        SafeMcal_ReportError(
; ..\mcal_src\Dio.c	   455                          (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio.c	   456                           DIO_INSTANCE_ID,
; ..\mcal_src\Dio.c	   457                           ApiId,
; ..\mcal_src\Dio.c	   458                           DIO_E_PARAM_INVALID_CHANNEL_ID
; ..\mcal_src\Dio.c	   459                        );
; ..\mcal_src\Dio.c	   460     #endif /*DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio.c	   461    }
; ..\mcal_src\Dio.c	   462  
; ..\mcal_src\Dio.c	   463    return (ErrStatus);
; ..\mcal_src\Dio.c	   464  }/* Dio_lCheckChannelId */
; ..\mcal_src\Dio.c	   465  
; ..\mcal_src\Dio.c	   466  /*******************************************************************************
; ..\mcal_src\Dio.c	   467  ** Traceability     : [cover parentID=DS_AS_DIO075,DS_AS40X_DIO177,DS_AS_DIO179]
; ..\mcal_src\Dio.c	   468  **                                                                            **
; ..\mcal_src\Dio.c	   469  ** Syntax           : uint8 Dio_lCheckPortId                                  **
; ..\mcal_src\Dio.c	   470  **                    (                                                       **
; ..\mcal_src\Dio.c	   471  **                      uint8 ApiId,                                          **
; ..\mcal_src\Dio.c	   472  **                      Dio_PortType PortId                                   **
; ..\mcal_src\Dio.c	   473  **                    )                                                       **
; ..\mcal_src\Dio.c	   474  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	   475  **                                                                            **
; ..\mcal_src\Dio.c	   476  ** Service ID       : none                                                    **
; ..\mcal_src\Dio.c	   477  **                                                                            **
; ..\mcal_src\Dio.c	   478  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	   479  **                                                                            **
; ..\mcal_src\Dio.c	   480  ** Reentrancy       : reentrant                                               **
; ..\mcal_src\Dio.c	   481  **                                                                            **
; ..\mcal_src\Dio.c	   482  ** Parameters (in)  : ApiId  - Service ID of API that calls this function     **
; ..\mcal_src\Dio.c	   483  **                    PortId - that needs to be checked for validity          **
; ..\mcal_src\Dio.c	   484  **                                                                            **
; ..\mcal_src\Dio.c	   485  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio.c	   486  **                                                                            **
; ..\mcal_src\Dio.c	   487  ** Return value     : uint16                                                  **
; ..\mcal_src\Dio.c	   488  **                    DIO_NO_ERROR: if no error is detected                   **
; ..\mcal_src\Dio.c	   489  **                    DIO_ERROR: if DET error is detected                     **
; ..\mcal_src\Dio.c	   490  **                                                                            **
; ..\mcal_src\Dio.c	   491  ** Description      :                                                         **
; ..\mcal_src\Dio.c	   492  ** - This function checks the validity of the PortId. In case error is        **
; ..\mcal_src\Dio.c	   493  **   detected, the error DIO_E_PARAM_INVALID_PORT_ID is reported              **
; ..\mcal_src\Dio.c	   494  ** - This function is local to the driver module and is not for the user of   **
; ..\mcal_src\Dio.c	   495  **   DIO driver                                                               **
; ..\mcal_src\Dio.c	   496  ** - This function is available only if DET is enabled                        **
; ..\mcal_src\Dio.c	   497  **                                                                            **
; ..\mcal_src\Dio.c	   498  *******************************************************************************/
; ..\mcal_src\Dio.c	   499  uint8 Dio_lCheckPortId (uint8 ApiId, Dio_PortType PortId)
; ..\mcal_src\Dio.c	   500  {
; ..\mcal_src\Dio.c	   501    uint32      Index;
; ..\mcal_src\Dio.c	   502    uint32      ConfigIndex;
; ..\mcal_src\Dio.c	   503    uint32      PortReadOnly;
; ..\mcal_src\Dio.c	   504    uint32      PortAvailable;
; ..\mcal_src\Dio.c	   505    uint8       ErrStatus;
; ..\mcal_src\Dio.c	   506  
; ..\mcal_src\Dio.c	   507    ErrStatus = (uint8)DIO_NO_ERROR;
; ..\mcal_src\Dio.c	   508    ConfigIndex = 0U;
; ..\mcal_src\Dio.c	   509  
; ..\mcal_src\Dio.c	   510    PortAvailable = (uint32)Dio_lIsPortAvailable((uint32)PortId);
; ..\mcal_src\Dio.c	   511  
; ..\mcal_src\Dio.c	   512    /* Check for the validity of symbolic port ID */
; ..\mcal_src\Dio.c	   513    if( (PortAvailable == 0U) )
; ..\mcal_src\Dio.c	   514    {
; ..\mcal_src\Dio.c	   515      ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   516    }
; ..\mcal_src\Dio.c	   517    else
; ..\mcal_src\Dio.c	   518    {
; ..\mcal_src\Dio.c	   519      for(Index = 0U;Index <= PortId;Index++)
; ..\mcal_src\Dio.c	   520      {
; ..\mcal_src\Dio.c	   521        if(Dio_lIsPortAvailable(Index) != 0U)
; ..\mcal_src\Dio.c	   522        {
; ..\mcal_src\Dio.c	   523          ConfigIndex++; /* to identify the Index of configuration*/
; ..\mcal_src\Dio.c	   524        }
; ..\mcal_src\Dio.c	   525      }
; ..\mcal_src\Dio.c	   526  
; ..\mcal_src\Dio.c	   527      ConfigIndex--;/*decremented since the ConfigIndex value is 1 for Index 0*/
; ..\mcal_src\Dio.c	   528  
; ..\mcal_src\Dio.c	   529      PortReadOnly = Dio_lIsPortReadOnly((uint32)PortId);
; ..\mcal_src\Dio.c	   530  
; ..\mcal_src\Dio.c	   531      /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic used
; ..\mcal_src\Dio.c	   532        due to PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Dio.c	   533      if(Dio_kConfigPtr->Dio_PortChannelConfigPtr[ConfigIndex].Dio_PortIdConfig
; ..\mcal_src\Dio.c	   534                                                    == DIO_PORT_NOT_CONFIGURED)
; ..\mcal_src\Dio.c	   535      {
; ..\mcal_src\Dio.c	   536         ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   537      }
; ..\mcal_src\Dio.c	   538      else if(Dio_lCheckAnalogPort(ApiId,PortReadOnly) == DIO_ERROR)
; ..\mcal_src\Dio.c	   539      {
; ..\mcal_src\Dio.c	   540         ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   541      }
; ..\mcal_src\Dio.c	   542      else
; ..\mcal_src\Dio.c	   543      {
; ..\mcal_src\Dio.c	   544        /*Misra check*/
; ..\mcal_src\Dio.c	   545      }
; ..\mcal_src\Dio.c	   546    }
; ..\mcal_src\Dio.c	   547    if(ErrStatus == (uint8)DIO_ERROR)
; ..\mcal_src\Dio.c	   548    {
; ..\mcal_src\Dio.c	   549      #if (DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio.c	   550        /* The port id passed to the function is invalid. Report error id
; ..\mcal_src\Dio.c	   551           DIO_E_PARAM_INVALID_PORT_ID to the error hook function */
; ..\mcal_src\Dio.c	   552        Det_ReportError( (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio.c	   553                          DIO_INSTANCE_ID,
; ..\mcal_src\Dio.c	   554                          ApiId,
; ..\mcal_src\Dio.c	   555                          DIO_E_PARAM_INVALID_PORT_ID
; ..\mcal_src\Dio.c	   556                        );
; ..\mcal_src\Dio.c	   557     #endif /*DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio.c	   558  
; ..\mcal_src\Dio.c	   559     #if (DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio.c	   560        SafeMcal_ReportError(
; ..\mcal_src\Dio.c	   561                          (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio.c	   562                           DIO_INSTANCE_ID,
; ..\mcal_src\Dio.c	   563                           ApiId,
; ..\mcal_src\Dio.c	   564                           DIO_E_PARAM_INVALID_PORT_ID
; ..\mcal_src\Dio.c	   565                        );
; ..\mcal_src\Dio.c	   566     #endif /*DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio.c	   567    }
; ..\mcal_src\Dio.c	   568    return (ErrStatus);
; ..\mcal_src\Dio.c	   569  }/* Dio_lCheckPortId */
; ..\mcal_src\Dio.c	   570  
; ..\mcal_src\Dio.c	   571  /*******************************************************************************
; ..\mcal_src\Dio.c	   572  ** Traceability     : [cover parentID=DS_AS_DIO114,DS_AS40X_DIO178,DS_AS_DIO179]
; ..\mcal_src\Dio.c	   573  **                                                                            **
; ..\mcal_src\Dio.c	   574  ** Syntax           : uint8 Dio_lCheckGroupId                                 **
; ..\mcal_src\Dio.c	   575  **                    (                                                       **
; ..\mcal_src\Dio.c	   576  **                      uint8 ApiId,                                          **
; ..\mcal_src\Dio.c	   577  **                      const Dio_ChannelGroupType *GroupIdPtr                **
; ..\mcal_src\Dio.c	   578  **                    )                                                       **
; ..\mcal_src\Dio.c	   579  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	   580  **                                                                            **
; ..\mcal_src\Dio.c	   581  ** Service ID       : none                                                    **
; ..\mcal_src\Dio.c	   582  **                                                                            **
; ..\mcal_src\Dio.c	   583  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	   584  **                                                                            **
; ..\mcal_src\Dio.c	   585  ** Reentrancy       : reentrant                                               **
; ..\mcal_src\Dio.c	   586  **                                                                            **
; ..\mcal_src\Dio.c	   587  ** Parameters (in)  : ApiId - Service ID of API that calls this function      **
; ..\mcal_src\Dio.c	   588  **                    GroupIdPtr - Pointer to group configuration that needs  **
; ..\mcal_src\Dio.c	   589  **                    to be checked for any DET error                         **
; ..\mcal_src\Dio.c	   590  **                                                                            **
; ..\mcal_src\Dio.c	   591  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio.c	   592  **                                                                            **
; ..\mcal_src\Dio.c	   593  ** Return value     : uint32                                                  **
; ..\mcal_src\Dio.c	   594  **                    DIO_NO_ERROR: if no error is detected                   **
; ..\mcal_src\Dio.c	   595  **                    DIO_ERROR: if DET error is detected                     **
; ..\mcal_src\Dio.c	   596  **                                                                            **
; ..\mcal_src\Dio.c	   597  ** Description      : This function:                                          **
; ..\mcal_src\Dio.c	   598  **  - DIO048: This function checks the GroupIdPtr for DET error               **
; ..\mcal_src\Dio.c	   599  **    DIO_E_PARAM_INVALID_GROUP_ID                                            **
; ..\mcal_src\Dio.c	   600  **                                                                            **
; ..\mcal_src\Dio.c	   601  *******************************************************************************/
; ..\mcal_src\Dio.c	   602  uint8 Dio_lCheckGroupId(uint8 ApiId, const Dio_ChannelGroupType *GroupIdPtr)
; ..\mcal_src\Dio.c	   603  {
; ..\mcal_src\Dio.c	   604    uint32 PortReadOnly;
; ..\mcal_src\Dio.c	   605    uint32 PortAvailable;
; ..\mcal_src\Dio.c	   606    uint8  ErrStatus;
; ..\mcal_src\Dio.c	   607  
; ..\mcal_src\Dio.c	   608    /* Initialise the Error status to DIO_ERROR */
; ..\mcal_src\Dio.c	   609    ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   610  
; ..\mcal_src\Dio.c	   611    /* Check GroupIdPtr for NULL_PTR */
; ..\mcal_src\Dio.c	   612    if (GroupIdPtr != NULL_PTR)
; ..\mcal_src\Dio.c	   613    {
; ..\mcal_src\Dio.c	   614      ErrStatus = Dio_lCheckChGrpValue(GroupIdPtr);
; ..\mcal_src\Dio.c	   615  
; ..\mcal_src\Dio.c	   616      if (ErrStatus != (uint8)DIO_ERROR)
; ..\mcal_src\Dio.c	   617      {
; ..\mcal_src\Dio.c	   618        /*Check if the port is Analog port for Write operation*/
; ..\mcal_src\Dio.c	   619        PortReadOnly = Dio_lIsPortReadOnly((uint32)(GroupIdPtr->port));
; ..\mcal_src\Dio.c	   620        PortAvailable = Dio_lIsPortAvailable((uint32)(GroupIdPtr->port));
; ..\mcal_src\Dio.c	   621  
; ..\mcal_src\Dio.c	   622        if( ((ApiId == DIO_SID_WRITECHANNELGROUP) && (PortReadOnly != 0U))
; ..\mcal_src\Dio.c	   623               || (PortAvailable == 0U)
; ..\mcal_src\Dio.c	   624           )
; ..\mcal_src\Dio.c	   625        {
; ..\mcal_src\Dio.c	   626          /* Error status = Error has occurred */
; ..\mcal_src\Dio.c	   627          ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   628        }
; ..\mcal_src\Dio.c	   629        /* Check if mask = 0 */
; ..\mcal_src\Dio.c	   630        else if( ((GroupIdPtr->mask) == 0U) )
; ..\mcal_src\Dio.c	   631        {
; ..\mcal_src\Dio.c	   632          ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   633        }
; ..\mcal_src\Dio.c	   634        /* Check if mask value is invalid, i.e., mask value contains the
; ..\mcal_src\Dio.c	   635           unimplemented bits */
; ..\mcal_src\Dio.c	   636        else if( ( (GroupIdPtr->mask) &
; ..\mcal_src\Dio.c	   637                   (~((uint32)Dio_kMaskUndefinedPortPins[GroupIdPtr->port]))
; ..\mcal_src\Dio.c	   638                 ) != (Dio_PortLevelType)0U)
; ..\mcal_src\Dio.c	   639        {
; ..\mcal_src\Dio.c	   640          ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   641        }
; ..\mcal_src\Dio.c	   642  
; ..\mcal_src\Dio.c	   643        /* Check if offset is greater than the most significant pin on the port
; ..\mcal_src\Dio.c	   644           Note that existence of the port is checked earlier */
; ..\mcal_src\Dio.c	   645        else if((uint32)(GroupIdPtr->offset) >
; ..\mcal_src\Dio.c	   646                                  (uint32)(Dio_kMSPortPin[GroupIdPtr->port]))
; ..\mcal_src\Dio.c	   647        {
; ..\mcal_src\Dio.c	   648          ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   649        }
; ..\mcal_src\Dio.c	   650        else
; ..\mcal_src\Dio.c	   651        {
; ..\mcal_src\Dio.c	   652          /* No functionality to implement. This is to avoid MISRA violation */
; ..\mcal_src\Dio.c	   653        }
; ..\mcal_src\Dio.c	   654     }
; ..\mcal_src\Dio.c	   655    } /* GroupIdPtr != NULL_PTR */
; ..\mcal_src\Dio.c	   656  
; ..\mcal_src\Dio.c	   657    if (ErrStatus == (uint8)DIO_ERROR)
; ..\mcal_src\Dio.c	   658    {
; ..\mcal_src\Dio.c	   659      #if (DIO_DEV_ERROR_DETECT == STD_ON)
; ..\mcal_src\Dio.c	   660      /* DIO114: Reporting error Invalid Channel Group for wrong
; ..\mcal_src\Dio.c	   661         group configuration ID */
; ..\mcal_src\Dio.c	   662      Det_ReportError(
; ..\mcal_src\Dio.c	   663                     (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio.c	   664                      DIO_INSTANCE_ID,
; ..\mcal_src\Dio.c	   665                      ApiId,
; ..\mcal_src\Dio.c	   666                      DIO_E_PARAM_L_INVALID_GROUP
; ..\mcal_src\Dio.c	   667                      );
; ..\mcal_src\Dio.c	   668      #endif /*DIO_DEV_ERROR_DETECT == STD_ON */
; ..\mcal_src\Dio.c	   669  
; ..\mcal_src\Dio.c	   670      #if (DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio.c	   671      SafeMcal_ReportError(
; ..\mcal_src\Dio.c	   672                      (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio.c	   673                       DIO_INSTANCE_ID,
; ..\mcal_src\Dio.c	   674                       ApiId,
; ..\mcal_src\Dio.c	   675                       DIO_E_PARAM_L_INVALID_GROUP
; ..\mcal_src\Dio.c	   676                       );
; ..\mcal_src\Dio.c	   677      #endif /*DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio.c	   678    }
; ..\mcal_src\Dio.c	   679    return (ErrStatus);
; ..\mcal_src\Dio.c	   680  
; ..\mcal_src\Dio.c	   681  }/* Dio_lCheckGroupId */
; ..\mcal_src\Dio.c	   682  
; ..\mcal_src\Dio.c	   683  #endif /* (DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON )*/
; ..\mcal_src\Dio.c	   684  
; ..\mcal_src\Dio.c	   685  #if (DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio.c	   686  /*******************************************************************************
; ..\mcal_src\Dio.c	   687  **                                                                            **
; ..\mcal_src\Dio.c	   688  ** Syntax           : uint8 Dio_lCheckPortLevel                               **
; ..\mcal_src\Dio.c	   689  **                    (                                                       **
; ..\mcal_src\Dio.c	   690  **                      Dio_PortType PortId,                                  **
; ..\mcal_src\Dio.c	   691  **                      Dio_PortLevelType Level                               **
; ..\mcal_src\Dio.c	   692  **                    )                                                       **
; ..\mcal_src\Dio.c	   693  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	   694  **                                                                            **
; ..\mcal_src\Dio.c	   695  ** Service ID       : none                                                    **
; ..\mcal_src\Dio.c	   696  **                                                                            **
; ..\mcal_src\Dio.c	   697  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	   698  **                                                                            **
; ..\mcal_src\Dio.c	   699  ** Reentrancy       : reentrant                                               **
; ..\mcal_src\Dio.c	   700  **                                                                            **
; ..\mcal_src\Dio.c	   701  ** Parameters (in):  PortId - port id whose level to be checked               **
; ..\mcal_src\Dio.c	   702  **                   Level - port level to be checked                         **
; ..\mcal_src\Dio.c	   703  **                                                                            **
; ..\mcal_src\Dio.c	   704  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio.c	   705  **                                                                            **
; ..\mcal_src\Dio.c	   706  ** Return value     : uint8                                                  **
; ..\mcal_src\Dio.c	   707  **                    DIO_NO_ERROR: if no error is detected                   **
; ..\mcal_src\Dio.c	   708  **                    DIO_ERROR: if DET error is detected                     **
; ..\mcal_src\Dio.c	   709  **                                                                            **
; ..\mcal_src\Dio.c	   710  ** Description      : This function validates the "Level" value               **
; ..\mcal_src\Dio.c	   711  **                                                                            **
; ..\mcal_src\Dio.c	   712  *******************************************************************************/
; ..\mcal_src\Dio.c	   713  uint8 Dio_lCheckPortLevel(Dio_PortType PortId, Dio_PortLevelType Level)
; ..\mcal_src\Dio.c	   714  {
; ..\mcal_src\Dio.c	   715    uint8  ErrStatus;
; ..\mcal_src\Dio.c	   716  
; ..\mcal_src\Dio.c	   717    /* Initialise the Error status to DIO_ERROR */
; ..\mcal_src\Dio.c	   718    ErrStatus = (uint8)DIO_ERROR;
; ..\mcal_src\Dio.c	   719  
; ..\mcal_src\Dio.c	   720    if ((Level & (~((uint32)Dio_kMaskUndefinedPortPins[PortId])))
; ..\mcal_src\Dio.c	   721                                  == (Dio_PortLevelType)0U)
; ..\mcal_src\Dio.c	   722    {
; ..\mcal_src\Dio.c	   723      ErrStatus = (uint8)DIO_NO_ERROR;
; ..\mcal_src\Dio.c	   724    }
; ..\mcal_src\Dio.c	   725    return (ErrStatus);
; ..\mcal_src\Dio.c	   726  }
; ..\mcal_src\Dio.c	   727  #endif /* (DIO_SAFETY_ENABLE == STD_ON )*/
; ..\mcal_src\Dio.c	   728  
; ..\mcal_src\Dio.c	   729  /*******************************************************************************
; ..\mcal_src\Dio.c	   730  ** Traceability     : [cover parentID=DS_AS_DIO137,DS_AS_DIO014_DIO037,
; ..\mcal_src\Dio.c	   731                         DS_AS_DIO092,DS_AS_DIO093,DS_AS_DIO060_6,DS_AS_DIO012,
; ..\mcal_src\Dio.c	   732                         DS_AS_DIO083,DS_AS_DIO118_3]                           **
; ..\mcal_src\Dio.c	   733  **                                                                            **
; ..\mcal_src\Dio.c	   734  ** Syntax           : Dio_PortLevelType Dio_ReadChannelGroup                  **
; ..\mcal_src\Dio.c	   735  **                    (                                                       **
; ..\mcal_src\Dio.c	   736  **                      const Dio_ChannelGroupType *ChannelGroupIdPtr         **
; ..\mcal_src\Dio.c	   737  **                    )                                                       **
; ..\mcal_src\Dio.c	   738  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	   739  **                                                                            **
; ..\mcal_src\Dio.c	   740  ** Service ID       : 0x04                                                    **
; ..\mcal_src\Dio.c	   741  **                                                                            **
; ..\mcal_src\Dio.c	   742  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	   743  **                                                                            **
; ..\mcal_src\Dio.c	   744  ** Reentrancy       : DIO005: reentrant                                       **
; ..\mcal_src\Dio.c	   745  **                                                                            **
; ..\mcal_src\Dio.c	   746  ** Parameters (in)  : ChannelGroupIdPtr -                                     **
; ..\mcal_src\Dio.c	   747                          pointer to channel group configuration                **
; ..\mcal_src\Dio.c	   748  **                                                                            **
; ..\mcal_src\Dio.c	   749  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio.c	   750  **                                                                            **
; ..\mcal_src\Dio.c	   751  ** Return value     : Dio_PortLevelType                                       **
; ..\mcal_src\Dio.c	   752  **                                                                            **
; ..\mcal_src\Dio.c	   753  ** Description      : This function:                                          **
; ..\mcal_src\Dio.c	   754  **  - DIO014: DIO037: returns the level of specified channel group            **
; ..\mcal_src\Dio.c	   755  **                                                                            **
; ..\mcal_src\Dio.c	   756  *******************************************************************************/
; ..\mcal_src\Dio.c	   757  Dio_PortLevelType Dio_ReadChannelGroup
; Function Dio_ReadChannelGroup
.L11:
Dio_ReadChannelGroup:	.type	func
	mov.aa	a15,a4
.L120:

; ..\mcal_src\Dio.c	   758  (
; ..\mcal_src\Dio.c	   759    const Dio_ChannelGroupType *ChannelGroupIdPtr
; ..\mcal_src\Dio.c	   760  )
; ..\mcal_src\Dio.c	   761  {
; ..\mcal_src\Dio.c	   762    Ifx_P             *GetPortAddressPtr;
; ..\mcal_src\Dio.c	   763    Dio_PortLevelType  RetVal;
; ..\mcal_src\Dio.c	   764  
; ..\mcal_src\Dio.c	   765    #if((DIO_SAFETY_ENABLE == STD_ON) || (DIO_DEV_ERROR_DETECT == STD_ON))
; ..\mcal_src\Dio.c	   766  
; ..\mcal_src\Dio.c	   767    /* Return value should be zero for errors*/
; ..\mcal_src\Dio.c	   768    RetVal = (Dio_PortLevelType)STD_LOW;
; ..\mcal_src\Dio.c	   769  
; ..\mcal_src\Dio.c	   770    if(
; ..\mcal_src\Dio.c	   771      Dio_lErrorCheckChannelGroupDet(ChannelGroupIdPtr,DIO_SID_READCHANNELGROUP)
; ..\mcal_src\Dio.c	   772                           == DIO_NO_ERROR
; ..\mcal_src\Dio.c	   773      )
; ..\mcal_src\Dio.c	   774    #endif /*DIO_SAFETY_ENABLE == STD_ON || DIO_DEV_ERROR_DETECT == STD_ON*/
; ..\mcal_src\Dio.c	   775    {
; ..\mcal_src\Dio.c	   776       /* DIO092: The input port value is masked with the configured mask value
; ..\mcal_src\Dio.c	   777       GetPortAddressPtr will hold the port address of Port0 to Port11 or
; ..\mcal_src\Dio.c	   778       Port12 to Port16 depending on the Port number */
; ..\mcal_src\Dio.c	   779       GetPortAddressPtr = Dio_lGetPortAdr(ChannelGroupIdPtr->port);
	ld.bu	d4,[a15]3
	call	Dio_lGetPortAdr
.L119:

; ..\mcal_src\Dio.c	   780       RetVal = (Dio_PortLevelType)(DIO_SFR_RUNTIME_USER_MODE_READ32\ 
	ld.w	d15,[a2]36
.L176:

; ..\mcal_src\Dio.c	   781                    (GetPortAddressPtr->IN.U) & (uint32)ChannelGroupIdPtr->mask);
	extr.u	d0,d15,#0,#16
	ld.hu	d15,[a15]0
.L177:

; ..\mcal_src\Dio.c	   782  
; ..\mcal_src\Dio.c	   783       /* DIO093: shift the level by configured offset value and then return */
; ..\mcal_src\Dio.c	   784       RetVal = (RetVal >> ChannelGroupIdPtr->offset);
	and	d0,d15
	ld.bu	d15,[a15]2
.L178:
	rsub	d15,#0
.L179:

; ..\mcal_src\Dio.c	   785    }
; ..\mcal_src\Dio.c	   786    return (RetVal);
; ..\mcal_src\Dio.c	   787  } /* Dio_ReadChannelGroup */
	sha	d2,d0,d15
	ret
.L101:
	
__Dio_ReadChannelGroup_function_end:
	.size	Dio_ReadChannelGroup,__Dio_ReadChannelGroup_function_end-Dio_ReadChannelGroup
.L56:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_WriteChannelGroup')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_WriteChannelGroup

; ..\mcal_src\Dio.c	   788  
; ..\mcal_src\Dio.c	   789  /*******************************************************************************
; ..\mcal_src\Dio.c	   790  ** Traceability     : [cover parentID=DS_AS_DIO138,DS_AS_DIO008_DIO039,
; ..\mcal_src\Dio.c	   791                         DS_AS_DIO040,DS_AS_DIO090,DS_AS_DIO091,DS_AS_DIO060_7,
; ..\mcal_src\Dio.c	   792                         DS_AS_DIO109,DS_AS_DIO064,DS_AS_DIO119_3]              **
; ..\mcal_src\Dio.c	   793  **                                                                            **
; ..\mcal_src\Dio.c	   794  ** Syntax           : void Dio_WriteChannelGroup                              **
; ..\mcal_src\Dio.c	   795  **                    (                                                       **
; ..\mcal_src\Dio.c	   796  **                      const Dio_ChannelGroupType *ChannelGroupIdPtr,        **
; ..\mcal_src\Dio.c	   797  **                      Dio_PortLevelType Level                               **
; ..\mcal_src\Dio.c	   798  **                    )                                                       **
; ..\mcal_src\Dio.c	   799  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	   800  **                                                                            **
; ..\mcal_src\Dio.c	   801  ** Service ID       : 0x05                                                    **
; ..\mcal_src\Dio.c	   802  **                                                                            **
; ..\mcal_src\Dio.c	   803  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	   804  **                                                                            **
; ..\mcal_src\Dio.c	   805  ** Reentrancy       : DIO005: reentrant                                       **
; ..\mcal_src\Dio.c	   806  **                                                                            **
; ..\mcal_src\Dio.c	   807  ** Parameters (in)  : ChannelGroupIdPtr -                                     **
; ..\mcal_src\Dio.c	   808                          pointer to channel group configuration                **
; ..\mcal_src\Dio.c	   809  **                    Level - Sets level of specified channel group           **
; ..\mcal_src\Dio.c	   810  **                                                                            **
; ..\mcal_src\Dio.c	   811  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio.c	   812  **                                                                            **
; ..\mcal_src\Dio.c	   813  ** Return value     : none                                                    **
; ..\mcal_src\Dio.c	   814  **                                                                            **
; ..\mcal_src\Dio.c	   815  ** Description      : This function:                                          **
; ..\mcal_src\Dio.c	   816  **  - DIO039: sets the level of specified channel group                       **
; ..\mcal_src\Dio.c	   817  **  - DIO008: The value for all the pins the channel group are set at         **
; ..\mcal_src\Dio.c	   818  **    one shot                                                                **
; ..\mcal_src\Dio.c	   819  **  - DIO040: Other pins of the port and pins that are configured as          **
; ..\mcal_src\Dio.c	   820  **    input remains unchanged                                                 **
; ..\mcal_src\Dio.c	   821  **                                                                            **
; ..\mcal_src\Dio.c	   822  *******************************************************************************/
; ..\mcal_src\Dio.c	   823  void Dio_WriteChannelGroup
; Function Dio_WriteChannelGroup
.L13:
Dio_WriteChannelGroup:	.type	func

; ..\mcal_src\Dio.c	   824  (
; ..\mcal_src\Dio.c	   825    const Dio_ChannelGroupType *ChannelGroupIdPtr,
; ..\mcal_src\Dio.c	   826    Dio_PortLevelType Level
; ..\mcal_src\Dio.c	   827  )
; ..\mcal_src\Dio.c	   828  {
; ..\mcal_src\Dio.c	   829    /*
; ..\mcal_src\Dio.c	   830      Note: volatile is used for the variable GetPortAddressPtr.
; ..\mcal_src\Dio.c	   831      The compiler may optimise the function call and the desired Level may not
; ..\mcal_src\Dio.c	   832      be written to the Channel or to the Port.
; ..\mcal_src\Dio.c	   833    */
; ..\mcal_src\Dio.c	   834    volatile Ifx_P *GetPortAddressPtr;
; ..\mcal_src\Dio.c	   835    uint32          PortVal;
; ..\mcal_src\Dio.c	   836    uint32          PortResetVal;
; ..\mcal_src\Dio.c	   837  
; ..\mcal_src\Dio.c	   838    #if((DIO_SAFETY_ENABLE == STD_ON) || (DIO_DEV_ERROR_DETECT == STD_ON))
; ..\mcal_src\Dio.c	   839    if(
; ..\mcal_src\Dio.c	   840     Dio_lErrorCheckChannelGroupDet(ChannelGroupIdPtr,DIO_SID_WRITECHANNELGROUP)
; ..\mcal_src\Dio.c	   841                           == DIO_NO_ERROR )
; ..\mcal_src\Dio.c	   842    #endif /*DIO_SAFETY_ENABLE == STD_ON || DIO_DEV_ERROR_DETECT == STD_ON*/
; ..\mcal_src\Dio.c	   843    {
; ..\mcal_src\Dio.c	   844      /* DIO090: passed Level is masked as per the configuration */
; ..\mcal_src\Dio.c	   845      /* DIO091: Level is shifted by the configured offset */
; ..\mcal_src\Dio.c	   846      /* PortVal is used to set the bits */
; ..\mcal_src\Dio.c	   847      PortVal = (uint32)(((uint32)Level << ChannelGroupIdPtr->offset) & \ 
	ld.bu	d15,[a4]2
.L184:

; ..\mcal_src\Dio.c	   848                                                 ChannelGroupIdPtr->mask);
	ld.hu	d0,[a4]0
.L185:
	sh	d4,d4,d15
.L122:
	and	d4,d0
.L186:

; ..\mcal_src\Dio.c	   849  
; ..\mcal_src\Dio.c	   850      /* PortResetVal is used to reset the bits */
; ..\mcal_src\Dio.c	   851      PortResetVal = ((~PortVal) & ((uint32)ChannelGroupIdPtr->mask));
	mov	d15,#-1
	xor	d15,d4
.L187:
	and	d0,d15
.L188:

; ..\mcal_src\Dio.c	   852  
; ..\mcal_src\Dio.c	   853      PortVal = (PortVal | (PortResetVal << DIO_CHANNEL_GROUP_RESET));
	sh	d15,d0,#16
.L124:

; ..\mcal_src\Dio.c	   854  
; ..\mcal_src\Dio.c	   855      /* GetPortAddressPtr will hold the port address of Port0 to Port11 or
; ..\mcal_src\Dio.c	   856         Port12 to Port16 depending on the Port number */
; ..\mcal_src\Dio.c	   857      GetPortAddressPtr = Dio_lGetPortAdr(ChannelGroupIdPtr->port);
	or	d15,d4
	ld.bu	d4,[a4]3
.L123:
	call	Dio_lGetPortAdr
.L121:

; ..\mcal_src\Dio.c	   858      /* Mapping of code and data to specific memory sections via memory mapping
; ..\mcal_src\Dio.c	   859         file*/
; ..\mcal_src\Dio.c	   860      /* IFX_MISRA_RULE_14_03_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio.c	   861         is allowed only for MemMap.h*/
; ..\mcal_src\Dio.c	   862      DIO_SFR_RUNTIME_USER_MODE_WRITE32((GetPortAddressPtr->OMR.U),PortVal);
	st.w	[a2]4,d15
.L189:

; ..\mcal_src\Dio.c	   863    }
; ..\mcal_src\Dio.c	   864  } /* Dio_WriteChannelGroup */
	ret
.L106:
	
__Dio_WriteChannelGroup_function_end:
	.size	Dio_WriteChannelGroup,__Dio_WriteChannelGroup_function_end-Dio_WriteChannelGroup
.L61:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_ReadChannel')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_ReadChannel

; ..\mcal_src\Dio.c	   865  
; ..\mcal_src\Dio.c	   866  /*******************************************************************************
; ..\mcal_src\Dio.c	   867  ** Traceability     : [cover parentID=DS_AS_DIO133,DS_AS_DIO027,DS_AS_DIO011,
; ..\mcal_src\Dio.c	   868                         DS_AS_DIO060_1,DS_AS_DIO089,DS_AS_DIO012,DS_AS_DIO083,
; ..\mcal_src\Dio.c	   869                         DS_AS_DIO118_1]                                        **
; ..\mcal_src\Dio.c	   870  **                                                                            **
; ..\mcal_src\Dio.c	   871  ** Syntax           : Dio_LevelType Dio_ReadChannel                           **
; ..\mcal_src\Dio.c	   872  **                    (                                                       **
; ..\mcal_src\Dio.c	   873  **                      Dio_ChannelType ChannelId                             **
; ..\mcal_src\Dio.c	   874  **                    )                                                       **
; ..\mcal_src\Dio.c	   875  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	   876  **                                                                            **
; ..\mcal_src\Dio.c	   877  ** Service ID       : 0x00                                                    **
; ..\mcal_src\Dio.c	   878  **                                                                            **
; ..\mcal_src\Dio.c	   879  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	   880  **                                                                            **
; ..\mcal_src\Dio.c	   881  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Dio.c	   882  **                                                                            **
; ..\mcal_src\Dio.c	   883  ** Parameters (in)  : ChannelId - ChannelId whose level to be read            **
; ..\mcal_src\Dio.c	   884  **                                                                            **
; ..\mcal_src\Dio.c	   885  ** Parameters (out) : none                                                    **
; ..\mcal_src\Dio.c	   886  **                                                                            **
; ..\mcal_src\Dio.c	   887  ** Return value     : Dio_LevelType - The function returns value or the level **
; ..\mcal_src\Dio.c	   888  **                  of the specified channel which is of type Dio_LevelType   **
; ..\mcal_src\Dio.c	   889  **                                                                            **
; ..\mcal_src\Dio.c	   890  ** Description      : This function:                                          **
; ..\mcal_src\Dio.c	   891  **      - DIO011: DIO27: returns the level of specified channel               **
; ..\mcal_src\Dio.c	   892  **      - DIO012: The specified channel can be input or output                **
; ..\mcal_src\Dio.c	   893  **      - DIO085: returns the physical level of the channel                   **
; ..\mcal_src\Dio.c	   894  **                                                                            **
; ..\mcal_src\Dio.c	   895  *******************************************************************************/
; ..\mcal_src\Dio.c	   896  Dio_LevelType Dio_ReadChannel(Dio_ChannelType ChannelId)
; Function Dio_ReadChannel
.L15:
Dio_ReadChannel:	.type	func
	mov	d15,d4
.L126:

; ..\mcal_src\Dio.c	   897  {
; ..\mcal_src\Dio.c	   898    uint32        PinPosition;
; ..\mcal_src\Dio.c	   899    Dio_LevelType RetVal;
; ..\mcal_src\Dio.c	   900    Ifx_P *GetPortAddressPtr;
; ..\mcal_src\Dio.c	   901  
; ..\mcal_src\Dio.c	   902    /* Return value should be zero for errors*/
; ..\mcal_src\Dio.c	   903    RetVal = (Dio_LevelType)STD_LOW;
; ..\mcal_src\Dio.c	   904  
; ..\mcal_src\Dio.c	   905  
; ..\mcal_src\Dio.c	   906    #if((DIO_SAFETY_ENABLE == STD_ON) || (DIO_DEV_ERROR_DETECT == STD_ON))
; ..\mcal_src\Dio.c	   907    if(Dio_lErrorCheckChannelDet(ChannelId,DIO_SID_READCHANNEL)
; ..\mcal_src\Dio.c	   908                           == DIO_NO_ERROR)
; ..\mcal_src\Dio.c	   909    #endif /*DIO_SAFETY_ENABLE == STD_ON || DIO_DEV_ERROR_DETECT == STD_ON*/
; ..\mcal_src\Dio.c	   910    {
; ..\mcal_src\Dio.c	   911      /*
; ..\mcal_src\Dio.c	   912        GetPortAddressPtr will hold the port address
; ..\mcal_src\Dio.c	   913      */
; ..\mcal_src\Dio.c	   914      GetPortAddressPtr = Dio_lGetPortAdr(Dio_lGetPortNumber(ChannelId));
	mov	d9,#0
	call	Dio_lGetPortNumber
.L125:
	mov	d4,d2
	call	Dio_lGetPortAdr
.L128:

; ..\mcal_src\Dio.c	   915  
; ..\mcal_src\Dio.c	   916      /* Get the Pin position */
; ..\mcal_src\Dio.c	   917      PinPosition = ((uint32)0x01U << Dio_lGetPinNumber(ChannelId));
	mov	d4,d15
	mov.aa	a15,a2
.L130:
	mov	d8,#1
	call	Dio_lGetPinNumber
.L129:

; ..\mcal_src\Dio.c	   918  
; ..\mcal_src\Dio.c	   919      /* Read the Channel level and decide the return value */
; ..\mcal_src\Dio.c	   920      if ( (PinPosition & (DIO_SFR_RUNTIME_USER_MODE_READ32\ 
	sh	d8,d8,d2
	ld.w	d15,[a15]36
.L127:
	and	d8,d15
.L131:

; ..\mcal_src\Dio.c	   921                          (GetPortAddressPtr->IN.U))) != (Dio_LevelType)STD_LOW )
; ..\mcal_src\Dio.c	   922      {
; ..\mcal_src\Dio.c	   923        RetVal = (Dio_LevelType)STD_HIGH;
; ..\mcal_src\Dio.c	   924      }
; ..\mcal_src\Dio.c	   925    }
; ..\mcal_src\Dio.c	   926    return RetVal;
; ..\mcal_src\Dio.c	   927  }/* Dio_ReadChannel */
	seln	d2,d8,d9,#1
	ret
.L80:
	
__Dio_ReadChannel_function_end:
	.size	Dio_ReadChannel,__Dio_ReadChannel_function_end-Dio_ReadChannel
.L36:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_WriteChannel')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_WriteChannel

; ..\mcal_src\Dio.c	   928  
; ..\mcal_src\Dio.c	   929  /*******************************************************************************
; ..\mcal_src\Dio.c	   930  ** Traceability   : [cover parentID=DS_AS_DIO134,DS_AS_DIO006,DS_AS_DIO028,
; ..\mcal_src\Dio.c	   931                       DS_AS_DIO029_DIO070,DS_AS_DIO079,DS_AS_DIO060_2,
; ..\mcal_src\Dio.c	   932                       DS_AS_DIO109,DS_AS_DIO089,DS_AS_DIO064,DS_AS_DIO119_1]   **
; ..\mcal_src\Dio.c	   933  **                                                                            **
; ..\mcal_src\Dio.c	   934  ** Syntax : void Dio_WriteChannel                                             **
; ..\mcal_src\Dio.c	   935  **          (                                                                 **
; ..\mcal_src\Dio.c	   936  **             Dio_ChannelType ChannelId,                                     **
; ..\mcal_src\Dio.c	   937  **             Dio_LevelType Level                                            **
; ..\mcal_src\Dio.c	   938  **          )                                                                 **
; ..\mcal_src\Dio.c	   939  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	   940  **                                                                            **
; ..\mcal_src\Dio.c	   941  ** Service ID:  1                                                             **
; ..\mcal_src\Dio.c	   942  **                                                                            **
; ..\mcal_src\Dio.c	   943  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Dio.c	   944  **                                                                            **
; ..\mcal_src\Dio.c	   945  ** Reentrancy:  DIO005: DIO060: Reentrant                                     **
; ..\mcal_src\Dio.c	   946  **                                                                            **
; ..\mcal_src\Dio.c	   947  ** Parameters (in):  ChannelId - ChannelId whose level to be set              **
; ..\mcal_src\Dio.c	   948  **                   Level - Channel level to be set                          **
; ..\mcal_src\Dio.c	   949  **                                                                            **
; ..\mcal_src\Dio.c	   950  ** Parameters (out):  none                                                    **
; ..\mcal_src\Dio.c	   951  **                                                                            **
; ..\mcal_src\Dio.c	   952  ** Return value:  none                                                        **
; ..\mcal_src\Dio.c	   953  **                                                                            **
; ..\mcal_src\Dio.c	   954  ** Description : This function:                                               **
; ..\mcal_src\Dio.c	   955  **      - DIO006: DIO028: DIO064: sets the specified level for specified      **
; ..\mcal_src\Dio.c	   956  **        channel                                                             **
; ..\mcal_src\Dio.c	   957  **      - DIO023: Possible levels for channel are STD_HIGH and STD_LOW        **
; ..\mcal_src\Dio.c	   958  **      - DIO029: DIO070: DIO079: The real physical level of pin is not       **
; ..\mcal_src\Dio.c	   959  **        modified if the specified channel is configured as input            **
; ..\mcal_src\Dio.c	   960  **      - DIO109: For input channel this function writes into the output      **
; ..\mcal_src\Dio.c	   961  **        register, so that pin level can be set immediately when direction   **
; ..\mcal_src\Dio.c	   962  **        changed by PORT driver                                              **
; ..\mcal_src\Dio.c	   963  **                                                                            **
; ..\mcal_src\Dio.c	   964  *******************************************************************************/
; ..\mcal_src\Dio.c	   965  void Dio_WriteChannel(Dio_ChannelType ChannelId, Dio_LevelType Level)
; Function Dio_WriteChannel
.L17:
Dio_WriteChannel:	.type	func
	mov	d8,d4
.L133:

; ..\mcal_src\Dio.c	   966  {
; ..\mcal_src\Dio.c	   967    /*
; ..\mcal_src\Dio.c	   968      Note: volatile is used for the variable GetPortAddressPtr.
; ..\mcal_src\Dio.c	   969      The compiler may optimise the function call and the desired Level may not
; ..\mcal_src\Dio.c	   970      be written to the Channel or to the Port.
; ..\mcal_src\Dio.c	   971    */
; ..\mcal_src\Dio.c	   972    volatile Ifx_P *GetPortAddressPtr;
; ..\mcal_src\Dio.c	   973    uint32 OmrVal;
; ..\mcal_src\Dio.c	   974  
; ..\mcal_src\Dio.c	   975    OmrVal = DIO_OMR_RESET_BIT;
	movh	d15,#1
.L134:

; ..\mcal_src\Dio.c	   976  
; ..\mcal_src\Dio.c	   977    #if((DIO_SAFETY_ENABLE == STD_ON) || (DIO_DEV_ERROR_DETECT == STD_ON))
; ..\mcal_src\Dio.c	   978    if(Dio_lErrorCheckChannelDet(ChannelId,DIO_SID_WRITECHANNEL)
; ..\mcal_src\Dio.c	   979                           == DIO_NO_ERROR)
; ..\mcal_src\Dio.c	   980    #endif /*DIO_SAFETY_ENABLE == STD_ON || DIO_DEV_ERROR_DETECT == STD_ON*/
; ..\mcal_src\Dio.c	   981    {
; ..\mcal_src\Dio.c	   982      #if(DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio.c	   983      if( (Level == (Dio_LevelType)STD_LOW) ||
; ..\mcal_src\Dio.c	   984          (Level == (Dio_LevelType)STD_HIGH)   )
; ..\mcal_src\Dio.c	   985      #endif /* (DIO_SAFETY_ENABLE == ON) */
; ..\mcal_src\Dio.c	   986      {
; ..\mcal_src\Dio.c	   987        /* Decide the value to be written depending
; ..\mcal_src\Dio.c	   988            on the input parameter Level */
; ..\mcal_src\Dio.c	   989        if (Level != (Dio_LevelType)STD_LOW)
; ..\mcal_src\Dio.c	   990        {
; ..\mcal_src\Dio.c	   991          OmrVal = 0x01U;
; ..\mcal_src\Dio.c	   992        }
; ..\mcal_src\Dio.c	   993        /* GetPortAddressPtr will hold the port address */
; ..\mcal_src\Dio.c	   994        GetPortAddressPtr = Dio_lGetPortAdr(Dio_lGetPortNumber(ChannelId));
	seln	d15,d5,d15,#1
	call	Dio_lGetPortNumber
.L132:
	mov	d4,d2
	call	Dio_lGetPortAdr
.L136:
	mov.aa	a15,a2
.L138:

; ..\mcal_src\Dio.c	   995  
; ..\mcal_src\Dio.c	   996        /* Write to the PORT OMR register to reflect at the channel*/
; ..\mcal_src\Dio.c	   997        DIO_SFR_RUNTIME_USER_MODE_WRITE32((GetPortAddressPtr->OMR.U),
	mov	d4,d8
	call	Dio_lGetPinNumber
.L137:
	sh	d15,d15,d2
	st.w	[a15]4,d15
.L135:

; ..\mcal_src\Dio.c	   998         ((unsigned_int)(OmrVal << Dio_lGetPinNumber(ChannelId))));
; ..\mcal_src\Dio.c	   999      }
; ..\mcal_src\Dio.c	  1000      #if (DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio.c	  1001      else
; ..\mcal_src\Dio.c	  1002      {
; ..\mcal_src\Dio.c	  1003         SafeMcal_ReportError(
; ..\mcal_src\Dio.c	  1004               (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio.c	  1005                DIO_INSTANCE_ID,
; ..\mcal_src\Dio.c	  1006                DIO_SID_WRITECHANNEL,
; ..\mcal_src\Dio.c	  1007                DIO_E_PARAM_INVALID_LEVEL);
; ..\mcal_src\Dio.c	  1008      }
; ..\mcal_src\Dio.c	  1009      #endif /*DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio.c	  1010    }
; ..\mcal_src\Dio.c	  1011  }/* Dio_WriteChannel */
	ret
.L88:
	
__Dio_WriteChannel_function_end:
	.size	Dio_WriteChannel,__Dio_WriteChannel_function_end-Dio_WriteChannel
.L41:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_ReadPort')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_ReadPort

; ..\mcal_src\Dio.c	  1012  
; ..\mcal_src\Dio.c	  1013  
; ..\mcal_src\Dio.c	  1014  /*******************************************************************************
; ..\mcal_src\Dio.c	  1015  ** Traceability    : [cover parentID=DS_AS_DIO135,DS_AS_DIO013,DS_AS_DIO031,
; ..\mcal_src\Dio.c	  1016                        DS_AS_DIO104,DS_AS_DIO060_4,DS_AS_DIO012,DS_AS_DIO083,
; ..\mcal_src\Dio.c	  1017                        DS_AS_DIO118_2]                                         **
; ..\mcal_src\Dio.c	  1018  **                                                                            **
; ..\mcal_src\Dio.c	  1019  ** Syntax : Dio_PortLevelType Dio_ReadPort(Dio_PortType PortId)               **
; ..\mcal_src\Dio.c	  1020  **                                                                            **
; ..\mcal_src\Dio.c	  1021  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	  1022  **                                                                            **
; ..\mcal_src\Dio.c	  1023  ** Service ID:  2                                                             **
; ..\mcal_src\Dio.c	  1024  **                                                                            **
; ..\mcal_src\Dio.c	  1025  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Dio.c	  1026  **                                                                            **
; ..\mcal_src\Dio.c	  1027  ** Reentrancy:  DIO005: DIO060: reentrant                                     **
; ..\mcal_src\Dio.c	  1028  **                                                                            **
; ..\mcal_src\Dio.c	  1029  ** Parameters (in):  PortId - Port id whose level to be read                  **
; ..\mcal_src\Dio.c	  1030  **                                                                            **
; ..\mcal_src\Dio.c	  1031  ** Parameters (out):  none                                                    **
; ..\mcal_src\Dio.c	  1032  **                                                                            **
; ..\mcal_src\Dio.c	  1033  ** Return value:  Dio_PortLevelType - The function returns value or the level **
; ..\mcal_src\Dio.c	  1034  **                of the specified channel which is of type Dio_PortLevelType **
; ..\mcal_src\Dio.c	  1035  **                                                                            **
; ..\mcal_src\Dio.c	  1036  ** Description : This function:                                               **
; ..\mcal_src\Dio.c	  1037  **      - DIO013: DIO031: returns the level of specified port                 **
; ..\mcal_src\Dio.c	  1038  **                                                                            **
; ..\mcal_src\Dio.c	  1039  *******************************************************************************/
; ..\mcal_src\Dio.c	  1040  Dio_PortLevelType Dio_ReadPort(Dio_PortType PortId)
; Function Dio_ReadPort
.L19:
Dio_ReadPort:	.type	func

; ..\mcal_src\Dio.c	  1041  {
; ..\mcal_src\Dio.c	  1042    Ifx_P             *GetPortAddressPtr;
; ..\mcal_src\Dio.c	  1043    Dio_PortLevelType  RetVal;
; ..\mcal_src\Dio.c	  1044  
; ..\mcal_src\Dio.c	  1045    /* Check for the validity of symbolic Port ID
; ..\mcal_src\Dio.c	  1046       Reported DET if error detected
; ..\mcal_src\Dio.c	  1047    */
; ..\mcal_src\Dio.c	  1048    #if((DIO_SAFETY_ENABLE == STD_ON) || (DIO_DEV_ERROR_DETECT == STD_ON))
; ..\mcal_src\Dio.c	  1049  
; ..\mcal_src\Dio.c	  1050    /* Return value should be zero for errors*/
; ..\mcal_src\Dio.c	  1051    RetVal = (Dio_PortLevelType)STD_LOW;
; ..\mcal_src\Dio.c	  1052  
; ..\mcal_src\Dio.c	  1053    if(Dio_lErrorCheckPortDet(PortId,DIO_SID_READPORT)
; ..\mcal_src\Dio.c	  1054                           == DIO_NO_ERROR)
; ..\mcal_src\Dio.c	  1055    #endif /*DIO_SAFETY_ENABLE == STD_ON || DIO_DEV_ERROR_DETECT == STD_ON*/
; ..\mcal_src\Dio.c	  1056      {
; ..\mcal_src\Dio.c	  1057        /* Return the Port IN register value
; ..\mcal_src\Dio.c	  1058           DIO104: MSB for small port size are zero from hardware itself
; ..\mcal_src\Dio.c	  1059           GetPortAddressPtr will hold the port address
; ..\mcal_src\Dio.c	  1060        */
; ..\mcal_src\Dio.c	  1061        GetPortAddressPtr = Dio_lGetPortAdr(PortId);
	mov	d8,d4
	call	Dio_lGetPortAdr
.L139:

; ..\mcal_src\Dio.c	  1062        RetVal = ((Dio_PortLevelType)DIO_SFR_RUNTIME_USER_MODE_READ32\ 
; ..\mcal_src\Dio.c	  1063                (GetPortAddressPtr->IN.U) & Dio_kMaskUndefinedPortPins[PortId]);
	movh.a	a15,#@his(Dio_kMaskUndefinedPortPins)
.L162:
	ld.w	d15,[a2]36
.L163:
	lea	a15,[a15]@los(Dio_kMaskUndefinedPortPins)
.L164:
	addsc.a	a15,a15,d8,#1
.L165:
	extr.u	d2,d15,#0,#16
.L166:
	ld.hu	d15,[a15]0
.L140:

; ..\mcal_src\Dio.c	  1064      }
; ..\mcal_src\Dio.c	  1065    return (RetVal);
; ..\mcal_src\Dio.c	  1066  }/* Dio_ReadPort */
	and	d2,d15
	ret
.L93:
	
__Dio_ReadPort_function_end:
	.size	Dio_ReadPort,__Dio_ReadPort_function_end-Dio_ReadPort
.L46:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_WritePort')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_WritePort

; ..\mcal_src\Dio.c	  1067  
; ..\mcal_src\Dio.c	  1068  /*******************************************************************************
; ..\mcal_src\Dio.c	  1069  ** Traceability    : [cover parentID=DS_AS_DIO136,DS_AS_DIO007_DIO034,
; ..\mcal_src\Dio.c	  1070                        DS_AS_DIO004_DIO035_DIO108,DS_AS_DIO105,DS_AS_DIO060_5,
; ..\mcal_src\Dio.c	  1071                        DS_AS_DIO109,DS_AS_DIO064,DS_AS_DIO119_2]               **
; ..\mcal_src\Dio.c	  1072  **                                                                            **
; ..\mcal_src\Dio.c	  1073  ** Syntax : void Dio_WritePort(Dio_PortType PortId, Dio_PortLevelType Level)  **
; ..\mcal_src\Dio.c	  1074  **                                                                            **
; ..\mcal_src\Dio.c	  1075  ** [/cover]                                                                   **
; ..\mcal_src\Dio.c	  1076  **                                                                            **
; ..\mcal_src\Dio.c	  1077  ** Service ID:  3                                                             **
; ..\mcal_src\Dio.c	  1078  **                                                                            **
; ..\mcal_src\Dio.c	  1079  ** Sync/Async:  Synchronous                                                   **
; ..\mcal_src\Dio.c	  1080  **                                                                            **
; ..\mcal_src\Dio.c	  1081  ** Reentrancy:  DIO005: DIO060: reentrant                                     **
; ..\mcal_src\Dio.c	  1082  **                                                                            **
; ..\mcal_src\Dio.c	  1083  ** Parameters (in):  PortId - port id whose level to be set                   **
; ..\mcal_src\Dio.c	  1084  **                   Level - port level to be set                             **
; ..\mcal_src\Dio.c	  1085  **                                                                            **
; ..\mcal_src\Dio.c	  1086  ** Parameters (out):  none                                                    **
; ..\mcal_src\Dio.c	  1087  **                                                                            **
; ..\mcal_src\Dio.c	  1088  ** Return value:  none                                                        **
; ..\mcal_src\Dio.c	  1089  **                                                                            **
; ..\mcal_src\Dio.c	  1090  ** Description : This function:                                               **
; ..\mcal_src\Dio.c	  1091  **      - DIO034: sets the specified level of specified port                  **
; ..\mcal_src\Dio.c	  1092  **      - DIO004: DIO035: DIO108: Hardware takes care not to change the value **
; ..\mcal_src\Dio.c	  1093  **        for the input channels while writing to complete port               **
; ..\mcal_src\Dio.c	  1094  **      - DIO007: All the port pins are written at one shot                   **
; ..\mcal_src\Dio.c	  1095  **                                                                            **
; ..\mcal_src\Dio.c	  1096  *******************************************************************************/
; ..\mcal_src\Dio.c	  1097  void Dio_WritePort (Dio_PortType PortId, Dio_PortLevelType Level)
; Function Dio_WritePort
.L21:
Dio_WritePort:	.type	func

; ..\mcal_src\Dio.c	  1098  {
; ..\mcal_src\Dio.c	  1099    /*
; ..\mcal_src\Dio.c	  1100      Note: volatile is used for the variable GetPortAddressPtr.
; ..\mcal_src\Dio.c	  1101      The compiler may optimise the function call and the desired Level may not
; ..\mcal_src\Dio.c	  1102      be written to the Channel or to the Port.
; ..\mcal_src\Dio.c	  1103    */
; ..\mcal_src\Dio.c	  1104    volatile Ifx_P *GetPortAddressPtr;
; ..\mcal_src\Dio.c	  1105    uint32 LocalRegData;
; ..\mcal_src\Dio.c	  1106  
; ..\mcal_src\Dio.c	  1107  
; ..\mcal_src\Dio.c	  1108    #if((DIO_SAFETY_ENABLE == STD_ON) || (DIO_DEV_ERROR_DETECT == STD_ON))
; ..\mcal_src\Dio.c	  1109    if(Dio_lErrorCheckPortDet(PortId,DIO_SID_WRITEPORT)
; ..\mcal_src\Dio.c	  1110                           == DIO_NO_ERROR)
; ..\mcal_src\Dio.c	  1111    #endif /*DIO_SAFETY_ENABLE == STD_ON || DIO_DEV_ERROR_DETECT == STD_ON*/
; ..\mcal_src\Dio.c	  1112    {
; ..\mcal_src\Dio.c	  1113  
; ..\mcal_src\Dio.c	  1114      #if(DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio.c	  1115    /* check whether the requested Level is valid */
; ..\mcal_src\Dio.c	  1116      if(Dio_lCheckPortLevel(PortId,Level) == DIO_NO_ERROR)
; ..\mcal_src\Dio.c	  1117      {
; ..\mcal_src\Dio.c	  1118      #endif /* DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio.c	  1119        /* Write the Level to the Port OUT register
; ..\mcal_src\Dio.c	  1120           DIO105: If the port size is small, MSB's of Level are ignored
; ..\mcal_src\Dio.c	  1121           GetPortAddressPtr will hold the port address
; ..\mcal_src\Dio.c	  1122        */
; ..\mcal_src\Dio.c	  1123        GetPortAddressPtr = Dio_lGetPortAdr(PortId);
	mov	d15,d5
	call	Dio_lGetPortAdr
.L141:

; ..\mcal_src\Dio.c	  1124        LocalRegData = (uint32)Level & DIO_OUT_MASK;
; ..\mcal_src\Dio.c	  1125        DIO_SFR_RUNTIME_USER_MODE_WRITE32((GetPortAddressPtr->OUT.U),\ 
	st.w	[a2],d15
.L171:

; ..\mcal_src\Dio.c	  1126                                         LocalRegData);
; ..\mcal_src\Dio.c	  1127      #if(DIO_SAFETY_ENABLE == STD_ON)
; ..\mcal_src\Dio.c	  1128      }
; ..\mcal_src\Dio.c	  1129      else
; ..\mcal_src\Dio.c	  1130      {
; ..\mcal_src\Dio.c	  1131        SafeMcal_ReportError(
; ..\mcal_src\Dio.c	  1132                          (uint16)DIO_MODULE_ID,
; ..\mcal_src\Dio.c	  1133                           DIO_INSTANCE_ID,
; ..\mcal_src\Dio.c	  1134                           DIO_SID_WRITEPORT,
; ..\mcal_src\Dio.c	  1135                           DIO_E_PARAM_INVALID_LEVEL
; ..\mcal_src\Dio.c	  1136                           );
; ..\mcal_src\Dio.c	  1137      }
; ..\mcal_src\Dio.c	  1138      #endif /* DIO_SAFETY_ENABLE == STD_ON */
; ..\mcal_src\Dio.c	  1139    }
; ..\mcal_src\Dio.c	  1140  }/* Dio_WritePort */
	ret
.L97:
	
__Dio_WritePort_function_end:
	.size	Dio_WritePort,__Dio_WritePort_function_end-Dio_WritePort
.L51:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_lGetPortNumber')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_lGetPortNumber

; ..\mcal_src\Dio.c	  1141  
; ..\mcal_src\Dio.c	  1142  /*******************************************************************************
; ..\mcal_src\Dio.c	  1143  **                      Converted INLINE functions                            **
; ..\mcal_src\Dio.c	  1144  *******************************************************************************/
; ..\mcal_src\Dio.c	  1145  
; ..\mcal_src\Dio.c	  1146  #if((DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON))
; ..\mcal_src\Dio.c	  1147  /*******************************************************************************
; ..\mcal_src\Dio.c	  1148  ** Syntax           : IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable31          **
; ..\mcal_src\Dio.c	  1149  **                    (                                                       **
; ..\mcal_src\Dio.c	  1150  **                      Port                                                  **
; ..\mcal_src\Dio.c	  1151  **                    )                                                       **
; ..\mcal_src\Dio.c	  1152  **                                                                            **
; ..\mcal_src\Dio.c	  1153  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1154  **                                                                            **
; ..\mcal_src\Dio.c	  1155  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1156  **                                                                            **
; ..\mcal_src\Dio.c	  1157  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1158  **                                                                            **
; ..\mcal_src\Dio.c	  1159  ** Parameters (in)  : Port - Port to be checked                               **
; ..\mcal_src\Dio.c	  1160  **                                                                            **
; ..\mcal_src\Dio.c	  1161  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1162  **                                                                            **
; ..\mcal_src\Dio.c	  1163  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio.c	  1164  **                                                                            **
; ..\mcal_src\Dio.c	  1165  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1166  ** - The function like macro is to check if the port is available or not      **
; ..\mcal_src\Dio.c	  1167  **   for the microcontroller.                                                 **
; ..\mcal_src\Dio.c	  1168  **   Parameter: Port number of type uint8/uint16                              **
; ..\mcal_src\Dio.c	  1169  *******************************************************************************/
; ..\mcal_src\Dio.c	  1170  IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable31(uint32 Port)
; ..\mcal_src\Dio.c	  1171  {
; ..\mcal_src\Dio.c	  1172    uint32 RetVal;
; ..\mcal_src\Dio.c	  1173  
; ..\mcal_src\Dio.c	  1174    RetVal = ( ((uint32)(DIO_CHANNEL_BIT_MASK) << (Port)) &
; ..\mcal_src\Dio.c	  1175               ((uint32)DIO_PORTS_AVAILABLE_00_31)
; ..\mcal_src\Dio.c	  1176             );
; ..\mcal_src\Dio.c	  1177    return(RetVal);
; ..\mcal_src\Dio.c	  1178  }
; ..\mcal_src\Dio.c	  1179  
; ..\mcal_src\Dio.c	  1180  /*******************************************************************************
; ..\mcal_src\Dio.c	  1181  ** Syntax           : IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable40          **
; ..\mcal_src\Dio.c	  1182  **                    (                                                       **
; ..\mcal_src\Dio.c	  1183  **                      Port                                                  **
; ..\mcal_src\Dio.c	  1184  **                    )                                                       **
; ..\mcal_src\Dio.c	  1185  **                                                                            **
; ..\mcal_src\Dio.c	  1186  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1187  **                                                                            **
; ..\mcal_src\Dio.c	  1188  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1189  **                                                                            **
; ..\mcal_src\Dio.c	  1190  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1191  **                                                                            **
; ..\mcal_src\Dio.c	  1192  ** Parameters (in)  : Port - Port to be checked                               **
; ..\mcal_src\Dio.c	  1193  **                                                                            **
; ..\mcal_src\Dio.c	  1194  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1195  **                                                                            **
; ..\mcal_src\Dio.c	  1196  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio.c	  1197  **                                                                            **
; ..\mcal_src\Dio.c	  1198  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1199  ** - The function like macro is to check if the port is available or not      **
; ..\mcal_src\Dio.c	  1200  **   for the microcontroller.                                                 **
; ..\mcal_src\Dio.c	  1201  **   Parameter: Port number of type uint8/uint16                              **
; ..\mcal_src\Dio.c	  1202  *******************************************************************************/
; ..\mcal_src\Dio.c	  1203  IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable40(uint32 Port)
; ..\mcal_src\Dio.c	  1204  {
; ..\mcal_src\Dio.c	  1205    uint32 RetVal;
; ..\mcal_src\Dio.c	  1206  
; ..\mcal_src\Dio.c	  1207    RetVal = ( ((uint32)(DIO_CHANNEL_BIT_MASK) << (Port - DIO_NUMBER_32)) &
; ..\mcal_src\Dio.c	  1208               ((uint32)DIO_PORTS_AVAILABLE_32_40)
; ..\mcal_src\Dio.c	  1209             );
; ..\mcal_src\Dio.c	  1210    return(RetVal);
; ..\mcal_src\Dio.c	  1211  }
; ..\mcal_src\Dio.c	  1212  
; ..\mcal_src\Dio.c	  1213  /*******************************************************************************
; ..\mcal_src\Dio.c	  1214  ** Syntax           : IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable            **
; ..\mcal_src\Dio.c	  1215  **                    (                                                       **
; ..\mcal_src\Dio.c	  1216  **                      Port                                                  **
; ..\mcal_src\Dio.c	  1217  **                    )                                                       **
; ..\mcal_src\Dio.c	  1218  **                                                                            **
; ..\mcal_src\Dio.c	  1219  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1220  **                                                                            **
; ..\mcal_src\Dio.c	  1221  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1222  **                                                                            **
; ..\mcal_src\Dio.c	  1223  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1224  **                                                                            **
; ..\mcal_src\Dio.c	  1225  ** Parameters (in)  : Port - Port to be checked                               **
; ..\mcal_src\Dio.c	  1226  **                                                                            **
; ..\mcal_src\Dio.c	  1227  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1228  **                                                                            **
; ..\mcal_src\Dio.c	  1229  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio.c	  1230  **                                                                            **
; ..\mcal_src\Dio.c	  1231  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1232  ** - The function like macro is to check if the port is read only or          **
; ..\mcal_src\Dio.c	  1233  **   it is writable.                                                          **
; ..\mcal_src\Dio.c	  1234  **   Parameter: Port number of type uint8/uint16                              **
; ..\mcal_src\Dio.c	  1235  *******************************************************************************/
; ..\mcal_src\Dio.c	  1236  IFX_LOCAL_INLINE uint32 Dio_lIsPortAvailable(uint32 Port)
; ..\mcal_src\Dio.c	  1237  {
; ..\mcal_src\Dio.c	  1238    uint32 RetVal;
; ..\mcal_src\Dio.c	  1239  
; ..\mcal_src\Dio.c	  1240    RetVal = ((Port > DIO_NUMBER_31) ? (Dio_lIsPortAvailable40(Port)) :
; ..\mcal_src\Dio.c	  1241                                       (Dio_lIsPortAvailable31(Port))
; ..\mcal_src\Dio.c	  1242             );
; ..\mcal_src\Dio.c	  1243    return(RetVal);
; ..\mcal_src\Dio.c	  1244  }
; ..\mcal_src\Dio.c	  1245  
; ..\mcal_src\Dio.c	  1246  /*******************************************************************************
; ..\mcal_src\Dio.c	  1247  ** Syntax           : IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly31           **
; ..\mcal_src\Dio.c	  1248  **                    (                                                       **
; ..\mcal_src\Dio.c	  1249  **                      Port                                                  **
; ..\mcal_src\Dio.c	  1250  **                    )                                                       **
; ..\mcal_src\Dio.c	  1251  **                                                                            **
; ..\mcal_src\Dio.c	  1252  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1253  **                                                                            **
; ..\mcal_src\Dio.c	  1254  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1255  **                                                                            **
; ..\mcal_src\Dio.c	  1256  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1257  **                                                                            **
; ..\mcal_src\Dio.c	  1258  ** Parameters (in)  : Port - Port to be checked                               **
; ..\mcal_src\Dio.c	  1259  **                                                                            **
; ..\mcal_src\Dio.c	  1260  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1261  **                                                                            **
; ..\mcal_src\Dio.c	  1262  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio.c	  1263  **                                                                            **
; ..\mcal_src\Dio.c	  1264  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1265  ** - The function like macro is to check if the port is read only or          **
; ..\mcal_src\Dio.c	  1266  **   it is writable.                                                          **
; ..\mcal_src\Dio.c	  1267  **   Parameter: Port number of type uint8/uint16                              **
; ..\mcal_src\Dio.c	  1268  *******************************************************************************/
; ..\mcal_src\Dio.c	  1269  IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly31(uint32 Port)
; ..\mcal_src\Dio.c	  1270  {
; ..\mcal_src\Dio.c	  1271    uint32 RetVal;
; ..\mcal_src\Dio.c	  1272  
; ..\mcal_src\Dio.c	  1273    RetVal = ( ((uint32)(DIO_CHANNEL_BIT_MASK) << (Port)) &
; ..\mcal_src\Dio.c	  1274               ((uint32)DIO_PORTS_READONLY_00_31)
; ..\mcal_src\Dio.c	  1275             );
; ..\mcal_src\Dio.c	  1276    return(RetVal);
; ..\mcal_src\Dio.c	  1277  }
; ..\mcal_src\Dio.c	  1278  
; ..\mcal_src\Dio.c	  1279  /*******************************************************************************
; ..\mcal_src\Dio.c	  1280  ** Syntax           : IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly40           **
; ..\mcal_src\Dio.c	  1281  **                    (                                                       **
; ..\mcal_src\Dio.c	  1282  **                      Port                                                  **
; ..\mcal_src\Dio.c	  1283  **                    )                                                       **
; ..\mcal_src\Dio.c	  1284  **                                                                            **
; ..\mcal_src\Dio.c	  1285  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1286  **                                                                            **
; ..\mcal_src\Dio.c	  1287  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1288  **                                                                            **
; ..\mcal_src\Dio.c	  1289  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1290  **                                                                            **
; ..\mcal_src\Dio.c	  1291  ** Parameters (in)  : Port - Port to be checked                               **
; ..\mcal_src\Dio.c	  1292  **                                                                            **
; ..\mcal_src\Dio.c	  1293  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1294  **                                                                            **
; ..\mcal_src\Dio.c	  1295  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio.c	  1296  **                                                                            **
; ..\mcal_src\Dio.c	  1297  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1298  ** - The function like macro is to check if the port is read only or          **
; ..\mcal_src\Dio.c	  1299  **   it is writable.                                                          **
; ..\mcal_src\Dio.c	  1300  **   Parameter: Port number of type uint8/uint16                              **
; ..\mcal_src\Dio.c	  1301  *******************************************************************************/
; ..\mcal_src\Dio.c	  1302  IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly40(uint32 Port)
; ..\mcal_src\Dio.c	  1303  {
; ..\mcal_src\Dio.c	  1304    uint32 RetVal;
; ..\mcal_src\Dio.c	  1305  
; ..\mcal_src\Dio.c	  1306    RetVal = ( ((uint32)(DIO_CHANNEL_BIT_MASK) << (Port - DIO_NUMBER_32)) &
; ..\mcal_src\Dio.c	  1307               ((uint32)DIO_PORTS_READONLY_32_40)
; ..\mcal_src\Dio.c	  1308             );
; ..\mcal_src\Dio.c	  1309    return(RetVal);
; ..\mcal_src\Dio.c	  1310  }
; ..\mcal_src\Dio.c	  1311  
; ..\mcal_src\Dio.c	  1312  /*******************************************************************************
; ..\mcal_src\Dio.c	  1313  ** Syntax           : IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly             **
; ..\mcal_src\Dio.c	  1314  **                    (                                                       **
; ..\mcal_src\Dio.c	  1315  **                      Port                                                  **
; ..\mcal_src\Dio.c	  1316  **                    )                                                       **
; ..\mcal_src\Dio.c	  1317  **                                                                            **
; ..\mcal_src\Dio.c	  1318  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1319  **                                                                            **
; ..\mcal_src\Dio.c	  1320  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1321  **                                                                            **
; ..\mcal_src\Dio.c	  1322  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1323  **                                                                            **
; ..\mcal_src\Dio.c	  1324  ** Parameters (in)  : Port - Port to be checked                               **
; ..\mcal_src\Dio.c	  1325  **                                                                            **
; ..\mcal_src\Dio.c	  1326  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1327  **                                                                            **
; ..\mcal_src\Dio.c	  1328  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio.c	  1329  **                                                                            **
; ..\mcal_src\Dio.c	  1330  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1331  ** - The function like macro is to check if the port is read only or          **
; ..\mcal_src\Dio.c	  1332  **   it is writable.                                                          **
; ..\mcal_src\Dio.c	  1333  **   Parameter: Port number of type uint8/uint16                              **
; ..\mcal_src\Dio.c	  1334  *******************************************************************************/
; ..\mcal_src\Dio.c	  1335  IFX_LOCAL_INLINE uint32 Dio_lIsPortReadOnly(uint32 Port)
; ..\mcal_src\Dio.c	  1336  {
; ..\mcal_src\Dio.c	  1337    uint32 RetVal;
; ..\mcal_src\Dio.c	  1338  
; ..\mcal_src\Dio.c	  1339    RetVal = ((Port <= DIO_NUMBER_31) ? (Dio_lIsPortReadOnly31(Port)) :
; ..\mcal_src\Dio.c	  1340                                        (Dio_lIsPortReadOnly40(Port))
; ..\mcal_src\Dio.c	  1341             );
; ..\mcal_src\Dio.c	  1342    return(RetVal);
; ..\mcal_src\Dio.c	  1343  }
; ..\mcal_src\Dio.c	  1344  
; ..\mcal_src\Dio.c	  1345  /*******************************************************************************
; ..\mcal_src\Dio.c	  1346  ** Syntax           : IFX_LOCAL_INLINE uint16 Dio_lIsPinAvailable             **
; ..\mcal_src\Dio.c	  1347  **                    (                                                       **
; ..\mcal_src\Dio.c	  1348  **                      Port,                                                 **
; ..\mcal_src\Dio.c	  1349  **                      Pin                                                   **
; ..\mcal_src\Dio.c	  1350  **                    )                                                       **
; ..\mcal_src\Dio.c	  1351  **                                                                            **
; ..\mcal_src\Dio.c	  1352  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1353  **                                                                            **
; ..\mcal_src\Dio.c	  1354  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1355  **                                                                            **
; ..\mcal_src\Dio.c	  1356  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1357  **                                                                            **
; ..\mcal_src\Dio.c	  1358  ** Parameters (in)  : Port - Port number                                      **
; ..\mcal_src\Dio.c	  1359  **                    Pin  - Pin to be validated                              **
; ..\mcal_src\Dio.c	  1360  **                                                                            **
; ..\mcal_src\Dio.c	  1361  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1362  **                                                                            **
; ..\mcal_src\Dio.c	  1363  ** Return value     : RetVal                                                  **
; ..\mcal_src\Dio.c	  1364  **                                                                            **
; ..\mcal_src\Dio.c	  1365  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1366  ** - The function like macro is to check if the Pin is available or not.      **
; ..\mcal_src\Dio.c	  1367  **                                                                            **
; ..\mcal_src\Dio.c	  1368  **   Parameter: Port number of type uint8/uint16                              **
; ..\mcal_src\Dio.c	  1369  *******************************************************************************/
; ..\mcal_src\Dio.c	  1370  IFX_LOCAL_INLINE uint16 Dio_lIsPinAvailable(uint32 Port, uint8 Pin)
; ..\mcal_src\Dio.c	  1371  {
; ..\mcal_src\Dio.c	  1372   uint16 RetVal;
; ..\mcal_src\Dio.c	  1373  
; ..\mcal_src\Dio.c	  1374   RetVal =  ( (uint16)((uint32)DIO_CHANNEL_BIT_MASK << (Pin)) &
; ..\mcal_src\Dio.c	  1375                Dio_kMaskUndefinedPortPins[Port] );
; ..\mcal_src\Dio.c	  1376   return RetVal;
; ..\mcal_src\Dio.c	  1377  }
; ..\mcal_src\Dio.c	  1378  
; ..\mcal_src\Dio.c	  1379  #endif /* (DIO_DEV_ERROR_DETECT == STD_ON) || (DIO_SAFETY_ENABLE == STD_ON) */
; ..\mcal_src\Dio.c	  1380  
; ..\mcal_src\Dio.c	  1381  
; ..\mcal_src\Dio.c	  1382  /*******************************************************************************
; ..\mcal_src\Dio.c	  1383  ** Syntax           : Dio_PortType Dio_lGetPortNumber                         **
; ..\mcal_src\Dio.c	  1384  **                    (                                                       **
; ..\mcal_src\Dio.c	  1385  **                      ChannelId                                             **
; ..\mcal_src\Dio.c	  1386  **                    )                                                       **
; ..\mcal_src\Dio.c	  1387  **                                                                            **
; ..\mcal_src\Dio.c	  1388  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1389  **                                                                            **
; ..\mcal_src\Dio.c	  1390  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1391  **                                                                            **
; ..\mcal_src\Dio.c	  1392  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1393  **                                                                            **
; ..\mcal_src\Dio.c	  1394  ** Parameters (in)  : ChannelId - derived the port number from ChannelId      **
; ..\mcal_src\Dio.c	  1395  **                                                                            **
; ..\mcal_src\Dio.c	  1396  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1397  **                                                                            **
; ..\mcal_src\Dio.c	  1398  ** Return value     : Dio_PortType RetVal                                     **
; ..\mcal_src\Dio.c	  1399  **                                                                            **
; ..\mcal_src\Dio.c	  1400  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1401  ** - The function like macro is to identify the port number from the passed   **
; ..\mcal_src\Dio.c	  1402  **   ChannelId.                                                               **
; ..\mcal_src\Dio.c	  1403  *******************************************************************************/
; ..\mcal_src\Dio.c	  1404  Dio_PortType Dio_lGetPortNumber(Dio_ChannelType ChannelId)
; Function Dio_lGetPortNumber
.L23:
Dio_lGetPortNumber:	.type	func

; ..\mcal_src\Dio.c	  1405  {
; ..\mcal_src\Dio.c	  1406    Dio_PortType RetVal;
; ..\mcal_src\Dio.c	  1407  
; ..\mcal_src\Dio.c	  1408    RetVal = (Dio_PortType)((ChannelId &
; ..\mcal_src\Dio.c	  1409                     (uint16)DIO_4_TO_11_MASK) >> (uint16)DIO_PORT_NUM_OFFSET
	mov	d15,#4080
.L194:
	and	d4,d15
.L142:

; ..\mcal_src\Dio.c	  1410                    );
; ..\mcal_src\Dio.c	  1411    return(RetVal);
; ..\mcal_src\Dio.c	  1412  }
	sha	d2,d4,#-4
	ret
.L111:
	
__Dio_lGetPortNumber_function_end:
	.size	Dio_lGetPortNumber,__Dio_lGetPortNumber_function_end-Dio_lGetPortNumber
.L66:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_lGetPortAdr')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_lGetPortAdr

; ..\mcal_src\Dio.c	  1413  
; ..\mcal_src\Dio.c	  1414  /*******************************************************************************
; ..\mcal_src\Dio.c	  1415  ** Syntax           : Ifx_P *Dio_lGetPortAdr                                  **
; ..\mcal_src\Dio.c	  1416  **                    (                                                       **
; ..\mcal_src\Dio.c	  1417  **                      x                                                     **
; ..\mcal_src\Dio.c	  1418  **                    )                                                       **
; ..\mcal_src\Dio.c	  1419  **                                                                            **
; ..\mcal_src\Dio.c	  1420  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1421  **                                                                            **
; ..\mcal_src\Dio.c	  1422  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1423  **                                                                            **
; ..\mcal_src\Dio.c	  1424  ** Reentrancy       : Reentrant                                               **
; ..\mcal_src\Dio.c	  1425  **                                                                            **
; ..\mcal_src\Dio.c	  1426  ** Parameters (in)  : x - Port Number                                         **
; ..\mcal_src\Dio.c	  1427  **                                                                            **
; ..\mcal_src\Dio.c	  1428  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1429  **                                                                            **
; ..\mcal_src\Dio.c	  1430  ** Return value     : Ifx_P RetVal                                            **
; ..\mcal_src\Dio.c	  1431  **                                                                            **
; ..\mcal_src\Dio.c	  1432  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1433  ** - The function like macro is to extract the Address of Px_OUT              **
; ..\mcal_src\Dio.c	  1434  **   register, where x is variable. Note: x-PortNumber.                       **
; ..\mcal_src\Dio.c	  1435  *******************************************************************************/
; ..\mcal_src\Dio.c	  1436  Ifx_P *Dio_lGetPortAdr(Dio_PortType PortNumber)
; Function Dio_lGetPortAdr
.L25:
Dio_lGetPortAdr:	.type	func

; ..\mcal_src\Dio.c	  1437  {
; ..\mcal_src\Dio.c	  1438    Ifx_P *RetVal;
; ..\mcal_src\Dio.c	  1439    uint8  Quotient;
; ..\mcal_src\Dio.c	  1440  
; ..\mcal_src\Dio.c	  1441  /*
; ..\mcal_src\Dio.c	  1442  Optimization used for the conversion
; ..\mcal_src\Dio.c	  1443  
; ..\mcal_src\Dio.c	  1444  ((Ifx_P * )(void *)&P00_OUT +
; ..\mcal_src\Dio.c	  1445               (((PortNumber / DIO_CONSTANT_10) * DIO_CONSTANT_16) +
; ..\mcal_src\Dio.c	  1446               (PortNumber % DIO_CONSTANT_10)))
; ..\mcal_src\Dio.c	  1447             );
; ..\mcal_src\Dio.c	  1448  
; ..\mcal_src\Dio.c	  1449  1.Convert 1/C into binary (where C is the number u want to divide with)
; ..\mcal_src\Dio.c	  1450  2.Consider all the bits present to the right of the binary point,
; ..\mcal_src\Dio.c	  1451    and do a left shift until the bit to the right of the binary point is 1.
; ..\mcal_src\Dio.c	  1452    Let the number of shifts performed is "S".
; ..\mcal_src\Dio.c	  1453  3.Now take the most significant 17 bits, add 1 to it
; ..\mcal_src\Dio.c	  1454    and then truncate it to 16 bits.
; ..\mcal_src\Dio.c	  1455    Convert that 16 bits to hexadecimal format and let that be "M"
; ..\mcal_src\Dio.c	  1456  The quotient "Q" is obtained as
; ..\mcal_src\Dio.c	  1457    Q = ((VAL * M) >> 16) >> S
; ..\mcal_src\Dio.c	  1458  
; ..\mcal_src\Dio.c	  1459  Example1: To get (P/10)
; ..\mcal_src\Dio.c	  1460    To divide a number P by 10,, i.e., (P/10)
; ..\mcal_src\Dio.c	  1461  1.Convert (1/10) into binary, i.e., 0.00011001100110011......
; ..\mcal_src\Dio.c	  1462  2.Do left shift by 3 digit,
; ..\mcal_src\Dio.c	  1463    such that bit to the right of the decinmal point is 1.
; ..\mcal_src\Dio.c	  1464    S = 3,
; ..\mcal_src\Dio.c	  1465    ie., 0.11001100110011001100.......
; ..\mcal_src\Dio.c	  1466  3.Now, let the type of the variable P be uint8(8 bits) ,
; ..\mcal_src\Dio.c	  1467    As it is 8-bit, consider 8 + 1 = 9 bits and binary one to it
; ..\mcal_src\Dio.c	  1468     0.110011001
; ..\mcal_src\Dio.c	  1469     +         1
; ..\mcal_src\Dio.c	  1470   = 0.110011010
; ..\mcal_src\Dio.c	  1471  Round it to 8-bits again
; ..\mcal_src\Dio.c	  1472    becomes 0.11001101 = 0xCD.
; ..\mcal_src\Dio.c	  1473    M = 0xCD
; ..\mcal_src\Dio.c	  1474  
; ..\mcal_src\Dio.c	  1475    Quotient is obtained by,
; ..\mcal_src\Dio.c	  1476    Q = ((VAL * M) >> 16) >> S
; ..\mcal_src\Dio.c	  1477    1'e.,
; ..\mcal_src\Dio.c	  1478    Q = ((VAL * (0xCD)) >> 8) >> 3
; ..\mcal_src\Dio.c	  1479  
; ..\mcal_src\Dio.c	  1480  Example2: To get (P%10) modulus 10
; ..\mcal_src\Dio.c	  1481  1. Calculate the quotient as in Example1, Q
; ..\mcal_src\Dio.c	  1482     Q = P/10
; ..\mcal_src\Dio.c	  1483     Modulus 10 = P - (Q*10),
; ..\mcal_src\Dio.c	  1484                = P - (Q*(8 + 2))
; ..\mcal_src\Dio.c	  1485                = P - (Q*8 + Q*2)
; ..\mcal_src\Dio.c	  1486                = P - (Q<<3 + Q<<1)
; ..\mcal_src\Dio.c	  1487  */
; ..\mcal_src\Dio.c	  1488    Quotient = (uint8)(((uint16)PortNumber*(uint16)DIO_QUOTIENT_EXTRACT_NUM) >>
; ..\mcal_src\Dio.c	  1489                                                           DIO_QUOTIENT_SHIFT);
; ..\mcal_src\Dio.c	  1490  
; ..\mcal_src\Dio.c	  1491    /*IFX_MISRA_RULE_17_04_STATUS=Pointer arithmetic is used
; ..\mcal_src\Dio.c	  1492     to efficiently access the SFRs of multiple Ports*/
; ..\mcal_src\Dio.c	  1493    RetVal = ( (Ifx_P * )(void *)&P00_OUT +
	mul	d15,d4,#205
	movh.a	a15,#61444
	lea	a15,[a15]@los(0xf003a000)
.L199:
	sha	d15,d15,#-11
.L144:

; ..\mcal_src\Dio.c	  1494               ((uint8)(Quotient<<DIO_SHIFT_FOUR) + (PortNumber - ((uint8)
	sha	d0,d15,#4
.L200:
	extr.u	d0,d0,#0,#8
.L201:

; ..\mcal_src\Dio.c	  1495                       (Quotient<<DIO_SHIFT_THREE) + (uint8)(Quotient<<1U))))
	sha	d1,d15,#3
.L202:
	sha	d15,#1
.L145:
	add	d1,d15
.L203:
	sub	d4,d1
.L143:
	add	d0,d4
	sha	d15,d0,#8
.L204:
	addsc.a	a2,a15,d15,#0
.L205:

; ..\mcal_src\Dio.c	  1496             );
; ..\mcal_src\Dio.c	  1497  
; ..\mcal_src\Dio.c	  1498    return(RetVal);
; ..\mcal_src\Dio.c	  1499  }
	ret
.L113:
	
__Dio_lGetPortAdr_function_end:
	.size	Dio_lGetPortAdr,__Dio_lGetPortAdr_function_end-Dio_lGetPortAdr
.L71:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Dio_lGetPinNumber')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Dio_lGetPinNumber

; ..\mcal_src\Dio.c	  1500  
; ..\mcal_src\Dio.c	  1501  /*******************************************************************************
; ..\mcal_src\Dio.c	  1502  ** Syntax           : uint8 Dio_lGetPinNumber                                 **
; ..\mcal_src\Dio.c	  1503  **                    (                                                       **
; ..\mcal_src\Dio.c	  1504  **                      ChannelId                                             **
; ..\mcal_src\Dio.c	  1505  **                    )                                                       **
; ..\mcal_src\Dio.c	  1506  **                                                                            **
; ..\mcal_src\Dio.c	  1507  ** Service ID       : None                                                    **
; ..\mcal_src\Dio.c	  1508  **                                                                            **
; ..\mcal_src\Dio.c	  1509  ** Sync/Async       : Synchronous                                             **
; ..\mcal_src\Dio.c	  1510  **                                                                            **
; ..\mcal_src\Dio.c	  1511  ** Reentrancy       : Reentrant                                           **
; ..\mcal_src\Dio.c	  1512  **                                                                            **
; ..\mcal_src\Dio.c	  1513  ** Parameters (in)  : ChannelId - derived the pin number from ChannelId       **
; ..\mcal_src\Dio.c	  1514  **                                                                            **
; ..\mcal_src\Dio.c	  1515  ** Parameters (out) : None                                                    **
; ..\mcal_src\Dio.c	  1516  **                                                                            **
; ..\mcal_src\Dio.c	  1517  ** Return value     : uint8 RetVal                                            **
; ..\mcal_src\Dio.c	  1518  **                                                                            **
; ..\mcal_src\Dio.c	  1519  ** Description      :                                                         **
; ..\mcal_src\Dio.c	  1520  ** - The function like macro is to identify the pin number from the passed    **
; ..\mcal_src\Dio.c	  1521  **   ChannelId.                                                               **
; ..\mcal_src\Dio.c	  1522  *******************************************************************************/
; ..\mcal_src\Dio.c	  1523  uint8 Dio_lGetPinNumber(Dio_ChannelType ChannelId)
; Function Dio_lGetPinNumber
.L27:
Dio_lGetPinNumber:	.type	func

; ..\mcal_src\Dio.c	  1524  {
; ..\mcal_src\Dio.c	  1525    uint8 RetVal;
; ..\mcal_src\Dio.c	  1526  
; ..\mcal_src\Dio.c	  1527    RetVal = (uint8)(ChannelId & (uint16)DIO_PIN_LOW4_MASK );
	extr.u	d15,d4,#0,#8
.L210:

; ..\mcal_src\Dio.c	  1528    return(RetVal);
; ..\mcal_src\Dio.c	  1529  }
	and	d2,d15,#15
	ret
.L116:
	
__Dio_lGetPinNumber_function_end:
	.size	Dio_lGetPinNumber,__Dio_lGetPinNumber_function_end-Dio_lGetPinNumber
.L76:
	; End of function
	
	.sdecl	'.rodata.CPU0.Private.DEFAULT_CONST_16BIT',data,rom,cluster('Dio_kMaskUndefinedPortPins')
	.sect	'.rodata.CPU0.Private.DEFAULT_CONST_16BIT'
	.align	4
Dio_kMaskUndefinedPortPins:	.type	object
	.size	Dio_kMaskUndefinedPortPins,84
	.half	5119
	.space	2
	.half	511
	.space	14
	.half	110,8012
	.space	2
	.half	15,511,511
	.space	8
	.half	32717,252,31,2
	.space	18
	.half	8191,15
	.space	10
	.half	4095,4095
	.calls	'Dio_ReadChannelGroup','Dio_lGetPortAdr'
	.calls	'Dio_WriteChannelGroup','Dio_lGetPortAdr'
	.calls	'Dio_ReadChannel','Dio_lGetPortNumber'
	.calls	'Dio_ReadChannel','Dio_lGetPortAdr'
	.calls	'Dio_ReadChannel','Dio_lGetPinNumber'
	.calls	'Dio_WriteChannel','Dio_lGetPortNumber'
	.calls	'Dio_WriteChannel','Dio_lGetPortAdr'
	.calls	'Dio_WriteChannel','Dio_lGetPinNumber'
	.calls	'Dio_ReadPort','Dio_lGetPortAdr'
	.calls	'Dio_WritePort','Dio_lGetPortAdr'
	.calls	'Dio_ReadChannelGroup','',0
	.calls	'Dio_WriteChannelGroup','',0
	.calls	'Dio_ReadChannel','',0
	.calls	'Dio_WriteChannel','',0
	.calls	'Dio_ReadPort','',0
	.calls	'Dio_WritePort','',0
	.calls	'Dio_lGetPortNumber','',0
	.calls	'Dio_lGetPortAdr','',0
	.calls	'Dio_lGetPinNumber','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L29:
	.word	8835
	.half	3
	.word	.L30
	.byte	4
.L28:
	.byte	1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L31
.L79:
	.byte	2
	.byte	'unsigned char',0,1,8
.L81:
	.byte	2
	.byte	'unsigned short int',0,2,7
.L83:
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'_Ifx_P',0,1,159,5,25,128,2,4,1,239,4,9,4,2
	.byte	'unsigned int',0,4,7,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,2
	.byte	'int',0,4,5,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OUT_Bits',0,1,231,2,16,4,6
	.byte	'P0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'P1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'P2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'P3',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'P4',0,1
	.word	172
	.byte	1,3,2,35,0,6
	.byte	'P5',0,1
	.word	172
	.byte	1,2,2,35,0,6
	.byte	'P6',0,1
	.word	172
	.byte	1,1,2,35,0,6
	.byte	'P7',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'P8',0,1
	.word	172
	.byte	1,7,2,35,1,6
	.byte	'P9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'P10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'P11',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'P12',0,1
	.word	172
	.byte	1,3,2,35,1,6
	.byte	'P13',0,1
	.word	172
	.byte	1,2,2,35,1,6
	.byte	'P14',0,1
	.word	172
	.byte	1,1,2,35,1,6
	.byte	'P15',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'reserved_16',0,2
	.word	189
	.byte	16,0,2,35,2,0,5
	.byte	'B',0,4
	.word	297
	.byte	2,35,0,0,5
	.byte	'OUT',0,4
	.word	246
	.byte	2,35,0,4,1,191,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMR_Bits',0,1,129,2,16,4,6
	.byte	'PS0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'PS1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'PS2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'PS3',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'PS4',0,1
	.word	172
	.byte	1,3,2,35,0,6
	.byte	'PS5',0,1
	.word	172
	.byte	1,2,2,35,0,6
	.byte	'PS6',0,1
	.word	172
	.byte	1,1,2,35,0,6
	.byte	'PS7',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'PS8',0,1
	.word	172
	.byte	1,7,2,35,1,6
	.byte	'PS9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'PS10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'PS11',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'PS12',0,1
	.word	172
	.byte	1,3,2,35,1,6
	.byte	'PS13',0,1
	.word	172
	.byte	1,2,2,35,1,6
	.byte	'PS14',0,1
	.word	172
	.byte	1,1,2,35,1,6
	.byte	'PS15',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'PCL0',0,1
	.word	172
	.byte	1,7,2,35,2,6
	.byte	'PCL1',0,1
	.word	172
	.byte	1,6,2,35,2,6
	.byte	'PCL2',0,1
	.word	172
	.byte	1,5,2,35,2,6
	.byte	'PCL3',0,1
	.word	172
	.byte	1,4,2,35,2,6
	.byte	'PCL4',0,1
	.word	172
	.byte	1,3,2,35,2,6
	.byte	'PCL5',0,1
	.word	172
	.byte	1,2,2,35,2,6
	.byte	'PCL6',0,1
	.word	172
	.byte	1,1,2,35,2,6
	.byte	'PCL7',0,1
	.word	172
	.byte	1,0,2,35,2,6
	.byte	'PCL8',0,1
	.word	172
	.byte	1,7,2,35,3,6
	.byte	'PCL9',0,1
	.word	172
	.byte	1,6,2,35,3,6
	.byte	'PCL10',0,1
	.word	172
	.byte	1,5,2,35,3,6
	.byte	'PCL11',0,1
	.word	172
	.byte	1,4,2,35,3,6
	.byte	'PCL12',0,1
	.word	172
	.byte	1,3,2,35,3,6
	.byte	'PCL13',0,1
	.word	172
	.byte	1,2,2,35,3,6
	.byte	'PCL14',0,1
	.word	172
	.byte	1,1,2,35,3,6
	.byte	'PCL15',0,1
	.word	172
	.byte	1,0,2,35,3,0,5
	.byte	'B',0,4
	.word	626
	.byte	2,35,0,0,5
	.byte	'OMR',0,4
	.word	598
	.byte	2,35,4,4,1,231,3,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_ID_Bits',0,1,110,16,4,6
	.byte	'MODREV',0,1
	.word	172
	.byte	8,0,2,35,0,6
	.byte	'MODTYPE',0,1
	.word	172
	.byte	8,0,2,35,1,6
	.byte	'MODNUMBER',0,2
	.word	189
	.byte	16,0,2,35,2,0,5
	.byte	'B',0,4
	.word	1210
	.byte	2,35,0,0,5
	.byte	'ID',0,4
	.word	1182
	.byte	2,35,8,7,4
	.word	172
	.byte	8,3,0,5
	.byte	'reserved_C',0,4
	.word	1313
	.byte	2,35,12,4,1,247,3,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_IOCR0_Bits',0,1,140,1,16,4,6
	.byte	'reserved_0',0,1
	.word	172
	.byte	3,5,2,35,0,6
	.byte	'PC0',0,1
	.word	172
	.byte	5,0,2,35,0,6
	.byte	'reserved_8',0,1
	.word	172
	.byte	3,5,2,35,1,6
	.byte	'PC1',0,1
	.word	172
	.byte	5,0,2,35,1,6
	.byte	'reserved_16',0,1
	.word	172
	.byte	3,5,2,35,2,6
	.byte	'PC2',0,1
	.word	172
	.byte	5,0,2,35,2,6
	.byte	'reserved_24',0,1
	.word	172
	.byte	3,5,2,35,3,6
	.byte	'PC3',0,1
	.word	172
	.byte	5,0,2,35,3,0,5
	.byte	'B',0,4
	.word	1370
	.byte	2,35,0,0,5
	.byte	'IOCR0',0,4
	.word	1342
	.byte	2,35,16,4,1,135,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_IOCR4_Bits',0,1,166,1,16,4,6
	.byte	'reserved_0',0,1
	.word	172
	.byte	3,5,2,35,0,6
	.byte	'PC4',0,1
	.word	172
	.byte	5,0,2,35,0,6
	.byte	'reserved_8',0,1
	.word	172
	.byte	3,5,2,35,1,6
	.byte	'PC5',0,1
	.word	172
	.byte	5,0,2,35,1,6
	.byte	'reserved_16',0,1
	.word	172
	.byte	3,5,2,35,2,6
	.byte	'PC6',0,1
	.word	172
	.byte	5,0,2,35,2,6
	.byte	'reserved_24',0,1
	.word	172
	.byte	3,5,2,35,3,6
	.byte	'PC7',0,1
	.word	172
	.byte	5,0,2,35,3,0,5
	.byte	'B',0,4
	.word	1600
	.byte	2,35,0,0,5
	.byte	'IOCR4',0,4
	.word	1572
	.byte	2,35,20,4,1,143,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_IOCR8_Bits',0,1,179,1,16,4,6
	.byte	'reserved_0',0,1
	.word	172
	.byte	3,5,2,35,0,6
	.byte	'PC8',0,1
	.word	172
	.byte	5,0,2,35,0,6
	.byte	'reserved_8',0,1
	.word	172
	.byte	3,5,2,35,1,6
	.byte	'PC9',0,1
	.word	172
	.byte	5,0,2,35,1,6
	.byte	'reserved_16',0,1
	.word	172
	.byte	3,5,2,35,2,6
	.byte	'PC10',0,1
	.word	172
	.byte	5,0,2,35,2,6
	.byte	'reserved_24',0,1
	.word	172
	.byte	3,5,2,35,3,6
	.byte	'PC11',0,1
	.word	172
	.byte	5,0,2,35,3,0,5
	.byte	'B',0,4
	.word	1830
	.byte	2,35,0,0,5
	.byte	'IOCR8',0,4
	.word	1802
	.byte	2,35,24,4,1,255,3,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_IOCR12_Bits',0,1,153,1,16,4,6
	.byte	'reserved_0',0,1
	.word	172
	.byte	3,5,2,35,0,6
	.byte	'PC12',0,1
	.word	172
	.byte	5,0,2,35,0,6
	.byte	'reserved_8',0,1
	.word	172
	.byte	3,5,2,35,1,6
	.byte	'PC13',0,1
	.word	172
	.byte	5,0,2,35,1,6
	.byte	'reserved_16',0,1
	.word	172
	.byte	3,5,2,35,2,6
	.byte	'PC14',0,1
	.word	172
	.byte	5,0,2,35,2,6
	.byte	'reserved_24',0,1
	.word	172
	.byte	3,5,2,35,3,6
	.byte	'PC15',0,1
	.word	172
	.byte	5,0,2,35,3,0,5
	.byte	'B',0,4
	.word	2062
	.byte	2,35,0,0,5
	.byte	'IOCR12',0,4
	.word	2034
	.byte	2,35,28,5
	.byte	'reserved_20',0,4
	.word	1313
	.byte	2,35,32,4,1,239,3,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_IN_Bits',0,1,118,16,4,6
	.byte	'P0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'P1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'P2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'P3',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'P4',0,1
	.word	172
	.byte	1,3,2,35,0,6
	.byte	'P5',0,1
	.word	172
	.byte	1,2,2,35,0,6
	.byte	'P6',0,1
	.word	172
	.byte	1,1,2,35,0,6
	.byte	'P7',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'P8',0,1
	.word	172
	.byte	1,7,2,35,1,6
	.byte	'P9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'P10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'P11',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'P12',0,1
	.word	172
	.byte	1,3,2,35,1,6
	.byte	'P13',0,1
	.word	172
	.byte	1,2,2,35,1,6
	.byte	'P14',0,1
	.word	172
	.byte	1,1,2,35,1,6
	.byte	'P15',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'reserved_16',0,2
	.word	189
	.byte	16,0,2,35,2,0,5
	.byte	'B',0,4
	.word	2319
	.byte	2,35,0,0,5
	.byte	'IN',0,4
	.word	2291
	.byte	2,35,36,7,24
	.word	172
	.byte	8,23,0,5
	.byte	'reserved_28',0,24
	.word	2617
	.byte	2,35,40,4,1,135,5,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_PDR0_Bits',0,1,160,3,16,4,6
	.byte	'PD0',0,1
	.word	172
	.byte	3,5,2,35,0,6
	.byte	'PL0',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'PD1',0,1
	.word	172
	.byte	3,1,2,35,0,6
	.byte	'PL1',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'PD2',0,1
	.word	172
	.byte	3,5,2,35,1,6
	.byte	'PL2',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'PD3',0,1
	.word	172
	.byte	3,1,2,35,1,6
	.byte	'PL3',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'PD4',0,1
	.word	172
	.byte	3,5,2,35,2,6
	.byte	'PL4',0,1
	.word	172
	.byte	1,4,2,35,2,6
	.byte	'PD5',0,1
	.word	172
	.byte	3,1,2,35,2,6
	.byte	'PL5',0,1
	.word	172
	.byte	1,0,2,35,2,6
	.byte	'PD6',0,1
	.word	172
	.byte	3,5,2,35,3,6
	.byte	'PL6',0,1
	.word	172
	.byte	1,4,2,35,3,6
	.byte	'PD7',0,1
	.word	172
	.byte	3,1,2,35,3,6
	.byte	'PL7',0,1
	.word	172
	.byte	1,0,2,35,3,0,5
	.byte	'B',0,4
	.word	2675
	.byte	2,35,0,0,5
	.byte	'PDR0',0,4
	.word	2647
	.byte	2,35,64,4,1,143,5,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_PDR1_Bits',0,1,181,3,16,4,6
	.byte	'PD8',0,1
	.word	172
	.byte	3,5,2,35,0,6
	.byte	'PL8',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'PD9',0,1
	.word	172
	.byte	3,1,2,35,0,6
	.byte	'PL9',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'PD10',0,1
	.word	172
	.byte	3,5,2,35,1,6
	.byte	'PL10',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'PD11',0,1
	.word	172
	.byte	3,1,2,35,1,6
	.byte	'PL11',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'PD12',0,1
	.word	172
	.byte	3,5,2,35,2,6
	.byte	'PL12',0,1
	.word	172
	.byte	1,4,2,35,2,6
	.byte	'PD13',0,1
	.word	172
	.byte	3,1,2,35,2,6
	.byte	'PL13',0,1
	.word	172
	.byte	1,0,2,35,2,6
	.byte	'PD14',0,1
	.word	172
	.byte	3,5,2,35,3,6
	.byte	'PL14',0,1
	.word	172
	.byte	1,4,2,35,3,6
	.byte	'PD15',0,1
	.word	172
	.byte	3,1,2,35,3,6
	.byte	'PL15',0,1
	.word	172
	.byte	1,0,2,35,3,0,5
	.byte	'B',0,4
	.word	2993
	.byte	2,35,0,0,5
	.byte	'PDR1',0,4
	.word	2965
	.byte	2,35,68,7,8
	.word	172
	.byte	8,7,0,5
	.byte	'reserved_48',0,8
	.word	3295
	.byte	2,35,72,4,1,223,3,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_ESR_Bits',0,1,88,16,4,6
	.byte	'EN0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'EN1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'EN2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'EN3',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'EN4',0,1
	.word	172
	.byte	1,3,2,35,0,6
	.byte	'EN5',0,1
	.word	172
	.byte	1,2,2,35,0,6
	.byte	'EN6',0,1
	.word	172
	.byte	1,1,2,35,0,6
	.byte	'EN7',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'EN8',0,1
	.word	172
	.byte	1,7,2,35,1,6
	.byte	'EN9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'EN10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'EN11',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'EN12',0,1
	.word	172
	.byte	1,3,2,35,1,6
	.byte	'EN13',0,1
	.word	172
	.byte	1,2,2,35,1,6
	.byte	'EN14',0,1
	.word	172
	.byte	1,1,2,35,1,6
	.byte	'EN15',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'reserved_16',0,2
	.word	189
	.byte	16,0,2,35,2,0,5
	.byte	'B',0,4
	.word	3353
	.byte	2,35,0,0,5
	.byte	'ESR',0,4
	.word	3325
	.byte	2,35,80,7,12
	.word	172
	.byte	8,11,0,5
	.byte	'reserved_54',0,12
	.word	3669
	.byte	2,35,84,4,1,255,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_PDISC_Bits',0,1,138,3,16,4,6
	.byte	'PDIS0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'PDIS1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'PDIS2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'PDIS3',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'PDIS4',0,1
	.word	172
	.byte	1,3,2,35,0,6
	.byte	'PDIS5',0,1
	.word	172
	.byte	1,2,2,35,0,6
	.byte	'PDIS6',0,1
	.word	172
	.byte	1,1,2,35,0,6
	.byte	'PDIS7',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'PDIS8',0,1
	.word	172
	.byte	1,7,2,35,1,6
	.byte	'PDIS9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'PDIS10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'PDIS11',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'PDIS12',0,1
	.word	172
	.byte	1,3,2,35,1,6
	.byte	'PDIS13',0,1
	.word	172
	.byte	1,2,2,35,1,6
	.byte	'PDIS14',0,1
	.word	172
	.byte	1,1,2,35,1,6
	.byte	'PDIS15',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'reserved_16',0,2
	.word	189
	.byte	16,0,2,35,2,0,5
	.byte	'B',0,4
	.word	3727
	.byte	2,35,0,0,5
	.byte	'PDISC',0,4
	.word	3699
	.byte	2,35,96,4,1,247,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_PCSR_Bits',0,1,253,2,16,4,6
	.byte	'reserved_0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'SEL1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'SEL2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'reserved_3',0,2
	.word	189
	.byte	6,7,2,35,0,6
	.byte	'SEL9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'SEL10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'reserved_11',0,4
	.word	252
	.byte	20,1,2,35,2,6
	.byte	'LCK',0,1
	.word	172
	.byte	1,0,2,35,3,0,5
	.byte	'B',0,4
	.word	4108
	.byte	2,35,0,0,5
	.byte	'PCSR',0,4
	.word	4080
	.byte	2,35,100,5
	.byte	'reserved_64',0,8
	.word	3295
	.byte	2,35,104,4,1,207,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMSR0_Bits',0,1,166,2,16,4,6
	.byte	'PS0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'PS1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'PS2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'PS3',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'reserved_4',0,4
	.word	252
	.byte	28,0,2,35,2,0,5
	.byte	'B',0,4
	.word	4354
	.byte	2,35,0,0,5
	.byte	'OMSR0',0,4
	.word	4326
	.byte	2,35,112,4,1,223,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMSR4_Bits',0,1,187,2,16,4,6
	.byte	'reserved_0',0,1
	.word	172
	.byte	4,4,2,35,0,6
	.byte	'PS4',0,1
	.word	172
	.byte	1,3,2,35,0,6
	.byte	'PS5',0,1
	.word	172
	.byte	1,2,2,35,0,6
	.byte	'PS6',0,1
	.word	172
	.byte	1,1,2,35,0,6
	.byte	'PS7',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'reserved_8',0,4
	.word	252
	.byte	24,0,2,35,2,0,5
	.byte	'B',0,4
	.word	4516
	.byte	2,35,0,0,5
	.byte	'OMSR4',0,4
	.word	4488
	.byte	2,35,116,4,1,231,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMSR8_Bits',0,1,198,2,16,4,6
	.byte	'reserved_0',0,1
	.word	172
	.byte	8,0,2,35,0,6
	.byte	'PS8',0,1
	.word	172
	.byte	1,7,2,35,1,6
	.byte	'PS9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'PS10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'PS11',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'reserved_12',0,4
	.word	252
	.byte	20,0,2,35,2,0,5
	.byte	'B',0,4
	.word	4700
	.byte	2,35,0,0,5
	.byte	'OMSR8',0,4
	.word	4672
	.byte	2,35,120,4,1,215,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMSR12_Bits',0,1,176,2,16,4,6
	.byte	'reserved_0',0,2
	.word	189
	.byte	12,4,2,35,0,6
	.byte	'PS12',0,1
	.word	172
	.byte	1,3,2,35,1,6
	.byte	'PS13',0,1
	.word	172
	.byte	1,2,2,35,1,6
	.byte	'PS14',0,1
	.word	172
	.byte	1,1,2,35,1,6
	.byte	'PS15',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'reserved_16',0,2
	.word	189
	.byte	16,0,2,35,2,0,5
	.byte	'B',0,4
	.word	4887
	.byte	2,35,0,0,5
	.byte	'OMSR12',0,4
	.word	4859
	.byte	2,35,124,4,1,159,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMCR0_Bits',0,1,192,1,16,4,6
	.byte	'reserved_0',0,2
	.word	189
	.byte	16,0,2,35,0,6
	.byte	'PCL0',0,1
	.word	172
	.byte	1,7,2,35,2,6
	.byte	'PCL1',0,1
	.word	172
	.byte	1,6,2,35,2,6
	.byte	'PCL2',0,1
	.word	172
	.byte	1,5,2,35,2,6
	.byte	'PCL3',0,1
	.word	172
	.byte	1,4,2,35,2,6
	.byte	'reserved_20',0,2
	.word	189
	.byte	12,0,2,35,2,0,5
	.byte	'B',0,4
	.word	5078
	.byte	2,35,0,0,5
	.byte	'OMCR0',0,4
	.word	5050
	.byte	3,35,128,1,4,1,175,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMCR4_Bits',0,1,213,1,16,4,6
	.byte	'reserved_0',0,4
	.word	252
	.byte	20,12,2,35,2,6
	.byte	'PCL4',0,1
	.word	172
	.byte	1,3,2,35,2,6
	.byte	'PCL5',0,1
	.word	172
	.byte	1,2,2,35,2,6
	.byte	'PCL6',0,1
	.word	172
	.byte	1,1,2,35,2,6
	.byte	'PCL7',0,1
	.word	172
	.byte	1,0,2,35,2,6
	.byte	'reserved_24',0,1
	.word	172
	.byte	8,0,2,35,3,0,5
	.byte	'B',0,4
	.word	5268
	.byte	2,35,0,0,5
	.byte	'OMCR4',0,4
	.word	5240
	.byte	3,35,132,1,4,1,183,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMCR8_Bits',0,1,224,1,16,4,6
	.byte	'reserved_0',0,4
	.word	252
	.byte	24,8,2,35,2,6
	.byte	'PCL8',0,1
	.word	172
	.byte	1,7,2,35,3,6
	.byte	'PCL9',0,1
	.word	172
	.byte	1,6,2,35,3,6
	.byte	'PCL10',0,1
	.word	172
	.byte	1,5,2,35,3,6
	.byte	'PCL11',0,1
	.word	172
	.byte	1,4,2,35,3,6
	.byte	'reserved_28',0,1
	.word	172
	.byte	4,0,2,35,3,0,5
	.byte	'B',0,4
	.word	5458
	.byte	2,35,0,0,5
	.byte	'OMCR8',0,4
	.word	5430
	.byte	3,35,136,1,4,1,167,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMCR12_Bits',0,1,203,1,16,4,6
	.byte	'reserved_0',0,4
	.word	252
	.byte	28,4,2,35,2,6
	.byte	'PCL12',0,1
	.word	172
	.byte	1,3,2,35,3,6
	.byte	'PCL13',0,1
	.word	172
	.byte	1,2,2,35,3,6
	.byte	'PCL14',0,1
	.word	172
	.byte	1,1,2,35,3,6
	.byte	'PCL15',0,1
	.word	172
	.byte	1,0,2,35,3,0,5
	.byte	'B',0,4
	.word	5650
	.byte	2,35,0,0,5
	.byte	'OMCR12',0,4
	.word	5622
	.byte	3,35,140,1,4,1,199,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMSR_Bits',0,1,209,2,16,4,6
	.byte	'PS0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'PS1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'PS2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'PS3',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'PS4',0,1
	.word	172
	.byte	1,3,2,35,0,6
	.byte	'PS5',0,1
	.word	172
	.byte	1,2,2,35,0,6
	.byte	'PS6',0,1
	.word	172
	.byte	1,1,2,35,0,6
	.byte	'PS7',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'PS8',0,1
	.word	172
	.byte	1,7,2,35,1,6
	.byte	'PS9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'PS10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'PS11',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'PS12',0,1
	.word	172
	.byte	1,3,2,35,1,6
	.byte	'PS13',0,1
	.word	172
	.byte	1,2,2,35,1,6
	.byte	'PS14',0,1
	.word	172
	.byte	1,1,2,35,1,6
	.byte	'PS15',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'reserved_16',0,2
	.word	189
	.byte	16,0,2,35,2,0,5
	.byte	'B',0,4
	.word	5823
	.byte	2,35,0,0,5
	.byte	'OMSR',0,4
	.word	5795
	.byte	3,35,144,1,4,1,151,4,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_OMCR_Bits',0,1,235,1,16,4,6
	.byte	'reserved_0',0,2
	.word	189
	.byte	16,0,2,35,0,6
	.byte	'PCL0',0,1
	.word	172
	.byte	1,7,2,35,2,6
	.byte	'PCL1',0,1
	.word	172
	.byte	1,6,2,35,2,6
	.byte	'PCL2',0,1
	.word	172
	.byte	1,5,2,35,2,6
	.byte	'PCL3',0,1
	.word	172
	.byte	1,4,2,35,2,6
	.byte	'PCL4',0,1
	.word	172
	.byte	1,3,2,35,2,6
	.byte	'PCL5',0,1
	.word	172
	.byte	1,2,2,35,2,6
	.byte	'PCL6',0,1
	.word	172
	.byte	1,1,2,35,2,6
	.byte	'PCL7',0,1
	.word	172
	.byte	1,0,2,35,2,6
	.byte	'PCL8',0,1
	.word	172
	.byte	1,7,2,35,3,6
	.byte	'PCL9',0,1
	.word	172
	.byte	1,6,2,35,3,6
	.byte	'PCL10',0,1
	.word	172
	.byte	1,5,2,35,3,6
	.byte	'PCL11',0,1
	.word	172
	.byte	1,4,2,35,3,6
	.byte	'PCL12',0,1
	.word	172
	.byte	1,3,2,35,3,6
	.byte	'PCL13',0,1
	.word	172
	.byte	1,2,2,35,3,6
	.byte	'PCL14',0,1
	.word	172
	.byte	1,1,2,35,3,6
	.byte	'PCL15',0,1
	.word	172
	.byte	1,0,2,35,3,0,5
	.byte	'B',0,4
	.word	6171
	.byte	2,35,0,0,5
	.byte	'OMCR',0,4
	.word	6143
	.byte	3,35,148,1,7,96
	.word	172
	.byte	8,95,0,5
	.byte	'reserved_98',0,96
	.word	6506
	.byte	3,35,152,1,4,1,215,3,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_ACCEN1_Bits',0,1,82,16,4,6
	.byte	'reserved_0',0,4
	.word	252
	.byte	32,0,2,35,2,0,5
	.byte	'B',0,4
	.word	6565
	.byte	2,35,0,0,5
	.byte	'ACCEN1',0,4
	.word	6537
	.byte	3,35,248,1,4,1,207,3,9,4,5
	.byte	'U',0,4
	.word	252
	.byte	2,35,0,5
	.byte	'I',0,4
	.word	279
	.byte	2,35,0,3
	.byte	'_Ifx_P_ACCEN0_Bits',0,1,45,16,4,6
	.byte	'EN0',0,1
	.word	172
	.byte	1,7,2,35,0,6
	.byte	'EN1',0,1
	.word	172
	.byte	1,6,2,35,0,6
	.byte	'EN2',0,1
	.word	172
	.byte	1,5,2,35,0,6
	.byte	'EN3',0,1
	.word	172
	.byte	1,4,2,35,0,6
	.byte	'EN4',0,1
	.word	172
	.byte	1,3,2,35,0,6
	.byte	'EN5',0,1
	.word	172
	.byte	1,2,2,35,0,6
	.byte	'EN6',0,1
	.word	172
	.byte	1,1,2,35,0,6
	.byte	'EN7',0,1
	.word	172
	.byte	1,0,2,35,0,6
	.byte	'EN8',0,1
	.word	172
	.byte	1,7,2,35,1,6
	.byte	'EN9',0,1
	.word	172
	.byte	1,6,2,35,1,6
	.byte	'EN10',0,1
	.word	172
	.byte	1,5,2,35,1,6
	.byte	'EN11',0,1
	.word	172
	.byte	1,4,2,35,1,6
	.byte	'EN12',0,1
	.word	172
	.byte	1,3,2,35,1,6
	.byte	'EN13',0,1
	.word	172
	.byte	1,2,2,35,1,6
	.byte	'EN14',0,1
	.word	172
	.byte	1,1,2,35,1,6
	.byte	'EN15',0,1
	.word	172
	.byte	1,0,2,35,1,6
	.byte	'EN16',0,1
	.word	172
	.byte	1,7,2,35,2,6
	.byte	'EN17',0,1
	.word	172
	.byte	1,6,2,35,2,6
	.byte	'EN18',0,1
	.word	172
	.byte	1,5,2,35,2,6
	.byte	'EN19',0,1
	.word	172
	.byte	1,4,2,35,2,6
	.byte	'EN20',0,1
	.word	172
	.byte	1,3,2,35,2,6
	.byte	'EN21',0,1
	.word	172
	.byte	1,2,2,35,2,6
	.byte	'EN22',0,1
	.word	172
	.byte	1,1,2,35,2,6
	.byte	'EN23',0,1
	.word	172
	.byte	1,0,2,35,2,6
	.byte	'EN24',0,1
	.word	172
	.byte	1,7,2,35,3,6
	.byte	'EN25',0,1
	.word	172
	.byte	1,6,2,35,3,6
	.byte	'EN26',0,1
	.word	172
	.byte	1,5,2,35,3,6
	.byte	'EN27',0,1
	.word	172
	.byte	1,4,2,35,3,6
	.byte	'EN28',0,1
	.word	172
	.byte	1,3,2,35,3,6
	.byte	'EN29',0,1
	.word	172
	.byte	1,2,2,35,3,6
	.byte	'EN30',0,1
	.word	172
	.byte	1,1,2,35,3,6
	.byte	'EN31',0,1
	.word	172
	.byte	1,0,2,35,3,0,5
	.byte	'B',0,4
	.word	6669
	.byte	2,35,0,0,5
	.byte	'ACCEN0',0,4
	.word	6641
	.byte	3,35,252,1,0,9
	.word	232
.L86:
	.byte	10
	.word	7226
	.byte	3
	.byte	'Dio_ChannelGroupType',0,2,129,2,16,4,5
	.byte	'mask',0,2
	.word	189
	.byte	2,35,0,5
	.byte	'offset',0,1
	.word	172
	.byte	2,35,2,5
	.byte	'port',0,1
	.word	172
	.byte	2,35,3,0,11
	.word	7236
.L102:
	.byte	10
	.word	7308
	.byte	12
	.byte	'void',0,10
	.word	7318
	.byte	13
	.byte	'__prof_adm',0,3,1,1
	.word	7324
	.byte	14,1,10
	.word	7348
	.byte	13
	.byte	'__codeptr',0,3,1,1
	.word	7350
	.byte	13
	.byte	'uint8',0,4,90,29
	.word	172
	.byte	13
	.byte	'uint16',0,4,92,29
	.word	189
	.byte	13
	.byte	'uint32',0,4,94,29
	.word	211
	.byte	13
	.byte	'Ifx_P_ACCEN0_Bits',0,1,79,3
	.word	6669
	.byte	13
	.byte	'Ifx_P_ACCEN1_Bits',0,1,85,3
	.word	6565
	.byte	13
	.byte	'Ifx_P_ESR_Bits',0,1,107,3
	.word	3353
	.byte	13
	.byte	'Ifx_P_ID_Bits',0,1,115,3
	.word	1210
	.byte	13
	.byte	'Ifx_P_IN_Bits',0,1,137,1,3
	.word	2319
	.byte	13
	.byte	'Ifx_P_IOCR0_Bits',0,1,150,1,3
	.word	1370
	.byte	13
	.byte	'Ifx_P_IOCR12_Bits',0,1,163,1,3
	.word	2062
	.byte	13
	.byte	'Ifx_P_IOCR4_Bits',0,1,176,1,3
	.word	1600
	.byte	13
	.byte	'Ifx_P_IOCR8_Bits',0,1,189,1,3
	.word	1830
	.byte	13
	.byte	'Ifx_P_OMCR0_Bits',0,1,200,1,3
	.word	5078
	.byte	13
	.byte	'Ifx_P_OMCR12_Bits',0,1,210,1,3
	.word	5650
	.byte	13
	.byte	'Ifx_P_OMCR4_Bits',0,1,221,1,3
	.word	5268
	.byte	13
	.byte	'Ifx_P_OMCR8_Bits',0,1,232,1,3
	.word	5458
	.byte	13
	.byte	'Ifx_P_OMCR_Bits',0,1,254,1,3
	.word	6171
	.byte	13
	.byte	'Ifx_P_OMR_Bits',0,1,163,2,3
	.word	626
	.byte	13
	.byte	'Ifx_P_OMSR0_Bits',0,1,173,2,3
	.word	4354
	.byte	13
	.byte	'Ifx_P_OMSR12_Bits',0,1,184,2,3
	.word	4887
	.byte	13
	.byte	'Ifx_P_OMSR4_Bits',0,1,195,2,3
	.word	4516
	.byte	13
	.byte	'Ifx_P_OMSR8_Bits',0,1,206,2,3
	.word	4700
	.byte	13
	.byte	'Ifx_P_OMSR_Bits',0,1,228,2,3
	.word	5823
	.byte	13
	.byte	'Ifx_P_OUT_Bits',0,1,250,2,3
	.word	297
	.byte	13
	.byte	'Ifx_P_PCSR_Bits',0,1,135,3,3
	.word	4108
	.byte	13
	.byte	'Ifx_P_PDISC_Bits',0,1,157,3,3
	.word	3727
	.byte	13
	.byte	'Ifx_P_PDR0_Bits',0,1,178,3,3
	.word	2675
	.byte	13
	.byte	'Ifx_P_PDR1_Bits',0,1,199,3,3
	.word	2993
	.byte	13
	.byte	'Ifx_P_ACCEN0',0,1,212,3,3
	.word	6641
	.byte	13
	.byte	'Ifx_P_ACCEN1',0,1,220,3,3
	.word	6537
	.byte	13
	.byte	'Ifx_P_ESR',0,1,228,3,3
	.word	3325
	.byte	13
	.byte	'Ifx_P_ID',0,1,236,3,3
	.word	1182
	.byte	13
	.byte	'Ifx_P_IN',0,1,244,3,3
	.word	2291
	.byte	13
	.byte	'Ifx_P_IOCR0',0,1,252,3,3
	.word	1342
	.byte	13
	.byte	'Ifx_P_IOCR12',0,1,132,4,3
	.word	2034
	.byte	13
	.byte	'Ifx_P_IOCR4',0,1,140,4,3
	.word	1572
	.byte	13
	.byte	'Ifx_P_IOCR8',0,1,148,4,3
	.word	1802
	.byte	13
	.byte	'Ifx_P_OMCR',0,1,156,4,3
	.word	6143
	.byte	13
	.byte	'Ifx_P_OMCR0',0,1,164,4,3
	.word	5050
	.byte	13
	.byte	'Ifx_P_OMCR12',0,1,172,4,3
	.word	5622
	.byte	13
	.byte	'Ifx_P_OMCR4',0,1,180,4,3
	.word	5240
	.byte	13
	.byte	'Ifx_P_OMCR8',0,1,188,4,3
	.word	5430
	.byte	13
	.byte	'Ifx_P_OMR',0,1,196,4,3
	.word	598
	.byte	13
	.byte	'Ifx_P_OMSR',0,1,204,4,3
	.word	5795
	.byte	13
	.byte	'Ifx_P_OMSR0',0,1,212,4,3
	.word	4326
	.byte	13
	.byte	'Ifx_P_OMSR12',0,1,220,4,3
	.word	4859
	.byte	13
	.byte	'Ifx_P_OMSR4',0,1,228,4,3
	.word	4488
	.byte	13
	.byte	'Ifx_P_OMSR8',0,1,236,4,3
	.word	4672
	.byte	13
	.byte	'Ifx_P_OUT',0,1,244,4,3
	.word	246
	.byte	13
	.byte	'Ifx_P_PCSR',0,1,252,4,3
	.word	4080
	.byte	13
	.byte	'Ifx_P_PDISC',0,1,132,5,3
	.word	3699
	.byte	13
	.byte	'Ifx_P_PDR0',0,1,140,5,3
	.word	2647
	.byte	13
	.byte	'Ifx_P_PDR1',0,1,148,5,3
	.word	2965
	.byte	9
	.word	232
	.byte	13
	.byte	'Ifx_P',0,1,193,5,3
	.word	8564
	.byte	13
	.byte	'Dio_LevelType',0,2,237,1,17
	.word	172
	.byte	13
	.byte	'Dio_ChannelType',0,2,243,1,17
	.word	189
	.byte	13
	.byte	'Dio_PortType',0,2,249,1,17
	.word	172
	.byte	13
	.byte	'Dio_PortLevelType',0,2,252,1,17
	.word	189
	.byte	13
	.byte	'Dio_ChannelGroupType',0,2,137,2,3
	.word	7236
	.byte	3
	.byte	'Dio_PortChannelIdType',0,2,139,2,16,8,5
	.byte	'Dio_PortIdConfig',0,4
	.word	211
	.byte	2,35,0,5
	.byte	'Dio_ChannelConfig',0,4
	.word	211
	.byte	2,35,4,0,13
	.byte	'Dio_PortChannelIdType',0,2,145,2,2
	.word	8711
	.byte	7,84
	.word	189
	.byte	8,41,0
.L118:
	.byte	11
	.word	8824
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L30:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,3,8,58,15,59,15,57,15
	.byte	11,15,0,0,4,23,1,58,15,59,15,57,15,11,15,0,0,5,13,0,3,8,11,15,73,19,56,9,0,0,6,13,0,3,8,11,15,73,19,13
	.byte	15,12,15,56,9,0,0,7,1,1,11,15,73,19,0,0,8,33,0,47,15,0,0,9,53,0,73,19,0,0,10,15,0,73,19,0,0,11,38,0,73
	.byte	19,0,0,12,59,0,3,8,0,0,13,22,0,3,8,58,15,59,15,57,15,73,19,0,0,14,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L31:
	.word	.L147-.L146
.L146:
	.half	3
	.word	.L149-.L148
.L148:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\IfxPort_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Dio.h',0,0,0,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0,0
.L149:
.L147:
	.sdecl	'.debug_info',debug,cluster('Dio_ReadChannel')
	.sect	'.debug_info'
.L32:
	.word	328
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L35,.L34
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_ReadChannel',0,1,128,7,15
	.word	.L79
	.byte	1,1,1
	.word	.L15,.L80,.L14
	.byte	4
	.byte	'ChannelId',0,1,128,7,47
	.word	.L81,.L82
	.byte	5
	.word	.L15,.L80
	.byte	6
	.byte	'PinPosition',0,1,130,7,17
	.word	.L83,.L84
	.byte	6
	.byte	'RetVal',0,1,131,7,17
	.word	.L79,.L85
	.byte	6
	.byte	'GetPortAddressPtr',0,1,132,7,10
	.word	.L86,.L87
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_ReadChannel')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_ReadChannel')
	.sect	'.debug_line'
.L34:
	.word	.L151-.L150
.L150:
	.half	3
	.word	.L153-.L152
.L152:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L153:
	.byte	5,15,7,0,5,2
	.word	.L15
	.byte	3,255,6,1,5,10,9
	.half	.L126-.L15
	.byte	3,7,1,5,60,3,11,1,5,59,9
	.half	.L125-.L126
	.byte	1,5,55,9
	.half	.L128-.L125
	.byte	3,3,1,5,23,3,125,1,5,20,9
	.half	.L130-.L128
	.byte	3,3,1,5,55,1,5,34,9
	.half	.L129-.L130
	.byte	1,5,26,3,3,1,5,23,9
	.half	.L127-.L129
	.byte	1,5,14,9
	.half	.L131-.L127
	.byte	3,3,1,5,1,3,4,1,7,9
	.half	.L36-.L131
	.byte	0,1,1
.L151:
	.sdecl	'.debug_ranges',debug,cluster('Dio_ReadChannel')
	.sect	'.debug_ranges'
.L35:
	.word	-1,.L15,0,.L36-.L15,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_WriteChannel')
	.sect	'.debug_info'
.L37:
	.word	319
	.half	3
	.word	.L38
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L40,.L39
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_WriteChannel',0,1,197,7,6,1,1,1
	.word	.L17,.L88,.L16
	.byte	4
	.byte	'ChannelId',0,1,197,7,39
	.word	.L81,.L89
	.byte	4
	.byte	'Level',0,1,197,7,64
	.word	.L79,.L90
	.byte	5
	.word	.L17,.L88
	.byte	6
	.byte	'GetPortAddressPtr',0,1,204,7,19
	.word	.L86,.L91
	.byte	6
	.byte	'OmrVal',0,1,205,7,10
	.word	.L83,.L92
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_WriteChannel')
	.sect	'.debug_abbrev'
.L38:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_WriteChannel')
	.sect	'.debug_line'
.L39:
	.word	.L155-.L154
.L154:
	.half	3
	.word	.L157-.L156
.L156:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L157:
	.byte	5,6,7,0,5,2
	.word	.L17
	.byte	3,196,7,1,5,12,9
	.half	.L133-.L17
	.byte	3,10,1,5,16,9
	.half	.L134-.L133
	.byte	3,16,1,5,62,3,3,1,5,61,9
	.half	.L132-.L134
	.byte	1,5,25,9
	.half	.L136-.L132
	.byte	1,5,7,9
	.half	.L138-.L136
	.byte	3,3,1,5,1,9
	.half	.L135-.L138
	.byte	3,14,1,7,9
	.half	.L41-.L135
	.byte	0,1,1
.L155:
	.sdecl	'.debug_ranges',debug,cluster('Dio_WriteChannel')
	.sect	'.debug_ranges'
.L40:
	.word	-1,.L17,0,.L41-.L17,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_ReadPort')
	.sect	'.debug_info'
.L42:
	.word	297
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L45,.L44
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_ReadPort',0,1,144,8,19
	.word	.L81
	.byte	1,1,1
	.word	.L19,.L93,.L18
	.byte	4
	.byte	'PortId',0,1,144,8,45
	.word	.L79,.L94
	.byte	5
	.word	.L19,.L93
	.byte	6
	.byte	'GetPortAddressPtr',0,1,146,8,22
	.word	.L86,.L95
	.byte	6
	.byte	'RetVal',0,1,147,8,22
	.word	.L81,.L96
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_ReadPort')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_ReadPort')
	.sect	'.debug_line'
.L44:
	.word	.L159-.L158
.L158:
	.half	3
	.word	.L161-.L160
.L160:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L161:
	.byte	5,19,7,0,5,2
	.word	.L19
	.byte	3,143,8,1,5,43,3,21,1,9
	.half	.L139-.L19
	.byte	3,2,1,5,36,9
	.half	.L162-.L139
	.byte	3,127,1,5,43,9
	.half	.L163-.L162
	.byte	3,1,1,5,69,9
	.half	.L164-.L163
	.byte	1,5,17,9
	.half	.L165-.L164
	.byte	3,127,1,5,69,9
	.half	.L166-.L165
	.byte	3,1,1,5,41,9
	.half	.L140-.L166
	.byte	1,5,1,3,3,1,7,9
	.half	.L46-.L140
	.byte	0,1,1
.L159:
	.sdecl	'.debug_ranges',debug,cluster('Dio_ReadPort')
	.sect	'.debug_ranges'
.L45:
	.word	-1,.L19,0,.L46-.L19,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_WritePort')
	.sect	'.debug_info'
.L47:
	.word	293
	.half	3
	.word	.L48
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L50,.L49
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_WritePort',0,1,201,8,6,1,1,1
	.word	.L21,.L97,.L20
	.byte	4
	.byte	'PortId',0,1,201,8,34
	.word	.L79,.L98
	.byte	4
	.byte	'Level',0,1,201,8,60
	.word	.L81,.L99
	.byte	5
	.word	.L21,.L97
	.byte	6
	.byte	'GetPortAddressPtr',0,1,208,8,19
	.word	.L86,.L100
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_WritePort')
	.sect	'.debug_abbrev'
.L48:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_WritePort')
	.sect	'.debug_line'
.L49:
	.word	.L168-.L167
.L167:
	.half	3
	.word	.L170-.L169
.L169:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L170:
	.byte	5,6,7,0,5,2
	.word	.L21
	.byte	3,200,8,1,5,43,3,26,1,5,7,9
	.half	.L141-.L21
	.byte	3,2,1,5,1,9
	.half	.L171-.L141
	.byte	3,15,1,7,9
	.half	.L51-.L171
	.byte	0,1,1
.L168:
	.sdecl	'.debug_ranges',debug,cluster('Dio_WritePort')
	.sect	'.debug_ranges'
.L50:
	.word	-1,.L21,0,.L51-.L21,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_ReadChannelGroup')
	.sect	'.debug_info'
.L52:
	.word	316
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L55,.L54
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_ReadChannelGroup',0,1,245,5,19
	.word	.L81
	.byte	1,1,1
	.word	.L11,.L101,.L10
	.byte	4
	.byte	'ChannelGroupIdPtr',0,1,247,5,31
	.word	.L102,.L103
	.byte	5
	.word	.L11,.L101
	.byte	6
	.byte	'GetPortAddressPtr',0,1,250,5,22
	.word	.L86,.L104
	.byte	6
	.byte	'RetVal',0,1,251,5,22
	.word	.L81,.L105
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_ReadChannelGroup')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_ReadChannelGroup')
	.sect	'.debug_line'
.L54:
	.word	.L173-.L172
.L172:
	.half	3
	.word	.L175-.L174
.L174:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L175:
	.byte	5,19,7,0,5,2
	.word	.L11
	.byte	3,244,5,1,5,59,9
	.half	.L120-.L11
	.byte	3,22,1,5,35,9
	.half	.L119-.L120
	.byte	3,1,1,5,72,9
	.half	.L176-.L119
	.byte	3,1,1,5,45,9
	.half	.L177-.L176
	.byte	1,5,43,3,3,1,5,23,9
	.half	.L178-.L177
	.byte	1,5,1,9
	.half	.L179-.L178
	.byte	3,3,1,7,9
	.half	.L56-.L179
	.byte	0,1,1
.L173:
	.sdecl	'.debug_ranges',debug,cluster('Dio_ReadChannelGroup')
	.sect	'.debug_ranges'
.L55:
	.word	-1,.L11,0,.L56-.L11,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_WriteChannelGroup')
	.sect	'.debug_info'
.L57:
	.word	333
	.half	3
	.word	.L58
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L60,.L59
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_WriteChannelGroup',0,1,183,6,6,1,1,1
	.word	.L13,.L106,.L12
	.byte	4
	.byte	'ChannelGroupIdPtr',0,1,185,6,31
	.word	.L102,.L107
	.byte	4
	.byte	'Level',0,1,186,6,21
	.word	.L81,.L108
	.byte	5
	.word	.L13,.L106
	.byte	6
	.byte	'GetPortAddressPtr',0,1,194,6,19
	.word	.L86,.L109
	.byte	6
	.byte	'PortVal',0,1,195,6,19
	.word	.L83,.L110
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_WriteChannelGroup')
	.sect	'.debug_abbrev'
.L58:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_WriteChannelGroup')
	.sect	'.debug_line'
.L59:
	.word	.L181-.L180
.L180:
	.half	3
	.word	.L183-.L182
.L182:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L183:
	.byte	5,59,7,0,5,2
	.word	.L13
	.byte	3,206,6,1,5,65,9
	.half	.L184-.L13
	.byte	3,1,1,5,39,9
	.half	.L185-.L184
	.byte	3,127,1,5,69,9
	.half	.L122-.L185
	.byte	1,5,22,9
	.half	.L186-.L122
	.byte	3,4,1,5,32,9
	.half	.L187-.L186
	.byte	1,5,40,9
	.half	.L188-.L187
	.byte	3,2,1,5,24,9
	.half	.L124-.L188
	.byte	1,5,58,3,4,1,5,5,9
	.half	.L121-.L124
	.byte	3,5,1,5,1,9
	.half	.L189-.L121
	.byte	3,2,1,7,9
	.half	.L61-.L189
	.byte	0,1,1
.L181:
	.sdecl	'.debug_ranges',debug,cluster('Dio_WriteChannelGroup')
	.sect	'.debug_ranges'
.L60:
	.word	-1,.L13,0,.L61-.L13,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_lGetPortNumber')
	.sect	'.debug_info'
.L62:
	.word	254
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L65,.L64
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_lGetPortNumber',0,1,252,10,14
	.word	.L79
	.byte	1,1,1
	.word	.L23,.L111,.L22
	.byte	4
	.byte	'ChannelId',0,1,252,10,49
	.word	.L81,.L112
	.byte	5
	.word	.L23,.L111
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_lGetPortNumber')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_lGetPortNumber')
	.sect	'.debug_line'
.L64:
	.word	.L191-.L190
.L190:
	.half	3
	.word	.L193-.L192
.L192:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L193:
	.byte	5,20,7,0,5,2
	.word	.L23
	.byte	3,128,11,1,5,38,9
	.half	.L194-.L23
	.byte	3,127,1,5,46,9
	.half	.L142-.L194
	.byte	3,1,1,5,1,3,3,1,7,9
	.half	.L66-.L142
	.byte	0,1,1
.L191:
	.sdecl	'.debug_ranges',debug,cluster('Dio_lGetPortNumber')
	.sect	'.debug_ranges'
.L65:
	.word	-1,.L23,0,.L66-.L23,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_lGetPortAdr')
	.sect	'.debug_info'
.L67:
	.word	275
	.half	3
	.word	.L68
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L70,.L69
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_lGetPortAdr',0,1,156,11,8
	.word	.L86
	.byte	1,1,1
	.word	.L25,.L113,.L24
	.byte	4
	.byte	'PortNumber',0,1,156,11,37
	.word	.L79,.L114
	.byte	5
	.word	.L25,.L113
	.byte	6
	.byte	'Quotient',0,1,159,11,10
	.word	.L79,.L115
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_lGetPortAdr')
	.sect	'.debug_abbrev'
.L68:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_lGetPortAdr')
	.sect	'.debug_line'
.L69:
	.word	.L196-.L195
.L195:
	.half	3
	.word	.L198-.L197
.L197:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L198:
	.byte	5,41,7,0,5,2
	.word	.L25
	.byte	3,207,11,1,5,14,3,5,1,5,76,9
	.half	.L199-.L25
	.byte	3,123,1,5,31,9
	.half	.L144-.L199
	.byte	3,6,1,5,15,9
	.half	.L200-.L144
	.byte	1,5,31,9
	.half	.L201-.L200
	.byte	3,1,1,5,68,9
	.half	.L202-.L201
	.byte	1,5,50,9
	.half	.L145-.L202
	.byte	1,5,63,9
	.half	.L203-.L145
	.byte	3,127,1,5,49,9
	.half	.L143-.L203
	.byte	1,5,41,9
	.half	.L204-.L143
	.byte	3,127,1,5,1,9
	.half	.L205-.L204
	.byte	3,6,1,7,9
	.half	.L71-.L205
	.byte	0,1,1
.L196:
	.sdecl	'.debug_ranges',debug,cluster('Dio_lGetPortAdr')
	.sect	'.debug_ranges'
.L70:
	.word	-1,.L25,0,.L71-.L25,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_lGetPinNumber')
	.sect	'.debug_info'
.L72:
	.word	253
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L75,.L74
	.byte	2
	.word	.L28
	.byte	3
	.byte	'Dio_lGetPinNumber',0,1,243,11,7
	.word	.L79
	.byte	1,1,1
	.word	.L27,.L116,.L26
	.byte	4
	.byte	'ChannelId',0,1,243,11,41
	.word	.L81,.L117
	.byte	5
	.word	.L27,.L116
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_lGetPinNumber')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Dio_lGetPinNumber')
	.sect	'.debug_line'
.L74:
	.word	.L207-.L206
.L206:
	.half	3
	.word	.L209-.L208
.L208:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Dio.c',0,0,0,0,0
.L209:
	.byte	5,20,7,0,5,2
	.word	.L27
	.byte	3,246,11,1,5,30,9
	.half	.L210-.L27
	.byte	1,5,1,3,2,1,7,9
	.half	.L76-.L210
	.byte	0,1,1
.L207:
	.sdecl	'.debug_ranges',debug,cluster('Dio_lGetPinNumber')
	.sect	'.debug_ranges'
.L75:
	.word	-1,.L27,0,.L76-.L27,0,0
	.sdecl	'.debug_info',debug,cluster('Dio_kMaskUndefinedPortPins')
	.sect	'.debug_info'
.L77:
	.word	212
	.half	3
	.word	.L78
	.byte	4,1
	.byte	'..\\mcal_src\\Dio.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L28
	.byte	3
	.byte	'Dio_kMaskUndefinedPortPins',0,3,251,1,32
	.word	.L118
	.byte	5,3
	.word	Dio_kMaskUndefinedPortPins
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Dio_kMaskUndefinedPortPins')
	.sect	'.debug_abbrev'
.L78:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_ReadChannel')
	.sect	'.debug_loc'
.L82:
	.word	-1,.L15,0,.L125-.L15
	.half	5
	.byte	144,34,157,32,0
	.word	.L126-.L15,.L127-.L15
	.half	5
	.byte	144,39,157,32,32
	.word	.L130-.L15,.L129-.L15
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L14:
	.word	-1,.L15,0,.L80-.L15
	.half	2
	.byte	138,0
	.word	0,0
.L87:
	.word	-1,.L15,.L128-.L15,.L129-.L15
	.half	1
	.byte	98
	.word	.L130-.L15,.L80-.L15
	.half	1
	.byte	111
	.word	0,0
.L84:
	.word	-1,.L15,.L127-.L15,.L131-.L15
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L85:
	.word	-1,.L15,.L125-.L15,.L80-.L15
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_ReadChannelGroup')
	.sect	'.debug_loc'
.L103:
	.word	-1,.L11,0,.L119-.L11
	.half	1
	.byte	100
	.word	.L120-.L11,.L101-.L11
	.half	1
	.byte	111
	.word	0,0
.L10:
	.word	-1,.L11,0,.L101-.L11
	.half	2
	.byte	138,0
	.word	0,0
.L104:
	.word	-1,.L11,.L119-.L11,.L101-.L11
	.half	1
	.byte	98
	.word	0,0
.L105:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_ReadPort')
	.sect	'.debug_loc'
.L18:
	.word	-1,.L19,0,.L93-.L19
	.half	2
	.byte	138,0
	.word	0,0
.L95:
	.word	-1,.L19,.L139-.L19,.L93-.L19
	.half	1
	.byte	98
	.word	0,0
.L94:
	.word	-1,.L19,0,.L139-.L19
	.half	5
	.byte	144,34,157,32,0
	.word	.L139-.L19,.L93-.L19
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L96:
	.word	-1,.L19,.L140-.L19,.L93-.L19
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_WriteChannel')
	.sect	'.debug_loc'
.L89:
	.word	-1,.L17,0,.L132-.L17
	.half	5
	.byte	144,34,157,32,0
	.word	.L133-.L17,.L88-.L17
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L16:
	.word	-1,.L17,0,.L88-.L17
	.half	2
	.byte	138,0
	.word	0,0
.L91:
	.word	-1,.L17,.L136-.L17,.L137-.L17
	.half	1
	.byte	98
	.word	.L138-.L17,.L88-.L17
	.half	1
	.byte	111
	.word	0,0
.L90:
	.word	-1,.L17,0,.L132-.L17
	.half	5
	.byte	144,34,157,32,32
	.word	0,0
.L92:
	.word	-1,.L17,.L134-.L17,.L135-.L17
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_WriteChannelGroup')
	.sect	'.debug_loc'
.L107:
	.word	-1,.L13,0,.L121-.L13
	.half	1
	.byte	100
	.word	0,0
.L12:
	.word	-1,.L13,0,.L106-.L13
	.half	2
	.byte	138,0
	.word	0,0
.L109:
	.word	-1,.L13,.L121-.L13,.L106-.L13
	.half	1
	.byte	98
	.word	0,0
.L108:
	.word	-1,.L13,0,.L122-.L13
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L110:
	.word	-1,.L13,.L122-.L13,.L123-.L13
	.half	5
	.byte	144,34,157,32,0
	.word	.L124-.L13,.L106-.L13
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_WritePort')
	.sect	'.debug_loc'
.L20:
	.word	-1,.L21,0,.L97-.L21
	.half	2
	.byte	138,0
	.word	0,0
.L100:
	.word	-1,.L21,.L141-.L21,.L97-.L21
	.half	1
	.byte	98
	.word	0,0
.L99:
	.word	-1,.L21,0,.L141-.L21
	.half	5
	.byte	144,34,157,32,32
	.word	.L141-.L21,.L97-.L21
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L98:
	.word	-1,.L21,0,.L141-.L21
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_lGetPinNumber')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L27,0,.L116-.L27
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L26:
	.word	-1,.L27,0,.L116-.L27
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_lGetPortAdr')
	.sect	'.debug_loc'
.L24:
	.word	-1,.L25,0,.L113-.L25
	.half	2
	.byte	138,0
	.word	0,0
.L114:
	.word	-1,.L25,0,.L143-.L25
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L115:
	.word	-1,.L25,.L144-.L25,.L145-.L25
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Dio_lGetPortNumber')
	.sect	'.debug_loc'
.L112:
	.word	-1,.L23,0,.L142-.L23
	.half	5
	.byte	144,34,157,32,0
	.word	0,0
.L22:
	.word	-1,.L23,0,.L111-.L23
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L211:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Dio_ReadChannelGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L211,.L11,.L101-.L11
	.sdecl	'.debug_frame',debug,cluster('Dio_WriteChannelGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L211,.L13,.L106-.L13
	.sdecl	'.debug_frame',debug,cluster('Dio_ReadChannel')
	.sect	'.debug_frame'
	.word	12
	.word	.L211,.L15,.L80-.L15
	.sdecl	'.debug_frame',debug,cluster('Dio_WriteChannel')
	.sect	'.debug_frame'
	.word	12
	.word	.L211,.L17,.L88-.L17
	.sdecl	'.debug_frame',debug,cluster('Dio_ReadPort')
	.sect	'.debug_frame'
	.word	12
	.word	.L211,.L19,.L93-.L19
	.sdecl	'.debug_frame',debug,cluster('Dio_WritePort')
	.sect	'.debug_frame'
	.word	12
	.word	.L211,.L21,.L97-.L21
	.sdecl	'.debug_frame',debug,cluster('Dio_lGetPortNumber')
	.sect	'.debug_frame'
	.word	24
	.word	.L211,.L23,.L111-.L23
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Dio_lGetPortAdr')
	.sect	'.debug_frame'
	.word	24
	.word	.L211,.L25,.L113-.L25
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Dio_lGetPinNumber')
	.sect	'.debug_frame'
	.word	24
	.word	.L211,.L27,.L116-.L27
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Dio.c	  1530  
; ..\mcal_src\Dio.c	  1531  /*Memory Map of the DIO Code*/
; ..\mcal_src\Dio.c	  1532  #define DIO_STOP_SEC_CODE
; ..\mcal_src\Dio.c	  1533  /*mapping of code and data to specific memory sections via memory mapping file*/
; ..\mcal_src\Dio.c	  1534  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives
; ..\mcal_src\Dio.c	  1535    is allowed only for MemMap.h*/
; ..\mcal_src\Dio.c	  1536  #include "MemMap.h"

	; Module end
