"-o"
"eeprom\CallOutFunction.o"
"..\eeprom\CallOutFunction.c"
"-Cuserdef16x"
"-t"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot"
"-Wa-Hsfr/reguserdef16x.def"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\flash\flsloader"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\Crc"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\Fee"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\Fls"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\MemIf"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\NvM"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\flash"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\uds"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\wdg"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_cfg"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\dma_infineon_tricore"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\dma_infineon_tricore\inc"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\dma_infineon_tricore\src"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\integration_general"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\integration_general\inc"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\integration_general\src"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\spi_infineon_tricore"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\spi_infineon_tricore\inc"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\spi_infineon_tricore\src"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\Src_file"
"-I"
"-I"
"-I"
"-I"
"-I"
"-I"
"-Wa-gAHLs"
"--emit-locals=-equ,-symbols"
"-Wa-Ogs"
"-Wa--error-limit=42"
"-D_TASKING_C_TRICORE_=1"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\MCU"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\flash\flsloader"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\Crc"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\Fee"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\Fls"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\MemIf"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom\NvM"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\eeprom"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\flash"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\uds"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\wdg"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\Secure"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\Vss"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\vss_code"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_cfg"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\dma_infineon_tricore"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\dma_infineon_tricore\inc"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\dma_infineon_tricore\src"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\integration_general"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\integration_general\inc"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\integration_general\src"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\spi_infineon_tricore"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\spi_infineon_tricore\inc"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\mcal_src\spi_infineon_tricore\src"
"-IE:\Project\ESC3.0_SAIC\boot\0621_boot\ESC30_SAIC_AP31_Boot0621ok\ESC30_SAIC_AP31_Boot\Src_file"
"-I"
"-I"
"-I"
"-I"
"-I"
"-I"
"--iso=99"
"--language=-gcc,-volatile,+strings"
"--switch=auto"
"--align=4"
"--default-near-size=0"
"--default-a0-size=0"
"--default-a1-size=0"
"-O2"
"--tradeoff=4"
"--compact-max-size=200"
"-g"
"--source"
"-c"
"--dep-file=eeprom\.CallOutFunction.o.d"
"-Wc--make-target=eeprom\CallOutFunction.o"
