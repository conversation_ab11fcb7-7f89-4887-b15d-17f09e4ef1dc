	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc28152a -c99 --dep-file=eeprom\\.Fls_17_Pmu_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=eeprom\\Fls_17_Pmu_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o eeprom\\Fls_17_Pmu_PBCfg.src ..\\eeprom\\Fls_17_Pmu_PBCfg.c"
	.compiler_name		"ctc"
	.name	"Fls_17_Pmu_PBCfg"

	
$TC16X
	
	.sdecl	'.data.Fls_17_Pmu_PBCfg.FlsStateVar',data,cluster('FlsStateVar')
	.sect	'.data.Fls_17_Pmu_PBCfg.FlsStateVar'
	.align	4
FlsStateVar:	.type	object
	.size	FlsStateVar,36
	.space	36
	.sdecl	'.rodata.Fls_17_Pmu_PBCfg.Fls_17_Pmu_ConfigRoot',data,rom,cluster('Fls_17_Pmu_ConfigRoot')
	.sect	'.rodata.Fls_17_Pmu_PBCfg.Fls_17_Pmu_ConfigRoot'
	.global	Fls_17_Pmu_ConfigRoot
	.align	4
Fls_17_Pmu_ConfigRoot:	.type	object
	.size	Fls_17_Pmu_ConfigRoot,40
	.word	FlsStateVar,64,32,Fee_JobEndNotification,Fee_JobErrorNotification,Fee_17_IllegalStateNotification,4672,Fls_WriteCmdCycles
	.word	Fls_EraseCmdCycles
	.byte	1
	.space	3
	.calls	'__INDIRECT__','Fls_WriteCmdCycles'
	.calls	'__INDIRECT__','Fls_EraseCmdCycles'
	.calls	'__INDIRECT__','Fee_JobEndNotification'
	.calls	'__INDIRECT__','Fee_JobErrorNotification'
	.extern	Fls_WriteCmdCycles
	.extern	Fls_EraseCmdCycles
	.extern	Fee_JobEndNotification
	.extern	Fee_JobErrorNotification
	.extern	Fee_17_IllegalStateNotification
	.extern	__INDIRECT__
	.calls	'__INDIRECT__','Fee_17_IllegalStateNotification'
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	2061
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\eeprom\\Fls_17_Pmu_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'Fls_WriteCmdCycles',0,1,90,6,1,1,1,1,3
	.byte	'unsigned long int',0,4,7,4
	.word	210
	.byte	5
	.byte	'StartAddress',0,1,90,41
	.word	231
	.byte	5
	.byte	'PageAddress',0,1,91,32
	.word	210
	.byte	6
	.word	210
	.byte	7
	.word	277
	.byte	5
	.byte	'ProgramDataPtr',0,1,92,48
	.word	282
	.byte	3
	.byte	'unsigned char',0,1,8,5
	.byte	'WriteMode',0,1,93,31
	.word	310
	.byte	0,2
	.byte	'Fls_EraseCmdCycles',0,1,101,6,1,1,1,1,4
	.word	210
	.byte	5
	.byte	'StartAddress',0,1,101,41
	.word	373
	.byte	0,8
	.byte	'Fee_JobEndNotification',0,2,63,13,1,1,1,1,8
	.byte	'Fee_JobErrorNotification',0,2,66,13,1,1,1,1,8
	.byte	'Fee_17_IllegalStateNotification',0,2,69,13,1,1,1,1,9
	.byte	'__INDIRECT__',0,2,1,1,1,1,1,10
	.byte	'void',0,7
	.word	524
	.byte	11
	.byte	'__prof_adm',0,2,1,1
	.word	530
	.byte	12,1,7
	.word	554
	.byte	11
	.byte	'__codeptr',0,2,1,1
	.word	556
	.byte	11
	.byte	'uint8',0,3,90,29
	.word	310
	.byte	3
	.byte	'unsigned short int',0,2,7,11
	.byte	'uint16',0,3,92,29
	.word	593
	.byte	11
	.byte	'uint32',0,3,94,29
	.word	210
	.byte	3
	.byte	'unsigned int',0,4,7,11
	.byte	'unsigned_int',0,4,121,22
	.word	645
	.byte	13,5,72,9,1,14
	.byte	'MEMIF_JOB_OK',0,0,14
	.byte	'MEMIF_JOB_FAILED',0,1,14
	.byte	'MEMIF_JOB_PENDING',0,2,14
	.byte	'MEMIF_JOB_CANCELED',0,3,14
	.byte	'MEMIF_BLOCK_INCONSISTENT',0,4,14
	.byte	'MEMIF_BLOCK_INVALID',0,5,0,11
	.byte	'MemIf_JobResultType',0,5,80,3
	.word	682
	.byte	13,5,88,9,1,14
	.byte	'MEMIF_MODE_SLOW',0,0,14
	.byte	'MEMIF_MODE_FAST',0,1,0,11
	.byte	'MemIf_ModeType',0,5,92,3
	.word	840
	.byte	11
	.byte	'Fls_AddressType',0,6,175,3,16
	.word	210
	.byte	11
	.byte	'Fls_LengthType',0,6,177,3,16
	.word	210
	.byte	15
	.byte	'Fls_JobStartType',0,6,179,3,16,1,16
	.byte	'Reserved1',0,1
	.word	310
	.byte	1,7,2,35,0,16
	.byte	'Write',0,1
	.word	310
	.byte	1,6,2,35,0,16
	.byte	'Erase',0,1
	.word	310
	.byte	1,5,2,35,0,16
	.byte	'Read',0,1
	.word	310
	.byte	1,4,2,35,0,16
	.byte	'Compare',0,1
	.word	310
	.byte	1,3,2,35,0,16
	.byte	'Reserved2',0,1
	.word	310
	.byte	3,0,2,35,0,0,11
	.byte	'Fls_JobStartType',0,6,187,3,3
	.word	954
	.byte	11
	.byte	'Fls_17_Pmu_Job_Type',0,6,191,3,15
	.word	310
.L11:
	.byte	15
	.byte	'Fls_17_Pmu_StateType',0,6,202,3,16,36,17
	.byte	'FlsReadAddress',0,4
	.word	210
	.byte	2,35,0,17
	.byte	'FlsWriteAddress',0,4
	.word	210
	.byte	2,35,4,17
	.byte	'FlsReadLength',0,4
	.word	210
	.byte	2,35,8,17
	.byte	'FlsWriteLength',0,4
	.word	210
	.byte	2,35,12,7
	.word	310
	.byte	17
	.byte	'FlsReadBufferPtr',0,4
	.word	1267
	.byte	2,35,16,6
	.word	310
	.byte	7
	.word	1298
	.byte	17
	.byte	'FlsWriteBufferPtr',0,4
	.word	1303
	.byte	2,35,20,17
	.byte	'FlsJobResult',0,1
	.word	682
	.byte	2,35,24,17
	.byte	'FlsMode',0,1
	.word	840
	.byte	2,35,25,17
	.byte	'NotifCaller',0,1
	.word	310
	.byte	2,35,26,17
	.byte	'JobStarted',0,1
	.word	954
	.byte	2,35,27,18,2
	.word	310
	.byte	19,1,0,17
	.byte	'FlsJobType',0,2
	.word	1415
	.byte	2,35,28,17
	.byte	'FlsPver',0,1
	.word	310
	.byte	2,35,30,17
	.byte	'FlsOper',0,1
	.word	310
	.byte	2,35,31,17
	.byte	'FlsTimeoutErr',0,1
	.word	310
	.byte	2,35,32,0,11
	.byte	'Fls_17_Pmu_StateType',0,6,134,4,3
	.word	1144
	.byte	20,1,1,7
	.word	1532
	.byte	11
	.byte	'Fls_NotifFunctionPtrType',0,6,141,4,16
	.word	1535
	.byte	21,1,1,22
	.word	210
	.byte	22
	.word	210
	.byte	6
	.word	210
	.byte	7
	.word	1587
	.byte	22
	.word	1592
	.byte	22
	.word	310
	.byte	0,7
	.word	1574
	.byte	11
	.byte	'Fls_WriteCmdPtrType',0,6,143,4,16
	.word	1608
	.byte	21,1,1,22
	.word	210
	.byte	0,7
	.word	1642
	.byte	11
	.byte	'Fls_EraseCmdPtrType',0,6,148,4,16
	.word	1651
	.byte	15
	.byte	'Fls_17_Pmu_ConfigType',0,6,153,4,16,40,7
	.word	1144
	.byte	17
	.byte	'FlsStateVarPtr',0,4
	.word	1713
	.byte	2,35,0,17
	.byte	'FlsFastRead',0,4
	.word	210
	.byte	2,35,4,17
	.byte	'FlsSlowRead',0,4
	.word	210
	.byte	2,35,8,17
	.byte	'FlsJobEndNotificationPtr',0,4
	.word	1540
	.byte	2,35,12,17
	.byte	'FlsJobErrorNotificationPtr',0,4
	.word	1540
	.byte	2,35,16,17
	.byte	'FlsIllegalStateNotificationPtr',0,4
	.word	1540
	.byte	2,35,20,17
	.byte	'FlsWaitStates',0,4
	.word	210
	.byte	2,35,24,17
	.byte	'FlsAccessCodeWritePtr',0,4
	.word	1613
	.byte	2,35,28,17
	.byte	'FlsAccessCodeErasePtr',0,4
	.word	1656
	.byte	2,35,32,17
	.byte	'FlsDefaultMode',0,1
	.word	840
	.byte	2,35,36,0,11
	.byte	'Fls_17_Pmu_ConfigType',0,6,222,4,3
	.word	1685
	.byte	7
	.word	1532
	.byte	7
	.word	1574
	.byte	7
	.word	1642
	.byte	18,40
	.word	1685
	.byte	19,0,0
.L10:
	.byte	6
	.word	2050
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,36,0,3,8,11,15,62,15,0,0,4,53,0,73,19,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,38,0,73,19,0,0,7,15
	.byte	0,73,19,0,0,8,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,9,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	63,12,60,12,0,0,10,59,0,3,8,0,0,11,22,0,3,8,58,15,59,15,57,15,73,19,0,0,12,21,0,54,15,0,0,13,4,1,58,15
	.byte	59,15,57,15,11,15,0,0,14,40,0,3,8,28,13,0,0,15,19,1,3,8,58,15,59,15,57,15,11,15,0,0,16,13,0,3,8,11,15
	.byte	73,19,13,15,12,15,56,9,0,0,17,13,0,3,8,11,15,73,19,56,9,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20
	.byte	21,0,54,15,39,12,0,0,21,21,1,54,15,39,12,0,0,22,5,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L13-.L12
.L12:
	.half	3
	.word	.L15-.L14
.L14:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf',0
	.byte	0
	.byte	'Fls_17_Pmu_ac.h',0,1,0,0
	.byte	'..\\eeprom\\Fls_17_Pmu_PBCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,2,0,0
	.byte	'Mcal_TcLib.h',0,2,0,0
	.byte	'MemIf_Types.h',0,3,0,0
	.byte	'Fls_17_Pmu.h',0,1,0,0,0
.L15:
.L13:
	.sdecl	'.debug_info',debug,cluster('Fls_17_Pmu_ConfigRoot')
	.sect	'.debug_info'
.L6:
	.word	218
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\eeprom\\Fls_17_Pmu_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Fls_17_Pmu_ConfigRoot',0,2,91,29
	.word	.L10
	.byte	1,5,3
	.word	Fls_17_Pmu_ConfigRoot
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Fls_17_Pmu_ConfigRoot')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('FlsStateVar')
	.sect	'.debug_info'
.L8:
	.word	207
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\eeprom\\Fls_17_Pmu_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'FlsStateVar',0,2,80,30
	.word	.L11
	.byte	5,3
	.word	FlsStateVar
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('FlsStateVar')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0

; ..\eeprom\Fls_17_Pmu_PBCfg.c	     1  /******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	     2  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	     4  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	     5  ** All rights reserved.                                                      **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	     6  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    10  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    11  *******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    12  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    13  **  $FILENAME   : Fls_17_Pmu_PBCfg.c $                                       **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    14  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    15  **  $CC VERSION : \main\46 $                                                 **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    16  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    17  **  DATE, TIME: 2021-10-28, 12:21:21                                         **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    18  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    19  **  GENERATOR : Build b141014-0350                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    20  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    21  **  AUTHOR    : DL-AUTOSAR-Engineering                                       **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    22  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    23  **  VENDOR    : Infineon Technologies                                        **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    24  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    25  **  DESCRIPTION  : FLS configuration generated out of ECU configuration      **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    26  **                   file (Fls_17_Pmu.bmd)                                   **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    27  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    28  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    29  **                                                                           **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    30  ******************************************************************************/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    31  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    32  /*******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    33  **                      Includes                                              **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    34  *******************************************************************************/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    35  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    36  /* Include Flash Module File */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    37  #include "Fls_17_Pmu.h"
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    38  #include "Fls_17_Pmu_ac.h"
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    39  /* Include Diagnostic Error Manager Header */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    40  //#include "Dem.h"
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    41  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    42  /*******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    43  **                      Global Macro Definitions                              **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    44  *******************************************************************************/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    45  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    46  /*******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    47  **                      Global Type Definitions                               **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    48  *******************************************************************************/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    49  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    50  /*******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    51  **                      Global Constant Declarations                          **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    52  *******************************************************************************/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    53  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    54  /*******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    55  **                      Global Variable Declarations                          **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    56  *******************************************************************************/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    57  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    58  /*******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    59  **                      Global Function Declarations                          **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    60  *******************************************************************************/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    61  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    62  /* Function declaration of Fls Job End Notification */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    63  extern void Fee_JobEndNotification(void);
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    64  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    65  /* Function declaration of Fls Job Error Notifications */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    66  extern void Fee_JobErrorNotification(void);
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    67  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    68  /* Function declaration of Illegal State Notification */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    69  extern void Fee_17_IllegalStateNotification(void);
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    70  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    71  /*******************************************************************************
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    72  **                      Global Constant Definitions                          **
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    73  *******************************************************************************/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    74  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    75  #define FLS_17_PMU_START_SEC_VAR_UNSPECIFIED
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    76  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    77  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    78  /* Fls State Variable structure */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    79  #if(FLS_DEBUG_SUPPORT == STD_OFF)
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    80  static Fls_17_Pmu_StateType  FlsStateVar = {.FlsJobType={0}};
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    81  #else
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    82  Fls_17_Pmu_StateType  FlsStateVar = {.FlsJobType={0}};
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    83  #endif
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    84  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    85  #define FLS_17_PMU_STOP_SEC_VAR_UNSPECIFIED
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    86  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    87  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    88  #define FLS_17_PMU_START_SEC_POSTBUILDCFG
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    89  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    90  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    91  const Fls_17_Pmu_ConfigType Fls_17_Pmu_ConfigRoot[] = 
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    92  {
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    93    {
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    94      /* Fls state variable structure */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    95      &FlsStateVar,
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    96  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    97      /* Maximum number of bytes to Read in one cycle */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    98      /* Fast Mode */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	    99      64U,
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   100      /* Normal Mode */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   101      32U,
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   102  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   103      /* Job End Notification */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   104      &Fee_JobEndNotification,
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   105  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   106      /* Job Error Notification */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   107      &Fee_JobErrorNotification,
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   108  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   109      /* Illegal State Notification */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   110      &Fee_17_IllegalStateNotification,
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   111  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   112      /*Wait state configuration for Read access and error correction */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   113      (((uint32)FLS_WAIT_STATE_READACCESS9 << 6U) | 
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   114        ((uint32)FLS_WAIT_STATE_ERRORCORRECTION1 << 12U) ),
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   115  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   116      /*Flash access code address in RAM */ 
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   117      (Fls_WriteCmdPtrType)(void*)&Fls_WriteCmdCycles,
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   118      /*Flash access code address in RAM */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   119      (Fls_EraseCmdPtrType)(void*)&Fls_EraseCmdCycles,
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   120  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   121  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   122  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   123  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   124  /* Default mode of FLS driver */
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   125  MEMIF_MODE_FAST
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   126    }
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   127  };
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   128  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   129  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   130  #define FLS_17_PMU_STOP_SEC_POSTBUILDCFG
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   131  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   132   allowed only for MemMap.h*/
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   133  
; ..\eeprom\Fls_17_Pmu_PBCfg.c	   134  

	; Module end
