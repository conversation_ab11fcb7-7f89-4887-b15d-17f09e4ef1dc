	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc20564a -c99 --dep-file=mcal_cfg\\.Spi_PBCfg.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_cfg\\Spi_PBCfg.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_cfg\\Spi_PBCfg.src ..\\mcal_cfg\\Spi_PBCfg.c"
	.compiler_name		"ctc"
	.name	"Spi_PBCfg"

	
$TC16X
	
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Spi_kChannelConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Spi_kChannelConfig0:	.type	object
	.size	Spi_kChannelConfig0,12
	.space	4
	.half	263,511
	.space	4
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('SpiJob_Micron_Master_ChannelLinkPtr')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
SpiJob_Micron_Master_ChannelLinkPtr:	.type	object
	.size	SpiJob_Micron_Master_ChannelLinkPtr,2
	.space	1
	.byte	255
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Spi_kJobConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Spi_kJobConfig0:	.type	object
	.size	Spi_kJobConfig0,24
	.space	4
	.word	SpiJob_Micron_Master_ChannelLinkPtr,1441793,83220
	.half	61439
	.space	2
	.byte	3,161
	.space	1
	.byte	2
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('SpiSequence_Micron_Master_JobLinkPtr')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
SpiSequence_Micron_Master_JobLinkPtr:	.type	object
	.size	SpiSequence_Micron_Master_JobLinkPtr,4
	.space	2
	.half	65535
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Spi_kSequenceConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Spi_kSequenceConfig0:	.type	object
	.size	Spi_kSequenceConfig0,16
	.space	4
	.word	SpiSequence_Micron_Master_JobLinkPtr
	.space	4
	.half	1
	.space	2
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Spi_kDmaConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Spi_kDmaConfig0:	.type	object
	.size	Spi_kDmaConfig0,8
	.byte	255,255,4,5,255,255,255,255
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Spi_kModuleConfig0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Spi_kModuleConfig0:	.type	object
	.size	Spi_kModuleConfig0,64
	.word	255
	.space	4
	.word	255
	.space	4
	.word	8
	.space	4
	.word	1,Spi_kDmaConfig0+2,255
	.space	4
	.word	255
	.space	4
	.word	255
	.space	4
	.word	255
	.space	4
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Spi_kBaudrateEcon0')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.align	4
Spi_kBaudrateEcon0:	.type	object
	.size	Spi_kBaudrateEcon0,6
	.word	1408
	.byte	161
	.space	1
	.sdecl	'.rodata.CPU0.Private.CONFIGURATION_PB',data,rom,cluster('Spi_ConfigRoot')
	.sect	'.rodata.CPU0.Private.CONFIGURATION_PB'
	.global	Spi_ConfigRoot
	.align	4
Spi_ConfigRoot:	.type	object
	.size	Spi_ConfigRoot,40
	.word	Spi_kChannelConfig0,Spi_kJobConfig0,Spi_kSequenceConfig0,Spi_kModuleConfig0,Spi_kModuleConfig0+16,Spi_kModuleConfig0+32,Spi_kModuleConfig0+48,Spi_kBaudrateEcon0
	.half	1
	.byte	1,1,1
	.space	3
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	58344
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	178
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	184
	.byte	5,1,3
	.word	208
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	210
	.byte	6
	.byte	'unsigned char',0,1,8,4
	.byte	'uint8',0,2,90,29
	.word	233
	.byte	6
	.byte	'unsigned short int',0,2,7,4
	.byte	'uint16',0,2,92,29
	.word	264
	.byte	6
	.byte	'unsigned long int',0,4,7,4
	.byte	'uint32',0,2,94,29
	.word	301
	.byte	7
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,3,45,16,4,8
	.byte	'EN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN00_Bits',0,3,79,3
	.word	337
	.byte	7
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,3,82,16,4,6
	.byte	'unsigned int',0,4,7,8
	.byte	'reserved_0',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN01_Bits',0,3,85,3
	.word	896
	.byte	7
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,3,88,16,4,8
	.byte	'EN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN10_Bits',0,3,122,3
	.word	991
	.byte	7
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,3,125,16,4,8
	.byte	'reserved_0',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN11_Bits',0,3,128,1,3
	.word	1550
	.byte	7
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,3,131,1,16,4,8
	.byte	'EN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN20_Bits',0,3,165,1,3
	.word	1630
	.byte	7
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,3,168,1,16,4,8
	.byte	'reserved_0',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN21_Bits',0,3,171,1,3
	.word	2191
	.byte	7
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,3,174,1,16,4,8
	.byte	'EN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_ACCEN30_Bits',0,3,208,1,3
	.word	2272
	.byte	7
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,3,211,1,16,4,8
	.byte	'reserved_0',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_ACCEN31_Bits',0,3,214,1,3
	.word	2833
	.byte	7
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,3,217,1,16,4,8
	.byte	'reserved_0',0,2
	.word	264
	.byte	16,0,2,35,0,8
	.byte	'CSER',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'CDER',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	233
	.byte	2,4,2,35,2,8
	.byte	'CSPBER',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'CSRIER',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	233
	.byte	2,0,2,35,2,8
	.byte	'CRAMER',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'CSLLER',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'CDLLER',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	233
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,3,230,1,3
	.word	2914
	.byte	7
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,3,233,1,16,4,8
	.byte	'reserved_0',0,2
	.word	264
	.byte	16,0,2,35,0,8
	.byte	'ESER',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'EDER',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	233
	.byte	6,0,2,35,2,8
	.byte	'ERER',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'ELER',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	233
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_EER_Bits',0,3,243,1,3
	.word	3188
	.byte	7
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,3,246,1,16,4,8
	.byte	'LEC',0,1
	.word	233
	.byte	7,1,2,35,0,8
	.byte	'reserved_7',0,2
	.word	264
	.byte	9,0,2,35,0,8
	.byte	'SER',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'DER',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	233
	.byte	2,4,2,35,2,8
	.byte	'SPBER',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'SRIER',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	233
	.byte	2,0,2,35,2,8
	.byte	'RAMER',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'SLLER',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'DLLER',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	233
	.byte	5,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,3,132,2,3
	.word	3402
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,3,135,2,16,4,8
	.byte	'SMF',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'INCS',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'DMF',0,1
	.word	233
	.byte	3,1,2,35,0,8
	.byte	'INCD',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'CBLS',0,1
	.word	233
	.byte	4,4,2,35,1,8
	.byte	'CBLD',0,1
	.word	233
	.byte	4,0,2,35,1,8
	.byte	'SHCT',0,1
	.word	233
	.byte	4,4,2,35,2,8
	.byte	'SCBE',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'DCBE',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'STAMP',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'ETRL',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'WRPSE',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'WRPDE',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'INTCT',0,1
	.word	233
	.byte	2,4,2,35,3,8
	.byte	'IRDV',0,1
	.word	233
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,3,152,2,3
	.word	3686
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,3,155,2,16,4,8
	.byte	'TREL',0,2
	.word	264
	.byte	14,2,2,35,0,8
	.byte	'reserved_14',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'BLKM',0,1
	.word	233
	.byte	3,5,2,35,2,8
	.byte	'RROAT',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'CHMODE',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'CHDW',0,1
	.word	233
	.byte	3,0,2,35,2,8
	.byte	'PATSEL',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'PRSEL',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'reserved_29',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'DMAPRIO',0,1
	.word	233
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,3,168,2,3
	.word	3997
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,3,171,2,16,4,8
	.byte	'TCOUNT',0,2
	.word	264
	.byte	14,2,2,35,0,8
	.byte	'reserved_14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'LXO',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'WRPS',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'WRPD',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'ICH',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'IPM',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'reserved_20',0,1
	.word	233
	.byte	2,2,2,35,2,8
	.byte	'BUFFER',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'FROZEN',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,3,184,2,3
	.word	4270
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,3,187,2,16,4,8
	.byte	'DADR',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,3,190,2,3
	.word	4537
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,3,193,2,16,4,8
	.byte	'RD00',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'RD01',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'RD02',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'RD03',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,3,199,2,3
	.word	4620
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,3,202,2,16,4,8
	.byte	'RD10',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'RD11',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'RD12',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'RD13',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,3,208,2,3
	.word	4747
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,3,211,2,16,4,8
	.byte	'RD20',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'RD21',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'RD22',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'RD23',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,3,217,2,3
	.word	4874
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,3,220,2,16,4,8
	.byte	'RD30',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'RD31',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'RD32',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'RD33',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,3,226,2,3
	.word	5001
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,3,229,2,16,4,8
	.byte	'RD40',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'RD41',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'RD42',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'RD43',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,3,235,2,3
	.word	5128
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,3,238,2,16,4,8
	.byte	'RD50',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'RD51',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'RD52',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'RD53',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,3,244,2,3
	.word	5255
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,3,247,2,16,4,8
	.byte	'RD60',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'RD61',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'RD62',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'RD63',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,3,253,2,3
	.word	5382
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,3,128,3,16,4,8
	.byte	'RD70',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'RD71',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'RD72',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'RD73',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,3,134,3,3
	.word	5509
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,3,137,3,16,4,8
	.byte	'RDCRC',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,3,140,3,3
	.word	5636
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,3,143,3,16,4,8
	.byte	'SADR',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,3,146,3,3
	.word	5722
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,3,149,3,16,4,8
	.byte	'SDCRC',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,3,152,3,3
	.word	5805
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,3,155,3,16,4,8
	.byte	'SHADR',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,3,158,3,3
	.word	5891
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,3,161,3,16,4,8
	.byte	'RS',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	233
	.byte	3,4,2,35,0,8
	.byte	'WS',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'reserved_5',0,2
	.word	264
	.byte	11,0,2,35,0,8
	.byte	'CH',0,1
	.word	233
	.byte	7,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	264
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,3,169,3,3
	.word	5977
	.byte	7
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,3,172,3,16,4,8
	.byte	'SMF',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'INCS',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'DMF',0,1
	.word	233
	.byte	3,1,2,35,0,8
	.byte	'INCD',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'CBLS',0,1
	.word	233
	.byte	4,4,2,35,1,8
	.byte	'CBLD',0,1
	.word	233
	.byte	4,0,2,35,1,8
	.byte	'SHCT',0,1
	.word	233
	.byte	4,4,2,35,2,8
	.byte	'SCBE',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'DCBE',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'STAMP',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'ETRL',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'WRPSE',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'WRPDE',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'INTCT',0,1
	.word	233
	.byte	2,4,2,35,3,8
	.byte	'IRDV',0,1
	.word	233
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,3,189,3,3
	.word	6149
	.byte	7
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,3,192,3,16,4,8
	.byte	'TREL',0,2
	.word	264
	.byte	14,2,2,35,0,8
	.byte	'reserved_14',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'BLKM',0,1
	.word	233
	.byte	3,5,2,35,2,8
	.byte	'RROAT',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'CHMODE',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'CHDW',0,1
	.word	233
	.byte	3,0,2,35,2,8
	.byte	'PATSEL',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'reserved_27',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'PRSEL',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'reserved_29',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'DMAPRIO',0,1
	.word	233
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,3,205,3,3
	.word	6452
	.byte	7
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,3,208,3,16,4,8
	.byte	'TCOUNT',0,2
	.word	264
	.byte	14,2,2,35,0,8
	.byte	'reserved_14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'LXO',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'WRPS',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'WRPD',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'ICH',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'IPM',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'reserved_20',0,1
	.word	233
	.byte	2,2,2,35,2,8
	.byte	'BUFFER',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'FROZEN',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'SWB',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'CWRP',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'CICH',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'SIT',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	233
	.byte	3,1,2,35,3,8
	.byte	'SCH',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,3,226,3,3
	.word	6721
	.byte	7
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,3,229,3,16,4,8
	.byte	'DADR',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_DADR_Bits',0,3,232,3,3
	.word	7059
	.byte	7
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,3,235,3,16,4,8
	.byte	'RDCRC',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,3,238,3,3
	.word	7134
	.byte	7
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,3,241,3,16,4,8
	.byte	'SADR',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SADR_Bits',0,3,244,3,3
	.word	7214
	.byte	7
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,3,247,3,16,4,8
	.byte	'SDCRC',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,3,250,3,3
	.word	7289
	.byte	7
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,3,253,3,16,4,8
	.byte	'SHADR',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,3,128,4,3
	.word	7369
	.byte	7
	.byte	'_Ifx_DMA_CLC_Bits',0,3,131,4,16,4,8
	.byte	'DISR',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'DISS',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'EDIS',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	923
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_DMA_CLC_Bits',0,3,138,4,3
	.word	7447
	.byte	7
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,3,141,4,16,4,8
	.byte	'SIT',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	923
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_ERRINTR_Bits',0,3,145,4,3
	.word	7590
	.byte	7
	.byte	'_Ifx_DMA_HRR_Bits',0,3,148,4,16,4,8
	.byte	'HRP',0,1
	.word	233
	.byte	2,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	923
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_DMA_HRR_Bits',0,3,152,4,3
	.word	7686
	.byte	7
	.byte	'_Ifx_DMA_ID_Bits',0,3,155,4,16,4,8
	.byte	'MODREV',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'MODTYPE',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'MODNUMBER',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_DMA_ID_Bits',0,3,160,4,3
	.word	7774
	.byte	7
	.byte	'_Ifx_DMA_MEMCON_Bits',0,3,163,4,16,4,6
	.byte	'unsigned int',0,4,7,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	2,30,2,35,0,8
	.byte	'INTERR',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'RMWERR',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7908
	.byte	1,26,2,35,0,8
	.byte	'DATAERR',0,4
	.word	7908
	.byte	1,25,2,35,0,8
	.byte	'reserved_7',0,4
	.word	7908
	.byte	1,24,2,35,0,8
	.byte	'PMIC',0,4
	.word	7908
	.byte	1,23,2,35,0,8
	.byte	'ERRDIS',0,4
	.word	7908
	.byte	1,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	7908
	.byte	22,0,2,35,0,0,4
	.byte	'Ifx_DMA_MEMCON_Bits',0,3,175,4,3
	.word	7881
	.byte	7
	.byte	'_Ifx_DMA_MODE_Bits',0,3,178,4,16,4,8
	.byte	'MODE',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	923
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_MODE_Bits',0,3,182,4,3
	.word	8154
	.byte	7
	.byte	'_Ifx_DMA_OTSS_Bits',0,3,185,4,16,4,8
	.byte	'TGS',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	233
	.byte	3,1,2,35,0,8
	.byte	'BS',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	923
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_DMA_OTSS_Bits',0,3,191,4,3
	.word	8245
	.byte	7
	.byte	'_Ifx_DMA_PRR0_Bits',0,3,194,4,16,4,8
	.byte	'PAT00',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'PAT01',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'PAT02',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'PAT03',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_PRR0_Bits',0,3,200,4,3
	.word	8371
	.byte	7
	.byte	'_Ifx_DMA_PRR1_Bits',0,3,203,4,16,4,8
	.byte	'PAT10',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'PAT11',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'PAT12',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'PAT13',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_DMA_PRR1_Bits',0,3,209,4,3
	.word	8492
	.byte	7
	.byte	'_Ifx_DMA_SUSACR_Bits',0,3,212,4,16,4,8
	.byte	'SUSAC',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	923
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_SUSACR_Bits',0,3,216,4,3
	.word	8613
	.byte	7
	.byte	'_Ifx_DMA_SUSENR_Bits',0,3,219,4,16,4,8
	.byte	'SUSEN',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	923
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_DMA_SUSENR_Bits',0,3,223,4,3
	.word	8709
	.byte	7
	.byte	'_Ifx_DMA_TIME_Bits',0,3,226,4,16,4,8
	.byte	'COUNT',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_DMA_TIME_Bits',0,3,229,4,3
	.word	8805
	.byte	7
	.byte	'_Ifx_DMA_TSR_Bits',0,3,232,4,16,4,8
	.byte	'RST',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'HTRE',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'TRL',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'CH',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	233
	.byte	4,0,2,35,0,8
	.byte	'HLTREQ',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'HLTACK',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'reserved_10',0,1
	.word	233
	.byte	6,0,2,35,1,8
	.byte	'ECH',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'DCH',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'CTL',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	233
	.byte	5,0,2,35,2,8
	.byte	'HLTCLR',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	233
	.byte	7,0,2,35,3,0,4
	.byte	'Ifx_DMA_TSR_Bits',0,3,248,4,3
	.word	8875
	.byte	9,3,128,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,6
	.byte	'int',0,4,5,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	337
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN00',0,3,133,5,3
	.word	9176
	.byte	9,3,136,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	896
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN01',0,3,141,5,3
	.word	9248
	.byte	9,3,144,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	991
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN10',0,3,149,5,3
	.word	9313
	.byte	9,3,152,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1550
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN11',0,3,157,5,3
	.word	9378
	.byte	9,3,160,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	1630
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN20',0,3,165,5,3
	.word	9443
	.byte	9,3,168,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2191
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN21',0,3,173,5,3
	.word	9508
	.byte	9,3,176,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2272
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN30',0,3,181,5,3
	.word	9573
	.byte	9,3,184,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2833
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ACCEN31',0,3,189,5,3
	.word	9638
	.byte	9,3,192,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	2914
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_CLRE',0,3,197,5,3
	.word	9703
	.byte	9,3,200,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3188
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_EER',0,3,205,5,3
	.word	9769
	.byte	9,3,208,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3402
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ERRSR',0,3,213,5,3
	.word	9834
	.byte	9,3,216,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3686
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,3,221,5,3
	.word	9901
	.byte	9,3,224,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	3997
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,3,229,5,3
	.word	9971
	.byte	9,3,232,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4270
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,3,237,5,3
	.word	10040
	.byte	9,3,240,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4537
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_DADR',0,3,245,5,3
	.word	10109
	.byte	9,3,248,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4620
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R0',0,3,253,5,3
	.word	10178
	.byte	9,3,128,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4747
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R1',0,3,133,6,3
	.word	10245
	.byte	9,3,136,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	4874
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R2',0,3,141,6,3
	.word	10312
	.byte	9,3,144,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5001
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R3',0,3,149,6,3
	.word	10379
	.byte	9,3,152,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5128
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R4',0,3,157,6,3
	.word	10446
	.byte	9,3,160,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5255
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R5',0,3,165,6,3
	.word	10513
	.byte	9,3,168,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5382
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R6',0,3,173,6,3
	.word	10580
	.byte	9,3,176,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5509
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_R7',0,3,181,6,3
	.word	10647
	.byte	9,3,184,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5636
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,3,189,6,3
	.word	10714
	.byte	9,3,192,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5722
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SADR',0,3,197,6,3
	.word	10784
	.byte	9,3,200,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5805
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,3,205,6,3
	.word	10853
	.byte	9,3,208,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5891
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,3,213,6,3
	.word	10923
	.byte	9,3,216,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	5977
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_BLK_ME_SR',0,3,221,6,3
	.word	10993
	.byte	9,3,224,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6149
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_ADICR',0,3,229,6,3
	.word	11060
	.byte	9,3,232,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6452
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_CHCFGR',0,3,237,6,3
	.word	11126
	.byte	9,3,240,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	6721
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_CHCSR',0,3,245,6,3
	.word	11193
	.byte	9,3,248,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7059
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_DADR',0,3,253,6,3
	.word	11259
	.byte	9,3,128,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7134
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_RDCRCR',0,3,133,7,3
	.word	11324
	.byte	9,3,136,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7214
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SADR',0,3,141,7,3
	.word	11391
	.byte	9,3,144,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7289
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SDCRCR',0,3,149,7,3
	.word	11456
	.byte	9,3,152,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7369
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CH_SHADR',0,3,157,7,3
	.word	11523
	.byte	9,3,160,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7447
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_CLC',0,3,165,7,3
	.word	11589
	.byte	9,3,168,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7590
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ERRINTR',0,3,173,7,3
	.word	11650
	.byte	9,3,176,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7686
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_HRR',0,3,181,7,3
	.word	11715
	.byte	9,3,184,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7774
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_ID',0,3,189,7,3
	.word	11776
	.byte	9,3,192,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	7881
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_MEMCON',0,3,197,7,3
	.word	11836
	.byte	9,3,200,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8154
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_MODE',0,3,205,7,3
	.word	11900
	.byte	9,3,208,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8245
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_OTSS',0,3,213,7,3
	.word	11962
	.byte	9,3,216,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8371
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_PRR0',0,3,221,7,3
	.word	12024
	.byte	9,3,224,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8492
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_PRR1',0,3,229,7,3
	.word	12086
	.byte	9,3,232,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8613
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_SUSACR',0,3,237,7,3
	.word	12148
	.byte	9,3,240,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8709
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_SUSENR',0,3,245,7,3
	.word	12212
	.byte	9,3,248,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8805
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_TIME',0,3,253,7,3
	.word	12276
	.byte	9,3,128,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	8875
	.byte	2,35,0,0,4
	.byte	'Ifx_DMA_TSR',0,3,133,8,3
	.word	12338
	.byte	7
	.byte	'_Ifx_DMA_BLK_ME',0,3,144,8,25,112,10
	.byte	'SR',0,4
	.word	10993
	.byte	2,35,0,11,12
	.word	233
	.byte	12,11,0,10
	.byte	'reserved_4',0,12
	.word	12433
	.byte	2,35,4,10
	.byte	'R0',0,4
	.word	10178
	.byte	2,35,16,10
	.byte	'R1',0,4
	.word	10245
	.byte	2,35,20,10
	.byte	'R2',0,4
	.word	10312
	.byte	2,35,24,10
	.byte	'R3',0,4
	.word	10379
	.byte	2,35,28,10
	.byte	'R4',0,4
	.word	10446
	.byte	2,35,32,10
	.byte	'R5',0,4
	.word	10513
	.byte	2,35,36,10
	.byte	'R6',0,4
	.word	10580
	.byte	2,35,40,10
	.byte	'R7',0,4
	.word	10647
	.byte	2,35,44,11,32
	.word	233
	.byte	12,31,0,10
	.byte	'reserved_30',0,32
	.word	12558
	.byte	2,35,48,10
	.byte	'RDCRC',0,4
	.word	10714
	.byte	2,35,80,10
	.byte	'SDCRC',0,4
	.word	10853
	.byte	2,35,84,10
	.byte	'SADR',0,4
	.word	10784
	.byte	2,35,88,10
	.byte	'DADR',0,4
	.word	10109
	.byte	2,35,92,10
	.byte	'ADICR',0,4
	.word	9901
	.byte	2,35,96,10
	.byte	'CHCR',0,4
	.word	9971
	.byte	2,35,100,10
	.byte	'SHADR',0,4
	.word	10923
	.byte	2,35,104,10
	.byte	'CHSR',0,4
	.word	10040
	.byte	2,35,108,0,13
	.word	12399
	.byte	4
	.byte	'Ifx_DMA_BLK_ME',0,3,165,8,3
	.word	12705
	.byte	7
	.byte	'_Ifx_DMA_BLK',0,3,178,8,25,128,1,10
	.byte	'EER',0,4
	.word	9769
	.byte	2,35,0,10
	.byte	'ERRSR',0,4
	.word	9834
	.byte	2,35,4,10
	.byte	'CLRE',0,4
	.word	9703
	.byte	2,35,8,11,4
	.word	233
	.byte	12,3,0,10
	.byte	'reserved_C',0,4
	.word	12796
	.byte	2,35,12,13
	.word	12399
	.byte	10
	.byte	'ME',0,112
	.word	12825
	.byte	2,35,16,0,13
	.word	12734
	.byte	4
	.byte	'Ifx_DMA_BLK',0,3,185,8,3
	.word	12843
	.byte	7
	.byte	'_Ifx_DMA_CH',0,3,188,8,25,32,10
	.byte	'RDCRCR',0,4
	.word	11324
	.byte	2,35,0,10
	.byte	'SDCRCR',0,4
	.word	11456
	.byte	2,35,4,10
	.byte	'SADR',0,4
	.word	11391
	.byte	2,35,8,10
	.byte	'DADR',0,4
	.word	11259
	.byte	2,35,12,10
	.byte	'ADICR',0,4
	.word	11060
	.byte	2,35,16,10
	.byte	'CHCFGR',0,4
	.word	11126
	.byte	2,35,20,10
	.byte	'SHADR',0,4
	.word	11523
	.byte	2,35,24,10
	.byte	'CHCSR',0,4
	.word	11193
	.byte	2,35,28,0,13
	.word	12869
	.byte	4
	.byte	'Ifx_DMA_CH',0,3,198,8,3
	.word	13009
	.byte	7
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,8
	.byte	'EN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'EN1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'EN2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'EN3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'EN4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'EN5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'EN6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'EN7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'EN8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'EN9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'EN10',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'EN11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'EN12',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'EN13',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'EN14',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'EN15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'EN16',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'EN17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'EN18',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'EN19',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'EN20',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'EN21',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'EN22',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'EN23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'EN24',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'EN25',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'EN26',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'EN27',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'EN28',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'EN29',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'EN30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'EN31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	13034
	.byte	7
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,8
	.byte	'reserved_0',0,4
	.word	923
	.byte	32,0,2,35,2,0,4
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	13591
	.byte	7
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,8
	.byte	'STM0DIS',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'STM1DIS',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'STM2DIS',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,4
	.word	923
	.byte	29,0,2,35,2,0,4
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	13668
	.byte	7
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'BAUD2DIV',0,1
	.word	233
	.byte	4,0,2,35,0,8
	.byte	'SRIDIV',0,1
	.word	233
	.byte	4,4,2,35,1,8
	.byte	'LPDIV',0,1
	.word	233
	.byte	4,0,2,35,1,8
	.byte	'SPBDIV',0,1
	.word	233
	.byte	4,4,2,35,2,8
	.byte	'FSI2DIV',0,1
	.word	233
	.byte	2,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	233
	.byte	2,0,2,35,2,8
	.byte	'FSIDIV',0,1
	.word	233
	.byte	2,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	233
	.byte	2,4,2,35,3,8
	.byte	'CLKSEL',0,1
	.word	233
	.byte	2,2,2,35,3,8
	.byte	'UP',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	13804
	.byte	7
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,8
	.byte	'CANDIV',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'ERAYDIV',0,1
	.word	233
	.byte	4,0,2,35,0,8
	.byte	'STMDIV',0,1
	.word	233
	.byte	4,4,2,35,1,8
	.byte	'GTMDIV',0,1
	.word	233
	.byte	4,0,2,35,1,8
	.byte	'ETHDIV',0,1
	.word	233
	.byte	4,4,2,35,2,8
	.byte	'ASCLINFDIV',0,1
	.word	233
	.byte	4,0,2,35,2,8
	.byte	'ASCLINSDIV',0,1
	.word	233
	.byte	4,4,2,35,3,8
	.byte	'INSEL',0,1
	.word	233
	.byte	2,2,2,35,3,8
	.byte	'UP',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	14086
	.byte	7
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,8
	.byte	'BBBDIV',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	923
	.byte	26,2,2,35,2,8
	.byte	'UP',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	14324
	.byte	7
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,8
	.byte	'PLLDIV',0,1
	.word	233
	.byte	6,2,2,35,0,8
	.byte	'PLLSEL',0,1
	.word	233
	.byte	2,0,2,35,0,8
	.byte	'PLLERAYDIV',0,1
	.word	233
	.byte	6,2,2,35,1,8
	.byte	'PLLERAYSEL',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'SRIDIV',0,1
	.word	233
	.byte	6,2,2,35,2,8
	.byte	'SRISEL',0,1
	.word	233
	.byte	2,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	6,2,2,35,3,8
	.byte	'UP',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,149,1,3
	.word	14452
	.byte	7
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,152,1,16,4,8
	.byte	'SPBDIV',0,1
	.word	233
	.byte	6,2,2,35,0,8
	.byte	'SPBSEL',0,1
	.word	233
	.byte	2,0,2,35,0,8
	.byte	'GTMDIV',0,1
	.word	233
	.byte	6,2,2,35,1,8
	.byte	'GTMSEL',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'STMDIV',0,1
	.word	233
	.byte	6,2,2,35,2,8
	.byte	'STMSEL',0,1
	.word	233
	.byte	2,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	6,2,2,35,3,8
	.byte	'UP',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,163,1,3
	.word	14679
	.byte	7
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,166,1,16,4,8
	.byte	'MAXDIV',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	923
	.byte	26,2,2,35,2,8
	.byte	'UP',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,172,1,3
	.word	14898
	.byte	7
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,175,1,16,4,8
	.byte	'CPU0DIV',0,1
	.word	233
	.byte	6,2,2,35,0,8
	.byte	'reserved_6',0,4
	.word	923
	.byte	26,0,2,35,2,0,4
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,179,1,3
	.word	15026
	.byte	7
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,182,1,16,4,8
	.byte	'CHREV',0,1
	.word	233
	.byte	6,2,2,35,0,8
	.byte	'CHTEC',0,1
	.word	233
	.byte	2,0,2,35,0,8
	.byte	'CHID',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'EEA',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'UCODE',0,1
	.word	233
	.byte	7,0,2,35,2,8
	.byte	'FSIZE',0,1
	.word	233
	.byte	4,4,2,35,3,8
	.byte	'SP',0,1
	.word	233
	.byte	2,2,2,35,3,8
	.byte	'SEC',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,193,1,3
	.word	15126
	.byte	7
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,196,1,16,4,8
	.byte	'PWD',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'START',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	2,4,2,35,0,8
	.byte	'CAL',0,4
	.word	923
	.byte	22,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	233
	.byte	5,1,2,35,3,8
	.byte	'SLCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,204,1,3
	.word	15334
	.byte	7
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,207,1,16,4,8
	.byte	'LOWER',0,2
	.word	264
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	233
	.byte	5,1,2,35,1,8
	.byte	'LLU',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'UPPER',0,2
	.word	264
	.byte	10,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	233
	.byte	4,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'UOF',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,216,1,3
	.word	15499
	.byte	7
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,219,1,16,4,8
	.byte	'RESULT',0,2
	.word	264
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	233
	.byte	4,2,2,35,1,8
	.byte	'RDY',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'BUSY',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,226,1,3
	.word	15682
	.byte	7
	.byte	'_Ifx_SCU_EICR_Bits',0,4,229,1,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'EXIS0',0,1
	.word	233
	.byte	3,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'FEN0',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'REN0',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'LDEN0',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'EIEN0',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'INP0',0,1
	.word	233
	.byte	3,1,2,35,1,8
	.byte	'reserved_15',0,4
	.word	923
	.byte	5,12,2,35,2,8
	.byte	'EXIS1',0,1
	.word	233
	.byte	3,1,2,35,2,8
	.byte	'reserved_23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'FEN1',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'REN1',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'LDEN1',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'EIEN1',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'INP1',0,1
	.word	233
	.byte	3,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EICR_Bits',0,4,248,1,3
	.word	15836
	.byte	7
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,251,1,16,4,8
	.byte	'INTF0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'INTF1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'INTF2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'INTF3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'INTF4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'INTF5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'INTF6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'INTF7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	923
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_EIFR_Bits',0,4,134,2,3
	.word	16200
	.byte	7
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,137,2,16,4,8
	.byte	'POL',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'MODE',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'ENON',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'PSEL',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,2
	.word	264
	.byte	12,0,2,35,0,8
	.byte	'EMSF',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'SEMSF',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	233
	.byte	6,0,2,35,2,8
	.byte	'EMSFM',0,1
	.word	233
	.byte	2,6,2,35,3,8
	.byte	'SEMSFM',0,1
	.word	233
	.byte	2,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	233
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_EMSR_Bits',0,4,150,2,3
	.word	16411
	.byte	7
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,153,2,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	7,1,2,35,0,8
	.byte	'EDCON',0,2
	.word	264
	.byte	2,7,2,35,0,8
	.byte	'reserved_9',0,4
	.word	923
	.byte	23,0,2,35,2,0,4
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,158,2,3
	.word	16663
	.byte	7
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,161,2,16,4,8
	.byte	'ARI',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'ARC',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	923
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,166,2,3
	.word	16781
	.byte	7
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,169,2,16,4,8
	.byte	'reserved_0',0,4
	.word	923
	.byte	28,4,2,35,2,8
	.byte	'EVR13OFF',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'BPEVR13OFF',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,176,2,3
	.word	16892
	.byte	7
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,179,2,16,4,8
	.byte	'ADC13V',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'ADCSWDV',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	7,1,2,35,3,8
	.byte	'VAL',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,186,2,3
	.word	17055
	.byte	7
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,189,2,16,4,8
	.byte	'EVR13OVMOD',0,1
	.word	233
	.byte	2,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	2,4,2,35,0,8
	.byte	'EVR13UVMOD',0,1
	.word	233
	.byte	2,2,2,35,0,8
	.byte	'reserved_6',0,2
	.word	264
	.byte	10,0,2,35,0,8
	.byte	'SWDOVMOD',0,1
	.word	233
	.byte	2,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	233
	.byte	2,4,2,35,2,8
	.byte	'SWDUVMOD',0,1
	.word	233
	.byte	2,2,2,35,2,8
	.byte	'reserved_22',0,2
	.word	264
	.byte	8,2,2,35,2,8
	.byte	'SLCK',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,201,2,3
	.word	17217
	.byte	7
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,204,2,16,4,8
	.byte	'EVR13OVVAL',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'SWDOVVAL',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	6,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,212,2,3
	.word	17495
	.byte	7
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,215,2,16,4,8
	.byte	'reserved_0',0,4
	.word	923
	.byte	28,4,2,35,2,8
	.byte	'RSTSWDOFF',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'BPRSTSWDOFF',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,222,2,3
	.word	17674
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,225,2,16,4,8
	.byte	'SD33P',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	233
	.byte	4,0,2,35,0,8
	.byte	'SD33I',0,1
	.word	233
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,4
	.word	923
	.byte	19,1,2,35,2,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,232,2,3
	.word	17834
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,235,2,16,4,8
	.byte	'SDFREQSPRD',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	233
	.byte	4,0,2,35,0,8
	.byte	'TON',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'TOFF',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'SDSTEP',0,1
	.word	233
	.byte	4,4,2,35,3,8
	.byte	'SYNCDIV',0,1
	.word	233
	.byte	3,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,244,2,3
	.word	17995
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,247,2,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'STBS',0,1
	.word	233
	.byte	2,6,2,35,1,8
	.byte	'STSP',0,1
	.word	233
	.byte	2,4,2,35,1,8
	.byte	'NS',0,1
	.word	233
	.byte	2,2,2,35,1,8
	.byte	'OL',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'PIAD',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'ADCMODE',0,1
	.word	233
	.byte	4,4,2,35,2,8
	.byte	'ADCLPF',0,1
	.word	233
	.byte	2,2,2,35,2,8
	.byte	'ADCLSB',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'reserved_23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'SDLUT',0,1
	.word	233
	.byte	6,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,134,3,3
	.word	18187
	.byte	7
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,137,3,16,4,8
	.byte	'SDOLCON',0,1
	.word	233
	.byte	7,1,2,35,0,8
	.byte	'MODSEL',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'MODLOW',0,1
	.word	233
	.byte	7,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'SDVOKLVL',0,1
	.word	233
	.byte	6,2,2,35,2,8
	.byte	'MODMAN',0,1
	.word	233
	.byte	2,0,2,35,2,8
	.byte	'MODHIGH',0,1
	.word	233
	.byte	7,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,147,3,3
	.word	18483
	.byte	7
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,150,3,16,4,8
	.byte	'EVR13',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'OV13',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	2,4,2,35,0,8
	.byte	'OVSWD',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'UV13',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'UVSWD',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	2,6,2,35,1,8
	.byte	'BGPROK',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'reserved_11',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'SCMOD',0,1
	.word	233
	.byte	2,2,2,35,1,8
	.byte	'reserved_14',0,4
	.word	923
	.byte	18,0,2,35,2,0,4
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,164,3,3
	.word	18698
	.byte	7
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,167,3,16,4,8
	.byte	'EVR13UVVAL',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'SWDUVVAL',0,1
	.word	233
	.byte	8,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	6,2,2,35,3,8
	.byte	'SLCK',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,175,3,3
	.word	18987
	.byte	7
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,178,3,16,4,8
	.byte	'EN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'SEL0',0,1
	.word	233
	.byte	4,2,2,35,0,8
	.byte	'reserved_6',0,2
	.word	264
	.byte	10,0,2,35,0,8
	.byte	'EN1',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'NSEL',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'SEL1',0,1
	.word	233
	.byte	4,2,2,35,2,8
	.byte	'reserved_22',0,1
	.word	233
	.byte	2,0,2,35,2,8
	.byte	'DIV1',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,189,3,3
	.word	19166
	.byte	7
	.byte	'_Ifx_SCU_FDR_Bits',0,4,192,3,16,4,8
	.byte	'STEP',0,2
	.word	264
	.byte	10,6,2,35,0,8
	.byte	'reserved_10',0,1
	.word	233
	.byte	4,2,2,35,1,8
	.byte	'DM',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'RESULT',0,2
	.word	264
	.byte	10,6,2,35,2,8
	.byte	'reserved_26',0,1
	.word	233
	.byte	5,1,2,35,3,8
	.byte	'DISCLK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_FDR_Bits',0,4,200,3,3
	.word	19384
	.byte	7
	.byte	'_Ifx_SCU_FMR_Bits',0,4,203,3,16,4,8
	.byte	'FS0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'FS1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'FS2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'FS3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'FS4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'FS5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'FS6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'FS7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'FC0',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'FC1',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'FC2',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'FC3',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'FC4',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'FC5',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'FC6',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'FC7',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_FMR_Bits',0,4,223,3,3
	.word	19547
	.byte	7
	.byte	'_Ifx_SCU_ID_Bits',0,4,226,3,16,4,8
	.byte	'MODREV',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'MODTYPE',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'MODNUMBER',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_ID_Bits',0,4,231,3,3
	.word	19883
	.byte	7
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,234,3,16,4,8
	.byte	'IPEN00',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'IPEN01',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'IPEN02',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'IPEN03',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'IPEN04',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'IPEN05',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'IPEN06',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'IPEN07',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	5,3,2,35,1,8
	.byte	'GEEN0',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'IGP0',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'IPEN10',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'IPEN11',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'IPEN12',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'IPEN13',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'IPEN14',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'IPEN15',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'IPEN16',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'IPEN17',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	5,3,2,35,3,8
	.byte	'GEEN1',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'IGP1',0,1
	.word	233
	.byte	2,0,2,35,3,0,4
	.byte	'Ifx_SCU_IGCR_Bits',0,4,130,4,3
	.word	19990
	.byte	7
	.byte	'_Ifx_SCU_IN_Bits',0,4,133,4,16,4,8
	.byte	'P0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	923
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_IN_Bits',0,4,138,4,3
	.word	20442
	.byte	7
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,141,4,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	4,4,2,35,0,8
	.byte	'PC0',0,1
	.word	233
	.byte	4,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	4,4,2,35,1,8
	.byte	'PC1',0,1
	.word	233
	.byte	4,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_IOCR_Bits',0,4,148,4,3
	.word	20541
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,151,4,16,4,8
	.byte	'LBISTREQ',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'LBISTREQP',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'PATTERNS',0,2
	.word	264
	.byte	14,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,157,4,3
	.word	20691
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,160,4,16,4,8
	.byte	'SEED',0,4
	.word	923
	.byte	23,9,2,35,2,8
	.byte	'reserved_23',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'SPLITSH',0,1
	.word	233
	.byte	3,5,2,35,3,8
	.byte	'BODY',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'LBISTFREQU',0,1
	.word	233
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,167,4,3
	.word	20840
	.byte	7
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,170,4,16,4,8
	.byte	'SIGNATURE',0,4
	.word	923
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	7,1,2,35,3,8
	.byte	'LBISTDONE',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,175,4,3
	.word	21001
	.byte	7
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,4,178,4,16,4,8
	.byte	'reserved_0',0,2
	.word	264
	.byte	16,0,2,35,0,8
	.byte	'LS',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,2
	.word	264
	.byte	14,1,2,35,2,8
	.byte	'LSEN',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_LCLCON0_Bits',0,4,184,4,3
	.word	21131
	.byte	7
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,187,4,16,4,8
	.byte	'LCLT0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'LCLT1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	923
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,192,4,3
	.word	21265
	.byte	7
	.byte	'_Ifx_SCU_MANID_Bits',0,4,195,4,16,4,8
	.byte	'DEPT',0,1
	.word	233
	.byte	5,3,2,35,0,8
	.byte	'MANUF',0,2
	.word	264
	.byte	11,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_MANID_Bits',0,4,200,4,3
	.word	21380
	.byte	7
	.byte	'_Ifx_SCU_OMR_Bits',0,4,203,4,16,4,8
	.byte	'PS0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PS1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,2
	.word	264
	.byte	14,0,2,35,0,8
	.byte	'PCL0',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'PCL1',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,2
	.word	264
	.byte	14,0,2,35,2,0,4
	.byte	'Ifx_SCU_OMR_Bits',0,4,211,4,3
	.word	21491
	.byte	7
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,214,4,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PLLLV',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'OSCRES',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'GAINSEL',0,1
	.word	233
	.byte	2,3,2,35,0,8
	.byte	'MODE',0,1
	.word	233
	.byte	2,1,2,35,0,8
	.byte	'SHBY',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'PLLHV',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'reserved_9',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'X1D',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'X1DEN',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	233
	.byte	4,0,2,35,1,8
	.byte	'OSCVAL',0,1
	.word	233
	.byte	5,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	233
	.byte	2,1,2,35,2,8
	.byte	'APREN',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	8,0,2,35,3,0,4
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,231,4,3
	.word	21649
	.byte	7
	.byte	'_Ifx_SCU_OUT_Bits',0,4,234,4,16,4,8
	.byte	'P0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'P1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	923
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_OUT_Bits',0,4,239,4,3
	.word	21989
	.byte	7
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,242,4,16,4,8
	.byte	'CSEL0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'CSEL1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'CSEL2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,2
	.word	264
	.byte	13,0,2,35,0,8
	.byte	'OVSTRT',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'OVSTP',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'DCINVAL',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	233
	.byte	5,0,2,35,2,8
	.byte	'OVCONF',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'POVCONF',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	233
	.byte	6,0,2,35,3,0,4
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,255,4,3
	.word	22090
	.byte	7
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,130,5,16,4,8
	.byte	'OVEN0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'OVEN1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'OVEN2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,4
	.word	923
	.byte	29,0,2,35,2,0,4
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,136,5,3
	.word	22357
	.byte	7
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,139,5,16,4,8
	.byte	'PDIS0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PDIS1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	923
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDISC_Bits',0,4,144,5,3
	.word	22493
	.byte	7
	.byte	'_Ifx_SCU_PDR_Bits',0,4,147,5,16,4,8
	.byte	'PD0',0,1
	.word	233
	.byte	3,5,2,35,0,8
	.byte	'PL0',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PD1',0,1
	.word	233
	.byte	3,1,2,35,0,8
	.byte	'PL1',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	923
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDR_Bits',0,4,154,5,3
	.word	22604
	.byte	7
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,157,5,16,4,8
	.byte	'PDR0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PDR1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'PDR2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'PDR3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PDR4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'PDR5',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'PDR6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PDR7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	923
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PDRR_Bits',0,4,168,5,3
	.word	22737
	.byte	7
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,171,5,16,4,8
	.byte	'VCOBYP',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'VCOPWD',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'MODEN',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'SETFINDIS',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'CLRFINDIS',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'OSCDISCDIS',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'reserved_7',0,2
	.word	264
	.byte	2,7,2,35,0,8
	.byte	'NDIV',0,1
	.word	233
	.byte	7,0,2,35,1,8
	.byte	'PLLPWD',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'RESLD',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	233
	.byte	5,0,2,35,2,8
	.byte	'PDIV',0,1
	.word	233
	.byte	4,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	233
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,188,5,3
	.word	22940
	.byte	7
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,191,5,16,4,8
	.byte	'K2DIV',0,1
	.word	233
	.byte	7,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'K3DIV',0,1
	.word	233
	.byte	7,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'K1DIV',0,1
	.word	233
	.byte	7,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	264
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,199,5,3
	.word	23296
	.byte	7
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,202,5,16,4,8
	.byte	'MODCFG',0,2
	.word	264
	.byte	16,0,2,35,0,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,206,5,3
	.word	23474
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,209,5,16,4,8
	.byte	'VCOBYP',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'VCOPWD',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	2,4,2,35,0,8
	.byte	'SETFINDIS',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'CLRFINDIS',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'OSCDISCDIS',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'reserved_7',0,2
	.word	264
	.byte	2,7,2,35,0,8
	.byte	'NDIV',0,1
	.word	233
	.byte	5,2,2,35,1,8
	.byte	'reserved_14',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'PLLPWD',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'RESLD',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	233
	.byte	5,0,2,35,2,8
	.byte	'PDIV',0,1
	.word	233
	.byte	4,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	233
	.byte	4,0,2,35,3,0,4
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,226,5,3
	.word	23574
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,229,5,16,4,8
	.byte	'K2DIV',0,1
	.word	233
	.byte	7,1,2,35,0,8
	.byte	'reserved_7',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'K3DIV',0,1
	.word	233
	.byte	4,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	233
	.byte	4,0,2,35,1,8
	.byte	'K1DIV',0,1
	.word	233
	.byte	7,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	264
	.byte	9,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,237,5,3
	.word	23944
	.byte	7
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,240,5,16,4,8
	.byte	'VCOBYST',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'PWDSTAT',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'VCOLOCK',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'FINDIS',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'K1RDY',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'K2RDY',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,4
	.word	923
	.byte	26,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,249,5,3
	.word	24130
	.byte	7
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,252,5,16,4,8
	.byte	'VCOBYST',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'VCOLOCK',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'FINDIS',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'K1RDY',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'K2RDY',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'reserved_6',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'MODRUN',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,4
	.word	923
	.byte	24,0,2,35,2,0,4
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,135,6,3
	.word	24328
	.byte	7
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,138,6,16,4,8
	.byte	'REQSLP',0,1
	.word	233
	.byte	2,6,2,35,0,8
	.byte	'SMUSLP',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'reserved_3',0,1
	.word	233
	.byte	5,0,2,35,0,8
	.byte	'PMST',0,1
	.word	233
	.byte	3,5,2,35,1,8
	.byte	'reserved_11',0,4
	.word	923
	.byte	21,0,2,35,2,0,4
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,145,6,3
	.word	24561
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,148,6,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'ESR1WKEN',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'PINAWKEN',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'PINBWKEN',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'ESR0DFEN',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'ESR0EDCON',0,1
	.word	233
	.byte	2,1,2,35,0,8
	.byte	'ESR1DFEN',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'ESR1EDCON',0,1
	.word	233
	.byte	2,6,2,35,1,8
	.byte	'PINADFEN',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'PINAEDCON',0,1
	.word	233
	.byte	2,3,2,35,1,8
	.byte	'PINBDFEN',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'PINBEDCON',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'reserved_16',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'STBYRAMSEL',0,1
	.word	233
	.byte	2,5,2,35,2,8
	.byte	'reserved_19',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'WUTWKEN',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	233
	.byte	2,1,2,35,2,8
	.byte	'PORSTDF',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'DCDCSYNC',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	233
	.byte	3,3,2,35,3,8
	.byte	'ESR0TRIST',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'reserved_30',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,174,6,3
	.word	24713
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,177,6,16,4,8
	.byte	'reserved_0',0,2
	.word	264
	.byte	12,4,2,35,0,8
	.byte	'IRADIS',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'reserved_13',0,4
	.word	923
	.byte	14,5,2,35,2,8
	.byte	'STBYEVEN',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'STBYEV',0,1
	.word	233
	.byte	3,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,185,6,3
	.word	25272
	.byte	7
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,4,188,6,16,4,8
	.byte	'WUTREL',0,4
	.word	923
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	4,4,2,35,3,8
	.byte	'WUTDIV',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'WUTEN',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'WUTMODE',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'LCK',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,4,196,6,3
	.word	25455
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,199,6,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	2,6,2,35,0,8
	.byte	'ESR1WKP',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'ESR1OVRUN',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PINAWKP',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'PINAOVRUN',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'PINBWKP',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PINBOVRUN',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'PORSTDF',0,1
	.word	233
	.byte	1,6,2,35,1,8
	.byte	'HWCFGEVR',0,1
	.word	233
	.byte	3,3,2,35,1,8
	.byte	'STBYRAM',0,1
	.word	233
	.byte	2,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'WUTWKP',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'WUTOVRUN',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'WUTWKEN',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'ESR1WKEN',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'PINAWKEN',0,1
	.word	233
	.byte	1,2,2,35,2,8
	.byte	'PINBWKEN',0,1
	.word	233
	.byte	1,1,2,35,2,8
	.byte	'reserved_23',0,2
	.word	264
	.byte	4,5,2,35,2,8
	.byte	'ESR0TRIST',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'reserved_28',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'WUTEN',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'WUTMODE',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'WUTRUN',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,226,6,3
	.word	25624
	.byte	7
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,229,6,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	2,6,2,35,0,8
	.byte	'ESR1WKPCLR',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'ESR1OVRUNCLR',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'PINAWKPCLR',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'PINAOVRUNCLR',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'PINBWKPCLR',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PINBOVRUNCLR',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'WUTWKPCLR',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'WUTOVRUNCLR',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,2
	.word	264
	.byte	14,0,2,35,2,0,4
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,242,6,3
	.word	26191
	.byte	7
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,4,245,6,16,4,8
	.byte	'WUTCNT',0,4
	.word	923
	.byte	24,8,2,35,2,8
	.byte	'reserved_24',0,1
	.word	233
	.byte	7,1,2,35,3,8
	.byte	'VAL',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,4,250,6,3
	.word	26507
	.byte	7
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,253,6,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'CLRC',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,2
	.word	264
	.byte	10,4,2,35,0,8
	.byte	'CSS0',0,1
	.word	233
	.byte	1,3,2,35,1,8
	.byte	'CSS1',0,1
	.word	233
	.byte	1,2,2,35,1,8
	.byte	'CSS2',0,1
	.word	233
	.byte	1,1,2,35,1,8
	.byte	'reserved_15',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'USRINFO',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,135,7,3
	.word	26626
	.byte	7
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,138,7,16,4,8
	.byte	'ESR0',0,1
	.word	233
	.byte	2,6,2,35,0,8
	.byte	'ESR1',0,1
	.word	233
	.byte	2,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	233
	.byte	2,2,2,35,0,8
	.byte	'SMU',0,1
	.word	233
	.byte	2,0,2,35,0,8
	.byte	'SW',0,1
	.word	233
	.byte	2,6,2,35,1,8
	.byte	'STM0',0,1
	.word	233
	.byte	2,4,2,35,1,8
	.byte	'STM1',0,1
	.word	233
	.byte	2,2,2,35,1,8
	.byte	'STM2',0,1
	.word	233
	.byte	2,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,149,7,3
	.word	26835
	.byte	7
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,152,7,16,4,8
	.byte	'ESR0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'ESR1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'SMU',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'SW',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'STM0',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'STM1',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'STM2',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	8,0,2,35,1,8
	.byte	'PORST',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'reserved_17',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'CB0',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'CB1',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'CB3',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	233
	.byte	2,1,2,35,2,8
	.byte	'EVR13',0,1
	.word	233
	.byte	1,0,2,35,2,8
	.byte	'EVR33',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'SWD',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'reserved_26',0,1
	.word	233
	.byte	2,4,2,35,3,8
	.byte	'STBYR',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'reserved_29',0,1
	.word	233
	.byte	3,0,2,35,3,0,4
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,175,7,3
	.word	27046
	.byte	7
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,178,7,16,4,8
	.byte	'HBT',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,4
	.word	923
	.byte	31,0,2,35,2,0,4
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,182,7,3
	.word	27478
	.byte	7
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,185,7,16,4,8
	.byte	'HWCFG',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'FTM',0,1
	.word	233
	.byte	7,1,2,35,1,8
	.byte	'MODE',0,1
	.word	233
	.byte	1,0,2,35,1,8
	.byte	'FCBAE',0,1
	.word	233
	.byte	1,7,2,35,2,8
	.byte	'LUDIS',0,1
	.word	233
	.byte	1,6,2,35,2,8
	.byte	'reserved_18',0,1
	.word	233
	.byte	1,5,2,35,2,8
	.byte	'TRSTL',0,1
	.word	233
	.byte	1,4,2,35,2,8
	.byte	'SPDEN',0,1
	.word	233
	.byte	1,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	233
	.byte	3,0,2,35,2,8
	.byte	'RAMINT',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'reserved_25',0,1
	.word	233
	.byte	7,0,2,35,3,0,4
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,198,7,3
	.word	27574
	.byte	7
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,201,7,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'SWRSTREQ',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,4
	.word	923
	.byte	30,0,2,35,2,0,4
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,206,7,3
	.word	27834
	.byte	7
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,209,7,16,4,8
	.byte	'CCTRIG0',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'RAMINTM',0,1
	.word	233
	.byte	2,4,2,35,0,8
	.byte	'SETLUDIS',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'reserved_5',0,1
	.word	233
	.byte	3,0,2,35,0,8
	.byte	'DATM',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'reserved_9',0,4
	.word	923
	.byte	23,0,2,35,2,0,4
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,218,7,3
	.word	27959
	.byte	7
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,221,7,16,4,8
	.byte	'ESR0T',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	923
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,228,7,3
	.word	28156
	.byte	7
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,231,7,16,4,8
	.byte	'ESR0T',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	923
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,238,7,3
	.word	28309
	.byte	7
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,241,7,16,4,8
	.byte	'ESR0T',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	923
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,248,7,3
	.word	28462
	.byte	7
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,251,7,16,4,8
	.byte	'ESR0T',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'ESR1T',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'reserved_2',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'SMUT',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,4
	.word	923
	.byte	28,0,2,35,2,0,4
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,130,8,3
	.word	28615
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,133,8,16,4,8
	.byte	'ENDINIT',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'LCK',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'PW',0,4
	.word	7908
	.byte	14,16,2,35,0,8
	.byte	'REL',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,139,8,3
	.word	28770
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,142,8,16,4,8
	.byte	'reserved_0',0,1
	.word	233
	.byte	2,6,2,35,0,8
	.byte	'IR0',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'DR',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'IR1',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'UR',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PAR',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'TCR',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'TCTR',0,1
	.word	233
	.byte	7,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,154,8,3
	.word	28900
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,157,8,16,4,8
	.byte	'AE',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'OE',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'IS0',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'DS',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'TO',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'IS1',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'US',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PAS',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'TCS',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'TCT',0,1
	.word	233
	.byte	7,0,2,35,1,8
	.byte	'TIM',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,170,8,3
	.word	29138
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,173,8,16,4,8
	.byte	'ENDINIT',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'LCK',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'PW',0,4
	.word	7908
	.byte	14,16,2,35,0,8
	.byte	'REL',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,179,8,3
	.word	29361
	.byte	7
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,182,8,16,4,8
	.byte	'CLRIRF',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'reserved_1',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'IR0',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'DR',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'reserved_4',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'IR1',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'UR',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PAR',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'TCR',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'TCTR',0,1
	.word	233
	.byte	7,0,2,35,1,8
	.byte	'reserved_16',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,195,8,3
	.word	29487
	.byte	7
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,198,8,16,4,8
	.byte	'AE',0,1
	.word	233
	.byte	1,7,2,35,0,8
	.byte	'OE',0,1
	.word	233
	.byte	1,6,2,35,0,8
	.byte	'IS0',0,1
	.word	233
	.byte	1,5,2,35,0,8
	.byte	'DS',0,1
	.word	233
	.byte	1,4,2,35,0,8
	.byte	'TO',0,1
	.word	233
	.byte	1,3,2,35,0,8
	.byte	'IS1',0,1
	.word	233
	.byte	1,2,2,35,0,8
	.byte	'US',0,1
	.word	233
	.byte	1,1,2,35,0,8
	.byte	'PAS',0,1
	.word	233
	.byte	1,0,2,35,0,8
	.byte	'TCS',0,1
	.word	233
	.byte	1,7,2,35,1,8
	.byte	'TCT',0,1
	.word	233
	.byte	7,0,2,35,1,8
	.byte	'TIM',0,2
	.word	264
	.byte	16,0,2,35,2,0,4
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,211,8,3
	.word	29739
	.byte	9,4,219,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13034
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ACCEN0',0,4,224,8,3
	.word	29958
	.byte	9,4,227,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13591
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ACCEN1',0,4,232,8,3
	.word	30022
	.byte	9,4,235,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13668
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ARSTDIS',0,4,240,8,3
	.word	30086
	.byte	9,4,243,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	13804
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON0',0,4,248,8,3
	.word	30151
	.byte	9,4,251,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14086
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON1',0,4,128,9,3
	.word	30216
	.byte	9,4,131,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14324
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON2',0,4,136,9,3
	.word	30281
	.byte	9,4,139,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14452
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON3',0,4,144,9,3
	.word	30346
	.byte	9,4,147,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14679
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON4',0,4,152,9,3
	.word	30411
	.byte	9,4,155,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	14898
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON5',0,4,160,9,3
	.word	30476
	.byte	9,4,163,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15026
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CCUCON6',0,4,168,9,3
	.word	30541
	.byte	9,4,171,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15126
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_CHIPID',0,4,176,9,3
	.word	30606
	.byte	9,4,179,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15334
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSCON',0,4,184,9,3
	.word	30670
	.byte	9,4,187,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15499
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSLIM',0,4,192,9,3
	.word	30734
	.byte	9,4,195,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15682
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_DTSSTAT',0,4,200,9,3
	.word	30798
	.byte	9,4,203,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	15836
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EICR',0,4,208,9,3
	.word	30863
	.byte	9,4,211,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16200
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EIFR',0,4,216,9,3
	.word	30925
	.byte	9,4,219,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16411
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EMSR',0,4,224,9,3
	.word	30987
	.byte	9,4,227,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16663
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ESRCFG',0,4,232,9,3
	.word	31049
	.byte	9,4,235,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16781
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ESROCFG',0,4,240,9,3
	.word	31113
	.byte	9,4,243,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	16892
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVR13CON',0,4,248,9,3
	.word	31178
	.byte	9,4,251,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17055
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,128,10,3
	.word	31244
	.byte	9,4,131,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17217
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,136,10,3
	.word	31312
	.byte	9,4,139,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17495
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVROVMON',0,4,144,10,3
	.word	31380
	.byte	9,4,147,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17674
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRRSTCON',0,4,152,10,3
	.word	31446
	.byte	9,4,155,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17834
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,160,10,3
	.word	31513
	.byte	9,4,163,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	17995
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,168,10,3
	.word	31582
	.byte	9,4,171,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18187
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,176,10,3
	.word	31650
	.byte	9,4,179,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18483
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,184,10,3
	.word	31718
	.byte	9,4,187,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18698
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRSTAT',0,4,192,10,3
	.word	31786
	.byte	9,4,195,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	18987
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EVRUVMON',0,4,200,10,3
	.word	31851
	.byte	9,4,203,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19166
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_EXTCON',0,4,208,10,3
	.word	31917
	.byte	9,4,211,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19384
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_FDR',0,4,216,10,3
	.word	31981
	.byte	9,4,219,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19547
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_FMR',0,4,224,10,3
	.word	32042
	.byte	9,4,227,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19883
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_ID',0,4,232,10,3
	.word	32103
	.byte	9,4,235,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	19990
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IGCR',0,4,240,10,3
	.word	32163
	.byte	9,4,243,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20442
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IN',0,4,248,10,3
	.word	32225
	.byte	9,4,251,10,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20541
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_IOCR',0,4,128,11,3
	.word	32285
	.byte	9,4,131,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20691
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,136,11,3
	.word	32347
	.byte	9,4,139,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	20840
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,144,11,3
	.word	32415
	.byte	9,4,147,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21001
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,152,11,3
	.word	32483
	.byte	9,4,155,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21131
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LCLCON0',0,4,160,11,3
	.word	32551
	.byte	9,4,163,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21265
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_LCLTEST',0,4,168,11,3
	.word	32616
	.byte	9,4,171,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21380
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_MANID',0,4,176,11,3
	.word	32681
	.byte	9,4,179,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21491
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OMR',0,4,184,11,3
	.word	32744
	.byte	9,4,187,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21649
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OSCCON',0,4,192,11,3
	.word	32805
	.byte	9,4,195,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	21989
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OUT',0,4,200,11,3
	.word	32869
	.byte	9,4,203,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22090
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OVCCON',0,4,208,11,3
	.word	32930
	.byte	9,4,211,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22357
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_OVCENABLE',0,4,216,11,3
	.word	32994
	.byte	9,4,219,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22493
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDISC',0,4,224,11,3
	.word	33061
	.byte	9,4,227,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22604
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDR',0,4,232,11,3
	.word	33124
	.byte	9,4,235,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22737
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PDRR',0,4,240,11,3
	.word	33185
	.byte	9,4,243,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	22940
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON0',0,4,248,11,3
	.word	33247
	.byte	9,4,251,11,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23296
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON1',0,4,128,12,3
	.word	33312
	.byte	9,4,131,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23474
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLCON2',0,4,136,12,3
	.word	33377
	.byte	9,4,139,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23574
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,144,12,3
	.word	33442
	.byte	9,4,147,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	23944
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,152,12,3
	.word	33511
	.byte	9,4,155,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24130
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,160,12,3
	.word	33580
	.byte	9,4,163,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24328
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PLLSTAT',0,4,168,12,3
	.word	33649
	.byte	9,4,171,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24561
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMCSR',0,4,176,12,3
	.word	33714
	.byte	9,4,179,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	24713
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR0',0,4,184,12,3
	.word	33777
	.byte	9,4,187,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25272
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR1',0,4,192,12,3
	.word	33842
	.byte	9,4,195,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25455
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWCR3',0,4,200,12,3
	.word	33907
	.byte	9,4,203,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	25624
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWSTAT',0,4,208,12,3
	.word	33972
	.byte	9,4,211,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26191
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,216,12,3
	.word	34038
	.byte	9,4,219,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26507
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_PMSWUTCNT',0,4,224,12,3
	.word	34107
	.byte	9,4,227,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26835
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTCON',0,4,232,12,3
	.word	34174
	.byte	9,4,235,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	26626
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTCON2',0,4,240,12,3
	.word	34238
	.byte	9,4,243,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27046
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_RSTSTAT',0,4,248,12,3
	.word	34303
	.byte	9,4,251,12,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27478
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SAFECON',0,4,128,13,3
	.word	34368
	.byte	9,4,131,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27574
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_STSTAT',0,4,136,13,3
	.word	34433
	.byte	9,4,139,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27834
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SWRSTCON',0,4,144,13,3
	.word	34497
	.byte	9,4,147,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	27959
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_SYSCON',0,4,152,13,3
	.word	34563
	.byte	9,4,155,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28156
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPCLR',0,4,160,13,3
	.word	34627
	.byte	9,4,163,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28309
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPDIS',0,4,168,13,3
	.word	34692
	.byte	9,4,171,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28462
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPSET',0,4,176,13,3
	.word	34757
	.byte	9,4,179,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28615
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_TRAPSTAT',0,4,184,13,3
	.word	34822
	.byte	9,4,187,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28770
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,192,13,3
	.word	34888
	.byte	9,4,195,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	28900
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,200,13,3
	.word	34957
	.byte	9,4,203,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29138
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,208,13,3
	.word	35026
	.byte	9,4,211,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29361
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON0',0,4,216,13,3
	.word	35093
	.byte	9,4,219,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29487
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_CON1',0,4,224,13,3
	.word	35160
	.byte	9,4,227,13,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	29739
	.byte	2,35,0,0,4
	.byte	'Ifx_SCU_WDTS_SR',0,4,232,13,3
	.word	35227
	.byte	7
	.byte	'_Ifx_SCU_WDTCPU',0,4,243,13,25,12,10
	.byte	'CON0',0,4
	.word	34888
	.byte	2,35,0,10
	.byte	'CON1',0,4
	.word	34957
	.byte	2,35,4,10
	.byte	'SR',0,4
	.word	35026
	.byte	2,35,8,0,13
	.word	35292
	.byte	4
	.byte	'Ifx_SCU_WDTCPU',0,4,248,13,3
	.word	35355
	.byte	7
	.byte	'_Ifx_SCU_WDTS',0,4,251,13,25,12,10
	.byte	'CON0',0,4
	.word	35093
	.byte	2,35,0,10
	.byte	'CON1',0,4
	.word	35160
	.byte	2,35,4,10
	.byte	'SR',0,4
	.word	35227
	.byte	2,35,8,0,13
	.word	35384
	.byte	4
	.byte	'Ifx_SCU_WDTS',0,4,128,14,3
	.word	35445
	.byte	7
	.byte	'_Ifx_CPU_A_Bits',0,5,45,16,4,8
	.byte	'ADDR',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_A_Bits',0,5,48,3
	.word	35472
	.byte	7
	.byte	'_Ifx_CPU_BIV_Bits',0,5,51,16,4,8
	.byte	'VSS',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'BIV',0,4
	.word	7908
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_BIV_Bits',0,5,55,3
	.word	35533
	.byte	7
	.byte	'_Ifx_CPU_BTV_Bits',0,5,58,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'BTV',0,4
	.word	7908
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_BTV_Bits',0,5,62,3
	.word	35612
	.byte	7
	.byte	'_Ifx_CPU_CCNT_Bits',0,5,65,16,4,8
	.byte	'CountValue',0,4
	.word	7908
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7908
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_CCNT_Bits',0,5,69,3
	.word	35698
	.byte	7
	.byte	'_Ifx_CPU_CCTRL_Bits',0,5,72,16,4,8
	.byte	'CM',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'CE',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'M1',0,4
	.word	7908
	.byte	3,27,2,35,0,8
	.byte	'M2',0,4
	.word	7908
	.byte	3,24,2,35,0,8
	.byte	'M3',0,4
	.word	7908
	.byte	3,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	7908
	.byte	21,0,2,35,0,0,4
	.byte	'Ifx_CPU_CCTRL_Bits',0,5,80,3
	.word	35787
	.byte	7
	.byte	'_Ifx_CPU_COMPAT_Bits',0,5,83,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'RM',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'SP',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7908
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_COMPAT_Bits',0,5,89,3
	.word	35933
	.byte	7
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,5,92,16,4,8
	.byte	'CORE_ID',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7908
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CORE_ID_Bits',0,5,96,3
	.word	36060
	.byte	7
	.byte	'_Ifx_CPU_CPR_L_Bits',0,5,99,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'LOWBND',0,4
	.word	7908
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPR_L_Bits',0,5,103,3
	.word	36158
	.byte	7
	.byte	'_Ifx_CPU_CPR_U_Bits',0,5,106,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'UPPBND',0,4
	.word	7908
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPR_U_Bits',0,5,110,3
	.word	36251
	.byte	7
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,5,113,16,4,8
	.byte	'MODREV',0,4
	.word	7908
	.byte	8,24,2,35,0,8
	.byte	'MOD_32B',0,4
	.word	7908
	.byte	8,16,2,35,0,8
	.byte	'MOD',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPU_ID_Bits',0,5,118,3
	.word	36344
	.byte	7
	.byte	'_Ifx_CPU_CPXE_Bits',0,5,121,16,4,8
	.byte	'XE',0,4
	.word	7908
	.byte	8,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7908
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_CPXE_Bits',0,5,125,3
	.word	36451
	.byte	7
	.byte	'_Ifx_CPU_CREVT_Bits',0,5,128,1,16,4,8
	.byte	'EVTA',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'BBM',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'BOD',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'SUSP',0,4
	.word	7908
	.byte	1,26,2,35,0,8
	.byte	'CNT',0,4
	.word	7908
	.byte	2,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7908
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_CREVT_Bits',0,5,136,1,3
	.word	36538
	.byte	7
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,5,139,1,16,4,8
	.byte	'CID',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7908
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_CUS_ID_Bits',0,5,143,1,3
	.word	36692
	.byte	7
	.byte	'_Ifx_CPU_D_Bits',0,5,146,1,16,4,8
	.byte	'DATA',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_D_Bits',0,5,149,1,3
	.word	36786
	.byte	7
	.byte	'_Ifx_CPU_DATR_Bits',0,5,152,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'SBE',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'reserved_4',0,4
	.word	7908
	.byte	5,23,2,35,0,8
	.byte	'CWE',0,4
	.word	7908
	.byte	1,22,2,35,0,8
	.byte	'CFE',0,4
	.word	7908
	.byte	1,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	7908
	.byte	3,18,2,35,0,8
	.byte	'SOE',0,4
	.word	7908
	.byte	1,17,2,35,0,8
	.byte	'SME',0,4
	.word	7908
	.byte	1,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DATR_Bits',0,5,163,1,3
	.word	36849
	.byte	7
	.byte	'_Ifx_CPU_DBGSR_Bits',0,5,166,1,16,4,8
	.byte	'DE',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'HALT',0,4
	.word	7908
	.byte	2,29,2,35,0,8
	.byte	'SIH',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'SUSP',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7908
	.byte	1,26,2,35,0,8
	.byte	'PREVSUSP',0,4
	.word	7908
	.byte	1,25,2,35,0,8
	.byte	'PEVT',0,4
	.word	7908
	.byte	1,24,2,35,0,8
	.byte	'EVTSRC',0,4
	.word	7908
	.byte	5,19,2,35,0,8
	.byte	'reserved_13',0,4
	.word	7908
	.byte	19,0,2,35,0,0,4
	.byte	'Ifx_CPU_DBGSR_Bits',0,5,177,1,3
	.word	37067
	.byte	7
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,5,180,1,16,4,8
	.byte	'DTA',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	7908
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_DBGTCR_Bits',0,5,184,1,3
	.word	37282
	.byte	7
	.byte	'_Ifx_CPU_DCON0_Bits',0,5,187,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'DCBYP',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	7908
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCON0_Bits',0,5,192,1,3
	.word	37376
	.byte	7
	.byte	'_Ifx_CPU_DCON2_Bits',0,5,195,1,16,4,8
	.byte	'DCACHE_SZE',0,4
	.word	7908
	.byte	16,16,2,35,0,8
	.byte	'DSCRATCH_SZE',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCON2_Bits',0,5,199,1,3
	.word	37492
	.byte	7
	.byte	'_Ifx_CPU_DCX_Bits',0,5,202,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	6,26,2,35,0,8
	.byte	'DCXValue',0,4
	.word	7908
	.byte	26,0,2,35,0,0,4
	.byte	'Ifx_CPU_DCX_Bits',0,5,206,1,3
	.word	37593
	.byte	7
	.byte	'_Ifx_CPU_DEADD_Bits',0,5,209,1,16,4,8
	.byte	'ERROR_ADDRESS',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_DEADD_Bits',0,5,212,1,3
	.word	37686
	.byte	7
	.byte	'_Ifx_CPU_DIEAR_Bits',0,5,215,1,16,4,8
	.byte	'TA',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_DIEAR_Bits',0,5,218,1,3
	.word	37766
	.byte	7
	.byte	'_Ifx_CPU_DIETR_Bits',0,5,221,1,16,4,8
	.byte	'IED',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'IE_T',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'IE_C',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'IE_S',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'IE_BI',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'E_INFO',0,4
	.word	7908
	.byte	6,21,2,35,0,8
	.byte	'IE_DUAL',0,4
	.word	7908
	.byte	1,20,2,35,0,8
	.byte	'IE_SP',0,4
	.word	7908
	.byte	1,19,2,35,0,8
	.byte	'IE_BS',0,4
	.word	7908
	.byte	1,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	7908
	.byte	18,0,2,35,0,0,4
	.byte	'Ifx_CPU_DIETR_Bits',0,5,233,1,3
	.word	37835
	.byte	7
	.byte	'_Ifx_CPU_DMS_Bits',0,5,236,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'DMSValue',0,4
	.word	7908
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_DMS_Bits',0,5,240,1,3
	.word	38064
	.byte	7
	.byte	'_Ifx_CPU_DPR_L_Bits',0,5,243,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'LOWBND',0,4
	.word	7908
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPR_L_Bits',0,5,247,1,3
	.word	38157
	.byte	7
	.byte	'_Ifx_CPU_DPR_U_Bits',0,5,250,1,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'UPPBND',0,4
	.word	7908
	.byte	29,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPR_U_Bits',0,5,254,1,3
	.word	38252
	.byte	7
	.byte	'_Ifx_CPU_DPRE_Bits',0,5,129,2,16,4,8
	.byte	'RE',0,4
	.word	7908
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPRE_Bits',0,5,133,2,3
	.word	38347
	.byte	7
	.byte	'_Ifx_CPU_DPWE_Bits',0,5,136,2,16,4,8
	.byte	'WE',0,4
	.word	7908
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_DPWE_Bits',0,5,140,2,3
	.word	38437
	.byte	7
	.byte	'_Ifx_CPU_DSTR_Bits',0,5,143,2,16,4,8
	.byte	'SRE',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'GAE',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'LBE',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7908
	.byte	3,26,2,35,0,8
	.byte	'CRE',0,4
	.word	7908
	.byte	1,25,2,35,0,8
	.byte	'reserved_7',0,4
	.word	7908
	.byte	7,18,2,35,0,8
	.byte	'DTME',0,4
	.word	7908
	.byte	1,17,2,35,0,8
	.byte	'LOE',0,4
	.word	7908
	.byte	1,16,2,35,0,8
	.byte	'SDE',0,4
	.word	7908
	.byte	1,15,2,35,0,8
	.byte	'SCE',0,4
	.word	7908
	.byte	1,14,2,35,0,8
	.byte	'CAC',0,4
	.word	7908
	.byte	1,13,2,35,0,8
	.byte	'MPE',0,4
	.word	7908
	.byte	1,12,2,35,0,8
	.byte	'CLE',0,4
	.word	7908
	.byte	1,11,2,35,0,8
	.byte	'reserved_21',0,4
	.word	7908
	.byte	3,8,2,35,0,8
	.byte	'ALN',0,4
	.word	7908
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	7908
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_CPU_DSTR_Bits',0,5,161,2,3
	.word	38527
	.byte	7
	.byte	'_Ifx_CPU_EXEVT_Bits',0,5,164,2,16,4,8
	.byte	'EVTA',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'BBM',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'BOD',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'SUSP',0,4
	.word	7908
	.byte	1,26,2,35,0,8
	.byte	'CNT',0,4
	.word	7908
	.byte	2,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7908
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_EXEVT_Bits',0,5,172,2,3
	.word	38851
	.byte	7
	.byte	'_Ifx_CPU_FCX_Bits',0,5,175,2,16,4,8
	.byte	'FCXO',0,4
	.word	7908
	.byte	16,16,2,35,0,8
	.byte	'FCXS',0,4
	.word	7908
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	7908
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_FCX_Bits',0,5,180,2,3
	.word	39005
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,5,183,2,16,4,8
	.byte	'TST',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'TCL',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	7908
	.byte	6,24,2,35,0,8
	.byte	'RM',0,4
	.word	7908
	.byte	2,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	7908
	.byte	8,14,2,35,0,8
	.byte	'FXE',0,4
	.word	7908
	.byte	1,13,2,35,0,8
	.byte	'FUE',0,4
	.word	7908
	.byte	1,12,2,35,0,8
	.byte	'FZE',0,4
	.word	7908
	.byte	1,11,2,35,0,8
	.byte	'FVE',0,4
	.word	7908
	.byte	1,10,2,35,0,8
	.byte	'FIE',0,4
	.word	7908
	.byte	1,9,2,35,0,8
	.byte	'reserved_23',0,4
	.word	7908
	.byte	3,6,2,35,0,8
	.byte	'FX',0,4
	.word	7908
	.byte	1,5,2,35,0,8
	.byte	'FU',0,4
	.word	7908
	.byte	1,4,2,35,0,8
	.byte	'FZ',0,4
	.word	7908
	.byte	1,3,2,35,0,8
	.byte	'FV',0,4
	.word	7908
	.byte	1,2,2,35,0,8
	.byte	'FI',0,4
	.word	7908
	.byte	1,1,2,35,0,8
	.byte	'reserved_31',0,4
	.word	7908
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,5,202,2,3
	.word	39111
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,5,205,2,16,4,8
	.byte	'OPC',0,4
	.word	7908
	.byte	8,24,2,35,0,8
	.byte	'FMT',0,4
	.word	7908
	.byte	1,23,2,35,0,8
	.byte	'reserved_9',0,4
	.word	7908
	.byte	7,16,2,35,0,8
	.byte	'DREG',0,4
	.word	7908
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	7908
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,5,212,2,3
	.word	39460
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,5,215,2,16,4,8
	.byte	'PC',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,5,218,2,3
	.word	39620
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,5,221,2,16,4,8
	.byte	'SRC1',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,5,224,2,3
	.word	39701
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,5,227,2,16,4,8
	.byte	'SRC2',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,5,230,2,3
	.word	39788
	.byte	7
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,5,233,2,16,4,8
	.byte	'SRC3',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,5,236,2,3
	.word	39875
	.byte	7
	.byte	'_Ifx_CPU_ICNT_Bits',0,5,239,2,16,4,8
	.byte	'CountValue',0,4
	.word	7908
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7908
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_ICNT_Bits',0,5,243,2,3
	.word	39962
	.byte	7
	.byte	'_Ifx_CPU_ICR_Bits',0,5,246,2,16,4,8
	.byte	'CCPN',0,4
	.word	7908
	.byte	10,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	7908
	.byte	5,17,2,35,0,8
	.byte	'IE',0,4
	.word	7908
	.byte	1,16,2,35,0,8
	.byte	'PIPN',0,4
	.word	7908
	.byte	10,6,2,35,0,8
	.byte	'reserved_26',0,4
	.word	7908
	.byte	6,0,2,35,0,0,4
	.byte	'Ifx_CPU_ICR_Bits',0,5,253,2,3
	.word	40053
	.byte	7
	.byte	'_Ifx_CPU_ISP_Bits',0,5,128,3,16,4,8
	.byte	'ISP',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_ISP_Bits',0,5,131,3,3
	.word	40196
	.byte	7
	.byte	'_Ifx_CPU_LCX_Bits',0,5,134,3,16,4,8
	.byte	'LCXO',0,4
	.word	7908
	.byte	16,16,2,35,0,8
	.byte	'LCXS',0,4
	.word	7908
	.byte	4,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	7908
	.byte	12,0,2,35,0,0,4
	.byte	'Ifx_CPU_LCX_Bits',0,5,139,3,3
	.word	40262
	.byte	7
	.byte	'_Ifx_CPU_M1CNT_Bits',0,5,142,3,16,4,8
	.byte	'CountValue',0,4
	.word	7908
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7908
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M1CNT_Bits',0,5,146,3,3
	.word	40368
	.byte	7
	.byte	'_Ifx_CPU_M2CNT_Bits',0,5,149,3,16,4,8
	.byte	'CountValue',0,4
	.word	7908
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7908
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M2CNT_Bits',0,5,153,3,3
	.word	40461
	.byte	7
	.byte	'_Ifx_CPU_M3CNT_Bits',0,5,156,3,16,4,8
	.byte	'CountValue',0,4
	.word	7908
	.byte	31,1,2,35,0,8
	.byte	'SOvf',0,4
	.word	7908
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_M3CNT_Bits',0,5,160,3,3
	.word	40554
	.byte	7
	.byte	'_Ifx_CPU_PC_Bits',0,5,163,3,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'PC',0,4
	.word	7908
	.byte	31,0,2,35,0,0,4
	.byte	'Ifx_CPU_PC_Bits',0,5,167,3,3
	.word	40647
	.byte	7
	.byte	'_Ifx_CPU_PCON0_Bits',0,5,170,3,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'PCBYP',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	7908
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON0_Bits',0,5,175,3,3
	.word	40732
	.byte	7
	.byte	'_Ifx_CPU_PCON1_Bits',0,5,178,3,16,4,8
	.byte	'PCINV',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'PBINV',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	7908
	.byte	30,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON1_Bits',0,5,183,3,3
	.word	40848
	.byte	7
	.byte	'_Ifx_CPU_PCON2_Bits',0,5,186,3,16,4,8
	.byte	'PCACHE_SZE',0,4
	.word	7908
	.byte	16,16,2,35,0,8
	.byte	'PSCRATCH_SZE',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCON2_Bits',0,5,190,3,3
	.word	40959
	.byte	7
	.byte	'_Ifx_CPU_PCXI_Bits',0,5,193,3,16,4,8
	.byte	'PCXO',0,4
	.word	7908
	.byte	16,16,2,35,0,8
	.byte	'PCXS',0,4
	.word	7908
	.byte	4,12,2,35,0,8
	.byte	'UL',0,4
	.word	7908
	.byte	1,11,2,35,0,8
	.byte	'PIE',0,4
	.word	7908
	.byte	1,10,2,35,0,8
	.byte	'PCPN',0,4
	.word	7908
	.byte	10,0,2,35,0,0,4
	.byte	'Ifx_CPU_PCXI_Bits',0,5,200,3,3
	.word	41060
	.byte	7
	.byte	'_Ifx_CPU_PIEAR_Bits',0,5,203,3,16,4,8
	.byte	'TA',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_PIEAR_Bits',0,5,206,3,3
	.word	41190
	.byte	7
	.byte	'_Ifx_CPU_PIETR_Bits',0,5,209,3,16,4,8
	.byte	'IED',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'IE_T',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'IE_C',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'IE_S',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'IE_BI',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'E_INFO',0,4
	.word	7908
	.byte	6,21,2,35,0,8
	.byte	'IE_DUAL',0,4
	.word	7908
	.byte	1,20,2,35,0,8
	.byte	'IE_SP',0,4
	.word	7908
	.byte	1,19,2,35,0,8
	.byte	'IE_BS',0,4
	.word	7908
	.byte	1,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	7908
	.byte	18,0,2,35,0,0,4
	.byte	'Ifx_CPU_PIETR_Bits',0,5,221,3,3
	.word	41259
	.byte	7
	.byte	'_Ifx_CPU_PMA0_Bits',0,5,224,3,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	13,19,2,35,0,8
	.byte	'DAC',0,4
	.word	7908
	.byte	3,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA0_Bits',0,5,229,3,3
	.word	41488
	.byte	7
	.byte	'_Ifx_CPU_PMA1_Bits',0,5,232,3,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	14,18,2,35,0,8
	.byte	'CAC',0,4
	.word	7908
	.byte	2,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA1_Bits',0,5,237,3,3
	.word	41601
	.byte	7
	.byte	'_Ifx_CPU_PMA2_Bits',0,5,240,3,16,4,8
	.byte	'PSI',0,4
	.word	7908
	.byte	16,16,2,35,0,8
	.byte	'reserved_16',0,4
	.word	7908
	.byte	16,0,2,35,0,0,4
	.byte	'Ifx_CPU_PMA2_Bits',0,5,244,3,3
	.word	41714
	.byte	7
	.byte	'_Ifx_CPU_PSTR_Bits',0,5,247,3,16,4,8
	.byte	'FRE',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'FBE',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7908
	.byte	9,20,2,35,0,8
	.byte	'FPE',0,4
	.word	7908
	.byte	1,19,2,35,0,8
	.byte	'reserved_13',0,4
	.word	7908
	.byte	1,18,2,35,0,8
	.byte	'FME',0,4
	.word	7908
	.byte	1,17,2,35,0,8
	.byte	'reserved_15',0,4
	.word	7908
	.byte	17,0,2,35,0,0,4
	.byte	'Ifx_CPU_PSTR_Bits',0,5,129,4,3
	.word	41805
	.byte	7
	.byte	'_Ifx_CPU_PSW_Bits',0,5,132,4,16,4,8
	.byte	'CDC',0,4
	.word	7908
	.byte	7,25,2,35,0,8
	.byte	'CDE',0,4
	.word	7908
	.byte	1,24,2,35,0,8
	.byte	'GW',0,4
	.word	7908
	.byte	1,23,2,35,0,8
	.byte	'IS',0,4
	.word	7908
	.byte	1,22,2,35,0,8
	.byte	'IO',0,4
	.word	7908
	.byte	2,20,2,35,0,8
	.byte	'PRS',0,4
	.word	7908
	.byte	2,18,2,35,0,8
	.byte	'S',0,4
	.word	7908
	.byte	1,17,2,35,0,8
	.byte	'reserved_15',0,4
	.word	7908
	.byte	12,5,2,35,0,8
	.byte	'SAV',0,4
	.word	7908
	.byte	1,4,2,35,0,8
	.byte	'AV',0,4
	.word	7908
	.byte	1,3,2,35,0,8
	.byte	'SV',0,4
	.word	7908
	.byte	1,2,2,35,0,8
	.byte	'V',0,4
	.word	7908
	.byte	1,1,2,35,0,8
	.byte	'C',0,4
	.word	7908
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_PSW_Bits',0,5,147,4,3
	.word	42008
	.byte	7
	.byte	'_Ifx_CPU_SEGEN_Bits',0,5,150,4,16,4,8
	.byte	'ADFLIP',0,4
	.word	7908
	.byte	8,24,2,35,0,8
	.byte	'ADTYPE',0,4
	.word	7908
	.byte	2,22,2,35,0,8
	.byte	'reserved_10',0,4
	.word	7908
	.byte	21,1,2,35,0,8
	.byte	'AE',0,4
	.word	7908
	.byte	1,0,2,35,0,0,4
	.byte	'Ifx_CPU_SEGEN_Bits',0,5,156,4,3
	.word	42251
	.byte	7
	.byte	'_Ifx_CPU_SMACON_Bits',0,5,159,4,16,4,8
	.byte	'PC',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'reserved_1',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'PT',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7908
	.byte	5,24,2,35,0,8
	.byte	'DC',0,4
	.word	7908
	.byte	1,23,2,35,0,8
	.byte	'reserved_9',0,4
	.word	7908
	.byte	1,22,2,35,0,8
	.byte	'DT',0,4
	.word	7908
	.byte	1,21,2,35,0,8
	.byte	'reserved_11',0,4
	.word	7908
	.byte	13,8,2,35,0,8
	.byte	'IODT',0,4
	.word	7908
	.byte	1,7,2,35,0,8
	.byte	'reserved_25',0,4
	.word	7908
	.byte	7,0,2,35,0,0,4
	.byte	'Ifx_CPU_SMACON_Bits',0,5,171,4,3
	.word	42379
	.byte	7
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,5,174,4,16,4,8
	.byte	'EN',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,5,177,4,3
	.word	42620
	.byte	7
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,5,180,4,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,5,183,4,3
	.word	42703
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,5,186,4,16,4,8
	.byte	'EN',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,5,189,4,3
	.word	42794
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,5,192,4,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,5,195,4,3
	.word	42885
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,5,198,4,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	5,27,2,35,0,8
	.byte	'ADDR',0,4
	.word	7908
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,5,202,4,3
	.word	42984
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,5,205,4,16,4,8
	.byte	'reserved_0',0,4
	.word	7908
	.byte	5,27,2,35,0,8
	.byte	'ADDR',0,4
	.word	7908
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,5,209,4,3
	.word	43091
	.byte	7
	.byte	'_Ifx_CPU_SWEVT_Bits',0,5,212,4,16,4,8
	.byte	'EVTA',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'BBM',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'BOD',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'SUSP',0,4
	.word	7908
	.byte	1,26,2,35,0,8
	.byte	'CNT',0,4
	.word	7908
	.byte	2,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7908
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_SWEVT_Bits',0,5,220,4,3
	.word	43198
	.byte	7
	.byte	'_Ifx_CPU_SYSCON_Bits',0,5,223,4,16,4,8
	.byte	'FCDSF',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'PROTEN',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'TPROTEN',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'IS',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'IT',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7908
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_SYSCON_Bits',0,5,231,4,3
	.word	43352
	.byte	7
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,5,234,4,16,4,8
	.byte	'ASI',0,4
	.word	7908
	.byte	5,27,2,35,0,8
	.byte	'reserved_5',0,4
	.word	7908
	.byte	27,0,2,35,0,0,4
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,5,238,4,3
	.word	43513
	.byte	7
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,5,241,4,16,4,8
	.byte	'TEXP0',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'TEXP1',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'TEXP2',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'reserved_3',0,4
	.word	7908
	.byte	13,16,2,35,0,8
	.byte	'TTRAP',0,4
	.word	7908
	.byte	1,15,2,35,0,8
	.byte	'reserved_17',0,4
	.word	7908
	.byte	15,0,2,35,0,0,4
	.byte	'Ifx_CPU_TPS_CON_Bits',0,5,249,4,3
	.word	43611
	.byte	7
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,5,252,4,16,4,8
	.byte	'Timer',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,5,255,4,3
	.word	43783
	.byte	7
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,5,130,5,16,4,8
	.byte	'ADDR',0,4
	.word	7908
	.byte	32,0,2,35,0,0,4
	.byte	'Ifx_CPU_TR_ADR_Bits',0,5,133,5,3
	.word	43863
	.byte	7
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,5,136,5,16,4,8
	.byte	'EVTA',0,4
	.word	7908
	.byte	3,29,2,35,0,8
	.byte	'BBM',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'BOD',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'SUSP',0,4
	.word	7908
	.byte	1,26,2,35,0,8
	.byte	'CNT',0,4
	.word	7908
	.byte	2,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7908
	.byte	4,20,2,35,0,8
	.byte	'TYP',0,4
	.word	7908
	.byte	1,19,2,35,0,8
	.byte	'RNG',0,4
	.word	7908
	.byte	1,18,2,35,0,8
	.byte	'reserved_14',0,4
	.word	7908
	.byte	1,17,2,35,0,8
	.byte	'ASI_EN',0,4
	.word	7908
	.byte	1,16,2,35,0,8
	.byte	'ASI',0,4
	.word	7908
	.byte	5,11,2,35,0,8
	.byte	'reserved_21',0,4
	.word	7908
	.byte	6,5,2,35,0,8
	.byte	'AST',0,4
	.word	7908
	.byte	1,4,2,35,0,8
	.byte	'ALD',0,4
	.word	7908
	.byte	1,3,2,35,0,8
	.byte	'reserved_29',0,4
	.word	7908
	.byte	3,0,2,35,0,0,4
	.byte	'Ifx_CPU_TR_EVT_Bits',0,5,153,5,3
	.word	43936
	.byte	7
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,5,156,5,16,4,8
	.byte	'T0',0,4
	.word	7908
	.byte	1,31,2,35,0,8
	.byte	'T1',0,4
	.word	7908
	.byte	1,30,2,35,0,8
	.byte	'T2',0,4
	.word	7908
	.byte	1,29,2,35,0,8
	.byte	'T3',0,4
	.word	7908
	.byte	1,28,2,35,0,8
	.byte	'T4',0,4
	.word	7908
	.byte	1,27,2,35,0,8
	.byte	'T5',0,4
	.word	7908
	.byte	1,26,2,35,0,8
	.byte	'T6',0,4
	.word	7908
	.byte	1,25,2,35,0,8
	.byte	'T7',0,4
	.word	7908
	.byte	1,24,2,35,0,8
	.byte	'reserved_8',0,4
	.word	7908
	.byte	24,0,2,35,0,0,4
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,5,167,5,3
	.word	44254
	.byte	9,5,175,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35472
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_A',0,5,180,5,3
	.word	44449
	.byte	9,5,183,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35533
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_BIV',0,5,188,5,3
	.word	44508
	.byte	9,5,191,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35612
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_BTV',0,5,196,5,3
	.word	44569
	.byte	9,5,199,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35698
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CCNT',0,5,204,5,3
	.word	44630
	.byte	9,5,207,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35787
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CCTRL',0,5,212,5,3
	.word	44692
	.byte	9,5,215,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	35933
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_COMPAT',0,5,220,5,3
	.word	44755
	.byte	9,5,223,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36060
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CORE_ID',0,5,228,5,3
	.word	44819
	.byte	9,5,231,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36158
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPR_L',0,5,236,5,3
	.word	44884
	.byte	9,5,239,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36251
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPR_U',0,5,244,5,3
	.word	44947
	.byte	9,5,247,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36344
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPU_ID',0,5,252,5,3
	.word	45010
	.byte	9,5,255,5,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36451
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CPXE',0,5,132,6,3
	.word	45074
	.byte	9,5,135,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36538
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CREVT',0,5,140,6,3
	.word	45136
	.byte	9,5,143,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36692
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_CUS_ID',0,5,148,6,3
	.word	45199
	.byte	9,5,151,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36786
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_D',0,5,156,6,3
	.word	45263
	.byte	9,5,159,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	36849
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DATR',0,5,164,6,3
	.word	45322
	.byte	9,5,167,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37067
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DBGSR',0,5,172,6,3
	.word	45384
	.byte	9,5,175,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37282
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DBGTCR',0,5,180,6,3
	.word	45447
	.byte	9,5,183,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37376
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCON0',0,5,188,6,3
	.word	45511
	.byte	9,5,191,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37492
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCON2',0,5,196,6,3
	.word	45574
	.byte	9,5,199,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37593
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DCX',0,5,204,6,3
	.word	45637
	.byte	9,5,207,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37686
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DEADD',0,5,212,6,3
	.word	45698
	.byte	9,5,215,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37766
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DIEAR',0,5,220,6,3
	.word	45761
	.byte	9,5,223,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	37835
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DIETR',0,5,228,6,3
	.word	45824
	.byte	9,5,231,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38064
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DMS',0,5,236,6,3
	.word	45887
	.byte	9,5,239,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38157
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPR_L',0,5,244,6,3
	.word	45948
	.byte	9,5,247,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38252
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPR_U',0,5,252,6,3
	.word	46011
	.byte	9,5,255,6,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38347
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPRE',0,5,132,7,3
	.word	46074
	.byte	9,5,135,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38437
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DPWE',0,5,140,7,3
	.word	46136
	.byte	9,5,143,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38527
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_DSTR',0,5,148,7,3
	.word	46198
	.byte	9,5,151,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	38851
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_EXEVT',0,5,156,7,3
	.word	46260
	.byte	9,5,159,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39005
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FCX',0,5,164,7,3
	.word	46323
	.byte	9,5,167,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39111
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,5,172,7,3
	.word	46384
	.byte	9,5,175,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39460
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,5,180,7,3
	.word	46454
	.byte	9,5,183,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39620
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,5,188,7,3
	.word	46524
	.byte	9,5,191,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39701
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,5,196,7,3
	.word	46593
	.byte	9,5,199,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39788
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,5,204,7,3
	.word	46664
	.byte	9,5,207,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39875
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,5,212,7,3
	.word	46735
	.byte	9,5,215,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	39962
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ICNT',0,5,220,7,3
	.word	46806
	.byte	9,5,223,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40053
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ICR',0,5,228,7,3
	.word	46868
	.byte	9,5,231,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40196
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_ISP',0,5,236,7,3
	.word	46929
	.byte	9,5,239,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40262
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_LCX',0,5,244,7,3
	.word	46990
	.byte	9,5,247,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40368
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M1CNT',0,5,252,7,3
	.word	47051
	.byte	9,5,255,7,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40461
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M2CNT',0,5,132,8,3
	.word	47114
	.byte	9,5,135,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40554
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_M3CNT',0,5,140,8,3
	.word	47177
	.byte	9,5,143,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40647
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PC',0,5,148,8,3
	.word	47240
	.byte	9,5,151,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40732
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON0',0,5,156,8,3
	.word	47300
	.byte	9,5,159,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40848
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON1',0,5,164,8,3
	.word	47363
	.byte	9,5,167,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	40959
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCON2',0,5,172,8,3
	.word	47426
	.byte	9,5,175,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41060
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PCXI',0,5,180,8,3
	.word	47489
	.byte	9,5,183,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41190
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PIEAR',0,5,188,8,3
	.word	47551
	.byte	9,5,191,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41259
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PIETR',0,5,196,8,3
	.word	47614
	.byte	9,5,199,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41488
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA0',0,5,204,8,3
	.word	47677
	.byte	9,5,207,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41601
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA1',0,5,212,8,3
	.word	47739
	.byte	9,5,215,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41714
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PMA2',0,5,220,8,3
	.word	47801
	.byte	9,5,223,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	41805
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PSTR',0,5,228,8,3
	.word	47863
	.byte	9,5,231,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42008
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_PSW',0,5,236,8,3
	.word	47925
	.byte	9,5,239,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42251
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SEGEN',0,5,244,8,3
	.word	47986
	.byte	9,5,247,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42379
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SMACON',0,5,252,8,3
	.word	48049
	.byte	9,5,255,8,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42620
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENA',0,5,132,9,3
	.word	48113
	.byte	9,5,135,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42703
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_ACCENB',0,5,140,9,3
	.word	48183
	.byte	9,5,143,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42794
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,5,148,9,3
	.word	48253
	.byte	9,5,151,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42885
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,5,156,9,3
	.word	48327
	.byte	9,5,159,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	42984
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,5,164,9,3
	.word	48401
	.byte	9,5,167,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43091
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,5,172,9,3
	.word	48471
	.byte	9,5,175,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43198
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SWEVT',0,5,180,9,3
	.word	48541
	.byte	9,5,183,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43352
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_SYSCON',0,5,188,9,3
	.word	48604
	.byte	9,5,191,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43513
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TASK_ASI',0,5,196,9,3
	.word	48668
	.byte	9,5,199,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43611
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TPS_CON',0,5,204,9,3
	.word	48734
	.byte	9,5,207,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43783
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TPS_TIMER',0,5,212,9,3
	.word	48799
	.byte	9,5,215,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43863
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TR_ADR',0,5,220,9,3
	.word	48866
	.byte	9,5,223,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	43936
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TR_EVT',0,5,228,9,3
	.word	48930
	.byte	9,5,231,9,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	44254
	.byte	2,35,0,0,4
	.byte	'Ifx_CPU_TRIG_ACC',0,5,236,9,3
	.word	48994
	.byte	7
	.byte	'_Ifx_CPU_CPR',0,5,247,9,25,8,10
	.byte	'L',0,4
	.word	44884
	.byte	2,35,0,10
	.byte	'U',0,4
	.word	44947
	.byte	2,35,4,0,13
	.word	49060
	.byte	4
	.byte	'Ifx_CPU_CPR',0,5,251,9,3
	.word	49102
	.byte	7
	.byte	'_Ifx_CPU_DPR',0,5,254,9,25,8,10
	.byte	'L',0,4
	.word	45948
	.byte	2,35,0,10
	.byte	'U',0,4
	.word	46011
	.byte	2,35,4,0,13
	.word	49128
	.byte	4
	.byte	'Ifx_CPU_DPR',0,5,130,10,3
	.word	49170
	.byte	7
	.byte	'_Ifx_CPU_SPROT_RGN',0,5,133,10,25,16,10
	.byte	'LA',0,4
	.word	48401
	.byte	2,35,0,10
	.byte	'UA',0,4
	.word	48471
	.byte	2,35,4,10
	.byte	'ACCENA',0,4
	.word	48253
	.byte	2,35,8,10
	.byte	'ACCENB',0,4
	.word	48327
	.byte	2,35,12,0,13
	.word	49196
	.byte	4
	.byte	'Ifx_CPU_SPROT_RGN',0,5,139,10,3
	.word	49278
	.byte	7
	.byte	'_Ifx_CPU_TPS',0,5,142,10,25,16,10
	.byte	'CON',0,4
	.word	48734
	.byte	2,35,0,11,12
	.word	48799
	.byte	12,2,0,10
	.byte	'TIMER',0,12
	.word	49342
	.byte	2,35,4,0,13
	.word	49310
	.byte	4
	.byte	'Ifx_CPU_TPS',0,5,146,10,3
	.word	49367
	.byte	7
	.byte	'_Ifx_CPU_TR',0,5,149,10,25,8,10
	.byte	'EVT',0,4
	.word	48930
	.byte	2,35,0,10
	.byte	'ADR',0,4
	.word	48866
	.byte	2,35,4,0,13
	.word	49393
	.byte	4
	.byte	'Ifx_CPU_TR',0,5,153,10,3
	.word	49438
	.byte	7
	.byte	'_Ifx_SRC_SRCR_Bits',0,6,45,16,4,8
	.byte	'SRPN',0,1
	.word	233
	.byte	8,0,2,35,0,8
	.byte	'reserved_8',0,1
	.word	233
	.byte	2,6,2,35,1,8
	.byte	'SRE',0,1
	.word	233
	.byte	1,5,2,35,1,8
	.byte	'TOS',0,1
	.word	233
	.byte	1,4,2,35,1,8
	.byte	'reserved_12',0,1
	.word	233
	.byte	4,0,2,35,1,8
	.byte	'ECC',0,1
	.word	233
	.byte	5,3,2,35,2,8
	.byte	'reserved_21',0,1
	.word	233
	.byte	3,0,2,35,2,8
	.byte	'SRR',0,1
	.word	233
	.byte	1,7,2,35,3,8
	.byte	'CLRR',0,1
	.word	233
	.byte	1,6,2,35,3,8
	.byte	'SETR',0,1
	.word	233
	.byte	1,5,2,35,3,8
	.byte	'IOV',0,1
	.word	233
	.byte	1,4,2,35,3,8
	.byte	'IOVCLR',0,1
	.word	233
	.byte	1,3,2,35,3,8
	.byte	'SWS',0,1
	.word	233
	.byte	1,2,2,35,3,8
	.byte	'SWSCLR',0,1
	.word	233
	.byte	1,1,2,35,3,8
	.byte	'reserved_31',0,1
	.word	233
	.byte	1,0,2,35,3,0,4
	.byte	'Ifx_SRC_SRCR_Bits',0,6,62,3
	.word	49463
	.byte	9,6,70,9,4,10
	.byte	'U',0,4
	.word	923
	.byte	2,35,0,10
	.byte	'I',0,4
	.word	9193
	.byte	2,35,0,10
	.byte	'B',0,4
	.word	49463
	.byte	2,35,0,0,4
	.byte	'Ifx_SRC_SRCR',0,6,75,3
	.word	49779
	.byte	7
	.byte	'_Ifx_SRC_ASCLIN',0,6,86,25,12,10
	.byte	'TX',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'RX',0,4
	.word	49779
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	49779
	.byte	2,35,8,0,13
	.word	49839
	.byte	4
	.byte	'Ifx_SRC_ASCLIN',0,6,91,3
	.word	49898
	.byte	7
	.byte	'_Ifx_SRC_BCUSPB',0,6,94,25,4,10
	.byte	'SBSRC',0,4
	.word	49779
	.byte	2,35,0,0,13
	.word	49926
	.byte	4
	.byte	'Ifx_SRC_BCUSPB',0,6,97,3
	.word	49963
	.byte	7
	.byte	'_Ifx_SRC_CAN',0,6,100,25,64,11,64
	.word	49779
	.byte	12,15,0,10
	.byte	'INT',0,64
	.word	50009
	.byte	2,35,0,0,13
	.word	49991
	.byte	4
	.byte	'Ifx_SRC_CAN',0,6,103,3
	.word	50032
	.byte	7
	.byte	'_Ifx_SRC_CAN1',0,6,106,25,32,11,32
	.word	49779
	.byte	12,7,0,10
	.byte	'INT',0,32
	.word	50076
	.byte	2,35,0,0,13
	.word	50057
	.byte	4
	.byte	'Ifx_SRC_CAN1',0,6,109,3
	.word	50099
	.byte	7
	.byte	'_Ifx_SRC_CCU6',0,6,112,25,16,10
	.byte	'SR0',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49779
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	49779
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	49779
	.byte	2,35,12,0,13
	.word	50125
	.byte	4
	.byte	'Ifx_SRC_CCU6',0,6,118,3
	.word	50197
	.byte	7
	.byte	'_Ifx_SRC_CERBERUS',0,6,121,25,8,11,8
	.word	49779
	.byte	12,1,0,10
	.byte	'SR',0,8
	.word	50246
	.byte	2,35,0,0,13
	.word	50223
	.byte	4
	.byte	'Ifx_SRC_CERBERUS',0,6,124,3
	.word	50268
	.byte	7
	.byte	'_Ifx_SRC_CPU',0,6,127,25,32,10
	.byte	'SBSRC',0,4
	.word	49779
	.byte	2,35,0,11,28
	.word	233
	.byte	12,27,0,10
	.byte	'reserved_4',0,28
	.word	50331
	.byte	2,35,4,0,13
	.word	50298
	.byte	4
	.byte	'Ifx_SRC_CPU',0,6,131,1,3
	.word	50361
	.byte	7
	.byte	'_Ifx_SRC_DMA',0,6,134,1,25,80,10
	.byte	'ERR',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'reserved_4',0,12
	.word	12433
	.byte	2,35,4,10
	.byte	'CH',0,64
	.word	50009
	.byte	2,35,16,0,13
	.word	50387
	.byte	4
	.byte	'Ifx_SRC_DMA',0,6,139,1,3
	.word	50452
	.byte	7
	.byte	'_Ifx_SRC_EMEM',0,6,142,1,25,4,10
	.byte	'SR',0,4
	.word	49779
	.byte	2,35,0,0,13
	.word	50478
	.byte	4
	.byte	'Ifx_SRC_EMEM',0,6,145,1,3
	.word	50511
	.byte	7
	.byte	'_Ifx_SRC_ERAY',0,6,148,1,25,80,10
	.byte	'INT',0,8
	.word	50246
	.byte	2,35,0,10
	.byte	'TINT',0,8
	.word	50246
	.byte	2,35,8,10
	.byte	'NDAT',0,8
	.word	50246
	.byte	2,35,16,10
	.byte	'MBSC',0,8
	.word	50246
	.byte	2,35,24,10
	.byte	'OBUSY',0,4
	.word	49779
	.byte	2,35,32,10
	.byte	'IBUSY',0,4
	.word	49779
	.byte	2,35,36,11,40
	.word	233
	.byte	12,39,0,10
	.byte	'reserved_28',0,40
	.word	50643
	.byte	2,35,40,0,13
	.word	50538
	.byte	4
	.byte	'Ifx_SRC_ERAY',0,6,157,1,3
	.word	50674
	.byte	7
	.byte	'_Ifx_SRC_ETH',0,6,160,1,25,4,10
	.byte	'SR',0,4
	.word	49779
	.byte	2,35,0,0,13
	.word	50701
	.byte	4
	.byte	'Ifx_SRC_ETH',0,6,163,1,3
	.word	50733
	.byte	7
	.byte	'_Ifx_SRC_EVR',0,6,166,1,25,8,10
	.byte	'WUT',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'SCDC',0,4
	.word	49779
	.byte	2,35,4,0,13
	.word	50759
	.byte	4
	.byte	'Ifx_SRC_EVR',0,6,170,1,3
	.word	50806
	.byte	7
	.byte	'_Ifx_SRC_FFT',0,6,173,1,25,12,10
	.byte	'DONE',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'ERR',0,4
	.word	49779
	.byte	2,35,4,10
	.byte	'RFS',0,4
	.word	49779
	.byte	2,35,8,0,13
	.word	50832
	.byte	4
	.byte	'Ifx_SRC_FFT',0,6,178,1,3
	.word	50892
	.byte	7
	.byte	'_Ifx_SRC_GPSR',0,6,181,1,25,128,12,10
	.byte	'SR0',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49779
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	49779
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	49779
	.byte	2,35,12,11,240,11
	.word	233
	.byte	12,239,11,0,10
	.byte	'reserved_10',0,240,11
	.word	50991
	.byte	2,35,16,0,13
	.word	50918
	.byte	4
	.byte	'Ifx_SRC_GPSR',0,6,188,1,3
	.word	51025
	.byte	7
	.byte	'_Ifx_SRC_GPT12',0,6,191,1,25,48,10
	.byte	'CIRQ',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'T2',0,4
	.word	49779
	.byte	2,35,4,10
	.byte	'T3',0,4
	.word	49779
	.byte	2,35,8,10
	.byte	'T4',0,4
	.word	49779
	.byte	2,35,12,10
	.byte	'T5',0,4
	.word	49779
	.byte	2,35,16,10
	.byte	'T6',0,4
	.word	49779
	.byte	2,35,20,11,24
	.word	233
	.byte	12,23,0,10
	.byte	'reserved_18',0,24
	.word	51147
	.byte	2,35,24,0,13
	.word	51052
	.byte	4
	.byte	'Ifx_SRC_GPT12',0,6,200,1,3
	.word	51178
	.byte	7
	.byte	'_Ifx_SRC_GTM',0,6,203,1,25,192,11,10
	.byte	'AEIIRQ',0,4
	.word	49779
	.byte	2,35,0,11,236,2
	.word	233
	.byte	12,235,2,0,10
	.byte	'reserved_4',0,236,2
	.word	51242
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	49779
	.byte	3,35,240,2,10
	.byte	'reserved_174',0,12
	.word	12433
	.byte	3,35,244,2,11,32
	.word	50076
	.byte	12,0,0,10
	.byte	'TIM',0,32
	.word	51311
	.byte	3,35,128,3,11,224,7
	.word	233
	.byte	12,223,7,0,10
	.byte	'reserved_1A0',0,224,7
	.word	51334
	.byte	3,35,160,3,11,64
	.word	50076
	.byte	12,1,0,10
	.byte	'TOM',0,64
	.word	51369
	.byte	3,35,128,11,0,13
	.word	51206
	.byte	4
	.byte	'Ifx_SRC_GTM',0,6,212,1,3
	.word	51393
	.byte	7
	.byte	'_Ifx_SRC_HSM',0,6,215,1,25,8,10
	.byte	'HSM',0,8
	.word	50246
	.byte	2,35,0,0,13
	.word	51419
	.byte	4
	.byte	'Ifx_SRC_HSM',0,6,218,1,3
	.word	51452
	.byte	7
	.byte	'_Ifx_SRC_LMU',0,6,221,1,25,4,10
	.byte	'SR',0,4
	.word	49779
	.byte	2,35,0,0,13
	.word	51478
	.byte	4
	.byte	'Ifx_SRC_LMU',0,6,224,1,3
	.word	51510
	.byte	7
	.byte	'_Ifx_SRC_PMU',0,6,227,1,25,4,10
	.byte	'SR',0,4
	.word	49779
	.byte	2,35,0,0,13
	.word	51536
	.byte	4
	.byte	'Ifx_SRC_PMU',0,6,230,1,3
	.word	51568
	.byte	7
	.byte	'_Ifx_SRC_QSPI',0,6,233,1,25,24,10
	.byte	'TX',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'RX',0,4
	.word	49779
	.byte	2,35,4,10
	.byte	'ERR',0,4
	.word	49779
	.byte	2,35,8,10
	.byte	'PT',0,4
	.word	49779
	.byte	2,35,12,10
	.byte	'HC',0,4
	.word	49779
	.byte	2,35,16,10
	.byte	'U',0,4
	.word	49779
	.byte	2,35,20,0,13
	.word	51594
	.byte	4
	.byte	'Ifx_SRC_QSPI',0,6,241,1,3
	.word	51687
	.byte	7
	.byte	'_Ifx_SRC_SCU',0,6,244,1,25,20,10
	.byte	'DTS',0,4
	.word	49779
	.byte	2,35,0,11,16
	.word	49779
	.byte	12,3,0,10
	.byte	'ERU',0,16
	.word	51746
	.byte	2,35,4,0,13
	.word	51714
	.byte	4
	.byte	'Ifx_SRC_SCU',0,6,248,1,3
	.word	51769
	.byte	7
	.byte	'_Ifx_SRC_SENT',0,6,251,1,25,16,10
	.byte	'SR',0,16
	.word	51746
	.byte	2,35,0,0,13
	.word	51795
	.byte	4
	.byte	'Ifx_SRC_SENT',0,6,254,1,3
	.word	51828
	.byte	7
	.byte	'_Ifx_SRC_SMU',0,6,129,2,25,12,11,12
	.word	49779
	.byte	12,2,0,10
	.byte	'SR',0,12
	.word	51874
	.byte	2,35,0,0,13
	.word	51855
	.byte	4
	.byte	'Ifx_SRC_SMU',0,6,132,2,3
	.word	51896
	.byte	7
	.byte	'_Ifx_SRC_STM',0,6,135,2,25,96,10
	.byte	'SR0',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49779
	.byte	2,35,4,11,88
	.word	233
	.byte	12,87,0,10
	.byte	'reserved_8',0,88
	.word	51967
	.byte	2,35,8,0,13
	.word	51922
	.byte	4
	.byte	'Ifx_SRC_STM',0,6,140,2,3
	.word	51997
	.byte	7
	.byte	'_Ifx_SRC_VADCCG',0,6,143,2,25,192,2,10
	.byte	'SR0',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49779
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	49779
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	49779
	.byte	2,35,12,11,176,2
	.word	233
	.byte	12,175,2,0,10
	.byte	'reserved_10',0,176,2
	.word	52098
	.byte	2,35,16,0,13
	.word	52023
	.byte	4
	.byte	'Ifx_SRC_VADCCG',0,6,150,2,3
	.word	52132
	.byte	7
	.byte	'_Ifx_SRC_VADCG',0,6,153,2,25,16,10
	.byte	'SR0',0,4
	.word	49779
	.byte	2,35,0,10
	.byte	'SR1',0,4
	.word	49779
	.byte	2,35,4,10
	.byte	'SR2',0,4
	.word	49779
	.byte	2,35,8,10
	.byte	'SR3',0,4
	.word	49779
	.byte	2,35,12,0,13
	.word	52161
	.byte	4
	.byte	'Ifx_SRC_VADCG',0,6,159,2,3
	.word	52235
	.byte	7
	.byte	'_Ifx_SRC_XBAR',0,6,162,2,25,4,10
	.byte	'SRC',0,4
	.word	49779
	.byte	2,35,0,0,13
	.word	52263
	.byte	4
	.byte	'Ifx_SRC_XBAR',0,6,165,2,3
	.word	52297
	.byte	7
	.byte	'_Ifx_SRC_GASCLIN',0,6,178,2,25,24,11,24
	.word	49839
	.byte	12,1,0,13
	.word	52347
	.byte	10
	.byte	'ASCLIN',0,24
	.word	52356
	.byte	2,35,0,0,13
	.word	52324
	.byte	4
	.byte	'Ifx_SRC_GASCLIN',0,6,181,2,3
	.word	52378
	.byte	7
	.byte	'_Ifx_SRC_GBCU',0,6,184,2,25,4,13
	.word	49926
	.byte	10
	.byte	'SPB',0,4
	.word	52428
	.byte	2,35,0,0,13
	.word	52408
	.byte	4
	.byte	'Ifx_SRC_GBCU',0,6,187,2,3
	.word	52447
	.byte	7
	.byte	'_Ifx_SRC_GCAN',0,6,190,2,25,96,11,64
	.word	49991
	.byte	12,0,0,13
	.word	52494
	.byte	10
	.byte	'CAN',0,64
	.word	52503
	.byte	2,35,0,11,32
	.word	50057
	.byte	12,0,0,13
	.word	52521
	.byte	10
	.byte	'CAN1',0,32
	.word	52530
	.byte	2,35,64,0,13
	.word	52474
	.byte	4
	.byte	'Ifx_SRC_GCAN',0,6,194,2,3
	.word	52550
	.byte	7
	.byte	'_Ifx_SRC_GCCU6',0,6,197,2,25,32,11,32
	.word	50125
	.byte	12,1,0,13
	.word	52598
	.byte	10
	.byte	'CCU6',0,32
	.word	52607
	.byte	2,35,0,0,13
	.word	52577
	.byte	4
	.byte	'Ifx_SRC_GCCU6',0,6,200,2,3
	.word	52627
	.byte	7
	.byte	'_Ifx_SRC_GCERBERUS',0,6,203,2,25,8,13
	.word	50223
	.byte	10
	.byte	'CERBERUS',0,8
	.word	52680
	.byte	2,35,0,0,13
	.word	52655
	.byte	4
	.byte	'Ifx_SRC_GCERBERUS',0,6,206,2,3
	.word	52704
	.byte	7
	.byte	'_Ifx_SRC_GCPU',0,6,209,2,25,32,11,32
	.word	50298
	.byte	12,0,0,13
	.word	52756
	.byte	10
	.byte	'CPU',0,32
	.word	52765
	.byte	2,35,0,0,13
	.word	52736
	.byte	4
	.byte	'Ifx_SRC_GCPU',0,6,212,2,3
	.word	52784
	.byte	7
	.byte	'_Ifx_SRC_GDMA',0,6,215,2,25,80,11,80
	.word	50387
	.byte	12,0,0,13
	.word	52831
	.byte	10
	.byte	'DMA',0,80
	.word	52840
	.byte	2,35,0,0,13
	.word	52811
	.byte	4
	.byte	'Ifx_SRC_GDMA',0,6,218,2,3
	.word	52859
	.byte	7
	.byte	'_Ifx_SRC_GEMEM',0,6,221,2,25,4,11,4
	.word	50478
	.byte	12,0,0,13
	.word	52907
	.byte	10
	.byte	'EMEM',0,4
	.word	52916
	.byte	2,35,0,0,13
	.word	52886
	.byte	4
	.byte	'Ifx_SRC_GEMEM',0,6,224,2,3
	.word	52936
	.byte	7
	.byte	'_Ifx_SRC_GERAY',0,6,227,2,25,80,11,80
	.word	50538
	.byte	12,0,0,13
	.word	52985
	.byte	10
	.byte	'ERAY',0,80
	.word	52994
	.byte	2,35,0,0,13
	.word	52964
	.byte	4
	.byte	'Ifx_SRC_GERAY',0,6,230,2,3
	.word	53014
	.byte	7
	.byte	'_Ifx_SRC_GETH',0,6,233,2,25,4,11,4
	.word	50701
	.byte	12,0,0,13
	.word	53062
	.byte	10
	.byte	'ETH',0,4
	.word	53071
	.byte	2,35,0,0,13
	.word	53042
	.byte	4
	.byte	'Ifx_SRC_GETH',0,6,236,2,3
	.word	53090
	.byte	7
	.byte	'_Ifx_SRC_GEVR',0,6,239,2,25,8,11,8
	.word	50759
	.byte	12,0,0,13
	.word	53137
	.byte	10
	.byte	'EVR',0,8
	.word	53146
	.byte	2,35,0,0,13
	.word	53117
	.byte	4
	.byte	'Ifx_SRC_GEVR',0,6,242,2,3
	.word	53165
	.byte	7
	.byte	'_Ifx_SRC_GFFT',0,6,245,2,25,12,11,12
	.word	50832
	.byte	12,0,0,13
	.word	53212
	.byte	10
	.byte	'FFT',0,12
	.word	53221
	.byte	2,35,0,0,13
	.word	53192
	.byte	4
	.byte	'Ifx_SRC_GFFT',0,6,248,2,3
	.word	53240
	.byte	7
	.byte	'_Ifx_SRC_GGPSR',0,6,251,2,25,128,12,11,128,12
	.word	50918
	.byte	12,0,0,13
	.word	53289
	.byte	10
	.byte	'GPSR',0,128,12
	.word	53299
	.byte	2,35,0,0,13
	.word	53267
	.byte	4
	.byte	'Ifx_SRC_GGPSR',0,6,254,2,3
	.word	53320
	.byte	7
	.byte	'_Ifx_SRC_GGPT12',0,6,129,3,25,48,11,48
	.word	51052
	.byte	12,0,0,13
	.word	53370
	.byte	10
	.byte	'GPT12',0,48
	.word	53379
	.byte	2,35,0,0,13
	.word	53348
	.byte	4
	.byte	'Ifx_SRC_GGPT12',0,6,132,3,3
	.word	53400
	.byte	7
	.byte	'_Ifx_SRC_GGTM',0,6,135,3,25,192,11,11,192,11
	.word	51206
	.byte	12,0,0,13
	.word	53450
	.byte	10
	.byte	'GTM',0,192,11
	.word	53460
	.byte	2,35,0,0,13
	.word	53429
	.byte	4
	.byte	'Ifx_SRC_GGTM',0,6,138,3,3
	.word	53480
	.byte	7
	.byte	'_Ifx_SRC_GHSM',0,6,141,3,25,8,11,8
	.word	51419
	.byte	12,0,0,13
	.word	53527
	.byte	10
	.byte	'HSM',0,8
	.word	53536
	.byte	2,35,0,0,13
	.word	53507
	.byte	4
	.byte	'Ifx_SRC_GHSM',0,6,144,3,3
	.word	53555
	.byte	7
	.byte	'_Ifx_SRC_GLMU',0,6,147,3,25,4,11,4
	.word	51478
	.byte	12,0,0,13
	.word	53602
	.byte	10
	.byte	'LMU',0,4
	.word	53611
	.byte	2,35,0,0,13
	.word	53582
	.byte	4
	.byte	'Ifx_SRC_GLMU',0,6,150,3,3
	.word	53630
	.byte	7
	.byte	'_Ifx_SRC_GPMU',0,6,153,3,25,8,11,8
	.word	51536
	.byte	12,1,0,13
	.word	53677
	.byte	10
	.byte	'PMU',0,8
	.word	53686
	.byte	2,35,0,0,13
	.word	53657
	.byte	4
	.byte	'Ifx_SRC_GPMU',0,6,156,3,3
	.word	53705
	.byte	7
	.byte	'_Ifx_SRC_GQSPI',0,6,159,3,25,96,11,96
	.word	51594
	.byte	12,3,0,13
	.word	53753
	.byte	10
	.byte	'QSPI',0,96
	.word	53762
	.byte	2,35,0,0,13
	.word	53732
	.byte	4
	.byte	'Ifx_SRC_GQSPI',0,6,162,3,3
	.word	53782
	.byte	7
	.byte	'_Ifx_SRC_GSCU',0,6,165,3,25,20,13
	.word	51714
	.byte	10
	.byte	'SCU',0,20
	.word	53830
	.byte	2,35,0,0,13
	.word	53810
	.byte	4
	.byte	'Ifx_SRC_GSCU',0,6,168,3,3
	.word	53849
	.byte	7
	.byte	'_Ifx_SRC_GSENT',0,6,171,3,25,16,11,16
	.word	51795
	.byte	12,0,0,13
	.word	53897
	.byte	10
	.byte	'SENT',0,16
	.word	53906
	.byte	2,35,0,0,13
	.word	53876
	.byte	4
	.byte	'Ifx_SRC_GSENT',0,6,174,3,3
	.word	53926
	.byte	7
	.byte	'_Ifx_SRC_GSMU',0,6,177,3,25,12,11,12
	.word	51855
	.byte	12,0,0,13
	.word	53974
	.byte	10
	.byte	'SMU',0,12
	.word	53983
	.byte	2,35,0,0,13
	.word	53954
	.byte	4
	.byte	'Ifx_SRC_GSMU',0,6,180,3,3
	.word	54002
	.byte	7
	.byte	'_Ifx_SRC_GSTM',0,6,183,3,25,96,11,96
	.word	51922
	.byte	12,0,0,13
	.word	54049
	.byte	10
	.byte	'STM',0,96
	.word	54058
	.byte	2,35,0,0,13
	.word	54029
	.byte	4
	.byte	'Ifx_SRC_GSTM',0,6,186,3,3
	.word	54077
	.byte	7
	.byte	'_Ifx_SRC_GVADC',0,6,189,3,25,224,4,11,64
	.word	52161
	.byte	12,3,0,13
	.word	54126
	.byte	10
	.byte	'G',0,64
	.word	54135
	.byte	2,35,0,11,224,1
	.word	233
	.byte	12,223,1,0,10
	.byte	'reserved_40',0,224,1
	.word	54151
	.byte	2,35,64,11,192,2
	.word	52023
	.byte	12,0,0,13
	.word	54184
	.byte	10
	.byte	'CG',0,192,2
	.word	54194
	.byte	3,35,160,2,0,13
	.word	54104
	.byte	4
	.byte	'Ifx_SRC_GVADC',0,6,194,3,3
	.word	54214
	.byte	7
	.byte	'_Ifx_SRC_GXBAR',0,6,197,3,25,4,13
	.word	52263
	.byte	10
	.byte	'XBAR',0,4
	.word	54263
	.byte	2,35,0,0,13
	.word	54242
	.byte	4
	.byte	'Ifx_SRC_GXBAR',0,6,200,3,3
	.word	54283
	.byte	4
	.byte	'Dma_StatusType',0,7,121,22
	.word	923
	.byte	4
	.byte	'Dma_ErrorStatusType',0,7,141,1,22
	.word	923
	.byte	14,7,147,1,9,1,15
	.byte	'DMA_CHANNEL0',0,0,15
	.byte	'DMA_CHANNEL1',0,1,15
	.byte	'DMA_CHANNEL2',0,2,15
	.byte	'DMA_CHANNEL3',0,3,15
	.byte	'DMA_CHANNEL4',0,4,15
	.byte	'DMA_CHANNEL5',0,5,15
	.byte	'DMA_CHANNEL6',0,6,15
	.byte	'DMA_CHANNEL7',0,7,15
	.byte	'DMA_CHANNEL8',0,8,15
	.byte	'DMA_CHANNEL9',0,9,15
	.byte	'DMA_CHANNEL10',0,10,15
	.byte	'DMA_CHANNEL11',0,11,15
	.byte	'DMA_CHANNEL12',0,12,15
	.byte	'DMA_CHANNEL13',0,13,15
	.byte	'DMA_CHANNEL14',0,14,15
	.byte	'DMA_CHANNEL15',0,15,15
	.byte	'DMA_CHANNEL16',0,16,15
	.byte	'DMA_CHANNEL17',0,17,15
	.byte	'DMA_CHANNEL18',0,18,15
	.byte	'DMA_CHANNEL19',0,19,15
	.byte	'DMA_CHANNEL20',0,20,15
	.byte	'DMA_CHANNEL21',0,21,15
	.byte	'DMA_CHANNEL22',0,22,15
	.byte	'DMA_CHANNEL23',0,23,15
	.byte	'DMA_CHANNEL24',0,24,15
	.byte	'DMA_CHANNEL25',0,25,15
	.byte	'DMA_CHANNEL26',0,26,15
	.byte	'DMA_CHANNEL27',0,27,15
	.byte	'DMA_CHANNEL28',0,28,15
	.byte	'DMA_CHANNEL29',0,29,15
	.byte	'DMA_CHANNEL30',0,30,15
	.byte	'DMA_CHANNEL31',0,31,15
	.byte	'DMA_CHANNEL32',0,32,15
	.byte	'DMA_CHANNEL33',0,33,15
	.byte	'DMA_CHANNEL34',0,34,15
	.byte	'DMA_CHANNEL35',0,35,15
	.byte	'DMA_CHANNEL36',0,36,15
	.byte	'DMA_CHANNEL37',0,37,15
	.byte	'DMA_CHANNEL38',0,38,15
	.byte	'DMA_CHANNEL39',0,39,15
	.byte	'DMA_CHANNEL40',0,40,15
	.byte	'DMA_CHANNEL41',0,41,15
	.byte	'DMA_CHANNEL42',0,42,15
	.byte	'DMA_CHANNEL43',0,43,15
	.byte	'DMA_CHANNEL44',0,44,15
	.byte	'DMA_CHANNEL45',0,45,15
	.byte	'DMA_CHANNEL46',0,46,15
	.byte	'DMA_CHANNEL47',0,47,15
	.byte	'DMA_CHANNEL48',0,48,15
	.byte	'DMA_CHANNEL49',0,49,15
	.byte	'DMA_CHANNEL50',0,50,15
	.byte	'DMA_CHANNEL51',0,51,15
	.byte	'DMA_CHANNEL52',0,52,15
	.byte	'DMA_CHANNEL53',0,53,15
	.byte	'DMA_CHANNEL54',0,54,15
	.byte	'DMA_CHANNEL55',0,55,15
	.byte	'DMA_CHANNEL56',0,56,15
	.byte	'DMA_CHANNEL57',0,57,15
	.byte	'DMA_CHANNEL58',0,58,15
	.byte	'DMA_CHANNEL59',0,59,15
	.byte	'DMA_CHANNEL60',0,60,15
	.byte	'DMA_CHANNEL61',0,61,15
	.byte	'DMA_CHANNEL62',0,62,15
	.byte	'DMA_CHANNEL63',0,63,15
	.byte	'DMA_CHANNEL64',0,192,0,15
	.byte	'DMA_CHANNEL65',0,193,0,15
	.byte	'DMA_CHANNEL66',0,194,0,15
	.byte	'DMA_CHANNEL67',0,195,0,15
	.byte	'DMA_CHANNEL68',0,196,0,15
	.byte	'DMA_CHANNEL69',0,197,0,15
	.byte	'DMA_CHANNEL70',0,198,0,15
	.byte	'DMA_CHANNEL71',0,199,0,15
	.byte	'DMA_CHANNEL72',0,200,0,15
	.byte	'DMA_CHANNEL73',0,201,0,15
	.byte	'DMA_CHANNEL74',0,202,0,15
	.byte	'DMA_CHANNEL75',0,203,0,15
	.byte	'DMA_CHANNEL76',0,204,0,15
	.byte	'DMA_CHANNEL77',0,205,0,15
	.byte	'DMA_CHANNEL78',0,206,0,15
	.byte	'DMA_CHANNEL79',0,207,0,15
	.byte	'DMA_CHANNEL80',0,208,0,15
	.byte	'DMA_CHANNEL81',0,209,0,15
	.byte	'DMA_CHANNEL82',0,210,0,15
	.byte	'DMA_CHANNEL83',0,211,0,15
	.byte	'DMA_CHANNEL84',0,212,0,15
	.byte	'DMA_CHANNEL85',0,213,0,15
	.byte	'DMA_CHANNEL86',0,214,0,15
	.byte	'DMA_CHANNEL87',0,215,0,15
	.byte	'DMA_CHANNEL88',0,216,0,15
	.byte	'DMA_CHANNEL89',0,217,0,15
	.byte	'DMA_CHANNEL90',0,218,0,15
	.byte	'DMA_CHANNEL91',0,219,0,15
	.byte	'DMA_CHANNEL92',0,220,0,15
	.byte	'DMA_CHANNEL93',0,221,0,15
	.byte	'DMA_CHANNEL94',0,222,0,15
	.byte	'DMA_CHANNEL95',0,223,0,15
	.byte	'DMA_CHANNEL96',0,224,0,15
	.byte	'DMA_CHANNEL97',0,225,0,15
	.byte	'DMA_CHANNEL98',0,226,0,15
	.byte	'DMA_CHANNEL99',0,227,0,15
	.byte	'DMA_CHANNEL100',0,228,0,15
	.byte	'DMA_CHANNEL101',0,229,0,15
	.byte	'DMA_CHANNEL102',0,230,0,15
	.byte	'DMA_CHANNEL103',0,231,0,15
	.byte	'DMA_CHANNEL104',0,232,0,15
	.byte	'DMA_CHANNEL105',0,233,0,15
	.byte	'DMA_CHANNEL106',0,234,0,15
	.byte	'DMA_CHANNEL107',0,235,0,15
	.byte	'DMA_CHANNEL108',0,236,0,15
	.byte	'DMA_CHANNEL109',0,237,0,15
	.byte	'DMA_CHANNEL110',0,238,0,15
	.byte	'DMA_CHANNEL111',0,239,0,15
	.byte	'DMA_CHANNEL112',0,240,0,15
	.byte	'DMA_CHANNEL113',0,241,0,15
	.byte	'DMA_CHANNEL114',0,242,0,15
	.byte	'DMA_CHANNEL115',0,243,0,15
	.byte	'DMA_CHANNEL116',0,244,0,15
	.byte	'DMA_CHANNEL117',0,245,0,15
	.byte	'DMA_CHANNEL118',0,246,0,15
	.byte	'DMA_CHANNEL119',0,247,0,15
	.byte	'DMA_CHANNEL120',0,248,0,15
	.byte	'DMA_CHANNEL121',0,249,0,15
	.byte	'DMA_CHANNEL122',0,250,0,15
	.byte	'DMA_CHANNEL123',0,251,0,15
	.byte	'DMA_CHANNEL124',0,252,0,15
	.byte	'DMA_CHANNEL125',0,253,0,15
	.byte	'DMA_CHANNEL126',0,254,0,15
	.byte	'DMA_CHANNEL127',0,255,0,15
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0,4
	.byte	'Dma_ChannelType',0,7,149,2,2
	.word	54363
	.byte	4
	.byte	'Spi_NumberOfDataType',0,8,169,7,16
	.word	264
	.byte	4
	.byte	'Spi_ChannelType',0,8,177,7,15
	.word	233
	.byte	4
	.byte	'Spi_JobType',0,8,185,7,16
	.word	264
	.byte	4
	.byte	'Spi_SequenceType',0,8,193,7,15
	.word	233
	.byte	16,1,1,3
	.word	56650
	.byte	4
	.byte	'Spi_NotifFunctionPtrType',0,8,238,7,15
	.word	56653
	.byte	7
	.byte	'Spi_ChannelConfig',0,8,247,7,16,12,10
	.byte	'DefaultData',0,4
	.word	301
	.byte	2,35,0,10
	.byte	'DataConfig',0,2
	.word	264
	.byte	2,35,4,10
	.byte	'NoOfBuffers',0,2
	.word	264
	.byte	2,35,6,10
	.byte	'ChannelBufferType',0,1
	.word	233
	.byte	2,35,8,0,4
	.byte	'Spi_ChannelConfigType',0,8,144,8,3
	.word	56692
	.byte	4
	.byte	'Spi_DelayConfigType',0,8,162,8,16
	.word	301
	.byte	4
	.byte	'Spi_HWUnitType',0,8,170,8,15
	.word	233
	.byte	7
	.byte	'Spi_JobConfig',0,8,183,8,16,24,10
	.byte	'JobEndNotification',0,4
	.word	56658
	.byte	2,35,0,17
	.word	233
	.byte	3
	.word	56938
	.byte	10
	.byte	'ChannelLinkPtr',0,4
	.word	56943
	.byte	2,35,4,10
	.byte	'BaudRateConfig',0,4
	.word	301
	.byte	2,35,8,10
	.byte	'TimeDelayConfig',0,4
	.word	301
	.byte	2,35,12,10
	.byte	'CSPin',0,2
	.word	264
	.byte	2,35,16,10
	.byte	'CSPolarity',0,1
	.word	233
	.byte	2,35,18,10
	.byte	'ShiftClkConfig',0,1
	.word	233
	.byte	2,35,19,10
	.byte	'JobPriority',0,1
	.word	233
	.byte	2,35,20,10
	.byte	'HwUnit',0,1
	.word	233
	.byte	2,35,21,10
	.byte	'ChannelBasedChipSelect',0,1
	.word	233
	.byte	2,35,22,10
	.byte	'ParitySelection',0,1
	.word	233
	.byte	2,35,23,0,4
	.byte	'Spi_JobConfigType',0,8,241,8,2
	.word	56890
	.byte	3
	.word	56650
	.byte	7
	.byte	'Spi_SequenceConfig',0,8,252,8,16,16,10
	.byte	'SeqEndNotification',0,4
	.word	56658
	.byte	2,35,0,17
	.word	264
	.byte	3
	.word	57260
	.byte	10
	.byte	'JobLinkPtr',0,4
	.word	57265
	.byte	2,35,4,17
	.word	233
	.byte	3
	.word	57290
	.byte	10
	.byte	'SeqSharingJobs',0,4
	.word	57295
	.byte	2,35,8,10
	.byte	'JobsInParamSeq',0,2
	.word	264
	.byte	2,35,12,10
	.byte	'InterruptibleSequence',0,1
	.word	233
	.byte	2,35,14,0,4
	.byte	'Spi_SequenceConfigType',0,8,157,9,2
	.word	57207
	.byte	7
	.byte	'Spi_DmaConfigType',0,8,164,9,16,2,10
	.byte	'TxDmaChannel',0,1
	.word	54363
	.byte	2,35,0,10
	.byte	'RxDmaChannel',0,1
	.word	54363
	.byte	2,35,1,0,4
	.byte	'Spi_DmaConfigType',0,8,168,9,2
	.word	57412
	.byte	7
	.byte	'Spi_HWModuleConfig',0,8,174,9,16,16,17
	.word	301
	.byte	10
	.byte	'HWClkSetting',0,4
	.word	57533
	.byte	2,35,0,17
	.word	301
	.byte	10
	.byte	'HWCSPolaritySetting',0,4
	.word	57560
	.byte	2,35,4,17
	.word	301
	.byte	10
	.byte	'HWPinSetting',0,4
	.word	57594
	.byte	2,35,8,17
	.word	57412
	.byte	3
	.word	57621
	.byte	10
	.byte	'SpiDmaConfigPtr',0,4
	.word	57626
	.byte	2,35,12,0,4
	.byte	'Spi_HWModuleConfigType',0,8,187,9,2
	.word	57508
	.byte	7
	.byte	'Spi_BaudrateEconType',0,8,214,9,16,6,10
	.byte	'EconVal',0,4
	.word	301
	.byte	2,35,0,10
	.byte	'QSPIHwUnit',0,1
	.word	233
	.byte	2,35,4,0,4
	.byte	'Spi_BaudrateEconType',0,8,222,9,2
	.word	57689
	.byte	7
	.byte	'Spi_ConfigType',0,8,237,9,16,40,17
	.word	56692
	.byte	3
	.word	57805
	.byte	10
	.byte	'SpiChannelConfigPtr',0,4
	.word	57810
	.byte	2,35,0,17
	.word	56890
	.byte	3
	.word	57844
	.byte	10
	.byte	'SpiJobConfigPtr',0,4
	.word	57849
	.byte	2,35,4,17
	.word	57207
	.byte	3
	.word	57879
	.byte	10
	.byte	'SpiSequenceConfigPtr',0,4
	.word	57884
	.byte	2,35,8,17
	.word	57508
	.byte	3
	.word	57919
	.byte	11,16
	.word	57924
	.byte	12,3,0,10
	.byte	'HWModuleConfigPtr',0,16
	.word	57929
	.byte	2,35,12,17
	.word	57689
	.byte	3
	.word	57965
	.byte	10
	.byte	'SpiBaudrateEconPtr',0,4
	.word	57970
	.byte	2,35,28,10
	.byte	'NoOfJobs',0,2
	.word	264
	.byte	2,35,32,10
	.byte	'NoOfChannels',0,1
	.word	233
	.byte	2,35,34,10
	.byte	'NoOfSequences',0,1
	.word	233
	.byte	2,35,35,10
	.byte	'NoOfEconReg',0,1
	.word	233
	.byte	2,35,36,0,4
	.byte	'Spi_ConfigType',0,8,156,10,3
	.word	57784
	.byte	7
	.byte	'Spi_LastChannelDataType',0,8,161,10,16,8,3
	.word	301
	.byte	10
	.byte	'LastDataPtr',0,4
	.word	58142
	.byte	2,35,0,10
	.byte	'DataWidth',0,2
	.word	264
	.byte	2,35,4,0,4
	.byte	'Spi_LastChannelDataType',0,8,165,10,2
	.word	58112
	.byte	11,40
	.word	57784
	.byte	12,0,0
.L24:
	.byte	17
	.word	58221
	.byte	11,12
	.word	56692
	.byte	12,0,0
.L25:
	.byte	17
	.word	58235
	.byte	11,2
	.word	233
	.byte	12,1,0
.L26:
	.byte	17
	.word	58249
	.byte	11,24
	.word	56890
	.byte	12,0,0
.L27:
	.byte	17
	.word	58263
	.byte	11,4
	.word	264
	.byte	12,1,0
.L28:
	.byte	17
	.word	58277
	.byte	11,16
	.word	57207
	.byte	12,0,0
.L29:
	.byte	17
	.word	58291
	.byte	11,8
	.word	57412
	.byte	12,3,0
.L30:
	.byte	17
	.word	58305
	.byte	11,64
	.word	57508
	.byte	12,3,0
.L31:
	.byte	17
	.word	58319
	.byte	11,6
	.word	57689
	.byte	12,0,0
.L32:
	.byte	17
	.word	58333
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,6,36,0,3,8,11,15,62,15,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13,0
	.byte	3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,23,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,11,15,73,19,56,9,0
	.byte	0,11,1,1,11,15,73,19,0,0,12,33,0,47,15,0,0,13,53,0,73,19,0,0,14,4,1,58,15,59,15,57,15,11,15,0,0,15,40
	.byte	0,3,8,28,13,0,0,16,21,0,54,15,39,12,0,0,17,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L34-.L33
.L33:
	.half	3
	.word	.L36-.L35
.L35:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc',0
	.byte	0
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0,0,0,0
	.byte	'Platform_Types.h',0,1,0,0
	.byte	'IfxDma_regdef.h',0,1,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'Mcal_DmaLib.h',0,1,0,0
	.byte	'Spi.h',0,2,0,0,0
.L36:
.L34:
	.sdecl	'.debug_info',debug,cluster('Spi_ConfigRoot')
	.sect	'.debug_info'
.L6:
	.word	207
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Spi_ConfigRoot',0,1,128,2,22
	.word	.L24
	.byte	1,5,3
	.word	Spi_ConfigRoot
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Spi_ConfigRoot')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('Spi_kChannelConfig0')
	.sect	'.debug_info'
.L8:
	.word	210
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Spi_kChannelConfig0',0,1,80,36
	.word	.L25
	.byte	5,3
	.word	Spi_kChannelConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Spi_kChannelConfig0')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('SpiJob_Micron_Master_ChannelLinkPtr')
	.sect	'.debug_info'
.L10:
	.word	226
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'SpiJob_Micron_Master_ChannelLinkPtr',0,1,106,30
	.word	.L26
	.byte	5,3
	.word	SpiJob_Micron_Master_ChannelLinkPtr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('SpiJob_Micron_Master_ChannelLinkPtr')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Spi_kJobConfig0')
	.sect	'.debug_info'
.L12:
	.word	206
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Spi_kJobConfig0',0,1,115,32
	.word	.L27
	.byte	5,3
	.word	Spi_kJobConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Spi_kJobConfig0')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('SpiSequence_Micron_Master_JobLinkPtr')
	.sect	'.debug_info'
.L14:
	.word	228
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'SpiSequence_Micron_Master_JobLinkPtr',0,1,168,1,26
	.word	.L28
	.byte	5,3
	.word	SpiSequence_Micron_Master_JobLinkPtr
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('SpiSequence_Micron_Master_JobLinkPtr')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Spi_kSequenceConfig0')
	.sect	'.debug_info'
.L16:
	.word	212
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Spi_kSequenceConfig0',0,1,177,1,37
	.word	.L29
	.byte	5,3
	.word	Spi_kSequenceConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Spi_kSequenceConfig0')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Spi_kDmaConfig0')
	.sect	'.debug_info'
.L18:
	.word	207
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Spi_kDmaConfig0',0,1,193,1,32
	.word	.L30
	.byte	5,3
	.word	Spi_kDmaConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Spi_kDmaConfig0')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Spi_kModuleConfig0')
	.sect	'.debug_info'
.L20:
	.word	210
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Spi_kModuleConfig0',0,1,210,1,37
	.word	.L31
	.byte	5,3
	.word	Spi_kModuleConfig0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Spi_kModuleConfig0')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('Spi_kBaudrateEcon0')
	.sect	'.debug_info'
.L22:
	.word	210
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'..\\mcal_cfg\\Spi_PBCfg.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1,2
	.word	.L2
	.byte	3
	.byte	'Spi_kBaudrateEcon0',0,1,245,1,35
	.word	.L32
	.byte	5,3
	.word	Spi_kBaudrateEcon0
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('Spi_kBaudrateEcon0')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0

; ..\mcal_cfg\Spi_PBCfg.c	     1  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	     2  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	     3  ** Copyright (C) Infineon Technologies (2018)                                 **
; ..\mcal_cfg\Spi_PBCfg.c	     4  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	     5  ** All rights reserved.                                                       **
; ..\mcal_cfg\Spi_PBCfg.c	     6  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_cfg\Spi_PBCfg.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_cfg\Spi_PBCfg.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_cfg\Spi_PBCfg.c	    10  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    11  ********************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	    12  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    13  **  FILENAME  : Spi_PBCfg.c                                                   **
; ..\mcal_cfg\Spi_PBCfg.c	    14  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    15  **  $CC VERSION : \main\94 $                                                 **
; ..\mcal_cfg\Spi_PBCfg.c	    16  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    17  **  DATE, TIME: 2020-07-10, 19:49:42                                         **
; ..\mcal_cfg\Spi_PBCfg.c	    18  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    19  **  GENERATOR : Build b141014-0350                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    20  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    21  **  AUTHOR    : DL-AUTOSAR-Engineering                                        **
; ..\mcal_cfg\Spi_PBCfg.c	    22  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    23  **  VENDOR    : Infineon Technologies                                         **
; ..\mcal_cfg\Spi_PBCfg.c	    24  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    25  **  DESCRIPTION  : SPI configuration generated out of ECU configuration       **
; ..\mcal_cfg\Spi_PBCfg.c	    26  **                 file                                                       **
; ..\mcal_cfg\Spi_PBCfg.c	    27  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    28  **  MAY BE CHANGED BY USER [yes/no]: no                                       **
; ..\mcal_cfg\Spi_PBCfg.c	    29  **                                                                            **
; ..\mcal_cfg\Spi_PBCfg.c	    30  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	    31  
; ..\mcal_cfg\Spi_PBCfg.c	    32  
; ..\mcal_cfg\Spi_PBCfg.c	    33  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	    34  **                      Includes                                              **
; ..\mcal_cfg\Spi_PBCfg.c	    35  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	    36  
; ..\mcal_cfg\Spi_PBCfg.c	    37  /* Include SPI Module File */
; ..\mcal_cfg\Spi_PBCfg.c	    38  /* [cover parentID=DS_NAS_SPI_PR699,DS_NAS_SPI_PR709] */
; ..\mcal_cfg\Spi_PBCfg.c	    39  
; ..\mcal_cfg\Spi_PBCfg.c	    40  #include "Spi.h"
; ..\mcal_cfg\Spi_PBCfg.c	    41  /* Inclusion of Mcal Specific Global Header File */
; ..\mcal_cfg\Spi_PBCfg.c	    42  #include "Mcal.h"
; ..\mcal_cfg\Spi_PBCfg.c	    43  
; ..\mcal_cfg\Spi_PBCfg.c	    44  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	    45  **                      Private Macro Definitions                             **
; ..\mcal_cfg\Spi_PBCfg.c	    46  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	    47  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	    48  **                      Imported Compiler Switch Check                        **
; ..\mcal_cfg\Spi_PBCfg.c	    49  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	    50  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	    51  **                      Private Type Definitions                              **
; ..\mcal_cfg\Spi_PBCfg.c	    52  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	    53  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	    54  **                      Private Function Declarations                         **
; ..\mcal_cfg\Spi_PBCfg.c	    55  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	    56  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	    57  **                      Global Constant Definitions                           **
; ..\mcal_cfg\Spi_PBCfg.c	    58  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	    59  /* MISRA RULE 87 VIOLATION: Inclusion of MemMap.h in between the code can't
; ..\mcal_cfg\Spi_PBCfg.c	    60     be avoided as it is required for mapping global variables, constants
; ..\mcal_cfg\Spi_PBCfg.c	    61     and code
; ..\mcal_cfg\Spi_PBCfg.c	    62  */
; ..\mcal_cfg\Spi_PBCfg.c	    63  /* Violates MISRA Required Rule 16.9,
; ..\mcal_cfg\Spi_PBCfg.c	    64              function identifier used without '&' or parenthisized parameter list
; ..\mcal_cfg\Spi_PBCfg.c	    65             when using function pointer in configurations
; ..\mcal_cfg\Spi_PBCfg.c	    66  */
; ..\mcal_cfg\Spi_PBCfg.c	    67  
; ..\mcal_cfg\Spi_PBCfg.c	    68  /*
; ..\mcal_cfg\Spi_PBCfg.c	    69                       Container: SpiChannelConfiguration
; ..\mcal_cfg\Spi_PBCfg.c	    70  */
; ..\mcal_cfg\Spi_PBCfg.c	    71  #define SPI_START_SEC_POSTBUILDCFG
; ..\mcal_cfg\Spi_PBCfg.c	    72  /*
; ..\mcal_cfg\Spi_PBCfg.c	    73   * To be used for global or static constants (unspecified size)
; ..\mcal_cfg\Spi_PBCfg.c	    74  */
; ..\mcal_cfg\Spi_PBCfg.c	    75  #include "MemMap.h"
; ..\mcal_cfg\Spi_PBCfg.c	    76  /*
; ..\mcal_cfg\Spi_PBCfg.c	    77  Configuration : Channel Configuration Constant Structure.
; ..\mcal_cfg\Spi_PBCfg.c	    78  The IB Channels are configured first followed by EB.
; ..\mcal_cfg\Spi_PBCfg.c	    79  */
; ..\mcal_cfg\Spi_PBCfg.c	    80  static const Spi_ChannelConfigType Spi_kChannelConfig0[] =
; ..\mcal_cfg\Spi_PBCfg.c	    81  {
; ..\mcal_cfg\Spi_PBCfg.c	    82  /* EB Channel: SpiChannel_Micron_Master Configuration */
; ..\mcal_cfg\Spi_PBCfg.c	    83    {
; ..\mcal_cfg\Spi_PBCfg.c	    84      /* Default Data, SPI_DEFAULT_DATA */
; ..\mcal_cfg\Spi_PBCfg.c	    85      (uint32)0x00000000U,
; ..\mcal_cfg\Spi_PBCfg.c	    86      /* Data Configuration */
; ..\mcal_cfg\Spi_PBCfg.c	    87      Spi_DataConfig(8U, /* Data Width */
; ..\mcal_cfg\Spi_PBCfg.c	    88                     SPI_DATA_MSB_FIRST), /* Transfer Start */
; ..\mcal_cfg\Spi_PBCfg.c	    89      /* EB Channels : SPI112: Max EB Buffers  */
; ..\mcal_cfg\Spi_PBCfg.c	    90      (Spi_NumberOfDataType)511U,
; ..\mcal_cfg\Spi_PBCfg.c	    91      /* Buffer Type, SPI_CHANNEL_TYPE */
; ..\mcal_cfg\Spi_PBCfg.c	    92      (uint8)SPI_EB_BUFFER,
; ..\mcal_cfg\Spi_PBCfg.c	    93    },
; ..\mcal_cfg\Spi_PBCfg.c	    94  };
; ..\mcal_cfg\Spi_PBCfg.c	    95  
; ..\mcal_cfg\Spi_PBCfg.c	    96  /*
; ..\mcal_cfg\Spi_PBCfg.c	    97                      Container: SpiJobConfiguration
; ..\mcal_cfg\Spi_PBCfg.c	    98  */
; ..\mcal_cfg\Spi_PBCfg.c	    99  /* Notification Function of SpiJob_Micron_Master is NULL_PTR */
; ..\mcal_cfg\Spi_PBCfg.c	   100  
; ..\mcal_cfg\Spi_PBCfg.c	   101  
; ..\mcal_cfg\Spi_PBCfg.c	   102  /*
; ..\mcal_cfg\Spi_PBCfg.c	   103  Configuration: Channel Assignment
; ..\mcal_cfg\Spi_PBCfg.c	   104  */
; ..\mcal_cfg\Spi_PBCfg.c	   105  /*Channel Assignment of Job: SpiJob_Micron_Master*/
; ..\mcal_cfg\Spi_PBCfg.c	   106  static const Spi_ChannelType SpiJob_Micron_Master_ChannelLinkPtr[] =
; ..\mcal_cfg\Spi_PBCfg.c	   107  {
; ..\mcal_cfg\Spi_PBCfg.c	   108    SpiConf_SpiChannel_SpiChannel_Micron_Master,
; ..\mcal_cfg\Spi_PBCfg.c	   109    SPI_CHANNEL_LINK_DELIMITER
; ..\mcal_cfg\Spi_PBCfg.c	   110  };
; ..\mcal_cfg\Spi_PBCfg.c	   111  
; ..\mcal_cfg\Spi_PBCfg.c	   112  /*
; ..\mcal_cfg\Spi_PBCfg.c	   113  Configuration: Job Configuration Constant Structure.
; ..\mcal_cfg\Spi_PBCfg.c	   114  */
; ..\mcal_cfg\Spi_PBCfg.c	   115  static const Spi_JobConfigType Spi_kJobConfig0[] =
; ..\mcal_cfg\Spi_PBCfg.c	   116  {
; ..\mcal_cfg\Spi_PBCfg.c	   117  
; ..\mcal_cfg\Spi_PBCfg.c	   118  /* Job: SpiJob_Micron_Master Configuration */
; ..\mcal_cfg\Spi_PBCfg.c	   119    {
; ..\mcal_cfg\Spi_PBCfg.c	   120     /* Job End Notification: Spi_JobEndNotification, SPI118 */
; ..\mcal_cfg\Spi_PBCfg.c	   121      NULL_PTR,
; ..\mcal_cfg\Spi_PBCfg.c	   122      /* User given name outside the naming convention */
; ..\mcal_cfg\Spi_PBCfg.c	   123      /* Spi_ChannelLinkPtr */
; ..\mcal_cfg\Spi_PBCfg.c	   124      SpiJob_Micron_Master_ChannelLinkPtr,
; ..\mcal_cfg\Spi_PBCfg.c	   125      /* Baud Rate (10000000 Hz) Hw configuration Parameters */
; ..\mcal_cfg\Spi_PBCfg.c	   126      Spi_BaudRateParams(/*TQ*/(0x1U), /*Q*/(0x0U),
; ..\mcal_cfg\Spi_PBCfg.c	   127                /*A*/(0x2U), /*B*/(0x1U), /*C*/(0x1U)),
; ..\mcal_cfg\Spi_PBCfg.c	   128  
; ..\mcal_cfg\Spi_PBCfg.c	   129      /* Time Delay Configuration */
; ..\mcal_cfg\Spi_PBCfg.c	   130        (uint32)Spi_DelayParams(/*IPRE*/0x02U,/*IDLE*/ 0x01U,
; ..\mcal_cfg\Spi_PBCfg.c	   131          /*LPRE*/0x02U, /*LEAD*/0x01U,
; ..\mcal_cfg\Spi_PBCfg.c	   132          /*TPRE*/0x02U, /*TRAIL*/0x01U),
; ..\mcal_cfg\Spi_PBCfg.c	   133  
; ..\mcal_cfg\Spi_PBCfg.c	   134      SPI_CS_HW, /* Hw CS Pin is Selected */
; ..\mcal_cfg\Spi_PBCfg.c	   135  
; ..\mcal_cfg\Spi_PBCfg.c	   136      /*CS Active level Polarity*/
; ..\mcal_cfg\Spi_PBCfg.c	   137      SPI_CS_POLARITY_LOW,
; ..\mcal_cfg\Spi_PBCfg.c	   138  
; ..\mcal_cfg\Spi_PBCfg.c	   139   /* Shift Clock Configuration : Clock Idle Polarity: SPI_SHIFT_CLOCK_IDLE_LEVEL,
; ..\mcal_cfg\Spi_PBCfg.c	   140                                    Clock Phase: SPI_DATA_SHIFT_EDGE */
; ..\mcal_cfg\Spi_PBCfg.c	   141      Spi_ShiftClkConfig(SPI_CLK_IDLE_LOW,
; ..\mcal_cfg\Spi_PBCfg.c	   142                        SPI_DATA_SHIFT_TRAIL),
; ..\mcal_cfg\Spi_PBCfg.c	   143     /* Job Priority escalated to Maximum as it is mapped
; ..\mcal_cfg\Spi_PBCfg.c	   144        to one or more non-interruptible sequence */
; ..\mcal_cfg\Spi_PBCfg.c	   145      SPI_JOB_PRIORITY_3,
; ..\mcal_cfg\Spi_PBCfg.c	   146  
; ..\mcal_cfg\Spi_PBCfg.c	   147      /* Spi HW Unit. bit[7:4]: Channel no, bit[3:0]: hw module no */
; ..\mcal_cfg\Spi_PBCfg.c	   148        (uint8)((uint8)SPI_QSPI_CHANNEL10 << 4U) | (SPI_QSPI1_INDEX),
; ..\mcal_cfg\Spi_PBCfg.c	   149  
; ..\mcal_cfg\Spi_PBCfg.c	   150      /* Channel Based Chip Select */
; ..\mcal_cfg\Spi_PBCfg.c	   151      (uint8)0U,
; ..\mcal_cfg\Spi_PBCfg.c	   152      
; ..\mcal_cfg\Spi_PBCfg.c	   153      /* Spi Parity Selection */
; ..\mcal_cfg\Spi_PBCfg.c	   154      (uint8)SPI_QSPI_PARITY_UNUSED,
; ..\mcal_cfg\Spi_PBCfg.c	   155      
; ..\mcal_cfg\Spi_PBCfg.c	   156    }
; ..\mcal_cfg\Spi_PBCfg.c	   157  };
; ..\mcal_cfg\Spi_PBCfg.c	   158  
; ..\mcal_cfg\Spi_PBCfg.c	   159  /*
; ..\mcal_cfg\Spi_PBCfg.c	   160                       Container: Spi_SequenceConfiguration
; ..\mcal_cfg\Spi_PBCfg.c	   161  */
; ..\mcal_cfg\Spi_PBCfg.c	   162  /* Notification Function of Sequence: SpiSequence_Micron_Master is NULL_PTR */
; ..\mcal_cfg\Spi_PBCfg.c	   163  
; ..\mcal_cfg\Spi_PBCfg.c	   164  /*
; ..\mcal_cfg\Spi_PBCfg.c	   165  Configuration: Job Assignment
; ..\mcal_cfg\Spi_PBCfg.c	   166  */
; ..\mcal_cfg\Spi_PBCfg.c	   167  /* Job Assignment of Sequence: SpiSequence_Micron_Master */
; ..\mcal_cfg\Spi_PBCfg.c	   168  static const Spi_JobType SpiSequence_Micron_Master_JobLinkPtr[] =
; ..\mcal_cfg\Spi_PBCfg.c	   169  {
; ..\mcal_cfg\Spi_PBCfg.c	   170    SpiConf_SpiJob_SpiJob_Micron_Master,
; ..\mcal_cfg\Spi_PBCfg.c	   171    SPI_JOB_LINK_DELIMITER
; ..\mcal_cfg\Spi_PBCfg.c	   172  };
; ..\mcal_cfg\Spi_PBCfg.c	   173  
; ..\mcal_cfg\Spi_PBCfg.c	   174  /*
; ..\mcal_cfg\Spi_PBCfg.c	   175  Configuration: Sequence Configuration Constant Structure.
; ..\mcal_cfg\Spi_PBCfg.c	   176  */
; ..\mcal_cfg\Spi_PBCfg.c	   177  static const Spi_SequenceConfigType Spi_kSequenceConfig0[] =
; ..\mcal_cfg\Spi_PBCfg.c	   178  {   /* Sequence: SpiSequence_Micron_Master Configuration */
; ..\mcal_cfg\Spi_PBCfg.c	   179    {
; ..\mcal_cfg\Spi_PBCfg.c	   180      /* Spi_SeqEndNotification */
; ..\mcal_cfg\Spi_PBCfg.c	   181      NULL_PTR,
; ..\mcal_cfg\Spi_PBCfg.c	   182      /* User given name outside the naming convention */    /* Spi_JobLinkPtr */
; ..\mcal_cfg\Spi_PBCfg.c	   183      SpiSequence_Micron_Master_JobLinkPtr,
; ..\mcal_cfg\Spi_PBCfg.c	   184      /* User given name outside the naming convention */
; ..\mcal_cfg\Spi_PBCfg.c	   185      /* Sequences that share jobs with this sequence */
; ..\mcal_cfg\Spi_PBCfg.c	   186      NULL_PTR,
; ..\mcal_cfg\Spi_PBCfg.c	   187       /* This holds the total number of jobs linked to this sequence */
; ..\mcal_cfg\Spi_PBCfg.c	   188      1U,
; ..\mcal_cfg\Spi_PBCfg.c	   189      /* Sequence Interruptible or Not (SPI125, SPI126) */
; ..\mcal_cfg\Spi_PBCfg.c	   190      SPI_SEQ_INT_FALSE   }
; ..\mcal_cfg\Spi_PBCfg.c	   191  };
; ..\mcal_cfg\Spi_PBCfg.c	   192  
; ..\mcal_cfg\Spi_PBCfg.c	   193  static const Spi_DmaConfigType Spi_kDmaConfig0[]=
; ..\mcal_cfg\Spi_PBCfg.c	   194  {
; ..\mcal_cfg\Spi_PBCfg.c	   195  
; ..\mcal_cfg\Spi_PBCfg.c	   196    { /* QSPI0 Module Hw Dma Config */
; ..\mcal_cfg\Spi_PBCfg.c	   197      DMA_CHANNEL_INVALID,  /* Tx */    DMA_CHANNEL_INVALID   /* Rx */  },
; ..\mcal_cfg\Spi_PBCfg.c	   198  
; ..\mcal_cfg\Spi_PBCfg.c	   199    { /* QSPI1 Module Hw Dma Config */
; ..\mcal_cfg\Spi_PBCfg.c	   200      DMA_CHANNEL4,  /* Tx */    DMA_CHANNEL5   /* Rx */  },
; ..\mcal_cfg\Spi_PBCfg.c	   201  
; ..\mcal_cfg\Spi_PBCfg.c	   202    { /* QSPI2 Module Hw Dma Config */
; ..\mcal_cfg\Spi_PBCfg.c	   203      DMA_CHANNEL_INVALID,  /* Tx */    DMA_CHANNEL_INVALID   /* Rx */  },
; ..\mcal_cfg\Spi_PBCfg.c	   204  
; ..\mcal_cfg\Spi_PBCfg.c	   205    { /* QSPI3 Module Hw Dma Config */
; ..\mcal_cfg\Spi_PBCfg.c	   206      DMA_CHANNEL_INVALID,  /* Tx */    DMA_CHANNEL_INVALID   /* Rx */  },
; ..\mcal_cfg\Spi_PBCfg.c	   207  
; ..\mcal_cfg\Spi_PBCfg.c	   208  };
; ..\mcal_cfg\Spi_PBCfg.c	   209  
; ..\mcal_cfg\Spi_PBCfg.c	   210  static const Spi_HWModuleConfigType Spi_kModuleConfig0[]=
; ..\mcal_cfg\Spi_PBCfg.c	   211  {
; ..\mcal_cfg\Spi_PBCfg.c	   212    /* QSPI0 Module */
; ..\mcal_cfg\Spi_PBCfg.c	   213    {
; ..\mcal_cfg\Spi_PBCfg.c	   214      SPI_0_NOT_CONFIGURED,
; ..\mcal_cfg\Spi_PBCfg.c	   215      (uint32)0x0U,
; ..\mcal_cfg\Spi_PBCfg.c	   216      SPI_0_NOT_CONFIGURED,
; ..\mcal_cfg\Spi_PBCfg.c	   217      NULL_PTR,
; ..\mcal_cfg\Spi_PBCfg.c	   218    },
; ..\mcal_cfg\Spi_PBCfg.c	   219    /* QSPI1 Module */
; ..\mcal_cfg\Spi_PBCfg.c	   220    {
; ..\mcal_cfg\Spi_PBCfg.c	   221        /*Clock Settings:Sleep Control Disabled*/
; ..\mcal_cfg\Spi_PBCfg.c	   222        SPI_CLK_SLEEP_DISABLE,
; ..\mcal_cfg\Spi_PBCfg.c	   223  	  /*SSOC register value for QSPI1*/
; ..\mcal_cfg\Spi_PBCfg.c	   224  	  (uint32)0x0U,
; ..\mcal_cfg\Spi_PBCfg.c	   225        SPI_QSPI1_MRIS_SEL,
; ..\mcal_cfg\Spi_PBCfg.c	   226        &Spi_kDmaConfig0[1U],
; ..\mcal_cfg\Spi_PBCfg.c	   227    },
; ..\mcal_cfg\Spi_PBCfg.c	   228    /* QSPI2 Module */
; ..\mcal_cfg\Spi_PBCfg.c	   229    {
; ..\mcal_cfg\Spi_PBCfg.c	   230      SPI_2_NOT_CONFIGURED,
; ..\mcal_cfg\Spi_PBCfg.c	   231      (uint32)0x0U,
; ..\mcal_cfg\Spi_PBCfg.c	   232      SPI_2_NOT_CONFIGURED,
; ..\mcal_cfg\Spi_PBCfg.c	   233      NULL_PTR,
; ..\mcal_cfg\Spi_PBCfg.c	   234    },
; ..\mcal_cfg\Spi_PBCfg.c	   235    /* QSPI3 Module */
; ..\mcal_cfg\Spi_PBCfg.c	   236    {
; ..\mcal_cfg\Spi_PBCfg.c	   237      SPI_3_NOT_CONFIGURED,
; ..\mcal_cfg\Spi_PBCfg.c	   238      (uint32)0x0U,
; ..\mcal_cfg\Spi_PBCfg.c	   239      SPI_3_NOT_CONFIGURED,
; ..\mcal_cfg\Spi_PBCfg.c	   240      NULL_PTR,
; ..\mcal_cfg\Spi_PBCfg.c	   241    },
; ..\mcal_cfg\Spi_PBCfg.c	   242  };
; ..\mcal_cfg\Spi_PBCfg.c	   243  
; ..\mcal_cfg\Spi_PBCfg.c	   244  
; ..\mcal_cfg\Spi_PBCfg.c	   245  static const Spi_BaudrateEconType Spi_kBaudrateEcon0[]=
; ..\mcal_cfg\Spi_PBCfg.c	   246  {
; ..\mcal_cfg\Spi_PBCfg.c	   247    {
; ..\mcal_cfg\Spi_PBCfg.c	   248      Spi_BaudRateECON(0x0U, 0x2U, 0x1U, 0x1U,
; ..\mcal_cfg\Spi_PBCfg.c	   249      SPI_DATA_SHIFT_TRAIL,
; ..\mcal_cfg\Spi_PBCfg.c	   250      SPI_CLK_IDLE_LOW,
; ..\mcal_cfg\Spi_PBCfg.c	   251      SPI_QSPI_PARITY_DISABLE),
; ..\mcal_cfg\Spi_PBCfg.c	   252      (uint8)((uint8)SPI_QSPI_CHANNEL10 << 4U) | (SPI_QSPI1_INDEX)
; ..\mcal_cfg\Spi_PBCfg.c	   253    }
; ..\mcal_cfg\Spi_PBCfg.c	   254  };
; ..\mcal_cfg\Spi_PBCfg.c	   255  
; ..\mcal_cfg\Spi_PBCfg.c	   256  const Spi_ConfigType Spi_ConfigRoot[1U] =
; ..\mcal_cfg\Spi_PBCfg.c	   257  {
; ..\mcal_cfg\Spi_PBCfg.c	   258    {
; ..\mcal_cfg\Spi_PBCfg.c	   259      Spi_kChannelConfig0,
; ..\mcal_cfg\Spi_PBCfg.c	   260      Spi_kJobConfig0,
; ..\mcal_cfg\Spi_PBCfg.c	   261      Spi_kSequenceConfig0,
; ..\mcal_cfg\Spi_PBCfg.c	   262      {
; ..\mcal_cfg\Spi_PBCfg.c	   263        &Spi_kModuleConfig0[0U],
; ..\mcal_cfg\Spi_PBCfg.c	   264        &Spi_kModuleConfig0[1U],
; ..\mcal_cfg\Spi_PBCfg.c	   265        &Spi_kModuleConfig0[2U],
; ..\mcal_cfg\Spi_PBCfg.c	   266        &Spi_kModuleConfig0[3U],
; ..\mcal_cfg\Spi_PBCfg.c	   267      },
; ..\mcal_cfg\Spi_PBCfg.c	   268      Spi_kBaudrateEcon0,
; ..\mcal_cfg\Spi_PBCfg.c	   269      (Spi_JobType)(sizeof(Spi_kJobConfig0) / sizeof(Spi_JobConfigType)),
; ..\mcal_cfg\Spi_PBCfg.c	   270      (Spi_ChannelType)(sizeof(Spi_kChannelConfig0) / \ 
; ..\mcal_cfg\Spi_PBCfg.c	   271                        sizeof(Spi_ChannelConfigType)),
; ..\mcal_cfg\Spi_PBCfg.c	   272      (Spi_SequenceType)(sizeof(Spi_kSequenceConfig0) / \ 
; ..\mcal_cfg\Spi_PBCfg.c	   273                                          sizeof(Spi_SequenceConfigType)),
; ..\mcal_cfg\Spi_PBCfg.c	   274      (uint8)(sizeof(Spi_kBaudrateEcon0) / sizeof(Spi_BaudrateEconType)),
; ..\mcal_cfg\Spi_PBCfg.c	   275    }
; ..\mcal_cfg\Spi_PBCfg.c	   276  };
; ..\mcal_cfg\Spi_PBCfg.c	   277  
; ..\mcal_cfg\Spi_PBCfg.c	   278  
; ..\mcal_cfg\Spi_PBCfg.c	   279  #define SPI_STOP_SEC_POSTBUILDCFG
; ..\mcal_cfg\Spi_PBCfg.c	   280  /* Allows to map variables, constants and code of modules to individual
; ..\mcal_cfg\Spi_PBCfg.c	   281    memory sections.*/
; ..\mcal_cfg\Spi_PBCfg.c	   282  /*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
; ..\mcal_cfg\Spi_PBCfg.c	   283  allowed only for MemMap.h*/  
; ..\mcal_cfg\Spi_PBCfg.c	   284  #include "MemMap.h"
; ..\mcal_cfg\Spi_PBCfg.c	   285  
; ..\mcal_cfg\Spi_PBCfg.c	   286  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	   287  **                      Global Variable Definitions                           **
; ..\mcal_cfg\Spi_PBCfg.c	   288  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	   289  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	   290  **                      Private Constant Definitions                          **
; ..\mcal_cfg\Spi_PBCfg.c	   291  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	   292  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	   293  **                      Private Variable Definitions                          **
; ..\mcal_cfg\Spi_PBCfg.c	   294  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	   295  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	   296  **                      Global Function Definitions                           **
; ..\mcal_cfg\Spi_PBCfg.c	   297  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	   298  /*******************************************************************************
; ..\mcal_cfg\Spi_PBCfg.c	   299  **                      Private Function Definitions                          **
; ..\mcal_cfg\Spi_PBCfg.c	   300  *******************************************************************************/
; ..\mcal_cfg\Spi_PBCfg.c	   301  /* General Notes */
; ..\mcal_cfg\Spi_PBCfg.c	   302  /*
; ..\mcal_cfg\Spi_PBCfg.c	   303  SPI095: The code file structure shall not be defined within this specification
; ..\mcal_cfg\Spi_PBCfg.c	   304  completely. At this point it shall be pointed out that the code-file structure
; ..\mcal_cfg\Spi_PBCfg.c	   305  shall include the following file named:
; ..\mcal_cfg\Spi_PBCfg.c	   306  - Spi_Lcfg.c ?for link time and for post-build configurable parameters and
; ..\mcal_cfg\Spi_PBCfg.c	   307  - Spi_PBcfg.c ?for post build time configurable parameters.
; ..\mcal_cfg\Spi_PBCfg.c	   308  These files shall contain all link time and post-build time configurable
; ..\mcal_cfg\Spi_PBCfg.c	   309  parameters.
; ..\mcal_cfg\Spi_PBCfg.c	   310  This file shall contain all link time and post-build time configurable
; ..\mcal_cfg\Spi_PBCfg.c	   311  parameters.
; ..\mcal_cfg\Spi_PBCfg.c	   312  For the implementation of VariantPC, the implementation stores the
; ..\mcal_cfg\Spi_PBCfg.c	   313  pre compile time parameters that have to be defined as constants in this file.
; ..\mcal_cfg\Spi_PBCfg.c	   314  
; ..\mcal_cfg\Spi_PBCfg.c	   315  SPI123: In this configuration, all Sequences declared are considered as Non
; ..\mcal_cfg\Spi_PBCfg.c	   316  Interruptible Sequences. That means, their dedicated parameter
; ..\mcal_cfg\Spi_PBCfg.c	   317  SPI_INTERRUPTIBLE_SEQUENCE (see SPI064 & SPI106) could be omitted or the
; ..\mcal_cfg\Spi_PBCfg.c	   318  FALSE value should be used as default.
; ..\mcal_cfg\Spi_PBCfg.c	   319  
; ..\mcal_cfg\Spi_PBCfg.c	   320  */
; ..\mcal_cfg\Spi_PBCfg.c	   321  
; ..\mcal_cfg\Spi_PBCfg.c	   322  

	; Module end
